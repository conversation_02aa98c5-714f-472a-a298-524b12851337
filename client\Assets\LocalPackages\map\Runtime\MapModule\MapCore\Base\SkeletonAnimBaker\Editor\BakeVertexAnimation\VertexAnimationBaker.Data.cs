﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class VertexAnimationBaker
    {
        //包含一个动画的所有帧
        public class CPUAnimationData
        {
            public void AddCPUDriveCustomBoneTransformsInOneFrame(Vector3[] translations, Quaternion[] rotations, Vector3[] scalings)
            {
                cpuDrivenCustomBoneTranslationsInAllFrames.Add(translations);
                cpuDrivenCustomBoneRotationsInAllFrames.Add(rotations);
                cpuDrivenCustomBoneScalingsInAllFrames.Add(scalings);
            }

            public List<Vector3[]> cpuDrivenCustomBoneTranslationsInAllFrames = new List<Vector3[]>();
            public List<Quaternion[]> cpuDrivenCustomBoneRotationsInAllFrames = new List<Quaternion[]>();
            public List<Vector3[]> cpuDrivenCustomBoneScalingsInAllFrames = new List<Vector3[]>();
        }

        //skin mesh的所有动画烘培数据,这些数据组成一张贴图
        class MeshAllAnimationsData
        {
            public MeshAllAnimationsData(SkinnedMeshRenderer sourceRenderer)
            {
                mSourceRenderer = sourceRenderer;
            }

            public void AddMeshAnimationData(MeshOneAnimationData data)
            {
                mMeshAllAnimationsData.Add(data);
            }

            public void SetCPUDrivenCustomBoneNames(string[] names)
            {
                mCPUDrivenCustomBoneNames = names;
            }

            public int GetMeshVertexCount()
            {
                return meshAllAnimationsData[0].bakedVertexPositionsInAllFrames[0].Length;
            }

            public int GetAllAnimationsTotalFrameCount()
            {
                int n = 0;
                for (int i = 0; i < mMeshAllAnimationsData.Count; ++i)
                {
                    n += mMeshAllAnimationsData[i].bakedVertexPositionsInAllFrames.Count;
                }
                return n;
            }

            public List<MeshOneAnimationData> meshAllAnimationsData { get { return mMeshAllAnimationsData; } }
            public string[] cpuDrivenCustomBoneNames { get { return mCPUDrivenCustomBoneNames; } }
            public SkinnedMeshRenderer sourceRenderer { get { return mSourceRenderer; } }

            List<MeshOneAnimationData> mMeshAllAnimationsData = new List<MeshOneAnimationData>();
            string[] mCPUDrivenCustomBoneNames;
            SkinnedMeshRenderer mSourceRenderer;
        }

        //skin mesh的一个动画烘培数据,包含该动画的所有帧
        class MeshOneAnimationData
        {
            public MeshOneAnimationData(int totalFrame, string stateName, float lengthInSeconds, float framerate, bool loop, AnimationClip clip)
            {
                mBakedVertexPositionsInAllFrames = new List<Vector3[]>(totalFrame);
                mStateName = stateName;
                mLengthInSeconds = lengthInSeconds;
                mFramerate = framerate;
                mLoop = loop;
                mClip = clip;
            }

            //添加某帧的vertice snap
            public void AddBakedVertexPositionsInOneFrame(Vector3[] vertexPositionsInOneFrame)
            {
                mBakedVertexPositionsInAllFrames.Add(vertexPositionsInOneFrame);
            }

            public List<Vector3[]> bakedVertexPositionsInAllFrames { get { return mBakedVertexPositionsInAllFrames; } }
            public string stateName { get { return mStateName; } }
            public float lengthInSeconds { get { return mLengthInSeconds; } }
            public float framerate { get { return mFramerate; } }
            public bool loop { get { return mLoop; } }
            public AnimationClip clip { get { return mClip; } }
            public float lengthRatio { get { return mLengthRatio; } set { mLengthRatio = value; } }

            //顶点数据
            List<Vector3[]> mBakedVertexPositionsInAllFrames;
            string mStateName;
            float mLengthInSeconds;
            float mFramerate;
            float mLengthRatio;
            AnimationClip mClip;
            bool mLoop;   
        }
    }
}

#endif