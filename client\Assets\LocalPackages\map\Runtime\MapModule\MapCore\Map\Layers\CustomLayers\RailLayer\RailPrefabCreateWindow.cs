﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RailPrefabCreateWindow : EditorWindow
    {
        void OnGUI()
        {
            GUILayout.BeginVertical();
            mWidth = EditorGUILayout.FloatField("Width", mWidth);
            mLength = EditorGUILayout.FloatField("Length", mLength);
            mLOD = EditorGUILayout.IntField("LOD", mLOD);
            mMaterial = EditorGUILayout.ObjectField("Material", mMaterial, typeof(Material), false, null) as Material;
            mRailName = EditorGUILayout.TextField("Name", mRailName);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.TextField("Folder", mPrefabFolder);
            if (GUILayout.Button("Select"))
            {
                mPrefabFolder = EditorUtility.SaveFolderPanel("Select folder", "", "");
            }
            EditorGUILayout.EndHorizontal();
            GUILayout.EndHorizontal();

            mLength = Mathf.Clamp(mLength, 1, 100000);
            mWidth = Mathf.Clamp(mWidth, 1, 100000);
            mLOD = Mathf.Clamp(mLOD, 1, 1000);

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                if (!string.IsNullOrEmpty(mPrefabFolder) &&
                    mMaterial != null &&
                    !string.IsNullOrEmpty(mRailName))
                {
                    string prefabPath = mPrefabFolder + "/" + mRailName + "_lod" + mLOD + ".prefab";
                    ColorRailCreator.CreateRail(prefabPath, mMaterial, mWidth, mLength);
                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        float mWidth = 1.5f;
        float mLength = 50.0f;
        Material mMaterial;
        string mPrefabFolder;
        string mRailName = "Rail1";
        int mLOD = 1;
    }
}

#endif