﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;

/*
 * created by wzw at 2020.6.30
 */

namespace TFW.Map
{
    //加速碰撞监测
    public class BVH
    {
        class Node
        {
            public Bounds bounds;
            public List<Node> children;
            public List<int> childPrefabIndices;
        }

        public BVH(BVHQuadTree tree)
        {
            CreateFromTree(tree);
        }

        void CreateFromTree(BVHQuadTree tree)
        {
            Debug.Assert(mRoot == null);
            var treeNode = tree.rootNode;
            mModelTemplate = tree.modelTemplate;
            mRoot = CreateNode(null, treeNode);
        }

        Node CreateNode(Node parent, BVHQuadTreeNode treeNode)
        {
            Node node = new Node();
            node.bounds = treeNode.objectsBounds;
            if (treeNode.IsLeaf())
            {
                node.childPrefabIndices = treeNode.childPrefabIndices;
            }
            else
            {
                node.children = new List<Node>();
            }
            if (parent != null)
            {
                parent.children.Add(node);
            }

            for (int i = 0; i < treeNode.children.Length; ++i)
            {
                if (treeNode.children[i] != null)
                {
                    CreateNode(node, treeNode.children[i]);
                }
            }

            return node;
        }

        public void GetIntersectedObjects(Vector3 tileStartPos, ObstacleObject collider, List<int> intersectedChildPrefabIndices)
        {
            GetIntersectedObjects(mRoot, tileStartPos, collider, intersectedChildPrefabIndices);
        }

        void GetIntersectedObjects(Node node, Vector3 tileStartPos, ObstacleObject collider, List<int> intersectedChildPrefabIndices)
        {
            if (BoundsInterects(tileStartPos, node.bounds, collider.bounds))
            {
                if (node.children == null || node.children.Count == 0)
                {
                    for (int i = 0; i < node.childPrefabIndices.Count; ++i)
                    {
                        if (!intersectedChildPrefabIndices.Contains(node.childPrefabIndices[i]))
                        {
                            intersectedChildPrefabIndices.Add(node.childPrefabIndices[i]);
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < node.children.Count; ++i)
                    {
                        GetIntersectedObjects(node.children[i], tileStartPos, collider, intersectedChildPrefabIndices);
                    }
                }
            }
        }

        bool BoundsInterects(Vector3 tileStartPos, Bounds localBounds, Bounds worldBounds)
        {
            var min = tileStartPos + localBounds.min;
            var max = tileStartPos + localBounds.max;
            var worldMin = worldBounds.min;
            var worldMax = worldBounds.max;
            if (min.x > worldMax.x || min.z > worldMax.z || worldMin.x > max.x || worldMin.z > max.z)
            {
                return false;
            }
            return true;
        }

        Node mRoot;
        ModelTemplate mModelTemplate;
    }
}

#endif