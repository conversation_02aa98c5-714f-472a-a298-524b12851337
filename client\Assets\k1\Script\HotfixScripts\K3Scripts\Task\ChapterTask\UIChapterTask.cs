﻿using System;
using System.Collections.Generic;
using Common;
using cspb;
using DeepUI;
using Game.Config;
using TFW;
using Logic;
using Render;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace K3
{
    [Serializable]
    public class UIChapterTaskData
    {
        public Dictionary<int, string> TaskCache;
        public Dictionary<int, string> SkinCache;
        public Dictionary<int, string> HeroVideoCache;
    }

    /// <summary>
    /// 
    /// </summary>
    public class UIChapterTaskPopupData
    {
        public bool IsTest;
    }

    /// <summary>
    /// 新章节任务
    /// </summary>
    [Popup("Task/UIChapterTask", true, true)]
    public class UIChapterTask : BasePopupLayer
    {
        /// <summary>
        /// 任务面板
        /// </summary>
        [PopupField("Root/ChapterTask")]
        private GameObject m_ChapterTask;

        /// <summary>
        /// 照片墙
        /// </summary>
        [PopupField("Root/PhotoWall")]
        private GameObject m_PhotoWall;

        /// <summary>
        /// 
        /// </summary>
        [<PERSON>up<PERSON>ield("Root/ChapterTask/CloseBtn")]
        private GameObject m_Close;

        [<PERSON>up<PERSON>ield("Root/SpineHead")]
        private GameObject m_SpineHead;

        [PopupField("Root")]
        private Animator m_Animator;

        private ChapterTaskNewWidget m_ChapterTaskNewWidget;
        private PhotoWallWidget m_PhotoWallWidget;
        private float m_WaitTimes;
        private UIChapterTaskPopupData m_Data;


        //protected internal override PopupOverlayType OverlayType =>  PopupOverlayType.Blur;

        protected override void OnInit()
        {
            base.OnInit();

            if (m_ChapterTaskNewWidget == null)
            {
                m_ChapterTaskNewWidget = new ChapterTaskNewWidget(m_ChapterTask);
            }

            if (m_PhotoWallWidget == null)
            {
                m_PhotoWallWidget = new PhotoWallWidget(m_PhotoWall);
            }
            InitAnimDelayTimes();
        }

        protected internal override void OnDataReady()
        {
            base.OnDataReady();

            m_Data = Data as UIChapterTaskPopupData;
        }

        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();
            AddClickListener(m_Close, OnCloseBtnClick);

            m_ChapterTaskNewWidget.RegisterEvent();
            if (m_Data != null && m_Data.IsTest)
            {
                ResetPhotoAnim();
                m_PhotoWallWidget.RefreshHeros();
                m_PhotoWallWidget.ScrollToHero(ChapterTaskMgr.I.GetNewChapterId(), () =>
                {
                    m_Animator.Play("UI_ChapterTask_PhotoWall", 0, 0);
                    //NTimer.CountDown(0.2f, () => m_Animator.Play("UI_ChapterTask_PhotoWall", 0, 0));
                });
                ChapterTaskMgr.I.WaitPhotoTime = m_WaitTimes + 1f;//
            }
            else if (!ChapterTaskMgr.I.CheckStepIsFinish("start"))
            {
                ChapterTaskMgr.I.UpdateStatusData("start");
                ResetPhotoAnim();
                m_PhotoWallWidget.RefreshHeros();
                m_PhotoWallWidget.ScrollToHero(ChapterTaskMgr.I.GetNewChapterId(), () =>
                {
                    //NTimer.CountDown(1f, () =>
                    //{
                        GameAudio.PlayAudio(AudioConst.ChapterTaskPhoto);
                        m_Animator.Play("UI_ChapterTask_PhotoWall", 0, 0);
                    //});
                });
                ChapterTaskMgr.I.WaitPhotoTime = m_WaitTimes + 1f;//
            }
            else
            {
                m_Animator.Play("UI_ChapterTask_ChapterTask", 0, 0);
                ChapterTaskMgr.I.WaitPhotoTime = 0;
            }
            OnRefreshStatus();
        }

        private void InitAnimDelayTimes()
        {
            AnimationClip[] clips = m_Animator.runtimeAnimatorController.animationClips;
            foreach (AnimationClip clip in clips)
            {
                if (clip.name == "UI_ChapterTask_PhotoWall")
                {
                    m_WaitTimes = clip.length;
                }
            }
        }

        private void ResetPhotoAnim()
        {
            m_Animator.Play("UI_ChapterTask_PhotoWall", 0, 0);
            m_PhotoWallWidget.ScrollToHero(1, null, false);
        }

        [PopupEvent(TEventType.OnChapterJumpOut)]
        private void OnChapterJumpOut(object[] obj)
        {
            //m_Animator.Play("ChapterTask_Out", 0, 0);
            CommonZoomLoadCtrl.I.Show(0.8f, () =>
            {
                ChapterTaskMgr.I.ZoomStatus = true;
                PopupManager.I.ClosePopup<UIChapterTask>();
                PopupManager.I.ShowLayer<UIHeroSkinPreview>();
            });
        }

        private void OnCloseBtnClick(GameObject arg0, PointerEventData arg1)
        {
            PopupManager.I.ClosePopup<UIChapterTask>();
        }

        private void OnRefreshStatus()
        {
            // // 当前章节ID
            // int chapterId = ChapterTaskGameData.I.mChapterQuestInfo.curChapterID;
            //
            // K3PlayerMgr.I.SavePlayerDataToServer();



            m_ChapterTaskNewWidget.SetData();
            m_ChapterTaskNewWidget.SetSpineHead(m_SpineHead);
            m_PhotoWallWidget.SetData();
        }



        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();

            RemoveClickListener(m_Close);
            EventMgr.UnregisterEvent(this);
            m_ChapterTaskNewWidget.Destroy();
        }
    }
}
