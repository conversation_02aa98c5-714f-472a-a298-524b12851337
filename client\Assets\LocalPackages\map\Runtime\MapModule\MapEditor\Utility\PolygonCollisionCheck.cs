﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/19

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //检测任意两个simple的多边形是否相交, convex和concave都行
    [Black]
    public static class PolygonCollisionCheck
    {
        //todo, optimize this
        public static bool Overlap(List<Vector3> pa, List<Vector3> pb)
        {
            Bounds boundsA = Utils.CreateBounds(pa);
            Bounds boundsB = Utils.CreateBounds(pb);
            if (!boundsA.Intersects(boundsB))
            {
                return false;
            }

            //先检测是否是包含关系
            bool isIncluded = IsPolygonAIncludedInPolygonB(pa, pb) || IsPolygonAIncludedInPolygonB(pb, pa);
            if (isIncluded)
            {
                return true;
            }

            for (int i = 0; i < pa.Count; ++i)
            {
                Vector3 aStart = pa[i];
                Vector3 aEnd = pa[(i + 1) % pa.Count];
                for (int j = 0; j < pb.Count; ++j)
                {
                    Vector3 bStart = pb[j];
                    Vector3 bEnd = pb[(j + 1) % pb.Count];

                    if (Utils.SegmentSegmentIntersectionTest(Utils.ToVector2(aStart), Utils.ToVector2(aEnd), Utils.ToVector2(bStart), Utils.ToVector2(bEnd), out Vector2 p))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        static bool IsPolygonAIncludedInPolygonB(List<Vector3> pa, List<Vector3> pb)
        {
            for (int i = 0; i < pa.Count; ++i)
            {
                if (!EditorUtils.PointInPolygon2D(pa[i], pb))
                {
                    return false;
                }
            }

            return true;
        }
    }
}

#endif