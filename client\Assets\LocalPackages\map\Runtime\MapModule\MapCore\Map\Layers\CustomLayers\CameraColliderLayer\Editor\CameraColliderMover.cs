﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class CameraColliderMover
    {
        public CameraColliderMover(int layerID, int dataID, Vector3 pos)
        {
            Debug.Assert(mAction == null);
            mAction = new ActionMoveCameraCollider(layerID, dataID, pos);
        }

        public void Stop(Vector3 pos)
        {
            mAction.SetEndPosition(pos);
            if (mAction.moved)
            {
                ActionManager.instance.PushAction(mAction, true, false);
            }
            mAction = null;
        }

        ActionMoveCameraCollider mAction;
    }
}


#endif