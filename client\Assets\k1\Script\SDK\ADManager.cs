﻿//using GoogleMobileAds.Api;
//using TFW.Localization;
//using System;
//using System.Collections.Generic;
//using THelper;
//using UI;
//using UnityEngine;

//public class ADManager : Ins<ADManager>
//{

//    private ADRewardData mRewardData;

//    public void InitAD()
//    {
//        List<string> adUnitIds=new List<string>();
//#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
//        adUnitIds.Add("ca-app-pub-3940256099942544/5224354917"); //测试广告单元ID
//#else
//         adUnitIds.Add("ca-app-pub-2007948860532396/8958357538"); //广告单元ID
//         adUnitIds.Add("ca-app-pub-2007948860532396/6112702347"); //广告单元ID
//#endif

//        mRewardData = new ADRewardData(adUnitIds.ToArray());
//    }


//    public void ShowAd(Action<bool> callBack)
//    {
//#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
//        if (mRewardData == null)
//        {
//            InitAD();
//        }
//        else
//        {
//            mRewardData.ShowAd(callBack);
//        }
//#else
//        callBack?.Invoke(false);
//#endif
//    }
//}

//public class ADRewardData
//{
//    private string[] adunitIds;
//    private int idIndex;
//    private RewardedAd mReard;
//    private Action<bool> mCallBack;

//    private int loadfailNum = 0;

//    public ADRewardData(string[] adunitid)
//    {
//        this.adunitIds = adunitid;
//        this.idIndex = 0;
     
//        if (this.adunitIds.Length > this.idIndex)
//        {
//            InitReward();
//        }
//        else
//        {
//            Debug.LogError($"广告初始化有误，adunitid is null!");
//        }
//    }

//    private void InitReward()
//    {
//        this.mReard = new RewardedAd(this.adunitIds[this.idIndex]);

//        this.mReard.OnAdLoaded += MReard_OnAdLoaded;
//        this.mReard.OnAdClosed += MReard_OnAdClosed;
//        this.mReard.OnAdFailedToLoad += MReard_OnAdFailedToLoad;
//        this.mReard.OnUserEarnedReward += MReard_OnUserEarnedReward;
//    }

//    private void MReard_OnUserEarnedReward(object sender, GoogleMobileAds.Api.Reward e)
//    {
//        try
//        {
//            mCallBack?.Invoke(true);
//        }
//        catch (Exception ex)
//        {
//            Debug.LogError(ex.Message);
//        }
//    }

//    private void MReard_OnAdFailedToLoad(object sender, AdFailedToLoadEventArgs e)
//    {
//        if (loadfailNum < 3)
//        {
//            loadfailNum++;

//            this.idIndex++;
//            if (this.adunitIds.Length <= this.idIndex)
//            {
//                this.idIndex = 0;
//            }

//            InitReward();
//        }
//        else
//        {
//            Debug.LogError($"广告加载多次失败！！！{e.LoadAdError.GetMessage()}");
//        }
//    }

//    private void MReard_OnAdClosed(object sender, EventArgs e)
//    {
//        this.idIndex = 0;
//        InitReward();
//    }

//    private void MReard_OnAdLoaded(object sender, EventArgs e)
//    {
//        UnityEngine.Debug.Log($"广告加载完毕");
//        loadfailNum = 0;
//    }


//    public void ShowAd(Action<bool> callBack)
//    {
//        this.mCallBack = callBack;

//        if (mReard?.IsLoaded() == true)
//        {
//            mReard.Show();
//        }
//        else
//        {
//            callBack?.Invoke(false);

//            this.idIndex = 0;
//            this.loadfailNum = 0;
//            InitReward();
//        }
//    }
//}