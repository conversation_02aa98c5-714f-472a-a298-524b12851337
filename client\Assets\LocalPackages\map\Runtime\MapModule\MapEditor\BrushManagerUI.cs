﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public static class BrushManagerUI
    {
        public static void Draw(BrushManager brushManager)
        {
            if (brushManager != null)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                int nBrushes = brushManager.brushCount;
                EditorGUILayout.BeginHorizontal();
                for (int i = 0; i < nBrushes; ++i)
                {
                    var brush = brushManager.GetBrush(i);
                    TextureField("", brush.GetTexture(false), i == brushManager.activeBrush, brushManager, i);
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Refresh Brush", "修改了Brush Folder的贴图后需要点击该按钮刷新")))
                {
                    brushManager.Refresh();
                }
                if (GUILayout.But<PERSON>(new GUIContent("Open Brush Folder", "打开Brush Folder,所有笔刷的贴图都保存在该目录中")))
                {
                    OpenBrushFolder();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
            }
        }

        static void OpenBrushFolder()
        {
            EditorUtils.OpenFolder(MapModule.brushFolder);
        }

        static Texture2D TextureField(string name, Texture2D texture, bool selected, BrushManager brushManager, int idx)
        {
            GUILayout.BeginVertical();
            var result = (Texture2D)EditorGUILayout.ObjectField(texture, typeof(Texture2D), false, GUILayout.Width(70), GUILayout.Height(70));

            var style = new GUIStyle(GUI.skin.label);
            //style.alignment = TextAnchor.UpperCenter;
            style.fixedWidth = 70;
            EditorGUILayout.BeginHorizontal();
            bool newSelectionState = GUILayout.Toggle(selected, "");
            if (newSelectionState != selected)
            {
                brushManager.SetActiveBrush(idx);
            }
            GUILayout.Label(name, style);
            EditorGUILayout.EndHorizontal();
            GUILayout.EndVertical();
            return result;
        }
    }
}

#endif