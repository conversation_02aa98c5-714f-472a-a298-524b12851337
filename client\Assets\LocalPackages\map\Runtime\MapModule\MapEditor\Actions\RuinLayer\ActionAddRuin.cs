﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using UnityEditor;

namespace TFW.Map
{
    [Black]
    public class ActionAddRuin : ActionAddModel
    {
        public ActionAddRuin(int layerID, int objectID, int modelTemplateID, Vector3 position, Quaternion rotation, Vector3 scale, RuinType type, int level, string objectType, Color color, PropertyDatas properties, bool selectRuin)
             : base(layerID, objectID, modelTemplateID, position, rotation, scale)
        {
            mType = type;
            mLevel = level;
            mObjectType = objectType;
            mColor = color;
            mProperties = properties;
            mSelectRuin = selectRuin;
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                var layer = map.GetMapLayerByID(mLayerID) as RuinLayer;
                if (layer != null)
                {
                    if (layer.ExistsRuinObjectType(mObjectType))
                    {
                        var scale = Vector3.one * layer.ruinDisplayRadius * 2;
                        var ruinData = new RuinData(mObjectID, mLayerID, Map.currentMap, 0, mPosition, mRotation, scale, modelTemplate, mType, mLevel, mObjectType, mColor, mProperties);
                        layer.AddObject(ruinData);
                        if (mSelectRuin)
                        {
                            var gameObject = layer.GetObjectGameObject(mObjectID);
                            Selection.activeGameObject = gameObject;
                        }
                        return true;
                    }
                }
            }
            return false;
        }

        RuinType mType;
        int mLevel;
        string mObjectType;
        Color mColor;
        PropertyDatas mProperties;
        bool mSelectRuin;
    }
}

#endif