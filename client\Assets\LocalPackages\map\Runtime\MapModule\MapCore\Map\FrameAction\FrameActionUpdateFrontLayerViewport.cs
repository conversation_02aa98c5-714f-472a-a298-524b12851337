﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //更新前景层视野
    public class FrameActionUpdateFrontLayerViewport : FrameAction
    {
        public static FrameActionUpdateFrontLayerViewport Require(TileGridObjectLayerData layerData, int timeStamp, Rect oldViewport, float oldZoom, Rect newViewport, float newZoom)
        {
            var act = mPool.Require();
            act.Init(layerData, timeStamp, oldViewport, oldZoom, newViewport, newZoom);
            return act;
        }

        protected void Init(TileGridObjectLayerData layerData, int timeStamp, Rect oldViewport, float oldZoom, Rect newViewport, float newZoom)
        {
            InitAction();
            mLayerData = layerData;
            mOldViewport = oldViewport;
            mOldZoom = oldZoom;
            mNewViewport = newViewport;
            mNewZoom = newZoom;
            mKey = MakeActionKey(layerData.id, timeStamp, type);
        }

        protected override void DoImpl()
        {
            bool lodChanged = false;
            //先根据高度更新lod
            if (!Mathf.Approximately(mNewZoom, mOldZoom))
            {
                lodChanged = mLayerData.SetZoomFromAction(this, mOldZoom, mOldViewport, mNewZoom, mNewViewport);
            }

            if (mOldViewport != mNewViewport)
            {
                mLayerData.UpdateViewportFromAction(this, lodChanged, mOldViewport, mNewViewport);
            }
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public static long MakeActionKey(int id, int timeStamp, FrameActionType type)
        {
            long id64 = (long)id;
            long t64 = (long)timeStamp;
            long type64 = (long)type;
            long typeAndTime = (t64 << 16) | type64;
            id64 <<= 32;
            return typeAndTime | id64;
        }

        public override FrameActionType type => FrameActionType.UpdateFrontLayerViewport;
        public override string debugInfo
        {
            get
            {
                return string.Format("Update Front Layer Viewport old Zoom {0}, new Zoom {1}", mOldZoom, mNewZoom);
            }
        }
        public override string name
        {
            get
            {
                return "Update Front Layer Viewport";
            }
        }

        TileGridObjectLayerData mLayerData;
        Rect mOldViewport;
        float mOldZoom;
        Rect mNewViewport;
        float mNewZoom;

        long mKey;

        static ObjectPool<FrameActionUpdateFrontLayerViewport> mPool = new ObjectPool<FrameActionUpdateFrontLayerViewport>(1000, ()=>new FrameActionUpdateFrontLayerViewport());
    }
}
