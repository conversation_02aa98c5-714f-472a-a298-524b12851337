﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map {
    [Black]
    public class SpriteTileData : TileData {
        public SpriteTileData(SpriteTemplate spriteTemplate) {
            mSpriteTemplate = spriteTemplate;
        }

        public SpriteTemplate spriteTemplate { get { return mSpriteTemplate; } }
        public int spriteTemplateID {
            get {
                if (mSpriteTemplate != null) {
                    return mSpriteTemplate.id;
                }
                return 0;
            }
        }

        SpriteTemplate mSpriteTemplate;
    }
}

#endif