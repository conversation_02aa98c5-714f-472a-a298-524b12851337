﻿ 



 
 

//#define SHOW_MESH


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class VertexInfo
    {
        public VertexInfo(Vector3 pos, int idx)
        {
            position = pos;
            index = idx;
        }

        public Vector3 position;
        public int index;
    }

    public class TriangleQuadTreeNode
    {
        public TriangleQuadTreeNode(Rect bounds, int depth)
        {
            this.bounds = bounds;
            this.depth = depth;
            children = new TriangleQuadTreeNode[4];
        }

        public bool IsIntersected(float minX, float minZ, float maxX, float maxZ)
        {
            var min = bounds.min;
            var max = bounds.max;
            if (min.x > maxX || min.y > maxZ || minX > max.x || minZ > max.y)
            {
                return false;
            }

            return true;
        }

        public TriangleQuadTreeNode[] children;

        //三角形
        public List<Vector3Int> triangles = new List<Vector3Int>();

        //顶点
        public List<VertexInfo> vertices = new List<VertexInfo>();
        public Rect bounds;
        public int depth;
    }

    public class TriangleQuadTree
    {
        //startIndex: indices的起始索引,可以传入一个大的索引数组,但是只引用start到end的一段
        //endIndex: indices的结束索引
        public void Create(string name, Vector2 startOffset, Vector2 mapSize, Vector3[] vertices, int[] indices, int startIndex,
            int endIndex, int maxDepth, bool addVertex, bool createMesh, bool meshVisible)
        {
            Reset();

            mMaxDepth = maxDepth;
            mVertices = vertices;
            mIndices = indices;
            mStartIndex = startIndex;
            mEndIndex = endIndex;
            mRootNode = new TriangleQuadTreeNode(new Rect(startOffset.x, startOffset.y, mapSize.x, mapSize.y), 0);

            if (vertices != null && indices != null)
            {
                int indexCount = endIndex - startIndex + 1;
                Debug.Assert(indexCount % 3 == 0);

                int nTriangles = indexCount / 3;
                for (int i = 0; i < nTriangles; ++i)
                {
                    AddTriangle(mRootNode, indices[startIndex + i * 3], indices[startIndex + i * 3 + 1],
                        indices[startIndex + i * 3 + 2]);
                }

#if false
                if (createMesh)
                {
                    CreateViewer(name, meshVisible);
                }
#endif

                if (addVertex)
                {
                    for (int i = 0; i < vertices.Length; ++i)
                    {
                        AddVertex(mRootNode, vertices[i], i);
                    }
                }
            }
        }

        public void OnDestroy()
        {
#if false
            if (mViewer != null)
            {
                mViewer.OnDestroy();
                mViewer = null;
            }
#endif
        }

        public void Reset()
        {
            mMaxDepth = 0;
            mRootNode = null;
            mVertices = null;
        }

        public bool GetHitVertex(float centerX, float centerZ, float radius, out VertexInfo vertexInfo)
        {
            vertexInfo = null;
            if (mRootNode != null)
            {
                float nearestDistance = float.MaxValue;
                return GetHitVertex(mRootNode, centerX, centerZ, radius, nearestDistance, ref vertexInfo);
            }

            return false;
        }

        bool GetHitVertex(TriangleQuadTreeNode node, float centerX, float centerZ, float radius, float nearestDistance,
            ref VertexInfo vertexInfo)
        {
            bool intersected = false;
            float minX = centerX - radius;
            float maxX = centerX + radius;
            float minZ = centerZ - radius;
            float maxZ = centerZ + radius;
            var center = new Vector3(centerX, 0, centerZ);
            if (node.IsIntersected(minX, minZ, maxX, maxZ))
            {
                for (int i = 0; i < node.vertices.Count; ++i)
                {
                    var d = node.vertices[i].position - center;
                    var distance = d.sqrMagnitude - radius * radius;
                    if (distance <= 0)
                    {
                        intersected = true;
                        if (distance < nearestDistance)
                        {
                            nearestDistance = distance;
                            vertexInfo = new VertexInfo(node.vertices[i].position, node.vertices[i].index);
                        }

                        break;
                    }
                }

                for (int i = 0; i < 4; ++i)
                {
                    if (node.children[i] != null)
                    {
                        intersected |= GetHitVertex(node.children[i], centerX, centerZ, radius, nearestDistance,
                            ref vertexInfo);
                    }
                }
            }

            return intersected;
        }

        public bool IsTriangleIntersectedWithCircle(float centerX, float centerZ, float radius)
        {
            if (mRootNode != null)
            {
                return IsTriangleIntersectedWithCircle(mRootNode, centerX, centerZ, radius);
            }

            return false;
        }

        bool IsTriangleIntersectedWithCircle(TriangleQuadTreeNode node, float centerX, float centerZ, float radius)
        {
            bool intersected = false;

            float minX = centerX - radius;
            float maxX = centerX + radius;
            float minZ = centerZ - radius;
            float maxZ = centerZ + radius;
            Vector2[] triangleVertices = new Vector2[3];
            if (node.IsIntersected(minX, minZ, maxX, maxZ))
            {
                for (int i = 0; i < node.triangles.Count; ++i)
                {
                    var triangle = node.triangles[i];
                    var v0 = vertices[triangle.x];
                    var v1 = vertices[triangle.y];
                    var v2 = vertices[triangle.z];

                    triangleVertices[2] = new Vector2(v0.x, v0.z);
                    triangleVertices[1] = new Vector2(v1.x, v1.z);
                    triangleVertices[0] = new Vector2(v2.x, v2.z);

                    if (Utils.TriangleCircleIntersection2D(centerX, centerZ, radius, triangleVertices))
                    {
                        intersected = true;
                        break;
                    }
                }

                if (!intersected)
                {
                    for (int i = 0; i < 4; ++i)
                    {
                        if (node.children[i] != null)
                        {
                            intersected |= IsTriangleIntersectedWithCircle(node.children[i], centerX, centerZ, radius);
                            if (intersected)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return intersected;
        }

        public bool IsTriangleIntersectedWithPoint(float centerX, float centerZ)
        {
            if (mRootNode != null)
            {
                return IsTriangleIntersectedWithPoint(mRootNode, centerX, centerZ);
            }

            return false;
        }

        bool IsTriangleIntersectedWithPoint(TriangleQuadTreeNode node, float centerX, float centerZ)
        {
            bool intersected = false;

            float minX = centerX;
            float maxX = centerX;
            float minZ = centerZ;
            float maxZ = centerZ;
            Vector2[] triangleVertices = new Vector2[3];
            var point = new Vector2(centerX, centerZ);
            if (node.IsIntersected(minX, minZ, maxX, maxZ))
            {
                for (int i = 0; i < node.triangles.Count; ++i)
                {
                    var triangle = node.triangles[i];
                    var v0 = vertices[triangle.x];
                    var v1 = vertices[triangle.y];
                    var v2 = vertices[triangle.z];

                    if (Utils.PointInTriangle(point, new Vector2(v2.x, v2.z), new Vector2(v1.x, v1.z),
                            new Vector2(v0.x, v0.z)))
                    {
                        intersected = true;
                        break;
                    }
                }

                if (!intersected)
                {
                    for (int i = 0; i < 4; ++i)
                    {
                        if (node.children[i] != null)
                        {
                            intersected |= IsTriangleIntersectedWithPoint(node.children[i], centerX, centerZ);
                            if (intersected)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return intersected;
        }

        public bool IsTriangleIntersectedWithConvexPolygon(List<Vector3> convexPolygon, Rect bounds)
        {
            if (mRootNode != null)
            {
                return IsTriangleIntersectedWithConvexPolygon(mRootNode, convexPolygon, bounds);
            }

            return false;
        }

        bool IsTriangleIntersectedWithConvexPolygon(TriangleQuadTreeNode node, List<Vector3> convexPolygon, Rect bounds)
        {
            bool intersected = false;

            if (node.IsIntersected(bounds.xMin, bounds.yMin, bounds.xMax, bounds.yMax))
            {
                for (int i = 0; i < node.triangles.Count; ++i)
                {
                    var triangle = node.triangles[i];
                    mTriangleVertices[2] = vertices[triangle.x];
                    mTriangleVertices[1] = vertices[triangle.y];
                    mTriangleVertices[0] = vertices[triangle.z];

                    if (ConvexPolygonCollisionCheck.ConvexPolygonHit2D(mTriangleVertices, convexPolygon))
                    {
                        intersected = true;
                        break;
                    }
                }

                if (!intersected)
                {
                    for (int i = 0; i < 4; ++i)
                    {
                        if (node.children[i] != null)
                        {
                            intersected |=
                                IsTriangleIntersectedWithConvexPolygon(node.children[i], convexPolygon, bounds);
                            if (intersected)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return intersected;
        }

        public bool IsTriangleIntersectedWithRectangle(float minX, float minZ, float maxX, float maxZ)
        {
            if (mRootNode != null)
            {
                return IsTriangleIntersectedWithRectangle(mRootNode, minX, minZ, maxX, maxZ);
            }

            return false;
        }

        bool IsTriangleIntersectedWithRectangle(TriangleQuadTreeNode node, float minX, float minZ, float maxX,
            float maxZ)
        {
            bool intersected = false;

            if (node.IsIntersected(minX, minZ, maxX, maxZ))
            {
                for (int i = 0; i < node.triangles.Count; ++i)
                {
                    var triangle = node.triangles[i];
                    var v0 = vertices[triangle.x];
                    var v1 = vertices[triangle.y];
                    var v2 = vertices[triangle.z];

                    if (Utils.TriangleRectangleIntersection2D(minX, minZ, maxX, maxZ, v0.x, v0.z, v1.x, v1.z, v2.x,
                            v2.z))
                    {
                        intersected = true;
                        break;
                    }
                }

                if (!intersected)
                {
                    for (int i = 0; i < 4; ++i)
                    {
                        if (node.children[i] != null)
                        {
                            intersected |= IsTriangleIntersectedWithRectangle(node.children[i], minX, minZ, maxX, maxZ);
                            if (intersected)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return intersected;
        }

        public List<Vector3Int> GetIntersectedTriangles(float minX, float minZ, float maxX, float maxZ)
        {
            List<Vector3Int> intersected = new List<Vector3Int>();
            if (mRootNode != null)
            {
                GetIntersectedTriangles(mRootNode, minX, minZ, maxX, maxZ, intersected);
            }

            return intersected;
        }

        void GetIntersectedTriangles(TriangleQuadTreeNode node, float minX, float minZ, float maxX, float maxZ,
            List<Vector3Int> intersectedTriangles)
        {
            if (node.IsIntersected(minX, minZ, maxX, maxZ))
            {
                for (int i = 0; i < node.triangles.Count; ++i)
                {
                    var triangle = node.triangles[i];
                    var v0 = vertices[triangle.x];
                    var v1 = vertices[triangle.y];
                    var v2 = vertices[triangle.z];
                    if (Utils.TriangleRectangleIntersection2D(minX, minZ, maxX, maxZ,
                            v0.x, v0.z, v1.x, v1.z, v2.x, v2.z))
                    {
                        intersectedTriangles.Add(triangle);
                    }
                }

                for (int i = 0; i < 4; ++i)
                {
                    if (node.children[i] != null)
                    {
                        GetIntersectedTriangles(node.children[i], minX, minZ, maxX, maxZ, intersectedTriangles);
                    }
                }
            }
        }

        /*      |z+
         *      |
         * 2 3  |
         * 0 1  ----->x+
        */
        void AddTriangle(TriangleQuadTreeNode node, int v0, int v1, int v2)
        {
            if (node.depth == mMaxDepth)
            {
                node.triangles.Add(new Vector3Int(v0, v1, v2));
            }
            else
            {
                Rect regionBounds;
                int regionIdx = GetRegionIndex(node.bounds, v0, v1, v2, out regionBounds);
                if (regionIdx == -1)
                {
                    //三角形不完全包含在任何子区域中,直接将三角形加入到node中
                    node.triangles.Add(new Vector3Int(v0, v1, v2));
                }
                else
                {
                    if (node.children[regionIdx] == null)
                    {
                        //分割node
                        node.children[regionIdx] = new TriangleQuadTreeNode(regionBounds, node.depth + 1);
                    }

                    AddTriangle(node.children[regionIdx], v0, v1, v2);
                }
            }
        }

        //加入顶点管理
        void AddVertex(TriangleQuadTreeNode node, Vector3 vertex, int index)
        {
            if (node.depth == mMaxDepth)
            {
                node.vertices.Add(new VertexInfo(vertex, index));
            }
            else
            {
                Rect regionBounds;
                int regionIdx = GetRegionIndex(node.bounds, vertex, out regionBounds);
                if (regionIdx == -1)
                {
                    //顶点不包含在任何子区域中,直接将顶点加入到node中
                    node.vertices.Add(new VertexInfo(vertex, index));
                }
                else
                {
                    if (node.children[regionIdx] == null)
                    {
                        //分割node
                        node.children[regionIdx] = new TriangleQuadTreeNode(regionBounds, node.depth + 1);
                    }

                    AddVertex(node.children[regionIdx], vertex, index);
                }
            }
        }

        int GetRegionIndex(Rect rect, int v0, int v1, int v2, out Rect regionBounds)
        {
            var min = rect.min;
            var max = rect.max;
            float width = rect.width * 0.5f;
            float height = rect.height * 0.5f;
            var pos0 = mVertices[v0];
            var pos1 = mVertices[v1];
            var pos2 = mVertices[v2];

            Rect r0 = new Rect(min.x, min.y, width, height);
            if (
                r0.Contains(new Vector2(pos0.x, pos0.z)) &&
                r0.Contains(new Vector2(pos1.x, pos1.z)) &&
                r0.Contains(new Vector2(pos2.x, pos2.z))
            )
            {
                regionBounds = r0;
                return 0;
            }

            Rect r1 = new Rect(min.x + width, min.y, width, height);
            if (
                r1.Contains(new Vector2(pos0.x, pos0.z)) &&
                r1.Contains(new Vector2(pos1.x, pos1.z)) &&
                r1.Contains(new Vector2(pos2.x, pos2.z))
            )
            {
                regionBounds = r1;
                return 1;
            }

            Rect r2 = new Rect(min.x, min.y + height, width, height);
            if (
                r2.Contains(new Vector2(pos0.x, pos0.z)) &&
                r2.Contains(new Vector2(pos1.x, pos1.z)) &&
                r2.Contains(new Vector2(pos2.x, pos2.z))
            )
            {
                regionBounds = r2;
                return 2;
            }

            Rect r3 = new Rect(min.x + width, min.y + height, width, height);
            if (
                r3.Contains(new Vector2(pos0.x, pos0.z)) &&
                r3.Contains(new Vector2(pos1.x, pos1.z)) &&
                r3.Contains(new Vector2(pos2.x, pos2.z))
            )
            {
                regionBounds = r3;
                return 3;
            }

            regionBounds = new Rect();
            return -1;
        }

        bool Contains(float minX, float minY, float width, float height, float x, float y)
        {
            float maxX = minX + width;
            float maxY = minY + height;
            if (x >= minX && x <= maxX && y >= minY && y <= maxY)
            {
                return true;
            }

            return false;
        }

        int GetRegionIndex(Rect rect, Vector3 vertex, out Rect regionBounds)
        {
            var min = rect.min;
            var max = rect.max;
            float width = rect.width * 0.5f;
            float height = rect.height * 0.5f;

            if (Contains(min.x, min.y, width, height, vertex.x, vertex.z))
            {
                regionBounds = new Rect(min.x, min.y, width, height);
                return 0;
            }

            if (Contains(min.x + width, min.y, width, height, vertex.x, vertex.z))
            {
                regionBounds = new Rect(min.x + width, min.y, width, height);
                return 1;
            }

            if (Contains(min.x, min.y + height, width, height, vertex.x, vertex.z))
            {
                regionBounds = new Rect(min.x, min.y + height, width, height);
                return 2;
            }

            if (Contains(min.x + width, min.y + height, width, height, vertex.x, vertex.z))
            {
                regionBounds = new Rect(min.x + width, min.y + height, width, height);
                return 3;
            }

            regionBounds = new Rect();
            return -1;
        }

#if UNITY_EDITOR
        private void CreateViewer(string name, bool meshVisible)
        {
            int indexCount = mEndIndex - mStartIndex + 1;
            Debug.Assert(indexCount % 3 == 0);

            int nTriangles = indexCount / 3;

            if (nTriangles > 0)
            {
                mViewer = new BigMeshViewer();
                List<int> meshIndices = new List<int>();
                for (int i = 0; i < nTriangles; ++i)
                {
                    meshIndices.Add(mIndices[mStartIndex + i * 3]);
                    meshIndices.Add(mIndices[mStartIndex + i * 3 + 1]);
                    meshIndices.Add(mIndices[mStartIndex + i * 3 + 2]);
                }

                var randColor = new Color32((byte) Random.Range(0, 256), (byte) Random.Range(0, 256),
                    (byte) Random.Range(0, 256), 130);
                mViewer.Create(null, name, vertices, meshIndices.ToArray(), true, randColor);
                mViewer.active = meshVisible;
                if (Map.currentMap.isEditorMode)
                {
                    mViewer.active = false;
                }
            }
        }

        public bool visible
        {
            get => mViewer?.active ?? false;
            set
            {
                if (mViewer == null)
                {
                    CreateViewer("triangle quad tree", false);
                }

                if (mViewer != null)
                {
                    mViewer.active = value;
                    //if (Map.currentMap.isEditorMode)
                    //{
                    //    mViewer.active = false;
                    //}
                }
            }
        }
#endif

        public Vector3[] vertices
        {
            get { return mVertices; }
        }

        public Rect bounds
        {
            get { return mRootNode.bounds; }
        }

        Vector3[] mVertices;
        int[] mIndices;
        TriangleQuadTreeNode mRootNode;
        int mMaxDepth;
        int mStartIndex;
        int mEndIndex;

#if UNITY_EDITOR
        BigMeshViewer mViewer;
#endif

        //中间计算变量,减少GC
        List<Vector3> mTriangleVertices = new List<Vector3>() { Vector3.zero, Vector3.zero, Vector3.zero };
    }
}