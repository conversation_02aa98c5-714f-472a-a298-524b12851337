﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */


using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using UnityEngine;

namespace TFW.Map
{
    /// <summary>
    /// importer的管理器,使用major版本来管理importer
    /// </summary>
    public static class MapBinaryImporterManager
    {
        static MapBinaryImporterManager()
        {
            RegisterImporter(new MapBinaryImporterV2(2));
        }

        public static config.SLGMakerData LoadFromStream(string mapName, Stream stream)
        {
            if (stream != null)
            {
                using var reader = new BinaryReader(stream);
                var type = Utils.ReadString(reader);
                if (type == "comp")
                {
                    Debug.Assert(false, "不考虑压缩了");
#if false
                    // decompress file data
                    var originalFileSize = reader.ReadInt32();
                    var compressedFileSize = reader.ReadInt32();
                    int startOffset = bytes.Length - compressedFileSize;
                    reader.Close();
                    reader = Decompress(bytes, startOffset, originalFileSize, compressedFileSize);
#endif
                }

                // 读取major和minor版本
                var majorVersion = reader.ReadInt32();
                var minorVersion = reader.ReadInt32();
                mImporters.TryGetValue(majorVersion, out var importer);
                if (importer != null)
                {
                    var mapData = importer.Load(minorVersion, mapName, reader);
                    reader.Close();
                    return mapData;
                }

                reader.Close();
            }

            Debug.LogError($"Load Map Failed: {mapName}");

            // 返回默认值,可以创建一个空地图
            return new config.SLGMakerData(0, 0, 0, false, 0, 0, false, 0, 0, new config.BackgroundSetting(),
                new Version(0, 0), new Bounds());
        }

        public static config.SLGMakerData Load(string path)
        {
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            return LoadFromStream(Utils.GetPathName(path, false), stream);
        }


        public static async UniTask<config.SLGMakerData> LoadAsync(string path)
        {
            config.SLGMakerData mapData = null;

            using var stream= await MapModuleResourceMgr.LoadTextStreamAsync(path);
            {
                //UnityEngine.Debug.Log($"[MapDelayInitMgr] LoadTextStreamAsync step01");
                if (stream != null)
                {
                    using BinaryReader reader = new BinaryReader(stream);
                    string type = Utils.ReadString(reader);
                    //读取major和minor版本.
                    int majorVersion = reader.ReadInt32();
                    int minorVersion = reader.ReadInt32();

                    MapBinaryImporter importer = null;
                    mImporters.TryGetValue(majorVersion, out importer);
                    if (importer != null)
                    {
                        string mapName = Utils.GetPathName(path, false);
                        mapData = importer.Load(minorVersion, mapName, reader);
                    }

                    reader.Close();
                }
                //MapModuleResourceMgr.UnloadText(assetPath);
                if (mapData != null)
                {
                    //UnityEngine.Debug.Log($"[MapDelayInitMgr] LoadTextStreamAsync step13"); 
                }
                else
                {
                    Debug.LogError($"Load Map Failed: {path}");
                    //UnityEngine.Debug.Log($"[MapDelayInitMgr] LoadTextStreamAsync step16");
                    //返回默认值,可以创建一个空地图
                    mapData=new config.SLGMakerData(0, 0, 0, false, 0, 0, false, 0, 0, new config.BackgroundSetting(), new Version(0, 0), new Bounds());
                }
            }
            
            return mapData;
        }

        public static void RegisterImporter(MapBinaryImporter importer)
        {
            if (mImporters.ContainsKey(importer.version) == false)
            {
                mImporters.Add(importer.version, importer);
            }
        }

        private static BinaryReader Decompress(byte[] bytes, int startOffset, int originalFileSize,
            int compressedFileSize)
        {
            var compressedData = new byte[compressedFileSize];
            Array.Copy(bytes, startOffset, compressedData, 0, compressedFileSize);
            var uncompressedData = new byte[originalFileSize];

            using var compressedDataStream = new MemoryStream(compressedData);
            using var decompressionStream = new GZipStream(compressedDataStream, CompressionMode.Decompress);

            var decompressedCount = decompressionStream.Read(uncompressedData, 0, originalFileSize);
            Debug.Assert(decompressedCount == originalFileSize);

            return new BinaryReader(new MemoryStream(uncompressedData));
        }

        private static Dictionary<int, MapBinaryImporter> mImporters = new Dictionary<int, MapBinaryImporter>();
    }
}