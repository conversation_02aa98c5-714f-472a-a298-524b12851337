﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;

namespace TFW.Map
{
    public class CombineRiverPrefabWindow : EditorWindow
    {
        void OnEnable()
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }
        }

        void OnGUI()
        {
            mOnlyCombineLOD0 = EditorGUILayout.ToggleLeft("Only Combine LOD0", mOnlyCombineLOD0);
            mCombineMaterial = EditorGUILayout.ToggleLeft("Combine Material", mCombineMaterial);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(mRiverAssetFolder);
            if (GUILayout.Button("Select River Assets Folder"))
            {
                mRiverAssetFolder = EditorUtility.OpenFolderPanel("Select River Assets Folder", "Assets", ""); ;
                mRiverAssetFolder = Utils.ConvertToUnityAssetsPath(mRiverAssetFolder);
            }
            EditorGUILayout.EndHorizontal();

            if (!string.IsNullOrEmpty(mRiverAssetFolder))
            {
                if (GUILayout.Button("Combine"))
                {
                    RiverCombiner.Combine(mRiverAssetFolder, mCombineMaterial, mOnlyCombineLOD0);
                }
            }
        }

        bool mCombineMaterial = true;
        bool mOnlyCombineLOD0 = true;
        string mRiverAssetFolder = "";
    }
}


#endif