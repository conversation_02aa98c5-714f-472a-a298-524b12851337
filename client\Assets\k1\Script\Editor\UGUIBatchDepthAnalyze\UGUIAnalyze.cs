﻿using System;
using System.Collections.Generic;
using System.Text;
using TFW.UI;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace StaticUITool
{
    [InitializeOnLoad]
    public class UGUIAnalyze
    {
        // 层级窗口项回调
        private static readonly EditorApplication.HierarchyWindowItemCallback hiearchyItemCallback;

        private static Texture2D hierarchyIcon;
        private static Texture2D HierarchyIcon
        {
            get
            {
                if (UGUIAnalyze.hierarchyIcon == null)
                {
                    UGUIAnalyze.hierarchyIcon = (Texture2D)Resources.Load("icon_1");
                }
                return UGUIAnalyze.hierarchyIcon;
            }
        }

        /// <summary>
        /// 静态构造
        /// </summary>
        static UGUIAnalyze()
        {
            UGUIAnalyze.hiearchyItemCallback = new EditorApplication.HierarchyWindowItemCallback(UGUIAnalyze.DrawHierarchyIcon);
            EditorApplication.hierarchyWindowItemOnGUI = (EditorApplication.HierarchyWindowItemCallback)Delegate.Combine(
                EditorApplication.hierarchyWindowItemOnGUI,
                UGUIAnalyze.hiearchyItemCallback);
        }
        
       
        //重绘制Hierarchy面板
        private static void DrawHierarchyIcon(int instanceID, Rect selectionRect)
        {
            GameObject go = EditorUtility.InstanceIDToObject(instanceID) as GameObject;
            Rect rectCheck = new Rect(selectionRect);

            rectCheck.x += 200;
            // 绘制Label来覆盖原有的名字
            if (go != null)
            {
                GUIStyle style = new GUIStyle();
                style.richText = true;
                var str = new StringBuilder();
                if(OnlyUseRayCastTarget(go))
                {
                    str.Append("只作为点击事件的Image或TFWImage，替换为TFWEmpty4RayCast");
                }
                else if(ImageToTFWImage(go))
                {
                    str.Append("Image组件替换成TFWImage，父节点为滚动列表暂不处理");
                }
                else if (RawImageToTFWRawImage(go))
                {
                    str.Append("RawImage组件替换成TFWRawImage,父节点为滚动列表暂不处理");
                }
                else if (MaskToRectMask2D(go))
                {
                    str.Append("遮罩为矩形框显示的Mask替换成RectMask2D(父节点为滚动列表不替换)");
                }else if(RemoveUnUsedImage(go))
                {
                    str.Append("不显示但占用DC，透明的TFWImage尝试删除(注意是否涉及逻辑)");
                }
                GUI.Label(rectCheck, str.ToString(), style);
            } 
        }
        private static bool OnlyUseRayCastTarget(GameObject obj)
        {
            var tfwImage = obj.GetComponent<TFWImage>();
            if (tfwImage != null && tfwImage.enabled&& tfwImage.color.a == 0 && tfwImage.raycastTarget && tfwImage.sprite == null)
                return true;
            var image = obj.GetComponent<Image>();
            if (image != null  &&image.enabled && image.color.a == 0 && image.raycastTarget && image.sprite == null)
                return true;
            return false;
        }
        private static bool ImageToTFWImage(GameObject obj)
        {
            var image = obj.GetComponent<Image>();
            var tfwImage = obj.GetComponent<TFWImage>();
            return image != null && image.enabled && image.sprite != null&& image.color.a > 0 && tfwImage == null;
        }
        private static bool RawImageToTFWRawImage(GameObject obj)
        {
            var rawImage = obj.GetComponent<RawImage>();
            var tfwRawImage = obj.GetComponent<TFWRawImage>();
            return rawImage != null && rawImage.enabled && rawImage.mainTexture != null && rawImage.color.a > 0 && tfwRawImage == null;
        }
        private static bool MaskToRectMask2D(GameObject obj)
        {
            var mask = obj.GetComponent<Mask>();
            TFWLoopListView parentLoopListView = null;
            if (obj.transform.parent!= null)
                parentLoopListView = obj.transform.parent.GetComponent<TFWLoopListView>();
            return mask != null && mask.enabled && parentLoopListView== null;
        }
        private static bool RemoveUnUsedImage(GameObject obj)
        {
            var tfwImage = obj.GetComponent<TFWImage>();
            if (tfwImage != null && tfwImage.enabled && tfwImage.color.a == 0 && !tfwImage.raycastTarget)
                return true;
            return false;
        }
    }
}