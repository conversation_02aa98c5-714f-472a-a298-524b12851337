

using UnityEngine;
using System.Collections.Generic;
using Newtonsoft.Json;

/**
 *  unity调用ios中的方法
 */
using System.Runtime.InteropServices;
namespace maxsdk
{

#if UNITY_IOS && !UNITY_EDITOR
    public class MaxSDKUnitySupportIOS : MaxSDKUnitySupportBase
    {

        public override int GetSDKType()
        {
            int sdkType = MaxSDKType.MAX_SDK_UNKNOWN;
            string typeStr = MaxSDK_Call_Sync("getSDKType", "");
            
            if (int.TryParse(typeStr, out int parsedSdkType))
            {
                sdkType = parsedSdkType;
            }
            else
            {
                Debug.LogError("Failed to parse SDK type: " + typeStr);
            }
            return sdkType;
        }

        public override void SetListener(MaxSDKListener listener)
        {
            Debug.Log("gameObject is " + listener.gameObject.name);
            if (listener == null)
            {
                Debug.LogError("set SQSDKListener error, listener is null");
                return;
            }
            string gameObjectName = listener.gameObject.name;

            MaxSDK_Call_Async("setGameObject", gameObjectName);
        }

        public override void Init(MaxSDKInitInfo info)
        {
            MaxSDK_Call_Async("init", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Login(MaxSDKLoginInfo info)
        {
            MaxSDK_Call_Async("login", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Logout(MaxSDKLogoutInfo info)
        {
            MaxSDK_Call_Async("logout", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Pay(MaxSDKPayInfo info)
        {
            MaxSDK_Call_Async("pay", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void Share(MaxSDKShareInfo info)
        {
            MaxSDK_Call_Async("share", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void ReportRoleInfo(MaxSDKRoleInfo info)
        {
            MaxSDK_Call_Async("reportRoleInfo", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void SwitchAccount(MaxSDKSwitchAccountInfo info)
        {
            MaxSDK_Call_Async("switchAccount", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void ReportEvent(MaxSDKReportEventInfo info)
        {
            MaxSDK_Call_Async("reportEvent", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void ExitGame(MaxSDKExitInfo info)
        {
            MaxSDK_Call_Async("exitGame", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override void OpenActionExt(MaxSDKActionInfo info)
        {
            MaxSDK_Call_Async("openActionExt", JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        public override bool IsActionSupported(int type)
        {
            bool isSupported = false;
            string resultStr = MaxSDK_Call_Sync("isActionSupported", type.ToString());
            bool.TryParse(resultStr, out isSupported);
            return isSupported;
        }

        /**
        * 扩展接口（同步），支持返回值
        */
        public override string DispatchSync(MaxSDKDispatchInfo info)
        {
            return MaxSDK_Call_Sync(info.apiName, JsonConvert.SerializeObject(info, Formatting.Indented));
        }


        /**
         * 扩展接口（异步），带统一回调方法  OnDispatchResult(MaxSDKDispatchBean bean)
         */
        public override void DispatchASync(MaxSDKDispatchInfo info)
        {
            MaxSDK_Call_Async(info.apiName, JsonConvert.SerializeObject(info, Formatting.Indented));
        }

        [DllImport("__Internal")]
        private static extern void MaxSDK_Call_Async(string method, string data);

        [DllImport("__Internal")]
        private static extern string MaxSDK_Call_Sync(string method, string data);

  
    }
#endif
}

