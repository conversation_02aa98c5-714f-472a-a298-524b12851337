%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: rongyan
  m_Shader: {fileID: 4800000, guid: 5da664efc9846fb4fa446e4e9c134185, type: 3}
  m_ShaderKeywords: ALPHA_FOR_R_OFF DAYNIGHTTOGGLE_ON _METALLICGLOSSMAP _NORMALMAP
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Alpha:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 1d0677b739f9a8845b150b7b90bde9c4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cubemap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DiffuseTexture:
        m_Texture: {fileID: 2800000, guid: 1766d4e12ee377b4e941e8b44230be8d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Dissolvenoise:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ExtraLightTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 1766d4e12ee377b4e941e8b44230be8d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Main_Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 02a6ca66709fd7240a541c07fb12ca59, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RampTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _T_mask:
        m_Texture: {fileID: 2800000, guid: 02a6ca66709fd7240a541c07fb12ca59, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TerrainHolesTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureNoise:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - ALPHA_FOR_R: 0
    - DayNightToggle: 1
    - PixelSnap: 0
    - _BlurSize: 1
    - _BorderWidth: 0.514
    - _BumpScale: 1
    - _CamaB: 20
    - _CamaE: 30
    - _ColorStrength: 1
    - _CubemapL: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Emission: 0.9
    - _Emission_Power: 1
    - _EnableExternalAlpha: 0
    - _ExtraLightIntensity: 1
    - _ExtraLightTexClip: 0.01
    - _GlitterSpeed: 5
    - _GlitterStrength: 0.236
    - _GlossMapScale: 1
    - _Glossiness: 0.087
    - _GlossyReflections: 1
    - _Height: -0.0001
    - _InvFade: 1
    - _LightMapLocalIntensity: 1
    - _MaskDayNightInfluence: 1
    - _Metallic: 0
    - _Mode: 0
    - _N_BY_KD: 0.01
    - _N_BY_QD: 4.65
    - _N_mask: 2
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Parallax: 0.02
    - _Raodong: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpeedX: 0
    - _SpeedY: 0
    - _SrcBlend: 1
    - _Threshold: 0.1
    - _Transparent: 1
    - _UVSec: 0
    - _UseOverLy: 0
    - _Usedepth: 0
    - _Usetexturecolor: 0
    - _Usetexturedissolve: 0
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _C_BYcolor: {r: 2, g: 0, b: 0, a: 1}
    - _Color: {r: 2, g: 2, b: 2, a: 1}
    - _ColorAlpha: {r: 1, g: 1, b: 1, a: 1}
    - _DissolveEnd: {r: 1, g: 0, b: 0, a: 1}
    - _DissolveStart: {r: 0, g: 0, b: 0, a: 1}
    - _Dissolvecolor: {r: 1, g: 1, b: 1, a: 1}
    - _DissolvespeedXY: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _Maincolor: {r: 0.7609469, g: 0.8547776, b: 0.9433962, a: 1}
    - _MaskColor: {r: 1, g: 1, b: 1, a: 1}
    - _Noisecolor: {r: 0.2470588, g: 0.3012382, b: 0.3607843, a: 1}
    - _NoisespeedXYEmissonZPowerW: {r: 0.5, g: 0, b: 2, a: 1}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _TexControl: {r: 0, g: 0, b: 1, a: 1}
    - _TintColor: {r: 0.9999999, g: 0.9999999, b: 0.9999999, a: 0.5}
  m_BuildTextureStacks: []
