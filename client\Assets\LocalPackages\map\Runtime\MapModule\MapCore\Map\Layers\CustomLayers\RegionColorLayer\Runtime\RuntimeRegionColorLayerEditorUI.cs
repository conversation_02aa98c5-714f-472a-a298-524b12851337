﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    [CustomEditor(typeof(RuntimeRegionColorLayerEditor))]
    public partial class RuntimeRegionColorLayerEditorUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mEditor = target as RuntimeRegionColorLayerEditor;
        }

        void OnDisable()
        {
            mEditor.selectedLayerIndex = 0;
            mEditor.selectedRegionIndex = -1;
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                CreateLayerNames();

                int newLayer = EditorGUILayout.Popup("Layers", mEditor.selectedLayerIndex, mLayerNames);
                if (newLayer != mEditor.selectedLayerIndex)
                {
                    SetLayer(newLayer);
                }

                var layer = mEditor.GetLayer();
                var subLayer = layer.layerData.GetLayer(mEditor.selectedLayerIndex);
                if (mEditor.selectedRegionIndex == -1 && subLayer.regions.Length > 0)
                {
                    SetSelectedRegion(0);
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Save"))
                {
                    Save();
                }
                if (GUILayout.Button("Apply Properties To Reference Prefab"))
                {
                    if (mEditor.prefab != null)
                    {
                        mEditor.GetLayer().ApplyPropertiesToReferenceGameObject(mEditor.prefab, mPropertyEditor.properties);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Select a reference game object first!", "OK");
                    }
                }
                EditorGUILayout.EndHorizontal();

                var newPrefab = EditorGUILayout.ObjectField("Reference GameObject", mEditor.prefab, typeof(GameObject), true, null) as GameObject;
                if (newPrefab != mEditor.prefab)
                {
                    mEditor.SetReferencePrefab(newPrefab);
                }

                mPropertyEditor.Draw();
                DrawBrushes();

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Grid Width", subLayer.tileWidth.ToString());
                EditorGUILayout.LabelField("Grid Height", subLayer.tileHeight.ToString());
                EditorGUILayout.LabelField("Horizontal Region Count", subLayer.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Vertical Region Count", subLayer.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void SetSelectedRegion(int idx)
        {
            if (mEditor.selectedLayerIndex >= 0 && idx >= 0)
            {
                mEditor.selectedRegionIndex = idx;
                var subLayer = mEditor.GetLayer().layerData.GetLayer(mEditor.selectedLayerIndex);
                var region = subLayer.regions[idx];
                mPropertyEditor.Show(region.properties, OnAddRuinProperty, OnRemoveRuinProperty, OnRenameRuinProperty);
            }
        }

        void DrawBrushes()
        {
            mShowTemplate = EditorGUILayout.Foldout(mShowTemplate, "Brush Setting");
            if (mShowTemplate)
            {
                var layer = mEditor.GetLayer();
                var subLayer = layer.layerData.GetLayer(mEditor.selectedLayerIndex);
                var templates = subLayer.regions;
                int selectedRegionIndex = -1;
                bool selectionChange = false;
                for (int i = 0; i < templates.Length; ++i)
                {
                    var region = templates[i] as RuntimeRegionColorData;
                    selectionChange = DrawTemplate(region, i);
                    if (selectionChange)
                    {
                        selectedRegionIndex = i;
                    }
                }
                if (selectedRegionIndex >= 0)
                {
                    SetSelectedRegion(selectedRegionIndex);
                }
            }
        }

        bool DrawTemplate(RuntimeRegionColorData template, int i)
        {
            EditorGUIUtility.labelWidth = 40;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal("GroupBox");
            bool nowSelected = mEditor.selectedRegionIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            EditorGUIUtility.fieldWidth = 100;
            EditorGUILayout.IntField("ID", template.id);
            EditorGUIUtility.fieldWidth = 0;

            EditorGUILayout.ColorField("", template.color);

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.labelWidth = 0;

            return selectionChange;
        }

        void SetLayer(int newLayer)
        {
            Debug.Assert(newLayer >= 0 && mEditor.selectedLayerIndex != newLayer);

            mEditor.selectedLayerIndex = newLayer;
            mEditor.GetLayer().SetLayer(newLayer);
            mEditor.selectedRegionIndex = -1;
        }

        void CreateLayerNames()
        {
            var layerData = mEditor.GetLayer().layerData;
            int n = layerData.layerCount;
            if (mLayerNames == null || mLayerNames.Length != n)
            {
                mLayerNames = new string[n];
                for (int i = 0; i < n; ++i)
                {
                    mLayerNames[i] = layerData.GetLayer(i).name;
                }
            }
        }

        void Save()
        {
            string path = $"{Map.currentMap.dataFolder}/{MapCoreDef.RUNTIME_REGION_COLOR_LAYER_DATA}";
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            int version = 1;
            writer.Write(version);

            var layerData = mEditor.GetLayer().layerData;
            int layerCount = layerData.layerCount;
            writer.Write(layerCount);

            for (int s = 0; s < layerCount; ++s)
            {
                var layer = layerData.GetLayer(s);
                var regions = layer.regions;
                writer.Write(regions.Length);
                for (int i = 0; i < regions.Length; ++i)
                {
                    writer.Write(regions[i].id);
                    Utils.WriteProperties(writer, regions[i].properties);
                }
            }

            var data = stream.ToArray();
            File.WriteAllBytes(path, data);
            writer.Close();

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        void OnAddRuinProperty(PropertyBase property)
        {
            var layer = mEditor.GetLayer();

            var subLayer = layer.layerData.GetLayer(mEditor.selectedLayerIndex);
            var regions = subLayer.regions;
            for (int i = 0; i < regions.Length; ++i)
            {
                if (i != mEditor.selectedRegionIndex)
                {
                    regions[i].properties.AddProperty(property.Clone());
                }
            }
        }

        void OnRemoveRuinProperty(PropertyBase property)
        {
            var layer = mEditor.GetLayer();

            var subLayer = layer.layerData.GetLayer(mEditor.selectedLayerIndex);
            var regions = subLayer.regions;
            for (int i = 0; i < regions.Length; ++i)
            {
                if (i != mEditor.selectedRegionIndex)
                {
                    regions[i].properties.RemoveProperty(property.name);
                }
            }
        }

        void OnRenameRuinProperty(string oldName, PropertyBase property)
        {
            var layer = mEditor.GetLayer();

            var subLayer = layer.layerData.GetLayer(mEditor.selectedLayerIndex);
            var regions = subLayer.regions;
            for (int i = 0; i < regions.Length; ++i)
            {
                if (i != mEditor.selectedRegionIndex)
                {
                    var prop = regions[i].properties.FindProperty(oldName);
                    prop.name = property.name;
                }
            }
        }

        bool mShowTemplate = true;
      
        string[] mLayerNames;

        RuntimeRegionColorLayerEditor mEditor;
        PropertyDataEditor mPropertyEditor = new PropertyDataEditor(true);
    }
}

#endif