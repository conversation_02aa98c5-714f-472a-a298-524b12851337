﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveModelTemplates(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.ModelTemplate, writer);

            writer.Write(VersionSetting.ModelTemplateStructVersion);
            //-------------------version 1 start------------------------------
            //计算路径的string table
            SaveStringTableV1(writer);
            //get the actually used model templates
            var templates = GetUsedModelTemplates();
            writer.Write(templates.Count);
            for (int i = 0; i < templates.Count; ++i)
            {
                SaveModelTemplateV1(writer, templates[i]);
            }
            //-------------------version 1 end------------------------------
            //-------------------version 2 start------------------------------
            //calculate transform table
            TransformTable table = CalculateTransformTable(templates);
            SaveTransformTable(writer, table);
            for (int i = 0; i < templates.Count; ++i)
            {
                SaveModelTemplateV2(writer, templates[i], table);
            }
            //-------------------version 2 end------------------------------
        }

        void SaveModelTemplateV1(BinaryWriter writer, ModelTemplate temp)
        {
            mIDExport.Export(writer, temp.id);

            var prefabPath = temp.GetLODPrefabPath(0);

            GameObject gameObject = null;
            if (!string.IsNullOrEmpty(prefabPath))
            {
                gameObject = MapModuleResourceMgr.LoadPrefab(prefabPath);
            }
            Bounds bounds = Utils.RectToBounds(temp.bounds);
            if (gameObject != null)
            {
                bounds = GameObjectBoundsCalculator.CalculateBounds(gameObject);
            }

            Utils.WriteBounds(writer, bounds);
            Utils.WriteString(writer, prefabPath);

            writer.Write(temp.isTileModelTemplate);
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.lodCount;
                writer.Write(nLODs);
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.GetChildPrefabTransform(lod);
                    int n = childPrefabTransformList.Count;
                    writer.Write(n);
                    for (int i = 0; i < n; ++i)
                    {
                        if (string.IsNullOrEmpty(childPrefabTransformList[i].path))
                        {
                            Debug.LogError($"Invalid child prefab {i} of {temp.GetLODPrefabPath(lod)}");
                        }
                        int idx = mStringTableIndices[childPrefabTransformList[i].path];
                        writer.Write(idx);
                        Utils.WriteRect(writer, childPrefabTransformList[i].localBoundsInPrefab);
                        //int type = (int)MapCoreDef.GetTileObjectType(childPrefabTransformList[i].tag);
                        writer.Write((int)childPrefabTransformList[i].objectType);
                        Utils.WriteVector3(writer, childPrefabTransformList[i].position);
                        Utils.WriteVector3(writer, childPrefabTransformList[i].editorScaling);
                        Utils.WriteQuaternion(writer, childPrefabTransformList[i].editorRotation);
                    }
                }
            }

            //save lod info
            int nLOD = 0;
            if (temp.lodPrefabPaths != null)
            {
                nLOD = temp.lodPrefabPaths.Count;
            }
            writer.Write(nLOD);
            for (int i = 0; i < nLOD; ++i)
            {
                var idx = mStringTableIndices[temp.lodPrefabPaths[i]];
                writer.Write(idx);
            }
            nLOD = 0;
            if (temp.existedLODs != null)
            {
                nLOD = temp.existedLODs.Count;
            }
            writer.Write(nLOD);
            for (int i = 0; i < nLOD; ++i)
            {
                writer.Write(temp.existedLODs[i]);
            }

            writer.Write(temp.preload);
        }

        void SaveModelTemplateV2(BinaryWriter writer, ModelTemplate temp, TransformTable table)
        {
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.lodCount;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.GetChildPrefabTransform(lod);
                    int n = childPrefabTransformList.Count;
                    for (int i = 0; i < n; ++i)
                    {
                        writer.Write(childPrefabTransformList[i].scaleIndex);
                        writer.Write(childPrefabTransformList[i].rotationIndex);
                    }
                }
            }
        }

        List<ModelTemplate> GetUsedModelTemplates()
        {
            var map = Map.currentMap;
            var templates = map.data.GetUsedModelTemplates();
            return templates;
        }

        void SaveStringTableV1(BinaryWriter writer)
        {
            mStringTableIndices.Clear();
            var templates = GetUsedModelTemplates();
            List<string> uniqueStrings = new List<string>();
            for (int i = 0; i < templates.Count; ++i)
            {
                CalculateTemplateStringTable(writer, templates[i] as ModelTemplate, uniqueStrings);
            }

            int n = uniqueStrings.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, uniqueStrings[i]);
            }
        }

        void CalculateTemplateStringTable(BinaryWriter writer, ModelTemplate temp, List<string> stringTable)
        {
            var prefabPath = temp.GetLODPrefabPath(0);
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.lodCount;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.GetChildPrefabTransform(lod);
                    int n = childPrefabTransformList.Count;
                    for (int i = 0; i < n; ++i)
                    {
                        if (!stringTable.Contains(childPrefabTransformList[i].path))
                        {
                            mStringTableIndices[childPrefabTransformList[i].path] = stringTable.Count;
                            stringTable.Add(childPrefabTransformList[i].path);
                        }
                        //if (!stringTable.Contains(childPrefabTransformList[i].tag))
                        //{
                        //    mStringTableIndices[childPrefabTransformList[i].tag] = stringTable.Count;
                        //    stringTable.Add(childPrefabTransformList[i].tag);
                        //}
                    }
                }
            }

            //save lod info
            int nLOD = 0;
            if (temp.lodPrefabPaths != null)
            {
                nLOD = temp.lodPrefabPaths.Count;
            }
            for (int i = 0; i < nLOD; ++i)
            {
                if (!stringTable.Contains(temp.lodPrefabPaths[i]))
                {
                    mStringTableIndices[temp.lodPrefabPaths[i]] = stringTable.Count;
                    stringTable.Add(temp.lodPrefabPaths[i]);
                }
            }
        }

        TransformTable CalculateTransformTable(List<ModelTemplate> templates)
        {
            List<Vector3> scalings = new List<Vector3>();
            List<Quaternion> rotations = new List<Quaternion>();

            int n = templates.Count;
            for (int i = 0; i < n; ++i)
            {
                var temp = templates[i];
                if (temp.isTileModelTemplate)
                {
                    int nLODs = temp.lodCount;
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        var childPrefabTransforms = temp.childrenPrefabInfo[lod];
                        int childrenCount = childPrefabTransforms.Count;
                        for (int k = 0; k < childrenCount; ++k)
                        {
                            //set scaling index
                            bool found = false;
                            for (int s = 0; s < scalings.Count; ++s)
                            {
                                if (scalings[s] == childPrefabTransforms[k].editorScaling)
                                {
                                    childPrefabTransforms[k].scaleIndex = (short)s;
                                    found = true;
                                    break;
                                }
                            }
                            if (!found)
                            {
                                scalings.Add(childPrefabTransforms[k].editorScaling);
                                childPrefabTransforms[k].scaleIndex = (short)(scalings.Count - 1);
                            }

                            //set rotation index
                            found = false;
                            for (int s = 0; s < rotations.Count; ++s)
                            {
                                if (rotations[s] == childPrefabTransforms[k].editorRotation)
                                {
                                    childPrefabTransforms[k].rotationIndex = (short)s;
                                    found = true;
                                    break;
                                }
                            }
                            if (!found)
                            {
                                rotations.Add(childPrefabTransforms[k].editorRotation);
                                childPrefabTransforms[k].rotationIndex = (short)(rotations.Count - 1);
                            }
                        }
                    }
                }
            }

            return new TransformTable(scalings.ToArray(), rotations.ToArray());
        }

        void SaveTransformTable(BinaryWriter writer, TransformTable table)
        {
            Utils.WriteVector3Array(writer, table.scalings);
            Utils.WriteQuaternionArray(writer, table.rotations);
        }
    }
}

#endif