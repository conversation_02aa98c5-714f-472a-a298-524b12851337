%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Eff_com_gd002_ld
  m_Shader: {fileID: 4800000, guid: abd3b07a45b60c443b2ea4c4583f085d, type: 3}
  m_ValidKeywords:
  - _CA_SOFTPARTICLES_OFF
  - _EFFECTENUM_COLORADD
  m_InvalidKeywords: []
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: f31c2891a7caa654e8f4ed7b1cfa85a6, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BlendOp: 0
    - _BumpScale: 1
    - _CA_SoftParticles: 0
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _ColorMode: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _DstBlend: 0
    - _EffectEnum: 4
    - _EmissionEnabled: 0
    - _Exposure: 1.15
    - _FlipbookMode: 0
    - _GlowEnhance: 0
    - _GlowFactor: 1
    - _InvFade: 5
    - _LightingEnabled: 0
    - _Mode: 0
    - _SoftBevel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SpeedX: 2
    - _SpeedY: 15
    - _SrcBlend: 1
    - _ZWrite: 1
    m_Colors:
    - _BevelRange: {r: 0, g: 1, b: 0.5, a: 0}
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _TintColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
