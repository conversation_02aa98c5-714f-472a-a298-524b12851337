﻿using UnityEngine;

namespace Game.Config
{
    /// <summary>
    /// 游戏配置数据
    /// <AUTHOR>
    /// @date 2020/4/13 15:33:07
    /// @ver 1.0
    /// </summary>
    public static class GameConfig
    {

        #region 当前游戏状态
         
        /// <summary>
        /// 当前是否处于维护状态
        /// </summary>
        public static bool IsAppMaintain = false;

        /// <summary>
        /// 是否未冷库服处理(已经和其他服务器进行了合并)
        /// </summary>
        public static bool IsColdServer = false;

        #endregion

        #region 当前是否处于loading阶段

        /// <summary>
        /// 当前是否处于loading状态
        /// </summary>
        public static bool IsLoading = false;

        #endregion

        #region 当前是否进行重新加载

        /// <summary>
        /// 是否请求进入游戏，如果为true，那么一些登录和关卡相关的信息就需要处理，否则就不需要
        /// </summary>
        public static bool IsEnterGameReq = false;

        #endregion

        #region 时间转换

        /// <summary>
        /// 毫秒转换成秒
        /// </summary>
        public const float GAME_TIME_MILLISECOND_TO_SECOND = 0.001f;

        /// <summary>
        /// 秒转换成分钟
        /// </summary>
        public const float GAME_TIME_SECOND_TO_MINUTE = 60f;

        /// <summary>
        /// 分钟转换成小时
        /// </summary>
        public const float GAME_TIME_MINUTE_TO_HOUR = 60f;
        #endregion

        #region 数值转换

        /// <summary>
        /// INT转换成FLOAT
        /// </summary>
        public const float GAME_INT_TO_FLOAT = 0.001f;

        /// <summary>
        /// FLOAT转换成INT
        /// </summary>
        public const int GAME_FLOAT_TO_INT = 1000;

        /// <summary>
        /// 获取士兵等级
        /// </summary>
        public const int GET_SOLDIER_LEVEL = 10000;

        /// <summary>
        /// 新的士兵等级区间（10000区间已经不够用了）
        /// </summary>
        public const int NEW_SOLDIER_LEVEL = 10000000;

        #endregion

        #region 距离检测最小误差的平方

        /// <summary>
        /// 计算距离最小误差的平方
        /// </summary>
        public const float CALCULATE_DIS_MIN_ERROR_SQUARE = 0.01f;

        #endregion


        #region 配表设置

        /// <summary>
        /// 游戏关卡使用配置数据的关卡数
        /// </summary>
        public const int GAME_LELVE_USE_CONFIG = 100;

        /// <summary>
        /// 游戏士兵使用配置数据的士兵等级
        /// </summary>
        public const int GAME_SOLDIER_LEVEL_USE_CONFIG = 10;

        #endregion

        #region 关卡设置

        /// <summary>
        /// 游戏Boss关卡标记
        /// </summary>
        public const int GAME_BOSS_LEVEL_FLAG = 5;

        /// <summary>
        /// 游戏关卡显示UI通用参数
        /// （关卡大于等于当前值走当前逻辑，否则特殊处理）
        /// </summary>
        public const int GAME_LEVEL_SHOW_COMMON = 2;

        /// <summary>
        /// 第一个关卡
        /// </summary>
        public const int GAME_LEVEL_FIRSTLEVEL = 0;

        /// <summary>
        /// 主界面UI关卡显示的第一个获取的方式的参数（当前关卡-2）
        /// </summary>
        public const int GAME_LEVEL_SHOWFIRSTLEVEL = 2;

        /// <summary>
        /// 关卡显示条显示几个nodes
        /// </summary>
        public const int GAME_LEVEL_SHOWLEVEL_NODES = 5;

        /// <summary>
        /// 怪物进入地图大门路点标记Id
        /// </summary>
        public const int MONSTER_ENTER_MAP_DOOR_WAY_POINT_ID = 1;

        /// <summary>
        /// 怪物进入地图第二个路点标记Id
        /// </summary>
        public const int MONSTER_ENTER_MAP_SECOND_WAY_POINT_ID = 2;

        #endregion

        #region 资源路径

        /// <summary>
        /// UI图集名称
        /// </summary>
        public const string UIATLAS = "MainView";

        /// <summary>
        /// 普通关卡图标大
        /// </summary>
        public const int LEVEL_NODES_BIGIMG = 17000000;


        /// <summary>
        /// 普通关卡图标小
        /// </summary>
        public const int LEVEL_NODES_SMALLIMG = 17000001;
        public const int LEVEL_NODES_SMALLIMG_UIMAIN1 = 1280011;//UIMain1


        /// <summary>
        /// boss关卡图标小
        /// </summary>
        public const int LEVEL_NODES_BOSSSMALLIMG = 17000002;
        public const int LEVEL_NODES_BOSSSMALLIMG_UIMAIN1 = 1280012;//UIMain1

        /// <summary>
        /// boss关卡图标大
        /// </summary>
        public const int LEVEL_NODES_BOSSBIGSMALLIMG = 17000003;


        /// <summary>
        /// boss关卡图标(10)小
        /// </summary>
        public const int LEVEL_NODES_BOSSSMALLIMG_TEN = 17000004;
        public const int LEVEL_NODES_BOSSSMALLIMG_TEN_UIMAIN1 = 1280013;////UIMain1

        /// <summary>
        /// boss关卡图标(10)大
        /// </summary>
        public const int LEVEL_NODES_BOSSBIGSMALLIMG_TEN = 17000005;


        #endregion

        #region 内城英雄战斗
        /// <summary>
        /// 虚影材质颜色
        /// </summary>
        public const string HERO_GHOST_COLOR = "FFFFFF66";

        /// <summary>
        /// 正常英雄颜色
        /// </summary>
        public const string HERO_COMMON_COLOR = "FFFFFFFF";
        #endregion


        #region 场景设置

        /// <summary>
        /// 默认关卡场景名字
        /// </summary>
        public const string DEFAULT_SCENE_NAME = "Main";

        #endregion


        #region 联盟战

        /// <summary>
        /// 联盟战区域宽度数量
        /// </summary>
        public const int ALLIANCE_WAR_AREA_WIDTH_COUNT = 15;
        /// <summary>
        /// 联盟战区域高度数量
        /// </summary>
        public const int ALLIANCE_WAR_AREA_HEIGHT_COUNT = 15;
        /// <summary>
        /// 联盟战单块区域大小
        /// </summary>
        public const int ALLIANCE_WAR_AREA_SIZE = 480;

        /// <summary>
        /// 是否默认显示选中的区域信息，只有一次有效
        /// </summary>
        public static bool IS_AUTO_SELECT_AREA_INFO = false;

        #endregion

        /// <summary>
        /// 部队采集金币加速  读取配置 CBuffProperty   属性来自于部队（英雄）
        /// </summary>
        public const int GatherGoldBuffSpeedUpId_Hero = 21028;

        /// <summary>
        /// 玩家采集金币加速  属性来自于玩家
        /// </summary>
        public const int GatherGoldBuffSpeedUpId_Player = 11608;

        /// <summary>
        /// 联盟成员采集速度%
        /// </summary>
        public const int AllianceGoldSpeedBuffId = 30002;

        /// <summary>
        /// 联盟领地内采集速度%
        /// </summary>
        public const int AllianceTerritoryGoldSpeedBuffId = 30026;
        
        /// <summary>
        /// 出征体力消耗减少
        /// </summary>
        public const int TroopCostStrength=20502;

        /// <summary>
        /// 联盟礼物 上限加成
        /// </summary>
        public const int AllianceGiftLimitBuffId = 30019;
        /// <summary>
        /// 联盟属性 行军百分比
        /// </summary>
        public const int AllianceMarchSpeedBuffId = 30001;

        /// <summary>
        /// 联盟属性 战力加成
        /// </summary>
        public const int AlliancePowerUp = 30029;
        /// <summary>
        /// 联盟属性 联盟领地行军加成
        /// </summary>
        public const int AllianceLandSpeedBuffId = 30024;

        /// <summary>
        /// 全部队加速加成玩家属性  读取配置 CBuffProperty 
        /// </summary>
        public const int MonthCardBuffSpeedUpId = 11006;//全部队加速 玩家属性

        /// <summary>
        /// 当前上阵的英雄部队加速  读取配置 CBuffProperty 
        /// </summary>
        public const int HeroSpeedUpBuffId = 21006;

        /// <summary>
        /// 战力加成
        /// </summary>
        public const int PROP_POWER = 11507;

        /// <summary>
        /// 联盟限制最大输入等级
        /// </summary>
        public const int LimitLevelMax = 9999;

        /// <summary>
        /// 我方行军绿色
        /// </summary>
        public static Color SelfMarchLineColor = Color.green;

        /// <summary>
        /// 盟友行军蓝色
        /// </summary>
        public static Color AllyMarchLineColor = new Color(75f / 255, 166f / 255, 230f / 255, 1);

        /// <summary>
        /// 敌方行军是自己红色
        /// </summary>
        public static Color EnemyMarchLineColor = Color.red;// new Color(241f / 255, 50f / 255, 26f / 255, 1);


        public static Color EnemyMarchLineColorServer = new Color(255f / 255, 162f / 255, 69f / 255, 1);
        /// <summary>
        /// 敌方行军白色
        /// </summary>
        public static Color EnemyMarchLineColor1 = Color.white;// new Color(241f / 255, 50f / 255, 26f / 255, 1);

        /// <summary>
        /// 玩家英雄带兵容量添加
        /// </summary>
        public const int PROP_Hero_Capcity_Solider = 12054;

        /// <summary>
        /// 联盟战争列表的tips红点
        /// </summary>
        public const string ALLIANCE_WAR_TERRITORY_LIST_TIPS_RED = "Alliance_War_Territory_List_Tips_Red";

        /// <summary>
        /// AP恢复速度增加%
        /// </summary>
        public const int PROP_RECOVER_ACTION_POINT = 11509;
        /// <summary>
        /// 主城升级金币消耗降低%
        /// </summary>
        public const int PROP_CITY_UPCOIN_DEC = 11510;
        /// <summary>
        /// 进化升级金币消耗降低%
        /// </summary>
        public const int PROP_EVO_UPCOIN_DEC = 11511;
        /// <summary>
        /// 新科技升级金币消耗降低%
        /// </summary>
        public const int PROP_TECH_UPCOIN_DEC = 11512;
        /// <summary>
        /// 所有功能消耗金币降低%
        /// </summary>
        public const int PROP_ALL_UPCOIN_DEC = 11513;

        /// <summary>
        /// 联盟旗帜上限+N  联盟旗帜数量+{0}
        /// </summary>
        public const int PROP_UNION_FLAG_NUM_ADD = 30027;

        /// <summary>
        /// 左侧副将开启数目属性ID
        /// </summary>
        public const int PROP_DeputyHero_LEFT = 11609;
        /// <summary>
        /// 右侧副将开启数目属性ID
        /// </summary>
        public const int PROP_DeputyHero_RIGGT = 11610;

        /// <summary>
        /// 次元宝藏活动每日可购买攻击次数
        /// </summary>
        public const int PROP_WARPMINE_ATTACK_BUY = 11611;

        /// <summary>
        /// 回归活动体力消耗减少 
        /// </summary>
        public const int PROP_RETURN_POWER_RATE = 20502;
       
        public static string MapResRoot = "Assets/k1/Res/Map/";

        /// <summary>
        /// 英雄增加负重属性ID
        /// </summary>
        public const int Hero_Add_Load = 21029;
        /// <summary>
        /// 联盟增加负重属性ID
        /// </summary>
        public const int Alliance_Add_Load = 30003;
    }
}
