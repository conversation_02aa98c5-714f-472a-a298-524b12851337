﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    class SplitterVertexGameObject
    {
        public SplitterVertexGameObject(Vector3 pos, string name, float displayRadius, Transform parent)
        {
            mGameObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            var renderer = mGameObject.GetComponent<MeshRenderer>();
            renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            mGameObject.name = name;
            mGameObject.transform.position = pos;
            mGameObject.transform.localScale = Vector3.one * displayRadius;
            mGameObject.transform.parent = parent;

            Utils.HideGameObject(mGameObject);
        }

        public void OnDestroy()
        {
            if (mMtl != null)
            {
                Object.DestroyImmediate(mMtl);
                mMtl = null;
            }

            if (mGameObject != null)
            {
                Object.DestroyImmediate(mGameObject);
                mGameObject = null;
            }
        }

        public void SetActive(bool active)
        {
            if (mGameObject != null)
            {
                mGameObject.SetActive(active);
            }
        }

        public void SetColor(Color color)
        {
            var renderer = mGameObject.GetComponent<MeshRenderer>();
            renderer.sharedMaterial.SetColor("_Color", color);
        }

        public void SetPosition(Vector3 pos)
        {
            mGameObject.transform.position = pos;
        }

        public Vector3 GetPosition()
        {
            return mGameObject.transform.position;
        }

        public void SetSize(float size)
        {
            mGameObject.transform.localScale = Vector3.one * size;
        }

        public float GetSize()
        {
            return mGameObject.transform.localScale.x;
        }

        GameObject mGameObject;
        Material mMtl;
    }

    class SplitterLinkGameObject
    {
        public SplitterLinkGameObject(Vector3 startPos, Vector3 endPos, float displayRadius, Transform parent)
        {
            mGameObject = new GameObject("link");
            mGameObject.transform.parent = parent;
            var renderer = mGameObject.AddComponent<MeshRenderer>();
            var filter = mGameObject.AddComponent<MeshFilter>();
            filter.sharedMesh = CreateMesh();
            renderer.sharedMaterial = CreateMaterial();

            Update(startPos, endPos, displayRadius);
        }

        public void OnDestroy()
        {
            if (mMaterial != null)
            {
                Object.DestroyImmediate(mMaterial);
                mMaterial = null;
            }

            if (mMesh != null)
            {
                Object.DestroyImmediate(mMesh);
                mMesh = null;
            }

            if(mGameObject != null)
            {
                GameObject.DestroyImmediate(mGameObject);
                mGameObject = null;
            }
        }

        public void Update(Vector3 startPos, Vector3 endPos, float displayRadius)
        {
            var d = endPos - startPos;
            var len = d.magnitude;
            d /= len;
            mGameObject.transform.localScale = new Vector3(len, 0, displayRadius * 0.5f);
            mGameObject.transform.rotation = Quaternion.FromToRotation(Vector3.right, d);
            mGameObject.transform.position = (startPos + endPos) * 0.5f;
        }

        public void SetActive(bool active)
        {
            mGameObject.SetActive(active);
        }

        Mesh CreateMesh()
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]
                {
                    new Vector3(-0.5f, 0, -0.5f),
                    new Vector3(-0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, -0.5f),
                };
                mMesh.triangles = new int[]
                {
                    0,1,2,0,2,3
                };
            }

            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                mMaterial.SetColor("_Color", Color.yellow);
            }
            return mMaterial;
        }

        public void SetColor(Color color)
        {
            mMaterial?.SetColor("_Color", color);
        }

        GameObject mGameObject;
        Mesh mMesh;
        Material mMaterial;
    }

    public class PolygonRiverSplitterModel
    {
        public PolygonRiverSplitterModel(Vector3 startVertexPos, Vector3 endVertexPos, float displayRadius, GameObject parent, bool hasLink)
        {
            mStartVertex = new SplitterVertexGameObject(startVertexPos, "start vertex", displayRadius, parent?.transform);
            mEndVertex = new SplitterVertexGameObject(endVertexPos, "end vertex", displayRadius, parent?.transform);
            if (hasLink)
            {
                mLink = new SplitterLinkGameObject(startVertexPos, endVertexPos, displayRadius, parent?.transform);
            }
        }

        public void SetLinkColor(Color color)
        {
            mLink?.SetColor(color);
        }

        public void SetColor(bool start, Color color)
        {
            if (start)
            {
                mStartVertex?.SetColor(color);
            }
            else
            {
                mEndVertex?.SetColor(color);
            }
        }

        public void SetPosition(bool start, Vector3 pos)
        {
            if (start)
            {
                mStartVertex?.SetPosition(pos);
            }
            else
            {
                mEndVertex?.SetPosition(pos);
            }

            UpdateLink();
        }

        public Vector3 GetPosition(bool start)
        {
            if (start)
            {
                return mStartVertex.GetPosition();
            }
            else
            {
                return mEndVertex.GetPosition();
            }
        }

        public void SetSize(bool start, float size)
        {
            if (start)
            {
                mStartVertex?.SetSize(size);
            }
            else
            {
                mEndVertex?.SetSize(size);
            }

            UpdateLink();
        }

        public void SetActive(bool active)
        {
            mStartVertex?.SetActive(active);
            mEndVertex?.SetActive(active);
            mLink?.SetActive(active);
        }

        public void OnDestroy()
        {
            if (mStartVertex != null)
            {
                mStartVertex.OnDestroy();
                mStartVertex = null;
            }
            if (mEndVertex != null)
            {
                mEndVertex.OnDestroy();
                mEndVertex = null;
            }
            if (mLink != null)
            {
                mLink.OnDestroy();
            }
        }

        public void Update(PolygonRiverSplitterData splitter)
        {
            mStartVertex.SetPosition(splitter.startVertexPosition);
            mEndVertex.SetPosition(splitter.endVertexPosition);
            UpdateLink();
        }

        void UpdateLink()
        {
            mLink?.Update(mStartVertex.GetPosition(), mEndVertex.GetPosition(), mStartVertex.GetSize());
        }

        SplitterVertexGameObject mStartVertex;
        SplitterVertexGameObject mEndVertex;
        SplitterLinkGameObject mLink;
    }
}

#endif