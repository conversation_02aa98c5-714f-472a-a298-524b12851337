﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    class RenderTextureBlendTerrainLayerDataDebug : MonoBehaviour
    {
        public RenderTextureBlendTerrainLayerData layerData = null;
        public bool showGizmo = true;

        void OnGUI()
        {
            if (Map.currentMap != null)
            {
                if (layerData != null)
                {
                    //string flagText = string.Format("mCurrentGeneratedLOD: {0}, mGlobalCurrentLOD: {1}, mGlobalLastLOD: {2}, mCurrentLOD: {3}, mLastLOD: {4}", layerData.currentGeneratedLOD, layerData.currentGlobalLOD, layerData.lastGlobalLOD, layerData.currentLOD, layerData.lastLOD);
                    string flagText = string.Format("current lod: {0}, last lod: {1}", layerData.currentLOD, layerData.lastLOD);
                    GUILayout.BeginVertical();
                    GUILayout.Label(flagText);
                    GUILayout.EndVertical();
                }
            }
        }

        void DrawCube(Vector3 center, Vector2 size, Color color)
        {
            Gizmos.color = color;
            Gizmos.DrawWireCube(center, new Vector3(size.x, 0, size.y));
        }

        void OnDrawGizmos()
        {
            if (layerData != null)
            {
                var lastViewport = layerData.lastViewport;
                var newViewport = Map.currentMap.viewport;
                DrawCube(lastViewport.center, lastViewport.size, Color.cyan);
                DrawCube(newViewport.center, newViewport.size, Color.blue);
            }
        }
    }
}
