
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.Android;
using UnityEngine;
using System.IO;
using System.Text.RegularExpressions;
using System;
using System.Xml;
using System.IO.Compression;
using System.Collections.Generic;
using System.Linq;
using RiverLog;

public class AndroidProjectModify
{
    ////AGP版本
    //public const string AGPVersion = "4.2.0";

    //// gradle 版本
    //public const string GradleVersion = "gradle-6.7.1-all";

    const string CompileSdkVersion = "ANDROID_COMPLIE_SDK_VERSION";
    const string BuildToolsVersion = "ANDROID_COMPLIE_TOOL_VERSION";
    const string MinSdkVersion = "ANDROID_BUILD_MIN_SDK_VERSION";
    const string TargetSdkVersion = "ANDROID_BUILD_TARGET_SDK_VERSION";
    const string PackageNameValue = "PACKAGENAME";
    const string SdkApplicationName = "com.maxsdk.sdk.SQHWSDKApplication";
    const string SdkMainActivity = "com.maxsdk.asm21.MainActivity";
    const string SdkMainActivityPath = "/com/maxsdk/asm21/MainActivity.java";



    [PostProcessBuildAttribute(1)]
    public static void OnPostprocessBuild(BuildTarget target, string pathToBuiltProject)
    {
        // 当前脚本只处理Android工程
        if (target != BuildTarget.Android)
            return;

        //string sdkFolderPath = Application.dataPath + "/Plugins/Android/sdk";

        string sdkZipPath = (Application.dataPath + "/").Replace("/Assets/", "/Tools/Unity_RiverSDK_Android_global_csr_com.csgame.csr_2024-12-30-20-37-27.zip");// MaxSDKSetting.GetunityAndroidSdkPath();

        if (sdkZipPath is null)
        {
            LogError($"请检查sdk 是否已在MaxSdkSetting配置");
            return;
        }

        //解压存放sdk文件夹
        string sdkFolder = Path.Combine(Application.temporaryCachePath, "river_sdk");

        Log("临时存放解压sdk文件路径: " + sdkFolder);


        // 一、解压sdk压缩包
        if (!ExtractSdkZip(sdkZipPath, sdkFolder))
        {
            return;
        }

        // 二、修改 AGP 版本
        string rootBuildGradlePath = Path.Combine(pathToBuiltProject, "build.gradle");
        string agpVersion = "7.4.2";// MaxSDKSetting.GetAGPVersion();
        modifyAGPVersion(rootBuildGradlePath, agpVersion);

        // 三、修改Gradle版本
        string propertiesPath = Path.Combine(pathToBuiltProject, "gradle/wrapper/gradle-wrapper.properties");
        string gradleVersion = "gradle-8.5-all";// MaxSDKSetting.GetGradleVersion();
        modifyGradleVersion(propertiesPath, gradleVersion);

        // 四、复制sdk到unityLibrary模块libs下
        string libsPath = sdkFolder + "/MaxSDK_Android/libs";
        string unityLibsPath = Path.Combine(pathToBuiltProject, "unityLibrary/libs");
        // 检查launcher模块libs目录是否存在，如果不存在则创建
        if (!Directory.Exists(unityLibsPath))
            Directory.CreateDirectory(unityLibsPath);

        // 复制文件夹及其子文件夹中的文件到unityLibrary模块libs目录
        CopyFilesRecursively(libsPath, unityLibsPath);



        //五、修改gradle.properties配置文件
        //获取Demo下gradleProperties配置
        string demoGradleProperties = sdkFolder + "/MaxSDK_Android/MaxSDKDemo/gradle.properties";
        string packageName = ModifyGradleProperties(pathToBuiltProject, demoGradleProperties);


        //六、修改模块build.gradle文件内容
        string launcherBuildGradlePath = Path.Combine(pathToBuiltProject, "launcher/build.gradle");
        string unityLibraryBuildGradlePath = Path.Combine(pathToBuiltProject, "unityLibrary/build.gradle");
        string signReadmePath = sdkFolder + "/MaxSDK_Android/SignFile/"+ packageName + "_README.txt";
        ModifyGradleFile(launcherBuildGradlePath, CompileSdkVersion, BuildToolsVersion, MinSdkVersion, TargetSdkVersion, PackageNameValue, signReadmePath, false);
        ModifyGradleFile(unityLibraryBuildGradlePath, CompileSdkVersion, BuildToolsVersion, MinSdkVersion, TargetSdkVersion, PackageNameValue, "",true);


        //七、修改launcher AndroidManifest下，application的android:name
        string manifestPath = Path.Combine(pathToBuiltProject, "launcher/src/main/AndroidManifest.xml");
        ModifyApplicationName(manifestPath, SdkApplicationName);


        // 八、修改unityLibrary AndroidManifest.xml启动的Activity
        string unityLibraryManifestPath = Path.Combine(pathToBuiltProject, "unityLibrary/src/main/AndroidManifest.xml");
        ModifyMainActivity(unityLibraryManifestPath, SdkMainActivity);

        //九、复制文件
        //1.复制MainActivity到unityLibrary模块下
        string sourceFile = sdkFolder + "/MaxSDK_Android/MaxSDKDemo/launcher/src/main/java" + SdkMainActivityPath;
        string unityJavaPath = Path.Combine(pathToBuiltProject, "unityLibrary/src/main/java");
        string destinationFile = unityJavaPath + SdkMainActivityPath;
        CopyFile(sourceFile, destinationFile);
        //2.复制签名文件
        string originSign = sdkFolder + "/MaxSDK_Android/SignFile/" + packageName + ".jks";
        string destSign = Path.Combine(pathToBuiltProject, "launcher/"+ packageName + ".jks");
        CopyFile(originSign, destSign);

    }


    /// <summary>
    /// 修改gradle.properties配置文件
    /// </summary>
    /// <param name="pathToBuiltProject">输出工程目录</param>
    /// <param name="demoGradleProperties">demo gradle.properties文件</param>
    private static string ModifyGradleProperties(string pathToBuiltProject, string demoGradleProperties)
    {
        Dictionary<string, string> gradlePropertiesMap = ReadGradleProperties(demoGradleProperties);

        string gradlePropertiesPath = Path.Combine(pathToBuiltProject, "gradle.properties");
        string packageName = gradlePropertiesMap[PackageNameValue];

        if (!File.Exists(gradlePropertiesPath))
        {
            File.Create(gradlePropertiesPath);
        }

        using (StreamWriter writer = File.AppendText(gradlePropertiesPath))
        {
            // 写入内容到 gradle.properties 文件
            if (gradlePropertiesMap.ContainsKey(MinSdkVersion))
            {
                writer.WriteLine("\n#最低支持版本");
                writer.WriteLine(MinSdkVersion + "=24");//+ gradlePropertiesMap[MinSdkVersion])
            }
            if (gradlePropertiesMap.ContainsKey(TargetSdkVersion))
            {
                writer.WriteLine("#目标版本");
                writer.WriteLine(TargetSdkVersion + "=" + gradlePropertiesMap[TargetSdkVersion]);
            }
            if (gradlePropertiesMap.ContainsKey(CompileSdkVersion))
            {
                writer.WriteLine("#编译版本");
                writer.WriteLine(CompileSdkVersion + "=" + gradlePropertiesMap[CompileSdkVersion]);
            }
            if (gradlePropertiesMap.ContainsKey(BuildToolsVersion))
            {
                writer.WriteLine("#编译工具版本");
                writer.WriteLine(BuildToolsVersion + "=" + gradlePropertiesMap[BuildToolsVersion]);
            }
            if (gradlePropertiesMap.ContainsKey(PackageNameValue))
            {
                writer.WriteLine("#游戏包名");
                writer.WriteLine(PackageNameValue + "=" + packageName);
            }

        }

        return packageName;
    }


    /// <summary>
    /// 修改Gradle版本
    /// </summary>
    /// <param name="propertiesPath">gradle-wrapper.properties 文件目录</param>
    private static void modifyGradleVersion(string propertiesPath,string gradleVersion)
    {
        if (File.Exists(propertiesPath))
        {
            string propertiesContent = File.ReadAllText(propertiesPath);
            // 替换 AGP 版本
            string distributionPattern = @"distributionUrl=https\\://services.gradle.org/distributions/.*.zip";
            Match match = Regex.Match(propertiesContent, distributionPattern);
            if (match.Success)
            {
                string currentVersion = match.Groups[1].Value;
                if (currentVersion != gradleVersion)
                {
                    // 替换 gradle 版本
                    propertiesContent = Regex.Replace(propertiesContent, distributionPattern, "distributionUrl=https\\://services.gradle.org/distributions/" + gradleVersion + ".zip");
                    File.WriteAllText(propertiesPath, propertiesContent);
                }
            }
        }
    }


    /// <summary>
    /// 获取Unity sdk 文件路径
    /// </summary>
    /// <param name="folderPath">存放unity sdk文件目录</param>
    public static string getRirverSDKZipFile(string folderPath)
    {
        try
        {
            // 获取文件夹下所有文件
            string[] files = Directory.GetFiles(folderPath);

            // 过滤出以 Unity_RiverSDK_Android 开头并且以 .zip 结尾的文件
            string firstZipFile = files.FirstOrDefault(file =>
                Path.GetFileName(file).StartsWith("Unity_RiverSDK_Android") &&
                file.EndsWith(".zip")
            );

            return firstZipFile;
        }
        catch (Exception e)
        {
            LogError($"获取Unity sdk失败: {e.Message}");
            return null;
        }
    }


    /// <summary>
    /// 解压sdk
    /// </summary>
    /// <param name="sdkZipPath">压缩包路径</param>
    /// <param name="sdkFolder">解压到文件目录</param>
    private static bool ExtractSdkZip(string sdkZipPath, string sdkFolder)
    {
        // 检查压缩包路径是否有效
        if (string.IsNullOrEmpty(sdkZipPath))
        {
            LogError("Unity sdk zip不存在.");
            return false;
        }

        // 如果缓存目录存在，删除上一次缓存
        if (Directory.Exists(sdkFolder))
        {
            Directory.Delete(sdkFolder, true);
        }

        // 创建目标文件夹
        Directory.CreateDirectory(sdkFolder);
        // 解压缩文件
        try
        {
            ZipFile.ExtractToDirectory(sdkZipPath, sdkFolder);
            return true;
        }
        catch (Exception e)
        {
            LogError("解压Untiy sdk zip失败: " + e.Message);
            return false;
        }
    }

    /// <summary>
    /// 修改AGP版本
    /// </summary>
    /// <param name="filePath">根目录build.gradle文件路径</param>
    private static void modifyAGPVersion(string filePath,string agpVersion)
    {

        if (File.Exists(filePath))
        {
            string gradleScript = File.ReadAllText(filePath);
            // 使用正则表达式提取 gradle 版本
            string versionPattern = @"com\.android\.tools\.build:gradle:(\d+\.\d+\.\d+)";
            Match match = Regex.Match(gradleScript, versionPattern);
            if (match.Success)
            {
                string currentVersion = match.Groups[1].Value;
                if (currentVersion != agpVersion)
                {
                    // 替换 AGP 版本
                    gradleScript = Regex.Replace(gradleScript, versionPattern, "com.android.tools.build:gradle:" + agpVersion);
                    File.WriteAllText(filePath, gradleScript);
                }
            }
        }
    }


    /// <summary>
    /// 递归复制文件夹及其子文件夹中的文件到目标目录
    /// </summary>
    /// <param name="sourceDirectory">源目录</param>
    /// <param name="destinationDirectory">目的目录</param>
    private static void CopyFilesRecursively(string sourceDirectory, string destinationDirectory)
    {
        // 获取源文件夹中的所有文件和文件夹
        string[] files = Directory.GetFiles(sourceDirectory);
        string[] directories = Directory.GetDirectories(sourceDirectory);

        // 复制文件到目标目录
        foreach (string file in files)
        {
            // 跳过以 .DS_Store 结尾的文件
            if (file.EndsWith(".DS_Store"))
                continue;

            // 跳过以.meta结尾的文件
            if (file.EndsWith(".meta"))
                continue;



            string fileName = Path.GetFileName(file);
            string destinationPath = Path.Combine(destinationDirectory, fileName);

            if (!Directory.Exists(destinationDirectory))
                Directory.CreateDirectory(destinationDirectory);

            File.Copy(file, destinationPath, true);
        }

        // 递归复制子文件夹中的文件
        foreach (string directory in directories)
        {
            string directoryName = Path.GetFileName(directory);
            string destinationSubDirectory = Path.Combine(destinationDirectory, directoryName);

            // 递归复制子文件夹中的文件
            CopyFilesRecursively(directory, destinationSubDirectory);
        }
    }

    /// <summary>
    /// 修改build.gradle文件中的版本号
    /// </summary>
    /// <param name="filePath">build.gradle文件目录</param>
    /// <param name="compileSdkVersion">compile版本</param>
    /// <param name="buildToolsVersion">buildTools版本</param>
    /// <param name="minSdkVersion">最小版本</param>
    /// <param name="targetSdkVersion">目标版本</param>
    /// <param name="packageName">包名</param>
    /// <param name="signFilePath">签名说明文件路径</param>
    /// <param name="isModifyDependence">是否修改依赖</param>
    private static void ModifyGradleFile(string filePath, string compileSdkVersion, string buildToolsVersion, string minSdkVersion, string targetSdkVersion, string packageName,string signFilePath, bool isModifyDependence)
    {
        // 读取build.gradle文件内容
        string[] lines = File.ReadAllLines(filePath);

        string dependenceStatement = "api fileTree(dir: 'libs', include: ['**/*.jar','**/*.aar'])";


        bool hasExistingDependence = false;

        // 查找并替换版本号
        for (int i = 0; i < lines.Length; i++)
        {
            if (lines[i].Trim().StartsWith("compileSdkVersion"))
            {
                // 替换compileSdkVersion版本号
                lines[i] = "    compileSdkVersion " + compileSdkVersion;
            }
            else if (lines[i].Trim().StartsWith("buildToolsVersion"))
            {
                // 替换buildToolsVersion版本号
                lines[i] = "    buildToolsVersion " + buildToolsVersion;
            }
            else if (lines[i].Trim().StartsWith("minSdkVersion"))
            {
                // 替换minSdkVersion版本号
                lines[i] = "    minSdkVersion " + minSdkVersion;
            }
            else if (lines[i].Trim().StartsWith("targetSdkVersion"))
            {
                // 替换targetSdkVersion版本号
                lines[i] = "    targetSdkVersion " + targetSdkVersion;
            }
            else if (lines[i].Trim().StartsWith("applicationId"))
            {
                // 替换targetSdkVersion版本号
                lines[i] = "    applicationId " + packageName;
            }
            else if (lines[i].Contains(dependenceStatement))
            {
                hasExistingDependence = true;
            }
        }

        if (isModifyDependence && !hasExistingDependence)
        {
            // 查找依赖配置的位置
            int dependenciesIndex = -1;
            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].Contains("dependencies"))
                {
                    dependenciesIndex = i;
                    break;
                }
            }

            if (dependenciesIndex != -1)
            {
                string dependency = "    " + dependenceStatement;
                // 添加新的依赖
                lines[dependenciesIndex + 1] += "\n" + dependency;
            }
        }

        //设置签名信息
        if (!string.IsNullOrEmpty(signFilePath)) {
            SetSignInfo(lines,signFilePath);
        }




        // 将修改后的内容写回到build.gradle文件中
        File.WriteAllLines(filePath, lines);
    }


    /// <summary>
    /// 修改Application android:name
    /// </summary>
    /// <param name="manifestPath">文件路径</param>
    /// <param name="newApplicationName">app包名路径</param>
    public static void ModifyApplicationName(string manifestPath, string newApplicationName)
    {
        try
        {
            // 加载AndroidManifest.xml文件
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(manifestPath);

            // 获取Application节点
            XmlNode applicationNode = xmlDoc.SelectSingleNode("/manifest/application");

            // 设置android:name属性的值
            XmlAttribute nameAttribute = xmlDoc.CreateAttribute("android", "name", "http://schemas.android.com/apk/res/android");
            nameAttribute.Value = newApplicationName;
            applicationNode.Attributes.SetNamedItem(nameAttribute);

            // 保存修改后的XML文件
            xmlDoc.Save(manifestPath);
        }
        catch (Exception e)
        {
            LogWarning("ModifyApplicationName AndroidManifest.xml error: " + e.Message);
        }
    }

    /// <summary>
    /// 修改首页 android:name
    /// </summary>
    /// <param name="manifestPath">文件路径</param>
    /// <param name="newActivityName">首页包名路径</param>
    public static void ModifyMainActivity(string manifestPath, string newActivityName)
    {
        try
        {
            // 加载AndroidManifest.xml文件
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(manifestPath);

            // 创建命名空间管理器并添加 "android" 命名空间
            XmlNamespaceManager nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
            nsManager.AddNamespace("android", "http://schemas.android.com/apk/res/android");

            // 获取启动的Activity节点
            XmlNode activityNode = xmlDoc.SelectSingleNode("/manifest/application/activity[intent-filter/action/@android:name='android.intent.action.MAIN']/@android:name", nsManager);

            // 设置android:name属性的值
            activityNode.Value = newActivityName;

            // 保存修改后的XML文件
            xmlDoc.Save(manifestPath);
        }
        catch (Exception e)
        {
            LogWarning("ModifyMainActivity in  AndroidManifest.xml error: " + e.Message);
        }
    }


    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourceFile">源文件路径</param>
    /// <param name="destinationFile">目标文件路径</param>
    private static void CopyFile(string sourceFile, string destinationFile)
    {

        string directoryPath = Path.GetDirectoryName(destinationFile);
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }

        try
        {
            File.Copy(sourceFile, destinationFile, true);
        }
        catch (Exception e)
        {
            LogWarning("copying MainActivity Error: " + e.Message);
        }
    }

    /// <summary>
    /// 读取gradle.properties文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns></returns>
    public static Dictionary<string, string> ReadGradleProperties(string filePath)
    {
        Dictionary<string, string> properties = new Dictionary<string, string>();

        try
        {
            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                LogWarning(filePath + "File does not exist.");
                return properties;
            }

            // 读取文件内容
            string[] lines = File.ReadAllLines(filePath);

            // 使用正则表达式匹配每一行中的属性
            Regex regex = new Regex(@"^\s*([^#=]+?)\s*=\s*(.*?)\s*$");

            // 遍历每一行并匹配属性
            foreach (string line in lines)
            {
                Match match = regex.Match(line);
                if (match.Success)
                {
                    string key = match.Groups[1].Value.Trim();
                    string value = match.Groups[2].Value.Trim();

                    // 存储属性和值到字典中
                    properties[key] = value;
                }
            }
        }
        catch (Exception e)
        {
            LogWarning("读取 reading gradle.properties error: " + e.Message);
        }

        return properties;
    }

    /// <summary>
    /// 设置签名信息
    /// </summary>
    /// <param name="lines"></param>
    /// <param name="signFilePath">签名说明文件路径</param>
    private static void SetSignInfo(string[] lines, string signFilePath)
    {
        try
        {
            // 读取文件内容
            string fileContent = File.ReadAllText(signFilePath);
            // 使用正则表达式提取签名文件名、别名和密码
            string pattern = @"SignFile:(?<keystoreName>[^\n\r]+)\s+别名:(?<keyaliasName>[^\n\r]+)\s+密码:(?<keystorePass>[^\n\r]+)";
            Match match = Regex.Match(fileContent, pattern);

            if (match.Success)
            {
                // 提取匹配到的信息
                string keystoreName = match.Groups["keystoreName"].Value.Trim();
                string keyaliasName = match.Groups["keyaliasName"].Value.Trim();
                string keystorePass = match.Groups["keystorePass"].Value.Trim();
                // 替换build.gradle中的签名配置
                for (int i = 1; i < lines.Length; i++)
                {
                    if (lines[i].Contains("release {") && lines[i-1].Contains("signingConfigs {"))
                    {
                        lines[i + 1] = $"            keyAlias '{keyaliasName}'";
                        lines[i + 2] = $"            keyPassword '{keystorePass}'";
                        lines[i + 3] = $"            storeFile file('{keystoreName}')";
                        lines[i + 4] = $"            storePassword '{keystorePass}'";
                        break;
                    }
                }
            }
            else
            {
                LogWarning("No signature info found in the file："+ signFilePath);
            }
        }
        catch (Exception e)
        {
            LogError("Error reading signature info: " + signFilePath +",error:"+ e.Message);
        }
    }



    public static void Log(string message)
    {
        LogHelper.Log(message, "AndroidProjectModify");
    }



    public static void LogWarning(string message)
    {
        LogHelper.LogWarning(message, "AndroidProjectModify");
    }



    public static void LogError(string message)
    {
        LogHelper.LogError(message, "AndroidProjectModify");
    }

}
