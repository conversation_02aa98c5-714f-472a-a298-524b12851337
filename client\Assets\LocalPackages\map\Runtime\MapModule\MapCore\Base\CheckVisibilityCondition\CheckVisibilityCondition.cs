﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */
using UnityEngine;

namespace TFW.Map {
    public interface CheckObjectVisibility {
    }

    public delegate bool ConditionCheck(int objMinX, int objMinY, int objMaxX, int objMaxY, int x, int y, int maxX, int maxY);
    //基于格子的地图对象的可见性判断
    public class CheckGridObjectVisibility : CheckObjectVisibility {
        public CheckGridObjectVisibility(ConditionCheck checkFunc) {
            mCheckFunc = checkFunc;
        }
        public bool Check(int objMinX, int objMinY, int objMaxX, int objMaxY, int x, int y, int maxX, int maxY) {
            return mCheckFunc(objMinX, objMinY, objMaxX, objMaxY, x, y, maxX, maxY);
        }
        ConditionCheck mCheckFunc;
    }

    public delegate bool QuadTreeObjectConditionCheck(Rect modelWorldBounds, Rect viewBounds);
    //基于四叉树的地图对象的可见性判断
    public class CheckQuadTreeObjectVisibility : CheckObjectVisibility {
        public CheckQuadTreeObjectVisibility(QuadTreeObjectConditionCheck checkFunc) {
            mCheckFunc = checkFunc;
        }
        public bool Check(Rect modelWorldBounds, Rect viewBounds) {
            return mCheckFunc(modelWorldBounds, viewBounds);
        }

        QuadTreeObjectConditionCheck mCheckFunc;
    }

    public delegate bool QuadTreeRectObjectConditionCheck(Rect modelWorldBounds, Rect viewBounds);
    //基于四叉树的地图对象的可见性判断
    public class CheckQuadTreeRectObjectVisibility : CheckObjectVisibility
    {
        public CheckQuadTreeRectObjectVisibility(QuadTreeRectObjectConditionCheck checkFunc)
        {
            mCheckFunc = checkFunc;
        }
        public bool Check(Rect modelWorldBounds, Rect viewBounds)
        {
            return mCheckFunc(modelWorldBounds, viewBounds);
        }

        QuadTreeRectObjectConditionCheck mCheckFunc;
    }
}
