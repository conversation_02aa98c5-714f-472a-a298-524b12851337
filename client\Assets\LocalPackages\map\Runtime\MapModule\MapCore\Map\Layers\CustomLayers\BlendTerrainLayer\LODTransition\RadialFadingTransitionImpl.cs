﻿ 



 
 

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class RadialFadingTransitionImpl : MonoBehaviour
    {
        void Awake()
        {
            mCamera = GetComponent<Camera>();
            Debug.Assert(mCamera != null);
        }

        public void BlitTwoScreenshot()
        {
            this.enabled = true;
            m_RenderTexture = RenderTexture.GetTemporary(Screen.width, Screen.height, 24, RenderTextureFormat.ARGB32);
            mCamera.targetTexture = m_RenderTexture;
            mCamera.Render();
            m_RenderTexture.wrapMode = TextureWrapMode.Clamp;
            mCamera.targetTexture = null;

            m_fStep = m_fCurrnetStep;
            m_fCeiling = m_fCurrentCeiling;
            m_Curve = m_CurrentCurve;
            m_Material.SetTexture("_Tex", m_RenderTexture);
            m_bStart = true;
            m_fStrength = 0f;
        }

        void OnRenderImage(RenderTexture src, RenderTexture dest)
        {
            src.wrapMode = TextureWrapMode.Clamp;
            if (m_bStart)
            {
                Graphics.Blit(src, dest, m_Material);
            }
            else
            {
                Graphics.Blit(src, dest);
            }
            if (!m_bStart)
                return;

            if (m_fStrength <= -0.000001f)
            {
                m_bStart = false;
                this.enabled = false;
                if (OnFinish != null)
                {
                    OnFinish();
                }
                return;
            }

            m_fStrength += m_fStep * Time.deltaTime;
            float realStrength = m_Curve.Evaluate(m_fStrength);
            m_Material.SetFloat("_Strength", realStrength);

            if (m_fStrength >= m_fCeiling)
            {
                if (OnChangeTexture != null)
                {
                    OnChangeTexture();
                }
                m_fStep = -m_fNextStep;
                m_Material.SetTexture("_Tex", src);
                mCamera.targetTexture = null;
                m_fCeiling = m_fNextCeiling;
                m_Curve = m_NextCurve;
                
                RenderTexture.ReleaseTemporary(m_RenderTexture);
            }
        }

        public event Action OnChangeTexture;
        public event Action OnFinish;

        [Space]
        public AnimationCurve m_NextCurve;
        [Range(0, 10)] public float m_fCurrnetStep = 1f;
        public float m_fCurrentCeiling = 0.15f;
        [Space]
        public AnimationCurve m_CurrentCurve;
        [Range(0, 10)] public float m_fNextStep = 1f;
        public float m_fNextCeiling = 0.4f;

        private AnimationCurve m_Curve;
        private float m_fStep;
        private float m_fCeiling;
        public RenderTexture m_RenderTexture = null;
        public Material m_Material = null;
        private float m_fStrength = 0f;
        private bool m_bStart = false;
        Camera mCamera;
    }
}