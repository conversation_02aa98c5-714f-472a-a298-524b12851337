﻿ 



 
 

using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //测试资源建筑的LOD表现
    public class ResourceBuildingLODTest : MonoBehaviour
    {
        //创建一个测试实例
        public static void Test(Vector3 pos, bool newOne)
        {
            ResourceBuildingLODTest cityLODTest;

            if (mTestObject == null)
            {
                mTestObject = new GameObject("Resource Building Test");
                cityLODTest = mTestObject.AddComponent<ResourceBuildingLODTest>();
            }
            else
            {
                cityLODTest = mTestObject.GetComponent<ResourceBuildingLODTest>();
            }

            if (newOne)
            {
                cityLODTest.CreateNew(pos);
            }
            else
            {
                cityLODTest.CreateOne(pos);
            }
        }

        void CreateNew(Vector3 cityPos)
        {
            //创建一个资源建筑
            var prefab = MapModuleResourceMgr.LoadGameObject($"{MapModule.runtimeMapResDirectory}Building/AllianceStation/AllianceStation.prefab");
            if (prefab != null)
            {
                prefab.transform.position = cityPos;
                //需要给资源建筑根节点挂载一个ResourceBuildingLODManager脚本
                ResourceBuildingLODManagerContainer.InitGameObjectLOD(prefab);
            }
            else
            {
#if UNITY_EDITOR
                EditorUtility.DisplayDialog("Error", "Set a prefab first!", "OK");
#endif
            }
        }

        void CreateOne(Vector3 cityPos)
        {
            if (mCreate)
            {
                if (testPrefab == null)
                {
                    //创建一个资源建筑
                    testPrefab = MapModuleResourceMgr.LoadGameObject($"{MapModule.runtimeMapResDirectory}Building/AllianceStation/AllianceStation.prefab");
                    if (testPrefab != null)
                    {
                        testPrefab.transform.position = cityPos;
                        ResourceBuildingLODManagerContainer.InitGameObjectLOD(testPrefab);
                    }
                    else
                    {
#if UNITY_EDITOR
                        EditorUtility.DisplayDialog("Error", "Set a prefab first!", "OK");
#endif
                    }
                }
                else
                {
                    testPrefab.transform.position = cityPos;
                    testPrefab.SetActive(true);
                    ResourceBuildingLODManagerContainer.InitGameObjectLOD(testPrefab);
                }
                mCreate = false;
            }
            else
            {
                testPrefab.SetActive(false);
                ResourceBuildingLODManagerContainer.UninitGameObjectLOD(testPrefab);
                mCreate = true;
            }
        }

        GameObject testPrefab;
        bool mCreate = true;
        static GameObject mTestObject;
    }
}
