﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class DynamicObjectRegion
    {
        public void AddObject(IDynamicObjectData obj)
        {
#if DEBUG
            Debug.Assert(mObjects.Contains(obj) == false);
#endif
            mObjects.Add(obj);
        }

        public void RemoveObject(IDynamicObjectData obj)
        {
            mObjects.Remove(obj);
        }

        public void GetOverlappedObjects(Vector3 center, float radius, List<IDynamicObjectData> overlappedObjects)
        {
            int n = mObjects.Count;
            for (int i = 0; i < n; ++i)
            {
                var obj = mObjects[i];
                var d = obj.GetPosition() - center;
                var bounds = obj.GetBounds();
                //temp code
                float r2 = radius + bounds.size.x * 0.5f;
                r2 *= r2;
                if (d.sqrMagnitude <= r2)
                {
                    overlappedObjects.Add(obj);
                }
            }
        }

        public List<IDynamicObjectData> objects { get { return mObjects; } }

        List<IDynamicObjectData> mObjects = new List<IDynamicObjectData>();
    }
}