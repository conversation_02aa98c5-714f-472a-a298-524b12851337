﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;
using System;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //地图上对象使用的对象池 
    public class GameObjectPool
    {
        private string path;

#if UNITY_WEBGL
        private const int capacity = 0; //暂时不缓存后续考虑缓存
#else
        private const int capacity = 500; 
#endif
         
        public GameObjectPool(Func<string, GameObject> createModel)
        {
            mCreateFunc = createModel;
        }

        public GameObjectPool(Action<string, Action<GameObject>> createModel, Action<string> repleaseModel,Func<string,bool> ExitFunc)
        {
            mCreatAction = createModel;
            mReleaseAction = repleaseModel;
            mExitFunc = ExitFunc;

            
        }


        public void OnDestroy()
        {
            Clear();
        }

        public bool HasCache(string prefabPath)
        {
            List<GameObject> objectList = null;
            bool found = mObjects.TryGetValue(prefabPath, out objectList);
            if (found)
            {
                int n = objectList.Count;
                if (n > 0)
                {
                    return true;
                }
            }
            return false;
        }

        //path是prefab的路径
        public GameObject Require(string path, Vector3 pos, Vector3 scale, Quaternion rot)
        {
            GameObject model = Require(path);

            if (model != null)
            {
                model.transform.localPosition = pos;
                model.transform.localScale = scale;
                model.transform.rotation = rot;
                model.SetActive(true);
            }

            return model;
        }

        public GameObject Require(string path)
        {
            //Debug.Log($"[WorldRequirePrefabs] {path}");

            GameObject model = null;

            List<GameObject> objectList = null;
            bool found = mObjects.TryGetValue(path, out objectList);
            if (found)
            {
                int n = objectList.Count;
                if (n > 0)
                {
                    model = objectList[n - 1];
                    objectList.RemoveAt(n - 1);
                }
            }
            else if(capacity > 0)
            {
                objectList = new List<GameObject>(capacity);
                mObjects[path] = objectList;
            }
            if (System.Object.ReferenceEquals(model, null))
            {
                model = mCreateFunc?.Invoke(path);
                if (model == null)
                {
                    model = new GameObject(path);
                    mCreatAction?.Invoke(path, (obj) =>
                    {
                        if (model != null)
                        {
                            obj.transform.SetParent(model.transform, false);
                            obj.transform.localPosition = Vector3.zero;
                            obj.transform.localEulerAngles = Vector3.zero;
                            obj.transform.localScale = Vector3.one;
                        }
                        else
                        {
                            Utils.DestroyObject(obj);
                            mReleaseAction?.Invoke(path);
                        }
                    });
                }
            }
            else
            {
                model.SetActive(true);
            }
            return model;
        }


        public void Require(string path, Action<GameObject> callBack)
        {
            //Debug.Log($"[WorldRequirePrefabs] {path}");

            GameObject model = null;

            List<GameObject> objectList = null;
            bool found = mObjects.TryGetValue(path, out objectList);
            if (found)
            {
                int n = objectList.Count;
                if (n > 0)
                {
                    model = objectList[n - 1];
                    objectList.RemoveAt(n - 1);
                }
            }
            else if(capacity>0)
            {
                objectList = new List<GameObject>(capacity);
                mObjects[path] = objectList;
            }

            if (System.Object.ReferenceEquals(model, null))
            {
                mCreatAction?.Invoke(path, (obj) =>
                {
                    model = obj; 
                    callBack?.Invoke(model);
                }); 
            }
            else
            {
                model.SetActive(true);
                callBack?.Invoke(model);
            }
        }


        public void Release(string path, GameObject model, Map map)
        {
#if UNITY_EDITOR
            Debug.Assert(!string.IsNullOrEmpty(path));
#endif

            if (model != null)
            {
                List<GameObject> objectList = null;
                bool found = mObjects.TryGetValue(path, out objectList);
                if (!found && capacity > 0)
                {
                    objectList = new List<GameObject>(capacity);
                    mObjects[path] = objectList;
                }

                if (objectList?.Count < capacity)
                {


                    objectList.Add(model);
                    model.SetActive(false);

#if UNITY_EDITOR
                var parentObj = map.view.poolObjectRoot;
                if (parentObj != null)
                {
                    model.transform.SetParent(parentObj.transform, false);
                }
                else
                {
                    model.transform.SetParent(null, false);
                }
#else
                    model.transform.SetParent(null, false);
#endif
                }
                else
                {
                    Utils.DestroyObject(model);
                    mReleaseAction?.Invoke(path);
                }
            }
        }

        public void Release(string path)
        {
            mReleaseAction?.Invoke(path);
        }

        public bool Exit(string path)
        {
            return mExitFunc?.Invoke(path)??false;
        }

        public void Clear(string prefabPath)
        {
            List<GameObject> cachedObjects;
            mObjects.TryGetValue(prefabPath, out cachedObjects);
            if (cachedObjects != null)
            {
                foreach (var model in cachedObjects)
                {
                    Utils.DestroyObject(model);
                }
                mObjects.Remove(prefabPath);
            }

            mReleaseAction?.Invoke(prefabPath);
        }

        public void Clear()
        {
            foreach (var list in mObjects)
            {
                foreach (var model in list.Value)
                {
                    Utils.DestroyObject(model);
                }

                mReleaseAction?.Invoke(list.Key);
            }
            mObjects.Clear();
        }


        Func<string, GameObject> mCreateFunc;
        Func<string, bool> mExitFunc;
        Action<string, Action<GameObject>> mCreatAction;
        System.Action<string> mReleaseAction;
        Dictionary<string, List<GameObject>> mObjects = new Dictionary<string, List<GameObject>>();
    }
}
