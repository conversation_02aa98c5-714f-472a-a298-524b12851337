﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //创建一个镂空的平面,作为border
    public static class PlaneBorderMaker
    {
        public static string Create(float totalWidth, float totalHeight, float mapWidth, float mapHeight, Material borderMaterial, string borderName, string outputFolder)
        {
            if (borderMaterial == null)
            {
                EditorUtility.DisplayDialog("Error", "Invalid material", "OK");
                return null;
            }

            var obj = CreateGameObject(totalWidth, totalHeight, mapWidth, mapHeight, borderMaterial, borderName);
            
            return CreatePrefab(obj, outputFolder);
        }

        public static GameObject CreateGameObject(float totalWidth, float totalHeight, float mapWidth, float mapHeight, Material borderMaterial, string borderName)
        {
            if (borderMaterial == null)
            {
                return null;
            }

            if (totalWidth < mapWidth || totalHeight < mapHeight)
            {
                EditorUtility.DisplayDialog("Error", "Invalid size", "OK");
                return null;
            }

            float borderX = (mapWidth - totalWidth) * 0.5f;
            float borderZ = (mapHeight - totalHeight) * 0.5f;
            List<Vector3> bounds = new List<Vector3>()
            {
                new Vector3(borderX, 0, borderZ),
                new Vector3(borderX, 0, borderZ + totalHeight),
                new Vector3(borderX + totalWidth, 0, borderZ + totalHeight),
                new Vector3(borderX + totalWidth, 0, borderZ),
            };

            List<Vector3> mapRegion = new List<Vector3>()
            {
                new Vector3(0, 0, 0),
                new Vector3(0, 0, mapHeight),
                new Vector3(mapWidth, 0, mapHeight),
                new Vector3(mapWidth, 0, 0),
            };

            Vector3[] vertices;
            int[] indices;
            Triangulator.TriangulatePolygons(new List<List<Vector3>>() { bounds }, new List<List<Vector3>>() { mapRegion }, false, 0, 0, null, out vertices, out indices);

            var mesh = new Mesh();
            mesh.name = "border mesh";
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.uv = CreateUV(borderX, borderZ, totalWidth, totalHeight, vertices);
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            var obj = new GameObject(borderName);
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            filter.sharedMesh = mesh;
            renderer.sharedMaterial = borderMaterial;

            return obj;
        }

        static Vector2[] CreateUV(float borderX, float borderZ, float width, float height, Vector3[] vertices)
        {
            Vector2[] uvs = new Vector2[vertices.Length];
            for (int i = 0; i < vertices.Length; ++i)
            {
                float u = (vertices[i].x - borderX) / width;
                float v = (vertices[i].z - borderZ) / height;
                uvs[i] = new Vector2(u, v);
            }
            return uvs;
        }

        static string CreatePrefab(GameObject obj, string outputFolder)
        {
            string prefix = outputFolder + "/" + obj.name;

            //create mesh
            string meshPath = prefix + ".asset";
            AssetDatabase.CreateAsset(obj.GetComponent<MeshFilter>().sharedMesh, meshPath);

            //create prefab
            string newPrefabPath = prefix + ".prefab";
            PrefabUtility.SaveAsPrefabAsset(obj, newPrefabPath);

            GameObject.DestroyImmediate(obj);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            return newPrefabPath;
        }
    }
}

#endif