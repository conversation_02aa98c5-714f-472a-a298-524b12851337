{"skeleton": {"hash": "bbzYISPdDMw", "spine": "4.2.33", "x": -335.57, "y": -9.84, "width": 573.09, "height": 1859.12, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 199.34, "y": 1238.79}, {"name": "ALL2", "parent": "ALL", "x": -251.17, "y": -27.43, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 126.04, "rotation": 97, "x": -3.39, "y": 11.37}, {"name": "body2", "parent": "body", "length": 247.91, "rotation": 61.93, "x": 126.04, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 74.59, "rotation": 96.39, "x": 247.91, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 172.46, "rotation": 6.59, "x": 74.59}, {"name": "tun", "parent": "ALL2", "length": 187.05, "rotation": -72.1, "x": 3.17, "y": -7.06}, {"name": "leg_R", "parent": "tun", "length": 439.14, "rotation": -13.65, "x": 151.37, "y": -59.29}, {"name": "leg_R2", "parent": "leg_R", "length": 427.3, "rotation": -0.65, "x": 439.14, "inherit": "noScale"}, {"name": "leg_R3", "parent": "leg_R2", "length": 182.76, "rotation": -103.3, "x": 427.3, "inherit": "onlyTranslation"}, {"name": "leg_L0", "parent": "tun", "rotation": -72.1, "x": 118.28, "y": 57.21, "inherit": "noRotationOrReflection"}, {"name": "leg_L", "parent": "leg_L0", "length": 476.38, "rotation": -62.49, "x": 18.4, "y": -8.32}, {"name": "leg_L2", "parent": "leg_L", "length": 419.13, "rotation": 96.79, "x": 476.38, "inherit": "noScale"}, {"name": "leg_L3", "parent": "leg_L2", "length": 151.45, "rotation": -56.31, "x": 419.13, "inherit": "onlyTranslation"}, {"name": "leg_L4", "parent": "leg_L3", "length": 97.68, "rotation": -67.91, "x": 151.45}, {"name": "sh_L", "parent": "body2", "x": 262.99, "y": -87.18, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 181.09, "y": 81.62, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 256.8, "rotation": 175.35, "x": -19.05, "y": -10.12}, {"name": "arm_L2", "parent": "arm_L", "length": 184.77, "rotation": -18.18, "x": 256.8, "inherit": "noScale"}, {"name": "arm_L3", "parent": "arm_L2", "length": 59.81, "rotation": -174.98, "x": 184.77, "inherit": "onlyTranslation"}, {"name": "arm_R", "parent": "sh_R", "length": 252.8, "rotation": -150.18, "x": -19.11, "y": -11.43, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 238.67, "rotation": 207.84, "x": 252.8, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 68, "rotation": 25.88, "x": 238.67, "inherit": "onlyTranslation"}, {"name": "arm_R4", "parent": "arm_R3", "length": 37, "rotation": -81.9, "x": 68}, {"name": "arm_R5", "parent": "arm_R4", "length": 40, "rotation": -18.44, "x": 37}, {"name": "RU_L", "parent": "body2", "length": 34, "rotation": 7.13, "x": 92.99, "y": 36.5}, {"name": "RU_L2", "parent": "RU_L", "length": 34, "rotation": 9.92, "x": -14.87, "y": 23.73}, {"name": "RU_L3", "parent": "RU_L2", "length": 34, "x": -11.37, "y": 15.5}, {"name": "RU_R", "parent": "body2", "length": 34, "rotation": 7.13, "x": 75.17, "y": 113.49}, {"name": "RU_R2", "parent": "RU_R", "length": 34, "rotation": 13.36, "x": -28.26, "y": 37.05}, {"name": "RU_R3", "parent": "RU_R2", "length": 34, "rotation": 4.08, "x": -10.82, "y": 21.95}, {"name": "eye_L", "parent": "head", "x": 68.76, "y": 13.22}, {"name": "eye_R", "parent": "head", "x": 67.65, "y": 67.98}, {"name": "eyebrow_L", "parent": "head", "length": 14.99, "rotation": 73.72, "x": 91.99, "y": -6.46}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 24.14, "rotation": 31.39, "x": 14.99}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 16.26, "rotation": 8.27, "x": 24.14}, {"name": "eyebrow_R", "parent": "head", "length": 11.83, "rotation": -121.42, "x": 91.83, "y": 82.78}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 10.56, "rotation": -1.48, "x": 11.83}, {"name": "hat", "parent": "head", "x": 149.48, "y": 35.91}, {"name": "hair_L", "parent": "hat", "length": 41.68, "rotation": -163.72, "x": -16.36, "y": -46.68}, {"name": "hair_L2", "parent": "hair_L", "length": 41.93, "rotation": -10.96, "x": 41.68, "color": "abe323ff"}, {"name": "hair_L3", "parent": "hair_L2", "length": 43.29, "rotation": -8.29, "x": 41.93, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 35.76, "rotation": -8, "x": 43.29, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 30.06, "rotation": -13.44, "x": 35.76, "color": "abe323ff"}, {"name": "hair_L6", "parent": "hair_L5", "length": 28.07, "rotation": -8.83, "x": 30.06, "color": "abe323ff"}, {"name": "hair_R", "parent": "hat", "length": 33.06, "rotation": 145.32, "x": -14.83, "y": 47.33}, {"name": "hair_R2", "parent": "hair_R", "length": 33.57, "rotation": 23.84, "x": 33.06, "color": "abe323ff"}, {"name": "hair_R3", "parent": "hair_R2", "length": 29.67, "rotation": 17.65, "x": 33.57, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 27.59, "rotation": 15.5, "x": 29.67, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 25.4, "rotation": -0.76, "x": 27.59, "color": "abe323ff"}, {"name": "hair_B", "parent": "sh_L", "length": 50.17, "rotation": -93.5, "x": -12.45, "y": -40.47, "inherit": "noRotationOrReflection"}, {"name": "hair_B2", "parent": "hair_B", "length": 45.25, "rotation": -14.93, "x": 50.17, "color": "abe323ff"}, {"name": "hair_B3", "parent": "hair_B2", "length": 45.98, "rotation": 3.63, "x": 45.25, "color": "abe323ff"}, {"name": "hair_B4", "parent": "hair_B3", "length": 35.86, "rotation": 10.72, "x": 45.98, "color": "abe323ff"}, {"name": "hair_B5", "parent": "hair_B4", "length": 27.32, "rotation": 11.61, "x": 35.86, "color": "abe323ff"}, {"name": "hair_B6", "parent": "hair_B5", "length": 31.83, "rotation": 11.2, "x": 27.32, "color": "abe323ff"}, {"name": "tunround", "parent": "ALL2", "x": 294.28, "y": -41.8, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 294.28, "y": -101.4, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "rotation": -61.93, "x": 298.15, "y": -260.24, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "rotation": -61.93, "x": 244.83, "y": -288.68, "icon": "warning"}, {"name": "headround3", "parent": "head", "x": 391.48, "y": -2.22, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -67.43, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 334.31, "y": -69.65, "icon": "warning"}, {"name": "leg_L1", "parent": "leg_L0", "length": 537.29, "rotation": -66.55, "x": 17.05, "y": -9.84}, {"name": "leg_L5", "parent": "leg_L1", "length": 470.15, "rotation": 107.34, "x": 537.29, "color": "abe323ff"}, {"name": "leg_L6", "parent": "leg_L0", "rotation": 72.1, "x": 584.68, "y": -194.59, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L7", "parent": "root", "x": -294.56, "y": 750, "color": "ff3f00ff", "icon": "ik"}, {"name": "tun2", "parent": "tun", "length": 867.24, "rotation": -13.95, "x": 151.28, "y": -59.16}, {"name": "leg_R4", "parent": "root", "x": 0.7, "y": 177.66, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "tun2", "rotation": 86.08, "x": 439.26, "y": 2.42, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_L2", "parent": "sh_L", "length": 261.37, "rotation": -179.13, "x": -18.89, "y": -9.55}, {"name": "sh_L3", "parent": "sh_L2", "length": 191.99, "rotation": -30.89, "x": 261.37, "color": "abe323ff"}, {"name": "arm_L4", "parent": "ALL2", "x": -100.31, "y": -26.67, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "root", "x": -8.73, "y": 1301.23, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "sh_R", "length": 288.92, "rotation": 149.86, "x": -19.11, "y": -11.04}, {"name": "sh_R3", "parent": "sh_R2", "length": 275.18, "rotation": -155.78, "x": 288.92, "color": "abe323ff"}, {"name": "arm_R6", "parent": "sh_R", "rotation": -61.93, "x": 4.77, "y": 105.22, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "sh_R", "rotation": -61.93, "x": -233.24, "y": 122.94, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "arm_R", "bone": "root", "attachment": "arm_R"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "hat", "bone": "root", "attachment": "hat"}, {"name": "arm_Rb2", "bone": "root", "attachment": "arm_Rb"}], "ik": [{"name": "arm_L", "order": 1, "bones": ["sh_L2", "sh_L3"], "target": "arm_L4", "bendPositive": false}, {"name": "arm_L1", "order": 3, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 4, "bones": ["arm_L2"], "target": "arm_L4", "compress": true, "stretch": true}, {"name": "arm_R", "order": 6, "bones": ["sh_R2", "sh_R3"], "target": "arm_R6", "bendPositive": false}, {"name": "arm_R1", "order": 8, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 9, "bones": ["arm_R2"], "target": "arm_R6", "compress": true, "stretch": true}, {"name": "leg_L", "order": 11, "bones": ["leg_L1", "leg_L5"], "target": "leg_L6"}, {"name": "leg_L1", "order": 13, "bones": ["leg_L"], "target": "leg_L7", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 14, "bones": ["leg_L2"], "target": "leg_L6", "compress": true, "stretch": true}, {"name": "leg_R", "order": 15, "bones": ["tun2"], "target": "leg_R4", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 16, "bones": ["leg_R"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 17, "bones": ["leg_R2"], "target": "leg_R4", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 2, "bones": ["arm_L1"], "target": "sh_L3", "rotation": 148.74, "x": 8.92, "y": -25.2, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 7, "bones": ["arm_R1"], "target": "sh_R3", "rotation": -55.88, "x": 36.61, "y": -7.43, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 18, "bones": ["bodyround2"], "target": "bodyround", "y": -60.43, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "bones": ["sh_L"], "target": "bodyround", "rotation": 61.93, "x": -169.26, "y": 50.42, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 5, "bones": ["sh_R"], "target": "bodyround", "rotation": 61.93, "x": -356.73, "y": 57.59, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 19, "bones": ["headround2"], "target": "headround", "x": -57.17, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 20, "bones": ["eyebrow_L"], "target": "headround", "rotation": 73.72, "x": -299.49, "y": 63.2, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 21, "bones": ["eyebrow_R"], "target": "headround", "rotation": -121.42, "x": -299.65, "y": 152.44, "mixRotate": 0, "mixX": 0.025, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 22, "bones": ["hat"], "target": "headround", "x": -242, "y": 105.57, "mixRotate": 0, "mixX": 0.035, "mixY": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_L3", "order": 12, "bones": ["leg_L7"], "target": "leg_L5", "rotation": 31.08, "x": 53.89, "y": 48.98, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 23, "bones": ["tunround2"], "target": "tunround", "y": -59.59, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 10, "bones": ["leg_L0"], "target": "tunround", "rotation": -72.1, "x": -200.32, "y": -60.22, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_B2", "order": 24, "bone": "hair_B2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B3", "order": 25, "bone": "hair_B3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B4", "order": 26, "bone": "hair_B4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B5", "order": 27, "bone": "hair_B5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_B6", "order": 28, "bone": "hair_B6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L2", "order": 29, "bone": "hair_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 30, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 31, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 32, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L6", "order": 33, "bone": "hair_L6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R2", "order": 34, "bone": "hair_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 35, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 36, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 37, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.91062, 0, 0.95016, 0.00467, 0.97165, 0.02418, 0.9916, 0.05255, 0.99888, 0.08442, 0.99536, 0.13221, 0.98709, 0.18347, 0.94308, 0.26473, 0.90982, 0.30028, 0.87071, 0.32609, 0.83229, 0.37782, 0.8363, 0.39611, 0.77547, 0.49094, 0.73586, 0.50837, 0.68261, 0.51103, 0.65786, 0.54137, 0.63761, 0.56729, 0.61769, 0.59469, 0.59812, 0.62482, 0.55868, 0.66026, 0.52653, 0.69463, 0.47568, 0.7353, 0.27701, 0.85985, 0.2493, 0.87879, 0.22646, 0.89481, 0.20409, 0.90938, 0.18147, 0.92463, 0.15347, 0.93633, 0.12411, 0.94139, 0.09728, 0.94055, 0.07649, 0.97072, 0.05404, 0.98055, 0.03072, 1, 0.00546, 1, 0.0019, 0.98967, 0.00544, 0.89527, 0.04855, 0.83878, 0.08612, 0.80293, 0.1541, 0.82219, 0.16847, 0.82422, 0.18314, 0.81165, 0.19934, 0.79715, 0.38018, 0.58586, 0.42046, 0.54549, 0.45437, 0.51514, 0.48287, 0.4947, 0.50518, 0.47291, 0.5203, 0.44742, 0.53855, 0.41645, 0.48919, 0.35365, 0.55959, 0.26904, 0.60148, 0.25469, 0.64953, 0.18975, 0.70571, 0.13981, 0.74432, 0.09496, 0.78418, 0.05274, 0.83451, 0.02643, 0.88222, 0.00423, 0.58826, 0.33682, 0.74917, 0.42772, 0.79665, 0.31964, 0.67623, 0.25259, 0.74233, 0.27503, 0.67628, 0.36194, 0.58507, 0.44675, 0.56593, 0.47294, 0.53869, 0.51054, 0.49821, 0.54345, 0.45846, 0.5871, 0.51072, 0.63343, 0.55194, 0.60724, 0.58654, 0.57434, 0.60641, 0.54345, 0.62261, 0.51256, 0.64395, 0.47965, 0.6383, 0.34578, 0.71144, 0.39013, 0.72706, 0.20235, 0.77641, 0.15733, 0.82794, 0.10833, 0.87729, 0.05999, 0.94261, 0.04741, 0.94697, 0.10038, 0.92955, 0.156, 0.87657, 0.21758, 0.82794, 0.26592], "triangles": [31, 32, 34, 32, 33, 34, 34, 35, 31, 31, 35, 30, 30, 35, 29, 27, 28, 38, 29, 38, 28, 29, 35, 36, 38, 29, 36, 26, 38, 39, 26, 27, 38, 38, 36, 37, 26, 39, 25, 25, 39, 24, 24, 39, 23, 39, 40, 23, 40, 41, 23, 23, 41, 22, 21, 42, 68, 42, 21, 41, 21, 22, 41, 21, 69, 20, 21, 68, 69, 20, 69, 19, 69, 70, 19, 19, 70, 18, 69, 68, 70, 70, 71, 18, 68, 67, 70, 70, 67, 71, 42, 43, 68, 68, 43, 67, 67, 43, 44, 44, 45, 67, 18, 71, 17, 17, 71, 16, 67, 66, 71, 71, 72, 16, 71, 66, 72, 16, 72, 15, 67, 45, 66, 72, 73, 15, 72, 66, 73, 15, 73, 14, 66, 65, 73, 73, 65, 74, 73, 74, 14, 74, 65, 64, 13, 14, 59, 45, 46, 66, 66, 46, 65, 12, 13, 59, 59, 14, 74, 12, 59, 11, 74, 64, 63, 74, 76, 59, 74, 63, 76, 63, 64, 75, 46, 47, 65, 65, 47, 64, 47, 48, 64, 64, 58, 75, 64, 48, 58, 59, 10, 11, 59, 76, 10, 48, 49, 58, 76, 60, 10, 76, 63, 60, 10, 60, 9, 63, 62, 60, 62, 63, 61, 49, 50, 58, 58, 51, 75, 63, 75, 61, 75, 51, 61, 58, 50, 51, 60, 85, 9, 9, 85, 8, 60, 62, 85, 85, 84, 8, 8, 84, 7, 61, 77, 62, 62, 77, 85, 77, 78, 85, 85, 78, 84, 7, 84, 6, 6, 84, 83, 51, 52, 61, 61, 52, 77, 78, 79, 84, 84, 79, 83, 52, 53, 77, 77, 53, 78, 6, 83, 5, 53, 54, 78, 78, 54, 79, 83, 79, 82, 83, 82, 5, 82, 80, 81, 80, 82, 79, 5, 82, 4, 54, 55, 79, 79, 56, 80, 82, 81, 3, 82, 3, 4, 81, 80, 0, 80, 57, 0, 80, 56, 57, 81, 2, 3, 81, 1, 2, 81, 0, 1, 79, 55, 56], "vertices": [3, 16, 7.18, 8.66, 0.90416, 4, 270.17, -78.51, 0.08476, 59, -173.52, 60.83, 0.01108, 1, 16, 12.61, -5.7, 1, 1, 16, 9.24, -16.87, 1, 2, 16, 2.29, -29.28, 0.87714, 18, -22.82, 17.37, 0.12286, 2, 16, -8.26, -38.08, 0.71732, 18, -13.02, 27, 0.28268, 2, 16, -26.69, -46.38, 0.33777, 18, 4.68, 36.76, 0.66223, 2, 16, -47.28, -53.75, 0.12571, 18, 24.6, 45.77, 0.87429, 1, 18, 62.61, 50.05, 1, 1, 18, 82.15, 47.39, 1, 1, 18, 99.45, 40.6, 1, 1, 18, 125.82, 39.96, 1, 1, 18, 131.48, 45.43, 1, 1, 18, 177.8, 47.36, 1, 1, 18, 192.23, 38.5, 1, 1, 18, 204.26, 21.86, 1, 1, 18, 220.18, 20.76, 1, 2, 18, 233.6, 20.11, 0.99796, 19, -28.32, 11.87, 0.00204, 2, 18, 247.47, 19.91, 0.87724, 19, -15.08, 16.01, 0.12276, 2, 18, 262.24, 20.44, 0.37295, 19, -1.21, 21.12, 0.62705, 2, 18, 283.03, 15.75, 0.00856, 19, 20, 23.15, 0.99144, 1, 19, 38.75, 26.6, 1, 1, 19, 64.77, 27.57, 1, 1, 19, 157.26, 20.12, 1, 2, 19, 170.58, 19.6, 0.96884, 20, -22.73, 8.28, 0.03116, 2, 19, 181.67, 19.3, 0.75231, 20, -13.38, 14.25, 0.24769, 2, 19, 192.23, 18.64, 0.36627, 20, -4.27, 19.62, 0.63373, 2, 19, 203.05, 18.14, 0.09684, 20, 4.97, 25.27, 0.90316, 2, 19, 214.53, 15.17, 0.00691, 20, 16.14, 29.24, 0.99309, 1, 20, 27.59, 30.38, 1, 1, 20, 37.85, 29.12, 1, 1, 20, 46.94, 41.1, 1, 1, 20, 55.91, 44.48, 1, 1, 20, 65.57, 51.87, 1, 1, 20, 75.26, 51.02, 1, 1, 20, 76.25, 46.56, 1, 1, 20, 71.4, 6.99, 1, 1, 20, 52.78, -15.3, 1, 1, 20, 37.05, -29.11, 1, 2, 19, 183.96, -22.05, 0.20644, 20, 11.68, -18.72, 0.79356, 2, 19, 180.21, -17.9, 0.43368, 20, 6.25, -17.38, 0.56632, 2, 19, 172.48, -18.45, 0.76409, 20, 0.16, -22.17, 0.23591, 2, 19, 163.79, -19.27, 0.93279, 20, -6.59, -27.72, 0.06721, 2, 18, 293.76, -59.04, 0.01887, 19, 53.53, -44.56, 0.98113, 2, 18, 271.05, -55.2, 0.14574, 19, 30.75, -48, 0.85426, 2, 18, 253.21, -51.13, 0.38299, 19, 12.54, -49.7, 0.61701, 2, 18, 240.02, -46.56, 0.62985, 19, -1.41, -49.48, 0.37015, 2, 18, 227.65, -44.31, 0.83024, 19, -13.87, -51.2, 0.16976, 2, 18, 215.45, -45.23, 0.94145, 19, -25.17, -55.87, 0.05855, 2, 18, 200.66, -46.38, 0.99189, 19, -38.87, -61.58, 0.00811, 2, 18, 188.63, -76.69, 1, 19, -40.83, -94.13, 0, 1, 18, 143.94, -73.19, 1, 1, 18, 130.13, -62.89, 1, 2, 16, -110.78, 59.68, 0.00012, 18, 97.07, -62.14, 0.99988, 4, 16, -82, 50.51, 0.07003, 18, 67.65, -55.33, 0.77542, 4, 180.99, -36.67, 0.14955, 59, -252.41, 1.83, 0.005, 4, 16, -58.3, 46.3, 0.18144, 18, 43.69, -53.05, 0.49941, 4, 204.68, -40.87, 0.30415, 59, -237.55, 20.76, 0.015, 3, 16, -35.36, 41.14, 0.63478, 4, 227.62, -46.03, 0.3381, 59, -222.2, 38.58, 0.02712, 3, 16, -16.45, 29.27, 0.80887, 4, 246.54, -57.9, 0.1701, 59, -202.82, 49.68, 0.02103, 3, 16, 0.46, 17.48, 0.81539, 4, 263.45, -69.7, 0.1687, 59, -184.46, 59.05, 0.0159, 1, 18, 162.04, -48.44, 1, 1, 18, 160.83, 24.42, 1, 2, 18, 112.57, 15.14, 0.98559, 59, -217.4, -74.06, 0.01441, 2, 18, 113.83, -39.16, 0.98777, 59, -263.76, -45.76, 0.01223, 2, 18, 108.04, -12.63, 0.98147, 59, -238.31, -55.23, 0.01853, 2, 18, 152.64, -14.2, 0.98172, 59, -263.74, -91.91, 0.01828, 3, 18, 201.73, -24.4, 0.98337, 19, -44.7, -40.37, 0.0052, 59, -298.86, -127.69, 0.01144, 3, 18, 215.01, -24.63, 0.96113, 19, -32.01, -36.44, 0.03032, 59, -306.23, -138.75, 0.00856, 3, 18, 234.03, -24.87, 0.8387, 19, -13.87, -30.73, 0.15702, 59, -316.71, -154.62, 0.00428, 2, 18, 254.14, -30.48, 0.43629, 19, 6.98, -29.79, 0.56371, 2, 18, 277.91, -33.4, 0.0768, 19, 30.47, -25.14, 0.9232, 1, 19, 27.19, 2.72, 1, 2, 18, 265.61, 1.48, 0.00784, 19, 7.91, 4.15, 0.99216, 1, 18, 246.73, 5.18, 1, 2, 18, 231.62, 4.57, 0.99523, 59, -290.64, -168.5, 0.00477, 2, 18, 217.29, 2.77, 0.99174, 59, -284.41, -155.47, 0.00826, 2, 18, 201.16, 2.18, 0.98793, 59, -276.19, -141.58, 0.01207, 2, 18, 154.81, -30.19, 0.98544, 59, -278.37, -85.09, 0.01456, 2, 18, 155.33, 3.62, 0.9828, 59, -250.2, -103.8, 0.0172, 3, 16, -101.42, 30.84, 0.00108, 18, 85.41, -34.15, 0.98396, 59, -244.19, -24.56, 0.01496, 3, 16, -75.71, 23.01, 0.01447, 18, 59.16, -28.43, 0.97002, 59, -225.19, -5.56, 0.01551, 3, 16, -48.13, 15.24, 0.07694, 18, 31.04, -22.92, 0.9073, 59, -205.35, 15.12, 0.01576, 3, 16, -21.19, 8.07, 0.34675, 18, 3.61, -17.96, 0.63758, 59, -186.35, 35.52, 0.01567, 3, 16, -4.67, -11.62, 0.56632, 18, -14.45, 0.33, 0.42249, 59, -161.2, 40.82, 0.01119, 3, 16, -23.61, -23.62, 0.01346, 18, 3.45, 13.82, 0.97496, 59, -159.53, 18.47, 0.01158, 2, 18, 26.82, 20.87, 0.98683, 59, -166.23, -5, 0.01317, 2, 18, 59.71, 17.75, 0.98563, 59, -186.63, -30.99, 0.01437, 2, 18, 86.99, 13.03, 0.98496, 59, -205.35, -51.39, 0.01504], "hull": 58, "edges": [6, 8, 18, 20, 24, 26, 26, 28, 62, 64, 64, 66, 66, 68, 68, 70, 94, 96, 96, 98, 98, 100, 100, 102, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 56, 58, 58, 60, 76, 78, 70, 72, 72, 74, 74, 76, 78, 80, 80, 82, 42, 44, 44, 46, 82, 84, 84, 86, 92, 94, 90, 92, 86, 88, 88, 90, 36, 38, 38, 40, 40, 42, 34, 36, 32, 34, 20, 22, 22, 24, 28, 30, 30, 32, 102, 104, 16, 18, 14, 16, 12, 14, 8, 10, 10, 12, 2, 4, 4, 6, 2, 0, 0, 114, 110, 112, 112, 114, 108, 110, 104, 106, 106, 108, 116, 96, 28, 118, 120, 124, 124, 122, 128, 130, 130, 132, 132, 134, 134, 136, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 150, 126, 128, 150, 152, 126, 148, 152, 122, 154, 154, 156, 156, 158, 158, 160, 164, 166, 166, 168, 168, 170, 170, 120], "width": 385, "height": 422}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.71114, 0.08428, 0.7418, 0.02966, 0.7636, 0.0475, 0.81416, 0.00394, 0.85772, 0.01105, 0.88718, 0.04619, 0.94325, 0.15816, 0.96966, 0.28618, 0.99607, 0.4142, 0.9182, 0.48125, 0.82317, 0.55357, 0.77135, 0.61301, 0.71717, 0.66094, 0.64504, 0.75355, 0.64108, 0.80696, 0.52355, 0.91607, 0.46431, 0.8341, 0.3352, 0.92938, 0.23294, 0.96716, 0.15443, 0.98274, 0.07593, 0.99832, 0.03366, 0.96586, 0.00553, 0.90722, 0.00459, 0.84012, 0.07196, 0.73425, 0.13932, 0.62837, 0.16684, 0.60382, 0.26307, 0.55064, 0.36271, 0.48756, 0.36573, 0.40817, 0.4791, 0.29984, 0.49807, 0.30719, 0.54022, 0.24593, 0.58791, 0.19952, 0.62913, 0.13196, 0.67776, 0.06801, 0.40594, 0.45503, 0.51128, 0.81053, 0.45043, 0.53116, 0.56597, 0.41562, 0.49429, 0.69364, 0.62801, 0.61601, 0.68364, 0.54199, 0.80453, 0.42285, 0.88797, 0.35063, 0.74837, 0.50047, 0.62433, 0.35364, 0.67615, 0.25455, 0.72679, 0.22447, 0.84388, 0.12618, 0.77909, 0.15847, 0.28059, 0.62824, 0.29958, 0.76893, 0.17349, 0.71695, 0.09209, 0.82489, 0.18267, 0.81718], "triangles": [50, 2, 49, 44, 43, 50, 44, 6, 7, 44, 7, 8, 9, 44, 8, 43, 44, 9, 44, 50, 49, 44, 49, 6, 49, 5, 6, 10, 43, 9, 49, 4, 5, 19, 20, 54, 20, 21, 54, 19, 55, 18, 19, 54, 55, 18, 52, 17, 18, 55, 52, 21, 22, 54, 17, 52, 16, 16, 37, 15, 15, 37, 14, 22, 23, 54, 23, 24, 54, 16, 40, 37, 16, 52, 40, 54, 53, 55, 54, 24, 53, 55, 53, 52, 14, 37, 13, 13, 37, 40, 53, 51, 52, 52, 38, 40, 52, 51, 38, 40, 41, 13, 13, 41, 12, 24, 25, 53, 25, 26, 53, 51, 26, 27, 51, 53, 26, 41, 40, 39, 41, 42, 12, 12, 42, 11, 38, 28, 36, 38, 51, 28, 51, 27, 28, 40, 38, 39, 41, 39, 42, 42, 45, 11, 11, 45, 10, 45, 43, 10, 39, 46, 42, 42, 46, 45, 38, 31, 39, 31, 36, 30, 31, 38, 36, 46, 47, 45, 47, 48, 45, 45, 48, 43, 28, 29, 36, 36, 29, 30, 31, 32, 39, 39, 32, 46, 32, 33, 46, 46, 33, 47, 33, 34, 47, 47, 0, 48, 0, 34, 35, 0, 47, 34, 0, 50, 48, 48, 50, 43, 50, 0, 2, 2, 0, 1, 2, 3, 49, 49, 3, 4], "vertices": [2, 17, -22.25, 23.46, 0.31104, 21, 21.2, -27.89, 0.68896, 2, 17, -9.48, 19.95, 0.67101, 21, 8.52, -31.7, 0.32899, 2, 17, -9.2, 12.76, 0.82977, 21, 4.47, -25.75, 0.17023, 1, 17, 4.63, 3.12, 1, 2, 17, 9.61, -8.89, 0.7624, 4, 190.7, 72.73, 0.2376, 2, 17, 8.27, -19.52, 0.38094, 4, 189.36, 62.1, 0.61906, 2, 17, -1.28, -43.49, 0.16857, 4, 179.81, 38.13, 0.83143, 1, 4, 163.62, 20.61, 1, 1, 4, 147.43, 3.09, 1, 2, 21, 2.59, 63.31, 0.0411, 4, 126.14, 17.94, 0.9589, 2, 21, 33.41, 60.32, 0.59277, 4, 101.62, 36.85, 0.40723, 2, 21, 51.96, 61.74, 0.91617, 4, 85.15, 45.51, 0.08383, 1, 21, 70.12, 61.06, 1, 1, 21, 96.81, 64.55, 1, 1, 21, 102.5, 72.12, 1, 1, 21, 142.34, 71.42, 1, 1, 21, 150.43, 50.16, 1, 1, 21, 192.04, 45.64, 1, 1, 21, 221.69, 36.3, 1, 1, 21, 243.29, 27.09, 1, 1, 21, 264.88, 17.88, 1, 1, 21, 272.93, 6.68, 1, 1, 21, 275.04, -6.43, 1, 1, 21, 269.41, -16.82, 1, 1, 21, 242.79, -23.04, 1, 1, 21, 216.17, -29.25, 1, 1, 21, 206.93, -28.94, 1, 1, 21, 177.48, -22.85, 1, 1, 21, 146.28, -17.77, 1, 1, 21, 138.55, -29.44, 1, 1, 21, 99.86, -29.24, 1, 1, 21, 95.62, -25.32, 1, 1, 21, 79.39, -28.45, 1, 2, 17, -57.37, 46.21, 0.00269, 21, 63.04, -28.49, 0.99731, 2, 17, -41.11, 41, 0.0265, 21, 46.51, -32.72, 0.9735, 2, 17, -24.39, 33.55, 0.1714, 21, 28.38, -35.3, 0.8286, 1, 21, 132.29, -16.35, 1, 1, 21, 136.26, 53.49, 1, 2, 21, 127.49, 1.85, 0.98525, 59, -465.33, -29.65, 0.01475, 2, 21, 87.61, 1.27, 0.98514, 59, -431.02, -9.32, 0.01486, 2, 21, 130.41, 33.14, 0.98519, 59, -452.31, -58.25, 0.01481, 2, 21, 89.16, 41.03, 0.98475, 59, -412.59, -44.59, 0.01525, 2, 21, 68.35, 37.94, 0.98421, 59, -396.07, -31.56, 0.01579, 3, 21, 26.77, 37.6, 0.44421, 4, 119.32, 52.57, 0.54331, 59, -360.17, -10.59, 0.01248, 2, 4, 142.2, 36.68, 0.99451, 59, -335.38, 2.12, 0.00549, 3, 21, 48.04, 41.16, 0.81437, 4, 99.42, 60.85, 0.17129, 59, -376.85, -24.25, 0.01435, 2, 21, 67.15, 0.42, 0.98504, 59, -413.68, 1.59, 0.01496, 4, 17, -53.58, 18.52, 0.00296, 21, 45.12, -7.05, 0.86326, 4, 127.51, 100.15, 0.11987, 59, -398.29, 19.03, 0.01392, 4, 17, -41.83, 7.75, 0.00891, 21, 29.44, -4.17, 0.69844, 4, 139.26, 89.37, 0.27786, 59, -383.25, 24.33, 0.01479, 4, 17, -10.2, -14.8, 0.16031, 21, -9.33, -1.89, 0.10221, 4, 170.89, 66.83, 0.73004, 59, -348.48, 41.62, 0.00743, 4, 17, -24.27, -0.49, 0.11413, 21, 10.19, -6.52, 0.40171, 4, 156.82, 81.13, 0.47255, 59, -367.72, 35.94, 0.0116, 2, 21, 179.75, -8.41, 0.99751, 59, -515.78, -46.74, 0.00249, 2, 21, 187.17, 15.88, 0.99799, 59, -510.13, -71.5, 0.00201, 1, 21, 215.11, -10.68, 1, 1, 21, 245.54, -6.22, 1, 1, 21, 221.52, 5.98, 1], "hull": 36, "edges": [10, 12, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 56, 58, 54, 56, 32, 34, 34, 36, 50, 52, 52, 54, 58, 60, 2, 4, 4, 6, 2, 0, 0, 70, 6, 8, 8, 10, 26, 28, 24, 26, 22, 24, 20, 22, 16, 18, 18, 20, 68, 70, 64, 66, 66, 68, 60, 62, 62, 64, 72, 56, 74, 32, 76, 78, 80, 82, 82, 84, 86, 88, 12, 14, 14, 16, 88, 14, 84, 90, 90, 86, 78, 92, 92, 94, 94, 96, 96, 100, 100, 98, 76, 102, 80, 104, 36, 38, 38, 40, 46, 48, 48, 50], "width": 297, "height": 176}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.85094, 0.00714, 0.8748, 0.0311, 0.89952, 0.06075, 0.95235, 0.11246, 0.97547, 0.16662, 1, 0.21502, 1, 0.22591, 0.90009, 0.30624, 0.78473, 0.31003, 0.76466, 0.2834, 0.75983, 0.22913, 0.72519, 0.22795, 0.68446, 0.22014, 0.6396, 0.2003, 0.60622, 0.21356, 0.58348, 0.22681, 0.56378, 0.25595, 0.48412, 0.3974, 0.42547, 0.5314, 0.32752, 0.78022, 0.29016, 0.83249, 0.23058, 0.87988, 0.15419, 0.93751, 0.07779, 0.99513, 0.03508, 0.97678, 0.00769, 0.94002, 0.00411, 0.90027, 0.02928, 0.83965, 0.04263, 0.77589, 0.06067, 0.70772, 0.09662, 0.62514, 0.1527, 0.5352, 0.2602, 0.39974, 0.36552, 0.2827, 0.45931, 0.15677, 0.48469, 0.12199, 0.5149, 0.08947, 0.55416, 0.06847, 0.59348, 0.0588, 0.69219, 0.03452, 0.781, 0.01127, 0.81034, 0.0154, 0.82834, 0.00489, 0.09849, 0.89358, 0.7251, 0.14913, 0.75249, 0.19693, 0.70318, 0.08895, 0.68839, 0.06815, 0.71469, 0.12045, 0.73469, 0.03971, 0.75676, 0.05218, 0.78389, 0.06931, 0.84648, 0.0384, 0.81532, 0.02738, 0.80548, 0.10179, 0.67627, 0.14077, 0.65893, 0.09362, 0.62752, 0.131, 0.58611, 0.13686, 0.55684, 0.15078, 0.53328, 0.17497, 0.51257, 0.20721, 0.85683, 0.15504, 0.86525, 0.20493], "triangles": [23, 43, 22, 22, 43, 21, 23, 24, 43, 24, 25, 43, 25, 26, 43, 26, 27, 43, 21, 43, 28, 28, 43, 27, 21, 28, 29, 29, 30, 19, 30, 31, 19, 19, 31, 18, 18, 31, 32, 18, 32, 17, 32, 33, 17, 17, 61, 16, 17, 33, 61, 33, 34, 61, 16, 61, 15, 61, 60, 15, 14, 15, 59, 15, 60, 59, 61, 34, 60, 34, 35, 60, 60, 35, 59, 20, 29, 19, 29, 20, 21, 11, 45, 10, 45, 11, 44, 13, 55, 12, 11, 12, 44, 12, 55, 44, 59, 58, 14, 13, 58, 57, 13, 14, 58, 13, 57, 55, 35, 36, 59, 58, 59, 37, 55, 48, 44, 54, 44, 48, 57, 56, 55, 55, 46, 48, 55, 56, 46, 59, 36, 37, 58, 38, 57, 58, 37, 38, 57, 38, 56, 48, 50, 51, 48, 46, 50, 56, 47, 46, 47, 56, 39, 46, 49, 50, 46, 47, 49, 47, 39, 49, 39, 56, 38, 53, 50, 40, 50, 49, 40, 49, 39, 40, 45, 54, 62, 45, 44, 54, 62, 54, 2, 62, 2, 3, 2, 52, 1, 52, 2, 54, 54, 48, 51, 54, 51, 52, 51, 53, 52, 51, 50, 53, 53, 40, 41, 53, 42, 52, 52, 0, 1, 52, 42, 0, 53, 41, 42, 7, 8, 63, 8, 9, 63, 6, 63, 4, 6, 7, 63, 9, 10, 63, 10, 45, 63, 4, 5, 6, 45, 62, 63, 4, 62, 3, 4, 63, 62], "vertices": [2, 24, 2.04, 20.88, 0.98197, 23, 88.96, 0.92, 0.01803, 2, 25, -31.92, 13.31, 0.00725, 24, 10.93, 22.72, 0.99275, 2, 25, -22.55, 17.68, 0.09037, 24, 21.2, 23.9, 0.90963, 2, 25, -5.5, 27.86, 0.59249, 24, 40.6, 28.17, 0.40751, 3, 25, 10.01, 30.08, 0.91228, 24, 56.02, 25.36, 0.08772, 23, 101, -51.88, 0, 3, 25, 24.16, 33.07, 0.99795, 24, 70.38, 23.73, 0.00205, 23, 101.41, -66.33, 0, 3, 25, 26.94, 32.3, 0.99917, 24, 72.77, 22.12, 0.00083, 23, 100.15, -68.93, 0, 1, 25, 40.18, 0.42, 1, 3, 25, 32.75, -30.08, 0.93128, 24, 58.55, -38.9, 0.01611, 23, 37.74, -63.44, 0.05261, 3, 25, 24.49, -33.45, 0.87864, 24, 49.65, -39.48, 0.03975, 23, 35.91, -54.71, 0.08161, 3, 25, 10.28, -30.87, 0.53637, 24, 36.98, -32.54, 0.17381, 23, 41, -41.2, 0.28982, 3, 25, 7.46, -39.87, 0.25582, 24, 31.46, -40.18, 0.17727, 23, 32.65, -36.81, 0.5669, 4, 25, 2.5, -49.99, 0.11708, 24, 23.55, -48.21, 0.10791, 23, 23.59, -30.11, 0.77284, 22, 242.86, -38.02, 0.00217, 4, 25, -5.83, -60.34, 0.02773, 24, 12.38, -55.39, 0.02808, 23, 14.9, -20.06, 0.85786, 22, 240.77, -24.9, 0.08634, 4, 25, -4.88, -70.02, 0.00466, 24, 10.22, -64.88, 0.00418, 23, 5.2, -19.26, 0.55966, 22, 232.95, -19.11, 0.4315, 4, 25, -3.15, -76.93, 0.00063, 24, 9.68, -71.98, 0.00041, 23, -1.9, -19.72, 0.21272, 22, 226.67, -15.76, 0.78624, 2, 23, -10.09, -24.33, 0.01946, 22, 217.28, -15.36, 0.98054, 1, 22, 174.02, -17.11, 1, 2, 22, 135.49, -22.62, 0.99659, 21, 122.43, -43.27, 0.00341, 2, 22, 65.52, -35.38, 0.93718, 21, 178.34, 0.69, 0.06282, 2, 22, 48.38, -34.21, 0.61968, 21, 194.04, 7.65, 0.38032, 1, 21, 214.35, 10.49, 1, 1, 21, 239.97, 13.4, 1, 2, 22, -18.93, -8.46, 0.23631, 21, 265.59, 16.32, 0.76369, 2, 22, -21.03, 3.96, 0.51677, 21, 273.25, 6.32, 0.48323, 2, 22, -16.79, 15.47, 0.81399, 21, 274.87, -5.83, 0.18601, 2, 22, -8.41, 21.92, 0.96568, 21, 270.48, -15.46, 0.03432, 1, 22, 8.82, 24.73, 1, 1, 22, 25.04, 30.7, 1, 1, 22, 42.93, 36.22, 1, 1, 22, 66.65, 39.66, 1, 1, 22, 94.95, 39.52, 1, 1, 22, 140.92, 34.02, 1, 1, 22, 182.45, 26.41, 1, 2, 23, -24.19, 11.72, 0.05296, 22, 224.29, 22.7, 0.94704, 2, 23, -13.96, 17, 0.27392, 22, 235.77, 21.8, 0.72608, 2, 23, -2.8, 21.17, 0.64728, 22, 247.45, 19.47, 0.35272, 2, 23, 9.24, 21.52, 0.92645, 22, 257.86, 13.42, 0.07355, 2, 23, 19.98, 19.16, 0.99934, 22, 265.75, 5.76, 0.00066, 2, 24, -16.07, -18.98, 7e-05, 23, 46.94, 13.23, 0.99993, 2, 24, -7.68, 4.49, 0.28719, 23, 71.37, 8.24, 0.71281, 2, 24, -2.31, 10.5, 0.80746, 23, 78.07, 3.77, 0.19254, 2, 24, -1.89, 16.12, 0.93182, 23, 83.69, 4.14, 0.06818, 1, 22, 6.82, 1.18, 1, 3, 25, -12.68, -34.3, 0.08749, 24, 14.12, -28.53, 0.19942, 23, 41.74, -18, 0.71308, 3, 25, 1.52, -30.51, 0.32115, 24, 28.79, -29.43, 0.2512, 23, 42.92, -32.65, 0.42765, 3, 25, -29.64, -35.78, 0.00124, 24, -2.43, -24.56, 0.00573, 23, 43.34, -1.05, 0.99303, 1, 23, 42.12, 5.67, 1, 3, 25, -20.76, -35, 0.02909, 24, 6.24, -26.63, 0.10298, 23, 42.51, -9.93, 0.86793, 1, 23, 56.74, 6.95, 1, 1, 23, 60.7, 1.36, 1, 3, 25, -28.78, -13.23, 0.00028, 24, 5.51, -3.45, 0.82321, 23, 65.36, -5.94, 0.1765, 3, 25, -32.12, 5.37, 0.00039, 24, 8.23, 15.25, 0.99409, 23, 84.26, -6, 0.00553, 2, 24, 1.07, 9.85, 0.87558, 23, 77.9, 0.32, 0.12442, 3, 25, -18.91, -9.88, 0.00645, 24, 15.93, -3.38, 0.93827, 23, 66.89, -16.25, 0.05528, 3, 25, -18.37, -46.5, 0.02636, 24, 4.87, -38.31, 0.04136, 23, 30.76, -10.21, 0.93228, 1, 23, 31.97, 3.09, 1, 4, 25, -24.41, -58.59, 0.00125, 24, -4.69, -47.86, 0.00138, 23, 19.96, -2.1, 0.99641, 22, 254.53, -12.3, 0.00095, 1, 23, 9.15, 1.42, 1, 2, 23, 0.37, 1.57, 0.61889, 22, 239.82, 1.14, 0.38111, 1, 22, 230.98, 3.13, 1, 1, 22, 220.74, 3.31, 1, 2, 25, -1.58, -0.19, 0.26584, 24, 35.44, 0.32, 0.73416, 3, 25, 11.77, -1.53, 0.99354, 24, 47.68, -5.17, 0.00389, 23, 69.59, -47.94, 0.00257], "hull": 43, "edges": [0, 84, 10, 12, 12, 14, 14, 16, 36, 38, 38, 40, 50, 52, 58, 60, 72, 74, 40, 42, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 74, 76, 76, 78, 70, 72, 68, 70, 64, 66, 66, 68, 60, 62, 62, 64, 56, 58, 52, 54, 54, 56, 48, 50, 46, 48, 42, 44, 44, 46, 16, 18, 18, 20, 20, 22, 22, 24, 78, 80, 80, 82, 82, 84, 0, 2, 88, 90, 80, 98, 98, 94, 100, 92, 102, 96, 102, 104, 100, 106, 2, 4, 4, 6, 6, 8, 8, 10], "width": 272, "height": 265}}, "arm_Rb2": {"arm_Rb": {"type": "mesh", "uvs": [0.85094, 0.00714, 0.8748, 0.0311, 0.89952, 0.06075, 0.95235, 0.11246, 0.97547, 0.16662, 1, 0.21502, 1, 0.22591, 0.90009, 0.30624, 0.78473, 0.31003, 0.76466, 0.2834, 0.75983, 0.22913, 0.72519, 0.22795, 0.68446, 0.22014, 0.6396, 0.2003, 0.60622, 0.21356, 0.58348, 0.22681, 0.56378, 0.25595, 0.51257, 0.20721, 0.45931, 0.15677, 0.48469, 0.12199, 0.5149, 0.08947, 0.55416, 0.06847, 0.59348, 0.0588, 0.69219, 0.03452, 0.781, 0.01127, 0.81034, 0.0154, 0.82834, 0.00489, 0.7251, 0.14913, 0.75249, 0.19693, 0.70318, 0.08895, 0.68839, 0.06815, 0.71469, 0.12045, 0.73469, 0.03971, 0.75676, 0.05218, 0.78389, 0.06931, 0.84648, 0.0384, 0.81532, 0.02738, 0.80548, 0.10179, 0.67627, 0.14077, 0.65893, 0.09362, 0.62752, 0.131, 0.58611, 0.13686, 0.55684, 0.15078, 0.53328, 0.17497, 0.85683, 0.15504, 0.86525, 0.20493], "triangles": [43, 19, 42, 18, 19, 43, 17, 18, 43, 15, 43, 42, 14, 15, 42, 17, 43, 15, 16, 17, 15, 32, 23, 24, 33, 32, 24, 36, 33, 24, 23, 39, 22, 30, 23, 32, 29, 30, 32, 29, 32, 33, 30, 39, 23, 39, 30, 29, 31, 29, 33, 31, 33, 34, 40, 22, 39, 41, 21, 22, 41, 22, 40, 42, 20, 21, 38, 39, 29, 38, 29, 31, 40, 39, 38, 37, 27, 31, 38, 31, 27, 41, 42, 21, 19, 20, 42, 13, 40, 38, 13, 14, 41, 13, 41, 40, 42, 41, 14, 12, 38, 27, 11, 12, 27, 13, 38, 12, 28, 11, 27, 11, 28, 10, 36, 25, 26, 35, 26, 0, 35, 0, 1, 36, 26, 35, 36, 24, 25, 34, 33, 36, 34, 36, 35, 37, 34, 35, 37, 31, 34, 35, 2, 37, 2, 35, 1, 44, 2, 3, 44, 37, 2, 28, 27, 37, 28, 37, 44, 4, 45, 44, 4, 44, 3, 28, 44, 45, 4, 5, 6, 10, 28, 45, 9, 10, 45, 6, 7, 45, 6, 45, 4, 8, 9, 45, 7, 8, 45], "vertices": [2, 24, 2.04, 20.88, 0.98197, 23, 88.96, 0.92, 0.01803, 2, 25, -31.92, 13.31, 0.00725, 24, 10.93, 22.72, 0.99275, 2, 25, -22.55, 17.68, 0.09037, 24, 21.2, 23.9, 0.90963, 2, 25, -5.5, 27.86, 0.59249, 24, 40.6, 28.17, 0.40751, 3, 25, 10.01, 30.08, 0.91228, 24, 56.02, 25.36, 0.08772, 23, 101, -51.88, 0, 3, 25, 24.16, 33.07, 0.99795, 24, 70.38, 23.73, 0.00205, 23, 101.41, -66.33, 0, 3, 25, 26.94, 32.3, 0.99917, 24, 72.77, 22.12, 0.00083, 23, 100.15, -68.93, 0, 1, 25, 40.18, 0.42, 1, 3, 25, 32.75, -30.08, 0.93128, 24, 58.55, -38.9, 0.01611, 23, 37.74, -63.44, 0.05261, 3, 25, 24.49, -33.45, 0.87864, 24, 49.65, -39.48, 0.03975, 23, 35.91, -54.71, 0.08161, 3, 25, 10.28, -30.87, 0.53637, 24, 36.98, -32.54, 0.17381, 23, 41, -41.2, 0.28982, 3, 25, 7.46, -39.87, 0.25582, 24, 31.46, -40.18, 0.17727, 23, 32.65, -36.81, 0.5669, 4, 25, 2.5, -49.99, 0.11708, 24, 23.55, -48.21, 0.10791, 23, 23.59, -30.11, 0.77284, 22, 242.86, -38.02, 0.00217, 4, 25, -5.83, -60.34, 0.02773, 24, 12.38, -55.39, 0.02808, 23, 14.9, -20.06, 0.85786, 22, 240.77, -24.9, 0.08634, 4, 25, -4.88, -70.02, 0.00466, 24, 10.22, -64.88, 0.00418, 23, 5.2, -19.26, 0.55966, 22, 232.95, -19.11, 0.4315, 4, 25, -3.15, -76.93, 0.00063, 24, 9.68, -71.98, 0.00041, 23, -1.9, -19.72, 0.21272, 22, 226.67, -15.76, 0.78624, 2, 23, -10.09, -24.33, 0.01946, 22, 217.28, -15.36, 0.98054, 1, 22, 220.74, 3.31, 1, 2, 23, -24.19, 11.72, 0.05296, 22, 224.29, 22.7, 0.94704, 2, 23, -13.96, 17, 0.27392, 22, 235.77, 21.8, 0.72608, 2, 23, -2.8, 21.17, 0.64728, 22, 247.45, 19.47, 0.35272, 2, 23, 9.24, 21.52, 0.92645, 22, 257.86, 13.42, 0.07355, 2, 23, 19.98, 19.16, 0.99934, 22, 265.75, 5.76, 0.00066, 2, 24, -16.07, -18.98, 7e-05, 23, 46.94, 13.23, 0.99993, 2, 24, -7.68, 4.49, 0.28719, 23, 71.37, 8.24, 0.71281, 2, 24, -2.31, 10.5, 0.80746, 23, 78.07, 3.77, 0.19254, 2, 24, -1.89, 16.12, 0.93182, 23, 83.69, 4.14, 0.06818, 3, 25, -12.68, -34.3, 0.08749, 24, 14.12, -28.53, 0.19942, 23, 41.74, -18, 0.71308, 3, 25, 1.52, -30.51, 0.32115, 24, 28.79, -29.43, 0.2512, 23, 42.92, -32.65, 0.42765, 3, 25, -29.64, -35.78, 0.00124, 24, -2.43, -24.56, 0.00573, 23, 43.34, -1.05, 0.99303, 1, 23, 42.12, 5.67, 1, 3, 25, -20.76, -35, 0.02909, 24, 6.24, -26.63, 0.10298, 23, 42.51, -9.93, 0.86793, 1, 23, 56.74, 6.95, 1, 1, 23, 60.7, 1.36, 1, 3, 25, -28.78, -13.23, 0.00028, 24, 5.51, -3.45, 0.82321, 23, 65.36, -5.94, 0.1765, 3, 25, -32.12, 5.37, 0.00039, 24, 8.23, 15.25, 0.99409, 23, 84.26, -6, 0.00553, 2, 24, 1.07, 9.85, 0.87558, 23, 77.9, 0.32, 0.12442, 3, 25, -18.91, -9.88, 0.00645, 24, 15.93, -3.38, 0.93827, 23, 66.89, -16.25, 0.05528, 3, 25, -18.37, -46.5, 0.02636, 24, 4.87, -38.31, 0.04136, 23, 30.76, -10.21, 0.93228, 1, 23, 31.97, 3.09, 1, 4, 25, -24.41, -58.59, 0.00125, 24, -4.69, -47.86, 0.00138, 23, 19.96, -2.1, 0.99641, 22, 254.53, -12.3, 0.00095, 1, 23, 9.15, 1.42, 1, 2, 23, 0.37, 1.57, 0.61889, 22, 239.82, 1.14, 0.38111, 1, 22, 230.98, 3.13, 1, 2, 25, -1.58, -0.19, 0.26584, 24, 35.44, 0.32, 0.73416, 3, 25, 11.77, -1.53, 0.99354, 24, 47.68, -5.17, 0.00389, 23, 69.59, -47.94, 0.00257], "hull": 27, "edges": [0, 52, 10, 12, 12, 14, 14, 16, 40, 42, 24, 26, 26, 28, 28, 30, 30, 32, 42, 44, 44, 46, 38, 40, 36, 38, 16, 18, 18, 20, 20, 22, 22, 24, 46, 48, 48, 50, 50, 52, 0, 2, 54, 56, 48, 64, 64, 60, 66, 58, 68, 62, 68, 70, 66, 72, 2, 4, 4, 6, 6, 8, 8, 10, 36, 34, 34, 32], "width": 272, "height": 265}}, "body": {"body": {"type": "mesh", "uvs": [0.5333, 0, 0.65649, 0.0004, 0.68645, 0.01193, 0.72204, 0.0284, 0.75035, 0.04674, 0.76775, 0.06513, 0.7863, 0.07952, 0.80249, 0.0922, 0.83196, 0.10029, 0.85493, 0.11001, 0.8697, 0.12112, 0.87154, 0.13224, 0.85603, 0.1435, 0.867, 0.15447, 0.88372, 0.16731, 0.89984, 0.1766, 0.92502, 0.18573, 0.95573, 0.1901, 0.99839, 0.19254, 0.99746, 0.19489, 0.9485, 0.19525, 0.91896, 0.19286, 0.91288, 0.2126, 0.89466, 0.2262, 0.86338, 0.2399, 0.8726, 0.24391, 0.89791, 0.24788, 0.87839, 0.24847, 0.85438, 0.24639, 0.84161, 0.25693, 0.85242, 0.26586, 0.88041, 0.26995, 0.9143, 0.26599, 0.94069, 0.25755, 0.9277, 0.27357, 0.8674, 0.28486, 0.79366, 0.28798, 0.74672, 0.28356, 0.71613, 0.2722, 0.70638, 0.2626, 0.68395, 0.27218, 0.65847, 0.27695, 0.63086, 0.27001, 0.61506, 0.25893, 0.64689, 0.25173, 0.65897, 0.24136, 0.65112, 0.23068, 0.61366, 0.24258, 0.57042, 0.25675, 0.53437, 0.2682, 0.51057, 0.27925, 0.4995, 0.28776, 0.53328, 0.30002, 0.56421, 0.31167, 0.59294, 0.32249, 0.64349, 0.33393, 0.68101, 0.34545, 0.68682, 0.35604, 0.73094, 0.37253, 0.7559, 0.38762, 0.74069, 0.41601, 0.68435, 0.43698, 0.66675, 0.46209, 0.63167, 0.47938, 0.60821, 0.49414, 0.60001, 0.50809, 0.59837, 0.55637, 0.57938, 0.59657, 0.57567, 0.60999, 0.57639, 0.6209, 0.58563, 0.63694, 0.58846, 0.65116, 0.58644, 0.6634, 0.59526, 0.67358, 0.62222, 0.68982, 0.63986, 0.7032, 0.65376, 0.74752, 0.6258, 0.79335, 0.59057, 0.82569, 0.5581, 0.86191, 0.55006, 0.87278, 0.54324, 0.88366, 0.54066, 0.8939, 0.54342, 0.90193, 0.56221, 0.91133, 0.59111, 0.92127, 0.60933, 0.9316, 0.61371, 0.94077, 0.57247, 0.96252, 0.56636, 0.96875, 0.55275, 0.9944, 0.52325, 0.99371, 0.52716, 0.98287, 0.51672, 0.9629, 0.43843, 0.99607, 0.40823, 0.99951, 0.31341, 0.99946, 0.19245, 0.9924, 0.19238, 0.98284, 0.32744, 0.97214, 0.34487, 0.96438, 0.37345, 0.9255, 0.38287, 0.91269, 0.39453, 0.90141, 0.40812, 0.88873, 0.41809, 0.87747, 0.42479, 0.86037, 0.4202, 0.81986, 0.40845, 0.74358, 0.40615, 0.70354, 0.39857, 0.68859, 0.38118, 0.67748, 0.35751, 0.66392, 0.349, 0.65146, 0.34142, 0.63577, 0.32698, 0.6242, 0.29879, 0.60582, 0.26572, 0.58154, 0.20849, 0.52645, 0.18603, 0.49236, 0.17563, 0.46738, 0.17293, 0.44366, 0.17602, 0.42547, 0.19439, 0.3986, 0.17299, 0.38889, 0.17272, 0.37782, 0.17924, 0.37283, 0.16496, 0.35116, 0.16187, 0.32987, 0.14079, 0.31143, 0.14705, 0.29797, 0.13125, 0.28069, 0.12714, 0.26053, 0.10554, 0.24584, 0.12128, 0.23213, 0.11558, 0.2263, 0.05995, 0.22248, 0.03126, 0.21559, 0.01261, 0.20656, 0.00304, 0.19525, 0.00537, 0.18436, 0.02782, 0.1724, 0.07601, 0.16101, 0.14163, 0.15484, 0.21904, 0.1509, 0.30343, 0.14829, 0.32379, 0.13941, 0.36121, 0.13405, 0.392, 0.13487, 0.37258, 0.12396, 0.34403, 0.12243, 0.31575, 0.11391, 0.30151, 0.09967, 0.29964, 0.08594, 0.30133, 0.07333, 0.28557, 0.05806, 0.28032, 0.04483, 0.29148, 0.0305, 0.30797, 0.02134, 0.32446, 0.01218, 0.42779, 0.00329, 0.4539, 0.0692, 0.62912, 0.05844, 0.63263, 0.07363, 0.63672, 0.0866, 0.64022, 0.09884, 0.64431, 0.1105, 0.4685, 0.08312, 0.48252, 0.09595, 0.49537, 0.1079, 0.50588, 0.11911, 0.50646, 0.12869, 0.22969, 0.41108, 0.26627, 0.42809, 0.30859, 0.4393, 0.35141, 0.44486, 0.41196, 0.44671, 0.41231, 0.44261, 0.3667, 0.43358, 0.33734, 0.42185, 0.32266, 0.40949, 0.33122, 0.39606, 0.36976, 0.38308, 0.43031, 0.37505, 0.51655, 0.3732, 0.60266, 0.37578, 0.68584, 0.3815, 0.66835, 0.12534, 0.70384, 0.12905, 0.74181, 0.13156, 0.77024, 0.13528, 0.80272, 0.14621, 0.80076, 0.15996, 0.78098, 0.17545, 0.75423, 0.18952, 0.72178, 0.20399, 0.6878, 0.21756, 0.7325, 0.24176, 0.718, 0.25182, 0.75062, 0.22895, 0.77454, 0.2165, 0.80425, 0.20259, 0.81223, 0.18848, 0.81078, 0.17348, 0.44257, 0.13327, 0.4392, 0.12356, 0.42418, 0.11147, 0.40988, 0.09901, 0.39342, 0.08465, 0.38414, 0.07023, 0.37198, 0.05668, 0.36554, 0.04621, 0.36197, 0.03303, 0.36579, 0.02253, 0.62131, 0.0485, 0.60672, 0.04769, 0.58905, 0.04166, 0.56983, 0.03277, 0.54986, 0.02353, 0.52421, 0.01549, 0.51204, 0.0115, 0.57847, 0.00939, 0.63423, 0.00915, 0.43952, 0.05508, 0.61656, 0.01886, 0.63905, 0.0297, 0.67038, 0.04236, 0.69423, 0.05457, 0.70499, 0.06853, 0.70976, 0.08419, 0.7268, 0.10053, 0.74133, 0.11231, 0.76313, 0.12104, 0.17102, 0.22127, 0.25229, 0.23365, 0.33289, 0.2362, 0.4014, 0.23315, 0.46185, 0.22331, 0.5176, 0.20923, 0.59032, 0.17128, 0.64635, 0.15071, 0.76754, 0.13858, 0.78656, 0.13926, 0.78162, 0.13005, 0.7529, 0.13847, 0.73086, 0.13922, 0.68935, 0.14465, 0.35496, 0.14567, 0.45628, 0.14033, 0.53199, 0.14155, 0.60816, 0.13381, 0.53798, 0.12207, 0.66511, 0.13747, 0.60221, 0.14527, 0.15426, 0.20622, 0.16066, 0.18577, 0.20045, 0.17158, 0.262, 0.16187, 0.33213, 0.15767, 0.42447, 0.15655, 0.23593, 0.20321, 0.02603, 0.19645, 0.03795, 0.17928, 0.0741, 0.1871, 0.07668, 0.20301, 0.04829, 0.21133, 0.12289, 0.16214, 0.15023, 0.17368, 0.09982, 0.21898, 0.18711, 0.15759, 0.25386, 0.15446, 0.3195, 0.15156, 0.36695, 0.1485, 0.39991, 0.14278, 0.41446, 0.14517, 0.2655, 0.16629, 0.34432, 0.1681, 0.40573, 0.1783, 0.42535, 0.19947, 0.40033, 0.21761, 0.32624, 0.22713, 0.24474, 0.22716, 0.19104, 0.21794, 0.26886, 0.18408, 0.31434, 0.1909, 0.21648, 0.18695, 0.19033, 0.1996, 0.19927, 0.21338, 0.24086, 0.21985, 0.3197, 0.20516, 0.29418, 0.21726, 0.52701, 0.18663, 0.49799, 0.16474, 0.49717, 0.15191, 0.56818, 0.19027, 0.55478, 0.1552, 0.26285, 0.24764, 0.26819, 0.26337, 0.26908, 0.27951, 0.27263, 0.29411, 0.28153, 0.30939, 0.29488, 0.32444, 0.30645, 0.34214, 0.33032, 0.35869, 0.17867, 0.2333, 0.17867, 0.24748, 0.18083, 0.26356, 0.18839, 0.28236, 0.19486, 0.29763, 0.19378, 0.31425, 0.20674, 0.3287, 0.20998, 0.34996, 0.21214, 0.36877, 0.24248, 0.3984, 0.27103, 0.41296, 0.2954, 0.42624, 0.32532, 0.43678, 0.22652, 0.38577, 0.38389, 0.24634, 0.37812, 0.26262, 0.37138, 0.27744, 0.36465, 0.2891, 0.37234, 0.30271, 0.37427, 0.31583, 0.39736, 0.32482, 0.42238, 0.33599, 0.44705, 0.35329, 0.30306, 0.37871, 0.30017, 0.3962, 0.30573, 0.41061, 0.31761, 0.42379, 0.33577, 0.43153, 0.35901, 0.44245, 0.36542, 0.43931, 0.35225, 0.36613, 0.74509, 0.15382, 0.71258, 0.16793, 0.67803, 0.18204, 0.64146, 0.19923, 0.60183, 0.21283, 0.55713, 0.22668, 0.51039, 0.23874, 0.47788, 0.25139, 0.45654, 0.26654, 0.43926, 0.2786, 0.44231, 0.29091, 0.45959, 0.30323, 0.48803, 0.31577, 0.53985, 0.33398, 0.56017, 0.35102, 0.61892, 0.33829, 0.57418, 0.10459, 0.57418, 0.09017, 0.57057, 0.07684, 0.56336, 0.06248, 0.5838, 0.11746, 0.0635, 0.1706, 0.27319, 0.44784, 0.29205, 0.46752, 0.30381, 0.49158, 0.3355, 0.5601, 0.38688, 0.61833, 0.39392, 0.63384, 0.39855, 0.65049, 0.4055, 0.66423, 0.43096, 0.67825, 0.44484, 0.69316, 0.45757, 0.74415, 0.50481, 0.82453, 0.56789, 0.74394, 0.5568, 0.70859, 0.53047, 0.689, 0.5277, 0.6743, 0.52077, 0.66065, 0.50968, 0.64561, 0.49721, 0.62916, 0.48889, 0.61411, 0.44705, 0.7113, 0.49582, 0.55803, 0.4875, 0.51362, 0.48376, 0.4906, 0.49665, 0.46649, 0.51661, 0.44574, 0.31401, 0.5186, 0.47759, 0.40853, 0.6187, 0.41344, 0.39973, 0.39014], "triangles": [34, 32, 33, 31, 32, 34, 37, 38, 30, 35, 30, 31, 35, 31, 34, 36, 37, 30, 35, 36, 30, 42, 43, 44, 42, 44, 39, 40, 42, 39, 29, 38, 39, 38, 29, 30, 41, 42, 40, 197, 45, 46, 28, 197, 199, 28, 24, 25, 27, 25, 26, 28, 25, 27, 198, 45, 197, 44, 45, 198, 29, 197, 28, 198, 197, 29, 39, 44, 198, 29, 39, 198, 200, 201, 23, 199, 196, 200, 24, 200, 23, 199, 200, 24, 199, 197, 46, 24, 28, 199, 201, 194, 202, 201, 202, 21, 22, 201, 21, 200, 195, 201, 23, 201, 22, 15, 202, 203, 15, 203, 14, 21, 15, 16, 21, 16, 17, 202, 15, 21, 19, 17, 18, 20, 21, 17, 20, 17, 19, 149, 150, 206, 148, 149, 205, 247, 146, 147, 273, 147, 148, 194, 193, 202, 337, 239, 336, 246, 252, 188, 239, 240, 336, 231, 230, 7, 231, 7, 8, 232, 231, 8, 232, 8, 9, 232, 9, 10, 232, 188, 231, 243, 232, 10, 189, 188, 232, 243, 189, 232, 243, 10, 11, 190, 189, 243, 244, 189, 190, 241, 244, 190, 245, 188, 189, 245, 189, 244, 242, 243, 11, 190, 243, 242, 241, 190, 242, 12, 242, 11, 191, 242, 12, 335, 245, 244, 246, 245, 335, 335, 244, 241, 191, 335, 241, 191, 241, 242, 192, 335, 191, 336, 246, 335, 191, 13, 192, 13, 191, 12, 14, 192, 13, 14, 203, 192, 193, 335, 192, 193, 192, 203, 336, 335, 193, 193, 203, 202, 245, 246, 188, 240, 252, 246, 240, 246, 336, 194, 336, 193, 337, 336, 194, 240, 253, 252, 84, 85, 101, 86, 93, 85, 87, 93, 86, 84, 103, 83, 83, 104, 82, 83, 103, 104, 93, 87, 88, 84, 102, 103, 84, 101, 102, 85, 93, 101, 100, 101, 93, 89, 93, 88, 92, 93, 89, 96, 97, 98, 90, 92, 89, 91, 92, 90, 94, 100, 93, 99, 100, 94, 99, 96, 98, 95, 96, 99, 94, 95, 99, 72, 373, 71, 364, 374, 373, 372, 373, 72, 372, 72, 73, 111, 112, 364, 365, 364, 373, 365, 373, 372, 111, 364, 365, 110, 111, 365, 371, 372, 73, 365, 372, 371, 371, 73, 74, 366, 365, 371, 110, 365, 366, 109, 110, 366, 370, 371, 74, 370, 74, 75, 377, 366, 371, 377, 371, 370, 109, 366, 377, 108, 109, 377, 369, 370, 75, 367, 377, 370, 367, 108, 377, 370, 369, 367, 369, 75, 76, 77, 369, 76, 107, 108, 367, 368, 367, 369, 368, 107, 367, 369, 77, 368, 78, 368, 77, 106, 107, 368, 79, 368, 78, 106, 368, 79, 80, 106, 79, 105, 106, 80, 81, 105, 80, 81, 104, 105, 82, 104, 81, 121, 122, 173, 120, 121, 357, 120, 357, 358, 64, 380, 381, 358, 380, 359, 119, 120, 358, 119, 358, 359, 65, 380, 64, 379, 380, 65, 380, 383, 359, 358, 381, 380, 383, 380, 379, 383, 118, 119, 383, 119, 359, 66, 379, 65, 378, 379, 66, 360, 383, 379, 378, 360, 379, 118, 383, 360, 117, 118, 360, 67, 378, 66, 116, 117, 360, 67, 376, 378, 376, 360, 378, 68, 376, 67, 361, 116, 360, 376, 361, 360, 376, 68, 69, 115, 116, 361, 375, 376, 69, 361, 376, 375, 362, 361, 375, 115, 361, 362, 114, 115, 362, 375, 69, 70, 374, 375, 70, 374, 363, 362, 374, 362, 375, 114, 362, 363, 374, 70, 71, 113, 114, 363, 373, 374, 71, 112, 113, 363, 364, 363, 374, 112, 363, 364, 181, 182, 386, 326, 325, 348, 326, 348, 349, 303, 325, 326, 334, 303, 326, 184, 326, 349, 183, 334, 326, 183, 326, 184, 57, 185, 349, 184, 349, 185, 182, 334, 183, 386, 182, 183, 384, 183, 184, 386, 183, 384, 385, 185, 186, 384, 184, 185, 384, 185, 385, 347, 324, 346, 348, 347, 53, 348, 53, 54, 325, 324, 347, 325, 347, 348, 350, 54, 55, 348, 54, 350, 324, 302, 301, 302, 324, 325, 311, 310, 302, 349, 348, 350, 127, 128, 311, 350, 55, 56, 57, 350, 56, 303, 302, 325, 303, 312, 311, 303, 311, 302, 127, 311, 312, 126, 127, 312, 57, 349, 350, 186, 185, 57, 327, 312, 303, 327, 303, 334, 58, 186, 57, 327, 334, 182, 317, 312, 327, 126, 312, 317, 125, 126, 317, 186, 58, 59, 124, 125, 317, 181, 327, 182, 328, 317, 327, 328, 327, 181, 313, 317, 328, 123, 124, 317, 123, 317, 313, 180, 328, 181, 329, 328, 180, 314, 313, 328, 172, 123, 313, 314, 172, 313, 328, 329, 314, 60, 186, 59, 385, 186, 60, 386, 180, 181, 384, 180, 386, 384, 179, 180, 179, 330, 329, 179, 329, 180, 314, 329, 330, 122, 123, 172, 315, 314, 330, 173, 172, 314, 173, 314, 315, 122, 172, 173, 331, 330, 179, 178, 179, 384, 331, 179, 178, 331, 315, 330, 316, 315, 331, 61, 385, 60, 174, 315, 316, 173, 315, 174, 333, 331, 178, 316, 331, 333, 332, 316, 333, 177, 178, 384, 333, 178, 177, 175, 316, 332, 174, 316, 175, 382, 384, 385, 382, 385, 61, 177, 384, 382, 177, 332, 333, 176, 177, 382, 176, 332, 177, 175, 332, 176, 357, 173, 174, 121, 173, 357, 62, 382, 61, 381, 176, 382, 381, 382, 62, 175, 358, 357, 175, 357, 174, 63, 381, 62, 381, 358, 176, 176, 358, 175, 63, 64, 381, 134, 305, 133, 132, 133, 305, 343, 319, 318, 319, 296, 318, 297, 296, 319, 306, 305, 296, 306, 296, 297, 132, 305, 306, 343, 342, 49, 320, 297, 319, 344, 320, 319, 343, 344, 319, 50, 343, 49, 344, 343, 50, 298, 297, 320, 131, 132, 306, 298, 307, 306, 298, 306, 297, 131, 306, 307, 51, 344, 50, 321, 298, 320, 345, 344, 51, 299, 298, 321, 307, 298, 299, 308, 307, 299, 130, 131, 307, 130, 307, 308, 321, 344, 345, 344, 321, 320, 322, 321, 345, 299, 321, 322, 346, 345, 51, 346, 51, 52, 322, 345, 346, 300, 299, 322, 308, 299, 300, 308, 129, 130, 309, 308, 300, 309, 129, 308, 347, 346, 52, 347, 52, 53, 323, 322, 346, 324, 323, 346, 300, 322, 323, 301, 300, 323, 310, 309, 300, 310, 300, 301, 128, 129, 309, 128, 309, 310, 324, 301, 323, 310, 301, 302, 128, 310, 311, 205, 169, 170, 205, 149, 206, 188, 187, 231, 171, 170, 251, 205, 170, 171, 204, 205, 171, 250, 355, 187, 251, 355, 250, 148, 205, 204, 252, 250, 187, 252, 187, 188, 248, 204, 171, 249, 171, 251, 249, 251, 250, 248, 171, 249, 273, 148, 204, 273, 204, 248, 274, 273, 248, 253, 249, 250, 253, 250, 252, 273, 247, 147, 145, 146, 247, 272, 247, 273, 272, 273, 274, 271, 145, 247, 271, 247, 272, 293, 248, 249, 270, 144, 145, 270, 145, 271, 295, 249, 253, 293, 249, 295, 259, 274, 248, 259, 248, 293, 272, 274, 259, 269, 143, 144, 269, 144, 270, 258, 271, 272, 258, 272, 259, 257, 270, 271, 257, 271, 258, 266, 142, 143, 266, 143, 269, 292, 293, 295, 259, 293, 292, 275, 257, 258, 276, 258, 259, 275, 258, 276, 356, 142, 266, 240, 239, 295, 240, 295, 253, 292, 295, 239, 257, 256, 269, 257, 269, 270, 256, 257, 275, 267, 266, 269, 356, 141, 142, 256, 267, 269, 277, 276, 259, 277, 259, 292, 262, 141, 356, 283, 275, 276, 256, 275, 283, 140, 141, 262, 255, 267, 256, 285, 255, 256, 291, 292, 239, 277, 292, 291, 283, 285, 256, 267, 263, 356, 267, 356, 266, 263, 267, 255, 262, 356, 263, 294, 291, 239, 294, 239, 337, 284, 283, 276, 284, 276, 277, 261, 140, 262, 261, 262, 263, 139, 140, 261, 338, 294, 337, 278, 277, 291, 284, 277, 278, 286, 255, 285, 264, 263, 255, 254, 264, 255, 261, 263, 264, 260, 285, 283, 260, 283, 284, 286, 285, 260, 195, 337, 194, 195, 194, 201, 338, 337, 195, 289, 284, 278, 260, 284, 289, 286, 254, 255, 138, 139, 261, 265, 138, 261, 238, 278, 291, 238, 291, 294, 264, 265, 261, 339, 294, 338, 238, 294, 339, 287, 286, 260, 254, 286, 287, 137, 138, 265, 290, 260, 289, 288, 287, 260, 196, 338, 195, 196, 195, 200, 339, 338, 196, 279, 289, 278, 237, 279, 278, 280, 290, 289, 282, 254, 287, 268, 264, 254, 233, 268, 254, 265, 264, 268, 290, 288, 260, 282, 287, 288, 282, 233, 254, 136, 265, 268, 137, 265, 136, 238, 237, 278, 135, 268, 233, 136, 268, 135, 340, 238, 339, 237, 238, 340, 279, 280, 289, 281, 288, 290, 281, 290, 280, 282, 288, 281, 46, 339, 196, 46, 196, 199, 340, 339, 46, 134, 135, 233, 236, 279, 237, 280, 279, 236, 234, 304, 233, 233, 281, 234, 134, 233, 304, 234, 281, 280, 281, 233, 282, 235, 280, 236, 234, 280, 235, 341, 237, 340, 47, 340, 46, 341, 340, 47, 318, 235, 236, 305, 304, 234, 296, 234, 235, 305, 234, 296, 341, 236, 237, 342, 236, 341, 318, 236, 342, 48, 341, 47, 342, 341, 48, 305, 134, 304, 318, 296, 235, 342, 343, 318, 49, 342, 48, 165, 164, 229, 352, 164, 165, 7, 230, 229, 165, 229, 230, 351, 352, 165, 168, 352, 351, 169, 168, 351, 166, 165, 230, 351, 165, 166, 206, 168, 169, 166, 230, 231, 355, 351, 166, 251, 170, 169, 351, 251, 169, 355, 251, 351, 205, 206, 169, 187, 166, 231, 355, 166, 187, 1, 221, 0, 222, 221, 1, 220, 160, 0, 220, 0, 221, 222, 1, 2, 219, 220, 221, 224, 221, 222, 213, 159, 160, 158, 159, 213, 218, 219, 221, 218, 221, 224, 2, 224, 222, 225, 2, 3, 225, 224, 2, 217, 218, 224, 217, 224, 225, 212, 158, 213, 157, 158, 212, 216, 217, 225, 226, 225, 3, 214, 215, 216, 223, 211, 212, 226, 3, 4, 216, 225, 214, 226, 214, 225, 227, 226, 4, 214, 226, 227, 220, 213, 160, 219, 213, 220, 223, 219, 218, 223, 218, 217, 223, 217, 216, 213, 219, 212, 219, 223, 212, 210, 211, 223, 156, 211, 155, 212, 156, 157, 211, 156, 212, 155, 211, 210, 162, 214, 227, 354, 223, 216, 354, 216, 215, 162, 354, 215, 162, 215, 214, 227, 4, 5, 228, 227, 5, 162, 227, 228, 161, 223, 354, 209, 210, 223, 209, 223, 161, 154, 155, 210, 154, 210, 209, 163, 162, 228, 354, 162, 163, 353, 354, 163, 161, 354, 353, 167, 161, 353, 6, 229, 228, 6, 228, 5, 163, 228, 229, 208, 209, 161, 208, 161, 167, 164, 163, 229, 353, 163, 164, 352, 353, 164, 167, 353, 352, 168, 167, 352, 207, 208, 167, 207, 167, 168, 154, 208, 153, 208, 154, 209, 152, 208, 207, 152, 153, 208, 7, 229, 6, 206, 207, 168, 151, 152, 207, 151, 207, 206, 150, 151, 206], "vertices": [1, 6, 157.4, -23.12, 1, 1, 6, 144.05, -77.82, 1, 1, 6, 120.64, -86.48, 1, 2, 6, 87.92, -95.63, 0.9994, 63, -246.39, -25.97, 0.0006, 4, 6, 52.67, -100.78, 0.96232, 5, 138.49, -94.06, 0.00616, 16, 152.33, 87.98, 0.02936, 63, -281.64, -31.12, 0.00217, 4, 6, 18.46, -101.05, 0.70553, 5, 104.54, -98.26, 0.16085, 16, 126.72, 65.3, 0.13058, 63, -315.85, -31.4, 0.00304, 4, 6, -8.83, -103.46, 0.44052, 5, 77.7, -103.79, 0.28193, 16, 107.71, 45.56, 0.27404, 63, -343.15, -33.8, 0.0035, 4, 6, -32.86, -105.51, 0.23023, 5, 54.06, -108.59, 0.31441, 16, 90.94, 28.24, 0.45195, 63, -367.17, -35.86, 0.00341, 4, 6, -50.15, -115.35, 0.11886, 5, 38.02, -120.34, 0.25097, 16, 84.37, 9.46, 0.62643, 63, -384.46, -45.69, 0.00374, 4, 6, -69.66, -121.62, 0.05772, 5, 19.36, -128.82, 0.1769, 16, 73.77, -8.08, 0.76204, 63, -403.97, -51.97, 0.00334, 4, 6, -90.76, -123.69, 0.00619, 5, -1.36, -133.29, 0.11891, 16, 59.22, -23.5, 0.8722, 63, -425.07, -54.03, 0.0027, 3, 5, -21.46, -131.89, 0.04017, 16, 41.86, -33.71, 0.95827, 63, -444.87, -50.33, 0.00156, 1, 16, 20.53, -37.05, 1, 2, 16, 5.37, -50.81, 0.92688, 51, -11.91, 16.82, 0.07312, 2, 16, -11.54, -68.49, 0.51714, 51, 10.82, 25.87, 0.48286, 2, 16, -22.91, -82.91, 0.24857, 51, 27.15, 34.25, 0.75143, 3, 16, -32.07, -100.84, 0.07428, 51, 42.94, 46.75, 0.8406, 52, -19.03, 43.31, 0.08512, 2, 51, 49.98, 61.24, 0.83793, 52, -15.96, 59.12, 0.16207, 2, 51, 53.21, 80.97, 0.75357, 52, -17.93, 79.02, 0.24643, 2, 51, 57.48, 80.81, 0.72533, 52, -13.76, 79.96, 0.27467, 2, 51, 59.48, 58.51, 0.7684, 52, -6.08, 58.93, 0.2316, 3, 16, -44.77, -104.46, 0.01714, 51, 56, 44.77, 0.69986, 52, -5.91, 44.76, 0.283, 4, 51, 91.84, 44.18, 0.08096, 52, 28.87, 53.42, 0.68389, 53, -12.96, 54.35, 0.22941, 54, -47.8, 64.37, 0.00573, 5, 51, 116.91, 37.38, 0.0004, 52, 54.85, 53.31, 0.39877, 53, 12.96, 52.6, 0.54123, 54, -22.66, 57.82, 0.0582, 55, -45.69, 68.42, 0.00139, 5, 52, 82.89, 47.59, 0.1258, 53, 40.58, 45.11, 0.47311, 54, 3.08, 45.33, 0.35402, 55, -22.98, 51, 0.03978, 56, -39.44, 59.8, 0.0073, 5, 52, 88.45, 53.88, 0.08057, 53, 46.53, 51.04, 0.39561, 54, 10.03, 50.05, 0.43595, 55, -15.23, 54.22, 0.06976, 56, -31.2, 61.46, 0.01811, 5, 52, 91.6, 67.12, 0.07383, 53, 50.51, 64.06, 0.39778, 54, 16.37, 62.1, 0.45622, 55, -6.6, 64.75, 0.06214, 56, -20.69, 70.11, 0.01003, 5, 52, 95.45, 59.01, 0.07835, 53, 53.84, 55.71, 0.39942, 54, 18.08, 53.28, 0.44445, 55, -6.69, 55.77, 0.06452, 56, -22.53, 61.31, 0.01326, 5, 52, 95.35, 47.4, 0.06296, 53, 53, 44.14, 0.34872, 54, 15.1, 42.06, 0.45805, 55, -11.87, 45.38, 0.09615, 56, -29.62, 52.13, 0.03412, 5, 52, 115.29, 47.9, 0.01027, 53, 72.94, 43.37, 0.08363, 54, 34.55, 37.6, 0.35438, 55, 6.28, 37.1, 0.3291, 56, -13.43, 40.48, 0.22261, 5, 52, 129.06, 57.7, 0.00127, 53, 87.3, 52.28, 0.01476, 54, 50.32, 43.68, 0.09847, 55, 22.95, 39.88, 0.23539, 56, 3.46, 39.96, 0.65011, 5, 52, 132.04, 72.18, 1e-05, 53, 91.19, 66.54, 0.00108, 54, 56.79, 56.97, 0.01707, 55, 31.97, 51.59, 0.07156, 56, 14.58, 49.7, 0.91028, 3, 52, 120.33, 84.6, 0, 55, 26.88, 67.88, 0.01038, 56, 12.75, 66.68, 0.98962, 4, 52, 102.03, 91.21, 1e-05, 53, 62.44, 87.43, 1e-05, 54, 32.43, 82.84, 0, 56, 2.16, 83, 0.99997, 2, 55, 41.29, 72.16, 0.01819, 56, 27.73, 68.07, 0.98181, 2, 55, 57.93, 42.16, 0.02622, 56, 38.22, 35.41, 0.97378, 1, 56, 32.76, 1.68, 1, 3, 54, 85.7, -2.22, 0.00265, 55, 48.38, -12.2, 0.03388, 56, 18.29, -16.06, 0.96347, 4, 4, 47.89, -177.43, 0.0003, 54, 66.2, -17.63, 0.08898, 55, 26.17, -23.37, 0.62348, 56, -5.67, -22.7, 0.28723, 5, 4, 61.13, -165.32, 0.00324, 3, 81.09, -170.43, 0.00018, 54, 49.18, -23.31, 0.31008, 55, 8.36, -25.51, 0.67324, 56, -23.56, -21.34, 0.01326, 2, 54, 67.21, -32.3, 0.32709, 55, 24.21, -37.95, 0.67291, 2, 54, 76.65, -43.3, 0.33734, 55, 31.24, -50.62, 0.66266, 3, 4, 33.05, -141.18, 0.00054, 54, 65.02, -56.78, 0.35635, 55, 17.13, -61.49, 0.64311, 5, 4, 47.35, -125.37, 0.00561, 3, 92.77, -129.82, 0.00033, 53, 102.89, -55.8, 0.0025, 54, 45.53, -65.41, 0.41822, 55, -3.69, -66.02, 0.57334, 5, 4, 65.69, -132.08, 0.02398, 3, 103.92, -145.84, 0.0018, 53, 86.58, -45.07, 0.02854, 54, 31.5, -51.83, 0.49967, 55, -14.7, -49.89, 0.44602, 6, 4, 84.85, -128.12, 0.17701, 3, 121.88, -153.61, 0.01525, 16, -178.14, -40.94, 0.0006, 53, 67.02, -44.53, 0.1498, 54, 12.38, -47.66, 0.53472, 55, -32.59, -41.96, 0.12263, 4, 4, 100.21, -115.86, 0.88007, 3, 141.5, -152.4, 0.0874, 16, -162.78, -28.68, 0.03078, 51, 131.81, -73.22, 0.00175, 3, 4, 73.16, -110.89, 0.81504, 3, 122.21, -132.79, 0.18432, 16, -189.83, -23.71, 0.00064, 4, 4, 41.23, -105.52, 0.65356, 3, 99.17, -110.05, 0.33962, 7, -96.11, 126.25, 0.00141, 52, 154.17, -69.78, 0.00541, 5, 4, 15.18, -100.74, 0.45804, 3, 80.59, -91.17, 0.52864, 7, -81.45, 104.19, 0.01277, 16, -247.81, -13.56, 0, 52, 179.05, -78.85, 0.00055, 4, 4, -7.57, -100.55, 0.25712, 3, 62.08, -77.94, 0.69178, 7, -65.77, 87.7, 0.0511, 16, -270.56, -13.37, 0, 4, 4, -23.55, -103.33, 0.12417, 3, 47.4, -71.04, 0.74742, 7, -52.66, 78.15, 0.1284, 16, -286.54, -16.16, 0, 3, 4, -35.87, -127.4, 0.03212, 3, 23.49, -83.67, 0.6148, 7, -26.79, 86.02, 0.35308, 4, 4, -47.81, -149.79, 0.00714, 3, 0.85, -95.13, 0.4168, 7, -2.39, 92.99, 0.53035, 11, -120.67, 35.79, 0.04571, 4, 4, -58.91, -170.59, 0.00046, 3, -20.19, -105.78, 0.26503, 7, 20.28, 99.47, 0.63785, 11, -98, 42.27, 0.09666, 4, 4, -66.31, -200.72, 0, 3, -43.55, -126.18, 0.12954, 7, 47.08, 115.09, 0.66564, 11, -71.19, 57.88, 0.20482, 4, 4, -76.64, -225.66, 0, 3, -66.34, -140.66, 0.08575, 7, 72.19, 125, 0.6978, 11, -46.08, 67.8, 0.21645, 4, 4, -92.3, -237.02, 1e-05, 3, -85.68, -140.96, 0.07235, 7, 91.25, 121.64, 0.70494, 11, -27.03, 64.43, 0.22271, 4, 12, -78.41, 28.63, 0.06572, 4, -109.15, -268.86, 4e-05, 7, 125.85, 131.65, 0.77005, 11, 7.57, 74.45, 0.16419, 5, 12, -66.95, 55.94, 0.15, 4, -127.9, -291.78, 0, 7, 155.36, 134.11, 0.73335, 11, 37.08, 76.9, 0.11665, 16, -390.88, -204.61, 0, 5, 12, -17.12, 77.76, 0.28493, 4, -181.87, -298.54, 0, 7, 197.73, 99.99, 0.57918, 11, 79.45, 42.79, 0.13589, 16, -444.86, -211.36, 0, 5, 12, 30.69, 71.99, 0.05079, 7, 214.71, 54.92, 0.81873, 11, 96.43, -2.28, 0.08976, 8, 34.62, 125.94, 0.04071, 16, -489.07, -192.24, 0, 4, 7, 246.48, 36.81, 0.71147, 11, 128.2, -20.39, 0.06824, 8, 69.79, 115.84, 0.22028, 16, -524.17, -202.49, 0, 4, 7, 282.04, 19.22, 0.53913, 11, 163.76, -37.99, 0.01143, 8, 108.53, 107.14, 0.44944, 16, -561.53, -215.84, 0, 3, 7, 313.44, 7.36, 0.33576, 8, 141.88, 103.02, 0.66424, 16, -591.89, -230.17, 0, 3, 7, 340.95, -0.69, 0.1891, 8, 170.54, 101.69, 0.8109, 16, -616.8, -244.36, 0, 3, 7, 423.89, -28.26, 0.01393, 8, 257.71, 94.48, 0.98607, 16, -694.26, -284.82, 0, 2, 8, 329.67, 80.43, 1, 16, -762.54, -311.4, 0, 3, 8, 353.8, 76.94, 0.99571, 9, -86.09, 75.96, 0.00429, 16, -784.77, -321.34, 0, 3, 8, 373.53, 75.81, 0.97074, 9, -66.37, 75.05, 0.02926, 16, -802.05, -330.92, 0, 3, 8, 402.81, 77.87, 0.8437, 9, -37.16, 77.45, 0.1563, 16, -825.67, -348.3, 0, 3, 8, 428.6, 77.26, 0.62213, 9, -11.41, 77.13, 0.37787, 16, -847.77, -361.56, 0, 4, 6, -1018.13, 222.94, 0, 8, 450.65, 74.69, 0.36121, 9, 10.63, 74.82, 0.63879, 16, -867.76, -371.17, 0, 4, 6, -1036.99, 223.15, 0, 8, 469.33, 77.35, 0.1677, 9, 29.26, 77.69, 0.8323, 16, -882.12, -383.4, 0, 4, 6, -1068.4, 217.74, 0, 8, 499.59, 87.46, 0.02982, 9, 59.35, 88.14, 0.97018, 16, -902.26, -408.1, 0, 3, 6, -1093.81, 215.33, 0, 8, 524.36, 93.71, 0.00322, 9, 84, 94.67, 0.99678, 2, 6, -1173.41, 227.16, 0, 9, 164.4, 95.99, 1, 2, 6, -1251.38, 258.24, 0, 9, 246.33, 78.04, 1, 2, 6, -1304.78, 287.07, 0, 9, 303.68, 58.31, 1, 1, 9, 368.14, 39.39, 1, 3, 9, 387.53, 34.49, 0.99809, 10, -48.11, 21.44, 0.00191, 16, -1209.98, -534.85, 0, 3, 9, 406.96, 30.15, 0.89866, 10, -28.24, 22.94, 0.10134, 16, -1228.81, -541.36, 0, 3, 9, 425.38, 27.81, 0.54167, 10, -9.93, 26.05, 0.45833, 16, -1245.72, -549.04, 0, 3, 9, 439.96, 28.16, 0.21099, 10, 3.94, 30.62, 0.78901, 16, -1257.96, -557, 0, 3, 9, 457.46, 35.66, 0.03596, 10, 18.52, 42.89, 0.96404, 16, -1268.93, -572.58, 0, 2, 10, 32.99, 59.89, 1, 16, -1278.59, -592.7, 0, 2, 10, 49.26, 72.29, 1, 16, -1291.16, -608.84, 0, 2, 10, 64.96, 78.05, 1, 16, -1304.87, -618.42, 0, 2, 10, 107.61, 68.77, 1, 16, -1348.48, -620.32, 0, 2, 10, 119.22, 68.64, 1, 16, -1359.73, -623.16, 0, 2, 10, 165.83, 73.27, 1, 16, -1403.62, -639.52, 0, 2, 10, 167.72, 59.86, 1, 16, -1408.87, -627.04, 0, 2, 10, 148.21, 57.09, 1, 16, -1390.72, -619.38, 0, 2, 10, 114.15, 44.13, 1, 16, -1361.08, -598.17, 0, 2, 10, 180.8, 23.13, 1, 16, -1430.89, -594.85, 0, 2, 10, 190.04, 11.12, 1, 16, -1442.88, -585.6, 0, 2, 6, -1582.82, 481.15, 0, 10, 199.9, -31.07, 1, 2, 6, -1557.95, 532.15, 0, 10, 200.18, -87.81, 1, 2, 6, -1541.09, 528.29, 0, 10, 183.36, -91.81, 1, 2, 6, -1536.08, 463.8, 0, 10, 150.31, -36.21, 1, 2, 6, -1524.17, 452.88, 0, 10, 134.8, -31.68, 1, 2, 6, -1458.55, 424.35, 0, 10, 63.32, -35.16, 1, 3, 6, -1436.92, 414.95, 0, 9, 454.79, -46.29, 0.03165, 10, 39.77, -36.3, 0.96835, 3, 6, -1418.22, 405.17, 0, 9, 434.77, -39.69, 0.18426, 10, 18.68, -35.81, 0.81574, 3, 6, -1397.25, 393.96, 0, 9, 412.26, -32.06, 0.67376, 10, -5.09, -35.05, 0.32624, 4, 6, -1378.42, 384.94, 0, 9, 392.22, -26.23, 0.95551, 10, -25.97, -35.3, 0.04449, 16, -1245.85, -485.63, 0, 2, 9, 361.54, -21.23, 1, 16, -1217.09, -473.76, 0, 2, 9, 288.29, -18.74, 1, 16, -1153.38, -437.4, 0, 2, 9, 150.28, -15.45, 1, 16, -1034.09, -367.7, 0, 1, 9, 77.94, -11.96, 1, 1, 9, 50.72, -13.72, 1, 2, 8, 469.14, -20.74, 0.01201, 9, 30.18, -20.4, 0.98799, 2, 8, 443.83, -29.71, 0.42785, 9, 5.02, -29.65, 0.57215, 2, 8, 421.03, -31.92, 0.86673, 9, -17.72, -32.12, 0.13327, 1, 8, 392.44, -33.27, 1, 1, 8, 371.05, -38.3, 1, 1, 8, 336.89, -48.68, 1, 1, 8, 291.9, -60.5, 1, 1, 8, 190.44, -79.2, 1, 2, 7, 255.73, -171.98, 0.03429, 8, 128.11, -84.87, 0.96571, 2, 7, 211.23, -162.6, 0.07714, 8, 82.63, -86.26, 0.92286, 2, 7, 170, -150.58, 0.18, 8, 39.69, -84.31, 0.82, 2, 7, 139.1, -139.13, 0.28847, 8, 6.93, -80.47, 0.71153, 3, 7, 95.4, -116.19, 0.91964, 11, -22.87, -173.39, 0.03429, 8, -40.99, -68.49, 0.04608, 3, 3, -116.1, 99.35, 0.04875, 7, 75.67, -120.09, 0.908, 8, -59.26, -76.95, 0.04325, 2, 3, -96.21, 97.03, 0.10737, 7, 56.57, -114.05, 0.89263, 2, 3, -87.61, 92.97, 0.13542, 7, 48.9, -108.44, 0.86458, 2, 3, -47.87, 94.68, 0.33978, 7, 9.55, -102.6, 0.66022, 3, 3, -9.47, 91.38, 0.64172, 7, -27.54, -92.1, 0.35828, 16, -426.4, 84.11, 0, 4, 3, 24.84, 96.88, 0.86364, 7, -62.26, -91.01, 0.13621, 16, -401.49, 108.32, 0, 17, -319.59, -60.48, 0.00015, 4, 6, -328.5, 270.03, 0, 4, -115.65, 30.09, 0.00281, 3, 48.67, 91.07, 0.94521, 7, -84.57, -80.8, 0.05198, 4, 6, -296.39, 270.04, 0, 4, -91.45, 51.18, 0.04782, 3, 80.6, 94.43, 0.94686, 7, -116.56, -78.06, 0.00532, 2, 4, -60.14, 70.01, 0.23299, 3, 117.04, 91.85, 0.76701, 2, 4, -41.32, 91.24, 0.4357, 3, 144.65, 98.41, 0.5643, 3, 4, -16.04, 96.57, 0.57851, 3, 168.4, 88.25, 0.32149, 29, -92.6, -5.48, 0.1, 4, 4, -7.95, 103.83, 0.60775, 3, 179.19, 89.55, 0.18888, 29, -83.68, 0.72, 0.19916, 59, -465.29, -98.77, 0.00421, 4, 4, -13.81, 129.52, 0.69957, 3, 189.15, 113.94, 0.08795, 30, -58.82, 3.57, 0.19688, 59, -490.71, -91.85, 0.01561, 5, 4, -8.99, 146.95, 0.7329, 3, 203.12, 125.44, 0.06145, 31, -37.54, -1.07, 0.09807, 30, -48.19, 18.21, 0.08826, 59, -503.82, -79.39, 0.01931, 4, 4, 1.43, 162.16, 0.74291, 3, 220.38, 131.9, 0.04036, 31, -21.74, 8.44, 0.19582, 59, -512.35, -63.04, 0.02091, 4, 4, 17.43, 175.65, 0.75874, 3, 241.23, 133.75, 0.02251, 31, -1.59, 14.06, 0.19531, 59, -516.72, -42.57, 0.02345, 4, 4, 35.32, 183.99, 0.76859, 3, 260.66, 130.3, 0.01168, 31, 18.15, 14.2, 0.19507, 59, -515.65, -22.86, 0.02466, 5, 4, 59.25, 185.12, 0.78682, 3, 280.9, 117.48, 0.0038, 31, 40.39, 5.29, 0.09761, 30, 29.09, 30.1, 0.08785, 59, -505.4, -1.21, 0.02392, 3, 4, 87.8, 175.39, 0.78279, 30, 52.42, 10.99, 0.1957, 59, -483.37, 19.4, 0.02151, 5, 4, 111.77, 154.19, 0.77684, 17, -69.32, 72.57, 0.01625, 30, 67.47, -17.26, 0.09791, 29, 41.37, 35.84, 0.08812, 59, -453.39, 30.58, 0.02089, 4, 4, 134.72, 126.33, 0.76047, 17, -46.38, 44.71, 0.077, 29, 60.68, 5.35, 0.14779, 59, -418.01, 37.71, 0.01474, 4, 4, 157.03, 94.53, 0.765, 17, -24.07, 12.9, 0.1419, 29, 78.87, -28.97, 0.07886, 59, -379.45, 42.43, 0.01423, 3, 4, 175.59, 93.88, 0.48631, 17, -5.5, 12.26, 0.51027, 59, -370.14, 58.51, 0.00342, 1, 17, 11.11, 1.73, 1, 2, 4, 197.51, 70.24, 0.39152, 17, 16.42, -11.38, 0.60848, 5, 6, -44.74, 98.85, 0.30019, 5, 18.79, 93.06, 0.01977, 4, 210.75, 87.36, 0.16023, 17, 29.66, 5.74, 0.5177, 62, -436.23, 168.5, 0.0021, 5, 6, -39.11, 110.94, 0.34641, 5, 23, 105.71, 0.07971, 4, 207.06, 100.18, 0.05066, 17, 25.96, 18.55, 0.52142, 62, -430.59, 180.59, 0.0018, 5, 6, -21.18, 120.07, 0.37102, 5, 39.76, 116.84, 0.17275, 4, 214.58, 118.84, 0.00799, 17, 33.49, 37.22, 0.445, 62, -412.66, 189.72, 0.00324, 4, 6, 5.39, 120.62, 0.50403, 5, 66.1, 120.44, 0.25003, 17, 53.17, 55.09, 0.23983, 62, -386.09, 190.27, 0.0061, 4, 6, 29.81, 115.87, 0.6656, 5, 90.9, 118.53, 0.23802, 17, 74.7, 67.54, 0.08776, 62, -361.67, 185.53, 0.00862, 4, 6, 51.88, 109.99, 0.87122, 5, 113.5, 115.22, 0.11492, 17, 95.2, 77.6, 0.00327, 62, -339.6, 179.65, 0.01058, 3, 6, 80.42, 110.8, 0.98777, 5, 141.76, 119.31, 0.00193, 62, -311.06, 180.46, 0.0103, 2, 6, 104.29, 107.77, 0.98948, 62, -287.19, 177.42, 0.01052, 2, 6, 128.42, 96.96, 0.98878, 62, -263.06, 166.62, 0.01122, 2, 6, 142.88, 85.9, 0.98905, 62, -248.6, 155.55, 0.01095, 2, 6, 157.34, 74.83, 0.98932, 62, -234.14, 144.49, 0.01068, 1, 6, 162.43, 25.2, 1, 2, 6, 43.49, 40.37, 0.9909, 5, 113.16, 45.1, 0.0091, 1, 6, 44.49, -42.04, 1, 2, 6, 17.34, -37.42, 0.87382, 5, 96.11, -35.18, 0.12618, 2, 6, -5.97, -33.97, 0.55552, 5, 72.56, -34.43, 0.44448, 3, 6, -27.92, -30.55, 0.26682, 5, 50.37, -33.55, 0.7319, 4, 308.42, 0.83, 0.00128, 3, 6, -48.89, -27.63, 0.05831, 5, 29.2, -33.06, 0.82825, 4, 290.7, -10.74, 0.11344, 2, 6, 17.44, 39.53, 0.79777, 5, 87.38, 41.27, 0.20223, 3, 6, -6.63, 38.51, 0.49837, 5, 63.58, 37.49, 0.48713, 4, 279.12, 66.89, 0.01451, 3, 6, -29.02, 37.64, 0.20682, 5, 41.44, 34.06, 0.65458, 4, 262.81, 51.53, 0.13861, 3, 6, -49.87, 37.51, 0.02308, 5, 20.75, 31.54, 0.45061, 4, 247.17, 37.75, 0.5263, 1, 4, 231.99, 29.34, 1, 5, 3, -159.12, 78.52, 0.01175, 7, 121.85, -107.77, 0.90613, 11, 3.57, -164.98, 0.05473, 8, -17.26, -54.08, 0.01873, 57, -356.22, -114.33, 0.00866, 3, 7, 156.29, -101.33, 0.9368, 11, 38.01, -158.54, 0.04782, 57, -339.51, -145.11, 0.01538, 3, 7, 181.54, -89.16, 0.95347, 11, 63.26, -146.37, 0.02614, 57, -320.17, -165.41, 0.02039, 2, 7, 197.13, -73.63, 0.97795, 57, -300.6, -175.47, 0.02205, 3, 7, 208.83, -48.33, 0.9305, 11, 90.55, -105.54, 0.0502, 57, -272.93, -178.82, 0.0193, 3, 7, 201.81, -45.9, 0.8862, 11, 83.53, -103.1, 0.09596, 57, -272.77, -171.4, 0.01784, 3, 7, 179.86, -60.71, 0.83853, 11, 61.58, -117.92, 0.14175, 57, -293.61, -155.07, 0.01972, 3, 7, 155.52, -66.95, 0.78851, 11, 37.24, -124.16, 0.19364, 57, -307.03, -133.82, 0.01785, 3, 7, 132.18, -66.46, 0.71219, 11, 13.9, -123.67, 0.27088, 57, -313.74, -111.46, 0.01693, 4, 3, -137.79, 29.16, 0.00074, 7, 110.24, -55.27, 0.62109, 11, -8.04, -112.47, 0.36414, 57, -309.82, -87.14, 0.01403, 3, 7, 93.3, -31.29, 0.44574, 11, -24.98, -88.5, 0.54648, 57, -292.21, -63.65, 0.00778, 3, 7, 87.97, -0.49, 0.2545, 11, -30.3, -57.7, 0.73827, 57, -264.54, -49.12, 0.00723, 3, 7, 96.89, 38.05, 0.20871, 11, -21.38, -19.16, 0.78575, 57, -225.13, -45.76, 0.00555, 3, 7, 113.44, 74.05, 0.2865, 11, -4.83, 16.85, 0.7085, 57, -185.78, -50.45, 0.005, 6, 12, -52.37, 25.35, 0.1194, 4, -133.18, -258.31, 0, 7, 134.97, 107.05, 0.43677, 11, 16.69, 49.84, 0.43883, 16, -396.17, -171.14, 0, 57, -147.77, -60.79, 0.005, 2, 4, 272.16, -33.08, 0.92785, 16, 9.17, 54.1, 0.07215, 2, 4, 273.86, -50.55, 0.63421, 16, 10.88, 36.63, 0.36579, 2, 4, 278.02, -68, 0.28432, 16, 15.04, 19.18, 0.71568, 2, 4, 278.19, -82.63, 0.08418, 16, 15.21, 4.55, 0.91582, 1, 16, 4.74, -17.86, 1, 2, 4, 245.34, -115.96, 0.09472, 16, -17.65, -28.78, 0.90528, 2, 4, 216.35, -121.18, 0.28578, 16, -46.64, -34, 0.71422, 3, 4, 188.13, -122.37, 0.5521, 3, 209.71, -208.25, 0.00025, 16, -74.86, -35.2, 0.44765, 3, 4, 158.04, -121.61, 0.71264, 3, 185.53, -190.34, 0.00724, 16, -104.94, -34.43, 0.28012, 3, 4, 129.07, -119.47, 0.8323, 3, 163.04, -171.94, 0.03357, 16, -133.92, -32.29, 0.13414, 7, 4, 100.03, -158.1, 0.02476, 3, 117.08, -186.87, 0.00177, 16, -162.96, -70.93, 0, 53, 59.13, -11.86, 0.11213, 54, 10.71, -14.09, 0.82851, 55, -27.47, -8.74, 0.0299, 59, -183.36, -126.75, 0.00293, 6, 4, 80.83, -160.83, 0.01236, 3, 99.8, -178.08, 0.00085, 53, 78.43, -13.6, 0.01274, 54, 29.35, -19.4, 0.67735, 55, -10.28, -17.7, 0.29632, 59, -189.98, -144.97, 0.00038, 6, 4, 124.38, -154.5, 0.02997, 3, 139.08, -197.92, 0.00146, 16, -138.6, -67.32, 0.00065, 53, 34.59, -9.78, 0.87422, 54, -13.01, -7.49, 0.08866, 59, -175.08, -103.56, 0.00503, 6, 4, 149.4, -153.54, 0.01387, 3, 160.11, -211.51, 0.00033, 16, -113.59, -66.37, 0.00094, 52, 55.56, -4.32, 0.02579, 53, 10.02, -4.96, 0.95148, 59, -164.15, -81.04, 0.00758, 3, 16, -84.98, -66.51, 0.03394, 52, 27.39, 0.6, 0.95597, 59, -150.57, -55.87, 0.01009, 5, 4, 202.26, -144.88, 0.05283, 16, -60.73, -57.7, 0.19213, 51, 51.07, -4.4, 0.28285, 52, 2, -4.02, 0.46287, 59, -146.92, -30.32, 0.00932, 4, 4, 225.91, -131.51, 0.22365, 16, -37.08, -44.33, 0.47323, 51, 24, -6.72, 0.29482, 59, -147.59, -3.16, 0.00831, 3, 4, 210.93, 51.21, 0.81348, 17, 29.84, -30.42, 0.18084, 59, -315.85, 69.6, 0.00568, 6, 6, -50.88, 69.02, 0.13934, 5, 16.12, 62.72, 0.07793, 4, 225.72, 60.84, 0.57921, 17, 44.62, -20.79, 0.19344, 63, -385.19, 138.67, 0.00225, 59, -317.4, 87.18, 0.00784, 6, 6, -28, 70.79, 0.42521, 5, 38.65, 67.1, 0.35412, 4, 241.81, 77.2, 0.14255, 17, 60.71, -4.43, 0.06378, 63, -362.31, 140.44, 0.00631, 59, -324.26, 109.08, 0.00802, 5, 6, -4.56, 72.09, 0.62461, 5, 61.79, 71.09, 0.34221, 4, 258.63, 93.58, 0.0164, 63, -338.87, 141.75, 0.01011, 59, -330.79, 131.63, 0.00667, 4, 6, 22.46, 73.58, 0.7382, 5, 88.46, 75.67, 0.24454, 63, -311.85, 143.24, 0.01301, 59, -338.32, 157.62, 0.00426, 4, 6, 48.83, 71.86, 0.90903, 5, 114.85, 76.99, 0.07543, 63, -285.48, 141.51, 0.01401, 59, -342.56, 183.71, 0.00153, 2, 6, 73.99, 71.76, 0.98639, 63, -260.32, 141.41, 0.01361, 2, 6, 93.12, 70.37, 0.9869, 63, -241.19, 140.02, 0.0131, 2, 6, 116.73, 66.6, 0.98765, 63, -217.58, 136.26, 0.01235, 2, 6, 134.84, 60.63, 0.98782, 63, -199.47, 130.29, 0.01218, 2, 6, 62.82, -42.6, 0.98584, 62, -328.66, 27.06, 0.01416, 2, 6, 65.74, -36.43, 0.98168, 62, -325.74, 33.23, 0.01832, 2, 6, 78.19, -31.01, 0.97807, 62, -313.29, 38.64, 0.02193, 2, 6, 95.85, -26.07, 0.97498, 62, -295.63, 43.58, 0.02502, 2, 6, 114.19, -20.93, 0.97376, 62, -277.29, 48.73, 0.02624, 2, 6, 131.01, -12.78, 0.9721, 62, -260.47, 56.88, 0.0279, 2, 6, 139.3, -8.98, 0.97197, 62, -252.19, 60.67, 0.02803, 2, 6, 136.2, -39.42, 0.97093, 62, -255.28, 30.23, 0.02907, 2, 6, 130.89, -64.35, 0.98212, 62, -260.59, 5.31, 0.01788, 1, 6, 69.88, 41.03, 1, 2, 6, 115.59, -52.54, 0.97575, 62, -275.89, 17.12, 0.02425, 2, 6, 94.16, -58.14, 0.98084, 62, -297.32, 11.51, 0.01916, 2, 6, 68.62, -66.94, 0.9932, 62, -322.86, 2.71, 0.0068, 3, 6, 44.62, -72.6, 0.97958, 5, 127.26, -67, 0.0146, 62, -346.86, -2.95, 0.00582, 4, 6, 18.9, -71.72, 0.82452, 5, 101.6, -69.07, 0.16567, 62, -372.58, -2.07, 0.00612, 59, -195.93, 186.79, 0.00368, 5, 6, -9.2, -67.48, 0.54312, 5, 73.2, -68.09, 0.4102, 16, 83.8, 72.45, 0.0353, 62, -400.68, 2.18, 0.00481, 59, -193.75, 158.46, 0.00657, 6, 6, -39.78, -68.42, 0.26921, 5, 42.93, -72.53, 0.44451, 4, 324.35, -35.51, 0.00272, 16, 61.36, 51.66, 0.27322, 62, -431.26, 1.23, 0.00304, 59, -185.96, 128.87, 0.0073, 6, 6, -62.05, -70.1, 0.10572, 5, 21, -76.76, 0.32152, 4, 308.65, -51.41, 0.02811, 16, 45.67, 35.77, 0.53748, 62, -453.53, -0.44, 0.00151, 59, -179.32, 107.54, 0.00565, 5, 5, 4.19, -84.91, 0.16085, 4, 299.4, -67.64, 0.05241, 16, 36.42, 19.54, 0.78234, 62, -471.17, -6.61, 0.00038, 59, -169.36, 91.74, 0.00402, 5, 4, 11.99, 85.75, 0.62345, 3, 185.13, 63.3, 0.15415, 26, -74.27, 58.92, 0.096, 29, -66.13, -19.69, 0.0864, 59, -439.96, -89.68, 0.04, 4, 4, 9.7, 42.43, 0.59265, 3, 158.36, 29.16, 0.17535, 26, -81.92, 16.22, 0.192, 59, -402.81, -112.08, 0.04, 3, 4, 22.97, 7.77, 0.768, 26, -73.05, -19.83, 0.192, 59, -365.98, -116.69, 0.04, 4, 4, 42.58, -17.26, 0.72328, 3, 150.98, -38.59, 0.04472, 26, -56.69, -47.09, 0.192, 59, -334.67, -111.16, 0.04, 5, 4, 71.29, -33.26, 0.72862, 3, 165.28, -68.18, 0.03935, 16, -191.7, 53.92, 3e-05, 26, -30.19, -66.53, 0.192, 59, -307.04, -93.36, 0.04, 5, 4, 105.76, -43.75, 0.74793, 3, 187.47, -96.57, 0.01633, 16, -157.23, 43.43, 0.00374, 26, 2.71, -81.21, 0.192, 59, -281.57, -67.88, 0.04, 4, 4, 182, -40.75, 0.61917, 16, -80.98, 46.43, 0.29536, 26, 78.74, -87.69, 0.04813, 59, -248.33, 0.81, 0.03734, 3, 4, 226.92, -45.81, 0.3381, 16, -36.07, 41.36, 0.63478, 59, -222.73, 38.06, 0.02712, 3, 4, 272.35, -84.35, 0.03124, 16, 9.36, 2.83, 0.9628, 59, -167.35, 60, 0.00595, 1, 16, 12.36, -5.43, 1, 3, 4, 289, -82.76, 0.02047, 16, 26.02, 4.42, 0.97818, 59, -160.91, 75.45, 0.00135, 3, 4, 269.38, -78.35, 0.08476, 16, 6.39, 8.82, 0.90416, 59, -174.04, 60.21, 0.01108, 3, 4, 263.44, -70.1, 0.1687, 16, 0.45, 17.07, 0.81539, 59, -184.11, 58.85, 0.0159, 3, 4, 245.84, -57.99, 0.1701, 16, -17.14, 29.18, 0.80887, 59, -203.08, 49.02, 0.02103, 4, 4, 172.29, 75.98, 0.80867, 17, -8.8, -5.64, 0.13872, 29, 91.72, -49.27, 0.03947, 59, -355.9, 47.17, 0.01314, 2, 4, 202.61, 39.67, 0.98901, 59, -309.59, 56.83, 0.01099, 3, 4, 216.95, 8.1, 0.8988, 16, -46.04, 95.28, 0.07804, 59, -274.99, 54.63, 0.02316, 3, 4, 245.69, -16.02, 0.90794, 16, -17.3, 71.16, 0.07128, 59, -240.18, 68.64, 0.02078, 3, 5, 13.78, 17.56, 0.40089, 4, 249.34, 22.28, 0.57845, 59, -272.25, 89.88, 0.02066, 3, 4, 252.08, -42.1, 0.42857, 16, -10.9, 45.07, 0.55132, 59, -214.16, 62.01, 0.02011, 3, 4, 226.11, -23.38, 0.62639, 16, -36.88, 63.8, 0.34718, 59, -242.9, 47.89, 0.02643, 5, 4, 32.42, 105.33, 0.70094, 3, 213.1, 67.58, 0.06451, 26, -51.56, 75.81, 0.0945, 29, -43.43, -2.8, 0.08505, 59, -447.61, -62.43, 0.055, 5, 4, 66.47, 120.17, 0.75321, 3, 249.49, 60.17, 0.01224, 26, -15.94, 86.31, 0.0945, 29, -7.8, 7.71, 0.08505, 59, -444.69, -25.41, 0.055, 4, 4, 97.69, 116.22, 0.75574, 3, 272.77, 38.99, 0.00026, 27, 38.42, 48.9, 0.189, 59, -426.5, 0.28, 0.055, 4, 4, 126.42, 99.66, 0.7706, 27, 61.04, 24.65, 0.09514, 26, 41.01, 58.52, 0.08562, 59, -398.38, 17.84, 0.04864, 3, 4, 148.21, 74.96, 0.77055, 26, 59.56, 31.31, 0.19264, 59, -366.33, 25.44, 0.03681, 4, 4, 169.87, 38.68, 0.74639, 16, -93.12, 125.86, 0.0265, 26, 76.55, -7.37, 0.19322, 59, -324.13, 27.48, 0.03389, 4, 4, 54.79, 74.96, 0.67017, 3, 213.96, 29.87, 0.02211, 28, -3.32, 6.53, 0.23076, 59, -410.29, -56.99, 0.07697, 4, 4, 20.45, 165.36, 0.69493, 3, 237.79, 123.59, 0.02348, 31, -3.11, 3.44, 0.23947, 59, -506.21, -44.74, 0.04213, 4, 4, 50.45, 175.18, 0.76056, 3, 267.98, 114.4, 0.00668, 31, 28.25, -0.1, 0.19181, 59, -500.77, -13.66, 0.04095, 4, 4, 45.72, 153.94, 0.74655, 3, 251.91, 99.73, 0.01482, 31, 15.12, -17.45, 0.19034, 59, -484.25, -27.82, 0.04829, 4, 4, 20.87, 139.35, 0.71676, 3, 223.19, 102.06, 0.04578, 31, -13.54, -20.39, 0.19064, 59, -483.07, -56.61, 0.04682, 4, 4, 1.47, 143.7, 0.71421, 3, 209.81, 116.77, 0.05806, 31, -29.38, -8.37, 0.19307, 59, -496.04, -71.68, 0.03466, 4, 4, 96.07, 155.52, 0.7669, 3, 294.03, 72.09, 7e-05, 30, 53.23, -10.52, 0.19174, 59, -461.95, 17.35, 0.04128, 4, 4, 83.53, 134.67, 0.75688, 3, 271.79, 62.23, 0.00216, 30, 34.18, -25.66, 0.18976, 59, -449.45, -3.53, 0.05119, 4, 4, 0.34, 116.42, 0.65973, 3, 193.21, 95.09, 0.11356, 30, -50.14, -13.65, 0.19332, 59, -472.49, -85.52, 0.03339, 4, 4, 117.16, 133.51, 0.78258, 30, 65.28, -38.52, 0.09662, 29, 44.15, 14.66, 0.08695, 59, -432.6, 25.6, 0.03385, 4, 4, 136.52, 109.26, 0.74909, 17, -44.58, 27.64, 0.02485, 29, 60.35, -11.81, 0.19348, 59, -402.1, 31.26, 0.03258, 4, 4, 155.26, 85.26, 0.78668, 17, -25.83, 3.64, 0.05052, 29, 75.97, -37.95, 0.13629, 59, -372.1, 36.51, 0.02651, 4, 4, 170.35, 68.73, 0.85288, 17, -10.74, -12.89, 0.04869, 29, 88.9, -56.22, 0.0784, 59, -350.41, 42.05, 0.02004, 3, 4, 186.58, 60.31, 0.93726, 17, 5.49, -21.31, 0.0548, 59, -335.35, 52.41, 0.00794, 3, 4, 185.89, 52.41, 0.95469, 29, 102.28, -74.35, 0.02953, 59, -328.7, 48.07, 0.01579, 3, 4, 120.12, 94.48, 0.756, 27, 53.5, 21.55, 0.189, 59, -396.78, 9.84, 0.055, 3, 4, 134.18, 61.16, 0.756, 27, 57.17, -14.44, 0.189, 59, -360.76, 6.57, 0.055, 3, 4, 131.09, 27.71, 0.756, 27, 44.42, -45.51, 0.189, 59, -332.69, -11.9, 0.055, 3, 4, 101.5, 1.76, 0.756, 27, 8.52, -61.65, 0.189, 59, -323.72, -50.22, 0.055, 4, 4, 67.15, -3.59, 0.75385, 3, 178.94, -41.52, 0.00215, 27, -25.89, -56.7, 0.189, 59, -335.16, -83.05, 0.055, 4, 4, 36.02, 18.18, 0.75465, 3, 165.97, -5.82, 0.00135, 27, -49.27, -26.76, 0.189, 59, -369.02, -100.27, 0.055, 4, 4, 18.44, 51.01, 0.63593, 3, 170.44, 31.15, 0.12007, 27, -56.46, 9.78, 0.189, 59, -406.26, -100.34, 0.055, 4, 4, 21.62, 80.52, 0.6396, 3, 190, 53.48, 0.1164, 27, -44.77, 37.07, 0.189, 59, -430.81, -83.64, 0.055, 4, 4, 92.44, 77.98, 0.74348, 3, 246.5, 10.72, 0.00052, 28, 33.56, -1.61, 0.186, 59, -395.24, -22.35, 0.07, 3, 4, 91.32, 53.83, 0.744, 28, 25.41, -24.38, 0.186, 59, -374.46, -34.7, 0.07, 4, 4, 76.59, 96.66, 0.73697, 3, 244.26, 35.11, 0.00703, 28, 23.88, 20.89, 0.186, 59, -419.18, -27.54, 0.07, 4, 4, 50.76, 96.43, 0.71065, 3, 222.99, 49.76, 0.03335, 28, -0.88, 28.24, 0.186, 59, -431.13, -50.45, 0.07, 4, 4, 30.68, 81.09, 0.66262, 3, 197.74, 48.74, 0.08138, 28, -24.57, 19.46, 0.186, 59, -427.04, -75.38, 0.07, 4, 4, 29.29, 58.81, 0.66893, 3, 183.81, 31.3, 0.07507, 28, -32.43, -1.44, 0.186, 59, -408.04, -87.09, 0.07, 3, 4, 69.71, 39.53, 0.744, 28, 0.56, -31.71, 0.186, 59, -372.01, -60.5, 0.07, 4, 4, 44.89, 39.51, 0.73145, 3, 185.48, 6.54, 0.01255, 28, -23.18, -24.46, 0.186, 59, -383.67, -82.41, 0.07, 5, 4, 143.88, -28.28, 0.70392, 3, 227.56, -105.82, 0.00045, 16, -119.1, 58.89, 0.06363, 26, 42.46, -70.6, 0.192, 59, -277.27, -26.96, 0.04, 4, 4, 172.59, 2.05, 0.67848, 16, -90.39, 89.23, 0.08952, 26, 74.71, -44.06, 0.192, 59, -290.53, 12.65, 0.04, 4, 4, 192.92, 13.32, 0.76064, 16, -70.07, 100.5, 0.11415, 26, 96.28, -35.4, 0.0972, 59, -290.9, 35.88, 0.02801, 5, 4, 146.92, -47.99, 0.79808, 3, 218.73, -123.69, 0.00241, 16, -116.06, 39.19, 0.06351, 26, 43.03, -90.53, 0.096, 59, -258.45, -33.55, 0.04, 4, 4, 200.04, -12.72, 0.66061, 16, -62.94, 74.46, 0.20884, 26, 100.12, -62.12, 0.09661, 59, -264.57, 29.92, 0.03395, 3, 4, -10.37, 26.26, 0.44471, 3, 132.64, 27.46, 0.51993, 59, -397.99, -137.4, 0.03535, 4, 4, -34.34, 10.72, 0.02471, 3, 104.09, 28.5, 0.94586, 59, -395.55, -165.87, 0.02777, 57, -338.63, 153.02, 0.00166, 3, 3, 75.05, 31.65, 0.97346, 59, -395.14, -195.08, 0.01996, 57, -338.23, 123.81, 0.00658, 5, 6, -334.58, 212.53, 0, 3, 48.62, 33.26, 0.97064, 7, -73.58, -24.04, 0.00498, 59, -393.52, -221.51, 0.01299, 57, -336.6, 97.38, 0.01139, 5, 3, 20.68, 32.59, 0.9308, 7, -46.02, -28.67, 0.04777, 16, -367.95, 53.31, 0, 59, -389.45, -249.16, 0.00588, 57, -332.53, 69.73, 0.01555, 6, 3, -7.1, 29.85, 0.58508, 7, -18.22, -31.24, 0.2995, 11, -136.5, -88.44, 0.10046, 16, -389.12, 35.11, 0, 59, -383.35, -276.4, 0.00106, 57, -326.43, 42.49, 0.0139, 4, 3, -39.55, 28.51, 0.14202, 7, 13.89, -36.05, 0.59825, 11, -104.39, -93.26, 0.24821, 57, -321.15, 10.45, 0.01151, 4, 3, -70.61, 21.33, 0.0216, 7, 45.75, -34.87, 0.57923, 11, -72.52, -92.08, 0.3899, 57, -310.24, -19.51, 0.00928, 5, 4, -5.58, 72.42, 0.5675, 3, 163.09, 62.48, 0.306, 26, -93.35, 47.87, 0.04839, 29, -85.21, -30.74, 0.04597, 59, -436.46, -111.45, 0.03213, 3, 4, -28.21, 60.35, 0.3967, 3, 137.63, 65.6, 0.57549, 59, -436.46, -137.1, 0.02781, 3, 4, -53.43, 45.78, 0.15786, 3, 108.62, 68.17, 0.82145, 59, -435.47, -166.21, 0.02069, 6, 6, -305.22, 245.28, 0, 4, -81.84, 26.71, 0.02229, 3, 74.41, 68.88, 0.95366, 7, -105.65, -54.14, 0.00631, 59, -432.02, -200.25, 0.01263, 57, -375.1, 118.64, 0.00511, 6, 6, -332.8, 248.6, 0, 4, -104.82, 11.1, 0.00013, 3, 46.63, 69.31, 0.94107, 7, -78.45, -59.82, 0.04376, 59, -429.06, -227.87, 0.00597, 57, -372.14, 91.02, 0.00906, 4, 3, 16.82, 73.47, 0.82358, 7, -49.96, -69.54, 0.16626, 16, -394.6, 84.55, 0, 57, -372.63, 60.92, 0.01016, 4, 3, -9.85, 70.77, 0.626, 7, -23.26, -71.94, 0.36195, 59, -423.63, -284.12, 0.00166, 57, -366.71, 34.77, 0.01038, 4, 3, -48.23, 73.99, 0.3084, 7, 13.81, -82.35, 0.65382, 11, -104.46, -139.56, 0.02785, 57, -365.23, -3.71, 0.00993, 4, 3, -82.14, 77.16, 0.1356, 7, 46.51, -91.88, 0.77677, 11, -71.77, -149.08, 0.07758, 57, -364.25, -37.75, 0.01005, 3, 7, 101.81, -95.16, 0.86059, 11, -16.47, -152.37, 0.12789, 57, -350.38, -91.38, 0.01152, 4, 3, -164.81, 60.18, 0.00423, 7, 130.9, -90.85, 0.85045, 11, 12.62, -148.05, 0.12984, 57, -337.33, -117.73, 0.01549, 3, 7, 157.2, -87.63, 0.89317, 11, 38.92, -144.84, 0.08913, 57, -326.19, -141.77, 0.0177, 3, 7, 179.55, -80.49, 0.92473, 11, 61.27, -137.69, 0.05423, 57, -312.52, -160.84, 0.02105, 4, 3, -113.48, 74.38, 0.03034, 7, 77.81, -95.08, 0.84205, 11, -40.46, -152.28, 0.11553, 57, -357.67, -68.52, 0.01207, 4, 4, 17.73, -21.44, 0.67108, 3, 128.24, -27.74, 0.29332, 16, -245.25, 65.74, 0, 59, -342.67, -135.05, 0.0356, 6, 4, -9.51, -32.98, 0.19694, 3, 99.32, -21.53, 0.77236, 7, -113, 39.35, 0.0003, 16, -272.49, 54.2, 0, 59, -345.31, -164.51, 0.02777, 57, -288.39, 154.38, 0.00262, 6, 4, -34.62, -42.88, 0.04174, 3, 73.07, -15.21, 0.92698, 7, -88.42, 28.17, 0.00381, 16, -297.61, 44.29, 0, 59, -348.39, -191.34, 0.02061, 57, -291.47, 127.55, 0.00686, 6, 4, -54.7, -50.1, 0.00967, 3, 52.49, -9.58, 0.95892, 7, -69.28, 18.76, 0.00625, 16, -317.68, 37.08, 0, 59, -351.47, -212.45, 0.01503, 57, -294.55, 106.44, 0.01013, 6, 4, -74.77, -64.79, 0.00297, 3, 27.62, -10.07, 0.90876, 7, -44.77, 14.54, 0.01844, 11, -163.04, -42.67, 0.04903, 59, -347.95, -237.07, 0.00845, 57, -291.03, 81.82, 0.01235, 6, 4, -95.31, -76.74, 0.00028, 3, 3.94, -8.05, 0.75099, 7, -21.9, 8.08, 0.04812, 11, -140.18, -49.13, 0.18567, 59, -347.07, -260.82, 0.00218, 57, -290.15, 58.07, 0.01277, 4, 3, -13.49, -16.55, 0.13863, 7, -3.17, 13.12, 0.53627, 11, -121.45, -44.09, 0.31434, 57, -279.6, 41.8, 0.01076, 4, 3, -34.96, -25.43, 0.01913, 7, 19.59, 17.78, 0.50437, 11, -98.69, -39.42, 0.46805, 57, -268.17, 21.57, 0.00845, 3, 7, 52.84, 18.89, 0.31812, 11, -65.43, -38.31, 0.67661, 57, -256.89, -9.73, 0.00527, 4, 3, -105.05, 38.11, 0.01579, 7, 76.4, -57.86, 0.64863, 11, -41.88, -115.07, 0.32406, 57, -322.69, -55.74, 0.01152, 4, 3, -136.32, 43.27, 0.00596, 7, 106.13, -68.85, 0.71531, 11, -12.15, -126.06, 0.26373, 57, -324.01, -87.4, 0.015, 4, 3, -162.51, 43.93, 0.00064, 7, 131.72, -74.45, 0.78529, 11, 13.45, -131.65, 0.19632, 57, -321.47, -113.48, 0.01775, 3, 7, 156.1, -76.61, 0.85757, 11, 37.82, -133.82, 0.12448, 57, -316.04, -137.34, 0.01795, 3, 7, 171.97, -73.02, 0.88706, 11, 53.69, -130.23, 0.09302, 57, -307.75, -151.34, 0.01992, 3, 7, 194.05, -68.99, 0.95212, 11, 75.77, -126.19, 0.02659, 57, -297.12, -171.11, 0.0213, 3, 7, 189.55, -64.46, 0.88123, 11, 71.27, -121.66, 0.09825, 57, -294.2, -165.43, 0.02052, 4, 3, -85.19, 13.02, 0.00441, 7, 61.64, -29.48, 0.51486, 11, -56.63, -86.68, 0.47294, 57, -300.22, -32.97, 0.0078, 2, 16, -19.81, -1.11, 0.98817, 59, -177.6, 32.41, 0.01183, 3, 4, 213.65, -87.19, 0.20324, 16, -49.34, -0.01, 0.77605, 59, -192.46, 6.87, 0.0207, 4, 4, 183.69, -85.28, 0.57268, 3, 227.39, -175.33, 0.0002, 16, -79.3, 1.9, 0.40008, 59, -208.25, -18.66, 0.02704, 4, 4, 148.37, -85.17, 0.87121, 3, 198.55, -154.95, 0.0095, 16, -114.62, 2.01, 0.08866, 59, -224.96, -49.77, 0.03062, 4, 4, 118.13, -80.77, 0.91331, 3, 176.33, -133.98, 0.03649, 16, -144.85, 6.4, 0.01841, 59, -243.07, -74.38, 0.03179, 4, 4, 86.39, -74.54, 0.86689, 3, 153.93, -110.65, 0.09903, 16, -176.59, 12.63, 0.00212, 59, -263.5, -99.46, 0.03196, 3, 4, 57.09, -65.97, 0.77646, 3, 134.87, -86.79, 0.19218, 59, -284.86, -121.28, 0.03136, 4, 4, 29.88, -63.64, 0.60214, 3, 113.94, -69.25, 0.37135, 7, -118.33, 88.98, 0.0011, 59, -299.72, -144.19, 0.02541, 6, 4, 1.1, -67.94, 0.33322, 3, 87.91, -56.24, 0.63294, 7, -95.24, 71.27, 0.0117, 16, -261.89, 19.24, 0, 59, -309.47, -171.61, 0.01929, 57, -252.56, 147.28, 0.00285, 6, 4, -21.88, -71.24, 0.15245, 3, 67.21, -45.74, 0.79332, 7, -76.89, 57.05, 0.03265, 16, -284.86, 15.93, 0, 59, -317.37, -193.44, 0.01516, 57, -260.45, 125.45, 0.00643, 6, 4, -40.89, -82.96, 0.06739, 3, 44.92, -44.41, 0.82281, 7, -55.26, 51.53, 0.09184, 16, -303.87, 4.21, 0, 59, -315.97, -215.72, 0.00933, 57, -259.06, 103.17, 0.00864, 6, 4, -56.84, -100.41, 0.02551, 3, 21.83, -49.53, 0.68075, 7, -31.62, 52.19, 0.24153, 11, -149.9, -5.02, 0.03952, 59, -308.08, -238.01, 0.00353, 57, -251.16, 80.88, 0.00916, 5, 4, -70.74, -122.56, 0.00541, 3, -2.27, -59.67, 0.3748, 7, -6.03, 57.59, 0.44184, 11, -124.31, 0.38, 0.16994, 57, -238.16, 58.19, 0.00801, 4, 3, -37.88, -79.16, 0.10732, 7, 32.62, 69.99, 0.4942, 11, -85.66, 12.79, 0.39384, 57, -214.48, 25.22, 0.00463, 4, 3, -69.62, -84.62, 0.01889, 7, 64.82, 69.35, 0.33661, 11, -53.46, 12.15, 0.64239, 57, -205.19, -5.62, 0.00211, 3, 3, -50.03, -114.08, 0.11473, 7, 51.15, 101.98, 0.64157, 11, -67.13, 44.77, 0.2437, 2, 5, 43.39, -2.4, 0.9799, 59, -255.71, 121.53, 0.0201, 3, 6, -5.83, -4.67, 0.38783, 5, 69.34, -5.31, 0.59174, 59, -255.71, 147.63, 0.02043, 2, 6, 18.05, -8.48, 0.97946, 59, -257.36, 171.76, 0.02054, 1, 6, 44.12, -11.11, 1, 2, 5, 19.76, -4.18, 0.97934, 59, -251.31, 98.24, 0.02066, 5, 4, 69.79, 172.27, 0.77495, 3, 282.14, 100.9, 0.00176, 31, 44.64, -10.79, 0.09589, 30, 34.47, 14.37, 0.0863, 59, -489.09, 2.04, 0.04111, 3, 7, 191.28, -109.31, 0.57434, 8, 50.63, -39.18, 0.41105, 57, -336.35, -180.87, 0.01462, 3, 7, 227.83, -112.05, 0.24425, 8, 86.83, -33.22, 0.73837, 57, -327.72, -216.49, 0.01737, 3, 7, 270.92, -120.32, 0.08975, 8, 130.69, -31.09, 0.89193, 57, -322.35, -260.04, 0.01832, 3, 7, 393.39, -144.65, 0.00841, 8, 255.54, -25.83, 0.97267, 57, -307.87, -384.06, 0.01892, 2, 8, 362.47, -10.21, 0.98394, 57, -284.39, -489.45, 0.01606, 2, 8, 390.73, -9.08, 0.98427, 57, -281.17, -517.53, 0.01573, 3, 8, 420.97, -9.21, 0.96711, 9, -18.04, -9.41, 0.01765, 57, -279.06, -547.67, 0.01524, 3, 8, 446.02, -7.88, 0.24436, 9, 6.95, -7.8, 0.7407, 57, -275.88, -572.53, 0.01494, 4, 6, -1028.37, 298.21, 0, 9, 33, 2.22, 0.98347, 16, -924.92, -321.13, 0, 57, -264.25, -597.92, 0.01653, 4, 6, -1056.08, 298.09, 0, 9, 60.29, 6.87, 0.98308, 16, -945.73, -339.42, 0, 57, -257.9, -624.89, 0.01692, 3, 6, -1147.32, 313.15, 0, 9, 152.7, 6.89, 0.99403, 57, -252.08, -717.18, 0.00597, 2, 6, -1293.94, 324.8, 0, 9, 299.14, 19.32, 1, 3, 6, -1158.27, 263.94, 0, 9, 155.47, 57.23, 0.99502, 57, -201.67, -716.8, 0.00498, 3, 6, -1094.8, 254.51, 0, 9, 91.36, 56.18, 0.98559, 57, -206.74, -652.83, 0.01441, 5, 6, -1057.53, 258.27, 0, 8, 495, 45.75, 0.02889, 9, 55.24, 46.39, 0.95583, 16, -920.68, -370.41, 0, 57, -218.77, -617.37, 0.01527, 5, 6, -1031.33, 253.53, 0, 8, 468.35, 46.46, 0.14768, 9, 28.63, 46.79, 0.83764, 16, -897.8, -356.77, 0, 57, -220.04, -590.76, 0.01468, 5, 6, -1006.54, 251.06, 0, 8, 443.46, 45.13, 0.42733, 9, 3.8, 45.18, 0.55802, 16, -877.5, -342.35, 0, 57, -223.2, -566.06, 0.01465, 4, 8, 415.91, 42.1, 0.79873, 9, -23.68, 41.83, 0.18645, 16, -855.85, -325.06, 0, 57, -228.27, -538.83, 0.01482, 4, 8, 385.77, 38.61, 0.96389, 9, -53.72, 38, 0.02122, 16, -832.27, -306.03, 0, 57, -233.97, -509.06, 0.01489, 4, 8, 358.31, 36.84, 0.98401, 9, -81.12, 35.92, 0.00018, 16, -810.02, -289.86, 0, 57, -237.77, -481.82, 0.01581, 3, 6, -1088.31, 304.48, 0, 9, 93.11, 5.81, 0.98449, 57, -256.89, -657.74, 0.01551, 3, 7, 412.34, -73.78, 0.0058, 8, 257.23, 47.51, 0.97453, 57, -234.6, -380.32, 0.01968, 3, 7, 334.69, -52.7, 0.07406, 8, 176.73, 49.68, 0.90559, 57, -238.4, -299.94, 0.02034, 3, 7, 294.5, -41.52, 0.2929, 8, 135.01, 51.06, 0.68525, 57, -240.11, -258.26, 0.02185, 3, 7, 254.79, -22.5, 0.63671, 8, 91.89, 60.16, 0.34327, 57, -234.22, -214.62, 0.02002, 4, 7, 221.85, -2.28, 0.78347, 11, 103.57, -59.49, 0.1224, 8, 55.08, 72.04, 0.07614, 57, -225.1, -177.06, 0.018, 3, 7, 318.89, -130.92, 0.02805, 8, 179.84, -30.06, 0.95369, 57, -317.69, -308.94, 0.01826, 3, 7, 152.29, 1.45, 0.44567, 11, 34.01, -55.76, 0.54965, 57, -242.93, -109.73, 0.00468, 4, 4, -198.64, -258.45, 0, 7, 180.56, 60.08, 0.56222, 11, 62.28, 2.87, 0.4328, 57, -178.45, -118.61, 0.00498, 4, 12, 50.55, -56.79, 0.14852, 7, 109.66, -22.18, 0.3271, 11, -8.61, -79.39, 0.51451, 57, -278.52, -76.42, 0.00987], "hull": 161, "edges": [0, 320, 0, 2, 2, 4, 12, 14, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 66, 68, 68, 70, 70, 72, 72, 74, 100, 102, 118, 120, 120, 122, 128, 130, 130, 132, 142, 144, 150, 152, 152, 154, 154, 156, 162, 164, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 210, 212, 214, 216, 216, 218, 218, 220, 228, 230, 234, 236, 246, 248, 248, 250, 254, 256, 256, 258, 258, 260, 264, 266, 270, 272, 290, 292, 292, 294, 312, 314, 318, 320, 324, 326, 326, 328, 328, 330, 330, 332, 322, 334, 334, 336, 336, 338, 338, 340, 340, 342, 298, 300, 300, 302, 294, 296, 296, 298, 302, 304, 288, 290, 286, 288, 284, 286, 280, 282, 282, 284, 278, 280, 272, 274, 268, 270, 274, 276, 276, 278, 260, 262, 262, 264, 250, 252, 252, 254, 244, 246, 240, 242, 242, 244, 236, 238, 238, 240, 230, 232, 232, 234, 224, 226, 226, 228, 220, 222, 222, 224, 144, 146, 146, 148, 148, 150, 140, 142, 136, 138, 138, 140, 132, 134, 134, 136, 204, 206, 206, 208, 208, 210, 212, 214, 156, 158, 158, 160, 160, 162, 164, 166, 200, 202, 202, 204, 166, 168, 168, 170, 170, 172, 124, 126, 126, 128, 122, 124, 116, 118, 114, 116, 110, 112, 112, 114, 108, 110, 106, 108, 102, 104, 104, 106, 96, 98, 98, 100, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 266, 268, 246, 344, 344, 346, 346, 348, 348, 350, 350, 352, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 118, 308, 310, 310, 312, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 20, 22, 22, 24, 78, 80, 80, 82, 82, 84, 84, 86, 74, 76, 76, 78, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 44, 46, 46, 48, 52, 54, 54, 56, 374, 376, 376, 378, 378, 380, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 92, 440, 442, 442, 444, 444, 4, 314, 316, 316, 318, 304, 306, 306, 308, 440, 438, 438, 436, 436, 434, 434, 432, 432, 430, 430, 428, 270, 466, 466, 468, 468, 470, 470, 472, 472, 474, 474, 476, 380, 484, 484, 382, 482, 484, 482, 488, 488, 490, 490, 492, 480, 492, 290, 494, 502, 498, 498, 500, 506, 504, 466, 508, 508, 510, 510, 512, 514, 516, 516, 518, 280, 524, 524, 526, 528, 530, 530, 276, 522, 530, 522, 524, 284, 532, 532, 534, 536, 272, 532, 538, 538, 540, 540, 542, 542, 544, 494, 546, 546, 496, 544, 548, 550, 552, 552, 554, 554, 556, 556, 558, 558, 560, 560, 562, 562, 564, 566, 568, 566, 570, 570, 572, 572, 574, 574, 576, 568, 578, 576, 580, 580, 578, 550, 512, 476, 582, 582, 584, 586, 518, 592, 594, 594, 596, 596, 598, 598, 600, 600, 602, 602, 604, 604, 606, 608, 610, 610, 612, 612, 614, 614, 616, 616, 618, 618, 620, 620, 622, 622, 624, 626, 628, 628, 630, 630, 632, 624, 634, 634, 626, 636, 638, 638, 640, 640, 642, 642, 644, 644, 646, 646, 648, 648, 650, 606, 654, 654, 656, 656, 658, 658, 660, 660, 662, 632, 664, 662, 666, 114, 700, 702, 704, 704, 706, 322, 708, 708, 324, 706, 708, 710, 702, 532, 712, 712, 524, 526, 528, 514, 512, 714, 716, 716, 718, 720, 722, 722, 724, 724, 726, 726, 728, 728, 730, 730, 732, 734, 736, 736, 738, 738, 740, 740, 742, 742, 744, 744, 746, 746, 748, 748, 750, 750, 752, 732, 754, 754, 734, 752, 756, 756, 758, 760, 762, 762, 764, 718, 766, 766, 720, 760, 758], "width": 457, "height": 1810}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.06197, 0.50331, 0.13932, 0.88998, 0.60875, 0.91972, 0.95016, 0.57395, 0.8568, 0.04972, 0.37937, 0.06087], "triangles": [2, 1, 0, 3, 2, 5, 3, 5, 4, 2, 0, 5], "vertices": [2, 6, 70.04, 36.9, 0.9635, 62, -321.44, 106.56, 0.0365, 2, 6, 56.8, 36.3, 0.9635, 62, -334.68, 105.96, 0.0365, 2, 6, 51, 15.48, 0.96232, 62, -340.48, 85.13, 0.03768, 2, 6, 58.59, -2.39, 0.96042, 62, -332.89, 67.27, 0.03958, 2, 6, 76.41, -2.09, 0.96038, 62, -315.07, 67.57, 0.03962, 2, 6, 80.98, 19.4, 0.96256, 62, -310.5, 89.05, 0.03744], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 46, "height": 33}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.06515, 0.35192, 0.41096, 0.05721, 0.9154, 0.31508, 0.92966, 0.70004, 0.42165, 0.94502, 0.14537, 0.77004], "triangles": [5, 0, 1, 4, 5, 1, 2, 4, 1, 3, 4, 2], "vertices": [2, 6, 73.56, 83.12, 0.97913, 62, -317.92, 152.78, 0.02087, 2, 6, 79.77, 70.69, 0.97068, 62, -311.71, 140.34, 0.02932, 2, 6, 68.72, 57.19, 0.969, 62, -322.76, 126.84, 0.031, 2, 6, 57.36, 59.35, 0.969, 62, -334.12, 129.01, 0.031, 2, 6, 53.74, 76.35, 0.97168, 62, -337.74, 146, 0.02832, 2, 6, 60.78, 83.52, 0.97893, 62, -330.7, 153.17, 0.02107], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 31, "height": 30}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 32, -8.8, -4.94, 0.9614, 62, -331.52, 77.94, 0.0386, 2, 32, -5.66, 8.7, 0.9615, 62, -328.38, 91.58, 0.0385, 2, 32, 7.99, 5.56, 0.9615, 62, -314.73, 88.44, 0.0385, 2, 32, 4.84, -8.08, 0.96125, 62, -317.88, 74.79, 0.03875], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 14}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.65845, 0.08995, 0.83752, 0.20903, 0.98459, 0.11368, 0.978, 0.26837, 0.89087, 0.50767, 0.85992, 0.68591, 0.7323, 0.84129, 0.55593, 0.94434, 0.32537, 0.94405, 0.11638, 0.89363, 0.02493, 0.92942, 0.02335, 0.85782, 0.0873, 0.74014, 0.14151, 0.57205, 0.20487, 0.42374, 0.28401, 0.34333, 0.28108, 0.14705, 0.49022, 0.05582, 0.13591, 0.81264, 0.15962, 0.6814, 0.22753, 0.55821, 0.31806, 0.46983, 0.44417, 0.42028, 0.55518, 0.41626, 0.66404, 0.44171, 0.73086, 0.51536, 0.23507, 0.81799, 0.37734, 0.82067, 0.52177, 0.81264, 0.63817, 0.74702, 0.70931, 0.63989], "triangles": [8, 27, 7, 27, 28, 7, 7, 29, 6, 7, 28, 29, 8, 9, 26, 9, 18, 26, 8, 26, 27, 10, 11, 9, 9, 11, 18, 11, 12, 18, 29, 30, 6, 6, 30, 5, 27, 22, 28, 26, 20, 27, 26, 18, 19, 26, 19, 20, 27, 20, 21, 27, 21, 22, 28, 23, 29, 28, 22, 23, 18, 12, 19, 30, 23, 24, 30, 29, 23, 12, 13, 19, 30, 25, 5, 5, 25, 4, 19, 13, 20, 30, 24, 25, 13, 14, 20, 20, 14, 21, 4, 25, 1, 25, 24, 1, 4, 1, 3, 14, 15, 21, 21, 15, 22, 1, 24, 0, 23, 22, 17, 17, 22, 16, 24, 23, 0, 23, 17, 0, 22, 15, 16, 3, 1, 2], "vertices": [2, 6, 81.65, 4.31, 0.96167, 62, -309.83, 73.97, 0.03833, 2, 6, 76.18, -1.96, 0.96142, 62, -315.3, 67.7, 0.03858, 2, 6, 77.89, -8.54, 0.9616, 62, -313.59, 61.11, 0.0384, 2, 6, 72.97, -7.13, 0.96129, 62, -318.51, 62.52, 0.03871, 2, 6, 66.08, -1.88, 0.96122, 62, -325.4, 67.78, 0.03878, 2, 6, 60.63, 0.68, 0.96137, 62, -330.85, 70.34, 0.03863, 2, 6, 56.81, 6.93, 0.96189, 62, -334.67, 76.59, 0.03811, 2, 6, 55.12, 14.74, 0.96265, 62, -336.36, 84.4, 0.03735, 2, 6, 57.26, 23.95, 0.96342, 62, -334.22, 93.61, 0.03658, 2, 6, 60.8, 31.93, 0.964, 62, -330.68, 101.58, 0.036, 2, 6, 60.49, 35.85, 0.964, 62, -330.99, 105.5, 0.036, 2, 6, 62.81, 35.38, 0.964, 62, -328.67, 105.03, 0.036, 2, 6, 66.01, 31.95, 0.964, 62, -325.47, 101.61, 0.036, 2, 6, 70.91, 28.54, 0.96367, 62, -320.57, 98.19, 0.03633, 2, 6, 75.1, 24.91, 0.96334, 62, -316.38, 94.56, 0.03666, 2, 6, 76.95, 21.15, 0.96301, 62, -314.53, 90.81, 0.03699, 2, 6, 83.29, 19.81, 0.96303, 62, -308.19, 89.47, 0.03697, 2, 6, 84.3, 10.78, 0.96225, 62, -307.18, 80.44, 0.03775, 2, 6, 63.23, 30.55, 0.96392, 62, -328.25, 100.2, 0.03608, 2, 6, 67.23, 28.63, 0.96369, 62, -324.25, 98.28, 0.03631, 2, 6, 70.57, 25, 0.96333, 62, -320.92, 94.66, 0.03667, 2, 6, 72.57, 20.73, 0.96292, 62, -318.91, 90.38, 0.03708, 2, 6, 73.01, 15.32, 0.96239, 62, -318.48, 84.98, 0.03761, 2, 6, 72.11, 10.86, 0.96195, 62, -319.37, 80.51, 0.03805, 2, 6, 70.29, 6.7, 0.96154, 62, -321.19, 76.35, 0.03846, 2, 6, 67.31, 4.57, 0.96135, 62, -324.17, 74.23, 0.03865, 2, 6, 62.14, 26.63, 0.96356, 62, -329.34, 96.28, 0.03644, 2, 6, 60.75, 20.96, 0.96305, 62, -330.74, 90.62, 0.03695, 2, 6, 59.67, 15.13, 0.96252, 62, -331.81, 84.79, 0.03748, 2, 6, 60.71, 9.99, 0.96201, 62, -330.77, 79.65, 0.03799, 2, 6, 63.5, 6.36, 0.96159, 62, -327.98, 76.01, 0.03841], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 20, 22, 32, 34, 26, 28, 22, 24, 24, 26, 28, 30, 30, 32, 6, 8, 8, 10, 16, 18, 18, 20, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 36, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 50], "width": 41, "height": 33}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 33, -8.45, -5.13, 0.97, 62, -332.28, 132.5, 0.03, 2, 33, -5.3, 8.51, 0.973, 62, -329.14, 146.15, 0.027, 2, 33, 8.34, 5.37, 0.97221, 62, -315.5, 143, 0.02779, 2, 33, 5.19, -8.27, 0.97, 62, -318.64, 129.36, 0.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 14}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.40055, 0.05608, 0.50667, 0.0998, 0.54696, 0.22284, 0.66742, 0.22192, 0.77667, 0.26287, 0.88267, 0.38562, 0.97669, 0.50686, 0.97485, 0.6287, 0.89606, 0.72987, 0.80971, 0.84503, 0.65967, 0.97075, 0.48576, 0.97134, 0.38821, 0.82747, 0.37264, 0.66075, 0.15121, 0.66562, 0.0246, 0.53724, 0.12736, 0.22871, 0.43389, 0.50903, 0.47681, 0.40422, 0.55566, 0.34283, 0.66646, 0.34134, 0.76827, 0.3638, 0.85511, 0.44166, 0.92498, 0.56143, 0.43289, 0.65576, 0.4838, 0.75907, 0.56764, 0.79799, 0.67045, 0.7905, 0.77725, 0.72463, 0.85757, 0.63079], "triangles": [12, 25, 11, 11, 26, 10, 11, 25, 26, 10, 27, 9, 10, 26, 27, 27, 28, 9, 9, 28, 8, 12, 24, 25, 12, 13, 24, 26, 18, 19, 27, 19, 20, 27, 26, 19, 28, 20, 21, 28, 27, 20, 25, 24, 26, 26, 24, 17, 18, 26, 17, 28, 29, 8, 8, 23, 7, 8, 29, 23, 29, 21, 22, 29, 28, 21, 17, 13, 14, 24, 13, 17, 14, 16, 17, 29, 22, 23, 7, 23, 6, 6, 23, 5, 16, 14, 15, 17, 16, 18, 23, 22, 5, 22, 21, 5, 16, 0, 18, 18, 2, 19, 2, 0, 1, 2, 18, 0, 21, 4, 5, 21, 20, 4, 20, 19, 3, 20, 3, 4, 3, 19, 2], "vertices": [2, 6, 81.09, 79.74, 0.97495, 62, -310.39, 149.4, 0.02505, 2, 6, 79.06, 75.96, 0.97279, 62, -312.42, 145.62, 0.02721, 2, 6, 75.59, 75.15, 0.97261, 62, -315.9, 144.81, 0.02739, 2, 6, 74.55, 70.57, 0.97103, 62, -316.93, 140.22, 0.02897, 2, 6, 72.56, 66.66, 0.96994, 62, -318.92, 136.31, 0.03006, 2, 6, 68.52, 63.34, 0.96936, 62, -322.96, 133, 0.03064, 2, 6, 64.63, 60.48, 0.96902, 62, -326.86, 130.13, 0.03098, 2, 6, 61.55, 61.26, 0.96903, 62, -329.93, 130.92, 0.03097, 2, 6, 59.68, 64.85, 0.96933, 62, -331.8, 134.5, 0.03067, 2, 6, 57.52, 68.8, 0.97008, 62, -333.96, 138.45, 0.02992, 2, 6, 55.65, 75.24, 0.97186, 62, -335.83, 144.89, 0.02814, 2, 6, 57.16, 81.85, 0.97654, 62, -334.32, 151.5, 0.02346, 2, 6, 61.66, 84.72, 0.98042, 62, -329.82, 154.37, 0.01958, 2, 6, 66.02, 84.33, 0.98035, 62, -325.46, 153.99, 0.01965, 2, 6, 67.83, 92.78, 0.98739, 62, -323.65, 162.43, 0.01261, 2, 6, 72.2, 96.84, 0.99114, 62, -319.28, 166.49, 0.00886, 2, 6, 79.11, 91.13, 0.98536, 62, -312.37, 160.79, 0.01464, 2, 6, 69.33, 81.12, 0.9768, 62, -322.16, 150.77, 0.0232, 2, 6, 71.6, 78.88, 0.97528, 62, -319.88, 148.53, 0.02472, 2, 6, 72.47, 75.52, 0.97306, 62, -319.01, 145.18, 0.02694, 2, 6, 71.54, 71.3, 0.97128, 62, -319.94, 140.96, 0.02872, 2, 6, 70.08, 67.56, 0.9702, 62, -321.41, 137.22, 0.0298, 2, 6, 67.34, 64.72, 0.96948, 62, -324.14, 134.37, 0.03052, 2, 6, 63.7, 62.76, 0.96922, 62, -327.78, 132.42, 0.03078, 2, 6, 65.62, 82.02, 0.97734, 62, -325.86, 151.67, 0.02266, 2, 6, 62.55, 80.68, 0.97621, 62, -328.93, 150.34, 0.02379, 2, 6, 60.83, 77.73, 0.97412, 62, -330.65, 147.38, 0.02588, 2, 6, 60.12, 73.77, 0.97165, 62, -331.36, 143.43, 0.02835, 2, 6, 60.85, 69.33, 0.97043, 62, -330.63, 138.98, 0.02957, 2, 6, 62.53, 65.73, 0.96949, 62, -328.95, 135.38, 0.03051], "hull": 17, "edges": [0, 32, 12, 14, 20, 22, 30, 32, 14, 16, 8, 10, 10, 12, 6, 8, 4, 6, 26, 28, 28, 30, 22, 24, 24, 26, 16, 18, 18, 20, 0, 2, 2, 4, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 34, 48, 48, 50, 50, 52, 52, 54, 54, 56, 46, 58, 58, 56], "width": 39, "height": 26}}, "hat": {"hat": {"type": "mesh", "uvs": [0.74121, 0.02202, 0.89993, 0.05712, 0.99558, 0.08973, 0.98292, 0.25363, 0.89618, 0.23113, 0.79381, 0.22717, 0.67297, 0.23581, 0.71117, 0.26361, 0.75124, 0.31064, 0.79628, 0.38429, 0.82826, 0.4575, 0.84004, 0.53408, 0.84391, 0.60378, 0.84415, 0.67381, 0.84361, 0.7293, 0.83515, 0.80993, 0.82284, 0.88117, 0.79541, 0.94146, 0.76019, 0.99763, 0.73226, 0.98999, 0.71273, 0.90238, 0.75338, 0.85369, 0.78107, 0.80402, 0.77755, 0.73399, 0.7484, 0.68219, 0.71197, 0.62193, 0.68741, 0.55718, 0.66075, 0.49627, 0.62685, 0.43254, 0.58469, 0.36832, 0.53612, 0.31943, 0.48806, 0.29081, 0.41909, 0.28482, 0.27444, 0.33156, 0.23455, 0.32979, 0.2049, 0.3666, 0.17576, 0.41408, 0.15931, 0.45677, 0.1546, 0.50245, 0.15699, 0.54982, 0.1689, 0.59313, 0.18863, 0.63881, 0.21105, 0.68021, 0.23811, 0.72486, 0.26641, 0.75911, 0.30278, 0.80172, 0.24441, 0.77459, 0.20674, 0.7383, 0.17398, 0.69333, 0.09368, 0.67853, 0.0483, 0.62947, 0.01721, 0.56829, 0.00672, 0.50395, 0.01212, 0.44549, 0.04344, 0.37998, 0.07532, 0.33146, 0.09855, 0.30214, 0.00584, 0.13124, 0.12387, 0.07948, 0.26932, 0.03558, 0.41372, 0.00889, 0.55797, 0, 0.17979, 0.14609, 0.29832, 0.10693, 0.42917, 0.08052, 0.5786, 0.06179, 0.74206, 0.05321, 0.55382, 0.25271, 0.13627, 0.66286, 0.08962, 0.61927, 0.05793, 0.56216, 0.05441, 0.50689, 0.06761, 0.44572, 0.09578, 0.38738, 0.12835, 0.34501], "triangles": [46, 44, 45, 46, 47, 44, 47, 43, 44, 47, 48, 43, 48, 42, 43, 49, 68, 48, 42, 48, 41, 48, 68, 41, 68, 69, 41, 50, 69, 49, 49, 69, 68, 69, 40, 41, 50, 70, 69, 50, 51, 70, 40, 70, 39, 40, 69, 70, 51, 71, 70, 51, 52, 71, 70, 71, 39, 71, 38, 39, 52, 53, 71, 71, 72, 38, 71, 53, 72, 38, 72, 37, 18, 19, 17, 19, 20, 17, 17, 20, 16, 16, 20, 21, 16, 21, 15, 15, 21, 22, 15, 22, 14, 22, 23, 14, 14, 23, 13, 13, 23, 24, 13, 24, 12, 24, 25, 12, 25, 26, 12, 26, 11, 12, 26, 27, 11, 27, 10, 11, 27, 28, 10, 28, 9, 10, 28, 29, 9, 1, 66, 0, 3, 4, 2, 5, 66, 1, 29, 8, 9, 29, 7, 8, 6, 66, 5, 6, 65, 66, 7, 67, 6, 67, 65, 6, 36, 74, 35, 35, 74, 34, 53, 54, 72, 54, 55, 73, 74, 56, 34, 73, 55, 74, 72, 54, 73, 62, 33, 34, 34, 56, 62, 56, 57, 62, 32, 33, 62, 62, 63, 32, 32, 64, 67, 32, 63, 64, 57, 58, 62, 62, 58, 63, 58, 59, 63, 63, 59, 64, 59, 60, 64, 64, 61, 65, 64, 60, 61, 55, 56, 74, 72, 73, 36, 73, 74, 36, 37, 72, 36, 31, 32, 67, 67, 64, 65, 30, 31, 67, 67, 7, 30, 29, 30, 7, 65, 61, 0, 65, 0, 66, 4, 5, 1, 2, 4, 1], "vertices": [2, 39, 45.51, -90.02, 0.94299, 63, -139.32, 15.55, 0.05701, 2, 39, 28.32, -119.12, 0.95965, 63, -156.51, -13.56, 0.04035, 2, 39, 14.71, -135.91, 0.94098, 63, -170.12, -30.34, 0.05902, 2, 39, -31.19, -122.69, 0.9347, 63, -216.02, -17.13, 0.0653, 2, 39, -20.85, -107.01, 0.95119, 63, -205.69, -1.44, 0.04881, 2, 39, -15.06, -87.02, 0.97123, 63, -199.9, 18.55, 0.02877, 2, 39, -12, -62.55, 0.98726, 63, -196.84, 43.02, 0.01274, 4, 39, -21.63, -68.29, 0.78886, 40, 11.12, 19.26, 0.19528, 44, -147.23, -50.25, 0, 63, -206.46, 37.28, 0.01586, 5, 39, -36.79, -73.14, 0.59122, 40, 27.03, 19.67, 0.37575, 41, -18.12, 16.52, 0.01499, 44, -135.43, -39.56, 0, 63, -221.62, 32.43, 0.01803, 5, 39, -59.73, -77.23, 0.28446, 40, 50.2, 17.17, 0.16553, 41, 5.09, 18.47, 0.53074, 44, -116.23, -26.36, 0, 63, -244.56, 28.33, 0.01927, 4, 41, 27.36, 17.95, 0.97061, 42, -17.01, 15.66, 0.01053, 44, -96.64, -15.77, 0, 63, -266.78, 26.79, 0.01886, 3, 41, 49.27, 13.22, 0.23174, 42, 5.35, 14.14, 0.75261, 63, -289.03, 29.47, 0.01564, 2, 42, 25.46, 11.39, 0.98778, 63, -308.97, 33.26, 0.01222, 3, 42, 45.54, 7.9, 0.31472, 43, 1.13, 8.13, 0.67675, 63, -328.84, 37.79, 0.00853, 2, 43, 17.26, 7.46, 0.99451, 63, -344.55, 41.52, 0.00549, 3, 43, 40.65, 4.92, 0.10703, 44, 3.61, 5.92, 0.89205, 63, -367.03, 48.47, 0.00092, 3, 44, 24.43, 7.58, 0.9796, 45, -6.73, 6.62, 0.02038, 62, -443.84, 55.56, 2e-05, 2, 45, 11.66, 7.47, 0.99956, 62, -459.68, 64.93, 0.00044, 2, 45, 29.47, 6.42, 0.99909, 62, -474.01, 75.57, 0.00091, 2, 45, 29.34, 0.33, 0.99847, 62, -470.57, 80.59, 0.00153, 3, 44, 34.91, -13.11, 0.11261, 45, 6.8, -12.21, 0.88517, 62, -444.83, 78.73, 0.00223, 3, 44, 19.38, -7.83, 0.98291, 45, -9.35, -9.37, 0.01562, 62, -432.88, 67.5, 0.00147, 4, 42, 80.63, -11.3, 0, 43, 38.54, -5.99, 0.15894, 44, 4.1, -5.18, 0.84009, 62, -420.06, 58.78, 0.00097, 3, 43, 18.15, -5.99, 0.99882, 44, -15.73, -9.92, 0.00018, 62, -400.04, 54.9, 0.001, 4, 42, 44.56, -11.67, 0.35717, 43, 2.88, -11.38, 0.64129, 44, -29.33, -18.71, 1e-05, 62, -384.02, 57.28, 0.00153, 5, 41, 65.38, -19.49, 0.00083, 42, 26.01, -15.9, 0.98778, 43, -14.9, -18.15, 0.00929, 44, -45.06, -29.43, 0, 62, -365.27, 60.55, 0.0021, 3, 41, 45.92, -18.3, 0.28896, 42, 6.59, -17.54, 0.70877, 62, -345.79, 61.17, 0.00227, 4, 40, 65.18, -22.76, 0.00042, 41, 27.39, -17.88, 0.92761, 42, -11.81, -19.79, 0.06959, 62, -327.3, 62.46, 0.00238, 4, 39, -65.68, -40.56, 0.11337, 40, 45.64, -19.7, 0.21163, 41, 7.63, -18.59, 0.67232, 62, -307.69, 65, 0.00268, 4, 39, -45.55, -36.42, 0.39521, 40, 25.15, -18.03, 0.56548, 41, -12.8, -20.85, 0.03707, 62, -287.55, 69.15, 0.00225, 3, 39, -29.47, -30.01, 0.69674, 40, 7.92, -19.68, 0.30196, 62, -271.48, 75.56, 0.0013, 2, 39, -19.16, -22.37, 0.99937, 62, -261.17, 83.19, 0.00063, 2, 39, -14.32, -9.12, 0.99964, 62, -256.33, 96.45, 0.00036, 3, 39, -20.98, 22.55, 0.99994, 62, -262.98, 128.11, 5e-05, 63, -205.81, 128.11, 2e-05, 2, 39, -18.66, 30.32, 0.98197, 63, -203.49, 135.89, 0.01803, 4, 39, -27.74, 38.59, 0.73472, 44, -97.49, -145.04, 0, 46, 5.65, 14.53, 0.24752, 63, -212.58, 144.16, 0.01776, 4, 39, -39.88, 47.46, 0.50791, 46, 20.67, 14.15, 0.43984, 47, -5.61, 17.95, 0.03503, 63, -224.71, 153.03, 0.01722, 4, 39, -51.24, 53.5, 0.32892, 46, 33.45, 15.64, 0.20357, 47, 6.68, 14.15, 0.45046, 63, -236.07, 159.07, 0.01705, 5, 39, -63.97, 57.42, 0.14219, 46, 46.16, 19.66, 0.01171, 47, 19.92, 12.69, 0.79591, 48, -9.15, 16.23, 0.03293, 63, -248.81, 162.99, 0.01727, 3, 47, 33.72, 12.66, 0.35106, 48, 3.98, 12.02, 0.63156, 63, -262.35, 165.61, 0.01737, 4, 47, 46.4, 14.61, 0.00572, 48, 16.66, 10.03, 0.96069, 49, -9.85, 13.14, 0.01638, 63, -275.17, 166.09, 0.01721, 3, 48, 30.53, 9.3, 0.28855, 49, 3.31, 8.73, 0.69388, 63, -289.03, 165.17, 0.01758, 3, 49, 15.77, 5.48, 0.98101, 50, -11.89, 5.32, 0.00094, 63, -301.79, 163.44, 0.01804, 4, 44, 3.36, -117.78, 0, 49, 29.55, 2.46, 0.28145, 50, 1.93, 2.48, 0.69987, 63, -315.68, 161.01, 0.01868, 3, 44, 11.99, -110.18, 0, 50, 13.4, 1.57, 0.98053, 63, -326.69, 157.65, 0.01947, 2, 50, 27.8, 0.62, 0.98025, 63, -340.43, 153.24, 0.01975, 2, 50, 14.58, -4.67, 0.98155, 63, -330.07, 163.01, 0.01845, 4, 44, 8.46, -123.25, 0, 49, 29.06, -5, 0.33406, 50, 1.54, -4.98, 0.64866, 63, -318.06, 168.09, 0.01728, 3, 44, -3.06, -132.36, 0, 49, 14.54, -2.86, 0.98304, 63, -303.82, 171.63, 0.01696, 5, 44, -4.05, -149.19, 0, 48, 34.87, -12.75, 0.41695, 49, 1.61, -13.68, 0.56069, 50, -25.8, -14.03, 0.00058, 63, -295.96, 186.55, 0.02178, 6, 44, -16.22, -161.05, 0, 47, 56.05, -10.25, 0.00245, 48, 18.32, -16.59, 0.91608, 49, -15.37, -12.95, 0.05237, 50, -42.78, -13.52, 0.00013, 63, -279.98, 192.32, 0.02897, 5, 44, -32.42, -170.76, 0, 47, 38.03, -15.89, 0.40981, 48, -0.57, -16.5, 0.55414, 50, -61.03, -8.63, 1e-05, 63, -261.21, 194.47, 0.03604, 5, 39, -57.66, 86.77, 0.0991, 44, -50.34, -176.56, 0, 47, 19.24, -17.32, 0.84327, 48, -18.91, -12.16, 0.01671, 63, -242.49, 192.34, 0.04092, 4, 39, -41.33, 81.88, 0.33103, 46, 41.45, -13.34, 0.11812, 47, 2.28, -15.59, 0.50866, 63, -226.16, 187.45, 0.04219, 4, 39, -24.18, 71.41, 0.60114, 46, 21.39, -14.48, 0.34896, 47, -16.53, -8.52, 0.00572, 63, -209.01, 176.97, 0.04418, 4, 39, -11.87, 61.93, 0.81006, 44, -102.3, -172.85, 0, 46, 5.88, -13.69, 0.14423, 63, -196.71, 167.5, 0.04571, 2, 39, -4.62, 55.42, 0.95242, 63, -189.45, 160.98, 0.04758, 2, 39, 48.07, 62.58, 0.95785, 63, -136.76, 168.15, 0.04215, 2, 39, 57.37, 35.85, 0.95151, 63, -127.47, 141.42, 0.04849, 2, 39, 63.18, 4.21, 0.94932, 63, -121.65, 109.78, 0.05068, 2, 39, 64.17, -26.1, 0.94817, 63, -120.67, 79.47, 0.05183, 2, 39, 60.11, -55.21, 0.9424, 63, -124.73, 50.35, 0.0576, 2, 39, 35.93, 29.15, 0.99937, 63, -148.91, 134.71, 0.00063, 1, 39, 41.63, 3.14, 1, 1, 39, 43.15, -24.47, 1, 2, 39, 41.65, -55.25, 0.98894, 63, -143.19, 50.31, 0.01106, 2, 39, 36.63, -88.15, 0.97897, 63, -148.21, 17.42, 0.02103, 2, 39, -11.36, -37.87, 0.99607, 63, -196.19, 67.7, 0.00393, 5, 44, -10.23, -141.62, 0, 48, 33.51, -3.08, 0.22852, 49, 2.88, -3.99, 0.75276, 50, -24.65, -4.32, 0.00036, 63, -293.46, 177.1, 0.01836, 5, 44, -20.79, -153.42, 0, 48, 18.37, -7.69, 0.93795, 49, -12.95, -4.39, 0.04057, 50, -40.47, -4.93, 9e-05, 63, -278.97, 183.48, 0.0214, 5, 44, -35.8, -163.01, 0, 47, 36.55, -7.57, 0.38746, 48, 0.55, -8.11, 0.58459, 50, -57.81, -0.81, 1e-05, 63, -261.33, 186.02, 0.02794, 5, 39, -60.66, 77.53, 0.10536, 44, -51.42, -166.9, 0, 47, 20.46, -7.68, 0.85227, 48, -14.82, -3.34, 0.01111, 63, -245.5, 183.1, 0.03126, 4, 39, -43.92, 70.92, 0.35317, 46, 37.35, -2.85, 0.11229, 47, 2.77, -4.34, 0.50373, 63, -228.75, 176.49, 0.03081, 3, 39, -28.66, 61.54, 0.5953, 46, 19.46, -3.81, 0.37606, 63, -213.5, 167.1, 0.02864, 3, 39, -18.13, 52.32, 0.76941, 46, 5.56, -2.23, 0.20578, 63, -202.97, 157.89, 0.02481], "hull": 62, "edges": [4, 6, 10, 12, 38, 40, 44, 46, 58, 60, 68, 70, 114, 116, 120, 122, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 28, 30, 30, 32, 46, 48, 52, 54, 60, 62, 62, 64, 64, 66, 66, 68, 12, 14, 14, 16, 54, 56, 56, 58, 48, 50, 50, 52, 24, 26, 26, 28, 70, 72, 72, 74, 110, 112, 106, 108, 108, 110, 112, 114, 116, 118, 118, 120, 102, 104, 104, 106, 98, 100, 100, 102, 96, 98, 94, 96, 92, 94, 90, 92, 88, 90, 84, 86, 86, 88, 82, 84, 78, 80, 80, 82, 74, 76, 76, 78, 66, 124, 124, 126, 124, 114, 126, 128, 128, 130, 130, 132, 0, 2, 2, 4, 132, 2, 122, 0, 64, 134, 134, 12], "width": 203, "height": 291}}, "head": {"head": {"type": "mesh", "uvs": [0.21466, 0.03626, 0.30385, 0.02333, 0.38433, 0.01167, 0.46481, 1e-05, 0.54829, 0.07116, 0.62011, 0.14734, 0.67471, 0.23136, 0.72932, 0.31539, 0.7783, 0.37098, 0.81605, 0.38047, 0.89891, 0.31478, 0.95373, 0.32693, 0.99736, 0.38469, 0.98628, 0.48561, 0.89722, 0.60027, 0.82887, 0.60042, 0.81965, 0.70564, 0.79247, 0.76354, 0.66989, 0.8718, 0.54092, 0.94934, 0.45301, 0.98848, 0.38483, 0.99363, 0.33313, 0.97867, 0.25724, 0.89388, 0.16632, 0.79788, 0.0782, 0.66983, 0.06111, 0.61721, 0.05409, 0.54696, 0.04479, 0.50794, 0.0141, 0.46025, 0.00493, 0.40778, 0.00423, 0.38136, 0.00817, 0.28258, 0.03321, 0.18122, 0.07212, 0.10346, 0.12545, 0.04918, 0.37442, 0.46753, 0.38521, 0.44435, 0.40447, 0.41708, 0.43092, 0.39868, 0.46088, 0.39185, 0.49506, 0.39151, 0.52238, 0.40009, 0.54143, 0.41375, 0.53046, 0.43635, 0.51266, 0.45361, 0.48397, 0.46508, 0.45265, 0.46864, 0.42191, 0.46792, 0.39501, 0.46806, 0.36415, 0.46509, 0.37974, 0.43879, 0.39758, 0.408, 0.42459, 0.38598, 0.45752, 0.37461, 0.4976, 0.37423, 0.5343, 0.38117, 0.56773, 0.394, 0.36244, 0.48258, 0.38039, 0.47667, 0.42646, 0.48499, 0.48843, 0.48534, 0.53776, 0.46696, 0.56263, 0.44199, 0.57364, 0.40905, 0.19693, 0.49535, 0.18211, 0.48002, 0.15927, 0.46707, 0.13563, 0.46298, 0.10719, 0.464, 0.08555, 0.47456, 0.07193, 0.49398, 0.07594, 0.51372, 0.09557, 0.53211, 0.12923, 0.53418, 0.16007, 0.52397, 0.18171, 0.51, 0.05316, 0.48575, 0.07332, 0.46252, 0.1009, 0.45124, 0.13512, 0.45192, 0.1643, 0.46027, 0.18737, 0.47267, 0.20806, 0.48689, 0.06327, 0.51212, 0.06839, 0.53486, 0.08847, 0.55195, 0.13492, 0.55264, 0.16918, 0.53456, 0.19004, 0.51916, 0.20855, 0.5051, 0.21708, 0.47839, 0.27169, 0.4632, 0.32716, 0.46934, 0.37937, 0.65786, 0.349, 0.68421, 0.3065, 0.68611, 0.27519, 0.6953, 0.24164, 0.69562, 0.23073, 0.62262, 0.36699, 0.61204, 0.34708, 0.54069, 0.2522, 0.74259, 0.28451, 0.75099, 0.32759, 0.7319, 0.37247, 0.73534, 0.42184, 0.74526, 0.45909, 0.7529, 0.23215, 0.7529, 0.22659, 0.7765, 0.23639, 0.7978, 0.25265, 0.81949, 0.26967, 0.8443, 0.30307, 0.86136, 0.34526, 0.86136, 0.38969, 0.84686, 0.43053, 0.82205, 0.45566, 0.79456, 0.47271, 0.76937, 0.48169, 0.75869, 0.46419, 0.76365, 0.43861, 0.76785, 0.40134, 0.77243, 0.36627, 0.7735, 0.32858, 0.77853, 0.30212, 0.78732, 0.27608, 0.78731, 0.36942, 0.7854, 0.33172, 0.79647, 0.29762, 0.80067, 0.26942, 0.80243, 0.2539, 0.80532, 0.29532, 0.82533, 0.33192, 0.8229, 0.3771, 0.81025, 0.41172, 0.79397, 0.26666, 0.81806, 0.48426, 0.73922, 0.50152, 0.7562, 0.49261, 0.77679, 0.11091, 0.60686, 0.1428, 0.70533, 0.20813, 0.79944, 0.28209, 0.39737, 0.46342, 0.29455, 0.50263, 0.28552, 0.57696, 0.28969, 0.57615, 0.27302, 0.50835, 0.25634, 0.44545, 0.25843, 0.25258, 0.35291, 0.25758, 0.38461, 0.34539, 0.30185, 0.37439, 0.34388, 0.41646, 0.31609, 0.39379, 0.27771, 0.29546, 0.322, 0.32742, 0.37445, 0.1517, 0.38228, 0.17125, 0.41221, 0.16837, 0.42991, 0.03243, 0.40168, 0.02656, 0.36297, 0.10607, 0.37761, 0.11455, 0.42219, 0.06013, 0.36336, 0.06827, 0.40861, 0.34193, 0.88911, 0.42276, 0.87231, 0.5102, 0.88917, 0.59863, 0.81385, 0.68927, 0.62904, 0.65822, 0.48451, 0.65594, 0.73333, 0.50411, 0.55384, 0.5067, 0.68165, 0.34996, 0.93343, 0.43416, 0.92177, 0.31204, 0.90824, 0.2573, 0.85694, 0.22336, 0.54847, 0.17509, 0.59289, 0.19534, 0.68763, 0.8222, 0.4863, 0.22737, 0.242, 0.12443, 0.27241, 0.12226, 0.15629, 0.21978, 0.12865, 0.45057, 0.08441, 0.52317, 0.17104, 0.38591, 0.1882, 0.3498, 0.09178], "triangles": [165, 162, 32, 163, 165, 185, 161, 162, 165, 31, 162, 161, 166, 165, 163, 161, 165, 166, 166, 163, 164, 79, 166, 164, 166, 29, 161, 185, 158, 163, 159, 158, 151, 164, 163, 158, 164, 158, 159, 160, 164, 159, 80, 164, 160, 160, 159, 151, 156, 184, 152, 150, 184, 156, 153, 157, 156, 150, 156, 157, 150, 158, 185, 143, 151, 150, 158, 150, 151, 157, 143, 150, 157, 153, 52, 92, 151, 143, 151, 91, 160, 51, 143, 157, 155, 190, 149, 144, 149, 145, 155, 149, 144, 155, 152, 190, 154, 155, 144, 152, 155, 154, 153, 152, 154, 54, 154, 144, 153, 156, 152, 53, 153, 154, 148, 149, 189, 148, 189, 147, 145, 149, 148, 145, 148, 147, 146, 147, 6, 145, 147, 146, 55, 144, 145, 146, 56, 145, 188, 2, 3, 188, 3, 4, 191, 1, 2, 191, 2, 188, 187, 0, 1, 187, 1, 191, 35, 0, 187, 186, 34, 35, 187, 186, 35, 189, 188, 4, 189, 4, 5, 33, 34, 186, 190, 191, 188, 190, 188, 189, 187, 191, 190, 184, 187, 190, 186, 187, 184, 189, 149, 190, 185, 186, 184, 33, 186, 185, 147, 189, 5, 147, 5, 6, 152, 184, 190, 32, 33, 185, 146, 6, 7, 185, 184, 150, 185, 165, 32, 56, 55, 145, 55, 54, 144, 31, 32, 162, 54, 53, 154, 55, 40, 54, 42, 41, 55, 41, 40, 55, 53, 54, 40, 57, 56, 146, 57, 146, 7, 39, 53, 40, 56, 42, 55, 30, 31, 161, 52, 153, 53, 52, 53, 39, 64, 57, 7, 43, 56, 57, 43, 57, 64, 42, 56, 43, 38, 52, 39, 44, 42, 43, 51, 157, 52, 51, 52, 38, 63, 43, 64, 44, 43, 63, 37, 51, 38, 79, 164, 80, 44, 45, 41, 44, 41, 42, 29, 30, 161, 81, 80, 160, 78, 166, 79, 68, 80, 81, 82, 81, 160, 69, 79, 80, 69, 80, 68, 70, 78, 79, 46, 40, 41, 46, 41, 45, 47, 39, 40, 47, 48, 38, 48, 37, 38, 50, 51, 37, 51, 93, 143, 93, 92, 143, 62, 44, 63, 45, 44, 62, 67, 68, 81, 36, 50, 37, 47, 38, 39, 49, 36, 37, 48, 49, 37, 40, 46, 47, 51, 50, 93, 160, 91, 82, 67, 81, 82, 69, 70, 79, 59, 36, 49, 151, 92, 91, 66, 67, 82, 58, 93, 50, 58, 50, 36, 58, 36, 59, 8, 172, 64, 8, 64, 7, 63, 64, 172, 60, 48, 47, 49, 48, 60, 59, 49, 60, 61, 46, 45, 47, 46, 61, 60, 47, 61, 61, 45, 62, 11, 12, 9, 29, 166, 78, 77, 78, 70, 77, 29, 78, 9, 12, 183, 11, 9, 10, 13, 183, 12, 83, 82, 91, 66, 82, 83, 71, 77, 70, 65, 66, 83, 90, 83, 91, 65, 83, 90, 28, 29, 77, 28, 77, 71, 76, 67, 66, 76, 66, 65, 76, 65, 90, 84, 28, 71, 72, 71, 70, 84, 71, 72, 89, 76, 90, 75, 67, 76, 74, 73, 70, 72, 70, 73, 70, 69, 74, 74, 69, 68, 75, 68, 67, 68, 75, 74, 89, 88, 75, 89, 75, 76, 85, 84, 72, 85, 72, 73, 101, 93, 58, 85, 27, 28, 85, 28, 84, 91, 180, 90, 92, 180, 91, 89, 90, 180, 86, 85, 73, 88, 87, 74, 88, 74, 75, 86, 73, 74, 87, 86, 74, 174, 61, 62, 180, 181, 88, 180, 88, 89, 87, 88, 181, 14, 183, 13, 15, 183, 14, 140, 86, 87, 140, 87, 181, 101, 59, 60, 59, 101, 58, 174, 100, 101, 60, 174, 101, 174, 60, 61, 86, 26, 27, 86, 27, 85, 26, 86, 140, 180, 101, 99, 181, 180, 99, 92, 101, 180, 93, 101, 92, 172, 183, 171, 8, 183, 172, 9, 183, 8, 171, 183, 15, 175, 94, 100, 62, 63, 172, 174, 62, 172, 25, 26, 140, 171, 175, 174, 171, 174, 172, 175, 100, 174, 100, 99, 101, 95, 100, 94, 100, 96, 99, 95, 96, 100, 182, 181, 99, 99, 98, 182, 140, 181, 182, 141, 25, 140, 96, 97, 99, 97, 98, 99, 182, 141, 140, 16, 171, 15, 104, 96, 95, 173, 175, 171, 173, 171, 16, 105, 95, 94, 94, 106, 105, 104, 95, 105, 94, 175, 106, 138, 137, 175, 102, 98, 97, 175, 137, 106, 104, 103, 97, 104, 97, 96, 102, 97, 103, 107, 106, 137, 108, 182, 98, 108, 98, 102, 173, 138, 175, 119, 107, 137, 119, 137, 138, 17, 173, 16, 120, 107, 119, 121, 106, 107, 121, 107, 120, 118, 120, 119, 122, 105, 106, 122, 106, 121, 123, 104, 105, 123, 105, 122, 108, 141, 182, 141, 142, 24, 139, 119, 138, 118, 119, 139, 124, 104, 123, 103, 104, 124, 127, 123, 122, 126, 102, 103, 125, 126, 103, 108, 102, 126, 109, 108, 126, 124, 125, 103, 135, 122, 121, 127, 122, 135, 117, 121, 120, 117, 120, 118, 135, 121, 117, 127, 128, 124, 127, 124, 123, 125, 124, 128, 110, 109, 126, 142, 141, 109, 108, 109, 141, 25, 141, 24, 110, 142, 109, 129, 126, 125, 129, 125, 128, 126, 131, 110, 130, 126, 129, 130, 131, 126, 134, 127, 135, 128, 127, 134, 170, 138, 173, 139, 138, 170, 136, 131, 130, 111, 110, 131, 111, 131, 136, 116, 135, 117, 133, 128, 134, 129, 128, 133, 132, 130, 129, 132, 129, 133, 136, 130, 132, 112, 136, 132, 111, 136, 112, 116, 115, 134, 116, 134, 135, 133, 134, 115, 179, 111, 112, 142, 110, 111, 179, 142, 111, 113, 132, 133, 112, 132, 113, 114, 133, 115, 113, 133, 114, 18, 173, 17, 170, 173, 18, 168, 115, 116, 167, 113, 114, 167, 114, 115, 167, 115, 168, 169, 139, 170, 179, 24, 142, 23, 24, 179, 178, 113, 167, 179, 112, 113, 178, 179, 113, 116, 169, 168, 169, 177, 168, 167, 168, 177, 117, 118, 139, 117, 139, 169, 117, 169, 116, 176, 167, 177, 178, 167, 176, 18, 19, 169, 18, 169, 170, 177, 169, 19, 22, 178, 176, 23, 179, 178, 22, 23, 178, 20, 177, 19, 21, 176, 177, 21, 177, 20, 22, 176, 21], "vertices": [2, 6, 150.22, 39.39, 0.96968, 62, -241.26, 109.04, 0.03032, 2, 6, 149.36, 24.48, 0.96726, 62, -242.12, 94.14, 0.03274, 2, 6, 148.58, 11.03, 0.96806, 62, -242.9, 80.69, 0.03194, 2, 6, 147.8, -2.41, 0.97172, 62, -243.68, 67.24, 0.02828, 2, 6, 131.26, -12.74, 0.9721, 62, -260.23, 56.92, 0.0279, 2, 6, 114.19, -20.96, 0.97376, 62, -277.29, 48.69, 0.02624, 2, 6, 96.28, -26.08, 0.97498, 62, -295.2, 43.57, 0.02502, 2, 6, 78.38, -31.2, 0.97807, 62, -313.1, 38.46, 0.02193, 2, 6, 66.05, -36.65, 0.98168, 62, -325.43, 33, 0.01832, 2, 6, 62.86, -42.31, 0.98584, 62, -328.62, 27.35, 0.01416, 2, 6, 72.2, -58.49, 0.99135, 62, -319.28, 11.16, 0.00865, 2, 6, 67.88, -66.78, 0.9932, 62, -323.6, 2.88, 0.0068, 2, 6, 55.34, -71.27, 0.99387, 62, -336.14, -1.62, 0.00613, 2, 6, 36.67, -65.1, 0.99435, 62, -354.81, 4.56, 0.00565, 2, 6, 18.3, -45.78, 0.99087, 62, -373.18, 23.87, 0.00913, 2, 6, 20.8, -34.78, 0.98289, 62, -370.68, 34.87, 0.01711, 2, 6, 1.26, -28.72, 0.98164, 62, -390.23, 40.94, 0.01836, 2, 6, -8.68, -21.82, 0.97952, 62, -400.16, 47.83, 0.02048, 2, 6, -24.61, 2.6, 0.97292, 62, -416.09, 72.26, 0.02708, 2, 6, -34.49, 26.72, 0.96664, 62, -425.97, 96.37, 0.03336, 2, 6, -38.63, 42.56, 0.96698, 62, -430.11, 112.21, 0.03302, 2, 6, -37.07, 53.74, 0.96874, 62, -428.55, 123.4, 0.03126, 2, 6, -32.33, 61.41, 0.97392, 62, -423.81, 131.06, 0.02608, 2, 6, -13.49, 69.91, 0.97984, 62, -404.97, 139.57, 0.02016, 2, 6, 8.03, 80.35, 0.98565, 62, -383.45, 150, 0.01435, 2, 6, 35.5, 88.94, 0.98681, 62, -355.98, 158.59, 0.01319, 2, 6, 46.09, 89.39, 0.98674, 62, -345.4, 159.04, 0.01326, 2, 6, 59.61, 87.46, 0.989, 62, -331.87, 157.12, 0.011, 2, 6, 67.35, 87.25, 0.989, 62, -324.13, 156.91, 0.011, 2, 6, 77.5, 90.11, 0.98796, 62, -313.98, 159.76, 0.01204, 2, 6, 87.76, 89.3, 0.98822, 62, -303.72, 158.95, 0.01178, 2, 6, 92.78, 88.26, 0.98774, 62, -298.7, 157.91, 0.01226, 2, 6, 111.31, 83.32, 0.9873, 62, -280.17, 152.97, 0.0127, 2, 6, 129.54, 74.88, 0.9843, 62, -261.94, 144.53, 0.0157, 2, 6, 142.8, 65.23, 0.98391, 62, -248.68, 134.89, 0.01609, 2, 6, 151.08, 54.29, 0.97928, 62, -240.4, 123.95, 0.02072, 2, 6, 62.77, 32.49, 0.96405, 62, -328.71, 102.15, 0.03595, 2, 6, 66.75, 29.75, 0.9636, 62, -324.73, 99.4, 0.0364, 2, 6, 71.19, 25.46, 0.96289, 62, -320.29, 95.12, 0.03711, 2, 6, 73.69, 20.41, 0.96212, 62, -317.79, 90.06, 0.03788, 2, 6, 73.87, 15.29, 0.96186, 62, -317.61, 84.95, 0.03814, 2, 6, 72.67, 9.78, 0.96165, 62, -318.81, 79.44, 0.03835, 2, 6, 70.03, 5.76, 0.96152, 62, -321.45, 75.42, 0.03848, 2, 6, 66.75, 3.3, 0.96147, 62, -324.73, 72.95, 0.03853, 2, 6, 62.88, 6.05, 0.96158, 62, -328.6, 75.7, 0.03842, 2, 6, 60.28, 9.66, 0.96173, 62, -331.2, 79.31, 0.03827, 2, 6, 59.17, 14.77, 0.96196, 62, -332.31, 84.43, 0.03804, 2, 6, 59.66, 19.96, 0.96229, 62, -331.82, 89.62, 0.03771, 2, 6, 60.93, 24.87, 0.96298, 62, -330.55, 94.53, 0.03702, 2, 6, 61.91, 29.21, 0.96359, 62, -329.58, 98.86, 0.03641, 2, 6, 63.61, 34.04, 0.96426, 62, -327.87, 103.69, 0.03574, 2, 6, 68, 30.38, 0.96366, 62, -323.48, 100.04, 0.03634, 2, 6, 73.16, 26.18, 0.96294, 62, -318.32, 95.83, 0.03706, 2, 6, 76.32, 20.87, 0.96211, 62, -315.16, 90.53, 0.03789, 2, 6, 77.25, 15.08, 0.96179, 62, -314.23, 84.74, 0.03821, 2, 6, 75.84, 8.62, 0.96156, 62, -315.64, 78.28, 0.03844, 2, 6, 73.17, 3.02, 0.96183, 62, -318.31, 72.68, 0.03817, 2, 6, 69.51, -1.79, 0.96324, 62, -321.98, 67.86, 0.03676, 2, 6, 60.37, 35.08, 0.96442, 62, -331.11, 104.73, 0.03558, 2, 6, 60.82, 31.93, 0.96398, 62, -330.66, 101.59, 0.03602, 2, 6, 57.54, 24.89, 0.96305, 62, -333.94, 94.54, 0.03695, 2, 6, 55.18, 14.94, 0.96208, 62, -336.3, 84.59, 0.03792, 2, 6, 56.82, 6.21, 0.96169, 62, -334.66, 75.86, 0.03831, 2, 6, 60.62, 1.12, 0.96198, 62, -330.86, 70.77, 0.03802, 2, 6, 66.44, -2.09, 0.96316, 62, -325.04, 67.57, 0.03684, 2, 6, 64.09, 62.24, 0.96932, 62, -327.39, 131.9, 0.03068, 2, 6, 67.54, 63.96, 0.96968, 62, -323.94, 133.61, 0.03032, 2, 6, 70.83, 67.06, 0.97034, 62, -320.65, 136.72, 0.02966, 2, 6, 72.48, 70.69, 0.97118, 62, -319, 140.34, 0.02882, 2, 6, 73.34, 75.31, 0.97228, 62, -318.14, 144.96, 0.02772, 2, 6, 72.15, 79.24, 0.97423, 62, -319.33, 148.9, 0.02577, 2, 6, 68.98, 82.28, 0.97816, 62, -322.5, 151.94, 0.02184, 2, 6, 65.09, 82.5, 0.97861, 62, -326.39, 152.15, 0.02139, 2, 6, 60.89, 80.14, 0.97591, 62, -330.59, 149.8, 0.02409, 2, 6, 59.25, 74.82, 0.97245, 62, -332.23, 144.48, 0.02755, 2, 6, 60.04, 69.42, 0.97113, 62, -331.44, 139.07, 0.02887, 2, 6, 61.88, 65.33, 0.97011, 62, -329.6, 134.98, 0.02989, 2, 6, 71.23, 84.94, 0.98233, 62, -320.25, 154.59, 0.01767, 2, 6, 74.88, 80.69, 0.97583, 62, -316.61, 150.34, 0.02417, 2, 6, 75.98, 75.76, 0.97231, 62, -315.5, 145.41, 0.02769, 2, 6, 74.59, 70.29, 0.97103, 62, -316.89, 139.94, 0.02897, 2, 6, 71.93, 65.96, 0.97004, 62, -319.55, 135.61, 0.02996, 2, 6, 68.73, 62.79, 0.96937, 62, -322.75, 132.45, 0.03063, 2, 6, 65.28, 60.08, 0.96878, 62, -326.2, 129.74, 0.03122, 2, 6, 65.87, 84.47, 0.98149, 62, -325.61, 154.12, 0.01851, 2, 6, 61.37, 84.63, 0.98173, 62, -330.11, 154.29, 0.01827, 2, 6, 57.4, 82.15, 0.97865, 62, -334.08, 151.8, 0.02135, 2, 6, 55.55, 74.71, 0.9724, 62, -335.93, 144.36, 0.0276, 2, 6, 57.7, 68.41, 0.97079, 62, -333.78, 138.07, 0.02921, 2, 6, 59.84, 64.39, 0.96987, 62, -331.64, 134.04, 0.03013, 2, 6, 61.81, 60.8, 0.96901, 62, -329.67, 130.45, 0.03099, 2, 6, 66.55, 58.26, 0.96726, 62, -324.93, 127.92, 0.03274, 2, 6, 67.39, 48.82, 0.96507, 62, -324.09, 118.47, 0.03493, 2, 6, 64.18, 40.17, 0.96512, 62, -327.3, 109.82, 0.03488, 2, 6, 26.6, 39.99, 0.96294, 62, -364.88, 109.65, 0.03706, 2, 6, 22.75, 46.02, 0.96325, 62, -368.73, 115.68, 0.03675, 2, 6, 23.97, 52.94, 0.96434, 62, -367.52, 122.59, 0.03566, 2, 6, 23.39, 58.37, 0.96583, 62, -368.09, 128.03, 0.03417, 2, 6, 24.57, 63.78, 0.96757, 62, -366.91, 133.44, 0.03243, 2, 6, 38.77, 62.35, 0.96797, 62, -352.71, 132.01, 0.03203, 2, 6, 35.73, 39.98, 0.96356, 62, -355.75, 109.64, 0.03644, 2, 6, 49.95, 40.08, 0.96434, 62, -341.53, 109.73, 0.03566, 2, 6, 15.3, 64.13, 0.96634, 62, -376.18, 133.78, 0.03366, 2, 6, 12.52, 59.3, 0.96484, 62, -378.96, 128.96, 0.03516, 2, 6, 14.53, 51.54, 0.96333, 62, -376.95, 121.2, 0.03667, 2, 6, 12.21, 44.48, 0.96247, 62, -379.27, 114.13, 0.03753, 2, 6, 8.51, 36.97, 0.96225, 62, -382.97, 106.63, 0.03775, 2, 6, 5.68, 31.32, 0.9621, 62, -385.8, 100.97, 0.0379, 2, 6, 14.09, 67.8, 0.96694, 62, -377.39, 137.46, 0.03306, 2, 6, 9.84, 69.73, 0.96868, 62, -381.64, 139.38, 0.03132, 2, 6, 5.45, 69.08, 0.96895, 62, -386.03, 138.73, 0.03105, 2, 6, 0.75, 67.41, 0.96917, 62, -390.73, 137.06, 0.03083, 2, 6, -4.57, 65.75, 0.96905, 62, -396.05, 135.41, 0.03095, 2, 6, -9.04, 61.13, 0.96698, 62, -400.52, 130.78, 0.03302, 2, 6, -10.6, 54.34, 0.96514, 62, -402.08, 124, 0.03486, 2, 6, -9.51, 46.57, 0.96436, 62, -400.99, 116.22, 0.03564, 2, 6, -6.33, 38.92, 0.9633, 62, -397.81, 108.57, 0.0367, 2, 6, -2.07, 33.68, 0.96261, 62, -393.55, 103.34, 0.03739, 2, 6, 2.06, 29.84, 0.96218, 62, -389.42, 99.5, 0.03782, 2, 6, 3.75, 27.93, 0.96201, 62, -387.73, 97.59, 0.03799, 2, 6, 3.46, 30.96, 0.96216, 62, -388.02, 100.62, 0.03784, 2, 6, 3.62, 35.26, 0.96236, 62, -387.86, 104.91, 0.03764, 2, 6, 4.13, 41.45, 0.96265, 62, -387.35, 111.1, 0.03735, 2, 6, 5.23, 47.14, 0.96228, 62, -386.25, 116.79, 0.03772, 2, 6, 5.67, 53.42, 0.96312, 62, -385.81, 123.07, 0.03688, 2, 6, 4.99, 58.05, 0.96454, 62, -386.49, 127.71, 0.03546, 2, 6, 5.96, 62.24, 0.96593, 62, -385.52, 131.89, 0.03407, 2, 6, 2.86, 47.15, 0.96251, 62, -388.62, 116.8, 0.03749, 2, 6, 2.17, 53.69, 0.96348, 62, -389.31, 123.35, 0.03652, 2, 6, 2.64, 59.36, 0.96507, 62, -388.84, 129.01, 0.03493, 2, 6, 3.35, 63.97, 0.96701, 62, -388.13, 133.62, 0.03299, 2, 6, 3.38, 66.59, 0.96841, 62, -388.1, 136.25, 0.03159, 2, 6, -1.94, 60.8, 0.96491, 62, -393.42, 130.46, 0.03509, 2, 6, -2.84, 54.81, 0.96311, 62, -394.32, 124.47, 0.03689, 2, 6, -2.12, 47, 0.96248, 62, -393.6, 116.65, 0.03752, 2, 6, -0.33, 40.72, 0.96193, 62, -391.81, 110.38, 0.03807, 2, 6, 0.5, 65.09, 0.96705, 62, -390.98, 134.75, 0.03295, 2, 6, 7.34, 26.67, 0.96183, 62, -384.14, 96.33, 0.03817, 2, 6, 3.49, 24.64, 0.96187, 62, -387.99, 94.29, 0.03813, 2, 6, -0.07, 26.97, 0.96215, 62, -391.56, 96.62, 0.03785, 2, 6, 46.2, 80.93, 0.9775, 62, -345.29, 150.59, 0.0225, 2, 6, 26.4, 80.1, 0.97713, 62, -365.08, 149.75, 0.02287, 2, 6, 6.19, 73.69, 0.97437, 62, -385.29, 143.35, 0.02563, 4, 34, 45.19, 26.26, 0.00615, 35, 39.45, 6.69, 0.00054, 36, 16.11, 4.41, 0.98854, 63, -254.86, 113.94, 0.00477, 4, 34, 16.47, 4.62, 0.20342, 35, 3.67, 3.18, 0.79405, 36, -19.81, 6.09, 0.00228, 63, -242.14, 80.3, 0.00025, 4, 34, 10.11, 2.5, 0.91688, 35, -2.87, 4.68, 0.08027, 36, -26.06, 8.52, 0.00169, 63, -241.89, 73.6, 0.00115, 4, 34, -2.18, 2.6, 0.98366, 35, -13.31, 11.17, 0.00814, 36, -35.45, 16.44, 0.00366, 63, -245.43, 61.83, 0.00454, 3, 34, -1.86, -0.62, 0.99477, 36, -37.26, 13.75, 0.00028, 63, -242.25, 61.24, 0.00495, 2, 34, 9.49, -3.21, 0.99811, 63, -236.59, 71.41, 0.00189, 3, 34, 19.83, -2.2, 0.14176, 35, 2.98, -4.4, 0.85801, 63, -234.65, 81.61, 0.00023, 3, 35, 39.69, -3.22, 0.02409, 36, 14.92, -5.42, 0.97051, 63, -245.36, 116.74, 0.0054, 4, 34, 49.37, 24.02, 0.00019, 35, 41.85, 2.6, 6e-05, 36, 17.9, 0.02, 0.99414, 63, -251.54, 117.32, 0.00561, 3, 35, 21.51, -4.74, 0.80728, 36, -3.29, -4.32, 0.19112, 63, -239.15, 99.59, 0.00159, 4, 34, 30.58, 15.02, 0.0187, 35, 21.13, 4.7, 0.77218, 36, -2.31, 5.09, 0.20792, 63, -248.17, 96.76, 0.00121, 3, 34, 23.96, 9.24, 0.02616, 35, 12.47, 3.21, 0.96704, 36, -11.09, 4.86, 0.0068, 3, 35, 12.26, -5.12, 0.98928, 36, -12.49, -3.35, 0.0106, 63, -236.38, 90.76, 0.00013, 3, 35, 30.62, -5.18, 0.14218, 36, 5.66, -6.05, 0.85423, 63, -241.11, 108.5, 0.00359, 4, 34, 37.98, 21.39, 0.01168, 35, 30.76, 6.28, 0.05025, 36, 7.45, 5.27, 0.93497, 63, -252.21, 105.65, 0.00309, 3, 38, 5.99, 5.64, 0.91538, 37, 17.97, 5.49, 0.0775, 62, -304.34, 134.24, 0.00712, 2, 38, 11.01, 1.28, 0.99177, 62, -310.72, 132.4, 0.00823, 3, 38, 11.73, -2.11, 0.9887, 37, 23.5, -2.41, 0.00405, 62, -313.96, 133.64, 0.00725, 3, 38, -11.23, -4.6, 0.02715, 37, 0.49, -4.31, 0.96311, 63, -246.42, 154.26, 0.00974, 2, 37, -2.8, 2.51, 0.98846, 63, -238.88, 153.52, 0.01154, 3, 38, -1.39, 3.93, 0.32905, 37, 10.54, 3.96, 0.66533, 62, -301.77, 141.37, 0.00562, 3, 38, 2.87, -3.73, 0.84354, 37, 14.6, -3.8, 0.1508, 62, -310.51, 141.95, 0.00566, 3, 38, -9.46, 3.95, 0.01454, 37, 2.48, 4.19, 0.98276, 62, -297.37, 148.14, 0.0027, 3, 38, -5.21, -3.85, 0.10031, 37, 6.53, -3.71, 0.89664, 62, -306.23, 148.8, 0.00305, 2, 6, -15.72, 56.09, 0.9668, 62, -407.2, 125.74, 0.0332, 2, 6, -15.54, 42.36, 0.96443, 62, -407.02, 112.01, 0.03557, 2, 6, -21.97, 29.04, 0.96336, 62, -413.45, 98.69, 0.03664, 2, 6, -11.01, 11.54, 0.96379, 62, -402.49, 81.19, 0.03621, 2, 6, 20.57, -11.09, 0.96611, 62, -370.91, 58.56, 0.03389, 2, 6, 49.04, -12.4, 0.96443, 62, -342.44, 57.26, 0.03557, 2, 6, 2.09, -1.19, 0.96572, 62, -389.39, 68.47, 0.03428, 2, 6, 41.64, 15.4, 0.96186, 62, -349.84, 85.06, 0.03814, 2, 6, 17.39, 20.55, 0.96148, 62, -374.09, 90.21, 0.03852, 2, 6, -24.4, 56.73, 0.96607, 62, -415.88, 126.38, 0.03393, 2, 6, -25.32, 42.68, 0.9636, 62, -416.8, 112.34, 0.0364, 2, 6, -18.23, 61.73, 0.96917, 62, -409.71, 131.38, 0.03083, 2, 6, -6.51, 68.29, 0.97199, 62, -397.99, 137.95, 0.02801, 2, 6, 53.07, 60.31, 0.96823, 62, -338.41, 129.96, 0.03177, 2, 6, 46.46, 70.01, 0.97052, 62, -345.02, 139.66, 0.02948, 2, 6, 27.8, 70.88, 0.96997, 62, -363.68, 140.53, 0.03003, 2, 6, 42.62, -38.68, 0.98367, 62, -348.86, 30.97, 0.01633, 2, 6, 110.85, 46.31, 0.96599, 62, -280.63, 115.96, 0.03401, 2, 6, 108.92, 64.18, 0.97006, 62, -282.56, 133.84, 0.02994, 2, 6, 130.95, 59.47, 0.97081, 62, -260.53, 129.13, 0.02919, 2, 6, 132.56, 42.59, 0.96744, 62, -258.92, 112.24, 0.03256, 2, 6, 132.37, 3.55, 0.96686, 62, -259.11, 73.21, 0.03314, 2, 6, 113.31, -4.34, 0.96559, 62, -278.18, 65.31, 0.03441, 2, 6, 115.15, 18.47, 0.96302, 62, -276.33, 88.13, 0.03698, 2, 6, 134.71, 20.08, 0.96477, 62, -256.77, 89.73, 0.03523], "hull": 36, "edges": [6, 8, 18, 20, 20, 22, 22, 24, 24, 26, 38, 40, 48, 50, 68, 70, 30, 32, 32, 34, 26, 28, 28, 30, 34, 36, 36, 38, 40, 42, 46, 48, 42, 44, 44, 46, 14, 16, 16, 18, 8, 10, 64, 66, 66, 68, 62, 64, 50, 52, 52, 54, 54, 56, 10, 12, 12, 14, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 72, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 130, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 188, 190, 190, 192, 192, 194, 194, 196, 182, 184, 184, 186, 198, 196, 200, 188, 186, 202, 202, 200, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 204, 216, 216, 218, 218, 220, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 244, 254, 254, 256, 256, 258, 258, 260, 252, 260, 260, 262, 264, 266, 266, 268, 268, 270, 264, 272, 274, 276, 276, 278, 280, 282, 282, 284, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 300, 302, 302, 286, 288, 308, 308, 306, 298, 310, 310, 304, 300, 312, 312, 304, 286, 314, 314, 306, 316, 318, 318, 320, 58, 60, 60, 62, 56, 58, 316, 326, 320, 328, 324, 330, 330, 326, 322, 332, 332, 328, 334, 336, 338, 340, 342, 344, 346, 342, 346, 340, 348, 350, 352, 354, 354, 338, 356, 358, 182, 360, 360, 198, 362, 364, 284, 358, 0, 70, 0, 2, 2, 4, 4, 6], "width": 165, "height": 194}}, "head2": {"head": {"type": "mesh", "uvs": [0.1428, 0.70533, 0.19534, 0.68763, 0.24164, 0.69562, 0.27519, 0.6953, 0.3065, 0.68611, 0.349, 0.68421, 0.37937, 0.65786, 0.5067, 0.68165, 0.65594, 0.73333, 0.66989, 0.8718, 0.54092, 0.94934, 0.45301, 0.98848, 0.38483, 0.99363, 0.33313, 0.97867, 0.25724, 0.89388, 0.16632, 0.79788, 0.2522, 0.74259, 0.28451, 0.75099, 0.32759, 0.7319, 0.37247, 0.73534, 0.42184, 0.74526, 0.45909, 0.7529, 0.23215, 0.7529, 0.22659, 0.7765, 0.23639, 0.7978, 0.25265, 0.81949, 0.26967, 0.8443, 0.30307, 0.86136, 0.34526, 0.86136, 0.38969, 0.84686, 0.43053, 0.82205, 0.45566, 0.79456, 0.47271, 0.76937, 0.48169, 0.75869, 0.46419, 0.76365, 0.43861, 0.76785, 0.40134, 0.77243, 0.36627, 0.7735, 0.32858, 0.77853, 0.30212, 0.78732, 0.27608, 0.78731, 0.36942, 0.7854, 0.33172, 0.79647, 0.29762, 0.80067, 0.26942, 0.80243, 0.2539, 0.80532, 0.29532, 0.82533, 0.33192, 0.8229, 0.3771, 0.81025, 0.41172, 0.79397, 0.26666, 0.81806, 0.48426, 0.73922, 0.50152, 0.7562, 0.49261, 0.77679, 0.20813, 0.79944, 0.34193, 0.88911, 0.42276, 0.87231, 0.5102, 0.88917, 0.59863, 0.81385, 0.34996, 0.93343, 0.43416, 0.92177, 0.31204, 0.90824, 0.2573, 0.85694], "triangles": [18, 4, 5, 19, 5, 6, 6, 20, 19, 18, 5, 19, 6, 7, 20, 52, 51, 7, 16, 2, 3, 7, 51, 20, 18, 17, 3, 18, 3, 4, 16, 3, 17, 21, 20, 51, 22, 1, 2, 22, 2, 16, 8, 52, 7, 33, 21, 51, 33, 51, 52, 34, 21, 33, 35, 20, 21, 35, 21, 34, 32, 34, 33, 36, 19, 20, 36, 20, 35, 37, 18, 19, 37, 19, 36, 22, 0, 1, 0, 54, 15, 53, 33, 52, 32, 33, 53, 38, 18, 37, 17, 18, 38, 41, 37, 36, 40, 16, 17, 39, 40, 17, 22, 16, 40, 23, 22, 40, 38, 39, 17, 49, 36, 35, 41, 36, 49, 31, 35, 34, 31, 34, 32, 49, 35, 31, 41, 42, 38, 41, 38, 37, 39, 38, 42, 24, 23, 40, 54, 0, 23, 22, 23, 0, 24, 54, 23, 43, 40, 39, 43, 39, 42, 40, 45, 24, 44, 40, 43, 44, 45, 40, 48, 41, 49, 42, 41, 48, 58, 52, 8, 53, 52, 58, 50, 45, 44, 25, 24, 45, 25, 45, 50, 30, 49, 31, 47, 42, 48, 43, 42, 47, 46, 44, 43, 46, 43, 47, 50, 44, 46, 26, 50, 46, 25, 50, 26, 30, 29, 48, 30, 48, 49, 47, 48, 29, 62, 25, 26, 54, 24, 25, 62, 54, 25, 27, 46, 47, 26, 46, 27, 28, 47, 29, 27, 47, 28, 58, 8, 9, 56, 29, 30, 55, 27, 28, 55, 28, 29, 55, 29, 56, 57, 53, 58, 62, 15, 54, 14, 15, 62, 61, 27, 55, 62, 26, 27, 61, 62, 27, 30, 57, 56, 57, 60, 56, 55, 56, 60, 31, 32, 53, 31, 53, 57, 31, 57, 30, 59, 55, 60, 61, 55, 59, 9, 10, 57, 9, 57, 58, 60, 57, 10, 13, 61, 59, 14, 62, 61, 13, 14, 61, 11, 60, 10, 12, 59, 60, 12, 60, 11, 13, 59, 12], "vertices": [2, 6, 26.4, 80.1, 0.97713, 62, -365.08, 149.75, 0.02287, 2, 6, 27.8, 70.88, 0.96997, 62, -363.68, 140.53, 0.03003, 2, 6, 24.57, 63.78, 0.96757, 62, -366.91, 133.44, 0.03243, 2, 6, 23.39, 58.37, 0.96583, 62, -368.09, 128.03, 0.03417, 2, 6, 23.97, 52.94, 0.96434, 62, -367.52, 122.59, 0.03566, 2, 6, 22.75, 46.02, 0.96325, 62, -368.73, 115.68, 0.03675, 2, 6, 26.6, 39.99, 0.96294, 62, -364.88, 109.65, 0.03706, 2, 6, 17.39, 20.55, 0.96148, 62, -374.09, 90.21, 0.03852, 2, 6, 2.09, -1.19, 0.96572, 62, -389.39, 68.47, 0.03428, 2, 6, -24.61, 2.6, 0.97292, 62, -416.09, 72.26, 0.02708, 2, 6, -34.49, 26.72, 0.96664, 62, -425.97, 96.37, 0.03336, 2, 6, -38.63, 42.56, 0.96698, 62, -430.11, 112.21, 0.03302, 2, 6, -37.07, 53.74, 0.96874, 62, -428.55, 123.4, 0.03126, 2, 6, -32.33, 61.41, 0.97392, 62, -423.81, 131.06, 0.02608, 2, 6, -13.49, 69.91, 0.97984, 62, -404.97, 139.57, 0.02016, 2, 6, 8.03, 80.35, 0.98565, 62, -383.45, 150, 0.01435, 2, 6, 15.3, 64.13, 0.96634, 62, -376.18, 133.78, 0.03366, 2, 6, 12.52, 59.3, 0.96484, 62, -378.96, 128.96, 0.03516, 2, 6, 14.53, 51.54, 0.96333, 62, -376.95, 121.2, 0.03667, 2, 6, 12.21, 44.48, 0.96247, 62, -379.27, 114.13, 0.03753, 2, 6, 8.51, 36.97, 0.96225, 62, -382.97, 106.63, 0.03775, 2, 6, 5.68, 31.32, 0.9621, 62, -385.8, 100.97, 0.0379, 2, 6, 14.09, 67.8, 0.96694, 62, -377.39, 137.46, 0.03306, 2, 6, 9.84, 69.73, 0.96868, 62, -381.64, 139.38, 0.03132, 2, 6, 5.45, 69.08, 0.96895, 62, -386.03, 138.73, 0.03105, 2, 6, 0.75, 67.41, 0.96917, 62, -390.73, 137.06, 0.03083, 2, 6, -4.57, 65.75, 0.96905, 62, -396.05, 135.41, 0.03095, 2, 6, -9.04, 61.13, 0.96698, 62, -400.52, 130.78, 0.03302, 2, 6, -10.6, 54.34, 0.96514, 62, -402.08, 124, 0.03486, 2, 6, -9.51, 46.57, 0.96436, 62, -400.99, 116.22, 0.03564, 2, 6, -6.33, 38.92, 0.9633, 62, -397.81, 108.57, 0.0367, 2, 6, -2.07, 33.68, 0.96261, 62, -393.55, 103.34, 0.03739, 2, 6, 2.06, 29.84, 0.96218, 62, -389.42, 99.5, 0.03782, 2, 6, 3.75, 27.93, 0.96201, 62, -387.73, 97.59, 0.03799, 2, 6, 3.46, 30.96, 0.96216, 62, -388.02, 100.62, 0.03784, 2, 6, 3.62, 35.26, 0.96236, 62, -387.86, 104.91, 0.03764, 2, 6, 4.13, 41.45, 0.96265, 62, -387.35, 111.1, 0.03735, 2, 6, 5.23, 47.14, 0.96228, 62, -386.25, 116.79, 0.03772, 2, 6, 5.67, 53.42, 0.96312, 62, -385.81, 123.07, 0.03688, 2, 6, 4.99, 58.05, 0.96454, 62, -386.49, 127.71, 0.03546, 2, 6, 5.96, 62.24, 0.96593, 62, -385.52, 131.89, 0.03407, 2, 6, 2.86, 47.15, 0.96251, 62, -388.62, 116.8, 0.03749, 2, 6, 2.17, 53.69, 0.96348, 62, -389.31, 123.35, 0.03652, 2, 6, 2.64, 59.36, 0.96507, 62, -388.84, 129.01, 0.03493, 2, 6, 3.35, 63.97, 0.96701, 62, -388.13, 133.62, 0.03299, 2, 6, 3.38, 66.59, 0.96841, 62, -388.1, 136.25, 0.03159, 2, 6, -1.94, 60.8, 0.96491, 62, -393.42, 130.46, 0.03509, 2, 6, -2.84, 54.81, 0.96311, 62, -394.32, 124.47, 0.03689, 2, 6, -2.12, 47, 0.96248, 62, -393.6, 116.65, 0.03752, 2, 6, -0.33, 40.72, 0.96193, 62, -391.81, 110.38, 0.03807, 2, 6, 0.5, 65.09, 0.96705, 62, -390.98, 134.75, 0.03295, 2, 6, 7.34, 26.67, 0.96183, 62, -384.14, 96.33, 0.03817, 2, 6, 3.49, 24.64, 0.96187, 62, -387.99, 94.29, 0.03813, 2, 6, -0.07, 26.97, 0.96215, 62, -391.56, 96.62, 0.03785, 2, 6, 6.19, 73.69, 0.97437, 62, -385.29, 143.35, 0.02563, 2, 6, -15.72, 56.09, 0.9668, 62, -407.2, 125.74, 0.0332, 2, 6, -15.54, 42.36, 0.96443, 62, -407.02, 112.01, 0.03557, 2, 6, -21.97, 29.04, 0.96336, 62, -413.45, 98.69, 0.03664, 2, 6, -11.01, 11.54, 0.96379, 62, -402.49, 81.19, 0.03621, 2, 6, -24.4, 56.73, 0.96607, 62, -415.88, 126.38, 0.03393, 2, 6, -25.32, 42.68, 0.9636, 62, -416.8, 112.34, 0.0364, 2, 6, -18.23, 61.73, 0.96917, 62, -409.71, 131.38, 0.03083, 2, 6, -6.51, 68.29, 0.97199, 62, -397.99, 137.95, 0.02801], "hull": 16, "edges": [20, 22, 18, 20, 22, 24, 28, 30, 24, 26, 26, 28, 12, 10, 10, 8, 8, 6, 6, 4, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 32, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 72, 82, 82, 84, 84, 86, 86, 88, 80, 88, 88, 90, 92, 94, 94, 96, 96, 98, 92, 100, 102, 104, 104, 106, 0, 108, 110, 112, 114, 116, 16, 116, 118, 120, 120, 114, 122, 124, 108, 124, 30, 0, 0, 2, 12, 14, 14, 16, 16, 18, 4, 2], "width": 165, "height": 194}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.72854, 0.00619, 0.79949, 0.01921, 0.85488, 0.0316, 0.86849, 0.06716, 0.86905, 0.09669, 0.85665, 0.12763, 0.83288, 0.15863, 0.79456, 0.19117, 0.75418, 0.21615, 0.6513, 0.25536, 0.61103, 0.27422, 0.50542, 0.33027, 0.41212, 0.36395, 0.32977, 0.39108, 0.27985, 0.40996, 0.24821, 0.42355, 0.22997, 0.43467, 0.2635, 0.43777, 0.3047, 0.44748, 0.35123, 0.46231, 0.41347, 0.49294, 0.61323, 0.65813, 0.66057, 0.6905, 0.68547, 0.7041, 0.70679, 0.71176, 0.73012, 0.71468, 0.73922, 0.70993, 0.77213, 0.70604, 0.8183, 0.70623, 0.86494, 0.72181, 0.89952, 0.76151, 0.9234, 0.78354, 0.99828, 0.8329, 0.99853, 0.8448, 0.97841, 0.84529, 0.88265, 0.79372, 0.86222, 0.80755, 0.87266, 0.83089, 0.88774, 0.85325, 0.90298, 0.87803, 0.90943, 0.89905, 0.89785, 0.91969, 0.87694, 0.93629, 0.84271, 0.95656, 0.75745, 0.99907, 0.74138, 0.99903, 0.73034, 0.9913, 0.76994, 0.93913, 0.77944, 0.92345, 0.78862, 0.90292, 0.77889, 0.89127, 0.75657, 0.87436, 0.73911, 0.86201, 0.70677, 0.83729, 0.67576, 0.80974, 0.65094, 0.79485, 0.62904, 0.77639, 0.6068, 0.7587, 0.57927, 0.74138, 0.4915, 0.69314, 0.2775, 0.5994, 0.15797, 0.54033, 0.12303, 0.52732, 0.08757, 0.51745, 0.05131, 0.50437, 0.02449, 0.48747, 0.00566, 0.46194, 0.00031, 0.44275, 0.01196, 0.41482, 0.03312, 0.38656, 0.07305, 0.34313, 0.14637, 0.27215, 0.2385, 0.19674, 0.32999, 0.13635, 0.40611, 0.09557, 0.45949, 0.07018, 0.50447, 0.05043, 0.55693, 0.03631, 0.58239, 0.00543, 0.65263, 0.00047, 0.11288, 0.36494, 0.07945, 0.39269, 0.04636, 0.42227, 0.04394, 0.44666, 0.05605, 0.47313, 0.08267, 0.49077, 0.12705, 0.50375, 0.18192, 0.52139, 0.27976, 0.56826, 0.5073, 0.65337, 0.34189, 0.51947, 0.25994, 0.4776, 0.19459, 0.46203, 0.15182, 0.45424, 0.13649, 0.43712, 0.14375, 0.41584, 0.1849, 0.39508, 0.18654, 0.43874, 0.22636, 0.37642, 0.15974, 0.32504, 0.40312, 0.3114, 0.51794, 0.25601, 0.58765, 0.21293, 0.69253, 0.17517, 0.74888, 0.13881, 0.7754, 0.09798, 0.78859, 0.05204, 0.30702, 0.22646, 0.39953, 0.17131, 0.48076, 0.12704, 0.53604, 0.10092, 0.61164, 0.07987, 0.65451, 0.04432], "triangles": [5, 105, 4, 106, 1, 2, 106, 2, 3, 105, 3, 4, 105, 106, 3, 43, 44, 47, 47, 44, 46, 44, 45, 46, 47, 48, 43, 43, 48, 42, 48, 49, 42, 42, 49, 41, 41, 49, 40, 49, 39, 40, 39, 49, 38, 49, 50, 38, 38, 50, 37, 50, 51, 37, 51, 52, 37, 52, 36, 37, 52, 53, 36, 35, 31, 34, 34, 32, 33, 34, 31, 32, 36, 54, 25, 36, 25, 27, 36, 27, 28, 27, 25, 26, 36, 53, 54, 54, 55, 25, 36, 29, 35, 36, 28, 29, 55, 24, 25, 35, 30, 31, 35, 29, 30, 55, 56, 24, 56, 23, 24, 56, 57, 23, 57, 22, 23, 57, 58, 22, 58, 59, 21, 58, 21, 22, 21, 59, 89, 59, 60, 89, 90, 89, 88, 89, 60, 88, 90, 20, 89, 89, 20, 21, 60, 61, 88, 88, 61, 87, 87, 91, 88, 88, 91, 90, 61, 62, 87, 62, 86, 87, 62, 63, 86, 87, 86, 92, 87, 92, 91, 92, 86, 93, 90, 91, 19, 90, 19, 20, 19, 91, 18, 64, 85, 63, 63, 85, 86, 64, 84, 85, 64, 65, 84, 86, 85, 93, 85, 84, 93, 65, 66, 84, 92, 16, 91, 91, 17, 18, 91, 16, 17, 66, 83, 84, 84, 94, 93, 84, 83, 94, 93, 97, 92, 92, 97, 16, 105, 112, 106, 66, 67, 83, 93, 94, 97, 83, 82, 94, 83, 67, 82, 67, 68, 82, 96, 16, 97, 82, 81, 94, 97, 94, 95, 94, 81, 95, 97, 95, 96, 16, 96, 15, 96, 98, 15, 15, 98, 14, 68, 69, 82, 82, 69, 81, 81, 80, 95, 95, 80, 96, 14, 98, 13, 98, 96, 99, 80, 81, 70, 12, 13, 100, 81, 69, 70, 96, 80, 99, 13, 98, 100, 98, 99, 100, 80, 70, 99, 12, 100, 11, 70, 71, 99, 11, 101, 10, 11, 100, 101, 99, 107, 100, 107, 71, 72, 107, 99, 71, 100, 107, 101, 101, 102, 10, 10, 102, 9, 107, 108, 101, 101, 108, 102, 9, 103, 8, 9, 102, 103, 107, 73, 108, 107, 72, 73, 8, 103, 7, 108, 109, 102, 109, 110, 102, 103, 110, 111, 103, 102, 110, 103, 104, 7, 7, 104, 6, 103, 111, 104, 108, 74, 109, 108, 73, 74, 6, 104, 5, 104, 111, 105, 104, 105, 5, 105, 111, 112, 74, 75, 109, 109, 75, 110, 75, 76, 110, 110, 77, 111, 110, 76, 77, 111, 77, 112, 106, 112, 0, 106, 0, 1, 112, 79, 0, 78, 79, 112, 77, 78, 112], "vertices": [3, 11, -5.68, 17.4, 0.7085, 7, 112.59, 74.61, 0.2865, 57, -185.51, -49.47, 0.005, 4, 11, 16.45, 50.44, 0.43883, 12, -53.02, 25.41, 0.1194, 7, 134.72, 107.65, 0.43677, 57, -147.27, -60.37, 0.005, 3, 11, 35.5, 75.66, 0.11665, 12, -66.58, 53.96, 0.15, 7, 153.78, 132.87, 0.73335, 3, 11, 66.11, 73.49, 0.15436, 12, -50.51, 80.11, 0.1285, 7, 184.39, 130.69, 0.71714, 3, 11, 89.76, 66.17, 0.16416, 12, -33.1, 97.7, 0.1956, 7, 208.03, 123.38, 0.64024, 3, 11, 112.38, 51.84, 0.112, 12, -9.93, 111.14, 0.30741, 7, 230.66, 109.04, 0.58058, 3, 11, 133.16, 31.66, 0.021, 12, 17.56, 120.25, 0.51308, 7, 251.44, 88.87, 0.46592, 3, 11, 152.76, 3.63, 0.00103, 12, 51.48, 124.69, 0.62789, 7, 271.04, 60.83, 0.37108, 2, 12, 81.66, 123.88, 0.74522, 7, 284.27, 33.69, 0.25478, 2, 12, 143.99, 107.46, 0.87153, 7, 298.5, -29.18, 0.12847, 2, 12, 170.49, 103.09, 0.97458, 7, 306.87, -54.69, 0.02542, 1, 12, 243.89, 95.52, 1, 1, 12, 299.3, 79.53, 1, 2, 12, 346.65, 63.88, 0.99278, 13, 78.77, 121.27, 0.00722, 2, 12, 376.81, 55.82, 0.9359, 13, 67.21, 92.27, 0.0641, 2, 12, 396.89, 51.67, 0.78445, 13, 60.71, 72.83, 0.21555, 2, 12, 410.43, 51.21, 0.53329, 13, 58.65, 59.44, 0.46671, 2, 12, 399.59, 65.9, 0.25712, 13, 74.52, 68.46, 0.74288, 2, 12, 389.79, 87.42, 0.10791, 13, 97.05, 75.64, 0.89209, 2, 12, 381.04, 114.02, 0.03341, 13, 124.49, 81.19, 0.96659, 2, 12, 375.77, 155.92, 0.00175, 13, 166.73, 81.46, 0.99825, 1, 13, 336.66, 38.06, 1, 2, 13, 373.44, 32.27, 0.9998, 14, -53.57, 16.09, 0.0002, 2, 13, 391.03, 31.48, 0.94629, 14, -36.65, 20.93, 0.05371, 2, 13, 404.04, 33.45, 0.74978, 14, -24.93, 26.93, 0.25022, 2, 13, 415.48, 39.22, 0.43954, 14, -15.91, 36.03, 0.56046, 2, 13, 416.92, 45.38, 0.34585, 14, -16.51, 42.33, 0.65415, 2, 13, 428.94, 58.83, 0.16098, 14, -9.38, 58.9, 0.83902, 2, 13, 448.7, 73.95, 0.05124, 14, 4.55, 79.51, 0.94876, 2, 13, 476.57, 79.04, 0.00909, 14, 29.37, 93.19, 0.99091, 1, 14, 67.39, 90.24, 1, 1, 14, 89.89, 90.7, 1, 1, 14, 146.69, 101.34, 1, 1, 14, 155.06, 95.92, 1, 1, 14, 149.39, 86.67, 1, 1, 14, 84.8, 67.7, 1, 1, 14, 88.33, 52.11, 1, 1, 14, 107.73, 45.94, 1, 2, 14, 127.83, 42.31, 0.99932, 15, -48.09, -5.98, 0.00068, 2, 14, 149.67, 37.62, 0.88327, 15, -35.53, 12.5, 0.11673, 2, 14, 166.25, 30.74, 0.65333, 15, -22.92, 25.27, 0.34667, 2, 14, 177.17, 15.96, 0.3486, 15, -5.11, 29.84, 0.6514, 2, 14, 182.5, -1.14, 0.02442, 15, 12.73, 28.34, 0.97558, 1, 15, 37.16, 22.64, 1, 1, 15, 92.45, 4.67, 1, 1, 15, 97.3, -2.52, 1, 1, 15, 95.28, -11.09, 1, 2, 14, 152.49, -50.44, 0.00824, 15, 47.13, -18.01, 0.99176, 2, 14, 144.39, -38.9, 0.09481, 15, 33.39, -21.17, 0.90519, 2, 14, 132.82, -25.24, 0.5504, 15, 16.38, -26.75, 0.4496, 2, 14, 121.79, -24.18, 0.84136, 15, 11.25, -36.58, 0.15864, 2, 14, 103.32, -26.33, 0.98297, 15, 6.29, -54.5, 0.01703, 2, 14, 89.5, -28.42, 0.99945, 15, 3.03, -68.09, 0.00055, 1, 14, 62.6, -31.43, 1, 2, 13, 441.14, -41.68, 0.03873, 14, 34.11, -32.53, 0.96127, 2, 13, 422.93, -40.02, 0.26894, 14, 16.31, -36.74, 0.73106, 2, 13, 404.12, -35.03, 0.68858, 14, -3.11, -37.98, 0.31142, 2, 13, 385.56, -30.66, 0.95279, 14, -22.1, -39.73, 0.04721, 1, 13, 364.94, -28.28, 1, 1, 13, 302.78, -25.33, 1, 1, 13, 163.49, -33.95, 1, 1, 13, 82.24, -34.32, 1, 1, 13, 60.68, -37.24, 1, 1, 13, 40.51, -42.42, 1, 2, 12, 519.62, 23.63, 0.02415, 13, 18.35, -45.73, 0.97585, 2, 12, 519.69, 3.39, 0.14042, 13, -1.75, -43.4, 0.85958, 2, 12, 511.58, -18.86, 0.40565, 13, -22.89, -32.72, 0.59435, 2, 12, 502.15, -32.2, 0.60811, 13, -35.02, -21.78, 0.39189, 2, 12, 481.07, -44.16, 0.87375, 13, -44.41, 0.57, 0.12625, 2, 12, 456.2, -52.66, 0.99498, 13, -49.91, 26.27, 0.00502, 1, 12, 415.17, -62.88, 1, 1, 12, 345.07, -76.49, 1, 1, 12, 265.2, -85.49, 1, 1, 12, 194.54, -85.89, 1, 3, 11, 12.18, -171, 0.03996, 12, 141.4, -80.67, 0.87764, 7, 130.46, -113.79, 0.08241, 3, 11, 0.78, -137.08, 0.13996, 12, 106.05, -75.11, 0.66769, 7, 119.06, -79.87, 0.19235, 4, 11, -7.53, -108.92, 0.29274, 12, 77.25, -69.47, 0.40083, 7, 110.75, -51.71, 0.30143, 57, -306.29, -86.53, 0.005, 4, 11, -10.09, -78.38, 0.51444, 12, 48.97, -57.63, 0.1485, 7, 108.19, -21.17, 0.32706, 57, -278.01, -74.71, 0.01, 3, 11, -30.5, -57.36, 0.73993, 7, 87.77, -0.15, 0.25507, 57, -264.28, -48.82, 0.005, 3, 11, -22.82, -20.06, 0.78618, 7, 95.46, 37.15, 0.20882, 57, -226.42, -44.67, 0.005, 2, 12, 413.12, -34.76, 0.98375, 57, -517.35, -350.1, 0.01625, 2, 12, 442.33, -31.27, 0.98513, 57, -535.37, -373.35, 0.01487, 3, 12, 472.5, -26.57, 0.89186, 13, -25.92, 7, 0.09677, 57, -553.2, -398.14, 0.01137, 3, 12, 487.97, -13.15, 0.54707, 13, -14.43, -9.95, 0.44105, 57, -554.51, -418.58, 0.01188, 3, 12, 499.19, 7.07, 0.13154, 13, 4.32, -23.48, 0.85781, 57, -547.98, -440.76, 0.01065, 2, 13, 24.72, -26.37, 0.98966, 57, -533.63, -455.55, 0.01034, 2, 13, 50.29, -20.3, 0.98912, 57, -509.71, -466.42, 0.01088, 2, 13, 82.72, -13.86, 0.988, 57, -480.14, -481.21, 0.012, 2, 13, 148.46, -12.58, 0.99777, 57, -427.4, -520.48, 0.00223, 1, 13, 289.09, 6.22, 1, 3, 12, 418.69, 144.05, 0.00358, 13, 149.87, 40.25, 0.99433, 57, -393.92, -479.59, 0.00209, 3, 12, 424.7, 87.96, 0.09527, 13, 93.46, 40.91, 0.89256, 57, -438.08, -444.5, 0.01217, 3, 12, 440.14, 53.72, 0.2515, 13, 57.63, 29.63, 0.73173, 57, -473.31, -431.46, 0.01678, 3, 12, 451.68, 32.72, 0.3281, 13, 35.41, 20.66, 0.65459, 57, -496.36, -424.93, 0.01732, 3, 12, 447.26, 16.76, 0.70029, 13, 20.09, 26.94, 0.28451, 57, -504.62, -410.58, 0.0152, 3, 12, 431.81, 7.03, 0.92948, 13, 12.25, 43.43, 0.05448, 57, -500.71, -392.75, 0.01603, 3, 12, 403.85, 10.61, 0.94606, 13, 19.12, 70.77, 0.03648, 57, -478.53, -375.35, 0.01746, 3, 12, 429.29, 36.93, 0.55167, 13, 42.24, 42.4, 0.43925, 57, -477.65, -411.94, 0.00908, 3, 12, 377.03, 15.55, 0.96562, 13, 27.2, 96.82, 0.01729, 57, -456.18, -359.72, 0.01709, 2, 12, 371.57, -40.25, 0.98265, 57, -492.1, -316.66, 0.01735, 2, 12, 271.34, 45.15, 0.98426, 57, -360.91, -305.23, 0.01574, 2, 12, 194.84, 56.64, 0.98292, 57, -299.02, -258.81, 0.01708, 3, 12, 142.75, 58.06, 0.92128, 7, 254.12, -50.9, 0.0598, 57, -261.45, -222.71, 0.01892, 3, 12, 80.53, 76.1, 0.77399, 7, 241.38, 12.63, 0.20824, 57, -204.92, -191.07, 0.01776, 4, 11, 103.44, -6.32, 0.00406, 12, 37.51, 76.35, 0.6988, 7, 221.72, 50.89, 0.27911, 57, -174.55, -160.6, 0.01803, 4, 11, 75.27, 17.8, 0.10896, 12, 3.11, 62.5, 0.54658, 7, 193.55, 75, 0.33064, 57, -160.26, -126.38, 0.01382, 4, 11, 40.82, 36.4, 0.20862, 12, -29.3, 40.54, 0.38051, 7, 159.1, 93.6, 0.40263, 57, -153.15, -87.88, 0.00825, 2, 12, 257.01, -41.71, 0.98138, 57, -412.71, -234.05, 0.01862, 2, 12, 189.09, -38.64, 0.98262, 57, -362.85, -187.84, 0.01738, 4, 11, 49.65, -140.81, 0.00048, 12, 131.94, -33.5, 0.97785, 7, 167.92, -83.6, 0.00753, 57, -319.06, -150.74, 0.01415, 4, 11, 37.97, -105.73, 0.00785, 12, 95.43, -27.64, 0.88464, 7, 156.25, -48.52, 0.09092, 57, -289.27, -128.85, 0.01659, 4, 11, 33.71, -61.54, 0.15028, 12, 54.27, -11.01, 0.70786, 7, 151.99, -4.33, 0.12705, 57, -248.52, -111.21, 0.0148, 4, 11, 12.46, -30.39, 0.34337, 12, 16.83, -15.47, 0.43167, 7, 130.73, 26.82, 0.21297, 57, -225.41, -81.42, 0.01199], "hull": 80, "edges": [8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 86, 88, 116, 118, 128, 130, 130, 132, 136, 138, 142, 144, 144, 146, 4, 2, 0, 158, 0, 2, 156, 158, 152, 154, 154, 156, 146, 148, 132, 134, 134, 136, 138, 140, 140, 142, 122, 124, 124, 126, 126, 128, 118, 120, 120, 122, 110, 112, 108, 110, 104, 106, 106, 108, 112, 114, 114, 116, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 40, 42, 38, 40, 36, 38, 32, 34, 34, 36, 28, 30, 30, 32, 22, 24, 24, 26, 26, 28, 4, 6, 6, 8, 10, 12, 12, 14, 148, 150, 150, 152, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 194, 32, 196, 192, 160, 198, 196, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 198, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 102, 104, 92, 94, 94, 96, 96, 98, 88, 90, 90, 92, 98, 100, 100, 102, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 72, 74, 74, 76], "width": 539, "height": 838}}, "nose": {"nose": {"type": "mesh", "uvs": [0.72204, 0.06722, 0.81934, 0.3501, 0.92296, 0.64141, 0.98295, 0.82315, 0.83589, 0.93329, 0.61888, 0.94155, 0.47083, 0.97508, 0.3168, 0.98174, 0.06056, 0.81456, 0.06115, 0.69981, 0.1141, 0.58194, 0.17068, 0.3365, 0.17948, 0.10249, 0.44811, 0.04231, 0.29682, 0.82766, 0.52184, 0.76177, 0.49684, 0.63572, 0.44683, 0.53832, 0.4385, 0.27189, 0.25932, 0.70161, 0.28432, 0.53546, 0.29682, 0.28622, 0.74243, 0.58527, 0.76187, 0.74889], "triangles": [18, 12, 13, 18, 13, 0, 21, 12, 18, 11, 12, 21, 18, 0, 1, 20, 11, 21, 17, 18, 1, 21, 18, 17, 20, 21, 17, 10, 11, 20, 22, 17, 1, 16, 17, 22, 22, 1, 2, 19, 10, 20, 9, 10, 19, 16, 19, 20, 16, 20, 17, 23, 22, 2, 16, 22, 23, 15, 16, 23, 19, 16, 15, 8, 9, 19, 23, 2, 3, 14, 19, 15, 8, 19, 14, 4, 23, 3, 5, 15, 23, 5, 23, 4, 6, 14, 15, 5, 6, 15, 7, 14, 6, 8, 14, 7], "vertices": [2, 6, 64.04, 40.36, 0.96512, 62, -327.44, 110.01, 0.03488, 2, 6, 50.09, 40.28, 0.96434, 62, -341.39, 109.94, 0.03566, 2, 6, 35.7, 40.09, 0.96356, 62, -355.78, 109.74, 0.03644, 2, 6, 26.75, 40.12, 0.96294, 62, -364.73, 109.77, 0.03706, 2, 6, 22.69, 46.04, 0.96325, 62, -368.79, 115.69, 0.03675, 2, 6, 23.91, 53.1, 0.96434, 62, -367.57, 122.76, 0.03566, 2, 6, 23.44, 58.23, 0.96583, 62, -368.04, 127.88, 0.03417, 2, 6, 24.27, 63.25, 0.96757, 62, -367.21, 132.91, 0.03243, 2, 6, 33.99, 69.69, 0.96341, 62, -357.49, 139.34, 0.03659, 2, 6, 39.35, 68.43, 0.96302, 62, -352.13, 138.09, 0.03698, 2, 6, 44.47, 65.46, 0.96314, 62, -347.01, 135.11, 0.03686, 2, 6, 55.53, 60.99, 0.96533, 62, -335.95, 130.65, 0.03467, 2, 6, 66.41, 58.19, 0.96726, 62, -325.07, 127.84, 0.03274, 2, 6, 67.24, 48.9, 0.96507, 62, -324.24, 118.55, 0.03493, 2, 6, 31.63, 62.23, 0.9607, 62, -359.85, 131.89, 0.0393, 2, 6, 33.04, 54.29, 0.95966, 62, -358.44, 123.94, 0.04034, 2, 6, 39.12, 53.73, 0.96019, 62, -352.36, 123.39, 0.03981, 2, 6, 44.05, 54.29, 0.96046, 62, -347.43, 123.94, 0.03954, 2, 6, 56.57, 51.68, 0.96324, 62, -334.91, 121.34, 0.03676, 2, 6, 37.8, 62.08, 0.95996, 62, -353.68, 131.73, 0.04004, 2, 6, 45.39, 59.48, 0.96127, 62, -346.09, 129.14, 0.03873, 2, 6, 56.95, 56.39, 0.96358, 62, -334.53, 126.05, 0.03642, 2, 6, 39.66, 45.29, 0.9616, 62, -351.82, 114.94, 0.0384, 2, 6, 31.86, 46.43, 0.96104, 62, -359.62, 116.08, 0.03896], "hull": 14, "edges": [0, 26, 6, 8, 8, 10, 14, 16, 24, 26, 10, 12, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 26, 38, 40, 40, 42, 4, 6, 0, 2, 2, 4, 46, 44], "width": 33, "height": 48}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": -7.98, "curve": [0.444, -7.98, 0.889, 11.39, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 11.39, "curve": [1.778, 11.4, 2.222, -7.97, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -7.98, "curve": [3.111, -7.98, 3.556, 11.39, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 11.39, "curve": [4.444, 11.39, 4.889, -7.96, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -7.98, "curve": [5.667, -7.98, 6, 23.23, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 23.23, "curve": [6.778, 23.24, 7.222, 7.63, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 7.63, "curve": [8.111, 7.62, 8.556, 23.22, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 23.23, "curve": [9.444, 23.24, 9.889, -7.97, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -7.98, "curve": [10.778, -7.98, 11.222, 11.39, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 11.39, "curve": [12.111, 11.39, 12.556, -7.98, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -7.98}]}, "ALL2": {"translate": [{"y": -10.31, "curve": [0.444, 0, 0.889, 0, 0.444, -10.31, 0.889, 11.24]}, {"time": 1.3333, "y": 11.25, "curve": [1.778, 0, 2.222, 0, 1.778, 11.25, 2.222, -10.31]}, {"time": 2.6667, "y": -10.31, "curve": [3.111, 0, 3.556, 0, 3.111, -10.32, 3.556, 11.25]}, {"time": 4, "y": 11.25, "curve": [4.444, 0, 4.889, 0, 4.444, 11.25, 4.889, -10.3]}, {"time": 5.3333, "y": -10.31, "curve": [5.667, 0, 6, 0, 5.667, -10.32, 6, 14.93]}, {"time": 6.3333, "y": 14.94, "curve": [6.778, 0, 7.222, 0, 6.778, 14.94, 7.222, 2.32]}, {"time": 7.6667, "y": 2.31, "curve": [8.111, 0, 8.556, 0, 8.111, 2.31, 8.556, 14.93]}, {"time": 9, "y": 14.94, "curve": [9.444, 0, 9.889, 0, 9.444, 14.94, 9.889, -10.31]}, {"time": 10.3333, "y": -10.31, "curve": [10.778, 0, 11.222, 0, 10.778, -10.32, 11.222, 11.25]}, {"time": 11.6667, "y": 11.25, "curve": [12.111, 0, 12.556, 0, 12.111, 11.25, 12.556, -10.31]}, {"time": 13, "y": -10.31}]}, "body": {"rotate": [{"value": -2.76, "curve": [0.444, -2.76, 0.889, 1.97]}, {"time": 1.3333, "value": 1.98, "curve": [1.778, 1.98, 2.222, -2.76]}, {"time": 2.6667, "value": -2.76, "curve": [3.111, -2.77, 3.556, 1.98]}, {"time": 4, "value": 1.98, "curve": [4.444, 1.98, 4.889, -2.76]}, {"time": 5.3333, "value": -2.76, "curve": [5.667, -2.77, 6, 3.21]}, {"time": 6.3333, "value": 3.21, "curve": [6.778, 3.22, 7.222, 0.23]}, {"time": 7.6667, "value": 0.22, "curve": [8.111, 0.22, 8.556, 3.21]}, {"time": 9, "value": 3.21, "curve": [9.444, 3.22, 9.889, -2.76]}, {"time": 10.3333, "value": -2.76, "curve": [10.778, -2.77, 11.222, 1.98]}, {"time": 11.6667, "value": 1.98, "curve": [12.111, 1.98, 12.556, -2.76]}, {"time": 13, "value": -2.76}], "translate": [{"x": 0.71, "y": -10.28, "curve": [0.057, 0.79, 0.112, 0.84, 0.057, -10.93, 0.112, -11.42]}, {"time": 0.1667, "x": 0.84, "y": -11.42, "curve": [0.611, 0.84, 1.056, -2.04, 0.611, -11.42, 1.056, 12.92]}, {"time": 1.5, "x": -2.04, "y": 12.93, "curve": [1.944, -2.04, 2.389, 0.84, 1.944, 12.93, 2.389, -11.42]}, {"time": 2.8333, "x": 0.84, "y": -11.42, "curve": [3.278, 0.85, 3.722, -2.04, 3.278, -11.43, 3.722, 12.93]}, {"time": 4.1667, "x": -2.04, "y": 12.93, "curve": [4.611, -2.04, 5.056, 0.84, 4.611, 12.93, 5.056, -11.41]}, {"time": 5.5, "x": 0.84, "y": -11.42, "curve": [5.833, 0.85, 6.167, -2.04, 5.833, -11.43, 6.167, 12.93]}, {"time": 6.5, "x": -2.04, "y": 12.93, "curve": [6.944, -2.04, 7.389, -0.6, 6.944, 12.93, 7.389, 0.76]}, {"time": 7.8333, "x": -0.6, "y": 0.75, "curve": [8.278, -0.6, 8.722, -2.04, 8.278, 0.75, 8.722, 12.92]}, {"time": 9.1667, "x": -2.04, "y": 12.93, "curve": [9.611, -2.04, 10.056, 0.84, 9.611, 12.93, 10.056, -11.42]}, {"time": 10.5, "x": 0.84, "y": -11.42, "curve": [10.944, 0.85, 11.389, -2.04, 10.944, -11.43, 11.389, 12.93]}, {"time": 11.8333, "x": -2.04, "y": 12.93, "curve": [12.223, -2.04, 12.613, 0.16, 12.223, 12.93, 12.613, -5.66]}, {"time": 13, "x": 0.71, "y": -10.28}], "scale": [{"y": 1.036, "curve": [0.057, 1, 0.112, 1, 0.057, 1.039, 0.112, 1.04]}, {"time": 0.1667, "y": 1.04, "curve": [0.611, 1, 1.056, 1, 0.611, 1.04, 1.056, 0.956]}, {"time": 1.5, "y": 0.956, "curve": [1.944, 1, 2.389, 1, 1.944, 0.956, 2.389, 1.04]}, {"time": 2.8333, "y": 1.04, "curve": [3.278, 1, 3.722, 1, 3.278, 1.04, 3.722, 0.956]}, {"time": 4.1667, "y": 0.956, "curve": [4.611, 1, 5.056, 1, 4.611, 0.956, 5.056, 1.04]}, {"time": 5.5, "y": 1.04, "curve": [5.833, 1, 6.167, 1, 5.833, 1.04, 6.167, 0.956]}, {"time": 6.5, "y": 0.956, "curve": [6.944, 1, 7.389, 1, 6.944, 0.956, 7.389, 0.998]}, {"time": 7.8333, "y": 0.998, "curve": [8.278, 1, 8.722, 1, 8.278, 0.998, 8.722, 0.956]}, {"time": 9.1667, "y": 0.956, "curve": [9.611, 1, 10.056, 1, 9.611, 0.956, 10.056, 1.04]}, {"time": 10.5, "y": 1.04, "curve": [10.944, 1, 11.389, 1, 10.944, 1.04, 11.389, 0.956]}, {"time": 11.8333, "y": 0.956, "curve": [12.223, 1, 12.613, 1, 12.223, 0.956, 12.613, 1.02]}, {"time": 13, "y": 1.036}]}, "body2": {"rotate": [{"value": -1.12, "curve": [0.057, -1.21, 0.112, -1.27]}, {"time": 0.1667, "value": -1.27, "curve": [0.611, -1.27, 1.056, 1.89]}, {"time": 1.5, "value": 1.89, "curve": [1.944, 1.89, 2.389, -1.27]}, {"time": 2.8333, "value": -1.27, "curve": [3.278, -1.27, 3.722, 1.89]}, {"time": 4.1667, "value": 1.89, "curve": [4.611, 1.89, 5.056, -1.27]}, {"time": 5.5, "value": -1.27, "curve": [5.833, -1.27, 6.167, 5.39]}, {"time": 6.5, "value": 5.39, "curve": [6.944, 5.39, 7.389, 2.06]}, {"time": 7.8333, "value": 2.06, "curve": [8.278, 2.06, 8.722, 5.39]}, {"time": 9.1667, "value": 5.39, "curve": [9.611, 5.39, 10.056, -1.27]}, {"time": 10.5, "value": -1.27, "curve": [10.944, -1.27, 11.389, 1.89]}, {"time": 11.8333, "value": 1.89, "curve": [12.223, 1.89, 12.613, -0.52]}, {"time": 13, "value": -1.12}], "translate": [{"x": -7.26, "curve": [0.114, -9.05, 0.224, -10.38, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -10.38, "curve": [0.778, -10.38, 1.222, 9.11, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 9.11, "curve": [2.111, 9.12, 2.556, -10.38, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -10.38, "curve": [3.444, -10.39, 3.889, 9.11, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 9.11, "curve": [4.778, 9.11, 5.222, -10.37, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -10.38, "curve": [6, -10.39, 6.333, 9.11, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 9.11, "curve": [7.111, 9.12, 7.556, -0.63, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -0.63, "curve": [8.444, -0.64, 8.889, 9.11, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 9.11, "curve": [9.778, 9.12, 10.222, -10.38, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -10.38, "curve": [11.111, -10.39, 11.556, 9.11, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 9.11, "curve": [12.335, 9.11, 12.67, -1.8, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -7.26}], "scale": [{"x": 1.002, "y": 1.002, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.01, 0.778, 1, 1.222, 1.01]}, {"time": 1.6667, "x": 1.01, "y": 1.01, "curve": [2.111, 1.01, 2.556, 1, 2.111, 1.01, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.01, 3.444, 1, 3.889, 1.01]}, {"time": 4.3333, "x": 1.01, "y": 1.01, "curve": [4.778, 1.01, 5.222, 1, 4.778, 1.01, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.01, 6, 1, 6.333, 1.01]}, {"time": 6.6667, "x": 1.01, "y": 1.01, "curve": [7.111, 1.01, 7.556, 1.005, 7.111, 1.01, 7.556, 1.005]}, {"time": 8, "x": 1.005, "y": 1.005, "curve": [8.444, 1.005, 8.889, 1.01, 8.444, 1.005, 8.889, 1.01]}, {"time": 9.3333, "x": 1.01, "y": 1.01, "curve": [9.778, 1.01, 10.222, 1, 9.778, 1.01, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.01, 11.111, 1, 11.556, 1.01]}, {"time": 12, "x": 1.01, "y": 1.01, "curve": [12.335, 1.01, 12.67, 1.004, 12.335, 1.01, 12.67, 1.004]}, {"time": 13, "x": 1.002, "y": 1.002}]}, "neck": {"rotate": [{"value": 0.78, "curve": [0.168, 1.41, 0.334, 1.93]}, {"time": 0.5, "value": 1.93, "curve": [0.944, 1.93, 1.389, -1.66]}, {"time": 1.8333, "value": -1.66, "curve": [2.278, -1.66, 2.722, 1.92]}, {"time": 3.1667, "value": 1.93, "curve": [3.611, 1.93, 4.056, -1.66]}, {"time": 4.5, "value": -1.66, "curve": [4.944, -1.66, 5.389, 1.92]}, {"time": 5.8333, "value": 1.93, "curve": [6.167, 1.93, 6.5, -8.6]}, {"time": 6.8333, "value": -8.6, "curve": [7.278, -8.6, 7.722, -6.36]}, {"time": 8.1667, "value": -6.36, "curve": [8.611, -6.36, 9.056, -8.6]}, {"time": 9.5, "value": -8.6, "curve": [9.944, -8.6, 10.389, 1.92]}, {"time": 10.8333, "value": 1.93, "curve": [11.278, 1.93, 11.722, -1.66]}, {"time": 12.1667, "value": -1.66, "curve": [12.445, -1.66, 12.724, -0.27]}, {"time": 13, "value": 0.78}]}, "head": {"rotate": [{"value": 0.13, "curve": [0.225, 1.02, 0.446, 1.93]}, {"time": 0.6667, "value": 1.93, "curve": [1.111, 1.93, 1.556, -1.66]}, {"time": 2, "value": -1.66, "curve": [2.444, -1.66, 2.889, 1.92]}, {"time": 3.3333, "value": 1.93, "curve": [3.778, 1.93, 4.222, -1.66]}, {"time": 4.6667, "value": -1.66, "curve": [5.111, -1.66, 5.556, 1.92]}, {"time": 6, "value": 1.93, "curve": [6.333, 1.93, 6.667, -8.6]}, {"time": 7, "value": -8.6, "curve": [7.444, -8.6, 7.889, -6.36]}, {"time": 8.3333, "value": -6.36, "curve": [8.778, -6.36, 9.222, -8.6]}, {"time": 9.6667, "value": -8.6, "curve": [10.111, -8.6, 10.556, 1.92]}, {"time": 11, "value": 1.93, "curve": [11.444, 1.93, 11.889, -1.66]}, {"time": 12.3333, "value": -1.66, "curve": [12.557, -1.66, 12.781, -0.77]}, {"time": 13, "value": 0.13}]}, "tun": {"rotate": [{"value": 2.33, "curve": [0.444, 2.33, 0.889, -2.65]}, {"time": 1.3333, "value": -2.65, "curve": [1.778, -2.65, 2.222, 2.33]}, {"time": 2.6667, "value": 2.33, "curve": [3.111, 2.33, 3.556, -2.65]}, {"time": 4, "value": -2.65, "curve": [4.444, -2.65, 4.889, 2.33]}, {"time": 5.3333, "value": 2.33, "curve": [5.667, 2.33, 6, -4.37]}, {"time": 6.3333, "value": -4.37, "curve": [6.778, -4.37, 7.222, -1.02]}, {"time": 7.6667, "value": -1.02, "curve": [8.111, -1.02, 8.556, -4.37]}, {"time": 9, "value": -4.37, "curve": [9.444, -4.37, 9.889, 2.33]}, {"time": 10.3333, "value": 2.33, "curve": [10.778, 2.33, 11.222, -2.65]}, {"time": 11.6667, "value": -2.65, "curve": [12.111, -2.65, 12.556, 2.33]}, {"time": 13, "value": 2.33}]}, "leg_R": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.5]}, {"time": 7.6667, "value": 0.5, "curve": [8.111, 0.5, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.38]}, {"time": 7.6667, "value": 0.38, "curve": [8.111, 0.38, 8.556, 0]}, {"time": 9}]}, "leg_L0": {"translate": [{"y": -12.16, "curve": [0.057, 0, 0.112, 0, 0.057, -12.85, 0.112, -13.38]}, {"time": 0.1667, "y": -13.38, "curve": [0.611, 0, 1.056, 0, 0.611, -13.38, 1.056, 12.62]}, {"time": 1.5, "y": 12.62, "curve": [1.944, 0, 2.389, 0, 1.944, 12.63, 2.389, -13.37]}, {"time": 2.8333, "y": -13.38, "curve": [3.278, 0, 3.722, 0, 3.278, -13.39, 3.722, 12.62]}, {"time": 4.1667, "y": 12.62, "curve": [4.611, 0, 5.056, 0, 4.611, 12.62, 5.056, -13.37]}, {"time": 5.5, "y": -13.38, "curve": [5.833, 0, 6.167, 0, 5.833, -13.39, 6.167, 12.62]}, {"time": 6.5, "y": 12.62, "curve": [6.944, 0, 7.389, 0, 6.944, 12.63, 7.389, -0.37]}, {"time": 7.8333, "y": -0.38, "curve": [8.278, 0, 8.722, 0, 8.278, -0.38, 8.722, 12.62]}, {"time": 9.1667, "y": 12.62, "curve": [9.611, 0, 10.056, 0, 9.611, 12.63, 10.056, -13.37]}, {"time": 10.5, "y": -13.38, "curve": [10.944, 0, 11.389, 0, 10.944, -13.39, 11.389, 12.62]}, {"time": 11.8333, "y": 12.62, "curve": [12.223, 0, 12.613, 0, 12.223, 12.62, 12.613, -7.23]}, {"time": 13, "y": -12.16}]}, "leg_L": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.34]}, {"time": 7.6667, "value": -0.34, "curve": [8.111, -0.34, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.81]}, {"time": 7.6667, "value": 0.81, "curve": [8.111, 0.81, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"value": -3.03, "curve": [0.114, -3.92, 0.224, -4.58]}, {"time": 0.3333, "value": -4.58, "curve": [0.778, -4.58, 1.222, 5.1]}, {"time": 1.6667, "value": 5.1, "curve": [2.111, 5.11, 2.556, -4.58]}, {"time": 3, "value": -4.58, "curve": [3.444, -4.59, 3.889, 5.1]}, {"time": 4.3333, "value": 5.1, "curve": [4.778, 5.1, 5.222, -4.58]}, {"time": 5.6667, "value": -4.58, "curve": [6, -4.59, 6.333, 5.1]}, {"time": 6.6667, "value": 5.1, "curve": [7.111, 5.1, 7.556, 0.26]}, {"time": 8, "value": 0.26, "curve": [8.444, 0.26, 8.889, 5.1]}, {"time": 9.3333, "value": 5.1, "curve": [9.778, 5.11, 10.222, -4.58]}, {"time": 10.6667, "value": -4.58, "curve": [11.111, -4.59, 11.556, 5.1]}, {"time": 12, "value": 5.1, "curve": [12.335, 5.1, 12.67, -0.32]}, {"time": 13, "value": -3.03}]}, "leg_L4": {"rotate": [{"value": -1.5, "curve": [0.168, -3.19, 0.334, -4.58]}, {"time": 0.5, "value": -4.58, "curve": [0.944, -4.58, 1.389, 5.1]}, {"time": 1.8333, "value": 5.1, "curve": [2.278, 5.11, 2.722, -4.58]}, {"time": 3.1667, "value": -4.58, "curve": [3.611, -4.59, 4.056, 5.1]}, {"time": 4.5, "value": 5.1, "curve": [4.944, 5.1, 5.389, -4.58]}, {"time": 5.8333, "value": -4.58, "curve": [6.167, -4.59, 6.5, 5.1]}, {"time": 6.8333, "value": 5.1, "curve": [7.278, 5.1, 7.722, 0.26]}, {"time": 8.1667, "value": 0.26, "curve": [8.611, 0.26, 9.056, 5.1]}, {"time": 9.5, "value": 5.1, "curve": [9.944, 5.11, 10.389, -4.58]}, {"time": 10.8333, "value": -4.58, "curve": [11.278, -4.59, 11.722, 5.1]}, {"time": 12.1667, "value": 5.1, "curve": [12.445, 5.1, 12.724, 1.34]}, {"time": 13, "value": -1.5}]}, "sh_L": {"translate": [{"x": -3.02, "y": -1.77, "curve": [0.114, -4.03, 0.224, -4.79, 0.114, -2.3, 0.224, -2.69]}, {"time": 0.3333, "x": -4.79, "y": -2.69, "curve": [0.778, -4.79, 1.222, 6.3, 0.778, -2.69, 1.222, 3.1]}, {"time": 1.6667, "x": 6.3, "y": 3.1, "curve": [2.111, 6.31, 2.556, -4.79, 2.111, 3.1, 2.556, -2.69]}, {"time": 3, "x": -4.79, "y": -2.69, "curve": [3.444, -4.79, 3.889, 6.3, 3.444, -2.7, 3.889, 3.1]}, {"time": 4.3333, "x": 6.3, "y": 3.1, "curve": [4.778, 6.3, 5.222, -4.79, 4.778, 3.1, 5.222, -2.69]}, {"time": 5.6667, "x": -4.79, "y": -2.69, "curve": [6, -4.79, 6.333, 6.3, 6, -2.7, 6.333, 3.1]}, {"time": 6.6667, "x": 6.3, "y": 3.1, "curve": [7.111, 6.31, 7.556, 0.76, 7.111, 3.1, 7.556, 0.2]}, {"time": 8, "x": 0.76, "y": 0.2, "curve": [8.444, 0.76, 8.889, 6.3, 8.444, 0.2, 8.889, 3.1]}, {"time": 9.3333, "x": 6.3, "y": 3.1, "curve": [9.778, 6.31, 10.222, -4.79, 9.778, 3.1, 10.222, -2.69]}, {"time": 10.6667, "x": -4.79, "y": -2.69, "curve": [11.111, -4.79, 11.556, 6.3, 11.111, -2.7, 11.556, 3.1]}, {"time": 12, "x": 6.3, "y": 3.1, "curve": [12.335, 6.3, 12.67, 0.09, 12.335, 3.1, 12.67, -0.14]}, {"time": 13, "x": -3.02, "y": -1.77}]}, "sh_R": {"translate": [{"x": -3.02, "y": -1.77, "curve": [0.114, -4.03, 0.224, -4.79, 0.114, -2.3, 0.224, -2.69]}, {"time": 0.3333, "x": -4.79, "y": -2.69, "curve": [0.778, -4.79, 1.222, 6.3, 0.778, -2.69, 1.222, 3.1]}, {"time": 1.6667, "x": 6.3, "y": 3.1, "curve": [2.111, 6.31, 2.556, -4.79, 2.111, 3.1, 2.556, -2.69]}, {"time": 3, "x": -4.79, "y": -2.69, "curve": [3.444, -4.79, 3.889, 6.3, 3.444, -2.7, 3.889, 3.1]}, {"time": 4.3333, "x": 6.3, "y": 3.1, "curve": [4.778, 6.3, 5.222, -4.79, 4.778, 3.1, 5.222, -2.69]}, {"time": 5.6667, "x": -4.79, "y": -2.69, "curve": [6, -4.79, 6.333, 6.3, 6, -2.7, 6.333, 3.1]}, {"time": 6.6667, "x": 6.3, "y": 3.1, "curve": [7.111, 6.31, 7.556, 0.76, 7.111, 3.1, 7.556, 0.2]}, {"time": 8, "x": 0.76, "y": 0.2, "curve": [8.444, 0.76, 8.889, 6.3, 8.444, 0.2, 8.889, 3.1]}, {"time": 9.3333, "x": 6.3, "y": 3.1, "curve": [9.778, 6.31, 10.222, -4.79, 9.778, 3.1, 10.222, -2.69]}, {"time": 10.6667, "x": -4.79, "y": -2.69, "curve": [11.111, -4.79, 11.556, 6.3, 11.111, -2.7, 11.556, 3.1]}, {"time": 12, "x": 6.3, "y": 3.1, "curve": [12.335, 6.3, 12.67, 0.09, 12.335, 3.1, 12.67, -0.14]}, {"time": 13, "x": -3.02, "y": -1.77}]}, "arm_L": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.37]}, {"time": 7.6667, "value": 0.37, "curve": [8.111, 0.37, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -2.53]}, {"time": 7.6667, "value": -2.53, "curve": [8.111, -2.53, 8.556, 0]}, {"time": 9}]}, "arm_L3": {"rotate": [{"value": -3.79, "curve": [0.114, -5.04, 0.224, -5.96]}, {"time": 0.3333, "value": -5.97, "curve": [0.778, -5.97, 1.222, 7.62]}, {"time": 1.6667, "value": 7.62, "curve": [2.111, 7.62, 2.556, -5.96]}, {"time": 3, "value": -5.97, "curve": [3.444, -5.97, 3.889, 7.62]}, {"time": 4.3333, "value": 7.62, "curve": [4.778, 7.62, 5.222, -5.96]}, {"time": 5.6667, "value": -5.97, "curve": [6, -5.97, 6.333, 7.62]}, {"time": 6.6667, "value": 7.62, "curve": [7.111, 7.63, 7.556, -5.96]}, {"time": 8, "value": -5.97, "curve": [8.444, -5.97, 8.889, 0.82]}, {"time": 9.3333, "value": 0.83, "curve": [9.778, 0.83, 10.222, -5.96]}, {"time": 10.6667, "value": -5.97, "curve": [11.111, -5.97, 11.556, 7.62]}, {"time": 12, "value": 7.62, "curve": [12.335, 7.62, 12.67, 0.02]}, {"time": 13, "value": -3.79}]}, "arm_R": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.83]}, {"time": 7.6667, "value": 1.83, "curve": [8.111, 1.83, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, 0.01]}, {"time": 7.6667, "value": 0.01, "curve": [8.111, 0.01, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "arm_R3": {"rotate": [{"value": 0.53, "curve": [0.225, -1.99, 0.446, -4.55]}, {"time": 0.6667, "value": -4.55, "curve": [1.111, -4.55, 1.556, 5.6]}, {"time": 2, "value": 5.6, "curve": [2.444, 5.61, 2.889, -4.54]}, {"time": 3.3333, "value": -4.55, "curve": [3.778, -4.55, 4.222, 5.6]}, {"time": 4.6667, "value": 5.6, "curve": [5.111, 5.61, 5.556, -4.54]}, {"time": 6, "value": -4.55, "curve": [6.333, -4.55, 6.667, 5.6]}, {"time": 7, "value": 5.6, "curve": [7.444, 5.61, 7.889, 0.53]}, {"time": 8.3333, "value": 0.53, "curve": [8.778, 0.53, 9.222, 5.6]}, {"time": 9.6667, "value": 5.6, "curve": [10.111, 5.61, 10.556, -4.54]}, {"time": 11, "value": -4.55, "curve": [11.444, -4.55, 11.889, 5.6]}, {"time": 12.3333, "value": 5.6, "curve": [12.557, 5.61, 12.781, 3.08]}, {"time": 13, "value": 0.53}]}, "arm_R4": {"rotate": [{"value": 2.38, "curve": [0.279, -0.59, 0.556, -4.55]}, {"time": 0.8333, "value": -4.55, "curve": [1.278, -4.55, 1.722, 5.6]}, {"time": 2.1667, "value": 5.6, "curve": [2.611, 5.61, 3.056, -4.54]}, {"time": 3.5, "value": -4.55, "curve": [3.944, -4.55, 4.389, 5.6]}, {"time": 4.8333, "value": 5.6, "curve": [5.278, 5.61, 5.722, -4.54]}, {"time": 6.1667, "value": -4.55, "curve": [6.5, -4.55, 6.833, 5.6]}, {"time": 7.1667, "value": 5.6, "curve": [7.611, 5.61, 8.056, 0.53]}, {"time": 8.5, "value": 0.53, "curve": [8.944, 0.53, 9.389, 5.6]}, {"time": 9.8333, "value": 5.6, "curve": [10.278, 5.61, 10.722, -4.54]}, {"time": 11.1667, "value": -4.55, "curve": [11.611, -4.55, 12.056, 5.6]}, {"time": 12.5, "value": 5.6, "curve": [12.667, 5.61, 12.835, 4.17]}, {"time": 13, "value": 2.38}]}, "arm_R5": {"rotate": [{"value": 3.98, "curve": [0.336, 1.11, 0.668, -4.54]}, {"time": 1, "value": -4.55, "curve": [1.444, -4.55, 1.889, 5.6]}, {"time": 2.3333, "value": 5.6, "curve": [2.778, 5.61, 3.222, -4.54]}, {"time": 3.6667, "value": -4.55, "curve": [4.111, -4.55, 4.556, 5.6]}, {"time": 5, "value": 5.6, "curve": [5.444, 5.61, 5.889, -4.54]}, {"time": 6.3333, "value": -4.55, "curve": [6.667, -4.55, 7, 5.6]}, {"time": 7.3333, "value": 5.6, "curve": [7.778, 5.61, 8.222, 0.53]}, {"time": 8.6667, "value": 0.53, "curve": [9.111, 0.53, 9.556, 5.6]}, {"time": 10, "value": 5.6, "curve": [10.444, 5.61, 10.889, -4.54]}, {"time": 11.3333, "value": -4.55, "curve": [11.778, -4.55, 12.222, 5.6]}, {"time": 12.6667, "value": 5.6, "curve": [12.779, 5.6, 12.892, 4.95]}, {"time": 13, "value": 3.98}]}, "RU_L": {"translate": [{"x": -6.45, "y": -0.81, "curve": [0.168, -18.01, 0.334, -27.49, 0.168, -2.25, 0.334, -3.44]}, {"time": 0.5, "x": -27.49, "y": -3.44, "curve": [0.944, -27.49, 1.389, 38.65, 0.944, -3.44, 1.389, 4.83]}, {"time": 1.8333, "x": 38.67, "y": 4.83, "curve": [2.278, 38.68, 2.722, -27.47, 2.278, 4.84, 2.722, -3.43]}, {"time": 3.1667, "x": -27.49, "y": -3.44, "curve": [3.611, -27.5, 4.056, 38.67, 3.611, -3.44, 4.056, 4.83]}, {"time": 4.5, "x": 38.67, "y": 4.83, "curve": [4.944, 38.67, 5.389, -27.46, 4.944, 4.83, 5.389, -3.43]}, {"time": 5.8333, "x": -27.49, "y": -3.44, "curve": [6.167, -27.5, 6.5, 38.66, 6.167, -3.44, 6.5, 4.83]}, {"time": 6.8333, "x": 38.67, "y": 4.83, "curve": [7.278, 38.68, 7.722, 5.6, 7.278, 4.84, 7.722, 0.7]}, {"time": 8.1667, "x": 5.59, "y": 0.7, "curve": [8.611, 5.58, 9.056, 38.65, 8.611, 0.7, 9.056, 4.83]}, {"time": 9.5, "x": 38.67, "y": 4.83, "curve": [9.944, 38.68, 10.389, -27.47, 9.944, 4.84, 10.389, -3.43]}, {"time": 10.8333, "x": -27.49, "y": -3.44, "curve": [11.278, -27.5, 11.722, 38.67, 11.278, -3.44, 11.722, 4.83]}, {"time": 12.1667, "x": 38.67, "y": 4.83, "curve": [12.445, 38.67, 12.724, 12.95, 12.445, 4.83, 12.724, 1.62]}, {"time": 13, "x": -6.45, "y": -0.81}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.167, 1.021, 0.333, 0.956, 0.167, 1, 0.333, 1.087]}, {"time": 0.5, "x": 0.956, "y": 1.087, "curve": [0.722, 0.956, 0.944, 1.073, 0.722, 1.087, 0.944, 0.932]}, {"time": 1.1667, "x": 1.073, "y": 0.932, "curve": [1.389, 1.073, 1.611, 0.956, 1.389, 0.932, 1.611, 1.087]}, {"time": 1.8333, "x": 0.956, "y": 1.087, "curve": [2.056, 0.956, 2.278, 1.073, 2.056, 1.087, 2.278, 0.932]}, {"time": 2.5, "x": 1.073, "y": 0.932, "curve": [2.722, 1.073, 2.944, 0.956, 2.722, 0.932, 2.944, 1.087]}, {"time": 3.1667, "x": 0.956, "y": 1.087, "curve": [3.389, 0.956, 3.611, 1.073, 3.389, 1.087, 3.611, 0.932]}, {"time": 3.8333, "x": 1.073, "y": 0.932, "curve": [4.056, 1.073, 4.278, 0.956, 4.056, 0.932, 4.278, 1.087]}, {"time": 4.5, "x": 0.956, "y": 1.087, "curve": [4.722, 0.956, 4.944, 1.073, 4.722, 1.087, 4.944, 0.932]}, {"time": 5.1667, "x": 1.073, "y": 0.932, "curve": [5.389, 1.073, 5.611, 0.956, 5.389, 0.932, 5.611, 1.087]}, {"time": 5.8333, "x": 0.956, "y": 1.087, "curve": [6, 0.956, 6.167, 1.073, 6, 1.087, 6.167, 0.932]}, {"time": 6.3333, "x": 1.073, "y": 0.932, "curve": [6.5, 1.073, 6.667, 0.956, 6.5, 0.932, 6.667, 1.087]}, {"time": 6.8333, "x": 0.956, "y": 1.087, "curve": [7.056, 0.956, 7.278, 1.073, 7.056, 1.087, 7.278, 0.932]}, {"time": 7.5, "x": 1.073, "y": 0.932, "curve": [7.722, 1.073, 7.944, 0.956, 7.722, 0.932, 7.944, 1.087]}, {"time": 8.1667, "x": 0.956, "y": 1.087, "curve": [8.389, 0.956, 8.611, 1.073, 8.389, 1.087, 8.611, 0.932]}, {"time": 8.8333, "x": 1.073, "y": 0.932, "curve": [9.056, 1.073, 9.278, 0.956, 9.056, 0.932, 9.278, 1.087]}, {"time": 9.5, "x": 0.956, "y": 1.087, "curve": [9.722, 0.956, 9.944, 1.073, 9.722, 1.087, 9.944, 0.932]}, {"time": 10.1667, "x": 1.073, "y": 0.932, "curve": [10.389, 1.073, 10.611, 0.956, 10.389, 0.932, 10.611, 1.087]}, {"time": 10.8333, "x": 0.956, "y": 1.087, "curve": [11.056, 0.956, 11.278, 1.073, 11.056, 1.087, 11.278, 0.932]}, {"time": 11.5, "x": 1.073, "y": 0.932, "curve": [11.722, 1.073, 11.944, 0.956, 11.722, 0.932, 11.944, 1.087]}, {"time": 12.1667, "x": 0.956, "y": 1.087, "curve": [12.389, 0.956, 12.611, 1.073, 12.389, 1.087, 12.611, 0.932]}, {"time": 12.8333, "x": 1.073, "y": 0.932, "curve": [12.889, 1.073, 12.944, 1.065, 12.889, 0.932, 12.944, 0.942]}, {"time": 13, "x": 1.054, "y": 0.957}]}, "RU_L2": {"translate": [{"x": 1.04, "y": 0.18, "curve": [0.225, -15.06, 0.446, -31.37, 0.225, -2.63, 0.446, -5.49]}, {"time": 0.6667, "x": -31.37, "y": -5.49, "curve": [1.111, -31.37, 1.556, 33.44, 1.111, -5.49, 1.556, 5.85]}, {"time": 2, "x": 33.45, "y": 5.85, "curve": [2.444, 33.47, 2.889, -31.36, 2.444, 5.85, 2.889, -5.48]}, {"time": 3.3333, "x": -31.37, "y": -5.49, "curve": [3.778, -31.39, 4.222, 33.45, 3.778, -5.49, 4.222, 5.85]}, {"time": 4.6667, "x": 33.45, "y": 5.85, "curve": [5.111, 33.45, 5.556, -31.35, 5.111, 5.85, 5.556, -5.48]}, {"time": 6, "x": -31.37, "y": -5.49, "curve": [6.333, -31.39, 6.667, 33.45, 6.333, -5.49, 6.667, 5.85]}, {"time": 7, "x": 33.45, "y": 5.85, "curve": [7.444, 33.46, 7.889, 1.05, 7.444, 5.85, 7.889, 0.18]}, {"time": 8.3333, "x": 1.04, "y": 0.18, "curve": [8.778, 1.03, 9.222, 33.44, 8.778, 0.18, 9.222, 5.85]}, {"time": 9.6667, "x": 33.45, "y": 5.85, "curve": [10.111, 33.47, 10.556, -31.36, 10.111, 5.85, 10.556, -5.48]}, {"time": 11, "x": -31.37, "y": -5.49, "curve": [11.444, -31.39, 11.889, 33.45, 11.444, -5.49, 11.889, 5.85]}, {"time": 12.3333, "x": 33.45, "y": 5.85, "curve": [12.557, 33.45, 12.781, 17.35, 12.557, 5.85, 12.781, 3.03]}, {"time": 13, "x": 1.04, "y": 0.18}], "scale": [{"x": 1.073, "y": 0.932, "curve": [0.222, 1.073, 0.444, 0.956, 0.222, 0.932, 0.444, 1.087]}, {"time": 0.6667, "x": 0.956, "y": 1.087, "curve": [0.889, 0.956, 1.111, 1.073, 0.889, 1.087, 1.111, 0.932]}, {"time": 1.3333, "x": 1.073, "y": 0.932, "curve": [1.556, 1.073, 1.778, 0.956, 1.556, 0.932, 1.778, 1.087]}, {"time": 2, "x": 0.956, "y": 1.087, "curve": [2.222, 0.956, 2.444, 1.073, 2.222, 1.087, 2.444, 0.932]}, {"time": 2.6667, "x": 1.073, "y": 0.932, "curve": [2.889, 1.073, 3.111, 0.956, 2.889, 0.932, 3.111, 1.087]}, {"time": 3.3333, "x": 0.956, "y": 1.087, "curve": [3.556, 0.956, 3.778, 1.073, 3.556, 1.087, 3.778, 0.932]}, {"time": 4, "x": 1.073, "y": 0.932, "curve": [4.222, 1.073, 4.444, 0.956, 4.222, 0.932, 4.444, 1.087]}, {"time": 4.6667, "x": 0.956, "y": 1.087, "curve": [4.889, 0.956, 5.111, 1.073, 4.889, 1.087, 5.111, 0.932]}, {"time": 5.3333, "x": 1.073, "y": 0.932, "curve": [5.556, 1.073, 5.778, 0.956, 5.556, 0.932, 5.778, 1.087]}, {"time": 6, "x": 0.956, "y": 1.087, "curve": [6.167, 0.956, 6.333, 1.073, 6.167, 1.087, 6.333, 0.932]}, {"time": 6.5, "x": 1.073, "y": 0.932, "curve": [6.667, 1.073, 6.833, 0.956, 6.667, 0.932, 6.833, 1.087]}, {"time": 7, "x": 0.956, "y": 1.087, "curve": [7.222, 0.956, 7.444, 1.073, 7.222, 1.087, 7.444, 0.932]}, {"time": 7.6667, "x": 1.073, "y": 0.932, "curve": [7.889, 1.073, 8.111, 0.956, 7.889, 0.932, 8.111, 1.087]}, {"time": 8.3333, "x": 0.956, "y": 1.087, "curve": [8.556, 0.956, 8.778, 1.073, 8.556, 1.087, 8.778, 0.932]}, {"time": 9, "x": 1.073, "y": 0.932, "curve": [9.222, 1.073, 9.444, 0.956, 9.222, 0.932, 9.444, 1.087]}, {"time": 9.6667, "x": 0.956, "y": 1.087, "curve": [9.889, 0.956, 10.111, 1.073, 9.889, 1.087, 10.111, 0.932]}, {"time": 10.3333, "x": 1.073, "y": 0.932, "curve": [10.556, 1.073, 10.778, 0.956, 10.556, 0.932, 10.778, 1.087]}, {"time": 11, "x": 0.956, "y": 1.087, "curve": [11.222, 0.956, 11.444, 1.073, 11.222, 1.087, 11.444, 0.932]}, {"time": 11.6667, "x": 1.073, "y": 0.932, "curve": [11.889, 1.073, 12.111, 0.956, 11.889, 0.932, 12.111, 1.087]}, {"time": 12.3333, "x": 0.956, "y": 1.087, "curve": [12.556, 0.956, 12.778, 1.073, 12.556, 1.087, 12.778, 0.932]}, {"time": 13, "x": 1.073, "y": 0.932}]}, "RU_L3": {"translate": [{"x": 12.7, "curve": [0.279, -3.85, 0.556, -25.86, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.86, "curve": [1.278, -25.86, 1.722, 30.67, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 30.68, "curve": [2.611, 30.7, 3.056, -25.85, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.86, "curve": [3.944, -25.87, 4.389, 30.68, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 30.68, "curve": [5.278, 30.68, 5.722, -25.84, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.86, "curve": [6.5, -25.87, 6.833, 30.68, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 30.68, "curve": [7.611, 30.69, 8.056, 2.42, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 2.41, "curve": [8.944, 2.4, 9.389, 30.67, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 30.68, "curve": [10.278, 30.7, 10.722, -25.85, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.86, "curve": [11.611, -25.87, 12.056, 30.68, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 30.68, "curve": [12.667, 30.68, 12.835, 22.7, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.7}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.056, 1.065, 0.111, 1.073, 0.056, 0.942, 0.111, 0.932]}, {"time": 0.1667, "x": 1.073, "y": 0.932, "curve": [0.389, 1.073, 0.611, 0.956, 0.389, 0.932, 0.611, 1.087]}, {"time": 0.8333, "x": 0.956, "y": 1.087, "curve": [1.056, 0.956, 1.278, 1.073, 1.056, 1.087, 1.278, 0.932]}, {"time": 1.5, "x": 1.073, "y": 0.932, "curve": [1.722, 1.073, 1.944, 0.956, 1.722, 0.932, 1.944, 1.087]}, {"time": 2.1667, "x": 0.956, "y": 1.087, "curve": [2.389, 0.956, 2.611, 1.073, 2.389, 1.087, 2.611, 0.932]}, {"time": 2.8333, "x": 1.073, "y": 0.932, "curve": [3.056, 1.073, 3.278, 0.956, 3.056, 0.932, 3.278, 1.087]}, {"time": 3.5, "x": 0.956, "y": 1.087, "curve": [3.722, 0.956, 3.944, 1.073, 3.722, 1.087, 3.944, 0.932]}, {"time": 4.1667, "x": 1.073, "y": 0.932, "curve": [4.389, 1.073, 4.611, 0.956, 4.389, 0.932, 4.611, 1.087]}, {"time": 4.8333, "x": 0.956, "y": 1.087, "curve": [5.056, 0.956, 5.278, 1.073, 5.056, 1.087, 5.278, 0.932]}, {"time": 5.5, "x": 1.073, "y": 0.932, "curve": [5.722, 1.073, 5.944, 0.956, 5.722, 0.932, 5.944, 1.087]}, {"time": 6.1667, "x": 0.956, "y": 1.087, "curve": [6.333, 0.956, 6.5, 1.073, 6.333, 1.087, 6.5, 0.932]}, {"time": 6.6667, "x": 1.073, "y": 0.932, "curve": [6.833, 1.073, 7, 0.956, 6.833, 0.932, 7, 1.087]}, {"time": 7.1667, "x": 0.956, "y": 1.087, "curve": [7.389, 0.956, 7.611, 1.073, 7.389, 1.087, 7.611, 0.932]}, {"time": 7.8333, "x": 1.073, "y": 0.932, "curve": [8.056, 1.073, 8.278, 0.956, 8.056, 0.932, 8.278, 1.087]}, {"time": 8.5, "x": 0.956, "y": 1.087, "curve": [8.722, 0.956, 8.944, 1.073, 8.722, 1.087, 8.944, 0.932]}, {"time": 9.1667, "x": 1.073, "y": 0.932, "curve": [9.389, 1.073, 9.611, 0.956, 9.389, 0.932, 9.611, 1.087]}, {"time": 9.8333, "x": 0.956, "y": 1.087, "curve": [10.056, 0.956, 10.278, 1.073, 10.056, 1.087, 10.278, 0.932]}, {"time": 10.5, "x": 1.073, "y": 0.932, "curve": [10.722, 1.073, 10.944, 0.956, 10.722, 0.932, 10.944, 1.087]}, {"time": 11.1667, "x": 0.956, "y": 1.087, "curve": [11.389, 0.956, 11.611, 1.073, 11.389, 1.087, 11.611, 0.932]}, {"time": 11.8333, "x": 1.073, "y": 0.932, "curve": [12.056, 1.073, 12.278, 0.956, 12.056, 0.932, 12.278, 1.087]}, {"time": 12.5, "x": 0.956, "y": 1.087, "curve": [12.667, 0.956, 12.833, 1.021, 12.667, 1.087, 12.833, 1]}, {"time": 13, "x": 1.054, "y": 0.957}]}, "RU_R": {"translate": [{"x": -6.45, "y": -0.81, "curve": [0.168, -18.01, 0.334, -27.49, 0.168, -2.25, 0.334, -3.44]}, {"time": 0.5, "x": -27.49, "y": -3.44, "curve": [0.944, -27.49, 1.389, 38.65, 0.944, -3.44, 1.389, 4.83]}, {"time": 1.8333, "x": 38.67, "y": 4.83, "curve": [2.278, 38.68, 2.722, -27.47, 2.278, 4.84, 2.722, -3.43]}, {"time": 3.1667, "x": -27.49, "y": -3.44, "curve": [3.611, -27.5, 4.056, 38.67, 3.611, -3.44, 4.056, 4.83]}, {"time": 4.5, "x": 38.67, "y": 4.83, "curve": [4.944, 38.67, 5.389, -27.46, 4.944, 4.83, 5.389, -3.43]}, {"time": 5.8333, "x": -27.49, "y": -3.44, "curve": [6.167, -27.5, 6.5, 38.66, 6.167, -3.44, 6.5, 4.83]}, {"time": 6.8333, "x": 38.67, "y": 4.83, "curve": [7.278, 38.68, 7.722, 5.6, 7.278, 4.84, 7.722, 0.7]}, {"time": 8.1667, "x": 5.59, "y": 0.7, "curve": [8.611, 5.58, 9.056, 38.65, 8.611, 0.7, 9.056, 4.83]}, {"time": 9.5, "x": 38.67, "y": 4.83, "curve": [9.944, 38.68, 10.389, -27.47, 9.944, 4.84, 10.389, -3.43]}, {"time": 10.8333, "x": -27.49, "y": -3.44, "curve": [11.278, -27.5, 11.722, 38.67, 11.278, -3.44, 11.722, 4.83]}, {"time": 12.1667, "x": 38.67, "y": 4.83, "curve": [12.445, 38.67, 12.724, 12.95, 12.445, 4.83, 12.724, 1.62]}, {"time": 13, "x": -6.45, "y": -0.81}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.167, 1.021, 0.333, 0.956, 0.167, 1, 0.333, 1.087]}, {"time": 0.5, "x": 0.956, "y": 1.087, "curve": [0.722, 0.956, 0.944, 1.073, 0.722, 1.087, 0.944, 0.932]}, {"time": 1.1667, "x": 1.073, "y": 0.932, "curve": [1.389, 1.073, 1.611, 0.956, 1.389, 0.932, 1.611, 1.087]}, {"time": 1.8333, "x": 0.956, "y": 1.087, "curve": [2.056, 0.956, 2.278, 1.073, 2.056, 1.087, 2.278, 0.932]}, {"time": 2.5, "x": 1.073, "y": 0.932, "curve": [2.722, 1.073, 2.944, 0.956, 2.722, 0.932, 2.944, 1.087]}, {"time": 3.1667, "x": 0.956, "y": 1.087, "curve": [3.389, 0.956, 3.611, 1.073, 3.389, 1.087, 3.611, 0.932]}, {"time": 3.8333, "x": 1.073, "y": 0.932, "curve": [4.056, 1.073, 4.278, 0.956, 4.056, 0.932, 4.278, 1.087]}, {"time": 4.5, "x": 0.956, "y": 1.087, "curve": [4.722, 0.956, 4.944, 1.073, 4.722, 1.087, 4.944, 0.932]}, {"time": 5.1667, "x": 1.073, "y": 0.932, "curve": [5.389, 1.073, 5.611, 0.956, 5.389, 0.932, 5.611, 1.087]}, {"time": 5.8333, "x": 0.956, "y": 1.087, "curve": [6, 0.956, 6.167, 1.073, 6, 1.087, 6.167, 0.932]}, {"time": 6.3333, "x": 1.073, "y": 0.932, "curve": [6.5, 1.073, 6.667, 0.956, 6.5, 0.932, 6.667, 1.087]}, {"time": 6.8333, "x": 0.956, "y": 1.087, "curve": [7.056, 0.956, 7.278, 1.073, 7.056, 1.087, 7.278, 0.932]}, {"time": 7.5, "x": 1.073, "y": 0.932, "curve": [7.722, 1.073, 7.944, 0.956, 7.722, 0.932, 7.944, 1.087]}, {"time": 8.1667, "x": 0.956, "y": 1.087, "curve": [8.389, 0.956, 8.611, 1.073, 8.389, 1.087, 8.611, 0.932]}, {"time": 8.8333, "x": 1.073, "y": 0.932, "curve": [9.056, 1.073, 9.278, 0.956, 9.056, 0.932, 9.278, 1.087]}, {"time": 9.5, "x": 0.956, "y": 1.087, "curve": [9.722, 0.956, 9.944, 1.073, 9.722, 1.087, 9.944, 0.932]}, {"time": 10.1667, "x": 1.073, "y": 0.932, "curve": [10.389, 1.073, 10.611, 0.956, 10.389, 0.932, 10.611, 1.087]}, {"time": 10.8333, "x": 0.956, "y": 1.087, "curve": [11.056, 0.956, 11.278, 1.073, 11.056, 1.087, 11.278, 0.932]}, {"time": 11.5, "x": 1.073, "y": 0.932, "curve": [11.722, 1.073, 11.944, 0.956, 11.722, 0.932, 11.944, 1.087]}, {"time": 12.1667, "x": 0.956, "y": 1.087, "curve": [12.389, 0.956, 12.611, 1.073, 12.389, 1.087, 12.611, 0.932]}, {"time": 12.8333, "x": 1.073, "y": 0.932, "curve": [12.889, 1.073, 12.944, 1.065, 12.889, 0.932, 12.944, 0.942]}, {"time": 13, "x": 1.054, "y": 0.957}]}, "RU_R2": {"translate": [{"x": 1.03, "y": 0.24, "curve": [0.225, -14.87, 0.446, -30.99, 0.225, -3.53, 0.446, -7.36]}, {"time": 0.6667, "x": -30.99, "y": -7.36, "curve": [1.111, -30.99, 1.556, 33.03, 1.111, -7.36, 1.556, 7.84]}, {"time": 2, "x": 33.04, "y": 7.84, "curve": [2.444, 33.06, 2.889, -30.97, 2.444, 7.85, 2.889, -7.35]}, {"time": 3.3333, "x": -30.99, "y": -7.36, "curve": [3.778, -31, 4.222, 33.04, 3.778, -7.36, 4.222, 7.84]}, {"time": 4.6667, "x": 33.04, "y": 7.84, "curve": [5.111, 33.04, 5.556, -30.97, 5.111, 7.84, 5.556, -7.35]}, {"time": 6, "x": -30.99, "y": -7.36, "curve": [6.333, -31, 6.667, 33.04, 6.333, -7.36, 6.667, 7.84]}, {"time": 7, "x": 33.04, "y": 7.84, "curve": [7.444, 33.05, 7.889, 1.04, 7.444, 7.85, 7.889, 0.25]}, {"time": 8.3333, "x": 1.03, "y": 0.24, "curve": [8.778, 1.02, 9.222, 33.03, 8.778, 0.24, 9.222, 7.84]}, {"time": 9.6667, "x": 33.04, "y": 7.84, "curve": [10.111, 33.06, 10.556, -30.97, 10.111, 7.85, 10.556, -7.35]}, {"time": 11, "x": -30.99, "y": -7.36, "curve": [11.444, -31, 11.889, 33.04, 11.444, -7.36, 11.889, 7.84]}, {"time": 12.3333, "x": 33.04, "y": 7.84, "curve": [12.557, 33.04, 12.781, 17.14, 12.557, 7.84, 12.781, 4.07]}, {"time": 13, "x": 1.03, "y": 0.24}], "scale": [{"x": 1.073, "y": 0.932, "curve": [0.222, 1.073, 0.444, 0.956, 0.222, 0.932, 0.444, 1.087]}, {"time": 0.6667, "x": 0.956, "y": 1.087, "curve": [0.889, 0.956, 1.111, 1.073, 0.889, 1.087, 1.111, 0.932]}, {"time": 1.3333, "x": 1.073, "y": 0.932, "curve": [1.556, 1.073, 1.778, 0.956, 1.556, 0.932, 1.778, 1.087]}, {"time": 2, "x": 0.956, "y": 1.087, "curve": [2.222, 0.956, 2.444, 1.073, 2.222, 1.087, 2.444, 0.932]}, {"time": 2.6667, "x": 1.073, "y": 0.932, "curve": [2.889, 1.073, 3.111, 0.956, 2.889, 0.932, 3.111, 1.087]}, {"time": 3.3333, "x": 0.956, "y": 1.087, "curve": [3.556, 0.956, 3.778, 1.073, 3.556, 1.087, 3.778, 0.932]}, {"time": 4, "x": 1.073, "y": 0.932, "curve": [4.222, 1.073, 4.444, 0.956, 4.222, 0.932, 4.444, 1.087]}, {"time": 4.6667, "x": 0.956, "y": 1.087, "curve": [4.889, 0.956, 5.111, 1.073, 4.889, 1.087, 5.111, 0.932]}, {"time": 5.3333, "x": 1.073, "y": 0.932, "curve": [5.556, 1.073, 5.778, 0.956, 5.556, 0.932, 5.778, 1.087]}, {"time": 6, "x": 0.956, "y": 1.087, "curve": [6.167, 0.956, 6.333, 1.073, 6.167, 1.087, 6.333, 0.932]}, {"time": 6.5, "x": 1.073, "y": 0.932, "curve": [6.667, 1.073, 6.833, 0.956, 6.667, 0.932, 6.833, 1.087]}, {"time": 7, "x": 0.956, "y": 1.087, "curve": [7.222, 0.956, 7.444, 1.073, 7.222, 1.087, 7.444, 0.932]}, {"time": 7.6667, "x": 1.073, "y": 0.932, "curve": [7.889, 1.073, 8.111, 0.956, 7.889, 0.932, 8.111, 1.087]}, {"time": 8.3333, "x": 0.956, "y": 1.087, "curve": [8.556, 0.956, 8.778, 1.073, 8.556, 1.087, 8.778, 0.932]}, {"time": 9, "x": 1.073, "y": 0.932, "curve": [9.222, 1.073, 9.444, 0.956, 9.222, 0.932, 9.444, 1.087]}, {"time": 9.6667, "x": 0.956, "y": 1.087, "curve": [9.889, 0.956, 10.111, 1.073, 9.889, 1.087, 10.111, 0.932]}, {"time": 10.3333, "x": 1.073, "y": 0.932, "curve": [10.556, 1.073, 10.778, 0.956, 10.556, 0.932, 10.778, 1.087]}, {"time": 11, "x": 0.956, "y": 1.087, "curve": [11.222, 0.956, 11.444, 1.073, 11.222, 1.087, 11.444, 0.932]}, {"time": 11.6667, "x": 1.073, "y": 0.932, "curve": [11.889, 1.073, 12.111, 0.956, 11.889, 0.932, 12.111, 1.087]}, {"time": 12.3333, "x": 0.956, "y": 1.087, "curve": [12.556, 0.956, 12.778, 1.073, 12.556, 1.087, 12.778, 0.932]}, {"time": 13, "x": 1.073, "y": 0.932}]}, "RU_R3": {"translate": [{"x": 12.67, "y": 0.9, "curve": [0.279, -3.84, 0.556, -25.79, 0.279, -0.27, 0.556, -1.84]}, {"time": 0.8333, "x": -25.79, "y": -1.84, "curve": [1.278, -25.79, 1.722, 30.59, 1.278, -1.84, 1.722, 2.18]}, {"time": 2.1667, "x": 30.6, "y": 2.18, "curve": [2.611, 30.62, 3.056, -25.78, 2.611, 2.18, 3.056, -1.84]}, {"time": 3.5, "x": -25.79, "y": -1.84, "curve": [3.944, -25.81, 4.389, 30.6, 3.944, -1.84, 4.389, 2.18]}, {"time": 4.8333, "x": 30.6, "y": 2.18, "curve": [5.278, 30.6, 5.722, -25.77, 5.278, 2.18, 5.722, -1.84]}, {"time": 6.1667, "x": -25.79, "y": -1.84, "curve": [6.5, -25.81, 6.833, 30.6, 6.5, -1.84, 6.833, 2.18]}, {"time": 7.1667, "x": 30.6, "y": 2.18, "curve": [7.611, 30.61, 8.056, 2.41, 7.611, 2.18, 8.056, 0.17]}, {"time": 8.5, "x": 2.4, "y": 0.17, "curve": [8.944, 2.4, 9.389, 30.59, 8.944, 0.17, 9.389, 2.18]}, {"time": 9.8333, "x": 30.6, "y": 2.18, "curve": [10.278, 30.62, 10.722, -25.78, 10.278, 2.18, 10.722, -1.84]}, {"time": 11.1667, "x": -25.79, "y": -1.84, "curve": [11.611, -25.81, 12.056, 30.6, 11.611, -1.84, 12.056, 2.18]}, {"time": 12.5, "x": 30.6, "y": 2.18, "curve": [12.667, 30.6, 12.835, 22.64, 12.667, 2.18, 12.835, 1.62]}, {"time": 13, "x": 12.67, "y": 0.9}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.056, 1.065, 0.111, 1.073, 0.056, 0.942, 0.111, 0.932]}, {"time": 0.1667, "x": 1.073, "y": 0.932, "curve": [0.389, 1.073, 0.611, 0.956, 0.389, 0.932, 0.611, 1.087]}, {"time": 0.8333, "x": 0.956, "y": 1.087, "curve": [1.056, 0.956, 1.278, 1.073, 1.056, 1.087, 1.278, 0.932]}, {"time": 1.5, "x": 1.073, "y": 0.932, "curve": [1.722, 1.073, 1.944, 0.956, 1.722, 0.932, 1.944, 1.087]}, {"time": 2.1667, "x": 0.956, "y": 1.087, "curve": [2.389, 0.956, 2.611, 1.073, 2.389, 1.087, 2.611, 0.932]}, {"time": 2.8333, "x": 1.073, "y": 0.932, "curve": [3.056, 1.073, 3.278, 0.956, 3.056, 0.932, 3.278, 1.087]}, {"time": 3.5, "x": 0.956, "y": 1.087, "curve": [3.722, 0.956, 3.944, 1.073, 3.722, 1.087, 3.944, 0.932]}, {"time": 4.1667, "x": 1.073, "y": 0.932, "curve": [4.389, 1.073, 4.611, 0.956, 4.389, 0.932, 4.611, 1.087]}, {"time": 4.8333, "x": 0.956, "y": 1.087, "curve": [5.056, 0.956, 5.278, 1.073, 5.056, 1.087, 5.278, 0.932]}, {"time": 5.5, "x": 1.073, "y": 0.932, "curve": [5.722, 1.073, 5.944, 0.956, 5.722, 0.932, 5.944, 1.087]}, {"time": 6.1667, "x": 0.956, "y": 1.087, "curve": [6.333, 0.956, 6.5, 1.073, 6.333, 1.087, 6.5, 0.932]}, {"time": 6.6667, "x": 1.073, "y": 0.932, "curve": [6.833, 1.073, 7, 0.956, 6.833, 0.932, 7, 1.087]}, {"time": 7.1667, "x": 0.956, "y": 1.087, "curve": [7.389, 0.956, 7.611, 1.073, 7.389, 1.087, 7.611, 0.932]}, {"time": 7.8333, "x": 1.073, "y": 0.932, "curve": [8.056, 1.073, 8.278, 0.956, 8.056, 0.932, 8.278, 1.087]}, {"time": 8.5, "x": 0.956, "y": 1.087, "curve": [8.722, 0.956, 8.944, 1.073, 8.722, 1.087, 8.944, 0.932]}, {"time": 9.1667, "x": 1.073, "y": 0.932, "curve": [9.389, 1.073, 9.611, 0.956, 9.389, 0.932, 9.611, 1.087]}, {"time": 9.8333, "x": 0.956, "y": 1.087, "curve": [10.056, 0.956, 10.278, 1.073, 10.056, 1.087, 10.278, 0.932]}, {"time": 10.5, "x": 1.073, "y": 0.932, "curve": [10.722, 1.073, 10.944, 0.956, 10.722, 0.932, 10.944, 1.087]}, {"time": 11.1667, "x": 0.956, "y": 1.087, "curve": [11.389, 0.956, 11.611, 1.073, 11.389, 1.087, 11.611, 0.932]}, {"time": 11.8333, "x": 1.073, "y": 0.932, "curve": [12.056, 1.073, 12.278, 0.956, 12.056, 0.932, 12.278, 1.087]}, {"time": 12.5, "x": 0.956, "y": 1.087, "curve": [12.667, 0.956, 12.833, 1.021, 12.667, 1.087, 12.833, 1]}, {"time": 13, "x": 1.054, "y": 0.957}]}, "eyebrow_L": {"translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1.36, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 1.36, "curve": "stepped"}, {"time": 9.6667, "x": 1.36, "curve": [9.889, 1.36, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -4.04]}, {"time": 6.6667, "value": -4.04, "curve": "stepped"}, {"time": 9.6667, "value": -4.04, "curve": [9.889, -4.04, 10.111, 0]}, {"time": 10.3333}], "scale": [{"time": 6.3333, "curve": [6.444, 1, 6.556, 0.964, 6.444, 1, 6.556, 1]}, {"time": 6.6667, "x": 0.964, "curve": "stepped"}, {"time": 9.6667, "x": 0.964, "curve": [9.889, 0.964, 10.111, 1, 9.889, 1, 10.111, 1]}, {"time": 10.3333}]}, "eyebrow_L3": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, -4.67]}, {"time": 6.6667, "value": -4.67, "curve": "stepped"}, {"time": 9.6667, "value": -4.67, "curve": [9.889, -4.67, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 12.61]}, {"time": 6.6667, "value": 12.61, "curve": "stepped"}, {"time": 9.6667, "value": 12.61, "curve": [9.889, 12.61, 10.111, 0]}, {"time": 10.3333}], "translate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1.36, 6.444, 0, 6.556, 0]}, {"time": 6.6667, "x": 1.36, "curve": "stepped"}, {"time": 9.6667, "x": 1.36, "curve": [9.889, 1.36, 10.111, 0, 9.889, 0, 10.111, 0]}, {"time": 10.3333}], "scale": [{"time": 6.3333, "curve": [6.444, 1, 6.556, 0.964, 6.444, 1, 6.556, 1]}, {"time": 6.6667, "x": 0.964, "curve": "stepped"}, {"time": 9.6667, "x": 0.964, "curve": [9.889, 0.964, 10.111, 1, 9.889, 1, 10.111, 1]}, {"time": 10.3333}]}, "eyebrow_R2": {"rotate": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 9.2]}, {"time": 6.6667, "value": 9.2, "curve": "stepped"}, {"time": 9.6667, "value": 9.2, "curve": [9.889, 9.2, 10.111, 0]}, {"time": 10.3333}]}, "hair_L": {"rotate": [{"value": 1.77, "curve": [0.279, -1.16, 0.556, -5.05]}, {"time": 0.8333, "value": -5.05, "curve": [1.278, -5.05, 1.722, 4.95]}, {"time": 2.1667, "value": 4.95, "curve": [2.611, 4.95, 3.056, -5.05]}, {"time": 3.5, "value": -5.05, "curve": [3.944, -5.06, 4.389, 4.95]}, {"time": 4.8333, "value": 4.95, "curve": [5.278, 4.95, 5.722, -5.05]}, {"time": 6.1667, "value": -5.05, "curve": [6.5, -5.06, 6.833, 4.95]}, {"time": 7.1667, "value": 4.95, "curve": [7.611, 4.95, 8.056, -0.05]}, {"time": 8.5, "value": -0.05, "curve": [8.944, -0.05, 9.389, 4.95]}, {"time": 9.8333, "value": 4.95, "curve": [10.278, 4.95, 10.722, -5.05]}, {"time": 11.1667, "value": -5.05, "curve": [11.611, -5.06, 12.056, 4.95]}, {"time": 12.5, "value": 4.95, "curve": [12.667, 4.95, 12.835, 3.54]}, {"time": 13, "value": 1.77}]}, "hair_R": {"rotate": [{"value": -1.87, "curve": [0.279, 1.06, 0.556, 4.95]}, {"time": 0.8333, "value": 4.95, "curve": [1.278, 4.95, 1.722, -5.05]}, {"time": 2.1667, "value": -5.05, "curve": [2.611, -5.06, 3.056, 4.95]}, {"time": 3.5, "value": 4.95, "curve": [3.944, 4.95, 4.389, -5.05]}, {"time": 4.8333, "value": -5.05, "curve": [5.278, -5.05, 5.722, 4.95]}, {"time": 6.1667, "value": 4.95, "curve": [6.5, 4.95, 6.833, -5.05]}, {"time": 7.1667, "value": -5.05, "curve": [7.611, -5.06, 8.056, -0.05]}, {"time": 8.5, "value": -0.05, "curve": [8.944, -0.05, 9.389, -5.05]}, {"time": 9.8333, "value": -5.05, "curve": [10.278, -5.06, 10.722, 4.95]}, {"time": 11.1667, "value": 4.95, "curve": [11.611, 4.95, 12.056, -5.05]}, {"time": 12.5, "value": -5.05, "curve": [12.667, -5.05, 12.835, -3.64]}, {"time": 13, "value": -1.87}]}, "hair_B": {"rotate": [{"value": -0.42, "curve": [0.225, 1.68, 0.446, 3.82]}, {"time": 0.6667, "value": 3.82, "curve": [1.111, 3.82, 1.556, -4.66]}, {"time": 2, "value": -4.66, "curve": [2.444, -4.67, 2.889, 3.81]}, {"time": 3.3333, "value": 3.82, "curve": [3.778, 3.82, 4.222, -4.66]}, {"time": 4.6667, "value": -4.66, "curve": [5.111, -4.66, 5.556, 3.81]}, {"time": 6, "value": 3.82, "curve": [6.333, 3.82, 6.667, -4.66]}, {"time": 7, "value": -4.66, "curve": [7.444, -4.67, 7.889, -0.43]}, {"time": 8.3333, "value": -0.42, "curve": [8.778, -0.42, 9.222, -4.66]}, {"time": 9.6667, "value": -4.66, "curve": [10.111, -4.67, 10.556, 3.81]}, {"time": 11, "value": 3.82, "curve": [11.444, 3.82, 11.889, -4.66]}, {"time": 12.3333, "value": -4.66, "curve": [12.557, -4.66, 12.781, -2.56]}, {"time": 13, "value": -0.42}]}, "tunround": {"translate": [{"x": -241.28, "curve": [0.057, -256.35, 0.112, -267.84, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -267.84, "curve": [0.611, -267.84, 1.056, 297.08, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 297.23, "curve": [1.944, 297.37, 2.389, -267.7, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -267.84, "curve": [3.278, -267.98, 3.722, 297.23, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 297.23, "curve": [4.611, 297.23, 5.056, -267.65, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -267.84, "curve": [5.833, -267.98, 6.167, 297.17, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 297.23, "curve": [6.944, 297.3, 7.389, 14.76, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 14.69, "curve": [8.278, 14.62, 8.722, 297.08, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 297.23, "curve": [9.611, 297.37, 10.056, -267.7, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -267.84, "curve": [10.944, -267.98, 11.389, 297.23, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 297.23, "curve": [12.223, 297.23, 12.613, -134.16, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -241.28}]}, "bodyround": {"translate": [{"x": -84.69, "y": 145.73, "curve": [0.114, -107.09, 0.224, -123.78, 0.114, 188.38, 0.224, 220.17]}, {"time": 0.3333, "x": -123.78, "y": 220.17, "curve": [0.778, -123.78, 1.222, 120.46, 0.778, 220.17, 1.222, -244.98]}, {"time": 1.6667, "x": 120.52, "y": -245.1, "curve": [2.111, 120.58, 2.556, -123.72, 2.111, -245.21, 2.556, 220.05]}, {"time": 3, "x": -123.78, "y": 220.17, "curve": [3.444, -123.84, 3.889, 120.52, 3.444, 220.29, 3.889, -245.1]}, {"time": 4.3333, "x": 120.52, "y": -245.1, "curve": [4.778, 120.52, 5.222, -123.7, 4.778, -245.1, 5.222, 220.01]}, {"time": 5.6667, "x": -123.78, "y": 220.17, "curve": [6, -123.84, 6.333, 120.5, 6, 220.29, 6.333, -245.05]}, {"time": 6.6667, "x": 120.52, "y": -245.1, "curve": [7.111, 120.55, 7.556, -1.6, 7.111, -245.15, 7.556, -12.52]}, {"time": 8, "x": -1.63, "y": -12.46, "curve": [8.444, -1.66, 8.889, 120.46, 8.444, -12.41, 8.889, -244.98]}, {"time": 9.3333, "x": 120.52, "y": -245.1, "curve": [9.778, 120.58, 10.222, -123.72, 9.778, -245.21, 10.222, 220.05]}, {"time": 10.6667, "x": -123.78, "y": 220.17, "curve": [11.111, -123.84, 11.556, 120.52, 11.111, 220.29, 11.556, -245.1]}, {"time": 12, "x": 120.52, "y": -245.1, "curve": [12.335, 120.52, 12.67, -16.29, 12.335, -245.1, 12.67, 15.46]}, {"time": 13, "x": -84.69, "y": 145.73}]}, "headround3": {"translate": [{"x": 70.32, "curve": [0.279, -15.79, 0.556, -130.31, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -130.31, "curve": [1.278, -130.31, 1.722, 163.79, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 163.86, "curve": [2.611, 163.94, 3.056, -130.23, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -130.31, "curve": [3.944, -130.38, 4.389, 163.86, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 163.86, "curve": [5.278, 163.86, 5.722, -130.21, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -130.31, "curve": [6.5, -130.38, 6.833, 163.85, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 163.86, "curve": [7.611, 163.88, 8.056, 85.51, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 85.49, "curve": [8.944, 85.47, 9.389, 163.79, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 163.86, "curve": [10.278, 163.94, 10.722, -130.23, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -130.31, "curve": [11.611, -130.38, 12.056, 163.86, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 163.86, "curve": [12.667, 163.86, 12.835, 122.35, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 70.32}]}, "headround": {"translate": [{"y": -136.02, "curve": [0.336, 0, 0.668, 0, 0.336, -23.04, 0.668, 199.94]}, {"time": 1, "y": 199.94, "curve": [1.444, 0, 1.889, 0, 1.444, 199.94, 1.889, -199.91]}, {"time": 2.3333, "y": -200.01, "curve": [2.778, 0, 3.222, 0, 2.778, -200.11, 3.222, 199.84]}, {"time": 3.6667, "y": 199.94, "curve": [4.111, 0, 4.556, 0, 4.111, 200.04, 4.556, -200.01]}, {"time": 5, "y": -200.01, "curve": [5.444, 0, 5.889, 0, 5.444, -200.01, 5.889, 199.81]}, {"time": 6.3333, "y": 199.94, "curve": [6.667, 0, 7, 0, 6.667, 200.04, 7, -200]}, {"time": 7.3333, "y": -200.01, "curve": [7.778, 0, 8.222, 0, 7.778, -200.03, 8.222, -127.43]}, {"time": 8.6667, "y": -127.41, "curve": [9.111, 0, 9.556, 0, 9.111, -127.39, 9.556, -199.91]}, {"time": 10, "y": -200.01, "curve": [10.444, 0, 10.889, 0, 10.444, -200.11, 10.889, 199.84]}, {"time": 11.3333, "y": 199.94, "curve": [11.778, 0, 12.222, 0, 11.778, 200.04, 12.222, -200.01]}, {"time": 12.6667, "y": -200.01, "curve": [12.779, 0, 12.892, 0, 12.779, -200.01, 12.892, -174.35]}, {"time": 13, "y": -136.02}]}, "leg_L1": {"rotate": [{"value": -0.11, "curve": "stepped"}, {"time": 5.3333, "value": -0.11, "curve": [5.667, -0.11, 6, -0.11]}, {"time": 6.3333, "value": -0.11, "curve": [6.778, -0.11, 7.222, -0.34]}, {"time": 7.6667, "value": -0.34, "curve": [8.111, -0.34, 8.556, -0.11]}, {"time": 9, "value": -0.11}]}, "leg_L5": {"rotate": [{"value": 0.33, "curve": "stepped"}, {"time": 5.3333, "value": 0.33, "curve": [5.667, 0.33, 6, 0.33]}, {"time": 6.3333, "value": 0.33, "curve": [6.778, 0.33, 7.222, 0.91]}, {"time": 7.6667, "value": 0.91, "curve": [8.111, 0.91, 8.556, 0.33]}, {"time": 9, "value": 0.33}]}, "leg_L6": {"translate": [{"x": 24.81, "y": -0.1, "curve": [0.057, 26.49, 0.112, 27.78, 0.057, -0.18, 0.112, -0.24]}, {"time": 0.1667, "x": 27.78, "y": -0.24, "curve": [0.611, 27.78, 1.056, -35.53, 0.611, -0.24, 1.056, 2.75]}, {"time": 1.5, "x": -35.54, "y": 2.75, "curve": [1.944, -35.56, 2.389, 27.77, 1.944, 2.75, 2.389, -0.24]}, {"time": 2.8333, "x": 27.78, "y": -0.24, "curve": [3.278, 27.8, 3.722, -35.54, 3.278, -0.24, 3.722, 2.75]}, {"time": 4.1667, "x": -35.54, "y": 2.75, "curve": [4.611, -35.54, 5.056, 27.76, 4.611, 2.75, 5.056, -0.24]}, {"time": 5.5, "x": 27.78, "y": -0.24, "curve": [5.833, 27.8, 6.167, -35.54, 5.833, -0.24, 6.167, 2.75]}, {"time": 6.5, "x": -35.54, "y": 2.75, "curve": [6.944, -35.55, 7.389, -3.89, 6.944, 2.75, 7.389, 1.25]}, {"time": 7.8333, "x": -3.88, "y": 1.25, "curve": [8.278, -3.87, 8.722, -35.53, 8.278, 1.25, 8.722, 2.75]}, {"time": 9.1667, "x": -35.54, "y": 2.75, "curve": [9.611, -35.56, 10.056, 27.77, 9.611, 2.75, 10.056, -0.24]}, {"time": 10.5, "x": 27.78, "y": -0.24, "curve": [10.944, 27.8, 11.389, -35.54, 10.944, -0.24, 11.389, 2.75]}, {"time": 11.8333, "x": -35.54, "y": 2.75, "curve": [12.223, -35.54, 12.613, 12.8, 12.223, 2.75, 12.613, 0.47]}, {"time": 13, "x": 24.81, "y": -0.1}]}, "tun2": {"rotate": [{"value": -0.03, "curve": "stepped"}, {"time": 5.3333, "value": -0.03, "curve": [5.667, -0.03, 6, -0.03]}, {"time": 6.3333, "value": -0.03, "curve": [6.778, -0.03, 7.222, 0.66]}, {"time": 7.6667, "value": 0.66, "curve": [8.111, 0.66, 8.556, -0.03]}, {"time": 9, "value": -0.03}]}, "leg_R1": {"translate": [{"y": -8.75, "curve": [0.444, 0, 0.889, 0, 0.444, -8.75, 0.889, 5.84]}, {"time": 1.3333, "y": 5.84, "curve": [1.778, 0, 2.222, 0, 1.778, 5.84, 2.222, -8.75]}, {"time": 2.6667, "y": -8.75, "curve": [3.111, 0, 3.556, 0, 3.111, -8.76, 3.556, 5.84]}, {"time": 4, "y": 5.84, "curve": [4.444, 0, 4.889, 0, 4.444, 5.84, 4.889, -8.75]}, {"time": 5.3333, "y": -8.75, "curve": [5.667, 0, 6, 0, 5.667, -8.76, 6, 5.84]}, {"time": 6.3333, "y": 5.84, "curve": [6.778, 0, 7.222, 0, 6.778, 5.84, 7.222, -1.46]}, {"time": 7.6667, "y": -1.46, "curve": [8.111, 0, 8.556, 0, 8.111, -1.46, 8.556, 5.84]}, {"time": 9, "y": 5.84, "curve": [9.444, 0, 9.889, 0, 9.444, 5.84, 9.889, -8.75]}, {"time": 10.3333, "y": -8.75, "curve": [10.778, 0, 11.222, 0, 10.778, -8.76, 11.222, 5.84]}, {"time": 11.6667, "y": 5.84, "curve": [12.111, 0, 12.556, 0, 12.111, 5.84, 12.556, -8.75]}, {"time": 13, "y": -8.75}]}, "sh_L2": {"rotate": [{"value": 0.44, "curve": "stepped"}, {"time": 5.3333, "value": 0.44, "curve": [5.667, 0.44, 6, 0.44]}, {"time": 6.3333, "value": 0.44, "curve": [6.778, 0.44, 7.222, 1.16]}, {"time": 7.6667, "value": 1.16, "curve": [8.111, 1.16, 8.556, 0.44]}, {"time": 9, "value": 0.44}]}, "sh_L3": {"rotate": [{"value": -1.09, "curve": "stepped"}, {"time": 5.3333, "value": -1.09, "curve": [5.667, -1.09, 6, -1.1]}, {"time": 6.3333, "value": -1.09, "curve": [6.778, -1.09, 7.222, -4.14]}, {"time": 7.6667, "value": -4.14, "curve": [8.111, -4.14, 8.556, -1.09]}, {"time": 9, "value": -1.09}]}, "arm_L4": {"translate": [{"x": -2.19, "y": -3.14, "curve": [0.114, -4.17, 0.224, -5.65, 0.114, -5.09, 0.224, -6.54]}, {"time": 0.3333, "x": -5.65, "y": -6.54, "curve": [0.778, -5.65, 1.222, 15.98, 0.778, -6.54, 1.222, 14.65]}, {"time": 1.6667, "x": 15.98, "y": 14.66, "curve": [2.111, 15.99, 2.556, -5.65, 2.111, 14.66, 2.556, -6.53]}, {"time": 3, "x": -5.65, "y": -6.54, "curve": [3.444, -5.66, 3.889, 15.98, 3.444, -6.54, 3.889, 14.66]}, {"time": 4.3333, "x": 15.98, "y": 14.66, "curve": [4.778, 15.98, 5.222, -5.64, 4.778, 14.66, 5.222, -6.53]}, {"time": 5.6667, "x": -5.65, "y": -6.54, "curve": [6, -5.66, 6.333, 15.98, 6, -6.54, 6.333, 14.66]}, {"time": 6.6667, "x": 15.98, "y": 14.66, "curve": [7.111, 15.99, 7.556, 5.17, 7.111, 14.66, 7.556, 4.06]}, {"time": 8, "x": 5.17, "y": 4.06, "curve": [8.444, 5.16, 8.889, 15.98, 8.444, 4.06, 8.889, 14.65]}, {"time": 9.3333, "x": 15.98, "y": 14.66, "curve": [9.778, 15.99, 10.222, -5.65, 9.778, 14.66, 10.222, -6.53]}, {"time": 10.6667, "x": -5.65, "y": -6.54, "curve": [11.111, -5.66, 11.556, 15.98, 11.111, -6.54, 11.556, 14.66]}, {"time": 12, "x": 15.98, "y": 14.66, "curve": [12.335, 15.98, 12.67, 3.87, 12.335, 14.66, 12.67, 2.79]}, {"time": 13, "x": -2.19, "y": -3.14}]}, "sh_R2": {"rotate": [{"value": -0.04, "curve": "stepped"}, {"time": 5.3333, "value": -0.04, "curve": [5.667, -0.04, 6, -0.04]}, {"time": 6.3333, "value": -0.04, "curve": [6.778, -0.04, 7.222, -0.32]}, {"time": 7.6667, "value": -0.32, "curve": [8.111, -0.32, 8.556, -0.04]}, {"time": 9, "value": -0.04}]}, "sh_R3": {"rotate": [{"value": -0.08, "curve": "stepped"}, {"time": 6.3333, "value": -0.08, "curve": [6.778, -0.08, 7.222, -0.08]}, {"time": 7.6667, "value": -0.08, "curve": [8.111, -0.08, 8.556, -0.08]}, {"time": 9, "value": -0.08}]}, "arm_R6": {"translate": [{"x": -3.49, "y": -2.22, "curve": [0.168, -7.39, 0.334, -10.59, 0.168, -4.27, 0.334, -5.95]}, {"time": 0.5, "x": -10.59, "y": -5.95, "curve": [0.944, -10.59, 1.389, 11.73, 0.944, -5.95, 1.389, 5.77]}, {"time": 1.8333, "x": 11.74, "y": 5.77, "curve": [2.278, 11.74, 2.722, -10.58, 2.278, 5.78, 2.722, -5.95]}, {"time": 3.1667, "x": -10.59, "y": -5.95, "curve": [3.611, -10.59, 4.056, 11.74, 3.611, -5.96, 4.056, 5.77]}, {"time": 4.5, "x": 11.74, "y": 5.77, "curve": [4.944, 11.74, 5.389, -10.58, 4.944, 5.77, 5.389, -5.95]}, {"time": 5.8333, "x": -10.59, "y": -5.95, "curve": [6.167, -10.59, 6.5, 11.74, 6.167, -5.96, 6.5, 5.77]}, {"time": 6.8333, "x": 11.74, "y": 5.77, "curve": [7.278, 11.74, 7.722, 0.58, 7.278, 5.77, 7.722, -0.09]}, {"time": 8.1667, "x": 0.58, "y": -0.09, "curve": [8.611, 0.57, 9.056, 11.73, 8.611, -0.09, 9.056, 5.77]}, {"time": 9.5, "x": 11.74, "y": 5.77, "curve": [9.944, 11.74, 10.389, -10.58, 9.944, 5.78, 10.389, -5.95]}, {"time": 10.8333, "x": -10.59, "y": -5.95, "curve": [11.278, -10.59, 11.722, 11.74, 11.278, -5.96, 11.722, 5.77]}, {"time": 12.1667, "x": 11.74, "y": 5.77, "curve": [12.445, 11.74, 12.724, 3.06, 12.445, 5.77, 12.724, 1.21]}, {"time": 13, "x": -3.49, "y": -2.22}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -0.8, 2.033, 0, 2.067, 0.75]}, {"time": 2.1, "x": -0.8, "y": 0.75, "curve": [2.511, -0.8, 2.922, -0.8, 2.511, 0.75, 2.922, 0.75]}, {"time": 3.3333, "x": -0.8, "y": 0.75, "curve": [3.367, -0.8, 3.4, -0.5, 3.367, 0.75, 3.4, 1.42]}, {"time": 3.4333, "x": -0.5, "y": 1.42, "curve": [3.567, -0.5, 3.7, -0.5, 3.567, 1.42, 3.7, 1.42]}, {"time": 3.8333, "x": -0.5, "y": 1.42, "curve": [3.867, -0.5, 3.9, 0, 3.867, 1.42, 3.9, 0]}, {"time": 3.9333, "curve": [4.789, 0, 5.644, -0.01, 4.789, 0, 5.644, -0.01]}, {"time": 6.5, "curve": [6.533, 0, 6.567, -1.04, 6.533, 0, 6.567, -1.65]}, {"time": 6.6, "x": -1.04, "y": -1.65, "curve": [6.844, -1.04, 7.089, -1.04, 6.844, -1.65, 7.089, -1.65]}, {"time": 7.3333, "x": -1.04, "y": -1.65, "curve": [7.367, -1.04, 7.4, -2.34, 7.367, -1.65, 7.4, -2.44]}, {"time": 7.4333, "x": -2.34, "y": -2.44, "curve": [7.756, -2.34, 8.078, -2.33, 7.756, -2.44, 8.078, -2.44]}, {"time": 8.4, "x": -2.34, "y": -2.44, "curve": [8.433, -2.34, 8.467, -0.78, 8.433, -2.44, 8.467, -0.1]}, {"time": 8.5, "x": -0.78, "y": -0.1, "curve": [8.611, -0.78, 8.722, -0.78, 8.611, -0.1, 8.722, -0.11]}, {"time": 8.8333, "x": -0.78, "y": -0.1, "curve": [8.867, -0.78, 8.9, -1.04, 8.867, -0.1, 8.9, -1.65]}, {"time": 8.9333, "x": -1.04, "y": -1.65, "curve": [9.289, -1.04, 9.644, -1.04, 9.289, -1.65, 9.644, -1.65]}, {"time": 10, "x": -1.04, "y": -1.65, "curve": [10.033, -1.04, 10.067, 0, 10.033, -1.65, 10.067, 0]}, {"time": 10.1}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -0.8, 2.033, 0, 2.067, 1.19]}, {"time": 2.1, "x": -0.8, "y": 1.19, "curve": [2.511, -0.8, 2.922, -0.8, 2.511, 1.19, 2.922, 1.19]}, {"time": 3.3333, "x": -0.8, "y": 1.19, "curve": [3.367, -0.8, 3.4, -0.5, 3.367, 1.19, 3.4, 1.85]}, {"time": 3.4333, "x": -0.5, "y": 1.85, "curve": [3.567, -0.5, 3.7, -0.5, 3.567, 1.85, 3.7, 1.85]}, {"time": 3.8333, "x": -0.5, "y": 1.85, "curve": [3.867, -0.5, 3.9, 0, 3.867, 1.85, 3.9, 0]}, {"time": 3.9333, "curve": [4.789, 0, 5.644, -0.01, 4.789, 0, 5.644, -0.01]}, {"time": 6.5, "curve": [6.533, 0, 6.567, -1.04, 6.533, 0, 6.567, -1.65]}, {"time": 6.6, "x": -1.04, "y": -1.65, "curve": [6.844, -1.04, 7.089, -1.04, 6.844, -1.65, 7.089, -1.65]}, {"time": 7.3333, "x": -1.04, "y": -1.65, "curve": [7.367, -1.04, 7.4, -2.34, 7.367, -1.65, 7.4, -2.44]}, {"time": 7.4333, "x": -2.34, "y": -2.44, "curve": [7.756, -2.34, 8.078, -2.33, 7.756, -2.44, 8.078, -2.44]}, {"time": 8.4, "x": -2.34, "y": -2.44, "curve": [8.433, -2.34, 8.467, -0.78, 8.433, -2.44, 8.467, -0.1]}, {"time": 8.5, "x": -0.78, "y": -0.1, "curve": [8.611, -0.78, 8.722, -0.78, 8.611, -0.1, 8.722, -0.11]}, {"time": 8.8333, "x": -0.78, "y": -0.1, "curve": [8.867, -0.78, 8.9, -1.04, 8.867, -0.1, 8.9, -1.65]}, {"time": 8.9333, "x": -1.04, "y": -1.65, "curve": [9.289, -1.04, 9.644, -1.04, 9.289, -1.65, 9.644, -1.65]}, {"time": 10, "x": -1.04, "y": -1.65, "curve": [10.033, -1.04, 10.067, 0, 10.033, -1.65, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-11.2804, 0.42801, -11.28516, 0.42844, -6.21863, 0.00769, -6.22327, 0.00781, -6.21863, 0.00769, -6.22327, 0.00781, -4.49646, -0.09012, -4.50012, -0.0899, -3.02539, 0.11142, -3.02759, 0.11124, -1.76978, -0.14127, -1.77075, -0.1416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.63379, -0.05899, -0.63428, -0.05911, -5.57373, -0.18509, -5.57715, -0.18491, -9.68811, -0.22812, -9.69458, -0.22794, -11.80615, 0.09113, -11.80798, 0.09137, -15.48999, -0.11517, -15.4928, -0.11508, -14.35364, -0.22592, -14.35596, -0.22571, 0, 0, 0, 0, -4.62439, 0.11481, -4.62659, 0.11502, -9.01013, 0.5079, -9.01245, 0.50769, -12.16321, 0.14273, -12.16553, 0.1427, -13.49768, 0.59488, -13.49866, 0.59467, -12.46716, -0.06897, -12.47021, -0.06836, -9.42847, -0.15338, -9.43225, -0.15179, -4.58606, -0.3511, -4.58752, -0.35025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.60352, -0.03741, -0.60486, -0.03699, -2.44958, -0.13251, -2.45203, -0.1315], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-11.2804, 0.42801, -11.28516, 0.42844, -6.21863, 0.00769, -6.22327, 0.00781, -6.21863, 0.00769, -6.22327, 0.00781, -4.49646, -0.09012, -4.50012, -0.0899, -3.02539, 0.11142, -3.02759, 0.11124, -1.76978, -0.14127, -1.77075, -0.1416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.63379, -0.05899, -0.63428, -0.05911, -5.57373, -0.18509, -5.57715, -0.18491, -9.68811, -0.22812, -9.69458, -0.22794, -11.80615, 0.09113, -11.80798, 0.09137, -15.48999, -0.11517, -15.4928, -0.11508, -14.35364, -0.22592, -14.35596, -0.22571, 0, 0, 0, 0, -4.62439, 0.11481, -4.62659, 0.11502, -9.01013, 0.5079, -9.01245, 0.50769, -12.16321, 0.14273, -12.16553, 0.1427, -13.49768, 0.59488, -13.49866, 0.59467, -12.46716, -0.06897, -12.47021, -0.06836, -9.42847, -0.15338, -9.43225, -0.15179, -4.58606, -0.3511, -4.58752, -0.35025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.60352, -0.03741, -0.60486, -0.03699, -2.44958, -0.13251, -2.45203, -0.1315], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-11.2804, 0.42801, -11.28516, 0.42844, -6.21863, 0.00769, -6.22327, 0.00781, -6.21863, 0.00769, -6.22327, 0.00781, -4.49646, -0.09012, -4.50012, -0.0899, -3.02539, 0.11142, -3.02759, 0.11124, -1.76978, -0.14127, -1.77075, -0.1416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.63379, -0.05899, -0.63428, -0.05911, -5.57373, -0.18509, -5.57715, -0.18491, -9.68811, -0.22812, -9.69458, -0.22794, -11.80615, 0.09113, -11.80798, 0.09137, -15.48999, -0.11517, -15.4928, -0.11508, -14.35364, -0.22592, -14.35596, -0.22571, 0, 0, 0, 0, -4.62439, 0.11481, -4.62659, 0.11502, -9.01013, 0.5079, -9.01245, 0.50769, -12.16321, 0.14273, -12.16553, 0.1427, -13.49768, 0.59488, -13.49866, 0.59467, -12.46716, -0.06897, -12.47021, -0.06836, -9.42847, -0.15338, -9.43225, -0.15179, -4.58606, -0.3511, -4.58752, -0.35025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.60352, -0.03741, -0.60486, -0.03699, -2.44958, -0.13251, -2.45203, -0.1315], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-12.12207, 1.01761, -12.12048, 1.01749, -11.89392, 1.06967, -11.89197, 1.06927, -11.78052, 1.03055, -11.77881, 1.03024, -11.3092, 0.98392, -11.30688, 0.98343, -8.55969, 0.61151, -8.55908, 0.61087, -3.06726, -0.19055, -3.06714, -0.1908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.77075, -0.18539, -1.77087, -0.18546, -3.42078, -0.07669, -3.42346, -0.0769, -3.49805, -0.54535, -3.50037, -0.54495, -5.17847, -0.6235, -5.18225, -0.62302, -9.66003, -0.22992, -9.66357, -0.22934, -6.20813, 0.96646, -6.21362, 0.96713, -10.38037, 0.86252, -10.3844, 0.86313, -12.33386, 1.09457, -12.33289, 1.09442, -11.55774, 1.00208, -11.55591, 1.00171, -9.06763, 0.45578, -9.07056, 0.4563, -4.5907, 0.01495, -4.59131, 0.01477, 0, 0, 0, 0, -2.65051, 0.06195, -2.65198, 0.06213, -0.71387, 0.04947, -0.71558, 0.04974], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "vertices": [-12.12207, 1.01761, -12.12048, 1.01749, -11.89392, 1.06967, -11.89197, 1.06927, -11.78052, 1.03055, -11.77881, 1.03024, -11.3092, 0.98392, -11.30688, 0.98343, -8.55969, 0.61151, -8.55908, 0.61087, -3.06726, -0.19055, -3.06714, -0.1908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.77075, -0.18539, -1.77087, -0.18546, -3.42078, -0.07669, -3.42346, -0.0769, -3.49805, -0.54535, -3.50037, -0.54495, -5.17847, -0.6235, -5.18225, -0.62302, -9.66003, -0.22992, -9.66357, -0.22934, -6.20813, 0.96646, -6.21362, 0.96713, -10.38037, 0.86252, -10.3844, 0.86313, -12.33386, 1.09457, -12.33289, 1.09442, -11.55774, 1.00208, -11.55591, 1.00171, -9.06763, 0.45578, -9.07056, 0.4563, -4.5907, 0.01495, -4.59131, 0.01477, 0, 0, 0, 0, -2.65051, 0.06195, -2.65198, 0.06213, -0.71387, 0.04947, -0.71558, 0.04974], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-12.12207, 1.01761, -12.12048, 1.01749, -11.89392, 1.06967, -11.89197, 1.06927, -11.78052, 1.03055, -11.77881, 1.03024, -11.3092, 0.98392, -11.30688, 0.98343, -8.55969, 0.61151, -8.55908, 0.61087, -3.06726, -0.19055, -3.06714, -0.1908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.77075, -0.18539, -1.77087, -0.18546, -3.42078, -0.07669, -3.42346, -0.0769, -3.49805, -0.54535, -3.50037, -0.54495, -5.17847, -0.6235, -5.18225, -0.62302, -9.66003, -0.22992, -9.66357, -0.22934, -6.20813, 0.96646, -6.21362, 0.96713, -10.38037, 0.86252, -10.3844, 0.86313, -12.33386, 1.09457, -12.33289, 1.09442, -11.55774, 1.00208, -11.55591, 1.00171, -9.06763, 0.45578, -9.07056, 0.4563, -4.5907, 0.01495, -4.59131, 0.01477, 0, 0, 0, 0, -2.65051, 0.06195, -2.65198, 0.06213, -0.71387, 0.04947, -0.71558, 0.04974], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 148, "vertices": [-2.4873, 0.50052, -2.48889, 0.50089, -7.87549, -0.08066, -7.87708, -0.08014, -11.10986, -0.07724, -11.11206, -0.07632, -11.55481, 0.01218, -11.55591, 0.01285, -10.04346, 0.17065, -10.0448, 0.17133, -6.32825, 0.25562, -6.3291, 0.25604, -2.27502, -0.28201, -2.27649, -0.28156, -0.74646, -0.03476, -0.74695, -0.03461, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.34827, -0.4592, -3.34717, -0.45935, -7.82458, 0.15805, -7.82471, 0.15826, -10.57288, 0.56946, -10.57324, 0.56937, -11.33521, 0.6517, -11.33594, 0.65198, -9.30786, -0.18054, -9.30676, -0.1806, -4.23218, 0.0726, -4.23145, 0.07248, -1.70081, -0.01178, -1.70044, -0.01193], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6.3333, "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "offset": 148, "vertices": [-2.4873, 0.50052, -2.48889, 0.50089, -7.87549, -0.08066, -7.87708, -0.08014, -11.10986, -0.07724, -11.11206, -0.07632, -11.55481, 0.01218, -11.55591, 0.01285, -10.04346, 0.17065, -10.0448, 0.17133, -6.32825, 0.25562, -6.3291, 0.25604, -2.27502, -0.28201, -2.27649, -0.28156, -0.74646, -0.03476, -0.74695, -0.03461, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.34827, -0.4592, -3.34717, -0.45935, -7.82458, 0.15805, -7.82471, 0.15826, -10.57288, 0.56946, -10.57324, 0.56937, -11.33521, 0.6517, -11.33594, 0.65198, -9.30786, -0.18054, -9.30676, -0.1806, -4.23218, 0.0726, -4.23145, 0.07248, -1.70081, -0.01178, -1.70044, -0.01193], "curve": [6.556, 0, 6.611, 1]}, {"time": 6.6667, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 148, "vertices": [-2.4873, 0.50052, -2.48889, 0.50089, -7.87549, -0.08066, -7.87708, -0.08014, -11.10986, -0.07724, -11.11206, -0.07632, -11.55481, 0.01218, -11.55591, 0.01285, -10.04346, 0.17065, -10.0448, 0.17133, -6.32825, 0.25562, -6.3291, 0.25604, -2.27502, -0.28201, -2.27649, -0.28156, -0.74646, -0.03476, -0.74695, -0.03461, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.34827, -0.4592, -3.34717, -0.45935, -7.82458, 0.15805, -7.82471, 0.15826, -10.57288, 0.56946, -10.57324, 0.56937, -11.33521, 0.6517, -11.33594, 0.65198, -9.30786, -0.18054, -9.30676, -0.1806, -4.23218, 0.0726, -4.23145, 0.07248, -1.70081, -0.01178, -1.70044, -0.01193], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6.3333, "curve": [6.444, 0, 6.556, 1]}, {"time": 6.6667, "offset": 64, "vertices": [-2.1012, -0.02356, -2.10059, -0.02355, -1.39038, -0.01559, -1.3894, -0.01559, -1.67493, -0.01878, -1.67432, -0.01878, -1.02356, -0.01147, -1.02368, -0.01148, 0.10901, 0.14852, 0.10742, 0.14853, 1.03796, -0.5149, 1.03564, -0.51492, -2.50586, -0.40033, -2.5061, -0.40031, -1.14465, -0.34104, -1.14502, -0.341, 0.24878, 0.39247, 0.24609, 0.39247, 1.3822, 0.68932, 1.37915, 0.68933, 0.57153, -0.06176, 0.57104, -0.06177, 1.09131, -0.05593, 1.09082, -0.05594, 1.29578, -0.05364, 1.29517, -0.05366, 1.09131, -0.05593, 1.09082, -0.05594, 0.66418, 0.03292, 0.66333, 0.03291, 1.33508, -0.24831, 1.33374, -0.24831, 2.10278, -0.43193, 2.10034, -0.43194, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.14783, -0.25041, 1.14673, -0.25041, 0.14209, 0.00159, 0.14209, 0.00159, -1.1167, -0.01252, -1.11621, -0.01252, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, 0.70117, 0.10147, 0.69971, 0.10146, 1.12134, 0.19978, 1.1189, 0.19978, 0.60754, 0.10042, 0.6062, 0.10041, 0.26245, 0.06876, 0.26172, 0.06875, 0.67322, 0.53931, 0.67065, 0.5393, 1.6272, -0.07541, 1.62524, -0.07542, 2.1051, -0.13822, 2.10303, -0.13822, 2.04443, -0.07074, 2.04199, -0.07075, 1.24634, 0.01396, 1.24561, 0.01397, 0.8385, -0.27151, 0.83691, -0.27153, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 0, 0, 0, 0, 0.65698, -0.12613, 0.65674, -0.12612, 0.65698, -0.12613, 0.65674, -0.12612, 0, 0, 0, 0, 0, 0, 0, 0, 0.66602, 0.14095, 0.66602, 0.14096, 0.66602, 0.14095, 0.66602, 0.14096], "curve": "stepped"}, {"time": 9.6667, "offset": 64, "vertices": [-2.1012, -0.02356, -2.10059, -0.02355, -1.39038, -0.01559, -1.3894, -0.01559, -1.67493, -0.01878, -1.67432, -0.01878, -1.02356, -0.01147, -1.02368, -0.01148, 0.10901, 0.14852, 0.10742, 0.14853, 1.03796, -0.5149, 1.03564, -0.51492, -2.50586, -0.40033, -2.5061, -0.40031, -1.14465, -0.34104, -1.14502, -0.341, 0.24878, 0.39247, 0.24609, 0.39247, 1.3822, 0.68932, 1.37915, 0.68933, 0.57153, -0.06176, 0.57104, -0.06177, 1.09131, -0.05593, 1.09082, -0.05594, 1.29578, -0.05364, 1.29517, -0.05366, 1.09131, -0.05593, 1.09082, -0.05594, 0.66418, 0.03292, 0.66333, 0.03291, 1.33508, -0.24831, 1.33374, -0.24831, 2.10278, -0.43193, 2.10034, -0.43194, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.14783, -0.25041, 1.14673, -0.25041, 0.14209, 0.00159, 0.14209, 0.00159, -1.1167, -0.01252, -1.11621, -0.01252, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, 0.70117, 0.10147, 0.69971, 0.10146, 1.12134, 0.19978, 1.1189, 0.19978, 0.60754, 0.10042, 0.6062, 0.10041, 0.26245, 0.06876, 0.26172, 0.06875, 0.67322, 0.53931, 0.67065, 0.5393, 1.6272, -0.07541, 1.62524, -0.07542, 2.1051, -0.13822, 2.10303, -0.13822, 2.04443, -0.07074, 2.04199, -0.07075, 1.24634, 0.01396, 1.24561, 0.01397, 0.8385, -0.27151, 0.83691, -0.27153, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 0, 0, 0, 0, 0.65698, -0.12613, 0.65674, -0.12612, 0.65698, -0.12613, 0.65674, -0.12612, 0, 0, 0, 0, 0, 0, 0, 0, 0.66602, 0.14095, 0.66602, 0.14096, 0.66602, 0.14095, 0.66602, 0.14096], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": -7.98, "curve": [0.024, -7.81, 0.059, 27.6, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 27.76, "curve": [0.544, 28.06, 0.856, -10.07, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -7.98}]}, "ALL2": {"translate": [{"y": -10.31, "curve": [0.078, 0, 0.156, 0, 0.024, -10.18, 0.059, 18.43]}, {"time": 0.2333, "y": 18.56, "curve": [0.544, 0, 0.856, 0, 0.544, 18.8, 0.856, -12.01]}, {"time": 1.1667, "y": -10.31}]}, "body": {"rotate": [{"value": -2.76, "curve": [0.024, -2.73, 0.059, 3.94]}, {"time": 0.2333, "value": 3.97, "curve": [0.544, 4.03, 0.856, -3.16]}, {"time": 1.1667, "value": -2.76}], "translate": [{"x": 0.71, "y": -10.28, "curve": [0.024, 0.7, 0.059, -2.03, 0.024, -10.15, 0.059, 16.21]}, {"time": 0.2333, "x": -2.04, "y": 16.33, "curve": [0.544, -2.06, 0.856, 0.87, 0.544, 16.55, 0.856, -11.84]}, {"time": 1.1667, "x": 0.71, "y": -10.28}], "scale": [{"y": 1.036, "curve": [0.078, 1, 0.156, 1, 0.024, 1.036, 0.059, 0.956]}, {"time": 0.2333, "y": 0.956, "curve": [0.544, 1, 0.856, 1, 0.544, 0.955, 0.856, 1.041]}, {"time": 1.1667, "y": 1.036}]}, "body2": {"rotate": [{"value": -1.12, "curve": [0.028, -1.09, 0.067, 5.89]}, {"time": 0.2667, "value": 5.93, "curve": [0.567, 5.98, 0.867, -1.52]}, {"time": 1.1667, "value": -1.12}], "translate": [{"x": -7.26, "curve": [0.028, -7.16, 0.067, 11.52, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 11.62, "curve": [0.567, 11.77, 0.867, -8.33, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -7.26}], "scale": [{"x": 1.002, "y": 1.002, "curve": [0.028, 1.002, 0.067, 1.01, 0.028, 1.002, 0.067, 1.01]}, {"time": 0.2667, "x": 1.01, "y": 1.01, "curve": [0.567, 1.01, 0.867, 1.001, 0.567, 1.01, 0.867, 1.001]}, {"time": 1.1667, "x": 1.002, "y": 1.002}]}, "neck": {"rotate": [{"value": 0.78, "curve": [0.031, 0.71, 0.076, -11.16]}, {"time": 0.3, "value": -11.23, "curve": [0.589, -11.32, 0.878, 1.44]}, {"time": 1.1667, "value": 0.78}]}, "head": {"rotate": [{"value": 0.13, "curve": [0.031, 0.06, 0.076, -11.22]}, {"time": 0.3, "value": -11.23, "curve": [0.589, -11.25, 0.878, 0.75]}, {"time": 1.1667, "value": 0.13}]}, "tun": {"rotate": [{"value": 2.33, "curve": [0.024, 2.29, 0.059, -6.29]}, {"time": 0.2333, "value": -6.33, "curve": [0.544, -6.4, 0.856, 2.84]}, {"time": 1.1667, "value": 2.33}]}, "leg_R": {"rotate": [{}]}, "leg_R2": {"rotate": [{}]}, "leg_L0": {"translate": [{"y": -12.16, "curve": [0.078, 0, 0.156, 0, 0.024, -12.16, 0.059, 35.37]}, {"time": 0.2333, "y": 35.38, "curve": [0.544, 0, 0.856, 0, 0.544, 35.39, 0.856, -12.16]}, {"time": 1.1667, "y": -12.16}]}, "leg_L": {"rotate": [{}]}, "leg_L2": {"rotate": [{}]}, "leg_L3": {"rotate": [{"value": -3.03, "curve": [0.05, -3.92, 0.085, -4.58]}, {"time": 0.1333, "value": -4.58, "curve": [0.329, -4.58, 0.538, 5.1]}, {"time": 0.7333, "value": 5.1, "curve": [0.881, 5.1, 1.021, -0.32]}, {"time": 1.1667, "value": -3.03}]}, "leg_L4": {"rotate": [{"value": -1.5, "curve": [0.074, -3.19, 0.16, -4.58]}, {"time": 0.2333, "value": -4.58, "curve": [0.429, -4.58, 0.604, 5.1]}, {"time": 0.8, "value": 5.1, "curve": [0.923, 5.1, 1.045, 1.34]}, {"time": 1.1667, "value": -1.5}]}, "sh_L": {"translate": [{"x": -3.02, "y": -1.77, "curve": [0.028, -2.94, 0.067, 11.03, 0.028, -1.73, 0.067, 5.07]}, {"time": 0.2667, "x": 11.1, "y": 5.11, "curve": [0.567, 11.21, 0.867, -3.82, 0.567, 5.16, 0.867, -2.16]}, {"time": 1.1667, "x": -3.02, "y": -1.77}]}, "sh_R": {"translate": [{"x": -3.02, "y": -1.77, "curve": [0.028, -2.93, 0.067, 13.26, 0.028, -1.73, 0.067, 6]}, {"time": 0.2667, "x": 13.35, "y": 6.04, "curve": [0.567, 13.48, 0.867, -3.94, 0.567, 6.11, 0.867, -2.21]}, {"time": 1.1667, "x": -3.02, "y": -1.77}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "arm_L3": {"rotate": [{"value": -3.79, "curve": [0.024, -3.72, 0.059, 11.7]}, {"time": 0.2333, "value": 11.78, "curve": [0.544, 11.9, 0.856, -4.7]}, {"time": 1.1667, "value": -3.79}]}, "arm_R2": {"rotate": [{"value": 0.01}]}, "arm_R3": {"rotate": [{"value": 0.53, "curve": [0.099, -1.99, 0.203, -4.55]}, {"time": 0.3, "value": -4.55, "curve": [0.496, -4.55, 0.671, 5.6]}, {"time": 0.8667, "value": 5.6, "curve": [0.965, 5.61, 1.07, 3.09]}, {"time": 1.1667, "value": 0.53}]}, "arm_R4": {"rotate": [{"value": 2.38, "curve": [0.123, -0.59, 0.245, -4.55]}, {"time": 0.3667, "value": -4.55, "curve": [0.562, -4.55, 0.771, 5.6]}, {"time": 0.9667, "value": 5.6, "curve": [1.04, 5.61, 1.094, 4.17]}, {"time": 1.1667, "value": 2.38}]}, "arm_R5": {"rotate": [{"value": 3.98, "curve": [0.148, 1.11, 0.287, -4.54]}, {"time": 0.4333, "value": -4.55, "curve": [0.629, -4.55, 0.838, 5.6]}, {"time": 1.0333, "value": 5.6, "curve": [1.083, 5.61, 1.119, 4.95]}, {"time": 1.1667, "value": 3.98}]}, "RU_L": {"translate": [{"x": -6.45, "y": -0.81, "curve": [0.074, -18.01, 0.16, -27.49, 0.074, -2.25, 0.16, -3.44]}, {"time": 0.2333, "x": -27.49, "y": -3.44, "curve": [0.429, -27.49, 0.604, 38.65, 0.429, -3.44, 0.604, 4.83]}, {"time": 0.8, "x": 38.67, "y": 4.83, "curve": [0.923, 38.68, 1.045, 12.96, 0.923, 4.84, 1.045, 1.62]}, {"time": 1.1667, "x": -6.44, "y": -0.8}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.073, 1.021, 0.16, 0.956, 0.073, 1, 0.16, 1.087]}, {"time": 0.2333, "x": 0.956, "y": 1.087, "curve": [0.331, 0.956, 0.402, 1.073, 0.331, 1.087, 0.402, 0.932]}, {"time": 0.5, "x": 1.073, "y": 0.932, "curve": [0.598, 1.073, 0.702, 0.956, 0.598, 0.932, 0.702, 1.087]}, {"time": 0.8, "x": 0.956, "y": 1.087, "curve": [0.898, 0.956, 1.002, 1.073, 0.898, 1.087, 1.002, 0.932]}, {"time": 1.1, "x": 1.073, "y": 0.932, "curve": [1.125, 1.073, 1.143, 1.065, 1.125, 0.932, 1.143, 0.942]}, {"time": 1.1667, "x": 1.054, "y": 0.957}]}, "RU_L2": {"translate": [{"x": 1.04, "y": 0.18, "curve": [0.099, -15.06, 0.203, -31.37, 0.099, -2.63, 0.203, -5.49]}, {"time": 0.3, "x": -31.37, "y": -5.49, "curve": [0.496, -31.37, 0.671, 33.44, 0.496, -5.49, 0.671, 5.85]}, {"time": 0.8667, "x": 33.45, "y": 5.85, "curve": [0.965, 33.46, 1.07, 17.37, 0.965, 5.85, 1.07, 3.04]}, {"time": 1.1667, "x": 1.05, "y": 0.18}], "scale": [{"x": 1.073, "y": 0.932, "curve": [0.098, 1.073, 0.202, 0.956, 0.098, 0.932, 0.202, 1.087]}, {"time": 0.3, "x": 0.956, "y": 1.087, "curve": [0.398, 0.956, 0.502, 1.073, 0.398, 1.087, 0.502, 0.932]}, {"time": 0.6, "x": 1.073, "y": 0.932, "curve": [0.698, 1.073, 0.769, 0.956, 0.698, 0.932, 0.769, 1.087]}, {"time": 0.8667, "x": 0.956, "y": 1.087, "curve": [0.965, 0.956, 1.069, 1.073, 0.965, 1.087, 1.069, 0.932]}, {"time": 1.1667, "x": 1.073, "y": 0.932}]}, "RU_L3": {"translate": [{"x": 12.7, "curve": [0.123, -3.85, 0.245, -25.86, 0.123, 0, 0.245, 0]}, {"time": 0.3667, "x": -25.86, "curve": [0.562, -25.86, 0.771, 30.67, 0.562, 0, 0.771, 0]}, {"time": 0.9667, "x": 30.68, "curve": [1.04, 30.69, 1.094, 22.71, 1.04, 0, 1.094, 0]}, {"time": 1.1667, "x": 12.71}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.024, 1.065, 0.042, 1.073, 0.024, 0.942, 0.042, 0.932]}, {"time": 0.0667, "x": 1.073, "y": 0.932, "curve": [0.165, 1.073, 0.269, 0.956, 0.165, 0.932, 0.269, 1.087]}, {"time": 0.3667, "x": 0.956, "y": 1.087, "curve": [0.465, 0.956, 0.569, 1.073, 0.465, 1.087, 0.569, 0.932]}, {"time": 0.6667, "x": 1.073, "y": 0.932, "curve": [0.765, 1.073, 0.869, 0.956, 0.765, 0.932, 0.869, 1.087]}, {"time": 0.9667, "x": 0.956, "y": 1.087, "curve": [1.04, 0.956, 1.094, 1.021, 1.04, 1.087, 1.094, 1]}, {"time": 1.1667, "x": 1.054, "y": 0.957}]}, "RU_R": {"translate": [{"x": -6.45, "y": -0.81, "curve": [0.074, -18.01, 0.16, -27.49, 0.074, -2.25, 0.16, -3.44]}, {"time": 0.2333, "x": -27.49, "y": -3.44, "curve": [0.429, -27.49, 0.604, 38.65, 0.429, -3.44, 0.604, 4.83]}, {"time": 0.8, "x": 38.67, "y": 4.83, "curve": [0.923, 38.68, 1.045, 12.96, 0.923, 4.84, 1.045, 1.62]}, {"time": 1.1667, "x": -6.44, "y": -0.8}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.073, 1.021, 0.16, 0.956, 0.073, 1, 0.16, 1.087]}, {"time": 0.2333, "x": 0.956, "y": 1.087, "curve": [0.331, 0.956, 0.402, 1.073, 0.331, 1.087, 0.402, 0.932]}, {"time": 0.5, "x": 1.073, "y": 0.932, "curve": [0.598, 1.073, 0.702, 0.956, 0.598, 0.932, 0.702, 1.087]}, {"time": 0.8, "x": 0.956, "y": 1.087, "curve": [0.898, 0.956, 1.002, 1.073, 0.898, 1.087, 1.002, 0.932]}, {"time": 1.1, "x": 1.073, "y": 0.932, "curve": [1.125, 1.073, 1.143, 1.065, 1.125, 0.932, 1.143, 0.942]}, {"time": 1.1667, "x": 1.054, "y": 0.957}]}, "RU_R2": {"translate": [{"x": 1.03, "y": 0.24, "curve": [0.099, -14.87, 0.203, -30.99, 0.099, -3.53, 0.203, -7.36]}, {"time": 0.3, "x": -30.99, "y": -7.36, "curve": [0.496, -30.99, 0.671, 33.03, 0.496, -7.36, 0.671, 7.84]}, {"time": 0.8667, "x": 33.04, "y": 7.84, "curve": [0.965, 33.05, 1.07, 17.15, 0.965, 7.85, 1.07, 4.07]}, {"time": 1.1667, "x": 1.04, "y": 0.25}], "scale": [{"x": 1.073, "y": 0.932, "curve": [0.098, 1.073, 0.202, 0.956, 0.098, 0.932, 0.202, 1.087]}, {"time": 0.3, "x": 0.956, "y": 1.087, "curve": [0.398, 0.956, 0.502, 1.073, 0.398, 1.087, 0.502, 0.932]}, {"time": 0.6, "x": 1.073, "y": 0.932, "curve": [0.698, 1.073, 0.769, 0.956, 0.698, 0.932, 0.769, 1.087]}, {"time": 0.8667, "x": 0.956, "y": 1.087, "curve": [0.965, 0.956, 1.069, 1.073, 0.965, 1.087, 1.069, 0.932]}, {"time": 1.1667, "x": 1.073, "y": 0.932}]}, "RU_R3": {"translate": [{"x": 12.67, "y": 0.9, "curve": [0.123, -3.84, 0.245, -25.79, 0.123, -0.27, 0.245, -1.84]}, {"time": 0.3667, "x": -25.79, "y": -1.84, "curve": [0.562, -25.79, 0.771, 30.59, 0.562, -1.84, 0.771, 2.18]}, {"time": 0.9667, "x": 30.6, "y": 2.18, "curve": [1.04, 30.61, 1.094, 22.65, 1.04, 2.18, 1.094, 1.62]}, {"time": 1.1667, "x": 12.68, "y": 0.9}], "scale": [{"x": 1.054, "y": 0.957, "curve": [0.024, 1.065, 0.042, 1.073, 0.024, 0.942, 0.042, 0.932]}, {"time": 0.0667, "x": 1.073, "y": 0.932, "curve": [0.165, 1.073, 0.269, 0.956, 0.165, 0.932, 0.269, 1.087]}, {"time": 0.3667, "x": 0.956, "y": 1.087, "curve": [0.465, 0.956, 0.569, 1.073, 0.465, 1.087, 0.569, 0.932]}, {"time": 0.6667, "x": 1.073, "y": 0.932, "curve": [0.765, 1.073, 0.869, 0.956, 0.765, 0.932, 0.869, 1.087]}, {"time": 0.9667, "x": 0.956, "y": 1.087, "curve": [1.04, 0.956, 1.094, 1.021, 1.04, 1.087, 1.094, 1]}, {"time": 1.1667, "x": 1.054, "y": 0.957}]}, "hair_L": {"rotate": [{"value": 1.77, "curve": [0.123, -1.16, 0.245, -5.05]}, {"time": 0.3667, "value": -5.05, "curve": [0.562, -5.05, 0.771, 4.95]}, {"time": 0.9667, "value": 4.95, "curve": [1.04, 4.95, 1.094, 3.54]}, {"time": 1.1667, "value": 1.77}]}, "hair_R": {"rotate": [{"value": -1.87, "curve": [0.123, 1.06, 0.245, 4.95]}, {"time": 0.3667, "value": 4.95, "curve": [0.562, 4.95, 0.771, -5.05]}, {"time": 0.9667, "value": -5.05, "curve": [1.04, -5.06, 1.094, -3.64]}, {"time": 1.1667, "value": -1.87}]}, "hair_B": {"rotate": [{"value": -0.42, "curve": [0.099, 1.68, 0.203, 3.82]}, {"time": 0.3, "value": 3.82, "curve": [0.496, 3.82, 0.671, -4.66]}, {"time": 0.8667, "value": -4.66, "curve": [0.965, -4.67, 1.07, -2.56]}, {"time": 1.1667, "value": -0.43}]}, "tunround": {"translate": [{"x": -241.28, "curve": [0.024, -238.31, 0.059, 400.94, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 403.91, "curve": [0.544, 409.2, 0.881, -276.08, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -241.28}]}, "bodyround": {"translate": [{"x": -84.69, "y": 145.73, "curve": [0.028, -83.4, 0.067, 181.85, 0.028, 143.16, 0.067, -393.79]}, {"time": 0.2667, "x": 183.14, "y": -396.36, "curve": [0.567, 185.08, 0.871, -98.4, 0.566, -400.21, 0.881, 171.94]}, {"time": 1.1667, "x": -84.69, "y": 145.73}]}, "headround3": {"translate": [{"x": 70.32, "curve": [0.035, 71.95, 0.084, 317.32, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 318.96, "curve": [0.611, 320.78, 0.893, 57.45, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 70.32}]}, "headround": {"translate": [{"y": -136.02, "curve": [0.111, 0, 0.222, 0, 0.035, -137.22, 0.084, -317.32]}, {"time": 0.3333, "y": -318.52, "curve": [0.611, 0, 0.889, 0, 0.611, -319.86, 0.891, -126.52]}, {"time": 1.1667, "y": -136.02}]}, "leg_L1": {"rotate": [{"value": -0.11}]}, "leg_L5": {"rotate": [{"value": 0.33}]}, "leg_L6": {"translate": [{"x": 24.81, "y": -0.1, "curve": [0.024, 24.82, 0.059, -123.77, 0.024, -0.1, 0.059, -21.4]}, {"time": 0.2333, "x": -123.77, "y": -21.4, "curve": [0.544, -123.77, 0.856, 24.8, 0.544, -21.4, 0.856, -0.1]}, {"time": 1.1667, "x": 24.8, "y": -0.1}]}, "tun2": {"rotate": [{"value": -0.03}]}, "leg_R1": {"translate": [{"y": -8.75, "curve": [0.078, 0, 0.156, 0, 0.024, -8.67, 0.059, 9.44]}, {"time": 0.2333, "y": 9.53, "curve": [0.544, 0, 0.856, 0, 0.544, 9.68, 0.856, -9.83]}, {"time": 1.1667, "y": -8.75}]}, "sh_L2": {"rotate": [{"value": 0.44}]}, "sh_L3": {"rotate": [{"value": -1.09}]}, "arm_L4": {"translate": [{"x": -2.19, "y": -3.14, "curve": [0.028, -2.05, 0.067, 23.29, 0.028, -3.02, 0.067, 20.15]}, {"time": 0.2667, "x": 23.43, "y": 20.27, "curve": [0.567, 23.63, 0.867, -3.64, 0.567, 20.46, 0.867, -4.47]}, {"time": 1.1667, "x": -2.19, "y": -3.14}]}, "sh_R2": {"rotate": [{"value": -0.04}]}, "sh_R3": {"rotate": [{"value": -0.08}]}, "arm_R6": {"translate": [{"x": -3.49, "y": -2.22, "curve": [0.024, -3.23, 0.059, 52.55, 0.024, -2.13, 0.059, -0.22]}, {"time": 0.2333, "x": 52.61, "y": -0.19, "curve": [0.544, 52.72, 0.856, -3.48, 0.544, -0.16, 0.856, -2.22]}, {"time": 1.1667, "x": -3.48, "y": -2.22}]}, "eyebrow_R": {"rotate": [{"curve": [0.028, 0, 0.059, 15.67]}, {"time": 0.2333, "value": 15.67, "curve": [0.544, 15.67, 0.856, 0]}, {"time": 1.1667}], "translate": [{"curve": [0.028, 0, 0.059, -0.35, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -0.35, "curve": [0.544, -0.35, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.028, 1, 0.059, 0.922, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.922, "curve": [0.544, 0.922, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.028, 0, 0.059, 25.99]}, {"time": 0.2333, "value": 25.99, "curve": [0.544, 25.99, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.028, 0, 0.059, -16.9]}, {"time": 0.2333, "value": -16.9, "curve": [0.544, -16.9, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.028, 0, 0.059, -4.42]}, {"time": 0.2333, "value": -4.42, "curve": [0.544, -4.42, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.028, 1, 0.059, 0.922, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.922, "curve": [0.544, 0.922, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L": {"translate": [{"curve": [0.028, 0, 0.059, -2.75, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.75, "curve": [0.544, -2.75, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.94794, 0.11185, -2.94918, 0.11196, -1.62513, 0.00201, -1.62635, 0.00204, -1.62513, 0.00201, -1.62635, 0.00204, -1.17507, -0.02355, -1.17603, -0.0235, -0.79063, 0.02912, -0.79121, 0.02907, -0.4625, -0.03692, -0.46276, -0.03701, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16563, -0.01542, -0.16576, -0.01545, -1.4566, -0.04837, -1.45749, -0.04832, -2.53182, -0.05962, -2.53351, -0.05957, -3.08534, 0.02381, -3.08582, 0.02388, -4.04805, -0.0301, -4.04878, -0.03007, -3.75108, -0.05904, -3.75169, -0.05898, 0, 0, 0, 0, -1.20851, 0.03, -1.20908, 0.03006, -2.35465, 0.13273, -2.35525, 0.13268, -3.17865, 0.0373, -3.17925, 0.03729, -3.52739, 0.15546, -3.52765, 0.15541, -3.25808, -0.01802, -3.25888, -0.01786, -2.46397, -0.04008, -2.46496, -0.03967, -1.19849, -0.09176, -1.19887, -0.09153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.15772, -0.00978, -0.15807, -0.00967, -0.64016, -0.03463, -0.6408, -0.03437], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-3.1679, 0.26593, -3.16748, 0.2659, -3.10827, 0.27954, -3.10776, 0.27944, -3.07864, 0.26932, -3.07819, 0.26924, -2.95547, 0.25713, -2.95486, 0.257, -2.23693, 0.15981, -2.23677, 0.15964, -0.80158, -0.0498, -0.80154, -0.04986, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46276, -0.04845, -0.46279, -0.04847, -0.89396, -0.02004, -0.89466, -0.0201, -0.91416, -0.14252, -0.91476, -0.14241, -1.3533, -0.16294, -1.35429, -0.16281, -2.52449, -0.06009, -2.52541, -0.05993, -1.62239, 0.25257, -1.62383, 0.25274, -2.71273, 0.2254, -2.71379, 0.22556, -3.22325, 0.28605, -3.22299, 0.28601, -3.02042, 0.26188, -3.01994, 0.26178, -2.36967, 0.11911, -2.37044, 0.11925, -1.1997, 0.00391, -1.19986, 0.00386, 0, 0, 0, 0, -0.69267, 0.01619, -0.69305, 0.01624, -0.18656, 0.01293, -0.187, 0.013], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "offset": 148, "vertices": [-0.65001, 0.1308, -0.65043, 0.1309, -2.05813, -0.02108, -2.05854, -0.02094, -2.90337, -0.02019, -2.90395, -0.01995, -3.01965, 0.00318, -3.01994, 0.00336, -2.62469, 0.0446, -2.62504, 0.04477, -1.65378, 0.0668, -1.654, 0.06691, -0.59454, -0.0737, -0.59492, -0.07358, -0.19507, -0.00908, -0.1952, -0.00904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.87501, -0.12, -0.87473, -0.12004, -2.04482, 0.0413, -2.04485, 0.04136, -2.76304, 0.14882, -2.76314, 0.14879, -2.96226, 0.17031, -2.96245, 0.17038, -2.43245, -0.04718, -2.43216, -0.0472, -1.10601, 0.01897, -1.10582, 0.01894, -0.44448, -0.00308, -0.44438], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.023, 0, 0.058, 1]}, {"time": 0.2333, "offset": 64, "vertices": [-2.1012, -0.02356, -2.10059, -0.02355, -1.39038, -0.01559, -1.3894, -0.01559, -1.67493, -0.01878, -1.67432, -0.01878, -1.02356, -0.01147, -1.02368, -0.01148, 0.10901, 0.14852, 0.10742, 0.14853, 1.03796, -0.5149, 1.03564, -0.51492, -2.50586, -0.40033, -2.5061, -0.40031, -1.14465, -0.34104, -1.14502, -0.341, 0.24878, 0.39247, 0.24609, 0.39247, 1.3822, 0.68932, 1.37915, 0.68933, 0.57153, -0.06176, 0.57104, -0.06177, 1.09131, -0.05593, 1.09082, -0.05594, 1.29578, -0.05364, 1.29517, -0.05366, 1.09131, -0.05593, 1.09082, -0.05594, 0.66418, 0.03292, 0.66333, 0.03291, 1.33508, -0.24831, 1.33374, -0.24831, 2.10278, -0.43193, 2.10034, -0.43194, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.14783, -0.25041, 1.14673, -0.25041, 0.14209, 0.00159, 0.14209, 0.00159, -1.1167, -0.01252, -1.11621, -0.01252, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, -1.67493, -0.01878, -1.67432, -0.01878, 0.70117, 0.10147, 0.69971, 0.10146, 1.12134, 0.19978, 1.1189, 0.19978, 0.60754, 0.10042, 0.6062, 0.10041, 0.26245, 0.06876, 0.26172, 0.06875, 0.67322, 0.53931, 0.67065, 0.5393, 1.6272, -0.07541, 1.62524, -0.07542, 2.1051, -0.13822, 2.10303, -0.13822, 2.04443, -0.07074, 2.04199, -0.07075, 1.24634, 0.01396, 1.24561, 0.01397, 0.8385, -0.27151, 0.83691, -0.27153, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 1.67725, -0.50773, 1.67505, -0.50775, 0, 0, 0, 0, 0.65698, -0.12613, 0.65674, -0.12612, 0.65698, -0.12613, 0.65674, -0.12612, 0, 0, 0, 0, 0, 0, 0, 0, 0.66602, 0.14095, 0.66602, 0.14096, 0.66602, 0.14095, 0.66602, 0.14096], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}