// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

//using UnityEditor;
//using UnityEngine;
//namespace AmplifyShaderEditor
//{
//	//EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector)
//	// this might be a bit nonsense since I could use the GetBuiltinSkin directly but this way will bea easier  to change to some custom visuals on some near future
//	[System.Serializable]
//	public class CustomStylesContainer
//	{
//		public GUIStyle FoldoutStyle
//		{
//			get { return EditorStyles.foldout; }
//		}

//		public GUIStyle Label
//		{
//			get { return GUI.skin.label; }
//		}

//		public GUIStyle Button
//		{
//			get { return GUI.skin.button; }
//		}

//		public GUIStyle TextArea
//		{
//			get { return GUI.skin.textArea; }
//		}

//		public GUIStyle Toggle
//		{
//			get { return GUI.skin.toggle; }
//		}

//		public GUIStyle Window
//		{
//			get { return GUI.skin.window; }
//		}

//		public GUIStyle Textfield
//		{
//			get { return GUI.skin.textField; }
//		}

//		public GUIStyle Box
//		{
//			get { return GUI.skin.box; }
//		}
//	}
//}
