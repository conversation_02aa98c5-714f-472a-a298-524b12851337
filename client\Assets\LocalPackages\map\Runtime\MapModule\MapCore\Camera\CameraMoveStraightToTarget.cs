﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机移动并缩放到目标点
    public class CameraMoveStraightToTarget : CameraAction
    {
        public CameraMoveStraightToTarget(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
        }

        public void StartMoving(Vector3 targetPos, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool clampBorder)
        {
            if (MapCameraMgr.isMovingToTarget)
            {
                MapCameraMgr.EnableCameraDrag(true);
                MapCameraMgr.EnableCameraZoom(true);
            }

            if (enabled == false)
            {
                enabled = true;

                MapCameraMgr.isMovingToTarget = true;

                mStartPos = MapCameraMgr.updatedCameraPosition;
                mEndPos = targetPos;
                mZoomDuration = zoomDuration;
                mCameraReachTarget = onCameraReachTarget;

                if (NeedZoomCamera(mStartPos, mEndPos))
                {
                    mMoveEndPos = CalculateMoveEndPos(mEndPos, MapCameraMgr.MapCamera.transform.forward, mStartPos.y);
                    mZoomDuration = zoomDuration;
                }
                else
                {
                    mMoveEndPos = mEndPos;
                    mZoomDuration = 0;
                }

                mElapsedTime = 0;
                mMoveDuration = moveDuration;
                mIsZooming = false;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();

            if (mStartPos == mEndPos)
            {
                isFinished = true;
                return mEndPos;
            }

            mElapsedTime += Time.deltaTime;

            //平移相机
            float t = 1.0f;
            if (!Mathf.Approximately(mMoveDuration, 0))
            {
                t = Mathf.Clamp(mElapsedTime, 0, mMoveDuration) / mMoveDuration;
            }

            if (mIsZooming)
            {
                float x = Mathf.SmoothStep(mStartPos.x, mMoveEndPos.x, t);
                float y = Mathf.SmoothStep(mStartPos.y, mMoveEndPos.y, t);
                float z = Mathf.SmoothStep(mStartPos.z, mMoveEndPos.z, t);

                if (t >= 1.0f)
                {
                    isFinished = true;
                }

                return new Vector3(x, y, z);
            }
            else
            {
                float x = Mathf.SmoothStep(mStartPos.x, mMoveEndPos.x, t);
                float y = Mathf.SmoothStep(mStartPos.y, mMoveEndPos.y, t);
                float z = Mathf.SmoothStep(mStartPos.z, mMoveEndPos.z, t);

                if (mElapsedTime >= mMoveDuration)
                {
                    //看看是否需要zoom
                    if (NeedZoomCamera(mStartPos, mEndPos))
                    {
                        //下一帧开始缩放相机
                        mIsZooming = true;
                        mElapsedTime = 0;
                        t = 0;
                        mStartPos = mMoveEndPos;
                        mMoveEndPos = MapCameraMgr.ClampPosition(mEndPos, true, out _);
                        mMoveDuration = mZoomDuration;
                    }
                    else
                    {
                        //不用zoom
                        isFinished = true;
                    }
                }

                return new Vector3(x, y, z);
            }
        }

        bool NeedZoomCamera(Vector3 startPos, Vector3 targetPos)
        {
            if (mZoomDuration == 0)
            {
                return false;
            }

            if (Mathf.Abs(targetPos.y - startPos.y) <= MapCameraMgr.minDelta)
            {
                return false;
            }

            return true;
        }

        Vector3 CalculateMoveEndPos(Vector3 endPos, Vector3 cameraDir, float startY)
        {
            float t = (startY - endPos.y) / -cameraDir.y;
            return endPos - t * cameraDir;
        }

        public override void OnFinishImpl()
        {
            MapCameraMgr.isMovingToTarget = false;

            if (mCameraReachTarget != null)
            {
                mCameraReachTarget();
            }
        }

        //相机的起始位置
        Vector3 mStartPos;
        //相机最终的目标位置
        Vector3 mEndPos;
        //相机平移的目标位置
        Vector3 mMoveEndPos;
        float mElapsedTime;
        float mMoveDuration;
        float mZoomDuration;
        bool mIsZooming = false;
        System.Action mCameraReachTarget;
    }
}