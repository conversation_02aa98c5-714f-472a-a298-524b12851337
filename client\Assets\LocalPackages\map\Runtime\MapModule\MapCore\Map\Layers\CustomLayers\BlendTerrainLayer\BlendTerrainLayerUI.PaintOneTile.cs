﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class BlendTerrainLayerUI : UnityEditor.Editor
    {
        void SetOneTile(bool clearTile)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                if (group.selectedIndex >= 0)
                {
                    ActionSetBlendTerrainLayerOneTile action = null;
                    if (clearTile)
                    {
                        action = new ActionSetBlendTerrainLayerOneTile(mLogic.layerID, prefabManager.selectedGroupIndex, mPickedTiles[0], 0, 0, 0);
                    }
                    else
                    {
                        int idx = group.GetFixedSubgroupPrefabIndex(group.selectedIndex);
                        idx = Mathf.Max(idx, 0);
                        action = new ActionSetBlendTerrainLayerOneTile(mLogic.layerID, prefabManager.selectedGroupIndex, mPickedTiles[0], group.selectedIndex, group.groupID, idx);
                    }

                    ActionManager.instance.PushAction(action);
                }
            }
        }
    }
}


#endif