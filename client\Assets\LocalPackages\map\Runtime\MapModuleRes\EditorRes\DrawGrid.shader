Shader "SLGMaker/DrawGrid"
{
    Properties
	{
        _GridThickness ("Grid Thickness", Range(0, 1)) = 0.1
        _HorizontalCellNumber ("Horizontal Cell Number", Float) = 10.0
        _VerticalCellNumber ("Vertical Cell Number", Float) = 10.0
        _GridColour ("Grid Colour", Color) = (0, 1, 0, 1.0)
		_CornerSize ("Corner Size", Range(0, 1)) = 0
    }
     
    SubShader
	{
        Tags { "Queue" = "Transparent+999" }
     
        Pass
		{
            ZWrite Off
            Blend SrcAlpha OneMinusSrcAlpha
     
            CGPROGRAM
     
            #pragma vertex vert
            #pragma fragment frag
     
            uniform float _GridThickness;
            uniform float _HorizontalCellNumber;
            uniform float _VerticalCellNumber;
            uniform float4 _GridColour;
            uniform float _CornerSize;
     
            struct vertexInput
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
            };
 
            struct vertexOutput
			{
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            };
     
            vertexOutput vert(vertexInput input)
			{
                vertexOutput output;
                output.pos = UnityObjectToClipPos(input.vertex);
                output.uv = input.uv;
                return output;
            }

			float OutsideCorner(float2 uv)
			{
				/*
				uv.x + uv.y < _CornerSize;
				1.0 - uv.x + uv.y < _CornerSize;
				uv.x + 1.0 - uv.y < _CornerSize;
				1.0 - uv.x + 1.0 - uv.y < _CornerSize;
				*/

				return -max(max(_CornerSize - uv.x - uv.y, _CornerSize + uv.x - uv.y - 1.0),
					max(_CornerSize + uv.y - uv.x - 1.0, _CornerSize + uv.x + uv.y - 2.0));
			}

			float InSideCellLine(float2 uv)
			{
				float scaledU = uv.x * _HorizontalCellNumber;
				float fracU = abs(scaledU - round(scaledU));
				float scaledV = uv.y * _VerticalCellNumber;
				float fracV = abs(scaledV - round(scaledV));
				return max(_GridThickness - fracU, _GridThickness - fracV);
			}
 
            float4 frag(vertexOutput input) : COLOR
			{
				//clip(min(OutsideCorner(input.uv), InSideCellLine(input.uv)));
				return _GridColour;
            }
			ENDCG
		}
    }
}