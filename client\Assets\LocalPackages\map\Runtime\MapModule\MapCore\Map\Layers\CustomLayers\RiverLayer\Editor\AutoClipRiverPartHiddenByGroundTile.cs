﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public static class AutoClipRiverPartHiddenByGroundTile
    {
        enum TileType
        {
            //空地或者edge就是有海的tile
            Empty,
            Edge,
            Internal,
        }

        public static void Clip(PolygonRiverSectionData data, int offsetCount, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshIndices = null;
            meshVertices = null;
            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            if (blendTerrainLayer != null)
            {
                var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
                var exceptionList = riverLayer.groundTileTypeClipExceptions;
                if (exceptionList.Count > 0)
                {
                    //自动根据tile类型裁剪地表覆盖的海面
                    List<List<Vector3>> noneHoles = new List<List<Vector3>>();
                    List<List<Vector3>> holes = new List<List<Vector3>>();
                    List<List<Vector3>> obstacles = new List<List<Vector3>>();
                    //合并需要被裁剪的tile
                    int horizontalTileCount = blendTerrainLayer.horizontalTileCount;
                    int verticalTileCount = blendTerrainLayer.verticalTileCount;
                    TileType[] tiles = new TileType[verticalTileCount * horizontalTileCount];
                    for (int i = 0; i < verticalTileCount; ++i)
                    {
                        for (int j = 0; j < horizontalTileCount; ++j)
                        {
                            var tile = blendTerrainLayer.GetTile(j, i);
                            int idx = i * horizontalTileCount + j;
                            if (tile == null)
                            {
                                tiles[idx] = TileType.Empty;
                            }
                            else
                            {
                                if (exceptionList.Contains(tile.type))
                                {
                                    //海岸tile
                                    tiles[idx] = TileType.Edge;
                                }
                                else
                                {
                                    //内陆tile
                                    tiles[idx] = TileType.Internal;
                                }
                            }
                        }
                    }

                    int filledTileCount = 0;
                    for (int p = 0; p < offsetCount; ++p)
                    {
                        int[] tileHeightDifference = new int[tiles.Length];
                        //计算高低差数组
                        CalculateTileHeightDifference(verticalTileCount, horizontalTileCount, tiles, tileHeightDifference);
                        //将高低差为1的tile设置成Edge类型,因为edge不会裁剪海洋
                        for (int i = 0; i < verticalTileCount; ++i)
                        {
                            for (int j = 0; j < horizontalTileCount; ++j)
                            {
                                int idx = i * horizontalTileCount + j;
                                if (tileHeightDifference[idx] == 1)
                                {
                                    tiles[idx] = TileType.Edge;
                                    ++filledTileCount;
                                }
                            }
                        }
                    }

                    var connectedTiles = MergeRects.ProcessTiles(horizontalTileCount, verticalTileCount, blendTerrainLayer.tileWidth,
                        (int x, int y) =>
                        {
                            if (tiles[y * horizontalTileCount + x] != TileType.Internal)
                            {
                                return false;
                            }

                            return true;
                        },
                        (int x, int y) => { return blendTerrainLayer.FromCoordinateToWorldPosition(x, y); },
                        (int x, int y) => { return blendTerrainLayer.layerData.FromCoordinateToWorldPositionCenter(x, y); }
                        );

                    for (int i = 0; i < connectedTiles.Count; ++i)
                    {
                        var rects = connectedTiles[i].rectangles;
                        int rectCount = rects.Count;
                        for (int j = 0; j < rectCount; ++j)
                        {
                            var rect = rects[j];
                            int gMinX = rect.minX + connectedTiles[i].bounds.minX;
                            int gMinY = rect.minY + connectedTiles[i].bounds.minY;
                            int gMaxX = rect.maxX + connectedTiles[i].bounds.minX;
                            int gMaxY = rect.maxY + connectedTiles[i].bounds.minY;
                            Vector3 minPos = blendTerrainLayer.FromCoordinateToWorldPosition(gMinX, gMinY);
                            Vector3 maxPos = blendTerrainLayer.FromCoordinateToWorldPosition(gMaxX + 1, gMaxY + 1);
                            var list = new List<Vector3>()
                                        {
                                            minPos,
                                            new Vector3(minPos.x, 0, maxPos.z),
                                            maxPos,
                                            new Vector3(maxPos.x, 0, minPos.z),
                                        };
                            obstacles.Add(list);

                            //temp code
#if false
                            var obj = new GameObject();
                            var dp = obj.AddComponent<DrawBounds>();
                            dp.bounds.SetMinMax(minPos, maxPos);
#endif
                        }
                    }
                    PolygonAlgorithm.GetDifferencePolygons(data.outlineRaw, obstacles, out noneHoles, out holes, ClipperLib.PolyFillType.pftNonZero, ClipperLib.PolyFillType.pftEvenOdd);
                    Triangulator.TriangulatePolygons(noneHoles, holes, false, 0, 0, null, out meshVertices, out meshIndices);
                }
                else
                {
                    //不裁剪
                    Triangulator.TriangulatePolygon(data.outlineRaw, out meshVertices, out meshIndices);
                }
            }
        }

        static void CalculateTileHeightDifference(int verticalTileCount, int horizontalTileCount, TileType[] tiles, int[] heightDifferences)
        {
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    int idx = i * horizontalTileCount + j;
                    if (tiles[idx] != TileType.Internal)
                    {
                        int rightX = j + 1;
                        int leftX = j - 1;
                        int rightY = i;
                        int leftY = i;
                        int bottomY = i - 1;
                        int topY = i + 1;
                        //check right bottom
                        if (rightX >= 0 && rightX < horizontalTileCount)
                        {
                            int rightIdx = rightY * horizontalTileCount + rightX;
                            if (tiles[rightIdx] == TileType.Internal)
                            {
                                bool sameTileType = true;
                                if (bottomY >= 0 && bottomY < verticalTileCount)
                                {
                                    int rightBottomIdx = bottomY * horizontalTileCount + rightX;
                                    sameTileType = tiles[rightBottomIdx] != TileType.Internal;
                                }
                                if (sameTileType)
                                {
                                    heightDifferences[rightIdx] = 1;
                                }
                            }
                        }

                        //check left bottom
                        if (leftX >= 0 && leftX < horizontalTileCount)
                        {
                            int leftIdx = leftY * horizontalTileCount + leftX;
                            if (tiles[leftIdx] == TileType.Internal)
                            {
                                bool sameTileType = true;
                                if (bottomY >= 0 && bottomY < verticalTileCount)
                                {
                                    int leftBottomIdx = bottomY * horizontalTileCount + leftX;
                                    sameTileType = tiles[leftBottomIdx] != TileType.Internal;
                                }
                                if (sameTileType)
                                {
                                    heightDifferences[leftIdx] = 1;
                                }
                            }
                        }

                        //check right top
                        if (rightX >= 0 && rightX < horizontalTileCount)
                        {
                            int rightIdx = rightY * horizontalTileCount + rightX;
                            if (tiles[rightIdx] == TileType.Internal)
                            {
                                bool sameTileType = true;
                                if (topY >= 0 && topY < verticalTileCount)
                                {
                                    int rightTopIdx = topY * horizontalTileCount + rightX;
                                    sameTileType = tiles[rightTopIdx] != TileType.Internal;
                                }
                                if (sameTileType)
                                {
                                    heightDifferences[rightIdx] = 1;
                                }
                            }
                        }

                        //check left bottom
                        if (leftX >= 0 && leftX < horizontalTileCount)
                        {
                            int leftIdx = leftY * horizontalTileCount + leftX;
                            if (tiles[leftIdx] == TileType.Internal)
                            {
                                bool sameTileType = true;
                                if (topY >= 0 && topY < verticalTileCount)
                                {
                                    int leftTopIdx = topY * horizontalTileCount + leftX;
                                    sameTileType = tiles[leftTopIdx] != TileType.Internal;
                                }
                                if (sameTileType)
                                {
                                    heightDifferences[leftIdx] = 1;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


#endif