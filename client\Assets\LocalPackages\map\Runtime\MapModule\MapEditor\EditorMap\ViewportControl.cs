﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public class ViewportControl : MonoBehaviour
    {
        Vector3 mLastScale;
        bool viewportChanged = false;

        void Start()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var viewportSize = map.viewport.size;
                transform.localScale = new Vector3(viewportSize.x, 1, viewportSize.y);
                mLastScale = transform.localScale;
            }
        }

        void Update()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var pos = transform.position;
                pos.y = 0;
                transform.position = pos;

                float maxScale = 50000;
                var scale = transform.localScale;
                scale.x = Mathf.Clamp(scale.x, 1, maxScale);
                scale.z = Mathf.Clamp(scale.z, 1, maxScale);
                scale.y = 1.0f;
                transform.localScale = scale;
                var newViewportSize = new Vector2(scale.x, scale.z);
                var offset = pos - map.viewCenter;
                if (mLastScale != scale)
                {
                    viewportChanged = true;
                }
                if (offset != Vector3.zero)
                {
                    map.SetEditorLookAtPosition(pos, newViewportSize);
                    viewportChanged = false;
                }
                mLastScale = scale;
            }
        }

        public void UpdateViewport()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                if (viewportChanged)
                {
                    var scale = transform.localScale;
                    var newViewportSize = new Vector2(scale.x, scale.z);
                    map.SetEditorLookAtPosition(transform.position, newViewportSize);
                    viewportChanged = false;
                }
            }
        }

        void OnDrawGizmos()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                Gizmos.color = Color.green;
                var scale = transform.localScale;
                Gizmos.DrawWireCube(transform.position, new Vector3(scale.x, 0, scale.z));

                Gizmos.color = Color.red;
                var center = new Vector3(map.mapWidth * 0.5f, 0, map.mapHeight * 0.5f);
                Gizmos.DrawWireCube(center, new Vector3(map.mapWidth, 0, map.mapHeight));
            }
        }
    }
}

#endif