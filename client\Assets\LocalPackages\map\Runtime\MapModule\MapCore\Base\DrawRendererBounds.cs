﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    class DrawRendererBounds : MonoBehaviour
    {
        private void OnDrawGizmos()
        {
            var renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                var bounds = renderer.bounds;
                Gizmos.color = Color.blue;
                Gizmos.DrawWireCube(bounds.center, bounds.size);
            }

            if (calculateBounds)
            {
                CalculateBounds();
                Gizmos.color = Color.white;
                Gizmos.DrawWireCube(wholeBounds.center, wholeBounds.size);
            }
        }

        public void CalculateBounds()
        {
            if (calculateBounds)
            {
                wholeBounds = GameObjectBoundsCalculator.CalculateBounds(gameObject, false);
            }
        }

        Bounds wholeBounds;
        public bool calculateBounds = false;
    }
}
