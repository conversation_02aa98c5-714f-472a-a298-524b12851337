﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map {
    [Black]
    public abstract class TileLayerView : MapLayerView {
        public TileLayerView(MapLayerData layerData)
            : base(layerData, false) {

            var map = Map.currentMap;
            map.messageQueue.RegisterGameMessageHandler(GMTileActiveStateChange.staticType, this, OnTileActiveStateChange);
            map.messageQueue.RegisterGameMessageHandler(GMSetTile.staticType, this, OnSetTile);
            map.messageQueue.RegisterGameMessageHandler(GMClearTile.staticType, this, OnClearTile);
        }

        public override void OnDestroy() {
            base.OnDestroy();

            foreach (var view in mViews) {
                view.Value.OnDestroy();
            }
            mViews = null;

            var map = Map.currentMap;
            map.messageQueue.RemoveGameMessageHandler(GMTileActiveStateChange.staticType, this);
            map.messageQueue.RemoveGameMessageHandler(GMSetTile.staticType, this);
            map.messageQueue.RemoveGameMessageHandler(GMClearTile.staticType, this);
        }

        public TileView GetTileView(int x, int y) {
            TileView view = null;
            var key = GetKey(x, y);
            mViews.TryGetValue(key, out view);
            return view;
        }

        void OnTileActiveStateChange(GameMessage msg) {
            var m = msg as GMTileActiveStateChange;
            if (m.layerID == mLayerData.id) {
                int rows = mLayerData.verticalTileCount;
                int cols = mLayerData.horizontalTileCount;
                var layerData = mLayerData as TileLayerData;
                int minX = Mathf.Clamp(m.minX, 0, cols - 1);
                int minY = Mathf.Clamp(m.minY, 0, rows - 1);
                int maxX = Mathf.Clamp(m.maxX, 0, cols - 1);
                int maxY = Mathf.Clamp(m.maxY, 0, rows - 1);
                for (int i = minY; i <= maxY; ++i) {
                    for (int j = minX; j <= maxX; ++j) {
                        if (m.active) {
                            var tileData = layerData.GetTile(j, i);
                            ShowTile(j, i, tileData);
                        }
                        else {
                            HideTile(j, i);
                        }
                    }
                }
            }
        }

        void OnSetTile(GameMessage msg) {
            var m = msg as GMSetTile;
            if (m.layerID == mLayerData.id) {
                ShowTile(m.x, m.y, m.tileData);
            }
        }

        void OnClearTile(GameMessage msg) {
            var m = msg as GMClearTile;
            if (m.layerID == mLayerData.id) {
                HideTile(m.x, m.y);
            }
        }

        void ShowTile(int x, int y, TileData tileData) {
            if (tileData != null) {
                int wx = x;
                int wy = y;
                var view = GetTileView(wx, wy);
                if (view == null) {
                    view = CreateTileView(tileData);
                    StoreTileView(wx, wy, view);
                }
                view.SetActive(true);
                if (view.transform != null) {
                    view.transform.SetParent(root.transform, true);
                    ShowTileInternal(view, x, y, tileData);
                }
            }
        }

        void HideTile(int x, int y) {
            int wx = x;
            int wy = y;
            var view = GetTileView(wx, wy);
            if (view != null) {
                ReleaseTileView(wx, wy, view);
            }
        }

        void StoreTileView(int x, int y, TileView view) {
            var key = GetKey(x, y);
            mViews[key] = view;
        }

        int GetKey(int x, int y) {
            return x | (y << 16);
        }

        protected abstract TileView CreateTileView(TileData data);

        void ReleaseTileView(int x, int y, TileView view) {
            view.SetActive(false);
            if (view.gameObject != null) {
                view.transform.SetParent(null);
            }
            var key = GetKey(x, y);
            bool suc = mViews.Remove(key);
            Debug.Assert(suc);
            OnReleaseTileView(x, y, view);
        }
        protected virtual void OnReleaseTileView(int x, int y, TileView view) {
            view.OnDestroy();
        }

        protected abstract void ShowTileInternal(TileView view, int x, int y, TileData data);

        protected Dictionary<int, TileView> mViews = new Dictionary<int, TileView>();
    }
}

#endif