﻿ 



 
 

using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CustomVertexAnimator : CustomAnimatorBase
    {
        static int _AnimationParamsID = Shader.PropertyToID(MapCoreDef.BAKED_ANIMATION_PARAMETERS_PROPERTY_NAME);
        static int _BlendParamsID = Shader.PropertyToID(MapCoreDef.BAKED_ANIMATION_BLEND_PARAMETERS_PROPERTY_NAME);

        public class RendererEntry
        {
            public RendererEntry(MeshRenderer renderer, int meshIndex, int vertexCount)
            {
                this.renderer = renderer;
                this.meshIndex = meshIndex;
                this.vertexCount = vertexCount;
            }
            public MeshRenderer renderer;
            public Material[] sharedMaterials;
            public int meshIndex;
            public int vertexCount;
        }

        MaterialPropertyBlock mProps;
        List<RendererEntry> mRenderers = new List<RendererEntry>();
        List<Transform> mCPUDrivenBones = new List<Transform>();

        public CustomVertexAnimator(GameObject obj) : base(obj)
        {
        }

        public override void OnEnable()
        {
            if (mStateMachine == null)
            {
                mWrapper = mGameObject.GetComponent<AnimatorWrapper>();

                var tags = mGameObject.GetComponentsInChildren<RendererTag>(true);
                for (int i = 0; i < tags.Length; ++i)
                {
                    var meshRenderer = tags[i].GetComponent<MeshRenderer>();
                    var meshFilter = tags[i].GetComponent<MeshFilter>();
                    var renderEntry = new RendererEntry(meshRenderer, tags[i].meshIndex, meshFilter.sharedMesh.vertexCount);
                    mRenderers.Add(renderEntry);

                    var mtls = meshRenderer.sharedMaterials;
                    for (int k = 0; k < mtls.Length; ++k)
                    {
                        if (mWrapper.animationData.useAnimationBlending)
                        {
                            mtls[k].EnableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                        }
                        else
                        {
                            mtls[k].DisableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                        }

                        if (mWrapper.animationData.useSRPBatcher)
                        {
                            meshRenderer.materials[k] = mtls[k];
                        }
                    }

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        renderEntry.sharedMaterials = meshRenderer.sharedMaterials;
                    }
                }

                mProps = new MaterialPropertyBlock();
                mStateMachine = new CustomAnimationStateMachine(mWrapper.animationData.animationInfo, mWrapper.animationData.animationStateInfo, mWrapper.animationData.parameterInfo, mWrapper.animationData.useAnimationBlending/*, mWrapper.debugID*/);
#if UNITY_EDITOR
                mStateMachine.SetAnimatorName(mGameObject.name);
#endif
                //init cpu bones
                var names = mWrapper.animationData.GetCPUDrivenCustomBoneNames();
                if (names != null)
                {
                    for (int i = 0; i < names.Length; ++i)
                    {
                        var transform = mWrapper.transform.Find(names[i]);
                        Debug.Assert(transform != null, $"CPU bone {names[i]} not found!");
                        mCPUDrivenBones.Add(transform);
                    }
                }
            }
        }

        public override void OnDisable()
        {
        }

        public override void OnDestroy()
        {
            if (mWrapper.animationData.useSRPBatcher)
            {
                var tags = mGameObject.GetComponentsInChildren<RendererTag>(true);
                for (int i = 0; i < tags.Length; ++i)
                {
                    var meshRenderer = tags[i].GetComponent<MeshRenderer>();
                    var mtls = meshRenderer.sharedMaterials;
                    for (int m = 0; m < mtls.Length; ++m)
                    {
                        Utils.DestroyObject(mtls[m]);
                    }
                }
            }
        }

        public override bool Update(GlobalAnimationBlendingState state)
        {
            if (mStateMachine == null)
            {
                return false;
            }
            bool blendStateChanged = mStateMachine.Update(state);

            if (mStateMachine.enableAnimationBlendingNow)
            {
                UpdateAnimationBlending();
            }
            else
            {
                UpdateAnimation();
            }
            return blendStateChanged;
        }

        void UpdateAnimationBlending()
        {
            var curState = mStateMachine.currentState;
            var animationData = mWrapper.animationData as BakedVertexAnimationData;
            bool isVisible = false;

            var nextState = mStateMachine.nextState;
            bool isInTransition = mStateMachine.IsInTransitionDuration;
            var blendParams = mStateMachine.normalizedDurationRatio;
            float nextStateTime = isInTransition ? mStateMachine.nextStateTimeInTransitionDuration : 0;

            float currentTime = curState.currentTime;
            float curAnimLength = curState.animInfo.lengthInSeconds;

            for (int i = 0; i < mRenderers.Count; ++i)
            {
                var renderer = mRenderers[i].renderer;
                if (renderer.isVisible)
                {
                    isVisible = true;
                    int meshIndex = mRenderers[i].meshIndex;

                    int curAnimOffset = curState.animInfo.bakedVertexAnimationStartOffsetsForEachMesh[meshIndex];
                    int nextAnimOffset = isInTransition ? nextState.animInfo.bakedVertexAnimationStartOffsetsForEachMesh[meshIndex] : 0;
                    float curElapsedRatio = currentTime / curAnimLength;
                    float nextElapsedRatio = 0;
                    float nextAnimV = 0;
                    if (isInTransition)
                    {
                        nextElapsedRatio = nextStateTime / nextState.animInfo.lengthInSeconds;
                        nextAnimV = (nextAnimOffset + nextElapsedRatio * (nextState.animInfo.totalFrames - 1)) / animationData.totalFrameCount;
                    }
                    float curAnimV = (curAnimOffset + curElapsedRatio * (curState.animInfo.totalFrames - 1)) / animationData.totalFrameCount;

                    var animationParams = new Vector4(mRenderers[i].vertexCount, curAnimV, nextAnimV, 0);

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        var sharedMaterials = mRenderers[i].sharedMaterials;
                        for (int m = 0; m < sharedMaterials.Length; ++m)
                        {
                            sharedMaterials[m].SetVector(_AnimationParamsID, animationParams);
                            sharedMaterials[m].SetFloat(_BlendParamsID, blendParams);
                        }
                    }
                    else
                    {
                        renderer.GetPropertyBlock(mProps);
                        mProps.SetVector(_AnimationParamsID, animationParams);
                        mProps.SetFloat(_BlendParamsID, blendParams);
                        renderer.SetPropertyBlock(mProps);
                    }
                }
            }

            //update cpu driven bone animations
            var cpuDrivenBoneNames = animationData.GetCPUDrivenCustomBoneNames();
            if (isVisible && cpuDrivenBoneNames != null)
            {
                float curAnimFrame = currentTime / curAnimLength * (curState.animInfo.totalFrames - 1);
                int curAnimStartFrame = Mathf.FloorToInt(curAnimFrame);
                int curAnimEndFrame = Mathf.CeilToInt(curAnimFrame);
                float curRatio = curAnimFrame - curAnimStartFrame;
                var translations = animationData.cpuDrivenCustomBoneTranslations;
                var rotations = animationData.cpuDrivenCustomBoneRotations;
                var scalings = animationData.cpuDrivenCustomBoneScalings;

                int cpuDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(curState.animationIndex);
                int nBones = cpuDrivenBoneNames.Length;
                if (isInTransition)
                {
                    int nextStateCPUDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(nextState.animationIndex);
                    float nextAnimFrame = nextStateTime / nextState.animInfo.lengthInSeconds * (nextState.animInfo.totalFrames - 1);
                    int nextAnimStartFrame = Mathf.FloorToInt(nextAnimFrame);
                    int nextAnimEndFrame = Mathf.CeilToInt(nextAnimFrame);
                    float nextRatio = nextAnimFrame - nextAnimStartFrame;

                    for (int i = 0; i < nBones; ++i)
                    {
                        var boneTransform = mCPUDrivenBones[i].transform;

                        int curStartOffset = cpuDrivenAnimOffset + curAnimStartFrame * nBones + i;
                        int curEndOffset = cpuDrivenAnimOffset + curAnimEndFrame * nBones + i;
                        int nextStartOffset = nextStateCPUDrivenAnimOffset + nextAnimStartFrame * nBones + i;
                        int nextEndOffset = nextStateCPUDrivenAnimOffset + nextAnimEndFrame * nBones + i;

                        var curTranslation = Vector3.Lerp(translations[curStartOffset], translations[curEndOffset], curRatio);
                        var curRotation = Quaternion.Slerp(rotations[curStartOffset], rotations[curEndOffset], curRatio);
                        var nextTranslation = Vector3.Lerp(translations[nextStartOffset], translations[nextEndOffset], nextRatio);
                        var nextRotation = Quaternion.Slerp(rotations[nextStartOffset], rotations[nextEndOffset], nextRatio);
                        boneTransform.localPosition = Vector3.Lerp(curTranslation, nextTranslation, blendParams);
                        boneTransform.localRotation = Quaternion.Slerp(curRotation, nextRotation, blendParams);

                        if (scalings.Length > 0)
                        {
                            Vector3 curScale = Vector3.Lerp(scalings[curStartOffset], scalings[curEndOffset], curRatio);
                            Vector3 nextScale = Vector3.Lerp(scalings[nextStartOffset], scalings[nextEndOffset], nextRatio);
                            boneTransform.localScale = Vector3.Lerp(curScale, nextScale, blendParams);
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < nBones; ++i)
                    {
                        int startOffset = cpuDrivenAnimOffset + curAnimStartFrame * nBones + i;
                        int endOffset = cpuDrivenAnimOffset + curAnimEndFrame * nBones + i;
                        var boneTransform = mCPUDrivenBones[i].transform;
                        boneTransform.localPosition = Vector3.Lerp(translations[startOffset], translations[endOffset], curRatio);
                        boneTransform.localRotation = Quaternion.Slerp(rotations[startOffset], rotations[endOffset], curRatio);
                        if (scalings.Length > 0)
                        {
                            boneTransform.localScale = Vector3.Lerp(scalings[startOffset], scalings[endOffset], curRatio);
                        }
                    }
                }
            }
        }

        void UpdateAnimation()
        {
            var curState = mStateMachine.currentState;
            var animationData = mWrapper.animationData as BakedVertexAnimationData;
            bool isVisible = false;
            float currentTime = curState.currentTime;
            float length = curState.animInfo.lengthInSeconds;
            for (int i = 0; i < mRenderers.Count; ++i)
            {
                var renderer = mRenderers[i].renderer;
                if (renderer.isVisible)
                {
                    int meshIndex = mRenderers[i].meshIndex;
                    isVisible = true;

                    //Debug.LogError($"state: {curState.animInfo.clipName}, currentTime: {currentTime}, length: {length}");
                    int offset = curState.animInfo.bakedVertexAnimationStartOffsetsForEachMesh[meshIndex];
                    float elapsedRatio = currentTime / length;
                    //totalFrames-1是因为shader中uv坐标采样像素中心点,详情参考knowledge database中的记录
                    float v = (offset + elapsedRatio * (curState.animInfo.totalFrames - 1)) / animationData.totalFrameCount;
                    var animationParams = new Vector4(mRenderers[i].vertexCount, v, 0, 0);

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        var mtls = mRenderers[i].sharedMaterials;
                        for (int m = 0; m < mtls.Length; ++m)
                        {
                            mtls[m].SetVector(_AnimationParamsID, animationParams);
                        }
                    }
                    else
                    {
                        renderer.GetPropertyBlock(mProps);
                        mProps.SetVector(_AnimationParamsID, animationParams);
                        renderer.SetPropertyBlock(mProps);
                    }
                }
            }

            //update cpu driven bone animations
            var cpuDrivenBoneNames = animationData.GetCPUDrivenCustomBoneNames();
            if (isVisible && cpuDrivenBoneNames != null)
            {
                int cpuDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(curState.animationIndex);
                int nBones = cpuDrivenBoneNames.Length;

                var rotations = animationData.cpuDrivenCustomBoneRotations;
                var scalings = animationData.cpuDrivenCustomBoneScalings;
                var translations = animationData.cpuDrivenCustomBoneTranslations;
                float v = currentTime / length * (curState.animInfo.totalFrames - 1);
                int start = Mathf.FloorToInt(v);
                int end = Mathf.CeilToInt(v);
                float ratio = v - start;
                for (int i = 0; i < nBones; ++i)
                {
                    int startOffset = cpuDrivenAnimOffset + start * nBones + i;
                    int endOffset = cpuDrivenAnimOffset + end * nBones + i;
                    var boneTransform = mCPUDrivenBones[i].transform;
                    boneTransform.localPosition = Vector3.Lerp(translations[startOffset], translations[endOffset], ratio);
                    boneTransform.localRotation = Quaternion.Slerp(rotations[startOffset], rotations[endOffset], ratio);
                    if (scalings.Length > 0)
                    {
                        boneTransform.localScale = Vector3.Lerp(scalings[startOffset], scalings[endOffset], ratio);
                    }
                }
            }
        }
    }
}
