﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //检测tile object是否可见
    public class FrameActionUpdateTileObject2 : FrameAction
    {
        public static FrameActionUpdateTileObject2 Require(TileGridObjectLayerData2 layerData, int tileObjectID)
        {
            var act = mPool.Require();
            act.Init(layerData, tileObjectID);
            return act;
        }

        void Init(TileGridObjectLayerData2 layerData, int tileObjectID)
        {
            InitAction();
            mLayerData = layerData;
            mTileObjectID = tileObjectID;
            mKey = MakeActionKey(tileObjectID, FrameActionType.UpdateTileObject2);
        }

        protected override void DoImpl()
        {
            var tileObject = mLayerData.GetTileObject(mTileObjectID);
            if (tileObject == null || (MapCoreDef.IsRemovableObject(tileObject.objectType) && mLayerData.IsObjectRemoved(tileObject.viewID)))
            {
                return;
            }
            
            bool overlap = tileObject.worldBounds.Overlaps(mLayerData.map.viewport);
            bool isActive = tileObject.IsObjActive();
            if (overlap != isActive)
            {
                //隐藏视野外的物体,显示视野内的物体
                mLayerData.SetObjectActiveOnly(tileObject, overlap, tileObject.lod);
            }
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return MakeKeyHelper(id, type);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObject2;
        public override string debugInfo => "";
        public override string name => "Update Tile Object 2";

        TileGridObjectLayerData2 mLayerData;
        int mTileObjectID;
        long mKey;

        static ObjectPool<FrameActionUpdateTileObject2> mPool = new ObjectPool<FrameActionUpdateTileObject2>(1000, () => new FrameActionUpdateTileObject2());
    }
}
