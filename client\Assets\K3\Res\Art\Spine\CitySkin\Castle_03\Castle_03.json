{"skeleton": {"hash": "AB53+Ty+ivE", "spine": "4.2.33", "x": -423.86, "y": -246.34, "width": 846, "height": 932.66, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "tree_Fb", "parent": "root", "x": 51.34, "y": -175.35}, {"name": "tree_Fb2", "parent": "tree_Fb", "length": 30.37, "rotation": 87.83, "y": 4.32}, {"name": "tree_Fb3", "parent": "tree_Fb2", "length": 32.36, "rotation": 2, "x": 30.37}, {"name": "tree_Fb4", "parent": "tree_Fb3", "length": 30.65, "rotation": 2.14, "x": 32.36}, {"name": "tree_Fa", "parent": "root", "x": -57.44, "y": -173.71}, {"name": "tree_Fa2", "parent": "tree_Fa", "length": 29.96, "rotation": 92.53, "y": 5.46}, {"name": "tree_Fa3", "parent": "tree_Fa2", "length": 27.63, "rotation": -3.9, "x": 29.96}, {"name": "tree_Fa4", "parent": "tree_Fa3", "length": 32.09, "rotation": 0.49, "x": 27.63}, {"name": "tree_Fc", "parent": "root", "x": -30.28, "y": -118.97}, {"name": "tree_Fc2", "parent": "tree_Fc", "length": 30.93, "rotation": 90.92, "y": 4.63}, {"name": "tree_Fc3", "parent": "tree_Fc2", "length": 25.49, "rotation": 1.31, "x": 30.93}, {"name": "tree_Fc4", "parent": "tree_Fc3", "length": 29.28, "rotation": 3.93, "x": 25.49}, {"name": "tree_Ra", "parent": "root", "x": 238.9, "y": -80}, {"name": "tree_Ra2", "parent": "tree_Ra", "length": 32.99, "rotation": 85.98, "x": -0.17, "y": 4.13}, {"name": "tree_Ra3", "parent": "tree_Ra2", "length": 31.43, "rotation": 2.82, "x": 32.99}, {"name": "tree_Ra4", "parent": "tree_Ra3", "length": 31.09, "rotation": 0.6, "x": 31.43}, {"name": "tree_Rb", "parent": "root", "x": 200.78, "y": -39.99}, {"name": "tree_Rb2", "parent": "tree_Rb", "length": 34.59, "rotation": 87.81, "x": 0.83, "y": 6.95}, {"name": "tree_Rb3", "parent": "tree_Rb2", "length": 45.74, "rotation": -1.54, "x": 34.59}, {"name": "tree_Rb4", "parent": "tree_Rb3", "length": 47.58, "rotation": -7.7, "x": 45.74}, {"name": "tree_Rc", "parent": "root", "x": 369.3, "y": -12.58}, {"name": "tree_Rc2", "parent": "tree_Rc", "length": 35.8, "rotation": 86.29, "x": 0.99, "y": 4.96}, {"name": "tree_Rc3", "parent": "tree_Rc2", "length": 49.73, "rotation": -2.4, "x": 35.8}, {"name": "tree_Rc4", "parent": "tree_Rc3", "length": 51.95, "rotation": -6.95, "x": 49.73}, {"name": "tree_Rd", "parent": "root", "x": 310.7, "y": 12.97}, {"name": "tree_Rd2", "parent": "tree_Rd", "length": 44.29, "rotation": 83.35, "x": 0.33, "y": 5.46}, {"name": "tree_Rd3", "parent": "tree_Rd2", "length": 39.86, "rotation": -3.87, "x": 44.29}, {"name": "tree_Rd4", "parent": "tree_Rd3", "length": 48.74, "rotation": 6.43, "x": 39.86}, {"name": "tree_Re", "parent": "root", "x": 236.41, "y": 63.65}, {"name": "tree_Re2", "parent": "tree_Re", "length": 51.12, "rotation": 83.31, "x": 0.17, "y": 6.78}, {"name": "tree_Re3", "parent": "tree_Re2", "length": 57.1, "rotation": -2.31, "x": 51.12}, {"name": "tree_Re4", "parent": "tree_Re3", "length": 58.32, "rotation": -1.29, "x": 57.1}, {"name": "tree_Re5", "parent": "tree_Re4", "length": 34.1, "rotation": 4.16, "x": 58.32}, {"name": "tree_Rf", "parent": "root", "x": 156.21, "y": 103.79}, {"name": "tree_Rf2", "parent": "tree_Rf", "length": 53.01, "rotation": 83.26, "x": 0.22, "y": 8.22}, {"name": "tree_Rf3", "parent": "tree_Rf2", "length": 56.4, "rotation": -0.28, "x": 53.01}, {"name": "tree_Rf4", "parent": "tree_Rf3", "length": 51.69, "rotation": 2.58, "x": 56.4}, {"name": "tree_La", "parent": "root", "x": -202.32, "y": -120.96}, {"name": "tree_La2", "parent": "tree_La", "length": 44.16, "rotation": 91.47, "x": -0.32, "y": 5.52}, {"name": "tree_La3", "parent": "tree_La2", "length": 35.07, "rotation": -0.15, "x": 44.16}, {"name": "tree_La4", "parent": "tree_La3", "length": 37.91, "rotation": 2.6, "x": 35.07}, {"name": "tree_Lb", "parent": "root", "x": -294.27, "y": -15.82}, {"name": "tree_Lb2", "parent": "tree_Lb", "length": 50.49, "rotation": 94.79, "x": -0.81, "y": 6.01}, {"name": "tree_Lb3", "parent": "tree_Lb2", "length": 48.11, "rotation": 0.82, "x": 50.49}, {"name": "tree_Lb4", "parent": "tree_Lb3", "length": 45.08, "rotation": 2.46, "x": 48.11}, {"name": "tree_Lc", "parent": "root", "x": -379.52, "y": 32.17}, {"name": "tree_Lc2", "parent": "tree_Lc", "length": 37.71, "rotation": 95.23, "y": 6.87}, {"name": "tree_Lc3", "parent": "tree_Lc2", "length": 36.94, "rotation": -0.65, "x": 37.71}, {"name": "tree_Lc4", "parent": "tree_Lc3", "length": 42.26, "rotation": 2.1, "x": 36.94}, {"name": "tree_Ld", "parent": "root", "x": -273.77, "y": 97.8}, {"name": "tree_Ld2", "parent": "tree_Ld", "length": 52.04, "rotation": 90.81, "y": 7.36}, {"name": "tree_Ld3", "parent": "tree_Ld2", "length": 49.96, "rotation": 3.42, "x": 52.04}, {"name": "tree_Ld4", "parent": "tree_Ld3", "length": 53.53, "rotation": -2.65, "x": 49.96}, {"name": "light", "parent": "root", "rotation": 23.22, "x": 53.72, "y": 193.7}, {"name": "light2", "parent": "light", "scaleX": 0.7681, "scaleY": 0.7681}, {"name": "light3", "parent": "root", "x": -59.63, "y": 644.78, "scaleX": 0.3782, "scaleY": 0.3782}, {"name": "light4", "parent": "light3", "scaleX": 0.7681, "scaleY": 0.7681}, {"name": "light5", "parent": "light3", "rotation": 90, "scaleX": 0.7681, "scaleY": 0.7681}], "slots": [{"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "tree_Rf", "bone": "tree_Rf", "attachment": "tree_Rf"}, {"name": "tree_Re", "bone": "tree_Re", "attachment": "tree_Re"}, {"name": "tree_Ld", "bone": "tree_Ld", "attachment": "tree_Ld"}, {"name": "Castle2", "bone": "root", "attachment": "Castle"}, {"name": "tree_Rd", "bone": "tree_Rd", "attachment": "tree_Rd"}, {"name": "tree_Rc", "bone": "tree_Rc", "attachment": "tree_Rc"}, {"name": "tree_Rb", "bone": "tree_Rb", "attachment": "tree_Rb"}, {"name": "tree_Ra", "bone": "tree_Ra", "attachment": "tree_Ra"}, {"name": "tree_Lc", "bone": "tree_Lc", "attachment": "tree_Lc"}, {"name": "tree_Lb", "bone": "tree_Lb", "attachment": "tree_Lb"}, {"name": "tree_La", "bone": "tree_La", "attachment": "tree_La"}, {"name": "tree_Fc", "bone": "tree_Fc", "attachment": "tree_Fc"}, {"name": "tree_Fb", "bone": "tree_Fb", "attachment": "tree_Fb"}, {"name": "tree_Fa", "bone": "tree_Fa", "attachment": "tree_Fa"}, {"name": "light", "bone": "light2", "color": "ffffff95", "attachment": "light", "blend": "additive"}, {"name": "light2", "bone": "light4", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light3", "bone": "light5", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "LT1", "bone": "root", "color": "ffffff00", "attachment": "LT1", "blend": "additive"}, {"name": "LT2", "bone": "root", "color": "ffffff00", "attachment": "LT2", "blend": "additive"}, {"name": "LT3", "bone": "root", "color": "ffffff00", "attachment": "LT3", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"Castle": {"Castle": {"type": "mesh", "uvs": [0.43904, 0.00014, 0.44776, 0.14424, 0.50122, 0.09178, 0.52824, 0.10093, 0.5273, 0.14928, 0.54664, 0.17624, 0.63488, 0.13015, 0.66534, 0.22344, 0.64433, 0.38137, 0.58891, 0.38081, 0.74028, 0.54191, 0.73814, 0.58559, 1, 0.73553, 1, 0.74816, 0.48448, 0.99999, 0, 0.70554, 0, 0.69894, 0.21798, 0.59409, 0.21568, 0.54183, 0.34264, 0.27205, 0.36249, 0.26538, 0.40445, 0.26256, 0.43273, 0, 0.22658, 0.63088, 0.23652, 0.64861, 0.23665, 0.66047, 0.2239, 0.66694, 0.21191, 0.7096, 0.73408, 0.60008, 0.72866, 0.61294, 0.7231, 0.71198], "triangles": [4, 2, 3, 1, 2, 4, 5, 6, 7, 1, 21, 22, 1, 22, 0, 9, 5, 7, 8, 9, 7, 9, 10, 29, 24, 23, 17, 10, 11, 29, 11, 28, 29, 17, 18, 24, 5, 21, 1, 5, 1, 4, 18, 9, 24, 21, 5, 9, 9, 25, 24, 23, 16, 17, 26, 23, 24, 26, 24, 25, 26, 16, 23, 27, 15, 16, 26, 27, 16, 29, 25, 9, 19, 20, 21, 21, 9, 19, 18, 19, 9, 29, 30, 25, 27, 26, 25, 30, 27, 25, 28, 11, 12, 29, 28, 12, 30, 29, 12, 30, 12, 13, 14, 27, 30, 14, 30, 13, 15, 27, 14], "vertices": [-55.5, 646.53, -48.18, 517.85, -3.33, 564.69, 19.34, 556.53, 18.55, 513.34, 34.77, 489.28, 108.81, 530.43, 134.36, 447.12, 116.73, 306.09, 70.24, 306.59, 197.24, 162.73, 195.44, 123.73, 415.14, -10.17, 415.14, -21.45, -17.38, -246.34, -423.86, 16.61, -423.86, 22.5, -240.98, 116.14, -242.9, 162.8, -136.39, 403.72, -119.73, 409.67, -84.53, 412.19, -60.79, 646.65, -233.76, 83.28, -225.41, 67.44, -225.31, 56.85, -236.01, 51.07, -246.07, 12.98, 192.04, 110.78, 187.49, 99.3, 182.82, 10.85], "hull": 23, "edges": [0, 44, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 28, 30, 0, 2, 2, 4, 6, 8, 4, 6, 38, 40, 40, 42, 34, 46, 46, 48, 48, 50, 50, 52, 52, 54, 22, 56, 56, 58, 58, 60, 60, 54], "width": 839, "height": 893}}, "Castle2": {"Castle": {"type": "mesh", "uvs": [0.43904, 0.00014, 0.44776, 0.14424, 0.50122, 0.09178, 0.52824, 0.10093, 0.5273, 0.14928, 0.54664, 0.17624, 0.63488, 0.13015, 0.66534, 0.22344, 0.64433, 0.38137, 0.58891, 0.38081, 0.74028, 0.54191, 0.73814, 0.58559, 0.73408, 0.60008, 0.72866, 0.61294, 0.7231, 0.71198, 0.21191, 0.7096, 0.2239, 0.66694, 0.23665, 0.66047, 0.23652, 0.64861, 0.22658, 0.63088, 0.21798, 0.59409, 0.21568, 0.54183, 0.34264, 0.27205, 0.36249, 0.26538, 0.40445, 0.26256, 0.43273, 0], "triangles": [4, 2, 3, 1, 2, 4, 5, 6, 7, 1, 24, 25, 1, 25, 0, 9, 5, 7, 8, 9, 7, 9, 10, 13, 18, 19, 20, 10, 11, 13, 11, 12, 13, 20, 21, 18, 5, 24, 1, 5, 1, 4, 21, 9, 18, 24, 5, 9, 9, 17, 18, 13, 17, 9, 22, 23, 24, 24, 9, 22, 21, 22, 9, 13, 14, 17, 15, 16, 17, 14, 15, 17], "vertices": [-55.5, 646.53, -48.18, 517.85, -3.33, 564.69, 19.34, 556.53, 18.55, 513.34, 34.77, 489.28, 108.81, 530.43, 134.36, 447.12, 116.73, 306.09, 70.24, 306.59, 197.24, 162.73, 195.44, 123.73, 192.04, 110.78, 187.49, 99.3, 182.82, 10.85, -246.07, 12.98, -236.01, 51.07, -225.31, 56.85, -225.41, 67.44, -233.76, 83.28, -240.98, 116.14, -242.9, 162.8, -136.39, 403.72, -119.73, 409.67, -84.53, 412.19, -60.79, 646.65], "hull": 26, "edges": [0, 50, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 40, 42, 42, 44, 48, 50, 0, 2, 2, 4, 6, 8, 4, 6, 44, 46, 46, 48, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 22, 24, 24, 26, 26, 28, 28, 30], "width": 839, "height": 893}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [143, -7.99, -143, -7.99, -143, 8.01, 143, 8.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 16}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [143, -7.99, -143, -7.99, -143, 8.01, 143, 8.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 16}}, "light3": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [143, -7.99, -143, -7.99, -143, 8.01, 143, 8.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 16}}, "LT1": {"LT1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-46.69, 513.66, -58.69, 513.66, -58.69, 572.66, -46.69, 572.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 59}}, "LT2": {"LT2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-48.65, 526.47, -60.65, 526.47, -60.65, 622.47, -48.65, 622.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 96}}, "LT3": {"LT3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-52.75, 578.9, -61.75, 578.9, -61.75, 646.9, -52.75, 646.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 68}}, "tree_Fa": {"tree_Fa": {"type": "mesh", "uvs": [0.4304, 0.00187, 0.57635, 0.00988, 0.64909, 0.10962, 0.70591, 0.2091, 0.85035, 0.33683, 0.98624, 0.39907, 0.98689, 0.51535, 0.8376, 0.67572, 0.66256, 0.82547, 0.51536, 0.81234, 0.55582, 0.90239, 0.55571, 0.98054, 0.45785, 0.99754, 0.45793, 0.92458, 0.458, 0.85161, 0.29633, 0.8518, 0.08845, 0.7628, 0.01271, 0.6402, 0.01444, 0.51527, 0.06523, 0.39517, 0.11603, 0.27508, 0.30564, 0.10621, 0.44952, 0.70566, 0.40515, 0.55806, 0.45635, 0.40235, 0.48592, 0.25718, 0.7089, 0.524], "triangles": [0, 2, 21, 2, 0, 1, 25, 2, 3, 25, 21, 2, 20, 21, 25, 24, 20, 25, 3, 24, 25, 19, 20, 24, 26, 4, 5, 26, 5, 6, 3, 4, 24, 26, 24, 4, 23, 19, 24, 23, 24, 26, 18, 19, 23, 17, 18, 23, 7, 26, 6, 22, 23, 26, 16, 17, 23, 22, 26, 7, 22, 16, 23, 8, 9, 22, 7, 8, 22, 14, 22, 9, 15, 16, 22, 14, 15, 22, 14, 9, 10, 13, 14, 10, 11, 13, 10, 12, 13, 11], "vertices": [1, 8, 34.09, 5.3, 1, 1, 8, 33.45, -5.22, 1, 2, 7, 51.17, -10.42, 0.00189, 8, 23.45, -10.62, 0.99811, 3, 6, 70.09, -17.52, 0.00033, 7, 41.22, -14.75, 0.11845, 8, 13.47, -14.86, 0.88122, 3, 6, 56.74, -27.34, 0.0481, 7, 28.57, -25.45, 0.64461, 8, 0.73, -25.46, 0.3073, 3, 6, 50.03, -36.84, 0.11011, 7, 22.52, -35.39, 0.7545, 8, -5.4, -35.34, 0.13538, 3, 6, 38.29, -36.36, 0.19096, 7, 10.78, -35.71, 0.73981, 8, -17.14, -35.57, 0.06923, 3, 6, 22.58, -24.91, 0.585, 7, -5.67, -25.36, 0.41204, 8, -33.51, -25.07, 0.00296, 2, 6, 8.03, -11.65, 0.98566, 7, -21.09, -13.12, 0.01434, 1, 6, 9.82, -1.12, 1, 2, 5, 3.6, 6.23, 0.07292, 6, 0.61, -3.63, 0.92708, 2, 5, 3.59, -1.67, 0.98818, 6, -7.28, -3.28, 0.01182, 1, 5, -3.45, -3.38, 1, 2, 5, -3.45, 3.98, 0.3174, 6, -1.32, 3.51, 0.6826, 1, 6, 6.04, 3.18, 1, 2, 6, 6.54, 14.81, 0.97722, 7, -24.38, 13.18, 0.02278, 3, 6, 16.18, 29.36, 0.7836, 7, -15.75, 28.36, 0.21593, 8, -43.13, 28.72, 0.00047, 3, 6, 28.79, 34.26, 0.55762, 7, -3.5, 34.11, 0.42317, 8, -30.84, 34.37, 0.01922, 3, 6, 41.39, 33.58, 0.31663, 7, 9.12, 34.28, 0.59177, 8, -18.22, 34.44, 0.0916, 3, 6, 53.35, 29.39, 0.11268, 7, 21.33, 30.92, 0.59589, 8, -6.03, 30.97, 0.29143, 3, 6, 65.3, 25.2, 0.01859, 7, 33.54, 27.55, 0.36199, 8, 6.15, 27.5, 0.61942, 2, 7, 50.92, 14.31, 0.00706, 8, 23.42, 14.11, 0.99294, 2, 6, 20.8, 3.14, 0.9977, 7, -9.36, 2.51, 0.0023, 3, 6, 35.83, 5.67, 0.14428, 7, 5.47, 6.06, 0.85402, 8, -22.1, 6.24, 0.0017, 3, 6, 51.38, 1.29, 0.0001, 7, 21.28, 2.75, 0.98763, 8, -6.32, 2.8, 0.01227, 1, 8, 8.37, 0.9, 1, 3, 6, 38.3, -16.33, 0.19948, 7, 9.43, -15.72, 0.77279, 8, -18.33, -15.57, 0.02773], "hull": 22, "edges": [0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 2, 4, 8, 10, 4, 6, 6, 8, 42, 0, 24, 26, 26, 28, 36, 38, 38, 40], "width": 72, "height": 101}}, "tree_Fb": {"tree_Fb": {"type": "mesh", "uvs": [0.42374, 1e-05, 0.55842, 0.03349, 0.72764, 0.08135, 0.8574, 0.19317, 0.98715, 0.30499, 0.98693, 0.42845, 0.84924, 0.46128, 0.81201, 0.52869, 0.8954, 0.71874, 0.70048, 0.79259, 0.53615, 0.81757, 0.52726, 0.8968, 0.52717, 0.99047, 0.44547, 0.99305, 0.44574, 0.8975, 0.4459, 0.83901, 0.27367, 0.75729, 0.11255, 0.69187, 0.01155, 0.5694, 0.0219, 0.39534, 0.03226, 0.22128, 0.15087, 0.08488, 0.26538, 0.07899, 0.5744, 0.67375, 0.44649, 0.53403, 0.46408, 0.3853, 0.34737, 0.23657, 0.49766, 0.09347, 0.37615, 0.68051, 0.65593, 0.26699], "triangles": [27, 0, 1, 22, 0, 27, 26, 22, 27, 2, 27, 1, 29, 2, 3, 29, 27, 2, 26, 27, 29, 25, 26, 29, 21, 22, 26, 4, 29, 3, 20, 21, 26, 19, 20, 26, 4, 6, 29, 5, 6, 4, 25, 29, 6, 7, 25, 6, 25, 19, 26, 24, 25, 7, 23, 24, 7, 25, 24, 19, 28, 24, 23, 24, 18, 19, 24, 17, 18, 28, 17, 24, 23, 7, 8, 16, 17, 28, 9, 23, 8, 10, 28, 23, 10, 23, 9, 15, 16, 28, 10, 15, 28, 11, 15, 10, 14, 15, 11, 12, 14, 11, 13, 14, 12], "vertices": [1, 4, 35.16, 4.88, 1, 1, 4, 31.31, -4.96, 1, 2, 3, 58.85, -16.32, 0.03234, 4, 25.85, -17.3, 0.96766, 2, 3, 47.13, -25.96, 0.26322, 4, 13.79, -26.5, 0.73678, 2, 3, 35.42, -35.6, 0.53766, 4, 1.72, -35.69, 0.46234, 3, 2, 54.05, -34.81, 9e-05, 3, 22.46, -35.62, 0.65711, 4, -11.23, -35.22, 0.3428, 3, 2, 50.22, -24.76, 0.01946, 3, 18.98, -25.44, 0.76999, 4, -14.32, -24.92, 0.21055, 3, 2, 43.05, -22.28, 0.13141, 3, 11.89, -22.71, 0.81304, 4, -21.3, -21.92, 0.05555, 2, 2, 23.34, -29.2, 0.60536, 3, -8.04, -28.94, 0.39464, 2, 2, 15.04, -15.08, 0.85881, 3, -15.84, -14.54, 0.14119, 2, 2, 11.96, -3.03, 0.99874, 3, -18.5, -2.38, 0.00126, 1, 2, 3.62, -2.69, 1, 1, 1, 2.82, -2, 1, 2, 1, -3.23, -2.27, 0.99952, 2, -6.71, 2.98, 0.00048, 1, 2, 3.32, 3.34, 1, 2, 2, 9.46, 3.56, 0.99822, 3, -20.77, 4.29, 0.00178, 2, 2, 17.55, 16.62, 0.72054, 3, -12.23, 17.06, 0.27946, 3, 2, 23.96, 28.8, 0.36089, 3, -5.39, 29, 0.63067, 4, -36.64, 30.39, 0.00843, 3, 2, 36.53, 36.75, 0.1516, 3, 7.44, 36.51, 0.78289, 4, -23.53, 37.42, 0.06551, 3, 2, 54.82, 36.68, 0.02225, 3, 25.72, 35.8, 0.66403, 4, -5.3, 36.03, 0.31372, 3, 2, 73.11, 36.61, 0, 3, 44, 35.09, 0.30928, 4, 12.94, 34.63, 0.69072, 2, 3, 58.35, 26.35, 0.10549, 4, 26.95, 25.36, 0.89451, 2, 3, 58.99, 17.88, 0.04699, 4, 27.28, 16.87, 0.95301, 2, 2, 27.16, -5.28, 0.76374, 3, -3.39, -5.17, 0.23626, 3, 2, 41.46, 4.73, 0.00292, 3, 11.25, 4.34, 0.99652, 4, -20.93, 5.13, 0.00056, 2, 3, 26.87, 3.08, 0.97872, 4, -5.37, 3.29, 0.02128, 2, 3, 42.46, 11.77, 0.13128, 4, 10.54, 11.38, 0.86872, 1, 4, 25.17, -0.25, 1, 2, 2, 25.89, 9.35, 0.68909, 3, -4.14, 9.5, 0.31091, 2, 3, 39.34, -11.08, 0.21748, 4, 6.56, -11.33, 0.78252], "hull": 23, "edges": [0, 44, 8, 10, 10, 12, 12, 14, 14, 16, 24, 26, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 36, 38, 38, 40, 4, 6, 6, 8, 16, 18, 18, 20, 26, 28, 28, 30, 20, 22, 22, 24, 2, 4, 0, 2], "width": 74, "height": 105}}, "tree_Fc": {"tree_Fc": {"type": "mesh", "uvs": [0.82208, 0.10693, 0.86172, 0.1353, 0.86176, 0.25983, 0.93483, 0.31422, 0.96951, 0.42038, 0.99398, 0.60159, 0.90492, 0.82577, 0.68653, 0.81781, 0.56042, 0.82605, 0.5542, 0.90311, 0.55463, 0.98979, 0.46928, 0.99011, 0.48158, 0.88366, 0.49185, 0.79471, 0.35242, 0.8196, 0.15166, 0.76052, 0.12022, 0.63128, 0.01502, 0.43462, 0.01518, 0.34626, 0.10936, 0.14304, 0.34074, 0.03025, 0.465, 0.00673, 0.535, 0.66814, 0.51728, 0.52776, 0.48632, 0.36049, 0.44631, 0.19141], "triangles": [25, 20, 21, 25, 21, 0, 19, 20, 25, 25, 2, 24, 0, 2, 25, 1, 2, 0, 25, 18, 19, 24, 4, 23, 3, 24, 2, 4, 24, 3, 23, 4, 5, 18, 25, 24, 23, 17, 24, 24, 17, 18, 23, 16, 17, 22, 23, 5, 16, 23, 22, 15, 16, 22, 22, 14, 15, 7, 22, 5, 8, 13, 22, 13, 14, 22, 6, 7, 5, 7, 8, 22, 12, 13, 8, 9, 12, 8, 11, 12, 9, 11, 9, 10], "vertices": [2, 11, 45.08, -22.12, 0.08153, 12, 18.03, -23.42, 0.91847, 2, 11, 42.29, -24.59, 0.10826, 12, 15.08, -25.69, 0.89174, 3, 10, 61.94, -23.43, 0.00721, 11, 30.47, -24.14, 0.41233, 12, 3.32, -24.42, 0.58046, 3, 10, 56.7, -28.1, 0.03277, 11, 25.12, -28.68, 0.62085, 12, -2.33, -28.59, 0.34638, 3, 10, 46.58, -30.19, 0.12344, 11, 14.96, -30.54, 0.7309, 12, -12.6, -29.75, 0.14566, 3, 10, 29.34, -31.5, 0.4775, 11, -2.31, -31.46, 0.51383, 12, -29.89, -29.48, 0.00867, 2, 10, 8.14, -25.37, 0.86152, 11, -23.36, -24.85, 0.13848, 2, 10, 9.12, -11.19, 0.9566, 11, -22.06, -10.69, 0.0434, 2, 10, 8.47, -2.98, 0.99872, 11, -22.52, -2.47, 0.00128, 2, 9, 2.44, 5.83, 0.00257, 10, 1.16, -2.46, 0.99743, 1, 9, 2.47, -2.41, 1, 1, 9, -3.08, -2.44, 1, 1, 10, 3.08, 2.23, 1, 1, 10, 11.52, 1.42, 1, 2, 10, 9.3, 10.52, 0.97772, 11, -21.38, 11.02, 0.02228, 3, 10, 15.12, 23.48, 0.81829, 11, -15.26, 23.84, 0.1817, 12, -39.02, 26.58, 1e-05, 3, 10, 27.43, 25.33, 0.49174, 11, -2.92, 25.4, 0.49041, 12, -26.59, 27.29, 0.01785, 3, 10, 46.22, 31.87, 0.04733, 11, 16.02, 31.51, 0.71235, 12, -7.29, 32.08, 0.24032, 3, 10, 54.61, 31.72, 0.01034, 11, 24.41, 31.17, 0.59687, 12, 1.06, 31.17, 0.39279, 2, 11, 43.46, 24.3, 0.16361, 12, 19.6, 23.01, 0.83639, 2, 11, 53.58, 8.86, 0.00303, 12, 28.64, 6.91, 0.99697, 1, 12, 29.99, -1.36, 1, 2, 10, 23.5, -1.57, 0.98829, 11, -7.46, -1.4, 0.01171, 2, 10, 36.85, -0.63, 0.00042, 11, 5.91, -0.77, 0.99958, 2, 11, 21.86, 0.62, 0.99436, 12, -3.57, 0.87, 0.00564, 2, 11, 38.01, 2.6, 0.00305, 12, 12.68, 1.73, 0.99695], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 16, 18, 18, 20, 22, 24, 24, 26], "width": 65, "height": 95}}, "tree_La": {"tree_La": {"type": "mesh", "uvs": [0.68983, 0.04789, 0.85972, 0.22288, 0.91911, 0.35667, 0.9889, 0.51388, 0.9894, 0.62768, 0.79739, 0.7839, 0.57415, 0.82578, 0.53252, 0.86653, 0.53881, 0.91149, 0.54824, 0.97892, 0.52435, 0.99594, 0.47183, 0.99022, 0.467, 0.91961, 0.46544, 0.85365, 0.39045, 0.79304, 0.3489, 0.70167, 0.006, 0.63261, 0.00556, 0.50613, 0.04529, 0.39815, 0.01837, 0.24665, 0.12454, 0.07567, 0.29129, 0.01048, 0.56777, 0.71514, 0.5175, 0.56674, 0.45323, 0.41276, 0.45093, 0.27227, 0.39562, 0.09516], "triangles": [26, 21, 0, 25, 26, 0, 25, 0, 1, 19, 25, 18, 20, 21, 26, 26, 19, 20, 25, 19, 26, 2, 25, 1, 2, 24, 25, 18, 25, 24, 23, 24, 2, 23, 2, 3, 23, 3, 4, 15, 16, 17, 24, 17, 18, 23, 17, 24, 17, 23, 15, 22, 23, 4, 15, 23, 22, 5, 22, 4, 14, 15, 22, 6, 22, 5, 14, 22, 6, 13, 14, 6, 7, 13, 6, 12, 13, 7, 12, 7, 8, 12, 8, 9, 11, 12, 9, 10, 11, 9], "vertices": [2, 40, 71.19, -19.97, 0.00516, 41, 35.18, -21.59, 0.99484, 3, 39, 91.82, -34.67, 0.00248, 40, 47.74, -34.55, 0.29073, 41, 11.1, -35.09, 0.7068, 3, 39, 74.03, -39.5, 0.05987, 40, 29.97, -39.42, 0.62353, 41, -6.89, -39.15, 0.3166, 3, 39, 53.12, -45.18, 0.29211, 40, 9.08, -45.15, 0.65227, 41, -28.01, -43.93, 0.05562, 3, 39, 38.1, -44.84, 0.47977, 40, -5.94, -44.85, 0.51025, 41, -43, -42.94, 0.00998, 2, 39, 17.93, -27.22, 0.86056, 40, -26.16, -27.29, 0.13944, 2, 39, 12.91, -7.22, 0.99757, 40, -31.23, -7.3, 0.00243, 1, 39, 7.63, -3.38, 1, 2, 38, 3.41, 7.3, 0.02206, 39, 1.69, -3.78, 0.97794, 2, 38, 4.25, -1.6, 0.96828, 39, -7.23, -4.39, 0.03172, 1, 38, 2.13, -3.85, 1, 1, 38, -2.55, -3.09, 1, 2, 38, -2.98, 6.23, 0.01369, 39, 0.78, 2.63, 0.98631, 1, 39, 9.49, 2.55, 1, 2, 39, 17.66, 9.02, 0.9995, 40, -26.53, 8.95, 0.0005, 2, 39, 29.81, 12.4, 0.86474, 40, -14.39, 12.36, 0.13526, 3, 39, 39.7, 42.68, 0.24499, 40, -4.57, 42.66, 0.72047, 41, -37.66, 44.42, 0.03454, 3, 39, 56.4, 42.28, 0.14084, 40, 12.13, 42.32, 0.74184, 41, -21, 43.31, 0.11732, 3, 39, 70.55, 38.38, 0.04001, 40, 26.29, 38.45, 0.60966, 41, -7.02, 38.81, 0.35034, 3, 39, 90.61, 40.26, 0.00044, 40, 46.34, 40.38, 0.2491, 41, 13.1, 39.83, 0.75046, 2, 40, 68.69, 30.41, 0.02673, 41, 34.96, 28.86, 0.97327, 2, 40, 76.94, 15.38, 5e-05, 41, 42.53, 13.46, 0.99995, 2, 39, 27.53, -7.03, 0.98316, 40, -16.61, -7.07, 0.01684, 2, 39, 47.23, -3.06, 0.00666, 40, 3.07, -3.05, 0.99334, 2, 40, 23.52, 2.2, 0.98913, 41, -11.43, 2.72, 0.01087, 2, 40, 42.07, 1.97, 0.00405, 41, 7.08, 1.65, 0.99595, 1, 41, 30.75, 4.96, 1], "hull": 22, "edges": [0, 42, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 24, 26, 26, 28, 14, 16, 16, 18, 32, 34, 2, 4, 4, 6, 40, 42], "width": 89, "height": 132}}, "tree_Lb": {"tree_Lb": {"type": "mesh", "uvs": [0.1729, 0.00551, 0.62333, 0.00684, 0.6921, 0.02593, 0.83949, 0.16269, 0.89443, 0.27904, 0.94938, 0.39538, 0.9224, 0.52257, 0.87183, 0.55217, 1, 0.64699, 1, 0.67852, 0.98157, 0.74071, 0.89565, 0.8012, 0.76932, 0.86662, 0.65021, 0.89272, 0.65232, 0.99287, 0.56659, 0.98614, 0.55305, 0.89806, 0.54304, 0.83295, 0.53311, 0.76834, 0.33375, 0.72541, 0.079, 0.66977, 0.01763, 0.57893, 0.02478, 0.50303, 0.16268, 0.4664, 0.14059, 0.38685, 0.07116, 0.29172, 0.00173, 0.1966, 0.08407, 0.05927, 0.73458, 0.76034, 0.72069, 0.81903, 0.36853, 0.63685, 0.65506, 0.64905, 0.48971, 0.52938, 0.50914, 0.39197, 0.43593, 0.26391, 0.3968, 0.16939, 0.40505, 0.06599], "triangles": [36, 0, 1, 36, 27, 0, 35, 27, 36, 26, 27, 35, 36, 2, 35, 2, 36, 1, 3, 35, 2, 3, 34, 35, 34, 3, 4, 25, 26, 35, 25, 35, 34, 24, 25, 34, 33, 34, 4, 24, 34, 33, 33, 4, 5, 23, 24, 33, 5, 7, 33, 32, 23, 33, 7, 32, 33, 5, 6, 7, 30, 23, 32, 31, 32, 7, 30, 32, 31, 23, 21, 22, 30, 21, 23, 30, 20, 21, 19, 20, 30, 31, 7, 8, 9, 31, 8, 9, 28, 31, 10, 28, 9, 18, 30, 31, 18, 31, 28, 19, 30, 18, 11, 28, 10, 29, 18, 28, 29, 28, 11, 17, 18, 29, 12, 29, 11, 13, 17, 29, 13, 29, 12, 16, 17, 13, 15, 16, 13, 15, 13, 14], "vertices": [2, 44, 95.33, 26.28, 0.00021, 45, 48.31, 24.23, 0.99979, 1, 45, 42.22, -17.21, 1, 2, 44, 87.48, -21.47, 0.0014, 45, 38.41, -23.13, 0.9986, 2, 44, 65.18, -33.05, 0.18228, 45, 15.64, -33.75, 0.81772, 2, 44, 46.85, -36.38, 0.58144, 45, -2.82, -36.29, 0.41856, 3, 43, 79.57, -39.3, 0.01613, 44, 28.52, -39.71, 0.86935, 45, -21.28, -38.83, 0.11453, 3, 43, 60.27, -35.16, 0.17083, 44, 9.27, -35.3, 0.81649, 45, -40.32, -33.6, 0.01268, 3, 43, 56.12, -30.09, 0.33653, 44, 5.19, -30.17, 0.66044, 45, -44.17, -28.3, 0.00303, 2, 43, 40.57, -40.75, 0.78771, 44, -10.5, -40.61, 0.21229, 2, 43, 35.73, -40.35, 0.82621, 44, -15.34, -40.13, 0.17379, 2, 43, 26.33, -37.84, 0.90152, 44, -24.7, -37.49, 0.09848, 2, 43, 17.71, -29.1, 0.96742, 44, -33.19, -28.62, 0.03258, 2, 43, 8.66, -16.55, 0.9997, 44, -42.07, -15.95, 0.0003, 1, 43, 5.58, -5.17, 1, 1, 42, 4.07, -3.43, 1, 2, 42, -3.9, -2.39, 0.9989, 43, -8.11, 3.78, 0.0011, 1, 43, 5.51, 3.9, 1, 1, 43, 15.58, 3.99, 1, 2, 43, 25.58, 4.08, 0.99723, 44, -24.85, 4.44, 0.00277, 2, 43, 33.71, 22, 0.69237, 44, -16.46, 22.24, 0.30763, 2, 43, 44.23, 44.89, 0.29302, 44, -5.62, 44.98, 0.70698, 3, 43, 58.65, 49.41, 0.19842, 44, 8.86, 49.29, 0.80047, 45, -37.1, 50.93, 0.00111, 3, 43, 70.24, 47.77, 0.15239, 44, 20.43, 47.49, 0.83946, 45, -25.62, 48.63, 0.00815, 3, 43, 74.79, 34.52, 0.07452, 44, 24.79, 34.17, 0.84991, 45, -21.83, 35.14, 0.07557, 3, 43, 87.17, 35.55, 0.00805, 44, 37.18, 35.02, 0.65579, 45, -9.42, 35.45, 0.33615, 2, 44, 52.39, 40.01, 0.29308, 45, 6, 39.79, 0.70692, 2, 44, 67.6, 45, 0.10758, 45, 21.41, 44.13, 0.89242, 2, 44, 87.9, 35.31, 0.01085, 45, 41.27, 33.57, 0.98915, 2, 43, 25.24, -14.69, 0.9848, 44, -25.46, -14.33, 0.0152, 2, 43, 16.34, -12.65, 0.99937, 44, -34.33, -12.16, 0.00063, 2, 43, 47.03, 17.64, 0.50393, 44, -3.21, 17.69, 0.49607, 2, 43, 42.93, -8.76, 0.85524, 44, -7.68, -8.65, 0.14476, 2, 43, 62.58, 5.03, 0.00409, 44, 12.16, 4.85, 0.99591, 2, 44, 33.05, 0.98, 0.9988, 45, -15.01, 1.63, 0.0012, 2, 44, 53.34, 5.83, 0.11243, 45, 5.47, 5.6, 0.88757, 2, 44, 68.18, 8.03, 0.00541, 45, 20.4, 7.16, 0.99459, 1, 45, 36.05, 4.16, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 28, 30, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 52, 54, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 30, 32, 32, 34, 6, 8, 8, 10, 48, 50, 50, 52], "width": 93, "height": 154}}, "tree_Lc": {"tree_Lc": {"type": "mesh", "uvs": [0.43388, 0, 0.56256, 0.09848, 0.7338, 0.24123, 0.90504, 0.38398, 0.95707, 0.45984, 0.97432, 0.56726, 0.99713, 0.70934, 0.93829, 0.82275, 0.73155, 0.85302, 0.55088, 0.83641, 0.56145, 0.90842, 0.56334, 0.99424, 0.47847, 0.99298, 0.47439, 0.91458, 0.45242, 0.84192, 0.2308, 0.86634, 0.09421, 0.83787, 0.05452, 0.71042, 0.00524, 0.55215, 0.06109, 0.4638, 0.31823, 0.43483, 0.11609, 0.32345, 0.17947, 0.1575, 0.226, 0.03564, 0.36083, 0, 0.48641, 0.69084, 0.47016, 0.56293, 0.58501, 0.43895, 0.498, 0.2924, 0.39181, 0.14433], "triangles": [29, 24, 0, 29, 0, 1, 23, 24, 29, 22, 23, 29, 28, 29, 1, 28, 1, 2, 22, 28, 21, 28, 22, 29, 20, 21, 28, 27, 28, 2, 27, 2, 3, 20, 28, 27, 26, 20, 27, 5, 25, 26, 27, 3, 4, 26, 17, 18, 18, 19, 20, 20, 26, 18, 5, 27, 4, 27, 5, 26, 6, 25, 5, 6, 8, 25, 8, 9, 25, 16, 17, 15, 25, 17, 26, 17, 25, 15, 9, 14, 25, 6, 7, 8, 25, 14, 15, 13, 14, 9, 10, 13, 9, 11, 13, 10, 11, 12, 13], "vertices": [1, 49, 38.86, -5.54, 1, 2, 48, 63.09, -11.99, 0.00204, 49, 25.69, -12.94, 0.99796, 3, 47, 81.96, -22.86, 0.00059, 48, 44.5, -22.35, 0.37755, 49, 6.74, -22.62, 0.62186, 3, 47, 63.25, -33.01, 0.08057, 48, 25.91, -32.72, 0.8191, 49, -12.21, -32.29, 0.10033, 3, 47, 53.56, -35.73, 0.19259, 48, 16.25, -35.55, 0.77809, 49, -21.97, -34.77, 0.02932, 3, 47, 40.18, -35.7, 0.45599, 48, 2.88, -35.67, 0.54277, 49, -35.34, -34.4, 0.00124, 2, 47, 22.5, -35.66, 0.81959, 48, -14.81, -35.84, 0.18041, 2, 47, 8.86, -30.34, 0.94982, 48, -28.5, -30.67, 0.05018, 2, 47, 6.42, -15.79, 0.99443, 48, -31.11, -16.15, 0.00557, 1, 47, 9.61, -3.57, 1, 2, 46, 3.41, 7.84, 0.01969, 47, 0.65, -3.48, 0.98031, 1, 46, 3.54, -2.8, 1, 1, 46, -2.32, -2.65, 1, 2, 46, -2.6, 7.07, 0.03837, 47, 0.44, 2.57, 0.96163, 1, 47, 9.55, 3.26, 1, 2, 47, 7.93, 18.77, 0.99394, 48, -30, 18.42, 0.00606, 2, 47, 12.3, 27.83, 0.96762, 48, -25.73, 27.54, 0.03238, 2, 47, 28.29, 29.12, 0.75085, 48, -9.76, 29.01, 0.24915, 2, 47, 48.14, 30.71, 0.25907, 48, 10.08, 30.83, 0.74093, 2, 47, 58.7, 25.88, 0.12746, 48, 20.69, 26.12, 0.87254, 3, 47, 60.66, 7.88, 0.00571, 48, 22.86, 8.14, 0.97382, 49, -13.77, 8.65, 0.02047, 2, 48, 37.74, 20.95, 0.31192, 49, 1.57, 20.9, 0.68808, 2, 48, 57.9, 14.95, 0.00248, 49, 21.5, 14.17, 0.99752, 1, 49, 36.13, 9.22, 1, 1, 49, 39.44, -0.53, 1, 1, 47, 27.99, -0.78, 1, 1, 48, 6.19, -1.04, 1, 3, 47, 58.48, -10.4, 0.01633, 48, 20.88, -10.17, 0.96544, 49, -16.42, -9.57, 0.01823, 2, 48, 39.47, -5.63, 0.26922, 49, 2.33, -5.72, 0.73078, 1, 49, 21.42, -0.57, 1], "hull": 25, "edges": [0, 48, 0, 2, 6, 8, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 46, 48, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 12, 14, 8, 10, 10, 12, 2, 4, 4, 6, 42, 44, 44, 46], "width": 69, "height": 124}}, "tree_Ld": {"tree_Ld": {"type": "mesh", "uvs": [0.5656, 0.04962, 0.67739, 0.15603, 0.80779, 0.28014, 0.87398, 0.39499, 0.99142, 0.42075, 0.99184, 0.57974, 0.90154, 0.68343, 0.78879, 0.81289, 0.51789, 0.83572, 0.51784, 0.89945, 0.51776, 0.99139, 0.49069, 1, 0.48074, 1, 0.46494, 0.98714, 0.46479, 0.89163, 0.46464, 0.79612, 0.44024, 0.77807, 0.29282, 0.78604, 0.17069, 0.64115, 0.19276, 0.59872, 0.04144, 0.54844, 0.003, 0.47609, 0.06991, 0.32729, 0.17136, 0.29638, 0.20747, 0.2416, 0.21106, 0.11934, 0.21344, 0.03854, 0.36447, 0.02129, 0.51551, 0.00403, 0.52641, 0.69727, 0.54378, 0.57858, 0.6087, 0.43667, 0.24478, 0.4486, 0.4496, 0.31108, 0.44064, 0.18289, 0.41869, 0.08619], "triangles": [35, 27, 28, 35, 28, 0, 34, 35, 0, 34, 0, 1, 25, 26, 27, 35, 25, 27, 33, 34, 1, 33, 1, 2, 33, 24, 34, 34, 25, 35, 34, 24, 25, 33, 23, 24, 31, 33, 2, 31, 2, 3, 32, 33, 31, 32, 23, 33, 22, 23, 32, 21, 22, 32, 20, 21, 32, 30, 32, 31, 19, 20, 32, 19, 32, 30, 3, 5, 31, 5, 3, 4, 5, 30, 31, 6, 30, 5, 29, 19, 30, 29, 30, 6, 18, 19, 29, 29, 17, 18, 16, 17, 29, 15, 16, 29, 7, 29, 6, 8, 15, 29, 7, 8, 29, 14, 15, 8, 9, 14, 8, 13, 14, 9, 10, 13, 9, 11, 12, 13, 10, 11, 13], "vertices": [2, 52, 93.29, -16.05, 0.00018, 53, 44.02, -14.03, 0.99982, 2, 52, 74.74, -27.46, 0.10058, 53, 26.01, -26.28, 0.89942, 3, 51, 107.47, -37.53, 0.00668, 52, 53.09, -40.76, 0.55141, 53, 5.01, -40.57, 0.44191, 3, 51, 88.3, -44.8, 0.08588, 52, 33.53, -46.88, 0.79547, 53, -14.25, -47.59, 0.11866, 3, 51, 83.84, -58.13, 0.1461, 52, 28.27, -59.92, 0.80768, 53, -18.9, -60.86, 0.04622, 3, 51, 57.45, -57.8, 0.31326, 52, 1.95, -58.02, 0.6804, 53, -45.28, -60.18, 0.00633, 2, 51, 40.38, -47.27, 0.5634, 52, -14.46, -46.49, 0.4366, 2, 51, 19.07, -34.11, 0.88188, 52, -34.94, -32.08, 0.11812, 2, 51, 15.72, -3.18, 1, 52, -36.45, -1.01, 0, 1, 51, 5.14, -3.02, 1, 1, 50, 2.94, -2.71, 1, 2, 50, -0.15, -4.14, 0.99996, 51, -11.5, 0.31, 4e-05, 1, 50, -1.28, -4.14, 1, 2, 50, -3.08, -2.01, 0.99414, 51, -9.33, 3.21, 0.00586, 1, 51, 6.53, 3.01, 1, 2, 51, 22.38, 2.8, 0.99911, 52, -29.44, 4.56, 0.00089, 2, 51, 25.42, 5.54, 0.989, 52, -26.25, 7.12, 0.011, 2, 51, 24.33, 22.36, 0.8859, 52, -26.33, 23.97, 0.1141, 3, 51, 48.58, 35.94, 0.41455, 52, -1.32, 36.09, 0.5854, 53, -52.9, 33.68, 6e-05, 3, 51, 55.58, 33.33, 0.24448, 52, 5.52, 33.06, 0.75256, 53, -45.93, 30.97, 0.00296, 3, 51, 64.17, 50.46, 0.03078, 52, 15.12, 49.65, 0.93968, 53, -37.11, 47.98, 0.02954, 3, 51, 76.24, 54.67, 0.00875, 52, 27.42, 53.13, 0.93303, 53, -24.98, 52.03, 0.05823, 2, 52, 51.49, 43.7, 0.77054, 53, -0.5, 43.73, 0.22946, 2, 52, 55.75, 31.79, 0.55989, 53, 4.31, 32.03, 0.44011, 2, 52, 64.52, 27.02, 0.23111, 53, 13.29, 27.66, 0.76889, 2, 52, 84.73, 25.11, 0.00385, 53, 33.56, 26.69, 0.99615, 1, 53, 46.96, 26.05, 1, 1, 53, 49.35, 8.76, 1, 1, 53, 51.74, -8.53, 1, 2, 51, 38.69, -4.47, 0.99294, 52, -13.6, -3.67, 0.00706, 2, 51, 58.36, -6.73, 0.11345, 52, 5.9, -7.1, 0.88655, 3, 51, 81.81, -14.47, 0.01226, 52, 28.85, -16.21, 0.93447, 53, -20.34, -17.17, 0.05327, 3, 51, 80.42, 27.05, 0.00994, 52, 29.94, 25.31, 0.9235, 53, -21.18, 24.35, 0.06656, 2, 52, 50.98, 0.34, 0.25208, 53, 1, 0.39, 0.74792, 1, 53, 22.3, 0.82, 1, 1, 53, 38.41, 2.88, 1], "hull": 29, "edges": [0, 56, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 16, 18, 18, 20, 26, 28, 28, 30, 10, 12, 12, 14, 0, 2, 2, 4, 48, 50, 50, 52, 52, 54, 54, 56], "width": 114, "height": 166}}, "tree_Ra": {"tree_Ra": {"type": "mesh", "uvs": [0.46744, 0.01234, 0.72305, 0.07647, 0.88889, 0.16275, 0.93695, 0.17974, 0.98748, 0.39996, 0.98774, 0.59395, 0.97529, 0.65695, 0.7963, 0.79182, 0.55643, 0.8201, 0.52875, 0.89197, 0.52739, 0.99084, 0.44858, 0.98959, 0.45679, 0.90188, 0.45606, 0.8377, 0.22242, 0.81346, 0.12398, 0.71007, 0.0124, 0.5908, 0.01156, 0.48831, 0.04215, 0.33983, 0.0655, 0.22648, 0.21839, 0.10104, 0.36733, 0.03003, 0.48932, 0.70458, 0.38514, 0.53832, 0.36304, 0.36652, 0.71836, 0.35513, 0.6668, 0.54435, 0.65523, 0.19046, 0.37427, 0.19917], "triangles": [27, 0, 1, 27, 1, 2, 28, 21, 0, 28, 0, 27, 20, 21, 28, 25, 27, 2, 25, 2, 3, 28, 19, 20, 24, 19, 28, 27, 24, 28, 18, 19, 24, 25, 3, 4, 24, 27, 25, 26, 23, 24, 25, 26, 24, 26, 25, 4, 17, 23, 16, 24, 17, 18, 23, 17, 24, 26, 4, 5, 6, 26, 5, 22, 23, 26, 15, 16, 23, 7, 26, 6, 15, 23, 22, 22, 26, 7, 14, 15, 22, 8, 22, 7, 13, 14, 22, 8, 13, 22, 9, 13, 8, 12, 13, 9, 10, 12, 9, 11, 12, 10], "vertices": [1, 16, 30.84, 4.97, 1, 2, 15, 55.91, -15.04, 0.04251, 16, 24.32, -15.29, 0.95749, 2, 15, 47.13, -28.32, 0.27555, 16, 15.4, -28.49, 0.72445, 2, 15, 45.42, -32.16, 0.3233, 16, 13.66, -32.3, 0.6767, 3, 14, 57.16, -35.49, 0.01341, 15, 22.39, -36.64, 0.74179, 16, -9.42, -36.54, 0.2448, 3, 14, 36.84, -36.94, 0.17511, 15, 2.03, -37.09, 0.79026, 16, -29.79, -36.78, 0.03463, 3, 14, 30.17, -36.42, 0.24967, 15, -4.61, -36.24, 0.73496, 16, -36.41, -35.86, 0.01537, 2, 14, 15.05, -23.31, 0.6194, 15, -19.06, -22.4, 0.3806, 2, 14, 10.76, -4.62, 0.98325, 15, -22.43, -3.52, 0.01675, 1, 14, 3.08, -2.97, 1, 1, 13, 2.9, -3.38, 1, 1, 13, -3.32, -3.25, 1, 1, 14, 1.64, 2.63, 1, 2, 14, 8.36, 3.16, 0.99979, 15, -24.45, 4.37, 0.00021, 2, 14, 9.6, 21.75, 0.88574, 15, -22.29, 22.88, 0.11426, 3, 14, 19.89, 30.27, 0.65844, 15, -11.6, 30.88, 0.33494, 16, -42.7, 31.33, 0.00663, 3, 14, 31.76, 39.94, 0.37046, 15, 0.74, 39.96, 0.58021, 16, -30.28, 40.27, 0.04933, 3, 14, 42.49, 40.77, 0.22705, 15, 11.49, 40.25, 0.65496, 16, -19.52, 40.45, 0.11799, 3, 14, 58.21, 39.45, 0.06009, 15, 27.13, 38.16, 0.58558, 16, -3.9, 38.2, 0.35433, 3, 14, 70.21, 38.44, 0.0124, 15, 39.07, 36.57, 0.41613, 16, 8.02, 36.49, 0.57147, 2, 15, 52.49, 24.77, 0.13315, 16, 21.32, 24.55, 0.86685, 2, 15, 60.19, 13.16, 0.01134, 16, 28.9, 12.86, 0.98866, 1, 14, 22.49, 1.52, 1, 3, 14, 39.32, 10.96, 0.15809, 15, 6.86, 10.63, 0.8336, 16, -24.45, 10.89, 0.00831, 3, 14, 57.2, 13.96, 0.01007, 15, 24.86, 12.76, 0.76255, 16, -6.43, 12.83, 0.22738, 2, 15, 26.65, -15.28, 0.70314, 16, -4.94, -15.23, 0.29686, 3, 14, 40.25, -11.28, 0.07292, 15, 6.7, -11.63, 0.91808, 16, -24.85, -11.37, 0.00899, 2, 15, 43.83, -9.93, 0.07156, 16, 12.3, -10.06, 0.92844, 2, 15, 42.45, 12.24, 0.10801, 16, 11.15, 12.13, 0.89199], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 38, 40, 40, 42, 28, 30, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38], "width": 79, "height": 105}}, "tree_Rb": {"tree_Rb": {"type": "mesh", "uvs": [0.69917, 0.00648, 0.90407, 0.11348, 0.88758, 0.20893, 0.8881, 0.31901, 1, 0.31894, 1, 0.35736, 0.93766, 0.45167, 0.9382, 0.47593, 0.985, 0.58319, 0.94892, 0.6341, 0.61332, 0.73838, 0.52744, 0.71265, 0.5195, 0.80305, 0.51501, 0.89258, 0.4977, 0.97275, 0.45553, 0.99858, 0.39629, 0.98783, 0.4098, 0.89769, 0.42477, 0.79783, 0.43702, 0.71609, 0.33999, 0.70328, 0.23344, 0.62958, 0.12428, 0.55407, 0.01513, 0.47856, 0.01229, 0.38196, 0.00946, 0.28535, 0.24366, 0.14788, 0.45086, 0.01808, 0.62748, 0.00228, 0.57444, 0.60929, 0.54021, 0.50773, 0.48641, 0.39507, 0.49521, 0.25133, 0.59107, 0.12924], "triangles": [33, 27, 28, 26, 27, 33, 1, 33, 0, 32, 26, 33, 33, 28, 0, 2, 33, 1, 3, 4, 5, 32, 25, 26, 31, 32, 3, 2, 32, 33, 3, 32, 2, 25, 31, 24, 32, 31, 25, 6, 3, 5, 23, 24, 31, 6, 31, 3, 30, 6, 7, 6, 30, 31, 22, 23, 31, 30, 22, 31, 29, 30, 7, 29, 7, 8, 21, 22, 30, 29, 21, 30, 9, 29, 8, 20, 21, 29, 29, 19, 20, 10, 29, 9, 11, 29, 10, 11, 19, 29, 12, 19, 11, 18, 19, 12, 13, 18, 12, 17, 18, 13, 14, 17, 13, 16, 17, 14, 15, 16, 14], "vertices": [1, 20, 50.62, -5.24, 1, 2, 19, 81.22, -29.31, 0.00228, 20, 39.08, -24.29, 0.99772, 2, 19, 67.7, -28.87, 0.05967, 20, 25.63, -25.67, 0.94033, 2, 19, 52.22, -29.92, 0.41159, 20, 10.42, -28.78, 0.58841, 2, 19, 52.81, -38.85, 0.52347, 20, 12.21, -37.56, 0.47653, 2, 19, 47.4, -39.21, 0.54626, 20, 6.9, -38.63, 0.45374, 3, 18, 67.44, -35.99, 0.00164, 19, 33.81, -35.09, 0.76792, 20, -7.13, -36.38, 0.23044, 3, 18, 64.03, -36.16, 0.00469, 19, 30.4, -35.36, 0.83594, 20, -10.47, -37.1, 0.15937, 3, 18, 49.06, -40.48, 0.0324, 19, 15.55, -40.08, 0.94161, 20, -24.55, -43.76, 0.026, 3, 18, 41.77, -37.87, 0.05034, 19, 8.2, -37.67, 0.93813, 20, -32.16, -42.36, 0.01154, 2, 18, 26.05, -11.61, 0.54838, 19, -8.22, -11.83, 0.45162, 2, 18, 29.42, -4.6, 0.85675, 19, -5.05, -4.74, 0.14325, 1, 18, 16.65, -4.46, 1, 1, 18, 4.03, -4.58, 1, 2, 17, 4.17, -0.51, 0.87658, 18, -7.32, -3.63, 0.12342, 1, 17, 0.8, -4.15, 1, 1, 17, -3.94, -2.64, 1, 1, 18, 2.98, 3.8, 1, 1, 18, 17.1, 3.14, 1, 2, 18, 28.65, 2.61, 0.95747, 19, -6, 2.45, 0.04253, 2, 18, 30.16, 10.43, 0.49204, 19, -4.71, 10.31, 0.50796, 3, 18, 40.22, 19.35, 0.07296, 19, 5.11, 19.49, 0.92686, 20, -42.88, 13.87, 0.00018, 3, 18, 50.52, 28.48, 0.00145, 19, 15.17, 28.9, 0.97279, 20, -34.17, 24.54, 0.02575, 2, 19, 25.22, 38.31, 0.89329, 20, -25.46, 35.21, 0.10671, 2, 19, 38.8, 39.42, 0.73218, 20, -12.16, 38.13, 0.26782, 2, 19, 52.38, 40.53, 0.52856, 20, 1.15, 41.05, 0.47144, 2, 19, 72.94, 23.1, 0.08277, 20, 23.86, 26.53, 0.91723, 1, 20, 45.08, 13.91, 1, 1, 20, 50.07, 0.5, 1, 2, 18, 44.12, -7.8, 0.03443, 19, 9.74, -7.54, 0.96557, 2, 19, 23.85, -3.88, 0.99904, 20, -21.17, -6.78, 0.00096, 2, 19, 39.42, 1.45, 0.99852, 20, -6.45, 0.59, 0.00148, 2, 19, 59.69, 2.07, 0.0027, 20, 13.55, 3.91, 0.9973, 1, 20, 31.94, -0.19, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 38, 40, 54, 56, 18, 20, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 50, 52, 52, 54, 46, 48, 48, 50], "width": 80, "height": 141}}, "tree_Rc": {"tree_Rc": {"type": "mesh", "uvs": [0.72443, 0.00916, 0.74769, 0.11012, 0.74821, 0.23802, 0.97496, 0.23773, 0.94662, 0.34253, 0.91828, 0.44733, 1, 0.52739, 1, 0.5764, 0.87261, 0.70753, 0.63652, 0.73308, 0.49426, 0.80982, 0.48115, 0.90181, 0.46803, 0.99381, 0.37346, 0.99352, 0.37308, 0.96009, 0.38735, 0.89255, 0.40636, 0.80254, 0.3721, 0.69996, 0.09603, 0.61049, 0.03582, 0.54751, 0.00627, 0.4477, 0.10162, 0.3297, 0.17808, 0.23507, 0.41431, 0.11359, 0.541, 0.04844, 0.66065, 0.00168, 0.53027, 0.60052, 0.52385, 0.46934, 0.5292, 0.34611, 0.54772, 0.22584, 0.58731, 0.13109], "triangles": [30, 24, 25, 23, 24, 30, 1, 30, 25, 1, 25, 0, 29, 23, 30, 22, 23, 29, 30, 1, 2, 29, 30, 2, 4, 2, 3, 28, 22, 29, 28, 29, 2, 21, 22, 28, 4, 28, 2, 5, 28, 4, 27, 21, 28, 27, 28, 5, 20, 21, 27, 19, 20, 27, 5, 6, 7, 26, 27, 5, 26, 5, 7, 18, 19, 27, 26, 18, 27, 17, 18, 26, 8, 26, 7, 9, 26, 8, 17, 26, 9, 10, 16, 17, 9, 10, 17, 15, 16, 10, 11, 15, 10, 14, 15, 11, 12, 13, 14, 11, 12, 14], "vertices": [1, 24, 54.75, -6.98, 1, 2, 23, 88.7, -17.24, 0.00018, 24, 40.77, -12.39, 0.99982, 2, 23, 70.01, -19.28, 0.13279, 24, 22.47, -16.69, 0.86721, 2, 23, 72.25, -39.8, 0.32896, 24, 27.17, -36.78, 0.67104, 2, 23, 56.66, -38.87, 0.46222, 24, 11.58, -37.75, 0.53778, 3, 22, 75.24, -39.63, 0.00078, 23, 41.06, -37.95, 0.79427, 24, -4.01, -38.72, 0.20495, 3, 22, 63.97, -47.82, 0.00966, 23, 30.15, -46.59, 0.93895, 24, -13.79, -48.62, 0.0514, 3, 22, 56.78, -48.28, 0.01739, 23, 22.99, -47.36, 0.95212, 24, -20.81, -50.25, 0.03048, 3, 22, 36.8, -37.96, 0.0946, 23, 2.59, -37.89, 0.9053, 24, -42.21, -43.31, 0.0001, 2, 22, 31.66, -16.76, 0.48685, 23, -3.43, -16.92, 0.51315, 1, 22, 19.57, -4.58, 1, 1, 22, 5.99, -4.26, 1, 2, 21, 4.44, -2.86, 0.97444, 22, -7.58, -3.94, 0.02556, 2, 21, -4.17, -2.82, 0.99134, 22, -8.09, 4.65, 0.00866, 2, 21, -4.2, 2.1, 0.65409, 22, -3.19, 5, 0.34591, 1, 22, 6.8, 4.35, 1, 1, 22, 20.12, 3.48, 1, 2, 22, 34.96, 7.56, 0.4855, 23, -1.15, 7.52, 0.5145, 2, 23, 9.25, 33.9, 0.95178, 24, -44.28, 28.75, 0.04822, 2, 23, 17.88, 40.33, 0.9047, 24, -36.5, 36.18, 0.0953, 2, 23, 32.18, 44.57, 0.79656, 24, -22.82, 42.12, 0.20344, 2, 23, 50.35, 37.79, 0.50233, 24, -3.96, 37.58, 0.49767, 2, 23, 64.92, 32.35, 0.19653, 24, 11.16, 33.95, 0.80347, 1, 24, 33.42, 17.05, 1, 1, 24, 45.35, 7.98, 1, 1, 24, 54.51, -1.07, 1, 2, 22, 50.48, -5.86, 0.00504, 23, 14.92, -5.24, 0.99496, 1, 23, 34.03, -2.6, 1, 1, 24, 2.49, -0.86, 1, 1, 24, 20.09, 1.49, 1, 1, 24, 34.47, 1.13, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 48, 50, 28, 30, 30, 32, 20, 22, 22, 24, 40, 42, 42, 44, 6, 8, 8, 10, 44, 46, 46, 48], "width": 91, "height": 147}}, "tree_Rd": {"tree_Rd": {"type": "mesh", "uvs": [0.69767, 0.0571, 0.80944, 0.14975, 0.97919, 0.29047, 0.97883, 0.43757, 0.94558, 0.54326, 0.93953, 0.65364, 0.86386, 0.75527, 0.62885, 0.80976, 0.44345, 0.83439, 0.43527, 0.9021, 0.42447, 0.99154, 0.38642, 0.99625, 0.34851, 0.98092, 0.35694, 0.89773, 0.36436, 0.82459, 0.27349, 0.84186, 0.06376, 0.79157, 0.0634, 0.70596, 0.15215, 0.58281, 0.02143, 0.4742, 0.01891, 0.39971, 0.1903, 0.25955, 0.31971, 0.14138, 0.4059, 0.0592, 0.49565, 0, 0.55782, 0, 0.46975, 0.71363, 0.48329, 0.58309, 0.51822, 0.46273, 0.55797, 0.28853, 0.54111, 0.17609, 0.54706, 0.07456], "triangles": [31, 24, 25, 31, 25, 0, 23, 24, 31, 30, 23, 31, 22, 23, 30, 30, 0, 1, 0, 30, 31, 29, 30, 1, 29, 1, 2, 3, 29, 2, 22, 29, 21, 29, 22, 30, 28, 21, 29, 28, 29, 3, 20, 21, 28, 19, 20, 28, 4, 28, 3, 18, 19, 28, 27, 18, 28, 27, 28, 4, 5, 27, 4, 26, 18, 27, 5, 6, 27, 6, 26, 27, 17, 18, 26, 17, 15, 16, 7, 26, 6, 26, 14, 17, 8, 14, 26, 7, 8, 26, 14, 15, 17, 13, 14, 8, 9, 13, 8, 12, 13, 9, 10, 12, 9, 11, 12, 10], "vertices": [2, 27, 84.02, -8.37, 9e-05, 28, 42.94, -13.26, 0.99991, 2, 27, 72.91, -21.12, 0.04676, 28, 30.47, -24.69, 0.95324, 3, 26, 97.46, -44.17, 0.00186, 27, 56.03, -40.48, 0.35189, 28, 11.53, -42.04, 0.64625, 3, 26, 76.56, -46.57, 0.04706, 27, 35.35, -44.29, 0.63239, 28, -9.45, -43.5, 0.32055, 3, 26, 61.19, -45.22, 0.1765, 27, 19.92, -43.97, 0.71134, 28, -24.75, -41.46, 0.11216, 3, 26, 45.45, -46.48, 0.39168, 27, 4.29, -46.29, 0.58819, 28, -40.53, -42.02, 0.02013, 3, 26, 30.19, -41.1, 0.58856, 27, -11.29, -41.95, 0.41078, 28, -55.54, -35.96, 0.00066, 2, 26, 19.89, -20.06, 0.90797, 27, -22.99, -21.66, 0.09203, 2, 26, 14.38, -3.15, 0.99999, 27, -29.63, -5.16, 1e-05, 1, 26, 4.67, -3.51, 1, 2, 25, 3.34, -3.1, 0.99853, 26, -8.15, -3.98, 0.00147, 1, 25, -0.23, -3.77, 1, 2, 25, -3.8, -1.58, 0.97543, 26, -7.47, 3.29, 0.02457, 1, 26, 4.44, 3.88, 1, 1, 26, 14.91, 4.39, 1, 1, 26, 11.47, 12.59, 1, 2, 26, 16.33, 33.01, 0.97819, 27, -30.12, 31.04, 0.02181, 2, 26, 28.48, 34.46, 0.9101, 27, -18.09, 33.31, 0.0899, 3, 26, 46.94, 28.21, 0.39693, 27, 0.74, 28.32, 0.59468, 28, -35.7, 32.53, 0.00838, 3, 26, 60.94, 42.21, 0.04765, 27, 13.77, 43.24, 0.87385, 28, -21.09, 45.89, 0.0785, 3, 26, 71.5, 43.68, 0.0168, 27, 24.2, 45.42, 0.85656, 28, -10.48, 46.89, 0.12664, 2, 27, 46.85, 33.24, 0.46166, 28, 10.66, 32.25, 0.53834, 2, 27, 65.68, 24.36, 0.05259, 28, 28.38, 21.32, 0.94741, 2, 27, 78.71, 18.54, 0.00059, 28, 40.68, 14.07, 0.99941, 1, 28, 49.73, 6.26, 1, 1, 28, 50.15, 0.43, 1, 2, 26, 31.82, -3.61, 0.99909, 27, -12.2, -4.44, 0.00091, 1, 27, 6.38, -2.29, 1, 1, 27, 23.91, -2.37, 1, 1, 28, 8.99, -2.52, 1, 2, 27, 64.6, 2.99, 3e-05, 28, 24.92, 0.2, 0.99997, 1, 28, 39.44, 0.68, 1], "hull": 26, "edges": [4, 6, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 44, 46, 46, 48, 48, 50, 16, 18, 18, 20, 24, 26, 26, 28, 6, 8, 8, 10, 36, 38, 40, 42, 42, 44, 0, 2, 2, 4, 50, 0], "width": 94, "height": 143}}, "tree_Re": {"tree_Re": {"type": "mesh", "uvs": [0.85009, 0.03259, 0.86932, 0.09587, 0.86898, 0.2202, 0.88659, 0.24065, 0.99315, 0.27214, 0.98913, 0.36718, 0.99417, 0.52708, 0.78534, 0.63883, 0.76308, 0.72862, 0.63948, 0.79533, 0.55008, 0.86027, 0.49151, 0.91022, 0.4446, 0.91242, 0.44238, 0.99017, 0.38801, 1, 0.35531, 0.98955, 0.3683, 0.90316, 0.28733, 0.89299, 0.12218, 0.81918, 0.03729, 0.71918, 0.00436, 0.59288, 0.04833, 0.48134, 0.0923, 0.3698, 0.23091, 0.23384, 0.37035, 0.17614, 0.36033, 0.11324, 0.41147, 0.05672, 0.56088, 0, 0.58179, 0, 0.65192, 0.01846, 0.37874, 0.82399, 0.40247, 0.71987, 0.4337, 0.61505, 0.4252, 0.49254, 0.5655, 0.38947, 0.57236, 0.30444, 0.62233, 0.20307, 0.63232, 0.09756, 0.69934, 0.50195], "triangles": [2, 36, 1, 1, 36, 37, 37, 0, 1, 37, 29, 0, 36, 24, 37, 26, 37, 25, 26, 27, 37, 27, 28, 37, 37, 24, 25, 37, 28, 29, 38, 5, 6, 33, 34, 38, 38, 34, 5, 35, 5, 34, 5, 35, 3, 3, 36, 2, 36, 3, 35, 33, 22, 34, 22, 23, 34, 34, 23, 35, 35, 23, 24, 5, 3, 4, 35, 24, 36, 31, 32, 8, 8, 32, 7, 31, 19, 32, 33, 32, 21, 21, 32, 20, 20, 32, 19, 32, 38, 7, 7, 38, 6, 32, 33, 38, 21, 22, 33, 15, 16, 12, 10, 11, 12, 30, 10, 12, 30, 12, 16, 16, 17, 30, 17, 18, 30, 10, 30, 9, 30, 31, 9, 30, 18, 31, 18, 19, 31, 9, 31, 8, 14, 15, 13, 13, 15, 12], "vertices": [2, 32, 100.55, -22.79, 0.00277, 33, 40.46, -25.8, 0.99723, 2, 32, 87.21, -27.6, 0.05633, 33, 26.81, -29.63, 0.94367, 3, 31, 116.52, -33.82, 0.00476, 32, 60.17, -32.47, 0.64991, 33, -0.52, -32.52, 0.34532, 3, 31, 112.39, -36.65, 0.01333, 32, 56.1, -35.39, 0.79616, 33, -4.78, -35.14, 0.1905, 3, 31, 107.55, -50.58, 0.03692, 32, 51.58, -49.42, 0.90976, 33, -10.32, -48.8, 0.05332, 3, 31, 86.73, -53.38, 0.12849, 32, 30.83, -52.69, 0.8658, 33, -31.25, -50.56, 0.0057, 2, 31, 51.92, -59.51, 0.46381, 32, -3.83, -59.61, 0.53619, 3, 30, 73.1, -39.13, 0.06922, 31, 23.54, -38.21, 0.8158, 32, -32.68, -38.96, 0.11497, 3, 30, 53.08, -38.74, 0.31689, 31, 3.52, -38.63, 0.67327, 32, -52.69, -39.83, 0.00983, 2, 30, 36.68, -25.48, 0.75966, 31, -13.4, -26.04, 0.24034, 2, 30, 21.15, -16.32, 0.99323, 31, -29.28, -17.52, 0.00677, 1, 30, 9.36, -10.51, 1, 1, 30, 8.21, -4.88, 1, 2, 29, 5.7, -2.82, 0.98044, 30, -8.89, -6.61, 0.01956, 1, 29, -0.93, -4.99, 1, 1, 29, -4.92, -2.68, 1, 1, 30, 9.16, 4.6, 1, 2, 30, 10.24, 14.67, 0.99951, 31, -41.44, 13.01, 0.00049, 2, 30, 24.09, 36.58, 0.88371, 31, -28.48, 35.47, 0.11629, 3, 30, 44.83, 49.45, 0.57293, 31, -8.27, 49.15, 0.42381, 32, -66.46, 47.67, 0.00326, 3, 30, 72.09, 56.69, 0.21584, 31, 18.67, 57.49, 0.71548, 32, -39.71, 56.6, 0.06867, 3, 30, 97.2, 54.23, 0.05016, 31, 43.86, 56.04, 0.66475, 32, -14.5, 55.73, 0.28509, 3, 30, 122.3, 51.77, 0.00286, 31, 69.04, 54.6, 0.35915, 32, 10.71, 54.86, 0.63799, 3, 31, 101.36, 42.6, 0.04775, 32, 43.3, 43.58, 0.89244, 33, -11.82, 44.56, 0.05981, 3, 31, 116.62, 27.79, 0.00182, 32, 58.88, 29.12, 0.54256, 33, 2.67, 29.01, 0.45562, 2, 32, 72.34, 32.81, 0.1602, 33, 16.36, 31.71, 0.8398, 2, 32, 85.75, 28.9, 0.03272, 33, 29.45, 26.84, 0.96728, 1, 33, 43.86, 10.05, 1, 1, 33, 44.13, 7.51, 1, 1, 33, 40.99, -1.43, 1, 1, 30, 26.68, 5.37, 1, 2, 30, 49.87, 5.18, 0.74963, 31, -1.45, 5.12, 0.25037, 2, 30, 73.32, 4.09, 0.00203, 31, 22.02, 4.98, 0.99797, 3, 30, 100.09, 8.28, 0.00025, 31, 48.6, 10.24, 0.89587, 32, -8.72, 10.05, 0.10388, 2, 31, 73.78, -3.1, 0.00418, 32, 16.75, -2.72, 0.99582, 2, 31, 92.47, -0.99, 0, 32, 35.39, -0.19, 1, 2, 32, 58.52, -2.19, 0.49308, 33, 0.04, -2.19, 0.50692, 2, 32, 81.68, 0.78, 0.00028, 33, 23.35, -0.92, 0.99972, 2, 31, 51.78, -23.12, 0.59444, 32, -4.79, -23.23, 0.40556], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 20, 22, 22, 24, 24, 26, 40, 42, 42, 44, 8, 10, 10, 12, 46, 48, 14, 16, 12, 14], "width": 122, "height": 221}}, "tree_Rf": {"tree_Rf": {"type": "mesh", "uvs": [0.74229, 0.03031, 0.81201, 0.13016, 0.88173, 0.23, 0.7897, 0.29192, 0.86156, 0.39459, 1, 0.55421, 0.96803, 0.66873, 0.96795, 0.81403, 0.95072, 0.9446, 0.79203, 0.98812, 0.51206, 0.99698, 0.27319, 0.91776, 0.07055, 0.86892, 0, 0.78878, 0.00979, 0.68108, 0.08346, 0.56217, 0.08274, 0.43101, 0.22323, 0.29589, 0.33247, 0.19082, 0.33259, 0.11504, 0.45221, 0.05823, 0.57183, 0.00143, 0.56479, 0.27844, 0.68165, 0.18418, 0.50001, 0.15218, 0.63084, 0.09683, 0.54193, 0.41108, 0.54193, 0.55982, 0.52652, 0.71695, 0.37918, 0.86829, 0.60019, 0.89769], "triangles": [3, 26, 22, 22, 17, 18, 22, 18, 24, 22, 23, 3, 3, 23, 2, 22, 24, 23, 23, 1, 2, 18, 19, 24, 24, 25, 23, 23, 25, 1, 19, 20, 24, 24, 20, 25, 25, 0, 1, 20, 21, 25, 25, 21, 0, 28, 29, 14, 7, 28, 6, 14, 15, 28, 28, 27, 6, 28, 15, 27, 6, 27, 5, 27, 16, 26, 26, 16, 17, 16, 27, 15, 27, 4, 5, 4, 26, 3, 26, 4, 27, 26, 17, 22, 11, 29, 10, 10, 30, 9, 10, 29, 30, 8, 9, 7, 7, 9, 30, 11, 12, 29, 29, 28, 30, 30, 28, 7, 12, 13, 29, 29, 13, 14], "vertices": [1, 37, 44.93, -13.43, 1, 1, 37, 31.41, -21.2, 1, 2, 36, 75.57, -28.12, 0.01688, 37, 17.89, -28.96, 0.98312, 2, 36, 65.83, -20.42, 0.20676, 37, 8.5, -20.82, 0.79324, 3, 35, 105.17, -29.29, 0.00496, 36, 52.3, -29.04, 0.79476, 37, -5.4, -28.82, 0.20028, 3, 35, 84.38, -45.13, 0.09936, 36, 31.59, -44.97, 0.89814, 37, -26.81, -43.81, 0.0025, 2, 35, 67.98, -43.97, 0.27854, 36, 15.18, -43.9, 0.72146, 2, 35, 47.64, -46.37, 0.67213, 36, -5.15, -46.39, 0.32787, 2, 35, 29.16, -46.89, 0.8704, 36, -23.62, -47, 0.1296, 2, 35, 21.28, -32.48, 0.94753, 36, -31.57, -32.63, 0.05247, 2, 35, 16.88, -5.93, 1, 36, -36.1, -6.11, 0, 2, 35, 25.28, 18.15, 0.91697, 36, -27.81, 18.02, 0.08303, 2, 35, 29.84, 38.28, 0.69967, 36, -23.35, 38.17, 0.30033, 2, 35, 40.27, 46.33, 0.58424, 36, -12.96, 46.27, 0.41576, 2, 35, 55.46, 47.18, 0.40661, 36, 2.22, 47.19, 0.59339, 3, 35, 72.94, 42.12, 0.14579, 36, 19.73, 42.22, 0.84897, 37, -34.73, 43.82, 0.00524, 3, 35, 91.3, 44.36, 0.0212, 36, 38.08, 44.54, 0.91446, 37, -16.3, 45.32, 0.06434, 2, 36, 58.63, 33.48, 0.60184, 37, 3.74, 33.35, 0.39816, 2, 36, 74.62, 24.88, 0.11297, 37, 19.32, 24.04, 0.88703, 2, 36, 85.22, 26.18, 0.01447, 37, 29.97, 24.86, 0.98553, 1, 37, 38.85, 14.03, 1, 1, 37, 47.72, 3.2, 1, 1, 37, 8.73, 0.85, 1, 1, 37, 22.85, -9.31, 1, 2, 36, 81.99, 9.59, 0.00091, 37, 26, 8.43, 0.99909, 1, 37, 34.75, -3.49, 1, 1, 36, 46.25, 1.14, 1, 2, 35, 78.43, -1.55, 0.00019, 36, 25.43, -1.43, 0.99981, 1, 36, 3.26, -2.66, 1, 2, 35, 33.41, 8.87, 0.96665, 36, -19.64, 8.77, 0.03335, 2, 35, 31.78, -12.69, 0.99211, 36, -21.17, -12.79, 0.00789], "hull": 22, "edges": [0, 42, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 36, 38, 10, 12, 16, 18, 18, 20, 28, 30, 32, 34, 34, 36, 38, 40, 40, 42, 0, 2, 2, 4], "width": 96, "height": 141}}}}], "animations": {"idle": {"slots": {"light2": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": [2.189, 1, 2.211, 1, 2.189, 1, 2.211, 1, 2.189, 1, 2.211, 1, 2.189, 0, 2.211, 0.59]}, {"time": 2.2333, "color": "ffffff96", "curve": "stepped"}, {"time": 3, "color": "ffffff96", "curve": [3.056, 1, 3.111, 1, 3.056, 1, 3.111, 1, 3.056, 1, 3.111, 1, 3.056, 0.59, 3.111, 0]}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 7.1667, "color": "ffffff00", "curve": [7.189, 1, 7.211, 1, 7.189, 1, 7.211, 1, 7.189, 1, 7.211, 1, 7.189, 0, 7.211, 0.59]}, {"time": 7.2333, "color": "ffffff96", "curve": "stepped"}, {"time": 8, "color": "ffffff96", "curve": [8.056, 1, 8.111, 1, 8.056, 1, 8.111, 1, 8.056, 1, 8.111, 1, 8.056, 0.59, 8.111, 0]}, {"time": 8.1667, "color": "ffffff00"}]}, "light3": {"rgba": [{"time": 2.1667, "color": "ffffff00", "curve": [2.189, 1, 2.211, 1, 2.189, 1, 2.211, 1, 2.189, 1, 2.211, 1, 2.189, 0, 2.211, 0.59]}, {"time": 2.2333, "color": "ffffff96", "curve": "stepped"}, {"time": 3, "color": "ffffff96", "curve": [3.056, 1, 3.111, 1, 3.056, 1, 3.111, 1, 3.056, 1, 3.111, 1, 3.056, 0.59, 3.111, 0]}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 7.1667, "color": "ffffff00", "curve": [7.189, 1, 7.211, 1, 7.189, 1, 7.211, 1, 7.189, 1, 7.211, 1, 7.189, 0, 7.211, 0.59]}, {"time": 7.2333, "color": "ffffff96", "curve": "stepped"}, {"time": 8, "color": "ffffff96", "curve": [8.056, 1, 8.111, 1, 8.056, 1, 8.111, 1, 8.056, 1, 8.111, 1, 8.056, 0.59, 8.111, 0]}, {"time": 8.1667, "color": "ffffff00"}]}, "LT1": {"rgba": [{"time": 1.6667, "color": "ffffff00", "curve": [1.744, 1, 1.822, 1, 1.744, 1, 1.822, 1, 1.744, 1, 1.822, 1, 1.744, 0, 1.822, 0.78]}, {"time": 1.9, "color": "ffffffc8", "curve": [1.978, 1, 2.056, 1, 1.978, 1, 2.056, 1, 1.978, 1, 2.056, 1, 1.978, 0.78, 2.056, 0]}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.6667, "color": "ffffff00", "curve": [6.744, 1, 6.822, 1, 6.744, 1, 6.822, 1, 6.744, 1, 6.822, 1, 6.744, 0, 6.822, 0.78]}, {"time": 6.9, "color": "ffffffc8", "curve": [6.978, 1, 7.056, 1, 6.978, 1, 7.056, 1, 6.978, 1, 7.056, 1, 6.978, 0.78, 7.056, 0]}, {"time": 7.1333, "color": "ffffff00"}]}, "LT2": {"rgba": [{"time": 1.8333, "color": "ffffff00", "curve": [1.911, 1, 1.989, 1, 1.911, 1, 1.989, 1, 1.911, 1, 1.989, 1, 1.911, 0, 1.989, 0.78]}, {"time": 2.0667, "color": "ffffffc8", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 0.78, 2.222, 0]}, {"time": 2.3, "color": "ffffff00", "curve": "stepped"}, {"time": 6.8333, "color": "ffffff00", "curve": [6.911, 1, 6.989, 1, 6.911, 1, 6.989, 1, 6.911, 1, 6.989, 1, 6.911, 0, 6.989, 0.78]}, {"time": 7.0667, "color": "ffffffc8", "curve": [7.144, 1, 7.222, 1, 7.144, 1, 7.222, 1, 7.144, 1, 7.222, 1, 7.144, 0.78, 7.222, 0]}, {"time": 7.3, "color": "ffffff00"}]}, "LT3": {"rgba": [{"time": 2, "color": "ffffff00", "curve": [2.078, 1, 2.156, 1, 2.078, 1, 2.156, 1, 2.078, 1, 2.156, 1, 2.078, 0, 2.156, 0.78]}, {"time": 2.2333, "color": "ffffffc8", "curve": [2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 0.78, 2.389, 0]}, {"time": 2.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 7, "color": "ffffff00", "curve": [7.078, 1, 7.156, 1, 7.078, 1, 7.156, 1, 7.078, 1, 7.156, 1, 7.078, 0, 7.156, 0.78]}, {"time": 7.2333, "color": "ffffffc8", "curve": [7.311, 1, 7.389, 1, 7.311, 1, 7.389, 1, 7.311, 1, 7.389, 1, 7.311, 0.78, 7.389, 0]}, {"time": 7.4667, "color": "ffffff00"}]}}, "bones": {"tree_Fb2": {"rotate": [{"value": 2.48, "curve": [0.444, 2.48, 0.889, -1.65]}, {"time": 1.3333, "value": -1.65, "curve": [1.778, -1.65, 2.222, 2.48]}, {"time": 2.6667, "value": 2.48, "curve": [3.111, 2.48, 3.556, -1.65]}, {"time": 4, "value": -1.65, "curve": [4.444, -1.65, 4.889, 2.48]}, {"time": 5.3333, "value": 2.48, "curve": [5.778, 2.48, 6.222, -1.65]}, {"time": 6.6667, "value": -1.65, "curve": [7.111, -1.65, 7.556, 2.48]}, {"time": 8, "value": 2.48, "curve": [8.444, 2.48, 8.889, -1.65]}, {"time": 9.3333, "value": -1.65, "curve": [9.778, -1.65, 10.222, 2.48]}, {"time": 10.6667, "value": 2.48}]}, "tree_Fb3": {"rotate": [{"value": 1.82, "curve": [0.114, 2.2, 0.224, 2.48]}, {"time": 0.3333, "value": 2.48, "curve": [0.778, 2.48, 1.222, -1.65]}, {"time": 1.6667, "value": -1.65, "curve": [2.111, -1.65, 2.556, 2.48]}, {"time": 3, "value": 2.48, "curve": [3.444, 2.48, 3.889, -1.65]}, {"time": 4.3333, "value": -1.65, "curve": [4.778, -1.65, 5.222, 2.48]}, {"time": 5.6667, "value": 2.48, "curve": [6.111, 2.48, 6.556, -1.65]}, {"time": 7, "value": -1.65, "curve": [7.444, -1.65, 7.889, 2.48]}, {"time": 8.3333, "value": 2.48, "curve": [8.778, 2.48, 9.222, -1.65]}, {"time": 9.6667, "value": -1.65, "curve": [10.001, -1.65, 10.336, 0.66]}, {"time": 10.6667, "value": 1.82}]}, "tree_Fb4": {"rotate": [{"value": 0.41, "curve": [0.225, 1.44, 0.446, 2.48]}, {"time": 0.6667, "value": 2.48, "curve": [1.111, 2.48, 1.556, -1.65]}, {"time": 2, "value": -1.65, "curve": [2.444, -1.65, 2.889, 2.48]}, {"time": 3.3333, "value": 2.48, "curve": [3.778, 2.48, 4.222, -1.65]}, {"time": 4.6667, "value": -1.65, "curve": [5.111, -1.65, 5.556, 2.48]}, {"time": 6, "value": 2.48, "curve": [6.444, 2.48, 6.889, -1.65]}, {"time": 7.3333, "value": -1.65, "curve": [7.778, -1.65, 8.222, 2.48]}, {"time": 8.6667, "value": 2.48, "curve": [9.111, 2.48, 9.556, -1.65]}, {"time": 10, "value": -1.65, "curve": [10.224, -1.65, 10.447, -0.63]}, {"time": 10.6667, "value": 0.41}]}, "tree_Fa2": {"rotate": [{"value": -0.99, "curve": [0.336, 0.17, 0.668, 2.48]}, {"time": 1, "value": 2.48, "curve": [1.444, 2.48, 1.889, -1.65]}, {"time": 2.3333, "value": -1.65, "curve": [2.778, -1.65, 3.222, 2.48]}, {"time": 3.6667, "value": 2.48, "curve": [4.111, 2.48, 4.556, -1.65]}, {"time": 5, "value": -1.65, "curve": [5.444, -1.65, 5.889, 2.48]}, {"time": 6.3333, "value": 2.48, "curve": [6.778, 2.48, 7.222, -1.65]}, {"time": 7.6667, "value": -1.65, "curve": [8.111, -1.65, 8.556, 2.48]}, {"time": 9, "value": 2.48, "curve": [9.444, 2.48, 9.889, -1.65]}, {"time": 10.3333, "value": -1.65, "curve": [10.446, -1.65, 10.559, -1.39]}, {"time": 10.6667, "value": -0.99}]}, "tree_Fa3": {"rotate": [{"value": -1.65, "curve": [0.444, -1.65, 0.889, 2.48]}, {"time": 1.3333, "value": 2.48, "curve": [1.778, 2.48, 2.222, -1.65]}, {"time": 2.6667, "value": -1.65, "curve": [3.111, -1.65, 3.556, 2.48]}, {"time": 4, "value": 2.48, "curve": [4.444, 2.48, 4.889, -1.65]}, {"time": 5.3333, "value": -1.65, "curve": [5.778, -1.65, 6.222, 2.48]}, {"time": 6.6667, "value": 2.48, "curve": [7.111, 2.48, 7.556, -1.65]}, {"time": 8, "value": -1.65, "curve": [8.444, -1.65, 8.889, 2.48]}, {"time": 9.3333, "value": 2.48, "curve": [9.778, 2.48, 10.222, -1.65]}, {"time": 10.6667, "value": -1.65}]}, "tree_Fa4": {"rotate": [{"value": -0.99, "curve": [0.114, -1.37, 0.224, -1.65]}, {"time": 0.3333, "value": -1.65, "curve": [0.778, -1.65, 1.222, 2.48]}, {"time": 1.6667, "value": 2.48, "curve": [2.111, 2.48, 2.556, -1.65]}, {"time": 3, "value": -1.65, "curve": [3.444, -1.65, 3.889, 2.48]}, {"time": 4.3333, "value": 2.48, "curve": [4.778, 2.48, 5.222, -1.65]}, {"time": 5.6667, "value": -1.65, "curve": [6.111, -1.65, 6.556, 2.48]}, {"time": 7, "value": 2.48, "curve": [7.444, 2.48, 7.889, -1.65]}, {"time": 8.3333, "value": -1.65, "curve": [8.778, -1.65, 9.222, 2.48]}, {"time": 9.6667, "value": 2.48, "curve": [10.001, 2.48, 10.336, 0.16]}, {"time": 10.6667, "value": -0.99}]}, "tree_Fc2": {"rotate": [{"value": -0.99, "curve": [0.114, -1.37, 0.224, -1.65]}, {"time": 0.3333, "value": -1.65, "curve": [0.778, -1.65, 1.222, 2.48]}, {"time": 1.6667, "value": 2.48, "curve": [2.111, 2.48, 2.556, -1.65]}, {"time": 3, "value": -1.65, "curve": [3.444, -1.65, 3.889, 2.48]}, {"time": 4.3333, "value": 2.48, "curve": [4.778, 2.48, 5.222, -1.65]}, {"time": 5.6667, "value": -1.65, "curve": [6.111, -1.65, 6.556, 2.48]}, {"time": 7, "value": 2.48, "curve": [7.444, 2.48, 7.889, -1.65]}, {"time": 8.3333, "value": -1.65, "curve": [8.778, -1.65, 9.222, 2.48]}, {"time": 9.6667, "value": 2.48, "curve": [10.001, 2.48, 10.336, 0.16]}, {"time": 10.6667, "value": -0.99}]}, "tree_Fc3": {"rotate": [{"value": 0.41, "curve": [0.225, -0.61, 0.446, -1.65]}, {"time": 0.6667, "value": -1.65, "curve": [1.111, -1.65, 1.556, 2.48]}, {"time": 2, "value": 2.48, "curve": [2.444, 2.48, 2.889, -1.65]}, {"time": 3.3333, "value": -1.65, "curve": [3.778, -1.65, 4.222, 2.48]}, {"time": 4.6667, "value": 2.48, "curve": [5.111, 2.48, 5.556, -1.65]}, {"time": 6, "value": -1.65, "curve": [6.444, -1.65, 6.889, 2.48]}, {"time": 7.3333, "value": 2.48, "curve": [7.778, 2.48, 8.222, -1.65]}, {"time": 8.6667, "value": -1.65, "curve": [9.111, -1.65, 9.556, 2.48]}, {"time": 10, "value": 2.48, "curve": [10.224, 2.48, 10.447, 1.45]}, {"time": 10.6667, "value": 0.41}]}, "tree_Fc4": {"rotate": [{"value": 1.82, "curve": [0.336, 0.65, 0.668, -1.65]}, {"time": 1, "value": -1.65, "curve": [1.444, -1.65, 1.889, 2.48]}, {"time": 2.3333, "value": 2.48, "curve": [2.778, 2.48, 3.222, -1.65]}, {"time": 3.6667, "value": -1.65, "curve": [4.111, -1.65, 4.556, 2.48]}, {"time": 5, "value": 2.48, "curve": [5.444, 2.48, 5.889, -1.65]}, {"time": 6.3333, "value": -1.65, "curve": [6.778, -1.65, 7.222, 2.48]}, {"time": 7.6667, "value": 2.48, "curve": [8.111, 2.48, 8.556, -1.65]}, {"time": 9, "value": -1.65, "curve": [9.444, -1.65, 9.889, 2.48]}, {"time": 10.3333, "value": 2.48, "curve": [10.446, 2.48, 10.559, 2.21]}, {"time": 10.6667, "value": 1.82}]}, "tree_Ra2": {"rotate": [{"value": 1.82, "curve": [0.114, 2.2, 0.224, 2.48]}, {"time": 0.3333, "value": 2.48, "curve": [0.778, 2.48, 1.222, -1.65]}, {"time": 1.6667, "value": -1.65, "curve": [2.111, -1.65, 2.556, 2.48]}, {"time": 3, "value": 2.48, "curve": [3.444, 2.48, 3.889, -1.65]}, {"time": 4.3333, "value": -1.65, "curve": [4.778, -1.65, 5.222, 2.48]}, {"time": 5.6667, "value": 2.48, "curve": [6.111, 2.48, 6.556, -1.65]}, {"time": 7, "value": -1.65, "curve": [7.444, -1.65, 7.889, 2.48]}, {"time": 8.3333, "value": 2.48, "curve": [8.778, 2.48, 9.222, -1.65]}, {"time": 9.6667, "value": -1.65, "curve": [10.001, -1.65, 10.336, 0.66]}, {"time": 10.6667, "value": 1.82}]}, "tree_Ra3": {"rotate": [{"value": 0.41, "curve": [0.225, 1.44, 0.446, 2.48]}, {"time": 0.6667, "value": 2.48, "curve": [1.111, 2.48, 1.556, -1.65]}, {"time": 2, "value": -1.65, "curve": [2.444, -1.65, 2.889, 2.48]}, {"time": 3.3333, "value": 2.48, "curve": [3.778, 2.48, 4.222, -1.65]}, {"time": 4.6667, "value": -1.65, "curve": [5.111, -1.65, 5.556, 2.48]}, {"time": 6, "value": 2.48, "curve": [6.444, 2.48, 6.889, -1.65]}, {"time": 7.3333, "value": -1.65, "curve": [7.778, -1.65, 8.222, 2.48]}, {"time": 8.6667, "value": 2.48, "curve": [9.111, 2.48, 9.556, -1.65]}, {"time": 10, "value": -1.65, "curve": [10.224, -1.65, 10.447, -0.63]}, {"time": 10.6667, "value": 0.41}]}, "tree_Ra4": {"rotate": [{"value": -0.99, "curve": [0.336, 0.17, 0.668, 2.48]}, {"time": 1, "value": 2.48, "curve": [1.444, 2.48, 1.889, -1.65]}, {"time": 2.3333, "value": -1.65, "curve": [2.778, -1.65, 3.222, 2.48]}, {"time": 3.6667, "value": 2.48, "curve": [4.111, 2.48, 4.556, -1.65]}, {"time": 5, "value": -1.65, "curve": [5.444, -1.65, 5.889, 2.48]}, {"time": 6.3333, "value": 2.48, "curve": [6.778, 2.48, 7.222, -1.65]}, {"time": 7.6667, "value": -1.65, "curve": [8.111, -1.65, 8.556, 2.48]}, {"time": 9, "value": 2.48, "curve": [9.444, 2.48, 9.889, -1.65]}, {"time": 10.3333, "value": -1.65, "curve": [10.446, -1.65, 10.559, -1.39]}, {"time": 10.6667, "value": -0.99}]}, "tree_Rb2": {"rotate": [{"value": 0.41, "curve": [0.225, -0.61, 0.446, -1.65]}, {"time": 0.6667, "value": -1.65, "curve": [1.111, -1.65, 1.556, 2.48]}, {"time": 2, "value": 2.48, "curve": [2.444, 2.48, 2.889, -1.65]}, {"time": 3.3333, "value": -1.65, "curve": [3.778, -1.65, 4.222, 2.48]}, {"time": 4.6667, "value": 2.48, "curve": [5.111, 2.48, 5.556, -1.65]}, {"time": 6, "value": -1.65, "curve": [6.444, -1.65, 6.889, 2.48]}, {"time": 7.3333, "value": 2.48, "curve": [7.778, 2.48, 8.222, -1.65]}, {"time": 8.6667, "value": -1.65, "curve": [9.111, -1.65, 9.556, 2.48]}, {"time": 10, "value": 2.48, "curve": [10.224, 2.48, 10.447, 1.45]}, {"time": 10.6667, "value": 0.41}]}, "tree_Rb3": {"rotate": [{"value": 1.82, "curve": [0.336, 0.65, 0.668, -1.65]}, {"time": 1, "value": -1.65, "curve": [1.444, -1.65, 1.889, 2.48]}, {"time": 2.3333, "value": 2.48, "curve": [2.778, 2.48, 3.222, -1.65]}, {"time": 3.6667, "value": -1.65, "curve": [4.111, -1.65, 4.556, 2.48]}, {"time": 5, "value": 2.48, "curve": [5.444, 2.48, 5.889, -1.65]}, {"time": 6.3333, "value": -1.65, "curve": [6.778, -1.65, 7.222, 2.48]}, {"time": 7.6667, "value": 2.48, "curve": [8.111, 2.48, 8.556, -1.65]}, {"time": 9, "value": -1.65, "curve": [9.444, -1.65, 9.889, 2.48]}, {"time": 10.3333, "value": 2.48, "curve": [10.446, 2.48, 10.559, 2.21]}, {"time": 10.6667, "value": 1.82}]}, "tree_Rb4": {"rotate": [{"value": 2.48, "curve": [0.444, 2.48, 0.889, -1.65]}, {"time": 1.3333, "value": -1.65, "curve": [1.778, -1.65, 2.222, 2.48]}, {"time": 2.6667, "value": 2.48, "curve": [3.111, 2.48, 3.556, -1.65]}, {"time": 4, "value": -1.65, "curve": [4.444, -1.65, 4.889, 2.48]}, {"time": 5.3333, "value": 2.48, "curve": [5.778, 2.48, 6.222, -1.65]}, {"time": 6.6667, "value": -1.65, "curve": [7.111, -1.65, 7.556, 2.48]}, {"time": 8, "value": 2.48, "curve": [8.444, 2.48, 8.889, -1.65]}, {"time": 9.3333, "value": -1.65, "curve": [9.778, -1.65, 10.222, 2.48]}, {"time": 10.6667, "value": 2.48}]}, "tree_Rc2": {"rotate": [{"value": 1.16, "curve": [0.168, 1.89, 0.334, 2.48]}, {"time": 0.5, "value": 2.48, "curve": [0.944, 2.48, 1.389, -1.65]}, {"time": 1.8333, "value": -1.65, "curve": [2.278, -1.65, 2.722, 2.48]}, {"time": 3.1667, "value": 2.48, "curve": [3.611, 2.48, 4.056, -1.65]}, {"time": 4.5, "value": -1.65, "curve": [4.944, -1.65, 5.389, 2.48]}, {"time": 5.8333, "value": 2.48, "curve": [6.278, 2.48, 6.722, -1.65]}, {"time": 7.1667, "value": -1.65, "curve": [7.611, -1.65, 8.056, 2.48]}, {"time": 8.5, "value": 2.48, "curve": [8.944, 2.48, 9.389, -1.65]}, {"time": 9.8333, "value": -1.65, "curve": [10.112, -1.65, 10.39, -0.05]}, {"time": 10.6667, "value": 1.16}]}, "tree_Rc3": {"rotate": [{"value": -0.34, "curve": [0.279, 0.87, 0.556, 2.48]}, {"time": 0.8333, "value": 2.48, "curve": [1.278, 2.48, 1.722, -1.65]}, {"time": 2.1667, "value": -1.65, "curve": [2.611, -1.65, 3.056, 2.48]}, {"time": 3.5, "value": 2.48, "curve": [3.944, 2.48, 4.389, -1.65]}, {"time": 4.8333, "value": -1.65, "curve": [5.278, -1.65, 5.722, 2.48]}, {"time": 6.1667, "value": 2.48, "curve": [6.611, 2.48, 7.056, -1.65]}, {"time": 7.5, "value": -1.65, "curve": [7.944, -1.65, 8.389, 2.48]}, {"time": 8.8333, "value": 2.48, "curve": [9.278, 2.48, 9.722, -1.65]}, {"time": 10.1667, "value": -1.65, "curve": [10.334, -1.65, 10.501, -1.07]}, {"time": 10.6667, "value": -0.34}]}, "tree_Rc4": {"rotate": [{"value": -1.46, "curve": [0.39, -0.66, 0.779, 2.48]}, {"time": 1.1667, "value": 2.48, "curve": [1.611, 2.48, 2.056, -1.65]}, {"time": 2.5, "value": -1.65, "curve": [2.944, -1.65, 3.389, 2.48]}, {"time": 3.8333, "value": 2.48, "curve": [4.278, 2.48, 4.722, -1.65]}, {"time": 5.1667, "value": -1.65, "curve": [5.611, -1.65, 6.056, 2.48]}, {"time": 6.5, "value": 2.48, "curve": [6.944, 2.48, 7.389, -1.65]}, {"time": 7.8333, "value": -1.65, "curve": [8.278, -1.65, 8.722, 2.48]}, {"time": 9.1667, "value": 2.48, "curve": [9.611, 2.48, 10.056, -1.65]}, {"time": 10.5, "value": -1.65, "curve": [10.556, -1.65, 10.613, -1.57]}, {"time": 10.6667, "value": -1.46}]}, "tree_Rd2": {"rotate": [{"value": -1.65, "curve": [0.444, -1.65, 0.889, 2.48]}, {"time": 1.3333, "value": 2.48, "curve": [1.778, 2.48, 2.222, -1.65]}, {"time": 2.6667, "value": -1.65, "curve": [3.111, -1.65, 3.556, 2.48]}, {"time": 4, "value": 2.48, "curve": [4.444, 2.48, 4.889, -1.65]}, {"time": 5.3333, "value": -1.65, "curve": [5.778, -1.65, 6.222, 2.48]}, {"time": 6.6667, "value": 2.48, "curve": [7.111, 2.48, 7.556, -1.65]}, {"time": 8, "value": -1.65, "curve": [8.444, -1.65, 8.889, 2.48]}, {"time": 9.3333, "value": 2.48, "curve": [9.778, 2.48, 10.222, -1.65]}, {"time": 10.6667, "value": -1.65}]}, "tree_Rd3": {"rotate": [{"value": -0.99, "curve": [0.114, -1.37, 0.224, -1.65]}, {"time": 0.3333, "value": -1.65, "curve": [0.778, -1.65, 1.222, 2.48]}, {"time": 1.6667, "value": 2.48, "curve": [2.111, 2.48, 2.556, -1.65]}, {"time": 3, "value": -1.65, "curve": [3.444, -1.65, 3.889, 2.48]}, {"time": 4.3333, "value": 2.48, "curve": [4.778, 2.48, 5.222, -1.65]}, {"time": 5.6667, "value": -1.65, "curve": [6.111, -1.65, 6.556, 2.48]}, {"time": 7, "value": 2.48, "curve": [7.444, 2.48, 7.889, -1.65]}, {"time": 8.3333, "value": -1.65, "curve": [8.778, -1.65, 9.222, 2.48]}, {"time": 9.6667, "value": 2.48, "curve": [10.001, 2.48, 10.336, 0.16]}, {"time": 10.6667, "value": -0.99}]}, "tree_Rd4": {"rotate": [{"value": 0.41, "curve": [0.225, -0.61, 0.446, -1.65]}, {"time": 0.6667, "value": -1.65, "curve": [1.111, -1.65, 1.556, 2.48]}, {"time": 2, "value": 2.48, "curve": [2.444, 2.48, 2.889, -1.65]}, {"time": 3.3333, "value": -1.65, "curve": [3.778, -1.65, 4.222, 2.48]}, {"time": 4.6667, "value": 2.48, "curve": [5.111, 2.48, 5.556, -1.65]}, {"time": 6, "value": -1.65, "curve": [6.444, -1.65, 6.889, 2.48]}, {"time": 7.3333, "value": 2.48, "curve": [7.778, 2.48, 8.222, -1.65]}, {"time": 8.6667, "value": -1.65, "curve": [9.111, -1.65, 9.556, 2.48]}, {"time": 10, "value": 2.48, "curve": [10.224, 2.48, 10.447, 1.45]}, {"time": 10.6667, "value": 0.41}]}, "tree_Re2": {"rotate": [{"value": -0.99, "curve": [0.336, 0.17, 0.668, 2.48]}, {"time": 1, "value": 2.48, "curve": [1.444, 2.48, 1.889, -1.65]}, {"time": 2.3333, "value": -1.65, "curve": [2.778, -1.65, 3.222, 2.48]}, {"time": 3.6667, "value": 2.48, "curve": [4.111, 2.48, 4.556, -1.65]}, {"time": 5, "value": -1.65, "curve": [5.444, -1.65, 5.889, 2.48]}, {"time": 6.3333, "value": 2.48, "curve": [6.778, 2.48, 7.222, -1.65]}, {"time": 7.6667, "value": -1.65, "curve": [8.111, -1.65, 8.556, 2.48]}, {"time": 9, "value": 2.48, "curve": [9.444, 2.48, 9.889, -1.65]}, {"time": 10.3333, "value": -1.65, "curve": [10.446, -1.65, 10.559, -1.39]}, {"time": 10.6667, "value": -0.99}]}, "tree_Re3": {"rotate": [{"value": -1.65, "curve": [0.444, -1.65, 0.889, 2.48]}, {"time": 1.3333, "value": 2.48, "curve": [1.778, 2.48, 2.222, -1.65]}, {"time": 2.6667, "value": -1.65, "curve": [3.111, -1.65, 3.556, 2.48]}, {"time": 4, "value": 2.48, "curve": [4.444, 2.48, 4.889, -1.65]}, {"time": 5.3333, "value": -1.65, "curve": [5.778, -1.65, 6.222, 2.48]}, {"time": 6.6667, "value": 2.48, "curve": [7.111, 2.48, 7.556, -1.65]}, {"time": 8, "value": -1.65, "curve": [8.444, -1.65, 8.889, 2.48]}, {"time": 9.3333, "value": 2.48, "curve": [9.778, 2.48, 10.222, -1.65]}, {"time": 10.6667, "value": -1.65}]}, "tree_Re4": {"rotate": [{"value": -0.99, "curve": [0.114, -1.37, 0.224, -1.65]}, {"time": 0.3333, "value": -1.65, "curve": [0.778, -1.65, 1.222, 2.48]}, {"time": 1.6667, "value": 2.48, "curve": [2.111, 2.48, 2.556, -1.65]}, {"time": 3, "value": -1.65, "curve": [3.444, -1.65, 3.889, 2.48]}, {"time": 4.3333, "value": 2.48, "curve": [4.778, 2.48, 5.222, -1.65]}, {"time": 5.6667, "value": -1.65, "curve": [6.111, -1.65, 6.556, 2.48]}, {"time": 7, "value": 2.48, "curve": [7.444, 2.48, 7.889, -1.65]}, {"time": 8.3333, "value": -1.65, "curve": [8.778, -1.65, 9.222, 2.48]}, {"time": 9.6667, "value": 2.48, "curve": [10.001, 2.48, 10.336, 0.16]}, {"time": 10.6667, "value": -0.99}]}, "tree_Re5": {"rotate": [{"value": 0.41, "curve": [0.225, -0.61, 0.446, -1.65]}, {"time": 0.6667, "value": -1.65, "curve": [1.111, -1.65, 1.556, 2.48]}, {"time": 2, "value": 2.48, "curve": [2.444, 2.48, 2.889, -1.65]}, {"time": 3.3333, "value": -1.65, "curve": [3.778, -1.65, 4.222, 2.48]}, {"time": 4.6667, "value": 2.48, "curve": [5.111, 2.48, 5.556, -1.65]}, {"time": 6, "value": -1.65, "curve": [6.444, -1.65, 6.889, 2.48]}, {"time": 7.3333, "value": 2.48, "curve": [7.778, 2.48, 8.222, -1.65]}, {"time": 8.6667, "value": -1.65, "curve": [9.111, -1.65, 9.556, 2.48]}, {"time": 10, "value": 2.48, "curve": [10.224, 2.48, 10.447, 1.45]}, {"time": 10.6667, "value": 0.41}]}, "tree_Rf2": {"rotate": [{"value": 2.28, "curve": [0.057, 2.39, 0.112, 2.48]}, {"time": 0.1667, "value": 2.48, "curve": [0.611, 2.48, 1.056, -1.65]}, {"time": 1.5, "value": -1.65, "curve": [1.944, -1.65, 2.389, 2.48]}, {"time": 2.8333, "value": 2.48, "curve": [3.278, 2.48, 3.722, -1.65]}, {"time": 4.1667, "value": -1.65, "curve": [4.611, -1.65, 5.056, 2.48]}, {"time": 5.5, "value": 2.48, "curve": [5.944, 2.48, 6.389, -1.65]}, {"time": 6.8333, "value": -1.65, "curve": [7.278, -1.65, 7.722, 2.48]}, {"time": 8.1667, "value": 2.48, "curve": [8.611, 2.48, 9.056, -1.65]}, {"time": 9.5, "value": -1.65, "curve": [9.89, -1.65, 10.279, 1.5]}, {"time": 10.6667, "value": 2.28}]}, "tree_Rf3": {"rotate": [{"value": 1.16, "curve": [0.168, 1.89, 0.334, 2.48]}, {"time": 0.5, "value": 2.48, "curve": [0.944, 2.48, 1.389, -1.65]}, {"time": 1.8333, "value": -1.65, "curve": [2.278, -1.65, 2.722, 2.48]}, {"time": 3.1667, "value": 2.48, "curve": [3.611, 2.48, 4.056, -1.65]}, {"time": 4.5, "value": -1.65, "curve": [4.944, -1.65, 5.389, 2.48]}, {"time": 5.8333, "value": 2.48, "curve": [6.278, 2.48, 6.722, -1.65]}, {"time": 7.1667, "value": -1.65, "curve": [7.611, -1.65, 8.056, 2.48]}, {"time": 8.5, "value": 2.48, "curve": [8.944, 2.48, 9.389, -1.65]}, {"time": 9.8333, "value": -1.65, "curve": [10.112, -1.65, 10.39, -0.05]}, {"time": 10.6667, "value": 1.16}]}, "tree_Rf4": {"rotate": [{"value": -0.34, "curve": [0.279, 0.87, 0.556, 2.48]}, {"time": 0.8333, "value": 2.48, "curve": [1.278, 2.48, 1.722, -1.65]}, {"time": 2.1667, "value": -1.65, "curve": [2.611, -1.65, 3.056, 2.48]}, {"time": 3.5, "value": 2.48, "curve": [3.944, 2.48, 4.389, -1.65]}, {"time": 4.8333, "value": -1.65, "curve": [5.278, -1.65, 5.722, 2.48]}, {"time": 6.1667, "value": 2.48, "curve": [6.611, 2.48, 7.056, -1.65]}, {"time": 7.5, "value": -1.65, "curve": [7.944, -1.65, 8.389, 2.48]}, {"time": 8.8333, "value": 2.48, "curve": [9.278, 2.48, 9.722, -1.65]}, {"time": 10.1667, "value": -1.65, "curve": [10.334, -1.65, 10.501, -1.07]}, {"time": 10.6667, "value": -0.34}]}, "tree_La2": {"rotate": [{"value": -1.46, "curve": [0.057, -1.57, 0.112, -1.65]}, {"time": 0.1667, "value": -1.65, "curve": [0.611, -1.65, 1.056, 2.48]}, {"time": 1.5, "value": 2.48, "curve": [1.944, 2.48, 2.389, -1.65]}, {"time": 2.8333, "value": -1.65, "curve": [3.278, -1.65, 3.722, 2.48]}, {"time": 4.1667, "value": 2.48, "curve": [4.611, 2.48, 5.056, -1.65]}, {"time": 5.5, "value": -1.65, "curve": [5.944, -1.65, 6.389, 2.48]}, {"time": 6.8333, "value": 2.48, "curve": [7.278, 2.48, 7.722, -1.65]}, {"time": 8.1667, "value": -1.65, "curve": [8.611, -1.65, 9.056, 2.48]}, {"time": 9.5, "value": 2.48, "curve": [9.89, 2.48, 10.279, -0.68]}, {"time": 10.6667, "value": -1.46}]}, "tree_La3": {"rotate": [{"value": -0.34, "curve": [0.168, -1.06, 0.334, -1.65]}, {"time": 0.5, "value": -1.65, "curve": [0.944, -1.65, 1.389, 2.48]}, {"time": 1.8333, "value": 2.48, "curve": [2.278, 2.48, 2.722, -1.65]}, {"time": 3.1667, "value": -1.65, "curve": [3.611, -1.65, 4.056, 2.48]}, {"time": 4.5, "value": 2.48, "curve": [4.944, 2.48, 5.389, -1.65]}, {"time": 5.8333, "value": -1.65, "curve": [6.278, -1.65, 6.722, 2.48]}, {"time": 7.1667, "value": 2.48, "curve": [7.611, 2.48, 8.056, -1.65]}, {"time": 8.5, "value": -1.65, "curve": [8.944, -1.65, 9.389, 2.48]}, {"time": 9.8333, "value": 2.48, "curve": [10.112, 2.48, 10.39, 0.87]}, {"time": 10.6667, "value": -0.34}]}, "tree_La4": {"rotate": [{"value": 1.16, "curve": [0.279, -0.05, 0.556, -1.65]}, {"time": 0.8333, "value": -1.65, "curve": [1.278, -1.65, 1.722, 2.48]}, {"time": 2.1667, "value": 2.48, "curve": [2.611, 2.48, 3.056, -1.65]}, {"time": 3.5, "value": -1.65, "curve": [3.944, -1.65, 4.389, 2.48]}, {"time": 4.8333, "value": 2.48, "curve": [5.278, 2.48, 5.722, -1.65]}, {"time": 6.1667, "value": -1.65, "curve": [6.611, -1.65, 7.056, 2.48]}, {"time": 7.5, "value": 2.48, "curve": [7.944, 2.48, 8.389, -1.65]}, {"time": 8.8333, "value": -1.65, "curve": [9.278, -1.65, 9.722, 2.48]}, {"time": 10.1667, "value": 2.48, "curve": [10.334, 2.48, 10.501, 1.89]}, {"time": 10.6667, "value": 1.16}]}, "tree_Lb2": {"rotate": [{"value": 0.41, "curve": [0.225, -0.61, 0.446, -1.65]}, {"time": 0.6667, "value": -1.65, "curve": [1.111, -1.65, 1.556, 2.48]}, {"time": 2, "value": 2.48, "curve": [2.444, 2.48, 2.889, -1.65]}, {"time": 3.3333, "value": -1.65, "curve": [3.778, -1.65, 4.222, 2.48]}, {"time": 4.6667, "value": 2.48, "curve": [5.111, 2.48, 5.556, -1.65]}, {"time": 6, "value": -1.65, "curve": [6.444, -1.65, 6.889, 2.48]}, {"time": 7.3333, "value": 2.48, "curve": [7.778, 2.48, 8.222, -1.65]}, {"time": 8.6667, "value": -1.65, "curve": [9.111, -1.65, 9.556, 2.48]}, {"time": 10, "value": 2.48, "curve": [10.224, 2.48, 10.447, 1.45]}, {"time": 10.6667, "value": 0.41}]}, "tree_Lb3": {"rotate": [{"value": 1.82, "curve": [0.336, 0.65, 0.668, -1.65]}, {"time": 1, "value": -1.65, "curve": [1.444, -1.65, 1.889, 2.48]}, {"time": 2.3333, "value": 2.48, "curve": [2.778, 2.48, 3.222, -1.65]}, {"time": 3.6667, "value": -1.65, "curve": [4.111, -1.65, 4.556, 2.48]}, {"time": 5, "value": 2.48, "curve": [5.444, 2.48, 5.889, -1.65]}, {"time": 6.3333, "value": -1.65, "curve": [6.778, -1.65, 7.222, 2.48]}, {"time": 7.6667, "value": 2.48, "curve": [8.111, 2.48, 8.556, -1.65]}, {"time": 9, "value": -1.65, "curve": [9.444, -1.65, 9.889, 2.48]}, {"time": 10.3333, "value": 2.48, "curve": [10.446, 2.48, 10.559, 2.21]}, {"time": 10.6667, "value": 1.82}]}, "tree_Lb4": {"rotate": [{"value": 2.48, "curve": [0.444, 2.48, 0.889, -1.65]}, {"time": 1.3333, "value": -1.65, "curve": [1.778, -1.65, 2.222, 2.48]}, {"time": 2.6667, "value": 2.48, "curve": [3.111, 2.48, 3.556, -1.65]}, {"time": 4, "value": -1.65, "curve": [4.444, -1.65, 4.889, 2.48]}, {"time": 5.3333, "value": 2.48, "curve": [5.778, 2.48, 6.222, -1.65]}, {"time": 6.6667, "value": -1.65, "curve": [7.111, -1.65, 7.556, 2.48]}, {"time": 8, "value": 2.48, "curve": [8.444, 2.48, 8.889, -1.65]}, {"time": 9.3333, "value": -1.65, "curve": [9.778, -1.65, 10.222, 2.48]}, {"time": 10.6667, "value": 2.48}]}, "tree_Lc2": {"rotate": [{"value": -0.34, "curve": [0.279, 0.87, 0.556, 2.48]}, {"time": 0.8333, "value": 2.48, "curve": [1.278, 2.48, 1.722, -1.65]}, {"time": 2.1667, "value": -1.65, "curve": [2.611, -1.65, 3.056, 2.48]}, {"time": 3.5, "value": 2.48, "curve": [3.944, 2.48, 4.389, -1.65]}, {"time": 4.8333, "value": -1.65, "curve": [5.278, -1.65, 5.722, 2.48]}, {"time": 6.1667, "value": 2.48, "curve": [6.611, 2.48, 7.056, -1.65]}, {"time": 7.5, "value": -1.65, "curve": [7.944, -1.65, 8.389, 2.48]}, {"time": 8.8333, "value": 2.48, "curve": [9.278, 2.48, 9.722, -1.65]}, {"time": 10.1667, "value": -1.65, "curve": [10.334, -1.65, 10.501, -1.07]}, {"time": 10.6667, "value": -0.34}]}, "tree_Lc3": {"rotate": [{"value": -1.46, "curve": [0.39, -0.66, 0.779, 2.48]}, {"time": 1.1667, "value": 2.48, "curve": [1.611, 2.48, 2.056, -1.65]}, {"time": 2.5, "value": -1.65, "curve": [2.944, -1.65, 3.389, 2.48]}, {"time": 3.8333, "value": 2.48, "curve": [4.278, 2.48, 4.722, -1.65]}, {"time": 5.1667, "value": -1.65, "curve": [5.611, -1.65, 6.056, 2.48]}, {"time": 6.5, "value": 2.48, "curve": [6.944, 2.48, 7.389, -1.65]}, {"time": 7.8333, "value": -1.65, "curve": [8.278, -1.65, 8.722, 2.48]}, {"time": 9.1667, "value": 2.48, "curve": [9.611, 2.48, 10.056, -1.65]}, {"time": 10.5, "value": -1.65, "curve": [10.556, -1.65, 10.613, -1.57]}, {"time": 10.6667, "value": -1.46}]}, "tree_Lc4": {"rotate": [{"value": -1.46, "curve": [0.057, -1.57, 0.112, -1.65]}, {"time": 0.1667, "value": -1.65, "curve": [0.611, -1.65, 1.056, 2.48]}, {"time": 1.5, "value": 2.48, "curve": [1.944, 2.48, 2.389, -1.65]}, {"time": 2.8333, "value": -1.65, "curve": [3.278, -1.65, 3.722, 2.48]}, {"time": 4.1667, "value": 2.48, "curve": [4.611, 2.48, 5.056, -1.65]}, {"time": 5.5, "value": -1.65, "curve": [5.944, -1.65, 6.389, 2.48]}, {"time": 6.8333, "value": 2.48, "curve": [7.278, 2.48, 7.722, -1.65]}, {"time": 8.1667, "value": -1.65, "curve": [8.611, -1.65, 9.056, 2.48]}, {"time": 9.5, "value": 2.48, "curve": [9.89, 2.48, 10.279, -0.68]}, {"time": 10.6667, "value": -1.46}]}, "tree_Ld2": {"rotate": [{"value": 1.82, "curve": [0.336, 0.65, 0.668, -1.65]}, {"time": 1, "value": -1.65, "curve": [1.444, -1.65, 1.889, 2.48]}, {"time": 2.3333, "value": 2.48, "curve": [2.778, 2.48, 3.222, -1.65]}, {"time": 3.6667, "value": -1.65, "curve": [4.111, -1.65, 4.556, 2.48]}, {"time": 5, "value": 2.48, "curve": [5.444, 2.48, 5.889, -1.65]}, {"time": 6.3333, "value": -1.65, "curve": [6.778, -1.65, 7.222, 2.48]}, {"time": 7.6667, "value": 2.48, "curve": [8.111, 2.48, 8.556, -1.65]}, {"time": 9, "value": -1.65, "curve": [9.444, -1.65, 9.889, 2.48]}, {"time": 10.3333, "value": 2.48, "curve": [10.446, 2.48, 10.559, 2.21]}, {"time": 10.6667, "value": 1.82}]}, "tree_Ld3": {"rotate": [{"value": 2.48, "curve": [0.444, 2.48, 0.889, -1.65]}, {"time": 1.3333, "value": -1.65, "curve": [1.778, -1.65, 2.222, 2.48]}, {"time": 2.6667, "value": 2.48, "curve": [3.111, 2.48, 3.556, -1.65]}, {"time": 4, "value": -1.65, "curve": [4.444, -1.65, 4.889, 2.48]}, {"time": 5.3333, "value": 2.48, "curve": [5.778, 2.48, 6.222, -1.65]}, {"time": 6.6667, "value": -1.65, "curve": [7.111, -1.65, 7.556, 2.48]}, {"time": 8, "value": 2.48, "curve": [8.444, 2.48, 8.889, -1.65]}, {"time": 9.3333, "value": -1.65, "curve": [9.778, -1.65, 10.222, 2.48]}, {"time": 10.6667, "value": 2.48}]}, "tree_Ld4": {"rotate": [{"value": 1.82, "curve": [0.114, 2.2, 0.224, 2.48]}, {"time": 0.3333, "value": 2.48, "curve": [0.778, 2.48, 1.222, -1.65]}, {"time": 1.6667, "value": -1.65, "curve": [2.111, -1.65, 2.556, 2.48]}, {"time": 3, "value": 2.48, "curve": [3.444, 2.48, 3.889, -1.65]}, {"time": 4.3333, "value": -1.65, "curve": [4.778, -1.65, 5.222, 2.48]}, {"time": 5.6667, "value": 2.48, "curve": [6.111, 2.48, 6.556, -1.65]}, {"time": 7, "value": -1.65, "curve": [7.444, -1.65, 7.889, 2.48]}, {"time": 8.3333, "value": 2.48, "curve": [8.778, 2.48, 9.222, -1.65]}, {"time": 9.6667, "value": -1.65, "curve": [10.001, -1.65, 10.336, 0.66]}, {"time": 10.6667, "value": 1.82}]}, "light": {"scale": [{"curve": [0.444, 1, 0.889, 1.443, 0.444, 1, 0.889, 1.443]}, {"time": 1.3333, "x": 1.443, "y": 1.443, "curve": [1.778, 1.443, 2.222, 1, 1.778, 1.443, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.443, 3.111, 1, 3.556, 1.443]}, {"time": 4, "x": 1.443, "y": 1.443, "curve": [4.444, 1.443, 4.889, 1, 4.444, 1.443, 4.889, 1]}, {"time": 5.3333, "curve": [5.778, 1, 6.222, 1.443, 5.778, 1, 6.222, 1.443]}, {"time": 6.6667, "x": 1.443, "y": 1.443, "curve": [7.111, 1.443, 7.556, 1, 7.111, 1.443, 7.556, 1]}, {"time": 8, "curve": [8.444, 1, 8.889, 1.443, 8.444, 1, 8.889, 1.443]}, {"time": 9.3333, "x": 1.443, "y": 1.443, "curve": [9.778, 1.443, 10.222, 1, 9.778, 1.443, 10.222, 1]}, {"time": 10.6667}]}, "light2": {"scale": [{"time": 1.6667, "curve": [2.111, 1, 2.556, 0.807, 2.111, 1, 2.556, 0.807]}, {"time": 3, "x": 0.807, "y": 0.807, "curve": [3.044, 0.807, 3.089, 1, 3.044, 0.807, 3.089, 1]}, {"time": 3.1333, "curve": [3.178, 1, 3.222, 0.807, 3.178, 1, 3.222, 0.807]}, {"time": 3.2667, "x": 0.807, "y": 0.807, "curve": "stepped"}, {"time": 3.6667, "x": 0.807, "y": 0.807, "curve": [3.711, 0.807, 3.756, 1, 3.711, 0.807, 3.756, 1]}, {"time": 3.8, "curve": [3.889, 1, 3.978, 0.807, 3.889, 1, 3.978, 0.807]}, {"time": 4.0667, "x": 0.807, "y": 0.807, "curve": [4.111, 0.807, 4.156, 1, 4.111, 0.807, 4.156, 1]}, {"time": 4.2, "curve": [4.3, 1, 4.4, 0.807, 4.3, 1, 4.4, 0.807]}, {"time": 4.5, "x": 0.807, "y": 0.807, "curve": [4.622, 0.807, 4.744, 1, 4.622, 0.807, 4.744, 1]}, {"time": 4.8667, "curve": "stepped"}, {"time": 6.8667, "curve": [7.311, 1, 7.756, 0.807, 7.311, 1, 7.756, 0.807]}, {"time": 8.2, "x": 0.807, "y": 0.807, "curve": [8.244, 0.807, 8.289, 1, 8.244, 0.807, 8.289, 1]}, {"time": 8.3333, "curve": [8.378, 1, 8.422, 0.807, 8.378, 1, 8.422, 0.807]}, {"time": 8.4667, "x": 0.807, "y": 0.807, "curve": "stepped"}, {"time": 8.8667, "x": 0.807, "y": 0.807, "curve": [8.911, 0.807, 8.956, 1, 8.911, 0.807, 8.956, 1]}, {"time": 9, "curve": [9.089, 1, 9.178, 0.807, 9.089, 1, 9.178, 0.807]}, {"time": 9.2667, "x": 0.807, "y": 0.807, "curve": [9.311, 0.807, 9.356, 1, 9.311, 0.807, 9.356, 1]}, {"time": 9.4, "curve": [9.5, 1, 9.6, 0.807, 9.5, 1, 9.6, 0.807]}, {"time": 9.7, "x": 0.807, "y": 0.807, "curve": [9.822, 0.807, 9.944, 1, 9.822, 0.807, 9.944, 1]}, {"time": 10.0667}]}, "light3": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -180]}, {"time": 3.1667, "value": -180, "curve": [4.5, -180, 5.833, 0]}, {"time": 7.1667, "curve": [7.5, 0, 7.833, -180]}, {"time": 8.1667, "value": -180}]}, "light4": {"scale": [{"x": 0.409, "y": 0.118, "curve": "stepped"}, {"time": 2.1667, "x": 0.409, "y": 0.118, "curve": [2.278, 0.409, 2.389, 1, 2.278, 0.118, 2.389, 1]}, {"time": 2.5, "curve": [2.722, 1, 2.944, 0.409, 2.722, 1, 2.944, 0.118]}, {"time": 3.1667, "x": 0.409, "y": 0.118, "curve": "stepped"}, {"time": 7.1667, "x": 0.409, "y": 0.118, "curve": [7.278, 0.409, 7.389, 1, 7.278, 0.118, 7.389, 1]}, {"time": 7.5, "curve": [7.722, 1, 7.944, 0.409, 7.722, 1, 7.944, 0.118]}, {"time": 8.1667, "x": 0.409, "y": 0.118}]}, "light5": {"scale": [{"x": 0.306, "y": 0.042, "curve": "stepped"}, {"time": 2.1667, "x": 0.306, "y": 0.042, "curve": [2.278, 0.306, 2.389, 1, 2.278, 0.042, 2.389, 1]}, {"time": 2.5, "curve": [2.722, 1, 2.944, 0.306, 2.722, 1, 2.944, 0.042]}, {"time": 3.1667, "x": 0.306, "y": 0.042, "curve": "stepped"}, {"time": 7.1667, "x": 0.306, "y": 0.042, "curve": [7.278, 0.306, 7.389, 1, 7.278, 0.042, 7.389, 1]}, {"time": 7.5, "curve": [7.722, 1, 7.944, 0.306, 7.722, 1, 7.944, 0.042]}, {"time": 8.1667, "x": 0.306, "y": 0.042}]}}}}}