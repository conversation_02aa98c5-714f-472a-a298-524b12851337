﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveRuin : ActionRemoveModel
    {
        public ActionRemoveRuin(int objectID, int layerID) : base(objectID, layerID)
        {
            var obj = Map.currentMap.FindObject(objectID) as RuinData;
            mType = obj.type;
            mLevel = obj.level;
            mObjectType = obj.objectType;
            mColor = obj.color;
            mProperties = obj.properties;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                var layer = map.GetMapLayerByID(mLayerID) as RuinLayer;
                if (layer != null)
                {
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    if (layer.ExistsRuinObjectType(mObjectType))
                    {
                        var scale = layer.ruinDisplayRadius * Vector3.one * 2;
                        var ruinData = new RuinData(mObjectID, mLayerID, Map.currentMap, 0, mPosition, mRotation, scale, modelTemplate, mType, mLevel, mObjectType, mColor, mProperties);
                        layer.AddObject(ruinData);
                        return true;
                    }
                }
            }
            return false;
        }

        RuinType mType;
        int mLevel;
        string mObjectType;
        Color mColor;
        PropertyDatas mProperties;
    }
}

#endif