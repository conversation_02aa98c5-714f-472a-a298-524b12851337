﻿ 



 
 


//temp code
#define USE_ACTION_MAP

using UnityEngine;
using System.Collections.Generic;
using System.Diagnostics;
using System;

namespace TFW.Map
{
    [Flags]
    public enum FrameActionFlag
    {
        //执行一次action
        kRunOneAction = 1,
        //执行一次完整的action,包括其所有的子action
        kRunOneFullAction = 2,
    }

    //地图命令队列
    public class FrameActionQueue
    {
        public FrameActionQueue(string debugName)
        {
            runOnce = false;
            paused = false;
            flag = 0;
            this.debugName = debugName;
        }

        public bool HasAction(long key)
        {
#if USE_ACTION_MAP
            return mActionMap.ContainsKey(key);
#else
            foreach (var action in mActions)
            {
                if (action.key == key)
                {
                    return true;
                }
            }
            return false;
#endif
        }

        public FrameAction FindAction(long key)
        {
#if USE_ACTION_MAP
            LinkedListNode<FrameAction> node;
            bool found = mActionMap.TryGetValue(key, out node);
            if (found)
            {
                return node.Value;
            }
            return null;
#else
            foreach (var action in mActions)
            {
                if (action.key == key)
                {
                    return action;
                }
            }
            return null;
#endif
        }

        public LinkedListNode<FrameAction> FindActionPos(long key)
        {
#if USE_ACTION_MAP
            LinkedListNode<FrameAction> node;
            bool found = mActionMap.TryGetValue(key, out node);
            if (found)
            {
                return node;
            }
            return null;
#else
            var node = mActions.First;
            while (node != null)
            {
                if (node.Value.key == key)
                {
                    return node;
                }
                node = node.Next;
            }
            return null;
#endif
        }

        public void PromoteAction(LinkedListNode<FrameAction> node)
        {
            mActions.Remove(node);
            mActions.AddFirst(node);
        }

        public void InsertAction(LinkedListNode<FrameAction> pos, FrameAction action)
        {
            mActions.AddAfter(pos, action);
#if USE_ACTION_MAP
            mActionMap[action.key] = pos;
#endif
        }

        public void AddAction(FrameAction action, bool addFirst)
        {
            var node = mNodePool.Require();
            node.Value = action;
            if (addFirst)
            {
                mActions.AddFirst(node);
            }
            else
            {
                mActions.AddLast(node);
            }
#if USE_ACTION_MAP
            mActionMap[action.key] = node;
#endif
        }

        public void AddActions(List<FrameAction> actions, bool addFirst)
        {
            for (int i = 0; i < actions.Count; ++i)
            {
                var node = mNodePool.Require();
                node.Value = actions[i];
                if (addFirst)
                {
                    mActions.AddFirst(node);
                }
                else
                {
                    mActions.AddLast(node);
                }
#if USE_ACTION_MAP
                mActionMap[actions[i].key] = node;
#endif
            }
        }

        public bool RemoveActionsOfType(FrameActionType type)
        {
            bool removed = false;
            for (var p = mActions.Last; p != null; p = p.Previous)
            {
                var action = p.Value;
                if (action.IsRunning == false && action.type == type)
                {
                    RemoveAction(action.key);
                    removed = true;
                }
            }
            return removed;
        }

        public bool RemoveAction(long key)
        {
#if USE_ACTION_MAP
            LinkedListNode<FrameAction> node = null;
            bool found = mActionMap.TryGetValue(key, out node);
            if (found)
            {
                node.Value.OnDestroy();
                mActions.Remove(node);
                mNodePool.Release(node);
                mActionMap.Remove(key);
                return true;
            }
            return false;
#else
            var node = mActions.First;
            while (node != null)
            {
                if (node.Value.key == key)
                {
                    node.Value.OnDestroy();
                    mActions.Remove(node);
                    mNodePool.Release(node);
                    return true;
                }
                node = node.Next;
            }
            return false;
#endif
        }

        public void ReleaseAction(LinkedListNode<FrameAction> node)
        {
            node.Value.OnDestroy();
            mNodePool.Release(node);
        }

        public LinkedList<FrameAction> frameActions { get { return mActions; } }

        public bool runOnce { set; get; }
        public bool paused { set; get; }
        public FrameActionFlag flag { set; get; }
        public string debugName { set; get; }

        public LinkedList<FrameAction> mActions = new LinkedList<FrameAction>();
        //temp code, turn it on?
#if USE_ACTION_MAP
        public Dictionary<long, LinkedListNode<FrameAction>> mActionMap = new Dictionary<long, LinkedListNode<FrameAction>>();
#endif

        public static ObjectPool<LinkedListNode<FrameAction>> mNodePool = new ObjectPool<LinkedListNode<FrameAction>>(1000, () => new LinkedListNode<FrameAction>(null));
    }

    //每一帧执行的地图操作管理器
    public class FrameActionManager
    {
        public FrameActionManager(bool isEditorMode)
        {
#if UNITY_EDITOR
            if (isEditorMode == false)
            {
                var obj = new GameObject("FrameActionDebugger");
                mDebugger = obj.AddComponent<FrameActionDebugger>();
            }
#endif
        }

        public void OnDestroy()
        {
            if (mDebugger != null)
            {
                Utils.DestroyObject(mDebugger.gameObject);
                mDebugger = null;
            }
        }

        void Refresh()
        {
            if (mDebugger != null)
            {
                mDebugger.Refresh();
            }
        }

        public int AddQueue(string debugName)
        {
            for (int i = 0; i < mActionQueue.Count; ++i)
            {
                if (mActionQueue[i].debugName == debugName)
                {
                    return i;
                }
            }
            mActionQueue.Add(new FrameActionQueue(debugName));
            return mActionQueue.Count - 1;
        }

        public void PauseQueue(int i, bool pause)
        {
            mActionQueue[i].paused = pause;
        }

        public bool IsQueuePaused(int i)
        {
            return mActionQueue[i].paused;
        }

        public bool HasAction(int layerKey, long key)
        {
            return mActionQueue[layerKey].HasAction(key);
        }

        public FrameAction FindAction(int layerKey, long key)
        {
            return mActionQueue[layerKey].FindAction(key);
        }

        public LinkedListNode<FrameAction> FindActionPos(int layerKey, long key)
        {
            return mActionQueue[layerKey].FindActionPos(key);
        }

        public void PromoteAction(int layerKey, LinkedListNode<FrameAction> action)
        {
            mActionQueue[layerKey].PromoteAction(action);
        }

        public void AddActions(int layerKey, List<FrameAction> actions, bool addFirst)
        {
            mActionQueue[layerKey].AddActions(actions, addFirst);
            Refresh();
        }

        public void AddAction(int layerKey, FrameAction action, bool addFirst)
        {
            mActionQueue[layerKey].AddAction(action, addFirst);
            Refresh();
        }

        public void InsertFrameAction(int queueIndex, LinkedListNode<FrameAction> pos, FrameAction action)
        {
            mActionQueue[queueIndex].InsertAction(pos, action);
            Refresh();
        }

        public bool RemoveActionsOfType(int index, FrameActionType type)
        {
            bool removed = mActionQueue[index].RemoveActionsOfType(type);
            Refresh();
            return removed;
        }

        public bool RemoveAction(int layerKey, long key)
        {
            Refresh();
            return mActionQueue[layerKey].RemoveAction(key);
        }

        //执行某个队列里的命令
        bool Execute(int i)
        {
#if MAP_DEBUG
            bool running = false;
            if (mActionQueue[i].paused)
            {
                if (mActionQueue[i].runOnce)
                {
                    running = true;
                    mActionQueue[i].runOnce = false;
                }
                else
                {
                    running = false;
                }
            }
            else
            {
                running = true;
            }
#else
            bool running = true;
#endif
            if (running)
            {
                var actions = mActionQueue[i].mActions;
                while (actions.Count > 0)
                {
                    var curActionNode = actions.First;
                    var curAction = curActionNode.Value;
                    if (curAction.executeTimeStamp != mCurrentTimeStamp)
                    {
                        bool finished = curAction.Do();
                        ++mExecutedActionCount;
                        if (finished)
                        {
                            //当前命令包括其子命令都执行完之后才跳转到下一条命令
                            if (curAction.keepAlive == false)
                            {
                                //不是keep alive的action在执行完毕后立即删除,否则继续放在队列中等下一帧执行
                                actions.Remove(curActionNode);
                                mActionQueue[i].ReleaseAction(curActionNode);
                            }
                            else
                            {
                                //把执行过的action弄到最后,避免下一帧又从这个action开始执行,并耗尽时间片,导致后面的action无法执行
                                //通过时间标记来避免一个action在一帧执行多次
                                actions.Remove(curActionNode);
                                actions.AddLast(curActionNode);
                                curAction.executeTimeStamp = mCurrentTimeStamp;
                            }
                        }
                        if (mStopWatch.ElapsedMilliseconds >= mOneFrameMaxExecuteTime)
                        {
                            //UnityEngine.Debug.LogError("elapsed time: " + mStopWatch.ElapsedMilliseconds + " action count: " + mExecutedActionCount);
                            //超过最大消耗时间了,这帧不再执行任务
                            return true;
                        }
#if MAP_DEBUG
                        if (finished && mActionQueue[i].flag.HasFlag(FrameActionFlag.kRunOneFullAction))
                        {
                            break;
                        }
                        else if (mActionQueue[i].flag.HasFlag(FrameActionFlag.kRunOneAction))
                        {
                            break;
                        }
#endif
                    }
                    else
                    {
                        break;
                    }
                }
            }
            return false;
        }

        public void UpdateQueue(int i)
        {
            mActionQueue[i].runOnce = true;
            var oldFlag = mActionQueue[i].flag;
            mActionQueue[i].flag = FrameActionFlag.kRunOneFullAction;
            Execute(i);
            mActionQueue[i].flag = oldFlag;

            ++mCurrentTimeStamp;
        }

        public void Update()
        {
            mStopWatch.Restart();
            mExecutedActionCount = 0;
            for (int i = 0; i < mActionQueue.Count; ++i)
            {
                bool timeout = Execute(i);
                if (timeout)
                {
                    break;
                }
            }

            Refresh();

            ++mCurrentTimeStamp;
        }

        public FrameActionQueue FindActionQueue(string name)
        {
            for (int i = 0; i < mActionQueue.Count; ++i)
            {
                if (mActionQueue[i].debugName == name)
                {
                    return mActionQueue[i];
                }
            }

            return null;
        }

        public FrameActionQueue GetActionQueue(int index)
        {
            if (index >= 0 && index < mActionQueue.Count)
            {
                return mActionQueue[index];
            }
            return null;
        }

        public int actionQueueCount { get { return mActionQueue.Count; } }

        //每帧最多执行x毫秒
        public long maxExecuteTimePerFrame { set { mOneFrameMaxExecuteTime = value; } get { return mOneFrameMaxExecuteTime; } }

        //地图每一层一个队列,按优先级排列
        List<FrameActionQueue> mActionQueue = new List<FrameActionQueue>();
        Stopwatch mStopWatch = new Stopwatch();
        //每帧最多执行x毫秒
        long mOneFrameMaxExecuteTime = MapModule.frameActionMaxExecuteTime;
        long mCurrentTimeStamp;
        //debug
        FrameActionDebugger mDebugger;
        int mExecutedActionCount = 0;
    }
}
