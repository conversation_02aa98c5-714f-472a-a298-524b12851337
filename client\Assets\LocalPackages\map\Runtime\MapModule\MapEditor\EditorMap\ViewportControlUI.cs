﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [CustomEditor(typeof(ViewportControl))]
    class ViewportControlUI : UnityEditor.Editor
    {
        void OnDestroy()
        {
            var v = target as ViewportControl;
            v.UpdateViewport();
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
        }
    }
}


#endif