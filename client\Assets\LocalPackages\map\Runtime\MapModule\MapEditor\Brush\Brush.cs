﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class Brush
    {
        static Color black = new Color(0, 0, 0, 0);

        class BrushTextureData
        {
            public void OnDestroy()
            {
                Object.DestroyImmediate(texture);
                texture = null;
                textureData = null;
            }

            public Texture2D texture;
            public List<Color[]> textureData;
            public int textureWidth;
            public int textureHeight;
        }

        public Brush(RotateTexture textureRotator, Texture2D texture)
        {
            Debug.Assert(texture != null);
            Debug.Assert(texture.isReadable);
            mTextureData = CreateAlphaTexture(texture);
            CreateRotatedTexture(textureRotator, 0, false);
        }

        public void OnDestroy()
        {
            if (mTextureData != null)
            {
                mTextureData.OnDestroy();
                mTextureData = null;
            }

            if (mRotatedTextureData != null)
            {
                mRotatedTextureData.OnDestroy();
                mRotatedTextureData = null;
            }
        }

        public void CreateRotatedTexture(RotateTexture rotator, float yRotation, bool rgb)
        {
            if (mRotatedTextureData != null)
            {
                mRotatedTextureData.OnDestroy();
            }

            var rotatedTexture = rotator.GetRotateTexture(mTextureData.texture, yRotation, rgb);
            mRotatedTextureData = CreateAlphaTexture(rotatedTexture);
        }

        public int GetMipmap(int brushSize, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            int minSize = 32;
            brushSize = Mathf.Max(minSize, brushSize);

            int k = Mathf.ClosestPowerOfTwo(brushSize);
            for (int i = 0; i < data.textureData.Count; ++i)
            {
                if (k == Mathf.Sqrt(data.textureData[i].Length))
                {
                    return i;
                }
            }
            return 0;
        }

        public bool Sample(int mipmap, float rx, float ry, out Color val, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            val = black;
            int textureSize = data.texture.width;
            int x = Mathf.FloorToInt(rx * textureSize);
            int y = Mathf.FloorToInt(ry * textureSize);
            if (x >= 0 && x < textureSize && y >= 0 && y < textureSize)
            {
                val = data.textureData[mipmap][y * textureSize + x];
                return true;
            }
            return false;
        }

        public Color Sample(int mipmap, float rx, float ry, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            int size = (int)Mathf.Sqrt(data.textureData[mipmap].Length);
            int x = (int)(rx * (size - 1));
            int y = (int)(ry * (size - 1));
            if (x >= 0 && x < size && y >= 0 && y < size)
            {
                int idx = y * size + x;
                return data.textureData[mipmap][idx];
            }
            return black;
        }

        public Color SampleBilinear(int mipmap, float rx, float ry, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            int size = (int)Mathf.Sqrt(data.textureData[mipmap].Length);
            int size1 = size - 1;
            int x0 = (int)(rx * size1);
            int y0 = (int)(ry * size1);

            if (x0 >= 0 && x0 < size && y0 >= 0 && y0 < size)
            {
                int x1 = Mathf.Min(Mathf.CeilToInt(rx * size1), size1);
                int y1 = Mathf.Min(Mathf.CeilToInt(ry * size1), size1);
                float r1 = rx - (int)rx;
                float r2 = ry - (int)ry;

                int idx0 = y0 * size + x0;
                int idx1 = y0 * size + x1;
                int idx2 = y1 * size + x0;
                int idx3 = y1 * size + x1;
                Color p0 = data.textureData[mipmap][idx0];
                Color p1 = data.textureData[mipmap][idx1];
                Color p2 = data.textureData[mipmap][idx2];
                Color p3 = data.textureData[mipmap][idx3];

                Color px0 = Color.Lerp(p0, p1, r1);
                Color px1 = Color.Lerp(p2, p3, r1);
                return Color.Lerp(px0, px1, r2);
            }
            return black;
        }

        public float SampleAlpha(int mipmap, float rx, float ry, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            int size = (int)Mathf.Sqrt(data.textureData[mipmap].Length);
            int x = (int)(rx * (size - 1));
            int y = (int)(ry * (size - 1));
            if (x >= 0 && x < size && y >= 0 && y < size)
            {
                int idx = y * size + x;
                return data.textureData[mipmap][idx].a;
            }
            return 0;
        }

        public float SampleAlphaBilinear(int mipmap, float rx, float ry, bool rotated)
        {
            BrushTextureData data = rotated ? mRotatedTextureData : mTextureData;

            int size = (int)Mathf.Sqrt(data.textureData[mipmap].Length);
            int size1 = size - 1;
            int x0 = (int)(rx * size1);
            int y0 = (int)(ry * size1);

            if (x0 >= 0 && x0 < size && y0 >= 0 && y0 < size)
            {
                int x1 = Mathf.Min(Mathf.CeilToInt(rx * size1), size1);
                int y1 = Mathf.Min(Mathf.CeilToInt(ry * size1), size1);
                float r1 = rx - (int)rx;
                float r2 = ry - (int)ry;

                int idx0 = y0 * size + x0;
                int idx1 = y0 * size + x1;
                int idx2 = y1 * size + x0;
                int idx3 = y1 * size + x1;
                float p0 = data.textureData[mipmap][idx0].a;
                float p1 = data.textureData[mipmap][idx1].a;
                float p2 = data.textureData[mipmap][idx2].a;
                float p3 = data.textureData[mipmap][idx3].a;

                float px0 = Mathf.Lerp(p0, p1, r1);
                float px1 = Mathf.Lerp(p2, p3, r1);
                return Mathf.Lerp(px0, px1, r2);
            }
            return 0;
        }

        BrushTextureData CreateAlphaTexture(Texture2D texture)
        {
            BrushTextureData data = new BrushTextureData();
            data.textureWidth = texture.width;
            data.textureHeight = texture.height;
            data.texture = new Texture2D(texture.width, texture.height, TextureFormat.RGBA32, false);
            data.texture.alphaIsTransparency = true;
            data.texture.wrapMode = TextureWrapMode.Clamp;
            var texData = new Color[data.textureWidth * data.textureHeight];
            int idx = 0;
            var pixels = texture.GetPixels();
            int width = data.textureWidth;
            int height = data.textureHeight;
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    texData[idx] = pixels[idx];
                    ++idx;
                }
            }
            data.texture.SetPixels(texData);
            data.texture.Apply();
            CreateTextureMipmaps(pixels, data);

            return data;
        }

        void CreateTextureMipmaps(Color[] textureData, BrushTextureData data)
        {
            int nMipmaps = 0;
            int size = data.textureWidth;
            while (size > 0)
            {
                ++nMipmaps;
                size /= 2;
            }

            int width = data.textureWidth;
            int height = data.textureHeight;
            data.textureData = new List<Color[]>(nMipmaps);
            data.textureData.Add(new Color[width * height]);
            int idx = 0;
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    data.textureData[0][idx] = textureData[idx];
                    ++idx;
                }
            }

            Texture2D tempTexture = new Texture2D(width, height, TextureFormat.RGBA32, false);
            tempTexture.SetPixels(textureData);
            tempTexture.Apply();

            int newWidth = width;
            int newHeight = height;
            for (int i = 1; i < nMipmaps; ++i)
            {
                newWidth /= 2;
                newHeight /= 2;

                var newScaleTexture = Utils.ResizeTexture(tempTexture, newWidth, newHeight);
                Object.DestroyImmediate(tempTexture);
                tempTexture = newScaleTexture;
                data.textureData.Add(new Color[newWidth * newHeight]);
                int idxk = 0;
                var mipmapTextureData = newScaleTexture.GetPixels();
                var td = data.textureData;
                for (int y = 0; y < newHeight; ++y)
                {
                    for (int x = 0; x < newWidth; ++x)
                    {
                        td[i][idxk] = mipmapTextureData[idxk];
                        ++idxk;
                    }
                }
            }

            Object.DestroyImmediate(tempTexture);
        }

        public Texture2D GetTexture(bool rotated)
        {
            return rotated ? mRotatedTextureData.texture : mTextureData.texture;
        }

        BrushTextureData mTextureData;
        BrushTextureData mRotatedTextureData;
    }

    public class BrushManager
    {
        public BrushManager()
        {
            mTextureRotator = new RotateTexture();
            Refresh();
        }

        public void OnDestroy()
        {
            mTextureRotator.OnDestroy();
            mTextureRotator = null;
        }

        void Clear()
        {
            for (int i = 0; i < mBrushies.Count; ++i)
            {
                mBrushies[i].OnDestroy();
            }
            mBrushies.Clear();
            mActiveBrush = -1;
        }

        public void AddBrush(Texture2D texture)
        {
            if (texture != null)
            {
                var brush = new Brush(mTextureRotator, texture);
                mBrushies.Add(brush);
            }
            else
            {
                Debug.Assert(false, "invalid brush!");
            }

            if (mBrushies.Count == 1)
            {
                mActiveBrush = 0;
            }
        }

        public Brush GetBrush(int i)
        {
            if (i >= 0 && i < mBrushies.Count)
            {
                return mBrushies[i];
            }
            return null;
        }

        public void CreateRotatedTexture(float angle, bool rgb)
        {
            var brush = GetActiveBrush();
            if (brush != null)
            {
                brush.CreateRotatedTexture(mTextureRotator, angle, rgb);
            }
        }

        public Brush GetActiveBrush()
        {
            if (mActiveBrush >= 0 && mActiveBrush < mBrushies.Count)
            {
                return GetBrush(mActiveBrush);
            }
            return null;
        }

        public void SetActiveBrush(int idx)
        {
            if (idx >= 0 && idx < mBrushies.Count)
            {
                mActiveBrush = idx;
            }
        }

        public void Refresh()
        {
            AddBrushInFolder(MapModule.brushFolder);
        }

        void AddBrushInFolder(string folder)
        {
            Clear();

            var enumrator = Directory.EnumerateFiles(folder, "*.png", SearchOption.AllDirectories);
            foreach (var path in enumrator)
            {
                var texture = MapModuleResourceMgr.LoadTexture(path);
                if (texture.isReadable)
                {
                    AddBrush(texture);
                }
                else
                {
                    var readableTexture = EditorUtils.CreateTexture(path, true);
                    readableTexture.wrapMode = TextureWrapMode.Clamp;
                    AddBrush(readableTexture);
                    Object.DestroyImmediate(readableTexture);
                }
            }
        }

        public int brushCount { get { return mBrushies.Count; } }
        public int activeBrush { get { return mActiveBrush; } }

        List<Brush> mBrushies = new List<Brush>();
        int mActiveBrush = -1;
        RotateTexture mTextureRotator;
    }
}


#endif