﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class Map
    {
        /// <summary>
        /// lod开始偏移的配置位置
        /// </summary>
        public static int LOD_OFFSET_INDEX = 0;
        /// <summary>
        /// lod偏移数量
        /// </summary>
        public static int LOD_OFFSET_NUM = 0;
        /// <summary>
        /// 加载地图配置数据
        /// </summary>
        public virtual void LoadMapData(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter,
            GameObject root)
        {
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -1");
            mDataFolder = editorData.setting.dataFolder;
            var source = editorData.map;
            var viewSize = CalculateViewSize(viewCenter);
            var lodManager = CreateLODManager(editorData, this);
            var localObstacleManager = CreateLocalObstacleManager(editorData, this);
            var globalObstacleManager = CreateGlobalObstacleManager(this, editorData);
            var cameraCollider = CreateMapCameraCollider(editorData, root);
            var gridRegionSetting = CreateGridRegionSetting(editorData);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -9");
            var transformTable = CreateTransformTable(editorData.modelTemplates.transformTable);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -10");
            mTransformTable = transformTable;
            mData = new MapData(this, viewCenter, viewSize.x, viewSize.y, source.width, source.height,
                source.borderHeight, new List<ModelTemplate>(), editorData.setting.cameraMoveRange, lodManager,
                localObstacleManager, globalObstacleManager, cameraCollider, gridRegionSetting, editorData.map.isCircle,
                editorData.version, source.backExtendedSize, source.farClipOffset, source.useTerrainHeight,
                source.groundTileSize, source.frontTileSize, source.mapDataGenerationRange,
                editorData.pluginList.pluginLayerNames, editorData.map.maxCameraColliderHeight);

            LoadModelTemplates(editorData, this);
            LoadTerrainPrefabManager(editorData);
            LoadVaryingTileSizeTerrainPrefabManager(editorData);
            PreloadUsedPrefabs(false);
        }

        public IEnumerator LoadMapDataAsync(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter,
            GameObject root)
        {
            mDataFolder = editorData.setting.dataFolder;
            var source = editorData.map;
            var viewSize = CalculateViewSize(viewCenter);
            var lodManager = CreateLODManager(editorData, this);
            var localObstacleManager = CreateLocalObstacleManager(editorData, this);
            var refValue = new Ref<MapGlobalObstacleManager>();
            yield return CreateGlobalObstacleManagerAsync(this, editorData, refValue);
            var globalObstacleManager = refValue.value;
            var cameraCollider = CreateMapCameraCollider(editorData, root);
            var gridRegionSetting = CreateGridRegionSetting(editorData);
            var transformTable = CreateTransformTable(editorData.modelTemplates.transformTable);
            mTransformTable = transformTable;
            mData = new MapData(this, viewCenter, viewSize.x, viewSize.y, source.width, source.height,
                source.borderHeight, new List<ModelTemplate>(), editorData.setting.cameraMoveRange, lodManager,
                localObstacleManager, globalObstacleManager, cameraCollider, gridRegionSetting, editorData.map.isCircle,
                editorData.version, source.backExtendedSize, source.farClipOffset, source.useTerrainHeight,
                source.groundTileSize, source.frontTileSize, source.mapDataGenerationRange,
                editorData.pluginList.pluginLayerNames, editorData.map.maxCameraColliderHeight);

            LoadModelTemplates(editorData, this);
            LoadTerrainPrefabManager(editorData);
            LoadVaryingTileSizeTerrainPrefabManager(editorData);
            PreloadUsedPrefabs(false);
        }

        private MapLODManager CreateLODManager(config.SLGMakerData editorData, Map map)
        {
            int n = editorData.map.lodConfig.lods.Length;
            var lods = new MapLODManager.LOD[n];
            for (int i = 0; i < n; ++i)
            {
                var lodConfig = editorData.map.lodConfig.lods[i];
                float height = lodConfig.cameraHeight;
                if (i == 0)
                {
                    if (mMapSetting != null)
                    {
                        height = mMapSetting.cameraSetting.cameraMinHeight;
                    }
                    else
                    {
                        height = 0;
                    }
                }
				
                if (i > LOD_OFFSET_INDEX && i <= LOD_OFFSET_INDEX + LOD_OFFSET_NUM) {
                    lods[i] = lods[LOD_OFFSET_INDEX];
				}
                else {
                	lods[i] = new MapLODManager.LOD("", height, lodConfig.showTerritory);
				}
            }

            var lodManager = new MapLODManager(map, lods);
            return lodManager;
        }

        MapLocalObstacleManager CreateLocalObstacleManager(config.SLGMakerData editorData, Map map)
        {
            var d = editorData.localObstacleManager;

            Dictionary<int, KeyValuePair<Vector3[], int[]>> obstacles =
                new Dictionary<int, KeyValuePair<Vector3[], int[]>>();
            if (d.obstacles != null)
            {
                for (int i = 0; i < d.obstacles.Length; ++i)
                {
                    var modelTemplateID = d.obstacles[i].id;
                    var vertices = d.obstacles[i].vertices;
                    var indices = d.obstacles[i].triangleIndices;

                    var pair = new KeyValuePair<Vector3[], int[]>(vertices, indices);
                    obstacles[modelTemplateID] = pair;
                }
            }

            var obstacleManager = new MapLocalObstacleManager(map, editorData.map.width, editorData.map.height,
                d.regionWidth, d.regionHeight, d.tiles, obstacles, d.obstacleMaterialPath, editorData.map.isCircle);
            return obstacleManager;
        }

        private MapGlobalObstacleManager CreateGlobalObstacleManager(Map map, config.SLGMakerData editorData)
        {
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -1");
            var d = editorData.globalObstacleManager;
            return new MapGlobalObstacleManager("global obstacle", map, editorData.map.width, editorData.map.height,
                editorData.map.isCircle, d.obstacleMaterialPath, d.obstacleVertices, d.obstacleIndices, d.gridObstacles,
                d.gridSize);
        }

        private IEnumerator CreateGlobalObstacleManagerAsync(Map map, config.SLGMakerData editorData,
            Ref<MapGlobalObstacleManager> ret)
        {
            var d = editorData.globalObstacleManager;
            var manager = new MapGlobalObstacleManager("global obstacle", map, editorData.map.width, editorData.map.height,
                editorData.map.isCircle, d.obstacleMaterialPath);
            yield return manager.InitAsync(d.obstacleVertices, d.obstacleIndices, d.gridObstacles,
                d.gridSize);
            ret.value = manager;
        }

        MapCameraCollider CreateMapCameraCollider(config.SLGMakerData editorData, GameObject root)
        {
            if (editorData.cameraCollider.vertices != null)
            {
                return new MapCameraCollider(editorData.cameraCollider.vertices, editorData.cameraCollider.indices,
                    root.transform);
            }

            return null;
        }

        TransformTable CreateTransformTable(config.TransformTable t)
        {
            if (t == null)
            {
                return null;
            }

            TransformTable table = new TransformTable(t.scalings, t.rotations);
            return table;
        }

        GridRegionSetting CreateGridRegionSetting(config.SLGMakerData editorData)
        {
            var data = editorData.gridRegionSetting;
            if (data == null)
            {
                return null;
            }

            int n = editorData.gridRegionSetting.templates.Length;
            List<GridRegionTemplate> templates = new List<GridRegionTemplate>(n);
            for (int i = 0; i < n; ++i)
            {
                var src = editorData.gridRegionSetting.templates[i];
                templates.Add(new GridRegionTemplate(src.color, src.type));
            }

            GridRegionSetting setting = new GridRegionSetting(data.horizontalGridCount, data.verticalGridCount,
                data.gridWidth, data.gridHeight, data.gridData, templates);
            return setting;
        }

        void LoadTerrainPrefabManager(config.SLGMakerData editorData)
        {
            int nGroups = editorData.terrainPrefabManager.groups.Length;
            for (int i = 0; i < nGroups; ++i)
            {
                var group = editorData.terrainPrefabManager.groups[i];
                int nPrefabs = group.prefabPaths.Length;
                for (int k = 0; k < nPrefabs; ++k)
                {
                    mData.terrainPrefabManager.SetGroupPrefabByID(group.id, k, 0, group.prefabPaths[k].prefabPath);
                    if (group.prefabPaths[k].subGroupPrefabPaths != null)
                    {
                        int n = group.prefabPaths[k].subGroupPrefabPaths.Length;
                        for (int x = 1; x < n; ++x)
                        {
                            mData.terrainPrefabManager.SetGroupPrefabByID(group.id, k, x,
                                group.prefabPaths[k].subGroupPrefabPaths[x]);
                        }
                    }
                }
            }
        }

        void LoadVaryingTileSizeTerrainPrefabManager(config.SLGMakerData editorData)
        {
            int nGroups = editorData.varyingTileSizeTerrainPrefabManager.groups.Length;
            for (int i = 0; i < nGroups; ++i)
            {
                var group = editorData.varyingTileSizeTerrainPrefabManager.groups[i];
                int nPrefabs = group.prefabPaths.Length;
                for (int k = 0; k < nPrefabs; ++k)
                {
                    mData.varyingTileSizeTerrainPrefabManager.SetGroupPrefabByID(group.id, k,
                        group.prefabPaths[k].prefabPath);
                    mData.varyingTileSizeTerrainPrefabManager.SetGroupPrefabSizeByID(group.id, k,
                        group.prefabPaths[k].size);
                }
            }
        }

        ModelTemplateLODInfo CreateModelTemplateLODInfo(config.ModelTemplateLODInfo src, string[] stringTable)
        {
            if (src == null)
            {
                return null;
            }

            ModelTemplateLODInfo dst = new ModelTemplateLODInfo();
            dst.existedLODs = src.existedLODs;
            int n = src.lodPrefabPathIndices.Count;
            dst.lODPrefabPaths = new List<string>(n);
            for (int i = 0; i < n; ++i)
            {
                dst.lODPrefabPaths.Add(stringTable[src.lodPrefabPathIndices[i]]);
            }

            return dst;
        }

        void LoadModelTemplates(config.SLGMakerData editorData, Map map)
        {
            var stringTable = editorData.modelTemplates.stringTables;
            Dictionary<string, ModelTemplate> modelTemplatesMap = new Dictionary<string, ModelTemplate>(1000);

            var templates = editorData.modelTemplates.modelTemplates;
            int nTemplates = templates.Length;
            for (int i = 0; i < nTemplates; ++i)
            {
                var template = templates[i];

                List<List<ModelTemplate>> childrenModelTemplates = new List<List<ModelTemplate>>();
                List<List<ChildPrefabTransform>> childPrefabTransforms = new List<List<ChildPrefabTransform>>();
                if (template.isTileModelTemplate)
                {
                    int nLODs = template.childrenPrefabs.Length;
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        var childInfo = template.childrenPrefabs[lod];
                        int nChildPrefabs = childInfo.childrenPrefabBounds.Length;
                        List<ModelTemplate> oneLODChildrenTemplates = new List<ModelTemplate>();
                        List<ChildPrefabTransform> oneLODChildrenPrefabTransforms =
                            new List<ChildPrefabTransform>(nChildPrefabs);
                        //create child prefab使用的model template,这次modeltemplate可能被多个tile共享
                        for (int m = 0; m < nChildPrefabs; ++m)
                        {
                            var childPrefabPath = stringTable[childInfo.childrenPrefabPathIndices[m]];

                            ModelTemplate childTemplate;
                            modelTemplatesMap.TryGetValue(childPrefabPath, out childTemplate);
                            if (childTemplate == null)
                            {
                                childTemplate = new ModelTemplate(map.nextCustomObjectID, map, childPrefabPath,
                                    childInfo.childrenPrefabBounds[m], false, new List<List<ModelTemplate>>(),
                                    new List<List<ChildPrefabTransform>>(), null, false);
                                modelTemplatesMap[childPrefabPath] = childTemplate;
                            }

                            var childPrefabTransform = new ChildPrefabTransform();
                            childPrefabTransform.position = childInfo.childrenPrefabPosition[m];
                            childPrefabTransform.localBoundsInPrefab = childInfo.childrenPrefabBounds[m];
                            childPrefabTransform.scaleIndex = childInfo.childrenPrefabScalingIndex[m];
                            childPrefabTransform.rotationIndex = childInfo.childrenPrefabRotationIndex[m];
                            childPrefabTransform.objectType =
                                (TileObjectType) childInfo.childrenPrefabTileObjectType[m];

                            oneLODChildrenPrefabTransforms.Add(childPrefabTransform);
                            oneLODChildrenTemplates.Add(childTemplate);
                        }

                        childrenModelTemplates.Add(oneLODChildrenTemplates);
                        childPrefabTransforms.Add(oneLODChildrenPrefabTransforms);
                    }
                }

                var modelTemplateLODInfo = CreateModelTemplateLODInfo(template.lodInfo, stringTable);
                AddModelTemplate(template.prefabPath, map, Utils.BoundsToRect(template.bounds),
                    template.isTileModelTemplate, childrenModelTemplates, childPrefabTransforms, modelTemplateLODInfo,
                    template.id, template.preload);
            }
        }
    }
}
