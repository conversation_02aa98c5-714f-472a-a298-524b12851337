%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Houndstooth
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17902\n-1451;-120;1004;726;3255.592;1165.008;3.399142;True;False\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;1;-2288,0;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;25;-2240,144;Inherit;False;Tiling;2;0;False;1;0;FLOAT2;5,5;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;68;-1280,0;Inherit;False;FLOAT3;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;24;-2048,0;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;63;-1456,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;20;-1600,208;Inherit;False;Teeth;1;1;False;1;0;FLOAT;2;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;66;-1600,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;65;-1856,96;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;11;-624,0;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;16;-480,0;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.LerpOp;15;-240,0;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;10;-848,0;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0.5,0.5,0;False;2;FLOAT3;0.55,0.55,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector4Node;23;-1184,96;Inherit;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;0.5,0.55,0.95,1;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FractNode;8;-1088,0;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;12;-848,128;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0.95,0.95,0;False;2;FLOAT3;1,1,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;0,0;Inherit;False;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;68;0;24;0\nWireConnection;68;2;63;0\nWireConnection;24;0;1;0\nWireConnection;24;1;25;0\nWireConnection;63;0;66;0\nWireConnection;63;1;20;0\nWireConnection;66;0;65;0\nWireConnection;66;1;65;1\nWireConnection;65;0;24;0\nWireConnection;11;0;10;0\nWireConnection;11;1;12;0\nWireConnection;16;0;11;0\nWireConnection;15;0;16;0\nWireConnection;15;1;16;1\nWireConnection;15;2;16;2\nWireConnection;10;0;8;0\nWireConnection;10;1;23;1\nWireConnection;10;2;23;2\nWireConnection;8;0;68;0\nWireConnection;12;0;8;0\nWireConnection;12;1;23;3\nWireConnection;12;2;23;4\nWireConnection;0;0;15;0\nASEEND*/\n//CHKSM=85441DDEF3C9BF306BA13CF583CEBD72A1CEC9B0"
  m_functionName: 
  m_description: Creates a duotone textile pattern.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
