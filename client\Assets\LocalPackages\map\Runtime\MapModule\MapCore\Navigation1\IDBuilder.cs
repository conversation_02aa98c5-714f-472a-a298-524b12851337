﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map.Nav
{
    // 顶点编号生成器
    public class IDBuilder
    {
        Dictionary<Vector2Int, int> key2Index;// 序号映射，key为原始序号，value编号

        public IDBuilder(int cap)
        {
            key2Index = new Dictionary<Vector2Int, int>(cap);
        }

        // 获取编号
        public int GetIndexByKey(Vector2Int key)
        {
            //20210823 添加锁 由于NavigationManager里使用了多线程处理 线上抛异常
            //IndexOutOfRangeException:index was outside the bounds of the array 
            //TryGetValue 底层函数FindEntry时抛异常 数组越界
            lock (key2Index)
            {
                int id;
                bool ok = key2Index.TryGetValue(key, out id);
                if (ok)
                {
                    return id;
                }
                // 新建
                id = key2Index.Count;
                key2Index[key] = id;

                return id;
            }
        }

        //生成边的序号
        public static Vector2Int GenEdgeKey(int i, int j)
        {
            // i,j := 1,2和i,j := 2,1 是同一条边
            int min, max;
            if (i < j)
            {
                min = i;
                max = j;
            }
            else
            {
                min = j;
                max = i;
            }
            return new Vector2Int(min, max);
        }
    }
}