﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    class CreateTGAWindow : EditorWindow
    {
        void OnGUI()
        {
            mOutputFolder = EditorGUILayout.TextField("Output Folder", mOutputFolder);
            if (GUILayout.Button("Create"))
            {
                if (string.IsNullOrEmpty(mOutputFolder))
                {
                    EditorUtility.DisplayDialog("Error", "Select a folder", "OK");
                    return;
                }

                CreateTGA(mOutputFolder);
                Close();
            }
        }

        public static void CreateTGA(string outputFolder)
        {
            var enumerator = Directory.EnumerateFiles(outputFolder, "*.png", SearchOption.TopDirectoryOnly);
            foreach (var filePath in enumerator)
            {
                var validPath = filePath.Replace('\\', '/');
                validPath = Utils.ConvertToUnityAssetsPath(validPath);
                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(validPath);
                bool deleteTexture = false;
                if (!texture.isReadable)
                {
                    deleteTexture = true;
                    texture = EditorUtils.CreateTexture(validPath, true);
                }
                byte[] bytes = texture.EncodeToTGA();
                string newTexturePath = Utils.RemoveExtension(validPath) + ".tga";
                System.IO.File.WriteAllBytes(newTexturePath, bytes);

                if (deleteTexture)
                {
                    Object.DestroyImmediate(texture);
                }
            }
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        string mOutputFolder;
    }
}
#endif