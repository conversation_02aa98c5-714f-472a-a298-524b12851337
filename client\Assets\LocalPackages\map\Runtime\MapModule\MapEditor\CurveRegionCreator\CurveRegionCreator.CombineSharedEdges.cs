﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        class SharedEdgeInfo
        {
            public int selfTerritoryID;
            public int neighbourTerritoryID;
            public Mesh mesh;
            public string originalPrefabPath;
            public string originalMeshPath;
        }

        class ModifiedEndPointVertex
        {
            public ModifiedEndPointVertex(Vector3 oldPos, Vector3 newPos)
            {
                oldPosition = oldPos;
                newPosition = newPos;
            }

            public Vector3 oldPosition;
            public Vector3 newPosition;
        }

        class SharedEdgePair
        {
            public List<SharedEdgeInfo> edgePair = new List<SharedEdgeInfo>();
        }

        void CombineTerritorySharedEdges(bool generateAssets, int lod)
        {
            mModifiedEndPoints.Clear();
            mFixedEdgeMeshies.Clear();

            List<SharedEdgePair> allSharedEdgePairs = new List<SharedEdgePair>();
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var sharedEdges = mTerritories[i].sharedEdges;
                foreach (var edge in sharedEdges)
                {
                    if (edge.neighbourRegionID != 0)
                    {
                        SharedEdgePair pair = FindEdgePair(allSharedEdgePairs, edge);
                        if (pair == null)
                        {
                            pair = new SharedEdgePair();
                            allSharedEdgePairs.Add(pair);
                        }
                        var sharedEdgeInfo = new SharedEdgeInfo();
                        sharedEdgeInfo.selfTerritoryID = edge.selfRegionID;
                        sharedEdgeInfo.neighbourTerritoryID = edge.neighbourRegionID;
                        sharedEdgeInfo.mesh = edge.gameObject.GetComponent<MeshFilter>().sharedMesh;
                        sharedEdgeInfo.originalPrefabPath = edge.prefabPath;
                        sharedEdgeInfo.originalMeshPath = edge.meshPath;
                        pair.edgePair.Add(sharedEdgeInfo);
                    }
                }
            }

            //modify mesh
            foreach (var pair in allSharedEdgePairs)
            {
                ModifyEdgePairMesh(pair, generateAssets, lod);
            }

            //修改neighbour territory id 为0的edge的头尾顶点坐标,保证能衔接上合并成一条边的shared edge
            foreach (var p in mModifiedEndPoints)
            {
                foreach (var t in mTerritories)
                {
                    foreach (var edge in t.sharedEdges)
                    {
                        if (edge.neighbourRegionID == 0)
                        {
                            FixEdgeEndPoint(edge, p, generateAssets);
                        }
                    }
                }
            }

            foreach (var edge in mFixedEdgeMeshies)
            {
                if (generateAssets)
                {
                    //Debug.LogError($"create asset in FixEdgeEndPoint {edge.meshPath}");
                    //create edge mesh
                    var mesh = edge.gameObject.GetComponent<MeshFilter>().sharedMesh;
                    var localMesh = CreateLocalMesh(mesh, edge.selfRegionID);
                    AssetDatabase.CreateAsset(localMesh, edge.meshPath);

                    string name = Utils.GetPathName(edge.prefabPath, false);
                    var edgeObj = new GameObject(name);
                    edgeObj.transform.position = new Vector3(0, 0, 0);
                    var renderer = edgeObj.AddComponent<MeshRenderer>();
                    var filter = edgeObj.AddComponent<MeshFilter>();
                    filter.sharedMesh = localMesh;
                    renderer.sharedMaterial = mInput.settings.edgeMaterial;
                    PrefabUtility.SaveAsPrefabAssetAndConnect(edgeObj, edge.prefabPath, InteractionMode.AutomatedAction);
                    Utils.DestroyObject(edgeObj);
                }
            }
        }

        void FixEdgeEndPoint(SharedEdgeWithNeighbourTerritroy edge, ModifiedEndPointVertex p, bool generateAssets)
        {
            if (edge.fixedEdgePointCount == 2)
            {
                return;
            }

            var mesh = edge.gameObject.GetComponent<MeshFilter>().sharedMesh;
            var vertices = mesh.vertices;

            bool changed = false;
            if (Utils.ToVector2(vertices[1]) == Utils.ToVector2(p.oldPosition))
            {
                vertices[1] = p.newPosition;
                changed = true;
            }
            if (Utils.ToVector2(vertices[vertices.Length - 1]) == Utils.ToVector2(p.oldPosition))
            {
                vertices[vertices.Length - 1] = p.newPosition;
                changed = true;
            }
            if (changed)
            {
                float edgeHeight = mInput.settings.edgeHeight;
                for (int i = 0; i < vertices.Length; ++i)
                {
                    vertices[i] = new Vector3(vertices[i].x, edgeHeight, vertices[i].z);
                }
                mesh.vertices = vertices;

                ++edge.fixedEdgePointCount;
                if (mFixedEdgeMeshies.Contains(edge) == false)
                {
                    mFixedEdgeMeshies.Add(edge);
                }
            }
        }

        void ModifyEdgePairMesh(SharedEdgePair pair, bool generateAssets, int lod)
        {
            var edge0 = pair.edgePair[0];
            var edge1 = pair.edgePair[1];
            var vertices0 = edge0.mesh.vertices;
            var vertices1 = edge1.mesh.vertices;
            List<Vector4> uvs0 = new List<Vector4>();
            List<Vector4> uvs1 = new List<Vector4>();
            edge0.mesh.GetUVs(0, uvs0);
            edge1.mesh.GetUVs(0, uvs1);
            //缩短线段宽度
            for (int i = 0; i < vertices0.Length; ++i)
            {
                if (i % 2 == 1)
                {
                    var oldPos = vertices0[i];

                    var d = vertices0[i] - vertices0[i - 1];
                    vertices0[i] = vertices0[i - 1] + d * 0.5f;

                    if (i == 1 || i == vertices0.Length - 1)
                    {
                        //把被修改过的点保存,后续用来修改相同坐标的顶点
                        mModifiedEndPoints.Add(new ModifiedEndPointVertex(oldPos, vertices0[i]));
                    }
                }
            }
            for (int i = 0; i < vertices1.Length; ++i)
            {
                if (i % 2 == 1)
                {
                    var oldPos = vertices1[i];

                    var d = vertices1[i] - vertices1[i - 1];
                    vertices1[i] = vertices1[i - 1] + d * 0.5f;

                    if (i == 1 || i == vertices1.Length - 1)
                    {
                        //把被修改过的点保存,后续用来修改相同坐标的顶点
                        mModifiedEndPoints.Add(new ModifiedEndPointVertex(oldPos, vertices1[i]));
                    }
                }
            }

            List<Vector3> allVertices = new List<Vector3>();
            List<Vector4> allUVs = new List<Vector4>();
            List<int> allIndices = new List<int>();
            allUVs.AddRange(uvs0);
            allUVs.AddRange(uvs1);
            allVertices.AddRange(vertices0);
            allVertices.AddRange(vertices1);
            allIndices.AddRange(edge0.mesh.triangles);
            var indices1 = edge1.mesh.triangles;
            foreach (var idx in indices1)
            {
                allIndices.Add(idx + vertices0.Length);
            }

            List<Vector3> combinedVertices = new List<Vector3>();
            List<Vector4> combinedUVs = new List<Vector4>();
            List<int> combinedIndices = new List<int>();
            Combine(allVertices, allUVs, allIndices, vertices0.Length, combinedVertices, combinedUVs, combinedIndices);

            Mesh mesh = new Mesh();

            float edgeHeight = mInput.settings.edgeHeight;
            for (int i = 0; i < combinedVertices.Count; ++i)
            {
                combinedVertices[i] = new Vector3(combinedVertices[i].x, edgeHeight, combinedVertices[i].z);
            }

            mesh.SetVertices(combinedVertices);
            mesh.triangles = combinedIndices.ToArray();
            mesh.SetUVs(0, combinedUVs);

            var t0 = GetTerritory(edge0.selfTerritoryID);
            var t1 = GetTerritory(edge1.selfTerritoryID);
            var sharedEdge0 = t0.GetSharedEdge(edge0.selfTerritoryID, edge0.neighbourTerritoryID);
            var sharedEdge1 = t1.GetSharedEdge(edge1.selfTerritoryID, edge1.neighbourTerritoryID);

            sharedEdge0.gameObject.GetComponent<MeshFilter>().sharedMesh = mesh;
            //将另外一条边的mesh设置为null
            //if (lod == 1)
            {
                sharedEdge1.gameObject.GetComponent<MeshFilter>().sharedMesh = null;
            }

            if (generateAssets)
            {
                //create edge mesh
                var edgeInfo = pair.edgePair[0];
                var localMesh = CreateLocalMesh(mesh, edgeInfo.selfTerritoryID);
                AssetDatabase.CreateAsset(localMesh, edgeInfo.originalMeshPath);

                //Debug.LogError($"create asset in ModifyEdgePairMesh {edgeInfo.originalMeshPath}");

                string name = Utils.GetPathName(edgeInfo.originalPrefabPath, false);
                var edgeObj = new GameObject(name);
                edgeObj.transform.position = new Vector3(0, 0, 0);
                var renderer = edgeObj.AddComponent<MeshRenderer>();
                var filter = edgeObj.AddComponent<MeshFilter>();
                filter.sharedMesh = localMesh;
                renderer.sharedMaterial = mInput.settings.edgeMaterial;
                PrefabUtility.SaveAsPrefabAssetAndConnect(edgeObj, edgeInfo.originalPrefabPath, InteractionMode.AutomatedAction);
                Utils.DestroyObject(edgeObj);
            }
        }

        void Combine(List<Vector3> vertices, List<Vector4> uvs, List<int> indices, int splitIndex, List<Vector3> combinedVertices, List<Vector4> combinedUVs, List<int> combinedIndices)
        {
            for (int i = 0; i < indices.Count; ++i)
            {
                var idx = indices[i];
                var pos = vertices[idx];
                int p = combinedVertices.IndexOf(pos);
                if (p == -1)
                {
                    combinedVertices.Add(pos);
                    int originalVertexIndex = vertices.IndexOf(pos);
                    if (originalVertexIndex > splitIndex)
                    {
                        originalVertexIndex -= splitIndex;
                        originalVertexIndex = vertices.Count - splitIndex - 1 - originalVertexIndex;
                    }
                    Vector4 originalUV = uvs[originalVertexIndex];
                    if (idx < splitIndex)
                    {
                        if (idx % 2 == 0)
                        {
                            combinedUVs.Add(new Vector4(originalUV.x, 0.5f, originalUV.z, originalUV.w));
                        }
                        else
                        {
                            combinedUVs.Add(new Vector4(originalUV.x, 0, originalUV.z, originalUV.w));
                        }
                    }
                    else
                    {
                        combinedUVs.Add(new Vector4(originalUV.x, 1, originalUV.z, originalUV.w));
                    }

                    p = combinedVertices.Count - 1;
                }
                combinedIndices.Add(p);
            }
        }

        SharedEdgePair FindEdgePair(List<SharedEdgePair> edgePairs, SharedEdgeWithNeighbourTerritroy edge)
        {
            foreach (var pair in edgePairs)
            {
                foreach (var edgePair in pair.edgePair)
                {
                    if ((edgePair.selfTerritoryID == edge.selfRegionID && edgePair.neighbourTerritoryID == edge.neighbourRegionID) ||
                        (edgePair.selfTerritoryID == edge.neighbourRegionID && edgePair.neighbourTerritoryID == edge.selfRegionID))
                    {
                        return pair;
                    }
                }
            }
            return null;
        }

        List<ModifiedEndPointVertex> mModifiedEndPoints = new List<ModifiedEndPointVertex>();
        List<SharedEdgeWithNeighbourTerritroy> mFixedEdgeMeshies = new List<SharedEdgeWithNeighbourTerritroy>();
    }
}

#endif