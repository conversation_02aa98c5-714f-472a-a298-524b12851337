﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.GridModelLayerData LoadGridModelLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.GridModelLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();

            var objects = new config.GridMapObjectData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadGridModelDataV1(reader);
                }
            }

            var config = LoadGridModelLayerLODConfigV1(reader);
            //-------------------version 1 end------------------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadGridModelLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layer = new config.GridModelLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, (GridType)gridType, objects);
            return layer;
        }

        config.GridModelData LoadGridModelDataV1(BinaryReader reader)
        {
            bool hasTile = reader.ReadBoolean();
            if (!hasTile)
            {
                return null;
            }
            var id = reader.ReadInt32();
            var gridModelData = new config.GridModelData();

            gridModelData.SetID(id);
            gridModelData.position = Utils.ReadVector3(reader);
            var isDefaultRotation = reader.ReadBoolean();
            if (!isDefaultRotation)
            {
                gridModelData.rotation = Utils.ReadQuaternion(reader);
            }
            var isDefaultScale = reader.ReadBoolean();
            if (!isDefaultScale)
            {
                gridModelData.scale = Utils.ReadVector3(reader);
            }

            gridModelData.isDefaultPosition = reader.ReadBoolean();
            gridModelData.modelTemplateID = reader.ReadInt32();
            return gridModelData;
        }

        config.MapLayerLODConfig LoadGridModelLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadGridModelLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
