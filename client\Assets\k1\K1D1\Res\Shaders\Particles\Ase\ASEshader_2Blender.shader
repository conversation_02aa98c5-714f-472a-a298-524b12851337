// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "ASEshader/2blender"
{
	Properties
	{
		[Enum(UnityEngine.Rendering.BlendMode)]_Src("Src", Int) = 5
		[Enum(UnityEngine.Rendering.BlendMode)]_Dst("Dst", Int) = 10
		[Toggle(_WHETHERPREMULTIPLY_ON)] _WhetherPremultiply("WhetherPremultiply", Float) = 0
		[Enum(UnityEngine.Rendering.CullMode)]_Cull_Mode("Cull_Mode", Int) = 0
		[HDR]_GlobalColor("GlobalColor", Color) = (1,1,1,1)
		_MainTex("MainTex", 2D) = "white" {}
		[Enum(Default,0,Random,1)]_RandomOffsetUV3u("RandomOffset(UV3u)", Int) = 0
		_MainTexMask("MainTexMask", 2D) = "white" {}
		[HDR]_MainColor("MainColor", Color) = (1,1,1,1)
		[Enum(Default,0,Custom,1)]_MianOffsetModeCustom("MianOffsetModeCustom", Int) = 0
		_MainOffsetx("MainOffset(x)", Range( -1 , 1)) = 0
		_MainOffsety("MainOffset(y)", Range( -1 , 1)) = 0
		[Toggle(_MAINTEXDEFORMATIONKEY_ON)] _MainTexDeformationKey("MainTexDeformationKey", Float) = 0
		_MainTexNoise("MainTexNoise", 2D) = "white" {}
		[Toggle(_MAINTEXNOISECIRCULAR_ON)] _MainTexNoiseCircular("MainTexNoiseCircular", Float) = 0
		_MainTexDeformationStr("MainTexDeformationStr", Range( 0 , 1)) = 0
		_Tex2DeformationStr("Tex2DeformationStr", Range( 0 , 1)) = 0
		[Toggle(_TEX2KEY_ON)] _Tex2Key("Tex2Key", Float) = 0
		[HDR]_Tex2Color("Tex2Color", Color) = (1,1,1,1)
		_Tex2("Tex2", 2D) = "white" {}
		_Tex2Mask("Tex2Mask", 2D) = "white" {}
		_Tex2MaskScale("Tex2MaskScale", Float) = 0
		[Toggle(_TEX2CIRCULAR_ON)] _Tex2Circular("Tex2Circular", Float) = 0
		[Toggle(_DISSOLVEKEY_ON)] _DissolveKey("DissolveKey", Float) = 0
		[Enum(Default,0,Custom,1)]_DissolveModeCustom("DissolveModeCustom", Int) = 0
		[Toggle(_DOUBLEDECKDISSOLVE_ON)] _DoubleDeckDissolve("DoubleDeckDissolve", Float) = 0
		[HDR]_DissolveEdgeColor("DissolveEdgeColor", Color) = (0,1,0.02469373,1)
		_Dissolve("Dissolve", Range( 0 , 1)) = 0
		_EdgeWidth("EdgeWidth", Range( 0 , 1)) = 0.1836489
		_DissolveHard("DissolveHard", Range( 0 , 0.99)) = 0.5650588
		[Enum(Default,0,Random,1)]_DissolveRandomOffsetUV3w("DissolveRandomOffset(UV3w)", Int) = 0
		[Toggle(_DISSOLVETEXCIRCULAR_ON)] _DissolveTexCircular("DissolveTexCircular", Float) = 0
		_DissolveTex("DissolveTex", 2D) = "white" {}
		_Dissolve_DIR("Dissolve_DIR", 2D) = "white" {}
		[Enum(Off,0,On,1)]_DirReplaceUV2t("DirReplace(UV2t)", Int) = 0
		_DIRWeight("DIRWeight", Range( 0 , 1)) = 1
		[Toggle(_DIRFLIPUV3V_ON)] _DirFlipUV3v("DirFlip(UV3v)", Float) = 0
		_DissolveTexNoise("DissolveTexNoise", 2D) = "white" {}
		[Toggle(_DISSOLVETEXNOISECIRCULAR_ON)] _DissolveTexNoiseCircular("DissolveTexNoiseCircular", Float) = 0
		_NoiseDeformationStr("NoiseDeformationStr", Range( 0 , 1)) = 0
		[Toggle(_FRESNELKEY_ON)] _FresnelKey("FresnelKey", Float) = 0
		[Enum(Fresnel,0,FresnelOneMinus,1)]_FresnelOneMinus("FresnelOneMinus", Int) = 1
		_Fresnel_BiasScalePower("Fresnel_Bias.Scale.Power", Vector) = (0,1,0.2,0)
		[Toggle(_SOFTPARTICLEKEY_ON)] _SoftParticleKey("SoftParticleKey", Float) = 0
		_SoftStr("SoftStr", Range( 0.1 , 1)) = 0.1
		[Toggle(_FACECOLORKEY_ON)] _FaceColorKey("FaceColorKey", Float) = 0
		[HDR]_FaceColor("FaceColor", Color) = (1,1,1,1)
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}
	
	SubShader
	{
		
		
		Tags { "RenderType"="Transparent" "Queue"="Transparent" }
	LOD 100

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend [_Src] [_Dst], [_Src] [_Dst]
		AlphaToMask Off
		Cull [_Cull_Mode]
		ColorMask RGBA
		ZWrite Off
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"
			Tags {  }
			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#define ASE_NEEDS_FRAG_WORLD_POSITION
			#define ASE_NEEDS_FRAG_COLOR
			#pragma shader_feature_local _FACECOLORKEY_ON
			#pragma shader_feature_local _DOUBLEDECKDISSOLVE_ON
			#pragma  multi_compile __  _TEX2KEY_ON
			#pragma shader_feature_local _MAINTEXDEFORMATIONKEY_ON
			#pragma shader_feature_local _MAINTEXNOISECIRCULAR_ON
			#pragma shader_feature_local _TEX2CIRCULAR_ON
			#pragma  multi_compile __  _DISSOLVEKEY_ON
			#pragma shader_feature_local _DISSOLVETEXCIRCULAR_ON
			#pragma shader_feature_local _DISSOLVETEXNOISECIRCULAR_ON
			#pragma shader_feature_local _DIRFLIPUV3V_ON
			#pragma shader_feature_local _SOFTPARTICLEKEY_ON
			#pragma shader_feature_local _FRESNELKEY_ON
			#pragma shader_feature_local _WHETHERPREMULTIPLY_ON


			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				half3 ase_normal : NORMAL;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				float4 ase_texcoord3 : TEXCOORD3;
				float4 ase_color : COLOR;
				float4 ase_texcoord4 : TEXCOORD4;
				float4 ase_texcoord5 : TEXCOORD5;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			uniform int _Dst;
			uniform int _Src;
			uniform int _Cull_Mode;
			uniform sampler2D _MainTex;
			uniform half4 _MainTex_ST;
			uniform half _MainOffsetx;
			uniform half _MainOffsety;
			uniform int _MianOffsetModeCustom;
			uniform int _RandomOffsetUV3u;
			uniform sampler2D _MainTexNoise;
			uniform half4 _MainTexNoise_ST;
			uniform half _MainTexDeformationStr;
			uniform half4 _MainColor;
			uniform sampler2D _MainTexMask;
			uniform half4 _MainTexMask_ST;
			uniform half4 _Tex2Color;
			uniform sampler2D _Tex2;
			uniform half4 _Tex2_ST;
			uniform half _Tex2DeformationStr;
			uniform sampler2D _Tex2Mask;
			uniform half _Tex2MaskScale;
			uniform half4 _DissolveEdgeColor;
			uniform sampler2D _DissolveTex;
			uniform half4 _DissolveTex_ST;
			uniform int _DissolveRandomOffsetUV3w;
			uniform sampler2D _DissolveTexNoise;
			uniform half4 _DissolveTexNoise_ST;
			uniform half _NoiseDeformationStr;
			uniform sampler2D _Dissolve_DIR;
			uniform half4 _Dissolve_DIR_ST;
			uniform half _DIRWeight;
			uniform int _DirReplaceUV2t;
			uniform half _Dissolve;
			uniform int _DissolveModeCustom;
			uniform half _EdgeWidth;
			uniform half _DissolveHard;
			uniform half4 _GlobalColor;
			UNITY_DECLARE_DEPTH_TEXTURE( _CameraDepthTexture );
			uniform float4 _CameraDepthTexture_TexelSize;
			uniform half _SoftStr;
			uniform half4 _Fresnel_BiasScalePower;
			uniform int _FresnelOneMinus;
			uniform half4 _FaceColor;

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float4 ase_clipPos = UnityObjectToClipPos(v.vertex);
				float4 screenPos = ComputeScreenPos(ase_clipPos);
				o.ase_texcoord4 = screenPos;
				half3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
				o.ase_texcoord5.xyz = ase_worldNormal;
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				o.ase_texcoord2 = v.ase_texcoord1;
				o.ase_texcoord3 = v.ase_texcoord2;
				o.ase_color = v.color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				o.ase_texcoord5.w = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = vertexValue;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i , half ase_vface : VFACE) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				half2 texCoord52 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half2 appendResult59 = (half2(_MainOffsetx , _MainOffsety));
				half2 appendResult58 = (half2(i.ase_texcoord2.y , i.ase_texcoord2.z));
				half2 UV2vw267 = appendResult58;
				half2 lerpResult60 = lerp( appendResult59 , UV2vw267 , (float)_MianOffsetModeCustom);
				half2 appendResult357 = (half2(i.ase_texcoord3.x , 0.0));
				half2 lerpResult359 = lerp( float2( 0,0 ) , appendResult357 , (float)_RandomOffsetUV3u);
				half2 UV3u355 = lerpResult359;
				half2 temp_output_181_0 = ( ( texCoord52 + ( _MainTex_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + lerpResult60 + UV3u355 ) * _MainTex_ST.xy );
				half2 texCoord97 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half2 texCoord318 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half2 temp_output_319_0 = (texCoord318*2.0 + -1.0);
				half2 break320 = temp_output_319_0;
				half2 appendResult326 = (half2(length( temp_output_319_0 ) , ( ( atan2( break320.y , break320.x ) / ( 2.0 * UNITY_PI ) ) + 0.5 )));
				half2 CircularUV419 = appendResult326;
				#ifdef _MAINTEXNOISECIRCULAR_ON
				half2 staticSwitch101 = CircularUV419;
				#else
				half2 staticSwitch101 = texCoord97;
				#endif
				half temp_output_107_0 = (tex2D( _MainTexNoise, ( ( staticSwitch101 + ( _MainTexNoise_ST.zw * float2( 0.1,0.1 ) * _Time.y ) ) * _MainTexNoise_ST.xy ) ).r*2.0 + -1.0);
				#ifdef _MAINTEXDEFORMATIONKEY_ON
				half2 staticSwitch61 = ( temp_output_181_0 + ( temp_output_107_0 * 0.2 * _MainTexDeformationStr ) );
				#else
				half2 staticSwitch61 = temp_output_181_0;
				#endif
				half4 tex2DNode64 = tex2D( _MainTex, staticSwitch61 );
				float2 uv_MainTexMask = i.ase_texcoord1.xy * _MainTexMask_ST.xy + _MainTexMask_ST.zw;
				half4 tex2DNode273 = tex2D( _MainTexMask, uv_MainTexMask );
				half4 temp_output_177_0 = ( tex2DNode64 * _MainColor * tex2DNode273.r );
				half2 uv_Tex2 = i.ase_texcoord1.xy * _Tex2_ST.xy + _Tex2_ST.zw;
				#ifdef _TEX2CIRCULAR_ON
				half2 staticSwitch327 = CircularUV419;
				#else
				half2 staticSwitch327 = uv_Tex2;
				#endif
				half2 temp_output_315_0 = ( ( staticSwitch327 + ( _Tex2_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + UV3u355 ) * _Tex2_ST.xy );
				half MainTexDeformation336 = temp_output_107_0;
				#ifdef _MAINTEXDEFORMATIONKEY_ON
				half2 staticSwitch335 = ( temp_output_315_0 + ( MainTexDeformation336 * 0.2 * _Tex2DeformationStr ) );
				#else
				half2 staticSwitch335 = temp_output_315_0;
				#endif
				half temp_output_382_0 = ( tex2DNode273.r * tex2DNode64.a );
				half2 texCoord373 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half4 Tex2328 = ( _Tex2Color * tex2D( _Tex2, staticSwitch335 ) * temp_output_382_0 * tex2D( _Tex2Mask, (texCoord373*( 1.0 + _Tex2MaskScale ) + ( 0.0 - ( _Tex2MaskScale * 0.5 ) )) ).r );
				#ifdef _TEX2KEY_ON
				half4 staticSwitch331 = ( temp_output_177_0 + Tex2328 );
				#else
				half4 staticSwitch331 = temp_output_177_0;
				#endif
				half2 texCoord140 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				#ifdef _DISSOLVETEXCIRCULAR_ON
				half2 staticSwitch418 = CircularUV419;
				#else
				half2 staticSwitch418 = texCoord140;
				#endif
				half2 appendResult416 = (half2(i.ase_texcoord3.z , 0.0));
				half2 lerpResult413 = lerp( float2( 0,0 ) , appendResult416 , (float)_DissolveRandomOffsetUV3w);
				half2 VU3w412 = lerpResult413;
				half2 texCoord124 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				#ifdef _DISSOLVETEXNOISECIRCULAR_ON
				half2 staticSwitch128 = CircularUV419;
				#else
				half2 staticSwitch128 = texCoord124;
				#endif
				half2 texCoord409 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half4 tex2DNode150 = tex2D( _Dissolve_DIR, ( ( texCoord409 + _Dissolve_DIR_ST.zw ) * _Dissolve_DIR_ST.xy ) );
				half VU3v391 = i.ase_texcoord3.y;
				half lerpResult406 = lerp( tex2DNode150.r , ( 1.0 - tex2DNode150.r ) , VU3v391);
				#ifdef _DIRFLIPUV3V_ON
				half staticSwitch405 = lerpResult406;
				#else
				half staticSwitch405 = tex2DNode150.r;
				#endif
				half UV2t342 = i.ase_texcoord2.w;
				half lerpResult343 = lerp( _DIRWeight , UV2t342 , (float)_DirReplaceUV2t);
				half lerpResult297 = lerp( tex2D( _DissolveTex, ( ( ( staticSwitch418 + ( _DissolveTex_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + VU3w412 ) * _DissolveTex_ST.xy ) + ( (tex2D( _DissolveTexNoise, ( ( staticSwitch128 + ( _DissolveTexNoise_ST.zw * float2( 0.1,0.1 ) * _Time.y ) ) * _DissolveTexNoise_ST.xy ) ).r*2.0 + -1.0) * 0.2 * _NoiseDeformationStr ) ) ).r , staticSwitch405 , lerpResult343);
				half temp_output_159_0 = ( lerpResult297 + 1.0 );
				half UV2u78 = i.ase_texcoord2.x;
				half lerpResult270 = lerp( _Dissolve , UV2u78 , (float)_DissolveModeCustom);
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half staticSwitch254 = ( lerpResult270 * _EdgeWidth );
				#else
				half staticSwitch254 = 0.0;
				#endif
				half temp_output_230_0 = ( lerpResult270 + staticSwitch254 );
				half temp_output_2_0_g5 = _DissolveHard;
				#ifdef _DISSOLVEKEY_ON
				half staticSwitch163 = saturate( ( ( ( temp_output_159_0 - ( temp_output_230_0 * ( 2.0 - _DissolveHard ) ) ) - temp_output_2_0_g5 ) / ( 1.0 - temp_output_2_0_g5 ) ) );
				#else
				half staticSwitch163 = (float)1;
				#endif
				half DissolveRGB232 = staticSwitch163;
				half4 lerpResult234 = lerp( ( _DissolveEdgeColor * temp_output_382_0 ) , staticSwitch331 , DissolveRGB232);
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half4 staticSwitch251 = lerpResult234;
				#else
				half4 staticSwitch251 = staticSwitch331;
				#endif
				float4 screenPos = i.ase_texcoord4;
				half4 ase_screenPosNorm = screenPos / screenPos.w;
				ase_screenPosNorm.z = ( UNITY_NEAR_CLIP_VALUE >= 0 ) ? ase_screenPosNorm.z : ase_screenPosNorm.z * 0.5 + 0.5;
				float screenDepth168 = LinearEyeDepth(SAMPLE_DEPTH_TEXTURE( _CameraDepthTexture, ase_screenPosNorm.xy ));
				half distanceDepth168 = saturate( abs( ( screenDepth168 - LinearEyeDepth( ase_screenPosNorm.z ) ) / ( _SoftStr ) ) );
				#ifdef _SOFTPARTICLEKEY_ON
				half staticSwitch171 = distanceDepth168;
				#else
				half staticSwitch171 = 1.0;
				#endif
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				half3 ase_worldNormal = i.ase_texcoord5.xyz;
				half fresnelNdotV187 = dot( ase_worldNormal, ase_worldViewDir );
				half fresnelNode187 = ( _Fresnel_BiasScalePower.x + _Fresnel_BiasScalePower.y * pow( max( 1.0 - fresnelNdotV187 , 0.0001 ), _Fresnel_BiasScalePower.z ) );
				half lerpResult199 = lerp( fresnelNode187 , ( 1.0 - fresnelNode187 ) , (float)_FresnelOneMinus);
				half temp_output_203_0 = saturate( lerpResult199 );
				#ifdef _WHETHERPREMULTIPLY_ON
				half staticSwitch240 = temp_output_203_0;
				#else
				half staticSwitch240 = 1.0;
				#endif
				#ifdef _FRESNELKEY_ON
				half staticSwitch207 = staticSwitch240;
				#else
				half staticSwitch207 = 1.0;
				#endif
				half4 Global_Vertex_Soft_FresnelRGB174 = ( _GlobalColor * i.ase_color * staticSwitch171 * staticSwitch207 );
				half temp_output_2_0_g4 = _DissolveHard;
				#ifdef _DISSOLVEKEY_ON
				half staticSwitch243 = saturate( ( ( ( temp_output_159_0 - ( ( temp_output_230_0 - _EdgeWidth ) * ( 2.0 - _DissolveHard ) ) ) - temp_output_2_0_g4 ) / ( 1.0 - temp_output_2_0_g4 ) ) );
				#else
				half staticSwitch243 = 1.0;
				#endif
				half DissolveA233 = staticSwitch243;
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half staticSwitch252 = DissolveA233;
				#else
				half staticSwitch252 = DissolveRGB232;
				#endif
				#ifdef _WHETHERPREMULTIPLY_ON
				half staticSwitch246 = staticSwitch252;
				#else
				half staticSwitch246 = 1.0;
				#endif
				half4 temp_output_239_0 = ( staticSwitch251 * Global_Vertex_Soft_FresnelRGB174 * staticSwitch246 );
				half4 lerpResult396 = lerp( ( _FaceColor * temp_output_239_0 ) , temp_output_239_0 , (ase_vface*0.5 + 0.5));
				#ifdef _FACECOLORKEY_ON
				half4 staticSwitch400 = lerpResult396;
				#else
				half4 staticSwitch400 = temp_output_239_0;
				#endif
				#ifdef _FRESNELKEY_ON
				half staticSwitch205 = temp_output_203_0;
				#else
				half staticSwitch205 = 1.0;
				#endif
				half Global_Vertex_Soft_FresnelA176 = ( _GlobalColor.a * i.ase_color.a * staticSwitch171 * staticSwitch205 );
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half staticSwitch249 = DissolveA233;
				#else
				half staticSwitch249 = DissolveRGB232;
				#endif
				half MainAlpha361 = ( tex2DNode64.a * _MainColor.a * tex2DNode273.r * Global_Vertex_Soft_FresnelA176 * staticSwitch249 );
				half4 appendResult432 = (half4(staticSwitch400.rgb , MainAlpha361));
				
				
				finalColor = appendResult432;
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=18800
1604.002;-552.8472;2065;1176;-2594.336;1567.42;1.653248;True;True
Node;AmplifyShaderEditor.TextureCoordinatesNode;318;-3255.166,-1990.62;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ScaleAndOffsetNode;319;-3011.694,-1989.59;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;2;False;2;FLOAT;-1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.BreakToComponentsNode;320;-3085.475,-1837.281;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.PiNode;321;-3304.711,-1823.787;Inherit;False;1;0;FLOAT;2;False;1;FLOAT;0
Node;AmplifyShaderEditor.ATan2OpNode;322;-2930.479,-1843.281;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleDivideOpNode;323;-2810.939,-1841.583;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;325;-2692.286,-1838.075;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0
Node;AmplifyShaderEditor.LengthOpNode;324;-2822.588,-1990.566;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;326;-2687.328,-1989.935;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureTransformNode;93;-2586.183,-635.8453;Inherit;False;105;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.RegisterLocalVarNode;419;-2486.277,-1948.988;Inherit;False;CircularUV;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;421;-2545.117,-912.1453;Inherit;False;419;CircularUV;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleTimeNode;98;-2423.898,-521.5385;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;97;-2556.492,-817.2379;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureTransformNode;120;-2434.974,1949.773;Inherit;False;137;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.WireNode;95;-1859.409,-575.2285;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TexCoordVertexDataNode;53;2848.699,-694.2547;Inherit;False;1;4;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;99;-2223.9,-572.5383;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;124;-2454.689,1811.082;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TexCoordVertexDataNode;354;2396.679,-396.9228;Inherit;False;2;4;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;100;-1840.407,-577.2285;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.StaticSwitch;101;-2305.399,-815.1487;Inherit;False;Property;_MainTexNoiseCircular;MainTexNoiseCircular;14;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;422;-2389.911,1653.692;Inherit;False;419;CircularUV;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleTimeNode;123;-2359.689,2055.081;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;126;-1792.192,2008.389;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;130;-1773.192,2006.389;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;103;-1836.407,-608.2285;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;358;2801.851,-333.0244;Inherit;False;Property;_RandomOffsetUV3u;RandomOffset(UV3u);6;1;[Enum];Create;True;0;2;Default;0;Random;1;0;False;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.IntNode;414;2782.971,-20.83167;Inherit;False;Property;_DissolveRandomOffsetUV3w;DissolveRandomOffset(UV3w);30;1;[Enum];Create;True;0;2;Default;0;Random;1;0;False;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;127;-2159.687,2004.08;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;357;2843.566,-436.2617;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;416;2852.971,-125.8317;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;58;3064.605,-668.7717;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.StaticSwitch;128;-2206.526,1806.041;Inherit;False;Property;_DissolveTexNoiseCircular;DissolveTexNoiseCircular;38;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;102;-1986.612,-709.2285;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.LerpOp;359;3011.635,-442.575;Inherit;True;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;132;-1769.192,1975.39;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;104;-1835.41,-707.2285;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureTransformNode;131;-1604.614,1107.718;Inherit;False;149;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.TextureCoordinatesNode;409;-1201.93,634.0751;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;133;-1901.02,1875.535;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;267;3204.974,-673.1053;Inherit;False;UV2vw;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.LerpOp;413;3061.971,-109.8317;Inherit;True;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;55;-707.4211,-940.6192;Inherit;False;Property;_MainOffsetx;MainOffset(x);10;0;Create;True;0;0;0;False;0;False;0;0;-1;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.TextureTransformNode;408;-1191.93,767.0751;Inherit;False;150;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.RangedFloatNode;54;-708.7211,-839.7191;Inherit;False;Property;_MainOffsety;MainOffset(y);11;0;Create;True;0;0;0;False;0;False;0;0;-1;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;412;3331.971,-131.8317;Inherit;False;VU3w;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;140;-1658.398,953.3934;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;134;-1768.192,1876.392;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;410;-928.9297,638.0751;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;78;3059.681,-746.9789;Inherit;False;UV2u;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleTimeNode;184;-607.4111,-1020.572;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;268;-695.3793,-744.0545;Inherit;False;267;UV2vw;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;269;-536.8396,-689.0707;Inherit;False;Property;_MianOffsetModeCustom;MianOffsetModeCustom;9;1;[Enum];Create;True;0;2;Default;0;Custom;1;0;False;0;False;0;1;False;0;1;INT;0
Node;AmplifyShaderEditor.TextureTransformNode;179;-625.0208,-1164.245;Inherit;False;64;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.SimpleTimeNode;135;-1560.399,1227.294;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;105;-1598.888,-636.1155;Inherit;True;Property;_MainTexNoise;MainTexNoise;13;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;136;-971.903,1086.703;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;59;-429.5208,-920.2193;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;423;-1913.861,1109.366;Inherit;False;419;CircularUV;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;355;3291.679,-402.9228;Inherit;False;UV3u;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleTimeNode;312;-1181.204,-1922.894;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;418;-1448.848,996.2156;Inherit;False;Property;_DissolveTexCircular;DissolveTexCircular;31;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;356;-115.0179,-823.9037;Inherit;False;355;UV3u;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;420;-1595.209,-1984.63;Inherit;False;419;CircularUV;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureTransformNode;311;-1239.814,-2050.067;Inherit;False;299;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.ScaleAndOffsetNode;107;-1210.514,-606.5615;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;2;False;2;FLOAT;-1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;182;-390.4112,-1069.572;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;138;-1330.296,1133.094;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;271;-383.4517,1479.279;Inherit;False;78;UV2u;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.IntNode;56;-386.3233,1558.984;Inherit;False;Property;_DissolveModeCustom;DissolveModeCustom;24;1;[Enum];Create;True;0;2;Default;0;Custom;1;0;False;0;False;0;1;False;0;1;INT;0
Node;AmplifyShaderEditor.SamplerNode;137;-1417.965,1455.502;Inherit;True;Property;_DissolveTexNoise;DissolveTexNoise;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.GetLocalVarNode;417;-1348.743,1254.112;Inherit;False;412;VU3w;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;310;-1645.646,-2262.5;Inherit;False;0;299;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;139;-952.9032,1084.703;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;52;-637.6008,-1297.198;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;411;-743.9297,667.0751;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.LerpOp;60;-296.3422,-921.1398;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;152;-387.157,1396.255;Inherit;False;Property;_Dissolve;Dissolve;27;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;141;-1145.904,952.7025;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.ScaleAndOffsetNode;144;-959.5291,1483.106;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;2;False;2;FLOAT;-1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;313;-1000.504,-1989.794;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;106;-1207.843,-356.3837;Inherit;False;Property;_MainTexDeformationStr;MainTexDeformationStr;15;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;142;-1057.151,1634.321;Inherit;False;Property;_NoiseDeformationStr;NoiseDeformationStr;39;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;185;6.88962,-1108.299;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;180;-116.3113,-1292.571;Inherit;False;4;4;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;424;-1364.317,-1869.669;Inherit;False;355;UV3u;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;336;-1023.473,-720.4491;Inherit;False;MainTexDeformation;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;270;-86.45172,1400.279;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;391;2793.718,-245.4273;Inherit;False;VU3v;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;219;-236.0874,2006.97;Inherit;False;Property;_EdgeWidth;EdgeWidth;28;0;Create;True;0;0;0;False;0;False;0.1836489;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;143;-952.9032,1042.703;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;150;-503.6647,615.3708;Inherit;True;Property;_Dissolve_DIR;Dissolve_DIR;33;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StaticSwitch;327;-1262.752,-2166.521;Inherit;False;Property;_Tex2Circular;Tex2Circular;22;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;146;-681.9169,1483.835;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0.2;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;342;3173.785,-570.0485;Inherit;False;UV2t;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;407;-198.6908,810.6513;Inherit;False;391;VU3v;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;181;28.7888,-1289.971;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;314;-997.2368,-2158.949;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;108;-920.9033,-599.8325;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0.2;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;337;-1337.982,-1696.709;Inherit;False;336;MainTexDeformation;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;404;-185.8436,717.2373;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;378;-724.3265,-1551.564;Inherit;False;Property;_Tex2MaskScale;Tex2MaskScale;21;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;340;-1380.385,-1572.055;Inherit;False;Property;_Tex2DeformationStr;Tex2DeformationStr;16;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;145;-947.9042,954.7035;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;255;-64.35309,1551.155;Inherit;False;Constant;_Float6;Float 6;29;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;231;92.08901,1754.779;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;57;213.6931,-635.2742;Inherit;True;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;344;-383.8726,980.9054;Inherit;False;342;UV2t;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;315;-850.7706,-2066.035;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;339;-1019.445,-1664.504;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0.2;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;298;-487.6761,886.4717;Inherit;False;Property;_DIRWeight;DIRWeight;35;0;Create;True;0;0;0;False;0;False;1;0.814;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;380;-520.3265,-1548.564;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;406;-0.879456,675.6874;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;147;-408.1871,1180.881;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.StaticSwitch;254;94.74101,1551.361;Inherit;False;Property;_doubleDeckDissolve;doubleDeckDissolve;25;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;249;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;148;835.7806,1399.295;Inherit;False;Property;_DissolveHard;DissolveHard;29;0;Create;True;0;0;0;False;0;False;0.5650588;0.786;0;0.99;0;1;FLOAT;0
Node;AmplifyShaderEditor.IntNode;341;-413.9983,1069.815;Inherit;False;Property;_DirReplaceUV2t;DirReplace(UV2t);34;1;[Enum];Create;True;0;2;Off;0;On;1;0;False;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;338;-824.7963,-1688.942;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.StaticSwitch;61;384.2081,-1293.323;Inherit;False;Property;_MainTexDeformationKey;MainTexDeformationKey;12;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;226;1010.537,2158.29;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;153;1060.103,1572.616;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;343;-87.45251,976.9067;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;405;173.638,625.0509;Inherit;True;Property;_DirFlipUV3v;DirFlip(UV3v);36;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;149;-122.4718,1178.451;Inherit;True;Property;_DissolveTex;DissolveTex;32;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.Vector4Node;188;3001.074,283.6948;Inherit;False;Property;_Fresnel_BiasScalePower;Fresnel_Bias.Scale.Power;42;0;Create;True;0;0;0;False;0;False;0,1,0.2,0;0,1,0.2,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureCoordinatesNode;373;-578.108,-1864.357;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleSubtractOpNode;379;-418.3265,-1655.564;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;377;-545.3265,-1729.564;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;230;337.768,1409.095;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;220;599.6943,2091.04;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;227;454.1357,1986.839;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;157;616.0941,1507.262;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;297;200.5386,958.5659;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;273;864.2086,-1578.204;Inherit;True;Property;_MainTexMask;MainTexMask;7;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;64;883.4479,-1311.84;Inherit;True;Property;_MainTex;MainTex;5;0;Create;True;0;0;0;False;0;False;-1;None;6bf708452a7b62e45b49c94b3b48e22f;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StaticSwitch;335;-696.6119,-2070.196;Inherit;False;Property;_MainTexDeformationKey;MainTexDeformationKey;12;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;61;True;True;9;1;FLOAT2;0,0;False;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.FresnelNode;187;2994.811,466.2177;Inherit;True;Standard;WorldNormal;ViewDir;True;True;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;1.32;False;3;FLOAT;1.17;False;1;FLOAT;0
Node;AmplifyShaderEditor.ScaleAndOffsetNode;376;-313.3265,-1835.564;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;159;545.3705,1122.293;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;200;3306.172,526.7737;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;299;-392.7086,-2093.411;Inherit;True;Property;_Tex2;Tex2;19;0;Create;True;0;0;0;False;0;False;-1;None;673581e192d31184cbe0f58d30bd1130;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;366;-117.7187,-1810.614;Inherit;True;Property;_Tex2Mask;Tex2Mask;20;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;382;1282.335,-1608.299;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;218;604.1165,1987.219;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;2;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;158;616.8944,1406.455;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;2;False;1;FLOAT;0
Node;AmplifyShaderEditor.IntNode;201;3289.289,616.1337;Inherit;False;Property;_FresnelOneMinus;FresnelOneMinus;41;1;[Enum];Create;True;0;2;Fresnel;0;FresnelOneMinus;1;0;False;0;False;1;1;False;0;1;INT;0
Node;AmplifyShaderEditor.ColorNode;306;-310.7693,-2275.631;Inherit;False;Property;_Tex2Color;Tex2Color;18;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1.391894,1.273624,0.2429249,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;308;-63.41692,-2167.057;Inherit;True;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;160;817.955,1118.494;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;199;3496.503,464.4487;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;215;811.6657,1724.328;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;167;3814.154,84.54605;Inherit;False;Property;_SoftStr;SoftStr;44;0;Create;True;0;0;0;False;0;False;0.1;0.1;0.1;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;244;1536.815,1696.824;Inherit;False;Constant;_Float4;Float 4;26;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;83;933.8024,-1058.478;Inherit;False;Property;_MainColor;MainColor;8;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.FunctionNode;435;1247.91,1728.82;Inherit;False;SimpleSmoothStep;-1;;4;f13a47e064352a24f9030330da831405;0;3;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;434;1250.788,1168.871;Inherit;False;SimpleSmoothStep;-1;;5;f13a47e064352a24f9030330da831405;0;3;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;328;209.7318,-2171.91;Inherit;True;Tex2;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SaturateNode;203;3734.995,543.0952;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;208;3729.347,365.5619;Inherit;False;Constant;_Float2;Float 2;23;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.IntNode;161;1439.376,1007.553;Inherit;False;Constant;_Int2;Int 2;53;0;Create;True;0;0;0;False;0;False;1;0;False;0;1;INT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;177;1279.946,-1307.756;Inherit;True;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;242;4007.667,288.3079;Inherit;False;Constant;_Float3;Float 3;26;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;243;1683.186,1697.92;Inherit;True;Property;_DissolveKey;DissolveKey;23;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;163;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;169;3939.208,-87.1311;Inherit;False;Constant;_Float1;Float 1;49;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.DepthFade;168;3833.554,-11.25379;Inherit;False;True;True;True;2;1;FLOAT3;0,0,0;False;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;240;3880.491,366.4838;Inherit;False;Property;_WhetherPremultiply;WhetherPremultiply;2;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;329;1286.508,-1078.541;Inherit;True;328;Tex2;1;0;OBJECT;;False;1;COLOR;0
Node;AmplifyShaderEditor.StaticSwitch;163;1587.945,1084.013;Inherit;True;Property;_DissolveKey;DissolveKey;23;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;233;2008.79,1696.988;Inherit;False;DissolveA;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;170;4117.602,-341.3212;Inherit;False;Property;_GlobalColor;GlobalColor;4;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StaticSwitch;171;4093.355,56.09325;Inherit;False;Property;_SoftParticleKey;SoftParticleKey;43;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;236;1495.262,-1759.918;Inherit;False;Property;_DissolveEdgeColor;DissolveEdgeColor;26;1;[HDR];Create;True;0;0;0;False;0;False;0,1,0.02469373,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RegisterLocalVarNode;232;1947.526,1084.945;Inherit;False;DissolveRGB;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;332;1539.189,-1224.872;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.StaticSwitch;207;4168.482,289.7783;Inherit;False;Property;_Keyword0;Keyword 0;40;0;Create;True;0;0;0;False;0;False;0;0;0;True;;KeywordEnum;3;Nothing;Fresnel;PremultiplyFresnel;Reference;205;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.VertexColorNode;172;4127.956,-112.145;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;206;4019.854,643.9973;Inherit;False;Constant;_Float0;Float 0;23;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;237;1588.342,-1421.892;Inherit;False;232;DissolveRGB;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;253;1857.934,-999.111;Inherit;False;232;DissolveRGB;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;205;4159.664,514.2355;Inherit;False;Property;_FresnelKey;FresnelKey;40;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Nothing;Fresnel;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;331;1755.99,-1307.268;Inherit;True;Property;_Tex2Key;Tex2Key;17;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.GetLocalVarNode;247;1862.222,-909.0518;Inherit;False;233;DissolveA;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;173;4463.443,-336.1334;Inherit;False;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;402;1763.256,-1600.648;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.LerpOp;234;1992.54,-1528.622;Inherit;True;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;175;4459.172,-39.91305;Inherit;True;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;248;2175.932,-1087.065;Inherit;False;Constant;_Float5;Float 5;26;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;174;4821.16,-348.3102;Inherit;True;Global_Vertex_Soft_FresnelRGB;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.StaticSwitch;252;2058.935,-978.1107;Inherit;False;Property;_doubleDeckDissolve;doubleDeckDissolve;25;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;249;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;250;1789.413,-527.7639;Inherit;False;232;DissolveRGB;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;176;4824.166,-46.27351;Inherit;True;Global_Vertex_Soft_FresnelA;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;246;2382.777,-1085.303;Inherit;False;Property;_WhetherPremultiply;WhetherPremultiply;2;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Reference;240;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;238;1804.799,-431.262;Inherit;False;233;DissolveA;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;251;2263.294,-1308.042;Inherit;False;Property;_doubleDeckDissolve;doubleDeckDissolve;25;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;249;True;True;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;334;1235.466,-689.6541;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;211;2249.697,-1201.323;Inherit;False;174;Global_Vertex_Soft_FresnelRGB;1;0;OBJECT;;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;259;1197.19,-649.125;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;284;1176.878,-616.4934;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;256;1213.104,-647.7987;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;397;2261.705,-2228.361;Inherit;False;Property;_FaceColor;FaceColor;46;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StaticSwitch;249;2012.864,-492.5089;Inherit;False;Property;_DoubleDeckDissolve;DoubleDeckDissolve;25;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.FaceVariableNode;392;2257.208,-1930.79;Inherit;False;0;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;212;2002.059,-785.9655;Inherit;False;176;Global_Vertex_Soft_FresnelA;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;333;1271.784,-679.2791;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;239;2683.167,-1302.494;Inherit;True;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;283;1197.598,-616.3632;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;178;2304.112,-711.0012;Inherit;True;5;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;399;2666.352,-2051.132;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ScaleAndOffsetNode;395;2406.088,-1930.52;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0.5;False;2;FLOAT;0.5;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;396;2891.289,-1841.271;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;361;2541.786,-717.1274;Inherit;True;MainAlpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;400;3205.432,-1370.131;Inherit;False;Property;_FaceColorKey;FaceColorKey;45;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.GetLocalVarNode;362;3396.214,-1122.145;Inherit;False;361;MainAlpha;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.IntNode;260;2916.924,-936.4203;Inherit;False;Property;_Cull_Mode;Cull_Mode;3;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.CullMode;True;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.DynamicAppendNode;401;2988.391,-1217.391;Inherit;True;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0
Node;AmplifyShaderEditor.DynamicAppendNode;432;3501.899,-1384.339;Inherit;True;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0
Node;AmplifyShaderEditor.IntNode;353;3201.462,-937.5125;Inherit;False;Property;_Dst;Dst;1;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.BlendMode;True;0;False;10;10;False;0;1;INT;0
Node;AmplifyShaderEditor.IntNode;351;3063.996,-935.0515;Inherit;False;Property;_Src;Src;0;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.BlendMode;True;0;False;5;1;False;0;1;INT;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;431;3835.494,-1386.578;Half;False;True;-1;2;ASEMaterialInspector;100;1;ASEshader/2blender;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;True;2;5;True;351;10;True;353;2;5;True;351;10;True;353;True;0;False;-1;0;False;-1;False;False;False;False;False;False;True;0;False;-1;True;0;True;260;True;True;True;True;True;0;False;-1;False;False;False;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;True;2;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;2;RenderType=Transparent=RenderType;Queue=Transparent=Queue=0;True;2;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=;False;0;;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;1;True;False;;False;0
Node;AmplifyShaderEditor.CommentaryNode;166;3789.253,-148.907;Inherit;False;535.9048;309.9022;SoftParticle;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;165;3739.253,-404.1453;Inherit;False;964.4067;619.0135;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;204;2957.762,237.6948;Inherit;False;1750.315;487.1145;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;316;-2335.094,-2356.335;Inherit;False;2770.994;923.3079;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;51;-734.8146,-1392.225;Inherit;False;901.5621;786.3455;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;129;-1672.948,898.3935;Inherit;False;858.4456;434.8156;TilingOffset;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;272;2805.699,-796.9788;Inherit;False;614.2754;327.724;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;118;-2476.874,1761.082;Inherit;False;834.0811;399.1001;TilingOffset;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;50;-2604.784,-925.7396;Inherit;False;905.2213;484.3874;TilingOffset;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;111;-2607.149,566.9886;Inherit;False;4834.545;1936.739;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;48;-2639.335,-1380.517;Inherit;False;1857.405;1129.194;Comment;0;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;317;-3326.523,-2162.433;Inherit;False;798.1465;440.5569;旋转UV;0;;1,1,1,1;0;0
WireConnection;319;0;318;0
WireConnection;320;0;319;0
WireConnection;322;0;320;1
WireConnection;322;1;320;0
WireConnection;323;0;322;0
WireConnection;323;1;321;0
WireConnection;325;0;323;0
WireConnection;324;0;319;0
WireConnection;326;0;324;0
WireConnection;326;1;325;0
WireConnection;419;0;326;0
WireConnection;95;0;93;0
WireConnection;99;0;93;1
WireConnection;99;2;98;0
WireConnection;100;0;95;0
WireConnection;101;1;97;0
WireConnection;101;0;421;0
WireConnection;126;0;120;0
WireConnection;130;0;126;0
WireConnection;103;0;100;0
WireConnection;127;0;120;1
WireConnection;127;2;123;0
WireConnection;357;0;354;1
WireConnection;416;0;354;3
WireConnection;58;0;53;2
WireConnection;58;1;53;3
WireConnection;128;1;124;0
WireConnection;128;0;422;0
WireConnection;102;0;101;0
WireConnection;102;1;99;0
WireConnection;359;1;357;0
WireConnection;359;2;358;0
WireConnection;132;0;130;0
WireConnection;104;0;102;0
WireConnection;104;1;103;0
WireConnection;133;0;128;0
WireConnection;133;1;127;0
WireConnection;267;0;58;0
WireConnection;413;1;416;0
WireConnection;413;2;414;0
WireConnection;412;0;413;0
WireConnection;134;0;133;0
WireConnection;134;1;132;0
WireConnection;410;0;409;0
WireConnection;410;1;408;1
WireConnection;78;0;53;1
WireConnection;105;1;104;0
WireConnection;136;0;131;0
WireConnection;59;0;55;0
WireConnection;59;1;54;0
WireConnection;355;0;359;0
WireConnection;418;1;140;0
WireConnection;418;0;423;0
WireConnection;107;0;105;1
WireConnection;182;0;179;1
WireConnection;182;2;184;0
WireConnection;138;0;131;1
WireConnection;138;2;135;0
WireConnection;137;1;134;0
WireConnection;139;0;136;0
WireConnection;411;0;410;0
WireConnection;411;1;408;0
WireConnection;60;0;59;0
WireConnection;60;1;268;0
WireConnection;60;2;269;0
WireConnection;141;0;418;0
WireConnection;141;1;138;0
WireConnection;141;2;417;0
WireConnection;144;0;137;1
WireConnection;313;0;311;1
WireConnection;313;2;312;0
WireConnection;185;0;179;0
WireConnection;180;0;52;0
WireConnection;180;1;182;0
WireConnection;180;2;60;0
WireConnection;180;3;356;0
WireConnection;336;0;107;0
WireConnection;270;0;152;0
WireConnection;270;1;271;0
WireConnection;270;2;56;0
WireConnection;391;0;354;2
WireConnection;143;0;139;0
WireConnection;150;1;411;0
WireConnection;327;1;310;0
WireConnection;327;0;420;0
WireConnection;146;0;144;0
WireConnection;146;2;142;0
WireConnection;342;0;53;4
WireConnection;181;0;180;0
WireConnection;181;1;185;0
WireConnection;314;0;327;0
WireConnection;314;1;313;0
WireConnection;314;2;424;0
WireConnection;108;0;107;0
WireConnection;108;2;106;0
WireConnection;404;0;150;1
WireConnection;145;0;141;0
WireConnection;145;1;143;0
WireConnection;231;0;270;0
WireConnection;231;1;219;0
WireConnection;57;0;181;0
WireConnection;57;1;108;0
WireConnection;315;0;314;0
WireConnection;315;1;311;0
WireConnection;339;0;337;0
WireConnection;339;2;340;0
WireConnection;380;0;378;0
WireConnection;406;0;150;1
WireConnection;406;1;404;0
WireConnection;406;2;407;0
WireConnection;147;0;145;0
WireConnection;147;1;146;0
WireConnection;254;1;255;0
WireConnection;254;0;231;0
WireConnection;338;0;315;0
WireConnection;338;1;339;0
WireConnection;61;1;181;0
WireConnection;61;0;57;0
WireConnection;226;0;148;0
WireConnection;153;0;148;0
WireConnection;343;0;298;0
WireConnection;343;1;344;0
WireConnection;343;2;341;0
WireConnection;405;1;150;1
WireConnection;405;0;406;0
WireConnection;149;1;147;0
WireConnection;379;1;380;0
WireConnection;377;1;378;0
WireConnection;230;0;270;0
WireConnection;230;1;254;0
WireConnection;220;1;226;0
WireConnection;227;0;230;0
WireConnection;227;1;219;0
WireConnection;157;1;153;0
WireConnection;297;0;149;1
WireConnection;297;1;405;0
WireConnection;297;2;343;0
WireConnection;64;1;61;0
WireConnection;335;1;315;0
WireConnection;335;0;338;0
WireConnection;187;1;188;1
WireConnection;187;2;188;2
WireConnection;187;3;188;3
WireConnection;376;0;373;0
WireConnection;376;1;377;0
WireConnection;376;2;379;0
WireConnection;159;0;297;0
WireConnection;200;0;187;0
WireConnection;299;1;335;0
WireConnection;366;1;376;0
WireConnection;382;0;273;1
WireConnection;382;1;64;4
WireConnection;218;0;227;0
WireConnection;218;1;220;0
WireConnection;158;0;230;0
WireConnection;158;1;157;0
WireConnection;308;0;306;0
WireConnection;308;1;299;0
WireConnection;308;2;382;0
WireConnection;308;3;366;1
WireConnection;160;0;159;0
WireConnection;160;1;158;0
WireConnection;199;0;187;0
WireConnection;199;1;200;0
WireConnection;199;2;201;0
WireConnection;215;0;159;0
WireConnection;215;1;218;0
WireConnection;435;1;215;0
WireConnection;435;2;148;0
WireConnection;434;1;160;0
WireConnection;434;2;148;0
WireConnection;328;0;308;0
WireConnection;203;0;199;0
WireConnection;177;0;64;0
WireConnection;177;1;83;0
WireConnection;177;2;273;1
WireConnection;243;1;244;0
WireConnection;243;0;435;0
WireConnection;168;0;167;0
WireConnection;240;1;208;0
WireConnection;240;0;203;0
WireConnection;163;1;161;0
WireConnection;163;0;434;0
WireConnection;233;0;243;0
WireConnection;171;1;169;0
WireConnection;171;0;168;0
WireConnection;232;0;163;0
WireConnection;332;0;177;0
WireConnection;332;1;329;0
WireConnection;207;1;242;0
WireConnection;207;0;240;0
WireConnection;205;1;206;0
WireConnection;205;0;203;0
WireConnection;331;1;177;0
WireConnection;331;0;332;0
WireConnection;173;0;170;0
WireConnection;173;1;172;0
WireConnection;173;2;171;0
WireConnection;173;3;207;0
WireConnection;402;0;236;0
WireConnection;402;1;382;0
WireConnection;234;0;402;0
WireConnection;234;1;331;0
WireConnection;234;2;237;0
WireConnection;175;0;170;4
WireConnection;175;1;172;4
WireConnection;175;2;171;0
WireConnection;175;3;205;0
WireConnection;174;0;173;0
WireConnection;252;1;253;0
WireConnection;252;0;247;0
WireConnection;176;0;175;0
WireConnection;246;1;248;0
WireConnection;246;0;252;0
WireConnection;251;1;331;0
WireConnection;251;0;234;0
WireConnection;334;0;64;4
WireConnection;259;0;83;4
WireConnection;284;0;273;1
WireConnection;256;0;259;0
WireConnection;249;1;250;0
WireConnection;249;0;238;0
WireConnection;333;0;334;0
WireConnection;239;0;251;0
WireConnection;239;1;211;0
WireConnection;239;2;246;0
WireConnection;283;0;284;0
WireConnection;178;0;333;0
WireConnection;178;1;256;0
WireConnection;178;2;283;0
WireConnection;178;3;212;0
WireConnection;178;4;249;0
WireConnection;399;0;397;0
WireConnection;399;1;239;0
WireConnection;395;0;392;0
WireConnection;396;0;399;0
WireConnection;396;1;239;0
WireConnection;396;2;395;0
WireConnection;361;0;178;0
WireConnection;400;1;239;0
WireConnection;400;0;396;0
WireConnection;401;0;239;0
WireConnection;401;3;361;0
WireConnection;432;0;400;0
WireConnection;432;3;362;0
WireConnection;431;0;432;0
ASEEND*/
//CHKSM=920EADB9DA598C332C48521DCEDD9B53817CFBDA