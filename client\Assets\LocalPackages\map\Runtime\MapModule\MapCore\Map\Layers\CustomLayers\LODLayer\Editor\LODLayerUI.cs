﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    [CustomEditor(typeof(LODLayerLogic))]
    public class LODLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as LODLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            var layerData = mLogic.layer.GetLayerData();

            CopyFromMapLODSetting();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                if (GUILayout.Button(new GUIContent("Copy From Map LOD Setting", "复制Map LOD的设置")))
                {
                    CopyFromMapLODSetting();
                }

                var layerData = mLogic.layer.GetLayerData();
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, null, null);

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Width", mLogic.layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", mLogic.layer.GetTotalHeight().ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void CopyFromMapLODSetting()
        {
            mLogic.layer.CopyFromMapLODSetting();
        }

        LODLayerLogic mLogic;
    }
}

#endif