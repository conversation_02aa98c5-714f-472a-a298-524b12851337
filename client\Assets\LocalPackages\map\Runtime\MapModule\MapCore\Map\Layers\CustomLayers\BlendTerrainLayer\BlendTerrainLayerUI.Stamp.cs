﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class BlendTerrainLayerUI : UnityEditor.Editor
    {
        enum StampState
        {
            SelectStampAreaMin,
            SelectStampAreaMax,
            Paste,
        }

        public class StampData
        {
            public int tileIndex;
            public int tileType;
            public int subTypeIndex;
            public string prefabPath;
        }

        public class Stamp
        {
            StampData[,] data;

            public Stamp(StampData[,] data)
            {
                this.data = data;
            }

            public int GetWidth()
            {
                return data != null ? data.GetLength(1) : 0;
            }

            public int GetHeight()
            {
                return data != null ? data.GetLength(0) : 0;
            }

            public StampData GetData(int x, int y)
            {
                return data[y, x];
            }
        }

        void DrawStampInspector()
        {
            bool rectangleStamp = EditorGUILayout.Toggle("Rectangle", mRectangleStamp);
            if (rectangleStamp != mRectangleStamp)
            {
                mRectangleStamp = rectangleStamp;
            }
        }

        void DrawStampToolSceneGUI(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);

            if (mStampState == StampState.Paste)
            {
                if (mStamp != null)
                {
                    int stampHeight = mStamp.GetHeight();
                    int stampWidth = mStamp.GetWidth();
                    var coord = mLogic.layer.layerData.FromWorldPositionToCoordinate(worldPos);
                    var pos = mLogic.layer.FromCoordinateToWorldPosition(coord.x, coord.y);

                    mStampIndicator.Update(pos);

                    if (e.type == EventType.MouseDown && e.button == 0)
                    {
                        SetStampTiles(coord.x, coord.y);
                    }
                    else if (e.type == EventType.MouseUp && e.button == 1)
                    {
                        mStampState = StampState.SelectStampAreaMin;
                        mStampIndicator.OnDestroy();
                    }
                }
                else
                {
                    mStampState = StampState.SelectStampAreaMin;
                }
            }
            else
            {
                if (mRectangleStamp)
                {
                    DrawRectangleStampToolSceneGUI(e, worldPos);
                }
                else
                {
                    DrawPolygonStampToolSceneGUI(e, worldPos);
                }
            }

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void DrawPolygonStampToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mStampPolygonVertices.Count == 0)
                {
                    mStampPolygonVertices.Add(worldPos);
                }
                mStampPolygonVertices.Add(worldPos);
                mStampDiplayPolygonVertices = mStampPolygonVertices.ToArray();
            }
            else if (e.type == EventType.MouseMove && e.button == 0)
            {
                if (mStampPolygonVertices.Count > 0)
                {
                    mStampPolygonVertices[mStampPolygonVertices.Count - 1] = worldPos;
                    mStampDiplayPolygonVertices[mStampPolygonVertices.Count - 1] = worldPos;
                }
            }
            else if (e.type == EventType.MouseUp && e.button == 1)
            {
                if (mStampPolygonVertices.Count > 3)
                {
                    mStampPolygonVertices.RemoveAt(mStampPolygonVertices.Count - 1);
                    CreatePolygonStamp();
                    mStampState = StampState.Paste;
                }
                mStampPolygonVertices.Clear();
                mStampDiplayPolygonVertices = null;
            }

            if (mStampPolygonVertices.Count > 0)
            {
                Handles.DrawPolyLine(mStampDiplayPolygonVertices);
            }
        }

        void DrawRectangleStampToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mStampState == StampState.SelectStampAreaMin)
                {
                    mStampState = StampState.SelectStampAreaMax;
                    mMinStampPos = worldPos;
                    mMaxStampPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseDrag && e.button == 0)
            {
                if (mStampState == StampState.SelectStampAreaMax)
                {
                    mMaxStampPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseUp && e.button == 0)
            {
                if (mStampState == StampState.SelectStampAreaMax)
                {
                    CreateStamp();
                    if (mStamp != null)
                    {
                        mStampState = StampState.Paste;
                    }
                    else
                    {
                        mStampState = StampState.SelectStampAreaMin;
                    }
                }
            }

            if (mStampState == StampState.SelectStampAreaMax)
            {
                Vector3 min = Vector3.Min(mMinStampPos, mMaxStampPos);
                Vector3 max = Vector3.Max(mMinStampPos, mMaxStampPos);
                Handles.DrawWireCube((min + max) * 0.5f, max - min);
            }
        }

        void CreateStamp()
        {
            Vector3 min = Vector3.Min(mMinStampPos, mMaxStampPos);
            Vector3 max = Vector3.Max(mMinStampPos, mMaxStampPos);
            var layer = mLogic.layer;

            var minCoord = layer.layerData.FromWorldPositionToCoordinate(min);
            var maxCoord = layer.layerData.FromWorldPositionToCoordinate(max);

            int rows = layer.verticalTileCount;
            int cols = layer.horizontalTileCount;
            int minX = Mathf.Clamp(minCoord.x, 0, cols - 1);
            int minY = Mathf.Clamp(minCoord.y, 0, rows - 1);
            int maxX = Mathf.Clamp(maxCoord.x, 0, cols - 1);
            int maxY = Mathf.Clamp(maxCoord.y, 0, rows - 1);
            int width = maxX - minX + 1;
            int height = maxY - minY + 1;

            bool empty = true;
            var stampData = new StampData[height, width];
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    var tile = layer.GetTile(j, i);
                    if (tile != null)
                    {
                        empty = false;
                        var data = new StampData();
                        data.prefabPath = tile.GetAssetPath(0);
                        data.tileType = tile.type;
                        data.tileIndex = tile.index;
                        data.subTypeIndex = tile.subTypeIndex;
                        stampData[i - minY, j - minX] = data;
                    }
                }
            }

            if (!empty)
            {
                mStamp = new Stamp(stampData);
                mStampIndicator.SetStamp(mStamp, mLogic.layer);
            }
            else
            {
                mStamp = null;
                mStampIndicator.OnDestroy();
            }
        }

        void CreatePolygonStamp()
        {
            var layer = mLogic.layer;
            var layerData = layer.layerData;

            var bounds = Utils.CreateBounds(mStampPolygonVertices);
            var minCoord = layerData.FromWorldPositionToCoordinate(bounds.min);
            var maxCoord = layerData.FromWorldPositionToCoordinate(bounds.max);

            int minX = Mathf.Clamp(minCoord.x, 0, layerData.horizontalTileCount - 1);
            int minY = Mathf.Clamp(minCoord.y, 0, layerData.verticalTileCount - 1);
            int maxX = Mathf.Clamp(maxCoord.x, 0, layerData.horizontalTileCount - 1);
            int maxY = Mathf.Clamp(maxCoord.y, 0, layerData.verticalTileCount - 1);

            int width = maxX - minX + 1;
            int height = maxY - minY + 1;

            bool empty = true;
            var stampData = new StampData[height, width];

            List<Vector3> tilePolygon = new List<Vector3>();
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    var minPos = layerData.FromCoordinateToWorldPosition(j, i);
                    var maxPos = layerData.FromCoordinateToWorldPosition(j + 1, i + 1);
                    tilePolygon.Clear();

                    tilePolygon.Add(minPos);
                    tilePolygon.Add(new Vector3(minPos.x, 0, maxPos.z));
                    tilePolygon.Add(maxPos);
                    tilePolygon.Add(new Vector3(maxPos.x, 0, minPos.z));
                    if (PolygonCollisionCheck.Overlap(tilePolygon, mStampPolygonVertices))
                    {
                        var tile = layer.GetTile(j, i);
                        if (tile != null)
                        {
                            empty = false;
                            var data = new StampData();
                            data.prefabPath = tile.GetAssetPath(0);
                            data.tileType = tile.type;
                            data.tileIndex = tile.index;
                            data.subTypeIndex = tile.subTypeIndex;
                            stampData[i - minY, j - minX] = data;
                        }
                    }
                }
            }

            if (!empty)
            {
                mStamp = new Stamp(stampData);
                mStampIndicator.SetStamp(mStamp, mLogic.layer);
            }
            else
            {
                mStamp = null;
                mStampIndicator.OnDestroy();
            }
        }

        void SetStampTiles(int centerX, int centerY)
        {
            int width = mStamp.GetWidth();
            int height = mStamp.GetHeight();
            int minX = centerX - width / 2;
            int maxX = minX + width - 1;
            int minY = centerY - height / 2;
            int maxY = minY + height - 1;

            var layer = mLogic.layer;
            
            var act = new ActionChangeBlendTerrainLayerTiles(layer.id, Mathf.Max(0, minX - 1), Mathf.Max(0, minY - 1), maxX, maxY);
            act.Begin();

            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    var stampData = mStamp.GetData(j - minX, i - minY);
                    if (stampData != null)
                    {
                        layer.SetTile(j, i, stampData.tileIndex, stampData.tileType, stampData.subTypeIndex);
                    }
                }
            }

            act.End();
            ActionManager.instance.PushAction(act, true, false);
        }

        StampState mStampState = StampState.SelectStampAreaMin;
        bool mRectangleStamp = true;
        Stamp mStamp;
        Vector3 mMinStampPos;
        Vector3 mMaxStampPos;
        StampIndicator mStampIndicator = new StampIndicator();
        List<Vector3> mStampPolygonVertices = new List<Vector3>();
        Vector3[] mStampDiplayPolygonVertices;
    }
}
#endif