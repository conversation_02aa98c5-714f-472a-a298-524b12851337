﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionRemoveVariation : EditorAction
    {
        public ActionRemoveVariation(GroundTileMaker maker, int tileIndex, int instanceID)
        {
            mMaker = maker;
            mTileIndex = tileIndex;
            mVariationInstanceID = instanceID;
            GroundTileMaker.GroundTileVariation variation = maker.GetVariation(tileIndex, mVariationInstanceID);
            mName = variation.name;

            int lodCount = maker.lodCount;
            mLODTextureDatas = new List<List<Color32[]>>(lodCount);
            for (int lod = 0; lod < lodCount; ++lod)
            {
                List<Color32[]> textureDatas = new List<Color32[]>();
                mLODTextureDatas.Add(textureDatas);
                var maskTextures = variation.GetLOD(lod).maskTextures;
                for (int i = 0; i < maskTextures.Count; ++i)
                {
                    int textureResolution = maskTextures[i].texture.width;
                    Color32[] colors = maker.color32ArrayPool.Rent(textureResolution * textureResolution);
                    var textureData = maskTextures[i].textureData;
                    for (int y = 0; y < textureResolution; ++y)
                    {
                        for (int x = 0; x < textureResolution; ++x)
                        {
                            int idx = y * textureResolution + x;
                            colors[idx] = textureData[idx];
                        }
                    }
                    textureDatas.Add(colors);
                }
            }
        }

        public override void OnDestroy()
        {
            for (int lod = 0; lod < mLODTextureDatas.Count; ++lod)
            {
                var textureDatas = mLODTextureDatas[lod];
                for (int i = 0; i < textureDatas.Count; ++i)
                {
                    mMaker.color32ArrayPool.Return(textureDatas[i]);
                }
            }
            mLODTextureDatas = null;
        }

        public override bool Do()
        {
            if (mLODTextureDatas.Count != mMaker.lodCount)
            {
                //lod数据已经不同步了,不能执行这个命令了
                return false;
            }

            mMaker.RemoveVariationByID(mTileIndex, mVariationInstanceID);
            return true;
        }

        public override bool Undo()
        {
            if (mLODTextureDatas.Count != mMaker.lodCount)
            {
                //lod数据已经不同步了,不能执行这个命令了
                return false;
            }

            mMaker.AddVariation(mTileIndex, mName, mVariationInstanceID, mLODTextureDatas);
            return true;
        }

        GroundTileMaker mMaker;
        int mTileIndex;
        int mVariationInstanceID;
        string mName;
        List<List<Color32[]>> mLODTextureDatas = new List<List<Color32[]>>();
    }
}

#endif