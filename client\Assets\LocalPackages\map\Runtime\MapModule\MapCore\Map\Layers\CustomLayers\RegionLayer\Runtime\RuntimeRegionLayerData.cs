﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RuntimeRegionLayerData : QuadTreeObjectLayerData
    {
        public RuntimeRegionLayerData(MapLayerDataHeader header, Map map, RuntimeRegionData[] regions) : base(header, null, map, null, null)
        {
            for (int i = 0; i < regions.Length; ++i)
            {
                mRegionNumberToID.Add(regions[i].number, regions[i].id);
            }
        }

        public int RegionNumberToID(int regionNumber)
        {
            int id;
            mRegionNumberToID.TryGetValue(regionNumber, out id);
            return id;
        }

        public RuntimeRegionData FindRegion(int regionNumber)
        {
            var id = RegionNumberToID(regionNumber);
            return GetObjectData(id) as RuntimeRegionData;
        }

        Dictionary<int, int> mRegionNumberToID = new Dictionary<int, int>();
    }
}
