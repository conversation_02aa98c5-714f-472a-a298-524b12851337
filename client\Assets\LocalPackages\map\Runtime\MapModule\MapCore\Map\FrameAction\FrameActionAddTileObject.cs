﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //当新地块显示时加入管理其子节点
    public class FrameActionAddTileObject : FrameAction
    {
        public static FrameActionAddTileObject Require(TileGridObjectLayerData layerData, Vector3 tilePos, ChildPrefabTransform2 prefabTransform, string prefabPath, int objectDataID, int localIndex, int lod, System.Action<TileObjectData> onObjectScaleChangeCallback)
        {
            var act = mPool.Require();
            act.Init(layerData, tilePos, prefabTransform, prefabPath, objectDataID, localIndex, lod, onObjectScaleChangeCallback);
            return act;
        }

        void Init(TileGridObjectLayerData layerData, Vector3 tilePos, ChildPrefabTransform2 transform, string prefabPath, int objectDataID, int localIndex, int lod, System.Action<TileObjectData> onObjectScaleChangeCallback)
        {
            InitAction();
            mPrefabPath = prefabPath;
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
            mLayerData = layerData;
            mChildPrefabTransform = transform;
            mObjectDataID = objectDataID;
            mBigTilePos = tilePos;
            mLocalIndex = localIndex;
            mLOD = lod;

            mKey = MakeActionKey(objectDataID, type);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mChildPrefabTransform = null;
            mPool.Release(this);
        }

        protected override void DoImpl()
        {
            var map = mLayerData.map;
            var viewport = map.viewport;

            var tilePos = mBigTilePos;
            var coord = mLayerData.FromWorldPositionToCoordinate(mBigTilePos);
            int tileIndex = coord.y * mLayerData.horizontalTileCount + coord.x;

            var offset = new Vector2(tilePos.x, tilePos.z);

            var bounds = mChildPrefabTransform.GetLocalBoundsInPrefab(mLayerData.prefabInitInfo);
            var rectMin = bounds.min + offset;
            var rectMax = bounds.max + offset;

            bool isVisible = false;

            bool alwaysVisible = mChildPrefabTransform.objectType == TileObjectType.AlwaysVisible;

            if (alwaysVisible)
            {
                isVisible = true;
            }
            else
            {
                if (rectMin.x > viewport.xMax || rectMin.y > viewport.yMax ||
                    viewport.xMin > rectMax.x || viewport.yMin > rectMax.y)
                {
                    isVisible = false;
                }
                else
                {
                    isVisible = mLayerData.IsIDTaken(mObjectDataID, mChildPrefabTransform.objectType) ? false : true;
                }
            }

            var worldBounds = new Rect();
            worldBounds.Set(rectMin.x, rectMin.y, rectMax.x - rectMin.x, rectMax.y - rectMin.y);

            var transformTable = map.transformTable;
            Vector3 scale = mChildPrefabTransform.GetScale(mLayerData.prefabInitInfo);

            float baseScale = scale.x;
            if (mChildPrefabTransform.objectType == TileObjectType.ScaleDecorationObject)
            {
                mLayerData.hasScaleObject = true;
                scale = mLayerData.objectScale * scale;
            }

            float mapWidth = map.mapWidth;
            float mapHeight = map.mapHeight;
            var pos = mChildPrefabTransform.GetPosition(mLayerData.prefabInitInfo) + mBigTilePos;
            bool isInWorld = pos.x >= 0 && pos.x < mapWidth && pos.z >= 0 && pos.z < mapHeight;
            bool useCullManager = (worldBounds.width <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && worldBounds.height <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && !alwaysVisible) && isInWorld;

            var pool = mLayerData.pool;

            var tile = pool.Require(mObjectDataID, mLayerData.map, mChildPrefabTransform, mPrefabPath, worldBounds, pos, scale, mChildPrefabTransform.GetRotation(mLayerData.prefabInitInfo), mLocalIndex, tileIndex, mLOD, baseScale, mOnObjectScaleChangeCallback, useCullManager);

            mLayerData.SetObjectActiveOnly(tile, isVisible, mLOD);
            mLayerData.AddTileObject(tile);
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return FrameAction.MakeKeyHelper(id, type);
        }

        public override long key => mKey;
        public override FrameActionType type => FrameActionType.AddTileObject;
        public override string debugInfo
        {
            get
            {
                return "";
            }
        }
        public override string name => "Add Tile Object";

        static ObjectPool<FrameActionAddTileObject> mPool = new ObjectPool<FrameActionAddTileObject>(30000, () => new FrameActionAddTileObject());

        TileGridObjectLayerData mLayerData;
        ChildPrefabTransform2 mChildPrefabTransform;
        Vector3 mBigTilePos;
        int mObjectDataID;
        int mLocalIndex;
        int mLOD;
        long mKey;
        string mPrefabPath;
        System.Action<TileObjectData> mOnObjectScaleChangeCallback;
    }
}
