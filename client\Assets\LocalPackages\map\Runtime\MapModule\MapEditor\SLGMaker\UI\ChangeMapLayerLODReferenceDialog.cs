﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    class ChangeMapLayerLODReferenceDialog : EditorWindow
    {
        public void Show(MapLayerLODConfig lodConfig, string curLODName, System.Action<string, string> onChangeName)
        {
            mLayerLODConfig = lodConfig;
            mCurLODName = curLODName;
            mOnChangeName = onChangeName;
            var lodManager = Map.currentMap.data.lodManager;
            int n = lodManager.lodCount;
            mLODNames = new string[n];
            mLODNamesWithDetail = new string[n];
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                mLODNames[i] = lod.name;
                mLODNamesWithDetail[i] = lod.name + $" ({lod.cameraHeight}m)";

                if (mLODNames[i] == curLODName)
                {
                    mSelectedIndex = i;
                }
            }
        }

        void OnGUI()
        {
            mSelectedIndex = EditorGUILayout.Popup("LOD Name", mSelectedIndex, mLODNamesWithDetail);
            if (GUILayout.Button("OK"))
            {
                if (mLayerLODConfig.GetLOD(mLODNames[mSelectedIndex]) != null)
                {
                    EditorUtility.DisplayDialog("Error", $"{mLODNames[mSelectedIndex]} already exists", "OK");
                }
                else
                {
                    mOnChangeName(mCurLODName, mLODNames[mSelectedIndex]);
                    Close();
                }
            }
        }

        MapLayerLODConfig mLayerLODConfig;
        int mSelectedIndex;
        string[] mLODNames;
        string[] mLODNamesWithDetail;
        string mCurLODName;
        System.Action<string, string> mOnChangeName;
    }
}

#endif