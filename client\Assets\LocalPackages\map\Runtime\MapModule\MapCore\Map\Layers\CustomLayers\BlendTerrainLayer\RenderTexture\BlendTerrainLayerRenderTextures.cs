﻿ 



 
 

#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class BlendTerrainLayerRenderTextures
    {
        public void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;

            if (mBigTileRenderTextures != null)
            {
                for (int i = 0; i < mBigTileRenderTextures.Length; ++i)
                {
                    Utils.DestroyObject(mBigTileRenderTextures[i]);
                }
                mBigTileRenderTextures = null;
            }

            if (mBigTileMaterials != null)
            {
                for (int i = 0; i < mBigTileMaterials.Length; ++i)
                {
                    Utils.DestroyObject(mBigTileMaterials[i]);
                }
                mBigTileMaterials = null;
            }

            if (mRT != null)
            {
                mRT.OnDestroy(true);
            }
        }

        void CreateGameObjects(Transform parent, BlendTerrainLayerData layerData, int bigTileCount, int textureSize, Shader bigTileShader, string texturePropertyName)
        {
            mSmallTileCountPerBlock = layerData.horizontalTileCount / bigTileCount;
            mBigTileCount = bigTileCount;
            mBigTileShader = bigTileShader;
            mTextureSize = textureSize;
            //init objects
            if (mRoot == null)
            {
                mRoot = new GameObject("RenderTextures Root");
                mRoot.transform.parent = parent;
                mRoot.transform.position = new Vector3(0, -1, 0);
                mBigTileRenderTextures = new RenderTexture[bigTileCount * bigTileCount];
                mBigTileMaterials = new Material[bigTileCount * bigTileCount];
                mBigTileDirty = new bool[bigTileCount * bigTileCount];
                int idx = 0;
                for (int i = 0; i < bigTileCount; ++i)
                {
                    for (int j = 0; j < bigTileCount; ++j)
                    {
                        mBigTileDirty[idx] = true;
                        CreateBigTileGameObject(layerData, j, i, bigTileCount, textureSize, layerData.tileWidth, mSmallTileCountPerBlock, bigTileShader, texturePropertyName);
                        ++idx;
                    }
                }

                mRT = new RenderObjectToTexture(null, null);
                mRT.textureSize = textureSize;
            }
        }

        public void Create(Transform parent, BlendTerrainLayerData layerData, int bigTileCount, int textureSize, Shader bigTileShader, bool forceRender, string texturePropertyName)
        {
            if (!CheckValidation(layerData, bigTileCount))
            {
#if UNITY_EDITOR
                EditorUtility.DisplayDialog("Error", "Create Textures Failed!", "OK");
#endif
                return;
            }

            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            CreateGameObjects(parent, layerData, bigTileCount, textureSize, bigTileShader, texturePropertyName);

            int bigTileIdx = 0;
            for (int i = 0; i < bigTileCount; ++i)
            {
                for (int j = 0; j < bigTileCount; ++j)
                {
                    //生成这一层这个lod tile需要的model template
                    if (forceRender || mBigTileDirty[bigTileIdx]) {
                        RenderBigTile(layerData, j, i, bigTileCount, mSmallTileCountPerBlock, textureSize, bigTileShader, texturePropertyName);
                        mBigTileDirty[bigTileIdx] = false;
                    }
                    ++bigTileIdx;
                }
            }

            var duration = w.Stop();
            Debug.Log($"Create Render Textures cost {duration} time!");
        }

        public void Clear(string projectFolder)
        {
#if UNITY_EDITOR
            string textureFolder = $"{projectFolder}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME}";
            if (Directory.Exists(textureFolder))
            {
                FileUtil.DeleteFileOrDirectory(textureFolder);
            }

            OnDestroy();
#endif
        }

        public bool Update(Transform parent, BlendTerrainLayerData layerData, string texturePropertyName)
        {
            if (mTextureSize == 0)
            {
                return false;
            }
            Create(parent, layerData, mBigTileCount, mTextureSize, mBigTileShader, false, texturePropertyName);
            return true;
        }

        void CreateBigTileGameObject(BlendTerrainLayerData layerData, int x, int y, int bigTileCount, int textureSize, float tileWidth, int smallTileCountPerBlock, Shader tileShader, string texturePropertyName)
        {
            int bigTileIdx = y * bigTileCount + x;
            //添加图片生成大小数值检测处理
            if (textureSize <= 0)
                textureSize = 256;

            mBigTileRenderTextures[bigTileIdx] = new RenderTexture(textureSize, textureSize, 24, RenderTextureFormat.Default, RenderTextureReadWrite.Default);
            mBigTileRenderTextures[bigTileIdx].wrapMode = TextureWrapMode.Clamp;

            var planePrefab = GameObject.CreatePrimitive(PrimitiveType.Plane);
            planePrefab.transform.localRotation = Quaternion.Euler(0, 180, 0);
            planePrefab.transform.position = CalculateLODTilePosition(layerData, x, y, smallTileCountPerBlock);
            planePrefab.transform.SetParent(mRoot.transform, false);

            float scale = tileWidth * smallTileCountPerBlock;
            planePrefab.transform.localScale = Vector3.one * (scale * 0.1f);

            var collider = planePrefab.GetComponent<Collider>();
            GameObject.DestroyImmediate(collider);
            
            var meshRenderer = planePrefab.GetComponent<MeshRenderer>();
            mBigTileMaterials[bigTileIdx] = new Material(tileShader);
            meshRenderer.sharedMaterial = mBigTileMaterials[bigTileIdx];
            meshRenderer.sharedMaterial.SetTexture(texturePropertyName, mBigTileRenderTextures[bigTileIdx]);
            mBigTileMaterials[bigTileIdx].renderQueue = 1800;
        }

        public static bool CheckValidation(BlendTerrainLayerData layerData, int horizontalBlockCount)
        {
            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            if (rows != cols)
            {
                return false;
            }
            //if (cols % horizontalBlockCount != 0)
            //{
            //    return false;
            //}

            return true;
        }

        void RenderBigTile(BlendTerrainLayerData layerData, int x, int y, int bigTileCount, int smallTileCountPerBlock, int textureSize, Shader bigTileShader, string texturePropertyName)
        {
            Debug.Assert(bigTileShader != null);

            Bounds bounds = new Bounds();

            int bigTileIdx = y * bigTileCount + x;
            GameObject bigTileRoot = new GameObject(bigTileIdx.ToString());
            for (int i = 0; i < smallTileCountPerBlock; ++i)
            {
                for (int j = 0; j < smallTileCountPerBlock; ++j)
                {
                    int gx = j + x * smallTileCountPerBlock;
                    int gy = i + y * smallTileCountPerBlock;
                    var tile = layerData.GetTile(gx, gy);
                    var pos = layerData.FromCoordinateToWorldPosition(j, i);
                    if (tile != null)
                    {
                        string tilePrefabPath = tile.GetAssetPath(0);
                        var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, pos, Vector3.one, Quaternion.identity);
                        tileObject.name = tilePrefabPath;
                        tileObject.transform.parent = bigTileRoot.transform;
                    }
                }
            }
            bounds = CalculateBounds(smallTileCountPerBlock, layerData);

            //render block tiles into texture
            mRT.Render(bigTileRoot, false, 300, mBigTileRenderTextures[bigTileIdx], false, false, bounds);

            if (mBigTileMaterials[bigTileIdx] != null)
            {
                mBigTileMaterials[bigTileIdx].SetTexture(texturePropertyName, mBigTileRenderTextures[bigTileIdx]);
            }

            int n = bigTileRoot.transform.childCount;
            for (int i = n - 1; i >= 0; --i)
            {
                var child = bigTileRoot.transform.GetChild(i).gameObject;
                var map = Map.currentMap;
                map.view.reusableGameObjectPool.Release(child.name, child, map);
            }

            GameObject.DestroyImmediate(bigTileRoot);
        }

        Bounds CalculateBounds(int smallTileCountPerBlock, MapLayerData layerData)
        {
            var bounds = new Bounds();
            float boundsWidth = smallTileCountPerBlock * layerData.tileWidth;
            float boundsHeight = smallTileCountPerBlock * layerData.tileHeight;
            var boundsMin = layerData.layerOffset;
            var boundsMax = boundsMin + new Vector3(boundsWidth, 0, boundsHeight);
            bounds.SetMinMax(boundsMin, boundsMax);
            return bounds;
        }

        Vector3 CalculateLODTilePosition(BlendTerrainLayerData layerData, int x, int y, int smallTileCountPerBlock)
        {
            var startPos = layerData.FromCoordinateToWorldPosition(x * smallTileCountPerBlock, y * smallTileCountPerBlock);
            var endPos = layerData.FromCoordinateToWorldPosition(x * smallTileCountPerBlock + smallTileCountPerBlock, y * smallTileCountPerBlock + smallTileCountPerBlock);
            return (startPos + endPos) * 0.5f;
        }

        public void OnSmallTileChange(int x, int y)
        {
            if (mSmallTileCountPerBlock > 0)
            {
                int bigTileX = x / mSmallTileCountPerBlock;
                int bigTileY = y / mSmallTileCountPerBlock;
                if (bigTileX >= 0 && bigTileX < mBigTileCount &&
                    bigTileY >= 0 && bigTileY < mBigTileCount)
                {
                    mBigTileDirty[bigTileY * mBigTileCount + bigTileX] = true;
                }
            }
        }

        GameObject mRoot;
        RenderTexture[] mBigTileRenderTextures;
        Material[] mBigTileMaterials;
        bool[] mBigTileDirty;
        RenderObjectToTexture mRT;
        int mTextureSize;
        Shader mBigTileShader;
        int mBigTileCount;
        int mSmallTileCountPerBlock;
    }
}