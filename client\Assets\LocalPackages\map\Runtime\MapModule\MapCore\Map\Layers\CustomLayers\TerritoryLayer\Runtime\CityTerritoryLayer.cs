﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class CityTerritoryLayer : MapLayerBase
    {
        public CityTerritoryLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.CityTerritoryLayerData;

            var subLayer0 = sourceLayer.subLayers[0];
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, subLayer0.zTileCount, subLayer0.xTileCount, subLayer0.tileWidth, subLayer0.tileHeight, GridType.Rectangle, sourceLayer.origin + setting.origin);

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            List<CityTerritoryLayerData.SubLayerData> subLayers = new List<CityTerritoryLayerData.SubLayerData>();

            for (int s = 0; s < sourceLayer.subLayers.Count; ++s)
            {
                var subLayerData = sourceLayer.subLayers[s];
                CityTerritoryData[] territories = null;
                CityTerritoryEdgeData[] edges = null;
                var subLayer = new CityTerritoryLayerData.SubLayerData();

                Texture2D maskTexture = null;
                if (subLayerData.maskTextureWidth > 0)
                {
                    maskTexture = new Texture2D(subLayerData.maskTextureWidth, subLayerData.maskTextureHeight, TextureFormat.RGBA32, false);
                    maskTexture.filterMode = FilterMode.Point;
                    maskTexture.SetPixels32(subLayerData.maskTextureData);
                    maskTexture.Apply();
                }

                var origin = header.origin;
                origin.y = 0;
                if (subLayerData.edgesInfo != null)
                {
                    int n = subLayerData.edgesInfo.Length;
                    edges = new CityTerritoryEdgeData[n];
                    for (int i = 0; i < n; ++i)
                    {
                        var edgeData = subLayerData.edgesInfo[i];
                        var edgeID = map.nextCustomObjectID;
                        ModelTemplate modelTemplate = null;
                        if (MapModuleResourceMgr.Exists(edgeData.prefabPath))
                        {
                            modelTemplate = map.GetOrCreateModelTemplate(edgeID, edgeData.prefabPath, false);
                        }
                        var mtl = MapModuleResourceMgr.LoadMaterial(edgeData.materialPath);
                        var block = edgeData.blockIndex >= 0 ? subLayerData.blocks[edgeData.blockIndex] : null;
                        int index = subLayerData.territories.Length + i;
                        var data = new CityTerritoryEdgeData(edgeID, map, 0, edgeData.position + origin, modelTemplate, edgeData.worldBounds, edgeData.leftRegionID, edgeData.rightRegionID, mtl, block, subLayer, index, maskTexture);

                        edges[i] = data;
                    }
                }

                if (subLayerData.territories != null)
                {
                    int n = subLayerData.territories.Length;
                    territories = new CityTerritoryData[n];
                    for (int i = 0; i < n; ++i)
                    {
                        var territoryData = subLayerData.territories[i];
                        var territoryID = map.nextCustomObjectID;
                        ModelTemplate modelTemplate = null;
                        if (MapModuleResourceMgr.Exists(territoryData.assetPath))
                        {
                            modelTemplate = map.GetOrCreateModelTemplate(territoryID, territoryData.assetPath, false);
                        }
                        var mtl = MapModuleResourceMgr.LoadMaterial(territoryData.materialPath);
                        var block = territoryData.blockIndex >= 0 ? subLayerData.blocks[territoryData.blockIndex] : null;
                        List<CityTerritoryEdgeData> territoryEdges = GetTerritoryEdges(territoryData.territoryID, edges);
                        var data = new CityTerritoryData(territoryID, map, 0, territoryData.position + origin, modelTemplate, territoryData.worldBounds, territoryData.territoryID, territoryData.buildingPosition, territoryData.color, mtl, block, subLayer, i, maskTexture, territoryEdges);

                        territories[i] = data;
                    }
                }

                subLayer.Init(subLayerData.xTileCount, subLayerData.zTileCount, subLayerData.tileWidth, subLayerData.tileHeight, territories, edges, subLayerData.grids, s == 0, subLayerData.blocks, maskTexture);
                subLayers.Add(subLayer);
            }

            mLayerData = new CityTerritoryLayerData(header, config, map, subLayers);
            mLayerView = new CityTerritoryLayerView(mLayerData, false);
            mLayerView.active = layerData.active;
            mLayerView.root.transform.position = new Vector3(0, layerOffset.y, 0);
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            for (int i = 0; i < subLayers.Count; ++i)
            {
                int nt = subLayers[i].territories.Length;
                for (int k = 0; k < nt; ++k)
                {
                    mLayerData.AddObjectData(subLayers[i].territories[k]);
                }

                foreach (var e in subLayers[i].edges)
                {
                    mLayerData.AddObjectData(e);
                }
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            if (active)
            {
                bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
                if (lodChanged)
                {
                    mLayerView.SetZoom(newCameraZoom, lodChanged);
                }
                return lodChanged;
            }
            return false;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public void SetSubLayerVisibility(int subLayerIdx, bool visible)
        {
            var subLayer = mLayerData.GetSubLayer(subLayerIdx);
            if (subLayer != null)
            {
                if (subLayer.isActive != visible)
                {
                    subLayer.isActive = visible;
                    mLayerData.RefreshSubLayerDataVisibility(subLayerIdx);
                }
            }
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public int GetTerritoryID(int x, int y)
        {
            return mLayerData.GetTerritoryID(x, y);
        }

        public int GetTerritoryID(int layer, int x, int y)
        {
            return mLayerData.GetTerritoryID(layer, x, y);
        }

        public int GetTerritoryID(Vector3 pos)
        {
            return mLayerData.GetTerritoryID(pos);
        }

        public int GetTerritoryID(int layer, Vector3 pos)
        {
            return mLayerData.GetTerritoryID(layer, pos);
        }

        public Vector3 GetCityPosition(int territoryID)
        {
            return mLayerData.GetCityPosition(territoryID);
        }

        public void SetTerritoryColor(int territoryID, Color color)
        {
            mLayerData.SetTerritoryColor(territoryID, color);
        }

        public Color GetTerritoryColor(int territoryID)
        {
            return mLayerData.GetTerritoryColor(territoryID);
        }

        public bool HideTerritory(int territoryID)
        {
            return mLayerData.HideTerritory(territoryID);
        }

        public void Hide(CityTerritoryDataBase data)
        {
            mLayerData.Hide(data);
        }

        public bool ShowTerritory(int territoryID)
        {
            return mLayerData.ShowTerritory(territoryID);
        }

        public bool HideTerritoryEdge(int territoryID)
        {
            return mLayerData.HideTerritoryEdge(territoryID);
        }

        public bool ShowTerritoryEdge(int territoryID)
        {
            return mLayerData.ShowTerritoryEdge(territoryID);
        }

        public void SetIntParam(int territoryID, int neighbourTerritoryID, string paramName, int val)
        {
            var edge = mLayerData.GetEdge(territoryID, neighbourTerritoryID);
            if (edge != null)
            {
                edge.material.SetInt(paramName, val);
            }
        }

        public void SetEdgeMaskTextureValue(int territoryID, int neighbourTerritoryID, float value)
        {
            var edge = mLayerData.GetEdge(territoryID, neighbourTerritoryID);
            if (edge != null)
            {
                edge.subLayer.SetEdgeMaskPxiel(edge.index, value);
            }
        }

        public IEnumerable<CityTerritoryEdgeData> GetEdges(int territoryID)
        {
            return mLayerData.GetEdges(territoryID);
        }

        public IEnumerable<CityTerritoryEdgeData> GetAllEdges(int subLayer)
        {
            return mLayerData.GetAllEdges(subLayer);
        }

        public IEnumerable<CityTerritoryData> GetAllTerritories(int subLayer)
        {
            return mLayerData.GetAllTerritories(subLayer);
        }

        public void HideAllEdges(int layer)
        {
            var edges = GetAllEdges(layer);
            foreach (var edge in edges)
            {
                mLayerData.Hide(edge);
            }
        }

        public void ShowAllEdges(int layer)
        {
            var edges = GetAllEdges(layer);
            foreach (var edge in edges)
            {
                mLayerData.Show(edge);
            }
        }

        public void ShowAllTerritories(int layer)
        {
            var territories = GetAllTerritories(layer);
            foreach (var t in territories)
            {
                mLayerData.Show(t);
            }
        }

        public void HideAllTerritories(int layer)
        {
            var territories = GetAllTerritories(layer);
            foreach (var t in territories)
            {
                mLayerData.Hide(t);
            }
        }

        List<CityTerritoryEdgeData> GetTerritoryEdges(int territoyID, CityTerritoryEdgeData[] allEdges)
        {
            List<CityTerritoryEdgeData> edges = new List<CityTerritoryEdgeData>();
            foreach (var edge in allEdges)
            {
                if (edge.territoryID == territoyID)
                {
                    edges.Add(edge);
                }
            }
            return edges;
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public MapObjectLayerData layerData { get { return mLayerData; } }
        public CityTerritoryLayerView layerView { get { return mLayerView; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        protected CityTerritoryLayerData mLayerData;
        protected CityTerritoryLayerView mLayerView;
    }
}
