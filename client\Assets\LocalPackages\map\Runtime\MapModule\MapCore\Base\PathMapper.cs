﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public class PathMapper
    {
        public string Map(string path)
        {
#if UNITY_EDITOR
            if (!string.IsNullOrEmpty(path))
            {
                string guid = AssetDatabase.AssetPathToGUID(path);
                mPathToGuid[path] = guid;
            }
#endif
            return path;
        }

        //把path映射到有效的路径下
        public string Unmap(string path)
        {
#if UNITY_EDITOR
            if (MapModuleResourceMgr.Exists(path))
            {
                return path;
            }
            //path所在的文件不在了,查找这个path对应的guid
            string guid;
            mPathToGuid.TryGetValue(path, out guid);
            var guidPath = AssetDatabase.GUIDToAssetPath(guid);
            if (string.IsNullOrEmpty(guidPath))
            {
                return path;
            }
            return guidPath;
#else
            return "";
#endif
        }

        public Dictionary<string, string> pathToGuid { get { return mPathToGuid; } }

        Dictionary<string, string> mPathToGuid = new Dictionary<string, string>();
    }
}
