﻿#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class EditorTerritorySubLayer
    {
        public void CreateAndGenerateOutlineAssets(string folder, int layerIdx, int lod, bool generateAssets, float layerWidth, float layerHeight, int horizontalTileCount, int verticalTileCount)
        {
            try
            {
                EditorUtility.DisplayProgressBar("Generating Region Data", $"Generating...", 0);
                CreateOutline(lod, true);
                GenerateOutlineAssets(folder, layerIdx, lod, true, generateAssets, layerWidth, layerHeight, horizontalTileCount, verticalTileCount);
                EditorUtility.ClearProgressBar();
            }
            catch (System.Exception e)
            {
                Debug.LogError(e.ToString());
                EditorUtility.ClearProgressBar();
            }
        }

        public void CreateOutline(int lod, bool clearProgressBar)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].OnDestroy();
            }

            mCreatorsForLODs[lod] = new CurveRegionCreator(mTileRoot.transform);
            List<CurveRegionCreator.RegionInput> regions = new List<CurveRegionCreator.RegionInput>();
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var coordinates = GetTerritoryCoordinates(mTerritories[i].id);
                if (coordinates.Count > 0)
                {
                    CurveRegionCreator.RegionInput regionInput = new CurveRegionCreator.RegionInput(mTerritories[i].id, coordinates);
                    regions.Add(regionInput);
                }
            }

            var param = mCurveRegionMeshGenerationParam.lodParams[lod];
            CurveRegionCreator.SettingInput settings = new CurveRegionCreator.SettingInput(param.pointDeltaDistance, param.segmentLengthRatio, param.minTangentLength, param.maxTangentLength, param.maxPointCountInOneSegment, param.moreRectangular, param.lineWidth, mCurveRegionMeshGenerationParam.vertexDisplayRadius, param.textureAspectRatio, mCurveRegionMeshGenerationParam.segmentLengthRatioRandomRange, mCurveRegionMeshGenerationParam.tangentRotationRandomRange, param.gridErrorThreshold, param.edgeMaterial, param.regionMaterial, param.useVertexColorForRegionMesh, param.combineMesh, param.mergeEdge, param.edgeHeight, param.shareEdge);

            CurveRegionCreator.Input input = new CurveRegionCreator.Input(regions, mGridWidth, mHorizontalGridCount, mVerticalGridCount, GetGridData, FromCoordinateToPosition, FromPositionToCoordinate, GetRegionColor, GetTerritoryCenter, settings);
            mCreatorsForLODs[lod].Create(input, true);

            if (clearProgressBar)
            {
                EditorUtility.ClearProgressBar();
            }
        }

        public string GetAssetFolder(string exportFolder, int layer)
        {
            return $"{exportFolder}/{MapCoreDef.NEW_TERRITORY_LAYER_RUNTIME_ASSETS_FOLDER_NAME}/layer{layer}";
        }

        public string GetTerritoryAssetPath(string exportFolder, int layer, int territoryID, int lod)
        {
            string assetFolder = GetAssetFolder(exportFolder, layer);
            return $"{assetFolder}/region_{territoryID}_lod{lod}.prefab";
        }

        public void GenerateOutlineAssets(string folder, int layer, int lod, bool displayProgressBar, bool generateAssets, float layerWidth, float layerHeight, int horizontalTileCount, int verticalTileCount)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                var assetFolder = GetAssetFolder(folder, layer);
                if (generateAssets)
                {
                    if (lod == 0)
                    {
                        FileUtil.DeleteFileOrDirectory(assetFolder);
                    }
                    if (!Directory.Exists(assetFolder))
                    {
                        Directory.CreateDirectory(assetFolder);
                    }
                    AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                }

                mCreatorsForLODs[lod].Generate(assetFolder, lod, displayProgressBar, generateAssets, layerWidth, layerHeight, horizontalTileCount, verticalTileCount);
            }
        }

        public void HideLineAndMesh(int lod)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].HideLineAndMesh();
            }
        }

        public void HideLine(int lod)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].HideLine();
            }
        }

        public void HideMesh(int lod)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].HideMesh();
            }
        }

        public void HideRegionMesh(int lod)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].HideRegionMesh();
            }
        }

        public void ShowMesh(int lod)
        {
            if (mCreatorsForLODs[lod] != null)
            {
                mCreatorsForLODs[lod].ShowMesh();
            }
        }

        CurveRegionMeshGenerationLODParam GetDefaultLODParam(bool isLOD0)
        {
            CurveRegionMeshGenerationLODParam param = null;
            if (isLOD0)
            {
                param = new CurveRegionMeshGenerationLODParam(segmentLengthRatio: 0.3f, minTangentLength: mGridWidth * 0.1f, maxTangentLength: mGridWidth * 0.6f, pointDeltaDistance: mGridWidth * 0.1f, maxPointCountInOneSegment: 4, moreRectangular: false, lineWidth: mGridWidth * 0.5f, textureAspectRatio: 2, gridErrorThreshold: 10, edgeMaterial: null, regionMaterial: null, useVertexColorForRegionMesh: false, combineMesh: false, mergeEdge : true, edgeHeight : 1, shareEdge : true);
            }
            else
            {
                param = new CurveRegionMeshGenerationLODParam(segmentLengthRatio: 0.3f, minTangentLength: mGridWidth * 0.1f, maxTangentLength: mGridWidth * 0.6f, pointDeltaDistance: mGridWidth * 0.1f, maxPointCountInOneSegment: 3, moreRectangular: false, lineWidth: mGridWidth * 0.5f, textureAspectRatio: 2, gridErrorThreshold: 10, edgeMaterial: null, regionMaterial: null, useVertexColorForRegionMesh: false, combineMesh: true, mergeEdge: true, edgeHeight : 1, shareEdge : true);
            }
            return param;
        }

        public List<CurveRegionCreator> regionCreatorsForLODs { get { return mCreatorsForLODs; } }

        List<CurveRegionCreator> mCreatorsForLODs = new List<CurveRegionCreator>();
    }
}

#endif