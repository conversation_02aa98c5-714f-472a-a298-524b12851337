﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(RuinLayerLogic))]
    public partial class RuinLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as RuinLayerLogic;
            var editorMapData = Map.currentMap.data as EditorMapData;

            mLogic.UpdateGizmoVisibilityState();

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            layer.UpdateTransformChangedObjects();

            var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
            mPropertyEditor.Show(ruinSetting.properties, OnAddRuinProperty, OnRemoveRuinProperty, OnRenameRuinProperty);

            mLogic.operationType = ModelOperationType.kSelectObject;
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetLayerBoundsVisible(false);

                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
                if (layer != null)
                {
                    layer.SetVisible(false);
                }
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();

            var currentEvent = Event.current;

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (Event.current.button == 1 && Event.current.type == EventType.MouseDown)
            {
                GenericMenu menu = new GenericMenu();
                menu.AddItem(new GUIContent("Copy To Other Hordes"), false, CopyToOtherHordes, pos);
                menu.AddItem(new GUIContent("Remove Objects In Horde"), false, RemoveObjectsInHorde, pos);
                menu.ShowAsContext();

                SceneView.RepaintAll();
            }

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;

            if (mLogic.operationType == ModelOperationType.kCreateObject)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                        var worldPos = map.FromScreenToWorldPosition(screenPos);
                        if (layer.selectedObjectTypeIndex >= 0)
                        {
                            layer.AddObject(layer.ruinObjectTypes[layer.selectedObjectTypeIndex].name, worldPos, null, true, false);
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Add an object type first!", "OK");
                        }
                    }
                    Repaint();
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operationType == ModelOperationType.kRemoveObject)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        RemoveObject(screenPos);
                    }
                    Repaint();
                }

                HandleUtility.AddDefaultControl(0);
            }
            if (layer.selectedObjectTypeIndex >= 0)
            {
                var ruinType = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
                string objectType = ruinType.name;

                float bigGridWidth = layer.GetBigGridWidth(objectType);
                float bigGridHeight = layer.GetBigGridWidth(objectType);
                int horizontalBigGridCount = layer.GetHorizontalGridCount(objectType);
                int verticalBigGridCount = layer.GetVerticalGridCount(objectType);
                float startX = layer.GetStartX(objectType);
                float startZ = layer.GetStartZ(objectType);

                float totalWidth = bigGridWidth * horizontalBigGridCount;
                float totalHeight = bigGridHeight * verticalBigGridCount;
                if (totalWidth > 0 && totalHeight > 0)
                {
                    int h = horizontalBigGridCount;
                    int v = verticalBigGridCount;
                    Vector3 min = new Vector3(startX, 0, startZ);
                    for (int i = 0; i < v; ++i)
                    {
                        Handles.DrawLine(new Vector3(0, 0, i * bigGridHeight) + min, new Vector3(totalWidth, 0, i * bigGridHeight) + min);
                    }

                    for (int i = 0; i < h; ++i)
                    {
                        Handles.DrawLine(new Vector3(i * bigGridWidth, 0, 0) + min, new Vector3(i * bigGridWidth, 0, totalHeight) + min);
                    }

                    Vector3 max = min + new Vector3(totalWidth, 0, totalHeight);
                    Handles.DrawWireCube((min + max) * 0.5f, max - min);
                }
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;

                mLogic.DrawGizmoUI();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Remove All Objects Of Selected Object Type", "删除当前类型的所有点")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "This action can't be undone, are you sure?", "Yes", "No"))
                    {
                        var objectType = GetCurrentObjectType();
                        if (objectType != null)
                        {
                            layer.RemoveAllObjectsOfType(objectType);
                        }
                    }
                }
                if (GUILayout.Button(new GUIContent("Remove Duplicated Objects", "删除坐标重复的点")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "This action can't be undone, are you sure?", "Yes", "No"))
                    {
                        layer.RemoveDuplicatedObjects();
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Export Data", "导出该层运行时数据,一般修改地图后点Update Map即可")))
                {
                    if (layer.selectedObjectTypeIndex >= 0)
                    {
                        var ruinType = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
                        string objectType = ruinType.name;
                        float bigGridWidth = layer.GetBigGridWidth(objectType);
                        float bigGridHeight = layer.GetBigGridWidth(objectType);
                        int horizontalBigGridCount = layer.GetHorizontalGridCount(objectType);
                        int verticalBigGridCount = layer.GetVerticalGridCount(objectType);
                        float startX = layer.GetStartX(objectType);
                        float startZ = layer.GetStartZ(objectType);

                        var dlg = EditorWindow.GetWindow<ExportRuinDialog>();
                        dlg.Init(layer.layerData.id, mLogic.startID, startX, startZ, bigGridWidth, bigGridHeight, horizontalBigGridCount, verticalBigGridCount);
                    }
                }
                if (GUILayout.Button("Import Data"))
                {
                    string path = EditorUtility.OpenFilePanel("Select config file", "", "tsv");
                    if (!string.IsNullOrEmpty(path))
                    {
                        layer.ImportData(path);
                    }
                }
                EditorGUILayout.EndHorizontal();

                var editorMapData = Map.currentMap.data as EditorMapData;
                int n = layer.ruinObjectTypes.Count;
                if (mRuinObjectTypeNames == null || mRuinObjectTypeNames.Length != n)
                {
                    mRuinObjectTypeNames = new string[n];
                    for (int i = 0; i < n; ++i)
                    {
                        mRuinObjectTypeNames[i] = layer.ruinObjectTypes[i].name;
                    }
                }

                EditorGUILayout.BeginVertical("GroupBox");
                var newType = EditorGUILayout.Popup(new GUIContent("Object Type", "点的自定义类型"), layer.selectedObjectTypeIndex, mRuinObjectTypeNames);
                if (newType != layer.selectedObjectTypeIndex)
                {
                    SetCurrentObjectType(newType);
                }
                if (layer.selectedObjectTypeIndex >= 0)
                {
                    layer.ruinObjectTypes[layer.selectedObjectTypeIndex].alwaysDisplay = EditorGUILayout.ToggleLeft(new GUIContent("Always Display", "是否一直显示这个类型的点,即使切换到其他类型"), layer.ruinObjectTypes[layer.selectedObjectTypeIndex].alwaysDisplay);
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Add Object Type", "增加一种自定义点的类型")))
                {
                    var inputDialog = EditorUtils.CreateInputDialog("Add Object Type");

                    var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Type Name", "类型名称", ""),
                    };
                    inputDialog.Show(items, OnAddObjectType);
                }

                if (GUILayout.Button(new GUIContent("Remove Object Type", "删除当前的点类型,同时会清空所有当前类型的点")))
                {
                    RemoveCurrentObjectType();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();

                GUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Generate Random Points In Special Area", "针对当前选中的点类型,在指定的范围内将地图分为nxn个格子,在这些格子中生成随机点,可以使用障碍物边框排除某些区域")))
                {
                    if (layer.selectedObjectTypeIndex >= 0)
                    {
                        var ruinType = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
                        RandomWrapper wrapper = new RandomWrapper(Time.frameCount);
                        layer.GenerateRandomPointsInSpecialRegion(ruinType.name, null, wrapper);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "select and object type first!", "OK");
                    }
                }

                if (GUILayout.Button(new GUIContent("Generate All Random Points In Special Area", "对所有的点类型,在指定的范围内将地图分为nxn个格子,在这些格子中生成随机点,可以使用障碍物边框排除某些区域")))
                {
                    if (layer.selectedObjectTypeIndex >= 0)
                    {
                        RandomWrapper wrapper = new RandomWrapper(Time.frameCount);
                        layer.GenerateAllRandomPointsInSpecialRegion(wrapper);
                        var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
                        layer.ShowObjectsOfType(ruinSetting.name);

                        //CheckPointsOverlap(layer);
                    }
                }
                GUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Generate Random Points", "将地图划分为nxn的格子,然后每个格子内随机生成一个点,这个点的坐标可以按照某个数对齐,例如只在2结尾的坐标生成点")))
                {
                    mGeneratingInEmptyRegions = false;
                    GenerateRandomPoints();
                }

                if (GUILayout.Button(new GUIContent("Generate Random Points In Empty Region", "在没有ruin point的区域生成随机点")))
                {
                    mGeneratingInEmptyRegions = true;
                    GenerateRandomPoints();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Show Empty Regions", "显示没有ruin point的区域,方便手动添加")))
                {
                    ShowRegionsWithoutRuinPoint();
                }
                if (GUILayout.Button(new GUIContent("Hide Empty Region Marks", "隐藏没有ruin point的区域提示")))
                {
                    layer.HideRegionMarks();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Show Current Object Type Count", "统计当前类型的点的数量")))
                {
                    var currentObjectType = GetCurrentObjectType();
                    var objects = layer.GetObjectsOfType(currentObjectType);
                    EditorUtility.DisplayDialog("", $"{currentObjectType} Object Count: {objects.Count}", "OK");
                }

                if (GUILayout.Button(new GUIContent("Show All Objects", "同时显示所有的点")))
                {
                    layer.ShowAllObjects();
                }
                EditorGUILayout.EndHorizontal();

                if (MapModule.projectName == "k2")
                {
                    if (GUILayout.Button("初始化暗影区域设置"))
                    {
                        InitShadowAreaSettings();
                    }
                }

                if (MapModule.showHorde)
                {
                    bool isHordeVisible = layer.IsHordeVisible();
                    bool isVisible = EditorGUILayout.Toggle("Show Horde", isHordeVisible);
                    if (isVisible != isHordeVisible)
                    {
                        layer.ShowHorde(isVisible);
                    }
                    bool isTextVisible = EditorGUILayout.Toggle("Show Text", layer.IsTextVisible());
                    layer.ShowText(isTextVisible);

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.IntField("Horde Count", layer.hordeCount);
                    if (GUILayout.Button("Change"))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Set Horde Count");
                        var items = new List<InputDialog.Item> {
                            new InputDialog.StringItem("Horde Count", "", "5"),
                        };
                        dlg.Show(items, (List<InputDialog.Item> parameters) =>
                        {
                            int hordeCount;
                            string text = (parameters[0] as InputDialog.StringItem).text;
                            Utils.ParseInt(text, out hordeCount);
                            hordeCount = Mathf.Clamp(hordeCount, 1, 1000);
                            if (hordeCount != layer.hordeCount)
                            {
                                layer.SetHordeCount(hordeCount);
                            }
                            return true;
                        }
                        );

                    }
                    EditorGUILayout.EndHorizontal();
                }

                if (mLogic.operationType == ModelOperationType.kCreateObject)
                {
                    mLogic.operationType = (ModelOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                }
                else
                {
                    mLogic.operationType = (ModelOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                }

                float radius = EditorGUILayout.FloatField(new GUIContent("Ruin Display Radius", "点的显示半径,如果是模型,则设置为0.5就是模型的默认大小"), layer.ruinDisplayRadius);
                if (radius != layer.ruinDisplayRadius)
                {
                    layer.ruinDisplayRadius = radius;
                }

                if (layer.selectedObjectTypeIndex >= 0)
                {
                    var ruinType = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
                    float newRadius = EditorGUILayout.FloatField(new GUIContent("Ruin Radius", "点的运行时半径"), ruinType.colliderRadius);
                    if (newRadius < 0.1f)
                    {
                        newRadius = 0.1f;
                    }
                    ruinType.colliderRadius = newRadius;
                    layer.SetColliderRadius(newRadius);

                    EditorGUILayout.BeginHorizontal();
                    ruinType.color = EditorGUILayout.ColorField(new GUIContent("Ruin Color", "点的颜色"), ruinType.color);
                    ruinType.mtl.color = ruinType.color;
                    EditorGUILayout.EndHorizontal();
                    var newPrefab = EditorGUILayout.ObjectField(new GUIContent("Model", "点在编辑器中使用哪个模型显示"), ruinType.prefab, typeof(GameObject), false, null) as GameObject;
                    if (newPrefab != ruinType.prefab)
                    {
                        if (newPrefab == null || PrefabUtility.GetPrefabAssetType(newPrefab) == PrefabAssetType.Regular)
                        {
                            layer.OnRuinPrefabChanged(ruinType.name, newPrefab);
                        }
                    }

                    DrawSpecialRegionSetting(layer);
                }

                //draw properties
                if (layer.selectedObjectTypeIndex >= 0)
                {
                    mPropertyEditor.Draw();
                }

                var layerData = mLogic.layerData;
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Total Object Count", layerData.objectCount.ToString());
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        bool OnAddObjectType(List<InputDialog.Item> param)
        {
            var typeName = (param[0] as InputDialog.StringItem).text;
            if (!string.IsNullOrEmpty(typeName))
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
                var ruinSettings = layer.ruinObjectTypes;
                config.RuinSpecialRegionSetting setting = new config.RuinSpecialRegionSetting();
                if (ruinSettings.Count > 0)
                {
                    setting.bigGridWidth = ruinSettings[0].bigGridWidth;
                    setting.bigGridHeight = ruinSettings[0].bigGridHeight;
                    setting.startX = ruinSettings[0].startX;
                    setting.startZ = ruinSettings[0].startZ;
                    setting.horizontalBigGridCount = ruinSettings[0].horizontalBigGridCount;
                    setting.verticalBigGridCount = ruinSettings[0].verticalBigGridCount;
                    setting.pointCount = 0;
                    setting.invalidCircles = new List<int>();
                    setting.invalidCircles.AddRange(ruinSettings[0].invalidCircles);
                }
                var ruin = layer.AddRuinObjectType(typeName, Color.black, new PropertyDatas(null), 5.0f, setting);
                if (ruin != null)
                {
                    int typeCount = layer.ruinObjectTypes.Count;
                    SetCurrentObjectType(typeCount - 1);
                    return true;
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", string.Format("{0} is already existed!", typeName), "OK");
                }
            }
            return false;
        }

        void RemoveObject(Vector2 screenPos)
        {
            var layerID = mLogic.layerID;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
            var prefab = ruinSetting.prefab;
            var objects = layer.GetObjectsOfType(ruinSetting.name);
            if (prefab != null)
            {
                var worldPos2 = Utils.ToVector2(worldPos);
                for (int i = 0; i < objects.Count; ++i)
                {
                    if (objects[i].GetBounds().Contains(worldPos2))
                    {
                        var act = new ActionRemoveRuin(objects[i].GetEntityID(), layerID);
                        ActionManager.instance.PushAction(act);
                        break;
                    }
                }
            }
            else
            {
                float colliderRadius = layer.ruinDisplayRadius;
                for (int i = 0; i < objects.Count; ++i)
                {
                    var d = worldPos - objects[i].GetPosition();
                    if (d.sqrMagnitude <= colliderRadius * colliderRadius)
                    {
                        var act = new ActionRemoveRuin(objects[i].GetEntityID(), layerID);
                        ActionManager.instance.PushAction(act);
                        break;
                    }
                }
            }
        }

        string GetAssetGUID(GameObject prefab)
        {
            var assetPath = AssetDatabase.GetAssetPath(prefab);
            var guid = AssetDatabase.AssetPathToGUID(assetPath);
            return guid;
        }

        void CopyToOtherHordes(object posObj)
        {
            var pos = (Vector3)posObj;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var hordeIndex = layer.GetHordeIndex(pos);
            string objectType = GetCurrentObjectType();
            var objects = layer.GetObjectsInsideHorde(hordeIndex, objectType);
            int hordeCount = layer.hordeCount;
            float hordeAngle = layer.GetHordeAngle(hordeIndex);
            for (int i = 0; i < hordeCount; ++i)
            {
                if (i != hordeIndex)
                {
                    layer.RemoveObjectsInHorde(i, objectType);
                    float otherHordeAngle = layer.GetHordeAngle(i);
                    float difference = otherHordeAngle - hordeAngle;
                    var hordeRotation = Quaternion.Euler(0, difference, 0);
                    for (int o = 0; o < objects.Count; ++o)
                    {
                        var ruin = objects[o] as RuinData;
                        var modelTemplate = Map.currentMap.FindModelTemplate(ruin.GetAssetPath(0)) as ModelTemplate;
                        var newPos = hordeRotation * (ruin.GetPosition() - Map.currentMap.center) + Map.currentMap.center;
                        var ruinData = new RuinData(Map.currentMap.nextCustomObjectID, mLogic.layerID, Map.currentMap, 0, newPos, ruin.GetRotation(), ruin.GetScale(), modelTemplate, ruin.type, ruin.level, ruin.objectType, Color.black, ruin.properties);
                        layer.AddObject(ruinData);
                    }
                }
            }
        }

        void RemoveObjectsInHorde(object posObj)
        {
            var pos = (Vector3)posObj;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var hordeIndex = layer.GetHordeIndex(pos);
            string objectType = GetCurrentObjectType();
            layer.RemoveObjectsInHorde(hordeIndex, objectType);
        }

        void SetCurrentObjectType(int type)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            layer.selectedObjectTypeIndex = type;

            if (type >= 0)
            {   
                var ruinSetting = layer.ruinObjectTypes[type];
                layer.ShowObjectsOfType(ruinSetting.name);

                mPropertyEditor.Show(ruinSetting.properties, OnAddRuinProperty, OnRemoveRuinProperty, OnRenameRuinProperty);
            }
            else
            {
                mPropertyEditor.Show(null, null, null, null);
            }
        }

        void RemoveCurrentObjectType()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.selectedObjectTypeIndex >= 0)
            {
                if (EditorUtility.DisplayDialog("Warning", "Remove this object type will also remove all objects of this type and this operation can't be undone, continue?", "Yes", "No"))
                {
                    var currentObjectType = GetCurrentObjectType();
                    RemoveObjectType(currentObjectType);
                }
            }
        }

        void RemoveObjectType(string name)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            layer.RemoveRuinObjectType(name);
            layer.RemoveAllObjectsOfType(name);
            SetCurrentObjectType(layer.ruinObjectTypes.Count - 1);
        }

        string GetCurrentObjectType()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.selectedObjectTypeIndex >= 0)
            {
                return mRuinObjectTypeNames[layer.selectedObjectTypeIndex];
            }
            else
            {
                Debug.Assert(false);
            }
            return null;
        }

        void ShowRegionsWithoutRuinPoint()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.selectedObjectTypeIndex >= 0)
            {
                var dlg = EditorUtils.CreateInputDialog("Set Region Parameter");
                string width = mLogic.lastRegionWidth == 0 ? "180" : mLogic.lastRegionWidth.ToString();
                string height = mLogic.lastRegionHeight == 0 ? "180" : mLogic.lastRegionHeight.ToString();
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Grid Width", "格子宽", width),
                    new InputDialog.StringItem("Grid Height", "格子高", height),
                };
                dlg.Show(items, OnClickShowRegionsWithoutRuinPoint);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "select and object type first!", "OK");
            }
        }

        void GenerateRandomPoints()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.selectedObjectTypeIndex >= 0)
            {
                var dlg = EditorUtils.CreateInputDialog("Set Parameter");
                string width = mLogic.lastRegionWidth == 0 ? "180" : mLogic.lastRegionWidth.ToString();
                string height = mLogic.lastRegionHeight == 0 ? "180" : mLogic.lastRegionHeight.ToString();
                bool recreateRegions = mGeneratingInEmptyRegions ? false : true;
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Grid Width", "格子宽", width),
                    new InputDialog.StringItem("Grid Height", "格子高", height),
                    new InputDialog.StringItem("Alignment", "随机生成的坐标按多少米对齐", "0"),
                    new InputDialog.BoolItem("Avoid Decoration Objects", "生成点是否避开装饰物", true),
                    new InputDialog.BoolItem("Recreate Regions", "是否重新计算区域.如果障碍物区域未改变,可以不用每次生成都重新计算,可以节约时间", recreateRegions),
                };
                dlg.Show(items, OnClickGenerateRandomRuins);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "select and object type first!", "OK");
            }
        }

        bool OnClickShowRegionsWithoutRuinPoint(List<InputDialog.Item> parameters)
        {
            string regionWidthStr = (parameters[0] as InputDialog.StringItem).text;
            string regionHeightStr = (parameters[1] as InputDialog.StringItem).text;
            float regionWidth;
            float regionHeight;
            bool suc1 = Utils.ParseFloat(regionWidthStr, out regionWidth);
            bool suc2 = Utils.ParseFloat(regionHeightStr, out regionHeight);
            if (!suc1 || !suc2)
            {
                return false;
            }

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];
            layer.ShowRegionWithNoRuinPoint(ruinSetting.name, regionWidth, regionHeight);

            return true;
        }

        bool OnClickGenerateRandomRuins(List<InputDialog.Item> parameters)
        {
            string regionWidthStr = (parameters[0] as InputDialog.StringItem).text;
            string regionHeightStr = (parameters[1] as InputDialog.StringItem).text;
            string alignmentStr = (parameters[2] as InputDialog.StringItem).text;
            bool avoidDecorationObjects = (parameters[3] as InputDialog.BoolItem).value;
            bool recreateRegions = (parameters[4] as InputDialog.BoolItem).value;
            float regionWidth;
            float regionHeight;
            float alignment;
            bool suc1 = Utils.ParseFloat(regionWidthStr, out regionWidth);
            bool suc2 = Utils.ParseFloat(regionHeightStr, out regionHeight);
            bool suc3 = Utils.ParseFloat(alignmentStr, out alignment);
            if (!suc1 || !suc2 || !suc3)
            {
                return false;
            }
            if (regionWidth <= 0 || regionHeight <= 0 || alignment < 0)
            {
                return false;
            }

            mLogic.lastRegionWidth = regionWidth;
            mLogic.lastRegionHeight = regionHeight;
            
            var map = Map.currentMap;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];

            MapRegionManager regionManager = null;
            if (!mGeneratingInEmptyRegions)
            {
                recreateRegions = true;
            }
            if (recreateRegions || mLogic.lastRegionManager == null)
            {
                regionManager = new MapRegionManager();
                regionManager.CreateRegions(0, 0, map.mapWidth, map.mapHeight, regionWidth, regionHeight, ruinSetting.colliderRadius, PrefabOutlineType.ObjectPlacementObstacle, CheckMapCollisionOperation.kGenerateRuinPoints, false, true);
            }
            else
            {
                regionManager = mLogic.lastRegionManager;
            }
            mLogic.lastRegionManager = regionManager;
            ISpawnPointGenerationStrategy s = new GenerateOnePointInRegion(ruinSetting.colliderRadius, 0.5f, alignment, layer, avoidDecorationObjects);
            var regions = regionManager.regions;
            float mapRadius = map.mapWidth * 0.5f;

            var actions = new CompoundAction("Generate Random Objects");

            List<int> emptyRegions = null;
            if (mGeneratingInEmptyRegions)
            {
                emptyRegions = layer.GetEmptyRegions(ruinSetting.name, regionWidth, regionHeight);
            }
            else
            {
                emptyRegions = new List<int>(regions.Length);
                for (int i = 0; i < regions.Length; ++i)
                {
                    emptyRegions.Add(i);
                }
            }
            var r = new RandomWrapper((int)Time.time);
            for (int i = 0; i < emptyRegions.Count; ++i)
            {
                if (emptyRegions.Count > 1)
                {
                    EditorUtility.DisplayProgressBar("Generating", "Generating Random Points In Ruin Layer", ((float)i / (emptyRegions.Count - 1)));
                }
                var points = s.Generate(regions[emptyRegions[i]], mapRadius, r, null);
                for (int k = 0; k < points.Count; ++k)
                {
                    layer.AddObject(ruinSetting.name, new Vector3(points[k].x, 0, points[k].y), actions, true, false);
                }
            }
            EditorUtility.ClearProgressBar();

            if (actions != null && !actions.IsEmpty())
            {
                ActionManager.instance.PushAction(actions);
            }

            layer.ShowRegionWithNoRuinPoint(ruinSetting.name, regionWidth, regionHeight);

            return !mGeneratingInEmptyRegions;
        }

        void OnAddRuinProperty(PropertyBase property)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.shareProperties)
            {
                return;
            }

            var objects = layer.GetObjectsOfType(GetCurrentObjectType());
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                ruinData.properties.AddProperty(property.Clone());
            }
        }

        void OnRemoveRuinProperty(PropertyBase property)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.shareProperties)
            {
                return;
            }

            var objects = layer.GetObjectsOfType(GetCurrentObjectType());
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                ruinData.properties.RemoveProperty(property.name);
            }
        }

        void OnRenameRuinProperty(string oldName, PropertyBase property)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            if (layer.shareProperties)
            {
                return;
            }

            var objects = layer.GetObjectsOfType(GetCurrentObjectType());
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                var prop = ruinData.properties.FindProperty(oldName);
                prop.name = property.name;
            }
        }

        void DrawSpecialRegionSetting(RuinLayer layer)
        {
            if (layer.selectedObjectTypeIndex >= 0)
            {
                var ruinSetting = layer.ruinObjectTypes[layer.selectedObjectTypeIndex];

                EditorGUI.BeginChangeCheck(); 
                mDrawSpecialRegion = EditorGUILayout.Foldout(mDrawSpecialRegion, new GUIContent("Special Area Setting", "Generate Random Points In Special Area功能的参数"));
                if (mDrawSpecialRegion)
                {
                    EditorGUI.indentLevel++;
                    ruinSetting.bigGridWidth = EditorGUILayout.FloatField(new GUIContent("Grid Width", "大格子宽"), ruinSetting.bigGridWidth);
                    ruinSetting.bigGridHeight = EditorGUILayout.FloatField(new GUIContent("Grid Height", "大格子宽"), ruinSetting.bigGridHeight);
                    ruinSetting.startX = EditorGUILayout.FloatField(new GUIContent("Start X", "范围的起始X坐标"), ruinSetting.startX);
                    ruinSetting.startZ = EditorGUILayout.FloatField(new GUIContent("Start Z", "范围的起始Z坐标"), ruinSetting.startZ);
                    ruinSetting.horizontalBigGridCount = EditorGUILayout.IntField(new GUIContent("Horizontal Grid Count", "水平方向多少个大格子"), ruinSetting.horizontalBigGridCount);
                    ruinSetting.verticalBigGridCount = EditorGUILayout.IntField(new GUIContent("Vertical Grid Count", "垂直方向多少个大格子"), ruinSetting.verticalBigGridCount);
                    ruinSetting.pointCount = EditorGUILayout.IntField(new GUIContent("Point Count In One Grid", "每个大格子生成的点个数"), ruinSetting.pointCount);
                    DrawInvalidCircleList(ruinSetting.name, layer);
                    EditorGUI.indentLevel--;
                }
                bool changed = EditorGUI.EndChangeCheck();
                if (changed)
                {
                    SceneView.RepaintAll();
                }
            }
        }

        void DrawInvalidCircleList(string objectType, RuinLayer layer)
        {
            var invalidCircles = layer.GetInvalidCircles(objectType);
            int newCount = EditorGUILayout.IntField("Invalid Circle Count", invalidCircles.Count);
            if (newCount != invalidCircles.Count)
            {
                layer.ResizeInvalidCircleList(objectType, newCount);
            }

            for (int i = 0; i < newCount; ++i)
            {
                EditorGUI.indentLevel++;
                invalidCircles[i] = EditorGUILayout.IntField($"Invalid Circle {i}", invalidCircles[i]);
                EditorGUI.indentLevel--;
            }
        }

        //检查layer里有没有点重叠
        void CheckPointsOverlap(RuinLayer layer)
        {
            List<IMapObjectData> points = new List<IMapObjectData>();
            layer.GetAllObjects(points);
            
            for (int i = 0; i < points.Count; ++i)
            {
                var a = points[i] as RuinData;
                for (int j = i + 1; j < points.Count; ++j)
                {
                    var b = points[j] as RuinData;
                    var d = a.GetPosition() - b.GetPosition();
                    float r = a.radius + b.radius;
                    if (d.sqrMagnitude < r * r)
                    {
                        Debug.LogError($"point overlap {a.GetPosition()}, {b.GetPosition()}");
                    }
                }
            }
        }

        RuinLayerLogic mLogic;
        string[] mRuinObjectTypeNames;
        bool mGeneratingInEmptyRegions = false;
        PropertyDataEditor mPropertyEditor = new PropertyDataEditor(true);
        //for generate special points in region
        bool mDrawSpecialRegion = true;
    }
}

#endif