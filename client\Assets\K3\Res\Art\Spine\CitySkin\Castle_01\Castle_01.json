{"skeleton": {"hash": "RFSxJCthTE8", "spine": "4.2.33", "x": -424.13, "y": -206.82, "width": 815, "height": 804, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "sign2", "parent": "root", "x": 35.9, "y": -75.34}, {"name": "sign", "parent": "root", "x": -249.7, "y": 402}, {"name": "tree_L", "parent": "root", "x": -258, "y": 53.34}, {"name": "tree_R", "parent": "root", "length": 80.06, "rotation": 82.71, "x": 204.1, "y": 124.8}, {"name": "shadow", "parent": "root", "x": 31.14, "y": -96.85}, {"name": "bone", "parent": "root", "length": 19.04, "rotation": 64.65, "x": 92.37, "y": 507}, {"name": "bone2", "parent": "bone", "length": 22.57, "rotation": 41.05, "x": 19.04}, {"name": "bone3", "parent": "bone2", "length": 22.36, "rotation": 0.16, "x": 22.57}, {"name": "bone4", "parent": "bone3", "length": 18.45, "rotation": -45.26, "x": 22.36}, {"name": "bone5", "parent": "bone4", "length": 18.41, "rotation": -17.1, "x": 18.45}, {"name": "tree_L2", "parent": "tree_L", "length": 71.68, "rotation": 92.19, "x": -0.12, "y": 10.47}, {"name": "tree_L3", "parent": "tree_L2", "length": 76.53, "rotation": -0.6, "x": 71.68}, {"name": "tree_L4", "parent": "tree_L3", "length": 67.36, "rotation": -1.86, "x": 76.53}, {"name": "tree_L5", "parent": "tree_L4", "length": 51.84, "rotation": -1.43, "x": 67.36}, {"name": "tree_L6", "parent": "tree_L5", "length": 56.51, "rotation": -2.03, "x": 51.84}, {"name": "tree_L7", "parent": "tree_L2", "length": 48.04, "rotation": -54.67, "x": 86.95, "y": -26.46}, {"name": "tree_L8", "parent": "tree_L7", "length": 59.77, "rotation": 4.79, "x": 48.04}, {"name": "tree_L9", "parent": "tree_L8", "length": 61.2, "rotation": -5.61, "x": 59.77}, {"name": "tree_L10", "parent": "tree_L3", "length": 48.83, "rotation": 34.77, "x": 18.94, "y": 19.02}, {"name": "tree_L11", "parent": "tree_L10", "length": 44.66, "rotation": 11.12, "x": 48.83}, {"name": "tree_L12", "parent": "tree_L11", "length": 58.71, "rotation": 12.62, "x": 44.66}, {"name": "tree_R2", "parent": "tree_R", "length": 56.31, "rotation": 1.12, "x": 82.13}, {"name": "tree_R3", "parent": "tree_R2", "length": 56.38, "rotation": -0.61, "x": 56.31}, {"name": "tree_R4", "parent": "tree_R3", "length": 58.76, "rotation": -10.91, "x": 56.38}, {"name": "tree_R5", "parent": "tree_R", "length": 42.26, "rotation": -44.7, "x": 88.08, "y": -26.71}, {"name": "tree_R6", "parent": "tree_R5", "length": 45.77, "rotation": -21.5, "x": 42.26}, {"name": "tree_R7", "parent": "tree_R6", "length": 46.85, "rotation": -0.78, "x": 45.77}], "slots": [{"name": "tree_R", "bone": "tree_R", "attachment": "tree_R"}, {"name": "tail", "bone": "root", "attachment": "tail"}, {"name": "room", "bone": "root", "attachment": "room"}, {"name": "light_floor", "bone": "root", "color": "ffffff64", "attachment": "light_floor"}, {"name": "tree_L", "bone": "tree_L", "attachment": "tree_L"}, {"name": "room2", "bone": "root", "attachment": "room"}, {"name": "sign", "bone": "sign", "attachment": "sign"}, {"name": "shadow", "bone": "shadow", "attachment": "shadow"}, {"name": "light_O", "bone": "root", "color": "ffffff00", "attachment": "light_O", "blend": "additive"}, {"name": "light_O2", "bone": "root", "color": "ffffff00", "attachment": "light_O", "blend": "additive"}, {"name": "light", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light4", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light5", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light6", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light7", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light8", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light3", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "light2", "bone": "root", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "sign2", "bone": "sign2", "attachment": "sign2"}], "skins": [{"name": "default", "attachments": {"light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 2, 4, 5, 1, 2, 5, 1, 5, 0], "vertices": [-165.12, 22.26, -124.05, 29.35, -100.56, 21.5, -100.56, -3.32, -123.75, -30.42, -144.43, -4.08], "hull": 6, "edges": [2, 4, 4, 6, 8, 10, 10, 0, 0, 2, 6, 8], "width": 118, "height": 97}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.90909, 0, 0.81818, 0, 0.72727, 0, 0.63636, 0, 0.54545, 0, 0.45455, 0, 0.36364, 0, 0.27273, 0, 0.18182, 0, 0.09091, 0, 0, 1, 0, 1, 0.09091, 1, 0.18182, 1, 0.27273, 1, 0.36364, 1, 0.45455, 1, 0.54545, 1, 0.63636, 1, 0.72727, 1, 0.81818, 1, 0.90909], "triangles": [11, 12, 13, 11, 13, 14, 10, 11, 14, 10, 14, 15, 9, 10, 15, 9, 15, 16, 8, 9, 16, 8, 16, 17, 7, 8, 17, 7, 17, 18, 6, 7, 18, 6, 18, 19, 5, 6, 19, 5, 19, 20, 4, 5, 20, 4, 20, 21, 3, 4, 21, 3, 21, 22, 2, 3, 22, 2, 22, 23, 1, 2, 23, 1, 23, 0], "vertices": [-201.04, 243.93, -82.24, 287.91, -73.06, 284.65, -73.27, 279.49, -65.78, 281.73, -52.87, 277.42, -52.84, 272.35, -46.89, 274.5, -30.95, 268.88, -30.92, 233.01, -34.54, 226.09, -30.78, 224.42, -31.61, 192.15, -90.96, 121.47, -100.97, 132.61, -110.98, 143.74, -120.99, 154.87, -130.99, 166, -141, 177.14, -151.01, 188.27, -161.01, 199.4, -171.02, 210.53, -181.03, 221.66, -191.04, 232.8], "hull": 24, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0, 0, 2, 24, 26], "width": 118, "height": 97}}, "light3": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.875, 0, 0.75, 0, 0.625, 0, 0.5, 0, 0.375, 0, 0.25, 0, 0.125, 0, 0, 1, 0, 1, 0.125, 1, 0.25, 1, 0.375, 1, 0.5, 1, 0.625, 1, 0.75, 1, 0.875], "triangles": [8, 9, 10, 8, 10, 11, 7, 8, 11, 7, 11, 12, 6, 7, 12, 6, 12, 13, 5, 6, 13, 5, 13, 14, 4, 5, 14, 4, 14, 15, 3, 4, 15, 3, 15, 16, 2, 3, 16, 2, 16, 17, 1, 2, 17, 1, 17, 0], "vertices": [-222.71, 297.81, -117.69, 311.5, -108.56, 305.08, -108.72, 300.39, -101.25, 299.67, -93.86, 294.89, -93.86, 259.1, -98.03, 254.43, -93.86, 252.08, -93.86, 218.54, -147.85, 176.34, -157.21, 191.52, -166.57, 206.71, -175.93, 221.89, -185.28, 237.07, -194.64, 252.26, -204, 267.44, -213.36, 282.63], "hull": 18, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 18, 20], "width": 118, "height": 97}}, "light4": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 2, 4, 5, 1, 2, 5, 1, 5, 0], "vertices": [-93.94, 0.62, -72.15, 12.95, -56.02, 10.63, -56.02, -7.63, -62.51, -25.85, -78.22, -12.61], "hull": 6, "edges": [2, 4, 4, 6, 8, 10, 10, 0, 0, 2, 6, 8], "width": 118, "height": 97}}, "light5": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 2, 4, 5, 1, 2, 5, 1, 5, 0], "vertices": [52.31, -34.92, 38.32, -6.35, 38.57, 12.64, 53.77, 16.81, 90.52, -5.01, 72.16, -19.47], "hull": 6, "edges": [2, 4, 4, 6, 8, 10, 10, 0, 0, 2, 6, 8], "width": 118, "height": 97}}, "light6": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 2, 4, 5, 1, 2, 5, 1, 5, 0], "vertices": [24.26, -38.24, 19.23, -8.39, 19.48, 8.02, 34.68, 11.6, 65.19, -16.19, 38.33, -31.18], "hull": 6, "edges": [2, 4, 4, 6, 8, 10, 10, 0, 0, 2, 6, 8], "width": 118, "height": 97}}, "light7": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-21.59, -37.96, -12.9, 7, 6.32, 7.14, 24.74, -34.07], "hull": 4, "edges": [0, 2, 4, 6, 2, 4, 6, 0], "width": 118, "height": 97}}, "light8": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-63.24, -33.15, -39.57, 8.06, -15.57, 7.04, -16.81, -35.62], "hull": 4, "edges": [0, 2, 4, 6, 2, 4, 6, 0], "width": 118, "height": 97}}, "light_floor": {"light_floor": {"type": "mesh", "uvs": [0.81188, 0.23304, 0.99877, 0.54328, 0.71905, 0.99999, 0.23248, 0.81279, 0.00924, 0.46864, 0.01047, 0.2586, 0.56538, 0.00088], "triangles": [6, 4, 5, 3, 6, 0, 2, 3, 0, 3, 4, 6, 1, 2, 0], "vertices": [-128.07, -8.89, -108.26, -39.6, -137.91, -84.82, -189.49, -66.29, -213.15, -32.21, -213.02, -11.42, -154.2, 14.09], "hull": 7, "edges": [0, 12, 4, 6, 6, 8, 8, 10, 10, 12, 0, 2, 2, 4], "width": 106, "height": 99}}, "light_O": {"light_O": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [67.79, 149.98, -54.95, 149.98, -54.95, 272.73, 67.79, 272.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 148}}, "light_O2": {"light_O": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [69.07, 194.63, -53.67, 194.63, -53.67, 317.38, 69.07, 317.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 148}}, "room": {"room": {"type": "mesh", "uvs": [0.37312, 0, 0.54751, 0.01741, 0.77073, 0.2193, 0.74177, 0.35328, 0.79924, 0.40221, 0.85299, 0.51983, 0.85736, 0.56314, 0.84731, 0.56621, 1, 0.74613, 1, 0.76822, 0.54231, 0.9958, 0.49766, 1, 0.45665, 1, 0, 0.71348, 0, 0.68341, 0.02861, 0.60876, 0.19835, 0.52113, 0.19678, 0.47201, 0.2099, 0.41491, 0.31222, 0.34927, 0.3105, 0.33847, 0.25118, 0.22913, 0.36335, 0.13631, 0.34581, 0.13808, 0.36688, 0], "triangles": [22, 23, 24, 20, 21, 22, 3, 19, 20, 2, 22, 1, 19, 16, 17, 7, 4, 5, 7, 5, 6, 13, 14, 15, 7, 8, 9, 22, 24, 0, 1, 22, 0, 3, 7, 12, 7, 3, 4, 10, 7, 9, 12, 7, 11, 3, 22, 2, 22, 3, 20, 3, 16, 19, 19, 17, 18, 3, 12, 16, 13, 15, 16, 12, 13, 16, 10, 11, 7], "vertices": [-96.85, 597.18, 38.83, 583.18, 212.49, 420.86, 189.97, 313.14, 234.68, 273.8, 276.49, 179.24, 279.9, 144.41, 272.08, 141.95, 390.87, -2.7, 390.87, -20.47, 34.79, -203.44, 0.05, -206.82, -31.86, -206.82, -387.13, 23.54, -387.13, 47.72, -364.87, 107.74, -232.81, 178.19, -234.04, 217.69, -223.83, 263.59, -144.22, 316.37, -145.56, 325.05, -191.71, 412.96, -104.45, 487.59, -118.09, 486.16, -101.7, 597.18], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 778, "height": 804}}, "room2": {"room": {"type": "mesh", "uvs": [0.48201, 0.15918, 0.50217, 0.68256, 0.20732, 0.66596, 0.20727, 0.61131, 0.20134, 0.59679, 0.19662, 0.59586, 0.19226, 0.57279, 0.19166, 0.55967, 0.19468, 0.54129, 0.20073, 0.51529, 0.19678, 0.47201, 0.2099, 0.41491, 0.31222, 0.34927, 0.3105, 0.33847, 0.25118, 0.22913, 0.36335, 0.13631], "triangles": [13, 14, 15, 13, 15, 0, 12, 13, 0, 12, 9, 10, 4, 6, 7, 7, 8, 4, 5, 6, 4, 3, 4, 9, 4, 8, 9, 9, 1, 3, 1, 2, 3, 12, 0, 1, 12, 10, 11, 9, 12, 1], "vertices": [-12.13, 469.2, 3.56, 48.4, -225.84, 61.75, -225.88, 105.68, -230.49, 117.36, -234.16, 118.11, -237.55, 136.66, -238.02, 147.2, -235.67, 161.99, -230.96, 182.89, -234.04, 217.69, -223.83, 263.59, -144.22, 316.37, -145.56, 325.05, -191.71, 412.96, -104.45, 487.59], "hull": 16, "edges": [20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 30], "width": 778, "height": 804}}, "shadow": {"shadow": {"type": "mesh", "uvs": [0.53101, 0.00125, 0.99999, 0.56638, 0.53652, 0.99999, 0.34237, 0.86151, 0.09881, 0.54699, 0, 0.28932, 0.38377, 0.03868], "triangles": [4, 5, 6, 3, 4, 6, 2, 0, 1, 3, 6, 0, 2, 3, 0], "vertices": [-5.79, 25.98, 31.73, 1.11, -5.35, -17.97, -20.88, -11.88, -40.37, 1.96, -48.27, 13.3, -17.57, 24.33], "hull": 7, "edges": [0, 12, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 2], "width": 80, "height": 44}}, "sign": {"sign": {"type": "mesh", "uvs": [0.98537, 0.02742, 0.98537, 0.86148, 0.15208, 0.99849, 0, 0.97095, 0.01756, 0.11654, 0.84397, 0, 0.1879, 0.1411], "triangles": [6, 4, 5, 6, 5, 0, 1, 6, 0, 3, 4, 6, 2, 3, 6, 1, 2, 6], "vertices": [30.56, 95.7, 30.56, -71.11, -26.93, -98.52, -37.43, -93.01, -36.22, 77.87, 20.81, 101.18, -24.46, 72.96], "hull": 6, "edges": [8, 10, 10, 0, 0, 2, 6, 8, 4, 6, 2, 4, 12, 4, 12, 0, 12, 8], "width": 69, "height": 200}}, "sign2": {"sign2": {"type": "mesh", "uvs": [0.80873, 0.04566, 0.91591, 0.41266, 0.98949, 0.77536, 0.39074, 1, 0.3248, 0.9521, 0.24058, 0.69282, 0.05215, 0.75401, 0.00763, 0.72167, 0.14327, 0.246, 0.21777, 0.18652, 0.69505, 0.01073, 0.19868, 0.27162, 0.17527, 0.39297], "triangles": [11, 8, 9, 11, 9, 10, 11, 10, 0, 12, 8, 11, 11, 0, 1, 11, 5, 12, 11, 3, 5, 7, 8, 12, 6, 7, 12, 5, 6, 12, 11, 1, 3, 3, 4, 5, 1, 2, 3], "vertices": [17.72, 37.82, 24.58, 8.09, 29.29, -21.29, -9.03, -39.48, -13.25, -35.6, -18.64, -14.6, -30.7, -19.56, -33.55, -16.94, -24.86, 21.59, -20.1, 26.41, 10.45, 40.65, -21.32, 19.51, -22.82, 9.69], "hull": 11, "edges": [4, 6, 14, 16, 20, 0, 16, 22, 22, 0, 22, 6, 16, 18, 18, 20, 12, 14, 12, 10, 6, 8, 10, 8, 10, 24, 24, 16, 24, 12, 0, 2, 2, 4], "width": 64, "height": 81}}, "tail": {"tail": {"type": "mesh", "uvs": [0.69478, 0.006, 0.78483, 0.02562, 0.79999, 0.07199, 0.78855, 0.12012, 0.76851, 0.20445, 0.77003, 0.29544, 0.86483, 0.37128, 0.95603, 0.46729, 1, 0.57867, 0.99999, 0.67568, 0.96239, 0.77139, 0.82489, 0.91477, 0.61022, 0.99465, 0.42469, 0.99912, 0.33122, 0.92719, 0.30284, 0.80371, 0.2096, 0.72306, 0.0843, 0.63503, 0.01108, 0.53585, 0.00454, 0.41573, 0.08227, 0.2857, 0.2209, 0.18026, 0.34418, 0.10301, 0.55607, 0.02416, 0.5457, 0.9209, 0.60732, 0.80682, 0.63088, 0.69029, 0.58557, 0.59462, 0.54026, 0.51857, 0.4732, 0.43026, 0.44602, 0.32477, 0.49314, 0.22787, 0.58014, 0.12974, 0.67438, 0.06841], "triangles": [33, 23, 0, 33, 0, 1, 33, 1, 2, 3, 33, 2, 32, 23, 33, 22, 23, 32, 3, 32, 33, 4, 32, 3, 31, 32, 4, 31, 22, 32, 21, 22, 31, 30, 21, 31, 5, 31, 4, 20, 21, 30, 5, 29, 30, 30, 31, 5, 6, 29, 5, 6, 28, 29, 29, 18, 19, 19, 20, 29, 29, 20, 30, 17, 29, 28, 17, 18, 29, 16, 17, 28, 28, 6, 7, 27, 28, 7, 27, 7, 8, 8, 26, 27, 9, 26, 8, 16, 28, 27, 15, 16, 27, 10, 26, 9, 26, 15, 27, 25, 26, 10, 25, 15, 26, 11, 25, 10, 24, 15, 25, 24, 25, 11, 14, 15, 24, 12, 24, 11, 13, 14, 24, 13, 24, 12], "vertices": [1, 10, 14.85, 3.93, 1, 1, 10, 17.89, -1.63, 1, 3, 8, 37.5, -29.43, 0.0018, 9, 31.57, -9.96, 0.00388, 10, 15.47, -5.66, 0.99432, 3, 8, 33.13, -27.39, 0.01526, 9, 27.04, -11.63, 0.05106, 10, 11.63, -8.59, 0.93368, 4, 7, 48.1, -23.75, 0.0073, 8, 25.47, -23.82, 0.12907, 9, 19.11, -14.56, 0.32472, 10, 4.91, -13.72, 0.53891, 4, 7, 39.41, -21.41, 0.08732, 8, 16.77, -21.46, 0.43797, 9, 11.31, -19.07, 0.33872, 10, -1.22, -20.32, 0.136, 4, 7, 30.46, -25.49, 0.32874, 8, 7.81, -25.51, 0.51853, 9, 7.89, -28.29, 0.13149, 10, -1.78, -30.14, 0.02124, 5, 6, 52.77, -8.81, 0.00558, 7, 19.65, -28.8, 0.61397, 8, -3, -28.79, 0.34065, 9, 2.6, -38.28, 0.03871, 10, -3.89, -41.24, 0.0011, 4, 6, 44.07, -16.19, 0.06893, 7, 8.24, -28.65, 0.76605, 8, -14.41, -28.61, 0.15765, 9, -5.56, -46.26, 0.00737, 4, 6, 35.39, -20.3, 0.2216, 7, -1, -26.05, 0.71807, 8, -23.65, -25.98, 0.05986, 9, -13.92, -50.97, 0.00048, 3, 6, 25.75, -22.08, 0.49331, 7, -9.44, -21.06, 0.49421, 8, -32.07, -20.97, 0.01248, 2, 6, 8.97, -19.83, 0.95753, 7, -20.61, -8.35, 0.04247, 1, 6, -4.33, -10.22, 1, 1, 6, -10.05, 0.82, 1, 3, 6, -6.3, 9.53, 0.97471, 7, -12.84, 23.83, 0.02186, 8, -35.35, 23.92, 0.00343, 3, 6, 3.94, 16.48, 0.67131, 7, -0.56, 22.35, 0.24704, 8, -23.07, 22.41, 0.08165, 3, 6, 8.48, 25.55, 0.29033, 7, 8.82, 26.2, 0.39229, 8, -13.68, 26.24, 0.31738, 4, 6, 12.76, 36.87, 0.09372, 7, 19.48, 31.92, 0.27052, 8, -3.01, 31.93, 0.63562, 9, -40.53, 4.46, 0.00014, 4, 6, 19.54, 45.5, 0.03152, 7, 30.26, 33.99, 0.14402, 8, 7.78, 33.96, 0.80896, 9, -34.38, 13.55, 0.0155, 4, 6, 30.09, 50.99, 0.00571, 7, 41.83, 31.19, 0.05025, 8, 19.34, 31.13, 0.85329, 9, -24.24, 19.77, 0.09075, 3, 7, 52.81, 22.69, 0.0034, 8, 30.3, 22.61, 0.64966, 9, -10.47, 21.55, 0.34694, 3, 8, 37.8, 10.82, 0.18168, 9, 3.19, 18.58, 0.81727, 10, -20.05, 13.28, 0.00104, 3, 8, 42.9, 0.78, 0.0025, 9, 13.9, 15.14, 0.87857, 10, -8.79, 13.14, 0.11893, 2, 9, 27.67, 6.6, 0.01633, 10, 6.88, 9.03, 0.98367, 1, 6, 0.42, -3.19, 1, 1, 6, 12.39, -2.08, 1, 1, 7, 4.3, -1.85, 1, 3, 7, 14.24, -1.49, 0.99608, 8, -8.34, -1.47, 0.00379, 9, -20.56, -22.84, 0.00013, 3, 7, 22.31, -0.61, 0.62116, 8, -0.27, -0.61, 0.3785, 9, -15.49, -16.5, 0.00034, 2, 6, 42.24, 22, 7e-05, 8, 9.37, 1.32, 0.99993, 1, 8, 19.92, 0.22, 1, 1, 9, 8.03, 0.38, 1, 2, 9, 19.36, 0.07, 0.07416, 10, 0.85, 0.33, 0.92584, 1, 10, 9.61, 0.39, 1], "hull": 24, "edges": [16, 18, 2, 4, 14, 16, 8, 10, 10, 12, 12, 14, 18, 20, 22, 24, 20, 22, 24, 26, 26, 28, 34, 36, 32, 34, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 2, 0, 0, 46, 4, 6, 6, 8], "width": 67, "height": 99}}, "tree_L": {"tree_L": {"type": "mesh", "uvs": [0.54829, 0, 0.56011, 0, 0.59837, 0.03934, 0.61357, 0.04304, 0.6592, 0.02868, 0.70449, 0.07111, 0.74978, 0.11355, 0.80498, 0.16526, 0.8065, 0.20981, 0.83234, 0.23442, 0.85438, 0.23446, 0.85439, 0.25615, 0.80341, 0.29127, 0.89081, 0.31556, 0.90045, 0.38894, 0.91009, 0.46233, 0.87243, 0.47474, 0.87818, 0.53751, 0.93164, 0.52259, 1, 0.5873, 1, 0.59898, 0.96661, 0.65989, 0.91279, 0.65989, 0.92571, 0.67752, 0.91401, 0.72686, 0.86192, 0.72031, 0.83472, 0.71294, 0.79032, 0.75742, 0.75738, 0.73349, 0.71915, 0.74493, 0.67255, 0.74501, 0.66508, 0.66922, 0.60671, 0.67428, 0.57455, 0.68019, 0.55661, 0.70378, 0.52445, 0.73571, 0.52309, 0.80658, 0.52605, 0.85236, 0.52977, 0.91003, 0.53349, 0.9677, 0.51565, 0.98855, 0.47701, 0.99899, 0.45922, 0.99875, 0.41556, 0.97223, 0.41361, 0.91083, 0.41181, 0.85432, 0.41012, 0.80116, 0.40827, 0.74318, 0.39271, 0.70192, 0.36784, 0.66435, 0.35813, 0.64541, 0.33381, 0.65446, 0.33506, 0.69765, 0.32022, 0.73125, 0.27009, 0.717, 0.24347, 0.70981, 0.20964, 0.73375, 0.18268, 0.70707, 0.17821, 0.68987, 0.08554, 0.70504, 0.0856, 0.69088, 0.07691, 0.66559, 0.07717, 0.63645, 0.03236, 0.63656, 0, 0.57792, 0, 0.56557, 0.03479, 0.53788, 0.08162, 0.49137, 0.12369, 0.50752, 0.12626, 0.44655, 0.08965, 0.43746, 0.09948, 0.3649, 0.10931, 0.29234, 0.19685, 0.26752, 0.14888, 0.23535, 0.14283, 0.21179, 0.19989, 0.18165, 0.25694, 0.15151, 0.25012, 0.12721, 0.30433, 0.07779, 0.34004, 0.01196, 0.3591, 0.01048, 0.39358, 0.04117, 0.43225, 0.04071, 0.46011, 0.01502, 0.39761, 0.63594, 0.44232, 0.59005, 0.52071, 0.60011, 0.57093, 0.62777, 0.52081, 0.66311, 0.49234, 0.4878, 0.50578, 0.36039, 0.51843, 0.2492, 0.5382, 0.13477, 0.65444, 0.10717, 0.66393, 0.19401, 0.65839, 0.28491, 0.64179, 0.39366, 0.6236, 0.50404, 0.64732, 0.60629, 0.75723, 0.63957, 0.86318, 0.61279, 0.93593, 0.60629, 0.77858, 0.52919, 0.72876, 0.40827, 0.83867, 0.42531, 0.80496, 0.25478, 0.4453, 0.11532, 0.55047, 0.04552, 0.35358, 0.06825, 0.31009, 0.14454, 0.4287, 0.1981, 0.27214, 0.2192, 0.39944, 0.27926, 0.25704, 0.31544, 0.38592, 0.38849, 0.35773, 0.51044, 0.14345, 0.39682, 0.23042, 0.51369, 0.25731, 0.41284, 0.05539, 0.57361, 0.13825, 0.58231, 0.23421, 0.61628, 0.12876, 0.66304, 0.35414, 0.58552], "triangles": [95, 7, 8, 7, 95, 6, 106, 8, 9, 106, 95, 8, 96, 95, 106, 12, 96, 106, 11, 106, 9, 11, 9, 10, 12, 106, 11, 104, 96, 12, 97, 96, 104, 105, 12, 13, 105, 13, 14, 104, 12, 105, 105, 14, 15, 16, 105, 15, 103, 104, 105, 103, 105, 16, 103, 16, 17, 102, 18, 19, 104, 98, 97, 103, 98, 104, 103, 99, 98, 20, 102, 19, 17, 18, 102, 101, 103, 17, 101, 17, 102, 100, 99, 103, 100, 103, 101, 21, 102, 20, 22, 101, 102, 22, 102, 21, 26, 100, 101, 26, 101, 22, 25, 26, 22, 25, 22, 23, 24, 25, 23, 28, 100, 26, 29, 31, 100, 28, 29, 100, 30, 31, 29, 27, 28, 26, 88, 87, 98, 89, 87, 88, 32, 88, 99, 33, 88, 32, 89, 88, 33, 34, 89, 33, 35, 89, 34, 89, 48, 87, 99, 88, 98, 31, 99, 100, 32, 99, 31, 114, 73, 112, 117, 72, 73, 117, 73, 114, 71, 72, 117, 119, 114, 115, 117, 114, 119, 70, 71, 117, 69, 70, 117, 119, 69, 117, 118, 69, 119, 68, 69, 118, 120, 66, 67, 120, 67, 68, 120, 68, 121, 74, 75, 76, 73, 76, 112, 73, 74, 76, 65, 66, 120, 118, 119, 116, 121, 68, 118, 124, 118, 116, 121, 118, 122, 123, 62, 121, 123, 121, 122, 61, 62, 123, 58, 123, 122, 60, 61, 123, 59, 60, 123, 59, 123, 58, 63, 120, 62, 64, 65, 120, 62, 120, 121, 63, 64, 120, 124, 116, 86, 122, 118, 124, 85, 124, 86, 50, 124, 85, 51, 122, 124, 50, 51, 124, 49, 50, 85, 48, 49, 85, 55, 57, 58, 54, 55, 122, 51, 54, 122, 55, 58, 122, 52, 54, 51, 53, 54, 52, 56, 57, 55, 85, 87, 48, 48, 89, 47, 108, 0, 1, 84, 0, 108, 109, 80, 81, 110, 79, 109, 78, 79, 110, 109, 81, 82, 79, 80, 109, 107, 83, 84, 82, 107, 109, 107, 82, 83, 107, 110, 109, 93, 111, 107, 94, 3, 4, 94, 4, 5, 6, 95, 94, 6, 94, 5, 95, 93, 94, 108, 1, 2, 107, 84, 108, 93, 107, 108, 2, 93, 108, 3, 93, 2, 3, 94, 93, 77, 78, 110, 112, 77, 110, 76, 77, 112, 111, 112, 110, 113, 112, 111, 111, 110, 107, 92, 111, 93, 113, 111, 92, 91, 113, 92, 91, 92, 96, 114, 112, 113, 96, 92, 95, 95, 92, 93, 90, 115, 91, 116, 119, 115, 116, 115, 90, 115, 114, 113, 115, 113, 91, 97, 91, 96, 90, 91, 97, 98, 90, 97, 86, 116, 90, 87, 90, 98, 86, 90, 87, 86, 87, 85, 39, 40, 38, 35, 47, 89, 47, 36, 46, 35, 36, 47, 45, 46, 36, 45, 36, 37, 44, 45, 37, 44, 37, 38, 44, 38, 42, 42, 43, 44, 38, 41, 42, 40, 41, 38], "vertices": [4, 14, 104.73, -27.4, 0.01599, 15, 53.82, -25.52, 0.95275, 21, -15.75, -193.42, 0, 18, 33.04, 177.39, 0.03126, 4, 14, 104.85, -31.54, 0.02125, 15, 54.09, -29.64, 0.94322, 21, -19.34, -195.49, 0, 18, 36.36, 174.91, 0.03553, 6, 13, 158.04, -47.59, 5e-05, 14, 91.83, -45.32, 0.10732, 15, 41.57, -43.87, 0.79953, 21, -37.63, -190.53, 0, 17, 113.94, 151.59, 0, 18, 39.08, 156.16, 0.09308, 6, 13, 156.8, -52.91, 0.00048, 14, 90.73, -50.67, 0.14381, 15, 40.66, -49.26, 0.73143, 21, -42.88, -192.08, 0, 17, 117.02, 147.07, 4e-05, 18, 42.59, 151.97, 0.12424, 5, 13, 161.77, -68.86, 0.00143, 14, 96.09, -66.49, 0.18759, 15, 46.58, -64.88, 0.63388, 21, -54.28, -204.29, 0, 18, 58.32, 146.35, 0.1771, 5, 13, 147.37, -84.78, 0.00948, 14, 82.09, -82.76, 0.22928, 15, 33.16, -81.64, 0.51787, 21, -75.23, -199.65, 0, 18, 62.38, 125.27, 0.24338, 6, 13, 132.97, -100.69, 0.02362, 14, 68.1, -99.03, 0.25249, 15, 19.75, -98.39, 0.37636, 21, -96.19, -195, 0, 17, 136.09, 97.2, 0.00014, 18, 66.45, 104.2, 0.3474, 6, 13, 115.43, -120.09, 0.03575, 14, 51.04, -118.86, 0.23796, 15, 3.4, -118.81, 0.26116, 21, -121.73, -189.34, 0, 17, 138.5, 71.15, 0.00042, 18, 71.4, 78.51, 0.46472, 6, 13, 100.24, -120.69, 0.04219, 14, 35.87, -119.84, 0.21455, 15, -11.72, -120.33, 0.20289, 21, -129.76, -176.44, 0, 17, 128.67, 59.56, 0.0031, 18, 62.75, 66.02, 0.53727, 6, 13, 91.89, -129.78, 0.04226, 14, 27.75, -129.12, 0.18731, 15, -19.51, -129.9, 0.15793, 21, -141.78, -173.67, 0, 17, 129.71, 47.27, 0.00132, 18, 64.98, 53.88, 0.61118, 5, 13, 91.91, -137.49, 0.04133, 14, 27.96, -136.84, 0.18372, 15, -19.03, -137.6, 0.15278, 21, -148.48, -177.5, 0, 18, 71.16, 49.26, 0.62217, 6, 13, 84.51, -137.53, 0.04151, 14, 20.57, -137.06, 0.18318, 15, -26.41, -138.08, 0.15208, 21, -152.17, -171.09, 0, 17, 130.43, 36.59, 0.00023, 18, 66.74, 43.33, 0.62299, 6, 13, 72.46, -119.74, 0.04515, 14, 8.07, -119.57, 0.13314, 15, -39.51, -121.05, 0.09645, 21, -142.66, -151.81, 0, 17, 109.17, 39.75, 0.0156, 18, 45.28, 44.39, 0.70967, 5, 13, 64.31, -150.36, 0.00533, 14, 0.69, -150.39, 0.01865, 15, -45.8, -152.11, 0.01186, 21, -173.31, -159.88, 0, 18, 64.85, 19.47, 0.96415, 1, 18, 52.61, -2.61, 1, 2, 17, 97.51, -28.52, 0.01061, 18, 40.36, -24.69, 0.98939, 2, 17, 84.92, -22.78, 0.10467, 18, 27.26, -20.21, 0.89533, 3, 16, 123.12, -33.81, 0.00123, 17, 72, -39.96, 0.59804, 18, 16.08, -38.57, 0.40074, 2, 17, 89.26, -48.8, 0.70077, 18, 34.12, -45.68, 0.29923, 2, 17, 92.1, -81.22, 0.74334, 18, 40.12, -77.67, 0.25666, 3, 16, 144.17, -76.41, 0.00013, 17, 89.42, -84.16, 0.74499, 18, 37.74, -80.86, 0.25488, 3, 16, 122.25, -85.76, 0.005, 17, 66.79, -91.66, 0.76378, 18, 15.96, -90.53, 0.23122, 3, 16, 107.31, -74.29, 0.02531, 17, 52.86, -78.98, 0.79479, 18, 0.86, -79.27, 0.1799, 3, 16, 107.23, -81.81, 0.03325, 17, 52.16, -86.47, 0.80827, 18, 0.89, -86.8, 0.15849, 3, 16, 93.74, -92.66, 0.04455, 17, 37.8, -96.15, 0.82164, 18, -12.45, -97.84, 0.13381, 3, 16, 80.64, -79.78, 0.0691, 17, 25.83, -82.23, 0.82007, 18, -25.73, -85.15, 0.11083, 3, 16, 74.62, -71.99, 0.11155, 17, 20.48, -73.96, 0.80999, 18, -31.86, -77.45, 0.07846, 3, 16, 53.06, -74.55, 0.20255, 17, -1.22, -74.71, 0.76639, 18, -53.39, -80.32, 0.03106, 3, 16, 48.89, -61.06, 0.26304, 17, -4.25, -60.92, 0.71891, 18, -57.75, -66.89, 0.01805, 3, 16, 35.9, -56, 0.37077, 17, -16.78, -54.8, 0.62551, 18, -70.82, -62.02, 0.00372, 2, 16, 22.94, -46.09, 0.43153, 17, -28.86, -43.84, 0.56847, 2, 16, 36.61, -24, 0.61994, 17, -13.39, -22.96, 0.38006, 2, 16, 19.36, -12.93, 0.98946, 17, -29.66, -10.49, 0.01054, 2, 11, 86.02, -38.41, 0.00042, 16, 9.2, -7.67, 0.99958, 3, 11, 78.22, -31.82, 0.04364, 12, 6.87, -31.75, 0.0129, 16, -0.68, -10.23, 0.94346, 3, 11, 67.77, -20.16, 0.4819, 12, -3.7, -20.2, 0.1524, 16, -16.23, -12.01, 0.3657, 2, 11, 43.64, -18.76, 0.97818, 16, -31.33, -30.88, 0.02182, 2, 11, 28, -19.2, 0.99929, 16, -40.02, -43.89, 0.00071, 2, 3, 19.29, 19.52, 0.06371, 11, 8.3, -19.75, 0.93629, 2, 3, 20.59, -0.14, 0.59621, 11, -11.4, -20.3, 0.40379, 2, 3, 14.35, -7.25, 0.8035, 11, -18.27, -13.78, 0.1965, 1, 3, 0.82, -10.81, 1, 2, 3, -5.4, -10.73, 0.99929, 11, -20.99, 6.09, 0.00071, 2, 3, -20.68, -1.69, 0.57221, 11, -11.37, 21.01, 0.42779, 2, 3, -21.37, 19.25, 0.10386, 11, 9.58, 20.89, 0.89614, 2, 11, 28.86, 20.78, 0.99912, 19, -50.16, 36.43, 0.00088, 2, 11, 47, 20.68, 0.96771, 19, -35.21, 26.16, 0.03229, 3, 11, 66.78, 20.57, 0.59352, 12, -5.11, 20.52, 0.13889, 19, -18.91, 14.95, 0.26758, 3, 11, 81.05, 25.47, 0.10447, 12, 9.1, 25.57, 0.0803, 19, -4.35, 10.99, 0.81523, 3, 11, 94.18, 33.68, 0.00214, 19, 11.13, 10.4, 0.99662, 20, -34.99, 17.48, 0.00125, 2, 19, 18.35, 9.31, 0.97726, 20, -28.12, 15.02, 0.02274, 3, 19, 20.91, 18, 0.88273, 20, -23.93, 23.05, 0.11702, 21, -61.89, 37.47, 0.00025, 2, 19, 8.79, 26.38, 0.82943, 20, -34.2, 33.6, 0.17057, 3, 19, 2.64, 37.35, 0.81659, 20, -38.12, 45.56, 0.18272, 21, -70.82, 62.54, 0.00068, 3, 19, 16.96, 48.6, 0.71204, 20, -21.9, 53.83, 0.26599, 21, -53.19, 67.07, 0.02198, 3, 19, 24.46, 54.65, 0.59565, 20, -13.37, 58.32, 0.35179, 21, -43.89, 69.59, 0.05256, 3, 19, 24.91, 69.02, 0.51315, 20, -10.16, 72.34, 0.40463, 21, -37.69, 82.57, 0.08222, 3, 19, 37.83, 71.23, 0.44672, 20, 2.94, 72.01, 0.43909, 21, -24.98, 79.38, 0.11419, 3, 19, 43.48, 69.01, 0.38985, 20, 8.06, 68.75, 0.46526, 21, -20.69, 75.08, 0.14489, 3, 19, 58.55, 98.19, 0.2117, 20, 28.47, 94.48, 0.50935, 21, 4.85, 95.73, 0.27896, 3, 19, 62.42, 95.31, 0.20984, 20, 31.72, 90.9, 0.50891, 21, 7.23, 91.53, 0.28125, 3, 19, 71.17, 92.65, 0.18825, 20, 39.79, 86.6, 0.50529, 21, 14.17, 85.57, 0.30646, 3, 19, 79.12, 86.68, 0.14372, 20, 46.44, 79.21, 0.49301, 21, 19.04, 76.91, 0.36327, 3, 19, 88.39, 99.33, 0.09743, 20, 57.97, 89.84, 0.47614, 21, 32.62, 84.76, 0.42642, 3, 19, 111.21, 96.59, 0.07546, 20, 79.83, 82.75, 0.4625, 21, 52.41, 73.07, 0.46204, 3, 19, 114.6, 94.1, 0.07432, 20, 82.68, 79.65, 0.46132, 21, 54.5, 69.42, 0.46436, 3, 19, 114.98, 78.69, 0.0702, 20, 80.08, 64.46, 0.45033, 21, 48.65, 55.16, 0.47947, 3, 19, 118.03, 56.09, 0.05721, 20, 78.72, 41.69, 0.41335, 21, 42.35, 33.24, 0.52944, 3, 19, 104.86, 47.5, 0.04741, 20, 64.14, 35.8, 0.35147, 21, 26.84, 30.68, 0.60112, 3, 19, 121.07, 34.45, 0.0029, 20, 77.53, 19.87, 0.03227, 21, 36.42, 12.21, 0.96483, 3, 19, 131.17, 42.93, 4e-05, 20, 89.07, 26.24, 0.00145, 21, 49.07, 15.91, 0.99851, 4, 13, 46.23, 126.52, 0.00135, 14, -24.27, 125.96, 0.0139, 15, -80.51, 123.19, 0.00131, 21, 58.42, -7.26, 0.98343, 4, 13, 70.99, 123.19, 0.00876, 14, 0.57, 123.24, 0.06675, 15, -55.6, 121.35, 0.00819, 21, 67.77, -30.43, 0.9163, 5, 13, 79.59, 92.59, 0.04358, 14, 9.93, 92.87, 0.26611, 15, -45.17, 91.33, 0.05828, 20, 100.57, -41.83, 0.0087, 21, 45.42, -53.03, 0.62333, 5, 13, 90.49, 109.43, 0.02629, 14, 20.4, 109.97, 0.35054, 15, -35.31, 108.79, 0.09104, 20, 120.36, -38.57, 0.0001, 21, 65.45, -54.17, 0.53203, 4, 13, 98.51, 111.58, 0.02534, 14, 28.36, 112.32, 0.35452, 15, -27.43, 111.42, 0.09301, 21, 71.29, -60.09, 0.52714, 5, 13, 108.88, 91.66, 0.02662, 14, 39.23, 92.67, 0.38632, 15, -15.88, 92.16, 0.12789, 20, 119.57, -64.13, 0.00076, 21, 59.1, -78.95, 0.45841, 5, 13, 119.25, 71.74, 0.01447, 14, 50.09, 73.01, 0.43202, 15, -4.33, 72.9, 0.28218, 20, 111.8, -85.2, 0.00114, 21, 46.91, -97.81, 0.2702, 5, 13, 127.52, 74.16, 0.00782, 14, 58.3, 75.64, 0.41918, 15, 3.78, 75.82, 0.34702, 20, 119.15, -89.7, 0.0002, 21, 53.1, -103.8, 0.22577, 4, 13, 144.46, 55.27, 3e-05, 14, 75.7, 57.17, 0.30014, 15, 21.83, 57.97, 0.58918, 21, 45.05, -127.87, 0.11064, 3, 14, 98.51, 45.34, 0.15564, 15, 45.04, 46.96, 0.79141, 21, 45.41, -153.56, 0.05295, 3, 14, 99.21, 38.68, 0.14789, 15, 45.97, 40.33, 0.80196, 21, 39.87, -157.32, 0.05015, 3, 14, 89.1, 26.31, 0.11144, 15, 36.31, 27.61, 0.85322, 21, 24.2, -154.27, 0.03534, 3, 14, 89.66, 12.79, 0.02373, 15, 37.34, 14.12, 0.96714, 21, 12.54, -161.15, 0.00913, 3, 14, 98.7, 3.3, 0.00059, 15, 46.72, 4.95, 0.99884, 21, 8.45, -173.6, 0.00056, 2, 12, 31.55, 23.23, 0.08362, 19, 12.75, -3.73, 0.91638, 3, 12, 46.75, 7.15, 0.84371, 19, 16.07, -25.61, 0.15626, 20, -37.08, -18.81, 3e-05, 4, 12, 42.56, -20.18, 0.60086, 13, -33.31, -21.27, 0.00716, 16, 10.89, 25.46, 0.38438, 17, -34.89, 28.48, 0.0076, 3, 12, 32.64, -37.49, 0.10181, 13, -42.66, -38.89, 0.00293, 16, 19.09, 7.28, 0.89526, 3, 11, 92.56, -19.83, 0.00181, 12, 21.08, -19.62, 0.33979, 16, -2.17, 8.41, 0.6584, 5, 12, 81.12, -11.32, 0.23749, 13, 4.95, -11.17, 0.69589, 16, 26.34, 61.89, 0.02866, 17, -16.45, 63.48, 0.03749, 18, -82.07, 55.72, 0.00047, 6, 13, 48.42, -15.68, 0.86685, 14, -18.55, -16.14, 0.07047, 15, -69.77, -18.62, 0.0004, 16, 56.54, 93.48, 0.00028, 17, 16.27, 92.44, 0.04373, 18, -52.33, 87.75, 0.01826, 6, 13, 86.35, -19.93, 0.08999, 14, 19.48, -19.45, 0.82096, 15, -31.65, -20.59, 0.03153, 21, -49.04, -114.54, 0, 17, 45.07, 117.5, 0.01781, 18, -26.12, 115.5, 0.0397, 6, 13, 125.4, -26.68, 0.00303, 14, 58.69, -25.22, 0.2913, 15, 7.74, -24.96, 0.65193, 21, -35.59, -151.82, 0, 17, 76.46, 141.7, 0.00263, 18, 2.75, 142.65, 0.05112, 6, 13, 135, -67.31, 0.01161, 14, 69.29, -65.61, 0.24166, 15, 19.76, -64.95, 0.53727, 21, -66.17, -180.25, 0, 17, 112.87, 121.27, 0.00243, 18, 40.99, 125.88, 0.20702, 6, 13, 105.4, -70.77, 0.06203, 14, 39.79, -69.8, 0.33185, 15, -9.58, -70.18, 0.29892, 21, -83.81, -156.24, 0, 17, 95.4, 97.14, 0.02441, 18, 25.96, 100.16, 0.28278, 6, 13, 74.39, -68.97, 0.18337, 14, 8.75, -68.78, 0.2925, 15, -40.63, -70.26, 0.11144, 21, -97.58, -128.4, 0, 17, 73.1, 75.52, 0.10612, 18, 5.88, 76.46, 0.30657, 8, 12, 111.75, -64.5, 0.00934, 13, 37.28, -63.33, 0.33125, 14, -28.49, -64.06, 0.10436, 15, -78.01, -66.86, 0.01609, 21, -111.02, -93.35, 0, 16, 87.38, 55.49, 0.00261, 17, 43.84, 52.01, 0.36305, 18, -20.94, 50.2, 0.1733, 7, 12, 74.3, -57.09, 0.08754, 13, -0.38, -57.13, 0.19602, 14, -66.3, -58.8, 0.00744, 15, -115.99, -62.94, 9e-05, 16, 59.41, 29.51, 0.10673, 17, 13.79, 28.46, 0.5957, 18, -48.54, 23.83, 0.00647, 2, 16, 44.75, -3.2, 0.78047, 17, -3.54, -2.91, 0.21953, 3, 16, 68.35, -35.63, 0.15094, 17, 17.26, -37.2, 0.82285, 18, -38.66, -41.18, 0.02621, 3, 16, 103.32, -50.97, 0.02334, 17, 50.84, -55.41, 0.78078, 18, -3.47, -56.02, 0.19589, 3, 16, 124.87, -64.73, 0.00448, 17, 71.15, -70.91, 0.75021, 18, 18.27, -69.46, 0.24531, 3, 16, 97.2, -10.33, 0.00061, 17, 48.13, -14.4, 0.87735, 18, -10.17, -15.47, 0.12204, 7, 12, 105.92, -94.79, 0.00051, 13, 32.44, -93.79, 0.09576, 14, -32.58, -94.63, 0.05446, 15, -81.02, -97.56, 0.01623, 21, -139.9, -104.2, 0, 17, 62.99, 27.83, 0.35987, 18, 0.49, 28.01, 0.47317, 5, 13, 26.8, -132.29, 9e-05, 14, -37.25, -133.25, 0.00017, 15, -84.33, -136.32, 9e-05, 21, -176.14, -118.34, 0, 18, 27.86, 0.37, 0.99966, 6, 13, 84.9, -120.22, 0.04615, 14, 20.52, -119.75, 0.18313, 15, -27.06, -120.78, 0.15042, 21, -136.93, -162.87, 0, 17, 117.95, 48.58, 0.00729, 18, 53.15, 54.04, 0.61302, 3, 14, 64.36, 7.47, 0.07634, 15, 12.25, 7.91, 0.91667, 21, -4.1, -141.37, 0.007, 4, 14, 89.23, -28.62, 0.04108, 15, 38.38, -27.28, 0.91694, 21, -24.15, -180.34, 0, 18, 24.38, 164.49, 0.04198, 3, 14, 79.46, 40.03, 0.20252, 15, 26.19, 40.99, 0.73407, 21, 31.73, -139.28, 0.06341, 5, 13, 121.71, 53.15, 0.00783, 14, 53.01, 54.48, 0.43571, 15, -0.76, 54.49, 0.37848, 20, 99.69, -99.53, 0.00133, 21, 31.96, -109.14, 0.17665, 4, 14, 35.97, 12.45, 0.89672, 15, -16.3, 11.88, 0.07807, 20, 56.75, -114.11, 0.00128, 21, -13.13, -114, 0.02393, 5, 13, 96.19, 66.32, 0.05166, 14, 27.17, 67.01, 0.43657, 15, -27.02, 66.1, 0.14163, 20, 92.28, -71.78, 0.01308, 21, 30.79, -80.45, 0.35706, 5, 13, 75.91, 21.67, 0.2396, 14, 8.01, 21.87, 0.64252, 15, -44.58, 20.31, 0.01219, 20, 45.59, -86.79, 0.02889, 21, -18.04, -84.9, 0.0768, 5, 13, 63.35, 71.45, 0.12733, 14, -5.79, 71.33, 0.23175, 15, -60.11, 69.25, 0.02927, 20, 74, -44.02, 0.07163, 21, 19.02, -49.36, 0.54002, 5, 13, 38.64, 26.23, 0.70042, 14, -29.36, 25.51, 0.0502, 19, 83.12, -50.47, 1e-05, 20, 23.91, -56.14, 0.18367, 21, -32.51, -50.25, 0.0657, 5, 12, 74.71, 35.99, 0.11224, 13, -2.99, 35.91, 0.18512, 14, -71.22, 34.15, 0.00011, 19, 55.49, -17.87, 0.19503, 20, 3.09, -18.81, 0.50749, 4, 13, 35.42, 111.08, 0.00211, 14, -34.7, 110.26, 0.00711, 15, -90.38, 107.13, 0.00064, 21, 39.66, -5.49, 0.99014, 3, 19, 81.02, 18.67, 0.02526, 20, 35.18, 12.11, 0.80827, 21, -6.6, 13.89, 0.16648, 5, 13, 30.14, 71.21, 0.10709, 14, -38.99, 70.26, 0.04624, 15, -93.25, 67, 0.00153, 20, 51.48, -19.6, 0.30093, 21, 2.38, -20.62, 0.54421, 3, 19, 100.89, 80.11, 0.08265, 20, 66.54, 68.57, 0.46201, 21, 36.33, 62.13, 0.45533, 3, 19, 81.3, 58.52, 0.13338, 20, 43.15, 51.16, 0.50249, 21, 9.71, 50.25, 0.36413, 3, 19, 52.06, 38.35, 0.38242, 20, 10.57, 37, 0.537, 21, -25.18, 43.55, 0.08058, 3, 19, 61.11, 77.52, 0.23152, 20, 27, 73.7, 0.50783, 21, -1.13, 75.77, 0.26066, 3, 12, 49.16, 37.96, 0.01252, 13, -28.59, 37.05, 0.0018, 19, 35.62, -1.67, 0.98569], "hull": 85, "edges": [0, 168, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 82, 84, 84, 86, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 138, 140, 144, 146, 146, 148, 148, 150, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 78, 80, 80, 82, 66, 68, 68, 70, 62, 64, 64, 66, 98, 100, 94, 96, 104, 106, 100, 102, 102, 104, 36, 38, 32, 34, 34, 36, 26, 28, 28, 30, 12, 14, 8, 10, 10, 12, 140, 142, 142, 144, 132, 134, 134, 136, 136, 138, 150, 152, 152, 154, 96, 98, 70, 72, 92, 94, 90, 92, 86, 88, 88, 90, 72, 74, 74, 76, 76, 78, 100, 170, 170, 172, 172, 174, 174, 176, 176, 66, 126, 128], "width": 350, "height": 341}}, "tree_R": {"tree_R": {"type": "mesh", "uvs": [0.56083, 0, 0.56994, 0, 0.60852, 0.03951, 0.64915, 0.11735, 0.68977, 0.19519, 0.69516, 0.22601, 0.76432, 0.27588, 0.76706, 0.33643, 0.815, 0.36917, 0.8601, 0.42535, 0.9052, 0.48152, 0.92047, 0.46724, 0.93712, 0.51622, 0.8985, 0.53272, 0.91852, 0.60564, 0.94783, 0.60262, 0.95922, 0.61743, 0.95968, 0.67975, 0.9237, 0.73342, 0.86654, 0.70048, 0.82406, 0.63469, 0.84436, 0.70223, 0.88773, 0.7871, 0.94081, 0.81358, 0.97554, 0.8173, 1, 0.93745, 1, 0.94897, 0.95686, 0.9488, 0.9106, 0.97272, 0.87471, 0.93592, 0.87685, 0.9236, 0.80811, 0.95214, 0.73937, 0.98069, 0.69601, 0.91463, 0.66897, 0.95229, 0.60708, 0.96935, 0.55631, 0.95447, 0.52825, 0.99801, 0.49284, 0.98945, 0.4173, 0.96061, 0.31527, 0.91616, 0.24771, 0.88935, 0.18478, 0.87183, 0.13734, 0.83115, 0.09083, 0.87919, 0, 0.85638, 0, 0.84688, 0.02035, 0.74567, 0.04334, 0.72248, 0.11156, 0.69598, 0.02341, 0.55969, 0.07795, 0.46006, 0.06079, 0.39034, 0.09535, 0.38851, 0.14031, 0.33269, 0.18527, 0.27687, 0.23066, 0.24782, 0.23697, 0.17907, 0.28271, 0.1794, 0.30726, 0.15597, 0.30825, 0.09535, 0.34108, 0.06126, 0.35518, 0.01681, 0.39779, 0.08194, 0.44719, 0.06123, 0.50244, 0.06182, 0.54479, 0.10686, 0.82099, 0.8259, 0.8706, 0.85348, 0.9202, 0.89484, 0.96217, 0.88598, 0.74974, 0.79114, 0.67235, 0.72567, 0.76948, 0.50877, 0.82799, 0.50385, 0.70835, 0.37779, 0.64193, 0.53345, 0.57435, 0.80817, 0.49723, 0.89681, 0.5264, 0.58548, 0.6225, 0.31771, 0.57099, 0.22021, 0.52734, 0.37831, 0.44101, 0.78605, 0.48613, 0.21906, 0.36722, 0.09847, 0.38129, 0.20358, 0.39619, 0.37918, 0.40612, 0.57785, 0.30154, 0.75238, 0.29906, 0.57294, 0.27919, 0.26147, 0.2974, 0.39734, 0.21216, 0.44989, 0.21712, 0.61652, 0.10208, 0.42938, 0.12112, 0.57037, 0.20553, 0.76776], "triangles": [10, 11, 12, 19, 14, 18, 17, 14, 16, 16, 14, 15, 14, 17, 18, 19, 20, 14, 20, 13, 14, 20, 74, 13, 20, 73, 74, 13, 10, 12, 13, 74, 10, 73, 8, 74, 73, 7, 8, 74, 9, 10, 74, 8, 9, 31, 32, 71, 29, 30, 28, 28, 69, 27, 28, 30, 69, 32, 33, 71, 31, 67, 68, 31, 71, 67, 27, 25, 26, 27, 70, 25, 27, 69, 70, 70, 24, 25, 30, 31, 68, 30, 68, 69, 33, 72, 71, 69, 68, 23, 69, 23, 70, 23, 68, 22, 70, 23, 24, 68, 67, 22, 67, 21, 22, 67, 71, 21, 71, 20, 21, 71, 72, 20, 72, 73, 20, 72, 76, 73, 76, 75, 73, 75, 7, 73, 34, 35, 77, 35, 36, 77, 36, 78, 77, 77, 79, 72, 34, 77, 33, 77, 72, 33, 82, 81, 80, 82, 84, 81, 80, 5, 75, 75, 5, 6, 80, 4, 5, 80, 81, 4, 84, 66, 81, 81, 3, 4, 3, 66, 2, 3, 81, 66, 91, 59, 86, 84, 65, 66, 2, 66, 1, 66, 0, 1, 84, 86, 63, 63, 64, 84, 84, 87, 86, 59, 85, 86, 59, 60, 85, 84, 64, 65, 86, 85, 63, 60, 61, 85, 63, 85, 62, 85, 61, 62, 75, 6, 7, 79, 82, 76, 82, 80, 76, 76, 80, 75, 79, 76, 72, 96, 93, 94, 94, 93, 90, 79, 88, 82, 90, 87, 88, 82, 88, 87, 93, 92, 90, 90, 92, 87, 50, 51, 96, 51, 95, 96, 96, 95, 93, 95, 52, 53, 95, 51, 52, 95, 54, 93, 54, 55, 93, 92, 56, 91, 92, 93, 56, 93, 55, 56, 95, 53, 54, 87, 92, 91, 87, 91, 86, 59, 91, 58, 56, 57, 91, 91, 57, 58, 82, 87, 84, 49, 96, 94, 49, 50, 96, 39, 83, 78, 39, 40, 83, 41, 89, 40, 40, 89, 83, 78, 83, 77, 42, 97, 41, 41, 97, 89, 44, 46, 47, 47, 48, 44, 43, 48, 49, 43, 44, 48, 42, 43, 97, 44, 45, 46, 43, 49, 97, 83, 79, 77, 89, 88, 83, 83, 88, 79, 97, 94, 89, 97, 49, 94, 94, 90, 89, 89, 90, 88, 36, 37, 78, 37, 38, 78, 38, 39, 78], "vertices": [1, 24, 62.97, 6.42, 1, 1, 24, 63.81, 3.8, 1, 3, 24, 60.01, -9.64, 0.99641, 26, 19.58, 137.8, 0.00221, 27, -28.05, 137.43, 0.00138, 4, 23, 99.86, -34.8, 0.01828, 24, 49.27, -25.94, 0.93312, 26, 27.03, 119.76, 0.03013, 27, -20.36, 119.49, 0.01847, 4, 23, 86.23, -48.77, 0.09706, 24, 38.54, -42.24, 0.73862, 26, 34.47, 101.72, 0.09991, 27, -12.68, 101.55, 0.0644, 4, 23, 80.46, -51.1, 0.1333, 24, 33.31, -45.62, 0.64122, 26, 34.32, 95.49, 0.13502, 27, -12.74, 95.33, 0.09046, 4, 23, 73.27, -72.98, 0.17797, 24, 30.39, -68.47, 0.39575, 26, 51.58, 80.23, 0.23259, 27, 4.73, 80.3, 0.19369, 4, 23, 61.64, -75.2, 0.17361, 24, 19.39, -72.85, 0.29364, 26, 49.02, 68.67, 0.2681, 27, 2.32, 68.71, 0.26465, 4, 23, 57.01, -90.33, 0.12128, 24, 17.71, -88.58, 0.16006, 26, 61.08, 58.44, 0.2677, 27, 14.52, 58.64, 0.45095, 4, 23, 47.74, -105.15, 0.06644, 24, 11.41, -104.89, 0.08046, 26, 71.03, 44.06, 0.18625, 27, 24.66, 44.4, 0.66686, 4, 23, 38.47, -119.97, 0.0251, 24, 5.11, -121.19, 0.03298, 26, 80.97, 29.69, 0.05833, 27, 34.8, 30.16, 0.88359, 4, 23, 41.78, -124.22, 0.02181, 24, 9.17, -124.74, 0.02974, 26, 86.19, 31.05, 0.04305, 27, 39.99, 31.59, 0.9054, 4, 23, 32.89, -130.34, 0.02041, 24, 1.6, -132.43, 0.02827, 26, 88.29, 20.46, 0.03715, 27, 42.24, 21.03, 0.91418, 4, 23, 28.32, -119.14, 0.01978, 24, -5.01, -122.3, 0.02547, 26, 76.19, 20.69, 0.05184, 27, 30.14, 21.1, 0.90291, 4, 23, 14.91, -126.82, 0.00159, 24, -16.72, -132.38, 0.00219, 26, 77.95, 5.34, 0.00214, 27, 32.1, 5.77, 0.99407, 3, 23, 16.54, -135.54, 2e-05, 24, -13.47, -140.63, 3e-05, 27, 40.78, 3.94, 0.99996, 2, 26, 89.08, -0.36, 0, 27, 43.31, 0.23, 1, 1, 27, 40.15, -11.51, 1, 4, 23, -9.65, -131.32, 0, 24, -39.99, -141.44, 0, 26, 72.36, -19, 0.00022, 27, 26.85, -18.63, 0.99977, 4, 23, -5.31, -113.42, 6e-05, 24, -39.11, -123.04, 3e-05, 26, 57.64, -7.93, 0.00372, 27, 11.97, -7.77, 0.99618, 4, 23, 5.92, -99.16, 0.00929, 24, -30.79, -106.92, 0.00716, 26, 48.99, 8.02, 0.33666, 27, 3.11, 8.06, 0.64689, 2, 26, 51.12, -6.35, 0.58647, 27, 5.44, -6.28, 0.41353, 3, 25, 87.62, -45.75, 0.00244, 26, 58.97, -25.94, 0.72538, 27, 13.55, -25.76, 0.27218, 2, 26, 72.87, -35.45, 0.69477, 27, 27.58, -35.08, 0.30523, 2, 26, 82.72, -39.13, 0.68548, 27, 37.48, -38.62, 0.31452, 3, 25, 96.27, -89.73, 0, 26, 83.14, -63.69, 0.68023, 27, 38.23, -63.18, 0.31977, 3, 25, 94.89, -91.5, 0, 26, 82.5, -65.84, 0.67994, 27, 37.62, -65.34, 0.32006, 3, 25, 84.64, -83.45, 2e-05, 26, 70.02, -62.11, 0.68788, 27, 25.09, -61.77, 0.3121, 3, 25, 70.76, -78.52, 0.00235, 26, 55.3, -62.61, 0.7056, 27, 10.38, -62.47, 0.29205, 3, 25, 66.64, -66.19, 0.013, 26, 46.95, -52.65, 0.72794, 27, 1.89, -52.63, 0.25906, 3, 25, 68.63, -64.7, 0.01719, 26, 48.25, -50.53, 0.73407, 27, 3.17, -50.49, 0.24874, 3, 25, 48.85, -56.29, 0.13731, 26, 26.77, -49.96, 0.76521, 27, -18.32, -50.21, 0.09749, 3, 25, 29.07, -47.89, 0.31557, 26, 5.28, -49.39, 0.65539, 27, -39.82, -49.94, 0.02904, 3, 25, 26.69, -29.68, 0.62908, 26, -3.61, -33.32, 0.36558, 27, -48.92, -33.99, 0.00534, 3, 25, 15.73, -30.44, 0.8736, 26, -13.52, -38.04, 0.12607, 27, -58.77, -38.84, 0.00033, 4, 4, 72.18, -41.29, 0.02013, 23, -66.62, -41.8, 0, 25, -1.04, -21.54, 0.9703, 26, -32.39, -35.92, 0.00956, 4, 4, 73.11, -25.71, 0.28088, 22, -9.51, -25.53, 0.00588, 23, -65.55, -26.23, 1e-05, 25, -11.34, -9.82, 0.71323, 3, 4, 63.62, -18.38, 0.67573, 23, -74.98, -18.82, 2e-05, 25, -23.24, -11.28, 0.32425, 3, 4, 63.92, -7.56, 0.8841, 23, -74.59, -8, 3e-05, 25, -30.64, -3.38, 0.11587, 4, 4, 66.6, 15.78, 0.79427, 22, -15.22, 16.08, 0.19843, 23, -71.7, 15.32, 0.00648, 25, -45.15, 15.1, 0.00082, 4, 4, 71.29, 47.44, 0.38306, 22, -9.91, 47.64, 0.53098, 23, -66.73, 46.94, 0.08529, 25, -64.08, 40.91, 0.00067, 4, 4, 73.89, 68.35, 0.2918, 22, -6.91, 68.49, 0.54554, 23, -63.95, 67.81, 0.16202, 25, -76.94, 57.59, 0.00064, 5, 4, 74.87, 87.63, 0.25477, 22, -5.55, 87.75, 0.53063, 23, -62.8, 87.09, 0.21396, 24, -133.5, 62.97, 0, 25, -89.8, 71.99, 0.00063, 5, 4, 80.92, 102.85, 0.23787, 22, 0.8, 102.85, 0.51646, 23, -56.61, 102.25, 0.24472, 24, -130.3, 79.02, 0.00032, 25, -96.2, 87.06, 0.00064, 4, 4, 69.85, 115.59, 0.23809, 22, -10.03, 115.81, 0.5148, 23, -67.57, 115.09, 0.24645, 25, -113.04, 88.33, 0.00066, 5, 4, 70.78, 143.37, 0.23963, 22, -8.55, 143.56, 0.5154, 23, -66.4, 142.86, 0.24429, 24, -147.59, 117.05, 0, 25, -131.91, 108.73, 0.00068, 5, 4, 72.62, 143.6, 0.23961, 22, -6.71, 143.76, 0.51539, 23, -64.56, 143.08, 0.24432, 24, -145.82, 117.61, 0, 25, -130.77, 110.19, 0.00068, 5, 4, 92.97, 140.01, 0.23723, 22, 13.57, 139.77, 0.5134, 23, -44.23, 139.3, 0.24865, 24, -125.15, 117.75, 5e-05, 25, -113.77, 121.96, 0.00067, 5, 4, 98.34, 133.69, 0.23528, 22, 18.81, 133.35, 0.51172, 23, -38.92, 132.94, 0.25208, 24, -118.74, 112.51, 0.00026, 25, -105.52, 121.24, 0.00066, 5, 4, 106.08, 113.91, 0.20879, 22, 26.16, 113.42, 0.48552, 23, -31.36, 113.1, 0.29942, 24, -107.55, 94.45, 0.00567, 25, -86.1, 112.62, 0.00058, 5, 4, 129.06, 143.69, 0.16018, 22, 49.73, 142.75, 0.41767, 23, -8.11, 142.67, 0.40124, 24, -90.32, 127.89, 0.02043, 25, -90.71, 149.96, 0.00048, 5, 4, 150.42, 129.82, 0.14079, 22, 70.81, 128.46, 0.38297, 23, 13.13, 128.61, 0.43761, 24, -66.81, 118.1, 0.03821, 25, -65.77, 155.12, 0.00043, 5, 4, 163.25, 136.68, 0.13221, 22, 83.77, 135.07, 0.3662, 23, 26.01, 135.36, 0.45431, 24, -55.43, 127.17, 0.04686, 25, -61.48, 169.02, 0.00041, 5, 4, 164.93, 126.37, 0.12833, 22, 85.25, 124.73, 0.35826, 23, 27.6, 125.04, 0.4601, 24, -51.92, 117.33, 0.05291, 25, -53.03, 162.87, 0.0004, 5, 4, 177.45, 114.28, 0.10621, 22, 97.53, 112.41, 0.30929, 23, 40.01, 112.84, 0.49212, 24, -37.42, 107.7, 0.09204, 25, -35.63, 163.08, 0.00034, 5, 4, 189.97, 102.2, 0.08212, 22, 109.81, 100.08, 0.2485, 23, 52.43, 100.64, 0.51516, 24, -22.93, 98.08, 0.15396, 25, -18.23, 163.3, 0.00027, 5, 4, 197.33, 89.32, 0.05686, 22, 116.91, 87.06, 0.17625, 23, 59.67, 87.7, 0.51246, 24, -13.36, 86.74, 0.25424, 25, -3.94, 159.32, 0.00019, 5, 4, 210.86, 89.13, 0.04313, 22, 130.45, 86.6, 0.13313, 23, 73.21, 87.39, 0.4919, 24, -0.01, 89, 0.3317, 25, 5.82, 168.7, 0.00015, 5, 4, 212.55, 75.42, 0.03319, 22, 131.87, 72.86, 0.10119, 23, 74.77, 73.66, 0.45325, 24, 4.12, 75.81, 0.41226, 25, 16.66, 160.14, 0.00012, 5, 4, 218.02, 68.64, 0.02063, 22, 137.21, 65.98, 0.05928, 23, 80.19, 66.84, 0.37399, 24, 10.73, 70.14, 0.54603, 25, 25.31, 159.18, 8e-05, 5, 4, 229.79, 69.85, 0.01229, 22, 148.99, 66.96, 0.03079, 23, 91.96, 67.94, 0.29562, 24, 22.08, 73.45, 0.66126, 25, 32.83, 168.31, 5e-05, 5, 4, 237.64, 60.86, 0.00858, 22, 156.67, 57.82, 0.01849, 23, 99.73, 58.88, 0.24844, 24, 31.43, 66.02, 0.72446, 25, 44.73, 167.44, 4e-05, 5, 4, 246.78, 57.73, 0.00735, 22, 165.74, 54.51, 0.01436, 23, 108.84, 55.68, 0.23173, 24, 40.98, 64.6, 0.74653, 25, 53.43, 171.64, 3e-05, 5, 4, 235.81, 43.36, 0.00541, 22, 154.5, 40.35, 0.01009, 23, 97.75, 41.4, 0.18437, 24, 32.79, 48.48, 0.8001, 25, 55.74, 153.71, 2e-05, 5, 4, 241.71, 29.07, 0.0015, 22, 160.12, 25.95, 0.00132, 23, 103.52, 27.06, 0.06795, 24, 41.17, 35.49, 0.92922, 25, 69.98, 147.7, 1e-05, 4, 4, 243.71, 12.5, 0.00012, 23, 105.38, 10.48, 0.00844, 24, 46.13, 19.56, 0.99143, 25, 83.06, 137.34, 0, 2, 24, 41.65, 4.71, 1, 25, 87.72, 122.54, 0, 3, 25, 67.08, -39.3, 0.04216, 26, 37.5, -27.47, 0.81985, 27, -7.9, -27.57, 0.13798, 3, 25, 75.57, -52.76, 0.01461, 26, 50.33, -36.88, 0.7453, 27, 5.06, -36.81, 0.24008, 3, 25, 82.4, -68.34, 0.00183, 26, 62.4, -48.87, 0.7037, 27, 17.29, -48.64, 0.29446, 2, 26, 75.04, -50.82, 0.6858, 27, 29.96, -50.42, 0.3142, 3, 25, 54.3, -20.7, 0.10368, 26, 18.79, -14.85, 0.88997, 27, -26.77, -15.21, 0.00635, 6, 22, 38.61, -55.58, 0.00779, 23, -17.11, -55.76, 0.01506, 24, -61.61, -68.66, 0.0009, 25, 43.75, 3.75, 0.43225, 26, 0.02, 4.04, 0.54358, 27, -45.8, 3.42, 0.00042, 5, 23, 28.35, -79.9, 0.119, 24, -12.4, -83.76, 0.09119, 25, 92.91, 19, 0.00048, 26, 40.17, 36.25, 0.43979, 27, -6.09, 36.17, 0.34954, 4, 23, 31.39, -97.33, 0.06013, 24, -6.12, -100.3, 0.06252, 26, 57.38, 32.14, 0.23987, 27, 11.17, 32.3, 0.63748, 5, 23, 51.54, -58.55, 0.24071, 24, 6.32, -58.41, 0.30757, 25, 94.1, 50.49, 0.00211, 26, 29.73, 65.98, 0.27464, 27, -16.93, 65.76, 0.17497, 6, 22, 74.89, -42.41, 0.02396, 23, 19.03, -42.21, 0.41953, 24, -28.69, -48.52, 0.06992, 25, 59.6, 38.94, 0.09221, 26, 1.87, 42.58, 0.34806, 27, -44.48, 41.99, 0.04633, 3, 22, 19.43, -27.88, 0.17877, 23, -36.58, -28.27, 0.00153, 25, 10.53, 9.3, 0.81969, 3, 4, 82, -6.58, 0.38874, 22, -0.25, -6.58, 0.45855, 25, -18.47, 10.03, 0.15271, 6, 22, 61.05, -8.82, 0.19308, 23, 4.83, -8.77, 0.74018, 24, -48.96, -18.36, 0.00032, 25, 25.87, 52.43, 0.04492, 26, -34.47, 42.78, 0.02097, 27, -80.81, 41.69, 0.00055, 5, 23, 60.11, -31.42, 0.2835, 24, 9.61, -30.15, 0.55633, 25, 80.89, 75.69, 0.0013, 26, 8.2, 84.59, 0.10891, 27, -38.71, 84.07, 0.04997, 4, 23, 77.15, -13.73, 0.01892, 24, 22.99, -9.55, 0.96055, 26, -1.31, 107.24, 0.01356, 27, -48.52, 106.59, 0.00697, 5, 23, 44.98, -4.28, 0.9569, 24, -10.38, -6.36, 0.03303, 25, 50.97, 84.08, 0.00047, 26, -22.71, 81.43, 0.00768, 27, -69.58, 80.49, 0.00193, 4, 4, 101.27, 13, 0.06933, 22, 19.4, 12.62, 0.91809, 23, -37.05, 12.23, 0.01252, 25, -18.54, 37.51, 6e-05, 5, 4, 212.67, 13.5, 0.00087, 22, 130.78, 10.96, 0.00076, 23, 74.35, 11.75, 0.07023, 24, 15.42, 14.94, 0.92813, 25, 60.29, 116.21, 0, 5, 4, 231.44, 52.1, 0.00803, 22, 150.3, 49.18, 0.01737, 23, 93.46, 50.18, 0.2388, 24, 26.91, 56.29, 0.73576, 25, 46.49, 156.86, 3e-05, 5, 4, 211.65, 45.29, 0.01146, 22, 130.38, 42.76, 0.03088, 23, 73.61, 43.54, 0.3364, 24, 8.68, 46.02, 0.62121, 25, 37.21, 138.09, 4e-05, 5, 4, 178.26, 36.48, 0.01951, 22, 96.82, 34.6, 0.07917, 23, 40.14, 35.03, 0.73071, 24, -22.58, 31.33, 0.17055, 25, 19.66, 108.35, 6e-05, 5, 4, 140.21, 28.59, 0.0365, 22, 58.63, 27.46, 0.43082, 23, 2.02, 27.48, 0.52869, 24, -58.58, 16.7, 0.00391, 25, -1.83, 75.98, 9e-05, 5, 4, 102.44, 55.61, 0.2297, 22, 21.4, 55.2, 0.5846, 23, -35.5, 54.82, 0.18453, 24, -100.6, 36.45, 0.00068, 25, -47.68, 68.62, 0.00049, 5, 4, 137.06, 60.79, 0.11138, 22, 56.1, 59.71, 0.43322, 23, -0.85, 59.7, 0.43157, 24, -67.49, 47.8, 0.02354, 25, -26.72, 96.65, 0.0003, 5, 4, 196.54, 74.44, 0.0441, 22, 115.84, 72.2, 0.13925, 23, 58.76, 72.83, 0.50721, 24, -11.45, 71.96, 0.3093, 25, 5.97, 148.19, 0.00015, 5, 4, 170.96, 65.63, 0.06083, 22, 90.09, 63.88, 0.2181, 23, 33.1, 64.24, 0.57564, 24, -35.02, 58.68, 0.14523, 25, -6.02, 123.93, 0.00019, 5, 4, 157.53, 89.86, 0.1082, 22, 77.14, 88.38, 0.33001, 23, 19.88, 88.6, 0.48666, 24, -52.6, 80.09, 0.0748, 25, -32.61, 131.72, 0.00033, 5, 4, 125.49, 84.26, 0.16586, 22, 45, 83.39, 0.46003, 23, -12.21, 83.27, 0.35711, 24, -83.1, 68.79, 0.01655, 25, -51.45, 105.19, 0.00045, 5, 4, 157.28, 123.35, 0.13208, 22, 77.54, 121.86, 0.36621, 23, 19.93, 122.08, 0.45314, 24, -58.89, 112.97, 0.04816, 25, -56.34, 155.34, 0.00041, 5, 4, 130.74, 114.16, 0.16213, 22, 50.83, 113.19, 0.42368, 23, -6.7, 113.12, 0.3916, 24, -83.34, 99.14, 0.02212, 25, -68.74, 130.14, 0.00048, 5, 4, 95.79, 83.99, 0.23529, 22, 15.3, 83.7, 0.52448, 23, -41.91, 83.26, 0.23819, 24, -112.27, 63.16, 0.00145, 25, -72.37, 84.11, 0.00059], "hull": 67, "edges": [0, 132, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 70, 72, 72, 74, 74, 76, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 32, 34, 26, 28, 28, 30, 30, 32, 16, 18, 18, 20, 4, 6, 6, 8, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 66, 68, 68, 70, 64, 66, 60, 62, 62, 64, 76, 78, 82, 84, 78, 80, 80, 82, 106, 108, 108, 110], "width": 302, "height": 195}}}}], "animations": {"idle": {"slots": {"light": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light2": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light3": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light4": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light5": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light6": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light7": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light8": {"rgba": [{"color": "ffffff00", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0, 0.889, 0.39]}, {"time": 1.3333, "color": "ffffff64", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 0.39, 2.222, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0, 3.556, 0.39]}, {"time": 4, "color": "ffffff64", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 0.39, 4.889, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0, 6.222, 0.39]}, {"time": 6.6667, "color": "ffffff64", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 0.39, 7.556, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0, 8.889, 0.39]}, {"time": 9.3333, "color": "ffffff64", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 0.39, 10.222, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light_floor": {"rgba": [{"color": "ffffff64", "curve": [0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 1, 0.889, 1, 0.444, 0.39, 0.889, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 1, 1.778, 1, 2.222, 0.39]}, {"time": 2.6667, "color": "ffffff64", "curve": [3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 1, 3.556, 1, 3.111, 0.39, 3.556, 1]}, {"time": 4, "color": "ffffffff", "curve": [4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 1, 4.444, 1, 4.889, 0.39]}, {"time": 5.3333, "color": "ffffff64", "curve": [5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 1, 6.222, 1, 5.778, 0.39, 6.222, 1]}, {"time": 6.6667, "color": "ffffffff", "curve": [7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 1, 7.111, 1, 7.556, 0.39]}, {"time": 8, "color": "ffffff64", "curve": [8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 1, 8.889, 1, 8.444, 0.39, 8.889, 1]}, {"time": 9.3333, "color": "ffffffff", "curve": [9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 1, 9.778, 1, 10.222, 0.39]}, {"time": 10.6667, "color": "ffffff64"}]}, "light_O": {"rgba": [{"color": "ffffff00", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 0, 0.444, 0.39]}, {"time": 0.6667, "color": "ffffff64", "curve": [0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 0.39, 1.111, 0]}, {"time": 1.3333, "color": "ffffff00", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 0, 1.778, 0.39]}, {"time": 2, "color": "ffffff64", "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 0.39, 2.444, 0]}, {"time": 2.6667, "color": "ffffff00", "curve": [2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 0, 3.111, 0.39]}, {"time": 3.3333, "color": "ffffff64", "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 0.39, 3.778, 0]}, {"time": 4, "color": "ffffff00", "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 0, 4.444, 0.39]}, {"time": 4.6667, "color": "ffffff64", "curve": [4.889, 1, 5.111, 1, 4.889, 1, 5.111, 1, 4.889, 1, 5.111, 1, 4.889, 0.39, 5.111, 0]}, {"time": 5.3333, "color": "ffffff00", "curve": [5.556, 1, 5.778, 1, 5.556, 1, 5.778, 1, 5.556, 1, 5.778, 1, 5.556, 0, 5.778, 0.39]}, {"time": 6, "color": "ffffff64", "curve": [6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 0.39, 6.444, 0]}, {"time": 6.6667, "color": "ffffff00", "curve": [6.889, 1, 7.111, 1, 6.889, 1, 7.111, 1, 6.889, 1, 7.111, 1, 6.889, 0, 7.111, 0.39]}, {"time": 7.3333, "color": "ffffff64", "curve": [7.556, 1, 7.778, 1, 7.556, 1, 7.778, 1, 7.556, 1, 7.778, 1, 7.556, 0.39, 7.778, 0]}, {"time": 8, "color": "ffffff00", "curve": [8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 0, 8.444, 0.39]}, {"time": 8.6667, "color": "ffffff64", "curve": [8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 0.39, 9.111, 0]}, {"time": 9.3333, "color": "ffffff00", "curve": [9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 0, 9.778, 0.39]}, {"time": 10, "color": "ffffff64", "curve": [10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 0.39, 10.444, 0]}, {"time": 10.6667, "color": "ffffff00"}]}, "light_O2": {"rgba": [{"color": "ffffff32", "curve": [0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 1, 0.222, 1, 0.111, 0.1, 0.222, 0]}, {"time": 0.3333, "color": "ffffff00", "curve": [0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 1, 0.778, 1, 0.556, 0, 0.778, 0.39]}, {"time": 1, "color": "ffffff64", "curve": [1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 1, 1.444, 1, 1.222, 0.39, 1.444, 0]}, {"time": 1.6667, "color": "ffffff00", "curve": [1.889, 1, 2.111, 1, 1.889, 1, 2.111, 1, 1.889, 1, 2.111, 1, 1.889, 0, 2.111, 0.39]}, {"time": 2.3333, "color": "ffffff64", "curve": [2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 1, 2.778, 1, 2.556, 0.39, 2.778, 0]}, {"time": 3, "color": "ffffff00", "curve": [3.222, 1, 3.444, 1, 3.222, 1, 3.444, 1, 3.222, 1, 3.444, 1, 3.222, 0, 3.444, 0.39]}, {"time": 3.6667, "color": "ffffff64", "curve": [3.889, 1, 4.111, 1, 3.889, 1, 4.111, 1, 3.889, 1, 4.111, 1, 3.889, 0.39, 4.111, 0]}, {"time": 4.3333, "color": "ffffff00", "curve": [4.556, 1, 4.778, 1, 4.556, 1, 4.778, 1, 4.556, 1, 4.778, 1, 4.556, 0, 4.778, 0.39]}, {"time": 5, "color": "ffffff64", "curve": [5.222, 1, 5.444, 1, 5.222, 1, 5.444, 1, 5.222, 1, 5.444, 1, 5.222, 0.39, 5.444, 0]}, {"time": 5.6667, "color": "ffffff00", "curve": [5.889, 1, 6.111, 1, 5.889, 1, 6.111, 1, 5.889, 1, 6.111, 1, 5.889, 0, 6.111, 0.39]}, {"time": 6.3333, "color": "ffffff64", "curve": [6.556, 1, 6.778, 1, 6.556, 1, 6.778, 1, 6.556, 1, 6.778, 1, 6.556, 0.39, 6.778, 0]}, {"time": 7, "color": "ffffff00", "curve": [7.222, 1, 7.444, 1, 7.222, 1, 7.444, 1, 7.222, 1, 7.444, 1, 7.222, 0, 7.444, 0.39]}, {"time": 7.6667, "color": "ffffff64", "curve": [7.889, 1, 8.111, 1, 7.889, 1, 8.111, 1, 7.889, 1, 8.111, 1, 7.889, 0.39, 8.111, 0]}, {"time": 8.3333, "color": "ffffff00", "curve": [8.556, 1, 8.778, 1, 8.556, 1, 8.778, 1, 8.556, 1, 8.778, 1, 8.556, 0, 8.778, 0.39]}, {"time": 9, "color": "ffffff64", "curve": [9.222, 1, 9.444, 1, 9.222, 1, 9.444, 1, 9.222, 1, 9.444, 1, 9.222, 0.39, 9.444, 0]}, {"time": 9.6667, "color": "ffffff00", "curve": [9.889, 1, 10.111, 1, 9.889, 1, 10.111, 1, 9.889, 1, 10.111, 1, 9.889, 0, 10.111, 0.39]}, {"time": 10.3333, "color": "ffffff64", "curve": [10.444, 1, 10.556, 1, 10.444, 1, 10.556, 1, 10.444, 1, 10.556, 1, 10.444, 0.39, 10.556, 0.29]}, {"time": 10.6667, "color": "ffffff32"}]}, "shadow": {"rgba": [{"time": 5.6667, "color": "ffffffff", "curve": [5.833, 1, 6, 1, 5.833, 1, 6, 1, 5.833, 1, 6, 1, 5.74, 0.43, 5.966, 0.2]}, {"time": 6.1667, "color": "ffffff32", "curve": [6.333, 1, 6.5, 1, 6.333, 1, 6.5, 1, 6.333, 1, 6.5, 1, 6.341, 0.2, 6.604, 0.59]}, {"time": 6.6667, "color": "ffffffff"}]}}, "bones": {"bone": {"rotate": [{"value": 4.36, "curve": [0.222, 4.36, 0.444, -7.3]}, {"time": 0.6667, "value": -7.3, "curve": [0.889, -7.3, 1.111, 4.36]}, {"time": 1.3333, "value": 4.36, "curve": [1.556, 4.36, 1.778, -7.3]}, {"time": 2, "value": -7.3, "curve": [2.222, -7.3, 2.444, 4.36]}, {"time": 2.6667, "value": 4.36, "curve": [2.889, 4.36, 3.111, -7.3]}, {"time": 3.3333, "value": -7.3, "curve": [3.556, -7.3, 3.778, 4.36]}, {"time": 4, "value": 4.36, "curve": [4.222, 4.36, 4.444, -7.3]}, {"time": 4.6667, "value": -7.3, "curve": [4.889, -7.3, 5.111, 4.36]}, {"time": 5.3333, "value": 4.36, "curve": [5.556, 4.36, 5.778, -7.3]}, {"time": 6, "value": -7.3, "curve": [6.222, -7.3, 6.444, 4.36]}, {"time": 6.6667, "value": 4.36, "curve": [6.889, 4.36, 7.111, -7.3]}, {"time": 7.3333, "value": -7.3, "curve": [7.556, -7.3, 7.778, 4.36]}, {"time": 8, "value": 4.36, "curve": [8.222, 4.36, 8.444, -7.3]}, {"time": 8.6667, "value": -7.3, "curve": [8.889, -7.3, 9.111, 4.36]}, {"time": 9.3333, "value": 4.36, "curve": [9.556, 4.36, 9.778, -7.3]}, {"time": 10, "value": -7.3, "curve": [10.222, -7.3, 10.444, 4.36]}, {"time": 10.6667, "value": 4.36}]}, "bone2": {"rotate": [{"value": 3.01, "curve": [0.056, 4.84, 0.111, 6.12]}, {"time": 0.1667, "value": 6.12, "curve": [0.389, 6.12, 0.611, -13.3]}, {"time": 0.8333, "value": -13.3, "curve": [1.056, -13.3, 1.278, 6.12]}, {"time": 1.5, "value": 6.12, "curve": [1.722, 6.12, 1.944, -13.3]}, {"time": 2.1667, "value": -13.3, "curve": [2.389, -13.3, 2.611, 6.12]}, {"time": 2.8333, "value": 6.12, "curve": [3.056, 6.12, 3.278, -13.3]}, {"time": 3.5, "value": -13.3, "curve": [3.722, -13.3, 3.944, 6.12]}, {"time": 4.1667, "value": 6.12, "curve": [4.389, 6.12, 4.611, -13.3]}, {"time": 4.8333, "value": -13.3, "curve": [5.056, -13.3, 5.278, 6.12]}, {"time": 5.5, "value": 6.12, "curve": [5.722, 6.12, 5.944, -13.3]}, {"time": 6.1667, "value": -13.3, "curve": [6.389, -13.3, 6.611, 6.12]}, {"time": 6.8333, "value": 6.12, "curve": [7.056, 6.12, 7.278, -13.3]}, {"time": 7.5, "value": -13.3, "curve": [7.722, -13.3, 7.944, 6.12]}, {"time": 8.1667, "value": 6.12, "curve": [8.389, 6.12, 8.611, -13.3]}, {"time": 8.8333, "value": -13.3, "curve": [9.056, -13.3, 9.278, 6.12]}, {"time": 9.5, "value": 6.12, "curve": [9.722, 6.12, 9.944, -13.3]}, {"time": 10.1667, "value": -13.3, "curve": [10.333, -13.3, 10.5, -2.45]}, {"time": 10.6667, "value": 3.01}]}, "bone3": {"rotate": [{"value": -4.85, "curve": [0.111, 2.35, 0.222, 9.56]}, {"time": 0.3333, "value": 9.56, "curve": [0.556, 9.56, 0.778, -19.27]}, {"time": 1, "value": -19.27, "curve": [1.222, -19.27, 1.444, 9.56]}, {"time": 1.6667, "value": 9.56, "curve": [1.889, 9.56, 2.111, -19.27]}, {"time": 2.3333, "value": -19.27, "curve": [2.556, -19.27, 2.778, 9.56]}, {"time": 3, "value": 9.56, "curve": [3.222, 9.56, 3.444, -19.27]}, {"time": 3.6667, "value": -19.27, "curve": [3.889, -19.27, 4.111, 9.56]}, {"time": 4.3333, "value": 9.56, "curve": [4.556, 9.56, 4.778, -19.27]}, {"time": 5, "value": -19.27, "curve": [5.222, -19.27, 5.444, 9.56]}, {"time": 5.6667, "value": 9.56, "curve": [5.889, 9.56, 6.111, -19.27]}, {"time": 6.3333, "value": -19.27, "curve": [6.556, -19.27, 6.778, 9.56]}, {"time": 7, "value": 9.56, "curve": [7.222, 9.56, 7.444, -19.27]}, {"time": 7.6667, "value": -19.27, "curve": [7.889, -19.27, 8.111, 9.56]}, {"time": 8.3333, "value": 9.56, "curve": [8.556, 9.56, 8.778, -19.27]}, {"time": 9, "value": -19.27, "curve": [9.222, -19.27, 9.444, 9.56]}, {"time": 9.6667, "value": 9.56, "curve": [9.889, 9.56, 10.111, -19.27]}, {"time": 10.3333, "value": -19.27, "curve": [10.444, -19.27, 10.556, -12.06]}, {"time": 10.6667, "value": -4.85}]}, "bone4": {"rotate": [{"value": -17.56, "curve": [0.167, -6.78, 0.333, 14.63]}, {"time": 0.5, "value": 14.63, "curve": [0.722, 14.63, 0.944, -23.69]}, {"time": 1.1667, "value": -23.69, "curve": [1.389, -23.69, 1.611, 14.63]}, {"time": 1.8333, "value": 14.63, "curve": [2.056, 14.63, 2.278, -23.69]}, {"time": 2.5, "value": -23.69, "curve": [2.722, -23.69, 2.944, 14.63]}, {"time": 3.1667, "value": 14.63, "curve": [3.389, 14.63, 3.611, -23.69]}, {"time": 3.8333, "value": -23.69, "curve": [4.056, -23.69, 4.278, 14.63]}, {"time": 4.5, "value": 14.63, "curve": [4.722, 14.63, 4.944, -23.69]}, {"time": 5.1667, "value": -23.69, "curve": [5.389, -23.69, 5.611, 14.63]}, {"time": 5.8333, "value": 14.63, "curve": [6.056, 14.63, 6.278, -23.69]}, {"time": 6.5, "value": -23.69, "curve": [6.722, -23.69, 6.944, 14.63]}, {"time": 7.1667, "value": 14.63, "curve": [7.389, 14.63, 7.611, -23.69]}, {"time": 7.8333, "value": -23.69, "curve": [8.056, -23.69, 8.278, 14.63]}, {"time": 8.5, "value": 14.63, "curve": [8.722, 14.63, 8.944, -23.69]}, {"time": 9.1667, "value": -23.69, "curve": [9.389, -23.69, 9.611, 14.63]}, {"time": 9.8333, "value": 14.63, "curve": [10.056, 14.63, 10.278, -23.69]}, {"time": 10.5, "value": -23.69, "curve": [10.556, -23.69, 10.611, -21.15]}, {"time": 10.6667, "value": -17.56}]}, "bone5": {"rotate": [{"value": -31.11, "curve": [0.222, -31.11, 0.444, 20.82]}, {"time": 0.6667, "value": 20.82, "curve": [0.889, 20.82, 1.111, -31.11]}, {"time": 1.3333, "value": -31.11, "curve": [1.556, -31.11, 1.778, 20.82]}, {"time": 2, "value": 20.82, "curve": [2.222, 20.82, 2.444, -31.11]}, {"time": 2.6667, "value": -31.11, "curve": [2.889, -31.11, 3.111, 20.82]}, {"time": 3.3333, "value": 20.82, "curve": [3.556, 20.82, 3.778, -31.11]}, {"time": 4, "value": -31.11, "curve": [4.222, -31.11, 4.444, 20.82]}, {"time": 4.6667, "value": 20.82, "curve": [4.889, 20.82, 5.111, -31.11]}, {"time": 5.3333, "value": -31.11, "curve": [5.556, -31.11, 5.778, 20.82]}, {"time": 6, "value": 20.82, "curve": [6.222, 20.82, 6.444, -31.11]}, {"time": 6.6667, "value": -31.11, "curve": [6.889, -31.11, 7.111, 20.82]}, {"time": 7.3333, "value": 20.82, "curve": [7.556, 20.82, 7.778, -31.11]}, {"time": 8, "value": -31.11, "curve": [8.222, -31.11, 8.444, 20.82]}, {"time": 8.6667, "value": 20.82, "curve": [8.889, 20.82, 9.111, -31.11]}, {"time": 9.3333, "value": -31.11, "curve": [9.556, -31.11, 9.778, 20.82]}, {"time": 10, "value": 20.82, "curve": [10.222, 20.82, 10.444, -31.11]}, {"time": 10.6667, "value": -31.11}]}, "tree_R": {"rotate": [{"value": -0.38, "curve": [0.114, -0.49, 0.224, -0.58]}, {"time": 0.3333, "value": -0.58, "curve": [0.778, -0.58, 1.222, 0.66]}, {"time": 1.6667, "value": 0.66, "curve": [2.111, 0.66, 2.556, -0.58]}, {"time": 3, "value": -0.58, "curve": [3.444, -0.58, 3.889, 0.66]}, {"time": 4.3333, "value": 0.66, "curve": [4.778, 0.66, 5.222, -0.58]}, {"time": 5.6667, "value": -0.58, "curve": [6.111, -0.58, 6.556, 0.66]}, {"time": 7, "value": 0.66, "curve": [7.444, 0.66, 7.889, -0.58]}, {"time": 8.3333, "value": -0.58, "curve": [8.778, -0.58, 9.222, 0.66]}, {"time": 9.6667, "value": 0.66, "curve": [10.001, 0.66, 10.336, -0.03]}, {"time": 10.6667, "value": -0.38}]}, "tree_R2": {"rotate": [{"value": -0.56, "curve": [0.168, -1.03, 0.334, -1.42]}, {"time": 0.5, "value": -1.42, "curve": [0.944, -1.42, 1.389, 1.29]}, {"time": 1.8333, "value": 1.29, "curve": [2.278, 1.29, 2.722, -1.42]}, {"time": 3.1667, "value": -1.42, "curve": [3.611, -1.42, 4.056, 1.29]}, {"time": 4.5, "value": 1.29, "curve": [4.944, 1.29, 5.389, -1.42]}, {"time": 5.8333, "value": -1.42, "curve": [6.278, -1.42, 6.722, 1.29]}, {"time": 7.1667, "value": 1.29, "curve": [7.611, 1.29, 8.056, -1.42]}, {"time": 8.5, "value": -1.42, "curve": [8.944, -1.42, 9.389, 1.29]}, {"time": 9.8333, "value": 1.29, "curve": [10.112, 1.29, 10.39, 0.24]}, {"time": 10.6667, "value": -0.56}]}, "tree_R3": {"rotate": [{"value": -0.06, "curve": [0.225, -0.74, 0.446, -1.42]}, {"time": 0.6667, "value": -1.42, "curve": [1.111, -1.42, 1.556, 1.29]}, {"time": 2, "value": 1.29, "curve": [2.444, 1.29, 2.889, -1.42]}, {"time": 3.3333, "value": -1.42, "curve": [3.778, -1.42, 4.222, 1.29]}, {"time": 4.6667, "value": 1.29, "curve": [5.111, 1.29, 5.556, -1.42]}, {"time": 6, "value": -1.42, "curve": [6.444, -1.42, 6.889, 1.29]}, {"time": 7.3333, "value": 1.29, "curve": [7.778, 1.29, 8.222, -1.42]}, {"time": 8.6667, "value": -1.42, "curve": [9.111, -1.42, 9.556, 1.29]}, {"time": 10, "value": 1.29, "curve": [10.224, 1.29, 10.447, 0.62]}, {"time": 10.6667, "value": -0.06}]}, "tree_R4": {"rotate": [{"value": 0.43, "curve": [0.279, -0.36, 0.556, -1.42]}, {"time": 0.8333, "value": -1.42, "curve": [1.278, -1.42, 1.722, 1.29]}, {"time": 2.1667, "value": 1.29, "curve": [2.611, 1.29, 3.056, -1.42]}, {"time": 3.5, "value": -1.42, "curve": [3.944, -1.42, 4.389, 1.29]}, {"time": 4.8333, "value": 1.29, "curve": [5.278, 1.29, 5.722, -1.42]}, {"time": 6.1667, "value": -1.42, "curve": [6.611, -1.42, 7.056, 1.29]}, {"time": 7.5, "value": 1.29, "curve": [7.944, 1.29, 8.389, -1.42]}, {"time": 8.8333, "value": -1.42, "curve": [9.278, -1.42, 9.722, 1.29]}, {"time": 10.1667, "value": 1.29, "curve": [10.334, 1.29, 10.501, 0.91]}, {"time": 10.6667, "value": 0.43}]}, "tree_R6": {"rotate": [{"value": 0.24, "curve": [0.257, -0.52, 0.512, -1.42]}, {"time": 0.7667, "value": -1.42, "curve": [1.211, -1.42, 1.656, 1.29]}, {"time": 2.1, "value": 1.29, "curve": [2.544, 1.29, 2.989, -1.42]}, {"time": 3.4333, "value": -1.42, "curve": [3.878, -1.42, 4.322, 1.29]}, {"time": 4.7667, "value": 1.29, "curve": [5.211, 1.29, 5.656, -1.42]}, {"time": 6.1, "value": -1.42, "curve": [6.544, -1.42, 6.989, 1.29]}, {"time": 7.4333, "value": 1.29, "curve": [7.878, 1.29, 8.322, -1.42]}, {"time": 8.7667, "value": -1.42, "curve": [9.211, -1.42, 9.656, 1.29]}, {"time": 10.1, "value": 1.29, "curve": [10.29, 1.29, 10.479, 0.8]}, {"time": 10.6667, "value": 0.24}]}, "tree_R7": {"rotate": [{"value": 0.71, "curve": [0.314, -0.09, 0.624, -1.42]}, {"time": 0.9333, "value": -1.42, "curve": [1.378, -1.42, 1.822, 1.29]}, {"time": 2.2667, "value": 1.29, "curve": [2.711, 1.29, 3.156, -1.42]}, {"time": 3.6, "value": -1.42, "curve": [4.044, -1.42, 4.489, 1.29]}, {"time": 4.9333, "value": 1.29, "curve": [5.378, 1.29, 5.822, -1.42]}, {"time": 6.2667, "value": -1.42, "curve": [6.711, -1.42, 7.156, 1.29]}, {"time": 7.6, "value": 1.29, "curve": [8.044, 1.29, 8.489, -1.42]}, {"time": 8.9333, "value": -1.42, "curve": [9.378, -1.42, 9.822, 1.29]}, {"time": 10.2667, "value": 1.29, "curve": [10.401, 1.29, 10.536, 1.05]}, {"time": 10.6667, "value": 0.71}]}, "tree_R5": {"rotate": [{"value": -0.26, "curve": [0.203, -0.86, 0.401, -1.42]}, {"time": 0.6, "value": -1.42, "curve": [1.044, -1.42, 1.489, 1.29]}, {"time": 1.9333, "value": 1.29, "curve": [2.378, 1.29, 2.822, -1.42]}, {"time": 3.2667, "value": -1.42, "curve": [3.711, -1.42, 4.156, 1.29]}, {"time": 4.6, "value": 1.29, "curve": [5.044, 1.29, 5.489, -1.42]}, {"time": 5.9333, "value": -1.42, "curve": [6.378, -1.42, 6.822, 1.29]}, {"time": 7.2667, "value": 1.29, "curve": [7.711, 1.29, 8.156, -1.42]}, {"time": 8.6, "value": -1.42, "curve": [9.044, -1.42, 9.489, 1.29]}, {"time": 9.9333, "value": 1.29, "curve": [10.179, 1.29, 10.425, 0.48]}, {"time": 10.6667, "value": -0.26}]}, "tree_L2": {"rotate": [{"value": -0.05, "curve": [0.225, 0.14, 0.446, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [1.111, 0.34, 1.556, -0.44]}, {"time": 2, "value": -0.44, "curve": [2.444, -0.44, 2.889, 0.34]}, {"time": 3.3333, "value": 0.34, "curve": [3.778, 0.34, 4.222, -0.44]}, {"time": 4.6667, "value": -0.44, "curve": [5.111, -0.44, 5.556, 0.34]}, {"time": 6, "value": 0.34, "curve": [6.444, 0.34, 6.889, -0.44]}, {"time": 7.3333, "value": -0.44, "curve": [7.778, -0.44, 8.222, 0.34]}, {"time": 8.6667, "value": 0.34, "curve": [9.111, 0.34, 9.556, -0.44]}, {"time": 10, "value": -0.44, "curve": [10.224, -0.44, 10.447, -0.25]}, {"time": 10.6667, "value": -0.05}]}, "tree_L3": {"rotate": [{"value": 0.51, "curve": [0.168, 1.02, 0.334, 1.44]}, {"time": 0.5, "value": 1.44, "curve": [0.944, 1.44, 1.389, -1.49]}, {"time": 1.8333, "value": -1.49, "curve": [2.278, -1.49, 2.722, 1.44]}, {"time": 3.1667, "value": 1.44, "curve": [3.611, 1.44, 4.056, -1.49]}, {"time": 4.5, "value": -1.49, "curve": [4.944, -1.49, 5.389, 1.44]}, {"time": 5.8333, "value": 1.44, "curve": [6.278, 1.44, 6.722, -1.49]}, {"time": 7.1667, "value": -1.49, "curve": [7.611, -1.49, 8.056, 1.44]}, {"time": 8.5, "value": 1.44, "curve": [8.944, 1.44, 9.389, -1.49]}, {"time": 9.8333, "value": -1.49, "curve": [10.112, -1.49, 10.39, -0.35]}, {"time": 10.6667, "value": 0.51}]}, "tree_L4": {"rotate": [{"value": -0.56, "curve": [0.279, 0.3, 0.556, 1.44]}, {"time": 0.8333, "value": 1.44, "curve": [1.278, 1.44, 1.722, -1.49]}, {"time": 2.1667, "value": -1.49, "curve": [2.611, -1.49, 3.056, 1.44]}, {"time": 3.5, "value": 1.44, "curve": [3.944, 1.44, 4.389, -1.49]}, {"time": 4.8333, "value": -1.49, "curve": [5.278, -1.49, 5.722, 1.44]}, {"time": 6.1667, "value": 1.44, "curve": [6.611, 1.44, 7.056, -1.49]}, {"time": 7.5, "value": -1.49, "curve": [7.944, -1.49, 8.389, 1.44]}, {"time": 8.8333, "value": 1.44, "curve": [9.278, 1.44, 9.722, -1.49]}, {"time": 10.1667, "value": -1.49, "curve": [10.334, -1.49, 10.501, -1.08]}, {"time": 10.6667, "value": -0.56}]}, "tree_L5": {"rotate": [{"value": -1.02, "curve": [0.336, -0.2, 0.668, 1.44]}, {"time": 1, "value": 1.44, "curve": [1.444, 1.44, 1.889, -1.49]}, {"time": 2.3333, "value": -1.49, "curve": [2.778, -1.49, 3.222, 1.44]}, {"time": 3.6667, "value": 1.44, "curve": [4.111, 1.44, 4.556, -1.49]}, {"time": 5, "value": -1.49, "curve": [5.444, -1.49, 5.889, 1.44]}, {"time": 6.3333, "value": 1.44, "curve": [6.778, 1.44, 7.222, -1.49]}, {"time": 7.6667, "value": -1.49, "curve": [8.111, -1.49, 8.556, 1.44]}, {"time": 9, "value": 1.44, "curve": [9.444, 1.44, 9.889, -1.49]}, {"time": 10.3333, "value": -1.49, "curve": [10.446, -1.49, 10.559, -1.3]}, {"time": 10.6667, "value": -1.02}]}, "tree_L6": {"rotate": [{"value": -1.35, "curve": [0.39, -0.79, 0.779, 1.44]}, {"time": 1.1667, "value": 1.44, "curve": [1.611, 1.44, 2.056, -1.49]}, {"time": 2.5, "value": -1.49, "curve": [2.944, -1.49, 3.389, 1.44]}, {"time": 3.8333, "value": 1.44, "curve": [4.278, 1.44, 4.722, -1.49]}, {"time": 5.1667, "value": -1.49, "curve": [5.611, -1.49, 6.056, 1.44]}, {"time": 6.5, "value": 1.44, "curve": [6.944, 1.44, 7.389, -1.49]}, {"time": 7.8333, "value": -1.49, "curve": [8.278, -1.49, 8.722, 1.44]}, {"time": 9.1667, "value": 1.44, "curve": [9.611, 1.44, 10.056, -1.49]}, {"time": 10.5, "value": -1.49, "curve": [10.556, -1.49, 10.613, -1.43]}, {"time": 10.6667, "value": -1.35}]}, "tree_L10": {"rotate": [{"value": -0.35, "curve": [0.257, 0.47, 0.512, 1.44]}, {"time": 0.7667, "value": 1.44, "curve": [1.211, 1.44, 1.656, -1.49]}, {"time": 2.1, "value": -1.49, "curve": [2.544, -1.49, 2.989, 1.44]}, {"time": 3.4333, "value": 1.44, "curve": [3.878, 1.44, 4.322, -1.49]}, {"time": 4.7667, "value": -1.49, "curve": [5.211, -1.49, 5.656, 1.44]}, {"time": 6.1, "value": 1.44, "curve": [6.544, 1.44, 6.989, -1.49]}, {"time": 7.4333, "value": -1.49, "curve": [7.878, -1.49, 8.322, 1.44]}, {"time": 8.7667, "value": 1.44, "curve": [9.211, 1.44, 9.656, -1.49]}, {"time": 10.1, "value": -1.49, "curve": [10.29, -1.49, 10.479, -0.96]}, {"time": 10.6667, "value": -0.35}]}, "tree_L11": {"rotate": [{"value": -0.86, "curve": [0.314, 0, 0.624, 1.44]}, {"time": 0.9333, "value": 1.44, "curve": [1.378, 1.44, 1.822, -1.49]}, {"time": 2.2667, "value": -1.49, "curve": [2.711, -1.49, 3.156, 1.44]}, {"time": 3.6, "value": 1.44, "curve": [4.044, 1.44, 4.489, -1.49]}, {"time": 4.9333, "value": -1.49, "curve": [5.378, -1.49, 5.822, 1.44]}, {"time": 6.2667, "value": 1.44, "curve": [6.711, 1.44, 7.156, -1.49]}, {"time": 7.6, "value": -1.49, "curve": [8.044, -1.49, 8.489, 1.44]}, {"time": 8.9333, "value": 1.44, "curve": [9.378, 1.44, 9.822, -1.49]}, {"time": 10.2667, "value": -1.49, "curve": [10.401, -1.49, 10.536, -1.23]}, {"time": 10.6667, "value": -0.86}]}, "tree_L12": {"rotate": [{"value": -1.24, "curve": [0.368, -0.54, 0.734, 1.44]}, {"time": 1.1, "value": 1.44, "curve": [1.544, 1.44, 1.989, -1.49]}, {"time": 2.4333, "value": -1.49, "curve": [2.878, -1.49, 3.322, 1.44]}, {"time": 3.7667, "value": 1.44, "curve": [4.211, 1.44, 4.656, -1.49]}, {"time": 5.1, "value": -1.49, "curve": [5.544, -1.49, 5.989, 1.44]}, {"time": 6.4333, "value": 1.44, "curve": [6.878, 1.44, 7.322, -1.49]}, {"time": 7.7667, "value": -1.49, "curve": [8.211, -1.49, 8.656, 1.44]}, {"time": 9.1, "value": 1.44, "curve": [9.544, 1.44, 9.989, -1.49]}, {"time": 10.4333, "value": -1.49, "curve": [10.512, -1.49, 10.59, -1.39]}, {"time": 10.6667, "value": -1.24}]}, "tree_L7": {"rotate": [{"value": 0.19, "curve": [0.203, 0.84, 0.401, 1.44]}, {"time": 0.6, "value": 1.44, "curve": [1.044, 1.44, 1.489, -1.49]}, {"time": 1.9333, "value": -1.49, "curve": [2.378, -1.49, 2.822, 1.44]}, {"time": 3.2667, "value": 1.44, "curve": [3.711, 1.44, 4.156, -1.49]}, {"time": 4.6, "value": -1.49, "curve": [5.044, -1.49, 5.489, 1.44]}, {"time": 5.9333, "value": 1.44, "curve": [6.378, 1.44, 6.822, -1.49]}, {"time": 7.2667, "value": -1.49, "curve": [7.711, -1.49, 8.156, 1.44]}, {"time": 8.6, "value": 1.44, "curve": [9.044, 1.44, 9.489, -1.49]}, {"time": 9.9333, "value": -1.49, "curve": [10.179, -1.49, 10.425, -0.61]}, {"time": 10.6667, "value": 0.19}]}, "tree_L8": {"rotate": [{"value": -0.35, "curve": [0.257, 0.47, 0.512, 1.44]}, {"time": 0.7667, "value": 1.44, "curve": [1.211, 1.44, 1.656, -1.49]}, {"time": 2.1, "value": -1.49, "curve": [2.544, -1.49, 2.989, 1.44]}, {"time": 3.4333, "value": 1.44, "curve": [3.878, 1.44, 4.322, -1.49]}, {"time": 4.7667, "value": -1.49, "curve": [5.211, -1.49, 5.656, 1.44]}, {"time": 6.1, "value": 1.44, "curve": [6.544, 1.44, 6.989, -1.49]}, {"time": 7.4333, "value": -1.49, "curve": [7.878, -1.49, 8.322, 1.44]}, {"time": 8.7667, "value": 1.44, "curve": [9.211, 1.44, 9.656, -1.49]}, {"time": 10.1, "value": -1.49, "curve": [10.29, -1.49, 10.479, -0.96]}, {"time": 10.6667, "value": -0.35}]}, "tree_L9": {"rotate": [{"value": -0.86, "curve": [0.314, 0, 0.624, 1.44]}, {"time": 0.9333, "value": 1.44, "curve": [1.378, 1.44, 1.822, -1.49]}, {"time": 2.2667, "value": -1.49, "curve": [2.711, -1.49, 3.156, 1.44]}, {"time": 3.6, "value": 1.44, "curve": [4.044, 1.44, 4.489, -1.49]}, {"time": 4.9333, "value": -1.49, "curve": [5.378, -1.49, 5.822, 1.44]}, {"time": 6.2667, "value": 1.44, "curve": [6.711, 1.44, 7.156, -1.49]}, {"time": 7.6, "value": -1.49, "curve": [8.044, -1.49, 8.489, 1.44]}, {"time": 8.9333, "value": 1.44, "curve": [9.378, 1.44, 9.822, -1.49]}, {"time": 10.2667, "value": -1.49, "curve": [10.401, -1.49, 10.536, -1.23]}, {"time": 10.6667, "value": -0.86}]}, "sign2": {"rotate": [{"time": 5.6667, "curve": [5.751, 0, 5.816, 9.68]}, {"time": 5.9, "value": 9.68, "curve": [5.956, 9.68, 6.011, -11.15]}, {"time": 6.0667, "value": -11.15, "curve": [6.122, -11.15, 6.178, 10.96]}, {"time": 6.2333, "value": 10.96, "curve": [6.289, 10.96, 6.344, -11.15]}, {"time": 6.4, "value": -11.15, "curve": [6.482, -11.15, 6.585, 0]}, {"time": 6.6667}], "translate": [{"time": 5.3333, "curve": [5.433, 0, 5.533, 0, 5.433, 0, 5.533, -1.86]}, {"time": 5.6333, "y": -1.86}, {"time": 5.6667, "y": 2.41, "curve": [5.788, 0, 6.045, 0, 5.74, 55.17, 5.966, 76.8]}, {"time": 6.1667, "y": 76.8, "curve": [6.271, 0, 6.563, 0, 6.341, 76.8, 6.604, 39.96]}, {"time": 6.6667, "y": 2.41}, {"time": 6.7, "y": -1.86, "curve": [6.8, 0, 6.9, 0, 6.8, -1.86, 6.9, 0]}, {"time": 7}], "scale": [{"time": 5.3333, "curve": [5.433, 1, 5.533, 1.045, 5.433, 1, 5.533, 0.939]}, {"time": 5.6333, "x": 1.045, "y": 0.939}, {"time": 5.6667, "x": 0.952, "y": 1.059, "curve": [5.74, 0.986, 5.966, 1, 5.74, 1.017, 5.966, 1]}, {"time": 6.1667, "curve": [6.341, 1, 6.604, 0.976, 6.341, 1, 6.604, 1.029]}, {"time": 6.6667, "x": 0.952, "y": 1.059}, {"time": 6.7, "x": 1.045, "y": 0.939, "curve": [6.8, 1.045, 6.9, 1, 6.8, 0.939, 6.9, 1]}, {"time": 7}]}, "shadow": {"scale": [{"time": 5.6667, "curve": [5.74, 0.742, 5.966, 0.637, 5.74, 0.742, 5.966, 0.637]}, {"time": 6.1667, "x": 0.637, "y": 0.637, "curve": [6.341, 0.637, 6.604, 0.817, 6.341, 0.637, 6.604, 0.817]}, {"time": 6.6667}]}, "sign": {"translate": [{"time": 6.6667, "curve": [6.819, 0, 7.181, 0, 6.697, 0, 6.797, 66.73]}, {"time": 7.3333, "y": 65.99, "curve": [7.667, 0, 8, 0, 7.998, 65.06, 8.011, 0]}, {"time": 8.3333}]}}, "attachments": {"default": {"sign": {"sign": {"deform": [{"time": 7, "curve": [7.056, 0, 7.111, 1]}, {"time": 7.1667, "vertices": [-3.0898, 6.75827, -3.0898, 6.75827, 6.6073, -2.83414, 5.26692, -5.01422, 5.06262, -5.01422, -4.55458, 4.57819, 6.19054, -2.83414], "curve": [7.222, 0, 7.278, 1]}, {"time": 7.3333, "vertices": [5.68439, -2.68457, 5.68439, -2.68457, -2.9641, 8.35596, -0.50775, 7.04791, -0.30815, 7.04791, 7.00983, -5.41431, -2.55695, 8.35596], "curve": [7.389, 0, 7.444, 1]}, {"time": 7.5, "vertices": [-3.0898, 6.75827, -3.0898, 6.75827, 6.6073, -2.83414, 2.92693, -5.01422, 2.72263, -5.01422, -6.89458, 4.57819, 6.19054, -2.83414], "curve": [7.556, 0, 7.611, 1]}, {"time": 7.6667}]}}}}}}}