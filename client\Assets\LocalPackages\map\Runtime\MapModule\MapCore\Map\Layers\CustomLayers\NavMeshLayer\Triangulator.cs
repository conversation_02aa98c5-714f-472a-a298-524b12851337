﻿ 



 
 

//created by wzw at 2019/11/23

using UnityEngine;
using System.Collections.Generic;
using Poly2Tri;
using Poly2Tri.Triangulation.Polygon;
using Poly2Tri.Triangulation;
using System.Linq;
using Polygon = Poly2Tri.Triangulation.Polygon.Polygon;

using TriangleNet;
using TriangleNet.Geometry;
using TriangleNet.Smoothing;
using TriangleNet.Tools;

namespace TFW.Map
{
    public static class Triangulator
    {
        static Triangulator()
        {
            mTrianglePools = new TrianglePool[System.Environment.ProcessorCount];
            for (int i = 0; i < mTrianglePools.Length; ++i)
            {
                mTrianglePools[i] = new TrianglePool();
            }
        }

        public static void TriangulatePolygons(List<List<Vector3>> noneHoles, List<List<Vector3>> holes, bool useDelaunay, float minimumAngle, float maximumArea, TrianglePool pool, out Vector3[] meshVertices, out int[] meshIndices)
        {
            if (pool == null)
            {
                pool = GetPool(0);
            }

            holes = GetValidHoles(holes);
            Delaunay(noneHoles, holes, useDelaunay, minimumAngle, maximumArea, pool, out meshVertices, out meshIndices);
        }

        public static void TriangulatePolygons(List<List<Vector3>> noneHoles, List<List<Vector3>> holes, TrianglePool pool, out Vector3[] meshVertices, out int[] meshIndices)
        {
            if (pool == null)
            {
                pool = GetPool(0);
            }

            holes = GetValidHoles(holes);
            Delaunay(noneHoles, holes, false, 0, 0, pool, out meshVertices, out meshIndices);
        }

        public static void TriangulatePolygon(List<Vector3> polygonVertices, out Vector3[] meshVerticesOut, out int[] meshIndicesOut, bool swapIndex = true)
        {
            var points = new PolygonPoint[polygonVertices.Count];
            for (int i = 0; i < polygonVertices.Count; ++i)
            {
                points[i] = new PolygonPoint(polygonVertices[i].x, polygonVertices[i].z);
            }

            var polygon = new Polygon(points);

            //三角划分多边形
            P2T.Triangulate(polygon);

            List<Vector3> meshVertices = new List<Vector3>();
            List<int> meshIndices = new List<int>();

            var triangles = polygon.Triangles;
            for (int i = 0; i < triangles.Count; ++i)
            {
                for (int j = 0; j < 3; ++j)
                {
                    var pt = triangles[i].Points[j];
                    AddVertex(pt, meshVertices, meshIndices);
                }
            }

            if (swapIndex)
            {
                int triCount = meshIndices.Count / 3;
                for (int i = 0; i < triCount; ++i)
                {
                    int temp = meshIndices[i * 3];
                    meshIndices[i * 3] = meshIndices[i * 3 + 2];
                    meshIndices[i * 3 + 2] = temp;
                }
            }

            meshVerticesOut = meshVertices.ToArray();
            meshIndicesOut = meshIndices.ToArray();
        }

        static void AddVertex(TriangulationPoint pt, List<Vector3> vertices, List<int> indices)
        {
            int vtxIdx = -1;
            float x = (float)pt.X;
            float z = (float)pt.Y;

            for (int i = 0; i < vertices.Count; ++i)
            {
                if (Mathf.Approximately(x, vertices[i].x) &&
                    Mathf.Approximately(z, vertices[i].z))
                {
                    vtxIdx = i;
                    break;
                }
            }

            if (vtxIdx == -1)
            {
                vertices.Add(new Vector3(x, 0, z));
                vtxIdx = vertices.Count - 1;
            }
            indices.Add(vtxIdx);
        }

        static void Delaunay(List<List<Vector3>> noneHoles, List<List<Vector3>> holes, bool conformingDelaunay, float minimumAngle, float maximumArea, TrianglePool pool, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshVertices = null;
            meshIndices = null;
            if (noneHoles.Count == 0)
            {
                return;
            }

            TriangleNet.Geometry.Polygon poly = new TriangleNet.Geometry.Polygon();
            try
            {
                // Define Contour.
                if (noneHoles != null)
                {
                    for (int i = 0; i < noneHoles.Count; ++i)
                    {
                        var contour = ConvertPathToContour(noneHoles[i], 0);
                        if (contour != null)
                        {
                            poly.Add(contour);
                        }
                    }
                }
                // Add holes
                if (holes != null)
                {
                    for (int i = 0; i < holes.Count; ++i)
                    {
                        var hole = ConvertPathToContour(holes[i], 1);
                        if (hole != null)
                        {
                            poly.Add(hole, true);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError(e.ToString());
            }

            TriangleNet.Mesh mesh = null;
            if (conformingDelaunay)
            {
                // Set quality and constraint options.
                var options = new TriangleNet.Meshing.ConstraintOptions() { ConformingDelaunay = conformingDelaunay, SegmentSplitting = 2 };
                var quality = new TriangleNet.Meshing.QualityOptions();

                quality.MinimumAngle = minimumAngle;
                quality.MaximumArea = maximumArea;

                // Generate mesh.
                mesh = (TriangleNet.Mesh)poly.Triangulate(options, quality);

                ProcessMesh(mesh, out meshVertices, out meshIndices);
            }
            else
            {
                try
                {
#if false
                    mesh = (TriangleNet.Mesh)poly.Triangulate();
                    ProcessMesh(mesh, out meshVertices, out meshIndices);
#else

                    Debug.Assert(pool != null);
                    //if (pool == null)
                    //{
                    //    //为了多线程时减少内存消耗,多线程下每个线程给一个triangle pool
                    //    pool = new TrianglePool();
                    //}

                    var predicates = new RobustPredicates();
                    var config = new Configuration()
                    {
                        Predicates = () => predicates,
                        TrianglePool = () => pool.Restart()
                    };

                    var mesher = new TriangleNet.Meshing.GenericMesher(config);
                    mesh = (TriangleNet.Mesh)mesher.Triangulate(poly);
                    ProcessMesh(mesh, out meshVertices, out meshIndices);

                    //pool.Clear();
#endif
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.Message);
                }
            }
        }

        static void ProcessMesh(TriangleNet.Mesh mesh, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshVertices = null;
            meshIndices = null;
            if (mesh != null)
            {
                var triangles = mesh.Triangles;
                Dictionary<Vector3Int, int> vMark = new Dictionary<Vector3Int, int>();
                //ulong m = 0;
                List<Vector3Int> vertices = new List<Vector3Int>();
                List<int> indices = new List<int>();
                Vertex[] verts = mesh.Vertices.ToArray();
                int vCount = 0;
                int[] oneTriangle = new int[3];
                foreach (var triangle in triangles)
                {
                    for (int i = 2; i >= 0; --i)
                    {
                        var index = triangle.GetVertexID(i);
                        int x = (int)(verts[index].X * 1000);
                        int y = (int)(verts[index].Y * 1000);
                        Vector3Int v = new Vector3Int(x, 0, y);
                        int vIdx = 0;
                        if (!vMark.TryGetValue(v, out vIdx))
                        {
                            vIdx = vCount++;
                            vMark.Add(v, vIdx);
                            vertices.Add(v);
                        }

                        oneTriangle[i] = vIdx;
                    }

                    //check if triangle is valid
                    bool valid = true;
                    if (oneTriangle[0] == oneTriangle[1] || oneTriangle[0] == oneTriangle[2] || oneTriangle[1] == oneTriangle[2])
                    {
                        valid = false;
                    }
                    if (valid)
                    {
                        for (int i = 2; i >= 0; --i)
                        {
                            indices.Add(oneTriangle[i]);
                        }
                    }
                }

                meshVertices = new Vector3[vertices.Count];
                for (int i = 0; i < vertices.Count; ++i)
                {
                    meshVertices[i] = new Vector3(vertices[i].x / 1000.0f, 0, vertices[i].z / 1000.0f);
                }
                meshIndices = indices.ToArray();
            }
        }

        static UnityEngine.Mesh TriangulatePolygon(Polygon polygon, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshVertices = null;
            meshIndices = null;
            if (polygon != null)
            {
                //三角划分多边形
                P2T.Triangulate(polygon);

                List<Vector3> vertices = new List<Vector3>();
                List<int> indices = new List<int>();

                var points = polygon.Points;
                var triangles = polygon.Triangles;

                for (int i = 0; i < triangles.Count; ++i)
                {
                    for (int j = 0; j < 3; ++j)
                    {
                        var pt = triangles[i].Points[j];
                        AddVertex(pt, vertices, indices);
                    }
                }

                int triCount = indices.Count / 3;
                for (int i = 0; i < triCount; ++i)
                {
                    int temp = indices[i * 3];
                    indices[i * 3] = indices[i * 3 + 2];
                    indices[i * 3 + 2] = temp;
                }

                //生成mesh
                var mesh = new UnityEngine.Mesh();

                meshVertices = vertices.ToArray();
                meshIndices = indices.ToArray();

                mesh.vertices = vertices.ToArray();
                mesh.triangles = indices.ToArray();

                return mesh;
            }

            return null;
        }

        static TriangleNet.Geometry.Contour ConvertPathToContour(List<Vector3> path, int label = 0)
        {
            if (path.Count > 0)
            {
                var points = new TriangleNet.Geometry.Vertex[path.Count];

                for (int i = 0; i < path.Count; ++i)
                {
                    points[i] = new TriangleNet.Geometry.Vertex(path[i].x, path[i].z);
                }

                return new TriangleNet.Geometry.Contour(points, label);
            }
            return null;
        }

        static List<List<Vector3>> GetValidHoles(List<List<Vector3>> holes)
        {
            List<List<Vector3>> validHoles = new List<List<Vector3>>();
            for (int i = 0; i < holes.Count; ++i)
            {
                var vertices = Utils.RemoveDuplicated1(holes[i], 0.01f);
                if (vertices.Count >= 3)
                {
                    validHoles.Add(vertices);
                }
            }
            return validHoles;
        }

        public static TriangleNet.TrianglePool GetPool(int index)
        {
            return mTrianglePools[index];
        }

        static TriangleNet.TrianglePool[] mTrianglePools;
    }
}
