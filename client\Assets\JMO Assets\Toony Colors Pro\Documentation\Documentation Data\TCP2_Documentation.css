﻿/* GENERAL */

body
{
	font-family: Helvetica, Verdana, Arial, sans-serif;
	background-color: white;
	color: black;
	width:			980px;
	margin-left:	auto;
	margin-right:	auto;
	margin-top: 	0px;
	margin-bottom: 0px;
	
	font-size: 14px;
}
body div.main
{
	margin:	0px;
	padding: 0px;
	border-left:solid 4px #E0E0E0;
	border-right:solid 4px #E0E0E0;
	background-color: #F0F0F0;
	line-height: 1.3;
}
p { margin: 8px; padding: 0px; }
h1 { font-size: 24px; font-family: 'Helvetica', 'Arial', sans-serif; margin-bottom: 2px; }
h2 { font-size: 22px; font-family: 'Helvetica', 'Arial', sans-serif; margin-top: 2px; margin-bottom: 2px; }
h3 { font-size: 18px; font-family: 'Helvetica', 'Arial', sans-serif; margin-top: 16px; margin-bottom: 6px; color: #606060; }
h4 { font-size: 16px; font-family: 'Helvetica', 'Arial', sans-serif; margin-top: 16px; margin-bottom: 8px; color: #606060;  border-bottom: solid 1px #909090; display:inline-block; }
h5 { font-size: 14px; font-family: 'Helvetica', 'Arial', sans-serif; margin-top: 4px; margin-bottom: 4px; padding: 2px; color: #747474; }
strong { font-weight: normal; color: #FF5500; }

ul { margin: 5px 0px 5px 0px; list-style-type: circle; }

.separator { border-top: solid 1px #d8d8d8; border-bottom: solid 1px #d8d8d8; height: 1px; margin-bottom:8px; }
.title { text-align: center; color: #ff5500; padding-top: 8px; }
.center { text-align: center; }

a { text-decoration:none; border-bottom:dashed 1px #ff4000; color:#ff4000; padding-left:2px; padding-right:2px; }
a:hover { text-decoration:none; background-color:#ff8000; border-bottom: none; border-radius:2px; color:#fff; }

div.toplink
{
	position: fixed;
	left: 10px;
	top: 10px;
	font-weight: bold;
}
div.toplink a
{
	text-decoration: none;
	background-color: #f8f8f8;
	border-radius: 3px;
	border: 1px solid #ff8000;
	display: inline-block;
	color: #ff8000;
	font-size: 13px;
	padding: 6px 12px;
}
div.toplink a:hover { background-color: #f0f0f0; border-color: #f07000; color: #f07000 }
div.toplink a:active { position:relative; top:1px; }


.header
{
	text-align: left;
	margin: 20px 12px 0px 12px;
	font-size: 24px;
	font-family: 'Helvetica', 'Arial', sans-serif;
	margin-bottom: 2px;
	color: #ff5500;
}
.footer
{
	color: #808080;
	padding: 8px;
	text-align: center;
	font-size: 12px;
}

.img
{
	background-image: url('TCP2_HelpImages.png');
	display: inline-block;
	margin-left: 10px;
	margin-right: 10px;
	width: 160px;
	height: 160px;
}
div.img
{
	position: relative;
	margin-bottom: 36px;
}
div.img span
{
	position: absolute;
	bottom: -36px;
	width: 100%;
	min-height: 26px;
	display: table-cell;
    vertical-align: middle;
	text-align: center;
	font-family: Tahoma, Geneva, sans-serif;
	font-size: 15px;
	color: #909090;
	background-color: #fcfcfc;
}

/* Rounded Box */

div.box
{
	margin-left: 10px;
	margin-right: 10px;
	margin-bottom: 6px;
	padding: 8px;
	border-radius: 8px;
	background-color: #fcfcfc;
	border: solid 1px #d8d8d8;
	/*text-align: justify;*/
}

div.box pre
{
	background-color: #e0e0e0;
	margin: 2px 0px 2px 0px;
}

div.header:target + div { border-color: #ff8000; }
div.header:target:before { content: '► '; }
div.header:target { animation: highlight 1s linear; -webkit-animation: highlight 1s linear; }
div.header:target + div { animation: highlight2 1s linear; -webkit-animation: highlight2 1s linear; }
@-webkit-keyframes highlight
{
	0% { color: #f0f0f0; }
	20% { color: #ff8000; }
	40% { color: #f0f0f0; }
	60% { color: #ff8000; }
	80% { color: #f0f0f0; }
	100% { color: #ff8000; }
}
@keyframes highlight
{
	0% { color: #f0f0f0; }
	20% { color: #ff8000; }
	40% { color: #f0f0f0; }
	60% { color: #ff8000; }
	80% { color: #f0f0f0; }
	100% { color: #ff8000; }
}
@-webkit-keyframes highlight2
{
	0% { border-color: #fff; }
	20% { border-color: #ff8000; }
	40% { border-color: #fff; }
	60% { border-color: #ff8000; }
	80% { border-color: #fff; }
	100% { border-color: #ff8000; }
}
@keyframes highlight2
{
	0% { border-color: #fff; }
	20% { border-color: #ff8000; }
	40% { border-color: #fff; }
	60% { border-color: #ff8000; }
	80% { border-color: #fff; }
	100% { border-color: #ff8000; }
}

div.section { margin-bottom: 8px; }
div.section:target  h4{ color: #ff8000; border-color: #ff8000; }
div.section:target { padding-left: 5px; padding-right: 5px; border-left: solid 2px #ff8000; border-right: solid 2px #ff8000; border-radius: 8px; }
div.section:target h4:before { content: '► '; }

ul.options { list-style-type: square; }

/* Index */

div.index { width: 100%; position: relative; height:460px; }
div.index ul { list-style-type: none; font-size: 14px; }
div.index ul li { line-height: 1.4em; }
div.index ul ul { list-style-type: square; padding-left: 2em; }
div.index div.left { width:33%; position:absolute; }
div.index div.mid { width:33%; position:absolute; left:240px; }
div.index div.right { width:33%; position:absolute; left:480px; }
div.index ul li a { border: none; }

div.index div.column { display: inline-block; vertical-align: top; }

/* Other Assets Menu */

table.amenu
{
	padding: 0px;
	margin: 0xp;
	border-collapse: collapse;
	border: 0px;
	border-spacing:0;
	text-align: center;
	
	font-size:12px;
	width:100%;
}
table.amenu td
{
	border-right: solid 1px #b0b0b0;
	background: #e0e0e0;
	text-shadow: -1px -1px 0px rgba(255, 255, 255, 0.4);
}
table.amenu th
{
	border-right: solid 1px #b0b0b0;
	background: #f0f0f0;
	text-shadow: -1px -1px 0px rgba(255, 255, 255, 0.4);
}

table.amenu td a
{
	padding-top:	8px;
	padding-bottom:	8px;
	display: block;
	color:	#303030;
	text-decoration: none;
	border-top: solid 1px #b0b0b0;
	border-bottom: solid 1px #b0b0b0;
}
table.amenu td a:hover
{
	color:	#fff;
	background: #b0b0b0;
	text-decoration: none;
	text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.2);
}

div.otherassets
{
	color: #606060;
	font-size: 12px;
	padding-top: 4px;
	margin-bottom: 4px;
	padding-left: 10px;
	letter-spacing: 1px;
	text-align: left;
}

/* BOXES */

div.helpbox { margin-left: auto; margin-right: auto; margin-top: 8px; margin-bottom: 8px; padding: 4px; border: solid 1px; border-radius: 8px; text-align: left;
background: #e0ebf6;
border-color: #356bb1;
color: #356bb1;
}
div.helpbox td:first-child { padding-right: 4px; }
div.warning { margin-left: auto; margin-right: auto; margin-top: 8px; padding: 4px; border: solid 1px; border-radius: 8px; text-align: left;
background: #fff2b7;
border-color: #cf6d16;
color: #cf6d16;
}
div.warning td:first-child { padding-right: 4px; }

table.template_ui
{
	font-size:13px;
	border-collapse: collapse;
}
table.template_ui th { border: solid 1px #808080; margin:0px; padding: 4px; }
table.template_ui td { border: solid 1px #808080; margin:0px; padding: 4px; }

table.template_ui td.left
{
	font-weight: normal;
	color: #FF5500;
	padding-right: 8px;
}

/* IMAGES */

.icon { display: inline-block; width: 16px; height: 16px; background: url('TCP2_HelpImages.png') no-repeat 0px 0px; }