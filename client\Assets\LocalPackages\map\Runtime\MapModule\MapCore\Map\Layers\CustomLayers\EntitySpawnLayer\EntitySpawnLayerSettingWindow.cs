﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class EntitySpawnLayerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
            mNPCMoveRange = new Vector2(MapCoreDef.MAP_NPC_MOVE_RANGE_MIN, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX);
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mHorizontalRegionCount = EditorGUILayout.IntField("X Region Tile Count", mHorizontalRegionCount);
            mVerticalRegionCount = EditorGUILayout.IntField("Z Region Tile Count", mVerticalRegionCount);
            GUILayout.EndHorizontal();

            strategyType = (SpawnPointGenerateStrategyType)EditorGUILayout.Popup(new GUIContent("Strategy", "刷新点生成策略"), (int)strategyType, mStrategyTypeTexts);

            EditorGUILayout.BeginVertical("GroupBox");
            if (strategyType == SpawnPointGenerateStrategyType.Random)
            {
                mOneMeterHorizontalResolution = EditorGUILayout.IntField("Horizontal Resolution", mOneMeterHorizontalResolution);
                mOneMeterVerticalResolution = EditorGUILayout.IntField("Vertical Resolution", mOneMeterVerticalResolution);
                mNPCDensity = EditorGUILayout.FloatField("NPC Density (Spawn Count = Empty Area / NPC Density)", mNPCDensity);
            }
            else if (strategyType == SpawnPointGenerateStrategyType.Grid)
            {
                mStartOffset = EditorGUILayout.Vector3Field("Offset", mStartOffset);
                mPointDeltaDistance = EditorGUILayout.Vector3Field("Delta Distance Between Neighbours", mPointDeltaDistance);
                mXOffset = EditorGUILayout.FloatField("X Offset", mXOffset);
                mRandomRange = EditorGUILayout.FloatField("Random Range", mRandomRange);
            }
            else
            {
                Debug.Assert(false, "unknown strategy!");
            }
            EditorGUILayout.EndVertical();
            mNPCMoveRange = EditorGUILayout.Vector2Field("NPC Move Range", mNPCMoveRange);
            mNPCMoveWaypointCount = EditorGUILayout.IntField("NPC Move Waypoint Count", mNPCMoveWaypointCount);

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap as EditorMap;
                bool valid = CheckParameter();
                if (valid)
                {
                    float tileWidth = mLayerWidth / (float)mHorizontalRegionCount;
                    float tileHeight = mLayerHeight / (float)mVerticalRegionCount;

                    ISpawnPointGenerationStrategy s = null;
                    if (strategyType == SpawnPointGenerateStrategyType.Random)
                    {
                        s = new GenerateSpawnPointsRandomly(mNPCDensity, mNPCMoveRange.y, mOneMeterHorizontalResolution, mOneMeterVerticalResolution);
                    }
                    else if (strategyType == SpawnPointGenerateStrategyType.Grid)
                    {
                        s = new GenerateSpawnPointsInGridLayout(mStartOffset, mPointDeltaDistance, mXOffset, mRandomRange, mNPCMoveRange.y);
                    }
                    else
                    {
                        Debug.Assert(false, "unknown strategy!");
                    }
                    map.CreateEntitySpawnLayer(MapCoreDef.MAP_LAYER_NODE_ENTITY_SPAWN, mLayerWidth, mLayerHeight, tileWidth, tileHeight, mNPCMoveRange, mNPCMoveWaypointCount, s);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mHorizontalRegionCount <= 0 || mVerticalRegionCount <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public int mHorizontalRegionCount = 40;
        public int mVerticalRegionCount = 40;
        public int mOneMeterHorizontalResolution = 4;
        public int mOneMeterVerticalResolution = 4;
        public Vector2 mNPCMoveRange;
        public float mNPCDensity = MapCoreDef.MAP_NPC_DENSITY;
        public int mNPCMoveWaypointCount = MapCoreDef.MAP_NPC_WAYPOINT_COUNT;
        public Vector3 mStartOffset = new Vector3(4.5f, 0, 3.75f);
        public Vector2 mPointDeltaDistance = new Vector2(9, 7.5f);
        public float mXOffset = 4.5f;
        public float mRandomRange = 1.5f;
        public SpawnPointGenerateStrategyType strategyType = SpawnPointGenerateStrategyType.Random;

        string[] mStrategyTypeTexts = new string[] { "Random", "Grid" };
    }
}

#endif