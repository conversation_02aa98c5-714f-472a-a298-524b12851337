﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2020.3.11
 */
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    //编辑器的数据导出器,注意数据是分段的,每段新版本的内容只能放在段的最后,这样才能在老版本不更新地图代码的情况下兼容!
    class MapDataSection
    {
        public long positionInStream;
        public long startOffset;
    }

    [Black]
    public partial class MapBinaryExporter1
    {
        MapDataSection[] mSections = new MapDataSection[(int)MapDataSectionType.Total];

        void BeginSection(MapDataSectionType type, BinaryWriter writer)
        {
            int idx = (int)type;
            mSections[idx].startOffset = writer.BaseStream.Position;
        }

        void SaveHeader(BinaryWriter writer)
        {
            int nSections = mSections.Length;
            writer.Write(nSections);
            for (int i = 0; i < nSections; ++i)
            {
                mSections[i] = new MapDataSection();
                mSections[i].positionInStream = writer.BaseStream.Position;
                //先写一个占位符,在FixHeader的时候填入实际的数据偏移
                writer.Write(mSections[i].startOffset);
            }
        }

        void FixHeader(BinaryWriter writer)
        {
            int nSections = mSections.Length;
            for (int i = 0; i < nSections; ++i)
            {
                //跳到占位符的位置然后填入真是的偏移值
                Utils.WriteAndJump(writer, mSections[i].positionInStream, mSections[i].startOffset);
            }
        }

        public bool Save(string projectFolderPath)
        {
            string errorMsg = CheckCorrectness();
            if (errorMsg.Length > 0)
            {
                EditorUtility.DisplayDialog("Exporting Failed", errorMsg, "OK");
                return false;
            }

            mProjectFolder = projectFolderPath;

            mIDExport = new IDExporter();

            var dataPath = projectFolderPath + "/" + "mapData1.bytes";
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteString(writer, "ncmp");

            int majorVersion = 2;
            int minorVersion = 1;
            writer.Write(majorVersion);
            writer.Write(minorVersion);

            SaveHeader(writer);

            SaveSetting(writer, projectFolderPath);
            GenerateRiverAssets();
            SaveModelTemplates(writer);
            SaveTerrainPrefabManager(writer);
            SaveVaryingTileSizeTerrainPrefabManager(writer);
            SaveCamera(writer);
            SaveMapLayers(writer);
            SaveMapObstacles(writer);
            SaveGridRegionSetting(writer);

            FixHeader(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);

            writer.Close();

            return true;
        }

        //检测正确性
        string CheckCorrectness()
        {
            string errorMessage = "";
            var map = Map.currentMap;

            int nLayers = map.GetMapLayerCount();
            for (int i = 0; i < nLayers; ++i)
            {
                float nextLOD = -1;
                var layer = map.GetMapLayerByIndex(i);
                var layerData = layer.GetLayerData();
                if (layerData != null)
                {
                    var lodConfig = layerData.lodConfig;
                    if (lodConfig != null)
                    {
                        var lodSetting = lodConfig.lodConfigs;
                        for (int k = 0; k < lodSetting.Length; ++k)
                        {
                            if (lodSetting[k].changeZoom < nextLOD + 1)
                            {
                                errorMessage += string.Format("Invalid lod start index {0} for map layer \"{1}\"\n", lodSetting[k].changeZoom, layer.name);
                            }
                            nextLOD = lodSetting[k].changeZoom;
                        }
                    }
                }
            }

            //check river layer
            var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (riverLayer != null)
            {
                if (riverLayer.needGenerateAssets)
                {
                    errorMessage += "You need generate river assets before exporting!\n";
                }
            }

            return errorMessage;
        }

        void GenerateRiverAssets()
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (layer == null)
            {
                return;
            }

            //make sure we export river model templates
            foreach (var p in layer.riverPrefabInfos)
            {
                Map.currentMap.GetOrCreateModelTemplate(p.Key, p.Value, false, false);
            }
        }

        T GetMapLayer<T>(List<MapLayerBase> layers) where T : MapLayerBase
        {
            for (int i = 0; i < layers.Count; ++i)
            {
                if (layers[i].GetType() == typeof(T))
                {
                    return layers[i] as T;
                }
            }
            return null;
        }

        void SaveMapLayers(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var validLayers = GetValidMapLayers(map);

            var gridModelLayer = GetMapLayer<EditorGridModelLayer>(validLayers);
            SaveGridModelLayer(writer, gridModelLayer);

            var modelLayer = GetMapLayer<ModelLayer>(validLayers);
            SaveModelLayer(writer, modelLayer);

            var circleBorderLayer = GetMapLayer<CircleBorderLayer>(validLayers);
            SaveCircleBorderLayer(writer, circleBorderLayer);

            var blendTerrainLayer = GetMapLayer<BlendTerrainLayer>(validLayers);
            bool saved = SaveBlendTerrainLayer(writer, blendTerrainLayer);
            if (!saved)
            {
                saved = SaveSimpleBlendTerrainLayer(writer, blendTerrainLayer);
                if (!saved)
                {
                    SaveTileBlockTerrainLayer(writer, blendTerrainLayer);
                }
            }

            var varyingTileSizeTerrainLayer = GetMapLayer<VaryingTileSizeTerrainLayer>(validLayers);
            SaveVaryingTileSizeTerrainLayer(writer, varyingTileSizeTerrainLayer);

            var railwayLayer = GetMapLayer<RailwayLayer>(validLayers);
            SaveRailwayLayer(writer, railwayLayer);

            var lodLayer = GetMapLayer<LODLayer>(validLayers);
            SaveLODLayer(writer, lodLayer);

            var riverLayer = GetMapLayer<PolygonRiverLayer>(validLayers);
            SavePolygonRiverLayer(writer, riverLayer);

            var complexGridLayer = GetMapLayer<EditorComplexGridModelLayer>(validLayers);
            SaveComplexGridModelLayer(writer, complexGridLayer, VersionSetting.ComplextGridModelLayerStructVersion, MapDataSectionType.GridModelLayer2);

            var decorationBorderLayer = GetMapLayer<DecorationBorderLayer>(validLayers);
            SaveComplexGridModelLayer(writer, decorationBorderLayer, VersionSetting.ComplextGridModelLayerStructVersion, MapDataSectionType.DecorationBorderLayer);

            var splitFogLayer = GetMapLayer<SplitFogLayer>(validLayers);
            SaveSplitFogLayer(writer, splitFogLayer);

            var territoryLayer = GetMapLayer<EditorTerritoryLayer>(validLayers);
            //SaveEditorTerritoryLayer(writer, territoryLayer);
            SaveNewEditorTerritoryLayer(writer, territoryLayer);

            var regionLayer = GetMapLayer<RegionLayer>(validLayers);
            SaveRegionLayer(writer, regionLayer);

            var regionColorLayer = GetMapLayer<RegionColorLayer>(validLayers);
            SaveRegionColorLayer(writer, regionColorLayer);

            //export plugin layers
            ExportPluginLayers(writer);
        }

        void ExportPluginLayers(BinaryWriter writer)
        {
            SavePluginLayerList(writer);

            var map = Map.currentMap;
            int layerCount = map.GetMapLayerCount();
            for (int i = 0; i < layerCount; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (MapPlugin.IsPluginLayer(layer.name))
                {
                    var pluginLayer = layer as MapPluginLayerBase;
                    pluginLayer.SaveGameData(SLGMakerEditor.instance.exportFolder);
                }
            }
        }

        string ConvertToRuntimeLayerName(string editorLayerName)
        {
            if (editorLayerName == MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER)
            {
                return MapCoreDef.MAP_LAYER_NODE_RUNTIME_RIVER;
            }
            return editorLayerName;
        }

        List<MapLayerBase> GetValidMapLayers(Map map)
        {
            List<MapLayerBase> validMapLayers = new List<MapLayerBase>();
            int n = map.GetMapLayerCount();
            for (int i = 0; i < n; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (layer is NavMeshLayer ||
                    layer is SpriteTileLayer ||
                    layer is NPCRegionLayer ||
                    layer is EntitySpawnLayer ||
                    layer is RuinLayer ||
                    layer is CameraColliderLayer ||
                    layer is MapCollisionLayer)
                {
                    continue;
                }
                validMapLayers.Add(layer);
            }
            return validMapLayers;
        }

        //保存地图上prefab的障碍物数据
        void SaveMapObstacles(BinaryWriter writer)
        {
            SaveLocalObstacles(writer);
            SaveGlobalObstacles(writer);
            SaveCameraCollider(writer);
        }

        IDExporter mIDExport = null;
        //int majorVersion = 1;
        string mProjectFolder;
        Dictionary<string, int> mStringTableIndices = new Dictionary<string, int>();
    }

}

#endif