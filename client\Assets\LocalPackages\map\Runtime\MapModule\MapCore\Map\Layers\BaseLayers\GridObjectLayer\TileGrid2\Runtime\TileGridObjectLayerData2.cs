﻿ 



 
 


//#define USE_CULL

using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;

namespace TFW.Map
{
    //管理一个大块地表物体的数据层,将Tile中所有第一层子节点打散管理,分帧加载物体
    public sealed partial class TileGridObjectLayerData2 : MapLayerData
    {
        class DynamicDecorationObject
        {
            public List<int> objectIDsInEachLOD = new List<int>();
        }

        class RemovableObjectData
        {
            public List<RemovableLODObjectData> lods = new List<RemovableLODObjectData>();
        }

        class RemovableLODObjectData
        {
            public RemovableLODObjectData(int viewID)
            {
                this.viewID = viewID;
            }
            //每个view ID对应多个格子的tile object ID
            public int viewID;
            public List<int> tileObjectIDs = new List<int>();
        }

        class NPCInfo
        {
            public NPCInfo(float x, float z, float radius)
            {
                this.centerX = x;
                this.centerZ = z;
                this.radius = radius;
            }

            public float centerX;
            public float centerZ;
            public float radius;
        }

        public TileGridObjectLayerData2(MapLayerDataHeader header, MapLayerLODConfig config, Map map, bool enableObjectMaterialChange) : base(header,
            config, map)
        {
            mEnableObjectMaterialTypeChange = enableObjectMaterialChange;
        }

        public IEnumerator LoadAsync(Rect realLayerBounds, KeepScaleConfig[] scaleConfig, bool asyncLoading)
        {
            mLastViewport = new Rect(-10000, -10000, 0, 0);
            mRealLayerBounds = realLayerBounds;
            mScaleUpdater = new KeepScaleUpdater(scaleConfig);
            var dataPath = MapCoreDef.GetMapBigTileDataPath(map.dataFolder);

           
            if (MapModuleResourceMgr.Exists(dataPath))
            {
                LoadAllTiles(dataPath, asyncLoading);
            }
            else
            {
                InitLayer();

                int idx = 0;
                for (int i = 0; i < mRows; ++i)
                {
                    for (int j = 0; j < mCols; ++j)
                    {
                        mBigTileDatas[idx] = LoadTile(j, i);
                        ++idx;
                    }
                }
            }

            mDontUpdateTileBigObjectCulling = new bool[mRows, mCols];
            var stream = MapModuleResourceMgr.LoadTextStream(MapCoreDef.GetGridModelLayerHeaderFilePath(map.dataFolder), true);


            if (stream == null)
            {
                Debug.LogError($"{dataPath}===>>>{MapCoreDef.GetGridModelLayerHeaderFilePath(map.dataFolder)}");
            }

            var task = Task.Run(() => LoadPrefabInfos(stream));
            yield return new WaitUntil(() => task.IsCompleted);
        }

        public TileGridObjectLayerData2(MapLayerDataHeader header, MapLayerLODConfig config, Map map,
            Rect realLayerBounds, KeepScaleConfig[] scaleConfig, bool asyncLoading, bool enableObjectMaterialChange) : base(
            header, config, map)
        {
            mLastViewport = new Rect(-10000, -10000, 0, 0);
            mRealLayerBounds = realLayerBounds;
            mScaleUpdater = new KeepScaleUpdater(scaleConfig);
            mEnableObjectMaterialTypeChange = enableObjectMaterialChange;

#if UNITY_EDITOR
            var w = new StopWatchWrapper();
            w.Start();
#endif
            string dataPath = MapCoreDef.GetMapBigTileDataPath(map.dataFolder);
            if (MapModuleResourceMgr.Exists(dataPath))
            {
                LoadAllTiles(dataPath, asyncLoading);
            }
            else
            {
                InitLayer();

                int idx = 0;
                for (int i = 0; i < header.rows; ++i)
                {
                    for (int j = 0; j < header.cols; ++j)
                    {
                        mBigTileDatas[idx] = LoadTile(j, i);

                        ++idx;
                    }
                }
            }
#if UNITY_EDITOR
            var time = w.Stop();
            Debug.Log("load tile cost: " + time.ToString());
#endif

            mDontUpdateTileBigObjectCulling = new bool[mRows, mCols];

            LoadPrefabInfos();

#if UNITY_EDITOR && TFW_MAP_DEBUG
            mGridViewer =
 new GridViewer("Tile Grid Object Layer 2 Grids", 0, 0, header.cols, header.rows, header.tileWidth * header.cols, header.tileHeight * header.rows, header.tileWidth, header.tileHeight, new Color(0, 1, 0, 0.2f), new Color(1, 0, 0, 0.2f), true, 0.3f, map.root, false);
            mGridViewer.SetVisible(false);
#endif
        }

        #region hide

        //清理地图数据
        public override void OnDestroy()
        {
#if true
            var tmpTileObjList = mTileObjectDataListPool.Require();

            {
                tmpTileObjList.Clear();

                tmpTileObjList.AddRange(mObjects.Values);
                mObjects.Clear();
                mObjects = null;

                foreach (var tileData in tmpTileObjList)
                {
                    Map.currentMap.DestroyObject(tileData.GetEntityID());
                }
            }

            {
                tmpTileObjList.Clear();

                foreach (var tileChildren in mTileChildren.Values)
                {
                    tmpTileObjList.AddRange(tileChildren);
                    mTileObjectDataListPool.Release(tileChildren);
                }
                mTileChildren.Clear();
                mTileChildren = null;

                foreach (var tileData in tmpTileObjList)
                {
                    tileData.OnDestroy();
                    mPool.Release(tileData);
                }
            }

            mTileObjectDataListPool.Release(tmpTileObjList);
            mBigTileDatas = null;
            mPool.OnDestroy();
            mPool = null;
#else
            foreach (var obj in mObjects)
            {
                map.DestroyObject(obj.Value.GetEntityID());
            }

            mObjects = null;

            foreach (var p in mTileChildren)
            {
                var list = p.Value;
                for (int i = 0; i < list.Count; ++i)
                {
                    mPool.Release(list[i]);
                }
            }

            mTileChildren = null;

            mPool.OnDestroy();
            mPool = null;
#endif
        }

        //get一个地图对象的数据
        public TileObjectData2 GetObjectData(int objectID)
        {
            TileObjectData2 val;
            mObjects.TryGetValue(objectID, out val);
            return val;
        }
#endregion
        public void SetObjectActiveOnly(TileObjectData2 data, bool active, int lod)
        {
            bool change = SetObjectActiveFromAction(data, active, lod);

            if (change)
            {
                if (active)
                {
                    mVisibleTileChildren[data.GetEntityID()] = data;
                }
                else
                {
                    mVisibleTileChildren.Remove(data.GetEntityID());
                }
            }
        }

        public void SetObjectScaleChangeCallback(System.Action<TileObjectData2> onObjectScaleChangeCallback)
        {
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
        }

        public void SetObjectActiveStateChangeCallback(System.Action<TileObjectData2> onObjectActiveStateChangeCallback)
        {
            mOnActiveStateChangeCallback = onObjectActiveStateChangeCallback;
        }

        public bool SetObjectActiveFromAction(TileObjectData2 objectData, bool active, int lod)
        {
            bool changed = objectData.SetObjActive(active);
            if (changed)
            {
                mOnActiveStateChangeCallback(objectData);
            }

            return changed;
        }

        //设置big tile的可见性
        public void SetObjectActiveTop(FrameAction parentAction, bool active, bool changeLODInCurrentFrame, int x, int y)
        {
            int lastLOD = mCurrentLOD;
            if (mIsLODChanged)
            {
                lastLOD = mLastLOD;
            }

            if (active)
            {
                var addAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, true,
                    FrameActionType.ShowFrontTile2, false, changeLODInCurrentFrame, x, y);
                parentAction.AddChildAction(addAction);

#region hide

                //将立即执行的action独立成一个单独的action
                if (mIsLODChanged)
                {
                    if (changeLODInCurrentFrame)
                    {
                        var addNowAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, true,
                            FrameActionType.ShowFrontTile2, true, true, x, y);
                        while (true)
                        {
                            bool finished = addNowAction.Do();
                            if (finished)
                            {
                                addNowAction.OnDestroy();
                                break;
                            }
                        }
                    }
                }

#endregion
            }
            else
            {
                var action = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, false,
                    FrameActionType.HideFrontTile2, false, changeLODInCurrentFrame, x, y);
                parentAction.AddChildAction(action);

#region hide

                //将立即执行的action独立成一个单独的action
                if (mIsLODChanged)
                {
                    if (changeLODInCurrentFrame)
                    {
                        var removeNowAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD,
                            false, FrameActionType.HideFrontTile2, true, true, x, y);
                        while (true)
                        {
                            bool finished = removeNowAction.Do();
                            if (finished)
                            {
                                removeNowAction.OnDestroy();
                                break;
                            }
                        }
                    }
                }

#endregion
            }
        }

        //给frame action使用
        //instant: 是否立即在当前帧切换lod
        public void SetObjectActiveFromAction(FrameAction parentAction, int oldLOD, int newLOD, bool active,
            bool instant, bool hasInstantAction, int x, int y)
        {
            int idx = y * mCols + x;

            if (active)
            {
                AddChildren(parentAction, newLOD, instant, hasInstantAction, x, y, mBigTileDatas[idx]);
            }
            else
            {
                RemoveChildren(parentAction, oldLOD, instant, hasInstantAction, x, y);
            }
        }

#region hide

        int GetCurrentLOD()
        {
            if (mCurrentLOD < 0)
            {
                return 0;
            }

            return mCurrentLOD;
        }

        /*
         *  2 3
         *  0 1
         */
        int GetSubRegionIndex(float x, float z)
        {
            float centerX = tileWidth * 0.5f;
            float centerZ = tileHeight * 0.5f;
            if (x < centerX && z < centerZ)
            {
                return 0;
            }

            if (x >= centerX && z < centerZ)
            {
                return 1;
            }

            if (x < centerX && z >= centerZ)
            {
                return 2;
            }

            if (x >= centerX && z >= centerZ)
            {
                return 3;
            }
#if UNITY_EDITOR
            Debug.Log("invalid subregion");
#endif
            return 0;
        }

        //返回lodlevel下,第tileIndex个tile下的第index个物体的id
        public int GetObjectID(int lodLevel, ComplexGridBigTileData2 tileData, int tileIndex, int index)
        {
            if (lodLevel < 0)
            {
                lodLevel = 0;
            }

            List<List<int>> lodIDs = mTileObjectIDs[tileIndex];
            if (lodIDs == null)
            {
                int nLODs = tileData.objectsOfEachLOD.Count;
                lodIDs = new List<List<int>>(nLODs);
                mTileObjectIDs[tileIndex] = lodIDs;

                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabs = tileData.objectsOfEachLOD[lod];
                    int n = childPrefabs.Count;
                    List<int> ids = new List<int>(n);
                    lodIDs.Add(ids);
                    for (int i = 0; i < n; ++i)
                    {
                        ids.Add(map.nextCustomObjectID);
                    }
                }
            }

            if (lodLevel >= lodIDs.Count || index >= lodIDs[lodLevel].Count)
            {
                return 0;
            }

            return lodIDs[lodLevel][index];
        }

        public Vector2Int GetObjectCoordinate(TileObjectData2 data)
        {
            var pos = data.GetPosition();
            return FromWorldPositionToCoordinate(pos);
        }

        public bool IsInViewRange(TileObjectData2 data, Rect viewport)
        {
            var coord = GetObjectCoordinate(data);
            var rect = GetViewRect(viewport);
            return rect.Contains(new Vector2Int(coord.x, coord.y));
        }

        //地图对象是否在视野中
        public bool IsInViewRange(TileObjectData2 data)
        {
            return IsInViewRange(data, map.viewport);
        }

        public override void RefreshObjectsInViewport()
        {
            Debug.Assert(false, "can't be here");
        }

        public RectInt GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new RectInt(startCoord.x, startCoord.y, endCoord.x - startCoord.x, endCoord.y - startCoord.y);
        }

#endregion

        public override bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (map.enableLOD)
            {
                lodChanged = base.SetZoom(zoom);
            }

            return lodChanged;
        }

        public bool SetZoomFromAction(FrameAction parentAction, float oldZoom, Rect oldViewport, float newZoom,
            Rect newViewport)
        {
            bool lodChanged = base.SetZoom(newZoom);

            if (lodChanged)
            {
                var oldRect = GetViewRect(oldViewport);
                var oldMin = oldRect.min;
                var oldMax = oldRect.max;
                var oldMinX = oldMin.x;
                var oldMinY = oldMin.y;
                var oldMaxX = oldMax.x;
                var oldMaxY = oldMax.y;

                //隐藏视野内的旧lod的big tile
                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                        {
                            int idx = i * mCols + j;
                            if (mBigTileDatas[idx] != null)
                            {
                                mIsLODChanged = true;
                                SetObjectActiveTop(parentAction, active: false, changeLODInCurrentFrame: true, x: j,
                                    y: i);
                                mIsLODChanged = false;
                            }
                        }
                    }
                }

                var newRect = GetViewRect(newViewport);
                var newMin = newRect.min;
                var newMax = newRect.max;
                var newMinX = newMin.x;
                var newMinY = newMin.y;
                var newMaxX = newMax.x;
                var newMaxY = newMax.y;
                //显示视野内的新lod的big tile
                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                        {
                            int idx = i * mCols + j;
                            if (mBigTileDatas[idx] != null)
                            {
                                mIsLODChanged = true;
                                SetObjectActiveTop(parentAction, active: true, changeLODInCurrentFrame: true, x: j,
                                    y: i);
                                mIsLODChanged = false;
                            }
                        }
                    }
                }
            }

            //if (InstantActionCounter.addCount != 0 || InstantActionCounter.removeCount != 0)
            //{
            //    InstantActionCounter.Log();
            //}
            mLastCameraZoom = newZoom;

            return lodChanged;
        }

        void RemoveChildren(FrameAction parentAction, int currentLOD, bool instant, bool hasInstantAction, int tileX,
            int tileY)
        {
            var tileIndex = tileY * mCols + tileX;

            if (mTileChildren != null)
            {
                //取消删除任务
                List<TileObjectData2> objectsInThisTile;
                bool found = mTileChildren.TryGetValue(tileIndex, out objectsInThisTile);
                if (found)
                {
                    int n = objectsInThisTile.Count;
                    for (int i = 0; i < n; ++i)
                    {
                        if (!mRemovedViewIDs.Contains(objectsInThisTile[i].viewID))
                        {
                            if (instant)
                            {
                                if (MapCoreDef.IsInstantSwitchObjectType(objectsInThisTile[i].objectType) &&
                                    objectsInThisTile[i].lod == currentLOD)
                                {
                                    var action = FrameActionRemoveTileObject2.Require(null, this, objectsInThisTile[i]);
                                    mInstantActions.Add(action);
                                }
                            }
                            else
                            {
                                if (!hasInstantAction || !MapCoreDef.IsInstantSwitchObjectType(objectsInThisTile[i].objectType))
                                {
                                    if (objectsInThisTile[i].lod == currentLOD)
                                    {
                                        FrameActionRemoveTileObject2.Require(parentAction, this, objectsInThisTile[i]);
                                    }
                                }
                            }
                        }
                    }

                    for (int i = 0; i < mInstantActions.Count; ++i)
                    {
                        //++InstantActionCounter.removeCount;
                        mInstantActions[i].Do();
                        mInstantActions[i].OnDestroy();
                    }

                    mInstantActions.Clear();
                }
            }

            //删除这个tile的update action
            if (instant == false)
            {
                var key = FrameActionUpdateTileObjects2.MakeActionKey(tileIndex, currentLOD,
                    FrameActionType.UpdateTileObjects2);
                bool removed = map.RemoveFrameAction(mUpdateActionQueueIndex, key);
            }
        }

        //管理Tile的子节点
        void AddChildren(FrameAction parentAction, int newLOD, bool instantAction, bool hasInstantAction, int tileX,
            int tileY, ComplexGridBigTileData2 bigTileData)
        {
            var childrenModelTemplates = bigTileData.objectsOfEachLOD[newLOD];
            int n = childrenModelTemplates.Count;

            int currentQuality = QualityControl.GetCurrentQuality();

            var tilePos = FromCoordinateToWorldPosition(tileX, tileY);
            int tileIndex = tileY * mCols + tileX;
            for (int i = 0; i < n; ++i)
            {
                var objectID = GetObjectID(newLOD, bigTileData, tileIndex, i);
                //必须是没有加载的物体才加载
                if (mActiveTileChildren.ContainsKey(objectID) == false)
                {
                    var template = childrenModelTemplates[i];
                    if (!mRemovedViewIDs.Contains(template.viewID))
                    {
                        var prefabInfo = mPrefabInfos[template.prefabInitInfoIndex];
                        //检查quality
                        if (prefabInfo.maxVisibleQuality >= currentQuality)
                        {
                            var action = FrameActionAddTileObject2.Require(this, tilePos, template, objectID, i, newLOD, mOnObjectScaleChangeCallback);
                            if (instantAction)
                            {
                                if (MapCoreDef.IsInstantSwitchObjectType(template.objectType))
                                {
                                    //++InstantActionCounter.addCount;
                                    //如果这个tile object和上一个lod的tile object是相同位置并且是标记成了立即切换的object的,则直接显示,避免闪烁问题
                                    action.Do();
                                    action.OnDestroy();
                                }
                            }
                            else
                            {
                                if (!hasInstantAction || !MapCoreDef.IsInstantSwitchObjectType(template.objectType))
                                {
                                    //在后续帧切换lod
                                    mSortedActions.Add(action);
                                }
                            }
                        }
                    }
                }
            }

            //temp code
            //if (instantCount != 0)
            //{
            //    Debug.Log("instant action count: " + instantCount);
            //}
            parentAction.AddChildActions(mSortedActions);
            mSortedActions.Clear();

            if (instantAction == false && n > 0)
            {
                //增加更新新object的命令
                if (!mDontUpdateTileBigObjectCulling[tileY, tileX])
                {
                    var updateAction = FrameActionUpdateTileObjects2.Require(this, bigTileData, newLOD, tileX, tileY);
                    map.AddFrameAction(mUpdateActionQueueIndex, updateAction, false);
                }
            }
        }

        public void UpdateCulling()
        {
            if (mInited)
            {
                mTileObjectCullManager.UpdateViewport(map.viewport);
            }
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            if (mInited == false)
            {
                return false;
            }

            var key = FrameActionUpdateFrontLayerViewport2.MakeActionKey(id, mActionTimeStamp,
                FrameActionType.UpdateFrontLayerViewport2);
            bool removed = map.RemoveFrameActionsOfType(mActionQueueIndex, FrameActionType.UpdateFrontLayerViewport2);

            ++mActionTimeStamp;
            if (mActionTimeStamp > 10000)
            {
                mActionTimeStamp = 10000;
            }

            bool zoomChanged = !Mathf.Approximately(newZoom, mLastCameraZoom);
            bool viewportChanged = (newViewport != mLastViewport);
            if (zoomChanged || viewportChanged)
            {
                var action = FrameActionUpdateFrontLayerViewport2.Require(this, mActionTimeStamp, mLastViewport,
                    mLastCameraZoom, newViewport, newZoom);
                map.AddFrameAction(mActionQueueIndex, action, false);
            }

            return false;
        }

        public void UpdateViewportFromAction(FrameAction parentAction, bool lodChanged, Rect oldViewport,
            Rect newViewport)
        {
            if (!lodChanged)
            {
                var oldViewRect = GetViewRect(oldViewport);
                var newViewRect = GetViewRect(newViewport);

                UpdateViewRect(parentAction, oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;
        }

        bool UpdateViewRect(FrameAction parentAction, RectInt oldViewRect, RectInt newViewRect)
        {
            if (!oldViewRect.Equals(newViewRect))
            {
                var oldMin = oldViewRect.min;
                var oldMax = oldViewRect.max;
                var newMin = newViewRect.min;
                var newMax = newViewRect.max;
                int oldMinX = oldMin.x;
                int oldMinY = oldMin.y;
                int oldMaxX = oldMax.x;
                int oldMaxY = oldMax.y;
                int newMinX = newMin.x;
                int newMinY = newMin.y;
                int newMaxX = newMax.x;
                int newMaxY = newMax.y;

                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        if (!(i >= newMinY && i <= newMaxY &&
                              j >= newMinX && j <= newMaxX))
                        {
                            //物体不在新的视野中,隐藏它
                            if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                            {
                                int idx = i * mCols + j;
                                if (mBigTileDatas[idx] != null)
                                {
                                    SetObjectActiveTop(parentAction, active: false, changeLODInCurrentFrame: false, j,
                                        i);
                                }
                            }
                        }
                    }
                }

                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        if (!(i >= oldMinY && i <= oldMaxY &&
                              j >= oldMinX && j <= oldMaxX))
                        {
                            //物体不在旧的视野中,显示它
                            if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                            {
                                int idx = i * mCols + j;
                                if (mBigTileDatas[idx] != null)
                                {
                                    SetObjectActiveTop(parentAction, active: true, changeLODInCurrentFrame: false, j,
                                        i);
                                }
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public void GetObjectsInBounds(Bounds bounds, List<ComplexGridBigTileData2> objects)
        {
            var min = bounds.min;
            var max = bounds.max;
            var minCoord = FromWorldPositionToCoordinate(min);
            var maxCoord = FromWorldPositionToCoordinate(max);

            for (int y = minCoord.y; y <= maxCoord.y; ++y)
            {
                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                {
                    var tileData = GetBigTileData(x, y);
                    if (tileData != null)
                    {
                        objects.Add(tileData);
                    }
                }
            }
        }

#region show npc

        public void UpdateObjectScaleAtHeight()
        {
            if (mHasScaleObject)
            {
                float scaleFactor = mScaleUpdater.UpdateObjectScaleAtHeight();
                if (scaleFactor != 0)
                {
                    mScaleFactorAtCameraHeight = scaleFactor;
                    foreach (var obj in mVisibleTileChildren)
                    {
                        var tileObject = obj.Value;
                        if (tileObject.objectType == TileObjectType.ScaleDecorationObject)
                        {
                            tileObject.SetScale(mScaleFactorAtCameraHeight * obj.Value.baseScale);
                        }
                    }
                }
            }
        }

        public override bool isGameLayer => true;

        public void OnShowNPC(long npcID, Vector3 center, float radius)
        {
            if (mInited)
            {
                //temp code, todo, 地图lod确定后优化,在某个lod之上就不判断npc与装饰物是否重叠了
                var action = FrameActionCheckDecorationObjectOverlap2.Require(this, npcID, center, radius);
                map.AddFrameAction(mOverlapActionQueueIndex, action, false);

#if USE_CULL
            //temp code, change too pool later
            mNPCs.Add(npcID, new NPCInfo(center.x, center.z, radius));
#endif
            }
            else
            {
                Debug.LogError("layer not inited!");
            }
        }

        //释放npcID占领的坑位
        public void OnHideNPC(long npcID)
        {
            if (mInited)
            {
#if USE_CULL
            mNPCs.Remove(npcID);
#else
                List<int> takenIDs;
                mNPCTakenIDs.TryGetValue(npcID, out takenIDs);
                if (takenIDs != null)
                {
                    for (int i = 0; i < takenIDs.Count; ++i)
                    {
                        RemoveInvaderID(takenIDs[i], npcID);
                    }

                    takenIDs.Clear();
                    mIDPool.Release(takenIDs);
                    mNPCTakenIDs.Remove(npcID);
                }
#endif
            }
            else
            {
                Debug.LogError("layer not inited!");
            }
        }

#if !USE_CULL
        void RemoveInvaderID(int tileObjectID, long npcID)
        {
            List<long> invaders;
            mTileObjectsInvaderIDs.TryGetValue(tileObjectID, out invaders);
            if (invaders != null)
            {
                bool suc = invaders.Remove(npcID);
#if UNITY_EDITOR
                Debug.Assert(suc);
#endif
            }
        }
#endif

        //删除坐标在position的物体,物体的tag必须是removable
        public void RemoveRemovableObject(Vector3 position)
        {
            mRemovableObjects.TryGetValue(position, out RemovableObjectData obj);
            if (obj != null)
            {
                int nLODs = obj.lods.Count;
                for (int i = 0; i < nLODs; ++i)
                {
                    var lod = obj.lods[i];
                    if (mRemovedViewIDs.Contains(lod.viewID))
                    {
                        return;
                    }
                    foreach (var tileObjectID in lod.tileObjectIDs)
                    {
                        HideObject(tileObjectID);
                    }
                    
                    mRemovedViewIDs.Add(lod.viewID);
                }
            }
        }

        public void AddRemovableObject(Vector3 position)
        {
            mRemovableObjects.TryGetValue(position, out RemovableObjectData obj);
            if (obj != null)
            {
                int nLODs = obj.lods.Count;
                for (int i = 0; i < nLODs; ++i)
                {
                    var lod = obj.lods[i];
                    if (!mRemovedViewIDs.Contains(lod.viewID))
                    {
                        return;
                    }
                    if (i == currentLOD)
                    {
                        //只显示当前lod的物体
                        foreach (var tileObjectID in lod.tileObjectIDs)
                        {
                            ShowObject(tileObjectID);
                        }
                    }

                    mRemovedViewIDs.Remove(lod.viewID);
                }
            }
        }

#if USE_CULL
        public void HideDecorationObject(long npcID, Vector3 center, float radius)
        {
            mIntersectedTileObjects.Clear();
            mTileObjectCullManager.GetCirclesIntersectedWithObject(radius, center.x, center.z, mIntersectedTileObjects);
            int n = mIntersectedTileObjects.Count;
            for (int i = 0; i < n; ++i)
            {
                //隐藏目前可见的与npc重叠的装饰物
                HideVisibleObject((int)mIntersectedTileObjects[i]);
            }
        }
#else
        public void HideDecorationObject(long npcID, Vector3 center, float radius)
        {
            var rect = new Rect(center.x - radius, center.z - radius, radius * 2, radius * 2);
            var range = GetViewRect(rect);
            int minX = range.xMin;
            int minY = range.yMin;
            int maxX = range.xMax;
            int maxY = range.yMax;

            for (int y = minY; y <= maxY; ++y)
            {
                for (int x = minX; x <= maxX; ++x)
                {
                    if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                    {
                        var tileIndex = y * mCols + x;
                        if (mBigTileDatas[tileIndex] != null)
                        {
                            Vector3 tilePos = FromCoordinateToWorldPosition(x, y);
                            mIntersectedTileObjects.Clear();

                            var localCenter = center - tilePos;
                            Rect localBounds = new Rect(new Vector2(localCenter.x - radius, localCenter.z - radius),
                                new Vector2(radius * 2, radius * 2));
                            int nLODs = mBigTileDatas[tileIndex].objectsOfEachLOD.Count;
                            for (int lod = 0; lod < nLODs; ++lod)
                            {
                                var objectsInThisLOD = mBigTileDatas[tileIndex].objectsOfEachLOD[lod];
                                for (int k = 0; k < objectsInThisLOD.Count; ++k)
                                {
                                    //不检测big object
                                    if (!MapCoreDef.IsBigObject(objectsInThisLOD[k].objectType))
                                    {
                                        Rect localBoundsInPrefab = objectsInThisLOD[k].GetLocalBoundsInPrefab(mPrefabInfos);
                                        if (localBoundsInPrefab.Overlaps(localBounds))
                                        {
                                            mIntersectedTileObjects.Add(new TileObjectInfo2(lod, k, objectsInThisLOD[k].viewID));
                                        }
                                    }
                                }
                            }

                            foreach (var p in mIntersectedTileObjects)
                            {
                                var tileObjectID = GetObjectID(p.lod, mBigTileDatas[tileIndex], tileIndex, p.index);
                                TakeID(npcID, p.viewID);

                                //隐藏目前可见的与npc重叠的装饰物
                                HideDecorationObject(tileObjectID);
                            }
                        }
                    }
                }
            }
        }

        //tileObjectID对应的坑位是否被占领
        public bool IsIDTaken(int viewID, TileObjectType type)
        {
            if (type != TileObjectType.ScaleDecorationObject &&
                type != TileObjectType.NoneScaleDecorationObject)
            {
                return false;
            }

            List<long> invaderIDs;
            bool suc = mTileObjectsInvaderIDs.TryGetValue(viewID, out invaderIDs);
            if (!suc)
            {
                return false;
            }

            return invaderIDs.Count > 0;
        }

        //占领id对应的坑位
        public void TakeID(long npcID, int viewID)
        {
            List<int> takenIDs = null;
            mNPCTakenIDs.TryGetValue(npcID, out takenIDs);
            if (takenIDs == null)
            {
                takenIDs = mIDPool.Require();
                mNPCTakenIDs[npcID] = takenIDs;
            }

            takenIDs.Add(viewID);

            List<long> invaderIDs;
            mTileObjectsInvaderIDs.TryGetValue(viewID, out invaderIDs);
            if (invaderIDs == null)
            {
                invaderIDs = new List<long>();
                mTileObjectsInvaderIDs[viewID] = invaderIDs;
            }

            invaderIDs.Add(npcID);
        }
#endif

        //隐藏装饰物
        void HideDecorationObject(int tileObjectID)
        {
            var tileObjectData = map.FindObject(tileObjectID) as TileObjectData2;
            if (tileObjectData != null)
            {
                if (tileObjectData.objectType == TileObjectType.NoneScaleDecorationObject ||
                    tileObjectData.objectType == TileObjectType.ScaleDecorationObject)
                {
                    tileObjectData.collidesWithNPC = true;
                    SetObjectActiveOnly(tileObjectData, false, currentLOD);
                }
            }
        }

        void HideObject(int tileObjectID)
        {
            var tileObjectData = map.FindObject(tileObjectID) as TileObjectData2;
            if (tileObjectData != null)
            {
                SetObjectActiveOnly(tileObjectData, false, currentLOD);
                if (mTileObjectCullManager.IsCullObjectRegistered(tileObjectData.cullObjectID))
                {
                    mTileObjectCullManager.UnregisterCullObject(tileObjectData.cullObjectID);
                }
            }
        }

        void ShowObject(int tileObjectID)
        {
            var tileObjectData = map.FindObject(tileObjectID) as TileObjectData2;
            if (tileObjectData != null)
            {
                SetObjectActiveOnly(tileObjectData, true, currentLOD);
                if (!mTileObjectCullManager.IsCullObjectRegistered(tileObjectData.cullObjectID))
                {
                    RegisterObjectToCullManager(tileObjectData);
                }
            }
        }

        void RegisterObjectToCullManager(TileObjectData2 obj)
        {
            var pos = obj.GetPosition();
            bool isVisible;
            var bounds = obj.GetBounds();
            float width = bounds.width;
            float height = bounds.height;
            float x = bounds.x;
            float z = bounds.y;
#if USE_CULL
                mTileObjectCullManager.RegisterCircleWithCustomID(obj.id, radius, x + width * 0.5f, z + height * 0.5f, OnTileObjectVisibilityChange, obj, out isVisible);
#else
            obj.cullObjectID = mTileObjectCullManager.RegisterRectangle(width * 0.5f, height * 0.5f,
                x + width * 0.5f, z + height * 0.5f, mOnTileObjectVisibilityChange, obj, out isVisible);
#endif
        }

        public void AddTileObject(TileObjectData2 obj)
        {
            List<TileObjectData2> objectsInThisTile;

            if (!mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile))
            {
                mTileChildren[obj.tileIndex] = objectsInThisTile = mTileObjectDataListPool.Require();
            }
#if UNITY_EDITOR
            if (objectsInThisTile.Contains(obj))
            {
                Debug.Assert(false);
            }

#endif
            objectsInThisTile.Add(obj);
            mActiveTileChildren.Add(obj.id, obj);

            //现在只管理小件物体的视野,太大的物体例如山不适合用这种方式管理
            if (obj.useCullManager)
            {
                RegisterObjectToCullManager(obj);
            }
        }

        public void RemoveTileObject(TileObjectData2 obj)
        {
            List<TileObjectData2> objectsInThisTile;
            mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile);
            bool suc = objectsInThisTile != null && objectsInThisTile.Remove(obj);
#if UNITY_EDITOR
            Debug.Assert(suc);
#endif
            bool removeSuc = mActiveTileChildren.Remove(obj.id);
#if UNITY_EDITOR
            Debug.Assert(removeSuc);
#endif
            if (obj.useCullManager)
            {
#if USE_CULL
                suc = mTileObjectCullManager.UnregisterCullObject(obj.id);
#else
                suc = mTileObjectCullManager.UnregisterCullObject(obj.cullObjectID);
#endif
#if UNITY_EDITOR
                Debug.Assert(suc);
#endif
            }

            // 如果TileObject列表已为空，则将其回收到对象池
            if (objectsInThisTile != null && objectsInThisTile.Count == 0)
            {
                mTileChildren.Remove(obj.tileIndex);
                mTileObjectDataListPool.Release(objectsInThisTile);
            }
        }

        void OnTileObjectVisibilityChange(long id, object tileObj, bool visible)
        {
            var tileObject = tileObj as TileObjectData2;
#if USE_CULL
            if (!tileObject.collidesWithNPC)
#else
            bool isTaken = IsIDTaken(tileObject.viewID, tileObject.objectType);
            if (isTaken == false)
#endif
            {
                bool isActive = tileObject.IsObjActive();
                if (visible != isActive)
                {
                    //隐藏视野外的物体,显示视野内的物体
                    SetObjectActiveOnly(tileObject, visible, tileObject.lod);

                    if (visible && isActive == false)
                    {
                        //第一次显示,设置装饰物的初始scale
                        if (tileObject.objectType == TileObjectType.ScaleDecorationObject)
                        {
                            tileObject.SetScale(objectScale * tileObject.baseScale);
                        }
                    }
                }
            }
            else
            {
                SetObjectActiveOnly(tileObject, false, tileObject.lod);
            }
        }

        public bool HasTileObject(int id)
        {
            return mActiveTileChildren.ContainsKey(id);
        }

        public TileObjectData2 GetTileObject(int id)
        {
            TileObjectData2 obj;
            mActiveTileChildren.TryGetValue(id, out obj);
            return obj;
        }

        public override bool Contains(int objectID)
        {
            Debug.Assert(false);
            return true;
            //return mObjects.ContainsKey(objectID);
        }

        //x,y对应的地块是否是视野边界地块
        public bool IsBoundsTile(int x, int y)
        {
            var viewRect = GetViewRect(map.viewport);
            int xMin = viewRect.x;
            int xMax = xMin + viewRect.width;
            int yMin = viewRect.y;
            int yMax = yMin + viewRect.height;
            if (x > xMin && x < xMax &&
                y > yMin && y < yMax)
            {
                return false;
            }

            return true;
        }

        private void LoadPrefabInfos(Stream stream)
        {
            if (stream == null)
            {
                Debug.LogError("Null stream");
                return;
            }

            using var reader = new BinaryReader(stream);
            var version = Utils.ReadVersion(reader);
            var count = reader.ReadInt32();
            mPrefabInfos = new List<PrefabInitInfo2>(count);
            for (var i = 0; i < count; ++i)
            {
                List<string> prefabPathsForAllType = null;
                if (version.minorVersion >= 4)
                {
                    prefabPathsForAllType = Utils.ReadStringList(reader);
                }
                else
                {
                    string prefabPath = Utils.ReadString(reader);
                    prefabPathsForAllType = new List<string>() { prefabPath };
                }
                var boundsMinX = reader.ReadSingle();
                var boundsMinZ = reader.ReadSingle();
                var boundsWidth = reader.ReadSingle();
                var boundsHeight = reader.ReadSingle();
                var rotation = Utils.ReadQuaternion(reader);
                var scale = Utils.ReadVector3(reader);
                var height = reader.ReadSingle();
                mPrefabInfos.Add(new PrefabInitInfo2(prefabPathsForAllType, boundsMinX, boundsMinZ, boundsWidth, boundsHeight,
                    rotation, scale, height));
            }

            if (version.minorVersion >= 2)
            {
                for (var i = 0; i < count; ++i)
                {
                    mPrefabInfos[i].maxVisibleQuality = reader.ReadInt16();
                }
            }

            if (version.minorVersion >= 3)
            {
                for (var i = 0; i < mRows; ++i)
                {
                    for (var j = 0; j < mCols; ++j)
                    {
                        mDontUpdateTileBigObjectCulling[i, j] = reader.ReadBoolean();
                    }
                }
            }

            reader.Close();
        }

        void LoadPrefabInfos()
        {
            var path = MapCoreDef.GetGridModelLayerHeaderFilePath(map.dataFolder);
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            LoadPrefabInfos(stream);
        }

        void LoadRemovableObjects(Stream stream)
        {
            if (stream == null)
            {
                return;
            }

            using var reader = new BinaryReader(stream);
            var version = Utils.ReadVersion(reader);

            Utils.ReadIntArray(reader);

            var count = reader.ReadInt32();
            mRemovableObjects = new Dictionary<Vector3, RemovableObjectData>(count);
            for (var i = 0; i < count; ++i)
            {
                //object在每个lod的坐标都必须相同
                var pos = Utils.ReadVector3(reader);
                int nLODs = reader.ReadInt32();
                var objData = new RemovableObjectData();
                objData.lods = new List<RemovableLODObjectData>(nLODs);
                for (int k = 0; k < nLODs; ++k)
                {
                    int lod = reader.ReadInt32();
                    int viewID = reader.ReadInt32();
                    int nObjectsForThisViewID = reader.ReadInt32();
                    var lodData = new RemovableLODObjectData(viewID);
                    objData.lods.Add(lodData);

                    for (int j = 0; j < nObjectsForThisViewID; ++j)
                    {
                        int bigTileIndex = reader.ReadInt32();
                        int objectIndex = reader.ReadInt32();
                        int tileObjectID = GetObjectID(lod, mBigTileDatas[bigTileIndex], bigTileIndex, objectIndex);
                        lodData.tileObjectIDs.Add(tileObjectID);
                    }
                }

#if UNITY_EDITOR
                Debug.Assert(mRemovableObjects.ContainsKey(pos) == false);
#endif
                mRemovableObjects[pos] = objData;
            }

            reader.Close();
        }

        void LoadRemovableObjects()
        {
            if (mRemovableObjectsStream != null)
            {
                LoadRemovableObjects(mRemovableObjectsStream);
                mRemovableObjectsStream = null;
            }
        }

        void InitLayer()
        {
            mTileObjectIDs = new List<List<int>>[mRows * mCols];
            mBigTileDatas = new ComplexGridBigTileData2[mRows * mCols];

            //在地图很高的时候看不到npc了,所以格子小点可能好些
            mTileObjectCullManager.Init(map, map.origin + new Vector3(mRealLayerBounds.x, 0, mRealLayerBounds.y),
                "tile object layer 2 cull manager", MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE,
                mRealLayerBounds.width, mRealLayerBounds.height, 2, false, false);

            mActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_FRONT_TILE_QUEUE);
            mUpdateActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_UPDATE_TILE_OBJECT_QUEUE);
            mOverlapActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_FRONT_TILE_OVERLAPE_CHECK_QUEUE);
            mOnTileObjectVisibilityChange = OnTileObjectVisibilityChange;

            var updateAction = FrameActionUpdateTileObjectCull2.Require(this);
            map.AddFrameAction(mUpdateActionQueueIndex, updateAction, false);
        }

        void LoadTileTask(Stream stream)
        {
            InitLayer();

            using BinaryReader reader = new BinaryReader(stream);

            int majorVersion = reader.ReadInt32();
            int minorVersion = reader.ReadInt32();

            int tileCount = reader.ReadInt32();

            for (int i = 0; i < tileCount; ++i)
            {
                int tileX = reader.ReadInt32();
                int tileY = reader.ReadInt32();

                int idx = tileY * mCols + tileX;
                mBigTileDatas[idx] = new ComplexGridBigTileData2();

                int nLODs = reader.ReadInt32();
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    int nObjectsInThisLOD = reader.ReadInt32();
                    var objects = new List<BigTileChildPrefabData2>(nObjectsInThisLOD);
                    mBigTileDatas[idx].objectsOfEachLOD.Add(objects);
                    for (int k = 0; k < nObjectsInThisLOD; ++k)
                    {
                        BigTileChildPrefabData2 childPrefabData = new BigTileChildPrefabData2();
                        childPrefabData.x = reader.ReadSingle();
                        childPrefabData.z = reader.ReadSingle();
                        childPrefabData.prefabInitInfoIndex = reader.ReadInt16();
                        childPrefabData.objectType = (TileObjectType) reader.ReadByte();
                        childPrefabData.viewID = reader.ReadInt32();
                        objects.Add(childPrefabData);
                    }
                }
            }

            if (minorVersion >= 2)
            {
                //load special areas
                int specialAreaCount = reader.ReadInt32();
                for (int i = 0; i < specialAreaCount; ++i)
                {
                    SpecialArea area = new SpecialArea();
                    area.id = reader.ReadInt32();
                    area.width = reader.ReadSingle();
                    area.height = reader.ReadSingle();
                    area.shape = (SpecialAreaShape)reader.ReadInt32();
                    area.center = Utils.ReadVector3(reader);
                    mSpecialAreas[area.id] = area;
                    int objectCount = reader.ReadInt32();
                    for (int k = 0; k < objectCount; ++k)
                    {
                        int viewID = reader.ReadInt32();
                        mViewIDToSpecialArea.Add(viewID, area);
                    }
                }
            }

            mInited = true;
            reader.Close();

            LoadRemovableObjects();
        }

        void DoLoadAllTiles(Stream stream, bool async)
        {
            if (stream != null)
            {
                if (async)
                {
                    LoadingTask task = new LoadingTask(() => { LoadTileTask(stream); });

                    LoadingTaskManager.AddTask(task);
                }
                else
                {
                    LoadTileTask(stream);
                }
            }
        }

        void LoadAllTiles(string dataPath, bool asyncLoading)
        {
            var path = MapCoreDef.IsNotCreateFolder(map.dataFolder) ? MapCoreDef.GetRemovableObjectsFilePath(map.dataFolder) : MapCoreDef.GetRemovableBorderObjectsFilePath(map.dataFolder);


            if (asyncLoading)
            {
                MapModuleResourceMgr.LoadTextStreamAsync(path, (str1, stream1) =>
                {
                    mRemovableObjectsStream = stream1;

                    MapModuleResourceMgr.LoadTextStreamAsync(dataPath,
                 (str, stream) => { DoLoadAllTiles(stream, asyncLoading); });
                });
            }
            else
            {
                mRemovableObjectsStream = MapModuleResourceMgr.LoadTextStream(path, true);

                var stream = MapModuleResourceMgr.LoadTextStream(dataPath, true);
                DoLoadAllTiles(stream, asyncLoading);
            }
        }

        ComplexGridBigTileData2 LoadTile(int x, int y)
        {
            string dataPath = MapCoreDef.GetBigTileDataPath(map.dataFolder, x, y);
#if UNITY_EDITOR
            //Debug.Log($"Load Tile: {x}_{y}");
#endif
            ComplexGridBigTileData2 tileData = null;

            var stream = MapModuleResourceMgr.LoadTextStream(dataPath, true);
            if (stream != null)
            {
                tileData = new ComplexGridBigTileData2();
                using BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                int nLODs = reader.ReadInt32();
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    int nObjectsInThisLOD = reader.ReadInt32();
                    var objects = new List<BigTileChildPrefabData2>(nObjectsInThisLOD);
                    tileData.objectsOfEachLOD.Add(objects);
                    for (int k = 0; k < nObjectsInThisLOD; ++k)
                    {
                        BigTileChildPrefabData2 childPrefabData = new BigTileChildPrefabData2();
                        childPrefabData.x = reader.ReadSingle();
                        childPrefabData.z = reader.ReadSingle();
                        childPrefabData.prefabInitInfoIndex = reader.ReadInt16();
                        /*childPrefabData.pathIndexInStringTable = */
                        reader.ReadInt16();
                        childPrefabData.objectType = (TileObjectType) reader.ReadByte();
                        childPrefabData.viewID = reader.ReadInt32();
                        objects[k] = childPrefabData;
                    }
                }

                reader.Close();
            }

            return tileData;
        }

        public ComplexGridBigTileData2 GetBigTileData(int x, int y)
        {
            return mBigTileDatas[y * horizontalTileCount + x];
        }

        public override Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return new Vector3(x * mTileWidth + mLayerOrigin.x + mRealLayerBounds.x, 0,
                y * mTileHeight + mLayerOrigin.z + mRealLayerBounds.y);
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return new Vector3((x + 0.5f) * mTileWidth + mLayerOrigin.x + mRealLayerBounds.x, 0,
                (y + 0.5f) * mTileHeight + mLayerOrigin.z + mRealLayerBounds.y);
        }

        public override Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            int x = Mathf.FloorToInt((position.x - mLayerOrigin.x - mRealLayerBounds.x) / tileWidth);
            int y = Mathf.FloorToInt((position.z - mLayerOrigin.z - mRealLayerBounds.y) / tileHeight);
            return new Vector2Int(x, y);
        }

        public bool IsObjectRemoved(int viewID)
        {
            return mRemovedViewIDs.Contains(viewID);
        }

        public bool HasDynamicObject(int dynamicObjectID)
        {
            return mDynamicObjects.ContainsKey(dynamicObjectID);
        }

        //返回object id
        public int AddDynamicObject(List<string> prefabLODPaths, Vector3 position)
        {
            int dynamicObjectID = Map.currentMap.nextCustomObjectID;
            DynamicDecorationObject obj = new DynamicDecorationObject();
            mDynamicObjects[dynamicObjectID] = obj;

            int h = horizontalTileCount;
            int v = verticalTileCount;
            int nLODs = Mathf.Min(prefabLODPaths.Count, lodCount);
            for (int lod = 0; lod < nLODs; ++lod)
            {
                string prefabPath = prefabLODPaths[lod];
                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);
                Debug.Assert(prefab != null);
                var bounds = GameObjectBoundsCalculator.CalculateRect(prefab);
                PrefabInitInfo2 prefabInfo = GetDynamicPrefabInfo(prefabPath, prefab, bounds);
                if (prefabInfo == null)
                {
                    var transform = prefab.transform;
                    prefabInfo = new PrefabInitInfo2(new List<string>() { prefabPath }, bounds.min.x, bounds.min.y, bounds.width, bounds.height,
                        transform.rotation, transform.localScale, transform.position.y);
                    mPrefabInfos.Add(prefabInfo);
                    mDynamicPrefabInfos.Add(prefabInfo);
                }

                if (prefabInfo != null)
                {
                    float minX = position.x + prefabInfo.boundsMinX;
                    float minZ = position.z + prefabInfo.boundsMinZ;
                    float maxX = minX + prefabInfo.boundsWidth;
                    float maxZ = minZ + prefabInfo.boundsHeight;

                    var viewID = Map.currentMap.nextCustomObjectID;

                    int prefabIndex = mPrefabInfos.IndexOf(prefabInfo);
                    Debug.Assert(prefabIndex < short.MaxValue);

                    var tileObjectType = MapCoreDef.GetTileObjectType(prefab.tag);

                    var minCoord = FromWorldPositionToCoordinate(new Vector3(minX, 0, minZ));
                    var maxCoord = FromWorldPositionToCoordinate(new Vector3(maxX, 0, maxZ));
                    for (int i = minCoord.y; i <= maxCoord.y; ++i)
                    {
                        for (int j = minCoord.x; j <= maxCoord.x; ++j)
                        {
                            if (i >= 0 && i < v && j >= 0 && j < h)
                            {
                                int idx = i * mCols + j;
                                if (mBigTileDatas[idx] == null)
                                {
                                    mBigTileDatas[idx] = new ComplexGridBigTileData2();
                                    for (int k = 0; k < lodCount; ++k)
                                    {
                                        mBigTileDatas[idx].objectsOfEachLOD.Add(new List<BigTileChildPrefabData2>());
                                    }
                                }

                                var bigTilePos = FromCoordinateToWorldPosition(j, i);

                                var tileData = new BigTileChildPrefabData2();
                                tileData.x = position.x - bigTilePos.x;
                                tileData.z = position.z - bigTilePos.z;
                                tileData.viewID = viewID;
                                tileData.prefabInitInfoIndex = (short) prefabIndex;
                                tileData.objectType = tileObjectType;

                                mBigTileDatas[idx].objectsOfEachLOD[lod].Add(tileData);

                                int index = mBigTileDatas[idx].objectsOfEachLOD[lod].Count - 1;
                                int objectID = GetObjectID(lod, mBigTileDatas[idx], idx, index);
                                if (objectID == 0)
                                {
                                    //add object id
                                    objectID = Map.currentMap.nextCustomObjectID;
                                    mTileObjectIDs[idx][lod].Add(objectID);
                                }

                                obj.objectIDsInEachLOD.Add(objectID);

                                if (currentLOD == lod)
                                {
                                    var action = FrameActionAddTileObject2.Require(this, bigTilePos, tileData, objectID, index, lod, mOnObjectScaleChangeCallback);
                                    action.Do();
                                    action.OnDestroy();
                                }
                            }
                        }
                    }
                }
            }

            return dynamicObjectID;
        }

        PrefabInitInfo2 GetDynamicPrefabInfo(string prefabPath, GameObject prefab, Rect bounds)
        {
            for (int i = 0; i < mDynamicPrefabInfos.Count; ++i)
            {
                var prefabInfo = mDynamicPrefabInfos[i];
                if (prefabInfo.prefabPathForEachCustomType[0] == prefabPath)
                {
                    //注意,忽略了rotation和scale还有y的比较
                    if (Mathf.Approximately(prefabInfo.boundsMinX, bounds.xMin) &&
                        Mathf.Approximately(prefabInfo.boundsMinZ, bounds.yMin) &&
                        Mathf.Approximately(prefabInfo.boundsWidth, bounds.width) &&
                        Mathf.Approximately(prefabInfo.boundsHeight, bounds.height))
                    {
                        return prefabInfo;
                    }
                }
            }

            return null;
        }

#if USE_CULL
        public bool IsDecorationObjectVisible(float objCenterX, float objCenterZ, float objRadius)
        {
            foreach (var p in mNPCs)
            {
                var npcInfo = p.Value;
                if (Utils.IsCircleCircleOverlap(npcInfo.centerX, npcInfo.centerZ, npcInfo.radius, objCenterX, objCenterZ, objRadius))
                {
                    return false;
                }
            }
            return true;
        }
#endif

#endregion

        //动态缩放的物体在当前相机高度下的缩放值
        public float objectScale
        {
            get { return mScaleFactorAtCameraHeight; }
        }

        //是否有需要动态缩放的物体
        public bool hasScaleObject
        {
            set { mHasScaleObject = value; }
            get { return mHasScaleObject; }
        }

        public bool isLoading { set; get; }

        public int objectCount
        {
            get { return mObjects.Count; }
        }

        public List<PrefabInitInfo2> prefabInfos
        {
            get { return mPrefabInfos; }
        }

        public TileObjectDataPool2 pool
        {
            get { return mPool; }
        }

        Rect mLastViewport;
        float mLastCameraZoom;

        //更新装饰物的scale
        float mScaleFactorAtCameraHeight = 1.0f;
        KeepScaleUpdater mScaleUpdater;

#if USE_CULL
        List<long> mIntersectedTileObjects = new List<long>();
#else
        //npc占领的所有tile object id
        Dictionary<long, List<int>> mNPCTakenIDs = new Dictionary<long, List<int>>();

        //每个tile object被哪些npc占领了
        Dictionary<int, List<long>> mTileObjectsInvaderIDs = new Dictionary<int, List<long>>();
        List<TileObjectInfo2> mIntersectedTileObjects = new List<TileObjectInfo2>();
        //临时中间变量,key is view id
        Dictionary<int, TileObjectData2> mTemporaryObjects = new Dictionary<int, TileObjectData2>(500);
#endif

        List<FrameAction> mSortedActions = new List<FrameAction>();
        List<FrameAction> mInstantActions = new List<FrameAction>();

        //按tile来索引
        Dictionary<int, List<TileObjectData2>> mTileChildren = new Dictionary<int, List<TileObjectData2>>(3000);
        Dictionary<int, TileObjectData2> mActiveTileChildren = new Dictionary<int, TileObjectData2>(2000);
        Dictionary<int, TileObjectData2> mVisibleTileChildren = new Dictionary<int, TileObjectData2>(1000);
        int mActionQueueIndex;
        int mOverlapActionQueueIndex;

        int mUpdateActionQueueIndex;

        //这一层是否有需要缩放的物体
        bool mHasScaleObject = false;
        bool mIsLODChanged = false;
        bool mInited = false;

        static int mActionTimeStamp;

        //在每个tile中,每个lod中每个child object的id
        List<List<int>>[] mTileObjectIDs;

        ObjectPool<List<int>> mIDPool = new ObjectPool<List<int>>(1000, () => new List<int>(5));
        CullManager mTileObjectCullManager = new CullManager();
        Dictionary<int, TileObjectData2> mObjects = new Dictionary<int, TileObjectData2>(1000);

        System.Action<TileObjectData2> mOnActiveStateChangeCallback;
        System.Action<TileObjectData2> mOnObjectScaleChangeCallback;
        System.Action<long, object, bool> mOnTileObjectVisibilityChange;

        ComplexGridBigTileData2[] mBigTileDatas;

        List<PrefabInitInfo2> mPrefabInfos;

        static ObjectPool<List<TileObjectData2>> mTileObjectDataListPool = new ObjectPool<List<TileObjectData2>>(10, () => new List<TileObjectData2>(), l => l.Clear());

        //保存游戏运行后增加的prefab info
        List<PrefabInitInfo2> mDynamicPrefabInfos = new List<PrefabInitInfo2>();
        Dictionary<int, DynamicDecorationObject> mDynamicObjects = new Dictionary<int, DynamicDecorationObject>();
        Rect mRealLayerBounds;
        TileObjectDataPool2 mPool = new TileObjectDataPool2();

        //可删除物体信息
        Dictionary<Vector3, RemovableObjectData> mRemovableObjects = new Dictionary<Vector3, RemovableObjectData>();
        //已经被删除的物体view id
        HashSet<int> mRemovedViewIDs = new HashSet<int>();

        Stream mRemovableObjectsStream;

        bool[,] mDontUpdateTileBigObjectCulling;
#if USE_CULL
        Dictionary<long, NPCInfo> mNPCs = new Dictionary<long, NPCInfo>(1000);
#endif

#if UNITY_EDITOR && TFW_MAP_DEBUG
        GridViewer mGridViewer;
#endif
    };
}