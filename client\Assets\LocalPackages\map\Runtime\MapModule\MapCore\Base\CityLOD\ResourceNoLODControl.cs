﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    //城墙的接口
    public interface IResourceNoLOD : IBuildingElement
    {
        float scaledColliderRadius { get; }
        Vector3 colliderCenter { get; }
    }

    public class ResourceNoLODControl : MonoBehaviour, IResourceNoLOD
    {
        public void Init(BuildingLODManager lodManager, float objectGridCount, float gridSize, GameObject rendererRoot = null)
        {
            if (mInited)
            {
                return;
            }

            if (rendererRoot == null)
            {
                rendererRoot = this.gameObject;
            }

            mInited = true;
            mLODManager = lodManager;
            mGridSize = gridSize;
            mIsVisible = true;
            hidePriority = 999;

            mCurrentColliderRadius = objectGridCount * gridSize;

            if (!System.Object.ReferenceEquals(mLODManager, null))
            {
                if (mRenderers.Count == 0)
                {
                    rendererRoot.GetComponentsInChildren<Renderer>(mRenderers);
                    for (int i = 0; i < mRenderers.Count; ++i)
                    {
                        mRenderers[i].enabled = true;
                    }
                }

                if (System.Object.ReferenceEquals(mCollider, null))
                {
                    mCollider = gameObject.GetComponentInChildren<Collider>();
                }

                mLODManager.AddCity(this);

                //计算物体最大的碰撞半径
                mMaxColliderRadius = objectGridCount * gridSize;
                mScaledColliderRadius = mMaxColliderRadius;
            }

            var t = transform;
 
            mColliderCenter = t.position;
        }

        public void FilterRenderer(Renderer renderer)
        {
            mRenderers.Remove(renderer);
        }

        public void Uninit()
        {
            Debug.Assert(mInited);
            mInited = false;
            if (mLODManager != null)
            {
                mLODManager.RemoveCity(this);
                mLODManager = null;
            }

            mRenderers.Clear();
            mIsVisible = true;
            mCollider = null;


        }

        //根据相机的高度来更新建筑的缩放或位置
        public void UpdateCity()
        {

        }


        //设置主城的移动信息
        //startPos:在相机高度为cameraHeightRange.x时主城的位置
        //endPos:在相机高度为cameraHeightRange.y时主城的位置
        //cameraHeightRange:主城移动期间相机的高度范围
        public void SetMoveInfo(Vector3 startPos, Vector3 endPos, Vector2 cameraHeightRange, Transform cityRootTransform)
        {

        }

        public void SetCityStartPosition(Vector3 startPos)
        {

        }

        //显示或隐藏建筑
        public void SetVisible(bool visible)
        {
            if (mIsVisible != visible)
            {
                mIsVisible = visible;
                for (int i = 0; i < mRenderers.Count; ++i)
                {
                    mRenderers[i].enabled = visible;
                }
                visiableChangedEvent?.Invoke(visible);
            }
        }

        void SetLODManager(BuildingLODManager lodManager)
        {
            mLODManager = lodManager;
        }


        public bool isMainBuilding { get { return false; } }
        public bool isAtFinalPosition
        {
            get
            {
                return true;
            }
        }
        public Vector3 colliderCenter { get { return mColliderCenter; } }

        void OnDrawGizmos()
        {
#if true
            if (mIsVisible)
            {
                Gizmos.DrawWireSphere(colliderCenter, scaledColliderRadius);

                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(transform.position, objectResourceRadius);

                var collider = gameObject.GetComponentInChildren<Collider>();
                if (collider != null)
                {
                    Gizmos.color = Color.red;
                    var boxCollider = collider as BoxCollider;
                    if (boxCollider != null)
                    {
                        Gizmos.matrix = collider.transform.localToWorldMatrix;
                        Gizmos.DrawWireCube(boxCollider.center, boxCollider.size);
                    }
                }
            }
#endif
        }

        public void ClearPotentialCollidableList()
        {

        }

        public void AddPotentialCollidableBuilding(IBuildingElement building)
        {

        }

        public List<IBuildingElement> GetPotentialCollidableBuildingList()
        {
            return null;
        }

        public int GetPriority()
        {
            return hidePriority;
        }
        //建筑物未缩放时的半径,如果使用半径来做建筑的重叠判定时使用
        public float colliderRadius;
        //增加的collider radius,因为当主城大厅填满整个主城时有可能大厅的collider碰不到城墙
        public float increasedColliderRadius = 0;
        //碰撞框的中心点偏移
        public Vector3 colliderOffset;
        //随建筑物缩放后的半径
        public float scaledColliderRadius
        {
            get
            {
                return mScaledColliderRadius;
            }
        }

        /// <summary>
        /// 清除可视化变化事件
        /// </summary>
        public void clearVisiableChangedEvent()
        {
            if (visiableChangedEvent == null) return;
            Delegate[] dels = visiableChangedEvent.GetInvocationList();
            foreach (Delegate del in dels)
            {
                object delObj = del.GetType().GetProperty("Method").GetValue(del, null);
                string funcName = (string)delObj.GetType().GetProperty("Name").GetValue(delObj, null);////方法名
                Console.WriteLine(funcName);
                visiableChangedEvent -= del as OnVisibleChanged;
            }
        }

        public void AddVisiableChangedEvent(OnVisibleChanged pvisiableChangedEvent)
        {
            visiableChangedEvent -= pvisiableChangedEvent;
            visiableChangedEvent += pvisiableChangedEvent;
        }

        public void AddCollision(IBuildingElement building)
        {
        }

        public List<IBuildingElement> GetCollisionList()
        {
            return null;
        }


        //建筑物是否可见
        public bool isVisible { get { return mIsVisible; } }
        //使用collider来判定建筑物重叠时使用
        public Collider objectCollider { get { return mCollider; } }
        public BuildingLODManager lodManager { get { return mLODManager; } }
        public float maxColliderRadius { get { return mMaxColliderRadius; } }
        public int id { get { return gameObject.GetInstanceID(); } }
        public float lowerHeight { get { return 0; } }
        public float upperHeight { get { return 0; } }

        //在建筑物重叠时hidePriority小的隐藏hidePriority大的建筑
        private int hidePriority;

        //在建筑物显示、隐藏时回调
        public delegate void OnVisibleChanged(bool visible);

        //建筑显示、隐藏事件
        public event OnVisibleChanged visiableChangedEvent;

        BuildingLODManager mLODManager;
        //建筑物子节点中所有的renderer
        List<Renderer> mRenderers = new List<Renderer>();
        Collider mCollider;
        private float mCurrentColliderRadius;
        private float mGridSize;
        bool mIsVisible = true;
        private bool mInited = false;


        private Vector3 mColliderCenter;
        private float mScaledColliderRadius;
        //最大可能的碰撞半径,可以利用这个半径进行碰撞检测优化
        private float mMaxColliderRadius;

        //美术资源表示的大小,主要用于城建
        float objectResourceRadius = 0;
    }
}
