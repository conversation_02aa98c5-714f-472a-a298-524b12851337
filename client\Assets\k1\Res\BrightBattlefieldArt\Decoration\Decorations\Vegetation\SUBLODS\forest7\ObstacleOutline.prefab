%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &8013267329684777103
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2656654069132025354}
  - component: {fileID: 2921041458182362356}
  m_Layer: 0
  m_Name: ObstacleOutline
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2656654069132025354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8013267329684777103}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2921041458182362356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8013267329684777103}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -43.4903, y: 0, z: 82.04853}
    - {x: -32.200077, y: 0, z: 96.22276}
    - {x: -25.315952, y: 0, z: 93.3355}
    - {x: -20.069305, y: 0, z: 81.31694}
    - {x: -25.92195, y: 0, z: 68.00055}
    - {x: -35.575127, y: 0, z: 65.27415}
    - {x: -43.079746, y: 0, z: 69.65229}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -24.9826, y: 0, z: 65.415}
    - {x: -23.9678, y: 0, z: 66.2372}
    - {x: -19.1, y: 0, z: 76.031}
    - {x: -18.9941, y: 0, z: 76.3113}
    - {x: -18.1988, y: 0, z: 79.2807}
    - {x: -18.1875, y: 0, z: 79.3258}
    - {x: -17.5705, y: 0, z: 81.953}
    - {x: -17.6511, y: 0, z: 82.9032}
    - {x: -22.5537, y: 0, z: 90}
    - {x: -22.5562, y: 0, z: 90}
    - {x: -22.9891, y: 0, z: 90}
    - {x: -23.8901, y: 0, z: 90}
    - {x: -32.0829, y: 0, z: 90}
    - {x: -33.9539, y: 0, z: 90}
    - {x: -38.4452, y: 0, z: 90}
    - {x: -38.4552, y: 0, z: 90}
    - {x: -45.339, y: 0, z: 84.206}
    - {x: -45.6595, y: 0, z: 83.27}
    - {x: -45.6653, y: 0, z: 81.42}
    - {x: -45.6653, y: 0, z: 81.4086}
    - {x: -45.6205, y: 0, z: 71.4419}
    - {x: -45.5364, y: 0, z: 70.9518}
    - {x: -44.3325, y: 0, z: 67.4969}
    - {x: -43.635, y: 0, z: 66.6597}
    - {x: -36.8586, y: 0, z: 62.8074}
    - {x: -35.7007, y: 0, z: 62.6533}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
