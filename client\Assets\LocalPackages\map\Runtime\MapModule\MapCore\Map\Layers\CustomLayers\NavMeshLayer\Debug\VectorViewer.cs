﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //根据输入来绘制一条路径
    [Black]
    public class VectorViewer
    {
        public void Create(Vector3 start, Vector3 end, float width, Color color)
        {
            Destroy();

            var center = (start + end) * 0.5f;

            mRootObject = new GameObject("Vector Viewer");
            mRootObject.transform.position = center;

            mMaterial = CreateMaterial(color);

            mVectorMesh = CreateVector(center, "Vector", start, end);

            var startObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            startObject.name = "Start";
            startObject.transform.position = start;
            startObject.transform.localScale = Vector3.one * width;
            startObject.transform.SetParent(mRootObject.transform);
            startObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;

            var endObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            endObject.name = "End";
            endObject.transform.position = end;
            endObject.transform.localScale = Vector3.one * width;
            endObject.transform.SetParent(mRootObject.transform);
            endObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;
        }

        Mesh CreateVector(Vector3 center, string name, Vector3 start, Vector3 end)
        {
            var pathObject = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pathObject.name = name;
            pathObject.transform.position = center;
            pathObject.transform.SetParent(mRootObject.transform);
            var mesh = CreateVectorMesh(center, start, end);

            pathObject.GetComponent<MeshFilter>().sharedMesh = mesh;
            pathObject.GetComponent<MeshRenderer>().sharedMaterial = mMaterial;

            return mesh;
        }

        void Destroy()
        {
            if (mMaterial != null)
            {
                GameObject.DestroyImmediate(mMaterial);
                mMaterial = null;
            }
            if (mVectorMesh != null)
            {
                GameObject.DestroyImmediate(mVectorMesh);
                mVectorMesh = null;
            }
            if (mRootObject != null)
            {
                GameObject.DestroyImmediate(mRootObject);
                mRootObject = null;
            }
        }

        Material CreateMaterial(Color color)
        {
            var shader = Shader.Find("SLGMaker/ColorTransparent");
            var mtl = new Material(shader);
            mtl.color = color;
            return mtl;
        }

        Mesh CreateVectorMesh(Vector3 center, Vector3 start, Vector3 end)
        {
            int vertexCount = 4;
            int indexCount = 6;
            Vector3[] vertices = new Vector3[vertexCount];
            int[] indices = new int[indexCount];

            Vector3 d = start - end;
            float length = d.magnitude;
            d.Normalize();
            var d1 = Quaternion.Euler(0, 45, 0) * d;
            var d2 = Quaternion.Euler(0, -45, 0) * d;
            float arrowLength = length * 0.01f;
            var p1 = end + d1 * arrowLength;
            var p2 = end + d2 * arrowLength;

            vertices[0] = start - center;
            vertices[1] = end - center;
            vertices[2] = p1 - center;
            vertices[3] = p2 - center;

            indices[0] = 0;
            indices[1] = 1;
            indices[2] = 1;
            indices[3] = 2;
            indices[4] = 1;
            indices[5] = 3;

            Mesh mesh = new Mesh();
            mesh.vertices = vertices;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            mesh.RecalculateBounds();

            return mesh;
        }

        GameObject mRootObject;
        Mesh mVectorMesh;
        Material mMaterial;
    }
}


#endif