﻿using UnityEngine;

namespace TFW.Map
{
    public partial class SplitFogLayerData : MapLayerData
    {
        void CreateLODData()
        {
            int width = mCols * 2;
            int height = mRows * 2;
            int n = width * height;
            mMaskPixels = new Color32[n];
            Color32 white = new Color32(255, 255, 255, 255);
            for (int i = 0; i < n; ++i)
            {
                mMaskPixels[i] = white;
            }
        }

        public void UpdateLOD()
        {
            mIsSwitchingLOD = true;
            if (mCurrentLOD > 0)
            {
                UpdateMask();
                //change to fog plane lod
                //hide rect fogs
                for (int i = mVisibleRectFogs.Count - 1; i >= 0; --i)
                {
                    RemoveVisibleFog(i);
                }
#if UNITY_EDITOR
                Debug.Assert(mVisibleRectFogs.Count == 0);
#endif

                //hide fog tiles
                var rect = GetViewRect(GetViewportRect());
                var xMin = rect.minX;
                var yMin = rect.minY;
                var xMax = rect.maxX;
                var yMax = rect.maxY;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            SetObjectVisibility(x, y, false);
                        }
                    }
                }

                //show fog plane
                mLODChangeCallback();
            }
            else
            {
                //change to tile lod
                //hide fog tiles
                var rect = GetViewRect(GetViewportRect());
                var xMin = rect.minX;
                var yMin = rect.minY;
                var xMax = rect.maxX;
                var yMax = rect.maxY;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                        {
                            SetObjectVisibility(x, y, true);
                        }
                    }
                }

                mLODChangeCallback();
            }
            mIsSwitchingLOD = false;
        }

        public void ClearPixel(int x, int y)
        {
            int idx = y * mCols * 2 + x;
            mMaskPixels[idx] = new Color32(0, 0, 0, 0);
            mMaskDirty = true;
        }

        public void UpdateMask()
        {
            if (mMaskDirty)
            {
                mMaskDirty = false;
                mUpdateMaskCallback(mMaskPixels);
            }
        }

        public Color32[] maskPixels { get { return mMaskPixels; } }
        public string fogLOD1PrefabPath { get { return mFogLOD1PrefabPath; } set { mFogLOD1PrefabPath = value; } }

        Color32[] mMaskPixels;
        bool mMaskDirty = false;
        string mFogLOD1PrefabPath;
        System.Action mLODChangeCallback;
        System.Action<Color32[]> mUpdateMaskCallback;
    }
}
