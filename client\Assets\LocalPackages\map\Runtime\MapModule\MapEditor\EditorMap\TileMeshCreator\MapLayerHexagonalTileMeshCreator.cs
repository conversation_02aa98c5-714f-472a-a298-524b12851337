﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [Black]
    class MapLayerHexagonalTileMeshCreator : MapLayerTileMeshCreator {
        public override Mesh CreateLayerTileMesh() {
            var mesh = new Mesh();
            Vector3[] positions = new Vector3[7];
            int[] indices = new int[18];
            
            float radius = mLayer.tileHeight * 0.5f;
            Vector3 center = Vector3.zero;
            positions[0] = center;

            for (int i = 0; i < 6; ++i) {
                positions[i + 1] = HexagonalUtils.pointy_hex_corner_xz(center, radius, i);
            }

            for (int i = 1; i <= 6; ++i) {
                indices[(i - 1) * 3] = 0;
                indices[(i - 1) * 3 + 1] = i % 7;
                indices[(i - 1) * 3 + 2] = i % 6 + 1;
            }

            mesh.vertices = positions;
            mesh.triangles = indices;

            return mesh;
        }
    };
}

#endif