﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class SplineEditorUI : UnityEditor.Editor
    {
        public void CreateRiverPrefab(string prefabFolder)
        {
            if (string.IsNullOrEmpty(prefabFolder) || !Directory.Exists(prefabFolder))
            {
                EditorUtility.DisplayDialog("Error", "Select a folder first!", "OK");
                return;
            }

            int n = mEditor.splineObjectManager.splineObjectCount;
            for (int i = 0; i < n; ++i)
            {
                var spline = mEditor.splineObjectManager.splines[i];
                if (spline.riverData.isRiverObject)
                {
                    if (string.IsNullOrEmpty(spline.name))
                    {
                        EditorUtility.DisplayDialog("Error", "Spline name is empty!", "OK");
                        return;
                    }

                    var center = spline.meshGameObject.GetComponent<MeshFilter>().sharedMesh.bounds.center;
                    center.y = 0;

                    var root = new GameObject(spline.name);
                    string subFolder = $"{prefabFolder}/{spline.name}";

                    FileUtil.DeleteFileOrDirectory(subFolder);

                    if (!Directory.Exists(subFolder))
                    {
                        Directory.CreateDirectory(subFolder);
                    }

                    string prefix = $"{subFolder}/{spline.name}";

                    CreateGameObject(prefix, "water", spline.waterGameObject, root.transform, center);
                    CreateGameObject(prefix, "land", spline.meshGameObject, root.transform, center);
                    CreateGameObject(prefix, "mask", spline.stencilMaskGameObject, root.transform, center);

                    string prefabPath = $"{prefix}.prefab";
                    PrefabUtility.SaveAsPrefabAsset(root, prefabPath);

                    AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

                    Utils.DestroyObject(root);
                }
            }
        }

        static Mesh CreateLocalMesh(Mesh globalMesh, Vector3 center)
        {
            var mesh = Object.Instantiate(globalMesh);
            var vertices = mesh.vertices;
            //convert to local vertices
            for (int i = 0; i < vertices.Length; ++i)
            {
                vertices[i] = vertices[i] - center;
            }
            mesh.vertices = vertices;
            mesh.RecalculateBounds();
            return mesh;
        }

        GameObject CreateGameObject(string prefix, string meshName, GameObject obj, Transform parent, Vector3 center)
        {
            var clonedObj = GameObject.Instantiate(obj);
            clonedObj.name = meshName;
            clonedObj.transform.position = Vector3.zero;
            clonedObj.transform.SetParent(parent, false);
            var mesh = clonedObj.GetComponent<MeshFilter>().sharedMesh;
            var localMesh = CreateLocalMesh(mesh, center);
            string meshPath = $"{prefix}_{meshName}.asset";
            AssetDatabase.CreateAsset(localMesh, meshPath);
            clonedObj.GetComponent<MeshFilter>().sharedMesh = localMesh;

            if (mEditor.splineObjectManager.generateObjFile)
            {
                string objPath = $"{prefix}_{meshName}.obj";
                var uv = localMesh.uv;
                if (uv.Length == 0)
                {
                    uv = null;
                }
                OBJExporter.Export(objPath, localMesh.vertices, uv, null, localMesh.triangles);
            }
            return clonedObj;
        }
    }
}

#endif