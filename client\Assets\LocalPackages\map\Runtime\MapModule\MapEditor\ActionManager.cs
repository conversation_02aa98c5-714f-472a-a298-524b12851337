﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public delegate void OnCreateAction(int index);
    public delegate void OnRemoveAction(int startIndex, int count);
    public delegate void OnSetActionPointer(int newIndex);
    public delegate void OnFinishRedoAction(EditorAction action);
    public delegate void OnFinishUndoAction(EditorAction action);

    [Black]
    public class ActionManager
    {
        public event OnCreateAction createActionEvent;
        public event OnRemoveAction removeActionEvent;
        public event OnSetActionPointer setActionPointerEvent;
        public event OnFinishRedoAction finishRedoActionEvent;
        public event OnFinishUndoAction finishUndoActionEvent;

        public ActionManager()
        {
            mInstance = this;
        }

        public void OnDestroy()
        {
            foreach (var action in mActions)
            {
                action.OnDestroy();
            }
            mActions.Clear();
            mActionPointer = -1;
        }

        public void Redo()
        {
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = true;
            }
            int next = mActionPointer + 1;
            if (next >= 0 && next < mActions.Count)
            {
                mIsExecuting = true;
                bool suc = mActions[next].Do();
                if (!suc)
                {
                    for (int i = next; i < mActions.Count; ++i)
                    {
                        mActions[i].OnDestroy();
                        mActions[i] = null;
                    }
                    mActions.RemoveRange(next, mActions.Count - next);
                }
                else
                {
                    if (finishRedoActionEvent != null)
                    {
                        finishRedoActionEvent(mActions[next]);
                    }
                }
                mActionPointer = next;
                if (mActionPointer >= mActions.Count)
                {
                    mActionPointer = mActions.Count - 1;
                }
                mIsExecuting = false;

                if (setActionPointerEvent != null)
                {
                    setActionPointerEvent(mActionPointer);
                }
            }
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = false;
            }
        }

        public void Undo()
        {
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = true;
            }
            if (mActionPointer >= 0 && mActionPointer < mActions.Count)
            {
                mIsExecuting = true;
                bool suc = mActions[mActionPointer].Undo();
                if (!suc)
                {
                    for (int i = mActionPointer; i < mActions.Count; ++i)
                    {
                        mActions[i].OnDestroy();
                        mActions[i] = null;
                    }
                    mActions.RemoveRange(mActionPointer, mActions.Count - mActionPointer);
                }
                else
                {
                    if (finishUndoActionEvent != null)
                    {
                        finishUndoActionEvent(mActions[mActionPointer]);
                    }
                }
                --mActionPointer;
                mIsExecuting = false;

                if (setActionPointerEvent != null)
                {
                    setActionPointerEvent(mActionPointer);
                }
            }
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = false;
            }
        }

        public void PushAction(EditorAction act, bool addToQueue = true, bool execute = true)
        {
            Debug.Assert(mIsExecuting == false);
            Debug.Assert(mMaxActions > 1);
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = true;
            }
            if (addToQueue)
            {
                if (mActions.Count >= mMaxActions && mActionPointer == mActions.Count - 1)
                {
                    //remove first
                    mActions[0].OnDestroy();
                    mActions.RemoveAt(0);
                    --mActionPointer;
                }
                Debug.Assert(act != null);
                if (execute)
                {
                    act.Do();
                }
                if (act.IsUndoable())
                {
                    //drop already undone actions
                    for (int i = mActionPointer + 1; i < mActions.Count; ++i)
                    {
                        mActions[i].OnDestroy();
                    }

                    int startIndex = mActionPointer + 1;
                    int endIndex = mActions.Count - 1;
                    int n = endIndex - startIndex + 1;

                    if (removeActionEvent != null)
                    {
                        removeActionEvent(startIndex, n);
                    }
                    mActions.RemoveRange(startIndex, n);
                    //add new action
                    mActions.Add(act);
                    mActionPointer = mActions.Count - 1;

                    //send add message
                    if (createActionEvent != null)
                    {
                        createActionEvent(mActionPointer);
                    }
                    if (setActionPointerEvent != null)
                    {
                        setActionPointerEvent(mActionPointer);
                    }
                }
                else
                {
                    act.OnDestroy();
                }
            }
            else
            {
                act.Do();
                act.OnDestroy();
            }
            if (Map.currentMap != null)
            {
                Map.currentMap.inAction = false;
            }
        }
        public void Clear()
        {
            foreach (var act in mActions)
            {
                act.OnDestroy();
            }
            mActionPointer = -1;

            if (removeActionEvent != null)
            {
                removeActionEvent(0, mActions.Count);
            }
            if (setActionPointerEvent != null)
            {
                setActionPointerEvent(mActionPointer);
            }

            mActions.Clear();
        }
        public int GetActionPointer()
        {
            return mActionPointer;
        }

        public List<EditorAction> GetAllActions()
        {
            return mActions;
        }

        public EditorAction GetAction(int index)
        {
            if (index >= 0 && index < mActions.Count)
            {
                return mActions[index];
            }
            return null;
        }

        public int count { get { return mActions.Count; } }
        public int currentAction { get { return mActionPointer; } }
        public static ActionManager instance { get { return mInstance; } }

        int mActionPointer = -1;
        int mMaxActions = 1000;
        List<EditorAction> mActions = new List<EditorAction>();
        bool mIsExecuting = false;

        static ActionManager mInstance;
    }
}

#endif