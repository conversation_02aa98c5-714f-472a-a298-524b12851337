{"skeleton": {"hash": "YFtUVYW1bSk", "spine": "4.2.43", "x": -298.5, "y": -22.79, "width": 715.44, "height": 1796.99, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 395.31, "y": 1158.64}, {"name": "ALL2", "parent": "ALL", "x": -381.21, "y": -4.33, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 92.08, "rotation": 111.8, "x": -3.45, "y": 8.49}, {"name": "body2", "parent": "body", "length": 250.74, "rotation": 83.36, "x": 92.08, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 81.21, "rotation": 79.74, "x": 250.74, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 185.65, "rotation": -4.15, "x": 81.21}, {"name": "sh_L", "parent": "body2", "x": 232.61, "y": -141.15, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 222.21, "y": 163.02, "inherit": "noScale"}, {"name": "tun", "parent": "ALL2", "length": 205.3, "rotation": -71.2, "x": 5.51, "y": -15.88}, {"name": "leg_L2", "parent": "tun", "x": 74.47, "y": 109.06}, {"name": "leg_R2", "parent": "tun", "x": 80.98, "y": -80.03}, {"name": "leg_R3", "parent": "leg_R2", "length": 407.27, "rotation": -34.3, "x": 58.13, "y": -8.13}, {"name": "leg_R4", "parent": "leg_R3", "length": 337.13, "rotation": 1.16, "x": 407.27, "inherit": "noScale"}, {"name": "leg_L3", "parent": "leg_L2", "length": 451.75, "rotation": -26.04, "x": 60.77, "y": 1.08}, {"name": "leg_L4", "parent": "leg_L3", "length": 376.04, "rotation": 8.21, "x": 451.75, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 251.89, "rotation": -98.39, "x": -17.99, "y": 7.09, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 253.79, "rotation": 16.92, "x": 251.89}, {"name": "arm_R3", "parent": "arm_R2", "length": 83.41, "rotation": -6.68, "x": 253.79}, {"name": "arm_R4", "parent": "arm_R3", "length": 91.53, "rotation": 12.32, "x": 83.41}, {"name": "arm_L2", "parent": "sh_L", "length": 246.43, "rotation": -117.33, "x": -19.85, "y": -16.35}, {"name": "arm_L3", "parent": "arm_L2", "length": 211.62, "rotation": -115.82, "x": 246.43, "inherit": "noScale"}, {"name": "hair_F", "parent": "head", "x": 145.52, "y": 31.5}, {"name": "hair_F2", "parent": "hair_F", "length": 42.79, "rotation": 118.45, "x": 0.47, "y": 22.25}, {"name": "hair_F3", "parent": "hair_F2", "length": 42.64, "rotation": 31.9, "x": 42.79, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 38.77, "rotation": 27.01, "x": 42.64, "color": "abe323ff"}, {"name": "hair_F5", "parent": "hair_F4", "length": 34.31, "rotation": 28.68, "x": 38.77, "color": "abe323ff"}, {"name": "hair_F6", "parent": "hair_F", "length": 33.41, "rotation": -136.37, "x": 1.48, "y": -21.37}, {"name": "hair_F7", "parent": "hair_F6", "length": 35.79, "rotation": -11.53, "x": 33.41, "color": "abe323ff"}, {"name": "hair_F8", "parent": "hair_F7", "length": 27.65, "rotation": 6.59, "x": 35.79, "color": "abe323ff"}, {"name": "hair_R", "parent": "head", "x": 112.77, "y": 69.2}, {"name": "hair_R2", "parent": "hair_R", "length": 44.04, "rotation": 148.3, "x": -5.11, "y": 5.95}, {"name": "hair_R3", "parent": "hair_R2", "length": 36.42, "rotation": 53.08, "x": 44.04, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 56.59, "rotation": -54.56, "x": 36.42, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 36.09, "rotation": 93.94, "x": 56.59, "color": "abe323ff"}, {"name": "hair_R6", "parent": "hair_R5", "length": 31.63, "rotation": -86.72, "x": 36.38, "y": 0.28, "color": "abe323ff"}, {"name": "hair_R7", "parent": "hair_R6", "length": 19.92, "rotation": 88.64, "x": 31.63, "color": "abe323ff"}, {"name": "hair_Ba", "parent": "sh_L", "length": 62.19, "rotation": -153.32, "x": -14.36, "y": -11.2}, {"name": "hair_Ba2", "parent": "hair_Ba", "length": 55.1, "rotation": -16.46, "x": 62.19, "color": "abe323ff"}, {"name": "hair_Ba3", "parent": "hair_Ba2", "length": 53.65, "rotation": -5.78, "x": 55.1, "color": "abe323ff"}, {"name": "hair_Ba4", "parent": "hair_Ba3", "length": 60.88, "rotation": -4.28, "x": 53.65, "color": "abe323ff"}, {"name": "hand_L", "parent": "body", "length": 79.5, "rotation": -87.07, "x": 13.01, "y": -47.36, "inherit": "noScale"}, {"name": "hand_L2", "parent": "hand_L", "length": 62.25, "rotation": -51.15, "x": 79.5, "inherit": "noScale"}, {"name": "foot_R", "parent": "root", "length": 83.3, "rotation": 52.61, "x": -280.21, "y": 12.41}, {"name": "foot_R2", "parent": "foot_R", "length": 180.75, "rotation": 31.64, "x": 83.3}, {"name": "foot_L", "parent": "root", "length": 75.06, "rotation": 83.85, "x": 97.88, "y": -18.7}, {"name": "foot_L2", "parent": "foot_L", "length": 166.22, "rotation": 2.39, "x": 75.06}, {"name": "eyebrow_L", "parent": "head", "length": 10.39, "rotation": 86.49, "x": 79.47, "y": -38.43}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 24.23, "rotation": 23.43, "x": 10.39}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 17.39, "rotation": 4.11, "x": 24.23}, {"name": "eyebrow_R", "parent": "head", "length": 7.61, "rotation": -69.01, "x": 72.89, "y": 79.52}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 19.43, "rotation": -41.22, "x": 7.61}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 15.93, "rotation": -3.69, "x": 19.43}, {"name": "eye_L", "parent": "head", "x": 54.63, "y": -16.99}, {"name": "eye_R", "parent": "head", "x": 54.03, "y": 53.07}, {"name": "RU_L", "parent": "body2", "x": 105.6, "y": -37.62}, {"name": "RU_L2", "parent": "RU_L", "x": -7.31, "y": -2.26}, {"name": "RU_L3", "parent": "RU_L2", "x": -7.97, "y": -4.83}, {"name": "RU_R", "parent": "body2", "x": 97.57, "y": 120.43}, {"name": "RU_R2", "parent": "RU_R", "x": -12.04, "y": 21.33}, {"name": "RU_R3", "parent": "RU_R2", "x": -9.75, "y": 13.15, "color": "abe323ff"}, {"name": "bone61", "parent": "sh_L", "length": 279.79, "rotation": -113.62, "x": -18.65, "y": -15.66}, {"name": "bone62", "parent": "bone61", "length": 243.49, "rotation": -124.68, "x": 279.79, "color": "abe323ff"}, {"name": "arm_L", "parent": "hand_L2", "rotation": 26.42, "x": 63.73, "y": -0.15, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "ALL2", "x": 347.78, "y": 149.43, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone63", "parent": "leg_L2", "length": 831.01, "rotation": -22.26, "x": 60.79, "y": 1.09}, {"name": "leg_L", "parent": "foot_L2", "rotation": -86.24, "x": 166.19, "y": -0.01, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "bone63", "rotation": 93.52, "x": 453.69, "y": -29.39, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone64", "parent": "leg_R2", "length": 747.39, "rotation": -33.74, "x": 58.13, "y": -8.13}, {"name": "leg_R", "parent": "foot_R2", "rotation": -84.25, "x": 181.62, "y": -0.03, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "bone64", "rotation": 104.98, "x": 407.25, "y": -3.74, "color": "ff3f00ff", "icon": "ik"}, {"name": "cloth", "parent": "body2", "length": 68.01, "rotation": -82.32, "x": 54.24, "y": 162.68, "inherit": "onlyTranslation", "color": "abe323ff"}, {"name": "cloth2", "parent": "cloth", "length": 66.93, "rotation": 2.91, "x": 68.01, "color": "abe323ff"}, {"name": "cloth3", "parent": "cloth2", "length": 61.05, "rotation": 0.52, "x": 66.93, "color": "abe323ff"}, {"name": "cloth4", "parent": "cloth3", "length": 72.11, "rotation": -1.72, "x": 61.05, "color": "abe323ff"}, {"name": "hair_Bb", "parent": "sh_L", "length": 60.23, "rotation": -165.96, "x": -23, "y": 7.43}, {"name": "hair_Bb2", "parent": "hair_Bb", "length": 74.53, "rotation": -3.4, "x": 60.23, "color": "abe323ff"}, {"name": "hair_Bb3", "parent": "hair_Bb2", "length": 57.48, "rotation": -3.42, "x": 74.53, "color": "abe323ff"}, {"name": "hair_Bb4", "parent": "hair_Bb3", "length": 42.75, "rotation": -12.13, "x": 57.48, "color": "abe323ff"}, {"name": "hair_Bb5", "parent": "hair_Bb4", "length": 40.93, "rotation": -15.54, "x": 42.75, "color": "abe323ff"}, {"name": "earring", "parent": "head", "length": 31.01, "rotation": -88.93, "x": 22.06, "y": -66.15, "inherit": "noRotationOrReflection", "color": "abe323ff"}, {"name": "hair", "parent": "neck", "x": 132.9, "y": -110.54}, {"name": "headround3", "parent": "head", "x": 373.77, "y": -0.28, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "x": -5.07, "y": -48.71, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 321.14, "y": -48.98, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 250.5, "y": -458.08, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 196.2, "y": -458.08, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 370.66, "y": -95.18, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 370.66, "y": -157.55, "icon": "warning"}], "slots": [{"name": "hair_B", "bone": "root", "attachment": "hair_B"}, {"name": "arm_R", "bone": "root", "attachment": "arm_R"}, {"name": "cloth_B", "bone": "root", "attachment": "cloth_B"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "cloth", "bone": "root", "attachment": "cloth"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "hair_R", "bone": "root", "attachment": "hair_R"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "order": 1, "bones": ["bone61", "bone62"], "target": "arm_L", "bendPositive": false}, {"name": "arm_L1", "order": 3, "bones": ["arm_L2"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 4, "bones": ["arm_L3"], "target": "arm_L", "compress": true, "stretch": true}, {"name": "leg_L", "order": 6, "bones": ["bone63"], "target": "leg_L", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 7, "bones": ["leg_L3"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 8, "bones": ["leg_L4"], "target": "leg_L", "compress": true, "stretch": true}, {"name": "leg_R", "order": 10, "bones": ["bone64"], "target": "leg_R", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 11, "bones": ["leg_R3"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 12, "bones": ["leg_R4"], "target": "leg_R", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 2, "bones": ["arm_L1"], "target": "bone62", "rotation": 154.51, "x": 32.59, "y": -17.45, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 14, "bones": ["bodyround2"], "target": "bodyround", "x": -54.3, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "bones": ["sh_L"], "target": "bodyround", "x": -17.88, "y": 316.94, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 16, "bones": ["sh_R"], "target": "bodyround", "x": -28.28, "y": 621.1, "mixRotate": 0, "mixX": -0.008, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 13, "bones": ["headround2"], "target": "headround", "x": -47.56, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 17, "bones": ["eyebrow_L"], "target": "headround", "rotation": 86.49, "x": -289.22, "y": 10.55, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 18, "bones": ["eyebrow_R"], "target": "headround", "rotation": -69.01, "x": -295.81, "y": 128.5, "mixRotate": 0, "mixX": 0.035, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 19, "bones": ["hair_F"], "target": "headround", "x": -223.18, "y": 80.49, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 20, "bones": ["hair_R"], "target": "headround", "x": -255.93, "y": 118.19, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround5", "order": 21, "bones": ["hair"], "target": "headround", "rotation": 4.15, "x": -309.15, "y": -57.52, "mixRotate": 0, "mixX": -0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround6", "order": 23, "bones": ["earring"], "target": "headround", "rotation": -164.52, "x": -346.64, "y": -17.17, "mixRotate": 0, "mixX": 0.005, "mixScaleX": 0, "mixShearY": 0}, {"name": "RU_R3", "order": 24, "bones": ["cloth"], "target": "RU_R3", "rotation": -165.68, "x": -21.53, "y": 7.77, "mixRotate": 0, "mixX": 0.2, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 15, "bones": ["tunround2"], "target": "tunround", "y": -62.37, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 5, "bones": ["leg_L2"], "target": "tunround", "rotation": -71.2, "x": -237.91, "y": 43.94, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 9, "bones": ["leg_R2"], "target": "tunround", "rotation": -71.2, "x": -414.82, "y": -23.15, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "cloth2", "order": 25, "bone": "cloth2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "cloth3", "order": 26, "bone": "cloth3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "cloth4", "order": 27, "bone": "cloth4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "earring", "order": 22, "bone": "earring", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Ba2", "order": 38, "bone": "hair_Ba2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Ba3", "order": 39, "bone": "hair_Ba3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Ba4", "order": 40, "bone": "hair_Ba4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Bb2", "order": 41, "bone": "hair_Bb2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Bb3", "order": 42, "bone": "hair_Bb3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Bb4", "order": 43, "bone": "hair_Bb4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_Bb5", "order": 44, "bone": "hair_Bb5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 30, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 31, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F5", "order": 32, "bone": "hair_F5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F7", "order": 28, "bone": "hair_F7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F8", "order": 29, "bone": "hair_F8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 33, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 34, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 35, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R6", "order": 36, "bone": "hair_R6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R7", "order": 37, "bone": "hair_R7", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.99851, 0.05018, 0.99415, 0.15953, 0.95272, 0.35932, 0.93669, 0.38844, 0.92068, 0.53332, 0.8586, 0.65552, 0.76968, 0.65191, 0.72089, 0.71075, 0.65439, 0.76908, 0.45254, 0.85837, 0.41848, 0.87302, 0.38046, 0.89419, 0.33326, 0.94229, 0.26908, 0.93906, 0.2113, 0.79956, 0.19372, 0.79244, 0.15833, 0.94389, 0.13397, 1, 0.05096, 0.94801, 0, 0.83532, 0.00557, 0.54839, 0.09767, 0.45964, 0.17422, 0.4769, 0.21192, 0.4771, 0.24432, 0.55236, 0.30402, 0.65075, 0.32594, 0.67395, 0.34673, 0.67875, 0.38137, 0.63999, 0.41601, 0.60124, 0.58858, 0.36837, 0.59349, 0.28856, 0.69903, 0.06209, 0.736, 0.03125, 0.80233, 0.05007, 0.8708, 0.12941, 0.91293, 0.21985, 0.95125, 0.08587, 0.96965, 0, 0.98711, 0.00538, 0.20846, 0.62969, 0.17666, 0.6364, 0.23965, 0.65653, 0.65364, 0.25973, 0.73693, 0.27511, 0.81583, 0.36552, 0.86142, 0.48863], "triangles": [39, 0, 38, 0, 37, 38, 44, 33, 34, 32, 33, 44, 43, 32, 44, 43, 31, 32, 2, 36, 1, 45, 35, 36, 3, 45, 36, 44, 34, 45, 2, 3, 36, 46, 45, 3, 4, 46, 3, 6, 44, 45, 6, 45, 46, 5, 6, 46, 5, 46, 4, 7, 43, 44, 7, 44, 6, 43, 30, 31, 43, 7, 30, 8, 30, 7, 29, 30, 8, 9, 29, 8, 28, 29, 9, 10, 28, 9, 27, 28, 10, 11, 27, 10, 27, 11, 12, 45, 34, 35, 1, 36, 37, 1, 37, 0, 40, 23, 24, 42, 40, 24, 42, 24, 25, 42, 14, 40, 25, 14, 42, 13, 25, 26, 13, 14, 25, 26, 12, 13, 27, 12, 26, 40, 22, 23, 41, 22, 40, 15, 41, 40, 14, 15, 40, 21, 41, 20, 41, 21, 22, 41, 18, 19, 41, 19, 20, 18, 41, 15, 16, 18, 15, 17, 18, 16], "vertices": [2, 21, -57.12, 11.34, 0.51237, 20, 281.52, 46.48, 0.48763, 2, 21, -46.36, 26.59, 0.65117, 20, 290.56, 30.16, 0.34883, 2, 21, -15.92, 48.16, 0.91461, 20, 296.72, -6.64, 0.08539, 2, 21, -8.26, 49.43, 0.9534, 20, 294.52, -14.09, 0.0466, 2, 21, 9.3, 67.7, 1, 20, 303.33, -37.85, 0, 2, 21, 39.76, 74, 1, 20, 295.73, -68.02, 0, 2, 21, 68.12, 56.78, 1, 20, 267.88, -86.04, 0, 2, 21, 88.88, 56.26, 1, 20, 258.38, -104.51, 0, 2, 21, 115.3, 52.35, 1, 20, 243.35, -126.59, 0, 1, 21, 188, 27.57, 1, 1, 21, 200.23, 23.33, 1, 3, 42, 78.38, -13.01, 0.01156, 21, 214.3, 19.31, 0.98844, 20, 170.49, -201.32, 0, 4, 42, 66.25, -28.16, 0.54392, 21, 233.62, 17.51, 0.45592, 20, 160.46, -217.94, 0, 87, -215.96, 111.89, 0.00016, 5, 41, 77.62, -58.75, 0.00932, 42, 44.57, -38.32, 0.92055, 21, 254.03, 4.99, 0.06305, 20, 140.3, -230.85, 0, 87, -239.89, 112.44, 0.00708, 5, 41, 67.96, -28.19, 0.38435, 42, 14.72, -26.67, 0.59528, 20, 109.18, -223.23, 0, 85, -286.17, 306.07, 0.00143, 87, -261.45, 136.16, 0.01893, 5, 41, 62.51, -24.35, 0.68833, 42, 8.31, -28.51, 0.28326, 20, 103.06, -225.89, 0, 85, -285.72, 312.73, 0.00396, 87, -268, 137.37, 0.02444, 5, 41, 39.75, -42.21, 0.9583, 42, 7.94, -57.44, 0.00248, 20, 106.5, -254.62, 0, 85, -312.82, 322.86, 0.00625, 87, -281.2, 111.62, 0.03296, 4, 41, 27.51, -47.08, 0.95826, 20, 104.29, -267.61, 0, 85, -323.35, 330.79, 0.00775, 87, -290.29, 102.08, 0.03399, 4, 41, 3.08, -26.1, 0.9595, 20, 73.67, -277.57, 0, 85, -318.15, 362.56, 0.01311, 87, -321.25, 110.92, 0.02739, 4, 41, -6.17, -0.75, 0.96048, 20, 47.21, -272.31, 0, 85, -301.32, 383.66, 0.01932, 87, -340.26, 130.08, 0.0202, 3, 41, 16.12, 42.69, 0.96165, 85, -252.63, 387.24, 0.02822, 87, -338.18, 178.86, 0.01013, 3, 41, 53.63, 42.02, 0.96728, 85, -233.67, 354.86, 0.02347, 87, -303.83, 193.95, 0.00925, 4, 41, 78.34, 27.42, 0.86092, 42, -22.08, 16.3, 0.1123, 85, -233.28, 326.16, 0.01972, 87, -275.28, 191.01, 0.00706, 4, 41, 91.1, 21.5, 0.59063, 42, -9.47, 22.52, 0.38985, 85, -231.69, 312.19, 0.01455, 87, -261.22, 190.98, 0.00498, 4, 41, 96.72, 4.83, 0.09571, 42, 7.05, 16.44, 0.89051, 85, -243, 298.7, 0.00882, 87, -249.13, 178.18, 0.00497, 3, 42, 34.43, 11.37, 0.99559, 85, -257.04, 274.65, 0.00336, 87, -226.86, 161.46, 0.00106, 2, 42, 43.51, 11.48, 0.9772, 21, 213.02, -23.28, 0.0228, 2, 42, 50.82, 14.19, 0.81, 21, 206.74, -18.68, 0.19, 2, 42, 59.46, 25.84, 0.24292, 21, 192.25, -17.87, 0.75708, 3, 42, 68.1, 37.49, 0.02769, 21, 177.77, -17.06, 0.97231, 20, 153.66, -152.61, 0, 2, 21, 102.23, -18.87, 0.99684, 20, 184.93, -83.81, 0.00316, 2, 21, 93.82, -29.67, 0.98177, 20, 178.87, -71.53, 0.01823, 2, 21, 40.42, -43.13, 0.8869, 20, 190.01, -17.61, 0.1131, 2, 21, 25.87, -40.72, 0.78783, 20, 198.51, -5.56, 0.21217, 2, 21, 6.1, -25.5, 0.68, 20, 220.82, 5.61, 0.32, 2, 21, -9.18, -0.99, 0.56258, 20, 249.54, 8.7, 0.43742, 2, 21, -15.02, 20.2, 0.68008, 20, 271.16, 4.73, 0.31992, 2, 21, -38.84, 7.71, 0.46671, 20, 270.29, 31.6, 0.53329, 2, 21, -52.11, -1.45, 0.37585, 20, 267.83, 47.54, 0.62415, 2, 21, -57.28, 2.62, 0.4275, 20, 273.74, 50.42, 0.5725, 5, 41, 79.08, -1.52, 0.44758, 42, 0.92, -1.28, 0.52967, 20, 92.16, -199.87, 0, 85, -257.6, 310.47, 0.0092, 87, -262.51, 165.04, 0.01355, 3, 41, 67.82, 2.41, 0.97135, 85, -260.11, 322.12, 0.0132, 87, -274.37, 163.9, 0.01545, 5, 41, 87.74, -10.53, 0.0016, 42, 13.37, -0.19, 0.9818, 20, 104.36, -197.15, 0, 85, -260.79, 298.38, 0.00531, 87, -250.87, 160.47, 0.01129, 2, 21, 71.96, -22.62, 0.92654, 20, 194.74, -54.93, 0.07346, 2, 21, 46.43, -4.72, 0.96675, 20, 221.96, -39.74, 0.03325, 2, 21, 28.74, 23.37, 1, 20, 254.96, -36.05, 0, 2, 21, 24.58, 50.01, 1, 20, 280.75, -43.9, 0], "hull": 40, "edges": [0, 78, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 30, 32, 32, 34, 36, 38, 40, 42, 60, 62, 62, 64, 68, 70, 70, 72, 76, 78, 38, 40, 34, 36, 24, 26, 16, 18, 22, 24, 18, 20, 20, 22, 50, 52, 52, 54, 58, 60, 54, 56, 56, 58, 26, 28, 28, 30, 46, 48, 48, 50, 42, 44, 44, 46, 64, 66, 66, 68, 6, 8, 2, 4, 4, 6, 72, 74, 74, 76, 86, 88, 88, 90, 90, 92], "width": 373, "height": 170}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.87228, 0.00991, 0.90229, 0.03303, 0.9653, 0.08159, 0.99669, 0.13713, 0.96582, 0.16141, 0.93495, 0.1857, 0.88653, 0.22379, 0.80488, 0.28802, 0.79163, 0.33395, 0.77633, 0.3716, 0.78103, 0.40518, 0.75953, 0.42573, 0.79832, 0.44333, 0.83726, 0.51565, 0.78402, 0.62902, 0.81162, 0.69783, 0.84029, 0.72382, 0.86096, 0.74543, 0.90396, 0.7669, 0.99255, 0.79666, 0.98974, 0.84053, 0.9831, 0.87319, 0.95509, 0.90381, 0.92167, 0.93895, 0.88086, 0.97751, 0.82596, 1, 0.78393, 1, 0.70192, 0.97929, 0.54217, 0.94295, 0.50932, 0.89322, 0.46543, 0.86998, 0.49722, 0.83627, 0.52901, 0.80256, 0.53539, 0.77548, 0.49561, 0.75979, 0.50346, 0.74334, 0.47749, 0.72096, 0.34693, 0.65113, 0.16577, 0.53034, 0.17755, 0.49992, 0.01321, 0.4829, 0.0097, 0.4459, 0.0062, 0.40889, 0.1586, 0.3998, 0.22363, 0.38138, 0.16348, 0.35683, 0.22243, 0.32309, 0.17361, 0.29168, 0.21925, 0.23055, 0.31829, 0.17763, 0.37748, 0.13746, 0.41131, 0.08216, 0.49747, 0.05211, 0.58483, 0.02179, 0.69218, 0, 0.79068, 0.00103, 0.72467, 0.87362, 0.72036, 0.89614, 0.74045, 0.84456, 0.73834, 0.94064, 0.20114, 0.44018, 0.34842, 0.42436, 0.60032, 0.42561, 0.34987, 0.3968, 0.36857, 0.36662, 0.37646, 0.33335, 0.50211, 0.15589, 0.63187, 0.34086, 0.77151, 0.17796, 0.60883, 0.37188, 0.60311, 0.39985, 0.44461, 0.2553, 0.69328, 0.2701, 0.53098, 0.1025, 0.63111, 0.06898, 0.63969, 0.0317, 0.77774, 0.03089, 0.81462, 0.12944, 0.8402, 0.08102, 0.64167, 0.1042, 0.55968, 0.0666], "triangles": [77, 66, 79, 77, 79, 78, 77, 78, 2, 77, 2, 3, 4, 77, 3, 68, 77, 4, 29, 30, 56, 57, 29, 56, 22, 56, 21, 57, 56, 22, 23, 57, 22, 59, 57, 23, 28, 29, 57, 28, 57, 59, 24, 59, 23, 27, 28, 59, 27, 59, 24, 26, 27, 24, 25, 26, 24, 19, 58, 18, 33, 18, 32, 18, 33, 17, 20, 58, 19, 58, 32, 18, 31, 32, 58, 21, 58, 20, 56, 31, 58, 56, 58, 21, 30, 31, 56, 61, 63, 70, 61, 43, 63, 62, 61, 70, 11, 70, 10, 62, 70, 11, 60, 43, 61, 42, 43, 60, 41, 42, 60, 40, 41, 60, 39, 40, 60, 13, 38, 12, 11, 12, 62, 39, 61, 62, 61, 39, 60, 39, 12, 38, 38, 13, 37, 39, 62, 12, 14, 37, 13, 37, 14, 15, 36, 37, 15, 36, 15, 16, 35, 36, 16, 35, 16, 17, 33, 35, 17, 34, 35, 33, 6, 68, 5, 51, 52, 80, 73, 51, 80, 73, 80, 74, 50, 51, 73, 66, 50, 73, 49, 50, 66, 71, 49, 66, 48, 49, 71, 66, 73, 79, 68, 66, 77, 68, 71, 66, 72, 68, 6, 72, 71, 68, 7, 72, 6, 47, 48, 71, 65, 46, 47, 71, 65, 47, 67, 65, 71, 7, 67, 72, 72, 67, 71, 7, 8, 67, 65, 45, 46, 64, 65, 67, 64, 45, 65, 9, 67, 8, 69, 64, 67, 69, 67, 9, 44, 45, 64, 63, 44, 64, 70, 64, 69, 63, 64, 70, 63, 43, 44, 70, 69, 9, 10, 70, 9, 5, 68, 4, 80, 52, 75, 78, 1, 2, 73, 74, 79, 74, 80, 75, 76, 54, 55, 76, 55, 0, 75, 53, 54, 76, 75, 54, 76, 0, 1, 78, 76, 1, 76, 74, 75, 74, 76, 78, 79, 74, 78, 52, 53, 75], "vertices": [2, 8, 14.59, -19.03, 0.98975, 4, 236.8, 143.99, 0.01025, 2, 8, -1.21, -25.76, 0.78891, 4, 221.01, 137.26, 0.21109, 2, 8, -34.36, -39.9, 0.56571, 4, 187.85, 123.12, 0.43429, 2, 8, -73.06, -49.52, 0.06286, 4, 149.16, 113.5, 0.93714, 3, 8, -90.81, -46.56, 0.00784, 16, 71.15, 55.84, 0.25395, 4, 131.41, 116.47, 0.73821, 2, 16, 88.99, 53.41, 0.617, 4, 113.65, 119.43, 0.383, 2, 16, 116.96, 49.61, 0.87844, 4, 85.8, 124.09, 0.12156, 1, 16, 164.14, 43.2, 1, 2, 16, 196.8, 45.85, 0.97997, 17, -39.36, 59.89, 0.02003, 2, 16, 223.68, 47.3, 0.80826, 17, -13.22, 53.47, 0.19174, 2, 16, 247.22, 51.55, 0.46192, 17, 10.54, 50.67, 0.53808, 2, 16, 262.2, 50.23, 0.21289, 17, 24.49, 45.06, 0.78711, 2, 16, 273.68, 58.28, 0.07198, 17, 37.82, 49.41, 0.92802, 1, 17, 89.68, 48.01, 1, 1, 17, 168.22, 27.51, 1, 1, 17, 217.34, 24.66, 1, 2, 17, 236.32, 26.51, 0.92666, 18, -20.43, 24.3, 0.07334, 2, 17, 252.04, 27.54, 0.55195, 18, -4.95, 27.15, 0.44805, 2, 17, 268.19, 32.16, 0.14464, 18, 10.56, 33.62, 0.85536, 3, 17, 291.27, 43.21, 0.00171, 18, 32.2, 47.28, 0.99166, 19, -39.93, 57.12, 0.00662, 2, 18, 63.4, 45.82, 0.83586, 19, -9.76, 49.03, 0.16414, 2, 18, 86.62, 43.99, 0.47221, 19, 12.53, 42.29, 0.52779, 2, 18, 108.26, 38.76, 0.11966, 19, 32.55, 32.56, 0.88034, 2, 18, 133.09, 32.54, 0.00098, 19, 55.48, 21.18, 0.99902, 1, 19, 80.49, 8.05, 1, 1, 19, 93.83, -4.49, 1, 1, 19, 92.16, -11.1, 1, 1, 19, 74.61, -20.37, 1, 1, 19, 43.19, -39.12, 1, 2, 18, 98.39, -33.18, 0.30567, 19, 7.56, -35.61, 0.69433, 2, 18, 81.62, -39.75, 0.66292, 19, -10.23, -38.46, 0.33708, 2, 18, 57.8, -33.83, 0.95657, 19, -32.24, -27.59, 0.04343, 2, 17, 284.29, -31.67, 0.01022, 18, 33.97, -27.91, 0.98978, 2, 17, 265.37, -27.79, 0.21893, 18, 14.74, -26.26, 0.78107, 2, 17, 253.37, -32.51, 0.51639, 18, 3.36, -32.34, 0.48361, 2, 17, 241.98, -29.51, 0.77227, 18, -8.3, -30.69, 0.22773, 2, 17, 225.59, -31.31, 0.97387, 18, -24.36, -34.38, 0.02613, 1, 17, 173.29, -44.85, 1, 1, 17, 83.88, -61.12, 1, 1, 17, 62.74, -56.02, 1, 2, 16, 320.11, -63.44, 0.00558, 17, 46.81, -80.55, 0.99442, 2, 16, 294.13, -67.84, 0.04918, 17, 20.67, -77.2, 0.95082, 2, 16, 268.15, -72.25, 0.11913, 17, -5.46, -73.85, 0.88087, 2, 16, 258.15, -48.77, 0.29879, 17, -8.2, -48.48, 0.70121, 2, 16, 243.63, -40.26, 0.70965, 17, -19.61, -36.11, 0.29035, 2, 16, 227.76, -52.45, 0.9491, 17, -38.34, -43.16, 0.0509, 2, 16, 202.6, -46.51, 0.99944, 17, -60.69, -30.15, 0.00056, 1, 16, 181.63, -57.59, 1, 1, 16, 137.5, -56.63, 1, 1, 16, 97.88, -46.26, 1, 1, 16, 68.19, -40.94, 1, 2, 8, -45.15, 49.2, 0.09714, 16, 28.43, -41.26, 0.90286, 2, 8, -22.28, 37.81, 0.21091, 16, 5.23, -30.58, 0.78909, 2, 8, 0.8, 26.25, 0.65526, 16, -18.19, -19.73, 0.34474, 1, 8, 18.22, 10.77, 1, 1, 8, 19.34, -5.17, 1, 2, 18, 85.57, 2.14, 0.32369, 19, 2.57, 1.63, 0.67631, 1, 19, 17.95, -2.98, 1, 2, 18, 64.97, 5.36, 0.98716, 19, -16.86, 9.17, 0.01284, 1, 19, 49.38, -7.91, 1, 2, 16, 285.58, -37.76, 0.07101, 17, 21.25, -45.93, 0.92899, 2, 16, 270.96, -15.79, 0.11386, 17, 13.65, -20.66, 0.88614, 2, 16, 265.89, 24.71, 0.12998, 17, 20.59, 19.56, 0.87002, 2, 16, 251.51, -18.43, 0.5788, 17, -5.72, -17.52, 0.4212, 3, 16, 229.81, -18.56, 0.96562, 17, -26.52, -11.33, 0.02925, 85, -275.4, 653.76, 0.00513, 2, 16, 206.19, -20.76, 0.9912, 85, -251.73, 655.23, 0.0088, 2, 16, 78.22, -19.05, 0.98272, 85, -123.87, 649.62, 0.01728, 3, 16, 205.44, 20.96, 0.97481, 17, -38.33, 33.57, 0.01576, 85, -252.26, 613.51, 0.00943, 3, 16, 87.4, 26.42, 0.86058, 4, 116.06, 146.37, 0.12187, 85, -134.44, 604.45, 0.01755, 3, 16, 227.84, 20.49, 0.88297, 17, -17.04, 26.6, 0.11065, 85, -274.63, 614.67, 0.00638, 2, 16, 247.67, 22.48, 0.52414, 17, 2.51, 22.73, 0.47586, 2, 16, 149.6, -17.94, 0.9846, 85, -195.25, 650.69, 0.0154, 2, 16, 154.15, 23.45, 0.98502, 85, -201.06, 609.46, 0.01498, 3, 8, -57.29, 28.27, 0.14841, 16, 39.93, -19.97, 0.83167, 85, -85.57, 649.37, 0.01992, 3, 8, -31.71, 14.91, 0.72621, 4, 190.51, 177.94, 0.25915, 85, -59.99, 636.02, 0.01464, 2, 8, -5.18, 16.6, 0.98939, 85, -33.47, 637.71, 0.01061, 3, 8, -2.02, -5.54, 0.90569, 4, 220.19, 157.48, 0.08307, 85, -30.31, 615.56, 0.01125, 3, 8, -71.03, -19.59, 0.07032, 4, 151.18, 143.43, 0.91411, 85, -99.31, 601.51, 0.01558, 3, 8, -36.31, -19.72, 0.4951, 4, 185.9, 143.3, 0.48947, 85, -64.59, 601.38, 0.01543, 3, 8, -56.42, 10.32, 0.34353, 4, 165.79, 173.34, 0.63799, 85, -84.7, 631.42, 0.01848, 3, 8, -31.36, 26.61, 0.34376, 16, 13.97, -19.1, 0.64243, 85, -59.65, 647.71, 0.01381], "hull": 56, "edges": [0, 110, 4, 6, 20, 22, 22, 24, 24, 26, 34, 36, 36, 38, 48, 50, 50, 52, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 108, 110, 106, 108, 102, 104, 104, 106, 64, 66, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 56, 58, 58, 60, 60, 62, 62, 64, 42, 44, 44, 46, 46, 48, 52, 54, 54, 56, 26, 28, 18, 20, 16, 18, 80, 82, 82, 84, 122, 124, 12, 14, 10, 12, 6, 8, 8, 10, 130, 142, 142, 132, 134, 144, 144, 136, 14, 16, 0, 2, 2, 4], "width": 162, "height": 712}}, "body": {"body": {"type": "mesh", "uvs": [0.54288, 0.00203, 0.63305, 0.00609, 0.7018, 0.00905, 0.68768, 0.0226, 0.6782, 0.03248, 0.66756, 0.04325, 0.66077, 0.0531, 0.66818, 0.06657, 0.68119, 0.07462, 0.70653, 0.08036, 0.7757, 0.08956, 0.84587, 0.1004, 0.81812, 0.1182, 0.77425, 0.13525, 0.75505, 0.14538, 0.75686, 0.16509, 0.7488, 0.18281, 0.72734, 0.20007, 0.71652, 0.22018, 0.71184, 0.23593, 0.72706, 0.252, 0.75002, 0.26262, 0.78951, 0.27226, 0.84336, 0.28185, 0.89866, 0.29618, 0.95323, 0.31407, 0.98901, 0.32793, 0.99903, 0.34155, 0.99695, 0.36769, 0.96659, 0.39284, 0.91146, 0.41457, 0.8315, 0.43149, 0.75996, 0.43574, 0.73952, 0.45251, 0.7219, 0.47345, 0.69296, 0.49766, 0.63992, 0.53089, 0.53543, 0.57789, 0.4824, 0.60954, 0.46558, 0.62658, 0.45394, 0.64427, 0.4345, 0.66012, 0.40754, 0.67218, 0.38451, 0.68454, 0.37711, 0.70414, 0.36422, 0.73354, 0.33626, 0.75539, 0.26203, 0.79459, 0.22933, 0.81971, 0.21603, 0.8339, 0.20911, 0.84506, 0.21904, 0.85366, 0.25233, 0.86455, 0.267, 0.87926, 0.25438, 0.89437, 0.24065, 0.90632, 0.23279, 0.92573, 0.23268, 0.96833, 0.20859, 0.96965, 0.19958, 0.92352, 0.19468, 0.93237, 0.19664, 0.94995, 0.19415, 0.96673, 0.19121, 0.98237, 0.15323, 0.99232, 0.08257, 0.99893, 0.02721, 0.99916, 0, 0.97378, 0.00106, 0.96743, 0.04647, 0.94902, 0.06077, 0.94128, 0.0693, 0.93129, 0.072, 0.91811, 0.08645, 0.86561, 0.10032, 0.85081, 0.10868, 0.83785, 0.11181, 0.81961, 0.11371, 0.78841, 0.11712, 0.73229, 0.13507, 0.6994, 0.1568, 0.68036, 0.19378, 0.65962, 0.22324, 0.64346, 0.23375, 0.62678, 0.24934, 0.60882, 0.25381, 0.58548, 0.2554, 0.53299, 0.26697, 0.50091, 0.2865, 0.46661, 0.31142, 0.4358, 0.34382, 0.40887, 0.38031, 0.38745, 0.38615, 0.36312, 0.39346, 0.33977, 0.38474, 0.3124, 0.37014, 0.29376, 0.35137, 0.28104, 0.29055, 0.25637, 0.26427, 0.23711, 0.24192, 0.21784, 0.19837, 0.20938, 0.16574, 0.19479, 0.15394, 0.17732, 0.15995, 0.15831, 0.19272, 0.139, 0.25126, 0.11817, 0.24723, 0.09871, 0.24664, 0.07899, 0.29323, 0.07545, 0.35912, 0.0743, 0.43303, 0.07341, 0.45671, 0.07039, 0.47249, 0.06588, 0.48199, 0.05693, 0.48516, 0.04271, 0.48833, 0.02848, 0.49149, 0.01426, 0.49466, 3e-05, 0.31179, 0.10893, 0.38776, 0.11234, 0.4461, 0.12686, 0.45734, 0.14434, 0.50308, 0.13027, 0.58516, 0.12028, 0.68148, 0.12505, 0.43204, 0.16777, 0.37885, 0.21378, 0.31191, 0.22215, 0.46779, 0.21089, 0.52342, 0.22093, 0.59779, 0.22168, 0.6806, 0.21439, 0.5648, 0.09903, 0.68421, 0.09985, 0.75091, 0.10757, 0.47914, 0.09543, 0.37621, 0.08716, 0.30704, 0.09185, 0.51358, 0.07764, 0.51934, 0.06194, 0.52593, 0.04458, 0.5317, 0.02888, 0.53746, 0.01455, 0.58769, 0.08095, 0.59593, 0.06552, 0.60499, 0.04899, 0.61404, 0.03356, 0.62393, 0.01813, 0.33195, 0.23764, 0.36499, 0.25647, 0.39981, 0.27686, 0.42138, 0.29341, 0.43835, 0.3194, 0.44996, 0.3433, 0.48297, 0.36939, 0.54375, 0.39804, 0.61769, 0.42136, 0.69181, 0.436, 0.65169, 0.23031, 0.65615, 0.24734, 0.67401, 0.26526, 0.71062, 0.28229, 0.76777, 0.29663, 0.81299, 0.31264, 0.8596, 0.3304, 0.89995, 0.35326, 0.52729, 0.23235, 0.54426, 0.24944, 0.56909, 0.26569, 0.6007, 0.27966, 0.67019, 0.3225, 0.63962, 0.29913, 0.68444, 0.35237, 0.6921, 0.3808, 0.73088, 0.42445, 0.7064, 0.40757, 0.43051, 0.23312, 0.45305, 0.25431, 0.47881, 0.27406, 0.50779, 0.29273, 0.53033, 0.31536, 0.56467, 0.3448, 0.59687, 0.37102, 0.62958, 0.39551, 0.70957, 0.35058, 0.72503, 0.32616, 0.77655, 0.35177, 0.79115, 0.3759, 0.81326, 0.40255, 0.92082, 0.38078, 0.46892, 0.38485, 0.45947, 0.40742, 0.42184, 0.43448, 0.36738, 0.49085, 0.32082, 0.5905, 0.30597, 0.61295, 0.27798, 0.63033, 0.26281, 0.64985, 0.25235, 0.66831, 0.2968, 0.6771, 0.33823, 0.65884, 0.36348, 0.64262, 0.38167, 0.62369, 0.40491, 0.60239, 0.47361, 0.55496, 0.33871, 0.53843, 0.61158, 0.46353, 0.54597, 0.51321, 0.63236, 0.44422, 0.55089, 0.41911, 0.22203, 0.68759, 0.18698, 0.71478, 0.16719, 0.79236, 0.27427, 0.72415, 0.28342, 0.69495, 0.52401, 0.44337, 0.46525, 0.49421, 0.41865, 0.54234, 0.40829, 0.19184, 0.44104, 0.18957, 0.60579, 0.18273, 0.22649, 0.17677, 0.57941, 0.13954, 0.66841, 0.14266, 0.71148, 0.16374, 0.70437, 0.19057, 0.65462, 0.20682, 0.57199, 0.21119, 0.50017, 0.19989, 0.47702, 0.17573, 0.50036, 0.15201, 0.5849, 0.15791, 0.64499, 0.15954, 0.67213, 0.17348, 0.6639, 0.19095, 0.62811, 0.19967, 0.57329, 0.20035, 0.53882, 0.18865, 0.54321, 0.16973, 0.25236, 0.1294, 0.32682, 0.13164, 0.37952, 0.17167, 0.35694, 0.19518, 0.29084, 0.20805, 0.22141, 0.20581, 0.37158, 0.14745, 0.22074, 0.15151, 0.27691, 0.15515, 0.3044, 0.16859, 0.30022, 0.18555, 0.2667, 0.1965, 0.21332, 0.19482, 0.1807, 0.1823, 0.18422, 0.16357], "triangles": [66, 67, 65, 64, 65, 69, 69, 65, 67, 67, 68, 69, 63, 64, 62, 62, 64, 69, 62, 69, 70, 56, 57, 58, 58, 59, 56, 62, 70, 61, 61, 71, 60, 71, 61, 70, 59, 60, 72, 60, 71, 72, 56, 59, 55, 59, 72, 55, 55, 72, 73, 55, 73, 54, 73, 74, 51, 74, 50, 51, 74, 75, 50, 54, 51, 52, 51, 54, 73, 54, 52, 53, 50, 75, 49, 75, 76, 49, 49, 76, 48, 76, 212, 48, 48, 212, 47, 76, 77, 212, 47, 212, 46, 77, 78, 212, 212, 213, 46, 211, 212, 78, 211, 213, 212, 46, 213, 45, 45, 213, 44, 78, 79, 211, 213, 211, 214, 213, 214, 44, 214, 211, 210, 211, 79, 210, 210, 79, 80, 44, 214, 43, 214, 199, 43, 214, 210, 199, 80, 81, 210, 210, 198, 199, 210, 81, 198, 199, 200, 43, 43, 200, 42, 199, 198, 200, 42, 200, 41, 198, 81, 197, 198, 197, 200, 197, 81, 82, 200, 201, 41, 200, 197, 201, 197, 196, 201, 197, 82, 196, 41, 201, 40, 201, 202, 40, 40, 202, 39, 82, 83, 196, 201, 196, 202, 202, 196, 195, 83, 84, 196, 196, 84, 195, 202, 203, 39, 39, 203, 38, 202, 195, 203, 195, 194, 203, 195, 84, 194, 38, 203, 37, 84, 85, 194, 203, 204, 37, 204, 203, 217, 203, 194, 217, 194, 205, 217, 194, 85, 205, 85, 86, 205, 36, 204, 207, 36, 37, 204, 204, 217, 207, 205, 193, 217, 217, 216, 207, 217, 193, 216, 86, 87, 205, 205, 87, 193, 36, 207, 35, 207, 206, 35, 207, 216, 206, 87, 88, 193, 35, 206, 34, 193, 192, 216, 216, 215, 206, 216, 192, 215, 193, 88, 192, 192, 88, 89, 206, 215, 208, 209, 208, 215, 192, 191, 215, 215, 191, 209, 89, 90, 192, 192, 90, 191, 191, 155, 209, 90, 91, 191, 191, 190, 155, 191, 91, 190, 31, 32, 188, 31, 188, 30, 30, 189, 29, 30, 188, 189, 175, 187, 188, 188, 187, 189, 29, 189, 28, 187, 165, 189, 189, 165, 28, 187, 186, 165, 28, 165, 27, 186, 164, 165, 165, 26, 27, 165, 25, 26, 165, 164, 25, 190, 154, 155, 91, 92, 190, 190, 92, 154, 154, 93, 153, 154, 153, 181, 154, 92, 93, 93, 152, 153, 153, 152, 180, 93, 94, 152, 186, 163, 164, 186, 185, 163, 164, 24, 25, 164, 163, 24, 185, 162, 163, 163, 23, 24, 163, 162, 23, 162, 22, 23, 162, 161, 22, 161, 21, 22, 106, 137, 118, 118, 137, 136, 137, 107, 108, 137, 106, 107, 137, 109, 136, 137, 108, 109, 136, 109, 110, 124, 134, 13, 13, 134, 12, 11, 12, 10, 12, 134, 10, 10, 134, 9, 134, 133, 9, 133, 8, 9, 124, 133, 134, 206, 208, 34, 34, 208, 33, 33, 208, 157, 208, 209, 156, 33, 157, 32, 208, 156, 157, 157, 174, 32, 157, 175, 174, 157, 156, 175, 32, 174, 188, 174, 175, 188, 156, 183, 175, 183, 156, 155, 156, 209, 155, 183, 173, 175, 186, 187, 184, 184, 187, 173, 173, 187, 175, 155, 182, 183, 155, 154, 182, 183, 182, 173, 182, 172, 173, 173, 172, 184, 154, 181, 182, 182, 181, 172, 184, 172, 170, 184, 185, 186, 172, 181, 170, 184, 170, 185, 153, 180, 181, 181, 180, 170, 185, 170, 162, 180, 171, 170, 170, 161, 162, 170, 171, 161, 180, 169, 171, 171, 160, 161, 94, 151, 152, 152, 179, 180, 152, 151, 179, 180, 179, 169, 94, 95, 151, 171, 169, 160, 95, 150, 151, 95, 96, 150, 151, 178, 179, 151, 150, 178, 179, 168, 169, 179, 178, 168, 161, 160, 21, 96, 149, 150, 96, 97, 149, 169, 168, 160, 150, 177, 178, 150, 149, 177, 178, 167, 168, 178, 177, 167, 168, 159, 160, 158, 159, 130, 130, 159, 167, 167, 159, 168, 160, 20, 21, 160, 159, 20, 149, 176, 177, 177, 166, 167, 176, 166, 177, 159, 19, 20, 167, 166, 130, 159, 158, 19, 19, 158, 18, 97, 148, 149, 149, 148, 176, 97, 98, 148, 166, 176, 129, 129, 176, 128, 98, 127, 148, 148, 126, 176, 148, 127, 126, 98, 99, 127, 18, 158, 131, 176, 126, 128, 128, 218, 219, 218, 128, 126, 166, 129, 130, 158, 130, 131, 99, 243, 127, 127, 243, 126, 130, 226, 131, 129, 227, 130, 130, 227, 226, 128, 228, 129, 129, 228, 227, 18, 131, 17, 100, 244, 99, 99, 244, 243, 131, 225, 17, 131, 226, 225, 243, 242, 126, 126, 242, 218, 227, 235, 226, 227, 236, 235, 227, 228, 236, 236, 228, 237, 128, 219, 228, 100, 101, 244, 244, 250, 243, 243, 249, 242, 243, 250, 249, 226, 234, 225, 226, 235, 234, 101, 251, 244, 244, 251, 250, 236, 220, 235, 236, 237, 220, 17, 225, 16, 219, 229, 228, 228, 229, 237, 235, 220, 234, 249, 250, 221, 218, 242, 241, 101, 252, 251, 250, 251, 221, 251, 252, 221, 101, 102, 252, 242, 249, 241, 219, 218, 125, 234, 233, 225, 234, 220, 233, 16, 225, 224, 218, 241, 125, 219, 125, 229, 237, 238, 220, 237, 229, 238, 249, 248, 241, 249, 221, 248, 225, 233, 224, 16, 224, 15, 238, 231, 220, 220, 232, 233, 220, 231, 232, 221, 252, 253, 252, 102, 253, 102, 103, 253, 253, 246, 221, 221, 247, 248, 221, 246, 247, 229, 230, 238, 229, 125, 230, 233, 232, 224, 248, 245, 241, 241, 245, 125, 238, 230, 231, 248, 247, 245, 125, 121, 230, 125, 245, 121, 224, 14, 15, 232, 223, 224, 224, 223, 14, 253, 103, 246, 223, 232, 222, 103, 104, 246, 230, 222, 231, 232, 231, 222, 246, 239, 247, 247, 240, 245, 247, 239, 240, 230, 122, 222, 230, 121, 122, 246, 104, 239, 245, 120, 121, 245, 119, 120, 245, 240, 119, 13, 14, 124, 121, 120, 122, 14, 223, 124, 124, 223, 123, 223, 222, 123, 222, 122, 123, 104, 105, 239, 239, 118, 240, 240, 118, 119, 120, 135, 122, 122, 132, 123, 122, 135, 132, 239, 105, 118, 120, 119, 135, 124, 123, 133, 123, 132, 133, 105, 106, 118, 118, 136, 119, 119, 136, 135, 132, 143, 133, 133, 143, 8, 7, 8, 143, 135, 138, 132, 132, 138, 143, 136, 110, 135, 110, 111, 135, 135, 111, 138, 138, 111, 112, 143, 138, 139, 143, 144, 7, 144, 143, 139, 138, 112, 139, 144, 6, 7, 112, 113, 139, 144, 145, 6, 145, 144, 140, 144, 139, 140, 139, 113, 140, 113, 114, 140, 6, 145, 5, 145, 146, 5, 145, 140, 146, 114, 115, 140, 140, 141, 146, 140, 115, 141, 5, 146, 4, 141, 142, 146, 115, 116, 141, 146, 147, 4, 146, 142, 147, 4, 147, 3, 141, 116, 142, 147, 1, 3, 3, 1, 2, 142, 0, 147, 147, 0, 1, 0, 142, 117, 142, 116, 117], "vertices": [4, 6, 35.17, 34.56, 0.85266, 5, 118.79, 31.93, 0.12697, 9, -463.68, 132.06, 0, 85, 120.81, 482.44, 0.02037, 2, 6, 40.94, -14.32, 0.97917, 85, 119.93, 433.22, 0.02083, 2, 6, 45.56, -51.53, 0.94553, 5, 122.93, -54.69, 0.05447, 3, 6, 22.45, -49.59, 0.82046, 5, 100.01, -51.08, 0.17894, 7, 114.71, 83.84, 0.0006, 3, 6, 5.7, -48.59, 0.57726, 5, 83.39, -48.88, 0.4194, 7, 98.26, 87.09, 0.00334, 4, 6, -12.6, -47.36, 0.28935, 5, 65.22, -46.32, 0.69202, 4, 312.9, -50.35, 0.00373, 7, 80.29, 90.79, 0.0149, 4, 6, -28.95, -47.76, 0.10797, 5, 48.89, -45.55, 0.79256, 4, 296.65, -48.55, 0.04638, 7, 64.04, 92.6, 0.0531, 4, 6, -49.03, -57.06, 0.00859, 5, 28.18, -53.37, 0.57271, 4, 275.49, -55.04, 0.24337, 7, 42.88, 86.1, 0.17533, 4, 6, -59.89, -67.12, 0.00017, 5, 16.62, -62.61, 0.3157, 4, 263.37, -63.54, 0.37353, 7, 30.76, 77.61, 0.31059, 3, 5, 9.93, -77.76, 0.12242, 4, 255.73, -78.23, 0.36771, 7, 23.12, 62.92, 0.50987, 2, 4, 245.3, -117.12, 0.11595, 7, 12.68, 24.02, 0.88405, 2, 9, -260.28, 235.97, 0, 7, -0.35, -15.71, 1, 3, 4, 201.95, -145.27, 0.21029, 7, -30.66, -4.13, 0.78268, 85, -48.55, 312.81, 0.00703, 3, 4, 171.81, -124.89, 0.44778, 7, -60.8, 16.26, 0.53941, 85, -78.69, 333.19, 0.01281, 5, 4, 154.34, -116.46, 0.53828, 9, -207.26, 166.02, 0, 7, -78.27, 24.68, 0.24928, 55, 48.74, -78.85, 0.19689, 85, -96.15, 341.62, 0.01554, 5, 4, 122.8, -121.12, 0.72759, 9, -176.78, 156.68, 0, 7, -109.81, 20.03, 0.05985, 55, 17.2, -83.5, 0.19686, 85, -127.69, 336.96, 0.0157, 6, 5, -149.01, -129.78, 0, 4, 93.83, -120.1, 0.76557, 3, 117.39, -150.29, 0.02282, 9, -151.05, 143.31, 0, 55, -11.77, -82.49, 0.1971, 85, -156.67, 337.98, 0.01452, 6, 5, -178.53, -123.33, 0, 4, 64.77, -111.8, 0.67382, 3, 95.79, -129.15, 0.11896, 9, -128.38, 123.33, 6e-05, 55, -40.83, -74.18, 0.19821, 85, -185.73, 346.28, 0.00895, 4, 5, -211.58, -123.37, 0, 4, 31.79, -109.75, 0.60854, 3, 67.77, -111.64, 0.38428, 9, -99.47, 107.31, 0.00719, 5, 5, -237.08, -125.41, 0, 4, 6.2, -110.17, 0.38462, 3, 45.07, -99.83, 0.56615, 9, -76.19, 96.71, 0.03832, 10, -150.66, -12.35, 0.01091, 5, 5, -261.19, -138.15, 0, 4, -18.66, -121.36, 0.15978, 3, 17.87, -97.82, 0.56686, 9, -48.93, 96.13, 0.12029, 10, -123.4, -12.93, 0.15307, 5, 5, -275.87, -153.43, 0, 4, -34.28, -135.68, 0.06585, 3, -2.68, -102.98, 0.43074, 9, -28.68, 102.35, 0.18405, 10, -103.15, -6.7, 0.31937, 5, 5, -287.41, -177.23, 0, 4, -47.29, -158.7, 0.01994, 3, -25.09, -117.02, 0.2498, 9, -7.03, 117.55, 0.17436, 10, -81.51, 8.49, 0.5559, 6, 5, -297.47, -208.66, 0, 4, -59.33, -189.43, 0.00293, 3, -50.3, -138.31, 0.114, 9, 17.04, 140.13, 0.0843, 10, -57.44, 31.07, 0.7969, 14, -119.37, -24.95, 0.00186, 5, 5, -314.95, -242.23, 0, 3, -82.93, -157.48, 0.02098, 9, 48.62, 160.98, 0.00408, 10, -25.86, 51.93, 0.85594, 14, -100.15, 7.65, 0.119, 2, 10, 11.03, 70.56, 0.6037, 14, -75.19, 40.58, 0.3963, 2, 10, 38.49, 81.66, 0.3806, 14, -55.39, 62.61, 0.6194, 2, 10, 61.09, 79.69, 0.24661, 14, -34.22, 70.76, 0.75339, 2, 10, 100.74, 65, 0.05521, 14, 7.85, 74.98, 0.94479, 1, 14, 50.27, 63.82, 1, 1, 14, 88.87, 38.66, 1, 3, 5, -536.7, -245.46, 0, 9, 244.02, 56.08, 0.06323, 14, 121.47, -0.81, 0.93677, 3, 9, 238.05, 17.23, 0.76783, 14, 133.16, -38.33, 0.21926, 87, -272.12, -140.5, 0.01292, 3, 9, 260.17, -1.98, 0.73998, 14, 161.47, -45.88, 0.08857, 12, 51.63, 139.42, 0.17144, 2, 9, 289.15, -21.91, 0.54237, 12, 86.95, 139.29, 0.45763, 2, 9, 321.16, -49.35, 0.34539, 12, 129.03, 134.67, 0.65461, 2, 9, 362.78, -93.83, 0.16637, 12, 188.73, 121.39, 0.83363, 2, 9, 416.51, -171.84, 0.01628, 12, 277.44, 87.23, 0.98372, 3, 9, 455.71, -215.49, 0.00067, 12, 334.66, 73.27, 0.98009, 13, -70.47, 74.72, 0.01924, 3, 9, 478.87, -232.98, 2e-05, 12, 363.77, 71.87, 0.8845, 13, -41.65, 72.74, 0.11549, 2, 12, 393.13, 73.45, 0.6539, 13, -12.53, 73.72, 0.3461, 2, 12, 420.75, 70.17, 0.38225, 13, 14.77, 69.88, 0.61775, 2, 12, 443.53, 61.33, 0.15855, 13, 37.17, 60.58, 0.84145, 2, 12, 466.21, 54.67, 0.03065, 13, 59.51, 53.47, 0.96935, 2, 12, 497.96, 59.28, 0.00056, 13, 91.06, 57.44, 0.99944, 1, 13, 138.61, 62.46, 1, 1, 13, 176.42, 56.57, 1, 1, 13, 247.43, 33.36, 1, 1, 13, 290.95, 26.29, 1, 2, 13, 314.84, 25, 0.90739, 44, 200.02, -28.1, 0.09261, 2, 13, 333.17, 25.85, 0.49775, 44, 181.69, -26.18, 0.50225, 2, 13, 345.25, 34.5, 0.12751, 44, 168.4, -32.92, 0.87249, 2, 13, 357.79, 56.31, 0.00588, 44, 152.68, -52.6, 0.99412, 1, 44, 129.8, -62.89, 1, 1, 44, 104.81, -58.54, 1, 1, 44, 84.83, -53.09, 1, 1, 44, 53.18, -52, 1, 1, 44, -15.36, -58.84, 1, 1, 44, -18.79, -46.09, 1, 1, 44, 54.94, -33.76, 1, 1, 44, 40.43, -32.56, 1, 2, 44, 12.26, -36.46, 0.84443, 43, 112.87, -24.62, 0.15557, 2, 44, -14.87, -37.84, 0.3705, 43, 90.49, -40.02, 0.6295, 2, 44, -40.19, -38.79, 0.12604, 43, 69.43, -54.11, 0.87396, 2, 44, -58.27, -19.96, 0.02745, 43, 44.17, -47.56, 0.97255, 1, 43, 12.46, -23.67, 1, 2, 44, -76.09, 46.77, 0, 43, -6.01, -0.09, 1, 2, 5, -1479.72, 40.92, 0, 43, 17.66, 36.51, 1, 3, 5, -1469.51, 42.19, 0, 44, -26.46, 65.98, 0.0064, 43, 26.17, 42.3, 0.9936, 3, 5, -1435.84, 23.32, 0, 44, 5.62, 44.52, 0.31593, 43, 64.74, 40.85, 0.68407, 3, 5, -1422.14, 17.94, 0, 44, 18.85, 38.08, 0.68091, 43, 79.38, 42.31, 0.31909, 3, 5, -1405.43, 16.27, 0, 44, 35.37, 35.1, 0.92746, 43, 95.01, 48.44, 0.07254, 3, 5, -1384.2, 18.63, 0, 44, 56.72, 35.78, 0.99961, 43, 112.83, 60.22, 0.00039, 2, 5, -1299.27, 26.07, 0, 44, 141.98, 36.51, 1, 3, 5, -1274.39, 22.95, 0, 13, 356.65, -28.87, 0.1416, 44, 166.54, 31.45, 0.8584, 3, 5, -1252.96, 22.23, 0, 13, 335.33, -29.68, 0.55593, 44, 187.85, 29.04, 0.44407, 3, 5, -1223.63, 25.82, 0, 13, 306.47, -35.35, 0.96595, 44, 217.36, 30.31, 0.03405, 2, 5, -1173.81, 33.8, 0, 13, 257.58, -46.85, 1, 2, 5, -1084.19, 48.16, 0, 13, 169.63, -67.55, 1, 2, 5, -1030.13, 48.07, 0, 13, 115.97, -71.31, 1, 2, 5, -997.73, 42, 0, 13, 83.37, -67.56, 1, 3, 5, -961.17, 28.29, 0, 12, 454.93, -55.54, 0.01939, 13, 46.11, -56.49, 0.98061, 3, 5, -932.62, 17.26, 0, 12, 425.37, -47.16, 0.22595, 13, 16.98, -47.52, 0.77405, 3, 5, -905.06, 16.47, 0, 12, 397.74, -48.9, 0.65699, 13, -10.43, -48.69, 0.34301, 3, 5, -874.98, 13.35, 0, 12, 367.37, -48.53, 0.95399, 13, -40.51, -47.72, 0.04601, 3, 5, -837.42, 17.69, 0, 12, 330.22, -56.29, 0.99997, 13, -77.48, -54.73, 3e-05, 2, 5, -753.75, 31.97, 0, 12, 247.87, -78.16, 1, 2, 5, -701.59, 35.06, 0, 12, 195.99, -86, 1, 3, 5, -645.13, 34.54, 0, 11, 121.79, -161.31, 0.00174, 12, 139.49, -90.64, 0.99826, 3, 5, -593.71, 30.15, 0, 11, 78.97, -132.49, 0.03431, 12, 87.67, -90.97, 0.96569, 3, 5, -547.74, 20.66, 0, 11, 43.4, -101.86, 0.15118, 12, 40.83, -85.72, 0.84882, 4, 5, -510.14, 7.41, 0, 3, -115.81, 157.7, 0.00187, 11, 16.98, -72.01, 0.39378, 12, 2.02, -75.96, 0.60435, 3, 3, -80.46, 140.15, 0.04034, 11, -19.25, -56.34, 0.73163, 12, -36.9, -83.43, 0.22803, 3, 3, -46.87, 122.45, 0.1998, 9, 27.26, -120.46, 0.06344, 11, -53.72, -40.43, 0.73676, 4, 4, -137.09, 51.3, 0.01118, 3, -4.03, 110.4, 0.49504, 9, -16.16, -110.66, 0.10538, 11, -97.13, -30.63, 0.3884, 4, 4, -108.06, 62.63, 0.07173, 3, 26.9, 106.54, 0.69309, 9, -47.24, -108.43, 0.05448, 11, -128.22, -28.4, 0.18069, 4, 4, -88.81, 75.1, 0.18304, 3, 49.76, 108.33, 0.72838, 9, -69.97, -111.41, 0.01893, 11, -150.95, -31.38, 0.06965, 2, 4, -52.99, 112.39, 0.50515, 3, 99.02, 124.07, 0.49485, 3, 4, -23.69, 130.12, 0.68784, 3, 133.22, 125.69, 0.31216, 9, -152.41, -133.12, 0, 5, 4, 5.85, 145.73, 0.67553, 3, 166.64, 125.36, 0.11672, 9, -185.8, -134.53, 0, 58, -91.72, 25.3, 0.19806, 85, -244.65, 603.81, 0.00969, 6, 4, 16.72, 170.72, 0.74516, 3, 188.1, 142.15, 0.0587, 9, -206.35, -152.43, 0, 58, -80.84, 50.29, 0.08932, 59, -68.8, 28.96, 0.09924, 85, -233.77, 628.8, 0.00758, 5, 4, 38.11, 190.97, 0.75903, 3, 216.55, 149.77, 0.03048, 9, -234.37, -161.53, 0, 59, -47.42, 49.22, 0.19738, 85, -212.39, 649.06, 0.01312, 5, 4, 65.44, 200.59, 0.77288, 3, 245.15, 145.21, 0.0144, 9, -263.17, -158.47, 0, 59, -20.09, 58.83, 0.19682, 85, -185.06, 658.67, 0.01591, 4, 4, 96.35, 200.91, 0.78748, 9, -291.22, -145.49, 0, 59, 10.82, 59.16, 0.19687, 85, -154.15, 658.99, 0.01564, 6, 4, 129.4, 186.91, 0.72777, 9, -315.06, -118.64, 0, 7, -103.21, 328.05, 0.01702, 8, -92.81, 23.89, 0.04275, 59, 43.88, 45.16, 0.19689, 85, -121.09, 644.99, 0.01556, 5, 4, 166.53, 159.35, 0.54828, 9, -336.75, -77.81, 0, 8, -55.68, -3.68, 0.2391, 58, 68.96, 38.92, 0.19684, 85, -83.97, 617.43, 0.01578, 4, 4, 197.53, 165.15, 0.2253, 9, -367.23, -69.73, 0, 8, -24.68, 2.13, 0.76466, 85, -52.97, 623.23, 0.01004, 1, 8, 6.95, 6.13, 1, 3, 4, 237.76, 144.78, 0.06434, 9, -394.82, -34.06, 0, 8, 15.55, -18.24, 0.93566, 3, 4, 243.74, 109.59, 0.31138, 9, -385.09, 0.29, 0, 8, 21.52, -53.43, 0.68862, 4, 5, -5.38, 69.84, 0.09176, 4, 249.79, 70.04, 0.57825, 9, -373.57, 38.61, 0, 8, 27.57, -92.98, 0.32998, 4, 5, 1.71, 58.1, 0.26871, 4, 256.12, 57.88, 0.53737, 9, -374.07, 52.31, 0, 8, 33.91, -105.14, 0.19392, 5, 6, -74.31, 45.75, 0.00095, 5, 10.41, 51, 0.46466, 4, 264.35, 50.24, 0.408, 9, -378.22, 62.74, 0, 8, 42.14, -112.78, 0.1264, 5, 6, -59.02, 44.37, 0.02025, 5, 25.56, 48.52, 0.71609, 4, 279.32, 46.81, 0.20191, 9, -390.26, 72.27, 0, 8, 57.11, -116.21, 0.06174, 5, 6, -36.31, 48.44, 0.15566, 5, 48.5, 50.94, 0.8107, 4, 302.36, 47.77, 0.02154, 9, -411.48, 81.3, 0, 8, 80.15, -115.25, 0.0121, 4, 6, -13.61, 52.5, 0.36526, 5, 71.44, 53.35, 0.63373, 9, -432.7, 90.34, 0, 8, 103.2, -114.3, 0.00101, 3, 6, 9.09, 56.57, 0.66628, 5, 94.38, 55.76, 0.33372, 9, -453.92, 99.37, 0, 3, 6, 31.8, 60.63, 0.79814, 5, 117.32, 58.17, 0.20186, 9, -475.14, 108.41, 0, 5, 4, 185.15, 128.55, 0.52539, 9, -340.33, -42, 0, 8, -37.06, -34.47, 0.25264, 58, 87.58, 8.12, 0.19451, 85, -65.35, 586.63, 0.02746, 5, 4, 184.43, 87.09, 0.67042, 9, -321.87, -4.86, 0, 8, -37.78, -75.94, 0.09822, 58, 86.87, -33.34, 0.19216, 85, -66.06, 545.17, 0.03919, 4, 4, 164.75, 53.02, 0.768, 9, -289.47, 17.45, 0, 58, 67.19, -67.41, 0.192, 85, -85.74, 511.1, 0.04, 5, 4, 137.39, 43.71, 0.7776, 9, -260.76, 14.1, 0, 58, 39.82, -76.71, 0.096, 55, 31.79, 81.33, 0.0864, 85, -113.11, 501.79, 0.04, 4, 4, 162.85, 21.76, 0.768, 9, -274.32, 44.85, 0, 55, 57.25, 59.38, 0.192, 85, -87.65, 479.85, 0.04, 3, 4, 184.02, -20.47, 0.768, 55, 78.42, 17.14, 0.192, 85, -66.47, 437.61, 0.04, 4, 4, 182.4, -73.13, 0.54979, 7, -50.22, 68.02, 0.22665, 55, 76.8, -35.51, 0.19411, 85, -68.1, 384.95, 0.02945, 5, 4, 98.17, 52.93, 0.7776, 9, -229.3, -11.07, 0, 58, 0.6, -67.5, 0.096, 55, -7.43, 90.54, 0.0864, 85, -152.33, 511.01, 0.04, 5, 4, 20.95, 72.91, 0.69927, 3, 145.23, 54.13, 0.06873, 9, -168.15, -62.29, 0, 58, -76.62, -47.52, 0.192, 85, -229.55, 530.99, 0.04, 5, 4, 3.31, 107.31, 0.60214, 3, 146.1, 92.79, 0.16897, 9, -167, -100.93, 0, 58, -94.26, -13.11, 0.19278, 85, -247.19, 565.39, 0.03611, 4, 4, 31.16, 25.65, 0.768, 9, -157.07, -15.23, 0, 55, -74.44, 63.27, 0.192, 85, -219.34, 483.74, 0.04, 4, 5, -231.38, -20.79, 0, 4, 18.51, -6.12, 0.768, 55, -87.09, 31.5, 0.192, 85, -231.98, 451.96, 0.04, 5, 5, -225.41, -60.6, 0, 4, 21.95, -46.22, 0.51989, 3, 89.37, -51.1, 0.24811, 55, -83.65, -8.61, 0.192, 85, -228.54, 411.86, 0.04, 6, 5, -205.83, -102.58, 0, 4, 38.84, -89.36, 0.5447, 3, 83.68, -97.07, 0.23561, 9, -114.6, 91.93, 0.00213, 55, -66.76, -51.74, 0.19561, 85, -211.66, 368.72, 0.02195, 3, 4, 216.89, -5.56, 0.95822, 7, -15.73, 135.58, 0.01318, 85, -33.61, 452.52, 0.0286, 3, 4, 223.03, -69.88, 0.51235, 7, -9.58, 71.26, 0.4666, 85, -27.47, 388.2, 0.02105, 3, 4, 214.81, -107.17, 0.28619, 7, -17.8, 33.98, 0.70037, 85, -35.68, 350.91, 0.01343, 4, 4, 217.31, 41.14, 0.96618, 9, -331.83, 50.75, 0, 8, -4.9, -121.88, 0.00555, 85, -33.18, 499.22, 0.02827, 4, 4, 224.15, 98, 0.46443, 9, -362.43, 2.34, 0, 8, 1.94, -65.02, 0.51773, 85, -26.35, 556.08, 0.01784, 4, 4, 212.3, 134.3, 0.27916, 9, -367.32, -35.53, 0, 8, -9.91, -28.72, 0.7035, 85, -38.2, 592.38, 0.01735, 5, 5, -4.35, 25.74, 0.27717, 4, 248.03, 25.96, 0.66278, 9, -353.05, 77.65, 0, 8, 25.82, -137.06, 0.03916, 85, -2.47, 484.04, 0.02089, 6, 6, -61.83, 22.78, 0.00602, 5, 21.2, 27.2, 0.72194, 4, 273.61, 25.8, 0.20374, 9, -376.08, 88.79, 0, 8, 51.4, -137.22, 0.04797, 85, 23.12, 483.88, 0.02034, 6, 6, -33.76, 26.32, 0.12139, 5, 49.45, 28.69, 0.8405, 4, 301.91, 25.51, 0.00744, 9, -401.5, 101.21, 0, 8, 79.69, -137.51, 0.00939, 85, 51.41, 483.59, 0.02128, 5, 6, -8.39, 29.62, 0.35986, 5, 74.99, 30.15, 0.61882, 9, -424.54, 112.34, 0, 8, 105.28, -137.68, 0.00029, 85, 76.99, 483.43, 0.02103, 4, 6, 14.83, 32.36, 0.73958, 5, 98.34, 31.21, 0.24006, 9, -445.46, 122.76, 0, 85, 100.36, 483.01, 0.02036, 4, 5, -2.46, -14.67, 0.23794, 4, 247.36, -14.48, 0.69726, 7, 14.74, 126.66, 0.04419, 85, -3.14, 443.6, 0.02061, 4, 5, 22.88, -14.6, 0.80942, 4, 272.65, -16.02, 0.10706, 7, 40.04, 125.12, 0.06324, 85, 22.16, 442.06, 0.02028, 4, 6, -30.02, -16.88, 0.06897, 5, 50.06, -14.66, 0.88829, 7, 67.16, 123.35, 0.02229, 85, 49.27, 440.28, 0.02045, 4, 6, -4.63, -15.42, 0.30549, 5, 75.48, -15.04, 0.67151, 7, 92.5, 121.36, 0.00206, 85, 74.62, 438.3, 0.02095, 4, 6, 20.86, -14.38, 0.93288, 5, 100.98, -15.85, 0.04651, 7, 117.9, 118.94, 0.00011, 85, 100.02, 435.88, 0.02051, 3, 4, -20.32, 93.65, 0.59553, 3, 118.82, 92.02, 0.36955, 85, -270.82, 551.73, 0.03492, 4, 4, -48.48, 72.37, 0.36647, 3, 83.92, 86.73, 0.60165, 9, -105.22, -91.63, 0.00142, 85, -298.98, 530.46, 0.03046, 6, 4, -79.06, 49.85, 0.12799, 3, 46.3, 81.48, 0.7711, 9, -67.93, -84.42, 0.02477, 11, -148.91, -4.39, 0.05187, 85, -329.56, 507.93, 0.02221, 87, -466.96, 116.4, 0.00205, 6, 4, -104.29, 35.16, 0.03481, 3, 17.13, 80.59, 0.68548, 9, -38.84, -82, 0.08469, 11, -119.82, -1.97, 0.17068, 85, -354.79, 493.24, 0.01502, 87, -455.29, 89.64, 0.00933, 6, 4, -144.97, 21.18, 0.00134, 3, -25.3, 87.67, 0.36662, 9, 3.9, -86.85, 0.16757, 11, -77.08, -6.82, 0.4423, 85, -395.47, 479.27, 0.00443, 87, -446.11, 47.61, 0.01775, 4, 3, -63.52, 96.2, 0.14036, 9, 42.51, -93.36, 0.10491, 11, -38.47, -13.33, 0.72773, 87, -439.83, 8.97, 0.027, 4, 5, -471.51, -42.03, 0, 3, -109.32, 95.29, 0.00031, 11, 7.23, -10.03, 0.96592, 87, -421.98, -33.22, 0.03377, 3, 9, 142.66, -73.85, 0.68124, 12, -5.16, 13.82, 0.29595, 87, -389.09, -79.55, 0.02281, 3, 9, 191.24, -48.14, 0.8075, 12, 20.59, 62.45, 0.17384, 87, -349.09, -117.25, 0.01866, 4, 9, 226.57, -17.81, 0.89547, 14, 138.23, -74.86, 0.00424, 12, 32.73, 107.41, 0.08867, 87, -308.99, -140.92, 0.01163, 6, 5, -233.94, -91.77, 0, 4, 11.47, -76.8, 0.44961, 3, 65.59, -72.99, 0.50744, 9, -95.28, 68.83, 0.01065, 85, -239.03, 381.28, 0.02581, 87, -330.7, 191.67, 0.00649, 6, 5, -260.61, -99.06, 0, 4, -15.61, -82.38, 0.23449, 3, 39.13, -65.01, 0.6877, 9, -68.43, 62.24, 0.04636, 85, -266.1, 375.7, 0.01939, 87, -328.28, 164.14, 0.01207, 7, 5, -287.41, -113.73, 0, 4, -43.28, -95.33, 0.08598, 3, 8.62, -63.21, 0.62977, 9, -37.88, 62.05, 0.16234, 10, -112.35, -47.01, 0.09067, 85, -293.78, 362.75, 0.012, 87, -318.62, 135.15, 0.01924, 7, 5, -310.97, -138.12, 0, 4, -68.34, -118.19, 0.02456, 3, -24.3, -71.38, 0.34437, 9, -5.43, 71.92, 0.32042, 10, -79.9, -37.13, 0.2813, 85, -318.84, 339.89, 0.00289, 87, -298.82, 107.62, 0.02646, 6, 5, -328.28, -172.68, 0, 4, -87.8, -151.58, 0.00425, 3, -57.31, -91.47, 0.1324, 9, 26.49, 93.72, 0.25046, 10, -47.99, -15.34, 0.58641, 87, -267.9, 84.43, 0.02649, 6, 5, -349.39, -201.36, 0, 4, -110.68, -178.87, 1e-05, 3, -90.43, -104.57, 0.03443, 9, 58.87, 108.54, 0.08065, 10, -15.6, -0.52, 0.85943, 87, -243.43, 58.55, 0.02548, 3, 10, 19.71, 14.09, 0.64029, 14, -42.6, -6.34, 0.33518, 87, -218.22, 29.82, 0.02453, 3, 10, 61.73, 22.85, 0.10353, 14, -8.69, 19.98, 0.87752, 87, -196.39, -7.13, 0.01896, 4, 5, -249.18, -26.14, 0, 4, 0.4, -10.33, 0.42059, 3, 87.52, -9.28, 0.53941, 85, -250.09, 447.75, 0.04, 5, 5, -274.73, -40.1, 0, 4, -25.98, -22.65, 0.03068, 3, 58.45, -7.54, 0.9299, 85, -276.47, 435.43, 0.0333, 87, -388.82, 160.74, 0.00612, 6, 5, -298.2, -58, 0, 4, -50.53, -39.03, 0.01295, 3, 29.07, -10.26, 0.94388, 9, -55.52, 8.09, 0.00449, 85, -301.02, 419.05, 0.02554, 87, -375.38, 134.47, 0.01314, 6, 5, -317.38, -78.85, 0, 4, -70.99, -58.63, 0.00852, 3, 1.74, -17.74, 0.80063, 9, -28.63, 17, 0.15241, 85, -321.48, 399.45, 0.01816, 87, -358.28, 111.88, 0.02028, 5, 5, -378.84, -128.18, 0, 3, -76.53, -26.92, 0.01065, 9, 49.06, 30.26, 0.8629, 10, -25.41, -78.79, 0.08644, 87, -320.69, 42.61, 0.04, 7, 5, -344.61, -105.18, 0, 4, -99.83, -83.18, 0.00164, 3, -35.31, -25.6, 0.15739, 9, 7.96, 26.79, 0.78417, 10, -66.51, -82.27, 0.01833, 85, -350.32, 374.9, 0.00845, 87, -337.23, 80.39, 0.03002, 4, 5, -424.99, -144.38, 0, 9, 97.27, 22, 0.93837, 10, 22.8, -87.06, 0.02225, 87, -312.98, -5.69, 0.03938, 2, 9, 142.13, 11.1, 0.96672, 87, -308.84, -51.67, 0.03328, 3, 9, 215.71, 8.22, 0.90374, 14, 117.05, -56.24, 0.07731, 87, -287.86, -122.25, 0.01895, 3, 5, -510.71, -171.97, 0, 9, 185.6, 4.48, 0.97257, 87, -301.1, -94.96, 0.02743, 3, 4, -6.89, 41.53, 0.6061, 3, 105.8, 39.8, 0.3539, 85, -257.39, 499.61, 0.04, 4, 4, -39.51, 25.46, 0.1611, 3, 69.46, 41.2, 0.80482, 85, -290.01, 483.54, 0.03375, 87, -438.16, 152.86, 0.00033, 5, 4, -69.62, 7.92, 0.01282, 3, 34.64, 40.12, 0.92494, 9, -58.44, -42.51, 0.02784, 85, -320.12, 466, 0.02505, 87, -424.23, 120.93, 0.00935, 5, 3, 0.78, 36.78, 0.74134, 9, -24.81, -37.4, 0.20342, 11, -105.79, 42.63, 0.02043, 85, -348.3, 446.94, 0.01675, 87, -408.55, 90.73, 0.01807, 5, 3, -37.71, 39.04, 0.20515, 9, 13.75, -37.64, 0.61538, 11, -67.23, 42.39, 0.14435, 85, -383.22, 430.6, 0.00676, 87, -396.36, 54.15, 0.02836, 4, 3, -88.82, 39.48, 0.01318, 9, 64.81, -35.4, 0.68233, 11, -16.17, 44.63, 0.26449, 87, -377.77, 6.54, 0.04, 4, 5, -463.11, -103.14, 0, 9, 110.55, -32.57, 0.83334, 11, 29.57, 47.46, 0.12835, 87, -360.35, -35.85, 0.03831, 3, 5, -498.93, -127.61, 0, 9, 153.75, -28.58, 0.96833, 87, -342.66, -75.46, 0.03167, 5, 5, -419.73, -157.24, 0, 9, 98.92, 35.8, 0.76435, 10, 24.45, -73.26, 0.09246, 14, 0.01, -82.74, 0.10392, 87, -299.38, -2.81, 0.03927, 6, 5, -379.38, -158.43, 0, 3, -93.05, -52.27, 0.01499, 9, 64.23, 56.44, 0.5848, 10, -10.24, -52.62, 0.32664, 14, -40.22, -79.42, 0.03357, 87, -291.02, 36.68, 0.04, 5, 5, -415.16, -193.24, 0, 9, 112.41, 69.49, 0.35381, 10, 37.94, -39.57, 0.22735, 14, -2.66, -46.55, 0.38113, 87, -263.15, -4.72, 0.03771, 4, 9, 151.9, 64.39, 0.30054, 10, 77.43, -44.67, 0.0203, 14, 35.06, -33.79, 0.64662, 87, -255.25, -43.75, 0.03254, 4, 5, -492.42, -227.41, 0, 9, 196.54, 61.82, 0.142, 14, 76.29, -16.49, 0.83128, 87, -243.29, -86.84, 0.02672, 2, 14, 34.05, 36.79, 0.98312, 87, -185.1, -51.65, 0.01688, 4, 5, -497.45, -39.01, 0, 11, 28.44, -25.28, 0.55715, 12, -14.92, -30.89, 0.41201, 87, -429.58, -58.22, 0.03084, 4, 5, -534.28, -40.48, 0, 11, 61.35, -41.88, 0.1202, 12, 21.76, -26.06, 0.85092, 87, -434.69, -94.72, 0.02889, 4, 5, -580.96, -28.24, 0, 11, 96.2, -75.25, 0.024, 12, 69.57, -33.98, 0.95117, 87, -455.05, -138.47, 0.02482, 3, 5, -675.9, -15.5, 0, 12, 165.67, -38, 0.97827, 87, -484.51, -229.62, 0.02173, 3, 5, -838.95, -19.42, 0, 12, 328.34, -19.19, 0.98037, 87, -509.7, -390.76, 0.01963, 3, 5, -876.09, -17.99, 0, 12, 365.61, -17.23, 0.98062, 87, -517.73, -427.05, 0.01938, 4, 5, -906.45, -8.1, 0, 12, 396.87, -24.31, 0.68482, 13, -10.79, -24.09, 0.29746, 87, -532.87, -455.16, 0.01772, 4, 5, -938.97, -5.64, 0, 12, 429.62, -23.78, 0.12493, 13, 21.67, -24.22, 0.85799, 87, -541.08, -486.73, 0.01709, 3, 5, -969.35, -5.4, 0, 13, 51.84, -22.31, 0.98405, 87, -546.74, -516.57, 0.01595, 2, 13, 59.61, 4.51, 0.98419, 87, -522.69, -530.78, 0.01581, 3, 12, 432.74, 19.43, 0.03854, 13, 25.63, 18.91, 0.94408, 87, -500.28, -501.27, 0.01738, 3, 12, 403.68, 25.58, 0.55024, 13, -3.04, 25.65, 0.43262, 87, -486.62, -475.03, 0.01714, 3, 12, 371.43, 26.88, 0.9456, 13, -34.97, 27.6, 0.03685, 87, -476.78, -444.42, 0.01755, 4, 9, 431.26, -251.45, 5e-05, 12, 334.73, 29.79, 0.97903, 13, -71.27, 31.24, 0.00212, 87, -464.21, -409.99, 0.01881, 3, 9, 370.64, -191.55, 0.01389, 12, 250.55, 45.09, 0.96641, 87, -427.04, -333.29, 0.0197, 3, 5, -754.37, -13.95, 0, 12, 244.27, -32.37, 0.97858, 87, -500.02, -306.56, 0.02142, 3, 9, 254.72, -73.24, 0.39166, 12, 87.45, 77.49, 0.58874, 87, -352.4, -185.44, 0.0196, 3, 9, 319.34, -132.73, 0.10734, 12, 174.71, 64.77, 0.87288, 87, -387.89, -265.78, 0.01977, 3, 9, 228.79, -52.54, 0.59451, 12, 54.22, 79.98, 0.3856, 87, -341.15, -154.22, 0.0199, 3, 9, 176.15, -81.18, 0.32905, 12, 26.77, 26.65, 0.64156, 87, -385.23, -113.62, 0.02939, 3, 5, -1002.94, 5.19, 0, 13, 85.94, -30.47, 0.98852, 87, -563.14, -547.74, 0.01148, 3, 5, -1049.59, 16.01, 0, 13, 133.01, -37.95, 0.99327, 87, -582.1, -591.71, 0.00673, 2, 5, -1174.94, 4.19, 0, 13, 256.61, -17.24, 1, 2, 13, 135.98, 11.55, 0.99308, 87, -534.88, -606.87, 0.00692, 2, 13, 89.23, 4.65, 0.98812, 87, -529.93, -559.65, 0.01188, 3, 9, 208.6, -107.58, 0.09787, 12, 68.63, 23.13, 0.87123, 87, -399.77, -152.85, 0.0309, 3, 9, 276.18, -164.17, 0.01776, 12, 156.71, 14.48, 0.95325, 87, -431.56, -235.06, 0.02899, 3, 9, 341.73, -213.12, 0.0026, 12, 238.78, 10.99, 0.97194, 87, -456.77, -312.88, 0.02546, 4, 4, 58.02, 61.19, 0.768, 9, -196.59, -35.78, 0, 58, -39.55, -59.24, 0.192, 85, -192.48, 519.27, 0.04, 4, 4, 63.73, 44.01, 0.768, 9, -194.37, -17.82, 0, 55, -41.87, 81.63, 0.192, 85, -186.77, 502.09, 0.04, 4, 5, -162.67, -53.63, 0, 4, 85.01, -43.24, 0.6987, 57, -5.32, 1.47, 0.2329, 85, -165.49, 414.85, 0.0684, 4, 4, 70.85, 161.7, 0.70645, 9, -251.36, -121.03, 0, 60, -4.92, 6.79, 0.23548, 85, -179.65, 619.78, 0.05806, 3, 4, 152.73, -20.99, 0.756, 56, 54.44, 18.89, 0.189, 85, -97.77, 437.09, 0.055, 4, 4, 153.29, -69.39, 0.68364, 7, -79.32, 71.75, 0.0756, 56, 55, -29.51, 0.18981, 85, -97.21, 388.69, 0.05094, 5, 4, 122.13, -96.48, 0.74806, 9, -186.75, 134.14, 0, 7, -110.49, 44.66, 0.01706, 56, 23.84, -56.6, 0.19128, 85, -128.37, 361.6, 0.04361, 5, 5, -165.64, -108.37, 0, 4, 78.59, -97.68, 0.71582, 3, 114.66, -123.32, 0.0486, 56, -19.71, -57.8, 0.1911, 85, -171.91, 360.4, 0.04448, 6, 5, -196.29, -86.56, 0, 4, 49.37, -73.98, 0.62203, 3, 100.26, -88.57, 0.13806, 9, -130.72, 82.57, 0.00021, 56, -48.92, -34.1, 0.19007, 85, -201.12, 384.1, 0.04964, 5, 5, -211.2, -43.84, 0, 4, 37.19, -30.4, 0.70819, 3, 110.31, -44.44, 0.04781, 56, -61.1, 9.48, 0.189, 85, -213.3, 427.69, 0.055, 4, 4, 50.84, 10.31, 0.756, 9, -168.26, 7.08, 0, 56, -47.45, 50.19, 0.189, 85, -199.65, 468.39, 0.055, 4, 4, 88.21, 27.27, 0.756, 9, -209.28, 7.82, 0, 56, -10.08, 67.15, 0.189, 85, -162.29, 485.35, 0.055, 4, 4, 127.76, 19.16, 0.756, 9, -241.51, 32.13, 0, 56, 29.46, 59.04, 0.189, 85, -122.74, 477.24, 0.055, 3, 4, 123.57, -27.37, 0.748, 57, 33.25, 17.33, 0.187, 85, -126.92, 430.71, 0.065, 3, 4, 124.71, -59.97, 0.7501, 57, 34.38, -15.26, 0.18752, 85, -125.79, 398.11, 0.06238, 3, 4, 104.03, -77.16, 0.75132, 57, 13.7, -32.45, 0.18783, 85, -146.47, 380.92, 0.06085, 5, 5, -170.14, -86.93, 0, 4, 75.45, -76, 0.72595, 3, 122.23, -102.76, 0.02586, 57, -14.88, -31.3, 0.18795, 85, -175.05, 382.08, 0.06024, 5, 5, -187.47, -70.39, 0, 4, 59.2, -58.4, 0.7019, 3, 116.32, -79.55, 0.0479, 57, -31.13, -13.69, 0.18745, 85, -191.3, 399.68, 0.06275, 4, 5, -193.83, -41.4, 0, 4, 54.68, -29.07, 0.748, 57, -35.64, 15.64, 0.187, 85, -195.81, 429.01, 0.065, 4, 5, -178.54, -19.68, 0, 4, 71.31, -8.36, 0.748, 57, -19.01, 36.35, 0.187, 85, -179.18, 449.73, 0.065, 3, 4, 101.97, -7.18, 0.748, 57, 11.65, 37.53, 0.187, 85, -148.52, 450.9, 0.065, 5, 4, 148.56, 156.66, 0.71268, 9, -319.36, -83.1, 0, 8, -73.65, -6.37, 0.0576, 59, 63.03, 14.9, 0.19257, 85, -101.94, 614.74, 0.03715, 4, 4, 149.62, 116.23, 0.75998, 9, -302.95, -46.13, 0, 59, 64.09, -25.53, 0.19, 85, -100.88, 574.31, 0.05002, 4, 4, 88.63, 80.42, 0.75679, 9, -232.5, -40, 0, 59, 3.1, -61.33, 0.1892, 85, -161.87, 538.5, 0.05401, 4, 4, 49.45, 88.16, 0.75682, 9, -200.44, -63.82, 0, 59, -36.07, -53.59, 0.18921, 85, -201.04, 546.24, 0.05397, 5, 4, 24.64, 121.27, 0.69218, 3, 171.51, 94.9, 0.07032, 9, -192.26, -104.37, 0, 59, -60.89, -20.48, 0.19063, 85, -225.86, 579.35, 0.04687, 5, 4, 23.89, 159, 0.7106, 3, 188.82, 128.43, 0.06335, 9, -207.79, -138.77, 0, 59, -61.63, 17.25, 0.19349, 85, -226.6, 617.08, 0.03256, 4, 4, 127.02, 89.22, 0.75774, 9, -270.94, -31.45, 0, 59, 41.49, -52.54, 0.18943, 85, -123.48, 547.3, 0.05283, 4, 4, 111.07, 169.51, 0.75963, 9, -291.03, -110.81, 0, 60, 35.29, 14.61, 0.18991, 85, -139.43, 627.6, 0.05046, 4, 4, 108.74, 138.65, 0.75249, 9, -275.67, -83.95, 0, 60, 32.96, -16.26, 0.18812, 85, -141.76, 596.73, 0.05939, 4, 4, 88.88, 121.37, 0.75206, 9, -250.31, -76.87, 0, 60, 13.1, -33.54, 0.18802, 85, -161.62, 579.45, 0.05992, 4, 4, 61.38, 120.44, 0.75162, 9, -225.08, -87.85, 0, 60, -14.4, -34.47, 0.18791, 85, -189.12, 578.52, 0.06047, 5, 4, 41.68, 136.41, 0.7379, 3, 193.7, 100.09, 0.01808, 9, -214.15, -110.72, 0, 60, -34.09, -18.5, 0.189, 85, -208.81, 594.49, 0.05502, 5, 4, 41.04, 165.4, 0.73253, 3, 206.95, 125.89, 0.03128, 9, -226.03, -137.18, 0, 60, -34.73, 10.49, 0.19095, 85, -209.45, 623.48, 0.04523, 5, 4, 59.11, 185.27, 0.75417, 3, 232.3, 134.76, 0.01566, 9, -250.88, -147.37, 0, 60, -16.67, 30.37, 0.19246, 85, -191.39, 643.36, 0.03772, 4, 4, 89.42, 186.89, 0.76778, 9, -278.94, -135.8, 0, 60, 13.64, 31.98, 0.19195, 85, -161.08, 644.97, 0.04027], "hull": 118, "edges": [4, 6, 10, 12, 12, 14, 22, 24, 36, 38, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 70, 72, 72, 74, 82, 84, 84, 86, 90, 92, 92, 94, 98, 100, 100, 102, 102, 104, 104, 106, 110, 112, 112, 114, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 144, 146, 156, 158, 158, 160, 160, 162, 162, 164, 168, 170, 174, 176, 176, 178, 178, 180, 180, 182, 186, 188, 190, 192, 192, 194, 198, 200, 200, 202, 202, 204, 204, 206, 214, 216, 224, 226, 230, 232, 232, 234, 226, 228, 228, 230, 6, 8, 8, 10, 14, 16, 16, 18, 220, 222, 222, 224, 216, 218, 218, 220, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 212, 214, 210, 212, 206, 208, 208, 210, 194, 196, 196, 198, 188, 190, 182, 184, 184, 186, 170, 172, 172, 174, 38, 40, 40, 42, 44, 46, 42, 44, 46, 48, 48, 50, 50, 52, 66, 68, 68, 70, 164, 166, 166, 168, 86, 88, 88, 90, 80, 82, 78, 80, 146, 148, 148, 150, 150, 152, 94, 96, 96, 98, 152, 154, 154, 156, 136, 138, 138, 140, 140, 142, 142, 144, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 106, 108, 108, 110, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 28, 236, 210, 252, 254, 254, 198, 256, 258, 258, 260, 34, 262, 262, 260, 264, 266, 270, 272, 276, 278, 278, 280, 280, 282, 282, 284, 284, 0, 234, 0, 286, 288, 288, 290, 290, 292, 292, 294, 0, 2, 2, 4, 294, 2, 384, 386, 388, 390, 390, 392, 392, 394, 394, 396, 398, 400, 400, 402, 402, 404, 404, 406, 76, 78, 74, 76, 406, 408, 386, 410, 410, 388, 412, 414, 414, 408, 420, 422, 422, 424, 424, 426, 426, 428, 420, 396, 428, 398, 430, 432, 432, 434, 250, 242, 250, 436, 436, 252, 250, 438, 438, 256, 444, 446, 446, 448, 448, 450, 450, 452, 452, 454, 454, 456, 456, 458, 458, 460, 460, 444, 462, 464, 464, 466, 466, 468, 468, 470, 470, 472, 472, 474, 474, 476, 476, 462, 478, 480, 482, 484, 484, 486, 486, 488, 488, 202, 480, 490, 490, 482, 478, 208, 492, 494, 494, 496, 496, 498, 498, 500, 500, 502, 502, 504, 504, 506, 506, 492, 478, 236, 308, 186], "width": 541, "height": 1617}}, "cloth": {"cloth": {"type": "mesh", "uvs": [0.30449, 0.00148, 0.44406, 0.01325, 0.45254, 0.01304, 0.46431, 0.02671, 0.47543, 0.04454, 0.48252, 0.07282, 0.5197, 0.10052, 0.56317, 0.11298, 0.6247, 0.13979, 0.66108, 0.15209, 0.71822, 0.19527, 0.74323, 0.23587, 0.76551, 0.23821, 0.78531, 0.27443, 0.82799, 0.29239, 0.88081, 0.33778, 0.94873, 0.39496, 0.97442, 0.42936, 0.9991, 0.47073, 1, 0.5115, 0.98333, 0.56857, 0.93796, 0.63834, 0.88021, 0.59144, 0.82245, 0.54453, 0.81335, 0.52555, 0.76396, 0.53111, 0.73772, 0.50257, 0.70828, 0.50049, 0.65652, 0.46545, 0.62895, 0.45437, 0.60461, 0.47093, 0.57958, 0.48259, 0.55315, 0.53832, 0.5552, 0.5773, 0.54834, 0.63121, 0.55682, 0.66686, 0.55817, 0.70871, 0.59086, 0.73884, 0.63247, 0.76985, 0.66637, 0.80606, 0.69605, 0.83504, 0.69419, 0.85973, 0.68775, 0.87861, 0.66787, 0.85038, 0.64114, 0.87321, 0.61916, 0.89718, 0.59015, 0.93092, 0.57636, 0.96465, 0.57635, 0.9949, 0.57113, 0.99447, 0.15618, 0.99395, 0.13559, 0.99966, 0.09942, 0.96733, 0.09941, 0.91835, 0.09815, 0.87672, 0.08602, 0.82939, 0.06963, 0.76535, 0.05591, 0.69401, 0.04799, 0.63852, 0.04225, 0.58857, 0.03871, 0.53874, 0.00984, 0.48398, 1e-05, 0.42684, 0.00972, 0.36778, 0.03473, 0.31288, 0.02133, 0.29056, 0.07313, 0.23678, 0.07084, 0.18472, 0.07293, 0.13062, 0.08486, 0.08576, 0.10873, 0.08115, 0.15298, 0.07091, 0.1888, 0.0709, 0.2148, 0.05727, 0.23836, 0.03696, 0.26261, 0.0184, 0.28692, 0.00504, 0.17723, 0.94623, 0.14954, 0.8103, 0.13423, 0.7473, 0.09969, 0.5765, 0.09111, 0.49757, 0.142, 0.28339, 0.25985, 0.09633, 0.44169, 0.39173, 0.42357, 0.64252, 0.44362, 0.71328, 0.49811, 0.80275, 0.52588, 0.85668, 0.42898, 0.1218, 0.41301, 0.15664, 0.291, 0.07417, 0.44398, 0.02714, 0.44982, 0.05265, 0.44789, 0.08401, 0.41565, 0.22558, 0.43844, 0.3132, 0.44085, 0.44344, 0.43449, 0.49247, 0.42293, 0.57491, 0.5525, 0.95941, 0.54555, 0.91141, 0.47148, 0.76232, 0.30363, 0.04578, 0.23472, 0.14903, 0.21013, 0.1997, 0.12205, 0.31596, 0.10957, 0.62636, 0.12115, 0.68685, 0.16107, 0.86499, 0.16725, 0.90876, 0.58341, 0.87336, 0.63263, 0.83171, 0.58224, 0.80791, 0.55236, 0.77295, 0.52306, 0.73204, 0.50255, 0.69559, 0.49142, 0.63758, 0.49376, 0.57547, 0.50262, 0.52666, 0.51727, 0.47534, 0.52899, 0.41212, 0.52957, 0.35101, 0.51961, 0.29523, 0.51199, 0.2381, 0.50848, 0.18083, 0.48379, 0.10974, 0.46196, 0.12331, 0.58914, 0.19362, 0.65032, 0.2249, 0.69705, 0.26373, 0.73081, 0.30256, 0.85572, 0.39317, 0.81578, 0.46328, 0.69598, 0.3964, 0.65519, 0.37051, 0.59996, 0.42768, 0.611, 0.34571, 0.60166, 0.2756, 0.89964, 0.41924, 0.85785, 0.4592, 0.95859, 0.49992, 0.94119, 0.45481, 0.92997, 0.55331, 0.91042, 0.46517, 0.83221, 0.48834, 0.87969, 0.51701, 0.14733, 0.12666, 0.1084, 0.12453, 0.12927, 0.17358, 0.19407, 0.21969, 0.1067, 0.3592, 0.09264, 0.42612, 0.095, 0.5383, 0.4232, 0.25463, 0.42722, 0.53264, 0.08999, 0.28175, 0.13697, 0.21886, 0.03883, 0.48622, 0.04343, 0.41776, 0.06119, 0.35181], "triangles": [50, 51, 77, 50, 77, 100, 77, 102, 87, 77, 51, 52, 52, 110, 77, 52, 53, 110, 102, 77, 110, 110, 54, 109, 110, 53, 54, 110, 109, 86, 54, 78, 109, 54, 55, 78, 109, 78, 86, 55, 79, 78, 55, 56, 79, 78, 79, 85, 56, 108, 79, 56, 57, 108, 85, 79, 108, 57, 107, 108, 57, 58, 107, 99, 108, 107, 58, 80, 107, 58, 59, 80, 99, 107, 80, 59, 153, 80, 59, 60, 153, 106, 80, 153, 60, 81, 153, 48, 49, 47, 50, 100, 49, 77, 88, 100, 77, 87, 88, 88, 101, 100, 49, 100, 47, 47, 100, 46, 100, 101, 46, 101, 111, 46, 46, 111, 45, 101, 88, 111, 44, 45, 112, 42, 43, 41, 88, 113, 111, 45, 111, 112, 111, 113, 112, 44, 112, 43, 41, 43, 40, 88, 114, 113, 88, 87, 114, 112, 39, 43, 43, 39, 40, 113, 38, 112, 112, 38, 39, 113, 37, 38, 113, 114, 37, 87, 115, 114, 114, 36, 37, 114, 115, 36, 157, 67, 149, 148, 67, 68, 67, 148, 149, 105, 149, 147, 147, 71, 72, 104, 147, 72, 149, 148, 147, 72, 73, 104, 68, 69, 148, 148, 70, 147, 147, 70, 71, 148, 69, 70, 22, 143, 21, 21, 143, 20, 23, 146, 22, 22, 146, 143, 143, 141, 20, 20, 141, 19, 146, 144, 143, 141, 143, 144, 146, 140, 144, 141, 144, 142, 23, 145, 146, 23, 24, 145, 24, 25, 133, 24, 133, 145, 133, 25, 26, 145, 140, 146, 141, 18, 19, 133, 26, 134, 28, 134, 27, 26, 27, 134, 141, 17, 18, 141, 142, 17, 145, 133, 140, 134, 28, 135, 140, 139, 144, 144, 139, 142, 133, 132, 140, 132, 133, 131, 140, 132, 139, 142, 16, 17, 142, 139, 16, 28, 29, 135, 16, 139, 15, 133, 134, 131, 134, 135, 131, 139, 132, 15, 131, 13, 132, 13, 131, 12, 131, 11, 12, 13, 14, 132, 132, 14, 15, 135, 130, 131, 135, 137, 130, 130, 138, 129, 130, 137, 138, 131, 130, 11, 130, 10, 11, 130, 129, 10, 129, 9, 10, 124, 128, 138, 138, 128, 129, 124, 125, 128, 128, 8, 129, 129, 8, 9, 125, 7, 128, 128, 7, 8, 127, 126, 125, 125, 6, 7, 125, 126, 6, 126, 94, 5, 126, 5, 6, 94, 93, 5, 93, 4, 5, 93, 94, 89, 91, 74, 75, 89, 103, 92, 93, 89, 92, 91, 75, 103, 103, 75, 76, 3, 93, 92, 93, 3, 4, 3, 92, 2, 2, 92, 1, 103, 0, 92, 103, 76, 0, 92, 0, 1, 85, 108, 99, 99, 80, 155, 155, 80, 106, 155, 106, 82, 155, 82, 150, 154, 150, 105, 95, 104, 90, 95, 105, 104, 117, 99, 118, 34, 118, 33, 32, 118, 119, 118, 32, 33, 81, 151, 106, 99, 155, 118, 118, 155, 119, 95, 154, 105, 98, 155, 150, 60, 158, 81, 60, 61, 158, 119, 120, 32, 32, 120, 31, 90, 104, 91, 104, 83, 91, 106, 153, 81, 151, 81, 152, 155, 98, 119, 84, 98, 150, 119, 98, 120, 81, 158, 152, 98, 97, 120, 97, 98, 84, 150, 96, 84, 158, 159, 152, 158, 61, 159, 61, 62, 159, 120, 121, 31, 31, 136, 30, 31, 121, 136, 120, 97, 121, 30, 136, 29, 29, 136, 135, 97, 84, 121, 154, 96, 150, 121, 122, 136, 136, 137, 135, 136, 122, 137, 62, 63, 159, 159, 160, 152, 152, 160, 151, 159, 63, 160, 121, 84, 122, 122, 96, 123, 122, 84, 96, 63, 64, 160, 106, 151, 156, 151, 160, 156, 160, 64, 156, 137, 122, 138, 122, 123, 138, 106, 156, 82, 96, 124, 123, 96, 154, 124, 64, 65, 156, 123, 124, 138, 65, 66, 156, 156, 157, 82, 82, 157, 150, 156, 66, 157, 154, 95, 124, 95, 125, 124, 157, 66, 67, 125, 90, 127, 125, 95, 90, 105, 150, 149, 104, 105, 147, 150, 157, 149, 90, 89, 127, 91, 103, 90, 90, 103, 89, 104, 73, 83, 89, 94, 127, 126, 127, 94, 73, 74, 83, 91, 83, 74, 102, 110, 86, 78, 85, 86, 87, 102, 115, 102, 116, 115, 102, 86, 116, 115, 116, 36, 86, 117, 116, 86, 85, 117, 116, 35, 36, 116, 34, 35, 116, 117, 34, 85, 99, 117, 117, 118, 34], "vertices": [4, 4, 297.19, 43.22, 0.0873, 5, 43.62, 46.07, 0.74935, 6, -40.83, 43.23, 0.1405, 8, 74.97, -119.8, 0.02286, 5, 4, 301.5, -43.68, 0.05032, 5, 53.42, -40.38, 0.77343, 6, -24.8, -42.29, 0.12858, 7, 68.89, 97.47, 0.04748, 8, 79.29, -206.7, 0.00019, 5, 4, 302.21, -48.91, 0.0581, 5, 54.46, -45.56, 0.77422, 6, -23.39, -47.37, 0.12314, 7, 69.6, 92.24, 0.04435, 8, 80, -211.93, 0.0002, 5, 4, 296.41, -56.95, 0.08957, 5, 49.17, -53.95, 0.72673, 6, -28.05, -56.13, 0.07851, 7, 63.79, 84.19, 0.10498, 8, 74.19, -219.98, 0.00021, 5, 4, 288.53, -64.83, 0.15164, 5, 41.81, -62.31, 0.65599, 6, -34.8, -65, 0.01596, 7, 55.91, 76.31, 0.1762, 8, 66.31, -227.85, 0.00021, 4, 4, 275.27, -70.82, 0.2487, 5, 28.96, -69.12, 0.40509, 7, 42.66, 70.33, 0.34605, 8, 53.06, -233.84, 0.00016, 4, 4, 264.46, -95.36, 0.2295, 5, 19.72, -94.3, 0.09234, 7, 31.85, 45.79, 0.67809, 8, 42.25, -258.38, 7e-05, 4, 4, 261.53, -122.92, 0.10649, 5, 18.54, -121.99, 0.02277, 7, 28.91, 18.22, 0.87071, 8, 39.31, -285.94, 3e-05, 2, 7, 20.29, -21.31, 0.85909, 20, -14.02, 37.94, 0.14091, 2, 7, 16.92, -44.48, 0.37919, 20, 8.12, 45.58, 0.62081, 2, 7, 0.01, -82.23, 0.04473, 20, 49.41, 47.89, 0.95527, 2, 7, -17.95, -99.98, 0.00378, 20, 73.43, 40.08, 0.99622, 1, 20, 85.57, 46.87, 1, 1, 20, 105.69, 39.03, 1, 1, 20, 132.63, 46.57, 1, 1, 20, 172.31, 46.48, 1, 1, 20, 222.99, 46.85, 1, 2, 20, 245.67, 41.8, 0.94027, 21, -37.29, -18.89, 0.05973, 2, 20, 269.72, 33.56, 0.68007, 21, -40.35, 6.35, 0.31993, 2, 20, 281.35, 17.31, 0.43413, 21, -30.78, 23.89, 0.56587, 2, 20, 288.38, -11.68, 0.08164, 21, -7.75, 42.84, 0.91836, 1, 21, 33.84, 58.18, 1, 2, 20, 241.44, -56.81, 0.00745, 21, 53.32, 20.24, 0.99255, 3, 20, 198.8, -57.82, 0.81714, 21, 72.79, -17.7, 0.18286, 8, -152.08, -470.59, 0, 3, 3, 18.01, -302.04, 2e-05, 20, 188.91, -53.27, 0.99998, 8, -143.5, -463.88, 0, 4, 3, 26.89, -272.51, 0.00069, 4, 72.46, -270.67, 0.00263, 20, 164.96, -72.69, 0.99668, 8, -149.75, -433.69, 0, 4, 3, 45.93, -262.55, 0.00494, 4, 84.46, -252.84, 0.02206, 20, 143.61, -70.21, 0.973, 8, -137.75, -415.86, 0, 4, 3, 53.68, -245.92, 0.02306, 4, 83.36, -234.53, 0.10773, 20, 127.85, -79.6, 0.86921, 8, -138.85, -397.55, 0, 3, 4, 96.69, -200.57, 0.26999, 20, 91.56, -83.35, 0.73001, 8, -125.52, -363.59, 0, 4, 3, 92.99, -208.5, 0.00465, 4, 100.1, -182.9, 0.43654, 20, 74.3, -88.43, 0.55881, 8, -122.11, -345.92, 0, 4, 3, 91.08, -191.43, 0.05349, 4, 90.29, -168.8, 0.64349, 20, 66.28, -103.62, 0.30302, 8, -131.92, -331.83, 0, 5, 3, 91.56, -174.86, 0.10325, 4, 82.81, -154, 0.76832, 7, -149.8, -12.86, 0.00025, 20, 56.56, -117.05, 0.12765, 21, 188.06, -119.96, 0.00054, 3, 3, 72.31, -149.45, 0.2749, 9, -105.99, 144.83, 0.00142, 4, 53.79, -140.83, 0.72368, 3, 3, 54.1, -143.54, 0.39804, 9, -87.5, 139.89, 0.00793, 4, 34.96, -144.31, 0.59403, 4, 3, 31.16, -129.77, 0.50821, 9, -63.87, 127.33, 0.03234, 4, 8.23, -143.12, 0.37844, 10, -138.34, 18.28, 0.08102, 4, 3, 12.98, -128.18, 0.5013, 9, -45.63, 126.7, 0.05989, 4, -8.51, -150.38, 0.25181, 10, -120.1, 17.64, 0.187, 5, 3, -6.37, -121.34, 0.40721, 9, -25.94, 120.88, 0.09549, 4, -28.78, -153.59, 0.13866, 10, -100.42, 11.82, 0.35864, 74, -28.77, 279.83, 0, 5, 3, -27.63, -134.74, 0.23497, 9, -5.42, 135.37, 0.07819, 4, -41.1, -175.49, 0.05599, 10, -79.89, 26.32, 0.63085, 74, -10.89, 297.48, 0, 5, 3, -51.35, -153.12, 0.10266, 9, 17.31, 154.98, 0.02523, 4, -53.2, -202.96, 0.01834, 10, -57.16, 45.92, 0.83377, 14, -125.64, -11.5, 0.02, 5, 3, -75.66, -166.11, 0.01757, 9, 40.9, 169.22, 0.00056, 4, -68.38, -225.95, 0.00224, 10, -33.57, 60.16, 0.91705, 14, -110.7, 11.66, 0.06258, 4, 3, -95.7, -177.98, 0.00141, 4, -80.35, -245.93, 6e-05, 10, -14.18, 73.06, 0.80392, 14, -98.94, 31.76, 0.19461, 4, 3, -106.5, -172.41, 0.00037, 9, 71.37, 177.12, 0, 10, -3.1, 68.07, 0.78524, 14, -86.8, 32.14, 0.21439, 3, 9, 78.84, 170.35, 0, 10, 4.37, 61.29, 0.73227, 14, -77.11, 29.33, 0.26773, 4, 3, -96.17, -158.91, 0.00287, 4, -89.84, -229.39, 0.00025, 10, -12.71, 54.05, 0.78431, 14, -89.27, 15.32, 0.21257, 5, 3, -100.38, -139.32, 0.00512, 4, -102.88, -214.17, 0.00047, 10, -7.48, 34.7, 0.81057, 14, -76.08, 0.24, 0.16606, 87, -207.47, 62.21, 0.01778, 5, 3, -106.21, -122.27, 0.00304, 4, -116.13, -201.95, 0.00026, 10, -0.77, 17.98, 0.85547, 14, -62.71, -11.84, 0.11873, 87, -221.14, 50.47, 0.0225, 5, 9, 83.54, 104.62, 0.02591, 10, 9.06, -4.43, 0.87907, 14, -44.03, -27.66, 0.07293, 74, 81.89, 281.68, 4e-05, 87, -239.19, 33.94, 0.02205, 5, 9, 96.42, 91.18, 0.15644, 10, 21.95, -17.88, 0.78367, 14, -26.55, -34.08, 0.03339, 74, 96.8, 270.53, 0.00024, 87, -247.76, 17.4, 0.02626, 5, 3, -140.77, -79.76, 0, 9, 110.45, 86.39, 0.19828, 10, 35.97, -22.67, 0.77352, 74, 111.42, 268.1, 0.00032, 87, -247.78, 2.59, 0.02788, 4, 9, 109.21, 83.39, 0.19852, 10, 34.73, -25.67, 0.77437, 74, 110.69, 264.93, 0.00031, 87, -251.02, 2.79, 0.0268, 5, 3, -43.28, 162.72, 0.00513, 9, 25.79, -160.86, 0.02388, 10, -48.69, -269.92, 3e-05, 74, 68.31, 10.33, 0.93095, 85, -447.02, 536.7, 0.04, 4, 4, -200.79, 91.01, 0, 72, 196.9, -3.65, 0, 74, 68.99, -2.76, 0.96, 85, -451.28, 549.09, 0.04, 2, 4, -187.65, 115.19, 0, 74, 49.68, -22.37, 1, 4, 3, 4.23, 181.75, 0, 4, -163.81, 117.97, 0, 72, 153.6, -18.45, 0, 74, 26, -18.46, 1, 5, 3, 23.46, 174.9, 1e-05, 4, -143.64, 121.11, 0, 72, 133.4, -15.46, 3e-05, 73, 66.32, -16.07, 0.31241, 74, 5.75, -15.9, 0.68754, 2, 73, 42.12, -19, 0.99896, 74, -18.36, -19.56, 0.00104, 3, 72, 76.5, -22.87, 0.23928, 73, 9.36, -22.95, 0.76037, 74, -50.98, -24.5, 0.00034, 3, 72, 40.57, -24.84, 0.99775, 73, -26.59, -24.59, 0.00223, 74, -86.87, -27.21, 3e-05, 4, 4, -31.31, 165.6, 0.03143, 71, 82.18, -23.99, 0.09207, 72, 12.94, -24.68, 0.8765, 74, -114.49, -27.64, 0, 3, 4, -7.42, 171.97, 0.14309, 71, 57.45, -24.26, 0.50593, 72, -11.78, -23.69, 0.35098, 2, 4, 16.58, 176.98, 0.66857, 71, 32.96, -23.18, 0.33143, 4, 4, 41.16, 197.92, 0.73571, 71, 3.96, -37.38, 0.07403, 85, -209.34, 656, 0.00558, 59, -44.37, 56.17, 0.18468, 3, 4, 68.26, 207.23, 0.79227, 85, -182.24, 665.31, 0.00966, 59, -17.27, 65.48, 0.19807, 3, 4, 97.71, 204.58, 0.78836, 85, -152.79, 662.66, 0.01455, 59, 12.18, 62.83, 0.19709, 4, 4, 126.23, 192.23, 0.72467, 8, -95.99, 29.21, 0.06073, 85, -124.27, 650.32, 0.01825, 59, 40.7, 50.48, 0.19635, 4, 4, 136.13, 201.78, 0.70591, 8, -86.09, 38.76, 0.08298, 85, -114.37, 659.86, 0.01388, 59, 50.6, 60.03, 0.19722, 5, 4, 166.02, 172.83, 0.50924, 7, -66.59, 313.97, 0, 8, -56.19, 9.8, 0.27483, 85, -84.47, 630.91, 0.01991, 58, 68.46, 52.4, 0.19602, 4, 4, 191.2, 177.19, 0.25183, 8, -31.02, 14.16, 0.70541, 85, -59.3, 635.27, 0.01315, 58, 93.63, 56.76, 0.02961, 2, 8, -4.53, 15.94, 0.99087, 85, -32.82, 637.05, 0.00913, 2, 7, 7.76, 315.28, 0, 8, 18.16, 11.11, 1, 3, 4, 244.33, 159.65, 0.01833, 7, 11.72, 300.79, 0, 8, 22.12, -3.38, 0.98167, 2, 4, 252.5, 132.89, 0.13513, 8, 30.29, -30.13, 0.86487, 3, 4, 255.08, 110.76, 0.31411, 5, -2.67, 110.81, 0.00403, 8, 32.87, -52.26, 0.68186, 3, 4, 263.58, 95.47, 0.37649, 5, 6.78, 96.09, 0.16231, 8, 41.37, -67.55, 0.46121, 3, 4, 275.16, 82.06, 0.341, 5, 19.19, 83.44, 0.38727, 8, 52.95, -80.96, 0.27173, 4, 4, 285.94, 68.13, 0.23178, 5, 30.82, 70.22, 0.59951, 6, -55.34, 66.39, 0.03065, 8, 63.73, -94.89, 0.13805, 4, 4, 294.19, 53.87, 0.13414, 5, 39.96, 56.51, 0.71265, 6, -45.23, 53.38, 0.10504, 8, 71.98, -109.15, 0.04817, 6, 3, -26.43, 141.88, 0.0276, 9, 7.87, -140.93, 0.07739, 10, -66.6, -249.98, 0.0001, 73, 109.23, 25.63, 0.01142, 74, 47.38, 27.07, 0.84348, 85, -422.28, 526.39, 0.04, 7, 3, 41.8, 133.13, 0.06336, 9, -60.73, -135.76, 0.01201, 4, -107.62, 93.12, 0.00076, 72, 107.28, 21.94, 0.02292, 73, 40.55, 21.57, 0.80105, 74, -21.14, 20.95, 0.05991, 85, -358.11, 551.2, 0.04, 6, 3, 74, 130.51, 0.05087, 9, -93.02, -134.84, 0.00136, 4, -78.06, 106.15, 0.00665, 72, 75.19, 18.25, 0.27372, 73, 8.42, 18.17, 0.6274, 85, -328.55, 564.23, 0.04, 5, 4, 2.59, 137.16, 0.08381, 71, 56.36, 11.94, 0.58609, 72, -11.03, 12.52, 0.26542, 85, -247.91, 595.25, 0.04, 58, -94.98, 16.74, 0.02469, 4, 4, 40.39, 146.94, 0.47662, 71, 17.32, 11.82, 0.35957, 85, -210.11, 605.02, 0.04979, 60, -35.39, -7.97, 0.11403, 5, 4, 148.29, 127.63, 0.7291, 7, -84.32, 268.78, 0, 8, -73.92, -35.39, 0.02589, 85, -102.2, 585.71, 0.05626, 59, 62.77, -14.12, 0.18875, 3, 4, 247.82, 65.42, 0.6274, 5, -7.05, 65.1, 0.11552, 8, 25.6, -97.6, 0.25708, 4, 4, 117.12, -63.66, 0.72402, 7, -115.5, 77.48, 0.02581, 85, -133.38, 394.42, 0.06271, 57, 26.79, -18.96, 0.18746, 5, 3, 54.84, -55.65, 0.61882, 9, -83.63, 52.08, 0.00814, 4, -6.25, -66.68, 0.33528, 85, -256.74, 391.4, 0.02824, 87, -342.8, 175.25, 0.00952, 6, 3, 18.01, -54.35, 0.74663, 9, -46.79, 52.71, 0.09416, 4, -39.25, -83.07, 0.11509, 10, -121.26, -56.35, 0.00738, 85, -289.74, 375.01, 0.01895, 87, -330.33, 140.58, 0.0178, 7, 3, -35.28, -69.54, 0.26413, 9, 5.64, 70.67, 0.35228, 4, -78.87, -121.81, 0.03177, 10, -68.84, -38.39, 0.31892, 74, 10.59, 235.45, 3e-05, 85, -329.37, 336.27, 0.00424, 87, -296.44, 96.74, 0.02863, 6, 3, -66.23, -75.76, 0.08913, 9, 36.22, 78.5, 0.31934, 4, -103.12, -142.02, 0.00935, 10, -38.25, -30.55, 0.55413, 74, 39.48, 248.18, 0.0001, 87, -279.17, 70.31, 0.02795, 5, 4, 247.58, -40.51, 0.62563, 5, -0.59, -40.63, 0.11603, 7, 14.97, 100.63, 0.23364, 8, 25.37, -203.53, 6e-05, 85, -2.91, 417.57, 0.02464, 4, 4, 229.47, -32.62, 0.80996, 7, -3.14, 108.52, 0.16177, 8, 7.26, -195.65, 1e-05, 85, -21.02, 425.46, 0.02826, 3, 4, 260.84, 47.43, 0.4753, 5, 7.08, 47.97, 0.41739, 8, 38.62, -115.59, 0.10731, 5, 4, 294.74, -44.42, 0.06685, 5, 46.71, -41.55, 0.78233, 6, -31.4, -43.93, 0.10442, 7, 62.12, 96.73, 0.04618, 8, 72.52, -207.44, 0.00022, 6, 4, 282.74, -49.47, 0.15998, 5, 35.06, -47.35, 0.70023, 6, -42.61, -50.57, 0.02323, 7, 50.12, 91.67, 0.10934, 8, 60.52, -212.5, 0.00024, 85, 32.24, 408.61, 0.00699, 5, 4, 267.33, -50.06, 0.33278, 5, 19.72, -48.91, 0.46636, 7, 34.72, 91.09, 0.18636, 8, 45.12, -213.08, 0.00017, 85, 16.84, 408.02, 0.01432, 5, 4, 196.11, -38.16, 0.73458, 7, -36.5, 102.99, 0.09537, 8, -26.1, -201.18, 0, 85, -54.38, 419.92, 0.03494, 55, 90.51, -0.54, 0.13511, 5, 4, 155.11, -57.21, 0.66347, 7, -77.51, 83.94, 0.09196, 8, -67.11, -220.23, 0, 85, -95.39, 400.88, 0.05572, 56, 56.81, -17.33, 0.18886, 4, 4, 91.89, -66.07, 0.72581, 7, -140.72, 75.07, 0.00675, 85, -158.6, 392.01, 0.06082, 57, 1.57, -21.37, 0.20662, 5, 3, 120.58, -89.26, 0.04645, 4, 67.57, -64.92, 0.70849, 7, -165.05, 76.22, 0.00042, 85, -182.93, 393.16, 0.0558, 57, -22.76, -20.21, 0.18884, 5, 3, 85.74, -67.59, 0.21612, 9, -115.12, 62.38, 0.00011, 4, 26.61, -62.45, 0.55177, 85, -223.88, 395.63, 0.04, 55, -78.99, -24.84, 0.192, 5, 3, -119.12, -72.44, 0.00073, 9, 89.21, 77.96, 0.22689, 10, 14.74, -31.1, 0.74518, 74, 91.85, 256.3, 0.00031, 87, -262.61, 19.97, 0.02689, 6, 3, -95.68, -77.17, 0.01433, 9, 65.55, 81.44, 0.22181, 4, -128.35, -157.27, 0.00095, 10, -8.93, -27.61, 0.7363, 74, 67.94, 255.87, 0.00018, 87, -266.93, 43.49, 0.02642, 7, 3, -10.74, -61.52, 0.49008, 9, -18.45, 61.37, 0.26908, 4, -61.11, -103.07, 0.05994, 10, -92.93, -47.68, 0.14649, 74, -11.66, 222.34, 1e-05, 85, -311.61, 355.01, 0.01098, 87, -313, 116.55, 0.02342, 4, 4, 275.56, 41.24, 0.20758, 5, 22.17, 42.73, 0.72212, 6, -61.98, 38.34, 0.03206, 8, 53.35, -121.78, 0.03824, 3, 4, 220.36, 77.96, 0.70144, 8, -1.86, -85.06, 0.27486, 85, -30.14, 536.05, 0.0237, 5, 4, 193.93, 90.29, 0.66242, 7, -38.69, 231.43, 2e-05, 8, -28.29, -72.74, 0.16804, 85, -56.57, 548.37, 0.03433, 58, 96.36, -30.14, 0.13519, 5, 4, 131, 138.11, 0.76224, 7, -101.61, 279.26, 0, 85, -119.49, 596.19, 0.05896, 59, 45.48, -3.64, 0.08469, 60, 55.23, -16.8, 0.0941, 4, 4, -20.97, 128.24, 0.03014, 71, 81.39, 14.77, 0.04379, 72, 14.12, 14.07, 0.88607, 85, -271.46, 586.32, 0.04, 6, 3, 104.53, 127.07, 0.03232, 4, -49.58, 117.66, 0.01778, 71, 111.73, 17.94, 0.00416, 72, 44.58, 15.7, 0.86526, 73, -22.21, 15.9, 0.04047, 85, -300.07, 575.74, 0.04, 6, 3, 14.26, 136.43, 0.05283, 9, -33.05, -137.61, 0.03251, 72, 134.94, 24.06, 0.00163, 73, 68.23, 23.44, 0.28419, 74, 6.47, 23.65, 0.58884, 85, -383.9, 540.98, 0.04, 6, 3, -7.08, 140.83, 0.03489, 9, -11.51, -140.89, 0.05053, 10, -85.98, -249.95, 6e-05, 73, 90.02, 23.08, 0.04172, 74, 28.26, 23.94, 0.83281, 85, -404.76, 534.68, 0.04, 6, 3, -87.11, -105.96, 0.03107, 9, 55.49, 109.75, 0.05024, 4, -107.11, -178.51, 0.00372, 10, -18.98, 0.69, 0.89563, 74, 53.39, 282.15, 2e-05, 87, -243.38, 62.14, 0.01933, 6, 3, -79.53, -141.96, 0.04131, 9, 46.04, 145.31, 0.00689, 4, -83.29, -206.56, 0.0056, 10, -28.44, 36.25, 0.89573, 14, -95.59, -7.57, 0.03956, 87, -212.76, 82.55, 0.01091, 6, 3, -57.06, -117.19, 0.12743, 9, 24.89, 119.39, 0.07983, 4, -75.33, -174.08, 0.02234, 10, -49.58, 10.33, 0.75377, 74, 21.62, 286.67, 1e-05, 87, -244.11, 94.21, 0.01663, 6, 3, -34.26, -106.29, 0.25264, 9, 2.69, 107.32, 0.1408, 4, -60.47, -153.64, 0.05304, 10, -71.79, -1.74, 0.53505, 74, 1.69, 271.12, 1e-05, 87, -262.7, 111.34, 0.01846, 7, 3, -8.88, -96.82, 0.42742, 9, -22.16, 96.53, 0.13695, 4, -42.66, -133.22, 0.11388, 10, -96.64, -12.53, 0.29984, 74, -21.06, 256.42, 0, 85, -293.16, 324.86, 0.00596, 87, -280.92, 131.38, 0.01594, 7, 3, 12.44, -91.61, 0.54809, 9, -43.18, 90.2, 0.09136, 4, -26.4, -118.48, 0.18833, 10, -117.65, -18.85, 0.146, 74, -40.76, 246.75, 0, 85, -276.9, 339.6, 0.01128, 87, -293.68, 149.24, 0.01494, 6, 3, 41.41, -95.74, 0.54283, 9, -72.32, 92.81, 0.03042, 4, 1.04, -108.32, 0.37728, 10, -146.8, -16.25, 0.02149, 85, -249.46, 349.77, 0.01746, 87, -300.6, 177.67, 0.01051, 5, 3, 69.13, -108.39, 0.33038, 9, -100.66, 104, 0.00492, 4, 31.44, -106.24, 0.58347, 85, -219.06, 351.84, 0.03287, 55, -74.16, -68.63, 0.04836, 6, 3, 89.28, -122.39, 0.18199, 9, -121.52, 116.92, 0.00034, 4, 55.83, -108.95, 0.70451, 7, -176.79, 32.19, 0.0005, 85, -194.67, 349.13, 0.03549, 55, -49.77, -71.34, 0.07716, 6, 3, 109.25, -140.19, 0.06755, 4, 81.86, -115.09, 0.80388, 7, -150.75, 26.05, 0.00521, 85, -168.64, 342.99, 0.037, 55, -23.74, -77.48, 0.07704, 56, -16.43, -75.21, 0.00932, 5, 4, 113.47, -118.75, 0.83567, 7, -119.14, 22.39, 0.03161, 85, -137.02, 339.33, 0.03705, 55, 7.87, -81.14, 0.07704, 56, 15.18, -78.87, 0.01863, 6, 4, 143.26, -115.65, 0.76225, 7, -89.36, 25.49, 0.11579, 8, -78.96, -278.67, 0, 85, -107.24, 342.43, 0.03546, 55, 37.65, -78.04, 0.07716, 56, 44.96, -75.77, 0.00934, 5, 4, 169.69, -106.34, 0.63229, 7, -62.92, 34.81, 0.28521, 8, -52.52, -269.36, 0, 85, -80.81, 351.75, 0.0342, 55, 64.09, -68.72, 0.04829, 5, 4, 196.95, -98.39, 0.44996, 7, -35.67, 42.75, 0.49919, 8, -25.27, -261.41, 1e-05, 85, -53.55, 359.69, 0.0265, 55, 91.35, -60.78, 0.02434, 4, 4, 224.57, -92.98, 0.32424, 7, -8.04, 48.17, 0.65528, 8, 2.36, -256, 3e-05, 85, -25.93, 365.11, 0.02045, 5, 4, 257.39, -73.69, 0.33873, 5, 11.3, -73.12, 0.13317, 7, 24.78, 67.45, 0.51184, 8, 35.18, -236.71, 0.00011, 85, 6.9, 384.39, 0.01615, 5, 4, 249.22, -60.98, 0.49064, 5, 2.34, -60.95, 0.07541, 7, 16.61, 80.17, 0.41316, 8, 27.01, -224, 0.0001, 85, -1.28, 397.1, 0.02069, 4, 4, 224.14, -143.53, 0.0085, 7, -8.47, -2.39, 0.93813, 20, -17.62, 3.7, 0.03944, 85, -26.35, 314.55, 0.01392, 3, 7, -19.29, -41.96, 0.32811, 20, 22.5, 12.25, 0.65928, 85, -37.18, 274.98, 0.01262, 3, 7, -34.83, -73.03, 0.21395, 20, 57.24, 12.71, 0.77497, 85, -52.71, 243.9, 0.01108, 2, 20, 85.29, 8.66, 0.98922, 85, -69.18, 220.84, 0.01078, 2, 20, 174.52, 15.25, 0.99312, 85, -104.3, 138.54, 0.00688, 4, 3, 45.78, -314.78, 1e-05, 20, 173.12, -27.12, 0.99413, 8, -113.01, -461.86, 0, 85, -141.29, 159.24, 0.00586, 4, 4, 133.13, -221.03, 0.24637, 20, 93, -41.58, 0.73799, 8, -89.08, -384.05, 0, 85, -117.36, 237.05, 0.01564, 4, 4, 142.8, -194.37, 0.49614, 20, 64.88, -45.23, 0.48703, 8, -79.41, -357.39, 0, 85, -107.7, 263.72, 0.01682, 6, 3, 111.83, -196.62, 0.01781, 4, 111, -163.48, 0.7408, 7, -121.61, -22.34, 0.00231, 20, 52.04, -87.66, 0.22555, 8, -111.21, -326.5, 0, 85, -139.49, 294.6, 0.01354, 4, 4, 151.7, -165.66, 0.73869, 7, -80.92, -24.52, 0.01589, 20, 35.29, -50.51, 0.22543, 85, -98.8, 292.42, 0.01999, 5, 4, 185.15, -155.91, 0.47746, 7, -47.47, -14.77, 0.14261, 20, 11.28, -25.26, 0.36156, 8, -37.07, -318.94, 0, 85, -65.35, 302.17, 0.01837, 1, 20, 204.32, 19.92, 1, 3, 20, 193.7, -10.84, 0.49714, 21, 32.72, -42.75, 0.50286, 8, -108, -487.62, 0, 2, 20, 256.81, 7.62, 0.51237, 21, -11.38, 6.03, 0.48763, 1, 20, 235.49, 19.91, 1, 1, 21, 17.17, 19.68, 1, 2, 20, 222.45, 5, 0.37143, 21, 5.94, -23.77, 0.62857, 3, 20, 188.45, -31.59, 0.67714, 21, 53.69, -38.44, 0.32286, 8, -124.03, -473.43, 0, 3, 20, 220.79, -26.74, 0.55958, 21, 35.24, -11.43, 0.44042, 8, -134.57, -504.39, 0, 3, 4, 224.96, 133.22, 0.24289, 8, 2.74, -29.8, 0.73685, 85, -25.54, 591.3, 0.02027, 4, 4, 223.19, 157.39, 0.10478, 7, -9.42, 298.54, 1e-05, 8, 0.98, -5.63, 0.87957, 85, -27.3, 615.47, 0.01564, 5, 4, 200.82, 141.72, 0.38869, 7, -31.79, 282.86, 2e-05, 8, -21.39, -21.3, 0.52883, 85, -49.67, 599.8, 0.02092, 58, 103.26, 21.29, 0.06154, 5, 4, 183.04, 99.07, 0.64477, 7, -49.57, 240.22, 1e-05, 8, -39.17, -63.95, 0.12321, 85, -67.46, 557.16, 0.04, 58, 85.47, -21.35, 0.192, 4, 4, 108.86, 145.15, 0.75176, 7, -123.76, 286.3, 0, 85, -141.64, 603.23, 0.0603, 60, 33.08, -9.76, 0.18794, 4, 4, 75.27, 150.04, 0.73222, 71, -17.25, 17.45, 0.02241, 85, -175.22, 608.12, 0.05671, 60, -0.5, -4.86, 0.18866, 5, 4, 20.85, 142.23, 0.11023, 71, 37.42, 11.55, 0.77314, 72, -29.96, 13.09, 0.04742, 85, -229.65, 600.31, 0.0446, 59, -64.68, 0.47, 0.02461, 5, 4, 182.52, -44.47, 0.67713, 7, -50.1, 96.67, 0.09087, 8, -39.69, -207.49, 0, 85, -67.98, 413.61, 0.04, 55, 76.92, -6.86, 0.192, 6, 3, 103.98, -77.76, 0.13027, 9, -133.87, 71.58, 5e-05, 4, 47.49, -62.71, 0.62927, 7, -185.12, 78.44, 0.00021, 85, -203, 395.38, 0.05025, 56, -50.8, -22.83, 0.18995, 5, 4, 145.35, 159.86, 0.64127, 7, -87.27, 301.01, 0, 8, -76.86, -3.16, 0.12908, 85, -105.15, 617.94, 0.03707, 59, 59.82, 18.11, 0.19259, 5, 4, 179.34, 134.4, 0.56897, 7, -53.27, 275.55, 1e-05, 8, -42.87, -28.62, 0.20773, 85, -71.16, 592.48, 0.02913, 58, 81.77, 13.97, 0.19417, 5, 4, 42.15, 179.88, 0.65571, 71, 7.46, -19.66, 0.16446, 85, -208.34, 637.97, 0.01685, 59, -43.37, 38.13, 0.11928, 60, -33.62, 24.98, 0.04369, 5, 4, 75.8, 180.92, 0.7624, 71, -25.4, -12.34, 0.01332, 85, -174.69, 639, 0.03177, 59, -9.72, 39.17, 0.12541, 60, 0.03, 26.01, 0.0671, 6, 4, 109.18, 173.68, 0.75536, 7, -123.43, 314.83, 0, 8, -113.03, 10.66, 0.01312, 85, -141.32, 631.77, 0.0394, 59, 23.65, 31.93, 0.10284, 60, 33.41, 18.78, 0.08928], "hull": 77, "edges": [0, 152, 180, 178, 8, 10, 10, 12, 22, 24, 30, 32, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 58, 60, 84, 86, 86, 88, 88, 90, 94, 96, 96, 98, 176, 174, 172, 170, 158, 156, 100, 102, 102, 104, 120, 122, 122, 124, 136, 138, 142, 144, 154, 100, 100, 98, 4, 2, 2, 0, 2, 184, 186, 184, 178, 188, 188, 186, 180, 190, 168, 194, 196, 194, 170, 198, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 24, 26, 26, 28, 32, 34, 34, 36, 46, 48, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 76, 78, 78, 80, 80, 82, 82, 84, 90, 92, 92, 94, 98, 200, 176, 202, 202, 200, 172, 204, 204, 174, 70, 72, 0, 206, 206, 182, 182, 166, 150, 152, 144, 146, 146, 148, 148, 150, 138, 140, 140, 142, 132, 134, 134, 136, 130, 132, 128, 130, 124, 126, 126, 128, 112, 114, 110, 112, 108, 110, 208, 166, 210, 208, 212, 164, 214, 160, 158, 216, 216, 214, 218, 156, 154, 220, 220, 218, 104, 106, 106, 108, 72, 74, 74, 76, 250, 254, 254, 180, 262, 264, 266, 268, 42, 44, 44, 46, 118, 120, 114, 116, 116, 118, 300, 210, 300, 164, 302, 212, 302, 304, 304, 162, 160, 306, 306, 162, 190, 308, 308, 192, 192, 168, 196, 310, 310, 198, 128, 312, 312, 164, 132, 314, 314, 300], "width": 622, "height": 490}}, "cloth_B": {"cloth_B": {"type": "mesh", "uvs": [0, 0.00163, 0.18987, 0.00927, 0.39165, 0.01739, 0.5743, 0.02474, 0.75695, 0.03209, 0.78572, 0.14644, 0.81839, 0.2717, 0.85591, 0.41556, 0.89597, 0.56916, 0.93727, 0.7275, 0.96775, 0.84438, 0.99823, 0.96126, 0.85304, 0.98492, 0.71951, 0.99677, 0.55033, 0.99602, 0.37638, 0.98852, 0.2834, 0.97397, 0.22504, 0.91863, 0.18464, 0.874, 0.17726, 0.766, 0.15585, 0.69408, 0.10512, 0.56722, 0.06128, 0.42725, 0.02097, 0.27214, 0.00829, 0.12752, 0.3701, 0.87051, 0.36633, 0.75811, 0.34937, 0.65663, 0.31922, 0.5411, 0.28153, 0.41932, 0.2495, 0.27257, 0.21558, 0.14923, 0.65465, 0.89081, 0.64146, 0.77216, 0.63015, 0.65663, 0.60942, 0.53485, 0.5755, 0.40683, 0.54724, 0.27725, 0.52651, 0.15704], "triangles": [35, 7, 8, 34, 35, 8, 27, 35, 34, 34, 8, 9, 33, 34, 9, 26, 34, 33, 10, 32, 33, 10, 33, 9, 12, 32, 10, 12, 10, 11, 13, 32, 12, 14, 32, 13, 26, 27, 34, 19, 20, 27, 26, 19, 27, 25, 26, 33, 18, 19, 26, 25, 18, 26, 25, 33, 32, 17, 18, 25, 16, 17, 25, 14, 15, 25, 16, 25, 15, 32, 14, 25, 28, 29, 36, 28, 36, 35, 21, 29, 28, 27, 28, 35, 20, 21, 28, 20, 28, 27, 22, 23, 30, 22, 30, 29, 21, 22, 29, 23, 31, 30, 23, 24, 31, 24, 0, 1, 31, 1, 2, 24, 1, 31, 38, 2, 3, 31, 2, 38, 30, 31, 38, 38, 5, 37, 30, 38, 37, 3, 5, 38, 4, 5, 3, 6, 36, 37, 6, 37, 5, 36, 6, 7, 29, 37, 36, 35, 36, 7, 29, 30, 37], "vertices": [2, 4, 67.82, 182.02, 0.95693, 71, -17.94, -15.38, 0.04307, 3, 4, 71.1, 133.46, 0.97217, 71, -9.1, 32.48, 0.02763, 72, -75.36, 36.35, 0.00021, 1, 4, 74.58, 81.87, 1, 1, 4, 77.73, 35.16, 1, 1, 4, 80.88, -11.55, 1, 1, 4, 46.64, -22.95, 1, 2, 4, 9.16, -35.73, 0.94117, 2, -1.09, 98.95, 0.05883, 3, 4, -33.89, -50.42, 0.51251, 73, 14.1, 180.75, 0.0029, 2, 8.51, 54.5, 0.48459, 3, 4, -79.85, -66.09, 0.02105, 74, -3.86, 181.63, 0.00038, 2, 18.77, 7.04, 0.97858, 2, 74, 46.13, 184.08, 0.10085, 2, 29.34, -41.89, 0.89915, 2, 74, 83.04, 185.88, 0.17794, 2, 37.14, -78.01, 0.82206, 2, 74, 119.94, 187.69, 0.20364, 2, 44.95, -114.12, 0.79636, 2, 74, 121.09, 149.82, 0.25194, 2, 7.78, -121.43, 0.74806, 2, 74, 119.13, 115.5, 0.36857, 2, -26.4, -125.1, 0.63143, 3, 73, 175.02, 69.42, 0.00021, 74, 111.83, 72.81, 0.60015, 2, -69.72, -124.86, 0.39964, 2, 74, 102.28, 29.25, 0.86786, 2, -114.25, -122.55, 0.13214, 2, 74, 93.95, 6.5, 0.96146, 2, -138.05, -118.05, 0.03854, 1, 74, 74.65, -5.45, 1, 1, 74, 59.35, -13.4, 1, 1, 74, 26.12, -9.82, 1, 2, 73, 64, -11.69, 0.25943, 74, 3.3, -11.6, 0.74057, 2, 72, 90.12, -16.67, 0.00237, 73, 23.03, -16.88, 0.99763, 2, 72, 45.54, -19.75, 0.99515, 73, -21.57, -19.55, 0.00485, 3, 4, -14.58, 167.02, 0.14221, 71, 65.62, -21.23, 0.53082, 72, -3.47, -21.08, 0.32696, 2, 4, 29.43, 175.41, 0.57311, 71, 20.89, -18.47, 0.42689, 4, 4, -187.91, 56.86, 0.00038, 73, 128.07, 31.62, 0.00999, 74, 66.04, 33.62, 0.86915, 2, -115.85, -86.08, 0.12049, 5, 4, -153.52, 61.83, 0.00914, 72, 160.39, 38.22, 0.00014, 73, 93.8, 37.37, 0.1071, 74, 31.61, 38.34, 0.74895, 2, -116.82, -51.35, 0.13468, 5, 4, -122.87, 69.77, 0.03438, 72, 128.77, 39.72, 0.01066, 73, 62.2, 39.15, 0.43923, 74, -0.03, 39.17, 0.39541, 2, -121.16, -19.99, 0.12031, 5, 4, -88.31, 81.57, 0.08165, 72, 92.26, 38.69, 0.11948, 73, 25.68, 38.46, 0.67682, 74, -36.51, 37.38, 0.04879, 2, -128.88, 15.71, 0.07326, 6, 4, -52.05, 95.5, 0.12357, 71, 119.61, 38.79, 0.007, 72, 53.5, 36.12, 0.58826, 73, -13.1, 36.24, 0.25565, 74, -75.21, 34, 0.00065, 2, -138.53, 53.34, 0.02487, 5, 4, -7.95, 108.89, 0.28569, 71, 73.57, 36.73, 0.23154, 72, 7.42, 36.4, 0.4659, 73, -59.18, 36.94, 0.01546, 2, -146.73, 98.68, 0.00141, 4, 4, 28.9, 121.93, 0.65082, 71, 34.64, 33.22, 0.31508, 72, -31.64, 34.87, 0.03404, 73, -98.25, 35.76, 6e-05, 3, 73, 148.26, 101.89, 0.00884, 74, 84.11, 104.46, 0.4181, 2, -43.01, -92.35, 0.57306, 5, 4, -149.68, -8.63, 0.00579, 72, 177.6, 106.66, 0.00036, 73, 111.64, 105.64, 0.04754, 74, 47.39, 107.12, 0.33505, 2, -46.39, -55.69, 0.61127, 5, 4, -114.56, -1.62, 0.05485, 72, 141.98, 110.37, 0.009, 73, 76.05, 109.68, 0.10967, 74, 11.7, 110.09, 0.19656, 2, -49.28, -19.99, 0.62992, 5, 4, -77.8, 8, 0.24848, 72, 104.02, 112.07, 0.04311, 73, 38.1, 111.73, 0.15553, 74, -26.29, 110.99, 0.07049, 2, -54.59, 17.64, 0.48238, 6, 4, -39.51, 21.2, 0.57873, 71, 125.84, 113.89, 0.00527, 72, 63.54, 110.81, 0.10082, 73, -2.39, 110.83, 0.11838, 74, -66.74, 108.88, 0.01104, 2, -63.27, 57.2, 0.18576, 6, 4, -0.57, 33.02, 0.78927, 71, 85.19, 112.07, 0.0516, 72, 22.85, 111.05, 0.11075, 73, -43.07, 111.45, 0.0354, 74, -107.42, 108.27, 1e-05, 2, -70.51, 97.24, 0.01298, 4, 4, 35.71, 42.58, 0.78499, 71, 47.67, 111.78, 0.14155, 72, -14.64, 112.67, 0.06921, 73, -80.54, 113.4, 0.00424], "hull": 25, "edges": [0, 48, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 46, 48, 0, 2, 2, 4, 4, 6, 6, 8, 42, 44, 44, 46, 24, 26, 26, 28, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 256, "height": 309}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.07908, 0.24449, 0.49319, 0.05509, 0.95491, 0.2782, 0.90282, 0.76293, 0.52777, 0.93789, 0.04334, 0.67947], "triangles": [3, 1, 2, 4, 1, 3, 5, 0, 1, 4, 5, 1], "vertices": [2, 6, 57.31, 7.74, 0.96363, 83, -311.39, 56.72, 0.03637, 2, 6, 70.66, -11.49, 0.96074, 83, -298.04, 37.49, 0.03926, 2, 6, 67.46, -37.58, 0.96382, 83, -301.24, 11.4, 0.03618, 2, 6, 46.58, -40.1, 0.96438, 83, -322.11, 8.89, 0.03562, 2, 6, 34.35, -22.72, 0.96, 83, -334.35, 26.27, 0.04, 2, 6, 38.72, 4.92, 0.96338, 83, -329.98, 53.9, 0.03662], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 53, "height": 43}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.15537, 0.06229, 0.62434, 0.07837, 0.95353, 0.53669, 0.8579, 0.93671, 0.30434, 0.90455, 0.04503, 0.60905], "triangles": [3, 4, 2, 4, 5, 1, 4, 1, 2, 1, 5, 0], "vertices": [2, 6, 64.49, 75.55, 0.97221, 83, -304.21, 124.53, 0.02779, 2, 6, 69.31, 54.03, 0.96928, 83, -299.39, 103.01, 0.03072, 2, 6, 54.07, 34.14, 0.96702, 83, -314.63, 83.12, 0.03298, 2, 6, 36.29, 34.21, 0.96699, 83, -332.41, 83.19, 0.03301, 2, 6, 31.16, 59.75, 0.97025, 83, -337.54, 108.74, 0.02975, 2, 6, 40.43, 74.72, 0.97308, 83, -328.27, 123.7, 0.02692], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 47, "height": 43}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 53, -6.34, -10.43, 0.96109, 83, -320.41, 21.56, 0.03891, 2, 53, -10.82, 7, 0.96123, 83, -324.89, 39, 0.03877, 2, 53, 6.61, 11.48, 0.96157, 83, -307.46, 43.48, 0.03843, 2, 53, 11.09, -5.95, 0.96035, 83, -302.98, 26.04, 0.03965], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.25497, 0.15015, 0.39354, 0.10289, 0.47105, 0.02338, 0.64851, 0.13634, 0.84123, 0.16233, 0.98847, 0.32891, 0.85352, 0.6743, 0.74116, 0.82975, 0.58187, 0.93314, 0.40618, 0.9253, 0.26289, 0.7843, 0.16949, 0.64528, 0.09567, 0.52647, 0.01395, 0.51336, 0.01674, 0.44796, 0.08096, 0.38568, 0.164, 0.26677, 0.12771, 0.44179, 0.18919, 0.33816, 0.26339, 0.24506, 0.36727, 0.21169, 0.48068, 0.25209, 0.58774, 0.34167, 0.67572, 0.4699, 0.61106, 0.59285, 0.5199, 0.67892, 0.18919, 0.55773, 0.28777, 0.65082, 0.40649, 0.69649], "triangles": [8, 9, 25, 9, 28, 25, 8, 24, 7, 8, 25, 24, 9, 10, 28, 28, 10, 27, 24, 23, 7, 7, 23, 6, 10, 11, 27, 27, 20, 28, 28, 21, 25, 28, 20, 21, 25, 22, 24, 25, 21, 22, 23, 4, 6, 6, 4, 5, 11, 26, 27, 26, 18, 27, 18, 19, 27, 27, 19, 20, 11, 12, 26, 24, 22, 23, 12, 17, 26, 26, 17, 18, 13, 14, 12, 14, 15, 12, 12, 15, 17, 22, 3, 23, 23, 3, 4, 17, 15, 18, 15, 16, 18, 22, 21, 3, 18, 16, 19, 16, 0, 19, 20, 1, 21, 1, 2, 21, 21, 2, 3, 19, 0, 20, 20, 0, 1], "vertices": [2, 6, 59.73, -4.65, 0.96238, 83, -308.97, 44.34, 0.03762, 2, 6, 63.33, -12.02, 0.96137, 83, -305.37, 36.96, 0.03863, 2, 6, 67.14, -15.68, 0.96084, 83, -301.56, 33.3, 0.03916, 2, 6, 65.88, -26.63, 0.96126, 83, -302.82, 22.35, 0.03874, 2, 6, 67.78, -37.69, 0.96529, 83, -300.92, 11.3, 0.03471, 2, 6, 64.26, -47.41, 0.96915, 83, -304.44, 1.58, 0.03085, 2, 6, 50.6, -42.84, 0.96712, 83, -318.1, 6.15, 0.03288, 2, 6, 43.71, -37.88, 0.96456, 83, -324.99, 11.11, 0.03544, 2, 6, 37.9, -29.83, 0.9613, 83, -330.8, 19.15, 0.0387, 2, 6, 35.63, -19.89, 0.96024, 83, -333.07, 29.09, 0.03976, 2, 6, 38.34, -10.62, 0.96151, 83, -330.36, 38.37, 0.03849, 2, 6, 41.71, -4.16, 0.96241, 83, -326.99, 44.83, 0.03759, 2, 6, 44.67, 1.02, 0.96313, 83, -324.03, 50.01, 0.03687, 2, 6, 43.93, 5.73, 0.96375, 83, -324.76, 54.71, 0.03625, 2, 6, 46.19, 6.14, 0.96382, 83, -322.51, 55.13, 0.03618, 2, 6, 49.23, 3.08, 0.96343, 83, -319.47, 52.06, 0.03657, 2, 6, 54.46, -0.55, 0.96295, 83, -314.24, 48.43, 0.03705, 2, 6, 48, -0.04, 0.96301, 83, -320.7, 48.95, 0.03699, 2, 6, 52.4, -2.59, 0.96268, 83, -316.3, 46.39, 0.03732, 2, 6, 56.63, -5.95, 0.96223, 83, -312.07, 43.04, 0.03777, 2, 6, 59.26, -11.49, 0.96147, 83, -309.44, 37.49, 0.03853, 2, 6, 59.53, -18.22, 0.96058, 83, -309.17, 30.77, 0.03942, 2, 6, 58.04, -25.01, 0.96074, 83, -310.66, 23.98, 0.03926, 2, 6, 54.96, -31.07, 0.96293, 83, -313.74, 17.92, 0.03707, 2, 6, 49.86, -28.51, 0.96163, 83, -318.84, 20.48, 0.03837, 2, 6, 45.63, -24.14, 0.96056, 83, -323.07, 24.85, 0.03944, 2, 6, 44.96, -4.5, 0.9624, 83, -323.74, 44.48, 0.0376, 2, 6, 43.23, -10.85, 0.96154, 83, -325.47, 38.13, 0.03846, 2, 6, 43.39, -17.92, 0.9606, 83, -325.31, 31.07, 0.0394], "hull": 17, "edges": [8, 10, 12, 14, 26, 28, 4, 6, 6, 8, 4, 2, 2, 0, 0, 32, 24, 26, 22, 24, 18, 20, 20, 22, 28, 30, 30, 32, 10, 12, 14, 16, 16, 18, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 34, 52, 52, 54, 54, 56, 56, 50], "width": 58, "height": 35}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 54, -7.17, -10.65, 0.96833, 83, -321.84, 91.4, 0.03167, 2, 54, -11.65, 6.78, 0.97069, 83, -326.32, 108.84, 0.02931, 2, 54, 5.78, 11.27, 0.97084, 83, -308.88, 113.32, 0.02916, 2, 54, 10.26, -6.17, 0.96845, 83, -304.4, 95.88, 0.03155], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.69172, 0.16175, 0.78242, 0.29352, 0.88999, 0.46149, 0.9363, 0.64032, 0.956, 0.75244, 0.98186, 0.82366, 0.97879, 0.8887, 0.8912, 0.88685, 0.7608, 0.8925, 0.62106, 0.9173, 0.43835, 0.97744, 0.20837, 0.85711, 0.07336, 0.66772, 0.03132, 0.38251, 0.02179, 0.08502, 0.2035, 0.03946, 0.3841, 0.09844, 0.51612, 0.07607, 0.29243, 0.32146, 0.43181, 0.27101, 0.58985, 0.28477, 0.73171, 0.35662, 0.83002, 0.49116, 0.87607, 0.64252, 0.9022, 0.80611, 0.80265, 0.80764, 0.65829, 0.80917, 0.51643, 0.76789, 0.38701, 0.65017, 0.29617, 0.49881], "triangles": [11, 28, 10, 10, 27, 9, 10, 28, 27, 9, 26, 8, 9, 27, 26, 8, 25, 7, 8, 26, 25, 5, 6, 24, 6, 7, 24, 7, 25, 24, 11, 29, 28, 11, 12, 29, 24, 4, 5, 23, 25, 26, 24, 25, 23, 22, 23, 26, 4, 23, 3, 4, 24, 23, 27, 28, 20, 21, 22, 26, 26, 27, 21, 27, 20, 21, 20, 28, 19, 12, 13, 29, 28, 29, 19, 3, 23, 2, 23, 22, 2, 13, 18, 29, 29, 18, 19, 2, 22, 1, 22, 21, 1, 13, 15, 18, 13, 14, 15, 1, 21, 0, 18, 16, 19, 18, 15, 16, 21, 20, 0, 19, 17, 20, 20, 17, 0, 19, 16, 17], "vertices": [2, 6, 64.26, 56.45, 0.96907, 83, -304.44, 105.43, 0.03093, 2, 6, 60.76, 51.52, 0.96846, 83, -307.94, 100.51, 0.03154, 2, 6, 56.22, 45.58, 0.9677, 83, -312.48, 94.56, 0.0323, 2, 6, 50.65, 42.09, 0.96723, 83, -318.05, 91.08, 0.03277, 2, 6, 47.06, 40.29, 0.96697, 83, -321.64, 89.28, 0.03303, 2, 6, 44.92, 38.6, 0.96672, 83, -323.78, 87.58, 0.03328, 2, 6, 42.69, 38.16, 0.96663, 83, -326.01, 87.14, 0.03337, 2, 6, 41.81, 41.82, 0.9671, 83, -326.89, 90.81, 0.0329, 2, 6, 40.22, 47.2, 0.96778, 83, -328.47, 96.19, 0.03222, 2, 6, 37.89, 52.81, 0.96847, 83, -330.81, 101.79, 0.03153, 2, 6, 33.89, 59.89, 0.96928, 83, -334.81, 108.88, 0.03072, 2, 6, 35.51, 70.52, 0.97087, 83, -333.19, 119.5, 0.02913, 2, 6, 40.49, 77.79, 0.97321, 83, -328.21, 126.78, 0.02679, 2, 6, 49.7, 82.03, 0.97424, 83, -318.99, 131.01, 0.02576, 2, 6, 59.69, 85.02, 0.97415, 83, -309.01, 134, 0.02585, 2, 6, 63.18, 77.85, 0.97191, 83, -305.52, 126.83, 0.02809, 2, 6, 63.11, 69.81, 0.97085, 83, -305.59, 118.79, 0.02915, 2, 6, 65.28, 64.51, 0.97011, 83, -303.42, 113.49, 0.02989, 2, 6, 54.57, 71.68, 0.97119, 83, -314.13, 120.67, 0.02881, 2, 6, 57.77, 66.32, 0.97046, 83, -310.93, 115.3, 0.02954, 2, 6, 59, 59.62, 0.96956, 83, -309.7, 108.6, 0.03044, 2, 6, 58.08, 53.08, 0.96869, 83, -310.62, 102.07, 0.03131, 2, 6, 54.57, 47.82, 0.968, 83, -314.13, 96.8, 0.032, 2, 6, 49.93, 44.58, 0.96756, 83, -318.77, 93.57, 0.03244, 2, 6, 44.67, 42.07, 0.96718, 83, -324.03, 91.05, 0.03282, 2, 6, 43.55, 46.2, 0.96771, 83, -325.15, 95.19, 0.03229, 2, 6, 41.95, 52.2, 0.96848, 83, -326.75, 101.18, 0.03152, 2, 6, 41.83, 58.47, 0.9693, 83, -326.87, 107.45, 0.0307, 2, 6, 44.44, 64.88, 0.9702, 83, -324.26, 113.87, 0.0298, 2, 6, 48.6, 69.98, 0.97093, 83, -320.1, 118.97, 0.02907], "hull": 18, "edges": [0, 34, 0, 2, 10, 12, 12, 14, 20, 22, 22, 24, 28, 30, 24, 26, 26, 28, 18, 20, 8, 10, 2, 4, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 36, 14, 16, 16, 18, 4, 6, 6, 8, 30, 32, 32, 34], "width": 43, "height": 35}}, "hair_B": {"hair_B": {"type": "mesh", "uvs": [0.38051, 0.00166, 0.50356, 0.00164, 0.57955, 0.00983, 0.68216, 0.04804, 0.72903, 0.09212, 0.77589, 0.1362, 0.80186, 0.18124, 0.80725, 0.24205, 0.7893, 0.2954, 0.74225, 0.34436, 0.71187, 0.39784, 0.72506, 0.44673, 0.79137, 0.48975, 0.91146, 0.55108, 0.94168, 0.61024, 0.94372, 0.68156, 0.9451, 0.73735, 0.95609, 0.7873, 1, 0.8441, 0.99685, 0.90395, 0.91107, 0.9504, 0.66981, 0.98409, 0.46113, 0.99883, 0.30068, 0.99485, 0.38086, 0.96581, 0.28628, 0.9183, 0.15419, 0.88488, 0.19806, 0.86884, 0.23863, 0.84659, 0.23977, 0.81376, 0.19401, 0.76867, 0.12331, 0.71755, 0.09283, 0.67814, 0.09326, 0.61216, 0.11665, 0.55353, 0.09038, 0.49198, 0.0517, 0.448, 0.01454, 0.39636, 0.00692, 0.33498, 0.00647, 0.27527, 0.05427, 0.20864, 0.10899, 0.16467, 0.16936, 0.12122, 0.2336, 0.07232, 0.28446, 0.02367, 0.34567, 0.80256, 0.35603, 0.84717, 0.34049, 0.88756, 0.85621, 0.80015, 0.88472, 0.84416, 0.85881, 0.89961, 0.80957, 0.94663, 0.47784, 0.93036, 0.553, 0.89178, 0.5815, 0.84777, 0.57373, 0.80076, 0.33713, 0.75335, 0.79104, 0.74522, 0.55932, 0.74965], "triangles": [42, 43, 4, 43, 3, 4, 43, 44, 3, 44, 2, 3, 2, 44, 1, 44, 0, 1, 42, 4, 5, 41, 42, 5, 23, 24, 22, 22, 24, 21, 24, 52, 21, 24, 25, 52, 21, 51, 20, 21, 52, 51, 51, 50, 20, 20, 50, 19, 52, 53, 51, 51, 53, 50, 25, 47, 52, 52, 47, 53, 25, 26, 47, 47, 26, 27, 19, 50, 18, 53, 54, 50, 47, 46, 53, 18, 50, 49, 50, 54, 49, 53, 46, 54, 47, 27, 46, 46, 27, 28, 46, 55, 54, 54, 48, 49, 54, 55, 48, 28, 29, 46, 29, 45, 46, 46, 45, 55, 49, 48, 18, 48, 17, 18, 29, 30, 45, 55, 45, 58, 55, 57, 48, 48, 57, 17, 14, 32, 33, 30, 56, 45, 45, 56, 58, 55, 58, 57, 57, 16, 17, 30, 31, 56, 56, 31, 58, 57, 58, 15, 16, 57, 15, 15, 58, 31, 31, 32, 15, 32, 14, 15, 14, 33, 13, 13, 33, 34, 13, 34, 12, 12, 35, 11, 12, 34, 35, 35, 36, 11, 36, 10, 11, 36, 37, 10, 9, 10, 38, 10, 37, 38, 9, 38, 8, 40, 8, 39, 7, 8, 40, 7, 40, 41, 8, 38, 39, 6, 41, 5, 41, 6, 7], "vertices": [2, 6, 202.97, -46.66, 0.98461, 84, -118.17, 2.33, 0.01539, 2, 6, 207.18, -62.98, 0.98411, 84, -113.96, -14, 0.01589, 3, 81, 147.5, 21.63, 0.08306, 6, 205.1, -74.26, 0.90073, 84, -116.04, -25.28, 0.01621, 3, 81, 127.85, 3.79, 0.29538, 6, 186.79, -93.48, 0.68798, 84, -134.34, -44.5, 0.01665, 3, 81, 103.45, -7.16, 0.4508, 6, 163.25, -106.16, 0.53418, 84, -157.89, -57.18, 0.01503, 3, 81, 79.05, -18.1, 0.60524, 6, 139.7, -118.84, 0.38263, 84, -181.44, -69.86, 0.01214, 3, 81, 53.58, -26.33, 0.75099, 6, 114.89, -128.89, 0.24, 84, -206.25, -79.91, 0.00901, 4, 81, 18.47, -33.44, 0.95021, 7, 160.1, -12.11, 0.0017, 6, 80.39, -138.52, 0.04338, 84, -240.75, -89.54, 0.00471, 3, 81, -12.89, -36.62, 0.91711, 7, 128.6, -13.3, 0.08202, 84, -271.8, -94.98, 0.00087, 2, 81, -42.42, -35.41, 0.70216, 7, 99.21, -10.23, 0.29784, 2, 81, -74.16, -36.93, 0.40094, 7, 67.44, -9.74, 0.59906, 2, 81, -102.16, -43.84, 0.15415, 7, 39.05, -14.87, 0.84585, 2, 81, -125.48, -57.3, 0.02279, 7, 14.92, -26.82, 0.97721, 3, 7, -19.05, -47.34, 0.84152, 75, 9.45, 54.09, 0.13004, 76, -53.89, 50.99, 0.02844, 3, 7, -53.18, -55.48, 0.17688, 75, 44.54, 53.71, 0.41544, 76, -18.85, 52.69, 0.40768, 4, 7, -94.88, -60.62, 0.0033, 75, 86.24, 48.58, 0.15073, 76, 23.08, 50.04, 0.83942, 77, -54.34, 46.88, 0.00654, 3, 75, 118.85, 44.53, 0.00414, 76, 55.87, 47.93, 0.80009, 77, -21.48, 46.74, 0.19577, 3, 76, 85.33, 47.38, 0.29856, 77, 7.96, 47.94, 0.68482, 78, -58.5, 36.46, 0.01662, 3, 76, 119.13, 51.05, 0.01669, 77, 41.47, 53.62, 0.74595, 78, -26.92, 49.06, 0.23736, 3, 77, 76.72, 52.83, 0.30683, 78, 7.7, 55.69, 0.67465, 79, -48.68, 44.27, 0.01852, 3, 77, 103.96, 40.8, 0.06377, 78, 36.86, 49.66, 0.7853, 79, -18.97, 46.26, 0.15093, 2, 78, 62.92, 21.24, 0.21936, 79, 13.74, 25.87, 0.78064, 1, 79, 34.49, 4.37, 1, 1, 79, 42.42, -16.27, 1, 3, 77, 112.3, -31.93, 0.00363, 78, 60.3, -19.7, 0.05233, 79, 22.19, -14.27, 0.94403, 3, 77, 84.18, -44.6, 0.17484, 78, 35.47, -37.99, 0.62579, 79, 3.17, -38.55, 0.19937, 4, 76, 135.01, -66.22, 0.00107, 77, 64.31, -62.5, 0.3956, 78, 19.81, -59.67, 0.57096, 79, -6.11, -63.63, 0.03237, 4, 76, 126, -59.56, 0.00802, 77, 54.93, -56.39, 0.45828, 78, 9.35, -55.67, 0.51363, 79, -17.26, -62.58, 0.02008, 4, 76, 113.32, -53.1, 0.04782, 77, 41.88, -50.7, 0.62046, 78, -4.6, -52.85, 0.32864, 79, -31.46, -63.6, 0.00308, 3, 76, 94.04, -51.6, 0.22653, 77, 22.55, -50.35, 0.67271, 78, -23.57, -56.56, 0.10076, 4, 75, 123.89, -59.88, 0.01557, 76, 67.1, -56, 0.63724, 77, -4.07, -56.35, 0.33995, 78, -48.34, -68.02, 0.00724, 3, 75, 92.78, -65.61, 0.14681, 76, 36.39, -63.57, 0.78384, 77, -34.28, -65.73, 0.06935, 3, 75, 69.23, -66.76, 0.25881, 76, 12.95, -66.11, 0.72832, 77, -57.53, -69.67, 0.01287, 4, 81, -213.47, 23.97, 2e-05, 7, -67.75, 59.84, 0.0014, 75, 30.69, -61.7, 0.3721, 76, -25.82, -63.34, 0.62648, 4, 81, -178.92, 26.97, 0.10056, 7, -33.08, 60.65, 0.67142, 75, -3.13, -54.08, 0.1091, 76, -60.04, -57.74, 0.11892, 2, 81, -143.89, 36.97, 0.20781, 7, 2.52, 68.42, 0.79219, 2, 81, -119.34, 46.8, 0.31886, 7, 27.63, 76.68, 0.68114, 3, 81, -90.32, 57.23, 0.47831, 7, 57.26, 85.25, 0.5142, 6, -34.67, -55.96, 0.00749, 3, 81, -54.93, 64.7, 0.6171, 7, 93.05, 90.47, 0.26462, 6, 0.08, -45.95, 0.11827, 4, 81, -20.34, 71.03, 0.67345, 7, 127.97, 94.6, 0.09072, 6, 34.13, -37.14, 0.23456, 84, -287.01, 11.85, 0.00126, 4, 81, 19.45, 71.58, 0.36526, 7, 167.71, 92.63, 0.00624, 6, 73.77, -33.71, 0.62229, 84, -247.37, 15.27, 0.00621, 4, 81, 46.27, 68.81, 0.17809, 7, 194.3, 88.18, 0.00015, 6, 100.72, -34.53, 0.8122, 84, -220.42, 14.46, 0.00956, 3, 81, 72.92, 65.24, 0.04014, 6, 127.56, -36.17, 0.94712, 84, -193.57, 12.82, 0.01274, 2, 6, 157.65, -37.52, 0.98455, 84, -163.49, 11.46, 0.01545, 2, 6, 187.14, -37.14, 0.98453, 84, -134, 11.85, 0.01547, 3, 76, 88.47, -36.67, 0.26777, 77, 16.1, -35.77, 0.68106, 78, -32.94, -43.67, 0.05117, 4, 76, 114.78, -37.08, 0.03705, 77, 42.38, -34.62, 0.68268, 78, -7.49, -37.02, 0.27925, 79, -38.48, -49.12, 0.00102, 4, 76, 138.36, -40.87, 0.00091, 77, 66.15, -36.99, 0.33302, 78, 16.25, -34.34, 0.61662, 79, -16.33, -40.19, 0.04945, 3, 76, 91.93, 33.21, 0.18008, 77, 15.39, 34.18, 0.79624, 78, -48.34, 24.57, 0.02367, 3, 76, 118.06, 35.29, 0.01408, 77, 41.35, 37.83, 0.78127, 78, -23.73, 33.59, 0.20465, 3, 77, 73.97, 33.94, 0.27217, 78, 8.99, 36.65, 0.7095, 79, -42.34, 26.27, 0.01832, 3, 77, 101.6, 26.92, 0.03139, 78, 37.47, 35.59, 0.75209, 79, -14.62, 32.87, 0.21652, 3, 77, 91.55, -18.43, 0.01694, 78, 37.18, -10.86, 0.71396, 79, -2.46, -11.95, 0.2691, 3, 77, 68.93, -7.9, 0.05914, 78, 12.85, -5.32, 0.94031, 79, -27.38, -13.13, 0.00055, 3, 76, 117.28, -6.29, 0.00017, 77, 43.05, -3.73, 0.98995, 78, -13.32, -6.68, 0.00988, 3, 76, 89.58, -5.43, 0.01229, 77, 15.35, -4.52, 0.9868, 78, -40.24, -13.27, 0.0009, 4, 75, 117.47, -39.28, 0.01361, 76, 59.47, -35.81, 0.74218, 77, -12.9, -36.65, 0.2428, 78, -61.11, -50.62, 0.0014, 3, 75, 120.73, 23, 4e-05, 76, 59.03, 26.55, 0.81723, 77, -17.05, 25.58, 0.18273, 2, 76, 59.42, -5.3, 0.98743, 77, -14.77, -6.19, 0.01257], "hull": 45, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 78, 80, 6, 8, 8, 10, 86, 88, 84, 86, 80, 82, 82, 84, 16, 18, 18, 20, 74, 76, 76, 78, 12, 14, 14, 16, 88, 0, 32, 34, 56, 58, 50, 52, 46, 48, 48, 50, 40, 42, 62, 30], "width": 137, "height": 589}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.51916, 1e-05, 0.60118, 0.02276, 0.67047, 0.08181, 0.73689, 0.08086, 0.7885, 0.11734, 0.81976, 0.18338, 0.86394, 0.28752, 0.9061, 0.42974, 0.92859, 0.56015, 0.95251, 0.68329, 0.98245, 0.81837, 0.998, 0.88941, 0.99697, 0.96789, 0.9395, 0.83202, 0.79371, 0.83152, 0.5995, 0.86785, 0.44134, 0.78136, 0.29255, 0.80318, 0.15616, 0.89573, 0.12028, 0.9929, 0.08807, 0.99999, 0.03818, 0.93866, 0, 0.83538, 0.01032, 0.67252, 0.06742, 0.47623, 0.12565, 0.30882, 0.20666, 0.14572, 0.32006, 0.04221, 0.41808, 0.00284, 0.60777, 0.57703, 0.62546, 0.4419, 0.64977, 0.32533, 0.676, 0.20038, 0.65594, 0.12356, 0.55399, 0.14618, 0.48621, 0.22089, 0.40875, 0.33368, 0.33551, 0.48216, 0.29994, 0.65119, 0.12753, 0.73803, 0.13661, 0.54456, 0.19041, 0.3912, 0.26479, 0.26164, 0.34043, 0.157, 0.42389, 0.10132, 0.5183, 0.08164, 0.45141, 0.66084, 0.47321, 0.52045, 0.56333, 0.2869, 0.71115, 0.12078, 0.75324, 0.19856, 0.60766, 0.09735, 0.51052, 0.39252, 0.59784, 0.71843, 0.7691, 0.71759, 0.75387, 0.5702, 0.74817, 0.43924, 0.75102, 0.31116], "triangles": [13, 14, 9, 10, 13, 9, 11, 13, 10, 12, 13, 11, 55, 7, 8, 55, 56, 7, 54, 55, 8, 54, 8, 9, 53, 46, 29, 29, 30, 55, 16, 46, 53, 14, 54, 9, 53, 54, 15, 16, 53, 15, 55, 53, 29, 54, 53, 55, 15, 54, 14, 30, 56, 55, 18, 39, 17, 21, 22, 39, 18, 21, 39, 19, 21, 18, 20, 21, 19, 40, 24, 41, 38, 40, 37, 23, 24, 40, 39, 23, 40, 39, 40, 38, 16, 38, 46, 17, 39, 38, 17, 38, 16, 22, 23, 39, 42, 26, 43, 25, 26, 42, 41, 25, 42, 24, 25, 41, 37, 42, 36, 41, 42, 37, 37, 40, 41, 46, 37, 47, 38, 37, 46, 46, 47, 29, 27, 28, 44, 43, 27, 44, 26, 27, 43, 36, 43, 44, 42, 43, 36, 47, 36, 52, 37, 36, 47, 36, 44, 35, 36, 35, 52, 47, 52, 29, 29, 52, 30, 5, 6, 57, 57, 50, 5, 50, 3, 4, 57, 32, 50, 49, 3, 50, 44, 28, 45, 35, 45, 34, 45, 0, 1, 33, 51, 2, 33, 2, 49, 32, 33, 49, 33, 32, 51, 51, 1, 2, 34, 32, 48, 51, 32, 34, 45, 1, 51, 34, 45, 51, 35, 34, 48, 45, 28, 0, 35, 44, 45, 31, 48, 32, 52, 35, 48, 30, 48, 31, 49, 2, 3, 32, 49, 50, 52, 48, 30, 31, 32, 57, 56, 31, 57, 30, 31, 56, 50, 4, 5, 6, 56, 57, 56, 6, 7], "vertices": [3, 22, 8.65, 27.25, 0.90686, 23, 0.5, -9.58, 0.06787, 84, -166.97, 107.74, 0.02527, 2, 22, 9.69, 12.53, 0.97529, 84, -165.93, 93.01, 0.02471, 2, 22, 6.05, -1.07, 0.97897, 84, -169.57, 79.42, 0.02103, 2, 22, 9.09, -12.43, 0.97663, 84, -166.54, 68.06, 0.02337, 2, 22, 7.22, -22.34, 0.97836, 84, -168.4, 58.15, 0.02164, 3, 22, 1.12, -29.62, 0.89144, 27, 5.95, 5.72, 0.0938, 84, -174.5, 50.87, 0.01476, 3, 22, -8.74, -40.23, 0.59943, 27, 20.4, 6.6, 0.39137, 84, -184.36, 40.26, 0.0092, 4, 22, -22.99, -51.6, 0.20258, 27, 38.57, 4.99, 0.03922, 28, 4.05, 5.92, 0.75398, 84, -198.61, 28.89, 0.00422, 2, 28, 19.8, 5.08, 0.99739, 84, -212.4, 21.24, 0.00261, 3, 28, 34.81, 4.74, 0.63239, 29, -0.43, 4.82, 0.36681, 84, -225.3, 13.55, 0.0008, 1, 29, 16.16, 3.15, 1, 1, 29, 24.87, 2.24, 1, 1, 29, 33.16, -1.7, 1, 4, 23, -48.07, 102.91, 0.00043, 24, -22.75, 135.38, 0.00021, 25, 3.23, 150.31, 0.00021, 29, 14.49, -4.44, 0.99915, 7, 23, -23.05, 96.59, 0.04638, 24, -4.85, 116.8, 0.02042, 25, 10.74, 125.63, 0.01961, 26, 35.7, 123.67, 0.00019, 27, 69.88, -35.32, 0.03947, 28, 42.8, -27.31, 0.52062, 29, 3.83, -27.93, 0.35331, 7, 23, 11.33, 92.38, 0.17131, 24, 22.11, 95.05, 0.09486, 25, 24.88, 94.01, 0.09256, 26, 32.93, 89.14, 0.00733, 27, 56.81, -67.39, 0.11485, 28, 36.41, -61.35, 0.44534, 29, -6.43, -61.02, 0.07375, 7, 23, 36.03, 75.77, 0.20952, 24, 34.3, 67.9, 0.19452, 25, 23.41, 64.28, 0.23296, 26, 17.38, 63.76, 0.04774, 27, 34.31, -86.89, 0.08275, 28, 18.26, -84.95, 0.21642, 29, -27.16, -82.38, 0.01608, 7, 23, 62.2, 71.86, 0.08194, 24, 54.45, 50.75, 0.15485, 25, 33.57, 39.85, 0.44544, 26, 14.57, 37.45, 0.23658, 27, 23.69, -111.12, 0.02031, 28, 12.69, -110.82, 0.05938, 29, -35.66, -107.43, 0.00151, 8, 23, 88.24, 76.51, 0.00444, 24, 79.02, 40.93, 0.00968, 25, 51, 19.94, 0.10368, 26, 20.3, 11.62, 0.87062, 27, 21.35, -137.47, 0.00089, 28, 15.68, -137.11, 0.00379, 29, -35.72, -133.89, 0, 84, -284.46, 143.88, 0.00689, 7, 23, 97.16, 86, 2e-05, 24, 91.61, 44.27, 2e-05, 25, 63.73, 17.2, 0.00018, 26, 30.16, 3.11, 0.98785, 27, 28.18, -148.57, 0, 28, 24.58, -146.61, 2e-05, 84, -297.05, 147.2, 0.01192, 2, 26, 29.82, -2.64, 0.98378, 84, -299.27, 152.52, 0.01622, 2, 26, 21.01, -9.84, 0.97876, 84, -294.52, 162.86, 0.02124, 3, 25, 52.36, -8.55, 0.01511, 26, 7.81, -14.03, 0.96071, 84, -284.5, 172.41, 0.02418, 3, 25, 33.6, -12.39, 0.88432, 26, -10.48, -8.39, 0.09457, 84, -265.59, 175.38, 0.02111, 3, 24, 54.67, -4.49, 0.00103, 25, 8.68, -9.46, 0.98219, 84, -240.83, 171.31, 0.01678, 2, 24, 33.43, -10.7, 0.98431, 84, -219.3, 166.2, 0.01569, 3, 23, 58.29, -6.45, 0.00403, 24, 9.75, -13.67, 0.98041, 84, -197.25, 157.07, 0.01556, 4, 22, -4.9, 60.15, 0.13721, 23, 35.88, -13.33, 0.83391, 24, -12.91, -7.67, 0.01106, 84, -180.52, 140.64, 0.01781, 3, 22, 3.88, 44.5, 0.52123, 23, 17.93, -13.6, 0.45691, 84, -171.74, 124.98, 0.02186, 7, 23, 1.65, 59.72, 0.24975, 24, -3.36, 72.44, 0.08647, 25, -8.08, 85.43, 0.06782, 26, -0.1, 97.44, 0.00425, 27, 27.83, -49.51, 0.23082, 28, 4.43, -49.62, 0.33564, 29, -36.85, -45.69, 0.02525, 8, 22, -36.74, -3.84, 0.25529, 23, -5.22, 45.14, 0.23809, 24, -16.9, 63.7, 0.04067, 25, -24.12, 83.79, 0.02672, 26, -14.95, 103.69, 0.00082, 27, 15.56, -39.06, 0.27848, 28, -9.68, -41.84, 0.15566, 29, -49.97, -36.34, 0.00427, 9, 22, -22.46, -4.61, 0.56789, 23, -12.7, 32.96, 0.15111, 24, -29.69, 57.3, 0.00964, 25, -38.42, 83.9, 0.006, 26, -27.44, 110.66, 3e-05, 27, 5.76, -28.64, 0.22598, 28, -21.37, -33.59, 0.03822, 29, -60.64, -26.81, 0.00013, 84, -198.08, 75.87, 0.00099, 7, 22, -7.14, -5.47, 0.89474, 23, -20.75, 19.9, 0.03244, 24, -43.43, 50.47, 0.00034, 25, -53.75, 84.06, 0.00024, 27, -4.74, -17.45, 0.06202, 28, -33.88, -24.73, 0.00129, 84, -182.76, 75.02, 0.00893, 2, 22, 0.68, 0.2, 0.98425, 84, -174.94, 80.69, 0.01575, 7, 22, -6.37, 17.02, 0.90397, 23, -1.34, 8.51, 0.077, 24, -32.97, 30.55, 0.00035, 25, -53.49, 61.55, 0.00018, 27, -20.81, -33.21, 0.00808, 28, -46.49, -43.38, 0.00058, 84, -181.99, 97.51, 0.00985, 8, 22, -17.83, 26.47, 0.58646, 23, 12.42, 14.08, 0.36103, 24, -18.34, 28, 0.01515, 25, -41.61, 52.64, 0.00414, 26, -45.25, 84.76, 2e-05, 27, -19.04, -47.94, 0.02241, 28, -41.8, -57.46, 0.00787, 84, -193.45, 106.95, 0.00291, 8, 22, -34.02, 36.46, 0.16562, 23, 28.92, 23.56, 0.50724, 24, 0.68, 27.33, 0.21926, 25, -24.97, 43.41, 0.03916, 26, -35.09, 68.67, 0.00182, 27, -14.21, -66.35, 0.03444, 28, -33.39, -74.54, 0.0323, 29, -77.28, -66.1, 0.00015, 7, 23, 45.71, 37.27, 0.22233, 24, 22.17, 30.09, 0.46905, 25, -4.57, 36.11, 0.2058, 26, -20.69, 52.47, 0.02026, 27, -5.38, -86.15, 0.02889, 28, -20.78, -92.16, 0.05289, 29, -66.78, -85.06, 0.00077, 7, 23, 56.62, 54.92, 0.11151, 24, 40.76, 39.32, 0.2537, 25, 16.19, 35.88, 0.43737, 26, -2.59, 42.32, 0.10823, 27, 8.81, -101.29, 0.0243, 28, -3.85, -104.17, 0.06339, 29, -51.34, -98.93, 0.00151, 7, 23, 88.69, 57.38, 0.00442, 24, 69.29, 24.46, 0.01187, 25, 34.85, 9.69, 0.59579, 26, 1.21, 10.38, 0.37664, 27, 2.78, -132.89, 0.00085, 28, -3.44, -136.33, 0.00359, 84, -267.85, 153.38, 0.00683, 7, 23, 81.64, 35.81, 0.0027, 24, 51.9, 9.87, 0.03513, 25, 12.74, 4.59, 0.94823, 26, -20.64, 16.52, 0.00441, 27, -16.19, -120.44, 0.00043, 28, -24.52, -127.92, 0.00163, 84, -245.53, 157.46, 0.00746, 7, 23, 68.05, 20.71, 0.00509, 24, 32.39, 4.24, 0.93206, 25, -7.21, 8.43, 0.05449, 26, -36.29, 29.47, 0.00044, 27, -27.21, -103.37, 0.0005, 28, -38.72, -113.4, 0.00151, 84, -225.78, 152.7, 0.00591, 7, 23, 51.6, 9.2, 0.029, 24, 12.34, 3.16, 0.96228, 25, -25.56, 16.57, 0.00284, 26, -48.48, 45.42, 1e-05, 27, -34.01, -84.48, 0.00052, 28, -49.17, -96.25, 0.00106, 84, -207.82, 143.72, 0.0043, 7, 22, -17.01, 53.32, 0.14675, 23, 35.64, 0.57, 0.84046, 24, -5.77, 4.26, 0.00611, 25, -41.19, 25.78, 0.00012, 27, -38.16, -66.82, 7e-05, 28, -56.76, -79.78, 0.0001, 84, -192.63, 133.81, 0.00638, 3, 22, -7.02, 40.63, 0.49879, 23, 19.73, -2.17, 0.48993, 84, -182.64, 121.12, 0.01129, 3, 22, -0.63, 25.02, 0.85277, 23, 2.96, -0.35, 0.13244, 84, -176.25, 105.51, 0.01479, 7, 23, 30.88, 62.52, 0.2462, 24, 22.93, 59.37, 0.20961, 25, 9.41, 61.85, 0.20584, 26, 3.92, 68.35, 0.03532, 27, 22.88, -78.45, 0.09149, 28, 5.37, -78.97, 0.19896, 29, -39.29, -74.96, 0.01257, 7, 23, 23.15, 47.52, 0.36999, 24, 8.44, 50.72, 0.20781, 25, -7.43, 60.72, 0.12674, 26, -11.39, 75.44, 0.01367, 27, 10.43, -67.06, 0.11944, 28, -9.11, -70.3, 0.15609, 29, -52.67, -64.69, 0.00626, 9, 22, -21.91, 11.32, 0.55934, 23, 1.05, 24.88, 0.28337, 24, -22.28, 43.18, 0.01721, 25, -38.23, 67.96, 0.00782, 26, -34.93, 96.58, 9e-05, 27, -5.64, -39.8, 0.10633, 28, -30.3, -46.8, 0.02443, 29, -71.02, -38.91, 6e-05, 84, -197.53, 91.81, 0.00133, 2, 22, 3.43, -9.18, 0.98194, 84, -172.19, 71.31, 0.01806, 7, 22, -3.53, -18.66, 0.88882, 23, -34.06, 23.01, 0.00489, 24, -53.09, 60.15, 5e-05, 25, -57.97, 97.06, 4e-05, 27, 1.75, -5.42, 0.09403, 28, -29.93, -11.64, 0.00022, 84, -179.15, 61.83, 0.01195, 2, 22, 1.52, 9.24, 0.9836, 84, -174.1, 89.73, 0.0164, 8, 22, -36.21, 17.3, 0.25336, 23, 13.12, 34.6, 0.40996, 24, -6.9, 45.06, 0.09347, 25, -23.67, 62.65, 0.04037, 26, -24.71, 84.93, 0.00213, 27, 0.59, -53.99, 0.12206, 28, -21.36, -59.46, 0.07732, 29, -63.6, -52.51, 0.00134, 7, 23, 7.37, 75.35, 0.19465, 24, 9.75, 82.68, 0.09922, 25, 8.25, 88.6, 0.09202, 26, 15.75, 92.38, 0.00759, 27, 41.41, -59.12, 0.14191, 28, 19.66, -56.32, 0.41017, 29, -22.49, -54.1, 0.05444, 7, 23, -22.06, 82.6, 0.06091, 24, -11.4, 104.4, 0.0245, 25, -0.73, 117.55, 0.02294, 26, 21.77, 122.09, 0.0003, 27, 56.12, -32.61, 0.07615, 28, 28.78, -27.41, 0.6264, 29, -10.12, -26.43, 0.1888, 8, 23, -23.63, 65.22, 0.07844, 24, -21.92, 90.47, 0.02135, 25, -16.43, 109.92, 0.01837, 26, 4.33, 122.93, 0.00017, 27, 39.76, -26.55, 0.2392, 28, 11.53, -24.74, 0.60802, 29, -26.94, -21.79, 0.03439, 84, -221.24, 50.9, 7e-05, 9, 22, -31.03, -24.8, 0.27719, 23, -26.36, 50.11, 0.06553, 24, -32.23, 79.09, 0.00959, 25, -30.78, 104.46, 0.00752, 26, -10.88, 125.03, 2e-05, 27, 25.89, -19.95, 0.4214, 28, -3.38, -21.05, 0.21535, 29, -41.33, -16.41, 0.00174, 84, -206.65, 55.69, 0.00167, 7, 22, -16.39, -21.56, 0.60737, 23, -30.49, 35.69, 0.03033, 24, -43.35, 69.03, 0.00169, 25, -45.26, 100.55, 0.00127, 27, 13.06, -12.19, 0.33667, 28, -17.5, -16.02, 0.01796, 84, -192.01, 58.93, 0.00472], "hull": 29, "edges": [0, 56, 22, 24, 30, 32, 38, 40, 44, 46, 40, 42, 42, 44, 36, 38, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 2, 6, 8, 2, 4, 4, 6, 12, 14, 26, 24, 26, 28, 28, 30, 18, 20, 20, 22, 32, 34, 34, 36, 8, 10, 10, 12, 14, 16, 16, 18], "width": 177, "height": 117}}, "hair_R": {"hair_R": {"type": "mesh", "uvs": [1, 0.10329, 0.90392, 0.15477, 0.78873, 0.22023, 0.72665, 0.2782, 0.69639, 0.3507, 0.68998, 0.43344, 0.62369, 0.51601, 0.5032, 0.56933, 0.38613, 0.61301, 0.246, 0.65677, 0.21873, 0.68998, 0.25996, 0.71614, 0.39493, 0.75056, 0.44311, 0.79134, 0.41382, 0.84127, 0.33699, 0.87764, 0.23938, 0.89307, 0.19516, 0.92635, 0.24548, 0.96125, 0.33827, 0.97004, 0.32655, 0.99927, 0.18336, 0.99504, 0.09009, 0.94699, 0.09066, 0.90199, 0.15034, 0.86285, 0.23286, 0.84346, 0.28945, 0.81677, 0.25924, 0.78804, 0.16497, 0.75932, 0.07706, 0.73833, 0.0044, 0.68081, 0.00758, 0.61728, 0.08483, 0.55605, 0.188, 0.50545, 0.29296, 0.46351, 0.35522, 0.43049, 0.28595, 0.35175, 0.28348, 0.27355, 0.34001, 0.18004, 0.43158, 0.09665, 0.56609, 0.03949, 0.73262, 0.00749, 0.88655, 0], "triangles": [21, 18, 20, 20, 18, 19, 21, 17, 18, 21, 22, 17, 22, 23, 17, 23, 24, 17, 17, 24, 16, 24, 25, 16, 16, 25, 15, 25, 26, 15, 15, 26, 14, 14, 26, 13, 26, 12, 13, 26, 27, 12, 27, 11, 12, 27, 28, 11, 28, 10, 11, 28, 29, 10, 29, 30, 10, 10, 30, 31, 9, 10, 31, 31, 32, 9, 32, 33, 9, 9, 33, 8, 33, 34, 8, 8, 34, 7, 34, 35, 7, 7, 35, 6, 6, 35, 5, 5, 35, 4, 35, 36, 4, 36, 37, 4, 4, 37, 3, 37, 38, 3, 2, 3, 39, 3, 38, 39, 39, 40, 2, 2, 40, 1, 1, 40, 41, 41, 42, 1, 1, 42, 0], "vertices": [2, 30, -2.17, -14.53, 0.97227, 31, -13.26, 15.88, 0.02773, 2, 30, -13.81, -7.7, 0.46414, 31, 0.23, 16.18, 0.53586, 3, 30, -28.44, 0.31, 0.02069, 31, 16.89, 17.05, 0.93217, 32, -2.68, 31.95, 0.04714, 2, 31, 28.79, 20.56, 0.66357, 32, 7.28, 24.54, 0.33643, 2, 31, 40.3, 28.2, 0.17629, 32, 20.3, 19.93, 0.82371, 3, 31, 51.43, 38.85, 0.00991, 32, 35.5, 17.43, 0.83393, 33, -14.74, 9.36, 0.15616, 2, 32, 49.95, 9.06, 0.12908, 33, 0.46, 16.27, 0.87092, 1, 33, 15.96, 15.55, 1, 2, 33, 30, 13.73, 0.99846, 34, 15.53, 25.58, 0.00154, 2, 33, 45.73, 10.38, 0.66569, 34, 11.11, 10.12, 0.33431, 2, 33, 51.89, 13.12, 0.0888, 34, 13.41, 3.79, 0.9112, 2, 33, 52.16, 19.47, 0.001, 34, 19.73, 3.08, 0.999, 2, 34, 33.81, 7.67, 0.63381, 35, -7.53, -2.14, 0.36619, 1, 35, -4.84, 6.41, 1, 1, 35, 4.11, 10.22, 1, 1, 35, 14.2, 8.8, 1, 2, 35, 22.64, 3.3, 0.98691, 36, 3.08, 9.07, 0.01309, 2, 35, 30.19, 3.97, 0.13194, 36, 3.94, 1.54, 0.86806, 1, 36, 11.98, 0.01, 1, 1, 36, 19.92, 4.9, 1, 1, 36, 22.67, 0.07, 1, 1, 36, 11.57, -8.78, 1, 2, 35, 39.85, -1.47, 0.17392, 36, -1.27, -8.25, 0.82608, 2, 35, 33.44, -6.85, 0.74173, 36, -6.8, -1.97, 0.25827, 1, 35, 24.07, -7.06, 1, 1, 35, 16.03, -3.17, 1, 2, 34, 34.76, -8.45, 0.13121, 35, 8.62, -2.12, 0.86879, 2, 34, 28.91, -6.65, 0.91702, 35, 6.48, -7.86, 0.08298, 1, 34, 18.47, -9.22, 1, 1, 34, 9.47, -12.4, 1, 2, 33, 66.4, -2.45, 0.24364, 34, -3.12, -9.63, 0.75636, 2, 33, 58.2, -10.96, 0.94201, 34, -11.04, -0.86, 0.05799, 1, 33, 44.87, -14.21, 1, 1, 33, 30.99, -14.27, 1, 2, 32, 36.28, -22.26, 0.0653, 33, 18.05, -13.02, 0.9347, 2, 32, 30.94, -15.4, 0.45574, 33, 9.36, -13.4, 0.54426, 3, 31, 69.72, 0.17, 4e-05, 32, 15.57, -20.43, 0.96874, 33, 4.55, -28.84, 0.03122, 2, 31, 59.81, -10.48, 0.12693, 32, 1.1, -18.9, 0.87307, 2, 31, 43.72, -19.13, 0.82734, 32, -15.49, -11.24, 0.17266, 2, 31, 26.43, -24.02, 1, 32, -29.78, -0.36, 0, 2, 30, -1.37, 30.03, 0.00335, 31, 9.47, -22.45, 0.99665, 2, 30, 8.5, 15.54, 0.33283, 31, -6.54, -15.31, 0.66717, 2, 30, 13.65, 1.13, 0.95471, 31, -18.49, -5.75, 0.04529], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 4, 6, 24, 26, 40, 42, 42, 44, 70, 72, 76, 78, 82, 84, 78, 80, 80, 82, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76], "width": 99, "height": 186}}, "head": {"head": {"type": "mesh", "uvs": [0.59449, 0.01387, 0.6431, 0.00187, 0.69246, 0.00445, 0.74659, 0.02294, 0.78567, 0.05661, 0.81607, 0.1066, 0.84647, 0.15658, 0.87246, 0.206, 0.91087, 0.26246, 0.94878, 0.32481, 0.94156, 0.38546, 0.8965, 0.44233, 0.86848, 0.47563, 0.86726, 0.49693, 0.89001, 0.51354, 0.92859, 0.52034, 0.94941, 0.51261, 0.96418, 0.48614, 0.98293, 0.50723, 0.97052, 0.53031, 0.93498, 0.54126, 0.89675, 0.54098, 0.94392, 0.56652, 0.9849, 0.59573, 1, 0.62488, 0.9909, 0.65708, 0.94487, 0.69737, 0.92249, 0.72328, 0.92257, 0.76458, 0.93982, 0.79691, 0.97882, 0.84852, 0.97997, 0.90462, 0.87464, 0.9523, 0.7693, 0.99999, 0.76785, 0.95539, 0.75563, 0.91349, 0.73743, 0.86954, 0.7375, 0.82112, 0.74566, 0.77049, 0.73638, 0.7351, 0.70958, 0.70886, 0.63831, 0.66939, 0.5714, 0.62106, 0.5447, 0.57741, 0.52534, 0.52409, 0.54034, 0.46221, 0.57361, 0.41223, 0.61463, 0.36075, 0.66483, 0.29996, 0.70461, 0.24879, 0.72264, 0.20363, 0.72279, 0.15488, 0.71579, 0.11558, 0.6995, 0.07985, 0.67458, 0.05742, 0.65142, 0.05276, 0.6252, 0.05698, 0.61325, 0.06514, 0.65433, 0.10578, 0.67837, 0.15823, 0.67319, 0.20569, 0.6371, 0.26823, 0.63336, 0.28452, 0.61125, 0.31128, 0.54667, 0.34476, 0.54723, 0.35111, 0.54777, 0.35716, 0.54929, 0.3743, 0.54273, 0.40561, 0.52671, 0.40719, 0.51045, 0.37366, 0.51577, 0.35781, 0.51741, 0.35292, 0.51945, 0.34683, 0.50617, 0.34298, 0.47404, 0.37157, 0.42871, 0.39529, 0.33716, 0.41794, 0.2241, 0.43936, 0.16733, 0.43671, 0.12942, 0.42525, 0.08752, 0.39293, 0.05565, 0.36282, 0.0377, 0.32972, 0.0302, 0.28997, 0.03125, 0.25185, 0.04312, 0.22097, 0.00199, 0.17364, 0.00362, 0.11628, 0.03896, 0.07301, 0.10878, 0.03755, 0.19093, 0.01708, 0.26273, 0.00289, 0.35623, 1e-05, 0.48094, 0.01772, 0.54775, 0.03572, 0.56655, 0.03109, 0.05093, 0.18667, 0.08241, 0.14482, 0.12924, 0.10976, 0.22366, 0.09427, 0.29952, 0.09712, 0.41658, 0.13503, 0.49567, 0.1806, 0.53269, 0.22198, 0.54212, 0.26159, 0.52443, 0.31012, 0.57478, 0.26546, 0.61001, 0.25651, 0.31437, 0.24267, 0.44292, 0.2355, 0.47026, 0.23671, 0.49671, 0.24071, 0.48569, 0.22839, 0.4642, 0.22251, 0.435, 0.22111, 0.31958, 0.22783, 0.29452, 0.2379, 0.37788, 0.22336, 0.3833, 0.23949, 0.34869, 0.22518, 0.35203, 0.24145, 0.40553, 0.22242, 0.4142, 0.23679, 0.21075, 0.23018, 0.19487, 0.2139, 0.11297, 0.18481, 0.0886, 0.1844, 0.07161, 0.19135, 0.0927, 0.19511, 0.19131, 0.22851, 0.14091, 0.21216, 0.15392, 0.19936, 0.13345, 0.19209, 0.11681, 0.20391, 0.17439, 0.20663, 0.16584, 0.22027, 0.32205, 0.2716, 0.33532, 0.26386, 0.35057, 0.25743, 0.37058, 0.25476, 0.39436, 0.25576, 0.41421, 0.26018, 0.43287, 0.26593, 0.44714, 0.27285, 0.33405, 0.27806, 0.35209, 0.28473, 0.3767, 0.28898, 0.40081, 0.28898, 0.42165, 0.28523, 0.43821, 0.27932, 0.31568, 0.26834, 0.32974, 0.26055, 0.34867, 0.25178, 0.37336, 0.24862, 0.39828, 0.2502, 0.42033, 0.2541, 0.44286, 0.26067, 0.46395, 0.26859, 0.31814, 0.27584, 0.33332, 0.28395, 0.35249, 0.2926, 0.38437, 0.30283, 0.41696, 0.30246, 0.4462, 0.29759, 0.46585, 0.29199, 0.48431, 0.28152, 0.08346, 0.22674, 0.09877, 0.22291, 0.12397, 0.22208, 0.14564, 0.22483, 0.16613, 0.23237, 0.1772, 0.24146, 0.18238, 0.25104, 0.18544, 0.25977, 0.17013, 0.26013, 0.15035, 0.26037, 0.12986, 0.25906, 0.10795, 0.25439, 0.09052, 0.24481, 0.08416, 0.23536, 0.07786, 0.21874, 0.09977, 0.21563, 0.1245, 0.21623, 0.14876, 0.2197, 0.17137, 0.22748, 0.1848, 0.23765, 0.19139, 0.24854, 0.19257, 0.26015, 0.05714, 0.22556, 0.05007, 0.23908, 0.05808, 0.2538, 0.08305, 0.26434, 0.11319, 0.26984, 0.13369, 0.26577, 0.15465, 0.2641, 0.17349, 0.26362, 0.19163, 0.26362, 0.22703, 0.25662, 0.21604, 0.27455, 0.19481, 0.29465, 0.17383, 0.30924, 0.25927, 0.26009, 0.25305, 0.27807, 0.24294, 0.29921, 0.24138, 0.31995, 0.20173, 0.3243, 0.26834, 0.30596, 0.27067, 0.32848, 0.25434, 0.33776, 0.20029, 0.33519, 0.15558, 0.32393, 0.15403, 0.3105, 0.19523, 0.30871, 0.22206, 0.31187, 0.22029, 0.29473, 0.23398, 0.27419, 0.24215, 0.25581, 0.29196, 0.26376, 0.28339, 0.28602, 0.1439, 0.14399, 0.2281, 0.15523, 0.322, 0.16572, 0.4244, 0.17585, 0.12763, 0.35501, 0.14114, 0.35279, 0.17336, 0.34801, 0.1904, 0.35594, 0.21802, 0.35403, 0.23703, 0.35964, 0.25455, 0.36835, 0.27433, 0.37541, 0.15647, 0.34945, 0.11873, 0.34963, 0.10632, 0.35316, 0.11289, 0.35798, 0.12019, 0.3602, 0.12858, 0.37318, 0.14281, 0.38431, 0.16514, 0.39303, 0.19759, 0.39692, 0.22894, 0.39617, 0.25266, 0.38931, 0.27017, 0.38134, 0.28587, 0.38078, 0.28539, 0.37504, 0.29281, 0.36966, 0.30704, 0.37689, 0.2939, 0.38319, 0.11846, 0.35557, 0.12747, 0.35993, 0.14552, 0.36245, 0.16608, 0.36519, 0.18692, 0.37028, 0.2091, 0.37062, 0.2321, 0.37152, 0.2532, 0.37509, 0.2735, 0.3788, 0.29129, 0.37716, 0.15167, 0.37199, 0.16961, 0.37807, 0.22834, 0.38164, 0.19774, 0.38146, 0.16895, 0.39751, 0.20142, 0.40077, 0.24073, 0.41944, 0.13142, 0.40371, 0.15617, 0.41839, 0.20136, 0.42304, 0.30817, 0.40508, 0.36216, 0.38676, 0.43384, 0.35374, 0.46642, 0.31785, 0.33077, 0.31546, 0.39367, 0.32963, 0.34219, 0.35085, 0.0677, 0.28397, 0.06876, 0.31927, 0.08062, 0.35073, 0.10334, 0.3804, 0.12136, 0.28893, 0.10652, 0.32397, 0.18406, 0.28028, 0.33533, 0.10125, 0.31037, 0.07425, 0.35822, 0.03495, 0.40584, 0.08413, 0.44975, 0.04269, 0.4825, 0.10738, 0.54434, 0.07311, 0.59736, 0.11826, 0.53642, 0.14057, 0.60095, 0.17108, 0.60454, 0.21901, 0.27213, 0.02378, 0.23359, 0.06157, 0.16505, 0.06037, 0.10052, 0.07813, 0.04855, 0.12275], "triangles": [181, 128, 129, 182, 129, 134, 128, 127, 129, 129, 127, 126, 128, 97, 127, 126, 127, 98, 183, 134, 131, 131, 134, 132, 131, 132, 135, 132, 134, 133, 135, 132, 221, 129, 126, 134, 134, 126, 133, 132, 133, 221, 133, 126, 220, 124, 186, 130, 125, 117, 124, 124, 130, 125, 125, 130, 136, 130, 185, 136, 136, 135, 125, 135, 136, 131, 136, 184, 131, 125, 135, 221, 217, 124, 117, 116, 125, 222, 218, 117, 109, 153, 109, 121, 154, 121, 119, 109, 116, 121, 109, 117, 116, 116, 120, 121, 121, 120, 118, 116, 117, 125, 116, 222, 120, 120, 222, 118, 156, 123, 110, 155, 119, 123, 121, 118, 119, 119, 122, 123, 119, 118, 122, 123, 115, 110, 123, 122, 115, 110, 115, 114, 122, 118, 223, 122, 223, 115, 114, 115, 223, 158, 111, 112, 157, 110, 111, 111, 113, 112, 112, 113, 104, 110, 114, 111, 111, 114, 113, 103, 113, 114, 71, 72, 66, 67, 71, 66, 71, 67, 70, 69, 70, 68, 68, 70, 67, 33, 34, 32, 34, 35, 32, 32, 35, 31, 31, 35, 36, 37, 30, 36, 31, 36, 30, 30, 37, 29, 38, 29, 37, 26, 40, 22, 29, 38, 28, 38, 39, 28, 39, 27, 28, 39, 40, 27, 27, 40, 26, 25, 26, 22, 25, 23, 24, 40, 41, 22, 41, 42, 21, 21, 15, 20, 20, 15, 19, 21, 14, 15, 15, 16, 19, 19, 16, 18, 16, 17, 18, 14, 21, 13, 13, 21, 43, 43, 21, 42, 43, 44, 13, 44, 45, 13, 13, 45, 12, 45, 46, 12, 12, 46, 11, 41, 21, 22, 25, 22, 23, 51, 5, 6, 46, 47, 11, 11, 47, 10, 47, 48, 10, 10, 48, 9, 9, 48, 8, 8, 48, 49, 7, 8, 49, 49, 50, 7, 50, 6, 7, 50, 51, 6, 72, 65, 66, 73, 64, 65, 73, 74, 64, 64, 106, 63, 62, 63, 107, 272, 165, 106, 75, 271, 74, 271, 272, 74, 107, 105, 104, 51, 52, 5, 52, 53, 5, 77, 265, 269, 77, 78, 265, 79, 268, 78, 78, 268, 265, 80, 267, 79, 79, 267, 268, 80, 266, 267, 80, 81, 266, 268, 267, 264, 268, 264, 265, 264, 267, 263, 269, 242, 248, 244, 248, 243, 243, 248, 242, 265, 242, 269, 264, 241, 265, 265, 241, 242, 267, 266, 263, 77, 270, 76, 77, 269, 270, 248, 247, 269, 269, 247, 270, 81, 279, 266, 266, 279, 238, 266, 239, 263, 266, 238, 239, 238, 279, 237, 263, 240, 264, 264, 240, 241, 263, 239, 240, 241, 262, 261, 241, 240, 262, 240, 239, 262, 241, 261, 242, 76, 271, 75, 76, 270, 271, 239, 260, 262, 239, 238, 260, 81, 82, 279, 242, 256, 243, 242, 261, 256, 247, 275, 270, 271, 275, 274, 271, 270, 275, 238, 259, 260, 238, 237, 259, 244, 258, 248, 248, 258, 247, 262, 254, 261, 261, 255, 256, 261, 254, 255, 260, 253, 262, 262, 253, 254, 243, 257, 244, 243, 256, 257, 244, 257, 258, 235, 279, 278, 279, 236, 237, 279, 235, 236, 235, 278, 234, 82, 278, 279, 257, 245, 258, 257, 231, 245, 257, 256, 231, 259, 252, 260, 260, 252, 253, 247, 258, 246, 258, 245, 246, 247, 246, 275, 246, 245, 231, 231, 256, 230, 256, 255, 230, 246, 231, 230, 236, 250, 237, 237, 251, 259, 237, 250, 251, 259, 251, 252, 255, 229, 230, 255, 254, 229, 253, 227, 254, 254, 228, 229, 254, 227, 228, 253, 252, 227, 208, 275, 246, 229, 209, 230, 208, 246, 209, 209, 246, 230, 251, 232, 252, 252, 226, 227, 252, 232, 226, 82, 83, 278, 251, 250, 225, 250, 224, 225, 251, 225, 232, 235, 249, 236, 236, 249, 250, 250, 249, 224, 229, 228, 209, 235, 234, 249, 228, 227, 210, 249, 233, 224, 249, 234, 233, 224, 233, 225, 227, 226, 210, 228, 210, 209, 271, 274, 272, 272, 163, 164, 163, 272, 274, 233, 234, 281, 225, 233, 232, 208, 273, 275, 275, 273, 274, 83, 277, 278, 234, 278, 281, 278, 277, 281, 233, 211, 232, 233, 281, 211, 232, 211, 226, 226, 211, 210, 209, 210, 205, 210, 206, 205, 209, 205, 208, 210, 211, 206, 206, 211, 201, 83, 84, 277, 273, 162, 274, 274, 162, 163, 205, 207, 208, 208, 207, 273, 201, 213, 206, 206, 214, 205, 206, 213, 214, 211, 281, 212, 212, 281, 280, 211, 212, 201, 205, 204, 207, 205, 214, 204, 84, 276, 277, 281, 277, 280, 280, 276, 193, 193, 276, 192, 276, 280, 277, 207, 219, 273, 273, 219, 160, 273, 161, 162, 273, 160, 161, 160, 219, 159, 213, 215, 214, 214, 215, 204, 212, 280, 282, 200, 201, 212, 280, 195, 282, 194, 195, 280, 201, 200, 213, 200, 212, 282, 282, 195, 196, 213, 200, 215, 207, 204, 219, 161, 147, 162, 162, 148, 163, 162, 147, 148, 163, 149, 164, 163, 148, 149, 204, 203, 219, 204, 215, 203, 149, 150, 164, 164, 150, 165, 200, 199, 215, 215, 216, 203, 215, 199, 216, 200, 282, 199, 160, 146, 161, 161, 146, 147, 150, 144, 165, 165, 144, 166, 166, 144, 158, 276, 85, 191, 276, 84, 85, 146, 140, 147, 147, 141, 148, 147, 140, 141, 148, 142, 149, 148, 141, 142, 280, 193, 194, 159, 218, 151, 159, 219, 218, 219, 203, 218, 149, 143, 150, 149, 142, 143, 160, 145, 146, 145, 138, 146, 138, 139, 146, 146, 139, 140, 276, 191, 192, 160, 159, 145, 166, 158, 105, 282, 197, 199, 282, 196, 197, 150, 143, 144, 203, 202, 218, 203, 216, 202, 159, 137, 145, 145, 137, 138, 159, 151, 137, 216, 199, 198, 198, 199, 197, 197, 188, 198, 216, 217, 202, 216, 198, 217, 143, 157, 144, 144, 157, 158, 137, 151, 138, 192, 178, 193, 193, 177, 194, 193, 178, 177, 158, 112, 105, 158, 157, 111, 151, 152, 138, 151, 218, 152, 157, 143, 156, 194, 176, 195, 194, 177, 176, 192, 179, 178, 192, 191, 179, 195, 175, 196, 195, 176, 175, 138, 152, 139, 218, 109, 152, 218, 202, 117, 196, 174, 197, 196, 175, 174, 197, 174, 188, 112, 104, 105, 143, 142, 156, 157, 156, 110, 152, 153, 139, 152, 109, 153, 173, 175, 176, 141, 155, 142, 142, 155, 156, 198, 188, 187, 174, 175, 173, 202, 217, 117, 172, 173, 176, 187, 188, 173, 188, 174, 173, 177, 178, 170, 171, 172, 176, 176, 177, 171, 177, 170, 171, 170, 178, 169, 139, 153, 140, 217, 198, 124, 104, 293, 108, 198, 187, 124, 140, 154, 141, 141, 154, 155, 140, 153, 154, 178, 179, 169, 169, 179, 168, 155, 123, 156, 85, 190, 191, 179, 191, 190, 179, 190, 180, 180, 189, 167, 189, 180, 190, 85, 86, 190, 153, 121, 154, 173, 172, 187, 154, 119, 155, 60, 108, 293, 172, 186, 187, 187, 186, 124, 179, 180, 168, 172, 171, 186, 190, 86, 189, 171, 185, 186, 186, 185, 130, 180, 167, 168, 171, 170, 185, 185, 170, 184, 104, 113, 103, 222, 125, 221, 184, 136, 185, 189, 181, 167, 167, 181, 168, 189, 86, 181, 170, 169, 184, 168, 182, 169, 168, 181, 182, 223, 118, 222, 169, 183, 184, 169, 182, 183, 103, 114, 223, 293, 104, 292, 86, 128, 181, 86, 97, 128, 86, 87, 97, 183, 131, 184, 104, 103, 292, 293, 292, 60, 181, 129, 182, 182, 134, 183, 60, 292, 59, 133, 220, 221, 127, 97, 98, 220, 126, 98, 97, 87, 98, 103, 291, 292, 291, 103, 102, 222, 102, 223, 103, 223, 102, 87, 298, 98, 87, 88, 298, 291, 290, 292, 292, 290, 59, 221, 101, 222, 222, 283, 102, 222, 101, 283, 290, 58, 59, 220, 100, 221, 221, 100, 101, 220, 98, 99, 98, 298, 99, 220, 99, 100, 102, 288, 291, 291, 288, 290, 283, 286, 102, 102, 286, 288, 88, 89, 298, 298, 297, 99, 298, 89, 297, 288, 289, 290, 290, 57, 58, 290, 289, 57, 99, 296, 100, 99, 297, 296, 289, 288, 287, 101, 284, 283, 283, 284, 286, 284, 101, 295, 101, 100, 295, 100, 296, 295, 284, 285, 286, 288, 286, 287, 286, 285, 287, 53, 54, 4, 297, 90, 296, 297, 89, 90, 295, 294, 284, 284, 294, 285, 289, 95, 57, 95, 96, 57, 289, 287, 95, 95, 287, 94, 57, 96, 56, 296, 91, 295, 295, 91, 294, 296, 90, 91, 4, 54, 3, 55, 56, 0, 3, 54, 2, 56, 96, 0, 0, 1, 55, 54, 55, 2, 55, 1, 2, 94, 287, 93, 294, 93, 285, 287, 285, 93, 91, 92, 294, 294, 92, 93, 53, 4, 5, 108, 107, 104, 164, 165, 272, 165, 166, 106, 106, 105, 107, 106, 166, 105, 60, 61, 108, 74, 272, 106, 61, 107, 108, 63, 106, 107, 62, 107, 61, 74, 106, 64, 72, 73, 65], "vertices": [2, 6, 205, -37.38, 0.98305, 84, -116.14, 11.61, 0.01695, 2, 6, 214.86, -49, 0.98141, 84, -106.28, -0.01, 0.01859, 2, 6, 216.94, -62.83, 0.98033, 84, -104.2, -13.85, 0.01967, 3, 6, 210.8, -80.17, 0.92371, 81, 152.76, 15.33, 0.05682, 84, -110.33, -31.19, 0.01947, 3, 6, 195.45, -95.5, 0.76821, 81, 136.34, 1.15, 0.21459, 84, -125.69, -46.51, 0.0172, 3, 6, 170.71, -110.7, 0.51331, 81, 110.57, -12.23, 0.47312, 84, -150.43, -61.72, 0.01357, 3, 6, 145.98, -125.91, 0.25091, 81, 84.8, -25.6, 0.73933, 84, -175.16, -76.93, 0.00976, 3, 6, 121.24, -139.84, 0.1126, 81, 59.11, -37.7, 0.88141, 84, -199.9, -90.85, 0.00599, 4, 6, 93.58, -158.13, 0.0337, 81, 30.21, -53.95, 0.93989, 7, 170.52, -33.32, 0.02482, 84, -227.56, -109.14, 0.0016, 3, 6, 62.73, -177.1, 0.0038, 81, -1.93, -70.63, 0.85541, 7, 137.38, -47.94, 0.14079, 2, 81, -35.42, -74.63, 0.69327, 7, 103.71, -49.81, 0.30673, 3, 81, -68.74, -67.75, 0.47405, 7, 70.89, -40.84, 0.52401, 85, 53.01, 276.1, 0.00194, 3, 81, -88.33, -63.27, 0.31775, 7, 51.62, -35.13, 0.67438, 85, 33.74, 281.81, 0.00787, 3, 81, -100.03, -65.03, 0.22206, 7, 39.84, -36.15, 0.76693, 85, 21.96, 280.78, 0.01101, 3, 81, -107.96, -72.99, 0.14934, 7, 31.42, -43.59, 0.83839, 85, 13.54, 273.35, 0.01227, 3, 81, -109.73, -84.37, 0.09892, 7, 28.93, -54.83, 0.89017, 85, 11.05, 262.1, 0.01091, 3, 81, -104.46, -89.38, 0.07931, 7, 33.87, -60.17, 0.91233, 85, 15.99, 256.77, 0.00836, 3, 81, -89.26, -90.86, 0.06882, 7, 48.95, -62.61, 0.92752, 85, 31.06, 254.33, 0.00365, 3, 81, -99.84, -98.15, 0.07261, 7, 37.93, -69.21, 0.92221, 85, 20.05, 247.72, 0.00518, 3, 81, -113.07, -96.99, 0.08357, 7, 24.8, -67.22, 0.90707, 85, 6.92, 249.72, 0.00936, 3, 81, -120.83, -88.21, 0.09571, 7, 17.61, -57.97, 0.89071, 85, -0.27, 258.97, 0.01357, 3, 81, -122.6, -77.57, 0.10493, 7, 16.51, -47.24, 0.87899, 85, -1.37, 269.7, 0.01608, 4, 81, -134.18, -93.19, 0.06182, 7, 3.97, -62.09, 0.82019, 37, 6.47, 53.71, 0.10134, 85, -13.91, 254.84, 0.01665, 5, 81, -148.07, -107.45, 0.01476, 7, -10.79, -75.45, 0.50459, 37, 25.66, 59.01, 0.35704, 38, -51.76, 46.24, 0.10663, 85, -28.67, 241.49, 0.01699, 4, 7, -26.37, -81.54, 0.03396, 37, 42.31, 57.47, 0.51021, 38, -35.35, 49.48, 0.43883, 85, -44.25, 235.39, 0.017, 3, 37, 58.22, 48.94, 0.58656, 38, -17.67, 45.81, 0.39489, 85, -62.3, 235.87, 0.01856, 3, 37, 74.78, 29.08, 0.37938, 38, 3.84, 31.46, 0.6004, 85, -86.01, 246.18, 0.02023, 3, 37, 86.13, 18.22, 0.08947, 38, 17.8, 24.26, 0.89191, 85, -101.02, 250.79, 0.01862, 3, 38, 40.67, 22.85, 0.8845, 39, -16.65, 21.28, 0.10164, 85, -123.79, 248.11, 0.01386, 4, 38, 58.88, 26.59, 0.37458, 39, 1.09, 26.83, 0.6107, 40, -54.42, 22.84, 0.00443, 85, -141.05, 241.21, 0.01029, 4, 38, 88.16, 35.78, 0.0161, 39, 29.29, 38.92, 0.82835, 40, -27.2, 37, 0.15516, 85, -168.23, 226.97, 0.0004, 2, 39, 60.39, 40.45, 0.41606, 40, 3.7, 40.84, 0.58394, 2, 39, 87.98, 11.78, 0.0267, 40, 33.35, 14.31, 0.9733, 1, 40, 63, -12.22, 1, 2, 39, 90.85, -18.24, 0.00383, 40, 38.45, -15.42, 0.99617, 2, 39, 67.74, -22.58, 0.17426, 40, 15.74, -21.47, 0.82574, 4, 38, 95.56, -32.89, 0.00016, 39, 43.57, -28.65, 0.73905, 40, -7.92, -29.32, 0.25706, 85, -187.69, 293.24, 0.00372, 4, 38, 68.74, -31.19, 0.1594, 39, 16.71, -29.66, 0.82127, 40, -34.63, -32.33, 0.00821, 85, -160.99, 296.32, 0.01112, 4, 37, 93.66, -37.6, 0.00928, 38, 40.83, -27.14, 0.80872, 39, -11.46, -28.44, 0.16709, 85, -132.82, 297.29, 0.01491, 5, 7, -95.73, -14.78, 0.00154, 37, 74.31, -33.33, 0.15828, 38, 21.07, -28.53, 0.81575, 39, -30.98, -31.81, 0.00537, 85, -113.61, 302.16, 0.01905, 4, 7, -82.14, -5.59, 0.01932, 37, 58.04, -35.44, 0.42926, 38, 6.07, -35.17, 0.52957, 85, -100.02, 311.35, 0.02185, 4, 7, -62.7, 16.91, 0.31086, 37, 30.57, -46.82, 0.50774, 38, -17.06, -53.86, 0.157, 85, -80.58, 333.85, 0.02439, 5, 81, -182.69, 4.79, 0.0012, 7, -38.24, 38.75, 0.82845, 37, -1.09, -55.35, 0.14715, 38, -45, -71.02, 0.00325, 85, -56.12, 355.69, 0.01994, 3, 81, -160.19, 16.51, 0.02471, 7, -15.05, 49.04, 0.9585, 85, -32.93, 365.97, 0.01679, 3, 81, -132.04, 27.16, 0.0941, 7, 13.71, 57.88, 0.89477, 85, -4.17, 374.82, 0.01113, 3, 81, -97.49, 29.12, 0.23233, 7, 48.32, 57.65, 0.76251, 85, 30.44, 374.59, 0.00516, 3, 81, -68.53, 24.83, 0.40893, 7, 76.95, 51.54, 0.59061, 85, 59.07, 368.47, 0.00046, 2, 81, -38.35, 18.54, 0.6619, 7, 106.67, 43.35, 0.3381, 2, 81, -2.63, 10.62, 0.97061, 7, 141.82, 33.19, 0.02939, 3, 6, 86.45, -99.9, 0.04568, 81, 27.31, 4.64, 0.95121, 84, -234.69, -50.92, 0.00311, 3, 6, 111.99, -98.59, 0.17207, 81, 52.88, 4.11, 0.82144, 84, -209.14, -49.6, 0.0065, 3, 6, 138.21, -91.9, 0.40599, 81, 79.51, 8.89, 0.58398, 84, -182.93, -42.91, 0.01002, 3, 6, 158.85, -84.56, 0.62214, 81, 100.62, 14.71, 0.36513, 84, -162.29, -35.57, 0.01273, 4, 6, 176.91, -75.17, 0.84708, 81, 119.32, 22.77, 0.13767, 80, -146.82, 50.02, 0.00011, 84, -144.23, -26.19, 0.01515, 2, 6, 187.21, -65.27, 0.98336, 84, -133.92, -16.28, 0.01664, 2, 6, 188.09, -58.3, 0.9832, 84, -133.04, -9.31, 0.0168, 2, 6, 183.99, -51.72, 0.98388, 84, -137.15, -2.73, 0.01612, 2, 6, 178.76, -49.58, 0.98869, 84, -142.38, -0.6, 0.01131, 2, 6, 159.8, -66.42, 0.99051, 84, -161.34, -17.43, 0.00949, 2, 6, 133.29, -80.23, 0.99065, 84, -187.85, -31.24, 0.00935, 2, 6, 107.42, -85.37, 0.99256, 84, -213.72, -36.38, 0.00744, 3, 6, 71.26, -84.15, 0.98324, 83, -297.43, -35.17, 0.00494, 84, -249.87, -35.17, 0.01182, 3, 6, 62.25, -85.38, 0.98174, 83, -306.45, -36.39, 0.00493, 84, -258.89, -36.39, 0.01333, 3, 6, 46.31, -83.04, 0.98228, 83, -322.39, -34.05, 0.00494, 84, -274.83, -34.05, 0.01278, 2, 6, 23.78, -70.02, 0.995, 83, -344.92, -21.04, 0.005, 3, 6, 20.41, -71.05, 0.62597, 80, 2.9, 4.28, 0.37117, 83, -348.29, -22.07, 0.00286, 3, 6, 17.19, -72.04, 0.25432, 80, 6.26, 4.37, 0.74468, 83, -351.51, -23.05, 0.001, 2, 6, 8.09, -74.82, 0.02571, 80, 15.78, 4.62, 0.97429, 2, 6, -9.21, -77.35, 0.02571, 80, 33.12, 2.45, 0.97429, 2, 6, -11.18, -73.2, 0.02571, 80, 33.91, -2.08, 0.97429, 2, 6, 5.7, -64.12, 0.02571, 80, 15.22, -6.32, 0.97429, 3, 6, 14.6, -63.38, 0.26674, 80, 6.45, -4.66, 0.73226, 83, -354.1, -14.4, 0.001, 3, 6, 17.34, -63.16, 0.62791, 80, 3.75, -4.15, 0.36909, 83, -351.36, -14.17, 0.003, 2, 6, 20.76, -62.87, 0.995, 83, -347.94, -13.89, 0.005, 2, 6, 21.89, -58.71, 0.995, 83, -346.81, -9.73, 0.005, 2, 6, 4.27, -53.89, 0.9922, 83, -364.43, -4.91, 0.0078, 2, 6, -11.66, -44.79, 0.98509, 83, -380.36, 4.2, 0.01491, 2, 6, -30.26, -22.91, 0.97424, 83, -398.96, 26.07, 0.02576, 2, 6, -49.71, 5.01, 0.96847, 83, -418.41, 53.99, 0.03153, 2, 6, -52.27, 20.88, 0.96844, 83, -420.97, 69.87, 0.03156, 2, 6, -48.77, 32.82, 0.9682, 83, -417.47, 81.8, 0.0318, 2, 6, -34.34, 48.73, 0.97361, 83, -403.04, 97.71, 0.02639, 2, 6, -20.39, 61.59, 0.97784, 83, -389.09, 110.58, 0.02216, 2, 6, -3.86, 71.07, 0.97995, 83, -372.56, 120.05, 0.02005, 2, 6, 16.98, 78.6, 0.9816, 83, -351.72, 127.59, 0.0184, 2, 6, 37.55, 83.58, 0.98067, 83, -331.15, 132.57, 0.01933, 2, 6, 54.98, 84.61, 0.9794, 83, -313.72, 133.59, 0.0206, 2, 6, 77.53, 102.38, 0.98376, 83, -291.17, 151.37, 0.01624, 2, 6, 108.48, 109.86, 0.98274, 83, -260.22, 158.84, 0.01726, 2, 6, 134.22, 106.18, 0.98705, 83, -234.48, 155.17, 0.01295, 2, 6, 158.18, 92.01, 0.99307, 83, -210.52, 141, 0.00693, 2, 6, 174.95, 72.4, 0.99644, 83, -193.75, 121.39, 0.00356, 2, 6, 187.61, 54.75, 0.99832, 83, -181.08, 103.74, 0.00168, 2, 6, 195.73, 29.61, 0.9989, 84, -125.41, 78.6, 0.0011, 2, 6, 194.96, -6.9, 0.99135, 84, -126.18, 42.09, 0.00865, 2, 6, 189.97, -27.63, 0.98744, 84, -131.16, 21.36, 0.01256, 2, 6, 193.78, -32.12, 0.98434, 84, -127.36, 16.86, 0.01566, 2, 6, 73.96, 87.21, 0.97711, 83, -294.74, 136.2, 0.02289, 2, 6, 98.67, 84.4, 0.97557, 83, -270.03, 133.38, 0.02443, 2, 6, 120.8, 76.45, 0.97694, 83, -247.9, 125.43, 0.02306, 2, 6, 135.75, 52.8, 0.97333, 83, -232.94, 101.79, 0.02667, 2, 6, 139.55, 31.69, 0.97136, 83, -229.15, 80.67, 0.02864, 2, 6, 127.39, -5.52, 0.96601, 83, -241.31, 43.46, 0.03399, 2, 6, 108.44, -33.42, 0.96413, 83, -260.26, 15.57, 0.03587, 2, 6, 88.8, -49.24, 0.97459, 83, -279.9, -0.26, 0.02541, 2, 6, 68.17, -57.29, 0.98393, 83, -300.53, -8.31, 0.01607, 2, 6, 40.84, -59.16, 0.99195, 83, -327.86, -10.18, 0.00805, 3, 6, 68.38, -66.75, 0.99218, 83, -300.32, -17.76, 0.00711, 84, -252.76, -17.76, 0.00072, 3, 6, 75.66, -75.13, 0.98956, 83, -293.04, -26.15, 0.00497, 84, -245.48, -26.15, 0.00547, 4, 47, 44.82, 19.91, 0.00068, 48, 39.51, 4.57, 0.00642, 49, 15.57, 3.46, 0.98923, 84, -258.79, 56.51, 0.00367, 3, 47, 11.56, 4.96, 0.36605, 48, 3.05, 4.09, 0.63205, 49, -20.84, 5.6, 0.00191, 3, 47, 4.01, 3.23, 0.99775, 49, -28.33, 7.55, 0.00197, 84, -244.64, 14.76, 0.00027, 3, 47, -3.77, 3.05, 0.99026, 49, -35.31, 10.98, 0.00304, 84, -244.93, 6.98, 0.0067, 3, 47, 1.29, -2.5, 0.97019, 48, -9.34, 1.32, 0.02779, 84, -239.09, 11.69, 0.00202, 3, 47, 8.06, -3.74, 0.76372, 48, -3.62, -2.51, 0.23222, 49, -27.96, -0.51, 0.00406, 2, 47, 16.14, -1.95, 0.19803, 48, 4.5, -4.07, 0.80197, 4, 47, 45.96, 11.62, 0.00025, 48, 37.26, -3.48, 0.00283, 49, 12.74, -4.41, 0.99369, 84, -250.44, 57.14, 0.00323, 2, 49, 20.65, -0.08, 0.99542, 84, -257.61, 62.59, 0.00458, 3, 48, 20.66, -4.38, 0.71779, 49, -3.88, -4.11, 0.28174, 84, -243.95, 41.83, 0.00047, 4, 47, 26.87, 12.25, 0.00466, 48, 20, 4.68, 0.74052, 49, -3.89, 4.97, 0.2544, 84, -252.24, 38.12, 0.00042, 3, 48, 28.95, -4.16, 0.18018, 49, 4.41, -4.49, 0.81798, 84, -246.98, 49.55, 0.00184, 4, 47, 34.93, 15.99, 0.00195, 48, 28.88, 4.92, 0.11237, 49, 4.99, 4.57, 0.88379, 84, -255.49, 46.39, 0.0019, 1, 48, 12.85, -4.15, 1, 3, 47, 19.04, 8.14, 0.00387, 48, 11.18, 4.02, 0.99162, 49, -12.73, 4.95, 0.00451, 1, 52, 18.09, 0.25, 1, 2, 52, 8.97, 4.56, 0.96009, 51, 28.67, 3.98, 0.03991, 3, 51, 0.5, 4.13, 0.7434, 50, 10.7, 2.78, 0.25157, 84, -241.82, 119.51, 0.00503, 2, 50, 3.9, 3.8, 0.99235, 84, -243.31, 126.22, 0.00765, 3, 52, -26.06, -7.18, 0.00038, 50, -1.3, 0.51, 0.98942, 84, -248.24, 129.9, 0.0102, 4, 52, -20.1, -5.13, 0.00296, 51, -0.96, -3.82, 0.01481, 50, 4.37, -2.24, 0.97553, 84, -248.78, 123.62, 0.0067, 2, 52, 13.21, -2.42, 0.99568, 50, 29.86, -23.84, 0.00432, 4, 52, -3.57, -4.12, 0.23683, 51, 15.6, -3.88, 0.71922, 50, 16.79, -13.2, 0.04206, 84, -254.56, 108.1, 0.00188, 3, 52, -5.1, 3.73, 0.14558, 51, 14.58, 4.05, 0.85312, 84, -246.77, 106.32, 0.00131, 3, 52, -12.13, 3.32, 0.00312, 51, 7.54, 4.09, 0.9937, 84, -244.29, 112.91, 0.00318, 4, 52, -11.74, -4.74, 0.01937, 51, 7.41, -3.98, 0.80543, 50, 10.56, -7.87, 0.17105, 84, -251.82, 115.82, 0.00416, 2, 52, 1.94, 4.15, 0.63303, 51, 21.63, 4.01, 0.36697, 3, 52, 4.74, -3.29, 0.8988, 51, 23.94, -3.58, 0.08782, 50, 23.25, -18.48, 0.01338, 2, 6, 47.34, 1.43, 0.9631, 83, -321.36, 50.42, 0.0369, 2, 6, 52.43, -1.12, 0.96284, 83, -316.27, 47.86, 0.03716, 2, 6, 56.96, -4.4, 0.96254, 83, -311.74, 44.59, 0.03746, 2, 6, 59.8, -9.5, 0.96204, 83, -308.9, 39.49, 0.03796, 2, 6, 60.93, -16.13, 0.96137, 83, -307.77, 32.85, 0.03863, 2, 6, 59.95, -22.16, 0.96089, 83, -308.75, 26.82, 0.03911, 2, 6, 58.17, -28.05, 0.96182, 83, -310.53, 20.93, 0.03818, 2, 6, 55.45, -32.91, 0.96348, 83, -313.25, 16.08, 0.03652, 2, 6, 44.71, -2.74, 0.96266, 83, -323.99, 46.25, 0.03734, 2, 6, 42.39, -8.59, 0.96205, 83, -326.31, 40.4, 0.03795, 2, 6, 41.83, -15.9, 0.9613, 83, -326.87, 33.09, 0.0387, 2, 6, 43.52, -22.48, 0.96063, 83, -325.17, 26.5, 0.03937, 2, 6, 47, -27.65, 0.96122, 83, -321.7, 21.33, 0.03878, 2, 6, 51.35, -31.36, 0.96273, 83, -317.35, 17.62, 0.03727, 2, 6, 48.64, 3.62, 0.96332, 83, -320.06, 52.61, 0.03668, 2, 6, 53.82, 0.86, 0.96305, 83, -314.88, 49.84, 0.03695, 2, 6, 59.86, -3.1, 0.96269, 83, -308.84, 45.88, 0.03731, 2, 6, 63.3, -9.41, 0.96207, 83, -305.4, 39.58, 0.03793, 2, 6, 64.19, -16.43, 0.96136, 83, -304.5, 32.55, 0.03864, 2, 6, 63.65, -22.99, 0.96102, 83, -305.05, 25.99, 0.03898, 2, 6, 61.69, -30.05, 0.96229, 83, -307.01, 18.93, 0.03771, 2, 6, 58.92, -36.91, 0.96622, 83, -309.78, 12.08, 0.03378, 2, 6, 44.79, 1.91, 0.96314, 83, -323.91, 50.9, 0.03686, 2, 6, 41.49, -3.35, 0.96258, 83, -327.21, 45.63, 0.03742, 2, 6, 38.19, -9.78, 0.96191, 83, -330.51, 39.2, 0.03809, 2, 6, 34.93, -19.9, 0.96086, 83, -333.77, 29.08, 0.03914, 2, 6, 37.41, -28.75, 0.96105, 83, -331.29, 20.23, 0.03895, 2, 6, 42.08, -36.07, 0.96523, 83, -326.62, 12.92, 0.03477, 2, 6, 46.47, -40.66, 0.96852, 83, -322.23, 8.32, 0.03148, 2, 6, 53.4, -44.25, 0.97095, 83, -315.3, 4.73, 0.02905, 2, 6, 54.71, 72.79, 0.97187, 83, -313.99, 121.78, 0.02813, 2, 6, 57.84, 69.14, 0.97004, 83, -310.86, 118.13, 0.02996, 2, 6, 60.06, 62.37, 0.96937, 83, -308.64, 111.36, 0.03063, 2, 6, 60.1, 56.08, 0.96873, 83, -308.6, 105.06, 0.03127, 2, 6, 57.48, 49.44, 0.96802, 83, -311.21, 98.42, 0.03198, 2, 6, 53.37, 45.16, 0.96758, 83, -315.33, 94.14, 0.03242, 2, 6, 48.59, 42.42, 0.9673, 83, -320.11, 91.4, 0.0327, 2, 6, 44.11, 40.38, 0.96707, 83, -324.59, 89.36, 0.03293, 2, 6, 42.84, 44.51, 0.96749, 83, -325.86, 93.49, 0.03251, 2, 6, 41.32, 49.88, 0.96803, 83, -327.38, 98.86, 0.03197, 2, 6, 40.59, 55.66, 0.96862, 83, -328.11, 104.64, 0.03138, 2, 6, 41.56, 62.28, 0.9693, 83, -327.13, 111.27, 0.0307, 2, 6, 45.49, 68.37, 0.96994, 83, -323.21, 117.35, 0.03006, 2, 6, 50.12, 71.41, 0.97118, 83, -318.58, 120.4, 0.02882, 2, 6, 58.61, 75.43, 0.97336, 83, -310.08, 124.41, 0.02664, 2, 6, 61.82, 69.87, 0.9704, 83, -306.87, 118.86, 0.0296, 2, 6, 63.24, 63.04, 0.96947, 83, -305.46, 112.02, 0.03053, 2, 6, 63.08, 55.93, 0.96875, 83, -305.62, 104.92, 0.03125, 2, 6, 60.48, 48.68, 0.96798, 83, -308.22, 97.67, 0.03202, 2, 6, 55.96, 43.61, 0.96742, 83, -312.74, 92.59, 0.03258, 2, 6, 50.56, 40.3, 0.96709, 83, -318.13, 89.29, 0.03291, 2, 6, 44.41, 38.38, 0.96686, 83, -324.29, 87.36, 0.03314, 2, 6, 53.49, 80.15, 0.97598, 83, -315.21, 129.13, 0.02402, 2, 6, 45.73, 80.21, 0.97617, 83, -322.97, 129.19, 0.02383, 2, 6, 38.38, 75.99, 0.97395, 83, -330.32, 124.97, 0.02605, 2, 6, 34.47, 67.71, 0.96982, 83, -334.23, 116.7, 0.03018, 2, 6, 33.63, 58.72, 0.9689, 83, -335.07, 107.7, 0.0311, 2, 6, 37.25, 53.68, 0.9684, 83, -331.45, 102.67, 0.0316, 2, 6, 39.62, 48.19, 0.96785, 83, -329.08, 97.17, 0.03215, 2, 6, 41.2, 43.11, 0.96734, 83, -327.5, 92.09, 0.03266, 2, 6, 42.48, 38.16, 0.96684, 83, -326.22, 87.14, 0.03316, 2, 6, 48.72, 29.45, 0.96347, 83, -319.98, 78.44, 0.03653, 2, 6, 38.31, 29.98, 0.96298, 83, -330.38, 78.96, 0.03702, 2, 6, 26.02, 33, 0.96177, 83, -342.68, 81.98, 0.03823, 2, 6, 16.7, 36.71, 0.96064, 83, -352, 85.7, 0.03936, 2, 6, 49.12, 20.17, 0.96252, 83, -319.58, 69.15, 0.03748, 2, 6, 39.02, 19.38, 0.9619, 83, -329.68, 68.37, 0.0381, 2, 6, 26.95, 19.22, 0.96036, 83, -341.75, 68.21, 0.03964, 2, 6, 15.69, 16.78, 0.9586, 83, -353.01, 65.77, 0.0414, 2, 6, 10.57, 27.01, 0.95963, 83, -358.13, 76, 0.04037, 2, 6, 25.1, 11.36, 0.96406, 83, -343.6, 60.34, 0.03594, 2, 6, 13.16, 7.61, 0.96365, 83, -355.54, 56.59, 0.03635, 2, 6, 7.02, 10.79, 0.96398, 83, -361.68, 59.77, 0.03602, 2, 6, 4.61, 25.9, 0.96552, 83, -364.09, 74.89, 0.03448, 2, 6, 7.52, 39.67, 0.96693, 83, -361.17, 88.65, 0.03307, 2, 6, 14.64, 41.95, 0.96717, 83, -354.06, 90.93, 0.03283, 2, 6, 18.49, 30.94, 0.95856, 83, -350.21, 79.93, 0.04144, 2, 6, 18.67, 23.18, 0.95777, 83, -350.03, 72.16, 0.04223, 2, 6, 27.76, 26.03, 0.95956, 83, -340.94, 75.01, 0.04044, 2, 6, 39.77, 25.13, 0.96099, 83, -328.93, 74.11, 0.03901, 2, 6, 50.22, 25.44, 0.96156, 83, -318.48, 74.42, 0.03844, 2, 6, 49.44, 10.73, 0.96406, 83, -319.26, 59.72, 0.03594, 2, 6, 36.88, 10, 0.96393, 83, -331.82, 58.98, 0.03607, 2, 6, 103.43, 67.72, 0.97011, 83, -265.27, 116.7, 0.02989, 2, 6, 103.3, 43.17, 0.9676, 83, -265.4, 92.15, 0.0324, 2, 6, 104.25, 16.07, 0.96483, 83, -264.45, 65.06, 0.03517, 2, 6, 106, -13.3, 0.96188, 83, -262.7, 35.69, 0.03812, 2, 6, -11.14, 43.01, 0.96669, 83, -379.84, 91.99, 0.03331, 2, 6, -9, 39.63, 0.96572, 83, -377.7, 88.61, 0.03428, 2, 6, -4.17, 31.49, 0.96349, 83, -372.87, 80.47, 0.03651, 2, 6, -7.23, 25.74, 0.9631, 83, -375.93, 74.72, 0.0369, 2, 6, -4.27, 18.46, 0.96204, 83, -372.97, 67.44, 0.03796, 2, 6, -5.95, 12.49, 0.96219, 83, -374.65, 61.48, 0.03781, 2, 6, -9.41, 6.5, 0.96268, 83, -378.11, 55.49, 0.03732, 2, 6, -11.81, 0.12, 0.96286, 83, -380.51, 49.11, 0.03714, 2, 6, -6.13, 35.9, 0.9646, 83, -374.83, 84.88, 0.0354, 2, 6, -8.88, 46.18, 0.96712, 83, -377.58, 95.17, 0.03288, 2, 6, -11.64, 49.09, 0.96788, 83, -380.34, 98.07, 0.03212, 2, 6, -13.77, 46.63, 0.96763, 83, -382.47, 95.61, 0.03237, 2, 6, -14.45, 44.33, 0.96737, 83, -383.15, 93.31, 0.03263, 2, 6, -20.84, 40.24, 0.96648, 83, -389.54, 89.22, 0.03352, 2, 6, -25.82, 34.82, 0.96474, 83, -394.52, 83.8, 0.03526, 2, 6, -28.94, 27.51, 0.96298, 83, -397.64, 76.5, 0.03702, 2, 6, -28.76, 18.11, 0.96204, 83, -397.46, 67.1, 0.03796, 2, 6, -26.16, 9.65, 0.96227, 83, -394.86, 58.64, 0.03773, 2, 6, -20.81, 4.12, 0.96302, 83, -389.5, 53.11, 0.03698, 2, 6, -15.29, 0.44, 0.9629, 83, -383.99, 49.43, 0.0371, 2, 6, -13.89, -3.77, 0.96247, 83, -382.59, 45.21, 0.03753, 2, 6, -10.83, -2.84, 0.96256, 83, -379.53, 46.14, 0.03744, 2, 6, -7.42, -4.13, 0.96242, 83, -376.12, 44.86, 0.03758, 2, 6, -10.31, -9.01, 0.96192, 83, -379.01, 39.97, 0.03808, 2, 6, -14.62, -6.3, 0.96221, 83, -383.32, 42.69, 0.03779, 2, 6, -12.09, 45.44, 0.96732, 83, -380.79, 94.42, 0.03268, 2, 6, -13.8, 42.37, 0.96689, 83, -382.5, 91.36, 0.03311, 2, 6, -13.88, 37.1, 0.96589, 83, -382.58, 86.08, 0.03411, 2, 6, -13.92, 31.1, 0.96491, 83, -382.62, 80.08, 0.03509, 2, 6, -15.19, 24.71, 0.96431, 83, -383.89, 73.69, 0.03569, 2, 6, -13.82, 18.6, 0.96358, 83, -382.52, 67.59, 0.03642, 2, 6, -12.68, 12.2, 0.96314, 83, -381.38, 61.18, 0.03686, 2, 6, -13.12, 5.94, 0.96316, 83, -381.82, 54.92, 0.03684, 2, 6, -13.69, -0.12, 0.96284, 83, -382.39, 48.87, 0.03716, 2, 6, -11.56, -4.75, 0.96236, 83, -380.26, 44.24, 0.03764, 2, 6, -18.58, 34.1, 0.96416, 83, -387.28, 83.08, 0.03584, 2, 6, -20.59, 28.36, 0.96176, 83, -389.29, 77.34, 0.03824, 2, 6, -18.39, 11.83, 0.96226, 83, -387.09, 60.81, 0.03774, 2, 6, -20.44, 20.21, 0.96085, 83, -389.14, 69.19, 0.03915, 2, 6, -31.09, 25.85, 0.96239, 83, -399.78, 74.84, 0.03761, 2, 6, -30.56, 16.54, 0.96169, 83, -399.26, 65.52, 0.03831, 2, 6, -37.83, 3.22, 0.9631, 83, -406.53, 52.2, 0.0369, 2, 6, -37.05, 35.25, 0.96638, 83, -405.75, 84.23, 0.03362, 2, 6, -43.2, 26.46, 0.9634, 83, -411.9, 75.44, 0.0366, 2, 6, -42.53, 13.47, 0.96214, 83, -411.23, 62.46, 0.03786, 2, 6, -25.39, -13.22, 0.96288, 83, -394.08, 35.77, 0.03712, 2, 6, -11.75, -25.43, 0.96664, 83, -380.45, 23.55, 0.03336, 2, 6, 11.03, -40.45, 0.96799, 83, -357.66, 8.54, 0.03201, 2, 6, 32.61, -44.39, 0.96844, 83, -336.08, 4.6, 0.03156, 2, 6, 24.38, -7.01, 0.96218, 83, -344.32, 41.98, 0.03782, 2, 6, 21.18, -26.15, 0.96165, 83, -347.52, 22.84, 0.03835, 2, 6, 6.16, -15.02, 0.96133, 83, -362.54, 33.97, 0.03867, 2, 6, 22.84, 69.19, 0.97068, 83, -345.86, 118.18, 0.02932, 2, 6, 3.94, 64.03, 0.96943, 83, -364.76, 113.01, 0.03057, 2, 6, -12.14, 56.44, 0.96863, 83, -380.84, 105.42, 0.03137, 2, 6, -26.5, 46.14, 0.96754, 83, -395.2, 95.12, 0.03246, 2, 6, 23.94, 53.85, 0.96841, 83, -344.76, 102.83, 0.03159, 2, 6, 4.06, 53.06, 0.9683, 83, -364.64, 102.05, 0.0317, 2, 6, 32.99, 37.92, 0.96677, 83, -335.71, 86.91, 0.03323, 2, 6, 139.84, 21.34, 0.97028, 83, -228.86, 70.32, 0.02972, 2, 6, 152.6, 31.89, 0.97389, 83, -216.1, 80.87, 0.02611, 2, 6, 177.08, 24.24, 0.98492, 83, -191.61, 73.23, 0.01508, 2, 6, 153.99, 4.44, 0.97211, 83, -214.7, 53.43, 0.02789, 2, 6, 179.35, -1.83, 0.98607, 83, -189.35, 47.16, 0.01393, 2, 6, 146.88, -19.71, 0.97278, 83, -221.82, 29.28, 0.02722, 2, 6, 169.64, -31.86, 0.98405, 83, -199.06, 17.12, 0.01595, 2, 6, 149.09, -52.58, 0.98524, 83, -219.61, -3.6, 0.01476, 2, 6, 132.82, -39.02, 0.97329, 83, -235.88, 9.97, 0.02671, 2, 6, 120.95, -60.86, 0.98296, 83, -247.75, -11.87, 0.01704, 2, 6, 95.44, -68.46, 0.98873, 83, -273.26, -19.47, 0.01127, 2, 6, 177.05, 49.3, 0.98705, 83, -191.65, 98.28, 0.01295, 2, 6, 154.03, 54.6, 0.97585, 83, -214.67, 103.59, 0.02415, 2, 6, 149.86, 73.49, 0.9752, 83, -218.83, 122.48, 0.0248, 2, 6, 135.79, 88.66, 0.97605, 83, -232.91, 137.65, 0.02395, 2, 6, 108.15, 96.69, 0.977, 83, -260.55, 145.68, 0.023], "hull": 97, "edges": [2, 4, 4, 6, 6, 8, 22, 24, 34, 36, 36, 38, 50, 52, 52, 54, 54, 56, 60, 62, 66, 68, 78, 80, 160, 162, 162, 164, 172, 174, 174, 176, 176, 178, 178, 180, 190, 192, 186, 188, 188, 190, 2, 0, 0, 192, 184, 186, 170, 172, 168, 170, 156, 158, 158, 160, 154, 156, 152, 154, 148, 150, 150, 152, 138, 140, 146, 148, 138, 136, 136, 134, 128, 126, 126, 124, 124, 122, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 88, 90, 92, 90, 84, 86, 86, 88, 80, 82, 82, 84, 76, 78, 72, 74, 74, 76, 68, 70, 70, 72, 62, 64, 64, 66, 58, 60, 56, 58, 46, 48, 48, 50, 42, 44, 44, 46, 38, 40, 40, 42, 32, 34, 28, 30, 30, 32, 24, 26, 26, 28, 18, 20, 20, 22, 16, 18, 14, 16, 12, 14, 8, 10, 10, 12, 18, 96, 120, 122, 172, 194, 194, 196, 196, 198, 198, 200, 200, 202, 204, 206, 206, 208, 208, 210, 210, 212, 212, 148, 212, 214, 214, 216, 216, 122, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 232, 234, 232, 240, 240, 236, 218, 242, 242, 238, 230, 244, 244, 236, 220, 246, 246, 238, 248, 250, 252, 254, 254, 256, 256, 258, 260, 248, 252, 266, 266, 264, 258, 268, 268, 262, 250, 270, 270, 264, 260, 272, 272, 262, 234, 218, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 274, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 288, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 334, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 396, 398, 398, 400, 400, 402, 404, 406, 406, 408, 408, 410, 410, 412, 412, 402, 414, 416, 416, 418, 418, 420, 420, 422, 422, 424, 424, 400, 426, 428, 430, 432, 432, 434, 448, 450, 452, 454, 454, 456, 456, 458, 458, 460, 460, 462, 450, 464, 464, 452, 472, 474, 474, 476, 476, 478, 478, 480, 480, 482, 482, 484, 484, 486, 486, 488, 462, 490, 498, 500, 500, 502, 502, 504, 504, 506, 506, 508, 508, 510, 510, 512, 512, 514, 514, 516, 518, 520, 520, 524, 524, 522, 526, 528, 534, 536, 530, 538, 538, 540, 540, 542, 542, 544, 546, 548, 552, 554, 554, 556, 532, 558, 558, 556, 164, 166, 166, 168, 144, 146, 128, 130, 140, 142, 142, 144, 130, 132, 132, 134, 202, 566, 566, 204, 180, 182, 182, 184], "width": 282, "height": 555}}, "head2": {"head": {"type": "mesh", "uvs": [0.08062, 0.35073, 0.10652, 0.32397, 0.15403, 0.3105, 0.15558, 0.32393, 0.20029, 0.33519, 0.25434, 0.33776, 0.27067, 0.32848, 0.34219, 0.35085, 0.36216, 0.38676, 0.33716, 0.41794, 0.2241, 0.43936, 0.16733, 0.43671, 0.12942, 0.42525, 0.08752, 0.39293, 0.05565, 0.36282, 0.12763, 0.35501, 0.14114, 0.35279, 0.17336, 0.34801, 0.1904, 0.35594, 0.21802, 0.35403, 0.23703, 0.35964, 0.25455, 0.36835, 0.27433, 0.37541, 0.15647, 0.34945, 0.11873, 0.34963, 0.10632, 0.35316, 0.11289, 0.35798, 0.12019, 0.3602, 0.12858, 0.37318, 0.14281, 0.38431, 0.16514, 0.39303, 0.19759, 0.39692, 0.22894, 0.39617, 0.25266, 0.38931, 0.27017, 0.38134, 0.28587, 0.38078, 0.28539, 0.37504, 0.29281, 0.36966, 0.30704, 0.37689, 0.2939, 0.38319, 0.11846, 0.35557, 0.12747, 0.35993, 0.14552, 0.36245, 0.16608, 0.36519, 0.18692, 0.37028, 0.2091, 0.37062, 0.2321, 0.37152, 0.2532, 0.37509, 0.2735, 0.3788, 0.29129, 0.37716, 0.15167, 0.37199, 0.16961, 0.37807, 0.22834, 0.38164, 0.19774, 0.38146, 0.16895, 0.39751, 0.20142, 0.40077, 0.24073, 0.41944, 0.13142, 0.40371, 0.15617, 0.41839, 0.20136, 0.42304, 0.30817, 0.40508, 0.10334, 0.3804], "triangles": [9, 56, 60, 9, 10, 56, 11, 59, 10, 10, 59, 56, 12, 58, 11, 11, 58, 59, 12, 57, 58, 12, 13, 57, 59, 58, 55, 59, 55, 56, 55, 58, 54, 60, 33, 39, 35, 39, 34, 34, 39, 33, 56, 33, 60, 55, 32, 56, 56, 32, 33, 58, 57, 54, 9, 60, 8, 39, 38, 60, 60, 38, 8, 13, 61, 57, 57, 61, 29, 57, 30, 54, 57, 29, 30, 29, 61, 28, 54, 31, 55, 55, 31, 32, 54, 30, 31, 32, 53, 52, 32, 31, 53, 31, 30, 53, 32, 52, 33, 30, 51, 53, 30, 29, 51, 13, 14, 61, 33, 47, 34, 33, 52, 47, 38, 7, 8, 29, 50, 51, 29, 28, 50, 35, 49, 39, 39, 49, 38, 53, 45, 52, 52, 46, 47, 52, 45, 46, 51, 44, 53, 53, 44, 45, 34, 48, 35, 34, 47, 48, 35, 48, 49, 26, 61, 0, 61, 27, 28, 61, 26, 27, 26, 0, 25, 14, 0, 61, 48, 36, 49, 48, 22, 36, 48, 47, 22, 50, 43, 51, 51, 43, 44, 38, 49, 37, 49, 36, 37, 38, 37, 7, 37, 36, 22, 22, 47, 21, 47, 46, 21, 37, 22, 21, 27, 41, 28, 28, 42, 50, 28, 41, 42, 50, 42, 43, 46, 20, 21, 46, 45, 20, 44, 18, 45, 45, 19, 20, 45, 18, 19, 44, 43, 18, 6, 7, 37, 20, 5, 21, 6, 37, 5, 5, 37, 21, 42, 23, 43, 43, 17, 18, 43, 23, 17, 42, 41, 16, 41, 15, 16, 42, 16, 23, 26, 40, 27, 27, 40, 41, 41, 40, 15, 20, 19, 5, 26, 25, 40, 19, 18, 4, 40, 24, 15, 40, 25, 24, 15, 24, 16, 18, 17, 4, 19, 4, 5, 24, 25, 1, 16, 24, 23, 25, 0, 1, 24, 3, 23, 24, 1, 3, 23, 3, 17, 17, 3, 4, 1, 2, 3], "vertices": [2, 6, -12.14, 56.44, 0.96863, 83, -380.84, 105.42, 0.03137, 2, 6, 4.06, 53.06, 0.9683, 83, -364.64, 102.05, 0.0317, 2, 6, 14.64, 41.95, 0.96717, 83, -354.06, 90.93, 0.03283, 2, 6, 7.52, 39.67, 0.96693, 83, -361.17, 88.65, 0.03307, 2, 6, 4.61, 25.9, 0.96552, 83, -364.09, 74.89, 0.03448, 2, 6, 7.02, 10.79, 0.96398, 83, -361.68, 59.77, 0.03602, 2, 6, 13.16, 7.61, 0.96365, 83, -355.54, 56.59, 0.03635, 2, 6, 6.16, -15.02, 0.96133, 83, -362.54, 33.97, 0.03867, 2, 6, -11.75, -25.43, 0.96664, 83, -380.45, 23.55, 0.03336, 2, 6, -30.26, -22.91, 0.97424, 83, -398.96, 26.07, 0.02576, 2, 6, -49.71, 5.01, 0.96847, 83, -418.41, 53.99, 0.03153, 2, 6, -52.27, 20.88, 0.96844, 83, -420.97, 69.87, 0.03156, 2, 6, -48.77, 32.82, 0.9682, 83, -417.47, 81.8, 0.0318, 2, 6, -34.34, 48.73, 0.97361, 83, -403.04, 97.71, 0.02639, 2, 6, -20.39, 61.59, 0.97784, 83, -389.09, 110.58, 0.02216, 2, 6, -11.14, 43.01, 0.96669, 83, -379.84, 91.99, 0.03331, 2, 6, -9, 39.63, 0.96572, 83, -377.7, 88.61, 0.03428, 2, 6, -4.17, 31.49, 0.96349, 83, -372.87, 80.47, 0.03651, 2, 6, -7.23, 25.74, 0.9631, 83, -375.93, 74.72, 0.0369, 2, 6, -4.27, 18.46, 0.96204, 83, -372.97, 67.44, 0.03796, 2, 6, -5.95, 12.49, 0.96219, 83, -374.65, 61.48, 0.03781, 2, 6, -9.41, 6.5, 0.96268, 83, -378.11, 55.49, 0.03732, 2, 6, -11.81, 0.12, 0.96286, 83, -380.51, 49.11, 0.03714, 2, 6, -6.13, 35.9, 0.9646, 83, -374.83, 84.88, 0.0354, 2, 6, -8.88, 46.18, 0.96712, 83, -377.58, 95.17, 0.03288, 2, 6, -11.64, 49.09, 0.96788, 83, -380.34, 98.07, 0.03212, 2, 6, -13.77, 46.63, 0.96763, 83, -382.47, 95.61, 0.03237, 2, 6, -14.45, 44.33, 0.96737, 83, -383.15, 93.31, 0.03263, 2, 6, -20.84, 40.24, 0.96648, 83, -389.54, 89.22, 0.03352, 2, 6, -25.82, 34.82, 0.96474, 83, -394.52, 83.8, 0.03526, 2, 6, -28.94, 27.51, 0.96298, 83, -397.64, 76.5, 0.03702, 2, 6, -28.76, 18.11, 0.96204, 83, -397.46, 67.1, 0.03796, 2, 6, -26.16, 9.65, 0.96227, 83, -394.86, 58.64, 0.03773, 2, 6, -20.81, 4.12, 0.96302, 83, -389.5, 53.11, 0.03698, 2, 6, -15.29, 0.44, 0.9629, 83, -383.99, 49.43, 0.0371, 2, 6, -13.89, -3.77, 0.96247, 83, -382.59, 45.21, 0.03753, 2, 6, -10.83, -2.84, 0.96256, 83, -379.53, 46.14, 0.03744, 2, 6, -7.42, -4.13, 0.96242, 83, -376.12, 44.86, 0.03758, 2, 6, -10.31, -9.01, 0.96192, 83, -379.01, 39.97, 0.03808, 2, 6, -14.62, -6.3, 0.96221, 83, -383.32, 42.69, 0.03779, 2, 6, -12.09, 45.44, 0.96732, 83, -380.79, 94.42, 0.03268, 2, 6, -13.8, 42.37, 0.96689, 83, -382.5, 91.36, 0.03311, 2, 6, -13.88, 37.1, 0.96589, 83, -382.58, 86.08, 0.03411, 2, 6, -13.92, 31.1, 0.96491, 83, -382.62, 80.08, 0.03509, 2, 6, -15.19, 24.71, 0.96431, 83, -383.89, 73.69, 0.03569, 2, 6, -13.82, 18.6, 0.96358, 83, -382.52, 67.59, 0.03642, 2, 6, -12.68, 12.2, 0.96314, 83, -381.38, 61.18, 0.03686, 2, 6, -13.12, 5.94, 0.96316, 83, -381.82, 54.92, 0.03684, 2, 6, -13.69, -0.12, 0.96284, 83, -382.39, 48.87, 0.03716, 2, 6, -11.56, -4.75, 0.96236, 83, -380.26, 44.24, 0.03764, 2, 6, -18.58, 34.1, 0.96416, 83, -387.28, 83.08, 0.03584, 2, 6, -20.59, 28.36, 0.96176, 83, -389.29, 77.34, 0.03824, 2, 6, -18.39, 11.83, 0.96226, 83, -387.09, 60.81, 0.03774, 2, 6, -20.44, 20.21, 0.96085, 83, -389.14, 69.19, 0.03915, 2, 6, -31.09, 25.85, 0.96239, 83, -399.78, 74.84, 0.03761, 2, 6, -30.56, 16.54, 0.96169, 83, -399.26, 65.52, 0.03831, 2, 6, -37.83, 3.22, 0.9631, 83, -406.53, 52.2, 0.0369, 2, 6, -37.05, 35.25, 0.96638, 83, -405.75, 84.23, 0.03362, 2, 6, -43.2, 26.46, 0.9634, 83, -411.9, 75.44, 0.0366, 2, 6, -42.53, 13.47, 0.96214, 83, -411.23, 62.46, 0.03786, 2, 6, -25.39, -13.22, 0.96288, 83, -394.08, 35.77, 0.03712, 2, 6, -26.5, 46.14, 0.96754, 83, -395.2, 95.12, 0.03246], "hull": 15, "edges": [24, 26, 26, 28, 20, 22, 22, 24, 18, 20, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 32, 46, 46, 34, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 44, 72, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 100, 102, 102, 106, 106, 104, 108, 110, 116, 118, 112, 120, 120, 16, 114, 122, 122, 0, 4, 2, 12, 14, 14, 16, 16, 18, 2, 0, 0, 28], "width": 282, "height": 555}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.60814, 0.00112, 0.68531, 0.01416, 0.81484, 0.0414, 0.93931, 0.07429, 0.99958, 0.11402, 0.99455, 0.14995, 0.96186, 0.19592, 0.87123, 0.24997, 0.73943, 0.35662, 0.6583, 0.4253, 0.65408, 0.44804, 0.65497, 0.4726, 0.69993, 0.49116, 0.74275, 0.51459, 0.76, 0.54899, 0.73667, 0.60554, 0.55253, 0.7281, 0.51696, 0.75049, 0.49541, 0.76807, 0.47717, 0.78637, 0.46398, 0.8024, 0.48934, 0.81369, 0.54788, 0.83765, 0.53519, 0.85376, 0.50921, 0.86969, 0.5001, 0.89531, 0.50043, 0.91045, 0.50866, 0.92887, 0.51689, 0.94729, 0.46635, 0.98541, 0.38741, 0.99313, 0.23532, 1, 0.20472, 1, 0.10905, 0.99772, 0.03088, 0.94971, 0.06095, 0.93523, 0.09101, 0.92075, 0.11437, 0.9063, 0.11721, 0.89098, 0.11577, 0.87544, 0.13825, 0.81889, 0.16497, 0.80446, 0.19169, 0.79004, 0.20227, 0.77245, 0.20517, 0.75212, 0.20126, 0.72718, 0.15565, 0.57573, 0.18525, 0.53386, 0.18125, 0.51298, 0.13488, 0.49127, 0.09202, 0.46652, 0.09054, 0.43916, 0.0831, 0.41666, 0.00899, 0.35746, 0.00264, 0.24966, 0.03202, 0.18875, 0.09399, 0.13967, 0.17893, 0.09691, 0.23185, 0.04948, 0.33569, 0.02673, 0.47045, 0.00992, 0.15883, 0.92347, 0.27998, 0.93109, 0.40945, 0.92442, 0.14101, 0.95691, 0.29304, 0.95262, 0.43795, 0.94405, 0.44508, 0.90883, 0.17783, 0.90645, 0.18139, 0.89026, 0.43736, 0.89032, 0.17945, 0.87495, 0.42768, 0.87316, 0.29946, 0.06563, 0.51298, 0.0374, 0.23609, 0.26397, 0.1782, 0.37441, 0.1838, 0.42274, 0.1838, 0.44915, 0.20903, 0.47557, 0.24546, 0.49851, 0.2875, 0.52042, 0.27629, 0.54684, 0.27897, 0.5809, 0.3631, 0.70098, 0.52057, 0.58983, 0.48157, 0.5503, 0.43324, 0.5206, 0.40403, 0.49766, 0.38554, 0.47213, 0.36089, 0.44989, 0.36705, 0.42476, 0.48003, 0.37493, 0.63326, 0.26596, 0.29052, 0.19592, 0.32832, 0.13854, 0.37151, 0.09903, 0.61988, 0.06276, 0.70357, 0.09362, 0.73596, 0.13313, 0.70897, 0.19592], "triangles": [66, 27, 28, 66, 65, 62, 64, 61, 62, 64, 62, 65, 64, 35, 61, 34, 35, 64, 29, 66, 28, 65, 66, 29, 30, 65, 29, 33, 34, 64, 31, 32, 64, 65, 31, 64, 33, 64, 32, 30, 31, 65, 20, 42, 19, 22, 72, 21, 22, 23, 72, 41, 20, 21, 20, 41, 42, 24, 72, 23, 21, 40, 41, 72, 40, 21, 71, 40, 72, 39, 40, 71, 69, 71, 72, 39, 71, 69, 70, 72, 24, 69, 72, 70, 38, 39, 69, 25, 70, 24, 68, 38, 69, 37, 38, 68, 67, 70, 25, 67, 25, 26, 61, 37, 68, 36, 37, 61, 68, 70, 67, 70, 68, 69, 67, 62, 68, 62, 61, 68, 67, 63, 62, 63, 27, 66, 26, 63, 67, 27, 63, 26, 66, 62, 63, 61, 35, 36, 49, 50, 79, 88, 89, 11, 88, 11, 12, 80, 79, 89, 80, 89, 88, 49, 79, 80, 48, 49, 80, 81, 80, 88, 48, 80, 81, 87, 88, 12, 87, 12, 13, 81, 88, 87, 47, 48, 81, 82, 47, 81, 86, 87, 13, 86, 13, 14, 46, 47, 82, 82, 86, 83, 46, 82, 83, 87, 82, 81, 86, 82, 87, 85, 86, 14, 83, 86, 85, 15, 85, 14, 84, 83, 85, 84, 85, 15, 84, 45, 46, 84, 46, 83, 16, 84, 15, 17, 84, 16, 45, 84, 17, 44, 45, 17, 18, 44, 17, 43, 44, 18, 19, 43, 18, 42, 43, 19, 97, 96, 73, 94, 56, 95, 54, 55, 94, 98, 97, 2, 98, 2, 3, 96, 97, 98, 98, 3, 4, 99, 98, 4, 5, 99, 4, 99, 96, 98, 100, 99, 5, 99, 95, 96, 100, 95, 99, 94, 95, 100, 6, 100, 5, 7, 100, 6, 75, 54, 94, 93, 94, 100, 93, 100, 7, 75, 94, 93, 8, 93, 7, 53, 54, 75, 76, 53, 75, 92, 75, 93, 92, 93, 8, 76, 75, 92, 52, 53, 76, 91, 77, 76, 52, 76, 77, 92, 91, 76, 9, 92, 8, 91, 92, 9, 51, 52, 77, 10, 91, 9, 78, 51, 77, 78, 77, 91, 90, 78, 91, 90, 91, 10, 50, 51, 78, 89, 90, 10, 89, 10, 11, 79, 78, 90, 79, 90, 89, 50, 78, 79, 55, 56, 94, 74, 60, 0, 74, 0, 1, 59, 60, 74, 97, 74, 1, 97, 1, 2, 73, 58, 59, 73, 59, 74, 97, 73, 74, 56, 57, 95, 57, 58, 73, 57, 73, 96, 95, 57, 96], "vertices": [3, 9, 43.18, 156.22, 0.04572, 10, -31.29, 47.16, 0.88857, 14, -102.94, 0.98, 0.06572, 2, 10, -10.99, 59.41, 0.81652, 14, -90.08, 20.9, 0.18348, 2, 10, 29.04, 77.94, 0.39893, 14, -62.25, 55.12, 0.60107, 2, 10, 74.96, 93.2, 0.37168, 14, -27.69, 89, 0.62832, 2, 10, 123.61, 91.6, 0.12894, 14, 16.72, 108.92, 0.87106, 2, 10, 163.09, 76.92, 0.04571, 14, 58.63, 113.06, 0.95429, 1, 14, 113.06, 112.23, 1, 1, 14, 178.58, 99.09, 1, 1, 14, 306.48, 84.13, 1, 3, 14, 388.74, 75.37, 0.93291, 15, -51.61, 83.59, 0.06709, 45, 675.82, -23.09, 0, 3, 14, 415.3, 77.74, 0.76274, 15, -24.98, 82.15, 0.23726, 45, 649.22, -24.96, 0, 3, 14, 443.83, 81.58, 0.44362, 15, 3.81, 81.88, 0.55638, 45, 620.62, -28.25, 0, 3, 14, 464.07, 94.8, 0.21319, 15, 25.73, 92.07, 0.78681, 45, 600.12, -41.08, 0, 3, 14, 490.05, 108.25, 0.07646, 15, 53.36, 101.67, 0.92354, 45, 573.9, -54.03, 0, 3, 14, 529.53, 117.35, 0.00881, 15, 93.73, 105.04, 0.99119, 45, 534.25, -62.38, 0, 2, 15, 159.91, 98.45, 1, 45, 467.76, -64.02, 0, 1, 15, 302.8, 52.76, 1, 1, 15, 328.89, 43.96, 1, 2, 15, 349.41, 38.55, 0.96587, 46, 195.91, -36.24, 0.03413, 2, 15, 370.79, 33.9, 0.69611, 46, 174.22, -33.37, 0.30389, 2, 15, 389.51, 30.49, 0.21212, 46, 155.28, -31.5, 0.78788, 2, 15, 402.85, 36.22, 0.03451, 46, 142.46, -38.32, 0.96549, 1, 46, 115.34, -53.89, 1, 1, 46, 96.3, -52.15, 1, 1, 46, 77.27, -47.28, 1, 2, 46, 47.18, -47.11, 0.96691, 45, 124.16, -45.11, 0.03309, 2, 46, 29.48, -48.36, 0.85315, 45, 106.52, -47.09, 0.14685, 2, 46, 8.06, -51.7, 0.57522, 45, 85.27, -51.32, 0.42478, 2, 46, -13.35, -55.05, 0.27771, 45, 64.01, -55.56, 0.72229, 2, 46, -58.71, -46.13, 0.00498, 45, 18.32, -48.53, 0.99502, 1, 45, 7.33, -31.06, 1, 1, 45, -4.5, 3.62, 1, 1, 45, -5.27, 10.77, 1, 1, 45, -5.01, 33.41, 1, 2, 46, -23.67, 58.73, 0.1596, 45, 48.96, 57.69, 0.8404, 2, 46, -6.28, 52.79, 0.32612, 45, 66.59, 52.49, 0.67388, 2, 46, 11.12, 46.86, 0.63431, 45, 84.21, 47.28, 0.36569, 2, 46, 28.38, 42.49, 0.89743, 45, 101.65, 43.63, 0.10257, 2, 46, 46.34, 43, 0.99069, 45, 119.57, 44.9, 0.00931, 1, 46, 64.49, 44.53, 1, 3, 15, 407.55, -46.38, 0.00676, 46, 130.97, 43.61, 0.99323, 45, 204.1, 49.03, 0, 3, 15, 390.75, -39.81, 0.0958, 46, 148.25, 38.45, 0.9042, 45, 221.58, 44.59, 0, 3, 15, 373.95, -33.25, 0.41478, 46, 165.54, 33.3, 0.58522, 45, 239.06, 40.16, 0, 3, 15, 353.38, -30.41, 0.86891, 46, 186.27, 32.17, 0.13109, 45, 259.82, 39.89, 0, 2, 15, 329.57, -29.33, 0.99584, 46, 210.09, 33.05, 0.00416, 1, 15, 300.33, -29.76, 1, 1, 15, 122.68, -37.48, 1, 1, 15, 73.73, -29.7, 1, 2, 14, 504.81, -22.89, 0.00156, 15, 49.25, -30.23, 0.99844, 2, 14, 480.95, -36.91, 0.16193, 15, 23.62, -40.7, 0.83807, 2, 14, 453.44, -50.56, 0.65068, 15, -5.55, -50.28, 0.34932, 2, 14, 421.68, -54.94, 0.95018, 15, -37.62, -50.09, 0.04982, 1, 14, 395.74, -60, 1, 2, 9, 393.15, -111.65, 0.01179, 14, 329.1, -86.03, 0.98821, 2, 9, 273.07, -72.35, 0.11123, 14, 203.96, -103.44, 0.88877, 3, 9, 207.72, -42.81, 0.28504, 14, 132.28, -105.6, 0.71, 87, -338.74, -131.14, 0.00496, 4, 9, 157.95, -10.48, 0.47162, 10, 83.48, -119.54, 0.02286, 14, 73.37, -98.4, 0.48673, 87, -324.17, -73.61, 0.01878, 4, 9, 116.95, 24.56, 0.67749, 10, 42.47, -84.5, 0.08628, 14, 21.14, -84.92, 0.20317, 87, -304.21, -23.49, 0.03307, 4, 9, 68.33, 54.25, 0.58417, 10, -6.15, -54.81, 0.33272, 14, -35.58, -79.6, 0.04723, 87, -291.78, 32.1, 0.03587, 4, 9, 50.95, 85.94, 0.32659, 10, -23.52, -23.12, 0.64423, 14, -65.1, -58.75, 0.00168, 87, -267.37, 58.76, 0.0275, 4, 9, 42.51, 122.27, 0.12871, 10, -31.97, 13.21, 0.84588, 14, -88.64, -29.82, 0.00475, 87, -235.71, 78.46, 0.02066, 2, 46, 8.98, 30.75, 0.67423, 45, 82.75, 31.09, 0.32577, 2, 46, 1.94, 1.75, 0.59189, 45, 76.92, 1.83, 0.40811, 2, 46, 11.73, -28.1, 0.71622, 45, 87.95, -27.58, 0.28378, 2, 46, -30.4, 32.35, 0.068, 45, 43.34, 31.06, 0.932, 1, 45, 52.16, -3.92, 1, 2, 46, -10.78, -36.29, 0.28676, 45, 65.8, -36.71, 0.71324, 2, 46, 30.51, -35.25, 0.88865, 45, 107.01, -33.95, 0.11135, 2, 46, 29.18, 27.6, 0.95072, 45, 103.06, 28.79, 0.04928, 2, 46, 48.17, 28.01, 0.99807, 45, 122.02, 29.99, 0.00193, 2, 46, 52.05, -32.02, 0.9872, 45, 128.4, -29.82, 0.0128, 1, 46, 66.05, 29.64, 1, 1, 46, 71.96, -28.43, 1, 4, 9, 91.38, 63.19, 0.4683, 10, 16.9, -45.87, 0.4397, 14, -18.79, -61.45, 0.05853, 87, -275.89, 13.16, 0.03346, 3, 10, 1.75, 12.29, 0.91331, 14, -57.94, -15.84, 0.0674, 87, -225.71, 46.25, 0.01928, 3, 9, 306.62, -25.82, 0.06689, 14, 213.67, -46.91, 0.91311, 87, -290.78, -219.28, 0.02, 3, 9, 424.77, -80.41, 0.00178, 14, 343.8, -44.08, 0.97822, 87, -304.39, -348.73, 0.02, 2, 14, 399.82, -35.63, 0.98057, 87, -303.07, -405.37, 0.01943, 3, 14, 430.53, -31.73, 0.90982, 15, -25.54, -28.37, 0.07222, 87, -303.07, -436.33, 0.01796, 3, 14, 460.49, -21.94, 0.35463, 15, 5.52, -22.97, 0.62736, 87, -297.14, -467.28, 0.01802, 2, 15, 32.54, -14.86, 0.98187, 87, -288.58, -494.16, 0.01813, 2, 15, 58.39, -5.41, 0.98106, 87, -278.7, -519.85, 0.01894, 2, 15, 89.3, -8.57, 0.98484, 87, -281.33, -550.81, 0.01516, 2, 15, 129.22, -8.61, 0.99007, 87, -280.7, -590.73, 0.00993, 1, 15, 270.27, 8.79, 1, 3, 15, 140.64, 47.98, 0.99307, 45, 480.64, -11.56, 0, 87, -223.93, -601.19, 0.00693, 4, 14, 539.31, 52.64, 0.00075, 15, 94.17, 39.6, 0.9866, 45, 525.71, 2.51, 0, 87, -233.09, -554.87, 0.01265, 4, 14, 506.21, 36.98, 0.01152, 15, 59.18, 28.83, 0.97219, 45, 559.11, 17.53, 0, 87, -244.45, -520.06, 0.01629, 4, 14, 480.4, 26.78, 0.05288, 15, 32.18, 22.42, 0.93022, 45, 585.1, 27.24, 0, 87, -251.32, -493.18, 0.01691, 4, 14, 451.26, 18.7, 0.48485, 15, 2.18, 18.58, 0.49682, 45, 614.39, 34.76, 0, 87, -255.66, -463.25, 0.01833, 4, 14, 426.14, 9.67, 0.9746, 15, -23.98, 13.22, 0.00602, 45, 639.69, 43.31, 0, 87, -261.45, -437.18, 0.01938, 4, 14, 396.74, 7.39, 0.97994, 15, -53.39, 15.17, 0.00028, 45, 669.12, 45.02, 0, 87, -260.01, -407.74, 0.01978, 2, 14, 335.45, 26.36, 0.98025, 87, -233.46, -349.33, 0.01975, 2, 14, 204.22, 45.98, 0.98022, 87, -197.45, -221.62, 0.01978, 3, 9, 235.25, 11.99, 0.17994, 14, 132.95, -44.27, 0.79467, 87, -277.99, -139.54, 0.02539, 4, 9, 174.45, 42.07, 0.30732, 10, 99.98, -66.99, 0.00417, 14, 65.12, -43.94, 0.65875, 87, -269.11, -72.29, 0.02976, 4, 9, 133.88, 66.6, 0.3535, 10, 59.41, -42.45, 0.16112, 14, 17.9, -39.71, 0.45255, 87, -258.96, -25.98, 0.03283, 3, 10, 37.98, 26.49, 0.36583, 14, -31.63, 12.83, 0.61435, 87, -200.59, 16.53, 0.01983, 3, 10, 78.55, 33.46, 0.1004, 14, 1.76, 36.9, 0.88123, 87, -180.92, -19.63, 0.01837, 3, 10, 124.85, 25.74, 0.06286, 14, 46.75, 50.29, 0.91858, 87, -173.31, -65.95, 0.01856, 2, 14, 120.55, 53.28, 0.98021, 87, -179.66, -139.54, 0.01979], "hull": 61, "edges": [8, 10, 10, 12, 12, 14, 28, 30, 34, 36, 40, 42, 42, 44, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 94, 96, 100, 102, 102, 104, 106, 108, 108, 110, 110, 112, 116, 118, 118, 120, 14, 16, 112, 114, 114, 116, 120, 0, 4, 6, 6, 8, 0, 2, 2, 4, 22, 24, 18, 20, 20, 22, 24, 26, 26, 28, 92, 94, 96, 98, 98, 100, 16, 18, 104, 106, 84, 86, 86, 88, 36, 38, 38, 40, 80, 82, 82, 84, 88, 90, 90, 92, 30, 32, 32, 34, 78, 80, 48, 50, 44, 46, 46, 48, 74, 76, 76, 78, 72, 74, 52, 54, 54, 56, 122, 124, 124, 126, 68, 70, 70, 72, 128, 130, 132, 130, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 150, 188, 188, 190, 190, 192, 194, 196, 196, 198, 198, 200, 200, 186], "width": 235, "height": 1172}}}}, {"name": "01"}, {"name": "02"}, {"name": "03"}, {"name": "04"}, {"name": "05"}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 10.57, "curve": [0.444, 10.57, 0.889, -10.98, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -10.99, "curve": [1.778, -10.99, 2.222, 10.56, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 10.57, "curve": [3.111, 10.57, 3.556, -10.98, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -10.99, "curve": [4.444, -10.99, 4.889, 10.54, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 10.57, "curve": [5.667, 10.58, 6, -51.71, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -51.71, "curve": [6.778, -51.72, 7.222, -28.36, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -28.36, "curve": [8.111, -28.35, 8.556, -51.69, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -51.71, "curve": [9.444, -51.73, 9.889, 10.56, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 10.57, "curve": [10.778, 10.57, 11.222, -10.98, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -10.99, "curve": [12.111, -10.99, 12.556, 10.57, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 10.57}]}, "ALL2": {"translate": [{"y": -14.87, "curve": [0.444, 0, 0.889, 0, 0.444, -14.87, 0.889, 15.79]}, {"time": 1.3333, "y": 15.79, "curve": [1.778, 0, 2.222, 0, 1.778, 15.8, 2.222, -14.86]}, {"time": 2.6667, "y": -14.87, "curve": [3.111, 0, 3.556, 0, 3.111, -14.87, 3.556, 15.79]}, {"time": 4, "y": 15.79, "curve": [4.444, 0, 4.889, 0, 4.444, 15.8, 4.889, -14.85]}, {"time": 5.3333, "y": -14.87, "curve": [5.667, 0, 6, 0, 5.667, -14.88, 6, 23.19]}, {"time": 6.3333, "y": 23.2, "curve": [6.778, 0, 7.222, 0, 6.778, 23.2, 7.222, 8.93]}, {"time": 7.6667, "y": 8.92, "curve": [8.111, 0, 8.556, 0, 8.111, 8.92, 8.556, 23.19]}, {"time": 9, "y": 23.2, "curve": [9.444, 0, 9.889, 0, 9.444, 23.2, 9.889, -14.86]}, {"time": 10.3333, "y": -14.87, "curve": [10.778, 0, 11.222, 0, 10.778, -14.87, 11.222, 15.79]}, {"time": 11.6667, "y": 15.79, "curve": [12.111, 0, 12.556, 0, 12.111, 15.8, 12.556, -14.87]}, {"time": 13, "y": -14.87}]}, "body": {"rotate": [{"value": 0.81, "curve": [0.444, 0.81, 0.889, -3.07]}, {"time": 1.3333, "value": -3.07, "curve": [1.778, -3.07, 2.222, 0.81]}, {"time": 2.6667, "value": 0.81, "curve": [3.111, 0.81, 3.556, -3.07]}, {"time": 4, "value": -3.07, "curve": [4.444, -3.07, 4.889, 0.8]}, {"time": 5.3333, "value": 0.81, "curve": [5.667, 0.81, 6, -19.05]}, {"time": 6.3333, "value": -19.05, "curve": [6.778, -19.05, 7.222, -11.61]}, {"time": 7.6667, "value": -11.6, "curve": [8.111, -11.6, 8.556, -19.05]}, {"time": 9, "value": -19.05, "curve": [9.444, -19.06, 9.889, 0.81]}, {"time": 10.3333, "value": 0.81, "curve": [10.778, 0.81, 11.222, -3.07]}, {"time": 11.6667, "value": -3.07, "curve": [12.111, -3.07, 12.556, 0.81]}, {"time": 13, "value": 0.81}], "translate": [{"y": -8.85, "curve": [0.057, 0, 0.112, 0, 0.057, -9.29, 0.112, -9.63]}, {"time": 0.1667, "y": -9.63, "curve": [0.611, 0, 1.056, 0, 0.611, -9.63, 1.056, 7.09]}, {"time": 1.5, "y": 7.1, "curve": [1.944, 0, 2.389, 0, 1.944, 7.1, 2.389, -9.63]}, {"time": 2.8333, "y": -9.63, "curve": [3.278, 0, 3.722, 0, 3.278, -9.64, 3.722, 7.09]}, {"time": 4.1667, "y": 7.1, "curve": [4.611, 0, 5.056, 0, 4.611, 7.1, 5.056, -9.62]}, {"time": 5.5, "y": -9.63, "curve": [5.833, 0, 6.167, 0, 5.833, -9.64, 6.167, 11.41]}, {"time": 6.5, "y": 11.41, "curve": [6.944, 0, 7.389, 0, 6.944, 11.42, 7.389, 3.52]}, {"time": 7.8333, "y": 3.52, "curve": [8.278, 0, 8.722, 0, 8.278, 3.52, 8.722, 11.41]}, {"time": 9.1667, "y": 11.41, "curve": [9.611, 0, 10.056, 0, 9.611, 11.42, 10.056, -9.63]}, {"time": 10.5, "y": -9.63, "curve": [10.944, 0, 11.389, 0, 10.944, -9.64, 11.389, 7.09]}, {"time": 11.8333, "y": 7.1, "curve": [12.223, 0, 12.613, 0, 12.223, 7.1, 12.613, -5.67]}, {"time": 13, "y": -8.85}], "scale": [{"y": 1.036, "curve": [0.057, 1, 0.112, 1, 0.057, 1.037, 0.112, 1.039]}, {"time": 0.1667, "y": 1.039, "curve": [0.611, 1, 1.056, 1, 0.611, 1.039, 1.056, 0.973]}, {"time": 1.5, "y": 0.973, "curve": [1.944, 1, 2.389, 1, 1.944, 0.973, 2.389, 1.039]}, {"time": 2.8333, "y": 1.039, "curve": [3.278, 1, 3.722, 1, 3.278, 1.039, 3.722, 0.973]}, {"time": 4.1667, "y": 0.973, "curve": [4.611, 1, 5.056, 1, 4.611, 0.973, 5.056, 1.039]}, {"time": 5.5, "y": 1.039, "curve": [5.833, 1, 6.167, 1, 5.833, 1.039, 6.167, 0.973]}, {"time": 6.5, "y": 0.973, "curve": [6.944, 1, 7.389, 1, 6.944, 0.973, 7.389, 0.997]}, {"time": 7.8333, "y": 0.997, "curve": [8.278, 1, 8.722, 1, 8.278, 0.997, 8.722, 0.973]}, {"time": 9.1667, "y": 0.973, "curve": [9.611, 1, 10.056, 1, 9.611, 0.973, 10.056, 1.039]}, {"time": 10.5, "y": 1.039, "curve": [10.944, 1, 11.389, 1, 10.944, 1.039, 11.389, 0.973]}, {"time": 11.8333, "y": 0.973, "curve": [12.223, 1, 12.613, 1, 12.223, 0.973, 12.613, 1.023]}, {"time": 13, "y": 1.036}]}, "body2": {"rotate": [{"value": -2.3, "curve": [0.057, -2.42, 0.112, -2.52]}, {"time": 0.1667, "value": -2.52, "curve": [0.611, -2.52, 1.056, 2.13]}, {"time": 1.5, "value": 2.13, "curve": [1.944, 2.13, 2.389, -2.52]}, {"time": 2.8333, "value": -2.52, "curve": [3.278, -2.52, 3.722, 2.13]}, {"time": 4.1667, "value": 2.13, "curve": [4.611, 2.13, 5.056, -2.52]}, {"time": 5.5, "value": -2.52, "curve": [5.833, -2.52, 6.167, 4.52]}, {"time": 6.5, "value": 4.52, "curve": [6.944, 4.52, 7.389, 1.88]}, {"time": 7.8333, "value": 1.88, "curve": [8.278, 1.88, 8.722, 4.51]}, {"time": 9.1667, "value": 4.52, "curve": [9.611, 4.52, 10.056, -2.52]}, {"time": 10.5, "value": -2.52, "curve": [10.944, -2.52, 11.389, 2.13]}, {"time": 11.8333, "value": 2.13, "curve": [12.223, 2.13, 12.613, -1.42]}, {"time": 13, "value": -2.3}], "translate": [{"x": -6.15, "curve": [0.114, -7.83, 0.224, -9.09, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -9.09, "curve": [0.778, -9.09, 1.222, 9.28, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 9.28, "curve": [2.111, 9.28, 2.556, -9.08, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -9.09, "curve": [3.444, -9.09, 3.889, 9.28, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 9.28, "curve": [4.778, 9.28, 5.222, -9.08, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -9.09, "curve": [6, -9.09, 6.333, 12.52, 6, 0, 6.333, -3.58]}, {"time": 6.6667, "x": 12.52, "y": -3.58, "curve": [7.111, 12.52, 7.556, 4.42, 7.111, -3.58, 7.556, -2.24]}, {"time": 8, "x": 4.42, "y": -2.24, "curve": [8.444, 4.42, 8.889, 12.52, 8.444, -2.24, 8.889, -3.58]}, {"time": 9.3333, "x": 12.52, "y": -3.58, "curve": [9.778, 12.53, 10.222, -9.08, 9.778, -3.58, 10.222, 0]}, {"time": 10.6667, "x": -9.09, "curve": [11.111, -9.09, 11.556, 9.28, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 9.28, "curve": [12.335, 9.28, 12.67, -1, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -6.15}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.025, 0.778, 1, 1.222, 1.025]}, {"time": 1.6667, "x": 1.025, "y": 1.025, "curve": [2.111, 1.025, 2.556, 1, 2.111, 1.025, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.025, 3.444, 1, 3.889, 1.025]}, {"time": 4.3333, "x": 1.025, "y": 1.025, "curve": [4.778, 1.025, 5.222, 1, 4.778, 1.025, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.025, 6, 1, 6.333, 1.025]}, {"time": 6.6667, "x": 1.025, "y": 1.025, "curve": [7.111, 1.025, 7.556, 1.016, 7.111, 1.025, 7.556, 1.016]}, {"time": 8, "x": 1.016, "y": 1.016, "curve": [8.444, 1.016, 8.889, 1.025, 8.444, 1.016, 8.889, 1.025]}, {"time": 9.3333, "x": 1.025, "y": 1.025, "curve": [9.778, 1.025, 10.222, 1, 9.778, 1.025, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.025, 11.111, 1, 11.556, 1.025]}, {"time": 12, "x": 1.025, "y": 1.025, "curve": [12.335, 1.025, 12.67, 1.011, 12.335, 1.025, 12.67, 1.011]}, {"time": 13, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.44, "curve": [0.114, -1.94, 0.224, -2.32]}, {"time": 0.3333, "value": -2.32, "curve": [0.778, -2.32, 1.222, 3.18]}, {"time": 1.6667, "value": 3.18, "curve": [2.111, 3.19, 2.556, -2.32]}, {"time": 3, "value": -2.32, "curve": [3.444, -2.32, 3.889, 3.18]}, {"time": 4.3333, "value": 3.18, "curve": [4.778, 3.19, 5.222, -2.32]}, {"time": 5.6667, "value": -2.32, "curve": [6, -2.32, 6.333, 7.83]}, {"time": 6.6667, "value": 7.83, "curve": [7.111, 7.83, 7.556, 4.02]}, {"time": 8, "value": 4.02, "curve": [8.444, 4.02, 8.889, 7.83]}, {"time": 9.3333, "value": 7.83, "curve": [9.778, 7.83, 10.222, -2.32]}, {"time": 10.6667, "value": -2.32, "curve": [11.111, -2.32, 11.556, 3.18]}, {"time": 12, "value": 3.18, "curve": [12.335, 3.18, 12.67, 0.1]}, {"time": 13, "value": -1.44}]}, "head": {"rotate": [{"value": -0.57, "curve": [0.168, -1.53, 0.334, -2.32]}, {"time": 0.5, "value": -2.32, "curve": [0.944, -2.32, 1.389, 3.18]}, {"time": 1.8333, "value": 3.18, "curve": [2.278, 3.19, 2.722, -2.32]}, {"time": 3.1667, "value": -2.32, "curve": [3.611, -2.32, 4.056, 3.18]}, {"time": 4.5, "value": 3.18, "curve": [4.944, 3.19, 5.389, -2.32]}, {"time": 5.8333, "value": -2.32, "curve": [6.167, -2.32, 6.5, 7.83]}, {"time": 6.8333, "value": 7.83, "curve": [7.278, 7.83, 7.722, 4.02]}, {"time": 8.1667, "value": 4.02, "curve": [8.611, 4.02, 9.056, 7.83]}, {"time": 9.5, "value": 7.83, "curve": [9.944, 7.83, 10.389, -2.32]}, {"time": 10.8333, "value": -2.32, "curve": [11.278, -2.32, 11.722, 3.18]}, {"time": 12.1667, "value": 3.18, "curve": [12.445, 3.18, 12.724, 1.05]}, {"time": 13, "value": -0.57}]}, "sh_L": {"translate": [{"x": -0.62, "curve": [0.114, -1.38, 0.224, -1.94, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -1.94, "curve": [0.778, -1.94, 1.222, 6.31, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.31, "curve": [2.111, 6.31, 2.556, -1.94, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -1.94, "curve": [3.444, -1.95, 3.889, 6.31, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.31, "curve": [4.778, 6.31, 5.222, -1.94, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -1.94, "curve": [6, -1.95, 6.333, 6.31, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.31, "curve": [7.111, 6.31, 7.556, 3.22, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 3.22, "curve": [8.444, 3.22, 8.889, 6.31, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.31, "curve": [9.778, 6.31, 10.222, -1.94, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -1.94, "curve": [11.111, -1.95, 11.556, 6.31, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.31, "curve": [12.335, 6.31, 12.67, 1.69, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -0.62}]}, "sh_R": {"translate": [{"x": -3.32, "curve": [0.114, -4.74, 0.224, -5.79, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.79, "curve": [0.778, -5.79, 1.222, 9.63, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 9.64, "curve": [2.111, 9.64, 2.556, -5.79, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.79, "curve": [3.444, -5.8, 3.889, 9.63, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 9.64, "curve": [4.778, 9.64, 5.222, -5.79, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.79, "curve": [6, -5.8, 6.333, 9.64, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 9.64, "curve": [7.111, 9.64, 7.556, 3.85, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 3.85, "curve": [8.444, 3.85, 8.889, 9.63, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 9.64, "curve": [9.778, 9.64, 10.222, -5.79, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.79, "curve": [11.111, -5.8, 11.556, 9.63, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 9.64, "curve": [12.335, 9.64, 12.67, 1, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.32}]}, "tun": {"rotate": [{"value": 2.64, "curve": [0.444, 2.64, 0.889, -4.48]}, {"time": 1.3333, "value": -4.48, "curve": [1.778, -4.48, 2.222, 2.64]}, {"time": 2.6667, "value": 2.64, "curve": [3.111, 2.64, 3.556, -4.48]}, {"time": 4, "value": -4.48, "curve": [4.444, -4.48, 4.889, 2.63]}, {"time": 5.3333, "value": 2.64, "curve": [5.667, 2.65, 6, -14.72]}, {"time": 6.3333, "value": -14.72, "curve": [6.778, -14.73, 7.222, -8.21]}, {"time": 7.6667, "value": -8.21, "curve": [8.111, -8.21, 8.556, -14.72]}, {"time": 9, "value": -14.72, "curve": [9.444, -14.73, 9.889, 2.64]}, {"time": 10.3333, "value": 2.64, "curve": [10.778, 2.64, 11.222, -4.48]}, {"time": 11.6667, "value": -4.48, "curve": [12.111, -4.48, 12.556, 2.64]}, {"time": 13, "value": 2.64}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, -0.01, 7.222, 11.83]}, {"time": 7.6667, "value": 11.83, "curve": [8.111, 11.83, 8.556, 0]}, {"time": 9}]}, "leg_R4": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.4]}, {"time": 7.6667, "value": 1.4, "curve": [8.111, 1.4, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 11.25]}, {"time": 7.6667, "value": 11.26, "curve": [8.111, 11.26, 8.556, 0]}, {"time": 9}]}, "leg_L4": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0]}, {"time": 9}]}, "arm_R": {"rotate": [{"value": 0.59, "curve": [0.168, 1.37, 0.334, 2.01]}, {"time": 0.5, "value": 2.01, "curve": [0.944, 2.01, 1.389, -2.45]}, {"time": 1.8333, "value": -2.46, "curve": [2.278, -2.46, 2.722, 2.01]}, {"time": 3.1667, "value": 2.01, "curve": [3.611, 2.01, 4.056, -2.45]}, {"time": 4.5, "value": -2.46, "curve": [4.944, -2.46, 5.389, 2]}, {"time": 5.8333, "value": 2.01, "curve": [6.167, 2.01, 6.5, -2.45]}, {"time": 6.8333, "value": -2.46, "curve": [7.278, -2.46, 7.722, -0.78]}, {"time": 8.1667, "value": -0.78, "curve": [8.611, -0.78, 9.056, -2.45]}, {"time": 9.5, "value": -2.46, "curve": [9.944, -2.46, 10.389, 2.01]}, {"time": 10.8333, "value": 2.01, "curve": [11.278, 2.01, 11.722, -2.45]}, {"time": 12.1667, "value": -2.46, "curve": [12.445, -2.46, 12.724, -0.72]}, {"time": 13, "value": 0.59}]}, "arm_R2": {"rotate": [{"value": -0.22, "curve": [0.225, 0.88, 0.446, 2.01]}, {"time": 0.6667, "value": 2.01, "curve": [1.111, 2.01, 1.556, -2.45]}, {"time": 2, "value": -2.46, "curve": [2.444, -2.46, 2.889, 2.01]}, {"time": 3.3333, "value": 2.01, "curve": [3.778, 2.01, 4.222, -2.45]}, {"time": 4.6667, "value": -2.46, "curve": [5.111, -2.46, 5.556, 2]}, {"time": 6, "value": 2.01, "curve": [6.333, 2.01, 6.667, -2.45]}, {"time": 7, "value": -2.46, "curve": [7.444, -2.46, 7.889, -0.78]}, {"time": 8.3333, "value": -0.78, "curve": [8.778, -0.78, 9.222, -2.45]}, {"time": 9.6667, "value": -2.46, "curve": [10.111, -2.46, 10.556, 2.01]}, {"time": 11, "value": 2.01, "curve": [11.444, 2.01, 11.889, -2.45]}, {"time": 12.3333, "value": -2.46, "curve": [12.557, -2.46, 12.781, -1.35]}, {"time": 13, "value": -0.22}]}, "arm_R3": {"rotate": [{"value": -1.04, "curve": [0.279, 0.27, 0.556, 2.01]}, {"time": 0.8333, "value": 2.01, "curve": [1.278, 2.01, 1.722, -2.45]}, {"time": 2.1667, "value": -2.46, "curve": [2.611, -2.46, 3.056, 2.01]}, {"time": 3.5, "value": 2.01, "curve": [3.944, 2.01, 4.389, -2.45]}, {"time": 4.8333, "value": -2.46, "curve": [5.278, -2.46, 5.722, 2]}, {"time": 6.1667, "value": 2.01, "curve": [6.5, 2.01, 6.833, -2.45]}, {"time": 7.1667, "value": -2.46, "curve": [7.611, -2.46, 8.056, -0.78]}, {"time": 8.5, "value": -0.78, "curve": [8.944, -0.78, 9.389, -2.45]}, {"time": 9.8333, "value": -2.46, "curve": [10.278, -2.46, 10.722, 2.01]}, {"time": 11.1667, "value": 2.01, "curve": [11.611, 2.01, 12.056, -2.45]}, {"time": 12.5, "value": -2.46, "curve": [12.667, -2.46, 12.835, -1.83]}, {"time": 13, "value": -1.04}]}, "arm_R4": {"rotate": [{"value": -1.74, "curve": [0.336, -0.48, 0.668, 2.01]}, {"time": 1, "value": 2.01, "curve": [1.444, 2.01, 1.889, -2.45]}, {"time": 2.3333, "value": -2.46, "curve": [2.778, -2.46, 3.222, 2.01]}, {"time": 3.6667, "value": 2.01, "curve": [4.111, 2.01, 4.556, -2.45]}, {"time": 5, "value": -2.46, "curve": [5.444, -2.46, 5.889, 2]}, {"time": 6.3333, "value": 2.01, "curve": [6.667, 2.01, 7, -2.45]}, {"time": 7.3333, "value": -2.46, "curve": [7.778, -2.46, 8.222, -0.78]}, {"time": 8.6667, "value": -0.78, "curve": [9.111, -0.78, 9.556, -2.45]}, {"time": 10, "value": -2.46, "curve": [10.444, -2.46, 10.889, 2.01]}, {"time": 11.3333, "value": 2.01, "curve": [11.778, 2.01, 12.222, -2.45]}, {"time": 12.6667, "value": -2.46, "curve": [12.779, -2.46, 12.892, -2.17]}, {"time": 13, "value": -1.74}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -11.29]}, {"time": 7.6667, "value": -11.29, "curve": [8.111, -11.29, 8.556, 0]}, {"time": 9}]}, "arm_L3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0.01]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 15.84]}, {"time": 7.6667, "value": 15.84, "curve": [8.111, 15.85, 8.556, 0]}, {"time": 9}]}, "hair_F2": {"rotate": [{"value": -0.81, "curve": [0.279, 1.43, 0.556, 4.41]}, {"time": 0.8333, "value": 4.41, "curve": [1.278, 4.41, 1.722, -3.24]}, {"time": 2.1667, "value": -3.25, "curve": [2.611, -3.25, 3.056, 4.41]}, {"time": 3.5, "value": 4.41, "curve": [3.944, 4.41, 4.389, -3.24]}, {"time": 4.8333, "value": -3.25, "curve": [5.278, -3.25, 5.722, 4.41]}, {"time": 6.1667, "value": 4.41, "curve": [6.5, 4.41, 6.833, -3.25]}, {"time": 7.1667, "value": -3.25, "curve": [7.611, -3.25, 8.056, -0.38]}, {"time": 8.5, "value": -0.38, "curve": [8.944, -0.38, 9.389, -3.24]}, {"time": 9.8333, "value": -3.25, "curve": [10.278, -3.25, 10.722, 4.41]}, {"time": 11.1667, "value": 4.41, "curve": [11.611, 4.41, 12.056, -3.24]}, {"time": 12.5, "value": -3.25, "curve": [12.667, -3.25, 12.835, -2.17]}, {"time": 13, "value": -0.81}]}, "hair_F6": {"rotate": [{"value": -0.81, "curve": [0.279, 1.43, 0.556, 4.41]}, {"time": 0.8333, "value": 4.41, "curve": [1.278, 4.41, 1.722, -3.24]}, {"time": 2.1667, "value": -3.25, "curve": [2.611, -3.25, 3.056, 4.41]}, {"time": 3.5, "value": 4.41, "curve": [3.944, 4.41, 4.389, -3.24]}, {"time": 4.8333, "value": -3.25, "curve": [5.278, -3.25, 5.722, 4.41]}, {"time": 6.1667, "value": 4.41, "curve": [6.5, 4.41, 6.833, -3.25]}, {"time": 7.1667, "value": -3.25, "curve": [7.611, -3.25, 8.056, -0.38]}, {"time": 8.5, "value": -0.38, "curve": [8.944, -0.38, 9.389, -3.24]}, {"time": 9.8333, "value": -3.25, "curve": [10.278, -3.25, 10.722, 4.41]}, {"time": 11.1667, "value": 4.41, "curve": [11.611, 4.41, 12.056, -3.24]}, {"time": 12.5, "value": -3.25, "curve": [12.667, -3.25, 12.835, -2.17]}, {"time": 13, "value": -0.81}]}, "hair_R2": {"rotate": [{"value": -0.68, "curve": [0.225, 2.32, 0.446, 5.35]}, {"time": 0.6667, "value": 5.35, "curve": [1.111, 5.36, 1.556, -6.7]}, {"time": 2, "value": -6.7, "curve": [2.444, -6.71, 2.889, 5.35]}, {"time": 3.3333, "value": 5.35, "curve": [3.778, 5.36, 4.222, -6.7]}, {"time": 4.6667, "value": -6.7, "curve": [5.111, -6.71, 5.556, 5.35]}, {"time": 6, "value": 5.35, "curve": [6.333, 5.36, 6.667, -6.7]}, {"time": 7, "value": -6.7, "curve": [7.444, -6.71, 7.889, 5.35]}, {"time": 8.3333, "value": 5.35, "curve": [8.778, 5.36, 9.222, -6.7]}, {"time": 9.6667, "value": -6.7, "curve": [10.111, -6.71, 10.556, 5.35]}, {"time": 11, "value": 5.35, "curve": [11.444, 5.36, 11.889, -6.7]}, {"time": 12.3333, "value": -6.7, "curve": [12.557, -6.7, 12.781, -3.71]}, {"time": 13, "value": -0.68}]}, "hair_Ba": {"rotate": [{"value": 2.38, "curve": [0.168, 4.59, 0.334, 6.41]}, {"time": 0.5, "value": 6.41, "curve": [0.944, 6.41, 1.389, -6.25]}, {"time": 1.8333, "value": -6.26, "curve": [2.278, -6.26, 2.722, 6.4]}, {"time": 3.1667, "value": 6.41, "curve": [3.611, 6.41, 4.056, -6.25]}, {"time": 4.5, "value": -6.26, "curve": [4.944, -6.26, 5.389, 6.4]}, {"time": 5.8333, "value": 6.41, "curve": [6.167, 6.41, 6.5, -6.25]}, {"time": 6.8333, "value": -6.26, "curve": [7.278, -6.26, 7.722, 6.4]}, {"time": 8.1667, "value": 6.41, "curve": [8.611, 6.41, 9.056, -6.25]}, {"time": 9.5, "value": -6.26, "curve": [9.944, -6.26, 10.389, 6.4]}, {"time": 10.8333, "value": 6.41, "curve": [11.278, 6.41, 11.722, -6.25]}, {"time": 12.1667, "value": -6.26, "curve": [12.445, -6.26, 12.724, -1.33]}, {"time": 13, "value": 2.38}]}, "hand_L2": {"rotate": [{"value": 6.45, "curve": [0.114, 7.49, 0.224, 8.27]}, {"time": 0.3333, "value": 8.27, "curve": [0.778, 8.27, 1.222, -3.08]}, {"time": 1.6667, "value": -3.08, "curve": [2.111, -3.09, 2.556, 8.26]}, {"time": 3, "value": 8.27, "curve": [3.444, 8.27, 3.889, -3.08]}, {"time": 4.3333, "value": -3.08, "curve": [4.778, -3.09, 5.222, 8.26]}, {"time": 5.6667, "value": 8.27, "curve": [6, 8.27, 6.333, 13.83]}, {"time": 6.6667, "value": 13.83, "curve": [7.111, 13.83, 7.556, 11.37]}, {"time": 8, "value": 11.37, "curve": [8.444, 11.37, 8.889, 13.83]}, {"time": 9.3333, "value": 13.83, "curve": [9.778, 13.83, 10.222, 8.26]}, {"time": 10.6667, "value": 8.27, "curve": [11.111, 8.27, 11.556, -3.08]}, {"time": 12, "value": -3.08, "curve": [12.335, -3.09, 12.67, 3.27]}, {"time": 13, "value": 6.45}]}, "foot_R": {"rotate": [{"value": 1.03, "curve": [0.444, 1.03, 0.889, -1.33]}, {"time": 1.3333, "value": -1.33, "curve": [1.778, -1.33, 2.222, 1.03]}, {"time": 2.6667, "value": 1.03, "curve": [3.111, 1.03, 3.556, -1.33]}, {"time": 4, "value": -1.33, "curve": [4.444, -1.33, 4.889, 1.03]}, {"time": 5.3333, "value": 1.03, "curve": [5.667, 1.03, 6, -3.02]}, {"time": 6.3333, "value": -3.02, "curve": [6.778, -3.02, 7.222, -2.24]}, {"time": 7.6667, "value": -2.24, "curve": [8.111, -2.24, 8.556, -3.02]}, {"time": 9, "value": -3.02, "curve": [9.444, -3.02, 9.889, 1.03]}, {"time": 10.3333, "value": 1.03, "curve": [10.778, 1.03, 11.222, -1.33]}, {"time": 11.6667, "value": -1.33, "curve": [12.111, -1.33, 12.556, 1.03]}, {"time": 13, "value": 1.03}]}, "foot_R2": {"rotate": [{"value": 1.03, "curve": [0.444, 1.03, 0.889, -1.33]}, {"time": 1.3333, "value": -1.33, "curve": [1.778, -1.33, 2.222, 1.03]}, {"time": 2.6667, "value": 1.03, "curve": [3.111, 1.03, 3.556, -1.33]}, {"time": 4, "value": -1.33, "curve": [4.444, -1.33, 4.889, 1.03]}, {"time": 5.3333, "value": 1.03, "curve": [5.667, 1.03, 6, -3.02]}, {"time": 6.3333, "value": -3.02, "curve": [6.778, -3.02, 7.222, -2.24]}, {"time": 7.6667, "value": -2.24, "curve": [8.111, -2.24, 8.556, -3.02]}, {"time": 9, "value": -3.02, "curve": [9.444, -3.02, 9.889, 1.03]}, {"time": 10.3333, "value": 1.03, "curve": [10.778, 1.03, 11.222, -1.33]}, {"time": 11.6667, "value": -1.33, "curve": [12.111, -1.33, 12.556, 1.03]}, {"time": 13, "value": 1.03}]}, "eyebrow_L2": {"rotate": [{"curve": [1.895, 0, 4.105, 0.03]}, {"time": 6, "curve": [6.111, 0, 6.222, 5.98]}, {"time": 6.3333, "value": 5.98, "curve": [7.442, 5.98, 8.392, 5.97]}, {"time": 9.5, "value": 5.98, "curve": [9.667, 5.98, 9.833, 0]}, {"time": 10}], "scale": [{"curve": [1.895, 1, 4.105, 1, 1.895, 1, 4.105, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 1.03, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 1.03, "curve": [7.442, 1.03, 8.392, 1.03, 7.442, 1, 8.392, 1]}, {"time": 9.5, "x": 1.03, "curve": [9.667, 1.03, 9.833, 1, 10.426, 1, 9.833, 1]}, {"time": 10}]}, "eyebrow_L3": {"rotate": [{"curve": [1.895, 0, 4.105, -0.04]}, {"time": 6, "curve": [6.111, 0, 6.222, -9.07]}, {"time": 6.3333, "value": -9.07, "curve": [7.442, -9.07, 8.392, -9.04]}, {"time": 9.5, "value": -9.07, "curve": [9.667, -9.07, 9.833, 0]}, {"time": 10}]}, "eyebrow_R2": {"rotate": [{"curve": [1.895, 0, 4.105, -0.03]}, {"time": 6, "curve": [6.111, 0, 6.222, -5.97]}, {"time": 6.3333, "value": -5.97, "curve": [7.442, -5.97, 8.392, -5.96]}, {"time": 9.5, "value": -5.97, "curve": [9.667, -5.97, 9.833, 0]}, {"time": 10}], "scale": [{"curve": [1.895, 1, 4.105, 1, 1.895, 1, 4.105, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 1.03, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 1.03, "curve": [7.442, 1.03, 8.392, 1.03, 7.442, 1, 8.392, 1]}, {"time": 9.5, "x": 1.03, "curve": [9.667, 1.03, 9.833, 1, 10.426, 1, 9.833, 1]}, {"time": 10}]}, "eyebrow_R3": {"rotate": [{"curve": [1.895, 0, 4.105, 0.04]}, {"time": 6, "curve": [6.111, 0, 6.222, 10.24]}, {"time": 6.3333, "value": 10.24, "curve": [7.442, 10.24, 8.392, 10.21]}, {"time": 9.5, "value": 10.24, "curve": [9.667, 10.24, 9.833, 0]}, {"time": 10}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -2.73, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -2.73, "curve": "stepped"}, {"time": 3, "x": -2.73, "curve": [3.033, -2.73, 3.067, -1.9, 3.033, 0, 3.067, 1.36]}, {"time": 3.1, "x": -1.9, "y": 1.36, "curve": "stepped"}, {"time": 3.5, "x": -1.9, "y": 1.36, "curve": [3.533, -1.9, 3.567, 0, 3.533, 1.36, 3.567, 0]}, {"time": 3.6, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.9, 6.2, 0, 6.233, -3.77]}, {"time": 6.2667, "x": -1.9, "y": -3.77, "curve": "stepped"}, {"time": 6.8333, "x": -1.9, "y": -3.77, "curve": [6.867, -1.9, 6.9, -3.24, 6.867, -3.77, 6.9, -4.75]}, {"time": 6.9333, "x": -3.24, "y": -4.75, "curve": "stepped"}, {"time": 7.9, "x": -3.24, "y": -4.75, "curve": [7.933, -3.24, 7.967, -2.26, 7.933, -4.75, 7.967, -3.12]}, {"time": 8, "x": -2.26, "y": -3.12, "curve": "stepped"}, {"time": 8.5, "x": -2.26, "y": -3.12, "curve": [8.533, -2.26, 8.567, -1.9, 8.533, -3.12, 8.567, -3.77]}, {"time": 8.6, "x": -1.9, "y": -3.77, "curve": "stepped"}, {"time": 9.8333, "x": -1.9, "y": -3.77, "curve": [9.867, -1.9, 9.9, 0, 9.867, -3.77, 9.9, 0]}, {"time": 9.9333}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -2.73, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -2.73, "curve": "stepped"}, {"time": 3, "x": -2.73, "curve": [3.033, -2.73, 3.067, -1.9, 3.033, 0, 3.067, 1.36]}, {"time": 3.1, "x": -1.9, "y": 1.36, "curve": "stepped"}, {"time": 3.5, "x": -1.9, "y": 1.36, "curve": [3.533, -1.9, 3.567, 0, 3.533, 1.36, 3.567, 0]}, {"time": 3.6, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.9, 6.2, 0, 6.233, -3.77]}, {"time": 6.2667, "x": -1.9, "y": -3.77, "curve": "stepped"}, {"time": 6.8333, "x": -1.9, "y": -3.77, "curve": [6.867, -1.9, 6.9, -3.24, 6.867, -3.77, 6.9, -4.75]}, {"time": 6.9333, "x": -3.24, "y": -4.75, "curve": "stepped"}, {"time": 7.9, "x": -3.24, "y": -4.75, "curve": [7.933, -3.24, 7.967, -2.26, 7.933, -4.75, 7.967, -3.12]}, {"time": 8, "x": -2.26, "y": -3.12, "curve": "stepped"}, {"time": 8.5, "x": -2.26, "y": -3.12, "curve": [8.533, -2.26, 8.567, -1.9, 8.533, -3.12, 8.567, -3.77]}, {"time": 8.6, "x": -1.9, "y": -3.77, "curve": "stepped"}, {"time": 9.8333, "x": -1.9, "y": -3.77, "curve": [9.867, -1.9, 9.9, 0, 9.867, -3.77, 9.9, 0]}, {"time": 9.9333}]}, "RU_L": {"translate": [{"x": -13.66, "curve": [0.168, -32.36, 0.334, -47.7, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -47.7, "curve": [0.944, -47.7, 1.389, 59.31, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 59.33, "curve": [2.278, 59.36, 2.722, -47.67, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -47.7, "curve": [3.611, -47.73, 4.056, 59.31, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 59.33, "curve": [4.944, 59.36, 5.389, -47.66, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -47.7, "curve": [6.167, -47.73, 6.5, 59.31, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 59.33, "curve": [7.278, 59.36, 7.722, -47.67, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -47.7, "curve": [8.611, -47.73, 9.056, 59.31, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 59.33, "curve": [9.944, 59.36, 10.389, -47.67, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -47.7, "curve": [11.278, -47.73, 11.722, 59.31, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 59.33, "curve": [12.445, 59.35, 12.724, 17.73, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -13.66}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.167, 0.986, 0.333, 0.865, 0.167, 0.97, 0.333, 1.042]}, {"time": 0.5, "x": 0.865, "y": 1.042, "curve": [0.722, 0.865, 0.944, 1.082, 0.722, 1.042, 0.944, 0.912]}, {"time": 1.1667, "x": 1.082, "y": 0.912, "curve": [1.389, 1.082, 1.611, 0.865, 1.389, 0.912, 1.611, 1.042]}, {"time": 1.8333, "x": 0.865, "y": 1.042, "curve": [2.056, 0.865, 2.278, 1.082, 2.056, 1.042, 2.278, 0.912]}, {"time": 2.5, "x": 1.082, "y": 0.912, "curve": [2.722, 1.082, 2.944, 0.865, 2.722, 0.912, 2.944, 1.042]}, {"time": 3.1667, "x": 0.865, "y": 1.042, "curve": [3.389, 0.865, 3.611, 1.082, 3.389, 1.042, 3.611, 0.912]}, {"time": 3.8333, "x": 1.082, "y": 0.912, "curve": [4.056, 1.082, 4.278, 0.865, 4.056, 0.912, 4.278, 1.042]}, {"time": 4.5, "x": 0.865, "y": 1.042, "curve": [4.722, 0.865, 4.944, 1.082, 4.722, 1.042, 4.944, 0.912]}, {"time": 5.1667, "x": 1.082, "y": 0.912, "curve": [5.389, 1.082, 5.611, 0.865, 5.389, 0.912, 5.611, 1.042]}, {"time": 5.8333, "x": 0.865, "y": 1.042, "curve": [6, 0.865, 6.167, 1.082, 6, 1.042, 6.167, 0.912]}, {"time": 6.3333, "x": 1.082, "y": 0.912, "curve": [6.5, 1.082, 6.667, 0.865, 6.5, 0.912, 6.667, 1.042]}, {"time": 6.8333, "x": 0.865, "y": 1.042, "curve": [7.056, 0.865, 7.278, 1.082, 7.056, 1.042, 7.278, 0.912]}, {"time": 7.5, "x": 1.082, "y": 0.912, "curve": [7.722, 1.082, 7.944, 0.865, 7.722, 0.912, 7.944, 1.042]}, {"time": 8.1667, "x": 0.865, "y": 1.042, "curve": [8.389, 0.865, 8.611, 1.082, 8.389, 1.042, 8.611, 0.912]}, {"time": 8.8333, "x": 1.082, "y": 0.912, "curve": [9.056, 1.082, 9.278, 0.865, 9.056, 0.912, 9.278, 1.042]}, {"time": 9.5, "x": 0.865, "y": 1.042, "curve": [9.722, 0.865, 9.944, 1.082, 9.722, 1.042, 9.944, 0.912]}, {"time": 10.1667, "x": 1.082, "y": 0.912, "curve": [10.389, 1.082, 10.611, 0.865, 10.389, 0.912, 10.611, 1.042]}, {"time": 10.8333, "x": 0.865, "y": 1.042, "curve": [11.056, 0.865, 11.278, 1.082, 11.056, 1.042, 11.278, 0.912]}, {"time": 11.5, "x": 1.082, "y": 0.912, "curve": [11.722, 1.082, 11.944, 0.865, 11.722, 0.912, 11.944, 1.042]}, {"time": 12.1667, "x": 0.865, "y": 1.042, "curve": [12.389, 0.865, 12.611, 1.082, 12.389, 1.042, 12.611, 0.912]}, {"time": 12.8333, "x": 1.082, "y": 0.912, "curve": [12.889, 1.082, 12.944, 1.067, 12.889, 0.912, 12.944, 0.921]}, {"time": 13, "x": 1.047, "y": 0.933}]}, "RU_L2": {"translate": [{"x": 4.83, "curve": [0.225, -11.89, 0.446, -28.84, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -28.84, "curve": [1.111, -28.84, 1.556, 38.47, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 38.48, "curve": [2.444, 38.5, 2.889, -28.82, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -28.84, "curve": [3.778, -28.85, 4.222, 38.47, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 38.48, "curve": [5.111, 38.5, 5.556, -28.81, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -28.84, "curve": [6.333, -28.85, 6.667, 38.47, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 38.48, "curve": [7.444, 38.5, 7.889, -28.82, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -28.84, "curve": [8.778, -28.85, 9.222, 38.47, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 38.48, "curve": [10.111, 38.5, 10.556, -28.82, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -28.84, "curve": [11.444, -28.85, 11.889, 38.47, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 38.48, "curve": [12.557, 38.49, 12.781, 21.77, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 4.83}], "scale": [{"x": 1.082, "y": 0.912, "curve": [0.222, 1.082, 0.444, 0.865, 0.222, 0.912, 0.444, 1.042]}, {"time": 0.6667, "x": 0.865, "y": 1.042, "curve": [0.889, 0.865, 1.111, 1.082, 0.889, 1.042, 1.111, 0.912]}, {"time": 1.3333, "x": 1.082, "y": 0.912, "curve": [1.556, 1.082, 1.778, 0.865, 1.556, 0.912, 1.778, 1.042]}, {"time": 2, "x": 0.865, "y": 1.042, "curve": [2.222, 0.865, 2.444, 1.082, 2.222, 1.042, 2.444, 0.912]}, {"time": 2.6667, "x": 1.082, "y": 0.912, "curve": [2.889, 1.082, 3.111, 0.865, 2.889, 0.912, 3.111, 1.042]}, {"time": 3.3333, "x": 0.865, "y": 1.042, "curve": [3.556, 0.865, 3.778, 1.082, 3.556, 1.042, 3.778, 0.912]}, {"time": 4, "x": 1.082, "y": 0.912, "curve": [4.222, 1.082, 4.444, 0.865, 4.222, 0.912, 4.444, 1.042]}, {"time": 4.6667, "x": 0.865, "y": 1.042, "curve": [4.889, 0.865, 5.111, 1.082, 4.889, 1.042, 5.111, 0.912]}, {"time": 5.3333, "x": 1.082, "y": 0.912, "curve": [5.556, 1.082, 5.778, 0.865, 5.556, 0.912, 5.778, 1.042]}, {"time": 6, "x": 0.865, "y": 1.042, "curve": [6.167, 0.865, 6.333, 1.082, 6.167, 1.042, 6.333, 0.912]}, {"time": 6.5, "x": 1.082, "y": 0.912, "curve": [6.667, 1.082, 6.833, 0.865, 6.667, 0.912, 6.833, 1.042]}, {"time": 7, "x": 0.865, "y": 1.042, "curve": [7.222, 0.865, 7.444, 1.082, 7.222, 1.042, 7.444, 0.912]}, {"time": 7.6667, "x": 1.082, "y": 0.912, "curve": [7.889, 1.082, 8.111, 0.865, 7.889, 0.912, 8.111, 1.042]}, {"time": 8.3333, "x": 0.865, "y": 1.042, "curve": [8.556, 0.865, 8.778, 1.082, 8.556, 1.042, 8.778, 0.912]}, {"time": 9, "x": 1.082, "y": 0.912, "curve": [9.222, 1.082, 9.444, 0.865, 9.222, 0.912, 9.444, 1.042]}, {"time": 9.6667, "x": 0.865, "y": 1.042, "curve": [9.889, 0.865, 10.111, 1.082, 9.889, 1.042, 10.111, 0.912]}, {"time": 10.3333, "x": 1.082, "y": 0.912, "curve": [10.556, 1.082, 10.778, 0.865, 10.556, 0.912, 10.778, 1.042]}, {"time": 11, "x": 0.865, "y": 1.042, "curve": [11.222, 0.865, 11.444, 1.082, 11.222, 1.042, 11.444, 0.912]}, {"time": 11.6667, "x": 1.082, "y": 0.912, "curve": [11.889, 1.082, 12.111, 0.865, 11.889, 0.912, 12.111, 1.042]}, {"time": 12.3333, "x": 0.865, "y": 1.042, "curve": [12.556, 0.865, 12.778, 1.082, 12.556, 1.042, 12.778, 0.912]}, {"time": 13, "x": 1.082, "y": 0.912}]}, "RU_L3": {"translate": [{"x": 15.02, "curve": [0.279, -3.24, 0.556, -27.53, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -27.53, "curve": [1.278, -27.53, 1.722, 34.83, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 34.85, "curve": [2.611, 34.86, 3.056, -27.52, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -27.53, "curve": [3.944, -27.55, 4.389, 34.83, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 34.85, "curve": [5.278, 34.86, 5.722, -27.51, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -27.53, "curve": [6.5, -27.55, 6.833, 34.84, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 34.85, "curve": [7.611, 34.86, 8.056, -27.52, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -27.53, "curve": [8.944, -27.55, 9.389, 34.83, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 34.85, "curve": [10.278, 34.86, 10.722, -27.52, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -27.53, "curve": [11.611, -27.55, 12.056, 34.83, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 34.85, "curve": [12.667, 34.85, 12.835, 26.05, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 15.02}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.056, 1.067, 0.111, 1.082, 0.056, 0.921, 0.111, 0.912]}, {"time": 0.1667, "x": 1.082, "y": 0.912, "curve": [0.389, 1.082, 0.611, 0.865, 0.389, 0.912, 0.611, 1.042]}, {"time": 0.8333, "x": 0.865, "y": 1.042, "curve": [1.056, 0.865, 1.278, 1.082, 1.056, 1.042, 1.278, 0.912]}, {"time": 1.5, "x": 1.082, "y": 0.912, "curve": [1.722, 1.082, 1.944, 0.865, 1.722, 0.912, 1.944, 1.042]}, {"time": 2.1667, "x": 0.865, "y": 1.042, "curve": [2.389, 0.865, 2.611, 1.082, 2.389, 1.042, 2.611, 0.912]}, {"time": 2.8333, "x": 1.082, "y": 0.912, "curve": [3.056, 1.082, 3.278, 0.865, 3.056, 0.912, 3.278, 1.042]}, {"time": 3.5, "x": 0.865, "y": 1.042, "curve": [3.722, 0.865, 3.944, 1.082, 3.722, 1.042, 3.944, 0.912]}, {"time": 4.1667, "x": 1.082, "y": 0.912, "curve": [4.389, 1.082, 4.611, 0.865, 4.389, 0.912, 4.611, 1.042]}, {"time": 4.8333, "x": 0.865, "y": 1.042, "curve": [5.056, 0.865, 5.278, 1.082, 5.056, 1.042, 5.278, 0.912]}, {"time": 5.5, "x": 1.082, "y": 0.912, "curve": [5.722, 1.082, 5.944, 0.865, 5.722, 0.912, 5.944, 1.042]}, {"time": 6.1667, "x": 0.865, "y": 1.042, "curve": [6.333, 0.865, 6.5, 1.082, 6.333, 1.042, 6.5, 0.912]}, {"time": 6.6667, "x": 1.082, "y": 0.912, "curve": [6.833, 1.082, 7, 0.865, 6.833, 0.912, 7, 1.042]}, {"time": 7.1667, "x": 0.865, "y": 1.042, "curve": [7.389, 0.865, 7.611, 1.082, 7.389, 1.042, 7.611, 0.912]}, {"time": 7.8333, "x": 1.082, "y": 0.912, "curve": [8.056, 1.082, 8.278, 0.865, 8.056, 0.912, 8.278, 1.042]}, {"time": 8.5, "x": 0.865, "y": 1.042, "curve": [8.722, 0.865, 8.944, 1.082, 8.722, 1.042, 8.944, 0.912]}, {"time": 9.1667, "x": 1.082, "y": 0.912, "curve": [9.389, 1.082, 9.611, 0.865, 9.389, 0.912, 9.611, 1.042]}, {"time": 9.8333, "x": 0.865, "y": 1.042, "curve": [10.056, 0.865, 10.278, 1.082, 10.056, 1.042, 10.278, 0.912]}, {"time": 10.5, "x": 1.082, "y": 0.912, "curve": [10.722, 1.082, 10.944, 0.865, 10.722, 0.912, 10.944, 1.042]}, {"time": 11.1667, "x": 0.865, "y": 1.042, "curve": [11.389, 0.865, 11.611, 1.082, 11.389, 1.042, 11.611, 0.912]}, {"time": 11.8333, "x": 1.082, "y": 0.912, "curve": [12.056, 1.082, 12.278, 0.865, 12.056, 0.912, 12.278, 1.042]}, {"time": 12.5, "x": 0.865, "y": 1.042, "curve": [12.667, 0.865, 12.833, 0.986, 12.667, 1.042, 12.833, 0.97]}, {"time": 13, "x": 1.047, "y": 0.933}]}, "RU_R": {"translate": [{"x": -13.66, "curve": [0.168, -32.36, 0.334, -47.7, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -47.7, "curve": [0.944, -47.7, 1.389, 59.31, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 59.33, "curve": [2.278, 59.36, 2.722, -47.67, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -47.7, "curve": [3.611, -47.73, 4.056, 59.31, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 59.33, "curve": [4.944, 59.36, 5.389, -47.66, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -47.7, "curve": [6.167, -47.73, 6.5, 59.31, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 59.33, "curve": [7.278, 59.36, 7.722, -47.67, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -47.7, "curve": [8.611, -47.73, 9.056, 59.31, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 59.33, "curve": [9.944, 59.36, 10.389, -47.67, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -47.7, "curve": [11.278, -47.73, 11.722, 59.31, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 59.33, "curve": [12.445, 59.35, 12.724, 17.73, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -13.66}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.167, 0.986, 0.333, 0.865, 0.167, 0.97, 0.333, 1.042]}, {"time": 0.5, "x": 0.865, "y": 1.042, "curve": [0.722, 0.865, 0.944, 1.082, 0.722, 1.042, 0.944, 0.912]}, {"time": 1.1667, "x": 1.082, "y": 0.912, "curve": [1.389, 1.082, 1.611, 0.865, 1.389, 0.912, 1.611, 1.042]}, {"time": 1.8333, "x": 0.865, "y": 1.042, "curve": [2.056, 0.865, 2.278, 1.082, 2.056, 1.042, 2.278, 0.912]}, {"time": 2.5, "x": 1.082, "y": 0.912, "curve": [2.722, 1.082, 2.944, 0.865, 2.722, 0.912, 2.944, 1.042]}, {"time": 3.1667, "x": 0.865, "y": 1.042, "curve": [3.389, 0.865, 3.611, 1.082, 3.389, 1.042, 3.611, 0.912]}, {"time": 3.8333, "x": 1.082, "y": 0.912, "curve": [4.056, 1.082, 4.278, 0.865, 4.056, 0.912, 4.278, 1.042]}, {"time": 4.5, "x": 0.865, "y": 1.042, "curve": [4.722, 0.865, 4.944, 1.082, 4.722, 1.042, 4.944, 0.912]}, {"time": 5.1667, "x": 1.082, "y": 0.912, "curve": [5.389, 1.082, 5.611, 0.865, 5.389, 0.912, 5.611, 1.042]}, {"time": 5.8333, "x": 0.865, "y": 1.042, "curve": [6, 0.865, 6.167, 1.082, 6, 1.042, 6.167, 0.912]}, {"time": 6.3333, "x": 1.082, "y": 0.912, "curve": [6.5, 1.082, 6.667, 0.865, 6.5, 0.912, 6.667, 1.042]}, {"time": 6.8333, "x": 0.865, "y": 1.042, "curve": [7.056, 0.865, 7.278, 1.082, 7.056, 1.042, 7.278, 0.912]}, {"time": 7.5, "x": 1.082, "y": 0.912, "curve": [7.722, 1.082, 7.944, 0.865, 7.722, 0.912, 7.944, 1.042]}, {"time": 8.1667, "x": 0.865, "y": 1.042, "curve": [8.389, 0.865, 8.611, 1.082, 8.389, 1.042, 8.611, 0.912]}, {"time": 8.8333, "x": 1.082, "y": 0.912, "curve": [9.056, 1.082, 9.278, 0.865, 9.056, 0.912, 9.278, 1.042]}, {"time": 9.5, "x": 0.865, "y": 1.042, "curve": [9.722, 0.865, 9.944, 1.082, 9.722, 1.042, 9.944, 0.912]}, {"time": 10.1667, "x": 1.082, "y": 0.912, "curve": [10.389, 1.082, 10.611, 0.865, 10.389, 0.912, 10.611, 1.042]}, {"time": 10.8333, "x": 0.865, "y": 1.042, "curve": [11.056, 0.865, 11.278, 1.082, 11.056, 1.042, 11.278, 0.912]}, {"time": 11.5, "x": 1.082, "y": 0.912, "curve": [11.722, 1.082, 11.944, 0.865, 11.722, 0.912, 11.944, 1.042]}, {"time": 12.1667, "x": 0.865, "y": 1.042, "curve": [12.389, 0.865, 12.611, 1.082, 12.389, 1.042, 12.611, 0.912]}, {"time": 12.8333, "x": 1.082, "y": 0.912, "curve": [12.889, 1.082, 12.944, 1.067, 12.889, 0.912, 12.944, 0.921]}, {"time": 13, "x": 1.047, "y": 0.933}]}, "RU_R2": {"translate": [{"x": 4.78, "curve": [0.225, -10.24, 0.446, -25.48, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -25.48, "curve": [1.111, -25.48, 1.556, 35.02, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 35.04, "curve": [2.444, 35.05, 2.889, -25.46, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -25.48, "curve": [3.778, -25.49, 4.222, 35.02, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 35.04, "curve": [5.111, 35.05, 5.556, -25.46, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -25.48, "curve": [6.333, -25.49, 6.667, 35.02, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 35.04, "curve": [7.444, 35.05, 7.889, -25.46, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -25.48, "curve": [8.778, -25.49, 9.222, 35.02, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 35.04, "curve": [10.111, 35.05, 10.556, -25.46, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -25.48, "curve": [11.444, -25.49, 11.889, 35.02, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 35.04, "curve": [12.557, 35.04, 12.781, 20.02, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 4.78}], "scale": [{"x": 1.082, "y": 0.912, "curve": [0.222, 1.082, 0.444, 0.865, 0.222, 0.912, 0.444, 1.042]}, {"time": 0.6667, "x": 0.865, "y": 1.042, "curve": [0.889, 0.865, 1.111, 1.082, 0.889, 1.042, 1.111, 0.912]}, {"time": 1.3333, "x": 1.082, "y": 0.912, "curve": [1.556, 1.082, 1.778, 0.865, 1.556, 0.912, 1.778, 1.042]}, {"time": 2, "x": 0.865, "y": 1.042, "curve": [2.222, 0.865, 2.444, 1.082, 2.222, 1.042, 2.444, 0.912]}, {"time": 2.6667, "x": 1.082, "y": 0.912, "curve": [2.889, 1.082, 3.111, 0.865, 2.889, 0.912, 3.111, 1.042]}, {"time": 3.3333, "x": 0.865, "y": 1.042, "curve": [3.556, 0.865, 3.778, 1.082, 3.556, 1.042, 3.778, 0.912]}, {"time": 4, "x": 1.082, "y": 0.912, "curve": [4.222, 1.082, 4.444, 0.865, 4.222, 0.912, 4.444, 1.042]}, {"time": 4.6667, "x": 0.865, "y": 1.042, "curve": [4.889, 0.865, 5.111, 1.082, 4.889, 1.042, 5.111, 0.912]}, {"time": 5.3333, "x": 1.082, "y": 0.912, "curve": [5.556, 1.082, 5.778, 0.865, 5.556, 0.912, 5.778, 1.042]}, {"time": 6, "x": 0.865, "y": 1.042, "curve": [6.167, 0.865, 6.333, 1.082, 6.167, 1.042, 6.333, 0.912]}, {"time": 6.5, "x": 1.082, "y": 0.912, "curve": [6.667, 1.082, 6.833, 0.865, 6.667, 0.912, 6.833, 1.042]}, {"time": 7, "x": 0.865, "y": 1.042, "curve": [7.222, 0.865, 7.444, 1.082, 7.222, 1.042, 7.444, 0.912]}, {"time": 7.6667, "x": 1.082, "y": 0.912, "curve": [7.889, 1.082, 8.111, 0.865, 7.889, 0.912, 8.111, 1.042]}, {"time": 8.3333, "x": 0.865, "y": 1.042, "curve": [8.556, 0.865, 8.778, 1.082, 8.556, 1.042, 8.778, 0.912]}, {"time": 9, "x": 1.082, "y": 0.912, "curve": [9.222, 1.082, 9.444, 0.865, 9.222, 0.912, 9.444, 1.042]}, {"time": 9.6667, "x": 0.865, "y": 1.042, "curve": [9.889, 0.865, 10.111, 1.082, 9.889, 1.042, 10.111, 0.912]}, {"time": 10.3333, "x": 1.082, "y": 0.912, "curve": [10.556, 1.082, 10.778, 0.865, 10.556, 0.912, 10.778, 1.042]}, {"time": 11, "x": 0.865, "y": 1.042, "curve": [11.222, 0.865, 11.444, 1.082, 11.222, 1.042, 11.444, 0.912]}, {"time": 11.6667, "x": 1.082, "y": 0.912, "curve": [11.889, 1.082, 12.111, 0.865, 11.889, 0.912, 12.111, 1.042]}, {"time": 12.3333, "x": 0.865, "y": 1.042, "curve": [12.556, 0.865, 12.778, 1.082, 12.556, 1.042, 12.778, 0.912]}, {"time": 13, "x": 1.082, "y": 0.912}]}, "RU_R3": {"translate": [{"x": 13.79, "curve": [0.279, -2.43, 0.556, -24.01, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -24.01, "curve": [1.278, -24.01, 1.722, 31.39, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 31.4, "curve": [2.611, 31.41, 3.056, -23.99, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -24.01, "curve": [3.944, -24.02, 4.389, 31.39, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 31.4, "curve": [5.278, 31.41, 5.722, -23.99, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -24.01, "curve": [6.5, -24.02, 6.833, 31.39, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 31.4, "curve": [7.611, 31.41, 8.056, -23.99, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -24.01, "curve": [8.944, -24.02, 9.389, 31.39, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 31.4, "curve": [10.278, 31.41, 10.722, -23.99, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -24.01, "curve": [11.611, -24.02, 12.056, 31.39, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 31.4, "curve": [12.667, 31.41, 12.835, 23.59, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.79}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.056, 1.067, 0.111, 1.082, 0.056, 0.921, 0.111, 0.912]}, {"time": 0.1667, "x": 1.082, "y": 0.912, "curve": [0.389, 1.082, 0.611, 0.865, 0.389, 0.912, 0.611, 1.042]}, {"time": 0.8333, "x": 0.865, "y": 1.042, "curve": [1.056, 0.865, 1.278, 1.082, 1.056, 1.042, 1.278, 0.912]}, {"time": 1.5, "x": 1.082, "y": 0.912, "curve": [1.722, 1.082, 1.944, 0.865, 1.722, 0.912, 1.944, 1.042]}, {"time": 2.1667, "x": 0.865, "y": 1.042, "curve": [2.389, 0.865, 2.611, 1.082, 2.389, 1.042, 2.611, 0.912]}, {"time": 2.8333, "x": 1.082, "y": 0.912, "curve": [3.056, 1.082, 3.278, 0.865, 3.056, 0.912, 3.278, 1.042]}, {"time": 3.5, "x": 0.865, "y": 1.042, "curve": [3.722, 0.865, 3.944, 1.082, 3.722, 1.042, 3.944, 0.912]}, {"time": 4.1667, "x": 1.082, "y": 0.912, "curve": [4.389, 1.082, 4.611, 0.865, 4.389, 0.912, 4.611, 1.042]}, {"time": 4.8333, "x": 0.865, "y": 1.042, "curve": [5.056, 0.865, 5.278, 1.082, 5.056, 1.042, 5.278, 0.912]}, {"time": 5.5, "x": 1.082, "y": 0.912, "curve": [5.722, 1.082, 5.944, 0.865, 5.722, 0.912, 5.944, 1.042]}, {"time": 6.1667, "x": 0.865, "y": 1.042, "curve": [6.333, 0.865, 6.5, 1.082, 6.333, 1.042, 6.5, 0.912]}, {"time": 6.6667, "x": 1.082, "y": 0.912, "curve": [6.833, 1.082, 7, 0.865, 6.833, 0.912, 7, 1.042]}, {"time": 7.1667, "x": 0.865, "y": 1.042, "curve": [7.389, 0.865, 7.611, 1.082, 7.389, 1.042, 7.611, 0.912]}, {"time": 7.8333, "x": 1.082, "y": 0.912, "curve": [8.056, 1.082, 8.278, 0.865, 8.056, 0.912, 8.278, 1.042]}, {"time": 8.5, "x": 0.865, "y": 1.042, "curve": [8.722, 0.865, 8.944, 1.082, 8.722, 1.042, 8.944, 0.912]}, {"time": 9.1667, "x": 1.082, "y": 0.912, "curve": [9.389, 1.082, 9.611, 0.865, 9.389, 0.912, 9.611, 1.042]}, {"time": 9.8333, "x": 0.865, "y": 1.042, "curve": [10.056, 0.865, 10.278, 1.082, 10.056, 1.042, 10.278, 0.912]}, {"time": 10.5, "x": 1.082, "y": 0.912, "curve": [10.722, 1.082, 10.944, 0.865, 10.722, 0.912, 10.944, 1.042]}, {"time": 11.1667, "x": 0.865, "y": 1.042, "curve": [11.389, 0.865, 11.611, 1.082, 11.389, 1.042, 11.611, 0.912]}, {"time": 11.8333, "x": 1.082, "y": 0.912, "curve": [12.056, 1.082, 12.278, 0.865, 12.056, 0.912, 12.278, 1.042]}, {"time": 12.5, "x": 0.865, "y": 1.042, "curve": [12.667, 0.865, 12.833, 0.986, 12.667, 1.042, 12.833, 0.97]}, {"time": 13, "x": 1.047, "y": 0.933}]}, "bone61": {"rotate": [{"value": 0.07, "curve": "stepped"}, {"time": 5.3333, "value": 0.07, "curve": [5.667, 0.07, 6, 0.07]}, {"time": 6.3333, "value": 0.07, "curve": [6.778, 0.07, 7.222, -10.16]}, {"time": 7.6667, "value": -10.16, "curve": [8.111, -10.16, 8.556, 0.07]}, {"time": 9, "value": 0.07}]}, "bone62": {"rotate": [{"value": 0.35, "curve": "stepped"}, {"time": 5.3333, "value": 0.35, "curve": [5.667, 0.35, 6, 0.35]}, {"time": 6.3333, "value": 0.35, "curve": [6.778, 0.35, 7.222, 14.67]}, {"time": 7.6667, "value": 14.67, "curve": [8.111, 14.67, 8.556, 0.35]}, {"time": 9, "value": 0.35}]}, "bone63": {"rotate": [{"value": -0.06, "curve": "stepped"}, {"time": 5.3333, "value": -0.06, "curve": [5.667, -0.06, 6, -0.06]}, {"time": 6.3333, "value": -0.06, "curve": [6.778, -0.06, 7.222, 11.25]}, {"time": 7.6667, "value": 11.25, "curve": [8.111, 11.25, 8.556, -0.06]}, {"time": 9, "value": -0.06}]}, "bone64": {"rotate": [{"value": -0.04, "curve": "stepped"}, {"time": 5.3333, "value": -0.04, "curve": [5.667, -0.04, 6, -0.04]}, {"time": 6.3333, "value": -0.04, "curve": [6.778, -0.04, 7.222, 12.44]}, {"time": 7.6667, "value": 12.45, "curve": [8.111, 12.45, 8.556, -0.04]}, {"time": 9, "value": -0.04}]}, "leg_R1": {"translate": [{"y": -12.25, "curve": [0.444, 0, 0.889, -5.12, 0.444, -12.25, 0.889, 0]}, {"time": 1.3333, "x": -5.12, "curve": [1.778, -5.13, 2.222, 0, 1.778, 0, 2.222, -12.25]}, {"time": 2.6667, "y": -12.25, "curve": [3.111, 0, 3.556, -5.12, 3.111, -12.26, 3.556, 0]}, {"time": 4, "x": -5.12, "curve": [4.444, -5.13, 4.889, 0, 4.444, 0, 4.889, -12.25]}, {"time": 5.3333, "y": -12.25, "curve": [5.667, 0, 6, -5.12, 5.667, -12.26, 6, 0]}, {"time": 6.3333, "x": -5.12, "curve": [6.778, -5.13, 7.222, -3.2, 6.778, 0, 7.222, -4.59]}, {"time": 7.6667, "x": -3.2, "y": -4.59, "curve": [8.111, -3.2, 8.556, -5.12, 8.111, -4.6, 8.556, 0]}, {"time": 9, "x": -5.12, "curve": [9.444, -5.13, 9.889, 0, 9.444, 0, 9.889, -12.25]}, {"time": 10.3333, "y": -12.25, "curve": [10.778, 0, 11.222, -5.12, 10.778, -12.26, 11.222, 0]}, {"time": 11.6667, "x": -5.12, "curve": [12.111, -5.13, 12.556, 0, 12.111, 0, 12.556, -12.25]}, {"time": 13, "y": -12.25}]}, "cloth": {"rotate": [{"value": -5.76, "curve": [0.336, -2.95, 0.668, 2.59]}, {"time": 1, "value": 2.59, "curve": [1.444, 2.59, 1.889, -7.35]}, {"time": 2.3333, "value": -7.35, "curve": [2.778, -7.36, 3.222, 2.59]}, {"time": 3.6667, "value": 2.59, "curve": [4.111, 2.59, 4.556, -7.35]}, {"time": 5, "value": -7.35, "curve": [5.444, -7.36, 5.889, 2.59]}, {"time": 6.3333, "value": 2.59, "curve": [6.667, 2.59, 7, -7.35]}, {"time": 7.3333, "value": -7.35, "curve": [7.778, -7.36, 8.222, 2.59]}, {"time": 8.6667, "value": 2.59, "curve": [9.111, 2.59, 9.556, -7.35]}, {"time": 10, "value": -7.35, "curve": [10.444, -7.36, 10.889, 2.59]}, {"time": 11.3333, "value": 2.59, "curve": [11.778, 2.59, 12.222, -7.35]}, {"time": 12.6667, "value": -7.35, "curve": [12.779, -7.35, 12.892, -6.72]}, {"time": 13, "value": -5.76}]}, "hair_Bb": {"rotate": [{"value": 0.07, "curve": [0.225, 3.22, 0.446, 6.41]}, {"time": 0.6667, "value": 6.41, "curve": [1.111, 6.41, 1.556, -6.25]}, {"time": 2, "value": -6.26, "curve": [2.444, -6.26, 2.889, 6.4]}, {"time": 3.3333, "value": 6.41, "curve": [3.778, 6.41, 4.222, -6.25]}, {"time": 4.6667, "value": -6.26, "curve": [5.111, -6.26, 5.556, 6.4]}, {"time": 6, "value": 6.41, "curve": [6.333, 6.41, 6.667, -6.25]}, {"time": 7, "value": -6.26, "curve": [7.444, -6.26, 7.889, 6.4]}, {"time": 8.3333, "value": 6.41, "curve": [8.778, 6.41, 9.222, -6.25]}, {"time": 9.6667, "value": -6.26, "curve": [10.111, -6.26, 10.556, 6.41]}, {"time": 11, "value": 6.41, "curve": [11.444, 6.41, 11.889, -6.25]}, {"time": 12.3333, "value": -6.26, "curve": [12.557, -6.26, 12.781, -3.11]}, {"time": 13, "value": 0.07}]}, "hair": {"translate": [{"y": -0.54, "curve": [0.225, 0, 0.446, 0, 0.225, -4.27, 0.446, -8.05]}, {"time": 0.6667, "y": -8.05, "curve": [1.111, 0, 1.556, 0, 1.111, -8.05, 1.556, 6.97]}, {"time": 2, "y": 6.97, "curve": [2.444, 0, 2.889, 0, 2.444, 6.97, 2.889, -8.05]}, {"time": 3.3333, "y": -8.05, "curve": [3.778, 0, 4.222, 0, 3.778, -8.06, 4.222, 6.97]}, {"time": 4.6667, "y": 6.97, "curve": [5.111, 0, 5.556, 0, 5.111, 6.97, 5.556, -8.05]}, {"time": 6, "y": -8.05, "curve": [6.333, 0, 6.667, 0, 6.333, -8.06, 6.667, 12.79]}, {"time": 7, "y": 12.79, "curve": [7.444, 0, 7.889, 0, 7.444, 12.79, 7.889, 4.97]}, {"time": 8.3333, "y": 4.97, "curve": [8.778, 0, 9.222, 0, 8.778, 4.97, 9.222, 12.78]}, {"time": 9.6667, "y": 12.79, "curve": [10.111, 0, 10.556, 0, 10.111, 12.79, 10.556, -8.05]}, {"time": 11, "y": -8.05, "curve": [11.444, 0, 11.889, 0, 11.444, -8.06, 11.889, 6.97]}, {"time": 12.3333, "y": 6.97, "curve": [12.557, 0, 12.781, 0, 12.557, 6.97, 12.781, 3.24]}, {"time": 13, "y": -0.54}]}, "headround3": {"translate": [{"x": 40.63, "curve": [0.225, 119.91, 0.446, 200.27, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": 200.27, "curve": [1.111, 200.27, 1.556, -118.87, 1.111, 0, 1.556, 0]}, {"time": 2, "x": -118.95, "curve": [2.444, -119.03, 2.889, 200.19, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": 200.27, "curve": [3.778, 200.35, 4.222, -118.87, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": -118.95, "curve": [5.111, -119.03, 5.556, 200.16, 5.111, 0, 5.556, 0]}, {"time": 6, "x": 200.27, "curve": [6.333, 200.35, 6.667, -118.93, 6.333, 0, 6.667, 0]}, {"time": 7, "x": -118.95, "curve": [7.444, -118.98, 7.889, 0.73, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": 0.76, "curve": [8.778, 0.79, 9.222, -118.87, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": -118.95, "curve": [10.111, -119.03, 10.556, 200.19, 10.111, 0, 10.556, 0]}, {"time": 11, "x": 200.27, "curve": [11.444, 200.35, 11.889, -118.87, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": -118.95, "curve": [12.557, -118.99, 12.781, -39.71, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 40.63}]}, "headround": {"translate": [{"y": -76.47, "curve": [0.279, 0, 0.556, 0, 0.279, 35.56, 0.556, 184.58]}, {"time": 0.8333, "y": 184.58, "curve": [1.278, 0, 1.722, 0, 1.278, 184.58, 1.722, -198.03]}, {"time": 2.1667, "y": -198.13, "curve": [2.611, 0, 3.056, 0, 2.611, -198.22, 3.056, 184.48]}, {"time": 3.5, "y": 184.58, "curve": [3.944, 0, 4.389, 0, 3.944, 184.68, 4.389, -198.03]}, {"time": 4.8333, "y": -198.13, "curve": [5.278, 0, 5.722, 0, 5.278, -198.22, 5.722, 184.44]}, {"time": 6.1667, "y": 184.58, "curve": [6.5, 0, 6.833, 0, 6.5, 184.68, 6.833, -217.4]}, {"time": 7.1667, "y": -217.43, "curve": [7.611, 0, 8.056, 0, 7.611, -217.47, 8.056, -66.72]}, {"time": 8.5, "y": -66.68, "curve": [8.944, 0, 9.389, 0, 8.944, -66.64, 9.389, -217.33]}, {"time": 9.8333, "y": -217.43, "curve": [10.278, 0, 10.722, 0, 10.278, -217.53, 10.722, 184.48]}, {"time": 11.1667, "y": 184.58, "curve": [11.611, 0, 12.056, 0, 11.611, 184.68, 12.056, -198.03]}, {"time": 12.5, "y": -198.13, "curve": [12.667, 0, 12.835, 0, 12.667, -198.16, 12.835, -144.16]}, {"time": 13, "y": -76.47}]}, "bodyround": {"translate": [{"y": 77.78, "curve": [0.168, 0, 0.334, 0, 0.168, 158.04, 0.334, 223.9]}, {"time": 0.5, "y": 223.9, "curve": [0.944, 0, 1.389, 0, 0.944, 223.9, 1.389, -235.37]}, {"time": 1.8333, "y": -235.49, "curve": [2.278, 0, 2.722, 0, 2.278, -235.6, 2.722, 223.78]}, {"time": 3.1667, "y": 223.9, "curve": [3.611, 0, 4.056, 0, 3.611, 224.01, 4.056, -235.37]}, {"time": 4.5, "y": -235.49, "curve": [4.944, 0, 5.389, 0, 4.944, -235.6, 5.389, 223.7]}, {"time": 5.8333, "y": 223.9, "curve": [6.167, 0, 6.5, 0, 6.167, 224.04, 6.5, -354.53]}, {"time": 6.8333, "y": -354.57, "curve": [7.278, 0, 7.722, 0, 7.278, -354.62, 7.722, -137.7]}, {"time": 8.1667, "y": -137.64, "curve": [8.611, 0, 9.056, 0, 8.611, -137.59, 9.056, -354.42]}, {"time": 9.5, "y": -354.57, "curve": [9.944, 0, 10.389, 0, 9.944, -354.71, 10.389, 223.78]}, {"time": 10.8333, "y": 223.9, "curve": [11.278, 0, 11.722, 0, 11.278, 224.01, 11.722, -235.37]}, {"time": 12.1667, "y": -235.49, "curve": [12.445, 0, 12.724, 0, 12.445, -235.56, 12.724, -56.94]}, {"time": 13, "y": 77.78}]}, "tunround": {"translate": [{"x": -223.26, "curve": [0.057, -236.48, 0.112, -246.55, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -246.55, "curve": [0.611, -246.55, 1.056, 248.73, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 248.86, "curve": [1.944, 248.98, 2.389, -246.43, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -246.55, "curve": [3.278, -246.68, 3.722, 248.73, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 248.86, "curve": [4.611, 248.98, 5.056, -246.34, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -246.55, "curve": [5.833, -246.71, 6.167, 380.32, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 380.36, "curve": [6.944, 380.42, 7.389, 145.33, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 145.27, "curve": [8.278, 145.21, 8.722, 380.2, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 380.36, "curve": [9.611, 380.52, 10.056, -246.43, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -246.55, "curve": [10.944, -246.68, 11.389, 248.73, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 248.86, "curve": [12.223, 248.97, 12.613, -129.32, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -223.26}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-12.11169, -0.76498, -12.11194, -0.76421, -16.22009, -1.95209, -16.22034, -1.951, -16.16882, -2.09177, -16.16956, -2.09091, -10.67114, -0.82898, -10.67297, -0.82767, -6.4895, 2.13168, -6.49048, 2.13293, -2.99451, -0.1188, -2.99475, -0.11853, -1.79407, -0.19897, -1.79456, -0.19838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.08167, -0.40167, -4.08179, -0.40076, 0, 0, 0, 0, -6.70984, -0.03299, -6.70984, -0.03262, -12.51416, -0.16248, -12.51416, -0.16154, -16.31653, -0.41125, -16.31653, -0.40988, -16.3678, -0.10095, -16.3678, -0.10007, -11.97034, -1.02518, -11.97034, -1.02455, -4.5592, -0.88617, -4.5592, -0.88589, -1.81836, -0.29401, -1.81836, -0.29382], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-12.11169, -0.76498, -12.11194, -0.76421, -16.22009, -1.95209, -16.22034, -1.951, -16.16882, -2.09177, -16.16956, -2.09091, -10.67114, -0.82898, -10.67297, -0.82767, -6.4895, 2.13168, -6.49048, 2.13293, -2.99451, -0.1188, -2.99475, -0.11853, -1.79407, -0.19897, -1.79456, -0.19838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.08167, -0.40167, -4.08179, -0.40076, 0, 0, 0, 0, -6.70984, -0.03299, -6.70984, -0.03262, -12.51416, -0.16248, -12.51416, -0.16154, -16.31653, -0.41125, -16.31653, -0.40988, -16.3678, -0.10095, -16.3678, -0.10007, -11.97034, -1.02518, -11.97034, -1.02455, -4.5592, -0.88617, -4.5592, -0.88589, -1.81836, -0.29401, -1.81836, -0.29382], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-12.11169, -0.76498, -12.11194, -0.76421, -16.22009, -1.95209, -16.22034, -1.951, -16.16882, -2.09177, -16.16956, -2.09091, -10.67114, -0.82898, -10.67297, -0.82767, -6.4895, 2.13168, -6.49048, 2.13293, -2.99451, -0.1188, -2.99475, -0.11853, -1.79407, -0.19897, -1.79456, -0.19838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.08167, -0.40167, -4.08179, -0.40076, 0, 0, 0, 0, -6.70984, -0.03299, -6.70984, -0.03262, -12.51416, -0.16248, -12.51416, -0.16154, -16.31653, -0.41125, -16.31653, -0.40988, -16.3678, -0.10095, -16.3678, -0.10007, -11.97034, -1.02518, -11.97034, -1.02455, -4.5592, -0.88617, -4.5592, -0.88589, -1.81836, -0.29401, -1.81836, -0.29382], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-16.55481, 1.12079, -16.55591, 1.12175, -14.4408, 0.51324, -14.44165, 0.51396, -9.70325, 0.56732, -9.70447, 0.56776, -3.5802, 0.13303, -3.58032, 0.13342, -0.49756, -0.08063, -0.49805, -0.08028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.7334, -0.28046, -1.7334, -0.2802, -3.81409, -0.61697, -3.81409, -0.61647, -5.85608, -0.47226, -5.85608, -0.47185, -7.31763, -0.23383, -7.31824, -0.23323, -9.54614, 1.06732, -9.54639, 1.0679, -13.35815, 1.16309, -13.35876, 1.16397, -6.54687, 0.29068, -6.54687, 0.291, -12.901, 1.72079, -12.90125, 1.72139, -16.87256, 1.78735, -16.87305, 1.78801, -16.89331, 0.87537, -16.89368, 0.87601, -12.03748, 0.87482, -12.03748, 0.87527, -6.12939, -0.0098, -6.12939, -0.00911, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80835, 0.11453, -0.80835, 0.11476, -2.13293, 0.24567, -2.13293, 0.24606], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-16.55481, 1.12079, -16.55591, 1.12175, -14.4408, 0.51324, -14.44165, 0.51396, -9.70325, 0.56732, -9.70447, 0.56776, -3.5802, 0.13303, -3.58032, 0.13342, -0.49756, -0.08063, -0.49805, -0.08028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.7334, -0.28046, -1.7334, -0.2802, -3.81409, -0.61697, -3.81409, -0.61647, -5.85608, -0.47226, -5.85608, -0.47185, -7.31763, -0.23383, -7.31824, -0.23323, -9.54614, 1.06732, -9.54639, 1.0679, -13.35815, 1.16309, -13.35876, 1.16397, -6.54687, 0.29068, -6.54687, 0.291, -12.901, 1.72079, -12.90125, 1.72139, -16.87256, 1.78735, -16.87305, 1.78801, -16.89331, 0.87537, -16.89368, 0.87601, -12.03748, 0.87482, -12.03748, 0.87527, -6.12939, -0.0098, -6.12939, -0.00911, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80835, 0.11453, -0.80835, 0.11476, -2.13293, 0.24567, -2.13293, 0.24606], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-16.55481, 1.12079, -16.55591, 1.12175, -14.4408, 0.51324, -14.44165, 0.51396, -9.70325, 0.56732, -9.70447, 0.56776, -3.5802, 0.13303, -3.58032, 0.13342, -0.49756, -0.08063, -0.49805, -0.08028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.7334, -0.28046, -1.7334, -0.2802, -3.81409, -0.61697, -3.81409, -0.61647, -5.85608, -0.47226, -5.85608, -0.47185, -7.31763, -0.23383, -7.31824, -0.23323, -9.54614, 1.06732, -9.54639, 1.0679, -13.35815, 1.16309, -13.35876, 1.16397, -6.54687, 0.29068, -6.54687, 0.291, -12.901, 1.72079, -12.90125, 1.72139, -16.87256, 1.78735, -16.87305, 1.78801, -16.89331, 0.87537, -16.89368, 0.87601, -12.03748, 0.87482, -12.03748, 0.87527, -6.12939, -0.0098, -6.12939, -0.00911, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80835, 0.11453, -0.80835, 0.11476, -2.13293, 0.24567, -2.13293, 0.24606], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 738, "vertices": [-4.1731, 0.12909, -4.17419, 0.12946, -10.72253, -0.79602, -10.72388, -0.79527, -14.9425, -1.21039, -14.94434, -1.20966, -16.1864, -1.81348, -16.18738, -1.81303, -13.00952, -1.56784, -13.01099, -1.56732, -7.59314, -1.09396, -7.59351, -1.09343, -1.65405, -0.53552, -1.65503, -0.53532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.06482, -0.0383, -1.06555, -0.03816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.69092, 0.84933, -4.69238, 0.84973, -10.0155, 1.46231, -10.01709, 1.46289, -15.68958, 1.75082, -15.69104, 1.75146, -17.19238, 1.10568, -17.19482, 1.10661, -13.75964, 0.58887, -13.7616, 0.58958, -8.5625, 0.75922, -8.56348, 0.75975, -2.50085, -0.27045, -2.50183, -0.27023, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45667, -0.10162, -1.45728, -0.10147, -3.12854, 0.29797, -3.12952, 0.29823], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 738, "vertices": [-4.1731, 0.12909, -4.17419, 0.12946, -10.72253, -0.79602, -10.72388, -0.79527, -14.9425, -1.21039, -14.94434, -1.20966, -16.1864, -1.81348, -16.18738, -1.81303, -13.00952, -1.56784, -13.01099, -1.56732, -7.59314, -1.09396, -7.59351, -1.09343, -1.65405, -0.53552, -1.65503, -0.53532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.06482, -0.0383, -1.06555, -0.03816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.69092, 0.84933, -4.69238, 0.84973, -10.0155, 1.46231, -10.01709, 1.46289, -15.68958, 1.75082, -15.69104, 1.75146, -17.19238, 1.10568, -17.19482, 1.10661, -13.75964, 0.58887, -13.7616, 0.58958, -8.5625, 0.75922, -8.56348, 0.75975, -2.50085, -0.27045, -2.50183, -0.27023, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45667, -0.10162, -1.45728, -0.10147, -3.12854, 0.29797, -3.12952, 0.29823], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "offset": 738, "vertices": [-4.1731, 0.12909, -4.17419, 0.12946, -10.72253, -0.79602, -10.72388, -0.79527, -14.9425, -1.21039, -14.94434, -1.20966, -16.1864, -1.81348, -16.18738, -1.81303, -13.00952, -1.56784, -13.01099, -1.56732, -7.59314, -1.09396, -7.59351, -1.09343, -1.65405, -0.53552, -1.65503, -0.53532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.06482, -0.0383, -1.06555, -0.03816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.69092, 0.84933, -4.69238, 0.84973, -10.0155, 1.46231, -10.01709, 1.46289, -15.68958, 1.75082, -15.69104, 1.75146, -17.19238, 1.10568, -17.19482, 1.10661, -13.75964, 0.58887, -13.7616, 0.58958, -8.5625, 0.75922, -8.56348, 0.75975, -2.50085, -0.27045, -2.50183, -0.27023, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45667, -0.10162, -1.45728, -0.10147, -3.12854, 0.29797, -3.12952, 0.29823], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 60, "vertices": [0.34082, 0.11432, 0.33643, 0.11346, -0.7926, -0.09009, -0.80054, -0.09174, -1.45642, -0.16556, -1.46362, -0.16666, -1.21472, -0.13806, -1.22095, -0.1395, -1.36499, -0.15514, -1.37109, -0.15683, -0.82129, -0.09334, -0.8291, -0.09503, -0.54431, -0.06187, -0.55005, -0.0632, 0.35767, -0.03505, 0.35303, -0.03595, -0.96362, -0.10953, -0.97266, -0.11108, 1.44934, 0.69415, 1.44263, 0.69299, 1.44934, 0.69415, 1.44263, 0.69299, 1.44934, 0.69415, 1.44263, 0.69299, 1.61316, 0.25896, 1.60767, 0.25809, 1.56482, 0.17787, 1.54761, 0.17554, 1.6604, 0.18872, 1.64551, 0.18692, 1.51831, 0.17259, 1.50781, 0.17099, 1.56836, 0.17828, 1.55762, 0.17673, 1.84912, 0.21021, 1.83618, 0.20825, 1.80249, 0.20488, 1.78711, 0.20276, 1.63, 0.10959, 1.62427, 0.10867, 2.06299, -0.37067, 2.05347, -0.3721, 0.79065, -0.5153, 0.78223, -0.51672, 1.42664, -0.44301, 1.4187, -0.44424, 1.42664, -0.44301, 1.4187, -0.44424, 1.42664, -0.44301, 1.4187, -0.44424, 1.44934, 0.69415, 1.44263, 0.69299, 0.97681, 0.18661, 0.9729, 0.18594, 0.56067, 0.06375, 0.55225, 0.06189, -0.06677, -0.00757, -0.07324, -0.00928, 0.078, 0.00887, 0.06519, 0.0072, 0.01953, 0.00226, 0.00684, -3e-05, 0.07617, 0.10472, 0.06104, 0.10287, 0.25781, 0.12538, 0.24097, 0.12326, 0.99365, 0.03725, 0.9895, 0.03653, 1.42664, -0.44301, 1.4187, -0.44424, 1.17773, 0.13387, 1.17334, 0.13333, 0.95447, 0.10851, 0.95215, 0.10822, 1.17773, 0.13387, 1.17334, 0.13333, 0.95447, 0.10851, 0.95215, 0.10822, 0.53052, -0.07025, 0.52905, -0.07043, 0.53052, -0.07025, 0.52905, -0.07043], "curve": "stepped"}, {"time": 9.6667, "offset": 60, "vertices": [0.34082, 0.11432, 0.33643, 0.11346, -0.7926, -0.09009, -0.80054, -0.09174, -1.45642, -0.16556, -1.46362, -0.16666, -1.21472, -0.13806, -1.22095, -0.1395, -1.36499, -0.15514, -1.37109, -0.15683, -0.82129, -0.09334, -0.8291, -0.09503, -0.54431, -0.06187, -0.55005, -0.0632, 0.35767, -0.03505, 0.35303, -0.03595, -0.96362, -0.10953, -0.97266, -0.11108, 1.44934, 0.69415, 1.44263, 0.69299, 1.44934, 0.69415, 1.44263, 0.69299, 1.44934, 0.69415, 1.44263, 0.69299, 1.61316, 0.25896, 1.60767, 0.25809, 1.56482, 0.17787, 1.54761, 0.17554, 1.6604, 0.18872, 1.64551, 0.18692, 1.51831, 0.17259, 1.50781, 0.17099, 1.56836, 0.17828, 1.55762, 0.17673, 1.84912, 0.21021, 1.83618, 0.20825, 1.80249, 0.20488, 1.78711, 0.20276, 1.63, 0.10959, 1.62427, 0.10867, 2.06299, -0.37067, 2.05347, -0.3721, 0.79065, -0.5153, 0.78223, -0.51672, 1.42664, -0.44301, 1.4187, -0.44424, 1.42664, -0.44301, 1.4187, -0.44424, 1.42664, -0.44301, 1.4187, -0.44424, 1.44934, 0.69415, 1.44263, 0.69299, 0.97681, 0.18661, 0.9729, 0.18594, 0.56067, 0.06375, 0.55225, 0.06189, -0.06677, -0.00757, -0.07324, -0.00928, 0.078, 0.00887, 0.06519, 0.0072, 0.01953, 0.00226, 0.00684, -3e-05, 0.07617, 0.10472, 0.06104, 0.10287, 0.25781, 0.12538, 0.24097, 0.12326, 0.99365, 0.03725, 0.9895, 0.03653, 1.42664, -0.44301, 1.4187, -0.44424, 1.17773, 0.13387, 1.17334, 0.13333, 0.95447, 0.10851, 0.95215, 0.10822, 1.17773, 0.13387, 1.17334, 0.13333, 0.95447, 0.10851, 0.95215, 0.10822, 0.53052, -0.07025, 0.52905, -0.07043, 0.53052, -0.07025, 0.52905, -0.07043], "curve": [9.833, 0, 10, 1]}, {"time": 10.1667}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 10.57, "curve": [0.022, 10.29, 0.056, -70.19, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -70.19, "curve": [0.544, -70.19, 0.856, 10.57, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 10.57}]}, "ALL2": {"translate": [{"y": -14.87, "curve": [0.078, 0, 0.156, 0, 0.022, -14.71, 0.056, 29.62]}, {"time": 0.2333, "y": 29.62, "curve": [0.544, 0, 0.856, 0, 0.544, 29.62, 0.856, -14.87]}, {"time": 1.1667, "y": -14.87}]}, "body": {"rotate": [{"value": 0.81, "curve": [0.022, 0.73, 0.056, -20.72]}, {"time": 0.2333, "value": -20.72, "curve": [0.544, -20.72, 0.856, 0.81]}, {"time": 1.1667, "value": 0.81}], "translate": [{"y": -8.85, "curve": [0.078, 0, 0.156, 0, 0.022, -8.75, 0.056, 18.56]}, {"time": 0.2333, "y": 18.56, "curve": [0.544, 0, 0.856, 0, 0.544, 18.56, 0.856, -8.85]}, {"time": 1.1667, "y": -8.85}], "scale": [{"y": 1.036, "curve": [0.078, 1, 0.156, 1, 0.022, 1.035, 0.056, 0.947]}, {"time": 0.2333, "y": 0.947, "curve": [0.544, 1, 0.856, 1, 0.544, 0.947, 0.856, 1.036]}, {"time": 1.1667, "y": 1.036}]}, "body2": {"rotate": [{"value": -2.3, "curve": [0.026, -2.26, 0.064, 7.71]}, {"time": 0.2667, "value": 7.71, "curve": [0.567, 7.71, 0.867, -2.3]}, {"time": 1.1667, "value": -2.3}], "translate": [{"x": -6.15, "curve": [0.026, -6.05, 0.064, 18.45, 0.026, -0.01, 0.064, -3.58]}, {"time": 0.2667, "x": 18.45, "y": -3.58, "curve": [0.567, 18.45, 0.867, -6.15, 0.567, -3.58, 0.867, 0]}, {"time": 1.1667, "x": -6.15}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.026, 1.004, 0.064, 1.042, 0.026, 1.004, 0.064, 1.042]}, {"time": 0.2667, "x": 1.042, "y": 1.042, "curve": [0.567, 1.042, 0.867, 1.004, 0.567, 1.042, 0.867, 1.004]}, {"time": 1.1667, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.44, "curve": [0.029, -1.38, 0.073, 10.72]}, {"time": 0.3, "value": 10.72, "curve": [0.589, 10.72, 0.878, -1.44]}, {"time": 1.1667, "value": -1.44}]}, "head": {"rotate": [{"value": -0.57, "curve": [0.029, -0.53, 0.073, 9.33]}, {"time": 0.3, "value": 9.33, "curve": [0.589, 9.33, 0.878, -0.57]}, {"time": 1.1667, "value": -0.57}]}, "sh_L": {"translate": [{"x": -0.62, "curve": [0.026, -0.58, 0.064, 11.49, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 11.49, "curve": [0.567, 11.49, 0.867, -0.62, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -0.62}]}, "sh_R": {"translate": [{"x": -3.32, "curve": [0.026, -3.24, 0.064, 19.17, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 19.17, "curve": [0.567, 19.17, 0.867, -3.32, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.32}]}, "tun": {"rotate": [{"value": 2.64, "curve": [0.022, 2.58, 0.056, -16.39]}, {"time": 0.2333, "value": -16.39, "curve": [0.544, -16.39, 0.856, 2.64]}, {"time": 1.1667, "value": 2.64}]}, "leg_R3": {"rotate": [{}]}, "leg_R4": {"rotate": [{}]}, "leg_L4": {"rotate": [{}]}, "arm_R": {"rotate": [{"value": 0.59, "curve": [0.073, 1.37, 0.161, 2.01]}, {"time": 0.2333, "value": 2.01, "curve": [0.427, 2.01, 0.606, -2.45]}, {"time": 0.8, "value": -2.46, "curve": [0.922, -2.46, 1.046, -0.72]}, {"time": 1.1667, "value": 0.59}]}, "arm_R2": {"rotate": [{"value": -0.22, "curve": [0.098, 0.88, 0.204, 2.01]}, {"time": 0.3, "value": 2.01, "curve": [0.494, 2.01, 0.673, -2.45]}, {"time": 0.8667, "value": -2.46, "curve": [0.964, -2.46, 1.071, -1.35]}, {"time": 1.1667, "value": -0.23}]}, "arm_R3": {"rotate": [{"value": -1.04, "curve": [0.122, 0.27, 0.246, 2.01]}, {"time": 0.3667, "value": 2.01, "curve": [0.561, 2.01, 0.739, -2.45]}, {"time": 0.9333, "value": -2.46, "curve": [1.006, -2.46, 1.095, -1.83]}, {"time": 1.1667, "value": -1.04}]}, "arm_R4": {"rotate": [{"value": -1.74, "curve": [0.147, -0.48, 0.288, 2.01]}, {"time": 0.4333, "value": 2.01, "curve": [0.627, 2.01, 0.839, -2.45]}, {"time": 1.0333, "value": -2.46, "curve": [1.083, -2.46, 1.119, -2.17]}, {"time": 1.1667, "value": -1.74}]}, "arm_L2": {"rotate": [{}]}, "arm_L3": {"rotate": [{}]}, "hair_F2": {"rotate": [{"value": -0.81, "curve": [0.122, 1.43, 0.246, 4.41]}, {"time": 0.3667, "value": 4.41, "curve": [0.561, 4.41, 0.739, -3.24]}, {"time": 0.9333, "value": -3.25, "curve": [1.006, -3.25, 1.095, -2.17]}, {"time": 1.1667, "value": -0.81}]}, "hair_F6": {"rotate": [{"value": -0.81, "curve": [0.122, 1.43, 0.246, 4.41]}, {"time": 0.3667, "value": 4.41, "curve": [0.561, 4.41, 0.739, -3.24]}, {"time": 0.9333, "value": -3.25, "curve": [1.006, -3.25, 1.095, -2.17]}, {"time": 1.1667, "value": -0.81}]}, "hair_R2": {"rotate": [{"value": -0.68, "curve": [0.098, 2.32, 0.204, 5.35]}, {"time": 0.3, "value": 5.35, "curve": [0.494, 5.36, 0.673, -6.7]}, {"time": 0.8667, "value": -6.7, "curve": [0.964, -6.71, 1.071, -3.71]}, {"time": 1.1667, "value": -0.68}]}, "hair_Ba": {"rotate": [{"value": 2.38, "curve": [0.073, 4.59, 0.161, 6.41]}, {"time": 0.2333, "value": 6.41, "curve": [0.427, 6.41, 0.606, -6.25]}, {"time": 0.8, "value": -6.26, "curve": [0.922, -6.26, 1.046, -1.34]}, {"time": 1.1667, "value": 2.38}]}, "hand_L2": {"rotate": [{"value": 6.45, "curve": [0.026, 6.53, 0.064, 27.95]}, {"time": 0.2667, "value": 27.95, "curve": [0.567, 27.95, 0.867, 6.45]}, {"time": 1.1667, "value": 6.45}]}, "foot_R": {"rotate": [{"value": 1.03, "curve": [0.022, 1.01, 0.056, -3.02]}, {"time": 0.2333, "value": -3.02, "curve": [0.544, -3.02, 0.856, 1.03]}, {"time": 1.1667, "value": 1.03}]}, "foot_R2": {"rotate": [{"value": 1.03, "curve": [0.022, 1.01, 0.056, -3.02]}, {"time": 0.2333, "value": -3.02, "curve": [0.544, -3.02, 0.856, 1.03]}, {"time": 1.1667, "value": 1.03}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0.02, 0.059, -4.03, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -4.01, "curve": [0.544, -3.98, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.059, -7.05]}, {"time": 0.2333, "value": -7.05, "curve": [0.544, -7.05, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.963, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.963, "curve": [0.544, 0.963, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, -0.09, 0.059, -15.2]}, {"time": 0.2333, "value": -15.29, "curve": [0.544, -15.46, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0.02, 0.059, -4.03, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -4.01, "curve": [0.544, -3.98, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.059, 10.41]}, {"time": 0.2333, "value": 10.41, "curve": [0.544, 10.41, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.059, 0.963, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.963, "curve": [0.544, 0.963, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0.09, 0.059, 18.09]}, {"time": 0.2333, "value": 18.19, "curve": [0.544, 18.36, 0.856, 0]}, {"time": 1.1667}]}, "RU_L": {"translate": [{"x": -13.66, "curve": [0.074, -32.36, 0.161, -47.7, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -47.7, "curve": [0.428, -47.7, 0.605, 59.31, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 59.33, "curve": [0.922, 59.35, 1.046, 17.75, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -13.64}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.073, 0.986, 0.16, 0.865, 0.073, 0.97, 0.16, 1.042]}, {"time": 0.2333, "x": 0.865, "y": 1.042, "curve": [0.331, 0.865, 0.403, 1.082, 0.331, 1.042, 0.403, 0.912]}, {"time": 0.5, "x": 1.082, "y": 0.912, "curve": [0.597, 1.082, 0.703, 0.865, 0.597, 0.912, 0.703, 1.042]}, {"time": 0.8, "x": 0.865, "y": 1.042, "curve": [0.897, 0.865, 1.003, 1.082, 0.897, 1.042, 1.003, 0.912]}, {"time": 1.1, "x": 1.082, "y": 0.912, "curve": [1.125, 1.082, 1.143, 1.068, 1.125, 0.912, 1.143, 0.921]}, {"time": 1.1667, "x": 1.047, "y": 0.933}]}, "RU_L2": {"translate": [{"x": 4.83, "curve": [0.099, -11.89, 0.203, -28.84, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -28.84, "curve": [0.495, -28.84, 0.672, 38.47, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 38.48, "curve": [0.965, 38.49, 1.071, 21.78, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": 4.84}], "scale": [{"x": 1.082, "y": 0.912, "curve": [0.097, 1.082, 0.203, 0.865, 0.097, 0.912, 0.203, 1.042]}, {"time": 0.3, "x": 0.865, "y": 1.042, "curve": [0.397, 0.865, 0.503, 1.082, 0.397, 1.042, 0.503, 0.912]}, {"time": 0.6, "x": 1.082, "y": 0.912, "curve": [0.697, 1.082, 0.769, 0.865, 0.697, 0.912, 0.769, 1.042]}, {"time": 0.8667, "x": 0.865, "y": 1.042, "curve": [0.964, 0.865, 1.069, 1.082, 0.964, 1.042, 1.069, 0.912]}, {"time": 1.1667, "x": 1.082, "y": 0.912}]}, "RU_L3": {"translate": [{"x": 15.02, "curve": [0.122, -3.24, 0.245, -27.53, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -27.53, "curve": [0.561, -27.53, 0.739, 34.83, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 34.85, "curve": [1.007, 34.85, 1.094, 26.05, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 15.02}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.024, 1.067, 0.042, 1.082, 0.024, 0.921, 0.042, 0.912]}, {"time": 0.0667, "x": 1.082, "y": 0.912, "curve": [0.164, 1.082, 0.269, 0.865, 0.164, 0.912, 0.269, 1.042]}, {"time": 0.3667, "x": 0.865, "y": 1.042, "curve": [0.464, 0.865, 0.569, 1.082, 0.464, 1.042, 0.569, 0.912]}, {"time": 0.6667, "x": 1.082, "y": 0.912, "curve": [0.764, 1.082, 0.836, 0.865, 0.764, 0.912, 0.836, 1.042]}, {"time": 0.9333, "x": 0.865, "y": 1.042, "curve": [1.007, 0.865, 1.094, 0.986, 1.007, 1.042, 1.094, 0.97]}, {"time": 1.1667, "x": 1.047, "y": 0.933}]}, "RU_R": {"translate": [{"x": -13.66, "curve": [0.074, -32.36, 0.161, -47.7, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -47.7, "curve": [0.428, -47.7, 0.605, 59.31, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 59.33, "curve": [0.922, 59.35, 1.046, 17.75, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -13.64}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.073, 0.986, 0.16, 0.865, 0.073, 0.97, 0.16, 1.042]}, {"time": 0.2333, "x": 0.865, "y": 1.042, "curve": [0.331, 0.865, 0.403, 1.082, 0.331, 1.042, 0.403, 0.912]}, {"time": 0.5, "x": 1.082, "y": 0.912, "curve": [0.597, 1.082, 0.703, 0.865, 0.597, 0.912, 0.703, 1.042]}, {"time": 0.8, "x": 0.865, "y": 1.042, "curve": [0.897, 0.865, 1.003, 1.082, 0.897, 1.042, 1.003, 0.912]}, {"time": 1.1, "x": 1.082, "y": 0.912, "curve": [1.125, 1.082, 1.143, 1.068, 1.125, 0.912, 1.143, 0.921]}, {"time": 1.1667, "x": 1.047, "y": 0.933}]}, "RU_R2": {"translate": [{"x": 4.78, "curve": [0.099, -10.24, 0.203, -25.48, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -25.48, "curve": [0.495, -25.48, 0.672, 35.02, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 35.04, "curve": [0.965, 35.04, 1.071, 20.02, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": 4.79}], "scale": [{"x": 1.082, "y": 0.912, "curve": [0.097, 1.082, 0.203, 0.865, 0.097, 0.912, 0.203, 1.042]}, {"time": 0.3, "x": 0.865, "y": 1.042, "curve": [0.397, 0.865, 0.503, 1.082, 0.397, 1.042, 0.503, 0.912]}, {"time": 0.6, "x": 1.082, "y": 0.912, "curve": [0.697, 1.082, 0.769, 0.865, 0.697, 0.912, 0.769, 1.042]}, {"time": 0.8667, "x": 0.865, "y": 1.042, "curve": [0.964, 0.865, 1.069, 1.082, 0.964, 1.042, 1.069, 0.912]}, {"time": 1.1667, "x": 1.082, "y": 0.912}]}, "RU_R3": {"translate": [{"x": 13.79, "curve": [0.122, -2.43, 0.245, -24.01, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -24.01, "curve": [0.561, -24.01, 0.739, 31.39, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 31.4, "curve": [1.007, 31.41, 1.094, 23.59, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 13.79}], "scale": [{"x": 1.047, "y": 0.933, "curve": [0.024, 1.067, 0.042, 1.082, 0.024, 0.921, 0.042, 0.912]}, {"time": 0.0667, "x": 1.082, "y": 0.912, "curve": [0.164, 1.082, 0.269, 0.865, 0.164, 0.912, 0.269, 1.042]}, {"time": 0.3667, "x": 0.865, "y": 1.042, "curve": [0.464, 0.865, 0.569, 1.082, 0.464, 1.042, 0.569, 0.912]}, {"time": 0.6667, "x": 1.082, "y": 0.912, "curve": [0.764, 1.082, 0.836, 0.865, 0.764, 0.912, 0.836, 1.042]}, {"time": 0.9333, "x": 0.865, "y": 1.042, "curve": [1.007, 0.865, 1.094, 0.986, 1.007, 1.042, 1.094, 0.97]}, {"time": 1.1667, "x": 1.047, "y": 0.933}]}, "bone61": {"rotate": [{"value": 0.07}]}, "bone62": {"rotate": [{"value": 0.35}]}, "bone63": {"rotate": [{"value": -0.06}]}, "bone64": {"rotate": [{"value": -0.04}]}, "leg_R1": {"translate": [{"y": -12.25, "curve": [0.022, -0.02, 0.056, -5.12, 0.022, -12.21, 0.056, 0]}, {"time": 0.2333, "x": -5.12, "curve": [0.544, -5.12, 0.856, 0, 0.544, 0, 0.856, -12.25]}, {"time": 1.1667, "y": -12.25}]}, "cloth": {"rotate": [{"value": -5.76, "curve": [0.146, -2.95, 0.289, 2.59]}, {"time": 0.4333, "value": 2.59, "curve": [0.626, 2.59, 0.807, -7.35]}, {"time": 1, "value": -7.35, "curve": [1.049, -7.36, 1.12, -6.72]}, {"time": 1.1667, "value": -5.76}]}, "hair_Bb": {"rotate": [{"value": 0.07, "curve": [0.098, 3.22, 0.204, 6.41]}, {"time": 0.3, "value": 6.41, "curve": [0.494, 6.41, 0.673, -6.25]}, {"time": 0.8667, "value": -6.26, "curve": [0.964, -6.26, 1.071, -3.11]}, {"time": 1.1667, "value": 0.07}]}, "hair": {"translate": [{"y": -0.54, "curve": [0.111, 0, 0.222, 0, 0.032, -0.47, 0.081, 12.79]}, {"time": 0.3333, "y": 12.79, "curve": [0.611, 0, 0.889, 0, 0.611, 12.79, 0.889, -0.54]}, {"time": 1.1667, "y": -0.54}]}, "headround3": {"translate": [{"x": 40.63, "curve": [0.032, 38.93, 0.081, -307.27, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -307.27, "curve": [0.611, -307.27, 0.889, 40.63, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 40.63}]}, "headround": {"translate": [{"y": -76.47, "curve": [0.111, 0, 0.222, 0, 0.032, -77.35, 0.081, -256.5]}, {"time": 0.3333, "y": -256.5, "curve": [0.611, 0, 0.889, 0, 0.611, -256.5, 0.889, -76.47]}, {"time": 1.1667, "y": -76.47}]}, "bodyround": {"translate": [{"y": 77.78, "curve": [0.089, 0, 0.178, 0, 0.026, 76.09, 0.064, -354.57]}, {"time": 0.2667, "y": -354.57, "curve": [0.567, 0, 0.867, 0, 0.567, -354.57, 0.867, 77.78]}, {"time": 1.1667, "y": 77.78}]}, "tunround": {"translate": [{"x": -223.26, "curve": [0.022, -221.2, 0.056, 380.36, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 380.36, "curve": [0.544, 380.36, 0.856, -223.26, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -223.26}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-3.17106, -0.63241, -3.17111, -0.63226, -3.50354, -0.42165, -3.50359, -0.42142, -3.49246, -0.45182, -3.49262, -0.45164, -2.30497, -0.17906, -2.30536, -0.17878, -1.40173, 0.46044, -1.40194, 0.46071, -0.64681, -0.02566, -0.64687, -0.0256, -0.38752, -0.04298, -0.38762, -0.04285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.28679, -0.30939, -1.28682, -0.30925, 0, 0, 0, 0, -1.5991, -0.25168, -1.5991, -0.25161, -3.23602, -0.24674, -3.23602, -0.24656, -3.77986, -0.06689, -3.77986, -0.06662, -3.53544, -0.02181, -3.53544, -0.02161, -2.58559, -0.22144, -2.58559, -0.2213, -0.98479, -0.19141, -0.98479, -0.19135, -0.39277, -0.06351, -0.39277, -0.06347], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-3.57584, 0.24209, -3.57607, 0.2423, -3.11921, 0.11086, -3.11939, 0.11102, -2.0959, 0.12254, -2.09616, 0.12264, -0.77332, 0.02873, -0.77335, 0.02882, -0.10747, -0.01742, -0.10758, -0.01734, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37441, -0.06058, -0.37441, -0.06052, -0.82384, -0.13327, -0.82384, -0.13316, -1.26491, -0.10201, -1.26491, -0.10192, -1.58061, -0.05051, -1.58074, -0.05038, -2.06197, 0.23054, -2.06202, 0.23067, -2.88536, 0.25123, -2.88549, 0.25142, -1.41412, 0.06279, -1.41412, 0.06286, -2.78661, 0.37169, -2.78667, 0.37182, -3.64447, 0.38607, -3.64458, 0.38621, -3.64895, 0.18908, -3.64903, 0.18922, -2.60009, 0.18896, -2.60009, 0.18906, -1.32395, -0.00212, -1.32395, -0.00197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1746, 0.02474, -0.1746, 0.02479, -0.46071, 0.05306, -0.46071, 0.05315], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 738, "vertices": [-1.30654, -0.19473, -1.30678, -0.19472, -2.72122, -0.39456, -2.72151, -0.39447, -3.49406, -0.36727, -3.49445, -0.36715, -3.49626, -0.39171, -3.49647, -0.39162, -2.81005, -0.33865, -2.81037, -0.33854, -1.64012, -0.2363, -1.6402, -0.23618, -0.35728, -0.11567, -0.35749, -0.11563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23, -0.00827, -0.23016, -0.00824, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.01324, 0.18346, -1.01355, 0.18354, -2.16335, 0.31586, -2.16369, 0.31598, -3.38895, 0.37818, -3.38926, 0.37832, -3.71355, 0.23883, -3.71408, 0.23903, -2.97208, 0.1272, -2.9725, 0.12735, -1.8495, 0.16399, -1.84971, 0.16411, -0.54018, -0.05842, -0.5404, -0.05837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.31464, -0.02195, -0.31477, -0.02192, -0.67576, 0.06436, -0.67598, 0.06442], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.024, 0, 0.063, 0.99]}, {"time": 0.2333, "offset": 60, "vertices": [1.50098, -0.35926, 1.50256, -0.35933, 1.05627, 0.00889, 1.05713, 0.00911, 1.44336, 0.03392, 1.44348, 0.03395, 2.14124, -0.00064, 2.14172, -0.00052, 1.47607, 0.03354, 1.4762, 0.03377, 1.26013, 0.13071, 1.26025, 0.13089, 1.62952, 0.23862, 1.62976, 0.23859, 2.64697, 0.51054, 2.64819, 0.51073, 0.83704, 0.05639, 0.83716, 0.05664, 1.84619, 0.13811, 1.849, 0.13792, 1.80774, -0.30685, 1.81079, -0.30702, 1.86548, 0.3606, 1.86804, 0.3604, 1.94446, -0.49356, 1.94629, -0.49359, 3.08667, -0.38114, 3.08728, -0.3812, 4.21582, 0.07926, 4.21887, 0.07977, 5.77039, 0.86201, 5.77417, 0.86241, 5.76282, 0.86209, 5.7666, 0.86256, 4.97766, 0.69395, 4.98083, 0.69408, 3.29028, 0.61084, 3.29053, 0.61118, 3.29163, 0.02252, 3.29321, 0.02269, 4.68677, -0.73333, 4.68787, -0.73311, 3.46375, -0.95884, 3.46484, -0.95854, 2.94739, -0.12011, 2.94861, -0.11984, 2.64221, 0.24228, 2.64343, 0.24252, 3.28137, -0.14877, 3.28259, -0.1485, 1.96582, 0.35198, 1.96838, 0.35176, 2.31726, 0.61053, 2.31909, 0.61047, 2.12476, 0.19617, 2.12598, 0.1963, 2.59045, 0.14712, 2.59155, 0.14749, 3.33435, 0.09428, 3.33618, 0.09467, 2.59888, 0.14702, 2.59998, 0.14749, 1.83301, 0.15566, 1.83411, 0.15604, 1.88354, 0.32332, 1.88379, 0.3235, 3.51526, 0.50076, 3.51648, 0.5009, 3.37354, -0.38081, 3.37476, -0.38052, 4.97375, 0.049, 4.97595, 0.04947, 6.67004, 0.3749, 6.67297, 0.37544, 4.95313, 0.05082, 4.95544, 0.05125, 6.66711, 0.37494, 6.67004, 0.37549, 4.17859, 0.61156, 4.17932, 0.61159, 4.17859, 0.61156, 4.17932, 0.61159], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}}}}}}