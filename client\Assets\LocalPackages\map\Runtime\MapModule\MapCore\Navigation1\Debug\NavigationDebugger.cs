﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;


namespace TFW.Map.Nav
{
    public partial class NavigationDebugger
    {
        public void Create(NaviMgr navMgr, GameObject root)
        {
            mNavMgr = navMgr;

            CreateTriangles(root);
            CreateConvexs(root);
            CreateSearchGridInfo();
        }

        public void OnDestroy()
        {
            DestroyTriangles();
            DestroyConvexs();
        }

        NaviMgr mNavMgr;
    }
}

#endif