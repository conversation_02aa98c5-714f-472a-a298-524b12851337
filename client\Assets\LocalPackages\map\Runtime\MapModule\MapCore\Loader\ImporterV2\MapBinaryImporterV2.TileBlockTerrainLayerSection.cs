﻿ 



 
 


using JetBrains.Annotations;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadTileBlockTerrainLayerData(BinaryReader reader)
        {
            long position = GetSectionDataStartPosition(MapDataSectionType.TileBlockTerrainLayer);
            if (position < 0) 
            {
                return null;
            }
            reader.BaseStream.Position = position;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);
            bool useGeneratedLOD = reader.ReadBoolean();

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            int tileBlockCount = reader.ReadInt32();
            TileBlockTerrainLayerData.TileBlock[] blocks = new TileBlockTerrainLayerData.TileBlock[tileBlockCount];
            for (int i = 0; i < tileBlockCount; ++i)
            {
                int minX = reader.ReadUInt16();
                int minY = reader.ReadUInt16();
                int width = reader.ReadUInt16();
                int height = reader.ReadUInt16();
                int singleTileType = reader.ReadUInt16();

                blocks[i] = new TileBlockTerrainLayerData.TileBlock(minX, minY, minX + width - 1, minY + height - 1, singleTileType);
            }

            tileBlockCount = reader.ReadInt32();
            Dictionary<int, Vector2[]> atlasUVMappings = new Dictionary<int, Vector2[]>(tileBlockCount);
            for (int i = 0; i < tileBlockCount; ++i)
            {
                int singleTileType = reader.ReadInt32();
                Vector2[] uvsInAtlas = Utils.ReadVector2Array(reader);
                atlasUVMappings.Add(singleTileType, uvsInAtlas);
            }

            tileBlockCount = reader.ReadInt32();
            Dictionary<int, string> materialPaths = new Dictionary<int, string>(tileBlockCount);
            for (int i = 0; i < tileBlockCount; ++i)
            {
                int singleTileType = reader.ReadInt32();
                string mtlPath = Utils.ReadString(reader);
                materialPaths.Add(singleTileType, mtlPath);
            }

            int n = rows * cols;
            ushort[] tiles = new ushort[n];
            for (int i = 0; i < n; ++i)
            {
                tiles[i] = reader.ReadUInt16();
            }
            var prefabPaths = Utils.ReadStringArray(reader);

            var config = LoadTileBlockTerrainLayerLODConfig(reader);
            //-------------------version 1 end-----------------------

            var layer = new config.TileBlockTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, tiles, prefabPaths, useGeneratedLOD, blocks, atlasUVMappings, materialPaths);
            return layer;
        }

        config.MapLayerLODConfig LoadTileBlockTerrainLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                int terrainLODTileCount = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, flag, terrainLODTileCount);
            }
            return config;
        }
    }
}
