﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class LODLayerData : MapLayerData
    {
        public LODLayerData(MapLayerDataHeader header, MapLayerLODConfig lodConfig, Map map) : base(header, lodConfig, map)
        {
        }

        public override void OnDestroy()
        {
        }

        public override void RefreshObjectsInViewport() { }
        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (map.enableLOD)
            {
                lodChanged = base.SetZoom(newZoom);
            }
            return lodChanged;
        }
        public override bool Contains(int objectID) { return false; }

        public override bool isGameLayer { get { return true; } }
    }
}
