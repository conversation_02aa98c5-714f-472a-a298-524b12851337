﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.11.25
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum RiverEditorToolType
    {
        Create,
        EditVertex,
        Move,
        PaintTexture,
        Split,
    }

    public enum TextureChannel
    {
        Red,
        Green,
        Blue,
        Alpha,
    }

    public class BrushSetting
    {
        public BrushSetting(int size, int strength)
        {
            this.size = size;
            this.strength = strength;
        }

        public TextureChannel channel = TextureChannel.Alpha;
        public int size;
        public int strength;
        public bool increaseAlpha = false;
    }

    [ExecuteInEditMode]
    [Black]
    public class PolygonRiverLayerLogic : MapLayerLogic
    {
        public RiverEditorToolType operation = RiverEditorToolType.Create;
        public BrushSetting brushSetting = new BrushSetting(50, 50);
        public float riverHeight;

        public PolygonRiverLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as PolygonRiverLayer;
                return layer;
            }
        }

        protected override void OnUpdate()
        {
            if (mRemovedCollisionsGameObjectID.Count > 0)
            {
                var collisionLayer = this.layer;
                mExistedObjectIDs.Clear();
                mAllObjects.Clear();
                collisionLayer.GetAllObjects(mAllObjects);

                int n = transform.childCount;
                for (int k = n - 1; k >= 0; --k)
                {
                    var child = transform.GetChild(k).gameObject;

                    var objectID = collisionLayer.GetObjectIDByGameObjectID(child.GetInstanceID());
                    if (objectID != 0)
                    {
                        mExistedObjectIDs.Add(objectID);
                    }
                }

                //find difference
                for (int j = 0; j < mAllObjects.Count; ++j)
                {
                    bool found = false;
                    for (int i = 0; i < mExistedObjectIDs.Count; ++i)
                    {
                        if (mAllObjects[j].GetEntityID() == mExistedObjectIDs[i])
                        {
                            found = true;
                            break;
                        }
                    }
                    if (!found)
                    {
                        collisionLayer.RemoveObject(mAllObjects[j].GetEntityID());
                    }
                }

                mRemovedCollisionsGameObjectID.Clear();
            }
        }

        public void OnCollisionModelRemoved(int gameObjectID)
        {
            mRemovedCollisionsGameObjectID.Add(gameObjectID);
        }

        List<int> mRemovedCollisionsGameObjectID = new List<int>();
        List<int> mExistedObjectIDs = new List<int>();
        List<IMapObjectData> mAllObjects = new List<IMapObjectData>();
    }
}


#endif