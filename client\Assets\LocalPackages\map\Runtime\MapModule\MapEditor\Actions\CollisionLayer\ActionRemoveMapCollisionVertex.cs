﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionRemoveMapCollisionVertex : EditorAction
    {
        public ActionRemoveMapCollisionVertex(int layerID, int dataID, int index, PrefabOutlineType type)
        {
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            mLayerID = layerID;
            mDataID = dataID;
            mPosition = data.GetOutlineVertices(type)[index];
            mOutlineType = type;
            mIndex = index;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveCollsionVertex(mOutlineType, mDataID, mIndex);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.InsertCollisionVertex(mOutlineType, mDataID, mIndex, mPosition);
            return true;
        }

        public override string description
        {
            get
            {
                return string.Format("{0}, idx: {1}, position: {2}", GetType().Name, mIndex, mPosition.ToString());
            }
        }

        int mLayerID;
        int mDataID;
        int mIndex;
        Vector3 mPosition;
        PrefabOutlineType mOutlineType;
    }
}


#endif