﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public static class ChildPrefabReplacer
    {
        [MenuItem("Assets/TFW/Tools/Create Prefab LOD")]
        static void ReplacePrefab()
        {
            var objects = Selection.gameObjects;
            if (objects == null || objects.Length == 0)
            {
                EditorUtility.DisplayDialog("Error", "Select a prefab first!", "OK");
                return;
            }

            mOriginalPrefabPath = AssetDatabase.GetAssetPath(objects[0]);
            if (string.IsNullOrEmpty(mOriginalPrefabPath))
            {
                EditorUtility.DisplayDialog("Error", "Not a prefab!", "OK");
                return;
            }

            if (mOriginalPrefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX) < 0)
            {
                EditorUtility.DisplayDialog("Error", "Not a lod prefab, prefab must be named xxx_lod0 or xxx_lod1 etc!", "OK");
                return;
            }

            mOriginalPrefabPath = Utils.RemoveExtension(mOriginalPrefabPath);

            var inputDialog = EditorWindow.GetWindow<InputDialog>("Create LOD Prefab");
            inputDialog.minSize = new Vector2(200, 100);
            inputDialog.maxSize = new Vector2(300, 100);
            var position = inputDialog.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            inputDialog.position = position;

            string lodName = mOriginalPrefabPath.Substring(mOriginalPrefabPath.Length - 1);

            int currentLOD;
            bool suc = int.TryParse(lodName, out currentLOD);
            if (!suc)
            {
                currentLOD = 0;
            }
            int newLOD = currentLOD + 1;

            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("New LOD", "", newLOD.ToString()),
            };

            inputDialog.Show(items, OnCreateLOD);
        }

        static bool OnCreateLOD(List<InputDialog.Item> p)
        {
            int newLOD = 0;
            bool suc = int.TryParse((p[0] as InputDialog.StringItem).text, out newLOD);
            if (!suc)
            {
                return false;
            }
            string oldPrefabPath = mOriginalPrefabPath + ".prefab";
            string newPrefabPath = GetLODPrefabAssetPath(mOriginalPrefabPath, newLOD);

            GetPrefabInfos(oldPrefabPath);
            CreatePrefab(newPrefabPath, newLOD);

            return true;
        }

        static void GetPrefabInfos(string prefabPath)
        {
            mPrefabInfos = new List<ChildPrefabTransform>();
            //读取子prefeb的信息
            var tilePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            int n = tilePrefab.transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                var childTransform = tilePrefab.transform.GetChild(i);
                var childPrefabInstance = childTransform.gameObject;

                //获取prefab instance使用的prefab
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);

                var childPrefabAssetPath = AssetDatabase.GetAssetPath(childPrefab);

                var childPrefabTransform = new ChildPrefabTransform();
                childPrefabTransform.path = childPrefabAssetPath;
                childPrefabTransform.tag = childTransform.tag;
                childPrefabTransform.position = childTransform.position;
                childPrefabTransform.editorScaling = childTransform.localScale;
                childPrefabTransform.editorRotation = childTransform.rotation;
                mPrefabInfos.Add(childPrefabTransform);
            }
        }

        static void CreatePrefab(string newPrefabPath, int newLOD)
        {
            string prefabName = Utils.GetPathName(newPrefabPath, false);
            var newPrefabRoot = new GameObject(prefabName);
            PrefabUtility.SaveAsPrefabAsset(newPrefabRoot, newPrefabPath);
            var prefabInOtherScene = PrefabUtility.LoadPrefabContents(newPrefabPath);

            for (int i = 0; i < mPrefabInfos.Count; ++i)
            {
                if (!string.IsNullOrEmpty(mPrefabInfos[i].path))
                {
                    var newLODPrefabPath = GetLODPrefabAssetPath(mPrefabInfos[i].path, newLOD);
                    //加载一个独立场景的prefab    
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(newLODPrefabPath);
                    if (childPrefab == null)
                    {
                        newLODPrefabPath = mPrefabInfos[i].path;
                        childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(newLODPrefabPath);
                    }

                    Debug.Assert(childPrefab != null);

                    //生成一个prefab instance,用来嵌套到新的prefab中
                    var childPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(childPrefab);

                    childPrefabInstance.transform.position = mPrefabInfos[i].position;
                    childPrefabInstance.transform.rotation = mPrefabInfos[i].editorRotation;
                    childPrefabInstance.transform.localScale = mPrefabInfos[i].editorScaling;
                    childPrefabInstance.tag = mPrefabInfos[i].tag;
                    if (childPrefabInstance != null)
                    {
                        childPrefabInstance.transform.SetParent(prefabInOtherScene.transform, true);
                    }
                    else
                    {
                        Debug.Assert(false, "Invalid child prefab");
                    }
                }
            }

            PrefabUtility.SaveAsPrefabAsset(prefabInOtherScene, newPrefabPath);

            PrefabUtility.UnloadPrefabContents(prefabInOtherScene);
            GameObject.DestroyImmediate(newPrefabRoot);
        }

        static string GetLODPrefabAssetPath(string prefabPath, int newLOD)
        {
            prefabPath = Utils.RemoveExtension(prefabPath);
            if (prefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX) >= 0)
            {
                string newPrefabPath = prefabPath.Substring(0, prefabPath.Length - 1) + newLOD.ToString() + ".prefab";
                return newPrefabPath;
            }
            return prefabPath + ".prefab";
        }

        static string mOriginalPrefabPath;
        static List<ChildPrefabTransform> mPrefabInfos;
    }
}

#endif