﻿ 



 
 



#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System;
using System.Text.RegularExpressions;
using System.IO;

namespace TFW.Map
{
    public enum TileFillMode
    {
        kEmptyTiles,
        kEmptyTilesWithoutCollisionCheck,
        kAllTiles,
    }

    public class DecorationPrefabInfo
    {
        public List<string> prefabGUIDs = new List<string>();
        public int fixedIndex = -1;

        public void AddDecorationPrefab(string guid)
        {
            prefabGUIDs.Add(guid);
        }

        public void ChangeDecorationPrefabCount(int newCount)
        {
            if (newCount == prefabGUIDs.Count)
            {
                return;
            }

            List<string> newGuids = new List<string>(newCount);
            int n = Mathf.Min(newCount, prefabGUIDs.Count);
            for (int i = 0; i < n; ++i)
            {
                newGuids.Add(prefabGUIDs[i]);
            }
            for (int i = n; i < newCount; ++i)
            {
                newGuids.Add("");
            }

            prefabGUIDs = newGuids;
        }
    }

    public class PrefabGroup
    {
        public class Item
        {
            public Item(string prefabPath, string pathName, string[] subgroupPrefabs, int fixedSubgroupPrefabIndex, Texture2D previewTexture, object customParameter, Vector2Int size, int id)
            {
                this.prefabPath = prefabPath;
                this.pathName = pathName;
                this.previewTexture = previewTexture;
                this.subGroupPrefabs = subgroupPrefabs;
                this.fixedSubgroupPrefabIndex = fixedSubgroupPrefabIndex;
                this.customParameter = customParameter;
                this.size = size;
                mID = id;
            }

            public void OnDestroy()
            {
                GameObject.DestroyImmediate(previewTexture);
            }

            public string GetRandomPrefab()
            {
                if (decorationPrefab.prefabGUIDs.Count > 0)
                {
                    int index = decorationPrefab.fixedIndex;
                    if (index < 0)
                    {
                        index = UnityEngine.Random.Range(0, decorationPrefab.prefabGUIDs.Count);
                    }
                    index = Mathf.Clamp(index, 0, decorationPrefab.prefabGUIDs.Count - 1);
                    return AssetDatabase.GUIDToAssetPath(decorationPrefab.prefabGUIDs[index]);
                }
                return "";
            }

            public int id { get { return mID; } }

            public string prefabPath;
            public string pathName;
            public Texture2D previewTexture;
            //边角prefab使用的变体
            public string[] subGroupPrefabs;
            //边角prefab固定使用某个样式
            public int fixedSubgroupPrefabIndex = -1;
            public DecorationPrefabInfo decorationPrefab = new DecorationPrefabInfo();
            public object customParameter;
            //用在地表tile上
            public Vector2Int size;
            int mID = 0;
        }

        public PrefabGroup(int id, bool allowDuplication, PrefabManager prefabManager, string name, float gridSize, bool addPrefabSet, bool editSubgroup, string[] subGroups, Action<GameObject> onSelectPrefab, Action<int, GameObject> onAddPrefab, Action<int, int, string> onRemovePrefab, Action<List<GameObject>, TileFillMode> onFillRandomTiles, Action<int, int> onSetPrefabFixedIndex, Action<int, int, GameObject> onSetPrefab, Action<int, int, int, string> onEditSubGroupPrefab, Action<int, int, int> onSetPrefabSubGroupPrefabFixedIndex, Action<int, int, int> onChangeSubPrefabCountCallback, Action<int, float> onGridSizeChange)
        {
            mGroupID = id;
            mName = name;
            mGridSize = gridSize;
            mAllowDuplication = allowDuplication;
            mEditSubgroup = editSubgroup;
            mAddPrefabSet = addPrefabSet;
            mOnSelectPrefab = onSelectPrefab;
            mOnAddPrefab = onAddPrefab;
            mOnRemovePrefab = onRemovePrefab;
            mOnFillRandomTiles = onFillRandomTiles;
            mOnSetPrefabFixedIndex = onSetPrefabFixedIndex;
            mOnSetPrefab = onSetPrefab;
            mOnGridSizeChange = onGridSizeChange;
            mPrefabManager = prefabManager;
            mOnEditSubGroupPrefab = onEditSubGroupPrefab;
            mOnChangeSubPrefabCountCallback = onChangeSubPrefabCountCallback;
            mOnSetPrefabSubGroupPrefabFixedIndex = onSetPrefabSubGroupPrefabFixedIndex;
        }

        public void OnDestroy()
        {
            OnDeactivate();
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null)
                {
                    mPrefabs[i].OnDestroy();
                }
            }
            mPrefabs.Clear();
        }

        public int GetMaxItemID()
        {
            int maxID = 0;
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null && mPrefabs[i].id > maxID)
                {
                    maxID = mPrefabs[i].id;
                }
            }
            return maxID;
        }

        public bool AddPrefab(string prefabPath, string[] subGroupPrefabPaths, bool checkTransformIsIdentity, bool logWarning, bool addOnlyOnePrefab, object customParameter, Vector2Int size, int id)
        {
            if (addOnlyOnePrefab)
            {
                if (mPrefabs.Count != 0)
                {
                    EditorUtility.DisplayDialog("Error", $"Can only add one prefab!", "OK");
                    return false;
                }
            }

            if (mAllowDuplication || 
                Contains(prefabPath) == false || 
                mAddPrefabSet)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab != null)
                {
                    if (checkTransformIsIdentity && !Utils.IsTransformIdentity(prefab))
                    {
                        EditorUtility.DisplayDialog("Error", $"Can't add {prefabPath}, Prefab transform is not identity!", "OK");
                        return false;
                    }

                    var previewTexture = AssetPreview.GetAssetPreview(prefab);
                    if (previewTexture != null)
                    {
                        previewTexture = CreatePreviewTexture(previewTexture);
                    }
                    var pathName = Utils.GetPathName(prefabPath, true);
                    if (customParameter == null)
                    {
                        customParameter = mPrefabManager.createCustomParameterFunc != null ? mPrefabManager.createCustomParameterFunc() : null;
                    }
                    var item = new Item(prefabPath, pathName, subGroupPrefabPaths, -1, previewTexture, customParameter, size, id);
                    mPrefabs.Add(item);
                    SetSelection(mPrefabs.Count - 1);

                    if (mOnAddPrefab != null)
                    {
                        var groupIndex = mPrefabManager.GetGroupIndex(this);
                        mOnAddPrefab(groupIndex, prefab);
                    }
                }
                else
                {
                    //add一个null的item
                    mPrefabs.Add(null);
                }

                return true;
            }

            if (logWarning)
            {
                EditorUtility.DisplayDialog("Error", "Prefab already exist!", "OK");
            }
            return false;
        }

        void Clear()
        {
            OnDestroy();
            mSelectedPrefab = -1;
        }

        void AddPrefabSet(string prefabPath, bool checkTransformIsIdentity)
        {
            //先清理
            Clear();

            int maxPrefabCount = MapModule.useOnly15Prefab ? 16 : 32;

            string[] validPrefabPaths = new string[maxPrefabCount];

            int tileIndex;
            var prefabPathPrefix = GetPrefabPathPrefix(prefabPath, out tileIndex);
            for (int i = 0; i < maxPrefabCount; ++i)
            {
                var fullPath = string.Format("{0}{1}{2}0.prefab", prefabPathPrefix, i.ToString("D2"), MapCoreDef.MAP_PREFAB_LOD_PREFIX);
                if (AssetDatabase.LoadAssetAtPath<GameObject>(fullPath) != null)
                {
                    validPrefabPaths[i] = fullPath;
                }
            }

            var folder = Utils.GetFolderPath(prefabPath);
            for (int i = 0; i < validPrefabPaths.Length; ++i)
            {
                if (validPrefabPaths[i] != null)
                {
                    var prefix = GetPrefabPathPrefix(validPrefabPaths[i], out tileIndex);
                    List<string> variationPrefabPaths = GetVariationPrefabPaths(Utils.GetPathName(prefix, false), i, folder);
                    variationPrefabPaths.Insert(0, validPrefabPaths[i]);
                    AddPrefab(validPrefabPaths[i], variationPrefabPaths.ToArray(), checkTransformIsIdentity, false, false, null, new Vector2Int(1, 1), 0);

                    //set terrain prefab manager data
                    var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
                    for (int k = 0; k < variationPrefabPaths.Count; ++k)
                    {
                        terrainPrefabManager.SetGroupPrefabByID(groupID, i, k, variationPrefabPaths[k]);
                    }
                }
                else
                {
                    AddPrefab("", null, false, false, false, null, Vector2Int.zero, 0);
                }
            }


            LoadVariationList();
        }

        List<string> GetVariationPrefabPaths(string prefabPathPrefix, int tileIndex, string folder)
        {
            List<string> results = new List<string>();
            var enumerator = Directory.EnumerateFiles(folder, "*.prefab", SearchOption.TopDirectoryOnly);
            foreach (var file in enumerator)
            {
                var filepath = file.Replace('\\', '/');
                string fileName = Utils.GetPathName(filepath, true);

                string pattern = $"{prefabPathPrefix}{tileIndex.ToString("D2")}_\\w+{MapCoreDef.MAP_PREFAB_LOD_PREFIX}0.prefab";
                Regex rx = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase);
                if (rx.Match(fileName).Length == fileName.Length)
                {
                    results.Add(folder + "/" + fileName);
                }
            }

            return results;
        }

        string GetPrefabPathPrefix(string prefabPath, out int tileIndex)
        {
            tileIndex = -1;
            var dotIdx = prefabPath.IndexOf(".");
            var fullPathWithourExt = prefabPath.Substring(0, dotIdx);
            var prefixIdx = fullPathWithourExt.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (prefixIdx != -1)
            {
                var prefixString = fullPathWithourExt.Substring(0, prefixIdx);
                string tileIndexStr = prefixString.Substring(prefixString.Length - 2, 2);
                Utils.ParseInt(tileIndexStr, out tileIndex);
                return prefixString.Substring(0, prefixString.Length - 2);
            }
            return prefabPath;
        }

        Texture2D CreatePreviewTexture(Texture2D texture)
        {
            var previewTexture = new Texture2D(texture.width, texture.height, texture.format, false);
            previewTexture.SetPixels(texture.GetPixels());
            previewTexture.Apply();
            TextureScale.Bilinear(previewTexture, texture.width / 2, texture.height / 2);
            return previewTexture;
        }

        public int GetPrefabIndex(string prefabPath)
        {
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i].prefabPath == prefabPath)
                {
                    return i;
                }
            }
            return -1;
        }

        public void RemoveInvalidPrefabs()
        {
            for (int i = mPrefabs.Count - 1; i >= 0; --i)
            {
                string path = GetPrefabPath(i);
                if (string.IsNullOrEmpty(path))
                {
                    RemovePrefab(i);
                }
            }
        }

        public void RemovePrefab(int index)
        {
            if (index >= 0 && index < mPrefabs.Count)
            {
                if (mOnRemovePrefab != null && mSelectedPrefab >= 0)
                {
                    int groupIndex = mPrefabManager.GetGroupIndex(this);
                    if (mPrefabs[mSelectedPrefab] != null)
                    {
                        mOnRemovePrefab(groupIndex, index, mPrefabs[mSelectedPrefab].prefabPath);
                    }
                }
                mPrefabs.RemoveAt(index);
                if (mPrefabs.Count == 0)
                {
                    SetSelection(-1);
                }
                else
                {
                    index = Mathf.Clamp(index, 0, mPrefabs.Count - 1);
                    SetSelection(index);
                }
            }
        }

        public void OnActivate()
        {
            if (mPrefabs.Count > 1)
            {
                SetSelection(1);
            }
        }

        public void OnDeactivate()
        {
            mPrefab = null;
            SetSelection(-1);
        }

        public GameObject GetSelectedPrefab()
        {
            if (mSelectedPrefab >= 0 && mSelectedPrefab < mPrefabs.Count)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[mSelectedPrefab].prefabPath);
                return prefab;
            }
            return null;
        }

        public object GetSelectedCustomParameter()
        {
            if (mSelectedPrefab >= 0 && mSelectedPrefab < mPrefabs.Count)
            {
                return mPrefabs[mSelectedPrefab].customParameter;
            }
            return null;
        }

        bool Contains(string prefabPath)
        {
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null && mPrefabs[i].prefabPath == prefabPath)
                {
                    return true;
                }
            }
            return false;
        }

        public void Draw(PrefabGroupDisplayFlag displayFlags)
        {
            EditorGUILayout.BeginVertical("GroupBox");
            if (mAddPrefabSet == false && displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowColor))
            {
                mColor = EditorUtils.Color32Field("Color", "组的颜色,可以导入一张颜色表来填充Front Layer,其每个格子填上相同颜色组中的一个随机prefab", mColor);
            }

            if (displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowGridSize))
            {
                var newSize = Mathf.Max(0, EditorGUILayout.FloatField("Grid Alignment Size", mGridSize));
                if (!Mathf.Approximately(newSize, mGridSize))
                {
                    SetGridSize(newSize);
                }
            }

            EditorGUILayout.BeginHorizontal();
            var prefab = EditorGUILayout.ObjectField(new GUIContent("Prefab", "模型"), mPrefab, typeof(GameObject), false, null) as GameObject;
            if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular)
            {
                mPrefab = prefab;
            }
            if (GUILayout.Button(new GUIContent("Add", "添加一个新的模型")))
            {
                if (mPrefab != null)
                {
                    //对于有lod的prefab来说,只能添加lod0的模型
                    int lod = Utils.GetPrefabNameLOD(AssetDatabase.GetAssetPath(mPrefab));
                    if (lod > 0)
                    {
                        EditorUtility.DisplayDialog("Error", "You can only select lod0!", "OK");
                    }
                    else
                    {
                        if (mAddPrefabSet)
                        {
                            var prefabPath = AssetDatabase.GetAssetPath(mPrefab);
                            AddPrefabSet(prefabPath, true);
                        }
                        else
                        {
                            var prefabPath = AssetDatabase.GetAssetPath(mPrefab);
                            var itemID = mPrefabManager.nextItemID;
                            AddPrefab(prefabPath, new string[0], mPrefabManager.checkTransformIdentity, true, displayFlags.HasFlag(PrefabGroupDisplayFlag.AddOnlyOnePrefab), null, new Vector2Int(1, 1), itemID);
                        }
                    }
                }
            }
            EditorGUILayout.EndHorizontal();

            if (mAddPrefabSet == false)
            {
                if (displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowAddFolderWithLODConstrain) ||
                    displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowAddFolderWithoutLODConstrain))
                {
                    EditorGUILayout.BeginHorizontal();

                    mFolder = EditorGUILayout.ObjectField(new GUIContent("Folder", "目录"), mFolder, typeof(DefaultAsset), false, null) as DefaultAsset;
                    if (GUILayout.Button(new GUIContent("Add", "添加指定目录的所有prefab到group中")))
                    {
                        if (mFolder != null)
                        {
                            string folderPath = AssetDatabase.GetAssetPath(mFolder);
                            AddPrefabsInFolder(folderPath, displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowAddFolderWithLODConstrain));
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }

            if (mAddPrefabSet)
            {
                Refresh();
            }

            if (displayFlags.HasFlag(PrefabGroupDisplayFlag.IsGridModelLayer))
            {
                if (mAddPrefabSet == false)
                {
                    EditorGUILayout.BeginHorizontal();
                    mFillMode = (TileFillMode)EditorGUILayout.EnumPopup(new GUIContent("Fill Mode", "填充Front Layer模式"), mFillMode);
                    if (GUILayout.Button(new GUIContent("Fill Random Tiles", "随机填充Front Layer,使用上述填充模式, Empty Tiles:只填充空的tile. Empty Tiles Without Collision Check: 只填充和Map Collision Layer中物体不相交的tile. All Tiles: 填充所有的tile,已有的会被删除")))
                    {
                        FillRandomTiles();
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }

            if (mAddPrefabSet)
            {
                if (!MapModule.useOnly15Prefab)
                {
                    EditorGUILayout.BeginHorizontal();
                    var index = EditorGUILayout.IntField(new GUIContent("Prefab Index", "使用15到31号的随机地块替代15号地块"), mFixedFullPrefabIndex);
                    if (index >= 0)
                    {
                        index = Mathf.Clamp(index, 15, 31);
                    }
                    if (index != mFixedFullPrefabIndex)
                    {
                        mFixedFullPrefabIndex = index;
                        var groupIndex = mPrefabManager.GetGroupIndex(this);
                        mOnSetPrefabFixedIndex(groupIndex, index);
                    }
                    EditorGUILayout.EndHorizontal();
                }

                if (mEditSubgroup)
                {
                    if (MapModule.useOnly15Prefab)
                    {
                        if (GUILayout.Button(new GUIContent("Load Variations", "在Ground Tile Maker工具里修改地表变体后点此按钮刷新")))
                        {
                            LoadVariationList();
                        }
                    }
                }
            }

            DrawPrefabList(displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowDecoration), displayFlags.HasFlag(PrefabGroupDisplayFlag.CanRemovePrefab));

            EditorGUILayout.EndVertical();
        }

        void DrawPrefabList(bool showDecoration, bool canRemovePrefab)
        {
            mRemovedPrefabIndices.Clear();

            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null)
                {
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[i].prefabPath);
                    if (prefab != null)
                    {
                        if (mPrefabs[i].previewTexture == null)
                        {
                            var previewTexture = AssetPreview.GetAssetPreview(prefab);
                            if (previewTexture != null)
                            {
                                mPrefabs[i].previewTexture = CreatePreviewTexture(previewTexture);
                            }
                        }
                        EditorGUILayout.BeginHorizontal();
                        {
                            bool selected = mSelectedPrefab == i ? true : false;
                            EditorGUIUtility.labelWidth = 20;
                            bool newSelected = EditorGUILayout.Toggle(selected);
                            EditorGUIUtility.labelWidth = 0;
                            if (newSelected != selected)
                            {
                                if (newSelected)
                                {
                                    SetSelection(i);
                                }
                            }
                        }
                        GUILayout.Label(mPrefabs[i].pathName, GUILayout.Width(200));
                        GUILayout.Label(mPrefabs[i].previewTexture);
                        if (mAddPrefabSet == false)
                        {
                            EditorGUILayout.BeginVertical();
                            if (canRemovePrefab) {
                                if (GUILayout.Button(new GUIContent("Remove", "删除prefab")))
                                {
                                    if (EditorUtility.DisplayDialog("Remove Prefab", "Are you sure ?", "Yes", "No"))
                                    {
                                        mRemovedPrefabIndices.Add(i);
                                    }
                                }
                            }
                            if (mPrefabManager.customEditFunc != null)
                            {
                                if (GUILayout.Button(new GUIContent("Edit", "")))
                                {
                                    mPrefabManager.customEditFunc(mPrefabs[i], groupID, i);
                                }
                            }
                            EditorGUILayout.EndVertical();
                        }
                        EditorGUILayout.BeginVertical();
                        if (mEditSubgroup && mAddPrefabSet)
                        {   
                            if (GUILayout.Button(new GUIContent("Edit Variation", "编辑地块变体列表,默认会读取从Ground Tile Maker中生成的变体列表,也可在此手动修改")))
                            {
                                OnClickEditSubgroup(i);
                            }
                            EditorGUILayout.BeginHorizontal();

                            EditorGUIUtility.labelWidth = 60;
                            int fixedIndex = EditorGUILayout.IntField(new GUIContent("Variation", "变体序号,-1表示使用随机变体,固定序号则使用指定的变体"), mPrefabs[i].fixedSubgroupPrefabIndex);
                            EditorGUIUtility.labelWidth = 0;
                            if (fixedIndex < 0)
                            {
                                fixedIndex = -1;
                            }
                            else
                            {
                                fixedIndex = Mathf.Clamp(fixedIndex, 0, mPrefabs[i].prefabPath.Length - 1);
                            }
                            if (fixedIndex != mPrefabs[i].fixedSubgroupPrefabIndex)
                            {
                                mPrefabs[i].fixedSubgroupPrefabIndex = fixedIndex;

                                if (mOnSetPrefabSubGroupPrefabFixedIndex != null)
                                {
                                    var groupIndex = mPrefabManager.GetGroupIndex(this);
                                    mOnSetPrefabSubGroupPrefabFixedIndex(groupIndex, i, fixedIndex);
                                }
                            }
                            EditorGUILayout.EndHorizontal();

                            if (showDecoration)
                            {
                                if (GUILayout.Button("Edit Decoration"))
                                {
                                    OnClickEditDecoration(i);
                                }
                            }                            
                        }
                        if (GUILayout.Button(new GUIContent("Find", "在Project窗口中选中prefab")))
                        {
                            var activePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[i].prefabPath);
                            Selection.activeObject = activePrefab;
                        }
                        EditorGUILayout.EndVertical();
                        EditorGUILayout.EndHorizontal();
                    }
                }
                else
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.TextField("Invalid Prefab");
                    if (mAddPrefabSet == false)
                    {
                        if (GUILayout.Button(new GUIContent("Remove", "删除prefab")))
                        {
                            mRemovedPrefabIndices.Add(i);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }

            RemovePrefabs();
        }

        void RemovePrefabs()
        {
            for (int i = mRemovedPrefabIndices.Count - 1; i >= 0; --i)
            {
                RemovePrefab(mRemovedPrefabIndices[i]);
            }
            mRemovedPrefabIndices.Clear();
        }

        public string GetPrefabPathFromItemID(int itemID)
        {
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i].id == itemID)
                {
                    return mPrefabs[i].prefabPath;
                }
            }
            return null;
        }

        public void SelectRandomIndex()
        {
            if (count > 0)
            {
                int idx = UnityEngine.Random.Range(0, count);
                SetSelection(idx);
            }
        }

        public void SetSelection(int index)
        {
            mSelectedPrefab = index;
            GameObject prefab = null;
            if (index >= 0 && mPrefabs[index] != null)
            {
                prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[index].prefabPath);
            }
            if (mOnSelectPrefab != null && prefab != null)
            {
                mOnSelectPrefab(prefab);
            }
        }

        string GetPrefabIndexPath(string prefabPath, int index)
        {
            int tileIndex;
            var prefabPathPrefix = GetPrefabPathPrefix(prefabPath, out tileIndex);
            var fullPath = string.Format("{0}{1}{2}0.prefab", prefabPathPrefix, index.ToString("D2"), MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            return fullPath;
        }

        void Refresh()
        {
            string prefabPath = null;
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null && !string.IsNullOrEmpty(mPrefabs[i].prefabPath))
                {
                    prefabPath = mPrefabs[i].prefabPath;
                    break;
                }
            }

            if (prefabPath == null)
            {
                return;
            }

            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] == null)
                {
                    string path = GetPrefabIndexPath(prefabPath, i);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    if (prefab != null)
                    {
                        var previewTexture = AssetPreview.GetAssetPreview(prefab);
                        if (previewTexture != null)
                        {
                            previewTexture = CreatePreviewTexture(previewTexture);
                        }
                        var pathName = Utils.GetPathName(path, true);
                        var item = new Item(path, pathName, new string[0], -1, previewTexture, null, new Vector2Int(1, 1), 0);
                        mPrefabs[i] = item;

                        if (mOnSetPrefab != null)
                        {
                            var groupIndex = mPrefabManager.GetGroupIndex(this);
                            mOnSetPrefab(groupIndex, i, prefab);
                        }
                    }
                }
            }
        }

        void FillRandomTiles()
        {
            if (mPrefabs.Count > 0)
            {
                List<GameObject> prefabs = new List<GameObject>();
                for (int i = 0; i < mPrefabs.Count; ++i)
                {
                    if (mPrefabs[i] != null)
                    {
                        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[i].prefabPath);
                        if (prefab != null)
                        {
                            prefabs.Add(prefab);
                        }
                    }
                }

                if (mOnFillRandomTiles != null)
                {
                    if (prefabs.Count > 0)
                    {
                        mOnFillRandomTiles(prefabs, mFillMode);
                    }
                    else
                    {
                        Debug.LogWarning("No prefab is found!");
                    }
                }
            }
        }

        public string name { get { return mName; } set { mName = value; } }
        public int count { get { return mPrefabs.Count; } }
        public GameObject selectedPrefab
        {
            get
            {
                if (mSelectedPrefab != -1)
                {
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabs[mSelectedPrefab].prefabPath);
                    return prefab;
                }
                return null;
            }
        }
        public int selectedIndex { get { return mSelectedPrefab; } }

        public List<int> GetValidPrefabIndices()
        {
            List<int> indices = new List<int>();
            if (mFixedFullPrefabIndex < 0)
            {
                if (mAddPrefabSet)
                {
                    if (mPrefabs.Count > 16)
                    {
                        //int n = Mathf.Min(MAX_PREFAB_COUNT, mPrefabs.Count);
                        int n = mPrefabs.Count;
                        for (int i = 15; i < n; ++i)
                        {
                            if (mPrefabs[i] != null && !string.IsNullOrEmpty(mPrefabs[i].prefabPath))
                            {
                                indices.Add(i);
                            }
                        }
                    }
                }
            }
            else
            {
                if (mFixedFullPrefabIndex >= 0 && mFixedFullPrefabIndex < MAX_PREFAB_COUNT)
                {
                    if (mPrefabs[mFixedFullPrefabIndex] != null && !string.IsNullOrEmpty(mPrefabs[mFixedFullPrefabIndex].prefabPath))
                    {
                        indices.Add(mFixedFullPrefabIndex);
                    }
                }
            }

            if (indices.Count == 0)
            {
                indices.Add(15);
            }
            return indices;
        }

        public GameObject GetPrefab(int index)
        {
            var path = GetPrefabPath(index);
            return AssetDatabase.LoadAssetAtPath<GameObject>(path);
        }

        public string GetPrefabPath(int index)
        {
            if (index >= 0 && index < mPrefabs.Count && mPrefabs[index] != null)
            {
                return mPrefabs[index].prefabPath;
            }
            return "";
        }

        public object GetCustomParameter(int index)
        {
            if (index >= 0 && index < mPrefabs.Count && mPrefabs[index] != null)
            {
                return mPrefabs[index].customParameter;
            }
            return "";
        }

        public string[] GetSubGroupPrefabPaths(int index)
        {
            if (index >= 0 && index < mPrefabs.Count && mPrefabs[index] != null)
            {
                return mPrefabs[index].subGroupPrefabs;
            }
            return null;
        }

        public bool isValid
        {
            get
            {
                if (mPrefabs.Count == 0)
                {
                    return false;
                }
                if (mAddPrefabSet)
                {
                    if (mPrefabs.Count < 15)
                    {
                        return false;
                    }
                    for (int i = 1; i <= 15; ++i)
                    {
                        if (mPrefabs[i] == null)
                        {
                            return false;
                        }
                    }
                    return true;
                }
                return true;
            }
        }

        public string GetRandomDecorationPrefabPath(int index)
        {
            if (index >= 0 && index < mPrefabs.Count)
            {
                return mPrefabs[index].GetRandomPrefab();
            }
            return null;
        }

        public DecorationPrefabInfo GetDecorationPrefabInfo(int index)
        {
            if (index >= 0 && index < mPrefabs.Count)
            {
                if (mPrefabs[index] == null)
                {
                    return null;
                }
                return mPrefabs[index].decorationPrefab;
            }
            return null;
        }

        public string GetRandomPrefabPath()
        {
            if (mPrefabs.Count > 0)
            {
                List<string> validPrefabPaths = new List<string>();
                for (int i = 0; i < mPrefabs.Count; ++i)
                {
                    if (mPrefabs[i] != null)
                    {
                        validPrefabPaths.Add(mPrefabs[i].prefabPath);
                    }
                }
                if (validPrefabPaths.Count > 0)
                {
                    int idx = UnityEngine.Random.Range(0, validPrefabPaths.Count);
                    return validPrefabPaths[idx];
                }
            }
            return null;
        }

        void OnClickEditSubgroup(int prefabIndex)
        {
            var dlg = EditorWindow.GetWindow<PrefabSubGroupEditor>("Edit Variation");
            dlg.minSize = new Vector2(200, 100);
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            dlg.Show(prefabIndex, mPrefabs[prefabIndex].subGroupPrefabs, OnEditSubgroupPrefab, OnChangeSubPrefabCount);
        }

        void OnClickEditDecoration(int prefabIndex)
        {
            var dlg = EditorWindow.GetWindow<GroundDecorationTileSettingDialog>("Set Ground Decorations");
            dlg.minSize = new Vector2(500, 300);
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            dlg.Show(mPrefabs[prefabIndex]);
        }

        void OnEditSubgroupPrefab(int index, int subTypeIndex, string prefabPath)
        {
            mPrefabs[index].subGroupPrefabs[subTypeIndex] = prefabPath;

            if (mOnEditSubGroupPrefab != null)
            {
                var groupIndex = mPrefabManager.GetGroupIndex(this);
                mOnEditSubGroupPrefab(groupIndex, index, subTypeIndex, prefabPath);
            }
        }

        void OnChangeSubPrefabCount(int prefabIndex, int count)
        {
            mPrefabs[prefabIndex].subGroupPrefabs = new string[count];

            if (mOnChangeSubPrefabCountCallback != null)
            {
                var groupIndex = mPrefabManager.GetGroupIndex(this);
                mOnChangeSubPrefabCountCallback(groupIndex, prefabIndex, count);
            }
        }

        public void LoadVariationList()
        {
            if (mPrefabs.Count > 0 && mPrefabs[0] != null)
            {
                string folder = Utils.GetFolderPath(mPrefabs[0].prefabPath) + "/" + MapCoreDef.GROUND_TILE_VARIATION_FILE_NAME;
                var stream = MapModuleResourceMgr.LoadTextStream(folder, true);
                if (stream != null)
                {
                    BinaryReader reader = new BinaryReader(stream);

                    int version = reader.ReadInt32();

                    for (int i = 0; i < 16; ++i)
                    {
                        int variationCount = reader.ReadInt32();
                        //index 0 is for base tile
                        SetVariationPrefabCount(i, variationCount);
                        for (int v = 0; v < variationCount; ++v)
                        {
                            string prefabName = Utils.ReadString(reader);
                            if (i < mPrefabs.Count)
                            {
                                string prefabPath = Utils.GetFolderPath(mPrefabs[i].prefabPath) + "/" + prefabName + ".prefab";
                                SetVariationPrefab(i, v, prefabPath);
                            }
                        }
                    }

                    reader.Close();
                }
            }
        }

        void SetVariationPrefabCount(int index, int count)
        {
            if (index >= 0 && index < mPrefabs.Count && mPrefabs[index] != null)
            {
                string[] newPaths = new string[count];
                if (mPrefabs[index].subGroupPrefabs.Length > 0)
                {
                    newPaths[0] = mPrefabs[index].subGroupPrefabs[0];
                    mPrefabs[index].subGroupPrefabs = newPaths;
                    mPrefabs[index].fixedSubgroupPrefabIndex = -1;
                    OnChangeSubPrefabCount(index, count);
                }
            }
        }

        void SetVariationPrefab(int index, int variationIndex, string prefabPath)
        {
            if (index < mPrefabs.Count && mPrefabs[index].subGroupPrefabs.Length > 0)
            {
                if (MapModuleResourceMgr.Exists(prefabPath))
                {
                    mPrefabs[index].subGroupPrefabs[variationIndex] = prefabPath;
                }
                else
                {
                    mPrefabs[index].subGroupPrefabs[variationIndex] = "";
                }
                OnEditSubgroupPrefab(index, variationIndex, prefabPath);
            }
        }

        public string FindPrefabPath(string prefabName)
        {
            for (int i = 0; i < mPrefabs.Count; ++i)
            {
                if (mPrefabs[i] != null)
                {
                    string n = Utils.GetPathName(mPrefabs[i].pathName, false);
                    if (n == prefabName)
                    {
                        return mPrefabs[i].prefabPath;
                    }
                }
            }
            return null;
        }

        public int GetFixedSubgroupPrefabIndex(int index)
        {
            if (index >= 0 && index < mPrefabs.Count)
            {
                if (mPrefabs[index] != null)
                {
                    return mPrefabs[index].fixedSubgroupPrefabIndex;
                }
            }
            return 0;
        }

        public Item GetItem(int index)
        {
            if (index >= 0 && index < mPrefabs.Count)
            {
                return mPrefabs[index];
            }
            return null;
        }

        class SortByNumber : IComparer<string>
        {
            public int Compare(string a, string b)
            {
                int n1 = MapCoreDef.GetNumberFromPath(a);
                int n2 = MapCoreDef.GetNumberFromPath(b);
                return n1 - n2;
            }
        }

        //取路径中的id来排序
        void SortPrefabsByID(List<string> paths)
        {
            paths.Sort(new SortByNumber());
        }

        void AddPrefabsInFolder(string folderPath, bool checkLODName)
        {
            List<string> paths = new List<string>();
            var enumerator = Directory.EnumerateFiles(folderPath, "*.prefab", SearchOption.AllDirectories);
            foreach (var prefabPath in enumerator)
            {
                //对于有lod的prefab来说,只能添加lod0的模型
                int lod = Utils.GetPrefabNameLOD(prefabPath);
                if (!checkLODName || lod == 0)
                {
                    var path = prefabPath.Replace('\\', '/');
                    paths.Add(path);
                }
            }

            SortPrefabsByID(paths);

            for (int i = 0; i < paths.Count; ++i)
            {
                AddPrefab(paths[i], new string[0], mPrefabManager.checkTransformIdentity, false, false, null, new Vector2Int(1, 1), mPrefabManager.nextItemID);
            }
        }

        void SetGridSize(float size)
        {
            mGridSize = size;
            if (mOnGridSizeChange != null)
            {
                mOnGridSizeChange(mPrefabManager.GetGroupIndex(this), size);
            }
        }

        public Color32 color { get { return mColor; } set { mColor = value; } }
        public int groupID { get { return mGroupID; } }
        public bool addPrefabSet { get { return mAddPrefabSet; } }
        public float gridSize { get { return mGridSize; } }

        //地表配套的装饰物prefab
        List<DecorationPrefabInfo> mDecorationPrefabs = new List<DecorationPrefabInfo>();
        PrefabManager mPrefabManager;
        List<Item> mPrefabs = new List<Item>();
        List<int> mRemovedPrefabIndices = new List<int>();
        string mName;
        GameObject mPrefab;
        DefaultAsset mFolder;
        int mSelectedPrefab = -1;
        Action<GameObject> mOnSelectPrefab;
        Action<int, GameObject> mOnAddPrefab;
        Action<int, int, GameObject> mOnSetPrefab;
        Action<int, int, string> mOnRemovePrefab;
        Action<int, float> mOnGridSizeChange;
        Action<List<GameObject>, TileFillMode> mOnFillRandomTiles;
        Action<int, int> mOnSetPrefabFixedIndex;
        Color32 mColor;
        //用在object placement editor中,记录grid alignment的值
        float mGridSize = 0;
        bool mAddPrefabSet = false;
        bool mAllowDuplication = false;
        int mFixedFullPrefabIndex = -1;
        int mGroupID;
        TileFillMode mFillMode = TileFillMode.kEmptyTilesWithoutCollisionCheck;
        const int MAX_PREFAB_COUNT = 32;
        bool mEditSubgroup = false;
        Action<int, int, int, string> mOnEditSubGroupPrefab;
        Action<int, int, int> mOnChangeSubPrefabCountCallback;
        Action<int, int, int> mOnSetPrefabSubGroupPrefabFixedIndex;
    }
}

#endif