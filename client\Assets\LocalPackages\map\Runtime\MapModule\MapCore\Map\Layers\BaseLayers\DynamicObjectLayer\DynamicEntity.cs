﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class DynamicEntity : BaseObject, IDynamicObjectData
    {
        public DynamicEntity(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, int modelTemplateID) : base(id, map, (ObjectFlag)flag)
        {
            mModelTemplateID = modelTemplateID;

            if (mModelTemplateID != 0)
            {
                var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
                mBounds = Utils.TransformRect(modelTemplate.bounds, position, scale, rotation);
            }

            mPosition = position;
            mRotation = rotation;
            mScale = scale;
        }

        public override void OnDestroy()
        {
        }

        public int GetEntityID()
        {
            return id;
        }

        public void SetPosition(Vector3 pos)
        {
            mPosition = pos;
        }
        public Vector3 GetPosition()
        {
            return mPosition;
        }
        public void SetRotation(Quaternion rot)
        {
            mRotation = rot;
        }
        public Quaternion GetRotation()
        {
            return mRotation;
        }
        public void SetScale(Vector3 scale)
        {
            mScale = scale;
        }
        public Vector3 GetScale()
        {
            return mScale;
        }

        public bool IsObjActive()
        {
            return mIsActive;
        }
        public bool SetObjActive(bool active)
        {
            if (mIsActive != active)
            {
                mIsActive = active;
                return true;
            }
            return false;
        }

        public int GetModelTemplateID() { return mModelTemplateID; }
        public string GetAssetPath(int lod)
        {
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                return modelTemplate.GetLODPrefabPath(lod);
            }
            return "";
        }

        public Rect GetBounds()
        {
            return mBounds;
        }

        public int GetGridIndex()
        {
            return mGridIndex;
        }
        public void SetGridIndex(int index)
        {
            mGridIndex = index;
        }

        int mModelTemplateID;
        //属于哪个格子
        int mGridIndex;
        Rect mBounds;
        Vector3 mPosition;
        Vector3 mScale;
        Quaternion mRotation;
        bool mIsActive = false;
    }
}
