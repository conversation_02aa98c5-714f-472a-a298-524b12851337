﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //按格子大小来缩放prefab
    public class KeepScaleByGrid : KeepScale
    {
        //美术资源表示的大小,主要用于城建
        public float objectResourceRadius = 0;
        //物体在游戏中实际占地的格子数量,主要用于城建
        public float objectGridCount = 0;
        //物体在缩放到最大时在游戏中的占地大小
        public float objectMaxSizeInGameRadius = 0;

        protected float mObjectMinScale;
        //物体的最大scale,用于计算填满整个主城时物体的缩放
        protected float mObjectMaxScale;

        protected override void SetInitScale()
        {
            var config = GetScaleConfig();
            if (Map.currentMap != null && config != null)
            {
                var camera = Map.currentMap.camera;
                if (camera != null)
                {
                    if (mPrefabInitScale == 0)
                    {
                        mPrefabInitScale = gameObject.transform.localScale.x;
                        mPrefabInitScaleConst = mPrefabInitScale;
                        if (objectResourceRadius != 0 && objectGridCount != 0)
                        {
                            float objectInGameRadius = objectGridCount * GetGridSize() * 0.5f;
                            mPrefabInitScale *= (objectInGameRadius / objectResourceRadius);
                        }

                        GetObjectFinalScale();
                    }

                    float cameraHeight = camera.transform.position.y;
                    cameraHeight = Mathf.Clamp(cameraHeight, config.minimumCameraHeight, config.maximumCameraHeight);
                    mLastCameraHeight = cameraHeight;
                    SetScaleAtHeight(cameraHeight);
                }
            }
        }

        protected float GetScaleAtHeight(float cameraHeight)
        {
            var config = GetScaleConfig();
            //calculate camera height ratio
            float deltaHeight = config.maximumCameraHeight - config.minimumCameraHeight;
            if (deltaHeight == 0)
            {
                return transform.localScale.x;
            }
            float t = (cameraHeight - config.minimumCameraHeight) / deltaHeight;
            t = Mathf.Clamp(t, 0, 1.0f);

            float maxScale = mObjectMaxScale;
            if (objectMaxSizeInGameRadius != 0)
            {
                float objectInGameRadius = objectGridCount * GetGridSize() * 0.5f;
                maxScale = mObjectMaxScale * (objectMaxSizeInGameRadius / (mObjectMaxScale / mObjectMinScale * objectInGameRadius));
            }

            float scaleAtCameraHeight = Mathf.Lerp(mObjectMinScale, maxScale, t);
            return scaleAtCameraHeight;
        }

        protected override void SetScaleAtHeight(float cameraHeight)
        {
            float factor = GetScaleAtHeight(cameraHeight);
            transform.localScale = Vector3.one * factor;
        }

        protected virtual void GetObjectFinalScale()
        {
            var config = GetScaleConfig();
            mObjectMinScale = CalculateObjectScaleAtHeight(mPrefabInitScale, config.minimumCameraHeight);
            mObjectMaxScale = CalculateObjectScaleAtHeight(mPrefabInitScale, config.maximumCameraHeight);
        }

        protected override KeepScaleConfig GetScaleConfig()
        {
            return MapCameraMgr.buildingScaleConfig;
        }

#if false
        //用于编辑器时计算scale
        public float CalculateScaleAtHeight(float cameraHeight)
        {
            var config = GetScaleConfig();
            //calculate camera height ratio
            float deltaHeight = config.maximumCameraHeight - config.minimumCameraHeight;
            if (deltaHeight == 0)
            {
                return transform.localScale.x;
            }
            float t = (cameraHeight - config.minimumCameraHeight) / deltaHeight;
            t = Mathf.Clamp(t, 0, 1.0f);

            float prefabInitScale = gameObject.transform.localScale.x;
            float objectMinScale = CalculateObjectScaleAtHeight(prefabInitScale, config.minimumCameraHeight);
            float objectMaxScale = CalculateObjectScaleAtHeight(prefabInitScale, config.maximumCameraHeight);

            if (objectMaxSizeInGameRadius != 0)
            {
                float objectInGameRadius = objectGridCount * GetGridSize() * 0.5f;
                objectMaxScale = objectMaxScale * (objectMaxSizeInGameRadius / (objectMaxScale / objectMinScale * objectInGameRadius));
            }

            float scaleAtCameraHeight = Mathf.Lerp(objectMinScale, objectMaxScale, t);
            return scaleAtCameraHeight;
        }
#endif
        protected virtual float GetGridSize() { return 1.0f; }
    }
}
