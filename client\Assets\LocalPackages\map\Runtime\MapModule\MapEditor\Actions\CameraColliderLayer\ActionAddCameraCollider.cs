﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionAddCameraCollider : EditorAction
    {
        public ActionAddCameraCollider(int layerID, int dataID, List<Vector3> vertices)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mVertices = new List<Vector3>(vertices.Count);
            mVertices.AddRange(vertices);
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            var data = new CameraColliderData(mDataID, Map.currentMap, mVertices, null, layer.displayVertexRadius, 0, null, null);
            layer.AddObject(data);

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        List<Vector3> mVertices;
    }
}

#endif