﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using TFW;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(CircleBorderLayerLogic))]
    public class CircleBorderLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as CircleBorderLayerLogic;

            mLogic.UpdateGizmoVisibilityState();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetLayerBoundsVisible(false);
                mLogic.SetGridVisible(false);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.circleBorderLayerPrefabManager;
                prefabManager.Draw(PrefabGroupDisplayFlag.ShowColor);

                var layerID = mLogic.layerID;
                var layerData = map.FindObject(layerID) as CircleBorderLayerData;
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, null, null);

                mLogic.sliceCount = EditorGUILayout.IntField("Slice Count", mLogic.sliceCount);
                mLogic.sliceCount = Mathf.Clamp(mLogic.sliceCount, 2, 10000);

                layerData.combineBorderLOD = EditorGUILayout.IntField("Start LOD When Use Full Circle Prefab", layerData.combineBorderLOD);
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Create Borders"))
                {
                    CreateBorders();
                }
                if (GUILayout.Button("Remove Borders"))
                {
                    RemoveBorders();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Object Count", layerData.objectCount.ToString());
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void CreateBorders()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLogic.layerID) as CircleBorderLayer;
            var pos = new Vector3(map.mapWidth * 0.5f, 0, map.mapHeight * 0.5f);
            var editorMapData = map.data as EditorMapData;

            List<string> prefabPaths = new List<string>();
            for (int i = 0; i < mLogic.sliceCount; ++i)
            {
                var path = editorMapData.circleBorderLayerPrefabManager.GetRandomPrefabInCurrentGroup();
                if (string.IsNullOrEmpty(path))
                {
                    EditorUtility.DisplayDialog("Error", "No border prefabs found!", "OK");
                    return;
                }
                prefabPaths.Add(path);
            }

            RemoveBorders();
            MapCircleBorderCreator.Create(prefabPaths, pos, layer);
        }

        void RemoveBorders()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as CircleBorderLayer;
            layer.RemoveAllObjects();
        }

        CircleBorderLayerLogic mLogic;
    }
}

#endif