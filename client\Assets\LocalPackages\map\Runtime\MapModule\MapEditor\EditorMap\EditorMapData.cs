﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;
using TFW.Map.config;

namespace TFW.Map
{
    public class RuinSetting
    {
        public RuinSetting(string name, Color color, Material mtl, bool alwaysDisplay, PropertyDatas properties, float colliderRadius, GameObject prefab, config.RuinSpecialRegionSetting setting)
        {
            Debug.Assert(properties != null);
            this.name = name;
            this.color = color;
            this.mtl = mtl;
            this.alwaysDisplay = alwaysDisplay;
            this.properties = properties;
            this.colliderRadius = colliderRadius;
            this.prefab = prefab;
            bigGridHeight = setting.bigGridHeight;
            bigGridWidth = setting.bigGridWidth;
            startX = setting.startX;
            startZ = setting.startZ;
            horizontalBigGridCount = setting.horizontalBigGridCount;
            verticalBigGridCount = setting.verticalBigGridCount;
            pointCount = setting.pointCount;
            invalidCircles = setting.invalidCircles;
        }

        public string name;
        public Color color;
        public Material mtl;
        public bool alwaysDisplay = false;
        public PropertyDatas properties;
        public float colliderRadius;
        public GameObject prefab;

        public float bigGridWidth;
        public float bigGridHeight;
        public float startX;
        public float startZ;
        public int horizontalBigGridCount;
        public int verticalBigGridCount;
        public int pointCount;
        //不能在哪些圈中生成
        public List<int> invalidCircles = new List<int>();
    }

    public class LoadRangeData
    {
        public LoadRangeData(float cameraFov, Vector2 screenResolution, float cameraRotationX, float cameraHeight, Vector2 loadRangeScale)
        {
            this.cameraFov = cameraFov;
            this.resolution = screenResolution;
            this.cameraRotationX = cameraRotationX;
            this.cameraHeight = cameraHeight;
            this.loadRangeScale = loadRangeScale;
        }

        public float cameraFov = 30;
        public Vector2 resolution;
        public float cameraRotationX = 45;
        public float cameraHeight = 50;
        public Vector2 loadRangeScale = Vector2.one;
    }

    //编辑器使用的地图数据
    public class EditorMapData : MapData
    {
        public EditorMapData(Map map, Vector3 viewCenter, float viewWidth, float viewHeight, float width, float height, float borderHeight, List<ModelTemplate> modelTemplates, Vector2 cameraHeightRange, MapLODManager lodManager, MapLocalObstacleManager obstacleManager, MapGlobalObstacleManager globalObstacleManager, bool isCircleMap, float groundTileSize, float frontTileSize, Version version, float backExtendedSize, float farClipOffset, List<SpriteTemplate> spriteTemplates, DetailSpritesSetting ds, GridRegionEditor regionEditor, LoadRangeData loadRangeData, config.BackgroundSetting backgroundSetting, Bounds mapDataGenerationRange, float maxCameraColliderHeight) : base(map, viewCenter, viewWidth, viewHeight, width, height, borderHeight, modelTemplates, cameraHeightRange, lodManager, obstacleManager, globalObstacleManager, null, null, isCircleMap, version, backExtendedSize, farClipOffset, false, groundTileSize, frontTileSize, mapDataGenerationRange, new List<string>(), maxCameraColliderHeight)
        {
            foreach (var temp in spriteTemplates)
            {
                AddSpriteTemplate(temp);
            }
            mMapLODSetting = new EditorMapLODSetting();
            mDetailSpriteSetting = ds;
            mGridRegionEditor = regionEditor;
            mSplineObjectManager = new SplineObjectManager(new MapLayerLODConfig(map), 10.0f);
            mLoadRangeData = loadRangeData;
            mBackgroundTexturePath = backgroundSetting.texturePath;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            mModelPropertyManager = null;
        }

        public void AddSpriteTemplate(SpriteTemplate temp)
        {
            mSpriteTemplates.AddSpriteTemplate(temp);
        }

        public void RemoveSpriteTemplate(int id)
        {
            mSpriteTemplates.RemoveSpriteTemplate(id);
        }

        public int GetSpriteTemplateCount()
        {
            return mSpriteTemplates.spriteTemplates.Count;
        }

        public SpriteTemplate GetSpriteTemplate(int index)
        {
            return mSpriteTemplates.GetSpriteTemplate(index);
        }

        public int GetSpriteTemplateIndex(int spriteTemplateID)
        {
            return mSpriteTemplates.GetSpriteTemplateIndex(spriteTemplateID);
        }

        public SpriteTemplate FindSpriteTemplate(string name)
        {
            return mSpriteTemplates.FindSpriteTemplate(name);
        }

        public override void Resize(float newWidth, float newHeight)
        {
            base.Resize(newWidth, newHeight);
            //mSplineObjectManager.Resize(newWidth, newHeight);
            //mGridRegionEditor.Resize(newWidth, newHeight);
            //Debug.LogError("todo resize");
        }

        public PropertySetManager propertySets { get { return mPropertySets; } }
        public SpriteTemplateManager spriteTemplates { get { return mSpriteTemplates; } }
        public ModelTemplatePropertyManager modelPropertyManager { get { return mModelPropertyManager; } }
        public PrefabManager gridModelLayerPrefabManager { get { return mGridModelLayerPrefabManager; } }
        public PrefabManager modelLayerPrefabManager { get { return mModelLayerPrefabManager; } }
        public PrefabManager circleBorderLayerPrefabManager { get { return mCircleBorderLayerPrefabManager; } }
        public PrefabManager complexGridModelLayerPrefabManager { get { return mComplexGridModelLayerPrefabManager; } }
        public PrefabManager editorTerrainPrefabManager { get { return mEditorTerrainPrefabManager; } }
        public PrefabManager editorVaryingTileSizeTerrainPrefabManager { get { return mEditorVaryingTileSizeTerrainPrefabManager; } }
        public PrefabManager editorSplitFogPrefabManager { get { return mEditorSplitFogPrefabManager; } }
        public EditorMapLODSetting mapLODSetting { get { return mMapLODSetting; } }
        public DetailSpritesSetting detailSpritesSetting { get { return mDetailSpriteSetting; } set { mDetailSpriteSetting = value; } }
        public GridRegionEditor gridRegionEditor { get { return mGridRegionEditor; } set { mGridRegionEditor = value; } }
        public LoadRangeData loadRangeData { get { return mLoadRangeData; } }
        public string backgroundTexturePath { get { return mBackgroundTexturePath; } set { mBackgroundTexturePath = value; } }
        public SplineObjectManager splineObjectManager
        {
            get { return mSplineObjectManager; }
            set
            {
                if (mSplineObjectManager != null)
                {
                    mSplineObjectManager.OnDestroy();
                }
                mSplineObjectManager = value;
            }
        }

        //sprite的配置管理器
        SpriteTemplateManager mSpriteTemplates = new SpriteTemplateManager();
        //属性的配置管理器
        PropertySetManager mPropertySets = new PropertySetManager();
        //地图物体的属性管理器
        ModelTemplatePropertyManager mModelPropertyManager = new ModelTemplatePropertyManager();
        //prefab分类管理器
        PrefabManager mGridModelLayerPrefabManager = new PrefabManager(false, false, false, false, true, null, null);
        PrefabManager mModelLayerPrefabManager = new PrefabManager(false, false, false, false, true, null, null);
        PrefabManager mCircleBorderLayerPrefabManager = new PrefabManager(false, false, false, false, true, null, null);
        PrefabManager mComplexGridModelLayerPrefabManager = new PrefabManager(false, false, false, false, true, null, null);
        PrefabManager mEditorTerrainPrefabManager = new PrefabManager(false, true, true, false, true, null, null);
        PrefabManager mEditorVaryingTileSizeTerrainPrefabManager = new PrefabManager(false, true, true, false, true, null, null);
        PrefabManager mEditorSplitFogPrefabManager = new PrefabManager(false, true, false, true, true, null, null);
        //map lod setting
        EditorMapLODSetting mMapLODSetting;
        DetailSpritesSetting mDetailSpriteSetting;
        GridRegionEditor mGridRegionEditor;
        SplineObjectManager mSplineObjectManager;
        LoadRangeData mLoadRangeData;
        string mBackgroundTexturePath;
    }
}


#endif