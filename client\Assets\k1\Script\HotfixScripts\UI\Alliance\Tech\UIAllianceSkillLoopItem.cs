﻿using Game.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using TFW.Localization;
using TFW.UI;

namespace UI
{
    public class UIAllianceSkillLoopItem: TFWLoopListViewItem
    {
        public TFWText mTitle;

        public UIGrid techGrid;

        public void SetData(List<int> tech)
        {
            techGrid.Clear();
            for (int i = 0; i < tech.Count; i++) 
            {
                int groupId = tech[i];
                if (GameData.I.AllianceTechData.techGroupLevelData.TryGetValue(groupId, out var level))
                { 
                    var techId = GetTechIdByGroupId(groupId, level);
                    AllianceTechData itemData = GameData.I.AllianceTechData.GetTechData(techId);

                    if (i == 0)
                        mTitle.text = LocalizationMgr.Format("Union_Tech_Level", itemData.techCfg.Unlock);

                    techGrid.AddItem<UIAllianceSkillGridItem>().SetTechData(itemData);
                }
            }
        }

        public int GetTechIdByGroupId(int groupId, int level)
        {
            var techIdStr = string.Format("{0}{1}", groupId, level.ToString("00"));
            return int.Parse(techIdStr);
        }
    }
}
