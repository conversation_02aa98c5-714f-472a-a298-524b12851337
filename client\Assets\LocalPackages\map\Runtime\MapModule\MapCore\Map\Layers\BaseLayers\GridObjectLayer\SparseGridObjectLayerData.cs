﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public interface IGridObjectLayerData
    {
        IMapObjectData GetObjectData(int x, int y);
    }

    //使用稀疏格子来管理地图对象的数据
    public class SparseGridObjectLayerData : MapObjectLayerData, IGridObjectLayerData
    {
        public SparseGridObjectLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ModelLODGroupManager groupManager)
            : base(header, config, map, groupManager)
        {
            mLastViewport = new Rect(-10000, -10000, 0, 0);
        }

        //清理地图数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            int n = mRows * mCols;
            foreach (var obj in mGridObjects)
            {
                var objectData = obj.Value;
                if (objectData != null)
                {
                    map.DestroyObject(objectData.GetEntityID());
                }
            }
            mGridObjects = null;
        }

        //从[x,y]到[x+width-1, y+height-1]范围内的格子是否都没有被占据
        public bool IsEmpty(int x, int y, int width, int height)
        {
            var maxX = x + width - 1;
            var maxY = y + height - 1;
            if (x < 0 || maxX >= mCols || y < 0 || maxY >= mRows || width <= 0 || height <= 0)
            {
                return false;
            }

            for (int i = y; i <= maxY; ++i)
            {
                for (int j = x; j <= maxX; ++j)
                {
                    int idx = i * mCols + j;
                    var coord = new Vector2Int(j, i);
                    if (mGridObjects.ContainsKey(coord))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        protected override bool IsAbleToAdd(IMapObjectData objectData)
        {
            var pos = objectData.GetPosition();
            var coord = FromWorldPositionToCoordinate(pos);
            if (IsEmpty(coord.x, coord.y, 1, 1))
            {
                return true;
            }
            return false;
        }

        //添加一个object
        protected override void OnAddObjectData(IMapObjectData data)
        {
            var pos = data.GetPosition();
            var coord = FromWorldPositionToCoordinate(pos);
            AddObjectDataInternal(data, coord.x, coord.y);
            bool isVisible = false;
            if (!isLoading)
            {
                isVisible = IsInViewRange(data);
            }
            SetObjectActiveImpl(data, isVisible);
        }

        //删除地图对象的数据
        protected override void OnRemoveObjectData(IMapObjectData data)
        {
            var coord = GetObjectCoordinate(data);
            RemoveObjectDataInternal(data, coord.x, coord.y);
        }

        //返回地图对象的数据
        public IMapObjectData GetObjectData(int x, int y)
        {
            IMapObjectData obj;
            mGridObjects.TryGetValue(new Vector2Int(x, y), out obj);
            return obj;
        }

        void AddObjectDataInternal(IMapObjectData data, int x, int y)
        {
            var maxX = x;
            var maxY = y;

            Debug.Assert(x >= 0 && maxX < mCols && y >= 0 && maxY < mRows);

            for (int i = y; i <= maxY; ++i)
            {
                for (int j = x; j <= maxX; ++j)
                {
                    int idx = i * mCols + j;
                    var coord = new Vector2Int(j, i);
                    mGridObjects[coord] = data;
                }
            }
        }

        void RemoveObjectDataInternal(IMapObjectData data, int x, int y)
        {
            int maxX = x;
            int maxY = y;
            Debug.Assert(x >= 0 && maxX < mCols && y >= 0 && maxY < mRows);

            for (int i = y; i <= maxY; ++i)
            {
                for (int j = x; j <= maxX; ++j)
                {
                    int idx = i * mCols + j;
                    var coord = new Vector2Int(j, i);
                    mGridObjects.Remove(coord);
                }
            }
        }

        //设置[x,y]到[x+width-1, y+height-1]范围内物体的可见性
        protected void SetObjectsActiveStateInRange(int x, int y, int width, int height, bool active)
        {
            int minX = x;
            int minY = y;
            int maxX = x + width - 1;
            int maxY = y + height - 1;

            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    if (j >= 0 && j < mCols && i >= 0 && i < mRows)
                    {
                        var data = GetObjectData(j, i);
                        if (data != null)
                        {
                            SetObjectActive(data, active, currentLOD);
                        }
                    }
                }
            }
        }

        public Vector2Int GetObjectCoordinate(IMapObjectData data)
        {
            var pos = data.GetPosition();
            return FromWorldPositionToCoordinate(pos);
        }

        public bool IsInViewRange(IMapObjectData data, Rect viewport)
        {
            var coord = GetObjectCoordinate(data);
            var rect = GetViewRect(viewport);
            return rect.Contains(coord.x, coord.y);
        }

        //地图对象是否在视野中
        public bool IsInViewRange(IMapObjectData data)
        {
            return IsInViewRange(data, map.viewport);
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(map.viewport, 0);
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastCameraZoom))
            {
                mLastCameraZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            var oldViewRect = GetViewRect(mLastViewport);
            var newViewRect = GetViewRect(newViewport);

            UpdateViewRect(oldViewRect, newViewRect);

            mLastViewport = newViewport;

            return lodChanged;
        }

        void UpdateViewRect(Rect2D oldViewRect, Rect2D newViewRect)
        {
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            var objectData = GetObjectData(j, i);
                            if (objectData != null)
                            {
                                if (objectData.IgnoreViewport() == false)
                                {
                                    SetObjectActive(objectData, false, currentLOD);
                                }
                            }
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            var objectData = GetObjectData(j, i);
                            if (objectData != null)
                            {
                                if (objectData.IgnoreViewport() == false)
                                {
                                    SetObjectActive(objectData, true, currentLOD);
                                }
                            }
                        }
                    }
                }
            }
        }

        protected override void OnPositionChange(IMapObjectData objectData)
        {
        }

        protected override void OnScaleChange(IMapObjectData objectData)
        {
        }

        protected override void OnRotationChange(IMapObjectData objectData)
        {
        }

        public override bool isGameLayer => true;

        //当物体位置改变时,修改物体所占据的格子坐标
        public void UpdateCoordinate(IMapObjectData data, int oldX, int oldY, int newX, int newY)
        {
            //暂时没这个需求
            Debug.Assert(false, "todo");
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            throw new System.NotImplementedException();
        }

        //改变layer的大小
        public void Resize(int newSize, bool useLayerOffset)
        {
            //only valid for map editor
            Debug.Assert(map.isEditorMode == true);
            int oldSize = horizontalTileCount;
            if (newSize > oldSize)
            {
                int offset = Mathf.CeilToInt((newSize - oldSize) / 2.0f);
                Dictionary<Vector2Int, IMapObjectData> newTiles = new Dictionary<Vector2Int, IMapObjectData>();
                for (int i = 0; i < oldSize; ++i)
                {
                    for (int j = 0; j < oldSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        int oldIndex = i * oldSize + j;
                        int newIndex = (i + offset) * newSize + j + offset;
                        newTiles.Add(new Vector2Int(j + offset, i + offset), GetObjectData(j, i));
                    }
                }
                mGridObjects = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }
            else
            {
                int offset = (oldSize - newSize) / 2;
                Dictionary<Vector2Int, IMapObjectData> newTiles = new Dictionary<Vector2Int, IMapObjectData>();
                for (int i = 0; i < newSize; ++i)
                {
                    for (int j = 0; j < newSize; ++j)
                    {
                        //只是简单的移动tile,并不新增加tile
                        newTiles.Add(new Vector2Int(j, i), GetObjectData(j + offset, i + offset));
                    }
                }
                mGridObjects = newTiles;
                mRows = newSize;
                mCols = newSize;
                var mapWidth = map.mapWidth;
                var mapHeight = map.mapHeight;
                var layerWidth = newSize * mTileWidth;
                var layerHeight = newSize * mTileHeight;
                var ox = (mapWidth - layerWidth) * 0.5f;
                var oz = (mapHeight - layerHeight) * 0.5f;
                mLayerOrigin = new Vector3(ox, 0, oz);
            }

            if (!useLayerOffset)
            {
                mLayerOrigin = Vector3.zero;
            }
        }

        public override void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects)
        {
            Debug.Assert(false, "todo");
        }

        internal Dictionary<Vector2Int, IMapObjectData> gridObjects { get { return mGridObjects; } }
        //地图层上对象的数据
        Dictionary<Vector2Int, IMapObjectData> mGridObjects = new Dictionary<Vector2Int, IMapObjectData>();
        Rect mLastViewport;
        float mLastCameraZoom;
    };
}