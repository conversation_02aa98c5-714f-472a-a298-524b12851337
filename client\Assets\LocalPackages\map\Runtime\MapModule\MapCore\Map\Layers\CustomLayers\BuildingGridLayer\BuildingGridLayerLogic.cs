﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public class BuildingGridLayerLogic : MapLayerLogic
    {
        public int brushSize = 1;
        public int selectedRegionIndex = -1;
        public int selectedLayerIndex = 0;

        public BuildingGridLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as BuildingGridLayer;
                return layer;
            }
        }
    }
}


#endif