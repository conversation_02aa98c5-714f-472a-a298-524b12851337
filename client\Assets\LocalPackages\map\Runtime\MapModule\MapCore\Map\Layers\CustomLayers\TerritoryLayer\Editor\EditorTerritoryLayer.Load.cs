﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class EditorTerritoryLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, version);

            reader.Close();

            var layer = new EditorTerritoryLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.EditorTerritoryLayerData LoadLayerData(BinaryReader reader, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();

            int subLayerCount = 1;
            if (version.minorVersion >= 2)
            {
                subLayerCount = reader.ReadInt32();
            }

            config.EditorTerritorySubLayerData[] subLayers = new config.EditorTerritorySubLayerData[subLayerCount];
            for (int s = 0; s < subLayerCount; ++s)
            {
                string name = $"layer {s}";
                bool subLayerActive = true;
                if (version.minorVersion >= 2)
                {
                    name = Utils.ReadString(reader);
                    subLayerActive = reader.ReadBoolean();
                }
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                float tileWidth = reader.ReadSingle();
                float tileHeight = reader.ReadSingle();
                float displayRadius = reader.ReadSingle();

                int n = reader.ReadInt32();
                config.TerritoryMeshGenerationParam[] meshGenerationParams = new config.TerritoryMeshGenerationParam[n];
                for (int i = 0; i < n; ++i)
                {
                    meshGenerationParams[i] = new config.TerritoryMeshGenerationParam();
                    meshGenerationParams[i].cornerSegment = reader.ReadInt32();
                    meshGenerationParams[i].borderSizeRatio = reader.ReadSingle();
                    meshGenerationParams[i].uvScale = reader.ReadSingle();
                    meshGenerationParams[i].curveCorner = reader.ReadBoolean();
                    meshGenerationParams[i].territoryMeshMaterialGuid = Utils.ReadString(reader);
                }

                var grids = new int[rows, cols];
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        grids[i, j] = reader.ReadInt32();
                    }
                }
                int territoryCount = reader.ReadInt32();
                config.EditorCityTerritoryData[] territories = new config.EditorCityTerritoryData[territoryCount];
                for (int i = 0; i < territoryCount; ++i)
                {
                    string territoryName = Utils.ReadString(reader);
                    Color color = Utils.ReadColor(reader);
                    int id = reader.ReadInt32();
                    int buildingCount = reader.ReadInt32();
                    Vector3[] buildingPositions = new Vector3[buildingCount];
                    for (int b = 0; b < buildingCount; ++b)
                    {
                        buildingPositions[b] = Utils.ReadVector3(reader);
                    }
                    var properties = Utils.ReadProperties(reader);
                    territories[i] = new config.EditorCityTerritoryData(id, territoryName, color, properties, buildingPositions);
                }

                CurveRegionMeshGenerationParam curveParam = null;
                if (version.minorVersion >= 6)
                {
                    curveParam = LoadCurveRegionMeshGenerationParamMultipleLOD(reader, version.minorVersion);
                }
                else if (version.minorVersion >= 4)
                {
                    curveParam = LoadCurveRegionMeshGenerationParamSingleLOD(reader);
                }
                config.TerritorySharedEdgeInfo[] sharedEdges = new config.TerritorySharedEdgeInfo[0];
                if (version.minorVersion >= 5)
                {
                    //load edge info
                    int sharedEdgeCount = reader.ReadInt32();
                    sharedEdges = new config.TerritorySharedEdgeInfo[sharedEdgeCount];
                    for (int i = 0; i < sharedEdgeCount; ++i)
                    {
                        var edge = new config.TerritorySharedEdgeInfo();
                        edge.territoryID = reader.ReadInt32();
                        edge.neighbourTerritoryID = reader.ReadInt32();
                        edge.prefabPath = Utils.ReadString(reader);
                        string materialGuid = Utils.ReadString(reader);
                        string materialPath = AssetDatabase.GUIDToAssetPath(materialGuid);
                        edge.material = MapModuleResourceMgr.LoadMaterial(materialPath);
                        sharedEdges[i] = edge;
                    }
                }

                config.TerritoryBlockInfo[] blocks = new config.TerritoryBlockInfo[0];
                int maskTextureWidth = 0;
                int maskTextureHeight = 0;
                Color32[] maskTextureData = null;
                if (version.minorVersion >= 7)
                {
                    maskTextureWidth = reader.ReadInt32();
                    maskTextureHeight = reader.ReadInt32();
                    maskTextureData = Utils.ReadColor32Array(reader);

                    int blockCount = reader.ReadInt32();
                    blocks = new config.TerritoryBlockInfo[blockCount];
                    for (int i = 0; i < blockCount; ++i)
                    {
                        var block = new config.TerritoryBlockInfo();
                        blocks[i] = block;

                        block.prefabPath = Utils.ReadString(reader);
                        block.bounds = Utils.ReadBounds(reader);

                        int nRegions = reader.ReadInt32();
                        block.regions = new config.TerritoryBlockRegionInfo[nRegions];
                        for (int r = 0; r < nRegions; ++r)
                        {
                            var region = new config.TerritoryBlockRegionInfo();
                            region.territoryID = reader.ReadInt32();
                            block.regions[r] = region;
                        }

                        int nEdges = reader.ReadInt32();
                        block.edges = new config.TerritoryBlockEdgeInfo[nEdges];
                        for (int e = 0; e < nEdges; ++e)
                        {
                            var edge = new config.TerritoryBlockEdgeInfo();
                            edge.territoryID = reader.ReadInt32();
                            edge.neighbourTerritoryID = reader.ReadInt32();
                            block.edges[e] = edge;
                        }
                    }
                }

                string exportFileName = "ia_point_city_zone";
                if (version.minorVersion >= 3)
                {
                    exportFileName = Utils.ReadString(reader);
                }

                subLayers[s] = new config.EditorTerritorySubLayerData(name, subLayerActive, tileWidth, tileHeight, cols, rows, displayRadius, grids, territories, meshGenerationParams, exportFileName, curveParam, sharedEdges, blocks, maskTextureWidth, maskTextureHeight, maskTextureData);
            }

            var lodConfig = LoadMapLayerLODConfig(reader, version);

            var layer = new config.EditorTerritoryLayerData(Map.currentMap.nextCustomObjectID, layerName, layerOffset, lodConfig, subLayers);
            layer.active = active;
            return layer;
        }

        static CurveRegionMeshGenerationParam LoadCurveRegionMeshGenerationParamSingleLOD(BinaryReader reader)
        {
            int nLODs = reader.ReadInt32();
            Debug.Assert(nLODs == 1);
            
            var segmentLengthRatio = reader.ReadSingle();
            var minTangentLength = reader.ReadSingle();
            var maxTangentLength = reader.ReadSingle();
            var pointDeltaDistance = reader.ReadSingle();
            var maxPointCountInOneSegment = reader.ReadInt32();
            var moreRectangular = reader.ReadBoolean();
            var lineWidth = reader.ReadSingle();
            var vertexDisplayRadius = reader.ReadSingle();
            var textureAspectRatio = reader.ReadSingle();
            var segmentLengthRatioRandomRange = reader.ReadSingle();
            var tangentRotationRandomRange = reader.ReadSingle();
            var gridErrorThreshold = reader.ReadSingle();
            var useVertexColorForRegionMesh = reader.ReadBoolean();
            var edgeMaterial = MapModuleResourceMgr.LoadMaterial(AssetDatabase.GUIDToAssetPath(Utils.ReadString(reader)));
            var regionMaterial = MapModuleResourceMgr.LoadMaterial(AssetDatabase.GUIDToAssetPath(Utils.ReadString(reader)));
            CurveRegionMeshGenerationLODParam lodParam = new CurveRegionMeshGenerationLODParam(segmentLengthRatio, minTangentLength, maxTangentLength, pointDeltaDistance, maxPointCountInOneSegment, moreRectangular, lineWidth, textureAspectRatio, gridErrorThreshold, edgeMaterial, regionMaterial, useVertexColorForRegionMesh, false, false, 0, true);
            CurveRegionMeshGenerationParam param = new CurveRegionMeshGenerationParam(vertexDisplayRadius, segmentLengthRatioRandomRange, tangentRotationRandomRange, new List<CurveRegionMeshGenerationLODParam>() {lodParam });
            return param;
        }

        static CurveRegionMeshGenerationParam LoadCurveRegionMeshGenerationParamMultipleLOD(BinaryReader reader, int minorVersion)
        {
            List<CurveRegionMeshGenerationLODParam> lodParams = new List<CurveRegionMeshGenerationLODParam>();
            int nLODs = reader.ReadInt32();
            for (int i = 0; i < nLODs; ++i)
            {
                var segmentLengthRatio = reader.ReadSingle();
                var minTangentLength = reader.ReadSingle();
                var maxTangentLength = reader.ReadSingle();
                var pointDeltaDistance = reader.ReadSingle();
                var maxPointCountInOneSegment = reader.ReadInt32();
                var moreRectangular = reader.ReadBoolean();
                var lineWidth = reader.ReadSingle();
                var textureAspectRatio = reader.ReadSingle();
                var gridErrorThreshold = reader.ReadSingle();
                var useVertexColorForRegionMesh = reader.ReadBoolean();
                var combineMesh = reader.ReadBoolean();
                bool mergeEdge = true;
                if (minorVersion >= 8)
                {
                    mergeEdge = reader.ReadBoolean();
                }
                float edgeHeight = 0.5f;
                if (minorVersion >= 9)
                {
                    edgeHeight = reader.ReadSingle();
                }
                bool shareEdge = true;
                if (minorVersion >= 10)
                {
                    shareEdge = reader.ReadBoolean();
                }
                var edgeMaterial = MapModuleResourceMgr.LoadMaterial(AssetDatabase.GUIDToAssetPath(Utils.ReadString(reader)));
                var regionMaterial = MapModuleResourceMgr.LoadMaterial(AssetDatabase.GUIDToAssetPath(Utils.ReadString(reader)));
                CurveRegionMeshGenerationLODParam lodParam = new CurveRegionMeshGenerationLODParam(segmentLengthRatio, minTangentLength, maxTangentLength, pointDeltaDistance, maxPointCountInOneSegment, moreRectangular, lineWidth, textureAspectRatio, gridErrorThreshold, edgeMaterial, regionMaterial, useVertexColorForRegionMesh, combineMesh, mergeEdge, edgeHeight, shareEdge);
                lodParams.Add(lodParam);
            }
            var vertexDisplayRadius = reader.ReadSingle();
            var segmentLengthRatioRandomRange = reader.ReadSingle();
            var tangentRotationRandomRange = reader.ReadSingle();
            CurveRegionMeshGenerationParam param = new CurveRegionMeshGenerationParam(vertexDisplayRadius, segmentLengthRatioRandomRange, tangentRotationRandomRange, lodParams);
            return param;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                string name = Utils.ReadString(reader);
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }
    }
}

#endif