﻿//using UnityEditor;
//using UnityEngine;

//namespace K1CustomDrawers.Editor
//{
//    [CustomPropertyDrawer(typeof(AssetReferenceAttribute))]
//    public class AssetReferenceDrawer : PropertyDrawer
//    {
//        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
//        {
//            var targetAttribute = attribute as AssetReferenceAttribute;

//            if (SerializedPropertyType.String != property.propertyType || null == targetAttribute)
//            {
//                EditorGUI.PropertyField(position, property);
//                return;
//            }
            
//            //根据路径得到一个类型为GameObject的对象
//            var assetObj = AssetDatabase.LoadAssetAtPath(property.stringValue, targetAttribute.TargetAssetType);
//            //ObjectField会在Inspector面板中显示一个框，后面带一个小按钮，点击后弹出面板选择assetObj
//            var obj = EditorGUI.ObjectField(position, property.displayName, assetObj, targetAttribute.TargetAssetType,
//                false);
//            //得到prefab的路径
//            string newPath = AssetDatabase.GetAssetPath(obj);
//            //设置路径
//            property.stringValue = newPath;
//            Debug.Log(newPath);
//        }
//    }
//}