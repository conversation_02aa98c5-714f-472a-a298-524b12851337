﻿ 



 
 


using System.IO;


namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadVaryingTileSizeTerrainPrefabManager(BinaryReader reader)
        {
            var pos = GetSectionDataStartPosition(MapDataSectionType.VaryingTileSizeTerrainPrefabManager);
            if (pos < 0)
            {
                return;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //--------------------version 1 start-------------------------
            int nGroups = reader.ReadInt32();
            mEditorData.varyingTileSizeTerrainPrefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.PrefabGroup();
                group.id = reader.ReadInt32();
                int nPrefabs = reader.ReadInt32();
                group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
                for (int k = 0; k < nPrefabs; ++k)
                {
                    group.prefabPaths[k] = new config.PrefabSubGroup();
                    group.prefabPaths[k].prefabPath = Utils.ReadString(reader);
                    group.prefabPaths[k].size = Utils.ReadVector2Int(reader);
                }

                mEditorData.varyingTileSizeTerrainPrefabManager.groups[i] = group;
            }
            //--------------------version 1 end-------------------------
        }
    }
}
