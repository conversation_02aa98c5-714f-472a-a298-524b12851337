﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2020.6.12
 */

using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [Black]
    public static class MapLoaderManager
    {
        public static config.EditorMapData Load(string path, Map map)
        {
            Init();

            var bytes = GetBytes(path);
            if (bytes != null)
            {
                MemoryStream stream = new MemoryStream(bytes);
                BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                MapBinaryLoader loader = null;
                mLoaders.TryGetValue(majorVersion, out loader);
                if (loader != null)
                {
                    string mapName = Utils.GetPathName(path, false);
                    var mapData = loader.Load(minorVersion, mapName, map, reader);
                    reader.Close();
                    return mapData;
                }
                else
                {
                    reader.Close();
                }
            }

            return null;
        }

        public static void RegisterImporter(MapBinaryLoader loader)
        {
            if (mLoaders.ContainsKey(loader.majorVersion) == false)
            {
                mLoaders.Add(loader.majorVersion, loader);
            }
        }

        static byte[] GetBytes(string dataPath)
        {
            //in editor mode
            byte[] bytes = null;
            if (File.Exists(dataPath))
            {
                bytes = File.ReadAllBytes(dataPath);
            }
            return bytes;
        }

        static void Init()
        {
            RegisterImporter(new MapBinaryLoaderV1(1));
            RegisterImporter(new MapBinaryLoaderV2(2));
        }

        static Dictionary<int, MapBinaryLoader> mLoaders = new Dictionary<int, MapBinaryLoader>();
    }
}


#endif