﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class ModelTemplateBlockPrefabCreator
    {
        public static bool CheckBlockPrefabs(ModelTemplate modelTemplate, float tileSize)
        {
            var exportFolder = SLGMakerEditor.instance.exportFolder;
            if (string.IsNullOrEmpty(exportFolder))
            {
                Debug.LogError("Export folder is null, Can't create block prefabs!");
                return false;
            }

            string prefabFolder = GetPrefabFolder();
            bool countMatch = CheckIfPrefabCountMatch(prefabFolder, modelTemplate);
            if (!countMatch)
            {
                DeleteBlockPrefabs(modelTemplate.prefabPathPrefix);
                CreateBlockPrefabs(modelTemplate, tileSize);

                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }

            return true;
        }

        static bool CheckIfPrefabCountMatch(string prefabFolder, ModelTemplate modelTemplate)
        {
            int blockWidth = modelTemplate.blockWidth;
            int blockHeight = modelTemplate.blockHeight;
            var enumerator = Directory.EnumerateFiles(prefabFolder, "*.*", SearchOption.TopDirectoryOnly);
            List<string> paths = new List<string>();
            string name = Utils.GetPathName(modelTemplate.prefabPathPrefix, false);
            foreach (var path in enumerator)
            {
                string validPath = Utils.GetPathName(path.Replace('\\', '/'), true);
                var pathName = Utils.GetPathName(validPath, false);
                if (validPath.EndsWith("bytes", true, null) && validPath.IndexOf(name) >= 0 && pathName.EndsWith("_lod0"))
                {
                    paths.Add(validPath);
                }
            }
            //only check lod0
            if (paths.Count != blockWidth * blockHeight)
            {
                return false;
            }
            return true;
        }

        public static void CreateBlockPrefabs(ModelTemplate modelTemplate, float tileSize)
        {
            string prefabFolder = GetPrefabFolder();
            if (!Directory.Exists(prefabFolder))
            {
                Directory.CreateDirectory(prefabFolder);
            }
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            int nLODs = modelTemplate.lodCount;
            int blockWidth = modelTemplate.blockWidth;
            int blockHeight = modelTemplate.blockHeight;
            var layer = Map.currentMap.GetMapLayer<EditorGridModelLayer>();
            for (int lod = 0; lod < nLODs; ++lod)
            {
                for (int i = 0; i < blockHeight; ++i)
                {
                    for (int j = 0; j < blockWidth; ++j)
                    {
                        string blockPrefabName = Utils.GetPathName(modelTemplate.GetLODPrefabPathOfBlock(lod, i, j), true);
                        string prefabPath = modelTemplate.GetLODPrefabPath(lod);
                        GameObject prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);

                        var pos = layer.FromCoordinateToWorldPosition(j, i);
                        var objects = GetObjectsInBounds(modelTemplate, lod, prefab, pos.x, pos.z, pos.x + tileSize, pos.z + tileSize);

                        string blockPrefabPath = $"{prefabFolder}/{blockPrefabName}";
                        Map.currentMap.fakePrefabManager.CreateAsset(blockPrefabPath, objects);
                    }
                }
            }
        }

        static List<ChildPrefabTransform> GetObjectsInBounds(ModelTemplate modelTemplate, int lod, GameObject tilePrefab, float minX, float minZ, float maxX, float maxZ)
        {
            List<ChildPrefabTransform> childPrefabs = new List<ChildPrefabTransform>();

            int n = tilePrefab.transform.childCount;
            var childPrefabTransformList = modelTemplate.GetChildPrefabTransform(lod);
            for (int i = 0; i < n; ++i)
            {
                var pos = childPrefabTransformList[i].position;
                if (pos.x >= minX && pos.x < maxX &&
                    pos.z >= minZ && pos.z < maxZ)
                {
                    var newChildPrefabTransform = new ChildPrefabTransform();
                    newChildPrefabTransform.path = childPrefabTransformList[i].path;
                    newChildPrefabTransform.localBoundsInPrefab = childPrefabTransformList[i].localBoundsInPrefab;
                    newChildPrefabTransform.tag = childPrefabTransformList[i].tag;
                    newChildPrefabTransform.position = pos;
                    newChildPrefabTransform.editorScaling = childPrefabTransformList[i].editorScaling;
                    newChildPrefabTransform.editorRotation = childPrefabTransformList[i].editorRotation;
                    newChildPrefabTransform.objectType = MapCoreDef.GetTileObjectType(childPrefabTransformList[i].tag);

                    childPrefabs.Add(newChildPrefabTransform);
                }
            }

            return childPrefabs;
        }

        public static void DeleteBlockPrefabs(string prefabPathPrefix)
        {
            string prefabFolder = GetPrefabFolder();
            if (Directory.Exists(prefabFolder))
            {
                string name = Utils.GetPathName(prefabPathPrefix, false);
                var enumerator = Directory.EnumerateFiles(prefabFolder, "*.*", SearchOption.TopDirectoryOnly);
                List<string> paths = new List<string>();
                foreach (var path in enumerator)
                {
                    if (path.EndsWith("bytes", true, null) && path.IndexOf(name) >= 0)
                    {
                        string validPath = Utils.GetPathName(path.Replace('\\', '/'), true);
                        paths.Add(validPath);
                    }
                }

                foreach (var path in paths)
                {
                    FileUtil.DeleteFileOrDirectory(path);
                }
            }
        }

        public static string GetPrefabFolder()
        {
            var prefabFolder = SLGMakerEditor.instance.exportFolder + MapCoreDef.MAP_GENERATED_BLOCK_PREFABS_SUBFOLDER;
            return prefabFolder;
        }
    }
}

#endif