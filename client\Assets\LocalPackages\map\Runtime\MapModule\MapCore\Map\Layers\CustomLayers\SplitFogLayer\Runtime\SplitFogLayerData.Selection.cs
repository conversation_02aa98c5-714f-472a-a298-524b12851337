﻿using UnityEngine;

namespace TFW.Map
{
    public partial class SplitFogLayerData : MapLayerData
    {
        public void Select(int x, int y, int width, int height)
        {
            Debug.Assert(width > 1 && height > 1);
            if (mSelection.width != width || mSelection.height != height || mSelection.minX != x || mSelection.minY != y)
            {
                mSelection = new Rect2D(x, y, x + width - 1, y + height - 1);
                bool invalidSelection = mSelectionChangeCallback(x, y, width, height);
                if (invalidSelection)
                {
                    HideSelection();
                }
            }
        }

        public void HideSelection()
        {
            mSelection = Rect2D.empty;
            mHideSelectionCallback();
        }

        Rect2D mSelection = Rect2D.empty;
        System.Func<int, int, int, int, bool> mSelectionChangeCallback;
        System.Action mHideSelectionCallback;
    }
}
