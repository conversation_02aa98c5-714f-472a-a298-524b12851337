﻿using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 扩展方法
/// </summary>
public static class ObjExtensionMethods
{
    /// <summary>
    /// 获取或添加一个组件
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="go"></param>
    /// <returns></returns>
    public static T GetAddCom<T>(this GameObject go) where T : Component
    {
        var com = go.GetComponent<T>();
        if (com == null)
        {
            com = go.AddComponent<T>();
        }
        return com;
    }

    /// <summary>
    /// 设置到父物体
    /// </summary>
    /// <param name="go"></param>
    /// <returns></returns>
    public static void SetParent(this GameObject go, Transform parent, bool isReset = true)
    {
        var tr = go.transform;
        tr.SetParent(parent);
        if (isReset)
        {
            tr.localPosition = Vector3.zero;
            tr.localScale = Vector3.one;
            tr.localRotation = Quaternion.identity;
        }
    }

    /// <summary>
    /// 转换为float
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static float[] ToFloatArr(this List<string> strLis)
    {
        float[] arr = null;
        if (strLis != null && strLis.Count > 0)
        {
            arr = new float[strLis.Count];
            for (int i = 0; i < strLis.Count; i++)
            {
                arr[i] = Game.Utils.ConvertUtils.GetFloatFromString(strLis[i]);
            }
        }
        return arr;
    }


    /// <summary>
    /// 转换为int
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static int[] ToIntArr(this List<string> strLis)
    {
        int[] arr = null;
        if (strLis != null && strLis.Count > 0)
        {
            arr = new int[strLis.Count];
            for (int i = 0; i < strLis.Count; i++)
            {
                arr[i] = Game.Utils.ConvertUtils.GetIntFromString(strLis[i]);
            }
        }
        return arr;
    }

    /// <summary>
    /// 转换为float
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static List<float> ToFloatList(this List<string> strLis, bool filterZero = false)
    {
        List<float> arr = new List<float>();
        if (strLis != null && strLis.Count > 0)
        {
            for (int i = 0; i < strLis.Count; i++)
            {
                var val = Game.Utils.ConvertUtils.GetFloatFromString(strLis[i]);
                if (filterZero)
                {
                    if (val == 0f)
                    {
                        continue;
                    }
                }
                arr.Add(val);
            }
        }
        return arr;
    }


    /// <summary>
    /// 转换为int
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static List<int> ToIntList(this List<string> strLis)
    {
        List<int> arr = new List<int>();
        if (strLis != null && strLis.Count > 0)
        {
            for (int i = 0; i < strLis.Count; i++)
            {
                arr.Add(Game.Utils.ConvertUtils.GetIntFromString(strLis[i]));
            }
        }
        return arr;
    }

    /// <summary>
    /// 转换为int
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static int ToInt(this string str)
    {
        if(int.TryParse(str, out var r))
            return r;

        return 0;
    }

    public static string ToSquareString(this string str)
    {
        if (string.IsNullOrEmpty(str))
        {
            return str;
        }

        return str.Replace("[", string.Empty).Replace("]", string.Empty);
    }

    /// <summary>
    /// 转换为float
    /// </summary>
    /// <returns></returns>
    public static float ToFloat(this string str)
    {
        float.TryParse(str, out var r);
        return r;
    }


    /// <summary>
    /// 转换为long
    /// </summary>
    /// <param name="strLis"></param>
    /// <returns></returns>
    public static long ToLong(this string str)
    {
        long.TryParse(str, out var r);
        return r;
    }

    #region 兼容配置

    public static int Count<T>(this T[] arr)
    {
        if (arr != null)
            return arr.Length;
        return 0;
    }

    public static int Count<T>(this List<T> list)
    {
        if (list != null)
            return list.Count;
        return 0;
    }


    public static List<string> ToList(this List<int> source)
    {
        if (source == null)
        {
            return new List<string>(0);
        }

        var len = source.Count;

        var strList = new List<string>(len);
        for (int i = 0; i < len; i++)
        {
            strList[i] = source[i].ToString();
        }

        return strList;
    }

    public static List<string> ToList(this List<string> source)
    {
        return source;
    }
    #endregion

}
