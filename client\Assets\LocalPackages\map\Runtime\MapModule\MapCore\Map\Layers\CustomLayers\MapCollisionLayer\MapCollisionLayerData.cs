﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

//created by wzw at 2019/8/15

namespace TFW.Map
{
    public class MapCollisionLayerData : PolygonCollisionLayerData
    {
        public MapCollisionLayerData(MapLayerDataHeader header, Map map) : base(header, null, map)
        {
        }

        public void GetPolygonsOfCanNotPlaceDecorationObjects(List<ObstacleObject> polygons)
        {
            foreach (var p in objects)
            {
                var collsionData = p.Value as MapCollisionData;
                if (collsionData.HasAttribute(CollisionAttribute.IsFrontLayerObjectClipper) && !collsionData.HasAttribute(CollisionAttribute.IsConvertedFromPrefabOutline))
                {
                    //prefaboutline转换而来的collision要忽略
                    var vertices = collsionData.GetOutlineVerticesCopy(PrefabOutlineType.ObjectPlacementObstacle);
                    ObstacleObject obj = new ObstacleObject() { polygon = vertices, bounds = Utils.CreateBounds(vertices) };
                    polygons.Add(obj);
                }
            }
        }

        public void GetCollisionsOfType(PrefabOutlineType outlineType, List<ObstacleObject> collisions, CollisionAttribute attribute)
        {
            foreach (var p in objects)
            {
                var collisionData = p.Value as MapCollisionData;
                if (collisionData.HasAttribute(attribute))
                {
                    var vertices = collisionData.GetOutlineVerticesCopy(outlineType);
                    collisions.Add(new ObstacleObject() { polygon = vertices, bounds = Utils.CreateBounds(vertices) });
                }
            }
        }

        public void GetCollisionsOfType(List<MapCollisionData> collisions, CollisionAttribute attribute)
        {
            foreach (var p in objects)
            {
                var collisionData = p.Value as MapCollisionData;
                if (collisionData.HasAttribute(attribute))
                {
                    collisions.Add(collisionData);
                }
            }
        }

        public void GetAllCollisions(List<MapCollisionData> allCollisions)
        {
            foreach (var p in objects)
            {
                var collisionData = p.Value as MapCollisionData;
                allCollisions.Add(collisionData);
            }
        }

        public bool FindSnapPosition(PrefabOutlineType type, Vector3 pos, float radius, int exceptionCollisionID, out Vector3 snapPos)
        {
            snapPos = Vector3.zero;
            foreach (var p in objects)
            {
                if (p.Key != exceptionCollisionID)
                {
                    var collisionData = p.Value as MapCollisionData;
                    bool found = collisionData.FindSnapPosition(type, pos, radius, out snapPos);
                    if (found)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}

#endif