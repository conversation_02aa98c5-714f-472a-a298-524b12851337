﻿








using UnityEngine;
using System.Collections.Generic;
using System.Diagnostics;
using Debug = UnityEngine.Debug;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System;
using System.IO;

namespace TFW.Build
{
    public class ProcessHelperUtils : MonoBehaviour
    {
#if UNITY_EDITOR
        [UnityEditor.MenuItem("Framework/Tools/Print Current Process ID", false, 2021)]
        static void LogCurrentProcessId()
        {
            Debug.Log(GetCurrentProcessId());
        }
#endif

        public static int GetCurrentProcessId()
        {
            return Process.GetCurrentProcess().Id;
        }

        public static void StartProcess(string fileName, string arguments, bool waitForExit = true, string currentDirectory = "")
        {

#if UNITY_EDITOR
            if (waitForExit && !Application.isPlaying)
                EditorUtility.DisplayProgressBar("Hold on", fileName + " " + arguments, 0.5f);
#endif

            var processStartInfo = new ProcessStartInfo();
            Debug.Log(fileName + " -> " + arguments);
            processStartInfo.FileName = fileName;
            processStartInfo.Arguments = arguments;
            processStartInfo.CreateNoWindow = true;
            processStartInfo.RedirectStandardOutput = true;
            processStartInfo.RedirectStandardError = true;
            processStartInfo.UseShellExecute = false;

            var process = new Process();
            process.StartInfo = processStartInfo;

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    Debug.Log(e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    Debug.LogError(e.Data);
                }
            };

            process.Start();

            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            if (waitForExit)
            {
                process.WaitForExit();

#if UNITY_EDITOR
                if (!Application.isPlaying)
                    EditorUtility.ClearProgressBar();
#endif
            }
        }
    }
}