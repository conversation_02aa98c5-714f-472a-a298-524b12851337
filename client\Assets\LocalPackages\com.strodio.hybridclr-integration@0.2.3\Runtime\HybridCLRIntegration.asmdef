{"name": "HybridCLRIntegration", "rootNamespace": "", "references": ["GUID:9e24947de15b9834991c9d8411ea37cf", "GUID:84651a3751eca9349aac36a66bba901b", "GUID:13ba8ce62aa80c74598530029cb2d649", "GUID:f51ebe6a0ceec4240a699833d6309b23", "GUID:593a5b492d29ac6448b1ebf7f035ef33", "GUID:a72800e68485f8344a5a4e844fe934fb", "GUID:ff4bf6c59d1ac413a8f34ced5be76ecd", "GUID:6de90ce572153b748af42cf0485c51ac", "GUID:85ea05664d421a14187d6d61a4e66718"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}