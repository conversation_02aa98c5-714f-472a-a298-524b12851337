﻿#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        class GroundGroupItem
        {
            public GroundGroupItem(Transform transform, Mesh mesh)
            {
                this.transform = transform;
                this.mesh = mesh;
            }

            public Transform transform;
            public Mesh mesh;
        }

        class GroundGroup
        {
            public List<GroundGroupItem> tiles = new List<GroundGroupItem>();

            public void Add(Mesh mesh, Transform transform)
            {
                if (mesh == null || transform == null)
                {
                    return;
                }

                foreach (var tile in tiles)
                {
                    if (tile.transform == transform)
                    {
                        return;
                    }
                }
                tiles.Add(new GroundGroupItem(transform, mesh));
            }
        }

        class GroundTileInfo
        {
            public GroundTileInfo(int x, int y)
            {
                this.x = x;
                this.y = y;
            }

            public int x;
            public int y;
        }

        public void RemoveMeshOutlines()
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetCollisionsOfType(collisions, CollisionAttribute.IsMeshOutline);
            for (int i = 0; i < collisions.Count; ++i)
            {
                RemoveObject(collisions[i].id);
            }
        }

        public void AddObjectFromMeshOutline(float distanceError, float angleError)
        {
            EditorUtility.DisplayProgressBar("Remove Mesh Outline Collisions", "Please wait", 0.0f);
            RemoveMeshOutlines();

            //转换collision
            IBlendTerrainLayer layer = null;
            var blendLayer = Map.currentMap.GetMapLayer<BlendTerrainLayer>();
            if (blendLayer != null)
            {
                layer = blendLayer;
            }
            else
            {
                var varyingTileSizeLayer = Map.currentMap.GetMapLayer<VaryingTileSizeTerrainLayer>();
                if (varyingTileSizeLayer != null)
                {
                    layer = varyingTileSizeLayer;
                }
            }
            if (layer != null)
            {
                List<GroundGroup> groups = CreateGroundGroups(layer);
                GroundGroup largestGroup = GetLargestGroup(groups);

                var creator = new MeshOutlineCreator();

                EditorUtility.DisplayProgressBar("Find Largest Tile Group", "Please wait", 0.1f);
                List<MeshOutlineCreator.MeshInfo> meshes = new List<MeshOutlineCreator.MeshInfo>();
                foreach (var tile in largestGroup.tiles)
                {
                    var meshInfo = new MeshOutlineCreator.MeshInfo(tile.mesh, tile.transform);
                    meshes.Add(meshInfo);
                }

                var hull = creator.CombineMeshesAndCreate(meshes, new Vector2(-0.2f, 0.2f));
                if (hull != null)
                {
                    //EditorUtils.CreateDrawPolygon($"mesh outline: {hull.Count}", hull, 1);
                }

                //reduce vertex count
                if (distanceError > 0)
                {
                    hull = RemoveCloseEnoughVertices(hull, distanceError, 0.1f);
                    if (hull != null)
                    {
                        //EditorUtils.CreateDrawPolygon($"After remove close enough vertices: {hull.Count}", hull, 1);
                    }
                }

                if (angleError > 0)
                {
                    hull = RemoveAlmostParallelVertices(hull, angleError);
                    if (hull != null)
                    {
                        //EditorUtils.CreateDrawPolygon($"After remove almost parallel vertices: {hull.Count}", hull, 1);
                    }
                }

                List<Vector3> v0 = new List<Vector3>();
                List<Vector3> v1 = new List<Vector3>();
                v0.AddRange(hull);
                v1.AddRange(hull);
                OutlineData[] outlineData = new OutlineData[2]
                {
                new OutlineData(v0),
                new OutlineData(v1),
                };

                var collision = new MapCollisionData(Map.currentMap.nextCustomObjectID, Map.currentMap, outlineData, displayVertexRadius, true, CollisionAttribute.IsAutoExpandingObstacle | CollisionAttribute.IsMeshOutline | CollisionAttribute.BorderLine | CollisionAttribute.SpecialRegion, 0, true);
                AddObject(collision);
            }
            EditorUtility.ClearProgressBar();
        }

        List<GroundGroup> CreateGroundGroups(IBlendTerrainLayer layer)
        {
            List<GroundGroup> groups = new List<GroundGroup>();
            HashSet<Vector2Int> processedTiles = new HashSet<Vector2Int>();
            int h = layer.horizontalTileCount;
            int v = layer.verticalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    bool hasTile = layer.HasTile(j, i);
                    if (hasTile)
                    {
                        if (!processedTiles.Contains(new Vector2Int(j, i)))
                        {
                            var group = CreateGroup(layer, j, i, processedTiles);
                            groups.Add(group);
                        }
                    }
                }
            }

            return groups;
        }

        GroundGroup CreateGroup(IBlendTerrainLayer layer, int x, int y, HashSet<Vector2Int> processedTiles)
        {
            GroundGroup group = new GroundGroup();
            List<GroundTileInfo> stack = new List<GroundTileInfo>();
            stack.Add(new GroundTileInfo(x, y));
            Vector2Int[] neighbourOffset = new Vector2Int[4]
            {
                new Vector2Int(1, 0),
                new Vector2Int(-1, 0),
                new Vector2Int(0, 1),
                new Vector2Int(0, -1),
            };

            while (stack.Count > 0)
            {
                var cur = stack[stack.Count - 1];
                stack.RemoveAt(stack.Count - 1);
                processedTiles.Add(new Vector2Int(cur.x, cur.y));

                layer.GetTerrainTileMeshAndGameObject(cur.x, cur.y, out var mesh, out GameObject gameObject);
                group.Add(mesh, gameObject.transform);                

                for (int i = 0; i < 4; ++i)
                {
                    var nx = cur.x + neighbourOffset[i].x;
                    var ny = cur.y + neighbourOffset[i].y;
                    bool hasTile = layer.HasTile(nx, ny);
                    if (hasTile && processedTiles.Contains(new Vector2Int(nx, ny)) == false)
                    {
                        stack.Add(new GroundTileInfo(nx, ny));
                    }
                }
            }

            return group;
        }

        GroundGroup GetLargestGroup(List<GroundGroup> groups)
        {
            int maxTileIndex = 0;
            for (int i = 1; i < groups.Count; ++i)
            {
                if (groups[i].tiles.Count > groups[maxTileIndex].tiles.Count)
                {
                    maxTileIndex = i;
                }
            }
            return groups[maxTileIndex];
        }

        List<Vector3> RemoveCloseEnoughVertices(List<Vector3> outline, float distanceError, float vertexError)
        {
            List<Vector3> optimizedOutline = new List<Vector3>();
            optimizedOutline.Add(outline[outline.Count - 1]);
            for (int i = outline.Count - 1; i >= 0;)
            {
                float distanceSoFar = 0;
                var cur = outline[i];
                int j;
                for (j = i - 1; j >= 0; --j)
                {
                    var next = outline[j];
                    float dis = Vector3.Distance(cur, next);
                    if (distanceSoFar + dis > distanceError)
                    {
                        optimizedOutline.Add(next);
                        i = j;
                        break;
                    }
                    else
                    {
                        distanceSoFar += dis;
                    }
                }
                if (j == -1)
                {
                    break;
                }
            }
            return optimizedOutline;
        }

        List<Vector3> RemoveAlmostParallelVertices(List<Vector3> outline, float angleError)
        {
            while (true)
            {
                bool found = false;
                int n = outline.Count;
                for (int i = 1; i < n - 1; ++i)
                {
                    var last = outline[i - 1];
                    var cur = outline[i];
                    var next = outline[i + 1];
                    var d = cur - last;
                    d.Normalize();
                    var d1 = next - cur;
                    d1.Normalize();
                    float angle = Vector3.Angle(d, d1);
                    if (angle <= angleError)
                    {
                        outline.RemoveAt(i);
                        found = true;
                        break;
                    }
                }
                if (!found)
                {
                    break;
                }
            }
            return outline;
        }
    }
}

#endif