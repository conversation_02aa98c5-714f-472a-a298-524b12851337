﻿Shader "TFW/Terrain Detail Optimize Strengthen"
{
    Properties
    {
        _L1Tex("L1Tex", 2D) = "white" {}
        _L1FarColor("L1Color", Color) = (1, 1, 1, 1)
        _L2FarColor("L2Color", Color) = (1, 1, 1, 1)
        _L3FarColor("L3Color", Color) = (1, 1, 1, 1)
        _L4FarColor("L4Color", Color) = (1, 1, 1, 1)

        [Space(20)]
        _DetailMask("Detail", 2D) = "black" {}
        _DetailFarColor("DetailColor", color) = (0,0,0,0)

        [Space(20)]
        [NoScaleOffset]
        _MaskTex("MaskTex ((R)MixL1/L2(G)MixL1+L2/L3(B)MixL1+L2+L3/L4 )", 2D) = "black" {}
        [Space(20)]
        [Toggle] DayNightToggle("Day Night Toggle", Float) = 0
    }

    CGINCLUDE
    #include "UnityCG.cginc"
    #include "Lighting.cginc"
    #include "AutoLight.cginc"
    #include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
    #define PI 3.1415926

    struct appdata
    {
        float4 vertex : POSITION;
        float2 uv : TEXCOORD0;
        float2 uv2 : TEXCOORD1;
    };

    struct v2f
    {
        float4 pos : SV_POSITION;
        float2 uv[3] : TEXCOORD2;
    };


    float4 _L1Tex_ST;
    float4 _MaskTex_ST;
    float4 _DetailMask_ST;
    half4 _L1FarColor;
    half4 _L2FarColor;
    half4 _L3FarColor;
    half4 _L4FarColor;
    half4 _DetailFarColor;

    sampler2D _L1Tex;
    sampler2D _MaskTex;
    sampler2D _DetailMask;

    v2f vert(appdata v)
    {
        v2f o;

        o.pos = UnityObjectToClipPos(v.vertex);
        o.uv[0] = TRANSFORM_TEX(v.uv, _MaskTex);
        o.uv[1] = TRANSFORM_TEX(v.uv, _L1Tex);
        o.uv[2] = TRANSFORM_TEX(v.uv, _DetailMask);

        return o;
    }

    float InverseLerp(float A, float B, float T)
    {
        return (T - A) / (B - A);
    }

    half Sharpening(half fValue)
    {
        return saturate(fValue);
    }

    fixed4 frag(v2f i) : SV_Target
    {
        half4 mask = tex2D(_MaskTex, i.uv[0]);
        half4 col = 0;
        half4 farCol = 0;
        float currentCameraDistance = _WorldSpaceCameraPos.y;
        #if HIGH_QUALITY
        half4 toggledDetail = float4(0, 0, 0, 0);
        toggledDetail.g = mask.g;
        toggledDetail.b = mask.b;
        toggledDetail.a = mask.a;
        float detailAlpha = saturate(toggledDetail.r + toggledDetail.g + toggledDetail.b + toggledDetail.a);
        float4 detail = tex2D(_DetailMask, i.uv[2]);
        float4 detailMask =float4(detail.a,detail.a,detail.a,1.0);
        float4 detailTex = float4(detail.r, detail.g, detail.b, 1.0);
        #endif
        if (currentCameraDistance < 140)
        {
            float4 l1234 = tex2D(_L1Tex, i.uv[1]);
            half4 l1 = half4(_L1FarColor.r, l1234.r, _L1FarColor.b, 1.0);
            half4 l2 = half4(_L2FarColor.r, l1234.g, _L2FarColor.b, 1.0);
            half4 l3 = half4(_L3FarColor.r, l1234.b, _L3FarColor.b, 1.0);
            half4 l4 = half4(_L4FarColor.r, l1234.a, _L4FarColor.b, 1.0);
            col.rgb = lerp(lerp(lerp(l1.rgb, l2.rgb, Sharpening(mask.r)), l3.rgb, Sharpening(mask.g)), l4.rgb,
                                               Sharpening(mask.b));
            farCol.rgb = lerp(lerp(lerp(_L1FarColor.rgb, _L2FarColor.rgb, mask.r), _L3FarColor.rgb, mask.g), _L4FarColor.rgb,
                                                                      mask.b);

            float currentCameraScale = saturate(
                InverseLerp(70, 140, currentCameraDistance));

            col.rgb = lerp(col.rgb, farCol.rgb, currentCameraScale);
            #if HIGH_QUALITY
            detailMask = lerp(detailMask, 0,saturate(((currentCameraDistance - 70) / 140)));
            col = lerp(col, (1 - _DetailFarColor.a * detailAlpha) * col + detailTex * _DetailFarColor.a * detailAlpha,detailMask);
            #endif
        }
        else
        {
            col.rgb = lerp(lerp(lerp(_L1FarColor.rgb, _L2FarColor.rgb, mask.r), _L3FarColor.rgb, mask.g), _L4FarColor.rgb, mask.b);
            #if HIGH_QUALITY
            col = lerp(col, (1 - _DetailFarColor.a * detailAlpha) * col + _DetailFarColor * _DetailFarColor.a * detailAlpha,detailMask);
            #endif
        }
        #ifdef DAYNIGHTTOGGLE_GLOBAL_ON
        #ifdef DAYNIGHTTOGGLE_ON
                col.rgb = ApplyDayNightLut(col.rgb);
        #endif
        #endif

        return col;
    }
    ENDCG

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "Queue" = "Geometry-2"
        }
        LOD 100
        ZWrite Off

        Pass
        {
            Tags
            {
                "LightMode" = "ForwardBase"
            }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            //#pragma target 3.0
            #pragma multi_compile_fog
            #pragma multi_compile_fwdbase
            //#pragma multi_compile_instancing
            #pragma multi_compile __ HIGH_QUALITY
            #pragma multi_compile __ DAYNIGHTTOGGLE_GLOBAL_ON
            #pragma multi_compile_local __ DAYNIGHTTOGGLE_ON
            ENDCG
        }
    }
    Fallback "Diffuse"
}