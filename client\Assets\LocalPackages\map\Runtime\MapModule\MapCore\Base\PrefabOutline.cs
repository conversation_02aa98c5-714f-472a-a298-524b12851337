﻿ 



 
 




/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public interface IObstacle
    {
        bool IsSimplePolygon(PrefabOutlineType type);
        //是否可扩充,有些障碍物不能扩充,不然会有问题.例如圆形地图的边界
        bool IsExtendable();
        List<Vector3> GetOutlineVertices(PrefabOutlineType type);
        Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type);
        GameObject gameObject { get; }
        Rect GetOutlineBounds(PrefabOutlineType type);
        Vector3 offset { set; get; }
    }

    [System.Serializable]
    public class OutlineData
    {
        public OutlineData()
        {
        }

        public OutlineData(List<Vector3> vertices)
        {
            outline.AddRange(vertices);
            CalculateBounds();
        }

        public void CalculateBounds()
        {
            Bounds bounds3D = new Bounds();
            if (outline.Count > 0)
            {
                bounds3D.SetMinMax(outline[0], outline[0]);
                for (int i = 1; i < outline.Count; ++i)
                {
                    bounds3D.Encapsulate(outline[i]);
                }

                bounds = Utils.BoundsToRect(bounds3D);
            }
        }

        public OutlineData Clone()
        {
            OutlineData d = new OutlineData(outline);
            d.isSimplePolygon = isSimplePolygon;
            d.isConvex = isConvex;
            return d;
        }

        public List<Vector3> GetCopy()
        {
            List<Vector3> copy = new List<Vector3>(outline.Count);
            copy.AddRange(outline);
            return copy;
        }

        public void Rotate(float degree, Vector3 center)
        {
            Quaternion q = Quaternion.Euler(0, degree, 0);
            for (int i = 0; i < outline.Count; ++i)
            {
                var localPos = outline[i] - center;
                outline[i] = center + q * localPos;
            }
        }

        public void Clear()
        {
            outline.Clear();
            bounds = new Rect();
            isSimplePolygon = false;
            isConvex = false;
        }

        public Vector3 GetVertexPos(int index)
        {
            if (index >= 0 && index < outline.Count)
            {
                return outline[index];
            }
            return Vector3.zero;
        }

        public void SetVertexPos(int index, Vector3 pos)
        {
            if (index >= 0 && index < outline.Count)
            {
                outline[index] = pos;
            }
        }

        public int GetVertexIndex(Vector3 pos)
        {
            for (int i = 0; i < outline.Count; ++i)
            {
                if (pos == outline[i])
                {
                    return i;
                }
            }
            return -1;
        }

        //局部坐标系下的顶点
        public List<Vector3> outline = new List<Vector3>();
        public Rect bounds { set; get; }
        public bool isSimplePolygon = true;
        public bool isConvex = true;
    }

    //障碍物的边框
    [ExecuteInEditMode]
    [SelectionBaseAttribute]
    public class PrefabOutline : MonoBehaviour, IObstacle
    {
#if UNITY_EDITOR
        void OnEnable()
        {
            for (int i = 0; i < 2; ++i)
            {
                Reverse(i);
            }
        }
#endif

        public void Init()
        {
            currentOutlineType = PrefabOutlineType.NavMeshObstacle;

            for (int i = 0; i < 2; ++i)
            {
                if (mOutlineData[i] == null)
                {
                    ResetOutline(gameObject, (PrefabOutlineType)i, true);
                }
            }
        }

        //从gameObjects里创建边框
        public void CreateFrom(List<GameObject> gameObjects)
        {
            for (int idx = 0; idx < 2; ++idx)
            {
                mOutlineData[idx] = new OutlineData();

                var hull = ConvexHullBuilder.CreateGameObject2DConvexHullInXZPlane(gameObjects, true);
                if (hull.Count == 0)
                {
                    var bounds = GameObjectBoundsCalculator.CalculateBounds(gameObjects[0]);
                    if (bounds.size == Vector3.zero)
                    {
                        bounds = new Bounds(Vector3.zero, Vector3.one * 5.0f);
                    }
                    var min = bounds.min;
                    var max = bounds.max;
                    List<Vector3> outlineVertices = new List<Vector3> {
                        new Vector3(min.x, 0, min.z),
                        new Vector3(max.x, 0, min.z),
                        new Vector3(max.x, 0, max.z),
                        new Vector3(min.x, 0, max.z),
                        };
                    mOutlineData[idx].outline = outlineVertices;
                }
                else
                {
                    List<Vector3> outlineVertices = new List<Vector3>();
                    for (int k = 0; k < hull.Count; ++k)
                    {
                        outlineVertices.Add(new Vector3(hull[k].x, 0, hull[k].z));
                    }
                    mOutlineData[idx].outline = outlineVertices;
                }

                mOutlineData[idx].CalculateBounds();

                Reverse(idx);
            }

            Expand(PrefabOutlineType.ObjectPlacementObstacle, 3);
        }

        public void ResetOutline(GameObject gameObject, PrefabOutlineType type, bool worldSpace)
        {
            var idx = (int)type;
            mOutlineData[idx] = new OutlineData();

            var hull = ConvexHullBuilder.CreateGameObject2DConvexHullInXZPlane(gameObject, worldSpace);
            if (hull.Count == 0)
            {
                var bounds = GameObjectBoundsCalculator.CalculateBounds(gameObject);
                if (bounds.size == Vector3.zero)
                {
                    bounds = new Bounds(Vector3.zero, Vector3.one * 5.0f);
                }
                var min = bounds.min;
                var max = bounds.max;
                List<Vector3> outlineVertices = new List<Vector3> {
                        new Vector3(min.x, 0, min.z),
                        new Vector3(max.x, 0, min.z),
                        new Vector3(max.x, 0, max.z),
                        new Vector3(min.x, 0, max.z),
                        };
                mOutlineData[idx].outline = outlineVertices;
            }
            else
            {
                List<Vector3> outlineVertices = new List<Vector3>();
                for (int k = 0; k < hull.Count; ++k)
                {
                    outlineVertices.Add(new Vector3(hull[k].x, 0, hull[k].z));
                }
                mOutlineData[idx].outline = outlineVertices;
            }

            mOutlineData[idx].CalculateBounds();

            Reverse(idx);
        }

        void Reverse(int idx)
        {
            if (mOutlineData[idx] != null && !Utils.IsPolygonCW(mOutlineData[idx].outline))
            {
                Utils.ReverseList(mOutlineData[idx].outline);
            }
        }

        //检测outline的vertex是否在有效的范围内
        public void IsInValidRange(float minX, float maxX, float minZ, float maxZ, out bool validNavMeshObstacle, out bool validObjectplacementObstacle)
        {
            validNavMeshObstacle = true;
            validObjectplacementObstacle = true;
            for (int i = 0; i < mOutlineData.Length; ++i)
            {
                if (mOutlineData[i] != null)
                {
                    int n = mOutlineData[i].outline.Count;
                    var worldSpaceOutlines = GetWorldSpaceOutlineVertices((PrefabOutlineType)i);
                    for (int j = 0; j < n; ++j)
                    {
                        var p = worldSpaceOutlines[j];
                        if (Utils.LTE(p.x, minX, 0.001f) || Utils.GTE(p.x, maxX, 0.001f) ||
                            Utils.LTE(p.z, minZ, 0.001f) || Utils.GTE(p.z, maxZ, 0.001f))
                        {
                            if (i == 0)
                            {
                                validNavMeshObstacle = false;
                            }
                            else if (i == 1)
                            {
                                validObjectplacementObstacle = false;
                            }
                            else
                            {
                                Debug.Assert(false, "Unknown obstacle type!");
                            }
                            break;
                        }
                    }
                }
            }
        }

        public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
        {
            return mOutlineData[(int)type].outline;
        }

        public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
        {
            var outlineVertices = GetOutlineVertices(type);
            int n = outlineVertices.Count;
            Vector3[] worldSpaceOutlines = new Vector3[n];
            for (int i = 0; i < n; ++i)
            {
                worldSpaceOutlines[i] = TransformToWorldPosition(outlineVertices[i]);
            }
            return worldSpaceOutlines;
        }

        public Vector3 TransformToWorldPosition(Vector3 localPoint)
        {
            var worldPos = transform.position;
            var worldScale = transform.lossyScale;
            var worldRotationEuler = transform.rotation.eulerAngles;
            var rotY = Quaternion.Euler(0, worldRotationEuler.y, 0);
            var p1 = new Vector3(worldScale.x * localPoint.x, 0, worldScale.z * localPoint.z);
            var pos = rotY * p1 + new Vector3(worldPos.x, 0, worldPos.z);
            pos.y = 0;
            return pos;
        }

        public Vector3 TransformToLocalPosition(Vector3 worldPos)
        {
            var objWorldPos = transform.position;
            var objWorldScale = transform.lossyScale;
            var worldRotationEuler = transform.rotation.eulerAngles;
            var rotY = Quaternion.Euler(0, worldRotationEuler.y, 0);
            var localPos = new Vector3(worldPos.x - objWorldPos.x, 0, worldPos.z - objWorldPos.z);
            var invRot = Quaternion.Inverse(rotY);
            localPos = invRot * localPos;
            localPos = new Vector3(localPos.x / objWorldScale.x, 0, localPos.z / objWorldScale.z);
            return localPos;
        }

        public Rect GetOutlineBounds(PrefabOutlineType type)
        {
            return mOutlineData[(int)type].bounds;
        }

        public bool IsSimplePolygon(PrefabOutlineType type)
        {
            return mOutlineData[(int)type].isSimplePolygon;
        }

        public void SetSimplePolygon(PrefabOutlineType type, bool isSimple)
        {
            mOutlineData[(int)type].isSimplePolygon = isSimple;
        }

        public bool IsExtendable()
        {
            return true;
        }

        public void ClampToBorder()
        {
            if (hasRange)
            {
                for (int idx = 0; idx < 2; ++idx)
                {
                    var expandedVertices = new List<Vector3>();
                    expandedVertices.AddRange(mOutlineData[idx].outline);
                    List<Vector3> finalVertices = new List<Vector3>();
                    for (int i = 0; i < expandedVertices.Count; ++i)
                    {
                        if (hasRange)
                        {
                            var pos = ClampPosition(expandedVertices[i]);
                            finalVertices.Add(pos);
                        }
                        else
                        {
                            finalVertices.Add(expandedVertices[i]);
                        }
                    }

                    mOutlineData[idx].outline = finalVertices;
                    mOutlineData[idx].CalculateBounds();
                }
            }
        }

        public void Expand(PrefabOutlineType type, float radius)
        {
#if UNITY_EDITOR
            if (radius > 0)
            {
                int idx = (int)type;
                var expandedVertices = PolygonAlgorithm.ExpandPolygon(radius, mOutlineData[idx].outline)[0];
                List<Vector3> finalVertices = new List<Vector3>();
                for (int i = 0; i < expandedVertices.Count; ++i)
                {
                    if (hasRange)
                    {
                        var pos = ClampPosition(expandedVertices[i]);
                        finalVertices.Add(pos);
                    }
                    else
                    {
                        finalVertices.Add(expandedVertices[i]);
                    }
                }

                mOutlineData[idx].outline = finalVertices;
                mOutlineData[idx].CalculateBounds();
            }
#endif
        }

        //将顶点限制在某个范围内
        public Vector3 ClampPosition(Vector3 originalLocalPos)
        {
            var worldPos = transform.TransformPoint(originalLocalPos);

            Transform root = Utils.GetRootTransform(transform);
            var localPosInRoot = root.worldToLocalMatrix.MultiplyPoint(worldPos);

            if (hasRange)
            {
                localPosInRoot.x = Mathf.Clamp(localPosInRoot.x, minX, maxX);
                localPosInRoot.z = Mathf.Clamp(localPosInRoot.z, minZ, maxZ);
                worldPos = root.localToWorldMatrix.MultiplyPoint(localPosInRoot);
            }

            var localPos = transform.worldToLocalMatrix.MultiplyPoint(worldPos);

            return localPos;
        }

        //radius: 边框扩大一圈的半径
        public void CopyFromTo(PrefabOutlineType fromType, PrefabOutlineType toType, float radius)
        {
#if UNITY_EDITOR
            var fromList = mOutlineData[(int)fromType].outline;
            if (radius > 0)
            {
                fromList = PolygonAlgorithm.ExpandPolygon(radius, fromList)[0];
            }
            int toIndex = (int)toType;
            if (mOutlineData[toIndex] == null)
            {
                mOutlineData[toIndex] = new OutlineData();
            }
            mOutlineData[toIndex].outline = new List<Vector3>();

            for (int i = 0; i < fromList.Count; ++i)
            {
                if (hasRange)
                {
                    var pos = ClampPosition(fromList[i]);
                    mOutlineData[toIndex].outline.Add(pos);
                }
                else
                {
                    mOutlineData[toIndex].outline.Add(fromList[i]);
                }
            }

            mOutlineData[toIndex].CalculateBounds();
            mOutlineData[toIndex].isSimplePolygon = mOutlineData[(int)fromType].isSimplePolygon;
#endif
        }

        public void SetOutlineData(OutlineData data, PrefabOutlineType type)
        {
            mOutlineData[(int)type] = data;
        }

        public Vector3 offset { set; get; }

        [SerializeField]
        [HideInInspector]
        OutlineData[] mOutlineData = new OutlineData[2];
        public PrefabOutlineType currentOutlineType { set; get; }
        public bool saveMe = false;
        public float radius = 1.0f;
        public bool hasRange = false;
        public float minX = -90;
        public float maxX = 90;
        public float minZ = -90;
        public float maxZ = 90;
    }
}