﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class RuntimeRegionColorLayer : MapLayerBase
    {
        public RuntimeRegionColorLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }

            if (mLayerData != null)
            {
                mLayerData.OnDestroy();
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }
        //加载地图的数据
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.RuntimeRegionColorLayerData;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 0, 0, 0, 0, GridType.Rectangle, sourceLayer.origin);

            List<RuntimeRegionColorLayerData.Layer> layers = new List<RuntimeRegionColorLayerData.Layer>();

            for (int s = 0; s < sourceLayer.layers.Length; ++s)
            {
                var sourceSubLayer = sourceLayer.layers[s];
                int n = sourceSubLayer.regions != null ? sourceSubLayer.regions.Length : 0;
                RuntimeRegionColorData[] regions = new RuntimeRegionColorData[n];
                if (sourceSubLayer.regions != null)
                {
                    for (int i = 0; i < n; ++i)
                    {
                        var sourceRegion = sourceSubLayer.regions[i];
                        regions[i] = new RuntimeRegionColorData(sourceRegion.id, sourceRegion.color);
                    }
                }

                RuntimeRegionColorLayerData.Layer layer = new RuntimeRegionColorLayerData.Layer();
                layer.horizontalTileCount = sourceSubLayer.horizontalTileCount;
                layer.verticalTileCount = sourceSubLayer.verticalTileCount;
                layer.tileWidth = sourceSubLayer.tileWidth;
                layer.tileHeight = sourceSubLayer.tileHeight;
                layer.regions = regions;
                layer.regionIDs = sourceSubLayer.regionIDs;
                layer.name = sourceSubLayer.name;

                layers.Add(layer);
            }

            mLayerData = new RuntimeRegionColorLayerData(header, map, layers);
            mLayerView = new RuntimeRegionColorLayerView(mLayerData, sourceLayer.showRegionInGame);
            mLayerView.active = layerData.active;
            mLayerData.SetOnPixelsChangeCallback(mLayerView.OnSetPixels);

            //load properties
            LoadProperties();

            map.AddMapLayer(this);
        }
        //卸载地图层
        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            Debug.Assert(false, "todo");
            return true;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }

        public Color GetRegionColor(int layerIdx, int x, int y)
        {
            var region = mLayerData.GetRegion(layerIdx, x, y);
            if (region != null)
            {
                return region.color;
            }
            return Color.black;
        }

        public Color GetRegionColor(int layerIdx, Vector3 worldPos)
        {
            var coord = mLayerData.FromWorldPositionToCoordinate(layerIdx, worldPos);
            return GetRegionColor(layerIdx, coord.x, coord.y);
        }

        public int GetRegionID(int layerIdx, int x, int y)
        {
            return mLayerData.GetRegionID(layerIdx, x, y);
        }

        public int GetRegionID(int layerIdx, Vector3 worldPos)
        {
            var coord = mLayerData.FromWorldPositionToCoordinate(layerIdx, worldPos);
            return GetRegionID(layerIdx, coord.x, coord.y);
        }

        public void BeginQueryInterpolatedProperty(int layerIdx, Vector3 worldPos)
        {
            mLayerData.BeginQueryInterpolatedProperty(layerIdx, worldPos);
        }

        public float QueryInterpolatedFloat(string propName)
        {
            return mLayerData.QueryInterpolatedFloat(propName);
        }

        public Vector4 QueryInterpolatedVector4(string propName)
        {
            return mLayerData.QueryInterpolatedVector4(propName);
        }

        public Color QueryInterpolatedColor(string propName)
        {
            return mLayerData.QueryInterpolatedColor(propName);
        }

        public void SetLayer(int newLayerIndex)
        {
            mLayerView.OnSwitchLayer(newLayerIndex);
        }

        public void ApplyPropertiesToReferenceGameObject(GameObject gameObject, PropertyDatas properties)
        {
            var renderers = gameObject.GetComponentsInChildren<Renderer>(true);
            foreach (var renderer in renderers)
            {
                var ps = properties.properties;
                var mtls = renderer.materials;
                for (int i = 0; i < ps.Count; ++i)
                {
                    switch (ps[i].type)
                    {
                        case PropertyType.kPropertyFloat:
                            {
                                for(int m = 0; m < mtls.Length; ++m)
                                {
                                    mtls[m].SetFloat(ps[i].name, (ps[i] as PropertyData<float>).value);
                                }
                                break;
                            }
                        case PropertyType.kPropertyInt:
                            {
                                for (int m = 0; m < mtls.Length; ++m)
                                {
                                    mtls[m].SetInt(ps[i].name, (ps[i] as PropertyData<int>).value);
                                }
                                break;
                            }
                        case PropertyType.kPropertyVector4:
                            {
                                for (int m = 0; m < mtls.Length; ++m)
                                {
                                    mtls[m].SetVector(ps[i].name, (ps[i] as PropertyData<Vector4>).value);
                                }
                                break;
                            }
                        case PropertyType.kPropertyColor:
                            {
                                for (int m = 0; m < mtls.Length; ++m)
                                {
                                    mtls[m].SetColor(ps[i].name, (ps[i] as PropertyData<Color>).value);
                                }
                                break;
                            }
                        default:
                            break;
                    }
                }
            }
        }

        void LoadProperties()
        {
            string path = $"{map.dataFolder}/{MapCoreDef.RUNTIME_REGION_COLOR_LAYER_DATA}";
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                //--------------------------version 1 start------------------------------------
                int layerCount = reader.ReadInt32();
                for (int s = 0; s < layerCount; ++s)
                {
                    var subLayer = mLayerData.GetLayer(s);
                    int regionCount = reader.ReadInt32();   
                    for (int i = 0; i < regionCount; ++i)
                    {
                        int id = reader.ReadInt32();
                        var properties = Utils.ReadProperties(reader);
                        if (subLayer != null)
                        {
                            subLayer.SetRegionProperties(id, properties);
                        }
                    }
                }
                //--------------------------version 1 end------------------------------------

                reader.Close();
            }
        }

        public int horizontalRegionCount { get { return mLayerData.horizontalTileCount; } }
        public int verticalRegionCount { get { return mLayerData.verticalTileCount; } }
        //地图层的id
        public override int id { get { return mLayerData.id; } }
        //地图层的root gameobject
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override bool Contains(int objectID) { return false; }
        public override int GetCurrentLOD() { return 0; }
        public override void RefreshObjectsInViewport() { }
        public override Vector3 layerOffset => mLayerData.layerOffset;

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }
        //地图层的名称
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        public RuntimeRegionColorLayerData layerData { get { return mLayerData; } }
        public RuntimeRegionColorLayerView layerView { get { return mLayerView; } }

        RuntimeRegionColorLayerData mLayerData;
        RuntimeRegionColorLayerView mLayerView;
    }
}