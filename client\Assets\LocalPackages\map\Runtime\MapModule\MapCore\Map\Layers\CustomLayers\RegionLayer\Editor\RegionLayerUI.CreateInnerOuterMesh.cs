﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RegionLayerUI : UnityEditor.Editor
    {
        //计算一个区域内刨除障碍物后的剩余区域
        void CreateInnerOuterMesh(RegionData innerRegion, RegionData outerRegion)
        {
            var innerVertices = innerRegion.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            var outerVertices = outerRegion.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);

            List<List<Vector3>> noneHoles;
            List<List<Vector3>> holes;
            PolygonAlgorithm.GetDifferencePolygon(outerVertices, innerVertices, out noneHoles, out holes);

            Vector3[] borderMeshVertices;
            int[] borderMeshIndices;
            Triangulator.TriangulatePolygons(noneHoles, holes, false, 0, 0, null, out borderMeshVertices, out borderMeshIndices);

            Vector3[] innerMeshVertices;
            int[] innerMeshIndices;
            Triangulator.TriangulatePolygon(innerVertices, out innerMeshVertices, out innerMeshIndices);

            List<NavMeshCreator.MeshItem> meshItems = new List<NavMeshCreator.MeshItem>();
            if (borderMeshVertices != null)
            {
                meshItems.Add(new NavMeshCreator.MeshItem(borderMeshVertices, borderMeshIndices));
            }
            if (innerMeshVertices != null)
            {
                meshItems.Add(new NavMeshCreator.MeshItem(innerMeshVertices, innerMeshIndices));
            }

            Vector3[] meshVertices;
            int[] meshIndices;
            NavMeshCreator.CombineMesh(meshItems, out meshVertices, out meshIndices);

            Color[] vertexColors = new Color[meshVertices.Length];
            for (int i = 0; i < vertexColors.Length; ++i)
            {
                bool isIn = VertexExists(innerVertices, meshVertices[i]);
                if (isIn)
                {
                    vertexColors[i] = innerRegion.innerColor;
                }
                else
                {
                    Debug.Assert(VertexExists(outerVertices, meshVertices[i]));
                    vertexColors[i] = innerRegion.outerColor;
                }
            }

            var layer = mLogic.layer;

            layer.SetMesh(innerRegion.id, meshVertices, meshIndices, vertexColors);
            layer.SetMeshVisible(innerRegion.id, layer.layerData.showRegionMesh);
            layer.SetMesh(outerRegion.id, null, null, null);
        }

        public void CreateBorderMesh()
        {
            var layer = mLogic.layer;
            List<List<Vector3>> noneHoles;
            List<List<Vector3>> holes;
            float minX = layer.layerData.borderMinX;
            float minZ = layer.layerData.borderMinZ;
            float maxX = layer.layerData.borderMaxX;
            float maxZ = layer.layerData.borderMaxZ;
            var objects = layer.layerData.objects;
            List<List<Vector3>> outerRegionVertices = new List<List<Vector3>>();
            foreach (var p in objects)
            {
                var regionData = p.Value as RegionData;
                if (regionData.type == RegionType.Outer)
                {
                    outerRegionVertices.Add(regionData.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                }
            }
            PolygonAlgorithm.GetDifferencePolygon(minX, minZ, maxX, maxZ, outerRegionVertices, out noneHoles, out holes);

            Vector3[] borderVertices;
            int[] borderIndices;
            Triangulator.TriangulatePolygons(noneHoles, holes, false, 0, 0, null, out borderVertices, out borderIndices);

            layer.SetBorderMesh(borderVertices, borderIndices);
        }

        bool VertexExists(List<Vector3> vertices, Vector3 v)
        {
            for (int i = 0; i < vertices.Count; ++i)
            {
                if (Utils.Approximately(vertices[i].x, v.x, 0.01f) &&
                    Utils.Approximately(vertices[i].y, v.y, 0.01f) &&
                    Utils.Approximately(vertices[i].z, v.z, 0.01f))
                {
                    return true;
                }
            }
            return false;
        }
    }
}

#endif