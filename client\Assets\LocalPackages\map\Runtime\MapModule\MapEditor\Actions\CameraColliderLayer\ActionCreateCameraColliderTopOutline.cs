﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionCreateCameraColliderTopOutline : EditorAction
    {
        public ActionCreateCameraColliderTopOutline(int layerID, int dataID, float newHeight)
        {
            mDataID = dataID;
            mLayerID = layerID;
            var data = Map.currentMap.FindObject(mDataID) as CameraColliderData;
            mOldOutline = data.GetTopOutlineCopy();
            mOldHeight = data.height;
            mNewHeight = newHeight;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.CreateTopOutline(mDataID, mNewHeight);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetTopOutline(mDataID, mOldOutline, mOldHeight);
            return true;
        }

        List<Vector3> mOldOutline;
        float mOldHeight;
        float mNewHeight;
        int mDataID;
        int mLayerID;
    }
}

#endif