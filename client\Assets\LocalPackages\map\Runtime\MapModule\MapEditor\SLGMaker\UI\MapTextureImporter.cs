﻿ 



 
 


#if UNITY_EDITOR

using System.IO;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class MapTextureImporter : AssetPostprocessor
    {
        private void OnPreprocessAsset()
        {
            string fileName = Path.GetFileName(assetPath);
            if (fileName.StartsWith(MapCoreDef.RIVER_MASK_PREFIX))
            {
                var importer = assetImporter as TextureImporter;
                if (importer != null)
                {
                    importer.isReadable = true;
                    importer.alphaIsTransparency = false;
                    if (Map.currentMap != null)
                    {
                        var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
                        if (riverLayer != null)
                        {
                            riverLayer.SetRiverMaskTextureDirty();
                        }
                    }
                }
            }

            if (!assetPath.StartsWith($"{MapModule.runtimeMapResDirectory}MapData/Res/"))
            {
                return;
            }

            var textureImporter = assetImporter as TextureImporter;
            if (textureImporter != null)
            {
                textureImporter.wrapMode = UnityEngine.TextureWrapMode.Clamp;
            }
        }
    }
}
#endif