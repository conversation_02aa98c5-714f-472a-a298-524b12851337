﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ComplexGridObjectLayerToolPanel : EditorWindow
    {
        void DrawPlaceMultipleObjectsGUI()
        {
            mLogic.rotationSetting.Draw();
            mRange = EditorGUILayout.FloatField("Range", mRange);
            mRange = Mathf.Max(0.1f, mRange);
            mMinimumSpacing = EditorGUILayout.FloatField("Minimum Spacing", mMinimumSpacing);
            mMinObjectCount = EditorGUILayout.IntField("Minimum Object Count", mMinObjectCount);
            mMaxObjectCount = EditorGUILayout.IntField("Maximum Object Count", mMaxObjectCount);

            //保证count的有效性
            mMinObjectCount = Mathf.Max(1, mMinObjectCount);
            mMaxObjectCount = Mathf.Max(1, mMaxObjectCount);
            if (mMinObjectCount > mMaxObjectCount)
            {
                int t = mMinObjectCount;
                mMinObjectCount = mMaxObjectCount;
                mMaxObjectCount = t;
            }
        }

        public void PlaceMultipleObjects(int lod, Vector3 worldPos)
        {
            var map = SLGMakerEditor.GetMap();
            var layer = map.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;

            if (!layer.layerData.IsValidPosition(worldPos))
            {
                return;
            }

            var setting = mLogic.layerData.objectPlacementSetting;

            var modelTemplate = GetModelTemplate();
            if (modelTemplate != null)
            {
                int objectCount = Random.Range(mMinObjectCount, mMaxObjectCount + 1);

                float boundsWidth = modelTemplate.bounds.width;
                float boundsHeight = modelTemplate.bounds.height;

                List<Vector3> positions = new List<Vector3>();
                if (mMinimumSpacing <= 0)
                {
                    //无间隔随机生成
                    for (int i = 0; i < objectCount; ++i)
                    {
                        var obj = layer.FindObjectAtExactSamePosition(lod, worldPos);
                        if (obj == null)
                        {
                            var circle = Random.insideUnitCircle * mRange;
                            var pos = new Vector3(worldPos.x + circle.x, 0, worldPos.z + circle.y);

                            if (layer.layerData.IsValidPosition(pos))
                            {
                                positions.Add(pos);
                            }
                        }
                    }
                }
                else
                {
                    //有最小间隔生成
                    int horizontalGridCount = Mathf.FloorToInt(mRange / mMinimumSpacing);
                    int totalGridCount = horizontalGridCount * horizontalGridCount;
                    objectCount = Mathf.Min(objectCount, totalGridCount);
                    List<int> gridIndices = new List<int>(totalGridCount);
                    for (int i = 0; i < totalGridCount; ++i)
                    {
                        gridIndices.Add(i);
                    }
                    //shuffle
                    for (int i = 0; i < gridIndices.Count; ++i)
                    {
                        var swapIdx = Random.Range(i, gridIndices.Count);
                        var temp = gridIndices[swapIdx];
                        gridIndices[swapIdx] = gridIndices[i];
                        gridIndices[i] = temp;
                    }
                    //取前object count个坐标来生成
                    for (int i = 0; i < objectCount; ++i)
                    {
                        int x = gridIndices[i] % horizontalGridCount;
                        int y = gridIndices[i] / horizontalGridCount;
                        float localX = Random.Range(0.0f, mMinimumSpacing);
                        float localY = Random.Range(0.0f, mMinimumSpacing);
                        positions.Add(new Vector3(worldPos.x + x * mMinimumSpacing + localX - mRange * 0.5f, 0, worldPos.z + y * mMinimumSpacing + localY - mRange * 0.5f));
                    }
                }

                CompoundAction actions = new CompoundAction("Place Multiple Objects");
                for (int i = 0; i < positions.Count; ++i)
                {
                    var pos = positions[i];
                    float minX = pos.x - boundsWidth * 0.5f;
                    float minZ = pos.z - boundsHeight * 0.5f;
                    float maxX = pos.x + boundsWidth * 0.5f;
                    float maxZ = pos.z + boundsHeight * 0.5f;
                    int occupiedGridCount = layer.CalculateOccupiedGridCount(minX, minZ, maxX, maxZ);
                    var obj = layer.FindObjectAtExactSamePosition(lod, pos);
                    if (obj == null)
                    {
                        bool isOverlapWithObstacle = false;
                        var rot = mLogic.rotationSetting.rotation;
                        if (setting.considerObstacle)
                        {
                            isOverlapWithObstacle = mLogic.layer.CheckObstacle(modelTemplate.GetLODPrefabPath(0), pos, rot, Vector3.one);
                        }

                        if (!isOverlapWithObstacle)
                        {
                            var act = new ActionAddComplexGridModel(mLogic.layerID, map.nextCustomObjectID, modelTemplate.id, pos, rot, Vector3.one, occupiedGridCount, lod, modelTemplate.GetLODPrefabPath(0), mLogic.GetObjectTag(), false);
                            actions.Add(act);
                        }
                    }

                    mLogic.rotationSetting.StepToNextRandomRotation();
                }
                if (!actions.IsEmpty())
                {
                    ActionManager.instance.PushAction(actions);
                }
            }
        }

        void DrawPlaceMultipleObjectsScene(Vector3 worldPos)
        {
            Handles.DrawWireDisc(worldPos, Vector3.up, mRange);
            SceneView.RepaintAll();
        }

        float mRange = 100;
        float mMinimumSpacing;
        int mMinObjectCount;
        int mMaxObjectCount;
    }
}

#endif