﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CityTerritoryData : CityTerritoryDataBase
    {
        public CityTerritoryData(int id, Map map, int flag, Vector3 position, ModelTemplate modelTemplate, Rect bounds, int territoryID, Vector3 cityPosition, Color color, Material mtl, CityTerritoryDataBlock block, CityTerritoryLayerData.SubLayerData subLayer, int index, Texture2D maskTexture, List<CityTerritoryEdgeData> edges) : base(id, map, flag, position, modelTemplate, bounds, territoryID, mtl, block, subLayer, index, maskTexture)
        {
            mCityPosition = cityPosition;
            mEdges = edges;
            mColor = color;
            if (material != null)
            {
                material.color = color;
            }
        }

        public CityTerritoryEdgeData GetNeighbourEdge(int neighbourTerritoryID)
        {
            foreach (var edge in mEdges)
            {
                if (edge.neighbourTerritoryID == neighbourTerritoryID)
                {
                    return edge;
                }
            }
            return null;
        }

        public void SetColor(Color color)
        {
            ApplyColor(color);
        }

        public override void SetAlpha(float alpha)
        {
            var color = mColor;
            color.a = alpha;
            ApplyColor(color);
        }
        
        void ApplyColor(Color color)
        {
            mColor = color;
            material.color = color;
            if (ownerBlock != null)
            {
                subLayer.SetTerritoryColor(index, color);
            }
        }

        public Vector3 cityPosition { get { return mCityPosition; } }
        public List<CityTerritoryEdgeData> edges { get { return mEdges; } }
        public override Color color { get { return mColor; } set { mColor = value; } }

        Color mColor;
        Vector3 mCityPosition;
        List<CityTerritoryEdgeData> mEdges;
    }
}
