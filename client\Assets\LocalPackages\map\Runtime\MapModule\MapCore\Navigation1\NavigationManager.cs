﻿ 



 
 

using UnityEngine;
using System.Threading;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TFW.Map.Nav
{
    public static class NavigationManager
    {
        public static NaviMgr activeNavigationManager => mNavMgr;

        /// <summary>
        /// 导航管理器是否初始化完成, 只有初始化完成后导航功能才可用
        /// </summary>
        public static bool Initialized { get; private set; }

        /// <summary>
        /// 异步初始化地图导航管理器
        /// 注: 在使用前请确认<see cref="Initialized"/>为<c>true</c>
        /// </summary>
        /// <param name="path">NavMesh配置文件路径</param>
        public static void InitAsync(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                Debug.LogError("Empty nav mesh path");
                return;
            }

            if (path.EndsWith("json"))
            {
                Task.Run(() => { CreateNav(MeshCfg.CreateFromJson(path)); });
            }
            else
            {
                MapModuleResourceMgr.LoadTextStreamAsync(path,
                    (str,stream) => { Task.Run(() => { CreateNav(MeshCfg.CreateFromStream(stream)); }); });
            }
        }

        public static void StartInitNavigation(string navDataPath)
        {
            if (mNavMgr == null)
            {
#if UNITY_EDITOR
                var stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
#endif
                //var meshCfg = MeshCfg.CreateMeshCfg(navDataPath);
                MeshCfg.CreateMeshCfgSync(navDataPath, (meshCfg) => { 
#if UNITY_EDITOR
                    stopwatch.Stop();
                    var elapsedTime = stopwatch.ElapsedMilliseconds;
                    Debug.Log($"time: {elapsedTime}");
#endif
                    if (meshCfg != null)
                    {
//#if UNITY_WEBGL
                        if (meshCfg == null)
                        {
                            return;
                        }

                        if (meshCfg.mapWidth == 0)
                        {
                            meshCfg.mapWidth = Map.currentMap.mapWidth;
                            meshCfg.mapHeight = Map.currentMap.mapHeight;
                        }

                        var width = Utils.F2I(meshCfg.mapWidth);
                        var height = Utils.F2I(meshCfg.mapHeight);
                        mNavMgr = NaviMgr.Create(0, 0, width, height, meshCfg, 0);
                        Initialized = true;
//#else
//                    mNavInitThread = new Thread(() => { CreateNav(meshCfg); });
//                    mNavInitThread.Start();
//                    mWaitInit = true;
//#endif
                    }
                });


            }
        }

        public static void UninitNavigation()
        {
            if (mNavMgr != null)
            {
                mNavMgr.OnDestroy();
                mNavMgr = null;
            }

            Initialized = false;
        }

        public static void WaitInitNavigation()
        {
            MapStats.BeginLoading();
            if (mWaitInit)
            {
                mNavInitThread?.Join();
                mNavInitThread = null;
                mWaitInit = false;
            }

            MapStats.EndLoading();
        }

        //localStart:地图空间坐标
        public static void FindPath(Vector3 localStart, Vector3 localEnd, List<Vector3> path, Vector3 origin)
        {
            path.Clear();
            if (mNavMgr != null)
            {
                var coords = mNavMgr.Route(Geo.GeoUtils.Vector3ToCoord(localStart),
                    Geo.GeoUtils.Vector3ToCoord(localEnd));
                for (int i = 0; i < coords.Count; ++i)
                {
                    path.Add(Geo.GeoUtils.CoordToVector3(coords[i]) + origin);
                }
            }
        }

        /// <summary>
        /// 找到从startPosition开始到targetPosition这条线上能到达的估计最近的目标坐标
        /// </summary>
        public static Vector3 FindEstimatedWalkablePosition(Vector3 startPosition, Vector3 targetPosition,
            float testDistance, int maxTryCount = 50)
        {
            return mNavMgr.FindEstimatedWalkablePosition(Utils.ToVector2(startPosition),
                Utils.ToVector2(targetPosition), testDistance, maxTryCount);
        }

        public static void SetRegionState(int regionID, bool enable)
        {
            mNavMgr?.SetRegionState(regionID, enable);
        }

        public static bool GetRegionState(int regionID)
        {
            return mNavMgr == null || mNavMgr.GetRegionState(regionID);
        }

        public static int GetNavMeshRegionID(Vector3 pos)
        {
            return mNavMgr.GetIntersectedRegionID(pos.x, pos.z);
        }

        public static List<int> GetNavMeshRegionIDs(Vector3 center, float radius)
        {
            return mNavMgr.GetIntersectedRegionIDs(center.x, center.z, radius);
        }

        public static void ShowNavMeshRegions(bool show)
        {
            mNavMgr.ShowNavMeshRegions(show);
        }

        private static void CreateNav(MeshCfg mesh)
        {
            if (mesh == null 
                || Map.currentMap == null)
            {
                return;
            }

            if (mesh.mapWidth == 0)
            {
                mesh.mapWidth = Map.currentMap.mapWidth;
                mesh.mapHeight = Map.currentMap.mapHeight;
            }

            var width = Utils.F2I(mesh.mapWidth);
            var height = Utils.F2I(mesh.mapHeight);
            mNavMgr = NaviMgr.Create(0, 0, width, height, mesh, 0);
            Initialized = true;
        }

        private static Thread mNavInitThread;

        private static NaviMgr mNavMgr;

        private static bool mWaitInit;
    }
}