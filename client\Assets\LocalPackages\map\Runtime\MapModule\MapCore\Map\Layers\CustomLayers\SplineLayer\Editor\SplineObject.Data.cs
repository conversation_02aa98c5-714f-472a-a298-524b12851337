﻿ 



 
 


//created by wzw at 2019/6/16

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplineObject
    {
        [System.Flags]
        public enum Attribute
        {
            None = 0,
            Loop = 1,
            Inside = 2,
            OnlyRotate = 4,
            FadeOutEndPoints = 8,
            //only used in k2 demo
            Shadow = 16,
        }

        public class RiverData
        {
            public string stencilMaskMaterialPath;
            public string waterMaterialPath;
            public bool isRiverObject;
            public GameObject waterObject;
            public GameObject stencilMaskObject;
            public int stripeCount = 5;
            public List<float> stripeWidth = new List<float> { 10, 10, 10, 40, 20};
            public List<float> stripeDepth = new List<float> { -5, -10, -15, -20, -25};
        }

        public class ControlPoint
        {
            public Vector3 pos;
            //0 is left, 1 is right
            public Vector3[] tangents = new Vector3[2];
            public int pointCountInSegment = 2;
            public float width = 1.0f;
            public float horizontalTextureCoord;
            public Color color = Color.white;
            public float curveLength = 0;
            public int tileIndex = 0;
        }

        public class EvaluatePoint
        {
            public EvaluatePoint(Vector3 pos, int controlPointIndex, float lengthFromStartControlPoint, bool isBreakPoint)
            {
                this.pos = pos;
                this.controlPointIndex = controlPointIndex;
                this.lengthFromStartControlPoint = lengthFromStartControlPoint;
                this.isBreakPoint = isBreakPoint;
            }

            public Vector3 pos;
            public int controlPointIndex = -1;
            public float lengthFromStartControlPoint = 0;
            public bool isBreakPoint;
        }

        public class ExportSplineSegmentInfo
        {
            public int splineIndex;
            public int startControlPointIndex;
            public int endControlPointIndex;
            public string prefabPath;
            public Vector3 position;
        }

        public SplineObject(int id, List<ControlPoint> controlPoints, Vector3[] meshVertices, int[] meshIndices, string materialPath, float ratio, Attribute attributes, List<ExportSplineSegmentInfo> segmentsInfo, bool visible, float displayRadius, int tileCount, string name, RiverData riverData)
        {
            mID = id;
            mRatio = ratio;
            mTileCount = tileCount;

            bool isInside = CheckDirection(controlPoints);
            if (riverData.isRiverObject)
            {
                isInside = true;
            }
            if (isInside)
            {
                attributes |= SplineObject.Attribute.Inside;
            }
            else
            {
                attributes &= ~SplineObject.Attribute.Inside;
            }

            mAttributes = attributes;
            mControlPoints = controlPoints;
            mMeshVertices = Utils.ToVector3List(meshVertices);
            mMeshIndices = Utils.ToIntList(meshIndices);
            mMaterialPath = materialPath;
            mSegments = segmentsInfo;
            mRiverData = riverData;
            mName = name;

            var rootTransform = Map.currentMap.root.transform;
            mRoot = new GameObject();
            mRoot.transform.SetParent(rootTransform);
            mRoot.SetActive(visible);
            Utils.HideGameObject(mRoot);
            Evaluate();
            CreateControlPointGameObjects();
            SortControlPoints();
            CreateSplineMesh();

            SetDisplayRadius(displayRadius);
        }

        bool CheckDirection(List<ControlPoint> controlPoints)
        {
            List<Vector3> points = new List<Vector3>(controlPoints.Count);
            for (int i = 0; i < controlPoints.Count; ++i)
            {
                points.Add(controlPoints[i].pos);
            }
            if (!Utils.IsPolygonCW(points))
            {
                return true;
            }
            return false;
        }

        void SwapControlPointPosition(int idx)
        {
            int k = mControlPoints.Count - 1 - idx;

            var temp = mControlPoints[idx];
            mControlPoints[idx] = mControlPoints[k];
            mControlPoints[k] = temp;
        }

        void SortControlPoints()
        {
            List<Vector3> controlPoints = new List<Vector3>(mControlPoints.Count);
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                controlPoints.Add(mControlPoints[i].pos);
            }
            if (Utils.IsPolygonCW(controlPoints))
            {
                if (mAttributes.HasFlag(Attribute.Inside))
                {
                    int n = mControlPoints.Count / 2;
                    for (int i = 0; i < n; ++i)
                    {
                        //swap
                        SwapControlPointPosition(i);

                        var temp1 = mControlPointGameObjects[i];
                        int k1 = mControlPointGameObjects.Count - 1 - i;
                        mControlPointGameObjects[i] = mControlPointGameObjects[k1];
                        mControlPointGameObjects[k1] = temp1;
                    }
                }
            }
            else
            {
                if (!mAttributes.HasFlag(Attribute.Inside))
                {
                    int n = mControlPoints.Count / 2;
                    for (int i = 0; i < n; ++i)
                    {
                        //swap
                        SwapControlPointPosition(i);

                        var temp1 = mControlPointGameObjects[i];
                        int k1 = mControlPointGameObjects.Count - 1 - i;
                        mControlPointGameObjects[i] = mControlPointGameObjects[k1];
                        mControlPointGameObjects[k1] = temp1;
                    }
                }
            }
        }

        float TextureCoordToDistance(int controlPointIndex, float texCoord)
        {
            float ratio = GetRatio();
            float distance = texCoord * ratio * GetControlPointWidth(controlPointIndex);
            return distance;
        }

        float DistanceToTextureCoord(int controlPointIndex, float distance)
        {
            float ratio = GetRatio();
            float width = GetControlPointWidth(controlPointIndex);
            float textureCoord = (distance / width) / ratio;
            return textureCoord;
        }

        public ControlPoint GetControlPoint(int controlPointIndex)
        {
            if (controlPointIndex >= 0 && controlPointIndex < mControlPoints.Count)
            {
                return mControlPoints[controlPointIndex];
            }
            return null;
        }

        public void SetAllPointCountInSegment(int count)
        {
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                mControlPoints[i].pointCountInSegment = count;
            }
            CreateSplineMesh();
        }

        public void SetWidth(float width)
        {
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                mControlPoints[i].width = width;
            }
            CreateSplineMesh();
        }

        public void SetPointCountInSegment(int controlPointIndex, int count)
        {
            var cp = GetControlPoint(controlPointIndex);
            if (cp != null)
            {
                if (cp.pointCountInSegment != count)
                {
                    cp.pointCountInSegment = count;
                    CreateSplineMesh();
                }
            }
        }

        public int GetPointCountInSegment(int controlPointIndex)
        {
            var cp = GetControlPoint(controlPointIndex);
            if (cp != null)
            {
                return cp.pointCountInSegment;
            }
            return 0;
        }

        public void SetControlPointWidth(int controlPointIndex, float width)
        {
            var cp = GetControlPoint(controlPointIndex);
            if (cp != null)
            {
                if (!Mathf.Approximately(cp.width, width))
                {
                    cp.width = width;
                    CreateSplineMesh();
                }
            }
        }

        public float GetControlPointWidth(int controlPointIndex)
        {
            var cp = GetControlPoint(controlPointIndex);
            if (cp != null)
            {
                return cp.width;
            }
            Debug.Assert(false);
            return 0;
        }

        public float GetHorizontalTextureCoord(int controlPointIndex)
        {
            var cp = GetControlPoint(controlPointIndex);
            if (cp != null)
            {
                return cp.horizontalTextureCoord;
            }
            Debug.Assert(false);
            return 0;
        }

        public Rect CalculateBounds()
        {
            float minX = float.MaxValue;
            float minZ = float.MaxValue;
            float maxX = float.MinValue;
            float maxZ = float.MinValue;
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                var local = mControlPoints[i].pos;
                if (local.x < minX)
                {
                    minX = local.x;
                }
                if (local.z < minZ)
                {
                    minZ = local.z;
                }
                if (local.x > maxX)
                {
                    maxX = local.x;
                }
                if (local.z > maxZ)
                {
                    maxZ = local.z;
                }
            }

            var bounds = new Rect(minX, minZ, maxX - minX, maxZ - minZ);
            return bounds;
        }

        //返回controlPoint对应的mesh顶点序号
        public int GetVertexIndex(int controlPointIndex)
        {
            if (controlPointIndex == 0)
            {
                return 0;
            }

            int idx = 0;
            for (int i = 1; i <= controlPointIndex; ++i)
            {
                idx += mControlPoints[i].pointCountInSegment;
            }
            idx -= (controlPointIndex - 1);

            if (isLoop)
            {
                idx += mControlPoints[0].pointCountInSegment - 1;
            }

            return idx * 2 - 1;
        }

        public void SetStripeCount(int n)
        {
            if (mRiverData.stripeCount != n)
            {
                int delta = mRiverData.stripeCount - n;
                mRiverData.stripeCount = n;
                if (delta > 0)
                {
                    for (int i = 0; i < delta; ++i)
                    {
                        mRiverData.stripeDepth.RemoveAt(mRiverData.stripeDepth.Count - 1);
                        mRiverData.stripeWidth.RemoveAt(mRiverData.stripeWidth.Count - 1);
                    }
                }
                else
                {
                    for (int i = 0; i < -delta; ++i)
                    {
                        mRiverData.stripeDepth.Add(mRiverData.stripeDepth[mRiverData.stripeDepth.Count - 1]);
                        mRiverData.stripeWidth.Add(mRiverData.stripeWidth[mRiverData.stripeWidth.Count - 1]);
                    }
                }
                CreateSplineMesh();
            }
        }

        public int GetStripeCount()
        {
            return mRiverData.stripeCount;
        }

        public void SetStripeWidth(int index, float width)
        {
            if (index >= 0 && index < mRiverData.stripeWidth.Count)
            {
                if (!Mathf.Approximately(mRiverData.stripeWidth[index], width))
                {
                    mRiverData.stripeWidth[index] = width;
                    CreateSplineMesh();
                }
            }
        }

        public float GetStripeWidth(int index)
        {
            if (index >= 0 && index < mRiverData.stripeWidth.Count)
            {
                return mRiverData.stripeWidth[index];
            }
            return 0;
        }

        public void SetStripeDepth(int index, float depth)
        {
            if (index >= 0 && index < mRiverData.stripeDepth.Count)
            {
                if (!Mathf.Approximately(mRiverData.stripeDepth[index], depth))
                {
                    mRiverData.stripeDepth[index] = depth;
                    CreateSplineMesh();
                }
            }
        }

        public float GetStripeDepth(int index)
        {
            if (index >= 0 && index < mRiverData.stripeDepth.Count)
            {
                return mRiverData.stripeDepth[index];
            }
            return 0;
        }

        public void ShowWater(bool show)
        {
            if (mRiverData.waterObject != null)
            {
                mRiverData.waterObject.SetActive(show);
            }
        }

        public bool IsWaterVisible()
        {
            if (mRiverData.waterObject != null)
            {
                return mRiverData.waterObject.activeSelf;
            }
            return false;
        }

        public void ShowLand(bool show)
        {
            if (mMeshGameObject != null)
            {
                mMeshGameObject.SetActive(show);
            }
        }

        public bool IsLandVisible()
        {
            if (mMeshGameObject != null)
            {
                return mMeshGameObject.activeSelf;
            }
            return false;
        }

        public void ShowStencilMask(bool show)
        {
            if (mRiverData.stencilMaskObject != null)
            {
                mRiverData.stencilMaskObject.SetActive(show);
            }
        }

        public bool IsStencilMaskVisible()
        {
            if (mRiverData.stencilMaskObject != null)
            {
                return mRiverData.stencilMaskObject.activeSelf;
            }
            return false;
        }

        public int id { get { return mID; } }
        
        public string materialPath
        {
            get
            {
                return mMaterialPath;
            }
            set
            {
                mMaterialPath = value;
                var renderer = mMeshGameObject.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mMaterialPath);
                CreateSplineMesh();
            }
        }

        public string waterMaterialPath
        {
            get
            {
                return mRiverData.waterMaterialPath;
            }
            set
            {
                mRiverData.waterMaterialPath = value;
                var renderer = mRiverData.waterObject.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mRiverData.waterMaterialPath);
                CreateSplineMesh();
            }
        }

        public string stencilMaskMaterialPath
        {
            get
            {
                return mRiverData.stencilMaskMaterialPath;
            }
            set
            {
                mRiverData.stencilMaskMaterialPath = value;
                var renderer = mRiverData.stencilMaskObject.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mRiverData.stencilMaskMaterialPath);
                CreateSplineMesh();
            }
        }

        public List<ControlPoint> controlPoints { get { return mControlPoints; } }
        public List<EvaluatePoint> evaluatedPoints { get { return mEvaluatedPoints; } }
        public List<Vector3> meshVertices { get { return mMeshVertices; } }
        public List<int> meshIndices { get { return mMeshIndices; } }
        public bool isLoop
        {
            get
            {
                return mAttributes.HasFlag(Attribute.Loop);
            }
            set
            {
                if (value)
                {
                    mAttributes |= Attribute.Loop;
                }
                else
                {
                    mAttributes &= ~Attribute.Loop;
                }
                Evaluate();
                CreateSplineMesh();
            }
        }
        public bool inside
        {
            get
            {
                return mAttributes.HasFlag(Attribute.Inside);
            }
            set
            {
                if (value)
                {
                    mAttributes |= Attribute.Inside;
                }
                else
                {
                    mAttributes &= ~Attribute.Inside;
                }
                SortControlPoints();
                CreateSplineMesh();
            }
        }
        public bool onlyRotate
        {
            get
            {
                return mAttributes.HasFlag(Attribute.OnlyRotate);
            }
            set
            {
                if (value)
                {
                    mAttributes |= Attribute.OnlyRotate;
                }
                else
                {
                    mAttributes &= ~Attribute.OnlyRotate;
                }
            }
        }

        public bool fadeoutEndPoints
        {
            get
            {
                return mAttributes.HasFlag(Attribute.FadeOutEndPoints);
            }
            set
            {
                if (value)
                {
                    mAttributes |= Attribute.FadeOutEndPoints;
                }
                else
                {
                    mAttributes &= ~Attribute.FadeOutEndPoints;
                }
                CreateSplineMesh();
            }
        }

        public float ratio
        {
            get
            {
                return GetRatio();
            }
            set
            {
                if (!Mathf.Approximately(mRatio, value))
                {
                    mRatio = value;
                    CreateSplineMesh();
                }
            }
        }

        public int tileCount
        {
            get
            {
                return mTileCount;
            }
            set
            {
                if (mTileCount != value)
                {
                    mTileCount = value;
                    CreateSplineMesh();
                }
            }
        }

        public Attribute attributes { get { return mAttributes; } }
        public List<ExportSplineSegmentInfo> segments
        {
            set
            {
                mSegments = value;
            }
            get
            {
                return mSegments;
            }
        }

        public string name { get { return mName; } set { mName = value; } }
        public RiverData riverData { get { return mRiverData; } }

        int mID;
        //ratio 为0则使用贴图的ratio
        float mRatio;
        string mMaterialPath;
        string mName;
        int mTileCount = 1;
        List<ControlPoint> mControlPoints = new List<ControlPoint>();
        List<EvaluatePoint> mEvaluatedPoints = new List<EvaluatePoint>();
        //保存生成资源后的分段信息,用于导出功能
        List<ExportSplineSegmentInfo> mSegments;
        List<Vector3> mMeshVertices;
        List<int> mMeshIndices;
        Attribute mAttributes;

        RiverData mRiverData = new RiverData();
    }
}

#endif