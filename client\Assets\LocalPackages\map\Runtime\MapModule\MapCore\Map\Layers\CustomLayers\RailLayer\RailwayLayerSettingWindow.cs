﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class RailwayLayerSettingWindow : EditorWindow
    {
        public void Show(string layerName)
        {
            mLayerName = layerName;
        }

        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Height", mLayerHeight);
            GUILayout.EndHorizontal();

            mRailwayTotalLength = EditorGUILayout.FloatField("Railway Total Length", mRailwayTotalLength);

            if (GUILayout.Button("Create"))
            {
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = Map.currentMap.CreateRailwayLayer(mLayerName, mLayerWidth, mLayerHeight, 5, mRailwayTotalLength, 50, 10, new Vector3(mLayerWidth * 0.5f, 0, mLayerHeight * 0.5f));
                    layer.asyncLoading = false;

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 || mRailwayTotalLength <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public float mRailwayTotalLength;
        public string mLayerName;
    }
}

#endif