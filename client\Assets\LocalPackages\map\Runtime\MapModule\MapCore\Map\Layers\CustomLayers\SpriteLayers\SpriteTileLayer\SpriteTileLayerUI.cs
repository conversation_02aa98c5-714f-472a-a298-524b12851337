﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(SpriteTileLayerLogic))]
    public class SpriteTileLayerUI : UnityEditor.Editor
    {
        protected void OnEnableImpl()
        {
            mLogic = target as SpriteTileLayerLogic;
            mIndex = mLogic.GetSpriteTemplateIndex();
            mLogic.UpdateGizmoVisibilityState();
        }
        void OnEnable()
        {
            mTileWidthName = "Tile Width";
            mTileHeightName = "Tile Height";
            mXTileCountName = "X Tile Count";
            mZTileCountName = "Z Tile Count";
            mSpriteTemplatesName = "Sprite Templates";
            OnEnableImpl();
        }

        protected void OnDisableImpl()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
                HideIndicator();
            }
        }

        void OnDisable()
        {
            OnDisableImpl();
        }

        protected void OnSceneGUIImpl()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
            {
                mLeftButtonDown = true;
            }
            else if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            if (currentEvent.type == EventType.MouseMove)
            {
                var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                var pos = map.FromScreenToWorldPosition(screenPos);
                var coord = mLogic.layerData.FromWorldPositionToCoordinate(pos);
                pos = mLogic.layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);
                UpdateIndicator(pos);
            }

            if (mLogic.operationType == TileOperationType.kCreateTile)
            {
                if (mLeftButtonDown && currentEvent.alt == false)
                {
                    var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                    SetTile(screenPos);
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operationType == TileOperationType.kRemoveTile)
            {
                if (mLeftButtonDown && currentEvent.alt == false)
                {
                    var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                    RemoveTile(screenPos);
                }
                HandleUtility.AddDefaultControl(0);
            }
        }

        void OnSceneGUI()
        {
            OnSceneGUIImpl();
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                EditorGUILayout.BeginHorizontal("GroupBox");
                var editorMapData = Map.currentMap.data as EditorMapData;
                var names = editorMapData.spriteTemplates.spriteTemplateNames;
                var index = EditorGUILayout.Popup(mSpriteTemplatesName, mIndex, names);
                if (mIndex != index)
                {
                    SetSelection(index);
                }
                else
                {
                    if (mIndex == -1 && editorMapData.GetSpriteTemplateCount() > 0)
                    {
                        SetSelection(0);
                    }
                }
                if (GUILayout.Button("Create"))
                {
                    CreateNewSpriteTemplate();
                }
                if (GUILayout.Button("Edit"))
                {
                    EditSpriteTemplate();
                }
                if (GUILayout.Button("Remove"))
                {
                    RemoveSpriteTemplate();
                }
                EditorGUILayout.EndHorizontal();

                DrawInspectorGUI();

                mLogic.operationType = (TileOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);

                EditorGUILayout.BeginVertical("GroupBox");
                var layerData = map.FindObject(mLogic.layerID) as SpriteTileLayerData;
                EditorGUILayout.LabelField(mTileWidthName, layerData.tileWidth.ToString());
                EditorGUILayout.LabelField(mTileHeightName, layerData.tileHeight.ToString());
                EditorGUILayout.LabelField(mXTileCountName, layerData.horizontalTileCount.ToString());
                EditorGUILayout.LabelField(mZTileCountName, layerData.verticalTileCount.ToString());

                EditorGUILayout.EndVertical();

                if (mExportProperties)
                {
                    if (GUILayout.Button("Export Map Layer Properties"))
                    {
                        var filePath = EditorUtility.SaveFilePanel("Select file", "", "", "json");
                        if (filePath != null && filePath.Length > 0)
                        {
                            JSONExporter.ExportSpriteTileLayer(mLogic.layerData, filePath);
                        }
                    }
                }
            }
        }

        void SetTile(Vector2 screenPos)
        {
            var spriteTemplate = GetSpriteTemplate();
            if (spriteTemplate != null)
            {
                var layerID = mLogic.layerID;
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(layerID) as SpriteTileLayer;
                var layerData = layer.layerData;

                var worldPos = map.FromScreenToWorldPosition(screenPos);
                var coord = layerData.FromWorldPositionToCoordinate(worldPos);
                var centerPos = layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);

                var tileData = new SpriteTileData(spriteTemplate);
                layerData.ClearTile(coord.x, coord.y);
                layerData.SetTile(coord.x, coord.y, tileData);
            }
        }

        void RemoveTile(Vector2 screenPos)
        {
            var layerID = mLogic.layerID;
            var map = SLGMakerEditor.GetMap();
            var layerData = map.FindObject(layerID) as SpriteTileLayerData;

            var worldPos = map.FromScreenToWorldPosition(screenPos);
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);
            layerData.ClearTile(coord.x, coord.y);
        }

        SpriteTemplate GetSpriteTemplate()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
            return spriteTemplate;
        }

        void ShowIndicator()
        {
            var indicator = SLGMakerEditor.instance.colorTileIndicator;
            var editorMapData = Map.currentMap.data as EditorMapData;
            var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
            if (spriteTemplate != null)
            {
                indicator.SetPrefab(spriteTemplate.id, mLogic.layerData);
                indicator.SetActive(true);
                //indicator.SetPosition(new Vector3(1000000, 0, 0));
            }
            else
            {
                indicator.SetActive(false);
            }
        }

        void UpdateIndicator(Vector3 pos)
        {
            if (mLogic.operationType == TileOperationType.kCreateTile)
            {
                ShowIndicator();
            }
            else
            {
                HideIndicator();
            }

            var indicator = SLGMakerEditor.instance.colorTileIndicator;
            indicator.SetPosition(pos);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
            if (spriteTemplate != null)
            {
                indicator.SetColor(spriteTemplate.color);
            }
        }

        void HideIndicator()
        {
            var indicator = SLGMakerEditor.instance.colorTileIndicator;
            indicator.SetPrefab(0, null);
        }

        void OnChangeSpriteTemplate()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
            if (spriteTemplate != null)
            {
                SLGMakerEditor.instance.colorTileIndicator.SetPrefab(spriteTemplate.id, mLogic.layerData);
            }
        }

        protected virtual void CreateNewSpriteTemplate()
        {
            var editorWindow = EditorWindow.GetWindow<CreateSpriteTemplateDialog>("Create Sprite Template");
            EditorUtils.CenterWindow(editorWindow);
            editorWindow.Show(OnCreateSpriteTemplate);
        }

        protected void OnCreateSpriteTemplate()
        {
            ++mIndex;
        }

        void RemoveSpriteTemplate()
        {
            if (EditorUtility.DisplayDialog("Remove Sprite Template", "Are you sure ?", "Yes", "No"))
            {
                if (mIndex >= 0 && Map.currentMap != null)
                {
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
                    var act = new ActionRemoveSpriteTemplate(spriteTemplate.id);
                    ActionManager.instance.PushAction(act, false);

                    if (editorMapData.GetSpriteTemplateCount() == 0)
                    {
                        mIndex = -1;
                    }
                }
            }
        }

        void EditSpriteTemplate()
        {
            if (mIndex >= 0 && Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var spriteTemplate = editorMapData.GetSpriteTemplate(mIndex);
                var window = EditorWindow.GetWindow<SpriteTemplateUI>();
                EditorUtils.CenterWindow(window);
                window.Show(spriteTemplate.id, mShowDefaultTile, OnSpriteTemplateNameChange);
            }
        }

        void SetSelection(int index)
        {
            mIndex = index;
            var editorMapData = Map.currentMap.data as EditorMapData;
            mLogic.selectedSpriteTemplateID = editorMapData.GetSpriteTemplate(mIndex).id;
            OnChangeSpriteTemplate();
        }

        void OnSpriteTemplateNameChange(string newName)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            editorMapData.spriteTemplates.ChangeSpriteTemplateName(mIndex, newName);
        }

        protected virtual void DrawInspectorGUI() { }

        int mIndex = -1;
        bool mLeftButtonDown = false;
        protected bool mShowDefaultTile = true;
        protected SpriteTileLayerLogic mLogic;
        protected string mTileWidthName;
        protected string mTileHeightName;
        protected string mXTileCountName;
        protected string mZTileCountName;
        protected string mSpriteTemplatesName;
        protected bool mExportProperties = true;
    }
}

#endif