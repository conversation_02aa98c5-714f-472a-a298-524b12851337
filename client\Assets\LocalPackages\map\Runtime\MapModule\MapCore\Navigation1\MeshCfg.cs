﻿ 



 
 

using System.Collections.Generic;
using System.IO;
using UnityEngine;
using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    /// <summary>
    /// 导入的向量和三角形数据结构
    /// </summary>
    public class MeshCfg
    {
        public int ID;

        public float mapWidth;

        public float mapHeight;

        public Coord[] Vertices;

        public int[] Triangles;

        /// <summary>
        /// 每个三角形的类型
        /// </summary>
        public ushort[] TriangleTypes;

        public TriangleTypeSetting[] DefaultTypeSettings;

        /// <summary>
        /// 导航网格所有的区域,至少有一个
        /// </summary>
        public NavMeshRegion[] NavMeshRegions = new NavMeshRegion[1];

        public static MeshCfg CreateMeshCfg(string filePath)
        {
            if (filePath.EndsWith("json"))
            {
                return CreateFromJson(filePath);
            }

            return CreateFromBinary(filePath);
        }

        public static void CreateMeshCfgSync(string filePath,System.Action<MeshCfg> callBack)
        {
            if (filePath.EndsWith("json"))
            {
               var meshCfg=CreateFromJson(filePath);
               callBack?.Invoke(meshCfg);
                return;
            }

            CreateFromBinarySync(filePath, callBack);
        }

        

        public static MeshCfg CreateFromStream(Stream stream)
        {
            if (stream == null)
            {
                Debug.LogError("Failed to create mesh from stream: null stream");
                return null;
            }

            using var reader = new BinaryReader(stream);
            var majorVersion = reader.ReadInt32();
            var minorVersion = reader.ReadInt32();
            var cfg = new MeshCfg();

            if (minorVersion >= 4)
            {
                cfg.mapWidth = reader.ReadSingle();
                cfg.mapHeight = reader.ReadSingle();
            }

            int nIndices = reader.ReadInt32();
            cfg.Triangles = new int[nIndices];
            for (int i = 0; i < nIndices; ++i)
            {
                cfg.Triangles[i] = reader.ReadInt32();
            }

            int vertexCount = reader.ReadInt32();
            cfg.Vertices = new Coord[vertexCount];
            Vector3[] vertices = new Vector3[vertexCount];
            for (int i = 0; i < vertexCount; ++i)
            {
                int x = reader.ReadInt32();
                int z = reader.ReadInt32();
                vertices[i] = new Vector3(Utils.I2F(x), 0, Utils.I2F(z));
                cfg.Vertices[i] = new Coord(x, z);
            }

            cfg.ID = 1;

            //version 2
            if (minorVersion >= 2)
            {
                int nTriangles = nIndices / 3;
                //load triangle types
                cfg.TriangleTypes = new ushort[nTriangles];
                for (int i = 0; i < nTriangles; ++i)
                {
                    cfg.TriangleTypes[i] = reader.ReadUInt16();
                }

                int nTypes = reader.ReadInt32();
                cfg.DefaultTypeSettings = new TriangleTypeSetting[nTypes];
                for (int i = 0; i < nTypes; ++i)
                {
                    bool valid = reader.ReadBoolean();
                    if (valid)
                    {
                        cfg.DefaultTypeSettings[i] = new TriangleTypeSetting(reader.ReadUInt16(), reader.ReadBoolean());
                    }
                }
            }

            //version 3
            if (minorVersion >= 3)
            {
                //load regions
                int regionCount = reader.ReadInt32();
                List<int> regionTriangleEndIndex = new List<int>(regionCount);
                List<int> regionIDs = new List<int>(regionCount);
                for (int i = 0; i < regionCount; ++i)
                {
                    regionTriangleEndIndex.Add(reader.ReadInt32());
                    regionIDs.Add(reader.ReadInt32());
                }

                cfg.CreateNavMeshRegions(regionTriangleEndIndex, regionIDs, vertices, cfg.Triangles, false, false);
            }

            reader.Close();

            return cfg;
        }

        public static MeshCfg CreateFromJson(string filePath)
        {
            var texts = string.Empty;
            try
            {
                texts = File.ReadAllText(filePath);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to read json file {filePath}: {e.Message}");
            }

            if (string.IsNullOrEmpty(texts))
            {
                Debug.LogError("Failed to create from json: empty texts");
                return null;
            }

            var meshies = JSONParser.Deserialize(texts) as List<object>;
            if (meshies == null || meshies.Count < 1)
            {
                return null;
            }

            var mesh = meshies[0] as Dictionary<string, object>;
            //read triangles
            object trianglesObj;
            mesh.TryGetValue("triangles", out trianglesObj);
            var type = trianglesObj.GetType();
            List<object> triangles = trianglesObj as List<object>;

            //read vertices
            object verticesObj;
            mesh.TryGetValue("vertices", out verticesObj);
            List<object> vertices = verticesObj as List<object>;

            //read triangle types
            object triangleTypeObj;
            mesh.TryGetValue("triangletypes", out triangleTypeObj);
            List<object> trianglesTypes = triangleTypeObj as List<object>;

            var id = System.Convert.ToInt32(mesh["id"]);

            MeshCfg cfg = new MeshCfg();
            cfg.ID = id;
            cfg.Vertices = new Coord[vertices.Count];
            cfg.Triangles = new int[triangles.Count * 3];
            cfg.TriangleTypes = new ushort[cfg.Triangles.Length];
            for (int i = 0; i < triangles.Count; ++i)
            {
                var triangle = triangles[i] as List<object>;
                for (int k = 0; k < 3; ++k)
                {
                    cfg.Triangles[i * 3 + k] = System.Convert.ToInt32(triangle[k]);
                }
            }

            //设置三角形类型
            if (triangleTypeObj != null)
            {
                for (int i = 0; i < trianglesTypes.Count; ++i)
                {
                    var triangleType = trianglesTypes[i] as List<object>;
                    cfg.TriangleTypes[i] = (ushort) System.Convert.ToInt32(trianglesTypes[i]);
                }
            }

            for (int i = 0; i < vertices.Count; ++i)
            {
                object x;
                object z;
                var vertex = vertices[i] as Dictionary<string, object>;
                vertex.TryGetValue("x", out x);
                vertex.TryGetValue("z", out z);
                cfg.Vertices[i] = new Coord(System.Convert.ToInt32(x), System.Convert.ToInt32(z));
            }

            return cfg;
        }

        private static MeshCfg CreateFromBinary(string filePath)
        {
            var stream = MapModuleResourceMgr.LoadTextStream(filePath, true);
            return CreateFromStream(stream);
        }

        private static void CreateFromBinarySync(string filePath,System.Action<MeshCfg> callBack)
        {
            MapModuleResourceMgr.LoadTextStreamAsync(filePath, (str, stream) => 
            {
                var meshCfg= CreateFromStream(stream);
                callBack?.Invoke(meshCfg);
            }); 
        }

        public void CreateNavMeshRegions(List<int> regionEndTriangleIndex, List<int> regionIDs, Vector3[] vertices,
            int[] triangles, bool createMesh, bool meshVisible)
        {
            int regionCount = regionIDs.Count;
            NavMeshRegions = new NavMeshRegion[regionCount];
            int startIndex = 0;
            for (int i = 0; i < regionCount; ++i)
            {
                TriangleQuadTree quadTree = new TriangleQuadTree();
                var mapSize = new Vector2(mapWidth, mapHeight);
                quadTree.Create("navmesh regions", Vector2.zero, mapSize, vertices, triangles, startIndex * 3,
                    (regionEndTriangleIndex[i] + 1) * 3 - 1, 5, false, createMesh, meshVisible);
                startIndex = regionEndTriangleIndex[i] + 1;
                NavMeshRegions[i] = new NavMeshRegion(quadTree, regionIDs[i]);
            }
        }

        // 生成三角形
        public void GenTriangles(out Triangle[] triangles, /*out Convex[] convexs, */out Vertice[] allVertices)
        {
            int n = Vertices.Length;
            allVertices = new Vertice[n];
            for (int i = 0; i < n; ++i)
            {
                allVertices[i] = new Vertice(i, Vertices[i]);
            }

            int nTriangles = Triangles.Length / 3;
            triangles = new Triangle[nTriangles];
            //convexs = new Convex[nTriangles];
            for (int i = 0; i < nTriangles; ++i)
            {
                ushort triangleType = 0;
                if (TriangleTypes != null)
                {
                    triangleType = TriangleTypes[i];
                }

                var t = new Triangle(i, Triangles[i * 3], Triangles[i * 3 + 1], Triangles[i * 3 + 2], triangleType);
                triangles[i] = t;
            }
        }
    }
}