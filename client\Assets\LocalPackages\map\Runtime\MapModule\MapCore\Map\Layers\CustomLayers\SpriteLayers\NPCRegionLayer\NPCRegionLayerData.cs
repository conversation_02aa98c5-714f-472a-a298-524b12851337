﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class NPCRegionTemplate
    {
        public NPCRegionTemplate(string name, int bandID, Color32 color, int level, int tileType)
        {
            this.name = name;
            this.bandID = bandID;
            this.color = color;
            this.level = level;
            this.tileType = tileType;
        }

        public string name;
        public int bandID;
        public int level;
        public int tileType;
        public Color32 color;
    }

    public class NPCRegionLayerData : MapLayerData
    {
        public class Layer
        {
            public int overridenWidth = 0;
            public int overridenHeight = 0;
            public float overrdenTileWidth = 0;
            public float overrdenTileHeight = 0;
            public string name;
            public int[] gridData;
            public List<NPCRegionTemplate> templates;
            public bool export = true;
        }

        public NPCRegionLayerData(MapLayerDataHeader header, Map map, List<Layer> layers) : base(header, null, map)
        {
            mLayers = layers;
        }

        public override void OnDestroy()
        {
        }

        public void Clear(int layerIndex)
        {
            Layer layer = GetLayer(layerIndex);
            Debug.Assert(layer != null);
            var gridData = layer.gridData;
            for (int i = 0; i < gridData.Length; ++i)
            {
                gridData[i] = 0;
            }
            layer.templates.Clear();
        }

        public int GetTileBandID(int layer, Vector3 pos)
        {
            var coord = FromWorldPositionToCoordinate(layer, pos);
            return GetTileBandID(layer, coord.x, coord.y);
        }

        public int GetTileBandID(int layerIndex, int x, int y)
        {
            Layer layer = GetLayer(layerIndex);
            if (x >= 0 && x < layer.overridenWidth &&
                y >= 0 && y < layer.overridenHeight)
            {
                int idx = y * layer.overridenWidth + x;
                return layer.gridData[idx];
            }
            return 0;
        }

        public void SetTileBandID(int layerIndex, int x, int y, int bandID)
        {
            var layer = GetLayer(layerIndex);
            if (x >= 0 && x < layer.overridenWidth &&
                y >= 0 && y < layer.overridenHeight)
            {
                int idx = y * layer.overridenWidth + x;
                layer.gridData[idx] = bandID;
            }
        }

        public void GetIntersectedRegions(int layerIndex, Vector3 center, float radius, List<int> intersectedRegions)
        {
            float minX = center.x - radius;
            float minZ = center.z - radius;
            float maxX = center.x + radius;
            float maxZ = center.z + radius;

            var minCoord = FromWorldPositionToCoordinate(new Vector3(minX, 0, minZ));
            var maxCoord = FromWorldPositionToCoordinate(new Vector3(maxX, 0, maxZ));

            intersectedRegions.Clear();
            for (int i = minCoord.y; i <= maxCoord.y; ++i)
            {
                for (int j = minCoord.x; j <= maxCoord.x; ++j)
                {
                    var id = GetTileBandID(layerIndex, j, i);
                    if (id != 0 && intersectedRegions.Contains(id) == false)
                    {
                        intersectedRegions.Add(id);
                    }
                }
            }
        }

        public int GetMaxBandID(int layerIndex)
        {
            int maxID = int.MinValue;
            var layer = GetLayer(layerIndex);
            for (int i = 0; i < layer.templates.Count; ++i)
            {
                if (layer.templates[i].bandID > maxID)
                {
                    maxID = layer.templates[i].bandID;
                }
            }
            return maxID;
        }

        public void RemoveNPCRegionTemplate(int layerIndex, int index)
        {
            var layer = GetLayer(layerIndex);
            if (index >= 0 && index < layer.templates.Count)
            {
                layer.templates.RemoveAt(index);
            }
        }

        public NPCRegionTemplate AddNPCRegionTemplate(int layerIndex, string name, int bandID, Color color, int level, int tileType)
        {
            var layer = GetLayer(layerIndex);
            var template = new NPCRegionTemplate(name, bandID, color, level, tileType);
            layer.templates.Add(template);
            return template;
        }

        public NPCRegionTemplate GetNPCRegionTemplateByCoord(int layerIndex, int x, int y)
        {
            var layer = GetLayer(layerIndex);
            if (x >= 0 && x < layer.overridenWidth && y >= 0 && y < layer.overridenHeight)
            {
                int bandID = layer.gridData[y * layer.overridenWidth + x];
                if (bandID >= 0)
                {
                    return GetNPCRegionTemplateByBandID(layerIndex, bandID);
                }
            }

            return null;
        }

        public NPCRegionTemplate GetNPCRegionTemplateByBandID(int layer, int bandID)
        {
            var templates = GetTemplates(layer);
            for (int i = 0; i < templates.Count; ++i)
            {
                if (templates[i].bandID == bandID)
                {
                    return templates[i];
                }
            }

            return null;
        }

        public NPCRegionTemplate GetNPCRegionTemplateByIndex(int layerIndex, int index)
        {
            var layer = GetLayer(layerIndex);
            if (index >= 0 && index < layer.templates.Count)
            {
                return layer.templates[index];
            }
            Debug.LogError($"npc region template {index} not found!");
            return null;
        }

        public List<NPCRegionTemplate> GetTemplates(int layerIndex)
        {
            var layer = GetLayer(layerIndex);
            return layer.templates;
        }

        public Layer GetLayer(int layerIndex)
        {
            if (layerIndex >= 0 && layerIndex < mLayers.Count)
            {
                return mLayers[layerIndex];
            }
            return null;
        }

        public int GetLayerIndex(string layerName)
        {
            for (int i = 0; i < mLayers.Count; ++i)
            {
                if (mLayers[i].name == layerName)
                {
                    return i;
                }
            }
            return -1;
        }

        public bool HasLayer(string name)
        {
            for (int i = 0; i < mLayers.Count; ++i)
            {
                if (mLayers[i].name == name)
                {
                    return true;
                }
            }
            return false;
        }

        public NPCRegionLayerData.Layer AddLayer(string name, int overridenWidth, int overridenHeight, bool export)
        {
            if (overridenWidth == 0)
            {
                overridenWidth = mCols;
            }
            if (overridenHeight == 0)
            {
                overridenHeight = mRows;
            }

            var layer = new Layer();
            layer.gridData = new int[overridenWidth * overridenHeight];
            layer.templates = new List<NPCRegionTemplate>();
            layer.name = name;
            layer.export = export;
            layer.overridenWidth = overridenWidth;
            layer.overridenHeight = overridenHeight;
            layer.overrdenTileWidth = GetLayerWidthInMeter() / overridenWidth;
            layer.overrdenTileHeight = GetLayerHeightInMeter() / overridenHeight;
            mLayers.Add(layer);
            return layer;
        }

        public bool RemoveLayer(int layerIndex)
        {
            if (layerIndex >= 0 && layerIndex < mLayers.Count)
            {
                mLayers.RemoveAt(layerIndex);
                return true;
            }
            return false;
        }

        public string GetLayerName(int layerIndex)
        {
            return mLayers[layerIndex].name;
        }

        public void SetLayerName(int layerIndex, string name)
        {
            mLayers[layerIndex].name = name;
        }

        public int GetLayerTileTypeByPosition(int layerIndex, float x, float z)
        {
            var coord = FromWorldPositionToCoordinate(layerIndex, new Vector3(x, 0, z));
            var temp = GetNPCRegionTemplateByCoord(layerIndex, coord.x, coord.y);
            return temp != null ? temp.tileType : 0;
        }

        public Vector2Int FromWorldPositionToCoordinate(int layerIndex, Vector3 pos)
        {
            var layer = GetLayer(layerIndex);
            float tileWidth = layer.overrdenTileWidth;
            float tileHeight = layer.overrdenTileHeight;
            float dx = pos.x - layerOffset.x;
            float dz = pos.z - layerOffset.z;
            return new Vector2Int(Mathf.FloorToInt(dx / tileWidth), Mathf.FloorToInt(dz / tileHeight));
        }

        public Vector3 FromCoordinateToWorldPosition(int layerIndex, int x, int y)
        {
            var layer = GetLayer(layerIndex);
            float tileWidth = layer.overrdenTileWidth;
            float tileHeight = layer.overrdenTileHeight;
            return new Vector3(x * tileWidth, 0, y * tileHeight) + layerOffset;
        }

        public override Vector2Int FromWorldPositionToCoordinate(Vector3 pos)
        {
            Debug.Assert(false, "not implemented!");
            return Vector2Int.zero;
        }

        public override Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            Debug.Assert(false, "not implemented!");
            return Vector3.zero;
        }

        public int GetLayerWidth(int layerIndex)
        {
            return GetLayer(layerIndex).overridenWidth;
        }

        public int GetLayerHeight(int layerIndex)
        {
            return GetLayer(layerIndex).overridenHeight;
        }

        public override void RefreshObjectsInViewport() { }
        public override bool UpdateViewport(Rect newViewport, float newZoom) { return false; }
        public override bool Contains(int objectID) { return false; }
        public override bool isGameLayer { get { return false; } }
        public List<Layer> layers { get { return mLayers; } }

        List<Layer> mLayers = new List<Layer>();
    }
}
