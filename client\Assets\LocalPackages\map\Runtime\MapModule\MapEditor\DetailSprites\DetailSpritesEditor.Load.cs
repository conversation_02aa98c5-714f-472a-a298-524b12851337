﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class DetailSpritesSetting
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            MemoryStream stream = new MemoryStream(bytes);
            BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var setting = LoadData(reader);

            reader.Close();

            var editor = EditorMap.CreateDetailSprites(setting, setting.mapWidth, setting.mapHeight);
            var editorMap = (Map.currentMap as EditorMap);
            editorMap.SetDetailSprites(editor);
        }

        static config.DetailSpritesSetting LoadData(BinaryReader reader)
        {
            config.DetailSpritesSetting ds = new config.DetailSpritesSetting();
            ds.mapWidth = reader.ReadSingle();
            ds.mapHeight = reader.ReadSingle();
            ds.alpha0Height = reader.ReadSingle();
            ds.alpha1Height = reader.ReadSingle();
            ds.crossfading = reader.ReadBoolean();
            ds.updateScale = reader.ReadBoolean();

            ds.horizontaolGridCount = reader.ReadInt32();
            ds.verticalGridCount = reader.ReadInt32();


            int nGroups = reader.ReadInt32();
            ds.groups = new config.DetailSpriteGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.DetailSpriteGroup();
                ds.groups[i] = group;
                int nSprites = reader.ReadInt32();
                group.detailSpritesGUIDs = new string[nSprites];
                for (int k = 0; k < nSprites; ++k)
                {
                    group.detailSpritesGUIDs[k] = Utils.ReadString(reader);
                }
                group.name = Utils.ReadString(reader);
                group.color = Utils.ReadColor32(reader);
            }

            return ds;
        }
    }
}

#endif