﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        public void Save()
        {
            if (mTiles == null)
            {
                return;
            }

            RefreshMaskTextures(false);

            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.GroundTileMakerVersion);

            //save setting
            SaveSetting(writer);

            //save mask texture
            SaveTextures(writer);

            var data = stream.ToArray();

            string filePath = mEditorAssetOutputFolder + "/" + MapCoreDef.MAP_GROUND_TILE_ASSETS_FILE;
            File.WriteAllBytes(filePath, data);
            writer.Close();

            AssetDatabase.Refresh();
        }

        void SaveSetting(BinaryWriter writer)
        {
            writer.Write(mTiles.Length);
            writer.Write((int)mPaintMode);
            Utils.WriteString(writer, mTileSetName);
            writer.Write(mTileSize);
            int nLODs = lodCount;
            writer.Write(nLODs);
            writer.Write(mCurrentLOD);
            for (int i = 0; i < nLODs; ++i)
            {
                string materialPath = GetMaterialPath(i);
                var materialGuid = AssetDatabase.AssetPathToGUID(materialPath);
                Utils.WriteString(writer, materialGuid);
            }
            Utils.WriteString(writer, mRuntimeAssetOutputFolder);
            Utils.WriteString(writer, mEditorAssetOutputFolder);
            writer.Write(mChannel);
            writer.Write(mStrength);
            writer.Write(mStrengthStep);
            writer.Write(mBrushSize);
            writer.Write(mBrushSizeStep);
            writer.Write(mEdgeWidth);
            writer.Write(mMaskTextureIndex);
            writer.Write(mShowEdge);
            writer.Write(mShowPaintAreaGuideline);
            writer.Write((int)mOperation);
            bool hasCamera = mCamera != null;
            writer.Write(hasCamera);
            if (hasCamera)
            {
                Utils.WriteVector3(writer, mCamera.transform.position);
                Utils.WriteQuaternion(writer, mCamera.transform.rotation);
            }

            writer.Write(mRandomBrushRotation);
            writer.Write(mFixedBrushRotationAngle);

            writer.Write(mShareMesh);
            writer.Write(mPackTextures);
            string atlasShader = AssetDatabase.GetAssetPath(mAtlasShader);
            string guid = AssetDatabase.AssetPathToGUID(atlasShader);
            Utils.WriteString(writer, guid);

            writer.Write(mUseExtraSubmeshMaterialAsFirstMaterial);
            string extraSubmeshMaterialPath = AssetDatabase.GetAssetPath(mExtraSubmeshMaterial);
            string extraSubmeshGuid = AssetDatabase.AssetPathToGUID(extraSubmeshMaterialPath);
            Utils.WriteString(writer, extraSubmeshGuid);

            for (int lod = 0; lod < nLODs; ++lod)
            {
                var maskTextureSetting = GetMaskTextureSetting(lod);
                int nMaskTextures = maskTextureSetting.Count;
                writer.Write(nMaskTextures);
                for (int i = 0; i < nMaskTextures; ++i)
                {
                    var textureSetting = maskTextureSetting[i];
                    writer.Write(textureSetting.initChannelData);
                    writer.Write(textureSetting.normalizeColor);
                    writer.Write(textureSetting.resolution);
                    Utils.WriteString(writer, textureSetting.shaderPropertyName);
                    writer.Write((int)textureSetting.uvChannel);
                }
            }
        }

        void SaveTextures(BinaryWriter writer)
        {
            int nLODs = lodCount;
            writer.Write(nLODs);

            for (int i = 0; i < mTiles.Length; ++i)
            {
                int variationsCount = mTiles[i].variations.Count;
                writer.Write(variationsCount);
                //save variations
                for (int v = 0; v < variationsCount; ++v)
                {
                    var variation = mTiles[i].variations[v];
                    Utils.WriteString(writer, variation.name);
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        var tileLOD = variation.GetLOD(lod);

                        var textures = tileLOD.maskTextures;
                        int nTextures = textures.Count;
                        for (int t = 0; t < nTextures; ++t)
                        {
                            var texturePath = AssetDatabase.GetAssetPath(textures[t].texture);
                            Utils.WriteString(writer, texturePath);
                        }
                    }
                }
            }

        }
    }
}

#endif