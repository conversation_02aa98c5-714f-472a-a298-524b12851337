﻿








/// <summary>
/// ThreadMgr.cs
/// Created by wang<PERSON><PERSON><PERSON> 2017-6-29
/// 线程管理器
/// 1. 管理自行创建的多线程逻辑
/// 2. 需要用到多线程的地方，都应该从本模块创建线程
/// </summary>

using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;

namespace TFW
{
    public static class ThreadMgr
    {
        #region 成员变量

        // 记录主线程
        private static int mMainThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;

        // 线程列表
        private static Dictionary<int, Thread> mThreadMap = new Dictionary<int, Thread>();

        // 主线程对调列表
        private static Queue<Action> mMainFunList = new Queue<Action>();

        #endregion

        private static List<int> removeList = new List<int>();

        /// <summary>
        /// 初始化
        /// </summary>
        public static void Init()
        {
            removeList = new List<int>();
        }

        /// <summary>
        /// 驱动更新
        /// </summary>
        public static void FixedUpdate()
        {
            // 清理残留
            removeList.Clear();
            Dictionary<int, Thread>.Enumerator tIt = mThreadMap.GetEnumerator();
            while (tIt.MoveNext())
            {
                Thread t = tIt.Current.Value;
                if (t.IsAlive)
                    continue;

                t.Abort();
                removeList.Add(tIt.Current.Key);
            }

            removeList.ForEach((id) => { mThreadMap.Remove(id); });

            //还是加下锁
            lock (mMainFunList)
            {
                // 驱动回调
                while (mMainFunList.Count > 0)
                {
                    mMainFunList.Dequeue()?.Invoke();
                }
            }
        }

        /// <summary>m
        /// 当前是否是主线程.
        /// </summary>
        /// <returns><c>true</c> if is main thread; otherwise, <c>false</c>.</returns>
        public static bool IsMainThread()
        {
            return Thread.CurrentThread.ManagedThreadId == mMainThreadId;
        }

        /// <summary>
        /// <summary>
        /// 创建一个线程.
        /// </summary>
        /// <returns>线程id.</returns>
        /// <param name="threadFun">线程执行的函数.</param>
        /// <param name="isBackground">是否是背景线程.</param>
        public static int OpenThread(Action threadFun, bool isBackground = true)
        {
            try
            {
                Thread newThread = new Thread(new ThreadStart(threadFun));
                mThreadMap.Add(newThread.ManagedThreadId, newThread);
                newThread.IsBackground = true;
                newThread.Start();
                return newThread.ManagedThreadId;
            }
            catch (Exception ex)
            {
                Debug.LogWarningFormat("线程创建失败，error = {0}, fun = {1}", ex.Message, threadFun.ToString());
                return 0;
            }
        }

        /// <summary>
        /// Closes the thread.
        /// </summary>
        /// <param name="threadId">Thread identifier.</param>
        public static void CloseThread(int threadId)
        {
            Thread threadOb;
            if (!mThreadMap.TryGetValue(threadId, out threadOb))
                return;

            threadOb.Abort();
            mThreadMap.Remove(threadId);
        }

        /// <summary>
        /// 关闭所有线程.
        /// </summary>
        public static void CloseThreadAll()
        {
            Dictionary<int, Thread>.Enumerator it = mThreadMap.GetEnumerator();
            while (it.MoveNext())
                it.Current.Value.Abort();

            mThreadMap.Clear();
        }

        /// <summary>
        /// 在主线程中执行函数.
        /// 用于次要线程调用Unity或Slua逻辑的时候
        /// 当然，这个是异步的，下一帧update时候触发
        /// </summary>
        /// <param name="fun">Fun.</param>
        public static void RunInMainThread(Action fun)
        {
            //还是加下锁，免得丢Action
            lock (mMainFunList)
            {
                mMainFunList.Enqueue(fun);
            }
        }
    }
}