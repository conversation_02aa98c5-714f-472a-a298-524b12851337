﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public interface IGroundLayerLODSwitchTransitionHandler
    {
        void OnDestroy();
        //开始transition
        //lastLOD:上一个LOD
        //curLOD:切换lod后的最新lod
        void OnStartTransition(int lastLOD, int curLOD, float forceToCameraHeight, Vector3 newViewCenter);
        void OnFinishTransition(int lastLOD, int curLOD, float forceToCameraHeight);
        //update transition
        bool UpdateTransition(int lastLOD, int curLOD, float percentage);
        //返回transition持续的时间
        float GetTransitionDuration();
    }

    public class BlendTerrainLODTransition
    {
        //自定义的lod switch高度
        public float lodSwitchHeight;
        public int startLOD;
        public int endLOD;
        public Ticker transitionTicker = new Ticker();
        public IGroundLayerLODSwitchTransitionHandler lodSwitchHandler;
        public bool started = false;

        public void OnDestroy()
        {
            if (lodSwitchHandler != null)
            {
                lodSwitchHandler.OnDestroy();
            }
        }

        //forceToCameraHeight:强制lod switch过后的相机高度
        public void Start(int startLOD, int endLOD, float forceToCameraHeight, Vector3 newViewCenter)
        {
            this.startLOD = startLOD;
            this.endLOD = endLOD;
            started = true;
            if (lodSwitchHandler != null)
            {
                lodSwitchHandler.OnStartTransition(startLOD, endLOD, forceToCameraHeight, newViewCenter);
                transitionTicker.Start(lodSwitchHandler.GetTransitionDuration());
            }
        }

        public bool Update(float forceToCameraHeight)
        {
            if (lodSwitchHandler != null)
            {
                bool finished = false;
                float duration = lodSwitchHandler.GetTransitionDuration();
                if (duration > 0)
                {
                    finished = transitionTicker.Update(Time.deltaTime);
                    lodSwitchHandler.UpdateTransition(startLOD, endLOD, transitionTicker.percentage);
                }
                else
                {
                    finished = lodSwitchHandler.UpdateTransition(startLOD, endLOD, transitionTicker.percentage);
                }
                if (finished)
                {
                    lodSwitchHandler.OnFinishTransition(startLOD, endLOD, forceToCameraHeight);
                }
                started = !finished;
                return finished;
            }
            return true;
        }
    }

    public partial class BlendTerrainLayer : MapLayerBase
    {
        public void SetLODSwitchHandler(IGroundLayerLODSwitchTransitionHandler handler)
        {
            mTransition.lodSwitchHandler = handler;
        }

        public void SetLODSwitchHeight(float cameraHeight)
        {
            Debug.LogError("todo");
        }

        public void EnableLODSwitchTransition(bool enable)
        {
            mEnableTransition = enable;
        }

        void OnLODChange(int lastLOD, int curLOD, Vector3 newViewCenter)
        {
            if (mForceToCameraHeight > 0)
            {
                //正在强行进行一次lod switch transition,忽略相机高度变化导致的lod切换效果
                return;
            }
            if (mEnableTransition && mTransition.lodSwitchHandler != null)
            {
                int testLOD = curLOD < lastLOD ? lastLOD : curLOD;
                if (mLayerData.IsOneTileLOD(testLOD))
                {
                    StartTransition(lastLOD, curLOD, newViewCenter);
                }
                else
                {
                    mLayerView.active = true;
                }
            }
        }

        bool StartTransition(int lastLOD, int curLOD, Vector3 newViewCenter, float targetCameraHeight = 0)
        {
            if (curLOD < lastLOD)
            {
                mLayerView.active = true;
            }

            mTransition.Start(lastLOD, curLOD, targetCameraHeight, newViewCenter);
            return true;
        }

        //强制在任意高度瞬间切换到目标高度
        public void ForceLODSwitchTransition(Vector3 newViewCenter, float targetCameraHeight)
        {
            if (!mTransition.started)
            {
                int curLOD = mLayerData.GetLODFromCameraHeight(targetCameraHeight);
                int lastLOD = mLayerData.currentLOD;
                bool suc = StartTransition(lastLOD, curLOD, newViewCenter, targetCameraHeight);
                if (suc)
                {
                    mForceToCameraHeight = targetCameraHeight;
                }
            }
        }

        public void UpdateLODTransition()
        {
            if (mTransition.lodSwitchHandler != null && mTransition.started)
            {
                bool finished = mTransition.Update(mForceToCameraHeight);
                if (finished)
                {
                    if (mTransition.startLOD < mTransition.endLOD)
                    {
                        mLayerView.active = false;
                    }
                    mForceToCameraHeight = 0;
                }
            }
        }

        BlendTerrainLODTransition mTransition = new BlendTerrainLODTransition();
        bool mEnableTransition = true;
        float mForceToCameraHeight = 0;
        Vector3 mUpViewCenter;
    }
}
