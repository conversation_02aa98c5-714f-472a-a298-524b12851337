﻿ 



 
 


#if false

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class RenderObjectToTextureTest : MonoBehaviour
    {
        void Start()
        {
            mRenderToTexture = new RenderObjectToTexture(null, null);
        }

        void OnGUI()
        {
#if UNITY_EDITOR
            var camera = SceneView.GetAllSceneCameras()[0];
            sceneCameraPosition = camera.transform.position;

            if (GUILayout.Button("Render"))
            {
                if (renderObject != null)
                {
                    mRenderToTexture.Render(renderObject, renderFront, cameraExtraDistance, null, false, false);
                    renderTexture = mRenderToTexture.renderTexture;
                    //compareObject.GetComponent<Renderer>().sharedMaterial.mainTexture = renderTexture;
                }
            }
#endif
        }

        public GameObject renderObject;
        public GameObject compareObject;
        public RenderTexture renderTexture;
        public Vector3 sceneCameraPosition;
        public float cameraExtraDistance = 10;
        public bool renderFront = true;

        RenderObjectToTexture mRenderToTexture;
    }
}
#endif