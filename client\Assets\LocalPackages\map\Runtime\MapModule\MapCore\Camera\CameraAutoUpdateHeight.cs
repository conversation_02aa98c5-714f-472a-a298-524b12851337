﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public enum CameraAutoUpdateState
    {
        InCityRange,
        InTransitionRange,
        InWorld,
    }

    //根据相机中心点是否在城内来调整相机能到达的最低高度
    public class CameraAutoUpdateHeight : ZoomActionBase
    {
        public CameraAutoUpdateHeight(CameraActionType type) : base(type)
        {
            ignoreAxis = true;
            Init();
        }

        //called every frame
        public void ResetHeight()
        {
        }

        public void SetMinimumHeight(float height, CameraAutoUpdateState state)
        {
            mUpdated = true;
            if (state == CameraAutoUpdateState.InCityRange)
            {
                mIsInCityRange = true;
            }
            else if (state == CameraAutoUpdateState.InTransitionRange)
            {
                mIsInTransitionRange = true;
                if (mMinimumTransitionHeight == 0)
                {
                    mMinimumTransitionHeight = height;
                }
                else
                {
                    mMinimumTransitionHeight = Mathf.Min(mMinimumTransitionHeight, height);
                }
            }
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mCurrentCameraPos = currentCameraPos;
            if (mOn == false || !mUpdated)
            {
                return;
            }

            if (mIsInTransitionRange)
            {
                enabled = true;
            }

            if (mIsInCityRange)
            {
                MapCameraMgr.currentMinimumHeight = MapCameraMgr.cameraSetting.cameraMinHeight;
            }
            else if (mIsInTransitionRange)
            {
                MapCameraMgr.currentMinimumHeight = mMinimumTransitionHeight;
            }
            else
            {
                MapCameraMgr.currentMinimumHeight = MapCameraMgr.cameraSetting.cameraWorldMapMinHeight;
            }

            mUpdated = false;
        }

        public override void PostUpdate()
        {
            base.PostUpdate();
            mMinimumTransitionHeight = 0;
            mIsInCityRange = false;
            mIsInTransitionRange = false;
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            return mCurrentCameraPos;
        }

        public override void OnFinishImpl()
        {
        }

        public bool on { set { mOn = value; } get { return mOn; } }

        bool mOn = true;
        bool mIsInCityRange = false;
        bool mIsInTransitionRange = false;
        bool mUpdated = false;
        float mMinimumTransitionHeight = 0;
        Vector3 mCurrentCameraPos;
    }
}
