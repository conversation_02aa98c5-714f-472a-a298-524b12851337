﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2020/2/17
 */

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class RegionLayerEventHandlers
    {
        public System.Action<int> onDeleteCollision;
        public System.Action<int> onAddCollision;
        public System.Action<int> onMoveCollision;
        public System.Action<int, int> onMoveVertex;
        public System.Action<int, int> onInsertVertex;
        public System.Action<int, int> onRemoveVertex;
        public System.Action<int, PrefabOutlineType> onOutlineChanged;
    }

    //管理地图的特殊障碍区域
    public partial class RegionLayer : MapLayerBase
    {
        public RegionLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
#if UNITY_EDITOR
            var sourceLayer = layerData as config.RegionLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            Material borderMaterial = AssetDatabase.LoadAssetAtPath<Material>(AssetDatabase.GUIDToAssetPath(sourceLayer.borderMaterialGuid));
            Material borderLineMaterial = AssetDatabase.LoadAssetAtPath<Material>(AssetDatabase.GUIDToAssetPath(sourceLayer.borderLineMaterialGuid));
            Material defaultRegionMaterial = AssetDatabase.LoadAssetAtPath<Material>(AssetDatabase.GUIDToAssetPath(sourceLayer.defaultRegionMaterialGuid));

            mLayerData = new RegionLayerData(header, map, defaultRegionMaterial, sourceLayer.borderMinX, sourceLayer.borderMinZ, sourceLayer.borderMaxX, sourceLayer.borderMaxZ, sourceLayer.borderMeshVertices, sourceLayer.borderMeshIndices, borderMaterial, sourceLayer.generateBorderLineMesh, borderLineMaterial, sourceLayer.borderLineWidth, sourceLayer.showBorderLineMesh, sourceLayer.showRegionMesh);
            mLayerView = new RegionLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);

            mLayerData.isLoading = true;
            if (sourceLayer.regions != null)
            {
                int n = sourceLayer.regions.Length;
                for (int i = 0; i < n; ++i)
                {
                    var outlineDatas = new OutlineData[2];
                    var model = sourceLayer.regions[i] as config.RegionData;
                    outlineDatas[0] = new OutlineData(model.outline);
                    Material mtl = AssetDatabase.LoadAssetAtPath<Material>(AssetDatabase.GUIDToAssetPath(model.materialGuid));
                    var region = new RegionData(model.id, map, outlineDatas, sourceLayer.displayVertexRadius, false, model.meshVertices, model.meshIndices, model.vertexColors, model.innerColor, model.outerColor, model.type, model.number, mtl);
                    mLayerData.AddObjectData(region);
                }
            }
            mLayerData.isLoading = false;

            this.displayType = PrefabOutlineType.NavMeshObstacle;
            this.displayVertexRadius = sourceLayer.displayVertexRadius;

            if (sourceLayer.borderMeshVertices.Length > 0)
            {
                mLayerView.UpdateBorderMesh(sourceLayer.borderMeshVertices, sourceLayer.borderMeshIndices, borderMaterial);
            }

            map.AddMapLayer(this);
#endif
        }

        public void SetEventHandlers(RegionLayerEventHandlers eventHandlers)
        {
            mEventHandlers = eventHandlers;
        }

        public void InsertCollisionVertex(PrefabOutlineType type, int dataID, int index, Vector3 vertex)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.InsertVertex(type, index, vertex);
                mLayerView.InsertVertex(type, objectData, index, vertex);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onInsertVertex != null)
                    {
                        mEventHandlers.onInsertVertex(dataID, index);
                    }
                }
            }
        }

        public void RemoveCollsionVertex(PrefabOutlineType type, int dataID, int index)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.RemoveVertex(type, index);
                mLayerView.RemoveVertex(type, objectData, index);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onRemoveVertex != null)
                    {
                        mEventHandlers.onRemoveVertex(dataID, index);
                    }
                }
            }
        }

        public void SetVertexPosition(PrefabOutlineType type, int dataID, int vertexIndex, Vector3 pos)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.SetVertexPosition(type, vertexIndex, pos);
                mLayerView.UpdateVertex(type, dataID, vertexIndex, pos);
            }

            if (mEventHandlers != null)
            {
                if (mEventHandlers.onMoveVertex != null)
                {
                    mEventHandlers.onMoveVertex(dataID, vertexIndex);
                }
            }
        }

        public void MoveObject(PrefabOutlineType type, int dataID, Vector3 offset)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.Move(type, offset);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onMoveCollision != null)
                    {
                        mEventHandlers.onMoveCollision(dataID);
                    }
                }
            }
        }

        public void ClearOutline(PrefabOutlineType type, int dataID)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.ClearOutline(type);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }
        }

        public void SetOutline(PrefabOutlineType type, int dataID, List<Vector3> vertices)
        {
            var objectData = mLayerData.GetObjectData(dataID) as RegionData;
            if (objectData != null)
            {
                objectData.SetOutline(type, vertices);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }
        }

        public void SetVertexDisplayRadius(float radius)
        {
            displayVertexRadius = radius;
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var obj = allObjects[i] as RegionData;
                obj.displayRadius = radius;
            }

            var allViews = mLayerView.allViews;
            foreach (var p in allViews)
            {
                var view = (p.Value as RegionView);
                view.SetVertexDisplayRadius(radius);
            }
        }

        public void SetSelected(int dataID, bool selected)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as RegionData;
            if (collisionData != null)
            {
                collisionData.isSelected = selected;
            }
        }

        public void UpdateColor(PrefabOutlineType type)
        {
            var allObjects = mLayerData.objects;
            foreach (var obj in allObjects)
            {
                var regionData = obj.Value as RegionData;
                if (regionData.hitObstacles)
                {
                    SetDisplayColor(type, regionData.id, Color.magenta);
                }
                else if (!regionData.IsSimplePolygon(type))
                {
                    SetDisplayColor(type, regionData.id, Color.red);
                }
                else if (regionData.isSelected)
                {
                    SetDisplayColor(type, regionData.id, Color.green);   
                }
                else
                {
                    if (regionData.type == RegionType.Inner)
                    {
                        SetDisplayColor(type, regionData.id, new Color32(255, 127, 39, 255));
                    }
                    else
                    {
                        SetDisplayColor(type, regionData.id, new Color32(0, 128, 255, 255));
                    }
                }
            }
        }

        void SetDisplayColor(PrefabOutlineType type, int dataID, Color color)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                var collisionView = (view as RegionView);
                collisionView.SetColor(type, color);
            }
        }

        public void SetMesh(int regionID, Vector3[] meshVertices, int[] meshIndices, Color[] vertexColors)
        {
            var data = mLayerData.GetObjectData(regionID) as RegionData;
            if (data != null)
            {
                data.SetMesh(meshVertices, meshIndices, vertexColors);
                var view = mLayerView.GetObjectView(regionID) as RegionView;
                if (view != null)
                {
                    view.UpdateMesh();
                }
            }
        }

        public void SetMeshVisible(int regionID, bool visible)
        {
            var view = mLayerView.GetObjectView(regionID) as RegionView;
            if (view != null)
            {
                view.SetMeshVisible(visible);
            }
        }

        public void SetBorderLineMeshVisible(int regionID, bool visible)
        {
            var view = mLayerView.GetObjectView(regionID) as RegionView;
            if (view != null)
            {
                view.SetBorderLineMeshVisible(visible);
            }
        }

        public void ShowBorderLineMesh(bool visible)
        {
            layerData.showBorderLineMesh = visible;
            var objects = layerData.objects;
            foreach (var p in objects)
            {
                SetBorderLineMeshVisible(p.Key, visible);
            }
        }

        public void ShowRegionMesh(bool visible)
        {
            layerData.showRegionMesh = visible;
            var objects = layerData.objects;
            foreach (var p in objects)
            {
                SetMeshVisible(p.Key, visible);
            }
        }

        public void SetBorderLineMesh(int regionID, Mesh mesh)
        {
            var view = mLayerView.GetObjectView(regionID) as RegionView;
            if (view != null)
            {
                view.SetBorderLineMesh(mesh, layerData.borderLineMaterial);
            }   
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);

                    if (mEventHandlers != null)
                    {
                        if (mEventHandlers.onAddCollision != null)
                        {
                            mEventHandlers.onAddCollision(objectID);
                        }
                    }

                    return true;
                }
            }
            return false;
        }

        public int GetObjectIDByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    return p.Value.objectDataID;
                }
            }
            return 0;
        }

        public void RemoveObjectByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    RemoveObject(p.Value.objectDataID);
                    break;
                }
            }
        }

        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onDeleteCollision != null)
                    {
                        mEventHandlers.onDeleteCollision(objectDataID);
                    }
                }

                mLayerView.RemoveObjectView(objectDataID);
            }
            return success;
        }

        public void RemoveAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }
        }

        public void ShowObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, 0);
            }
        }

        public void HideObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, false, 0);
            }
        }

        public RegionData GetRegion(int regionID)
        {
            return mLayerData.GetObjectData(regionID) as RegionData;
        }

        public GameObject GetObjectGameObject(int dataID)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
#if UNITY_EDITOR
            mLayerData.RefreshObjectsInViewport();
            CreateBorderLineMesh();
#endif
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        public void Traverse(System.Func<IMapObjectData, bool> visitFunc)
        {
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                if (visitFunc(p.Value))
                {
                    break;
                }
            }
        }

        public bool IntersectWithPolygon(PrefabOutlineType type, List<Vector3> polygon)
        {
            return mLayerData.IntersectWithPolygon(type, polygon);
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            mLayerView.ShowOutline(type);
        }

        public void SetInnerColor(int regionID, Color color)
        {
            var region = GetRegion(regionID);
            if (region != null)
            {
                region.innerColor = color;
            }
        }

        public void SetOuterColor(int regionID, Color color)
        {
            var region = GetRegion(regionID);
            if (region != null)
            {
                region.outerColor = color;
            }
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public List<RegionData> GetRegionsWithNumber(int number)
        {
            return layerData.GetRegionsWithNumber(number);
        }

        public List<RegionData> GetRegionsWithMesh()
        {
            return layerData.GetRegionsWithMesh();
        }

        public Mesh GetRegionMesh(int regionID)
        {
            var view = layerView.GetObjectView(regionID) as RegionView;
            if (view != null)
            {
                return view.GetMesh();
            }
            return null;
        }

        public void SetRegionMaterial(int regionID, Material mtl)
        {
            var region = layerData.GetObjectData(regionID) as RegionData;
            if (region != null)
            {
                region.material = mtl;
                var regionView = layerView.GetObjectView(regionID) as RegionView;
                regionView.SetMaterial(mtl);
            }
        }

        public bool FindMatchRegion(RegionData region, out RegionData matchedRegion)
        {
            matchedRegion = null;
            var regions = GetRegionsWithNumber(region.number);
            if (regions.Count != 2)
            {
                return false;
            }

            if (regions[0] == region)
            {
                matchedRegion = regions[1];
            }
            else
            {
                matchedRegion = regions[0];
            }

            return true;
        }

        public bool FindOuterRegion(RegionData innerRegion, out RegionData outerRegion)
        {
            outerRegion = null;
            var innerRegionVertices = innerRegion.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            var regions = mLayerData.objects;
            foreach (var p in regions)
            {
                var region = p.Value as RegionData;
                var outline = region.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                if (Utils.IsPolygonFullInsideOfPolygon(innerRegionVertices, outline))
                {
                    outerRegion = region;
                    return true;
                }
            }

            return false;
        }

        public string CheckValidation()
        {
            HashSet<int> usedNumbers = new HashSet<int>();
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var region = p.Value as RegionData;

                if (region.type == RegionType.Inner)
                {
                    if (region.number == 0)
                    {
                        return "region number is 0";
                    }

                    if (usedNumbers.Contains(region.number))
                    {
                        return $"region number {region.number} is used!";
                    }
                    usedNumbers.Add(region.number);
                }
            }

            return null;
        }

        public void SetBorderMesh(Vector3[] vertices, int[] indices)
        {
            mLayerData.SetBorderMesh(vertices, indices);
            mLayerView.UpdateBorderMesh(vertices, indices, mLayerData.borderMaterial);
        }

        public void SetBorderMaterial(Material mtl)
        {
            mLayerData.borderMaterial = mtl;
            mLayerView.SetBorderMaterial(mtl);
        }

        public RegionData FindRegion(Vector3 pos)
        {
#if UNITY_EDITOR
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var region = p.Value as RegionData;
                var outline = region.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                if (EditorUtils.PointInPolygon2D(pos, outline))
                {
                    return region;
                }
            }
#endif
            return null;
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public override int lodCount => mLayerData.lodCount;
        public RegionLayerData layerData { get { return mLayerData; } }
        public RegionLayerView layerView { get { return mLayerView; } }

        protected RegionLayerData mLayerData;
        protected RegionLayerView mLayerView;

        public PrefabOutlineType displayType { set; get; }
        public float displayVertexRadius { get; internal set; }
        public Material regionMaterial { set; get; }
        public bool generateBorderMesh { get; set; } = true;

        RegionLayerEventHandlers mEventHandlers;
    }
}
#endif