%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7751037680256717958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1521572340880488857}
  - component: {fileID: 2086025885020405953}
  m_Layer: 0
  m_Name: ObstacleOutline 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1521572340880488857
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7751037680256717958}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2086025885020405953
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7751037680256717958}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -29.203417, y: 0, z: -42.957817}
    - {x: -21.317822, y: 0, z: -38.29355}
    - {x: -11.130792, y: 0, z: -45.789875}
    - {x: 0.16145325, y: 0, z: -60.00744}
    - {x: 0.8679676, y: 0, z: -75.60477}
    - {x: -14.920305, y: 0, z: -77.62158}
    - {x: -26.840103, y: 0, z: -62.07809}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -13.1704, y: 0, z: -80.7463}
    - {x: -14.1708, y: 0, z: -80.4031}
    - {x: -19.7247, y: 0, z: -75.9172}
    - {x: -19.9733, y: 0, z: -75.6623}
    - {x: -27.7115, y: 0, z: -65.5726}
    - {x: -27.9787, y: 0, z: -65.0199}
    - {x: -30.2403, y: 0, z: -56.0268}
    - {x: -30.2484, y: 0, z: -55.993}
    - {x: -30.5599, y: 0, z: -54.6209}
    - {x: -30.5798, y: 0, z: -54.5159}
    - {x: -30.847, y: 0, z: -52.771}
    - {x: -30.862, y: 0, z: -52.6276}
    - {x: -31.0938, y: 0, z: -48.4794}
    - {x: -31.096, y: 0, z: -48.4155}
    - {x: -31.1632, y: 0, z: -43.3358}
    - {x: -30.8684, y: 0, z: -42.4093}
    - {x: -29.0007, y: 0, z: -39.8442}
    - {x: -28.6477, y: 0, z: -39.4972}
    - {x: -24.691, y: 0, z: -36.7187}
    - {x: -23.5618, y: 0, z: -36.4539}
    - {x: -20.6531, y: 0, z: -36.8982}
    - {x: -20.3764, y: 0, z: -36.9679}
    - {x: -15.1882, y: 0, z: -38.8128}
    - {x: -14.715, y: 0, z: -39.0857}
    - {x: -8.8728, y: 0, z: -44.0546}
    - {x: -8.643, y: 0, z: -44.2991}
    - {x: 2.6137, y: 0, z: -59.3393}
    - {x: 2.8962, y: 0, z: -59.9995}
    - {x: 3.2977, y: 0, z: -62.3953}
    - {x: 3.3178, y: 0, z: -62.6839}
    - {x: 2.9917, y: 0, z: -74.8914}
    - {x: 2.3673, y: 0, z: -76.1069}
    - {x: -1.6864, y: 0, z: -79.1783}
    - {x: -2.3354, y: 0, z: -79.4629}
    - {x: -8.9212, y: 0, z: -80.6485}
    - {x: -9.1596, y: 0, z: -80.672}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
