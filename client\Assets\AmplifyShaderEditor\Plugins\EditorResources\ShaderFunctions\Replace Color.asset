%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Replace Color
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17500\n731;73;658;709;498.7031;575.9198;1.332954;True;False\nNode;AmplifyShaderEditor.FunctionSwitch;12;160,-208;Inherit;False;Compare
    Alpha;True;0;2;-1;In 0;In 1;Object;-1;9;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;24;603.7781,-62.30967;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;20;752,-64;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;21;959.2881,-109.705;Inherit;True;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;23;314.2856,-334.9587;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;18;432,48;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;19;304,128;Inherit;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;False;0;1E-05;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;5;288,16;Inherit;False;Fuzziness;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;10;144,16;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;False;0;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;16;416,-160;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;8;528,-432;Inherit;False;Constant;_Color2;Color
    2;0;0;Create;True;0;0;False;0;0,0,0,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ColorNode;6;-608,-192;Inherit;False;Constant;_Color0;Color
    0;0;0;Create;True;0;0;False;0;0,0,0,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;1;-416,-192;Inherit;False;In;5;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;2;-416,0;Inherit;False;From;5;1;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.ColorNode;7;-608,0;Inherit;False;Constant;_Color1;Color
    1;0;0;Create;True;0;0;False;0;0,0,0,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DistanceOpNode;11;-160,-64;Inherit;False;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;14;-176,-176;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;13;-176,-272;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DistanceOpNode;15;0,-240;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;22;-142.4747,-355.8744;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RangedFloatNode;9;144,-96;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;768,-304;Inherit;False;To;5;2;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;4;272,-96;Inherit;False;Range;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1224.459,-102.2181;Inherit;False;True;-1;Out;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nWireConnection;12;0;15;0\nWireConnection;12;1;11;0\nWireConnection;24;0;16;0\nWireConnection;24;1;18;0\nWireConnection;20;0;24;0\nWireConnection;21;0;3;0\nWireConnection;21;1;23;0\nWireConnection;21;2;20;0\nWireConnection;23;0;22;0\nWireConnection;18;0;5;0\nWireConnection;18;1;19;0\nWireConnection;5;0;10;0\nWireConnection;16;0;11;0\nWireConnection;16;1;4;0\nWireConnection;1;0;6;0\nWireConnection;2;0;7;0\nWireConnection;11;0;1;0\nWireConnection;11;1;2;0\nWireConnection;14;0;2;0\nWireConnection;13;0;1;0\nWireConnection;15;0;13;0\nWireConnection;15;1;14;0\nWireConnection;22;0;1;0\nWireConnection;3;0;8;0\nWireConnection;4;0;9;0\nWireConnection;0;0;21;0\nASEEND*/\n//CHKSM=6A90827C59AE8E0E59809A229392575842D7BEF8"
  m_functionName: 
  m_description: Replaces colors from given In which are equal to From to color To
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
