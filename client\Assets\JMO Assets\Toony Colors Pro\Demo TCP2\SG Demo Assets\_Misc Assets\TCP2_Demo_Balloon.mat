%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo_Balloon
  m_Shader: {fileID: -6465566751694194690, guid: edd7abf643fa4bc4e8561d4c280c97cf,
    type: 3}
  m_ShaderKeywords: TCP2_DISABLE_WRAPPED_LIGHT TCP2_REFLECTIONS_FRESNEL TCP2_RIM_LIGHTING_LIGHTMASK
    TCP2_SHADOW_LIGHT_COLOR TCP2_UV_NORMALS_FULL
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: d66eb364b6566a14cbd42b55e6b7008a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d66eb364b6566a14cbd42b55e6b7008a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowBaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DirectIntensityOutline: 1
    - _DstBlend: 0
    - _EmissionChannel: 4
    - _FresnelMax: 1.5
    - _FresnelMin: 0
    - _IndirectIntensity: 1
    - _IndirectIntensityOutline: 0
    - _MatCapMaskChannel: 0
    - _MatCapType: 0
    - _NormalsSource: 0
    - _NormalsUVType: 0
    - _OcclusionChannel: 0
    - _OcclusionStrength: 1
    - _OutlineLightingType: 0
    - _OutlineLightingTypeURP: 0
    - _OutlineMaxWidth: 1
    - _OutlineMinWidth: 1
    - _OutlinePixelSizeType: 0
    - _OutlineTextureLOD: 5
    - _OutlineTextureType: 0
    - _OutlineWidth: 1
    - _RampBands: 4
    - _RampBandsSmoothing: 0.1
    - _RampOffset: 0
    - _RampScale: 1
    - _RampSmooth: 0.1
    - _RampSmoothing: 0.1
    - _RampThreshold: 0.5
    - _RampType: 0
    - _ReceiveShadowsOff: 1
    - _ReflectionMapType: 0
    - _ReflectionSmoothness: 0.5
    - _RenderingMode: 0
    - _RimMax: 1
    - _RimMin: 0.5
    - _ShadowColorLightAtten: 1
    - _SingleIndirectColor: 0
    - _SpecularMapType: 0
    - _SpecularRoughness: 0.5
    - _SpecularToonSize: 0.25
    - _SpecularToonSmoothness: 0.05
    - _SpecularType: 0
    - _SrcBlend: 1
    - _UseAlphaTest: 0
    - _UseEmission: 0
    - _UseFresnelReflections: 1
    - _UseMatCap: 0
    - _UseMatCapMask: 0
    - _UseMobileMode: 0
    - _UseNormalMap: 0
    - _UseOcclusion: 0
    - _UseOutline: 0
    - _UseReflections: 0
    - _UseRim: 0
    - _UseRimLightMask: 1
    - _UseShadowTexture: 0
    - _UseSpecular: 0
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.754717, g: 0.751157, b: 0.751157, a: 1}
    - _Color: {r: 0.78676474, g: 0.78676474, b: 0.78676474, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
    - _SColor: {r: 0.7941176, g: 0.7796498, b: 0.7417059, a: 1}
    - _SpecularColor: {r: 0.75, g: 0.75, b: 0.75, a: 1}
