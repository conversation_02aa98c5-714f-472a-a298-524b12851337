﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveRegionLayer(BinaryWriter writer, RegionLayer layer)
        {
            BeginSection(MapDataSectionType.RegionLayer, writer);
            //版本号
            writer.Write(VersionSetting.RegionLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            float tileWidth = layer.tileWidth;
            float tileHeight = layer.tileHeight;
            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);
            writer.Write(tileWidth);
            writer.Write(tileHeight);

            List<RegionData> regions = layer.GetRegionsWithMesh();
            writer.Write(regions.Count);
            for (int i = 0; i < regions.Count; ++i)
            {
                //asset path
                string regionPrefabPath = layer.GetAssetPathLOD0(SLGMakerEditor.instance.exportFolder, regions[i].number);
                Utils.WriteString(writer, regionPrefabPath);
                //mesh bounds
                var bounds = layer.GetRegionMesh(regions[i].id).bounds;
                Utils.WriteRect(writer, Utils.BoundsToRect(bounds));
                writer.Write(regions[i].number);

                //border line asset path
                string borderLinePrefabPath = layer.GetBorderLinePrefabPathLOD0(SLGMakerEditor.instance.exportFolder, regions[i].number);
                bool hasBorderLine = File.Exists(borderLinePrefabPath);
                writer.Write(hasBorderLine);
                if (hasBorderLine)
                {
                    Utils.WriteString(writer, borderLinePrefabPath);
                }
            }

            //save border
            bool hasBorder = layer.layerData.borderMeshVertices.Length > 0;
            writer.Write(hasBorder);
            if (hasBorder)
            {
                string borderPrefabPath = layer.GetBorderAssetPath(SLGMakerEditor.instance.exportFolder);
                Utils.WriteString(writer, borderPrefabPath);
            }

            return true;
        }
    }
}

#endif