﻿ 



 
 



/*
 * created by wzw at 2019/11/25
 */

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public class PolygonRiverLayerEventHandlers
    {
        public System.Action<int> onDeleteCollision;
        public System.Action<int> onAddCollision;
        public System.Action<int> onMoveCollision;
        public System.Action<int, int> onMoveVertex;
        public System.Action<int, int> onInsertVertex;
        public System.Action<int, int> onRemoveVertex;
        public System.Action<int, PrefabOutlineType> onOutlineChanged;
        public System.Action<int> onGenerateRiver;
    }

    public partial class PolygonRiverLayer : MapLayerBase
    {
        public PolygonRiverLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            SetDirty();

            mBrushManager.OnDestroy();
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.PolygonRiverLayerData;

            mOnlyGenerateLOD0Assets = sourceLayer.onlyGenerateLOD0Assets;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            mLayerData = new PolygonRiverLayerData(header, config, map, sourceLayer.useUV2, sourceLayer.generateOBJ, sourceLayer.expandingTileCount, sourceLayer.riverMaskTextureSaveFolder);
            mLayerView = new PolygonRiverLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mGroundTileTypeClipExceptions = sourceLayer.groundTileTypeClipExceptions;

            mLayerData.isLoading = true;
            if (sourceLayer.collisions != null)
            {
                int n = sourceLayer.collisions.Length;
                for (int i = 0; i < n; ++i)
                {
                    var outlineDatas = new OutlineData[2];
                    var model = sourceLayer.collisions[i] as config.PolygonRiverData;
                    var vertices = Utils.ConvertToVector3List(model.outline);

                    List<PolygonRiverSectionData> sections = new List<PolygonRiverSectionData>();

                    int textureSize = model.textureSize;
                    for (int s = 0; s < model.sections.Length; ++s)
                    {
                        if (textureSize == 0)
                        {
                            textureSize = (int)Mathf.Sqrt(model.sections[s].textureData.Length);
                        }
                        sections.Add(CreateSection(model.sections[s], textureSize));
                    }

                    List<PolygonRiverSplitterData> splitters = new List<PolygonRiverSplitterData>();
                    for (int s = 0; s < model.splitters.Length; ++s)
                    {
                        splitters.Add(CreateSplitter(model.splitters[s]));
                    }

                    var riverData = new PolygonRiverData(model.id, map, vertices, model.radius, textureSize, model.materialPath, sections, splitters, model.hideLOD, model.height, model.generateRiverMaterial);
                    mLayerData.AddObjectData(riverData);
                }
            }
            mLayerData.isLoading = false;

            this.displayType = PrefabOutlineType.NavMeshObstacle;
            this.displayVertexRadius = sourceLayer.displayVertexRadius;
            this.bakedTextureSize = sourceLayer.bakedTextureSize;

            LoadRiverPrefabsInfo(sourceLayer);

            map.AddMapLayer(this);

            SetDirty();

            defaultRiverMaterial = AssetDatabase.LoadAssetAtPath<Material>(EditorUtils.GetDefaultRiverMaterialPath());

            RefreshMaskTexture();
        }

        PolygonRiverSectionData CreateSection(config.PolygonRiverSectionData s, int textureSize)
        {
            PolygonRiverSectionData section = new PolygonRiverSectionData(map.nextCustomObjectID, s.outline, s.textureData, textureSize);
            return section;
        }

        PolygonRiverSplitterData CreateSplitter(config.PolygonRiverSplitterData s)
        {
            PolygonRiverSplitterData splitter = new PolygonRiverSplitterData(s.startPos, s.endPos);
            return splitter;
        }

        public void SetEventHandlers(PolygonRiverLayerEventHandlers eventHandlers)
        {
            mEventHandlers = eventHandlers;
        }

        public PolygonRiverLayerEventHandlers GetEventHandlers()
        {
            return mEventHandlers;
        }

        public void InsertCollisionVertex(PrefabOutlineType type, int dataID, int index, Vector3 vertex)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.InsertVertex(type, index, vertex);
                mLayerView.InsertVertex(type, objectData, index, vertex);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onInsertVertex != null)
                    {
                        mEventHandlers.onInsertVertex(dataID, index);
                    }
                }
            }

            SetDirty();
        }

        public void RemoveCollsionVertex(PrefabOutlineType type, int dataID, int index)
        {
            var riverData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (riverData != null)
            {
                var pos = riverData.GetVertexPos(type, index);
                riverData.RemoveVertex(type, index);
                mLayerView.RemoveVertex(type, riverData, index);

                var splitters = riverData.GetUsedSplitters(pos);
                if (splitters?.Count > 0)
                {
                    var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
                    for (int i = 0; i < splitters.Count; ++i)
                    {
                        var idx = riverData.GetSplitterIndex(splitters[i]);
                        riverData.DeleteSplitter(idx);
                        riverView.DeleteSplitter(idx);
                    }
                }

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onRemoveVertex != null)
                    {
                        mEventHandlers.onRemoveVertex(dataID, index);
                    }
                }
            }

            SetDirty();
        }

        public void SetVertexPosition(PrefabOutlineType type, int dataID, int vertexIndex, Vector3 pos)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.SetVertexPosition(type, vertexIndex, pos);
                mLayerView.UpdateVertex(type, dataID, vertexIndex, pos);
            }

            if (mEventHandlers != null)
            {
                if (mEventHandlers.onMoveVertex != null)
                {
                    mEventHandlers.onMoveVertex(dataID, vertexIndex);
                }
            }

            SetDirty();
        }

        public void MoveObject(PrefabOutlineType type, int dataID, Vector3 offset)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.Move(type, offset);
                mLayerView.Move(dataID, offset);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onMoveCollision != null)
                    {
                        mEventHandlers.onMoveCollision(dataID);
                    }
                }

                SetDirty();
            }
        }

        public void ClearOutline(PrefabOutlineType type, int dataID)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.ClearOutline(type);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void SetOutline(PrefabOutlineType type, int dataID, List<Vector3> vertices)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.SetOutline(type, vertices);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void ClearAllOutlines(PrefabOutlineType type)
        {
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var collision = allObjects[i] as PolygonRiverData;
                collision.ClearOutline(type);
                mLayerView.UpdateView(collision.id);
            }

            SetDirty();
        }

        public void ExpandOutline(PrefabOutlineType type, int dataID, float radius)
        {
            var objectData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (objectData != null)
            {
                objectData.ExpandOutline(type, radius);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void SetVertexDisplayRadius(float radius)
        {
            displayVertexRadius = radius;
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var obj = allObjects[i] as PolygonRiverData;
                obj.displayRadius = radius;
            }

            var allViews = mLayerView.allViews;
            foreach (var p in allViews)
            {
                var view = (p.Value as PolygonRiverView);
                view.SetVertexDisplayRadius(radius);
            }
        }

        public void SetSelected(int dataID, bool selected)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (collisionData != null)
            {
                collisionData.isSelected = selected;
            }
        }

        public void SetIntersectedWithObstacles(int dataID, bool hitObstacle)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (collisionData != null)
            {
                collisionData.hitObstacles = hitObstacle;
            }
        }

        public void UpdateColor(PrefabOutlineType type)
        {
            var allObjects = mLayerData.objects;
            foreach (var obj in allObjects)
            {
                var collisionData = obj.Value as PolygonRiverData;
                if (collisionData.hitObstacles)
                {
                    SetDisplayColor(type, collisionData.id, Color.magenta);
                }
                else if (!collisionData.IsSimplePolygon(type))
                {
                    SetDisplayColor(type, collisionData.id, Color.red);
                }
                else if (collisionData.isSelected)
                {
                    SetDisplayColor(type, collisionData.id, Color.green);
                }
                else
                {
                    SetDisplayColor(type, collisionData.id, new Color(121 / 255.0f, 188 / 255.0f, 1.0f, 1.0f));
                }
            }
        }

        void SetDisplayColor(PrefabOutlineType type, int dataID, Color color)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                var collisionView = (view as PolygonRiverView);
                collisionView.SetColor(type, color);
            }
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);

                    SetDirty();

                    if (mEventHandlers != null)
                    {
                        if (mEventHandlers.onAddCollision != null)
                        {
                            mEventHandlers.onAddCollision(objectID);
                        }
                    }

                    return true;
                }
            }
            return false;
        }

        public int GetObjectIDByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    return p.Value.objectDataID;
                }
            }
            return 0;
        }

        public void RemoveObjectByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    RemoveObject(p.Value.objectDataID);
                    break;
                }
            }
        }

        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onDeleteCollision != null)
                    {
                        mEventHandlers.onDeleteCollision(objectDataID);
                    }
                }

                mLayerView.RemoveObjectView(objectDataID);

                SetDirty();
            }
            return success;
        }

        public void RemoveAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }

            SetDirty();
        }

        public void ShowObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, 0);
            }
        }

        public void HideObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, false, 0);
            }
        }

        public GameObject GetObjectGameObject(int dataID)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
            var editorMap = map as EditorMap;
            //load river materials
            foreach (var p in mLayerData.objects)
            {
                PolygonRiverMaterialSaver.LoadRiverMaterials(this, p.Value as PolygonRiverData, editorMap.riverMaterialsFolder);
            }
            SetShaderLOD(100);
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        public void Traverse(System.Func<IMapObjectData, bool> visitFunc)
        {
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                if (visitFunc(p.Value))
                {
                    break;
                }
            }
        }

        public bool IntersectWithPolygon(PrefabOutlineType type, List<Vector3> polygon)
        {
            return mLayerData.IntersectWithPolygon(type, polygon);
        }

        //for debug
        public void CheckCollision(PrefabOutlineType type, System.Action<PolygonObjectData, PolygonObjectData> onCollide)
        {
            mLayerData.CheckCollision(type, onCollide);
        }

        public void HideOutline()
        {
            mLayerView.HideOutline();
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            mLayerView.ShowOutline(type);
        }

        public List<Vector3> GetOutline(int dataID, PrefabOutlineType type)
        {
            var river = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (river != null)
            {
                return river.GetOutlineVertices(type);
            }
            return null;
        }

        public Texture2D GetTexture(int dataID, int section)
        {
            var river = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (river != null)
            {
                return river.GetSection(section)?.texture;
            }
            return null;
        }

        public void RecreateMesh()
        {
            List<IMapObjectData> allRivers = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allRivers);

            for (int i = 0; i < allRivers.Count; ++i)
            {
                var riverView = mLayerView.GetObjectView(allRivers[i].GetEntityID()) as PolygonRiverView;
                if (riverView != null)
                {
                    riverView.RecreateMesh();
                }
            }
        }

        public void GenerateRiverMesh(int dataID, RiverGenerationParameter param, List<PolygonRiverSectionData> sections)
        {
            var riverData = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            riverData.SetTextureSize(param.textureSize, sections);
            riverData.materialPath = param.mtlPath;

            var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
            if (riverView != null)
            {
                riverView.GenerateMesh(param);
            }

            if (mEventHandlers.onGenerateRiver != null)
            {
                mEventHandlers.onGenerateRiver(dataID);
            }
        }

        public PolygonRiverData GetRiver(int dataID)
        {
            return mLayerData.GetObjectData(dataID) as PolygonRiverData;
        }

        public GameObject GetRiverMeshObject(int dataID, int section)
        {
            var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
            if (riverView != null)
            {
                return riverView.GetMeshObject(section);
            }
            return null;
        }

        public int GetSplitterIndex(int dataID, Vector3 startPos, Vector3 endPos)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                return data.GetSplitterIndex(startPos, endPos);
            }
            return -1;
        }

        public int GetVertexIndex(PrefabOutlineType type, int dataID, Vector3 pos)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                return data.GetVertexIndex(type, pos);
            }
            return -1;
        }

        public Vector3 GetVertexPos(PrefabOutlineType type, int dataID, int vertexIndex)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                return data.GetVertexPos(type, vertexIndex);
            }
            return Vector3.zero;
        }

        public void AddSplitter(int dataID, Vector3 startPos, Vector3 endPos)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                var splitter = new PolygonRiverSplitterData(startPos, endPos);
                data.AddSplitter(splitter);

                var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
                Debug.Assert(riverView != null);
                riverView.AddSplitter(splitter, displayVertexRadius, data.IsSplitterVisible());
            }
        }

        public void InsertSplitter(int dataID, int idx, Vector3 startPos, Vector3 endPos)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                var splitter = new PolygonRiverSplitterData(startPos, endPos);
                data.InsertSplitter(idx, splitter);

                var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
                Debug.Assert(riverView != null);
                riverView.InsertSplitter(idx, splitter, displayVertexRadius, data.IsSplitterVisible());
            }
        }

        public void DeleteSplitter(int dataID, int index)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                data.DeleteSplitter(index);

                var riverView = mLayerView.GetObjectView(dataID) as PolygonRiverView;
                Debug.Assert(riverView != null);
                riverView.DeleteSplitter(index);
            }
        }

        public bool IsIntersectedWithSplitter(int dataID, Vector3 startPos, Vector3 endPos)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                return data.IsIntersectedWithSplitter(startPos, endPos);
            }
            return false;
        }

        public void ShowSplitter(int dataID, bool show)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                bool changed = data.ShowSplitter(show);
                if (changed)
                {
                    mLayerView.ShowSplitter(dataID, show);
                }
            }
        }

        public bool IsSplitterVisible(int dataID)
        {
            var data = mLayerData.GetObjectData(dataID) as PolygonRiverData;
            if (data != null)
            {
                return data.IsSplitterVisible();
            }
            return false;
        }

        public void SetSplitterColor(int dataID, int splitterIndex, Color color)
        {
            var view = mLayerView.GetObjectView(dataID) as PolygonRiverView;
            if (view != null)
            {
                view.SetSplitterColor(splitterIndex, color);
            }
        }

        public int PickSplitter(Vector3 pos, out int riverDataID)
        {
            int splitterIndex = -1;
            riverDataID = -1;
            int selectedRiverID = -1;
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as PolygonRiverData;
                int nSplitters = collisionData.GetSplitterCount();
                for (int i = 0; i < nSplitters; ++i)
                {
                    var splitter = collisionData.GetSplitter(i);
                    if (HitSplitter(pos, splitter))
                    {
                        splitterIndex = i;
                        break;
                    }
                }

                if (splitterIndex >= 0)
                {
                    selectedRiverID = data.GetEntityID();
                    return true;
                }
                return false;
            };

            Traverse(func);
            riverDataID = selectedRiverID;
            return splitterIndex;
        }

        bool HitSplitter(Vector3 pos, PolygonRiverSplitterData splitter)
        {
            var d = splitter.endVertexPosition - splitter.startVertexPosition;
            float len = d.magnitude;
            d.Normalize();
            var p = new Vector3(d.z, 0, -d.x);
            var vDistance = Vector3.Project(pos - splitter.startVertexPosition, p).magnitude;
            bool inYRange = (vDistance <= displayVertexRadius * 0.25f);
            var middle = (splitter.endVertexPosition + splitter.startVertexPosition) * 0.5f;
            var hDistance = Vector3.Project(pos - middle, d).magnitude;
            bool inXRange = (hDistance <= len * 0.5f);
            return inXRange && inYRange;
        }

        public void SetRiverMaterialProperties(int dataID, int section, Material materialProperties)
        {
            var view = mLayerView.GetObjectView(dataID) as PolygonRiverView;
            if (view != null)
            {
                view.SetRiverMaterialProperties(section, materialProperties);
            }
        }

        public Material GetRiverMaterial(int dataID, int section)
        {
            var view = mLayerView.GetObjectView(dataID) as PolygonRiverView;
            if (view != null)
            {
                return view.GetRiverMaterial(section);
            }
            return null;
        }

        public void SetShaderLOD(int lod)
        {
            mShaderLOD = lod;
            mLayerView.SetShaderLOD(lod);
        }

        public int GetShaderLOD()
        {
            return mShaderLOD;
        }

        //生成游戏里的河流资源
        public void GenerateRiverAssetsForGame()
        {
            string projectFolder = SLGMakerEditor.instance.exportFolder;
            if (string.IsNullOrEmpty(projectFolder))
            {
                EditorUtility.DisplayDialog("Error", "Invalid export folder! please set export folder first!", "OK");
                return;
            }

            var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (layer == null)
            {
                return;
            }
            string riverAssetPath = MapCoreDef.GetRiverAssetsFolderPath(projectFolder);
            mRiverPrefabInfos = PolygonRiverAssetGenerator.Generate(riverAssetPath, layer, layer.layerData.useUV2, layer.layerData.generateOBJ);

            foreach (var p in mRiverPrefabInfos)
            {
                map.GetOrCreateModelTemplate(p.Key, p.Value, false, false);
            }

            string riverAssetFolderPath = MapCoreDef.GetRiverAssetsFolderPath(SLGMakerEditor.instance.exportFolder);
            PolygonRiverAssetManifestGenerator.Generate(riverAssetFolderPath, layer);
        }

        void SetDirty()
        {
#if UNITY_EDITOR
            EditorConfig.dirtyFlag |= DirtyMask.PolygonRiverLayer;
#endif
        }

        void LoadRiverPrefabsInfo(config.PolygonRiverLayerData riverLayerData)
        {
            mRiverPrefabInfos = new Dictionary<int, string>();
            bool validRiverPrefabInfo = true;
            var riverPrefabsInfos = riverLayerData.riverPrefabInfos;
            if (riverPrefabsInfos != null)
            {
                for (int i = 0; i < riverPrefabsInfos.Length; ++i)
                {
                    var river = GetRiver(riverPrefabsInfos[i].riverID);
                    var section = river.GetSection(riverPrefabsInfos[i].sectionIndex);
                    if (section == null)
                    {
                        validRiverPrefabInfo = false;
                    }
                    else
                    {
                        mRiverPrefabInfos[section.id] = riverPrefabsInfos[i].prefabPath;
                    }
                }
            }

            if (!validRiverPrefabInfo)
            {
                mRiverPrefabInfos.Clear();
            }
        }

        public PolygonRiverData GetRiverFromSectionID(int sectionID, out int sectionIndex)
        {
            var rivers = mLayerData.objects;
            foreach (var pair in rivers)
            {
                var river = pair.Value as PolygonRiverData;
                int idx = river.GetSectionIndexByID(sectionID);
                if (idx >= 0)
                {
                    sectionIndex = idx;
                    return river;
                }
            }
            sectionIndex = -1;
            return null;
        }

        public void SetRiverHeight(int riverID, float height)
        {
            var river = GetRiver(riverID);
            if (river != null)
            {
                river.height = height;
                var view = mLayerView.GetObjectView(riverID) as PolygonRiverView;
                if (view != null)
                {
                    view.transform.position = river.GetPosition();
                }
            }
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            var removedObjectIDs = mLayerData.Resize(newWidth, newHeight);
            if (removedObjectIDs != null && removedObjectIDs.Count > 0)
            {
                for (int i = 0; i < removedObjectIDs.Count; ++i)
                {
                    mLayerView.RemoveObjectView(removedObjectIDs[i]);
                }
                return true;
            }
            return false;
        }

        public void RevertCollision(int collisionID, PrefabOutlineType outlineType)
        {
            var collisionData = layerData.GetObjectData(collisionID) as PolygonRiverData;
            collisionData.Revert(outlineType);
            var collisionView = layerView.GetObjectView(collisionID) as PolygonRiverView;
            collisionView.Revert(outlineType);
        }

        public void SetGroundTileTypeClipExceptionCount(int n)
        {
            int delta = n - mGroundTileTypeClipExceptions.Count;
            if (delta < 0)
            {
                delta = -delta;
                for (int i = 0; i < delta; ++i)
                {
                    mGroundTileTypeClipExceptions.RemoveAt(mGroundTileTypeClipExceptions.Count - 1);
                }
            }
            else if (delta > 0)
            {
                for (int i = 0; i < delta; ++i)
                {
                    mGroundTileTypeClipExceptions.Add(0);
                }
            }
        }

        //将river的mask贴图数据保存到贴图文件中
        public void FlushMaskTextures()
        {
            List<IMapObjectData> allRivers = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allRivers);

            string folderPath = mLayerData.riverMaskTextureSaveFolderPath;
            folderPath = folderPath.Replace('\\', '/');
            folderPath = Utils.ConvertToUnityAssetsPath(folderPath);
            if (string.IsNullOrEmpty(folderPath))
            {
                folderPath = MapCoreDef.DEFAULT_RIVER_MASK_TEXTURE_FOLDER;
            }

            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            List<string> usedTextureNames = new List<string>();
            for (int i = 0; i < allRivers.Count; ++i)
            {
                var river = allRivers[i] as PolygonRiverData;
                river.FlushMaskTexture(folderPath, false, false, usedTextureNames);
            }

            //remove unused mask textures
            var enumerator = Directory.EnumerateFiles(folderPath, "*.tga", SearchOption.TopDirectoryOnly);
            List<string> unsedTextures = new List<string>();
            foreach (var file in enumerator)
            {
                string fileName = Path.GetFileName(file);
                if (!usedTextureNames.Contains(fileName))
                {
                    unsedTextures.Add(file);
                }
            }

            for (int i = 0; i < unsedTextures.Count; ++i)
            {
                FileUtil.DeleteFileOrDirectory(unsedTextures[i]);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        public void RefreshMaskTexture()
        {
            List<IMapObjectData> allRivers = new List<IMapObjectData>();
            GetAllObjects(allRivers);
            string textureFolder = layerData.riverMaskTextureSaveFolderPath;
            if (string.IsNullOrEmpty(textureFolder))
            {
                textureFolder = MapCoreDef.DEFAULT_RIVER_MASK_TEXTURE_FOLDER;
            }
            foreach (var river in allRivers)
            {
                var r = river as PolygonRiverData;
                r.RefreshMaskTexture(textureFolder);
            }
        }

        public string GetRiverMaskTextureFilePath(int riverID)
        {
            var river = GetRiver(riverID);
            if (river != null)
            {
                string folder = layerData.riverMaskTextureSaveFolderPath;
                if (string.IsNullOrEmpty(folder))
                {
                    folder = MapCoreDef.DEFAULT_RIVER_MASK_TEXTURE_FOLDER;
                }
                
                return folder + "/" + river.GetTextureName(riverID, river.sections[0].id);
            }
            return "";
        }

        public override void UpdateInEditor()
        {
            if (mRiverMaskTextureDirty)
            {
                mRiverMaskTextureDirty = false;
                RefreshMaskTexture();
            }
        }

        public void SetRiverMaskTextureDirty()
        {
            mRiverMaskTextureDirty = true;
        }

        //修改所有河流的材质，保留mask贴图
        public void ChangeAllRiversMaterial(Material newMtl)
        {
            List<IMapObjectData> allRivers = new List<IMapObjectData>();
            GetAllObjects(allRivers);
            foreach (var river in allRivers)
            {
                var data = river as PolygonRiverData;
                data.ChangeMaterial(newMtl);

                var view = mLayerView.GetObjectView(river.GetEntityID()) as PolygonRiverView;
                if (view != null)
                {
                    view.ChangeMaterial(newMtl);
                }
            }
            
        }

        public static string GetAssetName(string folder, string name, int objectID, int section, int lod, string assetExt)
        {
            return folder + "/_" + name + "_" + objectID + "_sec_" + section + MapCoreDef.MAP_PREFAB_LOD_PREFIX + lod + "." + assetExt;
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public PolygonRiverLayerData layerData { get { return mLayerData; } }
        public PolygonRiverLayerView layerView { get { return mLayerView; } }
        public BrushManager brushManager { get { return mBrushManager; } }
        public Dictionary<int, string> riverPrefabInfos { get { return mRiverPrefabInfos; } }
        public bool needGenerateAssets { get { return mRiverPrefabInfos.Count == 0 && mLayerData.objectCount > 0; } }
        public int bakedTextureSize { set { mBakedTextureSize = value; } get { return mBakedTextureSize; } }
        public List<int> groundTileTypeClipExceptions { get { return mGroundTileTypeClipExceptions; } }

        public PrefabOutlineType displayType { set; get; }
        public bool onlyGenerateLOD0Assets { get { return mOnlyGenerateLOD0Assets; } set { mOnlyGenerateLOD0Assets = value; } }
        public float displayVertexRadius { get; internal set; }
        public Material defaultRiverMaterial { get; set; }
        public override int lodCount => mLayerData.lodCount;
        protected PolygonRiverLayerData mLayerData;
        protected PolygonRiverLayerView mLayerView;
        PolygonRiverLayerEventHandlers mEventHandlers;
        BrushManager mBrushManager = new BrushManager();
        int mShaderLOD = 100;
        int mBakedTextureSize = 256;
        List<int> mGroundTileTypeClipExceptions;
        bool mOnlyGenerateLOD0Assets;
        bool mRiverMaskTextureDirty = false;

        //key是river section的id
        //value是section对应的prefab path
        Dictionary<int, string> mRiverPrefabInfos;
    }
}

#endif