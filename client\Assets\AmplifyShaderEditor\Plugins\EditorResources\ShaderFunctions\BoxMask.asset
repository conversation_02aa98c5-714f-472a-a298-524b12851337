%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: BoxMask
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=14205\n585;92;1335;966;841.4062;390.9513;1;True;False\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;3;-770.7323,-83.37894;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0.0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;7;-359.1361,-15.58681;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.AbsOpNode;6;-536.6868,-59.16749;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;15;268.7505,-15.58675;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;12;-99.26636,-15.5865;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;1;-1107.93,-142.7251;Float;False;World
    Position;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;10;-880.4891,142.5947;Float;False;Size;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;4;-1091.939,-4.288156;Float;False;Center;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;17;37.9352,82.87358;Float;False;Falloff;1;3;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DistanceOpNode;13;36.31915,-15.58687;Float;False;2;0;FLOAT3;0,0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;8;-599.6362,161.9639;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0.5,0.5,0.5;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;461.6318,-16.14097;Float;False;True;Output;0;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nWireConnection;3;0;1;0\nWireConnection;3;1;4;0\nWireConnection;7;0;6;0\nWireConnection;7;1;8;0\nWireConnection;6;0;3;0\nWireConnection;15;0;13;0\nWireConnection;15;1;17;0\nWireConnection;12;0;7;0\nWireConnection;13;0;12;0\nWireConnection;8;0;10;0\nWireConnection;0;0;15;0\nASEEND*/\n//CHKSM=B3B832D729B3AA37648FFC3D368D473533835BDC"
  m_functionName: 
  m_description: Box Mask
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
