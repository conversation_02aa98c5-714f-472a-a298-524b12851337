﻿ 



 
 

//created by wzw at 2019/11/25

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class PolygonCollisionLayerData : MapObjectLayerData
    {
        public PolygonCollisionLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map) : base(header, config, map, null)
        {
        }

        public override void RefreshObjectsInViewport()
        {
            foreach (var p in mObjects)
            {
                SetObjectActive(p.Value, true, 0);
            }
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            return false;
        }

        protected override void OnRemoveObjectData(IMapObjectData data)
        {
        }

        protected override void OnAddObjectData(IMapObjectData objectData)
        {
            if (!isLoading)
            {
                SetObjectActiveImpl(objectData, true);
            }
        }

        protected override bool IsAbleToAdd(IMapObjectData objectData) { return true; }

        public bool IntersectWithPolygon(PrefabOutlineType type, List<Vector3> polygon)
        {
#if UNITY_EDITOR
            foreach (var obj in mObjects)
            {
                var collision = obj.Value as PolygonObjectData;
                bool hit = PolygonCollisionCheck.Overlap(collision.GetOutlineVertices(type), polygon);
                if (hit)
                {
                    return true;
                }
            }
#endif
            return false;
        }

        public void CheckCollision(PrefabOutlineType type, System.Action<PolygonObjectData, PolygonObjectData> onCollide)
        {
#if UNITY_EDITOR
            foreach (var obj in mObjects)
            {
                var collisionA = obj.Value as PolygonObjectData;
                foreach (var obj2 in mObjects)
                {
                    var collisionB = obj2.Value as PolygonObjectData;
                    if (collisionA != collisionB)
                    {
                        bool hit = PolygonCollisionCheck.Overlap(collisionA.GetOutlineVertices(type), collisionB.GetOutlineVertices(type));
                        if (hit && onCollide != null)
                        {
                            onCollide(collisionA, collisionB);
                        }
                    }
                }
            }
#endif
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            throw new System.NotImplementedException();
        }

        public override void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects)
        {
            Debug.Assert(false, "todo");
        }

        public void SetSize(float newWidth, float newHeight)
        {
            mTileWidth = newWidth;
            mTileHeight = newHeight;
        }

        public override bool isGameLayer => false;
    }
}