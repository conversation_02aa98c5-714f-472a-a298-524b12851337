﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [Black]
    public static class JSONExporter
    {
        public static string ExportNavMesh(string name, string folder, Vector3[] vertices, int[] indices, ushort[] triangleTypes, Nav.TriangleTypeSetting[] typeSettings)
        {
            string path = folder + "/" + name + ".json";
            if (path != null && path.Length > 0)
            {
                List<object> navMeshList = new List<object>();

                Dictionary<string, object> rootObject = new Dictionary<string, object>();
                var trianglesObject = ExportTriangles(indices);
                var verticesObject = ExportVertices(vertices);
                if (triangleTypes == null)
                {
                    if (indices != null)
                    {
                        triangleTypes = new ushort[indices.Length / 3];
                    }
                    else
                    {
                        triangleTypes = null;
                    }
                }
                var triangleTypesObject = ExportTriangleTypes(triangleTypes);
                rootObject["id"] = 1;
                rootObject["mapwidth"] = Map.currentMap.mapWidth;
                rootObject["mapheight"] = Map.currentMap.mapHeight;
                rootObject["triangles"] = trianglesObject;
                rootObject["vertices"] = verticesObject;
                if (triangleTypesObject != null)
                {
                    rootObject["triangletypes"] = triangleTypesObject;
                    var typeSettingObject = ExportTypeSettings(typeSettings);
                    if (typeSettingObject != null)
                    {
                        rootObject["triangletypesettings"] = typeSettingObject;
                    }
                }

                navMeshList.Add(rootObject);

                var data = JSONParser.Serialize(navMeshList);
                File.WriteAllText(path, data);
            }
            return path;
        }

        public static object ExportTriangles(int[] indices)
        {
            List<int[]> trianglesObject = new List<int[]>();
            if (indices != null)
            {
                int nTriangles = indices.Length / 3;
                for (int i = 0; i < nTriangles; ++i)
                {
                    int[] triple = new int[] { indices[i * 3], indices[i * 3 + 1], indices[i * 3 + 2] };
                    trianglesObject.Add(triple);
                }
            }
            return trianglesObject;
        }

        public static object ExportTriangleTypes(ushort[] triangleTypes)
        {
            List<ushort> triangleTypesObject = new List<ushort>();
            if (triangleTypes != null)
            {
                int nTriangles = triangleTypes.Length;
                for (int i = 0; i < nTriangles; ++i)
                {
                    triangleTypesObject.Add(triangleTypes[i]);
                }
            }
            return triangleTypesObject;
        }

        public static object ExportTypeSettings(Nav.TriangleTypeSetting[] typeSettings)
        {
            if (typeSettings != null)
            {
                List<object> settings = new List<object>();
                for (int i = 0; i < typeSettings.Length; ++i)
                {
                    if (typeSettings[i] != null)
                    {
                        Dictionary<string, object> setting = new Dictionary<string, object>();
                        setting["type"] = typeSettings[i].typeID;
                        setting["walkable"] = typeSettings[i].walkable;
                        settings.Add(setting);
                    }
                }
                return settings;
            }
            return null;
        }

        public static object ExportVertices(Vector3[] vertices)
        {
            List<Dictionary<string, int>> verticesObject = new List<Dictionary<string, int>>();
            if (vertices != null)
            {
                for (int i = 0; i < vertices.Length; ++i)
                {
                    Dictionary<string, int> pos = new Dictionary<string, int>();
                    pos["x"] = Utils.F2I(vertices[i].x);
                    pos["z"] = Utils.F2I(vertices[i].z);

                    verticesObject.Add(pos);
                }
            }
            return verticesObject;
        }

        public static void ExportPropertyTiles(Dictionary<Vector2Int, int> tiles, string filePath)
        {
            if (tiles != null)
            {
                Dictionary<string, object> layerJsonObject = new Dictionary<string, object>();

                Dictionary<string, object> sprites = new Dictionary<string, object>();
                int n = tiles.Count;
                foreach (var p in tiles)
                {
                    int propertySetID = p.Value;
                    var coord = p.Key;
                    var key = string.Format("{0},{1}", coord.x, coord.y);
                    ExportProperty(propertySetID, key, sprites);
                }

                layerJsonObject["Tiles"] = sprites;

                string data = JSONParser.Serialize(layerJsonObject);
                File.WriteAllText(filePath, data);
            }
        }

        public static void ExportSpriteTileLayer(SpriteTileLayerData layerData, string filePath)
        {
            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;

            Dictionary<string, object> layerJsonObject = new Dictionary<string, object>();
            layerJsonObject["X Tile Count"] = cols;
            layerJsonObject["Z Tile Count"] = rows;
            layerJsonObject["Tile Width"] = layerData.tileWidth;
            layerJsonObject["Tile Height"] = layerData.tileHeight;
            layerJsonObject["Name"] = layerData.name;

            Dictionary<string, object> sprites = new Dictionary<string, object>();
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    var tile = layerData.GetTile(j, i) as SpriteTileData;
                    if (tile != null && tile.spriteTemplate != null && !tile.spriteTemplate.isDefault)
                    {
                        var key = string.Format("{0},{1}", j, i);
                        ExportProperty(tile.spriteTemplate.propertySetID, key, sprites);
                    }
                }
            }

            layerJsonObject["Tiles"] = sprites;

            string data = JSONParser.Serialize(layerJsonObject);
            File.WriteAllText(filePath, data);
        }

        static void ExportObjectProperty(PropertyBase prop, Dictionary<string, object> jsonObject)
        {
            //name,
            jsonObject["Name"] = prop.name;
            switch (prop.type)
            {
                case PropertyType.kPropertyBool:
                    {
                        var p = prop as PropertyData<bool>;
                        jsonObject["Value"] = p.value;
                        break;
                    }
                case PropertyType.kPropertyFloat:
                    {
                        var p = prop as PropertyData<float>;
                        jsonObject["Value"] = p.value;
                        break;
                    }
                case PropertyType.kPropertyInt:
                    {
                        var p = prop as PropertyData<int>;
                        jsonObject["Value"] = p.value;
                        break;
                    }
                case PropertyType.kPropertyIntArray:
                    {
                        var p = prop as PropertyData<int[]>;
                        jsonObject["Value"] = p.value;
                        break;
                    }
                case PropertyType.kPropertyVector3:
                    {
                        var p = prop as PropertyData<Vector3>;
                        JsonHelper.WriteVec3(jsonObject, "Value", p.value);
                        break;
                    }
                case PropertyType.kPropertyVector2:
                    {
                        var p = prop as PropertyData<Vector2>;
                        JsonHelper.WriteVec2(jsonObject, "Value", p.value);
                        break;
                    }
                case PropertyType.kPropertyVector4:
                    {
                        var p = prop as PropertyData<Vector4>;
                        JsonHelper.WriteVec4(jsonObject, "Value", p.value);
                        break;
                    }
                case PropertyType.kPropertyColor:
                    {
                        var p = prop as PropertyData<Color>;
                        JsonHelper.WriteColor(jsonObject, "Value", p.value);
                        break;
                    }
                case PropertyType.kPropertyString:
                    {
                        var p = prop as PropertyData<string>;
                        jsonObject["Value"] = p.value;
                        break;
                    }
                default:
                    Debug.Assert(false, "unknown property type");
                    break;
            }
        }

        static void ExportProperty(int propertySetID, string key, Dictionary<string, object> jsonObject)
        {
            var propertySet = Map.currentMap.FindObject(propertySetID) as PropertySet;
            if (propertySet != null)
            {
                var properties = propertySet.properties;
                int n = properties.GetPropertyCount();
                Dictionary<string, object> arr = new Dictionary<string, object>();
                for (int i = 0; i < n; ++i)
                {
                    var prop = properties.GetProperty(i);
                    ExportObjectProperty(prop, arr);
                    jsonObject[key] = arr;
                }
            }
        }
    }
}

#endif