%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &8877792660627732623
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 470446105697604218}
  - component: {fileID: 4716437878192140963}
  m_Layer: 0
  m_Name: ObstacleOutline 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &470446105697604218
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8877792660627732623}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4716437878192140963
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8877792660627732623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: 26.261993, y: 0, z: -33.475693}
    - {x: 33.201866, y: 0, z: -23.621387}
    - {x: 44.5465, y: 0, z: -24.567455}
    - {x: 46.631615, y: 0, z: -29.984297}
    - {x: 45.152725, y: 0, z: -41.067463}
    - {x: 28.932796, y: 0, z: -43.19641}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: 33.7591, y: 0, z: -45.5051}
    - {x: 33.9252, y: 0, z: -45.4925}
    - {x: 35.0714, y: 0, z: -45.3411}
    - {x: 35.1658, y: 0, z: -45.3255}
    - {x: 44.7878, y: 0, z: -43.4238}
    - {x: 45.6674, y: 0, z: -42.906}
    - {x: 46.6931, y: 0, z: -41.6924}
    - {x: 47.021, y: 0, z: -41.0194}
    - {x: 49.2808, y: 0, z: -30.1623}
    - {x: 49.1114, y: 0, z: -29.0816}
    - {x: 46.2421, y: 0, z: -24.0247}
    - {x: 45.3063, y: 0, z: -23.2928}
    - {x: 40.0427, y: 0, z: -21.8203}
    - {x: 39.7758, y: 0, z: -21.771}
    - {x: 37.598, y: 0, z: -21.5702}
    - {x: 37.4801, y: 0, z: -21.564}
    - {x: 35.6064, y: 0, z: -21.5393}
    - {x: 35.4265, y: 0, z: -21.5477}
    - {x: 33.5733, y: 0, z: -21.7463}
    - {x: 33.2453, y: 0, z: -21.8192}
    - {x: 31.492, y: 0, z: -22.4208}
    - {x: 31.3037, y: 0, z: -22.5001}
    - {x: 29.8499, y: 0, z: -23.2325}
    - {x: 29.1604, y: 0, z: -23.9292}
    - {x: 25.0964, y: 0, z: -32.2068}
    - {x: 25.0008, y: 0, z: -32.4549}
    - {x: 24.6059, y: 0, z: -33.8318}
    - {x: 24.5648, y: 0, z: -34.0198}
    - {x: 24.2749, y: 0, z: -35.9249}
    - {x: 24.3117, y: 0, z: -36.5543}
    - {x: 25.431, y: 0, z: -40.6339}
    - {x: 25.5982, y: 0, z: -41.0207}
    - {x: 26.8434, y: 0, z: -43.0595}
    - {x: 27.324, y: 0, z: -43.5492}
    - {x: 30.1081, y: 0, z: -45.3222}
    - {x: 30.9625, y: 0, z: -45.5618}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
