using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using UnityEngine.Serialization;

[FilePath("ProjectSettings/CISystemSettings.asset", FilePathAttribute.Location.ProjectFolder)]
public class CISystemProjectSettings : ScriptableSingleton<CISystemProjectSettings>
{
    public string dingTalkWebhook => this.m_DingTalkWebhook;
    
    public string DingTalkWeChatNotificationTitle => this.m_DingTalkWeChatNotificationTitle;
    
    [SerializeField]
    private string m_DingTalkWebhook;
    
    [FormerlySerializedAs("m_DingTalkNotificationTitle")]
    [SerializeField]
    private string m_DingTalkWeChatNotificationTitle;

    [SerializeField]
    private List<ExcludePackageConfig> m_ExcludePackages;
    
    public IReadOnlyList<string> GetExculePackages(BuildTarget buildTarget)
    {
        var excludePackages = this.m_ExcludePackages.FirstOrDefault(config => config.buildTarget == buildTarget);
        return excludePackages?.excludePackages ?? new List<string>();
    }
    
    private void OnDisable()
    {
        this.Save();
    }

    internal void Save()
    {
        this.Save(true);
    }

    internal SerializedObject GetSerializedObject()
    {
        return new SerializedObject(this);
    }

    [Serializable]
    class ExcludePackageConfig
    {
        [SerializeField]
        public BuildTarget buildTarget;
        
        [SerializeField]
        public List<string> excludePackages;
    }
}

internal static class CISystemSettingsUIElementsRegister
{
    [SettingsProvider]
    public static SettingsProvider CreateCISystemSettingsProvider()
    {
        var provider = new SettingsProvider("Project/CI System", SettingsScope.Project)
        {
            label = "CI System",
            activateHandler = (searchContext, rootElement) =>
            {
                CISystemProjectSettings.instance.hideFlags = HideFlags.None;
                var settings = CISystemProjectSettings.instance.GetSerializedObject();
                var title = new Label()
                {
                    text = "CI System",
                    style = 
                    {
                        fontSize = new StyleLength(20),
                        unityFontStyleAndWeight = FontStyle.Bold,
                    }
                };
                title.AddToClassList("title");
                rootElement.Add(title);

                var properties = new VisualElement()
                {
                    style =
                    {
                        flexDirection = FlexDirection.Column,
                        
                    }
                };
                properties.AddToClassList("property-list");
                rootElement.Add(properties);
                
                properties.Add(new InspectorElement(settings));

                rootElement.Bind(settings);
            },
            deactivateHandler = () =>
            {
                CISystemProjectSettings.instance.Save();
            },

            // Populate the search keywords to enable smart search filtering and label highlighting:
            keywords = new HashSet<string>(new[] { "title", "dingtalk bot Webhook" })
        };

        return provider;
    }
}
