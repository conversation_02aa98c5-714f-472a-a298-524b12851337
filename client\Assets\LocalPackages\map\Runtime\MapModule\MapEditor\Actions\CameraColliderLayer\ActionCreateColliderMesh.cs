﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionCreateColliderMesh : EditorAction
    {
        public ActionCreateColliderMesh(int layerID, int dataID)
        {
            mDataID = dataID;
            mLayerID = layerID;
            var data = Map.currentMap.FindObject(mDataID) as CameraColliderData;
            mOldVertices = data.verticesCopy;
            mOldIndices = data.indicesCopy;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.CreateCollider(mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetCollider(mDataID, mOldVertices, mOldIndices);
            return true;
        }

        Vector3[] mOldVertices;
        int[] mOldIndices;
        int mDataID;
        int mLayerID;
    }
}

#endif