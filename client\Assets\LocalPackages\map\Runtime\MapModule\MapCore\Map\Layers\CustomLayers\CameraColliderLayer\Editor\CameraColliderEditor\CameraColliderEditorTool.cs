﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public abstract class CameraColliderEditorTool
    {
        public CameraColliderEditorTool(CameraColliderEditor editor)
        {
            mEditor = editor;
        }

        public abstract void OnDestroy();
        public abstract void OnEnabled();
        public abstract void OnDisabled();

        public abstract void Update(Event e);
        public abstract void DrawScene();
        public abstract void DrawGUI();

        public virtual void DrawMenu()
        {
            GenericMenu menu = new GenericMenu();
            if (mEditor.selectedObjectID != 0)
            {
                menu.AddItem(new GUIContent("Delete Camera Collider"), false, mEditor.DeleteCameraCollider);
                menu.AddItem(new GUIContent("Clone"), false, mEditor.Clone);
            }
            DrawSubMenu(menu);
            menu.ShowAsContext();
        }

        protected virtual void DrawSubMenu(GenericMenu menu) { }

        public CameraColliderEditor editor { get { return mEditor; } }
        public abstract CameraColliderEditorToolType type { get; }

        protected CameraColliderEditor mEditor;
    }
}


#endif