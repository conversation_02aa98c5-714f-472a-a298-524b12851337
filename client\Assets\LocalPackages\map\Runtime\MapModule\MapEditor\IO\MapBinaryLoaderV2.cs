﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.IO;
using System.IO.Compression;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class MapBinaryLoaderV2 : MapBinaryLoader
    {
        public MapBinaryLoaderV2(int majorVersion) : base(majorVersion)
        {
        }

        public override config.EditorMapData Load(int minorVersion, string mapName, Map map, BinaryReader reader)
        {
            mMinorVersion = minorVersion;
            mEditorData = new config.EditorMapData(0, 0, 80, false, 0, 80, false, MapModule.defaultNavMeshCreateMode, MapModule.defaultGlobalObstacleCreateMode, MapModule.defaultGroundTileSize, MapModule.defaultFrontTileSize, new config.BackgroundSetting(), new Version(this.majorVersion, minorVersion), new Bounds());

            int compressedFileSize = 0;
            int originalFileSize = 0;
            bool compressed = reader.ReadBoolean();

            if (compressed)
            {
                compressedFileSize = reader.ReadInt32();
                originalFileSize = reader.ReadInt32();
                //读取压缩的数据
                var compressedData = reader.ReadBytes(compressedFileSize);
                MemoryStream compressedDataStream = new MemoryStream(compressedData);
                DeflateStream compressedStream = new DeflateStream(compressedDataStream, CompressionMode.Decompress);

                byte[] uncompressedData = new byte[originalFileSize];
                compressedStream.Read(uncompressedData, 0, originalFileSize);
                compressedStream.Close();

                compressedDataStream.Close();

                MemoryStream uncompressedStream = new MemoryStream(uncompressedData);
                reader = new BinaryReader(uncompressedStream);
            }

            LoadSetting(reader);
            LoadNavMeshObstacles(reader);
            LoadSpriteTemplates(reader);
            LoadModelTemplates(reader);
            LoadPropertySets(reader);
            LoadModelProperties(reader);
            LoadPrefabManagers(reader);
            LoadCamera(reader);
            LoadMap(reader);
            LoadMapObstacles(reader);

            return mEditorData;
        }

        void LoadSetting(BinaryReader reader)
        {
            mEditorData.setting.cameraMoveRange = Utils.ReadVector2(reader);
            mEditorData.setting.exportFolder = Utils.ReadString(reader);

            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;

            if (mMinorVersion >= 3)
            {
                mEditorData.setting.saveRiverMaterials = reader.ReadBoolean();
                mEditorData.setting.riverMaterialFolder = Utils.ReadString(reader);
            }

            if (mMinorVersion >= 5)
            {
                LoadLoadRangeData(reader);
            }

            if (mMinorVersion >= 7)
            {
                mEditorData.map.farClipOffset = reader.ReadSingle();
            }

            if (mMinorVersion >= 14)
            {
                mEditorData.map.maxCameraColliderHeight = reader.ReadSingle();
            }
        }

        void LoadLoadRangeData(BinaryReader reader)
        {
            mEditorData.loadRangeData = new config.LoadRangeData();
            mEditorData.loadRangeData.cameraFov = reader.ReadSingle();
            mEditorData.loadRangeData.cameraHeight = reader.ReadSingle();
            mEditorData.loadRangeData.cameraRotationX = reader.ReadSingle();
            mEditorData.loadRangeData.resolution = Utils.ReadVector2(reader);
            mEditorData.loadRangeData.loadRangeScale = Utils.ReadVector2(reader);
        }

        config.PropertySet LoadPropertySet(BinaryReader reader)
        {
            var id = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var properties = LoadProperties(reader);
            var ps = new config.PropertySet(id, properties, name);
            return ps;
        }

        void LoadPropertySets(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var editorData = mEditorData as config.EditorMapData;
            editorData.propertySets.propertySets = new config.PropertySet[n];
            for (int i = 0; i < n; ++i)
            {
                editorData.propertySets.propertySets[i] = LoadPropertySet(reader);
            }
        }

        config.Property LoadOneProperty(BinaryReader reader)
        {
            var propInfo = new config.Property();
            propInfo.name = Utils.ReadString(reader);
            propInfo.type = (PropertyType)reader.ReadInt32();
            if (propInfo.type == PropertyType.kPropertyInt)
            {
                propInfo.value = reader.ReadInt32();
            }
            else if (propInfo.type == PropertyType.kPropertyIntArray)
            {
                propInfo.value = Utils.ReadIntArray(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyFloat)
            {
                propInfo.value = reader.ReadSingle();
            }
            else if (propInfo.type == PropertyType.kPropertyBool)
            {
                propInfo.value = reader.ReadBoolean();
            }
            else if (propInfo.type == PropertyType.kPropertyString)
            {
                propInfo.value = Utils.ReadString(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector3)
            {
                propInfo.value = Utils.ReadVector3(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector2)
            {
                propInfo.value = Utils.ReadVector2(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector4)
            {
                propInfo.value = Utils.ReadVector4(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyColor)
            {
                propInfo.value = Utils.ReadColor32(reader);
            }
            else
            {
                Debug.Assert(false);
            }
            return propInfo;
        }

        config.Properties LoadProperties(BinaryReader reader)
        {
            var pi = new config.Properties();
            int nProperties = reader.ReadInt32();
            for (int i = 0; i < nProperties; ++i)
            {
                var propInfo = LoadOneProperty(reader);
                pi.properties.Add(propInfo);
            }
            return pi;
        }

        void LoadMap(BinaryReader reader)
        {
            mEditorData.mapType = reader.ReadInt32();
            mEditorData.map.width = reader.ReadSingle();
            mEditorData.map.height = reader.ReadSingle();
            mEditorData.map.borderHeight = reader.ReadSingle();
            mEditorData.map.isCircle = reader.ReadBoolean();
            mEditorData.map.backExtendedSize = reader.ReadSingle();
            mEditorData.map.generateNPCSpawnPointsInBorderLine = reader.ReadBoolean();
            if (mMinorVersion >= 15)
            {
                mEditorData.map.removeSameHoles = reader.ReadBoolean();
            }
            if (mMinorVersion >= 8)
            {
                mEditorData.map.navMeshRegionVisible = reader.ReadBoolean();
            }
            if (mMinorVersion >= 9)
            {
                mEditorData.navMeshMode = (NavigationCreateMode)reader.ReadInt32();
                mEditorData.globalObstacleMode = (NavigationCreateMode)reader.ReadInt32();
            }
            if (mMinorVersion >= 10)
            {
                mEditorData.map.groundTileSize = reader.ReadSingle();
                mEditorData.map.frontTileSize = reader.ReadSingle();
            }

            if (mMinorVersion >= 12)
            {
                mEditorData.backgroundSetting.position = Utils.ReadVector3(reader);
                mEditorData.backgroundSetting.scale = Utils.ReadVector3(reader);
                mEditorData.backgroundSetting.texturePath = Utils.ReadString(reader);
            }

            if (mMinorVersion >= 13)
            {
                mEditorData.map.mapDataGenerationRange = Utils.ReadBounds(reader);
            }

            mEditorData.grid.visible = reader.ReadBoolean();
            mEditorData.grid.totalWidth = reader.ReadSingle();
            mEditorData.grid.totalHeight = reader.ReadSingle();
            mEditorData.grid.gridWidth = reader.ReadSingle();
            mEditorData.grid.gridHeight = reader.ReadSingle();
            mEditorData.grid.color = Utils.ReadColor32(reader);
            mEditorData.viewCenter = Utils.ReadVector3(reader);
            mEditorData.viewportSize = Utils.ReadVector2(reader);

            LoadMapLayers(reader);
            LoadMapLODConfig(reader);
        }

        void LoadMapLODConfig(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.lodConfig.lods = new config.MapLOD[n];
            for (int i = 0; i < n; ++i)
            {
                var lod = new config.MapLOD();
                mEditorData.map.lodConfig.lods[i] = lod;
                lod.cameraHeight = reader.ReadSingle();
                lod.viewWidth = reader.ReadSingle();
                lod.viewHeight = reader.ReadSingle();
                int unitCount = reader.ReadInt32();
                lod.displayingUnits = new List<config.MapLODUnit>();
                for (int k = 0; k < unitCount; ++k)
                {
                    string unit = Utils.ReadString(reader);
                    string relation = "";
                    if (mMinorVersion >= 6)
                    {
                        relation = Utils.ReadString(reader);
                    }
                    var mapLODUnit = new config.MapLODUnit(unit, relation);
                    lod.displayingUnits.Add(mapLODUnit);
                }
                if (mMinorVersion >= 4)
                {
                    lod.name = Utils.ReadString(reader);
                }
                else
                {
                    lod.name = MapLayerLODConfig.GetDefaultLODName(i);
                }
                if (mMinorVersion >= 6)
                {
                    lod.showTerritory = reader.ReadBoolean();
                }
            }
        }

        void LoadCamera(BinaryReader reader)
        {
            var cameraInfo = new config.Camera();
            cameraInfo.position = Utils.ReadVector3(reader);
            cameraInfo.rotation = Utils.ReadQuaternion(reader);
            cameraInfo.position = new Vector3(3600, 1000, 3600);
            cameraInfo.rotation = Quaternion.Euler(new Vector3(90, 0, 0));
            cameraInfo.orthographic = reader.ReadBoolean();
            cameraInfo.orthongonalSize = reader.ReadSingle();
            cameraInfo.verticalFov = reader.ReadSingle();

            mEditorData.map.camera = cameraInfo;
        }

        config.SpriteTileLayerData LoadSpriteTileLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            int nRows = reader.ReadInt32();
            int nCols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();
            var tiles = new int[nRows * nCols];

            for (int i = 0; i < nRows; ++i)
            {
                for (int j = 0; j < nCols; ++j)
                {
                    var spriteTemplateID = reader.ReadInt32();
                    int idx = i * nCols + j;
                    tiles[idx] = spriteTemplateID;
                }
            }

            var layer = new config.SpriteTileLayerData(layerID, name, offset, null, nRows, nCols, tileWidth, tileHeight, (GridType)gridType, tiles);
            return layer;
        }

        bool LoadMapObjectBaseData(BinaryReader reader, config.MapObjectData data)
        {
            bool isDefaultRotation;
            bool isDefaultScale;

            data.SetID(reader.ReadInt32());

            data.flag = reader.ReadInt32();


            if (data.id != 0)
            {
                data.position = Utils.ReadVector3(reader);
                isDefaultRotation = reader.ReadBoolean();
                if (!isDefaultRotation)
                {
                    data.rotation = Utils.ReadQuaternion(reader);
                }
                isDefaultScale = reader.ReadBoolean();
                if (!isDefaultScale)
                {
                    data.scale = Utils.ReadVector3(reader);
                }
                return true;
            }
            return false;
        }

        bool LoadGridObjectBaseData(BinaryReader reader, config.GridMapObjectData data)
        {
            bool valid = LoadMapObjectBaseData(reader, data);
            if (valid)
            {
                data.isDefaultPosition = reader.ReadBoolean();
            }
            return valid;
        }

        config.ModelData LoadModelData(BinaryReader reader)
        {
            var modelData = new config.ModelData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            return modelData;
        }

        void LoadMapLayers(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.mapLayers = new config.MapLayerData[n];
            for (int i = 0; i < n; ++i)
            {
                config.MapLayerData layer = null;
                var layerType = reader.ReadInt32();
                var layerID = reader.ReadInt32();
                var name = Utils.ReadString(reader);

                var offset = Utils.ReadVector3(reader);
                bool active = reader.ReadBoolean();

                if (layerType == MapLayerType.kSpriteTileLayer)
                {
                    layer = LoadSpriteTileLayerData(reader, layerID, name, offset);
                }
                else
                {
                    Debug.Assert(false, $"Unknown map layer {layerType}");
                }
                layer.active = active;

                mEditorData.map.mapLayers[i] = layer;
            }
        }

        void LoadSpriteTemplates(BinaryReader reader)
        {
            int nSpriteTemplates = reader.ReadInt32();
            var editorData = mEditorData as config.EditorMapData;
            editorData.spriteTemplates.templates = new config.SpriteTemplate[nSpriteTemplates];
            for (int i = 0; i < nSpriteTemplates; ++i)
            {
                editorData.spriteTemplates.templates[i] = LoadSpriteTemplate(reader);
            }
        }

        config.SpriteTemplate LoadSpriteTemplate(BinaryReader reader)
        {
            var id = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var width = reader.ReadInt16();
            var height = reader.ReadInt16();
            var color = Utils.ReadColor32(reader);
            var propertySetID = reader.ReadInt32();

            config.SpriteTemplate spriteTemplate = new config.SpriteTemplate(id, propertySetID);

            spriteTemplate.width = width;
            spriteTemplate.height = height;
            spriteTemplate.color = color;
            spriteTemplate.name = name;
            return spriteTemplate;
        }

        void LoadPrefabManagers(BinaryReader reader)
        {
            mEditorData.modelLayerPrefabManager = LoadPrefabManager(reader);
        }

        config.PrefabManager LoadPrefabManager(BinaryReader reader)
        {
            var prefabManager = new config.PrefabManager();
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();

            int groupID = -1;
            if (mMinorVersion >= 11)
            {
                groupID = reader.ReadInt32();
            }
            group.name = Utils.ReadString(reader);

            group.color = Utils.ReadColor32(reader);

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mPathMapper.Unmap(subgroupPrefabPath);
                }

            }
            return group;
        }

        void LoadModelProperties(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.modelPropertyManager.modelProperties = new KeyValuePair<string, int>[n];
            for (int i = 0; i < n; ++i)
            {
                string assetGUID = Utils.ReadString(reader);
                int propertySetID = reader.ReadInt32();
                mEditorData.modelPropertyManager.modelProperties[i] = new KeyValuePair<string, int>(assetGUID, propertySetID);
            }
        }

        void LoadModelTemplates(BinaryReader reader)
        {
            int nModelTemplates = reader.ReadInt32();
            mEditorData.modelTemplates.modelTemplates = new config.ModelTemplate[nModelTemplates];
            for (int i = 0; i < nModelTemplates; ++i)
            {
                mEditorData.modelTemplates.modelTemplates[i] = LoadMeshModelTemplate(reader);
            }
        }

        void LoadModelTemplateBase(config.ModelTemplate temp, BinaryReader reader)
        {
            var id = reader.ReadInt32();
            temp.SetID(id);

            temp.bounds = Utils.ReadBounds(reader);
            string prefabPath = Utils.ReadString(reader);
            temp.prefabPath = mPathMapper.Unmap(prefabPath);


            temp.isTileModelTemplate = reader.ReadBoolean();


            temp.generated = reader.ReadBoolean();

            temp.preload = reader.ReadBoolean();

        }

        config.ModelTemplate LoadMeshModelTemplate(BinaryReader reader)
        {
            var template = new config.ModelTemplate();
            LoadModelTemplateBase(template, reader);
            return template;
        }

        void LoadNavMeshObstacles(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.navMeshObstacles.obstacles = new config.NavMeshObstacle[n];
            for (int i = 0; i < n; ++i)
            {
                var ob = new config.NavMeshObstacle();
                ob.assetGUID = Utils.ReadString(reader);
                int nVertices = reader.ReadInt32();
                ob.outlineVertices = new Vector3[nVertices];
                for (int j = 0; j < nVertices; ++j)
                {
                    ob.outlineVertices[j] = Utils.ReadVector3(reader);
                }
                mEditorData.navMeshObstacles.obstacles[i] = ob;
            }
        }

        void LoadMapObstacles(BinaryReader reader)
        {
            LoadLocalObstacles(reader);

            LoadGlobalObstacles(reader);
        }

        void LoadLocalObstacles(BinaryReader reader)
        {
            var localObstacleManager = new config.MapLocalObstacleManager();
            mEditorData.localObstacleManager = localObstacleManager;
            localObstacleManager.regionWidth = reader.ReadSingle();
            localObstacleManager.regionHeight = reader.ReadSingle();
            if (localObstacleManager.regionWidth == 0)
            {
                localObstacleManager.regionWidth = MapModule.defaultFrontTileSize;
            }
            if (localObstacleManager.regionHeight == 0)
            {
                localObstacleManager.regionHeight = MapModule.defaultFrontTileSize;
            }
            int tileCount = reader.ReadInt32();
            localObstacleManager.tiles = new int[tileCount];
            for (int i = 0; i < tileCount; ++i)
            {
                localObstacleManager.tiles[i] = reader.ReadInt32();
            }

            int obstacleCount = reader.ReadInt32();
            localObstacleManager.obstacles = new config.MapPrefabObstacle[obstacleCount];
            for (int i = 0; i < obstacleCount; ++i)
            {
                config.MapPrefabObstacle obstacle = new config.MapPrefabObstacle();
                localObstacleManager.obstacles[i] = obstacle;

                obstacle.id = reader.ReadInt32();
                int vertexCount = reader.ReadInt32();
                obstacle.vertices = new Vector3[vertexCount];
                for (int k = 0; k < vertexCount; ++k)
                {
                    obstacle.vertices[k] = Utils.ReadVector3(reader);
                }
                int indexCount = reader.ReadInt32();
                obstacle.triangleIndices = new int[indexCount];
                for (int k = 0; k < indexCount; ++k)
                {
                    obstacle.triangleIndices[k] = reader.ReadInt32();
                }
            }

            localObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
        }

        void LoadGlobalObstacles(BinaryReader reader)
        {
            var globalObstacleManager = new config.MapGlobalObstacleManager();
            mEditorData.globalObstacleManager = globalObstacleManager;
            globalObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
            globalObstacleManager.obstacleVertices = Utils.ReadVector3Array(reader);
            globalObstacleManager.obstacleIndices = Utils.ReadIntArray(reader);
            if (mMinorVersion >= 2)
            {
                int gridObstacleCount = reader.ReadInt32();
                if (gridObstacleCount > 0)
                {
                    globalObstacleManager.gridObstacles = reader.ReadBytes(gridObstacleCount);
                }
                globalObstacleManager.gridSize = reader.ReadSingle();
            }
        }

        void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mPathMapper.pathToGuid[path] = guid;
            }
        }

        config.EditorMapData mEditorData;
        int mMinorVersion;
        PathMapper mPathMapper = new PathMapper();
    }
}
#endif
