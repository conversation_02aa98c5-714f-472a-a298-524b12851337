﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class DetailSpritesSetting
    {
        public float alpha0Height = 18;
        public float alpha1Height = 17;
        public bool crossfading = true;
        public bool updateScale = true;
        public int spriteCountPerGrid = 5;
        public float gridSize = 10.0f;
        public int horizontalGridCount;
        public int verticalGridCount;
        public List<DetailSpriteGroup> spriteGroups = new List<DetailSpriteGroup>();

        public DetailSpritesSetting(float mapWidth, float mapHeight)
        {
            horizontalGridCount = Mathf.FloorToInt(mapWidth / gridSize);
            verticalGridCount = Mathf.FloorToInt(mapHeight / gridSize);
        }
    }

    public class DetailSpriteGroup
    {
        public DetailSpriteGroup(string name, Color32 color, string[] detailSpriteIDs)
        {
            this.name = name;
            this.color = color;
            this.detailSpritesGUIDs = detailSpriteIDs;
        }

        public string name;
        public Color32 color;
        public string[] detailSpritesGUIDs;
    }
}
