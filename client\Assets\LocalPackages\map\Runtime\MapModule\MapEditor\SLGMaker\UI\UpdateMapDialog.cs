﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class UpdateMapDialog : EditorWindow
    {
        public void Show(System.Action OnClickOK, UpdateMapConfig config)
        {
            mOnClickOK = OnClickOK;
            mExportConfig = config;
            mCollisionDataChanged = false;
        }

        void OnGUI()
        {
            EditorGUILayout.TextArea("如果是大地图,在清楚修改了哪些地图数据后可以选择Update Map时只生成对应数据,减少生成时间\n如果是副本地图,则直接勾上所有选项生成所有数据即可");

            bool newState = EditorGUILayout.ToggleLeft(new GUIContent("地图碰撞是否被修改", "如果地图碰撞数据被修改,则几乎需要重新生成所有数据.\n如果地图碰撞数据没有改变,则可以选择是否导出时生成以下几种数据.\n地图碰撞数据包括\n1.LayerCollision层的数据\n2.LayerFront层GameObject挂的PrefabOutline\n3.LayerDecoration层中GameObject挂的PrefabOutline\n4.也可能有一些特殊layer的GameObject上挂了PrefabOutline脚本,例如K2的LayerVillage"), mCollisionDataChanged);
            if (newState != mCollisionDataChanged)
            {
                mCollisionDataChanged = newState;
                if (mCollisionDataChanged)
                {
                    mExportConfig.generateNPCSpawnPoints = true;
                    mExportConfig.generateCollisions = true;
                    mExportConfig.generateDecorationLayerObjects = true;
                    mExportConfig.generateNPCRegionLayerData = true;
                }
            }

            EditorGUILayout.BeginVertical("GroupBox");
            mExportConfig.generateCollisions = EditorGUILayout.ToggleLeft(new GUIContent("生成障碍物和导航网格", "地图障碍物数据修改后必须重新生成,参考<地图碰撞是否修改>的说明.生成的数据包含在\n1.global_obstacles.json\n2.nav_mesh.json\n3.navmesh_obstacle.json\n4.tile_mesh.json\n5.mapData1.bytes"), mExportConfig.generateCollisions);
            mExportConfig.generateNPCSpawnPoints = EditorGUILayout.ToggleLeft(new GUIContent("生成NPC刷新点", "是否生成Entity_Spawn_Layer的npc刷新点数据,如果碰撞数据很多,例如大地图中,这一步耗时较长.在地图碰撞被修改后必须重新生成.生成的数据包含在\n1.map_npc_moving_point.tsv\n2.map_npc_refresh_point.json\n3.mapData1.bytes"), mExportConfig.generateNPCSpawnPoints);
            mExportConfig.generateNPCRegionLayerData = EditorGUILayout.ToggleLeft(new GUIContent("生成NPC区域数据", "是否生成NPC_Region_Layer的数据,如果碰撞数据很多,例如大地图中,这一步耗时较长.在地图碰撞被修改后必须重新生成.生成的数据包含在\n1.map_npc_refresh_zone.json"), mExportConfig.generateNPCRegionLayerData);

            EditorGUILayout.BeginVertical("GroupBox");
            mExportConfig.generateDecorationLayerObjects = EditorGUILayout.ToggleLeft(new GUIContent("生成LayerDecoration数据", "是否生成LayerDecoration的数据,修改过LayerDecoration后需要勾上.生成的文件包含在\n1.BigTiles文件夹"), mExportConfig.generateDecorationLayerObjects);
            if (mExportConfig.generateDecorationLayerObjects)
            {
                var decorationLayer = Map.currentMap.GetMapLayer<EditorComplexGridModelLayer>();
                if (decorationLayer != null)
                {
                    var layerData = decorationLayer.layerData;
                    ++EditorGUI.indentLevel;
                    layerData.enableCullIntersectedObjects = EditorGUILayout.ToggleLeft(new GUIContent("是否剔除LayerDecoration里相互穿插的装饰物", "注意,这一步会耗时较长,推荐只在最终确定装饰物摆放位置后再剔除一次"), layerData.enableCullIntersectedObjects);
                    --EditorGUI.indentLevel;
                }
            }
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("OK"))
            {
                if (mOnClickOK != null)
                {
                    mOnClickOK();
                    Close();
                }
                else
                {
                    Close();
                }
            }
        }

        System.Action mOnClickOK;
        UpdateMapConfig mExportConfig;
        bool mCollisionDataChanged = false;
    }
}

#endif