﻿ 



 
 


using System.IO;
using TFW.Map.config;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadNewCityTerritoryLayer(BinaryReader reader)
        {
            var pos = GetSectionDataStartPosition(MapDataSectionType.NewCityTerritoryLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            List<config.CityTerritorySubLayerData> subLayers = new List<CityTerritorySubLayerData>();

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int subLayerCount = reader.ReadInt32();
            for (int s = 0; s < subLayerCount; ++s)
            {
                var rows = reader.ReadInt32();
                var cols = reader.ReadInt32();
                var tileWidth = reader.ReadSingle();
                var tileHeight = reader.ReadSingle();

                var nTerritories = reader.ReadInt32();

                List<CityTerritoryDataBlock> blocks = new List<CityTerritoryDataBlock>();
                bool hasBlock = reader.ReadBoolean();
                int maskTextureWidth = 0;
                int maskTextureHeight = 0;
                Color32[] maskTextureData = null;
                if (hasBlock)
                {
                    maskTextureWidth = reader.ReadInt32();
                    maskTextureHeight = reader.ReadInt32();
                    maskTextureData = Utils.ReadColor32Array(reader);

                    int nBlocks = reader.ReadInt32();
                    for (int i = 0; i < nBlocks; ++i)
                    {
                        string blockPrefabPath = Utils.ReadString(reader);
                        Rect bounds = Utils.ReadRect(reader);
                        
                        var block = new CityTerritoryDataBlock(blockPrefabPath, bounds, maskTextureWidth, maskTextureHeight, maskTextureData);
                        blocks.Add(block);
                    }
                }

                var territories = new config.CityTerritoryData[nTerritories];
                for (int i = 0; i < nTerritories; ++i)
                {
                    int territoryID = reader.ReadInt32();

                    int nBuildings = reader.ReadInt32();
                    Vector3[] buildingPositions = new Vector3[nBuildings];
                    for (int b = 0; b < nBuildings; ++b)
                    {
                        buildingPositions[b] = Utils.ReadVector3(reader);
                    }

                    string assetPath = Utils.ReadString(reader);

                    Vector3 meshPosition = Utils.ReadVector3(reader);
                    Rect worldBounds = Utils.ReadRect(reader);
                    Color color = Utils.ReadColor(reader);

                    string mtlPath = Utils.ReadString(reader);

                    int blockIndex = reader.ReadInt32();

                    territories[i] = new config.CityTerritoryData(territoryID, assetPath, nBuildings > 0 ? buildingPositions[0] : Vector3.zero, meshPosition, worldBounds, color, mtlPath, blockIndex);
                }

                //edge info
                int edgeCount = reader.ReadInt32();
                var edgesInfo = new config.CityTerritorySubLayerEdgeInfo[edgeCount];
                for (int e = 0; e < edgeCount; ++e)
                {
                    var info = new config.CityTerritorySubLayerEdgeInfo();
                    info.position = Utils.ReadVector3(reader);
                    info.worldBounds = Utils.ReadRect(reader);
                    info.leftRegionID = reader.ReadInt32();
                    info.rightRegionID = reader.ReadInt32();
                    info.prefabPath = Utils.ReadString(reader);
                    info.materialPath = Utils.ReadString(reader);
                    info.blockIndex = reader.ReadInt32();
                    edgesInfo[e] = info;
                }

                var grids = new short[rows, cols];
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        grids[i, j] = reader.ReadInt16();
                    }
                }

                var subLayer = new config.CityTerritorySubLayerData(rows, cols, tileWidth, tileHeight, grids, territories, edgesInfo, blocks, maskTextureWidth, maskTextureHeight, maskTextureData);
                subLayers.Add(subLayer);
            }
            
            var config = LoadNewCityTerritoryLayerLODConfig(reader);
            
            var layer = new config.CityTerritoryLayerData(layerID, name, offset, config, subLayers);
            return layer;
        }

        config.MapLayerLODConfig LoadNewCityTerritoryLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, 100, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }
    }
}
