﻿ 



 
 

//是否使用多线程加载

#define USE_MULITHREAD_LOADING
#define DONT_CLEANUP

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace TFW.Map
{
    //管理烘培的动画数据
    public static class BakedAnimationDataManager
    {
        class DataEntry
        {
            public DataEntry(Texture2D texture, int rigidAnimStartOffset)
            {
                this.texture = texture;
                this.rigidAnimStartOffset = rigidAnimStartOffset;
            }

            public Texture2D texture { get; private set; }
            public int rigidAnimStartOffset { get; private set; }
        }

        class TextureDataInfo
        {
            public TextureDataInfo(Color[] data, int width, int height)
            {
                textureData = data;
                textureWidth = width;
                textureHeight = height;
            }

            public Color[] textureData;
            public int textureWidth;
            public int textureHeight;
        }

#if USE_MULITHREAD_LOADING
        public static async void Init(string configPath)
        {
            if (mInited || string.IsNullOrEmpty(configPath))
            {
                return;
            }

            using var stream = await MapModuleResourceMgr.LoadTextStreamAsync(configPath);
            {
                mInited = true;
                if (stream != null)
                {
                    using BinaryReader reader = new BinaryReader(stream);

                    int majorVersion = reader.ReadInt32();
                    int minorVersion = reader.ReadInt32();

                    bool useBakedAnimation = reader.ReadBoolean();
                    if (useBakedAnimation)
                    {
                        var animDataPaths = Utils.ReadStringArray(reader);
                        string outputFolder = Utils.ReadString(reader);
                        string[] bakedPrefabNames = Utils.ReadStringArray(reader);

                        bool useRGBA32Texture = false;
                        if (minorVersion >= 2)
                        {
                            useRGBA32Texture = reader.ReadBoolean();
                        }

                        BakedAnimationDataManager.DoInit(outputFolder, bakedPrefabNames);
                    }
                    reader.Close();
                }
                else
                {
 
                }
            }  
        }
#else
        public static void Init()
        {
            if (mInited)
            {
                return;
            }
            //temp code
            //StopWatchWrapper w = new StopWatchWrapper();
            //w.Start();
            MapStats.BeginLoading();
            mInited = true;
            //load baked animation data
            var stream = MapModuleResourceMgr.LoadTextStream(MapCoreDef.MAP_BAKED_ANIM_PATH1, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                bool useBakedAnimation = reader.ReadBoolean();
                if (useBakedAnimation)
                {
                    var animDataPaths = Utils.ReadStringArray(reader);
                    string outputFolder = Utils.ReadString(reader);
                    string[] bakedPrefabNames = Utils.ReadStringArray(reader);
                    BakedAnimationDataManager.DoInit(outputFolder, bakedPrefabNames);
                    for (int i = 0; i < animDataPaths.Length; ++i)
                    {
                        var animData = MapModuleResourceMgr.LoadResource<BakedAnimationData>(animDataPaths[i]);
                        if (animData != null)
                        {
                            BakedAnimationDataManager.CreateTexture(animDataPaths[i], animData.boneTransformData, animData.customBoneTransformData);
                        }
                    }
                }
                reader.Close();
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError("baked data list not found!");
#endif
            }
            MapStats.EndLoading();
            //var time0 = w.Stop();
            //Debug.LogError("single thread loading " + time0.ToString("n8"));
        }
#endif

        public static void Uninit()
        {
#if !UNITY_EDITOR || DONT_CLEANUP
            //退出到主界面也不清理贴图
            mInited = false;
            foreach (var p in mBakedSkinAnimationTextures)
            {
                Object.Destroy(p.Value.texture);
            }

            mBakedSkinAnimationTextures.Clear();
#endif
        }

        static void DoInit(string bakedPrefabDirectory, string[] bakedPrefabNames)
        {
            mBakedPrefabDirectory = bakedPrefabDirectory;
            for (int i = 0; i < bakedPrefabNames.Length; ++i)
            {
                if (!string.IsNullOrEmpty(bakedPrefabNames[i]))
                {
                    mBakedPrefabNames.Add(bakedPrefabNames[i]);
                }
            }
        }

        public static string GetBakedPrefabPath(string prefabName)
        {
            if (string.IsNullOrEmpty(mBakedPrefabDirectory))
            {
                return null;
            }

            if (mBakedPrefabNames.Contains(prefabName))
            {
                return $"{mBakedPrefabDirectory}/{prefabName}_baked/{prefabName}.prefab";
            }

            return null;
        }

        public static Texture2D GetTexture(string path, out int rigidAnimStartOffset)
        {
            rigidAnimStartOffset = 0;
            DataEntry entry;
            mBakedSkinAnimationTextures.TryGetValue(path, out entry);
            if (entry != null)
            {
                rigidAnimStartOffset = entry.rigidAnimStartOffset;
            }
            return entry?.texture;
        }

        //把骨骼动画和刚体动画都烘培到一张贴图上
        public static void CreateTexture(string path, Color[] skinAnimData, Color[] rigidAnimData)
        {
            Debug.Assert(SystemInfo.SupportsTextureFormat(TextureFormat.RGBAHalf), "RGBAHalf is not " +
                "supported!");

            int totalLength = skinAnimData.Length + rigidAnimData.Length;

            DataEntry entry = null;
#if UNITY_EDITOR
            //only check in editor
            mBakedSkinAnimationTextures.TryGetValue(path, out entry);
            if (entry == null)
            {
#endif
                int textureSize = Mathf.NextPowerOfTwo(Mathf.CeilToInt(Mathf.Sqrt(totalLength)));
                var texture = new Texture2D(textureSize, textureSize, TextureFormat.RGBAHalf, false);

                Color[] textureData = new Color[textureSize * textureSize];
                //use array copy is much faster!
                System.Array.Copy(skinAnimData, textureData, skinAnimData.Length);
                System.Array.Copy(rigidAnimData, 0, textureData, skinAnimData.Length, rigidAnimData.Length);
                entry = new DataEntry(texture, skinAnimData.Length);
                texture.SetPixels(textureData);
                texture.Apply(false, true);
                texture.filterMode = FilterMode.Point;
                mBakedSkinAnimationTextures.Add(path, entry);
#if UNITY_EDITOR
            }
            else
            {
                Debug.Assert(false, "Can't be here");
            }
#endif
        }

        static void CreateTexture(string path, TextureDataInfo textureDataInfo, int rigidDataOffset)
        {
            Debug.Assert(SystemInfo.SupportsTextureFormat(TextureFormat.RGBAHalf), "RGBAHalf is not " +
                "supported!");

            DataEntry entry = null;
#if UNITY_EDITOR
            //only check in editor
            mBakedSkinAnimationTextures.TryGetValue(path, out entry);
            if (entry == null)
            {
#endif
                var texture = new Texture2D(textureDataInfo.textureWidth, textureDataInfo.textureHeight, TextureFormat.RGBAHalf, false);
                entry = new DataEntry(texture, rigidDataOffset);
                texture.SetPixels(textureDataInfo.textureData);
                texture.Apply(false, true);
                texture.filterMode = FilterMode.Point;
                mBakedSkinAnimationTextures.Add(path, entry);
#if UNITY_EDITOR
            }
            else
            {
                Debug.Assert(false, "Can't be here");
            }
#endif
        }

        //static List<TextureDataInfo> GenerateAllTextureData(BakedAnimationData[] animationDatas)
        //{
        //    List<Task<TextureDataInfo>> tasks = new List<Task<TextureDataInfo>>();
        //    for (int t = 0; t < animationDatas.Length; ++t)
        //    {
        //        var block = animationDatas[t];
        //        var task = Task<Color[]>.Run(() =>
        //        {
        //            int totalLength = block.boneTransformData.Length + block.customBoneTransformData.Length;
        //            Vector2Int textureSize = Utils.CalculateSmallestPOTTextureSize(totalLength);

        //            Color[] textureData = new Color[textureSize.x * textureSize.y];
        //            TextureDataInfo texInfo = new TextureDataInfo(textureData, textureSize.x, textureSize.y);

        //            //use array copy is much faster!
        //            System.Array.Copy(block.boneTransformData, textureData, block.boneTransformData.Length);
        //            System.Array.Copy(block.customBoneTransformData, 0, textureData, block.boneTransformData.Length, block.customBoneTransformData.Length);

        //            return texInfo;
        //        });

        //        tasks.Add(task);
        //    }

        //    List<TextureDataInfo> generatedTextureDatas = new List<TextureDataInfo>();
        //    for (int i = 0; i < tasks.Count; ++i)
        //    {
        //        generatedTextureDatas.Add(tasks[i].Result);
        //    }

        //    return generatedTextureDatas;
        //}

        static Dictionary<string, DataEntry> mBakedSkinAnimationTextures = new Dictionary<string, DataEntry>(100);
        //烘培的资源的目录,要求所有烘培的资源都在同一目录下
        static string mBakedPrefabDirectory;
        //烘焙的prefab的名称
        static HashSet<string> mBakedPrefabNames = new HashSet<string>();
        static bool mInited = false;
    }
}
