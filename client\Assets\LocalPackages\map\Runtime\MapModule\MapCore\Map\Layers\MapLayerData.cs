﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    //地图数据层的header
    public class MapLayerDataHeader
    {
        public MapLayerDataHeader(int id, string name, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, Vector3 origin)
        {
            this.layerID = id;
            this.name = name;
            this.rows = rows;
            this.cols = cols;
            this.tileWidth = tileWidth;
            this.tileHeight = tileHeight;
            this.gridType = gridType;
            this.origin = origin;
        }

        public int layerID;
        public string name;
        public int rows;
        public int cols;
        public float tileWidth;
        public float tileHeight;
        public Vector3 origin;
        public GridType gridType = GridType.Rectangle;
    }

    /*地图层的数据,目前一个地图层包括了数据和视图,分别是继承自MapLayerData和MapLayerView,data中管理游戏对象的数据和逻辑,
    view中管理游戏对象的显示
    将地图层用数据层和视图层来分开管理主要有2个考虑:
    1.一个地图层可以有多个视图,主要是在编辑器中使用多个视图
    2.数据和视图不用同时存在,可以将地图对象的视图延迟显示出来,并且可以在视图还未加载时不影响游戏的逻辑
    */
    public abstract class MapLayerData : BaseObject
    {
        public MapLayerData(MapLayerDataHeader header, MapLayerLODConfig lodConfig, Map map) : base(header.layerID, map)
        {
            mMap = map;
            mRows = header.rows;
            mCols = header.cols;
            mName = header.name;
            mTileWidth = header.tileWidth;
            mTileHeight = header.tileHeight;
            mGridType = header.gridType;
            mRows = header.rows;
            mCols = header.cols;
            mTileWidth = header.tileWidth;
            mTileHeight = header.tileHeight;
            mLayerOrigin = header.origin;
            mLODConfig = lodConfig;
            mConverter = Utils.GetMapLayerCoordinateConverter(gridType);
            mConverter.SetLayerData(this);
        }

        public abstract void RefreshObjectsInViewport();
        public abstract bool UpdateViewport(Rect newViewport, float newZoom);
        public abstract bool Contains(int objectID);

        public virtual void SetActive(bool active)
        {
            if (mActive != active)
            {
                mActive = active;
            }
        }

        public virtual bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (mLODConfig != null)
            {
                var newLOD = mLODConfig.GetLODLevel(mCurrentLOD, zoom);
                if (newLOD != mCurrentLOD)
                {
                    //改变lod
                    mLastLOD = mCurrentLOD;
                    mCurrentLOD = newLOD;
                    lodChanged = true;

                    map.OnMapLayerLODChanged(name, mCurrentLOD);
                }
                mCurrentZoom = zoom;
            }

            return lodChanged;
        }

        public int GetLODFromCameraHeight(float cameraHeight)
        {
            float zoom = map.CalculateCameraZoom(cameraHeight);
            return mLODConfig.GetLODFromZoom(zoom);
        }

        public float GetLODHeight(int lod)
        {
            var lodConfig = mLODConfig.GetLOD(lod);
            if (lodConfig != null)
            {
                return map.data.lodManager.GetCameraHeight(lodConfig.changeZoom);
            }
            return 0;
        }

        public virtual Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            return mConverter.FromWorldPositionToCoordinate(position);
        }
        public Vector2Int FromWorldPositionToCoordinateUpperBounds(Vector3 position)
        {
            return mConverter.FromWorldPositionToCoordinateUpperBounds(position);
        }
        public Vector2Int FromScreenToCoordinate(Vector3 screenPos, Camera camera)
        {
            return mConverter.FromScreenToCoordinate(screenPos, camera);
        }
        public virtual Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mConverter.FromCoordinateToWorldPosition(x, y);
        }

        public virtual Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return mConverter.FromCoordinateToWorldPositionCenter(x, y);
        }
        public virtual Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height)
        {
            return mConverter.FromCoordinateToWorldPositionCenter(x, y, width, height);
        }

        public float GetLayerWidthInMeter(int width = 0)
        {
            return mConverter.GetLayerWidthInMeter(width);
        }
        public float GetLayerHeightInMeter(int height = 0)
        {
            return mConverter.GetLayerHeightInMeter(height);
        }

        public abstract bool isGameLayer { get; }

        public int verticalTileCount { get { return mRows; } }
        public int horizontalTileCount { get { return mCols; } }
        public float tileWidth { get { return mTileWidth; } }
        public float tileHeight { get { return mTileHeight; } }
        public GridType gridType { get { return mGridType; } }
        public string name { get { return mName; } set { mName = value; } }
        public Vector3 layerOffset { get { return mLayerOrigin; } set { mLayerOrigin = value; } }
        public int currentLOD { get { return mCurrentLOD; } set { mCurrentLOD = value; } }
        public int lastLOD { get { return mLastLOD; } }
        public MapLayerLODConfig lodConfig { get { return mLODConfig; } set { mLODConfig = value; } }
        public int lodCount { get { return mLODConfig.lodConfigs.Length; } }

        //地图层的名字
        protected string mName;
        //地图层是否显示
        protected bool mActive = true;
        //地图层上z轴的格子数,只对使用格子的地图层有效,使用四叉树的地图层mRows = 1
        protected int mRows;
        //地图层上x轴的格子数,只对使用格子的地图层有效,使用四叉树的地图层mCols = 1
        protected int mCols;
        //地图上一个格子的tile的宽,对于非格子的地图层来说,代表地图层的总体宽度
        protected float mTileWidth;
        //地图上一个格子的tile的高,对于非格子的地图层来说,代表地图层的总体高度
        protected float mTileHeight;
        //该层使用的格子类型,可以为矩形或六边形,只对使用格子的地图层有效
        protected GridType mGridType;
        //坐标转换器,可以为六边形或矩形格子的地图层转换坐标
        protected MapLayerCoordinateConverter mConverter;
        //地图层的偏移值
        protected Vector3 mLayerOrigin = Vector3.zero;
        //地图层的lod控制
        MapLayerLODConfig mLODConfig;
        //地图层当前在哪一个LOD等级上
        protected int mCurrentLOD = 0;
        protected int mLastLOD = 0;
        //地图层当前的缩放值
        protected float mCurrentZoom = 0;
    };
}
