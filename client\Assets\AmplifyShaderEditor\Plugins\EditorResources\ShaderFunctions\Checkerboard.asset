%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Checkerboard
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17500\n2092;369;1066;673;519.6959;768.1077;1.3;True;False\nNode;AmplifyShaderEditor.CommentaryNode;18;-1101.678,-1164.645;Inherit;False;1511.133;506.8259;;12;6;1;10;11;12;4;9;13;14;15;16;17;UV
    and Derivatives;1,1,1,1;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;39;-1068.804,243.1458;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;40;-1228.804,307.1458;Inherit;False;Constant;_Float4;Float
    4;0;0;Create;True;0;0;False;0;4;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.AbsOpNode;32;-1208.804,195.1458;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;42;-1068.804,355.1458;Inherit;False;Constant;_Float5;Float
    5;0;0;Create;True;0;0;False;0;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;95.94269,118.7242;Inherit;False;Color
    B;5;2;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;37;-1340.804,195.1458;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;38;-1532.805,275.1458;Inherit;False;Constant;_Float3;Float
    3;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;66;-60.19727,349.9423;Inherit;False;Constant;_Float9;Float
    9;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;8;-112.0574,118.7242;Inherit;False;Constant;_Color1;Color
    1;0;0;Create;True;0;0;False;0;0.6980392,0.6980392,0.6980392,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;50;-829.9077,762.2733;Inherit;False;Constant;_Float6;Float
    6;0;0;Create;True;0;0;False;0;1.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;68;-56.59735,495.6422;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;67;95.94269,422.7241;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;7;-112.0574,-73.27585;Inherit;False;Constant;_Color0;Color
    0;0;0;Create;True;0;0;False;0;0.2,0.2,0.2,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.BreakToComponentsNode;71;-302.0974,513.8422;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FractNode;33;-1484.805,163.1458;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SqrtOpNode;48;-366.826,795.2014;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;49;-517.6261,801.4014;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;53;-1232.34,860.269;Inherit;False;29;derivativesLength;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;55;-1036.94,854.6689;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;58;-637.4819,548.7932;Inherit;False;Constant;_Float7;Float
    7;0;0;Create;True;0;0;False;0;-1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;35;-1596.804,163.1458;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;51;-662.9076,794.273;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ClampOpNode;57;-477.4815,516.7932;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;1,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;63;-637.4819,452.7921;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;41;-924.8043,291.1458;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;45;-987.7781,549.8922;Inherit;False;29;derivativesLength;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;59;-637.4819,628.7933;Inherit;False;Constant;_Float8;Float
    8;0;0;Create;True;0;0;False;0;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;43;-956.7781,452.0911;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;False;0;0.35;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;44;-774.1779,498.6923;Inherit;False;2;0;FLOAT;0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;73;383.9426,22.72415;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SqrtOpNode;26;-160,-448;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;19;-593.6686,-565.8171;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;21;-592,-368;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;25;-288,-448;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;28;-966.1454,-418.2566;Inherit;False;FLOAT4;1;0;FLOAT4;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.DotProductOpNode;23;-448,-336;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;95.94269,-73.27585;Inherit;False;Color
    A;5;1;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;10;-678.6784,-881.8192;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;22;-592,-272;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;20;-592,-464;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;12;-549.6785,-980.8192;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;6;-1051.678,-816.8192;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DdyOpNode;15;-140.6784,-877.8192;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;27;-1139.145,-405.2566;Inherit;False;17;UVDerivatives;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;11;-833.6784,-945.8192;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;69;-223.9972,434.8422;Inherit;False;Constant;_Float10;Float
    10;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;36;-1740.804,211.1458;Inherit;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;False;0;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;34;-1755.153,114.0224;Inherit;False;13;FinalUV;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SaturateNode;65;253.0029,341.8422;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;52;-810.9399,854.6689;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;29;-48,-448;Inherit;False;derivativesLength;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DotProductOpNode;24;-448,-544;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector2Node;9;-994.1925,-1114.645;Inherit;False;Constant;_Vector0;Vector
    0;0;0;Create;True;0;0;False;0;1,1;0,0;0;3;FLOAT2;0;FLOAT;1;FLOAT;2\nNode;AmplifyShaderEditor.FunctionInput;1;-855.6784,-822.8192;Inherit;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;4;-828.1924,-1094.645;Inherit;False;Frequency;2;3;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;13;-395.1576,-957.8666;Inherit;False;FinalUV;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;16;17.45426,-967.288;Inherit;False;FLOAT4;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT2;0,0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;17;185.4542,-971.288;Inherit;False;UVDerivatives;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DdxOpNode;14;-140.6784,-1005.819;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;0;612.215,21.62323;Inherit;False;True;-1;Out;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nWireConnection;39;0;32;0\nWireConnection;39;1;40;0\nWireConnection;32;0;37;0\nWireConnection;3;0;8;0\nWireConnection;37;0;33;0\nWireConnection;37;1;38;0\nWireConnection;68;0;69;0\nWireConnection;68;1;71;0\nWireConnection;68;2;71;1\nWireConnection;68;3;48;0\nWireConnection;67;0;66;0\nWireConnection;67;1;68;0\nWireConnection;71;0;57;0\nWireConnection;33;0;35;0\nWireConnection;48;0;49;0\nWireConnection;49;0;51;0\nWireConnection;55;0;53;0\nWireConnection;35;0;34;0\nWireConnection;35;1;36;0\nWireConnection;51;0;50;0\nWireConnection;51;1;52;0\nWireConnection;57;0;63;0\nWireConnection;57;1;58;0\nWireConnection;57;2;59;0\nWireConnection;63;0;41;0\nWireConnection;63;1;44;0\nWireConnection;41;0;39;0\nWireConnection;41;1;42;0\nWireConnection;44;0;43;0\nWireConnection;44;1;45;0\nWireConnection;73;0;2;0\nWireConnection;73;1;3;0\nWireConnection;73;2;65;0\nWireConnection;26;0;25;0\nWireConnection;19;0;28;0\nWireConnection;19;1;28;2\nWireConnection;21;0;28;1\nWireConnection;21;1;28;3\nWireConnection;25;0;24;0\nWireConnection;25;1;23;0\nWireConnection;28;0;27;0\nWireConnection;23;0;21;0\nWireConnection;23;1;22;0\nWireConnection;2;0;7;0\nWireConnection;10;0;11;0\nWireConnection;10;1;1;0\nWireConnection;22;0;28;1\nWireConnection;22;1;28;3\nWireConnection;20;0;28;0\nWireConnection;20;1;28;2\nWireConnection;12;0;4;0\nWireConnection;12;1;10;0\nWireConnection;15;0;13;0\nWireConnection;65;0;67;0\nWireConnection;52;0;55;0\nWireConnection;52;1;55;1\nWireConnection;29;0;26;0\nWireConnection;24;0;19;0\nWireConnection;24;1;20;0\nWireConnection;1;0;6;0\nWireConnection;4;0;9;0\nWireConnection;13;0;12;0\nWireConnection;16;0;14;0\nWireConnection;16;2;15;0\nWireConnection;17;0;16;0\nWireConnection;14;0;13;0\nWireConnection;0;0;73;0\nASEEND*/\n//CHKSM=2A92AA4C137FA7B827E342AC45B5BF06C4CEEF0A"
  m_functionName: 
  m_description: Created a checkerboard pattern with given colors where Frequency
    controls its tiling
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
