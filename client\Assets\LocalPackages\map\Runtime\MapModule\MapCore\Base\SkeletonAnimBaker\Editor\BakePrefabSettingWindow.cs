﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class BakePrefabSettingWindow : EditorWindow
    {
        GUIStyle mMeshRendererStyle;
        GUIStyle mSkinMeshRendererStyle;
        string[] mBlendTypeNames = new string[] {
            "None",
            "Fast",
            "Slerp",
        };

        void OnEnable()
        {
            mMeshRendererStyle = new GUIStyle();
            mMeshRendererStyle.normal.textColor = Color.blue;

            mSkinMeshRendererStyle = new GUIStyle();
            mSkinMeshRendererStyle.normal.textColor = Color.red;
        }

        public void Draw(GameObject prefab, AnimationBaker baker, BakeSkeletonAnimationWindow.Entry entry)
        {
            int index = CalculateIndex(entry);
            int newIndex = EditorGUILayout.Popup("Animation Blend Type", index, mBlendTypeNames);
            if (newIndex > 0)
            {
                entry.blendType = (AnimationBlendType)(newIndex - 1);
                entry.useAnimationBlending = true;
            }
            else
            {
                entry.blendType = AnimationBlendType.Fast;
                entry.useAnimationBlending = false;
            }

            EditorGUILayout.BeginHorizontal();
            entry.useVertexAnimation = EditorGUILayout.Toggle("Bake Vertex Animation", entry.useVertexAnimation);
            if (entry.useVertexAnimation)
            {
                entry.sampleFrameInterval = Mathf.Max(1, EditorGUILayout.IntField("Sample Frame Interval", entry.sampleFrameInterval));
            }
            EditorGUILayout.EndHorizontal();

            var renderers = baker.renderers;
            for (int i = 0; i < renderers.Count; ++i)
            {
                DrawRendererSetting(i, renderers[i], baker);
            }
        }

        //renderer使用相同的material只需要设置相同的shader
        void DrawRendererSetting(int idx, Renderer r, AnimationBaker baker)
        {
            var mtls = r.sharedMaterials;
            if (mtls != null)
            {
                for (int i = 0; i < mtls.Length; ++i)
                {
                    var originalShader = mtls[i].shader;
                    var bakeShader = baker.materialManager.GetTargetShader(mtls[i]);
                    EditorGUILayout.BeginHorizontal();
                    if (r is MeshRenderer)
                    {
                        EditorGUILayout.LabelField(r.name, mMeshRendererStyle, GUILayout.MaxWidth(160));
                    }
                    else
                    {
                        EditorGUILayout.LabelField(r.name, mSkinMeshRendererStyle, GUILayout.MaxWidth(160));
                    }

                    EditorGUIUtility.labelWidth = 100;
                    EditorGUILayout.ObjectField("Original Shader", originalShader, typeof(Shader), false, null);
                    var newBakeShader = EditorGUILayout.ObjectField("Bake Shader", bakeShader, typeof(Shader), false, null) as Shader;
                    if (bakeShader != newBakeShader)
                    {
                        baker.materialManager.SetMaterialShader(mtls[i], newBakeShader);
                    }
                    EditorGUIUtility.labelWidth = 0;
                    EditorGUILayout.EndHorizontal();
                }
            }
        }

        int CalculateIndex(BakeSkeletonAnimationWindow.Entry entry)
        {
            if (entry.useAnimationBlending == false)
            {
                return 0;
            }
            else
            {
                return (int)entry.blendType + 1;
            }
        }
    }
}
#endif