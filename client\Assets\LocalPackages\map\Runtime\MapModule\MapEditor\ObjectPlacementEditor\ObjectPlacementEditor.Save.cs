﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void Save(string folderPath)
        {
            string filePath = $"{folderPath}/{MapCoreDef.OBJECT_PLACEMENT_EDITOR_FILE_NAME}";
            mProjectFolder = folderPath; 

            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.ObjectPlacementEditorVersion);

            //save setting
            writer.Write(mShowRotationSetting);
            writer.Write(mShowGridSetting);
            writer.Write(mShowPrefabSetting);
            writer.Write(mSnapToGrid);
            writer.Write(mGridSize);
            Utils.WriteQuaternion(writer, mPrefabRotationSetting.rotation);
            writer.Write(mPrefabRotationSetting.rotationStep);
            writer.Write(mPrefabRotationSetting.randomYRotation);
            writer.Write(mPlaceRandomObject);
            writer.Write(mMinDistance);
            writer.Write(mFillCount);
            writer.Write((int)mMode);
            writer.Write(mFillEdge);
            writer.Write(mEdgeSize);
            writer.Write(mCircleRadius);
            writer.Write(mUseSameDeltaDistance);
            writer.Write(mRemoveObjectOfSeletedPrefab);
            writer.Write(mWorldSize);
            writer.Write(mDisplayGridSize);
            string backgroundPrefabGuid = Utils.GetAssetGuid(mWorldIndicator.prefab);
            Utils.WriteString(writer, backgroundPrefabGuid);

            //save prefab manager
            SavePrefabManager(writer, mPrefabManager);

            //save brushies
            SaveBrush(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(filePath, data);
            writer.Close();
        }

        void SaveBrush(BinaryWriter writer)
        {
            int nBrushies = mBrushies.Count;
            writer.Write(nBrushies);

            for (int i = 0; i < nBrushies; ++i)
            {
                Utils.WriteString(writer, mBrushies[i].name);
                int nObjects = mBrushies[i].objects.Count;
                writer.Write(nObjects);
                for (int k = 0; k < nObjects; ++k)
                {
                    var obj = mBrushies[i].objects[k];
                    writer.Write(obj.layer);
                    Utils.WriteVector3(writer, obj.position);
                    Utils.WriteVector3(writer, obj.scale);
                    Utils.WriteQuaternion(writer, obj.rotation);
                    Utils.WriteString(writer, obj.tag);
                    string guid = AssetDatabase.AssetPathToGUID(obj.prefabPath);
                    Utils.WriteString(writer, guid);
                }
            }
        }

        void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            Utils.WriteString(writer, group.name);
            writer.Write(group.gridSize);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                string prefabGuid = AssetDatabase.AssetPathToGUID(group.GetPrefabPath(i));
                Utils.WriteString(writer, prefabGuid);
            }
        }
    }
}

#endif