﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //使用对象池的模型
    public class ReusableModel : ModelBase
    {
        public void Init(Map map, string prefabPath, int lod, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            mMap = map;
            mPrefabPath = prefabPath;
            //从对象池中拿出一个模型
            mGameObject = mMap.view.reusableGameObjectPool.Require(mPrefabPath, position, scale, rotation);

            Utils.HideGameObject(mGameObject);
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
            if (mGameObject != null)
            {
                mMap.view.reusableGameObjectPool.Release(mPrefabPath, mGameObject, mMap);
            }
        }

        public override void Release()
        {
            ReleaseToPool(this);
        }

        public static ReusableModel Require(Map map, int modelTemplateID, int lod, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            var modelTemplate = map.FindObject(modelTemplateID) as ModelTemplate;
            var newPrefabPath = modelTemplate.GetLODPrefabPath(lod);
            var model = mPool.Require();
            model.Init(map, newPrefabPath, lod, position, scale, rotation);
            return model;
        }

        public static ReusableModel Require(Map map, string prefabPath, int lod, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            var model = mPool.Require();
            model.Init(map, prefabPath, lod, position, scale, rotation);
            return model;
        }

        static void ReleaseToPool(ReusableModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        Map mMap;
        string mPrefabPath;
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<ReusableModel> mPool = new ObjectPool<ReusableModel>(1000, () => new ReusableModel());
    }
}
