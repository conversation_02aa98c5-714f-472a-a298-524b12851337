%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Rejection
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18104\n-1705;-353;1296;693;1041;283.5;1;True;False\nNode;AmplifyShaderEditor.FunctionInput;4;-560,96;Inherit;False;B;4;1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;3;-560,0;Inherit;False;A;4;0;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionNode;1;-368,80;Inherit;False;Projection;-1;;1;3249e2c8638c9ef4bbd1902a2d38a67c;0;2;5;FLOAT4;0,0,0,0;False;6;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;2;-176,0;Inherit;False;2;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;0;0,0;Inherit;False;True;-1;Out;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;1;5;3;0\nWireConnection;1;6;4;0\nWireConnection;2;0;3;0\nWireConnection;2;1;1;0\nWireConnection;0;0;2;0\nASEEND*/\n//CHKSM=3BD893037EAFB176DD5A4C61C6A15D4B88CB7106"
  m_functionName: 
  m_description: Returns the result of the projection of the value of input A onto
    the plane orthogonal, or perpendicular, to the value of input B. The value of
    the rejection vector is equal to the original vector, the value of input A, minus
    the value of the Projection of the same inputs
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 16
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
