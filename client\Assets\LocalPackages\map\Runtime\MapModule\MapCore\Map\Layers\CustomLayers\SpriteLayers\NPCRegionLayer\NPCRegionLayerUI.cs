﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(NPCRegionLayerLogic))]
    public class NPCRegionLayerUI : UnityEditor.Editor
    {
        NPCRegionLayerLogic mLogic;
        bool mLeftButtonDown;
        string[] mLayerNames;

        void OnEnable()
        {
            mLogic = target as NPCRegionLayerLogic;

            mLogic.showLayerGrid = false;
            mLogic.UpdateGizmoVisibilityState();

            var layer = mLogic.layer;
            var layers = layer.layerData.layers;
            if (layers.Count > 0)
            {
                SetSelectedLayer(0);
            }
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);
            var layer = mLogic.layer;

            if (layer.selectedLayerIndex >= 0 && mLeftButtonDown)
            {
                if (mLogic.operation == GridRegionOperation.Create)
                {
                    if (layer.selectedIndex >= 0)
                    {
                        int bandID = 0;
                        if (!currentEvent.control)
                        {
                            bandID = layer.layerData.GetTemplates(layer.selectedLayerIndex)[layer.selectedIndex].bandID;
                        }
                        layer.SetTileBandID(layer.selectedLayerIndex, pos, layer.brushSize, bandID);
                    }
                }
                else
                {
                    layer.SetTileBandID(layer.selectedLayerIndex, pos, layer.brushSize, 0);
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    layer.brushSize = layer.brushSize + 1;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    layer.brushSize = layer.brushSize - 1;
                    Repaint();
                }
            }

            if (layer.selectedLayerIndex >= 0)
            {
                var coord = layer.layerData.FromWorldPositionToCoordinate(layer.selectedLayerIndex, pos);
                DrawBrush(layer.selectedLayerIndex, coord);
            }

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void DrawBrush(int layerIndex, Vector2Int centerCoord)
        {
            int brushSize = mLogic.layer.brushSize;
            int startX = centerCoord.x - brushSize / 2;
            int startY = centerCoord.y - brushSize / 2;
            int endX = startX + brushSize;
            int endY = startY + brushSize;
            var startPos = mLogic.layer.layerData.FromCoordinateToWorldPosition(layerIndex, startX, startY);
            var endPos = mLogic.layer.layerData.FromCoordinateToWorldPosition(layerIndex, endX, endY);

            Handles.color = Color.white;
            Handles.DrawWireCube((startPos + endPos) * 0.5f, endPos - startPos);
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                var layer = mLogic.layer;
                var layers = mLogic.layer.layerData.layers;
                if (mLayerNames == null || mLayerNames.Length != layers.Count)
                {
                    CreateLayerNames();
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Add Layer", "添加一个子层,每一子层可以有不同的格子大小,如果子layer的width和height为0,则使用layer的格子数")))
                {
                    AddLayer();
                }
                if (layer.selectedLayerIndex >= 0)
                {
                    if (GUILayout.Button(new GUIContent("Remove Layer", "删除当前子层")))
                    {
                        RemoveLayer();
                    }
                }
                if (GUILayout.Button(new GUIContent("Change Layer Name", "修改子层名字")))
                {
                    ChangeLayerName();
                }
                EditorGUILayout.EndHorizontal();

                if (layers.Count > 0)
                {
                    int newLayer = EditorGUILayout.Popup("Layers", layer.selectedLayerIndex, mLayerNames);
                    if (newLayer != layer.selectedLayerIndex)
                    {
                        SetSelectedLayer(newLayer);
                    }
                }

                if (layer.selectedLayerIndex >= 0)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    if (layer.selectedIndex == -1 && mLogic.layer.layerData.GetTemplates(layer.selectedLayerIndex).Count > 0)
                    {
                        layer.selectedIndex = 0;
                    }

                    mLogic.operation = (GridRegionOperation)EditorGUILayout.EnumPopup("Operation", mLogic.operation);

                    layer.brushSize = EditorGUILayout.IntField("Brush Size", layer.brushSize);
                    layer.brushSize = Mathf.Clamp(layer.brushSize, 1, 200);

                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button(new GUIContent("Clear", "清空当前子层的所有格子")))
                    {
                        if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone!", "Yes", "No"))
                        {
                            mLogic.layer.Clear(layer.selectedLayerIndex);
                            layer.selectedIndex = -1;
                        }
                    }

                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button(new GUIContent("Import Color Texture", "通过导入一张tga贴图来设置格子, 格式就是下面的Export Color Texture输出的数据")))
                    {
                        layer.ImportColorMap(layer.selectedLayerIndex);
                        layer.selectedIndex = 0;
                    }
                    if (GUILayout.Button(new GUIContent("Export Color Texture", "将当前子层的格子数据导出成一张贴图和对应的颜色表,这些数据可以通过Import Color Texture再导入到编辑器中")))
                    {
                        mLogic.layer.layerView.RefreshTexture(layer.selectedLayerIndex);
                        layer.ExportColorMap(layer.selectedLayerIndex);
                    }
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.BeginHorizontal();

                    layer.exportZoneIDs = EditorGUILayout.ToggleLeft(new GUIContent("Export band id list", "导出的配置表中是否导出band id list"), layer.exportZoneIDs, GUILayout.MaxWidth(150));
                    var subLayer = layer.layerData.GetLayer(layer.selectedLayerIndex);
                    subLayer.export = EditorGUILayout.ToggleLeft(new GUIContent("Include In Config File", "是否导出该子层数据, 因为有些子层作为其他功能使用,并不需要导出,例如作为City Territory Layer中标记地形"), subLayer.export, GUILayout.MaxWidth(150));
                    if (GUILayout.Button(new GUIContent("Export Region Config", "导出该层数据的服务器配置表")))
                    {
                        var filePath = EditorUtility.SaveFilePanel("Select file", "", "map_npc_refresh_zone", "tsv");
                        if (filePath != null && filePath.Length > 0)
                        {
                            layer.ExportCSV(layer.selectedLayerIndex, filePath, layer.exportZoneIDs);
                        }
                    }
                    EditorGUILayout.EndHorizontal();

                    if (mLogic.operation == GridRegionOperation.Create)
                    {
                        DrawGridTemplates();
                    }
                    EditorGUILayout.EndVertical();
                }

                EditorGUILayout.LabelField("Horizontal Tile Count", mLogic.layer.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Vertical Tile Count", mLogic.layer.verticalTileCount.ToString());
                EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Mouse Button' to paint.\n 'Ctrl + Left Mouse Button' to erase.\n 'Up Arrow' and 'Down Arrow' to change brush size.\n 使用鼠标左键绘制格子.\n 左键+ctrl消除格子.\n 上下键可以修改brush大小.");
            }
        }

        void DrawGridTemplates()
        {
            mLogic.showBrush = EditorGUILayout.Foldout(mLogic.showBrush, new GUIContent("Region Types", "区域类型"));
            if (mLogic.showBrush)
            {
                var layer = mLogic.layer;
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Add", "添加一个区域类型")))
                {
                    AddTemplate();
                }
                if (GUILayout.Button(new GUIContent("Remove", "删除一个区域类型")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure delete this? this action can't be redone!", "Yes", "No"))
                    {
                        RemoveTemplate();
                    }
                }
                if (GUILayout.Button(new GUIContent("Change Band ID", "修改区域band id")))
                {
                    ChangeTemplateBandID();
                }
                EditorGUILayout.EndHorizontal();

                var templates = layer.layerData.GetTemplates(layer.selectedLayerIndex);
                int selectedIndex = -1;
                bool selectionChange = false;
                for (int i = 0; i < templates.Count; ++i)
                {
                    selectionChange = DrawTemplate(templates[i], i);
                    if (selectionChange)
                    {
                        selectedIndex = i;
                    }
                }
                if (selectedIndex >= 0)
                {
                    layer.selectedIndex = selectedIndex;
                }
                EditorGUILayout.EndVertical();
            }
        }

        bool DrawTemplate(NPCRegionTemplate template, int i)
        {
            var layer = mLogic.layer;
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUIUtility.labelWidth = 50;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal();
            bool nowSelected = layer.selectedIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            template.name = EditorGUILayout.TextField(template.name);
            EditorGUILayout.IntField(new GUIContent("Band ID", "区域id"), template.bandID);
            var newColor = EditorGUILayout.ColorField("", template.color);
            if (newColor != template.color)
            {
                template.color = newColor;
            }

            if (GUILayout.Button(new GUIContent("Change Color", "修改区域类型颜色")))
            {
                mLogic.layer.layerView.RefreshTexture(layer.selectedLayerIndex);
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUIUtility.labelWidth = 60;
            template.level = EditorGUILayout.IntField(new GUIContent("Level", "区域等级"), template.level);
            template.level = Mathf.Max(1, template.level);
            template.tileType = EditorGUILayout.IntField(new GUIContent("Type", "区域类型,例如海洋还是陆地等"), template.tileType);
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            EditorGUIUtility.labelWidth = 0;
            return selectionChange;
        }

        void AddTemplate()
        {
            var layer = mLogic.layer;
            var templates = mLogic.layer.layerData.GetTemplates(layer.selectedLayerIndex);
            int bandID = 1;
            if (templates.Count > 0)
            {
                bandID = templates[templates.Count - 1].bandID + 1;
            }
            var dlg = EditorUtils.CreateInputDialog("Add Region Brush");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "Region"),
                    new InputDialog.StringItem("Band ID", "", bandID.ToString()),
                    new InputDialog.StringItem("Level", "", "1"),
                };
            dlg.Show(items, OnClickAdd);
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            var layer = mLogic.layer;
            string name = (parameters[0] as InputDialog.StringItem).text;
            string bandIDStr = (parameters[1] as InputDialog.StringItem).text;
            string levelStr = (parameters[2] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(name))
            {
                return false;
            }
            int bandID;
            bool suc = Utils.ParseInt(bandIDStr, out bandID);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid bandID!", "OK");
                return false;
            }

            if (bandID <= 0)
            {
                EditorUtility.DisplayDialog("Error", "bandID must be > 0", "OK");
                return false;
            }

            int level;
            suc = Utils.ParseInt(levelStr, out level);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid level!", "OK");
                return false;
            }

            if (level <= 0)
            {
                EditorUtility.DisplayDialog("Error", "level must be > 0", "OK");
                return false;
            }

            var layerData = mLogic.layer.layerData;
            var temp = layerData.GetNPCRegionTemplateByBandID(layer.selectedLayerIndex, bandID);
            if (temp != null)
            {
                EditorUtility.DisplayDialog("Error", $"Band ID {bandID} already existed!", "OK");
                return false;
            }

            var template = layerData.AddNPCRegionTemplate(layer.selectedLayerIndex, name, bandID, Color.white, level, 0);
            layer.selectedIndex = layerData.GetTemplates(layer.selectedLayerIndex).Count - 1;

            return true;
        }

        void RemoveTemplate()
        {
            var layer = mLogic.layer;
            if (layer.selectedIndex >= 0)
            {
                var template = layer.layerData.GetTemplates(layer.selectedLayerIndex)[layer.selectedIndex];
                int rows = layer.verticalTileCount;
                int cols = layer.horizontalTileCount;
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        int bandID = layer.layerData.GetTileBandID(layer.selectedLayerIndex, j, i);
                        if (bandID == template.bandID)
                        {
                            layer.SetTileBandID(layer.selectedLayerIndex, j, i, 0);
                        }
                    }
                }

                layer.layerData.RemoveNPCRegionTemplate(layer.selectedLayerIndex, layer.selectedIndex);
                layer.selectedIndex = layer.layerData.GetTemplates(layer.selectedLayerIndex).Count - 1;

                layer.layerView.RefreshTexture(layer.selectedLayerIndex);
            }
        }

        void ChangeTemplateBandID()
        {
            var layer = mLogic.layer;
            if (layer.selectedIndex >= 0)
            {
                var dlg = EditorUtils.CreateInputDialog("Change Region Band ID");
                var template = mLogic.layer.layerData.GetTemplates(layer.selectedLayerIndex)[layer.selectedIndex];
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Band ID", "", template.bandID.ToString()),
                };
                dlg.Show(items, OnClickChangeTemplateBandID);
            }
        }

        bool OnClickChangeTemplateBandID(List<InputDialog.Item> parameters)
        {
            var layer = mLogic.layer;
            int newBandID;
            bool suc = Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newBandID);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid band id!", "OK");
                return false;
            }
            if (newBandID <= 0 || newBandID >= int.MaxValue)
            {
                EditorUtility.DisplayDialog("Error", "Band ID must be > 0 and < int.MaxValue", "OK");
                return false;
            }

            var layerData = mLogic.layer.layerData;
            if (layerData.GetNPCRegionTemplateByBandID(layer.selectedLayerIndex, newBandID) != null)
            {
                EditorUtility.DisplayDialog("Error", $"Band ID {newBandID} is already used!", "OK");
                return false;
            }

            var template = layerData.GetTemplates(layer.selectedLayerIndex)[layer.selectedIndex];
            int rows = layer.verticalTileCount;
            int cols = layer.horizontalTileCount;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (layerData.GetTileBandID(layer.selectedLayerIndex, j, i) == template.bandID)
                    {
                        layer.SetTileBandID(layer.selectedLayerIndex, j, i, newBandID);
                    }
                }
            }
            template.bandID = newBandID;

            mLogic.layer.layerView.RefreshTexture(layer.selectedLayerIndex);
            return true;
        }

        void SetSelectedLayer(int layerIndex)
        {
            var layer = mLogic.layer;
            if (layer.selectedLayerIndex >= 0)
            {
                mLogic.layer.ShowLayer(layer.selectedLayerIndex, false);
            }
            layer.selectedLayerIndex = layerIndex;
            if (layer.selectedLayerIndex >= 0)
            {
                mLogic.layer.ShowLayer(layer.selectedLayerIndex, true);
                layer.selectedIndex = mLogic.layer.layerData.GetTemplates(layer.selectedLayerIndex).Count - 1;
            }
            else
            {
                layer.selectedIndex = -1;
            }

        }

        void CreateLayerNames()
        {
            var layers = mLogic.layer.layerData.layers;
            mLayerNames = new string[layers.Count];
            for (int i = 0; i < mLayerNames.Length; ++i)
            {
                mLayerNames[i] = layers[i].name;
            }
        }

        void AddLayer()
        {
            var dlg = EditorUtils.CreateInputDialog("Add New Layer");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "New Layer"),
                    new InputDialog.StringItem("Width", "新子layer的格子宽,0表示使用NPCRegionLayer的格子宽", "0"),
                    new InputDialog.StringItem("Height", "新子layer的格子高,0表示使用NPCRegionLayer的格子高", "0"),
                };
            dlg.Show(items, OnClickAddLayer);
        }

        void ChangeLayerName()
        {
            var dlg = EditorUtils.CreateInputDialog("Change Layer Name");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Name", "", "New Layer"),
                };
            dlg.Show(items, OnClickChangeLayerName);
        }

        bool OnClickChangeLayerName(List<InputDialog.Item> parameters)
        {
            var layer = mLogic.layer;
            var nameStr = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(nameStr))
            {
                EditorUtility.DisplayDialog("Error", "Invalid layer name!", "OK");
                return false;
            }

            bool found = mLogic.layer.layerData.HasLayer(nameStr);
            if (found)
            {
                EditorUtility.DisplayDialog("Error", "Name already exists!", "OK");
                return false;
            }

            mLogic.layer.SetLayerName(layer.selectedLayerIndex, nameStr);
            mLayerNames[layer.selectedLayerIndex] = nameStr;
            return true;
        }

        bool OnClickAddLayer(List<InputDialog.Item> parameters)
        {
            var nameStr = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(nameStr))
            {
                EditorUtility.DisplayDialog("Error", "Invalid layer name!", "OK");
                return false;
            }

            bool found = mLogic.layer.layerData.HasLayer(nameStr);
            if (found)
            {
                EditorUtility.DisplayDialog("Error", "Name already exists!", "OK");
                return false;
            }


            int overridenWidth;
            int overridenHeight;
            bool suc = int.TryParse((parameters[1] as InputDialog.StringItem).text, out overridenWidth);
            suc &= int.TryParse((parameters[2] as InputDialog.StringItem).text, out overridenHeight);
            if (!suc)
            {
                return false;
            }

            bool export = overridenWidth == 0 && overridenHeight == 0;
            mLogic.layer.AddLayer(nameStr, overridenWidth, overridenHeight, export);
            SetSelectedLayer(mLogic.layer.layerData.layers.Count - 1);
            return true;
        }

        void RemoveLayer()
        {
            if (EditorUtility.DisplayDialog("Warning", "This operation can't be undone, are you sure?", "Yes", "No"))
            {
                var layer = mLogic.layer;
                layer.RemoveLayer(layer.selectedLayerIndex);
                if (layer.layerData.layers.Count > 0)
                {
                    SetSelectedLayer(Mathf.Max(0, layer.selectedLayerIndex - 1));
                }
                else
                {
                    SetSelectedLayer(-1);
                }
            }
        }
    }
}

#endif