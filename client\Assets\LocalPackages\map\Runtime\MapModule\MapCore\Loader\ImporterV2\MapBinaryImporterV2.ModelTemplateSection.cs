﻿ 



 
 


using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadModelTemplates(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.ModelTemplate);
            int version = reader.ReadInt32();

            //------------------------version 1 start--------------------------------
            int nStrings = reader.ReadInt32();
            var stringTables = new string[nStrings];
            for (int i = 0; i < nStrings; ++i)
            {
                stringTables[i] = Utils.ReadString(reader);
            }
            mEditorData.modelTemplates.stringTables = stringTables;

            int nModelTemplates = reader.ReadInt32();
            var modelTemplates = new config.ModelTemplate[nModelTemplates];
            for (int i = 0; i < nModelTemplates; ++i)
            {
                modelTemplates[i] = LoadModelTemplateV1(reader);
            }
            //------------------------version 1 end--------------------------------
            mEditorData.modelTemplates.modelTemplates = modelTemplates;
            //------------------------version 2 start--------------------------------
            if (version >= 2)
            {
                mEditorData.modelTemplates.transformTable = LoadTransformTableData(reader);
                for (int i = 0; i < nModelTemplates; ++i)
                {
                    LoadModelTemplateV2(reader, modelTemplates[i]);
                }
            }
            //------------------------version 2 end--------------------------------
        }

        config.ModelTemplate LoadModelTemplateV1(BinaryReader reader)
        {
            var temp = new config.ModelTemplate();
            var id = reader.ReadInt32();
            temp.SetID(id);

            temp.bounds = Utils.ReadBounds(reader);
            temp.prefabPath = Utils.ReadString(reader);

            temp.isTileModelTemplate = reader.ReadBoolean();
            if (temp.isTileModelTemplate)
            {
                int nLODs = reader.ReadInt32();
                temp.childrenPrefabs = new config.ModelTemplateChildrenPrefabInfo[nLODs];
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    int childCount = reader.ReadInt32();
                    var childrenInfo = new config.ModelTemplateChildrenPrefabInfo();
                    childrenInfo.childrenPrefabBounds = new Rect[childCount];
                    childrenInfo.childrenPrefabPathIndices = new short[childCount];
                    childrenInfo.childrenPrefabTileObjectType = new byte[childCount];
                    childrenInfo.childrenPrefabPosition = new Vector3[childCount];
                    childrenInfo.childrenPrefabRotationIndex = new short[childCount];
                    childrenInfo.childrenPrefabScalingIndex = new short[childCount];
                    for (int i = 0; i < childCount; ++i)
                    {
                        childrenInfo.childrenPrefabPathIndices[i] = (short)reader.ReadInt32();
                        childrenInfo.childrenPrefabBounds[i] = Utils.ReadRect(reader);
                        childrenInfo.childrenPrefabTileObjectType[i] = (byte)reader.ReadInt32();
                        childrenInfo.childrenPrefabPosition[i] = Utils.ReadVector3(reader);
                        /*childrenInfo.childrenPrefabScaling[i] = */
                        Utils.ReadVector3(reader);
                        /*childrenInfo.childrenPrefabRotation[i] = */
                        Utils.ReadQuaternion(reader);
                    }

                    temp.childrenPrefabs[lod] = childrenInfo;
                }
            }

            temp.lodInfo = new config.ModelTemplateLODInfo();
            int n = reader.ReadInt32();
            temp.lodInfo.lodPrefabPathIndices = new List<int>(n);
            for (int i = 0; i < n; ++i)
            {
                temp.lodInfo.lodPrefabPathIndices.Add(reader.ReadInt32());
            }
            n = reader.ReadInt32();
            temp.lodInfo.existedLODs = new List<int>(n);
            for (int i = 0; i < n; ++i)
            {
                temp.lodInfo.existedLODs.Add(reader.ReadInt32());
            }

            temp.preload = reader.ReadBoolean();
            return temp;
        }


        void LoadModelTemplateV2(BinaryReader reader, config.ModelTemplate temp)
        {
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.childrenPrefabs.Length;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.childrenPrefabs[lod].childrenPrefabPosition;
                    var scalingIndices = temp.childrenPrefabs[lod].childrenPrefabScalingIndex;
                    var rotationIndices = temp.childrenPrefabs[lod].childrenPrefabRotationIndex;
                    int n = childPrefabTransformList.Length;
                    for (int i = 0; i < n; ++i)
                    {
                        scalingIndices[i] = reader.ReadInt16();
                        rotationIndices[i] = reader.ReadInt16();
                    }
                }
            }
        }

        config.TransformTable LoadTransformTableData(BinaryReader reader)
        {
            config.TransformTable table = new config.TransformTable();
            table.scalings = Utils.ReadVector3Array(reader);
            table.rotations = Utils.ReadQuaternionArray(reader);
            return table;
        }
    }
}
