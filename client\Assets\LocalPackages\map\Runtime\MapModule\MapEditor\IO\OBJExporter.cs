﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Text;
using System.IO;

namespace TFW.Map
{
    [Black]
    public static class OBJExporter
    {
        public static void Export(string filePath, Vector3[] vertices, Vector2[] uvs, Color[] colors, int[] indices)
        {
            if (vertices != null && indices != null)
            {
                StringBuilder objBuilder = new StringBuilder();
                for (int i = 0; i < vertices.Length; ++i)
                {
                    if (colors != null)
                    {
                        objBuilder.Append(string.Format("v {0} {1} {2} {3} {4} {5}\n", vertices[i].x, vertices[i].y, vertices[i].z, colors[i].r, colors[i].g, colors[i].b));
                    }
                    else
                    {
                        objBuilder.Append(string.Format("v {0} {1} {2}\n", vertices[i].x, vertices[i].y, vertices[i].z));
                    }
                }

                if (uvs != null)
                {
                    for (int i = 0; i < vertices.Length; ++i)
                    {
                        objBuilder.Append(string.Format("vt {0} {1}\n", uvs[i].x, uvs[i].y));
                    }
                }

                if (uvs != null)
                {
                    for (int i = 0; i < indices.Length; i += 3)
                    {
                        int a = indices[i] + 1;
                        int b = indices[i + 1] + 1;
                        int c = indices[i + 2] + 1;
                        objBuilder.Append(string.Format("f {0}/{1} {2}/{3} {4}/{5}\n", a,a, b,b, c,c));
                    }
                }
                else
                {
                    for (int i = 0; i < indices.Length; i += 3)
                    {
                        objBuilder.Append(string.Format("f {0} {1} {2}\n", indices[i] + 1, indices[i + 1] + 1, indices[i + 2] + 1));
                    }
                }

                var str = objBuilder.ToString();
                File.WriteAllText(filePath, str);
            }
        }
    }
}


#endif