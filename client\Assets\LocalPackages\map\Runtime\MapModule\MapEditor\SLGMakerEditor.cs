﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;

namespace TFW.Map
{
    public class UpdateMapConfig
    {
        public bool generateCollisions = true;
        public bool generateDecorationLayerObjects = true;
        public bool generateNPCRegionLayerData = true;
        public bool generateNPCSpawnPoints = true;
    }

    [ExecuteInEditMode]
    public class SLGMakerEditor : MonoBehaviour
    {
        void OnDestroy()
        {
            Map.currentMap = null;
        }

        void OnEnable()
        {
            mInstance = this;
            float size = 100000;
            mEditorViewRect = Rect.MinMaxRect(-size, -size, size, size);
        }

        private void OnDrawGizmos()
        {
            //var map = Map.currentMap;
            //if (map != null)
            //{
            //    var viewRect = map.GetViewportRect(map.camera, map.camera.transform.position);
            //    var center = new Vector3(viewRect.center.x, 0, viewRect.center.y);
            //    var size = new Vector3(viewRect.size.x, 0, viewRect.size.y);
            //    Gizmos.color = Color.red;
            //    Gizmos.DrawWireCube(center, size);
            //}
        }

        void Start()
        {
            mInstance = this;
        }

        void Update()
        {
            DisableMovement();
        }

        void DisableMovement()
        {
            var transform = gameObject.transform;
            if (transform.position != Vector3.zero)
            {
                transform.position = Vector3.zero;
            }
            if (transform.rotation != Quaternion.identity)
            {
                transform.rotation = Quaternion.identity;
            }
            if (transform.localScale != Vector3.one)
            {
                transform.localScale = Vector3.one;
            }
        }

        public static Rect GetRenderArea()
        {
            var camera = Map.currentMap.camera.firstCamera;
            return Map.currentMap.GetViewportRect(camera, camera.fieldOfView, camera.transform.position);
        }

        public Map CreateNewMap(GameMapType mapType, float mapWidth, float mapHeight)
        {
            Reset();

            bool isDefaultMap;
            var map = CreateMap(mapType, "", mapWidth, mapHeight, null, out isDefaultMap);
            if (map != null)
            {
                SaveMap(projectFolder);
            }
            return map;
        }

#if true
        public void LoadMap(string projectPath)
        {
            Reset();
            projectFolder = projectPath;
            
            bool isDefaultMap;

            MapCameras cameras = null;
            GameObject cameraObject = null;
            if (EditorApplication.isPlaying)
            {
                cameraObject = new GameObject("Camera");
                var camera = cameraObject.AddComponent<Camera>();
                camera.clearFlags = CameraClearFlags.SolidColor;
                camera.farClipPlane = 10000;
                camera.depthTextureMode = DepthTextureMode.Depth;
                cameras = new MapCameras(cameraObject, true);
            }

            //SceneView.GetAllSceneCameras()[0].depthTextureMode = DepthTextureMode.Depth;
            //Camera.current.depthTextureMode = DepthTextureMode.Depth;
            var map = CreateMap(GameMapType.Empty, projectPath, 1000, 1000, cameras, out isDefaultMap);

            if (cameraObject != null && map != null)
            {
                cameraObject.transform.SetParent(map.root.transform, true);
            }

            SceneView.lastActiveSceneView.LookAt(map.center);
        }


        string CheckCorrectness()
        {
            string errorMsg = "";

            var map = Map.currentMap as EditorMap;
            if (map.saveRiverMaterials && string.IsNullOrEmpty(map.riverMaterialsFolder))
            {
                return "river material save folder is empty!";
            }

            var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (riverLayer != null)
            {
                Dictionary<int, string> riverPrefabInfo = riverLayer.riverPrefabInfos;
                foreach (var pair in riverPrefabInfo)
                {
                    int sectionID = pair.Key;
                    string prefabPath = pair.Value;
                    int sectionIndex;
                    PolygonRiverData river = riverLayer.GetRiverFromSectionID(sectionID, out sectionIndex);
                    if (river == null)
                    {
                        errorMsg += "you need generate river assets!\n";
                        break;
                    }
                }
            }

            return errorMsg;
        }

        public void SaveMap(string folderPath = null)
        {
            if (folderPath == null)
            {
                folderPath = projectFolder;
            }
            if (Map.currentMap != null)
            {
                AssetDatabase.StartAssetEditing();

                try
                {
                    float progress = 0;
                    EditorUtility.DisplayProgressBar("Saving Map...", "Validating Map Data...", Mathf.Min(1.0f, progress));
                    progress += 0.1f;
                    string errMsg = CheckCorrectness();
                    if (string.IsNullOrEmpty(errMsg))
                    {
                        var editorMap = Map.currentMap as EditorMap;

                        EditorUtility.DisplayProgressBar("Saving Map...", "Invoking Save Map Event...", Mathf.Min(1.0f, progress));
                        progress += 0.03f;
                        editorMap.InvokeSaveMapEvent();

                        EditorUtility.DisplayProgressBar("Saving Map...", "Updating Decoration Layer Object Transform...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        var decorationLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
                        if (decorationLayer != null)
                        {
                            decorationLayer.UpdateTransformChangedObjects();
                        }

                        EditorUtility.DisplayProgressBar("Saving Map...", "Copying Map Layer LOD Setting...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        var lodLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_LOD) as LODLayer;
                        if (lodLayer != null)
                        {
                            lodLayer.CopyFromMapLODSetting();
                        }

                        EditorUtility.DisplayProgressBar("Saving Map...", "Updating Ruin Layer Object Transform...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
                        foreach (var layer in ruinLayers)
                        {
                            layer.UpdateTransformChangedObjects();
                        }

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Map Data...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        MapBinarySaver.Save(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Decoration Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.1f;
                        SaveComplexGridModelLayer(decorationLayer, folderPath);

                        var decorationBorderLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER) as DecorationBorderLayer;
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Decoration Border Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.1f;
                        SaveDecorationBorderLayer(decorationBorderLayer, folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Collision Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveMapCollisionLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Camera Collider Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveCameraColliderLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Ruin Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveRuinLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving NPC Region Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveNPCRegionLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Front Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveGridModelLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Ground Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveBlendTerrainLayer(folderPath);
                        progress += 0.05f;
                        SaveVaryingTileSizeTerrainLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving LOD Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.01f;
                        SaveLODLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving NPC Spawn Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveEntitySpawnLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving NavMesh Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.03f;
                        SaveNavMeshLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Polygon River Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SavePolygonRiverLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Circle Border Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.01f;
                        SaveCircleBorderLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Railway Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        SaveRailwayLayer(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Grid Region Editor...", Mathf.Min(1.0f, progress));
                        progress += 0.01f;
                        SaveGridRegionEditor(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Detail Sprites...", Mathf.Min(1.0f, progress));
                        progress += 0.01f;
                        SaveDetailSprites(folderPath);
                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Spline Editor...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveSplineObjects(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Split Fog Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveSplitFogLayer(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving City Territory Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveEditorTerritoryLayer(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Region Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveRegionLayer(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Region Color Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveRegionColorLayer(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Saving Building Grid Layer...", Mathf.Min(1.0f, progress));
                        progress += 0.02f;
                        SaveBuildingGridLayer(folderPath);

                        //save plugin layers
                        progress += 0.02f;
                        SavePluginLayers(folderPath);

                        EditorUtility.DisplayProgressBar("Saving Map...", "Refresh Project...", Mathf.Min(1.0f, progress));
                        progress += 0.05f;
                        
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", errMsg, "OK");
                    }

                    EditorUtility.ClearProgressBar();
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }

                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }
        }

        void DeletePluginLayerSaveFiles(string folderPath)
        {
            var pluginLayerInfos = MapPlugin.pluginLayerInfos;
            for (int i = 0; i < pluginLayerInfos.Count; ++i)
            {
                var dataPath = folderPath + "/" + pluginLayerInfos[i].editorSaveFileName;
                if (File.Exists(dataPath))
                {
                    FileUtil.DeleteFileOrDirectory(dataPath);
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        void SavePluginLayers(string folderPath)
        {
            DeletePluginLayerSaveFiles(folderPath);

            var map = Map.currentMap;
            int layerCount = map.GetMapLayerCount();
            for (int i = 0; i < layerCount; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (MapPlugin.IsPluginLayer(layer.name))
                {
                    var pluginLayer = layer as MapPluginLayerBase;
                    pluginLayer.SaveEditorData(folderPath);
                }
            }
        }

        void SaveComplexGridModelLayer(ComplexGridModelLayer layer, string folderPath)
        {
            var dataPath = folderPath + "/" + MapCoreDef.COMPLEX_GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveDecorationBorderLayer(DecorationBorderLayer layer, string folderPath)
        {
            var dataPath = folderPath + "/" + MapCoreDef.DECORATION_BORDER_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveGridModelLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            var dataPath = folderPath + "/" + MapCoreDef.GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveBlendTerrainLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            var dataPath = folderPath + "/" + MapCoreDef.BLEND_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveVaryingTileSizeTerrainLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND) as VaryingTileSizeTerrainLayer;
            var dataPath = folderPath + "/" + MapCoreDef.VARYING_TILE_SIZE_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveLODLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_LOD) as LODLayer;
            var dataPath = folderPath + "/" + MapCoreDef.LOD_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveEntitySpawnLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_ENTITY_SPAWN) as EntitySpawnLayer;
            var dataPath = folderPath + "/" + MapCoreDef.ENTITY_SPAWN_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveNavMeshLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;
            var dataPath = folderPath + "/" + MapCoreDef.NAVMESH_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SavePolygonRiverLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            var dataPath = folderPath + "/" + MapCoreDef.POLYGON_RIVER_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveCircleBorderLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER) as CircleBorderLayer;
            var dataPath = folderPath + "/" + MapCoreDef.CIRCLE_BORDER_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveSplitFogLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_SPLIT_FOG) as SplitFogLayer;
            var dataPath = folderPath + "/" + MapCoreDef.SPLIT_FOG_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveEditorTerritoryLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY) as EditorTerritoryLayer;
            var dataPath = folderPath + "/" + MapCoreDef.EDITOR_TERRITORY_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveRegionLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_REGION) as RegionLayer;
            var dataPath = folderPath + "/" + MapCoreDef.REGION_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveRegionColorLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_REGION_COLOR) as RegionColorLayer;
            var dataPath = folderPath + "/" + MapCoreDef.REGION_COLOR_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveBuildingGridLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_BUILDING_GRID) as BuildingGridLayer;
            var dataPath = folderPath + "/" + MapCoreDef.BUILDING_GRID_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveRailwayLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_RAILWAY) as RailwayLayer;
            var dataPath = folderPath + "/" + MapCoreDef.RAILWAY_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveMapCollisionLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (layer != null)
            {
                layer.RemoveEmptyCollisions();
            }

            var dataPath = folderPath + "/" + MapCoreDef.MAP_COLLISION_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveCameraColliderLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER) as CameraColliderLayer;
            var dataPath = folderPath + "/" + MapCoreDef.MAP_CAMERA_COLLIDER_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveRuinLayerHeader(string folderPath)
        {
            string dataPath = folderPath + "/" + MapCoreDef.MAP_RUIN_LAYER_HEADER_FILE_NAME;
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.RuinLayerHeaderVersion);

            var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
            writer.Write(ruinLayers.Count);
            for (int i = 0; i < ruinLayers.Count; ++i)
            {
                Utils.WriteString(writer, ruinLayers[i].name);
            }

            var data = stream.ToArray();

            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveRuinLayer(string folderPath)
        {
            //save header
            SaveRuinLayerHeader(folderPath);

            var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
            foreach (var layer in ruinLayers)
            {
                string dataPath = null;
                if (layer.name == MapCoreDef.MAP_LAYER_NODE_RUIN)
                {
                    dataPath = folderPath + "/" + MapCoreDef.MAP_RUIN_LAYER_EDITOR_DATA_FILE_NAME;
                }
                else
                {
                    dataPath = $"{folderPath}/{Utils.GetPathName(layer.name, false)}.config";
                }
                if (layer == null)
                {
                    FileUtil.DeleteFileOrDirectory(dataPath);
                }
                else
                {
                    layer.Save(dataPath);
                }
            }
        }

        void SaveNPCRegionLayer(string folderPath)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION) as NPCRegionLayer;
            var dataPath = folderPath + "/" + MapCoreDef.MAP_NPC_REGION_LAYER_EDITOR_DATA_FILE_NAME;
            if (layer == null)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                layer.Save(dataPath);
            }
        }

        void SaveGridRegionEditor(string folderPath)
        {
            var editor = (Map.currentMap.data as EditorMapData).gridRegionEditor;

            var dataPath = folderPath + "/" + MapCoreDef.GRID_REGION_EDITOR_DATA_FILE_NAME;
            if (!editor.HasRegionData())
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                editor.Save(dataPath);
            }
        }

        void SaveDetailSprites(string folderPath)
        {
            var dataPath = folderPath + "/" + MapCoreDef.DETAIL_SPRITES_EDITOR_DATA_FILE_NAME;
            DetailSpriteSetting.Save(dataPath);
        }

        void SaveSplineObjects(string folderPath)
        {
            var splineObjectManager = (Map.currentMap.data as EditorMapData).splineObjectManager;

            var dataPath = folderPath + "/" + MapCoreDef.SPLINE_OBJECTS_EDITOR_DATA_FILE_NAME;
            if (splineObjectManager.splineObjectCount == 0)
            {
                FileUtil.DeleteFileOrDirectory(dataPath);
            }
            else
            {
                splineObjectManager.Save(dataPath);
            }
        }

        string CheckExportSetting()
        {
            //check lod
            var map = Map.currentMap;
            int layerCount = map.GetMapLayerCount();
            for (int k = 0; k < layerCount; ++k)
            {
                var layerData = map.GetMapLayerByIndex(k).GetLayerData();
                if (layerData != null)
                {
                    var layerLODConfig = layerData.lodConfig;
                    if (layerLODConfig != null)
                    {
                        bool validZoom = layerLODConfig.CheckZoom();
                        if (!validZoom)
                        {
                            return $"Invalid lod zoom in layer {layerData.name}";
                        }
                    }
                }
            }

            var editorMapData = map.data as EditorMapData;
            bool valid = editorMapData.splineObjectManager.lodConfig.CheckZoom();
            if (!valid)
            {
                return $"Invalid lod zoom in spline layer";
            }

            bool ok = map.data.lodManager.Validate();
            if (!ok)
            {
                return $"Invalid map lod height setting, please check!";
            }

            //check blend terrain layer
            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            if (blendTerrainLayer != null && !blendTerrainLayer.IsInValidState())
            {
                return "Can't combine terrain tiles!\n";
            }

            var regionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_REGION) as RegionLayer;
            if (regionLayer != null)
            {
                string errorMsg = regionLayer.CheckValidation();
                if (!string.IsNullOrEmpty(errorMsg))
                {
                    return $"{errorMsg}\n";
                }
            }

            return "";
        }

        void GenerateCollisions(bool showProgressBar)
        {
            var editorMap = Map.currentMap as EditorMap;

            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Creating Obstacles...", 0);
            MapEditorUI.CreateObstacles(SLGMakerEditor.instance.projectFolder);

            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Creating Navmesh Data...", 0.4f);
            var navMeshLayer = Map.currentMap.GetMapLayerOfType(typeof(NavMeshLayer)) as NavMeshLayer;
            if (navMeshLayer != null)
            {
                //StopWatchWrapper w = new StopWatchWrapper();
                //w.Start();
                var meshies = navMeshLayer.CreateNavMesh(2, 2, PrefabOutlineType.NavMeshObstacle, 0, 100, 180000, false, editorMap.navMeshMode);
                //var t = w.Stop();
                //Debug.Log($"CreateNavMesh cost: {t} seconds");
                if (meshies != null)
                {
                    var layerView = navMeshLayer.layerView;
                    layerView.ShowNavMesh(true);
                }
                //w.Start();
                EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting Navmesh Data...", 0.45f);
                NavMeshLayerLogic.ExportNavMesh(navMeshLayer, SLGMakerEditor.instance.projectFolder);
                //t = w.Stop();
                //Debug.LogError($"ExportNavMesh: {t}");
            }
        }

        void GenerateDecorationLayerObjects(bool showProgressBar)
        {
            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting Decoration Layer Data...", 0.1f);
            var decorationLayer = Map.currentMap.GetMapLayerOfType(typeof(EditorComplexGridModelLayer)) as EditorComplexGridModelLayer;
            if (decorationLayer != null)
            {
                decorationLayer.UpdateTransformChangedObjects();
                decorationLayer.ExportData();
            }
        }

        void GenerateDecorationBorderLayerObjects(bool showProgressBar)
        {
            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting Decoration Border Layer Data...", 0.1f);
            var decorationBorderLayer = Map.currentMap.GetMapLayerOfType(typeof(DecorationBorderLayer)) as DecorationBorderLayer;
            if (decorationBorderLayer != null)
            {
                decorationBorderLayer.UpdateTransformChangedObjects();
                decorationBorderLayer.ExportData();
            }
        }

        void GenerateRuinLayerObjects(bool showProgressBar)
        {
            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Updating Ruin Data...", 0.2f);
            var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
            foreach (var layer in ruinLayers)
            {
                layer.UpdateTransformChangedObjects();
            }
        }

        void GenerateNPCRegionLayerData(bool showProgressBar)
        {
            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting NPC Region Data...", 0.7f);
            var npcRegionLayer = Map.currentMap.GetMapLayerOfType(typeof(NPCRegionLayer)) as NPCRegionLayer;
            if (npcRegionLayer != null)
            {
                string filePath = SLGMakerEditor.instance.projectFolder + "/map_npc_refresh_zone.tsv";
                npcRegionLayer.ExportCSV(0, filePath, true);
            }
        }

        void GenerateNPCSpawnPoints(bool showProgressBar)
        {
            EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Generating NPC Spawn Points...", 0.8f);
            var entitySpawnLayer = Map.currentMap.GetMapLayerOfType(typeof(EntitySpawnLayer)) as EntitySpawnLayer;
            if (entitySpawnLayer != null)
            {
                bool isCircleMap = Map.currentMap.data.isCircleMap;
                entitySpawnLayer.GenerateSpawnPoints(false, isCircleMap ? Map.currentMap.mapWidth * 0.5f : 0);
                EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting NPC Spawn Points...", 0.85f);
                entitySpawnLayer.Export(SLGMakerEditor.instance.projectFolder, false);
            }
        }

        void GenerateCityTerritoryLayerData(bool showProgressBar)
        {
#if false
            //this operation cost too much time, so disable it for now
            var layer = Map.currentMap.GetMapLayerOfType(typeof(EditorTerritoryLayer)) as EditorTerritoryLayer;
            if (layer != null)
            {
                EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Generating City Territory Layer Assets", 0.85f);
                layer.GenerateOutlineAssets(SLGMakerEditor.instance.exportFolder);
            }    
#endif
        }

        void ExportOtherConfigs(bool showProgressBar, UpdateMapConfig config)
        {
            if (Map.currentMap != null)
            {
                var collisionLayer = Map.currentMap.GetMapLayer<MapCollisionLayer>();
                bool usePrefabOutline = MapModule.usePrefabOutline;
                var decorationLayer = Map.currentMap.GetMapLayerOfType(typeof(EditorComplexGridModelLayer)) as EditorComplexGridModelLayer;
                bool cullDecorationObject = false;
                if (decorationLayer != null)
                {
                    cullDecorationObject = decorationLayer.layerData.enableCullIntersectedObjects;
                }
                bool addPrefabOutline = config.generateCollisions || config.generateNPCRegionLayerData || config.generateNPCSpawnPoints || cullDecorationObject;
                if (addPrefabOutline)
                {
                    if (collisionLayer != null)
                    {
                        collisionLayer.AddObjectFromPrefabOutlines();
                    }
                    MapModule.usePrefabOutline = false;
                }

                if (config.generateCollisions)
                {
                    GenerateCollisions(showProgressBar);
                }

                if (config.generateDecorationLayerObjects)
                {
                    GenerateDecorationLayerObjects(showProgressBar);
                }

                GenerateDecorationBorderLayerObjects(showProgressBar);

                GenerateRuinLayerObjects(showProgressBar);

                if (config.generateNPCRegionLayerData)
                {
                    GenerateNPCRegionLayerData(showProgressBar);
                }

                if (config.generateNPCSpawnPoints)
                {
                    GenerateNPCSpawnPoints(showProgressBar);
                }

                GenerateCityTerritoryLayerData(showProgressBar);

                EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Export Grid Region Data...", 0.9f);
                var gridRegionEditor = (Map.currentMap.data as EditorMapData).gridRegionEditor;
                gridRegionEditor.Export();

                if (addPrefabOutline)
                {
                    if (collisionLayer != null)
                    {
                        collisionLayer.RemovePrefabOutlines();
                    }
                    MapModule.usePrefabOutline = usePrefabOutline;
                }

                EditorConfig.dirtyFlag = 0;
            }
        }

        public void UpdateMap(bool showProgressBar)
        {
            try
            {
                if (exportFolder.Length > 0)
                {
                    string msg = CheckExportSetting();
                    if (string.IsNullOrEmpty(msg))
                    {
                        StopWatchWrapper w = new StopWatchWrapper();
                        w.Start();
                        ExportOtherConfigs(showProgressBar, mUpdateMapConfig);
                        //also export map
                        EditorUtils.ShowProgressiveBar(showProgressBar, "Updating...", "Exporting Map Data...", 1.0f);
                        ExportMap(exportFolder, false);
                        EditorUtility.ClearProgressBar();
                        var t = w.Stop();
                        Debug.Log($"UpdateMap cost: {t} seconds");
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", msg, "OK");
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Select export folder first!", "OK");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Update map failed {e.ToString()}");
                EditorUtility.ClearProgressBar();
            }

        }

        public void ExportMap(string folderPath, bool checkError)
        {
            if (Map.currentMap != null)
            {
                string msg = CheckExportSetting();
                if (string.IsNullOrEmpty(msg) || !checkError)
                {
                    var editorMap = Map.currentMap as EditorMap;
                    editorMap.InvokeExportMapEvent();

                    var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
                    if (layer != null)
                    {
                        layer.UpdateTransformChangedObjects();
                    }

                    var territoryLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY) as EditorTerritoryLayer;
                    if (territoryLayer != null)
                    {
                        territoryLayer.Export(SLGMakerEditor.instance.projectFolder);
                    }

                    var lodLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_LOD) as LODLayer;
                    if (lodLayer != null)
                    {
                        lodLayer.CopyFromMapLODSetting();
                    }

                    var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
                    foreach (var ruinLayer in ruinLayers)
                    {
                        ruinLayer.UpdateTransformChangedObjects();
                    }

                    var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
                    if (collisionLayer != null)
                    {
                        collisionLayer.ExportCameraLookAtArea();
                    }

                    MapBinaryExporter1 saver1 = new MapBinaryExporter1();
                    saver1.Save(folderPath);

                    var splineObjectManager = (Map.currentMap.data as EditorMapData).splineObjectManager;
                    splineObjectManager.Export();

                    //export front layer data as separate files
                    var frontLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
                    if (frontLayer != null)
                    {
                        string folder = MapCoreDef.GetOptimizedFrontTileDataFolder(exportFolder);
                        if (!Directory.Exists(folder))
                        {
                            Directory.CreateDirectory(folder);
                        }
                        //导出内存占用更少的数据结构,但为了保证老版本的兼容,旧的数据结构还是要导出,新项目或强更的版本可以直接忽略老数据导出
                        frontLayer.Export(MapCoreDef.GetOptimizedFrontTileDataPath(exportFolder));
                    }

                    AssetDatabase.Refresh();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", msg, "OK");
                }
            }
        }
#endif

        Map CreateMap(GameMapType mapType, string dataPath, float mapWidth, float mapHeight, MapCameras camera, out bool isDefaultMap)
        {
            var sceneCameras = SceneView.GetAllSceneCameras();
            if (sceneCameras.Length == 0)
            {
                isDefaultMap = true;
                EditorUtility.DisplayDialog("Error", "No scene camera, please add a scene view first!", "OK");
                return null;
            }

            string mapConfigFilePath = Utils.TryToGetValidConfigFilePath();
            Debug.Assert(!string.IsNullOrEmpty(mapConfigFilePath));
            var mapConfig = MapConfig.CreateFromFile(mapConfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }

            isDefaultMap = false;

            if (camera == null)
            {
                var sceneCamera = sceneCameras[0];
                camera = new MapCameras(sceneCamera.gameObject, true);   
            }

            dataPath = dataPath + "/" + "mapData.config";
            var map = LoadEditorMap(mapType, dataPath, camera, mapWidth, mapHeight);
            var viewCenter = map.view.viewCenter;
            Utils.HideGameObject(viewCenter);
            map.root.gameObject.AddComponent<MapEditor>();

            SceneView.duringSceneGui -= this.OnSceneGUI;
            SceneView.duringSceneGui += this.OnSceneGUI;

            return map;
        }

        List<string> LoadRuinLayerHeader(string path)
        {
            if (!File.Exists(path))
            {
                return new List<string>() { MapCoreDef.MAP_LAYER_NODE_RUIN };
            }

            List<string> layerNames = new List<string>();
            var bytes = File.ReadAllBytes(path);
            MemoryStream stream = new MemoryStream(bytes);
            BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                layerNames.Add(Utils.ReadString(reader));
            }

            reader.Close();
            

            return layerNames;
        }

        Map LoadEditorMap(GameMapType maptype, string dataPath, MapCameras camera, float mapWidth, float mapHeight)
        {
            float progress = 0;
            EditorUtility.DisplayProgressBar("Loading..", "Loading Map Data...", progress);
            progress += 0.05f;

            Shader.SetGlobalFloat("_ToneMappingMin", 0);
            Shader.SetGlobalFloat("_ToneMappingMax", 1);
            Shader.SetGlobalColor("_GlobalDayNightColor", new Color(1, 1, 1, 1));

            string folderPath = Utils.GetFolderPath(dataPath);
            folderPath = folderPath.Replace('\\', '/');
            string mapName = Utils.GetPathName(folderPath, false);
            var eventListener = new EditorMapEventListener();
            var map = new EditorMap(mapName, maptype, eventListener);
            var data = MapLoaderManager.Load(dataPath, map);
            if (data == null)
            {
                data = new config.EditorMapData(mapWidth, mapHeight, 80, false, 0, 30, false, MapModule.defaultNavMeshCreateMode, MapModule.defaultGlobalObstacleCreateMode, MapModule.defaultGroundTileSize, MapModule.defaultFrontTileSize, new config.BackgroundSetting(), new Version(0, 0), new Bounds());
            }
            fov = data.map.camera.verticalFov;

            var viewport = new Rect(data.viewCenter.x - data.viewportSize.x * 0.5f,
                data.viewCenter.z - data.viewportSize.y * 0.5f, data.viewportSize.x, data.viewportSize.y);

            map.Load(data, camera, data.viewCenter, viewport);
            map.view.root.AddComponent<DisableKeyboardDelete>();
            data.OnDestroy();

            EditorUtility.DisplayProgressBar("Loading..", "Loading Front Layer...", progress);
            progress += 0.2f;
            //load grid model layer
            var gridModelLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME;
            EditorGridModelLayer.LoadEditorData(gridModelLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Decoration Layer...", progress);
            progress += 0.2f;
            //load complex grid model layer
            var complexGridModelLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.COMPLEX_GRID_MODEL_LAYER_EDITOR_DATA_FILE_NAME;
            ComplexGridModelLayer.LoadEditorData(complexGridModelLayerDataPath, typeof(EditorComplexGridModelLayer));

            EditorUtility.DisplayProgressBar("Loading..", "Loading Decoration Border Layer...", progress);
            progress += 0.1f;
            //load complex grid model layer
            var decorationBorderLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.DECORATION_BORDER_LAYER_EDITOR_DATA_FILE_NAME;
            DecorationBorderLayer.LoadEditorData(decorationBorderLayerDataPath, typeof(DecorationBorderLayer));

            EditorUtility.DisplayProgressBar("Loading..", "Loading Collision Layer...", progress);
            progress += 0.1f;
            //load map collision layer
            var mapCollisionLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_COLLISION_LAYER_EDITOR_DATA_FILE_NAME;
            MapCollisionLayer.LoadEditorData(mapCollisionLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Camera Collider Layer...", progress);
            progress += 0.05f;
            //load camera collider layer
            var cameraColliderLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_CAMERA_COLLIDER_LAYER_EDITOR_DATA_FILE_NAME;
            CameraColliderLayer.LoadEditorData(cameraColliderLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Ruin Layer...", progress);
            progress += 0.02f;
            //load ruin layer header
            var ruinLayerHeaderPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_RUIN_LAYER_HEADER_FILE_NAME;
            List<string> ruinLayerNames = LoadRuinLayerHeader(ruinLayerHeaderPath);
            //load ruin layer
            foreach (var layerName in ruinLayerNames)
            {
                string ruinLayerDataPath = null;
                if (layerName == MapCoreDef.MAP_LAYER_NODE_RUIN)
                {
                    ruinLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_RUIN_LAYER_EDITOR_DATA_FILE_NAME;
                }
                else
                {
                    ruinLayerDataPath = $"{SLGMakerEditor.instance.projectFolder}/{Utils.GetPathName(layerName, false)}.config";
                }
                RuinLayer.LoadEditorData(ruinLayerDataPath);
            }

            EditorUtility.DisplayProgressBar("Loading..", "Loading Railway Layer...", progress);
            progress += 0.01f;
            //load rail layer
            var railwayLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.RAILWAY_LAYER_EDITOR_DATA_FILE_NAME;
            RailwayLayer.LoadEditorData(railwayLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Circle Border Layer...", progress);
            progress += 0.01f;
            //load circle border layer
            var circleBorderLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.CIRCLE_BORDER_LAYER_EDITOR_DATA_FILE_NAME;
            CircleBorderLayer.LoadEditorData(circleBorderLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Polygon River Layer...", progress);
            progress += 0.1f;
            //load polygon river layer
            var polygonRiverLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.POLYGON_RIVER_LAYER_EDITOR_DATA_FILE_NAME;
            PolygonRiverLayer.LoadEditorData(polygonRiverLayerDataPath);

            //load navmesh layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading NavMesh Layer...", progress);
            progress += 0.02f;
            var navMeshLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.NAVMESH_LAYER_EDITOR_DATA_FILE_NAME;
            NavMeshLayer.LoadEditorData(navMeshLayerDataPath);

            //load building grid layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading Building Grid Layer...", progress);
            progress += 0.02f;
            var buildingGridLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.BUILDING_GRID_LAYER_EDITOR_DATA_FILE_NAME;
            BuildingGridLayer.LoadEditorData(buildingGridLayerDataPath);

            //load entity spawn layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading NPC Spawn Layer...", progress);
            progress += 0.05f;
            var entitySpawnLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.ENTITY_SPAWN_LAYER_EDITOR_DATA_FILE_NAME;
            EntitySpawnLayer.LoadEditorData(entitySpawnLayerDataPath);

            //load lod layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading LOD Layer...", progress);
            progress += 0.01f;
            var lodLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.LOD_LAYER_EDITOR_DATA_FILE_NAME;
            LODLayer.LoadEditorData(lodLayerDataPath);

            //load npc region layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading NPC Region Layer...", progress);
            progress += 0.01f;
            var npcRegionLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.MAP_NPC_REGION_LAYER_EDITOR_DATA_FILE_NAME;
            NPCRegionLayer.LoadEditorData(npcRegionLayerDataPath);

            //load blend terrain layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading Ground Layer...", progress);
            progress += 0.1f;
            var blendTerrainLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.BLEND_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME;
            BlendTerrainLayer.LoadEditorData(blendTerrainLayerDataPath);

            //load varying tile size terrain layer
            progress += 0.1f;
            var varyingTileSizeTerrainLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.VARYING_TILE_SIZE_TERRAIN_LAYER_EDITOR_DATA_FILE_NAME;
            VaryingTileSizeTerrainLayer.LoadEditorData(varyingTileSizeTerrainLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading City Territory Layer...", progress);
            progress += 0.05f;
            //load editor territory layer
            var editorTerritoryLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.EDITOR_TERRITORY_LAYER_EDITOR_DATA_FILE_NAME;
            EditorTerritoryLayer.LoadEditorData(editorTerritoryLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Region Layer...", progress);
            progress += 0.05f;
            //load editor territory layer
            var regionLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.REGION_LAYER_EDITOR_DATA_FILE_NAME;
            RegionLayer.LoadEditorData(regionLayerDataPath);

            EditorUtility.DisplayProgressBar("Loading..", "Loading Split Fog Layer...", progress);
            progress += 0.1f;
            //load split fog layer
            var splitFogLayerDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.SPLIT_FOG_LAYER_EDITOR_DATA_FILE_NAME;
            SplitFogLayer.LoadEditorData(splitFogLayerDataPath);

            //load plugin layers
            LoadPluginLayers(SLGMakerEditor.instance.projectFolder);

            //load grid region editor
            EditorUtility.DisplayProgressBar("Loading..", "Loading Grid Region Data...", progress);
            progress += 0.01f;
            var gridRegionEditorDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.GRID_REGION_EDITOR_DATA_FILE_NAME;
            GridRegionEditor.LoadEditorData(gridRegionEditorDataPath);

            //load region color layer
            EditorUtility.DisplayProgressBar("Loading..", "Loading Region Color Data...", progress);
            progress += 0.01f;
            var regionColorDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.REGION_COLOR_LAYER_EDITOR_DATA_FILE_NAME;
            RegionColorLayer.LoadEditorData(regionColorDataPath);

            //load detail sprites
            EditorUtility.DisplayProgressBar("Loading..", "Loading Detail Sprites...", progress);
            progress += 0.02f;
            var detailSpriteEditorDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.DETAIL_SPRITES_EDITOR_DATA_FILE_NAME;
            DetailSpritesSetting.LoadEditorData(detailSpriteEditorDataPath);

            //load spline objects
            EditorUtility.DisplayProgressBar("Loading..", "Loading Spline Objects...", progress);
            progress += 0.05f;
            var splineObjectsDataPath = SLGMakerEditor.instance.projectFolder + "/" + MapCoreDef.SPLINE_OBJECTS_EDITOR_DATA_FILE_NAME;
            SplineObjectManager.LoadEditorData(splineObjectsDataPath);

            var viewportObj = new GameObject(MapCoreDef.VIEWPORT_OBJECT_NAME);
            viewportObj.transform.SetParent(map.view.root.transform, true);
            viewportObj.transform.position = data.viewCenter;
            mViewportControl = viewportObj.AddComponent<ViewportControl>();
            viewportObj.AddComponent<DisableKeyboardDelete>();

            var gridRegionEditorObject = new GameObject(MapCoreDef.GRID_REGION_EDITOR_NAME);
            gridRegionEditorObject.transform.SetParent(map.view.root.transform, true);
            gridRegionEditorObject.AddComponent<GridRegionEditorLogic>();
            gridRegionEditorObject.AddComponent<DisableKeyboardDelete>();

            map.root.gameObject.AddComponent<PhysxSetup>();

            EditorUtility.DisplayProgressBar("Loading..", "Creating Objects In Viewport...", Mathf.Max(progress, 0.8f));
            map.RefreshObjectsInViewport();

            EditorUtility.ClearProgressBar();

            return map;
        }

        void LoadPluginLayers(string projectFolderPath)
        {
            var pluginInfos = MapPlugin.pluginLayerInfos;
            for (int i = 0; i < pluginInfos.Count; ++i)
            {
                var layer = MapPlugin.CreatePluginLayer(pluginInfos[i].layerClassName, Map.currentMap);
                if (layer != null)
                {
                    bool ok = layer.LoadEditorData(projectFolderPath);
                    if (ok)
                    {
                        Map.currentMap.AddMapLayer(layer);
                    }
                }
            }
        }

        Map LoadRuntimeMap(string dataPath, Camera camera)
        {
            Debug.Assert(false, "todo");
            return null;
        }

        public void Reset()
        {
            EditorConfig.Reset();

            int childCount = transform.childCount;
            for (int i = childCount - 1; i >= 0; --i)
            {
                GameObject.DestroyImmediate(transform.GetChild(i).gameObject);
            }

            if (Map.currentMap != null)
            {
                Map.currentMap.Unload();
                Map.currentMap = null;
                //System.GC.Collect();
            }
            var mapEditor = GameObject.FindObjectOfType<MapEditor>();
            if (mapEditor)
            {
                GameObject.DestroyImmediate(mapEditor.gameObject);
            }
            MapEditor.instance = null;

            var obj = GameObject.Find("Pool Objects");
            GameObject.DestroyImmediate(obj);

            mPrefabIndicator.OnDestroy();
            mColorTileIndicator.OnDestroy();
            mRenderTextureObjectIndicator.OnDestroy();
            mBrushIndicator.OnDestroy();
            mActionManager.OnDestroy();

            TilePrefabConvexHullCache.Clear();

            SceneView.duringSceneGui -= this.OnSceneGUI;
        }

        public void ClearProjectFolder(string projectFolder)
        {
            if (IsProjectFolder())
            {
                if (projectFolder != null)
                {
                    if (Directory.Exists(projectFolder))
                    {
                        var entries = Directory.EnumerateFileSystemEntries(projectFolder);
                        foreach (var e in entries)
                        {
                            FileUtil.DeleteFileOrDirectory(e);
                        }
                    }
                }
            }
        }

        public void SetEventListener(EditorEventListener listener)
        {
            mListener = listener;
        }

        public static Map GetMap()
        {
            return Map.currentMap;
        }

        string ConvertToRelativePath(string folderPath)
        {
            string relativePath = "";
            string assetPattern = "/Assets";
            var startIndex = folderPath.IndexOf(assetPattern);
            if (startIndex >= 0)
            {
                relativePath = folderPath.Substring(startIndex + 1);
            }
            return relativePath;
        }

        bool IsProjectFolder()
        {
            if (projectFolder == null || projectFolder.Length == 0)
            {
                return false;
            }

            string projectFilePath = projectFolder + "/" + "mapData.config";
            if (Directory.Exists(projectFolder) && File.Exists(projectFilePath))
            {
                return true;
            }
            return false;
        }

        void OnSceneGUI(SceneView sceneView)
        {
            var map = Map.currentMap;
            if (map != null)
            {
                int mapLayerCount = map.GetMapLayerCount();
                if (mMapLayerNames == null || mMapLayerNames.Length != mapLayerCount)
                {
                    mMapLayerNames = new string[mapLayerCount];
                }
                for (int i = 0; i < mapLayerCount; ++i)
                {
                    mMapLayerNames[i] = map.GetMapLayerByIndex(i).name;
                }

#if false
                Handles.BeginGUI();
                EditorGUILayout.BeginHorizontal();
                EditorGUIUtility.labelWidth = 90;
                int selectedLayerIndex = GetSelectedLayerIndex();
                int newSelectedLayerIndex = EditorGUILayout.Popup("Map Layers", selectedLayerIndex, mMapLayerNames, GUILayout.MaxWidth(220));
                if (newSelectedLayerIndex != selectedLayerIndex)
                {
                    if (newSelectedLayerIndex >= 0)
                    {
                        SetSelectedLayer(mMapLayerNames[newSelectedLayerIndex]);
                    }
                }
                EditorGUIUtility.labelWidth = 0;
                EditorGUILayout.EndHorizontal();
                
                Handles.EndGUI();
#endif
            }
        }

        int GetSelectedLayerIndex()
        {
            var obj = Selection.activeGameObject;
            if (obj == null)
            {
                return -1;
            }

            var name = obj.name;
            var map = Map.currentMap;
            var layer = map.GetMapLayer(name);
            int index = map.GetMapLayerIndex(layer);
            return index;
        }

        void SetSelectedLayer(string name)
        {
            var layer = Map.currentMap.GetMapLayer(name);
            Selection.activeGameObject = layer.gameObject;
        }

        public PrefabIndicator prefabIndicator { get { return mPrefabIndicator; } }
        public RenderTextureObjectIndicator renderTextureObjectIndicator { get { return mRenderTextureObjectIndicator; } }
        public ColorTileIndicator colorTileIndicator { get { return mColorTileIndicator; } }
        public BrushIndicator brushIndicator { get { return mBrushIndicator; } }
        public EditorEventListener listener { get { return mListener; } }
        public string exportFolder
        {
            get
            {
                if (Map.currentMap != null)
                {
                    return Map.currentMap.dataFolder;
                }
                return "";
            }
            set
            {
                if (Map.currentMap != null)
                {
                    Map.currentMap.dataFolder = value;
                }
            }
        }
        public static SLGMakerEditor instance
        {
            get
            {
                if (mInstance == null)
                {
                    var obj = GameObject.Find("SLGMakerMapEditor");
                    if (obj != null)
                    {
                        mInstance = obj.GetComponent<SLGMakerEditor>();
                    }
                }
                return mInstance;
            }
        }

        public ViewportControl viewportControl { get { return mViewportControl; } }
        public UpdateMapConfig updateMapConfig { get { return mUpdateMapConfig; } }

        static SLGMakerEditor mInstance;
        public string projectFolder = "";
        public static float fov = -1;

        Rect mEditorViewRect;
        EditorEventListener mListener;
        PrefabIndicator mPrefabIndicator = new PrefabIndicator(true);
        RenderTextureObjectIndicator mRenderTextureObjectIndicator = new RenderTextureObjectIndicator();
        ColorTileIndicator mColorTileIndicator = new ColorTileIndicator();
        BrushIndicator mBrushIndicator = new BrushIndicator();
        ActionManager mActionManager = new ActionManager();
        ViewportControl mViewportControl;
        UpdateMapConfig mUpdateMapConfig = new UpdateMapConfig();
        string[] mMapLayerNames;
    }
}

#endif