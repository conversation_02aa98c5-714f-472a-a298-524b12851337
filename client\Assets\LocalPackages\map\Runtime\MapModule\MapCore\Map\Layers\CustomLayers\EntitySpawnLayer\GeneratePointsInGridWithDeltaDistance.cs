﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //在一个格子里生成点,点有最小间隔
    public class GeneratePointsInGridWithDeltaDistance
    {
        public List<Vector2> Generate(MapRegion region, float startX, float startZ, float deltaDistance, int pointCount, RandomWrapper r, System.Func<Vector3, bool> isValidPointCheck, bool generateAtleastOnePoint, List<Vector2> allCurrentTypePoints)
        {
            List<Vector2> spawnPoints = new List<Vector2>();

            var regionSize = region.size;

            int axisCount = Mathf.CeilToInt(Mathf.Sqrt(pointCount));
            float gridSize = regionSize.x / axisCount;

            int maxTryCount = 1000;
            for (int i = 0; i < axisCount; ++i)
            {
                for (int j = 0; j < axisCount; ++j)
                {
                    float x = r.Range(j * gridSize, (j + 1) * gridSize);
                    float z = r.Range(i * gridSize, (i + 1) * gridSize);

                    Vector2 pos = new Vector2(x, z) + region.startPosition;
                    int tryCount = 0;
                    while (!HasDeltaDistanceConflict(pos, allCurrentTypePoints, deltaDistance + deltaDistance) &&
                        !HasDeltaDistanceConflict(pos, spawnPoints, deltaDistance + deltaDistance)
                        && tryCount < maxTryCount)
                    {
                        ++tryCount;
                        var p3 = new Vector3(pos.x, 0, pos.y);
                        if (region.IsInEmptySpace(p3, deltaDistance))
                        {
                            bool isValid = true;
                            if (isValidPointCheck != null)
                            {
                                isValid = isValidPointCheck(p3);
                            }
                            if (isValid)
                            {
                                spawnPoints.Add(pos);
                                break;
                            }
                        }
                    }
                }
            }

            int removeCount = spawnPoints.Count - pointCount;
            if (removeCount >= 0) {
                Utils.Shuffle(spawnPoints);
                spawnPoints.RemoveRange(pointCount, removeCount);
            }

#if true
            if (pointCount == 1 && spawnPoints.Count == 0 && generateAtleastOnePoint)
            {
                //至少生成一个点,需要和当前类型的点不重叠,并且和更高优先级的点不重叠
                MapRegionPointGenerator gen = new MapRegionPointGenerator(new Rect(0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight));
                gen.Create(region.noneHolePolygons, region.holePolygons, 1, 0, 0, r, 0, (List<Vector2> generatedPoints, Vector3 pos) => {
                    if (HasDeltaDistanceConflict(pos, allCurrentTypePoints, deltaDistance + deltaDistance))
                    {
                        return false;
                    }
                    return isValidPointCheck(pos);
                }, 1000, null);
                spawnPoints.AddRange(gen.generatedPoints);
            }
#endif

            return spawnPoints;
        }

        bool HasDeltaDistanceConflict(Vector2 pos, List<Vector2> points, float deltaDistance)
        {
            int n = points.Count;
            float r = deltaDistance * deltaDistance;
            for (int i = 0; i < n; ++i)
            {
                if ((pos - points[i]).sqrMagnitude <= r)
                {
                    return true;
                }
            }
            return false;
        }
    }
}

#endif