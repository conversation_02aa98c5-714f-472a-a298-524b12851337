﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    /* 基于四叉树来管理地图对象的数据
     */
    public class QuadTreeObjectLayerData : MapObjectLayerData
    {
        public QuadTreeObjectLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ModelLODGroupManager groupManager, KeepScaleConfig scaleConfig) : base(header, config, map, groupManager)
        {
            mMiddleCheck = new CheckQuadTreeObjectVisibility(QuadTreeIntersectionTest.IntersectCondition);

            var layerWidth = GetLayerWidthInMeter(0);
            var layerHeight = GetLayerHeightInMeter(0);

            //temp code, replace 4
            mTree = new QuadTree<ModelData>(0, 0, layerWidth, layerHeight, 4);

            Vector2 offset;
            if (map.isEditorMode)
            {
                offset = new Vector2(200, 200);
            }
            else
            {
                offset = new Vector2(20, 20);
            }

            mViewport = new MapLayerViewport(new Rect(-10000, -10000, 1000, 1000), offset, Vector2.zero, new Vector2(10000, 10000));

            if (scaleConfig != null)
            {
                mScaleUpdater = new KeepScaleUpdater(new KeepScaleConfig[] { scaleConfig });
            }
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            if (mViewport != null)
            {
                mViewport.OnDestroy();
            }
        }

        protected override bool IsAbleToAdd(IMapObjectData objectData)
        {
            return true;
        }

        //add一个地图对象的数据
        protected override void OnAddObjectData(IMapObjectData data)
        {
            Debug.Assert(data is ModelData);
            mTree.AddObject(data as ModelData);
            var mapData = map.data;
            bool isVisible = false;
            if (!isLoading)
            {
                isVisible = IsInViewRange(data, map.viewport);
            }
            SetObjectActiveImpl(data, isVisible);

            SetObjectScale(data);
        }

        public void SetObjectScale(IMapObjectData data)
        {
            if (mScaleUpdater != null)
            {
                float scale = mScaleUpdater.currentScaleFactor;
                if (mScaleUpdater.currentScaleFactor == 0)
                {
                    scale = mScaleUpdater.UpdateObjectScaleAtHeight();
                }
                if (scale != 0)
                {
                    SetScale(data, scale);
                }
            }
        }

        //remove一个地图对象的数据
        protected override void OnRemoveObjectData(IMapObjectData data)
        {
            Debug.Assert(data is ModelData);
            mTree.RemoveObject(data as ModelData);
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(map.viewport, 1);
        }

        void UpdateObjectActiveState(Rect oldViewport, Rect newViewport)
        {
            mIntersectedObjectsInTwoStep.Clear();
            if (oldViewport.size.x != 0)
            {
                mTree.GetIntersection(oldViewport, mMiddleCheck, mIntersectedObjectsInTwoStep, 1);
            }

            mTree.GetIntersection(newViewport, mMiddleCheck, mIntersectedObjectsInTwoStep, 2);

            for (int i = 0; i < mIntersectedObjectsInTwoStep.Count; ++i)
            {
                var obj = mIntersectedObjectsInTwoStep[i];
                if (obj.visibilityValue == 1)
                {
                    SetObjectActive(obj, false, currentLOD);
                }
                else if (obj.visibilityValue == 2)
                {
                    if (!obj.isHidden)
                    {
                        SetObjectActive(obj, true, currentLOD);
                        SetObjectScale(obj);
                    }
                }
                obj.visibilityValue = 0;
            }
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            bool passOutline;
            bool viewportChanged = mViewport.Update(newViewport, out passOutline);
            if (mFirstUpdate || viewportChanged)
            {
                var oldViewport = mViewport.lastOuterViewport;
                var curViewport = mViewport.outerViewport;
                if (mFirstUpdate)
                {
                    mFirstUpdate = false;
                    oldViewport = new Rect(0, 0, 0, 0);
                }

                UpdateObjectActiveState(oldViewport, curViewport);
            }

            return lodChanged;
        }

        public void GetObjectsInViewport(List<ModelData> objects)
        {
            objects.Clear();
            var curViewport = mViewport.outerViewport;
            mTree.GetIntersection(curViewport, mMiddleCheck, objects, 2);
        }

        //对象是否在视野中
        public bool IsInViewRange(IMapObjectData data, Rect viewport)
        {
            var bounds = data.GetBounds();
            var dataMin = bounds.min;
            var dataMax = bounds.max;

            return !(dataMax.x < viewport.xMin || dataMax.y < viewport.yMin || viewport.xMax < dataMin.x || viewport.yMax < dataMin.y);
        }

        public override IMapObjectData FindObjectAtPosition(Vector3 pos, float radius)
        {
            mIntersectedObjectsInTwoStep.Clear();
            radius = 0.1f;
            var bounds = new Rect(pos.x - radius, pos.z - radius, radius * 2, radius * 2);
            mTree.GetIntersection(bounds, mMiddleCheck, mIntersectedObjectsInTwoStep, 1);
            for (int i = 0; i < mIntersectedObjectsInTwoStep.Count; ++i)
            {
                mIntersectedObjectsInTwoStep[i].visibilityValue = 0;
            }
            if (mIntersectedObjectsInTwoStep.Count > 0)
            {
                return mIntersectedObjectsInTwoStep[0];
            }
            return null;
        }

        public void UpdateScale()
        {
            if (mScaleUpdater != null)
            {
                float scale = mScaleUpdater.UpdateObjectScaleAtHeight();
                if (scale != 0)
                {
                    foreach (var p in mVisibleObjects)
                    {
                        SetScale(p.Value, scale);
                    }
                }
            }
        }

        public void SetScale(IMapObjectData data, float scale)
        {
            //temp code, only set z scale now
            data.SetScale(new Vector3(1, 1, scale));
            if (mOnObjectScaleChangeCallback != null)
            {
                mOnObjectScaleChangeCallback(data);
            }
        }

        protected override void OnPositionChange(IMapObjectData objectData)
        {
            UpdateQuadTreeIndex(objectData as ModelData);
        }

        protected override void OnScaleChange(IMapObjectData objectData)
        {
            UpdateQuadTreeIndex(objectData as ModelData);
        }

        protected override void OnRotationChange(IMapObjectData objectData)
        {
            UpdateQuadTreeIndex(objectData as ModelData);
        }

        public override void GetObjectInBounds(Bounds worldBounds, List<IMapObjectData> objects)
        {
            mIntersectedObjectsInTwoStep.Clear();
            mTree.GetIntersection(Utils.BoundsToRect(worldBounds), new CheckQuadTreeObjectVisibility(QuadTreeIntersectionTest.IntersectCondition), mIntersectedObjectsInTwoStep, 1);
            objects.AddRange(mIntersectedObjectsInTwoStep);
        }

        public override bool isGameLayer => true;

        void UpdateQuadTreeIndex(ModelData data)
        {
            bool isVisible = IsInViewRange(data, map.viewport);
            SetObjectActive(data, isVisible, currentLOD);
            mTree.RemoveObject(data);
            mTree.AddObject(data);
        }

        //管理地图层对象的四叉树
        QuadTree<ModelData> mTree;
        //无条件的判断地图对象可见性的函数
        CheckQuadTreeObjectVisibility mMiddleCheck;

        //temp variables,用于中间计算
        List<ModelData> mIntersectedObjectsInTwoStep = new List<ModelData>();

        //缩放物体
        KeepScaleUpdater mScaleUpdater;

        MapLayerViewport mViewport;
        float mLastZoom;
        bool mFirstUpdate = true;

        static GMMapObjectTransformChange mTransformChangeEvent = new GMMapObjectTransformChange();
    };
}