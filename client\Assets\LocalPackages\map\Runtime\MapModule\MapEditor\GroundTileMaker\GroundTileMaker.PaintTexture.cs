﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        ArrayPool<Color> mColorArrayPool = ArrayPool<Color>.Create();
        bool mIsPainting = false;

        public void StartRecording()
        {
            mIsPainting = true;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                var variation = mTiles[i].GetCurrentVariation();
                var tileLOD = variation.GetLOD(mCurrentLOD);
                var textures = tileLOD.maskTextures[maskTextureIndex];
                textures.textureData.CopyTo(textures.beforePaintingTextureData, 0);
                textures.paintDirtyRange.Clear();
            }
        }

        public void StopRecording()
        {
            mIsPainting = false;
            CompoundAction actions = null;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                var variation = mTiles[i].GetCurrentVariation();
                var tileLOD = variation.GetLOD(mCurrentLOD);
                var textures = tileLOD.maskTextures[maskTextureIndex];
                if (!textures.paintDirtyRange.IsEmpty())
                {
                    if (actions == null)
                    {
                        actions = new CompoundAction("Paint Ground Tile Texture");
                    }

                    var instanceID = mTiles[i].variations[mTiles[i].currentVariationIndex].instanceID;
                    var action = new ActionPaintGroundTileMask(this, mCurrentLOD, i, instanceID, maskTextureIndex, textures.beforePaintingTextureData, textures.textureData, textures.paintDirtyRange);
                    actions.Add(action);
                }
            }
            if (actions != null)
            {
                mActionManager.PushAction(actions, true, false);
            }
        }

        public void Paint(Vector3 mouseCursorWorldPos, bool subtract, int maskTextureIndex)
        {
            if (mInstances == null)
            {
                return;
            }
            int rows = mInstances.GetLength(0);
            int cols = mInstances.GetLength(1);

            int h = horizontalInstanceCount;
            int v = verticalInstanceCount;

            var maskTextureSetting = mLODMaterialSettings[mCurrentLOD].maskTextureSetting;
            int textureResolution = maskTextureSetting[mMaskTextureIndex].resolution;
            int mousePixelX = Mathf.FloorToInt(mouseCursorWorldPos.x / (mTileSize * h) * (textureResolution * h - 1));
            int mousePixelY = Mathf.FloorToInt(mouseCursorWorldPos.z / (mTileSize * v) * (textureResolution * v - 1));
            int paintAreaPixelMinX = mousePixelX - mBrushSize / 2;
            int paintAreaPixelMinY = mousePixelY - mBrushSize / 2;
            int paintAreaPixelMaxX = paintAreaPixelMinX + mBrushSize - 1;
            int paintAreaPixelMaxY = paintAreaPixelMinY + mBrushSize - 1;

            float sign = 1.0f;
            if (subtract)
            {
                sign = -1.0f;
            }
            Vector3 tileSize = new Vector3(mTileSize, 0, mTileSize);
            var brush = mBrushManager.GetActiveBrush();
            bool normalizeColor = maskTextureSetting[maskTextureIndex].normalizeColor;
            bool rotateBrush = useRotatedBrush;
            int mipmap = brush.GetMipmap(mBrushSize, rotateBrush);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var instance = mInstances[j, i];
                    if (instance != null)
                    {
                        int tilePixelMinX = textureResolution * j;
                        int tilePixelMinY = textureResolution * i;
                        int tilePixelMaxX = tilePixelMinX + textureResolution - 1;
                        int tilePixelMaxY = tilePixelMinY + textureResolution - 1;

                        int overlappedPixelMinX = Mathf.Max(paintAreaPixelMinX, tilePixelMinX);
                        int overlappedPixelMinY = Mathf.Max(paintAreaPixelMinY, tilePixelMinY);
                        int overlappedPixelMaxX = Mathf.Min(paintAreaPixelMaxX, tilePixelMaxX);
                        int overlappedPixelMaxY = Mathf.Min(paintAreaPixelMaxY, tilePixelMaxY);

                        if (overlappedPixelMinX <= overlappedPixelMaxX && overlappedPixelMinY <= overlappedPixelMaxY)
                        {
                            var template = mTiles[instance.tileIndex];
                            var variation = template.GetCurrentVariation();
                            var tileLOD = variation.GetLOD(mCurrentLOD);
                            if (template.writable)
                            {
                                var maskTexture = tileLOD.maskTextures[maskTextureIndex].texture;
                                var maskTextureData = tileLOD.maskTextures[maskTextureIndex].textureData;

                                int width = overlappedPixelMaxX - overlappedPixelMinX + 1;
                                int height = overlappedPixelMaxY - overlappedPixelMinY + 1;
                                int localPixelMinX = overlappedPixelMinX - tilePixelMinX;
                                int localPixelMinY = overlappedPixelMinY - tilePixelMinY;
                                Color[] pixels = mColorArrayPool.Rent(width * height);

                                for (int py = 0; py < height; ++py)
                                {
                                    for (int px = 0; px < width; ++px)
                                    {
                                        int dstIdx = py * width + px;
                                        float rx = (px + overlappedPixelMinX - paintAreaPixelMinX) / (float)mBrushSize;
                                        float ry = (py + overlappedPixelMinY - paintAreaPixelMinY) / (float)mBrushSize;
                                        Color brushVal = brush.SampleBilinear(mipmap, rx, ry, rotateBrush);
                                        Color val = brushVal * strength * sign;

                                        var srcIdx = (localPixelMinY + py) * textureResolution + localPixelMinX + px;
                                        Color oldVal = maskTextureData[srcIdx];
                                        if (normalizeColor)
                                        {
                                            if (mChannel == 4)
                                            {
                                                oldVal[0] = Mathf.Max(0, oldVal[0] + val[0]);
                                                oldVal[1] = Mathf.Max(1, oldVal[1] + val[1]);
                                                oldVal[2] = Mathf.Max(2, oldVal[2] + val[2]);
                                            }
                                            else if (mChannel == 5)
                                            {
                                                oldVal[0] = Mathf.Max(0, oldVal[0] + val[0]);
                                                oldVal[1] = Mathf.Max(1, oldVal[1] + val[1]);
                                                oldVal[2] = Mathf.Max(2, oldVal[2] + val[2]);
                                                oldVal[3] = Mathf.Max(2, oldVal[3] + val[3]);
                                            }
                                            else
                                            {
                                                oldVal[mChannel] = Mathf.Max(0, oldVal[mChannel] + val.a);
                                            }
                                            float d = Mathf.Sqrt(oldVal.r * oldVal.r + oldVal.g * oldVal.g + oldVal.b * oldVal.b + oldVal.a * oldVal.a);
                                            if (d > 0)
                                            {
                                                oldVal.r /= d;
                                                oldVal.g /= d;
                                                oldVal.b /= d;
                                                oldVal.a /= d;
                                            }
                                            pixels[dstIdx] = oldVal;
                                        }
                                        else
                                        {
                                            if (mChannel == 4)
                                            {
                                                oldVal[0] = Mathf.Clamp01(oldVal[0] + val[0]);
                                                oldVal[1] = Mathf.Clamp01(oldVal[1] + val[1]);
                                                oldVal[2] = Mathf.Clamp01(oldVal[2] + val[2]);
                                            }
                                            else if (mChannel == 5)
                                            {
                                                oldVal[0] = Mathf.Clamp01(oldVal[0] + val[0]);
                                                oldVal[1] = Mathf.Clamp01(oldVal[1] + val[1]);
                                                oldVal[2] = Mathf.Clamp01(oldVal[2] + val[2]);
                                                oldVal[3] = Mathf.Clamp01(oldVal[3] + val[3]);
                                            }
                                            else
                                            {
                                                oldVal[mChannel] = Mathf.Clamp01(oldVal[mChannel] + val.a);
                                            }
                                            pixels[dstIdx] = oldVal;

                                        }
                                    }
                                }

                                variation.SetPixels(mCurrentLOD, maskTextureIndex, localPixelMinX, localPixelMinY, width, height, pixels);
                                mColorArrayPool.Return(pixels);
                            }
                        }
                    }
                }
            }
        }

        bool GetOverlappedArea(Vector3 min0, Vector3 max0, Vector3 min1, Vector3 max1, out Vector3 worldOverlappedMin, out Vector3 worldOverlappedMax)
        {
            float minX = Mathf.Max(min0.x, min1.x);
            float minZ = Mathf.Max(min0.z, min1.z);
            float maxX = Mathf.Min(max0.x, max1.x);
            float maxZ = Mathf.Min(max0.z, max1.z);

            worldOverlappedMin = new Vector3(minX, 0, minZ);
            worldOverlappedMax = new Vector3(maxX, 0, maxZ);

            return minX <= maxX && minZ <= maxZ;
        }

        Vector2Int ConvertToPixelCoordinate(Vector3 worldPos, int textureResolution)
        {
            return new Vector2Int(
                Mathf.FloorToInt(worldPos.x / mTileSize * (textureResolution - 1)),
                Mathf.FloorToInt(worldPos.z / mTileSize * (textureResolution - 1)));
        }

        public void ResetTextureData(int maskTextureIndex)
        {
            var maskTextureSetting = mLODMaterialSettings[mCurrentLOD].maskTextureSetting;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    var tileLOD = variations[v].GetLOD(mCurrentLOD);
                    var maskTexture = tileLOD.maskTextures[maskTextureIndex].texture;
                    int resolution = maskTexture.width;
                    var pixels = new Color[resolution * resolution];
                    if (maskTextureSetting[maskTextureIndex].initChannelData)
                    {
                        for (int y = 0; y < resolution; ++y)
                        {
                            for (int x = 0; x < resolution; ++x)
                            {
                                int idx = y * resolution + x;
                                pixels[idx].r = 1.0f;
                            }
                        }
                    }
                    maskTexture.SetPixels(pixels);
                    maskTexture.Apply();
                    tileLOD.maskTextures[maskTextureIndex].dirty = true;
                    tileLOD.maskTextures[maskTextureIndex].textureData = pixels;
                    tileLOD.assetsDirty = true;
                }
            }

            mActionManager.Clear();
        }

        public void ResetChannelData(int maskTextureIndex, int channel)
        {
            var maskTextureSetting = mLODMaterialSettings[mCurrentLOD].maskTextureSetting;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    var tileLOD = variations[v].GetLOD(mCurrentLOD);
                    var maskTexture = tileLOD.maskTextures[maskTextureIndex].texture;
                    int resolution = maskTexture.width;
                    var pixels = maskTexture.GetPixels();
                    for (int y = 0; y < resolution; ++y)
                    {
                        for (int x = 0; x < resolution; ++x)
                        {
                            int idx = y * resolution + x;
                            pixels[idx][channel] = 0;
                        }
                    }

                    maskTexture.SetPixels(pixels);
                    maskTexture.Apply();
                    tileLOD.maskTextures[maskTextureIndex].dirty = true;
                    tileLOD.maskTextures[maskTextureIndex].textureData = pixels;
                    tileLOD.assetsDirty = true;
                }
            }

            mActionManager.Clear();
        }

        public void RefreshMaskTextures(bool forceRefresh)
        {
            for (int lod = 0; lod < mLODMaterialSettings.Count; ++lod)
            {
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    var variations = mTiles[i].variations;
                    for (int v = 0; v < variations.Count; ++v)
                    {
                        var tileLOD = variations[v].GetLOD(lod);
                        var maskTextures = tileLOD.maskTextures;
                        for (int t = 0; t < maskTextures.Count; ++t)
                        {
                            if (maskTextures[t].dirty || forceRefresh)
                            {
                                var texturePath = AssetDatabase.GetAssetPath(maskTextures[t].texture);
                                var bytes = maskTextures[t].texture.EncodeToTGA();
                                File.WriteAllBytes(texturePath, bytes);
                                maskTextures[t].dirty = false;
                            }
                        }
                    }
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        public void SetTextureData32(int lod, int tileIndex, int variationIndex, int maskTextureIndex, int x, int y, int width, int height, Color32[] textureData)
        {
            int resolution = mLODMaterialSettings[lod].maskTextureSetting[maskTextureIndex].resolution;
            var variation = mTiles[tileIndex].GetVariation(variationIndex);
            variation.SetPixels32(lod, maskTextureIndex, x, y, width, height, textureData);
        }

        public bool isPainting { get { return mIsPainting; } }
    }
}


#endif