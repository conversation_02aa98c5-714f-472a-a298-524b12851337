﻿ 



 
 



/*
 * created by wzw at 2020.6.30
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //四叉树,用来生成bvh
    public class BVHQuadTreeNode
    {
        public BVHQuadTreeNode(Bounds bounds, Bounds objectBounds, int depth)
        {
            this.quadBounds = bounds;
            this.objectsBounds = objectBounds;
            this.depth = depth;
        }

        public bool IsLeaf()
        {
            return children[0] == null &&
                children[1] == null &&
                children[2] == null &&
                children[3] == null;
        }

        public BVHQuadTreeNode[] children = new BVHQuadTreeNode[4];
        public List<int> childPrefabIndices = new List<int>();
        public Bounds quadBounds;
        public Bounds objectsBounds;
        public int depth;
    };

    //生成一个tile model template的bvh quad tree
    public class BVHQuadTree
    {
        public BVHQuadTree(ModelTemplate modelTemplate, int lod, int maxDepth)
        {
            Debug.Assert(modelTemplate.isTileModelTemplate);
            mModelTemplate = modelTemplate;

            AddObjects(lod, maxDepth);
        }

        void AddObjects(int lod, int maxDepth)
        {
            Debug.Assert(mRoot == null);
            var bounds = Utils.RectToBounds(mModelTemplate.bounds);
            //temp code
            Vector3 offset = new Vector3(Map.currentMap.frontTileSize * 0.5f, 0, Map.currentMap.frontTileSize * 0.5f);
            bounds.SetMinMax(bounds.min + offset, bounds.max + offset);
            mRoot = new BVHQuadTreeNode(bounds, bounds, 0);
            var childPrefabs = mModelTemplate.GetChildPrefabTransform(lod);
            for (int i = 0; i < childPrefabs.Count; ++i)
            {
                AddObject(mRoot, childPrefabs[i], i, maxDepth);
            }
        }

        void AddObject(BVHQuadTreeNode node, ChildPrefabTransform childPrefab, int childPrefabIndex, int maxDepth)
        {
            var childPrefabBounds = Utils.RectToBounds(childPrefab.localBoundsInPrefab);
            node.objectsBounds.Encapsulate(childPrefabBounds);
            if (node.depth < maxDepth)
            {
                var pos = childPrefab.position;
                Bounds quadBounds;
                int quadIndex = GetQuadIndex(node, pos, out quadBounds);
                if (node.children[quadIndex] == null)
                {
                    node.children[quadIndex] = new BVHQuadTreeNode(quadBounds, childPrefabBounds, node.depth + 1);
                }
                AddObject(node.children[quadIndex], childPrefab, childPrefabIndex, maxDepth);
            }
            else
            {
                node.childPrefabIndices.Add(childPrefabIndex);
            }
        }

        int GetQuadIndex(BVHQuadTreeNode node, Vector3 pos, out Bounds quadBounds)
        {
            var bounds = node.quadBounds;
            var center = bounds.center;
            var min = bounds.min;
            var max = bounds.max;

            if (pos.x < center.x && pos.z < center.z)
            {
                quadBounds = new Bounds();
                quadBounds.SetMinMax(min, center);
                return 0;
            }

            if (pos.x >= center.x && pos.z < center.z)
            {
                quadBounds = new Bounds();
                quadBounds.SetMinMax(new Vector3(center.x, 0, min.z), new Vector3(max.x, 0, center.z));
                return 1;
            }

            if (pos.x < center.x && pos.z >= center.z)
            {
                quadBounds = new Bounds();
                quadBounds.SetMinMax(new Vector3(min.x, 0, center.z), new Vector3(center.x, 0, max.z));
                return 2;
            }

            if (pos.x >= center.x && pos.z >= center.z)
            {
                quadBounds = new Bounds();
                quadBounds.SetMinMax(center, max);
                return 3;
            }

            quadBounds = new Bounds();
            Debug.Assert(false);
            return -1;
        }

        public BVHQuadTreeNode rootNode { get { return mRoot; } }
        public ModelTemplate modelTemplate { get { return mModelTemplate; } }

        ModelTemplate mModelTemplate;
        BVHQuadTreeNode mRoot;
    };
}