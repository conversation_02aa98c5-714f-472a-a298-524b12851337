﻿ 



 
 



/*
 * created by wzw at 2019.12.13
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ObjectTransformInfo
    {
        public int objectID;
        public Vector3 position;
        public Vector3 scale;
        public Quaternion rotation;
    }

    //游戏运行时使用
    public sealed class ComplexGridObjectLayerView : MapLayerView
    {
        public ComplexGridObjectLayerView(MapLayerData layerData, bool asyncLoading, Vector3 localViewportCenter)
            : base(layerData, asyncLoading)
        {
            mObjectPool = Map.currentMap.view.reusableGameObjectPool;

            var viewportObj = new GameObject(MapCoreDef.DECORATION_LAYER_VIEWPORT_NAME);
            viewportObj.transform.SetParent(root.transform, true);
            viewportObj.transform.position = localViewportCenter;
            mViewportControl = viewportObj.AddComponent<ComplexGridObjectLayerViewportControl>();
            mViewportControl.Init((layerData as ComplexGridObjectLayerData).layer);
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var p in mTileViews)
            {
                Utils.DestroyObject(p.Value);
            }
            mTileViews = null;

            if (mViewportControl != null)
            {
                GameObject.DestroyImmediate(mViewportControl.gameObject);
            }
        }

        public void OnObjectActiveStateChange(IMapObjectData data, int lod)
        {
            if (data.IsObjActive())
            {
                ShowObject(data, lod);
            }
            else
            {
                HideObject(data, lod);
            }
        }

        public void OnAddObject(IMapObjectData data, int lod)
        {
            if (data.IsObjActive())
            {
                ShowObject(data, lod);
            }
        }

        public void OnRemoveObject(int dataID)
        {
            var data = Map.currentMap.FindObject(dataID) as ComplexGridModelData;
            HideObject(data, data.lod);
        }

        public void OnObjectScaleChange(IMapObjectData data)
        {
            mTileViews.TryGetValue(data.GetEntityID(), out GameObject obj);
            obj.transform.localScale = data.GetScale();
        }

        //显示地图对象的模型
        void ShowObject(IMapObjectData data, int lod)
        {
#if UNITY_EDITOR
            Debug.Assert(data is ComplexGridModelData);
#endif
            var complexData = data as ComplexGridModelData;

            var path = data.GetAssetPath();
            GameObject obj = null;
            var mapView = Map.currentMap.view;
            if (complexData.useRenderTextureModel)
            {
                obj = mapView.modelTemplateRenderToTextureGameObjectManager.Require(data.GetModelTemplateID(), data.GetPosition(), data.GetScale(), data.GetRotation());
            }
            else
            {
                obj = mObjectPool.Require(path);
            }
            
            mTileViews[data.GetEntityID()] = obj;

            obj.SetActive(true);
            var transform = obj.transform;
            transform.localPosition = data.GetPosition();
            transform.localRotation = MapObjectUtils.NeedRotation ? data.GetRotation() * Quaternion.Euler(0, 180f, 0) : data.GetRotation();
            transform.localScale = data.GetScale();
#if UNITY_EDITOR
            //选择物体时选到子节点的root
            var behaviour = obj.GetComponent<ComplexGridObjectBehaviour>();
            if (behaviour == null)
            {
                behaviour = obj.AddComponent<ComplexGridObjectBehaviour>();
                //MakeChildrenGameObjectStatic(obj);
            }
            behaviour.Init(data.GetEntityID());
            transform.SetParent(root.transform);
#endif
        }

        //让子节点不能移动,因为这部分不属于编辑器控制的内容
        void MakeChildrenGameObjectStatic(GameObject obj)
        {
            int n = obj.transform.childCount;
            for (int i = 0; i < n; i++)
            {
                var childObj = obj.transform.GetChild(i).gameObject;
                childObj.AddComponent<StaticLocalObject>();
            }
        }

        //隐藏地图对象的模型
        void HideObject(IMapObjectData data, int lod)
        {
#if UNITY_EDITOR
            Debug.Assert(data is ComplexGridModelData);
#endif
            var complexData = data as ComplexGridModelData;

            var id = data.GetEntityID();
            GameObject obj = null;
            mTileViews.TryGetValue(id, out obj);
            mTileViews.Remove(id);

            var mapView = Map.currentMap.view;
            if (complexData.useRenderTextureModel)
            {
                mapView.modelTemplateRenderToTextureGameObjectManager.Release(data.GetModelTemplateID(), obj);
            }
            else
            {
                mObjectPool.Release(data.GetAssetPath(), obj, Map.currentMap);
            }
        }

        public GameObject GetGameObject(int objectID)
        {
            GameObject obj;
            mTileViews.TryGetValue(objectID, out obj);
            return obj;
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        //计算transform改变后的entity id
        public List<ObjectTransformInfo> GetTransformChangedObjectIDs()
        {
            List<ObjectTransformInfo> changedObjects = new List<ObjectTransformInfo>();
#if UNITY_EDITOR
            foreach (var view in mTileViews)
            {
                var selectBase = view.Value.GetComponent<ComplexGridObjectBehaviour>();
                if (selectBase != null)
                {
                    bool isTransformChanged = selectBase.IsTransformChanged();
                    if (isTransformChanged)
                    {
                        ObjectTransformInfo info = new ObjectTransformInfo();
                        var transform = view.Value.transform;
                        info.objectID = view.Key;
                        info.position = transform.position;
                        info.rotation = transform.rotation;
                        info.scale = transform.localScale;

                        changedObjects.Add(info);
                        selectBase.Reset();
                    }
                }
            }
#endif
            return changedObjects;
        }

        public void MoveLocalViewport(Vector3 offset)
        {
            mViewportControl.gameObject.transform.position += offset;
        }

        public void ScaleLocalViewport(float deltaScale)
        {
            mViewportControl.gameObject.transform.localScale += Vector3.one * deltaScale;
        }

        public List<SpecialAreaConfig> GetSpecialAreaConfigs()
        {
            List<SpecialAreaConfig> allConfigs = new List<SpecialAreaConfig>();

            var complexLayerData = layerData as ComplexGridObjectLayerData;
            List<int> renderTextureObjectIDs = new List<int>();
            foreach (var p in mTileViews) 
            {
                var objData = Map.currentMap.FindObject(p.Key) as ComplexGridModelData;
                if (objData.useRenderTextureModel)
                {
                    renderTextureObjectIDs.Add(p.Key);
                }
            }

            foreach (var id in renderTextureObjectIDs)
            {
                //如果是贴图模式,先转换成3d模式再还原
                complexLayerData.ChangeObjectModel(id, false);
            }

            //获取component
            foreach (var p in mTileViews)
            {
                var configs = p.Value.GetComponentsInChildren<SpecialAreaConfig>();
                allConfigs.AddRange(configs);
            }

            //还原render texture
            foreach (var id in renderTextureObjectIDs)
            {
                complexLayerData.ChangeObjectModel(id, true);
            }

            return allConfigs;
        }

        //该层中所有地图对象的视图
        Dictionary<int, GameObject> mTileViews = new Dictionary<int, GameObject>();
        GameObjectPool mObjectPool;
        ComplexGridObjectLayerViewportControl mViewportControl;
    };
}
