﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateIslandNavigationMesh(PrefabOutlineType type, Vector3 min, Vector3 max, List<IObstacle> obstacles, float agentRadius, float minimumAngle, float maximumArea, float scaleFactor, bool useDelaunay, bool oceanAreaEnabled, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            List<ushort> triangleTypeList;
            List<bool> triangleStateList;
            //只创建海岛的navmesh
            CreateSpecialRegionNavMesh(type, agentRadius, false, out meshVertices, out meshIndices, out triangleTypeList, out triangleStateList);
            triangleTypes = triangleTypeList.ToArray();
            triangleStates = triangleStateList.ToArray();

            if (triangleTypeList.Count == 0)
            {
                CreateNavMesh(type, min, max, obstacles, agentRadius, minimumAngle, maximumArea, useDelaunay, NavigationCreateMode.CreateLandNavigationMesh, oceanAreaEnabled, scaleFactor, removeSameHoles, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
        }
    }
}


#endif