﻿
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using maxsdk;

namespace maxsdkHWExt
{
    // 密封类无法被继承，只能通过扩展的方式
    // 使用时， using maxsdkHWExt; 然后 MaxSDK.GetInstance().ExtMethod
    public static class MaxSDKHWExt
    {

        /// <summary>
        /// 获取初始化结果
        ///     iOS及Android的初始化在Native层处理，且时机早于Unity初始化
        ///     当前接口会将初始化结果通过始化OnInitSuccess、OnInitFail回调返回
        /// </summary>
        /// <param name="obj"></param>
        public static void GetInitResult(this MaxSDK obj)
        {
            MaxSDKInitInfo info = new();
            obj.Init(info);
        }


        /// <summary>
        /// 自动登录
        /// </summary>
        /// <param name="obj"></param>
        public static void AutoLogin(this MaxSDK obj)
        {
            MaxSDKLoginInfo info = new();
            obj.Login(info);
        }


        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="obj"></param>
        public static void Logout(this MaxSDK obj)
        {
            MaxSDKLogoutInfo info = new();
            obj.Logout(info);
        }


        /// <summary>
        /// 设置用户中心内账号切换回调
        /// </summary>
        /// <param name="obj"></param>
        public static void SetChangeAccountCallback(this MaxSDK obj)
        {
            MaxSDKSwitchAccountInfo info = new();
            obj.SwitchAccount(info);
        }


        /// <summary>
        /// 海外内购方法
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="payInfo"></param>
        public static void HWPay(this MaxSDK obj, MaxSDKHWPayInfo payInfo)
        {
            // 将海外模型封装成通用模型
            string localPurchase = payInfo.localPurchase ? "1" : "0";
            string purchaseType = payInfo.purchaseType == HWIAPType.InAppSubscription ? "2" : "1";
            string productDesc = payInfo.productDesc ?? "";
            string cpProductId = payInfo.cpProductId ?? "";
            string tagCurrency = payInfo.tagCurrency ?? "";
            string tagMoney = payInfo.tagMoney ?? "";
            string remark = payInfo.remark ?? "";
            MaxSDKPayInfo info = new()
            {
                productId = payInfo.productId ?? "", 
                serverId = payInfo.serverId ?? "", 
                roleId = payInfo.roleId ?? "",
                roleName = payInfo.roleName ?? "", 
                roleLevel = payInfo.roleLevel > 0 ? payInfo.roleLevel : 0, 
                cpOrderId = payInfo.cpOrderId ?? "",


                extData = "{\"localPurchase\":\"" + localPurchase
                + "\",\"purchaseType\":\"" + purchaseType
                + "\",\"productDesc\":\"" + productDesc
                + "\",\"cpProductId\":\"" + cpProductId
                + "\",\"tagCurrency\":\"" + tagCurrency
                + "\",\"tagMoney\":\"" + tagMoney
                + "\",\"remark\":\"" + remark + "\"}",
               
            };

            obj.Pay(info);
        }


        /// <summary>
        /// 上报创角信息
        /// </summary>
        /// <param name="obj"></param>
        public static void ReportCreateRole(this MaxSDK obj, MaxSDKHWRoleInfo roleInfo)
        {
            roleInfo.dataType = MaxSDKRoleInfo.TYPE_ROLE_INFO_CREATE_ROLE;
            obj.ReportRoleInfo(roleInfo);
        }


        /// <summary>
        /// 上报角色入服
        /// </summary>
        /// <param name="obj"></param>
        public static void ReportEnterServer(this MaxSDK obj, MaxSDKHWRoleInfo roleInfo)
        {
            roleInfo.dataType = MaxSDKRoleInfo.TYPE_ROLE_INFO_ENTER_GAME;
            // 包装扩展数据
            string roleDiamonds = roleInfo.roleDiamonds.ToString() ?? "0";
            string castleLevel = roleInfo.castleLevel.ToString() ?? "0";
            roleInfo.extData = "{\"roleDiamonds\":\"" + roleDiamonds
                + "\",\"castleLevel\":\"" + castleLevel + "\"}";
            obj.ReportRoleInfo(roleInfo);
        }


        /// <summary>
        /// 海外游戏事件打点
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="eventInfo"> 事件信息 </param>
        public static void HWReportEvent(this MaxSDK obj, MaxSDKHWReportEventInfo eventInfo)
        {
            string eventType = eventInfo.eventType ?? "";
            string eventName = eventInfo.eventName ?? "";
            string eventValue = eventInfo.eventValue ?? "";
            // 封装成通用模型
            MaxSDKReportEventInfo info = new()
            {
                eventData = "{\"eventType\":\"" + eventType
                + "\",\"eventName\":\"" + eventName
                + "\",\"eventValue\":\"" + eventValue + "\"}",

            };
            obj.ReportEvent(info);
        }


        /// <summary>
        /// 海外分享
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="shareInfo"> 分享信息 </param>
        public static void HWShare(this MaxSDK obj, MaxSDKHWShareInfo shareInfo)
        {
            if (shareInfo.isSimplyShare)
            {
                shareInfo.extData = "{\"isSimplyShare\":\"" + "1" + "\"}";
            } else
            {
                string achievementId = shareInfo.achievementId ?? "";
                string serverId = shareInfo.serverId ?? "";
                string roleId = shareInfo.roleId ?? "";
                string roleName = shareInfo.roleName ?? "";
                shareInfo.extData = "{\"isSimplyShare\":\"" + "0"
                    + "\",\"achievementId\":\"" + achievementId
                    + "\",\"serverId\":\"" + serverId
                    + "\",\"roleId\":\"" + roleId
                    + "\",\"roleName\":\"" + roleName + "\"}";
            }
            obj.Share(shareInfo);
        }


        /// <summary>
        /// 退出游戏
        /// </summary>
        public static void ExitGame(this MaxSDK obj)
        {
            obj.ExitGame(new MaxSDKExitInfo());
        }

        /// <summary>
        /// 打开链接
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="url"> 链接 </param>
        /// <param name="openInBrowser"> 是否外跳浏览器打开 </param>
        public static void OpenUrl(this MaxSDK obj, string url, bool openInBrowser)
        {
            string flag = openInBrowser == true ? "1" : "0";
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_URL,
                data = "{\"url\":\"" + url + "\",\"openInBrowser\":\"" + flag + "\"}" 
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开用户中心
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenUserCenter(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_USER_CENTER
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开客服工单
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenFAQ(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_FAQ_VIEW
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开离线FAQ
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenOfflineFAQ(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_LOCAL_FAQ_VIEW
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开Naver社交中心
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenNaverLounge(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_NAVER_SDK_MAIN_VIEW
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开游戏助手
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenGameHelper(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_GAME_HELPER
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开Facebook粉丝页面
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="pageId"> 页面id </param>
        /// <param name="pageName"> 页面名称</param>
        public static void OpenFacebookPage(this MaxSDK obj, string pageId, string pageName)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_FACEBOOK_PAGE,
                data = "{\"pageId\":\"" + pageId + "\",\"pageName\":\"" + pageName + "\"}"
            };
            obj.OpenActionExt(actionInfo);
        }


        /// <summary>
        /// 打开登录界面
        /// </summary>
        /// <param name="obj"></param>
        public static void OpenLoginView(this MaxSDK obj)
        {
            MaxSDKActionInfo actionInfo = new()
            {
                type = MaxSDKActionType.TYPE_OPEN_LOGIN_VIEW
            };
            obj.OpenActionExt(actionInfo);
        }


        /* ------------------------- Dispatch同步方法 ------------------------- */

        /// <summary>
        /// 获取用户登录状态
        /// </summary>
        /// <param name="obj"></param>
        /// <returns> 是否已登录 </returns>
        public static bool SdkHasLogin(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SDK_HAS_LOGIN,
            };
            string sdkHasLogin = obj.DispatchSync(info);
            if (sdkHasLogin == "1")
            {
                return true;
            }
            return false;
        }


        /// <summary>
        ///  同步获取广告渠道邀请安装信息 - 【休闲益智游戏生效】
        /// </summary>
        /// <param name="obj"></param>
        /// <returns> 广告渠道邀请安装信息,json格式 </returns>
        public static string GetAdChannelData(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.GET_AD_CHANNEL_DATA,
            };
            string dataJson = obj.DispatchSync(info);
            return dataJson;
        }


        /* ------------------------- Dispatch异步方法 ------------------------- */

        /// <summary>
        /// 获取内购商品信息
        /// </summary>
        /// <param name="purchaseType"> 内购类型(Android端必传，iOS可忽略) </param>
        /// <param name="productIDs"> 商品ID </param>>
        public static void GetInAppSkuDetail(this MaxSDK obj, HWIAPType purchaseType, List<string> productIDs)
        {
            string ids = string.Join(",", productIDs);
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.GET_IN_APP_SKU_DETAIL,
                extData = "{\"purchaseType\":\"" + (int)purchaseType + "\",\"productIDs\":\"" + ids + "\"}"
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 设置账号绑定回调
        /// </summary>
        /// <param name="obj"></param>
        public static void SetBindAccountCallback(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SET_ACCOUNT_BIND_CALLBACK,
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 获取Facebook游戏内好友信息
        /// </summary>
        /// <param name="obj"></param>
        public static void GetFacebookInGameFriendInfo(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.GET_FB_IN_GAME_FRIEND_INFO,
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 邀请Facebook好友进入游戏
        /// </summary>
        /// <param name="obj"></param>
        public static void InviteFacebookFriends(this MaxSDK obj, string title, string message)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.INVITE_FB_FRIEND,
                extData = "{\"title\":\"" + title + "\",\"message\":\"" + message + "\"}"
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 设置获取广告渠道邀请安装信息回调
        /// </summary>
        /// <param name="obj"></param>
        public static void SetGetAdChannelDataCallback(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SET_AD_CHANNEL_DATA_CALLBACK,
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 设置SDK语言
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="languageType"> 语言类型 </param>
        public static void SetSDKLanguage(this MaxSDK obj, HWSDKLanguageType languageType)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SET_LOCAL_LANGUAGE,
                extData = "{\"languageType\":\"" + (int)languageType + "\"}"

            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 获取游戏内按钮对接信息
        /// </summary>
        /// <param name="obj"></param>
        public static void SetInGameButtonInfoUpdateCallback(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SET_GET_BUTTON_LIST_CALLBACK,
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 获取Firebase推送Token
        /// </summary>
        /// <param name="obj"></param>
        public static void GetFirebaseToken(this MaxSDK obj)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.GET_FIREBASE_TOKEN,
            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 游客账户绑定第三方平台
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="userType"> 要绑定的第三方平台类型 </param>
        public static void BindThirdPlatform(this MaxSDK obj, HWUserType userType)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.BIND_PLATFORM,
                extData = "{\"userType\":\"" + (int)userType + "\"}"

            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 通过第三方关联登录
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="userType"> 要登录的第三方平台类型 </param>
        public static void LoginViaThirdPlatform(this MaxSDK obj, HWUserType userType)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.LOGIN_PLATFORM,
                extData = "{\"userType\":\"" + (int)userType + "\"}"

            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// 游戏内切换icon  - 【仅iOS生效】
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="iconName"> 要切换的icon名称 </param>
        public static void ChangeAppIcon(this MaxSDK obj, string iconName)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.SET_ALTERNATE_ICON,
                extData = "{\"iconName\":\"" + iconName + "\"}"

            };
            obj.DispatchASync(info);
        }


        /// <summary>
        /// Facebook登录  - 【休闲益智游戏生效】
        /// </summary>
        /// <param name="obj"></param>
        public static void LoginViaFacebook(this MaxSDK obj)
        {
            LoginViaThirdPlatform(obj, HWUserType.Facebook);
        }


        /// <summary>
        /// Apple登录  - 【休闲益智游戏生效】【仅iOS生效】
        /// </summary>
        /// <param name="obj"></param>
        public static void LoginViaApple(this MaxSDK obj)
        {
            LoginViaThirdPlatform(obj, HWUserType.Apple);
        }


        /// <summary>
        /// Google登录  - 【休闲益智游戏生效】【仅Android生效】
        /// </summary>
        /// <param name="obj"></param>
        public static void LoginViaGoogle(this MaxSDK obj)
        {
            LoginViaThirdPlatform(obj, HWUserType.Google);
        }


        /// <summary>
        /// 按用户类型登出  - 【休闲益智游戏生效】
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="userType">
        ///     要登出的用户类型
        ///     iOS仅支持传Facebook和Apple
        ///     Android仅支持Facebook和Google
        /// </param>
        public static void LogoutWithUserType(this MaxSDK obj, HWUserType userType)
        {
            MaxSDKDispatchInfo info = new()
            {
                apiName = HWDispatchApi.LOGOUT_WITH_TYPE,
                extData = "{\"userType\":\"" + (int)userType + "\"}"

            };
            obj.DispatchASync(info);
        }

    }




    /// <summary>
    /// 海外回调监听器，继承自通用回调监听器，并添加海外回调方法
    /// </summary>
    public abstract class MaxSDKHWListener : MaxSDKListener
    {

        // OpenActionExt成功回调
        public override void OnActionSuccess(MaxSDKActionBean actionBean)
        {
            ShowMessage("[MaxSDK HWExt] Unity收到原生回调：调用OpenActionExt成功", actionBean.extData);
        }

        // OpenActionExt失败回调
        public override void OnActionFail(MaxSDKActionFailBean bean)
        {
            ShowMessage("[MaxSDK HWExt] Unity收到原生回调：调用OpenActionExt失败", ObjectToJson(bean));
        }


        // DispatchAsync统一回调
        public override void OnDispatchResult(MaxSDKDispatchBean dispatchBean)
        {
            //throw new NotImplementedException();

            ShowMessage("[MaxSDK HWExt] Unity收到原生回调：disapatchAsync接口回调", ObjectToJson(dispatchBean));

            /**
             * 获取内购商品信息回调
             */
            if (dispatchBean.apiName.Equals(HWDispatchApi.GET_IN_APP_SKU_DETAIL))
            {   
                if (dispatchBean.statusCode == 1) // 成功，returnData返回商品信息
                {
                    //将回调数据转换成MaxSDKHWSkuDetailBean模型数组
                    JObject jo = (JObject)JsonConvert.DeserializeObject(dispatchBean.returnData);
                    Dictionary<string, object> joDict = jo.ToObject<Dictionary<string, object>>();
                    Dictionary<string, MaxSDKHWSkuDetailBean> skuInfoDict = new();
                    foreach (var item in joDict)
                    {
                        string productId = item.Key.ToString();
                        string productSku = item.Value.ToString();
                        MaxSDKHWSkuDetailBean skuDetailBean = JsonConvert.DeserializeObject<MaxSDKHWSkuDetailBean>(productSku);
                        skuInfoDict.Add(productId, skuDetailBean);
                    }
                    OnGetInAppSkuDetailSuccess(skuInfoDict);
                }
                else // 失败
                {   
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnGetInAppSkuDetailFail(failBean);
                }
            }

            /**
             * 用户中心内账号绑定回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.SET_ACCOUNT_BIND_CALLBACK))
            {  
                if (dispatchBean.statusCode == 1) // 成功
                {   
                    ShowMessage("[MaxSDK HWExt] 用户中心内账号绑定成功：", dispatchBean.returnData);
                    // TODO 封装bean
                    OnBindAccountSuccess(dispatchBean.returnData);
                }
                else // 失败
                {
                    // 绑定失败不回调
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnBindAccountFail(failBean);
                }
            }

            /**
             * 获取FB游戏内好友信息回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.GET_FB_IN_GAME_FRIEND_INFO))
            {
                if (dispatchBean.statusCode == 1) //成功，返回好友信息
                {
                    ShowMessage("[MaxSDK HWExt] 获取fb游戏内好友信息成功：", dispatchBean.returnData);
                    // 解析Facebook好友信息
                    OnGetFBInGameFriendsSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnGetFBInGameFriendsFail(failBean);
                }
            }

            /**
             * 邀请FB好友进入游戏回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.INVITE_FB_FRIEND))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    OnInviteFBFriendsSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnInviteFBFriendsFail(failBean);
                }
            }

            /**
             * 获取用户首次安装受邀信息回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.SET_AD_CHANNEL_DATA_CALLBACK))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    OnGetAdChannelDataSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnGetAdChannelDataFail(failBean);
                }
            }

            /**
             * SDK语言设置回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.SET_LOCAL_LANGUAGE))
            {
                OnSetSDKLanguageComplete(dispatchBean.returnData);
            }

            /**
             * 游戏内按钮对接信息更新回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.SET_GET_BUTTON_LIST_CALLBACK))
            {
                if (dispatchBean.statusCode == 1) //成功，返回游戏内按钮信息
                {
                    JObject jo = JsonConvert.DeserializeObject<JObject>(dispatchBean.returnData);
                    Dictionary<string, object> joDict = jo.ToObject<Dictionary<string, object>>();
                    
                    if (joDict.ContainsKey("list"))
                    {
                        string listStr = joDict["list"].ToString();
                        List<MaxSDKHWInGameButtonBean> gameButtonBeans = JsonConvert.DeserializeObject<List<MaxSDKHWInGameButtonBean>>(listStr);
                        OnInGameButtonInfoUpdate(gameButtonBeans);
                    }

                }
                else //失败
                {
                    // 获取失败不回调
                    //MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    //OnGameButtonsUpdateFail(failBean);
                }
            }

            /**
             * 游客账户绑定第三方平台回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.BIND_PLATFORM))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    ShowMessage("[MaxSDK HWExt] 游客账户绑定第三方平台成功：", dispatchBean.returnData);
                    //TODO 封装bean
                    //MaxSDKLoginBean userInfo = JsonConvert.DeserializeObject<MaxSDKLoginBean>(dispatchBean.returnData);
                    OnBindPlatformSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnBindPlatformFail(failBean);
                }
            }

            /**
             * 第三方登录回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.LOGIN_PLATFORM))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    ShowMessage("[MaxSDK HWExt] 第三方登录成功：", dispatchBean.returnData);
                    OnLoginPlatformSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnLoginPlatformFail(failBean);
                }
            }

            /**
             * 获取Firebase推送Token回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.GET_FIREBASE_TOKEN))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    ShowMessage("[MaxSDK HWExt] 获取Firebase推送Token成功：", dispatchBean.returnData);
                    OnGetFCMTokenSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnGetFCMTokenFail(failBean);
                }
            }

            /**
             * 游戏内切换icon回调
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.SET_ALTERNATE_ICON))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    ShowMessage("[MaxSDK HWExt] 游戏内切换icon成功：", dispatchBean.returnData);
                    OnChangeAppIconSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnChangeAppIconFail(failBean);
                }
            }

            /**
             * 带用户类型的登出接口回调 - 【休闲益智游戏生效】
             */
            else if (dispatchBean.apiName.Equals(HWDispatchApi.LOGOUT_WITH_TYPE))
            {
                if (dispatchBean.statusCode == 1) //成功
                {
                    ShowMessage("[MaxSDK HWExt] 用户登出成功：", dispatchBean.returnData);
                    OnLogoutWithTypeSuccess(dispatchBean.returnData);
                }
                else //失败
                {
                    MaxSDKFailBean failBean = JsonConvert.DeserializeObject<MaxSDKFailBean>(dispatchBean.returnData);
                    OnLogoutWithTypeFail(failBean);
                }
            }


        }


        // 海外扩展回调
        public abstract void OnGetInAppSkuDetailSuccess(Dictionary<string, MaxSDKHWSkuDetailBean> skuInfoDict);
        public abstract void OnGetInAppSkuDetailFail(MaxSDKFailBean bean);

        public abstract void OnBindAccountSuccess(string infoJson);
        public abstract void OnBindAccountFail(MaxSDKFailBean bean); // Inactive

        public abstract void OnGetFBInGameFriendsSuccess(string infoJson);
        public abstract void OnGetFBInGameFriendsFail(MaxSDKFailBean bean);

        public abstract void OnInviteFBFriendsSuccess(string infoJson);
        public abstract void OnInviteFBFriendsFail(MaxSDKFailBean bean);

        public abstract void OnGetAdChannelDataSuccess(string infoJson);
        public abstract void OnGetAdChannelDataFail(MaxSDKFailBean bean);

        public abstract void OnSetSDKLanguageComplete(string infoJson);

        public abstract void OnInGameButtonInfoUpdate(List<MaxSDKHWInGameButtonBean> gameButtonBeans);

        public abstract void OnBindPlatformSuccess(string infoJson);
        public abstract void OnBindPlatformFail(MaxSDKFailBean bean);

        public abstract void OnLoginPlatformSuccess(string infoJson);
        public abstract void OnLoginPlatformFail(MaxSDKFailBean bean);

        public abstract void OnGetFCMTokenSuccess(string infoJson);
        public abstract void OnGetFCMTokenFail(MaxSDKFailBean bean);

        public abstract void OnChangeAppIconSuccess(string infoJson);
        public abstract void OnChangeAppIconFail(MaxSDKFailBean bean);

        public abstract void OnLogoutWithTypeSuccess(string infoJson);
        public abstract void OnLogoutWithTypeFail(MaxSDKFailBean bean);

        /**
         * 显示调用信息
         */
        public abstract void ShowMessage(string message, string json);


        /**
         * 显示调用信息
         */
        public string ObjectToJson(object obj)
        {
            if (obj == null)
            {
                return "";
            }
            string json = JsonUtility.ToJson(obj, true);
            if (json == null || "".Equals(json))
            {
                return "";
            }
            return "\n" + json;
        }

    }




    /// <summary>
    /// 海外分享信息模型
    /// </summary>
    public class MaxSDKHWShareInfo : MaxSDKShareInfo
    {
        public bool isSimplyShare;      // 是否为简单模式，简单分享时，以下参数不生效
        public string achievementId;    // 分享关联的成就奖励ID
        public string serverId;
        public string roleId;
        public string roleName;
    }


    /// <summary>
    /// 海外角色信息
    /// </summary>
    public class MaxSDKHWRoleInfo : MaxSDKRoleInfo
    {
        public int roleDiamonds;
        public int castleLevel;
    }


    /// <summary>
    /// 海外事件上报信息模型
    /// </summary>
    public class MaxSDKHWReportEventInfo : MaxSDKReportEventInfo
    {
        public string eventType;        // 事件分类
        public new string eventName;    // 事件名称
        public string eventValue;       // 事件值
    }


    /// <summary>
    /// 内购订单信息模型
    /// </summary>
    public class MaxSDKHWPayInfo : MaxSDKPayInfo
    {
        public bool localPurchase;      // 是否启用本地内购逻辑
        public HWIAPType purchaseType;  // 内购类型；[Android必传][iOS可不传]
        public string productDesc;      // 游戏内显示商品描述
        public string cpProductId;      // 开发商商品项id
        public string tagCurrency;      // 游戏内显示的货币币种
        public string tagMoney;         // 游戏内显示的价格
        public string remark;           // 订单附加信息
    }


    /// <summary>
    /// 内购商品信息模型
    /// </summary>
    public class MaxSDKHWSkuDetailBean : MaxSDKBaseBean
    {
        public string productId;        //商品ID
        public string countryCode;      //地区代码
        public string currencyCode;     //币种
        public string currencySymbol;   //货币符号
        public string priceValue;       //商品价格（纯数字）
        public string price;            //商品价格（带货币符号）
        public string title;            //商品标题（苹果后台配置）
        public string description;      //商品描述（苹果后台配置）

        public string country;          //地区代码（Android有效）
        public string itemType;         //内购类型，内购或订阅（Android有效）
        public string platform;         //内购平台（Android有效）
    }


    /// <summary>
    /// 游戏内按钮信息模型
    /// </summary>
    public class MaxSDKHWInGameButtonBean : MaxSDKBaseBean
    {
        public int gameButtonId;    //研发商按钮ID
        public int id;              //SDK按钮ID
        public bool showRedPoint;   //是否显示小红点
        public string url;          //点击后显示的链接
    }


    /// <summary>
    /// 海外内购类型枚举
    /// </summary>
    public enum HWIAPType
    {
        InAppPurchase = 1,      // 消耗性内购
        InAppSubscription = 2   // 订阅类
    }


    /// <summary>
    /// SDK语言枚举
    /// </summary>
    public enum HWSDKLanguageType
    {
        Auto = 0,                   // 跟随系统
        SimplifiedChinese = 1,      // 简体中文
        English = 2,                // 英语
        TraditionalChinese = 3,     // 繁体中文
        Vietnamese = 4,             // 越南语
        Thai = 5,                   // 泰语
        Korean = 6,                 // 韩语
        Turkish = 7,                // 土耳其语
        Japanese = 8,               // 日语
        French = 9,                 // 法语
        German = 10,                // 德语
        Portuguese = 11,            // 葡萄牙语
        Spanish = 12,               // 西班牙语
        Russian = 13,               // 俄语
        Italian = 14,               // 意大利语
        Arabic = 15,                // 阿拉伯语
        Indonesian = 16,            // 印尼语
        Malaysian = 17,             // 马来语
        Dutch = 18,                 // 荷兰语
        Polish = 19                 // 波兰语
    }

    /// <summary>
    /// 用户类型
    /// </summary>
    public enum HWUserType
    {
        Guest = 2,          // 游客身份
        Facebook = 3,
        Google = 4,
        Twitter = 5,
        Huawei = 6,         // Android Only
        Line = 8,
        Naver = 9,
        VK = 11,
        Apple = 12,         // iOS Only
        GameCenter = 13,    // iOS Only
    }


    /// <summary>
    /// 海外扩展接口
    /// </summary>
    public static class HWDispatchApi
    {
        /**
         * SDK是否已经登录
         */
        public static readonly string SDK_HAS_LOGIN = "sdkHasLogin";

        /**
         * 获取内购商品信息
         */
        public static readonly string GET_IN_APP_SKU_DETAIL = "getInAppSkuDetail";

        /**
         * 设置用户中心内账号绑定监听回调
         */
        public static readonly string SET_ACCOUNT_BIND_CALLBACK = "setAccountBindCallback";

        /**
         * 获取fb游戏内好友信息
         */
        public static readonly string GET_FB_IN_GAME_FRIEND_INFO = "getFBInGameFriendInfo";

        /**
         * 邀请好友进入游戏
         */
        public static readonly string INVITE_FB_FRIEND = "inviteFBFriend";

        /**
         * 设置首次安装用户获取被邀请信息监听回调
         */
        public static readonly string SET_AD_CHANNEL_DATA_CALLBACK = "setAdChannelDataCallback";

        /**
         * 设置SDK语言
         */
        public static readonly string SET_LOCAL_LANGUAGE = "setLocalLanguage";

        /**
         * 设置游戏内按钮对接信息回调
         */
        public static readonly string SET_GET_BUTTON_LIST_CALLBACK = "setGetButtonListCallback";

        /**
         * 获取Firebase推送Token
         */
        public static readonly string GET_FIREBASE_TOKEN = "getFirebaseToken";

        /**
         * 游客账户绑定第三方平台
         */
        public static readonly string BIND_PLATFORM = "bindPlatform";

        /**
         * 第三方登录
         */
        public static readonly string LOGIN_PLATFORM = "loginPlatform";

        /**
         * 游戏内切换icon（仅iOS生效）
         */
        public static readonly string SET_ALTERNATE_ICON = "setAlternateIcon";

        /**
         * 带用户类型的登出接口（仅休闲SDK生效）
         */
        public static readonly string LOGOUT_WITH_TYPE = "logoutWithType";

        /**
         * 获取广告渠道回调的数据（仅休闲SDK生效）（仅iOS生效）
         */
        public static readonly string GET_AD_CHANNEL_DATA = "getAdChannelData";

    }



}


