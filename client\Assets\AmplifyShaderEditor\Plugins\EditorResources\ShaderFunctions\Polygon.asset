%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Polygon
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17500\n776;81;858;790;607.6838;-337.546;1.3;True;False\nNode;AmplifyShaderEditor.RegisterLocalVarNode;12;-192,-496;Inherit;False;cosSides;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-896.0001,-468.8;Inherit;False;Sides;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;25;576,-112;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.PiNode;9;-600.1328,-531.7235;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;43;112.5215,865.7967;Inherit;False;30;polarCoords;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ATan2OpNode;27;606.6644,49.34894;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;29;732.6644,-113.6511;Inherit;False;finalUVs;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;37;-240.4785,767.7967;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;20;-368,-224;Inherit;False;Constant;_Float3;Float
    3;0;0;Create;True;0;0;False;0;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;42;314.5215,754.7967;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;30;735.6644,34.34894;Inherit;False;polarCoords;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;38;-117.4785,719.7967;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;21;-368,-144;Inherit;False;Constant;_Float4;Float
    4;0;0;Create;True;0;0;False;0;-1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;19;-208,-240;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;5;-560,-304;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;8;-621.4227,144.4016;Inherit;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;28;425.6644,54.34894;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;14;-552.0002,37.50001;Inherit;False;12;cosSides;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;7;-648.0002,-42.49999;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;15;-369.5999,170.3;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CosOpNode;53;466.3291,788.9124;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;55;-474.633,867.5168;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;6;-1020.8,-468.8;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;6;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;52;-641.1151,595.2535;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;16;-529.6003,234.3001;Inherit;False;12;cosSides;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;35;-431.4811,738.2798;Inherit;False;30;polarCoords;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;-504.0001,-42.49999;Inherit;False;Width;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;18;-192.8964,46.80437;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.CosOpNode;11;-304,-496;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;10;-427.0461,-493.8062;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;22;48,-112;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.NegateNode;26;442,-37;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;13;-376,-10.5;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;-487.6001,145.3;Inherit;False;Height;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FloorOpNode;40;9.521484,720.7967;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1;-368,-304;Inherit;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;44;610.5215,816.7967;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FWidthOpNode;50;784,880;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;49;944,816;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;51;1072,816;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;45;224,960;Inherit;False;29;finalUVs;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LengthOpNode;46;400,960;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;39;-320,656;Inherit;False;Constant;_Float5;Float
    5;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TauNode;31;-749.1282,559.6296;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;33;-837.237,553.755;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;54;-468.628,792.4522;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;23;161.6644,-112.6511;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.OneMinusNode;48;784,800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;144.5215,747.7967;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1216,816;Inherit;False;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;12;0;11;0\nWireConnection;2;0;6;0\nWireConnection;25;0;23;0\nWireConnection;25;1;26;0\nWireConnection;27;0;28;0\nWireConnection;27;1;26;0\nWireConnection;29;0;25;0\nWireConnection;37;0;35;0\nWireConnection;37;1;54;0\nWireConnection;42;0;41;0\nWireConnection;42;1;43;0\nWireConnection;30;0;27;0\nWireConnection;38;0;39;0\nWireConnection;38;1;37;0\nWireConnection;19;0;1;0\nWireConnection;19;1;20;0\nWireConnection;19;2;21;0\nWireConnection;28;0;23;0\nWireConnection;15;0;4;0\nWireConnection;15;1;16;0\nWireConnection;53;0;42;0\nWireConnection;55;0;52;0\nWireConnection;52;0;31;0\nWireConnection;52;1;33;0\nWireConnection;3;0;7;0\nWireConnection;18;0;13;0\nWireConnection;18;1;15;0\nWireConnection;11;0;10;0\nWireConnection;10;0;9;0\nWireConnection;10;1;2;0\nWireConnection;22;0;19;0\nWireConnection;22;1;18;0\nWireConnection;26;0;23;1\nWireConnection;13;0;3;0\nWireConnection;13;1;14;0\nWireConnection;4;0;8;0\nWireConnection;40;0;38;0\nWireConnection;1;0;5;0\nWireConnection;44;0;53;0\nWireConnection;44;1;46;0\nWireConnection;50;0;44;0\nWireConnection;49;0;48;0\nWireConnection;49;1;50;0\nWireConnection;51;0;49;0\nWireConnection;46;0;45;0\nWireConnection;33;0;2;0\nWireConnection;54;0;52;0\nWireConnection;23;0;22;0\nWireConnection;48;0;44;0\nWireConnection;41;0;40;0\nWireConnection;41;1;55;0\nWireConnection;0;0;51;0\nASEEND*/\n//CHKSM=4217BFEBCCECAD2073A228BB92D684CF54BC2E87"
  m_functionName: 
  m_description: Creates a polygon shape with a specified amount of sides
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
