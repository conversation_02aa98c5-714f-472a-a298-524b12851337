﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionAddPolygonRiverSplitter : EditorAction
    {
        public ActionAddPolygonRiverSplitter(int layerID, int dataID, Vector3 startPos, Vector3 endPos)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mStartPos = startPos;
            mEndPos = endPos;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.AddSplitter(mDataID, mStartPos, mEndPos);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            var index = layer.GetSplitterIndex(mDataID, mStartPos, mEndPos);
            layer.DeleteSplitter(mDataID, index);
            return true;
        }

        int mLayerID;
        int mDataID;
        Vector3 mStartPos;
        Vector3 mEndPos;
    }
}


#endif