﻿ 



 
 



/*
 * created by wzw at 2019.3.10
 */
#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public struct PackRect
    {
        static PackRect empty = new PackRect(0, 0, 0, 0);

        public PackRect(int minX, int minY, int width, int height)
        {
            this.minX = minX;
            this.maxX = minX + width - 1;
            this.minY = minY;
            this.maxY = minY + height - 1;
        }

        public bool IsEmpty()
        {
            return maxX < minX || maxY < minY;
        }

        public int width { get { return maxX - minX + 1; } }
        public int height { get { return maxY - minY + 1; } }
        public Vector3 center { get { return new Vector3((minX + maxX) * 0.5f, 0, (minY + maxY) * 0.5f); } }
        public static bool operator ==(PackRect a, PackRect b)
        {
            return a.minX == b.minX && a.minY == b.minY && a.maxX == b.maxX && a.maxY == b.maxY;
        }
        public static bool operator !=(PackRect a, PackRect b)
        {
            return !(a == b);
        }
        public override string ToString()
        {
            return string.Format("{0}, {1}, {2}, {3}", minX, minY, maxX, maxY);
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public bool Contains(int x, int y)
        {
            if (IsEmpty())
            {
                return false;
            }
            if (x >= minX && x <= maxX && y >= minY && y <= maxY)
            {
                return true;
            }
            return false;
        }

        public bool Contains(PackRect r)
        {
            if (IsEmpty() || r.IsEmpty())
            {
                return false;
            }
            return r.minX >= minX && r.maxX <= maxX &&
                r.minY >= minY && r.maxY <= maxY;
        }

        public static bool Intersect(PackRect a, PackRect b)
        {
            if (a.IsEmpty() || b.IsEmpty())
            {
                return false;
            }
            if (a.minX > b.maxX || a.minY > b.maxY || b.minX > a.maxX || b.minY > a.maxY)
            {
                return false;
            }
            return true;
        }

        public bool Intersect(int minX, int minY, int maxX, int maxY)
        {
            if (minX > this.maxX || minY > this.maxY || this.minX > maxX || this.minY > maxY)
            {
                return false;
            }
            return true;
        }

        public void Add(PackRect r)
        {
            if (r.IsEmpty())
            {
                return;
            }
            if (IsEmpty())
            {
                minX = r.minX;
                minY = r.minY;
                maxX = r.maxX;
                maxY = r.maxY;
            }
            else
            {
                minX = Mathf.Min(r.minX, minX);
                minY = Mathf.Min(r.minY, minY);
                maxX = Mathf.Max(r.maxX, maxX);
                maxY = Mathf.Max(r.maxY, maxY);
            }
        }

        public int x() { return minX; }
        public int y() { return minY; }

        public int minX;
        public int minY;
        public int maxX;
        public int maxY;
    }
}
#endif