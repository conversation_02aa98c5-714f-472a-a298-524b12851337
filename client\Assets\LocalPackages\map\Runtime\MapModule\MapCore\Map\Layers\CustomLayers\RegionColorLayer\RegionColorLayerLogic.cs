﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public class RegionColorLayerLogic : MapLayerLogic
    {
        public int brushSize = 1;
        public int selectedRegionIndex = -1;
        public int selectedLayerIndex = 0;

        public RegionColorLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as RegionColorLayer;
                return layer;
            }
        }
    }
}


#endif