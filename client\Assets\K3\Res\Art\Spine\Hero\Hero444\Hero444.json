{"skeleton": {"hash": "F5MB/0lUoNg", "spine": "4.2.33", "x": -283.57, "y": -3.89, "width": 665.24, "height": 1874.72, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 422.05, "y": 1197.07}, {"name": "ALL2", "parent": "ALL", "x": -416.95, "y": -4.6, "color": "ffffffff", "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 107.37, "rotation": 90.68, "x": -0.08, "y": 10.02}, {"name": "body2", "parent": "body", "length": 245.59, "rotation": 86.89, "x": 107.37, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 87.68, "rotation": 89.17, "x": 245.59, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 226.17, "rotation": 0.56, "x": 87.68}, {"name": "tun", "parent": "ALL2", "length": 186.02, "rotation": -91.43, "x": -0.39, "y": -13.92}, {"name": "leg_L", "parent": "tun", "x": 23.83, "y": 91.04}, {"name": "leg_R", "parent": "tun", "x": 35.92, "y": -88.33}, {"name": "leg_R2", "parent": "leg_R", "length": 480.41, "rotation": 11.48, "x": 99.77, "y": -19.31}, {"name": "leg_R3", "parent": "leg_R2", "length": 336.21, "rotation": -23.3, "x": 480.41, "inherit": "noScale"}, {"name": "leg_L2", "parent": "leg_L", "length": 491.65, "rotation": -7.09, "x": 98.18, "y": 20.26}, {"name": "leg_L3", "parent": "leg_L2", "length": 390.14, "rotation": 3.59, "x": 491.65, "inherit": "noScale"}, {"name": "foot_L", "parent": "root", "length": 125.7, "rotation": 83.54, "x": -7.35, "y": 54.39}, {"name": "foot_R", "parent": "root", "length": 238, "rotation": 89.5, "x": -101.52, "y": 7.73}, {"name": "sh_L", "parent": "body2", "x": 227.33, "y": -154.47, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 210.75, "y": 166.9, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 228.51, "rotation": -131.46, "x": -29.92, "y": -7.57}, {"name": "arm_L2", "parent": "arm_L", "length": 214.78, "rotation": -83.64, "x": 228.51, "inherit": "noScale"}, {"name": "hand_L", "parent": "leg_L", "length": 72.51, "rotation": 112.56, "x": -4.34, "y": -20.06}, {"name": "hand_L2", "parent": "hand_L", "length": 65, "rotation": -33.68, "x": 72.51}, {"name": "arm_R", "parent": "sh_R", "length": 212.99, "rotation": -94.78, "x": -30.13, "y": -1.39, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 249.34, "rotation": -6.43, "x": 212.99, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 185.33, "rotation": 2.04, "x": 249.34, "inherit": "noScale"}, {"name": "RU_L", "parent": "body2", "length": 40, "x": 100.05, "y": -76.64, "color": "abe323ff"}, {"name": "RU_L2", "parent": "RU_L", "length": 40, "x": -10.26, "y": -2.75}, {"name": "RU_L3", "parent": "RU_L2", "length": 40, "x": -9.6, "y": -2.58}, {"name": "RU_R", "parent": "body2", "length": 40, "x": 93.67, "y": 88.13, "color": "abe323ff"}, {"name": "RU_R2", "parent": "RU_R", "length": 40, "x": -8.67, "y": 7.68}, {"name": "RU_R3", "parent": "RU_R2", "length": 40, "x": -11.19, "y": 9.01}, {"name": "cloth_L", "parent": "body2", "length": 41.85, "rotation": -67.62, "x": 36.52, "y": -127.4, "inherit": "onlyTranslation"}, {"name": "cloth_L2", "parent": "cloth_L", "length": 49.05, "rotation": 5.77, "x": 41.85, "color": "abe323ff"}, {"name": "cloth_R", "parent": "body2", "length": 45.6, "rotation": -104.19, "x": 38.49, "y": 152.39, "inherit": "onlyTranslation"}, {"name": "cloth_R2", "parent": "cloth_R", "length": 46.24, "rotation": -8.43, "x": 45.6, "color": "abe323ff"}, {"name": "cloth_R3", "parent": "cloth_R2", "length": 45.39, "rotation": 0.94, "x": 46.24, "color": "abe323ff"}, {"name": "eye_L", "parent": "head", "x": 54.34, "y": -30.12}, {"name": "eye_R", "parent": "head", "x": 54.23, "y": 36.01}, {"name": "eyebrow_L", "parent": "head", "length": 13.35, "rotation": 77.65, "x": 73.34, "y": -58.53}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 25.29, "rotation": 32.87, "x": 13.35}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 15.14, "rotation": 0.05, "x": 25.29}, {"name": "eyebrow_R", "parent": "head", "length": 13.4, "rotation": -76.3, "x": 72.39, "y": 61.31}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 22.04, "rotation": -36.83, "x": 13.4}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 13.4, "rotation": -4.28, "x": 22.04}, {"name": "hair_L", "parent": "head", "x": 116.03, "y": -82.54}, {"name": "hair_L2", "parent": "hair_L", "length": 27.06, "rotation": -173.06, "x": -12.77, "y": -4.72}, {"name": "hair_L3", "parent": "hair_L2", "length": 35.07, "rotation": 15.56, "x": 27.06, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 32.26, "rotation": -12.25, "x": 35.07, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 26.33, "rotation": -24.56, "x": 32.26, "color": "abe323ff"}, {"name": "hair_L6", "parent": "hair_L5", "length": 25.92, "rotation": -30.96, "x": 26.33, "color": "abe323ff"}, {"name": "hair_R", "parent": "head", "x": 139.52, "y": 65.2}, {"name": "hair_R2", "parent": "hair_R", "length": 39.25, "rotation": 153.7, "x": -9.89, "y": 8.52}, {"name": "hair_R3", "parent": "hair_R2", "length": 40.59, "rotation": 0.27, "x": 39.25, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 42.87, "rotation": 17.68, "x": 40.59, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 29.91, "rotation": 21.86, "x": 42.87, "color": "abe323ff"}, {"name": "hair_R6", "parent": "hair_R5", "length": 31.63, "rotation": 26.27, "x": 29.91, "color": "abe323ff"}, {"name": "hair_F", "parent": "head", "x": 130.75, "y": -21.46}, {"name": "hair_F2", "parent": "hair_F", "length": 16.93, "rotation": 136.7, "x": 18.52, "y": 16.67}, {"name": "hair_F3", "parent": "hair_F2", "length": 25.68, "rotation": 35.53, "x": 16.93, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 25.6, "rotation": 1.33, "x": 25.68, "color": "abe323ff"}, {"name": "hair_F5", "parent": "hair_F4", "length": 29.32, "rotation": -21.95, "x": 25.6, "color": "abe323ff"}, {"name": "hair_F6", "parent": "hair_F", "length": 45.06, "rotation": 98.33, "x": 30.87, "y": 16.51}, {"name": "hair_F7", "parent": "hair_F6", "length": 31.25, "rotation": 24.3, "x": 45.06}, {"name": "hair_F8", "parent": "hair_F7", "length": 38.33, "rotation": 27.34, "x": 31.57, "y": -0.2}, {"name": "hair_F9", "parent": "hair_F8", "length": 36.82, "rotation": 29.14, "x": 38.33, "color": "abe323ff"}, {"name": "hair_F10", "parent": "hair_F9", "length": 33.11, "rotation": 13.48, "x": 36.82, "color": "abe323ff"}, {"name": "hair_F11", "parent": "hair_F10", "length": 23.06, "rotation": 25.47, "x": 33.11, "color": "abe323ff"}, {"name": "hair_F12", "parent": "hair_F11", "length": 25.89, "rotation": 36.37, "x": 23.06, "color": "abe323ff"}, {"name": "headround3", "parent": "head", "x": 387.35, "y": 1.56, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "x": 0.32, "y": -68.28, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 327.06, "y": -67.01, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "rotation": -86.89, "x": 207.18, "y": -434.89, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "rotation": -86.89, "x": 148.88, "y": -438.07, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 386.73, "y": -119.19, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 386.73, "y": -179.81, "icon": "warning"}, {"name": "sh_L2", "parent": "sh_L", "length": 256.63, "rotation": -125.04, "x": -29.92, "y": -7.6}, {"name": "sh_L3", "parent": "sh_L2", "length": 241.7, "rotation": -96.84, "x": 256.63, "color": "abe323ff"}, {"name": "arm_L3", "parent": "hand_L2", "rotation": 12.55, "x": 65.05, "y": -0.13, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "sh_L", "rotation": -86.89, "x": -181.23, "y": -178.81, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "leg_R", "length": 493.29, "rotation": 16.6, "x": 99.76, "y": -19.31}, {"name": "leg_R5", "parent": "leg_R4", "length": 347.48, "rotation": -35.65, "x": 493.29, "color": "abe323ff"}, {"name": "leg_R6", "parent": "foot_R", "rotation": -89.5, "x": 237.56, "y": -0.06, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "root", "x": -22.4, "y": 572.55, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L4", "parent": "leg_L", "length": 881.6, "rotation": -5.37, "x": 98.17, "y": 20.26}, {"name": "leg_L5", "parent": "foot_L", "rotation": -83.54, "x": 125.34, "y": 0.04, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L4", "rotation": 96.92, "x": 491.64, "y": -13.65, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "arm", "bone": "root", "attachment": "arm"}, {"name": "cloth_B", "bone": "root", "attachment": "cloth_B"}, {"name": "hat_B", "bone": "root", "attachment": "hat_B"}, {"name": "hat_F", "bone": "root", "attachment": "hat_F"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "gun", "bone": "root", "attachment": "gun"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair_FR", "bone": "root", "attachment": "hair_FR"}, {"name": "hair_FL", "bone": "root", "attachment": "hair_FL"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "bones": ["sh_L2", "sh_L3"], "target": "arm_L3", "bendPositive": false}, {"name": "arm_L1", "order": 2, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 3, "bones": ["arm_L2"], "target": "arm_L3", "compress": true, "stretch": true}, {"name": "leg_L", "order": 4, "bones": ["leg_L4"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 5, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 6, "bones": ["leg_L3"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_R", "order": 7, "bones": ["leg_R4", "leg_R5"], "target": "leg_R6", "bendPositive": false}, {"name": "leg_R1", "order": 9, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 10, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 1, "bones": ["arm_L1"], "target": "sh_L3", "rotation": 135.24, "x": 28.72, "y": -26.23, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 11, "bones": ["bodyround2"], "target": "bodyround", "y": -58.39, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 12, "bones": ["headround2"], "target": "headround", "x": -60.61, "y": -0.29, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 13, "bones": ["eyebrow_L"], "target": "headround", "rotation": 77.65, "x": -314.33, "y": 8.2, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 14, "bones": ["eyebrow_R"], "target": "headround", "rotation": -76.3, "x": -315.29, "y": 128.03, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 15, "bones": ["hair_F"], "target": "headround", "x": -256.92, "y": 45.26, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 16, "bones": ["hair_L"], "target": "headround", "x": -271.65, "y": -15.81, "mixRotate": 0, "mixX": 0.018, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround5", "order": 17, "bones": ["hair_R"], "target": "headround", "x": -248.16, "y": 131.93, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_R3", "order": 8, "bones": ["leg_R1"], "target": "leg_R5", "rotation": 110.88, "x": 14.26, "y": -44.7, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "RU_L", "order": 21, "bones": ["cloth_L"], "target": "RU_L", "rotation": -154.51, "x": -63.53, "y": -50.76, "mixRotate": 0, "mixX": 0.2, "mixScaleX": 0, "mixShearY": 0}, {"name": "RU_R", "order": 22, "bones": ["cloth_R"], "target": "RU_R", "rotation": 168.92, "x": -55.19, "y": 64.25, "mixRotate": 0, "mixX": 0.2, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 18, "bones": ["tunround2"], "target": "tunround", "y": -60.62, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "cloth_L2", "order": 23, "bone": "cloth_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "cloth_R2", "order": 19, "bone": "cloth_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "cloth_R3", "order": 20, "bone": "cloth_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 32, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 33, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F5", "order": 34, "bone": "hair_F5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F9", "order": 35, "bone": "hair_F9", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F10", "order": 36, "bone": "hair_F10", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F11", "order": 37, "bone": "hair_F11", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F12", "order": 38, "bone": "hair_F12", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 24, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 25, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 26, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L6", "order": 27, "bone": "hair_L6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 28, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 29, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 30, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R6", "order": 31, "bone": "hair_R6", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm": {"arm": {"type": "mesh", "uvs": [0.19041, 0.00066, 0.3609, 0.02634, 0.54179, 0.02489, 0.60558, 0.02002, 0.67648, 0.01115, 0.71505, 0.00543, 0.72876, 0.00901, 0.73874, 0.02609, 0.73915, 0.05331, 0.76218, 0.08278, 0.78367, 0.10948, 0.82647, 0.15373, 0.88469, 0.20856, 0.927, 0.25108, 0.95109, 0.26341, 0.97391, 0.29794, 0.99104, 0.31667, 0.99885, 0.33144, 0.99785, 0.36206, 0.97884, 0.40561, 0.96427, 0.42813, 0.93788, 0.4828, 0.91424, 0.52373, 0.88356, 0.56413, 0.84429, 0.53446, 0.80658, 0.50595, 0.76886, 0.47745, 0.79276, 0.4288, 0.80627, 0.41372, 0.80971, 0.39283, 0.82342, 0.37625, 0.81806, 0.35266, 0.8034, 0.34255, 0.79042, 0.32872, 0.71425, 0.26837, 0.68358, 0.24713, 0.65039, 0.22315, 0.6111, 0.20012, 0.55063, 0.2005, 0.35464, 0.20088, 0.30348, 0.20077, 0.29217, 0.22952, 0.28086, 0.26417, 0.26631, 0.31761, 0.24867, 0.37304, 0.23569, 0.407, 0.23543, 0.42711, 0.22905, 0.4509, 0.22335, 0.48491, 0.22049, 0.51919, 0.19529, 0.64248, 0.20334, 0.65671, 0.1793, 0.75262, 0.16185, 0.76296, 0.14007, 0.76575, 0.13609, 0.78889, 0.15169, 0.79825, 0.14543, 0.81897, 0.13917, 0.8397, 0.13113, 0.83964, 0.13178, 0.85985, 0.13265, 0.88159, 0.13929, 0.97357, 0.13185, 0.98545, 0.10499, 0.99905, 0.07361, 0.99831, 0.01893, 0.96367, 0.00207, 0.94345, 0.00124, 0.92922, 0.03498, 0.8667, 0.04351, 0.84775, 0.04956, 0.83293, 0.0445, 0.81338, 0.03945, 0.79384, 0.05956, 0.78463, 0.05857, 0.7708, 0.03003, 0.74965, 0.03455, 0.6405, 0.05582, 0.62412, 0.06528, 0.5362, 0.07829, 0.47843, 0.07577, 0.45179, 0.09248, 0.42251, 0.0859, 0.39106, 0.10473, 0.36939, 0.11627, 0.32702, 0.12852, 0.27407, 0.13861, 0.21208, 0.14485, 0.13096, 0.15583, 0.08765, 0.15026, 0.05134, 0.15197, 0.02538, 0.15985, 0.0102, 0.17358, 0.00235, 0.70091, 0.01788, 0.69051, 0.03463, 0.68144, 0.06741, 0.67832, 0.11001, 0.67872, 0.18523, 0.18597, 0.01653, 0.18916, 0.04037, 0.19358, 0.06287, 0.20118, 0.10727, 0.22378, 0.15901, 0.26134, 0.18821, 0.25678, 0.22119, 0.24632, 0.25652, 0.21299, 0.36612, 0.20453, 0.39845, 0.20106, 0.42557, 0.19134, 0.44941, 0.18551, 0.48209, 0.17606, 0.52375, 0.15061, 0.6208, 0.0969, 0.61536, 0.11543, 0.50852, 0.12453, 0.46579, 0.12989, 0.43374, 0.13363, 0.40618, 0.1422, 0.38144, 0.15344, 0.34658, 0.18035, 0.2403, 0.18624, 0.19598, 0.1841, 0.15268, 0.18249, 0.11557, 0.05305, 0.73284, 0.14812, 0.73358, 0.05581, 0.75182, 0.1441, 0.74948, 0.08214, 0.71604, 0.12223, 0.71753, 0.10165, 0.73896, 0.10023, 0.76169, 0.09881, 0.77958, 0.09505, 0.80614, 0.09374, 0.82903, 0.09006, 0.84788, 0.08665, 0.86609, 0.72878, 0.108, 0.75255, 0.14088, 0.79313, 0.17742, 0.86211, 0.24318, 0.89923, 0.27829, 0.92995, 0.3063, 0.94908, 0.33797, 0.9456, 0.37633, 0.93285, 0.41104, 0.91198, 0.44626, 0.84764, 0.41642, 0.86329, 0.38841, 0.86792, 0.36101, 0.85401, 0.32934, 0.81633, 0.28976, 0.74469, 0.23556, 0.70585, 0.20877, 0.71604, 0.07509, 0.71488, 0.04464], "triangles": [72, 73, 74, 72, 74, 134, 134, 55, 57, 135, 72, 134, 59, 135, 134, 71, 72, 135, 57, 59, 134, 58, 59, 57, 136, 71, 135, 136, 135, 59, 136, 59, 60, 136, 70, 71, 137, 136, 60, 137, 70, 136, 137, 60, 61, 66, 67, 68, 69, 66, 68, 137, 62, 65, 62, 137, 61, 137, 69, 70, 62, 64, 65, 137, 66, 69, 65, 66, 137, 63, 64, 62, 117, 82, 118, 110, 117, 118, 109, 110, 118, 47, 109, 46, 110, 109, 47, 116, 82, 117, 116, 117, 110, 81, 82, 116, 80, 81, 116, 111, 116, 110, 48, 110, 47, 111, 110, 48, 115, 80, 116, 115, 116, 111, 49, 111, 48, 112, 115, 111, 112, 111, 49, 79, 80, 115, 114, 79, 115, 113, 114, 115, 112, 113, 115, 78, 79, 114, 50, 112, 49, 113, 112, 50, 129, 78, 114, 77, 78, 129, 130, 114, 113, 130, 113, 50, 130, 50, 51, 129, 114, 130, 125, 77, 129, 126, 130, 51, 131, 129, 130, 128, 130, 126, 131, 130, 128, 76, 77, 125, 127, 125, 129, 127, 129, 131, 76, 125, 127, 52, 126, 51, 53, 128, 126, 132, 127, 131, 132, 131, 128, 52, 53, 126, 54, 132, 128, 54, 128, 53, 75, 127, 132, 76, 127, 75, 133, 75, 132, 133, 132, 54, 74, 75, 133, 55, 133, 54, 134, 74, 133, 134, 133, 55, 57, 55, 56, 123, 102, 103, 124, 89, 101, 88, 89, 124, 123, 124, 102, 88, 124, 123, 122, 123, 103, 87, 88, 123, 87, 123, 122, 104, 122, 103, 41, 105, 104, 105, 122, 104, 121, 87, 122, 121, 122, 105, 106, 121, 105, 42, 105, 41, 106, 105, 42, 86, 87, 121, 43, 106, 42, 120, 86, 121, 85, 86, 120, 107, 121, 106, 107, 106, 43, 120, 121, 107, 84, 85, 120, 44, 107, 43, 119, 84, 120, 108, 120, 107, 119, 120, 108, 118, 84, 119, 83, 84, 118, 45, 107, 44, 108, 107, 45, 82, 83, 118, 108, 118, 119, 109, 108, 45, 109, 118, 108, 46, 109, 45, 31, 151, 150, 17, 144, 16, 18, 144, 17, 30, 31, 150, 145, 150, 144, 145, 144, 18, 149, 30, 150, 146, 149, 150, 19, 145, 18, 145, 146, 150, 146, 145, 19, 148, 30, 149, 29, 30, 148, 28, 29, 148, 20, 146, 19, 147, 149, 146, 148, 149, 147, 20, 147, 146, 21, 147, 20, 148, 27, 28, 25, 27, 148, 26, 27, 25, 22, 147, 21, 24, 25, 148, 24, 148, 147, 24, 147, 22, 23, 24, 22, 98, 97, 138, 36, 98, 154, 155, 156, 8, 155, 8, 9, 138, 155, 9, 97, 155, 138, 10, 139, 138, 10, 138, 9, 140, 10, 11, 139, 10, 140, 98, 138, 139, 154, 98, 139, 153, 154, 139, 140, 153, 139, 141, 11, 12, 140, 11, 141, 152, 153, 140, 35, 36, 154, 34, 35, 154, 141, 12, 13, 153, 34, 154, 142, 141, 13, 141, 152, 140, 143, 13, 14, 143, 14, 15, 142, 13, 143, 33, 153, 152, 34, 153, 33, 151, 152, 141, 151, 141, 142, 32, 33, 152, 144, 143, 15, 144, 15, 16, 151, 32, 152, 31, 32, 151, 143, 150, 151, 143, 151, 142, 150, 143, 144, 102, 101, 1, 101, 89, 90, 99, 93, 0, 100, 99, 0, 100, 0, 1, 92, 93, 99, 91, 92, 99, 101, 100, 1, 91, 100, 90, 100, 91, 99, 101, 90, 100, 102, 124, 101, 97, 96, 155, 97, 3, 96, 94, 4, 5, 95, 4, 94, 6, 156, 94, 6, 94, 5, 156, 6, 7, 95, 94, 156, 156, 7, 8, 96, 4, 95, 96, 95, 156, 3, 4, 96, 96, 156, 155, 36, 37, 98, 37, 3, 97, 40, 41, 104, 103, 102, 1, 104, 103, 1, 40, 104, 1, 38, 2, 3, 39, 40, 1, 39, 1, 2, 39, 2, 38, 37, 38, 3, 37, 97, 98], "vertices": [3, 16, 3.15, 327.74, 0, 17, 19.73, 6.37, 1, 18, -273.18, -197.22, 0, 4, 4, 220.39, 59.01, 0.83889, 16, -6.94, 213.48, 0.00042, 17, 9.64, -107.89, 0.16043, 18, -180.87, -129.13, 0.00027, 5, 4, 227.86, -61.24, 0.64912, 16, 0.52, 93.23, 0.21674, 17, 17.1, -228.13, 0.00047, 18, -95.7, -43.92, 0.13303, 22, -53.8, 225.27, 0.00064, 5, 4, 233.25, -103.49, 0.27559, 16, 5.92, 50.98, 0.54352, 17, 22.5, -270.39, 0.00023, 18, -67.61, -11.9, 0.18035, 22, -60.42, 267.35, 0.00031, 4, 4, 241.43, -150.33, 0.00835, 16, 14.1, 4.14, 0.99163, 17, 30.68, -317.23, 1e-05, 22, -69.96, 313.93, 1e-05, 2, 16, 19.12, -21.32, 0.92044, 18, -22.17, 45.86, 0.07956, 2, 16, 17.35, -30.55, 0.86801, 18, -14.07, 50.64, 0.13199, 2, 16, 6.89, -37.78, 0.74524, 18, -1.73, 47.6, 0.25476, 2, 16, -10.33, -38.99, 0.41242, 18, 10.58, 35.49, 0.58758, 2, 16, -28.15, -55.32, 0.08606, 18, 34.61, 32.95, 0.91394, 2, 16, -44.28, -70.53, 0.00895, 18, 56.69, 30.94, 0.99105, 1, 18, 96.68, 30.96, 1, 1, 18, 148.7, 33.41, 1, 1, 18, 187.69, 33.98, 1, 2, 18, 204.61, 39.68, 0.99744, 19, -42.12, -19.36, 0.00256, 2, 18, 230.8, 34.75, 0.74259, 19, -34.31, 6.12, 0.25741, 2, 18, 247.26, 34.3, 0.5388, 19, -32.04, 22.43, 0.4612, 2, 18, 257.54, 31.28, 0.4262, 19, -27.9, 32.31, 0.5738, 2, 18, 270.69, 16.98, 0.25705, 19, -12.22, 43.8, 0.74295, 2, 18, 281.04, -11.57, 0.04097, 19, 17.33, 50.93, 0.95903, 2, 18, 284.15, -28.55, 0.00435, 19, 34.57, 52.14, 0.99565, 1, 19, 72.71, 59.77, 1, 1, 19, 102.86, 63.46, 1, 1, 19, 135.65, 63.25, 1, 1, 19, 137.05, 31.06, 1, 1, 19, 138.4, 0.15, 1, 1, 19, 139.74, -30.76, 1, 2, 18, 203.09, -109.02, 0.01083, 19, 105.63, -37.34, 0.98917, 2, 18, 202.79, -95.9, 0.03817, 19, 92.54, -36.19, 0.96183, 2, 18, 195.12, -84.85, 0.10996, 19, 80.7, -42.58, 0.89004, 2, 18, 194.25, -70.96, 0.23366, 19, 66.78, -41.91, 0.76634, 2, 18, 181.21, -62.81, 0.52659, 19, 57.24, -53.97, 0.47341, 2, 18, 169.75, -65.1, 0.69209, 19, 58.24, -65.61, 0.30791, 2, 18, 157.44, -64.92, 0.80833, 19, 56.7, -77.82, 0.19167, 5, 4, 79.96, -184.31, 0.08435, 17, -130.79, -351.21, 0, 18, 94.46, -73.27, 0.91148, 19, 58.02, -141.34, 0.00416, 22, 90.46, 352.59, 0, 4, 4, 92.3, -163.18, 0.23143, 17, -118.46, -330.08, 1e-05, 18, 70.45, -78.01, 0.76855, 22, 78.74, 331.11, 1e-05, 5, 4, 106.28, -140.29, 0.5211, 16, -121.06, 14.19, 0.00024, 17, -104.48, -307.18, 2e-05, 18, 44.04, -82.7, 0.47862, 22, 65.43, 307.82, 2e-05, 1, 4, 119.43, -113.36, 1, 1, 4, 117, -73.16, 1, 4, 4, 109.68, 57.16, 0.98799, 16, -117.65, 211.63, 4e-05, 17, -101.08, -109.74, 0.01194, 18, -106.18, -210.88, 3e-05, 5, 4, 107.89, 91.19, 0.85072, 16, -119.44, 245.66, 9e-05, 17, -102.86, -75.71, 0.0149, 18, -130.5, -234.75, 6e-05, 22, 70.54, 76.4, 0.13423, 5, 4, 89.28, 97.72, 0.33165, 16, -138.05, 252.19, 4e-05, 17, -121.47, -69.18, 0.00056, 18, -123.07, -253.02, 2e-05, 22, 89.33, 70.41, 0.66773, 4, 4, 66.94, 104.05, 0.15393, 16, -160.39, 258.52, 1e-05, 18, -113.02, -273.95, 1e-05, 22, 111.85, 64.73, 0.84604, 4, 4, 32.58, 111.88, 0.00776, 16, -194.75, 266.35, 0, 18, -96.14, -304.89, 0, 22, 146.43, 57.9, 0.99224, 3, 4, -3.15, 121.7, 5e-05, 22, 182.42, 49.12, 0.96956, 23, -35.87, 45.39, 0.03039, 2, 22, 204.6, 42.3, 0.74416, 23, -13.07, 41.1, 0.25584, 2, 22, 217.32, 43.19, 0.50951, 23, -0.53, 43.4, 0.49049, 2, 22, 232.7, 40.21, 0.22317, 23, 15.09, 42.17, 0.77683, 2, 22, 254.51, 38.23, 0.02847, 23, 36.98, 42.63, 0.97153, 1, 23, 58.67, 44.99, 1, 2, 23, 138.61, 43.72, 1, 24, -109.1, 47.64, 0, 2, 23, 146.41, 50.73, 0.99999, 24, -101.05, 54.37, 1e-05, 2, 23, 209.18, 46.84, 0.9999, 24, -38.46, 48.25, 0.0001, 1, 23, 217.87, 36.72, 1, 2, 23, 222.42, 22.83, 0.9846, 24, -26.08, 23.78, 0.0154, 2, 23, 237.33, 23.08, 0.73736, 24, -11.18, 23.49, 0.26264, 2, 23, 241.13, 34.43, 0.58362, 24, -6.98, 34.7, 0.41638, 2, 23, 254.82, 32.89, 0.36598, 24, 6.66, 32.68, 0.63402, 2, 23, 268.52, 31.36, 0.14338, 24, 20.29, 30.65, 0.85662, 2, 23, 269.53, 26.09, 0.09604, 24, 21.11, 25.36, 0.90396, 2, 23, 282.01, 29.01, 0.01435, 24, 33.69, 27.82, 0.98565, 1, 24, 47.2, 30.59, 1, 1, 24, 104.08, 44.24, 1, 1, 24, 112.3, 40.55, 1, 1, 24, 123.66, 24.26, 1, 1, 24, 126.53, 3.55, 1, 1, 24, 110.64, -35.89, 1, 1, 24, 99.78, -49.02, 1, 1, 24, 90.95, -51, 1, 2, 23, 298.8, -33.39, 0.0013, 24, 48.24, -35.13, 0.9987, 2, 23, 285.91, -30.15, 0.02162, 24, 35.48, -31.43, 0.97838, 2, 23, 275.92, -28.02, 0.08508, 24, 25.56, -28.95, 0.91492, 2, 23, 264.41, -33.73, 0.25511, 24, 13.86, -34.25, 0.74489, 2, 23, 252.91, -39.44, 0.39582, 24, 2.17, -39.55, 0.60418, 2, 23, 244.58, -27.44, 0.59839, 24, -5.73, -27.25, 0.40161, 2, 23, 236.11, -29.79, 0.85772, 24, -14.28, -29.3, 0.14228, 2, 23, 226.65, -51.04, 0.99449, 24, -24.49, -50.2, 0.00551, 1, 23, 158.18, -61.54, 1, 1, 23, 145.24, -49.66, 1, 1, 23, 89.34, -54.31, 1, 2, 22, 258.46, -58.39, 0.03736, 23, 51.73, -52.93, 0.96264, 2, 22, 241.77, -61.47, 0.10989, 23, 35.49, -57.86, 0.89011, 2, 22, 222.35, -51.92, 0.33672, 23, 15.12, -50.55, 0.66328, 2, 22, 202.84, -57.96, 0.61371, 23, -3.59, -58.73, 0.38629, 2, 22, 188.1, -46.6, 0.81458, 23, -19.51, -49.09, 0.18542, 2, 22, 160.7, -41.18, 0.99298, 23, -47.35, -46.78, 0.00702, 1, 22, 126.57, -35.85, 1, 1, 22, 86.84, -32.43, 1, 1, 22, 35.24, -32.57, 1, 2, 17, -36.59, 26.37, 0.1095, 22, 7.27, -27.57, 0.8905, 2, 17, -13.8, 31.33, 0.60758, 22, -15.36, -33.18, 0.39242, 4, 16, -13.89, 352.46, 0, 17, 2.69, 31.09, 0.88721, 18, -280.42, -226.36, 0, 22, -31.86, -33.42, 0.11279, 4, 16, -3.99, 347.73, 0, 17, 12.59, 26.37, 0.96437, 18, -283.43, -215.82, 0, 22, -41.89, -28.99, 0.03563, 1, 17, 18.05, 17.51, 1, 1, 16, 10.72, -12.34, 1, 3, 4, 227.07, -160.47, 0.02555, 16, -0.26, -6, 0.96807, 71, -272.94, 34.76, 0.00638, 3, 4, 205.99, -155.57, 0.22765, 16, -21.34, -1.1, 0.76168, 71, -278.97, 13.99, 0.01068, 3, 4, 178.91, -154.97, 0.43887, 16, -48.42, -0.49, 0.54799, 71, -281.05, -13.03, 0.01314, 4, 4, 131.31, -157.82, 0.56569, 16, -96.02, -3.35, 0.14284, 18, 40.6, -52.33, 0.28618, 71, -280.79, -60.71, 0.00529, 2, 17, 9.52, 8.78, 0.99989, 71, -608.96, 46.24, 0.00011, 3, 4, 205.3, 172.74, 0.11009, 17, -5.45, 5.84, 0.88438, 71, -606.83, 31.13, 0.00553, 5, 4, 191.21, 169.02, 0.19333, 16, -36.12, 323.49, 0, 17, -19.54, 2.13, 0.7965, 18, -243.99, -223.84, 0, 71, -603.89, 16.86, 0.01017, 3, 4, 163.38, 162.44, 0.40384, 17, -47.37, -4.46, 0.58545, 71, -598.83, -11.29, 0.0107, 6, 4, 131.45, 145.62, 0.47773, 16, -95.89, 300.09, 6e-05, 17, -79.31, -21.27, 0.3069, 18, -186.89, -253.14, 4e-05, 22, 48.58, 21.3, 0.20157, 71, -583.77, -44.09, 0.01371, 6, 4, 114.32, 119.64, 0.60421, 16, -113.01, 274.11, 9e-05, 17, -96.43, -47.26, 0.00435, 18, -156.07, -248.77, 6e-05, 22, 64.95, 47.77, 0.37757, 71, -558.76, -62.6, 0.01373, 5, 4, 93.28, 121.54, 0.22776, 16, -134.05, 276.01, 3e-05, 18, -143.57, -265.79, 2e-05, 22, 86.03, 46.48, 0.76026, 71, -561.8, -83.51, 0.01193, 5, 4, 70.53, 127.28, 0.10724, 16, -156.8, 281.75, 1e-05, 18, -132.81, -286.64, 1e-05, 22, 108.94, 41.41, 0.88015, 71, -568.77, -105.91, 0.01259, 3, 22, 180.03, 25.08, 0.98402, 23, -35.55, 21.23, 0.00933, 71, -590.96, -175.4, 0.00665, 3, 22, 200.93, 21.17, 0.83382, 23, -14.35, 19.69, 0.16104, 71, -596.6, -195.9, 0.00514, 3, 22, 218.25, 20.3, 0.41508, 23, 2.96, 20.76, 0.58316, 71, -598.91, -213.09, 0.00176, 2, 22, 233.85, 15.11, 0.07856, 23, 19.05, 17.35, 0.92144, 1, 23, 40.13, 17.56, 1, 1, 23, 67.26, 16.53, 1, 2, 23, 130.91, 11.85, 1, 24, -117.93, 16.07, 0, 1, 23, 134.48, -23.9, 1, 1, 23, 65.64, -24.96, 1, 2, 22, 247.91, -28.37, 0.04188, 23, 37.88, -24.28, 0.95812, 2, 22, 227.36, -26.51, 0.20932, 23, 17.26, -24.73, 0.79068, 3, 22, 209.75, -25.48, 0.59323, 23, -0.37, -25.68, 0.40527, 71, -643.81, -200.8, 0.0015, 3, 22, 193.64, -21.1, 0.90459, 23, -16.86, -23.13, 0.09104, 71, -638.11, -185.11, 0.00436, 2, 22, 170.99, -15.48, 0.99181, 71, -630.62, -163.01, 0.00819, 2, 22, 102.35, -3.23, 0.98682, 71, -612.7, -95.63, 0.01318, 2, 22, 74.02, -1.67, 0.98638, 71, -608.78, -67.53, 0.01362, 3, 17, -76.74, 5.34, 0.12419, 22, 46.79, -5.38, 0.86368, 71, -610.21, -40.08, 0.01213, 3, 17, -53.3, 7.68, 0.31476, 22, 23.43, -8.4, 0.67626, 71, -611.28, -16.55, 0.00898, 2, 23, 213.21, -38.07, 0.99767, 24, -37.46, -36.76, 0.00233, 1, 23, 201.37, 24.13, 1, 2, 23, 224.66, -33.93, 0.96816, 24, -25.87, -33.03, 0.03184, 1, 23, 211.78, 23.46, 1, 1, 23, 199, -21.14, 1, 2, 23, 194.74, 5.23, 1, 24, -54.37, 7.18, 0, 1, 23, 210.73, -5.57, 1, 1, 23, 225.05, -3.7, 1, 1, 23, 236.36, -2.42, 1, 2, 23, 253.36, -1.61, 0.32729, 24, 3.97, -1.75, 0.67271, 1, 24, 18.44, -0.3, 1, 1, 24, 30.63, -0.81, 1, 1, 24, 42.39, -1.22, 1, 3, 16, -45.32, -33.98, 0.01857, 18, 29.99, 5.95, 0.97237, 71, -247.44, -11.75, 0.00906, 3, 16, -65.28, -50.92, 0.00048, 18, 55.9, 2.21, 0.99092, 71, -231.62, -32.6, 0.00859, 2, 18, 91.41, 4.68, 0.99096, 71, -204.59, -55.76, 0.00904, 2, 18, 153.39, 7.22, 0.99456, 71, -158.65, -97.46, 0.00544, 1, 18, 186.62, 8.72, 1, 1, 18, 213.66, 10.43, 1, 2, 18, 236.83, 5.07, 0.41781, 19, -4.12, 8.83, 0.58219, 2, 18, 252.25, -13.88, 0.05009, 19, 16.44, 22.05, 0.94991, 2, 18, 261.64, -35.52, 0.00101, 19, 39, 28.99, 0.99899, 1, 19, 65.17, 31.89, 1, 2, 18, 223.61, -77.78, 0.02757, 19, 76.83, -13.48, 0.97243, 2, 18, 218.57, -57.81, 0.09418, 19, 56.41, -16.28, 0.90582, 2, 18, 208.58, -43.27, 0.27907, 19, 40.83, -24.6, 0.72093, 2, 18, 187.89, -35.47, 0.64446, 19, 30.78, -44.3, 0.35554, 3, 18, 152.4, -35.21, 0.9009, 19, 26.59, -79.54, 0.09356, 71, -189.14, -126.99, 0.00555, 6, 4, 101.83, -203.42, 0.02601, 17, -108.92, -370.32, 0, 18, 94.3, -44.22, 0.96388, 19, 29.11, -138.29, 0.00297, 22, 68.04, 371.06, 0, 71, -236.85, -92.62, 0.00713, 5, 4, 117.39, -176.67, 0.15705, 17, -93.37, -343.57, 1e-05, 18, 63.95, -50.28, 0.83446, 22, 53.27, 343.87, 1e-05, 71, -262.72, -75.64, 0.00847, 3, 16, -24.95, -24.37, 0.19322, 18, 9.3, 14.85, 0.79905, 71, -255.93, 9.11, 0.00773, 3, 16, -5.72, -22.55, 0.6151, 18, -4.8, 28.06, 0.37903, 71, -256.71, 28.42, 0.00587], "hull": 94, "edges": [0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 60, 62, 72, 74, 78, 80, 88, 90, 100, 102, 102, 104, 116, 118, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 152, 154, 154, 156, 162, 164, 164, 166, 166, 168, 168, 170, 174, 176, 176, 178, 178, 180, 8, 10, 4, 6, 6, 8, 2, 4, 184, 186, 186, 0, 180, 182, 182, 184, 170, 172, 172, 174, 156, 158, 158, 160, 160, 162, 150, 152, 146, 148, 148, 150, 122, 124, 118, 120, 120, 122, 138, 140, 140, 142, 108, 110, 110, 112, 104, 106, 106, 108, 98, 100, 90, 92, 92, 94, 94, 96, 96, 98, 86, 88, 84, 86, 80, 82, 82, 84, 74, 76, 76, 78, 68, 70, 62, 64, 58, 60, 54, 56, 56, 58, 52, 54, 42, 44, 44, 46, 36, 38, 38, 40, 40, 42, 34, 36, 30, 32, 32, 34, 18, 20, 20, 22, 22, 24, 24, 26, 64, 66, 66, 68, 70, 72, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 226, 224, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 248, 246, 150, 254, 254, 250, 108, 256, 256, 252, 258, 228, 226, 260, 112, 114, 114, 116, 142, 144, 144, 146, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 46, 48, 294, 48, 48, 50, 50, 52, 50, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 196, 276, 310, 310, 312], "width": 666, "height": 634}}, "body": {"body": {"type": "mesh", "uvs": [0.48265, 0.004, 0.52948, 0.00729, 0.56017, 0.01485, 0.59027, 0.02368, 0.60582, 0.0385, 0.61802, 0.04444, 0.64037, 0.05282, 0.65886, 0.06265, 0.66686, 0.0726, 0.66373, 0.0834, 0.64271, 0.09313, 0.59559, 0.09716, 0.56564, 0.10102, 0.54573, 0.10151, 0.56042, 0.10635, 0.58531, 0.10635, 0.60712, 0.10181, 0.59728, 0.10858, 0.57724, 0.11132, 0.60317, 0.11841, 0.56183, 0.13157, 0.60577, 0.13614, 0.67949, 0.13675, 0.74246, 0.13495, 0.74475, 0.14051, 0.727, 0.14339, 0.71328, 0.14898, 0.70224, 0.16059, 0.69743, 0.17586, 0.69684, 0.20234, 0.69067, 0.20989, 0.69176, 0.2222, 0.68077, 0.23682, 0.65776, 0.25113, 0.67158, 0.25772, 0.68724, 0.26439, 0.70408, 0.27097, 0.72789, 0.27958, 0.74707, 0.28561, 0.73737, 0.29295, 0.7054, 0.30285, 0.65294, 0.31424, 0.64419, 0.29933, 0.63099, 0.28714, 0.62121, 0.27724, 0.61198, 0.26945, 0.60779, 0.26045, 0.59288, 0.26385, 0.57831, 0.27233, 0.56926, 0.28513, 0.56239, 0.28654, 0.55812, 0.29782, 0.56173, 0.30634, 0.57704, 0.31448, 0.60086, 0.32412, 0.63137, 0.32876, 0.65182, 0.33167, 0.6956, 0.3372, 0.71365, 0.33948, 0.74975, 0.33647, 0.76319, 0.33385, 0.75386, 0.32647, 0.75314, 0.31974, 0.80557, 0.2952, 0.83191, 0.29198, 0.87439, 0.29268, 0.92048, 0.29764, 0.95457, 0.30471, 0.98072, 0.31393, 0.99603, 0.32441, 0.99866, 0.33344, 0.90799, 0.35785, 0.87609, 0.3575, 0.83646, 0.35285, 0.81716, 0.35668, 0.82299, 0.36199, 0.77634, 0.37216, 0.74714, 0.36704, 0.72972, 0.36884, 0.71032, 0.3684, 0.68894, 0.36623, 0.70202, 0.37812, 0.70734, 0.39576, 0.71834, 0.41713, 0.72877, 0.44298, 0.72879, 0.4669, 0.72621, 0.48209, 0.38254, 0.47755, 0.38467, 0.46566, 0.39696, 0.46232, 0.39807, 0.46076, 0.37685, 0.46195, 0.35641, 0.46085, 0.3573, 0.46631, 0.37142, 0.46947, 0.37805, 0.48598, 0.37097, 0.48842, 0.38059, 0.51218, 0.39129, 0.56445, 0.41095, 0.60883, 0.41726, 0.64465, 0.41718, 0.66163, 0.41599, 0.67512, 0.40537, 0.68922, 0.38795, 0.70011, 0.36224, 0.71167, 0.3536, 0.72037, 0.34999, 0.73476, 0.34095, 0.75222, 0.26042, 0.81181, 0.2419, 0.83449, 0.23898, 0.84536, 0.23901, 0.85307, 0.25207, 0.86439, 0.24336, 0.87296, 0.25471, 0.88, 0.24868, 0.89423, 0.24703, 0.90919, 0.24869, 0.92269, 0.27049, 0.95666, 0.26342, 0.97174, 0.22906, 0.99178, 0.20459, 1, 0.18901, 1, 0.16114, 0.99848, 0.11536, 0.98004, 0.09156, 0.96262, 0.08708, 0.9483, 0.12153, 0.91082, 0.12782, 0.88823, 0.13532, 0.87998, 0.12966, 0.87257, 0.13956, 0.86296, 0.1413, 0.85474, 0.14075, 0.84603, 0.13884, 0.8347, 0.12879, 0.81168, 0.1073, 0.76766, 0.10628, 0.7408, 0.1291, 0.71884, 0.17057, 0.70284, 0.20036, 0.6922, 0.21556, 0.68355, 0.21976, 0.67569, 0.21965, 0.66259, 0.21342, 0.65306, 0.17815, 0.62386, 0.08228, 0.55299, 0.05284, 0.52191, 0.0419, 0.49051, 0.03453, 0.48706, 0.03139, 0.47179, 0.04425, 0.4673, 0.04603, 0.44671, 0.05349, 0.42561, 0.06332, 0.40386, 0.0763, 0.38706, 0.12025, 0.35738, 0.14998, 0.33996, 0.18022, 0.32447, 0.20859, 0.31063, 0.20784, 0.29888, 0.20277, 0.28807, 0.19125, 0.28693, 0.18288, 0.27257, 0.16798, 0.26233, 0.13407, 0.25641, 0.11268, 0.2479, 0.10831, 0.25515, 0.10114, 0.26502, 0.09334, 0.28062, 0.07779, 0.2915, 0.05953, 0.3038, 0.04774, 0.31407, 0.03625, 0.32305, 0.00447, 0.31536, 0.00123, 0.31058, 0.00727, 0.30642, 0.02288, 0.29592, 0.0422, 0.28449, 0.06116, 0.27326, 0.07736, 0.26207, 0.08956, 0.25097, 0.08961, 0.24353, 0.07702, 0.22992, 0.07418, 0.21587, 0.08982, 0.1988, 0.1063, 0.19043, 0.09722, 0.17456, 0.08906, 0.15906, 0.0826, 0.15104, 0.07941, 0.14267, 0.063, 0.13911, 0.0775, 0.13406, 0.14358, 0.13563, 0.20118, 0.13518, 0.2449, 0.13148, 0.20287, 0.11763, 0.25225, 0.10984, 0.22995, 0.10721, 0.24674, 0.10595, 0.25811, 0.10405, 0.26603, 0.09806, 0.25123, 0.10482, 0.21893, 0.09741, 0.19675, 0.09622, 0.17669, 0.09032, 0.16342, 0.08216, 0.16246, 0.07173, 0.1724, 0.0627, 0.19559, 0.05288, 0.21509, 0.04479, 0.2328, 0.0356, 0.24905, 0.02521, 0.26345, 0.01455, 0.28362, 0.00595, 0.30794, 0.00274, 0.34733, 0.00046, 0.4229, 0.00058, 0.70148, 0.29043, 0.67735, 0.28009, 0.23009, 0.26431, 0.28835, 0.2596, 0.35459, 0.25017, 0.46758, 0.26297, 0.39025, 0.25039, 0.53577, 0.26771, 0.77164, 0.3631, 0.73357, 0.34665, 0.80756, 0.3483, 0.78173, 0.33949, 0.76892, 0.35335, 0.74939, 0.34489, 0.74566, 0.35661, 0.71192, 0.35297, 0.8242, 0.34373, 0.79875, 0.33651, 0.88721, 0.34141, 0.79907, 0.32317, 0.8294, 0.32589, 0.86285, 0.33316, 0.56439, 0.32103, 0.50146, 0.33648, 0.50385, 0.35771, 0.54631, 0.36856, 0.63211, 0.35894, 0.64765, 0.35776, 0.66995, 0.36343, 0.58516, 0.37123, 0.32622, 0.07594, 0.49948, 0.07597, 0.3428, 0.1223, 0.34289, 0.10888, 0.33135, 0.08671, 0.33685, 0.09709, 0.48582, 0.11637, 0.48728, 0.1048, 0.49119, 0.09549, 0.49558, 0.08566, 0.29985, 0.12692, 0.51282, 0.12275, 0.34384, 0.11648, 0.48556, 0.11169, 0.33704, 0.12616, 0.41203, 0.16141, 0.48936, 0.15796, 0.37651, 0.16058, 0.29813, 0.15851, 0.17528, 0.17176, 0.27368, 0.16988, 0.34732, 0.18168, 0.38074, 0.20036, 0.43002, 0.18226, 0.51433, 0.17067, 0.60337, 0.17237, 0.66322, 0.1876, 0.21991, 0.18657, 0.2898, 0.19279, 0.32016, 0.21069, 0.31496, 0.23262, 0.28084, 0.2489, 0.37077, 0.22582, 0.21483, 0.25651, 0.14266, 0.2496, 0.11224, 0.20946, 0.14787, 0.19381, 0.10558, 0.22967, 0.18979, 0.22758, 0.55391, 0.23125, 0.17394, 0.20704, 0.23487, 0.20713, 0.13627, 0.21792, 0.13357, 0.23464, 0.16996, 0.24577, 0.22905, 0.24608, 0.2624, 0.23544, 0.26503, 0.21853, 0.54472, 0.18966, 0.46501, 0.19496, 0.42674, 0.21338, 0.42301, 0.23301, 0.4596, 0.25042, 0.52865, 0.25871, 0.60306, 0.25372, 0.64776, 0.23917, 0.65521, 0.21594, 0.62274, 0.19571, 0.56972, 0.20871, 0.51012, 0.21004, 0.47883, 0.22305, 0.48107, 0.23871, 0.52279, 0.24974, 0.5794, 0.2482, 0.61069, 0.23606, 0.60995, 0.2193, 0.579, 0.15308, 0.65543, 0.15469, 0.20229, 0.15307, 0.13282, 0.15352, 0.29985, 0.14747, 0.27151, 0.14287, 0.49872, 0.14665, 0.37118, 0.14682, 0.3697, 0.13567, 0.37118, 0.12364, 0.37266, 0.1103, 0.3734, 0.0987, 0.37414, 0.08755, 0.37192, 0.07595, 0.4251, 0.14704, 0.4288, 0.13282, 0.43175, 0.12167, 0.43766, 0.11052, 0.44136, 0.09914, 0.44505, 0.08733, 0.44874, 0.07596, 0.49779, 0.1142, 0.48018, 0.13175, 0.3223, 0.13252, 0.30535, 0.10176, 0.52878, 0.14194, 0.66036, 0.17285, 0.12907, 0.17107, 0.28157, 0.09144, 0.26889, 0.07956, 0.26769, 0.06705, 0.27928, 0.04359, 0.29114, 0.03193, 0.32503, 0.01638, 0.54982, 0.0922, 0.56116, 0.08118, 0.56137, 0.06702, 0.54866, 0.05443, 0.53683, 0.03732, 0.54068, 0.01994, 0.43563, 0.01511, 0.46946, 0.01464, 0.4917, 0.01917, 0.50855, 0.02382, 0.51996, 0.0312, 0.54586, 0.0459, 0.56129, 0.04933, 0.61945, 0.08156, 0.61442, 0.06589, 0.59848, 0.05304, 0.58023, 0.0412, 0.56661, 0.03057, 0.51034, 0.01104, 0.45254, 0.01488, 0.47452, 0.0094, 0.43092, 0.01318, 0.38551, 0.01403, 0.30822, 0.02164, 0.34328, 0.0144, 0.27688, 0.05353, 0.21652, 0.08784, 0.20977, 0.07601, 0.21859, 0.06464, 0.23312, 0.05434, 0.24973, 0.04491, 0.26472, 0.28058, 0.27683, 0.29784, 0.28137, 0.31352, 0.2791, 0.32696, 0.25337, 0.34758, 0.26851, 0.37446, 0.49386, 0.37207, 0.48554, 0.32487, 0.48024, 0.30986, 0.48175, 0.29529, 0.48781, 0.28163, 0.3371, 0.27846, 0.34158, 0.29614, 0.34158, 0.31227, 0.3371, 0.32994, 0.32666, 0.35093, 0.33934, 0.37634, 0.4162, 0.28001, 0.41098, 0.29548, 0.41247, 0.31205, 0.41545, 0.3284, 0.43187, 0.34961, 0.42292, 0.37612, 0.18129, 0.36759, 0.20517, 0.34484, 0.23278, 0.32628, 0.24546, 0.31125, 0.24621, 0.29689, 0.23502, 0.28253, 0.52591, 0.28297, 0.52218, 0.29601, 0.52293, 0.30794, 0.53188, 0.32009, 0.14208, 0.40045, 0.21992, 0.41859, 0.27298, 0.43197, 0.32438, 0.44826, 0.28361, 0.3921, 0.31172, 0.41471, 0.37777, 0.43387, 0.4499, 0.40766, 0.53543, 0.3997, 0.19356, 0.39793, 0.37901, 0.44959, 0.13066, 0.42289, 0.12321, 0.43806, 0.12414, 0.45488, 0.21708, 0.45161, 0.20748, 0.43593, 0.29805, 0.456, 0.13104, 0.47846, 0.14955, 0.51607, 0.30233, 0.47952, 0.31234, 0.51386, 0.28551, 0.44301, 0.22027, 0.4762, 0.18431, 0.55628, 0.25346, 0.61231, 0.32212, 0.55502, 0.35632, 0.60931, 0.38311, 0.66062, 0.38184, 0.67527, 0.37359, 0.68843, 0.35644, 0.70009, 0.33232, 0.71287, 0.31898, 0.72677, 0.30311, 0.65963, 0.30057, 0.67203, 0.29549, 0.68463, 0.28787, 0.69666, 0.27454, 0.70888, 0.25549, 0.72223, 0.20422, 0.74607, 0.20595, 0.81257, 0.28934, 0.74907, 0.29931, 0.64748, 0.37855, 0.64661, 0.53811, 0.06672, 0.28748, 0.06673, 0.26627, 0.0496, 0.42972, 0.4484, 0.47794, 0.4294, 0.52861, 0.41196, 0.62096, 0.39251], "triangles": [114, 130, 131, 115, 130, 114, 115, 116, 130, 113, 114, 132, 114, 131, 132, 116, 129, 130, 116, 128, 129, 126, 119, 125, 117, 128, 116, 128, 117, 118, 127, 118, 119, 119, 126, 127, 118, 127, 128, 120, 125, 119, 121, 125, 120, 124, 125, 121, 123, 124, 121, 122, 123, 121, 442, 448, 447, 142, 143, 448, 442, 441, 103, 449, 142, 448, 141, 142, 449, 443, 448, 442, 449, 448, 443, 104, 442, 103, 443, 442, 104, 450, 141, 449, 105, 443, 104, 444, 449, 443, 444, 443, 105, 450, 449, 444, 106, 444, 105, 450, 140, 141, 451, 140, 450, 139, 140, 451, 445, 450, 444, 445, 444, 106, 451, 450, 445, 107, 445, 106, 452, 139, 451, 138, 139, 452, 454, 451, 445, 454, 445, 107, 452, 451, 454, 108, 454, 107, 137, 138, 452, 137, 453, 136, 137, 452, 453, 452, 454, 453, 109, 454, 108, 453, 454, 109, 110, 453, 109, 135, 136, 453, 135, 453, 110, 111, 135, 110, 134, 135, 111, 134, 111, 112, 133, 134, 112, 113, 132, 112, 132, 133, 112, 460, 86, 87, 85, 460, 461, 463, 84, 85, 84, 463, 83, 463, 85, 462, 85, 461, 462, 86, 460, 85, 424, 413, 414, 154, 155, 424, 428, 424, 414, 428, 414, 415, 425, 154, 424, 425, 424, 428, 153, 154, 425, 434, 427, 428, 434, 428, 415, 427, 426, 425, 427, 425, 428, 153, 425, 426, 427, 434, 429, 152, 153, 426, 435, 427, 429, 426, 427, 435, 430, 426, 435, 152, 426, 430, 93, 432, 429, 435, 429, 432, 150, 151, 152, 150, 152, 430, 94, 432, 93, 95, 432, 94, 95, 96, 432, 149, 150, 430, 433, 432, 96, 433, 96, 97, 431, 430, 435, 149, 430, 431, 148, 149, 431, 147, 148, 431, 438, 433, 97, 431, 433, 436, 435, 433, 431, 432, 433, 435, 436, 433, 438, 147, 431, 436, 438, 97, 98, 439, 438, 98, 439, 98, 99, 437, 436, 438, 437, 438, 439, 146, 147, 436, 146, 436, 437, 456, 439, 99, 456, 99, 100, 455, 437, 439, 455, 439, 456, 145, 146, 437, 455, 145, 437, 446, 455, 456, 145, 455, 446, 440, 456, 100, 446, 456, 440, 101, 440, 100, 144, 145, 446, 447, 144, 446, 101, 441, 440, 440, 447, 446, 102, 441, 101, 441, 447, 440, 143, 144, 447, 448, 143, 447, 441, 442, 447, 103, 441, 102, 462, 421, 463, 247, 246, 234, 81, 247, 80, 245, 244, 243, 248, 244, 245, 386, 243, 244, 247, 463, 248, 245, 246, 247, 247, 248, 245, 81, 463, 247, 421, 386, 244, 421, 244, 248, 421, 248, 463, 80, 247, 234, 463, 81, 82, 463, 82, 83, 238, 62, 63, 239, 63, 64, 239, 64, 65, 238, 63, 239, 61, 62, 238, 66, 240, 239, 66, 239, 65, 240, 66, 67, 240, 67, 68, 237, 240, 68, 69, 237, 68, 60, 61, 238, 236, 60, 238, 236, 238, 239, 230, 60, 236, 70, 237, 69, 235, 236, 239, 235, 239, 240, 235, 240, 237, 59, 60, 230, 229, 236, 235, 230, 236, 229, 73, 235, 237, 229, 235, 73, 230, 232, 59, 231, 230, 229, 74, 229, 73, 231, 229, 74, 72, 73, 237, 71, 237, 70, 72, 237, 71, 227, 231, 74, 227, 74, 75, 59, 228, 58, 232, 228, 59, 234, 57, 58, 234, 58, 228, 233, 228, 232, 233, 232, 231, 234, 228, 233, 246, 56, 57, 246, 57, 234, 233, 231, 227, 77, 233, 227, 79, 234, 233, 78, 79, 233, 77, 78, 233, 76, 227, 75, 77, 227, 76, 231, 232, 230, 79, 80, 234, 241, 412, 53, 241, 53, 54, 242, 387, 412, 242, 412, 241, 401, 242, 243, 56, 245, 55, 246, 245, 56, 245, 243, 242, 54, 242, 241, 54, 245, 242, 245, 54, 55, 386, 401, 243, 384, 405, 383, 424, 155, 413, 155, 156, 413, 404, 159, 405, 158, 159, 404, 404, 405, 384, 404, 157, 158, 403, 157, 404, 156, 157, 403, 403, 385, 422, 384, 403, 404, 385, 403, 384, 422, 385, 417, 413, 156, 403, 422, 413, 403, 385, 384, 395, 384, 383, 395, 413, 422, 414, 402, 401, 386, 401, 395, 394, 402, 395, 401, 396, 385, 395, 402, 396, 395, 417, 385, 396, 417, 396, 402, 420, 402, 386, 420, 386, 421, 418, 417, 402, 420, 418, 402, 414, 417, 418, 461, 420, 462, 419, 418, 420, 419, 420, 461, 415, 418, 419, 416, 434, 415, 419, 416, 415, 460, 419, 461, 423, 419, 460, 416, 419, 423, 429, 434, 416, 90, 423, 460, 92, 416, 423, 91, 92, 423, 429, 416, 92, 90, 91, 423, 93, 429, 92, 89, 90, 460, 460, 87, 89, 87, 88, 89, 462, 420, 421, 415, 414, 418, 414, 422, 417, 405, 160, 406, 381, 380, 391, 407, 408, 380, 381, 407, 380, 162, 408, 407, 161, 162, 407, 406, 160, 161, 407, 406, 161, 406, 407, 381, 382, 406, 381, 393, 382, 381, 383, 406, 382, 405, 406, 383, 394, 382, 393, 383, 382, 394, 395, 383, 394, 391, 392, 381, 393, 381, 392, 50, 409, 49, 389, 397, 390, 397, 392, 391, 398, 397, 389, 410, 390, 409, 410, 409, 50, 389, 390, 410, 398, 392, 397, 51, 410, 50, 411, 410, 51, 411, 51, 52, 388, 398, 389, 411, 388, 389, 411, 389, 410, 399, 398, 388, 392, 398, 399, 393, 392, 399, 412, 387, 388, 412, 388, 411, 399, 388, 387, 400, 399, 387, 242, 401, 400, 242, 400, 387, 393, 399, 400, 394, 393, 400, 401, 394, 400, 412, 411, 52, 412, 52, 53, 159, 160, 405, 220, 36, 37, 43, 44, 220, 219, 220, 37, 219, 37, 38, 39, 219, 38, 219, 42, 43, 219, 43, 220, 40, 219, 39, 42, 219, 40, 41, 42, 40, 45, 46, 34, 220, 35, 36, 35, 44, 45, 35, 45, 34, 220, 44, 35, 172, 178, 171, 177, 178, 172, 173, 177, 172, 176, 177, 173, 175, 176, 173, 174, 175, 173, 170, 180, 169, 179, 180, 170, 171, 179, 170, 178, 179, 171, 169, 180, 181, 169, 181, 168, 181, 182, 168, 17, 15, 16, 260, 14, 18, 259, 198, 339, 20, 18, 19, 337, 255, 260, 330, 331, 337, 323, 263, 324, 338, 259, 263, 321, 337, 260, 163, 164, 408, 164, 221, 408, 49, 409, 48, 409, 226, 48, 274, 341, 275, 29, 275, 28, 306, 274, 275, 29, 30, 275, 30, 306, 275, 305, 30, 31, 303, 304, 33, 32, 305, 31, 32, 304, 305, 187, 342, 268, 285, 187, 268, 285, 186, 187, 284, 186, 285, 285, 268, 276, 183, 184, 286, 284, 291, 286, 284, 285, 289, 291, 284, 289, 320, 195, 196, 294, 295, 280, 391, 222, 223, 295, 294, 287, 291, 289, 287, 294, 293, 287, 282, 294, 280, 292, 291, 287, 168, 182, 167, 293, 283, 292, 283, 166, 167, 286, 292, 283, 165, 283, 282, 166, 283, 165, 292, 286, 291, 286, 167, 183, 165, 282, 221, 164, 165, 221, 380, 221, 222, 286, 283, 167, 182, 183, 167, 283, 293, 282, 221, 282, 280, 282, 293, 294, 222, 221, 280, 293, 292, 287, 289, 285, 276, 46, 303, 33, 303, 313, 304, 47, 302, 303, 303, 312, 313, 302, 312, 303, 304, 313, 314, 305, 304, 314, 288, 314, 313, 226, 302, 47, 302, 311, 312, 288, 307, 314, 196, 197, 198, 196, 198, 259, 338, 263, 323, 320, 196, 259, 319, 320, 259, 340, 321, 260, 329, 322, 323, 330, 329, 323, 338, 319, 259, 340, 20, 315, 337, 329, 330, 265, 321, 340, 267, 320, 319, 264, 266, 322, 337, 321, 329, 265, 329, 321, 338, 323, 322, 319, 338, 322, 267, 319, 322, 267, 322, 266, 329, 264, 322, 264, 329, 265, 267, 317, 320, 269, 317, 267, 315, 273, 265, 315, 265, 340, 273, 315, 274, 270, 267, 266, 269, 267, 270, 272, 270, 266, 272, 264, 265, 272, 265, 273, 272, 266, 264, 276, 268, 269, 297, 273, 274, 277, 269, 270, 276, 269, 277, 298, 272, 273, 298, 273, 297, 297, 274, 306, 271, 270, 272, 271, 272, 298, 277, 270, 271, 290, 276, 277, 289, 276, 290, 307, 297, 306, 308, 298, 297, 308, 297, 307, 278, 277, 271, 290, 277, 278, 299, 271, 298, 299, 298, 308, 296, 290, 278, 309, 299, 308, 281, 278, 271, 281, 271, 299, 287, 289, 290, 287, 290, 296, 288, 308, 307, 309, 308, 288, 279, 296, 278, 279, 278, 281, 300, 281, 299, 300, 299, 309, 295, 287, 296, 295, 296, 279, 310, 309, 288, 300, 309, 310, 280, 295, 279, 311, 310, 288, 223, 279, 281, 225, 223, 281, 280, 279, 223, 300, 225, 281, 301, 300, 310, 301, 310, 311, 225, 300, 301, 301, 311, 302, 222, 280, 223, 224, 301, 302, 225, 301, 224, 224, 302, 226, 397, 225, 224, 391, 223, 225, 390, 224, 226, 314, 307, 306, 311, 288, 312, 48, 226, 47, 312, 288, 313, 46, 47, 303, 284, 286, 185, 184, 185, 286, 185, 186, 284, 268, 317, 269, 305, 314, 306, 305, 306, 30, 33, 304, 32, 275, 341, 28, 46, 33, 34, 391, 225, 397, 380, 222, 391, 409, 390, 226, 397, 224, 390, 408, 221, 380, 162, 163, 408, 340, 260, 20, 323, 324, 330, 20, 260, 18, 251, 263, 259, 337, 331, 255, 18, 15, 17, 14, 260, 336, 255, 336, 260, 18, 14, 15, 315, 20, 21, 25, 22, 23, 25, 23, 24, 26, 22, 25, 316, 21, 22, 316, 22, 26, 315, 21, 316, 27, 316, 26, 274, 315, 316, 341, 316, 27, 274, 316, 341, 28, 341, 27, 187, 188, 342, 342, 318, 268, 191, 193, 194, 192, 193, 191, 318, 190, 191, 194, 195, 317, 194, 318, 191, 318, 194, 317, 189, 190, 318, 342, 189, 318, 268, 318, 317, 188, 189, 342, 320, 317, 195, 202, 203, 204, 205, 375, 204, 202, 204, 375, 207, 208, 376, 376, 344, 375, 207, 376, 375, 206, 207, 375, 205, 206, 375, 377, 210, 378, 209, 210, 377, 376, 209, 377, 208, 209, 376, 345, 376, 377, 379, 211, 212, 378, 211, 379, 210, 211, 378, 350, 363, 362, 9, 362, 8, 10, 362, 9, 11, 362, 10, 11, 350, 362, 363, 6, 7, 363, 7, 8, 362, 363, 8, 364, 5, 6, 343, 344, 253, 327, 328, 334, 349, 258, 350, 257, 334, 258, 257, 258, 349, 254, 253, 327, 343, 253, 254, 349, 350, 11, 343, 202, 375, 326, 254, 327, 333, 327, 334, 333, 334, 257, 326, 327, 333, 12, 349, 11, 13, 257, 349, 13, 349, 12, 339, 343, 254, 202, 343, 339, 256, 333, 257, 256, 257, 13, 252, 254, 326, 339, 254, 252, 198, 200, 201, 325, 252, 326, 332, 325, 326, 333, 332, 326, 332, 333, 256, 262, 332, 256, 336, 256, 13, 262, 256, 336, 255, 262, 336, 261, 252, 325, 331, 325, 332, 259, 339, 261, 261, 339, 252, 14, 336, 13, 324, 261, 325, 324, 325, 331, 251, 261, 324, 261, 251, 259, 339, 201, 202, 339, 198, 201, 332, 255, 331, 255, 332, 262, 330, 324, 331, 324, 263, 251, 199, 200, 198, 253, 328, 327, 258, 250, 350, 334, 335, 258, 363, 364, 6, 363, 350, 351, 369, 218, 0, 367, 0, 1, 369, 0, 367, 370, 218, 369, 371, 217, 218, 371, 218, 370, 373, 216, 217, 373, 217, 371, 369, 368, 370, 356, 368, 369, 355, 370, 368, 348, 216, 373, 215, 216, 348, 372, 214, 215, 357, 369, 367, 356, 369, 357, 354, 1, 2, 367, 1, 354, 358, 357, 367, 348, 372, 215, 354, 358, 367, 213, 214, 372, 3, 354, 2, 366, 354, 3, 359, 358, 354, 359, 354, 366, 347, 213, 372, 212, 213, 347, 353, 359, 366, 366, 3, 4, 365, 366, 4, 353, 366, 365, 346, 212, 347, 346, 379, 212, 360, 353, 365, 361, 360, 365, 459, 379, 346, 5, 364, 365, 5, 365, 4, 361, 365, 364, 374, 459, 346, 378, 379, 459, 352, 360, 361, 353, 360, 359, 352, 335, 360, 346, 249, 374, 458, 374, 249, 371, 370, 355, 364, 352, 361, 351, 352, 364, 457, 352, 351, 374, 378, 459, 345, 374, 458, 345, 378, 374, 377, 378, 345, 372, 348, 373, 373, 371, 372, 371, 347, 372, 346, 347, 328, 328, 347, 371, 346, 328, 249, 355, 328, 371, 368, 356, 357, 355, 368, 357, 355, 335, 328, 357, 359, 355, 355, 359, 335, 358, 359, 357, 360, 335, 359, 335, 352, 250, 457, 250, 352, 344, 345, 458, 344, 458, 249, 350, 457, 351, 250, 457, 350, 258, 335, 250, 253, 249, 328, 344, 249, 253, 334, 328, 335, 344, 376, 345, 351, 364, 363, 375, 344, 343], "vertices": [2, 6, 145.08, -35.2, 0.97509, 69, -242.6, 31.52, 0.02491, 2, 6, 139.31, -60.05, 0.96955, 69, -248.36, 6.68, 0.03045, 2, 6, 125.85, -76.38, 0.98206, 69, -261.82, -9.65, 0.01794, 4, 6, 110.13, -92.4, 0.99608, 46, -30.45, 14.64, 0, 4, 447.78, -83.33, 0, 70, -216.94, -25.39, 0.00392, 4, 6, 83.63, -100.77, 0.60198, 45, 21.11, 11.04, 0.2852, 46, -2.77, 12.23, 0.11054, 70, -243.44, -33.76, 0.00229, 5, 6, 73.03, -107.29, 0.45606, 45, 32.42, 16.23, 0.03695, 46, 9.51, 14.2, 0.50287, 47, -27.99, 8.45, 0.00224, 70, -254.03, -40.27, 0.00188, 5, 6, 58.08, -119.2, 0.2327, 46, 27.88, 19.49, 0.57502, 47, -11.16, 17.52, 0.1919, 4, 397.13, -112.68, 0, 70, -268.98, -52.19, 0.00039, 5, 6, 40.54, -129.09, 0.08857, 46, 47.87, 21.91, 0.17397, 47, 7.86, 24.13, 0.72875, 48, -32.22, 11.8, 0.00871, 4, 380.1, -123.42, 0, 4, 46, 65.96, 19.1, 0.01819, 47, 26.13, 25.22, 0.79531, 48, -16.05, 20.39, 0.1865, 4, 362.55, -128.62, 0, 3, 47, 44.88, 20.24, 0.32855, 48, 3.07, 23.65, 0.67144, 4, 343.16, -128.02, 1e-05, 4, 47, 60.12, 6.24, 0.00413, 48, 22.75, 17.26, 0.97797, 49, -11.95, 12.96, 0.01787, 4, 325.15, -117.84, 2e-05, 5, 6, -21.39, -95.85, 0.02929, 5, 67.23, -96.05, 0.26036, 46, 92.37, -32.5, 0.00748, 49, 10.92, 0.61, 0.65878, 4, 316.6, -93.3, 0.0441, 5, 6, -28.37, -80, 0.08118, 5, 60.09, -80.28, 0.53636, 47, 66.95, -36.43, 0.02508, 49, 27.09, -5.57, 0.22966, 4, 308.83, -77.82, 0.12772, 2, 5, 59.06, -69.74, 0.70832, 4, 307.38, -67.33, 0.29168, 3, 5, 50.51, -77.65, 0.4756, 16, 71.82, 78.89, 0.02682, 4, 299.15, -75.58, 0.49758, 4, 5, 50.7, -90.84, 0.33691, 47, 78.16, -27.82, 0.00019, 16, 72.54, 65.73, 0.0791, 4, 299.87, -88.75, 0.5838, 5, 5, 59, -102.28, 0.38427, 46, 102.38, -29.99, 0.0001, 47, 72.14, -15.02, 0.00054, 16, 81.29, 54.62, 0.06273, 4, 308.62, -99.85, 0.55236, 5, 5, 46.81, -97.24, 0.23144, 46, 111.63, -39.4, 4e-05, 47, 83.18, -22.26, 0.00031, 16, 68.91, 59.17, 0.14291, 4, 296.24, -95.3, 0.6253, 3, 5, 41.75, -86.69, 0.21209, 16, 63.43, 69.51, 0.12638, 4, 290.76, -84.96, 0.66152, 2, 16, 51.51, 55.1, 0.28422, 4, 278.84, -99.37, 0.71578, 2, 16, 26.79, 75.7, 0.42882, 4, 254.12, -78.78, 0.57118, 2, 16, 19.89, 52, 0.69872, 4, 247.22, -102.47, 0.30128, 2, 16, 20.91, 12.93, 0.9518, 4, 248.24, -141.55, 0.0482, 2, 16, 25.94, -20.22, 0.99798, 4, 253.27, -174.7, 0.00202, 2, 16, 16.08, -21.98, 0.96668, 4, 243.41, -176.45, 0.03332, 1, 16, 10.41, -12.86, 1, 3, 16, 0.02, -6.15, 0.9692, 4, 227.35, -160.62, 0.02558, 71, -272.78, 35.04, 0.00522, 3, 16, -21.04, -1.43, 0.76181, 4, 206.29, -155.9, 0.22769, 71, -278.63, 14.26, 0.01051, 3, 16, -48.46, -0.37, 0.54897, 4, 178.87, -154.84, 0.43965, 71, -281.18, -13.06, 0.01138, 3, 16, -95.82, -2.64, 0.1531, 4, 131.51, -157.11, 0.6969, 25, 31.46, -80.47, 0.15, 4, 16, -109.5, -0.1, 0.04385, 4, 117.84, -154.58, 0.74685, 25, 17.78, -77.94, 0.19768, 71, -284.76, -73.99, 0.01162, 3, 4, 95.87, -156.35, 0.78897, 25, -4.18, -79.71, 0.19724, 71, -284.18, -96.01, 0.01379, 3, 4, 69.43, -151.96, 0.78768, 25, -30.62, -75.32, 0.19692, 71, -290, -122.18, 0.0154, 3, 4, 43.19, -141.16, 0.79139, 25, -56.86, -64.52, 0.19785, 71, -302.2, -147.8, 0.01076, 2, 31, 13.6, 17.58, 0.99172, 71, -294.88, -159.59, 0.00828, 3, 31, 27.8, 20.71, 0.93891, 32, -11.9, 22.02, 0.0532, 71, -286.58, -171.53, 0.00789, 3, 31, 42.11, 24.47, 0.59665, 32, 2.71, 24.32, 0.39673, 71, -277.65, -183.32, 0.00663, 3, 31, 61.15, 30.28, 0.17416, 32, 22.24, 28.19, 0.82154, 71, -265.03, -198.72, 0.0043, 2, 32, 36.57, 32.05, 0.99789, 71, -254.87, -209.53, 0.00211, 2, 32, 45.72, 21.33, 0.99343, 71, -260.01, -222.66, 0.00657, 2, 32, 53.35, -1.98, 0.9897, 71, -276.95, -240.38, 0.0103, 2, 32, 58.2, -36.11, 0.99183, 71, -304.76, -260.76, 0.00817, 2, 32, 32.49, -27.61, 0.99324, 71, -309.4, -234.08, 0.00676, 3, 31, 54.11, -22.36, 0.37678, 32, 9.95, -23.48, 0.61698, 71, -316.39, -212.25, 0.00624, 5, 4, -4.53, -124.36, 0.12343, 31, 35.74, -20.41, 0.67705, 32, -8.13, -19.69, 0.16866, 25, -104.58, -47.72, 0.02558, 71, -321.58, -194.53, 0.00528, 4, 4, 9.13, -118.72, 0.38643, 31, 20.99, -19.62, 0.51183, 25, -90.93, -42.08, 0.09665, 71, -326.46, -180.59, 0.00509, 3, 4, 25.09, -115.63, 0.79513, 25, -74.97, -38.99, 0.19878, 71, -328.68, -164.49, 0.00608, 3, 4, 18.58, -108.07, 0.79677, 25, -81.47, -31.43, 0.19919, 71, -336.59, -170.57, 0.00404, 2, 4, 3.01, -101.18, 0.83018, 3, 103.69, -101.16, 0.16982, 2, 4, -20.14, -97.64, 0.47943, 3, 80.82, -96.09, 0.52057, 2, 4, -22.84, -94.14, 0.42292, 3, 78.35, -92.42, 0.57708, 3, 4, -43.14, -92.98, 0.14768, 3, 58.18, -89.92, 0.76439, 20, 55.43, 90.54, 0.08793, 3, 4, -58.26, -95.71, 0.04822, 3, 42.91, -91.65, 0.69609, 20, 51.71, 75.63, 0.25569, 3, 4, -72.37, -104.61, 0.01332, 3, 28.24, -99.59, 0.53567, 20, 54.03, 59.1, 0.45101, 4, 4, -88.91, -118.15, 0.00153, 32, 60.78, -68.79, 0, 3, 10.84, -112.01, 0.28259, 20, 59.58, 38.46, 0.71588, 4, 32, 75.73, -58.45, 0, 3, 2.36, -128.08, 0.08387, 20, 71.67, 24.89, 0.76868, 21, -14.5, 20.25, 0.14745, 4, 32, 85.44, -51.36, 0, 3, -2.99, -138.86, 0.01579, 20, 79.9, 16.11, 0.50196, 21, -2.79, 17.51, 0.48225, 2, 32, 105.11, -35.57, 0.00088, 21, 22.01, 12.89, 0.99912, 3, 32, 113.22, -29.05, 0, 21, 32.24, 10.98, 0.99436, 19, 218.98, -34.39, 0.00564, 2, 21, 49.74, 20.4, 0.53981, 19, 202.9, -22.69, 0.46019, 2, 21, 55.68, 26.51, 0.17535, 19, 194.81, -19.99, 0.82465, 2, 21, 47.98, 38.34, 0.02598, 19, 187.48, -32.05, 0.97402, 2, 21, 44.99, 50.02, 0.00237, 19, 178.24, -39.8, 0.99763, 1, 19, 126.49, -45.15, 1, 1, 19, 113.31, -37.74, 1, 1, 19, 100.36, -19.28, 1, 2, 32, 98.91, 102.93, 0, 19, 92.22, 5.41, 1, 2, 32, 118.6, 112.89, 0, 19, 90.99, 27.44, 1, 2, 32, 139.68, 117.33, 0, 19, 95.37, 48.53, 1, 2, 32, 160.05, 115.63, 0, 19, 105.11, 66.51, 1, 2, 32, 174.96, 109.24, 0, 19, 116.95, 77.61, 1, 2, 32, 190.81, 46.24, 0, 19, 181.07, 66.88, 1, 2, 32, 182.28, 31.64, 0, 19, 191.04, 53.21, 1, 3, 32, 165.04, 17.04, 0, 21, 100.97, 1.76, 0.0129, 19, 197.5, 31.56, 0.9871, 3, 32, 166.26, 4.79, 0, 21, 92.47, -7.16, 0.18982, 19, 209.23, 27.76, 0.81018, 2, 21, 97.56, -15.76, 0.30375, 19, 214.79, 36.07, 0.69625, 3, 21, 77.38, -38.9, 0.66482, 19, 244.41, 27.91, 0.32846, 8, 20.93, 115.32, 0.00672, 4, 20, 104.19, -61.16, 0.00093, 21, 60.28, -33.32, 0.71698, 19, 246.79, 10.08, 0.22262, 8, 12.16, 99.62, 0.05946, 4, 20, 94.42, -60.82, 0.0122, 21, 51.97, -38.46, 0.66934, 19, 255.03, 4.81, 0.11345, 8, 15.6, 90.47, 0.20501, 4, 20, 85.11, -56.39, 0.03658, 21, 41.76, -39.94, 0.62966, 19, 260.79, -3.74, 0.02087, 8, 15.08, 80.17, 0.31289, 2, 20, 75.94, -48.69, 0.14749, 8, 11.48, 68.75, 0.85251, 3, 20, 74.74, -71.02, 0.02415, 8, 32.57, 76.21, 0.80084, 12, -72.01, 47.43, 0.17501, 2, 8, 64.08, 79.81, 0.66564, 12, -41.19, 54.9, 0.33436, 3, 7, 126.01, 177.64, 0.00022, 8, 102.18, 86.6, 0.20396, 12, -4.22, 66.33, 0.79583, 4, 7, 172.12, 184.31, 0.01324, 21, 80.3, -168.12, 0.03102, 8, 148.28, 93.27, 0.02006, 12, 40.71, 78.64, 0.93567, 3, 32, 318.11, -129.58, 0, 7, 214.91, 185.4, 0.00246, 12, 83.05, 84.99, 0.99754, 3, 32, 341.44, -143.62, 0, 7, 242.14, 184.71, 0.00303, 12, 110.15, 87.67, 0.99697, 2, 7, 238.55, 2.41, 0.71447, 12, 129.08, -93.68, 0.28553, 2, 7, 217.25, 3.01, 0.75267, 12, 107.87, -95.71, 0.24733, 2, 7, 211.11, 9.38, 0.78983, 12, 101, -90.15, 0.21017, 2, 7, 208.29, 9.89, 0.82659, 12, 98.13, -89.98, 0.17341, 3, 7, 210.71, -1.3, 0.83635, 10, 94.68, 89.28, 0.09733, 12, 101.91, -100.8, 0.06632, 2, 7, 209.02, -12.18, 0.73742, 10, 90.86, 78.96, 0.26258, 2, 7, 218.76, -11.46, 0.52149, 10, 100.56, 77.72, 0.47851, 2, 7, 224.24, -3.84, 0.44171, 10, 107.44, 84.1, 0.55829, 2, 7, 253.69, 0.41, 0.24608, 10, 137.15, 82.4, 0.75392, 2, 7, 258.15, -3.23, 0.21882, 10, 140.79, 77.94, 0.78118, 2, 7, 300.55, 2.92, 0.05596, 10, 183.57, 75.54, 0.94404, 1, 10, 276.68, 64.78, 1, 1, 10, 356.73, 61.17, 1, 1, 10, 420.43, 53.27, 1, 2, 10, 450.35, 47.93, 0.9872, 11, -46.57, 32.13, 0.0128, 2, 10, 474.03, 43.09, 0.81948, 11, -22.9, 37.05, 0.18052, 2, 10, 497.89, 33.14, 0.36324, 11, 2.95, 37.35, 0.63676, 2, 10, 515.47, 20.64, 0.05375, 11, 24.03, 32.82, 0.94625, 1, 11, 47.29, 24.3, 1, 2, 10, 548, -3.61, 0, 11, 63.51, 23.41, 1, 2, 10, 573.03, -10, 0, 11, 89.02, 27.45, 1, 2, 10, 602.96, -20.17, 0, 11, 120.54, 29.94, 1, 1, 11, 234.16, 12.83, 1, 2, 10, 738.8, -97.57, 0, 11, 275.91, 12.56, 1, 2, 10, 757.68, -102.49, 0, 11, 295.2, 15.51, 1, 3, 10, 771.27, -104.89, 0, 11, 308.63, 18.69, 0.96808, 15, 260.33, -24.37, 0.03192, 3, 10, 792.43, -101.61, 0, 11, 326.77, 30.07, 0.59476, 15, 240.13, -31.47, 0.40524, 3, 10, 806.73, -108.84, 0, 11, 342.76, 29.09, 0.20894, 15, 224.75, -26.99, 0.79106, 3, 10, 820.21, -105.11, 0, 11, 353.66, 37.83, 0.05152, 15, 212.19, -33.12, 0.94848, 2, 10, 844.72, -112.71, 0, 15, 186.7, -30.14, 1, 2, 10, 870.94, -118.24, 0, 15, 159.91, -29.5, 1, 2, 10, 894.88, -121.6, 0, 15, 135.76, -30.58, 1, 2, 10, 956.77, -120.83, 0, 15, 75.06, -42.67, 1, 2, 10, 982.69, -129.24, 0, 15, 48.04, -39.15, 1, 2, 10, 1014.84, -153.43, 0, 15, 12, -21.26, 1, 2, 10, 1027.06, -168.77, 0, 15, -2.83, -8.41, 1, 2, 10, 1025.62, -176.9, 0, 15, -2.9, -0.16, 1, 1, 15, -0.3, 14.64, 1, 1, 15, 32.49, 39.19, 1, 1, 15, 63.56, 52.07, 1, 1, 15, 89.18, 54.67, 1, 1, 15, 156.42, 36.99, 1, 2, 11, 383.4, -24.26, 0.00022, 15, 196.88, 34.01, 0.99978, 2, 11, 368.11, -23.77, 0.03644, 15, 211.69, 30.16, 0.96356, 2, 11, 355.9, -29.73, 0.16718, 15, 224.91, 33.28, 0.83282, 2, 11, 337.94, -28.56, 0.5497, 15, 242.17, 28.18, 0.4503, 2, 11, 323.4, -31.04, 0.89378, 15, 256.89, 27.38, 0.10622, 2, 11, 308.3, -34.89, 0.99427, 15, 272.48, 27.81, 0.00573, 1, 11, 288.78, -40.52, 1, 1, 11, 249.9, -55.14, 1, 1, 11, 175.81, -84.27, 1, 1, 11, 129.12, -95.81, 1, 2, 10, 524.52, -120.29, 0.00012, 11, 88.09, -93.04, 0.99988, 2, 10, 500.17, -93.65, 0.0272, 11, 55.19, -78.21, 0.9728, 2, 10, 484.17, -74.78, 0.13585, 11, 33.03, -67.2, 0.86415, 2, 10, 470.33, -64.15, 0.33262, 11, 16.11, -62.9, 0.66738, 2, 10, 456.87, -59.5, 0.55179, 11, 1.91, -63.96, 0.44821, 2, 10, 433.77, -55.46, 0.83842, 11, -20.91, -69.39, 0.16158, 2, 10, 416.4, -55.74, 0.94525, 11, -36.75, -76.51, 0.05475, 1, 10, 361.67, -65.01, 1, 2, 32, 292.33, -504.41, 0, 10, 227.89, -92.9, 1, 2, 32, 235.91, -491.92, 0, 10, 170.38, -98.54, 1, 1, 10, 114.03, -94.44, 1, 1, 10, 107.26, -97.21, 1, 1, 10, 80.07, -94.08, 1, 1, 10, 73.34, -85.96, 1, 2, 9, 151.88, -88.93, 0.10598, 10, 37.21, -78.59, 0.89402, 2, 9, 114.03, -85.91, 0.26602, 10, 0.71, -68.11, 0.73398, 2, 9, 74.98, -81.68, 0.56008, 10, -36.72, -56.18, 0.43992, 2, 9, 44.75, -75.55, 0.8747, 10, -65.12, -44.16, 0.1253, 2, 3, -45.67, 143.4, 0.02848, 9, -8.95, -53.59, 0.97152, 3, 4, -130.21, 118.92, 0.00016, 3, -14.69, 127.27, 0.18217, 9, -40.5, -38.61, 0.81767, 3, 4, -101.64, 104.43, 0.00928, 3, 12.86, 110.92, 0.39625, 9, -68.64, -23.28, 0.59447, 3, 4, -76.09, 90.76, 0.06878, 3, 37.45, 95.59, 0.62929, 9, -93.77, -8.86, 0.30193, 3, 4, -55.11, 92.3, 0.23287, 3, 58.49, 95.74, 0.66782, 9, -114.78, -9.78, 0.09931, 2, 4, -35.93, 96.03, 0.47632, 3, 77.87, 98.19, 0.52368, 2, 4, -34.24, 102.23, 0.52566, 3, 79.98, 104.28, 0.47434, 2, 4, -8.82, 108.06, 0.77671, 3, 105.73, 108.41, 0.22329, 3, 4, 9.06, 116.94, 0.79424, 28, -84.61, 28.81, 0.19856, 71, -561.79, -167.85, 0.00721, 3, 4, 18.67, 135.47, 0.79192, 28, -75, 47.33, 0.19798, 71, -579.76, -157.25, 0.0101, 4, 4, 33.27, 147.61, 0.78577, 33, 4.2, 5.69, 0.00331, 28, -60.4, 59.48, 0.19727, 71, -591.09, -142.01, 0.01364, 5, 4, 20.18, 149.22, 0.30913, 33, 17.36, 6.63, 0.60059, 34, -28.9, 2.42, 0.00056, 28, -73.5, 61.09, 0.07916, 71, -593.41, -155, 0.01056, 3, 33, 35.41, 7.27, 0.62296, 34, -11.14, 5.7, 0.36654, 71, -597.21, -172.66, 0.01049, 2, 34, 16.24, 12.63, 0.98944, 71, -601.34, -200.6, 0.01056, 3, 34, 37.37, 12.51, 0.6057, 35, -8.66, 12.65, 0.38414, 71, -609.58, -220.06, 0.01016, 3, 34, 61.42, 12.04, 0.17557, 35, 15.37, 11.79, 0.81486, 71, -619.26, -242.07, 0.00957, 3, 34, 80.79, 13.34, 0.06546, 35, 34.76, 12.77, 0.92499, 71, -625.51, -260.46, 0.00955, 2, 35, 51.96, 13.06, 0.99058, 71, -631.6, -276.54, 0.00942, 2, 35, 45.38, -7.68, 0.99546, 71, -648.45, -262.76, 0.00454, 2, 35, 38.07, -12.44, 0.99854, 71, -650.16, -254.21, 0.00146, 3, 34, 76.4, -11.72, 0.06536, 35, 29.96, -12.21, 0.93198, 71, -646.96, -246.76, 0.00266, 3, 34, 55.88, -11.31, 0.21921, 35, 9.45, -11.47, 0.77557, 71, -638.69, -227.98, 0.00522, 3, 34, 33.05, -9.73, 0.70892, 35, -13.35, -9.52, 0.28351, 71, -628.45, -207.51, 0.00756, 3, 33, 54.91, -9.65, 0.26039, 34, 10.63, -8.18, 0.73055, 71, -618.4, -187.42, 0.00906, 3, 33, 33.39, -6.24, 0.61941, 34, -11.16, -7.96, 0.37058, 71, -609.81, -167.39, 0.01001, 4, 4, 27.11, 159.55, 0.2523, 33, 12.55, -4.84, 0.67528, 28, -66.57, 71.42, 0.06221, 71, -603.35, -147.52, 0.0102, 3, 4, 40.41, 160.25, 0.78897, 28, -53.26, 72.12, 0.19724, 71, -603.32, -134.19, 0.01379, 3, 4, 64.37, 168.23, 0.78602, 28, -29.3, 80.1, 0.1965, 71, -609.99, -109.84, 0.01748, 3, 4, 89.41, 171.1, 0.78743, 28, -4.27, 82.97, 0.19686, 71, -611.5, -84.68, 0.01571, 4, 17, -90.38, -2.41, 0.11228, 4, 120.37, 164.48, 0.67755, 28, 26.7, 76.35, 0.19746, 71, -603.21, -54.13, 0.01271, 4, 17, -74.95, -10.32, 0.22802, 4, 135.81, 156.58, 0.56474, 28, 42.13, 68.44, 0.19819, 71, -594.47, -39.14, 0.00906, 4, 17, -46.85, -3.97, 0.55669, 4, 163.9, 162.93, 0.384, 28, 70.23, 74.79, 0.04951, 71, -599.29, -10.74, 0.0098, 3, 17, -19.38, 1.86, 0.79665, 4, 191.37, 168.75, 0.19337, 71, -603.61, 17, 0.00999, 3, 17, -5.24, 6.05, 0.88445, 4, 205.52, 172.95, 0.1101, 71, -607.04, 31.36, 0.00545, 1, 17, 9.64, 8.56, 1, 1, 17, 15.53, 17.58, 1, 1, 17, 24.98, 10.4, 1, 2, 17, 24.07, -24.72, 0.92683, 4, 234.82, 142.18, 0.07317, 2, 17, 26.52, -55.16, 0.70453, 4, 237.28, 111.74, 0.29547, 2, 17, 34.4, -77.93, 0.39038, 4, 245.15, 88.96, 0.60962, 2, 17, 57.95, -54.35, 0.26487, 4, 268.7, 112.55, 0.73513, 3, 5, 41.9, 85.57, 0.24864, 17, 73.3, -79.72, 0.10996, 4, 284.05, 87.18, 0.6414, 3, 5, 46.44, 97.46, 0.45071, 17, 77.36, -67.66, 0.08286, 4, 288.11, 99.23, 0.46643, 3, 5, 48.81, 88.59, 0.69617, 17, 80.08, -76.43, 0.02572, 4, 290.83, 90.47, 0.27811, 3, 5, 52.3, 82.62, 0.82745, 55, 32.29, 5.9, 0.09683, 4, 294.56, 84.64, 0.07572, 4, 6, -23.82, 78.81, 0.13265, 5, 63.09, 78.58, 0.64052, 55, 26.68, 15.96, 0.22595, 4, 305.5, 81.03, 0.00088, 2, 5, 50.88, 86.25, 0.46187, 55, 31.02, 2.21, 0.53813, 3, 5, 63.89, 103.55, 0.19216, 54, 39.92, 2.09, 0.1179, 55, 9.9, -2.55, 0.68994, 3, 54, 35.16, -8.87, 0.50883, 55, 0.79, -10.28, 0.49117, 4, 306.79, 117.87, 0, 4, 53, 69.95, -7.23, 0.03348, 54, 22.44, -16.79, 0.83876, 55, -14.13, -11.75, 0.12776, 4, 316.76, 129.06, 0, 3, 53, 56.56, -16.38, 0.1907, 54, 6.61, -20.3, 0.8093, 4, 330.96, 136.88, 0, 3, 53, 38.18, -19.68, 0.7566, 54, -11.67, -16.52, 0.2434, 4, 349.57, 138.4, 0, 2, 53, 21.41, -16.89, 0.99327, 4, 366, 134.02, 0.00673, 2, 52, 44.91, -6.36, 0.43983, 53, 2.18, -7.37, 0.56017, 2, 6, 71.41, 106.26, 0.11755, 52, 27.35, -3.51, 0.88245, 4, 6, 87.89, 96.95, 0.35469, 51, 47.71, -2.34, 0.02936, 52, 8.45, -2.38, 0.61577, 69, -299.78, 163.67, 0.00018, 3, 6, 106.53, 88.42, 0.60767, 51, 27.22, -2.95, 0.38888, 69, -281.14, 155.15, 0.00345, 3, 6, 125.66, 80.88, 0.85792, 51, 6.74, -4.66, 0.13569, 69, -262.02, 147.61, 0.00639, 2, 6, 141.1, 70.27, 0.99179, 69, -246.58, 136.99, 0.00821, 2, 6, 146.89, 57.41, 0.98879, 69, -240.78, 124.13, 0.01121, 2, 6, 151.07, 36.55, 0.98393, 69, -236.6, 103.27, 0.01607, 2, 6, 151.05, -3.51, 0.97872, 69, -236.63, 63.22, 0.02128, 3, 31, 73.79, 9.94, 0.00759, 32, 32.78, 6.68, 0.98321, 71, -279.03, -218.16, 0.0092, 2, 32, 10.42, 4.14, 0.99048, 71, -291.82, -199.64, 0.00952, 4, 4, 7.3, 83.88, 0.69903, 3, 120.21, 83.21, 0.07992, 28, -86.37, -4.25, 0.19474, 71, -528.87, -171.4, 0.02632, 3, 4, 17.41, 53.51, 0.768, 28, -76.26, -34.63, 0.192, 71, -497.99, -162.96, 0.04, 3, 4, 36.18, 19.37, 0.768, 28, -57.5, -68.76, 0.192, 71, -462.88, -146.08, 0.04, 3, 4, 16.55, -41.67, 0.768, 25, -83.5, 34.97, 0.192, 71, -403, -168.99, 0.04, 3, 4, 36.81, 0.48, 0.768, 25, -63.25, 77.12, 0.192, 71, -443.98, -146.48, 0.04, 4, 4, 10.04, -78.22, 0.67663, 3, 112.22, -78.71, 0.10546, 25, -90.02, -1.58, 0.19552, 71, -366.86, -177.48, 0.02239, 3, 21, 71.42, -23.61, 0.66384, 19, 233.2, 15.92, 0.32839, 8, 4.78, 112.43, 0.00777, 2, 21, 45.33, 0.74, 0.9542, 19, 222.55, -18.15, 0.0458, 3, 32, 150.63, 7.38, 0, 21, 84.25, 6.38, 0.02538, 19, 200.59, 14.49, 0.97462, 2, 21, 67.46, 18.8, 0.04486, 19, 196.66, -6.02, 0.95514, 3, 21, 66.22, -6.9, 0.61066, 19, 220.38, 3.99, 0.38767, 8, -12.62, 110.55, 0.00168, 2, 21, 52.83, 5.64, 0.86375, 19, 214.87, -13.51, 0.13625, 3, 21, 55.46, -15.26, 0.81999, 19, 232.59, -2.09, 0.14208, 8, -6.49, 98.37, 0.03793, 3, 20, 95.86, -30.93, 0.04722, 21, 36.58, -12.79, 0.76467, 8, -12.56, 80.33, 0.18811, 2, 32, 147.58, 19.02, 0, 19, 188.69, 16.36, 1, 1, 19, 186.88, -2.24, 1, 2, 32, 159.67, 50.42, 0, 19, 164.75, 40.02, 1, 1, 19, 168, -16.88, 1, 1, 19, 161.87, -1.24, 1, 2, 32, 140.55, 46, 0, 19, 161.12, 20.74, 1, 5, 4, -84.43, -98.55, 0.00592, 3, 16.61, -92.75, 0.41334, 7, -43.94, 91.79, 0.00024, 20, 43.55, 50.59, 0.5705, 73, -294.26, 146.91, 0.01, 5, 3, -10.65, -59.07, 0.25602, 7, -15.46, 59.13, 0.08085, 20, 2.47, 36.83, 0.63302, 71, -385.04, -300.57, 0.00408, 73, -327.62, 119.25, 0.02603, 5, 3, -48.66, -59.89, 0.01057, 7, 22.5, 61.34, 0.1113, 20, -10.05, 0.93, 0.79821, 8, -1.34, -29.7, 0.048, 73, -326.35, 81.25, 0.03193, 3, 7, 41.36, 84.32, 0.05429, 8, 17.52, -6.72, 0.91991, 73, -303.85, 61.83, 0.02581, 3, 20, 52.55, -25.65, 0.41754, 8, -0.81, 38.31, 0.5725, 73, -258.37, 79.04, 0.00997, 3, 20, 61, -26.65, 0.46547, 8, -3.13, 46.49, 0.52693, 73, -250.13, 81.15, 0.00761, 3, 20, 68.37, -40.37, 0.24329, 8, 6.71, 58.56, 0.75237, 73, -238.31, 71.02, 0.00435, 3, 7, 45.61, 105.03, 0.01967, 8, 21.78, 13.98, 0.96081, 73, -283.26, 57.05, 0.01952, 3, 6, 15.91, 47.1, 0.7208, 5, 103.14, 47.25, 0.2754, 51, 90.16, 74.24, 0.0038, 2, 6, 16.29, -44.73, 0.70556, 5, 104.41, -44.57, 0.29444, 2, 5, 20.29, 37.26, 0.59718, 4, 264.38, 38.04, 0.40282, 2, 5, 44.3, 37.56, 0.93934, 4, 288.36, 39.3, 0.06066, 2, 6, -3.35, 44.29, 0.40393, 5, 83.89, 44.25, 0.59607, 3, 6, -21.9, 41.29, 0.07994, 5, 65.38, 41.07, 0.91979, 4, 309.28, 43.65, 0.00027, 2, 5, 32.01, -38.38, 0.60694, 4, 279.1, -37.07, 0.39306, 2, 5, 52.72, -38.85, 0.91363, 4, 299.82, -36.72, 0.08637, 3, 6, -18.66, -40.5, 0.11919, 5, 69.42, -40.68, 0.87191, 4, 316.58, -37.88, 0.00891, 3, 6, -1.06, -42.74, 0.39586, 5, 87.04, -42.75, 0.60347, 4, 334.27, -39.25, 0.00067, 2, 5, 11.7, 59.9, 0.15769, 4, 254.89, 60.32, 0.84231, 3, 5, 20.8, -52.85, 0.15346, 16, 41.15, 102.5, 0.06444, 4, 268.48, -51.98, 0.7821, 3, 5, 30.72, 36.86, 0.80507, 53, 102.97, 87.37, 0.00076, 4, 274.82, 38.06, 0.19417, 2, 5, 40.38, -38.11, 0.76071, 4, 287.46, -36.47, 0.23929, 2, 5, 13.35, 40.22, 0.40782, 4, 257.32, 40.72, 0.59218, 2, 4, 196.47, -2.4, 0.97011, 71, -432.44, 12.8, 0.02989, 3, 16, -22.47, 111.48, 0.05274, 4, 204.87, -42.99, 0.91924, 71, -391.45, 18.98, 0.02802, 2, 4, 196.93, 16.48, 0.97034, 71, -451.26, 14.28, 0.02966, 3, 17, -12.38, -108.73, 0.07424, 4, 198.38, 58.16, 0.89679, 71, -492.81, 17.99, 0.02897, 4, 17, -39.59, -45.01, 0.41113, 4, 171.17, 121.89, 0.41457, 28, 77.49, 33.76, 0.14571, 71, -557.92, -5.72, 0.02858, 4, 17, -33.4, -96.9, 0.0866, 4, 177.35, 70, 0.73446, 28, 83.68, -18.14, 0.14489, 71, -505.76, -2.36, 0.03404, 3, 4, 158.38, 29.88, 0.78785, 28, 64.7, -58.25, 0.17294, 71, -466.74, -23.48, 0.0392, 4, 4, 125.95, 10.37, 0.7776, 28, 32.28, -77.76, 0.096, 25, 25.9, 87.01, 0.0864, 71, -449.02, -56.92, 0.04, 3, 4, 159.72, -13.94, 0.78794, 25, 59.67, 62.7, 0.17296, 71, -422.91, -24.53, 0.0391, 4, 16, -44.47, 97.03, 0.07279, 4, 182.86, -57.44, 0.74867, 25, 82.81, 19.2, 0.14496, 71, -378.22, -3.78, 0.03358, 4, 16, -44.94, 49.75, 0.3054, 4, 182.39, -104.72, 0.51562, 25, 82.34, -28.08, 0.14489, 71, -331.03, -6.82, 0.03409, 5, 49, 98.72, 141.28, 0.00153, 16, -70.45, 16.59, 0.27889, 4, 156.89, -137.88, 0.50257, 25, 56.83, -61.24, 0.19575, 71, -299.31, -34.09, 0.02127, 4, 17, -64.79, -70.07, 0.09547, 4, 145.97, 96.83, 0.66053, 29, 60.96, 1.02, 0.189, 71, -534.26, -32.24, 0.055, 3, 4, 136.87, 59.23, 0.756, 29, 51.87, -36.58, 0.189, 71, -497.22, -43.36, 0.055, 3, 4, 105.74, 41.43, 0.756, 29, 20.73, -54.38, 0.189, 71, -481.13, -75.42, 0.055, 3, 4, 66.39, 42.05, 0.756, 29, -18.61, -53.76, 0.189, 71, -483.88, -114.67, 0.055, 3, 4, 36.32, 58.52, 0.756, 29, -48.68, -37.29, 0.189, 71, -501.97, -143.8, 0.055, 4, 4, 80.16, 13.17, 0.76545, 29, -4.84, -82.64, 0.08505, 26, -9.63, 92.57, 0.0945, 71, -454.3, -102.49, 0.055, 3, 4, 20.81, 92.72, 0.75822, 29, -64.19, -3.1, 0.18956, 71, -536.95, -157.43, 0.05222, 3, 4, 31.1, 131.58, 0.76464, 29, -53.91, 35.77, 0.19116, 71, -575.21, -145.05, 0.0442, 3, 4, 101.96, 151.58, 0.76635, 29, 16.95, 55.77, 0.19159, 71, -591.33, -73.21, 0.04206, 4, 17, -79.79, -32.65, 0.13148, 4, 130.96, 134.25, 0.63115, 29, 45.95, 38.44, 0.19066, 71, -572.44, -45.19, 0.04671, 3, 4, 65.64, 153.14, 0.76766, 29, -19.36, 57.33, 0.19191, 71, -594.86, -109.39, 0.04043, 3, 4, 71.8, 108.78, 0.69838, 30, -2.02, 3.96, 0.23279, 71, -550.23, -105.65, 0.06883, 3, 4, 75.72, -84.28, 0.69723, 27, -4.47, -2.3, 0.23241, 71, -357.24, -112.22, 0.07036, 3, 4, 108.06, 119.17, 0.7498, 30, 34.24, 14.35, 0.18745, 71, -558.63, -68.88, 0.06275, 3, 4, 109.66, 86.91, 0.744, 30, 35.84, -17.9, 0.186, 71, -526.33, -69.03, 0.07, 3, 4, 87.53, 138.05, 0.75514, 30, 13.71, 33.23, 0.18879, 71, -578.59, -88.35, 0.05607, 3, 4, 57.57, 137.85, 0.75639, 30, -16.25, 33.03, 0.1891, 71, -580.02, -118.28, 0.05451, 3, 4, 38.71, 117.5, 0.75133, 30, -35.11, 12.69, 0.18783, 71, -560.73, -138.22, 0.06084, 3, 4, 39.86, 86.2, 0.74511, 30, -33.96, -18.61, 0.18628, 71, -529.42, -138.77, 0.06862, 3, 4, 59.84, 69.59, 0.744, 30, -13.97, -35.23, 0.186, 71, -511.74, -119.72, 0.07, 3, 4, 90.14, 69.84, 0.744, 30, 16.32, -34.98, 0.186, 71, -510.35, -89.45, 0.07, 3, 4, 149.79, -75.37, 0.75766, 26, 60, 4.03, 0.18941, 71, -362.11, -37.78, 0.05293, 3, 4, 138.03, -33.7, 0.756, 26, 48.24, 45.7, 0.189, 71, -404.36, -47.25, 0.055, 3, 4, 104.01, -15.23, 0.756, 26, 14.21, 64.16, 0.189, 71, -424.64, -80.23, 0.055, 3, 4, 68.81, -15.17, 0.756, 26, -20.98, 64.22, 0.189, 71, -426.62, -115.37, 0.055, 3, 4, 38.76, -36.23, 0.756, 26, -51.04, 43.17, 0.189, 71, -407.23, -146.52, 0.055, 3, 4, 25.92, -73.58, 0.76045, 26, -63.87, 5.81, 0.19011, 71, -370.63, -161.37, 0.04944, 3, 4, 36.97, -112.47, 0.76907, 26, -52.82, -33.08, 0.19227, 71, -331.19, -152.44, 0.03866, 3, 4, 64.28, -134.71, 0.77292, 26, -25.51, -55.32, 0.19323, 71, -307.5, -126.39, 0.03385, 3, 4, 106.01, -136.4, 0.77118, 26, 16.22, -57, 0.19279, 71, -303.55, -84.81, 0.03603, 6, 48, 203.11, 53.27, 0.00012, 49, 124.19, 136.62, 0.00206, 16, -86.1, 37.22, 0.08219, 4, 141.23, -117.25, 0.68162, 26, 51.43, -37.85, 0.1915, 71, -320.76, -48.6, 0.04252, 5, 48, 232.71, 31.94, 0, 49, 160.55, 133.55, 0.00048, 4, 116.46, -90.45, 0.74844, 27, 36.27, -8.48, 0.18723, 71, -348.86, -71.87, 0.06385, 3, 4, 112.38, -59.04, 0.744, 27, 32.19, 22.93, 0.186, 71, -380.45, -74.24, 0.07, 3, 4, 88.22, -43.75, 0.744, 27, 8.03, 38.23, 0.186, 71, -397.03, -97.54, 0.07, 3, 4, 60.29, -46.45, 0.744, 27, -19.9, 35.52, 0.186, 71, -395.85, -125.57, 0.07, 3, 4, 41.78, -69.6, 0.74463, 27, -38.41, 12.37, 0.18616, 71, -373.74, -145.31, 0.06922, 3, 4, 46.17, -99.42, 0.75016, 27, -34.02, -17.44, 0.18754, 71, -343.73, -142.55, 0.0623, 3, 4, 68.76, -114.79, 0.75307, 27, -11.44, -32.82, 0.18827, 71, -327.15, -120.83, 0.05866, 3, 4, 98.7, -112.77, 0.75277, 27, 18.5, -30.8, 0.18819, 71, -327.54, -90.83, 0.05904, 3, 16, -11.16, 64.52, 0.45182, 4, 216.17, -89.95, 0.52257, 71, -343.95, 27.71, 0.02561, 3, 16, -11.85, 23.92, 0.74239, 4, 215.49, -130.55, 0.2396, 71, -303.44, 24.82, 0.018, 3, 17, -5.42, -57.48, 0.59303, 4, 205.33, 109.41, 0.38043, 71, -543.6, 27.72, 0.02655, 3, 17, -8.21, -20.76, 0.80266, 4, 202.54, 146.13, 0.18168, 71, -580.42, 26.93, 0.01567, 3, 17, 7.4, -108.57, 0.05331, 4, 218.15, 58.33, 0.92265, 71, -491.89, 37.74, 0.02404, 3, 17, 14.8, -93.13, 0.23887, 4, 225.56, 73.77, 0.75071, 71, -506.91, 45.98, 0.01042, 3, 16, -1.98, 107.63, 0.07859, 4, 225.35, -46.84, 0.89844, 71, -386.49, 39.22, 0.02297, 2, 4, 221.37, 20.64, 0.97648, 71, -454.09, 38.91, 0.02352, 2, 4, 241.26, 22.5, 0.97992, 71, -454.87, 58.88, 0.02008, 3, 5, 18.11, 22.19, 0.67224, 4, 262.81, 22.89, 0.30771, 71, -454.09, 80.41, 0.02005, 3, 5, 42, 21.75, 0.96345, 4, 286.7, 23.41, 0.01597, 71, -453.31, 104.29, 0.02057, 2, 5, 62.76, 21.66, 0.97953, 71, -452.91, 125.04, 0.02047, 3, 6, -4.74, 21.61, 0.33816, 5, 82.73, 21.56, 0.6414, 71, -452.52, 145.01, 0.02044, 4, 6, 16.01, 22.88, 0.77437, 5, 103.47, 23.03, 0.20387, 51, 79.34, 95.91, 0.00126, 71, -453.7, 165.77, 0.0205, 2, 4, 222.53, -7.92, 0.97662, 71, -425.51, 38.52, 0.02338, 3, 5, 2.12, -8.58, 0.1936, 4, 248.05, -8.49, 0.78633, 71, -423.55, 63.97, 0.02007, 3, 5, 22.1, -9.86, 0.76923, 4, 268.07, -8.97, 0.2103, 71, -421.99, 83.93, 0.02047, 2, 5, 42.11, -12.7, 0.97894, 71, -418.85, 103.9, 0.02106, 2, 5, 62.5, -14.36, 0.97904, 71, -416.9, 124.26, 0.02096, 3, 6, -4.17, -15.98, 0.30484, 5, 83.66, -16.02, 0.67471, 71, -414.94, 145.4, 0.02045, 3, 6, 16.18, -17.84, 0.84267, 5, 104.04, -17.68, 0.13723, 71, -412.98, 165.74, 0.0201, 2, 5, 35.98, -44.66, 0.60422, 4, 283.32, -43.19, 0.39578, 2, 4, 251.45, -35.58, 0.98149, 71, -396.32, 65.9, 0.01851, 3, 5, 1.84, 47.86, 0.04846, 4, 245.53, 47.89, 0.93469, 71, -479.99, 64.51, 0.01685, 2, 5, 56.76, 57.64, 0.92551, 4, 300.01, 59.86, 0.07449, 3, 16, 7.31, 92.18, 0.27794, 4, 234.64, -62.29, 0.71368, 71, -370.56, 47.65, 0.00838, 4, 16, -44.16, 19.54, 0.50592, 4, 183.17, -134.93, 0.42626, 25, 83.12, -58.29, 0.04906, 71, -300.83, -7.68, 0.01876, 4, 17, -39.69, -20.49, 0.59456, 4, 171.07, 146.41, 0.35775, 28, 77.39, 58.27, 0.02945, 71, -582.4, -4.49, 0.01824, 4, 6, -11.94, 70.63, 0.33218, 5, 75.05, 70.51, 0.58842, 55, 22.78, 29.85, 0.07326, 70, -339.01, 137.65, 0.00614, 4, 6, 9.3, 77.45, 0.60559, 5, 96.22, 77.54, 0.29423, 54, 14.88, 35.18, 0.08506, 70, -317.76, 144.46, 0.01512, 4, 6, 31.69, 78.19, 0.84829, 5, 118.6, 78.5, 0.13232, 51, 89.79, 39.38, 0.00329, 70, -295.38, 145.21, 0.0161, 2, 6, 73.7, 72.25, 0.98692, 69, -313.97, 138.97, 0.01308, 2, 6, 94.6, 66.06, 0.97936, 69, -293.07, 132.79, 0.02064, 2, 6, 122.53, 48.23, 0.97317, 69, -265.14, 114.96, 0.02683, 5, 6, -12.63, -71.54, 0.21305, 5, 75.75, -71.66, 0.63873, 49, 22.02, -22.71, 0.11717, 4, 324.13, -68.59, 0.0251, 70, -339.7, -4.53, 0.00595, 5, 6, 7.12, -77.46, 0.36255, 5, 95.56, -77.39, 0.32818, 48, 12.93, -29.95, 0.19774, 49, 3.92, -32.58, 0.09729, 70, -319.94, -10.45, 0.01424, 4, 6, 32.47, -77.45, 0.62779, 5, 120.9, -77.13, 0.11679, 47, 6.63, -28.12, 0.23982, 70, -294.6, -10.44, 0.01559, 2, 6, 54.97, -70.61, 0.98966, 69, -332.7, -3.89, 0.01034, 2, 6, 85.57, -64.2, 0.98236, 69, -302.1, 2.53, 0.01764, 2, 6, 116.69, -66.09, 0.97682, 69, -270.98, 0.64, 0.02318, 2, 6, 125.08, -10.38, 0.96961, 69, -262.6, 56.35, 0.03039, 2, 6, 126, -28.3, 0.96995, 69, -261.67, 38.43, 0.03005, 2, 6, 117.94, -40.13, 0.97265, 69, -269.73, 26.6, 0.02735, 2, 6, 109.67, -49.09, 0.97487, 69, -278, 17.63, 0.02513, 2, 6, 96.48, -55.2, 0.97541, 69, -291.19, 11.52, 0.02459, 2, 6, 70.23, -69.05, 0.98967, 69, -317.44, -2.33, 0.01033, 3, 6, 64.14, -77.26, 0.99099, 5, 152.57, -76.63, 0.00152, 69, -323.54, -10.53, 0.00749, 5, 6, 6.59, -108.36, 0.02587, 5, 95.33, -108.29, 0.0086, 47, 37.59, -2.31, 0.12901, 48, 5.81, 0.12, 0.82976, 70, -320.48, -41.35, 0.00676, 7, 6, 34.62, -105.56, 0.31074, 45, 70.34, 9.88, 0.0001, 46, 44.34, -2.09, 0.03077, 47, 9.5, -0.08, 0.64945, 49, -35.4, -32.35, 0.0001, 4, 373.02, -100.22, 0, 70, -292.44, -38.55, 0.00883, 5, 6, 57.59, -97.01, 0.63305, 5, 146.21, -96.44, 1e-05, 46, 19.85, -1.21, 0.3623, 49, -45.48, -54.68, 0.0001, 70, -269.48, -29.99, 0.00454, 6, 6, 78.74, -87.23, 0.77551, 45, 24.33, -2.99, 0.14395, 46, -3.44, -2.15, 0.07619, 47, -37.17, -10.27, 0.00064, 49, -53.42, -76.59, 0.00016, 69, -308.93, -20.5, 0.00355, 4, 6, 97.73, -79.92, 0.98762, 47, -57.16, -14.09, 5e-05, 49, -61.6, -95.23, 1e-05, 69, -289.94, -13.2, 0.01232, 2, 6, 132.55, -49.94, 0.9725, 69, -255.13, 16.79, 0.0275, 2, 6, 125.54, -19.34, 0.9698, 69, -262.14, 47.39, 0.0302, 2, 6, 135.4, -30.94, 0.97294, 69, -252.27, 35.79, 0.02706, 2, 6, 128.52, -7.86, 0.96966, 69, -259.16, 58.87, 0.03034, 2, 6, 126.88, 16.2, 0.96976, 69, -260.8, 82.92, 0.03024, 2, 6, 113.08, 57.1, 0.97534, 69, -274.6, 123.82, 0.02466, 2, 6, 126.12, 38.58, 0.97047, 69, -261.55, 105.3, 0.02953, 2, 6, 55.91, 73.44, 0.98985, 69, -331.76, 140.17, 0.01015, 4, 52, 96.1, 31.31, 0.01138, 54, 22.95, 4.77, 0.63473, 55, -4.13, 7.36, 0.34617, 70, -332.71, 172.15, 0.00771, 5, 52, 78.7, 18.72, 0.01222, 53, 41.99, 6.26, 0.50291, 54, 1.52, 6.14, 0.46512, 55, -22.74, 18.07, 0.00833, 70, -311.55, 175.83, 0.01142, 5, 6, 35.88, 104.24, 0.09474, 5, 122.54, 104.58, 0.01044, 52, 58.38, 13.9, 0.15492, 53, 21.17, 7.84, 0.72773, 70, -291.18, 171.25, 0.01218, 5, 6, 54.35, 96.62, 0.09817, 5, 141.08, 97.15, 0.00284, 52, 38.45, 12.64, 0.70986, 53, 1.8, 12.69, 0.18161, 70, -272.72, 163.63, 0.00751, 3, 6, 71.28, 87.9, 0.76414, 51, 58.6, 13.14, 0.02273, 52, 19.41, 13.04, 0.21313, 4, 4, -20.78, 63.97, 0.48126, 3, 90.87, 65.2, 0.48823, 71, -510.51, -200.52, 0.02899, 73, -453.08, 219.3, 0.00152, 4, 4, -51.26, 55.89, 0.15641, 3, 59.92, 59.15, 0.81158, 71, -504.09, -231.41, 0.02261, 73, -446.67, 188.42, 0.0094, 6, 4, -79.17, 51.96, 0.02898, 3, 31.82, 57.08, 0.85934, 7, -53.64, -58.5, 0.00338, 9, -89.56, 29.83, 0.07523, 71, -501.69, -259.48, 0.0162, 73, -444.26, 160.34, 0.01686, 6, 4, -103.26, 51.85, 0.00525, 3, 7.77, 58.57, 0.6528, 7, -29.55, -59.11, 0.05018, 9, -65.47, 29.23, 0.25924, 71, -502.89, -283.54, 0.00998, 73, -445.46, 136.28, 0.02254, 4, 3, -28.96, 72.64, 0.1578, 7, 7.68, -71.82, 0.1359, 9, -28.24, 16.51, 0.67762, 73, -459.1, 99.38, 0.02868, 4, 3, -77.18, 65.19, 0.00215, 7, 55.59, -62.6, 0.35428, 9, 19.67, 25.73, 0.61373, 73, -451.08, 51.26, 0.02984, 4, 7, 48.33, 56.69, 0.312, 20, -24.26, -21.14, 0.30073, 8, 24.5, -34.35, 0.35716, 73, -331.64, 55.55, 0.03012, 6, 4, -93.57, -57.2, 0.00077, 3, 10.23, -50.88, 0.56063, 7, -36.02, 50.18, 0.03275, 20, 2.09, 59.25, 0.37464, 71, -393.48, -279.79, 0.01038, 73, -336.05, 140.03, 0.02083, 5, 4, -66.89, -52.93, 0.01502, 3, 37.13, -48.39, 0.81398, 20, 9.16, 85.32, 0.14043, 71, -396.29, -252.92, 0.01675, 73, -338.86, 166.9, 0.01383, 5, 4, -40.82, -52.32, 0.12076, 3, 63.19, -49.5, 0.83188, 20, 19.31, 109.35, 0.01703, 71, -395.48, -226.85, 0.02343, 73, -338.06, 192.97, 0.0069, 4, 4, -16.22, -54.19, 0.41202, 3, 87.61, -53, 0.55823, 71, -392.28, -202.39, 0.02936, 73, -334.85, 217.44, 0.00039, 4, 4, -14.9, 25.87, 0.43796, 3, 94.22, 26.8, 0.52379, 71, -472.15, -196.73, 0.03778, 73, -414.72, 223.1, 0.00047, 4, 4, -46.37, 21.78, 0.06184, 3, 62.55, 24.8, 0.90041, 71, -469.78, -228.37, 0.02915, 73, -412.35, 191.46, 0.00859, 4, 4, -75.19, 20.22, 0.00279, 3, 33.68, 25.14, 0.95954, 71, -469.78, -257.24, 0.02141, 73, -412.35, 162.59, 0.01626, 5, 3, 2.07, 27.89, 0.81434, 7, -24.98, -28.24, 0.07992, 9, -60.9, 60.09, 0.0681, 71, -472.15, -288.88, 0.01297, 73, -414.72, 130.95, 0.02467, 5, 3, -35.43, 33.87, 0.11856, 7, 12.71, -32.84, 0.60524, 9, -23.21, 55.49, 0.23841, 71, -477.69, -326.45, 0.00314, 73, -420.26, 93.38, 0.03465, 3, 7, 58.01, -24.99, 0.8154, 9, 22.09, 63.34, 0.15115, 73, -413.54, 47.9, 0.03345, 4, 4, -15.39, -16.14, 0.25536, 3, 90.95, -15.09, 0.70619, 71, -430.23, -199.5, 0.03751, 73, -372.8, 220.33, 0.00094, 4, 4, -43.18, -14.88, 0.01867, 3, 63.3, -11.99, 0.94316, 71, -433, -227.18, 0.02989, 73, -375.57, 192.65, 0.00828, 3, 3, 33.63, -12.43, 0.96205, 71, -432.21, -256.84, 0.02185, 73, -374.78, 162.98, 0.0161, 5, 3, 4.35, -13.66, 0.80444, 7, -28.79, 13.2, 0.08584, 20, -34.83, 66.75, 0.07194, 71, -430.62, -286.11, 0.01404, 73, -373.2, 133.72, 0.02374, 6, 3, -33.72, -21.92, 0.08412, 7, 8.95, 22.85, 0.63729, 20, -40.41, 28.2, 0.23731, 8, -14.88, -68.2, 0.00381, 71, -421.92, -324.07, 0.00408, 73, -364.5, 95.75, 0.03339, 4, 7, 56.51, 19.28, 0.82393, 20, -61.95, -14.35, 0.07699, 8, 32.68, -71.76, 0.06502, 73, -369.24, 48.29, 0.03406, 2, 9, 8.53, -20.79, 0.98242, 73, -497.3, 63.56, 0.01758, 4, 3, -23.75, 98.13, 0.14288, 7, 3.41, -97.48, 0.00438, 9, -32.51, -9.15, 0.8333, 73, -484.65, 104.29, 0.01945, 4, 4, -103.37, 76.43, 0.00915, 3, 9.29, 83.1, 0.51323, 9, -66.09, 4.65, 0.46121, 73, -470.01, 137.51, 0.01642, 5, 4, -76.15, 71.18, 0.0575, 3, 36.1, 76.06, 0.70818, 9, -93.14, 10.7, 0.21388, 71, -520.72, -255.42, 0.00819, 73, -463.29, 164.4, 0.01224, 5, 4, -50.46, 72.18, 0.21914, 3, 61.8, 75.36, 0.71986, 9, -118.85, 10.46, 0.03929, 71, -520.32, -229.71, 0.01527, 73, -462.89, 190.11, 0.00644, 3, 4, -25.11, 79.5, 0.49215, 3, 87.58, 80.99, 0.48704, 71, -526.25, -204.01, 0.02081, 3, 4, -17.52, -74.49, 0.41397, 3, 84.96, -73.17, 0.56662, 71, -372.08, -204.8, 0.01941, 5, 4, -40.93, -73.78, 0.14935, 3, 61.65, -70.91, 0.76287, 20, 38.83, 100.43, 0.06958, 71, -374.06, -228.13, 0.01354, 73, -316.63, 191.69, 0.00467, 5, 4, -62.23, -75.34, 0.03579, 3, 40.29, -71.06, 0.70021, 20, 31.5, 80.37, 0.24616, 71, -373.66, -249.49, 0.0077, 73, -316.24, 170.34, 0.01014, 6, 4, -83.7, -81.26, 0.00698, 3, 18.49, -75.54, 0.50682, 7, -45.19, 74.52, 0.00437, 20, 28.08, 58.37, 0.46593, 71, -368.92, -271.24, 0.00132, 73, -311.49, 148.59, 0.01459, 4, 7, 103.76, -128.43, 0.01496, 9, 67.84, -40.1, 0.64467, 10, -35.43, -14.01, 0.3227, 73, -518.09, 4.74, 0.01767, 4, 7, 135.18, -86.38, 0.3425, 9, 99.26, 1.95, 0.09605, 10, 3.73, 20.94, 0.53942, 73, -476.83, -27.72, 0.02203, 4, 7, 158.43, -57.67, 0.59935, 9, 122.51, 30.66, 0.01074, 10, 32.23, 44.45, 0.37216, 73, -448.71, -51.68, 0.01775, 3, 7, 186.9, -29.71, 0.78434, 10, 65.69, 66.18, 0.20714, 73, -421.47, -80.83, 0.00852, 3, 7, 86.96, -53.82, 0.62455, 9, 51.04, 34.52, 0.34422, 73, -443.08, 19.68, 0.03123, 3, 7, 127.04, -37.91, 0.9148, 9, 91.12, 50.42, 0.0595, 73, -428.18, -20.79, 0.0257, 3, 7, 160.45, -2.06, 0.9779, 9, 124.53, 86.27, 0.00012, 73, -393.17, -55.08, 0.02198, 4, 7, 112.6, 34.99, 0.87079, 20, -68.96, -72.17, 0.00565, 8, 88.77, -56.05, 0.09844, 73, -354.94, -8.17, 0.02511, 5, 7, 97.23, 79.95, 0.31136, 20, -21.55, -75.23, 0.00941, 8, 73.4, -11.09, 0.42673, 12, -20.72, -34.17, 0.23175, 73, -309.61, 6.08, 0.02073, 4, 7, 98.57, -101.26, 0.15279, 9, 62.65, -12.93, 0.62828, 10, -35.11, 13.64, 0.19842, 73, -490.8, 9.25, 0.02051, 2, 7, 188.57, -0.7, 0.98871, 73, -392.51, -83.22, 0.01129, 3, 9, 108.14, -45.15, 0.2793, 10, 3.05, -26.98, 0.70255, 73, -524.14, -35.42, 0.01815, 3, 9, 135.38, -48.42, 0.16823, 10, 29.1, -35.62, 0.81225, 73, -528.09, -62.58, 0.01952, 2, 10, 58.85, -40.39, 0.98041, 73, -527.59, -92.69, 0.01959, 3, 7, 194.32, -86.41, 0.10806, 10, 61.68, 9.14, 0.87019, 73, -478.33, -86.84, 0.02175, 4, 7, 166.38, -92.19, 0.20286, 9, 130.46, -3.86, 0.00131, 10, 33.15, 9.03, 0.77539, 73, -483.42, -58.76, 0.02045, 3, 7, 201.11, -43.32, 0.49572, 10, 76.91, 50.02, 0.48987, 73, -435.42, -94.7, 0.01442, 2, 10, 101.04, -44.16, 0.9804, 73, -523.94, -134.9, 0.0196, 3, 32, 250.88, -441.8, 0, 10, 169.04, -46.25, 0.98041, 73, -514.13, -202.22, 0.01959, 3, 7, 243.13, -40, 0.20604, 10, 118.76, 44.9, 0.77758, 73, -433.15, -136.79, 0.01639, 3, 7, 304.44, -33.16, 0.03268, 10, 180.2, 39.4, 0.9478, 73, -427.85, -198.25, 0.01952, 4, 7, 178.03, -50.54, 0.58143, 9, 142.11, 37.8, 0.00052, 10, 52.86, 47.54, 0.40266, 73, -442.07, -71.45, 0.01539, 3, 7, 238.27, -83.62, 0.00693, 10, 105.31, 3.12, 0.97173, 73, -476.64, -130.85, 0.02133, 3, 32, 323.03, -459.51, 0, 10, 243.12, -40.68, 0.98039, 73, -495.7, -274.19, 0.01961, 2, 10, 348.28, -22.1, 0.98064, 73, -459.05, -374.49, 0.01936, 3, 7, 377.97, -26.15, 0.00021, 10, 253.65, 31.63, 0.98023, 73, -422.66, -271.94, 0.01956, 2, 10, 352.51, 32.52, 0.9807, 73, -404.54, -369.12, 0.0193, 2, 10, 445.42, 30.46, 0.98027, 73, -390.34, -460.96, 0.01973, 3, 10, 471.12, 25.22, 0.81331, 11, -18.5, 19.49, 0.16721, 73, -391.01, -487.19, 0.01948, 3, 10, 493.55, 16.8, 0.27185, 11, 5.43, 20.63, 0.70902, 73, -395.39, -510.74, 0.01913, 2, 11, 27.82, 16.56, 0.98052, 73, -404.47, -531.6, 0.01948, 2, 11, 53.02, 9.35, 0.98079, 73, -417.26, -554.49, 0.01921, 2, 11, 78.86, 8.17, 0.9848, 73, -424.33, -579.37, 0.0152, 3, 10, 436.27, -10.98, 0.95326, 11, -36.21, -27.54, 0.02686, 73, -432.74, -459.18, 0.01988, 3, 10, 457.9, -16.18, 0.83098, 11, -14.28, -23.77, 0.14962, 73, -434.09, -481.39, 0.0194, 3, 10, 479.63, -22.77, 0.33114, 11, 8.29, -21.22, 0.64938, 73, -436.78, -503.94, 0.01948, 3, 10, 500.13, -30.51, 0.05022, 11, 30.18, -20.22, 0.92994, 73, -440.82, -525.47, 0.01984, 3, 10, 520.43, -41.28, 0.00833, 11, 53.09, -22.09, 0.97189, 73, -447.88, -547.35, 0.01977, 3, 10, 542.2, -55.4, 0.00018, 11, 78.65, -26.45, 0.98331, 73, -457.98, -571.24, 0.01651, 2, 11, 126.42, -43.13, 0.9883, 73, -485.15, -613.92, 0.0117, 1, 11, 242.08, -14.97, 1, 3, 10, 592.65, -46.12, 0, 11, 121.32, 2.02, 0.99037, 73, -440.04, -619.3, 0.00963, 3, 10, 414.51, -9.17, 0.97574, 11, -56.9, -34.48, 0.00446, 73, -434.75, -437.45, 0.0198, 2, 10, 420.31, 32.46, 0.98069, 73, -392.76, -435.89, 0.01931, 3, 6, 32.96, -65.12, 0.84896, 5, 121.28, -64.8, 0.13883, 70, -294.11, 1.89, 0.01221, 2, 6, 32.3, 67.71, 0.98728, 70, -294.76, 134.72, 0.01272, 2, 6, 62.92, 79.09, 0.99201, 69, -324.76, 145.82, 0.00799, 4, 7, 185.77, 26.11, 0.775, 8, 161.94, -64.93, 0.00016, 12, 73.78, -76.67, 0.21685, 73, -365.64, -81.09, 0.00799, 4, 7, 151.12, 50.81, 0.61743, 8, 127.29, -40.23, 0.02095, 12, 36.35, -56.43, 0.34803, 73, -340.08, -47.07, 0.01359, 5, 7, 119.26, 76.88, 0.37014, 20, -32.83, -94.39, 0.00096, 8, 95.42, -14.16, 0.17319, 12, 1.51, -34.49, 0.43879, 73, -313.22, -15.86, 0.01692, 4, 7, 83.23, 124.95, 0.01142, 8, 59.4, 33.9, 0.70197, 12, -40.17, 8.76, 0.27322, 73, -264.28, 18.95, 0.01339], "hull": 219, "edges": [6, 8, 20, 22, 44, 46, 46, 48, 64, 66, 66, 68, 80, 82, 82, 84, 102, 104, 104, 106, 106, 108, 130, 132, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 158, 160, 160, 162, 926, 924, 196, 198, 204, 206, 216, 218, 218, 220, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 264, 266, 272, 274, 274, 276, 276, 278, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 302, 304, 346, 348, 348, 350, 350, 352, 364, 366, 384, 386, 390, 392, 392, 394, 394, 396, 408, 410, 430, 432, 432, 434, 428, 430, 416, 418, 422, 424, 424, 426, 426, 428, 418, 420, 420, 422, 414, 416, 410, 412, 412, 414, 396, 398, 406, 408, 386, 388, 388, 390, 382, 384, 380, 382, 378, 380, 374, 376, 376, 378, 370, 372, 372, 374, 366, 368, 368, 370, 360, 362, 362, 364, 358, 360, 356, 358, 344, 346, 340, 342, 342, 344, 336, 338, 338, 340, 332, 334, 334, 336, 328, 330, 330, 332, 352, 354, 354, 356, 2, 0, 0, 436, 434, 436, 2, 4, 4, 6, 12, 14, 8, 10, 10, 12, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 34, 36, 36, 38, 18, 20, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 72, 74, 74, 76, 90, 92, 88, 90, 84, 86, 86, 88, 68, 70, 70, 72, 76, 78, 78, 80, 92, 94, 94, 96, 96, 98, 326, 328, 324, 326, 316, 318, 318, 320, 320, 322, 322, 324, 98, 100, 100, 102, 312, 314, 314, 316, 330, 442, 442, 444, 444, 446, 446, 450, 450, 448, 448, 452, 452, 94, 308, 310, 310, 312, 304, 306, 306, 308, 300, 302, 296, 298, 298, 300, 180, 182, 182, 184, 184, 186, 186, 188, 920, 180, 924, 922, 922, 920, 188, 190, 190, 192, 192, 194, 194, 196, 278, 280, 280, 282, 212, 214, 214, 216, 210, 212, 206, 208, 208, 210, 202, 204, 198, 200, 200, 202, 156, 158, 152, 154, 154, 156, 108, 110, 110, 112, 124, 126, 118, 120, 148, 150, 150, 152, 126, 128, 128, 130, 120, 122, 122, 124, 132, 134, 134, 136, 458, 460, 462, 464, 116, 118, 456, 466, 112, 114, 114, 116, 470, 472, 146, 474, 476, 120, 478, 480, 108, 482, 482, 484, 484, 486, 486, 488, 490, 492, 492, 494, 494, 160, 488, 496, 496, 490, 498, 506, 504, 508, 508, 506, 512, 514, 514, 516, 516, 500, 502, 522, 522, 504, 510, 524, 524, 512, 502, 526, 536, 538, 538, 540, 540, 542, 542, 544, 544, 546, 546, 548, 548, 550, 550, 60, 536, 374, 552, 554, 554, 556, 556, 558, 558, 560, 560, 564, 564, 566, 568, 570, 570, 552, 566, 572, 572, 568, 578, 580, 578, 582, 582, 584, 584, 586, 586, 588, 588, 590, 590, 592, 592, 580, 594, 596, 596, 598, 598, 600, 600, 602, 602, 604, 604, 606, 606, 608, 608, 610, 610, 612, 612, 594, 614, 616, 616, 618, 618, 620, 620, 622, 622, 624, 624, 626, 626, 628, 628, 614, 392, 640, 640, 534, 532, 534, 528, 530, 644, 646, 646, 648, 648, 650, 650, 652, 652, 654, 498, 656, 654, 656, 658, 660, 660, 662, 662, 664, 664, 666, 666, 668, 500, 670, 670, 656, 668, 670, 520, 672, 674, 642, 676, 638, 40, 680, 680, 530, 712, 714, 714, 716, 716, 718, 718, 706, 704, 722, 706, 720, 704, 720, 710, 736, 736, 712, 740, 742, 518, 638, 520, 642, 760, 762, 762, 764, 764, 766, 766, 768, 768, 770, 774, 776, 776, 778, 778, 780, 782, 784, 784, 786, 786, 788, 788, 790, 790, 792, 794, 796, 796, 798, 798, 800, 800, 802, 802, 804, 806, 808, 808, 810, 810, 812, 812, 814, 814, 816, 818, 820, 820, 822, 822, 824, 826, 828, 828, 830, 830, 832, 832, 184, 162, 926, 834, 804, 926, 842, 842, 840, 852, 850, 854, 856, 860, 862, 864, 866, 862, 872, 872, 874, 866, 876, 876, 878, 880, 882, 882, 884, 884, 886, 886, 888, 888, 890, 892, 894, 894, 896, 896, 898, 898, 900, 900, 902, 902, 904, 904, 906, 906, 908, 908, 890, 262, 264, 224, 226, 226, 228, 228, 230, 220, 222, 222, 224, 270, 272, 266, 268, 268, 270, 258, 260, 260, 262, 230, 232, 232, 234, 874, 910, 910, 892, 878, 912, 912, 880, 742, 746, 746, 696, 696, 744, 744, 694, 694, 692, 692, 748, 748, 918, 178, 180, 176, 178, 172, 174, 174, 176, 170, 172, 168, 170, 166, 168, 162, 164, 164, 166, 406, 404, 404, 402, 402, 400, 398, 400], "width": 530, "height": 1790}}, "cloth_B": {"cloth_B": {"type": "mesh", "uvs": [0.81737, 0.0991, 0.83665, 0.18154, 0.85005, 0.31621, 0.86344, 0.45089, 0.87326, 0.51044, 0.88878, 0.57447, 0.91013, 0.64017, 0.93861, 0.71516, 0.96909, 0.77742, 0.99779, 0.83815, 0.96637, 0.89062, 0.90849, 0.93078, 0.84775, 0.93756, 0.77199, 0.91883, 0.70406, 0.9092, 0.59425, 0.90292, 0.49793, 0.89834, 0.40625, 0.9041, 0.31516, 0.92304, 0.22321, 0.94712, 0.13845, 0.99561, 0.06855, 0.99474, 1e-05, 0.99491, 0.02696, 0.89009, 0.05392, 0.78956, 0.07762, 0.69833, 0.09639, 0.60277, 0.10723, 0.52229, 0.11087, 0.43888, 0.11614, 0.36133, 0.13528, 0.2352, 0.17278, 0.11022, 0.21308, 0.00764, 0.35745, 0.00577, 0.50182, 0.00389, 0.64619, 0.00202, 0.79057, 0.00014, 0.15335, 0.9052, 0.16807, 0.81582, 0.17838, 0.71134, 0.1931, 0.59553, 0.20783, 0.49986, 0.31752, 0.83639, 0.32047, 0.71177, 0.32562, 0.61359, 0.33151, 0.49148, 0.48854, 0.81005, 0.48633, 0.69298, 0.48339, 0.59479, 0.4856, 0.48654, 0.4002, 0.82281, 0.84093, 0.84671, 0.81541, 0.7507, 0.79703, 0.66691, 0.78376, 0.56392, 0.77355, 0.48187, 0.73271, 0.80481, 0.63981, 0.8118, 0.6347, 0.70531, 0.63368, 0.60058, 0.63062, 0.49235, 0.75373, 0.34557, 0.62815, 0.36117, 0.48777, 0.35071, 0.34086, 0.36326, 0.22577, 0.3821, 0.29686, 0.16962, 0.49473, 0.16559, 0.67376, 0.16559], "triangles": [51, 52, 7, 8, 51, 7, 10, 8, 9, 8, 11, 51, 13, 52, 51, 10, 11, 8, 12, 51, 11, 13, 51, 12, 61, 1, 2, 55, 61, 2, 3, 55, 2, 60, 61, 55, 4, 54, 55, 4, 55, 3, 59, 55, 54, 5, 53, 54, 5, 54, 4, 53, 5, 6, 53, 58, 59, 53, 59, 54, 52, 53, 6, 52, 6, 7, 56, 58, 53, 56, 53, 52, 13, 56, 52, 14, 56, 13, 21, 23, 37, 22, 23, 21, 20, 21, 37, 38, 25, 39, 24, 25, 38, 38, 39, 42, 37, 24, 38, 23, 24, 37, 42, 19, 38, 19, 37, 38, 20, 37, 19, 31, 32, 66, 66, 30, 31, 65, 66, 64, 65, 30, 66, 29, 30, 65, 45, 65, 64, 65, 28, 29, 41, 65, 45, 41, 28, 65, 27, 28, 41, 40, 27, 41, 26, 27, 40, 44, 41, 45, 40, 41, 44, 39, 26, 40, 25, 26, 39, 43, 40, 44, 39, 40, 43, 42, 39, 43, 43, 44, 47, 46, 47, 58, 50, 43, 47, 57, 58, 56, 46, 58, 57, 46, 50, 47, 42, 43, 50, 15, 16, 46, 57, 15, 46, 17, 50, 46, 17, 46, 16, 14, 57, 56, 15, 57, 14, 17, 18, 42, 17, 42, 50, 42, 18, 19, 68, 35, 36, 68, 36, 0, 67, 33, 34, 66, 32, 33, 66, 33, 67, 1, 61, 68, 1, 68, 0, 67, 64, 66, 67, 35, 68, 35, 67, 34, 62, 68, 61, 62, 67, 68, 63, 67, 62, 63, 64, 67, 49, 64, 63, 49, 63, 62, 45, 64, 49, 60, 62, 61, 49, 62, 60, 48, 45, 49, 59, 60, 55, 44, 45, 48, 48, 60, 59, 60, 48, 49, 47, 48, 59, 44, 48, 47, 47, 59, 58], "vertices": [3, 4, 138.92, -109.36, 0.60398, 33, -148.85, 237.57, 6e-05, 31, -100.19, 27.8, 0.39597, 3, 4, 120.89, -117.74, 0.5206, 33, -132.76, 249.26, 4e-05, 31, -80.31, 27.59, 0.47936, 3, 4, 91.04, -124.5, 0.34334, 33, -104.77, 261.63, 2e-05, 31, -50.46, 20.85, 0.65663, 3, 4, 61.2, -131.26, 0.11338, 33, -76.78, 274, 1e-05, 31, -20.61, 14.11, 0.88661, 3, 4, 48.08, -135.74, 0.02467, 33, -64.77, 280.92, 0, 31, -6.84, 12.51, 0.97532, 1, 31, 8.68, 12.54, 1, 2, 31, 25.4, 14.5, 0.9722, 32, -14.91, 16.08, 0.0278, 2, 31, 45.09, 18.19, 0.21865, 32, 5.05, 17.77, 0.78135, 2, 31, 62.43, 23.68, 0.0006, 32, 22.85, 21.49, 0.9994, 1, 32, 40.03, 24.76, 1, 2, 32, 44.72, 8.6, 0.9836, 72, -271.69, -169.38, 0.0164, 4, 3, 52.06, -151, 0.00917, 31, 85.36, -10.87, 0.00275, 32, 42.19, -15.19, 0.96863, 72, -293.86, -178.38, 0.01946, 5, 4, -47.98, -131.18, 0.00024, 3, 50.82, -127.72, 0.05686, 31, 77.9, -32.96, 0.09786, 32, 32.56, -36.41, 0.8225, 72, -317.13, -179.9, 0.02255, 5, 4, -45.37, -101.98, 0.01499, 3, 55.36, -98.76, 0.19632, 31, 62.98, -58.19, 0.29478, 32, 15.17, -60.02, 0.46763, 72, -346.14, -175.7, 0.02628, 5, 4, -44.63, -75.88, 0.04134, 3, 57.82, -72.76, 0.40111, 31, 51.08, -81.43, 0.30336, 32, 0.99, -81.94, 0.2246, 72, -372.16, -173.55, 0.02959, 5, 4, -45.51, -33.81, 0.01881, 3, 59.73, -30.73, 0.80697, 31, 33.76, -119.78, 0.09756, 32, -20.09, -118.36, 0.04198, 72, -414.22, -172.14, 0.03468, 4, 3, 61.19, 6.15, 0.95363, 33, 54.71, 162.85, 0.00415, 34, -14.86, 162.43, 0.00526, 72, -451.11, -171.11, 0.03695, 6, 4, -49.68, 38.07, 0.02473, 3, 60.32, 41.27, 0.7502, 33, 64.57, 129.13, 0.08395, 34, -0.16, 130.51, 0.10512, 35, -44.26, 131.26, 0.00186, 72, -486.22, -172.4, 0.03414, 6, 4, -55.82, 72.68, 0.03825, 3, 56.49, 76.21, 0.44966, 33, 77.23, 96.34, 0.16918, 34, 17.17, 99.94, 0.29117, 35, -27.43, 100.4, 0.0218, 72, -521.11, -176.65, 0.02994, 6, 4, -63.12, 107.55, 0.01151, 3, 51.51, 111.49, 0.19809, 33, 91.1, 63.52, 0.12834, 34, 35.7, 69.51, 0.51355, 35, -9.41, 69.67, 0.12312, 72, -556.32, -182.04, 0.02539, 6, 4, -75.73, 139.37, 1e-05, 3, 41.03, 144.08, 0.05637, 33, 109.59, 34.72, 0.01845, 34, 58.21, 43.72, 0.46779, 35, 12.68, 43.52, 0.43593, 72, -588.79, -192.9, 0.02145, 4, 3, 41.55, 170.84, 0.00916, 34, 68.32, 18.93, 0.10258, 35, 22.39, 18.57, 0.87038, 72, -615.56, -192.71, 0.01788, 4, 3, 41.82, 197.09, 0, 34, 78.45, -5.29, 0, 35, 32.12, -5.81, 0.99999, 31, -33.84, -338.08, 0, 2, 35, 6.49, -4.9, 1, 31, -51.62, -319.59, 0, 2, 34, 28.05, -3.92, 1, 31, -68.51, -301.47, 0, 2, 34, 5.7, -3.4, 1, 31, -83.95, -285.29, 0, 2, 33, 28.22, -2.48, 1, 31, -101.01, -270.5, 0, 1, 33, 9.73, -2.87, 1, 3, 4, 48.23, 156.7, 0.02292, 33, -8.73, -6.1, 0.97708, 31, -132.84, -251.39, 0, 3, 4, 65.68, 155.63, 0.09855, 33, -26.06, -8.4, 0.90145, 31, -148.14, -242.91, 0, 3, 4, 94.29, 149.84, 0.24185, 33, -55.25, -8.22, 0.75814, 31, -171.47, -225.38, 1e-05, 3, 4, 123.02, 137.02, 0.38419, 33, -85.91, -1.16, 0.61579, 31, -191.89, -201.43, 2e-05, 3, 4, 146.81, 122.86, 0.46685, 33, -111.97, 8.17, 0.53312, 31, -207.26, -178.42, 2e-05, 4, 4, 150.23, 67.67, 0.71314, 33, -125.94, 61.67, 0.27751, 31, -186.6, -127.13, 3e-05, 72, -504.91, 28.82, 0.00932, 4, 4, 153.65, 12.48, 0.97384, 33, -139.9, 115.18, 0.01587, 31, -165.93, -75.84, 3e-05, 72, -449.62, 29.24, 0.01026, 4, 4, 157.08, -42.71, 0.8555, 33, -153.86, 168.68, 7e-05, 31, -145.27, -24.55, 0.13503, 72, -394.32, 29.66, 0.0094, 3, 4, 160.5, -97.9, 0.66358, 33, -167.83, 222.19, 6e-05, 31, -124.6, 26.74, 0.33636, 6, 4, -55.19, 134.78, 0.00189, 3, 61.22, 138.13, 0.08001, 33, 88.55, 35.28, 0.0538, 34, 37.32, 41.2, 0.59526, 35, -8.25, 41.34, 0.24737, 72, -583.08, -172.65, 0.02167, 6, 4, -34.89, 130.23, 0.01074, 3, 81.17, 132.26, 0.0944, 33, 67.76, 35.84, 0.16774, 34, 16.67, 38.7, 0.63075, 35, -28.93, 39.18, 0.07457, 72, -577.44, -152.63, 0.0218, 7, 4, -11.31, 127.56, 0.03168, 3, 104.52, 128.03, 0.08047, 33, 44.1, 33.93, 0.4819, 34, -6.45, 33.34, 0.37674, 35, -52.14, 34.2, 0.00812, 31, -66.56, -250.72, 0, 72, -573.5, -129.23, 0.02109, 6, 4, 14.9, 123.34, 0.07395, 3, 130.4, 122.09, 0.05457, 33, 17.57, 33.04, 0.77077, 34, -32.57, 28.57, 0.08083, 31, -88.4, -235.63, 0, 72, -567.86, -103.29, 0.01989, 6, 4, 36.6, 118.87, 0.13894, 3, 151.76, 116.19, 0.03213, 33, -4.59, 33.25, 0.79385, 34, -54.52, 25.53, 0.01649, 31, -106.07, -222.25, 0, 72, -562.22, -81.86, 0.01859, 6, 4, -36.39, 72.83, 0.06657, 3, 75.88, 75.07, 0.44491, 33, 58.19, 92.46, 0.19081, 34, -1.09, 93.31, 0.25436, 35, -45.8, 94.07, 0.01418, 72, -520.2, -157.24, 0.02916, 7, 4, -8.45, 73.22, 0.19767, 3, 103.78, 73.62, 0.32867, 33, 30.85, 86.71, 0.28115, 34, -27.29, 83.61, 0.16236, 35, -72.16, 84.81, 0.00299, 31, -45.75, -200.44, 0, 72, -519.07, -129.32, 0.02716, 7, 4, 13.62, 72.44, 0.3449, 3, 125.75, 71.38, 0.19649, 33, 9.05, 83.23, 0.34889, 34, -48.36, 76.98, 0.08442, 35, -93.32, 78.52, 0.00011, 31, -65.34, -190.24, 0, 72, -517.1, -107.33, 0.0252, 6, 4, 41.05, 71.67, 0.49604, 3, 153.07, 68.8, 0.06855, 33, -18.02, 78.72, 0.38812, 34, -74.47, 68.54, 0.02493, 31, -89.77, -177.74, 0, 72, -514.85, -79.98, 0.02236, 5, 4, -26.94, 7.74, 0.00024, 3, 81.01, 9.51, 0.94823, 33, 36.42, 154.52, 0.00854, 34, -31.73, 151.5, 0.00872, 72, -454.7, -151.34, 0.03427, 5, 4, -0.8, 10.01, 0.41438, 3, 107.24, 10.04, 0.53567, 33, 11.2, 147.27, 0.01301, 34, -55.61, 140.63, 0.00615, 72, -455.55, -125.11, 0.03079, 6, 4, 21.1, 12.33, 0.9293, 3, 129.25, 10.91, 0.01789, 33, -9.85, 140.78, 0.02088, 34, -75.48, 131.13, 0.00407, 31, -46.23, -132.76, 0, 72, -456.68, -103.12, 0.02785, 6, 4, 45.36, 12.81, 0.95007, 3, 153.48, 9.78, 0.00044, 33, -33.56, 135.66, 0.02366, 34, -98.19, 122.59, 0.00119, 31, -68.33, -122.75, 0, 72, -455.83, -78.87, 0.02464, 6, 4, -31.63, 41.38, 0.06835, 3, 78.55, 43.38, 0.69904, 33, 47.48, 122.41, 0.0968, 34, -16.08, 121.37, 0.10191, 35, -60.32, 122.37, 0.00147, 72, -488.54, -154.2, 0.03242, 5, 4, -27.8, -127.47, 0.0021, 3, 71.2, -125.35, 0.06092, 31, 58.09, -27.62, 0.194, 32, 13.38, -29.11, 0.72059, 72, -319.74, -159.55, 0.02239, 5, 4, -6.86, -116.54, 0.01843, 3, 92.82, -115.83, 0.07497, 31, 34.48, -28.47, 0.58758, 32, -10.19, -27.59, 0.2964, 72, -329.51, -138.04, 0.02263, 6, 4, 11.5, -108.49, 0.05378, 3, 111.67, -109.01, 0.06603, 33, -23.64, 261.2, 0, 31, 14.45, -27.83, 0.79185, 32, -30.06, -24.94, 0.06614, 72, -336.55, -119.27, 0.02221, 6, 4, 34.26, -102.16, 0.13046, 3, 134.8, -104.2, 0.03501, 33, -44.76, 250.62, 0, 31, -8.82, -23.75, 0.80952, 32, -52.8, -18.54, 0.00417, 72, -341.63, -96.2, 0.02083, 5, 4, 52.4, -97.26, 0.23655, 3, 153.22, -100.51, 0.01619, 33, -61.61, 242.32, 1e-05, 31, -27.3, -20.37, 0.7278, 72, -345.54, -77.83, 0.01944, 5, 4, -20.68, -85.57, 0.06371, 3, 81.08, -84.02, 0.27974, 31, 33.63, -62.37, 0.3983, 32, -14.45, -61.23, 0.23114, 72, -361.19, -150.16, 0.02711, 5, 4, -24.18, -50.13, 0.09926, 3, 79.93, -48.42, 0.58568, 31, 21.53, -95.87, 0.20472, 32, -29.86, -93.34, 0.07913, 72, -396.77, -151.73, 0.0312, 6, 4, -0.47, -46.88, 0.31412, 3, 103.81, -46.74, 0.39047, 33, -0.05, 203.04, 0, 31, -1.27, -88.6, 0.22661, 32, -51.81, -83.81, 0.03973, 72, -398.72, -127.88, 0.02907, 6, 4, 22.94, -45.21, 0.56317, 3, 127.27, -46.63, 0.15187, 33, -22.7, 196.91, 0, 31, -23.11, -80.03, 0.24615, 32, -72.68, -73.09, 0.01236, 72, -399.11, -104.42, 0.02646, 6, 4, 47.08, -42.72, 0.71276, 3, 151.53, -45.74, 0.03298, 33, -45.92, 189.83, 0, 31, -45.98, -71.88, 0.22947, 32, -94.61, -62.69, 0.00118, 72, -400.29, -80.17, 0.02361, 5, 4, 82.47, -88.02, 0.44727, 3, 183.84, -93.28, 0.00156, 33, -89.35, 227.48, 2e-05, 31, -58.42, -15.76, 0.53436, 72, -353.13, -47.29, 0.0168, 5, 4, 76.37, -40.18, 0.79354, 3, 180.92, -45.15, 0.0014, 33, -74.17, 181.7, 1e-05, 31, -73.51, -61.57, 0.18508, 72, -401.23, -50.79, 0.01998, 5, 4, 75.79, 13.63, 0.95353, 33, -63.27, 129.01, 0.02586, 34, -126.6, 111.65, 2e-05, 31, -96.14, -110.39, 0, 72, -455, -48.44, 0.0206, 6, 4, 69.92, 69.66, 0.59943, 3, 181.75, 64.88, 0.01235, 33, -46.74, 75.14, 0.36608, 34, -102.36, 60.8, 0.00296, 31, -114.97, -163.49, 1e-05, 72, -511.27, -51.26, 0.01916, 6, 4, 63.31, 113.44, 0.25672, 3, 178.05, 109.01, 0.01094, 33, -31.85, 33.45, 0.71454, 34, -81.51, 21.73, 0.00122, 31, -127.85, -205.86, 1e-05, 72, -555.34, -55.48, 0.01658, 5, 4, 112.32, 88.84, 0.54911, 3, 225.32, 81.22, 0, 33, -84.66, 48.17, 0.43792, 31, -161.49, -162.56, 2e-05, 72, -528.12, -7.88, 0.01295, 4, 4, 117.34, 13.22, 0.96359, 33, -104.12, 121.42, 0.02132, 31, -133.47, -92.14, 0, 72, -452.33, -6.98, 0.01508, 4, 4, 121.06, -55.25, 0.76838, 33, -120.93, 187.9, 4e-05, 31, -107.36, -28.74, 0.21787, 72, -383.76, -6.98, 0.01371], "hull": 37, "edges": [0, 72, 0, 2, 6, 8, 12, 14, 20, 22, 30, 32, 32, 34, 34, 36, 40, 42, 48, 50, 50, 52, 52, 54, 60, 62, 62, 64, 54, 56, 56, 58, 58, 60, 2, 4, 4, 6, 8, 10, 10, 12, 44, 46, 46, 48, 42, 44, 36, 38, 38, 40, 22, 24, 28, 30, 24, 26, 26, 28, 18, 20, 14, 16, 16, 18, 64, 66, 66, 68, 68, 70, 70, 72], "width": 383, "height": 224}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04914, 0.38796, 0.05384, 0.85462, 0.59948, 0.93508, 0.94757, 0.64221, 0.93581, 0.07899, 0.4325, 0.06933], "triangles": [2, 0, 5, 3, 5, 4, 3, 2, 5, 2, 1, 0], "vertices": [2, 6, 55.38, -9.37, 0.96, 69, -332.3, 57.35, 0.04, 2, 6, 37.64, -9.7, 0.96, 69, -350.03, 57.02, 0.04, 2, 6, 34.72, -38.09, 0.96, 69, -352.95, 28.64, 0.04, 2, 6, 45.94, -56.14, 0.96875, 69, -341.74, 10.59, 0.03125, 2, 6, 67.33, -55.43, 0.9689, 69, -320.34, 11.3, 0.0311, 2, 6, 67.58, -29.25, 0.96, 69, -320.1, 37.47, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 52, "height": 38}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.05833, 0.12837, 0.03387, 0.66054, 0.3861, 0.93158, 0.95357, 0.82912, 0.95601, 0.40272, 0.55242, 0.07218], "triangles": [4, 2, 5, 5, 1, 0, 3, 2, 4, 2, 1, 5], "vertices": [2, 6, 63.05, 57.3, 0.96819, 69, -324.62, 124.03, 0.03181, 2, 6, 43.36, 58.43, 0.96816, 69, -344.32, 125.16, 0.03184, 2, 6, 33.41, 40.77, 0.96003, 69, -354.26, 107.5, 0.03997, 2, 6, 37.34, 12.42, 0.96, 69, -350.34, 79.14, 0.04, 2, 6, 53.12, 12.37, 0.96, 69, -334.56, 79.1, 0.04, 2, 6, 65.25, 32.61, 0.96, 69, -322.42, 99.33, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 50, "height": 37}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 36, -9.09, -8.75, 0.96274, 69, -342.42, 27.86, 0.03726, 2, 36, -9.17, 9.25, 0.96, 69, -342.5, 45.86, 0.04, 2, 36, 8.83, 9.33, 0.96, 69, -324.5, 45.94, 0.04, 2, 36, 8.91, -8.67, 0.96273, 69, -324.42, 27.94, 0.03727], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.72385, 0.02047, 0.93563, 0.0701, 0.99586, 0.2187, 0.87072, 0.39742, 0.93, 0.52995, 0.74264, 0.86055, 0.56349, 0.98717, 0.40021, 0.97898, 0.21707, 0.83127, 0.11551, 0.78434, 0.01996, 0.74253, 0.01925, 0.66978, 0.07467, 0.51368, 0.15081, 0.33556, 0.26416, 0.18973, 0.39645, 0.11121, 0.55821, 0.09113, 0.5759, 0.01977, 0.08729, 0.66483, 0.13183, 0.56387, 0.21101, 0.43346, 0.30999, 0.31988, 0.44857, 0.26099, 0.60199, 0.29043, 0.72419, 0.38589, 0.16819, 0.68286, 0.27089, 0.70389, 0.39709, 0.71651, 0.54308, 0.69127, 0.66186, 0.55245], "triangles": [7, 28, 6, 6, 28, 5, 8, 26, 7, 26, 27, 7, 7, 27, 28, 28, 29, 5, 5, 29, 4, 3, 29, 24, 29, 3, 4, 9, 25, 8, 8, 25, 26, 10, 18, 9, 9, 18, 25, 10, 11, 18, 26, 21, 27, 27, 22, 28, 27, 21, 22, 25, 20, 26, 26, 20, 21, 28, 23, 29, 28, 22, 23, 18, 19, 25, 25, 19, 20, 11, 12, 18, 18, 12, 19, 20, 19, 13, 29, 23, 24, 19, 12, 13, 21, 20, 14, 2, 3, 1, 23, 0, 24, 1, 3, 0, 3, 24, 0, 20, 13, 14, 21, 15, 22, 21, 14, 15, 22, 16, 23, 23, 16, 0, 0, 16, 17, 22, 15, 16], "vertices": [2, 6, 65.69, -48.69, 0.96654, 69, -321.98, 18.04, 0.03346, 2, 6, 64.25, -59.5, 0.97141, 69, -323.42, 7.23, 0.02859, 2, 6, 59.81, -62.59, 0.97293, 69, -327.87, 4.14, 0.02707, 2, 6, 54.42, -56.23, 0.97012, 69, -333.26, 10.5, 0.02988, 2, 6, 50.46, -59.27, 0.97145, 69, -337.22, 7.45, 0.02855, 2, 6, 40.49, -49.76, 0.96679, 69, -347.18, 16.96, 0.03321, 2, 6, 36.65, -40.65, 0.96249, 69, -351.02, 26.08, 0.03751, 2, 6, 36.86, -32.32, 0.96, 69, -350.82, 34.41, 0.04, 2, 6, 41.24, -22.96, 0.96, 69, -346.43, 43.77, 0.04, 2, 6, 42.63, -17.77, 0.96, 69, -345.05, 48.96, 0.04, 2, 6, 43.86, -12.89, 0.96, 69, -343.82, 53.83, 0.04, 2, 6, 46.04, -12.85, 0.96, 69, -341.63, 53.88, 0.04, 2, 6, 50.74, -15.65, 0.96, 69, -336.94, 51.08, 0.04, 2, 6, 56.1, -19.51, 0.96, 69, -331.57, 47.22, 0.04, 2, 6, 60.5, -25.27, 0.96, 69, -327.17, 41.46, 0.04, 2, 6, 62.89, -32, 0.96, 69, -324.79, 34.72, 0.04, 2, 6, 63.53, -40.25, 0.96287, 69, -324.14, 26.48, 0.03713, 2, 6, 65.68, -41.14, 0.96319, 69, -322, 25.58, 0.03681, 2, 6, 46.21, -16.31, 0.96, 69, -341.47, 50.41, 0.04, 2, 6, 49.25, -18.57, 0.96, 69, -338.43, 48.15, 0.04, 2, 6, 53.18, -22.59, 0.96, 69, -334.5, 44.13, 0.04, 2, 6, 56.61, -27.62, 0.96, 69, -331.07, 39.1, 0.04, 2, 6, 58.41, -34.68, 0.96048, 69, -329.27, 32.04, 0.03952, 2, 6, 57.56, -42.51, 0.96398, 69, -330.11, 24.22, 0.03602, 2, 6, 54.73, -48.76, 0.96677, 69, -332.95, 17.97, 0.03323, 2, 6, 45.69, -20.44, 0.96, 69, -341.99, 46.28, 0.04, 2, 6, 45.08, -25.68, 0.96, 69, -342.6, 41.04, 0.04, 2, 6, 44.73, -32.12, 0.96, 69, -342.94, 34.61, 0.04, 2, 6, 45.52, -39.56, 0.96252, 69, -342.15, 27.16, 0.03748, 2, 6, 49.72, -45.6, 0.96533, 69, -337.96, 21.12, 0.03467], "hull": 18, "edges": [2, 4, 8, 10, 18, 20, 20, 22, 32, 34, 30, 32, 2, 0, 0, 34, 26, 28, 28, 30, 22, 24, 24, 26, 14, 16, 16, 18, 10, 12, 12, 14, 4, 6, 6, 8, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 36, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 48], "width": 51, "height": 30}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 37, -9.28, -8.88, 0.96, 69, -342.73, 93.86, 0.04, 2, 37, -9.36, 9.12, 0.96182, 69, -342.81, 111.86, 0.03818, 2, 37, 8.64, 9.21, 0.96191, 69, -324.81, 111.94, 0.03809, 2, 37, 8.72, -8.79, 0.96, 69, -324.73, 93.94, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.47309, 0.11658, 0.61748, 0.13336, 0.76072, 0.21538, 0.86254, 0.36867, 0.93239, 0.53921, 0.98395, 0.6652, 0.98395, 0.75493, 0.90041, 0.78409, 0.79584, 0.88409, 0.67178, 0.96827, 0.48982, 0.97285, 0.36926, 0.93579, 0.14039, 0.67174, 0.20966, 0.5264, 0.00832, 0.2001, 0.05571, 0.06788, 0.29059, 0.01709, 0.43589, 0.02552, 0.91061, 0.67669, 0.87851, 0.57388, 0.82003, 0.43747, 0.72714, 0.33664, 0.59642, 0.26744, 0.48061, 0.2793, 0.37282, 0.33466, 0.2937, 0.45329, 0.35103, 0.58773, 0.44735, 0.6846, 0.57005, 0.72019, 0.69733, 0.71425, 0.81659, 0.69053], "triangles": [11, 27, 10, 10, 28, 9, 10, 27, 28, 9, 29, 8, 9, 28, 29, 11, 12, 26, 26, 12, 13, 13, 25, 26, 11, 26, 27, 8, 30, 7, 8, 29, 30, 7, 18, 6, 7, 30, 18, 18, 5, 6, 27, 23, 28, 29, 28, 21, 28, 23, 22, 30, 29, 20, 21, 28, 22, 30, 19, 18, 30, 20, 19, 20, 29, 21, 26, 24, 27, 27, 24, 23, 18, 4, 5, 18, 19, 4, 26, 25, 24, 4, 19, 3, 19, 20, 3, 25, 13, 15, 13, 14, 15, 15, 16, 25, 25, 16, 24, 20, 21, 3, 21, 2, 3, 2, 21, 1, 24, 0, 23, 24, 17, 0, 24, 16, 17, 23, 0, 22, 21, 22, 1, 22, 0, 1], "vertices": [2, 6, 61.49, 43.56, 0.96205, 69, -326.19, 110.28, 0.03795, 2, 6, 61.04, 36.33, 0.96, 69, -326.64, 103.06, 0.04, 2, 6, 58.69, 29.16, 0.96, 69, -328.98, 95.89, 0.04, 2, 6, 54.27, 24.05, 0.96, 69, -333.4, 90.78, 0.04, 2, 6, 49.34, 20.53, 0.96, 69, -338.33, 87.26, 0.04, 2, 6, 45.7, 17.94, 0.96, 69, -341.98, 84.66, 0.04, 2, 6, 43.1, 17.93, 0.96, 69, -344.58, 84.65, 0.04, 2, 6, 42.23, 22.1, 0.96, 69, -345.44, 88.83, 0.04, 2, 6, 39.31, 27.31, 0.96, 69, -348.37, 94.04, 0.04, 2, 6, 36.84, 33.5, 0.96, 69, -350.84, 100.23, 0.04, 2, 6, 36.66, 42.6, 0.96137, 69, -351.01, 109.33, 0.03863, 2, 6, 37.71, 48.63, 0.96381, 69, -349.97, 115.36, 0.03619, 2, 6, 45.31, 60.11, 0.96875, 69, -342.36, 126.84, 0.03125, 2, 6, 49.54, 56.67, 0.96747, 69, -338.13, 123.4, 0.03253, 2, 6, 58.96, 66.78, 0.9714, 69, -328.72, 133.51, 0.0286, 2, 6, 62.8, 64.43, 0.97028, 69, -324.87, 131.16, 0.02972, 2, 6, 64.33, 52.69, 0.96554, 69, -323.34, 119.42, 0.03446, 2, 6, 64.12, 45.43, 0.96268, 69, -323.55, 112.15, 0.03732, 2, 6, 45.35, 21.6, 0.96, 69, -342.33, 88.33, 0.04, 2, 6, 48.32, 23.22, 0.96, 69, -339.35, 89.95, 0.04, 2, 6, 52.26, 26.17, 0.96, 69, -335.41, 92.89, 0.04, 2, 6, 55.17, 30.82, 0.96, 69, -332.51, 97.55, 0.04, 2, 6, 57.14, 37.37, 0.96, 69, -330.53, 104.09, 0.04, 2, 6, 56.77, 43.16, 0.96203, 69, -330.9, 109.88, 0.03797, 2, 6, 55.14, 48.54, 0.96421, 69, -332.53, 115.27, 0.03579, 2, 6, 51.68, 52.48, 0.96581, 69, -335.99, 119.21, 0.03419, 2, 6, 47.8, 49.59, 0.96462, 69, -339.88, 116.32, 0.03538, 2, 6, 45.01, 44.76, 0.96262, 69, -342.66, 111.49, 0.03738, 2, 6, 44.01, 38.63, 0.96015, 69, -343.67, 105.35, 0.03985, 2, 6, 44.21, 32.26, 0.96, 69, -343.47, 98.99, 0.04, 2, 6, 44.93, 26.3, 0.96, 69, -342.75, 93.03, 0.04], "hull": 18, "edges": [8, 10, 10, 12, 18, 20, 28, 30, 0, 34, 30, 32, 32, 34, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 24, 26, 26, 28, 20, 22, 22, 24, 14, 16, 16, 18, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 36], "width": 50, "height": 29}}, "gun": {"gun": {"type": "mesh", "uvs": [0.78068, 0.00193, 0.9276, 0.01596, 0.99673, 0.0358, 0.9848, 0.10942, 0.92421, 0.26478, 0.68883, 0.50931, 0.63512, 0.57841, 0.36036, 0.99206, 0.31777, 0.99772, 0.1828, 0.99881, 0.03762, 0.9869, 0, 0.97605, 0.1903, 0.55592, 0.19597, 0.48544, 0.31908, 0.22937, 0.44011, 0.08138, 0.50962, 0.02532, 0.60239, 1e-05], "triangles": [3, 1, 2, 16, 17, 0, 0, 15, 16, 3, 0, 1, 3, 15, 0, 3, 14, 15, 4, 14, 3, 13, 14, 4, 5, 13, 4, 12, 13, 5, 6, 12, 5, 11, 12, 6, 10, 11, 9, 6, 7, 11, 11, 7, 9, 7, 8, 9], "vertices": [1, 24, -59.09, 3.83, 1, 1, 24, -56.24, 19.32, 1, 1, 24, -49.99, 27.4, 1, 1, 24, -22.47, 30.62, 1, 1, 24, 36.17, 33.88, 1, 1, 24, 130.73, 25.05, 1, 1, 24, 157.24, 23.83, 1, 1, 24, 315.21, 21.2, 1, 1, 24, 318, 17.29, 1, 1, 24, 320.57, 3.9, 1, 1, 24, 318.48, -11.29, 1, 1, 24, 315.06, -15.69, 1, 1, 24, 156.05, -21.87, 1, 1, 24, 129.8, -25.53, 1, 1, 24, 32.76, -28.58, 1, 1, 24, -24.12, -25.37, 1, 1, 24, -46.05, -21.8, 1, 1, 24, -56.93, -14.06, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 18, 20, 20, 22, 32, 34, 24, 26, 8, 10, 30, 32, 26, 28, 28, 30, 22, 24, 16, 18, 14, 16], "width": 101, "height": 376}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.829, 0.01108, 0.94105, 0.05123, 0.98904, 0.10235, 0.9874, 0.18674, 0.95089, 0.2195, 0.88507, 0.25499, 0.87971, 0.20146, 0.84675, 0.16554, 0.80321, 0.14686, 0.77617, 0.1587, 0.74772, 0.21861, 0.74282, 0.30202, 0.76018, 0.36957, 0.79934, 0.42804, 0.86169, 0.48926, 0.76694, 0.49247, 0.72487, 0.44508, 0.69624, 0.39327, 0.67878, 0.48267, 0.62153, 0.56656, 0.53918, 0.62303, 0.38968, 0.64881, 0.26425, 0.68245, 0.26418, 0.78572, 0.30237, 0.82382, 0.3596, 0.85226, 0.43101, 0.86201, 0.42219, 0.91661, 0.34578, 0.99268, 0.24626, 0.99853, 0.16918, 0.972, 0.09298, 0.91132, 0.02602, 0.82778, 0.00285, 0.6832, 0, 0.52755, 0.02491, 0.39724, 0.10462, 0.26003, 0.18789, 0.15272, 0.3162, 0.07846, 0.49464, 0.02429, 0.66727, 0.00572, 0.8182, 0.06041, 0.72069, 0.06071, 0.63768, 0.11434, 0.5722, 0.17288, 0.51924, 0.25215, 0.47873, 0.36416, 0.44709, 0.48254, 0.40233, 0.59824, 0.19393, 0.68606, 0.19238, 0.56775, 0.21109, 0.43324, 0.25786, 0.30247, 0.34206, 0.1991, 0.4512, 0.11815, 0.5907, 0.0671, 0.89601, 0.09331, 0.9314, 0.16476], "triangles": [27, 25, 26, 24, 29, 30, 28, 25, 27, 24, 25, 29, 28, 29, 25, 31, 32, 23, 31, 23, 24, 30, 31, 24, 48, 22, 50, 49, 33, 50, 22, 49, 50, 23, 49, 22, 32, 33, 49, 32, 49, 23, 35, 36, 51, 47, 51, 46, 34, 35, 51, 50, 34, 51, 50, 51, 47, 33, 34, 50, 52, 37, 53, 36, 37, 52, 52, 53, 46, 51, 36, 52, 51, 52, 46, 54, 38, 39, 53, 38, 54, 37, 38, 53, 53, 54, 45, 46, 53, 45, 55, 39, 40, 54, 39, 55, 44, 54, 55, 44, 55, 43, 19, 47, 18, 48, 50, 47, 20, 48, 47, 19, 20, 47, 21, 48, 20, 21, 22, 48, 17, 45, 11, 17, 11, 12, 46, 45, 17, 16, 17, 12, 16, 12, 13, 18, 46, 17, 47, 46, 18, 15, 16, 13, 15, 13, 14, 45, 54, 44, 10, 45, 44, 11, 45, 10, 9, 43, 42, 7, 8, 56, 56, 1, 2, 57, 56, 2, 57, 7, 56, 3, 57, 2, 6, 7, 57, 4, 57, 3, 6, 57, 4, 5, 6, 4, 56, 41, 1, 0, 42, 40, 41, 0, 1, 41, 42, 0, 8, 42, 41, 8, 41, 56, 8, 9, 42, 10, 43, 9, 55, 40, 42, 44, 43, 10, 43, 55, 42], "vertices": [2, 56, 33.56, 10.56, 0.99936, 70, -162.75, 56.11, 0.00064, 1, 56, 27.04, -4.15, 1, 1, 56, 18.69, -10.48, 1, 1, 56, 4.85, -10.33, 1, 1, 56, -0.55, -5.57, 1, 1, 56, -6.41, 3.03, 1, 1, 56, 2.37, 3.77, 1, 2, 56, 8.24, 8.12, 0.99777, 69, -248.69, 53.38, 0.00223, 4, 56, 11.27, 13.83, 0.94606, 57, 3.33, 7.03, 0.04945, 58, -6.98, 13.62, 7e-05, 69, -245.65, 59.09, 0.00442, 4, 56, 9.32, 17.37, 0.72587, 57, 7.18, 5.8, 0.25573, 58, -4.56, 10.39, 0.0121, 69, -247.61, 62.63, 0.0063, 4, 56, -0.53, 21.05, 0.45684, 57, 16.87, 9.88, 0.13183, 58, 5.69, 8.07, 0.40186, 69, -257.45, 66.31, 0.00948, 4, 56, -14.21, 21.62, 0.28118, 58, 19.33, 9.35, 0.59979, 59, -6.14, 9.49, 0.10686, 69, -271.13, 66.89, 0.01218, 4, 56, -25.28, 19.3, 0.1507, 58, 29.98, 13.15, 0.1066, 59, 4.6, 13.05, 0.72828, 69, -282.2, 64.56, 0.01443, 3, 56, -34.84, 14.12, 0.01665, 59, 13.52, 19.26, 0.96697, 69, -291.76, 59.38, 0.01637, 2, 59, 22.54, 28.54, 0.982, 69, -301.76, 51.17, 0.018, 2, 59, 24.51, 16.28, 0.98161, 69, -302.35, 63.58, 0.01839, 2, 59, 17.44, 9.9, 0.98345, 69, -294.6, 69.13, 0.01655, 2, 59, 9.44, 5.18, 0.98528, 69, -286.13, 72.92, 0.01472, 3, 59, 24.27, 4.62, 0.89462, 60, -2.97, 3.79, 0.08859, 69, -300.8, 75.14, 0.01679, 2, 60, 12.7, 3.81, 0.98374, 69, -314.59, 82.57, 0.01626, 5, 60, 26, -1.22, 0.9536, 63, 44.59, 71.67, 0.00151, 64, 40.36, 59.55, 0.01224, 65, 17.33, 57.08, 0.01841, 69, -323.9, 93.31, 0.01425, 6, 59, 55.75, -29.81, 0.00675, 60, 39.1, -16.38, 0.53816, 63, 58.12, 56.89, 0.01467, 64, 44.99, 40.05, 0.14461, 65, 17.28, 37.05, 0.2854, 69, -328.22, 112.88, 0.01041, 7, 59, 63.15, -45.48, 0.00059, 60, 51.83, -28.15, 0.14125, 63, 71.17, 45.48, 0.00282, 64, 50.83, 23.74, 0.09195, 65, 19.16, 19.82, 0.72163, 66, -4.07, 23.89, 0.03466, 69, -333.82, 129.28, 0.00711, 6, 60, 66.69, -20.04, 0.00679, 64, 67.77, 24.07, 0.00039, 65, 35.71, 16.19, 0.28912, 66, 9.31, 13.5, 0.64629, 67, -3.06, 19.02, 0.05126, 69, -350.75, 129.21, 0.00615, 5, 60, 69.78, -12.65, 0.0004, 65, 42.88, 19.75, 0.04108, 66, 17.31, 13.63, 0.56646, 67, 3.46, 14.38, 0.38559, 69, -356.98, 124.18, 0.00647, 4, 65, 49.04, 26.08, 0.00063, 66, 25.59, 16.69, 0.11675, 67, 11.94, 11.93, 0.87545, 69, -361.61, 116.66, 0.00717, 3, 66, 32.59, 23.1, 0.00213, 67, 21.38, 12.95, 0.98951, 69, -363.16, 107.3, 0.00836, 2, 67, 22.71, 4.02, 0.99307, 69, -372.12, 108.41, 0.00693, 2, 67, 16.48, -10.71, 0.9962, 69, -384.64, 118.36, 0.0038, 4, 66, 35.45, -9.74, 0.16481, 67, 4.2, -15.19, 0.83073, 69, -385.66, 131.4, 0.00192, 70, -325.05, 131.68, 0.00255, 4, 66, 25.82, -15.05, 0.66186, 67, -6.7, -13.76, 0.33374, 69, -381.36, 141.51, 0.00099, 70, -320.75, 141.8, 0.00341, 5, 65, 51.05, -10.11, 0.00415, 66, 11.84, -16.84, 0.98475, 67, -19.02, -6.91, 0.00518, 69, -371.46, 151.54, 0.00045, 70, -310.85, 151.83, 0.00547, 4, 65, 35.79, -15.76, 0.44521, 66, -4.36, -15.38, 0.54624, 69, -357.8, 160.38, 4e-05, 70, -297.19, 160.67, 0.0085, 4, 64, 51.65, -10.5, 0.00128, 65, 11.98, -13.67, 0.98361, 69, -334.1, 163.53, 0.00054, 70, -273.49, 163.81, 0.01458, 4, 64, 26.14, -11.39, 0.96638, 65, -13.04, -8.58, 0.01292, 69, -308.58, 164.02, 0.00048, 70, -247.97, 164.31, 0.02023, 4, 63, 46.6, -5.18, 0.02169, 64, 4.7, -8.56, 0.95485, 69, -287.19, 160.86, 0.00045, 70, -226.58, 161.14, 0.02301, 3, 63, 21.9, -7.52, 0.9778, 69, -264.64, 150.52, 0.00065, 70, -204.03, 150.81, 0.02155, 4, 62, 35.84, -5.85, 0.13192, 63, 1.2, -6.98, 0.85049, 69, -246.99, 139.7, 0.00039, 70, -186.38, 139.98, 0.01719, 3, 62, 15.13, -7.13, 0.98606, 69, -234.73, 122.95, 0.0009, 70, -174.12, 123.23, 0.01305, 4, 56, 31.19, 54.35, 0.22153, 61, 37.39, -5.79, 0.76847, 69, -225.74, 99.61, 0.00168, 70, -165.13, 99.9, 0.00831, 4, 56, 34.34, 31.75, 0.72581, 61, 14.58, -5.64, 0.26741, 69, -222.59, 77.01, 0.00232, 70, -161.98, 77.3, 0.00446, 2, 56, 25.46, 11.94, 0.99615, 69, -231.46, 57.2, 0.00385, 4, 56, 25.35, 24.71, 0.78324, 57, 0.55, -10.54, 0.04836, 61, 8.91, 4.27, 0.16413, 69, -231.57, 69.97, 0.00428, 7, 56, 16.5, 35.54, 0.33171, 57, 14.41, -12.35, 0.23027, 58, -9.23, -8.59, 0.08116, 61, 20.91, 11.46, 0.34662, 62, -17.29, 20.38, 0.00445, 63, -33.95, 40.72, 0.00013, 69, -240.42, 80.8, 0.00565, 10, 56, 6.86, 44.08, 0.1432, 57, 27.28, -11.95, 0.05786, 58, 1.48, -15.74, 0.42328, 59, -24.56, -15.18, 0.00739, 60, -40.85, -32.83, 4e-05, 61, 30.75, 19.76, 0.26285, 62, -4.91, 23.9, 0.0876, 63, -21.34, 38.16, 0.01073, 64, -33.53, 62.38, 0.00016, 69, -250.06, 89.34, 0.00691, 9, 56, -6.17, 50.95, 0.0244, 58, 15.32, -20.8, 0.49278, 59, -10.84, -20.55, 0.11867, 60, -26.12, -32.68, 0.00969, 61, 39.44, 31.66, 0.08018, 62, 7.91, 31.16, 0.18249, 63, -6.61, 38.73, 0.07587, 64, -20.4, 55.71, 0.00743, 69, -263.09, 96.21, 0.00849, 9, 58, 34.25, -23.48, 0.16136, 59, 8.03, -23.68, 0.40632, 60, -7.45, -28.53, 0.11902, 61, 47.28, 49.1, 0.00431, 62, 22.23, 43.84, 0.08564, 63, 11.93, 43.41, 0.15485, 64, -1.92, 50.77, 0.05417, 65, -25.84, 58.4, 0.00388, 69, -281.49, 101.43, 0.01044, 8, 58, 54.06, -24.87, 0.01346, 59, 27.79, -25.52, 0.21949, 60, 11.57, -22.86, 0.47838, 62, 36.12, 58.02, 0.01524, 63, 30.78, 49.63, 0.09373, 64, 17.57, 47.02, 0.12188, 65, -7.76, 50.21, 0.0463, 69, -300.92, 105.49, 0.01151, 8, 58, 73.66, -28.03, 0, 59, 47.32, -29.13, 0.02129, 60, 31.03, -18.9, 0.59823, 62, 51.23, 70.91, 0.0005, 63, 50.12, 54.14, 0.02377, 64, 36.66, 41.54, 0.14219, 65, 9.53, 40.43, 0.20323, 69, -319.92, 111.26, 0.01079, 7, 59, 64.82, -54.56, 3e-05, 60, 56.76, -35.95, 0.05112, 63, 76.33, 37.83, 0.00029, 64, 51.61, 14.54, 0.03614, 65, 17.78, 10.69, 0.8854, 66, -9.24, 16.25, 0.02169, 69, -334.45, 138.49, 0.00533, 7, 58, 72.56, -55.96, 5e-05, 59, 45.57, -57.03, 0.00874, 60, 39.83, -45.43, 0.09918, 63, 59.68, 27.87, 0.01759, 64, 32.22, 13.94, 0.51861, 65, -1.22, 14.63, 0.35039, 69, -315.05, 138.79, 0.00544, 8, 58, 50.37, -56.62, 0.00661, 59, 23.38, -57.17, 0.03956, 60, 19.3, -53.86, 0.07743, 62, 57.9, 34.64, 0.0046, 63, 39.39, 18.85, 0.25934, 64, 10.11, 15.95, 0.58367, 65, -22.25, 21.73, 0.02347, 69, -292.98, 136.44, 0.00532, 8, 58, 28.28, -53.55, 0.03135, 59, 1.36, -53.59, 0.04836, 60, -2.46, -58.77, 0.0272, 62, 41.25, 19.81, 0.06422, 63, 17.79, 13.33, 0.7648, 64, -11.45, 21.64, 0.05904, 65, -41.9, 32.29, 0.00032, 69, -271.51, 130.41, 0.00471, 8, 58, 9.95, -45, 0.06823, 59, -16.77, -44.62, 0.02945, 60, -22.62, -57.22, 0.00521, 61, 61.21, 19.79, 0.01129, 62, 22.86, 11.39, 0.6478, 63, -2.42, 14.3, 0.22999, 64, -28.62, 32.33, 0.00376, 69, -254.5, 119.47, 0.00427, 9, 56, 15.77, 59.97, 0.09028, 57, 31.7, -29.62, 0.00168, 58, -5.19, -32.7, 0.06921, 59, -31.62, -31.97, 0.00449, 60, -41.13, -51.04, 3e-05, 61, 45.19, 8.65, 0.30796, 62, 3.67, 7.83, 0.5153, 63, -21.09, 19.94, 0.00716, 69, -241.16, 105.23, 0.00391, 6, 56, 24.22, 41.73, 0.25971, 57, 13.04, -22.15, 0.02179, 58, -16.04, -15.77, 0.01576, 61, 25.92, 2.92, 0.69801, 62, -16.24, 10.54, 0.00087, 69, -232.7, 86.99, 0.00385, 2, 56, 20.11, 1.72, 0.99685, 69, -236.81, 46.98, 0.00315, 2, 56, 8.42, -2.97, 0.99794, 69, -248.5, 42.29, 0.00206], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 28, 30, 44, 46, 56, 58, 58, 60, 68, 70, 78, 80, 4, 6, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 34, 36, 30, 32, 32, 34, 24, 26, 26, 28, 74, 76, 76, 78, 70, 72, 72, 74, 64, 66, 66, 68, 60, 62, 62, 64, 52, 54, 54, 56, 50, 52, 46, 48, 48, 50, 40, 42, 42, 44, 36, 38, 38, 40, 6, 8, 8, 10, 12, 14, 14, 16], "width": 131, "height": 164}}, "hair_FL": {"hair_FL": {"type": "mesh", "uvs": [0.19816, 0.06335, 0.34194, 0.138, 0.44553, 0.22821, 0.54768, 0.28214, 0.75733, 0.33985, 0.90743, 0.41423, 0.98756, 0.5036, 0.98511, 0.61526, 0.91031, 0.69365, 0.74613, 0.75231, 0.73731, 0.83015, 0.66832, 0.88125, 0.45997, 0.89648, 0.30313, 0.96306, 0.18809, 0.98779, 0.08504, 0.99044, 0.05834, 0.98038, 0.14831, 0.95617, 0.21496, 0.91998, 0.25295, 0.85936, 0.25233, 0.77016, 0.22362, 0.67081, 0.17169, 0.57441, 0.07973, 0.45794, 0.01197, 0.3519, 0.01864, 0.2531, 0.06886, 0.15961, 0.07485, 0.07916, 0.06511, 0, 0.21186, 0.14704, 0.24722, 0.23836, 0.32185, 0.32183, 0.42986, 0.40462, 0.53984, 0.49103, 0.64001, 0.57647, 0.71856, 0.67957, 0.50646, 0.84793, 0.5202, 0.77997, 0.51235, 0.70043, 0.47034, 0.62116], "triangles": [15, 17, 14, 15, 16, 17, 13, 14, 18, 14, 17, 18, 12, 13, 19, 13, 18, 19, 12, 36, 11, 12, 19, 36, 11, 36, 10, 19, 20, 36, 36, 37, 10, 36, 20, 37, 10, 37, 9, 20, 38, 37, 37, 38, 9, 20, 21, 38, 38, 35, 9, 9, 35, 8, 21, 39, 38, 38, 39, 35, 8, 35, 7, 39, 34, 35, 35, 34, 7, 21, 22, 39, 39, 33, 34, 39, 22, 33, 7, 34, 6, 34, 5, 6, 34, 33, 5, 22, 32, 33, 22, 23, 32, 33, 4, 5, 33, 32, 4, 23, 24, 31, 23, 31, 32, 24, 30, 31, 32, 3, 4, 32, 31, 3, 30, 24, 25, 31, 2, 3, 31, 30, 2, 25, 26, 30, 26, 29, 30, 30, 29, 1, 26, 27, 29, 27, 0, 29, 27, 28, 0, 29, 0, 1, 30, 1, 2], "vertices": [2, 44, -2.41, -6.46, 0.98314, 70, -213.45, -21.99, 0.01686, 2, 44, -14.01, -17.73, 0.95572, 70, -225.05, -33.26, 0.04428, 5, 44, -28.04, -25.88, 0.596, 45, 17.71, 19.16, 0.22654, 46, -3.87, 20.96, 0.13219, 47, -42.5, 12.22, 8e-05, 70, -239.08, -41.41, 0.04518, 5, 44, -36.42, -33.89, 0.4454, 45, 26.99, 26.09, 0.10283, 46, 6.93, 25.16, 0.39384, 47, -32.84, 18.61, 0.01327, 70, -247.46, -49.41, 0.04467, 5, 44, -45.34, -50.28, 0.14724, 45, 37.83, 41.29, 0.00946, 46, 21.45, 36.89, 0.60877, 47, -21.14, 33.16, 0.18604, 70, -256.38, -65.81, 0.04849, 4, 44, -56.89, -62.04, 0.00482, 46, 36.62, 43.34, 0.50302, 47, -7.68, 42.68, 0.45541, 70, -267.93, -77.57, 0.03675, 4, 46, 51.89, 43.85, 0.31811, 47, 7.13, 46.42, 0.64499, 48, -42.15, 31.78, 0.00413, 70, -281.84, -83.89, 0.03277, 4, 46, 67.94, 37.09, 0.14897, 47, 24.25, 43.22, 0.77181, 48, -25.24, 35.98, 0.05237, 70, -299.26, -83.78, 0.02685, 4, 46, 77.06, 27.06, 0.06702, 47, 35.29, 35.35, 0.75292, 48, -11.94, 33.42, 0.15594, 70, -311.52, -78, 0.02411, 4, 46, 80.68, 11.74, 0.00864, 47, 42.08, 21.16, 0.40804, 48, 0.14, 23.33, 0.56459, 70, -320.73, -65.24, 0.01873, 3, 47, 53.92, 18.38, 0.06649, 48, 12.07, 25.72, 0.9102, 70, -332.87, -64.61, 0.02331, 3, 47, 60.84, 11.7, 0.00698, 48, 21.14, 22.52, 0.96641, 70, -340.87, -59.26, 0.02661, 3, 48, 27.53, 7.39, 0.71429, 49, -2.77, 6.95, 0.27006, 70, -343.32, -43.02, 0.01565, 2, 49, 13.23, 5.8, 0.98996, 70, -353.77, -30.84, 0.01004, 2, 49, 22.34, 2.27, 0.98958, 70, -357.67, -21.88, 0.01042, 2, 49, 28.37, -3.06, 0.98801, 70, -358.12, -13.85, 0.01199, 2, 49, 28.75, -5.64, 0.98757, 70, -356.56, -11.76, 0.01243, 2, 49, 21.1, -3.42, 0.98884, 70, -352.75, -18.76, 0.01116, 3, 48, 35.89, -10.18, 0.00193, 49, 13.43, -3.81, 0.98771, 70, -347.08, -23.93, 0.01036, 3, 48, 25.99, -9.69, 0.42209, 49, 4.7, -8.49, 0.56779, 70, -337.61, -26.85, 0.01012, 4, 47, 38.15, -17.26, 0.10293, 48, 12.54, -13.25, 0.88263, 49, -5.01, -18.45, 0.00453, 70, -323.69, -26.74, 0.00991, 4, 46, 53.5, -21.17, 0.00478, 47, 22.5, -16.78, 0.77685, 48, -1.9, -19.32, 0.20856, 70, -308.21, -24.42, 0.0098, 4, 46, 38.05, -19.24, 0.28232, 47, 6.99, -18.17, 0.70449, 48, -15.43, -27.02, 0.00361, 70, -293.19, -20.3, 0.00958, 3, 46, 18.51, -19, 0.93414, 47, -12.15, -22.09, 0.05676, 70, -275.05, -13.04, 0.0091, 4, 44, -47.5, 7.85, 0.06977, 45, 32.95, -16.67, 0.21397, 46, 1.2, -17.64, 0.70839, 70, -258.53, -7.68, 0.00787, 4, 44, -32.08, 7.4, 0.44637, 45, 17.7, -14.36, 0.50151, 46, -12.87, -11.33, 0.04652, 70, -243.12, -8.13, 0.0056, 3, 44, -17.48, 3.55, 0.83277, 45, 3.67, -8.78, 0.16422, 70, -228.52, -11.98, 0.00301, 2, 44, -4.93, 3.14, 0.99916, 70, -215.96, -12.38, 0.00084, 1, 44, 7.42, 3.96, 1, 3, 44, -15.46, -7.59, 0.85587, 45, 3.02, 2.53, 0.13244, 70, -226.5, -23.12, 0.01169, 4, 44, -29.7, -10.42, 0.47492, 45, 17.49, 3.61, 0.49401, 46, -8.25, 6.05, 0.01866, 70, -240.74, -25.95, 0.01241, 4, 44, -42.69, -16.3, 0.22832, 45, 31.1, 7.88, 0.11114, 46, 6, 6.51, 0.64543, 70, -253.73, -31.83, 0.01512, 4, 45, 44.9, 14.75, 0.00857, 46, 21.14, 9.42, 0.95901, 47, -15.61, 6.25, 0.01401, 70, -266.6, -40.31, 0.0184, 3, 46, 36.87, 12.27, 0.52038, 47, -0.85, 12.37, 0.46242, 70, -280.04, -48.96, 0.0172, 4, 46, 52.16, 14.46, 0.12743, 47, 13.63, 17.75, 0.84481, 48, -24.32, 8.41, 0.01095, 70, -293.33, -56.83, 0.01681, 4, 46, 69.37, 14.05, 0.03094, 47, 30.53, 21, 0.71613, 48, -10.3, 18.39, 0.23536, 70, -309.39, -63.03, 0.01758, 3, 48, 19.29, 8.99, 0.98222, 49, -10.67, 4.09, 0.00021, 70, -335.73, -46.61, 0.01757, 3, 47, 43.28, 3.05, 0.01789, 48, 8.76, 7.36, 0.96452, 70, -325.13, -47.64, 0.01759, 4, 46, 66.29, -2.08, 0.00014, 47, 30.95, 4.6, 0.73611, 48, -3.1, 3.64, 0.24661, 70, -312.72, -46.97, 0.01714, 3, 46, 53.61, -0.43, 0.00075, 47, 18.21, 3.51, 0.98355, 70, -300.37, -43.63, 0.01571], "hull": 29, "edges": [4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 46, 48, 54, 56, 52, 54, 56, 0, 0, 2, 2, 4, 48, 50, 50, 52, 38, 40, 40, 42, 34, 36, 36, 38, 20, 22, 14, 16, 16, 18, 18, 20, 12, 14, 10, 12], "width": 78, "height": 156}}, "hair_FR": {"hair_FR": {"type": "mesh", "uvs": [0.99358, 0.00303, 0.91278, 0.0686, 0.84529, 0.14023, 0.78562, 0.2253, 0.73238, 0.31172, 0.69927, 0.41222, 0.67822, 0.50853, 0.66096, 0.59462, 0.63207, 0.6836, 0.58982, 0.78196, 0.59222, 0.8499, 0.65059, 0.92041, 0.72044, 0.99145, 0.66943, 0.99117, 0.53776, 0.97943, 0.41239, 0.94908, 0.30685, 0.92856, 0.22074, 0.87508, 0.1596, 0.79411, 0.06681, 0.76475, 0.00734, 0.69506, 0.00783, 0.6009, 0.08158, 0.52169, 1e-05, 0.45446, 0.10148, 0.41458, 0.14635, 0.43997, 0.21758, 0.4415, 0.32925, 0.41824, 0.35654, 0.35756, 0.43059, 0.29751, 0.53996, 0.23996, 0.64267, 0.16371, 0.73301, 0.0937, 0.8315, 0.04231, 0.52556, 0.43133, 0.57189, 0.35979, 0.45991, 0.50248, 0.34408, 0.55554, 0.26106, 0.62394, 0.19735, 0.70767, 0.46301, 0.91179, 0.40316, 0.82924, 0.41088, 0.73844, 0.45336, 0.64882, 0.50355, 0.5757], "triangles": [40, 10, 11, 15, 16, 40, 14, 40, 11, 15, 40, 14, 13, 11, 12, 14, 11, 13, 9, 42, 8, 42, 18, 39, 41, 42, 9, 41, 18, 42, 41, 9, 10, 17, 18, 41, 40, 41, 10, 16, 17, 41, 16, 41, 40, 25, 22, 23, 25, 23, 24, 37, 27, 36, 26, 27, 37, 44, 36, 6, 37, 36, 44, 7, 44, 6, 37, 22, 26, 43, 37, 44, 38, 37, 43, 8, 44, 7, 43, 44, 8, 38, 22, 37, 26, 22, 25, 38, 21, 22, 39, 21, 38, 20, 21, 39, 42, 38, 43, 42, 43, 8, 39, 38, 42, 19, 20, 39, 18, 19, 39, 35, 30, 4, 29, 30, 35, 5, 35, 4, 35, 28, 29, 34, 35, 5, 34, 28, 35, 27, 28, 34, 36, 27, 34, 6, 34, 5, 36, 34, 6, 4, 30, 3, 3, 30, 31, 2, 31, 32, 32, 33, 2, 1, 33, 0, 2, 33, 1, 3, 31, 2], "vertices": [1, 50, 8.75, -11.86, 1, 2, 50, -3.43, -2.79, 0.99927, 69, -251.58, 129.14, 0.00073, 4, 50, -16.71, 4.78, 0.7781, 51, 4.46, 6.38, 0.21961, 69, -264.87, 136.71, 0.00223, 70, -204.26, 136.99, 6e-05, 3, 50, -32.48, 11.45, 0.44937, 51, 21.56, 7.38, 0.54688, 69, -280.64, 143.38, 0.00374, 4, 50, -48.5, 17.39, 0.11179, 51, 38.55, 9.15, 0.44253, 52, -0.66, 9.15, 0.44053, 69, -296.66, 149.32, 0.00514, 3, 52, 17.67, 14.04, 0.98258, 53, -17.58, 20.34, 0.01052, 69, -315.27, 152.97, 0.0069, 3, 52, 34.69, 19.8, 0.56615, 53, 0.39, 20.65, 0.42538, 69, -333.09, 155.27, 0.00847, 4, 52, 49.84, 25.1, 0.0782, 53, 16.43, 21.11, 0.90765, 54, -16.68, 29.44, 0.00478, 69, -349.03, 157.14, 0.00937, 3, 53, 33.19, 20.35, 0.79375, 54, -1.4, 22.49, 0.19686, 69, -365.51, 160.33, 0.0094, 4, 53, 51.9, 18.35, 0.10357, 54, 15.22, 13.67, 0.88252, 55, -7.12, 18.76, 0.00534, 69, -383.73, 165.02, 0.00857, 4, 53, 64.29, 20.51, 5e-05, 54, 27.51, 11.06, 0.56386, 55, 2.75, 10.98, 0.42765, 69, -396.29, 164.69, 0.00843, 3, 54, 41.72, 14.49, 0.00256, 55, 17.01, 7.77, 0.98879, 69, -409.31, 158.03, 0.00865, 2, 55, 32.17, 5.49, 0.99155, 69, -422.41, 150.07, 0.00845, 2, 55, 28.46, 1.08, 0.99254, 69, -422.39, 155.84, 0.00746, 2, 55, 17.32, -9.02, 0.99476, 69, -420.29, 170.73, 0.00524, 3, 54, 40.72, -12.93, 0.32983, 55, 3.98, -16.38, 0.66701, 69, -414.74, 184.92, 0.00316, 4, 53, 83.51, -9.2, 0.00777, 54, 34.29, -23.67, 0.74991, 55, -6.54, -23.16, 0.24122, 69, -411, 196.86, 0.0011, 4, 53, 75.18, -20.3, 0.08478, 54, 22.43, -30.87, 0.86584, 55, -20.36, -24.38, 0.04931, 70, -340.54, 206.93, 8e-05, 4, 53, 61.41, -29.38, 0.40375, 54, 6.27, -34.17, 0.59323, 55, -36.32, -20.18, 0.00031, 70, -325.59, 213.91, 0.00271, 3, 53, 57.61, -40.56, 0.61553, 54, -1.42, -43.13, 0.37934, 70, -320.21, 224.42, 0.00513, 3, 53, 45.87, -49.13, 0.75694, 54, -15.51, -46.72, 0.23551, 70, -307.35, 231.2, 0.00755, 4, 52, 83.57, -40.55, 7e-05, 53, 28.64, -51.69, 0.88132, 54, -32.45, -42.67, 0.10949, 70, -289.93, 231.22, 0.00913, 4, 52, 66.74, -39.57, 0.01799, 53, 12.9, -45.64, 0.95064, 54, -44.81, -31.2, 0.02229, 70, -275.24, 222.96, 0.00909, 4, 52, 59.67, -53.34, 0.02122, 53, 1.98, -56.62, 0.96944, 54, -59.02, -37.33, 4e-05, 70, -262.84, 232.24, 0.00929, 3, 52, 47.98, -46.33, 0.03304, 53, -7.03, -46.39, 0.95763, 70, -255.41, 220.8, 0.00933, 4, 52, 49.95, -39.7, 0.0556, 53, -3.14, -40.67, 0.9348, 54, -57.85, -20.62, 0.0003, 70, -260.08, 215.71, 0.0093, 3, 52, 46.63, -32.36, 0.15475, 53, -4.07, -32.67, 0.83742, 70, -260.33, 207.66, 0.00784, 3, 52, 37.19, -22.96, 0.58927, 53, -10.22, -20.84, 0.4047, 70, -255.97, 195.06, 0.00603, 3, 52, 25.76, -25.16, 0.92838, 53, -21.78, -19.47, 0.06504, 70, -244.73, 192.03, 0.00657, 5, 50, -46.03, 51.5, 0.09025, 51, 51.45, -22.53, 0.02528, 52, 12.09, -22.58, 0.87795, 53, -34.01, -12.86, 0.00037, 70, -233.58, 183.72, 0.00614, 4, 50, -35.33, 39.19, 0.27469, 51, 36.4, -16.23, 0.36867, 52, -2.93, -16.22, 0.35182, 70, -222.87, 171.41, 0.00483, 4, 50, -21.17, 27.65, 0.59342, 51, 18.59, -12.16, 0.40238, 52, -20.72, -12.06, 0.00033, 70, -208.71, 159.87, 0.00387, 3, 50, -8.16, 17.51, 0.9202, 51, 2.44, -8.82, 0.07663, 70, -195.71, 149.72, 0.00317, 2, 50, 1.39, 6.42, 0.99807, 70, -186.15, 138.64, 0.00193, 3, 52, 29.53, -2, 0.9893, 69, -318.9, 172.58, 0.00892, 70, -258.28, 172.87, 0.00178, 3, 52, 15.35, -3.16, 0.98999, 69, -305.63, 167.41, 0.00785, 70, -245.02, 167.7, 0.00215, 4, 52, 44.62, -2.82, 0.12845, 53, 2.98, -3.9, 0.86107, 69, -332.09, 179.94, 0.00867, 70, -271.48, 180.22, 0.00181, 5, 52, 59.22, -10.2, 0.02339, 53, 14.65, -15.38, 0.96689, 54, -31.92, -3.76, 0.00102, 69, -341.97, 192.98, 0.00552, 70, -281.36, 193.27, 0.00318, 5, 52, 74.72, -13.01, 0.00029, 53, 28.56, -22.76, 0.92629, 54, -21.75, -15.79, 0.06672, 69, -354.67, 202.3, 0.00305, 70, -294.06, 202.59, 0.00365, 4, 53, 44.96, -27.55, 0.66608, 54, -8.32, -26.35, 0.32871, 69, -370.19, 209.43, 0.00177, 70, -309.58, 209.72, 0.00344, 3, 54, 35.32, -5.78, 0.23721, 55, 2.29, -7.57, 0.75568, 69, -407.81, 179.23, 0.00712, 4, 53, 63.71, -1.19, 0.00919, 54, 18.9, -8.86, 0.9752, 55, -13.79, -3.08, 0.00858, 69, -392.57, 186.07, 0.00703, 3, 53, 46.97, -2.84, 0.19134, 54, 2.75, -4.17, 0.80034, 69, -375.77, 185.27, 0.00832, 2, 53, 29.86, -0.58, 0.98994, 69, -359.17, 180.55, 0.01006, 3, 52, 54.58, 7.61, 0.00383, 53, 15.64, 3, 0.98472, 69, -345.62, 174.94, 0.01145], "hull": 34, "edges": [6, 8, 24, 26, 26, 28, 32, 34, 46, 48, 48, 50, 64, 66, 0, 2, 66, 0, 12, 14, 62, 64, 58, 60, 56, 58, 54, 56, 50, 52, 52, 54, 10, 12, 2, 4, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 4, 6, 60, 62, 8, 10, 22, 24], "width": 113, "height": 185}}, "hat_B": {"hat_B": {"type": "mesh", "uvs": [0.55576, 0.06011, 0.69659, 0.2001, 0.81383, 0.30862, 0.87439, 0.39184, 0.93613, 0.53734, 0.9886, 0.70036, 0.99745, 0.81589, 0.975, 0.89872, 0.92756, 0.95945, 0.86073, 0.98947, 0.79998, 0.99999, 0.71996, 0.96473, 0.54869, 0.88926, 0.34194, 0.79816, 0.15443, 0.71555, 0.02044, 0.6565, 0.00409, 0.56829, 0.00717, 0.42684, 0.05361, 0.27591, 0.17265, 0.10314, 0.25088, 0.04273, 0.34693, 0.00645, 0.43424, 0, 0.36293, 0.12606, 0.52214, 0.207, 0.65706, 0.35572, 0.78523, 0.4856, 0.87428, 0.64184, 0.89182, 0.82256, 0.85539, 0.93975, 0.20795, 0.15633, 0.15154, 0.34646, 0.32693, 0.31446, 0.4659, 0.35775, 0.57801, 0.47735, 0.69674, 0.61853, 0.78218, 0.75437, 0.76329, 0.89367, 0.14965, 0.51852, 0.31658, 0.55113, 0.47683, 0.65361, 0.6304, 0.81198], "triangles": [11, 37, 10, 10, 29, 9, 10, 37, 29, 9, 29, 8, 12, 41, 11, 11, 41, 37, 7, 8, 28, 8, 29, 28, 29, 37, 28, 7, 28, 6, 37, 36, 28, 37, 41, 36, 13, 40, 12, 12, 40, 41, 36, 27, 28, 28, 5, 6, 28, 27, 5, 41, 35, 36, 41, 40, 35, 14, 39, 13, 13, 39, 40, 36, 35, 27, 15, 38, 14, 14, 38, 39, 27, 4, 5, 15, 16, 38, 39, 33, 40, 40, 34, 35, 40, 33, 34, 35, 26, 27, 27, 26, 4, 34, 25, 35, 35, 25, 26, 16, 17, 38, 38, 31, 39, 39, 32, 33, 39, 31, 32, 26, 3, 4, 38, 17, 31, 26, 2, 3, 26, 25, 2, 34, 33, 25, 17, 18, 31, 33, 24, 25, 24, 33, 23, 25, 1, 2, 25, 24, 1, 31, 30, 32, 31, 18, 30, 33, 32, 23, 32, 30, 23, 18, 19, 30, 23, 22, 24, 24, 0, 1, 24, 22, 0, 30, 20, 23, 30, 19, 20, 20, 21, 23, 23, 21, 22], "vertices": [3, 6, 218.17, -28.54, 0.96946, 69, -169.5, 38.18, 0.01696, 70, -108.89, 38.47, 0.01358, 3, 6, 194.95, -61.47, 0.97487, 69, -192.73, 5.26, 0.01535, 70, -132.11, 5.54, 0.00979, 3, 6, 176.96, -88.87, 0.97393, 69, -210.72, -22.14, 0.01405, 70, -150.11, -21.86, 0.01201, 3, 6, 163.13, -103.05, 0.97509, 69, -224.55, -36.32, 0.01263, 70, -163.94, -36.03, 0.01228, 3, 6, 138.89, -117.55, 0.97825, 69, -248.78, -50.82, 0.00826, 70, -188.17, -50.53, 0.01349, 3, 6, 111.73, -129.9, 0.9795, 69, -275.95, -63.17, 0.00133, 70, -215.34, -62.89, 0.01917, 2, 6, 92.44, -132.05, 0.97414, 70, -234.62, -65.04, 0.02586, 2, 6, 78.59, -126.89, 0.97009, 70, -248.48, -59.87, 0.02991, 2, 6, 68.39, -115.88, 0.96874, 70, -258.67, -48.87, 0.03126, 3, 6, 63.31, -100.33, 0.96771, 69, -324.37, -33.61, 0.00118, 70, -263.76, -33.32, 0.03111, 3, 6, 61.48, -86.19, 0.96693, 69, -326.19, -19.46, 0.00285, 70, -265.58, -19.18, 0.03022, 3, 6, 67.28, -67.51, 0.96827, 69, -320.39, -0.79, 0.00607, 70, -259.78, -0.5, 0.02565, 3, 6, 79.7, -27.55, 0.9718, 69, -307.98, 39.18, 0.01215, 70, -247.37, 39.46, 0.01605, 3, 6, 94.68, 20.7, 0.97114, 69, -292.99, 87.42, 0.01585, 70, -232.38, 87.71, 0.01301, 3, 6, 108.28, 64.45, 0.97288, 69, -279.4, 131.17, 0.01665, 70, -218.79, 131.46, 0.01047, 3, 6, 117.99, 95.72, 0.97196, 69, -269.69, 162.44, 0.01416, 70, -209.08, 162.73, 0.01388, 3, 6, 132.7, 99.59, 0.97155, 69, -254.97, 166.32, 0.01514, 70, -194.36, 166.6, 0.01331, 3, 6, 156.33, 98.99, 0.97358, 69, -231.35, 165.71, 0.01684, 70, -170.74, 166, 0.00958, 3, 6, 181.58, 88.28, 0.97134, 69, -206.09, 155.01, 0.01875, 70, -145.48, 155.3, 0.00991, 3, 6, 210.57, 60.68, 0.97174, 69, -177.11, 127.41, 0.01972, 70, -116.5, 127.7, 0.00854, 3, 6, 220.74, 42.5, 0.97016, 69, -166.93, 109.23, 0.01902, 70, -106.32, 109.52, 0.01082, 3, 6, 226.91, 20.15, 0.96837, 69, -160.77, 86.88, 0.01812, 70, -100.16, 87.17, 0.01351, 3, 6, 228.08, -0.18, 0.96758, 69, -159.6, 66.54, 0.01701, 70, -98.99, 66.83, 0.01541, 3, 6, 206.95, 16.33, 0.97705, 69, -180.73, 83.06, 0.02225, 70, -120.12, 83.34, 0.0007, 2, 6, 193.6, -20.83, 0.97784, 69, -194.07, 45.9, 0.02216, 2, 6, 168.92, -52.38, 0.98118, 69, -218.76, 14.35, 0.01882, 3, 6, 147.37, -82.35, 0.98282, 69, -240.31, -15.62, 0.01315, 70, -179.7, -15.33, 0.00403, 3, 6, 121.37, -103.22, 0.97692, 69, -266.3, -36.49, 0.00855, 70, -205.69, -36.2, 0.01453, 3, 6, 91.21, -107.45, 0.97354, 69, -296.46, -40.72, 0.00348, 70, -235.85, -40.43, 0.02297, 3, 6, 71.6, -99.05, 0.96838, 69, -316.07, -32.32, 0.00246, 70, -255.46, -32.04, 0.02916, 3, 6, 201.72, 52.42, 0.97701, 69, -185.95, 119.14, 0.02222, 70, -125.34, 119.43, 0.00077, 2, 6, 169.91, 65.41, 0.97613, 69, -217.77, 132.14, 0.02387, 2, 6, 175.45, 24.57, 0.97133, 69, -212.23, 91.3, 0.02867, 2, 6, 168.37, -7.84, 0.97241, 69, -219.31, 58.88, 0.02759, 3, 6, 148.52, -34.06, 0.97565, 69, -239.16, 32.67, 0.02205, 70, -178.55, 32.95, 0.0023, 3, 6, 125.07, -61.83, 0.97811, 69, -262.6, 4.89, 0.01488, 70, -201.99, 5.18, 0.00701, 3, 6, 102.48, -81.85, 0.9783, 69, -285.19, -15.12, 0.00901, 70, -224.58, -14.83, 0.01269, 3, 6, 79.2, -77.55, 0.97231, 69, -308.48, -10.83, 0.0066, 70, -247.87, -10.54, 0.0211, 3, 6, 141.17, 65.72, 0.97426, 69, -246.5, 132.44, 0.02186, 70, -185.89, 132.73, 0.00388, 3, 6, 135.91, 26.8, 0.97173, 69, -251.76, 93.52, 0.02351, 70, -191.15, 93.81, 0.00476, 3, 6, 118.97, -10.62, 0.97108, 69, -268.7, 56.1, 0.02074, 70, -208.09, 56.39, 0.00818, 3, 6, 92.69, -46.53, 0.97385, 69, -294.98, 20.2, 0.01269, 70, -234.37, 20.48, 0.01347], "hull": 23, "edges": [0, 44, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 2, 2, 4, 4, 6, 18, 20, 20, 22, 28, 30, 26, 28, 22, 24, 24, 26], "width": 233, "height": 167}}, "hat_F": {"hat_F": {"type": "mesh", "uvs": [0.53374, 0.00577, 0.63817, 0.05061, 0.72301, 0.15101, 0.81927, 0.34269, 0.92209, 0.52871, 0.9715, 0.6527, 0.99645, 0.77695, 0.99426, 0.91725, 0.96978, 0.99514, 0.32245, 1, 0.2011, 0.92667, 0.08587, 0.81974, 0.02368, 0.69202, 0.00068, 0.53865, 0.00758, 0.42113, 0.04804, 0.29566, 0.13879, 0.16478, 0.27284, 0.04879, 0.38996, 0.00848, 0.20596, 0.89454, 0.109, 0.78828, 0.04664, 0.656, 0.03039, 0.53998, 0.03825, 0.43263, 0.07074, 0.32854, 0.15311, 0.22168, 0.2824, 0.11247, 0.399, 0.06701, 0.52431, 0.06516, 0.63637, 0.113, 0.70619, 0.20002, 0.80821, 0.41673, 0.9091, 0.61335, 0.96253, 0.74538, 0.16499, 0.53046, 0.31641, 0.35085, 0.48489, 0.26834, 0.30731, 0.76402, 0.62982, 0.4184, 0.44913, 0.59392, 0.54002, 0.86581, 0.74921, 0.69072], "triangles": [28, 18, 0, 27, 18, 28, 26, 17, 18, 26, 18, 27, 1, 28, 0, 29, 28, 1, 29, 1, 2, 16, 17, 26, 30, 29, 2, 25, 16, 26, 36, 27, 28, 24, 15, 16, 25, 24, 16, 30, 2, 3, 35, 26, 27, 35, 27, 36, 25, 26, 35, 31, 30, 3, 29, 36, 28, 38, 29, 30, 38, 30, 31, 38, 36, 29, 23, 14, 15, 24, 23, 15, 31, 3, 4, 34, 25, 35, 24, 25, 34, 23, 24, 34, 22, 13, 14, 23, 22, 14, 22, 23, 34, 39, 35, 36, 39, 36, 38, 32, 31, 4, 32, 4, 5, 21, 22, 34, 41, 38, 31, 41, 31, 32, 21, 12, 13, 21, 13, 22, 33, 32, 5, 37, 34, 35, 37, 35, 39, 33, 5, 6, 20, 21, 34, 19, 20, 34, 11, 21, 20, 12, 21, 11, 40, 39, 38, 40, 38, 41, 37, 19, 34, 7, 33, 6, 10, 20, 19, 11, 20, 10, 8, 33, 7, 40, 9, 37, 40, 37, 39, 19, 37, 9, 10, 19, 9, 41, 8, 40, 33, 41, 32, 8, 41, 33, 9, 40, 8], "vertices": [2, 6, 190.36, 9.72, 0.95, 69, -197.31, 76.45, 0.05, 2, 6, 185.28, -15.37, 0.95064, 69, -202.4, 51.36, 0.04936, 2, 6, 173.73, -35.79, 0.95745, 69, -213.95, 30.94, 0.04255, 2, 6, 151.6, -58.99, 0.96881, 69, -236.07, 7.74, 0.03119, 3, 6, 130.14, -83.77, 0.96598, 69, -257.53, -17.04, 0.02033, 70, -196.92, -16.76, 0.01369, 3, 6, 115.81, -95.69, 0.9612, 69, -271.86, -28.97, 0.01309, 70, -211.25, -28.68, 0.02571, 3, 6, 101.43, -101.75, 0.97521, 69, -286.25, -35.02, 0.00359, 70, -225.64, -34.74, 0.0212, 2, 6, 85.15, -101.3, 0.98229, 70, -241.91, -34.29, 0.01771, 2, 6, 76.09, -95.47, 0.97713, 70, -250.98, -28.46, 0.02287, 2, 6, 74.79, 59.89, 0.95119, 70, -252.27, 126.9, 0.04881, 2, 6, 83.16, 89.05, 0.96234, 70, -243.9, 156.06, 0.03766, 3, 6, 95.43, 116.76, 0.97383, 69, -292.24, 183.49, 0.00781, 70, -231.63, 183.77, 0.01836, 3, 6, 110.18, 131.76, 0.9793, 69, -277.49, 198.48, 0.01533, 70, -216.88, 198.77, 0.00538, 2, 6, 127.94, 137.36, 0.97572, 69, -259.73, 204.09, 0.02428, 2, 6, 141.59, 135.77, 0.96893, 69, -246.09, 202.5, 0.03107, 2, 6, 156.18, 126.13, 0.96164, 69, -231.49, 192.85, 0.03836, 2, 6, 171.47, 104.42, 0.95425, 69, -216.21, 171.15, 0.04575, 2, 6, 185.08, 72.31, 0.95, 69, -202.6, 139.04, 0.05, 2, 6, 189.88, 44.23, 0.95, 69, -197.79, 110.95, 0.05, 3, 6, 86.89, 87.9, 0.95773, 69, -300.78, 154.63, 0.00027, 70, -240.17, 154.91, 0.042, 3, 6, 99.11, 111.23, 0.96496, 69, -288.56, 177.95, 0.00953, 70, -227.95, 178.24, 0.02551, 3, 6, 114.38, 126.27, 0.9737, 69, -273.29, 192.99, 0.0173, 70, -212.68, 193.28, 0.009, 3, 6, 127.82, 130.23, 0.97485, 69, -259.85, 196.96, 0.02455, 70, -199.24, 197.24, 0.00059, 2, 6, 140.29, 128.4, 0.96861, 69, -247.39, 195.13, 0.03139, 2, 6, 152.4, 120.66, 0.96219, 69, -235.28, 187.39, 0.03781, 2, 6, 164.89, 100.95, 0.95548, 69, -222.79, 167.68, 0.04452, 2, 6, 177.7, 69.98, 0.9499, 69, -209.97, 136.71, 0.0501, 2, 6, 183.11, 42.02, 0.94779, 69, -204.57, 108.75, 0.05221, 2, 6, 183.46, 11.95, 0.94783, 69, -204.21, 78.68, 0.05217, 2, 6, 178.04, -14.97, 0.95116, 69, -209.64, 51.76, 0.04884, 2, 6, 168.02, -31.77, 0.95555, 69, -219.65, 34.95, 0.04445, 3, 6, 143, -56.38, 0.96024, 69, -244.67, 10.35, 0.03501, 70, -184.06, 10.64, 0.00475, 3, 6, 120.31, -80.7, 0.95475, 69, -267.37, -13.97, 0.02572, 70, -206.76, -13.69, 0.01953, 3, 6, 105.05, -93.59, 0.9554, 69, -282.62, -26.87, 0.01579, 70, -222.01, -26.58, 0.02881, 3, 6, 129.08, 97.93, 0.96465, 69, -258.59, 164.66, 0.02426, 70, -197.98, 164.94, 0.01109, 3, 6, 150.09, 61.69, 0.9633, 69, -237.59, 128.42, 0.03446, 70, -176.98, 128.7, 0.00224, 2, 6, 159.85, 21.3, 0.96146, 69, -227.83, 88.03, 0.03854, 3, 6, 102.15, 63.65, 0.95756, 69, -285.53, 130.37, 0.01024, 70, -224.92, 130.66, 0.0322, 3, 6, 142.61, -13.56, 0.96685, 69, -245.07, 53.16, 0.02843, 70, -184.46, 53.45, 0.00472, 3, 6, 122.04, 29.7, 0.96218, 69, -265.63, 96.43, 0.01955, 70, -205.02, 96.72, 0.01828, 3, 6, 90.6, 7.74, 0.96168, 69, -297.07, 74.47, 0.00108, 70, -236.46, 74.75, 0.03724, 3, 6, 111.15, -42.37, 0.96476, 69, -276.52, 24.36, 0.01094, 70, -215.91, 24.65, 0.02429], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 18, 20, 20, 22, 4, 6, 6, 8, 14, 16, 18, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 14], "width": 240, "height": 116}}, "head": {"head": {"type": "mesh", "uvs": [0.56796, 0.02278, 0.66662, 0.01817, 0.73323, 0.06421, 0.78071, 0.11025, 0.81572, 0.18785, 0.86377, 0.2463, 0.89224, 0.33944, 0.89703, 0.42417, 0.93236, 0.37143, 0.96768, 0.37375, 0.99228, 0.42847, 0.9942, 0.5351, 0.95407, 0.61104, 0.88418, 0.68013, 0.84121, 0.66107, 0.8002, 0.77581, 0.75407, 0.83539, 0.59992, 0.95799, 0.52739, 0.99133, 0.44913, 0.99207, 0.38724, 0.95916, 0.2523, 0.85431, 0.19432, 0.7826, 0.1532, 0.66773, 0.11698, 0.67717, 0.05458, 0.62273, 0.01098, 0.54439, 0.00709, 0.44289, 0.03224, 0.37279, 0.06835, 0.37385, 0.09899, 0.42038, 0.10481, 0.31473, 0.14032, 0.18984, 0.19083, 0.09148, 0.2402, 0.03561, 0.28817, 0.01659, 0.41864, 0.01207, 0.55385, 0.00221, 0.54805, 0.36419, 0.73253, 0.28772, 0.78585, 0.28584, 0.84469, 0.31467, 0.83733, 0.3341, 0.78786, 0.31795, 0.7483, 0.32385, 0.56529, 0.40158, 0.54707, 0.38795, 0.6583, 0.36459, 0.63863, 0.32254, 0.59084, 0.34166, 0.61196, 0.38496, 0.68508, 0.30445, 0.70296, 0.34388, 0.15608, 0.31711, 0.15954, 0.33719, 0.20227, 0.31829, 0.23866, 0.32124, 0.40497, 0.40629, 0.42597, 0.39323, 0.42403, 0.36909, 0.24328, 0.28462, 0.2017, 0.28994, 0.33675, 0.31523, 0.32447, 0.3595, 0.28289, 0.33707, 0.36567, 0.38561, 0.38415, 0.34252, 0.29156, 0.29698, 0.87599, 0.54437, 0.12059, 0.55717, 0.58444, 0.47737, 0.6023, 0.45159, 0.60597, 0.48167, 0.66716, 0.48355, 0.70497, 0.48328, 0.73779, 0.47388, 0.76484, 0.45025, 0.78184, 0.42401, 0.62613, 0.4275, 0.65475, 0.40844, 0.68836, 0.40038, 0.72617, 0.3985, 0.75821, 0.40763, 0.63359, 0.48262, 0.57333, 0.47262, 0.59433, 0.44464, 0.61982, 0.41282, 0.64943, 0.38943, 0.69104, 0.37639, 0.73115, 0.37409, 0.77313, 0.38176, 0.81661, 0.40515, 0.57748, 0.48637, 0.63183, 0.49905, 0.6963, 0.52665, 0.75441, 0.5186, 0.80202, 0.4841, 0.82976, 0.44576, 0.40932, 0.4734, 0.39606, 0.45157, 0.37198, 0.41934, 0.34196, 0.39655, 0.30582, 0.38212, 0.2587, 0.38205, 0.21559, 0.39482, 0.17866, 0.42065, 0.3945, 0.47633, 0.384, 0.45078, 0.36192, 0.42782, 0.33332, 0.41338, 0.29965, 0.40598, 0.26308, 0.40635, 0.23158, 0.4156, 0.20624, 0.4356, 0.37278, 0.48448, 0.33802, 0.48929, 0.29385, 0.4904, 0.25439, 0.48262, 0.2247, 0.46041, 0.17462, 0.47487, 0.22349, 0.50968, 0.27527, 0.5256, 0.32994, 0.52264, 0.37338, 0.49709, 0.40778, 0.48709, 0.40625, 0.18249, 0.55203, 0.1835, 0.70972, 0.16322, 0.25726, 0.16322, 0.42352, 0.61193, 0.40362, 0.64328, 0.40577, 0.68014, 0.42729, 0.69883, 0.45633, 0.69883, 0.49021, 0.71809, 0.52355, 0.69719, 0.55367, 0.69334, 0.5741, 0.67354, 0.57464, 0.63723, 0.55582, 0.60863, 0.43643, 0.53745, 0.43213, 0.46265, 0.45794, 0.45055, 0.46601, 0.5215, 0.45955, 0.58915, 0.44557, 0.626, 0.44772, 0.6546, 0.48537, 0.67165, 0.5214, 0.6546, 0.52247, 0.62545, 0.51333, 0.58915, 0.51226, 0.5204, 0.51656, 0.45, 0.54399, 0.53877, 0.54452, 0.45627, 0.48819, 0.44569, 0.48872, 0.51389, 0.48657, 0.58264, 0.48577, 0.62914, 0.38184, 0.78656, 0.40555, 0.76824, 0.43585, 0.74853, 0.46325, 0.74813, 0.49182, 0.76694, 0.51257, 0.74973, 0.54018, 0.75049, 0.57163, 0.76464, 0.6002, 0.78666, 0.36865, 0.79127, 0.38391, 0.79688, 0.42227, 0.79648, 0.45554, 0.79968, 0.49076, 0.80729, 0.52599, 0.79928, 0.55926, 0.79648, 0.59801, 0.79808, 0.61562, 0.79047, 0.39594, 0.82593, 0.41904, 0.85556, 0.4487, 0.87282, 0.49194, 0.87917, 0.53568, 0.87077, 0.56895, 0.84915, 0.59439, 0.82313, 0.36662, 0.76263, 0.34549, 0.78745, 0.35762, 0.81227, 0.61711, 0.76303, 0.6402, 0.78865, 0.62376, 0.81347, 0.44763, 0.82878, 0.49138, 0.83453, 0.53326, 0.83028, 0.56728, 0.81667, 0.42034, 0.81589, 0.43486, 0.77704, 0.46427, 0.77846, 0.49022, 0.78943, 0.51547, 0.77917, 0.54315, 0.77846, 0.79735, 0.57583, 0.76545, 0.68635, 0.69032, 0.81792, 0.59275, 0.91255, 0.52225, 0.94617, 0.45258, 0.94633, 0.38877, 0.91265, 0.29203, 0.81897, 0.22308, 0.69372, 0.1922, 0.57794, 0.44236, 0.89046, 0.49234, 0.88839, 0.54208, 0.88951, 0.32444, 0.59879, 0.32588, 0.7077, 0.66603, 0.58961, 0.65533, 0.70549], "triangles": [54, 31, 53, 54, 55, 104, 104, 55, 56, 54, 53, 55, 60, 56, 61, 53, 61, 55, 56, 55, 61, 61, 53, 32, 60, 61, 128, 102, 64, 63, 103, 56, 64, 63, 62, 66, 63, 64, 62, 64, 67, 62, 64, 56, 67, 56, 60, 67, 62, 67, 125, 67, 60, 128, 66, 62, 125, 101, 63, 65, 155, 58, 46, 141, 57, 58, 100, 65, 57, 58, 57, 59, 57, 65, 59, 58, 59, 46, 65, 66, 59, 65, 63, 66, 59, 66, 125, 90, 43, 42, 90, 44, 43, 42, 41, 6, 42, 43, 41, 44, 40, 43, 43, 40, 41, 41, 40, 5, 50, 48, 47, 88, 47, 52, 89, 52, 44, 47, 51, 52, 47, 48, 51, 52, 39, 44, 52, 51, 39, 44, 39, 40, 48, 126, 51, 51, 127, 39, 40, 39, 4, 126, 38, 59, 87, 50, 47, 49, 126, 48, 152, 46, 45, 45, 50, 86, 49, 50, 45, 45, 46, 38, 46, 59, 38, 45, 38, 49, 50, 49, 48, 38, 126, 49, 41, 5, 6, 40, 4, 5, 6, 91, 42, 10, 8, 9, 11, 7, 10, 87, 47, 88, 88, 52, 89, 7, 11, 68, 7, 91, 6, 81, 88, 89, 89, 44, 90, 91, 90, 42, 82, 89, 90, 80, 87, 88, 86, 50, 87, 76, 81, 82, 96, 77, 97, 82, 90, 77, 74, 80, 81, 79, 87, 80, 73, 79, 80, 76, 82, 77, 85, 45, 86, 78, 86, 79, 86, 87, 79, 13, 14, 12, 14, 200, 68, 200, 96, 68, 95, 74, 75, 74, 81, 75, 85, 154, 45, 200, 94, 95, 83, 71, 78, 73, 83, 78, 83, 72, 71, 70, 84, 85, 102, 63, 101, 103, 64, 102, 110, 103, 102, 104, 56, 103, 105, 54, 104, 105, 30, 54, 113, 105, 104, 116, 111, 110, 111, 112, 103, 111, 103, 110, 109, 110, 102, 57, 99, 100, 100, 108, 101, 108, 109, 101, 119, 69, 105, 119, 113, 118, 114, 108, 107, 58, 142, 141, 57, 141, 99, 99, 107, 100, 117, 118, 111, 98, 99, 141, 117, 111, 116, 116, 110, 109, 106, 99, 98, 106, 107, 99, 124, 106, 98, 23, 24, 69, 23, 69, 209, 69, 30, 105, 209, 69, 119, 24, 25, 69, 25, 26, 69, 26, 30, 69, 27, 30, 26, 31, 54, 30, 53, 31, 32, 29, 27, 28, 128, 61, 32, 19, 204, 18, 19, 205, 204, 19, 20, 205, 18, 204, 17, 20, 21, 206, 20, 206, 205, 206, 21, 207, 17, 202, 16, 204, 203, 17, 17, 203, 202, 206, 210, 205, 205, 211, 204, 205, 210, 211, 204, 212, 203, 204, 211, 212, 207, 186, 206, 206, 186, 177, 169, 177, 186, 206, 178, 210, 206, 177, 178, 169, 186, 168, 202, 189, 188, 202, 203, 189, 203, 183, 189, 212, 182, 203, 203, 182, 183, 211, 179, 180, 211, 210, 179, 210, 178, 179, 211, 181, 212, 212, 181, 182, 211, 180, 181, 179, 191, 180, 180, 191, 181, 178, 190, 179, 179, 190, 191, 191, 192, 181, 181, 192, 182, 178, 194, 190, 178, 177, 194, 21, 22, 207, 192, 193, 182, 182, 193, 183, 16, 202, 15, 191, 190, 172, 190, 171, 172, 192, 172, 173, 192, 191, 172, 192, 174, 193, 192, 173, 174, 190, 194, 171, 171, 194, 170, 177, 169, 194, 183, 175, 189, 183, 193, 175, 22, 208, 207, 207, 185, 186, 207, 214, 185, 207, 208, 214, 188, 216, 202, 202, 201, 15, 202, 216, 201, 193, 174, 175, 194, 169, 170, 175, 176, 189, 189, 176, 188, 186, 185, 168, 172, 171, 197, 171, 196, 197, 173, 197, 198, 173, 172, 197, 170, 195, 171, 171, 195, 196, 173, 199, 174, 173, 198, 199, 175, 167, 176, 175, 174, 167, 168, 159, 169, 170, 169, 160, 169, 159, 160, 170, 160, 195, 174, 166, 167, 174, 199, 166, 159, 168, 184, 188, 176, 187, 197, 163, 198, 197, 196, 163, 176, 167, 187, 188, 187, 216, 168, 185, 184, 185, 214, 184, 167, 166, 187, 159, 184, 160, 22, 23, 208, 163, 164, 198, 198, 165, 199, 198, 164, 165, 196, 195, 162, 195, 161, 162, 196, 162, 163, 199, 165, 166, 195, 160, 161, 15, 201, 14, 161, 160, 132, 164, 163, 134, 187, 166, 136, 166, 165, 136, 136, 137, 187, 187, 137, 216, 160, 184, 132, 184, 131, 132, 184, 214, 131, 164, 135, 165, 165, 135, 136, 163, 162, 134, 164, 134, 135, 161, 133, 162, 161, 132, 133, 162, 133, 134, 133, 147, 134, 134, 147, 135, 208, 213, 214, 214, 130, 131, 214, 213, 130, 137, 138, 216, 216, 215, 201, 216, 138, 215, 132, 146, 133, 133, 146, 147, 132, 131, 146, 147, 148, 135, 135, 148, 136, 213, 209, 121, 209, 213, 208, 136, 148, 137, 201, 215, 200, 200, 215, 94, 201, 200, 14, 146, 130, 145, 145, 130, 129, 130, 146, 131, 137, 148, 138, 138, 148, 149, 138, 149, 139, 147, 158, 148, 147, 146, 158, 146, 145, 158, 148, 158, 149, 130, 213, 129, 138, 139, 215, 145, 144, 158, 158, 150, 149, 158, 157, 150, 158, 144, 157, 145, 129, 144, 149, 150, 139, 129, 213, 140, 140, 213, 122, 140, 122, 123, 129, 140, 144, 140, 123, 124, 150, 153, 139, 215, 153, 93, 93, 153, 92, 153, 215, 139, 213, 121, 122, 215, 93, 94, 157, 144, 143, 153, 150, 151, 144, 140, 143, 150, 157, 151, 157, 156, 151, 157, 143, 156, 92, 153, 154, 141, 143, 140, 124, 98, 141, 141, 140, 124, 95, 94, 74, 94, 93, 73, 93, 83, 73, 94, 73, 74, 141, 142, 143, 143, 142, 156, 92, 154, 84, 154, 153, 151, 151, 152, 154, 151, 156, 152, 142, 155, 156, 156, 155, 152, 93, 92, 72, 92, 70, 72, 93, 72, 83, 92, 84, 70, 84, 154, 85, 142, 58, 155, 154, 152, 45, 152, 155, 46, 126, 59, 125, 1, 127, 126, 126, 0, 1, 0, 126, 36, 126, 125, 36, 36, 37, 0, 128, 35, 125, 125, 35, 36, 127, 1, 2, 128, 34, 35, 51, 126, 127, 4, 39, 127, 127, 3, 4, 127, 2, 3, 33, 34, 128, 125, 67, 128, 32, 33, 128, 30, 27, 29, 23, 209, 208, 121, 209, 120, 120, 209, 119, 121, 116, 122, 122, 115, 123, 122, 116, 115, 121, 117, 116, 123, 106, 124, 115, 114, 123, 123, 114, 106, 120, 117, 121, 116, 109, 115, 115, 108, 114, 114, 107, 106, 120, 118, 117, 120, 119, 118, 111, 118, 112, 118, 113, 112, 115, 109, 108, 108, 100, 107, 119, 105, 113, 113, 104, 112, 112, 104, 103, 101, 65, 100, 101, 109, 102, 70, 71, 72, 71, 70, 85, 71, 85, 78, 79, 73, 78, 80, 74, 73, 200, 95, 96, 96, 95, 75, 85, 86, 78, 76, 75, 81, 96, 75, 76, 76, 77, 96, 14, 68, 12, 96, 97, 68, 68, 97, 7, 77, 91, 97, 81, 89, 82, 80, 88, 81, 12, 68, 11, 91, 7, 97, 77, 90, 91, 10, 7, 8], "vertices": [2, 6, 125.12, -10.72, 0.96961, 69, -262.56, 56, 0.03039, 2, 6, 126.01, -28.48, 0.96995, 69, -261.66, 38.25, 0.03005, 2, 6, 117.97, -40.51, 0.97265, 69, -269.71, 26.22, 0.02735, 2, 6, 109.9, -49.09, 0.97487, 69, -277.77, 17.64, 0.02513, 2, 6, 96.27, -55.46, 0.97541, 69, -291.4, 11.27, 0.02459, 2, 6, 86.03, -64.15, 0.98236, 69, -301.65, 2.57, 0.01764, 2, 6, 69.66, -69.36, 0.98967, 69, -318.01, -2.63, 0.01033, 2, 6, 54.75, -70.29, 0.98966, 69, -332.92, -3.56, 0.01034, 3, 6, 64.06, -76.6, 0.99156, 69, -323.61, -9.88, 0.00749, 70, -263, -9.59, 0.00096, 3, 6, 63.69, -82.96, 0.99155, 69, -323.99, -16.24, 0.00562, 70, -263.38, -15.95, 0.00283, 3, 6, 54.08, -87.44, 0.99062, 69, -333.6, -20.71, 0.00527, 70, -272.99, -20.43, 0.0041, 3, 6, 35.31, -87.87, 0.98823, 69, -352.36, -21.14, 0.00716, 70, -291.75, -20.86, 0.00461, 3, 6, 21.91, -80.71, 0.98649, 69, -365.76, -13.98, 0.01015, 70, -305.15, -13.7, 0.00336, 3, 6, 9.69, -68.19, 0.98352, 69, -377.98, -1.46, 0.0157, 70, -317.37, -1.18, 0.00078, 2, 6, 13.01, -60.44, 0.97988, 69, -374.66, 6.29, 0.02012, 2, 6, -7.22, -53.15, 0.97952, 69, -394.89, 13.57, 0.02048, 2, 6, -17.74, -44.9, 0.97652, 69, -405.42, 21.83, 0.02348, 2, 6, -39.45, -17.25, 0.96609, 69, -427.13, 49.47, 0.03391, 2, 6, -45.38, -4.22, 0.96196, 69, -433.05, 62.5, 0.03804, 2, 6, -45.58, 9.86, 0.96179, 69, -433.25, 76.59, 0.03821, 2, 6, -39.84, 21.03, 0.96549, 69, -427.51, 87.76, 0.03451, 2, 6, -21.5, 45.4, 0.97526, 69, -409.17, 112.13, 0.02474, 2, 6, -8.93, 55.9, 0.98051, 69, -396.6, 122.63, 0.01949, 2, 6, 11.26, 63.4, 0.98144, 69, -376.42, 130.12, 0.01856, 2, 6, 9.56, 69.91, 0.98758, 69, -378.11, 136.64, 0.01242, 2, 6, 19.09, 81.19, 0.99031, 69, -368.58, 147.91, 0.00969, 2, 6, 32.84, 89.1, 0.99338, 69, -354.83, 155.83, 0.00662, 2, 6, 50.7, 89.88, 0.99453, 69, -336.97, 156.61, 0.00547, 2, 6, 63.06, 85.42, 0.99303, 69, -324.61, 152.14, 0.00697, 2, 6, 62.91, 78.91, 0.99201, 69, -324.77, 145.64, 0.00799, 2, 6, 54.74, 73.36, 0.98985, 69, -332.93, 140.09, 0.01015, 2, 6, 73.34, 72.4, 0.98692, 69, -314.33, 139.13, 0.01308, 2, 6, 95.35, 66.11, 0.97936, 69, -292.32, 132.84, 0.02064, 2, 6, 112.7, 57.1, 0.97534, 69, -274.97, 123.83, 0.02466, 2, 6, 122.58, 48.26, 0.97317, 69, -265.09, 114.99, 0.02683, 2, 6, 125.97, 39.64, 0.97047, 69, -261.71, 106.37, 0.02953, 2, 6, 126.88, 16.16, 0.96976, 69, -260.8, 82.89, 0.03024, 2, 6, 128.73, -8.17, 0.96966, 69, -258.95, 58.56, 0.03034, 1, 40, 14.28, -2.88, 1, 5, 39, 3.76, -4, 0.9376, 38, 18.68, -1.32, 0.05445, 42, 80.51, 37.72, 0, 41, 100.45, -18.07, 0, 70, -248.44, 26.45, 0.00794, 5, 39, -5.36, -0.99, 0.06548, 38, 9.39, -3.74, 0.92122, 42, 89.19, 41.84, 0, 41, 109.86, -19.97, 0, 70, -248.06, 16.85, 0.0133, 4, 38, -2.06, -1.1, 0.9733, 42, 100.92, 41.38, 0, 41, 118.98, -27.37, 0, 70, -253.09, 6.24, 0.0267, 4, 40, -36.4, 10.22, 0.00043, 39, -11.12, 10.19, 0.0121, 38, -1.51, 2.52, 0.96313, 70, -256.51, 7.54, 0.02434, 4, 40, -29.03, 4.46, 0.00027, 39, -3.75, 4.44, 0.03915, 38, 7.8, 1.69, 0.94746, 70, -253.71, 16.46, 0.01312, 4, 40, -21.99, 2.97, 0.00078, 39, 3.3, 2.95, 0.84874, 38, 14.52, 4.26, 0.14138, 70, -254.78, 23.58, 0.00911, 3, 40, 13.65, 4.37, 0.99513, 39, 38.94, 4.38, 0.00317, 38, 43.68, 24.81, 0.0017, 2, 40, 15.89, 0.98, 0.99988, 38, 47.4, 23.19, 0.00012, 3, 40, -4.31, 4.07, 0.12105, 39, 20.98, 4.07, 0.86565, 38, 28.76, 14.8, 0.01329, 4, 40, -3.56, -4.1, 0.13411, 39, 21.74, -4.1, 0.86589, 42, 67.43, 25.38, 0, 41, 82.59, -20.1, 0, 4, 40, 5.68, -3.93, 0.94236, 39, 30.97, -3.92, 0.05764, 42, 60.87, 18.88, 0, 41, 73.44, -21.38, 0, 3, 40, 4.76, 4.54, 0.87612, 39, 30.04, 4.55, 0.11768, 38, 36.12, 20.12, 0.0062, 4, 39, 12.79, -4.19, 0.99639, 42, 73.84, 31.63, 0, 41, 91.46, -18.95, 0, 70, -251.42, 34.97, 0.0036, 4, 40, -13.11, 3.44, 0.00922, 39, 12.17, 3.43, 0.95846, 38, 21.71, 9.49, 0.02733, 70, -258.35, 31.72, 0.00499, 4, 39, 102.9, -35.06, 0, 38, 118.81, 26.39, 0, 41, -1.68, 1, 0.97855, 70, -254.1, 130.18, 0.02145, 4, 43, -31.8, -13.64, 0.00261, 42, -10.69, -11.23, 0.01479, 41, -1.89, -2.58, 0.96195, 70, -257.63, 129.54, 0.02065, 4, 43, -26.53, -7.12, 0.00053, 42, -4.95, -5.12, 0.01742, 41, 6.36, -1.13, 0.97031, 70, -254.27, 121.87, 0.01174, 3, 42, 1.26, -3, 0.54787, 41, 12.61, -3.16, 0.4457, 70, -254.76, 115.32, 0.00643, 3, 43, 12.97, -3.89, 0.99414, 42, 34.68, -4.85, 0.00078, 41, 38.25, -24.67, 0.00509, 1, 43, 15.25, -0.1, 1, 3, 38, 69.74, 24.79, 0, 43, 12.97, 3.5, 0.99672, 42, 35.23, 2.52, 0.00328, 5, 39, 86.19, -34.99, 0, 38, 104.74, 17.39, 0, 42, -0.53, 3.25, 0.54415, 41, 14.92, 2.92, 0.44988, 70, -248.31, 114.51, 0.00597, 4, 39, 93.54, -36.7, 0, 38, 111.84, 19.93, 0, 41, 7.42, 3.75, 0.98837, 70, -249.28, 121.99, 0.01163, 4, 39, 72.27, -24.11, 0, 38, 87.14, 18.97, 0, 43, -5.35, 4.6, 0.04799, 42, 17.05, 4.99, 0.95201, 3, 43, -3.69, -3.33, 0.04504, 42, 18.11, -3.04, 0.94087, 41, 26.07, -13.29, 0.01409, 4, 43, -12.15, -3.31, 0.0003, 42, 9.68, -2.39, 0.965, 41, 19.71, -7.72, 0.03245, 70, -257.5, 107.34, 0.00225, 3, 43, 5.02, -3.95, 0.94428, 42, 26.75, -4.31, 0.04606, 41, 32.22, -19.49, 0.00966, 4, 39, 65.93, -16.65, 0, 38, 77.77, 21.79, 0, 43, 4.44, 4.31, 0.86713, 42, 26.79, 3.97, 0.13287, 4, 39, 78.79, -29.94, 0, 38, 95.78, 17.61, 0, 42, 8.31, 4.7, 0.99844, 70, -250.44, 105.81, 0.00156, 2, 6, 33.58, -66.6, 0.98443, 69, -354.1, 0.12, 0.01557, 2, 6, 30.69, 69.36, 0.98621, 69, -356.99, 136.09, 0.01379, 2, 6, 45.12, -14.07, 0.96, 69, -342.55, 52.66, 0.04, 2, 6, 49.68, -17.26, 0.96, 69, -338, 49.47, 0.04, 2, 6, 44.39, -17.95, 0.96, 69, -343.29, 48.78, 0.04, 2, 6, 44.11, -28.96, 0.96, 69, -343.57, 37.77, 0.04, 2, 6, 44.19, -35.77, 0.96204, 69, -343.49, 30.96, 0.03796, 2, 6, 45.87, -41.67, 0.96443, 69, -341.81, 25.06, 0.03557, 2, 6, 50.05, -46.52, 0.9664, 69, -337.63, 20.21, 0.0336, 2, 6, 54.68, -49.55, 0.96762, 69, -332.99, 17.17, 0.03238, 2, 6, 53.94, -21.53, 0.96, 69, -333.74, 45.2, 0.04, 2, 6, 57.32, -26.67, 0.96, 69, -330.36, 40.06, 0.04, 2, 6, 58.76, -32.71, 0.96089, 69, -328.91, 34.02, 0.03911, 2, 6, 59.12, -39.51, 0.96361, 69, -328.55, 27.21, 0.03639, 2, 6, 57.55, -45.29, 0.96592, 69, -330.13, 21.44, 0.03408, 2, 6, 44.24, -22.92, 0.96, 69, -343.43, 43.81, 0.04, 2, 6, 45.95, -12.06, 0.96, 69, -341.73, 54.66, 0.04, 2, 6, 50.89, -15.82, 0.96, 69, -336.78, 50.91, 0.04, 2, 6, 56.52, -20.38, 0.96, 69, -331.16, 46.35, 0.04, 2, 6, 60.66, -25.69, 0.96, 69, -327.02, 41.03, 0.04, 2, 6, 62.99, -33.17, 0.96109, 69, -324.69, 33.56, 0.03891, 2, 6, 63.42, -40.39, 0.96397, 69, -324.25, 26.34, 0.03603, 2, 6, 62.11, -47.95, 0.96699, 69, -325.56, 18.78, 0.03301, 2, 6, 58.03, -55.8, 0.97012, 69, -329.64, 10.93, 0.02988, 2, 6, 43.53, -12.82, 0.96, 69, -344.14, 53.9, 0.04, 2, 6, 41.35, -22.61, 0.96, 69, -346.33, 44.11, 0.04, 2, 6, 36.54, -34.24, 0.96114, 69, -351.13, 32.48, 0.03886, 2, 6, 38.01, -44.7, 0.96536, 69, -349.66, 22.03, 0.03464, 2, 6, 44.12, -53.24, 0.96901, 69, -343.55, 13.49, 0.03099, 2, 6, 50.9, -58.2, 0.97107, 69, -336.78, 8.53, 0.02893, 2, 6, 45.67, 17.46, 0.96, 69, -342, 84.18, 0.04, 2, 6, 49.5, 19.86, 0.96, 69, -338.17, 86.59, 0.04, 2, 6, 55.16, 24.22, 0.96, 69, -332.52, 90.95, 0.04, 2, 6, 59.14, 29.65, 0.96, 69, -328.53, 96.37, 0.04, 2, 6, 61.65, 36.16, 0.96, 69, -326.02, 102.89, 0.04, 2, 6, 61.62, 44.65, 0.96238, 69, -326.05, 111.37, 0.03762, 2, 6, 59.34, 52.39, 0.9654, 69, -328.34, 119.12, 0.0346, 2, 6, 54.76, 59.02, 0.96805, 69, -332.91, 125.75, 0.03195, 2, 6, 45.15, 20.12, 0.96, 69, -342.53, 86.85, 0.04, 2, 6, 49.63, 22.03, 0.96, 69, -338.04, 88.76, 0.04, 2, 6, 53.66, 26.03, 0.96, 69, -334.02, 92.75, 0.04, 2, 6, 56.17, 31.19, 0.96, 69, -331.5, 97.91, 0.04, 2, 6, 57.45, 37.25, 0.96, 69, -330.23, 103.98, 0.04, 2, 6, 57.35, 43.84, 0.96197, 69, -330.32, 110.56, 0.03803, 2, 6, 55.7, 49.5, 0.96424, 69, -331.98, 116.22, 0.03576, 2, 6, 52.15, 54.04, 0.96608, 69, -335.52, 120.77, 0.03392, 2, 6, 43.69, 24.03, 0.96, 69, -343.98, 90.75, 0.04, 2, 6, 42.82, 30.28, 0.96, 69, -344.86, 97, 0.04, 2, 6, 42.58, 38.23, 0.96, 69, -345.09, 104.95, 0.04, 2, 6, 43.92, 45.34, 0.96277, 69, -343.76, 112.06, 0.03723, 2, 6, 47.8, 50.7, 0.96488, 69, -339.87, 117.43, 0.03512, 2, 6, 45.22, 59.7, 0.96852, 69, -342.46, 126.43, 0.03148, 2, 6, 39.13, 50.88, 0.965, 69, -348.54, 117.6, 0.035, 2, 6, 36.37, 41.54, 0.96127, 69, -351.3, 108.27, 0.03873, 2, 6, 36.94, 31.71, 0.96, 69, -350.73, 98.43, 0.04, 2, 6, 41.47, 23.91, 0.96, 69, -346.2, 90.63, 0.04, 2, 6, 43.26, 17.72, 0.96, 69, -344.41, 84.45, 0.04, 2, 6, 96.87, 18.25, 0.96231, 69, -290.8, 84.98, 0.03769, 2, 6, 96.82, -7.99, 0.96256, 69, -290.86, 58.74, 0.03744, 2, 6, 100.52, -36.36, 0.96646, 69, -287.15, 30.37, 0.03354, 2, 6, 100.14, 45.09, 0.96668, 69, -287.54, 111.81, 0.03332, 2, 6, 21.3, 14.79, 0.958, 69, -366.37, 81.51, 0.042, 2, 6, 15.77, 18.34, 0.958, 69, -371.91, 85.07, 0.042, 2, 6, 9.29, 17.92, 0.958, 69, -378.39, 84.65, 0.042, 2, 6, 6.01, 14.04, 0.958, 69, -381.66, 80.76, 0.042, 2, 6, 6.04, 8.81, 0.959, 69, -381.64, 75.54, 0.041, 2, 6, 2.68, 2.7, 0.959, 69, -385, 69.42, 0.041, 2, 6, 6.39, -3.29, 0.959, 69, -381.29, 63.44, 0.041, 2, 6, 7.09, -8.71, 0.959, 69, -380.59, 58.02, 0.041, 2, 6, 10.59, -12.37, 0.959, 69, -377.08, 54.36, 0.041, 2, 6, 16.98, -12.44, 0.958, 69, -370.7, 54.29, 0.042, 2, 6, 22, -9.02, 0.958, 69, -365.68, 57.7, 0.042, 2, 6, 34.42, 12.53, 0.959, 69, -353.25, 79.25, 0.041, 2, 6, 47.59, 13.36, 0.96, 69, -340.09, 80.09, 0.04, 2, 6, 49.74, 8.73, 0.958, 69, -337.94, 75.45, 0.042, 2, 6, 37.26, 7.21, 0.956, 69, -350.42, 73.94, 0.044, 2, 6, 25.34, 8.32, 0.95393, 69, -362.33, 75.05, 0.04607, 2, 6, 18.85, 10.81, 0.95351, 69, -368.83, 77.53, 0.04649, 2, 6, 13.81, 10.4, 0.95318, 69, -373.86, 77.12, 0.04682, 2, 6, 10.85, 3.61, 0.954, 69, -376.83, 70.33, 0.046, 2, 6, 13.88, -2.87, 0.95421, 69, -373.8, 63.86, 0.04579, 2, 6, 19.01, -3.04, 0.95354, 69, -368.67, 63.69, 0.04646, 2, 6, 25.39, -1.36, 0.95396, 69, -362.28, 65.37, 0.04604, 2, 6, 37.49, -1.11, 0.956, 69, -350.19, 65.62, 0.044, 2, 6, 49.88, -1.83, 0.958, 69, -337.79, 64.9, 0.042, 2, 6, 34.28, -6.84, 0.959, 69, -353.39, 59.89, 0.041, 2, 6, 48.8, -6.86, 0.96, 69, -338.87, 59.86, 0.04, 2, 6, 50.62, 3.28, 0.957, 69, -337.06, 70.01, 0.043, 2, 6, 38.62, 3.13, 0.955, 69, -349.06, 69.86, 0.045, 2, 6, 26.51, 3.46, 0.952, 69, -361.16, 70.19, 0.048, 2, 6, 18.33, 3.57, 0.95047, 69, -369.35, 70.29, 0.04953, 2, 6, -9.46, 22.14, 0.96, 69, -397.14, 88.87, 0.04, 2, 6, -6.22, 17.89, 0.95926, 69, -393.9, 84.62, 0.04074, 2, 6, -2.73, 12.45, 0.95832, 69, -390.4, 79.18, 0.04168, 2, 6, -2.63, 7.52, 0.95759, 69, -390.31, 74.25, 0.04241, 2, 6, -5.92, 2.36, 0.95776, 69, -393.59, 69.09, 0.04224, 2, 6, -2.87, -1.36, 0.95783, 69, -390.55, 65.37, 0.04217, 2, 6, -2.98, -6.33, 0.95855, 69, -390.66, 60.4, 0.04145, 2, 6, -5.45, -12, 0.95944, 69, -393.12, 54.73, 0.04056, 2, 6, -9.3, -17.16, 0.96, 69, -396.97, 49.57, 0.04, 2, 6, -10.31, 24.52, 0.96, 69, -397.98, 91.24, 0.04, 2, 6, -11.28, 21.76, 0.96, 69, -398.95, 88.49, 0.04, 2, 6, -11.18, 14.86, 0.95925, 69, -398.85, 81.59, 0.04075, 2, 6, -11.71, 8.87, 0.95877, 69, -399.39, 75.59, 0.04123, 2, 6, -13.02, 2.52, 0.9588, 69, -400.7, 69.25, 0.0412, 2, 6, -11.58, -3.81, 0.95894, 69, -399.26, 62.91, 0.04106, 2, 6, -11.06, -9.8, 0.95952, 69, -398.73, 56.93, 0.04048, 2, 6, -11.31, -16.78, 0.96, 69, -398.98, 49.95, 0.04, 2, 6, -9.96, -19.94, 0.96, 69, -397.63, 46.79, 0.04, 2, 6, -16.38, 19.57, 0.95999, 69, -404.06, 86.3, 0.04001, 2, 6, -21.58, 15.39, 0.95915, 69, -409.25, 82.12, 0.04085, 2, 6, -24.59, 10.04, 0.95839, 69, -412.26, 76.76, 0.04161, 2, 6, -25.67, 2.25, 0.95781, 69, -413.35, 68.98, 0.04219, 2, 6, -24.16, -5.62, 0.95845, 69, -411.83, 61.11, 0.04155, 2, 6, -20.32, -11.59, 0.95932, 69, -408, 55.14, 0.04068, 2, 6, -15.72, -16.14, 0.9601, 69, -403.4, 50.58, 0.0399, 2, 6, -5.27, 24.9, 0.96, 69, -392.94, 91.63, 0.04, 2, 6, -9.65, 28.69, 0.96, 69, -397.33, 95.41, 0.04, 2, 6, -14.01, 26.48, 0.96, 69, -401.69, 93.21, 0.04, 2, 6, -5.13, -20.18, 0.96, 69, -392.8, 46.54, 0.04, 2, 6, -9.61, -24.36, 0.96, 69, -397.29, 42.36, 0.04, 2, 6, -14, -21.42, 0.96, 69, -401.67, 45.3, 0.04, 2, 6, -16.84, 10.27, 0.95798, 69, -404.51, 76.99, 0.04202, 2, 6, -17.81, 2.39, 0.95716, 69, -405.49, 69.11, 0.04284, 2, 6, -17.03, -5.15, 0.95794, 69, -404.71, 61.58, 0.04206, 2, 6, -14.61, -11.26, 0.95872, 69, -402.28, 55.47, 0.04128, 2, 6, -14.6, 15.19, 0.95863, 69, -402.27, 81.92, 0.04137, 2, 6, -7.75, 12.61, 0.95933, 69, -395.42, 79.33, 0.04067, 2, 6, -7.97, 7.31, 0.95888, 69, -395.64, 74.04, 0.04112, 2, 6, -9.88, 2.63, 0.95846, 69, -397.55, 69.36, 0.04154, 2, 6, -8.05, -1.9, 0.95886, 69, -395.73, 64.82, 0.04114, 2, 6, -7.9, -6.88, 0.95925, 69, -395.58, 59.84, 0.04075, 2, 6, 27.98, -52.47, 0.96786, 69, -359.7, 14.25, 0.03214, 2, 6, 8.5, -46.82, 0.96687, 69, -379.18, 19.9, 0.03313, 2, 6, -14.72, -33.41, 0.96575, 69, -402.4, 33.32, 0.03425, 2, 6, -31.46, -15.92, 0.96287, 69, -419.14, 50.8, 0.03713, 2, 6, -37.44, -3.26, 0.95917, 69, -425.11, 63.47, 0.04083, 2, 6, -37.52, 9.28, 0.95913, 69, -425.2, 76, 0.04087, 2, 6, -31.65, 20.79, 0.96189, 69, -419.33, 87.52, 0.03811, 2, 6, -15.25, 38.28, 0.96564, 69, -402.92, 105.01, 0.03436, 2, 6, 6.74, 50.8, 0.96686, 69, -380.93, 117.52, 0.03314, 2, 6, 27.09, 56.45, 0.9674, 69, -360.58, 123.18, 0.0326, 2, 6, -27.7, 11.17, 0.96, 69, -415.37, 77.89, 0.04, 2, 6, -27.29, 2.17, 0.96, 69, -414.97, 68.9, 0.04, 2, 6, -27.45, -6.78, 0.96, 69, -415.12, 59.94, 0.04, 2, 6, 23.53, 32.63, 0.96, 69, -364.14, 99.36, 0.04, 2, 6, 4.37, 32.28, 0.96, 69, -383.31, 99.01, 0.04, 2, 6, 25.44, -28.85, 0.96, 69, -362.24, 37.88, 0.04, 2, 6, 5.04, -27.02, 0.96, 69, -382.64, 39.71, 0.04], "hull": 38, "edges": [18, 20, 20, 22, 22, 24, 34, 36, 36, 38, 40, 42, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 74, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 70, 72, 72, 74, 78, 80, 82, 84, 84, 86, 86, 88, 82, 80, 90, 92, 92, 76, 76, 98, 98, 96, 90, 100, 100, 94, 78, 102, 102, 96, 88, 104, 104, 94, 106, 108, 108, 110, 110, 112, 114, 116, 116, 118, 120, 122, 122, 106, 112, 128, 128, 126, 114, 130, 130, 126, 118, 132, 132, 124, 120, 134, 134, 124, 14, 136, 136, 28, 60, 138, 138, 46, 140, 142, 140, 144, 146, 148, 148, 150, 150, 152, 152, 154, 142, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 154, 144, 166, 166, 146, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 212, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 226, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 308, 306, 306, 278, 282, 280, 280, 258, 310, 312, 312, 314, 316, 314, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 368, 370, 370, 372, 374, 376, 376, 378, 380, 382, 382, 384, 380, 388, 384, 386, 390, 392, 392, 394, 394, 396, 396, 398, 400, 402, 402, 404, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 420, 422, 422, 424, 426, 428, 430, 432], "width": 180, "height": 176}}, "head2": {"head": {"type": "mesh", "uvs": [0.29203, 0.81897, 0.32588, 0.7077, 0.40577, 0.68014, 0.42729, 0.69883, 0.45633, 0.69883, 0.49021, 0.71809, 0.52355, 0.69719, 0.55367, 0.69334, 0.5741, 0.67354, 0.65533, 0.70549, 0.69032, 0.81792, 0.75407, 0.83539, 0.59992, 0.95799, 0.52739, 0.99133, 0.44913, 0.99207, 0.38724, 0.95916, 0.2523, 0.85431, 0.38184, 0.78656, 0.40555, 0.76824, 0.43585, 0.74853, 0.46325, 0.74813, 0.49182, 0.76694, 0.51257, 0.74973, 0.54018, 0.75049, 0.57163, 0.76464, 0.6002, 0.78666, 0.36865, 0.79127, 0.38391, 0.79688, 0.42227, 0.79648, 0.45554, 0.79968, 0.49076, 0.80729, 0.52599, 0.79928, 0.55926, 0.79648, 0.59801, 0.79808, 0.61562, 0.79047, 0.39594, 0.82593, 0.41904, 0.85556, 0.4487, 0.87282, 0.49194, 0.87917, 0.53568, 0.87077, 0.56895, 0.84915, 0.59439, 0.82313, 0.36662, 0.76263, 0.34549, 0.78745, 0.35762, 0.81227, 0.61711, 0.76303, 0.6402, 0.78865, 0.62376, 0.81347, 0.44763, 0.82878, 0.49138, 0.83453, 0.53326, 0.83028, 0.56728, 0.81667, 0.42034, 0.81589, 0.43486, 0.77704, 0.46427, 0.77846, 0.49022, 0.78943, 0.51547, 0.77917, 0.54315, 0.77846, 0.59275, 0.91255, 0.52225, 0.94617, 0.45258, 0.94633, 0.38877, 0.91265, 0.44236, 0.89046, 0.49234, 0.88839, 0.54208, 0.88951], "triangles": [20, 4, 5, 19, 3, 4, 19, 4, 20, 22, 5, 6, 21, 20, 5, 23, 6, 7, 22, 6, 23, 42, 1, 2, 42, 2, 3, 18, 42, 3, 45, 8, 9, 7, 8, 45, 24, 23, 7, 45, 24, 7, 22, 21, 5, 19, 18, 3, 53, 18, 19, 57, 23, 24, 54, 20, 21, 53, 19, 20, 54, 53, 20, 56, 22, 23, 56, 23, 57, 21, 22, 56, 17, 42, 18, 25, 24, 45, 43, 1, 42, 26, 43, 42, 46, 45, 9, 34, 25, 45, 55, 54, 21, 55, 21, 56, 46, 34, 45, 17, 26, 42, 32, 57, 24, 32, 24, 25, 28, 18, 53, 27, 17, 18, 28, 27, 18, 26, 17, 27, 33, 32, 25, 33, 25, 34, 31, 56, 57, 31, 57, 32, 29, 53, 54, 28, 53, 29, 31, 30, 55, 31, 55, 56, 29, 54, 55, 30, 29, 55, 44, 43, 26, 47, 34, 46, 33, 34, 47, 52, 27, 28, 51, 32, 33, 46, 9, 10, 0, 1, 43, 0, 43, 44, 41, 51, 33, 41, 33, 47, 35, 27, 52, 29, 52, 28, 48, 52, 29, 50, 31, 32, 50, 32, 51, 50, 49, 30, 50, 30, 31, 48, 29, 30, 49, 48, 30, 40, 51, 41, 50, 51, 40, 36, 35, 52, 36, 52, 48, 39, 50, 40, 49, 50, 39, 37, 48, 49, 36, 48, 37, 38, 49, 39, 37, 49, 38, 63, 38, 39, 64, 39, 40, 63, 39, 64, 62, 36, 37, 63, 62, 37, 63, 37, 38, 58, 40, 41, 64, 40, 58, 58, 41, 47, 10, 58, 47, 10, 47, 46, 27, 44, 26, 61, 35, 36, 61, 36, 62, 27, 35, 44, 61, 44, 35, 0, 44, 61, 59, 63, 64, 59, 64, 58, 60, 62, 63, 60, 63, 59, 61, 62, 60, 12, 58, 10, 59, 58, 12, 12, 10, 11, 61, 16, 0, 15, 61, 60, 15, 16, 61, 13, 59, 12, 14, 15, 60, 14, 60, 59, 14, 59, 13], "vertices": [2, 6, -15.25, 38.28, 0.96564, 69, -402.92, 105.01, 0.03436, 2, 6, 4.37, 32.28, 0.96, 69, -383.31, 99.01, 0.04, 2, 6, 9.29, 17.92, 0.958, 69, -378.39, 84.65, 0.042, 2, 6, 6.01, 14.04, 0.958, 69, -381.66, 80.76, 0.042, 2, 6, 6.04, 8.81, 0.959, 69, -381.64, 75.54, 0.041, 2, 6, 2.68, 2.7, 0.959, 69, -385, 69.42, 0.041, 2, 6, 6.39, -3.29, 0.959, 69, -381.29, 63.44, 0.041, 2, 6, 7.09, -8.71, 0.959, 69, -380.59, 58.02, 0.041, 2, 6, 10.59, -12.37, 0.959, 69, -377.08, 54.36, 0.041, 2, 6, 5.04, -27.02, 0.96, 69, -382.64, 39.71, 0.04, 2, 6, -14.72, -33.41, 0.96575, 69, -402.4, 33.32, 0.03425, 2, 6, -17.74, -44.9, 0.97652, 69, -405.42, 21.83, 0.02348, 2, 6, -39.45, -17.25, 0.96609, 69, -427.13, 49.47, 0.03391, 2, 6, -45.38, -4.22, 0.96196, 69, -433.05, 62.5, 0.03804, 2, 6, -45.58, 9.86, 0.96179, 69, -433.25, 76.59, 0.03821, 2, 6, -39.84, 21.03, 0.96549, 69, -427.51, 87.76, 0.03451, 2, 6, -21.5, 45.4, 0.97526, 69, -409.17, 112.13, 0.02474, 2, 6, -9.46, 22.14, 0.96, 69, -397.14, 88.87, 0.04, 2, 6, -6.22, 17.89, 0.95926, 69, -393.9, 84.62, 0.04074, 2, 6, -2.73, 12.45, 0.95832, 69, -390.4, 79.18, 0.04168, 2, 6, -2.63, 7.52, 0.95759, 69, -390.31, 74.25, 0.04241, 2, 6, -5.92, 2.36, 0.95776, 69, -393.59, 69.09, 0.04224, 2, 6, -2.87, -1.36, 0.95783, 69, -390.55, 65.37, 0.04217, 2, 6, -2.98, -6.33, 0.95855, 69, -390.66, 60.4, 0.04145, 2, 6, -5.45, -12, 0.95944, 69, -393.12, 54.73, 0.04056, 2, 6, -9.3, -17.16, 0.96, 69, -396.97, 49.57, 0.04, 2, 6, -10.31, 24.52, 0.96, 69, -397.98, 91.24, 0.04, 2, 6, -11.28, 21.76, 0.96, 69, -398.95, 88.49, 0.04, 2, 6, -11.18, 14.86, 0.95925, 69, -398.85, 81.59, 0.04075, 2, 6, -11.71, 8.87, 0.95877, 69, -399.39, 75.59, 0.04123, 2, 6, -13.02, 2.52, 0.9588, 69, -400.7, 69.25, 0.0412, 2, 6, -11.58, -3.81, 0.95894, 69, -399.26, 62.91, 0.04106, 2, 6, -11.06, -9.8, 0.95952, 69, -398.73, 56.93, 0.04048, 2, 6, -11.31, -16.78, 0.96, 69, -398.98, 49.95, 0.04, 2, 6, -9.96, -19.94, 0.96, 69, -397.63, 46.79, 0.04, 2, 6, -16.38, 19.57, 0.95999, 69, -404.06, 86.3, 0.04001, 2, 6, -21.58, 15.39, 0.95915, 69, -409.25, 82.12, 0.04085, 2, 6, -24.59, 10.04, 0.95839, 69, -412.26, 76.76, 0.04161, 2, 6, -25.67, 2.25, 0.95781, 69, -413.35, 68.98, 0.04219, 2, 6, -24.16, -5.62, 0.95845, 69, -411.83, 61.11, 0.04155, 2, 6, -20.32, -11.59, 0.95932, 69, -408, 55.14, 0.04068, 2, 6, -15.72, -16.14, 0.9601, 69, -403.4, 50.58, 0.0399, 2, 6, -5.27, 24.9, 0.96, 69, -392.94, 91.63, 0.04, 2, 6, -9.65, 28.69, 0.96, 69, -397.33, 95.41, 0.04, 2, 6, -14.01, 26.48, 0.96, 69, -401.69, 93.21, 0.04, 2, 6, -5.13, -20.18, 0.96, 69, -392.8, 46.54, 0.04, 2, 6, -9.61, -24.36, 0.96, 69, -397.29, 42.36, 0.04, 2, 6, -14, -21.42, 0.96, 69, -401.67, 45.3, 0.04, 2, 6, -16.84, 10.27, 0.95798, 69, -404.51, 76.99, 0.04202, 2, 6, -17.81, 2.39, 0.95716, 69, -405.49, 69.11, 0.04284, 2, 6, -17.03, -5.15, 0.95794, 69, -404.71, 61.58, 0.04206, 2, 6, -14.61, -11.26, 0.95872, 69, -402.28, 55.47, 0.04128, 2, 6, -14.6, 15.19, 0.95863, 69, -402.27, 81.92, 0.04137, 2, 6, -7.75, 12.61, 0.95933, 69, -395.42, 79.33, 0.04067, 2, 6, -7.97, 7.31, 0.95888, 69, -395.64, 74.04, 0.04112, 2, 6, -9.88, 2.63, 0.95846, 69, -397.55, 69.36, 0.04154, 2, 6, -8.05, -1.9, 0.95886, 69, -395.73, 64.82, 0.04114, 2, 6, -7.9, -6.88, 0.95925, 69, -395.58, 59.84, 0.04075, 2, 6, -31.46, -15.92, 0.96287, 69, -419.14, 50.8, 0.03713, 2, 6, -37.44, -3.26, 0.95917, 69, -425.11, 63.47, 0.04083, 2, 6, -37.52, 9.28, 0.95913, 69, -425.2, 76, 0.04087, 2, 6, -31.65, 20.79, 0.96189, 69, -419.33, 87.52, 0.03811, 2, 6, -27.7, 11.17, 0.96, 69, -415.37, 77.89, 0.04, 2, 6, -27.29, 2.17, 0.96, 69, -414.97, 68.9, 0.04, 2, 6, -27.45, -6.78, 0.96, 69, -415.12, 59.94, 0.04], "hull": 17, "edges": [24, 26, 26, 28, 30, 32, 22, 24, 28, 30, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 90, 92, 92, 94, 96, 98, 98, 100, 96, 104, 100, 102, 106, 108, 108, 110, 110, 112, 112, 114, 20, 116, 116, 118, 118, 120, 120, 122, 122, 0, 124, 126, 126, 128, 4, 2, 2, 0, 0, 32, 16, 18, 18, 20, 20, 22], "width": 180, "height": 176}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.40793, 0.01695, 0.58249, 0.00759, 0.72194, 0.00012, 0.77582, 0.01492, 0.84581, 0.03897, 0.88226, 0.05267, 0.9105, 0.06945, 0.93037, 0.09588, 0.9699, 0.13639, 0.98342, 0.16819, 0.98431, 0.19139, 0.99641, 0.20086, 0.9948, 0.22002, 0.97467, 0.25523, 0.95757, 0.27544, 0.94085, 0.27821, 0.91302, 0.30225, 0.86851, 0.32608, 0.87602, 0.32888, 0.85477, 0.34354, 0.79854, 0.39112, 0.70434, 0.4658, 0.68272, 0.48805, 0.67401, 0.5097, 0.67278, 0.52495, 0.68514, 0.54471, 0.71676, 0.57313, 0.74409, 0.59914, 0.75369, 0.64257, 0.69241, 0.69454, 0.4711, 0.79953, 0.43922, 0.81521, 0.41555, 0.83008, 0.41081, 0.84195, 0.42719, 0.85813, 0.37691, 0.87246, 0.387, 0.88324, 0.37993, 0.90459, 0.37797, 0.92176, 0.38613, 0.93604, 0.39794, 0.94908, 0.41791, 0.96649, 0.41871, 0.98403, 0.3815, 0.99456, 0.27123, 0.99977, 0.1206, 0.99959, 0.03968, 0.99364, 0.00376, 0.98128, 0.00349, 0.96532, 0.03586, 0.94796, 0.05921, 0.93323, 0.07934, 0.91946, 0.09097, 0.90413, 0.09862, 0.87812, 0.11949, 0.87006, 0.11098, 0.85349, 0.1569, 0.8372, 0.18809, 0.82363, 0.21753, 0.80492, 0.25644, 0.76378, 0.26166, 0.63882, 0.30347, 0.58904, 0.30535, 0.56518, 0.27893, 0.54552, 0.2663, 0.52573, 0.26662, 0.50154, 0.26955, 0.4814, 0.26893, 0.4585, 0.24368, 0.3767, 0.23459, 0.32912, 0.2348, 0.31353, 0.2447, 0.31186, 0.25193, 0.2647, 0.24081, 0.26251, 0.24501, 0.24316, 0.25334, 0.21575, 0.26014, 0.19753, 0.28697, 0.19262, 0.28692, 0.1875, 0.23393, 0.18251, 0.23379, 0.14346, 0.23365, 0.10441, 0.23351, 0.06535, 0.23337, 0.0263, 0.36323, 0.47905, 0.35234, 0.4999, 0.34301, 0.52043, 0.34612, 0.54779, 0.36634, 0.57451, 0.38656, 0.59797, 0.55766, 0.481, 0.54366, 0.5012, 0.52811, 0.52271, 0.51877, 0.54486, 0.52655, 0.57093, 0.54988, 0.59504, 0.382, 0.75731, 0.57166, 0.64446, 0.36831, 0.64381, 0.27475, 0.85433, 0.29379, 0.8407, 0.3176, 0.82558, 0.21364, 0.9584, 0.21795, 0.9463, 0.2214, 0.93312, 0.22485, 0.91813, 0.23433, 0.90513, 0.24554, 0.88327, 0.25502, 0.87136, 0.2116, 0.97494, 0.37159, 0.45707, 0.38587, 0.31659, 0.57643, 0.46014, 0.71895, 0.32306, 0.38957, 0.25945, 0.42022, 0.1767, 0.40785, 0.20216, 0.39719, 0.23371, 0.76891, 0.26465, 0.82375, 0.17571, 0.80991, 0.20346, 0.79024, 0.23633, 0.35575, 0.17234, 0.45769, 0.14369, 0.56409, 0.1176, 0.7579, 0.08871, 0.81802, 0.12787, 0.63784, 0.13484, 0.63148, 0.17262, 0.62204, 0.19768, 0.60772, 0.23398, 0.5948, 0.25973, 0.56567, 0.32012, 0.65202, 0.0338, 0.71001, 0.06251, 0.45022, 0.04794, 0.49561, 0.07834], "triangles": [47, 48, 109, 102, 48, 49, 46, 47, 109, 45, 46, 109, 102, 49, 103, 44, 109, 43, 41, 109, 102, 42, 43, 109, 102, 40, 41, 42, 109, 41, 40, 102, 103, 109, 48, 102, 45, 109, 44, 133, 1, 2, 133, 2, 3, 133, 3, 4, 135, 0, 1, 135, 1, 133, 55, 56, 99, 108, 54, 55, 99, 108, 55, 99, 33, 34, 35, 99, 34, 108, 99, 35, 107, 54, 108, 53, 54, 107, 36, 107, 35, 107, 52, 53, 106, 52, 107, 107, 108, 35, 37, 107, 36, 105, 52, 106, 51, 52, 105, 37, 106, 107, 38, 106, 37, 105, 106, 38, 104, 51, 105, 104, 105, 38, 50, 51, 104, 104, 38, 39, 103, 50, 104, 103, 104, 39, 49, 50, 103, 103, 39, 40, 93, 86, 92, 87, 63, 64, 86, 87, 64, 93, 87, 86, 62, 63, 87, 93, 25, 94, 24, 93, 92, 25, 93, 24, 88, 87, 93, 88, 62, 87, 94, 25, 26, 93, 94, 88, 61, 62, 88, 95, 94, 26, 89, 88, 94, 89, 94, 95, 61, 88, 89, 95, 26, 27, 60, 61, 89, 98, 60, 89, 97, 95, 27, 97, 27, 28, 29, 97, 28, 96, 97, 29, 89, 95, 97, 97, 98, 89, 97, 96, 98, 96, 59, 60, 96, 60, 98, 30, 96, 29, 30, 59, 96, 30, 58, 59, 31, 58, 30, 101, 58, 31, 57, 58, 101, 32, 101, 31, 100, 57, 101, 56, 57, 100, 33, 101, 32, 100, 101, 33, 99, 56, 100, 33, 99, 100, 127, 124, 125, 126, 127, 125, 126, 7, 8, 128, 123, 127, 119, 126, 8, 119, 8, 9, 127, 126, 119, 128, 127, 119, 119, 9, 10, 129, 115, 128, 116, 115, 129, 119, 129, 128, 120, 119, 10, 120, 10, 11, 120, 129, 119, 12, 120, 11, 117, 75, 116, 130, 116, 129, 130, 129, 120, 117, 116, 130, 121, 130, 120, 121, 120, 12, 74, 75, 117, 13, 121, 12, 114, 74, 117, 131, 117, 130, 114, 117, 131, 72, 73, 74, 118, 130, 121, 118, 121, 13, 131, 130, 118, 114, 72, 74, 13, 15, 118, 14, 15, 13, 16, 118, 15, 111, 72, 114, 71, 72, 111, 132, 114, 131, 113, 132, 131, 118, 113, 131, 111, 114, 132, 16, 113, 118, 17, 113, 16, 19, 113, 17, 19, 17, 18, 69, 71, 111, 71, 69, 70, 68, 69, 111, 20, 113, 19, 112, 110, 132, 111, 110, 68, 110, 111, 132, 113, 112, 132, 67, 68, 110, 113, 20, 112, 21, 112, 20, 84, 67, 110, 90, 110, 112, 90, 112, 21, 84, 110, 90, 66, 67, 84, 22, 90, 21, 85, 66, 84, 91, 84, 90, 91, 90, 22, 85, 84, 91, 65, 66, 85, 23, 91, 22, 86, 65, 85, 92, 85, 91, 92, 91, 23, 86, 85, 92, 24, 92, 23, 64, 65, 86, 128, 115, 123, 123, 124, 127, 136, 135, 133, 134, 133, 4, 134, 4, 5, 136, 133, 134, 6, 125, 134, 6, 134, 5, 125, 136, 134, 125, 124, 136, 125, 6, 7, 126, 125, 7, 135, 82, 83, 135, 83, 0, 82, 135, 136, 81, 82, 136, 81, 136, 124, 123, 80, 81, 124, 123, 81, 122, 80, 123, 115, 122, 123, 79, 80, 122, 78, 79, 122, 116, 77, 78, 116, 122, 115, 116, 78, 122, 77, 75, 76, 77, 116, 75], "vertices": [3, 7, -0.75, 34.52, 0.40373, 20, -25.91, 32.69, 0.56205, 73, -352.59, 105.16, 0.03422, 3, 7, -13.1, 78.21, 0.00501, 20, 19.18, 27.32, 0.96592, 73, -308.6, 116.41, 0.02908, 2, 8, -46.79, 22.08, 0.00729, 20, 55.2, 23.03, 0.99271, 1, 20, 61.44, 1.53, 1, 2, 8, -0.85, 54.44, 0.45303, 20, 67.46, -31.81, 0.54697, 2, 8, 15.4, 64.04, 0.68701, 20, 70.08, -50.5, 0.31299, 3, 8, 35.4, 71.65, 0.71378, 12, -68.64, 43.26, 0.151, 20, 69.44, -71.89, 0.13522, 3, 8, 67.07, 77.45, 0.666, 12, -37.93, 52.92, 0.29403, 20, 62.65, -103.36, 0.03997, 2, 8, 115.53, 88.62, 0.13582, 12, 8.79, 69.99, 0.86418, 2, 8, 153.69, 92.98, 0.04875, 12, 46.12, 79.02, 0.95125, 1, 12, 73.68, 83.37, 1, 1, 12, 84.51, 88.08, 1, 1, 12, 107.36, 91.09, 1, 1, 12, 150, 92.34, 1, 1, 12, 174.68, 91.68, 1, 1, 12, 178.6, 88.01, 1, 1, 12, 208.24, 85.35, 1, 1, 12, 238.25, 78.5, 1, 1, 12, 241.31, 80.87, 1, 1, 12, 259.54, 78.19, 1, 1, 12, 318.24, 72.65, 1, 2, 12, 410.61, 62.47, 0.99488, 13, -76.98, 67.43, 0.00512, 2, 12, 437.89, 61.05, 0.93447, 13, -49.84, 64.3, 0.06553, 2, 12, 463.97, 62.73, 0.7332, 13, -23.7, 64.34, 0.2668, 2, 12, 482.16, 65.14, 0.50525, 13, -5.39, 65.61, 0.49475, 2, 12, 505.21, 71.74, 0.23159, 13, 18.03, 70.75, 0.76841, 2, 12, 537.84, 84.68, 0.04064, 13, 51.41, 81.62, 0.95936, 2, 12, 567.77, 96.13, 0.00266, 13, 82, 91.16, 0.99734, 1, 13, 133.86, 98.05, 1, 1, 13, 197.48, 88.03, 1, 1, 13, 328.12, 43.29, 1, 1, 13, 347.61, 36.91, 1, 2, 13, 365.95, 32.5, 0.94051, 14, 150.38, -31.8, 0.05949, 2, 13, 380.28, 32.53, 0.69354, 14, 136.06, -32.22, 0.30646, 2, 13, 399.32, 38.31, 0.31934, 14, 117.19, -38.51, 0.68066, 2, 13, 417.59, 27.17, 0.04845, 14, 98.63, -27.85, 0.95155, 2, 13, 430.29, 30.81, 0.00441, 14, 86.03, -31.84, 0.99559, 1, 14, 60.31, -32.96, 1, 2, 14, 39.72, -34.79, 0.94852, 0, 31.68, 89.95, 0.05148, 2, 14, 22.89, -38.76, 0.66083, 0, 33.74, 72.78, 0.33917, 2, 14, 7.64, -43.49, 0.39928, 0, 36.72, 57.09, 0.60072, 2, 14, -12.61, -50.84, 0.07533, 0, 41.75, 36.14, 0.92467, 1, 0, 41.95, 15.04, 1, 1, 0, 32.57, 2.37, 1, 1, 0, 4.78, -3.89, 1, 1, 0, -33.18, -3.67, 1, 1, 0, -53.57, 3.48, 1, 1, 0, -62.62, 18.35, 1, 2, 14, -22.95, 53.09, 0.08544, 0, -62.69, 37.56, 0.91456, 2, 14, -1.29, 47.33, 0.4276, 0, -54.53, 58.43, 0.5724, 2, 14, 16.99, 43.48, 0.72274, 0, -48.65, 76.16, 0.27726, 2, 14, 34.02, 40.3, 0.99357, 0, -43.57, 92.73, 0.00643, 1, 14, 52.67, 39.46, 1, 2, 13, 430.38, -42.12, 0.00377, 14, 83.98, 41.06, 0.99623, 2, 13, 420.27, -37.71, 0.02812, 14, 94.21, 36.93, 0.97188, 2, 13, 400.59, -41.56, 0.20053, 14, 113.77, 41.3, 0.79947, 2, 13, 380.08, -31.71, 0.60873, 14, 134.54, 32, 0.39127, 2, 13, 363.14, -25.28, 0.94555, 14, 151.65, 26.03, 0.05445, 1, 13, 340.06, -19.82, 1, 1, 13, 289.91, -14.29, 1, 2, 12, 632.98, -17.04, 0, 13, 140, -25.87, 1, 2, 12, 572.19, -15.49, 0, 13, 79.42, -20.51, 1, 1, 13, 50.77, -22.49, 1, 2, 12, 521.33, -29.35, 0.08195, 13, 27.78, -31.15, 0.91805, 2, 12, 498.25, -36.02, 0.50115, 13, 4.33, -36.37, 0.49885, 2, 12, 469.46, -40.25, 0.91835, 13, -24.68, -38.78, 0.08165, 1, 12, 445.39, -43.11, 1, 1, 12, 418.17, -47.34, 1, 1, 12, 321.79, -68.2, 1, 2, 7, 375.77, 0.2, 0.00733, 12, 265.53, -78.94, 0.99267, 2, 7, 357.01, -0.22, 0.01622, 12, 246.96, -81.67, 0.98378, 2, 7, 354.94, 2.23, 0.01819, 12, 244.61, -79.5, 0.98181, 2, 7, 298.18, 2.64, 0.12812, 12, 188.23, -86.1, 0.87188, 2, 7, 295.62, -0.23, 0.13851, 12, 186.04, -89.26, 0.86149, 2, 7, 272.32, 0.25, 0.23245, 12, 162.86, -91.66, 0.76755, 2, 7, 239.31, 1.53, 0.43895, 12, 129.94, -94.46, 0.56105, 2, 7, 217.35, 2.69, 0.60305, 12, 108.01, -96.01, 0.39695, 2, 7, 211.28, 9.31, 0.69322, 12, 101.17, -90.2, 0.30678, 2, 7, 205.12, 9.14, 0.79934, 12, 95.08, -91.12, 0.20066, 2, 7, 199.45, -4.36, 0.96797, 12, 91.12, -105.22, 0.03203, 1, 7, 152.49, -5.56, 1, 1, 7, 105.52, -6.77, 1, 1, 7, 58.56, -7.97, 1, 1, 7, 11.59, -9.18, 1, 2, 12, 439.1, -20.18, 0.98038, 73, -363.85, -450.75, 0.01962, 3, 12, 464.31, -19.18, 0.94868, 13, -28.5, -17.43, 0.0317, 73, -366.6, -475.83, 0.01961, 3, 12, 489.08, -17.85, 0.63999, 13, -3.69, -17.65, 0.34033, 73, -368.95, -500.53, 0.01968, 2, 13, 29.05, -14.05, 0.98006, 73, -368.16, -533.45, 0.01994, 2, 13, 60.64, -6.22, 0.98336, 73, -363.07, -565.59, 0.01664, 2, 13, 88.33, 1.28, 0.9864, 73, -357.97, -593.81, 0.0136, 3, 12, 434.17, 28.62, 0.96337, 13, -55.59, 32.17, 0.01681, 73, -314.86, -453.1, 0.01982, 3, 12, 458.72, 28.73, 0.89123, 13, -31.07, 30.74, 0.08938, 73, -318.39, -477.4, 0.01939, 3, 12, 484.89, 28.69, 0.53964, 13, -4.95, 29.05, 0.44064, 73, -322.3, -503.27, 0.01972, 3, 12, 511.6, 30.31, 0.15479, 13, 21.81, 29, 0.82553, 73, -324.66, -529.92, 0.01968, 3, 12, 542.32, 36.89, 0.01807, 13, 52.89, 33.64, 0.96489, 73, -322.7, -561.28, 0.01703, 2, 13, 81.29, 41.98, 0.98554, 73, -316.82, -590.29, 0.01446, 1, 13, 279.43, 16.57, 1, 2, 13, 140.06, 52.55, 0.99105, 73, -311.33, -649.74, 0.00895, 2, 13, 143.67, 1.42, 0.99164, 73, -362.57, -648.96, 0.00836, 2, 13, 398.06, -0.35, 0.10883, 14, 117.41, 0.18, 0.89117, 2, 13, 381.31, 3.02, 0.85777, 14, 134.24, -2.75, 0.14223, 2, 13, 362.67, 7.44, 0.99976, 14, 152.99, -6.66, 0.00024, 2, 14, -8.72, 1.4, 0.21239, 0, -9.73, 45.88, 0.78761, 2, 14, 5.86, 1.96, 0.4556, 0, -8.64, 60.43, 0.5444, 2, 14, 21.71, 2.88, 0.72006, 0, -7.77, 76.29, 0.27994, 1, 14, 39.72, 4.04, 1, 1, 14, 55.53, 3.43, 1, 1, 14, 81.99, 3.58, 1, 1, 14, 96.5, 2.82, 1, 1, 0, -10.24, 25.98, 1, 2, 12, 412.64, -22.01, 0.98028, 73, -361.75, -424.31, 0.01972, 3, 7, 359.75, 37.94, 0.00909, 12, 244.97, -43.47, 0.9712, 73, -358.15, -255.31, 0.0197, 3, 12, 408.65, 29.59, 0.97952, 13, -81, 34.73, 0.00098, 73, -310.13, -428, 0.0195, 2, 12, 240.24, 40.69, 0.98019, 73, -274.21, -263.1, 0.01981, 3, 7, 291.01, 37.16, 0.10264, 12, 176.85, -52.73, 0.87753, 73, -357.22, -186.57, 0.01984, 3, 7, 191.29, 42.4, 0.56185, 12, 77.25, -59.83, 0.42212, 73, -349.49, -87.02, 0.01603, 3, 7, 221.99, 40.04, 0.41099, 12, 108.01, -58.38, 0.57122, 73, -352.61, -117.65, 0.01778, 3, 7, 260, 38.3, 0.21106, 12, 145.94, -55.41, 0.76928, 73, -355.3, -155.6, 0.01966, 2, 12, 168.88, 42.74, 0.98146, 73, -261.62, -192.82, 0.01854, 2, 12, 61.02, 40.56, 0.98033, 73, -247.8, -85.83, 0.01967, 2, 12, 94.55, 42.06, 0.98013, 73, -251.29, -119.21, 0.01987, 2, 12, 134.39, 43.01, 0.98019, 73, -256.25, -158.75, 0.01981, 3, 7, 186.46, 26.03, 0.80194, 12, 74.48, -76.67, 0.19107, 73, -365.74, -81.78, 0.007, 4, 7, 151.36, 50.85, 0.65824, 8, 127.53, -40.2, 0.01479, 12, 36.58, -56.37, 0.31377, 73, -340.05, -47.31, 0.01321, 5, 7, 119.31, 76.87, 0.39699, 8, 95.48, -14.17, 0.16373, 12, 1.57, -34.5, 0.42004, 20, -32.87, -94.44, 0.00159, 73, -313.24, -15.92, 0.01765, 5, 7, 83.36, 124.83, 0.01337, 8, 59.53, 33.79, 0.70805, 12, -40.03, 8.66, 0.25208, 20, 25.22, -79.64, 0.00968, 73, -264.4, 18.83, 0.01681, 4, 8, 106.24, 50.1, 0.1807, 12, 4.32, 30.62, 0.80218, 20, 22.36, -129.04, 9e-05, 73, -249.25, -28.28, 0.01704, 4, 7, 139.59, 95.97, 0.19126, 8, 115.76, 4.92, 0.02337, 12, 19.34, -13.05, 0.76583, 73, -294.65, -36.67, 0.01954, 3, 7, 185.06, 95.5, 0.03281, 12, 64.52, -7.9, 0.94545, 73, -296.26, -82.11, 0.02173, 3, 7, 215.25, 93.87, 0.0168, 12, 94.68, -5.79, 0.95928, 73, -298.63, -112.26, 0.02392, 3, 7, 259, 91.35, 0.00464, 12, 138.4, -2.9, 0.97073, 73, -302.24, -155.93, 0.02463, 3, 7, 290.05, 88.87, 0.00083, 12, 169.53, -1.53, 0.97417, 73, -305.5, -186.91, 0.02499, 2, 12, 242.47, 1.97, 0.97536, 73, -312.84, -259.56, 0.02464, 3, 8, -5.84, 5.47, 0.54438, 20, 24.15, -8.4, 0.43109, 73, -291.08, 84.89, 0.02453, 3, 7, 52.15, 111.98, 0.01368, 8, 28.32, 20.94, 0.96688, 73, -276.47, 50.34, 0.01944, 4, 7, 36.26, 46.1, 0.38572, 8, 12.43, -44.95, 0.10482, 20, -29.41, -5.93, 0.47661, 73, -341.93, 67.87, 0.03285, 4, 7, 72.54, 58.44, 0.45476, 8, 48.71, -32.6, 0.40004, 20, -31.93, -44.17, 0.11895, 73, -330.49, 31.3, 0.02625], "hull": 84, "edges": [4, 6, 10, 12, 36, 38, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 84, 86, 86, 88, 88, 90, 90, 92, 102, 104, 114, 116, 116, 118, 120, 122, 122, 124, 6, 8, 8, 10, 12, 14, 14, 16, 22, 24, 156, 158, 152, 154, 154, 156, 138, 140, 136, 138, 26, 28, 16, 18, 18, 20, 20, 22, 144, 146, 140, 142, 142, 144, 34, 36, 28, 30, 30, 32, 32, 34, 40, 42, 38, 40, 132, 134, 130, 132, 128, 130, 124, 126, 126, 128, 50, 52, 52, 54, 42, 44, 44, 46, 134, 136, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 192, 194, 194, 190, 178, 196, 196, 192, 118, 120, 110, 112, 112, 114, 108, 110, 104, 106, 106, 108, 66, 68, 60, 62, 70, 72, 62, 64, 64, 66, 68, 70, 72, 74, 74, 76, 96, 98, 98, 100, 100, 102, 76, 78, 78, 80, 80, 82, 82, 84, 92, 94, 94, 96, 168, 220, 220, 222, 180, 224, 224, 226, 222, 228, 232, 230, 228, 234, 234, 232, 226, 236, 24, 26, 146, 148, 148, 150, 150, 152, 236, 242, 242, 240, 240, 238, 250, 248, 248, 246, 246, 244, 252, 238, 254, 256, 258, 260, 258, 256, 260, 262, 262, 264, 162, 164, 164, 166, 158, 160, 160, 162, 4, 2, 2, 0, 0, 166], "width": 252, "height": 1203}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 4.46, "curve": [0.444, 4.46, 0.889, -4.46, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -4.46, "curve": [1.778, -4.47, 2.222, 4.46, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 4.46, "curve": [3.111, 4.47, 3.556, -4.46, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -4.46, "curve": [4.444, -4.47, 4.889, 4.46, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 4.46, "curve": [5.667, 4.47, 6, -4.46, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -4.46, "curve": [6.778, -4.47, 7.222, 0, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, -4.46, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -4.46, "curve": [9.444, -4.47, 9.889, 4.46, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 4.46, "curve": [10.778, 4.47, 11.222, -4.46, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -4.46, "curve": [12.111, -4.47, 12.556, 4.46, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 4.46}]}, "ALL2": {"translate": [{"y": -16.85, "curve": [0.444, 0, 0.889, 0, 0.444, -16.85, 0.889, 18.37]}, {"time": 1.3333, "y": 18.38, "curve": [1.778, 0, 2.222, 0, 1.778, 18.39, 2.222, -16.84]}, {"time": 2.6667, "y": -16.85, "curve": [3.111, 0, 3.556, 0, 3.111, -16.85, 3.556, 18.37]}, {"time": 4, "y": 18.38, "curve": [4.444, 0, 4.889, 0, 4.444, 18.39, 4.889, -16.83]}, {"time": 5.3333, "y": -16.85, "curve": [5.667, 0, 6, 0, 5.667, -16.85, 6, 18.37]}, {"time": 6.3333, "y": 18.38, "curve": [6.778, 0, 7.222, 0, 6.778, 18.38, 7.222, 0.77]}, {"time": 7.6667, "y": 0.77, "curve": [8.111, 0, 8.556, 0, 8.111, 0.76, 8.556, 18.37]}, {"time": 9, "y": 18.38, "curve": [9.444, 0, 9.889, 0, 9.444, 18.39, 9.889, -16.84]}, {"time": 10.3333, "y": -16.85, "curve": [10.778, 0, 11.222, 0, 10.778, -16.85, 11.222, 18.37]}, {"time": 11.6667, "y": 18.38, "curve": [12.111, 0, 12.556, 0, 12.111, 18.39, 12.556, -16.85]}, {"time": 13, "y": -16.85}]}, "body": {"rotate": [{"time": 4.1667, "curve": [4.611, 0, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, -2.49]}, {"time": 6.5, "value": -2.49, "curve": [6.944, -2.49, 7.389, -1.24]}, {"time": 7.8333, "value": -1.24, "curve": [8.278, -1.24, 8.722, -2.49]}, {"time": 9.1667, "value": -2.49, "curve": [9.611, -2.49, 10.056, 0]}, {"time": 10.5}], "translate": [{"y": -10.55, "curve": [0.057, 0, 0.112, 0, 0.057, -11.16, 0.112, -11.62]}, {"time": 0.1667, "y": -11.62, "curve": [0.611, 0, 1.056, 0, 0.611, -11.62, 1.056, 11.24]}, {"time": 1.5, "y": 11.25, "curve": [1.944, 0, 2.389, 0, 1.944, 11.26, 2.389, -11.62]}, {"time": 2.8333, "y": -11.62, "curve": [3.278, 0, 3.722, 0, 3.278, -11.63, 3.722, 11.24]}, {"time": 4.1667, "y": 11.25, "curve": [4.611, 0, 5.056, 0, 4.611, 11.26, 5.056, -11.62]}, {"time": 5.5, "y": -11.62, "curve": [5.833, 0, 6.167, 0, 5.833, -11.63, 6.167, 11.25]}, {"time": 6.5, "y": 11.25, "curve": [6.944, 0, 7.389, 0, 6.944, 11.25, 7.389, -0.18]}, {"time": 7.8333, "y": -0.19, "curve": [8.278, 0, 8.722, 0, 8.278, -0.19, 8.722, 11.24]}, {"time": 9.1667, "y": 11.25, "curve": [9.611, 0, 10.056, 0, 9.611, 11.26, 10.056, -11.62]}, {"time": 10.5, "y": -11.62, "curve": [10.944, 0, 11.389, 0, 10.944, -11.63, 11.389, 11.24]}, {"time": 11.8333, "y": 11.25, "curve": [12.223, 0, 12.613, 0, 12.223, 11.25, 12.613, -6.21]}, {"time": 13, "y": -10.55}], "scale": [{"y": 1.041, "curve": [0.057, 1, 0.112, 1, 0.057, 1.044, 0.112, 1.045]}, {"time": 0.1667, "y": 1.045, "curve": [0.611, 1, 1.056, 1, 0.611, 1.045, 1.056, 0.966]}, {"time": 1.5, "y": 0.966, "curve": [1.944, 1, 2.389, 1, 1.944, 0.966, 2.389, 1.045]}, {"time": 2.8333, "y": 1.045, "curve": [3.278, 1, 3.722, 1, 3.278, 1.045, 3.722, 0.966]}, {"time": 4.1667, "y": 0.966, "curve": [4.611, 1, 5.056, 1, 4.611, 0.966, 5.056, 1.045]}, {"time": 5.5, "y": 1.045, "curve": [5.833, 1, 6.167, 1, 5.833, 1.045, 6.167, 0.966]}, {"time": 6.5, "y": 0.966, "curve": [6.944, 1, 7.389, 1, 6.944, 0.966, 7.389, 1.006]}, {"time": 7.8333, "y": 1.006, "curve": [8.278, 1, 8.722, 1, 8.278, 1.006, 8.722, 0.966]}, {"time": 9.1667, "y": 0.966, "curve": [9.611, 1, 10.056, 1, 9.611, 0.966, 10.056, 1.045]}, {"time": 10.5, "y": 1.045, "curve": [10.944, 1, 11.389, 1, 10.944, 1.045, 11.389, 0.966]}, {"time": 11.8333, "y": 0.966, "curve": [12.223, 1, 12.613, 1, 12.223, 0.966, 12.613, 1.026]}, {"time": 13, "y": 1.041}]}, "body2": {"rotate": [{"value": -0.81, "curve": [0.114, -1.03, 0.224, -1.19]}, {"time": 0.3333, "value": -1.19, "curve": [0.778, -1.19, 1.222, 1.14]}, {"time": 1.6667, "value": 1.14, "curve": [2.111, 1.14, 2.556, -1.19]}, {"time": 3, "value": -1.19, "curve": [3.444, -1.19, 3.889, 1.14]}, {"time": 4.3333, "value": 1.14, "curve": [4.778, 1.14, 5.222, -1.19]}, {"time": 5.6667, "value": -1.19, "curve": [6, -1.19, 6.333, -3.36]}, {"time": 6.6667, "value": -3.36, "curve": [7.111, -3.36, 7.556, -2.28]}, {"time": 8, "value": -2.28, "curve": [8.444, -2.28, 8.889, -3.36]}, {"time": 9.3333, "value": -3.36, "curve": [9.778, -3.36, 10.222, -1.19]}, {"time": 10.6667, "value": -1.19, "curve": [11.111, -1.19, 11.556, 1.14]}, {"time": 12, "value": 1.14, "curve": [12.335, 1.14, 12.67, -0.16]}, {"time": 13, "value": -0.81}], "translate": [{"x": -7.78, "curve": [0.114, -9.31, 0.224, -10.45, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -10.45, "curve": [0.778, -10.45, 1.222, 6.24, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.25, "curve": [2.111, 6.25, 2.556, -10.45, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -10.45, "curve": [3.444, -10.46, 3.889, 6.24, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.25, "curve": [4.778, 6.25, 5.222, -10.45, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -10.45, "curve": [6, -10.46, 6.333, 6.25, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.25, "curve": [7.111, 6.25, 7.556, -2.1, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -2.1, "curve": [8.444, -2.1, 8.889, 6.24, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.25, "curve": [9.778, 6.25, 10.222, -10.45, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -10.45, "curve": [11.111, -10.46, 11.556, 6.24, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.25, "curve": [12.335, 6.25, 12.67, -3.1, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -7.78}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.017, 0.778, 1, 1.222, 1.017]}, {"time": 1.6667, "x": 1.017, "y": 1.017, "curve": [2.111, 1.017, 2.556, 1, 2.111, 1.017, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.017, 3.444, 1, 3.889, 1.017]}, {"time": 4.3333, "x": 1.017, "y": 1.017, "curve": [4.778, 1.017, 5.222, 1, 4.778, 1.017, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.017, 6, 1, 6.333, 1.017]}, {"time": 6.6667, "x": 1.017, "y": 1.017, "curve": [7.111, 1.017, 7.556, 1.009, 7.111, 1.017, 7.556, 1.009]}, {"time": 8, "x": 1.009, "y": 1.009, "curve": [8.444, 1.009, 8.889, 1.017, 8.444, 1.009, 8.889, 1.017]}, {"time": 9.3333, "x": 1.017, "y": 1.017, "curve": [9.778, 1.017, 10.222, 1, 9.778, 1.017, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.017, 11.111, 1, 11.556, 1.017]}, {"time": 12, "x": 1.017, "y": 1.017, "curve": [12.335, 1.017, 12.67, 1.008, 12.335, 1.017, 12.67, 1.008]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"time": 4.5, "curve": [4.944, 0, 5.389, 0]}, {"time": 5.8333, "curve": [6.167, 0, 6.5, 5.47]}, {"time": 6.8333, "value": 5.47, "curve": [7.278, 5.47, 7.722, 4.55]}, {"time": 8.1667, "value": 4.55, "curve": [8.611, 4.55, 9.056, 5.47]}, {"time": 9.5, "value": 5.47, "curve": [9.944, 5.47, 10.389, 0]}, {"time": 10.8333}]}, "head": {"rotate": [{"time": 4.5, "curve": [4.944, 0, 5.389, 0]}, {"time": 5.8333, "curve": [6.167, 0, 6.5, 5.47]}, {"time": 6.8333, "value": 5.47, "curve": [7.278, 5.47, 7.722, 4.55]}, {"time": 8.1667, "value": 4.55, "curve": [8.611, 4.55, 9.056, 5.47]}, {"time": 9.5, "value": 5.47, "curve": [9.944, 5.47, 10.389, 0]}, {"time": 10.8333}]}, "tun": {"rotate": [{"value": 2.04, "curve": [0.444, 2.04, 0.889, -1.57]}, {"time": 1.3333, "value": -1.57, "curve": [1.778, -1.57, 2.222, 2.03]}, {"time": 2.6667, "value": 2.04, "curve": [3.111, 2.04, 3.556, -1.57]}, {"time": 4, "value": -1.57, "curve": [4.444, -1.57, 4.889, 2.03]}, {"time": 5.3333, "value": 2.04, "curve": [5.667, 2.04, 6, -1.57]}, {"time": 6.3333, "value": -1.57, "curve": [6.778, -1.57, 7.222, 0.23]}, {"time": 7.6667, "value": 0.23, "curve": [8.111, 0.23, 8.556, -1.57]}, {"time": 9, "value": -1.57, "curve": [9.444, -1.57, 9.889, 2.03]}, {"time": 10.3333, "value": 2.04, "curve": [10.778, 2.04, 11.222, -1.57]}, {"time": 11.6667, "value": -1.57, "curve": [12.111, -1.57, 12.556, 2.04]}, {"time": 13, "value": 2.04}]}, "leg_R2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.23]}, {"time": 7.6667, "value": -0.23, "curve": [8.111, -0.23, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.11]}, {"time": 7.6667, "value": 0.11, "curve": [8.111, 0.11, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.29]}, {"time": 7.6667, "value": -0.29, "curve": [8.111, -0.29, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 5.3333, "value": 0.01, "curve": [5.667, 0.01, 6, 0.01]}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, 0.1]}, {"time": 7.6667, "value": 0.1, "curve": [8.111, 0.1, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "foot_L": {"rotate": [{"value": 2.41, "curve": [0.444, 2.41, 0.889, -2]}, {"time": 1.3333, "value": -2, "curve": [1.778, -2, 2.222, 2.41]}, {"time": 2.6667, "value": 2.41, "curve": [3.111, 2.41, 3.556, -2]}, {"time": 4, "value": -2, "curve": [4.444, -2, 4.889, 2.41]}, {"time": 5.3333, "value": 2.41, "curve": [5.667, 2.41, 6, -2]}, {"time": 6.3333, "value": -2, "curve": [6.778, -2, 7.222, 2.41]}, {"time": 7.6667, "value": 2.41, "curve": [8.111, 2.41, 8.556, -2]}, {"time": 9, "value": -2, "curve": [9.444, -2, 9.889, 2.41]}, {"time": 10.3333, "value": 2.41, "curve": [10.778, 2.41, 11.222, -2]}, {"time": 11.6667, "value": -2, "curve": [12.111, -2, 12.556, 2.41]}, {"time": 13, "value": 2.41}]}, "foot_R": {"rotate": [{"value": -1.76, "curve": [0.444, -1.76, 0.889, 1.74]}, {"time": 1.3333, "value": 1.74, "curve": [1.778, 1.74, 2.222, -1.76]}, {"time": 2.6667, "value": -1.76, "curve": [3.111, -1.76, 3.556, 1.74]}, {"time": 4, "value": 1.74, "curve": [4.444, 1.74, 4.889, -1.76]}, {"time": 5.3333, "value": -1.76, "curve": [5.667, -1.76, 6, 1.74]}, {"time": 6.3333, "value": 1.74, "curve": [6.778, 1.74, 7.222, -0.01]}, {"time": 7.6667, "value": -0.01, "curve": [8.111, -0.01, 8.556, 1.74]}, {"time": 9, "value": 1.74, "curve": [9.444, 1.74, 9.889, -1.76]}, {"time": 10.3333, "value": -1.76, "curve": [10.778, -1.76, 11.222, 1.74]}, {"time": 11.6667, "value": 1.74, "curve": [12.111, 1.74, 12.556, -1.76]}, {"time": 13, "value": -1.76}]}, "sh_L": {"translate": [{"x": -5.01, "curve": [0.114, -6.44, 0.224, -7.51, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -7.51, "curve": [0.778, -7.51, 1.222, 8.09, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 8.09, "curve": [2.111, 8.09, 2.556, -7.5, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -7.51, "curve": [3.444, -7.51, 3.889, 8.09, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 8.09, "curve": [4.778, 8.09, 5.222, -7.5, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -7.51, "curve": [6, -7.51, 6.333, 8.09, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 8.09, "curve": [7.111, 8.09, 7.556, 0.29, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.29, "curve": [8.444, 0.29, 8.889, 8.09, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 8.09, "curve": [9.778, 8.09, 10.222, -7.5, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -7.51, "curve": [11.111, -7.51, 11.556, 8.09, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 8.09, "curve": [12.335, 8.09, 12.67, -0.64, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.01}]}, "sh_R": {"translate": [{"x": -5.01, "curve": [0.114, -6.44, 0.224, -7.51, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -7.51, "curve": [0.778, -7.51, 1.222, 8.09, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 8.09, "curve": [2.111, 8.09, 2.556, -7.5, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -7.51, "curve": [3.444, -7.51, 3.889, 8.09, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 8.09, "curve": [4.778, 8.09, 5.222, -7.5, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -7.51, "curve": [6, -7.51, 6.333, 8.09, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 8.09, "curve": [7.111, 8.09, 7.556, 0.29, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.29, "curve": [8.444, 0.29, 8.889, 8.09, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 8.09, "curve": [9.778, 8.09, 10.222, -7.5, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -7.51, "curve": [11.111, -7.51, 11.556, 8.09, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 8.09, "curve": [12.335, 8.09, 12.67, -0.64, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.01}]}, "arm_L": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.78]}, {"time": 7.6667, "value": 1.78, "curve": [8.111, 1.78, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 5.3333, "value": -0.01, "curve": [5.667, -0.01, 6, -0.01]}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, -2.93]}, {"time": 7.6667, "value": -2.93, "curve": [8.111, -2.93, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "hand_L2": {"rotate": [{"value": -3, "curve": [0.114, -4.06, 0.224, -4.85]}, {"time": 0.3333, "value": -4.85, "curve": [0.778, -4.85, 1.222, 6.73]}, {"time": 1.6667, "value": 6.73, "curve": [2.111, 6.73, 2.556, -4.85]}, {"time": 3, "value": -4.85, "curve": [3.444, -4.86, 3.889, 6.73]}, {"time": 4.3333, "value": 6.73, "curve": [4.778, 6.73, 5.222, -4.85]}, {"time": 5.6667, "value": -4.85, "curve": [6, -4.86, 6.333, 6.73]}, {"time": 6.6667, "value": 6.73, "curve": [7.111, 6.73, 7.556, 0.94]}, {"time": 8, "value": 0.94, "curve": [8.444, 0.94, 8.889, 6.73]}, {"time": 9.3333, "value": 6.73, "curve": [9.778, 6.73, 10.222, -4.85]}, {"time": 10.6667, "value": -4.85, "curve": [11.111, -4.86, 11.556, 6.73]}, {"time": 12, "value": 6.73, "curve": [12.335, 6.73, 12.67, 0.24]}, {"time": 13, "value": -3}]}, "arm_R": {"rotate": [{"value": 0.56, "curve": [0.168, 1.16, 0.334, 1.65]}, {"time": 0.5, "value": 1.65, "curve": [0.944, 1.65, 1.389, -1.75]}, {"time": 1.8333, "value": -1.75, "curve": [2.278, -1.75, 2.722, 1.64]}, {"time": 3.1667, "value": 1.65, "curve": [3.611, 1.65, 4.056, -1.75]}, {"time": 4.5, "value": -1.75, "curve": [4.944, -1.75, 5.389, 1.64]}, {"time": 5.8333, "value": 1.65, "curve": [6.167, 1.65, 6.5, -27.37]}, {"time": 6.8333, "value": -27.37, "curve": [7.278, -27.37, 7.722, -26.08]}, {"time": 8.1667, "value": -26.08, "curve": [8.611, -26.08, 9.056, -27.36]}, {"time": 9.5, "value": -27.37, "curve": [9.944, -27.37, 10.389, 1.64]}, {"time": 10.8333, "value": 1.65, "curve": [11.278, 1.65, 11.722, -1.75]}, {"time": 12.1667, "value": -1.75, "curve": [12.445, -1.75, 12.724, -0.43]}, {"time": 13, "value": 0.56}]}, "arm_R2": {"rotate": [{"value": -0.05, "curve": [0.225, 0.79, 0.446, 1.65]}, {"time": 0.6667, "value": 1.65, "curve": [1.111, 1.65, 1.556, -1.75]}, {"time": 2, "value": -1.75, "curve": [2.444, -1.75, 2.889, 1.64]}, {"time": 3.3333, "value": 1.65, "curve": [3.778, 1.65, 4.222, -1.75]}, {"time": 4.6667, "value": -1.75, "curve": [5.111, -1.75, 5.556, 1.64]}, {"time": 6, "value": 1.65, "curve": [6.333, 1.65, 6.667, -3.23]}, {"time": 7, "value": -3.23, "curve": [7.444, -3.23, 7.889, 1.35]}, {"time": 8.3333, "value": 1.35, "curve": [8.778, 1.35, 9.222, -3.23]}, {"time": 9.6667, "value": -3.23, "curve": [10.111, -3.23, 10.556, 1.64]}, {"time": 11, "value": 1.65, "curve": [11.444, 1.65, 11.889, -1.75]}, {"time": 12.3333, "value": -1.75, "curve": [12.557, -1.75, 12.781, -0.91]}, {"time": 13, "value": -0.05}]}, "arm_R3": {"rotate": [{"value": -0.67, "curve": [0.279, 0.32, 0.556, 1.65]}, {"time": 0.8333, "value": 1.65, "curve": [1.278, 1.65, 1.722, -1.75]}, {"time": 2.1667, "value": -1.75, "curve": [2.611, -1.75, 3.056, 1.64]}, {"time": 3.5, "value": 1.65, "curve": [3.944, 1.65, 4.389, -1.75]}, {"time": 4.8333, "value": -1.75, "curve": [5.278, -1.75, 5.722, 1.64]}, {"time": 6.1667, "value": 1.65, "curve": [6.5, 1.65, 6.833, -3.23]}, {"time": 7.1667, "value": -3.23, "curve": [7.611, -3.23, 8.056, 1.35]}, {"time": 8.5, "value": 1.35, "curve": [8.944, 1.35, 9.389, -3.23]}, {"time": 9.8333, "value": -3.23, "curve": [10.278, -3.23, 10.722, 1.64]}, {"time": 11.1667, "value": 1.65, "curve": [11.611, 1.65, 12.056, -1.75]}, {"time": 12.5, "value": -1.75, "curve": [12.667, -1.75, 12.835, -1.27]}, {"time": 13, "value": -0.67}]}, "RU_L": {"translate": [{"x": -16.96, "curve": [0.168, -32.6, 0.334, -45.44, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -45.44, "curve": [0.944, -45.44, 1.389, 44.06, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 44.08, "curve": [2.278, 44.11, 2.722, -45.41, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -45.44, "curve": [3.611, -45.46, 4.056, 44.06, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 44.08, "curve": [4.944, 44.11, 5.389, -45.41, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -45.44, "curve": [6.167, -45.46, 6.5, 44.08, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 44.08, "curve": [7.278, 44.1, 7.722, -0.66, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -0.68, "curve": [8.611, -0.69, 9.056, 44.06, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 44.08, "curve": [9.944, 44.11, 10.389, -45.41, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -45.44, "curve": [11.278, -45.46, 11.722, 44.06, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 44.08, "curve": [12.445, 44.1, 12.724, 9.29, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -16.96}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.167, 1, 0.333, 0.896, 0.167, 0.998, 0.333, 1.087]}, {"time": 0.5, "x": 0.896, "y": 1.087, "curve": [0.722, 0.896, 0.944, 1.083, 0.722, 1.087, 0.944, 0.928]}, {"time": 1.1667, "x": 1.083, "y": 0.928, "curve": [1.389, 1.083, 1.611, 0.896, 1.389, 0.928, 1.611, 1.087]}, {"time": 1.8333, "x": 0.896, "y": 1.087, "curve": [2.056, 0.896, 2.278, 1.083, 2.056, 1.087, 2.278, 0.928]}, {"time": 2.5, "x": 1.083, "y": 0.928, "curve": [2.722, 1.083, 2.944, 0.896, 2.722, 0.928, 2.944, 1.087]}, {"time": 3.1667, "x": 0.896, "y": 1.087, "curve": [3.389, 0.896, 3.611, 1.083, 3.389, 1.087, 3.611, 0.928]}, {"time": 3.8333, "x": 1.083, "y": 0.928, "curve": [4.056, 1.083, 4.278, 0.896, 4.056, 0.928, 4.278, 1.087]}, {"time": 4.5, "x": 0.896, "y": 1.087, "curve": [4.722, 0.896, 4.944, 1.083, 4.722, 1.087, 4.944, 0.928]}, {"time": 5.1667, "x": 1.083, "y": 0.928, "curve": [5.389, 1.083, 5.611, 0.896, 5.389, 0.928, 5.611, 1.087]}, {"time": 5.8333, "x": 0.896, "y": 1.087, "curve": [6, 0.896, 6.167, 1.083, 6, 1.087, 6.167, 0.928]}, {"time": 6.3333, "x": 1.083, "y": 0.928, "curve": [6.5, 1.083, 6.667, 0.896, 6.5, 0.928, 6.667, 1.087]}, {"time": 6.8333, "x": 0.896, "y": 1.087, "curve": [7.056, 0.896, 7.278, 1.083, 7.056, 1.087, 7.278, 0.928]}, {"time": 7.5, "x": 1.083, "y": 0.928, "curve": [7.722, 1.083, 7.944, 0.896, 7.722, 0.928, 7.944, 1.087]}, {"time": 8.1667, "x": 0.896, "y": 1.087, "curve": [8.389, 0.896, 8.611, 1.083, 8.389, 1.087, 8.611, 0.928]}, {"time": 8.8333, "x": 1.083, "y": 0.928, "curve": [9.056, 1.083, 9.278, 0.896, 9.056, 0.928, 9.278, 1.087]}, {"time": 9.5, "x": 0.896, "y": 1.087, "curve": [9.722, 0.896, 9.944, 1.083, 9.722, 1.087, 9.944, 0.928]}, {"time": 10.1667, "x": 1.083, "y": 0.928, "curve": [10.389, 1.083, 10.611, 0.896, 10.389, 0.928, 10.611, 1.087]}, {"time": 10.8333, "x": 0.896, "y": 1.087, "curve": [11.056, 0.896, 11.278, 1.083, 11.056, 1.087, 11.278, 0.928]}, {"time": 11.5, "x": 1.083, "y": 0.928, "curve": [11.722, 1.083, 11.944, 0.896, 11.722, 0.928, 11.944, 1.087]}, {"time": 12.1667, "x": 0.896, "y": 1.087, "curve": [12.389, 0.896, 12.611, 1.083, 12.389, 1.087, 12.611, 0.928]}, {"time": 12.8333, "x": 1.083, "y": 0.928, "curve": [12.889, 1.083, 12.944, 1.071, 12.889, 0.928, 12.944, 0.939]}, {"time": 13, "x": 1.053, "y": 0.954}]}, "RU_L2": {"translate": [{"x": 0.49, "curve": [0.225, -14.86, 0.446, -30.41, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.41, "curve": [1.111, -30.41, 1.556, 31.36, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 31.37, "curve": [2.444, 31.39, 2.889, -30.4, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.41, "curve": [3.778, -30.43, 4.222, 31.36, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 31.37, "curve": [5.111, 31.39, 5.556, -30.39, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.41, "curve": [6.333, -30.43, 6.667, 31.37, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 31.37, "curve": [7.444, 31.38, 7.889, 0.49, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": 0.48, "curve": [8.778, 0.47, 9.222, 31.36, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 31.37, "curve": [10.111, 31.39, 10.556, -30.4, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.41, "curve": [11.444, -30.43, 11.889, 31.36, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 31.37, "curve": [12.557, 31.38, 12.781, 16.04, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 0.49}], "scale": [{"x": 1.083, "y": 0.928, "curve": [0.222, 1.083, 0.444, 0.896, 0.222, 0.928, 0.444, 1.087]}, {"time": 0.6667, "x": 0.896, "y": 1.087, "curve": [0.889, 0.896, 1.111, 1.083, 0.889, 1.087, 1.111, 0.928]}, {"time": 1.3333, "x": 1.083, "y": 0.928, "curve": [1.556, 1.083, 1.778, 0.896, 1.556, 0.928, 1.778, 1.087]}, {"time": 2, "x": 0.896, "y": 1.087, "curve": [2.222, 0.896, 2.444, 1.083, 2.222, 1.087, 2.444, 0.928]}, {"time": 2.6667, "x": 1.083, "y": 0.928, "curve": [2.889, 1.083, 3.111, 0.896, 2.889, 0.928, 3.111, 1.087]}, {"time": 3.3333, "x": 0.896, "y": 1.087, "curve": [3.556, 0.896, 3.778, 1.083, 3.556, 1.087, 3.778, 0.928]}, {"time": 4, "x": 1.083, "y": 0.928, "curve": [4.222, 1.083, 4.444, 0.896, 4.222, 0.928, 4.444, 1.087]}, {"time": 4.6667, "x": 0.896, "y": 1.087, "curve": [4.889, 0.896, 5.111, 1.083, 4.889, 1.087, 5.111, 0.928]}, {"time": 5.3333, "x": 1.083, "y": 0.928, "curve": [5.556, 1.083, 5.778, 0.896, 5.556, 0.928, 5.778, 1.087]}, {"time": 6, "x": 0.896, "y": 1.087, "curve": [6.167, 0.896, 6.333, 1.083, 6.167, 1.087, 6.333, 0.928]}, {"time": 6.5, "x": 1.083, "y": 0.928, "curve": [6.667, 1.083, 6.833, 0.896, 6.667, 0.928, 6.833, 1.087]}, {"time": 7, "x": 0.896, "y": 1.087, "curve": [7.222, 0.896, 7.444, 1.083, 7.222, 1.087, 7.444, 0.928]}, {"time": 7.6667, "x": 1.083, "y": 0.928, "curve": [7.889, 1.083, 8.111, 0.896, 7.889, 0.928, 8.111, 1.087]}, {"time": 8.3333, "x": 0.896, "y": 1.087, "curve": [8.556, 0.896, 8.778, 1.083, 8.556, 1.087, 8.778, 0.928]}, {"time": 9, "x": 1.083, "y": 0.928, "curve": [9.222, 1.083, 9.444, 0.896, 9.222, 0.928, 9.444, 1.087]}, {"time": 9.6667, "x": 0.896, "y": 1.087, "curve": [9.889, 0.896, 10.111, 1.083, 9.889, 1.087, 10.111, 0.928]}, {"time": 10.3333, "x": 1.083, "y": 0.928, "curve": [10.556, 1.083, 10.778, 0.896, 10.556, 0.928, 10.778, 1.087]}, {"time": 11, "x": 0.896, "y": 1.087, "curve": [11.222, 0.896, 11.444, 1.083, 11.222, 1.087, 11.444, 0.928]}, {"time": 11.6667, "x": 1.083, "y": 0.928, "curve": [11.889, 1.083, 12.111, 0.896, 11.889, 0.928, 12.111, 1.087]}, {"time": 12.3333, "x": 0.896, "y": 1.087, "curve": [12.556, 0.896, 12.778, 1.083, 12.556, 1.087, 12.778, 0.928]}, {"time": 13, "x": 1.083, "y": 0.928}]}, "RU_L3": {"translate": [{"x": 10.43, "curve": [0.279, -6.37, 0.556, -28.72, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -28.72, "curve": [1.278, -28.72, 1.722, 28.67, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 28.68, "curve": [2.611, 28.69, 3.056, -28.7, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -28.72, "curve": [3.944, -28.73, 4.389, 28.67, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 28.68, "curve": [5.278, 28.69, 5.722, -28.7, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -28.72, "curve": [6.5, -28.73, 6.833, 28.67, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 28.68, "curve": [7.611, 28.69, 8.056, -0.01, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -0.02, "curve": [8.944, -0.03, 9.389, 28.67, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 28.68, "curve": [10.278, 28.69, 10.722, -28.7, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -28.72, "curve": [11.611, -28.73, 12.056, 28.67, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 28.68, "curve": [12.667, 28.69, 12.835, 20.59, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.43}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.056, 1.071, 0.111, 1.083, 0.056, 0.939, 0.111, 0.928]}, {"time": 0.1667, "x": 1.083, "y": 0.928, "curve": [0.389, 1.083, 0.611, 0.896, 0.389, 0.928, 0.611, 1.087]}, {"time": 0.8333, "x": 0.896, "y": 1.087, "curve": [1.056, 0.896, 1.278, 1.083, 1.056, 1.087, 1.278, 0.928]}, {"time": 1.5, "x": 1.083, "y": 0.928, "curve": [1.722, 1.083, 1.944, 0.896, 1.722, 0.928, 1.944, 1.087]}, {"time": 2.1667, "x": 0.896, "y": 1.087, "curve": [2.389, 0.896, 2.611, 1.083, 2.389, 1.087, 2.611, 0.928]}, {"time": 2.8333, "x": 1.083, "y": 0.928, "curve": [3.056, 1.083, 3.278, 0.896, 3.056, 0.928, 3.278, 1.087]}, {"time": 3.5, "x": 0.896, "y": 1.087, "curve": [3.722, 0.896, 3.944, 1.083, 3.722, 1.087, 3.944, 0.928]}, {"time": 4.1667, "x": 1.083, "y": 0.928, "curve": [4.389, 1.083, 4.611, 0.896, 4.389, 0.928, 4.611, 1.087]}, {"time": 4.8333, "x": 0.896, "y": 1.087, "curve": [5.056, 0.896, 5.278, 1.083, 5.056, 1.087, 5.278, 0.928]}, {"time": 5.5, "x": 1.083, "y": 0.928, "curve": [5.722, 1.083, 5.944, 0.896, 5.722, 0.928, 5.944, 1.087]}, {"time": 6.1667, "x": 0.896, "y": 1.087, "curve": [6.333, 0.896, 6.5, 1.083, 6.333, 1.087, 6.5, 0.928]}, {"time": 6.6667, "x": 1.083, "y": 0.928, "curve": [6.833, 1.083, 7, 0.896, 6.833, 0.928, 7, 1.087]}, {"time": 7.1667, "x": 0.896, "y": 1.087, "curve": [7.389, 0.896, 7.611, 1.083, 7.389, 1.087, 7.611, 0.928]}, {"time": 7.8333, "x": 1.083, "y": 0.928, "curve": [8.056, 1.083, 8.278, 0.896, 8.056, 0.928, 8.278, 1.087]}, {"time": 8.5, "x": 0.896, "y": 1.087, "curve": [8.722, 0.896, 8.944, 1.083, 8.722, 1.087, 8.944, 0.928]}, {"time": 9.1667, "x": 1.083, "y": 0.928, "curve": [9.389, 1.083, 9.611, 0.896, 9.389, 0.928, 9.611, 1.087]}, {"time": 9.8333, "x": 0.896, "y": 1.087, "curve": [10.056, 0.896, 10.278, 1.083, 10.056, 1.087, 10.278, 0.928]}, {"time": 10.5, "x": 1.083, "y": 0.928, "curve": [10.722, 1.083, 10.944, 0.896, 10.722, 0.928, 10.944, 1.087]}, {"time": 11.1667, "x": 0.896, "y": 1.087, "curve": [11.389, 0.896, 11.611, 1.083, 11.389, 1.087, 11.611, 0.928]}, {"time": 11.8333, "x": 1.083, "y": 0.928, "curve": [12.056, 1.083, 12.278, 0.896, 12.056, 0.928, 12.278, 1.087]}, {"time": 12.5, "x": 0.896, "y": 1.087, "curve": [12.667, 0.896, 12.833, 1, 12.667, 1.087, 12.833, 0.998]}, {"time": 13, "x": 1.053, "y": 0.954}]}, "RU_R": {"translate": [{"x": -16.96, "curve": [0.168, -32.6, 0.334, -45.44, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -45.44, "curve": [0.944, -45.44, 1.389, 44.06, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 44.08, "curve": [2.278, 44.11, 2.722, -45.41, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -45.44, "curve": [3.611, -45.46, 4.056, 44.06, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 44.08, "curve": [4.944, 44.11, 5.389, -45.41, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -45.44, "curve": [6.167, -45.46, 6.5, 44.08, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 44.08, "curve": [7.278, 44.1, 7.722, -0.66, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -0.68, "curve": [8.611, -0.69, 9.056, 44.06, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 44.08, "curve": [9.944, 44.11, 10.389, -45.41, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -45.44, "curve": [11.278, -45.46, 11.722, 44.06, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 44.08, "curve": [12.445, 44.1, 12.724, 9.29, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -16.96}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.167, 1, 0.333, 0.896, 0.167, 0.998, 0.333, 1.087]}, {"time": 0.5, "x": 0.896, "y": 1.087, "curve": [0.722, 0.896, 0.944, 1.083, 0.722, 1.087, 0.944, 0.928]}, {"time": 1.1667, "x": 1.083, "y": 0.928, "curve": [1.389, 1.083, 1.611, 0.896, 1.389, 0.928, 1.611, 1.087]}, {"time": 1.8333, "x": 0.896, "y": 1.087, "curve": [2.056, 0.896, 2.278, 1.083, 2.056, 1.087, 2.278, 0.928]}, {"time": 2.5, "x": 1.083, "y": 0.928, "curve": [2.722, 1.083, 2.944, 0.896, 2.722, 0.928, 2.944, 1.087]}, {"time": 3.1667, "x": 0.896, "y": 1.087, "curve": [3.389, 0.896, 3.611, 1.083, 3.389, 1.087, 3.611, 0.928]}, {"time": 3.8333, "x": 1.083, "y": 0.928, "curve": [4.056, 1.083, 4.278, 0.896, 4.056, 0.928, 4.278, 1.087]}, {"time": 4.5, "x": 0.896, "y": 1.087, "curve": [4.722, 0.896, 4.944, 1.083, 4.722, 1.087, 4.944, 0.928]}, {"time": 5.1667, "x": 1.083, "y": 0.928, "curve": [5.389, 1.083, 5.611, 0.896, 5.389, 0.928, 5.611, 1.087]}, {"time": 5.8333, "x": 0.896, "y": 1.087, "curve": [6, 0.896, 6.167, 1.083, 6, 1.087, 6.167, 0.928]}, {"time": 6.3333, "x": 1.083, "y": 0.928, "curve": [6.5, 1.083, 6.667, 0.896, 6.5, 0.928, 6.667, 1.087]}, {"time": 6.8333, "x": 0.896, "y": 1.087, "curve": [7.056, 0.896, 7.278, 1.083, 7.056, 1.087, 7.278, 0.928]}, {"time": 7.5, "x": 1.083, "y": 0.928, "curve": [7.722, 1.083, 7.944, 0.896, 7.722, 0.928, 7.944, 1.087]}, {"time": 8.1667, "x": 0.896, "y": 1.087, "curve": [8.389, 0.896, 8.611, 1.083, 8.389, 1.087, 8.611, 0.928]}, {"time": 8.8333, "x": 1.083, "y": 0.928, "curve": [9.056, 1.083, 9.278, 0.896, 9.056, 0.928, 9.278, 1.087]}, {"time": 9.5, "x": 0.896, "y": 1.087, "curve": [9.722, 0.896, 9.944, 1.083, 9.722, 1.087, 9.944, 0.928]}, {"time": 10.1667, "x": 1.083, "y": 0.928, "curve": [10.389, 1.083, 10.611, 0.896, 10.389, 0.928, 10.611, 1.087]}, {"time": 10.8333, "x": 0.896, "y": 1.087, "curve": [11.056, 0.896, 11.278, 1.083, 11.056, 1.087, 11.278, 0.928]}, {"time": 11.5, "x": 1.083, "y": 0.928, "curve": [11.722, 1.083, 11.944, 0.896, 11.722, 0.928, 11.944, 1.087]}, {"time": 12.1667, "x": 0.896, "y": 1.087, "curve": [12.389, 0.896, 12.611, 1.083, 12.389, 1.087, 12.611, 0.928]}, {"time": 12.8333, "x": 1.083, "y": 0.928, "curve": [12.889, 1.083, 12.944, 1.071, 12.889, 0.928, 12.944, 0.939]}, {"time": 13, "x": 1.053, "y": 0.954}]}, "RU_R2": {"translate": [{"x": 0.49, "curve": [0.225, -14.86, 0.446, -30.41, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.41, "curve": [1.111, -30.41, 1.556, 31.36, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 31.37, "curve": [2.444, 31.39, 2.889, -30.4, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.41, "curve": [3.778, -30.43, 4.222, 31.36, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 31.37, "curve": [5.111, 31.39, 5.556, -30.39, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.41, "curve": [6.333, -30.43, 6.667, 31.37, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 31.37, "curve": [7.444, 31.38, 7.889, 0.49, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": 0.48, "curve": [8.778, 0.47, 9.222, 31.36, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 31.37, "curve": [10.111, 31.39, 10.556, -30.4, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.41, "curve": [11.444, -30.43, 11.889, 31.36, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 31.37, "curve": [12.557, 31.38, 12.781, 16.04, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 0.49}], "scale": [{"x": 1.083, "y": 0.928, "curve": [0.222, 1.083, 0.444, 0.896, 0.222, 0.928, 0.444, 1.087]}, {"time": 0.6667, "x": 0.896, "y": 1.087, "curve": [0.889, 0.896, 1.111, 1.083, 0.889, 1.087, 1.111, 0.928]}, {"time": 1.3333, "x": 1.083, "y": 0.928, "curve": [1.556, 1.083, 1.778, 0.896, 1.556, 0.928, 1.778, 1.087]}, {"time": 2, "x": 0.896, "y": 1.087, "curve": [2.222, 0.896, 2.444, 1.083, 2.222, 1.087, 2.444, 0.928]}, {"time": 2.6667, "x": 1.083, "y": 0.928, "curve": [2.889, 1.083, 3.111, 0.896, 2.889, 0.928, 3.111, 1.087]}, {"time": 3.3333, "x": 0.896, "y": 1.087, "curve": [3.556, 0.896, 3.778, 1.083, 3.556, 1.087, 3.778, 0.928]}, {"time": 4, "x": 1.083, "y": 0.928, "curve": [4.222, 1.083, 4.444, 0.896, 4.222, 0.928, 4.444, 1.087]}, {"time": 4.6667, "x": 0.896, "y": 1.087, "curve": [4.889, 0.896, 5.111, 1.083, 4.889, 1.087, 5.111, 0.928]}, {"time": 5.3333, "x": 1.083, "y": 0.928, "curve": [5.556, 1.083, 5.778, 0.896, 5.556, 0.928, 5.778, 1.087]}, {"time": 6, "x": 0.896, "y": 1.087, "curve": [6.167, 0.896, 6.333, 1.083, 6.167, 1.087, 6.333, 0.928]}, {"time": 6.5, "x": 1.083, "y": 0.928, "curve": [6.667, 1.083, 6.833, 0.896, 6.667, 0.928, 6.833, 1.087]}, {"time": 7, "x": 0.896, "y": 1.087, "curve": [7.222, 0.896, 7.444, 1.083, 7.222, 1.087, 7.444, 0.928]}, {"time": 7.6667, "x": 1.083, "y": 0.928, "curve": [7.889, 1.083, 8.111, 0.896, 7.889, 0.928, 8.111, 1.087]}, {"time": 8.3333, "x": 0.896, "y": 1.087, "curve": [8.556, 0.896, 8.778, 1.083, 8.556, 1.087, 8.778, 0.928]}, {"time": 9, "x": 1.083, "y": 0.928, "curve": [9.222, 1.083, 9.444, 0.896, 9.222, 0.928, 9.444, 1.087]}, {"time": 9.6667, "x": 0.896, "y": 1.087, "curve": [9.889, 0.896, 10.111, 1.083, 9.889, 1.087, 10.111, 0.928]}, {"time": 10.3333, "x": 1.083, "y": 0.928, "curve": [10.556, 1.083, 10.778, 0.896, 10.556, 0.928, 10.778, 1.087]}, {"time": 11, "x": 0.896, "y": 1.087, "curve": [11.222, 0.896, 11.444, 1.083, 11.222, 1.087, 11.444, 0.928]}, {"time": 11.6667, "x": 1.083, "y": 0.928, "curve": [11.889, 1.083, 12.111, 0.896, 11.889, 0.928, 12.111, 1.087]}, {"time": 12.3333, "x": 0.896, "y": 1.087, "curve": [12.556, 0.896, 12.778, 1.083, 12.556, 1.087, 12.778, 0.928]}, {"time": 13, "x": 1.083, "y": 0.928}]}, "RU_R3": {"translate": [{"x": 10.43, "curve": [0.279, -6.37, 0.556, -28.72, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -28.72, "curve": [1.278, -28.72, 1.722, 28.67, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 28.68, "curve": [2.611, 28.69, 3.056, -28.7, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -28.72, "curve": [3.944, -28.73, 4.389, 28.67, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 28.68, "curve": [5.278, 28.69, 5.722, -28.7, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -28.72, "curve": [6.5, -28.73, 6.833, 28.67, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 28.68, "curve": [7.611, 28.69, 8.056, -0.01, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -0.02, "curve": [8.944, -0.03, 9.389, 28.67, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 28.68, "curve": [10.278, 28.69, 10.722, -28.7, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -28.72, "curve": [11.611, -28.73, 12.056, 28.67, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 28.68, "curve": [12.667, 28.69, 12.835, 20.59, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.43}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.056, 1.071, 0.111, 1.083, 0.056, 0.939, 0.111, 0.928]}, {"time": 0.1667, "x": 1.083, "y": 0.928, "curve": [0.389, 1.083, 0.611, 0.896, 0.389, 0.928, 0.611, 1.087]}, {"time": 0.8333, "x": 0.896, "y": 1.087, "curve": [1.056, 0.896, 1.278, 1.083, 1.056, 1.087, 1.278, 0.928]}, {"time": 1.5, "x": 1.083, "y": 0.928, "curve": [1.722, 1.083, 1.944, 0.896, 1.722, 0.928, 1.944, 1.087]}, {"time": 2.1667, "x": 0.896, "y": 1.087, "curve": [2.389, 0.896, 2.611, 1.083, 2.389, 1.087, 2.611, 0.928]}, {"time": 2.8333, "x": 1.083, "y": 0.928, "curve": [3.056, 1.083, 3.278, 0.896, 3.056, 0.928, 3.278, 1.087]}, {"time": 3.5, "x": 0.896, "y": 1.087, "curve": [3.722, 0.896, 3.944, 1.083, 3.722, 1.087, 3.944, 0.928]}, {"time": 4.1667, "x": 1.083, "y": 0.928, "curve": [4.389, 1.083, 4.611, 0.896, 4.389, 0.928, 4.611, 1.087]}, {"time": 4.8333, "x": 0.896, "y": 1.087, "curve": [5.056, 0.896, 5.278, 1.083, 5.056, 1.087, 5.278, 0.928]}, {"time": 5.5, "x": 1.083, "y": 0.928, "curve": [5.722, 1.083, 5.944, 0.896, 5.722, 0.928, 5.944, 1.087]}, {"time": 6.1667, "x": 0.896, "y": 1.087, "curve": [6.333, 0.896, 6.5, 1.083, 6.333, 1.087, 6.5, 0.928]}, {"time": 6.6667, "x": 1.083, "y": 0.928, "curve": [6.833, 1.083, 7, 0.896, 6.833, 0.928, 7, 1.087]}, {"time": 7.1667, "x": 0.896, "y": 1.087, "curve": [7.389, 0.896, 7.611, 1.083, 7.389, 1.087, 7.611, 0.928]}, {"time": 7.8333, "x": 1.083, "y": 0.928, "curve": [8.056, 1.083, 8.278, 0.896, 8.056, 0.928, 8.278, 1.087]}, {"time": 8.5, "x": 0.896, "y": 1.087, "curve": [8.722, 0.896, 8.944, 1.083, 8.722, 1.087, 8.944, 0.928]}, {"time": 9.1667, "x": 1.083, "y": 0.928, "curve": [9.389, 1.083, 9.611, 0.896, 9.389, 0.928, 9.611, 1.087]}, {"time": 9.8333, "x": 0.896, "y": 1.087, "curve": [10.056, 0.896, 10.278, 1.083, 10.056, 1.087, 10.278, 0.928]}, {"time": 10.5, "x": 1.083, "y": 0.928, "curve": [10.722, 1.083, 10.944, 0.896, 10.722, 0.928, 10.944, 1.087]}, {"time": 11.1667, "x": 0.896, "y": 1.087, "curve": [11.389, 0.896, 11.611, 1.083, 11.389, 1.087, 11.611, 0.928]}, {"time": 11.8333, "x": 1.083, "y": 0.928, "curve": [12.056, 1.083, 12.278, 0.896, 12.056, 0.928, 12.278, 1.087]}, {"time": 12.5, "x": 0.896, "y": 1.087, "curve": [12.667, 0.896, 12.833, 1, 12.667, 1.087, 12.833, 0.998]}, {"time": 13, "x": 1.053, "y": 0.954}]}, "cloth_L": {"rotate": [{"value": -1.04, "curve": [0.168, -3.17, 0.334, -4.92]}, {"time": 0.5, "value": -4.92, "curve": [0.944, -4.92, 1.389, 7.29]}, {"time": 1.8333, "value": 7.29, "curve": [2.278, 7.3, 2.722, -4.92]}, {"time": 3.1667, "value": -4.92, "curve": [3.611, -4.93, 4.056, 7.29]}, {"time": 4.5, "value": 7.29, "curve": [4.944, 7.3, 5.389, -4.92]}, {"time": 5.8333, "value": -4.92, "curve": [6.167, -4.93, 6.5, 7.29]}, {"time": 6.8333, "value": 7.29, "curve": [7.278, 7.29, 7.722, 1.19]}, {"time": 8.1667, "value": 1.18, "curve": [8.611, 1.18, 9.056, 7.29]}, {"time": 9.5, "value": 7.29, "curve": [9.944, 7.3, 10.389, -4.92]}, {"time": 10.8333, "value": -4.92, "curve": [11.278, -4.93, 11.722, 7.29]}, {"time": 12.1667, "value": 7.29, "curve": [12.445, 7.29, 12.724, 2.55]}, {"time": 13, "value": -1.04}]}, "cloth_R": {"rotate": [{"value": 3.41, "curve": [0.168, 5.54, 0.334, 7.29]}, {"time": 0.5, "value": 7.29, "curve": [0.944, 7.29, 1.389, -4.92]}, {"time": 1.8333, "value": -4.92, "curve": [2.278, -4.93, 2.722, 7.29]}, {"time": 3.1667, "value": 7.29, "curve": [3.611, 7.3, 4.056, -4.92]}, {"time": 4.5, "value": -4.92, "curve": [4.944, -4.93, 5.389, 7.29]}, {"time": 5.8333, "value": 7.29, "curve": [6.167, 7.3, 6.5, -4.92]}, {"time": 6.8333, "value": -4.92, "curve": [7.278, -4.92, 7.722, 1.18]}, {"time": 8.1667, "value": 1.18, "curve": [8.611, 1.19, 9.056, -4.92]}, {"time": 9.5, "value": -4.92, "curve": [9.944, -4.93, 10.389, 7.29]}, {"time": 10.8333, "value": 7.29, "curve": [11.278, 7.3, 11.722, -4.92]}, {"time": 12.1667, "value": -4.92, "curve": [12.445, -4.93, 12.724, -0.18]}, {"time": 13, "value": 3.41}]}, "eyebrow_L": {"translate": [{"curve": [0.444, 0, 5.889, 0, 0.444, 0, 5.889, 0]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, -1.59, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": -1.59, "curve": "stepped"}, {"time": 9.6667, "x": -1.59, "curve": [9.889, -1.59, 10.111, 0, 10.355, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_L2": {"rotate": [{"curve": [0.444, 0, 5.889, 0]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, 2.96]}, {"time": 6.5, "value": 2.96, "curve": "stepped"}, {"time": 9.6667, "value": 2.96, "curve": [9.889, 2.96, 10.111, 0]}, {"time": 10.3333}], "scale": [{"curve": [0.444, 1, 5.889, 1, 0.444, 1, 5.889, 1]}, {"time": 6.1667, "curve": [6.278, 1, 6.389, 1.02, 6.278, 1, 6.389, 1]}, {"time": 6.5, "x": 1.02, "curve": "stepped"}, {"time": 9.6667, "x": 1.02, "curve": [9.889, 1.02, 10.111, 1, 10.355, 1, 10.111, 1]}, {"time": 10.3333}]}, "eyebrow_L3": {"rotate": [{"curve": [0.444, 0, 5.889, 0]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, -6.48]}, {"time": 6.5, "value": -6.48, "curve": "stepped"}, {"time": 9.6667, "value": -6.48, "curve": [9.889, -6.48, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R": {"translate": [{"curve": [0.444, 0, 5.889, 0, 0.444, 0, 5.889, 0]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, -1.59, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": -1.59, "curve": "stepped"}, {"time": 9.6667, "x": -1.59, "curve": [9.889, -1.59, 10.111, 0, 10.355, 0, 10.111, 0]}, {"time": 10.3333}]}, "eyebrow_R2": {"rotate": [{"curve": [0.444, 0, 5.889, 0]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, -3.18]}, {"time": 6.5, "value": -3.18, "curve": "stepped"}, {"time": 9.6667, "value": -3.18, "curve": [9.889, -3.18, 10.111, 0]}, {"time": 10.3333}], "scale": [{"curve": [0.444, 1, 5.889, 1, 0.444, 1, 5.889, 1]}, {"time": 6.1667, "curve": [6.278, 1, 6.389, 1.02, 6.278, 1, 6.389, 1]}, {"time": 6.5, "x": 1.02, "curve": "stepped"}, {"time": 9.6667, "x": 1.02, "curve": [9.889, 1.02, 10.111, 1, 10.355, 1, 10.111, 1]}, {"time": 10.3333}]}, "eyebrow_R3": {"rotate": [{"curve": [0.444, 0, 5.889, 0.01]}, {"time": 6.1667, "curve": [6.278, 0, 6.389, 9.57]}, {"time": 6.5, "value": 9.57, "curve": "stepped"}, {"time": 9.6667, "value": 9.57, "curve": [9.889, 9.57, 10.111, 0]}, {"time": 10.3333}]}, "hair_L2": {"rotate": [{"value": -1.09, "curve": [0.225, -3.7, 0.446, -6.35]}, {"time": 0.6667, "value": -6.35, "curve": [1.111, -6.35, 1.556, 4.17]}, {"time": 2, "value": 4.17, "curve": [2.444, 4.18, 2.889, -6.34]}, {"time": 3.3333, "value": -6.35, "curve": [3.778, -6.35, 4.222, 4.17]}, {"time": 4.6667, "value": 4.17, "curve": [5.111, 4.18, 5.556, -6.34]}, {"time": 6, "value": -6.35, "curve": [6.333, -6.35, 6.667, 4.17]}, {"time": 7, "value": 4.17, "curve": [7.444, 4.17, 7.889, -1.09]}, {"time": 8.3333, "value": -1.09, "curve": [8.778, -1.09, 9.222, 4.17]}, {"time": 9.6667, "value": 4.17, "curve": [10.111, 4.18, 10.556, -6.34]}, {"time": 11, "value": -6.35, "curve": [11.444, -6.35, 11.889, 4.17]}, {"time": 12.3333, "value": 4.17, "curve": [12.557, 4.17, 12.781, 1.56]}, {"time": 13, "value": -1.09}]}, "hair_R2": {"rotate": [{"value": -1.09, "curve": [0.225, 1.52, 0.446, 4.17]}, {"time": 0.6667, "value": 4.17, "curve": [1.111, 4.17, 1.556, -6.34]}, {"time": 2, "value": -6.35, "curve": [2.444, -6.35, 2.889, 4.17]}, {"time": 3.3333, "value": 4.17, "curve": [3.778, 4.18, 4.222, -6.34]}, {"time": 4.6667, "value": -6.35, "curve": [5.111, -6.35, 5.556, 4.17]}, {"time": 6, "value": 4.17, "curve": [6.333, 4.18, 6.667, -6.35]}, {"time": 7, "value": -6.35, "curve": [7.444, -6.35, 7.889, -1.09]}, {"time": 8.3333, "value": -1.09, "curve": [8.778, -1.09, 9.222, -6.34]}, {"time": 9.6667, "value": -6.35, "curve": [10.111, -6.35, 10.556, 4.17]}, {"time": 11, "value": 4.17, "curve": [11.444, 4.18, 11.889, -6.34]}, {"time": 12.3333, "value": -6.35, "curve": [12.557, -6.35, 12.781, -3.74]}, {"time": 13, "value": -1.09}]}, "hair_F2": {"rotate": [{"value": -3.41, "curve": [0.279, 1.7, 0.556, 8.49]}, {"time": 0.8333, "value": 8.49, "curve": [1.278, 8.49, 1.722, -8.95]}, {"time": 2.1667, "value": -8.95, "curve": [2.611, -8.96, 3.056, 8.49]}, {"time": 3.5, "value": 8.49, "curve": [3.944, 8.5, 4.389, -8.95]}, {"time": 4.8333, "value": -8.95, "curve": [5.278, -8.96, 5.722, 8.49]}, {"time": 6.1667, "value": 8.49, "curve": [6.5, 8.5, 6.833, -8.95]}, {"time": 7.1667, "value": -8.95, "curve": [7.611, -8.95, 8.056, -0.23]}, {"time": 8.5, "value": -0.23, "curve": [8.944, -0.23, 9.389, -8.95]}, {"time": 9.8333, "value": -8.95, "curve": [10.278, -8.96, 10.722, 8.49]}, {"time": 11.1667, "value": 8.49, "curve": [11.611, 8.5, 12.056, -8.95]}, {"time": 12.5, "value": -8.95, "curve": [12.667, -8.95, 12.835, -6.49]}, {"time": 13, "value": -3.41}]}, "hair_F8": {"rotate": [{"value": -0.61, "curve": [0.225, 1.1, 0.446, 2.83]}, {"time": 0.6667, "value": 2.83, "curve": [1.111, 2.83, 1.556, -4.06]}, {"time": 2, "value": -4.06, "curve": [2.444, -4.06, 2.889, 2.83]}, {"time": 3.3333, "value": 2.83, "curve": [3.778, 2.83, 4.222, -4.06]}, {"time": 4.6667, "value": -4.06, "curve": [5.111, -4.06, 5.556, 2.83]}, {"time": 6, "value": 2.83, "curve": [6.333, 2.83, 6.667, -4.06]}, {"time": 7, "value": -4.06, "curve": [7.444, -4.06, 7.889, -0.61]}, {"time": 8.3333, "value": -0.61, "curve": [8.778, -0.61, 9.222, -4.06]}, {"time": 9.6667, "value": -4.06, "curve": [10.111, -4.06, 10.556, 2.83]}, {"time": 11, "value": 2.83, "curve": [11.444, 2.83, 11.889, -4.06]}, {"time": 12.3333, "value": -4.06, "curve": [12.557, -4.06, 12.781, -2.35]}, {"time": 13, "value": -0.61}]}, "headround3": {"translate": [{"x": -42.39, "curve": [0.168, -97.78, 0.334, -143.22, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -143.22, "curve": [0.944, -143.22, 1.389, 173.69, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 173.77, "curve": [2.278, 173.85, 2.722, -143.14, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -143.22, "curve": [3.611, -143.3, 4.056, 173.69, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 173.77, "curve": [4.944, 173.85, 5.389, -143.11, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -143.22, "curve": [6.167, -143.3, 6.5, 173.76, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 173.77, "curve": [7.278, 173.79, 7.722, 84.73, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": 84.71, "curve": [8.611, 84.69, 9.056, 173.69, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 173.77, "curve": [9.944, 173.85, 10.389, -143.14, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -143.22, "curve": [11.278, -143.3, 11.722, 173.69, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 173.77, "curve": [12.445, 173.82, 12.724, 50.57, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -42.39}]}, "headround": {"translate": [{"time": 4.6667, "curve": [5.111, 0, 5.556, 0, 5.111, 0, 5.556, 0.11]}, {"time": 6, "curve": [6.333, 0, 6.667, 0, 6.333, -0.09, 6.667, 339.95]}, {"time": 7, "y": 339.96, "curve": [7.444, 0, 7.889, 0, 7.444, 339.98, 7.889, 278.14]}, {"time": 8.3333, "y": 278.13, "curve": [8.778, 0, 9.222, 0, 8.778, 278.11, 9.222, 339.87]}, {"time": 9.6667, "y": 339.96, "curve": [10.111, 0, 10.556, 0, 10.111, 340.05, 10.556, 0]}, {"time": 11}]}, "bodyround": {"translate": [{"x": -80.88, "y": -6.86, "curve": [0.114, -102.77, 0.224, -119.08, 0.114, -8.06, 0.224, -8.96]}, {"time": 0.3333, "x": -119.08, "y": -8.96, "curve": [0.778, -119.08, 1.222, 119.52, 0.778, -8.96, 1.222, 4.11]}, {"time": 1.6667, "x": 119.58, "y": 4.12, "curve": [2.111, 119.64, 2.556, -119.02, 2.111, 4.12, 2.556, -8.95]}, {"time": 3, "x": -119.08, "y": -8.96, "curve": [3.444, -119.14, 3.889, 119.52, 3.444, -8.96, 3.889, 4.11]}, {"time": 4.3333, "x": 119.58, "y": 4.12, "curve": [4.778, 119.64, 5.222, -119, 4.778, 4.12, 5.222, -8.95]}, {"time": 5.6667, "x": -119.08, "y": -8.96, "curve": [6, -119.14, 6.333, 119.56, 6, -8.96, 6.333, 4.12]}, {"time": 6.6667, "x": 119.58, "y": 4.12, "curve": [7.111, 119.61, 7.556, 0.28, 7.111, 4.12, 7.556, -2.42]}, {"time": 8, "x": 0.25, "y": -2.42, "curve": [8.444, 0.22, 8.889, 119.52, 8.444, -2.42, 8.889, 4.11]}, {"time": 9.3333, "x": 119.58, "y": 4.12, "curve": [9.778, 119.64, 10.222, -119.02, 9.778, 4.12, 10.222, -8.95]}, {"time": 10.6667, "x": -119.08, "y": -8.96, "curve": [11.111, -119.14, 11.556, 119.52, 11.111, -8.96, 11.556, 4.11]}, {"time": 12, "x": 119.58, "y": 4.12, "curve": [12.335, 119.63, 12.67, -14.05, 12.335, 4.12, 12.67, -3.2]}, {"time": 13, "x": -80.88, "y": -6.86}]}, "tunround": {"translate": [{"y": 107.27, "curve": [0.444, 0, 0.889, 0, 0.444, 107.27, 0.889, -111.62]}, {"time": 1.3333, "y": -111.68, "curve": [1.778, 0, 2.222, 0, 1.778, -111.73, 2.222, 107.21]}, {"time": 2.6667, "y": 107.27, "curve": [3.111, 0, 3.556, 0, 3.111, 107.32, 3.556, -111.62]}, {"time": 4, "y": -111.68, "curve": [4.444, 0, 4.889, 0, 4.444, -111.73, 4.889, 107.2]}, {"time": 5.3333, "y": 107.27, "curve": [5.667, 0, 6, 0, 5.667, 107.32, 6, -111.66]}, {"time": 6.3333, "y": -111.68, "curve": [6.778, 0, 7.222, 0, 6.778, -111.71, 7.222, -2.23]}, {"time": 7.6667, "y": -2.2, "curve": [8.111, 0, 8.556, 0, 8.111, -2.18, 8.556, -111.62]}, {"time": 9, "y": -111.68, "curve": [9.444, 0, 9.889, 0, 9.444, -111.73, 9.889, 107.21]}, {"time": 10.3333, "y": 107.27, "curve": [10.778, 0, 11.222, 0, 10.778, 107.32, 11.222, -111.62]}, {"time": 11.6667, "y": -111.68, "curve": [12.111, 0, 12.556, 0, 12.111, -111.73, 12.556, 107.27]}, {"time": 13, "y": 107.27}]}, "sh_L2": {"rotate": [{"value": -0.07, "curve": "stepped"}, {"time": 5.3333, "value": -0.07, "curve": [5.667, -0.07, 6, -0.07]}, {"time": 6.3333, "value": -0.07, "curve": [6.778, -0.07, 7.222, 1.8]}, {"time": 7.6667, "value": 1.8, "curve": [8.111, 1.8, 8.556, -0.07]}, {"time": 9, "value": -0.07}]}, "sh_L3": {"rotate": [{"value": -0.17, "curve": "stepped"}, {"time": 5.3333, "value": -0.17, "curve": [5.667, -0.17, 6, -0.17]}, {"time": 6.3333, "value": -0.17, "curve": [6.778, -0.17, 7.222, -3.16]}, {"time": 7.6667, "value": -3.16, "curve": [8.111, -3.16, 8.556, -0.17]}, {"time": 9, "value": -0.17}]}, "leg_R4": {"rotate": [{"value": 0.2, "curve": "stepped"}, {"time": 5.3333, "value": 0.2, "curve": [5.667, 0.2, 6, 0.2]}, {"time": 6.3333, "value": 0.2, "curve": [6.778, 0.2, 7.222, -0.13]}, {"time": 7.6667, "value": -0.13, "curve": [8.111, -0.13, 8.556, 0.2]}, {"time": 9, "value": 0.2}]}, "leg_R5": {"rotate": [{"value": -0.6, "curve": "stepped"}, {"time": 5.3333, "value": -0.6, "curve": [5.667, -0.6, 6, -0.6]}, {"time": 6.3333, "value": -0.6, "curve": [6.778, -0.6, 7.222, -0.45]}, {"time": 7.6667, "value": -0.45, "curve": [8.111, -0.45, 8.556, -0.6]}, {"time": 9, "value": -0.6}]}, "leg_R1": {"translate": [{"x": 13.27, "y": -29.86, "curve": [0.444, 13.27, 0.889, -8.84, 0.444, -29.86, 0.889, 47.54]}, {"time": 1.3333, "x": -8.85, "y": 47.56, "curve": [1.778, -8.85, 2.222, 13.27, 1.778, 47.58, 2.222, -29.84]}, {"time": 2.6667, "x": 13.27, "y": -29.86, "curve": [3.111, 13.28, 3.556, -8.84, 3.111, -29.88, 3.556, 47.54]}, {"time": 4, "x": -8.85, "y": 47.56, "curve": [4.444, -8.85, 4.889, 13.27, 4.444, 47.58, 4.889, -29.84]}, {"time": 5.3333, "x": 13.27, "y": -29.86, "curve": [5.667, 13.28, 6, -8.85, 5.667, -29.88, 6, 47.55]}, {"time": 6.3333, "x": -8.85, "y": 47.56, "curve": [6.778, -8.85, 7.222, 2.21, 6.778, 47.57, 7.222, 8.86]}, {"time": 7.6667, "x": 2.21, "y": 8.85, "curve": [8.111, 2.21, 8.556, -8.84, 8.111, 8.84, 8.556, 47.54]}, {"time": 9, "x": -8.85, "y": 47.56, "curve": [9.444, -8.85, 9.889, 13.27, 9.444, 47.58, 9.889, -29.84]}, {"time": 10.3333, "x": 13.27, "y": -29.86, "curve": [10.778, 13.28, 11.222, -8.84, 10.778, -29.88, 11.222, 47.54]}, {"time": 11.6667, "x": -8.85, "y": 47.56, "curve": [12.111, -8.85, 12.556, 13.27, 12.111, 47.58, 12.556, -29.86]}, {"time": 13, "x": 13.27, "y": -29.86}]}, "leg_L4": {"rotate": [{"value": -0.13, "curve": "stepped"}, {"time": 5.3333, "value": -0.13, "curve": [5.667, -0.13, 6, -0.13]}, {"time": 6.3333, "value": -0.13, "curve": [6.778, -0.13, 7.222, -0.38]}, {"time": 7.6667, "value": -0.38, "curve": [8.111, -0.38, 8.556, -0.13]}, {"time": 9, "value": -0.13}]}, "leg_L1": {"translate": [{"y": -7.08, "curve": [0.444, 0, 0.889, 0, 0.444, -7.08, 0.889, 6.38]}, {"time": 1.3333, "y": 6.38, "curve": [1.778, 0, 2.222, 0, 1.778, 6.39, 2.222, -7.08]}, {"time": 2.6667, "y": -7.08, "curve": [3.111, 0, 3.556, 0, 3.111, -7.09, 3.556, 6.38]}, {"time": 4, "y": 6.38, "curve": [4.444, 0, 4.889, 0, 4.444, 6.39, 4.889, -7.08]}, {"time": 5.3333, "y": -7.08, "curve": [5.667, 0, 6, 0, 5.667, -7.09, 6, 6.38]}, {"time": 6.3333, "y": 6.38, "curve": [6.778, 0, 7.222, 0, 6.778, 6.39, 7.222, -0.35]}, {"time": 7.6667, "y": -0.35, "curve": [8.111, 0, 8.556, 0, 8.111, -0.35, 8.556, 6.38]}, {"time": 9, "y": 6.38, "curve": [9.444, 0, 9.889, 0, 9.444, 6.39, 9.889, -7.08]}, {"time": 10.3333, "y": -7.08, "curve": [10.778, 0, 11.222, 0, 10.778, -7.09, 11.222, 6.38]}, {"time": 11.6667, "y": 6.38, "curve": [12.111, 0, 12.556, 0, 12.111, 6.39, 12.556, -7.08]}, {"time": 13, "y": -7.08}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -3.08, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -3.08, "curve": "stepped"}, {"time": 3, "x": -3.08, "curve": [3.033, -3.08, 3.067, -1.36, 3.033, 0, 3.067, -1.35]}, {"time": 3.1, "x": -1.36, "y": -1.35, "curve": "stepped"}, {"time": 3.3333, "x": -1.36, "y": -1.35, "curve": [3.367, -1.36, 3.4, 0, 3.367, -1.35, 3.4, 0]}, {"time": 3.4333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -4.49, 6.2, 0, 6.233, 5.22]}, {"time": 6.2667, "x": -4.49, "y": 5.22, "curve": "stepped"}, {"time": 7, "x": -4.49, "y": 5.22, "curve": [7.033, -4.49, 7.067, -4.49, 7.033, 5.22, 7.067, 6.42]}, {"time": 7.1, "x": -4.49, "y": 6.42, "curve": "stepped"}, {"time": 8, "x": -4.49, "y": 6.42, "curve": [8.033, -4.49, 8.067, -3.16, 8.033, 6.42, 8.067, 7.32]}, {"time": 8.1, "x": -3.16, "y": 7.32, "curve": "stepped"}, {"time": 8.5, "x": -3.16, "y": 7.32, "curve": [8.533, -3.16, 8.567, -4.49, 8.533, 7.32, 8.567, 5.22]}, {"time": 8.6, "x": -4.49, "y": 5.22, "curve": "stepped"}, {"time": 10, "x": -4.49, "y": 5.22, "curve": [10.033, -4.49, 10.067, 0, 10.033, 5.22, 10.067, 0]}, {"time": 10.1}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -3.08, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -3.08, "curve": "stepped"}, {"time": 3, "x": -3.08, "curve": [3.033, -3.08, 3.067, -1.36, 3.033, 0, 3.067, -1.35]}, {"time": 3.1, "x": -1.36, "y": -1.35, "curve": "stepped"}, {"time": 3.3333, "x": -1.36, "y": -1.35, "curve": [3.367, -1.36, 3.4, 0, 3.367, -1.35, 3.4, 0]}, {"time": 3.4333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -4.49, 6.2, 0, 6.233, 5.22]}, {"time": 6.2667, "x": -4.49, "y": 5.22, "curve": "stepped"}, {"time": 7, "x": -4.49, "y": 5.22, "curve": [7.033, -4.49, 7.067, -4.49, 7.033, 5.22, 7.067, 6.99]}, {"time": 7.1, "x": -4.49, "y": 6.99, "curve": "stepped"}, {"time": 8, "x": -4.49, "y": 6.99, "curve": [8.033, -4.49, 8.067, -3.16, 8.033, 6.99, 8.067, 7.9]}, {"time": 8.1, "x": -3.16, "y": 7.9, "curve": "stepped"}, {"time": 8.5, "x": -3.16, "y": 7.9, "curve": [8.533, -3.16, 8.567, -4.49, 8.533, 7.9, 8.567, 5.22]}, {"time": 8.6, "x": -4.49, "y": 5.22, "curve": "stepped"}, {"time": 10, "x": -4.49, "y": 5.22, "curve": [10.033, -4.49, 10.067, 0, 10.033, 5.22, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-9.1908, -0.04215, -9.18701, -0.0423, -5.80798, -0.02738, -5.79761, -0.0276, -4.28711, -0.02021, -4.27808, -0.02037, -2.95166, -0.0841, -2.94482, -0.08423, -2.03931, 0.06057, -2.03296, 0.06045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.89783, -0.07913, -1.89233, -0.07923, -6.94739, -0.17195, -6.94165, -0.17216, -11.22388, -0.68222, -11.21875, -0.68241, -13.11707, -0.48293, -13.1106, -0.48318, -11.85291, -0.26758, -11.84546, -0.26781, -11.71484, -0.19792, -11.70776, -0.19815, 0, 0, 0, 0, -3.51636, -0.40716, -3.51392, -0.40728, -8.13672, -0.39957, -8.13013, -0.39977, -12.01379, -0.54485, -12.01025, -0.54507, -13.96875, -0.45642, -13.96191, -0.45668, -11.06409, -0.19483, -11.05933, -0.19511, -4.63879, -0.52129, -4.6377, -0.5215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42786, -0.07808, -1.42627, -0.07822], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-9.1908, -0.04215, -9.18701, -0.0423, -5.80798, -0.02738, -5.79761, -0.0276, -4.28711, -0.02021, -4.27808, -0.02037, -2.95166, -0.0841, -2.94482, -0.08423, -2.03931, 0.06057, -2.03296, 0.06045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.89783, -0.07913, -1.89233, -0.07923, -6.94739, -0.17195, -6.94165, -0.17216, -11.22388, -0.68222, -11.21875, -0.68241, -13.11707, -0.48293, -13.1106, -0.48318, -11.85291, -0.26758, -11.84546, -0.26781, -11.71484, -0.19792, -11.70776, -0.19815, 0, 0, 0, 0, -3.51636, -0.40716, -3.51392, -0.40728, -8.13672, -0.39957, -8.13013, -0.39977, -12.01379, -0.54485, -12.01025, -0.54507, -13.96875, -0.45642, -13.96191, -0.45668, -11.06409, -0.19483, -11.05933, -0.19511, -4.63879, -0.52129, -4.6377, -0.5215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42786, -0.07808, -1.42627, -0.07822], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-9.1908, -0.04215, -9.18701, -0.0423, -5.80798, -0.02738, -5.79761, -0.0276, -4.28711, -0.02021, -4.27808, -0.02037, -2.95166, -0.0841, -2.94482, -0.08423, -2.03931, 0.06057, -2.03296, 0.06045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.89783, -0.07913, -1.89233, -0.07923, -6.94739, -0.17195, -6.94165, -0.17216, -11.22388, -0.68222, -11.21875, -0.68241, -13.11707, -0.48293, -13.1106, -0.48318, -11.85291, -0.26758, -11.84546, -0.26781, -11.71484, -0.19792, -11.70776, -0.19815, 0, 0, 0, 0, -3.51636, -0.40716, -3.51392, -0.40728, -8.13672, -0.39957, -8.13013, -0.39977, -12.01379, -0.54485, -12.01025, -0.54507, -13.96875, -0.45642, -13.96191, -0.45668, -11.06409, -0.19483, -11.05933, -0.19511, -4.63879, -0.52129, -4.6377, -0.5215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42786, -0.07808, -1.42627, -0.07822], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-11.43323, -0.39841, -11.42554, -0.39874, -12.96619, -0.0611, -12.95508, -0.06153, -10.70105, -0.16527, -10.69287, -0.16557, -6.45386, 0.14185, -6.44604, 0.14153, -1.70569, -0.00804, -1.70117, -0.00818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.05627, 0.05357, -1.05371, 0.05347, -2.34692, 0.1646, -2.34204, 0.16441, -5.92615, -0.70412, -5.91992, -0.70445, -6.90735, -0.24155, -6.89819, -0.24194, -9.96289, -0.10437, -9.95679, -0.10463, -11.43323, -0.39841, -11.42554, -0.39874, 0, 0, 0, 0, -2.97327, -0.05831, -2.96606, -0.05859, -7.67554, -0.30696, -7.67041, -0.30709, -11.25208, -0.19093, -11.24487, -0.19122, -13.54614, -0.33463, -13.53687, -0.33483, -12.31934, -0.27951, -12.31323, -0.27981, -8.97742, 0.21036, -8.97192, 0.20999, -3.42346, 0.02958, -3.41187, 0.02927, -1.05542, -0.00498, -1.05322], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-11.43323, -0.39841, -11.42554, -0.39874, -12.96619, -0.0611, -12.95508, -0.06153, -10.70105, -0.16527, -10.69287, -0.16557, -6.45386, 0.14185, -6.44604, 0.14153, -1.70569, -0.00804, -1.70117, -0.00818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.05627, 0.05357, -1.05371, 0.05347, -2.34692, 0.1646, -2.34204, 0.16441, -5.92615, -0.70412, -5.91992, -0.70445, -6.90735, -0.24155, -6.89819, -0.24194, -9.96289, -0.10437, -9.95679, -0.10463, -11.43323, -0.39841, -11.42554, -0.39874, 0, 0, 0, 0, -2.97327, -0.05831, -2.96606, -0.05859, -7.67554, -0.30696, -7.67041, -0.30709, -11.25208, -0.19093, -11.24487, -0.19122, -13.54614, -0.33463, -13.53687, -0.33483, -12.31934, -0.27951, -12.31323, -0.27981, -8.97742, 0.21036, -8.97192, 0.20999, -3.42346, 0.02958, -3.41187, 0.02927, -1.05542, -0.00498, -1.05322], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-11.43323, -0.39841, -11.42554, -0.39874, -12.96619, -0.0611, -12.95508, -0.06153, -10.70105, -0.16527, -10.69287, -0.16557, -6.45386, 0.14185, -6.44604, 0.14153, -1.70569, -0.00804, -1.70117, -0.00818, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.05627, 0.05357, -1.05371, 0.05347, -2.34692, 0.1646, -2.34204, 0.16441, -5.92615, -0.70412, -5.91992, -0.70445, -6.90735, -0.24155, -6.89819, -0.24194, -9.96289, -0.10437, -9.95679, -0.10463, -11.43323, -0.39841, -11.42554, -0.39874, 0, 0, 0, 0, -2.97327, -0.05831, -2.96606, -0.05859, -7.67554, -0.30696, -7.67041, -0.30709, -11.25208, -0.19093, -11.24487, -0.19122, -13.54614, -0.33463, -13.53687, -0.33483, -12.31934, -0.27951, -12.31323, -0.27981, -8.97742, 0.21036, -8.97192, 0.20999, -3.42346, 0.02958, -3.41187, 0.02927, -1.05542, -0.00498, -1.05322], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 390, "vertices": [-2.52527, -0.19876, -2.52368, -0.19885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.02734, -0.37855, -1.02661, -0.3786, -2.33655, -0.38471, -2.33521, -0.38478, -6.82397, 0.06126, -6.8208, 0.06111, -10.65356, -0.4239, -10.64966, -0.42406, -12.05762, -0.89766, -12.05151, -0.89797, -10.83972, -0.61164, -10.8374, -0.61182, -7.29089, -0.31464, -7.28784, -0.3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.99231, 0.07932, -2.9895, 0.07925, -7.3844, -0.0348, -7.38086, -0.03493, -10.28333, 0.13839, -10.27881, 0.13817, -11.59229, 0.4125, -11.58765, 0.41235, -10.28503, 0.32523, -10.27905, 0.32504, -7.01245, 0.34066, -7.00928, 0.34054, -1.59094, 0.17935, -1.59009, 0.17931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.21594, -0.00573, -1.21509], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 390, "vertices": [-2.52527, -0.19876, -2.52368, -0.19885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.02734, -0.37855, -1.02661, -0.3786, -2.33655, -0.38471, -2.33521, -0.38478, -6.82397, 0.06126, -6.8208, 0.06111, -10.65356, -0.4239, -10.64966, -0.42406, -12.05762, -0.89766, -12.05151, -0.89797, -10.83972, -0.61164, -10.8374, -0.61182, -7.29089, -0.31464, -7.28784, -0.3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.99231, 0.07932, -2.9895, 0.07925, -7.3844, -0.0348, -7.38086, -0.03493, -10.28333, 0.13839, -10.27881, 0.13817, -11.59229, 0.4125, -11.58765, 0.41235, -10.28503, 0.32523, -10.27905, 0.32504, -7.01245, 0.34066, -7.00928, 0.34054, -1.59094, 0.17935, -1.59009, 0.17931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.21594, -0.00573, -1.21509], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 390, "vertices": [-2.52527, -0.19876, -2.52368, -0.19885, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.02734, -0.37855, -1.02661, -0.3786, -2.33655, -0.38471, -2.33521, -0.38478, -6.82397, 0.06126, -6.8208, 0.06111, -10.65356, -0.4239, -10.64966, -0.42406, -12.05762, -0.89766, -12.05151, -0.89797, -10.83972, -0.61164, -10.8374, -0.61182, -7.29089, -0.31464, -7.28784, -0.3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.99231, 0.07932, -2.9895, 0.07925, -7.3844, -0.0348, -7.38086, -0.03493, -10.28333, 0.13839, -10.27881, 0.13817, -11.59229, 0.4125, -11.58765, 0.41235, -10.28503, 0.32523, -10.27905, 0.32504, -7.01245, 0.34066, -7.00928, 0.34054, -1.59094, 0.17935, -1.59009, 0.17931, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.21594, -0.00573, -1.21509], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 68, "vertices": [0.40491, 0.48494, 0.40503, 0.48475, 0.04675, 0.37818, 0.04773, 0.37816, -1.12476, 0.23842, -1.12463, 0.23821, -1.12878, 0.11701, -1.12805, 0.11687, -0.7572, -0.00784, -0.7572, -0.008, -1.09729, -0.10136, -1.09729, -0.10139, -1.08545, -0.22363, -1.08447, -0.22364, -0.80908, -0.36169, -0.80908, -0.36177, 0.4115, -0.48255, 0.41223, -0.48258, 0.79126, 0.93904, 0.79126, 0.93891, 0.60876, 0.47671, 0.60889, 0.47667, -0.37927, 0.3021, -0.37927, 0.30212, -0.11621, 0.15595, -0.11548, 0.15591, 0.32971, 0.00197, 0.32971, 0.00195, -0.12341, -0.15624, -0.12329, -0.15634, -0.37781, -0.30486, -0.37781, -0.30481, 0.63696, -0.47177, 0.6377, -0.47179, 0.88184, -0.84404, 0.88391, -0.8441, 1.18311, 0.4261, 1.18408, 0.42597, 0.98511, 0.32286, 0.98511, 0.32275, 1.92114, 0.19583, 1.92188, 0.19579, 2.34558, 0.00625, 2.34558, 0.00607, 1.88196, -0.18972, 1.88208, -0.18974, 0.86084, -0.34193, 0.86084, -0.342, 1.13135, -0.4534, 1.13208, -0.45348, 0.22546, 0.94534, 0.22546, 0.94526, 0.71533, 1.04129, 0.71533, 1.04129, 1.20569, 0.98984, 1.20569, 0.98969, 0.34009, -0.85317, 0.34216, -0.85326, 0.84668, -0.95309, 0.84705, -0.95315, 1.33643, -0.87796, 1.33862, -0.87798, 1.54529, 0.19879, 1.54602, 0.19868, 1.95911, 0.0069, 1.95911, 0.00679, 1.5769, -0.18046, 1.57703, -0.1806, 0.71436, -0.33525, 0.71436, -0.33526, 0.69641, 0.31573, 0.69641, 0.31563, -1.25574, 0.24217, -1.25562, 0.2421, -1.2251, 0.11202, -1.22437, 0.11195, -1.00574, -0.00192, -1.00574, -0.00198, -1.20996, -0.11478, -1.20996, -0.11489, -1.22522, -0.23749, -1.22424, -0.23749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67126, 0.00315, 0.67126, 0.00315, 1.53442, 0.0072, 1.53442, 0.0072, 0.67126, 0.00315, 0.67126], "curve": "stepped"}, {"time": 9.8333, "offset": 68, "vertices": [0.40491, 0.48494, 0.40503, 0.48475, 0.04675, 0.37818, 0.04773, 0.37816, -1.12476, 0.23842, -1.12463, 0.23821, -1.12878, 0.11701, -1.12805, 0.11687, -0.7572, -0.00784, -0.7572, -0.008, -1.09729, -0.10136, -1.09729, -0.10139, -1.08545, -0.22363, -1.08447, -0.22364, -0.80908, -0.36169, -0.80908, -0.36177, 0.4115, -0.48255, 0.41223, -0.48258, 0.79126, 0.93904, 0.79126, 0.93891, 0.60876, 0.47671, 0.60889, 0.47667, -0.37927, 0.3021, -0.37927, 0.30212, -0.11621, 0.15595, -0.11548, 0.15591, 0.32971, 0.00197, 0.32971, 0.00195, -0.12341, -0.15624, -0.12329, -0.15634, -0.37781, -0.30486, -0.37781, -0.30481, 0.63696, -0.47177, 0.6377, -0.47179, 0.88184, -0.84404, 0.88391, -0.8441, 1.18311, 0.4261, 1.18408, 0.42597, 0.98511, 0.32286, 0.98511, 0.32275, 1.92114, 0.19583, 1.92188, 0.19579, 2.34558, 0.00625, 2.34558, 0.00607, 1.88196, -0.18972, 1.88208, -0.18974, 0.86084, -0.34193, 0.86084, -0.342, 1.13135, -0.4534, 1.13208, -0.45348, 0.22546, 0.94534, 0.22546, 0.94526, 0.71533, 1.04129, 0.71533, 1.04129, 1.20569, 0.98984, 1.20569, 0.98969, 0.34009, -0.85317, 0.34216, -0.85326, 0.84668, -0.95309, 0.84705, -0.95315, 1.33643, -0.87796, 1.33862, -0.87798, 1.54529, 0.19879, 1.54602, 0.19868, 1.95911, 0.0069, 1.95911, 0.00679, 1.5769, -0.18046, 1.57703, -0.1806, 0.71436, -0.33525, 0.71436, -0.33526, 0.69641, 0.31573, 0.69641, 0.31563, -1.25574, 0.24217, -1.25562, 0.2421, -1.2251, 0.11202, -1.22437, 0.11195, -1.00574, -0.00192, -1.00574, -0.00198, -1.20996, -0.11478, -1.20996, -0.11489, -1.22522, -0.23749, -1.22424, -0.23749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67126, 0.00315, 0.67126, 0.00315, 1.53442, 0.0072, 1.53442, 0.0072, 0.67126, 0.00315, 0.67126], "curve": [9.944, 0, 10.056, 1]}, {"time": 10.1667}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 4.46, "curve": [0.024, 4.46, 0.058, -4.46, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -4.46, "curve": [0.544, -4.47, 0.856, 4.46, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": 4.46}]}, "ALL2": {"translate": [{"y": -16.85, "curve": [0.078, 0, 0.156, 0, 0.024, -16.85, 0.058, 22.19]}, {"time": 0.2333, "y": 22.19, "curve": [0.544, 0, 0.856, 0, 0.544, 22.2, 0.856, -16.85]}, {"time": 1.1667, "y": -16.85}]}, "body": {"translate": [{"y": -10.55, "curve": [0.078, 0, 0.156, 0, 0.024, -10.55, 0.058, 15.09]}, {"time": 0.2333, "y": 15.09, "curve": [0.544, 0, 0.856, 0, 0.544, 15.09, 0.856, -10.55]}, {"time": 1.1667, "y": -10.55}], "scale": [{"y": 1.041, "curve": [0.078, 1, 0.156, 1, 0.024, 1.041, 0.058, 0.966]}, {"time": 0.2333, "y": 0.966, "curve": [0.544, 1, 0.856, 1, 0.544, 0.966, 0.856, 1.041]}, {"time": 1.1667, "y": 1.041}]}, "body2": {"rotate": [{"value": -0.81}], "translate": [{"x": -7.78, "curve": [0.024, -7.78, 0.058, 12.01, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 12.01, "curve": [0.544, 12.01, 0.856, -7.78, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -7.78}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.024, 1.003, 0.058, 1.017, 0.024, 1.003, 0.058, 1.017]}, {"time": 0.2333, "x": 1.017, "y": 1.017, "curve": [0.544, 1.017, 0.856, 1.003, 0.544, 1.017, 0.856, 1.003]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"curve": [0.024, 0, 0.058, 4.18]}, {"time": 0.2333, "value": 4.18, "curve": [0.544, 4.18, 0.856, 0]}, {"time": 1.1667}]}, "head": {"rotate": [{"curve": [0.024, 0, 0.058, 4.18]}, {"time": 0.2333, "value": 4.18, "curve": [0.544, 4.18, 0.856, 0]}, {"time": 1.1667}]}, "tun": {"rotate": [{"value": 2.04, "curve": [0.024, 2.04, 0.058, -1.57]}, {"time": 0.2333, "value": -1.57, "curve": [0.544, -1.57, 0.856, 2.04]}, {"time": 1.1667, "value": 2.04}]}, "leg_R3": {"rotate": [{}]}, "leg_L3": {"rotate": [{"value": 0.01}]}, "foot_L": {"rotate": [{"curve": [0.024, 0, 0.058, -2]}, {"time": 0.2333, "value": -2, "curve": [0.544, -2, 0.856, 0]}, {"time": 1.1667}]}, "foot_R": {"rotate": [{"value": -1.76, "curve": [0.024, -1.76, 0.058, 2.54]}, {"time": 0.2333, "value": 2.54, "curve": [0.544, 2.55, 0.856, -1.76]}, {"time": 1.1667, "value": -1.76}]}, "sh_L": {"translate": [{"x": -5.01, "curve": [0.024, -5.01, 0.058, 16.13, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 16.13, "curve": [0.544, 16.13, 0.856, -5.01, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -5.01}]}, "sh_R": {"translate": [{"x": -5.01, "curve": [0.024, -5.01, 0.058, 16.13, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 16.13, "curve": [0.544, 16.13, 0.856, -5.01, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -5.01}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{"value": -0.01}]}, "hand_L2": {"rotate": [{"value": -3, "curve": [0.024, -3, 0.058, 8.94]}, {"time": 0.2333, "value": 8.94, "curve": [0.544, 8.94, 0.856, -3]}, {"time": 1.1667, "value": -3}]}, "arm_R": {"rotate": [{"value": 0.56, "curve": [0.073, 1.16, 0.128, 1.65]}, {"time": 0.2, "value": 1.65, "curve": [0.392, 1.65, 0.608, -1.75]}, {"time": 0.8, "value": -1.75, "curve": [0.92, -1.75, 1.047, -0.43]}, {"time": 1.1667, "value": 0.56}]}, "arm_R2": {"rotate": [{"value": -0.05, "curve": [0.097, 0.79, 0.205, 1.65]}, {"time": 0.3, "value": 1.65, "curve": [0.492, 1.65, 0.675, -1.75]}, {"time": 0.8667, "value": -1.75, "curve": [0.963, -1.75, 1.072, -0.91]}, {"time": 1.1667, "value": -0.05}]}, "arm_R3": {"rotate": [{"value": -0.67, "curve": [0.121, 0.32, 0.247, 1.65]}, {"time": 0.3667, "value": 1.65, "curve": [0.559, 1.65, 0.741, -1.75]}, {"time": 0.9333, "value": -1.75, "curve": [1.006, -1.75, 1.095, -1.27]}, {"time": 1.1667, "value": -0.67}]}, "RU_L": {"translate": [{"x": -16.96, "curve": [0.073, -32.6, 0.128, -45.44, 0.073, 0, 0.128, 0]}, {"time": 0.2, "x": -45.44, "curve": [0.392, -45.44, 0.608, 44.06, 0.392, 0, 0.608, 0]}, {"time": 0.8, "x": 44.08, "curve": [0.92, 44.1, 1.047, 9.3, 0.92, 0, 1.047, 0]}, {"time": 1.1667, "x": -16.95}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.072, 1, 0.128, 0.896, 0.072, 0.998, 0.128, 1.087]}, {"time": 0.2, "x": 0.896, "y": 1.087, "curve": [0.296, 0.896, 0.404, 1.083, 0.296, 1.087, 0.404, 0.928]}, {"time": 0.5, "x": 1.083, "y": 0.928, "curve": [0.596, 1.083, 0.704, 0.896, 0.596, 0.928, 0.704, 1.087]}, {"time": 0.8, "x": 0.896, "y": 1.087, "curve": [0.896, 0.896, 0.971, 1.083, 0.896, 1.087, 0.971, 0.928]}, {"time": 1.0667, "x": 1.083, "y": 0.928, "curve": [1.091, 1.083, 1.143, 1.071, 1.091, 0.928, 1.143, 0.938]}, {"time": 1.1667, "x": 1.053, "y": 0.954}]}, "RU_L2": {"translate": [{"x": 0.49, "curve": [0.097, -14.86, 0.205, -30.41, 0.097, 0, 0.205, 0]}, {"time": 0.3, "x": -30.41, "curve": [0.492, -30.41, 0.675, 31.36, 0.492, 0, 0.675, 0]}, {"time": 0.8667, "x": 31.37, "curve": [0.963, 31.38, 1.072, 16.04, 0.963, 0, 1.072, 0]}, {"time": 1.1667, "x": 0.49}], "scale": [{"x": 1.083, "y": 0.928, "curve": [0.096, 1.083, 0.204, 0.896, 0.096, 0.928, 0.204, 1.087]}, {"time": 0.3, "x": 0.896, "y": 1.087, "curve": [0.396, 0.896, 0.471, 1.083, 0.396, 1.087, 0.471, 0.928]}, {"time": 0.5667, "x": 1.083, "y": 0.928, "curve": [0.663, 1.083, 0.771, 0.896, 0.663, 0.928, 0.771, 1.087]}, {"time": 0.8667, "x": 0.896, "y": 1.087, "curve": [0.963, 0.896, 1.071, 1.083, 0.963, 1.087, 1.071, 0.928]}, {"time": 1.1667, "x": 1.083, "y": 0.928}]}, "RU_L3": {"translate": [{"x": 10.43, "curve": [0.121, -6.37, 0.247, -28.72, 0.121, 0, 0.247, 0]}, {"time": 0.3667, "x": -28.72, "curve": [0.559, -28.72, 0.741, 28.67, 0.559, 0, 0.741, 0]}, {"time": 0.9333, "x": 28.68, "curve": [1.006, 28.69, 1.095, 20.59, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 10.44}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.024, 1.071, 0.043, 1.083, 0.024, 0.939, 0.043, 0.928]}, {"time": 0.0667, "x": 1.083, "y": 0.928, "curve": [0.163, 1.083, 0.271, 0.896, 0.163, 0.928, 0.271, 1.087]}, {"time": 0.3667, "x": 0.896, "y": 1.087, "curve": [0.463, 0.896, 0.537, 1.083, 0.463, 1.087, 0.537, 0.928]}, {"time": 0.6333, "x": 1.083, "y": 0.928, "curve": [0.729, 1.083, 0.837, 0.896, 0.729, 0.928, 0.837, 1.087]}, {"time": 0.9333, "x": 0.896, "y": 1.087, "curve": [1.006, 0.896, 1.095, 1.001, 1.006, 1.087, 1.095, 0.998]}, {"time": 1.1667, "x": 1.053, "y": 0.954}]}, "RU_R": {"translate": [{"x": -16.96, "curve": [0.073, -32.6, 0.128, -45.44, 0.073, 0, 0.128, 0]}, {"time": 0.2, "x": -45.44, "curve": [0.392, -45.44, 0.608, 44.06, 0.392, 0, 0.608, 0]}, {"time": 0.8, "x": 44.08, "curve": [0.92, 44.1, 1.047, 9.3, 0.92, 0, 1.047, 0]}, {"time": 1.1667, "x": -16.95}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.072, 1, 0.128, 0.896, 0.072, 0.998, 0.128, 1.087]}, {"time": 0.2, "x": 0.896, "y": 1.087, "curve": [0.296, 0.896, 0.404, 1.083, 0.296, 1.087, 0.404, 0.928]}, {"time": 0.5, "x": 1.083, "y": 0.928, "curve": [0.596, 1.083, 0.704, 0.896, 0.596, 0.928, 0.704, 1.087]}, {"time": 0.8, "x": 0.896, "y": 1.087, "curve": [0.896, 0.896, 0.971, 1.083, 0.896, 1.087, 0.971, 0.928]}, {"time": 1.0667, "x": 1.083, "y": 0.928, "curve": [1.091, 1.083, 1.143, 1.071, 1.091, 0.928, 1.143, 0.938]}, {"time": 1.1667, "x": 1.053, "y": 0.954}]}, "RU_R2": {"translate": [{"x": 0.49, "curve": [0.097, -14.86, 0.205, -30.41, 0.097, 0, 0.205, 0]}, {"time": 0.3, "x": -30.41, "curve": [0.492, -30.41, 0.675, 31.36, 0.492, 0, 0.675, 0]}, {"time": 0.8667, "x": 31.37, "curve": [0.963, 31.38, 1.072, 16.04, 0.963, 0, 1.072, 0]}, {"time": 1.1667, "x": 0.49}], "scale": [{"x": 1.083, "y": 0.928, "curve": [0.096, 1.083, 0.204, 0.896, 0.096, 0.928, 0.204, 1.087]}, {"time": 0.3, "x": 0.896, "y": 1.087, "curve": [0.396, 0.896, 0.471, 1.083, 0.396, 1.087, 0.471, 0.928]}, {"time": 0.5667, "x": 1.083, "y": 0.928, "curve": [0.663, 1.083, 0.771, 0.896, 0.663, 0.928, 0.771, 1.087]}, {"time": 0.8667, "x": 0.896, "y": 1.087, "curve": [0.963, 0.896, 1.071, 1.083, 0.963, 1.087, 1.071, 0.928]}, {"time": 1.1667, "x": 1.083, "y": 0.928}]}, "RU_R3": {"translate": [{"x": 10.43, "curve": [0.121, -6.37, 0.247, -28.72, 0.121, 0, 0.247, 0]}, {"time": 0.3667, "x": -28.72, "curve": [0.559, -28.72, 0.741, 28.67, 0.559, 0, 0.741, 0]}, {"time": 0.9333, "x": 28.68, "curve": [1.006, 28.69, 1.095, 20.59, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 10.44}], "scale": [{"x": 1.053, "y": 0.954, "curve": [0.024, 1.071, 0.043, 1.083, 0.024, 0.939, 0.043, 0.928]}, {"time": 0.0667, "x": 1.083, "y": 0.928, "curve": [0.163, 1.083, 0.271, 0.896, 0.163, 0.928, 0.271, 1.087]}, {"time": 0.3667, "x": 0.896, "y": 1.087, "curve": [0.463, 0.896, 0.537, 1.083, 0.463, 1.087, 0.537, 0.928]}, {"time": 0.6333, "x": 1.083, "y": 0.928, "curve": [0.729, 1.083, 0.837, 0.896, 0.729, 0.928, 0.837, 1.087]}, {"time": 0.9333, "x": 0.896, "y": 1.087, "curve": [1.006, 0.896, 1.095, 1.001, 1.006, 1.087, 1.095, 0.998]}, {"time": 1.1667, "x": 1.053, "y": 0.954}]}, "cloth_L": {"rotate": [{"value": -1.04, "curve": [0.073, -3.17, 0.128, -4.92]}, {"time": 0.2, "value": -4.92, "curve": [0.392, -4.92, 0.608, 7.29]}, {"time": 0.8, "value": 7.29, "curve": [0.92, 7.29, 1.047, 2.55]}, {"time": 1.1667, "value": -1.04}]}, "cloth_R": {"rotate": [{"value": 3.41, "curve": [0.073, 5.54, 0.128, 7.29]}, {"time": 0.2, "value": 7.29, "curve": [0.392, 7.29, 0.608, -4.92]}, {"time": 0.8, "value": -4.92, "curve": [0.92, -4.93, 1.047, -0.18]}, {"time": 1.1667, "value": 3.41}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.058, 1.15, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.15, "curve": [0.544, 1.15, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.058, -7.48]}, {"time": 0.2333, "value": -7.48, "curve": [0.544, -7.48, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.938, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.938, "curve": [0.544, 0.938, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.058, -13.04]}, {"time": 0.2333, "value": -13.04, "curve": [0.544, -13.04, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0, 0.058, 1.15, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.15, "curve": [0.544, 1.15, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.058, 7.32]}, {"time": 0.2333, "value": 7.32, "curve": [0.544, 7.32, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.938, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.938, "curve": [0.544, 0.938, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0, 0.058, 19.8]}, {"time": 0.2333, "value": 19.8, "curve": [0.544, 19.8, 0.856, 0]}, {"time": 1.1667}]}, "hair_L2": {"rotate": [{"value": -1.09, "curve": [0.097, -3.7, 0.205, -6.35]}, {"time": 0.3, "value": -6.35, "curve": [0.492, -6.35, 0.675, 4.17]}, {"time": 0.8667, "value": 4.17, "curve": [0.963, 4.17, 1.072, 1.56]}, {"time": 1.1667, "value": -1.08}]}, "hair_R2": {"rotate": [{"value": -1.09, "curve": [0.097, 1.52, 0.205, 4.17]}, {"time": 0.3, "value": 4.17, "curve": [0.492, 4.17, 0.675, -6.34]}, {"time": 0.8667, "value": -6.35, "curve": [0.963, -6.35, 1.072, -3.74]}, {"time": 1.1667, "value": -1.09}]}, "hair_F2": {"rotate": [{"value": -3.41, "curve": [0.121, 1.7, 0.247, 8.49]}, {"time": 0.3667, "value": 8.49, "curve": [0.559, 8.49, 0.741, -8.95]}, {"time": 0.9333, "value": -8.95, "curve": [1.006, -8.95, 1.095, -6.49]}, {"time": 1.1667, "value": -3.41}]}, "hair_F8": {"rotate": [{"value": -0.61, "curve": [0.097, 1.1, 0.205, 2.83]}, {"time": 0.3, "value": 2.83, "curve": [0.492, 2.83, 0.675, -4.06]}, {"time": 0.8667, "value": -4.06, "curve": [0.963, -4.06, 1.072, -2.35]}, {"time": 1.1667, "value": -0.61}]}, "headround3": {"translate": [{"x": -42.39, "curve": [0.024, -42.39, 0.058, -416.87, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -416.87, "curve": [0.544, -416.87, 0.856, -42.39, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": -42.39}]}, "headround": {"translate": [{"curve": [0.078, 0, 0.156, 0, 0.024, 0, 0.058, -105.99]}, {"time": 0.2333, "y": -105.99, "curve": [0.544, 0, 0.856, 0, 0.544, -105.99, 0.856, 0]}, {"time": 1.1667}]}, "bodyround": {"translate": [{"x": -80.88, "y": -6.86, "curve": [0.024, -80.88, 0.058, 296.11, 0.024, -6.86, 0.058, 4.12]}, {"time": 0.2333, "x": 296.11, "y": 4.12, "curve": [0.544, 296.11, 0.856, -80.88, 0.544, 4.12, 0.856, -6.86]}, {"time": 1.1667, "x": -80.88, "y": -6.86}]}, "tunround": {"translate": [{"y": 107.27, "curve": [0.078, 0, 0.156, 0, 0.024, 107.27, 0.058, -294.47]}, {"time": 0.2333, "y": -294.47, "curve": [0.678, 0, 0.856, 0, 0.544, -294.47, 0.856, 107.27]}, {"time": 1.1667, "y": 107.27}]}, "sh_L2": {"rotate": [{"value": -0.07}]}, "sh_L3": {"rotate": [{"value": -0.17}]}, "leg_R4": {"rotate": [{"value": 0.2}]}, "leg_R5": {"rotate": [{"value": -0.6}]}, "leg_R1": {"translate": [{"x": 13.27, "y": -29.86, "curve": [0.024, 13.27, 0.058, -8.85, 0.024, -29.86, 0.058, 73.29]}, {"time": 0.2333, "x": -8.85, "y": 73.29, "curve": [0.544, -8.85, 0.856, 13.27, 0.544, 73.29, 0.856, -29.86]}, {"time": 1.1667, "x": 13.27, "y": -29.86}]}, "leg_L4": {"rotate": [{"value": -0.13}]}, "leg_L1": {"translate": [{"y": -7.08, "curve": [0.078, 0, 0.156, 0, 0.024, -7.08, 0.058, 11.63]}, {"time": 0.2333, "y": 11.63, "curve": [0.544, 0, 0.856, 0, 0.544, 11.63, 0.856, -7.08]}, {"time": 1.1667, "y": -7.08}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.024, 0, 0.059, 1]}, {"time": 0.2333, "vertices": [-1.98521, -0.0091, -1.98439, -0.00914, -1.25452, -0.00591, -1.25228, -0.00596, -0.92601, -0.00437, -0.92406, -0.0044, -0.63756, -0.01817, -0.63608, -0.01819, -0.44049, 0.01308, -0.43912, 0.01306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.40993, -0.01709, -0.40874, -0.01711, -1.50063, -0.03714, -1.49939, -0.03719, -2.42435, -0.14736, -2.42325, -0.1474, -2.83328, -0.10431, -2.83189, -0.10437, -2.56022, -0.0578, -2.55862, -0.05785, -2.5304, -0.04275, -2.52887, -0.0428, 0, 0, 0, 0, -0.75953, -0.08795, -0.759, -0.08797, -1.75753, -0.08631, -1.7561, -0.08635, -2.59498, -0.11769, -2.59421, -0.11774, -3.01725, -0.09859, -3.01577, -0.09864, -2.38984, -0.04208, -2.38881, -0.04214, -1.00198, -0.1126, -1.00174, -0.11264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30842, -0.01687, -0.30807, -0.01689], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.024, 0, 0.059, 1]}, {"time": 0.2333, "vertices": [-2.46957, -0.08606, -2.46791, -0.08613, -2.80069, -0.0132, -2.79829, -0.01329, -2.31142, -0.0357, -2.30966, -0.03576, -1.39403, 0.03064, -1.39234, 0.03057, -0.36843, -0.00174, -0.36745, -0.00177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.22815, 0.01157, -0.2276, 0.01155, -0.50693, 0.03555, -0.50588, 0.03551, -1.28005, -0.15209, -1.2787, -0.15216, -1.49199, -0.05217, -1.49001, -0.05226, -2.15198, -0.02254, -2.15066, -0.0226, -2.46957, -0.08606, -2.46791, -0.08613, 0, 0, 0, 0, -0.64222, -0.01259, -0.64067, -0.01266, -1.65791, -0.0663, -1.65681, -0.06633, -2.43044, -0.04124, -2.42889, -0.0413, -2.92596, -0.07228, -2.92396, -0.07232, -2.66097, -0.06037, -2.65965, -0.06044, -1.93912, 0.04544, -1.93793, 0.04536, -0.73947, 0.00639, -0.73696, 0.00632, -0.22797, -0.00107, -0.2275], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.024, 0, 0.059, 1]}, {"time": 0.2333, "offset": 390, "vertices": [-0.54546, -0.04293, -0.54511, -0.04295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.22191, -0.08177, -0.22175, -0.08178, -0.50469, -0.0831, -0.5044, -0.08311, -1.47398, 0.01323, -1.47329, 0.0132, -2.30117, -0.09156, -2.30032, -0.0916, -2.60444, -0.19389, -2.60312, -0.19396, -2.34138, -0.13211, -2.34088, -0.13215, -1.57483, -0.06796, -1.57417, -0.068, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.64634, 0.01713, -0.64573, 0.01712, -1.59503, -0.00752, -1.59426, -0.00755, -2.2212, 0.02989, -2.22022, 0.02984, -2.50393, 0.0891, -2.50293, 0.08907, -2.22156, 0.07025, -2.22027, 0.07021, -1.51469, 0.07358, -1.514, 0.07356, -0.34364, 0.03874, -0.34346, 0.03873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.26264, -0.00124, -0.26246], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.024, 0, 0.059, 1]}, {"time": 0.2333, "offset": 68, "vertices": [0.40491, 0.48494, 0.40503, 0.48475, 0.04675, 0.37818, 0.04773, 0.37816, -1.12476, 0.23842, -1.12463, 0.23821, -1.12878, 0.11701, -1.12805, 0.11687, -0.7572, -0.00784, -0.7572, -0.008, -1.09729, -0.10136, -1.09729, -0.10139, -1.08545, -0.22363, -1.08447, -0.22364, -0.80908, -0.36169, -0.80908, -0.36177, 0.4115, -0.48255, 0.41223, -0.48258, 0.79126, 0.93904, 0.79126, 0.93891, 0.60876, 0.47671, 0.60889, 0.47667, -0.37927, 0.3021, -0.37927, 0.30212, -0.11621, 0.15595, -0.11548, 0.15591, 0.32971, 0.00197, 0.32971, 0.00195, -0.12341, -0.15624, -0.12329, -0.15634, -0.37781, -0.30486, -0.37781, -0.30481, 0.63696, -0.47177, 0.6377, -0.47179, 0.88184, -0.84404, 0.88391, -0.8441, 1.18311, 0.4261, 1.18408, 0.42597, 0.98511, 0.32286, 0.98511, 0.32275, 1.92114, 0.19583, 1.92188, 0.19579, 2.34558, 0.00625, 2.34558, 0.00607, 1.88196, -0.18972, 1.88208, -0.18974, 0.86084, -0.34193, 0.86084, -0.342, 1.13135, -0.4534, 1.13208, -0.45348, 0.22546, 0.94534, 0.22546, 0.94526, 0.71533, 1.04129, 0.71533, 1.04129, 1.20569, 0.98984, 1.20569, 0.98969, 0.34009, -0.85317, 0.34216, -0.85326, 0.84668, -0.95309, 0.84705, -0.95315, 1.33643, -0.87796, 1.33862, -0.87798, 1.54529, 0.19879, 1.54602, 0.19868, 1.95911, 0.0069, 1.95911, 0.00679, 1.5769, -0.18046, 1.57703, -0.1806, 0.71436, -0.33525, 0.71436, -0.33526, 0.69641, 0.31573, 0.69641, 0.31563, -1.25574, 0.24217, -1.25562, 0.2421, -1.2251, 0.11202, -1.22437, 0.11195, -1.00574, -0.00192, -1.00574, -0.00198, -1.20996, -0.11478, -1.20996, -0.11489, -1.22522, -0.23749, -1.22424, -0.23749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67126, 0.00315, 0.67126, 0.00315, 1.53442, 0.0072, 1.53442, 0.0072, 0.67126, 0.00315, 0.67126], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}