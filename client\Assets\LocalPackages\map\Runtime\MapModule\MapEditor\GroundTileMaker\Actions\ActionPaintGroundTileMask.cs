﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionPaintGroundTileMask : EditorAction
    {
        public ActionPaintGroundTileMask(GroundTileMaker maker, int lod, int tileIndex, int variationInstanceID, int maskTextureIndex, Color[] oldTextureData, Color[] newTextureData, RectI dirtyRange)
        {
            mLOD = lod;
            mMaker = maker;
            mTileIndex = tileIndex;
            mVariationInstanceID = variationInstanceID;
            mMaskTextureIndex = maskTextureIndex;
            int textureResolution = maker.GetMaskTextureSetting(lod)[maskTextureIndex].resolution;
            mDirtyRange = new RectI();
            mDirtyRange.Set(dirtyRange.xMin, dirtyRange.yMin, dirtyRange.xMax, dirtyRange.yMax);
            int minX = dirtyRange.xMin;
            int minY = dirtyRange.yMin;
            int maxX = dirtyRange.xMax;
            int maxY = dirtyRange.yMax;
            int width = maxX - minX + 1;
            int height = maxY - minY + 1;
            mOldTextureData = maker.color32ArrayPool.Rent(width * height);
            mNewTextureData = maker.color32ArrayPool.Rent(width * height);
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    int srcIdx = (i - minY) * width + j - minX;
                    int dstIdx = i * textureResolution + j;
                    mOldTextureData[srcIdx] = oldTextureData[dstIdx];
                    mNewTextureData[srcIdx] = newTextureData[dstIdx];
                }
            }
        }

        public override void OnDestroy()
        {
            mMaker.color32ArrayPool.Return(mOldTextureData);
            mMaker.color32ArrayPool.Return(mNewTextureData);
        }

        public override bool Do()
        {
            return SetTextureData(mNewTextureData);
        }

        public override bool Undo()
        {
            return SetTextureData(mOldTextureData);
        }

        bool SetTextureData(Color32[] textureData)
        {
            if (mLOD >= mMaker.lodCount)
            {
                return false;
            }

            int variationIndex = mMaker.GetVariationIndex(mTileIndex, mVariationInstanceID);
            if (variationIndex < 0)
            {
                return false;
            }
            int width = mDirtyRange.xMax - mDirtyRange.xMin + 1;
            int height = mDirtyRange.yMax - mDirtyRange.yMin + 1;
            mMaker.SetTextureData32(mLOD, mTileIndex, variationIndex, mMaskTextureIndex, mDirtyRange.xMin, mDirtyRange.yMin, width, height, textureData);
            return true;
        }

        Color32[] mOldTextureData;
        Color32[] mNewTextureData;
        GroundTileMaker mMaker;
        int mLOD;
        int mTileIndex;
        int mMaskTextureIndex;
        int mVariationInstanceID;
        RectI mDirtyRange;
    }
}

#endif