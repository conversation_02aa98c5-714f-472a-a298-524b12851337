﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using System.IO;

namespace TFW.Map {
    public class IDExporter {
        public void Export(BinaryWriter writer, int id) {
            if (id == 0) {
                int nullID = 0;
                writer.Write(nullID);
            }
            else {
                int exportID = 0;
                bool found = mIDMapping.TryGetValue(id, out exportID);
                if (!found) {
                    exportID = ++mNextExportObjectID;
                    mIDMapping[id] = exportID;
                }

                writer.Write(exportID);
            }
        }

        int mNextExportObjectID = 0;
        Dictionary<int, int> mIDMapping = new Dictionary<int, int>();
    }
}

#endif