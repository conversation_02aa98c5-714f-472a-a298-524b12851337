﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionCopyPolygonRiver : EditorAction
    {
        public ActionCopyPolygonRiver(int layerID, int sourceRiverID, Vector3 offset)
        {
            mLayerID = layerID;
            mDataID = Map.currentMap.nextCustomObjectID;
            var sourceRiver = Map.currentMap.FindObject(sourceRiverID) as PolygonRiverData;

            var navMeshObstacleOutline = sourceRiver.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mTextureSize = sourceRiver.textureSize;
            mHideLOD = sourceRiver.hideLOD;
            mHeight = sourceRiver.height;
            mGenerateRiverMaterial = sourceRiver.generateRiverMaterial;
            mNavMeshObstacleOutline = new List<Vector3>(navMeshObstacleOutline.Count);
            for (int i = 0; i < navMeshObstacleOutline.Count; ++i)
            {
                mNavMeshObstacleOutline.Add(navMeshObstacleOutline[i] + offset);
            }
            mRiverMaterialPath = sourceRiver.materialPath;

            //sections
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            var sections = sourceRiver.sections;
            for (int i = 0; i < sections.Count; ++i)
            {
                var sectionData = EditorUtils.CreateRiverSectionData(sections[i], layer.GetRiverMaterial(sourceRiverID, i));
                for (int k = 0; k < sectionData.outline.Count; ++k)
                {
                    sectionData.outline[k] += offset;
                }
                mSections.Add(sectionData);
            }

            //splitters
            var splitters = sourceRiver.splitters;
            for (int i = 0; i < splitters.Count; ++i)
            {
                var splitterData = new RiverSplitterData();
                splitterData.startPos = splitters[i].startVertexPosition + offset;
                splitterData.endPos = splitters[i].endVertexPosition + offset;
                mSplitters.Add(splitterData);
            }
        }

        public override void OnDestroy()
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].OnDestroy();
            }
            mSections = null;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }

            //sections
            List<PolygonRiverSectionData> sections = new List<PolygonRiverSectionData>(mSections.Count);
            for (int i = 0; i < mSections.Count; ++i)
            {
                sections.Add(new PolygonRiverSectionData(mSections[i].id, mSections[i].outline, mSections[i].textureData, mTextureSize));
            }

            //splitters
            List<PolygonRiverSplitterData> splitters = new List<PolygonRiverSplitterData>(mSplitters.Count);
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                splitters.Add(new PolygonRiverSplitterData(mSplitters[i].startPos, mSplitters[i].endPos));
            }

            var data = new PolygonRiverData(mDataID, Map.currentMap, mNavMeshObstacleOutline, layer.displayVertexRadius, mTextureSize, mRiverMaterialPath, sections, splitters, mHideLOD, mHeight, mGenerateRiverMaterial);
            layer.AddObject(data);

            //restore material properties
            for (int i = 0; i < mSections.Count; ++i)
            {
                layer.SetRiverMaterialProperties(mDataID, i, mSections[i].materialPropeties);
            }

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        int mTextureSize;
        int mHideLOD;
        float mHeight;
        string mRiverMaterialPath;
        bool mGenerateRiverMaterial;
        List<Vector3> mNavMeshObstacleOutline;
        List<RiverSectionData> mSections = new List<RiverSectionData>();
        List<RiverSplitterData> mSplitters = new List<RiverSplitterData>();
    }
}

#endif