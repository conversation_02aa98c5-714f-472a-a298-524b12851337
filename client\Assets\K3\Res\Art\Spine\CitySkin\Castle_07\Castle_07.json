{"skeleton": {"hash": "jKUK07vDDnU", "spine": "4.2.33", "x": -431.79, "y": -240.32, "width": 850, "height": 775, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "CAR", "parent": "root", "length": 98.57, "rotation": -155.61, "x": 244.63, "y": -51.53}, {"name": "Car", "parent": "CAR", "length": 50, "rotation": -114.39, "x": -30.28, "y": -8.74}, {"name": "car", "parent": "Car", "length": 50, "rotation": -90, "x": 0.67, "y": -4.84}, {"name": "O1", "parent": "CAR", "rotation": 155.61, "x": -0.71, "y": 52.78}, {"name": "O2", "parent": "CAR", "rotation": 155.61, "x": -98.21, "y": 46.42}, {"name": "tree_A", "parent": "root", "x": 42.8, "y": -82.59}, {"name": "tree_A2", "parent": "tree_A", "length": 20.25, "rotation": 87.25, "x": -0.19, "y": 5.06}, {"name": "tree_A3", "parent": "tree_A2", "length": 20.08, "rotation": -1.14, "x": 20.25}, {"name": "tree_A4", "parent": "tree_A3", "length": 23.94, "rotation": 2.02, "x": 20.08}, {"name": "tree_B", "parent": "root", "x": 74.17, "y": -70.75}, {"name": "tree_B2", "parent": "tree_B", "length": 8.61, "rotation": 78.52, "x": 0.44, "y": 1.84}, {"name": "tree_B3", "parent": "tree_B2", "length": 11.97, "rotation": 16.64, "x": 8.61}, {"name": "tree_B4", "parent": "tree_B3", "length": 15.45, "rotation": 8.13, "x": 11.97}, {"name": "tree_LA", "parent": "root", "x": -198.75, "y": -34.26}, {"name": "tree_LA2", "parent": "tree_LA", "length": 14.89, "rotation": 89.18, "y": 3.72}, {"name": "tree_LA3", "parent": "tree_LA2", "length": 22.12, "rotation": 1.37, "x": 14.89}, {"name": "tree_LB", "parent": "root", "x": -271.16, "y": 11.97}, {"name": "tree_LB2", "parent": "tree_LB", "length": 19.35, "rotation": 97.22, "x": -0.38, "y": 4.99}, {"name": "tree_LB3", "parent": "tree_LB2", "length": 21, "rotation": -5.47, "x": 19.35}, {"name": "tree_LB4", "parent": "tree_LB3", "length": 25.22, "rotation": -0.58, "x": 21}, {"name": "tree_LC", "parent": "root", "x": -307.29, "y": 40.12}, {"name": "tree_LC2", "parent": "tree_LC", "length": 26.96, "rotation": 88.87, "x": 0.18, "y": 5.85}, {"name": "tree_LC3", "parent": "tree_LC2", "length": 29.98, "rotation": 2.15, "x": 26.96}, {"name": "tree_LC4", "parent": "tree_LC3", "length": 26.85, "rotation": 3.15, "x": 29.98}, {"name": "tree_LC5", "parent": "tree_LC4", "length": 28.29, "rotation": 0.51, "x": 26.85}, {"name": "tree_R", "parent": "root", "x": 240.52, "y": 24.95}, {"name": "tree_R2", "parent": "tree_R", "length": 24.64, "rotation": 88.12, "y": 5.38}, {"name": "tree_R3", "parent": "tree_R2", "length": 22.37, "rotation": -1.23, "x": 24.64}, {"name": "tree_UpA", "parent": "root", "x": 33.78, "y": 339.45}, {"name": "tree_UpA2", "parent": "tree_UpA", "length": 18.73, "rotation": 94.11, "x": -0.48, "y": 4.12}, {"name": "tree_UpA3", "parent": "tree_UpA2", "length": 21.36, "rotation": 1.3, "x": 18.73}, {"name": "tree_UpB", "parent": "root", "x": -29.21, "y": 304.44}, {"name": "tree_UpB2", "parent": "tree_UpB", "length": 59.19, "rotation": 93.74, "x": -0.5, "y": 2.17}, {"name": "light", "parent": "root", "x": 14.42, "y": 261.46, "scaleX": 0.4259, "scaleY": 0.4259}, {"name": "light2", "parent": "light"}, {"name": "light3", "parent": "light", "rotation": 90}], "slots": [{"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "tree_R", "bone": "tree_R", "attachment": "tree_R"}, {"name": "tree_UpB", "bone": "tree_UpB", "attachment": "tree_UpB"}, {"name": "Castle2", "bone": "root", "attachment": "Castle"}, {"name": "tree_UpA", "bone": "tree_UpA", "attachment": "tree_UpA"}, {"name": "tree_LC", "bone": "tree_LC", "attachment": "tree_LC"}, {"name": "tree_LB", "bone": "tree_LB", "attachment": "tree_LB"}, {"name": "tree_LA", "bone": "tree_LA", "attachment": "tree_LA"}, {"name": "tree_B", "bone": "tree_B", "attachment": "tree_B"}, {"name": "tree_A", "bone": "tree_A", "attachment": "tree_A"}, {"name": "O2", "bone": "O2", "attachment": "O2"}, {"name": "O1", "bone": "O1", "attachment": "O1"}, {"name": "car", "bone": "car", "attachment": "car"}, {"name": "light_O", "bone": "car", "attachment": "light_O", "blend": "additive"}, {"name": "light_O2", "bone": "car", "attachment": "light_O", "blend": "additive"}, {"name": "light", "bone": "light2", "attachment": "light", "blend": "additive"}, {"name": "light2", "bone": "light3", "attachment": "light", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"car": {"car": {"type": "mesh", "uvs": [0.67204, 0, 0.92663, 0.186, 0.98437, 0.2685, 0.99842, 0.32906, 0.98994, 0.47513, 0.87828, 0.66764, 0.60963, 0.8786, 0.48008, 0.99086, 0.3772, 0.99916, 0.24688, 0.95821, 0.08921, 0.83374, 0.00389, 0.69772, 0.00412, 0.5873, 0.04368, 0.48011, 0.12828, 0.3464, 0.2523, 0.29304, 0.28277, 0.26046, 0.27383, 0.22788, 0.29895, 0.20881, 0.32446, 0.21794, 0.41628, 0.1236, 0.53797, 0.01826, 0.36678, 0.89909, 0.47145, 0.7704, 0.63377, 0.5838, 0.90025, 0.32803, 0.06195, 0.69277, 0.24549, 0.84944, 0.27355, 0.34489, 0.52263, 0.56966, 0.36601, 0.70589, 0.14148, 0.52976, 0.80778, 0.23981, 0.57801, 0.0566, 0.43042, 0.16824, 0.65953, 0.35846], "triangles": [33, 21, 0, 34, 20, 21, 34, 21, 33, 19, 20, 34, 32, 0, 1, 33, 0, 32, 16, 17, 18, 25, 32, 1, 25, 1, 2, 25, 2, 3, 28, 15, 16, 16, 18, 19, 28, 16, 19, 35, 33, 32, 34, 33, 35, 4, 25, 3, 28, 31, 14, 28, 14, 15, 13, 14, 31, 29, 34, 35, 28, 19, 34, 29, 28, 34, 24, 29, 35, 25, 24, 35, 25, 35, 32, 5, 24, 25, 5, 25, 4, 26, 13, 31, 12, 13, 26, 11, 12, 26, 30, 28, 29, 31, 28, 30, 23, 30, 29, 23, 29, 24, 27, 10, 26, 31, 27, 26, 11, 26, 10, 30, 27, 31, 6, 23, 24, 6, 24, 5, 22, 30, 23, 27, 30, 22, 9, 27, 22, 10, 27, 9, 7, 23, 6, 22, 23, 7, 8, 22, 7, 9, 22, 8], "vertices": [29.06, 74.07, 89.4, 46.36, 103.09, 34.06, 106.41, 25.04, 104.41, 3.28, 77.94, -25.41, 14.27, -56.84, -16.43, -73.57, -40.81, -74.8, -71.7, -68.7, -109.07, -50.16, -129.29, -29.89, -129.23, -13.44, -119.86, 2.53, -99.81, 22.46, -70.42, 30.41, -63.19, 35.26, -65.31, 40.12, -59.36, 42.96, -53.31, 41.6, -31.55, 55.66, -2.71, 71.35, -43.28, -59.89, -18.48, -40.72, 19.99, -12.92, 83.15, 25.19, -115.53, -29.15, -72.03, -52.5, -65.38, 22.68, -6.35, -10.81, -43.47, -31.11, -96.68, -4.86, 61.23, 38.34, 6.78, 65.64, -28.2, 49, 26.1, 20.66], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 40, 42, 8, 10, 44, 46, 48, 50, 48, 46, 52, 54, 56, 58, 58, 60, 60, 54, 52, 62, 62, 56, 38, 40, 34, 36, 36, 38, 30, 32, 32, 34, 42, 0, 64, 66, 66, 68, 64, 70, 10, 12, 12, 14], "width": 237, "height": 149}}, "Castle": {"Castle": {"type": "mesh", "uvs": [0.52425, 0, 0.83127, 0.21695, 0.84332, 0.50522, 0.79264, 0.53292, 0.79068, 0.57914, 0.99579, 0.72917, 1, 0.73568, 1, 0.74645, 0.55238, 1, 0.54881, 1, 0, 0.61622, 0, 0.60733, 0.05177, 0.53847, 0.14616, 0.5356, 0.17871, 0.29495, 0.1744, 0.09207, 0.51577, 0], "triangles": [14, 15, 16, 3, 0, 1, 3, 1, 2, 0, 3, 16, 10, 11, 12, 5, 6, 7, 14, 3, 13, 4, 8, 9, 7, 4, 5, 3, 14, 16, 13, 3, 4, 9, 13, 4, 10, 12, 13, 9, 10, 13, 7, 8, 4], "vertices": [13.82, 534.68, 274.8, 366.55, 285.03, 143.14, 241.96, 121.67, 240.29, 85.85, 414.63, -30.43, 418.21, -35.48, 418.21, -43.82, 37.74, -240.32, 34.7, -240.32, -431.79, 57.11, -431.79, 64, -387.78, 117.36, -307.55, 119.59, -279.89, 306.1, -283.54, 463.32, 6.62, 534.68], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 850, "height": 775}}, "Castle2": {"Castle": {"type": "mesh", "uvs": [0.47951, 0.31687, 0.48241, 0.12937, 0.83127, 0.21695, 0.84332, 0.50522, 0.79264, 0.53292, 0.78902, 0.57776, 0.78631, 0.63227, 0.79531, 0.6335, 0.79505, 0.71115, 0.53241, 0.74882, 0.33461, 0.4966, 0.35577, 0.22957], "triangles": [0, 1, 2, 10, 11, 0, 4, 0, 2, 3, 4, 2, 4, 9, 0, 0, 9, 10, 9, 4, 5, 8, 6, 7, 6, 9, 5, 9, 6, 8], "vertices": [-24.2, 289.1, -21.74, 434.41, 274.8, 366.55, 285.03, 143.14, 241.96, 121.67, 238.88, 86.91, 236.58, 44.67, 244.23, 43.72, 244.01, -16.46, 20.77, -45.65, -147.37, 149.82, -129.38, 356.76], "hull": 12, "edges": [4, 6, 6, 8, 8, 10, 22, 0, 0, 2, 2, 4, 10, 12, 12, 14, 14, 16, 16, 18, 22, 20, 20, 18], "width": 850, "height": 775}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [143, -4.68, -143, -4.68, -143, 4.68, 143, 4.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 16}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [143, -4.68, -143, -4.68, -143, 4.68, 143, 4.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 16}}, "light_O": {"light_O": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-23.95, -75.23, -75.38, -59.78, -63.65, -20.77, -12.22, -36.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 148}}, "light_O2": {"light_O": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-107.59, -37.65, -147.67, -12.43, -128.53, 17.98, -88.45, -7.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 148}}, "O1": {"O1": {"type": "mesh", "uvs": [0.97908, 0.03444, 0.97935, 0.48932, 0.80627, 0.83897, 0.55399, 0.98253, 0.23891, 0.98159, 0.02151, 0.89082, 0.02131, 0.41066, 0.51506, 0.04413, 0.94861, 0.01038], "triangles": [6, 7, 1, 0, 7, 8, 1, 7, 0, 2, 6, 1, 4, 5, 6, 6, 3, 4, 2, 3, 6], "vertices": [26.18, 49.12, 26.19, 24.56, 18.23, 5.68, 6.62, -2.08, -7.87, -2.03, -17.87, 2.88, -17.88, 28.8, 4.83, 48.6, 24.78, 50.42], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 46, "height": 54}}, "O2": {"O2": {"type": "mesh", "uvs": [0.95144, 1e-05, 0.97457, 0.23858, 0.97813, 0.47717, 0.79444, 0.81607, 0.55238, 0.98194, 0.20883, 0.9825, 0.00037, 0.90937, 0.27024, 0.13469], "triangles": [7, 0, 1, 2, 3, 7, 2, 7, 1, 6, 7, 3, 3, 5, 6, 4, 5, 3], "vertices": [22.12, 51.93, 23, 38.8, 23.14, 25.68, 16.16, 7.04, 6.96, -2.08, -6.09, -2.11, -14.02, 1.91, -3.76, 44.52], "hull": 8, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 2, 4, 0, 2, 14, 0, 12, 14], "width": 38, "height": 55}}, "tree_A": {"tree_A": {"type": "mesh", "uvs": [0.54703, 0, 0.60757, 0, 0.78579, 0.15439, 0.84289, 0.2787, 0.91855, 0.44341, 0.9942, 0.60812, 0.93874, 0.70821, 0.85614, 0.88674, 0.59766, 0.97431, 0.31321, 0.97455, 0.2047, 0.94686, 0.07869, 0.84712, 0.00301, 0.6644, 0.04259, 0.46298, 0.1222, 0.37011, 0.24345, 0.24101, 0.37043, 0.08835, 0.49764, 0.87686, 0.49183, 0.72865, 0.50346, 0.56538, 0.55773, 0.416, 0.53835, 0.26779], "triangles": [20, 15, 21, 21, 2, 3, 2, 16, 0, 0, 1, 2, 15, 16, 21, 2, 21, 16, 6, 19, 5, 19, 20, 4, 5, 19, 4, 12, 13, 19, 13, 14, 19, 19, 14, 20, 15, 20, 14, 20, 3, 4, 20, 21, 3, 6, 7, 18, 11, 18, 17, 6, 18, 19, 18, 7, 17, 11, 12, 18, 18, 12, 19, 9, 17, 8, 9, 10, 17, 8, 17, 7, 10, 11, 17], "vertices": [1, 9, 23.96, 0.34, 1, 1, 9, 24.05, -2.45, 1, 2, 8, 32.89, -10.58, 0.1014, 9, 12.43, -11.03, 0.8986, 3, 7, 43.49, -14.31, 5e-05, 8, 23.52, -13.85, 0.56649, 9, 2.95, -13.96, 0.43346, 3, 7, 30.99, -18.4, 0.09591, 8, 11.1, -18.18, 0.88963, 9, -9.61, -17.85, 0.01446, 3, 6, 23.15, 22.44, 0.00817, 7, 18.49, -22.48, 0.46271, 8, -1.32, -22.51, 0.52912, 3, 6, 20.6, 14.74, 0.05533, 7, 10.67, -20.3, 0.67515, 8, -9.18, -20.49, 0.26952, 3, 6, 16.8, 0.99, 0.37326, 7, -3.24, -17.17, 0.60844, 8, -23.15, -17.63, 0.01831, 2, 6, 4.91, -5.75, 0.95066, 7, -10.55, -5.62, 0.04934, 2, 6, -8.18, -5.77, 0.79854, 7, -11.2, 7.45, 0.20146, 3, 6, -13.17, -3.64, 0.5816, 7, -9.31, 12.54, 0.41769, 8, -29.8, 11.95, 0.00071, 3, 6, -18.96, 4.04, 0.22341, 7, -1.92, 18.7, 0.74735, 8, -22.53, 18.26, 0.02924, 3, 6, -22.44, 18.11, 0.0125, 7, 11.97, 22.85, 0.73271, 8, -8.73, 22.68, 0.25479, 3, 7, 27.55, 21.78, 0.28263, 8, 6.87, 21.92, 0.71577, 9, -12.43, 22.37, 0.00161, 3, 7, 34.87, 18.46, 0.10577, 8, 14.25, 18.75, 0.8405, 9, -5.16, 18.94, 0.05373, 3, 7, 45.07, 13.37, 0.00366, 8, 24.55, 13.86, 0.48671, 9, 4.95, 13.69, 0.50963, 2, 8, 36.67, 8.83, 0.01234, 9, 16.89, 8.24, 0.98766, 2, 6, 0.31, 1.75, 0.68453, 7, -3.28, -0.66, 0.31547, 2, 7, 8.11, 0.15, 0.99992, 8, -12.14, -0.09, 8e-05, 2, 7, 20.69, 0.22, 0.2276, 8, 0.44, 0.23, 0.7724, 1, 8, 12.08, -1.48, 1, 1, 9, 3.33, 0.07, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 4, 6, 6, 8, 8, 10, 26, 28, 28, 30], "width": 46, "height": 77}}, "tree_B": {"tree_B": {"type": "mesh", "uvs": [0.70113, 0.11888, 0.87803, 0.21998, 0.9779, 0.42934, 0.8727, 0.64815, 0.55856, 0.74675, 0.52068, 0.87199, 0.47193, 0.98523, 0.39581, 0.97046, 0.41889, 0.86218, 0.42393, 0.78676, 0.36324, 0.80013, 0.18893, 0.7615, 0.02189, 0.61316, 0.0224, 0.45053, 0.14586, 0.19452, 0.29405, 0.07124, 0.52423, 0.01778, 0.25945, 0.58127, 0.51375, 0.52826, 0.72943, 0.49758, 0.30605, 0.3316, 0.571, 0.28418], "triangles": [17, 13, 20, 18, 20, 21, 19, 21, 1, 1, 21, 0, 13, 14, 20, 14, 15, 20, 20, 15, 21, 15, 16, 21, 21, 16, 0, 10, 11, 17, 9, 17, 18, 11, 12, 17, 4, 19, 3, 4, 18, 19, 3, 19, 2, 12, 13, 17, 17, 20, 18, 18, 21, 19, 19, 1, 2, 6, 8, 5, 8, 9, 5, 5, 9, 4, 9, 10, 17, 9, 18, 4, 7, 8, 6], "vertices": [3, 11, 34.02, -3.75, 0.00198, 12, 23.28, -10.87, 0.19285, 13, 9.65, -12.36, 0.80518, 3, 11, 31.41, -12.22, 0.0207, 12, 18.35, -18.24, 0.47935, 13, 3.73, -18.96, 0.49995, 3, 11, 23.67, -18.28, 0.06149, 12, 9.19, -21.83, 0.6702, 13, -5.84, -21.22, 0.26831, 3, 11, 13.74, -15.57, 0.13952, 12, 0.46, -16.39, 0.75444, 13, -13.72, -14.6, 0.10604, 2, 11, 6.93, -2.85, 0.90623, 12, -2.42, -2.25, 0.09377, 2, 10, 2.95, 2.81, 4e-05, 11, 1.45, -2.26, 0.99996, 2, 10, 0.81, -1.95, 0.9884, 11, -3.64, -1.11, 0.0116, 1, 10, -2.54, -1.33, 1, 4, 10, -1.53, 3.22, 0.05108, 11, 0.96, 2.21, 0.94664, 12, -6.7, 4.31, 0.00225, 13, -17.87, 6.9, 3e-05, 3, 11, 4.11, 2.62, 0.83607, 12, -3.56, 3.8, 0.15805, 13, -14.84, 5.96, 0.00588, 3, 11, 3.03, 5.13, 0.54447, 12, -3.88, 6.51, 0.4281, 13, -14.77, 8.69, 0.02743, 3, 11, 3.09, 12.96, 0.18007, 12, -1.57, 14, 0.67574, 13, -11.43, 15.78, 0.14419, 3, 11, 7.73, 21.41, 0.04246, 12, 5.29, 20.76, 0.61436, 13, -3.68, 21.5, 0.34317, 3, 11, 14.43, 22.74, 0.01226, 12, 12.09, 20.12, 0.4932, 13, 2.96, 19.9, 0.49453, 2, 12, 22.31, 13.74, 0.11913, 13, 12.18, 12.15, 0.88087, 2, 12, 26.88, 6.78, 0.00421, 13, 15.72, 4.61, 0.99579, 2, 12, 28.21, -3.5, 0.00931, 13, 15.57, -5.76, 0.99069, 3, 11, 11.13, 11.43, 0.06627, 12, 5.69, 10.23, 0.70426, 13, -4.78, 11.02, 0.22947, 3, 11, 15.53, 0.91, 0.00307, 12, 6.9, -1.11, 0.99097, 13, -5.18, -0.38, 0.00595, 3, 11, 18.68, -8.14, 0.08111, 12, 7.32, -10.68, 0.74215, 13, -6.11, -9.92, 0.17674, 2, 12, 15.94, 7.24, 0.16818, 13, 4.96, 6.61, 0.83182, 3, 11, 26.08, 0.48, 0.00087, 12, 16.88, -4.55, 0.1708, 13, 4.21, -5.19, 0.82833], "hull": 17, "edges": [2, 4, 4, 6, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 22, 24, 20, 22, 18, 20, 8, 10, 10, 12, 14, 16, 16, 18, 6, 8, 2, 0, 0, 32], "width": 44, "height": 42}}, "tree_LA": {"tree_LA": {"type": "mesh", "uvs": [0.45837, 0.02103, 0.62244, 0.01919, 0.89124, 0.2018, 0.9447, 0.37722, 0.98837, 0.52054, 0.86489, 0.63883, 0.74678, 0.67466, 0.63143, 0.58441, 0.60786, 0.76283, 0.6091, 0.96163, 0.49647, 0.96507, 0.50368, 0.77294, 0.50722, 0.58649, 0.45529, 0.69854, 0.35053, 0.63855, 0.18152, 0.63907, 0.10011, 0.47481, 0.00256, 0.27797, 0.16237, 0.12885, 0.41642, 0.45373, 0.60119, 0.24932, 0.30312, 0.27436, 0.67963, 0.45127], "triangles": [20, 0, 1, 20, 1, 2, 21, 18, 0, 21, 0, 20, 17, 18, 21, 22, 20, 2, 22, 2, 3, 19, 21, 20, 19, 20, 22, 16, 17, 21, 16, 21, 19, 22, 3, 4, 22, 12, 19, 19, 15, 16, 5, 22, 4, 7, 22, 5, 7, 12, 22, 14, 19, 12, 14, 15, 19, 6, 7, 5, 13, 14, 12, 8, 12, 7, 8, 11, 12, 11, 8, 9, 10, 11, 9], "vertices": [2, 15, 39.17, 4.26, 0.00011, 16, 24.38, 3.68, 0.99989, 1, 16, 24.4, -2.88, 1, 2, 15, 30.93, -13.17, 0.00914, 16, 15.72, -13.55, 0.99086, 2, 15, 22.71, -15.43, 0.10862, 16, 7.45, -15.61, 0.89138, 2, 15, 16, -17.27, 0.2439, 16, 0.7, -17.29, 0.7561, 2, 15, 10.37, -12.41, 0.39722, 16, -4.81, -12.3, 0.60278, 2, 15, 8.62, -7.71, 0.4928, 16, -6.45, -7.56, 0.5072, 2, 15, 12.8, -3.04, 0.75022, 16, -2.17, -2.99, 0.24978, 1, 15, 4.4, -2.21, 1, 2, 14, 2.33, -1.25, 0.98073, 15, -4.94, -2.4, 0.01927, 2, 14, -2.18, -1.42, 0.99742, 15, -5.17, 2.11, 0.00258, 1, 15, 3.86, 1.95, 1, 2, 15, 12.63, 1.93, 0.93242, 16, -2.22, 1.98, 0.06758, 1, 15, 7.33, 3.93, 1, 2, 15, 10.09, 8.16, 0.79046, 16, -4.6, 8.27, 0.20954, 2, 15, 9.97, 14.92, 0.57988, 16, -4.56, 15.03, 0.42012, 2, 15, 17.64, 18.29, 0.32646, 16, 3.19, 18.22, 0.67354, 2, 15, 26.84, 22.32, 0.13155, 16, 12.48, 22.03, 0.86845, 2, 15, 33.94, 16.03, 0.05136, 16, 19.42, 15.57, 0.94864, 2, 15, 18.82, 5.65, 0.19489, 16, 4.06, 5.56, 0.80511, 1, 16, 13.59, -1.93, 1, 2, 15, 27.18, 10.3, 0.06643, 16, 12.53, 10.01, 0.93357, 2, 15, 19.08, -4.87, 0.08384, 16, 4.07, -4.97, 0.91616], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 8, 10, 16, 18, 18, 20, 20, 22, 28, 30, 34, 36, 10, 12, 12, 14, 14, 16, 26, 28, 22, 24, 24, 26, 30, 32, 32, 34, 4, 6, 6, 8], "width": 40, "height": 47}}, "tree_LB": {"tree_LB": {"type": "mesh", "uvs": [0.55911, 0.02526, 0.59075, 0.04367, 0.67619, 0.14873, 0.85023, 0.29227, 0.97741, 0.47439, 0.97806, 0.69237, 0.7439, 0.75691, 0.5966, 0.71098, 0.63985, 0.84623, 0.64557, 0.9804, 0.55755, 0.97474, 0.52813, 0.83862, 0.46514, 0.70577, 0.2536, 0.76004, 0.02263, 0.58952, 0.02271, 0.45937, 0.09293, 0.33297, 0.22335, 0.20228, 0.35378, 0.07158, 0.46219, 0.02549, 0.52339, 0.48097, 0.50943, 0.32808, 0.54135, 0.61627], "triangles": [20, 21, 3, 16, 17, 21, 21, 17, 2, 2, 17, 18, 21, 2, 3, 1, 18, 19, 1, 19, 0, 2, 18, 1, 7, 22, 5, 12, 14, 22, 14, 20, 22, 20, 14, 15, 20, 16, 21, 16, 20, 15, 22, 4, 5, 22, 20, 4, 20, 3, 4, 10, 11, 8, 8, 11, 7, 11, 12, 7, 13, 14, 12, 6, 7, 5, 12, 22, 7, 10, 8, 9], "vertices": [1, 20, 27.51, -2.43, 1, 1, 20, 26.05, -3.76, 1, 2, 19, 38.71, -7.45, 0.00684, 20, 17.78, -7.27, 0.99316, 3, 18, 45.12, -17.12, 0.00079, 19, 27.29, -14.59, 0.37923, 20, 6.43, -14.52, 0.61998, 3, 18, 30.34, -20.76, 0.09992, 19, 12.92, -19.62, 0.83691, 20, -7.88, -19.7, 0.06317, 2, 18, 13.47, -18.66, 0.51535, 19, -4.07, -19.13, 0.48465, 2, 18, 9.74, -8.03, 0.77065, 19, -8.8, -8.91, 0.22935, 2, 18, 14.09, -2.2, 0.96252, 19, -5.03, -2.69, 0.03748, 1, 18, 3.39, -2.72, 1, 1, 17, 2.13, -1.76, 1, 1, 17, -1.65, -1.32, 1, 1, 18, 4.59, 1.97, 1, 2, 18, 15.21, 3.36, 0.89626, 19, -4.45, 2.95, 0.10374, 2, 18, 12.15, 12.91, 0.54003, 19, -8.4, 12.17, 0.45997, 3, 18, 26.59, 21.09, 0.1299, 19, 5.2, 21.69, 0.85431, 20, -16.03, 21.53, 0.01579, 3, 18, 36.67, 19.82, 0.02814, 19, 15.34, 21.38, 0.83301, 20, -5.88, 21.32, 0.13885, 3, 18, 46.07, 15.58, 0.00044, 19, 25.11, 18.06, 0.50864, 20, 3.92, 18.1, 0.49093, 2, 19, 35.12, 12.14, 0.07188, 20, 14, 12.28, 0.92812, 1, 20, 24.08, 6.47, 1, 1, 20, 27.58, 1.74, 1, 3, 18, 32.29, -1.33, 4e-05, 19, 13, -0.09, 0.99994, 20, -8, -0.17, 2e-05, 2, 19, 24.94, 0.15, 0.00025, 20, 3.94, 0.19, 0.99975, 2, 18, 21.72, -0.77, 0.01349, 19, 2.43, -0.54, 0.98651], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 26, 28, 28, 30, 30, 32, 36, 38, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36], "width": 43, "height": 78}}, "tree_LC": {"tree_LC": {"type": "mesh", "uvs": [0.4645, 0.00878, 0.58771, 0.06294, 0.66741, 0.16432, 0.7712, 0.27354, 0.89156, 0.38266, 0.9876, 0.57363, 0.85487, 0.72719, 0.68545, 0.76464, 0.57162, 0.76256, 0.56481, 0.85502, 0.55571, 0.97871, 0.50925, 0.99174, 0.44852, 0.99096, 0.48104, 0.86575, 0.4805, 0.7571, 0.14914, 0.78694, 0.06625, 0.71423, 0, 0.53849, 0, 0.53739, 0.08294, 0.38874, 0.16587, 0.2401, 0.26803, 0.13242, 0.38261, 0.00667, 0.53504, 0.66577, 0.52698, 0.54106, 0.52967, 0.38877, 0.47324, 0.26165], "triangles": [1, 21, 0, 2, 21, 1, 21, 22, 0, 2, 26, 21, 26, 2, 3, 25, 26, 3, 25, 3, 4, 20, 21, 26, 19, 20, 26, 19, 26, 25, 24, 19, 25, 4, 24, 25, 24, 17, 18, 24, 18, 19, 24, 4, 5, 23, 24, 5, 23, 16, 17, 23, 17, 24, 6, 23, 5, 14, 16, 23, 6, 7, 23, 8, 14, 23, 23, 7, 8, 15, 16, 14, 9, 14, 8, 13, 14, 9, 10, 13, 9, 12, 13, 10, 11, 12, 10], "vertices": [1, 25, 26.89, -1.56, 1, 1, 25, 19.82, -7.66, 1, 2, 24, 34.19, -10.88, 0.12295, 25, 7.24, -10.95, 0.87705, 3, 23, 51.4, -14.36, 0.01575, 24, 20.6, -15.51, 0.88514, 25, -6.39, -15.46, 0.09911, 2, 23, 38.08, -20.62, 0.30169, 24, 6.96, -21.04, 0.69831, 3, 22, 42.79, -24.82, 0.02814, 23, 14.89, -25.4, 0.91963, 24, -16.46, -24.53, 0.05223, 2, 22, 24.08, -18.02, 0.38085, 23, -3.56, -17.9, 0.61915, 2, 22, 19.36, -8.96, 0.74056, 23, -7.93, -8.67, 0.25944, 2, 22, 19.49, -2.81, 0.97352, 23, -7.57, -2.53, 0.02648, 1, 22, 8.3, -2.67, 1, 2, 21, 2.52, -0.87, 0.97857, 22, -6.67, -2.47, 0.02143, 1, 21, 0.01, -2.44, 1, 1, 21, -3.27, -2.35, 1, 1, 22, 6.91, 1.83, 1, 2, 22, 20.06, 2.12, 0.99684, 23, -6.82, 2.38, 0.00316, 2, 22, 16.09, 19.94, 0.62774, 23, -10.12, 20.33, 0.37226, 3, 22, 24.8, 24.59, 0.45241, 23, -1.24, 24.65, 0.54621, 24, -29.82, 26.33, 0.00139, 3, 22, 45.99, 28.58, 0.05087, 23, 20.08, 27.85, 0.79395, 24, -8.35, 28.35, 0.15518, 3, 22, 46.13, 28.58, 0.0503, 23, 20.22, 27.85, 0.79314, 24, -8.22, 28.34, 0.15656, 4, 22, 64.2, 24.46, 0.00018, 23, 38.12, 23.05, 0.32436, 24, 9.4, 22.57, 0.67156, 25, -17.26, 22.72, 0.0039, 3, 23, 56.03, 18.25, 0.01149, 24, 27.01, 16.79, 0.62435, 25, 0.31, 16.79, 0.36416, 2, 24, 39.6, 10.35, 0.03532, 25, 12.84, 10.23, 0.96468, 1, 25, 27.5, 2.83, 1, 2, 22, 31.16, -0.61, 0.00381, 23, 4.17, -0.77, 0.99619, 1, 23, 19.27, -0.6, 1, 2, 23, 37.69, -1.07, 0.00107, 24, 7.64, -1.49, 0.99893, 3, 23, 53.12, 1.7, 1e-05, 24, 23.2, 0.43, 0.99887, 25, -3.65, 0.46, 0.00112], "hull": 23, "edges": [0, 44, 0, 2, 8, 10, 10, 12, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 2, 4, 6, 8, 4, 6, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 24, 26], "width": 54, "height": 121}}, "tree_R": {"tree_R": {"type": "mesh", "uvs": [0.56195, 0.0167, 0.76899, 0.03354, 0.87147, 0.264, 1, 0.48372, 0.9307, 0.68367, 0.83799, 0.80521, 0.49707, 0.98409, 0.14433, 0.88449, 0.01182, 0.65088, 0.03836, 0.39711, 0.05839, 0.16675, 0.25295, 0.09083, 0.33722, 0.65363, 0.68807, 0.63319, 0.33861, 0.37327, 0.64352, 0.33823], "triangles": [13, 14, 15, 14, 13, 12, 5, 13, 4, 4, 13, 3, 8, 9, 12, 12, 9, 14, 3, 15, 2, 3, 13, 15, 9, 10, 14, 10, 11, 14, 14, 0, 15, 14, 11, 0, 15, 1, 2, 15, 0, 1, 7, 12, 6, 6, 12, 13, 6, 13, 5, 7, 8, 12], "vertices": [1, 28, 25.28, -3.69, 1, 1, 28, 25.07, -12.62, 1, 2, 27, 40.13, -17.87, 0.00085, 28, 15.87, -17.53, 0.99915, 2, 27, 31.31, -23.68, 0.04262, 28, 7.18, -23.54, 0.95738, 2, 27, 23.02, -20.97, 0.1661, 28, -1.17, -21, 0.8339, 2, 27, 17.91, -17.15, 0.34218, 28, -6.36, -17.29, 0.65782, 2, 27, 10.1, -2.74, 0.9862, 28, -14.48, -3.05, 0.0138, 2, 27, 13.68, 12.55, 0.93423, 28, -11.22, 12.31, 0.06577, 2, 27, 23.07, 18.56, 0.58629, 28, -1.97, 18.52, 0.41371, 2, 27, 33.5, 17.76, 0.18368, 28, 8.48, 17.95, 0.81632, 2, 27, 42.97, 17.21, 0.02659, 28, 17.96, 17.6, 0.97341, 2, 27, 46.36, 8.95, 0.00082, 28, 21.52, 9.41, 0.99918, 2, 27, 23.41, 4.57, 0.72393, 28, -1.32, 4.54, 0.27607, 2, 27, 24.74, -10.48, 0.22849, 28, 0.33, -10.47, 0.77151, 2, 27, 34.9, 4.89, 0.01718, 28, 10.16, 5.11, 0.98282, 1, 28, 12.3, -7.91, 1], "hull": 12, "edges": [0, 22, 0, 2, 8, 10, 10, 12, 12, 14, 14, 16, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 20, 22], "width": 43, "height": 41}}, "tree_UpA": {"tree_UpA": {"type": "mesh", "uvs": [0.57235, 0.00068, 0.76205, 0.02071, 0.94853, 0.20965, 0.98504, 0.34316, 0.93872, 0.60102, 0.74149, 0.70233, 0.58201, 0.80728, 0.5797, 0.97691, 0.54118, 0.98507, 0.48705, 0.82866, 0.32973, 0.76307, 0.02957, 0.6014, 0.01651, 0.5249, 0.0977, 0.33591, 0.16316, 0.18354, 0.30545, 0.03869, 0.31885, 0.5456, 0.67949, 0.53731, 0.33694, 0.29821, 0.68311, 0.24984], "triangles": [19, 0, 1, 19, 1, 2, 18, 15, 0, 18, 0, 19, 14, 15, 18, 13, 14, 18, 17, 18, 19, 16, 13, 18, 19, 3, 17, 3, 19, 2, 16, 18, 17, 12, 13, 16, 4, 17, 3, 11, 12, 16, 5, 17, 4, 16, 9, 10, 17, 6, 16, 11, 16, 10, 5, 6, 17, 6, 9, 16, 7, 9, 6, 8, 9, 7], "vertices": [2, 30, 40.87, -4.33, 0.00598, 31, 22.04, -4.83, 0.99402, 2, 30, 39.17, -14.67, 0.08933, 31, 20.1, -15.13, 0.91067, 2, 30, 29.39, -24.25, 0.34892, 31, 10.1, -24.49, 0.65108, 2, 30, 22.85, -25.8, 0.47867, 31, 3.54, -25.88, 0.52133, 2, 30, 10.69, -22.37, 0.74414, 31, -8.55, -22.18, 0.25586, 2, 30, 6.61, -11.2, 0.946, 31, -12.37, -10.92, 0.054, 1, 30, 2.22, -2.09, 1, 1, 29, 1.32, -1.66, 1, 1, 29, -0.8, -2.05, 1, 2, 29, -3.77, 5.45, 0.0007, 30, 1.57, 3.19, 0.9993, 2, 30, 5.33, 11.6, 0.94786, 31, -13.14, 11.9, 0.05214, 2, 30, 14.25, 27.51, 0.61496, 31, -3.86, 27.6, 0.38504, 2, 30, 17.96, 27.96, 0.58735, 31, -0.13, 27.97, 0.41265, 2, 30, 26.69, 22.86, 0.3731, 31, 8.48, 22.67, 0.6269, 2, 30, 33.73, 18.74, 0.16, 31, 15.42, 18.4, 0.84, 2, 30, 40.1, 10.44, 0.01796, 31, 21.61, 9.95, 0.98204, 2, 30, 15.78, 11.45, 0.71697, 31, -2.69, 11.51, 0.28303, 2, 30, 14.76, -8.37, 0.84098, 31, -4.16, -8.27, 0.15902, 2, 30, 27.55, 9.6, 0.13142, 31, 9.04, 9.4, 0.86858, 2, 30, 28.51, -9.55, 0.14938, 31, 9.56, -9.77, 0.85062], "hull": 16, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 28, 30, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 2, 0, 0, 30], "width": 55, "height": 48}}, "tree_UpB": {"tree_UpB": {"type": "mesh", "uvs": [0.65511, 0.039, 0.88853, 0.11051, 0.94579, 0.20251, 0.97823, 0.28285, 0.99138, 0.42643, 0.96775, 0.63957, 0.94412, 0.85272, 0.80825, 0.89369, 0.5632, 0.91906, 0.4751, 0.91962, 0.34153, 0.99017, 0.20578, 0.97284, 0.1093, 0.90597, 0.02983, 0.77419, 0.00885, 0.5233, 0.04428, 0.3488, 0.1333, 0.193, 0.30655, 0.04506, 0.48679, 0.00494, 0.25668, 0.73617, 0.53688, 0.66109, 0.78891, 0.64762, 0.78734, 0.37427, 0.51184, 0.33769, 0.27233, 0.40507], "triangles": [13, 14, 19, 19, 14, 24, 24, 14, 15, 5, 21, 4, 4, 21, 22, 22, 3, 4, 15, 16, 24, 24, 17, 23, 24, 16, 17, 23, 0, 22, 22, 2, 3, 22, 1, 2, 22, 0, 1, 17, 18, 23, 23, 18, 0, 24, 23, 20, 20, 22, 21, 20, 23, 22, 11, 12, 19, 12, 13, 19, 9, 10, 19, 10, 11, 19, 9, 19, 20, 8, 21, 7, 8, 20, 21, 19, 24, 20, 7, 21, 6, 6, 21, 5, 8, 9, 20], "vertices": [1, 33, 57.38, -14.3, 1, 2, 32, 31.28, 55.06, 3e-05, 33, 50.71, -35.15, 0.99997, 2, 32, 36.49, 48.25, 0.00148, 33, 43.58, -39.91, 0.99852, 2, 32, 39.44, 42.31, 0.00471, 33, 37.45, -42.47, 0.99529, 2, 32, 40.63, 31.68, 0.01797, 33, 26.77, -42.97, 0.98203, 2, 32, 38.48, 15.91, 0.07312, 33, 11.17, -39.8, 0.92688, 2, 32, 36.33, 0.14, 0.14403, 33, -4.43, -36.62, 0.85597, 2, 32, 23.97, -2.89, 0.21145, 33, -6.64, -24.09, 0.78855, 2, 32, 1.67, -4.77, 0.97739, 33, -7.06, -1.71, 0.02261, 2, 32, -6.35, -4.81, 0.73244, 33, -6.58, 6.29, 0.26756, 2, 32, -18.5, -10.03, 0.43349, 33, -11, 18.76, 0.56651, 2, 32, -30.86, -8.75, 0.32197, 33, -8.92, 31, 0.67803, 2, 32, -39.64, -3.8, 0.25068, 33, -3.41, 39.44, 0.74932, 2, 32, -46.87, 5.95, 0.17872, 33, 6.8, 46.02, 0.82128, 2, 32, -48.78, 24.51, 0.08089, 33, 25.45, 46.72, 0.91911, 2, 32, -45.55, 37.43, 0.03723, 33, 38.12, 42.66, 0.96277, 2, 32, -37.45, 48.96, 0.01123, 33, 49.1, 33.82, 0.98877, 2, 32, -21.69, 59.9, 0.00012, 33, 59, 17.38, 0.99988, 1, 33, 60.89, 0.82, 1, 2, 32, -26.22, 8.76, 0.17835, 33, 8.26, 25.24, 0.82165, 2, 32, -0.72, 14.32, 0.00033, 33, 12.14, -0.57, 0.99967, 2, 32, 22.21, 15.31, 0.07832, 33, 11.64, -23.52, 0.92168, 2, 32, 22.07, 35.54, 0.00567, 33, 31.83, -24.69, 0.99433, 1, 33, 36.17, 0.15, 1, 2, 32, -24.8, 33.26, 0.01864, 33, 32.62, 22.22, 0.98136], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 8, 10, 10, 12], "width": 91, "height": 74}}}}], "animations": {"idle": {"slots": {"light_O": {"rgba": [{"color": "ffffffff", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 0.59]}, {"time": 0.6667, "color": "ffffff96", "curve": [0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 0.59, 1.111, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 0.59]}, {"time": 2, "color": "ffffff96", "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 0.59, 2.444, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 0.59]}, {"time": 3.3333, "color": "ffffff96", "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 0.59, 3.778, 1]}, {"time": 4, "color": "ffffffff", "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.59]}, {"time": 4.6667, "color": "ffffff96", "curve": [4.779, 1, 4.89, 1, 4.779, 1, 4.89, 1, 4.779, 1, 4.89, 1, 4.779, 0.59, 4.884, 0.79]}, {"time": 5, "color": "ffffffca", "curve": [5.027, 1, 5.04, 1, 5.027, 1, 5.04, 1, 5.027, 1, 5.04, 1, 5.022, 0.79, 5.044, 1]}, {"time": 5.0667, "color": "ffffffff", "curve": [5.192, 1, 4.956, 1, 5.192, 1, 4.956, 1, 5.192, 1, 4.956, 1, 5.089, 1, 5.111, 0.39]}, {"time": 5.1333, "color": "ffffff64", "curve": [5.223, 1, 5.174, 1, 5.223, 1, 5.174, 1, 5.223, 1, 5.174, 1, 5.156, 0.39, 5.178, 1]}, {"time": 5.2, "color": "ffffffff", "curve": [5.326, 1, 5.089, 1, 5.326, 1, 5.089, 1, 5.326, 1, 5.089, 1, 5.222, 1, 5.244, 0.39]}, {"time": 5.2667, "color": "ffffff64", "curve": [5.341, 1, 5.307, 1, 5.341, 1, 5.307, 1, 5.341, 1, 5.307, 1, 5.289, 0.39, 5.311, 1]}, {"time": 5.3333, "color": "ffffffff", "curve": [5.459, 1, 5.222, 1, 5.459, 1, 5.222, 1, 5.459, 1, 5.222, 1, 5.356, 1, 5.378, 0.39]}, {"time": 5.4, "color": "ffffff64", "curve": [5.475, 1, 5.445, 1, 5.475, 1, 5.445, 1, 5.475, 1, 5.445, 1, 5.422, 0.39, 5.444, 1]}, {"time": 5.4667, "color": "ffffffff", "curve": [5.772, 1, 5.27, 1, 5.772, 1, 5.27, 1, 5.772, 1, 5.27, 1, 5.5, 1, 5.533, 0.88]}, {"time": 5.5667, "color": "ffffffe1", "curve": [5.713, 1, 5.856, 1, 5.713, 1, 5.856, 1, 5.713, 1, 5.856, 1, 5.719, 0.88, 5.856, 0.59]}, {"time": 6, "color": "ffffff96", "curve": [6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 0.59, 6.444, 1]}, {"time": 6.6667, "color": "ffffffff", "curve": [6.779, 1, 6.89, 1, 6.779, 1, 6.89, 1, 6.779, 1, 6.89, 1, 6.779, 1, 6.884, 0.79]}, {"time": 7, "color": "ffffffca", "curve": [7.113, 1, 7.04, 1, 7.113, 1, 7.04, 1, 7.113, 1, 7.04, 1, 7.022, 0.79, 7.044, 1]}, {"time": 7.0667, "color": "ffffffff", "curve": [7.192, 1, 6.956, 1, 7.192, 1, 6.956, 1, 7.192, 1, 6.956, 1, 7.089, 1, 7.111, 0.39]}, {"time": 7.1333, "color": "ffffff64", "curve": [7.223, 1, 7.174, 1, 7.223, 1, 7.174, 1, 7.223, 1, 7.174, 1, 7.156, 0.39, 7.178, 1]}, {"time": 7.2, "color": "ffffffff", "curve": [7.326, 1, 7.089, 1, 7.326, 1, 7.089, 1, 7.326, 1, 7.089, 1, 7.222, 1, 7.244, 0.39]}, {"time": 7.2667, "color": "ffffff64", "curve": [7.341, 1, 7.307, 1, 7.341, 1, 7.307, 1, 7.341, 1, 7.307, 1, 7.289, 0.39, 7.311, 1]}, {"time": 7.3333, "color": "ffffffff", "curve": [7.459, 1, 7.222, 1, 7.459, 1, 7.222, 1, 7.459, 1, 7.222, 1, 7.356, 1, 7.378, 0.39]}, {"time": 7.4, "color": "ffffff64", "curve": [7.475, 1, 7.445, 1, 7.475, 1, 7.445, 1, 7.475, 1, 7.445, 1, 7.422, 0.39, 7.444, 1]}, {"time": 7.4667, "color": "ffffffff", "curve": [7.772, 1, 7.448, 1, 7.772, 1, 7.448, 1, 7.772, 1, 7.448, 1, 7.5, 1, 7.533, 0.71]}, {"time": 7.5667, "color": "ffffffb4", "curve": [7.713, 1, 7.856, 1, 7.713, 1, 7.856, 1, 7.713, 1, 7.856, 1, 7.719, 0.71, 7.856, 1]}, {"time": 8, "color": "ffffffff", "curve": [8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 0.59]}, {"time": 8.6667, "color": "ffffff96", "curve": [8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 0.59, 9.111, 1]}, {"time": 9.3333, "color": "ffffffff", "curve": [9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 0.59]}, {"time": 10, "color": "ffffff96", "curve": [10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 0.59, 10.444, 1]}, {"time": 10.6667, "color": "ffffffff"}]}, "light_O2": {"rgba": [{"color": "ffffffff", "curve": [0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 1, 0.222, 1, 0.444, 0.59]}, {"time": 0.6667, "color": "ffffff96", "curve": [0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 1, 1.111, 1, 0.889, 0.59, 1.111, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 1, 1.556, 1, 1.778, 0.59]}, {"time": 2, "color": "ffffff96", "curve": [2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 1, 2.444, 1, 2.222, 0.59, 2.444, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 1, 2.889, 1, 3.111, 0.59]}, {"time": 3.3333, "color": "ffffff96", "curve": [3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 1, 3.778, 1, 3.556, 0.59, 3.778, 1]}, {"time": 4, "color": "ffffffff", "curve": [4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 1, 4.222, 1, 4.444, 0.59]}, {"time": 4.6667, "color": "ffffff96", "curve": [4.779, 1, 4.89, 1, 4.779, 1, 4.89, 1, 4.779, 1, 4.89, 1, 4.779, 0.59, 4.884, 0.79]}, {"time": 5, "color": "ffffffca", "curve": [5.027, 1, 5.04, 1, 5.027, 1, 5.04, 1, 5.027, 1, 5.04, 1, 5.022, 0.79, 5.044, 1]}, {"time": 5.0667, "color": "ffffffff", "curve": [5.192, 1, 4.956, 1, 5.192, 1, 4.956, 1, 5.192, 1, 4.956, 1, 5.089, 1, 5.111, 0.39]}, {"time": 5.1333, "color": "ffffff64", "curve": [5.223, 1, 5.174, 1, 5.223, 1, 5.174, 1, 5.223, 1, 5.174, 1, 5.156, 0.39, 5.178, 1]}, {"time": 5.2, "color": "ffffffff", "curve": [5.326, 1, 5.089, 1, 5.326, 1, 5.089, 1, 5.326, 1, 5.089, 1, 5.222, 1, 5.244, 0.39]}, {"time": 5.2667, "color": "ffffff64", "curve": [5.341, 1, 5.307, 1, 5.341, 1, 5.307, 1, 5.341, 1, 5.307, 1, 5.289, 0.39, 5.311, 1]}, {"time": 5.3333, "color": "ffffffff", "curve": [5.459, 1, 5.222, 1, 5.459, 1, 5.222, 1, 5.459, 1, 5.222, 1, 5.356, 1, 5.378, 0.39]}, {"time": 5.4, "color": "ffffff64", "curve": [5.475, 1, 5.445, 1, 5.475, 1, 5.445, 1, 5.475, 1, 5.445, 1, 5.422, 0.39, 5.444, 1]}, {"time": 5.4667, "color": "ffffffff", "curve": [5.772, 1, 5.27, 1, 5.772, 1, 5.27, 1, 5.772, 1, 5.27, 1, 5.5, 1, 5.533, 0.88]}, {"time": 5.5667, "color": "ffffffe1", "curve": [5.713, 1, 5.856, 1, 5.713, 1, 5.856, 1, 5.713, 1, 5.856, 1, 5.719, 0.88, 5.856, 0.59]}, {"time": 6, "color": "ffffff96", "curve": [6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 1, 6.444, 1, 6.222, 0.59, 6.444, 1]}, {"time": 6.6667, "color": "ffffffff", "curve": [6.779, 1, 6.89, 1, 6.779, 1, 6.89, 1, 6.779, 1, 6.89, 1, 6.779, 1, 6.884, 0.79]}, {"time": 7, "color": "ffffffca", "curve": [7.113, 1, 7.04, 1, 7.113, 1, 7.04, 1, 7.113, 1, 7.04, 1, 7.022, 0.79, 7.044, 1]}, {"time": 7.0667, "color": "ffffffff", "curve": [7.192, 1, 6.956, 1, 7.192, 1, 6.956, 1, 7.192, 1, 6.956, 1, 7.089, 1, 7.111, 0.39]}, {"time": 7.1333, "color": "ffffff64", "curve": [7.223, 1, 7.174, 1, 7.223, 1, 7.174, 1, 7.223, 1, 7.174, 1, 7.156, 0.39, 7.178, 1]}, {"time": 7.2, "color": "ffffffff", "curve": [7.326, 1, 7.089, 1, 7.326, 1, 7.089, 1, 7.326, 1, 7.089, 1, 7.222, 1, 7.244, 0.39]}, {"time": 7.2667, "color": "ffffff64", "curve": [7.341, 1, 7.307, 1, 7.341, 1, 7.307, 1, 7.341, 1, 7.307, 1, 7.289, 0.39, 7.311, 1]}, {"time": 7.3333, "color": "ffffffff", "curve": [7.459, 1, 7.222, 1, 7.459, 1, 7.222, 1, 7.459, 1, 7.222, 1, 7.356, 1, 7.378, 0.39]}, {"time": 7.4, "color": "ffffff64", "curve": [7.475, 1, 7.445, 1, 7.475, 1, 7.445, 1, 7.475, 1, 7.445, 1, 7.422, 0.39, 7.444, 1]}, {"time": 7.4667, "color": "ffffffff", "curve": [7.772, 1, 7.448, 1, 7.772, 1, 7.448, 1, 7.772, 1, 7.448, 1, 7.5, 1, 7.533, 0.71]}, {"time": 7.5667, "color": "ffffffb4", "curve": [7.713, 1, 7.856, 1, 7.713, 1, 7.856, 1, 7.713, 1, 7.856, 1, 7.719, 0.71, 7.856, 1]}, {"time": 8, "color": "ffffffff", "curve": [8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 1, 8.222, 1, 8.444, 0.59]}, {"time": 8.6667, "color": "ffffff96", "curve": [8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 1, 9.111, 1, 8.889, 0.59, 9.111, 1]}, {"time": 9.3333, "color": "ffffffff", "curve": [9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 1, 9.556, 1, 9.778, 0.59]}, {"time": 10, "color": "ffffff96", "curve": [10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 1, 10.444, 1, 10.222, 0.59, 10.444, 1]}, {"time": 10.6667, "color": "ffffffff"}]}}, "bones": {"car": {"rotate": [{"time": 5, "curve": [5.078, 0, 5.156, 0.77]}, {"time": 5.2333, "value": 0.77, "curve": [5.344, 0.77, 5.456, 0]}, {"time": 5.5667, "curve": "stepped"}, {"time": 7, "curve": [7.078, 0, 7.156, -0.72]}, {"time": 7.2333, "value": -0.72, "curve": [7.344, -0.72, 7.456, 0]}, {"time": 7.5667}]}, "tree_A2": {"rotate": [{"value": 2.41, "curve": [0.444, 2.41, 0.889, -2.89]}, {"time": 1.3333, "value": -2.89, "curve": [1.778, -2.89, 2.222, 2.41]}, {"time": 2.6667, "value": 2.41, "curve": [3.111, 2.41, 3.556, -2.89]}, {"time": 4, "value": -2.89, "curve": [4.444, -2.89, 4.889, 2.41]}, {"time": 5.3333, "value": 2.41, "curve": [5.778, 2.41, 6.222, -2.89]}, {"time": 6.6667, "value": -2.89, "curve": [7.111, -2.89, 7.556, 2.41]}, {"time": 8, "value": 2.41, "curve": [8.444, 2.41, 8.889, -2.89]}, {"time": 9.3333, "value": -2.89, "curve": [9.778, -2.89, 10.222, 2.41]}, {"time": 10.6667, "value": 2.41}]}, "tree_A3": {"rotate": [{"value": 1.56, "curve": [0.114, 2.05, 0.224, 2.41]}, {"time": 0.3333, "value": 2.41, "curve": [0.778, 2.41, 1.222, -2.89]}, {"time": 1.6667, "value": -2.89, "curve": [2.111, -2.89, 2.556, 2.41]}, {"time": 3, "value": 2.41, "curve": [3.444, 2.41, 3.889, -2.89]}, {"time": 4.3333, "value": -2.89, "curve": [4.778, -2.89, 5.222, 2.41]}, {"time": 5.6667, "value": 2.41, "curve": [6.111, 2.41, 6.556, -2.89]}, {"time": 7, "value": -2.89, "curve": [7.444, -2.89, 7.889, 2.41]}, {"time": 8.3333, "value": 2.41, "curve": [8.778, 2.41, 9.222, -2.89]}, {"time": 9.6667, "value": -2.89, "curve": [10.001, -2.89, 10.336, 0.08]}, {"time": 10.6667, "value": 1.56}]}, "tree_A4": {"rotate": [{"value": -0.24, "curve": [0.225, 1.08, 0.446, 2.41]}, {"time": 0.6667, "value": 2.41, "curve": [1.111, 2.41, 1.556, -2.89]}, {"time": 2, "value": -2.89, "curve": [2.444, -2.89, 2.889, 2.41]}, {"time": 3.3333, "value": 2.41, "curve": [3.778, 2.41, 4.222, -2.89]}, {"time": 4.6667, "value": -2.89, "curve": [5.111, -2.89, 5.556, 2.41]}, {"time": 6, "value": 2.41, "curve": [6.444, 2.41, 6.889, -2.89]}, {"time": 7.3333, "value": -2.89, "curve": [7.778, -2.89, 8.222, 2.41]}, {"time": 8.6667, "value": 2.41, "curve": [9.111, 2.41, 9.556, -2.89]}, {"time": 10, "value": -2.89, "curve": [10.224, -2.89, 10.447, -1.58]}, {"time": 10.6667, "value": -0.24}]}, "tree_B2": {"rotate": [{"value": -1.98, "curve": [0.336, -0.69, 0.668, 1.84]}, {"time": 1, "value": 1.84, "curve": [1.444, 1.84, 1.889, -2.7]}, {"time": 2.3333, "value": -2.7, "curve": [2.778, -2.7, 3.222, 1.84]}, {"time": 3.6667, "value": 1.84, "curve": [4.111, 1.84, 4.556, -2.7]}, {"time": 5, "value": -2.7, "curve": [5.444, -2.7, 5.889, 1.84]}, {"time": 6.3333, "value": 1.84, "curve": [6.778, 1.84, 7.222, -2.7]}, {"time": 7.6667, "value": -2.7, "curve": [8.111, -2.7, 8.556, 1.84]}, {"time": 9, "value": 1.84, "curve": [9.444, 1.84, 9.889, -2.7]}, {"time": 10.3333, "value": -2.7, "curve": [10.446, -2.7, 10.559, -2.41]}, {"time": 10.6667, "value": -1.98}]}, "tree_B3": {"rotate": [{"value": -2.7, "curve": [0.444, -2.7, 0.889, 1.84]}, {"time": 1.3333, "value": 1.84, "curve": [1.778, 1.84, 2.222, -2.7]}, {"time": 2.6667, "value": -2.7, "curve": [3.111, -2.7, 3.556, 1.84]}, {"time": 4, "value": 1.84, "curve": [4.444, 1.84, 4.889, -2.7]}, {"time": 5.3333, "value": -2.7, "curve": [5.778, -2.7, 6.222, 1.84]}, {"time": 6.6667, "value": 1.84, "curve": [7.111, 1.84, 7.556, -2.7]}, {"time": 8, "value": -2.7, "curve": [8.444, -2.7, 8.889, 1.84]}, {"time": 9.3333, "value": 1.84, "curve": [9.778, 1.84, 10.222, -2.7]}, {"time": 10.6667, "value": -2.7}]}, "tree_B4": {"rotate": [{"value": -1.98, "curve": [0.114, -2.39, 0.224, -2.7]}, {"time": 0.3333, "value": -2.7, "curve": [0.778, -2.7, 1.222, 1.84]}, {"time": 1.6667, "value": 1.84, "curve": [2.111, 1.84, 2.556, -2.7]}, {"time": 3, "value": -2.7, "curve": [3.444, -2.7, 3.889, 1.84]}, {"time": 4.3333, "value": 1.84, "curve": [4.778, 1.84, 5.222, -2.7]}, {"time": 5.6667, "value": -2.7, "curve": [6.111, -2.7, 6.556, 1.84]}, {"time": 7, "value": 1.84, "curve": [7.444, 1.84, 7.889, -2.7]}, {"time": 8.3333, "value": -2.7, "curve": [8.778, -2.7, 9.222, 1.84]}, {"time": 9.6667, "value": 1.84, "curve": [10.001, 1.84, 10.336, -0.7]}, {"time": 10.6667, "value": -1.98}]}, "tree_LA2": {"rotate": [{"value": -2.16, "curve": [0.114, -2.74, 0.224, -3.16]}, {"time": 0.3333, "value": -3.16, "curve": [0.778, -3.16, 1.222, 3.09]}, {"time": 1.6667, "value": 3.09, "curve": [2.111, 3.09, 2.556, -3.16]}, {"time": 3, "value": -3.16, "curve": [3.444, -3.16, 3.889, 3.09]}, {"time": 4.3333, "value": 3.09, "curve": [4.778, 3.09, 5.222, -3.16]}, {"time": 5.6667, "value": -3.16, "curve": [6.111, -3.16, 6.556, 3.09]}, {"time": 7, "value": 3.09, "curve": [7.444, 3.09, 7.889, -3.16]}, {"time": 8.3333, "value": -3.16, "curve": [8.778, -3.16, 9.222, 3.09]}, {"time": 9.6667, "value": 3.09, "curve": [10.001, 3.09, 10.336, -0.41]}, {"time": 10.6667, "value": -2.16}]}, "tree_LA3": {"rotate": [{"value": -0.04, "curve": [0.225, -1.59, 0.446, -3.16]}, {"time": 0.6667, "value": -3.16, "curve": [1.111, -3.16, 1.556, 3.09]}, {"time": 2, "value": 3.09, "curve": [2.444, 3.09, 2.889, -3.16]}, {"time": 3.3333, "value": -3.16, "curve": [3.778, -3.16, 4.222, 3.09]}, {"time": 4.6667, "value": 3.09, "curve": [5.111, 3.09, 5.556, -3.16]}, {"time": 6, "value": -3.16, "curve": [6.444, -3.16, 6.889, 3.09]}, {"time": 7.3333, "value": 3.09, "curve": [7.778, 3.09, 8.222, -3.16]}, {"time": 8.6667, "value": -3.16, "curve": [9.111, -3.16, 9.556, 3.09]}, {"time": 10, "value": 3.09, "curve": [10.224, 3.09, 10.447, 1.54]}, {"time": 10.6667, "value": -0.04}]}, "tree_LB2": {"rotate": [{"value": 1.09, "curve": [0.336, 0.15, 0.668, -1.71]}, {"time": 1, "value": -1.71, "curve": [1.444, -1.71, 1.889, 1.62]}, {"time": 2.3333, "value": 1.62, "curve": [2.778, 1.62, 3.222, -1.71]}, {"time": 3.6667, "value": -1.71, "curve": [4.111, -1.71, 4.556, 1.62]}, {"time": 5, "value": 1.62, "curve": [5.444, 1.62, 5.889, -1.71]}, {"time": 6.3333, "value": -1.71, "curve": [6.778, -1.71, 7.222, 1.62]}, {"time": 7.6667, "value": 1.62, "curve": [8.111, 1.62, 8.556, -1.71]}, {"time": 9, "value": -1.71, "curve": [9.444, -1.71, 9.889, 1.62]}, {"time": 10.3333, "value": 1.62, "curve": [10.446, 1.62, 10.559, 1.41]}, {"time": 10.6667, "value": 1.09}]}, "tree_LB3": {"rotate": [{"value": 1.62, "curve": [0.444, 1.62, 0.889, -1.71]}, {"time": 1.3333, "value": -1.71, "curve": [1.778, -1.71, 2.222, 1.62]}, {"time": 2.6667, "value": 1.62, "curve": [3.111, 1.62, 3.556, -1.71]}, {"time": 4, "value": -1.71, "curve": [4.444, -1.71, 4.889, 1.62]}, {"time": 5.3333, "value": 1.62, "curve": [5.778, 1.62, 6.222, -1.71]}, {"time": 6.6667, "value": -1.71, "curve": [7.111, -1.71, 7.556, 1.62]}, {"time": 8, "value": 1.62, "curve": [8.444, 1.62, 8.889, -1.71]}, {"time": 9.3333, "value": -1.71, "curve": [9.778, -1.71, 10.222, 1.62]}, {"time": 10.6667, "value": 1.62}]}, "tree_LB4": {"rotate": [{"value": 1.09, "curve": [0.114, 1.39, 0.224, 1.62]}, {"time": 0.3333, "value": 1.62, "curve": [0.778, 1.62, 1.222, -1.71]}, {"time": 1.6667, "value": -1.71, "curve": [2.111, -1.71, 2.556, 1.62]}, {"time": 3, "value": 1.62, "curve": [3.444, 1.62, 3.889, -1.71]}, {"time": 4.3333, "value": -1.71, "curve": [4.778, -1.71, 5.222, 1.62]}, {"time": 5.6667, "value": 1.62, "curve": [6.111, 1.62, 6.556, -1.71]}, {"time": 7, "value": -1.71, "curve": [7.444, -1.71, 7.889, 1.62]}, {"time": 8.3333, "value": 1.62, "curve": [8.778, 1.62, 9.222, -1.71]}, {"time": 9.6667, "value": -1.71, "curve": [10.001, -1.71, 10.336, 0.15]}, {"time": 10.6667, "value": 1.09}]}, "tree_LC2": {"rotate": [{"value": -0.31, "curve": [0.225, 0.77, 0.446, 1.86]}, {"time": 0.6667, "value": 1.86, "curve": [1.111, 1.86, 1.556, -2.47]}, {"time": 2, "value": -2.47, "curve": [2.444, -2.47, 2.889, 1.86]}, {"time": 3.3333, "value": 1.86, "curve": [3.778, 1.86, 4.222, -2.47]}, {"time": 4.6667, "value": -2.47, "curve": [5.111, -2.47, 5.556, 1.86]}, {"time": 6, "value": 1.86, "curve": [6.444, 1.86, 6.889, -2.47]}, {"time": 7.3333, "value": -2.47, "curve": [7.778, -2.47, 8.222, 1.86]}, {"time": 8.6667, "value": 1.86, "curve": [9.111, 1.86, 9.556, -2.47]}, {"time": 10, "value": -2.47, "curve": [10.224, -2.47, 10.447, -1.4]}, {"time": 10.6667, "value": -0.31}]}, "tree_LC3": {"rotate": [{"value": -1.78, "curve": [0.336, -0.55, 0.668, 1.86]}, {"time": 1, "value": 1.86, "curve": [1.444, 1.86, 1.889, -2.47]}, {"time": 2.3333, "value": -2.47, "curve": [2.778, -2.47, 3.222, 1.86]}, {"time": 3.6667, "value": 1.86, "curve": [4.111, 1.86, 4.556, -2.47]}, {"time": 5, "value": -2.47, "curve": [5.444, -2.47, 5.889, 1.86]}, {"time": 6.3333, "value": 1.86, "curve": [6.778, 1.86, 7.222, -2.47]}, {"time": 7.6667, "value": -2.47, "curve": [8.111, -2.47, 8.556, 1.86]}, {"time": 9, "value": 1.86, "curve": [9.444, 1.86, 9.889, -2.47]}, {"time": 10.3333, "value": -2.47, "curve": [10.446, -2.47, 10.559, -2.2]}, {"time": 10.6667, "value": -1.78}]}, "tree_LC4": {"rotate": [{"value": -2.47, "curve": [0.444, -2.47, 0.889, 1.86]}, {"time": 1.3333, "value": 1.86, "curve": [1.778, 1.86, 2.222, -2.47]}, {"time": 2.6667, "value": -2.47, "curve": [3.111, -2.47, 3.556, 1.86]}, {"time": 4, "value": 1.86, "curve": [4.444, 1.86, 4.889, -2.47]}, {"time": 5.3333, "value": -2.47, "curve": [5.778, -2.47, 6.222, 1.86]}, {"time": 6.6667, "value": 1.86, "curve": [7.111, 1.86, 7.556, -2.47]}, {"time": 8, "value": -2.47, "curve": [8.444, -2.47, 8.889, 1.86]}, {"time": 9.3333, "value": 1.86, "curve": [9.778, 1.86, 10.222, -2.47]}, {"time": 10.6667, "value": -2.47}]}, "tree_LC5": {"rotate": [{"value": -1.78, "curve": [0.114, -2.18, 0.224, -2.47]}, {"time": 0.3333, "value": -2.47, "curve": [0.778, -2.47, 1.222, 1.86]}, {"time": 1.6667, "value": 1.86, "curve": [2.111, 1.86, 2.556, -2.47]}, {"time": 3, "value": -2.47, "curve": [3.444, -2.47, 3.889, 1.86]}, {"time": 4.3333, "value": 1.86, "curve": [4.778, 1.86, 5.222, -2.47]}, {"time": 5.6667, "value": -2.47, "curve": [6.111, -2.47, 6.556, 1.86]}, {"time": 7, "value": 1.86, "curve": [7.444, 1.86, 7.889, -2.47]}, {"time": 8.3333, "value": -2.47, "curve": [8.778, -2.47, 9.222, 1.86]}, {"time": 9.6667, "value": 1.86, "curve": [10.001, 1.86, 10.336, -0.57]}, {"time": 10.6667, "value": -1.78}]}, "tree_R2": {"rotate": [{"value": 1.21, "curve": [0.168, 2.26, 0.334, 3.12]}, {"time": 0.5, "value": 3.12, "curve": [0.944, 3.12, 1.389, -2.87]}, {"time": 1.8333, "value": -2.87, "curve": [2.278, -2.87, 2.722, 3.12]}, {"time": 3.1667, "value": 3.12, "curve": [3.611, 3.12, 4.056, -2.87]}, {"time": 4.5, "value": -2.87, "curve": [4.944, -2.87, 5.389, 3.12]}, {"time": 5.8333, "value": 3.12, "curve": [6.278, 3.12, 6.722, -2.87]}, {"time": 7.1667, "value": -2.87, "curve": [7.611, -2.87, 8.056, 3.12]}, {"time": 8.5, "value": 3.12, "curve": [8.944, 3.12, 9.389, -2.87]}, {"time": 9.8333, "value": -2.87, "curve": [10.112, -2.87, 10.39, -0.54]}, {"time": 10.6667, "value": 1.21}]}, "tree_R3": {"rotate": [{"value": -0.97, "curve": [0.279, 0.79, 0.556, 3.12]}, {"time": 0.8333, "value": 3.12, "curve": [1.278, 3.12, 1.722, -2.87]}, {"time": 2.1667, "value": -2.87, "curve": [2.611, -2.87, 3.056, 3.12]}, {"time": 3.5, "value": 3.12, "curve": [3.944, 3.12, 4.389, -2.87]}, {"time": 4.8333, "value": -2.87, "curve": [5.278, -2.87, 5.722, 3.12]}, {"time": 6.1667, "value": 3.12, "curve": [6.611, 3.12, 7.056, -2.87]}, {"time": 7.5, "value": -2.87, "curve": [7.944, -2.87, 8.389, 3.12]}, {"time": 8.8333, "value": 3.12, "curve": [9.278, 3.12, 9.722, -2.87]}, {"time": 10.1667, "value": -2.87, "curve": [10.334, -2.87, 10.501, -2.03]}, {"time": 10.6667, "value": -0.97}]}, "tree_UpA2": {"rotate": [{"value": -3.39, "curve": [0.39, -2.13, 0.779, 2.81]}, {"time": 1.1667, "value": 2.81, "curve": [1.611, 2.81, 2.056, -3.69]}, {"time": 2.5, "value": -3.69, "curve": [2.944, -3.69, 3.389, 2.81]}, {"time": 3.8333, "value": 2.81, "curve": [4.278, 2.81, 4.722, -3.69]}, {"time": 5.1667, "value": -3.69, "curve": [5.611, -3.69, 6.056, 2.81]}, {"time": 6.5, "value": 2.81, "curve": [6.944, 2.81, 7.389, -3.69]}, {"time": 7.8333, "value": -3.69, "curve": [8.278, -3.69, 8.722, 2.81]}, {"time": 9.1667, "value": 2.81, "curve": [9.611, 2.81, 10.056, -3.69]}, {"time": 10.5, "value": -3.69, "curve": [10.556, -3.69, 10.613, -3.57]}, {"time": 10.6667, "value": -3.39}]}, "tree_UpA3": {"rotate": [{"value": -3.39, "curve": [0.057, -3.56, 0.112, -3.69]}, {"time": 0.1667, "value": -3.69, "curve": [0.611, -3.69, 1.056, 2.81]}, {"time": 1.5, "value": 2.81, "curve": [1.944, 2.81, 2.389, -3.69]}, {"time": 2.8333, "value": -3.69, "curve": [3.278, -3.69, 3.722, 2.81]}, {"time": 4.1667, "value": 2.81, "curve": [4.611, 2.81, 5.056, -3.69]}, {"time": 5.5, "value": -3.69, "curve": [5.944, -3.69, 6.389, 2.81]}, {"time": 6.8333, "value": 2.81, "curve": [7.278, 2.81, 7.722, -3.69]}, {"time": 8.1667, "value": -3.69, "curve": [8.611, -3.69, 9.056, 2.81]}, {"time": 9.5, "value": 2.81, "curve": [9.89, 2.81, 10.279, -2.16]}, {"time": 10.6667, "value": -3.39}]}, "tree_UpB2": {"rotate": [{"value": -1.07, "curve": [0.168, -2.45, 0.334, -3.58]}, {"time": 0.5, "value": -3.58, "curve": [0.944, -3.58, 1.389, 4.31]}, {"time": 1.8333, "value": 4.31, "curve": [2.278, 4.31, 2.722, -3.58]}, {"time": 3.1667, "value": -3.58, "curve": [3.611, -3.58, 4.056, 4.31]}, {"time": 4.5, "value": 4.31, "curve": [4.944, 4.31, 5.389, -3.58]}, {"time": 5.8333, "value": -3.58, "curve": [6.278, -3.58, 6.722, 4.31]}, {"time": 7.1667, "value": 4.31, "curve": [7.611, 4.31, 8.056, -3.58]}, {"time": 8.5, "value": -3.58, "curve": [8.944, -3.58, 9.389, 4.31]}, {"time": 9.8333, "value": 4.31, "curve": [10.112, 4.31, 10.39, 1.24]}, {"time": 10.6667, "value": -1.07}]}, "Car": {"translate": [{"y": -0.97, "curve": [0.222, 0, 0.444, 0.73, 0.222, -0.97, 0.444, 0.91]}, {"time": 0.6667, "x": 0.73, "y": 0.91, "curve": [0.889, 0.73, 1.111, 0, 0.889, 0.91, 1.111, -0.97]}, {"time": 1.3333, "y": -0.97, "curve": [1.556, 0, 1.778, 0.73, 1.556, -0.97, 1.778, 0.91]}, {"time": 2, "x": 0.73, "y": 0.91, "curve": [2.222, 0.73, 2.444, 0, 2.222, 0.91, 2.444, -0.97]}, {"time": 2.6667, "y": -0.97, "curve": [2.889, 0, 3.111, 0.73, 2.889, -0.97, 3.111, 0.91]}, {"time": 3.3333, "x": 0.73, "y": 0.91, "curve": [3.556, 0.73, 3.778, 0, 3.556, 0.91, 3.778, -0.97]}, {"time": 4, "y": -0.97, "curve": [4.222, 0, 4.444, 0.73, 4.222, -0.97, 4.444, 0.91]}, {"time": 4.6667, "x": 0.73, "y": 0.91, "curve": [4.889, 0.73, 5.111, 0, 4.889, 0.91, 5.111, -0.97]}, {"time": 5.3333, "y": -0.97, "curve": [5.556, 0, 5.778, 0.73, 5.556, -0.97, 5.778, 0.91]}, {"time": 6, "x": 0.73, "y": 0.91, "curve": [6.222, 0.73, 6.444, 0, 6.222, 0.91, 6.444, -0.97]}, {"time": 6.6667, "y": -0.97, "curve": [6.889, 0, 7.111, 0.73, 6.889, -0.97, 7.111, 0.91]}, {"time": 7.3333, "x": 0.73, "y": 0.91, "curve": [7.556, 0.73, 7.778, 0, 7.556, 0.91, 7.778, -0.97]}, {"time": 8, "y": -0.97, "curve": [8.211, 0, 8.422, 0.73, 8.211, -0.97, 8.422, 0.91]}, {"time": 8.6333, "x": 0.73, "y": 0.91, "curve": [8.867, 0.73, 9.1, 0, 8.867, 0.91, 9.1, -0.97]}, {"time": 9.3333, "y": -0.97, "curve": [9.556, 0, 9.778, 0.73, 9.556, -0.97, 9.778, 0.91]}, {"time": 10, "x": 0.73, "y": 0.91, "curve": [10.222, 0.73, 10.444, 0, 10.222, 0.91, 10.444, -0.97]}, {"time": 10.6667, "y": -0.97}]}, "CAR": {"translate": [{"time": 5, "curve": [5.078, 0, 5.156, -9.67, 5.078, 0, 5.156, -4.39]}, {"time": 5.2333, "x": -9.67, "y": -4.39, "curve": [5.344, -9.67, 5.456, -8.01, 5.344, -4.39, 5.456, -3.63]}, {"time": 5.5667, "x": -8.01, "y": -3.63, "curve": "stepped"}, {"time": 7, "x": -8.01, "y": -3.63, "curve": [7.078, -8.01, 7.156, 10.4, 7.078, -3.63, 7.156, 4.72]}, {"time": 7.2333, "x": 10.4, "y": 4.72, "curve": [7.344, 10.4, 7.456, 6.93, 7.344, 4.72, 7.456, 3.14]}, {"time": 7.5667, "x": 6.93, "y": 3.14, "curve": "stepped"}, {"time": 8.3333, "x": 6.93, "y": 3.14, "curve": [8.556, 6.93, 8.778, 0, 8.556, 3.14, 8.778, 0]}, {"time": 9}]}, "light": {"rotate": [{"time": 7.6667, "curve": [7.889, 0, 8.111, -180]}, {"time": 8.3333, "value": -180}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 7.6667, "x": 0, "y": 0, "curve": [7.778, 0, 7.889, 1.774, 7.778, 0, 7.889, 1.774]}, {"time": 8, "x": 1.774, "y": 1.774, "curve": [8.111, 1.774, 8.222, 0, 8.111, 1.774, 8.222, 0]}, {"time": 8.3333, "x": 0, "y": 0}]}}}}}