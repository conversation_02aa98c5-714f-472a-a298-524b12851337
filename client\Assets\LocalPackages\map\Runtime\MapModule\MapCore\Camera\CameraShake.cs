﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public class CameraShake : CameraAction
    {
        enum ShakeState
        {
            Shaking,
            Ending,
            End,
        }
        public CameraShake(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        public void StartShaking(float duration, AnimationCurve rangeCurve, AnimationCurve speedCurve)
        {
            StartShakingImpl(duration, 0, 0, rangeCurve, speedCurve);
        }

        public void StartShaking(float duration, float range, float speed)
        {
            StartShakingImpl(duration, range, speed, null, null);
        }

        void StartShakingImpl(float duration, float range, float speed, AnimationCurve rangeCurve, AnimationCurve speedCurve)
        {
            mTime = 0;
            mDuration = duration;
            mRange = range;
            mRangeCurve = rangeCurve;
            mSpeed = speed;
            mSpeedCurve = speedCurve;
            mCurrentOffset = Vector3.zero;
            mTargetOffset = GetOffset();
            enabled = true;
            mState = ShakeState.Shaking;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            if (enabled)
            {
                if (mState == ShakeState.End)
                {
                    enabled = false;
                }
                else
                {
                    mTime += Time.deltaTime;
                    if (mTime >= mDuration)
                    {
                        //end, go back to zero offset
                        mTargetOffset = Vector3.zero;
                        mState = ShakeState.Ending;
                    }

                    float speed = GetSpeed();
                    mCurrentOffset = Vector3.MoveTowards(mCurrentOffset, mTargetOffset, speed * Time.deltaTime);
                    if (mCurrentOffset == mTargetOffset)
                    {
                        if (mState == ShakeState.Ending)
                        {
                            mState = ShakeState.End;
                        }
                        else
                        {
                            mTargetOffset = GetOffset();
                        }
                    }
                }
            }
        }

        public override Vector3 GetTargetPosition()
        {
            return mCurrentOffset;
        }

        public override void OnFinishImpl()
        {
        }

        Vector3 GetOffset()
        {
            float range = GetRange();
            float angle = UnityEngine.Random.Range(0, 360) * Mathf.Deg2Rad;
            float offsetX = Mathf.Sin(angle) * range;
            float offsetZ = Mathf.Cos(angle) * range;
            return new Vector3(offsetX, 0, offsetZ);
        }

        float GetSpeed()
        {
            if (mDuration == 0)
            {
                return 0;
            }
            if (mSpeedCurve != null)
            {
                return mSpeedCurve.Evaluate(mTime / mDuration);
            }
            return mSpeed;
        }

        float GetRange()
        {
            if (mDuration == 0)
            {
                return 0;
            }

            if (mRangeCurve != null)
            {
                return mRangeCurve.Evaluate(mTime / mDuration);
            }
            return mRange;
        }

        float mTime;
        float mDuration;
        float mSpeed;
        AnimationCurve mSpeedCurve;
        float mRange;
        AnimationCurve mRangeCurve;
        Vector3 mCurrentOffset;
        Vector3 mTargetOffset;
        ShakeState mState;        
    }
}
