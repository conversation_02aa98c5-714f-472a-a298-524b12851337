%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Truchet
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17902\n-1451;-120;1004;726;4353.539;1501.093;4.613734;True;False\nNode;AmplifyShaderEditor.CosOpNode;17;-2128,0;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CeilOpNode;22;-2624,0;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LengthOpNode;8;-1136,0;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;25;-2992,256;Inherit;False;Tiling;2;0;False;1;0;FLOAT2;8,8;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LengthOpNode;9;-1136,96;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SignOpNode;16;-1984,0;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;10;-1312,96;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;12;-1616,0;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;34;-864,448;Inherit;False;Line
    Edge;1;3;False;1;0;FLOAT;0.1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;32;-864,352;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;33;-1040,272;Inherit;False;Line
    Width;1;2;False;1;0;FLOAT;0.7;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;31;-608,448;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;28;-592,128;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;27;-592,240;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;30;-592,336;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;18;-2304,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;14;-2096,128;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.LengthOpNode;21;-2480,0;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;19;-2480,96;Inherit;False;Seed;1;4;False;1;0;FLOAT;163;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;13;-1824,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;4;-592,0;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;23;-3040,128;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;24;-2784,128;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;3;-384,128;Inherit;False;3;0;FLOAT;0.4;False;1;FLOAT;0.4;False;2;FLOAT;0.2;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;2;-384,0;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0.8;False;2;FLOAT;0.6;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;11;-1456,0;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;6;-944,128;Inherit;False;Repetition;1;1;False;1;0;FLOAT;3;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMinOpNode;7;-928,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;5;-752,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;1;-160,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;0,0;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;17;0;18;0\nWireConnection;22;0;24;0\nWireConnection;8;0;11;0\nWireConnection;9;0;10;0\nWireConnection;16;0;17;0\nWireConnection;10;0;11;0\nWireConnection;12;0;13;0\nWireConnection;12;1;14;1\nWireConnection;32;0;33;0\nWireConnection;31;0;32;0\nWireConnection;31;1;34;0\nWireConnection;28;0;33;0\nWireConnection;28;1;34;0\nWireConnection;27;0;33;0\nWireConnection;27;1;34;0\nWireConnection;30;0;32;0\nWireConnection;30;1;34;0\nWireConnection;18;0;21;0\nWireConnection;18;1;19;0\nWireConnection;14;0;24;0\nWireConnection;21;0;22;0\nWireConnection;13;0;16;0\nWireConnection;13;1;14;0\nWireConnection;4;0;5;0\nWireConnection;24;0;23;0\nWireConnection;24;1;25;0\nWireConnection;3;0;4;0\nWireConnection;3;1;30;0\nWireConnection;3;2;31;0\nWireConnection;2;0;4;0\nWireConnection;2;1;28;0\nWireConnection;2;2;27;0\nWireConnection;11;0;12;0\nWireConnection;7;0;8;0\nWireConnection;7;1;9;0\nWireConnection;5;0;7;0\nWireConnection;5;1;6;0\nWireConnection;1;0;2;0\nWireConnection;1;1;3;0\nWireConnection;0;0;1;0\nASEEND*/\n//CHKSM=71BCBEAE6BE593A152630FFE597CDB7E884F9637"
  m_functionName: 
  m_description: Creates a circular truchet pattern
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
