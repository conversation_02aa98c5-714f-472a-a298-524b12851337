﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class VectorViewerWindow : EditorWindow
    {
        void OnGUI()
        {
            GUILayout.BeginVertical();
            mStartCoordText = EditorGUILayout.TextField("Start Coordinate", mStartCoordText);
            mEndCoordText = EditorGUILayout.TextField("End Coordinate", mEndCoordText);
            mColor = EditorGUILayout.ColorField("Color", mColor);
            mWidth = EditorGUILayout.FloatField("Width", mWidth);
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create"))
            {
                bool suc1, suc2;
                var start = DeserializeVector2(mStartCoordText, out suc1);
                var end = DeserializeVector2(mEndCoordText, out suc2);

                if (suc1 && suc2)
                {
                    var viewer = new VectorViewer();
                    viewer.Create(Utils.ToVector3(start), Utils.ToVector3(end), mWidth, mColor);

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        Vector2 DeserializeVector2(string text, out bool suc)
        {
            suc = false;
            var obj = JSONParser.Deserialize(text) as Dictionary<string, object>;
            if (obj != null)
            {
                object xObj;
                obj.TryGetValue("X", out xObj);
                object yObj;
                obj.TryGetValue("Z", out yObj);
                if (xObj != null && yObj != null)
                {
                    float x = System.Convert.ToSingle(xObj);
                    float y = System.Convert.ToSingle(yObj);
                    suc = true;
                    return new Vector2(x / 1000.0f, y / 1000.0f);
                }
            }
            return Vector2.zero;
        }

        List<Vector2> DeserializeVector2Array(string text, out bool suc)
        {
            suc = false;
            List<Vector2> ret = null;
            var obj = JSONParser.Deserialize(text) as List<object>;
            if (obj != null)
            {
                ret = new List<Vector2>();
                for (int i = 0; i < obj.Count; ++i)
                {
                    var vec2 = obj[i] as Dictionary<string, object>;
                    if (vec2 != null)
                    {
                        object xObj;
                        vec2.TryGetValue("X", out xObj);
                        object yObj;
                        vec2.TryGetValue("Z", out yObj);
                        if (xObj != null && yObj != null)
                        {
                            float x = System.Convert.ToSingle(xObj);
                            float y = System.Convert.ToSingle(yObj);
                            ret.Add(new Vector2(x / 1000.0f, y / 1000.0f));
                        }
                    }
                }

                suc = ret.Count == obj.Count;
            }
            return ret;
        }

        string mStartCoordText = "{\"X\":304778, \"Z\":4124723}";
        string mEndCoordText = "{\"X\":404778, \"Z\":4124723}";
        Color mColor = Color.green;
        float mWidth = 0.01f;
    }
}

#endif