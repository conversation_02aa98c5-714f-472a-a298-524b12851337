﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public class CameraHeightSetting
    {
        public string name;
        public float viewDistance;
        public float cameraHeight;
        public float fov;
        //viewDistance * fov的值
        public float dxf;
    }

    //相机的分段fov和高度设置管理器
    public class MapCameraSetting
    {

        public void Load(string filePath)
        {
            mName = Utils.GetPathName(filePath, false);
            var bytes = MapModuleResourceMgr.LoadTextBytes(filePath, true);
            if (bytes != null && bytes.Length > 0)
            {
                string json = System.Text.ASCIIEncoding.ASCII.GetString(bytes);

                var settings = JSONParser.Deserialize(json) as Dictionary<string, object>;

                var camera = MapCameraMgr.MapCamera;
                //load x rotation angle
                object rotationAngle;
                bool found = settings.TryGetValue("rotation_x_angle", out rotationAngle);
                if (found)
                {
                    mRotationXAngle = System.Convert.ToSingle(rotationAngle);
                }
                else
                {
                    mRotationXAngle = camera.transform.eulerAngles.x;
                }
                object rotationYAngle;
                found = settings.TryGetValue("rotation_y_angle", out rotationYAngle);
                if (found)
                {
                    mRotationYAngle = System.Convert.ToSingle(rotationYAngle);
                }

                object horizontalRotationRange;
                found = settings.TryGetValue("horizontal_rotation_range", out horizontalRotationRange);
                if (found)
                {
                    mHorizontalRotationRange = System.Convert.ToSingle(horizontalRotationRange);
                    mHorizontalRotationRange = Mathf.Clamp(mHorizontalRotationRange, 0.0f, 180.0f);
                }

                object maxRotateHeight;
                found = settings.TryGetValue("max_horizontal_rotation_height", out maxRotateHeight);
                if (found)
                {
                    mMaxHorizontalRotationHeight = System.Convert.ToSingle(maxRotateHeight);
                    mMaxHorizontalRotationHeight = Mathf.Max(mMaxHorizontalRotationHeight, 0.0f);
                }

                object horizontalRotationRestoreSpeed;
                found = settings.TryGetValue("horizontal_rotation_restore_speed", out horizontalRotationRestoreSpeed);
                if (found)
                {
                    mHorizontalRotationRestoreSpeed = System.Convert.ToSingle(horizontalRotationRestoreSpeed);
                    mHorizontalRotationRestoreSpeed = Mathf.Max(mHorizontalRotationRestoreSpeed, 10);
                }

                //load camera heights
                object cameraSettingList;
                settings.TryGetValue("camera_heights", out cameraSettingList);
                if (cameraSettingList == null)
                {
                    Debug.Assert(false, "invalid camera setting file!");
                }
                else
                {
                    LoadCameraSettings(cameraSettingList as List<object>);
                }

                object worldCameraRadiusObj;
                found = settings.TryGetValue("camera_rising_transition_size", out worldCameraRadiusObj);
                if (found)
                {
                    mCameraRisingTransitionSize = System.Convert.ToSingle(worldCameraRadiusObj);
                }

                //load building lod config
                object buildLodSetting;
                settings.TryGetValue("building_lod_scale", out buildLodSetting);
                if (buildLodSetting == null)
                {
                    Debug.Assert(false, "invalid building lod setting!");
                }
                else
                {
                    LoadBuildingLODSetting(buildLodSetting as Dictionary<string, object>);
                }

                //load border clamp
                object calculateEdgeSizeConfig;
                settings.TryGetValue("calculate_edge_size", out calculateEdgeSizeConfig);
                if (calculateEdgeSizeConfig != null)
                {
                    LoadCalculateEdgeSizeSetting(calculateEdgeSizeConfig as Dictionary<string, object>);
                }

                //load border clamp
                object borderClampConfig;
                settings.TryGetValue("border_clamp", out borderClampConfig);
                if (borderClampConfig != null)
                {
                    LoadBorderClampConfig(borderClampConfig as Dictionary<string, object>);
                }
            }
        }

        //读取配置
        public async UniTask LoadAsync(string filePath)
        {
            mName = Utils.GetPathName(filePath, false);
            var json = await MapModuleResourceMgr.LoadTextStringAsync(filePath);
            
                if (!string.IsNullOrEmpty(json))
                {

                    var settings = JSONParser.Deserialize(json) as Dictionary<string, object>;

                //UnityEngine.Debug.Log($"path {mName} Dic: {Newtonsoft.Json.JsonConvert.SerializeObject(settings)}");

                //load camera heights
                object cameraSettingList;
                    settings.TryGetValue("camera_heights", out cameraSettingList);
                    if (cameraSettingList == null)
                    {
                        Debug.Assert(false, "invalid camera setting file!");
                    }
                    else
                    {
                        LoadCameraSettings(cameraSettingList as List<object>);
                    }

                    //load x rotation angle
                    object rotationAngle;
                    bool found = settings.TryGetValue("rotation_x_angle", out rotationAngle);
                    if (found)
                    {
                        mRotationXAngle = System.Convert.ToSingle(rotationAngle);
                    }
                    object rotationYAngle;
                    found = settings.TryGetValue("rotation_y_angle", out rotationYAngle);
                    if (found)
                    {
                        mRotationYAngle = System.Convert.ToSingle(rotationYAngle);
                    }

                    object worldCameraRadiusObj;
                    found = settings.TryGetValue("camera_rising_transition_size", out worldCameraRadiusObj);
                    if (found)
                    {
                        mCameraRisingTransitionSize = System.Convert.ToSingle(worldCameraRadiusObj);
                    }

                    //load building lod config
                    object buildLodSetting;
                    settings.TryGetValue("building_lod_scale", out buildLodSetting);
                    if (buildLodSetting == null)
                    {
                        Debug.Assert(false, "invalid building lod setting!");
                    }
                    else
                    {
                        LoadBuildingLODSetting(buildLodSetting as Dictionary<string, object>);
                    }

                    //load border clamp
                    object calculateEdgeSizeConfig;
                    settings.TryGetValue("calculate_edge_size", out calculateEdgeSizeConfig);
                    if (calculateEdgeSizeConfig != null)
                    {
                        LoadCalculateEdgeSizeSetting(calculateEdgeSizeConfig as Dictionary<string, object>);
                    }

                    //load border clamp
                    object borderClampConfig;
                    settings.TryGetValue("border_clamp", out borderClampConfig);
                    if (borderClampConfig != null)
                    {
                        LoadBorderClampConfig(borderClampConfig as Dictionary<string, object>);
                    }
                }
                MapModuleResourceMgr.UnloadAsset(filePath); 
      }

        void LoadBuildingLODSetting(Dictionary<string, object> buildingLODSetting)
        {
            if (mBuildingScaleConfig != null)
            {
                Object.Destroy(mBuildingScaleConfig);
            }
 
            mBuildingScaleConfig = ScriptableObject.CreateInstance<KeepScaleConfig>();

            var minHeightSetting = buildingLODSetting["min"] as string;
            var maxHeightSetting = buildingLODSetting["max"] as string;
            var minSetting = GetCameraHeightSetting(minHeightSetting);
            var maxSetting = GetCameraHeightSetting(maxHeightSetting);
            mBuildingScaleConfig.cameraFovWhenScaleIsOne = minSetting.fov;
            mBuildingScaleConfig.minimumCameraHeight = minSetting.cameraHeight;
            mBuildingScaleConfig.maximumCameraHeight = maxSetting.cameraHeight;
            mBuildingScaleConfig.cameraBaseScale = 1.0f;
            mBuildingScaleConfig.cameraHeightWhenScaleIsBaseScale = minSetting.cameraHeight;
            float height;
            Utils.ParseFloat(buildingLODSetting["camera_height_when_city_is_at_center"] as string, out height);
            mBuildingScaleConfig.maximumCameraHeightWhenCityIsInCenter = height;
        }

        void LoadCalculateEdgeSizeSetting(Dictionary<string, object> config)
        {
            object enableObj;
            bool found = config.TryGetValue("enable", out enableObj);
            if (found)
            {
                mEnableCalculateEdgeSize = System.Convert.ToBoolean(enableObj);
            }

            object xOffsetConfig;
            found = config.TryGetValue("x_offset", out xOffsetConfig);
            if (found)
            {
                mEdgeXOffset = System.Convert.ToSingle(xOffsetConfig);
            }

            object zOffsetConfig;
            found = config.TryGetValue("z_offset", out zOffsetConfig);
            if (found)
            {
                mEdgeZOffset = System.Convert.ToSingle(zOffsetConfig);
            }
        }

        void LoadBorderClampConfig(Dictionary<string, object> borderClampSetting)
        {
            object xConfig;
            bool found = borderClampSetting.TryGetValue("x", out xConfig);
            if (found)
            {
                mXBorderClampConfigPath = xConfig as string;
            }

            object zConfig;
            found = borderClampSetting.TryGetValue("z", out zConfig);
            if (found)
            {
                mZBorderClampConfigPath = zConfig as string;
            }
        }

        void LoadCameraSettings(List<object> cameraSettingList)
        {
            for (int i = 0; i < cameraSettingList.Count; ++i)
            {
                var settingDic = cameraSettingList[i] as Dictionary<string, object>;
                CameraHeightSetting setting = new CameraHeightSetting();
                var name = settingDic["name"] as string;
                var cameraHeight = float.Parse(settingDic["height"] as string);
                var fov = float.Parse(settingDic["fov"] as string);
                Debug.Assert(cameraHeight > 0);
                Debug.Assert(fov > 0);
                Debug.Assert(!string.IsNullOrEmpty(name));
                setting.fov = (float)fov;
                setting.name = name;
                 
                setting.cameraHeight = (float)cameraHeight;

                //if (name == "min" || setting.cameraHeight<42f)//地图最小距离强制设置
                //{
                //    setting.cameraHeight = 42f;
                //}

                if (mCameraHeightSettings.Count > 0)
                {
                    //相机配置的顺序必须从低到高
                    Debug.Assert(setting.cameraHeight >= mCameraHeightSettings[mCameraHeightSettings.Count - 1].cameraHeight, $"Invalid camera height setting order  {setting.name} at {setting.cameraHeight}");
                }

                mCameraHeightSettings.Add(setting);
            }

            mCameraHeightSettings.Sort(
                (CameraHeightSetting a, CameraHeightSetting b) => {
                    if (a.cameraHeight < b.cameraHeight)
                    {
                        return -1;
                    }
                    else if (a.cameraHeight > b.cameraHeight)
                    {
                        return 1;
                    }
                    return 0;
                });

            for (int i = 0; i < mCameraHeightSettings.Count; ++i)
            {
                mCameraHeightSettings[i].viewDistance = Utils.HeightToViewDistance(mRotationXAngle, mCameraHeightSettings[i].cameraHeight);
                mCameraHeightSettings[i].dxf = mCameraHeightSettings[i].fov * mCameraHeightSettings[i].viewDistance;
            }

            mCameraMinHeightSetting = GetCameraHeightSetting("min");
            mCameraMaxHeightSetting = GetCameraHeightSetting("max");
            mCameraWorldMapMinHeightSetting = GetCameraHeightSetting("world_min");
        }

        //返回出城的相机最低高度
        public float GetOutofCityCameraHeight()
        {
            return mCameraWorldMapMinHeightSetting.cameraHeight;
        }

        //根据dxf返回view distance和fov
        public bool GetDistFovByVDF(float dxf, out float dist, out float fov)
        {
            dist = 0f;
            fov = 0f;
            CameraHeightSetting minSetting = minHeightSetting;
            CameraHeightSetting maxSetting = maxHeightSetting;
            float b = maxSetting.dxf;

            float customMinDxf = minSetting.dxf;

            dxf = Mathf.Max(customMinDxf, Mathf.Min(dxf, b));
            if (dxf <= minSetting.dxf)
            {
                dist = minSetting.viewDistance;
                fov = minSetting.fov;
                return true;
            }
            if (dxf >= maxSetting.dxf)
            {
                dist = maxSetting.viewDistance;
                fov = maxSetting.fov;
                return true;
            }
            CameraHeightSetting curItem = null;
            CameraHeightSetting nextItem = null;
            for (int i = 0; i < mCameraHeightSettings.Count - 1; i++)
            {
                curItem = mCameraHeightSettings[i];
                nextItem = mCameraHeightSettings[i + 1];
                if (dxf == nextItem.dxf)
                {
                    dist = nextItem.viewDistance;
                    fov = nextItem.fov;
                    return true;
                }
                if (dxf > curItem.dxf && dxf < nextItem.dxf)
                {
                    double calcDist = 0.0;
                    double calcFOV = 0.0;
                    if (GetDistFovWithTwoCameraInfo((double)curItem.viewDistance, (double)curItem.fov, (double)nextItem.viewDistance, (double)nextItem.fov, (double)dxf, out calcDist, out calcFOV))
                    {
                        dist = (float)calcDist;
                        fov = (float)calcFOV;
                        return true;
                    }
                }
            }
            return false;
        }

        //从dxf值找到最接近的dist和fov的组合,找到区间,并在这个区间内插值
        public bool GetDistFovWithTwoCameraInfo(double minDist, double minFOV, double maxDist, double maxFOV, double dxf, out double dist, out double fov)
        {
            dist = 0.0;
            fov = 0.0;
            #region NAIVE_CHECK
            if ((minDist * minFOV) > (maxDist * maxFOV))
            {
                return false;
            }
            if ((dxf < (minDist * minFOV)) || (dxf > (maxDist * maxFOV)))
            {
                return false;
            }
            if (dxf == (minDist * minFOV))
            {
                dist = minDist;
                fov = minFOV;
                return true;
            }
            if (dxf == (maxDist * maxFOV))
            {
                dist = maxDist;
                fov = maxFOV;
                return true;
            }
            if (maxDist == minDist)
            {
                dist = minDist;
                fov = dxf / minDist;
                return true;
            }
            if (maxFOV == minFOV)
            {
                fov = minFOV;
                dist = dxf / minFOV;
                return true;
            }
            #endregion
            double lower = 0.0;
            double higher = 1.0;
            double ratio = -1.0;
            double rangeStartDXF = minDist * minFOV;
            double rangeEndDXF = maxDist * maxFOV;
            //二分查找,找到相同或者最近的dxf
            for (int i = 0; i < 20; i++)
            {
                if (dxf == rangeStartDXF)
                {
                    ratio = lower;
                    break;
                }
                if (dxf == rangeEndDXF)
                {
                    ratio = higher;
                    break;
                }
                double half = (higher + lower) / 2.0;
                double middleDXF = (minDist + half * (maxDist - minDist)) * (minFOV + half * (maxFOV - minFOV));
                if (dxf == middleDXF)
                {
                    ratio = half;
                    break;
                }
                if (dxf > middleDXF)
                {
                    if (lower == half)
                    {
                        ratio = half;
                        break;
                    }
                    lower = half;
                    rangeStartDXF = middleDXF;
                }
                else if (dxf < middleDXF)
                {
                    if (higher == half)
                    {
                        ratio = half;
                        break;
                    }
                    higher = half;
                    rangeEndDXF = middleDXF;
                }
            }
            if (ratio == -1.0)
            {
                ratio = lower;
            }
            dist = minDist + ratio * (maxDist - minDist);
            fov = minFOV + ratio * (maxFOV - minFOV);
            return true;
        }

        public float GetCameraDXF(string name)
        {
            for (int i = 0; i < mCameraHeightSettings.Count; i++)
            {
                if (name.CompareTo(mCameraHeightSettings[i].name) == 0)
                {
                    return mCameraHeightSettings[i].dxf;
                }
            }
            Debug.Assert(false, "Invalid camera setting name!");
            return 0f;
        }

        public CameraHeightSetting GetCameraSettingFromHeight(float height)
        {
            var camera = MapCameraMgr.MapCamera;
            var xRot = camera.transform.eulerAngles.x;
            float viewDistance = Utils.HeightToViewDistance(xRot, height);

            for (int i = 0; i < mCameraHeightSettings.Count; i++)
            {
                if (Mathf.Approximately(viewDistance, mCameraHeightSettings[i].viewDistance))
                {
                    return mCameraHeightSettings[i];
                }
            }
            Debug.Assert(false, "Invalid camera height!");
            return null;
        }

        public float GetCameraDXFFromHeight(float height)
        {
            var camera = MapCameraMgr.MapCamera;
            var xRot = camera.transform.eulerAngles.x;
            float viewDistance = Utils.HeightToViewDistance(xRot, height);

            for (int i = 0; i < mCameraHeightSettings.Count; i++)
            {
                if (Mathf.Approximately(viewDistance, mCameraHeightSettings[i].viewDistance))
                {
                    return mCameraHeightSettings[i].dxf;
                }
            }

            float fov = Map.currentMap.GetCameraFOVAtHeight(height);
            return fov * viewDistance;
        }

        public CameraHeightSetting GetCameraHeightSetting(string name)
        {
            for (int i = 0; i < mCameraHeightSettings.Count; i++)
            {
                if (name.CompareTo(mCameraHeightSettings[i].name) == 0)
                {
                    return mCameraHeightSettings[i];
                }
            }
            Debug.Assert(false, "Invalid camera setting name! " + name);
            return null;
        }

        public void SetCameraFOV(float currentCameraHeight, float maxCameraHeight)
        {
            var camera = MapCameraMgr.MapCamera;
            float fov = Map.currentMap.GetCameraFOVAtHeight(currentCameraHeight);
            camera.fieldOfView = fov;
        }

        //返回相机某个高度下的fov值
        public float GetFOVAtHeight(float cameraHeight)
        {
            if (!mEnableFOVChangeWhenHeightChange)
            {
                Debug.Assert(mDefaultFOV != 0);
                return mDefaultFOV;
            }

            //find match value first
            for (int i = 0; i < mCameraHeightSettings.Count; ++i)
            {
                if (Mathf.Approximately(mCameraHeightSettings[i].cameraHeight, cameraHeight))
                {
                    return mCameraHeightSettings[i].fov;
                }
            }
            
            int upperIndex = -1;
            int lowerIndex = -1;
            for (int i = mCameraHeightSettings.Count - 1; i >= 0; --i)
            {
                if (cameraHeight >= mCameraHeightSettings[i].cameraHeight)
                {
                    upperIndex = i + 1;
                    lowerIndex = i;
                    break;
                }
            }

            upperIndex = Mathf.Clamp(upperIndex, 0, mCameraHeightSettings.Count - 1);
            lowerIndex = Mathf.Clamp(lowerIndex, 0, mCameraHeightSettings.Count - 1);

            float deltaHeight = mCameraHeightSettings[upperIndex].cameraHeight - mCameraHeightSettings[lowerIndex].cameraHeight;
            if (deltaHeight == 0)
            {
                return mCameraHeightSettings[upperIndex].fov;
            }
            float ratio = (cameraHeight - mCameraHeightSettings[lowerIndex].cameraHeight) / deltaHeight;
            return Mathf.Lerp(mCameraHeightSettings[lowerIndex].fov, mCameraHeightSettings[upperIndex].fov, ratio);
        }

        public KeepScaleConfig buildingScaleConfig { get { return mBuildingScaleConfig; } }
        public float cameraMaxHeight { get { return maxHeightSetting.cameraHeight; } }
        public float cameraMinHeight { get { return minHeightSetting.cameraHeight; } }
        public float cameraWorldMapMinHeight { get { return mCameraWorldMapMinHeightSetting.cameraHeight; } }

        public void SetCustomCameraHeightRange(float minHeight, float maxHeight)
        {
            var camera = Map.currentMap.camera;
            float xRot = camera.transform.eulerAngles.x;

            float minHeightFOV = GetFOVAtHeight(minHeight);
            float maxHeightFOV = GetFOVAtHeight(maxHeight);

            mCustomCameraMaxHeightSetting = new CameraHeightSetting();
            mCustomCameraMinHeightSetting = new CameraHeightSetting();

            mCustomCameraMinHeightSetting.cameraHeight = minHeight;
            mCustomCameraMinHeightSetting.name = "min";
            mCustomCameraMinHeightSetting.fov = minHeightFOV;
            mCustomCameraMinHeightSetting.viewDistance = Utils.HeightToViewDistance(xRot, mCustomCameraMinHeightSetting.cameraHeight);
            mCustomCameraMinHeightSetting.dxf = mCustomCameraMinHeightSetting.fov * mCustomCameraMinHeightSetting.viewDistance;

            mCustomCameraMaxHeightSetting.name = "max";
            mCustomCameraMaxHeightSetting.cameraHeight = maxHeight;
            mCustomCameraMaxHeightSetting.fov = maxHeightFOV;
            mCustomCameraMaxHeightSetting.viewDistance = Utils.HeightToViewDistance(xRot, mCustomCameraMaxHeightSetting.cameraHeight);
            mCustomCameraMaxHeightSetting.dxf = mCustomCameraMaxHeightSetting.fov * mCustomCameraMaxHeightSetting.viewDistance;

            //replace min and max
            for (int i = 0; i < mCameraHeightSettings.Count; ++i)
            {
                if (mCameraHeightSettings[i].name == "min")
                {
                    mCameraHeightSettings[i] = mCustomCameraMinHeightSetting;
                }
                else if (mCameraHeightSettings[i].name == "max")
                {
                    mCameraHeightSettings[i] = mCustomCameraMaxHeightSetting;
                }
            }
        }
        public void ClearCustomCameraHeightRange()
        {
            mCustomCameraMinHeightSetting = null;
            mCustomCameraMaxHeightSetting = null;

            //replace min and max
            for (int i = 0; i < mCameraHeightSettings.Count; ++i)
            {
                if (mCameraHeightSettings[i].name == "min")
                {
                    mCameraHeightSettings[i] = mCameraMinHeightSetting;
                }
                else if (mCameraHeightSettings[i].name == "max")
                {
                    mCameraHeightSettings[i] = mCameraMaxHeightSetting;
                }
            }
        }
        CameraHeightSetting minHeightSetting
        {
            get
            {
                if (mCustomCameraMinHeightSetting != null)
                {
                    return mCustomCameraMinHeightSetting;
                }
                return mCameraMinHeightSetting;
            }
        }

        CameraHeightSetting maxHeightSetting
        {
            get
            {
                if (mCustomCameraMaxHeightSetting != null)
                {
                    return mCustomCameraMaxHeightSetting;
                }
                return mCameraMaxHeightSetting;
            }
        }

        public string name { get { return mName; } }
        public string xBorderClampConfigPath { get { return mXBorderClampConfigPath; } }
        public string zBorderClampConfigPath { get { return mZBorderClampConfigPath; } }
        public float rotationXAngle { get { return mRotationXAngle; } }
        public float rotationYAngle { get { return mRotationYAngle; } }
        public float horizontalRotationRange { get { return mHorizontalRotationRange; } }
        public float maxHorizontalRotationHeight { get { return mMaxHorizontalRotationHeight; } }
        public float horizontalRotationRestoreSpeed { get { return mHorizontalRotationRestoreSpeed; } }
        public bool enableCalculateEdgeSize { get { return mEnableCalculateEdgeSize; } }
        public float edgeXOffset { get { return mEdgeXOffset; } }
        public float edgeZOffset { get { return mEdgeZOffset; } }
        public float cameraRisingTransitionSize { get { return mCameraRisingTransitionSize; } }
        public float defaultFOV { get { return mDefaultFOV; } set { mDefaultFOV = value; } }
        public bool enableFOVChangeWhenHeightChange { get { return mEnableFOVChangeWhenHeightChange; } set { mEnableFOVChangeWhenHeightChange = value; } }

        List<CameraHeightSetting> mCameraHeightSettings = new List<CameraHeightSetting>();
        //相机城市内最低高度的设置
        CameraHeightSetting mCameraMinHeightSetting = new CameraHeightSetting();
        //相机最高高度的设置
        CameraHeightSetting mCameraMaxHeightSetting = new CameraHeightSetting();
        //相机城外最低高度
        CameraHeightSetting mCameraWorldMapMinHeightSetting = new CameraHeightSetting();
        //自定义情况下的最低相机高度设置
        CameraHeightSetting mCustomCameraMinHeightSetting;
        CameraHeightSetting mCustomCameraMaxHeightSetting;
        KeepScaleConfig mBuildingScaleConfig;
        string mXBorderClampConfigPath;
        string mZBorderClampConfigPath;
        string mName;
        float mRotationXAngle = 45;
        float mRotationYAngle = 0;
        //相机左右旋转的范围,0表示不能左右旋转
        float mHorizontalRotationRange = 0;
        //如果不为0,相机高度超过这个值后左右旋转会被还原
        float mMaxHorizontalRotationHeight;
        //相机左右旋转还原时旋转速度
        float mHorizontalRotationRestoreSpeed = 200.0f;
        //是否动态计算edge size,让视野永远只能看到地图内,为true则会忽略border clamp config
        bool mEnableCalculateEdgeSize = false;
        float mEdgeXOffset = 0;
        float mEdgeZOffset = 0;
        float mCameraRisingTransitionSize = 4;
        float mDefaultFOV;
        bool mEnableFOVChangeWhenHeightChange = true;
    }
}
