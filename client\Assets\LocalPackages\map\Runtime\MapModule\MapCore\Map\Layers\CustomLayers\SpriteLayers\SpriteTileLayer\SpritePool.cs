﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public interface ISpritePool
    {
        void OnDestroy();
        GameObject Require(int spriteTemplateID);
        void Release(int spriteTemplateID, GameObject obj);
        GameObject root { get; }
    }

    [Black]
    public class SpritePool : ISpritePool
    {
        public SpritePool(MapLayerData layerData, Vector2 spriteAnchor)
        {
            mSpriteAnchor = spriteAnchor;
            mLayerData = layerData;
            mRoot = new GameObject("Sprite Pool");
            Utils.HideGameObject(mRoot);
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
            mColorSpriteCache.OnDestroy();
            mColorSpriteCache = null;
        }

        public GameObject Require(int spriteTemplateID)
        {
            GameObject obj;
            List<GameObject> objectList = null;
            mTileViews.TryGetValue(spriteTemplateID, out objectList);
            if (objectList == null)
            {
                objectList = new List<GameObject>();
                mTileViews[spriteTemplateID] = objectList;
            }
            if (objectList.Count == 0)
            {
                obj = CreateSpriteTileView(spriteTemplateID);
            }
            else
            {
                obj = objectList[objectList.Count - 1];
                objectList.RemoveAt(objectList.Count - 1);
            }
            return obj;
        }

        public void Release(int spriteTemplateID, GameObject obj)
        {
            List<GameObject> objectList = null;
            mTileViews.TryGetValue(spriteTemplateID, out objectList);
            Debug.Assert(objectList != null);
            objectList.Add(obj);
            obj.SetActive(false);
            obj.transform.SetParent(mRoot.transform, true);
        }

        GameObject CreateSpriteTileView(int spriteTemplateID)
        {
            var spriteTemplate = Map.currentMap.FindObject(spriteTemplateID) as SpriteTemplate;
            var gameObject = CreateColorTile(spriteTemplate);
            return gameObject;
        }

        GameObject CreateColorTile(SpriteTemplate template)
        {
            return mColorSpriteCache.CreateGameObject(template.color, mLayerData);
        }

        public void ChangeMaterialColor(Color32 oldColor, Color32 newColor)
        {
            mColorSpriteCache.ChangeMaterialColor(oldColor, newColor);
        }

        public GameObject root { get { return mRoot; } }

        Dictionary<int, List<GameObject>> mTileViews = new Dictionary<int, List<GameObject>>();
        Vector2 mSpriteAnchor;
        GameObject mRoot;
        MapLayerData mLayerData;
        ColorSpriteMeshCache mColorSpriteCache = new ColorSpriteMeshCache();
    }
}

#endif