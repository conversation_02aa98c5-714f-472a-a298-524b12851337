﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveModelLayer(BinaryWriter writer, ModelLayer layer)
        {
            BeginSection(MapDataSectionType.ModelLayer, writer);

            writer.Write(VersionSetting.ModelLayerStructVersion);

            //-----------------version 1 start---------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveModelDataV1(writer, objData as ModelData);
            }

            //save map layer lod config
            SaveModelLayerLODConfigV1(writer, layer.layerData);
            //-----------------version 1 end---------------------------
            //-----------------version 2 start------------------------------
            SaveModelLayerLODConfigV2(writer, layer.GetLayerData());
            //-----------------version 2 end------------------------------
        }

        void SaveModelDataV1(BinaryWriter writer, ModelData data)
        {
            var modelData = data as ModelData;
            bool isDefaultRotation = (data.GetRotation() == Quaternion.identity);
            bool isDefaultScale = (data.GetScale() == Vector3.one);

            mIDExport.Export(writer, data.id);
            Utils.WriteVector3(writer, data.GetPosition());
            writer.Write(isDefaultRotation);
            if (isDefaultRotation == false)
            {
                Utils.WriteQuaternion(writer, data.GetRotation());
            }
            writer.Write(isDefaultScale);
            if (isDefaultScale == false)
            {
                Utils.WriteVector3(writer, data.GetScale());
            }

            mIDExport.Export(writer, modelData.GetModelTemplateID());
        }

        void SaveModelLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }

            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SaveModelLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif