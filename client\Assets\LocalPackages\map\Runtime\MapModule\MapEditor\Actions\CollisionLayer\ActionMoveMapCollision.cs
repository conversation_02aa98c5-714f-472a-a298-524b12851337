﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionMoveMapCollision : EditorAction
    {
        public ActionMoveMapCollision(int layerID, int dataID, Vector3 startPosition)
        {
            mDataID = dataID;
            mLayerID = layerID;
            mStartPosition = startPosition;
            mEndPosition = startPosition;
        }

        public void SetEndPosition(Vector3 pos)
        {
            mEndPosition = pos;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.MoveObject(PrefabOutlineType.NavMeshObstacle, mDataID, mEndPosition - mStartPosition);
            layer.MoveObject(PrefabOutlineType.ObjectPlacementObstacle, mDataID, mEndPosition - mStartPosition);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.MoveObject(PrefabOutlineType.NavMeshObstacle, mDataID, mStartPosition - mEndPosition);
            layer.MoveObject(PrefabOutlineType.ObjectPlacementObstacle, mDataID, mStartPosition - mEndPosition);
            return true;
        }

        public bool moved { get { return mStartPosition != mEndPosition; } }

        Vector3 mEndPosition;
        Vector3 mStartPosition;
        int mDataID;
        int mLayerID;
    }
}

#endif