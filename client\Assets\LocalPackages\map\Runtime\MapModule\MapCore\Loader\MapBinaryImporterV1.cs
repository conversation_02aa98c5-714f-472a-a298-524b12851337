﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */
#if false
using System.IO;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //第一个major版本的importer
    public class MapBinaryImporterV1 : MapBinaryImporter
    {
        public MapBinaryImporterV1(int majorVersion) : base(majorVersion)
        {
        }

        public override config.SLGMakerData Load(int minorVersion, string mapName, BinaryReader reader)
        {
            mMinorVersion = minorVersion;
            mEditorData = new config.SLGMakerData(0, 0, 0, false, new Version(version, minorVersion));

            LoadSetting(reader);
            LoadModelTemplates(reader);
            LoadTerrainPrefabManager(reader);
            LoadCamera(reader);
            LoadMap(reader);
            LoadMapObstacles(reader);
            if (mMinorVersion >= 18)
            {
                LoadGridRegionSetting(reader);
            }
            return mEditorData;
        }

        void LoadSetting(BinaryReader reader)
        {
            if (mMinorVersion >= 3)
            {
                mEditorData.setting.cameraMoveRange = Utils.ReadVector2(reader);
            }
            else
            {
                var bounds = Utils.ReadBounds(reader);
                mEditorData.setting.cameraMoveRange = new Vector2(bounds.min.y, bounds.max.y);
            }

            if (mMinorVersion >= 4)
            {
                mEditorData.setting.dataFolder = Utils.ReadString(reader);
            }
        }

        void LoadMap(BinaryReader reader)
        {
            mEditorData.map.width = reader.ReadSingle();
            mEditorData.map.height = reader.ReadSingle();
            if (mMinorVersion >= 7)
            {
                mEditorData.map.borderHeight = reader.ReadSingle();
            }
            if (mMinorVersion >= 9)
            {
                mEditorData.map.isCircle = reader.ReadBoolean();
            }
            LoadMapLayers(reader);
            LoadMapLODConfig(reader);
        }

        void LoadMapLODConfig(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.lodConfig.lods = new config.MapLOD[n];
            for (int i = 0; i < n; ++i)
            {
                mEditorData.map.lodConfig.lods[i] = new config.MapLOD();
                mEditorData.map.lodConfig.lods[i].cameraHeight = reader.ReadSingle();
            }
        }

        void LoadCamera(BinaryReader reader)
        {
            var cameraInfo = new config.Camera();
            cameraInfo.position = Utils.ReadVector3(reader);
            cameraInfo.rotation = Utils.ReadQuaternion(reader);
            cameraInfo.orthographic = reader.ReadBoolean();
            cameraInfo.orthongonalSize = reader.ReadSingle();
            cameraInfo.verticalFov = reader.ReadSingle();

            mEditorData.map.camera = cameraInfo;
        }

        bool LoadMapObjectBaseData(BinaryReader reader, config.MapObjectData data)
        {
            bool isDefaultRotation;
            bool isDefaultScale;

            data.SetID(reader.ReadInt32());
            if (data.id != 0)
            {
                data.position = Utils.ReadVector3(reader);
                isDefaultRotation = reader.ReadBoolean();
                if (!isDefaultRotation)
                {
                    data.rotation = Utils.ReadQuaternion(reader);
                }
                isDefaultScale = reader.ReadBoolean();
                if (!isDefaultScale)
                {
                    data.scale = Utils.ReadVector3(reader);
                }
                return true;
            }
            return false;
        }

        bool LoadGridObjectBaseData(BinaryReader reader, config.GridMapObjectData data)
        {
            bool valid = LoadMapObjectBaseData(reader, data);
            if (valid)
            {
                data.isDefaultPosition = reader.ReadBoolean();
            }
            return valid;
        }

        config.GridModelData LoadGridModelData(BinaryReader reader)
        {
            var id = Utils.PeekID(reader);
            if (id != 0)
            {
                var gridModelData = new config.GridModelData();
                LoadGridObjectBaseData(reader, gridModelData);
                gridModelData.modelTemplateID = reader.ReadInt32();
                return gridModelData;
            }
            else
            {
                id = reader.ReadInt32();
            }
            return null;
        }

        config.ModelData LoadModelData(BinaryReader reader)
        {
            var modelData = new config.ModelData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            return modelData;
        }

        config.RiverData LoadRiverData(BinaryReader reader)
        {
            var riverData = new config.RiverData();
            riverData.SetID(reader.ReadInt32());
            riverData.position = Utils.ReadVector3(reader);
            riverData.modelTemplateID = reader.ReadInt32();
            if (mMinorVersion >= 22)
            {
                riverData.hideLOD = reader.ReadInt32();
            }
            return riverData;
        }

        config.RailObjectData LoadRailObjectData(BinaryReader reader)
        {
            var modelData = new config.RailObjectData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            if (mMinorVersion >= 8)
            {
                modelData.type = (RailObjectType)reader.ReadInt32();
            }
            if (mMinorVersion >= 11)
            {
                modelData.isGroupLeader = reader.ReadBoolean();
                modelData.groupID = reader.ReadInt32();
                modelData.railIndex = reader.ReadInt32();
            }
            if (mMinorVersion >= 12)
            {
                modelData.segmentIndex = reader.ReadInt32();
            }
            return modelData;
        }

        config.GridModelLayerData LoadGridModelLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();

            var objects = new config.GridMapObjectData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadGridModelData(reader);
                }
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.GridModelLayerData(layerID, layerName, offset, config, rows, cols, tileWidth, tileHeight, (GridType)gridType, objects);
            return layer;
        }

        config.MapLayerData LoadLODLayer(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            //load layer info
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.LODLayerData(layerID, name, offset, config, layerWidth, layerHeight);
            return layer;
        }

        config.MapLayerData LoadBlendTerrainLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            var objects = new config.BlendTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadBlendTerrainTileData(reader, layerID);
                }
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.BlendTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, objects);
            return layer;
        }

        config.BlendTerrainTileData LoadBlendTerrainTileData(BinaryReader reader, int layerID)
        {
            config.BlendTerrainTileData tileData = null;
            var tileID = reader.ReadInt32();
            if (tileID > 0)
            {
                var templateID = reader.ReadInt32();
                var type = reader.ReadInt32();
                var index = reader.ReadInt32();
                tileData = new config.BlendTerrainTileData(tileID, layerID, type, index, templateID);
            }
            return tileData;
        }

        config.CircleBorderLayerData LoadCircleBorderLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCircleBorderData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);

            int combineBorderLOD = 1;
            if (mMinorVersion >= 6)
            {
                combineBorderLOD = reader.ReadInt32();
            }

            var layer = new config.CircleBorderLayerData(layerID, layerName, offset, config, width, height, objects, combineBorderLOD);
            return layer;
        }

        config.CircleBorderData LoadCircleBorderData(BinaryReader reader)
        {
            var modelData = new config.CircleBorderData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            if (mMinorVersion >= 5)
            {
                modelData.isAlwaysVisibleAtHigherLODs = reader.ReadBoolean();
            }
            return modelData;
        }

        config.ComplexGridModelData LoadComplexGridModelData(BinaryReader reader)
        {
            var modelData = new config.ComplexGridModelData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            modelData.occupiedGridCount = reader.ReadByte();
            return modelData;
        }

        config.ComplexGridModelLayerData LoadComplexGridModelLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.GridMapObjectData[] objects = new config.GridMapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadComplexGridModelData(reader);
            }

            var lodConfig = LoadMapLayerLODConfig(reader);
            var layer = new config.ComplexGridModelLayerData(layerID, layerName, offset, lodConfig, rows, cols, tileWidth, tileHeight, GridType.Rectangle, objects);
            return layer;
        }

        config.ModelLayerData LoadModelLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadModelData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.ModelLayerData(layerID, layerName, offset, config, null, width, height, objects);
            return layer;
        }

        config.RiverLayerData LoadRiverLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRiverData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.RiverLayerData(layerID, layerName, offset, config, null, width, height, objects);
            return layer;
        }

        config.ModelLODGroupManager LoadModelLODGroupManager(BinaryReader reader)
        {
            config.ModelLODGroupManager lodGroupManager = new config.ModelLODGroupManager();
            int nGroups = reader.ReadInt32();
            lodGroupManager.groups = new config.ModelLODGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.ModelLODGroup();
                var id = reader.ReadInt32();
                group.SetID(id);
                group.combineModels = reader.ReadBoolean();
                group.leaderObjectID = reader.ReadInt32();
                group.lod = reader.ReadInt32();

                lodGroupManager.groups[i] = group;
            }
            return lodGroupManager;
        }

        config.RailwayLayerData LoadRailwayLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRailObjectData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);

            config.ModelLODGroupManager lodGroupManager = new TFW.Map.config.ModelLODGroupManager();
            if (mMinorVersion >= 11)
            {
                lodGroupManager = LoadModelLODGroupManager(reader);
            }

            int count = reader.ReadInt32();
            float width = reader.ReadSingle();
            float radius = reader.ReadSingle();
            Vector3 center = Utils.ReadVector3(reader);
            float railPrefabLength = 50.0f;
            if (mMinorVersion >= 12)
            {
                railPrefabLength = reader.ReadSingle();
            }

            var layerData = new config.RailwayLayerData(layerID, layerName, offset, config, lodGroupManager, layerWidth, layerHeight, objects, count, width, radius, center, railPrefabLength);
            return layerData;
        }

        void LoadMapLayers(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.mapLayers = new config.MapLayerData[n];
            for (int i = 0; i < n; ++i)
            {
                config.MapLayerData layer = null;
                var layerType = reader.ReadInt32();
                var layerID = reader.ReadInt32();
                var name = Utils.ReadString(reader);
                Vector3 offset = Vector3.zero;
                offset = Utils.ReadVector3(reader);

                if (layerType == MapLayerType.kGridModelLayer)
                {
                    layer = LoadGridModelLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kModelLayer)
                {
                    layer = LoadModelLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kBlendTerrainLayer)
                {
                    layer = LoadBlendTerrainLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kLODLayer)
                {
                    layer = LoadLODLayer(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kCircleBorderLayer)
                {
                    layer = LoadCircleBorderLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRailwayLayer)
                {
                    layer = LoadRailwayLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRiverLayer)
                {
                    layer = LoadRiverLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kComplexGridModelLayer)
                {
                    layer = LoadComplexGridModelLayerData(reader, layerID, name, offset);
                }
                else
                {
                    Debug.Assert(false, "Unknown map layer!");
                }
                mEditorData.map.mapLayers[i] = layer;
            }
        }

        void LoadTerrainPrefabManager(BinaryReader reader)
        {
            int nGroups = reader.ReadInt32();
            mEditorData.terrainPrefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                int nPrefabs = reader.ReadInt32();
                var group = new config.PrefabGroup();
                group.prefabPaths = new string[nPrefabs];
                for (int k = 0; k < nPrefabs; ++k)
                {
                    group.prefabPaths[k] = Utils.ReadString(reader);
                }

                mEditorData.terrainPrefabManager.groups[i] = group;
            }
        }

        void LoadModelTemplates(BinaryReader reader)
        {
            if (mMinorVersion >= 19)
            {
                int nStrings = reader.ReadInt32();
                var stringTables = new string[nStrings];
                for (int i = 0; i < nStrings; ++i)
                {
                    stringTables[i] = Utils.ReadString(reader);
                }
                mEditorData.modelTemplates.stringTables = stringTables;
            }

            int nModelTemplates = reader.ReadInt32();
            var modelTemplates = new config.ModelTemplate[nModelTemplates];
            for (int i = 0; i < nModelTemplates; ++i)
            {
                modelTemplates[i] = LoadMeshModelTemplate(reader);
            }
            mEditorData.modelTemplates.modelTemplates = modelTemplates;
        }

        void LoadModelTemplateBase(config.ModelTemplate temp, BinaryReader reader)
        {
            var id = reader.ReadInt32();
            temp.SetID(id);

            temp.bounds = Utils.ReadBounds(reader);
            temp.prefabPath = Utils.ReadString(reader);

            if (mMinorVersion >= 2)
            {
                temp.isTileModelTemplate = reader.ReadBoolean();
                if (temp.isTileModelTemplate)
                {
                    int nLODs = reader.ReadInt32();
                    temp.childrenPrefabs = new config.ModelTemplateChildrenPrefabInfo[nLODs];
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        int n = reader.ReadInt32();
                        var childrenInfo = new config.ModelTemplateChildrenPrefabInfo();
                        childrenInfo.childrenPrefabBounds = new Rect[n];
                        if (mMinorVersion >= 19)
                        {
                            childrenInfo.childrenPrefabPathIndices = new int[n];
                            childrenInfo.childrenPrefabTagIndices = new int[n];
                        }
                        else
                        {
                            childrenInfo.childrenPrefabPaths = new string[n];
                            childrenInfo.childrenPrefabTags = new string[n];
                        }
                        childrenInfo.childrenPrefabPosition = new Vector3[n];
                        childrenInfo.childrenPrefabRotation = new Quaternion[n];
                        childrenInfo.childrenPrefabScaling = new Vector3[n];
                        for (int i = 0; i < n; ++i)
                        {
                            if (mMinorVersion >= 19)
                            {
                                childrenInfo.childrenPrefabPathIndices[i] = reader.ReadInt32();
                            }
                            else
                            {
                                childrenInfo.childrenPrefabPaths[i] = Utils.ReadString(reader);
                            }
                            childrenInfo.childrenPrefabBounds[i] = Utils.ReadRect(reader);
                            if (mMinorVersion >= 19)
                            {
                                childrenInfo.childrenPrefabTagIndices[i] = reader.ReadInt32();
                            }
                            else
                            {
                                childrenInfo.childrenPrefabTags[i] = Utils.ReadString(reader);
                            }
                            childrenInfo.childrenPrefabPosition[i] = Utils.ReadVector3(reader);
                            childrenInfo.childrenPrefabScaling[i] = Utils.ReadVector3(reader);
                            childrenInfo.childrenPrefabRotation[i] = Utils.ReadQuaternion(reader);
                        }

                        temp.childrenPrefabs[lod] = childrenInfo;
                    }

                }
            }

            if (mMinorVersion >= 14)
            {
                temp.lodInfo = new config.ModelTemplateLODInfo();
                int n = reader.ReadInt32();
                if (mMinorVersion >= 19)
                {
                    temp.lodInfo.lodPrefabPathIndices = new List<int>(n);
                }
                else
                {
                    temp.lodInfo.lodPrefabPaths = new List<string>(n);
                }
                for (int i = 0; i < n; ++i)
                {
                    if (mMinorVersion >= 19)
                    {
                        temp.lodInfo.lodPrefabPathIndices.Add(reader.ReadInt32());
                    }
                    else
                    {
                        temp.lodInfo.lodPrefabPaths.Add(Utils.ReadString(reader));
                    }
                }
                n = reader.ReadInt32();
                temp.lodInfo.existedLODs = new List<int>(n);
                for (int i = 0; i < n; ++i)
                {
                    temp.lodInfo.existedLODs.Add(reader.ReadInt32());
                }
            }

            if (mMinorVersion >= 15)
            {
                temp.preload = reader.ReadBoolean();
            }
        }

        config.ModelTemplate LoadMeshModelTemplate(BinaryReader reader)
        {
            var template = new config.ModelTemplate();
            LoadModelTemplateBase(template, reader);
            return template;
        }

        config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = false;
                int shaderLOD = 0;
                if (mMinorVersion >= 13)
                {
                    hideObject = reader.ReadBoolean();
                }
                if (mMinorVersion >= 16)
                {
                    shaderLOD = reader.ReadInt32();
                }
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(zoom, threshold, hideObject, shaderLOD);
            }
            return config;
        }

        void LoadMapObstacles(BinaryReader reader)
        {
            LoadLocalObstacles(reader);
            if (mMinorVersion >= 10)
            {
                LoadGlobalObstacles(reader);
            }
            if (mMinorVersion >= 17)
            {
                LoadCameraCollider(reader);
            }
        }

        void LoadGridRegionSetting(BinaryReader reader)
        {
            bool hasRegionData = reader.ReadBoolean();
            if (hasRegionData)
            {
                mEditorData.gridRegionSetting = new config.GridRegionSetting();
                mEditorData.gridRegionSetting.gridWidth = reader.ReadSingle();
                mEditorData.gridRegionSetting.gridHeight = reader.ReadSingle();
                mEditorData.gridRegionSetting.horizontalGridCount = reader.ReadInt32();
                mEditorData.gridRegionSetting.verticalGridCount = reader.ReadInt32();
                int byteCount = reader.ReadInt32();
                short[] gridData = new short[byteCount];
                if (mMinorVersion >= 21)
                {
                    for (int i = 0; i < byteCount; ++i)
                    {
                        gridData[i] = reader.ReadInt16();
                    }
                }
                else
                {
                    var byteGridData = reader.ReadBytes(byteCount);
                    for (int i = 0; i < byteCount; ++i)
                    {
                        gridData[i] = byteGridData[i];
                    }
                }
                mEditorData.gridRegionSetting.gridData = gridData;

                if (mMinorVersion >= 20)
                {
                    int n = reader.ReadInt32();
                    mEditorData.gridRegionSetting.templates = new config.GridRegionTemplate[n];
                    for (int i = 0; i < n; ++i)
                    {
                        var color = Utils.ReadColor32(reader);
                        byte type = reader.ReadByte();
                        mEditorData.gridRegionSetting.templates[i] = new config.GridRegionTemplate(color, type);
                    }
                }
            }
        }

        void LoadCameraCollider(BinaryReader reader)
        {
            bool valid = reader.ReadBoolean();
            if (valid)
            {
                mEditorData.cameraCollider.vertices = Utils.ReadVector3Array(reader);
                mEditorData.cameraCollider.indices = Utils.ReadIntArray(reader);
            }
        }

        void LoadLocalObstacles(BinaryReader reader)
        {
            var localObstacleManager = new config.MapLocalObstacleManager();
            mEditorData.localObstacleManager = localObstacleManager;
            localObstacleManager.regionWidth = reader.ReadSingle();
            localObstacleManager.regionHeight = reader.ReadSingle();
            int tileCount = reader.ReadInt32();
            localObstacleManager.tiles = new int[tileCount];
            for (int i = 0; i < tileCount; ++i)
            {
                localObstacleManager.tiles[i] = reader.ReadInt32();
            }

            int obstacleCount = reader.ReadInt32();
            localObstacleManager.obstacles = new config.MapPrefabObstacle[obstacleCount];
            for (int i = 0; i < obstacleCount; ++i)
            {
                config.MapPrefabObstacle obstacle = new config.MapPrefabObstacle();
                localObstacleManager.obstacles[i] = obstacle;

                obstacle.id = reader.ReadInt32();
                int vertexCount = reader.ReadInt32();
                obstacle.vertices = new Vector3[vertexCount];
                for (int k = 0; k < vertexCount; ++k)
                {
                    obstacle.vertices[k] = Utils.ReadVector3(reader);
                }
                int indexCount = reader.ReadInt32();
                obstacle.triangleIndices = new int[indexCount];
                for (int k = 0; k < indexCount; ++k)
                {
                    obstacle.triangleIndices[k] = reader.ReadInt32();
                }
            }

            localObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
        }

        void LoadGlobalObstacles(BinaryReader reader)
        {
            var globalObstacleManager = new config.MapGlobalObstacleManager();
            mEditorData.globalObstacleManager = globalObstacleManager;
            globalObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
            globalObstacleManager.obstacleVertices = Utils.ReadVector3Array(reader);
            globalObstacleManager.obstacleIndices = Utils.ReadIntArray(reader);
        }

        config.SLGMakerData mEditorData;
        int mMinorVersion;
    }
}
#endif