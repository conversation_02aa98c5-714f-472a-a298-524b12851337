﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateIslandAndOceanObstacles(PrefabOutlineType type, Vector3 min, Vector3 max, List<IObstacle> obstacles, float agentRadius, float minimumAngle, float maximumArea, bool useDelaunay, bool oceanAreaEnabled, float scaleFactor, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            var combineMeshies = new List<MeshItem>();
            //创建山体等障碍物mesh
            Vector3[] normalObstacleVertices = new Vector3[0];
            int[] normalObstacleIndices = new int[0];
            List<List<Vector3>> holes = null;
            var obstaclePolygons = PolygonAlgorithm.GetCombinedObstaclePolygons(type, obstacles, 0, obstacles.Count - 1, agentRadius, scaleFactor, out holes);
            if (obstaclePolygons.Count > 0)
            {
                var editorMap = Map.currentMap as EditorMap;
                var pool = Triangulator.GetPool(0);
                Triangulator.TriangulatePolygons(obstaclePolygons, holes, useDelaunay, minimumAngle, maximumArea, pool, out normalObstacleVertices, out normalObstacleIndices);
                combineMeshies.Add(new MeshItem(normalObstacleVertices, normalObstacleIndices));
            }

#if false
            var viewer = new BigMeshViewer();
            viewer.Create(null, "normal obstacles", normalObstacleVertices, normalObstacleIndices, false, Color.red);
#endif

            int triangleCount = normalObstacleIndices.Length / 3;
            List<ushort> triangleTypeList = new List<ushort>(triangleCount);
            List<bool> triangleStateList = new List<bool>(triangleCount);
            for (int i = 0; i < triangleCount; ++i)
            {
                triangleTypeList.Add(0);
                triangleStateList.Add(false);
            }

            Vector3[] waterRegionVertices;
            int[] waterRegionIndices;
            //创建海的障碍物mesh
            CreateWaterRegionNavMesh(type, min, max, agentRadius, triangleTypeList, triangleStateList, oceanAreaEnabled, removeSameHoles, out waterRegionVertices, out waterRegionIndices);
            if (waterRegionIndices != null)
            {
                combineMeshies.Add(new MeshItem(waterRegionVertices, waterRegionIndices));
            };

#if false
            var viewer1 = new BigMeshViewer();
            viewer1.Create(null, "water obstacles", waterRegionVertices, waterRegionIndices, false, Color.blue);
#endif

            if (min != Vector3.zero || max != new Vector3(Map.currentMap.mapWidth, 0, Map.currentMap.mapHeight))
            {
                Vector3[] invalidSpaceMeshVertices;
                int[] invalidSpaceMeshIndices;
                CreateInvalidSpaceObstacleMesh(min, max, out invalidSpaceMeshVertices, out invalidSpaceMeshIndices);
                combineMeshies.Add(new MeshItem(invalidSpaceMeshVertices, invalidSpaceMeshIndices));

#if false
                var viewer2 = new BigMeshViewer();
                viewer2.Create(null, "invalid space obstacles", invalidSpaceMeshVertices, invalidSpaceMeshIndices, false, Color.yellow);
#endif
            }

            CombineMesh(combineMeshies, out meshVertices, out meshIndices);
            triangleTypes = triangleTypeList.ToArray();
            triangleStates = triangleStateList.ToArray();
        }
    }
}


#endif