﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionSetBlendTerrainLayerTiles : EditorAction
    {
        public class TileData
        {
            public TileData(int index, int type, int subTypeIndex)
            {
                tileIndex = index;
                tileType = type;
                this.subTypeIndex = subTypeIndex;
            }

            public int tileIndex;
            public int tileType;
            public int subTypeIndex;
        }

        public ActionSetBlendTerrainLayerTiles(int layerID, int prefabGroupIndex, Vector2Int[] pickedTiles, TileData[] oldTileDatas, TileData[] newTileDatas, bool[] drawMasks)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as BlendTerrainLayer;
            mPickedTiles = (Vector2Int[])pickedTiles.Clone();
            mOldTileDatas = oldTileDatas;
            mNewTileDatas = newTileDatas;
            mLayerID = layerID;
            mPrefabGroupIndex = prefabGroupIndex;
            mDescription = string.Format("set {0} layer's tiles", layer.name);
            mDrawMasks = drawMasks;
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                if (mPrefabGroupIndex >= 0)
                {
                    var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);
                    if (group != null && group.isValid)
                    {
                        for (int i = 0; i < 4; ++i)
                        {
                            if (mDrawMasks[i])
                            {
                                layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mNewTileDatas[i].tileIndex, mNewTileDatas[i].tileType, mNewTileDatas[i].subTypeIndex);
                            }
                        }

                        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                        return true;
                    }
                }
            }
            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                for (int i = 0; i < 4; ++i)
                {
                    if (mOldTileDatas[i] != null)
                    {
                        if (mDrawMasks[i])
                        {
                            layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mOldTileDatas[i].tileIndex, mOldTileDatas[i].tileType, mOldTileDatas[i].subTypeIndex);
                        }
                    }
                }
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                return true;
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };
        TileData[] mOldTileDatas;
        TileData[] mNewTileDatas;
        bool[] mDrawMasks;
        Vector2Int[] mPickedTiles;
        int mLayerID;
        int mPrefabGroupIndex;
        string mDescription;
    }
}

#endif