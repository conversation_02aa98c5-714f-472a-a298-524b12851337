﻿ 



 
 

using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public class MapPluginLayerInfo
    {
        public string assemblyName;
        public string namespaceName;
        public string layerClassName;
        //如果runtimeLayerClassName不为空，则在运行时创建这个名字的layer
        public string runtimeLayerClassName;
        public string proxyLayerClassName;
        public string layerTitle;
        public string layerTooltip;
        public string editorSaveFileName;
        public string runtimeSaveFileName;
    }

    public static class MapPlugin
    {
        public static async UniTask LoadPluginList(string jsonFilePath)
        {
            mPluginLayerInfos.Clear();
            if (!string.IsNullOrEmpty(jsonFilePath))
            {
                var bytes = await MapModuleResourceMgr.LoadTextBytesAsync(jsonFilePath);
                if (bytes != null)
                {
                    string configData = System.Text.Encoding.UTF8.GetString(bytes);
                    var configDict = JSONParser.Deserialize(configData) as Dictionary<string, object>;
                    object layers = null;
                    configDict.TryGetValue(MapCoreDef.MAP_PLUGIN_LAYERS, out layers);
                    if (layers != null)
                    {
                        List<object> layersConfig = layers as List<object>;
                        for (int i = 0; i < layersConfig.Count; ++i)
                        {
                            MapPluginLayerInfo info = new MapPluginLayerInfo();
                            var layerConfig = layersConfig[i] as Dictionary<string, object>;
                            //默认插件实现在项目工程内
                            info.assemblyName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_ASSEMBLY, "Assembly-CSharp");
                            info.namespaceName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_NAMESPACE, "");
                            info.layerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_NAME, "");
                            info.runtimeLayerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_RUNTIME_LAYER_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.layerClassName));
                            info.proxyLayerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_PROXY_LAYER_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.proxyLayerClassName));
                            info.layerTitle = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_TITLE, "");
                            info.layerTooltip = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_TOOLTIP, "");
                            info.editorSaveFileName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_EDITOR_SAVE_FILE_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.editorSaveFileName));
                            info.runtimeSaveFileName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_RUNTIME_SAVE_FILE_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.runtimeSaveFileName));
                            RegisterPluginLayerInfo(info);
                        }
                    }
                } 
            }
        }

        public static void LoadPluginListEditor(string jsonFilePath)
        {
            mPluginLayerInfos.Clear();
            if (!string.IsNullOrEmpty(jsonFilePath))
            {
                var bytes = MapModuleResourceMgr.LoadTextBytes(jsonFilePath, true);
                if (bytes != null)
                {
                    string configData = System.Text.Encoding.UTF8.GetString(bytes);
                    var configDict = JSONParser.Deserialize(configData) as Dictionary<string, object>;
                    object layers = null;
                    configDict.TryGetValue(MapCoreDef.MAP_PLUGIN_LAYERS, out layers);
                    if (layers != null)
                    {
                        List<object> layersConfig = layers as List<object>;
                        for (int i = 0; i < layersConfig.Count; ++i)
                        {
                            MapPluginLayerInfo info = new MapPluginLayerInfo();
                            var layerConfig = layersConfig[i] as Dictionary<string, object>;
                            //默认插件实现在项目工程内
                            info.assemblyName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_ASSEMBLY, "Assembly-CSharp");
                            info.namespaceName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_NAMESPACE, "");
                            info.layerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_NAME, "");
                            info.runtimeLayerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_RUNTIME_LAYER_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.layerClassName));
                            info.proxyLayerClassName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_PROXY_LAYER_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.proxyLayerClassName));
                            info.layerTitle = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_TITLE, "");
                            info.layerTooltip = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_TOOLTIP, "");
                            info.editorSaveFileName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_EDITOR_SAVE_FILE_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.editorSaveFileName));
                            info.runtimeSaveFileName = JsonHelper.ReadString(layerConfig, MapCoreDef.MAP_PLUGIN_LAYER_RUNTIME_SAVE_FILE_NAME, "");
                            Debug.Assert(!string.IsNullOrEmpty(info.runtimeSaveFileName));
                            RegisterPluginLayerInfo(info);
                        }
                    }
                }
            }
        }

        public static void SavePluginList(string jsonFilePath)
        {
            Dictionary<string, object> root = new Dictionary<string, object>();
            List<object> layers = new List<object>();
            root[MapCoreDef.MAP_PLUGIN_LAYERS] = layers;
            for (int i = 0; i < mPluginLayerInfos.Count; ++i)
            {
                Dictionary<object, string> layerConfig = new Dictionary<object, string>();
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_NAMESPACE] = mPluginLayerInfos[i].namespaceName;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_ASSEMBLY] = mPluginLayerInfos[i].assemblyName;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_NAME] = mPluginLayerInfos[i].layerClassName;
                layerConfig[MapCoreDef.MAP_PLUGIN_RUNTIME_LAYER_NAME] = mPluginLayerInfos[i].runtimeLayerClassName;
                layerConfig[MapCoreDef.MAP_PLUGIN_PROXY_LAYER_NAME] = mPluginLayerInfos[i].proxyLayerClassName;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_TITLE] = mPluginLayerInfos[i].layerTitle;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_TOOLTIP] = mPluginLayerInfos[i].layerTooltip;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_EDITOR_SAVE_FILE_NAME] = mPluginLayerInfos[i].editorSaveFileName;
                layerConfig[MapCoreDef.MAP_PLUGIN_LAYER_RUNTIME_SAVE_FILE_NAME] = mPluginLayerInfos[i].runtimeSaveFileName;
                layers.Add(layerConfig);
            }

            var code = JSONParser.Serialize(root);
            File.WriteAllText(jsonFilePath, code);
        }

        public static Type GetPluginLayerLogicType(string layerName)
        {
            var info = GetPluginLayerInfo(layerName);
            if (info != null)
            {
                string fullTypeName = null;
                if (string.IsNullOrEmpty(info.namespaceName))
                {
                    fullTypeName = $"{layerName}Logic, {info.assemblyName}";
                }
                else
                {
                    fullTypeName = $"{info.namespaceName}.{layerName}Logic, {info.assemblyName}";
                }
                var type = Type.GetType(fullTypeName);
                return type;
            }
            return null;
        }

        public static MapPluginLayerBase CreatePluginLayer(string layerName, Map map)
        {
            var info = GetPluginLayerInfo(layerName);
            if (info != null)
            {
                string fullTypeName = null;
                if (string.IsNullOrEmpty(info.namespaceName))
                {
                    fullTypeName = $"{layerName}, {info.assemblyName}";
                }
                else
                {
                    fullTypeName = $"{info.namespaceName}.{layerName}, {info.assemblyName}";
                }
                var type = Type.GetType(fullTypeName);
                if (type != null)
                {
                    var layer = Activator.CreateInstance(type, map) as MapPluginLayerBase;
                    layer.pluginLayerInfo = info;
                    return layer;
                }
            }
            return null;
        }

        public static Type GetPluginProxyLayerType(string layerName)
        {
            var info = GetPluginLayerInfo(layerName);
            if (info != null)
            {
                string fullTypeName = null;
                if (string.IsNullOrEmpty(info.namespaceName))
                {
                    fullTypeName = $"{info.proxyLayerClassName}, {info.assemblyName}";
                }
                else
                {
                    fullTypeName = $"{info.namespaceName}.{info.proxyLayerClassName}, {info.assemblyName}";
                }
                var type = Type.GetType(fullTypeName);
                return type;
            }
            return null;
        }

        public static MapPluginLayerInfo GetPluginLayerInfo(string layerName)
        {
            for (int i = 0; i < mPluginLayerInfos.Count; ++i)
            {
                if (mPluginLayerInfos[i].runtimeLayerClassName == layerName || mPluginLayerInfos[i].layerClassName == layerName)
                {
                    return mPluginLayerInfos[i];
                }
            }
            return null;
        }

        public static bool IsPluginLayer(string layerName)
        {
            return GetPluginLayerInfo(layerName) != null;
        }

        public static string GetRuntimeLayerName(string layerName)
        {
            var info = GetPluginLayerInfo(layerName);
            if (info != null)
            {
                if (!string.IsNullOrEmpty(info.runtimeLayerClassName))
                {
                    return info.runtimeLayerClassName;
                }
                return info.layerClassName;
            }
            return layerName;
        }

        public static void RegisterPluginLayerInfo(MapPluginLayerInfo info)
        {
            if (GetPluginLayerInfo(info.layerClassName) == null)
            {
                mPluginLayerInfos.Add(info);
            }
            else
            {
                Debug.LogError($"{info.layerClassName} has already been registered!");
            }
        }

        public static void UnregisterPluginLayerInfo(MapPluginLayerInfo info)
        {
            for (int i = 0; i < mPluginLayerInfos.Count; ++i)
            {
                if (mPluginLayerInfos[i] == info)
                {
                    mPluginLayerInfos.RemoveAt(i);
                    return;
                }
            }
        }

        public static Type GetPluginLayerSettingWindowType(string layerName)
        {
            var info = GetPluginLayerInfo(layerName);
            if (info != null)
            {
                string fullTypeName = null;
                if (string.IsNullOrEmpty(info.namespaceName))
                {
                    fullTypeName = $"{layerName}, {info.assemblyName}";
                }
                else
                {
                    fullTypeName = $"{info.namespaceName}.{info.layerClassName}SettingWindow, {info.assemblyName}";
                }
                var type = Type.GetType(fullTypeName);
                return type;
            }
            return null;
        }

        public static string CheckDuplication(MapPluginLayerInfo layerInfo)
        {
            for (int i = 0; i < mPluginLayerInfos.Count; ++i)
            {
                if (layerInfo == mPluginLayerInfos[i])
                {
                    continue;
                }

                if (mPluginLayerInfos[i].layerClassName == layerInfo.layerClassName)
                {
                    return $"duplicated class name {layerInfo.layerClassName}";
                }

                if (mPluginLayerInfos[i].proxyLayerClassName == layerInfo.proxyLayerClassName)
                {
                    return $"duplicated proxy class name {layerInfo.proxyLayerClassName}";
                }

                if (mPluginLayerInfos[i].editorSaveFileName == layerInfo.editorSaveFileName)
                {
                    return $"duplicated editor save file name {layerInfo.editorSaveFileName}";
                }

                if (mPluginLayerInfos[i].runtimeSaveFileName == layerInfo.runtimeSaveFileName)
                {
                    return $"duplicated runtime save file name {layerInfo.runtimeSaveFileName}";
                }
            }

            return "";
        }

        public static List<MapPluginLayerInfo> pluginLayerInfos { get { return mPluginLayerInfos; } }

        static List<MapPluginLayerInfo> mPluginLayerInfos = new List<MapPluginLayerInfo>();
    }
}
