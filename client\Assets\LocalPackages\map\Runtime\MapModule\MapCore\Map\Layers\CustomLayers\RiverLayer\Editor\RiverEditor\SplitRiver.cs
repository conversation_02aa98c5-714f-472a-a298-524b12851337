﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    //将河流分段
    public class SplitRiver : RiverEditorTool
    {
        enum State
        {
            PickStartVertex,
            PickEndVertex,
            CreateSplitter,
        }

        public SplitRiver(RiverEditor editor) : base(editor)
        {
            var parent = Map.currentMap.view.root;
            mSplitterIndicator = new PolygonRiverSplitterModel(Vector3.zero, Vector3.zero, mEditor.layer.displayVertexRadius, parent, false);
            mSplitterIndicator.SetActive(false);
        }

        public override void OnDestroy()
        {
            mSplitterIndicator.OnDestroy();
        }

        public override void OnEnabled()
        {
        }
        public override void OnDisabled()
        {
            ResetSplitter();
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.type == EventType.MouseDown && e.button == 0 && e.alt == false)
            {
                if (e.control)
                {
                    int startVertex = mEditor.selectedVertexIndex;
                    if (mState == State.PickEndVertex && startVertex >= 0)
                    {
                        PickVertex(pos);
                        if (mEndVertex >= 0)
                        {
                            mSplitterIndicator.SetColor(false, Color.red);
                            mSplitterIndicator.SetPosition(false, mEditor.layer.GetVertexPos(mEditor.layer.displayType, mEditor.selectedObjectID, mEndVertex));
                            mSplitterIndicator.SetSize(false, mEditor.layer.displayVertexRadius);
                            mState = State.CreateSplitter;
                        }
                    }
                    else if (mState == State.PickStartVertex)
                    {
                        mEditor.Pick(pos);
                        if (mEditor.selectedVertexIndex >= 0)
                        {
                            mState = State.PickEndVertex;
                            mSplitterIndicator.SetActive(true);
                            mSplitterIndicator.SetColor(true, Color.red);
                            mSplitterIndicator.SetPosition(true, mEditor.layer.GetVertexPos(mEditor.layer.displayType, mEditor.selectedObjectID, mEditor.selectedVertexIndex));
                            mSplitterIndicator.SetSize(true, mEditor.layer.displayVertexRadius);
                        }
                    }
                    else if (mState == State.CreateSplitter)
                    {
                        var startPos = mSplitterIndicator.GetPosition(true);
                        var endPos = mSplitterIndicator.GetPosition(false);
                        bool valid = CheckSplitterValidation(startPos, endPos);
                        if (valid)
                        {
                            var act = new ActionAddPolygonRiverSplitter(mEditor.layer.id, mEditor.selectedObjectID, mSplitterIndicator.GetPosition(true), mSplitterIndicator.GetPosition(false));
                            ActionManager.instance.PushAction(act);
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Invalid splitter!", "OK");
                        }
                        ResetSplitter();
                    }
                }
                else
                {
                    ResetSplitter();
                    mEditor.Pick(pos);
                    int selectedRiverID;
                    mPickedSplitterIndex = mEditor.layer.PickSplitter(pos, out selectedRiverID);
                    if (mPickedSplitterIndex >= 0)
                    {
                        var river = Map.currentMap.FindObject(selectedRiverID) as PolygonRiverData;
                        mEditor.SetSelection(river, 0);
                        mEditor.layer.SetSplitterColor(selectedRiverID, mPickedSplitterIndex, Color.green);
                    }
                }
            }

            HandleUtility.AddDefaultControl(0);
        }

        bool CheckSplitterValidation(Vector3 startPos, Vector3 endPos)
        {
            int selectedRiverID = mEditor.selectedObjectID;
            if (selectedRiverID == 0)
            {
                return false;
            }

            bool intersected = mEditor.layer.IsIntersectedWithSplitter(selectedRiverID, startPos, endPos);
            if (intersected)
            {
                return false;
            }

            var outlineType = mEditor.layer.displayType;
            bool contains = mEditor.layer.GetSplitterIndex(selectedRiverID, startPos, endPos) >= 0;
            if (contains)
            {
                return false;
            }

            var startIndex = mEditor.layer.GetVertexIndex(outlineType, selectedRiverID, startPos);
            var endIndex = mEditor.layer.GetVertexIndex(outlineType, selectedRiverID, endPos);
            if (startIndex >= 0 && endIndex >= 0 && Mathf.Abs(startIndex - endIndex) <= 1)
            {
                return false;
            }

            return true;
        }

        void ResetSplitter()
        {
            if (mPickedSplitterIndex >= 0)
            {
                mEditor.layer.SetSplitterColor(mEditor.selectedObjectID, mPickedSplitterIndex, Color.yellow);
                mPickedSplitterIndex = -1;
            }

            mSplitterIndicator.SetActive(false);
            mSplitterIndicator.SetColor(true, Color.green);
            mSplitterIndicator.SetColor(false, Color.green);
            mEndVertex = -1;
            mState = State.PickStartVertex;
        }

        void PickVertex(Vector3 pos)
        {
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as PolygonRiverData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mEditor.layer.displayType);
                float sqrr = collisionData.displayRadius * collisionData.displayRadius;
                for (int i = 0; i < vertices.Count; ++i)
                {
                    var d = vertices[i] - pos;
                    if (d.sqrMagnitude <= sqrr)
                    {
                        hitVertex = i;
                        break;
                    }
                }

                if (hitVertex >= 0)
                {
                    mEndVertex = hitVertex;
                    return true;
                }

                return false;
            };

            mEditor.layer.Traverse(func);
        }

        public override void DrawScene()
        {
        }

        protected override void DrawGUIImpl()
        {
            if (mEditor.selectedObjectID != 0)
            {
                float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "河流顶点的显示大小"), mEditor.layer.displayVertexRadius);
                if (radius != mEditor.layer.displayVertexRadius)
                {
                    mEditor.layer.SetVertexDisplayRadius(radius);
                }
                bool oldState = mEditor.layer.IsSplitterVisible(mEditor.selectedObjectID);
                bool newState = EditorGUILayout.Toggle(new GUIContent("Show Splitter", "是否显示Splitter"), oldState);
                if (newState != oldState)
                {
                    mEditor.layer.ShowSplitter(mEditor.selectedObjectID, newState);
                }

                EditorGUILayout.LabelField("Pick Split Line Vertex: Ctrl + Mouse Left Button");
                EditorGUILayout.LabelField("Pick Split Line: Mouse Left Button");
            }
        }

        protected override void DrawSubMenu(GenericMenu menu)
        {
            if (mEditor.selectedObjectID != 0 && mPickedSplitterIndex >= 0)
            {
                menu.AddItem(new GUIContent("Delete Splitter"), false, DeleteSplitter);
            }
        }

        void DeleteSplitter()
        {
            if (mEditor.selectedObjectID != 0 && mPickedSplitterIndex >= 0)
            {
                var act = new ActionRemovePolygonRiverSplitter(mEditor.layer.id, mEditor.selectedObjectID, mPickedSplitterIndex);
                ActionManager.instance.PushAction(act);
                ResetSplitter();
            }
        }

        public override RiverEditorToolType type { get { return RiverEditorToolType.Split; } }

        int mEndVertex = -1;
        int mPickedSplitterIndex = -1;
        PolygonRiverSplitterModel mSplitterIndicator;
        State mState = State.PickStartVertex;
    }
}

#endif