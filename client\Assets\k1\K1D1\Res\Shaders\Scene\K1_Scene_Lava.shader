﻿//场景中的液体流动效果（内城场景合成区）
Shader "K1/Scene/Lava"
{
    Properties
    {
		_Wave("_Wave", Range(0, 0.03)) = 0.01
		_Speed("_Speed", Range(0, 2)) = 1

		_MainTex("MainTex", 2D) = "white" {}
		_NoiseTex("Noise", 2D) = "white" {}
    }
    SubShader
    {
		Tags
		{
			"Queue" = "Transparent"
			"IgnoreProjector" = "True"
			"RenderType" = "Transparent"
			"PreviewType" = "Plane"
			"CanUseSpriteAtlas" = "True"
		}

		Cull Off
		Lighting Off
		ZWrite Off
		ZTest LEqual
		Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
			float _Wave;
			float _Speed;

			sampler2D _MainTex;
			half4 _MainTex_ST;
			sampler2D _NoiseTex;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed3 noiseCol = UnpackNormal(tex2D(_NoiseTex, i.uv + _Time.x * _Speed));
				//fixed lerpFix = step(i.uv.y, 0.8) * 0.2 + step(0.8, i.uv.y) * i.uv.y;
				float2 sampleUv = float2(i.uv.x, i.uv.y + _Wave * (sin(noiseCol.r)) * lerp(2, 0.5, i.uv.y));
				fixed4 waveCol = tex2D(_MainTex, sampleUv);

                return waveCol;
            }
            ENDCG
        }
    }
}
