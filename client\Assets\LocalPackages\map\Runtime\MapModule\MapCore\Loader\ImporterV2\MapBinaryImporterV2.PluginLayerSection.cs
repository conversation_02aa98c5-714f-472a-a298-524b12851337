﻿ 



 
 


using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapPluginLayerList Load<PERSON>lugin<PERSON>ayer<PERSON>ist(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.PluginLayerList);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            int count = reader.ReadInt32();
            config.MapPluginLayerList ret = new config.MapPluginLayerList();
            ret.pluginLayerNames = new List<string>();
            for (int i = 0; i < count; ++i)
            {
                ret.pluginLayerNames.Add(Utils.ReadString(reader));
            }
            //-------------------version 1 end------------------------------
            return ret;
        }
    }
}
