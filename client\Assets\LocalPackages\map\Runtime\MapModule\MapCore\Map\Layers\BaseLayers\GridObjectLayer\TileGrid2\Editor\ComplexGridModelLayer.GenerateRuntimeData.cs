﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace TFW.Map
{
    public partial class ComplexGridModelLayer : MapLayerBase
    {
        static Dictionary<int, int> mSharedObjectIDs = new Dictionary<int, int>();
        static int mCurrentObjectID = 1;

        class TileData
        {
            public TileData(int x, int y, int lodCount)
            {
                this.x = x;
                this.y = y;
                lodObjects = new List<IComplexGridModelData>[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    lodObjects[i] = new List<IComplexGridModelData>();
                }
            }

            public bool IsEmpty()
            {
                for (int i = 0; i < lodObjects.Length; ++i)
                {
                    if (lodObjects[i].Count > 0)
                    {
                        return false;
                    }
                }
                return true;
            }

            public int x;
            public int y;
            public List<IComplexGridModelData>[] lodObjects;
        }

        class RemovableObject
        {
            public class Data
            {
                public Data(int bigTileIndex, int objectIndex)
                {
                    this.bigTileIndex = bigTileIndex;
                    this.objectIndex = objectIndex;
                }

                public int bigTileIndex;
                public int objectIndex;
            }

            //每个lod中一个view id对应多个格子的object
            public class LOD
            {
                public int lod;
                public int viewID;
                public List<Data> objects = new List<Data>();
            }
            public Vector3 position;
            public List<LOD> viewIDsInEachLOD = new List<LOD>();

            public RemovableObject(Vector3 pos)
            {
                position = pos;
            }

            public void Add(int viewID, int lodIndex, int bigTileIndex, int objectIndex)
            {
                List<Data> data = null;
                foreach (var lod in viewIDsInEachLOD)
                {
                    if (lod.lod == lodIndex && lod.viewID == viewID)
                    {
                        data = lod.objects;
                        break;
                    }
                }
                if (data == null)
                {
                    var lod = new LOD();
                    lod.lod = lodIndex;
                    lod.viewID = viewID;
                    viewIDsInEachLOD.Add(lod);
                    data = lod.objects;
                }
                data.Add(new Data(bigTileIndex, objectIndex));
            }
        }

        class SpecialAreaData
        {
            public SpecialAreaData(SpecialAreaConfig config)
            {
                mConfig = config;
            }

            public bool Contains(Vector3 position)
            {
                if (config.shape == SpecialAreaShape.Circle)
                {
                    float radius = config.width * 0.5f;
                    radius *= radius;
                    float distance2 = (config.transform.position - position).sqrMagnitude;
                    if (distance2 <= radius)
                    {
                        return true;
                    }
                }
                else if (config.shape == SpecialAreaShape.Rectangle)
                {
                    var pos = config.transform.position;
                    float minX = pos.x - config.width * 0.5f;
                    float minZ = pos.z - config.height * 0.5f;
                    float maxX = pos.x + config.width * 0.5f;
                    float maxZ = pos.z + config.height * 0.5f;
                    if (position.x >= minX && position.x <= maxX &&
                        position.z >= minZ && position.z <= maxZ)
                    {
                        return true;
                    }
                }

                return false;
            }

            public void AddViewID(int viewID)
            {
                if (mObjectViewIDs.Contains(viewID) == false)
                {
                    mObjectViewIDs.Add(viewID);
                }
            }

            public List<int> objectViewIDs { get { return mObjectViewIDs; } }
            public SpecialAreaConfig config { get { return mConfig; } }

            SpecialAreaConfig mConfig;
            //在special area中的装饰物view id
            List<int> mObjectViewIDs = new List<int>();
        }

        class DisassembledTileObject : IComplexGridModelData
        {
            public DisassembledTileObject(int id, Vector3 pos, Quaternion rotation, Vector3 scale, string prefabPath, short prefabInitInfoIndex, string tag, bool hasPrefabOutline)
            {
                mEntityID = id;
                mPosition = pos;
                mRotation = rotation;
                mScale = scale;
                mPrefabInfoIndex = prefabInitInfoIndex;
                mPrefabPath = prefabPath;
                mTag = tag;
                mHasPrefabOutline = hasPrefabOutline;
            }

            public bool IsDynamicEntity()
            {
                return false;
            }
            public int GetEntityID()
            {
                return mEntityID;
            }
            public ModelTemplate GetModelTemplate() { Debug.LogError("Not implemented!"); return null; }
            public int GetModelTemplateID() { Debug.LogError("Not implemented!"); return 0; }
            public void OnHide() { }
            public void OnShow() { }
            public void OnInit(GameObject obj) { }
            public void OnZoomChange(float zoom) { }
            public bool IgnoreViewport() { return false; }
            public string GetAssetPath(int lod = 0) { return mPrefabPath; }
            public Rect GetBounds() { Debug.LogError("Not implemented!"); return new Rect(); }
            public Vector3 GetPosition() { return mPosition; }
            public Vector3 GetScale() { return mScale; }
            public Quaternion GetRotation() { return mRotation; }
            public void SetPosition(Vector3 pos) { Debug.LogError("Not implemented!"); }
            public void SetScale(Vector3 scale) { Debug.LogError("Not implemented!"); }
            public void SetRotation(Quaternion rotation) { Debug.LogError("Not implemented!"); }
            public bool IsObjActive() { Debug.LogError("Not implemented!"); return false; }
            public bool SetObjActive(bool active) { Debug.LogError("Not implemented!"); return false; }
            public bool HasFlag(int flag) { Debug.LogError("Not implemented!"); return false; }
            public void SetFlag(int flag) { Debug.LogError("Not implemented!"); }
            public void AddFlag(int flag) { Debug.LogError("Not implemented!"); }
            public void RemoveFlag(int flag) { Debug.LogError("Not implemented!"); }
            public int GetFlag() { Debug.LogError("Not implemented!"); return 0; }
            public int GetModelLODGroupID() { Debug.LogError("Not implemented!"); return 0; }
            public void CalculateBounds() { Debug.LogError("Not implemented!"); }
            public void ShowGrid() { Debug.LogError("Not implemented!"); }
            public void HideGrid() { Debug.LogError("Not implemented!"); }
            public short modifiedTransformDataIndex { get { return mPrefabInfoIndex; } set { mPrefabInfoIndex = value; } }
            public string gameObjectTag { get { return mTag; } set { mTag = value; } }
            public string objectTag { get { return ""; } set { } }
            public ushort occupiedGridCount { get { Debug.LogError("Not implemented!"); return 0; } set { } }
            public bool useRenderTextureModel { get { return false; } set { } }
            public ushort activeGridCount { 
                get { 
                    Debug.LogError("Not implemented!"); 
                    return 0; 
                } set { } }
            public byte lod { get { Debug.LogError("Not implemented!"); return 0; } }
            public bool hasPrefabOutline { get { return mHasPrefabOutline; } }

            int mEntityID;
            string mPrefabPath;
            Vector3 mPosition;
            Quaternion mRotation;
            Vector3 mScale;
            string mTag;
            short mPrefabInfoIndex;
            bool mHasPrefabOutline;
        }

        List<IComplexGridModelData> GetNoneEmptyGridObjects()
        {
            List<IComplexGridModelData> allObjects = new List<IComplexGridModelData>();
            int cols = layerData.horizontalTileCount;
            int rows = layerData.verticalTileCount;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var objects = layerData.GetObjectsInGrid(0, j, i);
                    foreach (var obj in objects)
                    {
                        if (allObjects.Contains(obj) == false)
                        {
                            allObjects.Add(obj);
                        }
                    }
                }
            }
            return allObjects;
        }

        public void ExportData()
        {
            if (string.IsNullOrEmpty(SLGMakerEditor.instance.exportFolder))
            {
                EditorUtility.DisplayDialog("Error", "Invalid export folder", "OK");
                return;
            }

            List<int> renderTextureObjectIDs = new List<int>();
            if (mLayerData.enableCullIntersectedObjects)
            {
                List<IComplexGridModelData> allObjects = new List<IComplexGridModelData>();
                mLayerData.GetAllObjects(allObjects);

                foreach (var obj in allObjects)
                {
                    var complexData = obj as ComplexGridModelData;
                    if (complexData.useRenderTextureModel)
                    {
                        renderTextureObjectIDs.Add(complexData.id);
                        //先切换成3d视图
                        mLayerData.ChangeObjectModel(complexData.id, false);
                    }
                }

                if (!MakeSureAllObjectsAreIn3DModel())
                {
                    return;
                }
            }

            int nLODs = lodCount;

            mSharedObjectIDs.Clear();
            mCurrentObjectID = 1;

            bool isDecorationBorderLayer = this is DecorationBorderLayer;

            List<RemovableObject> removableObjects = new List<RemovableObject>();

            //delete old assets
            string bigTileDataFolder = null;
            if (isDecorationBorderLayer)
            {
                bigTileDataFolder = MapCoreDef.GetFullBorderBigTileDataFolderPath(SLGMakerEditor.instance.exportFolder);
            }
            else
            {
                bigTileDataFolder = MapCoreDef.GetFullBigTileDataFolderPath(SLGMakerEditor.instance.exportFolder);
            }
            FileUtil.DeleteFileOrDirectory(bigTileDataFolder);
            AssetDatabase.Refresh();

            if (!Directory.Exists(bigTileDataFolder))
            {
                Directory.CreateDirectory(bigTileDataFolder);
            }
            AssetDatabase.Refresh();

            //save prefab infos
            List<TileData> allTiles = new List<TileData>();
            List<PrefabInitInfo2> allPrefabsInfo = new List<PrefabInitInfo2>();
            int verticalTileCount = this.verticalTileCount;
            int horizontalTileCount = this.horizontalTileCount;
            if (mLayerData.objectPlacementSetting.useMapLargeTile)
            {
                List<IComplexGridModelData> objectsInTile = GetNoneEmptyGridObjects();
                if (objectsInTile.Count == 0)
                {
                    Debug.LogError("Invalid Map Large Tile!");
                    return;
                }
                int startUniqueID = 1;
                if (objectsInTile != null)
                {
                    for (int k = 0; k < objectsInTile.Count; ++k)
                    {
                        var tiles = DisassembleMapLargeTile(ref startUniqueID, objectsInTile[k], allPrefabsInfo, out horizontalTileCount, out verticalTileCount);
                        for (int i = 0; i < tiles.Length; ++i)
                        {
                            if (tiles[i] != null && !tiles[i].IsEmpty())
                            {
                                var tile = GetTile(allTiles, tiles[i].x, tiles[i].y);
                                if (tile == null)
                                {
                                    allTiles.Add(tiles[i]);
                                }
                                else
                                {
                                    MergeTile(tile, tiles[i]);
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        for (int lod = 0; lod < nLODs; ++lod)
                        {
                            List<IComplexGridModelData> objectsInTile = layerData.GetObjectsInGrid(lod, j, i);
                            CalculateUniquePrefabInfo(objectsInTile, allPrefabsInfo);
                        }
                    }
                }
            }

            if (!mLayerData.objectPlacementSetting.useMapLargeTile) {
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        bool prefabIsNeeded = CheckIfPrefabIsNeededForThisTile(j, i);
                        if (prefabIsNeeded)
                        {
                            TileData tileData = new TileData(j, i, nLODs);
                            for (int lod = 0; lod < nLODs; ++lod)
                            {
                                //获取格子在某个lod下的物体
                                List<IComplexGridModelData> objectsInTile = layerData.GetObjectsInGrid(lod, j, i);
                                tileData.lodObjects[lod] = objectsInTile;
                            }

                            if (!tileData.IsEmpty())
                            {
                                allTiles.Add(tileData);
                            }
                        }
                    }
                }
            }

            SavePrefabInfo(allTiles, allPrefabsInfo, horizontalTileCount, verticalTileCount, isDecorationBorderLayer);

            List<SpecialAreaData> specialAreas = new List<SpecialAreaData>();
            List<SpecialAreaConfig> specialAreaConfigs = mLayerView.GetSpecialAreaConfigs();
            foreach (var config in specialAreaConfigs)
            {
                var areaData = new SpecialAreaData(config);
                specialAreas.Add(areaData);
            }

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.AllBigTileDataVersion.majorVersion);
            writer.Write(VersionSetting.AllBigTileDataVersion.minorVersion);

            Cull(allTiles, true);

            writer.Write(allTiles.Count);
            for (int i = 0; i < allTiles.Count; ++i)
            {
                EditorUtility.DisplayProgressBar($"Create Big Tile Data {allTiles[i].x}_{allTiles[i].y}", "Please wait", (float)i / (allTiles.Count - 1));
                CreateBigTileData(allTiles[i], tileWidth, tileHeight, allPrefabsInfo, removableObjects, specialAreas, writer);
            }

            SaveSpecialAreas(writer, specialAreas);

            string tilePath = null;
            if (isDecorationBorderLayer)
            {
                tilePath = MapCoreDef.GetMapBorderBigTileDataPath(SLGMakerEditor.instance.exportFolder);
            }
            else
            {
                tilePath = MapCoreDef.GetMapBigTileDataPath(SLGMakerEditor.instance.exportFolder);
            }
            var data = stream.ToArray();
            File.WriteAllBytes(tilePath, data);
            writer.Close();

            SaveRemovableObjects(removableObjects);

            if (mLayerData.enableCullIntersectedObjects)
            {
                //还原render texture
                foreach (var id in renderTextureObjectIDs)
                {
                    mLayerData.ChangeObjectModel(id, true);
                }
            }

            AssetDatabase.Refresh();

            EditorUtility.ClearProgressBar();
        }

        class ChildPrefabInfo
        {
            public ChildPrefabInfo(GameObject prefab, GameObject prefabInstance)
            {
                this.prefab = prefab;
                this.prefabInstance = prefabInstance;
            }

            public GameObject prefab;
            public GameObject prefabInstance;
        }

        //分解地图大小的tile,根据layer设置的lod数来生成runtime tile data
        TileData[] DisassembleMapLargeTile(ref int startUniqueID, IComplexGridModelData mapLargeTileData, List<PrefabInitInfo2> allPrefabInfo, out int horizontalTileCount, out int verticalTileCount)
        {
            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            int nLODs = lodCount;

            var realLayerSize = mLayerData.realLayerBounds.size;
            var lowerLeftCornerPos = mLayerData.realLayerBounds.min;
            horizontalTileCount = Mathf.CeilToInt(realLayerSize.x / tileWidth);
            verticalTileCount = Mathf.CeilToInt(realLayerSize.y / tileHeight);
            TileData[] allTiles = new TileData[horizontalTileCount * verticalTileCount];

            int maxLODCount = Mathf.Min(mapLargeTileData.GetModelTemplate().lodCount, nLODs);
            if (maxLODCount == 0)
            {
                EditorUtility.DisplayDialog("Error", $"Invalid lod tile name {mapLargeTileData.GetModelTemplate().GetLODPrefabPath(0)}", "OK");
                return allTiles;
            }

            List<ChildPrefabInfo> childrenPrefabs = new List<ChildPrefabInfo>();
            for (int lod = 0; lod < maxLODCount; ++lod)
            {
                string lodPrefabPath = mapLargeTileData.GetAssetPath(lod);
                var tilePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(lodPrefabPath);
                if (tilePrefab != null)
                {
                    int n = tilePrefab.transform.childCount;

                    for (int i = 0; i < n; ++i)
                    {
                        var childTransform = tilePrefab.transform.GetChild(i);
                        var childPrefabInstance = childTransform.gameObject;

                        childrenPrefabs.Clear();
                        if (childPrefabInstance.GetComponent<DecorationObjectGroup>() != null)
                        {
                            //分解第一级根节点
                            GetChildrenPrefabs(childPrefabInstance, childrenPrefabs);
                        }
                        else
                        {
                            //获取prefab instance使用的prefab
                            var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);
                            if (childPrefab != null)
                            {
                                childrenPrefabs.Add(new ChildPrefabInfo(childPrefab, childPrefabInstance));
                            }
                            else
                            {
                                Debug.LogError($"invalid child prefab {childPrefabInstance.name}");
                            }
                        }

                        foreach (var childPrefabInfo in childrenPrefabs)
                        {
                            string childPrefabAssetPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(childPrefabInfo.prefab);

                            var childGameObject = childPrefabInfo.prefabInstance;

                            int prefabInitInfoIndex = -1;
                            var offset = new Vector3(map.mapWidth * 0.5f, 0, map.mapHeight * 0.5f);
                            var pos = childGameObject.transform.position + offset;

                            if (blendTerrainLayer != null)
                            {
                                float terrainHeight = blendTerrainLayer.GetHeightAtPos(pos.x, pos.z);
                                if (terrainHeight != 0)
                                {
                                    pos.y += terrainHeight;
                                }
                            }
                            var rot = childGameObject.transform.rotation;
                            var scale = childGameObject.transform.lossyScale;
                            for (int k = 0; k < allPrefabInfo.Count; ++k)
                            {
                                if (
                                    allPrefabInfo[k].prefabPathForEachCustomType[0] == childPrefabAssetPath &&
                                    allPrefabInfo[k].rotation == rot &&
                                    allPrefabInfo[k].scale == scale &&
                                    Mathf.Approximately(allPrefabInfo[k].y, pos.y))
                                {
                                    prefabInitInfoIndex = k;
                                    break;
                                }
                            }

                            Rect childBounds;
                            if (prefabInitInfoIndex == -1)
                            {
                                childBounds = GameObjectBoundsCalculator.CalculateRect(childPrefabInfo.prefab, rot, scale);
                                if (childGameObject.tag == MapCoreDef.MAP_ALWAYS_VISIBLE_OBJECT)
                                {
                                    //temp code,扩大bounds一定范围
                                    childBounds = Utils.ExpandRect(childBounds, MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE * 4);
                                }
                                var prefabInfo = new PrefabInitInfo2(new List<string>() { childPrefabAssetPath }, childBounds.xMin, childBounds.yMin, childBounds.width, childBounds.height, rot, scale, pos.y);
                                allPrefabInfo.Add(prefabInfo);
                                prefabInitInfoIndex = allPrefabInfo.Count - 1;
                            }
                            else
                            {
                                childBounds = new Rect(allPrefabInfo[prefabInitInfoIndex].boundsMinX, allPrefabInfo[prefabInitInfoIndex].boundsMinZ, allPrefabInfo[prefabInitInfoIndex].boundsWidth, allPrefabInfo[prefabInitInfoIndex].boundsHeight);
                            }

                            Debug.Assert(prefabInitInfoIndex < 50000);

                            bool hasPrefabOutline = childGameObject.GetComponent<PrefabOutline>() != null;
                            var obj = new DisassembledTileObject(startUniqueID, pos, rot, scale, childPrefabAssetPath, (short)prefabInitInfoIndex, childGameObject.tag, hasPrefabOutline);

                            AddToGrid(allTiles, lod, obj, childBounds, horizontalTileCount, verticalTileCount, lowerLeftCornerPos);

                            ++startUniqueID;
                        }
                    }
                }
            }

            return allTiles;
        }

        void GetChildrenPrefabs(GameObject groupPrefabInstance, List<ChildPrefabInfo> childrenPrefabs)
        {
            int nChildren = groupPrefabInstance.transform.childCount;
            for (int i = 0; i < nChildren; ++i)
            {
                var childTransform = groupPrefabInstance.transform.GetChild(i);
                var childPrefabInstance = childTransform.gameObject;
                if (childPrefabInstance.GetComponent<DecorationObjectGroup>() != null)
                {
                    //分解第一级根节点
                    GetChildrenPrefabs(childPrefabInstance, childrenPrefabs);
                }
                else
                {
                    //获取prefab instance使用的prefab
                    var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);
                    if (childPrefab != null)
                    {
                        childrenPrefabs.Add(new ChildPrefabInfo(childPrefab, childPrefabInstance));
                    }
                    else
                    {
                        Debug.LogError($"invalid child prefab {childPrefabInstance.name}");
                    }
                }
            }
        }

        //将打散的子物体加入临时格子tile中
        void AddToGrid(TileData[] allTiles, int lod, IComplexGridModelData objectData, Rect objectLocalBounds, int horizontalTileCount, int verticalTileCount, Vector2 lowerLeftCornerPos)
        {
            var pos = objectData.GetPosition();
            var objectWorldBounds = new Rect(objectLocalBounds.xMin + pos.x, objectLocalBounds.yMin + pos.z, objectLocalBounds.width, objectLocalBounds.height);

            RectInt objCoordBounds = mLayerData.GetCoordinateBounds(objectWorldBounds, lowerLeftCornerPos);
            var min = objCoordBounds.min;
            var max = objCoordBounds.max;
            
            for (int i = min.y; i <= max.y; ++i)
            {
                for (int j = min.x; j <= max.x; ++j)
                {
                    if (i >= 0 && i < verticalTileCount &&
                        j >= 0 && j < horizontalTileCount)
                    {
                        int idx = i * horizontalTileCount + j;
                        if (allTiles[idx] == null)
                        {
                            allTiles[idx] = new TileData(j, i, lodCount);
                        }

                        allTiles[idx].lodObjects[lod].Add(objectData);
                    }
                }
            }
        }

        //计算原始prefab的bounding box
        void CalculateUniquePrefabInfo(List<IComplexGridModelData> objects, List<PrefabInitInfo2> allPrefabInfo)
        {
            var blendTerrainLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            int nObjectsInThisLOD = objects.Count;
            for (int k = 0; k < nObjectsInThisLOD; ++k)
            {
                var modelTemplate = objects[k].GetModelTemplate();
                string childPrefabPath = modelTemplate.GetLODPrefabPath(0);

                //find if have same prefab info
                int prefabInitInfoIndex = -1;
                var pos = objects[k].GetPosition();
                if (blendTerrainLayer != null)
                {
                    float terrainHeight = blendTerrainLayer.GetHeightAtPos(pos.x, pos.z);
                    if (terrainHeight != 0)
                    {
                        pos.y += terrainHeight;
                    }
                }
                var rot = objects[k].GetRotation();
                var scale = objects[k].GetScale();
                for (int i = 0; i < allPrefabInfo.Count; ++i)
                {
                    if (
                        allPrefabInfo[i].prefabPathForEachCustomType[0] == childPrefabPath &&
                        allPrefabInfo[i].rotation == rot &&
                        allPrefabInfo[i].scale == scale &&
                        Mathf.Approximately(allPrefabInfo[i].y, pos.y))
                    {
                        prefabInitInfoIndex = i;
                        break;
                    }
                }

                if (prefabInitInfoIndex == -1)
                {
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(childPrefabPath);
                    var childBounds = GameObjectBoundsCalculator.CalculateRect(childPrefab, rot, scale);
                    var prefabInfo = new PrefabInitInfo2(new List<string>() { childPrefabPath }, childBounds.xMin, childBounds.yMin, childBounds.width, childBounds.height, rot, scale, pos.y);
                    allPrefabInfo.Add(prefabInfo);
                    prefabInitInfoIndex = allPrefabInfo.Count - 1;
                }

                Debug.Assert(prefabInitInfoIndex < 50000);
                objects[k].modifiedTransformDataIndex = (short)prefabInitInfoIndex;
            }
        }

        void CreateBigTileData(TileData tileData, float bigTileWidth, float bigTileHeight, List<PrefabInitInfo2> allPrefabInfos, List<RemovableObject> removableObjects, List<SpecialAreaData> specialAreas, BinaryWriter writer)
        {
            float tileStartX = tileData.x * bigTileWidth + mLayerData.realLayerBounds.x;
            float tileStartZ = tileData.y * bigTileHeight + mLayerData.realLayerBounds.y;

            writer.Write(tileData.x);
            writer.Write(tileData.y);

            int nLODs = tileData.lodObjects.Length;
            writer.Write(nLODs);

            for (int lod = 0; lod < nLODs; ++lod)
            {
                var objects = tileData.lodObjects[lod];
                int nObjectsInThisLOD = objects.Count;

                writer.Write(nObjectsInThisLOD);
                for (int k = 0; k < nObjectsInThisLOD; ++k)
                {
                    string childPrefabPath = objects[k].GetAssetPath(0);
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(childPrefabPath);
                    if (childPrefab == null)
                    {
                        Debug.Assert(false, $"Can't find child prefab {childPrefabPath}");
                    }
                    else
                    {
                        Vector3 objPos = objects[k].GetPosition();
                        var localPositionInTile = objPos - new Vector3(tileStartX, 0, tileStartZ);
#if false
                        //debug bounds
                        var childBounds = GameObjectBoundsCalculator.CalculateRect(childPrefab, objects[k].GetRotation(), objects[k].GetScale());
                        //temp code
                        var obj = new GameObject(childPrefabPath);
                        childBounds = new Rect(objPos.x + childBounds.x, objPos.z + childBounds.y, childBounds.width, childBounds.height);
                        var dp = obj.AddComponent<DrawBounds>();
                        dp.bounds = Utils.RectToBounds(childBounds);
                        dp.worldSpace = true;
#endif

                        short pathIndexInStringTable = -1;
                        Debug.Assert(allPrefabInfos.Count < 60000);
                        for (int i = 0; i < allPrefabInfos.Count; ++i)
                        {
                            if (allPrefabInfos[i].prefabPathForEachCustomType[0] == childPrefabPath)
                            {
                                pathIndexInStringTable = (short)i;
                            }
                        }
                        Debug.Assert(pathIndexInStringTable >= 0);
                        //只保存xz坐标
                        writer.Write(localPositionInTile.x);
                        writer.Write(localPositionInTile.z);
                        writer.Write(objects[k].modifiedTransformDataIndex);
                        var objectType = MapCoreDef.GetTileObjectType(objects[k].gameObjectTag);
                        writer.Write((byte)objectType);
                        int viewID = GetSharedObjectID(objects[k].GetEntityID());
                        writer.Write(viewID);

                        if (objectType != TileObjectType.SpecialArea)
                        {
                            AddObjectToSpecialArea(specialAreas, objPos, viewID);
                        }

                        if (MapCoreDef.IsRemovableObject(objectType))
                        {
                            RemovableObject removableObject = GetRemovableObject(removableObjects, objPos);
                            if (removableObject == null)
                            {
                                removableObject = new RemovableObject(objPos);
                                removableObjects.Add(removableObject);
                            }

                            int bigTileIndex = tileData.y * mLayerData.horizontalTileCount + tileData.x;
                            removableObject.Add(viewID, lod, bigTileIndex, k);
                        }
                    }
                }
            }
        }

        //todo, 注意,这一步可能需要优化
        void AddObjectToSpecialArea(List<SpecialAreaData> allAreas, Vector3 objectPos, int viewID)
        {
            foreach (var area in allAreas)
            {
                if (area.Contains(objectPos))
                {
                    area.AddViewID(viewID);
                    //必须所有special area不能相交
                    break;
                }
            }
        }

        RemovableObject GetRemovableObject(List<RemovableObject> objects, Vector3 position)
        {
            foreach (var obj in objects)
            {
                if (obj.position == position)
                {
                    return obj;
                }
            }
            return null;
        }

        int GetSharedObjectID(int id)
        {
            int objID;
            bool found = mSharedObjectIDs.TryGetValue(id, out objID);
            if (found)
            {
                return objID;
            }
            objID = mCurrentObjectID;
            ++mCurrentObjectID;
            mSharedObjectIDs[id] = objID;

            return objID;
        }

        //计算prefab的所有类型的path
        List<string> CalculatePrefabTypePaths(string prefabPath)
        {
            List<string> prefabPathsForAllType = new List<string>();
            prefabPathsForAllType.Add(prefabPath);
            string pathName = Utils.RemoveExtension(prefabPath);
            string ext = Utils.GetExtension(prefabPath);
            for (int i = 1; i < 100; ++i)
            {
                string path = $"{pathName}{MapCoreDef.MAP_DECORATION_OBJECT_TYPE_POSTFIX}{i}.{ext}";
                if (File.Exists(path))
                {
                    prefabPathsForAllType.Add(path);
                }
                else
                {
                    break;
                }
            }
            return prefabPathsForAllType;
        }

        void SavePrefabInfo(List<TileData> allTiles, List<PrefabInitInfo2> allPrefabsInfo, int horizontalTileCount, int verticalTileCount, bool isDecorationBorderLayer)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.ComplexGridObjectLayerHeaderVersion);

            //----------------------version 1 start----------------------------
            int n = allPrefabsInfo.Count;
            writer.Write(n);
            Debug.LogError(n);
            for (int i = 0; i < n; ++i)
            {
                List<string> prefabPathsForAllTypes = CalculatePrefabTypePaths(allPrefabsInfo[i].prefabPathForEachCustomType[0]);
                Utils.WriteStringList(writer, prefabPathsForAllTypes);
                
                writer.Write(allPrefabsInfo[i].boundsMinX);
                writer.Write(allPrefabsInfo[i].boundsMinZ);
                writer.Write(allPrefabsInfo[i].boundsWidth);
                writer.Write(allPrefabsInfo[i].boundsHeight);
                Utils.WriteQuaternion(writer, allPrefabsInfo[i].rotation);
                Utils.WriteVector3(writer, allPrefabsInfo[i].scale);
                writer.Write(allPrefabsInfo[i].y);
            }
            //----------------------version 1 end---------------------------------

            //----------------------version 2 start----------------------------
            for (int i = 0; i < n; ++i)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(allPrefabsInfo[i].prefabPathForEachCustomType[0]);
                short maxVisibleQuality = short.MaxValue;
                if (prefab != null)
                {
                    var qualityControl = prefab.GetComponentInChildren<ObjectLoadingControl>();
                    if (qualityControl != null)
                    {
                        maxVisibleQuality = qualityControl.maxVisibleQuality;
                    }
                }
                writer.Write(maxVisibleQuality);
            }
            //----------------------version 2 end----------------------------

            //----------------------version 3 start----------------------------
            //导出tile是否需要update object
            bool[,] dontUpdateTileBigObjectCulling = new bool[verticalTileCount, horizontalTileCount];
            for (int i = 0; i < allTiles.Count; ++i) 
            { 
                bool result = IfDoNotUpdateTileBigObjectCulling(allTiles[i], allPrefabsInfo);
                dontUpdateTileBigObjectCulling[allTiles[i].y, allTiles[i].x] = result;
            }
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    writer.Write(dontUpdateTileBigObjectCulling[i, j]);
                }
            }
            //----------------------version 3 end----------------------------

            var data = stream.ToArray();
            string path = "";
            if (isDecorationBorderLayer)
            {
                path = MapCoreDef.GetDecorationBorderLayerHeaderFilePath(SLGMakerEditor.instance.exportFolder);
            }
            else
            {
                path = MapCoreDef.GetGridModelLayerHeaderFilePath(SLGMakerEditor.instance.exportFolder);
            }
            File.WriteAllBytes(path, data);
            writer.Close();
        }

        void SaveSpecialAreas(BinaryWriter writer, List<SpecialAreaData> specialAreas)
        {
            writer.Write(specialAreas.Count);
            for (int i = 0; i < specialAreas.Count; ++i)
            {
                var area = specialAreas[i];
                writer.Write(area.config.id);
                writer.Write(area.config.width);
                writer.Write(area.config.height);
                writer.Write((int)area.config.shape);
                Utils.WriteVector3(writer, area.config.transform.position);
                Utils.WriteIntArray(writer, area.objectViewIDs.ToArray());
            }
        }

        void SaveRemovableObjects(List<RemovableObject> removableObjects)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.ComplexGridObjectLayerRemovableObjectsVersion);

            //TextAssetEx有bug读取小文件GetAssetSize会返回错误大小,所以这里写入一些占位字节
            int[] buffer = new int[1024];
            Utils.WriteIntArray(writer, buffer);

            //----------------------version 1 start----------------------------
            int n = removableObjects.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, removableObjects[i].position);
                int nLODs = removableObjects[i].viewIDsInEachLOD.Count;
                writer.Write(nLODs);
                foreach (var lod in removableObjects[i].viewIDsInEachLOD)
                {
                    writer.Write(lod.lod);
                    writer.Write(lod.viewID);
                    writer.Write(lod.objects.Count);
                    foreach (var obj in lod.objects)
                    {
                        writer.Write(obj.bigTileIndex);
                        writer.Write(obj.objectIndex);
                    }
                }
            }
            //----------------------version 1 end---------------------------------

            var data = stream.ToArray();
            string path = null;
            bool isDecorationBorderLayer = this is DecorationBorderLayer;
            if (isDecorationBorderLayer)
            {
                path = MapCoreDef.GetRemovableBorderObjectsFilePath(SLGMakerEditor.instance.exportFolder); 
            }
            else
            {
                path = MapCoreDef.GetRemovableObjectsFilePath(SLGMakerEditor.instance.exportFolder);
            }
            File.WriteAllBytes(path, data);
            writer.Close();
        }

        bool IfDoNotUpdateTileBigObjectCulling(TileData tileData, List<PrefabInitInfo2> allPrefabsInfo)
        {
            bool needUpdate = false;
            if (tileData != null)
            {
                int nLODs = tileData.lodObjects.Length;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabTransforms = tileData.lodObjects[lod];
                    for (int i = 0; i < childPrefabTransforms.Count; ++i)
                    {
                        float boundsWidth = allPrefabsInfo[childPrefabTransforms[i].modifiedTransformDataIndex].boundsWidth;
                        float boundsHeight = allPrefabsInfo[childPrefabTransforms[i].modifiedTransformDataIndex].boundsHeight;
                        bool useCullManager = boundsWidth <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && boundsHeight <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE;
                        //现在只管理山的视野,因为山太大了,不适合用管理小件物体的方法管理
                        if (!useCullManager && childPrefabTransforms[i].gameObjectTag != MapCoreDef.MAP_ALWAYS_VISIBLE_OBJECT)
                        {
                            needUpdate = true;
                            break;
                        }
                    }
                }
            }

            return !needUpdate;
        }

        TileData GetTile(List<TileData> allTiles, int x, int y)
        {
            foreach (var tile in allTiles)
            {
                if (tile.x == x && tile.y == y)
                {
                    return tile;
                }
            }
            return null;
        }

        void MergeTile(TileData dst, TileData src)
        {
            Debug.Assert(dst.x == src.x && dst.y == src.y);
            Debug.Assert(dst.lodObjects.Length == src.lodObjects.Length);
            int nLODs = dst.lodObjects.Length;
            for (int i = 0; i < nLODs; ++i)
            {
                dst.lodObjects[i].AddRange(src.lodObjects[i]);
            }
        }

        Task CreateTask(int minIndex, int maxIndex, List<TileData> allTiles)
        {
            var task = Task.Run(() =>
            {
                for (int i = minIndex; i <= maxIndex; ++i)
                {
                    CullObjects(allTiles[i]);
                }
            });
            return task;
        }

        void Cull(List<TileData> allTiles, bool recreateGroups)
        {
            if (mLayerData.enableCullIntersectedObjects)
            {
                EditorUtility.DisplayProgressBar("Create Obstacle Groups", "Please wait", 0.0f);
                if (recreateGroups || mObstacleGroups.Count == 0)
                {
                    CreateObstacleGroups();
                }

                EditorUtility.DisplayProgressBar($"Cull Objects for {allTiles.Count - 1} tiles", "Please wait...", 0.3f);

                List<Task> tasks = new List<Task>();
                int threadCount = System.Environment.ProcessorCount;
                int regionCountPerThread = Mathf.CeilToInt(allTiles.Count / (float)threadCount);

                for (int i = 0; i < threadCount; ++i)
                {
                    int min = i * regionCountPerThread;
                    int max = Mathf.Min(min + regionCountPerThread - 1, allTiles.Count - 1);
                    var task = CreateTask(min, max, allTiles);
                    tasks.Add(task);
                }

                Task.WaitAll(tasks.ToArray());
            }
        }

        void CullObjects(TileData data)
        {
            //now only cull lod0
            int nLODs = data.lodObjects.Length;
            for (int lod = 0; lod < nLODs; ++lod)
            {
                var lodObjects = data.lodObjects[lod];
                for (int i = lodObjects.Count - 1; i >= 0; --i)
                {
                    if (!lodObjects[i].hasPrefabOutline && IsObjectInObstacle(lodObjects[i].GetPosition(), lod))
                    {
                        //Debug.LogError($"Object {lodObjects[i].GetAssetPath()} at position {lodObjects[i].GetPosition()} is culled!");
                        lodObjects.RemoveAt(i);
                    }
                }
            }
        }

        bool MakeSureAllObjectsAreIn3DModel()
        {
            List<IComplexGridModelData> allObjects = new List<IComplexGridModelData>();
            mLayerData.GetAllObjects(allObjects);

            foreach (var obj in allObjects)
            {
                var data = obj as ComplexGridModelData;
                if (data.useRenderTextureModel)
                {
                    EditorUtility.DisplayDialog("错误", "如果勾选了Enable Cull Intersected Objects, 需要将所有物体转换成3D物体再剔除", "确定");
                    return false;
                }
            }
            return true;
        }
    }
}
#endif