﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class MapLayerXZRectangleGridCreator : MapLayerGridCreator
    {
        public MapLayerXZRectangleGridCreator(float layerWidth = 0, float layerHeight = 0, float tileWidth = 0, float tileHeight = 0)
        {
            mLayerWidth = layerWidth;
            mLayerHeight = layerHeight;
            mTileWidth = tileWidth;
            mTileHeight = tileHeight;
        }

        public override GameObject CreateGrid(Color color)
        {
            var grid = new GameObject("XZ Rectangle Grid");
            grid.transform.position = layerOffset;
            Utils.HideGameObject(grid);
            var meshFilter = grid.AddComponent<MeshFilter>();
            var meshRenderer = grid.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Color"));
            mtl.color = color;
            meshRenderer.sharedMaterial = mtl;
            meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            var mesh = CreateGridMesh(verticalTileCount, horizontalTileCount, tileWidth, tileHeight);
            meshFilter.sharedMesh = mesh;
            grid.SetActive(true);
            return grid;
        }

        protected virtual Mesh CreateGridMesh(int rows, int cols, float tileWidth, float tileHeight)
        {
            var mesh = new Mesh();
            mesh.indexFormat = UnityEngine.Rendering.IndexFormat.UInt32;

            int rowVertexCount = rows + 1;
            int colVertexCount = cols + 1;
            int vertexCount = (rowVertexCount + colVertexCount) * 2;
            Vector3[] positions = new Vector3[vertexCount];
            int[] indices = new int[vertexCount];
            float totalWidth = tileWidth * cols;
            float totalHeight = tileHeight * rows;

            int offset = 0;
            //horizontal lines
            for (int i = 0; i < rowVertexCount; ++i)
            {
                positions[offset] = new Vector3(0, 0, i * tileHeight);
                positions[offset + 1] = new Vector3(totalWidth, 0, i * tileHeight);
                indices[offset] = offset;
                indices[offset + 1] = offset + 1;
                offset += 2;
            }

            //vertical lines
            for (int i = 0; i < colVertexCount; ++i)
            {
                positions[offset] = new Vector3(i * tileWidth, 0, 0);
                positions[offset + 1] = new Vector3(i * tileWidth, 0, totalHeight);
                indices[offset] = offset;
                indices[offset + 1] = offset + 1;
                offset += 2;
            }
            Debug.Assert(offset == vertexCount);

            mesh.vertices = positions;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            return mesh;
        }

        public float layerWidth
        {
            get
            {
                if (mLayer != null)
                {
                    return mLayer.GetLayerWidthInMeter();
                }
                return mLayerWidth;
            }
        }

        public float layerHeight
        {
            get
            {
                if (mLayer != null)
                {
                    return mLayer.GetLayerHeightInMeter();
                }
                return mLayerHeight;
            }
        }

        public float tileWidth
        {
            get
            {
                if (mLayer != null)
                {
                    return mLayer.tileWidth;
                }
                return mTileWidth;
            }
        }

        public float tileHeight
        {
            get
            {
                if (mLayer != null)
                {
                    return mLayer.tileHeight;
                }
                return mTileHeight;
            }
        }

        public int horizontalTileCount
        {
            get
            {
                return Mathf.FloorToInt(layerWidth / tileWidth);
            }
        }

        public int verticalTileCount
        {
            get
            {
                return Mathf.FloorToInt(layerHeight / tileHeight);
            }
        }

        public Vector3 layerOffset
        {
            get
            {
                if (mLayer != null)
                {
                    return mLayer.layerOffset;
                }
                return Vector3.zero;
            }
        }

        float mLayerWidth;
        float mLayerHeight;
        float mTileWidth;
        float mTileHeight;
    };
}


#endif