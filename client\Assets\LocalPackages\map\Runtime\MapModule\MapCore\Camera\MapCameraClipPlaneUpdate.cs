﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public static class MapCameraClipPlaneUpdate
    {
        public static void UpdateNearClipPlane(CameraClipPlaneSetting setting)
        {
            if (setting.clipCurve != null)
            {
                var camera = Map.currentMap.camera;
                float offset = setting.clipCurve.Evaluate(camera.transform.position.y);
                UpdateNearClipPlane(offset);
            }
        }

        public static void UpdateFarClipPlane(CameraClipPlaneSetting setting)
        {
            if (setting.clipCurve != null)
            {
                var camera = Map.currentMap.camera;
                float offset = setting.clipCurve.Evaluate(camera.transform.position.y);
                UpdateFarClipPlane(offset);
            }
        }

        public static void UpdateNearClipPlane(float nearClipOffset)
        {
            var camera = Map.currentMap.camera;
            float cameraXRot = camera.transform.rotation.eulerAngles.x;
            float cos = Mathf.Cos(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);
            Plane terrainPlane = new Plane(Vector3.up, Vector3.zero);
            float maxAngle = cameraXRot + camera.fieldOfView * 0.5f;
            Vector3 maxAngleDir = Quaternion.Euler(new Vector3(maxAngle, 0, 0)) * Vector3.forward;
            Ray maxAngleRay = new Ray(camera.transform.position, maxAngleDir);
            float maxAngleDistance;
            terrainPlane.Raycast(maxAngleRay, out maxAngleDistance);

            float cameraHeight = camera.transform.position.y;
            float l = (maxAngleDistance * nearClipOffset) / cameraHeight;
            float x = maxAngleDistance - l;
            float n = x * cos;
            if (n < 1.0f)
            {
                n = 1.0f;
            }
            camera.nearClipPlane = n;
            //add some offset
            //maxAngleDistance -= nearClipOffset;
            //float nearClip = cos * maxAngleDistance;
            //if (nearClip < 1.0f)
            //    nearClip = 1.0f;
            //camera.nearClipPlane = nearClip;
        }

        public static void UpdateFarClipPlane(float farClipOffset)
        {
            var camera = Map.currentMap.camera;
            float cameraXRot = camera.transform.rotation.eulerAngles.x;
            //calculate far clip plane distance
            float minAngle = cameraXRot - camera.fieldOfView * 0.5f;
            float cos = Mathf.Cos(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);
            Plane terrainPlane = new Plane(Vector3.up, Vector3.zero);
            Vector3 dir = Quaternion.Euler(new Vector3(minAngle, 0, 0)) * Vector3.forward;
            Ray ray = new Ray(camera.transform.position, dir);
            float distance;
            terrainPlane.Raycast(ray, out distance);
            float x = farClipOffset / Mathf.Sin(minAngle * Mathf.Deg2Rad);
            float k = distance + x;
            float f = k * cos;
            camera.farClipPlane = f;
            //add some offset
            //distance += farClipOffset;
            //float cos = Mathf.Cos(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);
            //float farClip = cos * distance;
            //camera.farClipPlane = farClip;
        }
    }
}
