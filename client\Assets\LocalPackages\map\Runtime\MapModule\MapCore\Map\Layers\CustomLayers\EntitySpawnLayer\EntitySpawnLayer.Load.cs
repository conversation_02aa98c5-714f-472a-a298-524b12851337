﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class EntitySpawnLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new EntitySpawnLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.MapLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            //load layer info
            float regionWidth = reader.ReadSingle();
            float regionHeight = reader.ReadSingle();
            int horizontalRegionCount = reader.ReadInt32();
            int verticalRegionCount = reader.ReadInt32();
            var npcMoveRange = Utils.ReadVector2(reader);

            ISpawnPointGenerationStrategy strategy = null;

            strategy = LoadSpawnPointGenerationStrategy(reader, npcMoveRange.y);

            //load npc move info
            int waypointCount = reader.ReadInt32();
            List<Vector2> npcWaypoints = new List<Vector2>();
            for (int i = 0; i < waypointCount; ++i)
            {
                npcWaypoints.Add(Utils.ReadVector2(reader));
            }

            //load spawn points
            int nRegions = horizontalRegionCount * verticalRegionCount;
            var regions = new List<config.EntitySpawnRegion>();
            for (int i = 0; i < nRegions; ++i)
            {
                var region = new config.EntitySpawnRegion();
                region.visible = reader.ReadBoolean();
                if (version.minorVersion >= 3)
                {
                    region.seed = reader.ReadInt32();
                }
                int nSpawnPointsInRegion = reader.ReadInt32();
                region.spawnPoints = new List<Vector2>();
                for (int s = 0; s < nSpawnPointsInRegion; ++s)
                {
                    region.spawnPoints.Add(Utils.ReadVector2(reader));
                }
                regions.Add(region);
            }

            int[,] grids = null;
            List<EntitySpawnRegionBrush> brushes = null;
            if (version.minorVersion >= 2)
            {
                brushes = new List<EntitySpawnRegionBrush>();
                grids = new int[verticalRegionCount, horizontalRegionCount];
                for (int i = 0; i < verticalRegionCount; ++i)
                {
                    for (int j = 0; j < horizontalRegionCount; ++j)
                    {
                        grids[i, j] = reader.ReadInt32();
                    }
                }
                int nBrushes = reader.ReadInt32();
                for (int i = 0; i < nBrushes; ++i)
                {
                    var brush = new EntitySpawnRegionBrush();
                    brush.id = reader.ReadInt32();
                    brush.priority = reader.ReadInt32();
                    brush.color = Utils.ReadColor(reader);
                    brushes.Add(brush);
                }
            }

            string spawnPointReferenceNPCReginLayerName = "";
            if (version.minorVersion >= 4)
            {
                spawnPointReferenceNPCReginLayerName = Utils.ReadString(reader);
            }

            var layer = new config.EntitySpawnLayerData(layerID, layerName, layerOffset, horizontalRegionCount, verticalRegionCount, new Vector2(regionWidth, regionHeight), npcMoveRange, regions, npcWaypoints, strategy, grids, brushes, spawnPointReferenceNPCReginLayerName);
            layer.active = active;
            return layer;
        }

        static ISpawnPointGenerationStrategy LoadSpawnPointGenerationStrategy(BinaryReader reader, float maxMoveRange)
        {
            ISpawnPointGenerationStrategy strategy = null;
            var type = (SpawnPointGenerateStrategyType)reader.ReadInt32();
            if (type == SpawnPointGenerateStrategyType.Random)
            {
                float npcDensity = reader.ReadSingle();
                int oneMeterHorizontalResolution = reader.ReadInt32();
                int oneMeterVerticalResolution = reader.ReadInt32();
                strategy = new GenerateSpawnPointsRandomly(npcDensity, maxMoveRange, oneMeterHorizontalResolution, oneMeterVerticalResolution);
            }
            else if (type == SpawnPointGenerateStrategyType.Grid)
            {
                var startOffset = Utils.ReadVector3(reader);
                var pointDeltaDistance = Utils.ReadVector2(reader);
                float xOffset = reader.ReadSingle();
                float randomRange = reader.ReadSingle();
                strategy = new GenerateSpawnPointsInGridLayout(startOffset, pointDeltaDistance, xOffset, randomRange, maxMoveRange);
            }
            else
            {
                Debug.Assert(false, "unknown strategy!");
            }

            return strategy;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif