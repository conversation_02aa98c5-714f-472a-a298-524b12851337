﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class PolygonRiverVertexMover
    {
        public PolygonRiverVertexMover(int layerID, int dataID, int vertexIdx, Vector3 pos, PrefabOutlineType outlineType)
        {
            Debug.Assert(mAction == null);
            mAction = new ActionMovePolygonRiverVertex(layerID, dataID, vertexIdx, pos, outlineType);
        }

        public void Stop(Vector3 pos)
        {
            mAction.SetEndPosition(pos);
            if (mAction.isMoved)
            {
                ActionManager.instance.PushAction(mAction, true, false);
            }
            mAction = null;
        }

        ActionMovePolygonRiverVertex mAction;
    }
}


#endif