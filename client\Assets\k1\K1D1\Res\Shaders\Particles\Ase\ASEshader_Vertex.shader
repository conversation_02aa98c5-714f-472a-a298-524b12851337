// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "ASEshader/Vertex"
{
	Properties
	{
		[Enum(UnityEngine.Rendering.BlendMode)]_Src("Src", Int) = 5
		[Enum(UnityEngine.Rendering.BlendMode)]_Dst("Dst", Int) = 10
		[Enum(UnityEngine.Rendering.CullMode)]_Cull_Mode("Cull_Mode", Int) = 0
		[HDR]_MainColor("MainColor", Color) = (1,1,1,1)
		_MainTex("MainTex", 2D) = "white" {}
		[HDR]_Tex2Color("Tex2Color", Color) = (1,1,1,1)
		_Tex2("Tex2", 2D) = "white" {}
		[Enum(Default,0,Random,1)]_RandomOffsetUV2v("RandomOffset(UV2v)", Int) = 0
		[Toggle(_DISSOLVEKEY_ON)] _DissolveKey("DissolveKey", Float) = 0
		[Enum(Default,0,Custom,1)]_DissolveModeCustom("DissolveModeCustom", Int) = 0
		[Toggle(_DOUBLEDECKDISSOLVE_ON)] _DoubleDeckDissolve("DoubleDeckDissolve", Float) = 0
		[HDR]_DissolveEdgeColor("DissolveEdgeColor", Color) = (0,1,0.02469373,1)
		_Dissolve("Dissolve", Range( 0 , 1)) = 0
		_EdgeWidth("EdgeWidth", Range( 0 , 1)) = 0.1836489
		_DissolveHard("DissolveHard", Range( 0 , 0.99)) = 0.5650588
		[Enum(Default,0,Random,1)]_DissolveRandomOffsetUV2w("DissolveRandomOffset(UV2w)", Int) = 0
		_DissolveTex("DissolveTex", 2D) = "white" {}
		_Dissolve_DIR("Dissolve_DIR", 2D) = "white" {}
		_DIRWeight("DIRWeight", Range( 0 , 1)) = 1
		_VertexTex("VertexTex", 2D) = "white" {}
		_VertexStr("VertexStr", Float) = 1
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}
	
	SubShader
	{
		
		
		Tags { "RenderType"="Transparent" "Queue"="Transparent" }
	LOD 100

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend [_Src] [_Dst], [_Src] [_Dst]
		AlphaToMask Off
		Cull [_Cull_Mode]
		ColorMask RGBA
		ZWrite Off
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"
			Tags {  }
			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#define ASE_NEEDS_VERT_POSITION
			#define ASE_NEEDS_FRAG_COLOR
			#pragma shader_feature_local _DOUBLEDECKDISSOLVE_ON
			#pragma shader_feature_local _DISSOLVEKEY_ON


			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				half3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			uniform int _Dst;
			uniform int _Src;
			uniform int _Cull_Mode;
			uniform sampler2D _VertexTex;
			uniform half4 _VertexTex_ST;
			uniform half _VertexStr;
			uniform sampler2D _MainTex;
			uniform half4 _MainTex_ST;
			uniform int _RandomOffsetUV2v;
			uniform half4 _MainColor;
			uniform half4 _Tex2Color;
			uniform sampler2D _Tex2;
			uniform half4 _Tex2_ST;
			uniform half4 _DissolveEdgeColor;
			uniform sampler2D _DissolveTex;
			uniform half4 _DissolveTex_ST;
			uniform int _DissolveRandomOffsetUV2w;
			uniform sampler2D _Dissolve_DIR;
			uniform half4 _Dissolve_DIR_ST;
			uniform half _DIRWeight;
			uniform half _Dissolve;
			uniform int _DissolveModeCustom;
			uniform half _EdgeWidth;
			uniform half _DissolveHard;

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				half3 normalizeResult437 = normalize( v.vertex.xyz );
				half2 appendResult451 = (half2(( atan2( (normalizeResult437).x , (normalizeResult437).z ) / ( 2.0 * UNITY_PI ) ) , ( 1.0 - ( acos( (normalizeResult437).y ) / UNITY_PI ) )));
				half3 VerTex510 = ( ( tex2Dlod( _VertexTex, float4( ( ( appendResult451 + ( _VertexTex_ST.zw * float2( 0.1,0.1 ) * _Time.y ) ) * _VertexTex_ST.xy ), 0, 0.0) ).r * _VertexStr ) * v.ase_normal );
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				o.ase_texcoord2 = v.ase_texcoord1;
				o.ase_color = v.color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = VerTex510;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				half2 texCoord52 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half2 appendResult357 = (half2(i.ase_texcoord2.y , 0.0));
				half2 lerpResult359 = lerp( float2( 0,0 ) , appendResult357 , (float)_RandomOffsetUV2v);
				half2 UV3u355 = lerpResult359;
				half4 tex2DNode64 = tex2D( _MainTex, ( ( texCoord52 + ( _MainTex_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + UV3u355 ) * _MainTex_ST.xy ) );
				half2 uv_Tex2 = i.ase_texcoord1.xy * _Tex2_ST.xy + _Tex2_ST.zw;
				half4 Tex2328 = ( _Tex2Color * tex2D( _Tex2, ( ( uv_Tex2 + ( _Tex2_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + UV3u355 ) * _Tex2_ST.xy ) ) * tex2DNode64.a );
				half4 temp_output_332_0 = ( ( tex2DNode64 * _MainColor ) + Tex2328 );
				half2 texCoord140 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				half2 appendResult416 = (half2(i.ase_texcoord2.z , 0.0));
				half2 lerpResult413 = lerp( float2( 0,0 ) , appendResult416 , (float)_DissolveRandomOffsetUV2w);
				half2 VU3w412 = lerpResult413;
				float2 uv_Dissolve_DIR = i.ase_texcoord1.xy * _Dissolve_DIR_ST.xy + _Dissolve_DIR_ST.zw;
				half lerpResult297 = lerp( tex2D( _DissolveTex, ( ( texCoord140 + ( _DissolveTex_ST.zw * float2( 0.1,0.1 ) * _Time.y ) + VU3w412 ) * _DissolveTex_ST.xy ) ).r , tex2D( _Dissolve_DIR, uv_Dissolve_DIR ).r , _DIRWeight);
				half temp_output_159_0 = ( lerpResult297 + 1.0 );
				half UV2u78 = i.ase_texcoord2.x;
				half lerpResult270 = lerp( _Dissolve , UV2u78 , (float)_DissolveModeCustom);
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half staticSwitch254 = ( lerpResult270 * _EdgeWidth );
				#else
				half staticSwitch254 = 0.0;
				#endif
				half temp_output_230_0 = ( lerpResult270 + staticSwitch254 );
				half temp_output_2_0_g7 = _DissolveHard;
				#ifdef _DISSOLVEKEY_ON
				half staticSwitch163 = saturate( ( ( ( temp_output_159_0 - ( temp_output_230_0 * ( 2.0 - _DissolveHard ) ) ) - temp_output_2_0_g7 ) / ( 1.0 - temp_output_2_0_g7 ) ) );
				#else
				half staticSwitch163 = (float)1;
				#endif
				half DissolveRGB232 = staticSwitch163;
				half4 lerpResult234 = lerp( ( _DissolveEdgeColor * tex2DNode64.a ) , temp_output_332_0 , DissolveRGB232);
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half4 staticSwitch251 = lerpResult234;
				#else
				half4 staticSwitch251 = temp_output_332_0;
				#endif
				half temp_output_2_0_g8 = _DissolveHard;
				#ifdef _DISSOLVEKEY_ON
				half staticSwitch243 = saturate( ( ( ( temp_output_159_0 - ( ( temp_output_230_0 - _EdgeWidth ) * ( 2.0 - _DissolveHard ) ) ) - temp_output_2_0_g8 ) / ( 1.0 - temp_output_2_0_g8 ) ) );
				#else
				half staticSwitch243 = 1.0;
				#endif
				half DissolveA233 = staticSwitch243;
				#ifdef _DOUBLEDECKDISSOLVE_ON
				half staticSwitch249 = DissolveA233;
				#else
				half staticSwitch249 = DissolveRGB232;
				#endif
				half4 appendResult432 = (half4(( staticSwitch251 * i.ase_color ).rgb , ( tex2DNode64.a * _MainColor.a * i.ase_color.a * staticSwitch249 )));
				
				
				finalColor = appendResult432;
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=18800
3277;145;1578;1209;-1397.743;3037.41;1;True;True
Node;AmplifyShaderEditor.CommentaryNode;272;2973.986,-418.7684;Inherit;False;962.9585;687.8865;Comment;10;359;412;413;414;416;358;355;357;78;53;;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;111;-867.7927,-41.11532;Inherit;False;3506.553;1637.158;Comment;33;232;233;163;243;435;244;161;434;215;160;158;159;218;220;297;227;157;153;298;150;149;226;230;254;148;231;255;270;219;152;56;271;129;;1,1,1,1;0;0
Node;AmplifyShaderEditor.TexCoordVertexDataNode;53;2997.986,-327.0444;Inherit;False;1;4;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.DynamicAppendNode;416;3217.41,-22.38438;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;414;3139.41,82.61555;Inherit;False;Property;_DissolveRandomOffsetUV2w;DissolveRandomOffset(UV2w);15;1;[Enum];Create;True;0;2;Default;0;Random;1;0;False;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.CommentaryNode;129;-751.2862,312.9484;Inherit;False;858.4456;434.8156;TilingOffset;10;145;141;143;138;139;417;140;136;135;131;;1,1,1,1;0;0
Node;AmplifyShaderEditor.LerpOp;413;3450.41,-1.384404;Inherit;True;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureTransformNode;131;-682.9522,522.2723;Inherit;False;149;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.RegisterLocalVarNode;412;3720.41,-23.38438;Inherit;False;VU3w;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;78;3187.968,-377.7686;Inherit;False;UV2u;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleTimeNode;135;-638.7372,641.8483;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;136;-50.24078,501.2574;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;357;3252.91,-274.4624;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;358;3178.589,-115.1807;Inherit;False;Property;_RandomOffsetUV2v;RandomOffset(UV2v);7;1;[Enum];Create;True;0;2;Default;0;Random;1;0;False;0;False;0;0;False;0;1;INT;0
Node;AmplifyShaderEditor.GetLocalVarNode;417;-427.0812,668.6663;Inherit;False;412;VU3w;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;271;27.91273,871.1751;Inherit;False;78;UV2u;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;138;-421.8456,515.941;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;56;25.04112,950.8801;Inherit;False;Property;_DissolveModeCustom;DissolveModeCustom;9;1;[Enum];Create;True;0;2;Default;0;Custom;1;0;False;0;False;0;1;False;0;1;INT;0
Node;AmplifyShaderEditor.RangedFloatNode;152;24.20741,788.1511;Inherit;False;Property;_Dissolve;Dissolve;12;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;140;-602.7363,369.9485;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;139;-31.24102,499.2574;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;219;175.2771,1398.866;Inherit;False;Property;_EdgeWidth;EdgeWidth;13;0;Create;True;0;0;0;False;0;False;0.1836489;0.091;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;143;-31.24102,457.2574;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.CommentaryNode;51;-333.8128,-1128.092;Inherit;False;906.2407;488.4693;Comment;8;356;181;180;185;182;52;184;179;;1,1,1,1;0;0
Node;AmplifyShaderEditor.LerpOp;270;324.9127,792.1751;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.CommentaryNode;316;-1207.546,-2356.335;Inherit;False;1643.446;732.4534;Comment;12;314;313;424;310;311;312;315;328;308;299;306;515;;1,1,1,1;0;0
Node;AmplifyShaderEditor.LerpOp;359;3435.849,-334.7816;Inherit;True;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.PosVertexDataNode;436;662.3439,-2964.188;Inherit;False;0;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;141;-224.2419,367.2574;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;355;3722.023,-240.1235;Inherit;False;UV3u;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.NormalizeNode;437;858.1486,-3291.13;Inherit;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleTimeNode;312;-890.0041,-1928.094;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.TextureTransformNode;179;-224.019,-900.1121;Inherit;False;64;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;231;503.4534,1146.675;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;145;-26.24199,369.2584;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;255;347.0113,943.0511;Inherit;False;Constant;_Float6;Float 6;29;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleTimeNode;184;-206.4093,-756.4391;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;148;1247.145,791.1911;Inherit;False;Property;_DissolveHard;DissolveHard;14;0;Create;True;0;0;0;False;0;False;0.5650588;0.938;0;0.99;0;1;FLOAT;0
Node;AmplifyShaderEditor.TextureTransformNode;311;-948.6141,-2055.267;Inherit;False;299;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.RangedFloatNode;298;220.6884,397.3677;Inherit;False;Property;_DIRWeight;DIRWeight;18;0;Create;True;0;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.ComponentMaskNode;438;1148.149,-3221.13;Inherit;False;False;True;False;True;1;0;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;313;-709.3041,-1994.994;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;149;288.8926,570.347;Inherit;True;Property;_DissolveTex;DissolveTex;16;0;Create;True;0;0;0;False;0;False;-1;None;673581e192d31184cbe0f58d30bd1130;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;182;10.59062,-805.4391;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;150;253.6997,123.2669;Inherit;True;Property;_Dissolve_DIR;Dissolve_DIR;17;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureCoordinatesNode;52;-236.599,-1033.065;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.GetLocalVarNode;356;209.5654,-773.4307;Inherit;False;355;UV3u;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;424;-1073.117,-1874.869;Inherit;False;355;UV3u;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;153;1471.468,964.512;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;254;506.1055,943.257;Inherit;False;Property;_doubleDeckDissolve;doubleDeckDissolve;10;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;249;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;310;-1066.446,-2215.7;Inherit;False;0;299;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ACosOpNode;442;1340.817,-2663.738;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ComponentMaskNode;440;1150.149,-3299.13;Inherit;False;True;False;False;True;1;0;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;439;1128.458,-2902.708;Inherit;False;Constant;_Float7;Float 7;51;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.ComponentMaskNode;444;1155.149,-3139.13;Inherit;False;False;False;True;True;1;0;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.PiNode;443;1100.459,-2815.708;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.PiNode;441;1316.898,-2552.063;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;314;-706.0369,-2164.149;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;180;284.6905,-1028.438;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;185;407.8914,-844.166;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;157;1027.459,899.158;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;226;1409.901,1465.186;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;230;749.1325,800.991;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;297;664.9031,340.4619;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;220;1011.059,1482.936;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleDivideOpNode;445;1571.598,-2777.461;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;315;-559.5707,-2071.235;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;181;429.7906,-1025.838;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;446;1355.159,-2900.809;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;227;865.5002,1378.735;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;158;1028.259,798.351;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;2;False;1;FLOAT;0
Node;AmplifyShaderEditor.ATan2OpNode;447;1480.643,-3233.998;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;159;956.735,514.189;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleDivideOpNode;448;1793.428,-2999.718;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;449;1793.134,-2767.976;Inherit;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;299;-392.7086,-2093.411;Inherit;True;Property;_Tex2;Tex2;6;0;Create;True;0;0;0;False;0;False;-1;None;c1be2dce723ef32459fb20da68281207;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleSubtractOpNode;160;1229.319,510.39;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.TextureTransformNode;500;1820.445,-2288.585;Inherit;False;499;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;218;1015.481,1379.115;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;2;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;306;-310.7693,-2275.631;Inherit;False;Property;_Tex2Color;Tex2Color;5;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;64;782.6797,-1407.697;Inherit;True;Property;_MainTex;MainTex;4;0;Create;True;0;0;0;False;0;False;-1;None;02ff5523457eb774cb8967c6a4156685;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleTimeNode;502;1874.784,-2133.537;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;503;2106.784,-2195.537;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0.1,0.1;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;161;1850.74,399.449;Inherit;False;Constant;_Int2;Int 2;53;0;Create;True;0;0;0;False;0;False;1;0;False;0;1;INT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;308;-63.41692,-2167.057;Inherit;True;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.DynamicAppendNode;451;2067.318,-2896.596;Inherit;True;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;215;1223.03,1116.224;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;434;1662.152,560.767;Inherit;False;SimpleSmoothStep;-1;;7;f13a47e064352a24f9030330da831405;0;3;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;328;209.7318,-2171.91;Inherit;True;Tex2;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;505;2288.784,-2218.537;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;244;1948.179,1088.72;Inherit;False;Constant;_Float4;Float 4;26;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;435;1659.275,1120.716;Inherit;False;SimpleSmoothStep;-1;;8;f13a47e064352a24f9030330da831405;0;3;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;163;1999.309,475.909;Inherit;True;Property;_DissolveKey;DissolveKey;8;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;83;933.8024,-1058.478;Inherit;False;Property;_MainColor;MainColor;3;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;504;2445.784,-2311.537;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;329;1293.673,-1099.011;Inherit;True;328;Tex2;1;0;OBJECT;;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;236;1528.262,-1751.918;Inherit;False;Property;_DissolveEdgeColor;DissolveEdgeColor;11;1;[HDR];Create;True;0;0;0;False;0;False;0,1,0.02469373,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RegisterLocalVarNode;232;2358.891,476.841;Inherit;False;DissolveRGB;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;177;1288.946,-1331.756;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.StaticSwitch;243;2094.551,1089.816;Inherit;True;Property;_DissolveKey;DissolveKey;8;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Reference;163;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;499;2607.428,-2339.237;Inherit;True;Property;_VertexTex;VertexTex;19;0;Create;True;0;0;0;False;0;False;-1;None;0013a86a9366a2d43872a9deaebddaf5;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;402;1796.256,-1592.648;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.GetLocalVarNode;237;1670.342,-1472.892;Inherit;False;232;DissolveRGB;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;233;2420.155,1088.884;Inherit;False;DissolveA;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;507;2862.354,-2129.956;Inherit;False;Property;_VertexStr;VertexStr;20;0;Create;True;0;0;0;False;0;False;1;0.7;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;332;1559.289,-1257.972;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;506;3063.354,-2320.956;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;234;1992.54,-1528.622;Inherit;True;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.NormalVertexDataNode;509;3084.452,-2104.395;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;259;1194.08,-747.0954;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;250;1916.93,-681.7174;Inherit;False;232;DissolveRGB;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;334;1232.356,-787.6245;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;238;1932.316,-585.2155;Inherit;False;233;DissolveA;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.VertexColorNode;172;2277.014,-1013.95;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;256;1209.994,-745.7691;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;249;2140.381,-646.4623;Inherit;False;Property;_DoubleDeckDissolve;DoubleDeckDissolve;10;0;Create;True;0;0;0;False;0;False;0;0;1;True;;Toggle;2;Key0;Key1;Create;True;True;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;508;3270.352,-2318.895;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.WireNode;333;1268.674,-777.2495;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;251;2260.294,-1273.042;Inherit;False;Property;_doubleDeckDissolve;doubleDeckDissolve;10;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;249;True;True;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;510;3457.64,-1797.4;Inherit;False;VerTex;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;178;2546.229,-850.505;Inherit;True;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;239;2681.167,-1269.494;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.IntNode;351;3392.746,-672.9538;Inherit;False;Property;_Src;Src;0;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.BlendMode;True;0;False;5;5;False;0;1;INT;0
Node;AmplifyShaderEditor.IntNode;260;3245.673,-674.3226;Inherit;False;Property;_Cull_Mode;Cull_Mode;2;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.CullMode;True;0;False;0;2;False;0;1;INT;0
Node;AmplifyShaderEditor.GetLocalVarNode;515;-1018.988,-2286.134;Inherit;False;-1;;1;0;OBJECT;;False;1;FLOAT2;0
Node;AmplifyShaderEditor.IntNode;353;3530.211,-675.4148;Inherit;False;Property;_Dst;Dst;1;1;[Enum];Create;True;0;0;1;UnityEngine.Rendering.BlendMode;True;0;False;10;10;False;0;1;INT;0
Node;AmplifyShaderEditor.GetLocalVarNode;511;3635.174,-1509.391;Inherit;False;510;VerTex;1;0;OBJECT;;False;1;FLOAT3;0
Node;AmplifyShaderEditor.DynamicAppendNode;432;3397.906,-1296.705;Inherit;True;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;431;3838.999,-1315.302;Half;False;True;-1;2;ASEMaterialInspector;100;1;ASEshader/Vertex;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;True;2;5;True;351;10;True;353;2;5;True;351;10;True;353;True;0;False;-1;0;False;-1;False;False;False;False;False;False;True;0;False;-1;True;0;True;260;True;True;True;True;True;0;False;-1;False;False;False;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;True;2;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;2;RenderType=Transparent=RenderType;Queue=Transparent=Queue=0;True;2;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=;False;0;;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;1;True;False;;False;0
WireConnection;416;0;53;3
WireConnection;413;1;416;0
WireConnection;413;2;414;0
WireConnection;412;0;413;0
WireConnection;78;0;53;1
WireConnection;136;0;131;0
WireConnection;357;0;53;2
WireConnection;138;0;131;1
WireConnection;138;2;135;0
WireConnection;139;0;136;0
WireConnection;143;0;139;0
WireConnection;270;0;152;0
WireConnection;270;1;271;0
WireConnection;270;2;56;0
WireConnection;359;1;357;0
WireConnection;359;2;358;0
WireConnection;141;0;140;0
WireConnection;141;1;138;0
WireConnection;141;2;417;0
WireConnection;355;0;359;0
WireConnection;437;0;436;0
WireConnection;231;0;270;0
WireConnection;231;1;219;0
WireConnection;145;0;141;0
WireConnection;145;1;143;0
WireConnection;438;0;437;0
WireConnection;313;0;311;1
WireConnection;313;2;312;0
WireConnection;149;1;145;0
WireConnection;182;0;179;1
WireConnection;182;2;184;0
WireConnection;153;0;148;0
WireConnection;254;1;255;0
WireConnection;254;0;231;0
WireConnection;442;0;438;0
WireConnection;440;0;437;0
WireConnection;444;0;437;0
WireConnection;314;0;310;0
WireConnection;314;1;313;0
WireConnection;314;2;424;0
WireConnection;180;0;52;0
WireConnection;180;1;182;0
WireConnection;180;2;356;0
WireConnection;185;0;179;0
WireConnection;157;1;153;0
WireConnection;226;0;148;0
WireConnection;230;0;270;0
WireConnection;230;1;254;0
WireConnection;297;0;149;1
WireConnection;297;1;150;1
WireConnection;297;2;298;0
WireConnection;220;1;226;0
WireConnection;445;0;442;0
WireConnection;445;1;441;0
WireConnection;315;0;314;0
WireConnection;315;1;311;0
WireConnection;181;0;180;0
WireConnection;181;1;185;0
WireConnection;446;0;439;0
WireConnection;446;1;443;0
WireConnection;227;0;230;0
WireConnection;227;1;219;0
WireConnection;158;0;230;0
WireConnection;158;1;157;0
WireConnection;447;0;440;0
WireConnection;447;1;444;0
WireConnection;159;0;297;0
WireConnection;448;0;447;0
WireConnection;448;1;446;0
WireConnection;449;0;445;0
WireConnection;299;1;315;0
WireConnection;160;0;159;0
WireConnection;160;1;158;0
WireConnection;218;0;227;0
WireConnection;218;1;220;0
WireConnection;64;1;181;0
WireConnection;503;0;500;1
WireConnection;503;2;502;0
WireConnection;308;0;306;0
WireConnection;308;1;299;0
WireConnection;308;2;64;4
WireConnection;451;0;448;0
WireConnection;451;1;449;0
WireConnection;215;0;159;0
WireConnection;215;1;218;0
WireConnection;434;1;160;0
WireConnection;434;2;148;0
WireConnection;328;0;308;0
WireConnection;505;0;451;0
WireConnection;505;1;503;0
WireConnection;435;1;215;0
WireConnection;435;2;148;0
WireConnection;163;1;161;0
WireConnection;163;0;434;0
WireConnection;504;0;505;0
WireConnection;504;1;500;0
WireConnection;232;0;163;0
WireConnection;177;0;64;0
WireConnection;177;1;83;0
WireConnection;243;1;244;0
WireConnection;243;0;435;0
WireConnection;499;1;504;0
WireConnection;402;0;236;0
WireConnection;402;1;64;4
WireConnection;233;0;243;0
WireConnection;332;0;177;0
WireConnection;332;1;329;0
WireConnection;506;0;499;1
WireConnection;506;1;507;0
WireConnection;234;0;402;0
WireConnection;234;1;332;0
WireConnection;234;2;237;0
WireConnection;259;0;83;4
WireConnection;334;0;64;4
WireConnection;256;0;259;0
WireConnection;249;1;250;0
WireConnection;249;0;238;0
WireConnection;508;0;506;0
WireConnection;508;1;509;0
WireConnection;333;0;334;0
WireConnection;251;1;332;0
WireConnection;251;0;234;0
WireConnection;510;0;508;0
WireConnection;178;0;333;0
WireConnection;178;1;256;0
WireConnection;178;2;172;4
WireConnection;178;3;249;0
WireConnection;239;0;251;0
WireConnection;239;1;172;0
WireConnection;432;0;239;0
WireConnection;432;3;178;0
WireConnection;431;0;432;0
WireConnection;431;1;511;0
ASEEND*/
//CHKSM=F96D15D832448E90DB089D899EDAD153C9D406EA