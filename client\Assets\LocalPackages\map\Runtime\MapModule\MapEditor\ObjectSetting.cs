﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;


namespace TFW.Map
{
    [ExecuteInEditMode]
    [SelectionBase]
    [Black]
    public class ObjectSetting : MonoBehaviour
    {
        public int objectDataID { set; get; }
        public string assetPath { set; get; }
        public ModelTemplate modelTemplate
        {
            get
            {
                var map = Map.currentMap;
                if (map != null)
                {
                    return map.GetEntityModelTemplate(objectDataID);
                }
                return null;
            }
        }

        void OnDestroy()
        {
            if (gameObject.name == MapCoreDef.COLLISION_MODEL_NAME)
            {
                if (Map.currentMap != null)
                {
                    var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
                    if (layer != null && layer.layerView.root != null)
                    {
                        var logic = layer.layerView.root.GetComponent<MapCollisionLayerLogic>();
                        if (logic != null)
                        {
                            logic.OnCollisionModelRemoved(gameObject.GetInstanceID());
                        }
                    }
                }
            }
            else if (gameObject.name == MapCoreDef.POLYGON_RIVER_MODEL_NAME)
            {
                if (Map.currentMap != null)
                {
                    var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
                    if (layer != null && layer.layerView.root != null)
                    {
                        var logic = layer.layerView.root.GetComponent<PolygonRiverLayerLogic>();
                        if (logic != null)
                        {
                            logic.OnCollisionModelRemoved(gameObject.GetInstanceID());
                        }
                    }
                }
            }
        }

        void OnDrawGizmos()
        {
            //var obj = GetModelGameObject();
            //var map = Map.currentMap;
            //if (map != null) {
            //    var objData = map.FindObject(objectDataID);
            //    if (objData is IMapObjectData) {
            //        var template = map.GetEntityModelTemplate(objData.id);
            //        var objView = map.GetMapObjectView(objData.id);
            //        if (objView != null) {
            //            Bounds worldBounds;
            //            GameObjectBoundsCalculator.TransformBounds(objView.model.gameObject.transform.localToWorldMatrix, template.bounds, out worldBounds);
            //            Gizmos.DrawWireCube(worldBounds.center, worldBounds.size);
            //        }
            //    }
            //}
        }

        public GameObject GetModelGameObject()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var objData = map.FindObject(objectDataID);
                if (objData is IMapObjectData)
                {
                    var template = map.GetEntityModelTemplate(objData.id);
                    var objView = map.GetMapObjectView(objData.id);
                    if (objView != null)
                    {
                        return objView.model.gameObject;
                    }
                }
            }
            return null;
        }
    }
}

#endif