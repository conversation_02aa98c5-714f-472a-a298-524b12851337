﻿using UnityEngine;

namespace TFW.Map
{
    public class CameraHorizontalRotationAutoUpdater
    {
        public void Start(float startRotation, float targetRotation, float rotateSpeed)
        {
            mEnabled = true;
            mStartRotation = startRotation;
            mTargetRotation = targetRotation;
            mRotateSpeed = rotateSpeed;
        }

        public void Cancel()
        {
            mEnabled = false;
        }

        public float Update(Vector3 currentCameraPos, out bool rotationUpdated, out Vector3 newCameraPos)
        {
            newCameraPos = Vector3.zero;
            rotationUpdated = mEnabled;
            if (mEnabled)
            {
                float newRotation = Mathf.MoveTowards(mStartRotation, mTargetRotation, mRotateSpeed * Time.deltaTime);
                float deltaAngle = newRotation - mStartRotation;
                mStartRotation = newRotation;
                var viewCenter = Map.currentMap.viewCenter;
                var cameraToViewCenter = currentCameraPos - viewCenter;
                float viewDistance = cameraToViewCenter.magnitude;
                Quaternion rotation = Quaternion.Euler(0, deltaAngle, 0);
                //if (deltaAngle != 0)
                //{
                //    Debug.LogError($"StartRotation: {mStartRotation}, Delta angle: {deltaAngle}, currentCameraPos: {currentCameraPos}, ViewCenter: {viewCenter}");
                //    int a = 1;
                //}
                newCameraPos = viewCenter + rotation * cameraToViewCenter.normalized * viewDistance;
                if (deltaAngle == 0)
                {
                    rotationUpdated = false;
                    mEnabled = false;
                }
                return mStartRotation;
            }
            return 0;
        }

        bool mEnabled = false;
        float mRotateSpeed;
        float mStartRotation;
        float mTargetRotation;
    }
}
