﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionCloneCameraCollider : EditorAction
    {
        public ActionCloneCameraCollider(int layerID, int dataID)
        {
            mLayerID = layerID;
            mNewDataID = Map.currentMap.nextCustomObjectID;
            var data = Map.currentMap.FindObject(dataID) as CameraColliderData;
            var bottomOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mBottomOutline = new List<Vector3>(bottomOutline.Count);
            mBottomOutline.AddRange(bottomOutline);
            mTopOutline = new List<Vector3>();
            var topOutline = data.topOutline;
            if (topOutline != null)
            {
                mTopOutline.AddRange(topOutline.outline);
            }
            mHeight = data.height;
            mMeshVertices = data.verticesCopy;
            mMeshIndices = data.indicesCopy;

            Vector3 offset = new Vector3(100, 0, 100);
            OffsetOutline(mBottomOutline, offset);
            OffsetOutline(mTopOutline, offset);
            if (mMeshVertices != null)
            {
                for (int i = 0; i < mMeshVertices.Length; ++i)
                {
                    mMeshVertices[i] += offset;
                }
            }
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }

            var data = new CameraColliderData(mNewDataID, Map.currentMap, mBottomOutline, mTopOutline, layer.displayVertexRadius, mHeight, mMeshVertices, mMeshIndices);
            layer.AddObject(data);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mNewDataID);
            return true;
        }

        void OffsetOutline(List<Vector3> outline, Vector3 offset)
        {
            if (outline != null)
            {
                for (int i = 0; i < outline.Count; ++i)
                {
                    //add some offset
                    outline[i] += offset;
                }
            }
        }

        int mLayerID;
        int mNewDataID;
        float mHeight;
        List<Vector3> mBottomOutline;
        List<Vector3> mTopOutline;
        Vector3[] mMeshVertices;
        int[] mMeshIndices;
    }
}

#endif