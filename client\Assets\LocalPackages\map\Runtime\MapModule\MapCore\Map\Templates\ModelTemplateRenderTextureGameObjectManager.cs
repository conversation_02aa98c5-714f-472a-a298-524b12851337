﻿ 



 
 

//create by wzw at 2019.7.17


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理model template使用的render texture game object
    public class ModelTemplateRenderToTextureGameObjectManager
    {
        public ModelTemplateRenderToTextureGameObjectManager(GameObject parent)
        {
            if (Map.currentMap.isEditorMode)
            {
                mRenderObjectToTexture = new RenderObjectToTexture(null, parent);
            }
        }

        public void OnDestroy()
        {
            foreach (var p in mModelTemplateRenderTextures)
            {
                var list = p.Value;
                for (int i = 0; i < list.Count; ++i)
                {
                    DestroyObject(list[i]);
                }
            }
            mModelTemplateRenderTextures = null;

            foreach (var p in mRenderTextures)
            {
                if (Map.currentMap.isEditorMode)
                {
                    Object.DestroyImmediate(p.Value);
                }
                else
                {
                    Object.Destroy(p.Value);
                }
            }

            if (mRenderObjectToTexture != null)
            {
                mRenderObjectToTexture.OnDestroy(Map.currentMap.isEditorMode);
            }
        }

        void DestroyObject(GameObject obj)
        {
            var mtl = obj.GetComponent<MeshRenderer>().sharedMaterial;
            var mesh = obj.GetComponent<MeshFilter>().sharedMesh;
            if (Map.currentMap.isEditorMode)
            {
                Object.DestroyImmediate(mtl);
                Object.DestroyImmediate(mesh);
                Utils.DestroyObject(obj);
            }
            else
            {
                Object.Destroy(mtl);
                Object.Destroy(mesh);
                Utils.DestroyObject(obj);
            }
        }

        public void ClearCachedPool(int modelTemplateID)
        {
            List<GameObject> objectPool;
            mModelTemplateRenderTextures.TryGetValue(modelTemplateID, out objectPool);
            if (objectPool != null)
            {
                for (int i = 0; i < objectPool.Count; ++i)
                {
                    Utils.DestroyObject(objectPool[i]);
                }
                mModelTemplateRenderTextures.Remove(modelTemplateID);
            }

            RenderTexture renderTexture = null;
            mRenderTextures.TryGetValue(modelTemplateID, out renderTexture);
            if (renderTexture != null)
            {
                Object.DestroyImmediate(renderTexture);
            }
            mRenderTextures.Remove(modelTemplateID);
        }

        public GameObject Require(int modelTemplateID, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            List<GameObject> objectPool;
            mModelTemplateRenderTextures.TryGetValue(modelTemplateID, out objectPool);
            if (objectPool == null)
            {
                objectPool = new List<GameObject>();
                mModelTemplateRenderTextures[modelTemplateID] = objectPool;
            }

            GameObject obj = null;
            int n = objectPool.Count;
            if (n > 0)
            {
                obj = objectPool[n - 1];
                objectPool.RemoveAt(n - 1);
            }

            if (obj == null)
            {
                obj = CreateRenderTextureAndGameObject(modelTemplateID);
            }

            //add some offset
            obj.transform.position = position + new Vector3(0, 0.05f, 0);
            obj.transform.rotation = rotation;
            obj.transform.localScale = scale;
            obj.SetActive(true);

            return obj;
        }

        public void Release(int modelTemplateID, GameObject gameObject)
        {
            if (gameObject == null) {
                return;
            }
            if (mModelTemplateRenderTextures.ContainsKey(modelTemplateID))
            {
                mModelTemplateRenderTextures[modelTemplateID].Add(gameObject);
                gameObject.SetActive(false);
#if UNITY_EDITOR
                var parentObj = Map.currentMap.view.poolObjectRoot;
                if (parentObj != null)
                {
                    gameObject.transform.SetParent(parentObj.transform);
                }
#else
            gameObject.transform.SetParent(null);
#endif
            }
        }

        public void Update(float cameraHeight)
        {
            if (cameraHeight < 10)
            {
                cameraHeight = 10;
            }

            foreach (var p in mRenderTextures)
            {
                var modelTemplateID = p.Key;
                var renderTexture = p.Value;

                var modelTemplate = Map.currentMap.FindObject(modelTemplateID) as ModelTemplate;
                var prefabPath = modelTemplate.GetLODPrefabPath(0);

                var prefab = Map.currentMap.view.reusableGameObjectPool.Require(prefabPath, Vector3.zero, Vector3.one, Quaternion.identity);

                mRenderObjectToTexture.Render(prefab, false, cameraHeight, renderTexture, true, false, new Bounds());

                Map.currentMap.view.reusableGameObjectPool.Release(prefabPath, prefab, Map.currentMap);
            }
        }

        GameObject CreateRenderTextureAndGameObject(int modelTemplateID)
        {
            var modelTemplate = Map.currentMap.FindObject(modelTemplateID) as ModelTemplate;
            var prefabPath = modelTemplate.GetLODPrefabPath(0);

            var prefab = Map.currentMap.view.reusableGameObjectPool.Require(prefabPath, Vector3.zero, Vector3.one, Quaternion.identity);
            RenderTexture renderTexture = null;
            string name = Utils.GetPathName(prefabPath, false);
            mRenderTextures.TryGetValue(modelTemplateID, out renderTexture);
            if (renderTexture == null)
            {
                renderTexture = new RenderTexture(512, 512, 16, RenderTextureFormat.Default, RenderTextureReadWrite.Default);
                mRenderObjectToTexture.Render(prefab, false, 40, renderTexture, true, false, new Bounds());

                mRenderTextures[modelTemplateID] = renderTexture;
            }

            var objectBounds = GameObjectBoundsCalculator.CalculateBounds(prefab, false);
            var obj = CreateGameObject(name, renderTexture, objectBounds.min, objectBounds.max);

            Map.currentMap.view.reusableGameObjectPool.Release(prefabPath, prefab, Map.currentMap);
            return obj;
        }

        GameObject CreateGameObject(string name, RenderTexture renderTexture, Vector3 min, Vector3 max)
        {
            var obj = new GameObject(name);
            obj.transform.parent = Map.currentMap.root.transform;
            var mesh = CreateMesh(min, max);
            var meshFilter = obj.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;
            var renderer = obj.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Transparent"));
            mtl.mainTexture = renderTexture;
            mtl.renderQueue = 3202;
            renderer.sharedMaterial = mtl;

            return obj;
        }

        Mesh CreateMesh(Vector3 min, Vector3 max)
        {
            var mesh = new Mesh();
            var vertices = new Vector3[4]
            {
                new Vector3(min.x, 0, min.z),
                new Vector3(min.x, 0, max.z),
                new Vector3(max.x, 0, max.z),
                new Vector3(max.x, 0, min.z),
            };
            var indices = new int[6]
            {
                0, 1, 2, 0, 2, 3
            };
            var uvs = new Vector2[4]
            {
                Vector2.zero,
                new Vector2(0, 1),
                Vector2.one,
                new Vector2(1, 0),
            };
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.uv = uvs;
            mesh.RecalculateBounds();
            mesh.RecalculateNormals();
            return mesh;
        }

        RenderObjectToTexture mRenderObjectToTexture;
        Dictionary<int, RenderTexture> mRenderTextures = new Dictionary<int, RenderTexture>();
        Dictionary<int, List<GameObject>> mModelTemplateRenderTextures = new Dictionary<int, List<GameObject>>();
    }
}