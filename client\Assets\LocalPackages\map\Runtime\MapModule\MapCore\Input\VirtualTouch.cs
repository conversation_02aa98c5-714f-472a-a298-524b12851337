﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class VirtualTouch : IMapTouch
    {
        public VirtualTouch()
        {
            mHistory = new VirtualTouchHistory(2);
        }

        //当前touch的位置
        public Vector2 position { get { return mHistory.current; } set { mHistory.AddPoint(value); } }
        public Vector2 lastPosition { get { return mHistory.previous; } }
        //touch第一次接触屏幕时的位置
        public Vector2 touchPosition { get { return mHistory.startPosition; } }
        public MapTouchState state { get; set; }
        public IMapTouchHistory history { get { return mHistory; } }
        public bool isMoved { get { return mHistory.isMoved; } }
        public bool alive { get; set; }
        public int fingerId { get { return 0; } }
        public Vector2 scrollDelta { get { return Vector2.zero; } }

        //是否屏蔽ui点击
        public bool blockUIClick { get; set; }
        //是否屏蔽ui拖动
        public bool blockUIDrag { get; set; }

        public void Reset()
        {
            mHistory.Clear();
        }

        VirtualTouchHistory mHistory;
    }
}
