﻿ 



 
 

//created by wzw at 2020/2/17

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionLayerView : PolygonObjectLayerView
    {
        public RegionLayerView(MapLayerData layerData, bool asyncLoading) : base(layerData, asyncLoading)
        {
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            Utils.DestroyObject(mBorderObject);
            Utils.DestroyObject(mBorderMesh);
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new RegionView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public void UpdateBorderMesh(Vector3[] vertices, int[] indices, Material material)
        {
            if (vertices == null || vertices.Length == 0)
            {
                Utils.DestroyObject(mBorderObject);
                Utils.DestroyObject(mBorderMesh);
                mBorderObject = null;
                mBorderMesh = null;
            }
            else
            {
                if (mBorderMesh == null)
                {
                    mBorderMesh = new Mesh();
                }
                mBorderMesh.Clear();
                mBorderMesh.vertices = vertices;
                mBorderMesh.triangles = indices;
                mBorderMesh.RecalculateBounds();
                mBorderMesh.RecalculateNormals();

                MeshRenderer renderer = null;
                if (mBorderObject == null)
                {
                    mBorderObject = new GameObject();
                    Utils.HideGameObject(mBorderObject);
                    mBorderObject.transform.parent = root.transform;
                    renderer = mBorderObject.AddComponent<MeshRenderer>();
                    renderer.sharedMaterial = material;
                    var filter = mBorderObject.AddComponent<MeshFilter>();
                    filter.sharedMesh = mBorderMesh;
                }
                else
                {
                    renderer = mBorderObject.GetComponent<MeshRenderer>();
                }
                renderer.sharedMaterial = material;
            }
        }

        public void SetBorderMaterial(Material mtl)
        {
            if (mBorderObject != null)
            {
                var renderer = mBorderObject.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = mtl;
            }
        }

        public Mesh GetRegionBorderLineMesh(int regionID)
        {
            var view = GetObjectView(regionID) as RegionView;
            if (view != null)
            {
                return view.GetBorderLineMesh();
            }
            return null;
        }

        public Mesh borderMesh { get { return mBorderMesh; } }

        GameObject mBorderObject;
        Mesh mBorderMesh;
    }
}

#endif