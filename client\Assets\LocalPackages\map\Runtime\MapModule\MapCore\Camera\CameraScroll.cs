﻿ 



 
 


using UnityEngine;
using System.Text;

namespace TFW.Map
{
    public class CameraScroll : ZoomActionBase
    {
        public CameraScroll(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            int touchCount = MapTouchManager.touchCount;
            if (mOn && touchCount == 1)
            {
                var touch = MapTouchManager.GetTouch(0);

                Vector2 center = (Vector2)touch.position;
                int centerX = (int)center.x;
                int centerY = (int)center.y;

                if (touch.scrollDelta.y != 0)
                {
                    float minCameraHeight = MapCameraMgr.currentMinimumHeight;
                    float maxCameraHeight = MapCameraMgr.cameraSetting.cameraMaxHeight;
                    if (mHasHeightLimit)
                    {
                        if (currentCameraPos.y <= minCameraHeight)
                        {
                            if (touch.scrollDelta.y > 0)
                            {
                                return;
                            }
                        }
                        else if (currentCameraPos.y >= maxCameraHeight)
                        {
                            if (touch.scrollDelta.y < 0)
                            {
                                return;
                            }
                        }
                    }

                    enabled = true;

                    float maxZoomSpeed = 0.2f;
                    float minZoomSpeed = 0.05f;

                    var cameraTransform = Map.currentMap.camera.transform;
                    Vector3 cameraTargetPos = cameraTransform.position;
                    float t = (cameraTargetPos.y - minCameraHeight) / (maxCameraHeight - minCameraHeight);
                    t = Mathf.Clamp01(t);
                    float zoomSpeed = Mathf.SmoothStep(minZoomSpeed, maxZoomSpeed, t);
                    float delta = (0.03f + zoomSpeed) * Mathf.Sign(touch.scrollDelta.y);

                    float scrollRateMultiplier = 1.0f;
                    if (mScrollRateModifyCallback != null)
                    {
                        scrollRateMultiplier = mScrollRateModifyCallback(currentCameraPos.y);
                    }

                    mScrollRate = 1 - delta * scrollRateMultiplier;
                    
                    TouchZoomed(centerX, centerY, mScrollRate);
                    MapCameraMgr.ResetAutoUpdateTargetHeight();
                }
            }
            else
            {
                Reset();
                isFinished = true;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            var pos = GetCameraPos();
            MapCameraMgr.SetCameraHeightChangeTargetHeight(pos.y);
            return pos;
        }

        public void SetScrollRateModifyCallback(System.Func<float, float> callback)
        {
            mScrollRateModifyCallback = callback;
        }

        public override void OnFinishImpl()
        {
        }

        public override string GetDebugMessage()
        {
            var builder = new StringBuilder();
            builder.AppendLine($"on: {mOn}");
            builder.AppendLine($"enabled: {enabled}");
            return builder.ToString();
        }

        public bool hasHeightLimit { set { mHasHeightLimit = value; } get { return mHasHeightLimit; } }
        public bool on
        {
            get
            {
                return mOn;
            }
            set
            {
                mOn = value;
                if (value == false)
                {
                    isFinished = true;
                }
            }
        }

        Vector3 mZoomTargetWorldPos;
        Vector2 mZoomScreenPos;
        float mScrollRate = 1.0f;
        bool mHasHeightLimit = false;
        bool mOn = true;
        System.Func<float, float> mScrollRateModifyCallback;
    }
}
