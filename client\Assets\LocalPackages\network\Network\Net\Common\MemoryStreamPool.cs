﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;

namespace Net.Common
{
    /// <summary>
    /// MemoryStream对象池(不会自动检测数据，需手动释放清理数据)
    /// </summary>
    public static class MemoryStreamPool
    {
        private static Stack<PooledMemoryStream> s_Instances = new Stack<PooledMemoryStream>();
        private static readonly object s_LockHelper = new object();

        public static PooledMemoryStream Get()
        {
            lock (s_LockHelper)
            {
                if (s_Instances.Count > 0)
                {
                    return s_Instances.Pop();
                }
            }

            return new PooledMemoryStream();
        }

        public static void Release(PooledMemoryStream ms)
        {
            lock (s_LockHelper)
            {
                if (ms != null)
                {
                    ms.Position = 0;
                    ms.SetLength(0);
                    //var buffer = ms.GetBuffer();            
                    //Array.Clear(buffer, 0, buffer.Length);  //buffer内存置0，擦除脏数据。
                    s_Instances.Push(ms);
                }
            }
        }
    }

    /// <summary>
    /// 由对象池管理的MemoryStream
    /// </summary>
    public sealed class PooledMemoryStream : MemoryStream
    {
        public override void Close()
        {
#if UNITY_EDITOR
            UnityEngine.Debug.LogError("[PooledMemoryStream] PooledMemoryStream should never be closed. Skipping.");
#endif

            //base.Close();
        }
    }
}