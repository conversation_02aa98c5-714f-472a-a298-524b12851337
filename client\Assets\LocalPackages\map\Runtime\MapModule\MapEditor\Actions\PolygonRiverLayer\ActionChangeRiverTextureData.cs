﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionChangeRiverTextureData : EditorAction
    {
        public ActionChangeRiverTextureData(int dataID)
        {
            mDataID = dataID;
            GetSectionTextureData(mOldData);
        }

        bool SetTextureData(List<Color[]> val)
        {
            var river = Map.currentMap.FindObject(mDataID) as PolygonRiverData;
            if (river == null)
            {
                return false;
            }
            int textureSize = river.textureSize * river.textureSize;
            for (int i = 0; i < river.sections.Count; ++i)
            {
                if (river.sections[i].texture == null)
                {
                    return false;
                }
                bool suc = river.sections[i].UpdateTextureData(val[i], 0, 0, river.textureSize, river.textureSize, false);
                var cache = river.sections[i].textureData;
                //update cache
                for (int k = 0; k < textureSize; ++k)
                {
                    cache[k] = val[i][k];
                }
            }
            return true;
        }

        public override bool Do()
        {
            return SetTextureData(mNewData);
        }

        public override bool Undo()
        {
            return SetTextureData(mOldData);
        }

        public void FinishPainting()
        {
            GetSectionTextureData(mNewData);
        }

        void GetSectionTextureData(List<Color[]> channelVal)
        {
            var river = Map.currentMap.FindObject(mDataID) as PolygonRiverData;
            int textureSize = river.textureSize * river.textureSize;
            for (int i = 0; i < river.sections.Count; ++i)
            {
                Color[] val = new Color[textureSize];
                var colors = river.sections[i].textureData;
                for (int k = 0; k < textureSize; ++k)
                {
                    val[k] = colors[k];
                }
                channelVal.Add(val);
            }
        }

        int mDataID;
        //todo change to byte array or use pool
        List<Color[]> mNewData = new List<Color[]>();
        List<Color[]> mOldData = new List<Color[]>();
    }
}


#endif