﻿ 



 
 


#if UNITY_EDITOR


using UnityEngine;
using UnityEditor;
using System;

namespace TFW.Map
{
    public class CameraBoundsEditor
    {
        public void DrawInspector()
        {
            //if (Map.currentMap != null) {
            //    EditorGUILayout.BeginVertical("GroupBox");
            //    mEditCameraBounds = GUILayout.Toggle(mEditCameraBounds, "Edit Camera Height Range");

            //    if (mEditCameraBounds) {
            //        float curMinHeight = Map.currentMap.data.cameraMinHeight;
            //        float minHeight = EditorGUILayout.FloatField("Camera Minimum Height", curMinHeight);
            //        SetCameraMinHeight(minHeight);

            //        float curMaxHeight = Map.currentMap.data.cameraMaxHeight;
            //        float maxHeight = EditorGUILayout.FloatField("Camera Maximum Height", curMaxHeight);
            //        SetCameraMaxHeight(maxHeight);
            //    }
            //    else
            //    {
            //        EditorGUILayout.FloatField("Camera Minimum Height", Map.currentMap.data.cameraMinHeight);
            //        EditorGUILayout.FloatField("Camera Maximum Height", Map.currentMap.data.cameraMaxHeight);
            //    }

            //    EditorGUILayout.EndVertical();
            //}
        }

        void SetCameraMinHeight(float height)
        {
            var data = Map.currentMap.data as EditorMapData;
            //data.cameraMinHeight = height;
        }

        void SetCameraMaxHeight(float height)
        {
            var data = Map.currentMap.data as EditorMapData;
            //data.cameraMaxHeight = height;
        }

        //bool mEditCameraBounds = false;
    }
}

#endif
