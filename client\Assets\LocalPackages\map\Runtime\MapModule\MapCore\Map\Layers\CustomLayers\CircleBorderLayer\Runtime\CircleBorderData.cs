﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class CircleBorderData : ModelData
    {
        public CircleBorderData(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, bool isAlwaysVisibleAtHigherLODs)
            : base(id, map, flag, position, rotation, scale, modelTemplate, true)
        {
            mIsAlwaysVisibleAtHigherLODs = isAlwaysVisibleAtHigherLODs;
        }

        public bool isAlwaysVisibleAtHigherLODs { get { return mIsAlwaysVisibleAtHigherLODs; } }

        public override Rect GetBounds()
        {
            if (mIsAlwaysVisibleAtHigherLODs)
            {
                //return a very big bounds to make sure object is not culled by viewport
                return new Rect(Vector2.zero, new Vector2(1000000, 1000000));
            }
            return base.GetBounds();
        }

        //public override bool IsLocalBounds()
        //{
        //    if (mIsAlwaysVisibleAtHigherLODs)
        //    {
        //        return false;
        //    }
        //    return true;
        //}

        //是否在只使用一个模型的lod中一直显示
        bool mIsAlwaysVisibleAtHigherLODs;
    }
}
