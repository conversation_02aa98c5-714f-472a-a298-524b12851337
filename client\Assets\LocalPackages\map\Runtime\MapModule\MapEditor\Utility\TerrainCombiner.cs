﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;


namespace TFW.Map
{
    //将使用不同mask贴图的地表替换成同一个材质,将mask贴图打包成图集,从而可以batch渲染
    public static class TerrainCombiner
    {
        class PackInfo
        {
            public PackInfo(int startIdx, int count, Texture2D packedTexture, Rect[] uvs)
            {
                this.startIdx = startIdx;
                this.count = count;
                this.packedTexture = packedTexture;
                this.uvs = uvs;
            }

            public int startIdx { get; private set; }
            public int count { get; private set; }
            public Texture2D packedTexture { get; private set; }
            public Rect[] uvs { get; private set; }
        }

        //terrainPrefabs:同一个lod下的地表prefab
        //terrainMaterial:所有地表prefab使用的material
        public static void Combine(List<GameObject> terrainPrefabs, string outputFolder, int border = 4)
        {
            string errMsg = CheckValidPrefabs(terrainPrefabs);
            if (!string.IsNullOrEmpty(errMsg))
            {
                EditorUtility.DisplayDialog("Error", $"Can't combine prefabs because {errMsg}", "OK");
                return;
            }

            Material terrainMtl = null;
            List<Texture2D> maskTextures = new List<Texture2D>();
            for (int i = 0; i < terrainPrefabs.Count; ++i)
            {
                var renderers = terrainPrefabs[i].GetComponentsInChildren<MeshRenderer>();
                Debug.Assert(renderers.Length == 1);
                terrainMtl = renderers[0].sharedMaterial;
                var maskTexture = terrainMtl.GetTexture("_MaskTex") as Texture2D;
                Debug.Assert(maskTexture != null);
                maskTextures.Add(maskTexture);
            }

            //为了绕开texture的readable设置,创建一些临时贴图
            List<Texture2D> tempTextures = CreateTempTextures(maskTextures, border);
            //将所有mask贴图打包成一张或多张图集
            List<PackInfo> packInfos = PackAllTextures(tempTextures);
            for (int i = 0; i < packInfos.Count; ++i)
            {
                //对每个图集生成一个material
                var mtl = CreateMaterialAsset(packInfos[i], i, terrainMtl, outputFolder);
                for (int k = 0; k < packInfos[i].count; ++k)
                {
                    var idx = k + packInfos[i].startIdx;
                    //将地表prefab替换成使用新材质的prefab
                    var packedTextureSize = new Vector2(packInfos[i].packedTexture.width, packInfos[i].packedTexture.height);
                    ReplaceTerrainPrefab(terrainPrefabs[idx], mtl, packInfos[i].uvs[k], border, packedTextureSize);
                }
            }

            //删除临时贴图
            for (int i = 0; i < tempTextures.Count; ++i)
            {
                Object.DestroyImmediate(tempTextures[i]);
            }

            AssetDatabase.Refresh();
        }

        static List<Texture2D> CreateTempTextures(List<Texture2D> textures, int border)
        {
            List<Texture2D> tempTextures = new List<Texture2D>(textures.Count);
            for (int i = 0; i < textures.Count; ++i)
            {
                var texPath = AssetDatabase.GetAssetPath(textures[i]);
                Debug.Assert(!string.IsNullOrEmpty(texPath));
                var tempTexture = EditorUtils.CreateTexture(texPath, false);
                //将贴图扩大一圈,应对mipmap和linear采样的接缝问题
                tempTexture = ExpandTexture(tempTexture, border);
                tempTextures.Add(tempTexture);

            }
            return tempTextures;
        }

        static Texture2D ExpandTexture(Texture2D texture, int border)
        {
            Debug.Assert(texture.isReadable);
            int oldWidth = texture.width;
            int oldHeight = texture.height;
            int newWidth = texture.width + border * 2;
            int newHeight = texture.height + border * 2;
            var expandedTexture = new Texture2D(newWidth, newHeight, texture.format, false);
            Color[] oldTextureData = texture.GetPixels();
            Color[] expandedTextureData = new Color[newWidth * newHeight];
            if (border > 0)
            {
                //left top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = 0; x < border; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[0];
                    }
                }
                //right top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = oldWidth + border; x < newWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[oldWidth - 1];
                    }
                }
                //left bottom
                for (int y = border + oldHeight; y < newHeight; ++y)
                {
                    for (int x = 0; x < border; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        int srcIdx = (oldHeight - 1) * oldWidth;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }
                //right bottom
                for (int y = border + oldHeight; y < newHeight; ++y)
                {
                    for (int x = border + oldWidth; x < newWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[oldTextureData.Length - 1];
                    }
                }

                //region 0, top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = border; x < border + oldWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = x - border;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 2, bottom
                for (int y = oldHeight + border; y < newHeight; ++y)
                {
                    for (int x = border; x < border + oldWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (oldHeight - 1) * oldWidth + x - border;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 1, left
                for (int x = 0; x < border; ++x)
                {
                    for (int y = border; y < border + oldHeight; ++y)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (y - border) * oldWidth;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 3, right
                for (int x = oldWidth + border; x < newWidth; ++x)
                {
                    for (int y = border; y < border + oldHeight; ++y)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (y - border) * oldWidth + oldWidth - 1;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }
            }

            //middle
            for (int y = border; y < border + oldHeight; ++y)
            {
                for (int x = border; x < border + oldWidth; ++x)
                {
                    int dstIdx = y * newWidth + x;
                    var srcIdx = (y - border) * oldWidth + x - border;
                    expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                }
            }

            expandedTexture.SetPixels(expandedTextureData);
            expandedTexture.Apply();
            return expandedTexture;
        }

        static List<PackInfo> PackAllTextures(List<Texture2D> maskTextures)
        {
            List<PackInfo> ret = new List<PackInfo>();
            int offset = 0;
            while (offset < maskTextures.Count)
            {
                int maxSize = 2048;
                int padding = 8;
                Texture2D packedMaskTexture = new Texture2D(maxSize, maxSize, TextureFormat.RGBA32, true);
                int maxPackedCount = 0;
                //先测试最大能pack多少个texture
                for (int j = offset; j < maskTextures.Count; ++j)
                {
                    var triedTextures = new Texture2D[j - offset + 1];
                    for (int k = 0; k < triedTextures.Length; ++k)
                    {
                        triedTextures[k] = maskTextures[offset + k];
                    }
                    Rect[] retUV = packedMaskTexture.PackTextures(triedTextures, padding, maxSize, false);
                    if (retUV == null)
                    {
                        maxPackedCount = triedTextures.Length - 1;
                        break;
                    }
                }
                if (maxPackedCount == 0)
                {
                    maxPackedCount = maskTextures.Count - offset;
                }

                if (maxPackedCount == 0)
                {
                    //不能pack,贴图超过2048了
                    return null;
                }

                var textures = new Texture2D[maxPackedCount];
                for (int k = 0; k < textures.Length; ++k)
                {
                    textures[k] = maskTextures[k + offset];
                }
                var uvs = packedMaskTexture.PackTextures(textures, padding, maxSize, false);

                PackInfo pi = new PackInfo(offset, maxPackedCount, packedMaskTexture, uvs);
                ret.Add(pi);

                offset += maxPackedCount;
            }

            return ret;
        }

        //创建使用同一图集的material asset
        static Material CreateMaterialAsset(PackInfo pi, int idx, Material terrainUsedMaterial, string outputFolder)
        {
            //create texture asset
            string texturePath = $"{outputFolder}/terrain_mask_atlas_{idx}.png";
            byte[] bytes = pi.packedTexture.EncodeToPNG();
            System.IO.File.WriteAllBytes(texturePath, bytes);
            AssetDatabase.Refresh();
            //create material asset
            texturePath = Utils.ConvertToUnityAssetsPath(texturePath);
            var maskAtlasTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
            var newMtl = Object.Instantiate<Material>(terrainUsedMaterial);
            newMtl.shader = Shader.Find("TFW/TerrainUseAtlas");
            newMtl.SetTexture("_MaskTex", maskAtlasTexture);
            string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/terrain_atlas_mtl_{idx}.mat");
            AssetDatabase.CreateAsset(newMtl, mtlPath);
            return newMtl;
        }

        static void ReplaceTerrainPrefab(GameObject prefab, Material mtl, Rect uv, int border, Vector2 packedTextureSize)
        {
            var meshFilter = prefab.GetComponentInChildren<MeshFilter>();
            var newMesh = Object.Instantiate<Mesh>(meshFilter.sharedMesh);
            CreateUV2(newMesh, uv, border, packedTextureSize);

            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            Debug.Assert(!string.IsNullOrEmpty(prefabPath));
            var prefabFolder = Utils.GetFolderPath(prefabPath);
            var newPrefab = Object.Instantiate(prefab);
            var newMeshFilter = newPrefab.GetComponentInChildren<MeshFilter>();
            newMeshFilter.sharedMesh = newMesh;
            var newMeshRenderer = newPrefab.GetComponentInChildren<MeshRenderer>();
            newMeshRenderer.sharedMaterial = mtl;
            var name = Utils.GetPathName(prefabPath, false);
            SavePrefab(prefabFolder, name, newPrefab);

            Object.DestroyImmediate(newPrefab);
        }

        static void CreateUV2(Mesh newMesh, Rect uv, int border, Vector2 packedTextureSize)
        {
            //调整uv的偏移
            Vector2 offset = new Vector2(border / packedTextureSize.x, border / packedTextureSize.y);
            int vertexCount = newMesh.vertexCount;
            Vector2[] uv2 = new Vector2[vertexCount];
            var vertices = newMesh.vertices;
            var bounds = newMesh.bounds;
            var min = bounds.min;
            var max = bounds.max;
            for (int i = 0; i < vertexCount; ++i)
            {
                uv2[i].x = uv.xMin + offset.x + (vertices[i].x - min.x) / (max.x - min.x) * (uv.xMax - uv.xMin - offset.x * 2);
                uv2[i].y = uv.yMin + offset.y + (vertices[i].z - min.z) / (max.z - min.z) * (uv.yMax - uv.yMin - offset.y * 2);
            }
            newMesh.uv2 = uv2;
        }

        public static void SavePrefab(string outputFolder, string name, GameObject obj)
        {
            if (obj == null || string.IsNullOrEmpty(outputFolder) || string.IsNullOrEmpty(name))
            {
                return;
            }

            var newObj = UnityEngine.Object.Instantiate<GameObject>(obj);
            //save mesh
            string meshPath = outputFolder + "/" + name + ".asset";
            var mesh = obj.GetComponentInChildren<MeshFilter>().sharedMesh;
            AssetDatabase.CreateAsset(mesh, meshPath);

            //save prefab
            string prefabPath = outputFolder + "/" + name + ".prefab";
            PrefabUtility.SaveAsPrefabAsset(newObj, prefabPath);

            UnityEngine.Object.DestroyImmediate(newObj);
        }

        static string CheckValidPrefabs(List<GameObject> prefabs)
        {
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var renderers = prefabs[i].GetComponentsInChildren<MeshRenderer>();
                if (renderers == null || renderers.Length != 1)
                {
                    return $"{prefabs[i].name} has more than one mesh renderer!";
                }

                if (renderers[0].sharedMaterial == null)
                {
                    return $"{renderers[0].name} material not found!";
                }

                var maskTexture = renderers[0].sharedMaterial.GetTexture("_MaskTex") as Texture2D;
                if (maskTexture == null)
                {
                    return $"_MaskTex property not found in shader!";
                }

                for (int k = 0; k < renderers.Length; ++k)
                {
                    //只合并使用一个material的prefab
                    if (renderers[k].sharedMaterials.Length > 1)
                    {
                        return $"{renderers[k].name} has more than one material!";
                    }
                }

                var filters = prefabs[i].GetComponentsInChildren<MeshFilter>();
                if (filters == null || filters.Length != 1)
                {
                    return $"{prefabs[i].name} MeshFilter component not found!";
                }

                for (int k = 0; k < filters.Length; ++k)
                {
                    //只合并使用一个submesh的prefab
                    if (filters[k].sharedMesh.subMeshCount > 1)
                    {
                        return $"{filters[k].name} mesh has more than one subMesh!";
                    }
                }
            }
            return "";
        }
    }
}

#endif