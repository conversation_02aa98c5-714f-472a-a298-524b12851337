%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SL_Map_River_03
  m_Shader: {fileID: 4800000, guid: 031a91cf549796a43b792a8af4fd9249, type: 3}
  m_ShaderKeywords: OCEANON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: e689b18a867d47646959396e22cfe538, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cube:
        m_Texture: {fileID: 8900000, guid: a707718baa7b4c043b35455b813c8d58, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Decal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Depth:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FoamMask:
        m_Texture: {fileID: 2800000, guid: 47a4dda6e93bcaf4e911f323a0ee6496, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: e689b18a867d47646959396e22cfe538, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 2800000, guid: 312059b7040efd9459f8301b200e108a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormTex:
        m_Texture: {fileID: 2800000, guid: 7d2d05d9dedb8ae40844eae665b074ca, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Reflection:
        m_Texture: {fileID: 2800000, guid: 16c41c709e125ff41b947e6c4e9d5133, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _WaterSurface:
        m_Texture: {fileID: 2800000, guid: 6ba7fe515a301064789d129441500272, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - HIGH: 0
    - MIDDLE: 0
    - OCEANON: 1
    - _Alpha: 0
    - _AlphaClip: 0
    - _BigFoamAmplitude: 0.15
    - _BigFoamPosition: 0.15
    - _BigFoamSpeed: 0.8
    - _Blend: 0
    - _BumpScale: 1
    - _CubeIntensity: 0.954
    - _Cull: 2
    - _Cutoff: 0.5
    - _DeepOffset: 1
    - _DepthEdge: 0.219
    - _DstBlend: 0
    - _EdgeFade: 1
    - _EnvironmentReflections: 1
    - _Foam1Speed: 0.1
    - _Foam2Speed: 0.165
    - _FoamDepth: 1
    - _FoamEdgeAlpha: 0
    - _FoamIntensity: 0
    - _FoamRange: 0.6
    - _FoamWidth: 0.0366
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Intensity: 0
    - _LandColorIntensity: 1.269
    - _Metallic: 0
    - _NormalIntensity: 0.818
    - _NormalStrength: 0.322
    - _OcclusionStrength: 1
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReflectPower: 0.183
    - _Shininess: 0.0001
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _Tiling: 0.11
    - _TransAmount: 0.342
    - _Vertex_Use_G: 0
    - _Vertex_color: 0
    - _WaterSurfaceIntensity: 0.5
    - _WaterTiling: 1.68
    - _WaveWidth: 0
    - _WorkflowMode: 1
    - _WorldUVTilling: 0.27
    - _ZWrite: 1
    - _foamLineA: 0
    - _waterBlend: 0.33
    - _width: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DeepColor: {r: 0.17254901, g: 0.45639738, b: 0.59607846, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FoamColor: {r: 1, g: 1, b: 1, a: 1}
    - _FoamCotrol: {r: 0.5, g: 0.21, b: 0, a: 0}
    - _FoamSpeed: {r: 1, g: 1, b: 1, a: 1}
    - _LandDeepColor: {r: 0.48995668, g: 0.5943396, b: 0.4177198, a: 1}
    - _LandShallowColor: {r: 0.9056604, g: 0.75527036, b: 0.6194375, a: 1}
    - _LightPos: {r: -459.6, g: 2772, b: 2173, a: 185}
    - _ShadowColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShallowColor: {r: 0.12148449, g: 0.3805538, b: 0.735849, a: 1}
    - _SpecColor: {r: 0.6309185, g: 0.9622642, b: 0.8860576, a: 1}
    - _SurfaceCol: {r: 1, g: 1, b: 1, a: 1}
    - _SurfaceColVar: {r: 1, g: 1, b: 1, a: 1}
    - _TintColor: {r: 1, g: 1, b: 1, a: 1}
    - _WaterBalance: {r: 1, g: 1, b: 1, a: 0}
    - _WaterSpeed: {r: 1, g: 1, b: 0, a: 0}
    - _WaterSurfaceTilling: {r: 1, g: 1, b: 0, a: 0}
    - _WaveSpeed: {r: 0.02, g: 0.02, b: -0.02, a: -0.02}
    - _foamLineCol: {r: 1, g: 1, b: 1, a: 1}
    - _foamTilling: {r: 1000, g: 1000, b: 1000, a: 155.33}
    - _waterColor: {r: 0.6698113, g: 0.6698113, b: 0.6698113, a: 1}
    - _waveTilling: {r: 1, g: 1, b: 1, a: 1}
--- !u!114 &1439476327997269466
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 2
