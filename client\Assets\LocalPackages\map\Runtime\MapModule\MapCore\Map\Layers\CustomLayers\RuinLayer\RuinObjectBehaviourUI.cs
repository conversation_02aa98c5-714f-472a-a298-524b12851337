﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [CustomEditor(typeof(RuinObjectBehaviour))]
    class RuinObjectBehaviourUI : Editor
    {
        PropertyDataEditor mPropertyEditor;
        RuinObjectBehaviour mBehaviour;

        private void OnEnable()
        {
            mBehaviour = target as RuinObjectBehaviour;
            var ruinData = Map.currentMap.FindObject(mBehaviour.objectID) as RuinData;
            mPropertyEditor = new PropertyDataEditor(false);
            mPropertyEditor.Show(ruinData.properties, null, null, null);
        }

        public override void OnInspectorGUI()
        {
            mPropertyEditor.Draw();
        }
    }
}

#endif