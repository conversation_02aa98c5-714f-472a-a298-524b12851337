﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class ReplaceGroundMeshInFolder
    {
        public static void Replace(string folder)
        {
            if (!Directory.Exists(folder))
            {
                return;
            }

            //find prefabs
            List<string> prefabs = new List<string>();
            var enumerator = Directory.EnumerateFiles(folder, "*.prefab", SearchOption.TopDirectoryOnly);
            foreach (var e in enumerator)
            {
                var path = e.Replace('\\', '/');
                var unityAssetPath = Utils.ConvertToUnityAssetsPath(path);
                prefabs.Add(unityAssetPath);
            }

            //replace prefab mesh
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabs[i]);
                if (prefab != null)
                {
                    var filter = prefab.GetComponentInChildren<MeshFilter>();
                    if (filter != null)
                    {
                        string pathWithoutExt = Utils.RemoveExtension(prefabs[i]);
                        string objPath = pathWithoutExt + ".obj";
                        var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(objPath);
                        if (mesh == null)
                        {
                            mesh = AssetDatabase.LoadAssetAtPath<Mesh>(pathWithoutExt + ".fbx");
                        }
                        if (mesh != null)
                        {
                            filter.sharedMesh = mesh;
                            PrefabUtility.SavePrefabAsset(prefab);
                        }
                    }
                }
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }
    }
}

#endif