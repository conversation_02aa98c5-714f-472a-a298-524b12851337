{"skeleton": {"hash": "lEFXVbxIhy8", "spine": "4.2.33", "x": -479.87, "y": -1.1, "width": 882.7, "height": 1704.61, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 121.51, "y": 923.05}, {"name": "body", "parent": "ALL", "length": 139.88, "rotation": 114.93, "x": -2.88, "y": 20.09}, {"name": "body2", "parent": "body", "length": 116.16, "rotation": 110.25, "x": 139.88, "inherit": "noRotationOrReflection"}, {"name": "body3", "parent": "body2", "length": 268.37, "rotation": 93.48, "x": 116.16, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body3", "length": 88.75, "rotation": 100.38, "x": 268.37, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 166, "rotation": -0.43, "x": 88.75}, {"name": "sh_L", "parent": "body3", "x": 223.17, "y": -129.98, "inherit": "noScale"}, {"name": "sh_R", "parent": "body3", "x": 213.46, "y": 131.28, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 217.83, "rotation": -160.85, "x": -18.07, "y": 5}, {"name": "arm_L2", "parent": "arm_L", "length": 193.18, "rotation": 12.63, "x": 217.83, "inherit": "noScale"}, {"name": "hand_L", "parent": "ALL", "length": 44.77, "rotation": 78.25, "x": 247.45, "y": -28.6}, {"name": "hand_L2", "parent": "hand_L", "length": 42.72, "rotation": 32.57, "x": 44.77}, {"name": "hand_L3", "parent": "hand_L2", "length": 64.16, "rotation": 12.86, "x": 42.72}, {"name": "arm_R", "parent": "sh_R", "length": 253.28, "rotation": 164.79, "x": -12.95, "y": -13.13}, {"name": "arm_R2", "parent": "arm_R", "length": 220.13, "rotation": -143.5, "x": 253.28, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 120.92, "rotation": 109.04, "x": 220.13, "inherit": "onlyTranslation"}, {"name": "RU_L", "parent": "body3", "length": 30, "x": 101.03, "y": -8.17}, {"name": "RU_L2", "parent": "RU_L", "length": 30, "x": -9.56, "y": -1.34}, {"name": "RU_L3", "parent": "RU_L2", "length": 30, "x": -11.48, "y": -2.06}, {"name": "RU_R", "parent": "body3", "length": 30, "x": 101.54, "y": 113.75}, {"name": "RU_R2", "parent": "RU_R", "length": 30, "x": -15.9, "y": 41.8}, {"name": "RU_R3", "parent": "RU_R2", "length": 30, "x": -12.83, "y": 24.91}, {"name": "leg_L", "parent": "body", "x": 64.18, "y": -79.76}, {"name": "leg_R", "parent": "body", "x": 86.79, "y": 70.94}, {"name": "leg_L2", "parent": "ALL", "length": 422.88, "rotation": 161.53, "x": 55.16, "y": 25.5}, {"name": "leg_L3", "parent": "leg_L2", "length": 386.17, "rotation": -105.29, "x": 422.88, "inherit": "noRotationOrReflection"}, {"name": "leg_L4", "parent": "leg_L3", "length": 155.93, "rotation": -1.05, "x": 386.17}, {"name": "leg_L5", "parent": "leg_L4", "length": 98.28, "rotation": -55.52, "x": 155.93}, {"name": "leg_R2", "parent": "leg_R", "length": 284.17, "rotation": 63.13, "x": -86.71, "y": -20.09}, {"name": "leg_R3", "parent": "leg_R2", "length": 425.15, "rotation": 115.73, "x": 284.17}, {"name": "leg_R6", "parent": "root", "x": -40.08, "y": 542.41, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone", "parent": "leg_R6", "length": 100, "rotation": -69.47, "color": "ff3f00ff"}, {"name": "foot_R", "parent": "bone", "length": 85.23, "rotation": 130.76, "x": 201.08, "y": -63.22}, {"name": "foot_R2", "parent": "foot_R", "length": 145.46, "rotation": 49.66, "x": 85.23}, {"name": "wine", "parent": "arm_R3", "length": 103.03, "rotation": -51.44, "x": 69.49, "y": 8.52}, {"name": "wine2", "parent": "wine", "length": 40.46, "rotation": -0.15, "x": 110.7, "y": 1.46, "scaleX": 1.0407, "inherit": "noRotationOrReflection"}, {"name": "wine3", "parent": "wine2", "rotation": 69.09, "x": -0.26, "y": 0.47}, {"name": "head2", "parent": "head", "x": 64.45, "y": 15.81}, {"name": "head3", "parent": "head", "x": 59.38, "y": 69.6}, {"name": "head4", "parent": "head", "length": 23.88, "rotation": 63.48, "x": 77.58, "y": -10}, {"name": "head5", "parent": "head4", "length": 23.77, "rotation": 42.52, "x": 23.88}, {"name": "head6", "parent": "head5", "length": 14.9, "rotation": 2.89, "x": 23.77}, {"name": "head7", "parent": "head", "length": 6.32, "rotation": -48.88, "x": 75.98, "y": 89.55}, {"name": "head8", "parent": "head7", "length": 10.61, "rotation": -54.14, "x": 6.32}, {"name": "head9", "parent": "head8", "length": 8.92, "rotation": -1.8, "x": 10.61}, {"name": "hair_F", "parent": "head", "length": 53.41, "rotation": 176.17, "x": 113.16, "y": 96.19}, {"name": "hair_F2", "parent": "hair_F", "length": 53.16, "rotation": -3.56, "x": 53.41, "color": "abe323ff"}, {"name": "hair_F3", "parent": "hair_F2", "length": 41.26, "rotation": -3.87, "x": 53.16, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 43.47, "rotation": -12.57, "x": 41.26, "color": "abe323ff"}, {"name": "hair_B", "parent": "head", "length": 43.61, "rotation": 175.6, "x": 44.76, "y": -71.06}, {"name": "hair_B2", "parent": "hair_B", "length": 53.9, "rotation": 6.64, "x": 43.61, "color": "abe323ff"}, {"name": "headround3", "parent": "head", "x": 320.52, "y": 0.68, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -44.24, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 279.52, "y": -43.56, "icon": "warning"}, {"name": "bodyround", "parent": "body3", "x": 141.04, "y": -306.65, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body3", "x": 90.41, "y": -306.65, "icon": "warning"}, {"name": "sh_L2", "parent": "sh_L", "length": 228.99, "rotation": -172.5, "x": -18.06, "y": 5.01}, {"name": "sh_L3", "parent": "sh_L2", "length": 202.99, "rotation": 37.34, "x": 228.99, "color": "abe323ff"}, {"name": "arm_L3", "parent": "hand_L3", "rotation": -123.69, "x": 64.99, "y": -0.37, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "ALL", "x": 94.09, "y": 267.17, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "sh_R", "length": 282.99, "rotation": 167.07, "x": -12.5, "y": -12.75}, {"name": "sh_R3", "parent": "sh_R2", "length": 250.99, "rotation": -147.78, "x": 282.99, "color": "abe323ff"}, {"name": "arm_R4", "parent": "sh_R", "rotation": -93.48, "x": -52.25, "y": 133.26, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "sh_R", "rotation": -93.48, "x": -257.36, "y": 53.31, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "leg_R", "length": 329.22, "rotation": 59.81, "x": -86.7, "y": -20.08}, {"name": "leg_R5", "parent": "leg_R4", "length": 461.81, "rotation": 122.86, "x": 329.22, "color": "abe323ff"}, {"name": "leg_R1", "parent": "ALL", "x": -333.04, "y": 8.37, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "chair_b", "bone": "ALL", "attachment": "chair_b"}, {"name": "hair", "bone": "root", "attachment": "hair"}, {"name": "arm_Ra", "bone": "root", "attachment": "arm_Ra"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}, {"name": "wine", "bone": "root", "attachment": "wine"}, {"name": "light", "bone": "root", "attachment": "light"}, {"name": "chair_up", "bone": "ALL", "attachment": "chair_up"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "nose", "bone": "root", "attachment": "nose"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "chair_up2", "bone": "ALL", "attachment": "chair_up"}], "ik": [{"name": "arm_L", "order": 1, "bones": ["sh_L2", "sh_L3"], "target": "arm_L3"}, {"name": "arm_L1", "order": 3, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 4, "bones": ["arm_L2"], "target": "arm_L3", "compress": true, "stretch": true}, {"name": "arm_R", "order": 6, "bones": ["sh_R2", "sh_R3"], "target": "arm_R4", "bendPositive": false}, {"name": "arm_R1", "order": 8, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 9, "bones": ["arm_R2"], "target": "arm_R4", "compress": true, "stretch": true}, {"name": "leg_R", "order": 10, "bones": ["leg_R4", "leg_R5"], "target": "leg_R6"}, {"name": "leg_R1", "order": 12, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 13, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 2, "bones": ["arm_L1"], "target": "sh_L3", "rotation": 41.24, "x": 15.13, "y": 45.05, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 7, "bones": ["arm_R1"], "target": "sh_R3", "rotation": -113.1, "x": 30.94, "y": -6.46, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 14, "bones": ["bodyround2"], "target": "bodyround", "x": -50.64, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "bones": ["sh_L"], "target": "bodyround", "x": 82.12, "y": 176.67, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 5, "bones": ["sh_R"], "target": "bodyround", "x": 72.41, "y": 437.93, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 15, "bones": ["headround2"], "target": "headround", "x": -41, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 16, "bones": ["head4"], "target": "headround", "rotation": 63.48, "x": -242.94, "y": 33.56, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 17, "bones": ["head7"], "target": "headround", "rotation": -48.88, "x": -244.54, "y": 133.11, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_R3", "order": 11, "bones": ["leg_R1"], "target": "leg_R5", "rotation": 62.2, "x": 37.74, "y": 29.77, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}], "physics": [{"name": "hair_B2", "order": 18, "bone": "hair_B2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F2", "order": 19, "bone": "hair_F2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F3", "order": 20, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 21, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_Ra": {"arm_Ra": {"type": "mesh", "uvs": [0.71746, 0.00227, 1, 0.18214, 0.95627, 0.24245, 0.91719, 0.30262, 0.7645, 0.56377, 0.65931, 0.71367, 0.5753, 0.80561, 0.45551, 0.90569, 0.35997, 0.9676, 0.2874, 0.98882, 0.19488, 0.9978, 0.1194, 0.99359, 0.03935, 0.97168, 0.00986, 0.91946, 0.00979, 0.85329, 0.06839, 0.75427, 0.21302, 0.57257, 0.34517, 0.25639, 0.38198, 0.19122, 0.3787, 0.12295, 0.38443, 0.06641, 0.42465, 0.04151, 0.40631, 0.12538, 0.44499, 0.16706, 0.51038, 0.21108, 0.58964, 0.08137, 0.72126, 0.16661, 0.52373, 0.27889, 0.34874, 0.61528, 0.52338, 0.63672, 0.7459, 0.30315, 0.7628, 0.23286, 0.30141, 0.76866, 0.37332, 0.82909, 0.38949, 0.89623, 0.28217, 0.90021, 0.3085, 0.82591], "triangles": [24, 23, 26, 27, 24, 31, 3, 31, 2, 2, 31, 1, 1, 26, 0, 31, 26, 1, 24, 26, 31, 13, 14, 35, 12, 13, 35, 35, 11, 12, 30, 27, 31, 17, 18, 24, 17, 24, 27, 16, 17, 27, 27, 28, 16, 27, 30, 28, 28, 30, 29, 30, 4, 29, 5, 29, 4, 15, 16, 28, 32, 15, 28, 32, 28, 29, 32, 29, 5, 6, 32, 5, 33, 36, 32, 6, 33, 32, 7, 34, 33, 36, 15, 32, 35, 14, 36, 34, 35, 36, 34, 36, 33, 6, 7, 33, 8, 35, 34, 8, 34, 7, 8, 9, 35, 9, 10, 35, 4, 30, 3, 30, 31, 3, 15, 36, 14, 35, 10, 11, 18, 23, 24, 23, 18, 19, 25, 0, 26, 23, 22, 25, 25, 21, 0, 25, 22, 20, 25, 20, 21, 19, 20, 22, 23, 19, 22, 23, 25, 26], "vertices": [2, 8, 23, -29.66, 0.57714, 4, 236.46, 101.62, 0.42286, 1, 4, 179.49, 68.57, 1, 2, 14, 26.44, 51.58, 0.22857, 4, 161.47, 75.32, 0.77143, 3, 8, -70, -49.82, 0.275, 14, 45.43, 50.37, 0.43714, 4, 143.45, 81.47, 0.28786, 1, 14, 127.42, 47.27, 1, 1, 14, 174.94, 43.27, 1, 1, 14, 204.6, 38.36, 1, 1, 14, 237.63, 29.44, 1, 2, 14, 258.63, 21.2, 0.89712, 15, -16.91, -13.87, 0.10288, 2, 14, 266.86, 13.35, 0.64133, 15, -18.86, -2.66, 0.35867, 2, 14, 271.97, 2.22, 0.40862, 15, -16.34, 9.33, 0.59138, 2, 14, 272.69, -7.57, 0.20841, 15, -11.1, 17.63, 0.79159, 2, 14, 268.25, -19.04, 0.06571, 15, -0.7, 24.21, 0.93429, 1, 15, 15.35, 20.99, 1, 1, 15, 33.68, 12.54, 1, 2, 14, 202.56, -28.85, 0.92024, 15, 57.93, -6.99, 0.07976, 1, 14, 144.51, -21.84, 1, 2, 8, -51.45, 22.98, 0.0084, 14, 46.62, -24.75, 0.9916, 2, 8, -31.89, 17.03, 0.14416, 14, 26.19, -24.14, 0.85584, 2, 8, -11.08, 16.19, 0.69159, 14, 5.89, -28.78, 0.30841, 2, 8, 6.08, 14.41, 0.99781, 14, -11.14, -31.56, 0.00219, 1, 8, 13.35, 8.77, 1, 3, 8, -12.04, 12.68, 0.87135, 4, 201.42, 143.96, 0.12205, 55, 60.37, 450.62, 0.00661, 3, 8, -25.03, 8.47, 0.7103, 4, 188.43, 139.76, 0.2792, 55, 47.38, 446.41, 0.0105, 3, 8, -38.95, 0.87, 0.38721, 4, 174.51, 132.15, 0.60202, 55, 33.47, 438.8, 0.01077, 3, 8, -0.08, -11.74, 0.80513, 4, 213.38, 119.54, 0.18017, 55, 72.33, 426.19, 0.0147, 3, 8, -27.06, -27.11, 0.28275, 4, 186.4, 104.17, 0.70687, 55, 45.35, 410.83, 0.01038, 3, 8, -59.7, 0.41, 0.1118, 14, 48.66, -0.8, 0.86394, 55, 12.72, 438.34, 0.02426, 2, 14, 153.7, -2.05, 0.9952, 55, -88.32, 467.1, 0.0048, 2, 14, 155.53, 21.34, 0.99499, 55, -96.22, 445.02, 0.00501, 4, 8, -68.82, -27.75, 0.33759, 14, 50.08, 28.77, 0.54685, 4, 144.64, 103.53, 0.09207, 55, 3.59, 410.18, 0.02349, 4, 8, -47.56, -31.23, 0.09469, 14, 28.65, 26.55, 0.21738, 4, 165.9, 100.05, 0.67603, 55, 24.86, 406.7, 0.0119, 2, 14, 200.75, 1.48, 0.91429, 15, 41.35, -32.44, 0.08571, 2, 14, 216.91, 14.31, 0.97143, 15, 20.73, -33.14, 0.02857, 1, 14, 236.54, 20.51, 1, 1, 14, 240.54, 7.2, 1, 2, 14, 217.66, 5.92, 0.96857, 15, 25.11, -25.95, 0.03143], "hull": 22, "edges": [0, 42, 0, 2, 8, 10, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 36, 38, 38, 40, 2, 4, 4, 6, 6, 8, 14, 16, 10, 12, 12, 14, 54, 56, 58, 60, 18, 20, 20, 22], "width": 129, "height": 305}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.5439, 0, 0.68424, 0.02272, 0.81407, 0.06636, 0.86996, 0.09584, 0.79196, 0.18086, 0.58572, 0.29605, 0.55892, 0.33564, 0.5615, 0.36349, 0.5403, 0.40736, 0.53263, 0.44894, 0.54705, 0.47335, 0.56388, 0.49564, 0.6082, 0.49777, 0.74158, 0.63412, 0.8137, 0.69994, 0.95214, 0.79725, 0.9916, 0.83604, 0.99394, 0.87418, 0.97399, 0.92799, 0.97517, 0.99283, 0.91175, 0.99866, 0.85954, 0.9945, 0.81334, 0.98009, 0.74257, 0.94273, 0.6298, 0.88319, 0.55758, 0.82839, 0.5026, 0.76142, 0.41238, 0.68184, 0.27993, 0.58826, 0.35974, 0.54412, 0.33685, 0.52015, 0.30514, 0.49723, 0.26207, 0.49387, 0.16899, 0.47371, 0.04572, 0.43567, 1e-05, 0.41006, 0.09633, 0.32797, 0.21248, 0.23406, 0.28678, 0.23068, 0.21974, 0.1644, 0.34805, 0.09489, 0.51399, 0, 0.90172, 0.95048, 0.86237, 0.90122, 0.79691, 0.83743, 0.6875, 0.74732], "triangles": [34, 36, 33, 34, 35, 36, 6, 38, 5, 5, 38, 4, 4, 40, 1, 40, 4, 38, 1, 40, 0, 40, 41, 0, 38, 39, 40, 4, 2, 3, 4, 1, 2, 30, 31, 10, 10, 31, 9, 9, 31, 8, 8, 31, 32, 8, 32, 33, 37, 8, 36, 8, 33, 36, 7, 8, 37, 7, 38, 6, 38, 7, 37, 43, 16, 17, 19, 20, 42, 20, 21, 42, 21, 22, 42, 22, 23, 42, 23, 43, 42, 42, 43, 18, 23, 24, 43, 24, 44, 43, 43, 44, 16, 24, 25, 44, 25, 45, 44, 44, 45, 15, 25, 26, 45, 45, 14, 15, 26, 27, 45, 45, 13, 14, 45, 27, 13, 13, 11, 12, 11, 13, 29, 28, 29, 27, 13, 27, 29, 29, 30, 11, 30, 10, 11, 44, 15, 16, 42, 18, 19, 18, 43, 17], "vertices": [2, 16, 194.74, -87.71, 0.00012, 35, 153.32, 37.95, 0.99988, 1, 35, 159.39, 10.06, 1, 1, 35, 156.77, -20.98, 1, 1, 35, 151.73, -36.82, 1, 1, 35, 112.74, -43.92, 1, 2, 16, 71.23, -53.54, 0.13615, 35, 49.62, -37.33, 0.86385, 2, 16, 56.74, -43.12, 0.51715, 35, 32.44, -42.17, 0.48285, 2, 16, 45.2, -39.66, 0.7643, 35, 22.54, -49.03, 0.2357, 3, 15, 245.63, -32.37, 0.00051, 16, 28.61, -29.65, 0.96546, 35, 4.37, -55.77, 0.03403, 3, 15, 229.94, -23.51, 0.12651, 16, 12.11, -22.41, 0.87327, 35, -11.58, -64.16, 0.00022, 2, 15, 219.21, -21.59, 0.50504, 16, 1.24, -21.57, 0.49496, 2, 15, 209.12, -20.47, 0.89489, 16, -8.91, -21.47, 0.10511, 2, 15, 204.73, -27.77, 0.98256, 16, -12.54, -29.17, 0.01744, 1, 15, 140.58, -26.22, 1, 2, 14, 149.72, -43.29, 0.00779, 15, 109, -26.81, 0.99221, 2, 14, 185.5, -8.86, 0.2616, 15, 59.75, -33.2, 0.7384, 2, 14, 200.38, 1.93, 0.52439, 15, 41.38, -33.02, 0.47561, 2, 14, 216.42, 5.71, 0.86286, 15, 26.23, -26.52, 0.13714, 1, 14, 239.95, 6.7, 1, 2, 14, 267.34, 12.62, 0.69184, 15, -18.8, -1.78, 0.30816, 2, 14, 272.26, 1.27, 0.4408, 15, -16.01, 10.27, 0.5592, 2, 14, 272.53, -8.86, 0.19044, 15, -10.2, 18.57, 0.80956, 2, 14, 268.23, -18.77, 0.03202, 15, -0.85, 23.97, 0.96798, 1, 15, 19.47, 29.48, 1, 1, 15, 51.85, 38.26, 1, 1, 15, 79.12, 40.86, 1, 1, 15, 109.79, 38.27, 1, 1, 15, 148.22, 39.51, 1, 1, 15, 195.52, 45.54, 1, 3, 15, 206.44, 23.71, 0.93944, 16, -15.99, 22.22, 0.05821, 35, -63.99, -58.31, 0.00235, 3, 15, 217.68, 23.33, 0.59775, 16, -4.77, 22.97, 0.37868, 35, -57.59, -49.07, 0.02358, 3, 15, 229.2, 24.69, 0.16204, 16, 6.56, 25.47, 0.73005, 35, -52.48, -38.65, 0.1079, 3, 15, 233.97, 31.55, 0.04174, 16, 10.61, 32.77, 0.74192, 35, -55.66, -30.93, 0.21635, 2, 16, 24.65, 46.74, 0.49639, 35, -57.84, -11.25, 0.50361, 2, 16, 47.86, 63.63, 0.09077, 35, -56.58, 17.44, 0.90923, 2, 16, 61.17, 68.28, 0.01821, 35, -51.91, 30.74, 0.98179, 2, 16, 88.69, 39.32, 0.18387, 35, -12.11, 34.21, 0.81613, 1, 16, 119.8, 5.11, 1, 2, 16, 116.55, -8.78, 0.65269, 35, 42.86, 26.01, 0.34731, 2, 16, 147.8, -6.02, 0.23829, 35, 60.18, 52.17, 0.76171, 2, 16, 168.19, -38.98, 0.06126, 35, 98.66, 47.57, 0.93874, 2, 16, 196.6, -82.31, 0.00028, 35, 150.26, 42.77, 0.99972, 1, 15, 3.69, 3.29, 1, 1, 15, 26.16, 1.19, 1, 1, 15, 56.42, 1, 1, 1, 15, 100.51, 3.66, 1], "hull": 42, "edges": [0, 82, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 50, 52, 52, 54, 54, 56, 56, 58, 66, 68, 68, 70, 70, 72, 78, 80, 80, 82, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 72, 74, 74, 76, 76, 78, 62, 64, 64, 66, 58, 60, 60, 62, 32, 34, 34, 36, 40, 42, 42, 44, 48, 50, 44, 46, 46, 48], "width": 191, "height": 432}}, "body": {"body": {"type": "mesh", "uvs": [0.43592, 0.0271, 0.43489, 0.04782, 0.43898, 0.06608, 0.44583, 0.07856, 0.48761, 0.10438, 0.52031, 0.11357, 0.56756, 0.12119, 0.62089, 0.12877, 0.63004, 0.13225, 0.64093, 0.14392, 0.64703, 0.15641, 0.64802, 0.16342, 0.66149, 0.17784, 0.67269, 0.19511, 0.69065, 0.21873, 0.69373, 0.23012, 0.74849, 0.28935, 0.76144, 0.29562, 0.77859, 0.30103, 0.78047, 0.31193, 0.79205, 0.31746, 0.81294, 0.33561, 0.83663, 0.35616, 0.86832, 0.38169, 0.90327, 0.4063, 0.92315, 0.41869, 0.94304, 0.43107, 0.92745, 0.43775, 0.93622, 0.44418, 0.94818, 0.45055, 0.96571, 0.45909, 0.98811, 0.46762, 0.9927, 0.47827, 1, 0.48665, 0.99784, 0.49716, 0.98882, 0.50921, 0.98443, 0.52178, 0.97581, 0.53802, 0.96138, 0.55902, 0.94239, 0.55546, 0.94163, 0.53797, 0.95286, 0.51784, 0.92895, 0.50158, 0.90799, 0.49416, 0.89663, 0.50113, 0.89716, 0.50749, 0.9097, 0.52554, 0.90172, 0.54623, 0.89193, 0.54597, 0.87362, 0.52112, 0.86712, 0.50826, 0.85844, 0.4971, 0.85468, 0.48699, 0.85578, 0.46652, 0.85802, 0.45798, 0.85947, 0.45081, 0.84134, 0.45823, 0.83245, 0.4564, 0.82163, 0.44327, 0.8108, 0.43014, 0.7863, 0.41351, 0.74117, 0.38751, 0.71548, 0.36952, 0.69073, 0.34815, 0.67916, 0.33649, 0.67122, 0.32455, 0.65567, 0.31073, 0.62533, 0.28377, 0.60592, 0.26643, 0.59399, 0.25417, 0.58296, 0.26827, 0.56452, 0.29787, 0.55258, 0.32246, 0.55288, 0.33723, 0.5512, 0.3478, 0.56414, 0.35556, 0.5753, 0.36702, 0.59339, 0.37605, 0.63414, 0.38943, 0.68062, 0.40838, 0.7232, 0.43216, 0.75739, 0.46165, 0.77315, 0.48905, 0.77415, 0.51575, 0.76525, 0.54082, 0.74804, 0.56091, 0.72669, 0.57551, 0.65644, 0.59645, 0.58038, 0.61458, 0.43658, 0.61414, 0.35695, 0.60934, 0.2962, 0.60061, 0.23483, 0.58654, 0.20282, 0.57749, 0.21901, 0.59019, 0.23741, 0.60735, 0.2492, 0.62524, 0.26107, 0.68049, 0.28756, 0.72667, 0.3262, 0.7786, 0.3428, 0.79568, 0.35857, 0.80893, 0.3826, 0.8189, 0.39291, 0.81711, 0.46646, 0.82974, 0.48201, 0.85969, 0.50539, 0.88984, 0.55219, 0.92966, 0.53779, 0.93527, 0.46301, 0.88605, 0.45264, 0.89424, 0.45694, 0.91462, 0.47401, 0.9383, 0.4695, 0.95016, 0.45586, 0.96367, 0.42809, 0.97798, 0.38105, 0.9983, 0.32476, 1, 0.31916, 0.99965, 0.30035, 0.98385, 0.30523, 0.9711, 0.3282, 0.95378, 0.3506, 0.94117, 0.34843, 0.93221, 0.3428, 0.92082, 0.33168, 0.90375, 0.30739, 0.8647, 0.28737, 0.84277, 0.2741, 0.82283, 0.24903, 0.79923, 0.18379, 0.74621, 0.09888, 0.6772, 0.05883, 0.63602, 0.03779, 0.60042, 0.0175, 0.56791, 0.00366, 0.54526, 0, 0.52263, 0.01185, 0.50207, 0.03981, 0.48624, 0.07692, 0.48178, 0.1681, 0.47778, 0.23621, 0.4767, 0.30212, 0.47677, 0.3586, 0.4774, 0.36038, 0.45642, 0.36402, 0.43455, 0.36974, 0.41088, 0.37127, 0.39407, 0.36463, 0.38763, 0.35307, 0.38032, 0.3436, 0.37236, 0.31662, 0.35767, 0.29452, 0.34322, 0.2712, 0.32435, 0.23702, 0.31831, 0.19986, 0.31827, 0.1613, 0.31289, 0.1311, 0.29973, 0.11416, 0.27867, 0.11677, 0.25448, 0.14602, 0.22977, 0.17959, 0.21245, 0.20261, 0.18982, 0.18973, 0.17797, 0.18174, 0.16303, 0.18376, 0.15291, 0.21351, 0.14282, 0.25466, 0.13515, 0.28754, 0.12808, 0.3111, 0.12036, 0.33968, 0.10263, 0.33448, 0.09053, 0.31305, 0.06419, 0.29807, 0.03901, 0.29322, 0.01708, 0.32577, 0.01335, 0.39449, 0.00548, 0.43987, 0.00028, 0.29363, 0.28568, 0.3424, 0.30203, 0.43248, 0.31165, 0.49016, 0.30341, 0.53207, 0.2805, 0.55312, 0.24074, 0.54658, 0.20565, 0.50675, 0.17814, 0.42612, 0.17295, 0.35932, 0.18652, 0.31597, 0.20451, 0.30003, 0.18655, 0.26354, 0.18393, 0.21664, 0.19591, 0.27057, 0.30054, 0.23635, 0.31188, 0.5689, 0.23012, 0.27967, 0.25573, 0.28863, 0.22689, 0.43035, 0.26409, 0.13267, 0.2764, 0.14496, 0.2435, 0.17423, 0.24991, 0.18421, 0.2911, 0.16464, 0.30274, 0.19073, 0.21513, 0.22936, 0.21767, 0.25445, 0.2431, 0.25507, 0.2749, 0.2019, 0.30844, 0.23703, 0.29586, 0.18767, 0.26913, 0.40472, 0.23009, 0.46102, 0.22993, 0.4896, 0.2474, 0.4883, 0.27447, 0.45967, 0.28854, 0.41006, 0.28958, 0.37742, 0.27536, 0.37338, 0.24952, 0.41315, 0.19961, 0.48133, 0.20175, 0.52303, 0.23199, 0.51915, 0.268, 0.48725, 0.29421, 0.43042, 0.30274, 0.3724, 0.29522, 0.33326, 0.27681, 0.32875, 0.24384, 0.36022, 0.21419, 0.30733, 0.31204, 0.32972, 0.33193, 0.35291, 0.34977, 0.37517, 0.36913, 0.4008, 0.38427, 0.4143, 0.40222, 0.41429, 0.42945, 0.41632, 0.45655, 0.41969, 0.48366, 0.42644, 0.33451, 0.4406, 0.35493, 0.45477, 0.37535, 0.46489, 0.3919, 0.48397, 0.41769, 0.50218, 0.44374, 0.51972, 0.46873, 0.49678, 0.32229, 0.50758, 0.34658, 0.52039, 0.36488, 0.53253, 0.37967, 0.55345, 0.40255, 0.57503, 0.4279, 0.60085, 0.45862, 0.70587, 0.4706, 0.67665, 0.44365, 0.63837, 0.4169, 0.6003, 0.39363, 0.56006, 0.37547, 0.71388, 0.49668, 0.67151, 0.47602, 0.15916, 0.55134, 0.08611, 0.52155, 0.11661, 0.5114, 0.17156, 0.51609, 0.07266, 0.53903, 0.09164, 0.5651, 0.12061, 0.59065, 0.23933, 0.52081, 0.13716, 0.6133, 0.16963, 0.64802, 0.32239, 0.53209, 0.42004, 0.53792, 0.53252, 0.54123, 0.63688, 0.532, 0.68915, 0.52002, 0.22607, 0.16089, 0.29555, 0.1529, 0.34375, 0.14813, 0.38275, 0.14385, 0.49497, 0.13734, 0.44117, 0.13967, 0.53621, 0.14132, 0.59919, 0.13308, 0.57431, 0.14845, 0.55476, 0.17669, 0.39505, 0.12005, 0.40688, 0.09072, 0.40786, 0.07194, 0.40293, 0.05187, 0.39798, 0.03156, 0.35953, 0.12387, 0.35657, 0.10149, 0.35164, 0.08322, 0.33981, 0.05904, 0.33094, 0.03717, 0.34228, 0.11764, 0.45307, 0.11429, 0.25903, 0.15499, 0.61342, 0.14319, 0.62399, 0.16012, 0.63667, 0.1814, 0.59912, 0.15908, 0.58459, 0.19171, 0.60185, 0.21802, 0.61894, 0.23937, 0.68179, 0.29891, 0.71651, 0.2894, 0.66409, 0.22899, 0.65032, 0.20542, 0.69145, 0.31165, 0.70329, 0.32465, 0.71636, 0.33732, 0.73567, 0.29897, 0.74875, 0.31165, 0.76058, 0.32367, 0.95822, 0.47918, 0.93695, 0.46861, 0.89672, 0.47848, 0.90723, 0.45514, 0.89972, 0.44762, 0.89282, 0.43837, 0.9719, 0.49244, 0.85815, 0.44355, 0.88657, 0.42842, 0.91612, 0.42872, 0.85792, 0.42296], "triangles": [113, 122, 112, 114, 122, 113, 115, 122, 114, 115, 121, 122, 116, 121, 115, 120, 121, 116, 117, 118, 119, 119, 120, 117, 116, 117, 120, 102, 126, 127, 102, 127, 101, 103, 105, 102, 105, 103, 104, 109, 105, 106, 105, 109, 102, 110, 125, 126, 109, 126, 102, 109, 110, 126, 124, 125, 110, 111, 124, 110, 109, 106, 108, 123, 124, 111, 107, 108, 106, 122, 123, 111, 122, 111, 112, 136, 137, 259, 262, 136, 259, 135, 136, 262, 262, 259, 258, 263, 262, 258, 134, 135, 262, 134, 262, 263, 264, 263, 258, 264, 258, 93, 133, 134, 263, 133, 263, 264, 266, 264, 93, 266, 93, 94, 266, 132, 133, 266, 133, 264, 95, 267, 266, 95, 266, 94, 267, 95, 96, 132, 266, 267, 131, 132, 267, 267, 96, 97, 97, 131, 267, 130, 97, 98, 130, 131, 97, 129, 130, 98, 129, 98, 99, 129, 99, 100, 128, 129, 100, 128, 100, 101, 127, 128, 101, 260, 139, 140, 261, 140, 141, 260, 140, 261, 265, 141, 142, 261, 141, 265, 259, 139, 260, 138, 139, 259, 137, 138, 259, 268, 142, 143, 265, 142, 268, 270, 269, 236, 258, 260, 261, 259, 260, 258, 265, 258, 261, 93, 258, 265, 92, 93, 265, 92, 265, 268, 91, 92, 268, 269, 90, 268, 91, 268, 90, 89, 269, 270, 88, 89, 270, 269, 143, 236, 269, 268, 143, 90, 269, 89, 172, 173, 292, 292, 287, 286, 0, 176, 177, 287, 176, 0, 175, 176, 287, 292, 175, 287, 174, 175, 292, 173, 174, 292, 1, 287, 0, 285, 286, 2, 291, 286, 285, 284, 285, 3, 284, 3, 294, 284, 290, 285, 172, 291, 290, 170, 171, 289, 171, 172, 290, 171, 290, 289, 172, 292, 291, 289, 290, 284, 283, 289, 284, 290, 291, 285, 291, 292, 286, 285, 2, 3, 286, 287, 1, 286, 1, 2, 36, 41, 35, 37, 41, 36, 40, 41, 37, 39, 40, 37, 38, 39, 37, 48, 49, 46, 313, 31, 32, 319, 313, 32, 319, 32, 33, 42, 43, 313, 34, 319, 33, 319, 42, 313, 35, 319, 34, 41, 42, 319, 41, 319, 35, 47, 48, 46, 49, 45, 46, 43, 44, 315, 49, 50, 45, 50, 44, 45, 50, 51, 44, 27, 322, 26, 317, 318, 27, 55, 318, 317, 28, 316, 317, 28, 317, 27, 29, 316, 28, 314, 29, 30, 314, 316, 29, 54, 317, 316, 317, 54, 55, 315, 316, 314, 316, 53, 54, 315, 53, 316, 313, 314, 30, 313, 30, 31, 43, 315, 314, 43, 314, 313, 52, 53, 315, 315, 44, 51, 51, 52, 315, 311, 17, 18, 311, 18, 19, 312, 311, 19, 312, 19, 20, 308, 307, 311, 308, 311, 312, 64, 65, 308, 309, 308, 312, 64, 308, 309, 63, 64, 309, 62, 63, 309, 312, 62, 309, 22, 61, 62, 312, 20, 21, 22, 62, 21, 21, 62, 312, 60, 22, 23, 323, 60, 23, 60, 61, 22, 24, 323, 23, 321, 323, 24, 321, 24, 25, 322, 321, 25, 59, 60, 323, 322, 25, 26, 318, 321, 322, 318, 322, 27, 58, 59, 323, 320, 323, 321, 320, 321, 318, 58, 323, 320, 55, 320, 318, 57, 58, 320, 57, 320, 55, 55, 56, 57, 194, 301, 302, 69, 194, 302, 300, 282, 299, 297, 9, 10, 299, 296, 297, 297, 10, 11, 298, 297, 11, 298, 11, 12, 298, 300, 299, 298, 299, 297, 13, 306, 298, 13, 298, 12, 300, 298, 306, 301, 300, 306, 306, 13, 14, 305, 306, 14, 301, 306, 305, 305, 14, 15, 302, 301, 305, 68, 69, 302, 68, 302, 67, 302, 303, 67, 303, 302, 305, 15, 304, 305, 16, 304, 15, 304, 303, 305, 310, 304, 16, 310, 16, 17, 66, 67, 303, 311, 310, 17, 307, 303, 304, 307, 304, 310, 307, 310, 311, 66, 303, 307, 65, 66, 307, 65, 307, 308, 184, 300, 301, 165, 166, 273, 164, 165, 273, 163, 164, 273, 273, 295, 190, 162, 163, 273, 166, 167, 295, 273, 166, 295, 190, 162, 273, 282, 279, 281, 280, 6, 7, 279, 5, 6, 296, 280, 7, 296, 7, 8, 296, 8, 9, 281, 6, 280, 281, 280, 296, 279, 6, 281, 297, 296, 9, 299, 281, 296, 282, 281, 299, 191, 162, 190, 160, 161, 203, 200, 203, 204, 190, 295, 274, 167, 168, 274, 204, 190, 189, 293, 170, 289, 194, 183, 184, 184, 282, 300, 184, 185, 282, 277, 4, 5, 294, 4, 277, 278, 283, 294, 278, 294, 277, 277, 5, 279, 276, 283, 278, 275, 288, 276, 186, 276, 278, 185, 277, 279, 278, 277, 185, 186, 278, 185, 187, 275, 276, 187, 276, 186, 189, 274, 275, 189, 275, 187, 218, 187, 186, 219, 186, 185, 218, 186, 219, 188, 189, 187, 219, 185, 184, 227, 187, 218, 188, 187, 227, 196, 204, 189, 196, 189, 188, 211, 218, 219, 210, 227, 218, 210, 218, 211, 220, 219, 184, 211, 219, 220, 205, 204, 196, 227, 196, 188, 217, 226, 227, 226, 196, 227, 212, 211, 220, 210, 217, 227, 200, 199, 203, 199, 159, 160, 195, 205, 196, 195, 196, 226, 197, 210, 211, 197, 211, 212, 217, 210, 197, 220, 183, 221, 220, 221, 212, 182, 221, 183, 205, 209, 200, 205, 200, 204, 213, 197, 212, 213, 212, 221, 206, 205, 195, 209, 205, 206, 216, 217, 197, 198, 159, 199, 198, 199, 200, 198, 200, 209, 225, 226, 217, 225, 217, 216, 195, 226, 225, 178, 206, 195, 158, 159, 198, 70, 182, 183, 225, 178, 195, 214, 197, 213, 215, 216, 197, 215, 197, 214, 201, 198, 209, 208, 201, 209, 222, 214, 213, 222, 213, 221, 182, 222, 221, 224, 225, 216, 224, 216, 215, 206, 208, 209, 192, 208, 206, 71, 182, 70, 157, 158, 198, 202, 157, 198, 178, 192, 206, 179, 225, 224, 178, 225, 179, 201, 202, 198, 223, 215, 214, 223, 214, 222, 224, 215, 223, 181, 222, 182, 181, 182, 71, 223, 222, 181, 207, 201, 208, 202, 201, 207, 180, 223, 181, 224, 223, 180, 179, 224, 180, 193, 207, 208, 193, 208, 192, 228, 178, 179, 192, 178, 228, 156, 157, 202, 156, 202, 207, 155, 156, 207, 155, 207, 193, 153, 154, 193, 155, 193, 154, 180, 181, 244, 71, 72, 181, 153, 192, 228, 153, 193, 192, 229, 228, 179, 153, 228, 229, 237, 179, 180, 152, 153, 229, 151, 152, 229, 275, 293, 288, 288, 293, 289, 169, 170, 293, 169, 293, 275, 168, 169, 275, 274, 168, 275, 283, 288, 289, 283, 284, 294, 276, 288, 283, 185, 279, 282, 183, 220, 184, 194, 184, 301, 183, 194, 69, 70, 183, 69, 294, 3, 4, 190, 274, 189, 295, 167, 274, 204, 203, 190, 199, 160, 203, 203, 191, 190, 203, 161, 191, 161, 162, 191, 72, 244, 181, 237, 180, 244, 229, 179, 237, 73, 245, 244, 73, 244, 72, 238, 237, 244, 74, 245, 73, 230, 229, 237, 245, 238, 244, 231, 230, 237, 151, 229, 230, 246, 245, 74, 246, 74, 75, 238, 231, 237, 150, 151, 230, 150, 230, 231, 239, 238, 245, 239, 245, 246, 232, 231, 238, 255, 246, 75, 255, 75, 76, 255, 76, 77, 247, 246, 255, 149, 150, 231, 239, 232, 238, 148, 149, 231, 232, 148, 231, 240, 239, 246, 240, 246, 247, 147, 148, 232, 233, 232, 239, 233, 239, 240, 147, 232, 233, 248, 247, 255, 254, 248, 255, 254, 255, 77, 146, 147, 233, 241, 240, 247, 233, 240, 241, 234, 146, 233, 234, 233, 241, 145, 146, 234, 242, 235, 234, 145, 234, 235, 144, 145, 235, 243, 236, 235, 143, 144, 235, 235, 236, 143, 254, 77, 78, 253, 78, 79, 254, 78, 253, 249, 248, 254, 249, 254, 253, 252, 253, 79, 252, 79, 80, 250, 249, 253, 250, 253, 252, 251, 252, 80, 251, 80, 81, 257, 250, 252, 257, 252, 251, 241, 247, 248, 249, 242, 241, 249, 241, 248, 242, 234, 241, 243, 242, 249, 243, 249, 250, 243, 235, 242, 271, 243, 250, 270, 236, 243, 251, 81, 82, 256, 251, 82, 257, 251, 256, 256, 82, 83, 272, 257, 256, 272, 256, 83, 271, 250, 257, 271, 257, 272, 84, 272, 83, 271, 270, 243, 85, 272, 84, 86, 272, 85, 271, 272, 86, 87, 271, 86, 270, 271, 87, 87, 88, 270], "vertices": [2, 5, 98.58, -42.37, 0.24346, 6, 10.16, -42.3, 0.75654, 2, 5, 73.09, -37.02, 0.75243, 6, -15.38, -37.14, 0.24757, 3, 4, 322.3, -29.25, 0.00565, 5, 50.03, -35.52, 0.9828, 6, -38.45, -35.81, 0.01154, 2, 4, 306.37, -32.79, 0.12205, 5, 33.79, -37.12, 0.87795, 3, 4, 272.3, -58.17, 0.76577, 7, 49.14, 71.81, 0.09505, 5, -3.08, -58.22, 0.13918, 2, 4, 259.47, -78.88, 0.62114, 7, 36.3, 51.1, 0.37886, 2, 4, 248.03, -109.24, 0.2699, 7, 24.86, 20.74, 0.7301, 1, 7, 13.22, -13.6, 1, 2, 7, 8.5, -19.33, 0.96772, 9, -17.12, 31.7, 0.03228, 2, 7, -6.59, -25.56, 0.68619, 9, -0.82, 32.64, 0.31381, 2, 7, -22.5, -28.61, 0.29763, 9, 15.21, 30.3, 0.70237, 2, 7, -31.33, -28.72, 0.15465, 9, 23.59, 27.51, 0.84535, 2, 7, -49.96, -36.44, 0.02508, 9, 43.72, 28.68, 0.97492, 2, 7, -72.08, -42.45, 0.00066, 9, 66.58, 27.11, 0.99934, 1, 9, 98.52, 26.56, 1, 1, 9, 112.51, 22.91, 1, 2, 9, 195.05, 27.42, 0.87008, 10, -16.23, 31.74, 0.12992, 2, 9, 205.61, 32.22, 0.60623, 10, -4.88, 34.12, 0.39377, 2, 9, 216.2, 39.99, 0.38244, 10, 7.15, 39.38, 0.61756, 2, 9, 229.32, 35.86, 0.16672, 10, 19.05, 32.48, 0.83328, 2, 9, 238.66, 40.19, 0.04462, 10, 29.11, 34.66, 0.95538, 1, 10, 55.66, 32.67, 1, 1, 10, 85.72, 30.45, 1, 1, 10, 123.92, 28.89, 1, 2, 10, 162.42, 29.75, 0.98706, 13, 96.56, -29.26, 0.01294, 2, 10, 182.66, 31.41, 0.77364, 13, 76.37, -31.48, 0.22636, 2, 10, 202.9, 33.07, 0.52675, 13, 56.18, -33.7, 0.47325, 2, 10, 203.85, 19.87, 0.32457, 13, 54.86, -20.53, 0.67543, 2, 10, 213.76, 19.9, 0.05917, 13, 44.95, -20.84, 0.94083, 3, 10, 224.84, 21.68, 0.00387, 13, 33.93, -22.92, 0.99286, 12, 80.91, -14.79, 0.00327, 2, 13, 18.63, -26.54, 0.87714, 12, 66.79, -21.72, 0.12286, 2, 13, 1.55, -32.81, 0.55991, 12, 51.54, -31.64, 0.44009, 2, 13, -11.25, -27.9, 0.2988, 12, 37.96, -29.7, 0.7012, 2, 13, -22.68, -26.03, 0.1071, 12, 26.4, -30.43, 0.8929, 3, 13, -32.88, -17.53, 0.00768, 12, 14.57, -24.41, 0.98794, 11, 70.19, -12.72, 0.00437, 2, 12, 2.52, -13.49, 0.80091, 11, 54.16, -10.01, 0.19909, 2, 12, -11.23, -5.18, 0.15629, 11, 38.1, -10.41, 0.84371, 1, 11, 16.96, -9.04, 1, 1, 11, -10.81, -5.14, 1, 2, 13, -73.68, 53.39, 0, 11, -8.97, 7.97, 1, 2, 13, -55.12, 41.61, 1e-05, 11, 12.45, 12.93, 0.99999, 3, 13, -38.14, 21.44, 0.00223, 12, 0.77, 12.41, 0.40099, 11, 38.73, 10.87, 0.59678, 3, 13, -12.44, 23.15, 0.18916, 12, 25.44, 19.81, 0.80676, 11, 55.55, 30.39, 0.00408, 3, 13, 2.95, 29.42, 0.40794, 12, 39.05, 29.34, 0.42476, 11, 61.89, 45.75, 0.1673, 3, 13, -0.21, 40.48, 0.46504, 12, 33.51, 39.42, 0.32427, 11, 51.79, 51.26, 0.21069, 3, 13, -7.05, 44.63, 0.43631, 12, 25.91, 41.94, 0.31297, 11, 44.03, 49.3, 0.25072, 3, 13, -30.49, 50.37, 0.25888, 12, 1.78, 42.32, 0.37659, 11, 23.49, 36.62, 0.36453, 3, 13, -49.22, 69.15, 0.01095, 12, -20.66, 56.46, 0.49453, 11, -3.03, 36.46, 0.49452, 3, 13, -45.39, 74.31, 0.01806, 12, -18.07, 62.34, 0.49097, 11, -4.02, 42.8, 0.49097, 3, 13, -12.74, 66.98, 0.30807, 12, 15.39, 62.46, 0.35825, 11, 24.11, 60.92, 0.33367, 3, 13, 3.07, 61.57, 0.45011, 12, 32.01, 60.71, 0.29928, 11, 39.07, 68.39, 0.25061, 4, 10, 238.61, -60.16, 2e-05, 13, 17.91, 58.51, 0.60129, 12, 47.15, 61.03, 0.22241, 11, 51.65, 76.82, 0.17627, 4, 10, 226.82, -54.85, 0.00902, 13, 29.85, 53.53, 0.73558, 12, 59.9, 58.83, 0.14381, 11, 63.58, 81.82, 0.11159, 3, 10, 206.22, -39.4, 0.1251, 13, 50.86, 38.65, 0.87463, 12, 83.7, 49, 0.00028, 2, 10, 198.31, -32, 0.29717, 13, 58.97, 31.47, 0.70283, 2, 10, 191.5, -26.01, 0.57581, 13, 65.95, 25.68, 0.42419, 2, 10, 192.24, -41.11, 0.81307, 13, 64.79, 40.75, 0.18693, 2, 10, 187, -44.55, 0.82941, 13, 69.93, 44.32, 0.17059, 2, 10, 169.43, -40.81, 0.9314, 13, 87.6, 41.08, 0.0686, 2, 10, 151.85, -37.08, 0.99442, 13, 105.28, 37.83, 0.00558, 1, 10, 125.5, -38.13, 1, 1, 10, 81.73, -43.43, 1, 1, 10, 53.54, -44.13, 1, 2, 9, 248.69, -36, 0.04851, 10, 22.23, -41.88, 0.95149, 2, 9, 232.24, -37.36, 0.2305, 10, 5.88, -39.61, 0.7695, 2, 9, 216.38, -36.4, 0.55565, 10, -9.38, -35.2, 0.44435, 3, 4, 6.7, -152.46, 0.00012, 9, 196.43, -39.13, 0.89563, 10, -29.44, -33.5, 0.10425, 3, 3, 117.28, -140.97, 0.00096, 4, 41.74, -134.65, 0.01831, 9, 157.49, -44.46, 0.98074, 3, 3, 142.14, -136.56, 0.01481, 4, 64.27, -123.26, 0.20013, 9, 132.47, -47.82, 0.78506, 3, 3, 159.31, -134.56, 0.04481, 4, 80.13, -116.39, 0.50423, 9, 115.23, -49.11, 0.45097, 3, 3, 145.19, -121.64, 0.11335, 4, 62.88, -108.09, 0.69417, 9, 128.8, -62.61, 0.19248, 3, 23, 181.83, -26.66, 0.00217, 3, 114.47, -97.41, 0.33597, 4, 26.48, -93.76, 0.66186, 3, 23, 157.1, -6.54, 0.01574, 3, 88.18, -79.37, 0.61158, 4, -3.9, -84.07, 0.37268, 5, 2, 204.35, -78.65, 0.00653, 23, 140.17, 1.11, 0.04062, 3, 70.68, -73.12, 0.75133, 4, -22.45, -83.13, 0.19614, 9, 201.23, -114.18, 0.00538, 5, 2, 192.77, -72.05, 0.02209, 23, 128.59, 7.71, 0.0735, 3, 58.6, -67.49, 0.79737, 4, -35.65, -81.23, 0.093, 9, 213.06, -120.31, 0.01404, 5, 2, 180.35, -75.63, 0.05648, 23, 116.17, 4.13, 0.13078, 3, 46.51, -72.08, 0.76635, 4, -45.9, -89.1, 0.04015, 9, 225.34, -116.24, 0.00624, 5, 2, 164.2, -76.2, 0.11639, 23, 100.03, 3.56, 0.2263, 3, 30.46, -73.96, 0.64466, 4, -60.72, -95.54, 0.01079, 9, 241.44, -115.02, 0.00185, 5, 2, 148.9, -82.18, 0.16552, 23, 84.72, -2.42, 0.37733, 3, 15.7, -81.17, 0.45581, 4, -72.77, -106.7, 0.00107, 9, 256.49, -108.43, 0.00027, 3, 2, 122.39, -99.33, 0.12106, 23, 58.21, -19.57, 0.68853, 3, -9.33, -100.43, 0.19041, 4, 1, 66.1, 149.12, 0.01063, 2, 87.94, -116.94, 0.0037, 23, 23.76, -37.18, 0.94886, 3, -42.22, -120.79, 0.03682, 3, 1, 94.04, 119.23, 0.14005, 23, -15.12, -49.91, 0.8598, 3, -79.93, -136.66, 0.00014, 3, 1, 116.47, 82.16, 0.43577, 2, 5.99, -134.39, 0.00013, 23, -58.19, -54.63, 0.56409, 2, 1, 126.81, 47.72, 0.71776, 23, -93.78, -49.49, 0.28224, 2, 1, 127.46, 14.15, 0.92885, 23, -124.5, -35.93, 0.07115, 2, 1, 121.62, -17.35, 0.99963, 23, -150.6, -17.36, 0.00037, 2, 1, 110.33, -42.61, 0.99671, 29, -160.67, 35.9, 0.00329, 2, 1, 96.33, -60.95, 0.98145, 29, -147.3, 54.71, 0.01855, 2, 1, 50.24, -87.28, 0.85309, 29, -102.13, 82.59, 0.14691, 2, 1, 0.35, -110.06, 0.62253, 29, -53.04, 107.05, 0.37747, 2, 1, -93.99, -109.52, 0.17221, 29, 41.26, 109.71, 0.82779, 3, 1, -146.22, -103.48, 0.0437, 29, 93.67, 105.45, 0.94649, 30, 177.71, 125.84, 0.00981, 3, 1, -186.07, -92.51, 0.00728, 29, 133.87, 95.83, 0.93688, 30, 151.6, 93.8, 0.05584, 3, 1, -226.33, -74.82, 3e-05, 29, 174.7, 79.52, 0.7501, 30, 119.17, 64.09, 0.24987, 2, 29, 196.08, 68.87, 0.4271, 30, 100.3, 49.46, 0.5729, 2, 29, 184.92, 84.46, 0.14719, 30, 119.19, 52.74, 0.85281, 2, 29, 172.12, 105.61, 0.03599, 30, 143.79, 55.09, 0.96401, 2, 29, 163.64, 127.82, 0.0053, 30, 167.49, 53.1, 0.9947, 1, 30, 234.19, 32.22, 1, 1, 30, 294.33, 24.7, 1, 1, 30, 364.28, 21.58, 1, 2, 30, 388.32, 22.89, 0.9882, 34, 183.36, -20.75, 0.0118, 2, 30, 407.75, 25.63, 0.76951, 34, 164.1, -24.45, 0.23049, 2, 30, 425.57, 35, 0.30194, 34, 146.76, -34.7, 0.69806, 2, 30, 426.24, 42.1, 0.20478, 34, 146.44, -41.81, 0.79522, 2, 30, 460.23, 79.85, 0.00616, 34, 114.36, -81.2, 0.99384, 1, 34, 75.57, -77.27, 1, 1, 34, 34.69, -78.05, 1, 1, 34, -23.04, -88.82, 1, 1, 34, -26.25, -77.48, 1, 1, 34, 49.08, -53.79, 1, 1, 34, 41.89, -43.75, 1, 2, 34, 16.96, -37.22, 0.95376, 33, 124.58, -11.17, 0.04624, 2, 34, -14.84, -37.05, 0.44937, 33, 103.86, -35.29, 0.55063, 2, 34, -27.7, -28.96, 0.25071, 33, 89.37, -39.86, 0.74929, 2, 34, -40.37, -14.53, 0.06055, 33, 70.17, -40.17, 0.93945, 1, 33, 45.64, -32.83, 1, 1, 33, 8.42, -18.04, 1, 1, 33, -11.2, 13.31, 1, 1, 33, -12.58, 16.75, 1, 1, 33, -1.09, 37.11, 1, 1, 33, 14.5, 42, 1, 2, 34, 1.18, 59.24, 0.05233, 33, 40.84, 39.25, 0.94767, 2, 34, 10.73, 39.85, 0.30911, 33, 61.81, 33.97, 0.69089, 2, 34, 21.75, 37.16, 0.61143, 33, 70.99, 40.63, 0.38857, 2, 34, 36.45, 35.48, 0.85912, 33, 81.78, 50.75, 0.14088, 2, 34, 59.09, 34.63, 0.98516, 33, 97.09, 67.46, 0.01484, 2, 30, 458.36, -33.36, 0.02263, 34, 110.63, 31.96, 0.97737, 2, 30, 427.83, -34.26, 0.40561, 34, 141.08, 34.37, 0.59439, 2, 30, 401.38, -32.12, 0.91315, 34, 167.6, 33.54, 0.08685, 1, 30, 367.6, -35.21, 1, 1, 30, 289.34, -47.49, 1, 1, 30, 187.5, -63.48, 1, 1, 30, 129.53, -66.65, 1, 1, 30, 83.01, -61.22, 1, 1, 30, 40.25, -56.93, 1, 2, 29, 328.02, 32.81, 0.02117, 30, 10.52, -53.75, 0.97883, 2, 29, 331.39, 4.47, 0.16603, 30, -16.47, -44.48, 0.83397, 2, 29, 324.5, -21.63, 0.42979, 30, -36.99, -26.94, 0.57021, 2, 29, 306.84, -42.13, 0.70808, 30, -47.8, -2.13, 0.29192, 2, 29, 282.7, -48.57, 0.91615, 30, -43.12, 22.41, 0.08385, 2, 24, 63.75, 153.77, 0.00053, 29, 223.09, -55.62, 0.99947, 2, 24, 46.15, 112.68, 0.02681, 29, 178.49, -58.49, 0.97319, 2, 24, 27.85, 73.51, 0.16832, 29, 135.27, -59.87, 0.83168, 3, 24, 11.51, 40.25, 0.60302, 3, -50.52, 107.42, 0.01393, 29, 98.22, -60.33, 0.38305, 4, 2, 121.72, 99.01, 0.02529, 24, 34.93, 28.07, 0.8209, 3, -26.18, 97.2, 0.09989, 29, 97.94, -86.73, 0.05392, 4, 2, 145.65, 85.26, 0.06948, 24, 58.86, 14.32, 0.65509, 3, -1.21, 85.45, 0.27479, 29, 96.49, -114.29, 0.00065, 5, 2, 171.04, 69.32, 0.05576, 24, 84.25, -1.62, 0.3752, 3, 25.4, 71.63, 0.56838, 4, -107.56, 42.41, 0.00058, 8, -321.02, -88.87, 8e-05, 4, 2, 189.78, 59.5, 0.01665, 24, 102.99, -11.44, 0.18557, 3, 44.88, 63.38, 0.78291, 4, -86.53, 40.13, 0.01487, 4, 2, 198.96, 60.04, 0.00565, 24, 112.17, -10.91, 0.12375, 3, 53.98, 64.66, 0.83444, 4, -78.18, 43.98, 0.03615, 4, 2, 210.49, 63.04, 0.00084, 24, 123.71, -7.9, 0.07572, 3, 65.23, 68.6, 0.84303, 4, -68.54, 50.99, 0.08041, 3, 24, 135.4, -6.48, 0.04475, 3, 76.77, 70.96, 0.8065, 4, -58.18, 56.59, 0.14875, 3, 24, 159.6, 1.78, 0.0131, 3, 100.22, 81.18, 0.6264, 4, -38.67, 73.13, 0.3605, 3, 24, 182.18, 7.27, 0.00343, 3, 122.28, 88.49, 0.43111, 4, -19.66, 86.49, 0.56546, 4, 24, 210.13, 11.15, 0.00013, 3, 149.82, 94.63, 0.1842, 4, 4.94, 100.32, 0.76567, 20, -96.61, -13.43, 0.05, 4, 3, 164.71, 113.05, 0.06327, 4, 13.88, 122.25, 0.78257, 20, -87.66, 8.49, 0.15, 55, -127.17, 428.9, 0.00416, 4, 3, 173.18, 135.9, 0.02235, 4, 15.4, 146.57, 0.7722, 20, -86.14, 32.82, 0.19977, 55, -125.64, 453.23, 0.00567, 4, 3, 188.29, 157.29, 0.00739, 4, 23.7, 171.41, 0.78394, 21, -61.94, 15.86, 0.19964, 55, -117.35, 478.06, 0.00904, 4, 3, 210.67, 170.15, 0.0017, 4, 41.41, 190.18, 0.78579, 22, -31.4, 9.73, 0.19947, 55, -99.63, 496.83, 0.01304, 3, 4, 68.51, 199.66, 0.78458, 22, -4.3, 19.21, 0.19935, 55, -72.53, 506.31, 0.01607, 3, 4, 98.75, 196.11, 0.78544, 22, 25.94, 15.65, 0.19939, 55, -42.29, 502.76, 0.01517, 3, 4, 128.6, 175.07, 0.78698, 21, 42.95, 19.52, 0.19945, 55, -12.45, 481.72, 0.01357, 5, 4, 148.99, 151.77, 0.70197, 8, -64.47, 20.49, 0.08619, 6, -190.26, 163.58, 0, 20, 47.45, 38.02, 0.1995, 55, 7.95, 458.42, 0.01234, 5, 4, 176.46, 134.97, 0.48916, 8, -37, 3.68, 0.40255, 6, -164.87, 143.79, 0, 20, 74.91, 21.22, 0.1, 55, 35.41, 441.62, 0.00828, 4, 4, 191.84, 142.5, 0.28045, 8, -21.62, 11.21, 0.71347, 6, -148.73, 149.54, 0, 55, 50.8, 449.15, 0.00608, 2, 8, -2.55, 15.3, 0.99751, 55, 69.87, 453.24, 0.00249, 1, 8, 10.07, 13.21, 1, 3, 4, 235, 124.24, 0.14475, 8, 21.54, -7.04, 0.85521, 6, -107.9, 126.54, 4e-05, 3, 4, 242.98, 96.71, 0.39143, 8, 29.52, -34.57, 0.60848, 6, -103.07, 98.28, 8e-05, 3, 4, 250.55, 74.64, 0.68407, 8, 37.09, -56.64, 0.31579, 6, -98.04, 75.5, 0.00014, 4, 4, 259.28, 58.62, 0.86333, 8, 45.83, -72.66, 0.11176, 5, -1.97, 59.29, 0.02472, 6, -91.16, 58.6, 0.00019, 2, 4, 280.4, 38.55, 0.3986, 5, 16.58, 36.83, 0.6014, 3, 4, 295.79, 41.04, 0.09077, 5, 32.15, 37.45, 0.90759, 6, -56.87, 37.02, 0.00164, 2, 5, 67.26, 45.31, 0.82705, 6, -21.83, 45.14, 0.17295, 2, 5, 100.16, 49.27, 0.31282, 6, 11.04, 49.36, 0.68718, 2, 5, 127.85, 47.43, 0.09657, 6, 38.74, 47.72, 0.90343, 3, 5, 128.61, 25.58, 0.04295, 6, 39.67, 25.88, 0.9425, 55, 251.93, 347.5, 0.01455, 2, 6, 41.62, -20.23, 0.98445, 55, 259.06, 301.9, 0.01555, 1, 6, 42.91, -50.68, 1, 5, 3, 190.33, 64.01, 0.03096, 4, 52.56, 82.69, 0.74103, 17, -48.47, 90.86, 0.09923, 20, -48.98, -31.06, 0.0893, 55, -88.48, 389.34, 0.03948, 4, 3, 159.98, 41.1, 0.07098, 4, 30.11, 52, 0.6916, 17, -70.93, 60.17, 0.19839, 55, -110.94, 358.65, 0.03904, 4, 3, 128.18, -10.15, 0.02381, 4, 14.44, -6.25, 0.73785, 17, -86.59, 1.92, 0.19835, 55, -126.6, 300.41, 0.04, 5, 23, 196.07, 20.51, 0.00083, 3, 124.8, -49.23, 0.23579, 4, 22.48, -44.64, 0.52929, 17, -78.55, -36.47, 0.19854, 55, -118.57, 262.01, 0.03555, 5, 23, 210.6, -16.56, 6e-05, 3, 142.31, -84.99, 0.14208, 4, 49.56, -73.84, 0.62785, 17, -51.48, -65.67, 0.19872, 55, -91.49, 232.82, 0.0313, 5, 3, 184.42, -115.24, 0.02855, 4, 98.6, -90.66, 0.67635, 7, -124.57, 39.32, 0.06608, 17, -2.43, -82.49, 0.19876, 55, -42.44, 215.99, 0.03026, 5, 3, 227.29, -126.48, 0.00067, 4, 142.89, -89.05, 0.5138, 7, -80.28, 40.93, 0.25425, 17, 41.86, -80.88, 0.19866, 55, 1.85, 217.6, 0.03261, 4, 4, 178.99, -65.08, 0.56204, 7, -44.17, 64.9, 0.19963, 17, 77.96, -56.91, 0.19835, 55, 37.95, 241.58, 0.03998, 4, 4, 188.73, -12.67, 0.64632, 7, -34.44, 117.31, 0.11534, 17, 87.69, -4.5, 0.19835, 55, 47.68, 293.98, 0.04, 5, 4, 174.36, 32.1, 0.73118, 7, -48.81, 162.08, 0.03047, 6, -178.53, 41.82, 1e-05, 17, 73.33, 40.27, 0.19835, 55, 33.32, 338.75, 0.04, 5, 4, 153.51, 61.86, 0.77148, 6, -195.9, 73.73, 1e-05, 17, 52.48, 70.03, 0.09921, 20, 51.97, -51.89, 0.08929, 55, 12.47, 368.51, 0.04, 5, 4, 176.69, 70.93, 0.72488, 8, -36.77, -60.36, 0.04627, 6, -171.85, 80.13, 3e-05, 20, 75.14, -42.83, 0.19877, 55, 35.64, 377.58, 0.03006, 5, 4, 181.43, 94.62, 0.60025, 8, -32.03, -36.67, 0.17432, 6, -164.47, 103.14, 3e-05, 20, 79.88, -19.13, 0.19892, 55, 40.38, 401.27, 0.02649, 5, 4, 168.26, 126.24, 0.53946, 8, -45.2, -5.04, 0.23917, 6, -173.99, 136.04, 0, 20, 66.72, 12.49, 0.19909, 55, 27.22, 432.89, 0.02228, 4, 3, 178.05, 84.67, 0.06192, 4, 34.84, 98.92, 0.70767, 20, -66.71, -14.83, 0.1987, 55, -106.21, 405.57, 0.03171, 4, 3, 172.44, 110.66, 0.05117, 4, 21.97, 122.19, 0.72686, 20, -79.57, 8.44, 0.19907, 55, -119.07, 428.84, 0.0229, 5, 3, 193.37, -129.58, 0.01611, 4, 111.3, -101.8, 0.61909, 7, -111.87, 28.18, 0.20672, 9, 81, -52.67, 0.14008, 55, -29.74, 204.85, 0.018, 6, 3, 228.82, 59.58, 0.00188, 4, 90.69, 89.54, 0.76961, 6, -255.2, 108.31, 0, 17, -10.34, 97.71, 0.09921, 20, -10.85, -24.21, 0.08929, 55, -50.35, 396.19, 0.04, 5, 4, 126.52, 81.47, 0.77149, 6, -220.51, 96.26, 0, 17, 25.49, 89.64, 0.09921, 20, 24.98, -32.28, 0.08929, 55, -14.52, 388.12, 0.04, 4, 3, 184.76, -29.53, 0.00124, 4, 74.2, -8.48, 0.68338, 19, -5.78, 3.09, 0.24538, 55, -66.84, 298.17, 0.07, 4, 3, 237.82, 159.04, 8e-05, 4, 70.62, 187.37, 0.72193, 22, -2.19, 6.92, 0.24809, 55, -70.42, 494.03, 0.0299, 3, 4, 111.41, 176.81, 0.77128, 22, 38.59, -3.64, 0.19877, 55, -29.64, 483.46, 0.02995, 4, 3, 259.62, 121.94, 5e-05, 4, 102.19, 158.14, 0.76127, 22, 29.38, -22.32, 0.19833, 55, -38.85, 464.79, 0.04034, 4, 3, 208.78, 133.72, 0.00706, 4, 50.12, 154.75, 0.75379, 22, -22.69, -25.7, 0.19831, 55, -90.93, 461.4, 0.04084, 4, 3, 199.5, 150.82, 0.00594, 4, 36.3, 168.45, 0.76725, 22, -36.51, -12, 0.19886, 55, -104.74, 475.1, 0.02795, 5, 4, 145.18, 144.67, 0.72125, 8, -68.28, 13.39, 0.0501, 6, -194.86, 156.96, 0, 21, 59.53, -10.87, 0.19878, 55, 4.13, 451.33, 0.02987, 4, 4, 140.45, 119.57, 0.7637, 6, -202.37, 132.55, 0, 21, 54.81, -35.97, 0.19844, 55, -0.59, 426.23, 0.03786, 4, 4, 107.55, 105.09, 0.75856, 6, -236.7, 121.86, 0, 21, 21.91, -50.46, 0.19821, 55, -33.49, 411.74, 0.04323, 5, 3, 211.8, 83.05, 0.01279, 4, 67.63, 107.11, 0.746, 6, -276.14, 128.37, 0, 21, -18.02, -48.44, 0.19822, 55, -73.42, 413.76, 0.04299, 4, 3, 184.31, 130.37, 0.02029, 4, 27.66, 144.49, 0.7523, 21, -57.98, -11.06, 0.19883, 55, -113.39, 451.14, 0.02859, 4, 3, 191.18, 103.28, 0.03091, 4, 42.05, 120.53, 0.73134, 21, -43.6, -35.02, 0.19837, 55, -99, 427.18, 0.03938, 4, 3, 233.91, 122.03, 0.00194, 4, 77.55, 150.8, 0.75568, 22, 4.73, -29.65, 0.19817, 55, -63.5, 457.45, 0.04422, 4, 4, 117.88, 5.7, 0.73774, 6, -237.63, 21.95, 0, 19, 37.89, 17.27, 0.19726, 55, -23.17, 312.35, 0.065, 4, 3, 218.09, -63.26, 0.00019, 4, 115.84, -31.17, 0.73755, 19, 35.86, -19.61, 0.19726, 55, -25.2, 275.48, 0.065, 4, 3, 190.99, -73.25, 0.01247, 4, 92.78, -48.56, 0.72709, 19, 12.8, -36.99, 0.19734, 55, -48.26, 258.1, 0.0631, 4, 3, 159.37, -60.68, 0.05709, 4, 58.88, -45.64, 0.6835, 19, -21.11, -34.07, 0.19739, 55, -82.17, 261.02, 0.06202, 4, 3, 149.27, -36.94, 0.04844, 4, 42.36, -25.82, 0.68948, 19, -37.63, -14.25, 0.19727, 55, -98.68, 280.83, 0.06481, 3, 4, 43.03, 6.75, 0.73774, 19, -36.96, 18.32, 0.19726, 55, -98.02, 313.4, 0.065, 3, 4, 62.18, 27.03, 0.73774, 19, -17.81, 38.6, 0.19726, 55, -78.87, 333.68, 0.065, 4, 4, 94.76, 27.7, 0.73774, 6, -258.13, 46.41, 0, 19, 14.77, 39.27, 0.19726, 55, -46.29, 334.35, 0.065, 4, 4, 155.79, -2.15, 0.71314, 7, -67.38, 127.83, 0.03416, 18, 64.32, 7.36, 0.1977, 55, 14.74, 304.5, 0.055, 4, 4, 150.39, -46.63, 0.67401, 7, -72.78, 83.35, 0.07329, 18, 58.92, -37.12, 0.1977, 55, 9.34, 260.02, 0.055, 5, 3, 201.57, -100.53, 0.00987, 4, 110.78, -71.62, 0.69761, 7, -112.38, 58.36, 0.04516, 18, 19.31, -62.12, 0.19794, 55, -30.26, 235.03, 0.04941, 5, 3, 159.98, -82.48, 0.07482, 4, 65.75, -66.33, 0.67221, 7, -157.41, 63.65, 0.00646, 18, -25.72, -56.83, 0.19798, 55, -75.29, 240.32, 0.04854, 5, 23, 207.36, 17.37, 0.00019, 3, 136.32, -51.44, 0.14968, 4, 34.14, -43.44, 0.60109, 18, -57.33, -33.94, 0.19787, 55, -106.91, 263.21, 0.05117, 4, 3, 139.16, -12.76, 0.00313, 4, 25.7, -5.58, 0.74417, 18, -65.77, 3.92, 0.1977, 55, -115.34, 301.07, 0.055, 4, 3, 161.2, 19.68, 0.01733, 4, 37.45, 31.84, 0.72997, 18, -54.02, 41.34, 0.1977, 55, -103.6, 338.49, 0.055, 5, 3, 191.8, 35.76, 0.00851, 4, 62.12, 56.06, 0.73879, 6, -287.37, 78.27, 0, 18, -29.35, 65.57, 0.1977, 55, -78.93, 362.71, 0.055, 4, 4, 103.65, 56.5, 0.7473, 6, -246.05, 74.02, 0, 18, 12.18, 66, 0.1977, 55, -37.39, 363.15, 0.055, 4, 4, 139.6, 33.63, 0.7473, 6, -212.9, 47.25, 0, 18, 48.13, 43.13, 0.1977, 55, -1.44, 340.28, 0.055, 6, 24, 214.18, -16.87, 1e-05, 3, 156.14, 67.05, 0.16341, 4, 18.95, 75.73, 0.75473, 17, -82.09, 83.9, 0.025, 20, -82.6, -38.02, 0.02438, 55, -122.1, 382.38, 0.03247, 4, 24, 185.32, -19.65, 0.00262, 3, 127.6, 61.92, 0.40176, 4, -6.9, 62.59, 0.56322, 55, -147.94, 369.24, 0.0324, 4, 24, 158.57, -23.99, 0.01385, 3, 101.3, 55.4, 0.66697, 4, -30.21, 48.76, 0.29004, 55, -171.26, 355.41, 0.02914, 4, 24, 130.35, -26.98, 0.04955, 3, 73.41, 50.13, 0.84615, 4, -55.39, 35.66, 0.08139, 55, -196.43, 342.32, 0.02291, 5, 2, 192.79, 36.74, 0.00499, 24, 106, -34.21, 0.10479, 3, 49.74, 40.93, 0.86152, 4, -75.4, 20.04, 0.00915, 55, -216.45, 326.69, 0.01954, 5, 2, 168.6, 38.22, 0.0558, 24, 81.81, -32.72, 0.20628, 3, 25.5, 40.44, 0.72372, 8, -311.93, -118.71, 4e-05, 55, -239.51, 319.23, 0.01416, 4, 2, 137.56, 52.65, 0.19133, 24, 50.77, -18.29, 0.49012, 3, -6.61, 52.28, 0.31351, 55, -273.67, 321.31, 0.00504, 3, 2, 106.1, 65.81, 0.11389, 24, 19.32, -5.14, 0.82066, 3, -39.03, 62.83, 0.06545, 2, 24, -12.51, 7.22, 0.79629, 29, 57.9, -53.84, 0.20371, 3, 24, 155.63, -75.81, 0.00027, 3, 102.59, 3.52, 0.9604, 55, -155.05, 306.11, 0.03933, 3, 24, 128.44, -73.42, 0.00132, 3, 75.3, 3.68, 0.9665, 55, -181.23, 298.39, 0.03218, 3, 24, 101.25, -71.03, 0.00441, 3, 48, 3.84, 0.97056, 55, -207.41, 290.67, 0.02502, 3, 24, 79.59, -68.29, 0.01074, 3, 26.19, 4.81, 0.96998, 55, -228.57, 285.31, 0.01928, 4, 2, 131.7, 4.97, 0.79218, 24, 44.91, -65.97, 0.03071, 3, -8.56, 4.29, 0.16692, 55, -261.69, 274.79, 0.0102, 5, 2, 96.97, 7.94, 0.93426, 24, 10.18, -63, 0.06331, 3, -43.41, 4.41, 0.00056, 29, 5.52, -105.83, 0.00081, 55, -295.1, 264.85, 0.00106, 3, 2, 63.63, 10.75, 0.91155, 24, -23.16, -60.2, 0.06215, 29, -7.05, -74.82, 0.0263, 4, 23, 172.72, 26.57, 0.00621, 3, 101.04, -45.1, 0.59737, 4, -1.46, -47.54, 0.36235, 55, -142.51, 259.11, 0.03406, 5, 2, 206.23, -46.75, 0.00163, 23, 142.05, 33.02, 0.02806, 3, 69.95, -41.18, 0.85959, 4, -32.37, -52.75, 0.0845, 55, -173.42, 253.9, 0.02623, 6, 2, 181.82, -44.67, 0.032, 23, 117.64, 35.09, 0.0713, 3, 45.45, -41.1, 0.854, 4, -55.85, -59.75, 0.01893, 9, 225.1, -147.23, 0.00319, 55, -196.89, 246.9, 0.02058, 6, 2, 161.61, -44.06, 0.11864, 23, 97.43, 35.7, 0.1293, 3, 25.26, -42.14, 0.73253, 4, -74.88, -66.57, 0.0022, 9, 245.32, -147.03, 0.0006, 55, -215.93, 240.08, 0.01672, 4, 2, 129.75, -44.38, 0.39835, 23, 65.57, 35.39, 0.25647, 3, -6.48, -45.06, 0.33403, 55, -245.47, 228.14, 0.01115, 4, 2, 94.89, -43.79, 0.55184, 23, 30.71, 35.97, 0.38938, 3, -41.27, -47.32, 0.05673, 55, -278.13, 215.94, 0.00205, 3, 1, 13.77, 85.97, 0.02298, 2, 52.72, -42.87, 0.5618, 23, -11.46, 36.89, 0.41522, 3, 1, 82.67, 70.92, 0.40355, 2, 10.04, -99, 0.0431, 23, -54.14, -19.23, 0.55335, 2, 1, 63.5, 104.79, 0.09337, 23, -15.34, -16.14, 0.90663, 4, 2, 89.9, -87.3, 0.0638, 23, 25.73, -7.53, 0.87363, 3, -42.68, -91.08, 0.06076, 55, -266.86, 173.63, 0.00181, 4, 2, 126.96, -76.98, 0.19877, 23, 62.78, 2.78, 0.51675, 3, -6.6, -77.78, 0.27656, 55, -236.15, 196.77, 0.00793, 6, 2, 158.79, -62.66, 0.13096, 23, 94.61, 17.1, 0.2228, 3, 23.96, -60.91, 0.62645, 4, -70.71, -84.91, 0.00521, 9, 247.4, -128.33, 0.00101, 55, -211.75, 221.74, 0.01356, 3, 1, 87.92, 38.13, 0.72727, 2, -21.91, -89.95, 0.03616, 23, -86.09, -10.18, 0.23657, 3, 1, 60.13, 64.1, 0.34725, 2, 13.35, -75.69, 0.16888, 23, -50.83, 4.08, 0.48387, 2, 29, 225.82, 36.99, 0.51051, 30, 58.66, 36.51, 0.48949, 2, 29, 274.98, 1.19, 0.79555, 30, 5.07, 7.76, 0.20445, 1, 29, 255.42, -12.23, 1, 2, 24, 19.12, 172.01, 0, 29, 219.19, -7.56, 1, 1, 30, 21.62, -9.18, 1, 1, 30, 56.63, -11, 1, 1, 30, 93.68, -6.56, 1, 2, 24, -5, 134.2, 0.00088, 29, 174.56, -3.14, 0.99912, 1, 30, 124.11, -8.11, 1, 1, 30, 172.65, -6.21, 1, 3, 1, -168.89, -6.38, 3e-05, 29, 119.62, 9.18, 0.99799, 30, 79.71, 144.25, 0.00197, 2, 1, -104.83, -13.71, 0.00958, 29, 55.35, 14.33, 0.99042, 3, 1, -31.05, -17.87, 0.37101, 2, -22.55, 41.55, 0.02953, 29, -18.53, 15.98, 0.59946, 2, 1, 37.41, -6.26, 0.99408, 29, -86.56, 2.05, 0.00592, 3, 1, 71.7, 8.79, 0.95642, 2, -41.68, -62.87, 0.01544, 23, -105.86, 16.89, 0.02813, 4, 4, 211.83, 117.39, 0.211, 8, -1.63, -13.89, 0.77427, 6, -131.7, 122.35, 3e-05, 55, 70.78, 424.05, 0.01469, 4, 4, 219.08, 71.29, 0.8063, 8, 5.62, -60, 0.17283, 6, -129.69, 75.72, 0.00011, 55, 78.04, 377.94, 0.02076, 4, 4, 223.14, 39.37, 0.89642, 8, 9.68, -91.91, 0.07494, 6, -129.25, 43.54, 7e-05, 55, 82.1, 346.02, 0.02857, 4, 4, 226.96, 13.5, 0.92457, 7, 3.8, 143.48, 0.0472, 6, -128.36, 17.41, 2e-05, 55, 85.92, 320.15, 0.02821, 3, 4, 230.66, -60.47, 0.75767, 7, 7.49, 69.51, 0.21535, 55, 89.61, 246.18, 0.02698, 3, 4, 229.88, -25.07, 0.83581, 7, 6.72, 104.91, 0.13606, 55, 88.84, 281.58, 0.02812, 3, 4, 224.02, -87.17, 0.5113, 7, 0.85, 42.81, 0.4623, 55, 82.98, 219.48, 0.0264, 3, 4, 231.85, -129.04, 0.06067, 7, 8.68, 0.94, 0.92609, 55, 90.8, 177.61, 0.01324, 4, 4, 213.56, -111.58, 0.16329, 7, -9.61, 18.4, 0.67338, 9, -12.39, -9.88, 0.14368, 55, 72.51, 195.07, 0.01964, 5, 4, 178.9, -96.62, 0.44128, 7, -44.26, 33.36, 0.39965, 9, 15.44, -35.38, 0.08052, 17, 77.87, -88.45, 0.05, 55, 37.86, 210.03, 0.02856, 3, 4, 256.33, 3.63, 0.97883, 6, -100.29, 4.3, 0, 55, 115.29, 310.28, 0.02116, 2, 5, 23.35, -9.23, 0.98161, 55, 151.61, 300.3, 0.01839, 2, 5, 46.46, -14.12, 0.9821, 55, 175.14, 298.22, 0.0179, 3, 5, 71.85, -15.48, 0.91984, 6, -16.78, -15.61, 0.06317, 55, 200.51, 299.91, 0.01699, 3, 5, 97.55, -16.89, 0.21355, 6, 8.93, -16.82, 0.77023, 55, 226.19, 301.61, 0.01622, 3, 4, 252.95, 27.18, 0.97992, 6, -101, 28.07, 0.0001, 55, 111.91, 333.83, 0.01998, 3, 4, 281.15, 27.41, 0.34592, 5, 15.99, 25.67, 0.63707, 55, 140.11, 334.06, 0.01701, 4, 4, 304.27, 29.24, 0.03966, 5, 39.15, 24.72, 0.93896, 6, -49.78, 24.34, 0.00492, 55, 163.22, 335.89, 0.01646, 3, 5, 70.45, 26.87, 0.84244, 6, -18.49, 26.73, 0.14267, 55, 194.04, 341.79, 0.01489, 3, 5, 98.54, 27.65, 0.28926, 6, 9.59, 27.72, 0.69633, 55, 221.83, 345.93, 0.01441, 3, 4, 261.46, 38, 0.85849, 5, -2.29, 38.56, 0.14135, 6, -91.33, 37.87, 0.00016, 4, 4, 261.25, -34.8, 0.84305, 7, 38.08, 95.18, 0.11196, 5, -11.25, -33.7, 0.02466, 55, 120.2, 271.85, 0.02032, 4, 4, 217.92, 95.36, 0.44865, 8, 4.46, -35.92, 0.53375, 6, -128.13, 99.77, 7e-05, 55, 76.87, 402.02, 0.01754, 3, 7, -4.57, -7.61, 0.85664, 9, -8.61, 16.34, 0.12941, 55, 77.55, 169.06, 0.01394, 3, 7, -26.23, -13.24, 0.17356, 9, 13.69, 14.55, 0.81144, 55, 55.89, 163.44, 0.015, 3, 7, -53.44, -19.92, 0.01675, 9, 41.58, 11.94, 0.96892, 55, 28.69, 156.75, 0.01433, 3, 4, 199.23, -127.01, 0.14531, 9, 6.21, -0.01, 0.83422, 55, 58.19, 179.64, 0.02047, 4, 4, 158.87, -115.01, 0.29139, 7, -64.3, 14.97, 0.13067, 9, 40.4, -24.58, 0.55415, 55, 17.83, 191.64, 0.02379, 4, 3, 200.16, -155.13, 0.00324, 4, 125.18, -124.3, 0.22354, 9, 75.27, -26.86, 0.75121, 55, -15.87, 182.35, 0.02202, 4, 3, 171.09, -156.35, 0.00583, 4, 97.7, -133.86, 0.11856, 9, 104.37, -26.85, 0.85848, 55, -43.35, 172.79, 0.01714, 4, 4, 20.5, -170.46, 5e-05, 9, 189.3, -17.59, 0.98153, 10, -31.69, -10.93, 0.01267, 55, -120.55, 136.19, 0.00575, 3, 9, 187.04, 8.03, 0.99221, 10, -28.29, 14.58, 0.00227, 55, -110, 112.72, 0.00552, 2, 9, 103.72, 5.52, 0.98275, 55, -32.12, 142.43, 0.01725, 2, 9, 72.9, 8.57, 0.98349, 55, -2, 149.66, 0.01651, 2, 9, 206.52, -17.9, 0.78272, 10, -14.95, -15, 0.21728, 2, 9, 224.59, -17.03, 0.31642, 10, 2.87, -18.09, 0.68358, 2, 9, 242.6, -15.24, 0.03012, 10, 20.83, -20.29, 0.96988, 2, 9, 202.98, 15, 0.83366, 10, -11.21, 17.89, 0.16634, 2, 9, 220.99, 16.79, 0.32302, 10, 6.75, 15.69, 0.67698, 2, 9, 237.93, 18.13, 0.01512, 10, 23.57, 13.3, 0.98488, 2, 13, 0.34, -8.44, 0.53159, 12, 44.93, -8.15, 0.46841, 1, 13, 19.14, -4.2, 1, 4, 10, 234, -26.15, 0.00615, 13, 23.45, 24.64, 0.81122, 12, 60.1, 29.24, 0.11413, 11, 79.67, 57, 0.0685, 1, 13, 44.04, 2.63, 1, 2, 10, 203.47, -2.14, 0.07011, 13, 54.64, 1.48, 0.92989, 2, 10, 191.37, 0.87, 0.61154, 13, 66.82, -1.19, 0.38846, 2, 13, -18.51, -6.66, 0.01392, 12, 26.16, -10.61, 0.98608, 2, 10, 183.55, -21.45, 0.77787, 13, 74.02, 21.34, 0.22213, 1, 10, 178.79, 4.75, 1, 2, 10, 190.28, 20.36, 0.63523, 13, 68.44, -20.65, 0.36477, 1, 10, 162.34, -6.63, 1], "hull": 178, "edges": [6, 8, 8, 10, 16, 18, 28, 30, 30, 32, 42, 44, 52, 54, 66, 68, 76, 78, 78, 80, 118, 120, 148, 150, 150, 152, 164, 166, 170, 172, 176, 178, 178, 180, 180, 182, 182, 184, 200, 202, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 222, 224, 224, 226, 232, 234, 234, 236, 236, 238, 238, 240, 244, 246, 256, 258, 262, 264, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 286, 288, 304, 306, 322, 324, 328, 330, 334, 336, 336, 338, 338, 340, 346, 348, 344, 346, 340, 342, 342, 344, 330, 332, 332, 334, 324, 326, 326, 328, 2, 0, 0, 354, 2, 4, 4, 6, 10, 12, 26, 28, 18, 20, 24, 26, 20, 22, 22, 24, 134, 136, 136, 138, 144, 146, 146, 148, 138, 140, 140, 142, 142, 144, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 172, 174, 174, 176, 320, 322, 314, 316, 316, 318, 310, 312, 312, 314, 306, 308, 308, 310, 300, 302, 302, 304, 296, 298, 298, 300, 294, 296, 292, 294, 288, 290, 290, 292, 128, 130, 124, 126, 126, 128, 120, 122, 122, 124, 130, 132, 132, 134, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 322, 382, 382, 380, 356, 384, 310, 386, 384, 386, 356, 390, 390, 392, 392, 376, 318, 398, 398, 400, 402, 404, 320, 406, 406, 408, 408, 410, 410, 412, 312, 414, 414, 416, 416, 412, 404, 314, 318, 320, 400, 418, 418, 402, 406, 380, 406, 398, 400, 408, 408, 378, 420, 422, 422, 424, 426, 428, 428, 430, 430, 432, 432, 434, 434, 420, 424, 426, 436, 438, 438, 440, 440, 442, 442, 444, 444, 446, 446, 448, 448, 450, 450, 452, 452, 454, 454, 436, 166, 168, 168, 170, 162, 164, 284, 286, 280, 282, 282, 284, 268, 270, 264, 266, 266, 268, 254, 256, 252, 254, 198, 200, 196, 198, 194, 196, 258, 260, 260, 262, 206, 208, 202, 204, 204, 206, 230, 232, 218, 220, 220, 222, 226, 228, 228, 230, 246, 248, 240, 242, 242, 244, 248, 250, 250, 252, 192, 194, 192, 190, 186, 184, 186, 188, 188, 190, 566, 568, 568, 570, 570, 572, 572, 574, 352, 354, 574, 352, 576, 578, 578, 580, 580, 582, 582, 584, 348, 350, 350, 352, 584, 350, 12, 14, 14, 16, 604, 606, 608, 610, 54, 56, 56, 58, 110, 112, 112, 114, 108, 110, 104, 106, 106, 108, 94, 96, 96, 98, 92, 94, 90, 92, 88, 90, 84, 86, 80, 82, 82, 84, 102, 104, 98, 100, 100, 102, 58, 60, 60, 62, 62, 64, 64, 66, 68, 70, 70, 72, 72, 74, 74, 76, 86, 88, 44, 46, 46, 48, 640, 642, 642, 644, 48, 50, 50, 52, 114, 116, 116, 118], "width": 656, "height": 1257}}, "chair_b": {"chair_b": {"type": "mesh", "uvs": [0.64498, 0.00403, 0.84812, 0.00442, 0.95126, 0.05775, 0.98218, 0.11221, 0.99879, 0.23094, 0.994, 0.39402, 0.94076, 0.66408, 0.87692, 0.80896, 0.79286, 0.88071, 0.16482, 0.97878, 0.00264, 0.99574, 0.07381, 0.10737, 0.19907, 0.07703, 0.45083, 0.02379], "triangles": [4, 1, 3, 0, 1, 5, 3, 1, 2, 4, 5, 1, 0, 5, 6, 0, 6, 13, 7, 8, 6, 9, 11, 12, 8, 13, 6, 9, 12, 13, 8, 9, 13, 9, 10, 11], "vertices": [193.93, 130.58, 244.1, 130.49, 269.58, 118.81, 277.22, 106.88, 281.32, 80.88, 280.14, 45.17, 266.99, -13.98, 251.22, -45.7, 230.45, -61.42, 75.33, -82.89, 35.27, -86.61, 52.85, 107.94, 83.79, 114.59, 145.97, 126.25], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 18, 20, 20, 22], "width": 247, "height": 219}}, "chair_up": {"chair_up": {"type": "mesh", "uvs": [0.99154, 0.00949, 0.96905, 0.11452, 0.557, 0.13297, 0.55655, 0.16643, 0.55839, 1, 0.46087, 0.99906, 0.4588, 0.13698, 0.40349, 0.13371, 0.27422, 0.12963, 0, 0.121, 0, 0.09366, 0.00738, 0.062, 0.19749, 0.03958, 0.29844, 0.02767, 0.5898, 0.01645, 0.77466, 0.02807, 0.93008, 0.05488, 0.94277, 0.00596, 0.99999, 0.00041], "triangles": [0, 17, 18, 16, 17, 0, 1, 16, 0, 12, 9, 10, 8, 12, 13, 12, 10, 11, 8, 9, 12, 13, 6, 7, 7, 8, 13, 2, 14, 15, 1, 2, 15, 1, 15, 16, 6, 13, 14, 2, 6, 14, 3, 6, 2, 5, 6, 3, 5, 3, 4], "vertices": [226.98, -64.39, 214.45, -155.56, -15.06, -171.57, -15.31, -200.61, -14.28, -924.15, -68.6, -923.33, -69.75, -175.05, -100.56, -172.21, -172.57, -168.67, -325.31, -161.18, -325.31, -137.44, -321.2, -109.96, -215.31, -90.5, -159.07, -80.17, 3.21, -70.43, 106.18, -80.51, 192.75, -103.78, 199.82, -61.32, 231.69, -56.51], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 26, 28, 32, 34, 34, 36, 28, 30, 30, 32, 36, 0, 24, 26, 14, 16, 16, 18, 22, 24], "width": 557, "height": 868}}, "chair_up2": {"chair_up": {"type": "mesh", "uvs": [0.99154, 0.00949, 0.96905, 0.11452, 0.557, 0.13297, 0.4588, 0.13698, 0.40349, 0.13371, 0.27422, 0.12963, 0.19749, 0.03958, 0.29844, 0.02767, 0.5898, 0.01645, 0.77466, 0.02807, 0.93008, 0.05488, 0.94277, 0.00596, 0.99999, 0.00041], "triangles": [0, 11, 12, 10, 11, 0, 1, 10, 0, 5, 6, 7, 7, 3, 4, 4, 5, 7, 2, 8, 9, 1, 2, 9, 1, 9, 10, 3, 7, 8, 2, 3, 8], "vertices": [226.98, -64.39, 214.45, -155.56, -15.06, -171.57, -69.75, -175.05, -100.56, -172.21, -172.57, -168.67, -215.31, -90.5, -159.07, -80.17, 3.21, -70.43, 106.18, -80.51, 192.75, -103.78, 199.82, -61.32, 231.69, -56.51], "hull": 13, "edges": [0, 2, 2, 4, 6, 8, 14, 16, 20, 22, 22, 24, 16, 18, 18, 20, 24, 0, 12, 14, 8, 10, 12, 10, 6, 4], "width": 557, "height": 868}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.06146, 0.45185, 0.33871, 0.05537, 0.8779, 0.11805, 0.96095, 0.57811, 0.5176, 0.95028, 0.09501, 0.92597], "triangles": [4, 0, 1, 4, 2, 3, 4, 1, 2, 4, 5, 0], "vertices": [2, 6, 66.68, 39.11, 0.964, 53, -253.84, 82.67, 0.036, 2, 6, 76.83, 23.54, 0.964, 53, -243.69, 67.1, 0.036, 2, 6, 70.29, -2.14, 0.964, 53, -250.23, 41.42, 0.036, 2, 6, 55.08, -3.6, 0.964, 53, -265.44, 39.95, 0.036, 2, 6, 47.11, 19.85, 0.964, 53, -273.41, 63.41, 0.036, 2, 6, 51.45, 40.11, 0.964, 53, -269.07, 83.67, 0.036], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 49, "height": 32}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.08472, 0.41285, 0.15239, 0.9009, 0.4795, 0.89656, 0.91564, 0.59071, 0.88932, 0.17858, 0.37422, 0.09615], "triangles": [1, 0, 2, 0, 5, 2, 3, 5, 4, 3, 2, 5], "vertices": [2, 6, 61.11, 84.22, 0.98171, 53, -259.41, 127.78, 0.01829, 2, 6, 48.27, 84.41, 0.98156, 53, -272.26, 127.97, 0.01844, 2, 6, 46.68, 74.73, 0.97388, 53, -273.84, 118.29, 0.02612, 2, 6, 52.25, 60.47, 0.969, 53, -268.27, 104.02, 0.031, 2, 6, 62.94, 59.39, 0.969, 53, -257.58, 102.95, 0.031, 2, 6, 67.72, 74.24, 0.9737, 53, -252.8, 117.8, 0.0263], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 30, "height": 26}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 38, -9.56, -6.09, 0.964, 53, -265.64, 53.28, 0.036, 2, 38, -6.8, 9.67, 0.964, 53, -262.87, 69.04, 0.036, 2, 38, 8.96, 6.9, 0.964, 53, -247.11, 66.27, 0.036, 2, 38, 6.2, -8.86, 0.964, 53, -249.88, 50.51, 0.036], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.97496, 0.01347, 0.98877, 0.07807, 0.9349, 0.21245, 0.85446, 0.35212, 0.75957, 0.49043, 0.84852, 0.56225, 0.71313, 0.77237, 0.58339, 0.8851, 0.42271, 0.94304, 0.25448, 0.94978, 0.13143, 0.92833, 0.04374, 0.88169, 0.03653, 0.74649, 0.09412, 0.55873, 0.1806, 0.35778, 0.28644, 0.21269, 0.4363, 0.17473, 0.58247, 0.18204, 0.71924, 0.24232, 0.81209, 0.21485, 0.90078, 0.11223, 0.09724, 0.74259, 0.14408, 0.59131, 0.2245, 0.44142, 0.3338, 0.34842, 0.4673, 0.34287, 0.59515, 0.38814, 0.67166, 0.47835, 0.62247, 0.62131, 0.53269, 0.72679, 0.4109, 0.76566, 0.2813, 0.76011, 0.1759, 0.75039], "triangles": [20, 0, 1, 2, 20, 1, 19, 20, 2, 25, 16, 17, 24, 15, 16, 24, 16, 25, 3, 19, 2, 23, 14, 15, 26, 17, 18, 25, 17, 26, 24, 23, 15, 27, 26, 18, 3, 4, 18, 3, 18, 19, 27, 18, 4, 22, 13, 14, 23, 22, 14, 28, 26, 27, 29, 25, 26, 29, 26, 28, 21, 13, 22, 12, 13, 21, 31, 32, 22, 21, 22, 32, 31, 23, 24, 31, 22, 23, 30, 24, 25, 30, 25, 29, 31, 24, 30, 6, 27, 4, 6, 4, 5, 28, 27, 6, 11, 12, 21, 7, 29, 28, 7, 28, 6, 10, 21, 32, 11, 21, 10, 8, 30, 29, 8, 29, 7, 9, 32, 31, 10, 32, 9, 8, 9, 31, 8, 31, 30], "vertices": [2, 6, 71.15, -11.21, 0.96508, 53, -249.37, 32.35, 0.03492, 2, 6, 69.32, -11.56, 0.96509, 53, -251.21, 32, 0.03491, 2, 6, 66.19, -8.38, 0.96484, 53, -254.33, 35.17, 0.03516, 2, 6, 63.14, -3.93, 0.96451, 53, -257.38, 39.63, 0.03549, 2, 6, 60.25, 1.2, 0.96413, 53, -260.27, 44.76, 0.03587, 2, 6, 57.6, -2.67, 0.96437, 53, -262.92, 40.89, 0.03563, 2, 6, 53.14, 4.71, 0.964, 53, -267.38, 48.27, 0.036, 2, 6, 51.22, 11.37, 0.964, 53, -269.31, 54.93, 0.036, 2, 6, 51.01, 19.24, 0.964, 53, -269.51, 62.8, 0.036, 2, 6, 52.22, 27.22, 0.964, 53, -268.3, 70.78, 0.036, 2, 6, 53.81, 32.94, 0.964, 53, -266.71, 76.5, 0.036, 2, 6, 55.78, 36.87, 0.96421, 53, -264.74, 80.43, 0.03579, 2, 6, 59.44, 36.58, 0.96421, 53, -261.08, 80.14, 0.03579, 2, 6, 63.95, 32.98, 0.964, 53, -256.57, 76.54, 0.036, 2, 6, 68.58, 27.96, 0.964, 53, -251.94, 71.51, 0.036, 2, 6, 71.56, 22.27, 0.964, 53, -248.96, 65.83, 0.036, 2, 6, 71.33, 15.01, 0.964, 53, -249.19, 58.57, 0.036, 2, 6, 69.92, 8.14, 0.964, 53, -250.6, 51.7, 0.036, 2, 6, 67.18, 1.95, 0.96419, 53, -253.34, 45.51, 0.03581, 2, 6, 67.14, -2.57, 0.96448, 53, -253.38, 40.99, 0.03552, 2, 6, 69.14, -7.24, 0.9648, 53, -251.38, 36.32, 0.0352, 2, 6, 59.04, 33.69, 0.964, 53, -261.48, 77.25, 0.036, 2, 6, 62.67, 30.77, 0.964, 53, -257.85, 74.33, 0.036, 2, 6, 65.99, 26.27, 0.964, 53, -254.53, 69.83, 0.036, 2, 6, 67.56, 20.67, 0.964, 53, -252.96, 64.23, 0.036, 2, 6, 66.6, 14.33, 0.964, 53, -253.92, 57.89, 0.036, 2, 6, 64.33, 8.5, 0.964, 53, -256.19, 52.06, 0.036, 2, 6, 61.3, 5.3, 0.964, 53, -259.22, 48.86, 0.036, 2, 6, 57.91, 8.29, 0.964, 53, -262.61, 51.85, 0.036, 2, 6, 55.85, 13.03, 0.964, 53, -264.68, 56.59, 0.036, 2, 6, 55.82, 18.97, 0.964, 53, -264.7, 62.53, 0.036, 2, 6, 57.05, 25.07, 0.964, 53, -263.48, 68.63, 0.036, 2, 6, 58.18, 30.01, 0.964, 53, -262.34, 73.57, 0.036], "hull": 21, "edges": [0, 40, 0, 2, 12, 14, 14, 16, 36, 38, 38, 40, 24, 26, 26, 28, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 2, 4, 4, 6, 10, 12, 8, 10, 6, 8, 16, 18, 18, 20, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 42], "width": 48, "height": 27}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 39, -8.65, -5.26, 0.971, 53, -270.27, 107.9, 0.029, 2, 39, -6.23, 8.53, 0.97561, 53, -267.85, 121.69, 0.02439, 2, 39, 8.54, 5.94, 0.97529, 53, -253.08, 119.1, 0.02471, 2, 39, 6.12, -7.85, 0.971, 53, -255.5, 105.31, 0.029], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 15}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.56454, 0.06135, 0.72, 0.08811, 0.83662, 0.18581, 0.91958, 0.3026, 0.96144, 0.39758, 0.94584, 0.48214, 0.8889, 0.54478, 0.81197, 0.6356, 0.72449, 0.74014, 0.62857, 0.84396, 0.54926, 0.94742, 0.40552, 0.95088, 0.33715, 0.85679, 0.3166, 0.65156, 0.13584, 0.6461, 0.03445, 0.5084, 0.1139, 0.36264, 0.19924, 0.26667, 0.27093, 0.25406, 0.39714, 0.11233, 0.86793, 0.40337, 0.86571, 0.32791, 0.79185, 0.25291, 0.70496, 0.21065, 0.58496, 0.20519, 0.47718, 0.28155, 0.40163, 0.3961, 0.37163, 0.55064, 0.38941, 0.67247, 0.47274, 0.71065, 0.5583, 0.68519, 0.72941, 0.52701, 0.81052, 0.45609, 0.64041, 0.60479], "triangles": [24, 0, 1, 23, 24, 1, 22, 23, 1, 2, 22, 1, 25, 19, 0, 25, 0, 24, 21, 2, 3, 22, 2, 21, 26, 19, 25, 18, 19, 26, 20, 21, 3, 20, 3, 4, 32, 22, 21, 32, 21, 20, 5, 20, 4, 31, 23, 22, 31, 22, 32, 6, 20, 5, 32, 20, 6, 27, 18, 26, 31, 33, 24, 31, 24, 23, 25, 24, 33, 30, 26, 25, 30, 27, 26, 7, 32, 6, 31, 32, 7, 14, 16, 17, 15, 16, 14, 13, 18, 27, 17, 18, 13, 14, 17, 13, 27, 29, 28, 13, 27, 28, 30, 25, 33, 30, 29, 27, 8, 33, 31, 8, 31, 7, 9, 30, 33, 9, 33, 8, 12, 13, 28, 10, 29, 30, 10, 30, 9, 29, 12, 28, 11, 29, 10, 11, 12, 29], "vertices": [2, 6, 64.9, 74.64, 0.9747, 53, -255.62, 118.2, 0.0253, 2, 6, 63.35, 69.23, 0.97216, 53, -257.17, 112.79, 0.02784, 2, 6, 60.51, 65.46, 0.97084, 53, -260.01, 109.02, 0.02916, 2, 6, 57.46, 62.97, 0.97008, 53, -263.06, 106.53, 0.02992, 2, 6, 55.15, 61.84, 0.97, 53, -265.38, 105.4, 0.03, 2, 6, 53.41, 62.72, 0.97, 53, -267.11, 106.28, 0.03, 2, 6, 52.41, 64.97, 0.97045, 53, -268.11, 108.53, 0.02955, 2, 6, 50.92, 68.05, 0.97126, 53, -269.6, 111.61, 0.02874, 2, 6, 49.2, 71.55, 0.97259, 53, -271.32, 115.11, 0.02741, 2, 6, 47.54, 75.34, 0.97428, 53, -272.98, 118.9, 0.02572, 2, 6, 45.8, 78.55, 0.976, 53, -274.73, 122.11, 0.024, 2, 6, 46.61, 83.66, 0.97996, 53, -273.91, 127.22, 0.02004, 2, 6, 49.08, 85.73, 0.98198, 53, -271.44, 129.28, 0.01802, 2, 6, 53.65, 85.67, 0.98252, 53, -266.87, 129.23, 0.01748, 2, 6, 54.9, 92.06, 0.98759, 53, -265.63, 135.62, 0.01241, 2, 6, 58.51, 95.13, 0.99011, 53, -262.01, 138.69, 0.00989, 2, 6, 61.17, 91.76, 0.98752, 53, -259.35, 135.32, 0.01248, 2, 6, 62.72, 88.37, 0.98486, 53, -257.8, 131.93, 0.01514, 2, 6, 62.55, 85.78, 0.98288, 53, -257.97, 129.34, 0.01712, 2, 6, 64.84, 80.77, 0.97881, 53, -255.68, 124.33, 0.02119, 2, 6, 55.6, 65.18, 0.97064, 53, -264.92, 108.74, 0.02936, 2, 6, 57.25, 64.97, 0.97063, 53, -263.27, 108.53, 0.02937, 2, 6, 59.34, 67.31, 0.97137, 53, -261.19, 110.87, 0.02863, 2, 6, 60.79, 70.23, 0.97256, 53, -259.73, 113.79, 0.02744, 2, 6, 61.66, 74.46, 0.97467, 53, -258.86, 118.02, 0.02533, 2, 6, 60.67, 78.57, 0.97733, 53, -259.85, 122.13, 0.02267, 2, 6, 58.66, 81.69, 0.97972, 53, -261.86, 125.25, 0.02028, 2, 6, 55.5, 83.34, 0.98086, 53, -265.02, 126.9, 0.01914, 2, 6, 52.75, 83.17, 0.98049, 53, -267.77, 126.73, 0.01951, 2, 6, 51.4, 80.36, 0.97818, 53, -269.12, 123.92, 0.02182, 2, 6, 51.42, 77.23, 0.97581, 53, -269.1, 120.79, 0.02419, 2, 6, 53.78, 70.56, 0.97239, 53, -266.74, 114.12, 0.02761, 2, 6, 54.82, 67.42, 0.97126, 53, -265.7, 110.98, 0.02874, 2, 6, 52.65, 74.01, 0.97395, 53, -267.87, 117.57, 0.02605], "hull": 20, "edges": [8, 10, 18, 20, 20, 22, 2, 4, 34, 36, 36, 38, 14, 16, 16, 18, 2, 0, 0, 38, 30, 32, 32, 34, 28, 30, 26, 28, 22, 24, 24, 26, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 40, 4, 6, 6, 8, 10, 12, 12, 14, 60, 66, 66, 62], "width": 36, "height": 22}}, "hair": {"hair": {"type": "mesh", "uvs": [0.54776, 0.0123, 0.6196, 0.04005, 0.68878, 0.04902, 0.779, 0.0881, 0.84648, 0.14904, 0.89225, 0.23074, 0.91693, 0.31252, 0.92798, 0.40131, 0.92343, 0.47895, 0.93364, 0.52558, 0.98441, 0.57589, 1, 0.62788, 0.98718, 0.7098, 0.9317, 0.75762, 0.83558, 0.79654, 0.64833, 0.83405, 0.46653, 0.88493, 0.35078, 0.95499, 0.18693, 0.998, 0.07325, 0.9437, 0, 0.85155, 0.01113, 0.76336, 0, 0.67744, 0.07461, 0.60559, 0.03375, 0.57548, 0.01881, 0.53009, 0.08345, 0.47453, 0.02823, 0.39045, 0.0225, 0.32167, 0.04348, 0.25554, 0.09456, 0.20697, 0.13395, 0.15879, 0.21338, 0.09311, 0.29134, 0.04177, 0.37523, 0.00675, 0.46459, 1e-05, 0.49427, 0.17829, 0.44514, 0.17945, 0.39181, 0.19108, 0.34507, 0.21434, 0.29435, 0.21318, 0.26644, 0.22562, 0.2422, 0.24915, 0.56031, 0.25043, 0.64947, 0.31214, 0.61278, 0.28216, 0.69578, 0.4067, 0.71182, 0.4061, 0.75644, 0.37071, 0.79864, 0.44393, 0.82623, 0.52965, 0.84572, 0.61748, 0.84594, 0.69364, 0.56222, 0.14501, 0.61984, 0.09102, 0.56975, 0.06536, 0.4492, 0.04937, 0.33335, 0.12317, 0.21115, 0.14438, 0.27296, 0.11414, 0.1736, 0.19512, 0.15572, 0.26329, 0.17161, 0.34331, 0.20738, 0.42911, 0.23222, 0.51395, 0.20838, 0.59205, 0.19276, 0.69127, 0.18978, 0.80571, 0.21462, 0.91369, 0.09341, 0.5326, 0.26748, 0.32034, 0.30921, 0.40422, 0.342, 0.49704, 0.35293, 0.58671, 0.35789, 0.6754, 0.42247, 0.84219, 0.38231, 0.75782, 0.49148, 0.41532, 0.52526, 0.50306, 0.54712, 0.58404, 0.61368, 0.75083, 0.57891, 0.67274, 0.68959, 0.47606, 0.70946, 0.55222, 0.72237, 0.62839, 0.73728, 0.73348, 0.29945, 0.07032, 0.37041, 0.03624, 0.37397, 0.07596, 0.43494, 0.1141, 0.37454, 0.15246, 0.51045, 0.09331, 0.51296, 0.02716, 0.42539, 0.01441, 0.69877, 0.12391, 0.63455, 0.19366, 0.78205, 0.18282, 0.70674, 0.23669, 0.82647, 0.34178, 0.86434, 0.40899, 0.75861, 0.28351, 0.88993, 0.51265, 0.92246, 0.5898, 0.93571, 0.67865, 0.84776, 0.26363, 0.87306, 0.32326, 0.89394, 0.40916], "triangles": [18, 68, 17, 18, 19, 68, 17, 75, 16, 17, 68, 75, 68, 19, 67, 19, 20, 67, 68, 76, 75, 68, 67, 76, 76, 66, 74, 66, 76, 67, 20, 21, 67, 66, 65, 74, 67, 21, 66, 66, 22, 23, 66, 21, 22, 65, 73, 74, 66, 23, 65, 23, 69, 65, 23, 24, 69, 65, 64, 73, 65, 69, 64, 64, 72, 73, 24, 25, 69, 25, 26, 69, 69, 26, 64, 26, 63, 64, 26, 27, 62, 26, 62, 63, 62, 27, 28, 15, 85, 14, 14, 52, 13, 14, 85, 52, 13, 103, 12, 13, 52, 103, 85, 84, 52, 12, 103, 11, 84, 51, 52, 52, 51, 103, 51, 102, 103, 11, 102, 10, 11, 103, 102, 84, 50, 51, 102, 51, 50, 102, 9, 10, 102, 50, 101, 102, 101, 9, 101, 8, 9, 7, 106, 105, 49, 98, 99, 105, 104, 6, 104, 5, 6, 104, 4, 5, 98, 100, 104, 96, 4, 104, 100, 96, 104, 63, 62, 70, 42, 41, 70, 62, 61, 42, 42, 60, 41, 61, 60, 42, 61, 28, 29, 29, 30, 61, 61, 30, 60, 41, 60, 58, 40, 59, 57, 58, 32, 59, 15, 16, 80, 16, 75, 80, 75, 76, 80, 15, 80, 85, 76, 81, 80, 76, 74, 81, 85, 80, 84, 80, 81, 84, 74, 79, 81, 74, 73, 79, 81, 79, 84, 79, 83, 84, 73, 78, 79, 73, 72, 78, 79, 82, 83, 79, 78, 82, 47, 82, 46, 72, 77, 78, 82, 78, 46, 44, 77, 45, 77, 46, 78, 72, 71, 77, 44, 46, 77, 71, 39, 77, 39, 38, 77, 38, 37, 77, 77, 43, 45, 43, 37, 36, 43, 77, 37, 48, 46, 44, 48, 44, 100, 44, 97, 100, 44, 45, 97, 100, 97, 96, 45, 95, 97, 45, 43, 95, 43, 53, 95, 43, 36, 53, 95, 94, 97, 97, 94, 96, 38, 39, 90, 90, 39, 57, 39, 40, 57, 53, 54, 95, 95, 54, 94, 38, 90, 37, 94, 3, 96, 96, 3, 4, 90, 89, 37, 37, 89, 36, 36, 91, 53, 36, 89, 91, 57, 88, 90, 90, 88, 89, 53, 55, 54, 53, 91, 55, 54, 2, 94, 94, 2, 3, 59, 86, 57, 57, 86, 88, 89, 56, 91, 89, 88, 56, 91, 92, 55, 91, 56, 92, 32, 33, 86, 55, 1, 54, 54, 1, 2, 86, 87, 88, 56, 87, 93, 93, 87, 34, 87, 56, 88, 86, 33, 87, 92, 0, 55, 55, 0, 1, 56, 93, 92, 87, 33, 34, 93, 35, 92, 92, 35, 0, 93, 34, 35, 59, 32, 86, 58, 31, 32, 58, 59, 40, 60, 31, 58, 30, 31, 60, 40, 41, 58, 70, 41, 40, 70, 40, 39, 62, 28, 61, 71, 70, 39, 62, 42, 70, 63, 70, 71, 64, 63, 71, 64, 71, 72, 47, 46, 48, 48, 100, 98, 49, 82, 47, 47, 48, 49, 49, 83, 82, 98, 104, 105, 49, 48, 98, 99, 98, 105, 105, 6, 7, 106, 99, 105, 84, 83, 50, 8, 106, 7, 8, 99, 106, 83, 49, 50, 49, 99, 101, 50, 49, 101, 8, 101, 99], "vertices": [2, 6, 166.24, -13.38, 0.99192, 54, -113.28, 30.17, 0.00808, 2, 6, 155.61, -30.63, 0.99151, 54, -123.91, 12.93, 0.00849, 2, 6, 150.1, -48.06, 0.99282, 54, -129.43, -4.5, 0.00718, 2, 6, 135.62, -69.52, 0.99451, 54, -143.9, -25.96, 0.00549, 2, 6, 116.36, -84.09, 0.99552, 54, -163.16, -40.53, 0.00448, 2, 6, 92.56, -92.09, 0.99335, 54, -186.97, -48.54, 0.00665, 2, 6, 69.69, -94.65, 0.99141, 54, -209.83, -51.09, 0.00859, 3, 6, 45.58, -93.36, 0.7153, 50, -2.53, 22.17, 0.27574, 54, -233.94, -49.8, 0.00896, 4, 6, 25.14, -88.56, 0.4563, 50, 18.22, 18.96, 0.52927, 51, -23.03, 21.77, 0.00654, 54, -254.38, -45, 0.00789, 4, 6, 12.28, -89.02, 0.2977, 50, 31.01, 20.4, 0.54795, 51, -10.16, 21.72, 0.14837, 54, -267.25, -45.46, 0.00599, 5, 6, -3.4, -99.78, 0.10708, 50, 45.82, 32.33, 0.25465, 51, 5.93, 31.86, 0.62936, 48, -29.97, 204.46, 0, 54, -282.93, -56.22, 0.0089, 5, 6, -17.93, -101.37, 0, 50, 60.18, 35.04, 0.10241, 51, 20.51, 32.89, 0.88659, 48, -16.03, 208.87, 0, 54, -297.46, -57.81, 0.011, 6, 6, -39.14, -94.24, 2e-05, 50, 81.87, 29.56, 0.00289, 51, 41.42, 24.93, 0.98353, 48, 6.16, 206.02, 0, 49, -79.1, 193.44, 0, 54, -318.66, -50.68, 0.01355, 5, 6, -49.34, -77.7, 0.00011, 51, 50.97, 8, 0.98569, 48, 19.4, 191.78, 0, 49, -63.07, 182.43, 0, 54, -328.87, -34.14, 0.01421, 5, 6, -55.35, -51.07, 0.13414, 51, 55.93, -18.84, 0.84792, 48, 30.48, 166.85, 0.00155, 49, -46.83, 160.5, 0.00097, 54, -334.87, -7.52, 0.01541, 6, 6, -56.84, -1, 0.64301, 51, 55.47, -68.93, 0.22373, 47, 102.77, 114.95, 2e-05, 48, 41.73, 118.03, 0.07162, 49, -25.22, 115.31, 0.03983, 54, -336.37, 42.56, 0.02179, 6, 6, -62.14, 48.29, 0.42117, 51, 58.84, -118.39, 0.01247, 47, 114.37, 66.75, 0.00482, 48, 56.56, 70.73, 0.26295, 49, -0.46, 72.37, 0.26959, 54, -341.67, 91.85, 0.029, 5, 6, -75.54, 81.43, 0.13541, 51, 70.93, -152.02, 2e-05, 48, 76.17, 40.85, 0.15401, 49, 25.18, 47.46, 0.67449, 54, -355.06, 124.98, 0.03607, 3, 6, -79.56, 125.71, 0.0019, 49, 46.76, 8.58, 0.95867, 54, -359.08, 169.27, 0.03943, 4, 6, -59.97, 152.52, 2e-05, 48, 74.8, -31.92, 0.05945, 49, 39.67, -23.86, 0.90226, 54, -339.49, 196.08, 0.03827, 5, 6, -32.15, 167.12, 1e-05, 47, 99.92, -54.96, 0.01148, 48, 50.36, -51.67, 0.3323, 49, 20.13, -48.46, 0.61929, 54, -311.67, 210.68, 0.03691, 5, 6, -9.2, 160.13, 0, 47, 76.26, -50.98, 0.10874, 48, 26.49, -49.31, 0.55555, 49, -3.69, -51.34, 0.30527, 54, -288.72, 203.69, 0.03043, 5, 6, 14.15, 159, 0, 47, 52.96, -52.86, 0.31151, 48, 3.37, -52.75, 0.5606, 49, -25.51, -59.74, 0.10151, 54, -265.37, 202.56, 0.02638, 6, 6, 29.89, 136.39, 0, 46, 85.78, -34.54, 0.01239, 47, 34.45, -32.47, 0.77051, 48, -16.48, -33.66, 0.19072, 49, -49.04, -45.42, 0.01033, 54, -249.64, 179.95, 0.01606, 6, 6, 39.74, 145.53, 0, 46, 76.55, -44.32, 0.05689, 47, 25.85, -42.8, 0.88549, 48, -24.36, -44.55, 0.03889, 49, -54.36, -57.76, 0.00033, 54, -239.78, 189.09, 0.0184, 4, 46, 63.95, -46.91, 0.0936, 47, 13.43, -46.17, 0.87576, 48, -36.52, -48.74, 0.01261, 54, -227.03, 190.83, 0.01804, 4, 6, 64.34, 128, 0.08931, 46, 50.84, -28.47, 0.44122, 47, -0.8, -28.58, 0.45952, 54, -215.19, 171.56, 0.00994, 5, 6, 89.2, 138.33, 0.26448, 46, 26.73, -40.44, 0.70315, 47, -24.12, -42.02, 0.01963, 48, -74.27, -47.14, 0, 54, -190.32, 181.89, 0.01275, 5, 6, 107.75, 136.6, 0.45464, 46, 8.1, -39.95, 0.53349, 47, -42.74, -42.69, 4e-05, 48, -92.8, -49.07, 0, 54, -171.77, 180.16, 0.01183, 4, 6, 124.38, 128.1, 0.68132, 46, -9.07, -32.59, 0.30963, 48, -110.78, -43.99, 0, 54, -155.14, 171.66, 0.00904, 3, 6, 134.99, 112.65, 0.92061, 46, -20.68, -17.88, 0.07459, 54, -144.53, 156.21, 0.00479, 3, 6, 146.02, 100.24, 0.97363, 46, -32.52, -6.23, 0.02361, 54, -133.5, 143.79, 0.00276, 2, 6, 159.89, 76.68, 0.99541, 54, -119.63, 120.23, 0.00459, 2, 6, 170.01, 54.16, 0.99432, 54, -109.51, 97.72, 0.00568, 2, 6, 175.53, 30.88, 0.99357, 54, -103.99, 74.44, 0.00643, 2, 6, 173.28, 7.5, 0.99264, 54, -106.24, 51.06, 0.00736, 2, 6, 124.52, 8.16, 0.97368, 53, -196, 51.72, 0.02632, 2, 6, 126.44, 20.9, 0.97388, 53, -194.08, 64.45, 0.02612, 2, 6, 125.76, 35.2, 0.97427, 53, -194.76, 78.76, 0.02573, 2, 6, 121.69, 48.35, 0.97428, 53, -198.83, 91.91, 0.02572, 2, 6, 124.3, 61.38, 0.97956, 53, -196.23, 104.94, 0.02044, 2, 6, 122.25, 69.16, 0.985, 53, -198.27, 112.72, 0.015, 2, 6, 117.09, 76.52, 0.99, 53, -203.43, 120.08, 0.01, 2, 6, 102.35, -5.51, 0.96837, 53, -218.17, 38.04, 0.03163, 2, 6, 81.9, -25.64, 0.975, 53, -238.62, 17.91, 0.025, 2, 6, 91.53, -17.57, 0.97092, 53, -228.99, 25.98, 0.02908, 2, 6, 54.66, -33.18, 0.98124, 53, -265.86, 10.38, 0.01876, 3, 6, 54.09, -37.35, 0.98445, 53, -266.43, 6.21, 0.01482, 54, -225.43, 6.21, 0.00073, 3, 6, 61.48, -50.52, 0.98116, 53, -259.04, -6.96, 0.01388, 54, -218.04, -6.96, 0.00496, 2, 6, 40.1, -57.99, 0.77969, 50, 5.65, -12.67, 0.22031, 5, 6, 16.05, -61.11, 0.41188, 50, 29.39, -7.71, 0.57173, 51, -15.02, -6.02, 0.00852, 53, -304.47, -17.55, 0.00426, 54, -263.47, -17.55, 0.0036, 5, 6, -8.18, -62.05, 0.09412, 50, 53.48, -4.92, 0.05241, 51, 9.23, -6.03, 0.83989, 53, -328.71, -18.49, 0.00618, 54, -287.71, -18.49, 0.00739, 7, 6, -28.45, -58.55, 0.05664, 50, 73.96, -6.85, 0.0003, 51, 29.34, -10.32, 0.92737, 48, 2.65, 168.92, 3e-05, 49, -74.45, 156.47, 5e-05, 53, -348.97, -14.99, 0.00478, 54, -307.97, -14.99, 0.01083, 2, 6, 130.3, -10.92, 0.98217, 53, -190.22, 32.63, 0.01783, 2, 6, 142.05, -28.31, 0.99154, 53, -178.48, 15.25, 0.00846, 2, 6, 151.14, -16.58, 0.9902, 53, -169.38, 26.97, 0.0098, 2, 6, 160.85, 13.78, 0.98532, 53, -159.67, 57.34, 0.01468, 2, 6, 146.47, 47.12, 0.97152, 53, -174.06, 90.68, 0.02848, 2, 6, 146.36, 79.64, 0.97349, 53, -174.16, 123.2, 0.02651, 2, 6, 151.6, 62.28, 0.97228, 53, -168.92, 105.84, 0.02772, 2, 6, 134.56, 91.7, 0.97753, 53, -185.96, 135.26, 0.02247, 4, 6, 117.25, 99.49, 0.82518, 46, -3.86, -3.57, 0.16004, 53, -203.28, 143.05, 0.01467, 54, -162.28, 143.05, 0.0001, 4, 6, 95.24, 99.12, 0.60981, 46, 18.07, -1.73, 0.37977, 48, -87.86, -9.88, 0, 53, -225.28, 142.68, 0.01042, 4, 6, 70.81, 93.9, 0.42275, 46, 42.11, 5.12, 0.56599, 47, -11.6, 4.41, 0.00405, 53, -249.71, 137.46, 0.00721, 5, 6, 47.12, 91.45, 0.20511, 46, 65.58, 9.15, 0.05354, 47, 11.57, 9.89, 0.73419, 48, -42.16, 7.06, 5e-05, 53, -273.4, 135, 0.00711, 4, 6, 27.43, 101.24, 0.00512, 47, 32.36, 2.71, 0.98722, 53, -293.09, 144.8, 0.00367, 54, -252.09, 144.8, 0.00399, 5, 47, 58.94, -2.57, 0.00733, 48, 5.94, -2.18, 0.97395, 49, -34.01, -9.81, 0.00061, 53, -318.77, 153.46, 0.00538, 54, -277.77, 153.46, 0.01274, 4, 48, 36.85, -2.25, 0.92246, 49, -3.82, -3.16, 0.04831, 53, -349.07, 159.57, 0.00675, 54, -308.07, 159.57, 0.02248, 5, 6, -58.39, 114.63, 0.01075, 48, 65.84, 4.93, 0.0039, 49, 22.92, 10.16, 0.94642, 53, -378.91, 158.19, 0.00802, 54, -337.91, 158.19, 0.03092, 5, 6, 48.44, 128.14, 0, 46, 66.71, -27.55, 0.12389, 47, 14.98, -26.67, 0.8515, 48, -36.29, -29.19, 0.0135, 54, -231.08, 171.7, 0.0111, 4, 6, 97.01, 73.31, 0.99791, 46, 14.58, 23.91, 0.00207, 47, -40.24, 21.45, 1e-05, 48, -94.64, 15.09, 0, 4, 6, 72.82, 66.46, 0.85547, 46, 38.26, 32.36, 0.11895, 47, -17.13, 31.36, 0.02558, 48, -72.25, 26.54, 1e-05, 5, 6, 46.65, 62.33, 0.61493, 46, 64.1, 38.23, 0.12337, 47, 8.29, 38.82, 0.25171, 48, -47.39, 35.71, 0.00995, 54, -232.88, 105.89, 4e-05, 6, 6, 22.31, 63.69, 0.34601, 46, 88.48, 38.5, 0.03449, 47, 32.61, 40.61, 0.49091, 48, -23.25, 39.12, 0.12274, 49, -71.48, 24.15, 0.00123, 54, -257.22, 107.25, 0.00463, 6, 6, -1.51, 66.55, 0.31892, 46, 112.43, 37.25, 0.00123, 47, 56.59, 40.84, 0.27415, 48, 0.66, 40.98, 0.37064, 49, -48.55, 31.16, 0.02605, 54, -281.03, 110.11, 0.00901, 6, 6, -48.78, 57.66, 0.36536, 51, 45.12, -127.23, 0.00543, 47, 102.33, 55.73, 0.01167, 48, 45.29, 58.93, 0.30529, 49, -8.89, 58.39, 0.28764, 54, -328.31, 101.22, 0.02461, 6, 6, -24.53, 64.09, 0.3374, 51, 20.63, -132.71, 0.00059, 47, 79.1, 46.24, 0.07597, 48, 22.76, 47.88, 0.43414, 49, -28.48, 42.71, 0.13605, 54, -304.05, 107.65, 0.01585, 1, 6, 61.61, 19.94, 1, 2, 6, 36.75, 15.32, 0.9964, 54, -242.77, 58.87, 0.0036, 6, 6, 14.23, 13.45, 0.98543, 46, 93.18, 89.17, 0.00029, 47, 34.16, 91.47, 0.00381, 48, -25.14, 89.97, 0.00222, 49, -84.39, 73.37, 0.00014, 54, -265.3, 57.01, 0.00811, 6, 6, -33.14, 4.06, 0.73141, 51, 31.59, -73.06, 0.14394, 47, 79.92, 106.88, 0.00318, 48, 19.48, 108.44, 0.06983, 49, -44.86, 101.11, 0.03424, 54, -312.67, 47.61, 0.01739, 6, 6, -10.8, 9.39, 0.88486, 51, 9.06, -77.51, 0.02529, 47, 58.45, 98.72, 0.01466, 48, -1.39, 98.85, 0.0473, 49, -63.14, 87.2, 0.01481, 54, -290.32, 52.95, 0.01308, 2, 6, 36.49, -28.35, 0.9975, 54, -243.03, 15.21, 0.0025, 4, 6, 15.34, -29.92, 0.88779, 50, 32.5, -38.75, 0.08409, 51, -15.53, -37.21, 0.02171, 54, -264.19, 13.63, 0.00641, 6, 6, -5.5, -29.7, 0.58817, 50, 53.29, -37.37, 0.12662, 51, 5.29, -38.24, 0.27464, 48, -14.22, 136.15, 0.00012, 49, -83.79, 120.82, 0.00018, 54, -285.02, 13.85, 0.01027, 6, 6, -34.12, -28.65, 0.43611, 50, 81.91, -36.23, 0.00664, 51, 33.85, -40.42, 0.52776, 48, 14.05, 140.71, 0.00903, 49, -57.18, 131.42, 0.00508, 54, -313.65, 14.91, 0.01539, 2, 6, 162.06, 53.4, 0.98479, 53, -158.47, 96.96, 0.01521, 2, 6, 167.91, 33.5, 0.98957, 53, -152.62, 77.06, 0.01043, 2, 6, 157.18, 34.43, 0.98109, 53, -163.34, 77.99, 0.01891, 2, 6, 144.28, 20.48, 0.97511, 53, -176.24, 64.04, 0.02489, 2, 6, 136.81, 37.85, 0.97262, 53, -183.71, 81.41, 0.02738, 2, 6, 146.39, 0.02, 0.98251, 53, -174.13, 43.58, 0.01749, 2, 6, 163.87, -3.71, 0.99127, 53, -156.65, 39.85, 0.00873, 2, 6, 171.22, 18.29, 0.99643, 53, -149.3, 61.85, 0.00357, 2, 6, 129.73, -47.15, 0.98656, 53, -190.79, -3.59, 0.01344, 2, 6, 114.08, -27.32, 0.97641, 53, -206.44, 16.24, 0.02359, 2, 6, 110.29, -65.89, 0.98702, 53, -210.23, -22.33, 0.01298, 2, 6, 99.37, -43.94, 0.98064, 53, -221.15, -0.38, 0.01936, 2, 6, 66.01, -69.94, 0.99329, 53, -254.51, -26.38, 0.00671, 4, 6, 46.42, -76.58, 0.73953, 50, -2.07, 5.37, 0.25529, 53, -274.1, -33.02, 0.00315, 54, -233.11, -33.02, 0.00203, 2, 6, 84.58, -55.15, 0.98585, 53, -235.95, -11.59, 0.01415, 5, 6, 17.69, -78.34, 0.37847, 50, 26.43, 9.34, 0.5967, 51, -15.99, 11.27, 0.01675, 53, -302.83, -34.79, 0.00381, 54, -261.83, -34.79, 0.00427, 6, 6, -4.3, -83.14, 0.09987, 50, 47.99, 15.81, 0.27133, 51, 6.17, 15.2, 0.61357, 48, -25.84, 188.32, 0, 53, -324.82, -39.58, 0.00722, 54, -283.82, -39.58, 0.00801, 7, 6, -28.53, -82.41, 1e-05, 50, 72.2, 16.95, 0.00734, 51, 30.35, 13.53, 0.97393, 48, -1.94, 192.34, 0, 49, -84.02, 178.33, 0, 53, -349.05, -38.86, 0.00708, 54, -308.05, -38.86, 0.01165, 3, 6, 85.82, -79.08, 0.99199, 53, -234.7, -35.52, 0.00775, 54, -193.7, -35.52, 0.00026, 3, 6, 68.82, -82.83, 0.9922, 53, -251.7, -39.27, 0.00538, 54, -210.7, -39.27, 0.00242, 4, 6, 45.03, -84.21, 0.70999, 50, -1.28, 13.09, 0.28428, 53, -275.49, -40.65, 0.00211, 54, -234.49, -40.65, 0.00362], "hull": 36, "edges": [0, 70, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 32, 34, 36, 38, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 72, 86, 86, 90, 90, 88, 88, 92, 92, 94, 94, 96, 30, 32, 34, 36, 38, 40, 40, 42, 42, 44, 52, 54, 60, 62, 62, 64, 64, 66, 66, 68, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 186, 184, 10, 12, 12, 14], "width": 262, "height": 270}}, "head": {"head": {"type": "mesh", "uvs": [0.46452, 0.00609, 0.5618, 0.11396, 0.66103, 0.16916, 0.71772, 0.21465, 0.75573, 0.30012, 0.79217, 0.36493, 0.81575, 0.36298, 0.88373, 0.30646, 0.93715, 0.30362, 0.99079, 0.35958, 0.99454, 0.44977, 0.95846, 0.52944, 0.90771, 0.5894, 0.8754, 0.61851, 0.84338, 0.61926, 0.82539, 0.60733, 0.81536, 0.72319, 0.78911, 0.77944, 0.6451, 0.87853, 0.51286, 0.94229, 0.36821, 0.98976, 0.28425, 0.99028, 0.23603, 0.95254, 0.17742, 0.86384, 0.11687, 0.76101, 0.05632, 0.65818, 0.03909, 0.59362, 0.05208, 0.50365, 0.04835, 0.47124, 0.01236, 0.399, 0.01118, 0.33966, 0.01647, 0.29869, 0.02305, 0.1967, 0.05431, 0.10397, 0.1287, 0.06064, 0.21991, 0.06309, 0.30531, 0.02239, 0.38646, 0.00862, 0.28496, 0.34185, 0.47053, 0.24487, 0.26247, 0.32499, 0.26097, 0.28945, 0.45441, 0.20124, 0.50209, 0.19817, 0.57397, 0.21977, 0.64174, 0.25623, 0.51126, 0.23661, 0.56873, 0.24764, 0.63883, 0.26866, 0.35333, 0.24352, 0.37687, 0.2958, 0.30649, 0.26527, 0.33201, 0.32005, 0.42261, 0.26912, 0.40234, 0.22258, 0.15609, 0.32006, 0.04242, 0.31254, 0.04415, 0.35819, 0.15898, 0.37108, 0.17514, 0.35175, 0.11945, 0.36839, 0.07956, 0.31435, 0.07805, 0.36087, 0.11696, 0.31586, 0.1251, 0.19589, 0.23431, 0.17286, 0.34181, 0.1284, 0.45017, 0.11006, 0.36666, 0.40935, 0.37859, 0.38464, 0.39976, 0.36171, 0.42593, 0.3413, 0.46288, 0.33377, 0.50022, 0.33628, 0.53332, 0.34416, 0.56411, 0.35956, 0.54602, 0.38285, 0.52062, 0.40398, 0.48752, 0.41293, 0.45403, 0.41365, 0.41631, 0.41221, 0.38591, 0.41186, 0.35417, 0.40484, 0.36828, 0.37616, 0.38969, 0.34651, 0.41947, 0.32172, 0.45917, 0.30859, 0.50409, 0.30859, 0.54431, 0.31831, 0.59341, 0.33873, 0.19424, 0.44884, 0.19461, 0.45747, 0.17978, 0.46506, 0.15938, 0.47644, 0.13602, 0.49025, 0.11081, 0.49853, 0.08448, 0.49956, 0.07262, 0.48404, 0.0741, 0.45988, 0.08634, 0.44228, 0.10488, 0.42848, 0.13157, 0.42468, 0.15975, 0.42675, 0.182, 0.43366, 0.05916, 0.44803, 0.07436, 0.4218, 0.09958, 0.4042, 0.13258, 0.4011, 0.1615, 0.41042, 0.1856, 0.42456, 0.20525, 0.44561, 0.06508, 0.50712, 0.08659, 0.52852, 0.12033, 0.52438, 0.15407, 0.50126, 0.18448, 0.47986, 0.20747, 0.46502, 0.35983, 0.42627, 0.41359, 0.43765, 0.46513, 0.43731, 0.52224, 0.4273, 0.56482, 0.40772, 0.60227, 0.37528, 0.20823, 0.39171, 0.26761, 0.38135, 0.31806, 0.40943, 0.33909, 0.55411, 0.33858, 0.59231, 0.31166, 0.61338, 0.26157, 0.61452, 0.22004, 0.62816, 0.18706, 0.63839, 0.195, 0.55193, 0.22859, 0.46383, 0.22493, 0.42064, 0.32748, 0.47424, 0.18603, 0.77798, 0.17911, 0.75223, 0.17911, 0.70916, 0.19188, 0.6849, 0.2105, 0.67252, 0.23976, 0.674, 0.26689, 0.65668, 0.29669, 0.65767, 0.34244, 0.67599, 0.38447, 0.69777, 0.42171, 0.7151, 0.1982, 0.79113, 0.21256, 0.81341, 0.23703, 0.8243, 0.28438, 0.81886, 0.33758, 0.80153, 0.38174, 0.77331, 0.41632, 0.73815, 0.44771, 0.71439, 0.44931, 0.74756, 0.19754, 0.78222, 0.21561, 0.76347, 0.21984, 0.74199, 0.23245, 0.72605, 0.26113, 0.72234, 0.29031, 0.71306, 0.32005, 0.71562, 0.3597, 0.72862, 0.39862, 0.73188, 0.42027, 0.72693, 0.23403, 0.75592, 0.27506, 0.74673, 0.32065, 0.73647, 0.23278, 0.78097, 0.27558, 0.77432, 0.32964, 0.75906, 0.09149, 0.59363, 0.12522, 0.67512, 0.26087, 0.8566, 0.34807, 0.83236, 0.48384, 0.88822, 0.61004, 0.8179, 0.69866, 0.72699, 0.70445, 0.57856, 0.81228, 0.45687, 0.69619, 0.44131, 0.57329, 0.50898, 0.24155, 0.89536, 0.42361, 0.83844, 0.3586, 0.91855, 0.27479, 0.93225, 0.45443, 0.52096, 0.57389, 0.6593, 0.43724, 0.62229, 0.82111, 0.54637, 0.88965, 0.54318, 0.9419, 0.50209, 0.96928, 0.43979, 0.95407, 0.36828, 0.91299, 0.3343, 0.87952, 0.36545, 0.8605, 0.43554, 0.86887, 0.48085, 0.84452, 0.52262, 0.91755, 0.48934, 0.93581, 0.44403, 0.9206, 0.4143, 0.67762, 0.33235], "triangles": [30, 56, 57, 62, 61, 63, 62, 57, 61, 57, 56, 61, 64, 63, 61, 61, 56, 32, 107, 60, 58, 58, 59, 123, 59, 58, 55, 62, 63, 60, 58, 60, 55, 60, 63, 55, 59, 55, 40, 64, 55, 63, 83, 38, 52, 123, 59, 40, 124, 40, 38, 52, 50, 84, 38, 40, 52, 55, 41, 40, 40, 41, 52, 41, 55, 64, 41, 51, 52, 52, 51, 50, 41, 65, 51, 51, 65, 49, 85, 50, 53, 86, 53, 39, 51, 49, 50, 50, 49, 53, 49, 54, 53, 53, 54, 39, 54, 42, 39, 39, 42, 43, 49, 66, 54, 54, 66, 42, 42, 67, 43, 48, 89, 47, 87, 46, 47, 87, 39, 46, 48, 45, 4, 48, 47, 45, 47, 44, 45, 45, 44, 2, 47, 46, 44, 39, 43, 46, 46, 43, 44, 43, 1, 44, 22, 186, 21, 20, 21, 185, 21, 186, 185, 19, 185, 176, 19, 20, 185, 22, 23, 183, 22, 183, 186, 149, 23, 148, 183, 23, 174, 19, 176, 18, 185, 186, 174, 186, 183, 174, 174, 175, 185, 185, 184, 176, 185, 175, 184, 174, 23, 149, 176, 177, 18, 177, 176, 155, 18, 178, 17, 18, 177, 178, 23, 147, 148, 147, 136, 156, 147, 23, 136, 136, 24, 137, 136, 23, 24, 174, 150, 175, 174, 149, 150, 175, 152, 184, 184, 152, 155, 155, 152, 153, 176, 184, 155, 155, 153, 165, 150, 151, 175, 175, 151, 152, 148, 169, 149, 150, 169, 170, 150, 149, 169, 150, 170, 151, 155, 188, 177, 177, 188, 178, 148, 147, 169, 170, 171, 151, 151, 171, 152, 147, 156, 169, 156, 157, 169, 156, 136, 157, 169, 166, 170, 169, 157, 166, 17, 178, 16, 136, 137, 157, 166, 167, 170, 171, 167, 168, 171, 170, 167, 155, 165, 154, 165, 146, 154, 171, 163, 152, 152, 164, 153, 152, 163, 164, 157, 158, 166, 157, 137, 158, 24, 138, 137, 24, 173, 138, 24, 25, 173, 171, 168, 163, 158, 159, 166, 166, 160, 167, 166, 159, 160, 137, 138, 158, 155, 154, 188, 168, 161, 162, 168, 167, 161, 167, 160, 161, 158, 138, 159, 153, 164, 165, 168, 162, 163, 146, 165, 164, 164, 163, 145, 145, 163, 144, 16, 178, 15, 178, 188, 179, 146, 164, 145, 138, 139, 159, 139, 140, 159, 159, 141, 160, 159, 140, 141, 15, 178, 179, 160, 141, 161, 163, 162, 144, 144, 162, 143, 154, 146, 189, 146, 145, 189, 154, 189, 188, 141, 142, 161, 162, 161, 143, 161, 142, 143, 138, 173, 139, 145, 144, 189, 173, 131, 139, 139, 131, 140, 188, 189, 182, 189, 187, 182, 182, 187, 120, 187, 119, 120, 143, 128, 144, 128, 127, 144, 144, 127, 189, 25, 172, 173, 173, 172, 131, 142, 141, 130, 141, 140, 130, 140, 131, 130, 188, 182, 179, 25, 26, 172, 128, 143, 129, 130, 129, 142, 143, 142, 129, 131, 172, 132, 132, 172, 113, 131, 132, 130, 132, 113, 114, 130, 132, 129, 127, 126, 189, 189, 126, 187, 14, 15, 13, 12, 13, 191, 128, 129, 126, 126, 129, 132, 127, 128, 126, 133, 126, 132, 179, 190, 15, 13, 15, 191, 191, 190, 199, 191, 15, 190, 26, 112, 172, 172, 112, 113, 112, 27, 111, 112, 26, 27, 135, 126, 133, 12, 191, 11, 182, 181, 179, 179, 180, 190, 179, 181, 180, 187, 135, 118, 118, 135, 117, 135, 187, 126, 133, 115, 116, 114, 115, 132, 133, 132, 115, 190, 180, 199, 191, 192, 11, 191, 200, 192, 191, 199, 200, 11, 192, 10, 111, 96, 112, 112, 95, 113, 112, 96, 95, 113, 94, 114, 113, 95, 94, 199, 198, 200, 199, 180, 198, 187, 118, 119, 120, 121, 182, 181, 121, 122, 181, 182, 121, 96, 111, 97, 111, 27, 97, 27, 28, 97, 200, 201, 192, 192, 193, 10, 192, 201, 193, 114, 93, 115, 114, 94, 93, 96, 97, 95, 99, 94, 95, 95, 97, 98, 95, 98, 99, 99, 100, 94, 100, 101, 94, 94, 101, 93, 200, 198, 201, 97, 28, 98, 180, 197, 198, 201, 197, 202, 201, 198, 197, 93, 92, 115, 115, 91, 116, 115, 92, 91, 93, 102, 92, 93, 101, 102, 133, 125, 135, 117, 125, 82, 117, 135, 125, 28, 104, 98, 28, 29, 104, 92, 90, 91, 92, 103, 90, 92, 102, 103, 133, 116, 110, 116, 91, 110, 110, 134, 133, 125, 134, 124, 125, 133, 134, 98, 104, 99, 91, 90, 110, 181, 5, 180, 180, 6, 197, 180, 5, 6, 193, 9, 10, 90, 103, 110, 104, 105, 99, 104, 29, 105, 103, 109, 110, 110, 109, 134, 201, 202, 193, 99, 105, 100, 122, 203, 181, 5, 203, 4, 5, 181, 203, 202, 194, 193, 193, 194, 9, 118, 79, 119, 118, 80, 79, 118, 117, 81, 117, 68, 81, 118, 81, 80, 119, 78, 120, 119, 79, 78, 197, 196, 202, 197, 6, 196, 103, 102, 109, 105, 106, 100, 100, 106, 101, 78, 77, 120, 120, 77, 121, 102, 108, 109, 102, 101, 108, 117, 82, 68, 101, 107, 108, 101, 106, 107, 109, 123, 134, 109, 108, 123, 29, 57, 105, 105, 62, 106, 105, 57, 62, 134, 123, 124, 202, 196, 194, 194, 196, 195, 79, 70, 71, 79, 72, 78, 79, 71, 72, 78, 73, 77, 78, 72, 73, 80, 69, 70, 79, 80, 70, 68, 69, 81, 80, 81, 69, 107, 58, 108, 108, 58, 123, 83, 82, 125, 68, 82, 69, 77, 76, 121, 76, 75, 121, 121, 75, 122, 125, 124, 38, 83, 125, 38, 82, 83, 69, 106, 60, 107, 106, 62, 60, 77, 74, 76, 77, 73, 74, 29, 30, 57, 124, 123, 40, 69, 83, 70, 76, 74, 75, 83, 84, 70, 83, 52, 84, 75, 89, 122, 122, 89, 203, 9, 194, 8, 196, 7, 195, 196, 6, 7, 70, 84, 71, 194, 195, 8, 89, 75, 88, 84, 85, 71, 84, 50, 85, 75, 74, 88, 88, 74, 87, 71, 85, 72, 30, 31, 56, 89, 48, 203, 74, 73, 87, 73, 72, 87, 195, 7, 8, 85, 86, 72, 72, 86, 87, 203, 48, 4, 85, 53, 86, 89, 88, 47, 88, 87, 47, 31, 32, 56, 64, 61, 32, 86, 39, 87, 45, 3, 4, 64, 65, 41, 45, 2, 3, 65, 66, 49, 44, 1, 2, 66, 67, 42, 43, 67, 1, 32, 33, 64, 65, 34, 35, 65, 64, 34, 64, 33, 34, 65, 35, 66, 35, 36, 66, 66, 37, 67, 66, 36, 37, 67, 0, 1, 67, 37, 0], "vertices": [2, 6, 124.56, 7.86, 0.97368, 53, -195.96, 51.42, 0.02632, 2, 6, 103.47, -4.34, 0.96837, 53, -217.05, 39.22, 0.03163, 2, 6, 91.3, -18.42, 0.97091, 53, -229.22, 25.13, 0.02909, 2, 6, 81.97, -26.05, 0.97504, 53, -238.55, 17.5, 0.02496, 2, 6, 66.35, -29.53, 0.9772, 53, -254.17, 14.03, 0.0228, 2, 6, 54.3, -33.37, 0.9808, 53, -266.22, 10.19, 0.0192, 3, 6, 53.97, -37.17, 0.98377, 53, -266.55, 6.39, 0.0155, 54, -225.55, 6.39, 0.00073, 3, 6, 61.71, -49.64, 0.98202, 53, -258.81, -6.08, 0.01297, 54, -217.81, -6.08, 0.00501, 3, 6, 60.71, -58.19, 0.98061, 53, -259.81, -14.63, 0.0112, 54, -218.81, -14.63, 0.00819, 3, 6, 49.68, -65.03, 0.97766, 53, -270.84, -21.47, 0.01124, 54, -229.84, -21.47, 0.0111, 3, 6, 34.21, -62.92, 0.97809, 53, -286.31, -19.37, 0.01104, 54, -245.31, -19.37, 0.01087, 3, 6, 21.64, -54.82, 0.98122, 53, -298.88, -11.26, 0.01066, 54, -257.88, -11.26, 0.00812, 3, 6, 12.83, -44.98, 0.98341, 53, -307.69, -1.42, 0.01189, 54, -266.69, -1.42, 0.0047, 3, 6, 8.77, -38.99, 0.98465, 53, -311.75, 4.57, 0.01277, 54, -270.75, 4.57, 0.00258, 3, 6, 9.53, -33.89, 0.98572, 53, -310.99, 9.67, 0.01359, 54, -269.99, 9.67, 0.00069, 2, 6, 12.07, -31.39, 0.98594, 53, -308.45, 12.17, 0.01406, 2, 6, -7.4, -26.34, 0.98764, 53, -327.92, 17.22, 0.01236, 2, 6, -16.25, -20.49, 0.98485, 53, -336.77, 23.06, 0.01515, 2, 6, -29.13, 5.3, 0.9748, 53, -349.65, 48.86, 0.02519, 2, 6, -36.32, 28.18, 0.96939, 53, -356.84, 71.74, 0.03061, 2, 6, -40.38, 52.54, 0.9641, 53, -360.9, 96.1, 0.0359, 2, 6, -38.13, 65.87, 0.96702, 53, -358.65, 109.43, 0.03298, 2, 6, -30.36, 72.39, 0.9712, 53, -350.88, 115.95, 0.0288, 2, 6, -13.62, 79.03, 0.97585, 53, -334.14, 122.59, 0.02415, 2, 6, 5.59, 85.56, 0.981, 53, -314.93, 129.12, 0.019, 2, 6, 24.8, 92.09, 0.98538, 53, -295.72, 135.64, 0.01462, 2, 6, 36.28, 92.89, 0.98635, 53, -284.24, 136.45, 0.01365, 2, 6, 51.25, 88.14, 0.98945, 53, -269.28, 131.7, 0.01055, 2, 6, 56.87, 87.76, 0.98941, 53, -263.65, 131.32, 0.01059, 2, 6, 70.18, 91.31, 0.98883, 53, -250.34, 134.87, 0.01117, 2, 6, 80.33, 89.72, 0.98893, 53, -240.19, 133.28, 0.01107, 2, 6, 87.16, 87.66, 0.98913, 53, -233.36, 131.22, 0.01087, 2, 6, 104.36, 83.57, 0.98968, 53, -216.16, 127.13, 0.01032, 2, 6, 119.29, 75.84, 0.99, 53, -201.23, 119.4, 0.01, 2, 6, 124.6, 62.75, 0.97956, 53, -195.92, 106.31, 0.02044, 2, 6, 121.65, 48.36, 0.97428, 53, -198.87, 91.92, 0.02572, 2, 6, 126.21, 33.6, 0.97427, 53, -194.31, 77.16, 0.02573, 2, 6, 126.3, 20.32, 0.97388, 53, -194.23, 63.88, 0.02612, 4, 40, 48.1, 29.86, 0.00983, 41, 38.04, 5.64, 0.00168, 42, 14.53, 4.91, 0.98209, 54, -207.19, 89.93, 0.0064, 4, 40, 24.25, 5.26, 0.27677, 41, 3.83, 3.63, 0.72274, 42, -19.73, 4.63, 0.0003, 54, -195.82, 57.61, 0.00019, 3, 40, 52.4, 28.1, 0.00098, 42, 16.29, 0.61, 0.99201, 54, -203.69, 92.99, 0.00701, 3, 41, 37.54, -4.2, 0.0144, 42, 13.54, -4.89, 0.97934, 54, -197.59, 92.17, 0.00626, 3, 40, 28.89, -1.23, 0.07527, 41, 2.86, -4.29, 0.92471, 54, -187.94, 58.86, 2e-05, 3, 40, 21.69, -3.93, 0.85364, 41, -4.27, -1.41, 0.14501, 54, -188.74, 51.21, 0.00135, 2, 40, 9.53, -3.65, 0.9947, 54, -194.42, 40.45, 0.0053, 3, 40, -2.73, -0.71, 0.99033, 42, -42.93, 19.65, 0.00055, 54, -202.52, 30.79, 0.00912, 4, 40, 18.37, 2.02, 0.92997, 41, -2.69, 5.21, 0.06795, 42, -26.17, 6.54, 0.00046, 54, -195.55, 50.9, 0.00162, 4, 40, 8.96, 1.21, 0.98901, 41, -10.17, 10.98, 0.00553, 42, -33.35, 12.67, 0.00077, 54, -199.03, 42.12, 0.00469, 4, 40, -2.89, 1.48, 0.98677, 41, -18.73, 19.19, 0.00193, 42, -41.48, 21.3, 0.00267, 54, -204.56, 31.63, 0.00863, 3, 41, 20.7, -4.84, 0.8448, 42, -3.32, -4.68, 0.15337, 54, -192.33, 76.15, 0.00183, 4, 40, 36.19, 18.01, 0.02787, 41, 21.24, 4.95, 0.7392, 42, -2.28, 5.07, 0.23092, 54, -201.89, 73.98, 0.00202, 3, 41, 29.12, -4.76, 0.1163, 42, 5.1, -5.02, 0.87966, 54, -194.73, 84.23, 0.00404, 4, 40, 41.92, 24.09, 0.01617, 41, 29.57, 5.56, 0.0754, 42, 6.08, 5.26, 0.90424, 54, -204.78, 81.82, 0.00419, 4, 40, 30.45, 11.48, 0.05833, 41, 12.6, 4.02, 0.93352, 42, -10.95, 4.58, 0.00763, 54, -198.62, 65.93, 0.00052, 3, 41, 12.01, -4.64, 0.99456, 42, -11.98, -4.04, 0.00509, 54, -190.13, 67.75, 0.00035, 3, 45, 7.53, 3.94, 0.92808, 44, 18.26, 3.71, 0.06138, 53, -240.89, 109.72, 0.01054, 3, 45, -10.81, 3.69, 0.00119, 44, -0.08, 4.03, 0.73732, 43, 9.54, 2.43, 0.26149, 3, 45, -9.87, -4.16, 0.01605, 44, 0.62, -3.84, 0.22729, 43, 3.57, -2.75, 0.75665, 4, 45, 8.74, -4.81, 0.93124, 44, 19.2, -5.08, 0.01383, 43, 13.45, -18.53, 0.04427, 53, -249.66, 110.78, 0.01066, 3, 45, 11.05, -1.26, 0.97977, 43, 17.69, -18.46, 0.0077, 53, -246.81, 107.64, 0.01253, 4, 45, 2.36, -4.89, 0.68806, 44, 12.82, -4.96, 0.21856, 43, 9.81, -13.29, 0.086, 53, -248.1, 116.97, 0.00738, 3, 45, -4.83, 3.89, 0.05259, 44, 5.9, 4.03, 0.94323, 53, -237.78, 121.68, 0.00419, 4, 45, -4.39, -4.16, 0.12984, 44, 6.09, -4.02, 0.68115, 43, 6.64, -7.29, 0.18472, 53, -245.67, 123.31, 0.00429, 3, 45, 1.19, 4.13, 0.56856, 44, 11.93, 4.1, 0.42431, 53, -239.08, 115.8, 0.00713, 2, 6, 101.66, 67.36, 0.97429, 53, -218.86, 110.92, 0.02571, 2, 6, 102.54, 49.35, 0.96939, 53, -217.98, 92.91, 0.03061, 2, 6, 107.13, 30.98, 0.96785, 53, -213.39, 74.54, 0.03215, 2, 6, 107.24, 13.25, 0.96781, 53, -213.28, 56.81, 0.03219, 2, 6, 58.56, 35.44, 0.96438, 53, -261.96, 78.99, 0.03562, 2, 6, 62.44, 32.8, 0.96436, 53, -258.08, 76.36, 0.03564, 2, 6, 65.76, 28.76, 0.96416, 53, -254.76, 72.32, 0.03584, 2, 6, 68.51, 24, 0.96389, 53, -252.01, 67.56, 0.03611, 2, 6, 68.77, 17.92, 0.96339, 53, -251.76, 61.48, 0.03661, 2, 6, 67.3, 12.07, 0.96293, 53, -253.22, 55.63, 0.03707, 2, 6, 65.04, 7.06, 0.96363, 53, -255.49, 50.62, 0.03637, 2, 6, 61.56, 2.64, 0.96514, 53, -258.97, 46.19, 0.03486, 2, 6, 58.09, 6.2, 0.96369, 53, -262.43, 49.76, 0.03631, 2, 6, 55.2, 10.86, 0.9626, 53, -265.32, 54.42, 0.0374, 2, 6, 54.59, 16.38, 0.96268, 53, -265.93, 59.94, 0.03732, 2, 6, 55.4, 21.71, 0.96301, 53, -265.12, 65.27, 0.03699, 2, 6, 56.69, 27.65, 0.96346, 53, -263.83, 71.21, 0.03654, 2, 6, 57.6, 32.46, 0.96402, 53, -262.92, 76.02, 0.03598, 2, 6, 59.68, 37.28, 0.96464, 53, -260.84, 80.84, 0.03536, 2, 6, 64.17, 34.19, 0.96461, 53, -256.35, 77.75, 0.03539, 2, 6, 68.63, 29.9, 0.96464, 53, -251.89, 73.46, 0.03536, 2, 6, 72.03, 24.44, 0.96448, 53, -248.49, 68, 0.03552, 2, 6, 73.16, 17.75, 0.96383, 53, -247.36, 61.31, 0.03617, 2, 6, 71.91, 10.63, 0.96362, 53, -248.61, 54.19, 0.03638, 2, 6, 69.13, 4.54, 0.9645, 53, -251.39, 48.1, 0.0355, 2, 6, 64.29, -2.63, 0.96656, 53, -256.23, 40.93, 0.03344, 2, 6, 56.63, 63.96, 0.97022, 53, -263.89, 107.52, 0.02978, 2, 6, 55.15, 64.16, 0.97022, 53, -265.37, 107.72, 0.02978, 2, 6, 54.27, 66.74, 0.97148, 53, -266.25, 110.3, 0.02852, 2, 6, 52.9, 70.31, 0.97322, 53, -267.62, 113.87, 0.02678, 2, 6, 51.2, 74.43, 0.97522, 53, -269.33, 117.99, 0.02478, 2, 6, 50.49, 78.67, 0.9778, 53, -270.04, 122.23, 0.0222, 2, 6, 51.04, 82.88, 0.98046, 53, -269.48, 126.44, 0.01954, 2, 6, 54.02, 84.3, 0.98155, 53, -266.5, 127.86, 0.01845, 2, 6, 58.09, 83.34, 0.98124, 53, -262.43, 126.9, 0.01876, 2, 6, 60.75, 80.87, 0.97985, 53, -259.77, 124.43, 0.02015, 2, 6, 62.59, 77.52, 0.97784, 53, -257.93, 121.08, 0.02216, 2, 6, 62.49, 73.17, 0.97539, 53, -258.03, 116.73, 0.02461, 2, 6, 61.35, 68.77, 0.97303, 53, -259.17, 112.33, 0.02697, 2, 6, 59.56, 65.44, 0.9712, 53, -260.96, 109, 0.0288, 2, 6, 60.53, 85.35, 0.98268, 53, -259.99, 128.91, 0.01732, 2, 6, 64.57, 82.16, 0.98087, 53, -255.95, 125.72, 0.01913, 2, 6, 66.87, 77.64, 0.9781, 53, -253.65, 121.19, 0.0219, 2, 6, 66.48, 72.31, 0.97514, 53, -254.04, 115.87, 0.02486, 2, 6, 64.09, 68, 0.97278, 53, -256.43, 111.56, 0.02722, 2, 6, 61.01, 64.6, 0.97085, 53, -259.51, 108.16, 0.02915, 2, 6, 56.88, 62.12, 0.96936, 53, -263.65, 105.67, 0.03064, 2, 6, 50.29, 86.18, 0.9825, 53, -270.23, 129.74, 0.0175, 2, 6, 46.05, 83.41, 0.98047, 53, -274.47, 126.97, 0.01953, 2, 6, 45.82, 77.94, 0.977, 53, -274.71, 121.5, 0.023, 2, 6, 48.82, 71.89, 0.97375, 53, -271.7, 115.45, 0.02625, 2, 6, 51.62, 66.43, 0.97113, 53, -268.9, 109.99, 0.02887, 2, 6, 53.51, 62.34, 0.96926, 53, -267.02, 105.9, 0.03074, 2, 6, 55.87, 37.02, 0.96437, 53, -264.65, 80.58, 0.03563, 2, 6, 52.44, 28.84, 0.96343, 53, -268.09, 72.4, 0.03657, 2, 6, 51.06, 20.66, 0.96273, 53, -269.46, 64.21, 0.03727, 2, 6, 51.18, 11.3, 0.96216, 53, -269.34, 54.86, 0.03784, 2, 6, 53.33, 3.96, 0.96405, 53, -267.19, 47.52, 0.03595, 2, 6, 57.81, -2.95, 0.96654, 53, -262.71, 40.61, 0.03346, 2, 6, 65.98, 60.03, 0.96735, 53, -254.54, 103.59, 0.03265, 2, 6, 66.09, 50.31, 0.96607, 53, -254.43, 93.87, 0.03393, 2, 6, 59.9, 43.14, 0.96498, 53, -260.62, 86.7, 0.03502, 2, 6, 34.66, 44.13, 0.96419, 53, -285.86, 87.69, 0.03581, 2, 6, 28.17, 45.36, 0.96408, 53, -292.35, 88.92, 0.03592, 2, 6, 25.33, 50.26, 0.9646, 53, -295.19, 93.82, 0.0354, 2, 6, 26.53, 58.23, 0.96495, 53, -293.99, 101.79, 0.03505, 2, 6, 25.36, 65.23, 0.96627, 53, -295.16, 108.78, 0.03373, 2, 6, 24.53, 70.76, 0.9692, 53, -295.99, 114.32, 0.0308, 2, 6, 39.04, 66.92, 0.96815, 53, -281.48, 110.48, 0.03185, 2, 6, 53.12, 58.96, 0.96684, 53, -267.4, 102.52, 0.03316, 2, 6, 60.58, 58.25, 0.96698, 53, -259.94, 101.81, 0.03302, 2, 6, 48.6, 43.59, 0.9647, 53, -271.93, 87.15, 0.0353, 2, 6, 0.78, 75.1, 0.96924, 53, -319.75, 118.66, 0.03076, 2, 6, 5.35, 75.43, 0.96937, 53, -315.17, 118.98, 0.03063, 2, 6, 12.69, 74.14, 0.96877, 53, -307.83, 117.7, 0.03123, 2, 6, 16.47, 71.39, 0.9676, 53, -304.05, 114.95, 0.0324, 2, 6, 18.06, 68.07, 0.96611, 53, -302.46, 111.62, 0.03389, 2, 6, 17, 63.47, 0.96376, 53, -303.52, 107.03, 0.03624, 2, 6, 19.2, 58.65, 0.96299, 53, -301.33, 102.21, 0.03701, 2, 6, 18.2, 53.95, 0.9626, 53, -302.32, 97.51, 0.0374, 2, 6, 13.8, 47.25, 0.96217, 53, -306.72, 90.81, 0.03783, 2, 6, 8.92, 41.23, 0.9621, 53, -311.6, 84.79, 0.0379, 2, 6, 4.93, 35.84, 0.96202, 53, -315.59, 79.4, 0.03798, 2, 6, -1.8, 73.56, 0.96846, 53, -322.33, 117.12, 0.03154, 2, 6, -6, 71.95, 0.96766, 53, -326.52, 115.51, 0.03234, 2, 6, -8.54, 68.39, 0.96694, 53, -329.06, 111.95, 0.03306, 2, 6, -8.93, 60.72, 0.96247, 53, -329.45, 104.28, 0.03753, 2, 6, -7.45, 51.77, 0.96125, 53, -327.97, 95.33, 0.03875, 2, 6, -3.87, 43.92, 0.96162, 53, -324.39, 87.48, 0.03838, 2, 6, 1.16, 37.39, 0.96205, 53, -319.37, 80.95, 0.03795, 2, 6, 4.33, 31.7, 0.96146, 53, -316.19, 75.26, 0.03854, 2, 6, -1.36, 32.44, 0.96136, 53, -321.89, 76, 0.03864, 2, 6, -0.27, 73.4, 0.96837, 53, -320.79, 116.96, 0.03163, 2, 6, 2.42, 69.97, 0.96843, 53, -318.1, 113.53, 0.03157, 2, 6, 5.97, 68.66, 0.96807, 53, -314.55, 112.22, 0.03193, 2, 6, 8.33, 66.18, 0.96726, 53, -312.19, 109.74, 0.03274, 2, 6, 8.17, 61.53, 0.96565, 53, -312.35, 105.08, 0.03435, 2, 6, 8.94, 56.62, 0.96484, 53, -311.58, 100.18, 0.03516, 2, 6, 7.67, 51.98, 0.9642, 53, -312.85, 95.54, 0.0358, 2, 6, 4.35, 46.08, 0.96332, 53, -316.17, 89.64, 0.03668, 2, 6, 2.72, 40.01, 0.96192, 53, -317.8, 83.57, 0.03808, 2, 6, 2.96, 36.43, 0.96203, 53, -317.56, 79.99, 0.03797, 2, 6, 3.2, 66.83, 0.96737, 53, -317.32, 110.39, 0.03263, 2, 6, 3.62, 60.05, 0.96509, 53, -316.9, 103.6, 0.03491, 2, 6, 4.1, 52.51, 0.96414, 53, -316.42, 96.07, 0.03586, 2, 6, -1.04, 67.77, 0.96454, 53, -321.56, 111.33, 0.03546, 2, 6, -1.09, 60.79, 0.96139, 53, -321.61, 104.35, 0.03861, 2, 6, 0, 51.76, 0.96083, 53, -320.52, 95.32, 0.03917, 2, 6, 34.82, 84.58, 0.9775, 53, -285.7, 128.14, 0.0225, 2, 6, 19.99, 81.67, 0.97504, 53, -300.53, 125.23, 0.02496, 2, 6, -14.7, 65.58, 0.96678, 53, -335.22, 109.14, 0.03322, 2, 6, -13, 51.03, 0.96323, 53, -333.52, 94.59, 0.03677, 2, 6, -26.29, 31.17, 0.96163, 53, -346.82, 74.73, 0.03837, 2, 6, -17.82, 9.05, 0.9675, 53, -338.34, 52.61, 0.0325, 2, 6, -4.8, -7.72, 0.97319, 53, -325.32, 35.84, 0.02681, 2, 6, 20.33, -13.07, 0.97213, 53, -300.19, 30.49, 0.02787, 2, 6, 38.07, -33.81, 0.98397, 53, -282.45, 9.75, 0.01603, 2, 6, 43.95, -15.87, 0.96948, 53, -276.57, 27.69, 0.03052, 2, 6, 35.84, 5.65, 0.96311, 53, -284.68, 49.21, 0.03689, 2, 6, -20.77, 69.8, 0.96844, 53, -341.29, 113.36, 0.03156, 2, 6, -16.14, 39.23, 0.96214, 53, -336.66, 82.79, 0.03786, 2, 6, -27.98, 51.93, 0.96096, 53, -348.5, 95.49, 0.03904, 2, 6, -27.98, 65.63, 0.96529, 53, -348.5, 109.19, 0.03471, 2, 6, 37.1, 24.85, 0.96185, 53, -283.42, 68.41, 0.03815, 2, 6, 10.21, 10.04, 0.96329, 53, -310.31, 53.6, 0.03671, 2, 6, 20.32, 30.61, 0.96189, 53, -300.21, 74.17, 0.03811, 2, 6, 22.57, -32.54, 0.98503, 53, -297.95, 11.02, 0.01497, 3, 6, 21.21, -43.5, 0.98303, 53, -299.31, 0.06, 0.01301, 54, -258.31, 0.06, 0.00396, 3, 6, 26.76, -53.01, 0.98108, 53, -293.76, -9.45, 0.01155, 54, -252.76, -9.45, 0.00737, 3, 6, 36.61, -59.22, 0.97943, 53, -283.91, -15.66, 0.01114, 54, -242.91, -15.66, 0.00944, 3, 6, 49.22, -58.94, 0.97971, 53, -271.3, -15.38, 0.01141, 54, -230.3, -15.38, 0.00888, 3, 6, 56.16, -53.44, 0.98101, 53, -264.37, -9.89, 0.01234, 54, -223.37, -9.89, 0.00665, 3, 6, 51.78, -47.21, 0.98177, 53, -268.74, -3.65, 0.01376, 54, -227.74, -3.65, 0.00447, 3, 6, 40.36, -42.09, 0.98224, 53, -280.16, 1.46, 0.01474, 54, -239.16, 1.46, 0.00302, 3, 6, 32.41, -42.07, 0.98247, 53, -288.11, 1.49, 0.01429, 54, -247.11, 1.49, 0.00324, 3, 6, 25.97, -36.96, 0.98369, 53, -294.55, 6.6, 0.01484, 54, -253.55, 6.6, 0.00147, 3, 6, 29.61, -49.53, 0.98142, 53, -290.91, -5.97, 0.01252, 54, -249.91, -5.97, 0.00606, 3, 6, 36.82, -53.78, 0.9804, 53, -283.7, -10.22, 0.01217, 54, -242.7, -10.22, 0.00743, 3, 6, 42.31, -52.26, 0.98062, 53, -278.21, -8.7, 0.01269, 54, -237.21, -8.7, 0.0067, 2, 6, 63.03, -16.18, 0.97164, 53, -257.49, 27.38, 0.02836], "hull": 38, "edges": [0, 74, 16, 18, 18, 20, 20, 22, 38, 40, 40, 42, 64, 66, 76, 80, 80, 82, 84, 86, 86, 88, 88, 90, 78, 92, 92, 94, 94, 96, 82, 102, 102, 98, 76, 104, 104, 100, 78, 106, 106, 100, 84, 108, 108, 98, 96, 90, 116, 118, 118, 110, 58, 60, 50, 52, 56, 58, 52, 54, 54, 56, 60, 62, 62, 64, 120, 116, 122, 112, 114, 124, 124, 120, 110, 126, 126, 122, 66, 68, 72, 74, 68, 70, 70, 72, 4, 6, 2, 4, 0, 2, 6, 8, 8, 10, 14, 16, 10, 12, 12, 14, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 136, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 180, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 262, 264, 264, 266, 266, 268, 268, 246, 262, 260, 260, 258, 258, 256, 256, 254, 254, 252, 250, 270, 270, 252, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 314, 332, 332, 334, 334, 336, 336, 326, 338, 340, 340, 342, 344, 346, 348, 350, 352, 354, 354, 356, 356, 358, 358, 362, 366, 348, 350, 368, 370, 372, 378, 374, 364, 376, 46, 48, 48, 50, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 402, 404, 22, 24, 24, 26], "width": 161, "height": 173}}, "head2": {"head": {"type": "mesh", "uvs": [0.12522, 0.67512, 0.18706, 0.63839, 0.22004, 0.62816, 0.26157, 0.61452, 0.31166, 0.61338, 0.33858, 0.59231, 0.43724, 0.62229, 0.57389, 0.6593, 0.61004, 0.8179, 0.6451, 0.87853, 0.51286, 0.94229, 0.36821, 0.98976, 0.28425, 0.99028, 0.23603, 0.95254, 0.17742, 0.86384, 0.11687, 0.76101, 0.18603, 0.77798, 0.17911, 0.75223, 0.17911, 0.70916, 0.19188, 0.6849, 0.2105, 0.67252, 0.23976, 0.674, 0.26689, 0.65668, 0.29669, 0.65767, 0.34244, 0.67599, 0.38447, 0.69777, 0.42171, 0.7151, 0.1982, 0.79113, 0.21256, 0.81341, 0.23703, 0.8243, 0.28438, 0.81886, 0.33758, 0.80153, 0.38174, 0.77331, 0.41632, 0.73815, 0.44771, 0.71439, 0.44931, 0.74756, 0.19754, 0.78222, 0.21561, 0.76347, 0.21984, 0.74199, 0.23245, 0.72605, 0.26113, 0.72234, 0.29031, 0.71306, 0.32005, 0.71562, 0.3597, 0.72862, 0.39862, 0.73188, 0.42027, 0.72693, 0.23403, 0.75592, 0.27506, 0.74673, 0.32065, 0.73647, 0.23278, 0.78097, 0.27558, 0.77432, 0.32964, 0.75906, 0.26087, 0.8566, 0.34807, 0.83236, 0.48384, 0.88822, 0.24155, 0.89536, 0.42361, 0.83844, 0.3586, 0.91855, 0.27479, 0.93225], "triangles": [13, 58, 12, 11, 12, 57, 12, 58, 57, 10, 57, 54, 10, 11, 57, 13, 14, 55, 13, 55, 58, 29, 14, 28, 55, 14, 52, 10, 54, 9, 57, 58, 52, 58, 55, 52, 52, 53, 57, 57, 56, 54, 57, 53, 56, 52, 14, 29, 54, 8, 9, 8, 54, 35, 14, 27, 28, 27, 16, 36, 27, 14, 16, 16, 15, 17, 16, 14, 15, 52, 30, 53, 52, 29, 30, 53, 32, 56, 56, 32, 35, 35, 32, 33, 54, 56, 35, 35, 33, 45, 30, 31, 53, 53, 31, 32, 28, 49, 29, 30, 49, 50, 30, 29, 49, 30, 50, 31, 35, 7, 8, 28, 27, 49, 50, 51, 31, 31, 51, 32, 27, 36, 49, 36, 37, 49, 36, 16, 37, 49, 46, 50, 49, 37, 46, 16, 17, 37, 46, 47, 50, 51, 47, 48, 51, 50, 47, 35, 45, 34, 45, 26, 34, 51, 43, 32, 32, 44, 33, 32, 43, 44, 37, 38, 46, 37, 17, 38, 15, 18, 17, 15, 0, 18, 51, 48, 43, 38, 39, 46, 46, 40, 47, 46, 39, 40, 17, 18, 38, 35, 34, 7, 48, 41, 42, 48, 47, 41, 47, 40, 41, 38, 18, 39, 33, 44, 45, 48, 42, 43, 26, 45, 44, 44, 43, 25, 25, 43, 24, 26, 44, 25, 18, 19, 39, 19, 20, 39, 39, 21, 40, 39, 20, 21, 40, 21, 41, 43, 42, 24, 24, 42, 23, 34, 26, 6, 26, 25, 6, 34, 6, 7, 21, 22, 41, 42, 41, 23, 41, 22, 23, 18, 0, 19, 25, 24, 6, 0, 1, 19, 19, 1, 20, 23, 4, 24, 4, 5, 24, 24, 5, 6, 22, 21, 2, 21, 20, 2, 20, 1, 2, 4, 23, 3, 2, 3, 22, 23, 22, 3], "vertices": [2, 6, 19.99, 81.67, 0.97504, 53, -300.53, 125.23, 0.02496, 2, 6, 24.53, 70.76, 0.9692, 53, -295.99, 114.32, 0.0308, 2, 6, 25.36, 65.23, 0.96627, 53, -295.16, 108.78, 0.03373, 2, 6, 26.53, 58.23, 0.96495, 53, -293.99, 101.79, 0.03505, 2, 6, 25.33, 50.26, 0.9646, 53, -295.19, 93.82, 0.0354, 2, 6, 28.17, 45.36, 0.96408, 53, -292.35, 88.92, 0.03592, 2, 6, 20.32, 30.61, 0.96189, 53, -300.21, 74.17, 0.03811, 2, 6, 10.21, 10.04, 0.96329, 53, -310.31, 53.6, 0.03671, 2, 6, -17.82, 9.05, 0.9675, 53, -338.34, 52.61, 0.0325, 2, 6, -29.13, 5.3, 0.9748, 53, -349.65, 48.86, 0.02519, 2, 6, -36.32, 28.18, 0.96939, 53, -356.84, 71.74, 0.03061, 2, 6, -40.38, 52.54, 0.9641, 53, -360.9, 96.1, 0.0359, 2, 6, -38.13, 65.87, 0.96702, 53, -358.65, 109.43, 0.03298, 2, 6, -30.36, 72.39, 0.9712, 53, -350.88, 115.95, 0.0288, 2, 6, -13.62, 79.03, 0.97585, 53, -334.14, 122.59, 0.02415, 2, 6, 5.59, 85.56, 0.981, 53, -314.93, 129.12, 0.019, 2, 6, 0.78, 75.1, 0.96924, 53, -319.75, 118.66, 0.03076, 2, 6, 5.35, 75.43, 0.96937, 53, -315.17, 118.98, 0.03063, 2, 6, 12.69, 74.14, 0.96877, 53, -307.83, 117.7, 0.03123, 2, 6, 16.47, 71.39, 0.9676, 53, -304.05, 114.95, 0.0324, 2, 6, 18.06, 68.07, 0.96611, 53, -302.46, 111.62, 0.03389, 2, 6, 17, 63.47, 0.96376, 53, -303.52, 107.03, 0.03624, 2, 6, 19.2, 58.65, 0.96299, 53, -301.33, 102.21, 0.03701, 2, 6, 18.2, 53.95, 0.9626, 53, -302.32, 97.51, 0.0374, 2, 6, 13.8, 47.25, 0.96217, 53, -306.72, 90.81, 0.03783, 2, 6, 8.92, 41.23, 0.9621, 53, -311.6, 84.79, 0.0379, 2, 6, 4.93, 35.84, 0.96202, 53, -315.59, 79.4, 0.03798, 2, 6, -1.8, 73.56, 0.96846, 53, -322.33, 117.12, 0.03154, 2, 6, -6, 71.95, 0.96766, 53, -326.52, 115.51, 0.03234, 2, 6, -8.54, 68.39, 0.96694, 53, -329.06, 111.95, 0.03306, 2, 6, -8.93, 60.72, 0.96247, 53, -329.45, 104.28, 0.03753, 2, 6, -7.45, 51.77, 0.96125, 53, -327.97, 95.33, 0.03875, 2, 6, -3.87, 43.92, 0.96162, 53, -324.39, 87.48, 0.03838, 2, 6, 1.16, 37.39, 0.96205, 53, -319.37, 80.95, 0.03795, 2, 6, 4.33, 31.7, 0.96146, 53, -316.19, 75.26, 0.03854, 2, 6, -1.36, 32.44, 0.96136, 53, -321.89, 76, 0.03864, 2, 6, -0.27, 73.4, 0.96837, 53, -320.79, 116.96, 0.03163, 2, 6, 2.42, 69.97, 0.96843, 53, -318.1, 113.53, 0.03157, 2, 6, 5.97, 68.66, 0.96807, 53, -314.55, 112.22, 0.03193, 2, 6, 8.33, 66.18, 0.96726, 53, -312.19, 109.74, 0.03274, 2, 6, 8.17, 61.53, 0.96565, 53, -312.35, 105.08, 0.03435, 2, 6, 8.94, 56.62, 0.96484, 53, -311.58, 100.18, 0.03516, 2, 6, 7.67, 51.98, 0.9642, 53, -312.85, 95.54, 0.0358, 2, 6, 4.35, 46.08, 0.96332, 53, -316.17, 89.64, 0.03668, 2, 6, 2.72, 40.01, 0.96192, 53, -317.8, 83.57, 0.03808, 2, 6, 2.96, 36.43, 0.96203, 53, -317.56, 79.99, 0.03797, 2, 6, 3.2, 66.83, 0.96737, 53, -317.32, 110.39, 0.03263, 2, 6, 3.62, 60.05, 0.96509, 53, -316.9, 103.6, 0.03491, 2, 6, 4.1, 52.51, 0.96414, 53, -316.42, 96.07, 0.03586, 2, 6, -1.04, 67.77, 0.96454, 53, -321.56, 111.33, 0.03546, 2, 6, -1.09, 60.79, 0.96139, 53, -321.61, 104.35, 0.03861, 2, 6, 0, 51.76, 0.96083, 53, -320.52, 95.32, 0.03917, 2, 6, -14.7, 65.58, 0.96678, 53, -335.22, 109.14, 0.03322, 2, 6, -13, 51.03, 0.96323, 53, -333.52, 94.59, 0.03677, 2, 6, -26.29, 31.17, 0.96163, 53, -346.82, 74.73, 0.03837, 2, 6, -20.77, 69.8, 0.96844, 53, -341.29, 113.36, 0.03156, 2, 6, -16.14, 39.23, 0.96214, 53, -336.66, 82.79, 0.03786, 2, 6, -27.98, 51.93, 0.96096, 53, -348.5, 95.49, 0.03904, 2, 6, -27.98, 65.63, 0.96529, 53, -348.5, 109.19, 0.03471], "hull": 16, "edges": [20, 22, 22, 24, 18, 20, 24, 26, 26, 28, 2, 4, 4, 6, 6, 8, 8, 10, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 74, 92, 92, 94, 94, 96, 96, 86, 98, 100, 100, 102, 104, 106, 108, 16, 110, 104, 106, 112, 114, 116, 28, 30, 2, 0, 0, 30, 10, 12, 12, 14, 14, 16, 16, 18], "width": 161, "height": 173}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.39301, 0.00552, 0.45047, 0.02429, 0.53601, 0.05464, 0.64726, 0.10507, 0.72742, 0.14326, 0.74659, 0.14614, 0.84674, 0.18111, 0.8735, 0.19297, 0.90393, 0.21427, 0.9332, 0.23957, 0.95455, 0.26515, 0.99846, 0.3492, 0.98319, 0.38845, 0.95416, 0.43031, 0.91681, 0.46181, 0.87898, 0.47153, 0.82854, 0.4702, 0.76303, 0.45788, 0.71289, 0.43907, 0.65614, 0.4039, 0.60261, 0.35645, 0.53193, 0.28675, 0.47441, 0.24139, 0.44715, 0.22413, 0.42121, 0.21255, 0.41908, 0.24815, 0.40912, 0.30429, 0.38575, 0.36535, 0.3027, 0.52603, 0.26814, 0.60713, 0.25785, 0.63531, 0.2518, 0.66349, 0.25123, 0.68153, 0.26057, 0.70452, 0.29166, 0.74144, 0.30299, 0.7884, 0.27869, 0.88083, 0.27361, 1, 0.25431, 0.99826, 0.25467, 0.89013, 0.24599, 0.85823, 0.2388, 0.85446, 0.21971, 0.88294, 0.20696, 0.92176, 0.19334, 0.95947, 0.18033, 0.9779, 0.1294, 0.9802, 0.06915, 0.96909, 0.01544, 0.94352, 0.00246, 0.9309, 0, 0.92255, 0.00728, 0.89343, 0.02556, 0.88271, 0.07943, 0.87174, 0.09418, 0.86602, 0.10078, 0.84504, 0.11116, 0.81209, 0.14432, 0.69862, 0.15682, 0.66425, 0.16936, 0.63491, 0.18032, 0.60535, 0.18942, 0.57433, 0.21609, 0.45631, 0.24257, 0.31441, 0.25479, 0.25873, 0.26702, 0.20305, 0.2792, 0.14756, 0.2817, 0.10194, 0.28701, 0.06634, 0.29689, 0.03651, 0.31315, 0.01528, 0.33933, 0.00255, 0.3647, 0.00026, 0.08684, 0.92151, 0.12609, 0.92668, 0.15151, 0.90674, 0.16599, 0.86761, 0.17532, 0.83217, 0.37058, 0.08713, 0.40877, 0.08938, 0.45401, 0.11542, 0.41611, 0.1412, 0.34794, 0.12077, 0.35008, 0.17196, 0.34475, 0.22857, 0.31822, 0.33679, 0.33296, 0.2793, 0.50361, 0.14267, 0.58437, 0.1846, 0.6879, 0.25556, 0.75447, 0.29595, 0.82859, 0.31277, 0.87856, 0.30522, 0.92319, 0.28742, 0.79131, 0.21242, 0.83707, 0.20642, 0.85116, 0.41732, 0.89809, 0.39668, 0.93056, 0.37244, 0.95716, 0.34372], "triangles": [93, 9, 10, 97, 98, 13, 97, 92, 98, 98, 99, 12, 92, 93, 98, 12, 99, 11, 98, 93, 99, 93, 10, 99, 93, 8, 9, 11, 99, 10, 92, 8, 93, 13, 98, 12, 14, 97, 13, 97, 14, 15, 97, 91, 92, 15, 96, 97, 47, 73, 46, 73, 74, 46, 46, 74, 45, 74, 75, 45, 45, 75, 44, 47, 48, 73, 44, 75, 43, 48, 50, 51, 48, 52, 73, 48, 51, 52, 48, 49, 50, 75, 74, 54, 52, 53, 73, 74, 73, 54, 73, 53, 54, 38, 39, 37, 37, 39, 36, 75, 76, 43, 43, 76, 42, 75, 54, 76, 76, 54, 55, 39, 40, 36, 41, 42, 77, 36, 40, 35, 55, 56, 76, 42, 76, 77, 76, 56, 77, 40, 41, 35, 35, 41, 34, 56, 57, 77, 77, 57, 33, 34, 41, 33, 41, 77, 33, 32, 57, 58, 57, 32, 33, 58, 59, 32, 32, 59, 31, 59, 60, 31, 31, 60, 30, 29, 30, 61, 30, 60, 61, 29, 61, 28, 61, 62, 28, 28, 85, 27, 28, 62, 85, 62, 63, 85, 85, 86, 27, 27, 86, 26, 63, 64, 85, 85, 64, 86, 26, 86, 25, 25, 86, 84, 84, 86, 65, 86, 64, 65, 25, 84, 24, 84, 83, 24, 84, 65, 83, 83, 81, 24, 65, 66, 83, 66, 82, 83, 82, 81, 83, 66, 67, 82, 67, 68, 82, 82, 68, 78, 78, 68, 69, 78, 69, 70, 15, 16, 96, 16, 17, 96, 18, 90, 17, 17, 91, 96, 17, 90, 91, 18, 19, 90, 19, 89, 90, 19, 20, 89, 90, 89, 94, 89, 4, 5, 89, 20, 88, 91, 95, 92, 92, 95, 8, 90, 94, 91, 91, 94, 95, 94, 89, 5, 20, 21, 88, 21, 22, 88, 88, 3, 89, 89, 3, 4, 22, 87, 88, 22, 23, 87, 87, 23, 80, 80, 23, 81, 23, 24, 81, 95, 94, 6, 6, 94, 5, 87, 2, 88, 88, 2, 3, 79, 81, 78, 78, 81, 82, 87, 80, 2, 81, 79, 80, 78, 70, 71, 79, 1, 80, 80, 1, 2, 78, 0, 79, 79, 0, 1, 71, 72, 78, 78, 72, 0, 95, 6, 7, 8, 95, 7, 97, 96, 91], "vertices": [2, 25, 407.49, -45.72, 0.93862, 26, -44.8, 17.9, 0.06138, 1, 25, 364.28, -47.67, 1, 1, 25, 299.47, -49.12, 1, 1, 25, 213, -44.47, 1, 1, 25, 150.31, -40, 1, 1, 25, 136.57, -42.68, 1, 3, 25, 60.81, -44.72, 0.58494, 23, -9.47, 38.31, 0.21895, 2, 54.71, -41.46, 0.19611, 4, 25, 40.06, -43.76, 0.33111, 23, -24.42, 23.89, 0.31778, 1, 31.03, 79.69, 0.0842, 2, 39.76, -55.88, 0.2669, 4, 25, 14.91, -38, 0.13743, 23, -45.89, 9.57, 0.34645, 1, 53.07, 66.26, 0.25316, 2, 18.28, -70.19, 0.26296, 3, 23, -69.3, -2.91, 0.21967, 1, 74.25, 50.29, 0.54368, 2, -5.13, -82.68, 0.23665, 3, 23, -90.46, -10.13, 0.07143, 1, 89.71, 34.15, 0.73025, 2, -26.28, -89.89, 0.19832, 1, 1, 121.5, -18.89, 1, 1, 1, 110.45, -43.66, 1, 2, 25, -62.78, 79.78, 0.05612, 1, 89.43, -70.07, 0.94388, 2, 25, -43.43, 107.2, 0.29237, 1, 62.39, -89.94, 0.70763, 2, 25, -19.39, 121.7, 0.5368, 1, 35, -96.08, 0.4632, 2, 25, 15.5, 132.47, 0.57774, 1, -1.52, -95.24, 0.42226, 2, 25, 62.95, 140.13, 0.78838, 1, -48.95, -87.46, 0.21162, 2, 25, 101.14, 140.37, 0.9292, 1, -85.25, -75.59, 0.0708, 1, 25, 147.15, 132.34, 1, 1, 25, 193.4, 116.23, 1, 2, 25, 255.86, 90.72, 0.99479, 26, 99.86, 161.72, 0.00521, 2, 25, 304.43, 76.77, 0.91439, 26, 83.23, 114, 0.08561, 2, 25, 326.61, 72.7, 0.78239, 26, 77.93, 92.09, 0.21761, 2, 25, 346.73, 71.72, 0.49211, 26, 75.83, 72.04, 0.50789, 2, 25, 341.07, 93.51, 0.20529, 26, 97.91, 76.48, 0.79471, 2, 25, 336.69, 129.39, 0.05447, 26, 133.98, 78.87, 0.94553, 2, 25, 340.53, 171.3, 0.00615, 26, 175.61, 72.7, 0.99385, 1, 26, 289.27, 41.44, 1, 1, 26, 345.22, 30.8, 1, 2, 26, 364.34, 28.3, 0.92185, 27, -22.34, 27.9, 0.07815, 2, 26, 382.65, 28.77, 0.51561, 27, -4.04, 28.7, 0.48439, 2, 26, 393.74, 31.37, 0.20727, 27, 7, 31.5, 0.79273, 2, 26, 405.95, 41.72, 0.02626, 27, 19.02, 42.08, 0.97374, 1, 27, 35.04, 70.23, 1, 1, 27, 61.17, 86.44, 1, 1, 27, 122.09, 85.96, 1, 1, 27, 195.28, 103.57, 1, 1, 27, 198.16, 89.86, 1, 1, 27, 132.61, 70.92, 1, 1, 27, 115.06, 59.23, 1, 2, 27, 114.24, 53.56, 0.99938, 28, -67.75, -4.05, 0.00062, 2, 27, 135.37, 45.35, 0.9529, 28, -49.03, 8.72, 0.0471, 2, 27, 161.48, 43.38, 0.66078, 28, -32.62, 29.12, 0.33922, 2, 27, 187.08, 40.61, 0.31691, 28, -15.84, 48.67, 0.68309, 2, 27, 200.89, 34.84, 0.21048, 28, -3.27, 56.78, 0.78952, 2, 27, 212.66, -0.13, 0.02056, 28, 32.22, 46.69, 0.97944, 1, 28, 71.49, 26.44, 1, 1, 28, 103.42, -1, 1, 1, 28, 109.87, -11.49, 1, 1, 28, 109.92, -17.05, 1, 1, 28, 99.19, -32.87, 1, 1, 28, 84.51, -35.18, 1, 2, 27, 157.16, -54.1, 0.10299, 28, 45.29, -29.61, 0.89701, 2, 27, 150.69, -44.87, 0.2634, 28, 34.02, -29.72, 0.7366, 2, 27, 136.64, -44, 0.59145, 28, 25.35, -40.81, 0.40855, 2, 27, 114.58, -42.64, 0.89114, 28, 11.74, -58.23, 0.10886, 2, 26, 424.55, -40.45, 0.00702, 27, 39.11, -39.74, 0.99298, 2, 26, 401.25, -37.44, 0.14179, 27, 15.76, -37.16, 0.85821, 2, 26, 381, -33.56, 0.55393, 27, -4.56, -33.65, 0.44607, 2, 26, 360.91, -30.83, 0.91608, 27, -24.69, -31.28, 0.08392, 1, 26, 340.29, -29.64, 1, 1, 26, 263.37, -30.64, 1, 1, 26, 171.94, -35.76, 1, 1, 26, 135.72, -36.49, 1, 1, 26, 99.49, -37.22, 1, 1, 26, 63.39, -37.95, 1, 1, 26, 35.15, -43.79, 1, 2, 25, 468.12, 15, 0.02014, 26, 12.46, -46.01, 0.97986, 2, 25, 467.3, -5.12, 0.13255, 26, -7.58, -44.07, 0.86745, 2, 25, 460.38, -21.56, 0.29403, 26, -23.61, -36.24, 0.70597, 2, 25, 444.94, -35.18, 0.54286, 26, -36.36, -20.08, 0.45714, 2, 25, 427.98, -42.37, 0.75951, 26, -42.59, -2.74, 0.24049, 1, 28, 49.97, 1.9, 1, 1, 28, 23.98, 13.85, 1, 2, 27, 163.67, 2.19, 0.26218, 28, 2.57, 7.62, 0.73782, 1, 27, 137.03, 5.31, 1, 1, 27, 113.67, 5.5, 1, 2, 25, 406.57, 8.27, 0.76621, 26, 9.16, 15.82, 0.23379, 2, 25, 379.9, 0.86, 0.99892, 26, 3.24, 42.86, 0.00108, 2, 25, 343.63, 6.06, 0.98421, 26, 10.45, 78.79, 0.01579, 2, 25, 364.5, 30.19, 0.74825, 26, 33.38, 56.61, 0.25175, 2, 25, 415.4, 33.6, 0.05237, 26, 33.96, 5.6, 0.94763, 2, 25, 403.69, 63.74, 0.08382, 26, 64.71, 15.62, 0.91618, 2, 25, 396.03, 98.84, 0.04684, 26, 100.18, 21.31, 0.95316, 2, 25, 392.61, 169.7, 0.00058, 26, 171.12, 20.8, 0.99942, 2, 25, 393.98, 131.91, 0.01258, 26, 133.31, 21.52, 0.98742, 2, 25, 304.12, 10.99, 0.99226, 26, 17.57, 117.96, 0.00774, 1, 25, 240.28, 17.56, 1, 1, 25, 155, 36.28, 1, 2, 25, 101.21, 45.18, 0.92491, 1, -55.14, 14.71, 0.07509, 2, 25, 46.95, 38.24, 0.76446, 1, -1.48, 4.1, 0.23554, 2, 25, 14.15, 22.26, 0.40647, 1, 34.69, 8.87, 0.59353, 2, 25, -12.94, 1.37, 0.05592, 1, 67.01, 20.09, 0.94408, 2, 25, 92.61, -13.26, 0.93642, 1, -28.47, 67.42, 0.06358, 2, 25, 62.39, -27.35, 0.80198, 1, 4.66, 71.21, 0.19803, 2, 25, 10.55, 95.64, 0.60594, 1, 14.86, -61.87, 0.39406, 2, 25, -17.56, 72.51, 0.19174, 1, 48.84, -48.84, 0.80826, 1, 1, 72.34, -33.55, 1, 1, 1, 91.6, -15.43, 1], "hull": 73, "edges": [0, 144, 6, 8, 8, 10, 22, 24, 28, 30, 40, 42, 42, 44, 54, 56, 56, 58, 62, 64, 68, 70, 70, 72, 72, 74, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 108, 110, 142, 144, 84, 86, 80, 82, 82, 84, 76, 78, 78, 80, 74, 76, 86, 88, 88, 90, 90, 92, 92, 94, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 64, 66, 66, 68, 58, 60, 60, 62, 120, 122, 122, 124, 124, 126, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 0, 2, 52, 54, 44, 46, 46, 48, 48, 50, 50, 52, 38, 40, 30, 32, 32, 34, 26, 28, 24, 26, 34, 36, 36, 38, 2, 4, 4, 6, 162, 48, 130, 132, 126, 128, 128, 130, 18, 20, 20, 22, 14, 16, 16, 18, 10, 12, 12, 14], "width": 724, "height": 631}}, "light": {"light": {"type": "mesh", "uvs": [1, 0.26358, 0.68242, 0.87402, 0.53146, 0.9923, 0.1946, 0.95507, 0.03546, 0.88633, 0.03788, 0.79181, 0.17021, 0.57111, 0.63745, 0.00602], "triangles": [1, 7, 0, 6, 7, 1, 2, 3, 6, 5, 6, 3, 4, 5, 3, 1, 2, 6], "vertices": [1, 35, 144.92, -6.6, 1, 1, 35, 108.58, -9.73, 1, 1, 35, 99, -6.33, 1, 1, 35, 91.1, 9.8, 1, 1, 35, 89.66, 18.87, 1, 1, 35, 93.95, 21.45, 1, 1, 35, 107.59, 21.79, 1, 1, 35, 146.14, 16.93, 1], "hull": 8, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0, 0, 2], "width": 53, "height": 53}}, "nose": {"nose": {"type": "mesh", "uvs": [0.85288, 0.13629, 0.89366, 0.36551, 0.94703, 0.66555, 0.95483, 0.80554, 0.83715, 0.88915, 0.59281, 0.89802, 0.38459, 0.94349, 0.19977, 0.99038, 0.09244, 0.905, 0.05794, 0.7265, 0.0574, 0.58483, 0.19792, 0.45415, 0.29777, 0.32333, 0.32536, 0.19859, 0.3129, 0.06109, 0.58604, 0.02954, 0.76004, 0.52053, 0.55072, 0.18691, 0.50782, 0.30474, 0.46514, 0.43781, 0.51435, 0.59305, 0.15547, 0.72623, 0.7422, 0.74591, 0.23453, 0.58165, 0.33856, 0.43686, 0.40213, 0.30419, 0.43103, 0.19363, 0.23693, 0.84053, 0.42371, 0.80155, 0.32353, 0.70475], "triangles": [7, 27, 6, 7, 8, 27, 6, 28, 5, 6, 27, 28, 8, 21, 27, 8, 9, 21, 5, 22, 4, 5, 28, 22, 4, 22, 3, 27, 29, 28, 27, 21, 29, 22, 2, 3, 28, 20, 22, 28, 29, 20, 22, 16, 2, 22, 20, 16, 9, 10, 21, 21, 23, 29, 21, 10, 23, 19, 20, 24, 24, 20, 23, 23, 20, 29, 16, 1, 2, 20, 19, 16, 10, 11, 23, 23, 11, 24, 1, 16, 18, 11, 12, 24, 24, 25, 19, 16, 19, 18, 19, 25, 18, 24, 12, 25, 18, 17, 1, 17, 0, 1, 12, 13, 25, 25, 26, 18, 18, 26, 17, 25, 13, 26, 13, 14, 26, 17, 26, 15, 17, 15, 0, 15, 26, 14], "vertices": [2, 6, 59.46, 43.14, 0.96498, 53, -261.07, 86.7, 0.03502, 2, 6, 48.85, 43.67, 0.9647, 53, -271.68, 87.23, 0.0353, 2, 6, 34.96, 44.38, 0.96419, 53, -285.57, 87.93, 0.03581, 2, 6, 28.57, 45.24, 0.96408, 53, -291.95, 88.8, 0.03592, 2, 6, 25.43, 49.62, 0.9646, 53, -295.09, 93.18, 0.0354, 2, 6, 26.38, 57.39, 0.96495, 53, -294.14, 100.95, 0.03505, 2, 6, 25.47, 64.31, 0.96627, 53, -295.05, 107.87, 0.03373, 2, 6, 24.37, 70.51, 0.9692, 53, -296.15, 114.07, 0.0308, 2, 6, 28.83, 73.21, 0.967, 53, -291.69, 116.77, 0.033, 2, 6, 37.11, 72.88, 0.96311, 53, -283.41, 116.44, 0.03689, 2, 6, 43.53, 71.77, 0.96216, 53, -276.99, 115.33, 0.03784, 2, 6, 48.68, 66.31, 0.96227, 53, -271.85, 109.87, 0.03773, 2, 6, 54.05, 62.12, 0.96392, 53, -266.47, 105.68, 0.03608, 2, 6, 59.55, 60.26, 0.96531, 53, -260.97, 103.82, 0.03469, 2, 6, 65.85, 59.56, 0.96735, 53, -254.67, 103.12, 0.03265, 2, 6, 65.77, 50.7, 0.96607, 53, -254.75, 94.26, 0.03393, 2, 6, 42.56, 49.12, 0.96254, 53, -277.96, 92.68, 0.03746, 2, 6, 58.83, 53.06, 0.96389, 53, -261.69, 96.62, 0.03611, 2, 6, 53.73, 55.35, 0.96235, 53, -266.79, 98.91, 0.03765, 2, 6, 47.94, 57.75, 0.95991, 53, -272.58, 101.31, 0.04009, 2, 6, 40.63, 57.44, 0.95954, 53, -279.89, 101, 0.04046, 2, 6, 36.58, 69.81, 0.96219, 53, -283.94, 113.37, 0.03781, 2, 6, 32.45, 51.47, 0.96168, 53, -288.07, 95.03, 0.03832, 2, 6, 42.7, 66.17, 0.95885, 53, -277.82, 109.73, 0.04115, 2, 6, 48.68, 61.74, 0.96036, 53, -271.84, 105.3, 0.03964, 2, 6, 54.34, 58.68, 0.96279, 53, -266.18, 102.24, 0.03721, 2, 6, 59.19, 56.89, 0.96436, 53, -261.33, 100.45, 0.03564, 2, 6, 30.95, 68.15, 0.96416, 53, -289.57, 111.71, 0.03584, 2, 6, 31.69, 61.95, 0.96223, 53, -288.83, 105.51, 0.03777, 2, 6, 36.63, 64.34, 0.96064, 53, -283.89, 107.9, 0.03936], "hull": 16, "edges": [0, 30, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 22, 24, 24, 26, 26, 28, 12, 14, 8, 10, 10, 12, 4, 32, 0, 2, 2, 4, 30, 34, 34, 36, 36, 38, 38, 40, 46, 48, 48, 50, 50, 52, 40, 58, 58, 42], "width": 32, "height": 46}}, "wine": {"wine": {"type": "mesh", "uvs": [0.45182, 0.06888, 0.55481, 0.09924, 0.66317, 0.13403, 0.76729, 0.17152, 0.8637, 0.21331, 0.94848, 0.2551, 0.98966, 0.30525, 0.87558, 0.57278, 0.70404, 0.80287, 0.50866, 0.9454, 0.35686, 0.98385, 0.19781, 0.93992, 0.11059, 0.86496, 0.02657, 0.64281, 0.00439, 0.37601, 0.08444, 0.0314, 0.15891, 0.01266, 0.25281, 0.02559, 0.34672, 0.04258, 0.09981, 0.08018, 0.29946, 0.1864, 0.51769, 0.25392, 0.73685, 0.30724, 0.92457, 0.32743, 0.19207, 0.1356, 0.40752, 0.22556, 0.62833, 0.28193, 0.84052, 0.32312, 0.11415, 0.47367, 0.25301, 0.5418, 0.43052, 0.57, 0.60135, 0.56517, 0.77272, 0.5778], "triangles": [32, 22, 27, 7, 23, 6, 7, 27, 23, 30, 25, 21, 31, 26, 22, 31, 21, 26, 29, 20, 25, 28, 24, 20, 14, 19, 24, 14, 15, 19, 23, 5, 6, 5, 23, 4, 23, 27, 4, 4, 27, 3, 26, 2, 22, 27, 22, 3, 22, 2, 3, 21, 1, 26, 26, 1, 2, 25, 0, 21, 21, 0, 1, 20, 18, 25, 25, 18, 0, 24, 17, 20, 20, 17, 18, 19, 16, 24, 24, 16, 17, 19, 15, 16, 9, 10, 30, 30, 10, 29, 9, 31, 8, 9, 30, 31, 10, 11, 29, 11, 12, 29, 29, 13, 28, 29, 12, 13, 8, 32, 7, 8, 31, 32, 13, 14, 28, 31, 22, 32, 32, 27, 7, 29, 25, 30, 30, 21, 31, 29, 28, 20, 28, 14, 24], "vertices": [2, 36, -9.83, 6.76, 0.77609, 37, 2.46, 11.18, 0.22391, 2, 36, -0.11, 6.5, 0.73615, 37, 5.69, 2.01, 0.26385, 2, 36, 10.16, 6.03, 0.7834, 37, 8.92, -7.75, 0.2166, 2, 36, 20.08, 5.3, 0.85013, 37, 11.77, -17.28, 0.14987, 2, 36, 29.38, 4.12, 0.91441, 37, 13.98, -26.38, 0.08559, 2, 36, 37.62, 2.72, 0.97236, 37, 15.62, -34.58, 0.02764, 1, 36, 42.05, -0.06, 1, 1, 35, 103.76, -39.3, 1, 1, 35, 81.32, -34.85, 1, 1, 35, 63.02, -25.18, 1, 1, 35, 53.2, -14.87, 1, 1, 35, 48.03, -0.82, 1, 1, 35, 48.28, 8.85, 1, 1, 35, 57.65, 23.94, 1, 1, 35, 70.37, 35.28, 1, 1, 36, -43.53, 2.78, 1, 2, 36, -37.04, 5.35, 0.9619, 37, -8.56, 36.11, 0.0381, 2, 36, -28.39, 6.16, 0.90144, 37, -4.72, 28.31, 0.09856, 2, 36, -19.68, 6.68, 0.84142, 37, -1.12, 20.36, 0.15858, 2, 36, -41.4, -0.49, 0.99189, 37, -15.58, 38.09, 0.00811, 2, 36, -21.88, -4.33, 0.85742, 37, -12.2, 18.49, 0.14258, 2, 36, -1.24, -5.09, 0.74311, 37, -5.54, -1.07, 0.25689, 2, 36, 19.29, -4.84, 0.84693, 37, 2.02, -20.15, 0.15307, 2, 36, 36.5, -2.82, 0.9654, 37, 10.05, -35.51, 0.0346, 2, 36, -32.29, -2.71, 0.92842, 37, -14.4, 28.79, 0.07158, 2, 36, -11.58, -5.11, 0.79019, 37, -9.25, 8.58, 0.20981, 2, 36, 9.14, -5.04, 0.78067, 37, -1.79, -10.75, 0.21933, 2, 36, 28.86, -4.06, 0.91247, 37, 6.17, -28.82, 0.08753, 1, 35, 72.25, 23.67, 1, 1, 35, 74.95, 10.25, 1, 1, 35, 81.99, -4.62, 1, 1, 35, 90.7, -17.71, 1, 1, 35, 98.38, -31.51, 1], "hull": 19, "edges": [10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 30, 38, 46, 12, 38, 48, 48, 40, 32, 34, 34, 36, 40, 50, 50, 42, 2, 0, 0, 36, 42, 52, 52, 44, 2, 4, 4, 6, 44, 54, 54, 46, 6, 8, 8, 10, 12, 14], "width": 92, "height": 72}}}}], "animations": {"1": {"bones": {"body": {"rotate": [{"value": 2.36, "curve": [0.444, 2.36, 0.889, -2.94]}, {"time": 1.3333, "value": -2.94, "curve": [1.778, -2.94, 2.222, 2.36]}, {"time": 2.6667, "value": 2.36, "curve": [3.111, 2.36, 3.556, -2.94]}, {"time": 4, "value": -2.94, "curve": [4.444, -2.94, 4.889, 2.36]}, {"time": 5.3333, "value": 2.36, "curve": [5.667, 2.36, 6, -5.8]}, {"time": 6.3333, "value": -5.8, "curve": [6.778, -5.81, 7.222, -1.72]}, {"time": 7.6667, "value": -1.72, "curve": [8.111, -1.72, 8.556, -5.8]}, {"time": 9, "value": -5.8, "curve": [9.444, -5.81, 9.889, 2.36]}, {"time": 10.3333, "value": 2.36, "curve": [10.778, 2.36, 11.222, -2.94]}, {"time": 11.6667, "value": -2.94, "curve": [12.111, -2.94, 12.556, 2.36]}, {"time": 13, "value": 2.36}]}, "body2": {"rotate": [{"value": 1.59, "curve": [0.057, 1.74, 0.112, 1.85]}, {"time": 0.1667, "value": 1.85, "curve": [0.611, 1.85, 1.056, -3.59]}, {"time": 1.5, "value": -3.59, "curve": [1.944, -3.6, 2.389, 1.85]}, {"time": 2.8333, "value": 1.85, "curve": [3.278, 1.85, 3.722, -3.59]}, {"time": 4.1667, "value": -3.59, "curve": [4.611, -3.6, 5.056, 1.84]}, {"time": 5.5, "value": 1.85, "curve": [5.833, 1.85, 6.167, -9.53]}, {"time": 6.5, "value": -9.53, "curve": [6.944, -9.53, 7.389, -3.84]}, {"time": 7.8333, "value": -3.84, "curve": [8.278, -3.84, 8.722, -9.52]}, {"time": 9.1667, "value": -9.53, "curve": [9.611, -9.53, 10.056, 1.85]}, {"time": 10.5, "value": 1.85, "curve": [10.944, 1.85, 11.389, -3.59]}, {"time": 11.8333, "value": -3.59, "curve": [12.223, -3.6, 12.613, 0.56]}, {"time": 13, "value": 1.59}], "translate": [{"x": -3.92, "curve": [0.057, -4.18, 0.112, -4.39, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -4.39, "curve": [0.611, -4.39, 1.056, 5.51, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 5.52, "curve": [1.944, 5.52, 2.389, -4.38, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -4.39, "curve": [3.278, -4.39, 3.722, 5.51, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 5.52, "curve": [4.611, 5.52, 5.056, -4.38, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -4.39, "curve": [5.833, -4.39, 6.167, 5.51, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 5.52, "curve": [6.944, 5.52, 7.389, 0.57, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 0.56, "curve": [8.278, 0.56, 8.722, 5.51, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 5.52, "curve": [9.611, 5.52, 10.056, -4.38, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -4.39, "curve": [10.944, -4.39, 11.389, 5.51, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 5.52, "curve": [12.223, 5.52, 12.613, -2.04, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -3.92}], "scale": [{"y": 1.018, "curve": [0.057, 1, 0.112, 1, 0.057, 1.019, 0.112, 1.021]}, {"time": 0.1667, "y": 1.021, "curve": [0.611, 1, 1.056, 1, 0.611, 1.021, 1.056, 0.963]}, {"time": 1.5, "y": 0.963, "curve": [1.944, 1, 2.389, 1, 1.944, 0.963, 2.389, 1.02]}, {"time": 2.8333, "y": 1.021, "curve": [3.278, 1, 3.722, 1, 3.278, 1.021, 3.722, 0.963]}, {"time": 4.1667, "y": 0.963, "curve": [4.611, 1, 5.056, 1, 4.611, 0.963, 5.056, 1.02]}, {"time": 5.5, "y": 1.021, "curve": [5.833, 1, 6.167, 1, 5.833, 1.021, 6.167, 0.963]}, {"time": 6.5, "y": 0.963, "curve": [6.944, 1, 7.389, 1, 6.944, 0.963, 7.389, 0.992]}, {"time": 7.8333, "y": 0.992, "curve": [8.278, 1, 8.722, 1, 8.278, 0.992, 8.722, 0.963]}, {"time": 9.1667, "y": 0.963, "curve": [9.611, 1, 10.056, 1, 9.611, 0.963, 10.056, 1.02]}, {"time": 10.5, "y": 1.021, "curve": [10.944, 1, 11.389, 1, 10.944, 1.021, 11.389, 0.963]}, {"time": 11.8333, "y": 0.963, "curve": [12.223, 1, 12.613, 1, 12.223, 0.963, 12.613, 1.007]}, {"time": 13, "y": 1.018}]}, "body3": {"rotate": [{"value": -3, "curve": [0.114, -3.12, 0.224, -3.22]}, {"time": 0.3333, "value": -3.22, "curve": [0.778, -3.22, 1.222, -1.83]}, {"time": 1.6667, "value": -1.83, "curve": [2.111, -1.83, 2.556, -3.22]}, {"time": 3, "value": -3.22, "curve": [3.444, -3.22, 3.889, -1.83]}, {"time": 4.3333, "value": -1.83, "curve": [4.778, -1.83, 5.222, -3.22]}, {"time": 5.6667, "value": -3.22, "curve": [6, -3.22, 6.333, -4.16]}, {"time": 6.6667, "value": -4.16, "curve": [7.111, -4.16, 7.556, -3.69]}, {"time": 8, "value": -3.69, "curve": [8.444, -3.69, 8.889, -4.16]}, {"time": 9.3333, "value": -4.16, "curve": [9.778, -4.16, 10.222, -3.22]}, {"time": 10.6667, "value": -3.22, "curve": [11.111, -3.22, 11.556, -1.83]}, {"time": 12, "value": -1.83, "curve": [12.335, -1.83, 12.67, -2.61]}, {"time": 13, "value": -3}], "translate": [{"x": -4.16, "curve": [0.114, -5, 0.224, -5.63, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.63, "curve": [0.778, -5.63, 1.222, 3.56, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 3.56, "curve": [2.111, 3.57, 2.556, -5.62, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.63, "curve": [3.444, -5.63, 3.889, 3.56, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 3.56, "curve": [4.778, 3.57, 5.222, -5.62, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.63, "curve": [6, -5.63, 6.333, 3.56, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 3.56, "curve": [7.111, 3.57, 7.556, -1.03, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.03, "curve": [8.444, -1.03, 8.889, 3.56, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 3.56, "curve": [9.778, 3.57, 10.222, -5.62, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.63, "curve": [11.111, -5.63, 11.556, 3.56, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 3.56, "curve": [12.335, 3.57, 12.67, -1.58, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.16}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.021, 0.778, 1, 1.222, 1.021]}, {"time": 1.6667, "x": 1.021, "y": 1.021, "curve": [2.111, 1.021, 2.556, 1, 2.111, 1.021, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.021, 3.444, 1, 3.889, 1.021]}, {"time": 4.3333, "x": 1.021, "y": 1.021, "curve": [4.778, 1.021, 5.222, 1, 4.778, 1.021, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.021, 6, 1, 6.333, 1.021]}, {"time": 6.6667, "x": 1.021, "y": 1.021, "curve": [7.111, 1.021, 7.556, 1.011, 7.111, 1.021, 7.556, 1.011]}, {"time": 8, "x": 1.011, "y": 1.011, "curve": [8.444, 1.011, 8.889, 1.021, 8.444, 1.011, 8.889, 1.021]}, {"time": 9.3333, "x": 1.021, "y": 1.021, "curve": [9.778, 1.021, 10.222, 1, 9.778, 1.021, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.021, 11.111, 1, 11.556, 1.021]}, {"time": 12, "x": 1.021, "y": 1.021, "curve": [12.335, 1.021, 12.67, 1.009, 12.335, 1.021, 12.67, 1.009]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.54, "curve": [0.168, 1.55, 0.334, 2.37]}, {"time": 0.5, "value": 2.37, "curve": [0.944, 2.37, 1.389, -3.39]}, {"time": 1.8333, "value": -3.39, "curve": [2.278, -3.39, 2.722, 2.37]}, {"time": 3.1667, "value": 2.37, "curve": [3.611, 2.37, 4.056, -3.39]}, {"time": 4.5, "value": -3.39, "curve": [4.944, -3.39, 5.389, 2.37]}, {"time": 5.8333, "value": 2.37, "curve": [6.167, 2.37, 6.5, -8.58]}, {"time": 6.8333, "value": -8.58, "curve": [7.278, -8.58, 7.722, -3.11]}, {"time": 8.1667, "value": -3.1, "curve": [8.611, -3.1, 9.056, -8.58]}, {"time": 9.5, "value": -8.58, "curve": [9.944, -8.58, 10.389, 2.37]}, {"time": 10.8333, "value": 2.37, "curve": [11.278, 2.37, 11.722, -3.39]}, {"time": 12.1667, "value": -3.39, "curve": [12.445, -3.39, 12.724, -1.15]}, {"time": 13, "value": 0.54}]}, "head": {"rotate": [{"value": -0.51, "curve": [0.225, 0.92, 0.446, 2.37]}, {"time": 0.6667, "value": 2.37, "curve": [1.111, 2.37, 1.556, -3.39]}, {"time": 2, "value": -3.39, "curve": [2.444, -3.39, 2.889, 2.37]}, {"time": 3.3333, "value": 2.37, "curve": [3.778, 2.37, 4.222, -3.39]}, {"time": 4.6667, "value": -3.39, "curve": [5.111, -3.39, 5.556, 2.37]}, {"time": 6, "value": 2.37, "curve": [6.333, 2.37, 6.667, -8.58]}, {"time": 7, "value": -8.58, "curve": [7.444, -8.58, 7.889, -3.11]}, {"time": 8.3333, "value": -3.1, "curve": [8.778, -3.1, 9.222, -8.58]}, {"time": 9.6667, "value": -8.58, "curve": [10.111, -8.58, 10.556, 2.37]}, {"time": 11, "value": 2.37, "curve": [11.444, 2.37, 11.889, -3.39]}, {"time": 12.3333, "value": -3.39, "curve": [12.557, -3.39, 12.781, -1.96]}, {"time": 13, "value": -0.51}]}, "sh_L": {"translate": [{"x": -1.78, "curve": [0.114, -2.61, 0.224, -3.23, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -3.23, "curve": [0.778, -3.23, 1.222, 5.82, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.82, "curve": [2.111, 5.82, 2.556, -3.23, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -3.23, "curve": [3.444, -3.23, 3.889, 5.82, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.82, "curve": [4.778, 5.82, 5.222, -3.22, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -3.23, "curve": [6, -3.23, 6.333, 5.82, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.82, "curve": [7.111, 5.82, 7.556, -3.23, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -3.23, "curve": [8.444, -3.23, 8.889, 5.82, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.82, "curve": [9.778, 5.82, 10.222, -3.23, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -3.23, "curve": [11.111, -3.23, 11.556, 5.82, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.82, "curve": [12.335, 5.82, 12.67, 0.75, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.78}]}, "sh_R": {"translate": [{"x": -1.78, "curve": [0.114, -2.61, 0.224, -3.23, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -3.23, "curve": [0.778, -3.23, 1.222, 5.82, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.82, "curve": [2.111, 5.82, 2.556, -3.23, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -3.23, "curve": [3.444, -3.23, 3.889, 5.82, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.82, "curve": [4.778, 5.82, 5.222, -3.22, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -3.23, "curve": [6, -3.23, 6.333, 5.82, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.82, "curve": [7.111, 5.82, 7.556, -3.23, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -3.23, "curve": [8.444, -3.23, 8.889, 5.82, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.82, "curve": [9.778, 5.82, 10.222, -3.23, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -3.23, "curve": [11.111, -3.23, 11.556, 5.82, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.82, "curve": [12.335, 5.82, 12.67, 0.75, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.78}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "hand_L3": {"rotate": [{"value": 1.81, "curve": [0.114, 2.35, 0.224, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.778, 2.76, 1.222, -3.18]}, {"time": 1.6667, "value": -3.18, "curve": [2.111, -3.18, 2.556, 2.75]}, {"time": 3, "value": 2.76, "curve": [3.444, 2.76, 3.889, -3.18]}, {"time": 4.3333, "value": -3.18, "curve": [4.778, -3.18, 5.222, 2.75]}, {"time": 5.6667, "value": 2.76, "curve": [6, 2.76, 6.333, -3.18]}, {"time": 6.6667, "value": -3.18, "curve": [7.111, -3.18, 7.556, 2.75]}, {"time": 8, "value": 2.76, "curve": [8.444, 2.76, 8.889, -3.18]}, {"time": 9.3333, "value": -3.18, "curve": [9.778, -3.18, 10.222, 2.75]}, {"time": 10.6667, "value": 2.76, "curve": [11.111, 2.76, 11.556, -3.18]}, {"time": 12, "value": -3.18, "curve": [12.335, -3.18, 12.67, 0.14]}, {"time": 13, "value": 1.81}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "arm_R3": {"rotate": [{"value": 1.82, "curve": [0.225, 3.43, 0.446, 5.06]}, {"time": 0.6667, "value": 5.06, "curve": [1.111, 5.06, 1.556, -1.42]}, {"time": 2, "value": -1.42, "curve": [2.444, -1.42, 2.889, 5.06]}, {"time": 3.3333, "value": 5.06, "curve": [3.778, 5.06, 4.222, -1.42]}, {"time": 4.6667, "value": -1.42, "curve": [5.111, -1.42, 5.556, 5.06]}, {"time": 6, "value": 5.06, "curve": [6.333, 5.06, 6.667, 11.9]}, {"time": 7, "value": 11.9, "curve": [7.444, 11.9, 7.889, 8.48]}, {"time": 8.3333, "value": 8.48, "curve": [8.778, 8.48, 9.222, 11.9]}, {"time": 9.6667, "value": 11.9, "curve": [10.111, 11.9, 10.556, 5.06]}, {"time": 11, "value": 5.06, "curve": [11.444, 5.06, 11.889, -1.42]}, {"time": 12.3333, "value": -1.42, "curve": [12.557, -1.42, 12.781, 0.19]}, {"time": 13, "value": 1.82}]}, "RU_L": {"translate": [{"x": -11.63, "curve": [0.168, -22.84, 0.334, -32.04, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -32.04, "curve": [0.944, -32.04, 1.389, 32.11, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 32.13, "curve": [2.278, 32.15, 2.722, -32.03, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -32.04, "curve": [3.611, -32.06, 4.056, 32.11, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 32.13, "curve": [4.944, 32.15, 5.389, -32.02, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -32.04, "curve": [6.167, -32.06, 6.5, 32.12, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 32.13, "curve": [7.278, 32.15, 7.722, -32.03, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -32.04, "curve": [8.611, -32.06, 9.056, 32.11, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 32.13, "curve": [9.944, 32.15, 10.389, -32.03, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -32.04, "curve": [11.278, -32.06, 11.722, 32.11, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 32.13, "curve": [12.445, 32.14, 12.724, 7.19, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -11.63}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.167, 1.012, 0.333, 0.926, 0.167, 0.992, 0.333, 1.04]}, {"time": 0.5, "x": 0.926, "y": 1.04, "curve": [0.722, 0.926, 0.944, 1.079, 0.722, 1.04, 0.944, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955, "curve": [1.389, 1.079, 1.611, 0.926, 1.389, 0.955, 1.611, 1.04]}, {"time": 1.8333, "x": 0.926, "y": 1.04, "curve": [2.056, 0.926, 2.278, 1.079, 2.056, 1.04, 2.278, 0.955]}, {"time": 2.5, "x": 1.079, "y": 0.955, "curve": [2.722, 1.079, 2.944, 0.926, 2.722, 0.955, 2.944, 1.04]}, {"time": 3.1667, "x": 0.926, "y": 1.04, "curve": [3.389, 0.926, 3.611, 1.079, 3.389, 1.04, 3.611, 0.955]}, {"time": 3.8333, "x": 1.079, "y": 0.955, "curve": [4.056, 1.079, 4.278, 0.926, 4.056, 0.955, 4.278, 1.04]}, {"time": 4.5, "x": 0.926, "y": 1.04, "curve": [4.722, 0.926, 4.944, 1.079, 4.722, 1.04, 4.944, 0.955]}, {"time": 5.1667, "x": 1.079, "y": 0.955, "curve": [5.389, 1.079, 5.611, 0.926, 5.389, 0.955, 5.611, 1.04]}, {"time": 5.8333, "x": 0.926, "y": 1.04, "curve": [6, 0.926, 6.167, 1.079, 6, 1.04, 6.167, 0.955]}, {"time": 6.3333, "x": 1.079, "y": 0.955, "curve": [6.5, 1.079, 6.667, 0.926, 6.5, 0.955, 6.667, 1.04]}, {"time": 6.8333, "x": 0.926, "y": 1.04, "curve": [7.056, 0.926, 7.278, 1.079, 7.056, 1.04, 7.278, 0.955]}, {"time": 7.5, "x": 1.079, "y": 0.955, "curve": [7.722, 1.079, 7.944, 0.926, 7.722, 0.955, 7.944, 1.04]}, {"time": 8.1667, "x": 0.926, "y": 1.04, "curve": [8.389, 0.926, 8.611, 1.079, 8.389, 1.04, 8.611, 0.955]}, {"time": 8.8333, "x": 1.079, "y": 0.955, "curve": [9.056, 1.079, 9.278, 0.926, 9.056, 0.955, 9.278, 1.04]}, {"time": 9.5, "x": 0.926, "y": 1.04, "curve": [9.722, 0.926, 9.944, 1.079, 9.722, 1.04, 9.944, 0.955]}, {"time": 10.1667, "x": 1.079, "y": 0.955, "curve": [10.389, 1.079, 10.611, 0.926, 10.389, 0.955, 10.611, 1.04]}, {"time": 10.8333, "x": 0.926, "y": 1.04, "curve": [11.056, 0.926, 11.278, 1.079, 11.056, 1.04, 11.278, 0.955]}, {"time": 11.5, "x": 1.079, "y": 0.955, "curve": [11.722, 1.079, 11.944, 0.926, 11.722, 0.955, 11.944, 1.04]}, {"time": 12.1667, "x": 0.926, "y": 1.04, "curve": [12.389, 0.926, 12.611, 1.079, 12.389, 1.04, 12.611, 0.955]}, {"time": 12.8333, "x": 1.079, "y": 0.955, "curve": [12.889, 1.079, 12.944, 1.069, 12.889, 0.955, 12.944, 0.961]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_L2": {"translate": [{"x": -0.68, "curve": [0.225, -15.15, 0.446, -29.83, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -29.83, "curve": [1.111, -29.83, 1.556, 28.45, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.46, "curve": [2.444, 28.48, 2.889, -29.81, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -29.83, "curve": [3.778, -29.84, 4.222, 28.45, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.46, "curve": [5.111, 28.48, 5.556, -29.81, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -29.83, "curve": [6.333, -29.84, 6.667, 28.45, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 28.46, "curve": [7.444, 28.48, 7.889, -29.81, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -29.83, "curve": [8.778, -29.84, 9.222, 28.45, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.46, "curve": [10.111, 28.48, 10.556, -29.81, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -29.83, "curve": [11.444, -29.84, 11.889, 28.45, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.46, "curve": [12.557, 28.47, 12.781, 13.99, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -0.68}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.222, 1.079, 0.444, 0.926, 0.222, 0.955, 0.444, 1.04]}, {"time": 0.6667, "x": 0.926, "y": 1.04, "curve": [0.889, 0.926, 1.111, 1.079, 0.889, 1.04, 1.111, 0.955]}, {"time": 1.3333, "x": 1.079, "y": 0.955, "curve": [1.556, 1.079, 1.778, 0.926, 1.556, 0.955, 1.778, 1.04]}, {"time": 2, "x": 0.926, "y": 1.04, "curve": [2.222, 0.926, 2.444, 1.079, 2.222, 1.04, 2.444, 0.955]}, {"time": 2.6667, "x": 1.079, "y": 0.955, "curve": [2.889, 1.079, 3.111, 0.926, 2.889, 0.955, 3.111, 1.04]}, {"time": 3.3333, "x": 0.926, "y": 1.04, "curve": [3.556, 0.926, 3.778, 1.079, 3.556, 1.04, 3.778, 0.955]}, {"time": 4, "x": 1.079, "y": 0.955, "curve": [4.222, 1.079, 4.444, 0.926, 4.222, 0.955, 4.444, 1.04]}, {"time": 4.6667, "x": 0.926, "y": 1.04, "curve": [4.889, 0.926, 5.111, 1.079, 4.889, 1.04, 5.111, 0.955]}, {"time": 5.3333, "x": 1.079, "y": 0.955, "curve": [5.556, 1.079, 5.778, 0.926, 5.556, 0.955, 5.778, 1.04]}, {"time": 6, "x": 0.926, "y": 1.04, "curve": [6.167, 0.926, 6.333, 1.079, 6.167, 1.04, 6.333, 0.955]}, {"time": 6.5, "x": 1.079, "y": 0.955, "curve": [6.667, 1.079, 6.833, 0.926, 6.667, 0.955, 6.833, 1.04]}, {"time": 7, "x": 0.926, "y": 1.04, "curve": [7.222, 0.926, 7.444, 1.079, 7.222, 1.04, 7.444, 0.955]}, {"time": 7.6667, "x": 1.079, "y": 0.955, "curve": [7.889, 1.079, 8.111, 0.926, 7.889, 0.955, 8.111, 1.04]}, {"time": 8.3333, "x": 0.926, "y": 1.04, "curve": [8.556, 0.926, 8.778, 1.079, 8.556, 1.04, 8.778, 0.955]}, {"time": 9, "x": 1.079, "y": 0.955, "curve": [9.222, 1.079, 9.444, 0.926, 9.222, 0.955, 9.444, 1.04]}, {"time": 9.6667, "x": 0.926, "y": 1.04, "curve": [9.889, 0.926, 10.111, 1.079, 9.889, 1.04, 10.111, 0.955]}, {"time": 10.3333, "x": 1.079, "y": 0.955, "curve": [10.556, 1.079, 10.778, 0.926, 10.556, 0.955, 10.778, 1.04]}, {"time": 11, "x": 0.926, "y": 1.04, "curve": [11.222, 0.926, 11.444, 1.079, 11.222, 1.04, 11.444, 0.955]}, {"time": 11.6667, "x": 1.079, "y": 0.955, "curve": [11.889, 1.079, 12.111, 0.926, 11.889, 0.955, 12.111, 1.04]}, {"time": 12.3333, "x": 0.926, "y": 1.04, "curve": [12.556, 0.926, 12.778, 1.079, 12.556, 1.04, 12.778, 0.955]}, {"time": 13, "x": 1.079, "y": 0.955}]}, "RU_L3": {"translate": [{"x": 10.34, "curve": [0.279, -4.98, 0.556, -25.36, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.36, "curve": [1.278, -25.36, 1.722, 26.97, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 26.98, "curve": [2.611, 27, 3.056, -25.35, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.36, "curve": [3.944, -25.38, 4.389, 26.97, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 26.98, "curve": [5.278, 27, 5.722, -25.35, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.36, "curve": [6.5, -25.38, 6.833, 26.97, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 26.98, "curve": [7.611, 27, 8.056, -25.35, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -25.36, "curve": [8.944, -25.38, 9.389, 26.97, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 26.98, "curve": [10.278, 27, 10.722, -25.35, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.36, "curve": [11.611, -25.38, 12.056, 26.97, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 26.98, "curve": [12.667, 26.99, 12.835, 19.6, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.34}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.056, 1.069, 0.111, 1.079, 0.056, 0.961, 0.111, 0.955]}, {"time": 0.1667, "x": 1.079, "y": 0.955, "curve": [0.389, 1.079, 0.611, 0.926, 0.389, 0.955, 0.611, 1.04]}, {"time": 0.8333, "x": 0.926, "y": 1.04, "curve": [1.056, 0.926, 1.278, 1.079, 1.056, 1.04, 1.278, 0.955]}, {"time": 1.5, "x": 1.079, "y": 0.955, "curve": [1.722, 1.079, 1.944, 0.926, 1.722, 0.955, 1.944, 1.04]}, {"time": 2.1667, "x": 0.926, "y": 1.04, "curve": [2.389, 0.926, 2.611, 1.079, 2.389, 1.04, 2.611, 0.955]}, {"time": 2.8333, "x": 1.079, "y": 0.955, "curve": [3.056, 1.079, 3.278, 0.926, 3.056, 0.955, 3.278, 1.04]}, {"time": 3.5, "x": 0.926, "y": 1.04, "curve": [3.722, 0.926, 3.944, 1.079, 3.722, 1.04, 3.944, 0.955]}, {"time": 4.1667, "x": 1.079, "y": 0.955, "curve": [4.389, 1.079, 4.611, 0.926, 4.389, 0.955, 4.611, 1.04]}, {"time": 4.8333, "x": 0.926, "y": 1.04, "curve": [5.056, 0.926, 5.278, 1.079, 5.056, 1.04, 5.278, 0.955]}, {"time": 5.5, "x": 1.079, "y": 0.955, "curve": [5.722, 1.079, 5.944, 0.926, 5.722, 0.955, 5.944, 1.04]}, {"time": 6.1667, "x": 0.926, "y": 1.04, "curve": [6.333, 0.926, 6.5, 1.079, 6.333, 1.04, 6.5, 0.955]}, {"time": 6.6667, "x": 1.079, "y": 0.955, "curve": [6.833, 1.079, 7, 0.926, 6.833, 0.955, 7, 1.04]}, {"time": 7.1667, "x": 0.926, "y": 1.04, "curve": [7.389, 0.926, 7.611, 1.079, 7.389, 1.04, 7.611, 0.955]}, {"time": 7.8333, "x": 1.079, "y": 0.955, "curve": [8.056, 1.079, 8.278, 0.926, 8.056, 0.955, 8.278, 1.04]}, {"time": 8.5, "x": 0.926, "y": 1.04, "curve": [8.722, 0.926, 8.944, 1.079, 8.722, 1.04, 8.944, 0.955]}, {"time": 9.1667, "x": 1.079, "y": 0.955, "curve": [9.389, 1.079, 9.611, 0.926, 9.389, 0.955, 9.611, 1.04]}, {"time": 9.8333, "x": 0.926, "y": 1.04, "curve": [10.056, 0.926, 10.278, 1.079, 10.056, 1.04, 10.278, 0.955]}, {"time": 10.5, "x": 1.079, "y": 0.955, "curve": [10.722, 1.079, 10.944, 0.926, 10.722, 0.955, 10.944, 1.04]}, {"time": 11.1667, "x": 0.926, "y": 1.04, "curve": [11.389, 0.926, 11.611, 1.079, 11.389, 1.04, 11.611, 0.955]}, {"time": 11.8333, "x": 1.079, "y": 0.955, "curve": [12.056, 1.079, 12.278, 0.926, 12.056, 0.955, 12.278, 1.04]}, {"time": 12.5, "x": 0.926, "y": 1.04, "curve": [12.667, 0.926, 12.833, 1.012, 12.667, 1.04, 12.833, 0.992]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_R": {"translate": [{"x": -7.06, "curve": [0.168, -17.1, 0.334, -25.34, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -25.34, "curve": [0.944, -25.34, 1.389, 32.12, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 32.13, "curve": [2.278, 32.14, 2.722, -25.32, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -25.34, "curve": [3.611, -25.35, 4.056, 32.12, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 32.13, "curve": [4.944, 32.14, 5.389, -25.32, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -25.34, "curve": [6.167, -25.35, 6.5, 32.12, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 32.13, "curve": [7.278, 32.14, 7.722, -25.32, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -25.34, "curve": [8.611, -25.35, 9.056, 32.12, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 32.13, "curve": [9.944, 32.14, 10.389, -25.32, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -25.34, "curve": [11.278, -25.35, 11.722, 32.12, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 32.13, "curve": [12.445, 32.14, 12.724, 9.8, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -7.06}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.167, 1.012, 0.333, 0.926, 0.167, 0.992, 0.333, 1.04]}, {"time": 0.5, "x": 0.926, "y": 1.04, "curve": [0.722, 0.926, 0.944, 1.079, 0.722, 1.04, 0.944, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955, "curve": [1.389, 1.079, 1.611, 0.926, 1.389, 0.955, 1.611, 1.04]}, {"time": 1.8333, "x": 0.926, "y": 1.04, "curve": [2.056, 0.926, 2.278, 1.079, 2.056, 1.04, 2.278, 0.955]}, {"time": 2.5, "x": 1.079, "y": 0.955, "curve": [2.722, 1.079, 2.944, 0.926, 2.722, 0.955, 2.944, 1.04]}, {"time": 3.1667, "x": 0.926, "y": 1.04, "curve": [3.389, 0.926, 3.611, 1.079, 3.389, 1.04, 3.611, 0.955]}, {"time": 3.8333, "x": 1.079, "y": 0.955, "curve": [4.056, 1.079, 4.278, 0.926, 4.056, 0.955, 4.278, 1.04]}, {"time": 4.5, "x": 0.926, "y": 1.04, "curve": [4.722, 0.926, 4.944, 1.079, 4.722, 1.04, 4.944, 0.955]}, {"time": 5.1667, "x": 1.079, "y": 0.955, "curve": [5.389, 1.079, 5.611, 0.926, 5.389, 0.955, 5.611, 1.04]}, {"time": 5.8333, "x": 0.926, "y": 1.04, "curve": [6, 0.926, 6.167, 1.079, 6, 1.04, 6.167, 0.955]}, {"time": 6.3333, "x": 1.079, "y": 0.955, "curve": [6.5, 1.079, 6.667, 0.926, 6.5, 0.955, 6.667, 1.04]}, {"time": 6.8333, "x": 0.926, "y": 1.04, "curve": [7.056, 0.926, 7.278, 1.079, 7.056, 1.04, 7.278, 0.955]}, {"time": 7.5, "x": 1.079, "y": 0.955, "curve": [7.722, 1.079, 7.944, 0.926, 7.722, 0.955, 7.944, 1.04]}, {"time": 8.1667, "x": 0.926, "y": 1.04, "curve": [8.389, 0.926, 8.611, 1.079, 8.389, 1.04, 8.611, 0.955]}, {"time": 8.8333, "x": 1.079, "y": 0.955, "curve": [9.056, 1.079, 9.278, 0.926, 9.056, 0.955, 9.278, 1.04]}, {"time": 9.5, "x": 0.926, "y": 1.04, "curve": [9.722, 0.926, 9.944, 1.079, 9.722, 1.04, 9.944, 0.955]}, {"time": 10.1667, "x": 1.079, "y": 0.955, "curve": [10.389, 1.079, 10.611, 0.926, 10.389, 0.955, 10.611, 1.04]}, {"time": 10.8333, "x": 0.926, "y": 1.04, "curve": [11.056, 0.926, 11.278, 1.079, 11.056, 1.04, 11.278, 0.955]}, {"time": 11.5, "x": 1.079, "y": 0.955, "curve": [11.722, 1.079, 11.944, 0.926, 11.722, 0.955, 11.944, 1.04]}, {"time": 12.1667, "x": 0.926, "y": 1.04, "curve": [12.389, 0.926, 12.611, 1.079, 12.389, 1.04, 12.611, 0.955]}, {"time": 12.8333, "x": 1.079, "y": 0.955, "curve": [12.889, 1.079, 12.944, 1.069, 12.889, 0.955, 12.944, 0.961]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_R2": {"translate": [{"x": 3.05, "curve": [0.225, -9.58, 0.446, -22.38, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -22.38, "curve": [1.111, -22.38, 1.556, 28.45, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.46, "curve": [2.444, 28.48, 2.889, -22.36, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -22.38, "curve": [3.778, -22.39, 4.222, 28.45, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.46, "curve": [5.111, 28.48, 5.556, -22.36, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -22.38, "curve": [6.333, -22.39, 6.667, 28.45, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 28.46, "curve": [7.444, 28.48, 7.889, -22.36, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -22.38, "curve": [8.778, -22.39, 9.222, 28.45, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.46, "curve": [10.111, 28.48, 10.556, -22.36, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -22.38, "curve": [11.444, -22.39, 11.889, 28.45, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.46, "curve": [12.557, 28.47, 12.781, 15.84, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 3.05}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.222, 1.079, 0.444, 0.926, 0.222, 0.955, 0.444, 1.04]}, {"time": 0.6667, "x": 0.926, "y": 1.04, "curve": [0.889, 0.926, 1.111, 1.079, 0.889, 1.04, 1.111, 0.955]}, {"time": 1.3333, "x": 1.079, "y": 0.955, "curve": [1.556, 1.079, 1.778, 0.926, 1.556, 0.955, 1.778, 1.04]}, {"time": 2, "x": 0.926, "y": 1.04, "curve": [2.222, 0.926, 2.444, 1.079, 2.222, 1.04, 2.444, 0.955]}, {"time": 2.6667, "x": 1.079, "y": 0.955, "curve": [2.889, 1.079, 3.111, 0.926, 2.889, 0.955, 3.111, 1.04]}, {"time": 3.3333, "x": 0.926, "y": 1.04, "curve": [3.556, 0.926, 3.778, 1.079, 3.556, 1.04, 3.778, 0.955]}, {"time": 4, "x": 1.079, "y": 0.955, "curve": [4.222, 1.079, 4.444, 0.926, 4.222, 0.955, 4.444, 1.04]}, {"time": 4.6667, "x": 0.926, "y": 1.04, "curve": [4.889, 0.926, 5.111, 1.079, 4.889, 1.04, 5.111, 0.955]}, {"time": 5.3333, "x": 1.079, "y": 0.955, "curve": [5.556, 1.079, 5.778, 0.926, 5.556, 0.955, 5.778, 1.04]}, {"time": 6, "x": 0.926, "y": 1.04, "curve": [6.167, 0.926, 6.333, 1.079, 6.167, 1.04, 6.333, 0.955]}, {"time": 6.5, "x": 1.079, "y": 0.955, "curve": [6.667, 1.079, 6.833, 0.926, 6.667, 0.955, 6.833, 1.04]}, {"time": 7, "x": 0.926, "y": 1.04, "curve": [7.222, 0.926, 7.444, 1.079, 7.222, 1.04, 7.444, 0.955]}, {"time": 7.6667, "x": 1.079, "y": 0.955, "curve": [7.889, 1.079, 8.111, 0.926, 7.889, 0.955, 8.111, 1.04]}, {"time": 8.3333, "x": 0.926, "y": 1.04, "curve": [8.556, 0.926, 8.778, 1.079, 8.556, 1.04, 8.778, 0.955]}, {"time": 9, "x": 1.079, "y": 0.955, "curve": [9.222, 1.079, 9.444, 0.926, 9.222, 0.955, 9.444, 1.04]}, {"time": 9.6667, "x": 0.926, "y": 1.04, "curve": [9.889, 0.926, 10.111, 1.079, 9.889, 1.04, 10.111, 0.955]}, {"time": 10.3333, "x": 1.079, "y": 0.955, "curve": [10.556, 1.079, 10.778, 0.926, 10.556, 0.955, 10.778, 1.04]}, {"time": 11, "x": 0.926, "y": 1.04, "curve": [11.222, 0.926, 11.444, 1.079, 11.222, 1.04, 11.444, 0.955]}, {"time": 11.6667, "x": 1.079, "y": 0.955, "curve": [11.889, 1.079, 12.111, 0.926, 11.889, 0.955, 12.111, 1.04]}, {"time": 12.3333, "x": 0.926, "y": 1.04, "curve": [12.556, 0.926, 12.778, 1.079, 12.556, 1.04, 12.778, 0.955]}, {"time": 13, "x": 1.079, "y": 0.955}]}, "RU_R3": {"translate": [{"x": 12.95, "curve": [0.279, 0.03, 0.556, -17.16, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -17.16, "curve": [1.278, -17.16, 1.722, 26.97, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 26.98, "curve": [2.611, 26.99, 3.056, -17.15, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -17.16, "curve": [3.944, -17.17, 4.389, 26.97, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 26.98, "curve": [5.278, 26.99, 5.722, -17.15, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -17.16, "curve": [6.5, -17.17, 6.833, 26.97, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 26.98, "curve": [7.611, 26.99, 8.056, -17.15, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -17.16, "curve": [8.944, -17.17, 9.389, 26.97, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 26.98, "curve": [10.278, 26.99, 10.722, -17.15, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -17.16, "curve": [11.611, -17.17, 12.056, 26.97, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 26.98, "curve": [12.667, 26.99, 12.835, 20.76, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.95}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.056, 1.069, 0.111, 1.079, 0.056, 0.961, 0.111, 0.955]}, {"time": 0.1667, "x": 1.079, "y": 0.955, "curve": [0.389, 1.079, 0.611, 0.926, 0.389, 0.955, 0.611, 1.04]}, {"time": 0.8333, "x": 0.926, "y": 1.04, "curve": [1.056, 0.926, 1.278, 1.079, 1.056, 1.04, 1.278, 0.955]}, {"time": 1.5, "x": 1.079, "y": 0.955, "curve": [1.722, 1.079, 1.944, 0.926, 1.722, 0.955, 1.944, 1.04]}, {"time": 2.1667, "x": 0.926, "y": 1.04, "curve": [2.389, 0.926, 2.611, 1.079, 2.389, 1.04, 2.611, 0.955]}, {"time": 2.8333, "x": 1.079, "y": 0.955, "curve": [3.056, 1.079, 3.278, 0.926, 3.056, 0.955, 3.278, 1.04]}, {"time": 3.5, "x": 0.926, "y": 1.04, "curve": [3.722, 0.926, 3.944, 1.079, 3.722, 1.04, 3.944, 0.955]}, {"time": 4.1667, "x": 1.079, "y": 0.955, "curve": [4.389, 1.079, 4.611, 0.926, 4.389, 0.955, 4.611, 1.04]}, {"time": 4.8333, "x": 0.926, "y": 1.04, "curve": [5.056, 0.926, 5.278, 1.079, 5.056, 1.04, 5.278, 0.955]}, {"time": 5.5, "x": 1.079, "y": 0.955, "curve": [5.722, 1.079, 5.944, 0.926, 5.722, 0.955, 5.944, 1.04]}, {"time": 6.1667, "x": 0.926, "y": 1.04, "curve": [6.333, 0.926, 6.5, 1.079, 6.333, 1.04, 6.5, 0.955]}, {"time": 6.6667, "x": 1.079, "y": 0.955, "curve": [6.833, 1.079, 7, 0.926, 6.833, 0.955, 7, 1.04]}, {"time": 7.1667, "x": 0.926, "y": 1.04, "curve": [7.389, 0.926, 7.611, 1.079, 7.389, 1.04, 7.611, 0.955]}, {"time": 7.8333, "x": 1.079, "y": 0.955, "curve": [8.056, 1.079, 8.278, 0.926, 8.056, 0.955, 8.278, 1.04]}, {"time": 8.5, "x": 0.926, "y": 1.04, "curve": [8.722, 0.926, 8.944, 1.079, 8.722, 1.04, 8.944, 0.955]}, {"time": 9.1667, "x": 1.079, "y": 0.955, "curve": [9.389, 1.079, 9.611, 0.926, 9.389, 0.955, 9.611, 1.04]}, {"time": 9.8333, "x": 0.926, "y": 1.04, "curve": [10.056, 0.926, 10.278, 1.079, 10.056, 1.04, 10.278, 0.955]}, {"time": 10.5, "x": 1.079, "y": 0.955, "curve": [10.722, 1.079, 10.944, 0.926, 10.722, 0.955, 10.944, 1.04]}, {"time": 11.1667, "x": 0.926, "y": 1.04, "curve": [11.389, 0.926, 11.611, 1.079, 11.389, 1.04, 11.611, 0.955]}, {"time": 11.8333, "x": 1.079, "y": 0.955, "curve": [12.056, 1.079, 12.278, 0.926, 12.056, 0.955, 12.278, 1.04]}, {"time": 12.5, "x": 0.926, "y": 1.04, "curve": [12.667, 0.926, 12.833, 1.012, 12.667, 1.04, 12.833, 0.992]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "leg_L2": {"rotate": [{"value": -0.7, "curve": [0.444, -0.7, 0.889, 0.76]}, {"time": 1.3333, "value": 0.76, "curve": [1.778, 0.76, 2.222, -0.7]}, {"time": 2.6667, "value": -0.7, "curve": [3.111, -0.7, 3.556, 0.76]}, {"time": 4, "value": 0.76, "curve": [4.444, 0.76, 4.889, -0.7]}, {"time": 5.3333, "value": -0.7, "curve": [5.667, -0.7, 6, 0.76]}, {"time": 6.3333, "value": 0.76, "curve": [6.778, 0.76, 7.222, -0.7]}, {"time": 7.6667, "value": -0.7, "curve": [8.111, -0.7, 8.556, 0.76]}, {"time": 9, "value": 0.76, "curve": [9.444, 0.76, 9.889, -0.7]}, {"time": 10.3333, "value": -0.7, "curve": [10.778, -0.7, 11.222, 0.76]}, {"time": 11.6667, "value": 0.76, "curve": [12.111, 0.76, 12.556, -0.7]}, {"time": 13, "value": -0.7}]}, "leg_L3": {"rotate": [{"value": -1.43, "curve": [0.114, -1.75, 0.224, -1.98]}, {"time": 0.3333, "value": -1.98, "curve": [0.778, -1.98, 1.222, 1.47]}, {"time": 1.6667, "value": 1.47, "curve": [2.111, 1.47, 2.556, -1.98]}, {"time": 3, "value": -1.98, "curve": [3.444, -1.98, 3.889, 1.47]}, {"time": 4.3333, "value": 1.47, "curve": [4.778, 1.47, 5.222, -1.98]}, {"time": 5.6667, "value": -1.98, "curve": [6, -1.98, 6.333, 1.47]}, {"time": 6.6667, "value": 1.47, "curve": [7.111, 1.47, 7.556, -1.98]}, {"time": 8, "value": -1.98, "curve": [8.444, -1.98, 8.889, 1.47]}, {"time": 9.3333, "value": 1.47, "curve": [9.778, 1.47, 10.222, -1.98]}, {"time": 10.6667, "value": -1.98, "curve": [11.111, -1.98, 11.556, 1.47]}, {"time": 12, "value": 1.47, "curve": [12.335, 1.47, 12.67, -0.47]}, {"time": 13, "value": -1.43}]}, "leg_L4": {"rotate": [{"value": -1.19, "curve": [0.168, -2.73, 0.334, -4]}, {"time": 0.5, "value": -4, "curve": [0.944, -4, 1.389, 4.85]}, {"time": 1.8333, "value": 4.85, "curve": [2.278, 4.85, 2.722, -4]}, {"time": 3.1667, "value": -4, "curve": [3.611, -4, 4.056, 4.85]}, {"time": 4.5, "value": 4.85, "curve": [4.944, 4.85, 5.389, -4]}, {"time": 5.8333, "value": -4, "curve": [6.167, -4, 6.5, 4.85]}, {"time": 6.8333, "value": 4.85, "curve": [7.278, 4.85, 7.722, -4]}, {"time": 8.1667, "value": -4, "curve": [8.611, -4, 9.056, 4.85]}, {"time": 9.5, "value": 4.85, "curve": [9.944, 4.85, 10.389, -4]}, {"time": 10.8333, "value": -4, "curve": [11.278, -4, 11.722, 4.85]}, {"time": 12.1667, "value": 4.85, "curve": [12.445, 4.85, 12.724, 1.41]}, {"time": 13, "value": -1.19}]}, "leg_L5": {"rotate": [{"value": 0.43, "curve": [0.225, -1.77, 0.446, -4]}, {"time": 0.6667, "value": -4, "curve": [1.111, -4, 1.556, 4.85]}, {"time": 2, "value": 4.85, "curve": [2.444, 4.85, 2.889, -4]}, {"time": 3.3333, "value": -4, "curve": [3.778, -4, 4.222, 4.85]}, {"time": 4.6667, "value": 4.85, "curve": [5.111, 4.85, 5.556, -4]}, {"time": 6, "value": -4, "curve": [6.333, -4, 6.667, 4.85]}, {"time": 7, "value": 4.85, "curve": [7.444, 4.85, 7.889, -4]}, {"time": 8.3333, "value": -4, "curve": [8.778, -4, 9.222, 4.85]}, {"time": 9.6667, "value": 4.85, "curve": [10.111, 4.85, 10.556, -4]}, {"time": 11, "value": -4, "curve": [11.444, -4, 11.889, 4.85]}, {"time": 12.3333, "value": 4.85, "curve": [12.557, 4.85, 12.781, 2.65]}, {"time": 13, "value": 0.43}]}, "leg_R2": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "foot_R": {"rotate": [{"value": 6.84, "curve": [0.444, 6.84, 0.889, -4.44]}, {"time": 1.3333, "value": -4.44, "curve": [1.778, -4.44, 2.222, 6.83]}, {"time": 2.6667, "value": 6.84, "curve": [3.111, 6.84, 3.556, -4.44]}, {"time": 4, "value": -4.44, "curve": [4.444, -4.44, 4.889, 6.83]}, {"time": 5.3333, "value": 6.84, "curve": [5.667, 6.84, 6, -4.44]}, {"time": 6.3333, "value": -4.44, "curve": [6.778, -4.44, 7.222, 6.83]}, {"time": 7.6667, "value": 6.84, "curve": [8.111, 6.84, 8.556, -4.44]}, {"time": 9, "value": -4.44, "curve": [9.444, -4.44, 9.889, 6.83]}, {"time": 10.3333, "value": 6.84, "curve": [10.778, 6.84, 11.222, -4.44]}, {"time": 11.6667, "value": -4.44, "curve": [12.111, -4.44, 12.556, 6.84]}, {"time": 13, "value": 6.84}]}, "foot_R2": {"rotate": [{"value": -3.01, "curve": [0.444, -3.01, 0.889, 2.17]}, {"time": 1.3333, "value": 2.17, "curve": [1.778, 2.17, 2.222, -3]}, {"time": 2.6667, "value": -3.01, "curve": [3.111, -3.01, 3.556, 2.17]}, {"time": 4, "value": 2.17, "curve": [4.444, 2.17, 4.889, -3]}, {"time": 5.3333, "value": -3.01, "curve": [5.667, -3.01, 6, 2.17]}, {"time": 6.3333, "value": 2.17, "curve": [6.778, 2.17, 7.222, -3]}, {"time": 7.6667, "value": -3.01, "curve": [8.111, -3.01, 8.556, 2.17]}, {"time": 9, "value": 2.17, "curve": [9.444, 2.17, 9.889, -3]}, {"time": 10.3333, "value": -3.01, "curve": [10.778, -3.01, 11.222, 2.17]}, {"time": 11.6667, "value": 2.17, "curve": [12.111, 2.17, 12.556, -3.01]}, {"time": 13, "value": -3.01}]}, "wine2": {"translate": [{"x": 0.11, "y": 0.06, "curve": [0.225, 0.06, 0.446, 0, 0.225, 0.03, 0.446, 0]}, {"time": 0.6667, "curve": [1.111, 0, 1.556, 0.23, 1.111, 0, 1.556, 0.12]}, {"time": 2, "x": 0.23, "y": 0.12, "curve": [2.444, 0.23, 2.889, 0, 2.444, 0.12, 2.889, 0]}, {"time": 3.3333, "curve": [3.778, 0, 4.222, 0.23, 3.778, 0, 4.222, 0.12]}, {"time": 4.6667, "x": 0.23, "y": 0.12, "curve": [5.111, 0.23, 5.556, 0, 5.111, 0.12, 5.556, 0]}, {"time": 6, "curve": [6.333, 0, 6.667, 0.23, 6.333, 0, 6.667, 0.12]}, {"time": 7, "x": 0.23, "y": 0.12, "curve": [7.444, 0.23, 7.889, -0.21, 7.444, 0.12, 7.889, 0.47]}, {"time": 8.3333, "x": -0.21, "y": 0.47, "curve": [8.778, -0.21, 9.222, 0.23, 8.778, 0.47, 9.222, 0.12]}, {"time": 9.6667, "x": 0.23, "y": 0.12, "curve": [10.111, 0.23, 10.556, 0, 10.111, 0.12, 10.556, 0]}, {"time": 11, "curve": [11.444, 0, 11.889, 0.23, 11.444, 0, 11.889, 0.12]}, {"time": 12.3333, "x": 0.23, "y": 0.12, "curve": [12.557, 0.23, 12.781, 0.17, 12.557, 0.12, 12.781, 0.09]}, {"time": 13, "x": 0.11, "y": 0.06}], "scale": [{"x": 1.005, "curve": [0.225, 0.997, 0.446, 0.989, 0.225, 1, 0.446, 1]}, {"time": 0.6667, "x": 0.989, "curve": [1.111, 0.989, 1.556, 1.022, 1.111, 1, 1.556, 1]}, {"time": 2, "x": 1.022, "curve": [2.444, 1.022, 2.889, 0.989, 2.444, 1, 2.889, 1]}, {"time": 3.3333, "x": 0.989, "curve": [3.778, 0.989, 4.222, 1.022, 3.778, 1, 4.222, 1]}, {"time": 4.6667, "x": 1.022, "curve": [5.111, 1.022, 5.556, 0.989, 5.111, 1, 5.556, 1]}, {"time": 6, "x": 0.989, "curve": [6.333, 0.989, 6.667, 0.952, 6.333, 1, 6.667, 1]}, {"time": 7, "x": 0.952, "curve": [7.444, 0.952, 7.889, 0.961, 7.444, 1, 7.889, 1]}, {"time": 8.3333, "x": 0.961, "curve": [8.778, 0.961, 9.222, 0.952, 8.778, 1, 9.222, 1]}, {"time": 9.6667, "x": 0.952, "curve": [10.111, 0.952, 10.556, 0.989, 10.111, 1, 10.556, 1]}, {"time": 11, "x": 0.989, "curve": [11.444, 0.989, 11.889, 1.022, 11.444, 1, 11.889, 1]}, {"time": 12.3333, "x": 1.022, "curve": [12.557, 1.022, 12.781, 1.014, 12.557, 1, 12.781, 1]}, {"time": 13, "x": 1.005}]}, "wine3": {"rotate": [{"value": -69.09}, {"time": 1.3, "value": -420.09, "curve": "stepped"}, {"time": 1.3333, "value": -69.09}, {"time": 2.6333, "value": -420.09, "curve": "stepped"}, {"time": 2.6667, "value": -69.09}, {"time": 3.9667, "value": -420.09, "curve": "stepped"}, {"time": 4, "value": -69.09}, {"time": 5.3, "value": -420.09, "curve": "stepped"}, {"time": 5.3333, "value": -69.09}, {"time": 6.3, "value": -417.09, "curve": "stepped"}, {"time": 6.3333, "value": -69.09}, {"time": 7.6333, "value": -420.09, "curve": "stepped"}, {"time": 7.6667, "value": -69.09}, {"time": 8.9667, "value": -420.09, "curve": "stepped"}, {"time": 9, "value": -69.09}, {"time": 10.3, "value": -420.09, "curve": "stepped"}, {"time": 10.3333, "value": -69.09}, {"time": 11.6333, "value": -420.09, "curve": "stepped"}, {"time": 11.6667, "value": -69.09}, {"time": 12.9667, "value": -420.09, "curve": "stepped"}, {"time": 13, "value": -69.09}]}, "head4": {"translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.92, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.92, "curve": "stepped"}, {"time": 9.5, "x": 1.92, "curve": [9.722, 1.92, 9.944, 0, 9.585, 0, 10.082, 0]}, {"time": 10.1667}]}, "head5": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, -3.41]}, {"time": 6.3333, "value": -3.41, "curve": "stepped"}, {"time": 9.5, "value": -3.41, "curve": [9.722, -3.41, 9.944, 0]}, {"time": 10.1667}], "scale": [{"time": 6, "curve": [6.111, 1, 6.222, 0.988, 6.148, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.988, "curve": "stepped"}, {"time": 9.5, "x": 0.988, "curve": [9.722, 0.988, 9.944, 1, 9.585, 1, 10.082, 1]}, {"time": 10.1667}]}, "head6": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, -3.12]}, {"time": 6.3333, "value": -3.12, "curve": "stepped"}, {"time": 9.5, "value": -3.12, "curve": [9.722, -3.12, 9.944, 0]}, {"time": 10.1667}]}, "head7": {"translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.92, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.92, "curve": "stepped"}, {"time": 9.5, "x": 1.92, "curve": [9.722, 1.92, 9.944, 0, 9.585, 0, 10.082, 0]}, {"time": 10.1667}]}, "head8": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 6.16]}, {"time": 6.3333, "value": 6.16, "curve": "stepped"}, {"time": 9.5, "value": 6.16, "curve": [9.722, 6.16, 9.944, 0]}, {"time": 10.1667}], "scale": [{"time": 6, "curve": [6.111, 1, 6.222, 0.988, 6.148, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.988, "curve": "stepped"}, {"time": 9.5, "x": 0.988, "curve": [9.722, 0.988, 9.944, 1, 9.585, 1, 10.082, 1]}, {"time": 10.1667}]}, "head9": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 3.31]}, {"time": 6.3333, "value": 3.31, "curve": "stepped"}, {"time": 9.5, "value": 3.31, "curve": [9.722, 3.31, 9.944, 0]}, {"time": 10.1667}]}, "hair_F": {"rotate": [{"value": 2.67, "curve": [0.279, 0.09, 0.556, -3.34]}, {"time": 0.8333, "value": -3.34, "curve": [1.278, -3.34, 1.722, 5.47]}, {"time": 2.1667, "value": 5.47, "curve": [2.611, 5.48, 3.056, -3.34]}, {"time": 3.5, "value": -3.34, "curve": [3.944, -3.34, 4.389, 5.47]}, {"time": 4.8333, "value": 5.47, "curve": [5.278, 5.48, 5.722, -3.34]}, {"time": 6.1667, "value": -3.34, "curve": [6.5, -3.34, 6.833, 5.47]}, {"time": 7.1667, "value": 5.47, "curve": [7.611, 5.48, 8.056, -3.34]}, {"time": 8.5, "value": -3.34, "curve": [8.944, -3.34, 9.389, 5.47]}, {"time": 9.8333, "value": 5.47, "curve": [10.278, 5.48, 10.722, -3.34]}, {"time": 11.1667, "value": -3.34, "curve": [11.611, -3.34, 12.056, 5.47]}, {"time": 12.5, "value": 5.47, "curve": [12.667, 5.48, 12.835, 4.23]}, {"time": 13, "value": 2.67}]}, "hair_B": {"rotate": [{"value": 4.07, "curve": [0.336, 1.58, 0.668, -3.34]}, {"time": 1, "value": -3.34, "curve": [1.444, -3.34, 1.889, 5.47]}, {"time": 2.3333, "value": 5.47, "curve": [2.778, 5.48, 3.222, -3.34]}, {"time": 3.6667, "value": -3.34, "curve": [4.111, -3.34, 4.556, 5.47]}, {"time": 5, "value": 5.47, "curve": [5.444, 5.48, 5.889, -3.34]}, {"time": 6.3333, "value": -3.34, "curve": [6.667, -3.34, 7, 5.47]}, {"time": 7.3333, "value": 5.47, "curve": [7.778, 5.48, 8.222, -3.34]}, {"time": 8.6667, "value": -3.34, "curve": [9.111, -3.34, 9.556, 5.47]}, {"time": 10, "value": 5.47, "curve": [10.444, 5.48, 10.889, -3.34]}, {"time": 11.3333, "value": -3.34, "curve": [11.778, -3.34, 12.222, 5.47]}, {"time": 12.6667, "value": 5.47, "curve": [12.779, 5.48, 12.892, 4.91]}, {"time": 13, "value": 4.07}]}, "headround3": {"translate": [{"x": 28.66, "curve": [0.279, -43.33, 0.556, -139.1, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -139.1, "curve": [1.278, -139.1, 1.722, 106.79, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 106.85, "curve": [2.611, 106.91, 3.056, -139.03, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -139.1, "curve": [3.944, -139.16, 4.389, 106.79, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 106.85, "curve": [5.278, 106.91, 5.722, -139.01, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -139.1, "curve": [6.5, -139.16, 6.833, 106.83, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 106.85, "curve": [7.611, 106.86, 8.056, 41.1, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 41.08, "curve": [8.944, 41.06, 9.389, 106.79, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 106.85, "curve": [10.278, 106.91, 10.722, -139.03, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -139.1, "curve": [11.611, -139.16, 12.056, 106.79, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 106.85, "curve": [12.667, 106.87, 12.835, 72.17, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 28.66}]}, "headround": {"translate": [{"y": -144.57, "curve": [0.336, 0, 0.668, 0, 0.336, -26.24, 0.668, 207.41]}, {"time": 1, "y": 207.41, "curve": [1.444, 0, 1.889, 0, 1.444, 207.41, 1.889, -211.46]}, {"time": 2.3333, "y": -211.57, "curve": [2.778, 0, 3.222, 0, 2.778, -211.67, 3.222, 207.3]}, {"time": 3.6667, "y": 207.41, "curve": [4.111, 0, 4.556, 0, 4.111, 207.51, 4.556, -211.46]}, {"time": 5, "y": -211.57, "curve": [5.444, 0, 5.889, 0, 5.444, -211.67, 5.889, 207.27]}, {"time": 6.3333, "y": 207.41, "curve": [6.667, 0, 7, 0, 6.667, 207.51, 7, -211.54]}, {"time": 7.3333, "y": -211.57, "curve": [7.778, 0, 8.222, 0, 7.778, -211.6, 8.222, -85.97]}, {"time": 8.6667, "y": -85.94, "curve": [9.111, 0, 9.556, 0, 9.111, -85.91, 9.556, -211.46]}, {"time": 10, "y": -211.57, "curve": [10.444, 0, 10.889, 0, 10.444, -211.67, 10.889, 207.3]}, {"time": 11.3333, "y": 207.41, "curve": [11.778, 0, 12.222, 0, 11.778, 207.51, 12.222, -211.46]}, {"time": 12.6667, "y": -211.57, "curve": [12.779, 0, 12.892, 0, 12.779, -211.59, 12.892, -184.72]}, {"time": 13, "y": -144.57}]}, "bodyround": {"translate": [{"y": 81.84, "curve": [0.168, 0, 0.334, 0, 0.168, 163.66, 0.334, 230.79]}, {"time": 0.5, "y": 230.79, "curve": [0.944, 0, 1.389, 0, 0.944, 230.79, 1.389, -237.4]}, {"time": 1.8333, "y": -237.52, "curve": [2.278, 0, 2.722, 0, 2.278, -237.63, 2.722, 230.68]}, {"time": 3.1667, "y": 230.79, "curve": [3.611, 0, 4.056, 0, 3.611, 230.91, 4.056, -237.4]}, {"time": 4.5, "y": -237.52, "curve": [4.944, 0, 5.389, 0, 4.944, -237.63, 5.389, 230.64]}, {"time": 5.8333, "y": 230.79, "curve": [6.167, 0, 6.5, 0, 6.167, 230.91, 6.5, -237.49]}, {"time": 6.8333, "y": -237.52, "curve": [7.278, 0, 7.722, 0, 7.278, -237.55, 7.722, -97.72]}, {"time": 8.1667, "y": -97.69, "curve": [8.611, 0, 9.056, 0, 8.611, -97.65, 9.056, -237.4]}, {"time": 9.5, "y": -237.52, "curve": [9.944, 0, 10.389, 0, 9.944, -237.63, 10.389, 230.68]}, {"time": 10.8333, "y": 230.79, "curve": [11.278, 0, 11.722, 0, 11.278, 230.91, 11.722, -237.4]}, {"time": 12.1667, "y": -237.52, "curve": [12.445, 0, 12.724, 0, 12.445, -237.59, 12.724, -55.5]}, {"time": 13, "y": 81.84}]}, "sh_L2": {"rotate": [{"value": -0.22}]}, "sh_L3": {"rotate": [{"value": 0.65}]}, "sh_R2": {"rotate": [{"value": 0.33}]}, "sh_R3": {"rotate": [{}]}, "arm_R4": {"translate": [{"x": -3, "y": 8.15, "curve": [0.168, -7.44, 0.334, -11.08, 0.168, 10.3, 0.334, 12.07]}, {"time": 0.5, "x": -11.08, "y": 12.07, "curve": [0.944, -11.08, 1.389, 14.32, 0.944, 12.07, 1.389, -0.23]}, {"time": 1.8333, "x": 14.32, "y": -0.24, "curve": [2.278, 14.33, 2.722, -11.07, 2.278, -0.24, 2.722, 12.06]}, {"time": 3.1667, "x": -11.08, "y": 12.07, "curve": [3.611, -11.09, 4.056, 14.32, 3.611, 12.07, 4.056, -0.23]}, {"time": 4.5, "x": 14.32, "y": -0.24, "curve": [4.944, 14.33, 5.389, -11.07, 4.944, -0.24, 5.389, 12.08]}, {"time": 5.8333, "x": -11.08, "y": 12.07, "curve": [6.167, -11.09, 6.5, 21.17, 6.167, 12.06, 6.5, 52.63]}, {"time": 6.8333, "x": 21.17, "y": 52.63, "curve": [7.278, 21.17, 7.722, 5.05, 7.278, 52.64, 7.722, 32.35]}, {"time": 8.1667, "x": 5.05, "y": 32.35, "curve": [8.611, 5.04, 9.056, 21.16, 8.611, 32.34, 9.056, 52.62]}, {"time": 9.5, "x": 21.17, "y": 52.63, "curve": [9.944, 21.18, 10.389, -11.07, 9.944, 52.64, 10.389, 12.06]}, {"time": 10.8333, "x": -11.08, "y": 12.07, "curve": [11.278, -11.09, 11.722, 14.32, 11.278, 12.07, 11.722, -0.23]}, {"time": 12.1667, "x": 14.32, "y": -0.24, "curve": [12.445, 14.33, 12.724, 4.45, 12.445, -0.24, 12.724, 4.55]}, {"time": 13, "x": -3, "y": 8.15}]}, "leg_R4": {"rotate": [{"value": 0.18}]}, "leg_R5": {"rotate": [{"value": 0.01}]}, "head2": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.63, 2.033, 0, 2.067, 0.51]}, {"time": 2.1, "x": -1.63, "y": 0.51, "curve": "stepped"}, {"time": 3.0667, "x": -1.63, "y": 0.51, "curve": [3.1, -1.63, 3.133, -0.93, 3.1, 0.51, 3.133, 1.73]}, {"time": 3.1667, "x": -0.93, "y": 1.73, "curve": "stepped"}, {"time": 3.5667, "x": -0.93, "y": 1.73, "curve": [3.6, -0.93, 3.633, 0, 3.6, 1.73, 3.633, 0]}, {"time": 3.6667, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -3.31, 6.2, 0, 6.233, 9.15]}, {"time": 6.2667, "x": -3.31, "y": 9.15, "curve": "stepped"}, {"time": 6.9, "x": -3.31, "y": 9.15, "curve": [6.933, -3.31, 6.967, -4.34, 6.933, 9.15, 6.967, 10.83]}, {"time": 7, "x": -4.34, "y": 10.83, "curve": "stepped"}, {"time": 7.9, "x": -4.34, "y": 10.83, "curve": [7.933, -4.34, 7.967, -4.37, 7.933, 10.83, 7.967, 8.37]}, {"time": 8, "x": -4.37, "y": 8.37, "curve": "stepped"}, {"time": 8.4, "x": -4.37, "y": 8.37, "curve": [8.433, -4.37, 8.467, -3.31, 8.433, 8.37, 8.467, 9.15]}, {"time": 8.5, "x": -3.31, "y": 9.15, "curve": "stepped"}, {"time": 10, "x": -3.31, "y": 9.15, "curve": [10.033, -3.31, 10.067, 0, 10.033, 9.15, 10.067, 0]}, {"time": 10.1}]}, "head3": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.09, 2.033, 0, 2.067, 0.33]}, {"time": 2.1, "x": -1.09, "y": 0.33, "curve": "stepped"}, {"time": 3.0667, "x": -1.09, "y": 0.33, "curve": [3.1, -1.09, 3.133, -0.39, 3.1, 0.33, 3.133, 1.54]}, {"time": 3.1667, "x": -0.39, "y": 1.54, "curve": "stepped"}, {"time": 3.5667, "x": -0.39, "y": 1.54, "curve": [3.6, -0.39, 3.633, 0, 3.6, 1.54, 3.633, 0]}, {"time": 3.6667, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -3.5, 6.2, 0, 6.233, 9.26]}, {"time": 6.2667, "x": -3.5, "y": 9.26, "curve": "stepped"}, {"time": 6.9, "x": -3.5, "y": 9.26, "curve": [6.933, -3.5, 6.967, -4.53, 6.933, 9.26, 6.967, 10.94]}, {"time": 7, "x": -4.53, "y": 10.94, "curve": "stepped"}, {"time": 7.9, "x": -4.53, "y": 10.94, "curve": [7.933, -4.53, 7.967, -5.13, 7.933, 10.94, 7.967, 8.48]}, {"time": 8, "x": -5.13, "y": 8.48, "curve": "stepped"}, {"time": 8.4, "x": -5.13, "y": 8.48, "curve": [8.433, -5.13, 8.467, -3.5, 8.433, 8.48, 8.467, 9.26]}, {"time": 8.5, "x": -3.5, "y": 9.26, "curve": "stepped"}, {"time": 10, "x": -3.5, "y": 9.26, "curve": [10.033, -3.5, 10.067, 0, 10.033, 9.26, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": "stepped"}, {"time": 9.6667, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "2": {"bones": {"body": {"rotate": [{"value": 2.36, "curve": [0.023, 2.36, 0.059, -7.69]}, {"time": 0.2333, "value": -7.73, "curve": [0.544, -7.8, 0.856, 2.36]}, {"time": 1.1667, "value": 2.36}]}, "body2": {"rotate": [{"value": 1.59, "curve": [0.023, 1.59, 0.059, -11.26]}, {"time": 0.2333, "value": -11.31, "curve": [0.544, -11.4, 0.856, 1.59]}, {"time": 1.1667, "value": 1.59}], "translate": [{"x": -3.92, "curve": [0.023, -3.92, 0.059, 8.28, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 8.32, "curve": [0.544, 8.41, 0.856, -3.92, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": -3.92}], "scale": [{"y": 1.018, "curve": [0.078, 1, 0.156, 1, 0.023, 1.018, 0.059, 0.963]}, {"time": 0.2333, "y": 0.963, "curve": [0.678, 1, 0.856, 1, 0.544, 0.962, 0.856, 1.018]}, {"time": 1.1667, "y": 1.018}]}, "body3": {"rotate": [{"value": -3, "curve": [0.027, -3, 0.067, -2.8]}, {"time": 0.2667, "value": -2.8, "curve": [0.567, -2.8, 0.867, -3]}, {"time": 1.1667, "value": -3}], "translate": [{"x": -4.16, "curve": [0.027, -4.16, 0.067, 9.87, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 9.93, "curve": [0.567, 10.02, 0.867, -4.16, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.16}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.027, 1.003, 0.067, 1.021, 0.027, 1.003, 0.067, 1.021]}, {"time": 0.2667, "x": 1.021, "y": 1.021, "curve": [0.567, 1.021, 0.867, 1.003, 0.567, 1.021, 0.867, 1.003]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.54, "curve": [0.03, 0.54, 0.076, 7.88]}, {"time": 0.3, "value": 7.92, "curve": [0.589, 7.96, 0.878, 0.54]}, {"time": 1.1667, "value": 0.54}]}, "head": {"rotate": [{"value": -0.51, "curve": [0.03, -0.51, 0.076, 7.87]}, {"time": 0.3, "value": 7.92, "curve": [0.589, 7.97, 0.878, -0.51]}, {"time": 1.1667, "value": -0.51}]}, "sh_L": {"translate": [{"x": -1.78, "curve": [0.027, -1.78, 0.067, 10.2, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.26, "curve": [0.567, 10.34, 0.867, -1.78, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -1.78}]}, "sh_R": {"translate": [{"x": -1.78, "curve": [0.027, -1.78, 0.067, 8.72, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 8.77, "curve": [0.567, 8.84, 0.867, -1.78, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -1.78}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "hand_L3": {"rotate": [{"value": 1.81, "curve": [0.027, 1.81, 0.067, -3.16]}, {"time": 0.2667, "value": -3.19, "curve": [0.567, -3.22, 0.867, 1.81]}, {"time": 1.1667, "value": 1.81}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "arm_R3": {"rotate": [{"value": 1.82, "curve": [0.033, 1.82, 0.084, 10.28]}, {"time": 0.3333, "value": 10.33, "curve": [0.611, 10.38, 0.889, 1.82]}, {"time": 1.1667, "value": 1.82}]}, "RU_L": {"translate": [{"x": -11.63, "curve": [0.074, -22.84, 0.161, -32.04, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -32.04, "curve": [0.428, -32.04, 0.605, 32.11, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 32.13, "curve": [0.922, 32.14, 1.046, 7.19, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -11.63}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.073, 1.012, 0.16, 0.926, 0.073, 0.992, 0.16, 1.04]}, {"time": 0.2333, "x": 0.926, "y": 1.04, "curve": [0.331, 0.926, 0.403, 1.079, 0.331, 1.04, 0.403, 0.955]}, {"time": 0.5, "x": 1.079, "y": 0.955, "curve": [0.597, 1.079, 0.703, 0.926, 0.597, 0.955, 0.703, 1.04]}, {"time": 0.8, "x": 0.926, "y": 1.04, "curve": [0.897, 0.926, 1.003, 1.079, 0.897, 1.04, 1.003, 0.955]}, {"time": 1.1, "x": 1.079, "y": 0.955, "curve": [1.125, 1.079, 1.143, 1.069, 1.125, 0.955, 1.143, 0.96]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_L2": {"translate": [{"x": -0.68, "curve": [0.099, -15.15, 0.203, -29.83, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -29.83, "curve": [0.495, -29.83, 0.672, 28.45, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 28.46, "curve": [0.965, 28.47, 1.071, 14, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": -0.67}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.097, 1.079, 0.203, 0.926, 0.097, 0.955, 0.203, 1.04]}, {"time": 0.3, "x": 0.926, "y": 1.04, "curve": [0.397, 0.926, 0.503, 1.079, 0.397, 1.04, 0.503, 0.955]}, {"time": 0.6, "x": 1.079, "y": 0.955, "curve": [0.697, 1.079, 0.769, 0.926, 0.697, 0.955, 0.769, 1.04]}, {"time": 0.8667, "x": 0.926, "y": 1.04, "curve": [0.964, 0.926, 1.069, 1.079, 0.964, 1.04, 1.069, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955}]}, "RU_L3": {"translate": [{"x": 10.34, "curve": [0.122, -4.98, 0.245, -25.36, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -25.36, "curve": [0.561, -25.36, 0.739, 26.97, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 26.98, "curve": [1.007, 26.99, 1.094, 19.6, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 10.35}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.024, 1.069, 0.042, 1.079, 0.024, 0.961, 0.042, 0.955]}, {"time": 0.0667, "x": 1.079, "y": 0.955, "curve": [0.164, 1.079, 0.269, 0.926, 0.164, 0.955, 0.269, 1.04]}, {"time": 0.3667, "x": 0.926, "y": 1.04, "curve": [0.464, 0.926, 0.569, 1.079, 0.464, 1.04, 0.569, 0.955]}, {"time": 0.6667, "x": 1.079, "y": 0.955, "curve": [0.764, 1.079, 0.836, 0.926, 0.764, 0.955, 0.836, 1.04]}, {"time": 0.9333, "x": 0.926, "y": 1.04, "curve": [1.007, 0.926, 1.094, 1.012, 1.007, 1.04, 1.094, 0.992]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_R": {"translate": [{"x": -7.06, "curve": [0.074, -17.1, 0.161, -25.34, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -25.34, "curve": [0.428, -25.34, 0.605, 32.12, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 32.13, "curve": [0.922, 32.14, 1.046, 9.8, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -7.05}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.073, 1.012, 0.16, 0.926, 0.073, 0.992, 0.16, 1.04]}, {"time": 0.2333, "x": 0.926, "y": 1.04, "curve": [0.331, 0.926, 0.403, 1.079, 0.331, 1.04, 0.403, 0.955]}, {"time": 0.5, "x": 1.079, "y": 0.955, "curve": [0.597, 1.079, 0.703, 0.926, 0.597, 0.955, 0.703, 1.04]}, {"time": 0.8, "x": 0.926, "y": 1.04, "curve": [0.897, 0.926, 1.003, 1.079, 0.897, 1.04, 1.003, 0.955]}, {"time": 1.1, "x": 1.079, "y": 0.955, "curve": [1.125, 1.079, 1.143, 1.069, 1.125, 0.955, 1.143, 0.96]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_R2": {"translate": [{"x": 3.05, "curve": [0.099, -9.58, 0.203, -22.38, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -22.38, "curve": [0.495, -22.38, 0.672, 28.45, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 28.46, "curve": [0.965, 28.47, 1.071, 15.85, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": 3.05}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.097, 1.079, 0.203, 0.926, 0.097, 0.955, 0.203, 1.04]}, {"time": 0.3, "x": 0.926, "y": 1.04, "curve": [0.397, 0.926, 0.503, 1.079, 0.397, 1.04, 0.503, 0.955]}, {"time": 0.6, "x": 1.079, "y": 0.955, "curve": [0.697, 1.079, 0.769, 0.926, 0.697, 0.955, 0.769, 1.04]}, {"time": 0.8667, "x": 0.926, "y": 1.04, "curve": [0.964, 0.926, 1.069, 1.079, 0.964, 1.04, 1.069, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955}]}, "RU_R3": {"translate": [{"x": 12.95, "curve": [0.122, 0.03, 0.245, -17.16, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -17.16, "curve": [0.561, -17.16, 0.739, 26.97, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 26.98, "curve": [1.007, 26.99, 1.094, 20.76, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 12.95}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.024, 1.069, 0.042, 1.079, 0.024, 0.961, 0.042, 0.955]}, {"time": 0.0667, "x": 1.079, "y": 0.955, "curve": [0.164, 1.079, 0.269, 0.926, 0.164, 0.955, 0.269, 1.04]}, {"time": 0.3667, "x": 0.926, "y": 1.04, "curve": [0.464, 0.926, 0.569, 1.079, 0.464, 1.04, 0.569, 0.955]}, {"time": 0.6667, "x": 1.079, "y": 0.955, "curve": [0.764, 1.079, 0.836, 0.926, 0.764, 0.955, 0.836, 1.04]}, {"time": 0.9333, "x": 0.926, "y": 1.04, "curve": [1.007, 0.926, 1.094, 1.012, 1.007, 1.04, 1.094, 0.992]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "leg_L2": {"rotate": [{"value": -0.7, "curve": [0.023, -0.7, 0.059, -4.66]}, {"time": 0.2333, "value": -4.66, "curve": [0.544, -4.64, 0.856, -0.7]}, {"time": 1.1667, "value": -0.7}]}, "leg_L3": {"rotate": [{"value": -1.43, "curve": [0.024, -1.43, 0.059, 3.47]}, {"time": 0.2333, "value": 3.47, "curve": [0.544, 3.47, 0.856, -1.43]}, {"time": 1.1667, "value": -1.43}]}, "leg_L4": {"rotate": [{"value": -1.19, "curve": [0.024, -1.19, 0.059, 1.68]}, {"time": 0.2333, "value": 1.68, "curve": [0.544, 1.68, 0.856, -1.19]}, {"time": 1.1667, "value": -1.19}]}, "leg_L5": {"rotate": [{"value": 0.43, "curve": [0.024, 0.43, 0.059, 8.8]}, {"time": 0.2333, "value": 8.8, "curve": [0.544, 8.8, 0.856, 0.43]}, {"time": 1.1667, "value": 0.43}]}, "leg_R2": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "foot_R": {"rotate": [{"value": 6.84, "curve": [0.023, 6.84, 0.059, -8.33]}, {"time": 0.2333, "value": -8.39, "curve": [0.544, -8.49, 0.856, 6.84]}, {"time": 1.1667, "value": 6.84}]}, "foot_R2": {"rotate": [{"value": -3.01, "curve": [0.023, -3.01, 0.059, 3.54]}, {"time": 0.2333, "value": 3.57, "curve": [0.544, 3.62, 0.856, -3.01]}, {"time": 1.1667, "value": -3.01}]}, "wine2": {"translate": [{"x": 0.11, "y": 0.06}], "scale": [{"x": 1.005, "curve": [0.033, 1.005, 0.084, 0.957, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.956, "curve": [0.611, 0.956, 0.889, 1.005, 0.611, 1, 0.889, 1]}, {"time": 1.1667, "x": 1.005}]}, "wine3": {"rotate": [{"value": -69.09}, {"time": 1.1333, "value": -425.93, "curve": "stepped"}, {"time": 1.1667, "value": -69.09}]}, "head4": {"translate": [{"curve": [0.024, 0, 0.058, 1.43, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.43, "curve": [0.544, 1.43, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "head5": {"rotate": [{"curve": [0.024, 0, 0.058, -5.84]}, {"time": 0.2333, "value": -5.84, "curve": [0.544, -5.84, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.986, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.986, "curve": [0.544, 0.986, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "head6": {"rotate": [{"curve": [0.024, 0, 0.058, -14.96]}, {"time": 0.2333, "value": -14.96, "curve": [0.544, -14.96, 0.856, 0]}, {"time": 1.1667}]}, "head7": {"translate": [{"curve": [0.024, 0, 0.058, 0.09, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 0.09, "curve": [0.544, 0.09, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "head8": {"rotate": [{"curve": [0.024, 0, 0.058, 19.17]}, {"time": 0.2333, "value": 19.17, "curve": [0.544, 19.17, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.986, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.986, "curve": [0.544, 0.986, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "head9": {"rotate": [{"curve": [0.024, 0, 0.058, 19.21]}, {"time": 0.2333, "value": 19.21, "curve": [0.544, 19.21, 0.856, 0]}, {"time": 1.1667}]}, "hair_F": {"rotate": [{"value": 2.67, "curve": [0.123, 0.09, 0.244, -3.34]}, {"time": 0.3667, "value": -3.34, "curve": [0.563, -3.34, 0.77, 5.47]}, {"time": 0.9667, "value": 5.47, "curve": [1.041, 5.48, 1.094, 4.23]}, {"time": 1.1667, "value": 2.67}]}, "hair_B": {"rotate": [{"value": 4.07, "curve": [0.149, 1.58, 0.287, -3.34]}, {"time": 0.4333, "value": -3.34, "curve": [0.63, -3.34, 0.837, 5.47]}, {"time": 1.0333, "value": 5.47, "curve": [1.083, 5.48, 1.119, 4.91]}, {"time": 1.1667, "value": 4.07}]}, "headround3": {"translate": [{"x": 28.66, "curve": [0.033, 28.66, 0.084, -327.46, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -329.45, "curve": [0.611, -331.67, 0.889, 28.66, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 28.66}]}, "headround": {"translate": [{"y": -144.57, "curve": [0.111, 0, 0.222, 0, 0.033, -144.57, 0.084, -419.71]}, {"time": 0.3333, "y": -421.24, "curve": [0.611, 0, 0.889, 0, 0.611, -422.96, 0.889, -144.57]}, {"time": 1.1667, "y": -144.57}]}, "bodyround": {"translate": [{"y": 81.84, "curve": [0.078, 0, 0.156, 0, 0.023, 81.84, 0.059, -348.49]}, {"time": 0.2333, "y": -350.17, "curve": [0.544, 0, 0.856, 0, 0.544, -353.17, 0.856, 81.84]}, {"time": 1.1667, "y": 81.84}]}, "sh_L2": {"rotate": [{"value": -0.22}]}, "sh_L3": {"rotate": [{"value": 0.65}]}, "sh_R2": {"rotate": [{"value": 0.33}]}, "sh_R3": {"rotate": [{}]}, "arm_R4": {"translate": [{"x": -3, "y": 8.15, "curve": [0.03, -3, 0.076, -0.67, 0.03, 8.15, 0.076, -47.28]}, {"time": 0.3, "x": -0.65, "y": -47.56, "curve": [0.589, -0.64, 0.878, -3, 0.589, -47.92, 0.878, 8.15]}, {"time": 1.1667, "x": -3, "y": 8.15}]}, "leg_R4": {"rotate": [{"value": 0.18}]}, "leg_R5": {"rotate": [{"value": 0.01}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-0.75146, -0.08116, -0.75057, -0.08125, -0.75146, -0.08116, -0.75057, -0.08125, -0.60782, -0.06071, -0.60708, -0.06078, -0.46053, -0.00527, -0.45995, -0.00533, -0.2487, -0.00956, -0.24833, -0.0096, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.52492, 0.05446, -0.52452, 0.05442, -1.74319, 0.07478, -1.74189, 0.07465, -2.56961, -0.05163, -2.56808, -0.05174, -2.36624, 0.03334, -2.36482, 0.03326, -1.47224, -0.04175, -1.47118, -0.04178, -0.25513, 0.1143, -0.25431, 0.11424, -0.26275, 0.02722, -0.26198, 0.02719, -0.60782, -0.06071, -0.60708, -0.06078, 0, 0, 0, 0, -0.98885, 0.01421, -0.98832, 0.01416, -1.94255, 0.02475, -1.94173, 0.02472, -2.55827, 0.05331, -2.55764, 0.05328, -2.43205, 0.07558, -2.43155, 0.07555, -1.61855, 0.07954, -1.61786, 0.07949, -0.53051, -0.01542, -0.52982, -0.01549, -0.14283, -0.02028, -0.14267, -0.02029], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-1.87869, -0.1673, -1.87608, -0.16734, -1.34602, -0.05435, -1.34488, -0.05442, -0.53794, -0.06824, -0.53702, -0.06828, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33056, 0.00386, -0.32948, 0.00375, -0.3752, -0.07957, -0.37381, -0.07963, -0.42564, -0.0868, -0.42488, -0.08684, -0.60349, -0.17483, -0.60194, -0.17489, -0.81873, -0.12936, -0.81775, -0.12938, -1.1977, -0.08872, -1.19591, -0.08876, -1.96427, -0.09401, -1.96285, -0.09408, 0, 0, 0, 0, -0.32877, 0.01672, -0.32814, 0.01668, -0.97672, -0.02901, -0.97564, -0.02906, -1.51772, -0.0511, -1.51635, -0.0511, -1.89778, -0.32797, -1.89669, -0.32803, -2.02044, -0.24518, -2.01893, -0.24526, -1.59922, -0.14887, -1.59798, -0.14895, -0.77796, -0.02796, -0.77548, -0.02817, -0.18739, 0.0192, -0.18639, 0.01905, -0.02592, 0.00269, -0.02576], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 366, "vertices": [-0.51084, 0.05299, -0.51042, 0.05296, -1.41758, 0.01808, -1.41713, 0.01804, -2.21389, 0.08634, -2.21265, 0.08623, -2.40453, -0.09457, -2.40347, -0.09464, -1.92282, -0.0155, -1.9223, -0.01555, -1.11111, 0.07225, -1.11035, 0.07221, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80462, 0.11321, -0.80462, 0.11317, -1.60494, -0.15999, -1.60484, -0.16004, -1.96902, -0.09252, -1.96899, -0.09257, -1.71017, -0.03031, -1.71015, -0.03037, -1.17387, -0.01176, -1.17387, -0.0118, -0.61433, -0.05498, -0.61433, -0.05502], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}}}}, "idle": {"bones": {"body": {"rotate": [{"value": 2.36, "curve": [0.444, 2.36, 0.889, -2.94]}, {"time": 1.3333, "value": -2.94, "curve": [1.778, -2.94, 2.222, 2.36]}, {"time": 2.6667, "value": 2.36, "curve": [3.111, 2.36, 3.556, -2.94]}, {"time": 4, "value": -2.94, "curve": [4.444, -2.94, 4.889, 2.36]}, {"time": 5.3333, "value": 2.36, "curve": [5.667, 2.36, 6, -5.8]}, {"time": 6.3333, "value": -5.8, "curve": [6.778, -5.81, 7.222, -1.72]}, {"time": 7.6667, "value": -1.72, "curve": [8.111, -1.72, 8.556, -5.8]}, {"time": 9, "value": -5.8, "curve": [9.444, -5.81, 9.889, 2.36]}, {"time": 10.3333, "value": 2.36, "curve": [10.778, 2.36, 11.222, -2.94]}, {"time": 11.6667, "value": -2.94, "curve": [12.111, -2.94, 12.556, 2.36]}, {"time": 13, "value": 2.36}]}, "body2": {"rotate": [{"value": 1.59, "curve": [0.057, 1.74, 0.112, 1.85]}, {"time": 0.1667, "value": 1.85, "curve": [0.611, 1.85, 1.056, -3.59]}, {"time": 1.5, "value": -3.59, "curve": [1.944, -3.6, 2.389, 1.85]}, {"time": 2.8333, "value": 1.85, "curve": [3.278, 1.85, 3.722, -3.59]}, {"time": 4.1667, "value": -3.59, "curve": [4.611, -3.6, 5.056, 1.84]}, {"time": 5.5, "value": 1.85, "curve": [5.833, 1.85, 6.167, -9.53]}, {"time": 6.5, "value": -9.53, "curve": [6.944, -9.53, 7.389, -3.84]}, {"time": 7.8333, "value": -3.84, "curve": [8.278, -3.84, 8.722, -9.52]}, {"time": 9.1667, "value": -9.53, "curve": [9.611, -9.53, 10.056, 1.85]}, {"time": 10.5, "value": 1.85, "curve": [10.944, 1.85, 11.389, -3.59]}, {"time": 11.8333, "value": -3.59, "curve": [12.223, -3.6, 12.613, 0.56]}, {"time": 13, "value": 1.59}], "translate": [{"x": -3.92, "curve": [0.057, -4.18, 0.112, -4.39, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -4.39, "curve": [0.611, -4.39, 1.056, 5.51, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 5.52, "curve": [1.944, 5.52, 2.389, -4.38, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -4.39, "curve": [3.278, -4.39, 3.722, 5.51, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 5.52, "curve": [4.611, 5.52, 5.056, -4.38, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -4.39, "curve": [5.833, -4.39, 6.167, 5.51, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 5.52, "curve": [6.944, 5.52, 7.389, 0.57, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 0.56, "curve": [8.278, 0.56, 8.722, 5.51, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 5.52, "curve": [9.611, 5.52, 10.056, -4.38, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -4.39, "curve": [10.944, -4.39, 11.389, 5.51, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 5.52, "curve": [12.223, 5.52, 12.613, -2.04, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -3.92}], "scale": [{"y": 1.018, "curve": [0.057, 1, 0.112, 1, 0.057, 1.019, 0.112, 1.021]}, {"time": 0.1667, "y": 1.021, "curve": [0.611, 1, 1.056, 1, 0.611, 1.021, 1.056, 0.963]}, {"time": 1.5, "y": 0.963, "curve": [1.944, 1, 2.389, 1, 1.944, 0.963, 2.389, 1.02]}, {"time": 2.8333, "y": 1.021, "curve": [3.278, 1, 3.722, 1, 3.278, 1.021, 3.722, 0.963]}, {"time": 4.1667, "y": 0.963, "curve": [4.611, 1, 5.056, 1, 4.611, 0.963, 5.056, 1.02]}, {"time": 5.5, "y": 1.021, "curve": [5.833, 1, 6.167, 1, 5.833, 1.021, 6.167, 0.963]}, {"time": 6.5, "y": 0.963, "curve": [6.944, 1, 7.389, 1, 6.944, 0.963, 7.389, 0.992]}, {"time": 7.8333, "y": 0.992, "curve": [8.278, 1, 8.722, 1, 8.278, 0.992, 8.722, 0.963]}, {"time": 9.1667, "y": 0.963, "curve": [9.611, 1, 10.056, 1, 9.611, 0.963, 10.056, 1.02]}, {"time": 10.5, "y": 1.021, "curve": [10.944, 1, 11.389, 1, 10.944, 1.021, 11.389, 0.963]}, {"time": 11.8333, "y": 0.963, "curve": [12.223, 1, 12.613, 1, 12.223, 0.963, 12.613, 1.007]}, {"time": 13, "y": 1.018}]}, "body3": {"rotate": [{"value": -3, "curve": [0.114, -3.12, 0.224, -3.22]}, {"time": 0.3333, "value": -3.22, "curve": [0.778, -3.22, 1.222, -1.83]}, {"time": 1.6667, "value": -1.83, "curve": [2.111, -1.83, 2.556, -3.22]}, {"time": 3, "value": -3.22, "curve": [3.444, -3.22, 3.889, -1.83]}, {"time": 4.3333, "value": -1.83, "curve": [4.778, -1.83, 5.222, -3.22]}, {"time": 5.6667, "value": -3.22, "curve": [6, -3.22, 6.333, -4.16]}, {"time": 6.6667, "value": -4.16, "curve": [7.111, -4.16, 7.556, -3.69]}, {"time": 8, "value": -3.69, "curve": [8.444, -3.69, 8.889, -4.16]}, {"time": 9.3333, "value": -4.16, "curve": [9.778, -4.16, 10.222, -3.22]}, {"time": 10.6667, "value": -3.22, "curve": [11.111, -3.22, 11.556, -1.83]}, {"time": 12, "value": -1.83, "curve": [12.335, -1.83, 12.67, -2.61]}, {"time": 13, "value": -3}], "translate": [{"x": -4.16, "curve": [0.114, -5, 0.224, -5.63, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.63, "curve": [0.778, -5.63, 1.222, 3.56, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 3.56, "curve": [2.111, 3.57, 2.556, -5.62, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.63, "curve": [3.444, -5.63, 3.889, 3.56, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 3.56, "curve": [4.778, 3.57, 5.222, -5.62, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.63, "curve": [6, -5.63, 6.333, 3.56, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 3.56, "curve": [7.111, 3.57, 7.556, -1.03, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.03, "curve": [8.444, -1.03, 8.889, 3.56, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 3.56, "curve": [9.778, 3.57, 10.222, -5.62, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.63, "curve": [11.111, -5.63, 11.556, 3.56, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 3.56, "curve": [12.335, 3.57, 12.67, -1.58, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.16}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.021, 0.778, 1, 1.222, 1.021]}, {"time": 1.6667, "x": 1.021, "y": 1.021, "curve": [2.111, 1.021, 2.556, 1, 2.111, 1.021, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.021, 3.444, 1, 3.889, 1.021]}, {"time": 4.3333, "x": 1.021, "y": 1.021, "curve": [4.778, 1.021, 5.222, 1, 4.778, 1.021, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.021, 6, 1, 6.333, 1.021]}, {"time": 6.6667, "x": 1.021, "y": 1.021, "curve": [7.111, 1.021, 7.556, 1.011, 7.111, 1.021, 7.556, 1.011]}, {"time": 8, "x": 1.011, "y": 1.011, "curve": [8.444, 1.011, 8.889, 1.021, 8.444, 1.011, 8.889, 1.021]}, {"time": 9.3333, "x": 1.021, "y": 1.021, "curve": [9.778, 1.021, 10.222, 1, 9.778, 1.021, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.021, 11.111, 1, 11.556, 1.021]}, {"time": 12, "x": 1.021, "y": 1.021, "curve": [12.335, 1.021, 12.67, 1.009, 12.335, 1.021, 12.67, 1.009]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.54, "curve": [0.168, 1.55, 0.334, 2.37]}, {"time": 0.5, "value": 2.37, "curve": [0.944, 2.37, 1.389, -3.39]}, {"time": 1.8333, "value": -3.39, "curve": [2.278, -3.39, 2.722, 2.37]}, {"time": 3.1667, "value": 2.37, "curve": [3.611, 2.37, 4.056, -3.39]}, {"time": 4.5, "value": -3.39, "curve": [4.944, -3.39, 5.389, 2.37]}, {"time": 5.8333, "value": 2.37, "curve": [6.167, 2.37, 6.5, -8.58]}, {"time": 6.8333, "value": -8.58, "curve": [7.278, -8.58, 7.722, -3.11]}, {"time": 8.1667, "value": -3.1, "curve": [8.611, -3.1, 9.056, -8.58]}, {"time": 9.5, "value": -8.58, "curve": [9.944, -8.58, 10.389, 2.37]}, {"time": 10.8333, "value": 2.37, "curve": [11.278, 2.37, 11.722, -3.39]}, {"time": 12.1667, "value": -3.39, "curve": [12.445, -3.39, 12.724, -1.15]}, {"time": 13, "value": 0.54}]}, "head": {"rotate": [{"value": -0.51, "curve": [0.225, 0.92, 0.446, 2.37]}, {"time": 0.6667, "value": 2.37, "curve": [1.111, 2.37, 1.556, -3.39]}, {"time": 2, "value": -3.39, "curve": [2.444, -3.39, 2.889, 2.37]}, {"time": 3.3333, "value": 2.37, "curve": [3.778, 2.37, 4.222, -3.39]}, {"time": 4.6667, "value": -3.39, "curve": [5.111, -3.39, 5.556, 2.37]}, {"time": 6, "value": 2.37, "curve": [6.333, 2.37, 6.667, -8.58]}, {"time": 7, "value": -8.58, "curve": [7.444, -8.58, 7.889, -3.11]}, {"time": 8.3333, "value": -3.1, "curve": [8.778, -3.1, 9.222, -8.58]}, {"time": 9.6667, "value": -8.58, "curve": [10.111, -8.58, 10.556, 2.37]}, {"time": 11, "value": 2.37, "curve": [11.444, 2.37, 11.889, -3.39]}, {"time": 12.3333, "value": -3.39, "curve": [12.557, -3.39, 12.781, -1.96]}, {"time": 13, "value": -0.51}]}, "sh_L": {"translate": [{"x": -1.78, "curve": [0.114, -2.61, 0.224, -3.23, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -3.23, "curve": [0.778, -3.23, 1.222, 5.82, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.82, "curve": [2.111, 5.82, 2.556, -3.23, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -3.23, "curve": [3.444, -3.23, 3.889, 5.82, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.82, "curve": [4.778, 5.82, 5.222, -3.22, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -3.23, "curve": [6, -3.23, 6.333, 5.82, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.82, "curve": [7.111, 5.82, 7.556, -3.23, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -3.23, "curve": [8.444, -3.23, 8.889, 5.82, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.82, "curve": [9.778, 5.82, 10.222, -3.23, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -3.23, "curve": [11.111, -3.23, 11.556, 5.82, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.82, "curve": [12.335, 5.82, 12.67, 0.75, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.78}]}, "sh_R": {"translate": [{"x": -1.78, "curve": [0.114, -2.61, 0.224, -3.23, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -3.23, "curve": [0.778, -3.23, 1.222, 5.82, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 5.82, "curve": [2.111, 5.82, 2.556, -3.23, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -3.23, "curve": [3.444, -3.23, 3.889, 5.82, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 5.82, "curve": [4.778, 5.82, 5.222, -3.22, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -3.23, "curve": [6, -3.23, 6.333, 5.82, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 5.82, "curve": [7.111, 5.82, 7.556, -3.23, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -3.23, "curve": [8.444, -3.23, 8.889, 5.82, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 5.82, "curve": [9.778, 5.82, 10.222, -3.23, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -3.23, "curve": [11.111, -3.23, 11.556, 5.82, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 5.82, "curve": [12.335, 5.82, 12.67, 0.75, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.78}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "hand_L3": {"rotate": [{"value": 1.81, "curve": [0.114, 2.35, 0.224, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.778, 2.76, 1.222, -3.18]}, {"time": 1.6667, "value": -3.18, "curve": [2.111, -3.18, 2.556, 2.75]}, {"time": 3, "value": 2.76, "curve": [3.444, 2.76, 3.889, -3.18]}, {"time": 4.3333, "value": -3.18, "curve": [4.778, -3.18, 5.222, 2.75]}, {"time": 5.6667, "value": 2.76, "curve": [6, 2.76, 6.333, -3.18]}, {"time": 6.6667, "value": -3.18, "curve": [7.111, -3.18, 7.556, 2.75]}, {"time": 8, "value": 2.76, "curve": [8.444, 2.76, 8.889, -3.18]}, {"time": 9.3333, "value": -3.18, "curve": [9.778, -3.18, 10.222, 2.75]}, {"time": 10.6667, "value": 2.76, "curve": [11.111, 2.76, 11.556, -3.18]}, {"time": 12, "value": -3.18, "curve": [12.335, -3.18, 12.67, 0.14]}, {"time": 13, "value": 1.81}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "arm_R3": {"rotate": [{"value": 1.82, "curve": [0.225, 3.43, 0.446, 5.06]}, {"time": 0.6667, "value": 5.06, "curve": [1.111, 5.06, 1.556, -1.42]}, {"time": 2, "value": -1.42, "curve": [2.444, -1.42, 2.889, 5.06]}, {"time": 3.3333, "value": 5.06, "curve": [3.778, 5.06, 4.222, -1.42]}, {"time": 4.6667, "value": -1.42, "curve": [5.111, -1.42, 5.556, 5.06]}, {"time": 6, "value": 5.06, "curve": [6.333, 5.06, 6.667, 11.9]}, {"time": 7, "value": 11.9, "curve": [7.444, 11.9, 7.889, 8.48]}, {"time": 8.3333, "value": 8.48, "curve": [8.778, 8.48, 9.222, 11.9]}, {"time": 9.6667, "value": 11.9, "curve": [10.111, 11.9, 10.556, 5.06]}, {"time": 11, "value": 5.06, "curve": [11.444, 5.06, 11.889, -1.42]}, {"time": 12.3333, "value": -1.42, "curve": [12.557, -1.42, 12.781, 0.19]}, {"time": 13, "value": 1.82}]}, "RU_L": {"translate": [{"x": -11.63, "curve": [0.168, -22.84, 0.334, -32.04, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -32.04, "curve": [0.944, -32.04, 1.389, 32.11, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 32.13, "curve": [2.278, 32.15, 2.722, -32.03, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -32.04, "curve": [3.611, -32.06, 4.056, 32.11, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 32.13, "curve": [4.944, 32.15, 5.389, -32.02, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -32.04, "curve": [6.167, -32.06, 6.5, 32.12, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 32.13, "curve": [7.278, 32.15, 7.722, -32.03, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -32.04, "curve": [8.611, -32.06, 9.056, 32.11, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 32.13, "curve": [9.944, 32.15, 10.389, -32.03, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -32.04, "curve": [11.278, -32.06, 11.722, 32.11, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 32.13, "curve": [12.445, 32.14, 12.724, 7.19, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -11.63}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.167, 1.012, 0.333, 0.926, 0.167, 0.992, 0.333, 1.04]}, {"time": 0.5, "x": 0.926, "y": 1.04, "curve": [0.722, 0.926, 0.944, 1.079, 0.722, 1.04, 0.944, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955, "curve": [1.389, 1.079, 1.611, 0.926, 1.389, 0.955, 1.611, 1.04]}, {"time": 1.8333, "x": 0.926, "y": 1.04, "curve": [2.056, 0.926, 2.278, 1.079, 2.056, 1.04, 2.278, 0.955]}, {"time": 2.5, "x": 1.079, "y": 0.955, "curve": [2.722, 1.079, 2.944, 0.926, 2.722, 0.955, 2.944, 1.04]}, {"time": 3.1667, "x": 0.926, "y": 1.04, "curve": [3.389, 0.926, 3.611, 1.079, 3.389, 1.04, 3.611, 0.955]}, {"time": 3.8333, "x": 1.079, "y": 0.955, "curve": [4.056, 1.079, 4.278, 0.926, 4.056, 0.955, 4.278, 1.04]}, {"time": 4.5, "x": 0.926, "y": 1.04, "curve": [4.722, 0.926, 4.944, 1.079, 4.722, 1.04, 4.944, 0.955]}, {"time": 5.1667, "x": 1.079, "y": 0.955, "curve": [5.389, 1.079, 5.611, 0.926, 5.389, 0.955, 5.611, 1.04]}, {"time": 5.8333, "x": 0.926, "y": 1.04, "curve": [6, 0.926, 6.167, 1.079, 6, 1.04, 6.167, 0.955]}, {"time": 6.3333, "x": 1.079, "y": 0.955, "curve": [6.5, 1.079, 6.667, 0.926, 6.5, 0.955, 6.667, 1.04]}, {"time": 6.8333, "x": 0.926, "y": 1.04, "curve": [7.056, 0.926, 7.278, 1.079, 7.056, 1.04, 7.278, 0.955]}, {"time": 7.5, "x": 1.079, "y": 0.955, "curve": [7.722, 1.079, 7.944, 0.926, 7.722, 0.955, 7.944, 1.04]}, {"time": 8.1667, "x": 0.926, "y": 1.04, "curve": [8.389, 0.926, 8.611, 1.079, 8.389, 1.04, 8.611, 0.955]}, {"time": 8.8333, "x": 1.079, "y": 0.955, "curve": [9.056, 1.079, 9.278, 0.926, 9.056, 0.955, 9.278, 1.04]}, {"time": 9.5, "x": 0.926, "y": 1.04, "curve": [9.722, 0.926, 9.944, 1.079, 9.722, 1.04, 9.944, 0.955]}, {"time": 10.1667, "x": 1.079, "y": 0.955, "curve": [10.389, 1.079, 10.611, 0.926, 10.389, 0.955, 10.611, 1.04]}, {"time": 10.8333, "x": 0.926, "y": 1.04, "curve": [11.056, 0.926, 11.278, 1.079, 11.056, 1.04, 11.278, 0.955]}, {"time": 11.5, "x": 1.079, "y": 0.955, "curve": [11.722, 1.079, 11.944, 0.926, 11.722, 0.955, 11.944, 1.04]}, {"time": 12.1667, "x": 0.926, "y": 1.04, "curve": [12.389, 0.926, 12.611, 1.079, 12.389, 1.04, 12.611, 0.955]}, {"time": 12.8333, "x": 1.079, "y": 0.955, "curve": [12.889, 1.079, 12.944, 1.069, 12.889, 0.955, 12.944, 0.961]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_L2": {"translate": [{"x": -0.68, "curve": [0.225, -15.15, 0.446, -29.83, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -29.83, "curve": [1.111, -29.83, 1.556, 28.45, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.46, "curve": [2.444, 28.48, 2.889, -29.81, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -29.83, "curve": [3.778, -29.84, 4.222, 28.45, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.46, "curve": [5.111, 28.48, 5.556, -29.81, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -29.83, "curve": [6.333, -29.84, 6.667, 28.45, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 28.46, "curve": [7.444, 28.48, 7.889, -29.81, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -29.83, "curve": [8.778, -29.84, 9.222, 28.45, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.46, "curve": [10.111, 28.48, 10.556, -29.81, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -29.83, "curve": [11.444, -29.84, 11.889, 28.45, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.46, "curve": [12.557, 28.47, 12.781, 13.99, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -0.68}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.222, 1.079, 0.444, 0.926, 0.222, 0.955, 0.444, 1.04]}, {"time": 0.6667, "x": 0.926, "y": 1.04, "curve": [0.889, 0.926, 1.111, 1.079, 0.889, 1.04, 1.111, 0.955]}, {"time": 1.3333, "x": 1.079, "y": 0.955, "curve": [1.556, 1.079, 1.778, 0.926, 1.556, 0.955, 1.778, 1.04]}, {"time": 2, "x": 0.926, "y": 1.04, "curve": [2.222, 0.926, 2.444, 1.079, 2.222, 1.04, 2.444, 0.955]}, {"time": 2.6667, "x": 1.079, "y": 0.955, "curve": [2.889, 1.079, 3.111, 0.926, 2.889, 0.955, 3.111, 1.04]}, {"time": 3.3333, "x": 0.926, "y": 1.04, "curve": [3.556, 0.926, 3.778, 1.079, 3.556, 1.04, 3.778, 0.955]}, {"time": 4, "x": 1.079, "y": 0.955, "curve": [4.222, 1.079, 4.444, 0.926, 4.222, 0.955, 4.444, 1.04]}, {"time": 4.6667, "x": 0.926, "y": 1.04, "curve": [4.889, 0.926, 5.111, 1.079, 4.889, 1.04, 5.111, 0.955]}, {"time": 5.3333, "x": 1.079, "y": 0.955, "curve": [5.556, 1.079, 5.778, 0.926, 5.556, 0.955, 5.778, 1.04]}, {"time": 6, "x": 0.926, "y": 1.04, "curve": [6.167, 0.926, 6.333, 1.079, 6.167, 1.04, 6.333, 0.955]}, {"time": 6.5, "x": 1.079, "y": 0.955, "curve": [6.667, 1.079, 6.833, 0.926, 6.667, 0.955, 6.833, 1.04]}, {"time": 7, "x": 0.926, "y": 1.04, "curve": [7.222, 0.926, 7.444, 1.079, 7.222, 1.04, 7.444, 0.955]}, {"time": 7.6667, "x": 1.079, "y": 0.955, "curve": [7.889, 1.079, 8.111, 0.926, 7.889, 0.955, 8.111, 1.04]}, {"time": 8.3333, "x": 0.926, "y": 1.04, "curve": [8.556, 0.926, 8.778, 1.079, 8.556, 1.04, 8.778, 0.955]}, {"time": 9, "x": 1.079, "y": 0.955, "curve": [9.222, 1.079, 9.444, 0.926, 9.222, 0.955, 9.444, 1.04]}, {"time": 9.6667, "x": 0.926, "y": 1.04, "curve": [9.889, 0.926, 10.111, 1.079, 9.889, 1.04, 10.111, 0.955]}, {"time": 10.3333, "x": 1.079, "y": 0.955, "curve": [10.556, 1.079, 10.778, 0.926, 10.556, 0.955, 10.778, 1.04]}, {"time": 11, "x": 0.926, "y": 1.04, "curve": [11.222, 0.926, 11.444, 1.079, 11.222, 1.04, 11.444, 0.955]}, {"time": 11.6667, "x": 1.079, "y": 0.955, "curve": [11.889, 1.079, 12.111, 0.926, 11.889, 0.955, 12.111, 1.04]}, {"time": 12.3333, "x": 0.926, "y": 1.04, "curve": [12.556, 0.926, 12.778, 1.079, 12.556, 1.04, 12.778, 0.955]}, {"time": 13, "x": 1.079, "y": 0.955}]}, "RU_L3": {"translate": [{"x": 10.34, "curve": [0.279, -4.98, 0.556, -25.36, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.36, "curve": [1.278, -25.36, 1.722, 26.97, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 26.98, "curve": [2.611, 27, 3.056, -25.35, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.36, "curve": [3.944, -25.38, 4.389, 26.97, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 26.98, "curve": [5.278, 27, 5.722, -25.35, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.36, "curve": [6.5, -25.38, 6.833, 26.97, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 26.98, "curve": [7.611, 27, 8.056, -25.35, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -25.36, "curve": [8.944, -25.38, 9.389, 26.97, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 26.98, "curve": [10.278, 27, 10.722, -25.35, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.36, "curve": [11.611, -25.38, 12.056, 26.97, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 26.98, "curve": [12.667, 26.99, 12.835, 19.6, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 10.34}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.056, 1.069, 0.111, 1.079, 0.056, 0.961, 0.111, 0.955]}, {"time": 0.1667, "x": 1.079, "y": 0.955, "curve": [0.389, 1.079, 0.611, 0.926, 0.389, 0.955, 0.611, 1.04]}, {"time": 0.8333, "x": 0.926, "y": 1.04, "curve": [1.056, 0.926, 1.278, 1.079, 1.056, 1.04, 1.278, 0.955]}, {"time": 1.5, "x": 1.079, "y": 0.955, "curve": [1.722, 1.079, 1.944, 0.926, 1.722, 0.955, 1.944, 1.04]}, {"time": 2.1667, "x": 0.926, "y": 1.04, "curve": [2.389, 0.926, 2.611, 1.079, 2.389, 1.04, 2.611, 0.955]}, {"time": 2.8333, "x": 1.079, "y": 0.955, "curve": [3.056, 1.079, 3.278, 0.926, 3.056, 0.955, 3.278, 1.04]}, {"time": 3.5, "x": 0.926, "y": 1.04, "curve": [3.722, 0.926, 3.944, 1.079, 3.722, 1.04, 3.944, 0.955]}, {"time": 4.1667, "x": 1.079, "y": 0.955, "curve": [4.389, 1.079, 4.611, 0.926, 4.389, 0.955, 4.611, 1.04]}, {"time": 4.8333, "x": 0.926, "y": 1.04, "curve": [5.056, 0.926, 5.278, 1.079, 5.056, 1.04, 5.278, 0.955]}, {"time": 5.5, "x": 1.079, "y": 0.955, "curve": [5.722, 1.079, 5.944, 0.926, 5.722, 0.955, 5.944, 1.04]}, {"time": 6.1667, "x": 0.926, "y": 1.04, "curve": [6.333, 0.926, 6.5, 1.079, 6.333, 1.04, 6.5, 0.955]}, {"time": 6.6667, "x": 1.079, "y": 0.955, "curve": [6.833, 1.079, 7, 0.926, 6.833, 0.955, 7, 1.04]}, {"time": 7.1667, "x": 0.926, "y": 1.04, "curve": [7.389, 0.926, 7.611, 1.079, 7.389, 1.04, 7.611, 0.955]}, {"time": 7.8333, "x": 1.079, "y": 0.955, "curve": [8.056, 1.079, 8.278, 0.926, 8.056, 0.955, 8.278, 1.04]}, {"time": 8.5, "x": 0.926, "y": 1.04, "curve": [8.722, 0.926, 8.944, 1.079, 8.722, 1.04, 8.944, 0.955]}, {"time": 9.1667, "x": 1.079, "y": 0.955, "curve": [9.389, 1.079, 9.611, 0.926, 9.389, 0.955, 9.611, 1.04]}, {"time": 9.8333, "x": 0.926, "y": 1.04, "curve": [10.056, 0.926, 10.278, 1.079, 10.056, 1.04, 10.278, 0.955]}, {"time": 10.5, "x": 1.079, "y": 0.955, "curve": [10.722, 1.079, 10.944, 0.926, 10.722, 0.955, 10.944, 1.04]}, {"time": 11.1667, "x": 0.926, "y": 1.04, "curve": [11.389, 0.926, 11.611, 1.079, 11.389, 1.04, 11.611, 0.955]}, {"time": 11.8333, "x": 1.079, "y": 0.955, "curve": [12.056, 1.079, 12.278, 0.926, 12.056, 0.955, 12.278, 1.04]}, {"time": 12.5, "x": 0.926, "y": 1.04, "curve": [12.667, 0.926, 12.833, 1.012, 12.667, 1.04, 12.833, 0.992]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_R": {"translate": [{"x": -7.06, "curve": [0.168, -17.1, 0.334, -25.34, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -25.34, "curve": [0.944, -25.34, 1.389, 32.12, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 32.13, "curve": [2.278, 32.14, 2.722, -25.32, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -25.34, "curve": [3.611, -25.35, 4.056, 32.12, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 32.13, "curve": [4.944, 32.14, 5.389, -25.32, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -25.34, "curve": [6.167, -25.35, 6.5, 32.12, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 32.13, "curve": [7.278, 32.14, 7.722, -25.32, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -25.34, "curve": [8.611, -25.35, 9.056, 32.12, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 32.13, "curve": [9.944, 32.14, 10.389, -25.32, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -25.34, "curve": [11.278, -25.35, 11.722, 32.12, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 32.13, "curve": [12.445, 32.14, 12.724, 9.8, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -7.06}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.167, 1.012, 0.333, 0.926, 0.167, 0.992, 0.333, 1.04]}, {"time": 0.5, "x": 0.926, "y": 1.04, "curve": [0.722, 0.926, 0.944, 1.079, 0.722, 1.04, 0.944, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955, "curve": [1.389, 1.079, 1.611, 0.926, 1.389, 0.955, 1.611, 1.04]}, {"time": 1.8333, "x": 0.926, "y": 1.04, "curve": [2.056, 0.926, 2.278, 1.079, 2.056, 1.04, 2.278, 0.955]}, {"time": 2.5, "x": 1.079, "y": 0.955, "curve": [2.722, 1.079, 2.944, 0.926, 2.722, 0.955, 2.944, 1.04]}, {"time": 3.1667, "x": 0.926, "y": 1.04, "curve": [3.389, 0.926, 3.611, 1.079, 3.389, 1.04, 3.611, 0.955]}, {"time": 3.8333, "x": 1.079, "y": 0.955, "curve": [4.056, 1.079, 4.278, 0.926, 4.056, 0.955, 4.278, 1.04]}, {"time": 4.5, "x": 0.926, "y": 1.04, "curve": [4.722, 0.926, 4.944, 1.079, 4.722, 1.04, 4.944, 0.955]}, {"time": 5.1667, "x": 1.079, "y": 0.955, "curve": [5.389, 1.079, 5.611, 0.926, 5.389, 0.955, 5.611, 1.04]}, {"time": 5.8333, "x": 0.926, "y": 1.04, "curve": [6, 0.926, 6.167, 1.079, 6, 1.04, 6.167, 0.955]}, {"time": 6.3333, "x": 1.079, "y": 0.955, "curve": [6.5, 1.079, 6.667, 0.926, 6.5, 0.955, 6.667, 1.04]}, {"time": 6.8333, "x": 0.926, "y": 1.04, "curve": [7.056, 0.926, 7.278, 1.079, 7.056, 1.04, 7.278, 0.955]}, {"time": 7.5, "x": 1.079, "y": 0.955, "curve": [7.722, 1.079, 7.944, 0.926, 7.722, 0.955, 7.944, 1.04]}, {"time": 8.1667, "x": 0.926, "y": 1.04, "curve": [8.389, 0.926, 8.611, 1.079, 8.389, 1.04, 8.611, 0.955]}, {"time": 8.8333, "x": 1.079, "y": 0.955, "curve": [9.056, 1.079, 9.278, 0.926, 9.056, 0.955, 9.278, 1.04]}, {"time": 9.5, "x": 0.926, "y": 1.04, "curve": [9.722, 0.926, 9.944, 1.079, 9.722, 1.04, 9.944, 0.955]}, {"time": 10.1667, "x": 1.079, "y": 0.955, "curve": [10.389, 1.079, 10.611, 0.926, 10.389, 0.955, 10.611, 1.04]}, {"time": 10.8333, "x": 0.926, "y": 1.04, "curve": [11.056, 0.926, 11.278, 1.079, 11.056, 1.04, 11.278, 0.955]}, {"time": 11.5, "x": 1.079, "y": 0.955, "curve": [11.722, 1.079, 11.944, 0.926, 11.722, 0.955, 11.944, 1.04]}, {"time": 12.1667, "x": 0.926, "y": 1.04, "curve": [12.389, 0.926, 12.611, 1.079, 12.389, 1.04, 12.611, 0.955]}, {"time": 12.8333, "x": 1.079, "y": 0.955, "curve": [12.889, 1.079, 12.944, 1.069, 12.889, 0.955, 12.944, 0.961]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "RU_R2": {"translate": [{"x": 3.05, "curve": [0.225, -9.58, 0.446, -22.38, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -22.38, "curve": [1.111, -22.38, 1.556, 28.45, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 28.46, "curve": [2.444, 28.48, 2.889, -22.36, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -22.38, "curve": [3.778, -22.39, 4.222, 28.45, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 28.46, "curve": [5.111, 28.48, 5.556, -22.36, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -22.38, "curve": [6.333, -22.39, 6.667, 28.45, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 28.46, "curve": [7.444, 28.48, 7.889, -22.36, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -22.38, "curve": [8.778, -22.39, 9.222, 28.45, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 28.46, "curve": [10.111, 28.48, 10.556, -22.36, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -22.38, "curve": [11.444, -22.39, 11.889, 28.45, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 28.46, "curve": [12.557, 28.47, 12.781, 15.84, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 3.05}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.222, 1.079, 0.444, 0.926, 0.222, 0.955, 0.444, 1.04]}, {"time": 0.6667, "x": 0.926, "y": 1.04, "curve": [0.889, 0.926, 1.111, 1.079, 0.889, 1.04, 1.111, 0.955]}, {"time": 1.3333, "x": 1.079, "y": 0.955, "curve": [1.556, 1.079, 1.778, 0.926, 1.556, 0.955, 1.778, 1.04]}, {"time": 2, "x": 0.926, "y": 1.04, "curve": [2.222, 0.926, 2.444, 1.079, 2.222, 1.04, 2.444, 0.955]}, {"time": 2.6667, "x": 1.079, "y": 0.955, "curve": [2.889, 1.079, 3.111, 0.926, 2.889, 0.955, 3.111, 1.04]}, {"time": 3.3333, "x": 0.926, "y": 1.04, "curve": [3.556, 0.926, 3.778, 1.079, 3.556, 1.04, 3.778, 0.955]}, {"time": 4, "x": 1.079, "y": 0.955, "curve": [4.222, 1.079, 4.444, 0.926, 4.222, 0.955, 4.444, 1.04]}, {"time": 4.6667, "x": 0.926, "y": 1.04, "curve": [4.889, 0.926, 5.111, 1.079, 4.889, 1.04, 5.111, 0.955]}, {"time": 5.3333, "x": 1.079, "y": 0.955, "curve": [5.556, 1.079, 5.778, 0.926, 5.556, 0.955, 5.778, 1.04]}, {"time": 6, "x": 0.926, "y": 1.04, "curve": [6.167, 0.926, 6.333, 1.079, 6.167, 1.04, 6.333, 0.955]}, {"time": 6.5, "x": 1.079, "y": 0.955, "curve": [6.667, 1.079, 6.833, 0.926, 6.667, 0.955, 6.833, 1.04]}, {"time": 7, "x": 0.926, "y": 1.04, "curve": [7.222, 0.926, 7.444, 1.079, 7.222, 1.04, 7.444, 0.955]}, {"time": 7.6667, "x": 1.079, "y": 0.955, "curve": [7.889, 1.079, 8.111, 0.926, 7.889, 0.955, 8.111, 1.04]}, {"time": 8.3333, "x": 0.926, "y": 1.04, "curve": [8.556, 0.926, 8.778, 1.079, 8.556, 1.04, 8.778, 0.955]}, {"time": 9, "x": 1.079, "y": 0.955, "curve": [9.222, 1.079, 9.444, 0.926, 9.222, 0.955, 9.444, 1.04]}, {"time": 9.6667, "x": 0.926, "y": 1.04, "curve": [9.889, 0.926, 10.111, 1.079, 9.889, 1.04, 10.111, 0.955]}, {"time": 10.3333, "x": 1.079, "y": 0.955, "curve": [10.556, 1.079, 10.778, 0.926, 10.556, 0.955, 10.778, 1.04]}, {"time": 11, "x": 0.926, "y": 1.04, "curve": [11.222, 0.926, 11.444, 1.079, 11.222, 1.04, 11.444, 0.955]}, {"time": 11.6667, "x": 1.079, "y": 0.955, "curve": [11.889, 1.079, 12.111, 0.926, 11.889, 0.955, 12.111, 1.04]}, {"time": 12.3333, "x": 0.926, "y": 1.04, "curve": [12.556, 0.926, 12.778, 1.079, 12.556, 1.04, 12.778, 0.955]}, {"time": 13, "x": 1.079, "y": 0.955}]}, "RU_R3": {"translate": [{"x": 12.95, "curve": [0.279, 0.03, 0.556, -17.16, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -17.16, "curve": [1.278, -17.16, 1.722, 26.97, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 26.98, "curve": [2.611, 26.99, 3.056, -17.15, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -17.16, "curve": [3.944, -17.17, 4.389, 26.97, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 26.98, "curve": [5.278, 26.99, 5.722, -17.15, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -17.16, "curve": [6.5, -17.17, 6.833, 26.97, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 26.98, "curve": [7.611, 26.99, 8.056, -17.15, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -17.16, "curve": [8.944, -17.17, 9.389, 26.97, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 26.98, "curve": [10.278, 26.99, 10.722, -17.15, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -17.16, "curve": [11.611, -17.17, 12.056, 26.97, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 26.98, "curve": [12.667, 26.99, 12.835, 20.76, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 12.95}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.056, 1.069, 0.111, 1.079, 0.056, 0.961, 0.111, 0.955]}, {"time": 0.1667, "x": 1.079, "y": 0.955, "curve": [0.389, 1.079, 0.611, 0.926, 0.389, 0.955, 0.611, 1.04]}, {"time": 0.8333, "x": 0.926, "y": 1.04, "curve": [1.056, 0.926, 1.278, 1.079, 1.056, 1.04, 1.278, 0.955]}, {"time": 1.5, "x": 1.079, "y": 0.955, "curve": [1.722, 1.079, 1.944, 0.926, 1.722, 0.955, 1.944, 1.04]}, {"time": 2.1667, "x": 0.926, "y": 1.04, "curve": [2.389, 0.926, 2.611, 1.079, 2.389, 1.04, 2.611, 0.955]}, {"time": 2.8333, "x": 1.079, "y": 0.955, "curve": [3.056, 1.079, 3.278, 0.926, 3.056, 0.955, 3.278, 1.04]}, {"time": 3.5, "x": 0.926, "y": 1.04, "curve": [3.722, 0.926, 3.944, 1.079, 3.722, 1.04, 3.944, 0.955]}, {"time": 4.1667, "x": 1.079, "y": 0.955, "curve": [4.389, 1.079, 4.611, 0.926, 4.389, 0.955, 4.611, 1.04]}, {"time": 4.8333, "x": 0.926, "y": 1.04, "curve": [5.056, 0.926, 5.278, 1.079, 5.056, 1.04, 5.278, 0.955]}, {"time": 5.5, "x": 1.079, "y": 0.955, "curve": [5.722, 1.079, 5.944, 0.926, 5.722, 0.955, 5.944, 1.04]}, {"time": 6.1667, "x": 0.926, "y": 1.04, "curve": [6.333, 0.926, 6.5, 1.079, 6.333, 1.04, 6.5, 0.955]}, {"time": 6.6667, "x": 1.079, "y": 0.955, "curve": [6.833, 1.079, 7, 0.926, 6.833, 0.955, 7, 1.04]}, {"time": 7.1667, "x": 0.926, "y": 1.04, "curve": [7.389, 0.926, 7.611, 1.079, 7.389, 1.04, 7.611, 0.955]}, {"time": 7.8333, "x": 1.079, "y": 0.955, "curve": [8.056, 1.079, 8.278, 0.926, 8.056, 0.955, 8.278, 1.04]}, {"time": 8.5, "x": 0.926, "y": 1.04, "curve": [8.722, 0.926, 8.944, 1.079, 8.722, 1.04, 8.944, 0.955]}, {"time": 9.1667, "x": 1.079, "y": 0.955, "curve": [9.389, 1.079, 9.611, 0.926, 9.389, 0.955, 9.611, 1.04]}, {"time": 9.8333, "x": 0.926, "y": 1.04, "curve": [10.056, 0.926, 10.278, 1.079, 10.056, 1.04, 10.278, 0.955]}, {"time": 10.5, "x": 1.079, "y": 0.955, "curve": [10.722, 1.079, 10.944, 0.926, 10.722, 0.955, 10.944, 1.04]}, {"time": 11.1667, "x": 0.926, "y": 1.04, "curve": [11.389, 0.926, 11.611, 1.079, 11.389, 1.04, 11.611, 0.955]}, {"time": 11.8333, "x": 1.079, "y": 0.955, "curve": [12.056, 1.079, 12.278, 0.926, 12.056, 0.955, 12.278, 1.04]}, {"time": 12.5, "x": 0.926, "y": 1.04, "curve": [12.667, 0.926, 12.833, 1.012, 12.667, 1.04, 12.833, 0.992]}, {"time": 13, "x": 1.055, "y": 0.968}]}, "leg_L2": {"rotate": [{"value": -1.92, "curve": [0.444, -1.92, 0.889, 1.82]}, {"time": 1.3333, "value": 1.82, "curve": [1.778, 1.82, 2.222, -1.92]}, {"time": 2.6667, "value": -1.92, "curve": [3.111, -1.92, 3.556, 1.82]}, {"time": 4, "value": 1.82, "curve": [4.444, 1.82, 4.889, -1.92]}, {"time": 5.3333, "value": -1.92, "curve": [5.667, -1.92, 6, 1.82]}, {"time": 6.3333, "value": 1.82, "curve": [6.778, 1.82, 7.222, -1.92]}, {"time": 7.6667, "value": -1.92, "curve": [8.111, -1.92, 8.556, 1.82]}, {"time": 9, "value": 1.82, "curve": [9.444, 1.82, 9.889, -1.92]}, {"time": 10.3333, "value": -1.92, "curve": [10.778, -1.92, 11.222, 1.82]}, {"time": 11.6667, "value": 1.82, "curve": [12.111, 1.82, 12.556, -1.92]}, {"time": 13, "value": -1.92}]}, "leg_L3": {"rotate": [{"value": -1.43, "curve": [0.114, -1.75, 0.224, -1.98]}, {"time": 0.3333, "value": -1.98, "curve": [0.778, -1.98, 1.222, 1.47]}, {"time": 1.6667, "value": 1.47, "curve": [2.111, 1.47, 2.556, -1.98]}, {"time": 3, "value": -1.98, "curve": [3.444, -1.98, 3.889, 1.47]}, {"time": 4.3333, "value": 1.47, "curve": [4.778, 1.47, 5.222, -1.98]}, {"time": 5.6667, "value": -1.98, "curve": [6, -1.98, 6.333, 1.47]}, {"time": 6.6667, "value": 1.47, "curve": [7.111, 1.47, 7.556, -1.98]}, {"time": 8, "value": -1.98, "curve": [8.444, -1.98, 8.889, 1.47]}, {"time": 9.3333, "value": 1.47, "curve": [9.778, 1.47, 10.222, -1.98]}, {"time": 10.6667, "value": -1.98, "curve": [11.111, -1.98, 11.556, 1.47]}, {"time": 12, "value": 1.47, "curve": [12.335, 1.47, 12.67, -0.47]}, {"time": 13, "value": -1.43}]}, "leg_L4": {"rotate": [{"value": -1.19, "curve": [0.168, -2.73, 0.334, -4]}, {"time": 0.5, "value": -4, "curve": [0.944, -4, 1.389, 4.85]}, {"time": 1.8333, "value": 4.85, "curve": [2.278, 4.85, 2.722, -4]}, {"time": 3.1667, "value": -4, "curve": [3.611, -4, 4.056, 4.85]}, {"time": 4.5, "value": 4.85, "curve": [4.944, 4.85, 5.389, -4]}, {"time": 5.8333, "value": -4, "curve": [6.167, -4, 6.5, 4.85]}, {"time": 6.8333, "value": 4.85, "curve": [7.278, 4.85, 7.722, -4]}, {"time": 8.1667, "value": -4, "curve": [8.611, -4, 9.056, 4.85]}, {"time": 9.5, "value": 4.85, "curve": [9.944, 4.85, 10.389, -4]}, {"time": 10.8333, "value": -4, "curve": [11.278, -4, 11.722, 4.85]}, {"time": 12.1667, "value": 4.85, "curve": [12.445, 4.85, 12.724, 1.41]}, {"time": 13, "value": -1.19}]}, "leg_L5": {"rotate": [{"value": 0.43, "curve": [0.225, -1.77, 0.446, -4]}, {"time": 0.6667, "value": -4, "curve": [1.111, -4, 1.556, 4.85]}, {"time": 2, "value": 4.85, "curve": [2.444, 4.85, 2.889, -4]}, {"time": 3.3333, "value": -4, "curve": [3.778, -4, 4.222, 4.85]}, {"time": 4.6667, "value": 4.85, "curve": [5.111, 4.85, 5.556, -4]}, {"time": 6, "value": -4, "curve": [6.333, -4, 6.667, 4.85]}, {"time": 7, "value": 4.85, "curve": [7.444, 4.85, 7.889, -4]}, {"time": 8.3333, "value": -4, "curve": [8.778, -4, 9.222, 4.85]}, {"time": 9.6667, "value": 4.85, "curve": [10.111, 4.85, 10.556, -4]}, {"time": 11, "value": -4, "curve": [11.444, -4, 11.889, 4.85]}, {"time": 12.3333, "value": 4.85, "curve": [12.557, 4.85, 12.781, 2.65]}, {"time": 13, "value": 0.43}]}, "leg_R2": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "wine2": {"translate": [{"x": 0.11, "y": 0.06, "curve": [0.225, 0.06, 0.446, 0, 0.225, 0.03, 0.446, 0]}, {"time": 0.6667, "curve": [1.111, 0, 1.556, 0.23, 1.111, 0, 1.556, 0.12]}, {"time": 2, "x": 0.23, "y": 0.12, "curve": [2.444, 0.23, 2.889, 0, 2.444, 0.12, 2.889, 0]}, {"time": 3.3333, "curve": [3.778, 0, 4.222, 0.23, 3.778, 0, 4.222, 0.12]}, {"time": 4.6667, "x": 0.23, "y": 0.12, "curve": [5.111, 0.23, 5.556, 0, 5.111, 0.12, 5.556, 0]}, {"time": 6, "curve": [6.333, 0, 6.667, 0.23, 6.333, 0, 6.667, 0.12]}, {"time": 7, "x": 0.23, "y": 0.12, "curve": [7.444, 0.23, 7.889, -0.21, 7.444, 0.12, 7.889, 0.47]}, {"time": 8.3333, "x": -0.21, "y": 0.47, "curve": [8.778, -0.21, 9.222, 0.23, 8.778, 0.47, 9.222, 0.12]}, {"time": 9.6667, "x": 0.23, "y": 0.12, "curve": [10.111, 0.23, 10.556, 0, 10.111, 0.12, 10.556, 0]}, {"time": 11, "curve": [11.444, 0, 11.889, 0.23, 11.444, 0, 11.889, 0.12]}, {"time": 12.3333, "x": 0.23, "y": 0.12, "curve": [12.557, 0.23, 12.781, 0.17, 12.557, 0.12, 12.781, 0.09]}, {"time": 13, "x": 0.11, "y": 0.06}], "scale": [{"x": 1.005, "curve": [0.225, 0.997, 0.446, 0.989, 0.225, 1, 0.446, 1]}, {"time": 0.6667, "x": 0.989, "curve": [1.111, 0.989, 1.556, 1.022, 1.111, 1, 1.556, 1]}, {"time": 2, "x": 1.022, "curve": [2.444, 1.022, 2.889, 0.989, 2.444, 1, 2.889, 1]}, {"time": 3.3333, "x": 0.989, "curve": [3.778, 0.989, 4.222, 1.022, 3.778, 1, 4.222, 1]}, {"time": 4.6667, "x": 1.022, "curve": [5.111, 1.022, 5.556, 0.989, 5.111, 1, 5.556, 1]}, {"time": 6, "x": 0.989, "curve": [6.333, 0.989, 6.667, 0.952, 6.333, 1, 6.667, 1]}, {"time": 7, "x": 0.952, "curve": [7.444, 0.952, 7.889, 0.961, 7.444, 1, 7.889, 1]}, {"time": 8.3333, "x": 0.961, "curve": [8.778, 0.961, 9.222, 0.952, 8.778, 1, 9.222, 1]}, {"time": 9.6667, "x": 0.952, "curve": [10.111, 0.952, 10.556, 0.989, 10.111, 1, 10.556, 1]}, {"time": 11, "x": 0.989, "curve": [11.444, 0.989, 11.889, 1.022, 11.444, 1, 11.889, 1]}, {"time": 12.3333, "x": 1.022, "curve": [12.557, 1.022, 12.781, 1.014, 12.557, 1, 12.781, 1]}, {"time": 13, "x": 1.005}]}, "wine3": {"rotate": [{"value": -69.09}, {"time": 1.3, "value": -420.09, "curve": "stepped"}, {"time": 1.3333, "value": -69.09}, {"time": 2.6333, "value": -420.09, "curve": "stepped"}, {"time": 2.6667, "value": -69.09}, {"time": 3.9667, "value": -420.09, "curve": "stepped"}, {"time": 4, "value": -69.09}, {"time": 5.3, "value": -420.09, "curve": "stepped"}, {"time": 5.3333, "value": -69.09}, {"time": 6.3, "value": -417.09, "curve": "stepped"}, {"time": 6.3333, "value": -69.09}, {"time": 7.6333, "value": -420.09, "curve": "stepped"}, {"time": 7.6667, "value": -69.09}, {"time": 8.9667, "value": -420.09, "curve": "stepped"}, {"time": 9, "value": -69.09}, {"time": 10.3, "value": -420.09, "curve": "stepped"}, {"time": 10.3333, "value": -69.09}, {"time": 11.6333, "value": -420.09, "curve": "stepped"}, {"time": 11.6667, "value": -69.09}, {"time": 12.9667, "value": -420.09, "curve": "stepped"}, {"time": 13, "value": -69.09}]}, "head4": {"translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.92, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.92, "curve": "stepped"}, {"time": 9.5, "x": 1.92, "curve": [9.722, 1.92, 9.944, 0, 9.585, 0, 10.082, 0]}, {"time": 10.1667}]}, "head5": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, -3.41]}, {"time": 6.3333, "value": -3.41, "curve": "stepped"}, {"time": 9.5, "value": -3.41, "curve": [9.722, -3.41, 9.944, 0]}, {"time": 10.1667}], "scale": [{"time": 6, "curve": [6.111, 1, 6.222, 0.988, 6.148, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.988, "curve": "stepped"}, {"time": 9.5, "x": 0.988, "curve": [9.722, 0.988, 9.944, 1, 9.585, 1, 10.082, 1]}, {"time": 10.1667}]}, "head6": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, -3.12]}, {"time": 6.3333, "value": -3.12, "curve": "stepped"}, {"time": 9.5, "value": -3.12, "curve": [9.722, -3.12, 9.944, 0]}, {"time": 10.1667}]}, "head7": {"translate": [{"curve": [0.444, 0, 5.704, 0, 0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.92, 6.148, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.92, "curve": "stepped"}, {"time": 9.5, "x": 1.92, "curve": [9.722, 1.92, 9.944, 0, 9.585, 0, 10.082, 0]}, {"time": 10.1667}]}, "head8": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 6.16]}, {"time": 6.3333, "value": 6.16, "curve": "stepped"}, {"time": 9.5, "value": 6.16, "curve": [9.722, 6.16, 9.944, 0]}, {"time": 10.1667}], "scale": [{"time": 6, "curve": [6.111, 1, 6.222, 0.988, 6.148, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.988, "curve": "stepped"}, {"time": 9.5, "x": 0.988, "curve": [9.722, 0.988, 9.944, 1, 9.585, 1, 10.082, 1]}, {"time": 10.1667}]}, "head9": {"rotate": [{"curve": [0.444, 0, 5.704, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 3.31]}, {"time": 6.3333, "value": 3.31, "curve": "stepped"}, {"time": 9.5, "value": 3.31, "curve": [9.722, 3.31, 9.944, 0]}, {"time": 10.1667}]}, "hair_F": {"rotate": [{"value": 2.67, "curve": [0.279, 0.09, 0.556, -3.34]}, {"time": 0.8333, "value": -3.34, "curve": [1.278, -3.34, 1.722, 5.47]}, {"time": 2.1667, "value": 5.47, "curve": [2.611, 5.48, 3.056, -3.34]}, {"time": 3.5, "value": -3.34, "curve": [3.944, -3.34, 4.389, 5.47]}, {"time": 4.8333, "value": 5.47, "curve": [5.278, 5.48, 5.722, -3.34]}, {"time": 6.1667, "value": -3.34, "curve": [6.5, -3.34, 6.833, 5.47]}, {"time": 7.1667, "value": 5.47, "curve": [7.611, 5.48, 8.056, -3.34]}, {"time": 8.5, "value": -3.34, "curve": [8.944, -3.34, 9.389, 5.47]}, {"time": 9.8333, "value": 5.47, "curve": [10.278, 5.48, 10.722, -3.34]}, {"time": 11.1667, "value": -3.34, "curve": [11.611, -3.34, 12.056, 5.47]}, {"time": 12.5, "value": 5.47, "curve": [12.667, 5.48, 12.835, 4.23]}, {"time": 13, "value": 2.67}]}, "hair_B": {"rotate": [{"value": 4.07, "curve": [0.336, 1.58, 0.668, -3.34]}, {"time": 1, "value": -3.34, "curve": [1.444, -3.34, 1.889, 5.47]}, {"time": 2.3333, "value": 5.47, "curve": [2.778, 5.48, 3.222, -3.34]}, {"time": 3.6667, "value": -3.34, "curve": [4.111, -3.34, 4.556, 5.47]}, {"time": 5, "value": 5.47, "curve": [5.444, 5.48, 5.889, -3.34]}, {"time": 6.3333, "value": -3.34, "curve": [6.667, -3.34, 7, 5.47]}, {"time": 7.3333, "value": 5.47, "curve": [7.778, 5.48, 8.222, -3.34]}, {"time": 8.6667, "value": -3.34, "curve": [9.111, -3.34, 9.556, 5.47]}, {"time": 10, "value": 5.47, "curve": [10.444, 5.48, 10.889, -3.34]}, {"time": 11.3333, "value": -3.34, "curve": [11.778, -3.34, 12.222, 5.47]}, {"time": 12.6667, "value": 5.47, "curve": [12.779, 5.48, 12.892, 4.91]}, {"time": 13, "value": 4.07}]}, "headround3": {"translate": [{"x": 28.66, "curve": [0.279, -43.33, 0.556, -139.1, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -139.1, "curve": [1.278, -139.1, 1.722, 106.79, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 106.85, "curve": [2.611, 106.91, 3.056, -139.03, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -139.1, "curve": [3.944, -139.16, 4.389, 106.79, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 106.85, "curve": [5.278, 106.91, 5.722, -139.01, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -139.1, "curve": [6.5, -139.16, 6.833, 106.83, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 106.85, "curve": [7.611, 106.86, 8.056, 41.1, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 41.08, "curve": [8.944, 41.06, 9.389, 106.79, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 106.85, "curve": [10.278, 106.91, 10.722, -139.03, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -139.1, "curve": [11.611, -139.16, 12.056, 106.79, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 106.85, "curve": [12.667, 106.87, 12.835, 72.17, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 28.66}]}, "headround": {"translate": [{"y": -144.57, "curve": [0.336, 0, 0.668, 0, 0.336, -26.24, 0.668, 207.41]}, {"time": 1, "y": 207.41, "curve": [1.444, 0, 1.889, 0, 1.444, 207.41, 1.889, -211.46]}, {"time": 2.3333, "y": -211.57, "curve": [2.778, 0, 3.222, 0, 2.778, -211.67, 3.222, 207.3]}, {"time": 3.6667, "y": 207.41, "curve": [4.111, 0, 4.556, 0, 4.111, 207.51, 4.556, -211.46]}, {"time": 5, "y": -211.57, "curve": [5.444, 0, 5.889, 0, 5.444, -211.67, 5.889, 207.27]}, {"time": 6.3333, "y": 207.41, "curve": [6.667, 0, 7, 0, 6.667, 207.51, 7, -211.54]}, {"time": 7.3333, "y": -211.57, "curve": [7.778, 0, 8.222, 0, 7.778, -211.6, 8.222, -85.97]}, {"time": 8.6667, "y": -85.94, "curve": [9.111, 0, 9.556, 0, 9.111, -85.91, 9.556, -211.46]}, {"time": 10, "y": -211.57, "curve": [10.444, 0, 10.889, 0, 10.444, -211.67, 10.889, 207.3]}, {"time": 11.3333, "y": 207.41, "curve": [11.778, 0, 12.222, 0, 11.778, 207.51, 12.222, -211.46]}, {"time": 12.6667, "y": -211.57, "curve": [12.779, 0, 12.892, 0, 12.779, -211.59, 12.892, -184.72]}, {"time": 13, "y": -144.57}]}, "bodyround": {"translate": [{"y": 81.84, "curve": [0.168, 0, 0.334, 0, 0.168, 163.66, 0.334, 230.79]}, {"time": 0.5, "y": 230.79, "curve": [0.944, 0, 1.389, 0, 0.944, 230.79, 1.389, -237.4]}, {"time": 1.8333, "y": -237.52, "curve": [2.278, 0, 2.722, 0, 2.278, -237.63, 2.722, 230.68]}, {"time": 3.1667, "y": 230.79, "curve": [3.611, 0, 4.056, 0, 3.611, 230.91, 4.056, -237.4]}, {"time": 4.5, "y": -237.52, "curve": [4.944, 0, 5.389, 0, 4.944, -237.63, 5.389, 230.64]}, {"time": 5.8333, "y": 230.79, "curve": [6.167, 0, 6.5, 0, 6.167, 230.91, 6.5, -237.49]}, {"time": 6.8333, "y": -237.52, "curve": [7.278, 0, 7.722, 0, 7.278, -237.55, 7.722, -97.72]}, {"time": 8.1667, "y": -97.69, "curve": [8.611, 0, 9.056, 0, 8.611, -97.65, 9.056, -237.4]}, {"time": 9.5, "y": -237.52, "curve": [9.944, 0, 10.389, 0, 9.944, -237.63, 10.389, 230.68]}, {"time": 10.8333, "y": 230.79, "curve": [11.278, 0, 11.722, 0, 11.278, 230.91, 11.722, -237.4]}, {"time": 12.1667, "y": -237.52, "curve": [12.445, 0, 12.724, 0, 12.445, -237.59, 12.724, -55.5]}, {"time": 13, "y": 81.84}]}, "sh_L2": {"rotate": [{"value": -0.22}]}, "sh_L3": {"rotate": [{"value": 0.65}]}, "sh_R2": {"rotate": [{"value": 0.33}]}, "sh_R3": {"rotate": [{}]}, "arm_R4": {"translate": [{"x": -3, "y": 8.15, "curve": [0.168, -7.44, 0.334, -11.08, 0.168, 10.3, 0.334, 12.07]}, {"time": 0.5, "x": -11.08, "y": 12.07, "curve": [0.944, -11.08, 1.389, 14.32, 0.944, 12.07, 1.389, -0.23]}, {"time": 1.8333, "x": 14.32, "y": -0.24, "curve": [2.278, 14.33, 2.722, -11.07, 2.278, -0.24, 2.722, 12.06]}, {"time": 3.1667, "x": -11.08, "y": 12.07, "curve": [3.611, -11.09, 4.056, 14.32, 3.611, 12.07, 4.056, -0.23]}, {"time": 4.5, "x": 14.32, "y": -0.24, "curve": [4.944, 14.33, 5.389, -11.07, 4.944, -0.24, 5.389, 12.08]}, {"time": 5.8333, "x": -11.08, "y": 12.07, "curve": [6.167, -11.09, 6.5, 21.17, 6.167, 12.06, 6.5, 52.63]}, {"time": 6.8333, "x": 21.17, "y": 52.63, "curve": [7.278, 21.17, 7.722, 5.05, 7.278, 52.64, 7.722, 32.35]}, {"time": 8.1667, "x": 5.05, "y": 32.35, "curve": [8.611, 5.04, 9.056, 21.16, 8.611, 32.34, 9.056, 52.62]}, {"time": 9.5, "x": 21.17, "y": 52.63, "curve": [9.944, 21.18, 10.389, -11.07, 9.944, 52.64, 10.389, 12.06]}, {"time": 10.8333, "x": -11.08, "y": 12.07, "curve": [11.278, -11.09, 11.722, 14.32, 11.278, 12.07, 11.722, -0.23]}, {"time": 12.1667, "x": 14.32, "y": -0.24, "curve": [12.445, 14.33, 12.724, 4.45, 12.445, -0.24, 12.724, 4.55]}, {"time": 13, "x": -3, "y": 8.15}]}, "leg_R4": {"rotate": [{"value": 0.18}]}, "leg_R5": {"rotate": [{"value": 0.01}]}, "leg_R6": {"rotate": [{"value": 5.03, "curve": [0.444, 5.03, 0.889, -2.64]}, {"time": 1.3333, "value": -2.65, "curve": [1.778, -2.65, 2.222, 5.03]}, {"time": 2.6667, "value": 5.03, "curve": [3.111, 5.04, 3.556, -2.64]}, {"time": 4, "value": -2.65, "curve": [4.444, -2.65, 4.889, 5.03]}, {"time": 5.3333, "value": 5.03, "curve": [5.667, 5.04, 6, -2.64]}, {"time": 6.3333, "value": -2.65, "curve": [6.778, -2.65, 7.222, 5.03]}, {"time": 7.6667, "value": 5.03, "curve": [8.111, 5.04, 8.556, -2.64]}, {"time": 9, "value": -2.65, "curve": [9.444, -2.65, 9.889, 5.03]}, {"time": 10.3333, "value": 5.03, "curve": [10.778, 5.04, 11.222, -2.64]}, {"time": 11.6667, "value": -2.65, "curve": [12.111, -2.65, 12.556, 5.03]}, {"time": 13, "value": 5.03}], "translate": [{"x": 3.09, "y": 10.37, "curve": [0.444, 3.09, 0.889, -10.8, 0.444, 10.37, 0.889, -20.95]}, {"time": 1.3333, "x": -10.8, "y": -20.96, "curve": [1.778, -10.8, 2.222, 3.08, 1.778, -20.96, 2.222, 10.36]}, {"time": 2.6667, "x": 3.09, "y": 10.37, "curve": [3.111, 3.09, 3.556, -10.8, 3.111, 10.38, 3.556, -20.95]}, {"time": 4, "x": -10.8, "y": -20.96, "curve": [4.444, -10.8, 4.889, 3.08, 4.444, -20.96, 4.889, 10.36]}, {"time": 5.3333, "x": 3.09, "y": 10.37, "curve": [5.667, 3.09, 6, -10.8, 5.667, 10.38, 6, -20.95]}, {"time": 6.3333, "x": -10.8, "y": -20.96, "curve": [6.778, -10.8, 7.222, 3.08, 6.778, -20.96, 7.222, 10.36]}, {"time": 7.6667, "x": 3.09, "y": 10.37, "curve": [8.111, 3.09, 8.556, -10.8, 8.111, 10.38, 8.556, -20.95]}, {"time": 9, "x": -10.8, "y": -20.96, "curve": [9.444, -10.8, 9.889, 3.08, 9.444, -20.96, 9.889, 10.36]}, {"time": 10.3333, "x": 3.09, "y": 10.37, "curve": [10.778, 3.09, 11.222, -10.8, 10.778, 10.38, 11.222, -20.95]}, {"time": 11.6667, "x": -10.8, "y": -20.96, "curve": [12.111, -10.8, 12.556, 3.09, 12.111, -20.96, 12.556, 10.37]}, {"time": 13, "x": 3.09, "y": 10.37}]}, "head2": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.63, 2.033, 0, 2.067, 0.51]}, {"time": 2.1, "x": -1.63, "y": 0.51, "curve": "stepped"}, {"time": 3.0667, "x": -1.63, "y": 0.51, "curve": [3.1, -1.63, 3.133, -0.93, 3.1, 0.51, 3.133, 1.73]}, {"time": 3.1667, "x": -0.93, "y": 1.73, "curve": "stepped"}, {"time": 3.5667, "x": -0.93, "y": 1.73, "curve": [3.6, -0.93, 3.633, 0, 3.6, 1.73, 3.633, 0]}, {"time": 3.6667, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -3.31, 6.2, 0, 6.233, 9.15]}, {"time": 6.2667, "x": -3.31, "y": 9.15, "curve": "stepped"}, {"time": 6.9, "x": -3.31, "y": 9.15, "curve": [6.933, -3.31, 6.967, -4.34, 6.933, 9.15, 6.967, 10.83]}, {"time": 7, "x": -4.34, "y": 10.83, "curve": "stepped"}, {"time": 7.9, "x": -4.34, "y": 10.83, "curve": [7.933, -4.34, 7.967, -4.37, 7.933, 10.83, 7.967, 8.37]}, {"time": 8, "x": -4.37, "y": 8.37, "curve": "stepped"}, {"time": 8.4, "x": -4.37, "y": 8.37, "curve": [8.433, -4.37, 8.467, -3.31, 8.433, 8.37, 8.467, 9.15]}, {"time": 8.5, "x": -3.31, "y": 9.15, "curve": "stepped"}, {"time": 10, "x": -3.31, "y": 9.15, "curve": [10.033, -3.31, 10.067, 0, 10.033, 9.15, 10.067, 0]}, {"time": 10.1}]}, "head3": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.09, 2.033, 0, 2.067, 0.33]}, {"time": 2.1, "x": -1.09, "y": 0.33, "curve": "stepped"}, {"time": 3.0667, "x": -1.09, "y": 0.33, "curve": [3.1, -1.09, 3.133, -0.39, 3.1, 0.33, 3.133, 1.54]}, {"time": 3.1667, "x": -0.39, "y": 1.54, "curve": "stepped"}, {"time": 3.5667, "x": -0.39, "y": 1.54, "curve": [3.6, -0.39, 3.633, 0, 3.6, 1.54, 3.633, 0]}, {"time": 3.6667, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -3.5, 6.2, 0, 6.233, 9.26]}, {"time": 6.2667, "x": -3.5, "y": 9.26, "curve": "stepped"}, {"time": 6.9, "x": -3.5, "y": 9.26, "curve": [6.933, -3.5, 6.967, -4.53, 6.933, 9.26, 6.967, 10.94]}, {"time": 7, "x": -4.53, "y": 10.94, "curve": "stepped"}, {"time": 7.9, "x": -4.53, "y": 10.94, "curve": [7.933, -4.53, 7.967, -5.13, 7.933, 10.94, 7.967, 8.48]}, {"time": 8, "x": -5.13, "y": 8.48, "curve": "stepped"}, {"time": 8.4, "x": -5.13, "y": 8.48, "curve": [8.433, -5.13, 8.467, -3.5, 8.433, 8.48, 8.467, 9.26]}, {"time": 8.5, "x": -3.5, "y": 9.26, "curve": "stepped"}, {"time": 10, "x": -3.5, "y": 9.26, "curve": [10.033, -3.5, 10.067, 0, 10.033, 9.26, 10.067, 0]}, {"time": 10.1}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-3.479, -0.37576, -3.47485, -0.37614, -3.479, -0.37576, -3.47485, -0.37614, -2.81396, -0.28105, -2.81055, -0.28137, -2.13208, -0.02438, -2.12939, -0.02466, -1.15137, -0.04428, -1.14966, -0.04443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.43018, 0.25212, -2.42834, 0.25192, -8.07031, 0.34622, -8.06433, 0.3456, -11.89636, -0.23904, -11.88928, -0.23952, -10.95483, 0.15436, -10.94824, 0.15396, -6.81592, -0.19327, -6.81104, -0.19344, -1.18115, 0.52914, -1.17737, 0.52891, -1.21643, 0.12604, -1.21289, 0.12587, -2.81396, -0.28105, -2.81055, -0.28137, 0, 0, 0, 0, -4.578, 0.06578, -4.57556, 0.06554, -8.99329, 0.11459, -8.9895, 0.11443, -11.84387, 0.2468, -11.84094, 0.24669, -11.25952, 0.34991, -11.2572, 0.34978, -7.49329, 0.36824, -7.49011, 0.36802, -2.45605, -0.0714, -2.45288, -0.0717, -0.66125, -0.09387, -0.66052, -0.09396], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-8.69763, -0.77454, -8.68555, -0.77473, -6.23157, -0.2516, -6.22632, -0.25192, -2.49048, -0.31593, -2.48621, -0.31611, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.5304, 0.01785, -1.52539, 0.01738, -1.73706, -0.36838, -1.73059, -0.36865, -1.97058, -0.40186, -1.96704, -0.40205, -2.79395, -0.80939, -2.78674, -0.80967, -3.79041, -0.59891, -3.78589, -0.59897, -5.54492, -0.41072, -5.53662, -0.41093, -9.09387, -0.43521, -9.08728, -0.43555, 0, 0, 0, 0, -1.52209, 0.07739, -1.51917, 0.0772, -4.52185, -0.13429, -4.51685, -0.13454, -7.02649, -0.23659, -7.02014, -0.23656, -8.78601, -1.5184, -8.78101, -1.51867, -9.35388, -1.13512, -9.34692, -1.13548, -7.40381, -0.68921, -7.39807, -0.6896, -3.60168, -0.12944, -3.59021, -0.13042, -0.86755, 0.08887, -0.86292, 0.08819, -0.12, 0.01244, -0.11926, 0.01234], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 366, "vertices": [-2.36499, 0.24535, -2.36304, 0.24518, -6.56287, 0.08369, -6.56079, 0.08353, -10.24951, 0.39972, -10.24377, 0.39919, -11.13208, -0.43781, -11.1272, -0.43816, -8.90198, -0.07176, -8.89954, -0.07198, -5.14404, 0.3345, -5.1405, 0.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.7251, 0.52412, -3.7251, 0.52393, -7.4303, -0.74069, -7.42981, -0.74092, -9.11584, -0.42831, -9.11572, -0.42854, -7.91748, -0.14034, -7.91736, -0.14059, -5.43457, -0.05444, -5.43457, -0.05464, -2.84412, -0.25455, -2.84412, -0.25471], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": "stepped"}, {"time": 9.6667, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": [9.889, 0, 10.111, 1]}, {"time": 10.3333}]}}}}}, "touch": {"bones": {"body": {"rotate": [{"value": 2.36, "curve": [0.023, 2.36, 0.059, -7.69]}, {"time": 0.2333, "value": -7.73, "curve": [0.544, -7.8, 0.856, 2.36]}, {"time": 1.1667, "value": 2.36}]}, "body2": {"rotate": [{"value": 1.59, "curve": [0.023, 1.59, 0.059, -11.26]}, {"time": 0.2333, "value": -11.31, "curve": [0.544, -11.4, 0.856, 1.59]}, {"time": 1.1667, "value": 1.59}], "translate": [{"x": -3.92, "curve": [0.023, -3.92, 0.059, 8.28, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 8.32, "curve": [0.544, 8.41, 0.856, -3.92, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": -3.92}], "scale": [{"y": 1.018, "curve": [0.078, 1, 0.156, 1, 0.023, 1.018, 0.059, 0.963]}, {"time": 0.2333, "y": 0.963, "curve": [0.678, 1, 0.856, 1, 0.544, 0.962, 0.856, 1.018]}, {"time": 1.1667, "y": 1.018}]}, "body3": {"rotate": [{"value": -3, "curve": [0.027, -3, 0.067, -2.8]}, {"time": 0.2667, "value": -2.8, "curve": [0.567, -2.8, 0.867, -3]}, {"time": 1.1667, "value": -3}], "translate": [{"x": -4.16, "curve": [0.027, -4.16, 0.067, 9.87, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 9.93, "curve": [0.567, 10.02, 0.867, -4.16, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.16}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.027, 1.003, 0.067, 1.021, 0.027, 1.003, 0.067, 1.021]}, {"time": 0.2667, "x": 1.021, "y": 1.021, "curve": [0.567, 1.021, 0.867, 1.003, 0.567, 1.021, 0.867, 1.003]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.54, "curve": [0.03, 0.54, 0.076, 7.88]}, {"time": 0.3, "value": 7.92, "curve": [0.589, 7.96, 0.878, 0.54]}, {"time": 1.1667, "value": 0.54}]}, "head": {"rotate": [{"value": -0.51, "curve": [0.03, -0.51, 0.076, 7.87]}, {"time": 0.3, "value": 7.92, "curve": [0.589, 7.97, 0.878, -0.51]}, {"time": 1.1667, "value": -0.51}]}, "sh_L": {"translate": [{"x": -1.78, "curve": [0.027, -1.78, 0.067, 10.2, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.26, "curve": [0.567, 10.34, 0.867, -1.78, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -1.78}]}, "sh_R": {"translate": [{"x": -1.78, "curve": [0.027, -1.78, 0.067, 8.72, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 8.77, "curve": [0.567, 8.84, 0.867, -1.78, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -1.78}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "hand_L3": {"rotate": [{"value": 1.81, "curve": [0.027, 1.81, 0.067, -3.16]}, {"time": 0.2667, "value": -3.19, "curve": [0.567, -3.22, 0.867, 1.81]}, {"time": 1.1667, "value": 1.81}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "arm_R3": {"rotate": [{"value": 1.82, "curve": [0.033, 1.82, 0.084, 10.28]}, {"time": 0.3333, "value": 10.33, "curve": [0.611, 10.38, 0.889, 1.82]}, {"time": 1.1667, "value": 1.82}]}, "RU_L": {"translate": [{"x": -11.63, "curve": [0.074, -22.84, 0.161, -32.04, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -32.04, "curve": [0.428, -32.04, 0.605, 32.11, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 32.13, "curve": [0.922, 32.14, 1.046, 7.19, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -11.63}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.073, 1.012, 0.16, 0.926, 0.073, 0.992, 0.16, 1.04]}, {"time": 0.2333, "x": 0.926, "y": 1.04, "curve": [0.331, 0.926, 0.403, 1.079, 0.331, 1.04, 0.403, 0.955]}, {"time": 0.5, "x": 1.079, "y": 0.955, "curve": [0.597, 1.079, 0.703, 0.926, 0.597, 0.955, 0.703, 1.04]}, {"time": 0.8, "x": 0.926, "y": 1.04, "curve": [0.897, 0.926, 1.003, 1.079, 0.897, 1.04, 1.003, 0.955]}, {"time": 1.1, "x": 1.079, "y": 0.955, "curve": [1.125, 1.079, 1.143, 1.069, 1.125, 0.955, 1.143, 0.96]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_L2": {"translate": [{"x": -0.68, "curve": [0.099, -15.15, 0.203, -29.83, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -29.83, "curve": [0.495, -29.83, 0.672, 28.45, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 28.46, "curve": [0.965, 28.47, 1.071, 14, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": -0.67}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.097, 1.079, 0.203, 0.926, 0.097, 0.955, 0.203, 1.04]}, {"time": 0.3, "x": 0.926, "y": 1.04, "curve": [0.397, 0.926, 0.503, 1.079, 0.397, 1.04, 0.503, 0.955]}, {"time": 0.6, "x": 1.079, "y": 0.955, "curve": [0.697, 1.079, 0.769, 0.926, 0.697, 0.955, 0.769, 1.04]}, {"time": 0.8667, "x": 0.926, "y": 1.04, "curve": [0.964, 0.926, 1.069, 1.079, 0.964, 1.04, 1.069, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955}]}, "RU_L3": {"translate": [{"x": 10.34, "curve": [0.122, -4.98, 0.245, -25.36, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -25.36, "curve": [0.561, -25.36, 0.739, 26.97, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 26.98, "curve": [1.007, 26.99, 1.094, 19.6, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 10.35}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.024, 1.069, 0.042, 1.079, 0.024, 0.961, 0.042, 0.955]}, {"time": 0.0667, "x": 1.079, "y": 0.955, "curve": [0.164, 1.079, 0.269, 0.926, 0.164, 0.955, 0.269, 1.04]}, {"time": 0.3667, "x": 0.926, "y": 1.04, "curve": [0.464, 0.926, 0.569, 1.079, 0.464, 1.04, 0.569, 0.955]}, {"time": 0.6667, "x": 1.079, "y": 0.955, "curve": [0.764, 1.079, 0.836, 0.926, 0.764, 0.955, 0.836, 1.04]}, {"time": 0.9333, "x": 0.926, "y": 1.04, "curve": [1.007, 0.926, 1.094, 1.012, 1.007, 1.04, 1.094, 0.992]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_R": {"translate": [{"x": -7.06, "curve": [0.074, -17.1, 0.161, -25.34, 0.074, 0, 0.161, 0]}, {"time": 0.2333, "x": -25.34, "curve": [0.428, -25.34, 0.605, 32.12, 0.428, 0, 0.605, 0]}, {"time": 0.8, "x": 32.13, "curve": [0.922, 32.14, 1.046, 9.8, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -7.05}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.073, 1.012, 0.16, 0.926, 0.073, 0.992, 0.16, 1.04]}, {"time": 0.2333, "x": 0.926, "y": 1.04, "curve": [0.331, 0.926, 0.403, 1.079, 0.331, 1.04, 0.403, 0.955]}, {"time": 0.5, "x": 1.079, "y": 0.955, "curve": [0.597, 1.079, 0.703, 0.926, 0.597, 0.955, 0.703, 1.04]}, {"time": 0.8, "x": 0.926, "y": 1.04, "curve": [0.897, 0.926, 1.003, 1.079, 0.897, 1.04, 1.003, 0.955]}, {"time": 1.1, "x": 1.079, "y": 0.955, "curve": [1.125, 1.079, 1.143, 1.069, 1.125, 0.955, 1.143, 0.96]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "RU_R2": {"translate": [{"x": 3.05, "curve": [0.099, -9.58, 0.203, -22.38, 0.099, 0, 0.203, 0]}, {"time": 0.3, "x": -22.38, "curve": [0.495, -22.38, 0.672, 28.45, 0.495, 0, 0.672, 0]}, {"time": 0.8667, "x": 28.46, "curve": [0.965, 28.47, 1.071, 15.85, 0.965, 0, 1.071, 0]}, {"time": 1.1667, "x": 3.05}], "scale": [{"x": 1.079, "y": 0.955, "curve": [0.097, 1.079, 0.203, 0.926, 0.097, 0.955, 0.203, 1.04]}, {"time": 0.3, "x": 0.926, "y": 1.04, "curve": [0.397, 0.926, 0.503, 1.079, 0.397, 1.04, 0.503, 0.955]}, {"time": 0.6, "x": 1.079, "y": 0.955, "curve": [0.697, 1.079, 0.769, 0.926, 0.697, 0.955, 0.769, 1.04]}, {"time": 0.8667, "x": 0.926, "y": 1.04, "curve": [0.964, 0.926, 1.069, 1.079, 0.964, 1.04, 1.069, 0.955]}, {"time": 1.1667, "x": 1.079, "y": 0.955}]}, "RU_R3": {"translate": [{"x": 12.95, "curve": [0.122, 0.03, 0.245, -17.16, 0.122, 0, 0.245, 0]}, {"time": 0.3667, "x": -17.16, "curve": [0.561, -17.16, 0.739, 26.97, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 26.98, "curve": [1.007, 26.99, 1.094, 20.76, 1.007, 0, 1.094, 0]}, {"time": 1.1667, "x": 12.95}], "scale": [{"x": 1.055, "y": 0.968, "curve": [0.024, 1.069, 0.042, 1.079, 0.024, 0.961, 0.042, 0.955]}, {"time": 0.0667, "x": 1.079, "y": 0.955, "curve": [0.164, 1.079, 0.269, 0.926, 0.164, 0.955, 0.269, 1.04]}, {"time": 0.3667, "x": 0.926, "y": 1.04, "curve": [0.464, 0.926, 0.569, 1.079, 0.464, 1.04, 0.569, 0.955]}, {"time": 0.6667, "x": 1.079, "y": 0.955, "curve": [0.764, 1.079, 0.836, 0.926, 0.764, 0.955, 0.836, 1.04]}, {"time": 0.9333, "x": 0.926, "y": 1.04, "curve": [1.007, 0.926, 1.094, 1.012, 1.007, 1.04, 1.094, 0.992]}, {"time": 1.1667, "x": 1.055, "y": 0.968}]}, "leg_L2": {"rotate": [{"value": -0.7, "curve": [0.023, -0.7, 0.059, -4.66]}, {"time": 0.2333, "value": -4.66, "curve": [0.544, -4.64, 0.856, -0.7]}, {"time": 1.1667, "value": -0.7}]}, "leg_L3": {"rotate": [{"value": -1.43, "curve": [0.024, -1.43, 0.059, 3.47]}, {"time": 0.2333, "value": 3.47, "curve": [0.544, 3.47, 0.856, -1.43]}, {"time": 1.1667, "value": -1.43}]}, "leg_L4": {"rotate": [{"value": -1.19, "curve": [0.024, -1.19, 0.059, 1.68]}, {"time": 0.2333, "value": 1.68, "curve": [0.544, 1.68, 0.856, -1.19]}, {"time": 1.1667, "value": -1.19}]}, "leg_L5": {"rotate": [{"value": 0.43, "curve": [0.024, 0.43, 0.059, 8.8]}, {"time": 0.2333, "value": 8.8, "curve": [0.544, 8.8, 0.856, 0.43]}, {"time": 1.1667, "value": 0.43}]}, "leg_R2": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "wine2": {"translate": [{"x": 0.11, "y": 0.06}], "scale": [{"x": 1.005, "curve": [0.033, 1.005, 0.084, 0.957, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.956, "curve": [0.611, 0.956, 0.889, 1.005, 0.611, 1, 0.889, 1]}, {"time": 1.1667, "x": 1.005}]}, "wine3": {"rotate": [{"value": -69.09}, {"time": 1.1333, "value": -425.93, "curve": "stepped"}, {"time": 1.1667, "value": -69.09}]}, "head4": {"translate": [{"curve": [0.024, 0, 0.058, 1.43, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.43, "curve": [0.544, 1.43, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "head5": {"rotate": [{"curve": [0.024, 0, 0.058, -5.84]}, {"time": 0.2333, "value": -5.84, "curve": [0.544, -5.84, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.986, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.986, "curve": [0.544, 0.986, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "head6": {"rotate": [{"curve": [0.024, 0, 0.058, -14.96]}, {"time": 0.2333, "value": -14.96, "curve": [0.544, -14.96, 0.856, 0]}, {"time": 1.1667}]}, "head7": {"translate": [{"curve": [0.024, 0, 0.058, 0.09, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 0.09, "curve": [0.544, 0.09, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "head8": {"rotate": [{"curve": [0.024, 0, 0.058, 19.17]}, {"time": 0.2333, "value": 19.17, "curve": [0.544, 19.17, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.058, 0.986, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.986, "curve": [0.544, 0.986, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "head9": {"rotate": [{"curve": [0.024, 0, 0.058, 19.21]}, {"time": 0.2333, "value": 19.21, "curve": [0.544, 19.21, 0.856, 0]}, {"time": 1.1667}]}, "hair_F": {"rotate": [{"value": 2.67, "curve": [0.123, 0.09, 0.244, -3.34]}, {"time": 0.3667, "value": -3.34, "curve": [0.563, -3.34, 0.77, 5.47]}, {"time": 0.9667, "value": 5.47, "curve": [1.041, 5.48, 1.094, 4.23]}, {"time": 1.1667, "value": 2.67}]}, "hair_B": {"rotate": [{"value": 4.07, "curve": [0.149, 1.58, 0.287, -3.34]}, {"time": 0.4333, "value": -3.34, "curve": [0.63, -3.34, 0.837, 5.47]}, {"time": 1.0333, "value": 5.47, "curve": [1.083, 5.48, 1.119, 4.91]}, {"time": 1.1667, "value": 4.07}]}, "headround3": {"translate": [{"x": 28.66, "curve": [0.033, 28.66, 0.084, -327.46, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -329.45, "curve": [0.611, -331.67, 0.889, 28.66, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 28.66}]}, "headround": {"translate": [{"y": -144.57, "curve": [0.111, 0, 0.222, 0, 0.033, -144.57, 0.084, -419.71]}, {"time": 0.3333, "y": -421.24, "curve": [0.611, 0, 0.889, 0, 0.611, -422.96, 0.889, -144.57]}, {"time": 1.1667, "y": -144.57}]}, "bodyround": {"translate": [{"y": 81.84, "curve": [0.078, 0, 0.156, 0, 0.023, 81.84, 0.059, -348.49]}, {"time": 0.2333, "y": -350.17, "curve": [0.544, 0, 0.856, 0, 0.544, -353.17, 0.856, 81.84]}, {"time": 1.1667, "y": 81.84}]}, "sh_L2": {"rotate": [{"value": -0.22}]}, "sh_L3": {"rotate": [{"value": 0.65}]}, "sh_R2": {"rotate": [{"value": 0.33}]}, "sh_R3": {"rotate": [{}]}, "arm_R4": {"translate": [{"x": -3, "y": 8.15, "curve": [0.03, -3, 0.076, -0.67, 0.03, 8.15, 0.076, -47.28]}, {"time": 0.3, "x": -0.65, "y": -47.56, "curve": [0.589, -0.64, 0.878, -3, 0.589, -47.92, 0.878, 8.15]}, {"time": 1.1667, "x": -3, "y": 8.15}]}, "leg_R4": {"rotate": [{"value": 0.18}]}, "leg_R5": {"rotate": [{"value": 0.01}]}, "leg_R6": {"rotate": [{"value": 5.03, "curve": [0.023, 5.03, 0.059, 13.99]}, {"time": 0.2333, "value": 13.97, "curve": [0.544, 13.93, 0.856, 5.03]}, {"time": 1.1667, "value": 5.03}], "translate": [{"x": 3.09, "y": 10.37, "curve": [0.023, 3.09, 0.059, 24.74, 0.023, 10.37, 0.059, 53.69]}, {"time": 0.2333, "x": 24.7, "y": 53.6, "curve": [0.544, 24.61, 0.856, 3.09, 0.544, 53.43, 0.856, 10.37]}, {"time": 1.1667, "x": 3.09, "y": 10.37}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-0.75146, -0.08116, -0.75057, -0.08125, -0.75146, -0.08116, -0.75057, -0.08125, -0.60782, -0.06071, -0.60708, -0.06078, -0.46053, -0.00527, -0.45995, -0.00533, -0.2487, -0.00956, -0.24833, -0.0096, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.52492, 0.05446, -0.52452, 0.05442, -1.74319, 0.07478, -1.74189, 0.07465, -2.56961, -0.05163, -2.56808, -0.05174, -2.36624, 0.03334, -2.36482, 0.03326, -1.47224, -0.04175, -1.47118, -0.04178, -0.25513, 0.1143, -0.25431, 0.11424, -0.26275, 0.02722, -0.26198, 0.02719, -0.60782, -0.06071, -0.60708, -0.06078, 0, 0, 0, 0, -0.98885, 0.01421, -0.98832, 0.01416, -1.94255, 0.02475, -1.94173, 0.02472, -2.55827, 0.05331, -2.55764, 0.05328, -2.43205, 0.07558, -2.43155, 0.07555, -1.61855, 0.07954, -1.61786, 0.07949, -0.53051, -0.01542, -0.52982, -0.01549, -0.14283, -0.02028, -0.14267, -0.02029], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-1.87869, -0.1673, -1.87608, -0.16734, -1.34602, -0.05435, -1.34488, -0.05442, -0.53794, -0.06824, -0.53702, -0.06828, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33056, 0.00386, -0.32948, 0.00375, -0.3752, -0.07957, -0.37381, -0.07963, -0.42564, -0.0868, -0.42488, -0.08684, -0.60349, -0.17483, -0.60194, -0.17489, -0.81873, -0.12936, -0.81775, -0.12938, -1.1977, -0.08872, -1.19591, -0.08876, -1.96427, -0.09401, -1.96285, -0.09408, 0, 0, 0, 0, -0.32877, 0.01672, -0.32814, 0.01668, -0.97672, -0.02901, -0.97564, -0.02906, -1.51772, -0.0511, -1.51635, -0.0511, -1.89778, -0.32797, -1.89669, -0.32803, -2.02044, -0.24518, -2.01893, -0.24526, -1.59922, -0.14887, -1.59798, -0.14895, -0.77796, -0.02796, -0.77548, -0.02817, -0.18739, 0.0192, -0.18639, 0.01905, -0.02592, 0.00269, -0.02576], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 366, "vertices": [-0.51084, 0.05299, -0.51042, 0.05296, -1.41758, 0.01808, -1.41713, 0.01804, -2.21389, 0.08634, -2.21265, 0.08623, -2.40453, -0.09457, -2.40347, -0.09464, -1.92282, -0.0155, -1.9223, -0.01555, -1.11111, 0.07225, -1.11035, 0.07221, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80462, 0.11321, -0.80462, 0.11317, -1.60494, -0.15999, -1.60484, -0.16004, -1.96902, -0.09252, -1.96899, -0.09257, -1.71017, -0.03031, -1.71015, -0.03037, -1.17387, -0.01176, -1.17387, -0.0118, -0.61433, -0.05498, -0.61433, -0.05502], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.022, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 64, "vertices": [1.29358, 0.19482, 1.29358, 0.19479, 0.39478, -0.30219, 0.39478, -0.30222, -2.09753, -0.62959, -2.09753, -0.62956, -2.76453, -0.56883, -2.76453, -0.56873, -2.92334, -0.60289, -2.92334, -0.60286, -2.55933, -0.59164, -2.55933, -0.59158, -2.72437, -0.67699, -2.72437, -0.67691, -2.16711, -0.3242, -2.16711, -0.32413, -1.14502, 0.04285, -1.14502, 0.04279, -0.18005, 0.62434, -0.1803, 0.62428, 0.526, -0.1436, 0.52612, -0.14363, 1.29358, 0.19482, 1.29358, 0.19479, 2.77734, 0.44125, 2.77759, 0.44124, 1.82495, -0.03416, 1.82532, -0.03418, 1.97253, 0.20386, 1.97278, 0.20383, 1.82495, -0.03416, 1.82532, -0.03418, 1.33801, 0.1051, 1.33826, 0.10509, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 0.74402, -0.16789, 0.74414, -0.16791, 1.29358, 0.19482, 1.29358, 0.19479, 1.29358, 0.19482, 1.29358, 0.19479, -1.40063, -0.34369, -1.40063, -0.34363, -2.8855, -0.55534, -2.88599, -0.55528, -2.47559, -0.60095, -2.47559, -0.60091, -2.63489, -0.63506, -2.63489, -0.63501, -1.66821, 0.04918, -1.66821, 0.04916, 0.42798, 0.03703, 0.42798, 0.03703, 1.20032, 0.12041, 1.20032, 0.12038, 0.74402, -0.16789, 0.74414, -0.16791, 1.35791, 0.0179, 1.35828, 0.01788, 1.97253, 0.20386, 1.97278, 0.20383, 1.35791, 0.0179, 1.35828, 0.01788, 1.35791, 0.0179, 1.35828, 0.01788, 2.83716, 0.18626, 2.8374, 0.18625, 1.35791, 0.0179, 1.35828, 0.01788, 2.36047, 0.14445, 2.35974, 0.1445, 2.36047, 0.14445, 2.35974, 0.1445, 0, 0, 0, 0, 0.63318, 0.23311, 0.63293, 0.23311, 0.73291, -0.23354, 0.73291, -0.23354], "curve": [0.544, 0.01, 0.856, 1]}, {"time": 1.1667}]}}}}}}}