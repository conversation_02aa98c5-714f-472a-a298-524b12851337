﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    //管理3d city的lod实现,脚本挂接到一个建筑上,建筑大小和格子无关
    public class BuildingLODControl3D : KeepScaleBySize, IBuilding
    {
        public void Init(BuildingLODManager lodManager,  float fMaxObjectScale = 5.0f, GameObject rendererRoot = null)
        {
            if (mInited)
            {
                return;
            }

            if (rendererRoot == null)
            {
                rendererRoot = this.gameObject;
            }

            mInited = true;
            mLODManager = lodManager;
            mIsVisible = true;
            mUseDeltaScale = true;
            
            mCurrentColliderRadius = colliderRadius;

            if (!System.Object.ReferenceEquals(mLODManager, null))
            {
                if (mRenderers.Count == 0)
                {
                    rendererRoot.GetComponentsInChildren<Renderer>(mRenderers);
                    for (int i = 0; i < mRenderers.Count; ++i)
                    {
                        mRenderers[i].enabled = true;
                    }
                }

                if (System.Object.ReferenceEquals(mCollider, null))
                {
                    mCollider = gameObject.GetComponentInChildren<Collider>();
                }

                UpdatePosition();

                mLODManager.AddCity(this);

                InitMaxDelta(fMaxObjectScale);

                //在物体显示时根据相机的高度设置它初始的缩放值
                SetInitScale();

                UpdateCollider();
                
                //计算物体最大的碰撞半径
                mMaxColliderRadius = GetScaleAtHeight(GetScaleConfig().maximumCameraHeight, true);
                mMaxScale = mMaxColliderRadius;
                mMinScale = GetScaleAtHeight(GetScaleConfig().minimumCameraHeight, true);
            }
        }

        /// <summary>
        /// 初始化缩放比例
        /// </summary>
        /// <param name="fMaxObjectScale"></param>
        private void InitMaxDelta(float fMaxObjectScale)
        {

            var config = GetScaleConfig();
            if (config == null)
                return;

            var map = Map.currentMap;
            mInitViewportWidth = Utils.CalculateViewportWidth(config.cameraFovWhenScaleIsOne, map);

            //初始化放缩指数
            float oldWidth = Utils.CalculateViewportWidth(config.cameraFovWhenScaleIsOne, map);
            float fov = Map.currentMap.GetCameraFOVAtHeight(config.maximumCameraHeight);
            float newWidth = Utils.CalculateViewportWidth(fov, map);
            float ratio = newWidth / oldWidth;

            float scaleFactor = 1.0f / config.cameraHeightWhenScaleIsBaseScale;
            float t = (config.maximumCameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp(t, 0, 1.0f);

            float deltaScale = fMaxObjectScale / (scaleFactor * config.maximumCameraHeight * ratio);

            maxDeltaScale = (deltaScale - 1) / t;
        }

        public void FilterRenderer(Renderer renderer)
        {
            mRenderers.Remove(renderer);
        }

        public void Uninit()
        {
            Debug.Assert(mInited);
            mInited = false;
            if (mLODManager != null)
            {
                mLODManager.RemoveCity(this);
                mLODManager = null;
            }

            mRenderers.Clear();
            mIsVisible = true;
            mCollider = null;

            mLastCameraHeight = 0;

            mMoveToTarget = null;
            ClearPotentialCollidableList();
            clearVisiableChangedEvent();
        }

        //根据相机的高度来更新建筑的缩放或位置
        public void UpdateCity()
        {
            UpdateScaleImpl();
            UpdatePosition();

            var config = GetScaleConfig();
            //update collider radius
            float cameraHeight = Map.currentMap.camera.transform.position.y;
            float t = (cameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp01(t);
            mCurrentColliderRadius = colliderRadius + t * increasedColliderRadius;

            UpdateCollider();
        }

        void UpdateCollider()
        {
            var t = transform;
            float localScale = t.lossyScale.x;
            mColliderCenter = t.position + colliderOffset * localScale;
            if (mCurrentColliderRadius == 0)
            {
                mScaledColliderRadius = colliderRadius;
            }
            else
            {
                mScaledColliderRadius = mCurrentColliderRadius * localScale / mPrefabInitScale;
            }
        }

        //根据相机的高度更新建筑的位置
        void UpdatePosition()
        {
            if (mMoveToTarget != null)
            {
                mMoveToTarget.Update();
            }
        }

        //设置主城的移动信息
        //startPos:在相机高度为cameraHeightRange.x时主城的位置
        //endPos:在相机高度为cameraHeightRange.y时主城的位置
        //cameraHeightRange:主城移动期间相机的高度范围
        public void SetMoveInfo(Vector3 startPos, Vector3 endPos, Vector2 cameraHeightRange, Transform cityRootTransform)
        {
            mMoveToTarget = new LODMoveToTarget(transform, cityRootTransform);
            mMoveToTarget.SetMoveInfo(startPos, endPos, cameraHeightRange);
            mMoveEndPosition = mMoveToTarget.worldEndPosition;
        }

        public void SetMoveInfo(LODMoveToTarget pMoveToTarget)
        {
            mMoveToTarget = pMoveToTarget;
            mMoveToTarget.SetTransform(transform);
            mMoveEndPosition = mMoveToTarget.worldEndPosition;
        }

        public void SetCityStartPosition(Vector3 startPos)
        {
            if (mMoveToTarget != null)
            {
                mMoveToTarget.SetStartPosition(startPos);
            }
        }

        //显示或隐藏建筑
        public void SetVisible(bool visible)
        {
            if (mIsVisible != visible)
            {
                mIsVisible = visible;
                for (int i = 0; i < mRenderers.Count; ++i)
                {
                    if (mRenderers[i] != null)
                    {
                        mRenderers[i].enabled = visible;
                    }
                }
                visiableChangedEvent?.Invoke(visible);
            }
        }

        void SetLODManager(BuildingLODManager lodManager)
        {
            mLODManager = lodManager;
        }

        public bool isMainBuilding { get { return mMoveToTarget != null; } }
        public bool isAtFinalPosition
        {
            get
            {
                return transform.position == mMoveEndPosition;
            }
        }
        public Vector3 colliderCenter { get { return mColliderCenter; } }

        void OnDrawGizmos()
        {
            if (mIsVisible)
            {
                Gizmos.DrawWireSphere(colliderCenter, scaledColliderRadius);

                var collider = gameObject.GetComponentInChildren<Collider>();
                if (collider != null)
                {
                    Gizmos.color = Color.red;
                    var boxCollider = collider as BoxCollider;
                    if (boxCollider != null)
                    {
                        Gizmos.matrix = collider.transform.localToWorldMatrix;
                        Gizmos.DrawWireCube(boxCollider.center, boxCollider.size);
                    }
                }
            }
        }

        public void ClearPotentialCollidableList()
        {
            mPotentialCollidableBuildings.Clear();
        }

        public void AddPotentialCollidableBuilding(IBuildingElement building)
        {
#if DEBUG
            Debug.Assert(mPotentialCollidableBuildings.Contains(building) == false);
#endif
            mPotentialCollidableBuildings.Add(building);
        }

        public List<IBuildingElement> GetPotentialCollidableBuildingList()
        {
            return mPotentialCollidableBuildings;
        }

        public int GetPriority()
        {
            return hidePriority;
        }
        //建筑物未缩放时的半径,如果使用半径来做建筑的重叠判定时使用
        public float colliderRadius;
        //增加的collider radius,因为当主城大厅填满整个主城时有可能大厅的collider碰不到城墙
        public float increasedColliderRadius = 0;
        //碰撞框的中心点偏移
        public Vector3 colliderOffset;
        //随建筑物缩放后的半径
        public float scaledColliderRadius
        {
            get
            {
                if (mScaledColliderRadius == 0)
                {
                    return mCurrentColliderRadius * transform.lossyScale.x / mPrefabInitScale;
                }
                return mScaledColliderRadius;
            }
        }

        /// <summary>
        /// 清除可视化变化事件
        /// </summary>
        public void clearVisiableChangedEvent()
        {
            if (visiableChangedEvent == null) return;
            Delegate[] dels = visiableChangedEvent.GetInvocationList();
            foreach (Delegate del in dels)
            {
                object delObj = del.GetType().GetProperty("Method").GetValue(del, null);
                string funcName = (string)delObj.GetType().GetProperty("Name").GetValue(delObj, null);////方法名
                Console.WriteLine(funcName);
                visiableChangedEvent -= del as OnVisibleChanged;
            }
        }

        public void AddVisiableChangedEvent(OnVisibleChanged pvisiableChangedEvent)
        {
            visiableChangedEvent -= pvisiableChangedEvent;
            visiableChangedEvent += pvisiableChangedEvent;
        }

        public void AddCollision(IBuildingElement building)
        {
        }

        public List<IBuildingElement> GetCollisionList()
        {
            return null;
        }

        float GetScaleAtHeight(float cameraHeight, bool calculate)
        {
            var config = GetScaleConfig();
            if (cameraHeight >= config.maximumCameraHeight && mMaxScale != 0)
            {
                return mMaxScale;
            }
            else if (cameraHeight <= config.minimumCameraHeight && mMinScale != 0)
            {
                return mMinScale;
            }

            float newWidth;
            if (calculate)
            {
                float fov = Map.currentMap.GetCameraFOVAtHeight(cameraHeight);
                newWidth = Utils.CalculateViewportWidth(fov, Map.currentMap);
            }
            else
            {
                newWidth = BuildingLODManagerUpdateDataCache.viewportWidthAtCurrentCameraHeight;
            }

            float ratio = newWidth / mInitViewportWidth;

            float scaleFactor = mPrefabInitScale / config.cameraHeightWhenScaleIsBaseScale;
            float t = (cameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp(t, 0, 1.0f);

            float deltaScale = 1 + maxDeltaScale * t;
            
            return scaleFactor * cameraHeight * ratio * deltaScale;
        }

        public Vector3 GetObjectMinScale()
        {
            return mMinScale * Vector3.one;
        }

        //建筑物是否可见
        public bool isVisible { get { return mIsVisible; } }
        //使用collider来判定建筑物重叠时使用
        public Collider objectCollider { get { return mCollider; } }
        public BuildingLODManager lodManager { get { return mLODManager; } }
        public float maxColliderRadius { get { return mMaxColliderRadius; } }
        public int id { get { return gameObject.GetInstanceID(); } }

        public LODMoveToTarget MoveToTarget { get { return mMoveToTarget; } }

        //在建筑物重叠时hidePriority小的隐藏hidePriority大的建筑
        public int hidePriority;

        //在建筑物显示、隐藏时回调
        public delegate void OnVisibleChanged(bool visible);

        //建筑显示、隐藏事件
        public event OnVisibleChanged visiableChangedEvent;

        BuildingLODManager mLODManager;
        //建筑物子节点中所有的renderer
        List<Renderer> mRenderers = new List<Renderer>();
        Collider mCollider;
        float mCurrentColliderRadius;
        bool mIsVisible = true;
        bool mInited = false;
        Vector3 mMoveEndPosition;

        Vector3 mColliderCenter;
        float mScaledColliderRadius;
        //最大可能的碰撞半径,可以利用这个半径进行碰撞检测优化
        float mMaxColliderRadius;

        //用于优化
        float mMaxScale;
        float mMinScale;
        float mInitViewportWidth;

        static float mMinScaleFactor;
        static float mMaxScaleFactor;

        //潜在的可能碰撞的物体
        List<IBuildingElement> mPotentialCollidableBuildings = new List<IBuildingElement>();

        //将建筑物在相机缩放时移动到目标点
        LODMoveToTarget mMoveToTarget;
    }
}