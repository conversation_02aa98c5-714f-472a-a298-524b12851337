﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map {
    //地图内部的game message
    //public class GMMapObjectActiveStateChange : GameMessageTemplate<GMMapObjectActiveStateChange> {
    //    public IMapObjectData objectData;
    //    public bool active;
    //    public int layerID;
    //}

    public class GMMapObjectTransformChange : GameMessageTemplate<GMMapObjectTransformChange> {
        public void Set(IMapObjectData data, int layerID) {
            this.data = data;
            this.layerID = layerID;
        }
        public IMapObjectData data;
        public int layerID;
    }

    //public class GMMapObjectScaleChange : GameMessageTemplate<GMMapObjectScaleChange>
    //{
    //    public IMapObjectData objectData;
    //    public int layerID;
    //}
}
