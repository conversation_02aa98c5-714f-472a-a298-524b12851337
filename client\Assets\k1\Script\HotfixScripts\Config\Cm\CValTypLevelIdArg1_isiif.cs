using System;
using System.Collections.Generic;
using Cfg.H;

namespace Cfg.Cm
{

	public partial class CValTypLevelIdArg1_isiif : ICC
	{
		public void Initialize(Dictionary<string, object> o)
		{
		    this.Typ = CParse.ReadString(o,"typ");
		    this.Id = CParse.ReadInteger(o,"id");
		    this.Val = CParse.ReadInteger(o,"val");
		    this.Arg1 = CParse.ReadFloat(o,"arg1");
		    this.Level = CParse.ReadInteger(o,"level");
		}

		public string Typ { get; private set; }
		public int Id { get; private set; }
		public int Val { get; private set; }
		public float Arg1 { get; private set; }
		public int Level { get; private set; }
	}

}
