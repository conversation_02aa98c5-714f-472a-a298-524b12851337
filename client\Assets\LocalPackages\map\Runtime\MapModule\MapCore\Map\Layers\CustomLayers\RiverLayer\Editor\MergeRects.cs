﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    public static class MergeRects
    {
        public class ConnectedTiles
        {
            public ConnectedTiles(Rect2D bounds, int[,] tiles)
            {
                this.bounds = bounds;
                this.tiles = tiles;
            }

            public Rect2D bounds;
            public int[,] tiles;
            public List<Rect2D> rectangles = new List<Rect2D>();
        }

        //把同类的小tile合并成矩形
        public static List<ConnectedTiles> ProcessTiles(int horizontaTileCount, int verticalTileCount, float tileSize,
            Func<int, int, bool> isValidTileCallback, Func<int, int, Vector3> fromCoordinateToWorldPositionCallback, Func<int, int, Vector3> fromCoordinateToWorldPositionCenterCallback)
        {
            mHorizontalTileCount = horizontaTileCount;
            mVerticalTileCount = verticalTileCount;
            mTileSize = tileSize;
            mIsValidTileCallback = isValidTileCallback;
            mFromCoordinateToWorldPositionCallback = fromCoordinateToWorldPositionCallback;
            mFromCoordinateToWorldPositionCenterCallback = fromCoordinateToWorldPositionCenterCallback;

            //找到地图上所有联通的区域
            List<ConnectedTiles> connectedTiles = FindConnectedTiles();
            //把联通的区域分割成矩形
            SplitConnectedTiles(connectedTiles);

            //CreateConnectedTilesDebugView(connectedTiles);

            return connectedTiles;
        }

        static List<ConnectedTiles> FindConnectedTiles()
        {
            List<ConnectedTiles> allConnectedTiles = new List<ConnectedTiles>();

            bool[,] visited = new bool[mVerticalTileCount, mHorizontalTileCount];
            for (int i = 0; i < mVerticalTileCount; ++i)
            {
                for (int j = 0; j < mHorizontalTileCount; ++j)
                {
                    if (visited[i, j] == false)
                    {
                        var connectedTiles = FindConnectedTilesFor(j, i, visited);
                        if (connectedTiles != null)
                        {
                            allConnectedTiles.Add(connectedTiles);
                        }
                    }
                }
            }

            return allConnectedTiles;
        }

        static ConnectedTiles FindConnectedTilesFor(int x, int y, bool[,] visited)
        {
            if (!mIsValidTileCallback(x, y))
            {
                return null;
            }

            List<Vector2Int> connectedTileCoordinates = new List<Vector2Int>();

            Vector2Int[] neighbourOffset = new Vector2Int[4]
            {
                new Vector2Int(-1, 0),
                new Vector2Int(0, 1),
                new Vector2Int(1, 0),
                new Vector2Int(0, -1),
            };
            
            List<Vector2Int> stack = new List<Vector2Int>();
            stack.Add(new Vector2Int(x, y));
            while (stack.Count > 0)
            {
                Vector2Int coord = stack[stack.Count - 1];
                stack.RemoveAt(stack.Count - 1);
                visited[coord.y, coord.x] = true;

                connectedTileCoordinates.Add(coord);

                for (int i = 0; i < neighbourOffset.Length; ++i)
                {
                    int nx = neighbourOffset[i].x + coord.x;
                    int ny = neighbourOffset[i].y + coord.y;
                    if (nx >= 0 && nx < mHorizontalTileCount && ny >= 0 && ny < mVerticalTileCount &&
                        visited[ny, nx] == false)
                    {
                        if (mIsValidTileCallback(nx, ny))
                        {
                            stack.Add(new Vector2Int(nx, ny));
                        }
                    }
                }
            }

            if (connectedTileCoordinates.Count > 1)
            {
                Rect2D bounds = CalculateBounds(connectedTileCoordinates);
                int[,] tiles = new int[bounds.height, bounds.width];
                int minX = bounds.minX;
                int minY = bounds.minY;
                for (int k = 0; k < connectedTileCoordinates.Count; ++k)
                {
                    var c = connectedTileCoordinates[k];
                    tiles[c.y - minY, c.x - minX] = 1;
                }
                ConnectedTiles connectedTiles = new ConnectedTiles(bounds, tiles);
                return connectedTiles;
            }
            return null;
        }

        static Rect2D CalculateBounds(List<Vector2Int> coordinates)
        {
            int minX = int.MaxValue;
            int minY = int.MaxValue;
            int maxX = int.MinValue;
            int maxY = int.MinValue;
            for (int i = 0; i < coordinates.Count; ++i)
            {
                if (coordinates[i].x < minX)
                {
                    minX = coordinates[i].x;
                }
                if (coordinates[i].y < minY)
                {
                    minY = coordinates[i].y;
                }
                if (coordinates[i].x > maxX)
                {
                    maxX = coordinates[i].x;
                }
                if (coordinates[i].y > maxY)
                {
                    maxY = coordinates[i].y;
                }
            }
            Rect2D r = new Rect2D(minX, minY, maxX, maxY);
            return r;
        }

        static void SplitConnectedTiles(List<ConnectedTiles> allConnectedTiles)
        {
            for (int i = 0; i < allConnectedTiles.Count; ++i)
            {
                var connectedTiles = allConnectedTiles[i];
                int hori = connectedTiles.tiles.GetLength(1);
                int vert = connectedTiles.tiles.GetLength(0);
                bool[,] visited = new bool[vert, hori];
                for (int y = 0; y < vert; ++y)
                {
                    for (int x = 0; x < hori; ++x)
                    {
                        if (visited[y, x] == false && connectedTiles.tiles[y, x] != 0)
                        {
                            int maxValidX = FindMaxValidX(x, y, visited, connectedTiles.tiles);
                            int maxValidY = FindMaxValidY(x, y, maxValidX, visited, connectedTiles.tiles);
                            AddRectangle(x, y, maxValidX, maxValidY, visited, connectedTiles);
                        }
                    }
                }
            }
        }

        //扫描右边,找到该行最大的有效x坐标
        static int FindMaxValidX(int startX, int startY, bool[,] visited, int[,] tiles)
        {
            int h = tiles.GetLength(1);
            int maxValidX = startX;
            for (int k = startX; k < h; ++k)
            {
                if (visited[startY, k] == false && tiles[startY, k] != 0)
                {
                    maxValidX = k;
                }
                else
                {
                    break;
                }
            }
            return maxValidX;
        }

        //从下网上扫描,找到最大的有效y坐标
        static int FindMaxValidY(int startX, int startY, int maxValidX, bool[,] visited, int[,] tiles)
        {
            int v = tiles.GetLength(0);
            for (int y = startY; y < v; ++y)
            {
                for (int x = startX; x <= maxValidX; ++x)
                {
                    if (visited[y, x] == true || tiles[y, x] == 0)
                    {
                        return y - 1;
                    }
                }
            }

            return v - 1;
        }

        static void AddRectangle(int minX, int minY, int maxX, int maxY, bool[,] visited, ConnectedTiles connectedTiles)
        {
            int width = maxX - minX + 1;
            int height = maxY - minY + 1;

            for (int y = minY; y <= maxY; ++y)
            {
                for (int x = minX; x <= maxX; ++x)
                {
                    visited[y, x] = true;
                }
            }
            Rect2D r = new Rect2D(minX, minY, maxX, maxY);
            connectedTiles.rectangles.Add(r);
        }

        //生成合并tile后的prefab,只用于调试
        //void GenerateRectangleTilePrefabs(List<ConnectedTiles> allConnectedTiles)
        //{
        //    if (string.IsNullOrEmpty(SLGMakerEditor.instance.exportFolder))
        //    {
        //        return;
        //    }

        //    for (int i = 0; i < allConnectedTiles.Count; ++i)
        //    {
        //        var connectedTiles = allConnectedTiles[i];
        //        for (int r = 0; r < connectedTiles.rectangles.Count; ++r)
        //        {
        //            GenerateOneRectanglePrefab(connectedTiles.rectangles[r], connectedTiles);
        //        }
        //    }
        //}

        //void GenerateOneRectanglePrefab(Rect2D rectangle, ConnectedTiles connectedTiles)
        //{
        //    //create mesh
        //    float width = rectangle.width;
        //    float height = rectangle.height;
        //    Mesh mesh = new Mesh();
        //    Vector2[] uv0 = new Vector2[4]
        //    {
        //        new Vector2(0, 0),
        //        new Vector2(0, height),
        //        new Vector2(width, height),
        //        new Vector2(width, 0),
        //    };

        //    Vector2[] uvInAtlas;
        //    var modelTemplate = Map.currentMap.FindObject(connectedTiles.tiles[rectangle.minY, rectangle.minX]) as ModelTemplate;
        //    var prefabPath = modelTemplate.GetLODPrefabPath(0);
        //    mAtlasUVMapping.TryGetValue(prefabPath, out uvInAtlas);

        //    float minU = Mathf.Min(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
        //    float minV = Mathf.Min(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);
        //    float maxU = Mathf.Max(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
        //    float maxV = Mathf.Max(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);

        //    Vector4[] uv2 = new Vector4[4]
        //    {
        //        new Vector4(minU, minV, maxU, maxV),
        //        new Vector4(minU, minV, maxU, maxV),
        //        new Vector4(minU, minV, maxU, maxV),
        //        new Vector4(minU, minV, maxU, maxV),
        //    };

        //    Vector3 minPos = mLayerData.FromCoordinateToWorldPosition(rectangle.minX + connectedTiles.bounds.minX, rectangle.minY + connectedTiles.bounds.minY);
        //    Vector3 maxPos = mLayerData.FromCoordinateToWorldPosition(rectangle.maxX + connectedTiles.bounds.minX + 1, rectangle.maxY + connectedTiles.bounds.minY + 1);
        //    //temp code
        //    mesh.vertices = new Vector3[]
        //    {
        //        minPos,
        //        new Vector3(minPos.x, 0, maxPos.z),
        //        maxPos,
        //        new Vector3(maxPos.x, 0, minPos.z),
        //    };
        //    mesh.uv = uv0;
        //    mesh.SetUVs(1, uv2);
        //    mesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
        //    mesh.RecalculateBounds();

        //    GameObject tilePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);

        //    GameObject prefab = new GameObject($"combined prefab");
        //    var renderer = prefab.AddComponent<MeshRenderer>();
        //    renderer.sharedMaterial = tilePrefab.GetComponent<MeshRenderer>().sharedMaterial;
        //    var filter = prefab.AddComponent<MeshFilter>();
        //    filter.sharedMesh = mesh;
        //}

        ////debug code
        static void CreateConnectedTilesDebugView(List<ConnectedTiles> allConnectedTiles)
        {
            for (int i = 0; i < allConnectedTiles.Count; ++i)
            {
                GameObject root = new GameObject($"connect tiles {i}");
                var connectedTiles = allConnectedTiles[i];
                int horiz = connectedTiles.tiles.GetLength(1);
                int vert = connectedTiles.tiles.GetLength(0);
                for (int y = 0; y < vert; ++y)
                {
                    for (int x = 0; x < horiz; ++x)
                    {
                        if (connectedTiles.tiles[y, x] != 0)
                        {
                            int gx = connectedTiles.bounds.minX + x;
                            int gy = connectedTiles.bounds.minY + y;
                            
                            var tileObj = GameObject.CreatePrimitive(PrimitiveType.Plane);
                            tileObj.transform.position = mFromCoordinateToWorldPositionCenterCallback(gx, gy);
                            tileObj.transform.localScale = Vector3.one * mTileSize * 0.1f;
                            tileObj.transform.SetParent(root.transform, true);
                        }
                    }
                }

                //rectangles
                for (int k = 0; k < connectedTiles.rectangles.Count; ++k)
                {
                    GameObject rectangleRoot = new GameObject($"rectangle {i}");
                    rectangleRoot.transform.SetParent(root.transform);
                    var rect = connectedTiles.rectangles[k];
                    var drawBounds = rectangleRoot.AddComponent<DrawBounds>();
                    int gMinX = rect.minX + connectedTiles.bounds.minX;
                    int gMinY = rect.minY + connectedTiles.bounds.minY;
                    int gMaxX = rect.maxX + connectedTiles.bounds.minX;
                    int gMaxY = rect.maxY + connectedTiles.bounds.minY;
                    Vector3 minPos = mFromCoordinateToWorldPositionCallback(gMinX, gMinY);
                    Vector3 maxPos = mFromCoordinateToWorldPositionCallback(gMaxX + 1, gMaxY + 1);
                    var bounds = new Bounds();
                    bounds.SetMinMax(minPos, maxPos);
                    drawBounds.bounds = bounds;
                }
            }
        }

        static int mHorizontalTileCount;
        static int mVerticalTileCount;
        static float mTileSize;
        static Func<int, int, Vector3> mFromCoordinateToWorldPositionCallback;
        static Func<int, int, Vector3> mFromCoordinateToWorldPositionCenterCallback;
        static Func<int, int, bool> mIsValidTileCallback;
    }
}

#endif