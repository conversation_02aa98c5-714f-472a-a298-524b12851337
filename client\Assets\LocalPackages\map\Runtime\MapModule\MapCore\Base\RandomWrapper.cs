﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class RandomWrapper
    {
        public RandomWrapper()
        {
            mSeed = 0;
            mRandom = new System.Random();
        }

        public RandomWrapper(int seed)
        {
            mSeed = seed;
            mRandom = new System.Random(seed);
        }

        //return value in [min, max]
        public float Range(float min, float max)
        {
            float t = mRandom.Next() / (float)(int.MaxValue - 1);
            return min + t * (max - min);
        }

        //return value in [min, max)
        public int Range(int min, int max)
        {
            return mRandom.Next(min, max);
        }

        public Vector2 insideUnitCircle { 
            get
            {
                float r = Range(0, 1);
                float angle = Range(0, 1) * 360.0f * Mathf.Deg2Rad;
                float x = Mathf.Sin(angle) * r;
                float y = Mathf.Cos(angle) * r;
                return new Vector2(x, y);
            }
        }

        public int seed { get { return mSeed; } }

        System.Random mRandom;
        int mSeed;
    }
}
