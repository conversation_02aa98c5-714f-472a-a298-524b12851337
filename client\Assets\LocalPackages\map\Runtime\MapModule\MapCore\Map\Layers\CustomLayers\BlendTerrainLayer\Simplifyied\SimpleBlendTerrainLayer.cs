﻿ 



 
 


using UnityEngine;
using System.IO;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //使用拼接规则的地表层,lod使用render texture来优化game object数量,适用于相机可升到超高的超大地图
    public partial class SimpleBlendTerrainLayer : MapLayerBase, IBlendTerrainLayer
    {
        public SimpleBlendTerrainLayer(Map map) :base(map) { }

        //加载地图层
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.SimpleBlendTerrainLayerData;
            if (sourceLayer == null)
            {
                return;
            }
            int rows = sourceLayer.rows;
            int cols = sourceLayer.cols;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth, sourceLayer.tileHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            int lodCount = 1;
            if (layerData.config != null)
            {
                lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            List<ModelData[,]> lodTiles = null;
            TerrainRenderTextureLODSetting[] lodSetting = null;
            if (map.isEditorMode == false && sourceLayer.useGeneratedLOD && lodCount > 1)
            {
                var assetPath = map.dataFolder + "/Res/terrain_lod_data_ex.bytes";
                if (MapModuleResourceMgr.Exists(assetPath))
                {
                    LoadLODDataNewAsync(assetPath, (a, b) =>
                     {
                         mLayerData = new SimpleBlendTerrainLayerData(header, config, map, sourceLayer.tiles, sourceLayer.tilePrefabPaths, a, b);
                         mLayerView = new SimpleBlendTerrainLayerView(mLayerData, false);
                         mLayerView.active = layerData.active;
                         mLayerData.SetCallbacks(mLayerView.OnShowLOD0Tile, mLayerView.OnHideLOD0Tile, mLayerView.OnShowLOD1Object, mLayerView.OnHideLOD1Object, mLayerView.OnFinishUpdateViewport);

                         var pos = mLayerView.root.transform.position;
                         pos.y = sourceLayer.layerPosition.y;
                         mLayerView.root.transform.position = pos;

                         map.AddMapLayer(this);
                     });

                }
                else
                {
                    assetPath = map.dataFolder + "/Res/terrain_lod_data.bytes";

                    LoadLODDataAsync(assetPath, (a, b) =>
                     {
                         mLayerData = new SimpleBlendTerrainLayerData(header, config, map, sourceLayer.tiles, sourceLayer.tilePrefabPaths, a, b);
                         mLayerView = new SimpleBlendTerrainLayerView(mLayerData, false);
                         mLayerView.active = layerData.active;
                         mLayerData.SetCallbacks(mLayerView.OnShowLOD0Tile, mLayerView.OnHideLOD0Tile, mLayerView.OnShowLOD1Object, mLayerView.OnHideLOD1Object, mLayerView.OnFinishUpdateViewport);

                         var pos = mLayerView.root.transform.position;
                         pos.y = sourceLayer.layerPosition.y;
                         mLayerView.root.transform.position = pos;

                         map.AddMapLayer(this);
                     });
                }
            }

            
        }

        //卸载地图数据
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            if (mTransition != null)
            {
                mTransition.OnDestroy();
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        void LoadLODData(string path, out List<ModelData[,]> lodTiles, out TerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream == null)
            {
                return;
            }

            using BinaryReader reader = new BinaryReader(stream);

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        int objectDataID = map.nextCustomObjectID;
                        var modelTemplate = map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                        var tile = new ModelData(objectDataID, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false);
                        tiles[r, c] = tile;
                    }
                }
            }

            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();

            //preload prefabs
            for (int i = 0; i < prefabPaths.Count; ++i)
            {
                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
                Debug.Assert(prefab != null);
            }
        }

        void LoadLODDataNew(string path, out List<ModelData[,]> lodTiles, out TerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream == null)
            {
                return;
            }

            using BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();
            bool wrongLODOrientation = reader.ReadBoolean();

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        int objectDataID = map.nextCustomObjectID;
                        var modelTemplate = map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                        var tile = new ModelData(objectDataID, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false);
                        tiles[r, c] = tile;
                    }
                }
            }

            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();

            //preload prefabs
            for (int i = 0; i < prefabPaths.Count; ++i)
            {
                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
                Debug.Assert(prefab != null);
            }
        }

        void LoadLODDataNewAsync(string path, System.Action<List<ModelData[,]>, TerrainRenderTextureLODSetting[]> action)
        {
            TerrainRenderTextureLODSetting[] lodSettings = null;
            List<ModelData[,]> lodTiles = null;

            MapModuleResourceMgr.LoadTextStreamAsync(path, (str, result)=>
            {
                var stream = result;
                if (stream == null)
                {
                    return;
                }

                using BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();
                bool wrongLODOrientation = reader.ReadBoolean();

                //load prefabs
                List<string> prefabPaths = new List<string>();
                int prefabCount = reader.ReadInt32();
                for (int i = 0; i < prefabCount; ++i)
                {
                    prefabPaths.Add(Utils.ReadString(reader));
                }

                //load lod tile data
                lodTiles = new List<ModelData[,]>();
                int lodCount = reader.ReadInt32();
                for (int i = 0; i < lodCount; ++i)
                {
                    int rows = reader.ReadInt32();
                    int cols = reader.ReadInt32();
                    ModelData[,] tiles = new ModelData[rows, cols];
                    lodTiles.Add(tiles);

                    for (int r = 0; r < rows; ++r)
                    {
                        for (int c = 0; c < cols; ++c)
                        {
                            var position = Utils.ReadVector3(reader);
                            var prefabIndex = reader.ReadInt32();
                            int objectDataID = map.nextCustomObjectID;
                            var modelTemplate = map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                            var tile = new ModelData(objectDataID, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false);
                            tiles[r, c] = tile;
                        }
                    }
                }

                //load lod settings
                int lodSettingCount = reader.ReadInt32();
                lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
                for (int i = 0; i < lodSettingCount; ++i)
                {
                    float cameraHeight = reader.ReadSingle();
                    int blockSize = reader.ReadInt32();
                    int realBlockSize = reader.ReadInt32();
                    var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                    setting.realBlockSize = realBlockSize;
                    lodSettings[i] = setting;
                }

                reader.Close();

                //preload prefabs
                for (int i = 0; i < prefabPaths.Count; ++i)
                {
                    var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
                    Debug.Assert(prefab != null);
                }
                action.Invoke(lodTiles, lodSettings);
            });
            
        }

        void LoadLODDataAsync(string path, System.Action<List<ModelData[,]>, TerrainRenderTextureLODSetting[]> action)
        {
            TerrainRenderTextureLODSetting[] lodSettings = null;
            List<ModelData[,]>  lodTiles = null;

            var map = Map.currentMap;

            List<string> prefabPaths = null;

            MapModuleResourceMgr.LoadTextStreamAsync(path, (str, stream)=>
            {
                if (stream != null)
                {
                    using BinaryReader reader = new BinaryReader(stream);

                    //load prefabs
                    prefabPaths = new List<string>();
                    int prefabCount = reader.ReadInt32();
                    for (int i = 0; i < prefabCount; ++i)
                    {
                        prefabPaths.Add(Utils.ReadString(reader));
                    }

                    //load lod tile data
                    lodTiles = new List<ModelData[,]>();
                    int lodCount = reader.ReadInt32();
                    for (int i = 0; i < lodCount; ++i)
                    {
                        int rows = reader.ReadInt32();
                        int cols = reader.ReadInt32();
                        ModelData[,] tiles = new ModelData[rows, cols];
                        lodTiles.Add(tiles);

                        for (int r = 0; r < rows; ++r)
                        {
                            for (int c = 0; c < cols; ++c)
                            {
                                var position = Utils.ReadVector3(reader);
                                var prefabIndex = reader.ReadInt32();
                                int objectDataID = map.nextCustomObjectID;
                                var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                                var tile = new ModelData(objectDataID, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false);
                                tiles[r, c] = tile;
                            }
                        }
                    }

                    //load lod settings
                    int lodSettingCount = reader.ReadInt32();
                    lodSettings = new TerrainRenderTextureLODSetting[lodSettingCount];
                    for (int i = 0; i < lodSettingCount; ++i)
                    {
                        float cameraHeight = reader.ReadSingle();
                        int blockSize = reader.ReadInt32();
                        int realBlockSize = reader.ReadInt32();
                        var setting = new TerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                        setting.realBlockSize = realBlockSize;
                        lodSettings[i] = setting;
                    }

                    reader.Close();
                }
                //MapModuleResourceMgr.UnloadText(assetPath);

                if (prefabPaths != null)
                {
                    //preload prefabs
                    for (int i = 0; i < prefabPaths.Count; ++i)
                    {
                        var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
                        Debug.Assert(prefab != null);
                    }
                }
                action.Invoke(lodTiles, lodSettings);
            });
            
        }

        public int GetLODFromCameraHeight(float cameraHeight)
        {
            return mLayerData.GetLODFromCameraHeight(cameraHeight);
        }

        //返回地图的根game object
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override int id { get { return mLayerData.id; } }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateLOD(newCameraZoom);

            if (!lodChanged)
            {
                mLayerData.UpdateViewport(newViewport);
            }
            else
            {
                mLayerData.OnLODChanged(mLayerData.lastLOD, mLayerData.currentLOD, newViewport);
                OnLODChange(mLayerData.lastLOD, mLayerData.currentLOD, map.viewCenter);
            }

            CheckCustomLODSwitchEvent(mLayerData.currentLOD, map.viewCenter);

            return lodChanged;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            Debug.Assert(false, "Can't be here!");
            return false;
        }

        //加载完地图时刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public string GetTilePrefabPath(int x, int y, int lod)
        {
            Debug.Assert(lod == 0, "Only support lod0 in simple blend terrain layer!");
            int tileType;
            return mLayerData.GetTilePrefabPath(x, y, out tileType);
        }

        public bool HasTile(int x, int y)
        {
            return mLayerData.GetTileType(x, y) > 0;
        }

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }

        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }

        public void UpdateTerrainGeneratedLODHeight(int lod, float height)
        {
            mLayerData.UpdateTerrainGeneratedLODHeight(lod, height);
        }

        public List<string> GetUsedTilePrefabs()
        {
            List<string> prefabPaths = new List<string>();
            prefabPaths.AddRange(mLayerData.tilePrefabPaths);
            return prefabPaths;
        }

        public void GetTerrainTileMeshAndGameObject(int x, int y, out Mesh mesh, out GameObject gameObject)
        {
            mesh = null;
            gameObject = null;
            Debug.Assert(false, "todo");
        }

        //地图层的名称
        public override string name
        {
            get
            {
                return mLayerData?.name;
            }
            set {
                mLayerData.name = value;
            }
        }
        //地图层的格子类型
        public override GridType gridType { get { return GridType.Rectangle; } }
        //x方向上格子的数量
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        //格子的宽
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        //格子的高
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        //地图层的偏移值
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public bool asyncLoading { get { return mLayerView.asyncLoading; } set { mLayerView.asyncLoading = value; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;
        public MapLayerData layerData { get { return mLayerData; } }
        public MapLayerView layerView { get { return mLayerView; } }
        public MapLayerLODConfig lodConfig { get { return mLayerData.lodConfig; } }
        public bool supportGeneratingLOD { get { return true; } }

        //拼接地表层的tile数据
        SimpleBlendTerrainLayerData mLayerData;
        //地表层的模型管理
        SimpleBlendTerrainLayerView mLayerView;
    }
}
