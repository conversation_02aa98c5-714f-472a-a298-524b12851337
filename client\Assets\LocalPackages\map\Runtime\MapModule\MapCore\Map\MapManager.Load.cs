﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;
using System;
using System.Collections;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using TFW.Map.config;

namespace TFW.Map
{
    public static partial class MapManager
    {
        public static event Action unloadMapEvent;
        public static event Action loadMapEvent;

        static MapManager()
        {
            if (mMapSettings.maps.Length == 0)
            {
                //LoadSetting(MapModule.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH);
                LoadSettingAsync(MapModule.mapListDir + "/" + MapCoreDef.MAP_LIST_FILE_PATH);
            }
        }

        //public static IEnumerator LoadMapAsync(string mapName, bool async)
        //{
        //    if (string.IsNullOrEmpty(mapName))
        //    {
        //        mapName = mMapSettings.activeMapName;
        //    }

        //    if (string.IsNullOrEmpty(mapName))
        //    {
        //        Debug.LogError("LoadMap failed! Invalid map name");
        //        yield break;
        //    }

        //    var map = FindMap(mapName);
        //    if (map != null)
        //    {
        //        yield break;
        //    }

        //    if (async)
        //    {
        //        LoadingTaskManager.Init();
        //    }

        //    MapStats.ResetLoadingTime();
        //    MapStats.BeginLoading();

        //    MapCameraMgr.Reset();

        //    var mapSetting = GetSetting(mapName);
        //    var filePath = MapCoreDef.GetFullMapDataFilePath(mapSetting.file_folder);
        //    map = new Map(mapName, mapSetting);
        //    mMaps[mapName] = map;

        //    // 读取地图数据
        //    var cameraSetting = MapCameraMgr.GetMapCameraSetting(mapSetting.default_camera_setting);
        //    mapSetting.cameraSetting = cameraSetting;

        //    // 导入地图数据
        //    var stream = MapModuleResourceMgr.LoadTextStream(filePath, true);
        //    var task = Task.Run(() =>
        //        MapBinaryImporterManager.LoadFromStream(Utils.GetPathName(filePath, false), stream));
        //    yield return new WaitUntil(() => task.IsCompleted);
        //    var mapData = task.Result;

        //    // 加载地图实例
        //    yield return map.LoadAsync(mapData, MapCameraMgr.MapCamera, Vector3.zero, new Rect());

        //    // 加载地图层
        //    var nLayers = mapData.map.mapLayers.Length;
        //    for (var i = 0; i < nLayers; ++i)
        //    {
        //        // 获取编辑器中导出的每一层的地图数据
        //        var mapLayerData = mapData.map.mapLayers[i];
        //        yield return LoadMapLayerAsync(mapLayerData, mapSetting, map, async);
        //    }

        //    LoadSimpleBlendTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
        //    LoadTileBlockTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
        //    LoadSplineLayer(mapSetting, map, async);
        //    LoadCameraLookAtArea(map);

        //    // 删除地图的配置数据
        //    mapData.OnDestroy();

        //    var spritePath = MapCoreDef.GetDetailSpriteObjectPath(mapSetting.file_folder);
        //    var spawnPointPath = MapCoreDef.GetDetailSpriteSpawnPointFilePath(mapSetting.file_folder);
        //    map.LoadDetailSprites(spritePath, spawnPointPath,
        //        $"{MapModule.configResDirectory}keep_decoration_size.asset");

        //    MapStats.EndLoading();

        //    // 加载插件层
        //    LoadPluginLayers(map);

        //    loadMapEvent?.Invoke();

        //    ApplyMapSetting(mapSetting);
        //}

        //public static Map LoadMap(string mapName, bool async)
        //{
        //    if (string.IsNullOrEmpty(mapName))
        //    {
        //        mapName = mMapSettings.activeMapName;
        //    }

        //    if (string.IsNullOrEmpty(mapName))
        //    {
        //        Debug.LogError("LoadMap failed! Invalid map name");
        //        return null;
        //    }

        //    var map = FindMap(mapName);
        //    if (map != null)
        //    {
        //        return map;
        //    }

        //    if (async)
        //    {
        //        LoadingTaskManager.Init();
        //    }
            
        //    MapStats.ResetLoadingTime();
        //    MapStats.BeginLoading();

        //    MapCameraMgr.Reset();

        //    var mapSetting = GetSetting(mapName);
        //    var filePath = MapCoreDef.GetFullMapDataFilePath(mapSetting.file_folder);
           
        //    map = new Map(mapName, mapSetting);
        //    mMaps[mapName] = map;
           
        //    // 读取地图数据
        //    var cameraSetting = MapCameraMgr.GetMapCameraSetting(mapSetting.default_camera_setting);
        //    mapSetting.cameraSetting = cameraSetting;
        //    var mapData = MapBinaryImporterManager.Load(filePath);
            
        //    map.Load(mapData, MapCameraMgr.MapCamera, Vector3.zero, new Rect());
        //    // 加载地图层
        //    var nLayers = mapData.map.mapLayers.Length;
        //    for (var i = 0; i < nLayers; ++i)
        //    {
        //        //获取编辑器中导出的每一层的地图数据
        //        var mapLayerData = mapData.map.mapLayers[i];
        //        LoadMapLayer(mapLayerData, mapSetting, map, async);
        //    }
        //    LoadSimpleBlendTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
        //    LoadTileBlockTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
        //    LoadSplineLayer(mapSetting, map, async);
        //    LoadCameraLookAtArea(map);

        //    // 删除地图的配置数据
        //    mapData.OnDestroy();

        //    var spritePath = MapCoreDef.GetDetailSpriteObjectPath(mapSetting.file_folder);
        //    var spawnPointPath = MapCoreDef.GetDetailSpriteSpawnPointFilePath(mapSetting.file_folder);
        //    map.LoadDetailSprites(spritePath, spawnPointPath,
        //        $"{MapModule.configResDirectory}keep_decoration_size.asset");

        //    MapStats.EndLoading();

        //    // 加载插件层
        //    LoadPluginLayers(map);

        //    loadMapEvent?.Invoke();

        //    ApplyMapSetting(mapSetting);
        //    return map;
        //}

        public static async UniTask<Map> LoadMapAsync(string mapName)
        {
            if (string.IsNullOrEmpty(mapName))
            {
                mapName = mMapSettings.activeMapName;
            }

            if (string.IsNullOrEmpty(mapName))
            {
                Debug.LogError("LoadMap failed! Invalid map name");
                return null;
            }

            var map = FindMap(mapName);
            if (map != null)
            {
                return map;
            }

            MapStats.ResetLoadingTime();
            MapStats.BeginLoading();

            MapCameraMgr.Reset();

            UnityEngine.Debug.LogWarning($"地图 GetSettingAsync Start");
            var mapSetting = await GetSettingAsync(mapName, false);
            {
                UnityEngine.Debug.LogWarning($"地图 GetSettingAsync End");
                var filePath = MapCoreDef.GetFullMapDataFilePath(mapSetting.file_folder);

                map = new Map(mapName, mapSetting);
                mMaps[mapName] = map;

                // 读取地图数据
                var cameraSetting = MapCameraMgr.GetMapCameraSetting(mapSetting.default_camera_setting);

                if (cameraSetting == null)
                {
                    UnityEngine.Debug.LogError($"GetcameraSetting NUll {mapName} {mapSetting.default_camera_setting}");
                }

                mapSetting.cameraSetting = cameraSetting;
                UnityEngine.Debug.LogWarning($"地图 MapBinaryImporterManager Start");
                var mapData = await MapBinaryImporterManager.LoadAsync(filePath);
                {
                    UnityEngine.Debug.LogWarning($"地图 MapBinaryImporterManager End");
                    return await LoadMapLayerData(mapData, map, mapSetting);
                }
            }
        }

        private static async UniTask<Map> LoadMapLayerData(config.SLGMakerData mapData, Map map, MapSetting mapSetting)
        {
            UnityEngine.Debug.LogWarning($"地图 LoadMapLayerData 001");
            map.Load(mapData, MapCameraMgr.MapCamera, Vector3.zero, new Rect());
            UnityEngine.Debug.LogWarning($"地图 LoadMapLayerData 002");
            // 加载地图层
            var nLayers = mapData.map.mapLayers.Length;
            var async = true;
            for (var i = 0; i < nLayers; ++i)
            {
                //获取编辑器中导出的每一层的地图数据
                var mapLayerData = mapData.map.mapLayers[i];
                UnityEngine.Debug.LogWarning($"地图 LoadMapLayerData{i}=> {mapLayerData.name}");
                await LoadMapLayerAsync(mapLayerData, mapSetting, map, async).ToUniTask();
            }
             
            LoadSimpleBlendTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
            LoadTileBlockTerrainLayer(mapData.map.mapLayers, mapSetting, map, async);
            LoadSplineLayer(mapSetting, map, async);
            LoadCameraLookAtArea(map);

            // 删除地图的配置数据
            mapData.OnDestroy();

            var spritePath = MapCoreDef.GetDetailSpriteObjectPath(mapSetting.file_folder);
            var spawnPointPath = MapCoreDef.GetDetailSpriteSpawnPointFilePath(mapSetting.file_folder);


            if (map.name == "MobaMap")
            {
                map.LoadDetailSprites(spritePath, spawnPointPath,
                $"{MapModule.configResDirectory}keep_decoration_size_moba.asset");
            }
            else
            {
                map.LoadDetailSprites(spritePath, spawnPointPath,
                map.name == "MobaMap" ? $"{MapModule.configResDirectory}keep_decoration_size_moba.asset" : $"{MapModule.configResDirectory}keep_decoration_size.asset");

            }


            MapStats.EndLoading();

            // 加载插件层
            LoadPluginLayers(map);

            loadMapEvent?.Invoke();

            ApplyMapSetting(mapSetting);
             
            return map;
        }

        public static void UnloadMap(string mapName)
        {
            var map = FindMap(mapName);
            if (map != null)
            {
                if (unloadMapEvent != null)
                {
                    unloadMapEvent();
                }

                map.Unload();
                mMaps.Remove(mapName);
            }
        }

        public static void UnloadAllMaps()
        {
            foreach (var p in mMaps)
            {
                p.Value.Unload();
            }

            mMaps.Clear();
            Map.currentMap = null;
        }

        public static Map FindMap(string mapName)
        {
            Map map;
            mMaps.TryGetValue(mapName, out map);
            return map;
        }

        //public static Vector3 GetMapOrigin(string mapName)
        //{
        //    var setting = GetSetting(mapName);
        //    if (setting != null)
        //    {
        //        return setting.origin;
        //    }

        //    return Vector3.zero;
        //}

        private class LayerEntry
        {
            public LayerEntry(Func<Map, MapLayerBase> createLayerFunc, bool enabled)
            {
                this.createLayerFunc = createLayerFunc;
                this.enabled = enabled;
            }

            public Func<Map, MapLayerBase> createLayerFunc;

            public bool enabled = true;
        }

        private static Dictionary<string, LayerEntry> mLayerList = new Dictionary<string, LayerEntry>()
        {
            {
                MapCoreDef.MAP_LAYER_NODE_GROUND, new LayerEntry(map => new BlendTerrainLayer(map), true)
            }, // 地表     
            {
                MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND,
                new LayerEntry(map => new VaryingTileSizeTerrainLayer(map), true)
            }, // 地表 
            {
                MapCoreDef.MAP_LAYER_NODE_FRONT, new LayerEntry(map => new GridModelLayer(map), true)
            }, // 前景
            {
                MapCoreDef.MAP_LAYER_NODE_RAILWAY, new LayerEntry(map => new RailwayLayer(map), true)
            }, // 迷雾层   
            {
                MapCoreDef.MAP_LAYER_NODE_DYNAMIC,
                new LayerEntry(map => new DynamicObjectLayer(map), true)
            }, //自由摆放的物体层
            { MapCoreDef.MAP_LAYER_NODE_LOD, new LayerEntry(map => new LODLayer(map), true) }, //自由摆放的物体层
            {
                MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER,
                new LayerEntry(map => new CircleBorderLayer(map), true)
            }, //圆形的边界层
            {
                MapCoreDef.MAP_LAYER_NODE_RUNTIME_RIVER, new LayerEntry(map => new RiverLayer(map), true)
            }, //河流层
            {
                MapCoreDef.MAP_LAYER_NODE_DECORATION,
                new LayerEntry(map => new GridModelLayer2(map), true)
            }, //装饰物层
            {
                MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER,
                new LayerEntry(map => new RuntimeDecorationBorderLayer(map), true)
            }, //装饰物border层
            {
                MapCoreDef.MAP_LAYER_NODE_SPLINE, new LayerEntry(map => new SplineLayer(map), true)
            }, // 海岸线
            {
                MapCoreDef.MAP_LAYER_NODE_SPLIT_FOG, new LayerEntry(map => new SplitFogLayer(map), true)
            }, // split fog
            {
                MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY,
                new LayerEntry(map => new CityTerritoryLayer(map), true)
            }, // city territory layer
            {
                MapCoreDef.MAP_LAYER_NODE_REGION, new LayerEntry(map => new RuntimeRegionLayer(map), true)
            }, // region layer
            {
                MapCoreDef.MAP_LAYER_NODE_REGION_COLOR,
                new LayerEntry(map => new RuntimeRegionColorLayer(map), true)
            }, // region color layer
        };

        private static MapLayerBase LoadMapLayer(config.MapLayerData layerData, MapSetting setting, Map map,
            bool asyncLoading)
        {
            MapLayerBase layer = null;
            if (mLayerList.TryGetValue(layerData.name, out var entry) && entry.enabled)
            {
                layer = entry.createLayerFunc(map);
            }

            layer?.Load(layerData, setting, asyncLoading);
            if (map.name.Equals("KVK"))
            {
                if (layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_GROUND) || layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND))
                    layer.gameObject.transform.localPosition = new Vector3(0, -2.1f, 0);
                if (layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_DECORATION))
                    layer.gameObject.transform.localPosition = new Vector3(0, -2f, 0);
            }
            return layer;
        }

        private static IEnumerator LoadMapLayerAsync(config.MapLayerData layerData, MapSetting setting, Map map,
            bool asyncLoading)
        {
            MapLayerBase layer = null;
            if (mLayerList.TryGetValue(layerData.name, out var entry) && entry.enabled)
            {
                layer = entry.createLayerFunc(map);
            }
           
            yield return layer?.LoadAsync(layerData, setting, asyncLoading);
            if (map.name.Equals("KVK"))
            {
                if (layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_GROUND) || layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND))
                    layer.gameObject.transform.localPosition = new Vector3(0, -2.1f, 0);
                if (layerData.name.Equals(MapCoreDef.MAP_LAYER_NODE_DECORATION))
                    layer.gameObject.transform.localPosition = new Vector3(0, -2f, 0);
            }
        }

        private static void LoadSplineLayer(MapSetting setting, Map map, bool asyncLoading)
        {
            var layer = new SplineLayer(map);
            layer.Load(null, setting, asyncLoading);
        }

        private static void LoadSimpleBlendTerrainLayer(config.MapLayerData[] layerDatas, MapSetting setting, Map map,
            bool asyncLoading)
        {
            config.MapLayerData simpleBlendTerrainLayerData = null;
            foreach (var layer in layerDatas)
            {
                if (layer.name == MapCoreDef.MAP_LAYER_NODE_GROUND)
                {
                    simpleBlendTerrainLayerData = layer;
                    break;
                }
            }

            if (simpleBlendTerrainLayerData != null)
            {
                var layer = new SimpleBlendTerrainLayer(map);
                layer.Load(simpleBlendTerrainLayerData, setting, asyncLoading);
            }
        }

        private static void LoadTileBlockTerrainLayer(config.MapLayerData[] layerDatas, MapSetting setting, Map map,
            bool asyncLoading)
        {
            config.MapLayerData tileBlockTerrainLayerData = null;
            foreach (var layer in layerDatas)
            {
                if (layer.name == MapCoreDef.MAP_LAYER_NODE_GROUND)
                {
                    tileBlockTerrainLayerData = layer;
                    break;
                }
            }

            if (tileBlockTerrainLayerData != null)
            {
                var layer = new TileBlockTerrainLayer(map);
                layer.Load(tileBlockTerrainLayerData, setting, asyncLoading);
            }
        }

        private static void LoadCameraLookAtArea(Map map)
        {
            var dataPath = MapCoreDef.GetCameraLookAtAreaDataPath(map.dataFolder);
            map.data.lookAtAreaClamp = CameraClampInRegion.Load(dataPath);
        }

        private static void LoadPluginLayers(Map map)
        {
#if DISABLE_MAP_REFLECTION
#else
            var pluginLayerNames = map.data.pluginLayerNames;
            foreach (var name in pluginLayerNames)
            {
                var pluginLayer = MapPlugin.CreatePluginLayer(name, map);
                pluginLayer?.LoadGameData();
            }
#endif
        }
    }
}