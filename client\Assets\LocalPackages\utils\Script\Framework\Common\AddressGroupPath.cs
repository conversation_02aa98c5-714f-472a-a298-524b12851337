﻿using UnityEditor;
using UnityEngine;

namespace TFW.Build
{
    public class AddressGroupPath 
    {

        public static string LocalBuildPath
        {
            get
            {
                return "DLC/" + BundleHelper.GetPlatformName()+"/"+ UnityEngine.Application.version;
            }
        }

        public static string LoadPath
        {
            get 
            {
                return Application.streamingAssetsPath; 
            }
        }

        

    }


    public class BundleHelper
    {

        /// <summary>
        /// 获取当前平台名称
        /// </summary>
        /// <returns></returns>
        public static string GetPlatformName()
        {
#if UNITY_EDITOR
            return GetPlatformForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
#else
			return GetPlatformForAssetBundles(Application.platform);
#endif
        }

#if UNITY_EDITOR
        /// <summary>
        /// 通过BuildTarget获取平台名称
        /// </summary>
        /// <param name="target"></param>
        /// <returns></returns>
        private static string GetPlatformForAssetBundles(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return "android";
                case BuildTarget.iOS:
                    return "ios";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return "win32";
                case BuildTarget.WebGL:
                    return "WebGL";
                default:
                    return null;
            }
        }
#endif

        /// <summary>
        /// 通过BuildTarget获取平台名称
        /// </summary>
        /// <param name="platform"></param>
        /// <returns></returns>
        private static string GetPlatformForAssetBundles(RuntimePlatform platform)
        {
            switch (platform)
            {
                case RuntimePlatform.Android:
                    return "android";
                case RuntimePlatform.IPhonePlayer:
                    return "ios";
                case RuntimePlatform.WindowsPlayer:
                    return "win32";
                case RuntimePlatform.WebGLPlayer:
                    return "WebGL";
                default:
                    return null;
            }
        }
    }
}