﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public static class BuildingLODManagerController
    {
        public static void Update(bool forceUpdate)
        {
            MapStats.BeginMapUpdate();
            if (Map.currentMap != null)
            {
                if (BuildingLODManagerUpdateDataCache.collapseCameraHeight == 0)
                {
                    var scaleConfig = MapCameraMgr.buildingScaleConfig;
                    BuildingLODManagerUpdateDataCache.Init(scaleConfig.minimumCameraHeight, scaleConfig.maximumCameraHeight);
                    Debug.Assert(BuildingLODManagerUpdateDataCache.collapseCameraHeight != 0);
                }

                BuildingLODManagerUpdateDataCache.Update();
                var viewport = Map.currentMap.GetViewportRect();
                CityLODManager.UpdateAllCities(forceUpdate, viewport);
                ResourceBuildingLODManager.UpdateAllCities(forceUpdate, viewport);
            }
            MapStats.EndMapUpdate();
        }
    }
}
