﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveMapCollision : EditorAction
    {
        public ActionRemoveMapCollision(int layerID, int dataID)
        {
            mLayerID = layerID;
            mDataID = dataID;
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            var navMeshObstacleOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            mNavMeshObstacleOutline = new List<Vector3>(navMeshObstacleOutline.Count);
            mNavMeshObstacleOutline.AddRange(navMeshObstacleOutline);
            var objectPlacementOutline = data.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
            mObjectPlacementOutline = new List<Vector3>(objectPlacementOutline.Count);
            mObjectPlacementOutline.AddRange(objectPlacementOutline);

            mIsExtendable = data.IsExtendable();
            mAttribute = data.attribute;
            mType = data.type;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            var outlineData0 = new OutlineData(mNavMeshObstacleOutline);
            var outlineData1 = new OutlineData(mObjectPlacementOutline);
            var outlineDatas = new OutlineData[2] { outlineData0, outlineData1 };
            var data = new MapCollisionData(mDataID, Map.currentMap, outlineDatas, layer.displayVertexRadius, mIsExtendable, mAttribute, mType, false);
            layer.AddObject(data);
            return true;
        }

        int mLayerID;
        int mDataID;
        bool mIsExtendable;
        CollisionAttribute mAttribute;
        int mType;
        List<Vector3> mNavMeshObstacleOutline;
        List<Vector3> mObjectPlacementOutline;
    }
}

#endif