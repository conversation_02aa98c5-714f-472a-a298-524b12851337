﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2019.11.6
 */

using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using System.Diagnostics;

namespace TFW.Map
{
    public partial class EditorGridModelLayer : MapLayerBase
    {
        public EditorGridModelLayer(Map map) : base(map) { }

        //清理地图层
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            SetDirty();
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.GridModelLayerData;
            int rows = sourceLayer.zTileCount;
            int cols = sourceLayer.xTileCount;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth, sourceLayer.tileHeight, sourceLayer.gridType, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            var groupManager = Utils.CreateLODGroupManager(layerData, map);

            mLayerData = new SparseGridObjectLayerData(header, config, map, groupManager);
            mLayerView = new ModelLayerView(mLayerData, true);
            mLayerView.active = sourceLayer.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            int tileObjectStartID = map.nextCustomObjectID;
            if (sourceLayer.objects != null)
            {
                int n = sourceLayer.objects.Length;
                for (int y = 0; y < rows; ++y)
                {
                    for (int x = 0; x < cols; ++x)
                    {
                        int idx = y * cols + x;
                        var gridModel = sourceLayer.objects[idx] as config.GridModelData;
                        if (gridModel != null)
                        {
                            var modelTemplate = map.FindObject(gridModel.modelTemplateID) as ModelTemplate;
                            if (modelTemplate != null)
                            {
                                var modelData = new ModelData(gridModel.id, map, gridModel.flag, gridModel.position, gridModel.rotation, gridModel.scale, modelTemplate, false);

                                mLayerData.AddObjectData(modelData);
                            }
                            else
                            {
                                UnityEngine.Debug.LogError($"Model template used by {x}_{y} of id {gridModel.modelTemplateID} not found!");
                            }
                        }
                    }
                }
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);

            SetDirty();
        }

        //在格子的x,y坐标下添加一个地图对象
        //prefabPath: 地图对象prefab的路径,如果prefabPath对应的model template(模型的配置)没被找到,会自动生成一个默认的ModelTemplate
        //返回该地图对象的id
        public int AddObject(string prefabPath, Vector3 pos, Quaternion rotation, Vector3 scale, int objectID = 0, bool useTextureModel = false)
        {
            if (objectID == 0)
            {
                objectID = map.nextCustomObjectID;
            }
            ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, prefabPath, true, false);

            int flag = 0;
            if (useTextureModel)
            {
                flag = (int)ObjectFlag.kUseRenderTextureModel;
            }

            var data = new ModelData(objectID, map, flag, pos, rotation, scale, modelTemplate, false);
            bool success = mLayerData.AddObjectData(data);
            if (success)
            {
                //生成一个地图对象的视图
                mLayerView.AddObjectView(data);

                SetDirty();
            }
            return data.id;
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, objectData.GetAssetPath(), true, false);
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);

                    SetDirty();
                    return true;
                }
            }
            return false;
        }

        //删除该层的一个地图对象
        //objectDataID: 地图对象的id,这个id是客户端管理的,并非任何服务器对象的id
        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                mLayerView.RemoveObjectView(objectDataID);

                SetDirty();
            }
            return success;
        }

        public bool RemoveObject(int x, int y)
        {
            var data = GetObjectData(x, y);
            if (data != null)
            {
                return RemoveObject(data.GetEntityID());
            }
            return false;
        }

        //返回x,y格子中的地图对象数据
        public IMapObjectData GetObjectData(int x, int y)
        {
            return (mLayerData as IGridObjectLayerData).GetObjectData(x, y);
        }

        public IMapObjectData GetObjectData(int objectID)
        {
            return mLayerData.GetObjectData(objectID);
        }

        //获取坐标在x,z位置的tile使用的prefab的id
        public int GetModelTemplateID(float x, float z)
        {
            var coord = mLayerData.FromWorldPositionToCoordinate(new Vector3(x, 0, z));
            var data = GetObjectData(coord.x, coord.y);
            if (data != null)
            {
                return data.GetModelTemplateID();
            }
            return 0;
        }

        //返回游戏对象的game object
        public GameObject GetObjectGameObject(int objectDataID)
        {
            var view = mLayerView.GetObjectView(objectDataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            //mLayerView.SetLookAtPosition(newLookAtPos);
            return lodChanged;
        }

        //内部使用,加载完地图后刷新该层中可见的地图对象
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            UnityEngine.Debug.Assert(layerData.horizontalTileCount == layerData.verticalTileCount);
            int oldSize = layerData.horizontalTileCount;
            int newSize = Mathf.CeilToInt(newWidth / mLayerData.tileWidth);
            if (newSize != oldSize)
            {
                if (newSize > oldSize)
                {
                    mLayerData.Resize(newSize, useLayerOffset);

                    for (int i = 0; i < newSize; ++i)
                    {
                        for (int j = 0; j < newSize; ++j)
                        {
                            //改变tile view的位置
                            var tile = mLayerData.GetObjectData(j, i);
                            if (tile != null)
                            {
                                var newPos = mLayerData.FromCoordinateToWorldPositionCenter(j, i);
                                mLayerData.SetObjectPosition(tile.GetEntityID(), newPos);
                            }
                        }
                    }
                }
                else
                {
                    mLayerData.Resize(newSize, useLayerOffset);

                    for (int i = 0; i < newSize; ++i)
                    {
                        for (int j = 0; j < newSize; ++j)
                        {
                            //改变tile view的位置
                            var tile = mLayerData.GetObjectData(j, i);
                            if (tile != null)
                            {
                                var newPos = mLayerData.FromCoordinateToWorldPositionCenter(j, i);
                                mLayerData.SetObjectPosition(tile.GetEntityID(), newPos);
                            }
                        }
                    }
                }
            }

            return true;
        }

        //从世界坐标转换到格子坐标
        public Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            return mLayerData.FromWorldPositionToCoordinate(position);
        }
        //从屏幕坐标转换到格子坐标
        public Vector2Int FromScreenToCoordinate(Vector3 screenPos, UnityEngine.Camera camera)
        {
            return mLayerData.FromScreenToCoordinate(screenPos, camera);
        }
        //从格子坐标转换到世界坐标
        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }
        //从格子坐标转换到格子的中心点的世界坐标
        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y);
        }
        public Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height)
        {
            return mLayerData.FromCoordinateToWorldPositionCenter(x, y, width, height);
        }
        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void SwitchModel(int x, int y, bool toTexture)
        {
            //在模型或贴图之间切换
            var data = GetObjectData(x, y);
            if (data != null)
            {
                if (toTexture)
                {
                    data.AddFlag((int)ObjectFlag.kUseRenderTextureModel);
                }
                else
                {
                    data.RemoveFlag((int)ObjectFlag.kUseRenderTextureModel);
                }
                mLayerView.SwitchModel(data, toTexture);
            }
        }

        public void ReplaceObjectModelTemplate(int x, int y, string newModelTemplatePath)
        {
            var data = GetObjectData(x, y) as ModelData;
            if (data != null)
            {
                var modelTemplate = map.FindModelTemplate(newModelTemplatePath);
                if (modelTemplate != null)
                {
                    modelTemplate.Recreate();
                    map.GetOrCreateModelTemplate(data.id, newModelTemplatePath, true, true);
                    data.SetModelTemplate(modelTemplate);
                }
                else
                {
                    modelTemplate = map.GetOrCreateModelTemplate(data.id, newModelTemplatePath, true, true);
                    data.SetModelTemplate(modelTemplate);
                }

                //update view
                bool isVisible = data.IsObjActive();
                (mLayerView as ModelLayerView).RemoveObjectView(data.id);
                if (isVisible)
                {
                    map.view.ClearCachedGameObjects(newModelTemplatePath);
                    map.view.modelTemplateRenderToTextureGameObjectManager.ClearCachedPool(modelTemplate.id);
                    mLayerView.AddObjectView(data);
                }

                SetDirty();
            }
        }

        //创建front layer每个tile使用的model template的bvh trees
        public Dictionary<int, List<BVH>> CreateBVHTrees(int maxDepth)
        {
#if true
            return CreateBVHTreesMultithread(maxDepth);
#else
            Stopwatch w = new Stopwatch();
            w.Start();
            Dictionary<int, List<BVH>> bvhTrees = new Dictionary<int, List<BVH>>();
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    var objData = GetObjectData(j, i);
                    if (objData != null)
                    {
                        List<BVH> trees;
                        int modelTemplateID = objData.GetModelTemplateID();
                        bvhTrees.TryGetValue(modelTemplateID, out trees);
                        if (trees == null)
                        {
                            trees = CreateBVHTree(objData.GetModelTemplate(), maxDepth);
                            bvhTrees[modelTemplateID] = trees;
                        }
                    }
                }
            }
            w.Stop();
            var elapsedTime = w.ElapsedMilliseconds;
            UnityEngine.Debug.Log($"CreateBVHTrees Elapsed Time: {elapsedTime} ms");
            return bvhTrees;
#endif
        }

        //创建front layer每个tile使用的model template的bvh trees
        public Dictionary<int, List<BVH>> CreateBVHTreesMultithread(int maxDepth)
        {
            Stopwatch w = new Stopwatch();
            w.Start();
            Dictionary<int, List<BVH>> bvhTrees = new Dictionary<int, List<BVH>>();
            List<ModelTemplate> bvhsTemplateID = new List<ModelTemplate>();
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    var objData = GetObjectData(j, i);
                    if (objData != null)
                    {
                        var modelTemplate = objData.GetModelTemplate();
                        bvhsTemplateID.Add(modelTemplate);
                    }
                }
            }

            List<Task<List<BVH>>> tasks = new List<Task<List<BVH>>>();
            for (int i = 0; i < bvhsTemplateID.Count; ++i)
            {
                var template = bvhsTemplateID[i];
                Task<List<BVH>> task = Task<List<BVH>>.Run(()=> {
                    return CreateBVHTree(template, maxDepth);
                });
                tasks.Add(task);
            }
            for (int i = 0; i < tasks.Count; ++i)
            {
                var trees = tasks[i].Result;
                var temp = bvhsTemplateID[i];
                bvhTrees[temp.id] = trees;
            }

            w.Stop();
            var elapsedTime = w.ElapsedMilliseconds;
            UnityEngine.Debug.Log($"CreateBVHTreesMultithread Elapsed Time: {elapsedTime} ms");
            return bvhTrees;
        }

        List<BVH> CreateBVHTree(ModelTemplate modelTemplate, int maxDepth)
        {
            int lodCount = modelTemplate.lodCount;
            List<BVH> bvhs = new List<BVH>(lodCount);
            for (int lod = 0; lod < lodCount; ++lod)
            {
                BVHQuadTree tree = new BVHQuadTree(modelTemplate, lod, maxDepth);
                var bvh = new BVH(tree);
                bvhs.Add(bvh);
            }
            return bvhs;
        }

        public bool asyncLoading
        {
            set
            {
                mLayerView.asyncLoading = value;
            }
            get
            {
                return mLayerView.asyncLoading;
            }
        }
        public int objectCount { get { return mLayerData.objectCount; } }
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        public override GridType gridType { get { return mLayerData.gridType; } }

        public override string name { get { return mLayerData?.name; }  set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override int lodCount => mLayerData.lodCount;
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }

        public override bool active { get { return layerView.active; } set { mLayerView.active = value; } }
        public SparseGridObjectLayerData layerData { get { return mLayerData; } }
        public ModelLayerView layerView { get { return mLayerView; } }
        public bool useTextureModel = true;

        void SetDirty()
        {
            EditorConfig.dirtyFlag |= DirtyMask.FrontLayerObstacle;
            EditorConfig.dirtyFlag |= DirtyMask.NavMesh;
            EditorConfig.dirtyFlag |= DirtyMask.NPCRegionConfig;
            EditorConfig.dirtyFlag |= DirtyMask.NPCSpawnPoints;
        }

        //该层数据的管理
        SparseGridObjectLayerData mLayerData;
        //该层视图的管理
        ModelLayerView mLayerView;
    }
}

#endif