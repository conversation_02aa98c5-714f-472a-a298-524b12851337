﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(RegionLayerLogic))]
    public partial class RegionLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as RegionLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            var layerData = mLogic.layer.GetLayerData();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);

            RegionLayerEventHandlers handlers = new RegionLayerEventHandlers();
            handlers.onDeleteCollision = OnDeleteCollision;
            handlers.onAddCollision = OnAddCollision;
            handlers.onMoveCollision = OnMoveCollision;
            handlers.onMoveVertex = OnMoveVertex;
            handlers.onInsertVertex = OnInsertVertex;
            handlers.onRemoveVertex = OnRemoveVertex;
            handlers.onOutlineChanged = OnOutlineChanged;
            mLogic.layer.SetEventHandlers(handlers);

            mSelectionManager = new RegionSelectionManager(OnSelectionChange);
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);

                mSelectionManager.ClearSelections();

                var layer = mLogic.layer;
                layer?.SetEventHandlers(null);
            }
        }

        List<Vector3> CreateCircleVertices(Vector3 center, float radius, int segment)
        {
            List<Vector3> vertices = new List<Vector3>();
            float deltaAngle = 360.0f / segment;
            for (int i = 0; i < segment; ++i)
            {
                float z = Mathf.Cos(deltaAngle * i * Mathf.Deg2Rad) * radius;
                float x = Mathf.Sin(deltaAngle * i * Mathf.Deg2Rad) * radius;
                float y = 0;
                vertices.Add(new Vector3(x, y, z) + center);
            }

            return vertices;
        }

        List<Vector3> CreateRectangleVertices(Vector3 center, float width, float height)
        {
            float minX = center.x - width * 0.5f;
            float maxX = center.x + width * 0.5f;
            float minZ = center.z - height * 0.5f;
            float maxZ = center.z + height * 0.5f;
            List<Vector3> vertices = new List<Vector3>()
            {
                new Vector3(minX, 0, minZ),
                new Vector3(minX, 0, maxZ),
                new Vector3(maxX, 0, maxZ),
                new Vector3(maxX, 0, minZ),
            };

            return vertices;
        }

        void AddCircleCollision(object pos)
        {
            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string radiusStr = (texts[0] as InputDialog.StringItem).text;
                string segmentStr = (texts[1] as InputDialog.StringItem).text;
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);
                if (!suc)
                {
                    return false;
                }
                float segment;
                suc = Utils.ParseFloat(segmentStr, out segment);
                if (!suc)
                {
                    return false;
                }
                if (radius <= 0 || segment <= 2)
                {
                    return false;
                }

                var vertices = CreateCircleVertices((Vector3)pos, radius, (int)segment);

                var layer = mLogic.layer;
                var action = new ActionAddRegion(mLogic.layerID, Map.currentMap.nextCustomObjectID, vertices, true, new Color(0.5f, 0.5f, 0.5f, 0.6f), new Color(1, 0, 0, 0.6f), RegionType.Outer, 0, layer.layerData.defaultRegionMaterial);
                ActionManager.instance.PushAction(action);
                return true;
            };

            var window = EditorWindow.GetWindow<InputDialog>("Input Circle Parameters");
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;

            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("Radius", "", "10"),
                new InputDialog.StringItem("Segment", "", "20"),
                };
            window.Show(items, onClickAdd);
        }

        void AddRectangleCollision(object pos)
        {
            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string widthStr = (texts[0] as InputDialog.StringItem).text;
                string heightStr = (texts[1] as InputDialog.StringItem).text;
                float width;
                bool suc = Utils.ParseFloat(widthStr, out width);
                if (!suc)
                {
                    return false;
                }
                float height;
                suc = Utils.ParseFloat(heightStr, out height);
                if (!suc)
                {
                    return false;
                }
                if (width <= 0 || height <= 0)
                {
                    return false;
                }

                var vertices = CreateRectangleVertices((Vector3)pos, width, height);
                var action = new ActionAddRegion(mLogic.layerID, Map.currentMap.nextCustomObjectID, vertices, true, new Color(0.5f, 0.5f, 0.5f, 0.6f), new Color(1, 0, 0, 0.6f), RegionType.Outer, 0, mLogic.layer.layerData.defaultRegionMaterial);
                ActionManager.instance.PushAction(action);

                return true;
            };

            var window = EditorWindow.GetWindow<InputDialog>("Input Rectangle Parameters");
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("Width", "", "45"),
                new InputDialog.StringItem("Height", "", "45"),
                };
            window.Show(items, onClickAdd);
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                var layer = mLogic.layer;
                var operation = (RegionOperation)EditorGUILayout.EnumPopup("Operation", mLogic.operation);
                if (operation != mLogic.operation)
                {
                    mLogic.operation = operation;
                    mAddedVertices.Clear();
                    mDisplayedVertices = new Vector3[0];
                }

                float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "显示的顶点大小"), layer.displayVertexRadius);
                if (radius != layer.displayVertexRadius)
                {
                    layer.SetVertexDisplayRadius(radius);
                }

                if (mLogic.operation == RegionOperation.CreateRegion)
                {
                    mSnapToVertex = EditorGUILayout.ToggleLeft(new GUIContent("Snap To Vertex", "创建顶点的时候吸附到重合的顶点上"), mSnapToVertex);
                }
                DrawColorField();
                
                var selections = mSelectionManager.selections;
                if (selections.Count == 1)
                {
                    var region = Map.currentMap.FindObject(selections[0].regionID) as RegionData;
                    if (region.type == RegionType.Inner)
                    {
                        var newMtl = EditorGUILayout.ObjectField(new GUIContent("Region Material", "区域mesh的材质"), region.material, typeof(Material), false, null) as Material;
                        if (newMtl != region.material)
                        {
                            layer.SetRegionMaterial(region.id, newMtl);
                        }
                    }
                }
                else
                {
                    layer.layerData.defaultRegionMaterial = EditorGUILayout.ObjectField(new GUIContent("Region Material", "区域mesh的材质"), layer.layerData.defaultRegionMaterial, typeof(Material), false, null) as Material;
                }
                var newBorderMtl = EditorGUILayout.ObjectField(new GUIContent("Border Material", "边界的材质"), layer.layerData.borderMaterial, typeof(Material), false, null) as Material;
                if (newBorderMtl != layer.layerData.borderMaterial)
                {
                    layer.SetBorderMaterial(newBorderMtl);
                }

                if (selections.Count == 1)
                {
                    var region = Map.currentMap.FindObject(selections[0].regionID) as RegionData;
                    region.type = (RegionType)EditorGUILayout.EnumPopup(new GUIContent("Type", "边框的类型,inner类型可以设置区域id和生成mesh"), region.type);
                    if (region.type == RegionType.Inner)
                    {
                        region.number = EditorGUILayout.IntField(new GUIContent("ID", "区域的id,需要唯一"), region.number);
                    }
                }

                var layerData = layer.layerData;

                EditorGUILayout.BeginVertical("GroupBox");
                layer.generateBorderMesh = EditorGUILayout.ToggleLeft(new GUIContent("Generate Border Mesh", "是否生成边界mesh"), layer.generateBorderMesh);
                layerData.borderMinX = EditorGUILayout.FloatField(new GUIContent("Border Area Minimum X", "border的左下角x坐标"), layerData.borderMinX);
                layerData.borderMinZ = EditorGUILayout.FloatField(new GUIContent("Border Area Minimum Z", "border的左下角z坐标"), layerData.borderMinZ);
                layerData.borderMaxX = EditorGUILayout.FloatField(new GUIContent("Border Area Maximum X", "border的右上角x坐标"), layerData.borderMaxX);
                layerData.borderMaxZ = EditorGUILayout.FloatField(new GUIContent("Border Area Maximum Z", "border的右上角z坐标"), layerData.borderMaxZ);
                EditorGUILayout.EndVertical();

                EditorGUILayout.BeginVertical("GroupBox");
                layerData.generateBorderLine = EditorGUILayout.ToggleLeft(new GUIContent("Generate Border Line", "是否生成边界线"), layerData.generateBorderLine);
                layerData.borderLineMaterial = EditorGUILayout.ObjectField(new GUIContent("Border Line Material", "边界线的材质"), layerData.borderLineMaterial, typeof(Material), false, null) as Material;
                layerData.borderLineWidth = EditorGUILayout.FloatField(new GUIContent("Border Line Width", "边界线的宽度"), layerData.borderLineWidth);
                EditorGUILayout.EndVertical();

                bool visible = EditorGUILayout.ToggleLeft("Show Border Line Mesh", layerData.showBorderLineMesh);
                if (visible != layerData.showBorderLineMesh)
                {
                    layer.ShowBorderLineMesh(visible);
                }
                visible = EditorGUILayout.ToggleLeft("Show Region Mesh", layerData.showRegionMesh);
                if (visible != layerData.showRegionMesh)
                {
                    layer.ShowRegionMesh(visible);
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Combine Two Vertices", "选中两个重合的顶点,点击后合并成一个顶点")))
                {
                    CombineVertex();
                }

                if (GUILayout.Button(new GUIContent("Create Region Mesh", "创建区域mesh")))
                {
                    var objects = layer.layerData.objects;
                    foreach (var p in objects)
                    {
                        var region = p.Value as RegionData;
                        if (region.type == RegionType.Inner)
                        {
                            RegionData outerRegion = null;
                            bool valid = layer.FindOuterRegion(region, out outerRegion);
                            if (valid)
                            {
                                CreateInnerOuterMesh(region, outerRegion);
                            }
                        }
                    }   

                    if (layer.generateBorderMesh)
                    {
                        CreateBorderMesh();
                    }
                    else
                    {
                        layer.SetBorderMesh(new Vector3[0], new int[0]);
                    }

                    if (layerData.generateBorderLine)
                    {
                        if (layerData.borderLineMaterial != null)
                        {
                            layer.CreateBorderLineMesh();
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Select a border line material first!", "OK");
                        }
                    }
                    else
                    {
                        layer.ClearBorderLineMesh();
                    }
                }
                
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button(new GUIContent("Generate Runtime Assets", "生成游戏运行时模型数据,包括prefab,mesh,material")))
                {
                    string msg = layer.CheckValidation();
                    if (string.IsNullOrEmpty(msg))
                    {
                        layer.GenerateAssets(SLGMakerEditor.instance.exportFolder);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", msg, "OK");
                    }
                }

                if (mLogic.operation == RegionOperation.EditRegionOutline)
                {
                    EditorGUILayout.LabelField("Add Vertex: Ctrl + Mouse Left Button");
                    EditorGUILayout.LabelField("Remove Vertex: Ctrl + Shift + Mouse Left Button");
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Width", layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", layer.GetTotalHeight().ToString());
                EditorGUILayout.EndVertical();
            }
        }

        bool HasSelectedInnerRegion()
        {
            var selections = mSelectionManager.selections;
            if (selections.Count == 1)
            {
                var region = Map.currentMap.FindObject(selections[0].regionID) as RegionData;
                return region.type == RegionType.Inner;
            }
            return false;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (currentEvent.type == EventType.MouseUp)
            {
                if (currentEvent.button == 0)
                {
                    //press left key
                    mLeftButtonDown = false;
                    mCanCreate = true;

                    if (mCollisionMover != null)
                    {
                        mCollisionMover.Stop(pos);
                        mCollisionMover = null;
                    }
                    if (mVertexMover != null)
                    {
                        mVertexMover.Stop(pos);
                        mVertexMover = null;
                    }
                }

                mPickWhenMovingCollision = true;
                mMover.Reset();
            }

            if (Event.current.button == 1 && Event.current.type == EventType.MouseDown)
            {
                if (mAddedVertices.Count == 0)
                {
                    GenericMenu menu = new GenericMenu();
                    menu.AddItem(new GUIContent("Select Region"), false, SelectRegion, pos);
                    menu.AddItem(new GUIContent("Delete Region"), false, DeleteCollision);
                    menu.AddItem(new GUIContent("Create Circle Collision"), false, AddCircleCollision, pos);
                    menu.AddItem(new GUIContent("Create Rectangle Collision"), false, AddRectangleCollision, pos);
                    menu.ShowAsContext();
                }

                //press right key
                mCanCreate = true;
                bool created = AddCollision();
                if (created)
                {
                    mLogic.operation = RegionOperation.EditRegionOutline;
                }

                SceneView.RepaintAll();
            }

            if (mLogic.operation == RegionOperation.CreateRegion)
            {
                if (mSnapToVertex)
                {
                    pos = GetSnappedVertexPosition(pos);
                }
                if (mLeftButtonDown && mCanCreate)
                {
                    mCanCreate = false;

                    mAddedVertices.Add(pos);
                    if (mAddedVertices.Count == 1)
                    {
                        mAddedVertices.Add(pos);
                    }
                    mDisplayedVertices = mAddedVertices.ToArray();
                    SceneView.RepaintAll();
                }

                if (currentEvent.type == EventType.MouseMove)
                {
                    if (mAddedVertices.Count > 0)
                    {
                        mAddedVertices[mAddedVertices.Count - 1] = pos;
                        mDisplayedVertices[mDisplayedVertices.Length - 1] = pos;
                    }

                    SceneView.RepaintAll();
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operation == RegionOperation.EditRegionOutline)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
                {
                    if (currentEvent.control == false)
                    {
                        PickAllVertex(pos);
                    }
                    else
                    {
                        if (currentEvent.shift)
                        {
                            PickAllVertex(pos);
                            RemoveVertex();
                        }
                        else
                        {
                            AddVertex(pos);
                        }
                    }
                }

                if (mLeftButtonDown && currentEvent.control == false && currentEvent.shift == false)
                {
                    MoveVertex(pos);
                }

                HandleUtility.AddDefaultControl(0);
            }
            else if (mLogic.operation == RegionOperation.MoveRegion)
            {
                if (mLeftButtonDown)
                {
                    if (mPickWhenMovingCollision == true)
                    {
                        mPickWhenMovingCollision = false;
                        PickAllVertex(pos);
                    }
                    MoveCollision(pos);
                }

                HandleUtility.AddDefaultControl(0);
            }

            Handles.DrawPolyLine(mDisplayedVertices);

            mLogic.layer.UpdateColor(mLogic.layer.displayType);
        }

        Vector3 GetSnappedVertexPosition(Vector3 originalPos)
        {
            int hitRegionID;
            int hitVertexIndex;
            Pick(originalPos, out hitRegionID, out hitVertexIndex);
            if (hitRegionID != 0)
            {
                var regionData = Map.currentMap.FindObject(hitRegionID) as RegionData;
                var pos = regionData.GetVertexPos(PrefabOutlineType.NavMeshObstacle, hitVertexIndex);
                return pos;
            }
            return originalPos;
        }

        void Pick(Vector3 pos, out int objectID, out int vertexIndex)
        {
            int hitVertexIdx = -1;
            int hitRegionID = 0;
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as RegionData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                float sqrr = collisionData.displayRadius * collisionData.displayRadius;
                for (int i = 0; i < vertices.Count; ++i)
                {
                    var d = vertices[i] - pos;
                    if (d.sqrMagnitude <= sqrr)
                    {
                        hitVertex = i;
                        break;
                    }
                }

                if (hitVertex >= 0)
                {
                    hitVertexIdx = hitVertex;
                    hitRegionID = collisionData.id;
                }

                if (hitRegionID != 0)
                {
                    return true;
                }

                return false;
            };

            mLogic.layer.Traverse(func);

            objectID = hitRegionID;
            vertexIndex = hitVertexIdx;
        }

        void PickAllVertex(Vector3 pos)
        {
            mSelectionManager.ClearSelections();
            System.Func<IMapObjectData, bool> func = (IMapObjectData data) =>
            {
                var collisionData = data as RegionData;

                int hitVertex = -1;
                var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                for (int i = 0; i < vertices.Count; ++i)
                {
                    if (Utils.IsHitRectangle(pos, vertices[i], collisionData.displayRadius))
                    {
                        hitVertex = i;
                        break;
                    }
                }

                if (hitVertex >= 0)
                {
                    mSelectionManager.AddSelection(collisionData.id, hitVertex);
                }
                return false;
            };

            mLogic.layer.Traverse(func);
        }

        void OnMoveCollision(int dataID)
        {
            SceneView.RepaintAll();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void MoveCollision(Vector3 pos)
        {
            var selections = mSelectionManager.selections;
            mMover.Update(pos);
            var offset = mMover.GetDelta();
            for (int i = 0; i < selections.Count; ++i)
            {
                if (mCollisionMover == null)
                {
                    mCollisionMover = new RegionMover(mLogic.layerID, selections, pos);
                }

                UnityEngine.Debug.Assert(selections[i].vertexIndex >= 0);
                var collisionData = Map.currentMap.FindObject(selections[i].regionID) as RegionData;

                mLogic.layer.MoveObject(PrefabOutlineType.NavMeshObstacle, selections[i].regionID, offset);
                mLogic.layer.MoveObject(PrefabOutlineType.ObjectPlacementObstacle, selections[i].regionID, offset);

                SceneView.RepaintAll();
            }
        }

        void OnMoveVertex(int dataID, int vertexIndex)
        {
            SceneView.RepaintAll();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void MoveVertex(Vector3 pos)
        {
            var selections = mSelectionManager.selections;
            if (selections.Count > 0)
            {
                if (mVertexMover == null)
                {
                    mVertexMover = new RegionVertexMover(mLogic.layerID, selections, pos, mLogic.layer.displayType);
                }

                mMover.Update(pos);
            }
            for (int i = 0; i < selections.Count; ++i)
            {
                UnityEngine.Debug.Assert(selections[i].vertexIndex >= 0);

                var collisionData = Map.currentMap.FindObject(selections[i].regionID) as RegionData;
                var oldPos = collisionData.GetOutlineVertices(mLogic.layer.displayType)[selections[i].vertexIndex];
                var newPos = mMover.GetDelta() + oldPos;

                mLogic.layer.SetVertexPosition(mLogic.layer.displayType, selections[i].regionID, selections[i].vertexIndex, newPos);
            }

            if (selections.Count > 0)
            {
                SceneView.RepaintAll();
            }
        }

        void AddVertex(Vector3 localPos)
        {
            var selections = mSelectionManager.selections;
            if (selections.Count > 0)
            {
                var collisionData = Map.currentMap.FindObject(selections[0].regionID) as RegionData;
                var idx = Utils.FindNearestEdgeDistance(localPos, collisionData.GetOutlineVertices(mLogic.layer.displayType));
                InsertVertex(idx, localPos);
            }
        }

        void OnInsertVertex(int dataID, int index)
        {
            mSelectionManager.UpdateSelectionVertex(dataID, index);
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var selections = mSelectionManager.selections;
            var action = new ActionAddRegionVertex(mLogic.layerID, selections[0].regionID, index, localPos, mLogic.layer.displayType);
            ActionManager.instance.PushAction(action);
        }

        void OnDeleteCollision(int dataID)
        {
            mSelectionManager.RemoveSelection(dataID);
            Repaint();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void DeleteCollision()
        {
            var selections = mSelectionManager.selections;
            if (selections.Count > 0)
            {
                var actions = new CompoundAction("delete regions");
                List<RegionSelection> tempSelectedRegions = new List<RegionSelection>();
                tempSelectedRegions.AddRange(selections);
                for (int i = 0; i < tempSelectedRegions.Count; ++i)
                {
                    var act = new ActionRemoveRegion(mLogic.layerID, tempSelectedRegions[i].regionID);
                    actions.Add(act);
                }
                ActionManager.instance.PushAction(actions);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a collision first!", "OK");
            }
        }

        void OnRemoveVertex(int dataID, int index)
        {
            mSelectionManager.ClearSelections();
            Repaint();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void RemoveVertex()
        {
            CompoundAction actions = null;
            var selections = mSelectionManager.selectionsCopy;
            for (int i = 0; i < selections.Count; ++i)
            {
                if (selections[i].vertexIndex >= 0)
                {
                    if (actions == null)
                    {
                        actions = new CompoundAction("remove vertex");
                    }
                    var collisionData = Map.currentMap.FindObject(selections[i].regionID) as RegionData;
                    var vertices = collisionData.GetOutlineVertices(mLogic.layer.displayType);
                    if (vertices.Count > 3)
                    {
                        var action = new ActionRemoveRegionVertex(mLogic.layerID, selections[i].regionID, selections[i].vertexIndex, mLogic.layer.displayType);
                        actions.Add(action);
                    }
                }
            }
            if (actions != null)
            {
                ActionManager.instance.PushAction(actions);
            }
        }

        void OnSelectionChange(int regionID, bool selected)
        {
            mLogic.layer.SetSelected(regionID, selected);
            Repaint();
        }

        void OnAddCollision(int dataID)
        {
            var data = Map.currentMap.FindObject(dataID) as RegionData;
            mSelectionManager.SetSelection(data.id, 0);
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
            Repaint();
        }

        void RemoveDuplicatedVertices(List<Vector3> vertices)
        {
            float esp = 0.01f;
            for (int i = vertices.Count - 1; i >= 0; --i)
            {
                for (int k = i - 1; k >= 0; --k)
                {
                    if (Utils.Approximately(vertices[i].x, vertices[k].x, esp) &&
                        Utils.Approximately(vertices[i].y, vertices[k].y, esp) &&
                        Utils.Approximately(vertices[i].z, vertices[k].z, esp))
                    {
                        vertices.RemoveAt(i);
                        break;
                    }
                }
            }
        }

        bool AddCollision()
        {
            bool created = mAddedVertices.Count > 3;
            if (created)
            {
                var layer = mLogic.layer;

                RemoveDuplicatedVertices(mAddedVertices);
                //mAddedVertices.RemoveAt(mAddedVertices.Count - 1);

                var dataID = Map.currentMap.nextCustomObjectID;
                var action = new ActionAddRegion(mLogic.layerID, dataID, mAddedVertices, true, new Color(0.5f, 0.5f, 0.5f, 0.6f), new Color(1, 0, 0, 0.6f), RegionType.Outer, 0, layer.layerData.defaultRegionMaterial);
                ActionManager.instance.PushAction(action);
            }

            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];

            return created;
        }

        void OnOutlineChanged(int dataID, PrefabOutlineType type)
        {
            SceneView.RepaintAll();
            Repaint();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void DrawColorField()
        {
            var selections = mSelectionManager.selections;
            if (selections.Count == 1)
            {
                var region = mLogic.layer.GetRegion(selections[0].regionID);
                if (region.type == RegionType.Inner)
                {
                    var newColor = EditorGUILayout.ColorField("Inner Color", region.innerColor);
                    var newOuterColor = EditorGUILayout.ColorField("Outer Color", region.outerColor);
                    if (region.innerColor != newColor)
                    {
                        mLogic.layer.SetInnerColor(region.id, newColor);
                    }
                    if (region.outerColor != newOuterColor)
                    {
                        mLogic.layer.SetOuterColor(region.id, newOuterColor);
                    }
                }
            }
        }

        void SelectRegion(object pos)
        {
            Vector3 worldPos = (Vector3)pos;
            var region = mLogic.layer.FindRegion(worldPos);
            if (region != null)
            {
                mSelectionManager.SetSelection(region.id, 0);
                Repaint();
            }
        }

        RegionLayerLogic mLogic;
        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
        MouseMover mMover = new MouseMover();
        bool mLeftButtonDown;
        bool mCanCreate = true;
        bool mPickWhenMovingCollision = true;
        RegionSelectionManager mSelectionManager;
        RegionMover mCollisionMover;
        RegionVertexMover mVertexMover;
        //鼠标选取顶点的时候是否吸附到radius半径内的已有顶点上
        bool mSnapToVertex = true;
    }
}

#endif