﻿using System.Collections.Generic;
using UnityEngine;

public class ParticleSystemManager : MonoBehaviour
{
    public static ParticleSystemManager Instance { get; private set; }

    private List<ParticleSystem> particleSystems = new List<ParticleSystem>();
    private float lastCheckTime;
    private float frameRateCheckInterval = 1f;
    private int[] frameRateThresholds = { 20, 24, 30, 40, 50 };
    private int[] closePercentages = { 100, 80, 60, 40, 20 };
    private int currentCloseLevel = 0;
    private Queue<float> frameRates = new Queue<float>();
    private float frameRateSum = 0;
    private int upgradeCheckCounter = 0;
    private int upgradeCheckInterval = 10;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Update()
    {
        float frameRate = 1f / Time.deltaTime;
        frameRateSum += frameRate;
        frameRates.Enqueue(frameRate);

        if (frameRates.Count > upgradeCheckInterval)
        {
            frameRateSum -= frameRates.Dequeue();
        }

        if (Time.time - lastCheckTime > frameRateCheckInterval)
        {
            lastCheckTime = Time.time;
            CheckFrameRate();
        }
    }

    private void CheckFrameRate()
    {
        float averageFrameRate = frameRateSum / frameRates.Count;

        if (averageFrameRate < frameRateThresholds[currentCloseLevel])
        {
            if (currentCloseLevel < frameRateThresholds.Length - 1)
            {
                currentCloseLevel++;
                AdjustParticleSystems();
            }
        }
        else
        {
            if (currentCloseLevel > 0)
            {
                if (upgradeCheckCounter >= upgradeCheckInterval)
                {
                    currentCloseLevel--;
                    AdjustParticleSystems();
                }
            }
        }
    }

    private void AdjustParticleSystems()
    {
        float closePercentage = closePercentages[currentCloseLevel] / 100f;
        foreach (var ps in particleSystems)
        {
            SetAvitve(ps, closePercentage);
        }
    }

    private void SetAvitve(ParticleSystem ps, float closePercentage)
    {
        if (Random.value < closePercentage)
        {
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            if (renderer != null)
            {
                renderer.enabled = false;
            }
        }
        else
        {
            var renderer = ps.GetComponent<ParticleSystemRenderer>();
            if (renderer != null)
            {
                renderer.enabled = true;
            }
        }
    }

    public void ControlParticleSystem(GameObject instance)
    {
        if (instance.GetComponentInChildren<ParticleSystem>())
        {
            if (instance.GetComponent<ParticleSystemSign>() == null)
            {
                instance.AddComponent<ParticleSystemSign>();
            }
        }
    }

    public void AddParticleSystem(ParticleSystem ps)
    {
        if (particleSystems.Contains(ps))
        {
            Debug.LogError("重复例子：" + ps.name);
            return;
        }

        particleSystems.Add(ps);
        float closePercentage = closePercentages[currentCloseLevel] / 100f;
        SetAvitve(ps, closePercentage);
    }

    public void RemoveParticleSystem(ParticleSystem ps)
    {
        particleSystems.Remove(ps);
    }
}