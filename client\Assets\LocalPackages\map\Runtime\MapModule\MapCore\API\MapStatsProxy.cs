﻿ 



 
 

//#define MAP_STATS_ON

using System.Diagnostics;
using UnityEngine;

namespace TFW.Map
{
    //统计地图各种运行时信息,主要用于验证OKR进度
    public static class MapStats
    {
        public static void BeginLoading()
        {
#if MAP_STATS_ON
            mLoadingTimer.Start();
#endif
        }

        public static void EndLoading()
        {
#if MAP_STATS_ON
            var elapsedTime = mLoadingTimer.Stop();
            mFirstLoadingTime += elapsedTime;
#endif
        }

        public static void BeginMapUpdate()
        {
#if MAP_STATS_ON
            mUpdateTimer.Start();
#endif
        }

        public static void EndMapUpdate()
        {
#if MAP_STATS_ON
            var elapsedTime = mUpdateTimer.Stop();
            mUpdateTimePerFrame += elapsedTime;
#endif
        }

        public static void BeginFrontLayerUpdate()
        {
#if MAP_STATS_ON
            mFrontLayerTimer.Start();
#endif
        }

        public static void EndFrontLayerUpdate()
        {
#if MAP_STATS_ON
            var elapsedTime = mFrontLayerTimer.Stop();
            mFrontLayerUpdateTimerPerFrame += elapsedTime;
#endif
        }

        public static void ResetUpdateTime()
        {
#if MAP_STATS_ON
            mUpdateTimePerFrame = 0;
#endif
        }

        public static void ResetLoadingTime()
        {
#if MAP_STATS_ON
            mFirstLoadingTime = 0;
#endif
        }

        public static void ResetFrontLayerUpdateTime()
        {
#if MAP_STATS_ON
            mFrontLayerUpdateTimerPerFrame = 0;
#endif
        }

        public static double firstLoadingTime { get; }
        public static double updateTimePerFrame { get; }
        public static double frontLayerUpdateTimePerFrame { get; }

        static StopWatchWrapper mLoadingTimer = new StopWatchWrapper();
        static StopWatchWrapper mUpdateTimer = new StopWatchWrapper();
        static StopWatchWrapper mFrontLayerTimer = new StopWatchWrapper();
        static double mFrontLayerUpdateTimerPerFrame;
        static double mFirstLoadingTime;
        static double mUpdateTimePerFrame;
    }

    class MapStatsProxy : MonoBehaviour
    {
        public double firstLoadingTime = 0;
        public double updateTimePerFrame = 0;
        public bool showGrid = false;
    }
}
