﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class MapCameraBorderClampByCurve : MonoBehaviour
    {
        float mLookAtMinX;
        float mLookAtMinZ;
        float mLookAtMaxX;
        float mLookAtMaxZ;
        float mBounceRange = 0;
        bool mUseCustomRange = false;
        public BorderClampConfig xClampConfig;
        public BorderClampConfig zClampConfig;

        public void SetLookAtRange(float lookAtMinX, float lookAtMinZ, float lookAtMaxX, float lookAtMaxZ)
        {
            mLookAtMinX = lookAtMinX;
            mLookAtMaxX = lookAtMaxX;
            mLookAtMinZ = lookAtMinZ;
            mLookAtMaxZ = lookAtMaxZ;
            mUseCustomRange = true;
        }

        public Rect GetLookAtRange()
        {
            return new Rect(mLookAtMinX, mLookAtMinZ, mLookAtMaxX - mLookAtMinX, mLookAtMaxZ - mLookAtMinZ);
        }

        public void ClearLookAtRange()
        {
            mLookAtMinX = 0;
            mLookAtMaxX = 0;
            mLookAtMinZ = 0;
            mLookAtMaxZ = 0;
            mUseCustomRange = false;
        }

        public void SetBounceRange(float bounceRange)
        {
            mBounceRange = bounceRange;
        }

        public float GetBounceRange()
        {
            return mBounceRange;
        }

        //将相机限制在某个范围内移动
        public Vector3 ClampPosition(Vector3 newCameraPos, bool clampBorder, out bool clamped)
        {
            clamped = false;
            var camera = MapCameraMgr.MapCameraRoot;

            Vector3 min;
            Vector3 max;

            var setting = MapCameraMgr.cameraSetting;

            if (setting.enableCalculateEdgeSize)
            {
                CalculateCameraMoveRange(newCameraPos, clampBorder, out min, out max);
            }
            else
            {
                GetCameraMoveRangeByConfig(newCameraPos, clampBorder, out min, out max);
            }

            float minCameraHeight;
            if (mUseCustomRange)
            {
                minCameraHeight = Mathf.Max(MapCameraMgr.cameraSetting.cameraMinHeight, MapCameraMgr.currentMinimumHeight);
            }
            else
            {
                minCameraHeight = MapCameraMgr.currentMinimumHeight;
            }
            float maxCameraHeight = MapCameraMgr.cameraSetting.cameraMaxHeight;

            float dminY = newCameraPos.y - minCameraHeight;
            if (Utils.LT(dminY, 0))
            {
                //越界了
                var dir = camera.transform.forward;
                float xRot = camera.transform.eulerAngles.x;
                float distance = Mathf.Abs(dminY) / Mathf.Sin(xRot * Mathf.Deg2Rad);
                newCameraPos = newCameraPos - dir * distance;

                Debug.Assert(Mathf.Approximately(newCameraPos.y, minCameraHeight));
            }

            float dmaxY = newCameraPos.y - maxCameraHeight;
            if (Utils.GT(dmaxY, 0))
            {
                //越界了
                var dir = camera.transform.forward;
                float xRot = camera.transform.eulerAngles.x;
                float distance = Mathf.Abs(dmaxY) / Mathf.Sin(xRot * Mathf.Deg2Rad);
                newCameraPos = newCameraPos + dir * distance;

                Debug.Assert(Mathf.Approximately(newCameraPos.y, maxCameraHeight));
            }

            var map = Map.currentMap;

            var clamp = map.data.lookAtAreaClamp;
            if (clamp != null)
            {
                var oldViewCenter = map.viewCenter;
                var newViewCenter = map.GetViewCenterFromCamera(camera.transform.forward, newCameraPos);

                Vector3 newPos;
                bool hit = clamp.ClampAndSlide(oldViewCenter, newViewCenter, out newPos);
                if (hit)
                {
                    newPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(newPos.x, newPos.z, newCameraPos.y);
                    newCameraPos.x = newPos.x;
                    newCameraPos.z = newPos.z;
                }
            }
            else
            {
                if (newCameraPos.x < min.x || newCameraPos.x > max.x ||
                    newCameraPos.z < min.z || newCameraPos.z > max.z)
                {
                    clamped = true;
                }
                newCameraPos.x = Mathf.Clamp(newCameraPos.x, min.x, max.x);
                newCameraPos.z = Mathf.Clamp(newCameraPos.z, min.z, max.z);
            }

            return newCameraPos;
        }

        void CalculateCameraMoveRange(Vector3 newCameraPos, bool clampBorder, out Vector3 min, out Vector3 max)
        {
            float minCameraHeight = MapCameraMgr.currentMinimumHeight;
            float maxCameraHeight = MapCameraMgr.cameraSetting.cameraMaxHeight;
            float cameraHeight = Mathf.Clamp(newCameraPos.y, minCameraHeight, maxCameraHeight);

            float rangeWidth = 0;
            float rangeHeight = 0;
            var map = Map.currentMap;
            if (!mUseCustomRange)
            {
                rangeWidth = map.mapWidth;
                rangeHeight = map.mapHeight;
                var mapOrigin = map.origin;
                min = map.CalculateCameraPositionFromLookAtPosition(mapOrigin.x, mapOrigin.z, cameraHeight);
                max = map.CalculateCameraPositionFromLookAtPosition(mapOrigin.x + map.mapWidth, mapOrigin.z + map.mapHeight, cameraHeight);
            }
            else
            {
                rangeWidth = mLookAtMaxX - mLookAtMinX;
                rangeHeight = mLookAtMaxZ - mLookAtMinZ;
                min = map.CalculateCameraPositionFromLookAtPosition(mLookAtMinX, mLookAtMinZ, cameraHeight);
                max = map.CalculateCameraPositionFromLookAtPosition(mLookAtMaxX, mLookAtMaxZ, cameraHeight);
            }

            if (clampBorder)
            {
                var setting = MapCameraMgr.cameraSetting;
                var camera = map.camera.firstCamera;
                float fov = map.GetCameraFOVAtHeight(cameraHeight);
                var viewport = map.GetViewportRect(camera, fov, new Vector3(newCameraPos.x, cameraHeight, newCameraPos.z));

                float edgeSizeX = viewport.width * 0.5f;
                float edgeSizeZ = viewport.height * 0.5f;
                edgeSizeX = Mathf.Clamp(edgeSizeX, 0, rangeWidth * 0.5f);
                edgeSizeZ = Mathf.Clamp(edgeSizeZ, 0, rangeHeight * 0.5f);
                min.x = min.x + edgeSizeX - setting.edgeXOffset;
                max.x = max.x - edgeSizeX + setting.edgeXOffset;
                min.z = min.z + edgeSizeZ - setting.edgeZOffset;
                max.z = max.z - edgeSizeZ + setting.edgeZOffset;
            }
        }

        void GetCameraMoveRangeByConfig(Vector3 newCameraPos, bool clampBorder, out Vector3 min, out Vector3 max)
        {
            float minCameraHeight = MapCameraMgr.currentMinimumHeight;
            float maxCameraHeight = MapCameraMgr.cameraSetting.cameraMaxHeight;
            float cameraHeight = Mathf.Clamp(newCameraPos.y, minCameraHeight, maxCameraHeight);

            var map = Map.currentMap;
            float rangeWidth = 0;
            float rangeHeight = 0;
            if (!mUseCustomRange)
            {
                rangeWidth = map.mapWidth;
                rangeHeight = map.mapHeight;
                var origin = map.origin;
                min = map.CalculateCameraPositionFromLookAtPosition(origin.x - mBounceRange, origin.z - mBounceRange, cameraHeight);
                max = map.CalculateCameraPositionFromLookAtPosition(origin.x + map.mapWidth + mBounceRange, origin.z + map.mapHeight + mBounceRange, cameraHeight);
            }
            else
            {
                rangeWidth = mLookAtMaxX - mLookAtMinX;
                rangeHeight = mLookAtMaxZ - mLookAtMinZ;
                min = map.CalculateCameraPositionFromLookAtPosition(mLookAtMinX, mLookAtMinZ, cameraHeight);
                max = map.CalculateCameraPositionFromLookAtPosition(mLookAtMaxX, mLookAtMaxZ, cameraHeight);
            }

            if (clampBorder)
            {
                float t = (cameraHeight - minCameraHeight) / (maxCameraHeight - minCameraHeight);
                if (xClampConfig != null)
                {
                    var edgeSize = xClampConfig.curve.Evaluate(cameraHeight);
                    //edge size最多只能到地图的一半, 这时候相机可移动范围就限制在一个点上了
                    edgeSize = Mathf.Clamp(edgeSize, 0, rangeWidth * 0.5f);
                    float leftOffset = Mathf.Lerp(xClampConfig.bottomMinOffset, xClampConfig.bottomMaxOffset, t);
                    float rightOffset = Mathf.Lerp(xClampConfig.topMinOffset, xClampConfig.topMaxOffset, t);
                    min.x = min.x + edgeSize + leftOffset;
                    max.x = max.x - edgeSize + rightOffset;
                }

                if (zClampConfig != null)
                {
                    var zEdgeSize = zClampConfig.curve.Evaluate(cameraHeight);
                    //edge size最多只能到地图的一半, 这时候相机可移动范围就限制在一个点上了
                    zEdgeSize = Mathf.Clamp(zEdgeSize, 0, rangeHeight * 0.5f);
                    float bottomOffset = Mathf.Lerp(zClampConfig.bottomMinOffset, zClampConfig.bottomMaxOffset, t);
                    float topOffset = Mathf.Lerp(zClampConfig.topMinOffset, zClampConfig.topMaxOffset, t);
                    float viewOffset = Mathf.Lerp(zClampConfig.viewZOffsetMin, zClampConfig.viewZOffsetMax, t);
                    min.z = min.z + zEdgeSize + bottomOffset - viewOffset;
                    max.z = max.z - zEdgeSize + topOffset - viewOffset;
                }
            }
        }
    }
}