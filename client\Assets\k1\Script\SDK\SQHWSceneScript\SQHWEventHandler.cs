﻿
using System;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using UnityEngine.UI;
using UnityEngine;
using UnityEditor;
using maxsdk;
using maxsdkSQHW;
using maxsdkHWExt;
using System.Collections.Generic;
using System.Linq;


/**
 *  当前脚本为接口调用示例，并作为SQHW Demo Scene的绑定脚本
 */

public class SQHWEventHandler : SQHWMaxSDKCallBack
{

    public GameObject obj;


    /*
     * 【调用示例】 挂载脚本，设置回调监听者对象
     */
    void Start()
    {
        // 父类实现了MaxSDKListener监听方法，所以将当前实例设置为监听者
        MaxSDK.GetInstance().SetListener(this);

        // Demo界面配置，可忽略
        ConfigPlatformUI();
    }


    /* ------------------------------------  必接接口 -------------------------------------- */

    /*
     * 【调用示例】 初始化
     * iOS及Android的初始化在原生端处理，且时机早于Unity初始化；当前方法用于获取到初始化结果
    */
    public void OnClickGetInitResult()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("提示", "点击了获取初始化结果", "Yes", "No");
# endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().GetInitResult();
#endif
    }

    /*
     * 【调用示例】 自动登录
     */
    public void OnClickAutoLogin()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("提示", "点击了自动登录", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().AutoLogin();
#endif
    }


    /*
     * 【调用示例】 退出登录
     */
    public void OnClickLogout()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("提示", "点击了退出登录", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().Logout();
#endif
    }


    /*
     * 【调用示例】 设置用户中心内切换账户回调
     * 当用户在用户中心里面切换账号时,会触发 OnChangeAccountSuccess 或 OnChangeAccountFail 回调，必须调用
     */
    public void OnClickSetChangeAccountCallback()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("提示", "点击了设置切换账号回调", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().SetChangeAccountCallback();
#endif
    }


    /*
     * 【调用示例】 上报创角信息
     */
    public void OnClickCreateRole()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击创角上报", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDKHWRoleInfo roleInfo = new()
        {
            serverId = "10004",             // 服务器ID
            roleId = "*****************",   // 角色ID
            roleName = "Rora",              // 角色名称
        };
        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            roleInfo.serverId = obj["serverId"].ToString() ?? roleInfo.serverId;
            roleInfo.roleId = obj["roleId"].ToString() ?? roleInfo.roleId;
            roleInfo.roleName = obj["roleName"].ToString() ?? roleInfo.roleName;
        }
        MaxSDK.GetInstance().ReportCreateRole(roleInfo);
#endif
    }


    /*
     * 【调用示例】 上报角色入服信息
     */
    public void OnClickEnterServer()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击入服上报", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDKHWRoleInfo roleInfo = new()
        {
            serverId = "10004",             // 服务器ID
            roleId = "*****************",   // 角色ID
            roleName = "Rora",              // 角色名称
            roleLevel = 1,                  // 角色等级
            vipLevel = 10,                  // 玩家 VIP 等级
            roleDiamonds = 1,
            castleLevel = 1
        };
        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            roleInfo.serverId = obj["serverId"].ToString() ?? roleInfo.serverId;
            roleInfo.roleId = obj["roleId"].ToString() ?? roleInfo.roleId;
            roleInfo.roleName = obj["roleName"].ToString() ?? roleInfo.roleName;
            roleInfo.roleLevel = (int)(obj["roleLevel"] ?? roleInfo.roleLevel);
            roleInfo.vipLevel = (int)(obj["vipLevel"] ?? roleInfo.vipLevel);
            roleInfo.roleDiamonds = (int)(obj["roleDiamonds"] ?? roleInfo.roleDiamonds);
            roleInfo.castleLevel = (int)(obj["castleLevel"] ?? roleInfo.castleLevel);
        }
        MaxSDK.GetInstance().ReportEnterServer(roleInfo);
#endif
    }


    /*
     * 【调用示例】 上报游戏事件打点
     */ 
    public void OnClickReportGameCustomEvent()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了游戏事件打点", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDKHWReportEventInfo eventInfo = new()
        {
            eventType = "事件分类",
            eventName = "事件名称",
            eventValue = "事件值"
        };

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            eventInfo.eventType = obj["eventType"].ToString() ?? eventInfo.eventType;
            eventInfo.eventName = obj["eventName"].ToString() ?? eventInfo.eventName;
            eventInfo.eventValue = obj["eventValue"].ToString() ?? eventInfo.eventValue;
        }

        MaxSDK.GetInstance().HWReportEvent(eventInfo);
#endif
    }


    /*
     * 【调用示例】 打开用户中心
     */
    public void OnClickUserCenter()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开用户中心", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenUserCenter();
#endif
    }


    /*
     * 【调用示例】 应用内购买
     */
    public void OnClickPay()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了内购", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDKHWPayInfo hwPayInfo = new()
        {
            productId = "fyymxm.app.coin60",    // 商品项ID
            serverId = "10004",                 // 游戏服ID
            roleId = "*****************",       // 角色ID
            roleName = "Rora",                  // 角色名称
            roleLevel = 260,                    // 角色等级
            cpOrderId = "cd6ztek2nh2m7zukdy258hbax963xr24", // 开发商订单号
            localPurchase = false,                          // 默认不启用本地内购
            purchaseType = HWIAPType.InAppPurchase,         // 默认为消耗型内购
            productDesc = "60 Gold Package",                // 商品描述
            cpProductId = "app-gold60",                     // 开发商商品项ID
            tagCurrency = "USD",                            // 游戏内显示的货币币种
            tagMoney = "4.99",                              // 游戏内显示的价格数值
            remark = "12345-789",                           // 订单附加信息
        };

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            hwPayInfo.productId = obj["productId"].ToString() ?? hwPayInfo.productId;
            hwPayInfo.serverId = obj["serverId"].ToString() ?? hwPayInfo.serverId;
            hwPayInfo.roleId = obj["roleId"].ToString() ?? hwPayInfo.roleId;
            hwPayInfo.roleName = obj["roleName"].ToString() ?? hwPayInfo.roleName;
            hwPayInfo.roleLevel = (int)(obj["roleLevel"] ?? hwPayInfo.roleLevel);
            hwPayInfo.cpOrderId = obj["cpOrderId"].ToString() ?? hwPayInfo.cpOrderId;
            string localPurchase = obj["localPurchase"].ToString() ?? "0";
            hwPayInfo.localPurchase = localPurchase == "1";
            string purchaseType = obj["purchaseType"].ToString() ?? hwPayInfo.purchaseType.ToString();
            hwPayInfo.purchaseType = (HWIAPType)Enum.Parse(typeof(HWIAPType), purchaseType);
            hwPayInfo.productDesc = obj["productDesc"].ToString() ?? hwPayInfo.productDesc;
            hwPayInfo.cpProductId = obj["cpProductId"].ToString() ?? hwPayInfo.cpProductId;
            hwPayInfo.tagCurrency = obj["tagCurrency"].ToString() ?? hwPayInfo.tagCurrency;
            hwPayInfo.tagMoney = obj["tagMoney"].ToString() ?? hwPayInfo.tagMoney;
            hwPayInfo.remark = obj["remark"].ToString() ?? hwPayInfo.remark;
        }
        MaxSDK.GetInstance().HWPay(hwPayInfo);
#endif
    }


    /*
     * 【调用示例】 获取内购商品价格
     */
    public void OnClickGetInAppSkuDetail()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了获取内购商品价格", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        HWIAPType purchaseType = HWIAPType.InAppPurchase;
        List<string> productIDs = new() { "fyymxm.app.coin6", "fyymxm.app.item0.99" };

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            purchaseType = obj["purchaseType"].ToString() == "2" ? HWIAPType.InAppSubscription : HWIAPType.InAppPurchase;
            productIDs = obj["productIDs"].ToString().Split(",").ToList();
        }

        MaxSDK.GetInstance().GetInAppSkuDetail(purchaseType, productIDs);
#endif
    }


    /* ------------------------------------ 可选接口 -------------------------------------- */

    /*
    * 【调用示例】 判断用户是否登录
    */
    public void OnClickGetSDKHasLogin()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击获取用户登录状态", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        bool sdkHasLogin = MaxSDK.GetInstance().SdkHasLogin();
        ShowMessage("[MaxSDK Demo] SDK登录状态：", sdkHasLogin ? "已登录" : "未登录");
#endif
    }


    /*
     * 【调用示例】 显示登录界面
    */
    public void OnClickPresentLoginView()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了显示登录界面", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenLoginView();
#endif
    }


    /*
     * 【调用示例】 设置账号绑定回调
     */
    public void OnClickSetAccountBindCallback()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了设置账号绑定回调", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().SetBindAccountCallback();

#endif
    }


    /*
     * 【调用示例】 游客账户绑定第三方平台
     */
    public void OnClickBindPlatform()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了游客账户绑定第三方平台", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        HWUserType userType = HWUserType.Facebook;

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            userType = (HWUserType)Enum.Parse(typeof(HWUserType), obj["userType"].ToString() ?? userType.ToString());
        }
        MaxSDK.GetInstance().BindThirdPlatform(userType);
#endif
    }


    /*
     * 【调用示例】 打开FAQ客服工单
     */
    public void OnClickFAQView()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开FAQ客服工单", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenFAQ();
#endif
    }


    /*
     * 【调用示例】 打开离线FAQ
     */
    public void OnClickLocalFAQView()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开离线FAQ", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenOfflineFAQ();
#endif
    }


    /*
     * 【调用示例】 获取fb游戏内好友信息
     */
    public void OnClickGetFBInGameFriendInfo()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了获取fb游戏内好友信息", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().GetFacebookInGameFriendInfo();
#endif
    }


    /*
     * 【调用示例】 邀请FB好友
     */
    public void OnClickInviteFBFriend()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了邀请好友进入游戏", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        string title = "标题";
        string message = "快来跟我一起玩游戏";
        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            title = obj["title"].ToString() ?? title;
            message = obj["message"].ToString() ?? message;
        }
        MaxSDK.GetInstance().InviteFacebookFriends(title, message);
#endif
    }


    /*
     * 【调用示例】 打开Facebook粉丝页面
     */
    public void OnClickFBView()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开FB粉丝页", "Yes", "No");
#endif
#if UNITY_ANDROID || UNITY_IOS
        string pageId = "110646548220659";
        string pageName = "MortalImmortal.M";
        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            pageId = obj["pageId"].ToString() ?? pageId;
            pageName = obj["pageName"].ToString() ?? pageName;
        }
        MaxSDK.GetInstance().OpenFacebookPage(pageId: pageId, pageName: pageName);
#endif
    }


    /*
     * 【调用示例】 设置广告邀请安装信息回调
     */
    public void OnClickSetAdChannelDataCallback()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了监听AF邀请安装信息", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().SetGetAdChannelDataCallback();
#endif
    }


    /*
     * 【调用示例】 打开NAVER社交中心
     */
    public void OnClickNaverView()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开Naver社交中心", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenNaverLounge();
#endif
    }


    /*
     * 【调用示例】 分享
     */
    public void OnClickShare()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了分享", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS

        MaxSDKHWShareInfo simplyShareInfo = new()
        {
            sharePlatform = MaxSDKSharePlatform.SHARE_PLATFORM_FACEBOOK,    // 分享的平台
            isSimplyShare = true,
            title = "分享的标题",
            url = "https://www.37games.com/",
            imageLocalPath = "待分享图片的本地路径"   // url为空时才有效
        };

        //MaxSDKHWShareInfo achieveShareInfo = new()
        //{
        //    sharePlatform = MaxSDKSharePlatform.SHARE_PLATFORM_FACEBOOK,    // 分享的平台
        //    isSimplyShare = false,
        //    title = "分享的标题",
        //    imageLocalPath = "待分享图片的本地路径",
        //    achievementId = "2343",
        //    serverId = "1001",
        //    roleId = "2398472385",
        //    roleName = "无敌铁牛",
        //};

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            
            simplyShareInfo.sharePlatform = (MaxSDKSharePlatform)Enum.Parse(typeof(MaxSDKSharePlatform), obj["sharePlatform"].ToString()?? simplyShareInfo.sharePlatform.ToString());
            simplyShareInfo.title = obj["title"].ToString()?? simplyShareInfo.title;
            simplyShareInfo.url = obj["url"].ToString()?? simplyShareInfo.url;
            simplyShareInfo.imageLocalPath = obj["imageLocalPath"].ToString()?? simplyShareInfo.imageLocalPath;
        }

        MaxSDK.GetInstance().HWShare(simplyShareInfo);
#endif
    }


    /*
     * 【调用示例】 设置SDK语言
     */
    public void OnClickSetLocalLanguage()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了设置SDK语言", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        HWSDKLanguageType languageType = HWSDKLanguageType.Auto;

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            languageType = (HWSDKLanguageType)Enum.Parse(typeof(HWSDKLanguageType), obj["languageType"].ToString() ?? languageType.ToString());
        }
        MaxSDK.GetInstance().SetSDKLanguage(languageType);
#endif
    }


    /*
     * 【调用示例】 打开链接
     */
    public void OnClickOpenWebUrl()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击打开链接", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS

        string url = "https://www.37games.com";
        bool openInBrowser = false;

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            url = obj["url"].ToString() ?? url;
            openInBrowser = obj["openInBrowser"].ToString() == "1";
        }

        MaxSDK.GetInstance().OpenUrl(url, openInBrowser: openInBrowser);
#endif
    }
   

    /*
     * 【调用示例】 打开游戏助手界面
     */
    public void OnClickGameHelper()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了打开游戏助手", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().OpenGameHelper();
#endif
    }    


    /*
     * 【调用示例】 退出游戏确认框
     */
    public void OnClickExitGame()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了退出游戏", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().ExitGame();
#endif
    }


    /*
     * 【调用示例】 获取Firebase推送Token
     */
    public void OnClickGetFirebaseToken()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了获取Firebase推送Token", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().GetFirebaseToken();
#endif
    }


    /*
     * 【调用示例】 第三方登录
     */
    public void OnClickLoginThirdPlatform()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了第三方登录", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        HWUserType userType = HWUserType.Facebook;

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            userType = (HWUserType)Enum.Parse(typeof(HWUserType), obj["userType"].ToString() ?? userType.ToString());
        }
        MaxSDK.GetInstance().LoginViaThirdPlatform(userType);
#endif
    }


    /*
     * 【调用示例】 游戏内按钮对接信息回调
     */
    public void OnClickSetGetButtonListCallback()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了设置游戏内按钮对接信息回调", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().SetInGameButtonInfoUpdateCallback();
#endif
    }


    /*
     * 【调用示例】 [仅iOS生效] 游戏内切换icon
     */
    public void OnClickSetAlternateIcon()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了游戏内切换icon", "Yes", "No");
#endif

#if UNITY_IOS
        string iconName = "图标名称";

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            iconName = obj["iconName"].ToString() ?? iconName;
        }
        MaxSDK.GetInstance().ChangeAppIcon(iconName: iconName);
#endif
    }


    /* ----------------------- 以下接口仅休闲益智游戏生效 ----------------------- */

    /*
     * 【调用示例】 [休闲益智游戏] Facebook登录
     */
    public void OnClickFBLogin()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了Facebook登录", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        MaxSDK.GetInstance().LoginViaFacebook();
#endif
    }


    /*
     * 【调用示例】 [休闲益智游戏] [仅iOS生效] Apple登录
     */
    public void OnClickAPLogin()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了Apple登录", "Yes", "No");
#endif

#if UNITY_IOS

        MaxSDK.GetInstance().LoginViaApple();
#endif
    }


    /*
     * 【调用示例】 [休闲益智游戏] [仅Android生效] Google登录
     */
    public void OnClickGPLogin()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了Google登录", "Yes", "No");
#endif

#if UNITY_IOS

        MaxSDK.GetInstance().LoginViaGoogle();
#endif
    }


    /*
     * 【调用示例】 [休闲益智游戏] 带用户类型的登出接口
     */
    public void OnClickLogoutWithType()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了指定类型登出", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        HWUserType userType = HWUserType.Facebook;

        // 读取调试参数
        string testJson = GetInputField().text;
        if (!testJson.Equals(""))
        {
            JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);
            userType = (HWUserType)Enum.Parse(typeof(HWUserType), obj["userType"].ToString() ?? userType.ToString());
        }
        MaxSDK.GetInstance().LogoutWithUserType(userType);
#endif
    }


    /*
     * 【调用示例】 [休闲益智游戏] [仅iOS生效] 同步获取广告渠道回调的数据
     */
    public void OnClickGetAdChannelData()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击获取广告渠道回调的数据", "Yes", "No");
#endif

#if UNITY_IOS
        string dataJson = MaxSDK.GetInstance().GetAdChannelData();
        ShowMessage("[MaxSDK Demo] 获取广告渠道回调的数据成功：", dataJson);
#endif
    }


    /* --------------------------- 以下接口无需调用 --------------------------- */

    /*
     * 获取SDK类型
     */
    public void OnClickGetSdkType()
    {
#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击获取 SDK 类型", "Yes", "No");
#endif

#if UNITY_ANDROID || UNITY_IOS
        int sdkType = MaxSDK.GetInstance().GetSDKType();
        ShowMessage("[MaxSDK Demo] 当前SDK类型 sdkType = " + sdkType, "");
#endif
    }


    /* ----------------------- 以下Demo调试逻辑，无需关注 ----------------------- */

    /*
     * 调试参数输入框
     */
    public InputField GetInputField()
    {
        return GameObject.Find("Canvas/Panel/参数输入框").GetComponent<InputField>();
    }

    /*
     * 信息输出框
     */
    public InputField GetOutputField()
    {
        return GameObject.Find("Canvas/Panel/参数输出框").GetComponent<InputField>();
    }

    /*
     * 清空
     */
    public void OnClickClear()
    {
        GetInputField().text = "";
        GetOutputField().text = "";
    }

    /*
     * 扩展接口参数输入框，用于Demo调试扩展接口
     */
    public InputField GetDispatchDebugField()
    {
        return GameObject.Find("Canvas/Panel/扩展接口参数输入框").GetComponent<InputField>();
    }

    /*
     * 使用扩展参数输入框调用异步方法
     */
    public void OnClickDispatchASync()
    {

#if UNITY_EDITOR
        EditorUtility.DisplayDialog("注意", "点击了异步方法扩展接口", "Yes", "No");
#endif
#if UNITY_ANDROID || UNITY_IOS

        string testJson = GetDispatchDebugField().text;
        JObject obj = (JObject)JsonConvert.DeserializeObject(testJson);

        string apiName = obj["apiName"].ToString();
        string extData = obj["extData"].ToString();
        MaxSDKDispatchInfo info = new()
        {
            apiName = apiName,
            extData = extData
        };
        MaxSDK.GetInstance().DispatchASync(info);

#endif
    }



    /* ---------------------- 以下是Demo UI设置，无需关注 ---------------------- */

    /*
     * 根据平台配置UI界面
     */
    private void ConfigPlatformUI()
    {
#if UNITY_EDITOR
        Debug.Log("[MaxSDK Demo] 当前为编辑器模式");
#endif
#if !UNITY_EDITOR
        Debug.Log("[MaxSDK Demo] 当前非编辑器模式");
#endif
#if UNITY_ANDROID
        Debug.Log("[MaxSDK Demo] 当前为安卓平台");
#endif
#if UNITY_IPHONE || UNITY_IOS
        Debug.Log("[MaxSDK Demo] 当前为IOS平台");
#endif
#if UNITY_STANDALONE_WIN
        Debug.Log("[MaxSDK Demo] 当前为Windows平台");
#endif
#if UNITY_STANDALONE_OSX
        Debug.Log("[MaxSDK Demo] 当前为OSX平台");
#endif

        GameObject.Find("Canvas/Panel/BundleID").GetComponent<Text>().text = Application.identifier;

        // 隐藏只在编辑器显示的UI
        GameObject[] hideTagObjs = GameObject.FindGameObjectsWithTag("EditorOnly");
        for (int i = 0; i < hideTagObjs.Length; i++)
        {
            Debug.Log("[MaxSDK Demo] 隐藏无需调用的功能按钮：" + hideTagObjs[i].name);
            hideTagObjs[i].SetActive(false);
        }

#if !UNITY_IPHONE && !UNITY_IOS
        GameObject[] iOSObjs = GameObject.FindGameObjectsWithTag("iOS");
        for (int i = 0; i < iOSObjs.Length; i++)
        {
            Debug.Log("[MaxSDK Demo] 隐藏iOS专属功能按钮：" + iOSObjs[i].name);
            iOSObjs[i].SetActive(false);
        }
#endif

#if !UNITY_ANDROID
        GameObject[] androidObjs = GameObject.FindGameObjectsWithTag("Android");
        for (int i = 0; i < androidObjs.Length; i++)
        {
            Debug.Log("[MaxSDK Demo] 隐藏Android专属功能按钮：" + androidObjs[i].name);
            androidObjs[i].SetActive(false);
        }
#endif

    }



    /* ------------------------------ 工具方法 ------------------------------ */

    /*
     * 判断一个字符串是否为url
     */
    public static bool IsUrl(string str)
    {
        try
        {
            string Url = @"^http(s)?://([\w-]+\.)+[\w-]+(/[\w- ./?%&=]*)?$";
            return Regex.IsMatch(str, Url);
        }
        catch (Exception)
        {
            return false;
        }
    }


    /*
     * 显示信息
     */
    public override void ShowMessage(string message, string json)
    {
        // 显示到调试输出框
        GetOutputField().text = message + json;
        // 显示到平台log输出
        Debug.Log(message + json);

        Toast(message);
    }


    /*
     * Toast
     */
    public void Toast(string message)
    {
#if UNITY_ANDROID
        AndroidJavaClass player = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject activity = player.GetStatic<AndroidJavaObject>("currentActivity");
        AndroidJavaClass toast = new AndroidJavaClass("android.widget.Toast");
        AndroidJavaObject context = activity.Call<AndroidJavaObject>("getApplicationContext");
        activity.Call("runOnUiThread", new AndroidJavaRunnable(() =>
        {
            toast.CallStatic<AndroidJavaObject>("makeText", context, message, toast.GetStatic<int>("LENGTH_SHORT")).Call("show");
        }));
#endif

#if !UNITY_IPHONE && !UNITY_IOS
    // TODO: 
#endif
    }




}
