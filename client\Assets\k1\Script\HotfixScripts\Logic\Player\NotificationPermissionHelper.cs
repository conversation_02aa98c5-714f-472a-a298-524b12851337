﻿//using Common;
//using Cysharp.Threading.Tasks;
//using GameState;
//using THelper;
//using Unity.Notifications.Android;
//using UnityEngine;
//using UnityEngine.Android;
 

//public class NotificationPermissionHelper : Ins<NotificationPermissionHelper>, IAfterSceneLoadCallback
//{
    
//    private const string PERMISSION = "android.permission.POST_NOTIFICATIONS";
//    private const int ANDROID_T_API_LEVEL = 33; // Android 13+

    
//    // 检查并申请通知权限
//    private void CheckNotificationPermission()
//    {
//        if (Application.platform != RuntimePlatform.Android) return;

//        using var version = new AndroidJavaClass("android.os.Build$VERSION");
//        int apiLevel = version.GetStatic<int>("SDK_INT");

//        // Android 13+ 需要动态申请通知权限
//        if (apiLevel >= ANDROID_T_API_LEVEL)
//        {
//            if (!Permission.HasUserAuthorizedPermission(PERMISSION))
//            {
//                Permission.RequestUserPermission(PERMISSION);
//                D.Info?.Log("请求通知权限");
//            }
//        }
//    }

  


//    public UniTask AfterSceneLoad()
//    {
//        if (!string.IsNullOrEmpty(SDKManager.instance.FcmPushToken))
//        {
//            MessageMgr.Send(new cspb.MessagePushRegisterReq() { DeviceToken = SDKManager.instance.FcmPushToken, deviceType = "fcm" });
//        }

//        CheckNotificationPermission();

//        return UniTask.CompletedTask;
//    }


//}