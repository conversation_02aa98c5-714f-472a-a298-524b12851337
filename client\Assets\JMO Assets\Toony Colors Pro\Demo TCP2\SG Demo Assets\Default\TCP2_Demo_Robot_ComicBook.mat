%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Robot_ComicBook
  m_Shader: {fileID: 4800000, guid: 26ac6b86cbebf834fb2ac55c14c9bb16, type: 3}
  m_ShaderKeywords: TCP2_DISABLE_WRAPPED_LIGHT TCP2_TANGENT_AS_NORMALS _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 90f5c2a59e018304bb00979205857f25, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _SketchTex
      second:
        m_Texture: {fileID: 2800000, guid: 74f40e2117b2f334083f0f1bbbe11df1, type: 3}
        m_Scale: {x: 10, y: 10}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _EnableConstSizeOutline
      second: 0
    - first:
        name: _EnableTexturedOutline
      second: 0
    - first:
        name: _EnableZSmooth
      second: 0
    - first:
        name: _Offset1
      second: 0
    - first:
        name: _Offset2
      second: 0
    - first:
        name: _Outline
      second: 0.3
    - first:
        name: _RampSmooth
      second: 0.5
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RimMax
      second: 0.7
    - first:
        name: _RimMin
      second: 0.5
    - first:
        name: _Shininess
      second: 2
    - first:
        name: _SketchHalftoneMax
      second: 1
    - first:
        name: _SketchHalftoneMin
      second: 0
    - first:
        name: _SketchSpeed
      second: 6
    - first:
        name: _Smoothness
      second: 0.2
    - first:
        name: _SpecSmooth
      second: 0.35
    - first:
        name: _TexLod
      second: 5
    - first:
        name: _ZSmooth
      second: -0.5
    - first:
        name: __dummy__
      second: 0
    - first:
        name: __outline_gui_dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _OutlineColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _RimColor
      second: {r: 0, g: 0, b: 0, a: 0.359}
    - first:
        name: _SColor
      second: {r: 0, g: 0, b: 0, a: 0}
    - first:
        name: _SketchColor
      second: {r: 0.5588235, g: 0.5588235, b: 0.5588235, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.16176468, g: 0.14511244, b: 0.14511244, a: 1}
