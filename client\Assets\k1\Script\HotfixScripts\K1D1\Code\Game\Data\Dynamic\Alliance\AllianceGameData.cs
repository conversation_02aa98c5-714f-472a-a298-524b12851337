﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using GameState;
using THelper;

namespace Game.Data
{ 
    /// <summary>
    /// 联盟数据信息
    /// </summary>
    public partial class AllianceGameData : Ins<AllianceGameData>,IBeforeLoginCallback,ILoginCallback,ILogoutCallback
    {
        /// <summary>
        /// 联盟战相关信息数据
        /// </summary>
        private AllianceWarGameData _allianceWarData;
        /// <summary>
        /// 联盟战相关信息数据
        /// </summary>
        public AllianceWarGameData AllianceWarData
        {
            get
            { 
                return _allianceWarData;
            }
        }

        /// <summary>
        /// 联盟留言板数据
        /// </summary>
        private AllianceMsgBoardGameData _alliancMsgBoardData;

        /// <summary>
        /// 联盟留言板数据
        /// </summary>
        public AllianceMsgBoardGameData AlliancMsgBoardData
        {
            get
            {
                if (_alliancMsgBoardData == null)
                    _alliancMsgBoardData = new AllianceMsgBoardGameData();

                return _alliancMsgBoardData;
            }
        }

        private AlliancePropGameData alliancePropGameData;

        public AlliancePropGameData AlliancePropGameData
        {
            get
            {
                if (alliancePropGameData == null)
                    alliancePropGameData = new AlliancePropGameData();

                return alliancePropGameData;
            }
        }

        /// <summary>
        /// 联盟战争记录数据
        /// </summary>
        private AllianceWarRecordGameData _alliancWarRecordData;

        /// <summary>
        /// 联盟战争记录数据
        /// </summary>
        public AllianceWarRecordGameData AlliancWarRecordData
        {
            get
            {
                if (_alliancWarRecordData == null)
                    _alliancWarRecordData = new AllianceWarRecordGameData();

                return _alliancWarRecordData;
            }
        }

        /// <summary>
        /// 联盟圣坛数据
        /// </summary>
        private AllianceAltarGameData _altarData;

        /// <summary>
        /// 联盟圣坛数据
        /// </summary>
        public AllianceAltarGameData AltarData
        {
            get
            {
                if (_altarData == null)
                    _altarData = new AllianceAltarGameData();

                return _altarData;
            }
        }

        /// <summary>
        /// 联盟建筑记录数据
        /// </summary>
        private AllianceBuildingRecordGameData _buildingRecordData;

        /// <summary>
        /// 联盟建筑记录数据
        /// </summary>
        public AllianceBuildingRecordGameData BuildingRecordData
        {
            get
            {
                if (_buildingRecordData == null)
                    _buildingRecordData = new AllianceBuildingRecordGameData();

                return _buildingRecordData;
            }
        }

        /// <summary>
        /// 联盟领土建筑数据
        /// </summary>
        private AllianceTerritoryBuildingGameData _alliancTerritoryBuildingData;

        /// <summary>
        /// 联盟领土建筑数据
        /// </summary>
        public AllianceTerritoryBuildingGameData TerritoryBuildingData
        {
            get
            {
                if (_alliancTerritoryBuildingData == null)
                    _alliancTerritoryBuildingData = new AllianceTerritoryBuildingGameData();

                return _alliancTerritoryBuildingData;
            }
        }

        /// <summary>
        /// 联盟战争记录数据
        /// </summary>
        private AllianceHelpGameData _allianceHelp;

        /// <summary>
        /// 联盟帮助数据
        /// </summary>
        public AllianceHelpGameData AllianceHelp
        {
            get
            {
                if (_allianceHelp == null)
                    _allianceHelp = new AllianceHelpGameData();

                return _allianceHelp;
            }
        }

        /// <summary>
        /// 盟友城堡数据
        /// </summary>
        private AllianceAllyCityGameData _allianceAllyCityData;

        /// <summary>
        /// 盟友城堡数据
        /// </summary>
        public AllianceAllyCityGameData AllyCityData
        {
            get
            {
                if (_allianceAllyCityData == null)
                    _allianceAllyCityData = new AllianceAllyCityGameData();

                return _allianceAllyCityData;
            }
        }

        /// <summary>
        /// 初始化军情警报
        /// </summary>
        private MilitaryAlertGameData _militaryAlertData;

        /// <summary>
        /// 初始化军情警报数据
        /// </summary>
        public MilitaryAlertGameData MilitaryAlertData
        {
            get
            {
                if (_militaryAlertData == null)
                    _militaryAlertData = new MilitaryAlertGameData();
                return _militaryAlertData;
            }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        public async UniTask Init()
        {
            _allianceWarData = await AllianceWarGameData.CreateAsync();

            //初始化联盟礼物数据
            GameData.I.AllianceGiftData.Init();

            //联盟留言板
            AlliancMsgBoardData.Init();

            //战争记录
            AlliancWarRecordData.Init();

            //领土建筑
            TerritoryBuildingData.Init();

            //联盟帮助
            AllianceHelp.Init();

           await  AlliancePropGameData.Init();

            //圣坛
            AltarData.Init();

            //建筑记录
            BuildingRecordData.Init();

            //盟友城堡
            AllyCityData.Init();

            //军情
            MilitaryAlertData.Init();

        }

         
        public async UniTask OnBeforeLogin()
        {
            await Init();
        }

        public UniTask OnLogin()
        {
            GameData.I.AllianceGiftData.ReqUnionGiftInfo();
            AlliancWarRecordData.ReqPvpInfo();
            AlliancePropGameData.ReqData();
            return UniTask.CompletedTask;     
        }

        public UniTask OnLogout()
        {
            _allianceHelp = null;
            _alliancMsgBoardData = null;
            _alliancWarRecordData = null;
            _alliancTerritoryBuildingData = null;
            _allianceWarData = null;
            alliancePropGameData = null;
            _altarData = null;
            _buildingRecordData = null;
            _allianceAllyCityData = null;
            _militaryAlertData = null;
            return UniTask.CompletedTask;
        }
    }
}
