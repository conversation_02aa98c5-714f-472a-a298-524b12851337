﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.IO;
using System.IO.Compression;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //deprecated, only for very old map editor version
    [Black]
    public class MapBinaryLoaderV1 : MapBinaryLoader
    {
        public MapBinaryLoaderV1(int majorVersion) : base(majorVersion)
        {
        }

        public override config.EditorMapData Load(int minorVersion, string mapName, Map map, BinaryReader reader)
        {
            mMinorVersion = minorVersion;
            mEditorData = new config.EditorMapData(0, 0, 80, false, 0, 30, false, MapModule.defaultNavMeshCreateMode, MapModule.defaultGlobalObstacleCreateMode, MapModule.defaultGroundTileSize, MapModule.defaultFrontTileSize, new config.BackgroundSetting(), new Version(this.majorVersion, minorVersion), new Bounds());

            bool compressed = false;
            int compressedFileSize = 0;
            int originalFileSize = 0;
            if (mMinorVersion >= 51)
            {
                compressed = reader.ReadBoolean();
            }

            if (compressed)
            {
                compressedFileSize = reader.ReadInt32();
                originalFileSize = reader.ReadInt32();
                //读取压缩的数据
                var compressedData = reader.ReadBytes(compressedFileSize);
                MemoryStream compressedDataStream = new MemoryStream(compressedData);
                DeflateStream compressedStream = new DeflateStream(compressedDataStream, CompressionMode.Decompress);

                byte[] uncompressedData = new byte[originalFileSize];
                compressedStream.Read(uncompressedData, 0, originalFileSize);
                compressedStream.Close();

                compressedDataStream.Close();

                MemoryStream uncompressedStream = new MemoryStream(uncompressedData);
                reader = new BinaryReader(uncompressedStream);
            }

            LoadSetting(reader);
            LoadNavMeshObstacles(reader);
            LoadSpriteTemplates(reader);
            LoadModelTemplates(reader);
            LoadPropertySets(reader);
            LoadModelProperties(reader);
            LoadPrefabManagers(reader);
            LoadCamera(reader);
            LoadMap(reader);
            LoadMapObstacles(reader);

            List<config.RuinSetting> ruinSettings = new List<config.RuinSetting>();
            if (mMinorVersion >= 19)
            {
                ruinSettings = LoadRuinObjectTypes(reader);
            }

            var ruinLayerData = mEditorData.map.GetMapLayer(typeof(config.RuinLayerData)) as config.RuinLayerData;
            if (ruinLayerData != null)
            {
                ruinLayerData.ruinObjectTypes = ruinSettings;
            }

            if (mMinorVersion >= 37)
            {
                mEditorData.detailSpritesSetting = LoadDetailSprites(reader);
            }

            if (mMinorVersion >= 42)
            {
                mEditorData.gridRegionEditorSetting = LoadGridRegionEditorSetting(reader);
            }

            return mEditorData;
        }

        void LoadSetting(BinaryReader reader)
        {
            if (mMinorVersion >= 4)
            {
                mEditorData.setting.cameraMoveRange = Utils.ReadVector2(reader);
            }
            else
            {
                var bounds = Utils.ReadBounds(reader);
                mEditorData.setting.cameraMoveRange = new Vector2(bounds.min.y, bounds.max.y);
            }
            if (mMinorVersion >= 12)
            {
                mEditorData.setting.exportFolder = Utils.ReadString(reader);
            }

            if (mMinorVersion >= 22)
            {
                long pathMapperDataOffset = reader.ReadInt64();
                long curPos = reader.BaseStream.Position;
                reader.BaseStream.Position = pathMapperDataOffset;
                LoadPathMapper(reader);
                reader.BaseStream.Position = curPos;
            }
        }

        config.PropertySet LoadPropertySet(BinaryReader reader)
        {
            var id = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var properties = LoadProperties(reader);
            var ps = new config.PropertySet(id, properties, name);
            return ps;
        }

        void LoadPropertySets(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var editorData = mEditorData as config.EditorMapData;
            editorData.propertySets.propertySets = new config.PropertySet[n];
            for (int i = 0; i < n; ++i)
            {
                editorData.propertySets.propertySets[i] = LoadPropertySet(reader);
            }
        }

        config.Property LoadOneProperty(BinaryReader reader)
        {
            var propInfo = new config.Property();
            propInfo.name = Utils.ReadString(reader);
            propInfo.type = (PropertyType)reader.ReadInt32();
            if (propInfo.type == PropertyType.kPropertyInt)
            {
                propInfo.value = reader.ReadInt32();
            }
            else if (propInfo.type == PropertyType.kPropertyIntArray)
            {
                propInfo.value = Utils.ReadIntArray(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyFloat)
            {
                propInfo.value = reader.ReadSingle();
            }
            else if (propInfo.type == PropertyType.kPropertyBool)
            {
                propInfo.value = reader.ReadBoolean();
            }
            else if (propInfo.type == PropertyType.kPropertyString)
            {
                propInfo.value = Utils.ReadString(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector3)
            {
                propInfo.value = Utils.ReadVector3(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector2)
            {
                propInfo.value = Utils.ReadVector2(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyVector4)
            {
                propInfo.value = Utils.ReadVector4(reader);
            }
            else if (propInfo.type == PropertyType.kPropertyColor)
            {
                propInfo.value = Utils.ReadColor32(reader);
            }
            else
            {
                Debug.Assert(false);
            }
            return propInfo;
        }

        config.Properties LoadProperties(BinaryReader reader)
        {
            var pi = new config.Properties();
            int nProperties = reader.ReadInt32();
            for (int i = 0; i < nProperties; ++i)
            {
                var propInfo = LoadOneProperty(reader);
                pi.properties.Add(propInfo);
            }
            return pi;
        }

        void LoadMap(BinaryReader reader)
        {
            if (mMinorVersion >= 57)
            {
                mEditorData.mapType = reader.ReadInt32();
            }
            mEditorData.map.width = reader.ReadSingle();
            mEditorData.map.height = reader.ReadSingle();
            if (mMinorVersion >= 20)
            {
                mEditorData.map.borderHeight = reader.ReadSingle();
            }
            if (mMinorVersion >= 24)
            {
                mEditorData.map.isCircle = reader.ReadBoolean();
            }
            if (mMinorVersion >= 55)
            {
                mEditorData.map.backExtendedSize = reader.ReadSingle();
            }
            if (mMinorVersion >= 61)
            {
                mEditorData.map.generateNPCSpawnPointsInBorderLine = reader.ReadBoolean();
            }

            if (mMinorVersion >= 31)
            {
                mEditorData.grid.visible = reader.ReadBoolean();
                mEditorData.grid.totalWidth = reader.ReadSingle();
                mEditorData.grid.totalHeight = reader.ReadSingle();
                mEditorData.grid.gridWidth = reader.ReadSingle();
                mEditorData.grid.gridHeight = reader.ReadSingle();
                mEditorData.grid.color = Utils.ReadColor32(reader);
            }

            mEditorData.viewCenter = Utils.ReadVector3(reader);
            mEditorData.viewportSize = Utils.ReadVector2(reader);

            LoadMapLayers(reader);
            LoadMapLODConfig(reader);
        }

        void LoadMapLODConfig(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.lodConfig.lods = new config.MapLOD[n];
            for (int i = 0; i < n; ++i)
            {
                var lod = new config.MapLOD();
                mEditorData.map.lodConfig.lods[i] = lod;
                lod.cameraHeight = reader.ReadSingle();
                if (mMinorVersion >= 8)
                {
                    lod.viewWidth = reader.ReadSingle();
                    lod.viewHeight = reader.ReadSingle();
                    int unitCount = reader.ReadInt32();
                    lod.displayingUnits = new List<config.MapLODUnit>();
                    for (int k = 0; k < unitCount; ++k)
                    {
                        lod.displayingUnits.Add(new config.MapLODUnit(Utils.ReadString(reader), ""));
                    }
                }
            }
        }

        void LoadCamera(BinaryReader reader)
        {
            var cameraInfo = new config.Camera();
            cameraInfo.position = Utils.ReadVector3(reader);
            cameraInfo.rotation = Utils.ReadQuaternion(reader);
            cameraInfo.orthographic = reader.ReadBoolean();
            cameraInfo.orthongonalSize = reader.ReadSingle();
            cameraInfo.verticalFov = reader.ReadSingle();

            mEditorData.map.camera = cameraInfo;
        }

        config.SpriteTileLayerData LoadSpriteTileLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            int nRows = reader.ReadInt32();
            int nCols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();
            var tiles = new int[nRows * nCols];

            for (int i = 0; i < nRows; ++i)
            {
                for (int j = 0; j < nCols; ++j)
                {
                    var spriteTemplateID = reader.ReadInt32();
                    int idx = i * nCols + j;
                    tiles[idx] = spriteTemplateID;
                }
            }

            var layer = new config.SpriteTileLayerData(layerID, name, offset, null, nRows, nCols, tileWidth, tileHeight, (GridType)gridType, tiles);
            return layer;
        }

        config.BlendTerrainTileData LoadBlendTerrainTileData(BinaryReader reader, int layerID)
        {
            config.BlendTerrainTileData tileData = null;
            var tileID = reader.ReadInt32();
            if (tileID > 0)
            {
                var templateID = reader.ReadInt32();
                var type = reader.ReadInt32();
                var index = reader.ReadInt32();
                int subTypeIndex = 0;
                if (mMinorVersion >= 53)
                {
                    subTypeIndex = reader.ReadInt32();
                }
                tileData = new config.BlendTerrainTileData(tileID, layerID, type, index, subTypeIndex, templateID, null);
            }
            return tileData;
        }

        config.MapLayerData LoadBlendTerrainLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            bool useGeneratedLOD = true;
            if (mMinorVersion >= 52)
            {
                useGeneratedLOD = reader.ReadBoolean();
            }

            var objects = new config.BlendTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadBlendTerrainTileData(reader, layerID);
                }
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.BlendTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, objects, useGeneratedLOD, false, "", "", false, null, false, false, false, Vector3.zero);
            return layer;
        }

        config.MapLayerData LoadEntitySpawnLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            //load layer info
            float regionWidth = reader.ReadSingle();
            float regionHeight = reader.ReadSingle();
            int horizontalRegionCount = reader.ReadInt32();
            int verticalRegionCount = reader.ReadInt32();
            var npcMoveRange = Utils.ReadVector2(reader);

            ISpawnPointGenerationStrategy strategy = null;
            if (mMinorVersion >= 15)
            {
                strategy = LoadSpawnPointGenerationStrategy(reader, npcMoveRange.y);
            }
            else
            {
                float npcDensity = reader.ReadSingle();
                int oneMeterHorizontalResolution = reader.ReadInt32();
                int oneMeterVerticalResolution = reader.ReadInt32();
                strategy = new GenerateSpawnPointsRandomly(npcDensity, npcMoveRange.y, oneMeterHorizontalResolution, oneMeterVerticalResolution);
            }

            //load npc move info
            int waypointCount = reader.ReadInt32();
            List<Vector2> npcWaypoints = new List<Vector2>();
            for (int i = 0; i < waypointCount; ++i)
            {
                npcWaypoints.Add(Utils.ReadVector2(reader));
            }

            //load spawn points
            int nRegions = horizontalRegionCount * verticalRegionCount;
            var regions = new List<config.EntitySpawnRegion>();
            for (int i = 0; i < nRegions; ++i)
            {
                var region = new config.EntitySpawnRegion();
                region.visible = reader.ReadBoolean();
                int nSpawnPointsInRegion = reader.ReadInt32();
                region.spawnPoints = new List<Vector2>();
                for (int s = 0; s < nSpawnPointsInRegion; ++s)
                {
                    region.spawnPoints.Add(Utils.ReadVector2(reader));
                }
                regions.Add(region);
            }

            var layer = new config.EntitySpawnLayerData(layerID, name, offset, horizontalRegionCount, verticalRegionCount, new Vector2(regionWidth, regionHeight), npcMoveRange, regions, npcWaypoints, strategy, null, null, "");

            return layer;
        }

        ISpawnPointGenerationStrategy LoadSpawnPointGenerationStrategy(BinaryReader reader, float maxMoveRange)
        {
            ISpawnPointGenerationStrategy strategy = null;
            var type = (SpawnPointGenerateStrategyType)reader.ReadInt32();
            if (type == SpawnPointGenerateStrategyType.Random)
            {
                float npcDensity = reader.ReadSingle();
                int oneMeterHorizontalResolution = reader.ReadInt32();
                int oneMeterVerticalResolution = reader.ReadInt32();
                strategy = new GenerateSpawnPointsRandomly(npcDensity, maxMoveRange, oneMeterHorizontalResolution, oneMeterVerticalResolution);
            }
            else if (type == SpawnPointGenerateStrategyType.Grid)
            {
                var startOffset = Utils.ReadVector3(reader);
                var pointDeltaDistance = Utils.ReadVector2(reader);
                float xOffset = reader.ReadSingle();
                float randomRange = reader.ReadSingle();
                strategy = new GenerateSpawnPointsInGridLayout(startOffset, pointDeltaDistance, xOffset, randomRange, maxMoveRange);
            }
            else
            {
                Debug.Assert(false, "unknown strategy!");
            }

            return strategy;
        }

        config.ModelLODGroupManager LoadModelLODGroupManager(BinaryReader reader)
        {
            config.ModelLODGroupManager lodGroupManager = new config.ModelLODGroupManager();
            int nGroups = reader.ReadInt32();
            lodGroupManager.groups = new config.ModelLODGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.ModelLODGroup();
                var id = reader.ReadInt32();
                group.SetID(id);
                group.combineModels = reader.ReadBoolean();
                group.leaderObjectID = reader.ReadInt32();
                group.lod = reader.ReadInt32();

                lodGroupManager.groups[i] = group;
            }
            return lodGroupManager;
        }

        config.MapLayerData LoadRailwayLayer(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            if (mMinorVersion >= 11)
            {
                float layerWidth = reader.ReadSingle();
                float layerHeight = reader.ReadSingle();
                int n = reader.ReadInt32();
                config.MapObjectData[] objects = new config.MapObjectData[n];
                for (int i = 0; i < n; ++i)
                {
                    objects[i] = LoadRailObjectData(reader);
                }

                var config = LoadMapLayerLODConfig(reader);

                config.ModelLODGroupManager lodGroupManager = new TFW.Map.config.ModelLODGroupManager();
                if (mMinorVersion >= 26)
                {
                    lodGroupManager = LoadModelLODGroupManager(reader);
                }

                int count = reader.ReadInt32();
                float width = reader.ReadSingle();
                float radius = reader.ReadSingle();
                float railPrefabLength = 50;
                Vector3 center = Utils.ReadVector3(reader);
                if (mMinorVersion >= 28)
                {
                    railPrefabLength = reader.ReadSingle();
                }

                var layerData = new config.RailwayLayerData(layerID, name, offset, config, lodGroupManager, layerWidth, layerHeight, objects, count, width, radius, center, railPrefabLength);
                return layerData;
            }
            else
            {
                //load layer info
                float layerWidth = reader.ReadSingle();
                float layerHeight = reader.ReadSingle();

                int count = reader.ReadInt32();
                float width = reader.ReadSingle();
                float radius = reader.ReadSingle();
                Vector3 center = Utils.ReadVector3(reader);

                var layer = new config.RailwayLayerData(layerID, name, offset, null, null, layerWidth, layerHeight, null, count, width, radius, center, 50);
                return layer;
            }
        }

        config.MapLayerData LoadLODLayer(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            //load layer info
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.LODLayerData(layerID, name, offset, config, layerWidth, layerHeight);
            return layer;
        }

        config.MapLayerData LoadNPCRegionLayerData(BinaryReader reader, int layerID, string name, Vector3 offset)
        {
            int nRows = reader.ReadInt32();
            int nCols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();

            int nLayers = 1;
            if (mMinorVersion >= 65)
            {
                nLayers = reader.ReadInt32();
            }

            List<config.NPCRegionLayerData.Layer> layerDatas = new List<config.NPCRegionLayerData.Layer>();
            for (int k = 0; k < nLayers; ++k)
            {
                var layerData = new config.NPCRegionLayerData.Layer();
                layerDatas.Add(layerData);
                string layerName = "Default Layer";
                if (mMinorVersion >= 65)
                {
                    layerName = Utils.ReadString(reader);
                }
                layerData.name = layerName;

                layerData.tiles = new int[nRows * nCols];
                for (int i = 0; i < nRows; ++i)
                {
                    for (int j = 0; j < nCols; ++j)
                    {
                        var spriteTemplateID = reader.ReadInt32();
                        int idx = i * nCols + j;
                        layerData.tiles[idx] = spriteTemplateID;
                    }
                }

                layerData.regionTemplates = new config.NPCRegionTemplate[0];
                if (mMinorVersion >= 64)
                {
                    int nTemplates = reader.ReadInt32();
                    layerData.regionTemplates = new config.NPCRegionTemplate[nTemplates];
                    for (int i = 0; i < nTemplates; ++i)
                    {
                        int type = reader.ReadInt32();
                        //placeholder
                        reader.ReadInt32();
                        int level = reader.ReadInt32();
                        Color32 color = Utils.ReadColor32(reader);
                        string templateName = Utils.ReadString(reader);
                        layerData.regionTemplates[i] = new config.NPCRegionTemplate(templateName, type, level, color, 0);
                    }
                }
            }

            var layer = new config.NPCRegionLayerData(layerID, name, offset, null, nRows, nCols, tileWidth, tileHeight, layerDatas);
            return layer;
        }

        bool LoadMapObjectBaseData(BinaryReader reader, config.MapObjectData data)
        {
            bool isDefaultRotation;
            bool isDefaultScale;

            data.SetID(reader.ReadInt32());
            if (mMinorVersion >= 7)
            {
                data.flag = reader.ReadInt32();
            }

            if (data.id != 0)
            {
                data.position = Utils.ReadVector3(reader);
                isDefaultRotation = reader.ReadBoolean();
                if (!isDefaultRotation)
                {
                    data.rotation = Utils.ReadQuaternion(reader);
                }
                isDefaultScale = reader.ReadBoolean();
                if (!isDefaultScale)
                {
                    data.scale = Utils.ReadVector3(reader);
                }
                return true;
            }
            return false;
        }

        bool LoadGridObjectBaseData(BinaryReader reader, config.GridMapObjectData data)
        {
            bool valid = LoadMapObjectBaseData(reader, data);
            if (valid)
            {
                data.isDefaultPosition = reader.ReadBoolean();
            }
            return valid;
        }

        config.GridModelData LoadGridModelData(BinaryReader reader)
        {
            var id = Utils.PeekID(reader);
            if (id != 0)
            {
                var gridModelData = new config.GridModelData();
                LoadGridObjectBaseData(reader, gridModelData);
                gridModelData.modelTemplateID = reader.ReadInt32();
                return gridModelData;
            }
            else
            {
                id = reader.ReadInt32();
            }
            return null;
        }

        config.ModelData LoadModelData(BinaryReader reader)
        {
            var modelData = new config.ModelData();
            LoadMapObjectBaseData(reader, modelData);
            modelData.modelTemplateID = reader.ReadInt32();
            return modelData;
        }

        config.RailObjectData LoadRailObjectData(BinaryReader reader)
        {
            var data = new config.RailObjectData();
            LoadMapObjectBaseData(reader, data);
            data.modelTemplateID = reader.ReadInt32();
            if (mMinorVersion >= 23)
            {
                data.type = (RailObjectType)reader.ReadInt32();
            }
            if (mMinorVersion >= 26)
            {
                data.isGroupLeader = reader.ReadBoolean();
                data.groupID = reader.ReadInt32();
                data.railIndex = reader.ReadInt32();
            }
            if (mMinorVersion >= 28)
            {
                data.segmentIndex = reader.ReadInt32();
            }
            return data;
        }

        config.MapCollisionData LoadCollisionData(BinaryReader reader)
        {
            var data = new config.MapCollisionData();
            data.SetID(reader.ReadInt32());
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.navMeshObstacleOutlines.Add(Utils.ReadVector2(reader));
            }

            if (mMinorVersion >= 27)
            {
                n = reader.ReadInt32();
                for (int i = 0; i < n; ++i)
                {
                    data.objectPlacementOutlines.Add(Utils.ReadVector2(reader));
                }
            }

            if (mMinorVersion >= 10)
            {
                data.radius = reader.ReadSingle();
                data.isExtandable = reader.ReadBoolean();
                if (mMinorVersion >= 44)
                {
                    data.attribute = (CollisionAttribute)reader.ReadInt32();
                    data.attribute |= CollisionAttribute.IsAutoExpandingObstacle;
                    if (mMinorVersion >= 45)
                    {
                        data.type = reader.ReadInt32();
                    }
                }
                else
                {
                    if (mMinorVersion >= 21)
                    {
                        bool onlyForCollisionCheck = reader.ReadBoolean();
                        if (onlyForCollisionCheck)
                        {
                            data.attribute |= CollisionAttribute.CollisionCheck;
                        }
                    }
                    if (mMinorVersion >= 25)
                    {
                        bool isCreateObstacle = reader.ReadBoolean();
                        if (isCreateObstacle)
                        {
                            data.attribute |= CollisionAttribute.IsObstacle;
                        }
                    }
                    if (mMinorVersion >= 35)
                    {
                        bool canPlaceDecorationObject = reader.ReadBoolean();
                        if (canPlaceDecorationObject)
                        {
                            data.attribute |= CollisionAttribute.CanPlaceDecorationObject;
                        }
                    }
                }
            }
            return data;
        }

        config.RegionData LoadRegionData(BinaryReader reader)
        {
            Debug.Assert(false, "can't be here!");
            var data = new config.RegionData();
            data.SetID(reader.ReadInt32());
            int n = reader.ReadInt32();
            //for (int i = 0; i < n; ++i)
            //{
            //    data.outline.Add(Utils.ReadVector2(reader));
            //}

            //data.radius = reader.ReadSingle();
            //data.isExtandable = reader.ReadBoolean();
            data.meshVertices = Utils.ReadVector3Array(reader);
            data.meshIndices = Utils.ReadIntArray(reader);
            if (mMinorVersion >= 40)
            {
                data.innerColor = Utils.ReadColor(reader);
            }

            return data;
        }

        config.CameraColliderData LoadCameraColliderData(BinaryReader reader)
        {
            var data = new config.CameraColliderData();
            data.SetID(reader.ReadInt32());
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.bottomOutline.Add(Utils.ReadVector2(reader));
            }

            n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.topOutline.Add(Utils.ReadVector2(reader));
            }

            data.radius = reader.ReadSingle();
            data.height = reader.ReadSingle();

            int vertexCount = reader.ReadInt32();
            data.vertices = new Vector3[vertexCount];
            for (int i = 0; i < vertexCount; ++i)
            {
                data.vertices[i] = Utils.ReadVector3(reader);
            }

            int indexCount = reader.ReadInt32();
            data.indices = new int[indexCount];
            for (int i = 0; i < indexCount; ++i)
            {
                data.indices[i] = reader.ReadInt32();
            }

            return data;
        }

        config.GridModelLayerData LoadGridModelLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();

            var objects = new config.GridMapObjectData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadGridModelData(reader);
                }
            }

            var config = LoadMapLayerLODConfig(reader);

            var layer = new config.GridModelLayerData(layerID, layerName, offset, config, rows, cols, tileWidth, tileHeight, (GridType)gridType, objects);
            return layer;
        }

        config.ModelLayerData LoadModelLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadModelData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.ModelLayerData(layerID, layerName, offset, config, null, width, height, objects);
            return layer;
        }

        config.MapCollisionLayerData LoadCollisionLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            PrefabOutlineType displayType = PrefabOutlineType.NavMeshObstacle;
            float displayVertexRadius = 5.0f;
            if (mMinorVersion >= 30)
            {
                displayType = (PrefabOutlineType)reader.ReadInt32();
                displayVertexRadius = reader.ReadSingle();
            }

            int n = reader.ReadInt32();
            config.MapCollisionData[] objects = new config.MapCollisionData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCollisionData(reader);
            }

            var layer = new config.MapCollisionLayerData(layerID, layerName, offset, null, width, height, objects, displayType, displayVertexRadius, new config.Detector[0], 10);
            return layer;
        }

        config.RegionLayerData LoadRegionLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            Debug.Assert(false, "Can't be here!");
            return null;
            //float width = reader.ReadSingle();
            //float height = reader.ReadSingle();
            //PrefabOutlineType displayType = PrefabOutlineType.NavMeshObstacle;
            //float displayVertexRadius = 5.0f;
            //displayType = (PrefabOutlineType)reader.ReadInt32();
            //displayVertexRadius = reader.ReadSingle();

            //int n = reader.ReadInt32();
            //config.RegionData[] objects = new config.RegionData[n];
            //for (int i = 0; i < n; ++i)
            //{
            //    objects[i] = LoadRegionData(reader);
            //}

            //var layer = new config.RegionLayerData(layerID, layerName, offset, null, width, height, objects, displayType, displayVertexRadius);
            //return layer;
        }

        config.CameraColliderLayerData LoadCameraColliderLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            float displayVertexRadius = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.CameraColliderData[] objects = new config.CameraColliderData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCameraColliderData(reader);
            }

            var layer = new config.CameraColliderLayerData(layerID, layerName, offset, null, width, height, objects, displayVertexRadius);
            return layer;
        }

        config.PolygonRiverLayerData LoadPolygonRiverLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            float displayVertexRadius = reader.ReadSingle();
            int bakedTextureSize = 256;
            if (mMinorVersion >= 50)
            {
                bakedTextureSize = reader.ReadInt32();
            }

            int n = reader.ReadInt32();
            config.PolygonRiverData[] objects = new config.PolygonRiverData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadPolygonRiverData(reader);
            }

            config.RiverPrefab[] riverPrefabs = null;
            if (mMinorVersion >= 41)
            {
                riverPrefabs = LoadRiverPrefabInfos(reader);
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.PolygonRiverLayerData(layerID, layerName, offset, config, width, height, objects, displayVertexRadius, riverPrefabs, bakedTextureSize, false, new List<int>(), false, false, 0, "");
            return layer;
        }

        config.RiverPrefab[] LoadRiverPrefabInfos(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            config.RiverPrefab[] prefabs = new config.RiverPrefab[n];
            for (int i = 0; i < n; ++i)
            {
                int riverID = reader.ReadInt32();
                int sectionIndex = reader.ReadInt32();
                string prefabPath = Utils.ReadString(reader);
                prefabs[i] = new config.RiverPrefab(riverID, sectionIndex, prefabPath);
            }
            return prefabs;
        }

        config.PolygonRiverData LoadPolygonRiverData(BinaryReader reader)
        {
            var data = new config.PolygonRiverData();
            data.SetID(reader.ReadInt32());
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.outline.Add(Utils.ReadVector2(reader));
            }

            int nSections = reader.ReadInt32();
            data.sections = new config.PolygonRiverSectionData[nSections];
            for (int i = 0; i < nSections; ++i)
            {
                data.sections[i] = new config.PolygonRiverSectionData();
                if (mMinorVersion >= 56)
                {
                    var color = Utils.ReadColor32Array(reader);
                    var textureData = Utils.Color32ToColorArray(color);
                    data.sections[i].textureData = textureData;
                }
                else
                {
                    data.sections[i].textureData = Utils.ReadColorArray(reader);
                }
                data.sections[i].outline = Utils.ReadVector3List(reader);
            }

            int nSplitters = reader.ReadInt32();
            data.splitters = new config.PolygonRiverSplitterData[nSplitters];
            for (int i = 0; i < nSplitters; ++i)
            {
                data.splitters[i] = new config.PolygonRiverSplitterData();
                data.splitters[i].startPos = Utils.ReadVector3(reader);
                data.splitters[i].endPos = Utils.ReadVector3(reader);
            }

            data.radius = reader.ReadSingle();
            data.textureSize = reader.ReadInt32();
            //data.materialPath = Utils.ReadString(reader);
            string guidOrPath = Utils.ReadString(reader);
            //temp fix,有些水引用了地图package之前的资源,只有临时处理一下
            //if (guidOrPath == "Assets/Framework/Script/Map/MapModuleRes/RuntimeRes/water/_default_water_mtl.mat")
            //{
            //    guidOrPath = "Assets/Res/map/water/_default_water_mtl.mat";
            //}
            //先测试guid是否是路径
            if (MapModuleResourceMgr.LoadMaterial(guidOrPath) != null)
            {
                //是path
                data.materialPath = guidOrPath;
            }
            else
            {
                //是无效的path或者guid
                var path = AssetDatabase.GUIDToAssetPath(guidOrPath);
                if (!string.IsNullOrEmpty(path))
                {
                    //是guid
                    data.materialPath = path;
                }
                else
                {
                    //是无效的path
                    var guid = AssetDatabase.AssetPathToGUID(guidOrPath);
                    data.materialPath = AssetDatabase.GUIDToAssetPath(guid);
                }
            }

            if (mMinorVersion >= 47)
            {
                data.hideLOD = reader.ReadInt32();
            }

            return data;
        }

        config.CircleBorderLayerData LoadCircleBorderLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCircleBorderData(reader);
            }

            var config = LoadMapLayerLODConfig(reader);

            int combineBorderLOD = 1;
            if (mMinorVersion >= 17)
            {
                combineBorderLOD = reader.ReadInt32();
            }
            var layer = new config.CircleBorderLayerData(layerID, layerName, offset, config, width, height, objects, combineBorderLOD);
            return layer;
        }

        config.CircleBorderData LoadCircleBorderData(BinaryReader reader)
        {
            var borderData = new config.CircleBorderData();
            LoadMapObjectBaseData(reader, borderData);
            borderData.modelTemplateID = reader.ReadInt32();

            if (mMinorVersion >= 16)
            {
                borderData.isAlwaysVisibleAtHigherLODs = reader.ReadBoolean();
            }
            return borderData;
        }

        config.RuinLayerData LoadRuinLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRuinData(reader);
            }

            int hordeCount = reader.ReadInt32();
            float cellWidth = reader.ReadSingle();
            float displayRadius = reader.ReadSingle();
            bool textVisible = true;
            if (mMinorVersion >= 32)
            {
                textVisible = reader.ReadBoolean();
            }

            var layer = new config.RuinLayerData(layerID, layerName, offset, null, width, height, objects, cellWidth, hordeCount, displayRadius, textVisible, null, true, false);
            return layer;
        }

        config.RuinData LoadRuinData(BinaryReader reader)
        {
            var ruinData = new config.RuinData();
            LoadMapObjectBaseData(reader, ruinData);
            ruinData.modelTemplateID = reader.ReadInt32();
            if (mMinorVersion >= 14)
            {
                int ruinType = reader.ReadInt32();
                ruinData.level = reader.ReadInt32();
                ruinData.type = ruinType;
                if (mMinorVersion >= 18)
                {
                    ruinData.objectType = Utils.ReadString(reader);
                }
                if (mMinorVersion >= 36)
                {
                    ruinData.radius = reader.ReadSingle();
                }
            }
            else if (mMinorVersion >= 13)
            {
                bool needScout = reader.ReadBoolean();
                if (needScout == true)
                {
                    ruinData.type = (int)RuinType.NeedScout;
                }
                else
                {
                    ruinData.type = (int)RuinType.Normal;
                }
            }
            return ruinData;
        }

        config.NavMeshLayerData LoadNavMeshLayerData(BinaryReader reader, int layerID, string layerName, Vector3 offset)
        {
            bool visible = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            bool oceanAreaWalkable = true;
            if (mMinorVersion >= 63)
            {
                oceanAreaWalkable = reader.ReadBoolean();
            }

            config.NavMeshData[] navMeshDatas = null;
            if (mMinorVersion == 1)
            {
                //1.1版本的数据,nav mesh是一个整体
                navMeshDatas = new config.NavMeshData[1];
                navMeshDatas[0] = new config.NavMeshData();

                int vertexCount = reader.ReadInt32();
                int indexCount = reader.ReadInt32();
                Vector3[] vertices = null;
                if (vertexCount > 0)
                {
                    vertices = new Vector3[vertexCount];
                    for (int i = 0; i < vertexCount; ++i)
                    {
                        vertices[i] = Utils.ReadVector3(reader);
                    }
                }

                int[] indices = null;
                if (indexCount > 0)
                {
                    indices = new int[indexCount];
                    for (int i = 0; i < indexCount; ++i)
                    {
                        indices[i] = reader.ReadInt32();
                    }
                }

                navMeshDatas[0].vertices = vertices;
                navMeshDatas[0].triangles = indices;
            }
            else if (mMinorVersion > 1)
            {
                //后续版本nav mesh分成了多个小块
                int blockCount = reader.ReadInt32();
                navMeshDatas = new config.NavMeshData[blockCount];
                for (int b = 0; b < blockCount; ++b)
                {
                    navMeshDatas[b] = new config.NavMeshData();
                    int vertexCount = reader.ReadInt32();
                    int indexCount = reader.ReadInt32();
                    Vector3[] vertices = null;
                    if (vertexCount > 0)
                    {
                        vertices = new Vector3[vertexCount];
                        for (int i = 0; i < vertexCount; ++i)
                        {
                            vertices[i] = Utils.ReadVector3(reader);
                        }
                    }

                    int[] indices = null;
                    if (indexCount > 0)
                    {
                        indices = new int[indexCount];
                        for (int i = 0; i < indexCount; ++i)
                        {
                            indices[i] = reader.ReadInt32();
                        }
                    }

                    navMeshDatas[b].vertices = vertices;
                    navMeshDatas[b].triangles = indices;

                    if (mMinorVersion >= 63)
                    {
                        navMeshDatas[b].triangleTypes = Utils.ReadUInt16Array(reader);
                        navMeshDatas[b].triangleStates = Utils.ReadBoolArray(reader);
                    }
                }
            }

            var layer = new config.NavMeshLayerData(layerID, layerName, offset, null, null, visible, width, height, navMeshDatas, oceanAreaWalkable);
            return layer;
        }

        void LoadMapLayers(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.map.mapLayers = new config.MapLayerData[n];
            for (int i = 0; i < n; ++i)
            {
                config.MapLayerData layer = null;
                var layerType = reader.ReadInt32();
                var layerID = reader.ReadInt32();
                var name = Utils.ReadString(reader);

                Vector3 offset = Vector3.zero;

                offset = Utils.ReadVector3(reader);

                bool active = true;
                if (mMinorVersion >= 6)
                {
                    active = reader.ReadBoolean();
                }

                if (layerType == MapLayerType.kSpriteTileLayer)
                {
                    layer = LoadSpriteTileLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kGridModelLayer)
                {
                    layer = LoadGridModelLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kModelLayer)
                {
                    layer = LoadModelLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kNavMeshLayer)
                {
                    layer = LoadNavMeshLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kNPCRegionLayer)
                {
                    layer = LoadNPCRegionLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kBlendTerrainLayer)
                {
                    layer = LoadBlendTerrainLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kEntitySpawnLayer)
                {
                    layer = LoadEntitySpawnLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRailwayLayer)
                {
                    layer = LoadRailwayLayer(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kLODLayer)
                {
                    layer = LoadLODLayer(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kCircleBorderLayer)
                {
                    layer = LoadCircleBorderLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kCollisionLayer)
                {
                    layer = LoadCollisionLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRegionLayer)
                {
                    layer = LoadRegionLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kCameraColliderLayer)
                {
                    layer = LoadCameraColliderLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRiverLayer)
                {
                    layer = LoadPolygonRiverLayerData(reader, layerID, name, offset);
                }
                else if (layerType == MapLayerType.kRuinLayer)
                {
                    layer = LoadRuinLayerData(reader, layerID, name, offset);
                }
                else
                {
                    Debug.Assert(false, "Unknown map layer!");
                }
                layer.active = active;

                mEditorData.map.mapLayers[i] = layer;
            }
        }

        void LoadSpriteTemplates(BinaryReader reader)
        {
            int nSpriteTemplates = reader.ReadInt32();
            var editorData = mEditorData as config.EditorMapData;
            editorData.spriteTemplates.templates = new config.SpriteTemplate[nSpriteTemplates];
            for (int i = 0; i < nSpriteTemplates; ++i)
            {
                editorData.spriteTemplates.templates[i] = LoadSpriteTemplate(reader);
            }
        }

        config.SpriteTemplate LoadSpriteTemplate(BinaryReader reader)
        {
            var id = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var width = reader.ReadInt16();
            var height = reader.ReadInt16();
            var color = Utils.ReadColor32(reader);
            var propertySetID = reader.ReadInt32();

            config.SpriteTemplate spriteTemplate = new config.SpriteTemplate(id, propertySetID);

            spriteTemplate.width = width;
            spriteTemplate.height = height;
            spriteTemplate.color = color;
            spriteTemplate.name = name;
            return spriteTemplate;
        }

        void LoadPrefabManagers(BinaryReader reader)
        {
            mEditorData.gridModelLayerPrefabManager = LoadPrefabManager(reader);
            mEditorData.terrainPrefabManager = LoadPrefabManager(reader);
            if (mMinorVersion >= 43)
            {
                mEditorData.modelLayerPrefabManager = LoadPrefabManager(reader);
                mEditorData.circleBorderLayerPrefabManager = LoadPrefabManager(reader);
                if (mMinorVersion <= 61)
                {
                    mEditorData.complexGridModelLayerPrefabManager = LoadPrefabManager(reader);
                }
            }
        }

        config.PrefabManager LoadPrefabManager(BinaryReader reader)
        {
            var prefabManager = new config.PrefabManager();
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();
            group.name = Utils.ReadString(reader);
            if (mMinorVersion >= 5)
            {
                group.color = Utils.ReadColor32(reader);
            }
            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs
                if (mMinorVersion >= 53)
                {
                    int subGroupPrefabCount = reader.ReadInt32();
                    group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                    for (int k = 0; k < subGroupPrefabCount; ++k)
                    {
                        string subgroupPrefabPath = Utils.ReadString(reader);
                        group.prefabPaths[i].subGroupPrefabPaths[k] = mPathMapper.Unmap(subgroupPrefabPath);
                    }
                }
            }
            return group;
        }

        void LoadModelProperties(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.modelPropertyManager.modelProperties = new KeyValuePair<string, int>[n];
            for (int i = 0; i < n; ++i)
            {
                string assetGUID = Utils.ReadString(reader);
                int propertySetID = reader.ReadInt32();
                mEditorData.modelPropertyManager.modelProperties[i] = new KeyValuePair<string, int>(assetGUID, propertySetID);
            }
        }

        void LoadModelTemplates(BinaryReader reader)
        {
            int nModelTemplates = reader.ReadInt32();
            mEditorData.modelTemplates.modelTemplates = new config.ModelTemplate[nModelTemplates];
            for (int i = 0; i < nModelTemplates; ++i)
            {
                mEditorData.modelTemplates.modelTemplates[i] = LoadMeshModelTemplate(reader);
            }
        }

        void LoadModelTemplateBase(config.ModelTemplate temp, BinaryReader reader)
        {
            var id = reader.ReadInt32();
            temp.SetID(id);

            temp.bounds = Utils.ReadBounds(reader);
            string prefabPath = Utils.ReadString(reader);
            temp.prefabPath = mPathMapper.Unmap(prefabPath);

            if (mMinorVersion >= 3)
            {
                temp.isTileModelTemplate = reader.ReadBoolean();
            }
            if (mMinorVersion >= 9)
            {
                temp.generated = reader.ReadBoolean();
            }
            if (mMinorVersion >= 33)
            {
                temp.preload = reader.ReadBoolean();
            }
        }

        config.ModelTemplate LoadMeshModelTemplate(BinaryReader reader)
        {
            var template = new config.ModelTemplate();
            LoadModelTemplateBase(template, reader);
            return template;
        }

        void LoadNavMeshObstacles(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            mEditorData.navMeshObstacles.obstacles = new config.NavMeshObstacle[n];
            for (int i = 0; i < n; ++i)
            {
                var ob = new config.NavMeshObstacle();
                ob.assetGUID = Utils.ReadString(reader);
                int nVertices = reader.ReadInt32();
                ob.outlineVertices = new Vector3[nVertices];
                for (int j = 0; j < nVertices; ++j)
                {
                    ob.outlineVertices[j] = Utils.ReadVector3(reader);
                }
                mEditorData.navMeshObstacles.obstacles[i] = ob;
            }
        }

        config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = false;
                int shaderLOD = 0;
                bool useRenderTexture = false;
                if (mMinorVersion >= 29)
                {
                    hideObject = reader.ReadBoolean();
                }
                if (mMinorVersion >= 34)
                {
                    shaderLOD = reader.ReadInt32();
                }
                if (mMinorVersion >= 49)
                {
                    useRenderTexture = reader.ReadBoolean();
                }

                config.lodConfigs[i] = new config.MapLayerLODConfigItem(MapLayerLODConfig.GetDefaultLODName(i), zoom, threshold, hideObject, shaderLOD, useRenderTexture, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadMapObstacles(BinaryReader reader)
        {
            LoadLocalObstacles(reader);
            if (mMinorVersion >= 25)
            {
                LoadGlobalObstacles(reader);
            }
        }

        void LoadLocalObstacles(BinaryReader reader)
        {
            var localObstacleManager = new config.MapLocalObstacleManager();
            mEditorData.localObstacleManager = localObstacleManager;
            localObstacleManager.regionWidth = reader.ReadSingle();
            localObstacleManager.regionHeight = reader.ReadSingle();
            if (localObstacleManager.regionWidth == 0)
            {
                localObstacleManager.regionWidth = MapModule.defaultFrontTileSize;
            }
            if (localObstacleManager.regionHeight == 0)
            {
                localObstacleManager.regionHeight = MapModule.defaultFrontTileSize;
            }
            int tileCount = reader.ReadInt32();
            localObstacleManager.tiles = new int[tileCount];
            for (int i = 0; i < tileCount; ++i)
            {
                localObstacleManager.tiles[i] = reader.ReadInt32();
            }

            int obstacleCount = reader.ReadInt32();
            localObstacleManager.obstacles = new config.MapPrefabObstacle[obstacleCount];
            for (int i = 0; i < obstacleCount; ++i)
            {
                config.MapPrefabObstacle obstacle = new config.MapPrefabObstacle();
                localObstacleManager.obstacles[i] = obstacle;

                obstacle.id = reader.ReadInt32();
                int vertexCount = reader.ReadInt32();
                obstacle.vertices = new Vector3[vertexCount];
                for (int k = 0; k < vertexCount; ++k)
                {
                    obstacle.vertices[k] = Utils.ReadVector3(reader);
                }
                int indexCount = reader.ReadInt32();
                obstacle.triangleIndices = new int[indexCount];
                for (int k = 0; k < indexCount; ++k)
                {
                    obstacle.triangleIndices[k] = reader.ReadInt32();
                }
            }

            localObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
        }

        void LoadGlobalObstacles(BinaryReader reader)
        {
            var globalObstacleManager = new config.MapGlobalObstacleManager();
            mEditorData.globalObstacleManager = globalObstacleManager;
            globalObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
            globalObstacleManager.obstacleVertices = Utils.ReadVector3Array(reader);
            globalObstacleManager.obstacleIndices = Utils.ReadIntArray(reader);
        }

        List<config.RuinSetting> LoadRuinObjectTypes(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            List<config.RuinSetting> ruinObjectTypes = new List<config.RuinSetting>(n);
            for (int i = 0; i < n; ++i)
            {
                string name = Utils.ReadString(reader);
                Color color = Color.black;
                if (mMinorVersion >= 48)
                {
                    color = Utils.ReadColor(reader);
                }
                PropertyDatas properties = new PropertyDatas(null);
                if (mMinorVersion >= 59)
                {
                    properties = Utils.ReadProperties(reader);
                }
                float colliderRadius = 5.0f;
                if (mMinorVersion >= 60)
                {
                    colliderRadius = reader.ReadSingle();
                }
                ruinObjectTypes.Add(new config.RuinSetting(name, color, properties, colliderRadius, null, new config.RuinSpecialRegionSetting()));
            }

            return ruinObjectTypes;
        }

        config.DetailSpritesSetting LoadDetailSprites(BinaryReader reader)
        {
            config.DetailSpritesSetting ds = new config.DetailSpritesSetting();
            ds.alpha0Height = reader.ReadSingle();
            ds.alpha1Height = reader.ReadSingle();
            ds.crossfading = reader.ReadBoolean();
            ds.updateScale = reader.ReadBoolean();
            if (mMinorVersion >= 39)
            {
                ds.horizontaolGridCount = reader.ReadInt32();
                ds.verticalGridCount = reader.ReadInt32();
            }
            if (mMinorVersion >= 38)
            {
                int nGroups = reader.ReadInt32();
                ds.groups = new config.DetailSpriteGroup[nGroups];
                for (int i = 0; i < nGroups; ++i)
                {
                    var group = new config.DetailSpriteGroup();
                    ds.groups[i] = group;
                    int nSprites = reader.ReadInt32();
                    group.detailSpritesGUIDs = new string[nSprites];
                    for (int k = 0; k < nSprites; ++k)
                    {
                        group.detailSpritesGUIDs[k] = Utils.ReadString(reader);
                    }
                    group.name = Utils.ReadString(reader);
                    group.color = Utils.ReadColor32(reader);
                }
            }
            else
            {
                var group = new config.DetailSpriteGroup();
                ds.groups = new config.DetailSpriteGroup[1];
                ds.groups[0] = group;
                int spriteCount = reader.ReadInt32();
                group.detailSpritesGUIDs = new string[spriteCount];
                for (int i = 0; i < spriteCount; ++i)
                {
                    group.detailSpritesGUIDs[i] = Utils.ReadString(reader);
                }
            }
            return ds;
        }

        config.GridRegionEditorSetting LoadGridRegionEditorSetting(BinaryReader reader)
        {
            config.GridRegionEditorSetting setting = new config.GridRegionEditorSetting();
            setting.gridWidth = reader.ReadSingle();
            setting.gridHeight = reader.ReadSingle();
            setting.horizontalGridCount = reader.ReadInt32();
            setting.verticalGridCount = reader.ReadInt32();
            if (mMinorVersion >= 46)
            {
                setting.showGrid = reader.ReadBoolean();
            }
            //only placeholder
            Utils.ReadRectInt(reader);
            setting.grids = new int[setting.verticalGridCount, setting.horizontalGridCount];
            for (int i = 0; i < setting.verticalGridCount; ++i)
            {
                for (int j = 0; j < setting.horizontalGridCount; ++j)
                {
                    setting.grids[i, j] = reader.ReadInt32();
                }
            }
            int nTemplates = reader.ReadInt32();
            setting.gridTemplates = new config.GridTemplate[nTemplates];
            for (int i = 0; i < nTemplates; ++i)
            {
                var template = new config.GridTemplate();
                template.name = Utils.ReadString(reader);
                template.color = Utils.ReadColor(reader);
                template.type = reader.ReadInt32();
                setting.gridTemplates[i] = template;
            }

            return setting;
        }

        void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mPathMapper.pathToGuid[path] = guid;
            }
        }

        config.EditorMapData mEditorData;
        int mMinorVersion;
        PathMapper mPathMapper = new PathMapper();
    }
}
#endif
