﻿#if !UNITY_WEBGL
using System;
using System.Collections.Concurrent;
using System.Net.Sockets;
using Net.Common;
using Net.Tcp.Common;

namespace Net.Tcp.Server
{
    public sealed class TCPServerHandler : IHandlerServer
    {
        bool m_Closed = false;
        ConcurrentDictionary<IConnection, IHandlerMessage> m_Connections = new ConcurrentDictionary<IConnection, IHandlerMessage>();
        void IHandlerServer.Close()
        {
            m_Closed = true;
            var tmp = m_Connections.ToArray();
            m_Connections.Clear();
            foreach (var kv in tmp)
            {
                kv.Key.Close();
            }
        }

        void IHandlerServer.Remove(IConnection conn)
        {
            IHandlerMessage tmp;
            m_Connections.TryRemove(conn, out tmp);
        }

        void IHandlerServer.Add(IConnection conn)
        {
            if (m_Closed)
            {
                conn.Close();
                return;
            }
            m_Connections.TryAdd(conn, conn.<PERSON>);
        }
        Func<Func<IHandlerMessage,IConnection>, IConnection> HandleConnFunc = null;
        public TCPServerHandler H<T>() where T : class, IHandlerMessage, new()
        {
            HandleConnFunc = HandleConnFunction<T>;
            return this;
        }
        public TCPServerHandler H(Func<Func<IHandlerMessage, IConnection>, IConnection> handleFunc)
        {
            HandleConnFunc = handleFunc;
            return this;
        }
        IConnection HandleConnFunction<T>(Func<IHandlerMessage, IConnection> newFunc) where T : class, IHandlerMessage, new()
        {
            var handler = new T();
            var conn = newFunc.Invoke(handler);
            return conn;
        }
        IHandlerMessage IHandlerServer.HandleAcceptConnected(NewConnFunc newConnFunc)
        {
            if (m_Closed)
            {
                return null;
            }
            var conn = HandleConnFunc.Invoke((h) => newConnFunc.Invoke(h, ((IHandlerServer)this).Remove));
            ((IHandlerServer)this).Add(conn);
            conn.Initialize();
            return conn.Handler;
        }
    }
}

#endif