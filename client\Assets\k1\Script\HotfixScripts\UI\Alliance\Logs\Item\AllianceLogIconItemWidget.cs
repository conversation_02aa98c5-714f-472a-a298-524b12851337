﻿using System.Collections.Generic;
using System.Text;
using cspb;
using Common;
using Logic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;
using LocalizationMgr = TFW.Localization.LocalizationMgr;
using Public;
using System;
using TFW.UI;
using TFW;
using DeepUI;
using System.Linq;
using Cysharp.Threading.Tasks;

namespace UI.Alliance
{
    public enum ActionType
    {
        player,
        union,
        teach,
        build,
    }

    public class AllianceLogIconItemWidgetData 
    {
        public ActionType actionType;
        #region  ���
        public long id;
        public UnifyPlayerHead head;
        public long power;
        /// <summary>
        /// ���������˵ȼ�
        /// </summary>
        public int unionLevel;

        public Action closeAction;

        #endregion

        #region  �Ƽ�

        /// <summary>
        /// �����Ƽ�id
        /// </summary>
        public int groupId;

        /// <summary>
        /// �����Ƽ��ȼ�
        /// </summary>
        public int teachLevel;

        #endregion

        #region ����

        public AllianceInfo union;

        #endregion
    }

    public class AllianceLogIconItemWidget : UIWidgetBase
    {
        GameObject headObj;
        TFWText name;
        GameObject powerRoot;
        TFWText power;
        GameObject unionLevelIconRoot;
        TFWImage unionLevelIcon;
        GameObject coinRoot;
        TFWText spendCoin;
        GameObject diamondRoot;
        TFWText spendDiamond;
        GameObject unionFlagObj;
        TFWImage buildIcon;
        TFWImage teachIcon;

        AllianceLogIconItemWidgetData data;

        public AllianceLogIconItemWidget(GameObject root) : base(root)
        {

        }

        public override void OnInit()
        {
            base.OnInit();
            headObj = GetChild("Head/Head_k1");
            name = GetComponent<TFWText>("NameRoot/Name");
            powerRoot = GetChild("PC/Power");
            power = GetComponent<TFWText>("PC/Power/Text");
            unionLevelIconRoot = GetChild("NameRoot/IconRoot");
            unionLevelIcon = GetComponent<TFWImage>("NameRoot/IconRoot/Icon");
            coinRoot = GetChild("PC/Coin");
            spendCoin = GetComponent<TFWText>("PC/Coin/Text");
            diamondRoot = GetChild("PC/Diamond");
            spendDiamond = GetComponent<TFWText>("PC/Diamond/Text");
            unionFlagObj = GetChild("IconAllianceSymbol_k1");
            buildIcon = GetComponent<TFWImage>("TerritoryIcon");
            teachIcon = GetComponent<TFWImage>("AllianceSkillIcon");
            diamondRoot.SetActive(false);
            GetChild("PeopleNum").SetActive(false);
        }

        public void SetData(AllianceLogIconItemWidgetData data) 
        {
            this.data = data;
            ChangeShow();
            switch (data.actionType) 
            {
                case ActionType.player:
                    ShowPlayer();
                    break;
                case ActionType.union:
                    ShowUnion();
                    break;
                case ActionType.teach:
                    ShowTeach();
                    break;
            }
        }

        void ShowPlayer() 
        {
            if (!headObj)
                return;

            HeadIconWidget head = new HeadIconWidget(headObj);
            head.InitData(data.head);
            head.AddListener(EventTriggerType.Click, (x, y) =>
            {
                if (data != null && data.id != LPlayer.I.PlayerID)
                {
                    var popData = new UIOtherPlayerIdData();
                    popData.PlayerID = data.id;
                    popData.chatCallback = data.closeAction;
                    PopupManager.I.ShowLayer<UIOtherPlayerData>(popData);
                    LPlayer.I.TargetPlayerBaseTODO(popData.PlayerID, LPlayer.I.ServerId, (info) =>
                    {
                        if (info != null)
                        {
                            popData.playerInfo = info;
                            PopupManager.I.ShowPanel<UIOtherPlayerData>(popData);
                        }
                    });
                }
            });

            if (data.head != null)
                name.text = data.head.name;
            power.text = UIStringUtils.FormatIntegerByLanguage(data.power);
            Cfg.C.CUnionClass.GetConfigAsync(data.unionLevel).ContinueWith(
                cfg=>
                {
                    if (cfg != null)
                        UITools.SetImage(unionLevelIcon, cfg.DisplayKey, "Alliance");
                });

        }

        void ShowUnion() 
        {
            UIHelper.SetAllianceFlag(unionFlagObj, LAllianceMain.I.GetUnionFlagByFlagID(data.union.Flag));
            name.text = string.Format("<color=#FFD675FF>[{0}]</color> <color=#66E8FF>[{1}]</color>", data.union.NickName, data.union.Name);
        }

        void ShowTeach() 
        {
            if (Game.Data.GameData.I.AllianceTechData.techGroupCacheData.TryGetValue(data.groupId, out var cfgDic))
            {
                var cfg = cfgDic.First().Value;
                if (cfg != null)
                {
                    name.text = $"{LocalizationMgr.Get(cfg.Name)} LV{data.teachLevel}";
                    UITools.SetImageBySpriteName(teachIcon, cfg.Icon);
                }
            }
        }

        void ChangeShow() 
        {
            headObj.SetActive(data.actionType == ActionType.player);
            unionFlagObj.SetActive(data.actionType == ActionType.union);
            powerRoot.gameObject.SetActive(data.actionType == ActionType.player);
            buildIcon.gameObject.SetActive(data.actionType == ActionType.build);
            teachIcon.gameObject.SetActive(data.actionType == ActionType.teach);
            unionLevelIconRoot.gameObject.SetActive(data.actionType == ActionType.player);
        }
    }
}