﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    public class DetailSpriteGroupEditor
    {
        public DetailSpriteGroupEditor(DetailSpriteGroup group)
        {
            mGroup = group;
            if (group.detailSpritesGUIDs != null)
            {
                mPrefabCount = group.detailSpritesGUIDs.Length;
                mPrefabs = new GameObject[mPrefabCount];
                for (int i = 0; i < mPrefabCount; ++i)
                {
                    if (!string.IsNullOrEmpty(group.detailSpritesGUIDs[i]))
                    {
                        mPrefabs[i] = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GUIDToAssetPath(group.detailSpritesGUIDs[i]));
                    }
                }

                mPrefabNames = new string[mPrefabCount];
                for (int k = 0; k < mPrefabCount; ++k)
                {
                    mPrefabNames[k] = $"Prefab {k}";
                }
            }
        }

        public void Draw()
        {
            EditorGUILayout.BeginVertical("GroupBox");
            mGroup.color = EditorUtils.Color32Field("Color", "颜色", mGroup.color);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Sprite Prefab Count", mPrefabCount.ToString());
            if (GUILayout.Button("Change"))
            {
                var dlg = EditorUtils.CreateInputDialog("Set Sprite Prefab Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Prefab Count", "", "32"),
                };
                dlg.Show(items, OnClickChangeCount);
            }
            EditorGUILayout.EndHorizontal();

            for (int i = 0; i < mPrefabCount; ++i)
            {
                var prefab = EditorGUILayout.ObjectField(mPrefabNames[i], mPrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular)
                {
                    mPrefabs[i] = prefab;
                    mGroup.detailSpritesGUIDs[i] = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(mPrefabs[i]));
                }
            }
            EditorGUILayout.EndVertical();
        }

        public string CheckValidation()
        {
            if (mPrefabCount <= 0)
            {
                return $"Invalid prefab count!";
            }
            Debug.Assert(mPrefabCount == mPrefabs.Length);
            for (int i = 0; i < mPrefabCount; ++i)
            {
                if (mPrefabs[i] == null)
                {
                    return $"Prefab {i} can't be null";
                }

                var renderer = mPrefabs[i].GetComponent<Renderer>();
                if (renderer == null || renderer.sharedMaterials.Length > 1)
                {
                    return $"{mPrefabs[i].name} material not found or material count is greater than 1";
                }
            }

            var mtl = mPrefabs[0].GetComponent<Renderer>().sharedMaterial;
            for (int i = 1; i < mPrefabCount; ++i)
            {
                var mtl1 = mPrefabs[i].GetComponent<Renderer>().sharedMaterial;
                if (mtl != mtl1)
                {
                    return $"{mPrefabs[i].name} use different material";
                }
            }
            return "";
        }

        public List<string> GetPrefabPaths()
        {
            List<string> prefabPaths = new List<string>(mPrefabCount);
            for (int i = 0; i < mPrefabCount; ++i)
            {
                prefabPaths.Add(AssetDatabase.GetAssetPath(mPrefabs[i]));
            }
            return prefabPaths;
        }

        bool OnClickChangeCount(List<InputDialog.Item> param)
        {
            int prefabCount;
            Utils.ParseInt((param[0] as InputDialog.StringItem).text, out prefabCount);
            if (prefabCount <= 0)
            {
                return false;
            }

            if (mPrefabNames == null || mPrefabs == null || prefabCount != mPrefabs.Length)
            {
                GameObject[] newPrefabs = new GameObject[prefabCount];
                int n = Mathf.Min(prefabCount, mPrefabCount);
                mGroup.detailSpritesGUIDs = new string[prefabCount];
                for (int i = 0; i < n; ++i)
                {
                    newPrefabs[i] = mPrefabs[i];
                    if (mPrefabs[i] != null)
                    {
                        mGroup.detailSpritesGUIDs[i] = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(newPrefabs[i]));
                    }
                }

                mPrefabs = newPrefabs;
                mPrefabCount = prefabCount;

                mPrefabNames = new string[prefabCount];
                for (int k = 0; k < prefabCount; ++k)
                {
                    mPrefabNames[k] = $"Prefab {k}";
                }
            }

            return true;
        }

        public DetailSpriteGroup group { get { return mGroup; } }

        int mPrefabCount = 0;
        GameObject[] mPrefabs;
        string[] mPrefabNames;
        DetailSpriteGroup mGroup;
    }
}


#endif