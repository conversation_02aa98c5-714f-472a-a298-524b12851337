﻿ 



 
 



using UnityEngine;


namespace TFW.Map
{
    public class RectangleMoveDebugger : MonoBehaviour
    {
        static GameObject mTestGameObject;

        public static void CreateTestCase(Vector3 pos)
        {
            if (mTestGameObject == null)
            {
                mTestGameObject = new GameObject();
                var debugger = mTestGameObject.AddComponent<RectangleMoveDebugger>();
                debugger.mAnchorPos = pos;
                mTestGameObject.transform.position = pos;
            }
            else
            {
                var debugger = mTestGameObject.GetComponent<RectangleMoveDebugger>();
                debugger.MoveRectangle(pos);
            }
        }

        void OnDrawGizmos()
        {
            DrawRect(new Rect(mAnchorPos.x, mAnchorPos.z, mRectWidth, mRectHeight), Color.blue);

            DrawRect(mHideXRect, Color.red);
            DrawRect(mHideZRect, Color.red);

            DrawRect(mShowXRect, Color.green);
            DrawRect(mShowZRect, Color.green);
        }

        void DrawRect(Rect r, Color c)
        {
            Gizmos.color = c;
            var center = r.center;
            var size = r.size;
            Gizmos.DrawWireCube(new Vector3(center.x, 0, center.y), new Vector3(size.x, 0, size.y));
        }

        void MoveRectangle(Vector3 newAnchorPos)
        {
            mHideXRect = new Rect();
            mHideZRect = new Rect();
            mShowXRect = new Rect();
            mShowZRect = new Rect();

            var delta = newAnchorPos - mAnchorPos;
            if (delta.x > 0)
            {
                //视野向右移动
                var dataX = mAnchorPos.x + mRectWidth;
                var dataZ = mAnchorPos.z;
                float xOffset = Mathf.Abs(delta.x);
                mHideXRect = new Rect(mAnchorPos.x, mAnchorPos.z, xOffset, mRectHeight);
                mShowXRect = new Rect(dataX, dataZ + delta.z, xOffset, mRectHeight);
            }
            else if (delta.x < 0)
            {
                //视野向左移动
                float xOffset = Mathf.Abs(delta.x);
                var dataX = mAnchorPos.x - xOffset;
                var dataZ = mAnchorPos.z;
                mHideXRect = new Rect(mAnchorPos.x + mRectWidth - xOffset, mAnchorPos.z, xOffset, mRectHeight);
                mShowXRect = new Rect(dataX, dataZ + delta.z, xOffset, mRectHeight);
            }
            if (delta.z > 0)
            {
                //视野向下移动
                float zOffset = Mathf.Abs(delta.z);
                var dataX = mAnchorPos.x;
                var dataZ = mAnchorPos.z + mRectHeight;
                mHideZRect = new Rect(mAnchorPos.x, mAnchorPos.z, mRectWidth, zOffset);
                mShowZRect = new Rect(dataX + delta.x, dataZ, mRectWidth, zOffset);
            }
            else if (delta.z < 0)
            {
                //视野向上移动
                float zOffset = Mathf.Abs(delta.z);
                var dataX = mAnchorPos.x;
                var dataZ = mAnchorPos.z - zOffset;
                mHideZRect = new Rect(mAnchorPos.x, mAnchorPos.z + mRectHeight - zOffset, mRectWidth, zOffset);
                mShowZRect = new Rect(dataX + delta.x, dataZ, mRectWidth, zOffset);
            }

            mAnchorPos = newAnchorPos;
        }

        Rect mHideXRect = new Rect();
        Rect mHideZRect = new Rect();
        Rect mShowXRect = new Rect();
        Rect mShowZRect = new Rect();
        float mRectWidth = 10.0f;
        float mRectHeight = 10.0f;
        Vector3 mAnchorPos;
    }
}
