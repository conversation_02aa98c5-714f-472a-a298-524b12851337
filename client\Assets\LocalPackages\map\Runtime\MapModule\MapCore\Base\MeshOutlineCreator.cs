﻿#if UNITY_EDITOR

#define DISPLAY_NAME

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Linq;

namespace TFW.Map
{
    //创建mesh的边界线
    public class MeshOutlineCreator
    {
        class Edge
        {
            public Edge(Vector3 s, Vector3 e) {
                start = s;
                end = e;
            }

            public Vector3 start;
            public Vector3 end;
        }

        public class MeshInfo
        {
            public MeshInfo(Mesh mesh, Transform transform)
            {
                this.mesh = mesh;
                this.transform = transform;
            }

            public Mesh mesh;
            public Transform transform;
        }

        public List<Vector3> CombineMeshesAndCreate(List<MeshInfo> meshes, Vector2 validHeightRange)
        {
            StopWatchWrapper w = new StopWatchWrapper();

            EditorUtility.DisplayProgressBar("Combine Tile Mesh", "Please wait", 0.15f);
            w.Start();
            OptimizedMeshCombiner combiner = new OptimizedMeshCombiner(0.1f);
            for (int i = 0; i < meshes.Count; ++i)
            {
                combiner.AddMesh(meshes[i].mesh.vertices, meshes[i].mesh.triangles, meshes[i].transform.localToWorldMatrix);
            }
            combiner.Combine(true, out Vector3[] vertices, out int[] indices);

            double t = w.Stop();
            Debug.Log($"Combine Mesh Cost: {t} seconds");

            return DoCreate(vertices, indices, validHeightRange, 0.1f);
        }

        public List<Vector3> Create(Mesh mesh, Vector2 validHeightRange)
        {
            return DoCreate(mesh.vertices, mesh.triangles, validHeightRange, 0);
        }

        List<Vector3> DoCreate(Vector3[] vertices, int[] indices, Vector2 validHeightRange, float distanceError)
        {
            StopWatchWrapper w = new StopWatchWrapper();

            EditorUtility.DisplayProgressBar("Remove Vertices Out Of Height Range", "Please wait", 0.35f);
            w.Start();
            //删除高度在heightThreshold之下的顶点
            RemoveVerticesOutOfHeightRange(vertices, indices, validHeightRange, out var validVertices, out var validIndices);
            double time = w.Stop();
            Debug.Log($"RemoveVerticesOutOfHeightRange Cost: {time} seconds");

            EditorUtility.DisplayProgressBar("Generate Edge List", "Please wait", 0.55f);
            w.Start();
            //生成edge list
            int triangleCount = validIndices.Count / 3;
            for (int t = 0; t < triangleCount; ++t)
            {
                int a = validIndices[t * 3];
                int b = validIndices[t * 3 + 1];
                int c = validIndices[t * 3 + 2];

                AddEdge(validVertices[a], validVertices[b]);
                AddEdge(validVertices[a], validVertices[c]);
                AddEdge(validVertices[b], validVertices[c]);
            }
            time = w.Stop();
            Debug.Log($"Create Edge List Cost: {time} seconds");

            EditorUtility.DisplayProgressBar("Create Outline", "Please wait", 0.6f);
            w.Start();
            Vector3 firstPoint = FindMinZPoint(validVertices);
            List<Vector3> outline = new List<Vector3>();
            outline.Add(firstPoint);
            Vector3 currentPoint = firstPoint;
            Vector3 previousPoint = currentPoint - new Vector3(1, 0, 0);
            Vector2 firstPoint2 = Utils.ToVector2(firstPoint);
            while (true)
            {
                if (outline.Count >= validVertices.Count)
                {
                    Debug.LogError("Create Outline Failed!");
                    break;
                }

                mPointEdgeList.TryGetValue(currentPoint, out var edgeList);
                var nextPoint = GetMaxAngleEdgePoint(previousPoint, currentPoint, edgeList);
                if (!Utils.Approximately(Utils.ToVector2(nextPoint), firstPoint2, distanceError))
                {
                    outline.Add(nextPoint);
                }
                else
                {
                    break;
                }
                previousPoint = currentPoint;
                currentPoint = nextPoint;
            }
            time = w.Stop();
            Debug.Log($"Create Outline Cost: {time} seconds");

            return outline;
        }

        void RemoveVerticesOutOfHeightRange(Vector3[] vertices, int[] indices, Vector2 validHeightRange, out List<Vector3> validVertices, out List<int> validIndices)
        {
            Dictionary<Vector3, int> verticesIndex = new Dictionary<Vector3, int>();

            validVertices = new List<Vector3>();
            for (int i = 0; i < vertices.Length; ++i)
            {
                var pos = vertices[i];
                if (pos.y >= validHeightRange.x && pos.y <= validHeightRange.y)
                {
                    validVertices.Add(pos);
                    verticesIndex[pos] = validVertices.Count - 1;
                }
            }

            validIndices = new List<int>();
            
            int triangleCount = indices.Length / 3;
            for (int t = 0; t < triangleCount; ++t)
            {
                int a = indices[t * 3];
                int b = indices[t * 3 + 1];
                int c = indices[t * 3 + 2];

                var posA = vertices[a];
                var posB = vertices[b];
                var posC = vertices[c];

                if (posA.y < validHeightRange.x || posA.y > validHeightRange.y ||
                    posB.y < validHeightRange.x || posB.y > validHeightRange.y ||
                    posC.y < validHeightRange.x || posC.y > validHeightRange.y)
                {
                    continue;
                }

                verticesIndex.TryGetValue(posA, out int newA);
                verticesIndex.TryGetValue(posB, out int newB);
                verticesIndex.TryGetValue(posC, out int newC);
                Debug.Assert(newA >= 0 && newB >= 0 && newC >= 0);
                validIndices.Add(newA);
                validIndices.Add(newB);
                validIndices.Add(newC);
            }
        }

        Vector3 FindMinZPoint(List<Vector3> points)
        {
            float minZ = points[0].z;
            int minIndex = 0;
            for (int i = 1; i < points.Count; ++i)
            {
                if (points[i].z < minZ)
                {
                    minZ = points[i].z;
                    minIndex = i;
                }
            }
            return points[minIndex];
        }

        Vector3 GetMaxAngleEdgePoint(Vector3 previousPoint, Vector3 currentPoint, List<Edge> edgeList)
        {
            float maxAngle = -1;
            Vector3 maxEdgePoint = Vector3.zero;
            for (int i = 0; i < edgeList.Count; ++i)
            {
                float angle = GetAngle(previousPoint, currentPoint, edgeList[i].end);
                if (angle > maxAngle)
                {
                    maxAngle = angle;
                    maxEdgePoint = edgeList[i].end;
                }
            }
            return maxEdgePoint;
        }

        /*
         *                     c 
         *                    /
         *                   /
         *                  /
         *                 / 
         * a--------------b
         * angle的角度可以大于180，返回0到360的值
         */
        float GetAngle(Vector3 a, Vector3 b, Vector3 c)
        {
            var ab = a - b;
            var cb = c - b;
            ab.Normalize();
            cb.Normalize();
            float angle = Vector3.Angle(ab, cb);
            var cross = Vector3.Cross(ab, cb);
            if (cross.y < 0)
            {
                angle = 360 - angle;
            }
            return angle;
        }

        void AddEdge(Vector3 a, Vector3 b)
        {
            mPointEdgeList.TryGetValue(a, out var edgeAList);
            if (edgeAList == null)
            {
                edgeAList = new List<Edge>();
                mPointEdgeList[a] = edgeAList;
            }
            edgeAList.Add(new Edge(a, b));

            mPointEdgeList.TryGetValue(b, out var edgeBList);
            if (edgeBList == null)
            {
                edgeBList = new List<Edge>();
                mPointEdgeList[b] = edgeBList;
            }
            edgeBList.Add(new Edge(b, a));
        }

        Dictionary<Vector3, List<Edge>> mPointEdgeList = new Dictionary<Vector3, List<Edge>>();
    }
}


#endif