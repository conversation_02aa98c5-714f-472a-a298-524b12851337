﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class SplineObjectManager
    {
        public SplineObjectManager(MapLayerLODConfig lodConfig, float displayRadius)
        {
            mLODConfig = lodConfig;
            mDisplayRadius = displayRadius;
            mEditorObject = new GameObject("SplineEditor");
            mEditorObject.transform.SetParent(Map.currentMap.root.transform);
            mEditorObject.AddComponent<DisableKeyboardDelete>();
            mEditorObject.AddComponent<SplineEditor>();
            mEditorObject.AddComponent<StaticLocalObject>();
        }

        public void OnDestroy()
        {
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                mSplineObjects[i].OnDestroy();
            }
            Object.DestroyImmediate(mEditorObject);
        }

        //inside:mesh是向内还是向外扩展
        public SplineObject CreateSplineObject(List<SplineObject.ControlPoint> controlPoints, string materialPath, float ratio, SplineObject.Attribute attributes, List<SplineObject.ExportSplineSegmentInfo> segmentsInfo, int tileCount, string name, SplineObject.RiverData riverData)
        {
            var obj = new SplineObject(++mNextSplineObjectID, controlPoints, null, null, materialPath, ratio, attributes, segmentsInfo, mVisible, mDisplayRadius, tileCount, name, riverData);
            mSplineObjects.Add(obj);
            return obj;
        }

        public void RemoveSpline(int id)
        {
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (mSplineObjects[i].id == id)
                {
                    mSplineObjects[i].OnDestroy();
                    mSplineObjects.RemoveAt(i);
                    break;
                }
            }
        }

        public void RemoveAllSplines()
        {
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                mSplineObjects[i].OnDestroy();
            }
            mSplineObjects.Clear();
        }

        public void ApplySplineObjectTangent(int id, int controlPointIndex, int srcIdx, int targetIdx)
        {
            var spline = FindSplineObject(id);
            if (spline != null)
            {
                spline.ApplyTangent(controlPointIndex, srcIdx, targetIdx);
            }
        }

        public SplineObject FindSplineObject(int id)
        {
            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (mSplineObjects[i].id == id)
                {
                    return mSplineObjects[i];
                }
            }
            return null;
        }

        public GameObject root { get { return mEditorObject; } }
        public List<SplineObject> splines { get { return mSplineObjects; } }
        public int splineObjectCount { get { return mSplineObjects.Count; } }
        public float maxExportSegmentLength { get { return mMaxExportSegmentLength; } set { mMaxExportSegmentLength = value; } }
        public MapLayerLODConfig lodConfig { get { return mLODConfig; } }
        public bool visible
        {
            get { return mVisible; }
            set
            {
                if (mVisible != value)
                {
                    mVisible = value;
                    for (int i = 0; i < mSplineObjects.Count; ++i)
                    {
                        mSplineObjects[i].rootGameObject.SetActive(mVisible);
                    }
                }
            }
        }
        public float displayRadius
        {
            get { return mDisplayRadius; }
            set
            {
                mDisplayRadius = value;
                for (int i = 0; i < mSplineObjects.Count; ++i)
                {
                    mSplineObjects[i].SetDisplayRadius(mDisplayRadius);
                }
            }
        }
        public TangentMoveType tangentMoveType { get; set; } = TangentMoveType.RotateAndScale;
        public string riverPrefabFolder { get { return mRiverPrefabFolder; } set { mRiverPrefabFolder = value; } }
        public bool generateObjFile { get { return mGenerateObjFile; } set { mGenerateObjFile = value; } }

        int mNextSplineObjectID = 0;
        List<SplineObject> mSplineObjects = new List<SplineObject>();
        GameObject mEditorObject;
        float mMaxExportSegmentLength = 0f;
        float mDisplayRadius;
        MapLayerLODConfig mLODConfig;
        bool mVisible = true;
        //river prefab输出文件夹
        string mRiverPrefabFolder;
        bool mGenerateObjFile = true;
    }
}

#endif