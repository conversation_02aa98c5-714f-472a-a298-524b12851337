﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class SplitFogLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var editorMapData = Map.currentMap.data as EditorMapData;
            PrepareLoading();
            LoadSetting(reader);
            LoadPrefabManager(reader, version);
            var layerData = LoadLayerData(reader, AllocateID(), version);

            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            var groupCount = splitFogPrefabManager.groupCount;
            if (groupCount > 0)
            {
                layerData.fogTilePrefabPaths = new string[16];
                var group = splitFogPrefabManager.GetOrCreateGroup(0);
                for (int i = 0; i < 16; ++i)
                {
                    layerData.fogTilePrefabPaths[i] = group.GetPrefabPath(i, 0);
                }
            }

            reader.Close();

            var layer = new SplitFogLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static config.SplitFogLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            float fogHeight = reader.ReadSingle();
            string fogLOD1PrefabPath = Utils.ReadString(reader);
            string selectionMaterialPath = Utils.ReadString(reader);
            string fogMaskTexPropertyName = Utils.ReadString(reader);

            var objects = Utils.ReadIntArray(reader);

            var config = LoadMapLayerLODConfig(reader, version);
            var layer = new config.SplitFogLayerData(layerID, layerName, layerOffset, config, rows, cols, tileWidth, tileHeight, objects, null, fogHeight, fogLOD1PrefabPath, selectionMaterialPath, fogMaskTexPropertyName);
            layer.active = active;
            return layer;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                var name = Utils.ReadString(reader);

                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader, Version version)
        {
            var prefabManagerData = LoadPrefabManagerData(reader, version);
            var prefabManager = (Map.currentMap.data as EditorMapData).editorSplitFogPrefabManager;
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, false, false, false);

            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            int nGroups = prefabManager.groupCount;
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                int nPrefabs = group.count;
                for (int k = 0; k < nPrefabs; ++k)
                {
                    splitFogPrefabManager.SetGroupPrefabByID(group.groupID, k, 0, group.GetPrefabPath(k));
                    var subGroupPrefabPaths = group.GetSubGroupPrefabPaths(k);
                    if (subGroupPrefabPaths != null)
                    {
                        int n = subGroupPrefabPaths.Length;
                        for (int x = 1; x < n; ++x)
                        {
                            splitFogPrefabManager.SetGroupPrefabByID(group.groupID, k, x, subGroupPrefabPaths[x]);
                        }
                    }
                }
            }
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader, Version version)
        {
            int nextGroupID = -1;
            if (version.minorVersion >= 2)
            {
                nextGroupID = reader.ReadInt32();
            }
            var prefabManager = new config.PrefabManager();
            prefabManager.nextGroupID = nextGroupID;
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader, version, i);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader, Version version, int groupIndex)
        {
            config.PrefabGroup group = new config.PrefabGroup();

            if (version.minorVersion >= 2)
            {
                group.id = reader.ReadInt32();
            }
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mLoadPathMapper.Unmap(subgroupPrefabPath);
                }
            }
            return group;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPathMapper = new PathMapper();
            mLoadPrefabPathStringTable = new List<string>();
        }

        static PathMapper mLoadPathMapper;
        static List<string> mLoadPrefabPathStringTable;
    }
}

#endif