﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using Net.Common;
using System;
using System.Net;
using System.Net.Sockets;
using UnityEngine;

namespace Net.Tcp.Common
{
    public sealed class TCPConnection : IConnection, IIOCP
    {
        public void Close()
        {
            if (Running)
            {
                Running = false;
                IOCPConn.Close(Socket);
                Socket = null;
                var close = m_Close;
                m_Close = null;
                close?.Invoke(this);
                m_Close = null;
                Handler?.HandleDisconnected();
                Handler?.HandleClose();
                Handler = null;
            }
        }

        public TCPConnection(Socket sock, IHandlerMessage handler,IPack pack, Action<IConnection> close, byte[] kiv, byte cccflag)
        {
            Running = false;
            m_Initialized = false;
            Handler = handler;
            Pack = pack;
            m_Close = close;
            m_SendIdx = 0;
            m_RecvIdx = 0;
            m_CCCFlag = cccflag;
            m_AesDecryptor = new AesDecryptor(kiv, kiv, AesKeyIV.USE_KEY_V1);
            m_AesEncryptor = new AesEncryptor(kiv, kiv, AesKeyIV.USE_KEY_V1);
            EndPoint = (IPEndPoint)(sock.RemoteEndPoint);
            Socket = sock;
        }

        public void Initialize()
        {
            if (!m_Initialized)
            {
                m_Initialized = true;
                Running = true;
                Handler.HandleInitialize(this);
                Handler.HandleConnected(true);
                IOCPConn.Start(this);
            }
        }
        #region Fields
        Action<IConnection> m_Close;
        public IHandlerMessage Handler { get; private set; }
        public IPack Pack { get; private set; }
        bool m_Initialized;
        AesDecryptor m_AesDecryptor;
        AesEncryptor m_AesEncryptor;
        #endregion

        public bool Running { get; private set; }

        #region Method

        public void Send(byte[] buffer, int offset, int len)
        {
            BeginSend(buffer, offset, len);
        }

        object m_SendLock = new object();
        void BeginSend(byte[] buffer, int offset, int len)
        {
            if (!Running)
                return;

            try
            {
                //这个锁是用来保证CurSendIdx顺序的，不加锁多线程情况下会出现CurSendIdx 乱序到达目的地
                lock (m_SendLock)
                {
                    var ms = MemoryStreamPool.Get();
                    {
                        Pack.Encode(buffer, ms, offset, len, m_AesEncryptor, m_SendIdx, m_CCCFlag);
                        m_SendIdx = (m_SendIdx + 1) % 0x1F;
                        var encodedData = ms.ToArray(); // 这里不得不通过ToArray()创建一份数据的副本，因为BeginSend()是一个异步过程，期间始终需要持有数据的引用。
                        Socket.BeginSend(encodedData, 0, encodedData.Length, SocketFlags.None, null, null);// new AsyncCallback(SendCallback), m_Socket);
                    }
                    MemoryStreamPool.Release(ms);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                Debug.LogError("消息发送Exception了");
                Close();
            }
        }

        void PushPack(byte[] buffer, int offset, int size)
        {
            if (!Running)
            {
                return;
            }

            var ms = MemoryStreamPool.Get();
            {
                Pack.Decode(buffer, ms, offset, size, m_AesDecryptor, m_RecvIdx); //解压缩，解加密
                m_RecvIdx = (m_RecvIdx + 1) % 0x1F;
                Handler?.Handle(ms.GetBuffer(), 0, (int)ms.Length);
            }
            MemoryStreamPool.Release(ms);
        }

        static void SplitPack(SocketAsyncEventArgs e, Action<byte[], int, int> push)
        {
            //当前buf位置指针
            var offset = 0;
            var buffer = e.Buffer;
            var bufferSize = buffer.Length;
            var receivedSize = bufferSize - e.Count + e.BytesTransferred;
            //在mBuffer中可能有多个逻辑数据包，逐个解出
            while (receivedSize - offset > NetHelper.PackHeadSize)
            {
                //解包大小
                var packSize = NetHelper.ToInt32(buffer, offset);
                if (receivedSize - offset - NetHelper.PackHeadSize >= packSize) //已经接收了一个完整的包
                {
                    //当前buf指针加下包头偏移
                    offset += NetHelper.PackHeadSize;

                    //包体大小
                    //var pack = new byte[packSize];

                    //解MsgBody
                    //Buffer.BlockCopy(recvBuffer, offset, pack, 0, packSize);

                    //存起来
                    //push(pack);
                    push(buffer, offset, packSize);

                    //当前buf指针加下Body偏移
                    offset += packSize;
                }
                else if (bufferSize < packSize + NetHelper.PackHeadSize) //收到的包比buff大,需要做Buff的扩容
                {
                    //要扩容到的Buff大小
                    var newBuffSize = packSize + NetHelper.PackHeadSize;

                    //下面这段Baidu的 快速求 > newBuffSize 的 最小的2的幂次方数(原理近似快速的把最高为的1复制到右边所有的位置上然后+1)
                    newBuffSize |= (newBuffSize >> 1);
                    newBuffSize |= (newBuffSize >> 2);
                    newBuffSize |= (newBuffSize >> 4);
                    newBuffSize |= (newBuffSize >> 8);
                    newBuffSize |= (newBuffSize >> 16);
                    newBuffSize++;
                    if (newBuffSize < 0)
                    {
                        newBuffSize >>= 1;
                    }

                    var newBuff = new byte[newBuffSize];

                    //拷贝剩余的有效内容到新的buff
                    //Buffer中真正剩余的有效内容
                    receivedSize -= offset;
                    Buffer.BlockCopy(buffer, offset, newBuff, 0, receivedSize);
                    bufferSize = newBuffSize;
                    buffer = newBuff;
                    offset = 0;
                    break;
                }
                else //收到的包不完整 直接Break
                {
                    break;
                }
            }
            receivedSize -= offset;
            if (receivedSize > 0)
            {
                //buf内容前移
                Buffer.BlockCopy(buffer, offset, buffer, 0, receivedSize);
            }
            e.SetBuffer(buffer, receivedSize, bufferSize - receivedSize);
        }

        public void IOCPInitialize(SocketAsyncEventArgs e)
        {
           
        }
        public bool IOCPReceived(SocketAsyncEventArgs e)
        {
            if (e.UserToken != this) return false;
            SplitPack(e, PushPack);
            return true;
        }

        public void IOCPClose()
        {
            Close();
        }
        #endregion

        #region Encode&Decode
        int m_SendIdx;
        int m_RecvIdx;
        byte m_CCCFlag;

        public IPEndPoint EndPoint { get; private set; }

        public Socket Socket { get; private set; }

        #endregion
    }
}

#endif