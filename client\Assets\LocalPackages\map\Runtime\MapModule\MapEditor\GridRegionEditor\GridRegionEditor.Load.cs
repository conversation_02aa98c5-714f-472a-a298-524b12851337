﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GridRegionEditor
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            MemoryStream stream = new MemoryStream(bytes);
            BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var setting = LoadGridRegionEditorData(reader);

            reader.Close();

            var editor = CreateGridRegionEditor(setting);
            var editorMap = (Map.currentMap as EditorMap);
            editorMap.SetGridRegionEditor(editor);
        }

        static GridRegionEditor CreateGridRegionEditor(config.GridRegionEditorSetting setting)
        {
            List<GridTemplate> templates = new List<GridTemplate>();
            for (int i = 0; i < setting.gridTemplates.Length; ++i)
            {
                var t = new GridTemplate(setting.gridTemplates[i].type, setting.gridTemplates[i].name, setting.gridTemplates[i].color);
                templates.Add(t);
            }

            GridRegionEditor editor = new GridRegionEditor(setting.horizontalGridCount, setting.verticalGridCount, setting.gridWidth, setting.gridHeight, templates, setting.grids, setting.showGrid);
            return editor;
        }

        static config.GridRegionEditorSetting LoadGridRegionEditorData(BinaryReader reader)
        {
            config.GridRegionEditorSetting setting = new config.GridRegionEditorSetting();
            setting.gridWidth = reader.ReadSingle();
            setting.gridHeight = reader.ReadSingle();
            setting.horizontalGridCount = reader.ReadInt32();
            setting.verticalGridCount = reader.ReadInt32();
            setting.showGrid = reader.ReadBoolean();

            setting.grids = new int[setting.verticalGridCount, setting.horizontalGridCount];
            for (int i = 0; i < setting.verticalGridCount; ++i)
            {
                for (int j = 0; j < setting.horizontalGridCount; ++j)
                {
                    setting.grids[i, j] = reader.ReadInt32();
                }
            }
            int nTemplates = reader.ReadInt32();
            setting.gridTemplates = new config.GridTemplate[nTemplates];
            for (int i = 0; i < nTemplates; ++i)
            {
                var template = new config.GridTemplate();
                template.name = Utils.ReadString(reader);
                template.color = Utils.ReadColor(reader);
                template.type = reader.ReadInt32();
                setting.gridTemplates[i] = template;
            }

            return setting;
        }

    }
}

#endif