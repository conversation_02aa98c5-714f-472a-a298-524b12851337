﻿using UnityEngine;

namespace TFW.Map
{
    public enum SpecialAreaShape { 
        Rectangle,
        Circle,
    }

    //挂到tag为special area的game object上,注意按game object中心点对齐
    public class SpecialAreaConfig : MonoBehaviour
    {
        //id需要保证唯一
        public int id;
        public SpecialAreaShape shape = SpecialAreaShape.Rectangle;
        public float width;
        public float height;
    }
}
