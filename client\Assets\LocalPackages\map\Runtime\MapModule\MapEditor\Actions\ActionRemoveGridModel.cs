﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map {
    [Black]
    public class ActionRemoveGridModel : EditorAction {
        public ActionRemoveGridModel(int objectID, int layerID) {
            var obj = Map.currentMap.FindObject(objectID) as ModelData;
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = obj.GetModelTemplateID();
            mRotation = obj.GetRotation();
            mScale = obj.GetScale();
            mPosition = obj.GetPosition();
            var modelTemplate = Map.currentMap.FindObject(mModelTemplateID) as ModelTemplate;
            var prefabName = Path.GetFileName(modelTemplate.GetLODPrefabPath(0));
            mDescription = string.Format("remove {0}", prefabName);
            if (obj.HasObjectFlag(ObjectFlag.kUseRenderTextureModel))
            {
                mUseTextureModel = true;
            }
            else
            {
                mUseTextureModel = false;
            }
        }

        public override bool Do() {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as EditorGridModelLayer;
            if (layer != null) {
                layer.RemoveObject(mObjectID);
                return true;
            }

            return false;
        }

        public override bool Undo() {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null) {
                var layer = map.GetMapLayerByID(mLayerID) as EditorGridModelLayer;
                if (layer != null) {
                    layer.AddObject(modelTemplate.GetLODPrefabPath(0), mPosition, mRotation, mScale, mObjectID, mUseTextureModel);
                    return true;
                }
            }
            return false;
        }

        public override string description { get { return mDescription; } }

        int mLayerID;
        int mObjectID;
        int mModelTemplateID;
        Quaternion mRotation;
        Vector3 mScale;
        Vector3 mPosition;
        string mDescription;
        bool mUseTextureModel;
    }
}

#endif