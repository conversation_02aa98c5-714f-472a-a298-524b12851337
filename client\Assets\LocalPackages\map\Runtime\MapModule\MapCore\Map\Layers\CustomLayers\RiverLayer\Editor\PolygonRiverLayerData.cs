﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

//created by wzw at 2019/11/25

namespace TFW.Map
{
    public class PolygonRiverLayerData : PolygonCollisionLayerData
    {
        public PolygonRiverLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, bool useUV2, bool generateOBJ, int expandingTileCount, string riverMaskTextureSaveFolderPath) : base(header, config, map)
        {
            mUseUV2 = useUV2;
            mGenerateOBJ = generateOBJ;
            mExpandingTileCount = expandingTileCount;
            mRiverMaskTextureSaveFolderPath = riverMaskTextureSaveFolderPath;
        }

        public void SetRiverSectionTextureData(int riverID, int sectionIndex, Texture2D maskTexture)
        {
            var river = GetObjectData(riverID) as PolygonRiverData;
            if (river != null)
            {
                var section = river.GetSection(sectionIndex);
                if (section != null)
                {
                    section.SetTextureData(maskTexture);
                }
            }
        }

        public bool useUV2 { get { return mUseUV2; } set { mUseUV2 = value; } }
        public bool generateOBJ { get { return mGenerateOBJ; } set { mGenerateOBJ = value; } }
        public int expandingTileCount { get { return mExpandingTileCount; }set { mExpandingTileCount = value; } }
        public string riverMaskTextureSaveFolderPath { get { return mRiverMaskTextureSaveFolderPath; } set { mRiverMaskTextureSaveFolderPath = value; } }

        bool mUseUV2;
        bool mGenerateOBJ;
        int mExpandingTileCount;
        //保存river mask的贴图
        string mRiverMaskTextureSaveFolderPath;
    }
}

#endif