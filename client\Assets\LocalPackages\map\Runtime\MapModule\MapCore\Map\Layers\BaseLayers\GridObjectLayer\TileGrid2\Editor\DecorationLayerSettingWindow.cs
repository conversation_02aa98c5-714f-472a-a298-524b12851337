﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class DecorationLayerSettingWindow : EditorWindow
    {
        public void Show(string layerName)
        {
            mLayerName = layerName;
        }

        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            //mUseMapLargeTile = EditorGUILayout.ToggleLeft("Use One Big Tile", mUseMapLargeTile);

            //GUILayout.BeginHorizontal();
            //mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            //mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            //GUILayout.EndHorizontal();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap as EditorMap;
                float tileWidth = GetTileWidth();
                float tileHeight = GetTileHeight();
                bool valid = CheckParameter();
                if (valid)
                {
                    var setting = new DecorationObjectPlacementSetting();
                    setting.useMapLargeTile = mUseMapLargeTile;
                    if (mUseMapLargeTile)
                    {
                        setting.alignGridSize = Mathf.Min(mLayerWidth, mLayerHeight);
                        setting.alignByGrid = true;
                    }
                    var layer = map.CreateEditorComplexGridModelLayer(mLayerName, mLayerWidth, mLayerHeight, tileWidth, tileHeight, mGridType, setting);
                    layer.asyncLoading = false;

                    Selection.activeObject = layer.layerView.root;

                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        float GetTileWidth()
        {
            if (mGridType == GridType.Rectangle)
            {
                return mTileWidth;
            }
            return mRadius * Mathf.Cos(30 * Mathf.Deg2Rad) * 2.0f;
        }

        float GetTileHeight()
        {
            if (mGridType == GridType.Rectangle)
            {
                return mTileHeight;
            }
            return mRadius * 2.0f;
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public float mTileWidth = MapModule.defaultFrontTileSize;
        public float mTileHeight = MapModule.defaultFrontTileSize;
        public float mRadius = 1.0f;
        public string mLayerName;
        public GridType mGridType = GridType.Rectangle;
        //bool mUseMapLargeTile = MapModule.useMapLargeTileForDecorationLayer;
        bool mUseMapLargeTile = true;
    }
}

#endif
