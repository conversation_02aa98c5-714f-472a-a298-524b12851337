﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class BakeVertexAnimationTest
    {
#if false
        [MenuItem("Framework/BakeTest", false, 364)]
        static void BakeTest()
        {
            VertexAnimationBaker baker = new VertexAnimationBaker();
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/P2/Res/Proxy/Prefab/15111315.prefab");
            var errorMsg = baker.Check(prefab);
            if (!string.IsNullOrEmpty(errorMsg))
            {
                Debug.LogError(errorMsg);
                return;
            }

            var shader = Shader.Find("SLGMaker/BakedVertexAnimation");
            Debug.Assert(shader != null, "invalid shader");
            VertexAnimationBaker.BakeVertexOption option = new VertexAnimationBaker.BakeVertexOption();
            option.prefab = prefab;
            option.sampleFrameInterval = 4;
            option.outputFolder = "Assets/P2/Res/Proxy/Prefab/baked";
            option.blendType = AnimationBlendType.Fast;
            option.defaultShader = shader;
            option.deleteBipBones = true;
            option.clearOutputFolder = true;
            option.generatePrefab = true;
            option.useAnimationBlending = false;
            option.useSRPBatcher = false;
            baker.Bake(option);
        }
#endif
    }
}

#endif