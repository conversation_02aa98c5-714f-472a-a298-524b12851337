//using UnityEditor;
//using UnityEditor.Callbacks;
//using UnityEditor.iOS.Xcode;

//using System.IO;
//using UnityEngine;
//using System.Linq;
 
//[System.Obsolete]
//public class XcodeProjectModify
//{
//    [PostProcessBuildAttribute(1)]
//    public static void OnPostprocessBuild(BuildTarget target, string pathToBuiltProject)
//    {
//        Debug.Log(pathToBuiltProject);

//        // 当前脚本只处理iOS工程
//        if (target != BuildTarget.iOS)
//            return;

//        // 获取project和target对象
//        var projectPath = pathToBuiltProject + "/Unity-iPhone.xcodeproj/project.pbxproj"; //PBXProject.GetPBXProjectPath(pathToBuiltProject);
//        PBXProject project = new();
//        project.ReadFromFile(projectPath);
//        // Unity-iPhone Target
//        string mainTargetGuid = project.GetUnityMainTargetGuid();
//        // UnityFramework Target
//        string unityTargetGuid = project.GetUnityFrameworkTargetGuid();


//        // 一、修改编译设置
//        project.AddBuildProperty(unityTargetGuid, "OTHER_LDFLAGS", "-ObjC");
//        project.SetBuildProperty(unityTargetGuid, "ENABLE_BITCODE", "NO");


//        // 二、添加系统framwrok
//        project.AddFrameworkToProject(unityTargetGuid, "Accounts.framework", false);
//        project.AddFrameworkToProject(unityTargetGuid, "Accelerate.framework", false);
//        project.AddFrameworkToProject(mainTargetGuid, "StoreKit.framework", false);

//        // 添加lib
//        //AddLibToProject(project, unityTargetGuid, "libsqlite3.tbd");
//        //AddLibToProject(project, unityTargetGuid, "libc++.tbd");
//        //AddLibToProject(project, unityTargetGuid, "libz.tbd");

//        // 应用修改
//        File.WriteAllText(projectPath, project.WriteToString());


//        // 三、修改Info.plist
//        var plistPath = Path.Combine(pathToBuiltProject, "Info.plist");
//        var plist = new PlistDocument();
//        plist.ReadFromFile(plistPath);
//        PlistElementDict rootDict = plist.root;

//        // 3.1 从SDK参数文件中获取各个参数
//        string pathToParamsFolderInUnity = Path.Combine(Application.dataPath, "Plugins/iOS/Params");
//        var paramsPlistPath = Path.Combine(pathToParamsFolderInUnity, "新游戏需要设置到Info.plist文件的参数列表.plist");
//        var paramsPlist = new PlistDocument();
//        paramsPlist.ReadFromFile(paramsPlistPath);
//        PlistElementDict paramsRootDict = paramsPlist.root;

//        // 遍历添加URLTypes
//        var urlTypesArray = rootDict["CFBundleURLTypes"];
//        if (urlTypesArray == null)
//        {
//            urlTypesArray = rootDict.CreateArray("CFBundleURLTypes");
//        }
//        PlistElementArray paramsUrlTypesArray = paramsRootDict["CFBundleURLTypes"].AsArray();
//        foreach (PlistElementDict item in paramsUrlTypesArray.values )
//        {
//            var urlDict = urlTypesArray.AsArray().AddDict();
//            urlDict.SetString("CFBundleTypeRole", "Editor");
//            var urlInnerArray = urlDict.CreateArray("CFBundleURLSchemes");
//            PlistElementArray paramsUrlInnerArray = item["CFBundleURLSchemes"].AsArray();
//            foreach (PlistElement innerItem in paramsUrlInnerArray.values)
//            {
//                urlInnerArray.AddString(innerItem.AsString());
//            }
//        }

//        // 3.2 遍历添加LSApplicationQueriesSchemes
//        var queriesSchemesArray = rootDict["LSApplicationQueriesSchemes"];
//        if (queriesSchemesArray == null)
//        {
//            queriesSchemesArray = rootDict.CreateArray("LSApplicationQueriesSchemes");
//        }
//        PlistElementArray paramsQueriesSchemesArray = paramsRootDict["LSApplicationQueriesSchemes"].AsArray();
//        foreach (PlistElementString item in paramsQueriesSchemesArray.values)
//        {
//            queriesSchemesArray.AsArray().AddString(item.value);
//        }

//        // 3.3 添加隐私权限描述
//        string paramsUserTrackingDesc = paramsRootDict["NSUserTrackingUsageDescription"].AsString();
//        rootDict.SetString("NSUserTrackingUsageDescription", paramsUserTrackingDesc);
//        string paramsPhotoLibraryUsagDesc = paramsRootDict["NSPhotoLibraryUsageDescription"].AsString();
//        rootDict.SetString("NSPhotoLibraryUsageDescription", paramsPhotoLibraryUsagDesc);
//        string paramsCalendarsUsageDesc = paramsRootDict["NSCalendarsUsageDescription"].AsString();
//        rootDict.SetString("NSCalendarsUsageDescription", paramsCalendarsUsageDesc);

//        // 3.4 添加其它必要参数
//        var paramsAdReportEndPoint = paramsRootDict["NSAdvertisingAttributionReportEndpoint"];
//        rootDict.SetString("NSAdvertisingAttributionReportEndpoint", paramsAdReportEndPoint.AsString());

//        // 3.5 保存Info.plist变更
//        plist.WriteToFile(plistPath);


//        // 四、手动拷贝SDK使用的两个plist文件并添加引用
//        CopyAndAddPlistFilesToBuild(project, mainTargetGuid, pathToBuiltProject);


//        // 五、修改bundleID
//        var defaultBundleId = project.GetBuildPropertyForAnyConfig(mainTargetGuid, "PRODUCT_BUNDLE_IDENTIFIER");
//        // 从谷歌plist文件中获取bundleID
//        string pathToGoogleInfo = Path.Combine(Application.dataPath, "Plugins/iOS/GoogleService-Info.plist");
//        var googlekPlist = new PlistDocument();
//        googlekPlist.ReadFromString(File.ReadAllText(pathToGoogleInfo));
//        string expected_bundle_id = googlekPlist.root["BUNDLE_ID"].AsString();
//        if (expected_bundle_id != null && !expected_bundle_id.Equals(defaultBundleId))
//        {
//            project.SetBuildProperty(mainTargetGuid, "PRODUCT_BUNDLE_IDENTIFIER", expected_bundle_id);
//        }


//        // 六、Capability及证书
//        // 6.1 移除冗余config
//        project.RemoveBuildConfig("ReleaseForProfiling");
//        project.RemoveBuildConfig("ReleaseForRunning");

//        // 6.2 修改开发证书设置
//        //string debugConfigGuid = project.BuildConfigByName(mainTargetGuid, "Debug");
//        //project.SetBuildPropertyForConfig(debugConfigGuid, "CODE_SIGN_IDENTITY", "iPhone Developer: 威 李 (8BNRU8WZWR)");
//        //string releaseConfigGuid = project.BuildConfigByName(mainTargetGuid, "Release");
//        //project.SetBuildPropertyForConfig(releaseConfigGuid, "CODE_SIGN_IDENTITY", "iPhone Distribution: G-CONG NETWORK TECHNOLOGY CO.LIMITED (FXM4TAWNPC)");

//        // 6.3 修改证书描述文件
//        //project.SetTeamId(mainTargetGuid, "证书的TeamId");// 不在开发团队内可不设置
//        //project.SetBuildProperty(mainTargetGuid, "CODE_SIGN_STYLE", "Manual");
//        //project.SetBuildProperty(mainTargetGuid, "PROVISIONING_PROFILE", "33e4348f0-609e-4764-b185-1a7bd9ece950");//mobileprovison文件的UUID
//        //project.SetBuildProperty(mainTargetGuid, "PROVISIONING_PROFILE_SPECIFIER", "dqhw_adhoc_20240208");//mobileprovison文件的Name

//        // 6.4 添加必要的Capability
//        // 先拷贝entitlements文件
//        var xcodeFilePath = "Entitlements.entitlements";
//        string entitlementsFilePath = Path.Combine(Application.dataPath, "Plugins/iOS/" + xcodeFilePath);
//        string destinationFilePath = Path.Combine(pathToBuiltProject, xcodeFilePath);
//        File.Copy(entitlementsFilePath, destinationFilePath, true);
//        project.AddFileToBuild(mainTargetGuid, project.AddFile(xcodeFilePath, xcodeFilePath, PBXSourceTree.Source));

//        // 添加特定Capability
//        project.AddCapability(mainTargetGuid, PBXCapabilityType.PushNotifications, "Entitlements.entitlements");
//        //project.AddCapability(mainTargetGuid, PBXCapabilityType.InAppPurchase, "Entitlements.entitlements");
//        project.AddCapability(mainTargetGuid, PBXCapabilityType.GameCenter, "Entitlements.entitlements");
//        project.AddCapability(mainTargetGuid, PBXCapabilityType.SignInWithApple, "Entitlements.entitlements");
//        project.AddCapability(mainTargetGuid, PBXCapabilityType.BackgroundModes);
//        // Background Modes需要配置子项
//        var bgModes = rootDict["UIBackgroundModes"];
//        if (bgModes == null)
//        {
//            bgModes = rootDict.CreateArray("UIBackgroundModes");
//        }
//        bgModes.AsArray().AddString("remote-notification");
//        File.WriteAllText(plistPath, plist.WriteToString());

//        // 保存project变更
//        project.WriteToFile(projectPath);


//        // 七、在UnityAppController.mm文件中插入初始化代码
//        string unityAppControllerPath = pathToBuiltProject + "/Classes/UnityAppController.mm";
//        XClass UnityAppController = new(unityAppControllerPath);
//        // 插入头文件引用
//        UnityAppController.WriteBelow("#include \"PluginBase/AppDelegateListener.h\"", "#import <MaxSDK/MaxSDK.h>\n#import <MaxSDK/MaxRVAdapter.h>");

//        // 先从RVSDK-Info.plist中获取秘钥
//        string pathToRVSDKInfo = Path.Combine(Application.dataPath, "Plugins/iOS/RVSDK-Info.plist");
//        var rvsdkPlist = new PlistDocument();
//        rvsdkPlist.ReadFromString(File.ReadAllText(pathToRVSDKInfo));
//        string secrecyKey = rvsdkPlist.root["37SecrecyKey"].AsString();
//        if (secrecyKey == null)
//        {
//            secrecyKey = "DEFAULT_SECRECY_KEY";
//        }
//        // 再插入初始化代码
//        string initCode = "\n" +
//                   "    [MaxSDK sharedInstance].delegate = [MaxRVAdapter sharedInstance];\n" +
//                   "    [[MaxSDK sharedInstance] initSDKWithConfig:@{@\"secrectKey\":@\"" + secrecyKey + "\"} application:application launchingWithOptions:launchOptions];\n";
//        string newUnityFlag = "[self initUnityWithApplication: application];";
//        if (UnityAppController.HaveFlag(newUnityFlag)) 
//        {
//            UnityAppController.WriteBelow(newUnityFlag, initCode);
//        } else
//        {
//            UnityAppController.WriteBelow("[self preStartUnity];", initCode);
//        }
           
  
//    }


//    /// <summary>
//    /// 从Unity/Assets的Plugins/iOS/文件夹下复制两个Plist文件添加到Xcode主工程
//    /// </summary>
//    /// <param name="project"></param>
//    /// <param name="mainTargetGuid"></param>
//    /// <param name="pathToBuiltProject"></param>
//    private static void CopyAndAddPlistFilesToBuild(PBXProject project, string mainTargetGuid, string pathToBuiltProject)
//    {
//        string sourceFolder = Path.Combine(Application.dataPath, "Plugins");
//        string destinationFolder = Path.Combine(pathToBuiltProject, "Frameworks/Plugins");
//        foreach (var filePath in Directory.GetFiles(sourceFolder, "*", SearchOption.AllDirectories))
//        {
//            if (Path.GetFileName(filePath).Equals("RVSDK-Info.plist") || Path.GetFileName(filePath).Equals("GoogleService-Info.plist"))
//            {
//                var dstFilePath = filePath.Replace(sourceFolder, destinationFolder);
//                File.Copy(filePath, dstFilePath, true);

//                var xcodeFilePath = filePath.Replace(sourceFolder, "Frameworks/Plugins");
//                project.AddFileToBuild(mainTargetGuid, project.AddFile(xcodeFilePath, xcodeFilePath, PBXSourceTree.Source));
//            }
//        }
//    }


//    /// <summary>
//    /// 添加系统lib
//    /// </summary>
//    /// <param name="inst"></param>
//    /// <param name="targetGuid"></param>
//    /// <param name="lib"></param>
//    static void AddLibToProject(PBXProject inst, string targetGuid, string lib)
//    {
//        string fileGuid = inst.AddFile("usr/lib/" + lib, "Frameworks/" + lib, PBXSourceTree.Sdk);
//        inst.AddFileToBuild(targetGuid, fileGuid);
//    }
//}






//public partial class XClass : System.IDisposable
//{

//    private string filePath;

//    public XClass(string fPath)
//    {
//        filePath = fPath;
//        if (!File.Exists(filePath))
//        {
//            Debug.LogError(filePath + "路径下文件不存在");
//            return;
//        }
//    }

//    public bool HaveFlag(string flag)
//    {
//        StreamReader streamReader = new StreamReader(filePath);
//        string text_all = streamReader.ReadToEnd();
//        streamReader.Close();

//        int beginIndex = text_all.IndexOf(flag);
//        if (beginIndex == -1)
//        {
//            //Debug.LogWarning(filePath + "中没有找到代码标志：" + flag);
//            return false;
//        } else
//        {
//            return true;
//        }
//    }

//    public void WriteBelow(string below, string text)
//    {
//        StreamReader streamReader = new StreamReader(filePath);
//        string text_all = streamReader.ReadToEnd();
//        streamReader.Close();

//        int beginIndex = text_all.IndexOf(below);
//        if (beginIndex == -1)
//        {
//            Debug.LogError(filePath + "中没有找到代码标志：" + below);
//            return;
//        }

//        int endIndex = text_all.LastIndexOf("\n", beginIndex + below.Length);

//        text_all = text_all.Substring(0, endIndex) + "\n" + text + "\n" + text_all.Substring(endIndex);

//        StreamWriter streamWriter = new StreamWriter(filePath);
//        streamWriter.Write(text_all);
//        streamWriter.Close();
//    }

//    public void Replace(string below, string newText)
//    {
//        StreamReader streamReader = new StreamReader(filePath);
//        string text_all = streamReader.ReadToEnd();
//        streamReader.Close();

//        int beginIndex = text_all.IndexOf(below);
//        if (beginIndex == -1)
//        {
//            Debug.LogError(filePath + "中没有找到代码标志：" + below);
//            return;
//        }

//        text_all = text_all.Replace(below, newText);
//        StreamWriter streamWriter = new StreamWriter(filePath);
//        streamWriter.Write(text_all);
//        streamWriter.Close();

//    }

//    public void Dispose()
//    {

//    }
//}

