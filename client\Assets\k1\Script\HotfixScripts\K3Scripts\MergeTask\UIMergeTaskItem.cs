﻿using Cfg.Cm;
using Common;
using DeepUI;
using DG.Tweening;
using K3;
using TFW.UI;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using Cfg;
using Game.Data;
using TFW;
using UnityEngine;
using Render;
using Game.Config;
using TMPro;

namespace UI
{
    public class UIMergeTaskItem : MonoBehaviour
    {

        public TFWImage bg;

        public UIGrid costGrid, rewardGrid;

        public GameObject canDone;
        public GameObject mCanDoneImg;

        public GameObject btnCatch;
        //public Canvas btnCatchCanvas;

        public GameObject RootGo;
        public TFWText TestText;
        public TextMeshProUGUI btnCatchText;
        public TFWImage roleIcon;

        public TFWImage iconType;

        public GameObject photoObj;
        public Animator mAnimator;

        public GameObject soldierRoot;
        public TFWText soldierCount;

        public cspb.MergeTask mMergeTask { private set; get; }

        private List<CValTypId_isi> needItems = new List<CValTypId_isi>();
        private List<CValTypId_isi> rewards = new List<CValTypId_isi>();

        public bool mIsInRwdAni = false;

        public bool canDoned = false;

        private bool isCompleted = false;

        private string lastTextStr = string.Empty;

        public List<CValTypId_isi> NeedItems
        {
            get
            {
                return needItems;
            }
        }

        public void InitData(cspb.MergeTask mergeTask)
        {
            mIsInRwdAni = false;

            if (mergeTask != null)
            {
                mMergeTask = mergeTask;

                TestText.text = mMergeTask.id.ToString();

                UIBase.AddRemoveListener(TFW.EventTriggerType.Click, this.gameObject, (x, y) =>
                {
                    foreach (var rwd in rewards)
                    {
                        if (rwd.Typ != AssetType.Item)
                        {
                            continue;
                        }

                        var itemCfg = Cfg.C.CItem.I(rwd.Id);
                        if (null == itemCfg || itemCfg.Class != "item_hero")
                        {
                            continue;
                        }

                        var heroId = itemCfg.CategoryParam.Effect[0].Id;

                        if (heroId == UIRecruitRwdNewItem.HeroExpCardId)
                        {
                            continue;
                        }

                        var heroData = HeroGameData.I.GetHeroByCfgId(heroId);
                        
                        var heroList = HeroGameData.I.GetAllHeroData();

                        var hData = heroList.Find(s =>s.HeroCfg.Id == heroId);
                        if (hData == null)
                        {
                            DeepUI.PopupManager.I.ShowDialog<UIHeroPreview>(new UIHeroPreviewPopupData()
                            {
                                ShowHeroDatas = new List<Game.Data.HeroData>() { new Game.Data.HeroData(heroId, heroData.ModelId, heroData.HeroCfg.StarStageCost.Count, 100, 0, 0) },
                                first = true
                            });
                        }
                        else
                        {
                            var popData = new UIHeroShowViewData()
                            {
                                mainHeroData = heroData,
                            };

                            PopupManager.I.ShowPanel<MyHeroShowView>(popData);
                        }


                        /*if (null == heroData)
                        {
                            heroData = new HeroData(0, heroId, 0, 0, 0, 0);

                            DeepUI.PopupManager.I.ShowDialog<UIHeroPreview>(new UIHeroPreviewPopupData()
                            {
                                ShowHeroDatas = new List<Game.Data.HeroData>() { heroData },
                                first = true
                            });
                        }
                        else
                        {
                            var popData = new UIHeroShowViewData()
                            {
                                mainHeroData = heroData,
                            };
                            if (popData.showGetBtn)
                            {
                                DeepUI.PopupManager.I.ShowDialog<UIHeroPreview>(new UIHeroPreviewPopupData()
                                {
                                    ShowHeroDatas = new List<Game.Data.HeroData>() { heroData },
                                    first = true
                                });
                            }
                            else
                            {
                                DeepUI.PopupManager.I.ShowPanel<UIHeroShowView>(popData);
                            }
                        }*/

                        return;
                    }
                });

                UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btnCatch, (x, y) =>
                {
                    if (mMergeTask != null)
                    {
                        if (CSPlayer.I.SyncMerge())
                        {
                            GameAudio.PlayAudio(AudioConst.taskComplete);
                            //if (mMergeTask.CityTask() && Cfg.C.CMergeTask.I(mMergeTask.cfgId).PhotoLimit == 1)
                            //{
                            //    PopupManager.I.ClosePopup<UIMerge>();

                            //    var ui = WndMgr.Get<UIMainCity>();
                            //    if (ui != null)
                            //    {
                            //        int cityCfgID = LPlayer.I.GetCityBuildingIDByType((int)MainCityItem.CityType.Main);
                            //        ui.dragImage.MoveTOCfg(cityCfgID, () =>
                            //        {
                            //            EventMgr.FireEvent(TEventType.K3TaskCompletePhotoWall);
                            //        });
                            //    }

                            //}
                            //else
                            {
                                //List<cspb.TypIDVal> rewardsToFly = new List<cspb.TypIDVal>();
                                foreach (var item in rewards)
                                {
                                    if (item.Id == 21110436)
                                    {
                                        K3PlayerMgr.I.ShowFlyHeroStarEffect(btnCatch.transform, item.Val);
                                        break;
                                    }
                                    //rewardsToFly.Add(new cspb.TypIDVal()
                                    //{
                                    //    ID = item.Id,
                                    //    typ = item.Typ,
                                    //    val = item.Val
                                    //});
                                }

                                var taskEvent = new K3.TaskEvent();
                                taskEvent.EventKey = $"completemergetask";
                                taskEvent.Properties.Add("taskcfgid", mMergeTask.cfgId);
                                taskEvent.Properties.Add("taskid", mMergeTask.id.ToString());
                                K3.K3GameEvent.I.TaLog(taskEvent);

                                //UITools.ShowFlyEffect(rewardsToFly, btnCatch.transform);
                                MergeTaskMgr.I.CompleteTask(mMergeTask.id);
                                isCompleted = true;
                                //if (mMergeTask.CityTask())
                                //{
                                //    EventMgr.FireEvent(TEventType.K3TaskCompleteAnimation, mMergeTask);
                                //}
                            }
                        }
                    }
                });

                RefreshData(mMergeTask);

            }
            else
            {
                mMergeTask = null;
                UIBase.RemoveListener(TFW.EventTriggerType.Click, btnCatch);
            }

        }

        private void OnEnable()
        {
            //EventMgr.RegisterEvent(TEventType.K3TaskRefresh, (obj) =>
            //{
            //    RefreshCityTaskDisplay();
            //}, this);

            EventMgr.RegisterEvent(TEventType.K3GridDataRefresh, (obj) => { RefreshData(mMergeTask); }, this);

            TestText.gameObject.SetActive(false);

            //btnCatchCanvas.sortingLayerID = CustomSortingLayer.Layer;

            // #if UNITY_EDITOR
            //             TestText.gameObject.SetActive(true);
            // #endif
        }

        //private void RefreshCityTaskDisplay()
        //{
        //    if (MergeTaskMgr.I.HaveCityTask())
        //    {
        //        RootGo.SetActive(mMergeTask?.CityTask() ?? false);
        //    }
        //    else
        //    {
        //        RootGo.SetActive(true);
        //    }
        //}

        private void OnDisable()
        {
            canDoned = false;
            mIsInRwdAni = false;
            isCompleted = false;
            EventMgr.UnregisterEvent(this);
        }

        public void PlayCollectAni(Action callback)
        {
            mIsInRwdAni = true;

            mAnimator.SetTrigger("ok");

            NTimer.CountDown(1.6f, () =>
            {
                mIsInRwdAni = false;

                callback?.Invoke();
            });
        }

        public void RefreshDataWithOutUI(cspb.MergeTask mTask)
        {
            mMergeTask = mTask;
        }

        public void RefreshData(cspb.MergeTask mTask)
        {
            mMergeTask = mTask;

            if (mMergeTask != null)
            {
                
                if (isCompleted)
                {
                    canDone.SetActive(false);
                    costGrid.Clear();
                    return;
                }

                bool isNewReward = false;
                var newRewards =  mMergeTask.Rewards();
                if (rewards.Count == 0)
                {
                    isNewReward = true;
                    rewards = newRewards;
                }
                else
                    isNewReward = !rewards.SequenceEqual(newRewards);

                if (isNewReward)
                {
                    rewards = newRewards;
                    //rewards.AddRange(newRewards);
                    SetRewards();
                }
                
                bool isNewItem = false;
                var newItemList = mMergeTask.NeedItems();
                if (needItems.Count == 0)
                {
                    isNewItem = true;
                    needItems = newItemList;
                }
                else
                    isNewItem = !needItems.SequenceEqual(newItemList);

                if (isNewItem)
                {
                    needItems = newItemList;
                    /*needItems.Clear();
                    needItems.AddRange(mMergeTask.NeedItems());*/
                    SetCost(); 
                }
                else
                {
                    var itemIndex = 1;
                    var GetMList = costGrid.GetMList();
                    if (GetMList.Count <= 0)
                    {         
                        SetCost(); 
                    }
                    else
                    {
                        foreach (var item in needItems)
                        {
                            GetMList[itemIndex - 1].GetComponent<UIMergeTaskCostGridItem>().InitReward(new Reward() { RewardId = item.Id, RewardType = item.Typ, RewardVal = (int)item.Val }, itemIndex);
                            itemIndex++;
                        }
                    }

                }
                
                SetStars();
                // SetSoldiers();

                //if (iconType)
                //{
                //    if (needItems.Count > 0 && Cfg.C.CK3Item.I(needItems[0].Id).ItemSource.Count>0)
                //    {
                //        var itemType= int.Parse(Cfg.C.CK3Item.I(needItems[0].Id).ItemSource[0]); 
                //        UITools.SetImageBySpriteName(iconType, UITools.GetAttributeDisplayKey(itemType - 10));
                //        iconType.gameObject.SetActive(true);
                //    } 
                //    else
                //    {
                //        iconType.gameObject.SetActive(false);
                //    }
                //}


                if (!mMergeTask.random)
                {
                    //配置任务
                    var cfgData = Cfg.C.CMergeTask.I(mMergeTask.cfgId);

                    gameObject.name = $"{mMergeTask.cfgId}";
                    SetBg(mTask.Quality(), cfgData.TaskType);

                    /*btnCatchText.text = cfgData.TaskType == 4 ? "Wanted_btn_02".ToLocal() : "Wanted_btn_01".ToLocal();*/
                    var text = cfgData.TaskType == 4 ? "Wanted_btn_02".ToLocal() : "Wanted_btn_01".ToLocal();
                    
                    if (lastTextStr != text)
                    {
                        lastTextStr = text;
                        btnCatchText.text = lastTextStr;
                    }

                    photoObj.SetActive(cfgData.PhotoLimit == 1);
                    UITools.SetImageBySpriteName(roleIcon, cfgData.NpcIcon);
                }
                else
                {
                    photoObj.SetActive(false);

                    /*btnCatchText.text = "Wanted_btn_01".ToLocal();*/
                    gameObject.name = $"{mMergeTask.cfgId}";

                    var text = "Wanted_btn_01".ToLocal();
                    if (lastTextStr != text)
                    {
                        lastTextStr = text;
                        btnCatchText.text = lastTextStr;
                    }
                    var cfgData = Cfg.C.CNewMergeTaskAuto.I(mMergeTask.cfgId);

                    SetBg(mTask.Quality(), 0);
                    UITools.SetImageBySpriteName(roleIcon, cfgData.NpcIcon);
                }

                // 判断资源是否充足
                var haveAssets = Logic.PlayerAssetsMgr.I.HaveAssets(needItems);
                bool plentiful = string.IsNullOrEmpty(haveAssets);
                canDone.SetActive(plentiful);
                // 资源充足根据任务ID是否触发引导
                if (plentiful)
                {
                    costGrid.Clear(); //消耗不显示
                    GuidManage.TriggerGuid(GuidManage.GuidTriggerType.CompleteTheWantedNotice, mMergeTask.cfgId);
                }

                if (canDoned != plentiful)
                {
                    canDoned = plentiful;
                }
            }
            else
            {
                canDone.SetActive(false);
            }

            //RefreshCityTaskDisplay();
        }

        private void SetBg(int Quality, int taskType)
        {
            //switch (Quality)
            //{
            //    case 2:
            //        UITools.SetImageBySpriteName(bg, taskType == 4 ? "UI_Main_Missing_03" : "UI_Main_Wanted_03");
            //        break;
            //    case 3:
            //        UITools.SetImageBySpriteName(bg, taskType == 4 ? "UI_Main_Missing_04" : "UI_Main_Wanted_04");
            //        break;
            //    case 4:
            //        UITools.SetImageBySpriteName(bg, taskType == 4 ? "UI_Main_Missing_05" : "UI_Main_Wanted_05");
            //        break;
            //    case 5:
            //        UITools.SetImageBySpriteName(bg, taskType == 4 ? "UI_Main_Missing_02" : "UI_Main_Wanted_02");
            //        break;
            //    case 1:
            //    default:
            //        UITools.SetImageBySpriteName(bg, taskType == 4 ? "UI_Main_Missing_01" : "UI_Main_Wanted_01");
            //        break;
            //}
        }

        private void SetCost()
        {
            if (isCompleted)
            {
                return;
            }
            costGrid.Clear();
             
            int itemIndex = 1;
            foreach (var item in needItems)
            {
                costGrid.AddItem<UIMergeTaskCostGridItem>().InitReward(new Reward() { RewardId = item.Id, RewardType = item.Typ, RewardVal = (int)item.Val }, itemIndex);
                itemIndex++;
            }
        }

        private void SetRewards()
        {
            rewardGrid.Clear();
            rewardGrid.gameObject.SetActive(true);

            foreach (var item in rewards)
            {
                //if (item.Id == CfgConst.StarItemId)
                //{
                //    continue;
                //}

                rewardGrid.AddItem<CommonItem>().InitData(new CommonItem.CommonItemData() { Id = item.Id, Typ = item.Typ, Val = (int)item.Val });
            }

            if (rewardGrid.MList.Count <= 0)
            {
                rewardGrid.gameObject.SetActive(false);
            }
        }

        private void SetStars()
        {
            soldierRoot.SetActive(false);

            //var star = rewards.Find(r => r.Id == CfgConst.StarItemId);

            //if (null != star)
            //{
            //    soldierCount.text = "x" + UIStringUtils.FormatIntUnitByLanguage(star.Val, 2);
            //    soldierRoot.SetActive(true);
            //}
        }

        // private void SetSoldiers()
        // {
        //     soldierRoot.SetActive(false);
        //
        //     var soldier = rewards.Find(r => r.Id == CfgConst.Prisoner && r.Typ == AssetType.Vm);
        //
        //     if (null != soldier)
        //     {
        //         soldierCount.text = "+" + soldier.Val;
        //         soldierRoot.SetActive(true);
        //     }
        // }
    }
}
