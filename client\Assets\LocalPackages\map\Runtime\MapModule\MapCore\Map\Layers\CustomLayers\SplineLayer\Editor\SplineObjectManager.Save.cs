﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class SplineObjectManager
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.SplineObjectManagerEditorDataVersion);

            SaveEditorData(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write(c.useRenderTexture);
                writer.Write((int)c.flag);
                Utils.WriteString(writer, c.name);
            }
        }

        void SaveEditorData(BinaryWriter writer)
        {
            SaveMapLayerLODConfig(writer, mLODConfig);
            writer.Write(mDisplayRadius);
            writer.Write(mMaxExportSegmentLength);
            writer.Write(mVisible);
            Utils.WriteString(writer, mRiverPrefabFolder);
            writer.Write(mGenerateObjFile);

            int objectCount = mSplineObjects.Count;
            writer.Write(objectCount);
            for (int i = 0; i < objectCount; ++i)
            {
                SaveSpline(writer, mSplineObjects[i]);
            }
        }

        void SaveSpline(BinaryWriter writer, SplineObject spline)
        {
            writer.Write(1/*spline.width*/);
            writer.Write(spline.tileCount);
            string materialGuid = AssetDatabase.AssetPathToGUID(spline.materialPath);
            Utils.WriteString(writer, materialGuid);
            var controlPoints = spline.controlPoints;
            int nControlPoints = controlPoints.Count;
            writer.Write(nControlPoints);
            for (int k = 0; k < nControlPoints; ++k)
            {
                Utils.WriteVector3(writer, controlPoints[k].pos);
                Utils.WriteVector3(writer, controlPoints[k].tangents[0]);
                Utils.WriteVector3(writer, controlPoints[k].tangents[1]);
                writer.Write(controlPoints[k].pointCountInSegment);
                writer.Write(controlPoints[k].width);
                Utils.WriteColor(writer, controlPoints[k].color);
                writer.Write(controlPoints[k].tileIndex);
            }

            writer.Write((int)spline.attributes);
            writer.Write(spline.ratio);

            //save segments info
            var segments = spline.segments;
            int segmentCount = 0;
            if (segments != null)
            {
                segmentCount = segments.Count;
            }
            writer.Write(segmentCount);
            for (int s = 0; s < segmentCount; ++s)
            {
                var seg = segments[s];
                writer.Write(seg.splineIndex);
                writer.Write(seg.startControlPointIndex);
                writer.Write(seg.endControlPointIndex);
                Utils.WriteString(writer, seg.prefabPath);
                Utils.WriteVector3(writer, seg.position);
            }

            Utils.WriteString(writer, spline.name);
            SaveRiverData(writer, spline.riverData);
        }

        void SaveRiverData(BinaryWriter writer, SplineObject.RiverData riverData)
        {
            Utils.WriteString(writer, riverData.stencilMaskMaterialPath);
            Utils.WriteString(writer, riverData.waterMaterialPath);
            writer.Write(riverData.isRiverObject);
            writer.Write(riverData.stripeCount);
            Utils.WriteFloatArray(writer, riverData.stripeWidth.ToArray());
            Utils.WriteFloatArray(writer, riverData.stripeDepth.ToArray());
        }

        //save objects in range
        public void SaveObjectsInRange(string dataPath, float minX, float minZ, float maxX, float maxZ)
        {
            Rect layerBounds = new Rect(minX, minZ, maxX, maxZ);

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.SplineObjectManagerEditorDataVersion);

            int objectCount = mSplineObjects.Count;
            long offset = writer.BaseStream.Position;
            writer.Write(objectCount);

            int validObjectCount = 0;
            for (int i = 0; i < objectCount; ++i)
            {
                var spline = mSplineObjects[i];

                var objBounds = spline.CalculateBounds();
                if (objBounds.Overlaps(layerBounds))
                {
                    ++validObjectCount;

                    SaveSpline(writer, spline);
                }
            }

            Utils.WriteAndJump(writer, offset, validObjectCount);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }
    }
}

#endif