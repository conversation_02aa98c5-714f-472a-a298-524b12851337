using NativeWebSocket;
using System;
using System.IO;
using System.Net.Sockets;
using System.Threading;
using UnityEngine;

namespace GameServerConnection
{
    public class GameServerConnection : MonoBehaviour
    {
        public static event Action<(byte[] bytes, int offset, int len)> OnMessage;

        public static event Action<string> OnError;

        public static event Action<WebSocketCloseCode> OnClose;

        const string pubkey = "<RSAKeyValue><Modulus>nvMxm+FADINgOt6R/v1teOYR72EDyZZF+jn8yddteYbQiOj8odk3R0aw6lVOsD+tPWqPbS8UbG2162owRl4qtoo0V+wCq7FXYWItifgdqvwq7B+QgymtemAlTz8H/6GQ8/4ca3I2Y4TqEiGbqWN/8czgU043FXL1m08RsNMDpus=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";

        /// <summary>
        /// 老版本加密长度
        /// </summary>
        const int KeyIVLen_V0 = 16;

        /// <summary>
        /// 当前是否已连接
        /// </summary>
        public static bool IsConnected =>
            _instance != null
            && _instance._webSocket != null
            && _instance._webSocket.State == WebSocketState.Open
            && !_instance._firstFlag;

        static GameServerConnection _instance;

        MessagePacker _messagePacker = new MessagePacker();

        /// <summary>
        /// 最后一次接收到后端消息的时间戳
        /// </summary>
        public static long LastMsgTimestamp { get; private set; }

        public static byte[] _keyIV;

        private AesDecryptor _aesDecryptor;
        private AesEncryptor _aesEncryptor;

        bool _firstFlag = true;
        private WebSocket _webSocket;
        public CancellationToken webSocketCancelToken { get; private set; }

        Action<SocketError> _callback = null;

        #region
        public static void DoConnect(string websocketUrl, Action<SocketError> callback)
        {
            _instance._callback = callback;
            _instance.DoConnectInternal(websocketUrl);
        }

        public static void Disconnect()
        {
            _instance.DisconnectInternal();
        }

        public static void Send(byte[] msg, int offset, int len)
        {
            _instance.SendInternal(msg, offset, len);
        }

        [Obsolete]
        public static void Init()
        {
        }

        [Obsolete]
        public static bool DoConnect(string address, int port, Action<SocketError> callback)
        {
            DoConnect($"{address}:{port}", callback);
            return true;
        }

        /// <summary>
        /// 检查连接
        /// </summary>
        /// <param name="disconn">如果检查到连接已经断开，是否执行主动Disconnect</param>
        /// <returns></returns>
        [Obsolete]
        public static bool CheckConnect(bool disconn = false)
        {
            return IsConnected;
        }
        #endregion

        private void Awake()
        {
            _instance = this;
            DontDestroyOnLoad(this);
        }

        void Update()
        {
#if !UNITY_WEBGL || UNITY_EDITOR
            if (_webSocket != null)
            {
                _webSocket.DispatchMessageQueue();
            }
#endif
        }

        void SendInternal(byte[] msg, int offset, int len)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                _messagePacker.Encode(msg, ms, offset, len, _aesEncryptor);
                //m_SendIdx = (m_SendIdx + 1) % 0x1F;
                var encodedData = ms.ToArray(); // 这里不得不通过ToArray()创建一份数据的副本，因为BeginSend()是一个异步过程，期间始终需要持有数据的引用。
                _webSocket.Send(encodedData);
            }
        }

        private void DoConnectInternal(string webSocketUrl)
        {
            ResetWebSocket();

            _webSocket = new WebSocket(webSocketUrl);
            webSocketCancelToken = new CancellationToken();
            _webSocket.OnOpen += OnWsOpen;
            _webSocket.OnMessage += OnWsMessage;
            _webSocket.OnError += OnWsError;
            _webSocket.OnClose += OnWsClose;
            _webSocket.Connect();
        }

        private void DisconnectInternal()
        {
            if (_webSocket != null && this._webSocket.State == WebSocketState.Open)
            {
                _webSocket.Close();
            }
        }

        private void ResetWebSocket()
        {
            DisconnectInternal();
            _webSocket = null;

            _firstFlag = true;
        }

        private void OnWsOpen()
        {
            var kiv = EncryptionHelper.GenKeyIV();

            _keyIV = new byte[KeyIVLen_V0];
            Array.Copy(kiv, 1, _keyIV, 0, KeyIVLen_V0);

            SendAesKeyIVRsa(_webSocket, kiv);
        }

        void SendAesKeyIVRsa(WebSocket ws, byte[] kiv)
        {
            if (ws == null)
            {
                //Debug.LogError("Null ws");
                return;
            }
            if (ws.State != WebSocketState.Open)
            {
                //Debug.LogError("Socket is not connected");
                return;
            }
            if (!EncryptionHelper.RsaEncrypt(pubkey, kiv, out kiv))
            {
                ws.Close();
                throw new Exception("Rsa Encrypt error");
            }

            ws.Send(kiv);
        }

        private void OnWsMessage(byte[] buffer)
        {
            if (_firstFlag)
            {
                _firstFlag = false;
                var decrypt = new AesDecryptor(_keyIV, _keyIV, true);
                var bytes = decrypt.Decrypt(buffer);
                _aesDecryptor = new AesDecryptor(bytes, bytes, true);
                _aesEncryptor = new AesEncryptor(bytes, bytes, true);

                _callback?.Invoke(SocketError.Success);
            }
            else
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    _messagePacker.Decode(buffer, ms, 0, buffer.Length, _aesDecryptor); //解压缩，解加密

                    OnMessage?.Invoke((ms.GetBuffer(), 0, (int)ms.Length));
                    //获取当前的时间戳
                    LastMsgTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                }
            }
        }

        private void OnWsError(string errMsg)
        {
            Debug.Log($"Connection Error! code: {errMsg}");
            OnError?.Invoke(errMsg);
        }

        private void OnWsClose(WebSocketCloseCode code)
        {
            Debug.Log($"Connection closed! code: {code}");
            OnClose?.Invoke(code);
        }
    }
}