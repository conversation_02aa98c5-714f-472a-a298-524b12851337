﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MapRegionPointGenerator
    {
        public MapRegionPointGenerator(Rect mapRange)
        {
            mMapRange = mapRange;
        }

        public void Create(List<List<Vector3>> noneHolePolygons, List<List<Vector3>> holePolygons, int nPoints, float mapRadius, float moveRange, RandomWrapper r, float edgeSize, System.Func<List<Vector2>, Vector3, bool> FindDuplicatedPositionFunc, int maxTryCount, TriangleNet.TrianglePool pool)
        {
            //加个误差
            if (moveRange != 0)
            {
                mMoveRange = moveRange + 1;
            }
            else
            {
                mMoveRange = 0;
            }

            mTriangleAreas.Clear();
            mTriangleGeneratedPoints.Clear();
            mTrianglePercentage.Clear();
            mGeneratedPoints.Clear();

#if false
            for (int i = 0; i < noneHolePolygons.Count; ++i)
            {
                EditorUtils.CreateDrawPolygon($"noneHoles {i}", noneHolePolygons[i]);
            }

            for (int i = 0; i < holePolygons.Count; ++i)
            {
                EditorUtils.CreateDrawPolygon($"holes {i}", holePolygons[i]);
            }
#endif

            if (noneHolePolygons.Count > 0)
            {
                bool error = false;
                Vector3[] meshVertices = null;
                int[] indices = null;
                try
                {
                    Triangulator.TriangulatePolygons(noneHolePolygons, holePolygons, false, 0, 0, pool, out meshVertices, out indices);
                }
                catch
                {
                    error = true;
                }

                if (!error && indices != null)
                {
                    //temp code
                    //var obj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                    //var filter = obj.GetComponent<MeshFilter>();
                    //var mesh = new Mesh();
                    //mesh.vertices = meshVertices;
                    //mesh.triangles = indices;
                    //mesh.RecalculateNormals();
                    //filter.sharedMesh = mesh;

                    int nTriangles = indices.Length / 3;
                    Vector3[] triangleVertices = new Vector3[3];
                    float totalArea = 0;
                    for (int i = 0; i < nTriangles; ++i)
                    {
                        triangleVertices[0] = meshVertices[indices[i * 3]];
                        triangleVertices[1] = meshVertices[indices[i * 3 + 1]];
                        triangleVertices[2] = meshVertices[indices[i * 3 + 2]];
                        float area = EditorUtils.CalculatePolygonArea(triangleVertices);
                        mTriangleAreas.Add(area);
                        totalArea += area;
                        mTriangleGeneratedPoints.Add(new List<Vector3>());
                    }

                    float ratio = 0;
                    for (int i = 0; i < nTriangles; ++i)
                    {
                        ratio += mTriangleAreas[i] / totalArea;
                        mTrianglePercentage.Add(ratio);
                    }

                    var center = mMapRange.center;

                    //generate points
                    for (int i = 0; i < nPoints; ++i)
                    {
                        float val = r.Range(0.0f, 1.0f);
                        for (int k = 0; k < nTriangles; ++k)
                        {
                            if (val <= mTrianglePercentage[k])
                            {
                                triangleVertices[0] = meshVertices[indices[k * 3]];
                                triangleVertices[1] = meshVertices[indices[k * 3 + 1]];
                                triangleVertices[2] = meshVertices[indices[k * 3 + 2]];

                                bool validPoint;
                                var point = GeneratePointInTriangle(k, triangleVertices, r, edgeSize, FindDuplicatedPositionFunc, maxTryCount, out validPoint);
                                if (validPoint)
                                {
                                    if (mapRadius != 0)
                                    {
                                        double radiusWithError = mapRadius - 1;
                                        radiusWithError *= radiusWithError;
                                        var d = point - center;
                                        double dis = (double)d.x * (double)d.x + (double)d.y * (double)d.y;
                                        if (dis <= radiusWithError && IsInWorld(point))
                                        {
                                            mGeneratedPoints.Add(point);
                                        }
                                    }
                                    else
                                    {
                                        if (IsInWorld(point))
                                        {
                                            mGeneratedPoints.Add(point);
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }

        bool IsInWorld(Vector2 pos)
        {
            float minX = mMapRange.xMin + mMoveRange;
            float maxX = mMapRange.xMax - mMoveRange;
            float minZ = mMapRange.yMin + mMoveRange;
            float maxZ = mMapRange.yMax - mMoveRange;
            if (pos.x >= minX && pos.x <= maxX &&
                pos.y >= minZ && pos.y <= maxZ)
            {
                return true;
            }

            return false;
        }

        Vector2 GeneratePointInTriangle(int triangle, Vector3[] vertices, RandomWrapper r, float edgeSize, System.Func<List<Vector2>, Vector3, bool> FindDuplicatedPositionFunc, int maxTryCount, out bool validPoint)
        {
            int tick = 0;
            validPoint = true;
            while (true)
            {
                if (maxTryCount > 0)
                {
                    ++tick;
                    if (tick >= maxTryCount)
                    {
                        validPoint = false;
                        return Vector2.zero;
                    }
                }

                Vector3 point;
                if (edgeSize != 0)
                {
                    int vertexIndex = r.Range(0, 3);
                    var dir = vertices[(vertexIndex + 1) % 3] - vertices[vertexIndex];
                    float distance = r.Range(0.0f, 1.0f);
                    point = vertices[vertexIndex] + distance * dir;
                    dir.Normalize();
                    var perp = new Vector3(dir.z, 0, -dir.x);
                    float halfEdgeSize = edgeSize * 0.5f;
                    float perpDistance = r.Range(-halfEdgeSize, halfEdgeSize);
                    point += perp * perpDistance;
                }
                else
                {
                    //barycentric coord
                    float r1 = r.Range(0.0f, 1.0f);
                    float r2 = r.Range(0.0f, 1.0f);
                    point = (1 - Mathf.Sqrt(r1)) * vertices[0] + (Mathf.Sqrt(r1) * (1 - r2)) * vertices[1] + (Mathf.Sqrt(r1) * r2) * vertices[2];
                }
                
                bool found = false;

                for (int i = 0; i < mTriangleGeneratedPoints[triangle].Count; ++i)
                {
                    if (point == mTriangleGeneratedPoints[triangle][i])
                    {
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    bool foundDuplicated = false;
                    if (FindDuplicatedPositionFunc != null)
                    {
                        if (FindDuplicatedPositionFunc(mGeneratedPoints, point))
                        {
                            foundDuplicated = true;
                        }
                    }

                    if (!foundDuplicated)
                    {
                        mTriangleGeneratedPoints[triangle].Add(point);
                        return Utils.ToVector2(point);
                    }
                }
            }
        }

        public List<Vector2> generatedPoints { get { return mGeneratedPoints; } }

        List<Vector2> mGeneratedPoints = new List<Vector2>();
        List<float> mTrianglePercentage = new List<float>();
        List<float> mTriangleAreas = new List<float>();
        List<List<Vector3>> mTriangleGeneratedPoints = new List<List<Vector3>>();
        float mMoveRange;
        Rect mMapRange;
    }
}

#endif