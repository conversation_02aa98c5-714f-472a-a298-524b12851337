﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class CityLODTest : MonoBehaviour
    {
        //创建一个测试的主城
        public static void Test(Vector3 pos)
        {
            CityLODTest cityLODTest;

            if (mTestObject == null)
            {
                mTestObject = new GameObject("CityLOD Test");
                cityLODTest = mTestObject.AddComponent<CityLODTest>();
            }
            else
            {
                cityLODTest = mTestObject.GetComponent<CityLODTest>();
            }

            cityLODTest.Create(pos);
        }

        void Create(Vector3 cityPos)
        {
            //create city
            var cityRoot = new GameObject("City Root");
            cityRoot.transform.position = cityPos;
            //create city wall
            var wall = CreateWall(cityRoot);
            //create city buildings
            CreateBuildings(wall, cityRoot);
            //add CityLODManager to city root
            var cityLODManager = cityRoot.AddComponent<CityLODManager>();
            //find main building's position
            var mainBuildingPosition = GetMainBuildingPosition(wall);
            //create city main building
            var mainBuilding = CreateMainBuilding(cityRoot);
            //find decoration object
            var decoration = wall.transform.Find("Decoration").gameObject;
            //initialize city lod
            cityLODManager.SetCityGameObjects(wall, decoration, null, null);
            cityLODManager.SetCityInfo(mainBuilding, mainBuildingPosition, cityPos, 3, 7);
            //mark this city as player's city
            cityLODManager.SetAsPlayerCity(true);
            //register event handler when camera is lower to detailed view
            cityLODManager.SetDownScaleEvent(OnDownScaleEventHandler);
        }

        GameObject CreateWall(GameObject cityRoot)
        {
            var wall = MapModuleResourceMgr.LoadGameObject($"{MapModule.runtimeMapResDirectory}Building/WallCity/Wall_test.prefab");
            wall.transform.SetParent(cityRoot.transform, false);
            return wall;
        }

        GameObject CreateMainBuilding(GameObject cityRoot)
        {
            var mainBuilding = MapModuleResourceMgr.LoadGameObject($"{MapModule.runtimeMapResDirectory}world_map/building/building_10/building_10_rescale.prefab");
            mainBuilding.transform.parent = cityRoot.transform;
            return mainBuilding;
        }

        Vector3 GetMainBuildingPosition(GameObject wall)
        {
            return wall.transform.Find("Helper/startPos").position;
        }

        //create some test buildings
        void CreateBuildings(GameObject wall, GameObject cityRoot)
        {
            int buildingCount = 7;
            string[] buildingResPaths = new string[]
            {
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_16/building_16_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_17/building_17_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_18/building_18_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_19/building_19_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_16/building_16_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_17/building_17_rescale.prefab",
                $"{MapModule.runtimeMapResDirectory}world_map/building/building_18/building_18_rescale.prefab",
            };
            for (int i = 0; i < buildingCount; ++i)
            {
                //find test building's node in wall prefab
                string childPath = string.Format("Helper/building{0}", i);
                var buildingNode = wall.transform.Find(childPath);
                var building = MapModuleResourceMgr.LoadGameObject(buildingResPaths[i]);
                building.transform.position = buildingNode.transform.position;
                building.transform.SetParent(cityRoot.transform);
                //set building's lod manager
                var lodControl = building.GetComponent<BuildingLODControl>();
                //lodControl.SetLODManager(cityRoot.GetComponent<BuildingLODManager>());
            }
        }

        void OnDownScaleEventHandler(bool upScale)
        {
            UnityEngine.Debug.Log("OnDownScaleEventHandler");
        }

        //public static void Update()
        //{
        //    //update all cities, this must be called somewhere
        //    CityLODManager.UpdateAllCities();
        //}

        static GameObject mTestObject;
    }
}
