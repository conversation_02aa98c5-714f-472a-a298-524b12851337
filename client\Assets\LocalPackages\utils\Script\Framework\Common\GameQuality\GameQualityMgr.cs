﻿//#define TEST_IOS

using System;
using THelper;
using UnityEngine;

namespace Logic
{
    /// <summary>
    /// 游戏质量管理器。
    /// 这个管理器主要用于根据设备各项硬件参数，选择在性能上最合适的游戏配置。
    /// 游戏配置主要影响设备分辨率，Shader渲染质量，某些功能是否默认启用（如昼夜循环）等。
    /// </summary>
    public sealed class GameQualityMgr : Ins<GameQualityMgr>
    {
        #region 字段
        public const string PREF_KEY_PROFILE = "GameQualityMgr:SettingsProfile";
        public const int PROFILE_ID_INVALID = 0;

        public const string CONFIG_QUALITY = "GameQualityMgr:Config:Quality";
        public const string CONFIG_DAYNIGHT_CYCLE = "GameQualityMgr:Config:DayNightCycle";
        public const string CONFIG_MAP_TROOP_SHOW = "GameQualityMgr:Config:MapTroopShow";
        public const string CONFIG_POWERSAVE_MODE = "GameQualityMgr:Config:PowerSaveMode";
        public const string CONFIG_ALLOW_MSAA = "GameQualityMgr:Config:AllowMSAA";
        public const string CONFIG_POPUP_DESTROY_ON_CLOSE = "GameQualityMgr:Config:PopupDestroyOnClose";
        public const string CONFIG_NICE_VIBRATIONS = "GameQualityMgr:Config:NiceVibrations";
        public const int DESIGN_RESOLUTION_WIDTH = 1080;
        public const int DESIGN_RESOLUTION_HEIGHT = 1920;
        public bool isPowerSaveMode => GameQualityMgr.I.GetSingleConfig(GameQualityMgr.CONFIG_POWERSAVE_MODE, 0) > 0;
 

        /// <summary>
        /// 缓存的配置
        /// </summary>
        private GameQualitySettingsProfile m_CachedProfile;

        /// <summary>
        /// 初始分辨率
        /// </summary>
        private Vector2Int m_InitilalResolution;
#if !UNITY_WEBGL
        /// <summary>
        /// 事件：游戏品质设置变更
        /// </summary>
        public event Action<GameQualitySettingsProfile> onQualityChangedEvent;

        /// <summary>
        /// 调试用的配置ID
        /// </summary>
        private int? m_DebugProfileID;

      
#endif
        #endregion

        /// <summary>
        /// 初始化
        /// </summary>
        public void Init()
        {
#if !UNITY_WEBGL 
            m_CachedProfile = LoadProfile();
#endif

            //针对配置中不存在的iphone机型，过高的分辨率设置会导致无用的开销，玩家也得不到更高的画质
            //if (Screen.width > DESIGN_RESOLUTION_WIDTH && Screen.height > DESIGN_RESOLUTION_HEIGHT)
            //{
            //    m_InitilalResolution = new Vector2Int(DESIGN_RESOLUTION_WIDTH, DESIGN_RESOLUTION_HEIGHT);
            //}
            //else
            {
                m_InitilalResolution = new Vector2Int(Screen.width, Screen.height);
            } 
        }

        /// <summary>
        /// 获取画质配置。
        /// 如果从执行过DetectQuality，或者缓存的Pofile被清除，则会返回null。
        /// </summary>
        /// <returns></returns>
        public GameQualitySettingsProfile GetProfile()
        {
            return m_CachedProfile;
        }

        /// <summary>
        /// 获取当前的游戏品质级别
        /// </summary>
        /// <returns></returns>
        public GameQualityLevel GetQualityLevel()
        {
            return m_CachedProfile != null ? m_CachedProfile.QualityLevel : GameQualityLevel.MediumQuality;
        }

        /// <summary>
        /// 检测设备配置，确定游戏品质设置。
        /// 注：这个方法只有在表数据初始化后才能返回正确结果。
        /// </summary>
        /// <param name="autoChangeQuality">当游戏品质设置发生变化时，是否自动应用更改？</param>
        public void DetectQuality(bool autoChangeQuality)
        {
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(DetectQuality)}");

            var oldProfile = m_CachedProfile;
            var newProfile = DetectProfile();
            m_CachedProfile = newProfile;

            if (newProfile != null && GameQualitySettingsProfile.Compare(oldProfile, newProfile))
            {
                D.Warning?.Log($"{nameof(GameQualityMgr)}: {nameof(DetectQuality)}: New profile settings has changed. Will change quality.");
                SaveProfile(newProfile); 
            }
            else
            {
                D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(DetectQuality)}: New profile settings is invalid/unchanged. Will NOT change quality.");
            }

            ChangeQuality();
        }

        /// <summary>
        /// 应用游戏品质设置更改。
        /// 这会触发onQualityChangedEvent事件。
        /// </summary>
        public void ChangeQuality()
        {
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}");

            if (m_CachedProfile == null)
            {
                D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Profile is null. Will NOT change quality.");
                return;
            }

            if (UnityEngine.Debug.unityLogger.filterLogType == LogType.Log)
            {
                D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)} => {Newtonsoft.Json.JsonConvert.SerializeObject(m_CachedProfile)}");
            }

            var profile = m_CachedProfile;

            // 设置全局Shader属性
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Set globalMaximumLOD = {profile.ShaderMaxLod}");
            Shader.globalMaximumLOD = profile.ShaderMaxLod;
            Shader.SetGlobalFloat("_Quality", (int)profile.QualityLevel);
            Shader.SetGlobalFloat("_SwitchDistanceModifier", profile.TerrainShaderBlendDistanceModifier);

            // 设置分辨率
            // 规则：在分辨率长宽比不变的前提下，宽不大于ResolutionX，且高不大于ResolutionY
            var screenWidth = Mathf.Min(m_InitilalResolution.x, profile.ResolutionX);
            var screenHeight = Mathf.CeilToInt((float)screenWidth * m_InitilalResolution.y / m_InitilalResolution.x);
            screenHeight = Mathf.Min(screenHeight, profile.ResolutionY);
            screenWidth = Mathf.CeilToInt((float)screenHeight * m_InitilalResolution.x / m_InitilalResolution.y);

            //D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Set Resolution = ({screenWidth}, {screenHeight})");
            Screen.SetResolution(screenWidth, screenHeight, true);

            // 设置帧率
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Set Target Framerate = ({profile.Framerate})");
            this.ApplyTargetFrameRate();

            // 锁定物理更新频率
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Set fixedDeltaTime = ({0.1f})");
            Time.fixedDeltaTime = 0.1f;

            // 设置Unity全局品质
            D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(ChangeQuality)}: Set SetQualityLevel = ({profile.QualityLevel})");
            //QualitySettings.SetQualityLevel((int)profile.QualityLevel, true);  // 这个调用会导致部分机型崩溃，先屏蔽掉

            // 设置相机
            foreach (var camera in Camera.allCameras)
            {
                var isUiCamera = camera.CompareTag("UICamera");
                SetupCamera(camera, isUiCamera);
            }

            // 设置外城大地图部队数量限制
            // NOTE：外城大地图的LOD，精细度是由低至高定义。
            //       0为最高精细度，对应大地图相机高度最低时的LOD。
            //       "ICON"为"图标"LOD，所有大地图实体在此LOD下均应显示为图标作为替代，或完全隐藏。
            //       由于绝大多数大地图实体对"ICON"这个LOD级别都有特殊逻辑判断，因此做实体显示数量限制时不方便将分界LOD定在ICON或更高的LOD。
            //       经测试，分界LOD定为ICON-1较为合适。
            //EntityLodLimiter.I.SetLimitLod(LodLevel.ICON - 1, profile.MaxVisibleTroopCount,profile.MaxVisibleTroopIconCount);

            // 保存单独的配置
            SetSingleConfig(CONFIG_QUALITY, profile.HighQualityShader ? 1 : 0, false);
            SetSingleConfig(CONFIG_DAYNIGHT_CYCLE, profile.HighQualityShader ? 1 : 0, false);
            //SetSingleConfig(CONFIG_POWERSAVE_MODE, profile.PowerSaveModeEnable ? 1 : 0, false);
            SetSingleConfig(CONFIG_ALLOW_MSAA, profile.AllowMSAA ? 1 : 0, true);
            SetSingleConfig(CONFIG_POPUP_DESTROY_ON_CLOSE, profile.PopupDestroyOnClose ? 1 : 0, true);
            SaveSingleConfig();

            // 单项设置
            if (GetSingleConfig(CONFIG_QUALITY, 1) > 0)
            {
                Shader.EnableKeyword("HIGH_QUALITY");
            }
            else
            {
                Shader.DisableKeyword("HIGH_QUALITY");
            }
#if !UNITY_WEBGL
            // 触发事件
            onQualityChangedEvent?.Invoke(m_CachedProfile);
#endif
        }

        public void ApplyTargetFrameRate()
        {
            Application.targetFrameRate = 60;

            D.Debug?.Log($"Application.targetFrameRate: {Application.targetFrameRate}");
        }

        /// <summary>
        /// 获取单项设置的值。如果没有找到设置，则返回null，而不会返回0。
        /// </summary>
        /// <param name="configName"></param>
        /// <returns></returns>
        public int? GetSingleConfig(string configName)
        {
            return PlayerPrefs.HasKey(configName) ? PlayerPrefs.GetInt(configName) : (int?)null;
        }

        /// <summary>
        /// 获取单项设置的值。如果没有找到设置，则返回null，而不会返回0。
        /// </summary>
        /// <param name="configName"></param>
        /// <returns></returns>
        public int GetSingleConfig(string configName, int defaultVal)
        {
            return PlayerPrefs.GetInt(configName, defaultVal);
        }

        /// <summary>
        /// 设置单项设置的值
        /// </summary>
        /// <param name="configName"></param>
        /// <param name="val"></param>
        /// <param name="overwrite">是否覆盖已有值</param>
        public void SetSingleConfig(string configName, int val, bool overwrite)
        {
            if (overwrite || !PlayerPrefs.HasKey(configName))
            {
                PlayerPrefs.SetInt(configName, val);
            }
        }

        // 保存单项设置
        public void SaveSingleConfig()
        {
            PlayerPrefs.Save();
        }

        public void SetupCamera(Camera camera, bool isUiCamera)
        {
            var profile = m_CachedProfile;

            if (profile == null)
            {
                return;
            }

            var allowHDR = false;  // HDR一律关闭
            var allowMSAA = (GetSingleConfig(CONFIG_ALLOW_MSAA, 1) > 0);
            //if (isUiCamera) { allowMSAA = true; }

            D.Debug?.Log($"{nameof(GameQualityMgr)}: 设置相机设置。camera={camera.name}, isUiCamera={isUiCamera}, allowHDR={allowHDR}, allowMSAA={allowMSAA}");

            camera.allowHDR = allowHDR;
            camera.allowMSAA = allowMSAA;
        }

  

        #region 内部实现
        private GameQualitySettingsProfile LoadProfile()
        {
            try
            {
                var profileStr = PlayerPrefs.GetString(PREF_KEY_PROFILE);

                D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(LoadProfile)}: profileStr={profileStr}");

                if (string.IsNullOrEmpty(profileStr))
                {
                    return null;
                }

                return Newtonsoft.Json.JsonConvert.DeserializeObject<GameQualitySettingsProfile>(profileStr);
            }
            catch (Exception e)
            {
                D.Warning?.Log($"{nameof(GameQualityMgr)}: {nameof(LoadProfile)}: {e}");
                return null;
            }
        }

        private void SaveProfile(GameQualitySettingsProfile profile)
        {
            try
            {
                if (profile == null)
                {
                    throw new ArgumentNullException(nameof(profile));
                }

                var profileStr = Newtonsoft.Json.JsonConvert.SerializeObject(profile);

                D.Debug?.Log($"{nameof(GameQualityMgr)}: {nameof(SaveProfile)}: profileStr={profileStr}");

                PlayerPrefs.SetString(PREF_KEY_PROFILE, profileStr);
                PlayerPrefs.Save();
            }
            catch (Exception e)
            {
                D.Warning?.Log($"{nameof(GameQualityMgr)}: {nameof(SaveProfile)}: {e}");
            }
        }

        /// <summary>
        /// 检测设备匹配的最优配置
        /// </summary>
        /// <returns></returns>
        private GameQualitySettingsProfile DetectProfile()
        {
            return GameQualitySettingsProfile.FromDefaultData();
        }
         

#endregion
    }

    /// <summary>
    /// 游戏品质配置。
    /// 这个数据结构主要用于持久化配置数据。
    /// </summary>
    [Serializable]
    public sealed class GameQualitySettingsProfile
    {
        public string ProfileName;               // 配置名称
        public GameQualityLevel QualityLevel;    // 品质级别
        public int ResolutionX;                  // 水平分辨率
        public int ResolutionY;                  // 水平分辨率
        public int Framerate;                    // 帧率
        public int FramerateLow;                    // 低帧率模式帧率
        public int ShaderMaxLod;                 // Shader最大Lod
        public bool HighQualityShader;           // 高品质Shader
        public float TerrainShaderBlendDistanceModifier;  // 地块混合距离修正系数
        public bool DayNightCycleEnable;                  // 启用昼夜循环
        public int MaxVisibleTroopCount;                  // 视野内可见部队的最大数量（大地图）
        public bool AllowMSAA = true;            // 开启抗锯齿
        public bool PopupDestroyOnClose;         // 面板关闭时统一销毁
        public bool IsUnionCastleShow;           // 联盟城堡是否一直显示
        public int TroopAtkCount;                // 军队士兵攻击数量限制（一次攻击，有多少士兵有攻击效果）
        public int MaxVisibleTroopIconCount;
        //public static GameQualitySettingsProfile FromTableData(Cfg.G.CGameQualitySettingsProfile tableData)
        //{
        //    return new GameQualitySettingsProfile()
        //    {
        //        ProfileName = tableData.ProfileName,
        //        QualityLevel = (GameQualityLevel)tableData.QualityLevel,
        //        ResolutionX = tableData.ResolutionX,
        //        ResolutionY = tableData.ResolutionY,
        //        Framerate = tableData.Framerate,
        //        FramerateLow = tableData.FramerateLow,
        //        ShaderMaxLod = tableData.ShaderMaxLod,
        //        HighQualityShader = tableData.HighQualityShader > 0,
        //        TerrainShaderBlendDistanceModifier = tableData.TerrainShaderBlendDistanceModifier,
        //        DayNightCycleEnable = tableData.DayNightCycleEnable > 0,
        //        MaxVisibleTroopCount = tableData.MaxVisibleTroopCount,
        //        AllowMSAA = tableData.AllowMSAA > 0,
        //        PopupDestroyOnClose = tableData.PopupDestroyOnClose > 0,
        //        IsUnionCastleShow = tableData.UnionCastleShow > 0,
        //        TroopAtkCount = tableData.MaxVisibleTroopEffect,
        //        MaxVisibleTroopIconCount = tableData.MaxVisibleTroopIconCount,
        //    };
        //}

        public static GameQualitySettingsProfile FromDefaultData()
        {
            return new GameQualitySettingsProfile()
            {
                ProfileName = "high",
                QualityLevel = GameQualityLevel.HighQuality,
                ResolutionX =1080,
                ResolutionY = 1920,
                Framerate = 60,
                FramerateLow = 60,
                ShaderMaxLod = 500,
                HighQualityShader = 1 > 0,
                TerrainShaderBlendDistanceModifier = 0.75f,
                DayNightCycleEnable = 1 > 0,
                MaxVisibleTroopCount = 30,
                AllowMSAA = 1 > 0,
                PopupDestroyOnClose = 0 > 0,
                IsUnionCastleShow = 0 > 0,
                TroopAtkCount = 3,
                MaxVisibleTroopIconCount = 100,
            };
        }

        /// <summary>
        /// 比较传入的两套配置是否不同。true:不同，false:相同
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static bool Compare(GameQualitySettingsProfile a, GameQualitySettingsProfile b)
        {
            return a == null || b == null ||
                   a.ProfileName != b.ProfileName ||
                   a.QualityLevel != b.QualityLevel ||
                   a.ResolutionX != b.ResolutionX ||
                   a.ResolutionY != b.ResolutionY ||
                   a.Framerate != b.Framerate ||
                   a.FramerateLow != b.FramerateLow ||
                   a.ShaderMaxLod != b.ShaderMaxLod ||
                   a.HighQualityShader != b.HighQualityShader ||
                   a.TerrainShaderBlendDistanceModifier != b.TerrainShaderBlendDistanceModifier ||
                   a.DayNightCycleEnable != b.DayNightCycleEnable ||
                   a.MaxVisibleTroopCount != b.MaxVisibleTroopCount ||
                   a.AllowMSAA != b.AllowMSAA ||
                   a.PopupDestroyOnClose != b.PopupDestroyOnClose ||
                   a.IsUnionCastleShow != b.IsUnionCastleShow ||
                   a.MaxVisibleTroopIconCount != b.MaxVisibleTroopIconCount ||
                   a.TroopAtkCount != b.TroopAtkCount;
        }
    }

    public enum GameQualityLevel
    {
        LowestQuality = 0,
        LowQuality = 1,
        MediumLowQuality = 2,
        MediumQuality = 3,
        HighQuality = 4,
        VeryHighQuality = 5,
    }
}