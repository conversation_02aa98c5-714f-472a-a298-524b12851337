﻿ 



 
 



#if UNITY_EDITOR

namespace TFW.Map
{
    public class ActionChangeTerrainHeightsAndResolution : CompoundAction
    {
        public ActionChangeTerrainHeightsAndResolution(int layerID, int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution) : base("change terrain heights and resolution")
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as BlendTerrainLayer;
            var tile = layer.GetTile(tileX, tileY);
            if (tile.resolution != resolution)
            {
                var act = new ActionSetTerrainTileResolution(layerID, tileX, tileY, resolution);
                Add(act, true);
            }

            mChangeTerrainHeightsAction = new ActionChangeTerrainHeights(layerID, tileX, tileY, minX, minY, maxX, maxY, resolution);
            Add(mChangeTerrainHeightsAction, false);
        }

        public void Begin()
        {
            mChangeTerrainHeightsAction.Begin();
        }

        public void End()
        {
            mChangeTerrainHeightsAction.End();
        }

        ActionChangeTerrainHeights mChangeTerrainHeightsAction;
    }
}

#endif