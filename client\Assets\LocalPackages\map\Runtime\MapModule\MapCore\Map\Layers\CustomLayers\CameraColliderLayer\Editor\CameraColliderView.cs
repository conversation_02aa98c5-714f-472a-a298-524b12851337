﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/12/4

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class CameraColliderView : PolygonObjectView
    {
        public CameraColliderView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        //创建视图使用的模型
        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            Debug.Assert(data != null);

            ModelBase model = new CameraColliderModel(data as CameraColliderData);
            model.transform.SetParent(mLayerView.root.transform, true);

            return model;
        }

        public void CreateTopOutline(CameraColliderData collider)
        {
            var model = mModel as CameraColliderModel;
            model.CreateTopOutline(collider);
        }

        public void UpdateVertex(int index)
        {
            if (mModel != null)
            {
                var data = Map.currentMap.FindObject(objectDataID) as CameraColliderData;
                (mModel as CameraColliderModel).UpdateVertex(index, data);
            }
        }

        public void CreateCollider(CameraColliderData data)
        {
            if (mModel != null)
            {
                (mModel as CameraColliderModel).CreateCollider(data);
            }
        }
    }
}


#endif