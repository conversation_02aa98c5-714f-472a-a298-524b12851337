﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveLODLayer(BinaryWriter writer, LODLayer layer)
        {
            BeginSection(MapDataSectionType.LODLayer, writer);

            writer.Write(VersionSetting.LODLayerStructVersion);

            //-----------------version 1 start---------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            writer.Write(layer.GetLayerData().tileWidth);
            writer.Write(layer.GetLayerData().tileHeight);

            //save map layer lod config
            SaveLODLayerLODConfigV1(writer, layer.GetLayerData());
            //----------------version 1 end----------------------------
            //-----------------version 2 start------------------------------
            SaveLODLayerLODConfigV2(writer, layer.GetLayerData());
            //-----------------version 2 end------------------------------
        }

        void SaveLODLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SaveLODLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif
