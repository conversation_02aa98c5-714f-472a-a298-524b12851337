**v2.5.1** 2022/11/21
- 新增支持设置实例名称
- 新增支持获取国家/地区信息
- 优化代码

**v2.5.0** 2022/10/20
- 优化代码

**v2.4.1** 2022/08/08
- 支持 Package Manager 接入
- 新增自动采集场景加载、卸载事件
- 优化代码

**v2.4.0** 2022/06/24
- 新增支持去重追加用户属性
- 新增支持自动采集事件回调
- 新增支持 iOS / Android 数据加密上报
- 优化代码

**v2.3.1** 2022/04/21
- 新增预置属性禁用开关
- 优化代码

**v2.3.0** 2022/02/28
- 全平台支持缓存事件、批量发送
- 优化代码

**v2.2.5** 2022/01/17
- 新增预置属性`#app_version`
- 支持 Switch 等平台，放开对支持平台的限制
- 简化初始化流程，放开 Unity 预置体限制
- 优化代码

**v2.2.4** 2021/11/15
- 支持复杂数据类型传输
- 自动采集事件支持自定义属性
- 优化代码

**v2.2.3** 2021/09/24
- 适配 WebGL 平台
- 优化代码

**v2.2.2** 2021/09/15
- 支持手动初始化
- 支持事件黑名单
- 优化代码

**v2.2.1** 2021/07/21
- 优化代码

**v2.2.0** 2021/06/16
- 适配低版本 Xcode 版本
- 适配 .NET 3.5 / 2017.x 环境
- 新增 C# 异常采集
- 调整事件属性、公共属性、预置属性的覆盖顺序
- 更新 Android SDK (v2.7.0)、iOS SDK (v2.7.0)

**v2.1.5** 2021/04/19
- 兼容低版本 .NET
- 代码优化


**v2.1.4** 2021/03/15
- 适配 iOS 14
- 适配 Android 11
- 新增预置属性`#bundle_id` (应用唯一标识)
- 优化网络信号获取逻辑


**v2.1.3** 2021/01/04
- 支持 UnityEditor 中上报数据
- 支持 PC 端游戏 ( Windows, MacOS )
- 多实例场景,支持代码设置默认实例


**v.2.1.2**(2020-10-31)
- autodata中信息放入track事件的properties
- 移除iOS bundle资源文件
- install,start事件以后立即flush
- 新增iOS 5G信息采集;更新5G情况的数据上报判断逻辑
- 调整未知网络数据上报逻辑调整
- 默认上报策略调整为所有网络情况均可上报
- 大量安卓日志打印OOM异常优化


**v2.1.1** (2020-08-25)
- 修复特殊事件不设置timeZone导致上报错误的#zone_offset的问题.

**v2.1.0** (2020-08-24)
- 支持首次事件, 允许传入自定义的 ID 校验是否首次上报
- 支持可更新、可重写的事件
- 优化#lib/#lib_version 字段为 Unity SDK 信息
- 升级原生 SDK 版本为 v2.6.0

**v2.0.8** (2020-06-28)
- 更新原生 Android SDK，解决极端情况下的空指针异常

**v2.0.7** (2020-06-23)
- 新增预置属性 #system_language
- 更新原生 SDK 版本号: Android v2.5.5, iOS v2.5.5
- Android 不再通过修改 gradle 配置来引入ThinkingAnalyticsSDK 依赖

**v2.0.6** (2020-06-17)
- 解决 iOS 32 位机型 long 型溢出的问题

**v2.0.5** (2020-05-19)
- 解决 2018.04 版本无法使用 ntp 校准时间的问题

**v2.0.4** (2020-05-15)
- 更新原生 iOS SDK 到 v2.5.2，解决上个版本中 Debug 模式问题

**v2.0.3** (2020-05-14)
- 更新原生 SDK 版本，适配 TA 后台埋点管理功能
- 优化代码，避免出现异常（可能会导致与其他崩溃采集 SDK 冲突）

**v2.0.2** (2020-04-17)
- 修复 Double 类型在自定义 CultureInfo 的场景下格式错误

**v2.0.1** (2020-04-14)
- 修复 Android 平台 DEBUG 模式上报事件的 BUG.

**v2.0.0** (2020-04-03)
- 修改自动采集逻辑，支持四种类型的自动采集事件：启动、关闭、崩溃、安装
- 支持 SDK 时间校准功能，支持时间戳校准和 NTP 服务器校准
- 允许为用户属性相关设置接口传入指定时间

**v1.4.3** (2020-03-19)
- 支持对数据 #time 属性的默认时区设置
- 更新原生 SDK 版本

**v1.4.2** (2020-02-21)
- 更新iOS 插件，修复之前版本bug

**v1.4.1** (2020-02-14)
- 适配 Unity 2019.3.1f1

**v1.4.0** (2020-02-11)
- 属性值支持 List / Array 类型
- 新增 UserAppend 接口
- 支持 Debug 模式数据校验
- 支持为每个实例单独配置接收端地址
- 去除本地数据格式校验

**v1.3.1** (2019-12-25)
- 修复 Android 4.3 以下版本退出超时
- 修复 未包含 iOS 开发环境时的报错

**v1.3.0** (2019-10-18)
- 新增 UserUnset 接口：用于重置用户属性.
- 事件预置属性新增时间偏移，适配多时区需求

**v1.2.2** (2019-10-11)
- 优化 iOS 平台上报逻辑，解决缓存数据库异常导致的重复上报

**v1.2.1** (2019-09-27)
- 解决老版本 .NET 的兼容性问题

**v1.2.0** (2019-08-31)
- 支持关闭/打开数据上报
- 支持暂停/继续数据上报
- 支持轻量级实例
- 修复2019.2.1f 版本设置网络类型失败
- 修复timeEvent 不准确问题
- Android/iOS SDK 升级为2.1.0

**v1.1.0** (2019-08-09)
- 支持获取设备ID
- 支持动态公共属性
- 支持安装事件采集
- Android SDK 默认升级为2.0.2
- iOS SDK 升级为2.0.1
- 支持上报缓存选项，用户可以选择主动调用StartTrack() 去开始上报

**v1.0.0** (2019-06-20)
- 支持访客ID 和用户账号的设置
- 支持事件和用户属性上报
- 支持 ta_app_start 和 ta_app_end 事件的自动上报
- 支持公共属性接口
- 支持 timeEvent 接口
- 支持多项目上报
