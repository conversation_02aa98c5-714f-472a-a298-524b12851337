﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    //管理地图层的视图,不暴露lua接口
    public abstract class MapLayerView
    {
        //asyncLoading: 是否使用加载队列来加载地图对象
        public MapLayerView(MapLayerData layerData, bool asyncLoading)
        {
            mLayerData = layerData;
            mAsyncLoading = asyncLoading;
            mRoot = new GameObject(layerData.name);
            mRoot.transform.SetParent(layerData.map.view.root.transform, true);
        }

        public virtual void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
        }

        public virtual void SetViewSize(float newViewWidth, float newViewHeight)
        {
        }

        public abstract void SetZoom(float zoom, bool lodChanged);
        public abstract void ReloadVisibleViews();

        public virtual bool active { set { mRoot.SetActive(value); } get { return mRoot.activeSelf; } }
        public GameObject root { get { return mRoot; } }
        public MapLayerData layerData { get { return mLayerData; } }
        public string name { get { return mLayerData.name; } }
        public bool asyncLoading { get { return mAsyncLoading; } set { mAsyncLoading = value; } }

        //地图层的game object
        GameObject mRoot;
        protected MapLayerData mLayerData;
        //是否使用加载队列加载该地图层的模型
        bool mAsyncLoading;
    }
}
