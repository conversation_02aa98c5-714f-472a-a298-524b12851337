﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //地图上对象使用的对象池
    public class TileObjectDataPool
    {
        public void OnDestroy()
        {
            Clear();
        }

        public TileObjectData Require(int id, Map map, ChildPrefabTransform2 prefabTransform, string prefabPath, Rect worldBounds, Vector3 position, Vector3 scaling, Quaternion rotation, int localIndex, int tileIndex, int lod, float baseScale, System.Action<TileObjectData> onObjectScaleChangeCallback, bool useCullManager)
        {
            TileObjectData result = null;
            int n = mObjects.Count;
            if (n > 0)
            {
                result = mObjects[n - 1];
                mObjects.RemoveAt(n - 1);
            }
            else
            {
                BaseObject.sAddObjects = false;
                result = new TileObjectData(id, map);
                BaseObject.sAddObjects = true;
            }

            BaseObject.RemoveObject(map, id);
            result.Set(id, prefabTransform.objectType, prefabTransform, prefabPath, worldBounds, position, scaling, rotation, localIndex, tileIndex, lod, baseScale, onObjectScaleChangeCallback, useCullManager);
            BaseObject.AddObject(result);

            return result;
        }

        public void Release(TileObjectData tileObject)
        {
            if (tileObject != null)
            {
                mObjects.Add(tileObject);
                tileObject.OnDestroy();
                BaseObject.RemoveObject(tileObject);
            }
        }

        public void Clear()
        {
            mObjects.Clear();
        }

        List<TileObjectData> mObjects = new List<TileObjectData>(10000);
    }
}
