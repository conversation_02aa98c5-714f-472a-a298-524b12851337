﻿ 



 
 

﻿
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadTerrainPrefabManager(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.TerrainPrefabManager);
            int version = reader.ReadInt32();

            //--------------------version 1 start-------------------------
            int nGroups = reader.ReadInt32();
            mEditorData.terrainPrefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                int nPrefabs = reader.ReadInt32();
                var group = new config.PrefabGroup();
                group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
                for (int k = 0; k < nPrefabs; ++k)
                {
                    group.prefabPaths[k] = new config.PrefabSubGroup();
                    group.prefabPaths[k].prefabPath = Utils.ReadString(reader);
                }

                mEditorData.terrainPrefabManager.groups[i] = group;
            }
            //--------------------version 1 end-------------------------
            //--------------------version 2 start-------------------------
            if (version >= 2)
            {
                //load subgroup prefab paths
                for (int i = 0; i < nGroups; ++i)
                {
                    var group = mEditorData.terrainPrefabManager.groups[i];
                    int nPrefabs = reader.ReadInt32();
                    for (int k = 0; k < nPrefabs; ++k)
                    {
                        int subgroupPrefabCount = reader.ReadInt32();
                        group.prefabPaths[k].subGroupPrefabPaths = new string[subgroupPrefabCount];
                        for (int x = 0; x < subgroupPrefabCount; ++x)
                        {
                            group.prefabPaths[k].subGroupPrefabPaths[x] = Utils.ReadString(reader);
                        }
                    }
                }
            }
            //--------------------version 2 end-------------------------
            //--------------------version 3 start-------------------------
            for (int i = 0; i < nGroups; ++i)
            {
                var group = mEditorData.terrainPrefabManager.groups[i];
                if (version >= 3)
                {
                    group.id = reader.ReadInt32();
                }
                else
                {
                    group.id = i;
                }
            }
            //--------------------version 3 end-------------------------
        }
    }
}
