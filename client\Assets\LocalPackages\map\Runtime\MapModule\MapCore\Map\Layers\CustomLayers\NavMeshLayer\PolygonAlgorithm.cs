﻿ 



 
 



//#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using ClipperLib;

namespace TFW.Map
{
    public enum PolygonType
    {
        Hole,
        NoneHole,
        All,
    }

    public static class PolygonAlgorithm
    {
        #region api
        //扩大多边形一圈
        public static List<List<Vector3>> ExpandPolygon(float radius, List<Vector3> polygonVertices)
        {
            if (radius == 0)
            {
                List<Vector3> copyVal = new List<Vector3>();
                copyVal.AddRange(polygonVertices);
                return new List<List<Vector3>>() { copyVal };
            }

            List<IntPoint> path = new List<IntPoint>();
            for (int i = 0; i < polygonVertices.Count; ++i)
            {
                path.Add(new IntPoint(Utils.UpScale(polygonVertices[i].x), Utils.UpScale(polygonVertices[i].z)));
            }

            ClipperOffset offsetPolygon = new ClipperOffset();
            offsetPolygon.AddPath(path, JoinType.jtSquare, EndType.etClosedPolygon);
            
            PolyTree result = new PolyTree();
            offsetPolygon.Execute(ref result, Utils.UpScale(radius));
            var noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            var holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
            List<List<Vector3>> ret = new List<List<Vector3>>();
            for (int i = 0; i < noneHoles.Count; ++i)
            {
                ret.Add(noneHoles[i]);
            }
#if false
            //temp code
            for (int i = 0; i < noneHoles.Count; ++i)
            {
                var obj = new GameObject($"extended none holes {i}");
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(noneHoles[i]);
            }

            for (int i = 0; i < holes.Count; ++i)
            {
                var obj = new GameObject($"extended holes {i}");
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(holes[i]);
            }
#endif
            return ret;
        }

        //获取多边形相交
        public static List<Vector3> GetPolygonIntersections(List<Vector3> pa, List<Vector3> pb)
        {
            List<IntPoint> pathA = new List<IntPoint>();
            for (int i = 0; i < pa.Count; ++i)
            {
                pathA.Add(new IntPoint(Utils.UpScale(pa[i].x), Utils.UpScale(pa[i].z)));
            }

            List<IntPoint> pathB = new List<IntPoint>();
            for (int i = 0; i < pb.Count; ++i)
            {
                pathB.Add(new IntPoint(Utils.UpScale(pb[i].x), Utils.UpScale(pb[i].z)));
            }

            Clipper clipper = new Clipper();
            clipper.AddPath(pathB, PolyType.ptSubject, true);
            clipper.AddPath(pathA, PolyType.ptClip, true);
            List<List<IntPoint>> intersections = new List<List<IntPoint>>();
            bool succeeded = clipper.Execute(ClipperLib.ClipType.ctIntersection, intersections, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
            Debug.Assert(succeeded);

            List<Vector3> ret = new List<Vector3>();
            if (intersections.Count == 0)
            {
                return ret;
            }
            if (intersections.Count != 1)
            {
                Debug.Log("intersections: " + intersections.Count);
            }

            for (int i = 0; i < intersections.Count; ++i)
            {
                var list = intersections[i];
                for (int j = 0; j < list.Count; ++j)
                {
                    ret.Add(new Vector3((float)Utils.DownScale(intersections[i][j].X), 0, (float)Utils.DownScale(intersections[i][j].Y)));
                }
            }

            return ret;
        }

        //计算一个矩形区域与一堆多边形的交集
        public static List<List<Vector3>> GetPolygonIntersections(float minX, float minZ, float maxX, float maxZ, List<List<Vector3>> polygons, out List<List<Vector3>> holes)
        {
            var pathA = CreateBoundsPath(minX, minZ, maxX, maxZ);
            var pathsB = ConvertVector3ListListToIntPointListList(polygons);

            Clipper clipper = new Clipper();
            clipper.AddPath(pathA, PolyType.ptSubject, true);
            clipper.AddPaths(pathsB, PolyType.ptClip, true);
            PolyTree result = new PolyTree();
            bool succeeded = clipper.Execute(ClipperLib.ClipType.ctIntersection, result, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
            Debug.Assert(succeeded);

            var intersectedRegions = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
            return intersectedRegions;
        }

        //计算合并障碍物后的多边形
        public static List<List<Vector3>> GetCombinedObstaclePolygons(PrefabOutlineType type, List<IObstacle> obstacles, int startIndex, int endIndex, float extentSize, float scaleFactor, out List<List<Vector3>> holes)
        {
            holes = null;
            //合并所有的障碍物
            List<List<Vector3>> combinedResult = new List<List<Vector3>>();
            if (obstacles.Count > 0)
            {
                Debug.Assert(startIndex >= 0 && startIndex < obstacles.Count && endIndex >= 0 && endIndex < obstacles.Count);

                Clipper clipper = new Clipper();

                List<List<Vector3>> noneHoles = new List<List<Vector3>>();
                holes = new List<List<Vector3>>();

                List<List<IntPoint>> paths = new List<List<IntPoint>>();
                for (int i = startIndex; i <= endIndex; ++i)
                {
                    var path = CreateObstaclePath(type, obstacles[i], scaleFactor);
                    if (path.Count > 0)
                    {
                        if (extentSize > 0 && obstacles[i].IsExtendable())
                        {
                            //扩展障碍物多边形
                            ClipperOffset offsetPolygon = new ClipperOffset();
                            offsetPolygon.AddPath(path, JoinType.jtSquare, EndType.etClosedLine);
                            List<List<IntPoint>> extended = new List<List<IntPoint>>();
                            offsetPolygon.Execute(ref extended, Utils.UpScale(extentSize));
                            path = extended[0];
                        }
                        paths.Add(path);
                    }
                }
                clipper.AddPaths(paths, PolyType.ptSubject, true);
                List<List<IntPoint>> solution = new List<List<IntPoint>>();
                clipper.Execute(ClipperLib.ClipType.ctUnion, solution, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
                paths = solution;
                PolyTree combinedTree = new PolyTree();
                bool succeeded = clipper.Execute(ClipperLib.ClipType.ctUnion, combinedTree, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
                combinedResult = GetPolygonsFromPolyTree(combinedTree, PolygonType.NoneHole);
                holes.AddRange(GetPolygonsFromPolyTree(combinedTree, PolygonType.Hole));
                noneHoles.AddRange(combinedResult);

                combinedResult = ConvertIntPointListListToVector3ListList(paths);
#if false
                //temp code
                for (int i = 0; i < combinedResult.Count; ++i)
                {
                    var obj = new GameObject("none hole " + i);
                    var dp = obj.AddComponent<DrawPolygon>();
                    dp.SetVertices(combinedResult[i]);
                }
                for (int i = 0; i < holes.Count; ++i)
                {
                    var obj = new GameObject("hole " + i);
                    var dp = obj.AddComponent<DrawPolygon>();
                    dp.SetVertices(holes[i]);
                }
#endif
            }
            return combinedResult;
        }

        //return a - b
        public static void GetDifferencePolygon(List<Vector3> a, List<Vector3> b, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            Clipper clipper = new Clipper();
            clipper.AddPath(ConvertPolygon(a, Vector3.zero), PolyType.ptSubject, true);
            clipper.AddPath(ConvertPolygon(b, Vector3.zero), PolyType.ptClip, true);
            PolyTree result = new PolyTree();
            clipper.Execute(ClipperLib.ClipType.ctDifference, result);
            noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
        }

        public static void GetDifferencePolygon(List<Vector3> a, List<List<Vector3>> b, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            Clipper clipper = new Clipper();
            clipper.AddPath(ConvertPolygon(a, Vector3.zero), PolyType.ptSubject, true);
            clipper.AddPaths(ConvertVector3ListListToIntPointListList(b), PolyType.ptClip, true);
            PolyTree result = new PolyTree();
            clipper.Execute(ClipperLib.ClipType.ctDifference, result);
            noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
        }

        //return a - b
        public static void GetDifferencePolygon(List<List<Vector3>> a, List<List<Vector3>> b, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            Clipper clipper = new Clipper();
            clipper.AddPaths(ConvertVector3ListListToIntPointListList(a), PolyType.ptSubject, true);
            clipper.AddPaths(ConvertVector3ListListToIntPointListList(b), PolyType.ptClip, true);
            PolyTree result = new PolyTree();
            clipper.Execute(ClipperLib.ClipType.ctDifference, result);
            noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
        }

        public static void GetDifferencePolygon(float minX, float minZ, float maxX, float maxZ, List<List<Vector3>> obstacles, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes, PolyFillType subjType = PolyFillType.pftNonZero, PolyFillType clipType = PolyFillType.pftNonZero)
        {
            Clipper clipper = new Clipper();
            var boundsPath = CreateBoundsPath(minX, minZ, maxX, maxZ);
            clipper.AddPath(boundsPath, PolyType.ptSubject, true);
            clipper.AddPaths(ConvertVector3ListListToIntPointListList(obstacles), PolyType.ptClip, true);
            PolyTree result = new PolyTree();
            clipper.Execute(ClipperLib.ClipType.ctDifference, result, subjType, clipType);
            noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
            holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
        }

        //计算一个矩形区域与一堆三角形的差集
        public static void GetDifferencePolygons(float minX, float minZ, float maxX, float maxZ, List<Vector3Int> triangleIndices, Vector3[] triangleVertices, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            Clipper clipper = new Clipper();
            var boundsPath = CreateBoundsPath(minX, minZ, maxX, maxZ);
            clipper.AddPath(boundsPath, PolyType.ptSubject, true);

            List<List<IntPoint>> obstaclePaths = new List<List<IntPoint>>();
            for (int i = 0; i < triangleIndices.Count; ++i)
            {
                var obstaclePath = CreateTrianglePath(triangleIndices[i], triangleVertices);
                obstaclePaths.Add(obstaclePath);
            }

            clipper.AddPaths(obstaclePaths, PolyType.ptClip, true);
            //减去tile中的三角形,得到剩下的空间
            PolyTree differencePolyTree = new PolyTree();
            bool succeeded = clipper.Execute(ClipperLib.ClipType.ctDifference, differencePolyTree, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
            Debug.Assert(succeeded);

            noneHoles = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.NoneHole);
            holes = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.Hole);
        }

        //计算一堆多边形与一堆三角形的差集
        public static void GetDifferencePolygons(List<List<Vector3>> polygons, List<Vector3Int> triangleIndices, Vector3[] triangleVertices, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            noneHoles = new List<List<Vector3>>();
            holes = new List<List<Vector3>>();

            if (polygons.Count == 0)
            {
                return;
            }

            Clipper clipper = new Clipper();
            clipper.AddPaths(ConvertVector3ListListToIntPointListList(polygons), PolyType.ptSubject, true);

            List<List<IntPoint>> obstaclePaths = new List<List<IntPoint>>();
            for (int i = 0; i < triangleIndices.Count; ++i)
            {
                var obstaclePath = CreateTrianglePath(triangleIndices[i], triangleVertices);
                obstaclePaths.Add(obstaclePath);
            }

            clipper.AddPaths(obstaclePaths, PolyType.ptClip, true);
            //减去tile中的三角形,得到剩下的空间
            PolyTree differencePolyTree = new PolyTree();
            bool succeeded = clipper.Execute(ClipperLib.ClipType.ctDifference, differencePolyTree, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
            if (!succeeded)
            {
                Debug.Assert(false, "GetDifferencePolygons failed!");
            }

            noneHoles = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.NoneHole);
            holes = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.Hole);
        }

        //return polygon - obstacles
        public static void GetDifferencePolygons(List<Vector3> polygon, List<List<Vector3>> obstacles, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes, PolyFillType subjType = PolyFillType.pftNonZero, PolyFillType clipType = PolyFillType.pftNonZero)
        {
            noneHoles = new List<List<Vector3>>();
            holes = new List<List<Vector3>>();
            if (obstacles.Count == 0)
            {
                noneHoles.Add(polygon);
            }
            else
            {
                Clipper clipper = new Clipper();
                var polygonPath = ConvertPolygon(polygon, Vector3.zero);
                var obstaclePaths = ConvertVector3ListListToIntPointListList(obstacles);
                clipper.AddPath(polygonPath, PolyType.ptSubject, true);
                clipper.AddPaths(obstaclePaths, PolyType.ptClip, true);
                //减去tile中的三角形,得到剩下的空间
                PolyTree differencePolyTree = new PolyTree();
                bool succeeded = clipper.Execute(ClipperLib.ClipType.ctDifference, differencePolyTree, subjType, clipType);
                Debug.Assert(succeeded);
                noneHoles = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.NoneHole);
                holes = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.Hole);
            }
        }

        //return polygons - obstacles
        public static void GetDifferencePolygons(List<List<Vector3>> polygons, List<List<Vector3>> obstacles, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            noneHoles = new List<List<Vector3>>();
            holes = new List<List<Vector3>>();
            if (obstacles.Count == 0)
            {
                noneHoles.AddRange(polygons);
            }
            else
            {
                Clipper clipper = new Clipper();
                var polygonPaths = ConvertVector3ListListToIntPointListList(polygons);
                var obstaclePaths = ConvertVector3ListListToIntPointListList(obstacles);
                clipper.AddPaths(polygonPaths, PolyType.ptSubject, true);
                clipper.AddPaths(obstaclePaths, PolyType.ptClip, true);
                //减去tile中的三角形,得到剩下的空间
                PolyTree differencePolyTree = new PolyTree();
                bool succeeded = clipper.Execute(ClipperLib.ClipType.ctDifference, differencePolyTree, PolyFillType.pftNonZero, PolyFillType.pftNonZero);
                Debug.Assert(succeeded);
                noneHoles = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.NoneHole);
                holes = PolygonAlgorithm.GetPolygonsFromPolyTree(differencePolyTree, PolygonType.Hole);
            }
        }

        static void RemoveSameHoles(List<List<Vector3>> noneHoles, List<List<Vector3>> holes)
        {
            //删除相同的多边形
            for (int i = noneHoles.Count - 1; i >= 0; --i)
            {
                for (int j = holes.Count - 1; j >= 0; --j)
                {
                    if (Utils.ListEqual(noneHoles[i], holes[j], 0.01f))
                    {
                        holes.RemoveAt(j);
                        break;
                    }
                }
            }
        }

        //计算减去障碍物后的多边形
        public static void GetDifferencePolygons(PrefabOutlineType type, float minX, float minZ, float maxX, float maxZ, List<IObstacle> obstacles, float extendedSize, float scaleFactor, bool removeSameHoles, out List<List<Vector3>> noneHoles, out List<List<Vector3>> holes)
        {
            //创建可行走区域
            var boundsPath = CreateBoundsPath(minX, minZ, maxX, maxZ);
            if (obstacles.Count > 0)
            {
                List<List<Vector3>> combinedHoles;
                var combinedObstacles = GetCombinedObstaclePolygons(type, obstacles, 0, obstacles.Count - 1, extendedSize, scaleFactor, out combinedHoles);

#if false
                for (int i = 0; i < combinedObstacles.Count; ++i) {
                    EditorUtils.CreateDrawPolygon("noneHoles", combinedObstacles[i]);
                }

                for (int i = 0; i < combinedHoles.Count; ++i)
                {
                    EditorUtils.CreateDrawPolygon("holes", combinedHoles[i]);
                }

                Triangulator.TriangulatePolygons(combinedObstacles, combinedHoles, null, out var vertices, out var indices);
                var viewer = new BigMeshViewer();
                viewer.Create(null, "combined obstacles", vertices, indices, false, Color.green);
#endif
                if (removeSameHoles)
                {
                    RemoveSameHoles(combinedObstacles, combinedHoles);
                }

                Clipper clipper = new Clipper();
                clipper.AddPath(boundsPath, PolyType.ptSubject, true);
                clipper.AddPaths(ConvertVector3ListListToIntPointListList(combinedHoles), PolyType.ptSubject, true);
                clipper.AddPaths(ConvertVector3ListListToIntPointListList(combinedObstacles), PolyType.ptClip, true);
                PolyTree result = new PolyTree();
                clipper.Execute(ClipperLib.ClipType.ctDifference, result);
                //hole是可行走区域,none hole是整个地图区域
                noneHoles = GetPolygonsFromPolyTree(result, PolygonType.NoneHole);
                holes = GetPolygonsFromPolyTree(result, PolygonType.Hole);
            }
            else
            {
                noneHoles = new List<List<Vector3>>();
                noneHoles.Add(ConvertIntPointToVector3List(boundsPath));
                holes = new List<List<Vector3>>();
            }
        }
        #endregion

        static List<Vector3> ConvertIntPointToVector3List(List<IntPoint> list)
        {
            List<Vector3> ret = new List<Vector3>(list.Count);
            for (int i = 0; i < list.Count; ++i)
            {
                ret.Add(new Vector3((float)Utils.DownScale(list[i].X), 0, (float)Utils.DownScale(list[i].Y)));
            }
            return ret;
        }

        static List<List<Vector3>> ConvertIntPointListListToVector3ListList(List<List<IntPoint>> list)
        {
            List<List<Vector3>> ret = new List<List<Vector3>>(list.Count);
            for (int i = 0; i < list.Count; ++i)
            {
                int n = list[i].Count;
                var innerList = new List<Vector3>(n);
                for (int j = 0; j < n; ++j)
                {
                    innerList.Add(new Vector3((float)Utils.DownScale(list[i][j].X), 0, (float)Utils.DownScale(list[i][j].Y)));
                }
                ret.Add(innerList);
            }
            return ret;
        }

        static List<List<IntPoint>> ConvertVector3ListListToIntPointListList(List<List<Vector3>> list)
        {
            List<List<IntPoint>> ret = new List<List<IntPoint>>(list.Count);
            for (int i = 0; i < list.Count; ++i)
            {
                int n = list[i].Count;
                var innerList = new List<IntPoint>(n);
                for (int j = 0; j < n; ++j)
                {
                    innerList.Add(new IntPoint(Utils.UpScale(list[i][j].x), (float)Utils.UpScale(list[i][j].z)));
                }
                ret.Add(innerList);
            }
            return ret;
        }

        static List<IntPoint> ConvertPolygon(List<Vector3> polygon, Vector3 offset, Transform transform = null, float scaleFactor = 1.0f)
        {
            List<IntPoint> p = new List<IntPoint>();
            if (transform != null)
            {
                for (int i = 0; i < polygon.Count; ++i)
                {
                    var worldPos = (transform.localToWorldMatrix.MultiplyPoint(polygon[i]) + offset) * scaleFactor;
                    p.Add(new IntPoint(Utils.UpScale(worldPos.x), Utils.UpScale(worldPos.z)));
                }
            }
            else
            {
                for (int i = 0; i < polygon.Count; ++i)
                {
                    p.Add(new IntPoint(Utils.UpScale(polygon[i].x), Utils.UpScale(polygon[i].z)));
                }
            }

            return p;
        }

        static List<IntPoint> ConvertLine(Vector3 start, Vector3 end)
        {
            List<IntPoint> p = new List<IntPoint>();
            p.Add(new IntPoint(Utils.UpScale(start.x), Utils.UpScale(start.z)));
            p.Add(new IntPoint(Utils.UpScale(end.x), Utils.UpScale(end.z)));
            return p;
        }

        static List<IntPoint> CreateObstaclePath(PrefabOutlineType type, IObstacle obstacleData, float scaleFactor)
        {
            Transform transform = null;
            if (obstacleData.gameObject != null)
            {
                transform = obstacleData.gameObject.transform;
            }
            var outlineVertices = obstacleData.GetOutlineVertices(type);
            
            List<IntPoint> p = ConvertPolygon(outlineVertices, obstacleData.offset, transform, scaleFactor);
            return p;
        }

        static List<IntPoint> CreateBoundsPath(float minX, float minZ, float maxX, float maxZ)
        {
            var list = new List<IntPoint>() {
            new IntPoint(Utils.UpScale(minX), Utils.UpScale(minZ)),
            new IntPoint(Utils.UpScale(maxX), Utils.UpScale(minZ)),
            new IntPoint(Utils.UpScale(maxX), Utils.UpScale(maxZ)),
            new IntPoint(Utils.UpScale(minX), Utils.UpScale(maxZ)),
            };
            return list;
        }

        //see http://www.angusj.com/delphi/clipper/documentation/Docs/Units/ClipperLib/Classes/PolyTree/_Body.htm
        static List<List<Vector3>> GetPolygonsFromPolyTree(PolyTree tree, PolygonType type)
        {
            List<List<Vector3>> polygons = new List<List<Vector3>>();

            PolyNode polynode = tree.GetFirst();
            while (polynode != null)
            {
                if ((type == PolygonType.Hole || type == PolygonType.All) && polynode.IsHole)
                {
                    polygons.Add(ConvertIntPointToVector3List(polynode.Contour));
                }
                else if ((type == PolygonType.All || type == PolygonType.NoneHole) && polynode.IsHole == false)
                {
                    polygons.Add(ConvertIntPointToVector3List(polynode.Contour));
                }
                polynode = polynode.GetNext();
            }

            return polygons;
        }

        static List<IntPoint> CreateTrianglePath(Vector3Int triangle, Vector3[] triangleVertices)
        {
            var list = new List<IntPoint>()
            {
                new IntPoint(Utils.UpScale(triangleVertices[triangle.x].x), Utils.UpScale(triangleVertices[triangle.x].z)),
                new IntPoint(Utils.UpScale(triangleVertices[triangle.y].x), Utils.UpScale(triangleVertices[triangle.y].z)),
                new IntPoint(Utils.UpScale(triangleVertices[triangle.z].x), Utils.UpScale(triangleVertices[triangle.z].z)),
            };
            return list;
        }
    }
}


//#endif