﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace TFW.Map
{
    [CustomEditor(typeof(VaryingTileSizeTerrainLayerLogic))]
    public partial class VaryingTileSizeTerrainLayerUI : Editor
    {
        void OnEnable()
        {
            mLogic = target as VaryingTileSizeTerrainLayerLogic;
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            prefabManager.selectPrefabEvent += OnSelectPrefab;
            prefabManager.customEditFunc = EditTerrainPrefab;

            mPrefab = prefabManager.selectedPrefab;

            mLogic.UpdateGizmoVisibilityState();

            mIndicator = new TileIndicator(true);
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                prefabManager.selectPrefabEvent -= OnSelectPrefab;
                prefabManager.customEditFunc = null;

                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
                HideIndicator();

                mIndicator.OnDestroy();
                mIndicator = null;
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;
            if (mLogic.operationType == VaryingTileSizeTerrainOperationType.Create || mLogic.operationType == VaryingTileSizeTerrainOperationType.Remove)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
                {
                    mLeftButtonDown = true;
                }

                if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
                {
                    mLeftButtonDown = false;
                    mLastCoord = new Vector2Int(-1, -1);
                }

                if (currentEvent.type == EventType.KeyDown)
                {
                    float offset = 50;
                    if (currentEvent.keyCode == KeyCode.UpArrow)
                    {
                        MoveViewport(new Vector3(0, 0, offset));
                    }
                    else if (currentEvent.keyCode == KeyCode.DownArrow)
                    {
                        MoveViewport(new Vector3(0, 0, -offset));
                    }
                    else if (currentEvent.keyCode == KeyCode.LeftArrow)
                    {
                        MoveViewport(new Vector3(-offset, 0, 0));
                    }
                    else if (currentEvent.keyCode == KeyCode.RightArrow)
                    {
                        MoveViewport(new Vector3(offset, 0, 0));
                    }
                }

                var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
                var curCoord = PickTile(screenPos);

                UpdateIndicatorState();

                if (mLeftButtonDown)
                {
                    if (curCoord != mLastCoord)
                    {
                        mLastCoord = curCoord;
                        SetOneTile(mLogic.operationType == VaryingTileSizeTerrainOperationType.Remove);
                    }
                }

                HandleUtility.AddDefaultControl(0);
            }
            else
            {
                HideIndicator();
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                DrawResizeOption();

                if (GUILayout.Button(new GUIContent("Clear Tiles", "清除所有tile")))
                {
                    mLogic.layer.ClearAllTiles();
                }
                if (GUILayout.Button("Show Valid Tile Count"))
                {
                    int validTileCount = mLogic.layer.GetValidTileCount();
                    EditorUtility.DisplayDialog("Info", $"Valid Tile Count: {validTileCount}", "OK");
                }

                if (GUILayout.Button(new GUIContent("Create Full Map Ground LOD0 Prefab", "将lod0的所有地块合成一个大的prefab,可以在制作装饰物prefab的时候作为地表参考")))
                {
                    var folderName = EditorUtility.SaveFolderPanel("Select output folder", "", "");
                    if (!string.IsNullOrEmpty(folderName))
                    {
                        CreateLOD0Prefab(folderName, true);
                    }
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Load Tile Config", "加载一张H X W的tsv表,H和W分别是地图的高和宽,以格子数为单位")))
                {
                    string filePath = EditorUtility.OpenFilePanel("Select Config File", "", "txt,csv");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        var editorMapData = Map.currentMap.data as EditorMapData;
                        var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                        LoadTileConfig(filePath, prefabManager.selectedGroup);
                    }
                }
                if (GUILayout.Button(new GUIContent("Save Tile Config", "导出当前prefab group中的tile和地表使用的tile之间对应关系的tsv")))
                {
                    string filePath = EditorUtility.SaveFilePanel("Select Config File", "", "ground_tile_config", "txt");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        var editorMapData = Map.currentMap.data as EditorMapData;
                        var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                        SaveTileConfig(filePath, prefabManager.selectedGroup);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                
                //for (int i = 0; i < 4; ++i)
                //{
                //    mIndicator.ShowPart(i, true);
                //}
                    
                var layerData = mLogic.layerData;

                layerData.useGeneratedLOD = EditorGUILayout.ToggleLeft(new GUIContent("Use Generated LOD", "使用程序烘培生成的lod"), layerData.useGeneratedLOD);
                EditorGUILayout.EndHorizontal();

                if (mLogic.operationType == VaryingTileSizeTerrainOperationType.Create)
                {
                    var operationType = (VaryingTileSizeTerrainOperationType)EditorGUILayout.Popup(new GUIContent("Operation", "当前操作类型"), (int)mLogic.operationType, mOperationTexts);
                    if (operationType != mLogic.operationType)
                    {
                        SetOperation(operationType);
                    }
                    
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
                    prefabManager.Draw(PrefabGroupDisplayFlag.ShowAddFolderWithLODConstrain | PrefabGroupDisplayFlag.CanRemovePrefab);
                }
                else
                {
                    mLogic.operationType = (VaryingTileSizeTerrainOperationType)EditorGUILayout.Popup(new GUIContent("Operation", "当前操作类型"), (int)mLogic.operationType, mOperationTexts);
                }

                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, LODDisplayFlag.SpecialBufferSetting | LODDisplayFlag.TerrainLODTile, null, null);

                EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Right Up Down' to move viewport.\n 'Z' 'X' 'A' 'S' to set brush mask.\n 箭头上下左右键可以移动viewport.\n.");

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Tile Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Tile Height", layerData.tileHeight.ToString());
                EditorGUILayout.LabelField("X Tile Count", layerData.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Z Tile Count", layerData.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void MoveViewport(Vector3 offset)
        {
            SLGMakerEditor.instance.viewportControl.transform.position += offset;
        }

        //创建这个tile的多边形
        List<Vector3> CreateTilePolygon(int x, int y)
        {
            float minX = mLogic.layerData.tileWidth * x;
            float maxX = mLogic.layerData.tileWidth * (x + 1);
            float minZ = mLogic.layerData.tileHeight * y;
            float maxZ = mLogic.layerData.tileHeight * (y + 1);

            var polygon = new List<Vector3>()
            {
            new Vector3(minX, 0, minZ),
            new Vector3(maxX, 0, minZ),
            new Vector3(maxX, 0, maxZ),
            new Vector3(minX, 0, maxZ),
            };

            return polygon;
        }

        List<Vector2Int> GetValidTiles()
        {
            List<Vector2Int> tiles = new List<Vector2Int>();

            int rows = mLogic.layerData.verticalTileCount;
            int cols = mLogic.layerData.horizontalTileCount;

            bool isCircleMap = Map.currentMap.data.isCircleMap;
            var center = new Vector3(Map.currentMap.mapWidth * 0.5f, 0, Map.currentMap.mapHeight * 0.5f);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (isCircleMap)
                    {
                        var tilePolygon = CreateTilePolygon(j, i);

                        if (!Utils.IsConvexHullFullOutsideOfCircle(tilePolygon, center, Map.currentMap.mapWidth * 0.5f))
                        {
                            tiles.Add(new Vector2Int(j, i));
                        }
                    }
                    else
                    {
                        tiles.Add(new Vector2Int(j, i));
                    }
                }
            }

            return tiles;
        }

        bool CheckViewport(VaryingTileSizeTerrainLayer layer)
        {
            var viewport = Map.currentMap.viewport;
            if (viewport.width >= 1000 && viewport.height >= 1000 && layer.horizontalTileCount > 100 && layer.verticalTileCount > 100)
            {
                if (!EditorUtility.DisplayDialog("Warning", "Viewport is too large, this operation may cost long time! continue?", "Yes", "No"))
                {
                    return false;
                }
            }
            return true;
        }

        void ShowIndicator()
        {
            mIndicator.SetActive(true);
            mIndicator.SetColor(new Color(0, 1, 0, 0.5f));
        }

        void UpdateIndicatorState()
        {
            var indicatorPos = mLogic.layerData.FromCoordinateToWorldPosition(mPickedTile.x, mPickedTile.y);
            UpdateIndicator(indicatorPos);
        }

        void UpdateIndicator(Vector3 pos)
        {
            if (mLogic.operationType == VaryingTileSizeTerrainOperationType.Create || mLogic.operationType == VaryingTileSizeTerrainOperationType.Remove)
            {
                ShowIndicator();   
            }
            else
            {
                HideIndicator();
            }

            pos.y += 0.5f;
            mIndicator.SetPosition(pos);

            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            float scale = 1.0f;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                if (group.selectedIndex >= 0)
                {
                    scale = group.GetItem(group.selectedIndex).size.x;
                }
            }
            mIndicator.SetScale(mLogic.layerData.tileWidth * scale);
        }

        void HideIndicator()
        {
            mIndicator.SetActive(false);
        }

        void SetPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        Vector2Int PickTile(Vector2 screenPos)
        {
            var layerData = mLogic.layerData;
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);
            var curCoord = new Vector2Int(coord.x, coord.y);
            mPickedTile = curCoord;
            return curCoord;
        }

        void SetOperation(VaryingTileSizeTerrainOperationType operationType)
        {
            mLogic.operationType = operationType;
        }

        void DrawResizeOption()
        {
            if (MapModule.enableResizeMap)
            {
                if (GUILayout.Button(new GUIContent("Move And Resize Layer", "修改layer大小")))
                {
                    var dlg = EditorUtils.CreateInputDialog("Move And Resize Layer");

                    var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Size", "", "8100"),
                    new InputDialog.EnumItem("Alignment", "", ResizeAlignment.Move),
                };
                    dlg.Show(items, OnClickMoveAndResizeLayer);
                }
            }
        }

        bool OnClickMoveAndResizeLayer(List<InputDialog.Item> parameters)
        {
            string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
            System.Enum alignment = (parameters[1] as InputDialog.EnumItem).value;
            int newSize;
            Utils.ParseInt(newSizeStr, out newSize);
            if (newSize > 0)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as VaryingTileSizeTerrainLayer;
                layer.MoveAndResize(newSize, newSize, (ResizeAlignment)alignment);
                mLogic.RecreateGrid();
                return true;
            }

            return false;
        }

        void CreateLOD0Prefab(string outputFolder, bool middleAlignment)
        {
            outputFolder = Utils.ConvertToUnityAssetsPath(outputFolder);
            if (!string.IsNullOrEmpty(outputFolder))
            {
                var layerData = mLogic.layerData;
                int h = layerData.horizontalTileCount;
                int v = layerData.verticalTileCount;
                GameObject rootObj = new GameObject("ground_lod0");
                Vector3 alignmentOffset = Vector3.zero;
                if (middleAlignment)
                {
                    alignmentOffset = new Vector3(Map.currentMap.mapWidth, 0, Map.currentMap.mapHeight) * 0.5f;
                }
                for (int i = 0; i < v; ++i)
                {
                    for (int j = 0; j < h; ++j)
                    {
                        var tileData = layerData.GetTile(j, i);
                        if (tileData != null)
                        {
                            var modelTemplate = tileData.GetModelTemplate();
                            if (tileData.bigTile != null)
                            {
                                if (i == tileData.bigTile.y && j == tileData.bigTile.x)
                                {
                                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(modelTemplate.GetLODPrefabPath(0));
                                    var obj = GameObject.Instantiate<GameObject>(prefab);
                                    obj.transform.SetParent(rootObj.transform);
                                    obj.transform.position = mLogic.layer.FromCoordinateToWorldPosition(j, i) - alignmentOffset;
                                }
                            }
                            else
                            {
                                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(modelTemplate.GetLODPrefabPath(0));
                                var obj = GameObject.Instantiate<GameObject>(prefab);
                                obj.transform.SetParent(rootObj.transform);
                                obj.transform.position = tileData.GetPosition() - alignmentOffset;
                            }
                        }
                    }
                }

                string prefabPath = outputFolder + "/" + rootObj.name + ".prefab";
                PrefabUtility.SaveAsPrefabAsset(rootObj, prefabPath);

                GameObject.DestroyImmediate(rootObj);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", $"Invalid folder {outputFolder}", "OK");
            }
        }

        void SaveTileConfig(string filePath, PrefabGroup group)
        {
            if (group == null)
            {
                return;
            }
            var layerData = mLogic.layerData;
            int width = layerData.horizontalTileCount;
            int height = layerData.verticalTileCount;

            int[] indices = new int[width * height];
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    var tile = layerData.GetTile(j, i);
                    int idx = 0;
                    if (tile != null)
                    {
                        idx = group.GetPrefabIndex(tile.GetAssetPath(0));
                    }
                    idx = Mathf.Max(0, idx);

                    if (tile != null && tile.bigTile != null)
                    {
                        //只保留左下角的tile
                        if (i != tile.bigTile.y || j != tile.bigTile.x)
                        {
                            idx = 0;
                        }
                    }

                    //上下颠倒,更符合配置表的视角
                    var tileIdx = (height - i - 1) * width + j;
                    indices[tileIdx] = idx;
                }
            }

            StringBuilder builder = new StringBuilder();
            int k = 0;
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    builder.Append(indices[k]);
                    builder.Append(",");
                    ++k;
                }
                builder.Append("\r\n");
            }

            File.WriteAllText(filePath, builder.ToString());
        }

        void LoadTileConfig(string filePath, PrefabGroup group)
        {
            if (group == null)
            {
                return;
            }

            var text = File.ReadAllText(filePath);
            if (!string.IsNullOrEmpty(text))
            {
                text = text.Replace("\r\n", ",");
                var tokens = text.Split(new string[] { "," }, System.StringSplitOptions.RemoveEmptyEntries);
                int width = mLogic.layerData.horizontalTileCount;
                int height = mLogic.layerData.verticalTileCount;
                int n = width * height;
                if (tokens.Length != n)
                {
                    EditorUtility.DisplayDialog("Error", "Invalid config file, size not match!", "OK");
                    return;
                }

                var layer = mLogic.layer;
                bool error = false;

                layer.ClearAllTiles();
                for (int i = 0; i < height; ++i)
                {
                    for (int j = 0; j < width; ++j)
                    {
                        //上下颠倒,更符合配置表的视角
                        var idx = (height - i - 1) * width + j;
                        int prefabIndexInGroup;
                        bool suc = int.TryParse(tokens[idx], out prefabIndexInGroup);
                        if (suc)
                        {
                            if (prefabIndexInGroup >= 0 && prefabIndexInGroup < group.count)
                            {
                                string prefabPath = group.GetPrefabPath(prefabIndexInGroup);
                                if (!string.IsNullOrEmpty(prefabPath))
                                {
                                    if (prefabIndexInGroup > 0)
                                    {
                                        var item = group.GetItem(prefabIndexInGroup);
                                        layer.SetTile(j, i, prefabIndexInGroup, group.groupID, item.id);
                                    }
                                }   
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("Error", string.Format("invalid index [{0}] is not found", prefabIndexInGroup), "OK");
                                error = true;
                                break;
                            }
                        }
                    }

                    if (error)
                    {
                        break;
                    }
                }
            }   
        }

        void EditTerrainPrefab(PrefabGroup.Item item, int groupID, int index)
        {
            var dlg = EditorUtils.CreateInputDialog("Set Tile Size");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Width", "", item.size.x.ToString()),
                    new InputDialog.StringItem("Height", "", item.size.y.ToString()),
                };
            dlg.Show(items, (List<InputDialog.Item> parameters) => {
                string widthStr = (parameters[0] as InputDialog.StringItem).text;
                string heightStr = (parameters[1] as InputDialog.StringItem).text;
                bool ok = int.TryParse(widthStr, out int width);
                if (!ok) {
                    return false;
                }
                ok = int.TryParse(heightStr, out int height);
                if (!ok)
                {
                    return false;
                }

                return SetPrefabSize(item, groupID, index, width, height);
            });
        }

        bool SetPrefabSize(PrefabGroup.Item item, int groupID, int index, int width, int height)
        {
            width = Mathf.Max(1, width);
            height = Mathf.Max(1, height);
            var newSize = new Vector2Int(width, height);
            if (newSize != item.size)
            {
                if (EditorUtility.DisplayDialog("警告", "改变tile大小后地图中使用了该tile的地块会被清除,继续吗?", "继续", "取消"))
                {
                    item.size = newSize;
                    var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
                    terrainPrefabManager.SetGroupPrefabSizeByID(groupID, index, new Vector2Int(width, height));
                    //清理使用了这个prefab的地表tile
                    var layer = mLogic.layer;
                    int h = layer.horizontalTileCount;
                    int v = layer.verticalTileCount;
                    for (int i = 0; i < v; ++i)
                    {
                        for (int j = 0; j < h; ++j)
                        {
                            var tile = layer.GetTile(j, i);
                            if (tile != null)
                            {
                                if (tile.type == groupID && tile.tileID == item.id)
                                {
                                    layer.SetTile(j, i, 0, 0, 0);
                                }
                            }
                        }
                    }
                    return true;
                }
            }
            return false;
        }

        GameObject mPrefab;
        TileIndicator mIndicator;
        bool mLeftButtonDown = false;
        Vector2Int mLastCoord = new Vector2Int(-100, -100);
        Vector2Int mPickedTile;
        VaryingTileSizeTerrainLayerLogic mLogic;
        string[] mOperationTexts = new string[]
        {
            "Create",
            "Remove",
            "Select",
        };
    }
}

#endif