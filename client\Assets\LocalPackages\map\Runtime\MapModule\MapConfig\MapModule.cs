﻿ 



 
 


using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public static class MapModule
    {
        public static UniTask Init(MapConfig config)
        {
            InitEditor(config);
            return MapPlugin.LoadPluginList(config.pluginList);
        }

        public static void InitLoad(MapConfig config)
        {
            InitEditor(config);
            MapPlugin.LoadPluginListEditor(config.pluginList);
        }

        public static void InitEditor(MapConfig config)
        {
            mapConfig = config;
            MapModuleResourceMgr.SetResourceManagerImpl(config.resourceManagerImpl);
            MapTouchManager.SetTouchManagerImpl(config.touchManagerImpl);
            runtimeMapResDirectory = config.runtimeMapResDirectory;
            defaultNavMeshCreateMode = config.defaultNavmeshCreationMode;
            useOnly15Prefab = config.useOnly15Prefab;
            defaultFrontTileSize = config.defaultFrontTileSize;
            defaultGroundTileSize = config.defaultGroundTileSize;
            defaultNPCRegionSize = config.defaultNPCRegionSize;
            configResDirectory = config.configResDir;
            intuitiveZoomConfigFilePath = config.intuitiveZoomConfigFilePath;
            usePrefabOutline = config.usePrefabOutline;
            displayRailwayLayer = config.displayRailwayLayer;
            displayCircleBorderLayer = config.displayCircleBorderLayer;
            cameraClampBorder = config.cameraClampBorder;
            cameraCheckCircleBorderCollision = config.cameraCheckCircleBorderCollision;
            showHorde = config.showHorde;
            obstacleGridSize = config.obstacleGridSize;
            defaultGlobalObstacleCreateMode = config.defaultGlobalObstacleCreationMode;
            showDemoFunction = config.showDemoFunction;
            cameraSettings = config.cameraSettingFilePaths;
            cameraBorderColliderRadius = config.cameraBorderColliderRadius;
            enableCameraCollision = config.enableCameraCollision;
            defaultGroundBakingShader = config.defaultGroundBakingShader;
            defaultGroundBakingShaderTexturePropertyName = config.defaultGroundBakingShaderTexturePropertyName;
            defaultObstacleMaterial = config.defaultObstacleMaterial;
            brushFolder = config.brushFolderPath;
            useNewCameraHeightAutoUpdateAlgorithm = config.useNewCameraHeightAutoUpdateAlgorithm;
            useFakePrefab = config.useFakePrefab;
            intuitiveConfigUnitName = config.intuitiveConfigUnitName;
            groundTileMakerDefaultRuntimeAssetFolder = config.groundTileMakerDefaultRuntimeAssetFolder;
            groundTileMakerDefaultEditorAssetFolder = config.groundTileMakerDefaultEditorAssetFolder;
            frameActionMaxExecuteTime = config.frameActionMaxExecuteTime;
            doNotExportFrontLayerModelTemplates = config.doNotExportFrontLayerModelTemplates;
            mapListDir = config.mapListDir;
            defaultSplitFogTileSize = config.defaultSplitFogTileSize;
            bakedAnimDataPath = config.bakedAnimDataPath;
            groundTileAtlasInfoDir = config.groundTileAtlasInfoDir;
            defaultTerritoryMaterialPath = config.defaultTerritoryMaterialPath;
            projectName = config.projectName;
            enableUpdateFarClipPlane = config.enableUpdateFarClipPlane;
            enableUpdateNearClipPlane = config.enableUpdateNearClipPlane;
            is2DGame = config.is2DGame;
            groundTileMakerDefaultMaskTextureSize = config.groundTileMakerDefaultMaskTextureSize;
            groundTileMakerDefaultTileSize = config.groundTileMakerDefaultTileSize;
            groundTileMakerDefaultMaskTextureName = config.groundTileMakerDefaultMaskTextureName;
            groundTileMakerDefaultInitChannelData = config.groundTileMakerDefaultInitChannelData;
            groundTileMakerDefaultNormalizeColor = config.groundTileMakerDefaultNormalizeColor;
            groundTileMakerDefaultMaterialPath = config.groundTileMakerDefaultMaterialPath;
            useMapLargeTileForDecorationLayer = config.useMapLargeTileForDecorationLayer;
            wrongGroundLODOrientation = config.wrongGroundLODOrientation;
            preloadGroundLayerTile = config.preloadGroundLayerTile;
            pluginListFilePath = config.pluginList;
            useNewConfigFormat = config.useNewConfigFormat;
            hideWallWhenMainBuildingAtFinalPosition = config.hideWallWhenMainBuildingAtFinalPosition;
            generateRiverMaterial = config.generateRiverMaterial;
            rotatedUVMeshPath = config.rotatedUVMeshPath;
            supportGroundHeightChange = config.supportGroundHeightChange;
            useCameraShakeByDistance = config.useCameraShakeByDistance;
            enableResizeMap = config.enableResizeMap;
            mapGlobalSetting = config.mapGlobalSetting;

            BakedAnimationDataManager.Init(config.bakedAnimDataPath);
        }

        public static string runtimeMapResDirectory { get; private set; }
        public static string configResDirectory { get; private set; }
        public static string intuitiveZoomConfigFilePath { get; private set; }
        public static NavigationCreateMode defaultNavMeshCreateMode { get; private set; }
        public static NavigationCreateMode defaultGlobalObstacleCreateMode { get; private set; }
        public static bool useOnly15Prefab { get; private set; }
        public static float defaultGroundTileSize { get; private set; }
        public static float defaultNPCRegionSize { get; private set; }
        public static float defaultFrontTileSize { get; private set; }
        public static float defaultSplitFogTileSize { get; private set; }
        public static bool usePrefabOutline { get; set; }
        public static bool displayRailwayLayer { get; private set; }
        public static bool displayCircleBorderLayer { get; private set; }
        public static bool cameraClampBorder { get; private set; }
        public static bool cameraCheckCircleBorderCollision { get; private set; }
        public static float cameraBorderColliderRadius { get; private set; }
        public static bool showHorde { get; private set; }
        public static float obstacleGridSize { get; private set; }
        public static bool showDemoFunction { get; private set; }
        public static List<string> cameraSettings { get; private set; }
        public static bool enableCameraCollision { get; private set; }
        public static string defaultGroundBakingShader { get; private set; }
        public static string defaultGroundBakingShaderTexturePropertyName { get; private set; }
        public static string defaultObstacleMaterial { get; private set; }
        public static string brushFolder { get; private set; }
        public static bool useNewCameraHeightAutoUpdateAlgorithm { get; private set; }
        public static bool useFakePrefab { get; private set; }
        public static string intuitiveConfigUnitName { get; private set; }
        public static string groundTileMakerDefaultRuntimeAssetFolder { get; private set; }
        public static string groundTileMakerDefaultEditorAssetFolder { get; private set; }
        public static int groundTileMakerDefaultMaskTextureSize { get; private set; }
        public static int groundTileMakerDefaultTileSize { get; private set; }
        public static string groundTileMakerDefaultMaskTextureName { get; private set; }
        public static bool groundTileMakerDefaultInitChannelData { get; private set; }
        public static bool groundTileMakerDefaultNormalizeColor { get; private set; }
        public static string groundTileMakerDefaultMaterialPath { get; private set; }
        public static long frameActionMaxExecuteTime { get; private set; }
        public static bool doNotExportFrontLayerModelTemplates { get; private set; }
        public static string mapListDir { get; private set; }
        public static string bakedAnimDataPath { get; private set; }
        public static string groundTileAtlasInfoDir { get; private set; }
        public static string defaultTerritoryMaterialPath { get; private set; }
		public static string projectName { get; private set; }
        public static bool enableUpdateFarClipPlane { get; private set; }
        public static bool enableUpdateNearClipPlane { get; private set; }
        public static bool is2DGame { get; private set; }
        public static bool useMapLargeTileForDecorationLayer { get; private set; }
        public static bool wrongGroundLODOrientation { get; private set; }
        public static bool preloadGroundLayerTile { get; private set; }
        public static string pluginListFilePath { get; private set; }
        public static bool useNewConfigFormat { get; private set; }
        public static bool hideWallWhenMainBuildingAtFinalPosition { get; private set; }
        public static bool generateRiverMaterial { get; private set; }
        public static string rotatedUVMeshPath { get; private set; }
        public static bool supportGroundHeightChange { get; private set; }
        public static bool useCameraShakeByDistance { get; private set; }
        public static bool enableResizeMap { get; private set; }
        public static MapConfig mapConfig { get; private set; }
        public static MapGlobalSetting mapGlobalSetting { get;private set; }
    }
}
