﻿ 



 
 



#if UNITY_EDITOR

using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionSetTerrainTileResolution : EditorAction
    {
        public ActionSetTerrainTileResolution(int layerID, int tileX, int tileY, int newResolution)
        {
            mLayerID = layerID;
            var layer = Map.currentMap.GetMapLayerByID(layerID) as BlendTerrainLayer;
            var tile = layer.GetTile(tileX, tileY);
            mOldHeights = new List<float>();
            if (tile.heights != null)
            {
                mOldHeights.AddRange(tile.heights);
            }
            mTileX = tileX;
            mTileY = tileY;
            mOldResolution = tile.resolution;
            mNewResolution = newResolution;
            mDescription = string.Format("change terrain tile resolution");
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                layer.SetTileResolution(mTileX, mTileY, mNewResolution, false);
                return true;
            }
            return false;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                layer.SetTileResolution(mTileX, mTileY, mOldResolution, false);
                layer.SetHeights(mTileX, mTileY, 0, 0, mOldResolution, mOldResolution, mOldResolution, mOldHeights, true, false);
                return true;
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        List<float> mOldHeights;
        int mOldResolution;
        int mNewResolution;
        int mTileX;
        int mTileY;
        int mLayerID;
        string mDescription;
    }
}

#endif