﻿ 



 
 


using System.Collections.Generic;
using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    // 保存多边形的矩阵网格
    public class SearchGrid
    {
        // 网格大小: 60米
        const int GRID_SIZE = 60000;

        int mMinX;
        int mMinZ;
        int mWidth;
        int mHeight;
        int mGridSize;
        int mMaxGridX;
        int mMaxGridZ;

        //每个格子中保存了相交的多边形列表,按多边形的包围框计算
        List<Triangle>[][] mGrids;

        // 创建Grid
        public SearchGrid(int minX, int minZ, int width, int height, int gridSize)
        {
            if (gridSize == 0)
            {
                gridSize = GRID_SIZE;
            }

            mMinX = minX;
            mMinZ = minZ;
            mWidth = width;
            mHeight = height;
            mGridSize = gridSize;

            mMaxGridX = UnityEngine.Mathf.RoundToInt(width / gridSize) + 1;
            mMaxGridZ = UnityEngine.Mathf.RoundToInt(height / gridSize) + 1;
            mGrids = new List<Triangle>[mMaxGridX][];
            for (int i = 0; i < mMaxGridX; i++)
            {
                mGrids[i] = new List<Triangle>[mMaxGridZ];
                for (int k = 0; k < mMaxGridZ; ++k)
                {
                    mGrids[i][k] = new List<Triangle>();
                }
            }
        }

        // 新建矩阵
        public void NewGrids()
        {
            mGrids = new List<Triangle>[mMaxGridX][];
            for (int i = 0; i < mMaxGridX; i++)
            {
                mGrids[i] = new List<Triangle>[mMaxGridZ];
                for (int k = 0; k < mMaxGridZ; ++k)
                {
                    mGrids[i][k] = new List<Triangle>();
                }
            }
        }

        // 插入三角形，三角形不能超出SearchGrid范围
        public void Insert(Triangle polygon)
        {
            int minX, minZ, maxX, maxZ;
            polygon.ToRect(out minX, out minZ, out maxX, out maxZ);

            int minGX, minGZ, maxGX, maxGZ;
            Rect2Grid(minX - mMinX, minZ - mMinZ, maxX - mMinX, maxZ - mMinZ, false, out minGX, out minGZ, out maxGX, out maxGZ);

            for (int x = minGX; x <= maxGX; x++)
            {
                for (int z = minGZ; z <= maxGZ; z++)
                {
                    mGrids[x][z].Add(polygon);
                }
            }
        }

        // 获取可能和Coord碰撞的多边形,兼容三角形
        public List<Triangle> RetrieveByCoord(Coord coord)
        {
            var gridX = (coord.X - mMinX) / mGridSize;
            var gridZ = (coord.Z - mMinZ) / mGridSize;
            List<Triangle> ret = new List<Triangle>();
            // 取错误的点打印日志否则会导致panic
            if (gridX >= mMaxGridX || gridZ >= mMaxGridZ || gridX < 0 || gridZ < 0)
            {
#if UNITY_EDITOR
                UnityEngine.Debug.LogError($"The coord is in outside of map or has nagative value: {coord.X},{coord.Z}");
#endif
                return ret;
            }
            var polygons = mGrids[gridX][gridZ];
            for (int i = 0; i < polygons.Count; ++i)
            {
                if (polygons[i].IsCoordInside(coord))
                {
                    ret.Add(polygons[i]);
                }
            }

            return ret;
        }

        // 将Rect映射到SearchGrid中的Grid，返回的Grid坐标不会越界
        void Rect2Grid(int minX, int minZ, int maxX, int maxZ, bool allowExceed, out int minGX, out int minGZ, out int maxGX, out int maxGZ)
        {
            minGX = minX / mGridSize;
            minGZ = minZ / mGridSize;
            maxGX = maxX / mGridSize;
            maxGZ = maxZ / mGridSize;
            if (!allowExceed)
            { // 不允许越界，越界则panic
                CheckGrid(minGX, minGZ, maxGX, maxGZ);
            }
            else
            { // 允许越界，则处理越界
                minGX = System.Math.Max(0, minGX);
                minGZ = System.Math.Max(0, minGZ);
                maxGX = System.Math.Min(mMaxGridX, maxGX);
                maxGZ = System.Math.Min(mMaxGridZ, maxGZ);
            }
        }

        void CheckGrid(int minGX, int minGZ, int maxGX, int maxGZ)
        {
            if (minGX < 0 || minGZ < 0 ||
                maxGX >= mMaxGridX || maxGZ >= mMaxGridZ)
            {
                UnityEngine.Debug.Assert(false, string.Format("invalid grid: {0} {1} {2} {3}",
                    minGX, minGZ, maxGX, maxGZ));
            }
        }
    }
}
