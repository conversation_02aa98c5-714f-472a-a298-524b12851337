﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ColorSpriteMeshCache
    {
        struct ColorSpriteCacheItem
        {
            public GridType gridType;
            public float tileWidth;
            public float tileHeight;
            public Mesh mesh;
        }

        public void OnDestroy()
        {
            foreach (var cache in mCaches)
            {
                GameObject.DestroyImmediate(cache.mesh);
            }
            mCaches = null;

            foreach (var mtl in mColorToMaterial)
            {
                GameObject.DestroyImmediate(mtl.Value);
            }
            mColorToMaterial = null;
        }

        public GameObject CreateGameObject(Color32 color, MapLayerData layerData)
        {
            var gameObject = new GameObject();
            var meshFilter = gameObject.AddComponent<MeshFilter>();
            var mesh = CreateMesh(layerData);
            meshFilter.sharedMesh = mesh;
            var renderer = gameObject.AddComponent<MeshRenderer>();
            renderer.sharedMaterial = GetColorMaterial(color);
            Utils.HideGameObject(gameObject);
            return gameObject;
        }

        Mesh CreateMesh(MapLayerData layerData)
        {
            for (int i = 0; i < mCaches.Count; ++i)
            {
                var cache = mCaches[i];
                if (cache.gridType == layerData.gridType && Mathf.Approximately(layerData.tileWidth, cache.tileWidth) &&
                    Mathf.Approximately(layerData.tileHeight, cache.tileHeight))
                {
                    return cache.mesh;
                }
            }

            var item = new ColorSpriteCacheItem();
            item.gridType = layerData.gridType;
            item.tileWidth = layerData.tileWidth;
            item.tileHeight = layerData.tileHeight;
            MapLayerTileMeshCreator meshCreator = EditorUtils.GetMapLayerTileMeshCreator(layerData.gridType);
            meshCreator.SetMapLayer(layerData);
            item.mesh = meshCreator.CreateLayerTileMesh();

            mCaches.Add(item);

            return item.mesh;
        }

        public void ChangeMaterialColor(Color32 oldColor, Color32 newColor)
        {
            if (oldColor.r != newColor.r || oldColor.g != newColor.g || oldColor.b != newColor.b || oldColor.a != newColor.a)
            {
                var mtl = GetColorMaterial(oldColor);
                if (mtl != null)
                {
                    var newColorMtl = GetColorMaterial(newColor);
                    if (newColorMtl != null)
                    {
                        GameObject.DestroyImmediate(newColorMtl);
                        mColorToMaterial.Remove(newColor.ToString());
                    }

                    mColorToMaterial.Remove(oldColor.ToString());
                    mColorToMaterial[newColor.ToString()] = mtl;
                    mtl.color = newColor;
                }
            }
        }

        Material GetColorMaterial(Color32 color)
        {
            Material mtl;
            var key = color.ToString();
            mColorToMaterial.TryGetValue(key, out mtl);
            if (mtl == null)
            {
                mtl = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                mtl.color = color;

                mColorToMaterial.Add(key, mtl);
            }
            return mtl;
        }

        List<ColorSpriteCacheItem> mCaches = new List<ColorSpriteCacheItem>();
        Dictionary<string, Material> mColorToMaterial = new Dictionary<string, Material>();
    }
}

#endif