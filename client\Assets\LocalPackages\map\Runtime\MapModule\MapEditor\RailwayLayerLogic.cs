﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public enum RailwayLayerOperationType
    {
        kCreateObject,
        kSelectObject,
        kRemoveObject,
    }

    [ExecuteInEditMode]
    [Black]
    public class RailwayLayerLogic : MapLayerLogic
    {
        public RailwayLayerData layerData
        {
            get
            {
                var layerData = Map.currentMap.FindObject(layerID) as RailwayLayerData;
                return layerData;
            }
        }

        public GameObject prefab;
        public int currentRailIndex = 0;
        public RailObjectType currentObjectType = RailObjectType.Rail;
        public RailwayLayerOperationType operation = RailwayLayerOperationType.kCreateObject;
    }
}

#endif