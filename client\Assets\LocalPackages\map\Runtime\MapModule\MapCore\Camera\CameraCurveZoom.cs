﻿ 



 
 



using System.Collections;
using UnityEngine;

namespace TFW.Map
{
    //从某个高度缩放到某个高度,使用曲线
    public class CameraCurveZoom : CameraAction
    {
        public CameraCurveZoom(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        public override void OnFinishImpl()
        {
            if (mOnReachTarget != null)
            {
                mOnReachTarget();
            }
        }

        public void StartZooming(float zDistance, float targetHeight, float targetAngle, float duration, System.Action onReachTarget)
        {
            if (enabled == false)
            {
                mOnReachTarget = onReachTarget;
                mTime = 0;
                mStartPos = MapCameraMgr.updatedCameraPosition;
                var cameraRotation = MapCameraMgr.MapCameraRoot.transform.rotation.eulerAngles;
                mEndPos = new Vector3(mStartPos.x, targetHeight, mStartPos.z + zDistance);
                mEndAngle = targetAngle;
                mDuration = duration;
                mAngleCurve = AnimationCurve.EaseInOut(0, cameraRotation.x, mDuration, mEndAngle);
                enabled = true;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            mTime += Time.deltaTime;
            bool finished = false;
            if (mTime >= mDuration)
            {
                finished = true;
                mTime = mDuration;
            }

            float angle = mAngleCurve.Evaluate(mTime);
            var cameraTransform = MapCameraMgr.MapCameraRoot.transform;
            cameraTransform.rotation = Quaternion.Euler(angle, 0, 0);

            Vector3 targetPos = Vector3.Lerp(mStartPos, mEndPos, mTime / mDuration);

            if (finished)
            {
                isFinished = true;
            }

            MapCameraMgr.UpdateRotateCenter();
            return targetPos;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
        }

        Vector3 mEndPos;
        Vector3 mStartPos;
        float mEndAngle;
        float mDuration;
        float mTime = 0;
        AnimationCurve mAngleCurve;
        System.Action mOnReachTarget;
    }
}