﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveLocalObstacles(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.LocalObstacles, writer);

            writer.Write(VersionSetting.LocalObstacleStructVersion);

            //-------------------version 1 start------------------------------
            var obstacleManager = Map.currentMap.data.localObstacleManager;
            var obstacles = obstacleManager.prefabTileDatas;
            var tiles = obstacleManager.tiles;
            int n = 0;
            if (tiles != null)
            {
                n = tiles.Length;
            }

            writer.Write(obstacleManager.regionWidth);
            writer.Write(obstacleManager.regionHeight);
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                mIDExport.Export(writer, tiles[i]);
            }

            int obstacleCount = 0;
            if (obstacles != null)
            {
                obstacleCount = obstacles.Count;
            }
            writer.Write(obstacleCount);
            if (obstacles != null)
            {
                foreach (var p in obstacles)
                {
                    var modelTemplateID = p.Key;
                    var data = p.Value;
                    var vertices = data.vertices;
                    var triangles = data.triangles;

                    //save id
                    mIDExport.Export(writer, modelTemplateID);
                    //save vertices
                    int count = vertices.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        Utils.WriteVector3(writer, vertices[i]);
                    }
                    //save indices
                    count = triangles.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        writer.Write(triangles[i]);
                    }
                }
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
            //-------------------version 1 end------------------------------
        }
    }
}

#endif