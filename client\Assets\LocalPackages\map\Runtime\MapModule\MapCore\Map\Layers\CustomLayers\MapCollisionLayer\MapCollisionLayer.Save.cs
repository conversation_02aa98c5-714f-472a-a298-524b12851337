﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.MapCollisionLayerEditorDataVersion);

            Save<PERSON>ayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write((int)displayType);
            writer.Write(displayVertexRadius);
            writer.Write(maxCollectionDistance);

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveCollisionData(writer, objData as MapCollisionData);
            }

            SaveClosedRegionDetectors(writer);
        }

        void SaveCollisionData(BinaryWriter writer, MapCollisionData data)
        {
            var navMeshObstacleVertices = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            int n = navMeshObstacleVertices.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var pos = navMeshObstacleVertices[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            var objectPlacementObstacleVertices = data.GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
            n = objectPlacementObstacleVertices.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var pos = objectPlacementObstacleVertices[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            var radius = data.displayRadius;
            var extandable = data.IsExtendable();
            writer.Write(radius);
            writer.Write(extandable);
            writer.Write((int)data.attribute);
            writer.Write(data.type);
        }

        void SaveClosedRegionDetectors(BinaryWriter writer)
        {
            var items = regionDetector.items;
            int n = items.Length;
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, items[i].gameObject.transform.position);
                writer.Write(items[i].type);
            }
        }

        //save objects in range
        public void SaveObjectsInRange(string dataPath, float minX, float minZ, float maxX, float maxZ)
        {
            var layerBounds = new Rect(minX, minZ, maxX - minX, maxZ - minZ);

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.MapCollisionLayerEditorDataVersion);

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            long offset = writer.BaseStream.Position;
            writer.Write(n);
            int validObjectCount = 0;
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                var bounds = objData.GetBounds();
                if (layerBounds.Overlaps(bounds))
                {
                    ++validObjectCount;
                    SaveCollisionData(writer, objData as MapCollisionData);
                }
            }
            Utils.WriteAndJump(writer, offset, validObjectCount);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }
    }
}

#endif