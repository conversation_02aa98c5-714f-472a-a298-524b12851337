﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        Rect GetTerritoryBounds(EditorTerritorySubLayer subLayer, int territoryID, Dictionary<int, Rect> boundsCache)
        {
            bool found = boundsCache.TryGetValue(territoryID, out Rect bounds);
            if (!found)
            {
                bounds = subLayer.GetTerritoryWorldBounds(territoryID);
                boundsCache[territoryID] = bounds;
            }
            return bounds;
        }

        bool SaveNewEditorTerritoryLayer(BinaryWriter writer, EditorTerritoryLayer layer)
        {
            BeginSection(MapDataSectionType.NewCityTerritoryLayer, writer);
            //版本号
            writer.Write(VersionSetting.NewCityTerritoryLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            Dictionary<int, Rect> territoryBounds = new Dictionary<int, Rect>();

            int subLayerCount = layer.subLayers.Count;
            writer.Write(subLayerCount);
            //export lod0
            for (int s = 0; s < subLayerCount; ++s)
            {
                var subLayer = layer.subLayers[s];
                var horizontalTileCount = subLayer.horizontalTileCount;
                var verticalTileCount = subLayer.verticalTileCount;
                var tileWidth = subLayer.tileWidth;
                var tileHeight = subLayer.tileHeight;
                writer.Write(verticalTileCount);
                writer.Write(horizontalTileCount);
                writer.Write(tileWidth);
                writer.Write(tileHeight);

                var territories = subLayer.territories;
                writer.Write(territories.Count);

                //export lod1
                List<CurveRegionCreator.Block> blocks = null;
                bool hasBlock = false;
                if (layer.lodCount > 1)
                {
                    hasBlock = true;
                }
                writer.Write(hasBlock);

                if (hasBlock) 
                {
                    var creator = subLayer.regionCreatorsForLODs[1];
                    blocks = creator.blocks;

                    writer.Write(creator.lod1MaskTextureWidth);
                    writer.Write(creator.lod1MaskTextureHeight);
                    Utils.WriteColor32Array(writer, creator.lod1MaskTextureData);

                    int nBlocks = blocks.Count;
                    writer.Write(nBlocks);
                    for (int i = 0; i < nBlocks; ++i)
                    {
                        Utils.WriteString(writer, blocks[i].prefabPath);
                        Utils.WriteRect(writer, Utils.BoundsToRect(blocks[i].bounds));
                    }
                }

                for (int i = 0; i < territories.Count; ++i)
                {
                    writer.Write(territories[i].id);
                    //save buildings
                    var buildings = territories[i].buildings;
                    writer.Write(buildings.Count);
                    for (int b = 0; b < buildings.Count; ++b)
                    {
                        Utils.WriteVector3(writer, buildings[b].gameObject.transform.position);
                    }
                    
                    //asset path for lod0
                    string assetPrefixPath = subLayer.GetTerritoryAssetPath(SLGMakerEditor.instance.exportFolder, s, territories[i].id, 0);
                    Utils.WriteString(writer, assetPrefixPath);

                    //mesh position
                    Rect worldBounds = GetTerritoryBounds(subLayer, territories[i].id, territoryBounds);
                    Utils.WriteVector3(writer, Utils.ToVector3(worldBounds.center));
                    //mesh bounds
                    Utils.WriteRect(writer, worldBounds);

                    Utils.WriteColor(writer, territories[i].GetColor());

                    var mtl = layer.GetCurveRegionMeshGenerationParam(s).lodParams[0].regionMaterial;
                    Utils.WriteString(writer, AssetDatabase.GetAssetPath(mtl));

                    int blockIndex = GetBlockIndex(blocks, territories[i].id);
                    writer.Write(blockIndex);
                }

                //edge info
                var edgeAssetsInfo = subLayer.regionCreatorsForLODs[0].edgeAssetsInfo;
                int n = edgeAssetsInfo.Count;
                writer.Write(n);
                for (int e = 0; e < n; ++e)
                {
                    var worldBounds = GetTerritoryBounds(subLayer, edgeAssetsInfo[e].territoryID, territoryBounds);
                    Utils.WriteVector3(writer, Utils.ToVector3(worldBounds.center));
                    Utils.WriteRect(writer, worldBounds);

                    writer.Write(edgeAssetsInfo[e].territoryID);
                    writer.Write(edgeAssetsInfo[e].neighbourTerritoyID);
                    Utils.WriteString(writer, edgeAssetsInfo[e].prefabPath);

                    Utils.WriteString(writer, AssetDatabase.GetAssetPath(edgeAssetsInfo[e].material));

                    int blockIndex = GetBlockIndex(blocks, edgeAssetsInfo[e].territoryID, edgeAssetsInfo[e].neighbourTerritoyID);
                    writer.Write(blockIndex);
                }

                var grids = subLayer.grids;
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        short territoryIndex = subLayer.GetTerritoryIndex(grids[i, j]);
                        writer.Write(territoryIndex);
                    }
                }
            }

            SaveNewMapLayerLODConfig(writer, layer.lodConfig);

            return true;
        }

        void SaveNewMapLayerLODConfig(BinaryWriter writer, MapLayerLODConfig config)
        {
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
            }
        }

        int GetBlockIndex(List<CurveRegionCreator.Block> blocks, int territoryID)
        {
            if (blocks == null)
            {
                return -1;
            }

            for (int i = 0; i < blocks.Count; ++i)
            {
                foreach (var region in blocks[i].regions)
                {
                    if (region.territoryID == territoryID)
                    {
                        return i;
                    }
                }
            }
            return -1;
        }

        int GetBlockIndex(List<CurveRegionCreator.Block> blocks, int territoryID, int neighbourTerritoryID)
        {
            if (blocks == null)
            {
                return -1;
            }

            for (int i = 0; i < blocks.Count; ++i)
            {
                foreach (var edge in blocks[i].edges)
                {
                    if (edge.territoryID == territoryID && edge.neighbourTerritoryID == neighbourTerritoryID)
                    {
                        return i;
                    }
                }
            }
            return -1;
        }
    }
}

#endif