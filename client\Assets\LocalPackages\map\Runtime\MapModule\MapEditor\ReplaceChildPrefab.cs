﻿ 



 
 

using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;

#endif
using System.Collections.Generic;

namespace TFW.Map
{
    //替换是prefab的子节点
    class ReplaceChildPrefab : MonoBehaviour
    {

        public void Replace()
        {
#if UNITY_EDITOR
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetPrefabStage(gameObject);
            if (stage == null)
            {
                return;
            }
            var assetPath = stage.prefabAssetPath;
            if (string.IsNullOrEmpty(assetPath))
            {
                EditorUtility.DisplayDialog("Error", "Must be attached to a prefab!", "OK");
                return;
            }

            List<ChildPrefabTransform> childPrefabs = new List<ChildPrefabTransform>();
            int n = transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                var childTransform = transform.GetChild(i);
                var childPrefabInstance = childTransform.gameObject;

                //获取prefab instance使用的prefab
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);

                var childPrefabAssetPath = AssetDatabase.GetAssetPath(childPrefab);
                if (!string.IsNullOrEmpty(childPrefabAssetPath))
                {
                    var targetPrefabPath = GetTargetPrefabPath(childPrefabAssetPath);
                    if (!string.IsNullOrEmpty(targetPrefabPath))
                    {
                        var childPrefabTransform = new ChildPrefabTransform();
                        childPrefabTransform.path = targetPrefabPath;
                        childPrefabTransform.tag = childTransform.tag;
                        childPrefabTransform.position = childTransform.position;
                        childPrefabTransform.editorScaling = childTransform.localScale;
                        childPrefabTransform.editorRotation = childTransform.rotation;
                        childPrefabs.Add(childPrefabTransform);
                    }
                }
            }

            string newPrefabPath = Utils.RemoveExtension(assetPath) + "_gen.prefab";
            CreatePrefab(newPrefabPath, childPrefabs);
#endif
        }

        string GetChildPrefabPath(GameObject prefab)
        {
#if UNITY_EDITOR
            string path = AssetDatabase.GetAssetPath(prefab);
            if (string.IsNullOrEmpty(path))
            {
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(prefab);
                path = AssetDatabase.GetAssetPath(childPrefab);
            }
            return path;
#else
            return null;
#endif
        }

        string GetTargetPrefabPath(string sourcePrefabPath)
        {
#if UNITY_EDITOR
            for (int i = 0; i < mSourcePrefabs.Length; ++i)
            {
                if (mSourcePrefabs[i] != null)
                {
                    string path = GetChildPrefabPath(mSourcePrefabs[i]);
                    if (path == sourcePrefabPath)
                    {
                        return GetChildPrefabPath(mTargetPrefabs[i]);
                    }
                }
            }
            return sourcePrefabPath;
#else
            return null;
#endif
        }

        void CreatePrefab(string newPrefabPath, List<ChildPrefabTransform> childPrefabs)
        {
#if UNITY_EDITOR
            string prefabName = Utils.GetPathName(newPrefabPath, false);
            var newPrefabRoot = new GameObject(prefabName);
            PrefabUtility.SaveAsPrefabAsset(newPrefabRoot, newPrefabPath);
            var prefabInOtherScene = PrefabUtility.LoadPrefabContents(newPrefabPath);

            for (int i = 0; i < childPrefabs.Count; ++i)
            {
                if (!string.IsNullOrEmpty(childPrefabs[i].path))
                {
                    //加载一个独立场景的prefab    
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(childPrefabs[i].path);
                    Debug.Assert(childPrefab != null);

                    //生成一个prefab instance,用来嵌套到新的prefab中
                    var childPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(childPrefab);

                    childPrefabInstance.transform.position = childPrefabs[i].position;
                    childPrefabInstance.transform.rotation = childPrefabs[i].editorRotation;
                    childPrefabInstance.transform.localScale = childPrefabs[i].editorScaling;
                    childPrefabInstance.tag = childPrefabs[i].tag;
                    if (childPrefabInstance != null)
                    {
                        childPrefabInstance.transform.SetParent(prefabInOtherScene.transform, true);
                    }
                    else
                    {
                        Debug.Assert(false, "Invalid child prefab");
                    }
                }
            }

            PrefabUtility.SaveAsPrefabAsset(prefabInOtherScene, newPrefabPath);

            PrefabUtility.UnloadPrefabContents(prefabInOtherScene);
            GameObject.DestroyImmediate(newPrefabRoot);
#endif
        }

        public GameObject[] sourcePrefabs { get { return mSourcePrefabs; } }
        public GameObject[] targetPrefabs { get { return mTargetPrefabs; } }
        public int prefabCount
        {
            get
            {
                return mSourcePrefabs.Length;
            }
            set
            {
                int minCount = Mathf.Min(value, mSourcePrefabs.Length);
                GameObject[] newSourcePrefabs = new GameObject[value];
                GameObject[] newTargetPrefabs = new GameObject[value];
                for (int i = 0; i < minCount; ++i)
                {
                    newSourcePrefabs[i] = mSourcePrefabs[i];
                    newTargetPrefabs[i] = mTargetPrefabs[i];
                }
                mSourcePrefabs = newSourcePrefabs;
                mTargetPrefabs = newTargetPrefabs;
            }
        }

        [SerializeField]
        GameObject[] mSourcePrefabs = new GameObject[0];
        [SerializeField]
        GameObject[] mTargetPrefabs = new GameObject[0];
    }

#if UNITY_EDITOR
    [CustomEditor(typeof(ReplaceChildPrefab))]
    class ReplaceChildPrefabEditor : Editor
    {
        private void OnEnable()
        {
            mTarget = target as ReplaceChildPrefab;
        }

        public override void OnInspectorGUI()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetPrefabStage(mTarget.gameObject);
            if (stage == null)
            {
                return;
            }

            EditorGUILayout.BeginHorizontal();
            int newPrefabCount = EditorGUILayout.IntField("Prefab Count", mTarget.prefabCount);
            if (newPrefabCount != mTarget.prefabCount)
            {
                mTarget.prefabCount = newPrefabCount;
                EditorUtility.SetDirty(mTarget.gameObject);
                var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                if (prefabStage != null)
                {
                    EditorSceneManager.MarkSceneDirty(prefabStage.scene);
                }
            }
            if (GUILayout.Button("Replace"))
            {
                mTarget.Replace();
            }
            EditorGUILayout.EndHorizontal();

            DrawPrefabs(mTarget.sourcePrefabs, mTarget.targetPrefabs);
        }

        void DrawPrefabs(GameObject[] sourcePrefabs, GameObject[] targetPrefabs)
        {
            for (int i = 0; i < sourcePrefabs.Length; ++i)
            {
                EditorGUILayout.BeginHorizontal();
                var prefab = EditorGUILayout.ObjectField("", sourcePrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular && sourcePrefabs[i] != prefab)
                {
                    sourcePrefabs[i] = prefab;
                    EditorUtility.SetDirty(mTarget.gameObject);
                    var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                    if (prefabStage != null)
                    {
                        EditorSceneManager.MarkSceneDirty(prefabStage.scene);
                    }
                }

                prefab = EditorGUILayout.ObjectField("", targetPrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular && targetPrefabs[i] != prefab)
                {
                    targetPrefabs[i] = prefab;
                    EditorUtility.SetDirty(mTarget.gameObject);
                    var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                    if (prefabStage != null)
                    {
                        EditorSceneManager.MarkSceneDirty(prefabStage.scene);
                    }
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
            }
        }

        ReplaceChildPrefab mTarget;
    }

#endif
}

