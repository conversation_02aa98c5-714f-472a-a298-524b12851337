﻿//在vertex shader里根据动画贴图来skin

Shader "SLGMaker/ColorInstancing"
{
	SubShader
	{
		Tags { "RenderType" = "Opaque" }
		LOD 100

		Pass
		{
			CGPROGRAM
#pragma vertex vert
#pragma fragment frag
#pragma multi_compile_instancing
# include "UnityCG.cginc"

			UNITY_INSTANCING_BUFFER_START(Props)
				UNITY_DEFINE_INSTANCED_PROP(float4, _Color)
			UNITY_INSTANCING_BUFFER_END(Props)


			struct appdata
{
	float4 vertex : POSITION;
};

struct v2f
{
				float4 vertex : SV_POSITION;
			};

v2f vert(appdata v)
{
	v2f o;

	UNITY_SETUP_INSTANCE_ID(v);

	o.vertex = UnityObjectToClipPos(v.vertex);
	return o;
}

fixed4 frag(v2f i) : SV_Target
			{
				fixed4 col = _Color;
				return col;
			}
			ENDCG
		}
	}
}
