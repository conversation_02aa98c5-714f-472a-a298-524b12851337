%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo Material Layer Snow + Sand Advanced
  m_Shader: {fileID: 4800000, guid: 821eb686b9abe804f87193bcd690de25, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 8d95929314ce653489a6fa3be328f373, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap_snow:
        m_Texture: {fileID: 2800000, guid: c78e565eaf65cb1428fac66341a4a8bc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 2e7f49d39e4fb284eb26ddf4da1611ae, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _MainTex_sand:
        m_Texture: {fileID: 2800000, guid: 31943228e69a5b54992b5322327313e7, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture_sand:
        m_Texture: {fileID: 2800000, guid: 0a56ed8b745db3846972e3345f656b91, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture_snow:
        m_Texture: {fileID: 2800000, guid: 3eda0c4754885564f9df920405499055, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Albedo_sand: 0.8
    - _BumpScale: 0.5
    - _BumpScale_snow: 0.4
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _NoiseStrength_sand: 0.13
    - _NoiseStrength_snow: 0.236
    - _NormalThreshold_snow: -0.22
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _PositionRange_sand: -1.84
    - _PositionThreshold_sand: 0.82
    - _RampSmoothing: 0.2
    - _RampSmoothing_snow: 0.6
    - _RampThreshold: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    - __dummy__: 0
    - _contrast_sand: 0.16
    - _contrast_snow: 0.125
    m_Colors:
    - _Albedo_snow: {r: 0.98039216, g: 0.98039216, b: 0.98039216, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _SColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SColor_sand: {r: 0.78431374, g: 0.23921569, b: 0.23921569, a: 1}
    - _SColor_snow: {r: 0.41960785, g: 0.44313726, b: 0.5254902, a: 1}
