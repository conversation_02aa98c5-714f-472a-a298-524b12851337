fileFormatVersion: 2
guid: 8bc4e62ea9d964448a2c2e539d65b77a
timeCreated: **********
licenseType: Store
ShaderImporter:
  defaultTextures:
  - _NoTileNoiseTex: {fileID: 2800000, guid: af5515bfe14f1af4a9b8b3bf306b9261, type: 3}
  - _Ramp: {fileID: 2800000, guid: ccad9b0732473ee4e95de81e50e9050f, type: 3}
  userData: USER,FWORLDSPACE_UV,FVERTEX_SIN_WAVES,FVSW_2,FVSW_WORLDPOS,FVERTEX_SIN_NORMALS,FSMOOTH_FOAM,FUV_SIN_WAVES,FUSW_NORMAL,FSPECULAR_TOON,FDEPTH_BUFFER_COLOR,FEMISSION_PULSE,FEMISSION_COLOR,FUSW_SECOND_TEX,FEM_PULSE_MASK,FMASK2,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>1,<PERSON><PERSON><PERSON><PERSON>SK2,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>_<PERSON>SK,<PERSON>AS<PERSON>_MAINTEX,FCOLORMASK_SEPARATE,FDEPTH_BUFFER_FOAM,fnolightmap,KCOLORMASK_CHANNEL:.r,KUV_mask1:Main
    Tex UV,KEMISSION_MASK_CHANNEL:.r,KEM_PULSE_MASK:mask2,KEM_PULSE_MASK_CHANNEL:.g,KUV_mask2:Independent
    UV,KEMISSION_MASK:mask1,KRIM_MASK_CHANNEL:.r,KRIM_MASK:mainTex,KUV_mask3:Main
    Tex UV,KEM_PULSE_MULT:1.5,KSHADER_TARGET:3.0,ch95EC0A7F,cSM:30,cCT:water,cCF:TCP2_ShaderTemplate_Water
  assetBundleName: 
  assetBundleVariant: 
