﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class SplineEditorUI : Editor
    {
        public void HandleEditControlPointFunction(Event e, Vector3 worldPos)
        {
            if ((e.type == EventType.MouseDown || e.type == EventType.MouseDrag) && e.button == 0 && e.alt == false)
            {
                if (e.type == EventType.MouseDown && e.control)
                {
                    if (e.shift)
                    {
                        PickSpline(worldPos);
                        RemoveControlPoint(mSelectedControlPointIndex);
                    }
                    else
                    {
                        AddControlPoint(worldPos);
                    }
                }
                else
                {
                    if (e.type == EventType.MouseDown)
                    {
                        PickSpline(worldPos);
                    }
                    MoveControlPoint(worldPos);
                }
                Repaint();
                SceneView.RepaintAll();
            }

            if (e.type == EventType.MouseUp)
            {
                mMover.Reset();
            }

            HandleUtility.AddDefaultControl(0);
        }

        int TryPickControlPoint(Vector3 worldPos, SplineObject spline)
        {
            float pickRadius2 = mEditor.splineObjectManager.displayRadius;
            pickRadius2 *= pickRadius2;
            var controlPoints = spline.controlPoints;
            for (int i = 0; i < controlPoints.Count; ++i)
            {
                var d = worldPos - controlPoints[i].pos;
                if (d.sqrMagnitude <= pickRadius2)
                {
                    return i;
                }
            }
            return -1;
        }

        void PickControlPoint(Vector3 worldPos, SplineObject spline)
        {
            mMover.Reset();
            mSelectedControlPointIndex = TryPickControlPoint(worldPos, spline);
        }

        void RemoveControlPoint(int index)
        {
            if (index >= 0 && mSelectedSplineObjectID > 0)
            {
                mSelectedTangentPointIndex = -1;
                var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
                if (spline.controlPoints.Count > 2)
                {
                    spline.RemoveControlPoint(index);
                    UpdateDisplay(spline);
                    mSelectedControlPointIndex = -1;
                }
            }
        }

        void AddControlPoint(Vector3 pos)
        {
            if (mSelectedSplineObjectID > 0)
            {
                mSelectedTangentPointIndex = -1;
                var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
                var idx = Utils.FindNearestEdgeDistance(pos, mDisplayControlPoints, spline.isLoop);
                var controlPoint = spline.GetControlPoint(idx);
                int pointCountInOneSegment = 5;
                float width = 1.0f;
                if (controlPoint != null)
                {
                    pointCountInOneSegment = controlPoint.pointCountInSegment;
                    width = controlPoint.width;
                }
                spline.InsertControlPoint(idx, pos, width, pointCountInOneSegment);
                UpdateDisplay(spline);
            }
        }

        void MoveControlPoint(Vector3 worldPos)
        {
            if (mSelectedSplineObjectID > 0 && mSelectedControlPointIndex >= 0)
            {
                mMover.Update(worldPos);

                var delta = mMover.GetDelta();
                if (delta != Vector3.zero)
                {
                    var spline = mEditor.splineObjectManager.FindSplineObject(mSelectedSplineObjectID);
                    if (mSelectedTangentPointIndex >= 0)
                    {
                        var type = mEditor.splineObjectManager.tangentMoveType;
                        spline.MoveTangent(mSelectedControlPointIndex, mSelectedTangentPointIndex, delta, type);
                    }
                    else
                    {
                        spline.MoveControlPoint(mSelectedControlPointIndex, delta);
                        spline.MoveTangent(mSelectedControlPointIndex, 0, delta, TangentMoveType.Free);
                        spline.MoveTangent(mSelectedControlPointIndex, 1, delta, TangentMoveType.Free);
                    }
                    UpdateDisplay(spline);
                }
            }
        }

        void DrawEditControlPointFunctionSceneGUI()
        {
        }

        void DrawEditControlPointFunctionInspectorGUI()
        {
#if false
            if (mSelectedControlPointIndex >= 0)
            {
                var spline = GetSelectedSpline();
                var controlPoint = spline.controlPoints[mSelectedControlPointIndex];
                float newTexCoord = EditorGUILayout.FloatField("Horizontal Texture Coordinate", controlPoint.horizontalTextureCoord);
                if (!Mathf.Approximately(newTexCoord, controlPoint.horizontalTextureCoord))
                {
                    spline.SetTextureCoord(mSelectedControlPointIndex, newTexCoord);
                    UpdateDisplay(spline);
                }
            }
#endif
        }

        int mSelectedControlPointIndex = -1;
        int mSelectedTangentPointIndex = -1;
    }
}

#endif