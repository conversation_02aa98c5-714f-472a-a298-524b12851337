﻿








#if UNITY_EDITOR

/*
 * created by wzw at 2019.12.12
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum EditorTerritoryLayerOperation
    {
        SetGrid,
        ClearGrid,
        CreateBuilding,
        EditOutline,
    }

    [ExecuteInEditMode]
    public class EditorTerritoryLayerLogic : MapLayerLogic
    {
        public int selectedSubLayerIndex = -1;
        public bool showMeshGeneration = true;
        public bool showAdvancedParam = false;
        protected override MoveAxis moveAxis { get { return MoveAxis.Y; } }

        public EditorTerritoryLayer layer
        {
            get
            {
                return Map.currentMap.GetMapLayerByID(layerID) as EditorTerritoryLayer;
            }
        }
    }
}

#endif