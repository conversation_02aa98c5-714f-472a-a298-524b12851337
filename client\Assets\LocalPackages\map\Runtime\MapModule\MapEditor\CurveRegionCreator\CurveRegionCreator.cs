﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;
using System.Linq;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        public CurveRegionCreator(Transform parent)
        {
            mRoot = new GameObject("Root");
            mRoot.transform.SetParent(parent, true);
            Utils.HideGameObject(mRoot);
        }

        public void OnDestroy()
        {
            Clear();
            Utils.DestroyObject(mRoot);
        }

        void Clear()
        {
            mNextID = 0;
            mEvaluatedSegments.Clear();
            mEdgeAssetsInfo.Clear();
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].OnDestroy();
            }
            mTerritories.Clear();
        }

        //创建线条
        public void Create(Input input, bool displayProgressBar)
        {
            mInput = input;

            Clear();

            CreateTerritories(input.regions);
            if (displayProgressBar)
            {
                bool cancel = EditorUtility.DisplayCancelableProgressBar("Generating Region Data", $"Createing Territories...", 0.1f);
                if (cancel)
                {
                    return;
                }
            }

            RemoveTerritoriesVertices();
            if (displayProgressBar)
            {
                bool cancel = EditorUtility.DisplayCancelableProgressBar("Generating Region Data", $"Removing Territory Vertices...", 0.2f);
                if (cancel)
                {
                    return;
                }
            }

            CreateTerritoriesControlPoints();
            if (displayProgressBar)
            {
                bool cancel = EditorUtility.DisplayCancelableProgressBar("Generating Region Data", $"Creating Territory Control Points...", 0.3f);
                if (cancel)
                {
                    return;
                }
            }
        }

        //生成mesh
        public void Generate(string folder, int lod, bool displayProgressBar, bool generateAssets, float layerWidth, float layerHeight, int horizontalTileCount, int verticalTileCount)
        {
            mEvaluatedSegments.Clear();
            mEdgeAssetsInfo.Clear();

            SmoothTerritoriesVertices();
            if (displayProgressBar)
            {
                bool cancel = EditorUtility.DisplayCancelableProgressBar("Generating Region Data", $"Smoothing Territory Vertices...", 0.5f);
                if (cancel)
                {
                    return;
                }
            }

            try
            {
                AssetDatabase.StartAssetEditing();

                TriangulateTerritories(folder, lod, displayProgressBar, generateAssets);

                //合并被两个区域共享的两个edge为一个
                if (mInput.settings.mergeEdge)
                {
                    CombineTerritorySharedEdges(generateAssets, lod);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            if (mInput.settings.combineMesh && generateAssets)
            {
                CombineTerritoryMesh(layerWidth, layerHeight, horizontalTileCount, verticalTileCount, generateAssets, folder, lod);
            }
        }

        void CreateTerritories(List<RegionInput> regions)
        {
            //一个区域的所有格子必须是相连的，并且没有洞
            for (int i = 0; i < regions.Count; ++i)
            {
                var coord = regions[i].coordinates[0];
                var territory = CreateTerritory(regions[i].regionID, coord.x, coord.y);
                mTerritories.Add(territory);
            }
        }

        //删除并且移动某些满足条件的顶点，让边缘线更平滑
        List<Vector3> RemoveAndMoveVertices(Territory t, List<Vector3> outline, int territoryIndex)
        {
            List<Vector3> removedVertices = new List<Vector3>();

            //创建一个包含更多信息的顶点列表，方便后续对这个列表进行删除操作
            List<VertexWrapper> wrappedVertices = new List<VertexWrapper>();
            for (int i = 0; i < outline.Count; ++i)
            {
                var v = new VertexWrapper();
                v.index = i;
                v.position = outline[i];
                v.removed = false;
                wrappedVertices.Add(v);
            }

            float distanceThreshold = mInput.gridSize * mInput.settings.gridErrorThreshold;
            for (int i = 0; i < wrappedVertices.Count - 1; ++i)
            {
                if (!wrappedVertices[i].removed)
                {
                    //删除与这个点i距离够近且满足一定条件的点
                    RemoveCloseEnoughVertices(t, wrappedVertices[i], i, wrappedVertices, distanceThreshold, territoryIndex);
                }
            }

            for (int i = wrappedVertices.Count - 1; i >= 0; --i)
            {
                if (wrappedVertices[i].moved)
                {
                    //将某些顶点移动,让边缘更圆滑
                    outline[i] += new Vector3(mInput.gridSize * 0.5f, 0, mInput.gridSize * 0.5f);
                }
            }

            //去掉已经删除的顶点
            for (int i = wrappedVertices.Count - 1; i >= 0; --i)
            {
                if (wrappedVertices[i].removed)
                {
                    removedVertices.Add(wrappedVertices[i].position);
                    outline.RemoveAt(i);
                }
            }

            return removedVertices;
        }

        //删除与点index够近且满足一些其他条件的点
        void RemoveCloseEnoughVertices(Territory t, VertexWrapper v, int index, List<VertexWrapper> vertices, float distanceThreshold, int territoryIndex)
        {
            //遍历从index + 1开始的点
            int n = vertices.Count;
            for (int k = index + 1; k < vertices.Count; ++k)
            {
                if (vertices[k].removed)
                {
                    //点k已经被删除，跳过
                    continue;
                }

                var d1 = vertices[k].position - v.position;
                var d2 = vertices[(k + 1) % n].position - vertices[k].position;
                if (IsLeftTurnLH(d1, d2))
                {
                    //如果形成了left turn,则表示v, vertices[k], vertices[k+1]是内拐,vertices[k]是内部点,不删除，因为创建的边框会包围所有的outline点
                    break;
                }
                float dis = Vector3.Distance(v.position, vertices[k].position);
                bool satisfyRectangularCondition = true;
                if (mInput.settings.moreRectangular)
                {
                    //k+1和k点的距离也小于distanceThreshold才删除,这样生成的边缘更多方形
                    if (k + 1 < vertices.Count)
                    {
                        float dis2 = Vector3.Distance(vertices[k + 1].position, vertices[k].position);
                        satisfyRectangularCondition = dis2 <= distanceThreshold;
                    }
                }

                if (dis <= distanceThreshold && satisfyRectangularCondition)
                {
                    //后面的区域不能删除前面区域的点，如果删除了会导致区域之间有接缝
                    if (!IsPreviousTerritoryVertex(vertices[k].position, territoryIndex))
                    {
                        bool isSharedEdgeEndPoint = IsSharedEdgeEndPoint(t, vertices[k].position);
                        if (isSharedEdgeEndPoint)
                        {
                            break;
                        }
                        //删除k点
                        vertices[k].removed = true;
                        //check if it is interior point
                        var coord0 = mInput.fromPositionToCoordinate(v.position);
                        var coord1 = mInput.fromPositionToCoordinate(vertices[k].position);
                        if (mInput.getRegionIDFunc(coord0.x, coord0.y) == mInput.getRegionIDFunc(coord1.x, coord1.y))
                        {
                            //v点和k点都是属于同一个区域，移动k点到格子中心
                            vertices[k].moved = true;
                        }
                    }
                }
                else
                {
                    break;
                }
            }
        }

        //pos坐标的点是否是territoryIndex之前的区域的点
        bool IsPreviousTerritoryVertex(Vector3 pos, int territoryIndex)
        {
            for (int i = territoryIndex - 1; i >= 0; --i)
            {
                if (mTerritories[i].outline.Contains(pos))
                {
                    return true;
                }
            }
            return false;
        }

        //是否是shared edge的最后一个点
        bool IsSharedEdgeEndPoint(Territory t, Vector3 pos)
        {
            var sharedEdges = t.sharedEdges;
            int n = sharedEdges.Count;
            for (int i = 0; i < n; ++i)
            {
                if (sharedEdges[i].IsEndPoint(pos))
                {
                    return true;
                }
            }
            return false;
        }

        Territory CreateTerritory(int regionID, int x, int y)
        {
            //区域所有边缘格子的边的集合
            List<Edge> allEdges = new List<Edge>();
            //遍历找到一个区域中所有的边缘格子的边集合
            List<Vector2Int> stack = new List<Vector2Int>();
            stack.Add(new Vector2Int(x, y));
            HashSet<Vector2Int> processed = new HashSet<Vector2Int>();
            Vector2Int[] offset = new Vector2Int[4]
            {
                new Vector2Int(0, 1),
                new Vector2Int(0, -1),
                new Vector2Int(1, 0),
                new Vector2Int(-1, 0),
            };
            while (stack.Count > 0)
            {
                var coord = stack[stack.Count - 1];
                stack.RemoveAt(stack.Count - 1);
                processed.Add(coord);

                var edges = GetEdges(coord.x, coord.y, regionID);
                if (edges != null)
                {
                    allEdges.AddRange(edges);
                }

                for (int i = 0; i < 4; ++i)
                {
                    var neighbourCoord = coord + offset[i];
                    if (processed.Contains(neighbourCoord) == false && mInput.getRegionIDFunc(neighbourCoord.x, neighbourCoord.y) == regionID)
                    {
                        if (stack.Contains(neighbourCoord) == false)
                        {
                            stack.Add(neighbourCoord);
                        }
                    }
                }
            }

            //将这些格子的边合并成更长的边
            return Interconnect(allEdges, regionID, mInput.settings.vertexDisplayRadius);
        }

        /*
         * Edge方向,是逆时针
         * 3 <- 2
         * |    |
         * 0 -> 1
         */
        List<Edge> GetEdges(int x, int y, int regionID)
        {
            List<Edge> edges = null;
            Debug.Assert(regionID != 0);
            if (x >= 0 && x < mInput.horizontalGridCount && y >= 0 && y < mInput.verticalGridCount)
            {
                edges = new List<Edge>();
                int selfRegionID = mInput.getRegionIDFunc(x - 1, y);
                int neighbourRegionID = mInput.getRegionIDFunc(x + 1, y);
                int topRegionID = mInput.getRegionIDFunc(x, y + 1);
                int bottomRegionID = mInput.getRegionIDFunc(x, y - 1);
                if (selfRegionID != regionID)
                {
                    var leftEdge = new Edge(nextID, mInput.fromCoordinateToPositionFunc(x, y + 1), mInput.fromCoordinateToPositionFunc(x, y), regionID, selfRegionID);
                    edges.Add(leftEdge);
                }
                if (neighbourRegionID != regionID)
                {
                    var rightEdge = new Edge(nextID, mInput.fromCoordinateToPositionFunc(x + 1, y), mInput.fromCoordinateToPositionFunc(x + 1, y + 1), regionID, neighbourRegionID);
                    edges.Add(rightEdge);
                }
                if (topRegionID != regionID)
                {
                    var topEdge = new Edge(nextID, mInput.fromCoordinateToPositionFunc(x + 1, y + 1), mInput.fromCoordinateToPositionFunc(x, y + 1), regionID, topRegionID);
                    edges.Add(topEdge);
                }
                if (bottomRegionID != regionID)
                {
                    var bottomEdge = new Edge(nextID, mInput.fromCoordinateToPositionFunc(x, y), mInput.fromCoordinateToPositionFunc(x + 1, y), regionID, bottomRegionID);
                    edges.Add(bottomEdge);
                }
            }
            return edges;
        }

        //合并边
        Territory Interconnect(List<Edge> edges, int regionID, float vertexDisplayRadius)
        {
            for (int i = 0; i < edges.Count; ++i)
            {
                if (edges[i].removed == false)
                {
                    for (int j = edges.Count - 1; j >= 0; --j)
                    {
                        if (edges[j].removed == false)
                        {
                            if (TryConnect(edges[i], edges[j]))
                            {
                                //i和j合并后，删除j
                                edges[j].removed = true;
                            }
                        }
                    }
                }
            }

            //key is edge start position, value is edge
            Dictionary<Vector3, Edge> startPosToEdge = new Dictionary<Vector3, Edge>();
            for (int i = edges.Count - 1; i >= 0; --i)
            {
                if (edges[i].removed)
                {
                    edges.RemoveAt(i);
                }
                else
                {
                    startPosToEdge.Add(edges[i].start, edges[i]);
                }
            }

            List<Vector3> outline = new List<Vector3>();
            outline.Add(edges[0].start);
            var curEdge = edges[0];
            Vector3 end = curEdge.start;
            //从相连的edge一直走，找到所有的outline顶点
            while (curEdge != null)
            {
                bool found = startPosToEdge.TryGetValue(curEdge.end, out Edge connectedEdge);
                Debug.Assert(found);
                curEdge = connectedEdge;
                outline.Add(connectedEdge.start);
                if (connectedEdge.end == end)
                {
                    //又回到了起点，outline构建完毕
                    break;
                }
            }

            if (mInput.settings.shareEdge == false)
            {
                foreach (var edge in edges)
                {
                    edge.neighbourRegionID = 0;
                }
            }

            //根据territory的edge创建territory和相邻territory之间共享的边信息，用于后续创建分段的edge mesh
            var sharedEdgesWithNeighbourTerritory = CreateSharedEdgesWithNeighbourTerritory(edges);
            var regionColor = mInput.getRegionColorFunc(regionID);
            var territory = new Territory(sharedEdgesWithNeighbourTerritory, edges, outline, regionID, regionColor);
            return territory;
        }

        //根据territory的edge创建territory和相邻territory之间共享的边信息，用于后续创建分段的edge mesh
        List<SharedEdgeWithNeighbourTerritroy> CreateSharedEdgesWithNeighbourTerritory(List<Edge> edges)
        {
            List<SharedEdgeWithNeighbourTerritroy> sharedEdges = new List<SharedEdgeWithNeighbourTerritroy>();
            HashSet<int> processedEdges = new HashSet<int>();
            Dictionary<Vector3, Edge> startPosToEdge = new Dictionary<Vector3, Edge>();
            for (int i = edges.Count - 1; i >= 0; --i)
            {
                startPosToEdge.Add(edges[i].start, edges[i]);
            }
            Dictionary<Vector3, Edge> endPosToEdge = new Dictionary<Vector3, Edge>();
            for (int i = edges.Count - 1; i >= 0; --i)
            {
                endPosToEdge.Add(edges[i].end, edges[i]);
            }

            for (int e = 0; e < edges.Count; ++e)
            {
                if (processedEdges.Contains(edges[e].id))
                {
                    continue;
                }
                List<Edge> stack = new List<Edge>();

                stack.Add(edges[e]);
                while (stack.Count > 0)
                {
                    Edge cur = stack[stack.Count - 1];
                    stack.RemoveAt(stack.Count - 1);
                    processedEdges.Add(cur.id);

                    //check edge which is connected to current edge's start position
                    endPosToEdge.TryGetValue(cur.start, out Edge connectedToCurrentEdgeStartPoint);
                    if (!processedEdges.Contains(connectedToCurrentEdgeStartPoint.id))
                    {
                        if (IsSharedEdge(cur, connectedToCurrentEdgeStartPoint))
                        {
                            stack.Add(connectedToCurrentEdgeStartPoint);
                            cur.connectedToStart = connectedToCurrentEdgeStartPoint;
                            connectedToCurrentEdgeStartPoint.connectedToEnd = cur;
                        }
                    }

                    //check edge which is connected to current edge's end position
                    startPosToEdge.TryGetValue(cur.end, out Edge connectedToCurrentEdgeEndPoint);
                    if (!processedEdges.Contains(connectedToCurrentEdgeEndPoint.id))
                    {
                        if (IsSharedEdge(cur, connectedToCurrentEdgeEndPoint))
                        {
                            stack.Add(connectedToCurrentEdgeEndPoint);
                            cur.connectedToEnd = connectedToCurrentEdgeEndPoint;
                            connectedToCurrentEdgeEndPoint.connectedToStart = cur;
                        }
                    }
                }

                var connectedEdgeVertices = ConnectEdges(edges[e]);
                //EditorUtils.CreateDrawLineStrip("rectangle shared edge vertices", connectedEdgeVertices, mInput.settings.vertexDisplayRadius);
                sharedEdges.Add(new SharedEdgeWithNeighbourTerritroy(connectedEdgeVertices, edges[e].selfRegionID, edges[e].neighbourRegionID));
            }

            return sharedEdges;
        }

        bool IsSharedEdge(Edge a, Edge b)
        {
            return a.selfRegionID == b.selfRegionID &&
                a.neighbourRegionID == b.neighbourRegionID;
        }

        bool IsLoop(Edge e)
        {
            Edge s = e;
            while (e != null)
            {
                e = e.connectedToStart;
                if (e == s)
                {
                    return true;
                }
            }
            return false;
        }

        //合并和e相连的所有边的顶点
        List<Vector3> ConnectEdges(Edge e)
        {
            if (IsLoop(e))
            {
                //断开链表
                e.connectedToStart.connectedToEnd = null;
                e.connectedToStart = null;
            }

            Edge header = null;
            while (e != null)
            {
                if (e.connectedToStart == null)
                {
                    header = e;
                    break;
                }
                e = e.connectedToStart;
            }

            List<Vector3> edgeVertices = new List<Vector3>();
            while (header != null)
            {
                edgeVertices.Add(header.start);
                if (header.connectedToEnd == null)
                {
                    edgeVertices.Add(header.end);
                }
                header = header.connectedToEnd;
            }

            return edgeVertices;
        }

        //如果a和b可以合并，则合并成更长的边，并删除b
        bool TryConnect(Edge a, Edge b)
        {
            if (a.end == b.end && a.start == b.start)
            {
                //ab是同一条边，跳过
                return false;
            }
            if (a.neighbourRegionID != b.neighbourRegionID)
            {
                //a，b边不是相邻的同一个区域，不能合并
                return false;
            }

            if (a.isHorizontal != b.isHorizontal)
            {
                //a，b不是同一个方向，不能合并
                return false;
            }
            bool connected = false;
            if (a.end == b.start)
            {
                connected = true;
                a.end = b.end;
            }
            else if (a.start == b.end)
            {
                a.start = b.start;
                connected = true;
            }
            else if (a.start == b.start)
            {
                a.start = b.end;
                connected = true;
            }
            else if (a.end == b.end)
            {
                a.end = b.start;
                connected = true;
            }
            return connected;
        }

        List<ControlPoint> CreateControlPoints(List<Vector3> outline, float ratio, float minTangentLength, float maxTangentLength, int regionID)
        {
            List<ControlPoint> controlPoints = new List<ControlPoint>();
            int n = outline.Count;
            for (int i = 0; i < n; ++i)
            {
                var dir0 = outline[i] - outline[Mod(i - 1, n)];
                var dir1 = outline[i] - outline[Mod(i + 1, n)];
                var d = dir0.normalized + dir1.normalized;
                d.Normalize();
                //计算顶点的切线方向,因为outline是逆时针环绕，所以用右手坐标系算法
                var p = new Vector3(-d.z, 0, d.x);
                //计算一个随机的切线旋转角度
                float randomAngle = Random.Range(-mInput.settings.tangentRandomRotationRange, mInput.settings.tangentRandomRotationRange);
                Quaternion q = Quaternion.Euler(0, randomAngle, 0);
                p = q * p;
                //ratio加一个随机值
                float r = ratio + Random.Range(0, mInput.settings.segmentLengthRatioRandomRange);
                float dis0 = Mathf.Clamp(dir0.magnitude * r, minTangentLength, maxTangentLength);
                float dis1 = Mathf.Clamp(dir1.magnitude * r, minTangentLength, maxTangentLength);

                bool isLeftTurn = IsLeftTurnLH(dir0, -dir1);
                if (!isLeftTurn)
                {
                    //切线方向取反
                    p = -p;
                }

                var tangent0 = outline[i] - p * dis0;
                var tangent1 = outline[i] + p * dis1;

                var controlPoint = new ControlPoint(outline[i], tangent0, tangent1, mInput.settings.vertexDisplayRadius, $"{regionID}_{i}", mRoot.transform);
                controlPoints.Add(controlPoint);
            }

            return controlPoints;
        }

        //生成曲线
        List<Vector3> SmoothVertices(List<ControlPoint> controlPoints, float pointDeltaDistance, int maxPointCountInOneSegment)
        {
            List<Vector3> curvePoints = new List<Vector3>();
            var result = Evaluate(controlPoints, pointDeltaDistance, maxPointCountInOneSegment);
            for (int i = 0; i < result.Count; ++i)
            {
                curvePoints.Add(result[i].pos);
            }
            return curvePoints;
        }

        List<EvaluatePoint> Evaluate(List<ControlPoint> controlPoints, float pointDeltaDistance, int maxPointCountInOneSegment)
        {
            List<EvaluatePoint> evaluatePoints = new List<EvaluatePoint>();
            for (int i = 0; i < controlPoints.Count - 1; ++i)
            {
                //分段生成曲线
                var result = EvaluateSegment(controlPoints[i], controlPoints[i + 1], i + 1, pointDeltaDistance, maxPointCountInOneSegment);
                AddEvaluatePoints(evaluatePoints, result);
            }

            if (controlPoints.Count > 2)
            {
                //首尾相连
                var result = EvaluateSegment(controlPoints[controlPoints.Count - 1], controlPoints[0], 0, pointDeltaDistance, maxPointCountInOneSegment);
                AddEvaluatePoints(evaluatePoints, result);
                //remove last point which is same as first point
                evaluatePoints.RemoveAt(evaluatePoints.Count - 1);
            }

            return evaluatePoints;
        }

        void AddEvaluatePoints(List<EvaluatePoint> allPoints, List<EvaluatePoint> pointsInOneSegment)
        {
            for (int i = 0; i < pointsInOneSegment.Count; ++i)
            {
                var pos = pointsInOneSegment[i].pos;
                if (allPoints.Count == 0 || allPoints[allPoints.Count - 1].pos != pos)
                {
                    allPoints.Add(new EvaluatePoint(pos));
                }
            }
        }

        class SegmentEvaluateInfo
        {
            public SegmentEvaluateInfo(Vector3 start, Vector3 end, List<EvaluatePoint> points)
            {
                this.start = start;
                this.end = end;
                this.points = points;
            }

            public Vector3 start;
            public Vector3 end;
            public List<EvaluatePoint> points;
        }

        List<EvaluatePoint> FindPointsReverse(Vector3 controlPointStart, Vector3 controlPointEnd)
        {
            for (int i = 0; i < mEvaluatedSegments.Count; ++i)
            {
                if (mEvaluatedSegments[i].end == controlPointStart &&
                    mEvaluatedSegments[i].start == controlPointEnd)
                {
                    return Utils.GetReverseList(mEvaluatedSegments[i].points);
                }
            }
            return null;
        }

        List<EvaluatePoint> FindPoints(Vector3 controlPointStart, Vector3 controlPointEnd)
        {
            for (int i = 0; i < mEvaluatedSegments.Count; ++i)
            {
                if (mEvaluatedSegments[i].start == controlPointStart &&
                    mEvaluatedSegments[i].end == controlPointEnd)
                {
                    return mEvaluatedSegments[i].points;
                }
            }

            return FindPointsReverse(controlPointStart, controlPointEnd);
        }

        List<EvaluatePoint> EvaluateSegment(ControlPoint start, ControlPoint end, int controlPointIndex, float pointDeltaDistance, int maxPointCountInOneSegment)
        {
            //先查找是否已经生成过了end到start之间的线段，注意这里顺序要取反，因为邻边的winding order是相反的
            List<EvaluatePoint> points = FindPointsReverse(start.position, end.position);
            if (points != null)
            {
                return points;
            }

            points = new List<EvaluatePoint>();

            float distance = Vector3.Distance(end.position, start.position);
            //计算需要插值多少个点
            int n = Mathf.Min(Mathf.CeilToInt(distance / pointDeltaDistance), maxPointCountInOneSegment);
            for (int i = 0; i < n; ++i)
            {
                float t = (float)i / (n - 1);
                var pos = SplineUtils.Bezier(start.position, start.tangent1, end.tangent0, end.position, t);
                points.Add(new EvaluatePoint(pos));
            }

            //保存该段evaluate的结果，共后续邻接区域的共享边使用
            mEvaluatedSegments.Add(new SegmentEvaluateInfo(start.position, end.position, points));
            return points;
        }
        
        //删除符合某些条件的顶点
        void RemoveTerritoriesVertices()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var removedVertices = RemoveAndMoveVertices(mTerritories[i], mTerritories[i].outline, i);
                //删除self intersected points
                var outline = new List<Vector3>();
                outline.AddRange(mTerritories[i].outline);
                Utils.RemoveSelfIntersection(outline, out var removedSelfIntersectionVertices);
                if (removedSelfIntersectionVertices.Count > 0)
                {
                    foreach (var pos in removedSelfIntersectionVertices)
                    {
                        Debug.LogError($"Found Self Intersection position at {pos}");
                    }
                }
                //removedVertices.AddRange(removedSelfIntersectionVertices);
                //删除其他territory的顶点,还会删除shared edge的顶点
                for (int k = 0; k < mTerritories.Count; ++k)
                {
                    mTerritories[k].RemoveVertices(removedVertices);
#if false
                    var objddd = new GameObject($"After Remove Vertices of preview region {mTerritories[k].outline.Count}");
                    var dp11 = objddd.AddComponent<DrawPolygon>();
                    dp11.radius = mInput.settings.vertexDisplayRadius;
                    dp11.SetVertices(mTerritories[k].outline);
#endif

                }

#if false
                var obj1 = new GameObject($"After Remove Vertices {mTerritories[i].regionID} {mTerritories[i].outline.Count}");
                var dp1 = obj1.AddComponent<DrawPolygon>();
                dp1.radius = mInput.settings.vertexDisplayRadius;
                dp1.SetVertices(mTerritories[i].outline);
#endif
            }
        }

        //将outline的点转换成control point,为曲线生成做准备
        void CreateTerritoriesControlPoints()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].controlPoints = CreateControlPoints(mTerritories[i].outline, mInput.settings.segmentLengthRatio, mInput.settings.minTangentLength, mInput.settings.maxTangentLength, mTerritories[i].regionID);

                //将territory的shared edge的顶点转换成control point的引用
                mTerritories[i].CreateSharedEdgeControlPoints();
            }
        }

        //根据territory的control points生成曲线outline
        void SmoothTerritoriesVertices()
        {
            //smooth
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].outline = SmoothVertices(mTerritories[i].controlPoints, mInput.settings.pointDeltaDistance, mInput.settings.maxPointCountInOneSegment);
#if false
                var obj3 = new GameObject($"Curve Outline {mTerritories[i].regionID} {mTerritories[i].outline.Count}");
                var dp3 = obj3.AddComponent<DrawPolygon>();
                dp3.radius = mInput.settings.vertexDisplayRadius;
                dp3.SetVertices(mTerritories[i].outline);
                obj3.transform.SetParent(mRoot.transform, true);
                mTerritories[i].SetGameObject(Territory.ObjectType.CurveOutline, obj3);
#endif
            }
        }

        public void HideLineAndMesh()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].HideLineAndMesh();
            }
        }

        public void HideLine()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].HideLine();   
            }
        }

        public void HideMesh()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].HideMesh();
            }
        }

        public void HideRegionMesh()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].HideRegionMesh();
            }
        }

        public void ShowMesh()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].ShowMesh();
            }
        }

        public int nextID { get { return ++mNextID; } }
        public List<EdgeAssetInfo> edgeAssetsInfo { get { return mEdgeAssetsInfo; } set { mEdgeAssetsInfo = value; } }
        public List<Block> blocks { get { return mBlocks; } set { mBlocks = value; } }
        public int lod1MaskTextureWidth { get { return mLOD1MaskTextureWidth; } set { mLOD1MaskTextureWidth = value; } }
        public int lod1MaskTextureHeight { get { return mLOD1MaskTextureHeight; } set { mLOD1MaskTextureHeight = value; } }
        public Color32[] lod1MaskTextureData { get { return mLOD1MaskTextureData; } set { mLOD1MaskTextureData = value; } }

        List<SegmentEvaluateInfo> mEvaluatedSegments = new List<SegmentEvaluateInfo>();
        List<Territory> mTerritories = new List<Territory>();
        List<EdgeAssetInfo> mEdgeAssetsInfo = new List<EdgeAssetInfo>();
        List<Block> mBlocks = new List<Block>();
        Input mInput;
        GameObject mRoot;
        int mNextID;
        int mLOD1MaskTextureWidth;
        int mLOD1MaskTextureHeight;
        Color32[] mLOD1MaskTextureData;
    }
}

#endif