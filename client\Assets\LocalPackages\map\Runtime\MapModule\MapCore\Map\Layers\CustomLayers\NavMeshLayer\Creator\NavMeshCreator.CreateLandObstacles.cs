﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateLandObstacles(PrefabOutlineType type, Vector3 min, Vector3 max, List<IObstacle> obstacles, float agentRadius, float minimumAngle, float maximumArea, bool useDelaunay, float scaleFactor, out Vector3[] meshVertices, out int[] meshIndices)
        {
            //创建山体等障碍物mesh
            var combineMeshies = new List<MeshItem>();
            List<List<Vector3>> holes = null;
            Vector3[] normalObstacleVertices = null;
            int[] normalObstacleIndices = null;
            var obstaclePolygons = PolygonAlgorithm.GetCombinedObstaclePolygons(type, obstacles, 0, obstacles.Count - 1, agentRadius, scaleFactor, out holes);
            if (obstaclePolygons.Count > 0)
            {
                Triangulator.TriangulatePolygons(obstaclePolygons, holes, useDelaunay, minimumAngle, maximumArea, null, out normalObstacleVertices, out normalObstacleIndices);
                combineMeshies.Add(new MeshItem(normalObstacleVertices, normalObstacleIndices));
            }

            if (min != Vector3.zero || max != new Vector3(Map.currentMap.mapWidth, 0, Map.currentMap.mapHeight))
            {
                Vector3[] invalidSpaceMeshVertices;
                int[] invalidSpaceMeshIndices;
                CreateInvalidSpaceObstacleMesh(min, max, out invalidSpaceMeshVertices, out invalidSpaceMeshIndices);
                combineMeshies.Add(new MeshItem(invalidSpaceMeshVertices, invalidSpaceMeshIndices));
            }

            CombineMesh(combineMeshies, out meshVertices, out meshIndices);
        }
    }
}


#endif