﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionAddRegion : EditorAction
    {
        public ActionAddRegion(int layerID, int dataID, List<Vector3> vertices, bool isExtendable, Color innerColor, Color outerColor, RegionType type, int number, Material mtl)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mVertices = new List<Vector3>(vertices.Count);
            mVertices.AddRange(vertices);
            mIsExtendable = isExtendable;
            mInnerColor = innerColor;
            mOuterColor = outerColor;
            mNumber = number;
            mType = type;
            mMaterial = mtl;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RegionLayer;
            if (layer == null)
            {
                return false;
            }
            var outlineData = new OutlineData(mVertices);
            var outlineDatas = new OutlineData[2];
            outlineDatas[0] = outlineData;
            var data = new RegionData(mDataID, Map.currentMap, outlineDatas, layer.displayVertexRadius, mIsExtendable, null, null, null, mInnerColor, mOuterColor, mType, mNumber, mMaterial);
            layer.AddObject(data);

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RegionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        Color mInnerColor;
        Color mOuterColor;
        bool mIsExtendable;
        List<Vector3> mVertices;
        RegionType mType;
        int mNumber;
        Material mMaterial;
    }
}

#endif