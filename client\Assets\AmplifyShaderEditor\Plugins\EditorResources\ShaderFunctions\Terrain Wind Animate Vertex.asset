%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Terrain Wind Animate Vertex
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=16103\n279;100;1002;701;934;430;1;True;False\nNode;AmplifyShaderEditor.PosVertexDataNode;5;-706,-228;Float;False;1;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;2;-477,-116;Float;False;Pos;4;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1;-253,-72;Float;False;return
    AnimateVertex(Pos,Normal,AnimParams)@;4;False;3;True;Pos;FLOAT4;0,0,0,0;In;;Float;True;Normal;FLOAT3;0,0,0;In;;Float;True;AnimParams;FLOAT4;0,0,0,0;In;;Float;WindAnimateVertex;True;False;0;3;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;6;-712,2;Float;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;3;-491,-27;Float;False;Normal;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;4;-512,143;Float;False;Anim
    Params;4;2;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-21,-86;Float;False;True;Output;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;2;0;5;0\nWireConnection;1;0;2;0\nWireConnection;1;1;3;0\nWireConnection;1;2;4;0\nWireConnection;3;0;6;0\nWireConnection;0;0;1;0\nASEEND*/\n//CHKSM=A559079493ADDD12988F7585C781D01E1B32CCBB"
  m_functionName: 
  m_description: "Uses Unity AnimateVertex(...) function \nAnimation parameters\nx
    = branch phase\ny = edge flutter factor\nz = primary factor\nw = secondary factor"
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives:
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 0
      LineValue: TerrainEngine.cginc
      GUIDToggle: 0
      GUIDValue: 
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
