﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    class GroundTileMakerSettingWindow : EditorWindow
    {
        class TextureItem
        {
            public string propertyName = "";
            public int textureResolution = 512;
            public bool initChannelData = true;
            public bool normalizeColor = true;
        }

        public void Show(GroundTileMaker maker)
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }

            mMaker = maker;
            mRuntimeAssetOutputFolder = MapModule.groundTileMakerDefaultRuntimeAssetFolder;
            mEditorAssetOutputFolder = MapModule.groundTileMakerDefaultEditorAssetFolder;
            mTileSize = MapModule.groundTileMakerDefaultTileSize;
            SetMaterial(AssetDatabase.LoadAssetAtPath<Material>(MapModule.groundTileMakerDefaultMaterialPath));
        }

        void OnEnable()
        {
        }

        void SetMaterial(Material newMtl)
        {
            if (newMtl != mMaterial)
            {
                mMaterial = newMtl;
                mTexturePropertyNames = Utils.GetTexturePropertyNames(mMaterial);
                mSelectedPropertyIndex = 0;

                if (mTextures.Count == 0)
                {
                    for (int i = 0; i < mTexturePropertyNames.Length; ++i)
                    {
                        if (mTexturePropertyNames[i] == MapModule.groundTileMakerDefaultMaskTextureName)
                        {
                            AddTextureItem(MapModule.groundTileMakerDefaultMaskTextureName);
                            break;
                        }
                    }
                }
            }
        }

        void OnGUI()
        {
            mTileSetName = EditorGUILayout.TextField("Tile Set Name", mTileSetName);
            mTileCount = Mathf.Clamp(EditorGUILayout.IntField("Tile Count", mTileCount), 1, 100);
            
            EditorGUILayout.BeginHorizontal();
            mRuntimeAssetOutputFolder = EditorGUILayout.TextField("Game Asset Output Folder", mRuntimeAssetOutputFolder);
            if (GUILayout.Button("Select", GUILayout.MaxWidth(60)))
            {
                mRuntimeAssetOutputFolder = EditorUtility.OpenFolderPanel("Select Game Asset Folder", "Assets", "");
                mRuntimeAssetOutputFolder = Utils.ConvertToUnityAssetsPath(mRuntimeAssetOutputFolder);
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            mEditorAssetOutputFolder = EditorGUILayout.TextField("Editor Asset Output Folder", mEditorAssetOutputFolder);
            if (GUILayout.Button("Select", GUILayout.MaxWidth(60)))
            {
                mEditorAssetOutputFolder = EditorUtility.OpenFolderPanel("Select Editor Asset Folder", "Assets", "");
                mEditorAssetOutputFolder = Utils.ConvertToUnityAssetsPath(mEditorAssetOutputFolder);
            }
            EditorGUILayout.EndHorizontal();
            mTileSize = Mathf.Max(EditorGUILayout.FloatField("Tile Size", mTileSize), 1.0f);
            var newMtl = EditorGUILayout.ObjectField("Material", mMaterial, typeof(Material), false, null) as Material;
            if (newMtl != null)
            {
                SetMaterial(newMtl);
                DrawTextureSelectionGUI();
            }
            else
            {
                EditorGUILayout.LabelField("Please select a material first!");
            }

            DrawTextures();

            if (GUILayout.Button("Create"))
            {
                if (!Directory.Exists(mEditorAssetOutputFolder))
                {
                    Directory.CreateDirectory(mEditorAssetOutputFolder);
                }

                if (!Directory.Exists(mRuntimeAssetOutputFolder))
                {
                    Directory.CreateDirectory(mRuntimeAssetOutputFolder);
                }

                string errorMsg = CheckValidation();
                if (string.IsNullOrEmpty(errorMsg))
                {
                    bool ok = true;
                    if (!Utils.IsFolderEmpty(mEditorAssetOutputFolder))
                    {
                        ok = EditorUtility.DisplayDialog("Warning", $"Directory {mEditorAssetOutputFolder} is not empty! all files in this folder will be deleted! are you sure?", "Yes", "No");
                    }

                    if (ok)
                    {
                        mMaker.Uninit();
                        int n = mTextures != null ? mTextures.Count : 0;
                        List<GroundTileMaker.MaskTextureSetting> settings = new List<GroundTileMaker.MaskTextureSetting>();
                        for (int i = 0; i < n; ++i)
                        {
                            var setting = new GroundTileMaker.MaskTextureSetting() { shaderPropertyName = mTextures[i].propertyName, resolution = mTextures[i].textureResolution, initChannelData = mTextures[i].initChannelData, normalizeColor = mTextures[i].normalizeColor };
                            settings.Add(setting);
                        }

                        string materialPath = AssetDatabase.GetAssetPath(mMaterial);
                        var lodMaterialSetting = new GroundTileMaker.GroundLODMaterialSetting(materialPath, settings);
                        mMaker.Init(mTileSetName, mTileCount, mTileSize, new List<GroundTileMaker.GroundLODMaterialSetting>() { lodMaterialSetting }, mRuntimeAssetOutputFolder, mEditorAssetOutputFolder, true, 0, true);

                        mMaker.Save();

                        Close();
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", errorMsg, "OK");
                }
            }
        }

        void DrawTextureSelectionGUI()
        {
            if (mTexturePropertyNames.Length > 0)
            {
                EditorGUILayout.BeginHorizontal();
                mSelectedPropertyIndex = EditorGUILayout.Popup("Texture Property", mSelectedPropertyIndex, mTexturePropertyNames);
                if (GUILayout.Button("Add"))
                {
                    AddTextureItem(mTexturePropertyNames[mSelectedPropertyIndex]);
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        void AddTextureItem(string propertyName)
        {
            for (int i = 0; i < mTextures.Count; ++i)
            {
                if (mTextures[i].propertyName == propertyName)
                {
                    EditorUtility.DisplayDialog("Error", "Texture is already added!", "OK");
                    return;
                }
            }
            var item = new TextureItem();
            item.initChannelData = MapModule.groundTileMakerDefaultInitChannelData;
            item.normalizeColor = MapModule.groundTileMakerDefaultNormalizeColor;
            item.propertyName = propertyName;
            item.textureResolution = MapModule.groundTileMakerDefaultMaskTextureSize;
            mTextures.Add(item);
        }

        void RemoveTextureItem(int index)
        {
            mTextures.RemoveAt(index);
        }

        void DrawTextures()
        {
            if (mTextures != null)
            {
                for (int i = 0; i < mTextures.Count; ++i)
                {
                    if (mTextures[i] != null)
                    {
                        EditorGUILayout.BeginVertical("GroupBox");
                        mTextures[i].textureResolution = EditorGUILayout.IntField("Texture Size", mTextures[i].textureResolution);
                        EditorGUILayout.TextField("Texture Property Name", mTextures[i].propertyName);
                        mTextures[i].initChannelData = EditorGUILayout.ToggleLeft("Init Channel Data", mTextures[i].initChannelData);
                        mTextures[i].normalizeColor = EditorGUILayout.ToggleLeft("Normalize Color", mTextures[i].normalizeColor);
                        if (GUILayout.Button("Remove"))
                        {
                            RemoveTextureItem(i);
                            break;
                        }
                        EditorGUILayout.EndVertical();
                    }
                }
            }
        }

        string CheckValidation()
        {
            if (mMaterial == null)
            {
                return "invalid material";
            }

            if (mTextures == null || mTextures.Count == 0)
            {
                return "invalid texture";
            }

            for (int i = 0; i < mTextures.Count; ++i)
            {
                if (mTextures[i] == null)
                {
                    return $"invalid texture {i}";
                }
                if (string.IsNullOrEmpty(mTextures[i].propertyName))
                {
                    return $"invalid texture {i} property name";
                }

                if (!mMaterial.HasProperty(mTextures[i].propertyName))
                {
                    return $"invalid texture {i} property name {mTextures[i].propertyName}";
                }

                if (!Mathf.IsPowerOfTwo(mTextures[i].textureResolution))
                {
                    return $"{mTextures[i].propertyName} texture size must be power of 2!";
                }
            }

            if (string.IsNullOrEmpty(mTileSetName))
            {
                return "invalid tileset name";
            }

            if (string.IsNullOrEmpty(mRuntimeAssetOutputFolder) || !Directory.Exists(mRuntimeAssetOutputFolder))
            {
                return "invalid game asset output folder";
            }

            if (string.IsNullOrEmpty(mEditorAssetOutputFolder) || !Directory.Exists(mEditorAssetOutputFolder))
            {
                return "invalid editor asset output folder";
            }

            return "";
        }

        Material mMaterial;
        List<TextureItem> mTextures = new List<TextureItem>();
        GroundTileMaker mMaker;
        string mTileSetName = "ground_tiles";
        string mRuntimeAssetOutputFolder;
        string mEditorAssetOutputFolder;
        string[] mTexturePropertyNames;
        float mTileSize = 180.0f;
        int mTileCount = 16;
        int mSelectedPropertyIndex = -1;
    }
}
#endif