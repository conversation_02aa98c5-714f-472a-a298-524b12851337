﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class DisableKeyboardDelete : MonoBehaviour
    {

    }

    [CustomEditor(typeof(DisableKeyboardDelete))]
    public class DisableKeyboardDeleteEditor : Editor
    {
        // NOTE: you can still delete GameObjects by right click and select delete in Hierarchy view

        protected virtual void OnEnable()
        {
            EditorApplication.hierarchyWindowItemOnGUI += OnHierarchyGUI;
        }

        protected virtual void OnDisable()
        {
            EditorApplication.hierarchyWindowItemOnGUI -= OnHierarchyGUI;
        }

        // disable the ability to delete GameObjects in Scene view
        protected virtual void OnSceneGUI()
        {
            InterceptKeyboardDelete();
        }

        // disable the ability to delete GameObjects in Hierarchy view
        protected virtual void OnHierarchyGUI(int instanceID, Rect selectionRect)
        {
            InterceptKeyboardDelete();
        }

        // intercept keyboard delete event
        void InterceptKeyboardDelete()
        {
            var e = Event.current;
            if (e.keyCode == KeyCode.Delete)
            {
                //e.Use(); // warning
                e.type = EventType.Used;
            }
        }
    }
}

#endif