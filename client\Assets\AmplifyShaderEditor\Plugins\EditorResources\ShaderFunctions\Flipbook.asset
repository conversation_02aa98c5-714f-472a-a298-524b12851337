%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Flipbook
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=14204\n487;595;979;423;2696.247;1459.301;4.894685;True;False\nNode;AmplifyShaderEditor.GetLocalVarNode;50;-816,96;Float;False;39;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ClampOpNode;42;-992,80;Float;False;3;0;FLOAT;0.0;False;1;FLOAT;0.0001;False;2;FLOAT;8.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;8;-128,-128;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;49;640,-112;Float;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleDivideOpNode;27;-608,0;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;9.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;35;-464,0;Float;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;44;-1168,160;Float;False;2;0;FLOAT;0.0;False;1;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-992,0;Float;False;Time;1;5;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FloorOpNode;10;160,-64;Float;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;24;-1200,80;Float;False;Start
    Frame;1;4;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;39;-464,-176;Float;False;totalFrames;-1;True;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;68;1408,-720;Float;False;Mip
    Mode;False;0;3;1;Auto;Bias;Level;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.OneMinusNode;38;-304,64;Float;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;40;-1376,160;Float;False;39;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleTimeNode;3;-1200,0;Float;False;1;0;FLOAT;1.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;9;16,-64;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0.0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;15;480,-224;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0.0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;7;-128,-256;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;14;-128,-384;Float;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;67;-304,160;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;5;-768,-96;Float;False;Rows;1;3;False;1;0;FLOAT;3.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;6;-624,-176;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;-768,-256;Float;False;Colums;1;2;False;1;0;FLOAT;3.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;29;-128,0;Float;False;FLOAT2;4;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;13;128,-384;Float;False;UV;2;1;False;1;0;FLOAT2;1,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;69;896,-512;Float;True;Property;_SamplerLevel;Sampler
    Level;0;0;Create;True;None;None;True;0;False;white;Auto;False;Object;-1;MipLevel;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleDivideOpNode;12;320,-288;Float;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;51;512,-896;Float;False;Tex;9;0;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;11;320,-160;Float;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;22;-768,0;Float;False;2;2;0;FLOAT;0.0;False;1;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;70;512,-512;Float;False;Mip
    Level;1;7;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;52;896,-896;Float;True;Property;_SamplerAuto;Sampler
    Auto;0;0;Create;True;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;57;896,-704;Float;True;Property;_SamplerBias;Sampler
    Bias;0;0;Create;True;None;None;True;0;False;white;Auto;False;Object;-1;MipBias;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0.0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1.0;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;55;512,-704;Float;False;Mip
    Bias;1;6;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;71;1654.729,-742.7292;Float;False;Use
    Texture;True;0;2;0;False;True;8;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;5;FLOAT;0.0;False;6;FLOAT;0.0;False;7;FLOAT;0.0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;0;896,-224;Float;False;True;UV;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;53;1882.438,-748.9789;Float;False;False;RGBA;0;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;47;896,-128;Float;False;False;U;2;False;1;0;FLOAT;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;62;-112,160;Float;False;False;Cur
    Frame;4;False;1;0;FLOAT;0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;48;896,-64;Float;False;False;V;3;False;1;0;FLOAT;0,0;False;1;FLOAT;0\nWireConnection;42;0;24;0\nWireConnection;42;2;44;0\nWireConnection;8;0;39;0\nWireConnection;8;1;5;0\nWireConnection;49;0;15;0\nWireConnection;27;0;22;0\nWireConnection;27;1;50;0\nWireConnection;35;0;27;0\nWireConnection;44;0;40;0\nWireConnection;2;0;3;0\nWireConnection;10;0;9;0\nWireConnection;39;0;6;0\nWireConnection;68;0;52;0\nWireConnection;68;1;57;0\nWireConnection;68;2;69;0\nWireConnection;38;0;35;0\nWireConnection;9;0;8;0\nWireConnection;9;1;29;0\nWireConnection;15;0;12;0\nWireConnection;15;1;11;0\nWireConnection;7;0;4;0\nWireConnection;7;1;5;0\nWireConnection;67;0;35;0\nWireConnection;67;1;50;0\nWireConnection;6;0;4;0\nWireConnection;6;1;5;0\nWireConnection;29;0;35;0\nWireConnection;29;1;38;0\nWireConnection;13;0;14;0\nWireConnection;69;0;51;0\nWireConnection;69;1;15;0\nWireConnection;69;2;70;0\nWireConnection;12;0;13;0\nWireConnection;12;1;7;0\nWireConnection;11;0;10;0\nWireConnection;11;1;7;0\nWireConnection;22;0;2;0\nWireConnection;22;1;42;0\nWireConnection;52;0;51;0\nWireConnection;52;1;15;0\nWireConnection;57;0;51;0\nWireConnection;57;1;15;0\nWireConnection;57;2;55;0\nWireConnection;71;1;68;0\nWireConnection;0;0;15;0\nWireConnection;53;0;71;0\nWireConnection;47;0;49;0\nWireConnection;62;0;67;0\nWireConnection;48;0;49;1\nASEEND*/\n//CHKSM=312D1CE7E23C444F9583CB6B462131FEE8B66987"
  m_functionName: 
  m_description: This node generates either UVs or a final color which scroll through
    a texture sheet. Make sure your texture wrap mode is set to repeat.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_nodeCategory: 0
  m_customNodeCategory: 
  m_previewPosition: 1
