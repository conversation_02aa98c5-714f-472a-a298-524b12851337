﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //地图上物体视图的管理,不暴露给lua接口
    public class MapView
    {
        public MapView(Map map, Vector3 viewCenter, MapCameras camera, config.BackgroundSetting setting, bool isEditorMap)
        {
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -1");
            mMap = map;
            string mapName = mMap.name;
            if (isEditorMap)
            {
                mapName = "MapRoot";
            }
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -2 {mapName}");

            mRoot = new GameObject(mapName);
            if (camera == null)
            {
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -3");
                var cameraObj = GameObject.Find(MapCoreDef.MAP_CAMERA_NAME);
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -4");
                if (cameraObj == null)
                {
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -5");
                    Debug.Assert(false, "todo");
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -6");
                    //cameraObj = new GameObject(MapCoreDef.MAP_CAMERA_NAME);
                    //camera = cameraObj.AddComponent<Camera>();
                    //camera.transform.SetParent(mRoot.transform, true);
                }
            }
            mCamera = camera;
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -7");
            mPoolObjectRoot = new GameObject("Pool Objects");
            Utils.HideGameObject(mPoolObjectRoot);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -8");
            CreateViewCenter(viewCenter);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -9");
            mReusableGameObjectPool = new GameObjectPool(LoadGameObject, ReleaseGameObject, (str) => { 
             return MapModuleResourceMgr.Exists(str);
            });
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -10");
            mModelTemplateRenderToTextureGameObjectManager = new ModelTemplateRenderToTextureGameObjectManager(mRoot);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -5 -11");

            mMap.view = this;

#if UNITY_EDITOR
            CreateBackground(viewCenter, setting);
#endif
        }

        //返回相机能看到的视野包围框的大小
        public Vector2 GetViewportSizeFromScreen()
        {
            return Utils.GetViewportSizeFromScreen(mCamera.firstCamera);
        }

        //内部使用,初始化函数
        public void PostInitialize()
        {
        }

        //清理数据
        public virtual void OnDestroy()
        {
#if UNITY_EDITOR
            if (mBackground != null) {
                Utils.DestroyObject(mBackground.GetComponent<MeshRenderer>().sharedMaterial);
                Utils.DestroyObject(mBackground);
                mBackground = null;

                UnityEngine.Object.DestroyImmediate(mDummyTransparentTexture);
                mDummyTransparentTexture = null;
            }
#endif

            mReusableGameObjectPool.OnDestroy();

            Utils.DestroyObject(mViewCenter);
            mViewCenter = null;

            Utils.DestroyObject(mRoot);
            mRoot = null;

            mModelTemplateRenderToTextureGameObjectManager.OnDestroy();
            mModelTemplateRenderToTextureGameObjectManager = null;

            Utils.DestroyObject(mPoolObjectRoot);
            mPoolObjectRoot = null;
        }

        public void SetVisible(bool visible)
        {
            mRoot.SetActive(visible);
        }

        public virtual void SetActive(bool active)
        {
            root.SetActive(active);
            mViewCenter.SetActive(active);
        }

        public void SetViewCenterActive(bool active)
        {
            mViewCenter.SetActive(true);
        }

        //内部使用
        public void MoveViewport(Vector3 offset, bool moveCamera)
        {
            if (moveCamera)
            {
                mCamera.transform.position = mCamera.transform.position + offset;
            }
            mViewCenter.transform.position = mViewCenter.transform.position + new Vector3(offset.x, 0, offset.z);
        }

        void CreateViewCenter(Vector3 viewCenterPos)
        {
            Debug.Assert(mViewCenter == null);
            mViewCenter = GameObject.CreatePrimitive(UnityEngine.PrimitiveType.Sphere);
            mViewCenter.name = "View Center";
            mViewCenter.transform.SetParent(mRoot.transform, true);
            mViewCenter.SetActive(false);
            mViewCenter.transform.position = viewCenterPos;
            var drawBounds = mViewCenter.AddComponent<DrawBounds>();
            drawBounds.worldSpace = true;
            drawBounds.color = Color.black;
        }

        public void LoadGameObject(string path, Action<GameObject> callBack)
        {
            GameObject gameObject = null;
            if (!string.IsNullOrEmpty(path))
            {
                if (path == MapCoreDef.DUMMY_OBJECT_NAME)
                {
                    gameObject = new GameObject("Dummy");
                    callBack?.Invoke(gameObject);
                }
                else
                {
                    MapModuleResourceMgr.LoadGameObjectAsync(path, null, (_path,gameObject) =>
                    {
                        if (gameObject != null)
                        {
                            callBack?.Invoke(GameObject.Instantiate(gameObject));
                        }
                        else
                        {
                            Debug.Log($"资源：{path} 找不到！");
                        }
                    });
                     
                }
            }
            else
            {
                callBack?.Invoke(gameObject);
            }
          
        }


        private void ReleaseGameObject(string path)
        {
           MapModuleResourceMgr.UnloadAsset(path);
        }

        //public void LoadGameObjectAsync(string path, string defaultAsset, System.Action<GameObject> action)
        //{
        //    GameObject gameObject = null;
        //    if (!string.IsNullOrEmpty(path))
        //    {
        //        if (path == MapCoreDef.DUMMY_OBJECT_NAME)
        //        {
        //            gameObject = new GameObject("Dummy");
        //            if (gameObject == null)
        //            {
        //                gameObject = CreateErrorModel(path);
        //            }
        //            action.Invoke(gameObject);
        //        }
        //        else
        //        {
        //            MapModuleResourceMgr.LoadAssetInstanceAsync(path, defaultAsset, (p, o)=>
        //            {
        //                if (o == null)
        //                {
        //                    o = CreateErrorModel(path);
        //                }
        //                action.Invoke(o);
        //            });
        //        }
        //    }
        //}

        GameObject CreateErrorModel(string path)
        {
            GameObject gameObject = null;
            if (mMap.isEditorMode)
            {
                gameObject = new GameObject(path);
                var childObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                childObj.transform.localScale = Vector3.one * 10.0f;
                childObj.transform.SetParent(gameObject.transform);
            }
            else
            {
                gameObject = new GameObject(path);
            }
            return gameObject;
        }

        //internal use
        public void AddMapObjectView(int id, MapObjectView view)
        {
            Debug.Assert(mMapObjectViews.ContainsKey(id) == false);
            mMapObjectViews[id] = view;
        }

        //内部使用
        public void RemoveMapObjectView(int id, MapObjectView view)
        {
            Debug.Assert(mMapObjectViews.ContainsKey(id) == true);
            mMapObjectViews.Remove(id);
        }

        public MapObjectView GetMapObjectView(int id)
        {
            MapObjectView view = null;
            mMapObjectViews.TryGetValue(id, out view);
            return view;
        }

        Mesh CreateBoundsMesh(float width, float height, bool offset)
        {
            var mesh = new Mesh();
            Vector3[] positions = new Vector3[4];
            int[] indices = new int[8]{
            0,1,1,2,2,3,3,0,
            };
            float ox = 0;
            float oz = 0;
            if (offset)
            {
                ox = -width * 0.5f;
                oz = -height * 0.5f;
            }
            positions[0] = new Vector3(ox, 0, oz);
            positions[1] = new Vector3(ox + width, 0, oz);
            positions[2] = new Vector3(ox + width, 0, oz + height);
            positions[3] = new Vector3(ox, 0, oz + height);

            mesh.vertices = positions;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            mesh.RecalculateBounds();

            return mesh;
        }

        GameObject CreateBounds(float width, float height, Color color, bool offset)
        {
            var bounds = new GameObject("XZSquareBounds");
            Utils.HideGameObject(bounds);
            var meshFilter = bounds.AddComponent<MeshFilter>();
            var meshRenderer = bounds.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Color"));
            mtl.color = color;
            meshRenderer.sharedMaterial = mtl;
            meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            var mesh = CreateBoundsMesh(1, 1, offset);
            meshFilter.sharedMesh = mesh;
            bounds.SetActive(true);
            bounds.transform.localScale = new Vector3(width, 1, height);
            return bounds;
        }

        public void ClearCachedGameObjects(string prefabPath)
        {
            mReusableGameObjectPool.Clear(prefabPath);
        }

        public void Resize(float newWidth, float newHeight)
        {
        }

#if UNITY_EDITOR
        void CreateBackground(Vector3 viewCenter, config.BackgroundSetting setting)
        {
            if (mMap.isEditorMode)
            {
                Debug.Assert(mBackground == null);
                mBackground = new GameObject("Background");
                mBackground.AddComponent<DisableKeyboardDelete>();
                mBackground.transform.SetParent(mRoot.transform);
                mBackground.transform.position = setting.position;
                mBackground.transform.localScale = setting.scale;
                var renderer = mBackground.AddComponent<MeshRenderer>();
                var filter = mBackground.AddComponent<MeshFilter>();
                var mesh = new Mesh();
                mesh.vertices = new Vector3[] {
                new Vector3(-0.5f, 0, -0.5f),
                new Vector3(-0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, -0.5f),
            };
                mesh.uv = new Vector2[] {
                new Vector2(0, 0),
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };
                mesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
                mesh.RecalculateBounds();
                filter.sharedMesh = mesh;
                renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/TextureTransparent"));
                renderer.sharedMaterial.renderQueue = 1900;
                renderer.sharedMaterial.color = new Color(1, 1, 1, 1);
                var texture = MapModuleResourceMgr.LoadTexture(setting.texturePath);
                if (texture == null)
                {
                    if (mDummyTransparentTexture == null)
                    {
                        mDummyTransparentTexture = new Texture2D(1, 1, TextureFormat.RGBA32, false);
                        mDummyTransparentTexture.SetPixels(new Color[] { new Color(0, 0, 0, 0)});
                        mDummyTransparentTexture.Apply();
                    }
                    texture = mDummyTransparentTexture;
                }
                renderer.sharedMaterial.mainTexture = texture;
            }
        }

        public void SetBackgroundTexture(Texture2D texture)
        {
            mBackground.GetComponent<MeshRenderer>().sharedMaterial.mainTexture = texture;
        }
#endif

        public GameObject root { get { return mRoot; } }
        public GameObject viewCenter { get { return mViewCenter; } }
        public GameObject background { get { return mBackground; } }
        public MapCameras camera { get { return mCamera; } set { mCamera = value; } }
        public GameObjectPool reusableGameObjectPool { get { return mReusableGameObjectPool; } }
        public GameObject poolObjectRoot { get { return mPoolObjectRoot; } }
        public ModelTemplateRenderToTextureGameObjectManager modelTemplateRenderToTextureGameObjectManager { get { return mModelTemplateRenderToTextureGameObjectManager; } }
        //地图上所有物体的view
        protected Dictionary<int, MapObjectView> mMapObjectViews = new Dictionary<int, MapObjectView>();
        //大地图使用的相机
        protected MapCameras mCamera;
        //地图的根gameobject,地图上所有物体都挂接在它的子树上
        protected GameObject mRoot;
        //调试用,地图视野的中心点game object
        protected GameObject mViewCenter;
        protected GameObject mBackground;
#if UNITY_EDITOR
        Texture2D mDummyTransparentTexture;
#endif
        //地图对象的game object pool
        GameObjectPool mReusableGameObjectPool;
        GameObject mPoolObjectRoot;
        ModelTemplateRenderToTextureGameObjectManager mModelTemplateRenderToTextureGameObjectManager;
        Map mMap;
    }
}
