﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //相机的统计信息
    public class CameraStatistics
    {
        class Item
        {
            public Vector3 position;
            public float time;
        }

        public CameraStatistics(int itemCountInHistory)
        {
            Debug.Assert(itemCountInHistory > 1);
            mPositionHistory = new Item[itemCountInHistory];
            for (int i = 0; i < itemCountInHistory; ++i)
            {
                mPositionHistory[i] = new Item();
            }
        }

        public void Add(Vector3 position)
        {
            int itemCountInHistory = mPositionHistory.Length;
            mPositionHistory[mNextEmptySlot % itemCountInHistory].position = position;
            mPositionHistory[mNextEmptySlot % itemCountInHistory].time = Time.time;

            mHorizontalAverageSpeed = 0;
            mVerticalAverageSpeed = 0;
            //计算平均速度
            int startIndex = Mathf.Max(mNextEmptySlot - itemCountInHistory + 1, 0);
            int n = 0;
            for (int i = startIndex; i < mNextEmptySlot; ++i)
            {
                int cur = i % itemCountInHistory;
                int next = (i + 1) % itemCountInHistory;
                Vector3 deltaPos = mPositionHistory[next].position - mPositionHistory[cur].position;
                Vector2 deltaPosXZ = new Vector2(deltaPos.x, deltaPos.z);
                float deltaTime = mPositionHistory[next].time - mPositionHistory[cur].time;
                mHorizontalAverageSpeed += deltaPosXZ.magnitude / deltaTime;
                mVerticalAverageSpeed += Mathf.Abs(deltaPos.y) / deltaTime;
                ++n;
            }

            if (n > 0)
            {
                mHorizontalAverageSpeed /= n;
                mVerticalAverageSpeed /= n;
            }

            ++mNextEmptySlot;
        }

        public float horizontalAverageSpeed { get { return mHorizontalAverageSpeed; } }
        public float verticalAverageSpeed { get { return mVerticalAverageSpeed; } }
        public Vector3 lastPosition
        {
            get {
                if (mNextEmptySlot > 1)
                {
                    return mPositionHistory[(mNextEmptySlot - 1) % mPositionHistory.Length].position;
                }
                return mPositionHistory[mNextEmptySlot % mPositionHistory.Length].position;
            }
        }

        int mNextEmptySlot = 0;
        //相机的历史位置
        Item[] mPositionHistory;
        //最近n帧的水平平均速度
        float mHorizontalAverageSpeed;
        //最近n帧的垂直平均速度
        float mVerticalAverageSpeed;
    }
}
