﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        int mVersion;
        string mFilePath;

        public void Load(string filePath)
        {
            var loadSuccess = false;

            var path = Utils.ConvertToUnityAssetsPath(filePath);
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream != null)
            {
                Uninit();

                ClearSceneGameObjects();

                mFilePath = filePath;

                BinaryReader reader = new BinaryReader(stream);

                mVersion = reader.ReadInt32();

                LoadSetting(reader);

            	reader.Close();

                loadSuccess = true;
            }

            if (!loadSuccess)
            {
                EditorUtility.DisplayDialog("Error", $"Load {filePath} failed!", "OK");
            }
        }

        void LoadSetting(BinaryReader reader)
        {
            int tileCount = 16;
            if (mVersion >= 9)
            {
                tileCount = reader.ReadInt32();
            }
            int paintMode = reader.ReadInt32();
            string tileSetName = Utils.ReadString(reader);
            float tileSize = reader.ReadSingle();
            int nLODs = 1;
            int currentLOD = 0;
            if (mVersion >= 5)
            {
                nLODs = reader.ReadInt32();
                currentLOD = reader.ReadInt32();
            }
            List<string> materialPaths = new List<string>();
            for (int i = 0; i < nLODs; ++i)
            {
                string materialGUID = Utils.ReadString(reader);
                string materialPath = AssetDatabase.GUIDToAssetPath(materialGUID);
                materialPaths.Add(materialPath);
            }

            string runtimeAssetOutputFolder = Utils.ReadString(reader);
            string editorAssetOutputFolder = Utils.ReadString(reader);
            int channel = reader.ReadInt32();
            float strength = reader.ReadSingle();
            float strengthStep = reader.ReadSingle();
            int brushSize = reader.ReadInt32();
            int brushSizeStep = reader.ReadInt32();
            float edgeWidth = reader.ReadSingle();
            int maskTextureIndex = reader.ReadInt32();
            bool showEdge = reader.ReadBoolean();
            bool showEdgeGuideline = reader.ReadBoolean();
            int operation = reader.ReadInt32();

            Vector3 cameraPosition = new Vector3((180 * 5) / 2, 30, (180 * 5) / 4);
            Quaternion cameraRotation = Quaternion.Euler(new Vector3(45, 0, 0));
            if (mVersion >= 2)
            {
                bool hasCamera = reader.ReadBoolean();
                if (hasCamera)
                {
                    cameraPosition = Utils.ReadVector3(reader);
                    cameraRotation = Utils.ReadQuaternion(reader);
                }
            }

            bool randomBrushRotation = false;
            float fixedBrushRotationAngle = 0;
            if (mVersion >= 6)
            {
                randomBrushRotation = reader.ReadBoolean();
                fixedBrushRotationAngle = reader.ReadSingle();
            }

            bool shareMesh = false;
            bool packTexture = false;
            Shader atlasShader = null;
            if (mVersion >= 7)
            {
                shareMesh = reader.ReadBoolean();
                packTexture = reader.ReadBoolean();
                string shaderGuid = Utils.ReadString(reader);
                string shaderPath = AssetDatabase.GUIDToAssetPath(shaderGuid);
                atlasShader = AssetDatabase.LoadAssetAtPath<Shader>(shaderPath);
            }

            Material extraSubmeshMaterial = null;
            bool useExtraSubmeshMaterialAsFirstMaterial = true;
            if (mVersion >= 8)
            {
                useExtraSubmeshMaterialAsFirstMaterial = reader.ReadBoolean();
                string guid = Utils.ReadString(reader);
                string path = AssetDatabase.GUIDToAssetPath(guid);
                extraSubmeshMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
            }

            List<List<MaskTextureSetting>> lodMaskTextureSettings = new List<List<MaskTextureSetting>>();
            for (int lod = 0; lod < nLODs; ++lod)
            {
                int nMaskTextures = reader.ReadInt32();
                List<MaskTextureSetting> textureSettings = new List<MaskTextureSetting>(nMaskTextures);
                lodMaskTextureSettings.Add(textureSettings);
                for (int i = 0; i < nMaskTextures; ++i)
                {
                    var setting = new MaskTextureSetting();
                    setting.initChannelData = reader.ReadBoolean();
                    setting.normalizeColor = reader.ReadBoolean();
                    setting.resolution = reader.ReadInt32();
                    setting.shaderPropertyName = Utils.ReadString(reader);
                    if (mVersion >= 7)
                    {
                        setting.uvChannel = (GroundTileMaker.AtlasUVChannel)reader.ReadInt32();
                    }
                    textureSettings.Add(setting);
                }
            }

            List<GroundLODMaterialSetting> lodMaterialSettings = new List<GroundLODMaterialSetting>();
            for (int i = 0; i < nLODs; ++i)
            {
                lodMaterialSettings.Add(new GroundLODMaterialSetting(materialPaths[i], lodMaskTextureSettings[i]));
            }
            Init(tileSetName, tileCount, tileSize, lodMaterialSettings, runtimeAssetOutputFolder, editorAssetOutputFolder, false, fixedBrushRotationAngle, randomBrushRotation);

            LoadTiles(reader, (PaintMode)paintMode, tileCount);

            this.channel = channel;
            this.strength = strength;
            this.brushSize = brushSize;
            this.brushSizeStep = brushSizeStep;
            this.strengthStep = strengthStep;
            this.packTextures = packTexture;
            this.shareMesh = shareMesh;
            this.atlasShader = atlasShader;
            this.extraSubmeshMaterial = extraSubmeshMaterial;
            this.useExtraSubmeshMaterialAsFirstMaterial = useExtraSubmeshMaterialAsFirstMaterial;
            mEdgeWidth = edgeWidth;
            this.maskTextureIndex = maskTextureIndex;
            this.showEdge = showEdge;
            this.showGuideline = showEdgeGuideline;
            SetOperation((Operation)operation);

            if (mCamera != null)
            {
                mCamera.transform.position = cameraPosition;
                mCamera.transform.rotation = cameraRotation;
            }

            SetCurrentLOD(currentLOD);
        }

        void LoadTiles(BinaryReader reader, PaintMode mode, int tileCount)
        {
            //加载完后删除无效的贴图
            HashSet<string> validTexturePaths = new HashSet<string>();

            mInstances = new GroundTileInstance[10, 10];
            mTiles = new GroundTileTemplate[tileCount];

            int nLODs = 1;
            if (mVersion >= 5)
            {
                nLODs = reader.ReadInt32();
            }

            bool fix = false;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                if (mTiles[i] == null)
                {
                    mTiles[i] = new GroundTileTemplate();
                }
                int variationCount = 1;
                if (mVersion >= 3)
                {
                    variationCount = reader.ReadInt32();
                }

                for (int v = 0; v < variationCount; ++v)
                {
                    string variationName = "";
                    if (mVersion >= 4)
                    {
                        variationName = Utils.ReadString(reader);
                    }

                    bool validVariation = true;
                    var variation = new GroundTileVariation(variationName, ++mNextVariationInstanceID);

                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        var maskTextureSetting = mLODMaterialSettings[lod].maskTextureSetting;
                        List<MaskTexture> maskTextures = new List<MaskTexture>();
                        for (int t = 0; t < maskTextureSetting.Count; ++t)
                        {
                            var maskTexture = new MaskTexture();
                            maskTextures.Add(maskTexture);
                            int resolution = maskTextureSetting[t].resolution;
                            string maskTexturePath = Utils.ReadString(reader);

                            validTexturePaths.Add(maskTexturePath);

                            string textureVarName = MapCoreDef.GetGroundTileVariationName(maskTexturePath);
                            if (textureVarName != variationName)
                            {
                                Debug.Assert(false, $"{textureVarName}, {variationName}");
                            }

                            if (maskTexturePath.EndsWith(".png"))
                            {
                                fix = true;
                            }
                            maskTexture.shaderPropertyName = maskTextureSetting[t].shaderPropertyName;
                            maskTexture.texture = AssetDatabase.LoadAssetAtPath<Texture2D>(maskTexturePath);
                            if (maskTexture.texture == null)
                            {
                                validVariation = false;
                                break;
                            }
                            else
                            {
                                maskTexture.textureData = maskTexture.texture.GetPixels();
                                maskTexture.beforePaintingTextureData = (Color[])maskTexture.textureData.Clone();
                            }
                        }

                        if (!validVariation)
                        {
                            variation.OnDestroy(true);
                            variation = null;
                        }
                        else
                        {
                            var tileLOD = CreateGroundTileLOD(i, lod, variationName, maskTextures);
                            variation.AddLOD(tileLOD);
                        }
                    }

                    if (variation != null)
                    {
                        mTiles[i].variations.Add(variation);
                    }
                }
            }

            if (fix)
            {
                Debug.Assert(false, "should not fix!");
            }

            //remove unused textures
            ClearUnusedTextures(validTexturePaths);

            AssignEdgeID();

            SetPaintMode(mode);
        }

        void ClearUnusedTextures(HashSet<string> usedTextures)
        {
            string folder = Utils.GetFolderPath(mFilePath);
            var enumerator = Directory.EnumerateFiles(folder, "*.tga", SearchOption.TopDirectoryOnly);
            foreach (var filePath in enumerator)
            {
                var validPath = filePath.Replace('\\', '/');
                validPath = Utils.ConvertToUnityAssetsPath(validPath);
                if (!usedTextures.Contains(validPath))
                {
                    AssetDatabase.DeleteAsset(validPath);
                }
            }
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }
    }
}

#endif