%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb281ee3bf0b054c82ac2347e9e782c, type: 3}
  m_Name: preload_world_merge_default
  m_EditorClassIdentifier: 
  m_GroupName: preload_world_merge_default
  m_Data:
    m_SerializedData: []
  m_GUID: 4d47416455e6e67458393dabab1ccc41
  m_SerializeEntries:
  - m_GUID: 02794684e9696cf4fae979f1f55148d5
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 05dbc26c34c63684d9302c4ed5c0bb42
    m_Address: Assets/Res/map/Main/mapData1.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 082d695a9f691684184406d5d6cdf0fc
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 08b1abe02cb5b2d49b3e844643c77252
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_gozilla_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 0de14e52c0313654d8652fa5b1012d86
    m_Address: Assets/k1/Res/Map/Troop/Formation/PlayerTroopMarch.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 0ff051b1b7d3e894cb9703244a864a10
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 1657a5d99a67ddf49a5ae6e9bc06d77d
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_PumkinCatapult_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 187560bee769a7542bf3edd64a33c8f9
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 1de20e0342ecb59489cc812cef8da594
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 2208dcfcaae7a99429a50e79ba67b8c2
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 2622f42e5c9c070429ae59dec02f7d9b
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_ground.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 26421519f09ff354ab4ead2e8d0cce81
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 270a5695c0d8cbb42848f1fc65aa26bb
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 2bfece1e89d5f68439d3952483bf220e
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 2e95813ea724b184b83eec1b506df6ba
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 2f15c86f58af9624ba909ec8e7d5f0c7
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 32341bb613fc5854a9db07882dcd97e3
    m_Address: Assets/k1/Res/Map/Territory/Material/territory_line_p3_final.mat
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 33329730dc5764612a41b798549cac1c
    m_Address: Assets/k1/Res/Map/Troop/Formation/PlayerTroopConvoy.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 336916b8b12932842a05e95a264167d6
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 361f6bae28f88094886ca72996a909ea
    m_Address: Assets/Res/Combat/Data/SkillActs/move_Armor_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 382dfe21c35ad8749a292ebeaa44b6bb
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 3ce7cfd26a1561949a22319bdc054ed4
    m_Address: Assets/Res/Combat/Data/SkillActs/s_horde_skill_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 3d1657c084b9feb47b970c15ba2c01f0
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 3e15855c73307164baf406ac9017ca3c
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 3e30111c3f326224f9243588353a4ab9
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 40a2a8966bb835348a3223e7eb00304a
    m_Address: Assets/Res/map/Main/Splines.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 40af79744ebe4a745abe87785979dbc5
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 41c4e0a30672b924883ebec914ed367c
    m_Address: Assets/Res/Combat/Data/SkillActs/s_horde_skill_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 428edd421d876134eb060953ed1691a0
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 45fc2e6a2d7b55e4c89cdcacf725ca8a
    m_Address: Assets/k1/Res/Map/Troop/Formation/NPCTroopAttack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 4643a52ec62e13f4d9785ea04c1cd209
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 46f623c75e798d544a1aabb481620044
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 4c685e9ebd2cbd948bf1945fa3e2aa33
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 4c6e6753196ec4907b4c9f26115f5eaa
    m_Address: Assets/k1/Res/Map/Troop/MarchLine/Material/DragLineTroop.mat
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 4f1f7eb70a0e5e844b0e90cbeae42955
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 514be4c0021ff434ba52d57842d638eb
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_leader_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 530d50c4c985927428f384883089a98f
    m_Address: Assets/Res/Track/t_attack_soldier_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 531f5b961b7aed8498ba60216370c0f8
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_gozilla_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 54d46bb7e9ceead42855cc043768e029
    m_Address: Assets/Res/map/obstacle/obstacle_material.mat
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 557e5bbfce276ad4195f80433eab8c68
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 5bb0f7e0799b08245a345c543542085f
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 5dcb399aac32cc94da3ecb16f5eced64
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_Anubis_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 5e7c3dd13eca6c94c84e8d7a3a61191f
    m_Address: Assets/Res/Combat/Data/SkillActs/move_PumkinCatapult_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 5e7cc1dc87b400c4a8b24d6fa1efd148
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 5f7ac85ee267e8441a6e2e2baa6f5ec5
    m_Address: Assets/Res/Combat/Data/SkillActs/s_lightning_test.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 60fb0ff763ac8e44a8a177d532f8573c
    m_Address: Assets/k1/Res/Map/EntityLod/ClassicNpcTroopLod-1-Offset.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 612857fdcb7562d4db41b4a397a78dd0
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 6176e0d2810c9f14caec0293d7c83f1c
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_shark_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 65b606c94c4443d46adba74f3c726374
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 6657e9ca4427eec40ac84fd249f44f59
    m_Address: Assets/Res/map/Main/maplist1.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 66f810666744d5c4386d5db6b568ac52
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 670db00eae8b4184591ca3bf93760e5c
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 681df57b45c0c29e47a0f999010bd1b4
    m_Address: Assets/k1/Res/Effect/Prefab/TeleportCityBkg.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 6a3791db60cd3734b849556c6c83a1ee
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 6d592e56ecfcd8b4fb9ea17b7a533798
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 6f8f34fe648173444b15af412f7c96e8
    m_Address: Assets/Res/Track/Missile_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 7169f1b2ff14d1a4094c79ef01dec12e
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_07a.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 7204a25afdf92114c9b14056a33f3ecb
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_10.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 76830fa3fdf262a41b61efdd225f6fa8
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 79d274dd1d390604897b9ab924f3cbf4
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_10.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 7f803f1f00054d642a9ec15f50d9348e
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 801d61ceac9fd074b9dba460f2db334e
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 8022c53c28d8fb34a92f3a99a1e02825
    m_Address: Assets/Res/Combat/Data/SkillActs/s_rifle_attack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 84ffd5a8078323c47948c92376365612
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_phoenix_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 8509d8c5bc0319f4d81a72f931b549ab
    m_Address: Assets/Res/Combat/Data/SkillActs/s_horde_skill_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 864af7b1b622a0e43973b65fcb8f8280
    m_Address: Assets/Res/Combat/Data/SkillActs/Stunt_91004.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 886701924223fe64e8984616281fc353
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 8b70270a2825de74faab18ae3f5c393d
    m_Address: Assets/Res/Track/t_attack_soldier_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 8e731efffbfe50140875838f4ad28a6f
    m_Address: Assets/Res/Combat/Data/SkillActs/s_defeat.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 92e9094432ef7254f8eec239a0813ffe
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 963003d41f6e74b4c9f5b68b53d8527b
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 983754845be60c04faccd58374f77409
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_air.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 99af6825f27f0824f99bc5d3b3578881
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 9a030de4275d2cb47b0b37996bf590b5
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 9cdf8a0a5ed46104ab316f79b897175c
    m_Address: Assets/k1/Res/Map/EntityLod/EliteNpcTroopLod-1-Offset.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 9ee50091d39c3f44ba6a8f82cb3ade39
    m_Address: Assets/Res/Track/t_attack_soldier_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 9fcf52574dd5af846b9a3bab702c6655
    m_Address: Assets/Res/Combat/Data/SkillActs/s_unit_dead.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a016af6759c4ff549a24137bc58011f5
    m_Address: Assets/Res/map/Main/mapconfig.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a17436a6fc2d7ff4da9e7464115994ef
    m_Address: Assets/Res/Combat/Data/SkillActs/s_horde_skill_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a1d374e064f306b458db4657e2761c06
    m_Address: Assets/Res/Combat/Data/SkillActs/light_92104.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a26bb59763c517d4088a946e9ecc91aa
    m_Address: Assets/Res/Combat/Data/SkillActs/s_horde_skill_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a454043f338c9264da3098a2c2cb81d9
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_treeman_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a54c1bd0d2dbebb43bf9ae0477671fc1
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: a7d07cbbbb6845f40b72411dad592daa
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_01_bak.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: aae6ec7113cddc847a4c64d92c3df45a
    m_Address: Assets/Res/Combat/Data/SkillActs/s_skill_ready.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ab8732b55c0ebb4469416f36007b8a50
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ac6231b50133f634ab2d14447d7a8985
    m_Address: Assets/Res/Combat/Data/SkillActs/s_shotgun_attack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: aca8d1e75b5a3e942b8ab4c2afe99ade
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: af32f278eca515d4abdfe81bd4245e2c
    m_Address: Assets/k1/Res/Map/plugin_list.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b0335139bac4c0d46b215ca0cea7bf28
    m_Address: Assets/Res/Combat/Data/SkillActs/s_infantry_attack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b03a5884da3b55e45bc9115cd1133178
    m_Address: Assets/Res/map/Main/OptimizedTiles/OptimizedTileData.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b24888c5326bff149a1e12dc477c19e4
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_06.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b3e5133ce65c503449b4bedf0866f28e
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_magicsword_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b428d9edaee1db042b00a9722bdd0a18
    m_Address: Assets/Res/Combat/Data/SkillActs/move_Anubis_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b6c1b776e156a3e4b96cddae84c3c360
    m_Address: Assets/Res/Track/track_assets.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: b814535086f7ff14e92796f8febfa857
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: baecfaacab19ccc49ba7a381e62e7016
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: c097af2466b40d64bb4f2f70b0c91b62
    m_Address: Assets/Res/Track/StraightLine.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: c0f718df8dd4ae54492716c332a6df06
    m_Address: Assets/Res/Combat/Data/SkillActs/s_skill_test.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: c2920c7dee939734482b85777ecb053b
    m_Address: Assets/Res/Combat/Data/ActFilter.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: c5b02439ca55dfe4eab7351b30789676
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: c870eda98afb4b046a93142176249eba
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ca53e7837800b9d48a84848f284f591a
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_horse_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: cb7fa78cb924f8944b2c0d35152f1bba
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_04.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: cc26f1de93190e44d8a74addd70d655d
    m_Address: Assets/Res/Track/t_attack_soldier_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d01715b77b0b28c49975ab810d243ffd
    m_Address: Assets/k1/Res/Map/Troop/Formation/NPCTroopNormal.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d029b5e1ad5ee5d45876da2be78d3f39
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d0dba32f806f03143a4caf1325758670
    m_Address: Assets/k1/Res/Map/CityBuilding/GoldOre/GoldOre_LV1.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d1e153090d62c5c42908820ecb9b5448
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d1ed6e8961e58eb4b8b7852caba9eaad
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d54269022d354884daab8356e0cff6a0
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_enemy_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: d7e8d9726754eca4a9367e988b1abe59
    m_Address: Assets/Res/Combat/Data/SkillActs/s_archer_attack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: da88dca4026880542b40c2c3d703832c
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_soldier_07b.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: df519811e3349104eb78a2bb3e897b26
    m_Address: Assets/Res/Combat/Data/SkillActs/s_victory.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: dfe84ad6c9d0bfe4eb1eb80a7f3341f8
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_05.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e0dce171bd6e2fd4889d9612c7a617b6
    m_Address: Assets/k1/Res/Map/Troop/MarchLine/Material/MarchLineTroop.mat
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e0e5875e0746d8748b82ea8989ebe0c2
    m_Address: Assets/Res/Combat/Data/SkillActs/s_death_soldier_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e3eab6c1db591b947bbda650937055d3
    m_Address: Assets/Res/Combat/Data/SkillActs/s_attack_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e71342abcf63a804393e627322bc48e5
    m_Address: Assets/Res/Combat/Data/SkillActs/s_skill_003.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e769e492a95e8764f8f6e76ee0487c19
    m_Address: Assets/Res/map/MapRoot.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e7cb5a0af88c05d44a6bb5fc07d69c1e
    m_Address: Assets/k1/Res/Map/Troop/MarchLine/Material/MarchLineConvoy.mat
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e8f0f074d844b314797e24913a905a59
    m_Address: Assets/Res/map/Main/NavMesh1.bytes
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ea2b8b706684526449da837eb6f3c826
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_fire_08.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: eb9e8967002ba144f9b54f80fad54cf9
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_toxic_09.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ec6ff4d3e8b846b47adbfd1149a011ad
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_ice_07.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: efe2724bd94b632438f579ada94cb9bc
    m_Address: Assets/k1/Res/Map/Troop/Formation/PlayerTroopAttack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f0bf2ec672f6d3f4685eb7a02735cc91
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_03.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f26c3fdd182d395429aae2561a3e2b3c
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_Armor_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f36eb8b433591024a8c6711c2201651c
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_Wyvern_01.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f38f7b9adf24f4b4797871f375eb7500
    m_Address: Assets/k1/Res/Map/Troop/Troop.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f683aed5eed320984039178ae3b75d89
    m_Address: Assets/k1/Res/Map/Territory/Prefab/territory_line.prefab
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: f6cdcd2517e6ba3428822b234b64da13
    m_Address: Assets/Res/Combat/Data/SkillActs/SkillTestTimeline.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: fa8985651e1c2164584d3049a47028ed
    m_Address: Assets/Res/Combat/Data/SkillActs/s_beattack.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: ff09980d219574043929097cae205268
    m_Address: Assets/Res/Combat/Data/SkillActs/attack_archer_02.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  m_ReadOnly: 0
  m_Settings: {fileID: 11400000, guid: 0136e5c1707ffcc42a01c054e7a28fbc, type: 2}
  m_SchemaSet:
    m_Schemas:
    - {fileID: 11400000, guid: 40b5680fc4c113a4bbeec9dd2fb4ad3d, type: 2}
    - {fileID: 11400000, guid: fa6590ad21f7d8042a3e54cd04e31042, type: 2}
