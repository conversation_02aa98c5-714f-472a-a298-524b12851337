﻿ 



 
 


using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    public class Vector1
    {
        public Vector Vec;
        public Edge Edge;
        public int EdgeIdx;
        public int VerIdx;

        public Vector1(Coord start, Edge edge, int edgeIdx, int verIdx)
        {
            EdgeIdx = edgeIdx;
            Edge = edge;
            VerIdx = verIdx;
            Vec = new Vector(start, GetVertice().Coord);
        }

        public Vertice GetVertice()
        {
            return VerIdx == 0 ? Edge.Inflect0 : Edge.Inflect1;
        }

        // 获取边的向量, 从拐点开始
        Vector GetEdgeVector()
        {
            if (VerIdx == 0)
            {
                return new Vector(Edge.Vertice0.Coord, Edge.Vertice1.Coord);
            }
            return new Vector(Edge.Vertice1.Coord, Edge.Vertice0.Coord);
        }
    }
}
