﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionSetGroundTileMaskTextureData : EditorAction
    {
        public ActionSetGroundTileMaskTextureData(GroundTileMaker maker, int lod, int tileIndex, int variationIndex, int variationInstanceID, int maskTextureIndex, GroundTileMaker.EdgeID edgeID, GroundTileMaker.EdgeDirection direction, Color[] newTextureData)
        {
            mMaker = maker;
            mLOD = lod;
            mTileIndex = tileIndex;
            mVariationInstanceID = variationInstanceID;
            mMaskTextureIndex = maskTextureIndex;
            mEdge = edgeID;
            mDirection = direction;
            int textureResolution = maker.GetMaskTextureSetting(lod)[maskTextureIndex].resolution;
            var variation = maker.GetTile(mTileIndex).GetVariation(variationIndex);
            var maskTextureData = variation.GetLOD(lod).maskTextures[maskTextureIndex].textureData;

            mOldTextureData = GetTextureData(edgeID, direction, textureResolution, maskTextureData);
            mNewTextureData = newTextureData;
        }

        public override bool Do()
        {
            return SetTextureData(mNewTextureData);
        }

        public override bool Undo()
        {
            return SetTextureData(mOldTextureData);
        }

        Color[] GetTextureData(GroundTileMaker.EdgeID edge, GroundTileMaker.EdgeDirection direction, int textureResolution, Color[] maskTextureData)
        {
            Color[] pixels = new Color[textureResolution];
            switch (edge)
            {
                case GroundTileMaker.EdgeID.HorizontalLeft:
                case GroundTileMaker.EdgeID.Horizontal:
                case GroundTileMaker.EdgeID.HorizontalRight:
                    if (direction == GroundTileMaker.EdgeDirection.Down)
                    {
                        for (int i = 0; i < textureResolution; ++i)
                        {
                            pixels[i] = maskTextureData[i];
                        }
                    }
                    else
                    {
                        for (int i = 0; i < textureResolution; ++i)
                        {
                            pixels[i] = maskTextureData[i + textureResolution * (textureResolution - 1)];
                        }
                    }
                    break;

                case GroundTileMaker.EdgeID.Vertical:
                case GroundTileMaker.EdgeID.VerticalDown:
                case GroundTileMaker.EdgeID.VerticalUp:
                    if (direction == GroundTileMaker.EdgeDirection.Left)
                    {
                        for (int i = 0; i < textureResolution; ++i)
                        {
                            pixels[i] = maskTextureData[i * textureResolution];
                        }
                    }
                    else
                    {
                        for (int i = 0; i < textureResolution; ++i)
                        {
                            pixels[i] = maskTextureData[i * textureResolution + textureResolution - 1];
                        }
                    }
                    break;
                default:
                    Debug.Assert(false, "todo");
                    break;
            }
            return pixels;
        }

        bool SetTextureData(Color[] pixels)
        {
            if (mLOD >= mMaker.lodCount)
            {
                return false;
            }

            int variationIndex = mMaker.GetVariationIndex(mTileIndex, mVariationInstanceID);
            if (variationIndex < 0)
            {
                return false;
            }

            var variation = mMaker.GetTile(mTileIndex).GetVariation(variationIndex);
            var groundTileLOD = variation.GetLOD(mLOD);
            var texture = groundTileLOD.maskTextures[mMaskTextureIndex].texture;
            var textureData = groundTileLOD.maskTextures[mMaskTextureIndex].textureData;
            groundTileLOD.maskTextures[mMaskTextureIndex].dirty = true;
            int textureResolution = texture.width;
            switch (mEdge)
            {
                case GroundTileMaker.EdgeID.Horizontal:
                case GroundTileMaker.EdgeID.HorizontalRight:
                case GroundTileMaker.EdgeID.HorizontalLeft:
                    if (mDirection == GroundTileMaker.EdgeDirection.Down)
                    {
                        for (int i = 0; i < pixels.Length; ++i)
                        {
                            textureData[i] = pixels[i];
                        }
                        texture.SetPixels(0, 0, pixels.Length, 1, pixels);
                    }
                    else
                    {
                        for (int i = 0; i < pixels.Length; ++i)
                        {
                            textureData[textureResolution * (textureResolution - 1) + i] = pixels[i];
                        }
                        texture.SetPixels(0, textureResolution - 1, pixels.Length, 1, pixels);
                    }
                    break;

                case GroundTileMaker.EdgeID.VerticalUp:
                case GroundTileMaker.EdgeID.Vertical:
                case GroundTileMaker.EdgeID.VerticalDown:
                    if (mDirection == GroundTileMaker.EdgeDirection.Left)
                    {
                        for (int i = 0; i < pixels.Length; ++i)
                        {
                            textureData[i * textureResolution] = pixels[i];
                        }
                        texture.SetPixels(0, 0, 1, pixels.Length, pixels);
                    }
                    else
                    {
                        for (int i = 0; i < pixels.Length; ++i)
                        {
                            textureData[i * textureResolution + textureResolution - 1] = pixels[i];
                        }
                        texture.SetPixels(textureResolution - 1, 0, 1, pixels.Length, pixels);
                    }
                    break;
            }
            texture.Apply();
            mMaker.SetTileVariationLODDirty(mTileIndex, mLOD, mVariationInstanceID);

            return true;
        }

        Color[] mOldTextureData;
        Color[] mNewTextureData;
        GroundTileMaker mMaker;
        int mLOD;
        int mTileIndex;
        int mVariationInstanceID;
        int mMaskTextureIndex;
        GroundTileMaker.EdgeID mEdge;
        GroundTileMaker.EdgeDirection mDirection;
    }
}

#endif