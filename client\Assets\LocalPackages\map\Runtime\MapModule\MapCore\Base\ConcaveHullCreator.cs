﻿#if UNITY_EDITOR

#define DISPLAY_NAME

using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    //参考http://repositorium.sdum.uminho.pt/bitstream/1822/6429/1/ConcaveHull_ACM_MYS.pdf
    public static class ConcaveHullCreator
    {
        public static List<Vector3> Create(List<Vector3> points, List<string> pointNames, int k, bool removeDuplicated = true)
        {
            if (k >= points.Count)
            {
                return null;
            }
            Debug.LogError($"ConcaveHullCreator: {points.Count}, k: {k}, removeDuplicated: {removeDuplicated}");
            if (removeDuplicated)
            {
                Utils.RemoveDuplicated1(points, 0.001f);
            }

            List<Vector3> originalPoints = new List<Vector3>();
            originalPoints.AddRange(points);

            int kk = Mathf.Max(k, 3);
            
            if (points.Count <= 3)
            {
                return points;
            }
            kk = Mathf.Min(kk, points.Count - 1);
            Vector3 firstPoint = FindMinZPoint(points);
            List<Vector3> hull = new List<Vector3>();
            hull.Add(firstPoint);
            Vector3 currentPoint = firstPoint;
            Vector3 previousPoint = currentPoint - new Vector3(1, 0, 0);
            points.Remove(firstPoint);
            StringBuilder builder = new StringBuilder();
            while ((currentPoint != firstPoint || hull.Count == 1) && points.Count > 0)
            {
                if (hull.Count == 4)
                {
                    points.Add(firstPoint);
                }
                //找到currentPoint最近的k个点
                List<Vector3> kNearestPoints = NearestPoints(points, currentPoint, kk);

#if DISPLAY_NAME
                string currentPointName = pointNames[originalPoints.IndexOf(currentPoint)];
                builder.Clear();
                builder.Append($"k Nearest Points Of {currentPointName}: ");
                for (int p = 0; p < kNearestPoints.Count; ++p)
                {
                    builder.Append(pointNames[originalPoints.IndexOf(kNearestPoints[p])]);
                    builder.Append(",");
                }
                Debug.LogError(builder.ToString());
#endif
                //按降序排序这k个点，角度最大的最前面
                List<Vector3> cPoints = SortByAngle(kNearestPoints, currentPoint, previousPoint);

#if DISPLAY_NAME
                builder.Clear();
                builder.Append("Sorted By Angle K Points: ");
                for (int p = 0; p < cPoints.Count; ++p)
                {
                    builder.Append(pointNames[originalPoints.IndexOf(cPoints[p])]);
                    builder.Append(",");
                }
                Debug.LogError(builder.ToString());
#endif
                //检查候选点是否与hull相交
                int i = 0;
                for (i = 0; i < cPoints.Count; ++i)
                {
                    if (!Intersect(cPoints[i], currentPoint, hull))
                    {
                        break;
                    }
                }
                if (i == cPoints.Count)
                {
                    return Create(originalPoints, pointNames, kk + 1, false);
                }

                previousPoint = currentPoint;
                currentPoint = cPoints[i];
                hull.Add(currentPoint);
                points.Remove(currentPoint);
            }

            Vector3[] hullArray = hull.ToArray();
            for (int i = 0; i < points.Count; ++i)
            {
                if (!EditorUtils.PointInPolygon2D(points[i], hullArray))
                {
                    return Create(originalPoints, pointNames, kk + 1, false);
                }
            }

            return hull;
        }

        static Vector3 FindMinZPoint(List<Vector3> points)
        {
            float minZ = points[0].z;
            int minIndex = 0;
            for (int i = 1; i < points.Count; ++i)
            {
                if (points[i].z < minZ)
                {
                    minZ = points[i].z;
                    minIndex = i;
                }
            }
            return points[minIndex];
        }

        static List<Vector3> NearestPoints(List<Vector3> points, Vector3 point, int count)
        {
            mNearestPoints.Clear();
            mCopy.Clear();
            mCopy.AddRange(points);
            mCopy.Sort((Vector3 a, Vector3 b) =>
            {
                float disA = (a - point).sqrMagnitude;
                float disB = (b - point).sqrMagnitude;
                if (disA < disB)
                {
                    return -1;
                }
                else if (disA > disB)
                {
                    return 1;
                }
                return 0;
            }
            );

            int n = Mathf.Min(count, points.Count);
            for (int i = 0; i < n; ++i)
            {
                mNearestPoints.Add(mCopy[i]);
            }
            return mNearestPoints;
        }

        static List<Vector3> SortByAngle(List<Vector3> kNearestPoints, Vector3 currentPoint, Vector3 previousPoint)
        {
            kNearestPoints.Sort((Vector3 a, Vector3 b)=> {
                float angleA = GetAngle(previousPoint, currentPoint, a);
                float angleB = GetAngle(previousPoint, currentPoint, b);
                if (angleA < angleB)
                {
                    return 1;
                }
                else if (angleA > angleB)
                {
                    return -1;
                }
                return 0;
            });
            return kNearestPoints;
        }

        static bool Intersect(Vector3 start, Vector3 end, List<Vector3> hull)
        {
            for (int i = hull.Count - 1; i >= 1; --i)
            {
                if (Utils.SegmentSegmentIntersectionTest(Utils.ToVector2(start), Utils.ToVector2(end), Utils.ToVector2(hull[i]), Utils.ToVector2(hull[i - 1]), out _))
                {
                    return true;
                }
            }
            return false;
        }
        /*
         *                     c 
         *                    /
         *                   /
         *                  /
         *                 / 
         * a--------------b
         * angle的角度可以大于180，返回0到360的值
         */
        static float GetAngle(Vector3 a, Vector3 b, Vector3 c)
        {
            var ab = a - b;
            var cb = c - b;
            ab.Normalize();
            cb.Normalize();
            float angle = Vector3.Angle(ab, cb);
            var cross = Vector3.Cross(ab, cb);
            if (cross.y < 0)
            {
                angle = 360 - angle;
            }
            return angle;
        }

        static List<Vector3> mCopy = new List<Vector3>();
        static List<Vector3> mNearestPoints = new List<Vector3>();
    }
}


#endif