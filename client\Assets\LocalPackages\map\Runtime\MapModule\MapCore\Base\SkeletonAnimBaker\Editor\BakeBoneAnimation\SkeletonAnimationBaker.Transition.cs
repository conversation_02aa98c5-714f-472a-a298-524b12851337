﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using UnityEditor.Animations;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SkeletonAnimationBaker
    {
        //返回每个状态的transition信息
        List<StateInfo> GetAnimationStateInfo(GameObject prefab, out List<ParameterInfo> parameterInfo, out string animationControllerAssetGuid)
        {
            var animator = prefab.GetComponentInChildren<Animator>();
            AnimatorController controller = animator.runtimeAnimatorController as AnimatorController;
            animationControllerAssetGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(controller));
            Debug.Assert(!string.IsNullOrEmpty(animationControllerAssetGuid));
            //处理动画parameter
            var parameters = controller.parameters;
            parameterInfo = new List<ParameterInfo>();
            for (int i = 0; i < parameters.Length; ++i)
            {
                ParameterInfo pi = new ParameterInfo();
                pi.defaultBool = parameters[i].defaultBool;
                pi.defaultFloat = parameters[i].defaultFloat;
                pi.defaultInt = parameters[i].defaultInt;
                pi.name = parameters[i].name;
                pi.nameHash = parameters[i].nameHash;
                pi.type = parameters[i].type;
                parameterInfo.Add(pi);
            }

            //只处理layer0的statemachine,并且不支持嵌套statemachine
            var stateMachine = controller.layers[0].stateMachine;
            var states = stateMachine.states;
            List<StateInfo> stateInfos = new List<StateInfo>(states.Length);
            for (int i = 0; i < states.Length; ++i)
            {
                var transitions = states[i].state.transitions;
                var stateInfo = new StateInfo();
                stateInfo.name = states[i].state.name;
                stateInfo.clip = states[i].state.motion as AnimationClip;
                stateInfo.speed = states[i].state.speed;
                stateInfo.speedParameter = states[i].state.speedParameter;
                stateInfo.speedParameterActive = states[i].state.speedParameterActive;
                stateInfo.cycleOffset = states[i].state.cycleOffset;
                stateInfo.cycleOffsetParameter = states[i].state.cycleOffsetParameter;
                stateInfo.cycleOffsetParameterActive = states[i].state.cycleOffsetParameterActive;
                stateInfo.transitions = new AnimationTransitionInfo[transitions.Length];
                stateInfos.Add(stateInfo);
                for (int t = 0; t < transitions.Length; ++t)
                {
                    var conditions = transitions[t].conditions;
                    stateInfo.transitions[t] = new AnimationTransitionInfo(stateInfo.name, transitions[t].destinationState.name, new AnimationTransitionConditionInfo[conditions.Length], transitions[t].hasExitTime, transitions[t].exitTime, transitions[t].hasFixedDuration, transitions[t].duration, transitions[t].offset);
                    for (int c = 0; c < conditions.Length; ++c)
                    {
                        stateInfo.transitions[t].conditions[c] = new AnimationTransitionConditionInfo();
                        stateInfo.transitions[t].conditions[c].mode = (int)conditions[c].mode;
                        stateInfo.transitions[t].conditions[c].parameter = conditions[c].parameter;
                        stateInfo.transitions[t].conditions[c].threshold = conditions[c].threshold;
                    }
                }

                //process animation events
                if (stateInfo.clip != null)
                {
                    var events = stateInfo.clip.events;
                    if (events != null)
                    {
                        stateInfo.events = new AnimationEventInfo[events.Length];
                        for (int e = 0; e < events.Length; ++e)
                        {
                            stateInfo.events[e] = new AnimationEventInfo();
                            stateInfo.events[e].floatParameter = events[e].floatParameter;
                            stateInfo.events[e].intParameter = events[e].intParameter;
                            stateInfo.events[e].stringParameter = events[e].stringParameter;
                            stateInfo.events[e].functionName = events[e].functionName;
                            stateInfo.events[e].time = events[e].time;
                        }
                    }
                }
                else
                {
                    string msg = $"state {stateInfo.name} has no animation clip!";
                    ErrorMsg += msg;
                    Debug.LogError(msg);
                }
            }

            return stateInfos;
        }
    }
}

#endif