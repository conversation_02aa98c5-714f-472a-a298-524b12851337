%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5703813929111437495
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4068558480835626557}
  - component: {fileID: 6457937663432711914}
  m_Layer: 0
  m_Name: ObstacleOutline
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4068558480835626557
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5703813929111437495}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6457937663432711914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5703813929111437495}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -60.447693, y: 0, z: -35.429344}
    - {x: -19.0974, y: 0, z: -20.793726}
    - {x: -7.5247393, y: 0, z: -1.5439043}
    - {x: -11.627921, y: 0, z: 3.3857737}
    - {x: -44.13084, y: 0, z: -6.497533}
    - {x: -60.687363, y: 0, z: -17.908504}
    - {x: -65.6937, y: 0, z: -32.82691}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -18.6676, y: 0, z: -23.3357}
    - {x: -17.8871, y: 0, z: -22.7217}
    - {x: -5.7619, y: 0, z: -4.0611}
    - {x: -5.5165, y: 0, z: -3.1254}
    - {x: -5.6181, y: 0, z: -1.5188}
    - {x: -5.6419, y: 0, z: -1.3308}
    - {x: -5.8041, y: 0, z: -0.485}
    - {x: -5.8528, y: 0, z: -0.2975}
    - {x: -6.5288, y: 0, z: 1.7526}
    - {x: -6.9217, y: 0, z: 2.3754}
    - {x: -8.4284, y: 0, z: 3.8183}
    - {x: -8.5278, y: 0, z: 3.9054}
    - {x: -9.9595, y: 0, z: 5.053}
    - {x: -11.1032, y: 0, z: 5.3864}
    - {x: -13.9384, y: 0, z: 5.0781}
    - {x: -14.1983, y: 0, z: 5.0263}
    - {x: -26.7709, y: 0, z: 1.3445}
    - {x: -26.8021, y: 0, z: 1.335}
    - {x: -44.1657, y: 0, z: -4.1628}
    - {x: -44.52, y: 0, z: -4.3282}
    - {x: -46.7191, y: 0, z: -5.7283}
    - {x: -46.7526, y: 0, z: -5.7503}
    - {x: -57.6229, y: 0, z: -13.0861}
    - {x: -57.7459, y: 0, z: -13.1787}
    - {x: -59.8188, y: 0, z: -14.9114}
    - {x: -60.062, y: 0, z: -15.1691}
    - {x: -64.5433, y: 0, z: -21.205}
    - {x: -64.7526, y: 0, z: -21.5966}
    - {x: -67.9148, y: 0, z: -30.4479}
    - {x: -67.8856, y: 0, z: -31.5677}
    - {x: -67.2364, y: 0, z: -33.124}
    - {x: -67.1526, y: 0, z: -33.294}
    - {x: -66.7235, y: 0, z: -34.0408}
    - {x: -66.6163, y: 0, z: -34.2024}
    - {x: -65.3087, y: 0, z: -35.9202}
    - {x: -64.7342, y: 0, z: -36.3809}
    - {x: -62.8394, y: 0, z: -37.2537}
    - {x: -62.7169, y: 0, z: -37.3037}
    - {x: -61.0637, y: 0, z: -37.895}
    - {x: -60.0307, y: 0, z: -37.8976}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
