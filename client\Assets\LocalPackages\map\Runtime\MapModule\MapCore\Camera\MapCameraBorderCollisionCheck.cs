﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    //检测相机与圆形地图边缘的碰撞
    public static class MapCameraBorderCollisionCheck
    {
        public static Vector3 CheckCollision(Vector3 lastCameraPos, Vector3 newCameraPos, float cameraColliderRadius, float borderRadius, float borderHeight)
        {
            var map = Map.currentMap;
            var center = new Vector2(map.mapWidth * 0.5f, map.mapHeight * 0.5f);
            var delta = new Vector2(newCameraPos.x, newCameraPos.z) - center;
            var dis = delta.magnitude;
            delta.Normalize();
            float radius = borderRadius - cameraColliderRadius;
            float newCameraHeight = newCameraPos.y;
            float lastCameraHeight = lastCameraPos.y;
            float heightThreshold = borderHeight + cameraColliderRadius;
            if (newCameraHeight < heightThreshold && dis >= radius)
            {
                if (newCameraPos.z > center.y)
                {
                    return lastCameraPos;
                }

                if (Utils.LE(lastCameraHeight, newCameraHeight))
                {
                    //camera is rising or horizontally moving
                    var cameraPosXZ = center + radius * delta;
                    newCameraPos.x = cameraPosXZ.x;
                    newCameraPos.z = cameraPosXZ.y;
                }
                else
                {
                    //camera is falling
                    newCameraPos.y = heightThreshold;
                }
            }
            return newCameraPos;
        }
    }
}
