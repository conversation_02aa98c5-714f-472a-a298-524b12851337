// Toony Colors Pro+Mobile 2
// (c) 2014-2023 <PERSON>

// Shader Generator Module: Hash functions
// Should be the same on all GPUs, and doesn't rely on trigonometry functions
// (c) 2014 <PERSON>, CC BY-SA 4.0 License
// https://www.shadertoy.com/view/4djSRW

//================================================================

#FUNCTIONS
/// IF USE_DITHERING_FUNCTION

	static const float DITHER_THRESHOLD_8x8[64] =
	{
		0.2627,0.7725,0.3843,0.9529,0.2941,0.8196,0.4118,1.0000,
		0.5020,0.0392,0.6235,0.1412,0.5333,0.0510,0.6549,0.1725,
		0.3216,0.8627,0.2039,0.6824,0.3529,0.9098,0.2314,0.7294,
		0.5647,0.0824,0.4431,0.0078,0.5922,0.1137,0.4745,0.0235,
		0.2784,0.7961,0.4000,0.9765,0.2471,0.7529,0.3686,0.9333,
		0.5176,0.0471,0.6392,0.1569,0.4902,0.0314,0.6078,0.1294,
		0.3373,0.8863,0.2196,0.7059,0.3098,0.8431,0.1882,0.6706,
		0.5804,0.0980,0.4588,0.0157,0.5490,0.0667,0.4275,0.0001
	};
	float Dither8x8(float2 positionCS)
	{
		uint index = (uint(positionCS.x) % 8) * 8 + uint(positionCS.y) % 8;
		return DITHER_THRESHOLD_8x8[index];
	}

	static const float DITHER_THRESHOLDS_4x4[16] =
	{
		0.0588, 0.5294, 0.1765, 0.6471,
		0.7647, 0.2941, 0.8823, 0.4118,
		0.2353, 0.7059, 0.1176, 0.5882,
		0.9412, 0.4706, 0.8235, 0.3529
	};
	float Dither4x4(float2 positionCS)
	{
		uint index = (uint(positionCS.x) % 4) * 4 + uint(positionCS.y) % 4;
		return DITHER_THRESHOLDS_4x4[index];
	}

///
#END

//================================================================