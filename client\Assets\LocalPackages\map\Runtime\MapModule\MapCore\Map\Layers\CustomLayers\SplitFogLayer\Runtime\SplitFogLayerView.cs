﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerView : MapLayerView
    {
        public SplitFogLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = Map.currentMap.view.reusableGameObjectPool;
            if (!Map.currentMap.isEditorMode)
            {
                CreateMesh();
            }
            OnFogHeightChange();
            CreateLODPlane();

            if (!Map.currentMap.isEditorMode)
            {
                var fogLayerData = layerData as SplitFogLayerData;
                mTileAtlasUVs = new Vector2[16][];
                for (int i = 1; i < 16; ++i)
                {
                    mTileAtlasUVs[i] = GetTileAtlasUV(fogLayerData.tilePrefabPaths[i]);
                }
                InitSelectionData(fogLayerData.selectionMaterialPath);
            }
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var p in mTileViews)
            {
                Utils.DestroyObject(p.Value);
            }

            DestroySplitData();
            DestroySelectionData();

            Utils.DestroyObject(mFogPlane);
            Utils.DestroyObject(mMaskTexture);
        }

        bool CanControlInPlaneLOD()
        {
            var layerData = mLayerData as SplitFogLayerData;
            if (!layerData.isSwitchingLOD && layerData.currentLOD > 0)
            {
                return false;
            }
            return true;
        }

        public void OnTileVisibilityChange(int x, int y, bool newActiveState)
        {
            if (!CanControlInPlaneLOD())
            {
                return;
            }
            if (newActiveState)
            {
                ShowTile(x, y);
            }
            else
            {
                HideTile(x, y, -1);
            }
        }

        public void OnTileDataChange(int x, int y, int oldTileType, bool visible)
        {
            if (!CanControlInPlaneLOD())
            {
                return;
            }

            HideTile(x, y, oldTileType);
            if (visible)
            {
                ShowTile(x, y);
            }
        }

        public void OnFogHeightChange()
        {
            var layerData = mLayerData as SplitFogLayerData;
            var pos = root.transform.position;
            pos.y = layerData.fogHeight;
            root.transform.position = pos;
        }

        //只处理显示1到15类型的格子
        void ShowTile(int x, int y)
        {
            Vector2Int key = new Vector2Int(x, y);
            var layerData = mLayerData as SplitFogLayerData;
            int tileType = layerData.GetTileType(x, y);
            if (tileType > 0 && tileType <= 15)
            {
#if UNITY_EDITOR
                if (mTileViews.ContainsKey(key))
                {
                    Debug.Assert(false, string.Format("Show object {0}_{1} failed!", x, y));
                }
#endif
                var obj = mObjectPool.Require(layerData.GetAssetPath(tileType));
                Utils.HideGameObject(obj);
                var transform = obj.transform;
                transform.position = layerData.FromCoordinateToWorldPosition(x, y);

                transform.SetParent(root.transform, false);
                mTileViews[key] = obj;
            }
        }

        //只处理隐藏1到15类型的格子
        void HideTile(int x, int y, int oldTileType)
        {
            Vector2Int key = new Vector2Int(x, y);
            var layerData = mLayerData as SplitFogLayerData;
            if (oldTileType < 0)
            {
                oldTileType = layerData.GetTileType(x, y);
            }

            if (oldTileType > 0 && oldTileType <= 15)
            {
                string assetPath = layerData.GetAssetPath(oldTileType);
                GameObject obj;
                bool found = mTileViews.TryGetValue(key, out obj);
                if (found)
                {
                    mObjectPool.Release(assetPath, obj, layerData.map);
                    mTileViews.Remove(key);
                }
            }
        }

        Vector2[] GetTileAtlasUV(string prefabPath)
        {
            var prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);
            var filter = prefab.GetComponent<MeshFilter>();
            if (filter != null)
            {
                return filter.sharedMesh.uv2;
            }

            return new Vector2[] {
                new Vector2(0, 0),
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };
        }

        public Vector2[] GetTileAtlasUV(int tileIndex)
        {
            if (tileIndex > 0)
            {
                tileIndex = Mathf.Clamp(tileIndex, 1, 15);
                return mTileAtlasUVs[tileIndex];
            }
            Debug.Assert(false);
            return null;
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        Dictionary<Vector2Int, GameObject> mTileViews = new Dictionary<Vector2Int, GameObject>();

        GameObjectPool mObjectPool;
        Vector2[][] mTileAtlasUVs;
    };
}
