﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //当地块隐藏时去掉管理的其子节点
    public class FrameActionRemoveTileObject : FrameAction
    {
        public static FrameActionRemoveTileObject Require(FrameAction parent, TileGridObjectLayerData layerData, TileObjectData tileObject)
        {
            var act = mPool.Require();
            act.Init(parent, layerData, tileObject);
            return act;
        }

        void Init(FrameAction parent, TileGridObjectLayerData layerData, TileObjectData tileObject)
        {
            InitAction();
            if (parent != null)
            {
                parent.AddChildAction(this);
            }
            mLayerData = layerData;
            mTileObject = tileObject;
            mKey = MakeActionKey(tileObject.id, FrameActionType.RemoveTileObject);
        }

        protected override void DoImpl()
        {
            mLayerData.SetObjectActiveOnly(mTileObject, false, mTileObject.lod);
            mLayerData.pool.Release(mTileObject);
            mLayerData.RemoveTileObject(mTileObject);   
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mTileObject = null;
            mPool.Release(this);
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return MakeKeyHelper(id, type);
        }
        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.RemoveTileObject;
        public override string debugInfo => "";
        public override string name => "Remove Tile Object";

        TileGridObjectLayerData mLayerData;
        TileObjectData mTileObject;
        long mKey;

        static ObjectPool<FrameActionRemoveTileObject> mPool = new ObjectPool<FrameActionRemoveTileObject>(30000, ()=>new FrameActionRemoveTileObject());
    }
}
