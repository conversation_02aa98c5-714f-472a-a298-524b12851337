﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //update一个tile下所有的tile object
    public class FrameActionUpdateTileObjects2 : FrameAction
    {
        public static FrameActionUpdateTileObjects2 Require(TileGridObjectLayerData2 layerData, ComplexGridBigTileData2 bigTileData, int lod, int tileX, int tileY)
        {
            var act = mPool.Require();
            act.Init(layerData, bigTileData, lod, tileX, tileY);
            return act;
        }

        void Init(TileGridObjectLayerData2 layerData, ComplexGridBigTileData2 bigTileData, int lod, int tileX, int tileY)
        {
            InitAction();
            mLayerData = layerData;
            mBigTileData = bigTileData;
            mLOD = lod;
            int tileIndex = tileY * layerData.horizontalTileCount + tileX;
            mKey = MakeActionKey(tileIndex, lod, type);

            var childrenModelTemplates = bigTileData.objectsOfEachLOD[lod];
            int n = childrenModelTemplates.Count;

            mTileIndex = tileY * layerData.horizontalTileCount + tileX;

            for (int i = 0; i < n; ++i)
            {
                var objectID = mLayerData.GetObjectID(lod, mBigTileData, mTileIndex, i);
                var bounds = childrenModelTemplates[i].GetLocalBoundsInPrefab(mLayerData.prefabInfos);
                bool useCullManager = bounds.width <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && bounds.height <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE;
                //现在只管理山的视野,因为山太大了,不适合用管理小件物体的方法管理
                if (!useCullManager && childrenModelTemplates[i].objectType != TileObjectType.AlwaysVisible)
                {
                    FrameAction updateAction = FrameActionUpdateTileObject2.Require(layerData, objectID);
                    AddChildAction(updateAction);
                }
            }
        }

        protected override void DoImpl()
        {
        }

        public override bool isEnabled
        {
            get
            {
                return true;
            }
        }

        public static long MakeActionKey(int id, int lod, FrameActionType type)
        {
            long id64 = (long)id;
            long lod64 = (long)lod;
            long type64 = (long)type;
            long typeAndLOD = (lod64 << 16) | type64;
            id64 <<= 32;
            return typeAndLOD | id64;
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.UpdateTileObjects2;
        public override string debugInfo => string.Format("Tile Index: {0}, LOD: {1}, Enabled: {2}", mTileIndex, mLOD, isEnabled);
        public override string name => "Update Tile Objects 2";
        public override bool keepAlive { get { return true; } }

        TileGridObjectLayerData2 mLayerData;
        int mLOD;
        int mTileIndex;
        long mKey;
        ComplexGridBigTileData2 mBigTileData;

        static ObjectPool<FrameActionUpdateTileObjects2> mPool = new ObjectPool<FrameActionUpdateTileObjects2>(1000, () => new FrameActionUpdateTileObjects2());
    }
}
