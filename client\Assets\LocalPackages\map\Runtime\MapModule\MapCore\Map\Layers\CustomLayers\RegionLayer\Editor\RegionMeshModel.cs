﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionMeshModel
    {
        public RegionMeshModel(Vector3[] vertices, int[] indices, Color[] vertexColors, Material mtl)
        {
            Debug.Assert(vertexColors != null);
            mMesh = new Mesh();
            mMesh.vertices = vertices;
            mMesh.triangles = indices;
            mMesh.colors = vertexColors;
            mMesh.RecalculateNormals();
            mGameObject = new GameObject("region mesh");
            var renderer = mGameObject.AddComponent<MeshRenderer>();
            var filter = mGameObject.AddComponent<MeshFilter>();
            renderer.sharedMaterial = mtl;
            filter.sharedMesh = mMesh;
        }

        public void OnDestroy()
        {
            Object.DestroyImmediate(mGameObject);
            mGameObject = null;
            Object.DestroyImmediate(mMesh);
            mMesh = null;
        }

        public void Update(Vector3[] vertices)
        {
            mMesh.vertices = vertices;
            mMesh.RecalculateNormals();
            mMesh.RecalculateBounds();
        }

        public void SetParent(Transform parent)
        {
            mGameObject.transform.SetParent(parent);
        }

        public void SetMaterial(Material mtl)
        {
            var renderer = mGameObject.GetComponent<MeshRenderer>();
            renderer.sharedMaterial = mtl;
        }

        public Mesh GetMesh()
        {
            return mMesh;
        }

        public void SetVisible(bool visible)
        {
            mGameObject.SetActive(visible);
        }

        GameObject mGameObject;
        Mesh mMesh;
    }
}


#endif