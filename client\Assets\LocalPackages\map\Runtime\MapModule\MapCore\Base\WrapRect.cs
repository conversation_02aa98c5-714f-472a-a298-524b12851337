﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class WrapRect
    {
        public static List<RectI> Calculate(RectI oldRect, RectI newRect, int textureResolution)
        {
            mVisibleRects.Clear();
            mInvisibleRects.Clear();
            mDifference.Calculate(oldRect, newRect, mVisibleRects, mInvisibleRects);

            List<RectI> results = new List<RectI>();

            for (int i = 0; i < mInvisibleRects.Count; ++i)
            {
                int xMin = mInvisibleRects[i].xMin;
                int yMin = mInvisibleRects[i].yMin;
                int xMax = mInvisibleRects[i].xMax;
                int yMax = mInvisibleRects[i].yMax;
                if (xMin < 0)
                {
                    xMin += textureResolution;
                    xMax = textureResolution - 1;
                }
                if (yMin < 0)
                {
                    yMin += textureResolution;
                    yMax = textureResolution - 1;
                }
                if (xMax >= textureResolution)
                {
                    xMax -= textureResolution;
                    xMin = 0;
                }
                if (yMax >= textureResolution)
                {
                    yMax -= textureResolution;
                    yMin = 0;
                }

                var r = new RectI();
                r.Set(xMin, yMin, xMax, yMax);
                results.Add(r);
            }

            var intersection = oldRect.GetIntersection(newRect);
            results.Add(intersection);

            return results;
        }

        static RectDifference mDifference = new RectDifference();
        static List<RectInt> mVisibleRects = new List<RectInt>();
        static List<RectInt> mInvisibleRects = new List<RectInt>();
    }
}
