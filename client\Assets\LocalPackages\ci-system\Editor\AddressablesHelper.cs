using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Build;
using UnityEditor.AddressableAssets.Settings;

namespace CISystem
{
    public class AddressablesHelper
    {
        public static AddressablesPlayerBuildResult BuildAddressablesContent()
        {
            AddressableAssetSettings
                .BuildPlayerContent(out AddressablesPlayerBuildResult result);
            var success = string.IsNullOrEmpty(result.Error);

            if (!success)
            {
                UnityEngine.Debug.LogError("Addressables build error encountered: " + result.Error);
            }

            return result;
        }

        public static (AddressablesPlayerBuildResult buildResult, List<AddressableAssetEntry> modifiedEntries, string contentUpdateGroupName) UpdateAPreviousBuild()
        {
            string contentStateDataPath = ContentUpdateScript.GetContentStateDataPath(false);
            if (!File.Exists(contentStateDataPath))
            {
                throw new Exception("Previous Content State Data missing");
            }
            AddressableAssetSettings settings = AddressableAssetSettingsDefaultObject.Settings;
            List<AddressableAssetEntry> modifiedEntries = ContentUpdateScript.GatherModifiedEntries(settings, contentStateDataPath);
            ContentUpdateScript.CreateContentUpdateGroup(settings, modifiedEntries, "Content_Update");
            var buildResult = ContentUpdateScript.BuildContentUpdate(settings, contentStateDataPath);

            return (buildResult, modifiedEntries, "Content_Update");
        }
    }
}