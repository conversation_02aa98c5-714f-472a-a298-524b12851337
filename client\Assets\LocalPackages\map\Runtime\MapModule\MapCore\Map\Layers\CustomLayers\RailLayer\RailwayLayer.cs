﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public partial class RailwayLayer : ModelLayer
    {
        public RailwayLayer(Map map) :base(map) { }

        public override void OnDestroy()
        {
            base.OnDestroy();
            mMeshViewer.OnDestroy();
            Reset();

            SetDirty();
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.RailwayLayerData;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            KeepScaleConfig keepScaleConfig = null;
            if (!map.isEditorMode)
            {
                keepScaleConfig = MapModuleResourceMgr.LoadResource<KeepScaleConfig>($"{MapModule.configResDirectory}keep_rail_size.asset");
            }

            RailInfo ri = new RailInfo();
            ri.railWidth = sourceLayer.railWidth;
            ri.railwayCenter = sourceLayer.center;
            ri.railTotalLength = sourceLayer.railTotalLength;
            ri.railCount = sourceLayer.count;
            ri.railPrefabLength = sourceLayer.railPrefabLength;

            var lodGroupManager = Utils.CreateLODGroupManager(layerData, map);
            mRailLayerData = new RailwayLayerData(header, config, map, lodGroupManager, keepScaleConfig, ri);
            mLayerData = mRailLayerData;
            mLayerView = new RailwayLayerView(mRailLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            if (sourceLayer.objects != null)
            {
                int n = sourceLayer.objects.Length;
                for (int i = 0; i < n; ++i)
                {
                    var model = sourceLayer.objects[i] as config.RailObjectData;
                    int segmentIndex = model.segmentIndex;
                    if (segmentIndex == -1 && model.type == RailObjectType.Rail)
                    {
                        float offset = 1.0f;
                        segmentIndex = mRailLayerData.CalculateRailSegmentIndex(model.position + model.rotation * Vector3.right * offset);
                    }

                    var modelTemplate = map.FindObject(model.modelTemplateID) as ModelTemplate;
                    if (modelTemplate == null)
                    {
                        Debug.Assert(false, $"model template{model.modelTemplateID} is not found!");
                    }
                    else
                    {
                        var modelData = new RailObjectData(model.id, map, 0, model.position, model.rotation, model.scale, modelTemplate, model.type, model.isGroupLeader, model.groupID, model.railIndex, segmentIndex);
                        mLayerData.AddObjectData(modelData);
                    }
                }
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);

            if (ri.railCount > 0)
            {
                GenerateRailColliders(ri.railWidth, ri.railwayCenter, ri.railTotalLength, ri.railCount, ri.railPrefabLength);
            }

            SetDirty();
        }

        //生成铁轨的碰撞
        //width:铁轨宽度
        //center:铁轨的交汇点
        //radius:铁轨长度
        //count:多少根铁轨
        public void GenerateRailColliders(float width, Vector3 center, float radius, int count, float railPrefabLength)
        {
#if false
            if (map.isEditorMode)
            {
                Reset();

                Debug.Assert(count > 0);
                float deltaAngle = 360.0f / count;

                for (int i = 0; i < count; ++i)
                {
                    var rail = CreateRailCollider(i * deltaAngle + 180.0f, center, radius, width, railPrefabLength);
                    mRailColliderObjects.Add(rail);
                }

                mMeshBlocks = Utils.CreateNavMesh(LayerTypeMask.kRailwayLayer, layerData.GetLayerWidthInMeter(0), layerData.GetLayerHeightInMeter(0), 1, 1, PrefabOutlineType.ObjectPlacementObstacle, false, false, false, 0, 0, float.MaxValue);
                if (mMeshBlocks != null)
                {
                    if (mMeshBlocks[0].vertices != null)
                    {
                        mMeshViewer.Create(layerView.root, "railway navmesh", mMeshBlocks[0].vertices, mMeshBlocks[0].indices, true, new Color32(255, 127, 39, 150));
                    }
                }

                mRailLayerData.railWidth = width;
                mRailLayerData.railPrefabLength = railPrefabLength;
                mRailLayerData.railTotalLength = radius;
                mRailLayerData.railCount = count;

                SetDirty();
            }
#endif
        }

        public void ExportColliders(string folder)
        {
            if (string.IsNullOrEmpty(folder))
            {
                return;
            }

#if UNITY_EDITOR
            //if (mMeshBlocks != null)
            //{
            //    JSONExporter.ExportNavMesh("railway_obstacles", folder, mMeshBlocks[0].vertices, mMeshBlocks[0].indices);
            //}
#endif
        }

        void Reset()
        {
            for (int i = 0; i < mRailColliderObjects.Count; ++i)
            {
                GameObject.DestroyImmediate(mRailColliderObjects[i]);
            }
            mRailColliderObjects.Clear();
            //mMeshBlocks = null;
        }

        GameObject CreateRailCollider(float angle, Vector3 center, float length, float width, float railPrefabLength)
        {
            //只生成地图有效区域内的collider
            length = Mathf.Min(length, map.mapWidth * 0.5f);

            var rot = Quaternion.Euler(0, angle, 0);
            var railObject = new GameObject("Rail " + mRailColliderObjects.Count);
            Utils.HideGameObject(railObject);
            railObject.transform.parent = layerView.root.transform;
            railObject.transform.rotation = rot;
            railObject.transform.position = center;
            var outline = railObject.AddComponent<PrefabOutline>();
            OutlineData data = new OutlineData();
            var min = new Vector3(0, 0, -width * 0.5f);
            var max = new Vector3(length, 0, width * 0.5f);
            List<Vector3> outlineVertices = new List<Vector3> {
                        new Vector3(min.x, 0, min.z),
                        new Vector3(max.x, 0, min.z),
                        new Vector3(max.x, 0, max.z),
                        new Vector3(min.x, 0, max.z),
                };
            data.outline = outlineVertices;
            data.CalculateBounds();
            outline.SetOutlineData(data, PrefabOutlineType.NavMeshObstacle);
            var otherData = data.Clone();
            outline.SetOutlineData(otherData, PrefabOutlineType.ObjectPlacementObstacle);

            return railObject;
        }

        public List<PrefabOutline> GetColliderOutlines()
        {
            List<PrefabOutline> outlines = new List<PrefabOutline>();
            for (int i = 0; i < mRailColliderObjects.Count; ++i)
            {
                outlines.Add(mRailColliderObjects[i].GetComponentInChildren<PrefabOutline>());
            }
            return outlines;
        }

        public List<List<Vector3>> GetColliderPolygons()
        {
            List<List<Vector3>> polygons = new List<List<Vector3>>();
            for (int i = 0; i < mRailColliderObjects.Count; ++i)
            {
                var prefabOutline = mRailColliderObjects[i].GetComponentInChildren<PrefabOutline>();
                var outlines = prefabOutline.GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle);
                List<Vector3> polygon = new List<Vector3>();
                for (int v = 0; v < outlines.Length; ++v)
                {
                    polygon.Add(outlines[v]);
                }
                polygons.Add(polygon);
            }
            return polygons;
        }

        public void UpdateObjectScale()
        {
            mRailLayerData.UpdateScale();
        }

        public void RemoveObjectsOfType(RailObjectType type)
        {
            List<IMapObjectData> removed = new List<IMapObjectData>();
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var obj = p.Value as RailObjectData;
                if (obj.type == type)
                {
                    removed.Add(obj);
                }
            }

            for (int i = 0; i < removed.Count; ++i)
            {
                RemoveObject(removed[i].GetEntityID());
            }
        }

        public List<IMapObjectData> GetObjectsOfType(RailObjectType type)
        {
            List<IMapObjectData> results = new List<IMapObjectData>();
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var obj = p.Value as RailObjectData;
                if (obj.type == type)
                {
                    results.Add(obj);
                }
            }
            return results;
        }

        float ClampAngle(float angle)
        {
            while (angle > 360)
            {
                angle -= 360;
            }
            return angle;
        }

        public RailObjectData[] GetRailObjects(int railIndex)
        {
            return mRailLayerData.GetRailObjects(railIndex);
        }

        void SetDirty()
        {
#if UNITY_EDITOR
            EditorConfig.dirtyFlag |= DirtyMask.NPCRegionConfig;
            EditorConfig.dirtyFlag |= DirtyMask.NPCSpawnPoints;
#endif
        }

        public RailwayLayerData railLayerData { get { return mRailLayerData; } }
        public bool showColliders
        {
            get
            {
                return mMeshViewer.active;
            }

            set
            {
                mMeshViewer.active = value;
            }
        }

        List<GameObject> mRailColliderObjects = new List<GameObject>();
        BigMeshViewer mMeshViewer = new BigMeshViewer();
        //NavMeshBlock[] mMeshBlocks;
        RailwayLayerData mRailLayerData;
    }
}
