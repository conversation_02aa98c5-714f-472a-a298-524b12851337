﻿ 



 
 


//#define SHOW_TILE_OBJECT_BOUNDS

using UnityEngine;

namespace TFW.Map
{
    public enum TileObjectType : byte
    {
        NoneScaleDecorationObject,
        ScaleDecorationObject,
        StaticObject,
        //标记了这个tag的object也只是可能在当前帧切换lod,还需要根据其他的条件来判断是否立即切换lod
        //未标记这个tag的object永远不会在当前帧切换lod
        BigObject,
        //标记这个tag表示大物体但是不在同一帧切换lod
        BigObjectAsync,
        //不导出的物体
        IgnoredObject,
        //永远可见的物体,不裁剪,用于在地表以下的装饰物
        AlwaysVisible,
        //能够被删除的物体,但是不是decoration object
        Removable,
        SpecialArea,
    }

    public class TileObjectData : BaseObject, IMapObjectData
    {
        public TileObjectData(int id, Map map) : base(id, map)
        {
        }

        public void Set(int id, TileObjectType type, ChildPrefabTransform2 transform, string prefabPath, Rect worldBounds, Vector3 position, Vector3 scaling, Quaternion rotation, int localIndex, int tileIndex, int lod, float baseScale, System.Action<TileObjectData> onObjectScaleChangeCallback, bool useCullManager)
        {
            this.id = id;
            mPrefabPath = prefabPath;
            mTransform = transform;
            mUseCullManager = useCullManager;
            mWorldBounds = worldBounds;
            mLOD = lod;
            mIsActive = false;
            mLocalIndex = localIndex;
            mObjectType = type;
            mTileIndex = tileIndex;
            mBaseScale = baseScale;
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;

            mPosition = position;
            mRotation = rotation;
            mScale = scaling;

#if SHOW_TILE_OBJECT_BOUNDS
            //var path = modelTemplate.GetLODPrefabPath(0);
            //if (path.IndexOf("1-90-5-5_lod0") >= 0)
            {
                if (mDebugObject == null)
                {
                    mDebugObject = new GameObject();
                    mDebugObject.AddComponent<DrawBounds>();
                }
                var drawBounds = mDebugObject.GetComponent<DrawBounds>();
                drawBounds.bounds = Utils.RectToBounds(mWorldBounds);
                mDebugObject.transform.position = drawBounds.bounds.center;
                //mDebugObject.name = Utils.GetPathName(modelTemplate.GetLODPrefabPath(0), false);
            }
#endif
        }

        public override void OnDestroy()
        {
            mIsActive = false;
        }

        public bool IsDynamicEntity() { return false; }
        public int GetEntityID() { return id; }
        public ModelTemplate GetModelTemplate() { Debug.Assert(false, "Can't be here"); return null; }
        public int GetModelTemplateID() { Debug.Assert(false, "Can't be here"); return 0; }
        //当Entity被隐藏时调用
        public void OnHide() { }
        //当Entity被显示时调用
        public void OnShow() { }
        //当Entity的GameObject创建完成时调用
        public void OnInit(GameObject obj) { }
        //当LOD Zoom变化时被调用
        public void OnZoomChange(float zoom) { }
        //Entity是否忽略视野范围的管理,如果为true,则不管视野框如何变化,Entity的显示状态都不会被改变
        public bool IgnoreViewport() { return false; }
        //Entity的prefab路径
        public string GetAssetPath(int lod = 0)
        {
            return mPrefabPath;
        }
        //返回bounding box
        public Rect GetBounds()
        {
            return mWorldBounds;
        }

        //Entity的位置
        public Vector3 GetPosition()
        {
            return mPosition;
        }
        public void SetPosition(Vector3 pos)
        {
            mPosition = pos;
        }
        //Entity的缩放
        public Vector3 GetScale()
        {
            return mScale;
        }
        public void SetScale(Vector3 scale)
        {
            mScale = scale;
        }
        public void SetScale(float scale)
        {
            mScale = Vector3.one * scale;
            mOnObjectScaleChangeCallback(this);
        }
        //Entity的旋转
        public Quaternion GetRotation()
        {
            return mRotation;
        }
        public void SetRotation(Quaternion rot)
        {
            mRotation = rot;
        }

        public bool HasFlag(int flag)
        {
            return HasObjectFlag((ObjectFlag)flag);
        }

        public void SetFlag(int flag)
        {
            SetObjectFlag((ObjectFlag)flag);
        }

        public void AddFlag(int flag)
        {
            AddObjectFlag((ObjectFlag)flag);
        }

        public void RemoveFlag(int flag)
        {
            RemoveObjectFlag((ObjectFlag)flag);
        }

        public int GetFlag()
        {
            return (int)flag;
        }

        public int GetModelLODGroupID()
        {
            return 0;
        }

        public bool IsObjActive() { return mIsActive; }
        public bool SetObjActive(bool active)
        {
            if (mIsActive != active)
            {
                mIsActive = active;
                return true;
            }
            return false;
        }

        public int localIndex { get { return mLocalIndex; } }
        public int tileIndex { get { return mTileIndex; } }
        public TileObjectType objectType { get { return mObjectType; } }
        public int lod { get { return mLOD; } }
        public float baseScale { get { return mBaseScale; } }
        public long cullObjectID { set { mCullObjectID = value; } get { return mCullObjectID; } }
        public string prefabPath { get { return mPrefabPath; } }
        public int prefabInfoIndex { get { return mTransform.prefabInitInfoIndex; } }
        public bool useCullManager { get { return mUseCullManager; } }

        ChildPrefabTransform2 mTransform;
        Rect mWorldBounds;
        int mLocalIndex;
        int mTileIndex;
        int mLOD;
        //TileObject的类型
        TileObjectType mObjectType;
        float mBaseScale;

        long mCullObjectID;

        Vector3 mPosition;
        Vector3 mScale;
        Quaternion mRotation;

        string mPrefabPath;

        System.Action<TileObjectData> mOnObjectScaleChangeCallback;

        bool mIsActive = false;
        bool mUseCullManager = false;

#if SHOW_TILE_OBJECT_BOUNDS
        //temp code
        GameObject mDebugObject;
#endif
    }
}
