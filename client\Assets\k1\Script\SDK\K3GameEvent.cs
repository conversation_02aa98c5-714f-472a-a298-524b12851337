﻿using System;
using System.Collections.Generic;
using THelper;
using ThinkingAnalytics;

namespace K3
{

    public class K3GameEvent : Ins<K3GameEvent>
    {



        public const string taAppId = "1132c3b62ca34e42a7ba8a5bef2bb815";
        public const string taServerUrl = "https://hwta-api.gm99.com";

        /// <summary>
        /// 37SDK 打点
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="mdata"></param>
        /// <param name="eventGroupName"></param>
        public void BiLog<T>(T mdata,string eventGroupName= "custom_active") where T : GameEventType
        {
            D.Info?.Log("[K3GameEvent] BiLog---------------------{0}", mdata.GetEventKey());
#if USE_SDK 
            SDKManager.instance.SDKTrackGameEvent(eventGroupName, mdata.GetEventKey(), "1");
            
#endif
        }

        /// <summary>
        /// 37SDK 打点
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="mdata"></param>
        /// <param name="eventGroupName"></param>
        public void BiLogLoginFunnel(string eventType,string eventName)
        {
            D.Info?.Log($"[K3GameEvent] BiLog------eventType:{eventType}---------------eventName{eventName}");
#if USE_SDK  
            SDKManager.instance.SDKTrackGameEvent(eventType, eventName, "1");
#endif
        }
        public void TaLog<T>(T mdata) where T : GameEventType
        {

            if (mdata.Properties.Count == 0)
            {
                mdata.Properties.Add($"{mdata.GetEventKey()}_value", mdata.EventValue);
            }
            else
            {
                var dic = new Dictionary<string,object>(mdata.Properties);

                mdata.Properties.Clear();

                foreach (var item in dic)
                {
                    mdata.Properties.Add($"{mdata.GetEventKey()}_{item.Key.ToLower()}", item.Value);
                }
            }

            D.Info?.Log("[K3GameEvent] TaLog---------------------{0}  :: {1}", mdata.GetEventKey(), SDK37.SerializeToStr(mdata.Properties));

#if USE_SDK
            ThinkingAnalyticsAPI.Track(mdata.GetEventKey(), mdata.Properties);
             
            FlushCD();
#endif
        }


        //        public void TimeStart(string eventName)
        //        {
        //            D.Info?.Log("[K3GameEvent] TaLog---------------------TimeStart {0}", eventName);
        //#if USE_SDK 

        //            ThinkingAnalyticsAPI.TimeEvent(eventName);
        //#endif
        //        }

        //        public void TimeEnd(string eventName)
        //        {
        //#if USE_SDK  
        //            ThinkingAnalyticsAPI.Track(eventName);
        //            FlushCD();
        //#endif
        //        }


        //        public void TimeEnd(string eventName,string valueStr)
        //        {
        //            D.Info?.Log("[K3GameEvent] TaLog---------------------TimeEnd {0} :: {1}", eventName, valueStr);
        //#if USE_SDK  
        //            Dictionary<string, object> eventProperties = new Dictionary<string, object>();
        //            eventProperties.Add($"{eventName}_value", valueStr);



        //            ThinkingAnalyticsAPI.Track(eventName, eventProperties);
        //            FlushCD();
        //#endif
        //        }

        //private float flushLastTime;
        public void FlushCD()
        {
#if USE_SDK 
            //if (flushLastTime < UnityEngine.Time.time)
            //{
            //    flushLastTime = UnityEngine.Time.time + 10;//10S CD上传缓存

            ThinkingAnalyticsAPI.Flush();
            //}

#endif
        }
    }

    public class HeroGuidEvent : GameEventType
    { 
    }


    /// <summary>
    /// 引导事件
    /// </summary>
    public class GuidEvent: GameEventType
    {
        
    }

    /// <summary>
    /// 图鉴事件
    /// </summary>
    public class AtlasEvent : GameEventType
    {

    }


    public class ResourceEvent : GameEventType
    { }


    /// <summary>
    /// 创建事件
    /// </summary>
    public class CreateEvent: GameEventType
    { }

    /// <summary>
    /// 合成事件
    /// </summary>
    public class MergeEvent : GameEventType
    { }

    public class ADEvent : GameEventType
    { }

    /// <summary>
    /// 道具出售事件
    /// </summary>
    public class ItemSellEvent : GameEventType
    {
        
    }

    /// <summary>
    /// 相册事件
    /// </summary>
    public class PhotoEvent : GameEventType
    { }

    /// <summary>
    /// 商店事件
    /// </summary>
    public class ShopEvent : GameEventType
    { }

    /// <summary>
    /// 任务事件
    /// </summary>
    public class TaskEvent : GameEventType
    { }

    /// <summary>
    /// 登录事件
    /// </summary>
    public class LoginEvent : GameEventType
    {
    }

    public class LoginFailEvent : GameEventType
    {
    }


    /// <summary>
    /// 登出事件
    /// </summary>
    public class LoginOutEvent : GameEventType
    {
    }

    /// <summary>
    /// 云彩
    /// </summary>
    public class AreaEvent : GameEventType
    {
        
    }

    /// <summary>
    /// 主城事件
    /// </summary>
    public class CityTechEvent : GameEventType
    {
        
    }

    public class MapBattleEvent : GameEventType
    {

    }

    public class HeroEvent : GameEventType
    {

    }


    public class GameEvent : GameEventType
    {
        
    }

    /// <summary>
    /// 联盟事件
    /// </summary>
    public class AllianceEvent : GameEventType
    {
        
    }

    /// <summary>
    /// 招募
    /// </summary>
    public class RecruitEvent : GameEventType
    {

    }

    /// <summary>
    /// 解锁功能（主UI 按钮组）
    /// </summary>
    public class UnlockFunctionEvent : GameEventType
    {

    }
    /// <summary>
    /// 遗迹埋点
    /// </summary>
    public class RelicsEvent : GameEventType
    {

    }

    /// <summary>
    /// 重连事件
    /// </summary>
    public class ReconnectFailEvent : GameEventType
    {
        
    }

    public  class MergeSyncFailEvent : GameEventType
    {

    }

    public class MergeTaskError : GameEventType
    {

    }



    public abstract class GameEventType
    {
        public string EventKey { get; set; }

        public string EventValue { get; set; } = "1";

        public Dictionary<string, object> Properties { get;}=new Dictionary<string, object>();

        public string GetEventKey()
        {
            if (String.IsNullOrEmpty(EventKey))
            {
                return this.GetType().Name.ToLower();
            }
            else
                return EventKey.ToLower();
        }

        /// <summary>
        /// 获取当前事件的传递数据 Properties 不填就当前EventValue（默认1） 给他们
        /// </summary>
        /// <returns></returns>
        public virtual string GetEventJson()
        {
            //if(Properties.Count>0)
            //{
            //    return LitJson.JsonMapper.ToJson(Properties);
            //}
            //else
                return EventValue.ToLower();
        }
    }

    
   
}



