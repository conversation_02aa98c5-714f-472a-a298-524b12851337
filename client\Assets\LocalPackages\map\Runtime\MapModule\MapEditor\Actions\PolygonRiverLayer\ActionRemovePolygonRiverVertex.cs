﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionRemovePolygonRiverVertex : EditorAction
    {
        public ActionRemovePolygonRiverVertex(int layerID, int dataID, int index, PrefabOutlineType type)
        {
            var data = Map.currentMap.FindObject(dataID) as PolygonRiverData;
            mLayerID = layerID;
            mDataID = dataID;
            mPosition = data.GetOutlineVertices(type)[index];
            mOutlineType = type;
            mIndex = index;

            var splitters = data.GetUsedSplitters(mPosition);
            if (splitters != null)
            {
                for (int i = splitters.Count - 1; i >= 0; --i)
                {
                    var splitterIndex = data.GetSplitterIndex(splitters[i]);
                    var act = new ActionRemovePolygonRiverSplitter(layerID, dataID, splitterIndex);
                    mRemoveSplitters.Add(act);
                }
            }
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }

            for (int i = 0; i < mRemoveSplitters.Count; ++i)
            {
                mRemoveSplitters[i].Do();
            }
            layer.RemoveCollsionVertex(mOutlineType, mDataID, mIndex);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.InsertCollisionVertex(mOutlineType, mDataID, mIndex, mPosition);
            for (int i = mRemoveSplitters.Count - 1; i >= 0; --i)
            {
                mRemoveSplitters[i].Undo();
            }
            return true;
        }

        public override string description
        {
            get
            {
                return string.Format("{0}, idx: {1}, position: {2}", GetType().Name, mIndex, mPosition.ToString());
            }
        }

        int mLayerID;
        int mDataID;
        int mIndex;
        Vector3 mPosition;
        PrefabOutlineType mOutlineType;
        List<ActionRemovePolygonRiverSplitter> mRemoveSplitters = new List<ActionRemovePolygonRiverSplitter>();
    }
}
#endif
