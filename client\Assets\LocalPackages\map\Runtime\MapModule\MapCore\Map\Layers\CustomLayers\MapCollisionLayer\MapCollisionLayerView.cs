﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/15

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class MapCollisionLayerView : PolygonObjectLayerView
    {
        public MapCollisionLayerView(MapLayerData layerData, bool asyncLoading) : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new MapCollisionView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public override bool active
        {
            set
            {
                root.SetActive(value);
                shadowRoot.SetActive(value);
            }
            get
            {
                return root.activeSelf;
            }
        }
    }
}
#endif
