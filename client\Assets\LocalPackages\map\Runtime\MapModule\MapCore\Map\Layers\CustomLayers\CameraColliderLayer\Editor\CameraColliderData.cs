﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/12/4

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class CameraColliderData : PolygonObjectData
    {
        public CameraColliderData(int id, Map map, List<Vector3> bottomOutline, List<Vector3> topOutline, float displayRadius, float height, Vector3[] vertices, int[] indices) : base(id, map, new OutlineData[2] { new OutlineData(bottomOutline), null }, displayRadius, true, false)
        {
            mHeight = height;
            mVertices = (Vector3[])vertices?.Clone();
            mIndices = (int[])indices?.Clone();
            mColor = new Color(1, 0.5f, 0, 1.0f);
            if (topOutline != null && topOutline.Count > 0)
            {
                if (bottomOutline.Count != topOutline.Count)
                {
                    Debug.Assert(false);
                }
                mTopOutline = new OutlineData(topOutline);
            }
        }

        public void CreateTopOutline(float height)
        {
            //also destroy collider mesh data
            mVertices = null;
            mIndices = null;
            mHeight = height;
            var vertices = GetOutlineVerticesCopy(PrefabOutlineType.NavMeshObstacle);
            for (int i = 0; i < vertices.Count; ++i)
            {
                vertices[i] = new Vector3(vertices[i].x, height, vertices[i].z);
            }
            mTopOutline = new OutlineData(vertices);
        }

        public void SetTopOutline(List<Vector3> outline, float height)
        {
            mHeight = height;
            mVertices = null;
            mIndices = null;
            if (outline != null)
            {
                for (int i = 0; i < outline.Count; ++i)
                {
                    outline[i] = new Vector3(outline[i].x, height, outline[i].z);
                }
                mTopOutline = new OutlineData(outline);
            }
            else
            {
                mTopOutline = null;
            }
        }

        public Vector3 GetVertexPos(int index)
        {
            if (index >= mOutlines[0].outline.Count)
            {
                return mTopOutline.GetVertexPos(index - mOutlines[0].outline.Count);
            }
            return mOutlines[0].GetVertexPos(index);
        }

        public Vector3 AdjustVertexPos(int index, Vector3 pos)
        {
            if (index >= mOutlines[0].outline.Count)
            {
                return new Vector3(pos.x, mHeight, pos.z);
            }
            return pos;
        }

        public void SetVertexPosition(int index, Vector3 pos)
        {
            if (index >= mOutlines[0].outline.Count)
            {
                mTopOutline.SetVertexPos(index - mOutlines[0].outline.Count, pos);
            }
            mOutlines[0].SetVertexPos(index, pos);
        }

        public int GetBottomOutlineVertexCount()
        {
            return GetOutlineVertices(PrefabOutlineType.NavMeshObstacle).Count;
        }

        public void Rotate(float degree)
        {
            var center = Utils.ToVector3(mOutlines[0].bounds.center);
            mOutlines[0]?.Rotate(degree, center);
            if (mTopOutline != null)
            {
                mTopOutline.Rotate(degree, center);
            }
            RotateMeshData(degree, center);
        }

        void RotateMeshData(float degree, Vector3 center)
        {
            if (mVertices != null)
            {
                Quaternion q = Quaternion.Euler(0, degree, 0);
                for (int i = 0; i < mVertices.Length; ++i)
                {
                    var localPos = mVertices[i] - center;
                    mVertices[i] = center + q * localPos;
                }
            }
        }

        public override void Move(PrefabOutlineType type, Vector3 offset)
        {
            base.Move(type, offset);
            if (mTopOutline != null)
            {
                int n = mTopOutline.outline.Count;
                for (int i = 0; i < n; ++i)
                {
                    mTopOutline.outline[i] += offset;
                }
                RecalculateShapeInfo(mTopOutline);
            }
            //move mesh
            if (mVertices != null)
            {
                for (int i = 0; i < mVertices.Length; ++i)
                {
                    mVertices[i] += offset;
                }
            }
        }

        public void CreateCollider()
        {
            CreateCameraColliderMesh.CreateMesh(this, out mVertices, out mIndices);
        }

        public void SetColliderMeshData(Vector3[] vertices, int[] indices)
        {
            mVertices = vertices;
            mIndices = indices;
        }

        public bool HasMeshData()
        {
            return mVertices != null && mVertices.Length > 0;
        }

        public List<Vector3> GetTopOutlineCopy()
        {
            if (mTopOutline != null)
            {
                return mTopOutline.GetCopy();
            }
            return null;
        }

        public OutlineData topOutline { get { return mTopOutline; } }
        public float height { get { return mHeight; } }
        public Vector3[] vertices { get { return mVertices; } }
        public Vector3[] verticesCopy { get { return (Vector3[])mVertices?.Clone(); } }
        public int[] indices { get { return mIndices; } }
        public int[] indicesCopy { get { return (int[])mIndices?.Clone(); } }

        OutlineData mTopOutline;
        float mHeight;
        //collider mesh data
        Vector3[] mVertices;
        int[] mIndices;
    }
}


#endif
