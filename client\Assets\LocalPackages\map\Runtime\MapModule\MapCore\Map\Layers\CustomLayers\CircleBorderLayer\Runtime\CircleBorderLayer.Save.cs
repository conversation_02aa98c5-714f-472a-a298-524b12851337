﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class CircleBorderLayer : ModelLayer
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.CircleBorderLayerEditorDataVersion);

            var editorMapData = Map.currentMap.data as EditorMapData;
            //save prefab manager
            PrepareSaving();
            SaveSetting(writer);
            SavePrefabManager(writer, editorMapData.circleBorderLayerPrefabManager);
            SaveLayerData(writer);
            SavePathMapper(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveSetting(BinaryWriter writer)
        {
            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);
        }

        void SaveLayerData(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(mLayerData.tileWidth);
            writer.Write(mLayerData.tileHeight);

            var allObjects = mLayerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveCircleBorderData(writer, objData as CircleBorderData);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, mLayerData);

            var layerData = mLayerData as CircleBorderLayerData;
            writer.Write(layerData.combineBorderLOD);
        }

        void SaveCircleBorderData(BinaryWriter writer, CircleBorderData data)
        {
            writer.Write(data.GetFlag());
            Utils.WriteVector3(writer, data.GetPosition());
            Utils.WriteQuaternion(writer, data.GetRotation());
            Utils.WriteVector3(writer, data.GetScale());

            short prefabPathIndex = GetPrefabPathIndex(data.GetModelTemplate());
            writer.Write(prefabPathIndex);
            writer.Write(data.isAlwaysVisibleAtHigherLODs);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                writer.Write((int)0);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }

        void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            Utils.WriteString(writer, group.name);
            Utils.WriteColor32(writer, group.color);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, mPathMapper.Map(group.GetPrefabPath(i)));
                //save subgroup prefab paths
                var subgroupPrefabs = group.GetSubGroupPrefabPaths(i);
                int subGroupPrefabCount = subgroupPrefabs == null ? 0 : subgroupPrefabs.Length;
                writer.Write(subGroupPrefabCount);
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    Utils.WriteString(writer, mPathMapper.Map(subgroupPrefabs[k]));
                }
            }
        }

        short GetPrefabPathIndex(ModelTemplate modelTemplate)
        {
            short index;
            bool found = mModelTemplateIDToPrefabPathIndex.TryGetValue(modelTemplate.id, out index);
            if (!found)
            {
                mPrefabPathStringTable.Add(mPathMapper.Map(modelTemplate.GetLODPrefabPath(0)));
                index = (short)(mPrefabPathStringTable.Count - 1);
                mModelTemplateIDToPrefabPathIndex[modelTemplate.id] = index;
            }
            return index;
        }

        void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }

            //save string table
            int pathCount = mPrefabPathStringTable.Count;
            writer.Write(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                Utils.WriteString(writer, mPrefabPathStringTable[i]);
            }
        }

        void PrepareSaving()
        {
            mPathMapper = new PathMapper();
            mModelTemplateIDToPrefabPathIndex = new Dictionary<int, short>();
            mPrefabPathStringTable = new List<string>();
        }

        PathMapper mPathMapper;
        long mPathMapperPosition;
        Dictionary<int, short> mModelTemplateIDToPrefabPathIndex;
        List<string> mPrefabPathStringTable;
    }
}

#endif