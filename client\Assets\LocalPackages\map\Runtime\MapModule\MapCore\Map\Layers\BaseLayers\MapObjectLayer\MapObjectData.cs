﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    //地图对象的数据基类
    public abstract class MapObjectData : BaseObject, IMapObjectData
    {
        public MapObjectData(int id, Map map, int flag) : base(id, map, (ObjectFlag)flag)
        {
        }

        public override void OnDestroy()
        {
        }

        public bool IsDynamicEntity() { return false; }
        public void OnInit(GameObject obj) { }
        public int GetEntityID() { return id; }
        public bool IgnoreViewport() { return false; }
        public void OnZoomChange(float zoom) { }
        public abstract string GetAssetPath(int lod);
        public abstract Rect GetBounds();
        public abstract ModelTemplate GetModelTemplate();
        public abstract int GetModelTemplateID();

        public abstract Vector3 GetPosition();
        public abstract Vector3 GetScale();
        public abstract Quaternion GetRotation();
        public abstract void SetPosition(Vector3 pos);
        public abstract void SetRotation(Quaternion rot);
        public abstract void SetScale(Vector3 scale);

        public abstract bool IsObjActive();
        public abstract bool SetObjActive(bool active);

        public void OnHide()
        {
        }
        public void OnShow()
        {
        }

        public bool HasFlag(int flag)
        {
            return HasObjectFlag((ObjectFlag)flag);
        }

        public void SetFlag(int flag)
        {
            SetObjectFlag((ObjectFlag)flag);
        }

        public void AddFlag(int flag)
        {
            AddObjectFlag((ObjectFlag)flag);
        }

        public void RemoveFlag(int flag)
        {
            RemoveObjectFlag((ObjectFlag)flag);
        }
        public int GetFlag()
        {
            return (int)flag;
        }
        public virtual int GetModelLODGroupID()
        {
            return 0;
        }
    }
}
