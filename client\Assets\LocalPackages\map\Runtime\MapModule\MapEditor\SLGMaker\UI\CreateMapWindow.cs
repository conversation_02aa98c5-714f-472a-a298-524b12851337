﻿ 



 
 


#if UNITY_EDITOR

using System;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public enum GameMapType
    {
        Empty,
        P2,
        C5,
        K2,
    }

    public class CreateMapWindow : EditorWindow
    {
        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mWidth = EditorGUILayout.FloatField("Map Width", mWidth);
            mHeight = EditorGUILayout.FloatField("Map Height", mHeight);
            GUILayout.EndHorizontal();

            mGameMapType = (GameMapType)EditorGUILayout.EnumPopup("Project", mGameMapType);

            if (GUILayout.Button("Create"))
            {
                if (OnClickCreateMap != null)
                {
                    OnClickCreateMap(mWidth, mHeight, mGameMapType);
                }

                Close();
            }
        }

        public System.Action<float, float, GameMapType> OnClickCreateMap;

        public float mWidth = 7200;
        public float mHeight = 7200;
        GameMapType mGameMapType = GameMapType.P2;
    }
}

#endif