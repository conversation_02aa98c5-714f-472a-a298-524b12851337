﻿ 



 
 



/*
 * created by wzw at 2019.3.10
 */

using UnityEngine;

namespace TFW.Map
{
    public struct Rect2D
    {
        public static Rect2D empty = new Rect2D(int.MaxValue, int.MaxValue, int.MinValue, int.MinValue);

        public Rect2D(int minX, int minY, int maxX, int maxY)
        {
            this.minX = minX;
            this.maxX = maxX;
            this.minY = minY;
            this.maxY = maxY;
        }

        public bool IsEmpty()
        {
            return maxX < minX || maxY < minY;
        }

        public int width { get { return maxX - minX + 1; } }
        public int height { get { return maxY - minY + 1; } }
        public Vector3 center { get { return new Vector3((minX + maxX) * 0.5f, 0, (minY + maxY) * 0.5f); } }
        public static bool operator ==(Rect2D a, Rect2D b)
        {
            return a.minX == b.minX && a.minY == b.minY && a.maxX == b.maxX && a.maxY == b.maxY;
        }
        public static bool operator !=(Rect2D a, Rect2D b)
        {
            return !(a == b);
        }
        public override string ToString()
        {
            return string.Format("{0}, {1}, {2}, {3}", minX, minY, maxX, maxY);
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public bool Contains(int x, int y)
        {
            if (x >= minX && x <= maxX && y >= minY && y <= maxY)
            {
                return true;
            }
            return false;
        }

        public static bool Intersect(Rect2D a, Rect2D b)
        {
            if (a.minX > b.maxX || a.minY > b.maxY || b.minX > a.maxX || b.minY > a.maxY)
            {
                return false;
            }
            return true;
        }

        public bool Intersect(int minX, int minY, int maxX, int maxY)
        {
            if (minX > this.maxX || minY > this.maxY || this.minX > maxX || this.minY > maxY)
            {
                return false;
            }
            return true;
        }

        public void Add(Rect2D f)
        {
            minX = Mathf.Min(minX, f.minX);
            minY = Mathf.Min(minY, f.minY);
            maxX = Mathf.Max(maxX, f.maxX);
            maxY = Mathf.Max(maxY, f.maxY);
        }

        public void Add(int x, int y)
        {
            minX = Mathf.Min(minX, x);
            minY = Mathf.Min(minY, y);
            maxX = Mathf.Max(maxX, x);
            maxY = Mathf.Max(maxY, y);
        }

        public static void GetDifference(Rect2D a, Rect2D b, out Rect2D[] differenceRects)
        {
            Rect2D leftRect;
            Rect2D rightRect;
            if (a.minX < b.minX)
            {
                leftRect = a;
                rightRect = b;
            }
            else
            {
                leftRect = b;
                rightRect = a;
            }

            int minX = Mathf.Min(a.minX, b.minX);
            int maxX = Mathf.Max(a.maxX, b.maxX);
            int minY = Mathf.Min(a.minY, b.minY);
            int maxY = Mathf.Max(a.maxY, b.maxY);
            int dminX = Mathf.Abs(a.minX - b.minX);
            int dmaxX = Mathf.Abs(a.maxX - b.maxX);
            int dminY = Mathf.Abs(a.minY - b.minY);
            int dmaxY = Mathf.Abs(a.maxY - b.maxY);

            var r1 = new Rect2D(leftRect.minX, leftRect.minY, leftRect.minX + dminX - 1, leftRect.maxY);
            var r4 = new Rect2D(rightRect.maxX - dmaxX + 1, rightRect.minY, rightRect.maxX, rightRect.maxY);
            Rect2D r2, r3;
            if (leftRect.minY < rightRect.minY)
            {
                r2 = new Rect2D(r1.maxX + 1, r1.minY, r4.minX - 1, r4.minY - 1);
                r3 = new Rect2D(r1.maxX + 1, r1.maxY + 1, r4.minX - 1, r4.maxY);
            }
            else if (leftRect.minY > rightRect.minY)
            {
                r2 = new Rect2D(r1.maxX + 1, r4.maxY + 1, r4.minX - 1, r1.maxY);
                r3 = new Rect2D(r1.maxX + 1, r4.minY, r4.minX - 1, r1.minY - 1);
            }
            else
            {
                r2 = new Rect2D(r1.maxX + 1, r4.maxY + 1, r4.minX - 1, r1.maxY);
                r3 = new Rect2D(r1.maxX + 1, r1.maxY + 1, r4.minX - 1, r4.maxY);
            }

            differenceRects = new Rect2D[] { r1, r2, r3, r4 };
        }

        public int minX;
        public int minY;
        public int maxX;
        public int maxY;
    }
}