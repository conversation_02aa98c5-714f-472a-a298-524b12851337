﻿ 



 
 


#if UNITY_EDITOR

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace TFW.Map {
    public class MapLayerSelectionWindow : EditorWindow {
        public void Show(string okText, System.Action<List<MapLayerBase>> onClickOK) {
            mOKText = okText;
            mOnClickOK = onClickOK;
        }

        void OnGUI() {
            var map = Map.currentMap;
            int n = map.GetMapLayerCount();
            for (int i = 0; i < n; ++i) {
                var mapLayer = map.GetMapLayerByIndex(i);
                if (mapLayer is ModelLayer || mapLayer is EditorGridModelLayer) {
                    bool isSelected = IsMapLayerSelected(mapLayer.id);
                    //DrawMapLayerItem(mapLayer, isSelected);
                }
            }
            if (GUILayout.Button(mOKText)) {
                if (mOnClickOK != null) {
                    List<MapLayerBase> selectedLayers = new List<MapLayerBase>();
                    for (int i = 0; i < n; ++i) {
                        var mapLayer = map.GetMapLayerByIndex(i);
                        bool isSelected = IsMapLayerSelected(mapLayer.id);
                        if (isSelected) {
                            selectedLayers.Add(mapLayer);
                        }
                    }
                    mOnClickOK(selectedLayers);
                    Close();
                }
            }
        }

        void DrawMapLayerItem(MapLayerBase layer, bool selected) {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(layer.name);
            bool newState = EditorGUILayout.Toggle(selected);
            if (newState != selected) {
                SetSelection(layer.id, newState);
            }
            EditorGUILayout.EndHorizontal();
        }

        void SetSelection(int layerID, bool selected) {
            mSelectedLayers[layerID] = selected;
        }

        bool IsMapLayerSelected(int layerID) {
            bool selected;
            bool found = mSelectedLayers.TryGetValue(layerID, out selected);
            if (found) {
                return selected;
            }
            return false;
        }

        Dictionary<int, bool> mSelectedLayers = new Dictionary<int, bool>();
        string mOKText;
        System.Action<List<MapLayerBase>> mOnClickOK;
    }
}

#endif
#endif