﻿#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        public void RemoveCollisionSegments()
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetCollisionsOfType(collisions, CollisionAttribute.IsConvertedFromCollisionSegment);
            for (int i = 0; i < collisions.Count; ++i)
            {
                RemoveObject(collisions[i].id);
            }
        }

        //将Collisions Segment合并然后加入到collision layer
        //connectionMaxDistance:两条线段的端点能合并的最大距离,超过这个距离就不能合并两条线段
        public void AddObjectFromCollisionSegments()
        {
            EditorUtility.DisplayProgressBar("Remove Collisions Converted From Collision Segment", "Please wait", 0.0f);
            //先删除从collision segment转换的collision
            RemoveCollisionSegments();

            //转换collision
            CombineCollisionSegments(mMaxConnectionDistance);

            EditorUtility.ClearProgressBar();
        }

        void CombineCollisionSegments(float connectionMaxDistance)
        {
            var terrainLayer = Map.currentMap.GetMapLayer<BlendTerrainLayer>();
            if (terrainLayer == null)
            {
                return;
            }

            List<List<Vector3>> allCombinedVertices = new List<List<Vector3>>();
            HashSet<Vector2Int> processed = new HashSet<Vector2Int>();
            List<MapCollisionData> allData = new List<MapCollisionData>();
            while (true)
            {
                List<CollisionSegmentInfo> stack = new List<CollisionSegmentInfo>();
                var segmentsInfo = terrainLayer.GetCollisionSegments();
                for (int i = 0; i < segmentsInfo.Count; ++i)
                {
                    if (processed.Contains(new Vector2Int(segmentsInfo[i].x, segmentsInfo[i].y)) == false)
                    {
                        stack.Add(segmentsInfo[i]);
                        break;
                    }
                }
                if (stack.Count == 0)
                {
                    break;
                }

                List<Vector3> combinedVertices = new List<Vector3>();
                allCombinedVertices.Add(combinedVertices);

                Vector2Int[] neighbourCoordOffset = new Vector2Int[4]
                {
                new Vector2Int(-1, 0),      //left
                new Vector2Int(0, 1),       //up
                new Vector2Int(1, 0),       //right
                new Vector2Int(0, -1),      //down
                };

                while (stack.Count > 0)
                {
                    var info = stack[stack.Count - 1];
                    stack.RemoveAt(stack.Count - 1);

                    processed.Add(new Vector2Int(info.x, info.y));

                    var curOffset = terrainLayer.FromCoordinateToWorldPosition(info.x, info.y);
                    var curVertices = info.segment.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                    AddVertices(combinedVertices, curVertices, curOffset);

                    //check neighbour
                    bool foundValidNeighbour = false;
                    bool check = false;
                    for (int i = 0; i < 4; ++i)
                    {
                        int nx = info.x + neighbourCoordOffset[i].x;
                        int ny = info.y + neighbourCoordOffset[i].y;
                        if (processed.Contains(new Vector2Int(nx, ny)) == false)
                        {
                            var neighbourSegment = terrainLayer.GetCollisionSegment(nx, ny);
                            if (neighbourSegment != null)
                            {
                                check = true;
                                var neighbourVertices = neighbourSegment.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                                var neighbourOffset = terrainLayer.FromCoordinateToWorldPosition(nx, ny);
                                if (CanCombine(curVertices, curOffset, neighbourVertices, neighbourOffset, connectionMaxDistance))
                                {
                                    foundValidNeighbour = true;
                                    stack.Add(new CollisionSegmentInfo(nx, ny, neighbourSegment));
                                    break;
                                }
                            }
                        }
                    }

                    if (!foundValidNeighbour && check)
                    {
                        Debug.LogError($"Can't combine tile {info.x}, {info.y}");
                    }
                }

#if false
                //debug
                var obj = new GameObject($"combined vertices {combinedVertices.Count}");
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(combinedVertices);
#endif

                var outlines = new OutlineData[2];
                outlines[0] = new OutlineData(combinedVertices);
                outlines[1] = new OutlineData(combinedVertices);

                var collisionData = new MapCollisionData(Map.currentMap.nextCustomObjectID, Map.currentMap, outlines, displayVertexRadius, true, CollisionAttribute.SpecialRegion | CollisionAttribute.IsConvertedFromCollisionSegment | CollisionAttribute.IsAutoExpandingObstacle, 0, true);

                allData.Add(collisionData);
            }

            for (int i = 0; i < allData.Count; ++i)
            {
                if (IsHole(allData[i], allCombinedVertices, i))
                {
                    allData[i].Revert(PrefabOutlineType.NavMeshObstacle);
                    allData[i].Revert(PrefabOutlineType.ObjectPlacementObstacle);
                }
                AddObject(allData[i]);
            }
        }

        bool IsHole(MapCollisionData data, List<List<Vector3>> allCombinedVertices, int exceptionIndex)
        {
            var vertices = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            for (int i = 0; i < allCombinedVertices.Count; ++i)
            {
                if (i != exceptionIndex)
                {
                    if (Utils.IsPolygonFullInsideOfPolygon(vertices, allCombinedVertices[i]))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        void AddVertices(List<Vector3> result, List<Vector3> segmentLocalVertices, Vector3 offset)
        {
            if (result.Count == 0)
            {
                for (int i = 0; i < segmentLocalVertices.Count; ++i)
                {
                    var worldPos = segmentLocalVertices[i] + offset;
                    result.Add(worldPos);
                }
            }
            else
            {
                float head2headDistance = Vector3.Distance(result[0], segmentLocalVertices[0] + offset);
                float head2TailDistance = Vector3.Distance(result[0], segmentLocalVertices[segmentLocalVertices.Count - 1] + offset);
                float tail2HeadDistance = Vector3.Distance(result[result.Count - 1], segmentLocalVertices[0] + offset);
                float tail2TailDistance = Vector3.Distance(result[result.Count - 1], segmentLocalVertices[segmentLocalVertices.Count - 1] + offset);
                float min = Mathf.Min(head2headDistance, head2TailDistance, tail2TailDistance, tail2HeadDistance);
                if (Mathf.Approximately(min, head2headDistance))
                {
                    AddVerticesHead2Head(result, segmentLocalVertices, offset);
                }
                else if (Mathf.Approximately(min, head2TailDistance))
                {
                    AddVerticesHead2Tail(result, segmentLocalVertices, offset);
                }
                else if (Mathf.Approximately(min, tail2HeadDistance))
                {
                    AddVerticesTail2Head(result, segmentLocalVertices, offset);
                }
                else
                {
                    AddVerticesTail2Tail(result, segmentLocalVertices, offset);
                }
            }
        }

        void AddVerticesHead2Head(List<Vector3> result, List<Vector3> segmentLocalVertices, Vector3 offset)
        {
            for (int i = 0; i < segmentLocalVertices.Count; ++i)
            {
                var worldPos = segmentLocalVertices[i] + offset;
                if (worldPos != result[0])
                {
                    result.Insert(0, worldPos);
                }
            }
        }

        void AddVerticesHead2Tail(List<Vector3> result, List<Vector3> segmentLocalVertices, Vector3 offset)
        {
            for (int i = segmentLocalVertices.Count - 1; i >= 0; --i)
            {
                var worldPos = segmentLocalVertices[i] + offset;
                if (worldPos != result[0])
                {
                    result.Insert(0, worldPos);
                }
            }
        }

        void AddVerticesTail2Head(List<Vector3> result, List<Vector3> segmentLocalVertices, Vector3 offset)
        {
            for (int i = 0; i < segmentLocalVertices.Count; ++i)
            {
                var worldPos = segmentLocalVertices[i] + offset;
                if (worldPos != result[result.Count - 1])
                {
                    result.Add(worldPos);
                }
            }
        }

        void AddVerticesTail2Tail(List<Vector3> result, List<Vector3> segmentLocalVertices, Vector3 offset)
        {
            for (int i = segmentLocalVertices.Count - 1; i >= 0; --i)
            {
                var worldPos = segmentLocalVertices[i] + offset;
                if (worldPos != result[result.Count - 1])
                {
                    result.Add(worldPos);
                }
            }
        }

        bool CanCombine(List<Vector3> curVertices, Vector3 curOffset, List<Vector3> neighbourVertices, Vector3 neighbourOffset, float connectionMaxDistance)
        {
            var neighbourHeadPos = neighbourVertices[0] + neighbourOffset;
            var neighbourTailPos = neighbourVertices[neighbourVertices.Count - 1] + neighbourOffset;
            var curHeadPos = curVertices[0] + curOffset;
            var curTailPos = curVertices[curVertices.Count - 1] + curOffset;

            float head2headDis = Vector3.Distance(curHeadPos, neighbourHeadPos);
            float head2TailDis = Vector3.Distance(curHeadPos, neighbourTailPos);
            float tail2TailDis = Vector3.Distance(curTailPos, neighbourTailPos);
            float tail2HeadPos = Vector3.Distance(curTailPos, neighbourHeadPos);

            float minDis = Mathf.Min(head2headDis, head2TailDis, tail2TailDis, tail2HeadPos);
            return minDis <= connectionMaxDistance;
        }
    }
}


#endif