﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    [CustomEditor(typeof(PolygonRiverLayerLogic))]
    public partial class PolygonRiverLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as PolygonRiverLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            var layerData = mLogic.layer.GetLayerData();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);

            mEditor = new RiverEditor(mLogic.layer, mLogic, Repaint, RepaintScene);
            var editVertex = new EditRiverVertex(mEditor);
            var createRiver = new CreateRiver(mEditor);
            var moveRiver = new MoveRiver(mEditor);
            var texturePainter = new PaintRiverTexture(mEditor, mLogic.brushSetting);
            var splitRiver = new SplitRiver(mEditor);
            mEditor.AddTool(editVertex);
            mEditor.AddTool(createRiver);
            mEditor.AddTool(moveRiver);
            mEditor.AddTool(texturePainter);
            mEditor.AddTool(splitRiver);
            mEditor.SetActiveTool(mLogic.operation);

            var handlers = mLogic.layer.GetEventHandlers();
            handlers.onOutlineChanged = OnOutlineChanged;
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);

                mEditor.OnDestroy();

                var indicator = SLGMakerEditor.instance.brushIndicator;
                indicator.SetActive(false);
                indicator.SetTexture(null);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                var layerData = mLogic.layer.GetLayerData() as PolygonRiverLayerData;
                var layer = mLogic.layer;

                var operation = (RiverEditorToolType)EditorGUILayout.EnumPopup(new GUIContent("Operation", "当前所选操作"), mLogic.operation);
                if (operation != mLogic.operation)
                {
                    mLogic.operation = operation;
                    mEditor.SetActiveTool(operation);
                    if (operation == RiverEditorToolType.PaintTexture)
                    {
                        layer.HideOutline();
                    }
                    else
                    {
                        layer.ShowOutline(PrefabOutlineType.NavMeshObstacle);
                    }
                }

                mEditor.DrawGUI();

                if (mEditor.selectedObjectID != 0)
                {
                    var data = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;

                    if (GUILayout.Button(new GUIContent("Generate River", "生成河流模型")))
                    {
                        var dlg = EditorUtils.PopupDialog<RiverGenerationDialog>("Set River Parameter");
                        var mtl = AssetDatabase.LoadAssetAtPath<Material>(data.materialPath);
                        if (mtl == null)
                        {
                            mtl = mEditor.layer.defaultRiverMaterial;
                        }
                        dlg.Show(OnGenerate, mtl);
                    }

                    if (GUILayout.Button(new GUIContent("Export OBJ Mesh", "将选中的河流导出为OBJ格式")))
                    {
                        ExportObjMesh(mEditor.selectedObjectID);
                    }

                    data.hideLOD = EditorGUILayout.IntField(new GUIContent("Hide LOD", "是否在某个lod隐藏河流,需要和Map Layer LOD Setting搭配使用"), data.hideLOD);
                }

                if (GUILayout.Button(new GUIContent("Generate All Meshes Only(With Clipper)", "重新生成所有河流的mesh,如果有勾上River Clipper的Map Collision Layer的边框,则该边框会用来裁剪river")))
                {
                    layer.RecreateMesh();
                    ActionManager.instance.Clear();
                }
                if (GUILayout.Button(new GUIContent("Import Mask Texture Data From Exported Assets", "将导出的mask贴图导入到编辑器中,用于修改了_RiverAssets文件夹贴图但没有同步修改editor中mask贴图的情况!")))
                {
                    layer.ImportMaskTextureDataFromExportedAssets();
                    ActionManager.instance.Clear();
                }
                if (GUILayout.Button(new GUIContent("Change All River's Material", "修改所有河流的材质!")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone now!", "Yes", "No"))
                    {
                        mEditor.SetSelection(null, -1);
                        layer.ChangeAllRiversMaterial(mEditor.layer.defaultRiverMaterial);
                        ActionManager.instance.Clear();
                    }
                }

                DrawClipTileList();

                if (mEditor.selectedObjectID != 0)
                {
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button(new GUIContent("Revert", "将河流顶点顺序逆转")))
                    {
                        layer.RevertCollision(mEditor.selectedObjectID, layer.displayType);
                        mEditor.SetSelection(null, -1);
                    }
                    if (GUILayout.Button(new GUIContent("Is CW", "河流顶点是不是顺时针顺序")))
                    {
                        var data = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                        var outline = data.GetOutlineVertices(layer.displayType);
                        bool isCW = Utils.IsPolygonCW(outline);
                        EditorUtility.DisplayDialog("", isCW ? "CW" : "CCW", "OK");
                    }
                    EditorGUILayout.EndHorizontal();
                }

                layer.onlyGenerateLOD0Assets = EditorGUILayout.ToggleLeft(new GUIContent("Only Generate LOD0 Assets", "只生成LOD的河流资源"), layer.onlyGenerateLOD0Assets);
                layerData.generateOBJ = EditorGUILayout.ToggleLeft(new GUIContent("Generate OBJ file", "是否生成河流mesh的OBJ格式文件"), layerData.generateOBJ);
                layerData.useUV2 = EditorGUILayout.ToggleLeft(new GUIContent("Use UV2", "是否生成河流mesh的顶点uv2数据"), layerData.useUV2);
                layerData.expandingTileCount = EditorGUILayout.IntField(new GUIContent("Expanding Tile Count", "在使用地表tile裁剪河流时,将海岸tile扩大一圈,会让生成的河流mesh顶点更少,但是河流与地表的overdraw更高"), layerData.expandingTileCount);
                mLogic.setting.Draw("Map Layer LOD Setting", mEditor.layer.layerData.lodConfig, LODDisplayFlag.ShaderLOD | LODDisplayFlag.UseRenderTexture, null, null);

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField(new GUIContent("River Count", "河流数量"), new GUIContent(layer.objectCount.ToString()));
                EditorGUILayout.LabelField("Width", layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", layer.GetTotalHeight().ToString());
                EditorGUILayout.LabelField("you can use paint texture function only if river shader has _RiverMask texture property!");
                EditorGUILayout.EndVertical();
            }
        }

        void DrawClipTileList()
        {
            mShowClipTypeList = EditorGUILayout.Foldout(mShowClipTypeList, new GUIContent("Ground Tile Clip List", "非clip的地表tile type"));
            if (mShowClipTypeList)
            {
                var exceptionList = mLogic.layer.groundTileTypeClipExceptions;
                int n = EditorGUILayout.IntField("Count", exceptionList.Count);
                n = Math.Max(0, n);
                if (n != exceptionList.Count)
                {
                    mLogic.layer.SetGroundTileTypeClipExceptionCount(n);
                }
                for (int i = 0; i < n; ++i)
                {
                    exceptionList[i] = EditorGUILayout.IntField(new GUIContent("Exception Tile Type", "地表tile类型,即tile的索引"), exceptionList[i]);
                }
            }
        }

        bool OnGenerate(RiverGenerationParameter param)
        {
            if (mEditor.selectedObjectID != 0)
            {
                var act = new ActionGenerateRiverMesh(mEditor.layer.id, mEditor.selectedObjectID, param);
                ActionManager.instance.PushAction(act);

                mEditor.RepaintGUI();
                return true;
            }
            return false;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            mEditor.Update(currentEvent);

            if (Event.current.button == 1 && Event.current.type == EventType.MouseDown)
            {
                var tool = mEditor.GetActiveTool();
                mEditor.DrawMenu();
            }

            mEditor.DrawScene();

            mLogic.layer.UpdateColor(mLogic.layer.displayType);

            DoRepaint();
        }

        void SetOutlineType(PrefabOutlineType type)
        {
            mLogic.layer.displayType = type;
            mLogic.layer.ShowOutline(type);
        }

        void OnOutlineChanged(int dataID, PrefabOutlineType type)
        {
            RepaintScene();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void RepaintScene()
        {
            mSceneDirty = true;
        }

        void DoRepaint()
        {
            if (mSceneDirty)
            {
                mSceneDirty = false;
                SceneView.RepaintAll();
            }
        }

        void CreateLocalMeshData(Mesh globalMesh, out Vector3[] vertices, out Vector2[] uvs, out Color[] colors, out int[] indices)
        {
            vertices = globalMesh.vertices;
            var bounds = globalMesh.bounds;
            var center = bounds.center;
            //convert to local vertices
            for (int i = 0; i < vertices.Length; ++i)
            {
                vertices[i] = vertices[i] - center;
            }
            indices = globalMesh.triangles;
            uvs = globalMesh.uv;
            colors = globalMesh.colors;
            if (colors == null || colors.Length == 0)
            {
                colors = new Color[vertices.Length];
                for (int i = 0; i < colors.Length; ++i)
                {
                    colors[i] = Color.white;
                }
            }
        }

        void ExportObjMesh(int riverID)
        {
            var layer = mEditor.layer;
            var river = layer.GetRiver(riverID);
            if (river.sections.Count == 1)
            {
                string filePath = EditorUtility.SaveFilePanel("Export obj file", "", "river_mesh", "obj");
                if (!string.IsNullOrEmpty(filePath))
                {
                    GameObject riverMeshObject = layer.GetRiverMeshObject(river.id, 0);
                    UnityEngine.Debug.Assert(riverMeshObject != null);

                    var mesh = riverMeshObject.GetComponent<MeshFilter>().sharedMesh;
                    Vector3[] vertices;
                    Vector2[] uvs;
                    Color[] colors;
                    int[] indices;
                    CreateLocalMeshData(mesh, out vertices, out uvs, out colors, out indices);
                    OBJExporter.Export(filePath, vertices, uvs, colors, indices);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Only support 1 section now!", "OK");
            }
        }

        PolygonRiverLayerLogic mLogic;
        bool mSceneDirty = false;
        bool mShowClipTypeList = false;
        RiverEditor mEditor;
    }
}

#endif