﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map {
    [Black]
    public class ActionAddModel : EditorAction {
        public ActionAddModel(int layerID, int objectID, int modelTemplateID, Vector3 position, Quaternion rotation, Vector3 scale) {
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = modelTemplateID;
            mRotation = rotation;
            mPosition = position;
            mScale = scale;
            var modelTemplate = Map.currentMap.FindObject(modelTemplateID) as ModelTemplate;
            var prefabName = Path.GetFileName(modelTemplate.GetLODPrefabPath(0));
            mDescription = string.Format("add {0}", prefabName);
        }

        public override bool Do() {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null) {
                var layer = map.GetMapLayerByID(mLayerID) as ModelLayer;
                if (layer != null) {
                    layer.AddObject(modelTemplate.GetLODPrefabPath(0), mPosition, mRotation, mScale, mObjectID);
                    return true;
                }
            }
            return false;
        }

        public override bool Undo() {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as ModelLayer;
            if (layer != null) {
                layer.RemoveObject(mObjectID);
                return true;
            }

            return false;
        }

        public override string description { get { return mDescription; } }

        protected int mLayerID;
        protected int mObjectID;
        protected int mModelTemplateID;
        protected Quaternion mRotation;
        protected Vector3 mPosition;
        protected Vector3 mScale;
        protected string mDescription;
    }
}

#endif