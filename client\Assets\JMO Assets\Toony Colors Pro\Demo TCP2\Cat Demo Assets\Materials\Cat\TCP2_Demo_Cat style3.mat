%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Cat style3
  m_Shader: {fileID: 4800000, guid: 121194756ac93f54b912b16e3f5bcdd9, type: 3}
  m_ShaderKeywords: TCP2_OUTLINE_TEXTURED _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 08c21915d31a7bb4f9d200a43171b54f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 2800000, guid: 866e8039d8795af4e971ef43a7f00761, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _RampOtherLights
      second:
        m_Texture: {fileID: 2800000, guid: abc83c21d57d4a74cabe12065fa1a8b2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ThresholdTex
      second:
        m_Texture: {fileID: 2800000, guid: ebbb2bec16a72fe48b8463e62d47d076, type: 3}
        m_Scale: {x: 4, y: 4}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _EnableConstSizeOutline
      second: 0
    - first:
        name: _EnableTexturedOutline
      second: 1
    - first:
        name: _EnableZSmooth
      second: 0
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _HSV_H
      second: 0
    - first:
        name: _HSV_S
      second: 0
    - first:
        name: _HSV_V
      second: 0
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Offset1
      second: 0
    - first:
        name: _Offset2
      second: 0
    - first:
        name: _Outline
      second: 0.3
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.1
    - first:
        name: _RampSmoothOtherLights
      second: 0.5
    - first:
        name: _RampThreshold
      second: 0.65
    - first:
        name: _RampThresholdOtherLights
      second: 0.5
    - first:
        name: _RimMax
      second: 1
    - first:
        name: _RimMin
      second: 0.376
    - first:
        name: _Shadow_HSV_H
      second: 0
    - first:
        name: _Shadow_HSV_S
      second: 0
    - first:
        name: _Shadow_HSV_V
      second: 0
    - first:
        name: _Shininess
      second: 0.23
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecBands
      second: 2
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _TexLod
      second: 0
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _ZSmooth
      second: -0.5
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    - first:
        name: __outline_gui_dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _DiffTint
      second: {r: 0.42464498, g: 1, b: 0.31617647, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _OutlineColor
      second: {r: 0.63235295, g: 0.63235295, b: 0.63235295, a: 1}
    - first:
        name: _RimColor
      second: {r: 0.44852942, g: 0, b: 0, a: 0.734}
    - first:
        name: _SColor
      second: {r: 0.7794118, g: 0.51005626, b: 0.51005626, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.32352942, g: 0.32352942, b: 0.32352942, a: 1}
