﻿ 



 
 



/*
 * created by wzw at 2019.6.4
 */

using System;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ObjectPool<T> where T : class
    {
        public ObjectPool(int capacity, Func<T> creator, Action<T> deconstructor = null)
        {
            mObjects = new List<T>(capacity);
            mCreator = creator;
            mDeconstructor = deconstructor;
        }

        public void OnDestroy()
        {
            Clear();
        }

        public T Require()
        {
            T obj = null;

            int n = mObjects.Count;
            if (n > 0)
            {
                obj = mObjects[n - 1];
                mObjects.RemoveAt(n - 1);
            }

            if (System.Object.ReferenceEquals(obj, null))
            {
                obj = mCreator();
            }

            return obj;
        }

        public void Release(T obj)
        {
            mDeconstructor?.Invoke(obj);
            mObjects.Add(obj);
        }

        public void Clear()
        {
            mObjects.Clear();
        }

        public List<T> objects { get { return mObjects; } }

        List<T> mObjects;
        Func<T> mCreator;
        Action<T> mDeconstructor;
    }
}
