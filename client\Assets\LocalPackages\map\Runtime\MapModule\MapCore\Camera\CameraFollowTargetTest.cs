﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    //测试资源建筑的LOD表现
    public class CameraFollowTargetTest : MonoBehaviour
    {
        //创建一个测试实例
        public static void Test(Vector3 pos)
        {
            CameraFollowTargetTest test;

            if (mTestObject == null)
            {
                mTestObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                test = mTestObject.AddComponent<CameraFollowTargetTest>();
            }
            else
            {
                test = mTestObject.GetComponent<CameraFollowTargetTest>();
            }

            test.Create(pos);
        }

        void Create(Vector3 cityPos)
        {
            transform.position = cityPos;
            mEndPos = Vector3.up * 10000;
            MapCameraMgr.StartFollowTarget(cityPos.x, cityPos.y, cityPos.z, 0, 3);
        }

        void Update()
        {
            var newPos = Vector3.MoveTowards(transform.position, mEndPos, speed * Time.deltaTime);
            MapCameraMgr.UpdateFollowTarget(newPos.x, newPos.y, newPos.z);
            transform.position = newPos;
        }

        Vector3 mEndPos;
        public float speed = 20;
        static GameObject mTestObject;
    }
}
