﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.CircleBorderLayerData LoadCircleBorderLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.CircleBorderLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCircleBorderDataV1(reader);
            }

            var config = LoadCircleBorderLayerLODConfigV1(reader);

            int combineBorderLOD = reader.ReadInt32();
            //------------------version 1 end-----------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadCircleBorderLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layer = new config.CircleBorderLayerData(layerID, name, offset, config, width, height, objects, combineBorderLOD);
            return layer;
        }

        config.CircleBorderData LoadCircleBorderDataV1(BinaryReader reader)
        {
            var modelData = new config.CircleBorderData();

            modelData.SetID(reader.ReadInt32());

            modelData.position = Utils.ReadVector3(reader);
            var isDefaultRotation = reader.ReadBoolean();
            if (!isDefaultRotation)
            {
                modelData.rotation = Utils.ReadQuaternion(reader);
            }
            var isDefaultScale = reader.ReadBoolean();
            if (!isDefaultScale)
            {
                modelData.scale = Utils.ReadVector3(reader);
            }

            modelData.modelTemplateID = reader.ReadInt32();
            modelData.isAlwaysVisibleAtHigherLODs = reader.ReadBoolean();
            return modelData;
        }

        config.MapLayerLODConfig LoadCircleBorderLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadCircleBorderLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
