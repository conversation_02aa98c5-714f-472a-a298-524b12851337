﻿







//#define NTIMER_DEBUG       //打开这个宏会使NTimer输出更多调试信息，可以用来帮助Debug和性能优化。

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;

namespace TFW
{
    public static class NTimer
    {
        #region 常量

        /// <summary>
        /// 无效Timer对应的ID
        /// </summary>
        public const int TIMER_ID_NULL = 0;

        /// <summary>
        /// 无限时长
        /// </summary>
        public const long INF = long.MaxValue;

        #endregion

        #region 公开字段
        /// <summary>
        /// Timer对象池的大小
        /// </summary>
        public static int TimerPoolSize { get; set; } = 200;

        /// <summary>
        /// 运行Timer检查的时间间隔（秒）
        /// </summary>
        public static float TimerCheckInterval { get; set; } = 15f;

        /// <summary>
        /// 判定为长时运行的Timer的阈值时间（秒）
        /// </summary>
        public static float LongRunningTimerThreshold { get; set; } = 30f;
        #endregion

        #region 内部字段

        private static Dictionary<int, Timer> m_Timers = new Dictionary<int, Timer>();
        private static Dictionary<int, Timer> m_TimersToAdd = new Dictionary<int, Timer>();
        private static HashSet<int> m_TimersToRemove = new HashSet<int>();
        private static List<Timer> m_TimerPool = new List<Timer>(TimerPoolSize);
        private static int m_TimerIDCounter;

#if NTIMER_DEBUG
        private static int m_Debug_PeakActiveTimerCount;
        private static float m_Debug_TimerCheckTimer;
        private static StringBuilder m_Debug_TimerCheckStringBuilder = new StringBuilder();
#endif
        #endregion

        #region 私有方法

        private static void FixedUpdate(float deltaTime)
        {
            //if (SpritePropUtils.pause)
            //    return;
            foreach (Timer it in m_Timers.Values)
            {
                if (it.Active)
                {
                    if (it.Time >= it.EndTime)
                    {
                        if (!it.IsRepeat || (it.IsRepeat && (it.IntervalTime >= it.Interval || (it.Delay > 0 && it.IntervalTime >= it.Delay))))
                        {
                            //P.B(it.CallBack, "NTimer last tick");
                            it.Delay = 0;
                            try
                            {
                                it.CallBack.Invoke();
                            }
                            catch (Exception ex)
                            {
                                if (it.CallBack?.Target != null)
                                {
                                    D.Error?.Log($"Target?.Name={it.CallBack?.Target},Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                }
                                else
                                {
                                    D.Error?.Log($"Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                }
                            }
                            //P.E();
                        }

                        it.Destroy();
                    }
                    else
                    {
                        if (it.IsRepeat)
                        {
                            if (it.Delay > 0 && it.IntervalTime >= it.Delay)
                            {
                                //P.B(it.CallBack, "NTimer tick");
                                it.Delay = 0;
                                try
                                {
                                    it.CallBack.Invoke();
                                }
                                catch (Exception ex)
                                {
                                    if (it.CallBack?.Target != null)
                                    {
                                        D.Error?.Log($"Target?.Name={it.CallBack?.Target},Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                    }
                                    else
                                    {
                                        D.Error?.Log($"Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                    }
                                }
                                it.IntervalTime = 0;
                                //P.E();
                            }

                            if (it.IntervalTime >= it.Interval)
                            {
                                //P.B(it.CallBack, "NTimer tick");
                                it.Delay = 0;
                                try
                                {
                                    it.CallBack.Invoke();
                                }
                                catch (Exception ex)
                                {
                                    if (it.CallBack?.Target != null)
                                    {
                                        D.Error?.Log($"Target?.Name={it.CallBack?.Target},Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                    }
                                    else
                                    {
                                        D.Error?.Log($"Method?.Name={it.CallBack?.Method?.Name},Exception=" + ex);
                                    }
                                }
                                it.IntervalTime = 0;
                                //P.E();
                            }

                            it.IntervalTime += deltaTime;
                        }
                    }

                    it.Time += deltaTime;
                }

#if NTIMER_DEBUG
                it.Debug_TotalFixedUpdateTime += deltaTime;
#endif
            }

#if NTIMER_DEBUG
            Debug_TimerCheck(deltaTime);
#endif
        }

#if NTIMER_DEBUG
        /// <summary>
        /// 检查所有Timer。并对检查出的部分问题打印Log。
        /// </summary>
        private static void Debug_TimerCheck(float deltaTime)
        {
            // 每隔TIMER_CHECK_INTERVAL检查一次
            m_Debug_TimerCheckTimer += deltaTime;
            if (m_Debug_TimerCheckTimer < TimerCheckInterval)
            {
                return;
            }
            m_Debug_TimerCheckTimer = 0f;

            int longRunningTimerCount = 0;

            m_Debug_TimerCheckStringBuilder.Clear();
            m_Debug_TimerCheckStringBuilder.AppendFormat("[NTimer] Checking Timers. TotalActiveTimers={0}, PeakActiveTimers={1}, PooledTimerCount={2}\n",
                                                          m_Timers.Count, m_Debug_PeakActiveTimerCount, m_TimerPool.Count);

            // 检查每个激活的Timer
            foreach (var timer in m_Timers.Values)
            {
                // 检查长时运行的Timer
                if (timer.Debug_TotalFixedUpdateTime > LongRunningTimerThreshold)
                {
                    longRunningTimerCount++;

                    if (timer.CallBack != null)
                    {
                        m_Debug_TimerCheckStringBuilder.AppendFormat("LongRunningTimer({0}s)：{1}::{2}\n", timer.Debug_TotalFixedUpdateTime, timer.CallBack.Target ?? "(static)", timer.CallBack.Method);
                    }
                    else
                    {
                        m_Debug_TimerCheckStringBuilder.AppendFormat("LongRunningTimer({0}s)：ID={1}，Callback=null，Creator={2}\n", timer.Debug_TotalFixedUpdateTime, timer.ID, timer.Debug_CreatorInfo);
                    }
                }
            }

            if (m_Debug_TimerCheckStringBuilder.Length > 0)
            {
                D.Warning?.Log(m_Debug_TimerCheckStringBuilder.ToString());
            }
        }
#endif

        private static void LateUpdate(float deltaTime)
        {
            //if (SpritePropUtils.pause)
            //    return;
            if (m_TimersToAdd.Count > 0)
            {
                foreach (var kv in m_TimersToAdd)
                {
#if NTIMER_DEBUG
                    if (m_Timers.TryGetValue(kv.Key, out var tmpVal) && tmpVal != kv.Value)
                    {
                        D.Warning?.Log($"[NTimer] Same TimerID, but different Timer instance. TimerID = {kv.Key}");
                    }
#endif

                    m_Timers[kv.Key] = kv.Value;
                }
                m_TimersToAdd.Clear();

#if NTIMER_DEBUG
                m_Debug_PeakActiveTimerCount = Math.Max(m_Timers.Count, m_Debug_PeakActiveTimerCount);
#endif
            }

            if (m_TimersToRemove.Count > 0)
            {
                foreach (var key in m_TimersToRemove)
                {
                    // 如果Timer池未满，则视情况将要移除的Timer放到池里去。
                    if (m_TimerPool.Count < TimerPoolSize)
                    {
                        if (m_Timers.TryGetValue(key, out var timerToRemove))
                        {
                            m_Timers.Remove(key);

                            if (timerToRemove.AutoPooled)
                            {
                                m_TimerPool.Add(timerToRemove);
                            }
                        }
                    }
                    // 否则，直接丢弃要移除的Timer。
                    else
                    {
                        m_Timers.Remove(key);
                    }
                }
                m_TimersToRemove.Clear();
            }
        }

        /// <summary>
        /// 获取新的Timer ID
        /// </summary>
        /// <returns></returns>
        private static int GetNewTimerID()
        {
            return ++m_TimerIDCounter;
        }

        /// <summary>
        /// 初始化Timer实例，重置所有字段的值
        /// </summary>
        /// <param name="timer"></param>
        private static void InitTimer(Timer timer)
        {
            timer.ID = GetNewTimerID();
            timer.Time = 0;
            timer.IntervalTime = 0;
            timer.Interval = 0;
            timer.Delay = 0;
            timer.EndTime = 0;
            timer.IsRepeat = false;
            timer.Active = false;
            timer.AutoPooled = false;
            timer.IsDestroyed = false;
            timer.CallBack = null;

#if NTIMER_DEBUG
            timer.Debug_TotalFixedUpdateTime = 0;

            var stackTrace = new StackTrace(2, false);
            var creatorMethod1 = stackTrace.GetFrame(0).GetMethod();
            var creatorMethod2 = stackTrace.GetFrame(1).GetMethod();
            timer.Debug_CreatorInfo = $"{creatorMethod1.DeclaringType}::{creatorMethod1}; {creatorMethod2.DeclaringType}::{creatorMethod2}";
#endif
        }

        /// <summary>
        /// （内部方法，外部不要使用。）
        /// 将传入的Timer添加到待添加队列。
        /// 注：传入的Timer会从待删除队列中移除（如果已存在）。
        /// </summary>
        /// <param name="timer"></param>
        internal static void PendingAdd(Timer timer)
        {
            //从待删除队列中移除
            m_TimersToRemove.Remove(timer.ID);

            //添加到待添加队列
            m_TimersToAdd[timer.ID] = timer;
        }

        /// <summary>
        /// （内部方法，外部不要使用。）
        /// 将传入的Timer添加到待移除队列。
        /// 注：传入的Timer会从待添加队列中移除（如果已存在）。
        /// </summary>
        /// <param name="timer"></param>
        internal static void PendingRemove(Timer timer)
        {
            //从待添加队列中移除
            m_TimersToAdd.Remove(timer.ID);

            //添加到待删除队列
            m_TimersToRemove.Add(timer.ID);
        }

        #endregion

        #region 公开方法

        private static bool m_Inited = false;

        public static void Init()
        {
            if (m_Inited)
            {
                return;
            }

            m_Inited = true;
            m_Timers.Clear();
            m_TimersToAdd.Clear();
            m_TimersToRemove.Clear();
            m_TimerPool.Clear();
            FrameUpdateMgr.RegisterFixedUpdate("NTimer", FixedUpdate);
            FrameUpdateMgr.RegisterLateUpdate("NTimer", LateUpdate);
        }

        public static void DeInit()
        {
            FrameUpdateMgr.UnregisterLateUpdate("NTimer");
            FrameUpdateMgr.UnregisterFixedUpdate("NTimer");
            m_TimerPool.Clear();
            m_TimersToRemove.Clear();
            m_TimersToAdd.Clear();
            m_Timers.Clear();
            m_Inited = false;
        }

        /// <summary>
        /// 新建Timer。先取池里的，池里没有会新建。
        /// </summary>
        /// <returns></returns>
        public static Timer New()
        {
            Timer timer;

            if (m_TimerPool.Count > 0)
            {
                var lastIdx = m_TimerPool.Count - 1;
                timer = m_TimerPool[lastIdx];
                m_TimerPool.RemoveAt(lastIdx);
            }
            else
            {
                timer = new Timer();
            }

            InitTimer(timer);
            m_TimersToAdd.Add(timer.ID, timer);

            return timer;
        }
        public static Timer GetTimerById(int id)
        {
            if (m_TimersToAdd != null)
            {
                if (m_TimersToAdd.TryGetValue(id, out var timer))
                {
                    return timer;
                }

            }
            return null;
        }
        /// <summary>
        /// 开启单次计时器
        /// </summary>
        /// <param name="second"></param>
        /// <param name="callback"></param>
        /// <returns>新建的Timer的ID</returns>
        public static int CountDown(float second, Action callback)
        {
            Timer timer = New();
            timer.AutoPooled = true;
            timer.StartCountdown(second, callback);
            return timer.ID;
        }

        /// <summary>
        /// 开启单次计时器（不放回对象池，外部可以持有Timer任意长时间）
        /// </summary>
        /// <param name="second"></param>
        /// <param name="callback"></param>
        /// <returns>新建的Timer实例</returns>
        public static Timer CountDownNoPool(float second, Action callback)
        {
            Timer timer = New();
            timer.StartCountdown(second, callback);
            return timer;
        }

        /// <summary>
        /// 开启单次计时器（不放回对象池，外部可以持有Timer任意长时间）
        /// <para>如果传入的Timer引用为null，则新建一个，否则重用。</para>
        /// </summary>
        /// <param name="second"></param>
        /// <param name="callback"></param>
        public static void CountDownNoPool(ref Timer timer, float second, Action callback)
        {
            if (timer == null)
            {
                timer = New();
            }
            else
            {
                timer.Stop();
            }

            timer.StartCountdown(second, callback);
        }

        ///// <summary>
        ///// 开启循环计时器
        ///// </summary>
        ///// <param name="second"></param>
        ///// <param name="interval"></param>
        ///// <param name="callback"></param>
        ///// <returns>新建的Timer的ID</returns>
        //public static int Tick(float second, float interval, Action callback)
        //{
        //    Timer timer = New();
        //    timer.AutoPooled = true;
        //    timer.StartTick(second, interval, callback);

        //    return timer.ID;
        //}

        /// <summary>
        /// 开启循环计时器（不放回对象池，外部可以持有Timer任意长时间）
        /// <para>如果传入的Timer引用为null，则新建一个，否则重用。</para>
        /// </summary>
        /// <param name="second"></param>
        /// <param name="interval"></param>
        /// <param name="callback"></param>
        public static void TickNoPool(ref Timer timer, float second, float interval, Action callback)
        {
            if (timer == null)
            {
                timer = New();
            }
            else
            {
                timer.Stop();
            }

            timer.StartTick(second, interval, callback);
        }
        
        /// <summary>
        /// 开启循环计时器
        /// </summary>
        /// <param name="second"></param>
        /// <param name="interval"></param>
        /// <param name="callback"></param>
        /// <returns>新建的Timer的ID</returns>
        public static int Tick(float second, float interval, Action callback)
        {
            Timer timer = New();
            timer.AutoPooled = true;
            timer.StartTick(second, interval, callback);

            return timer.ID;
        }
        
        /// <summary>
        /// 销毁Timer
        /// </summary>
        /// <param name="timerID">要停止的Timer的ID</param>
        public static void Destroy(int timerID)
        {
            if (m_Timers.TryGetValue(timerID, out var timerActive))
            {
                timerActive.Destroy();
            }
            else if (m_TimersToAdd.TryGetValue(timerID, out var timerToAdd))
            {
                timerToAdd.Destroy();
            }
        }

        /// <summary>
        /// 安全销毁传入的Timer引用。
        /// 调用该方法会使传入的Timer被销毁，并且传入的引用被置null。
        /// </summary>
        /// <param name="timer"></param>
        public static void SafeDestroy(ref Timer timer)
        {
            if (timer != null)
            {
                var t = timer;
                timer = null;
                t.Destroy();
            }
        }

        #endregion

        #region 内部类型

        public class Timer
        {
            /// <summary>
            /// Timer唯一标识
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public int ID;

            /// <summary>
            /// 经过的总时间
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public float Time;

            /// <summary>
            /// 周期时间，本周期内经过的时间
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public float IntervalTime;

            /// <summary>
            /// 周期
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public float Interval;

            /// <summary>
            /// 延迟 只生效一次一般用于循环使用
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public float Delay;

            /// <summary>
            /// <para>结束时间</para>
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public float EndTime;

            /// <summary>
            /// <para>是否重复触发</para>
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public bool IsRepeat;

            /// <summary>
            /// <para>回调</para>
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public bool Active;

            /// <summary>
            /// <para>回调</para>
            /// <para>注：执行结束后是否自动回收到池。</para>
            /// </summary>
            public bool AutoPooled;

            /// <summary>
            /// <para>是否已经被销毁掉，从刷新队列中移除。</para>
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public bool IsDestroyed;

            /// <summary>
            /// <para>回调</para>
            /// <para>注：只读字段，使用者不要设置该字段的值。</para>
            /// </summary>
            public Action CallBack;

            #region 调试参数
#if NTIMER_DEBUG
            /// <summary>
            /// 调试参数：总共经过的Update时间，即被FixedUpdate处理过的总时间。
            /// </summary>
            internal float Debug_TotalFixedUpdateTime;
            /// <summary>
            /// 调试参数：实例创建者的信息（StackTrace）。
            /// </summary>
            internal string Debug_CreatorInfo;
#endif
            #endregion
        }

        #endregion
    }

    /// <summary>
    /// NTimer扩展方法。
    /// </summary>
    public static class NTimerExtensions
    {
        public static void StartCountdown(this NTimer.Timer timer, float second, Action callback)
        {
            //D.Error?.Log($"[{timer.ID}] =>{callback?.Target.ToString()}");

            timer.EndTime = second;
            timer.CallBack = callback;
            timer.IsRepeat = false;
            Restart(timer);
        }

        public static void StartTick(this NTimer.Timer timer, float second, float interval, Action callback)
        {
            timer.EndTime = second;
            timer.IsRepeat = true;
            timer.Interval = interval;
            timer.CallBack = callback;
            Restart(timer);
        }
        public static void UpdateTimer(this NTimer.Timer timer, float newSecond, float newInterval)
        {
            timer.EndTime = newSecond;
            timer.Interval = newInterval;
        }
        /// <summary>
        /// 带有第一次延迟
        /// </summary>
        /// <param name="second"></param>
        /// <param name="interval"></param>
        /// <param name="delay"></param>
        /// <param name="callback"></param>
        public static void StartTick(this NTimer.Timer timer, float second, float interval, float delay, Action callback)
        {
            timer.EndTime = second;
            timer.IsRepeat = true;
            timer.Interval = interval;
            timer.Delay = delay;
            timer.CallBack = callback;
            Restart(timer);
        }

        public static void Start(this NTimer.Timer timer)
        {
            timer.IsDestroyed = false;
            timer.Active = true;
            NTimer.PendingAdd(timer);
        }

        public static void Pause(this NTimer.Timer timer)
        {
            timer.Active = false;
        }
        public static void Resume(this NTimer.Timer timer)
        {
            timer.Active = true;
        }
        /// <summary>
        /// <para>停止Timer。</para>
        /// <para>注：调用这个方法会将传入的Timer从NTimer的更新列表中移除，但不会回收该NTimer实例（就是后续逻辑还可以用）。</para>
        /// <para>注：如果不再使用一个Timer，应调用Destroy()方法，这样Timer对象池可以回收再利用。</para>
        /// </summary>
        /// <param name="timer"></param>
        public static void Stop(this NTimer.Timer timer)
        {
            Pause(timer);
            timer.Time = 0;
            timer.IntervalTime = 0;

            NTimer.PendingRemove(timer);

#if NTIMER_DEBUG
            timer.Debug_TotalFixedUpdateTime = 0;
#endif
        }

        public static void Restart(this NTimer.Timer timer)
        {
            Stop(timer);
            Start(timer);
        }

        /// <summary>
        /// 获取计时器剩余时间
        /// </summary>
        /// <returns></returns>
        public static float RemainingTime(this NTimer.Timer timer)
        {
            if (!timer.Active)
            {
                return -1f;
            }

            if (timer.IsRepeat)
            {
                return timer.Interval + timer.Delay - timer.IntervalTime;
            }

            return timer.EndTime - timer.Time;
        }

        /// <summary>
        /// <para>销毁传入的Timer实例。</para>
        /// <para>这个方法会同时调用Stop()停止传入的Timer。</para>
        /// <para>注：调用这个方法后，传入的Timer不应继续被使用，否则会出现无法预测的行为。</para>
        /// </summary>
        /// <param name="timer"></param>
        public static void Destroy(this NTimer.Timer timer)
        {
            Stop(timer);

            // TODO：由于外部存在Timer被Destroy后仍继续使用的情况，因此目前还不能放回对象池，否则会引发问题。
            //timer.AutoPooled = true;       // 已经被销毁的Timer对象视为不再被使用，可以回收到池里。

            timer.CallBack = null; // 解除对Callback的引用
            timer.IsDestroyed = true;

            NTimer.PendingRemove(timer);
        }

        public static string ToDebugString(this NTimer.Timer timer)
        {
            return $@"
            -----------------------------
            [ID]            {timer.ID}
            [Time]          {timer.Time}
            [End Time]      {timer.EndTime}
            [Interval]      {timer.Interval}
            [Interval Time] {timer.IntervalTime}
            [Callback]      {timer.CallBack}
            [Is Repeat]     {timer.IsRepeat}
            [Active]        {timer.Active}
            [Delay]         {timer.Delay}
            -----------------------------
            ";
        }
    }
}