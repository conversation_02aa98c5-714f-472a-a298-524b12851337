﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理动态增加,删除并且每帧可移动的物体, 物体尺寸不能超过一个grid的大小
    public class DynamicObjectLayerData : MapLayerData
    {
        public DynamicObjectLayerData(MapLayerDataHeader header, MapLayerLODConfig lodConfig, Map map) : base(header, lodConfig, map)
        {
            mVisibilityCheck = new bool[header.rows * header.cols];
            mRegions = new DynamicObjectRegion[header.rows * header.cols];
            for (int i = 0; i < mRegions.Length; ++i)
            {
                mRegions[i] = new DynamicObjectRegion();
            }
            mInvalidRegion = new DynamicObjectRegion();

            //temp code
#if UNITY_EDITOR && TFW_MAP_DEBUG
            mGridViewer = new GridViewer("Dynamic Object Layer Grids", 14, 14, 10, 10, header.tileWidth * header.cols, header.tileHeight * header.rows, header.tileWidth, header.tileHeight, new Color(0, 1, 0, 0.2f), new Color(1, 0, 0, 0.2f), true, 0.3f, mMap.root.transform, false);
#endif
        }

        public override void OnDestroy()
        {
            //temp code,没有调用BaseObject.RemoveObject删除object
            mRegions = null;
            mInvalidRegion = null;

#if UNITY_EDITOR && TFW_MAP_DEBUG
            mGridViewer.OnDestroy();
#endif
        }

        public override void RefreshObjectsInViewport()
        {
            throw new System.NotImplementedException();
        }

        public void SetObjectActiveStateChangeCallback(System.Action<IDynamicObjectData, int> callback)
        {
            mObjectActionStateChangeCallback = callback;
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                UpdateObjectActiveState(lodChanged, mLastViewport, newViewport);

                mLastViewport = newViewport;
            }

            return lodChanged;
        }

        //使用差集来判断矩形的可见性,非常快速
        void UpdateObjectActiveState(bool lodChanged, Rect oldViewport, Rect newViewport)
        {
            var oldMin = FromWorldPositionToCoordinate(new Vector3(oldViewport.xMin, 0, oldViewport.yMin));
            var oldMax = FromWorldPositionToCoordinate(new Vector3(oldViewport.xMax, 0, oldViewport.yMax));
            var newMin = FromWorldPositionToCoordinate(new Vector3(newViewport.xMin, 0, newViewport.yMin));
            var newMax = FromWorldPositionToCoordinate(new Vector3(newViewport.xMax, 0, newViewport.yMax));

            if (lodChanged)
            {
                //bool isLastLODHidden = lodConfig.lodConfigs[lastLOD].hideObject;
                //if (!isLastLODHidden)
                //{
                for (int y = oldMin.y; y <= oldMax.y; ++y)
                {
                    for (int x = oldMin.x; x <= oldMax.x; ++x)
                    {
                        int idx = y * mCols + x;
                        SetGridActive(idx, false, lastLOD);
                    }
                }
                //}

                //bool isCurrentLODHidden = lodConfig.lodConfigs[currentLOD].hideObject;
                //if (!isCurrentLODHidden)
                //{
                for (int y = newMin.y; y <= newMax.y; ++y)
                {
                    for (int x = newMin.x; x <= newMax.x; ++x)
                    {
                        int idx = y * mCols + x;
                        SetGridActive(idx, true, currentLOD);
                    }
                }
                //}
            }
            else
            {
                var dlx = newMin.x - oldMin.x;
                var dly = newMin.y - oldMin.y;
                var drx = newMax.x - oldMax.x;
                var dry = newMax.y - oldMax.y;

                int newXSize = newMax.x - newMin.x + 1;
                int newYSize = newMax.y - newMin.y + 1;
                int oldXSize = oldMax.x - oldMin.x + 1;
                int oldYSize = oldMax.y - oldMin.y + 1;

                //check x direction
                if (dlx < 0)
                {
                    int len = Mathf.Min(Mathf.Abs(dlx), newXSize);
                    for (int y = oldMin.y; y <= oldMax.y; ++y)
                    {
                        for (int i = 0; i < len; ++i)
                        {
                            int x = i + newMin.x;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                mVisibilityCheck[idx] = true;
                                mNextVisibleGrids.Add(idx);
                            }
                        }
                    }
                }
                else if (dlx > 0)
                {
                    dlx = Mathf.Min(dlx, oldXSize);
                    for (int y = oldMin.y; y <= oldMax.y; ++y)
                    {
                        for (int i = 0; i < dlx; ++i)
                        {
                            int x = i + oldMin.x;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                mVisibilityCheck[idx] = true;
                                mNextInvisibleGrids.Add(idx);
                            }
                        }
                    }
                }

                if (drx < 0)
                {
                    int len = Mathf.Min(Mathf.Abs(drx), oldXSize);
                    for (int y = newMin.y; y <= newMax.y; ++y)
                    {
                        for (int i = 0; i < len; ++i)
                        {
                            int x = i + newMax.x + 1;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
#if DEBUG
                                if (mVisibilityCheck[idx] == true)
                                {
                                    Debug.Assert(false);
                                }
#endif
                                mVisibilityCheck[idx] = true;
                                mNextInvisibleGrids.Add(idx);
                            }
                        }
                    }
                }
                else if (drx > 0)
                {
                    drx = Mathf.Min(drx, newXSize);
                    for (int y = newMin.y; y <= newMax.y; ++y)
                    {
                        int sx = newMax.x - drx + 1;
                        for (int x = sx; x <= newMax.x; ++x)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
#if DEBUG
                                Debug.Assert(mVisibilityCheck[idx] == false);
#endif
                                mVisibilityCheck[idx] = true;
                                mNextVisibleGrids.Add(idx);
                            }
                        }
                    }
                }

                //check y direction, it's the reflection of x direction
                if (dly < 0)
                {
                    int len = Mathf.Min(Mathf.Abs(dly), newYSize);
                    for (int x = oldMin.x; x <= oldMax.x; ++x)
                    {
                        for (int i = 0; i < len; ++i)
                        {
                            int y = newMin.y + i;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                if (mVisibilityCheck[idx] == false)
                                {
                                    mNextVisibleGrids.Add(idx);
                                }
                            }
                        }
                    }
                }
                else if (dly > 0)
                {
                    dly = Mathf.Min(dly, oldYSize);
                    for (int x = oldMin.x; x <= oldMax.x; ++x)
                    {
                        for (int i = 0; i < dly; ++i)
                        {
                            int y = i + oldMin.y;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                if (mVisibilityCheck[idx] == false)
                                {
                                    mNextInvisibleGrids.Add(idx);
                                }
                            }
                        }
                    }
                }

                if (dry < 0)
                {
                    int len = Mathf.Min(Mathf.Abs(dry), oldYSize);
                    for (int x = newMin.x; x <= newMax.x; ++x)
                    {
                        for (int i = 0; i < len; ++i)
                        {
                            int y = newMax.y + i + 1;
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                if (mVisibilityCheck[idx] == false)
                                {
                                    mNextInvisibleGrids.Add(idx);
                                }
                            }
                        }
                    }
                }
                else if (dry > 0)
                {
                    dry = Mathf.Min(dry, newYSize);
                    for (int x = newMin.x; x <= newMax.x; ++x)
                    {
                        int sy = newMax.y - dry + 1;
                        for (int y = sy; y <= newMax.y; ++y)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                var idx = y * mCols + x;
                                if (mVisibilityCheck[idx] == false)
                                {
                                    mNextVisibleGrids.Add(idx);
                                }
                            }
                        }
                    }
                }

                for (int i = 0; i < mNextInvisibleGrids.Count; ++i)
                {
                    mVisibilityCheck[mNextInvisibleGrids[i]] = false;
                    SetGridActive(mNextInvisibleGrids[i], false, currentLOD);
                }
                for (int i = 0; i < mNextVisibleGrids.Count; ++i)
                {
                    mVisibilityCheck[mNextVisibleGrids[i]] = false;
                    SetGridActive(mNextVisibleGrids[i], true, currentLOD);
                }
                mNextInvisibleGrids.Clear();
                mNextVisibleGrids.Clear();

#if DEBUG
                //temp code
                for (int i = 0; i < mVisibilityCheck.Length; ++i)
                {
                    Debug.Assert(mVisibilityCheck[i] == false);
                }
#endif
            }
        }

        void SetGridActive(int gridIndex, bool active, int lod)
        {
            //#if DEBUG
            //            //temp code
            //            int row = gridIndex / mCols;
            //            int col = gridIndex % mCols;
            //            if (active)
            //            {
            //                Debug.Log(string.Format("Show Grid {0}_{1}", col, row));
            //            }
            //            else
            //            {
            //                Debug.Log(string.Format("Hide Grid {0}_{1}", col, row));
            //            }
            //#endif

            bool isObjectHidden = lodConfig.lodConfigs[lod].hideObject;
            if (isObjectHidden)
            {
                active = false;
            }
            var objects = mRegions[gridIndex].objects;
            for (int i = 0; i < objects.Count; ++i)
            {
                SetObjectActive(objects[i], active, lod);
            }
        }

        void SetObjectActive(IDynamicObjectData obj, bool active, int lod)
        {
            bool change = obj.SetObjActive(active);
            if (change)
            {
                mObjectActionStateChangeCallback(obj, lod);
            }
        }

        public override bool Contains(int objectID)
        {
            return mAllObjects.ContainsKey(objectID);
        }

        public void AddObject(IDynamicObjectData obj)
        {
            var regionIdx = GetRegionIndex(obj.GetPosition());
            if (regionIdx >= 0 && regionIdx < mRegions.Length)
            {
                mRegions[regionIdx].AddObject(obj);
                obj.SetGridIndex(regionIdx);
            }
            else
            {
                mInvalidRegion.AddObject(obj);
                obj.SetGridIndex(-1);
            }
            mAllObjects.Add(obj.GetEntityID(), obj);

            var mapData = mMap.data;
            bool isVisible = false;

            bool isObjectHidden = lodConfig.lodConfigs[currentLOD].hideObject;
            if (isObjectHidden)
            {
                isVisible = false;
            }
            else
            {
                isVisible = IsInViewRange(obj, mMap.viewport);
            }

            SetObjectActive(obj, isVisible, currentLOD);
            //SetObjectScale(data);
        }

        public bool IsInViewRange(IDynamicObjectData data, Rect viewport)
        {
            var bounds = data.GetBounds();
            var dataMin = bounds.min;
            var dataMax = bounds.max;

            return !(dataMax.x < viewport.xMin || dataMax.y < viewport.yMin || viewport.xMax < dataMin.x || viewport.yMax < dataMin.y);
        }

        public void RemoveObject(IDynamicObjectData obj)
        {
            var regionIdx = obj.GetGridIndex();
            if (regionIdx >= 0 && regionIdx < mRegions.Length)
            {
                mRegions[regionIdx].RemoveObject(obj);
            }
            else
            {
                mInvalidRegion.RemoveObject(obj);
            }
            mAllObjects.Remove(obj.GetEntityID());
        }

        public void ToggleOverlappedObjectsVisibility(Vector3 center, float radius, bool visible)
        {
            mOverlappedObjects.Clear();
            GetOverlappedObjects(center, radius, mOverlappedObjects);
            for (int i = 0; i < mOverlappedObjects.Count; ++i)
            {
                mOverlappedObjects[i].SetObjActive(visible);
            }
        }

        void GetOverlappedObjects(Vector3 center, float radius, List<IDynamicObjectData> objects)
        {
            mOverlappedRegions.Clear();
            GetOverlappedRegions(center, radius, mOverlappedRegions);
            for (int i = 0; i < mOverlappedRegions.Count; ++i)
            {
                mOverlappedRegions[i].GetOverlappedObjects(center, radius, objects);
            }
        }

        //扩大一圈region,保证object相交的正确性,这里有个限制是object的radius不能大于region size的一半
        //TODO一个优化是如果事先知道object的最大半径maxR,当测试圆在一个region去掉maxR的后的内部时就可以不扩大一圈region了
        void GetOverlappedRegions(Vector3 center, float radius, List<DynamicObjectRegion> overlappedRegions)
        {
            float minX = center.x - radius;
            float maxX = center.x + radius;
            float minZ = center.z - radius;
            float maxZ = center.z + radius;

            Vector2Int minCoord = GetRegionCoordinate(new Vector3(minX, 0, minZ));
            Vector2Int maxCoord = GetRegionCoordinate(new Vector3(maxX, 0, maxZ));

            minCoord.x = Mathf.Clamp(minCoord.x - 1, 0, mCols - 1);
            minCoord.y = Mathf.Clamp(minCoord.y - 1, 0, mRows - 1);
            maxCoord.x = Mathf.Clamp(maxCoord.x + 1, 0, mCols - 1);
            maxCoord.y = Mathf.Clamp(maxCoord.y + 1, 0, mRows - 1);

            for (int y = minCoord.y; y <= maxCoord.y; ++y)
            {
                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                {
                    overlappedRegions.Add(mRegions[y * mCols + x]);
                }
            }
        }

        int GetRegionIndex(Vector3 pos)
        {
            int x = (int)(pos.x / mTileWidth);
            int y = (int)(pos.z / mTileHeight);
            return y * mCols + x;
        }

        Vector2Int GetRegionCoordinate(Vector3 pos)
        {
            int x = (int)(pos.x / mTileWidth);
            int y = (int)(pos.z / mTileHeight);
            return new Vector2Int(x, y);
        }

        public override bool isGameLayer { get { return true; } }

        System.Action<IDynamicObjectData, int> mObjectActionStateChangeCallback;

        DynamicObjectRegion[] mRegions;
        //用于快速判断一个物体是否已经被设置过Active状态了
        bool[] mVisibilityCheck;
        //保存地图外的物体
        DynamicObjectRegion mInvalidRegion;

        float mLastZoom;
        Rect mLastViewport;

        //中间计算的变量,尽量避免产生GC
        List<int> mNextVisibleGrids = new List<int>(2000);
        List<int> mNextInvisibleGrids = new List<int>(2000);
        //中间计算的变量,尽量避免产生GC
        List<IDynamicObjectData> mOverlappedObjects = new List<IDynamicObjectData>();
        //中间计算的变量,尽量避免产生GC
        List<DynamicObjectRegion> mOverlappedRegions = new List<DynamicObjectRegion>();
        Dictionary<int, IDynamicObjectData> mAllObjects = new Dictionary<int, IDynamicObjectData>(1000);
#if UNITY_EDITOR && TFW_MAP_DEBUG
        //temp code
        GridViewer mGridViewer;
#endif
    }
}
