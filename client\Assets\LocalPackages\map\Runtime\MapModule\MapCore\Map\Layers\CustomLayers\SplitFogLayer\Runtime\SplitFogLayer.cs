﻿ 



 
 



using UnityEngine;
using TFW.Map.config;
using System.IO;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public partial class SplitFogLayer : MapLayerBase
    {
        public SplitFogLayer(Map map) : base(map) { }

        //加载地图层
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.SplitFogLayerData;
            int rows = sourceLayer.rows;
            int cols = sourceLayer.cols;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth, sourceLayer.tileHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            mLayerData = new SplitFogLayerData(header, config, map, sourceLayer.tiles, sourceLayer.fogTilePrefabPaths, sourceLayer.fogHeight, sourceLayer.fogLOD1PrefabPath, sourceLayer.selectionMaterialPath, sourceLayer.fogMaskTexPropertyName);
            mLayerView = new SplitFogLayerView(mLayerData, false);
            mLayerView.active = layerData.active;
            mLayerData.SetCallbacks(mLayerView.OnTileVisibilityChange, mLayerView.OnRectFogVisibilityChange, mLayerView.OnTileDataChange, mLayerView.OnFogHeightChange, mLayerView.OnCreateRectFog, mLayerView.OnRemoveRectFog, mLayerView.OnLODChange, mLayerView.OnMaskChange, mLayerView.OnSelectionChange, mLayerView.OnHideSelection);

            //默认开始所有雾组成一个大mesh
            if (!map.isEditorMode)
            {
                mLayerData.InitSplit(sourceLayer.tiles);
            }
            else
            {
                if (sourceLayer.tiles != null)
                {
                    for (int y = 0; y < rows; ++y)
                    {
                        for (int x = 0; x < cols; ++x)
                        {
                            int idx = y * cols + x;
                            var tile = sourceLayer.tiles[idx];
                            if (tile != 0)
                            {
                                PushTile(x, y, tile);
                            }
                        }
                    }
                }
            }

            map.AddMapLayer(this);

#if UNITY_EDITOR
            mGridViewer = new GridViewer("split fog grid view", 0, 0, 20, 20, 7200, 7200, 18, 18, new Color(1, 0, 0, 0.1f), new Color(0, 1, 0, 0.1f), true, mLayerData.fogHeight + 0.5f, null, false);
#endif
        }

        //卸载地图数据
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        //直接设置tile的信息,不使用拼接的规则
        public void SetTile(int x, int y, int tileIndex)
        {
            if (tileIndex == 0)
            {
                //清理tile
                mLayerData.ClearTile(x, y);
            }
            else
            {
                //直接设置这个tile使用的拼接后的数据
                mLayerData.PushTile(x, y, tileIndex, true);
            }
        }

        //拼接tile,让该tile可以和四周的tile无缝衔接
        public void PushTile(int x, int y, int tileIndex)
        {
            mLayerData.PushTile(x, y, tileIndex, false);
        }

        public bool GetPushTileResult(int x, int y, int tileIndex, out int combinedTileIndex)
        {
            return mLayerData.GetPushTileResult(x, y, tileIndex, out combinedTileIndex);
        }

        //撤销拼接操作,该操作过后地表的tile还是衔接好的
        public void PopTile(int x, int y, int tileIndex)
        {
            mLayerData.PopTile(x, y, tileIndex);
        }

        public void PopTiles(Vector3 pos)
        {
            mLayerData.PopTiles(pos);
        }

        public void PopTiles(int x, int y)
        {
            var pos = mLayerData.FromCoordinateToWorldPosition(x, y);
            PopTiles(pos);
        }

        public void PopTiles(int x, int y, int width, int height)
        {
            Debug.Assert(width > 1 && height > 1);

            for (int i = 0; i < height - 1; ++i)
            {
                for (int j = 0; j < width - 1; ++j)
                {
                    var pos = mLayerData.FromCoordinateToWorldPosition(x + j + 1, y + i + 1);
                    PopTiles(pos);
                }
            }
        }

        //获取tile数据
        public int GetTile(int x, int y)
        {
            return mLayerData.GetTileType(x, y);
        }

        //返回地图的根game object
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override int id { get { return mLayerData.id; } }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            return lodChanged;
        }

        public void UpdateMask()
        {
            if (mLayerData.currentLOD > 0)
            {
                mLayerData.UpdateMask();
            }
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            Debug.Assert(false, "todo");
            return false;
        }

        //加载完地图时刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void Select(int x, int y, int width, int height)
        {
            mLayerData.Select(x, y, width, height);
        }

        public void HideSelection()
        {
            mLayerData.HideSelection();
        }

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }

        //地图层的名称
        public override string name
        {
            get
            {
                return mLayerData?.name;
            }
            set {
                mLayerData.name = value;
            }
        }
        //地图层的格子类型
        public override GridType gridType { get { return GridType.Rectangle; } }
        //x方向上格子的数量
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        //格子的宽
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        //格子的高
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        //地图层的偏移值
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public bool asyncLoading { get { return mLayerView.asyncLoading; } set { mLayerView.asyncLoading = value; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;

        public SplitFogLayerData layerData { get { return mLayerData; } }
        public MapLayerView layerView { get { return mLayerView; } }

        //拼接地表层的tile数据
        SplitFogLayerData mLayerData;
        //地表层的模型管理
        SplitFogLayerView mLayerView;
#if UNITY_EDITOR
        GridViewer mGridViewer;
#endif
    }
}