﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class TerrainTileIndicator
    {
        public TerrainTileIndicator()
        {
            mIndicator = CreateGameObject(Color.white);
        }

        public void OnDestroy()
        {
            if (mIndicator != null)
            {
                GameObject.DestroyImmediate(mIndicator);
                mIndicator = null;
            }

            if (mIndicatorMaterial != null)
            {
                GameObject.DestroyImmediate(mIndicatorMaterial);
                mIndicatorMaterial = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mIndicator != null)
            {
                mIndicator.transform.position = pos;
            }
        }

        public void SetScale(float scale)
        {
            mIndicator.transform.localScale = new Vector3(scale, scale, scale);
        }

        public void SetColor(Color32 color)
        {
            if (mIndicator != null)
            {
                mIndicatorMaterial.color = color;
            }
        }

        public void SetActive(bool active)
        {
            if (mIndicator != null)
            {
                mIndicator.SetActive(active);
            }
        }

        public void ShowPart(int idx, bool visible)
        {
            if (idx >= 0 && idx < 4)
            {
                if (mIndicator != null)
                {
                    mIndicator.transform.GetChild(idx).gameObject.SetActive(visible);
                }
            }
        }

        GameObject CreateGameObject(Color32 color)
        {
            GameObject root = new GameObject("Indicator Root");
            root.transform.parent = SLGMakerEditor.instance.gameObject.transform;
            root.transform.rotation = Quaternion.Euler(90, 0, 0);
            Utils.HideGameObject(root);

            for (int i = 0; i < 4; ++i)
            {
                var gameObject = GameObject.CreatePrimitive(PrimitiveType.Quad);
                var renderer = gameObject.GetComponent<MeshRenderer>();
                if (mIndicatorMaterial == null)
                {
                    mIndicatorMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                    mIndicatorMaterial.color = color;
                }
                renderer.sharedMaterial = mIndicatorMaterial;
                Utils.HideGameObject(gameObject);
                gameObject.transform.SetParent(root.transform, false);
                gameObject.transform.localScale = Vector3.one * 0.5f;

                Vector3 pos = Vector3.zero;
                if (i == 0)
                {
                    pos = new Vector3(-0.25f, -0.25f, 0);
                }
                else if (i == 1)
                {
                    pos = new Vector3(0.25f, -0.25f, 0);
                }
                else if (i == 2)
                {
                    pos = new Vector3(-0.25f, 0.25f, 0);
                }
                else if (i == 3)
                {
                    pos = new Vector3(0.25f, 0.25f, 0);
                }
                gameObject.transform.localPosition = pos;
            }

            return root;
        }

        GameObject mIndicator;
        Material mIndicatorMaterial;
    }
}


#endif