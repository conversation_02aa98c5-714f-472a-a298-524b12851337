﻿ 



 
 

#if UNITY_EDITOR

using System;
using UnityEngine;
using UnityEditor;
using System.IO;
using Cysharp.Threading.Tasks;
using Object = UnityEngine.Object;

namespace TFW.Map
{
    public class EditorResourceMgr : IMapResourceMgr
    {
        public T LoadResource<T>(string path) where T : Object
        {
            var asset = AssetDatabase.LoadAssetAtPath<T>(path);
            if (asset is GameObject)
            {
                return Object.Instantiate(asset);
            }
            return asset;
        }

        public GameObject LoadGameObject(string path)
        {
            if (path.EndsWith(MapCoreDef.FAKE_PREFAB_EXT))
            {
                //this is a fake prefab
                return Map.currentMap.fakePrefabManager.LoadGameObject(path);
            }

            var asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (asset == null)
            {
                Debug.LogError(string.Format("asset {0} not found!", path));
            }
            if (asset != null)
            {
                var obj = Object.Instantiate(asset);
                obj.name = asset.name;
                return obj;
            }
            return asset;
        }

        public GameObject LoadPrefab(string path)
        {
            if (path.EndsWith(MapCoreDef.FAKE_PREFAB_EXT))
            {
                //this is a fake prefab
                var prefab = Map.currentMap.fakePrefabManager.LoadPrefab(path);
                if (prefab != null)
                {
                    return prefab.gameObject;
                }
                return null;
            }

            var asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (asset == null && path != MapCoreDef.SPHERE_OBJECT_NAME)
            {
                Debug.LogError(string.Format("asset {0} not found!", path));
            }
            return asset;
        }

        public Material LoadMaterial(string path)
        {
            return AssetDatabase.LoadAssetAtPath<Material>(path);
        }

        public Texture2D LoadTexture(string path)
        {
            return AssetDatabase.LoadAssetAtPath<Texture2D>(path);
        }

        public byte[] LoadTextBytes(string assetPath, bool unloadAssets)
        {
            var texta = LoadResource<TextAsset>(assetPath);
            var text_bytes = texta == null ? null : texta.bytes;
            return text_bytes;
        }

        public Stream LoadTextStream(string assetPath, bool unloadTextAsset)
        {
            var bytes = LoadTextBytes(assetPath, unloadTextAsset);
            if (bytes != null && bytes.Length > 0)
            {
                return new MemoryStream(bytes);
            }
            return null;
        }

        public void LoadTextStreamAsync(string assetPath, System.Action<Stream> onLoaded)
        {
            var bytes = LoadTextBytes(assetPath, true);
            if (bytes != null && bytes.Length > 0)
            {
                onLoaded(new MemoryStream(bytes));
            }
        }

        public bool Exists(string assetPath)
        {
            var obj = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            return obj!=null;
        }
		
	    public void UnloadAsset(string assetPath)
        {
            // Editor资源无需卸载
        }


        public UniTask<byte[]> LoadTextBytesAsync(string assetPath)
        {
            var texta = LoadResource<TextAsset>(assetPath);
            var text_bytes = texta == null ? null : texta.bytes;
            return UniTask.FromResult(text_bytes);
        }

        public UniTask<string> LoadTextStringAsync(string assetPath)
        {
            var texta = LoadResource<TextAsset>(assetPath);
            var text_bytes = texta == null ? string.Empty : texta.text;
            return UniTask.FromResult(text_bytes);
        }

        public UniTask<Stream> LoadTextStreamAsync(string assetPath)
        {
            var bytes = LoadTextBytes(assetPath, true);
            Stream stream = null;
            if (bytes != null && bytes.Length > 0)
            {
                stream = new MemoryStream(bytes); 
            }
            return UniTask.FromResult(stream);
        }

        public void LoadResourceAsync<T>(string path, string defaultAsset, System.Action<bool, T> action) where T : Object
        {
            var obj = AssetDatabase.LoadAssetAtPath<T>(path);
            action?.Invoke(false, obj);
        }

        public void LoadMaterialAsync(string path, string defaultAsset, System.Action<bool, Material> action)
        {
            var obj = AssetDatabase.LoadAssetAtPath<Material>(path);
            action?.Invoke(false, obj);
        }

        public void LoadGameObjectAsync(string path, string defaultAsset, System.Action<bool, GameObject> action)
        {
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            action?.Invoke(false, obj);
        }

        public void LoadPrefabAsync(string path, string defaultAsset, System.Action<bool, GameObject> action)
        {
            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            action?.Invoke(false,obj);
        }
        UniTask<T> IMapResourceMgr.LoadResourceAsync<T>(string path, string defaultAsset)
        {
            return UniTask.FromResult(AssetDatabase.LoadAssetAtPath<T>(path));
        }

        UniTask<GameObject> IMapResourceMgr.LoadGameObjectAsync(string path, string defaultAsset)
        {
            return UniTask.FromResult(AssetDatabase.LoadAssetAtPath<GameObject>(path));
        }

        UniTask<Material> IMapResourceMgr.LoadMaterialAsync(string path, string defaultAsset)
        {
           return  UniTask.FromResult(AssetDatabase.LoadAssetAtPath<Material>(path)); 
        }
    }
}


#endif