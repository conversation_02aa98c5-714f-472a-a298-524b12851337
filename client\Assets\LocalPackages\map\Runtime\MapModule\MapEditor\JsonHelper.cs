﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class JsonHelper
    {
        public static void WriteVec2(Dictionary<string, object> obj, string key, Vector2 val)
        {
            float[] arr = new float[2] { val.x, val.y };
            obj[key] = arr;
        }

        public static void WriteVec3(Dictionary<string, object> obj, string key, Vector3 val)
        {
            float[] arr = new float[3] { val.x, val.y, val.z };
            obj[key] = arr;
        }

        public static void WriteVec4(Dictionary<string, object> obj, string key, Vector4 val)
        {
            float[] arr = new float[4] { val.x, val.y, val.z, val.w };
            obj[key] = arr;
        }

        public static void WriteColor(Dictionary<string, object> obj, string key, Color val)
        {
            float[] arr = new float[4] { val.r, val.g, val.b, val.a };
            obj[key] = arr;
        }

        public static bool ReadBoolean(Dictionary<string, object> configDict, string name, bool defaultValue)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultValue;
            }
            return System.Convert.ToBoolean(valObj);
        }

        public static string ReadString(Dictionary<string, object> configDict, string name, string defaultValue)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultValue;
            }
            return valObj as string;
        }

        public static float ReadFloat(Dictionary<string, object> configDict, string name, float defaultVal)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultVal;
            }
            return System.Convert.ToSingle(valObj);
        }

        public static int ReadInt(Dictionary<string, object> configDict, string name, int defaultVal)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultVal;
            }
            return System.Convert.ToInt32(valObj);
        }

        public static List<string> ReadStringList(Dictionary<string, object> configDict, string name)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return null;
            }
            List<object> settings = valObj as List<object>;
            if (settings == null)
            {
                return null;
            }
            List<string> result = new List<string>();
            for (int i = 0; i < settings.Count; ++i)
            {
                var s = settings[i] as string;
                if (s == null)
                {
                    return null;
                }
                result.Add(s);
            }
            return result;
        }

        /// <summary>
        /// 读取颜色数据信息
        /// </summary>
        /// <param name="configDict"></param>
        /// <param name="name"></param>
        /// <param name="defaultVal"></param>
        /// <returns></returns>
        public static Color ReadColor(Dictionary<string, object> configDict, string name, Color defaultVal)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultVal;
            }
            List<object> settings = valObj as List<object>;
            if (settings == null)
            {
                return defaultVal;
            }
            if (settings.Count == 3)
            {
                return new Color(System.Convert.ToSingle(settings[0]),
                    System.Convert.ToSingle(settings[1]),
                    System.Convert.ToSingle(settings[2]));
            }
            else if (settings.Count == 4)
            {
                return new Color(System.Convert.ToSingle(settings[0]),
                    System.Convert.ToSingle(settings[1]),
                    System.Convert.ToSingle(settings[2]),
                    System.Convert.ToSingle(settings[3]));
            }

            return defaultVal;
        }



        public static Vector3 ReadVector3(Dictionary<string, object> configDict, string name, Vector3 defaultVal)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultVal;
            }
            List<object> settings = valObj as List<object>;
            if (settings == null)
            {
                return defaultVal;
            }
            if (settings.Count == 3)
            {
                return new Vector3(System.Convert.ToSingle(settings[0]),
                    System.Convert.ToSingle(settings[1]),
                    System.Convert.ToSingle(settings[2]));
            }
            return defaultVal;
        }

        public static Vector2 ReadVector2(Dictionary<string, object> configDict, string name, Vector2 defaultVal)
        {
            object valObj;
            bool found = configDict.TryGetValue(name, out valObj);
            if (!found)
            {
                return defaultVal;
            }
            List<object> settings = valObj as List<object>;
            if (settings == null)
            {
                return defaultVal;
            }
            if (settings.Count == 2)
            {
                return new Vector2(System.Convert.ToSingle(settings[0]),
                    System.Convert.ToSingle(settings[1]));
            }
            return defaultVal;
        }
    };
}