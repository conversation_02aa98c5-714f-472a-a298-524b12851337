﻿ 



 
 



#if UNITY_EDITOR

using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionChangeTerrainHeights : EditorAction
    {
        public ActionChangeTerrainHeights(int layerID, int tileX, int tileY, int minX, int minY, int maxX, int maxY, int resolution)
        {
            mLayerID = layerID;
            mMinX = minX;
            mMaxX = maxX;
            mMinY = minY;
            mMaxY = maxY;
            mTileX = tileX;
            mTileY = tileY;
            mResolution = resolution;
            mDescription = string.Format("change terrain layer heights");
        }

        void RecordHeights(ref List<float> heights)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            int horizontalTileCount = mMaxX - mMinX + 1;
            int verticalTileCount = mMaxY - mMinY + 1;
            heights = new List<float>(verticalTileCount * horizontalTileCount);

            var tile = layer.GetTile(mTileX, mTileY);
            for (int i = mMinY; i <= mMaxY; ++i)
            {
                for (int j = mMinX; j <= mMaxX; ++j)
                {
                    heights.Add(tile.GetHeight(j, i));
                }
            }
        }

        public void Begin()
        {
            RecordHeights(ref mOldHeights);
        }

        public void End()
        {
            RecordHeights(ref mNewHeights);
        }

        bool SetHeights(List<float> heights)
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                layer.SetHeights(mTileX, mTileY, mMinX, mMinY, mMaxX, mMaxY, mResolution, heights, true, false);   
                return true;
            }
            return false;
        }

        public override bool Do()
        {
            return SetHeights(mNewHeights);
        }

        public override bool Undo()
        {
            return SetHeights(mOldHeights);
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        public int tileX { get { return mTileX; } }
        public int tileY { get { return mTileY; } }

        List<float> mOldHeights;
        List<float> mNewHeights;
        int mMinX;
        int mMinY;
        int mMaxX;
        int mMaxY;
        int mTileX;
        int mTileY;
        int mLayerID;
        int mResolution;
        string mDescription;
    }
}

#endif