﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class CameraColliderVertexMover
    {
        public CameraColliderVertexMover(int layerID, int dataID, int vertexIdx, PrefabOutlineType outlineType)
        {
            Debug.Assert(mAction == null);
            mAction = new ActionMoveCameraColliderVertex(layerID, dataID, vertexIdx, outlineType);
        }

        public void Stop(Vector3 pos)
        {
            mAction.SetEndPosition(pos);
            if (mAction.isMoved)
            {
                ActionManager.instance.PushAction(mAction, true, false);
            }
            mAction = null;
        }

        ActionMoveCameraColliderVertex mAction;
    }
}


#endif