﻿Shader "Sprites/Common"
{
    Properties
    {
        [PerRendererData]_MainTex ("Texture", 2D) = "white" {}
		_Cutoff("Alpha cutoff", Range(0, 1)) = 0.2
		
    	[Toggle] DayNightToggle("Day Night Toggle", Float) = 0
    }
    SubShader
    {
        Tags { "Queue" = "AlphaTest" }
        LOD 200

        Pass
        {
		    ZWrite On ZTest On
			Blend SrcAlpha OneMinusSrcAlpha

            CGPROGRAM
            #pragma multi_compile _ DAYNIGHTTOGGLE_GLOBAL_ON
			#pragma multi_compile_local _ DAYNIGHTTOGGLE_ON
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
			#include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
			
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
			half _Cutoff;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);

				clip(col.a - _Cutoff);
				
				#ifdef DAYNIGHTTOGGLE_GLOBAL_ON
        	        #ifdef DAYNIGHTTOGGLE_ON
                        col.rgb = ApplyDayNightLut(col.rgb);
			        #endif
		        #endif
		        
                return col;
            }
            ENDCG
        }
    }
}
