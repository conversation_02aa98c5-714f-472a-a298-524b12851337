fileFormatVersion: 2
guid: 6f16c19a73fd8a644ae4e41c0aa203f6
timeCreated: **********
licenseType: Store
ShaderImporter:
  defaultTextures:
  - _NoTileNoiseTex: {fileID: 2800000, guid: af5515bfe14f1af4a9b8b3bf306b9261, type: 3}
  - _Ramp: {fileID: 2800000, guid: ccad9b0732473ee4e95de81e50e9050f, type: 3}
  userData: USER,FWORLDSPACE_UV,FVERTEX_SIN_WAVES,FVSW_2,FVSW_WORLDPOS,FVERTEX_SIN_NORMALS,FSMOOTH_FOAM,FUV_SIN_WAVES,FUSW_NORMAL,FSPECULAR_TOON,FDEPTH_BUFFER_FOAM,FDEPTH_BUFFER_COLOR,FEMISSION_PULSE,FEMISSION_COLOR,FUSW_SECOND_TEX,FEM_PULSE_MASK,<PERSON><PERSON><PERSON>2,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>ASK1,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>2,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>_<PERSON>SK,FMASK_MAINTEX,FC<PERSON>OR<PERSON>S<PERSON>,FCOLORMASK_SEPARATE,FMASK3,fnolightmap,KCOLORMASK_CHANNEL:.r,KUV_mask1:Main
    Tex UV,KEMISSION_MASK_CHANNEL:.r,KEM_PULSE_MASK:mask2,KEM_PULSE_MASK_CHANNEL:.g,KUV_mask2:Independent
    UV,KEMISSION_MASK:mask1,KRIM_MASK_CHANNEL:.r,KRIM_MASK:mainTex,KUV_mask3:Main
    Tex UV,KEM_PULSE_MULT:4,KCOLORMASK:mask3,KSHADER_TARGET:3.0,chEB1EE343,cSM:30,cCT:water,cCF:TCP2_ShaderTemplate_Water
  assetBundleName: 
  assetBundleVariant: 
