﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionAddMapCollision : EditorAction
    {
        public ActionAddMapCollision(int layerID, int dataID, List<Vector3> vertices, bool isExtendable, CollisionAttribute attribute, int type, float obstacleExpandRadius)
        {
            mObstacleExpandRadius = obstacleExpandRadius;
            mLayerID = layerID;
            mDataID = dataID;
            mVertices = new List<Vector3>(vertices.Count);
            mVertices.AddRange(vertices);
            mIsExtendable = isExtendable;
            mAttribute = attribute;
            mType = type;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            var outlineData = new OutlineData(mVertices);
            var outlineDatas = new OutlineData[2];
            outlineDatas[0] = outlineData;
            if (mObstacleExpandRadius != 0)
            {
                //直接在创建时就扩展
                var expandedVertices = PolygonAlgorithm.ExpandPolygon(mObstacleExpandRadius, mVertices)[0];
                outlineDatas[1] = new OutlineData(expandedVertices);
            }
            var data = new MapCollisionData(mDataID, Map.currentMap, outlineDatas, layer.displayVertexRadius, mIsExtendable, mAttribute, mType, true);
            layer.AddObject(data);

            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveObject(mDataID);
            return true;
        }

        int mLayerID;
        int mDataID;
        bool mIsObstacle;
        bool mIsExtendable;
        float mObstacleExpandRadius;
        CollisionAttribute mAttribute;
        int mType;
        List<Vector3> mVertices;
    }
}

#endif