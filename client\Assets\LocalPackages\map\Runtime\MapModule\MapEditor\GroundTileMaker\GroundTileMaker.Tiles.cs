﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        public class GroundTileLOD
        {
            public GroundTileLOD(GameObject gameObject, List<MaskTexture> maskTextures)
            {
                this.gameObject = gameObject;
                this.maskTextures = maskTextures;
            }

            public GameObject gameObject;
            public List<MaskTexture> maskTextures = new List<MaskTexture>();
            public bool assetsDirty = true;

            public void OnDestroy(bool removeTextures)
            {
                GameObject.DestroyImmediate(gameObject);
                gameObject = null;

                if (removeTextures)
                {
                    for (int i = 0; i < maskTextures.Count; ++i)
                    {
                        var texturePath = AssetDatabase.GetAssetPath(maskTextures[i].texture);
                        AssetDatabase.DeleteAsset(texturePath);
                    }
                    maskTextures = null;
                    AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                }
            }

            public void ChangeMaterial(Material mtl)
            {
                var renderer = gameObject.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = Object.Instantiate<Material>(mtl);
                for (int i = 0; i < maskTextures.Count; ++i)
                {
                    renderer.sharedMaterial.SetTexture(maskTextures[i].shaderPropertyName, maskTextures[i].texture);
                }
            }
        }

        public class GroundTileVariation
        {
            //运行时唯一id
            int mInstanceID;
            //一个tile中name必须唯一
            string mName;

            List<GroundTileLOD> mLODs = new List<GroundTileLOD>();

            public int lodCount { get { return mLODs.Count; } }

            public GroundTileVariation(string name, int id)
            {
                mName = name;
                mInstanceID = id;
            }

            public void OnDestroy(bool removeTextures)
            {
                for (int i = 0; i < mLODs.Count; ++i)
                {
                    mLODs[i].OnDestroy(removeTextures);
                }
            }

            public void AddLOD(GroundTileLOD lod)
            {
                mLODs.Add(lod);
            }

            public void RemoveLOD()
            {
                mLODs[mLODs.Count - 1].OnDestroy(true);
                mLODs.RemoveAt(mLODs.Count - 1);
            }

            public GroundTileLOD GetLOD(int lod)
            {
                return mLODs[lod];
            }

            public void SetPixels(int lod, int maskTextureIndex, int x, int y, int width, int height, Color[] pixels)
            {
                mLODs[lod].assetsDirty = true;
                var maskTextures = mLODs[lod].maskTextures;
                maskTextures[maskTextureIndex].dirty = true;
                var maskTexture = maskTextures[maskTextureIndex].texture;
                var maskTextureData = maskTextures[maskTextureIndex].textureData;
                int textureResolution = maskTexture.width;
                maskTexture.SetPixels(x, y, width, height, pixels);
                maskTexture.Apply();
                for (int i = 0; i < height; ++i)
                {
                    for (int j = 0; j < width; ++j)
                    {
                        var srcIdx = i * width + j;
                        var dstIdx = (y + i) * textureResolution + j + x;
                        maskTextureData[dstIdx] = pixels[srcIdx];
                    }
                }

                maskTextures[maskTextureIndex].paintDirtyRange.Enlarge(x, y, width, height);
            }

            public void SetPixels32(int lod, int maskTextureIndex, int x, int y, int width, int height, Color32[] pixels)
            {
                mLODs[lod].assetsDirty = true;
                //Debug.Log($"SetPixels32 {x}, {y}, {width}, {height}");
                var maskTextures = mLODs[lod].maskTextures;
                maskTextures[maskTextureIndex].dirty = true;
                var maskTexture = maskTextures[maskTextureIndex].texture;
                var maskTextureData = maskTextures[maskTextureIndex].textureData;
                int textureResolution = maskTexture.width;
                maskTexture.SetPixels32(x, y, width, height, pixels);
                maskTexture.Apply();
                for (int i = 0; i < height; ++i)
                {
                    for (int j = 0; j < width; ++j)
                    {
                        var srcIdx = i * width + j;
                        var dstIdx = (y + i) * textureResolution + j + x;
                        maskTextureData[dstIdx] = pixels[srcIdx];
                    }
                }
            }

            public void ReloadTextureData(string texturePath)
            {
                for (int i = 0; i < mLODs.Count; ++i)
                {
                    var maskTextures = mLODs[i].maskTextures;
                    for (int k = 0; k < maskTextures.Count; ++k)
                    {
                        if (maskTextures[k].texture.name == texturePath)
                        {
                            maskTextures[k].textureData = maskTextures[k].texture.GetPixels();
                        }
                    }
                }
            }

            public void ChangeMaterial(int lod, Material mtl)
            {
                if (lod >= 0 && lod < mLODs.Count)
                {
                    mLODs[lod].ChangeMaterial(mtl);
                }
                else
                {
                    Debug.LogError($"Invalid lod {lod}");
                }
            }

            public string name { get { return mName; } }
            public int instanceID { get { return mInstanceID; } }
        }

        public class GroundTileTemplate
        {
            public GroundTileTemplate()
            {
                for (int i = 0; i < edgeIDs.Length; ++i)
                {
                    edgeIDs[i] = EdgeID.Empty;
                }
            }

            public void OnDestroy()
            {
                for (int i = 0; i < mVariations.Count; ++i)
                {
                    mVariations[i].OnDestroy(false);
                }
                mVariations = null;
            }

            public void RemoveLOD()
            {
                for (int i = 0; i < mVariations.Count; ++i)
                {
                    mVariations[i].RemoveLOD();
                }
            }

            public GroundTileVariation GetVariation(int index)
            {
                if (index < 0 || index >= variations.Count)
                {
                    index = 0;
                }

                return variations[index];
            }

            public GroundTileVariation GetCurrentVariation()
            {
                Debug.Assert(mCurrentVariation >= 0 && mCurrentVariation < variations.Count, $"invalid variation {mCurrentVariation}");
                return GetVariation(mCurrentVariation);
            }

            public bool SetCurrentVariation(int variationIndex)
            {
                if (mCurrentVariation != variationIndex)
                {
                    mCurrentVariation = variationIndex;
                    return true;
                }
                return false;
            }

            public void RemoveVariation(int variationIndex)
            {
                mVariations[variationIndex].OnDestroy(true);
                mVariations.RemoveAt(variationIndex);
                mCurrentVariation = 0;
            }

            public void ReloadTextureData(string texturePath)
            {
                for (int i = 0; i < mVariations.Count; ++i)
                {
                    mVariations[i].ReloadTextureData(texturePath);
                }
            }

            public void ChangeMaterial(int lod, Material mtl)
            {
                for (int i = 0; i < mVariations.Count; ++i)
                {
                    mVariations[i].ChangeMaterial(lod, mtl);
                }
            }

            public int currentVariationIndex { get { return mCurrentVariation; } }
            public List<GroundTileVariation> variations { get { return mVariations; } }
            public EdgeID[] edgeIDs = new EdgeID[4];
            public bool writable = true;
            public bool generateAssets = true;

            int mCurrentVariation = 0;
            List<GroundTileVariation> mVariations = new List<GroundTileVariation>();
        }

        public class GroundTileInstance
        {
            public GameObject gameObject;
            public GameObject tileIndexText;
            public GameObject[] edgeTexts = new GameObject[4];
            public int tileIndex;

            public GroundTileInstance(Vector3 tileStartPos, int tileIndex, EdgeID[] edgeIDs, float tileSize)
            {
                this.tileIndex = tileIndex;
                tileIndexText = Utils.CreateTextGameObject($"tile_index_text_{tileIndex}", tileIndex.ToString(), Color.red, 32, 90);
                float ratio = 5.0f * (tileSize / 180.0f);
                tileIndexText.transform.localScale = Vector3.one * ratio;
                Utils.HideGameObject(tileIndexText);
                tileIndexText.transform.position = new Vector3(tileStartPos.x + tileSize * 0.5f, 2, tileStartPos.z + tileSize * 0.5f);

                for (int i = 0; i < 4; ++i)
                {
                    if (edgeIDs[i] != EdgeID.Empty)
                    {
                        string name = ((int)edgeIDs[i]).ToString();
                        edgeTexts[i] = Utils.CreateTextGameObject($"edge_text_{tileIndex}_{i}", name, Color.black, 32, 90);
                        float edgeRatio = tileSize / 180.0f;
                        edgeTexts[i].transform.localScale = Vector3.one * 5.0f * edgeRatio;
                        Utils.HideGameObject(edgeTexts[i]);

                        EdgeDirection dir = (EdgeDirection)i;
                        float edgeHeight = 16.0f * edgeRatio;
                        float edgeWidth = 10.0f * edgeRatio;
                        if (dir == EdgeDirection.Down)
                        {
                            edgeTexts[i].transform.position = tileStartPos + new Vector3(tileSize * 0.5f, 2, edgeHeight);
                        }
                        else if (dir == EdgeDirection.Up)
                        {
                            edgeTexts[i].transform.position = tileStartPos + new Vector3(tileSize * 0.5f, 2, tileSize);
                        }
                        else if (dir == EdgeDirection.Left)
                        {
                            edgeTexts[i].transform.position = tileStartPos + new Vector3(0, 2, tileSize * 0.5f);
                        }
                        else if (dir == EdgeDirection.Right)
                        {
                            edgeTexts[i].transform.position = tileStartPos + new Vector3(tileSize - edgeWidth, 2, tileSize * 0.5f);
                        }
                        else
                        {
                            Debug.Assert(false);
                        }
                    }
                }
            }

            public void OnDestroy()
            {
                if (edgeTexts != null)
                {
                    for (int k = 0; k < edgeTexts.Length; ++k)
                    {
                        GameObject.DestroyImmediate(edgeTexts[k]);
                    }
                    edgeTexts = null;
                }

                GameObject.DestroyImmediate(tileIndexText);
                GameObject.DestroyImmediate(gameObject);
                gameObject = null;
            }
        }
    }
}


#endif