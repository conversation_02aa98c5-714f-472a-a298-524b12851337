﻿using UnityEngine;

namespace TFW.Map
{
    //管理lod 1的数据
    public class CityTerritoryDataBlock
    {
        public CityTerritoryDataBlock(string prefabPath, Rect bounds, int maskTextureWidth, int maskTextureHeight, Color32[] maskTextureData)
        {
            mPrefabPath = prefabPath;
            mBounds = bounds;
        }

        public string prefabPath { get { return mPrefabPath; } }
        public Rect bounds { get { return mBounds; } }
        public Vector3 position { get { return Utils.ToVector3(bounds.center); } }

        string mPrefabPath;
        Rect mBounds;
    }
}
