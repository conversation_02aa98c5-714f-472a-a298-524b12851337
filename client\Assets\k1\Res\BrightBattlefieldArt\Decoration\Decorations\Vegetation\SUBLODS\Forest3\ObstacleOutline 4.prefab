%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1150651828400161284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7787246153602722413}
  - component: {fileID: 882389244761289206}
  m_Layer: 0
  m_Name: ObstacleOutline 4
  m_TagString: ignore
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7787246153602722413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150651828400161284}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &882389244761289206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150651828400161284}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -90, y: 0, z: -38.777878}
    - {x: -70.03731, y: 0, z: -25.13635}
    - {x: -58.502598, y: 0, z: -24.132458}
    - {x: -56.407288, y: 0, z: -38.337074}
    - {x: -72.531334, y: 0, z: -48.797073}
    - {x: -89.23986, y: 0, z: -45.802357}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -72.8159, y: 0, z: -51.1645}
    - {x: -73.8049, y: 0, z: -51.0414}
    - {x: -73.8113, y: 0, z: -51.0406}
    - {x: -86.3042, y: 0, z: -49.4312}
    - {x: -87.1656, y: 0, z: -49.0221}
    - {x: -89.1243, y: 0, z: -47.1658}
    - {x: -89.1576, y: 0, z: -47.1332}
    - {x: -90, y: 0, z: -44.8147}
    - {x: -90, y: 0, z: -44.2352}
    - {x: -90, y: 0, z: -42.7938}
    - {x: -90, y: 0, z: -41.8938}
    - {x: -90, y: 0, z: -37.4805}
    - {x: -90, y: 0, z: -36.8165}
    - {x: -88.5925, y: 0, z: -34.6183}
    - {x: -88.3891, y: 0, z: -34.4447}
    - {x: -73.1603, y: 0, z: -23.6258}
    - {x: -72.708, y: 0, z: -23.407}
    - {x: -66.9173, y: 0, z: -21.7196}
    - {x: -66.4598, y: 0, z: -21.6597}
    - {x: -65.0129, y: 0, z: -21.6936}
    - {x: -64.9781, y: 0, z: -21.6949}
    - {x: -60.3185, y: 0, z: -21.9124}
    - {x: -59.7873, y: 0, z: -22.0356}
    - {x: -57.25, y: 0, z: -23.1358}
    - {x: -56.3678, y: 0, z: -24.1307}
    - {x: -55.1865, y: 0, z: -28.0997}
    - {x: -55.1538, y: 0, z: -28.2307}
    - {x: -54.9288, y: 0, z: -29.3447}
    - {x: -54.918, y: 0, z: -29.4048}
    - {x: -53.8186, y: 0, z: -36.28}
    - {x: -53.9204, y: 0, z: -37.1177}
    - {x: -55.2072, y: 0, z: -40.1582}
    - {x: -55.7508, y: 0, z: -40.8237}
    - {x: -57.2338, y: 0, z: -41.8413}
    - {x: -57.2881, y: 0, z: -41.8768}
    - {x: -71.8072, y: 0, z: -50.942}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
