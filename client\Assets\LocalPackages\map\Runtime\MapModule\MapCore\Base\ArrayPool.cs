﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //a very naive pool is ok for now
    public class ArrayPool<T>
    {
        public T[] Rent(int size)
        {
            List<T[]> arrayList;
            mPools.TryGetValue(size, out arrayList);
            if (arrayList == null)
            {
                arrayList = new List<T[]>();
                mPools[size] = arrayList;
            }
            if (arrayList.Count > 0)
            {
                T[] val = arrayList[arrayList.Count - 1];
                arrayList.RemoveAt(arrayList.Count - 1);
                return val;
            }
            return new T[size];
        }

        public void Return(T[] array)
        {
            List<T[]> arrayList;
            mPools.TryGetValue(array.Length, out arrayList);
            if (arrayList != null)
            {
                arrayList.Add(array);
            }
            else
            {
                Debug.Assert(false);
            }
        }

        public static ArrayPool<T> Create()
        {
            return new ArrayPool<T>();
        }

        Dictionary<int, List<T[]>> mPools = new Dictionary<int, List<T[]>>();
    }
}
