﻿ 



 
 

﻿
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadCameraCollider(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.CameraCollider);
            if (pos < 0)
            {
                return;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //--------------------version 1 start--------------------------
            bool valid = reader.ReadBoolean();
            if (valid)
            {
                mEditorData.cameraCollider.vertices = Utils.ReadVector3Array(reader);
                mEditorData.cameraCollider.indices = Utils.ReadIntArray(reader);
            }
            //--------------------version 1 end--------------------------
        }
    }
}
