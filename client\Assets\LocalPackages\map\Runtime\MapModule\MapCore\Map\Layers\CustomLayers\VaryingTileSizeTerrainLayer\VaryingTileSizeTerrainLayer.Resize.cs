﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class VaryingTileSizeTerrainLayer : MapLayerBase, IBlendTerrainLayer
    {    
        class ResizeTileData
        {
            public ResizeTileData(int type, int index, int tileID)
            {
                tileType = type;
                tileIndex = index;
                this.tileID = tileID;
            }

            public int tileType;
            public int tileIndex;
            public int tileID;
        }

        public bool MoveAndResize(float newLayerWidth, float newLayerHeight, ResizeAlignment alignment)
        {
            if (!Map.currentMap.isEditorMode)
            {
                return false;
            }

            var mapWidth = map.mapWidth;
            var mapHeight = map.mapHeight;
            if (alignment == ResizeAlignment.Expand)
            {
                if (newLayerWidth < mapWidth || newLayerHeight < mapHeight)
                {
                    //只能扩大地图
                    return false;
                }
            }

            if (alignment == ResizeAlignment.StayAndOffsetLayer || 
                alignment == ResizeAlignment.Expand)
            {
                var ox = (mapWidth - newLayerWidth) * 0.5f;
                var oz = (mapHeight - newLayerHeight) * 0.5f;
                mLayerData.layerOffset = new Vector3(ox, 0, oz);
            }
            else
            {
                mLayerData.layerOffset = Vector3.zero;
            }

            int newHorizontalTileCount = Mathf.CeilToInt(newLayerWidth / layerData.tileWidth);
            int newVerticalTileCount = Mathf.CeilToInt(newLayerHeight / layerData.tileHeight);
            int oldHorizontalTileCount = layerData.horizontalTileCount;
            int oldVerticalTileCount = layerData.verticalTileCount;
            if (newHorizontalTileCount != oldHorizontalTileCount || newVerticalTileCount != oldVerticalTileCount)
            {
                ResizeTileData[,] newTileData = new ResizeTileData[newVerticalTileCount, newHorizontalTileCount];
                for (int i = 0; i < newVerticalTileCount; ++i)
                {
                    for (int j = 0; j < newHorizontalTileCount; ++j)
                    {
                        newTileData[i, j] = new ResizeTileData(0, 0, 0);
                    }
                }
                int offsetXInOld;
                int offsetYInOld;
                int offsetXInNew;
                int offsetYInNew;
                int offsetX = 0;
                int offsetY = 0;
                if (alignment == ResizeAlignment.Move || alignment == ResizeAlignment.StayAndOffsetLayer || alignment == ResizeAlignment.Expand)
                {
                    offsetX = Mathf.Abs(newHorizontalTileCount - oldHorizontalTileCount) / 2;
                    offsetY = Mathf.Abs(newVerticalTileCount - oldVerticalTileCount) / 2;
                }
                if (newHorizontalTileCount > oldHorizontalTileCount)
                {
                    //扩大
                    offsetXInOld = 0;
                    offsetXInNew = offsetX;
                }
                else
                {
                    //缩小
                    offsetXInOld = offsetX;
                    offsetXInNew = 0;
                }

                if (newVerticalTileCount > oldVerticalTileCount)
                {
                    //扩大
                    offsetYInOld = 0;
                    offsetYInNew = offsetY;
                }
                else
                {
                    //缩小
                    offsetYInOld = offsetY;
                    offsetYInNew = 0;
                }

                int minVerticalTileCount = Mathf.Min(newVerticalTileCount, oldVerticalTileCount);
                int minHorizontalTileCount = Mathf.Min(newHorizontalTileCount, oldHorizontalTileCount);

                for (int i = 0; i < minVerticalTileCount; ++i)
                {
                    for (int j = 0; j < minHorizontalTileCount; ++j)
                    {
                        var tile = mLayerData.GetTile(offsetXInOld + j, offsetYInOld + i);
                        if (tile != null)
                        {
                            var size = tile.size;
                            var bigTile = tile.bigTile;
                            if (size.x == 1 && size.y == 1 || (bigTile != null && bigTile.x == j && bigTile.y == i))
                            {
                                newTileData[offsetYInNew + i, offsetXInNew + j] = new ResizeTileData(tile.type, tile.index, tile.tileID);
                            }
                        }
                    }
                }

                SetSize(newHorizontalTileCount, newVerticalTileCount);

                for (int i = newVerticalTileCount - 1; i >= 0; --i)
                {
                    for (int j = newHorizontalTileCount - 1; j >= 0; --j)
                    {
                        var resizeTileData = newTileData[i, j];
                        SetTile(j, i, resizeTileData.tileIndex, resizeTileData.tileType, resizeTileData.tileID);
                    }
                }

                return true;
            }

            return false;
        }

        void SetSize(int newHorizontalTileCount, int newVerticalTileCount)
        {
            mLayerData.SetSize(newHorizontalTileCount, newVerticalTileCount);
            mLayerView.SetSize(newHorizontalTileCount, newVerticalTileCount);
        }
    }
}


#endif