﻿using UnityEngine;

namespace TFW.Map
{
    [System.Serializable]
    public class SubMeshSetting
    {
        public Material[] materials = new Material[0];

        public Material GetMaterial(int type)
        {
            if (type >= 0 && type < materials.Length)
            {
                return materials[type];
            }
            return materials[0];
        }
    }

    public class ChangeObjectMaterial : MonoBehaviour
    {
        public SubMeshSetting[] subMeshSettings = new SubMeshSetting[0];

        public void Change(int targetType)
        {
            if (mRenderer == null)
            {
                mRenderer = GetComponent<Renderer>();
            }
            
            if (mRenderer != null)
            {
                var sharedMaterials = mRenderer.sharedMaterials;
                int n = Mathf.Min(sharedMaterials.Length, subMeshSettings.Length);
                for (int i = 0; i < n; i++)
                {
                    sharedMaterials[i] = subMeshSettings[i].GetMaterial(targetType);
                }
                mRenderer.sharedMaterials = sharedMaterials;
            }
        }

        Renderer mRenderer;
    }
}
