﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class ResourceBuildingLODManagerContainer : MonoBehaviour
    {
        public static void InitGameObjectLOD(GameObject resourceBuilding)
        {
            mIsMultipleState = CheckIfBuildingIsMultipleState(resourceBuilding);

            if (mIsMultipleState)
            {
                int nChildren = resourceBuilding.transform.childCount;
                for (int i = 0; i < nChildren; ++i)
                {
                    var childGameObject = resourceBuilding.transform.GetChild(i).gameObject;
                    if (childGameObject.GetComponent<ResourceBuildingLODManager>() == null)
                    {
                        childGameObject.AddComponent<ResourceBuildingLODManager>();
                    }
                    ResourceBuildingLODManager.InitGameObjectLOD(childGameObject);
                }
            }
            else
            {
                if (resourceBuilding.GetComponent<ResourceBuildingLODManager>() == null)
                {
                    resourceBuilding.AddComponent<ResourceBuildingLODManager>();
                }
                ResourceBuildingLODManager.InitGameObjectLOD(resourceBuilding);
            }
        }

        static bool CheckIfBuildingIsMultipleState(GameObject resourceBuilding)
        {
            int n = resourceBuilding.transform.childCount;
            //遍历资源建筑根节点下的子节点,设置它们的LODManager
            for (int i = 0; i < n; ++i)
            {
                var buildingObject = resourceBuilding.transform.GetChild(i).gameObject;

                //找到主建筑,必须要有一个叫mainBuilding的节点作为主建筑
                if (buildingObject.name == "mainBuilding")
                {
                    return false;
                }
            }
            return true;
        }

        public static void UninitGameObjectLOD(GameObject resourceBuilding)
        {
            if (mIsMultipleState)
            {
                int nChildren = resourceBuilding.transform.childCount;
                for (int i = 0; i < nChildren; ++i)
                {
                    var child = resourceBuilding.transform.GetChild(i);
                    ResourceBuildingLODManager.UninitGameObjectLOD(child.gameObject);
                }
            }
            else
            {
                ResourceBuildingLODManager.UninitGameObjectLOD(resourceBuilding);
            }
        }

        static bool mIsMultipleState;
    }
}
