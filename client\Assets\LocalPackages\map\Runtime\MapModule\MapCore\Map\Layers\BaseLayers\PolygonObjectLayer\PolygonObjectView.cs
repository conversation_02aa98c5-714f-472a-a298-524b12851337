﻿ 



 
 

﻿//created by wzw at 2019/11/25

using UnityEngine;

namespace TFW.Map
{
    public abstract class PolygonObjectView : MapObjectView
    {
        public PolygonObjectView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        protected override void DestroyModelImpl()
        {
            if (mModel != null)
            {
                mModel.Release();
                mModel = null;
            }
        }

        public void SetVertexDisplayRadius(float radius)
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).SetVertexRadius(radius);
            }
        }

        public void SetColor(PrefabOutlineType type, Color color)
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).SetColor(type, color);
            }
        }

        public override void SetZoom(float zoom, bool lodChanged)
        {
        }

        public void UpdateVertex(PrefabOutlineType type, int index)
        {
            if (mModel != null)
            {
                var data = Map.currentMap.FindObject(objectDataID) as PolygonObjectData;
                (mModel as PolygonObjectModel).UpdateVertex(type, index, data);
            }
        }

        public void Update()
        {
            if (mModel != null)
            {
                var data = Map.currentMap.FindObject(objectDataID) as PolygonObjectData;
                (mModel as PolygonObjectModel).Update(data);
            }
        }

        public void InsertVertex(PrefabOutlineType type, PolygonObjectData data, int index, Vector3 pos)
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).InsertVertex(type, data, index, pos);
            }
        }

        public void RemoveVertex(PrefabOutlineType type, PolygonObjectData data, int index)
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).RemoveVertex(type, data, index);
            }
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).Show(type);
            }
        }

        public void HideOutline()
        {
            if (mModel != null)
            {
                (mModel as PolygonObjectModel).Hide();
            }
        }

        public void Revert(PrefabOutlineType type)
        {
            var model = mModel as PolygonObjectModel;
            model.Revert(type);
        }
    }
}