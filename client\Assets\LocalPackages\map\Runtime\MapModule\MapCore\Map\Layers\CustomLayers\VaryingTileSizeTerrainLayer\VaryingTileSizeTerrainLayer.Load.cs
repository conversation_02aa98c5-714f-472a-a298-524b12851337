﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class VaryingTileSizeTerrainLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            PrepareLoading();
            LoadSetting(reader);
            LoadPrefabManager(reader);
            var layerData = LoadLayerData(reader, AllocateID());

            reader.Close();

            var layer = new VaryingTileSizeTerrainLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static config.VaryingTileSizeTerrainLayerData LoadLayerData(BinaryReader reader, int layerID)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            bool useGeneratedLOD = reader.ReadBoolean();
            var layerPosition = Utils.ReadVector3(reader);

            int bigTileCount = reader.ReadInt32();
            var bigTiles = new config.VaryingTileSizeTerrainLayerBigTileData[bigTileCount];
            for (int i = 0; i < bigTileCount; ++i)
            {
                bigTiles[i] = new config.VaryingTileSizeTerrainLayerBigTileData();
                bigTiles[i].x = reader.ReadInt32();
                bigTiles[i].y = reader.ReadInt32();
                bigTiles[i].width = reader.ReadInt32();
                bigTiles[i].height = reader.ReadInt32();
            }

            var objects = new config.VaryingTileSizeTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    bool hasTile = reader.ReadBoolean();
                    if (hasTile)
                    {
                        var idx = i * cols + j;
                        objects[idx] = LoadTerrainTileData(reader, layerID);
                    }
                }
            }

            var config = LoadMapLayerLODConfig(reader);
            var layer = new config.VaryingTileSizeTerrainLayerData(layerID, layerName, layerOffset, config, rows, cols, tileWidth, tileHeight, objects, bigTiles, useGeneratedLOD, layerPosition);
            layer.active = active;
            return layer;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                var name = Utils.ReadString(reader);
                var terrainTileCount = reader.ReadInt32();

                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, terrainTileCount);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader)
        {
            var prefabManagerData = LoadPrefabManagerData(reader);
            var prefabManager = (Map.currentMap.data as EditorMapData).editorVaryingTileSizeTerrainPrefabManager;
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, true, false, false);

            var terrainPrefabManager = Map.currentMap.data.varyingTileSizeTerrainPrefabManager;
            int nGroups = prefabManager.groupCount;
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                int nPrefabs = group.count;
                for (int k = 0; k < nPrefabs; ++k)
                {
                    var item = group.GetItem(k);
                    terrainPrefabManager.SetGroupPrefabByID(group.groupID, k, item.prefabPath);
                    terrainPrefabManager.SetGroupPrefabSizeByID(group.groupID, k, item.size);
                }
            }
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader)
        {
            var nextGroupID = reader.ReadInt32();
            var prefabManager = new config.PrefabManager();
            prefabManager.nextGroupID = nextGroupID;
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();
            group.id = reader.ReadInt32();
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);
            group.addPrefabSet = reader.ReadBoolean();

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;

                group.prefabPaths[i].id = reader.ReadInt32();
                group.prefabPaths[i].size = Utils.ReadVector2Int(reader);
            }
            return group;
        }

        static config.VaryingTileSizeTerrainTileData LoadTerrainTileData(BinaryReader reader, int layerID)
        {
            var pathIndex = reader.ReadInt16();
            var type = reader.ReadInt32();
            var index = reader.ReadInt32();
            int tileID = reader.ReadInt32();
            int bigTileIndex = reader.ReadInt32();
            
            string prefabPath = "";
            if (pathIndex >= 0 && pathIndex < mLoadPrefabPathStringTable.Count)
            {
                prefabPath = mLoadPrefabPathStringTable[pathIndex];
            }
            
            var id = AllocateID();
            var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(id, prefabPath, false, MapModule.preloadGroundLayerTile);
            if (modelTemplate != null)
            {
                var tileData = new config.VaryingTileSizeTerrainTileData(id, layerID, type, index, tileID, modelTemplate.id, bigTileIndex);
                return tileData;
            }
            return null;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPathMapper = new PathMapper();
            mLoadPrefabPathStringTable = new List<string>();
        }

        static PathMapper mLoadPathMapper;
        static List<string> mLoadPrefabPathStringTable;
    }
}
#endif