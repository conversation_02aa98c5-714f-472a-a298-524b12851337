﻿using UnityEngine;

namespace TFW.Map
{
    public class CityTerritoryEdgeData : CityTerritoryDataBase
    {
        public CityTerritoryEdgeData(int id, Map map, int flag, Vector3 position, ModelTemplate modelTemplate, Rect bounds, int territoryID, int neighbourTerritoryID, Material mtl, CityTerritoryDataBlock block, CityTerritoryLayerData.SubLayerData subLayer, int index, Texture2D maskTexture) : base(id, map, flag, position, modelTemplate, bounds, territoryID, mtl, block, subLayer, index, maskTexture)
        {
            mNeighbourTerritoryID = neighbourTerritoryID;
        }

        public override void SetAlpha(float alpha)
        {
            if (ownerBlock != null)
            {
                subLayer.SetEdgeAlpha(index, alpha);
            }
        }

        public int neighbourTerritoryID { get { return mNeighbourTerritoryID; } }

        int mNeighbourTerritoryID;
    }
}
