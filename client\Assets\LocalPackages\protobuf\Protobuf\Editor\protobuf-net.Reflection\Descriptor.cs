// This file was generated by a tool; you should avoid making direct changes.
// Consider using 'partial classes' to extend these types
// Input: descriptor.proto

#pragma warning disable CS1591, CS0612, CS3021, IDE1006

namespace Google.Protobuf.Reflection
{

    [global::ProtoBuf.ProtoContract()]
    public partial class FileDescriptorSet
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"file")]
        public global::System.Collections.Generic.List<FileDescriptorProto> Files { get; } = new global::System.Collections.Generic.List<FileDescriptorProto>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class FileDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"package")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Package
        {
            get { return __pbn__Package ?? ""; }
            set { __pbn__Package = value; }
        }
        public bool ShouldSerializePackage() => __pbn__Package != null;
        public void ResetPackage() => __pbn__Package = null;
        private string __pbn__Package;

        [global::ProtoBuf.ProtoMember(3, Name = @"dependency")]
        public global::System.Collections.Generic.List<string> Dependencies { get; } = new global::System.Collections.Generic.List<string>();

        [global::ProtoBuf.ProtoMember(10, Name = @"public_dependency")]
        public int[] PublicDependencies { get; set; }

        [global::ProtoBuf.ProtoMember(11, Name = @"weak_dependency")]
        public int[] WeakDependencies { get; set; }

        [global::ProtoBuf.ProtoMember(4, Name = @"message_type")]
        public global::System.Collections.Generic.List<DescriptorProto> MessageTypes { get; } = new global::System.Collections.Generic.List<DescriptorProto>();

        [global::ProtoBuf.ProtoMember(5, Name = @"enum_type")]
        public global::System.Collections.Generic.List<EnumDescriptorProto> EnumTypes { get; } = new global::System.Collections.Generic.List<EnumDescriptorProto>();

        [global::ProtoBuf.ProtoMember(6, Name = @"service")]
        public global::System.Collections.Generic.List<ServiceDescriptorProto> Services { get; } = new global::System.Collections.Generic.List<ServiceDescriptorProto>();

        [global::ProtoBuf.ProtoMember(7, Name = @"extension")]
        public global::System.Collections.Generic.List<FieldDescriptorProto> Extensions { get; } = new global::System.Collections.Generic.List<FieldDescriptorProto>();

        [global::ProtoBuf.ProtoMember(8, Name = @"options")]
        public FileOptions Options { get; set; }

        [global::ProtoBuf.ProtoMember(9, Name = @"source_code_info")]
        public SourceCodeInfo SourceCodeInfo { get; set; }

        [global::ProtoBuf.ProtoMember(12, Name = @"syntax")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Syntax
        {
            get { return __pbn__Syntax ?? ""; }
            set { __pbn__Syntax = value; }
        }
        public bool ShouldSerializeSyntax() => __pbn__Syntax != null;
        public void ResetSyntax() => __pbn__Syntax = null;
        private string __pbn__Syntax;

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class DescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"field")]
        public global::System.Collections.Generic.List<FieldDescriptorProto> Fields { get; } = new global::System.Collections.Generic.List<FieldDescriptorProto>();

        [global::ProtoBuf.ProtoMember(6, Name = @"extension")]
        public global::System.Collections.Generic.List<FieldDescriptorProto> Extensions { get; } = new global::System.Collections.Generic.List<FieldDescriptorProto>();

        [global::ProtoBuf.ProtoMember(3, Name = @"nested_type")]
        public global::System.Collections.Generic.List<DescriptorProto> NestedTypes { get; } = new global::System.Collections.Generic.List<DescriptorProto>();

        [global::ProtoBuf.ProtoMember(4, Name = @"enum_type")]
        public global::System.Collections.Generic.List<EnumDescriptorProto> EnumTypes { get; } = new global::System.Collections.Generic.List<EnumDescriptorProto>();

        [global::ProtoBuf.ProtoMember(5, Name = @"extension_range")]
        public global::System.Collections.Generic.List<ExtensionRange> ExtensionRanges { get; } = new global::System.Collections.Generic.List<ExtensionRange>();

        [global::ProtoBuf.ProtoMember(8, Name = @"oneof_decl")]
        public global::System.Collections.Generic.List<OneofDescriptorProto> OneofDecls { get; } = new global::System.Collections.Generic.List<OneofDescriptorProto>();

        [global::ProtoBuf.ProtoMember(7, Name = @"options")]
        public MessageOptions Options { get; set; }

        [global::ProtoBuf.ProtoMember(9, Name = @"reserved_range")]
        public global::System.Collections.Generic.List<ReservedRange> ReservedRanges { get; } = new global::System.Collections.Generic.List<ReservedRange>();

        [global::ProtoBuf.ProtoMember(10, Name = @"reserved_name")]
        public global::System.Collections.Generic.List<string> ReservedNames { get; } = new global::System.Collections.Generic.List<string>();

        [global::ProtoBuf.ProtoContract()]
        public partial class ExtensionRange
        {
            [global::ProtoBuf.ProtoMember(1, Name = @"start")]
            public int Start
            {
                get { return __pbn__Start.GetValueOrDefault(); }
                set { __pbn__Start = value; }
            }
            public bool ShouldSerializeStart() => __pbn__Start != null;
            public void ResetStart() => __pbn__Start = null;
            private int? __pbn__Start;

            [global::ProtoBuf.ProtoMember(2, Name = @"end")]
            public int End
            {
                get { return __pbn__End.GetValueOrDefault(); }
                set { __pbn__End = value; }
            }
            public bool ShouldSerializeEnd() => __pbn__End != null;
            public void ResetEnd() => __pbn__End = null;
            private int? __pbn__End;

        }

        [global::ProtoBuf.ProtoContract()]
        public partial class ReservedRange
        {
            [global::ProtoBuf.ProtoMember(1, Name = @"start")]
            public int Start
            {
                get { return __pbn__Start.GetValueOrDefault(); }
                set { __pbn__Start = value; }
            }
            public bool ShouldSerializeStart() => __pbn__Start != null;
            public void ResetStart() => __pbn__Start = null;
            private int? __pbn__Start;

            [global::ProtoBuf.ProtoMember(2, Name = @"end")]
            public int End
            {
                get { return __pbn__End.GetValueOrDefault(); }
                set { __pbn__End = value; }
            }
            public bool ShouldSerializeEnd() => __pbn__End != null;
            public void ResetEnd() => __pbn__End = null;
            private int? __pbn__End;

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class FieldDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(3, Name = @"number")]
        public int Number
        {
            get { return __pbn__Number.GetValueOrDefault(); }
            set { __pbn__Number = value; }
        }
        public bool ShouldSerializeNumber() => __pbn__Number != null;
        public void ResetNumber() => __pbn__Number = null;
        private int? __pbn__Number;

        [global::ProtoBuf.ProtoMember(4)]
        public Label label
        {
            get { return __pbn__label.GetValueOrDefault(); }
            set { __pbn__label = value; }
        }
        public bool ShouldSerializelabel() => __pbn__label != null;
        public void Resetlabel() => __pbn__label = null;
        private Label? __pbn__label;

        [global::ProtoBuf.ProtoMember(5)]
        public Type type
        {
            get { return __pbn__type.GetValueOrDefault(); }
            set { __pbn__type = value; }
        }
        public bool ShouldSerializetype() => __pbn__type != null;
        public void Resettype() => __pbn__type = null;
        private Type? __pbn__type;

        [global::ProtoBuf.ProtoMember(6, Name = @"type_name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string TypeName
        {
            get { return __pbn__TypeName ?? ""; }
            set { __pbn__TypeName = value; }
        }
        public bool ShouldSerializeTypeName() => __pbn__TypeName != null;
        public void ResetTypeName() => __pbn__TypeName = null;
        private string __pbn__TypeName;

        [global::ProtoBuf.ProtoMember(2, Name = @"extendee")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Extendee
        {
            get { return __pbn__Extendee ?? ""; }
            set { __pbn__Extendee = value; }
        }
        public bool ShouldSerializeExtendee() => __pbn__Extendee != null;
        public void ResetExtendee() => __pbn__Extendee = null;
        private string __pbn__Extendee;

        [global::ProtoBuf.ProtoMember(7, Name = @"default_value")]
        [global::System.ComponentModel.DefaultValue("")]
        public string DefaultValue
        {
            get { return __pbn__DefaultValue ?? ""; }
            set { __pbn__DefaultValue = value; }
        }
        public bool ShouldSerializeDefaultValue() => __pbn__DefaultValue != null;
        public void ResetDefaultValue() => __pbn__DefaultValue = null;
        private string __pbn__DefaultValue;

        [global::ProtoBuf.ProtoMember(9, Name = @"oneof_index")]
        public int OneofIndex
        {
            get { return __pbn__OneofIndex.GetValueOrDefault(); }
            set { __pbn__OneofIndex = value; }
        }
        public bool ShouldSerializeOneofIndex() => __pbn__OneofIndex != null;
        public void ResetOneofIndex() => __pbn__OneofIndex = null;
        private int? __pbn__OneofIndex;

        [global::ProtoBuf.ProtoMember(10, Name = @"json_name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string JsonName
        {
            get { return __pbn__JsonName ?? ""; }
            set { __pbn__JsonName = value; }
        }
        public bool ShouldSerializeJsonName() => __pbn__JsonName != null;
        public void ResetJsonName() => __pbn__JsonName = null;
        private string __pbn__JsonName;

        [global::ProtoBuf.ProtoMember(8, Name = @"options")]
        public FieldOptions Options { get; set; }

        [global::ProtoBuf.ProtoContract()]
        public enum Type
        {
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_DOUBLE")]
            TypeDouble = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_FLOAT")]
            TypeFloat = 2,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_INT64")]
            TypeInt64 = 3,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_UINT64")]
            TypeUint64 = 4,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_INT32")]
            TypeInt32 = 5,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_FIXED64")]
            TypeFixed64 = 6,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_FIXED32")]
            TypeFixed32 = 7,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_BOOL")]
            TypeBool = 8,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_STRING")]
            TypeString = 9,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_GROUP")]
            TypeGroup = 10,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_MESSAGE")]
            TypeMessage = 11,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_BYTES")]
            TypeBytes = 12,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_UINT32")]
            TypeUint32 = 13,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_ENUM")]
            TypeEnum = 14,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_SFIXED32")]
            TypeSfixed32 = 15,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_SFIXED64")]
            TypeSfixed64 = 16,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_SINT32")]
            TypeSint32 = 17,
            [global::ProtoBuf.ProtoEnum(Name = @"TYPE_SINT64")]
            TypeSint64 = 18,
        }

        [global::ProtoBuf.ProtoContract()]
        public enum Label
        {
            [global::ProtoBuf.ProtoEnum(Name = @"LABEL_OPTIONAL")]
            LabelOptional = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"LABEL_REQUIRED")]
            LabelRequired = 2,
            [global::ProtoBuf.ProtoEnum(Name = @"LABEL_REPEATED")]
            LabelRepeated = 3,
        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class OneofDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"options")]
        public OneofOptions Options { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class EnumDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"value")]
        public global::System.Collections.Generic.List<EnumValueDescriptorProto> Values { get; } = new global::System.Collections.Generic.List<EnumValueDescriptorProto>();

        [global::ProtoBuf.ProtoMember(3, Name = @"options")]
        public EnumOptions Options { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class EnumValueDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"number")]
        public int Number
        {
            get { return __pbn__Number.GetValueOrDefault(); }
            set { __pbn__Number = value; }
        }
        public bool ShouldSerializeNumber() => __pbn__Number != null;
        public void ResetNumber() => __pbn__Number = null;
        private int? __pbn__Number;

        [global::ProtoBuf.ProtoMember(3, Name = @"options")]
        public EnumValueOptions Options { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class ServiceDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"method")]
        public global::System.Collections.Generic.List<MethodDescriptorProto> Methods { get; } = new global::System.Collections.Generic.List<MethodDescriptorProto>();

        [global::ProtoBuf.ProtoMember(3, Name = @"options")]
        public ServiceOptions Options { get; set; }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class MethodDescriptorProto
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"name")]
        [global::System.ComponentModel.DefaultValue("")]
        public string Name
        {
            get { return __pbn__Name ?? ""; }
            set { __pbn__Name = value; }
        }
        public bool ShouldSerializeName() => __pbn__Name != null;
        public void ResetName() => __pbn__Name = null;
        private string __pbn__Name;

        [global::ProtoBuf.ProtoMember(2, Name = @"input_type")]
        [global::System.ComponentModel.DefaultValue("")]
        public string InputType
        {
            get { return __pbn__InputType ?? ""; }
            set { __pbn__InputType = value; }
        }
        public bool ShouldSerializeInputType() => __pbn__InputType != null;
        public void ResetInputType() => __pbn__InputType = null;
        private string __pbn__InputType;

        [global::ProtoBuf.ProtoMember(3, Name = @"output_type")]
        [global::System.ComponentModel.DefaultValue("")]
        public string OutputType
        {
            get { return __pbn__OutputType ?? ""; }
            set { __pbn__OutputType = value; }
        }
        public bool ShouldSerializeOutputType() => __pbn__OutputType != null;
        public void ResetOutputType() => __pbn__OutputType = null;
        private string __pbn__OutputType;

        [global::ProtoBuf.ProtoMember(4, Name = @"options")]
        public MethodOptions Options { get; set; }

        [global::ProtoBuf.ProtoMember(5, Name = @"client_streaming")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool ClientStreaming
        {
            get { return __pbn__ClientStreaming ?? false; }
            set { __pbn__ClientStreaming = value; }
        }
        public bool ShouldSerializeClientStreaming() => __pbn__ClientStreaming != null;
        public void ResetClientStreaming() => __pbn__ClientStreaming = null;
        private bool? __pbn__ClientStreaming;

        [global::ProtoBuf.ProtoMember(6, Name = @"server_streaming")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool ServerStreaming
        {
            get { return __pbn__ServerStreaming ?? false; }
            set { __pbn__ServerStreaming = value; }
        }
        public bool ShouldSerializeServerStreaming() => __pbn__ServerStreaming != null;
        public void ResetServerStreaming() => __pbn__ServerStreaming = null;
        private bool? __pbn__ServerStreaming;

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class FileOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"java_package")]
        [global::System.ComponentModel.DefaultValue("")]
        public string JavaPackage
        {
            get { return __pbn__JavaPackage ?? ""; }
            set { __pbn__JavaPackage = value; }
        }
        public bool ShouldSerializeJavaPackage() => __pbn__JavaPackage != null;
        public void ResetJavaPackage() => __pbn__JavaPackage = null;
        private string __pbn__JavaPackage;

        [global::ProtoBuf.ProtoMember(8, Name = @"java_outer_classname")]
        [global::System.ComponentModel.DefaultValue("")]
        public string JavaOuterClassname
        {
            get { return __pbn__JavaOuterClassname ?? ""; }
            set { __pbn__JavaOuterClassname = value; }
        }
        public bool ShouldSerializeJavaOuterClassname() => __pbn__JavaOuterClassname != null;
        public void ResetJavaOuterClassname() => __pbn__JavaOuterClassname = null;
        private string __pbn__JavaOuterClassname;

        [global::ProtoBuf.ProtoMember(10, Name = @"java_multiple_files")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool JavaMultipleFiles
        {
            get { return __pbn__JavaMultipleFiles ?? false; }
            set { __pbn__JavaMultipleFiles = value; }
        }
        public bool ShouldSerializeJavaMultipleFiles() => __pbn__JavaMultipleFiles != null;
        public void ResetJavaMultipleFiles() => __pbn__JavaMultipleFiles = null;
        private bool? __pbn__JavaMultipleFiles;

        [global::ProtoBuf.ProtoMember(20, Name = @"java_generate_equals_and_hash")]
        [global::System.Obsolete]
        public bool JavaGenerateEqualsAndHash
        {
            get { return __pbn__JavaGenerateEqualsAndHash.GetValueOrDefault(); }
            set { __pbn__JavaGenerateEqualsAndHash = value; }
        }
        public bool ShouldSerializeJavaGenerateEqualsAndHash() => __pbn__JavaGenerateEqualsAndHash != null;
        public void ResetJavaGenerateEqualsAndHash() => __pbn__JavaGenerateEqualsAndHash = null;
        private bool? __pbn__JavaGenerateEqualsAndHash;

        [global::ProtoBuf.ProtoMember(27, Name = @"java_string_check_utf8")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool JavaStringCheckUtf8
        {
            get { return __pbn__JavaStringCheckUtf8 ?? false; }
            set { __pbn__JavaStringCheckUtf8 = value; }
        }
        public bool ShouldSerializeJavaStringCheckUtf8() => __pbn__JavaStringCheckUtf8 != null;
        public void ResetJavaStringCheckUtf8() => __pbn__JavaStringCheckUtf8 = null;
        private bool? __pbn__JavaStringCheckUtf8;

        [global::ProtoBuf.ProtoMember(9, Name = @"optimize_for")]
        [global::System.ComponentModel.DefaultValue(OptimizeMode.Speed)]
        public OptimizeMode OptimizeFor
        {
            get { return __pbn__OptimizeFor ?? OptimizeMode.Speed; }
            set { __pbn__OptimizeFor = value; }
        }
        public bool ShouldSerializeOptimizeFor() => __pbn__OptimizeFor != null;
        public void ResetOptimizeFor() => __pbn__OptimizeFor = null;
        private OptimizeMode? __pbn__OptimizeFor;

        [global::ProtoBuf.ProtoMember(11, Name = @"go_package")]
        [global::System.ComponentModel.DefaultValue("")]
        public string GoPackage
        {
            get { return __pbn__GoPackage ?? ""; }
            set { __pbn__GoPackage = value; }
        }
        public bool ShouldSerializeGoPackage() => __pbn__GoPackage != null;
        public void ResetGoPackage() => __pbn__GoPackage = null;
        private string __pbn__GoPackage;

        [global::ProtoBuf.ProtoMember(16, Name = @"cc_generic_services")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool CcGenericServices
        {
            get { return __pbn__CcGenericServices ?? false; }
            set { __pbn__CcGenericServices = value; }
        }
        public bool ShouldSerializeCcGenericServices() => __pbn__CcGenericServices != null;
        public void ResetCcGenericServices() => __pbn__CcGenericServices = null;
        private bool? __pbn__CcGenericServices;

        [global::ProtoBuf.ProtoMember(17, Name = @"java_generic_services")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool JavaGenericServices
        {
            get { return __pbn__JavaGenericServices ?? false; }
            set { __pbn__JavaGenericServices = value; }
        }
        public bool ShouldSerializeJavaGenericServices() => __pbn__JavaGenericServices != null;
        public void ResetJavaGenericServices() => __pbn__JavaGenericServices = null;
        private bool? __pbn__JavaGenericServices;

        [global::ProtoBuf.ProtoMember(18, Name = @"py_generic_services")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool PyGenericServices
        {
            get { return __pbn__PyGenericServices ?? false; }
            set { __pbn__PyGenericServices = value; }
        }
        public bool ShouldSerializePyGenericServices() => __pbn__PyGenericServices != null;
        public void ResetPyGenericServices() => __pbn__PyGenericServices = null;
        private bool? __pbn__PyGenericServices;

        [global::ProtoBuf.ProtoMember(23, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(31, Name = @"cc_enable_arenas")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool CcEnableArenas
        {
            get { return __pbn__CcEnableArenas ?? false; }
            set { __pbn__CcEnableArenas = value; }
        }
        public bool ShouldSerializeCcEnableArenas() => __pbn__CcEnableArenas != null;
        public void ResetCcEnableArenas() => __pbn__CcEnableArenas = null;
        private bool? __pbn__CcEnableArenas;

        [global::ProtoBuf.ProtoMember(36, Name = @"objc_class_prefix")]
        [global::System.ComponentModel.DefaultValue("")]
        public string ObjcClassPrefix
        {
            get { return __pbn__ObjcClassPrefix ?? ""; }
            set { __pbn__ObjcClassPrefix = value; }
        }
        public bool ShouldSerializeObjcClassPrefix() => __pbn__ObjcClassPrefix != null;
        public void ResetObjcClassPrefix() => __pbn__ObjcClassPrefix = null;
        private string __pbn__ObjcClassPrefix;

        [global::ProtoBuf.ProtoMember(37, Name = @"csharp_namespace")]
        [global::System.ComponentModel.DefaultValue("")]
        public string CsharpNamespace
        {
            get { return __pbn__CsharpNamespace ?? ""; }
            set { __pbn__CsharpNamespace = value; }
        }
        public bool ShouldSerializeCsharpNamespace() => __pbn__CsharpNamespace != null;
        public void ResetCsharpNamespace() => __pbn__CsharpNamespace = null;
        private string __pbn__CsharpNamespace;

        [global::ProtoBuf.ProtoMember(39, Name = @"swift_prefix")]
        [global::System.ComponentModel.DefaultValue("")]
        public string SwiftPrefix
        {
            get { return __pbn__SwiftPrefix ?? ""; }
            set { __pbn__SwiftPrefix = value; }
        }
        public bool ShouldSerializeSwiftPrefix() => __pbn__SwiftPrefix != null;
        public void ResetSwiftPrefix() => __pbn__SwiftPrefix = null;
        private string __pbn__SwiftPrefix;

        [global::ProtoBuf.ProtoMember(40, Name = @"php_class_prefix")]
        [global::System.ComponentModel.DefaultValue("")]
        public string PhpClassPrefix
        {
            get { return __pbn__PhpClassPrefix ?? ""; }
            set { __pbn__PhpClassPrefix = value; }
        }
        public bool ShouldSerializePhpClassPrefix() => __pbn__PhpClassPrefix != null;
        public void ResetPhpClassPrefix() => __pbn__PhpClassPrefix = null;
        private string __pbn__PhpClassPrefix;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

        [global::ProtoBuf.ProtoContract()]
        public enum OptimizeMode
        {
            [global::ProtoBuf.ProtoEnum(Name = @"SPEED")]
            Speed = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"CODE_SIZE")]
            CodeSize = 2,
            [global::ProtoBuf.ProtoEnum(Name = @"LITE_RUNTIME")]
            LiteRuntime = 3,
        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class MessageOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"message_set_wire_format")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool MessageSetWireFormat
        {
            get { return __pbn__MessageSetWireFormat ?? false; }
            set { __pbn__MessageSetWireFormat = value; }
        }
        public bool ShouldSerializeMessageSetWireFormat() => __pbn__MessageSetWireFormat != null;
        public void ResetMessageSetWireFormat() => __pbn__MessageSetWireFormat = null;
        private bool? __pbn__MessageSetWireFormat;

        [global::ProtoBuf.ProtoMember(2, Name = @"no_standard_descriptor_accessor")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool NoStandardDescriptorAccessor
        {
            get { return __pbn__NoStandardDescriptorAccessor ?? false; }
            set { __pbn__NoStandardDescriptorAccessor = value; }
        }
        public bool ShouldSerializeNoStandardDescriptorAccessor() => __pbn__NoStandardDescriptorAccessor != null;
        public void ResetNoStandardDescriptorAccessor() => __pbn__NoStandardDescriptorAccessor = null;
        private bool? __pbn__NoStandardDescriptorAccessor;

        [global::ProtoBuf.ProtoMember(3, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(7, Name = @"map_entry")]
        public bool MapEntry
        {
            get { return __pbn__MapEntry.GetValueOrDefault(); }
            set { __pbn__MapEntry = value; }
        }
        public bool ShouldSerializeMapEntry() => __pbn__MapEntry != null;
        public void ResetMapEntry() => __pbn__MapEntry = null;
        private bool? __pbn__MapEntry;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class FieldOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"ctype")]
        [global::System.ComponentModel.DefaultValue(CType.String)]
        public CType Ctype
        {
            get { return __pbn__Ctype ?? CType.String; }
            set { __pbn__Ctype = value; }
        }
        public bool ShouldSerializeCtype() => __pbn__Ctype != null;
        public void ResetCtype() => __pbn__Ctype = null;
        private CType? __pbn__Ctype;

        [global::ProtoBuf.ProtoMember(2, Name = @"packed")]
        public bool Packed
        {
            get { return __pbn__Packed.GetValueOrDefault(); }
            set { __pbn__Packed = value; }
        }
        public bool ShouldSerializePacked() => __pbn__Packed != null;
        public void ResetPacked() => __pbn__Packed = null;
        private bool? __pbn__Packed;

        [global::ProtoBuf.ProtoMember(6, Name = @"jstype")]
        [global::System.ComponentModel.DefaultValue(JSType.JsNormal)]
        public JSType Jstype
        {
            get { return __pbn__Jstype ?? JSType.JsNormal; }
            set { __pbn__Jstype = value; }
        }
        public bool ShouldSerializeJstype() => __pbn__Jstype != null;
        public void ResetJstype() => __pbn__Jstype = null;
        private JSType? __pbn__Jstype;

        [global::ProtoBuf.ProtoMember(5, Name = @"lazy")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Lazy
        {
            get { return __pbn__Lazy ?? false; }
            set { __pbn__Lazy = value; }
        }
        public bool ShouldSerializeLazy() => __pbn__Lazy != null;
        public void ResetLazy() => __pbn__Lazy = null;
        private bool? __pbn__Lazy;

        [global::ProtoBuf.ProtoMember(3, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(10, Name = @"weak")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Weak
        {
            get { return __pbn__Weak ?? false; }
            set { __pbn__Weak = value; }
        }
        public bool ShouldSerializeWeak() => __pbn__Weak != null;
        public void ResetWeak() => __pbn__Weak = null;
        private bool? __pbn__Weak;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

        [global::ProtoBuf.ProtoContract()]
        public enum CType
        {
            [global::ProtoBuf.ProtoEnum(Name = @"STRING")]
            String = 0,
            [global::ProtoBuf.ProtoEnum(Name = @"CORD")]
            Cord = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"STRING_PIECE")]
            StringPiece = 2,
        }

        [global::ProtoBuf.ProtoContract()]
        public enum JSType
        {
            [global::ProtoBuf.ProtoEnum(Name = @"JS_NORMAL")]
            JsNormal = 0,
            [global::ProtoBuf.ProtoEnum(Name = @"JS_STRING")]
            JsString = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"JS_NUMBER")]
            JsNumber = 2,
        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class OneofOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class EnumOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(2, Name = @"allow_alias")]
        public bool AllowAlias
        {
            get { return __pbn__AllowAlias.GetValueOrDefault(); }
            set { __pbn__AllowAlias = value; }
        }
        public bool ShouldSerializeAllowAlias() => __pbn__AllowAlias != null;
        public void ResetAllowAlias() => __pbn__AllowAlias = null;
        private bool? __pbn__AllowAlias;

        [global::ProtoBuf.ProtoMember(3, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class EnumValueOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(1, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class ServiceOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(33, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class MethodOptions : global::ProtoBuf.IExtensible
    {
        private global::ProtoBuf.IExtension __pbn__extensionData;
        global::ProtoBuf.IExtension global::ProtoBuf.IExtensible.GetExtensionObject(bool createIfMissing)
            => global::ProtoBuf.Extensible.GetExtensionObject(ref __pbn__extensionData, createIfMissing);

        [global::ProtoBuf.ProtoMember(33, Name = @"deprecated")]
        [global::System.ComponentModel.DefaultValue(false)]
        public bool Deprecated
        {
            get { return __pbn__Deprecated ?? false; }
            set { __pbn__Deprecated = value; }
        }
        public bool ShouldSerializeDeprecated() => __pbn__Deprecated != null;
        public void ResetDeprecated() => __pbn__Deprecated = null;
        private bool? __pbn__Deprecated;

        [global::ProtoBuf.ProtoMember(34)]
        [global::System.ComponentModel.DefaultValue(IdempotencyLevel.IdempotencyUnknown)]
        public IdempotencyLevel idempotency_level
        {
            get { return __pbn__idempotency_level ?? IdempotencyLevel.IdempotencyUnknown; }
            set { __pbn__idempotency_level = value; }
        }
        public bool ShouldSerializeidempotency_level() => __pbn__idempotency_level != null;
        public void Resetidempotency_level() => __pbn__idempotency_level = null;
        private IdempotencyLevel? __pbn__idempotency_level;

        [global::ProtoBuf.ProtoMember(999, Name = @"uninterpreted_option")]
        public global::System.Collections.Generic.List<UninterpretedOption> UninterpretedOptions { get; } = new global::System.Collections.Generic.List<UninterpretedOption>();

        [global::ProtoBuf.ProtoContract()]
        public enum IdempotencyLevel
        {
            [global::ProtoBuf.ProtoEnum(Name = @"IDEMPOTENCY_UNKNOWN")]
            IdempotencyUnknown = 0,
            [global::ProtoBuf.ProtoEnum(Name = @"NO_SIDE_EFFECTS")]
            NoSideEffects = 1,
            [global::ProtoBuf.ProtoEnum(Name = @"IDEMPOTENT")]
            Idempotent = 2,
        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class UninterpretedOption
    {
        [global::ProtoBuf.ProtoMember(2, Name = @"name")]
        public global::System.Collections.Generic.List<NamePart> Names { get; } = new global::System.Collections.Generic.List<NamePart>();

        [global::ProtoBuf.ProtoMember(3, Name = @"identifier_value")]
        [global::System.ComponentModel.DefaultValue("")]
        public string IdentifierValue
        {
            get { return __pbn__IdentifierValue ?? ""; }
            set { __pbn__IdentifierValue = value; }
        }
        public bool ShouldSerializeIdentifierValue() => __pbn__IdentifierValue != null;
        public void ResetIdentifierValue() => __pbn__IdentifierValue = null;
        private string __pbn__IdentifierValue;

        [global::ProtoBuf.ProtoMember(4, Name = @"positive_int_value")]
        public ulong PositiveIntValue
        {
            get { return __pbn__PositiveIntValue.GetValueOrDefault(); }
            set { __pbn__PositiveIntValue = value; }
        }
        public bool ShouldSerializePositiveIntValue() => __pbn__PositiveIntValue != null;
        public void ResetPositiveIntValue() => __pbn__PositiveIntValue = null;
        private ulong? __pbn__PositiveIntValue;

        [global::ProtoBuf.ProtoMember(5, Name = @"negative_int_value")]
        public long NegativeIntValue
        {
            get { return __pbn__NegativeIntValue.GetValueOrDefault(); }
            set { __pbn__NegativeIntValue = value; }
        }
        public bool ShouldSerializeNegativeIntValue() => __pbn__NegativeIntValue != null;
        public void ResetNegativeIntValue() => __pbn__NegativeIntValue = null;
        private long? __pbn__NegativeIntValue;

        [global::ProtoBuf.ProtoMember(6, Name = @"double_value")]
        public double DoubleValue
        {
            get { return __pbn__DoubleValue.GetValueOrDefault(); }
            set { __pbn__DoubleValue = value; }
        }
        public bool ShouldSerializeDoubleValue() => __pbn__DoubleValue != null;
        public void ResetDoubleValue() => __pbn__DoubleValue = null;
        private double? __pbn__DoubleValue;

        [global::ProtoBuf.ProtoMember(7, Name = @"string_value")]
        public byte[] StringValue
        {
            get { return __pbn__StringValue; }
            set { __pbn__StringValue = value; }
        }
        public bool ShouldSerializeStringValue() => __pbn__StringValue != null;
        public void ResetStringValue() => __pbn__StringValue = null;
        private byte[] __pbn__StringValue;

        [global::ProtoBuf.ProtoMember(8, Name = @"aggregate_value")]
        [global::System.ComponentModel.DefaultValue("")]
        public string AggregateValue
        {
            get { return __pbn__AggregateValue ?? ""; }
            set { __pbn__AggregateValue = value; }
        }
        public bool ShouldSerializeAggregateValue() => __pbn__AggregateValue != null;
        public void ResetAggregateValue() => __pbn__AggregateValue = null;
        private string __pbn__AggregateValue;

        [global::ProtoBuf.ProtoContract()]
        public partial class NamePart
        {
            [global::ProtoBuf.ProtoMember(1, IsRequired = true)]
            public string name_part { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"is_extension", IsRequired = true)]
            public bool IsExtension { get; set; }

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class SourceCodeInfo
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"location")]
        public global::System.Collections.Generic.List<Location> Locations { get; } = new global::System.Collections.Generic.List<Location>();

        [global::ProtoBuf.ProtoContract()]
        public partial class Location
        {
            [global::ProtoBuf.ProtoMember(1, Name = @"path", IsPacked = true)]
            public int[] Paths { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"span", IsPacked = true)]
            public int[] Spans { get; set; }

            [global::ProtoBuf.ProtoMember(3, Name = @"leading_comments")]
            [global::System.ComponentModel.DefaultValue("")]
            public string LeadingComments
            {
                get { return __pbn__LeadingComments ?? ""; }
                set { __pbn__LeadingComments = value; }
            }
            public bool ShouldSerializeLeadingComments() => __pbn__LeadingComments != null;
            public void ResetLeadingComments() => __pbn__LeadingComments = null;
            private string __pbn__LeadingComments;

            [global::ProtoBuf.ProtoMember(4, Name = @"trailing_comments")]
            [global::System.ComponentModel.DefaultValue("")]
            public string TrailingComments
            {
                get { return __pbn__TrailingComments ?? ""; }
                set { __pbn__TrailingComments = value; }
            }
            public bool ShouldSerializeTrailingComments() => __pbn__TrailingComments != null;
            public void ResetTrailingComments() => __pbn__TrailingComments = null;
            private string __pbn__TrailingComments;

            [global::ProtoBuf.ProtoMember(6, Name = @"leading_detached_comments")]
            public global::System.Collections.Generic.List<string> LeadingDetachedComments { get; } = new global::System.Collections.Generic.List<string>();

        }

    }

    [global::ProtoBuf.ProtoContract()]
    public partial class GeneratedCodeInfo
    {
        [global::ProtoBuf.ProtoMember(1, Name = @"annotation")]
        public global::System.Collections.Generic.List<Annotation> Annotations { get; } = new global::System.Collections.Generic.List<Annotation>();

        [global::ProtoBuf.ProtoContract()]
        public partial class Annotation
        {
            [global::ProtoBuf.ProtoMember(1, Name = @"path", IsPacked = true)]
            public int[] Paths { get; set; }

            [global::ProtoBuf.ProtoMember(2, Name = @"source_file")]
            [global::System.ComponentModel.DefaultValue("")]
            public string SourceFile
            {
                get { return __pbn__SourceFile ?? ""; }
                set { __pbn__SourceFile = value; }
            }
            public bool ShouldSerializeSourceFile() => __pbn__SourceFile != null;
            public void ResetSourceFile() => __pbn__SourceFile = null;
            private string __pbn__SourceFile;

            [global::ProtoBuf.ProtoMember(3, Name = @"begin")]
            public int Begin
            {
                get { return __pbn__Begin.GetValueOrDefault(); }
                set { __pbn__Begin = value; }
            }
            public bool ShouldSerializeBegin() => __pbn__Begin != null;
            public void ResetBegin() => __pbn__Begin = null;
            private int? __pbn__Begin;

            [global::ProtoBuf.ProtoMember(4, Name = @"end")]
            public int End
            {
                get { return __pbn__End.GetValueOrDefault(); }
                set { __pbn__End = value; }
            }
            public bool ShouldSerializeEnd() => __pbn__End != null;
            public void ResetEnd() => __pbn__End = null;
            private int? __pbn__End;

        }

    }

}

#pragma warning restore CS1591, CS0612, CS3021
