﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/11/30

using UnityEngine;

namespace TFW.Map
{
    //河流切割线数据
    [Black]
    public class PolygonRiverSplitterData
    {
        public PolygonRiverSplitterData(Vector3 startVertexPos, Vector3 endVertexPos)
        {
            mStartVertexPos = startVertexPos;
            mEndVertexPos = endVertexPos;
        }

        public void Move(Vector3 offset)
        {
            mStartVertexPos += offset;
            mEndVertexPos += offset;
        }

        public Vector3 startVertexPosition { get { return mStartVertexPos; } set { mStartVertexPos = value; } }
        public Vector3 endVertexPosition { get { return mEndVertexPos; } set { mEndVertexPos = value; } }

        //用position来追踪vertex,即便vertex索引变了也可以通过位置得到最新的index
        Vector3 mStartVertexPos;
        Vector3 mEndVertexPos;
    }
}


#endif