{"skeleton": {"hash": "K/cEZzcl5vM", "spine": "4.2.33", "x": -425.7, "y": -245.45, "width": 855, "height": 932.27, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "O_A", "parent": "root", "x": 114.74, "y": 387.74, "scaleX": 0.903, "scaleY": 1.0168, "shearX": 26.3, "shearY": 4.07}, {"name": "O_Aa", "parent": "O_A", "length": 50}, {"name": "O_Ab", "parent": "O_A", "length": 50, "x": -3.55, "y": 0.42}, {"name": "O_F", "parent": "root", "x": 139.51, "y": 218.23, "scaleX": 0.9136, "shearX": 30.98}, {"name": "O_Fa", "parent": "O_F", "length": 30}, {"name": "O_Fb", "parent": "O_F", "length": 30, "x": -3.44, "y": 3.71}, {"name": "O_R", "parent": "root", "x": -202.85, "y": 263.8}, {"name": "O_Rdown", "parent": "root", "x": -77.33, "y": 235.73, "scaleX": 0.8752, "shearX": -25.58, "shearY": -1.91}, {"name": "I", "parent": "root", "length": 188.82, "rotation": 88.94, "x": 72.29, "y": -108.7}, {"name": "light", "parent": "I", "rotation": -88.94, "x": 172.23, "y": -1.49, "scaleX": 1.5076, "scaleY": 1.5076}, {"name": "O_Rdown2", "parent": "O_Rdown", "length": 15}, {"name": "O_Rdown3", "parent": "root", "x": -67.02, "y": 191.07, "scaleX": 0.7676, "scaleY": 0.9107, "shearX": -25.58, "shearY": -6.08}, {"name": "O_Rdown4", "parent": "O_Rdown3", "length": 15}, {"name": "O_Rdown5", "parent": "root", "x": -137.44, "y": 225.86, "scaleX": 0.7701, "scaleY": 0.8957, "shearX": -25.58, "shearY": -5.02}, {"name": "O_Rdown6", "parent": "O_Rdown5", "length": 15}, {"name": "O_Rdown7", "parent": "root", "x": -118.8, "y": 254.93, "scaleX": 0.6048, "scaleY": 0.7175, "shearX": -25.58, "shearY": -2.69}, {"name": "O_Rdown8", "parent": "O_Rdown7", "length": 15}, {"name": "S", "parent": "root", "length": 27.56, "rotation": -24.78, "x": 373.79, "y": 138.22}, {"name": "A", "parent": "S", "length": 29.04, "rotation": -89.68, "x": 26.78, "y": 0.17, "inherit": "noRotationOrReflection"}, {"name": "B", "parent": "S", "length": 28.56, "rotation": -91.29, "x": -23.66, "y": 0.21, "inherit": "noRotationOrReflection"}, {"name": "O_A2", "parent": "root", "x": -77.1, "y": 383.7, "scaleX": 0.9351, "scaleY": 0.9559, "shearX": -26.14, "shearY": -9.54}, {"name": "O_Aa2", "parent": "O_A2", "length": 50}, {"name": "O_Ab2", "parent": "O_A2", "length": 50, "x": 2.15, "y": 1.19}, {"name": "light2", "parent": "light"}, {"name": "light3", "parent": "root", "x": 6.83, "y": 604.88, "scaleX": 0.6706, "scaleY": 0.6706}], "slots": [{"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "O_Fb", "bone": "O_Fb", "attachment": "O_Fb"}, {"name": "O_Fa", "bone": "O_Fa", "attachment": "O_Fa"}, {"name": "Castle2", "bone": "root", "attachment": "Castle"}, {"name": "O_Rdown", "bone": "O_Rdown2", "attachment": "O_Rdown"}, {"name": "O_Rdown3", "bone": "O_Rdown6", "attachment": "O_Rdown"}, {"name": "O_Rdown2", "bone": "O_Rdown4", "attachment": "O_Rdown"}, {"name": "O_Rdown4", "bone": "O_Rdown8", "attachment": "O_Rdown"}, {"name": "O4", "bone": "root", "attachment": "O4"}, {"name": "O3", "bone": "root", "attachment": "O3"}, {"name": "O2", "bone": "root", "attachment": "O2"}, {"name": "O1", "bone": "root", "attachment": "O1"}, {"name": "S", "bone": "S", "attachment": "S"}, {"name": "B", "bone": "B", "attachment": "B"}, {"name": "A", "bone": "A", "attachment": "A"}, {"name": "I", "bone": "I", "attachment": "I"}, {"name": "<PERSON>_<PERSON><PERSON>", "bone": "root", "attachment": "<PERSON>_<PERSON><PERSON>"}, {"name": "O_R", "bone": "O_R", "attachment": "O_R"}, {"name": "O_Ab", "bone": "O_Ab", "attachment": "O_Ab"}, {"name": "O_Ab2", "bone": "O_Ab2", "attachment": "O_Ab"}, {"name": "O_Aa", "bone": "O_Aa", "attachment": "O_Aa"}, {"name": "O_Aa2", "bone": "O_Aa2", "color": "ddc677ff", "attachment": "O_Aa"}, {"name": "light", "bone": "root", "color": "ffffff64", "attachment": "light", "blend": "additive"}, {"name": "light5", "bone": "root", "color": "ffffff64", "attachment": "light", "blend": "additive"}, {"name": "light2", "bone": "root", "color": "ffffff64", "attachment": "light", "blend": "additive"}, {"name": "light4", "bone": "root", "color": "ffffff32", "attachment": "light", "blend": "additive"}, {"name": "light3", "bone": "light2", "attachment": "light", "blend": "additive"}, {"name": "light6", "bone": "light3", "attachment": "light", "blend": "additive"}, {"name": "light7", "bone": "light3", "attachment": "light", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"A": {"A": {"type": "mesh", "uvs": [0.58362, 0.0236, 0.97487, 0.64328, 0.97483, 0.85275, 0.76575, 0.97137, 0.38901, 0.97096, 0.0274, 0.76427, 0.0216, 0.56364, 0.46796, 0.02873], "triangles": [4, 5, 6, 0, 4, 7, 7, 4, 6, 0, 1, 4, 1, 3, 4, 2, 3, 1], "vertices": [-2.59, 2.32, 18.56, 17.07, 25.68, 17.03, 29.67, 9.06, 29.58, -5.25, 22.48, -18.96, 15.65, -19.14, -2.44, -2.08], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 38, "height": 34}}, "B": {"B": {"type": "mesh", "uvs": [0.62431, 0.0492, 0.81369, 0.47996, 1, 0.733, 1, 0.81856, 0.66837, 0.99945, 0.29478, 0.93414, 0.07269, 0.81323, 0, 0.62655, 0, 0.55468, 0.42947, 0.00827], "triangles": [5, 6, 7, 7, 8, 5, 4, 1, 2, 8, 1, 5, 9, 1, 8, 1, 9, 0, 4, 5, 1, 2, 3, 4], "vertices": [-0.73, 5.37, 13.33, 12.5, 21.53, 19.4, 24.35, 19.46, 30.59, 7.66, 28.74, -5.84, 24.93, -13.92, 18.83, -16.67, 16.46, -16.73, -1.92, -1.68], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 36, "height": 33}}, "Castle": {"Castle": {"type": "mesh", "uvs": [0.51536, 0.00133, 0.6327, 0.16592, 0.62806, 0.13368, 0.77698, 0.25157, 0.84395, 0.47625, 0.82593, 0.59042, 0.92614, 0.65515, 0.93268, 0.54243, 0.92571, 0.53218, 0.93161, 0.52605, 0.92678, 0.51887, 0.91103, 0.54304, 0.90766, 0.54096, 0.92327, 0.5177, 0.92084, 0.51141, 0.9367, 0.50519, 0.95025, 0.51924, 0.95064, 0.53139, 0.96674, 0.56429, 0.96297, 0.56641, 0.94851, 0.53453, 0.9415, 0.54271, 0.94186, 0.63477, 0.97336, 0.70053, 0.97413, 0.71541, 1, 0.73482, 1, 0.75608, 0.53113, 1, 0.52354, 1, 0.16155, 0.81917, 0, 0.68299, 0, 0.66473, 0.19806, 0.55931, 0.21022, 0.54333, 0.18928, 0.46381, 0.22856, 0.22331, 0.36788, 0.21304, 0.38749, 0.13874, 0.48633, 0], "triangles": [11, 12, 13, 18, 19, 20, 3, 1, 2, 10, 13, 14, 15, 10, 14, 9, 10, 15, 16, 9, 15, 9, 16, 17, 10, 11, 13, 20, 9, 17, 21, 7, 9, 8, 9, 7, 20, 21, 9, 36, 33, 34, 20, 17, 18, 4, 1, 3, 22, 7, 21, 6, 7, 22, 30, 31, 32, 6, 22, 23, 24, 25, 26, 29, 30, 32, 0, 37, 38, 37, 1, 36, 4, 5, 1, 0, 1, 37, 36, 1, 5, 5, 33, 36, 36, 34, 35, 5, 28, 33, 29, 32, 33, 28, 29, 33, 27, 28, 5, 27, 5, 6, 24, 27, 6, 24, 6, 23, 27, 24, 26], "vertices": [1, 0, 14.94, 615.4, 1, 1, 0, 115.26, 473.53, 1, 1, 0, 111.3, 501.32, 1, 1, 0, 238.62, 399.7, 1, 1, 0, 295.88, 206.03, 1, 1, 0, 280.47, 107.61, 1, 1, 0, 366.16, 51.82, 1, 1, 0, 371.75, 148.98, 1, 1, 0, 365.78, 157.82, 1, 1, 0, 370.83, 163.09, 1, 1, 0, 366.7, 169.29, 1, 1, 18, -22.95, 0.68, 1, 1, 18, -26.32, 1.1, 1, 1, 0, 363.7, 170.3, 1, 1, 0, 361.62, 175.72, 1, 1, 0, 375.18, 181.07, 1, 1, 0, 386.77, 168.97, 1, 1, 0, 387.1, 158.5, 1, 1, 18, 27.97, 4.01, 1, 1, 18, 25.81, 1, 1, 1, 0, 385.28, 155.79, 1, 1, 0, 379.28, 148.73, 1, 1, 0, 379.6, 69.38, 1, 1, 0, 406.53, 12.7, 1, 1, 0, 407.18, -0.13, 1, 1, 0, 429.3, -16.86, 1, 1, 0, 429.3, -35.19, 1, 1, 0, 28.42, -245.45, 1, 1, 0, 21.93, -245.45, 1, 1, 0, -287.57, -89.57, 1, 1, 0, -425.7, 27.81, 1, 1, 0, -425.7, 43.56, 1, 1, 0, -256.35, 134.43, 1, 1, 0, -245.96, 148.2, 1, 1, 0, -263.86, 216.75, 1, 1, 0, -230.28, 424.06, 1, 1, 0, -111.16, 432.91, 1, 1, 0, -94.39, 496.96, 1, 1, 0, -9.88, 616.55, 1], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 46, 48, 46, 44, 44, 42, 42, 40, 40, 38, 34, 36, 38, 36, 32, 34, 30, 32, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12], "width": 855, "height": 862}}, "Castle2": {"Castle": {"type": "mesh", "uvs": [0.59302, 0.54972, 0.611, 0.52297, 0.62836, 0.50298, 0.64386, 0.4913, 0.66277, 0.47992, 0.67826, 0.475, 0.69469, 0.47347, 0.70988, 0.47531, 0.72166, 0.47931, 0.74677, 0.49376, 0.75917, 0.56847, 0.58837, 0.62443], "triangles": [10, 11, 0, 1, 10, 0, 3, 1, 2, 1, 3, 6, 5, 3, 4, 5, 6, 3, 6, 8, 1, 7, 8, 6, 8, 10, 1, 9, 10, 8], "vertices": [1, 0, 81.34, 142.7, 1, 1, 0, 96.71, 165.75, 1, 1, 0, 111.55, 182.98, 1, 1, 0, 124.8, 193.05, 1, 1, 0, 140.97, 202.86, 1, 1, 0, 154.22, 207.1, 1, 1, 0, 168.27, 208.42, 1, 1, 0, 181.25, 206.83, 1, 1, 0, 191.32, 203.39, 1, 1, 0, 212.79, 190.93, 1, 1, 0, 223.39, 126.53, 1, 1, 0, 77.36, 78.29, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 22, 22, 20, 20, 18], "width": 855, "height": 862}}, "I": {"I": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-14.15, -32.28, -15.3, 29.71, 189.67, 33.5, 190.82, -28.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 205}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.27, 317.93, 5.27, 317.93, 5.27, 521.93, 208.27, 521.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [244.31, 147.31, 41.31, 147.31, 41.31, 351.31, 244.31, 351.31], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light3": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [122.18, -4.09, -122.18, -4.09, -122.18, 4.09, 122.18, 4.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light4": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-46.88, 300.86, -146.61, 448.03, -47.16, 515.42, 52.56, 368.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light5": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.07, 572.76, -23.86, 572.76, -23.86, 631.98, 35.07, 631.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light6": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [122.18, -4.09, -122.18, -4.09, -122.18, 4.09, 122.18, 4.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "light7": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.09, -122.18, -4.09, 122.18, 4.09, 122.18, 4.09, -122.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 204}}, "O1": {"O1": {"x": -137.2, "y": 225.55, "width": 21, "height": 30}}, "O2": {"O2": {"x": -118.7, "y": 253.55, "width": 16, "height": 24}}, "O3": {"O3": {"x": -77.7, "y": 235.55, "width": 22, "height": 26}}, "O4": {"O4": {"x": -66.2, "y": 191.05, "width": 19, "height": 27}}, "O_Aa": {"O_Aa": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.5, -73.5, -73.5, -73.5, -73.5, 73.5, 73.5, 73.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 147}}, "O_Aa2": {"O_Aa": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.5, -73.5, -73.5, -73.5, -73.5, 73.5, 73.5, 73.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 147}}, "O_Ab": {"O_Ab": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.5, -73.5, -73.5, -73.5, -73.5, 73.5, 73.5, 73.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 147}}, "O_Ab2": {"O_Ab": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [73.5, -73.5, -73.5, -73.5, -73.5, 73.5, 73.5, 73.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 147}}, "O_Fa": {"O_Fa": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [47.5, -47.5, -47.5, -47.5, -47.5, 47.5, 47.5, 47.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 95}}, "O_Fb": {"O_Fb": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [47.5, -47.5, -47.5, -47.5, -47.5, 47.5, 47.5, 47.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 95}}, "O_R": {"O_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.5, -36.5, -35.5, -36.5, -35.5, 36.5, 35.5, 36.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 73}}, "O_Rdown": {"O_Rdown": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -24.5, -24, -24.5, -24, 24.5, 24, 24.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 49}}, "O_Rdown2": {"O_Rdown": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -24.5, -24, -24.5, -24, 24.5, 24, 24.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 49}}, "O_Rdown3": {"O_Rdown": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -24.5, -24, -24.5, -24, 24.5, 24, 24.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 49}}, "O_Rdown4": {"O_Rdown": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24, -24.5, -24, -24.5, -24, 24.5, 24, 24.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 49}}, "O_Rup": {"O_Rup": {"type": "mesh", "uvs": [0.54708, 0.02293, 0.68948, 0.0836, 0.79583, 0.17415, 0.89032, 0.27189, 0.97271, 0.43808, 0.99421, 0.58541, 0.98342, 0.698, 0.96031, 0.78333, 0.84711, 0.91763, 0.73575, 0.99312, 0.54036, 0.99319, 0.36511, 0.91862, 0.24862, 0.85114, 0.17465, 0.78223, 0.0909, 0.66153, 0.046, 0.56681, 0.00063, 0.41169, 0.0216, 0.28361, 0.08364, 0.12751, 0.17544, 0.05751, 0.27521, 0.01524, 0.41795, 0.00436, 0.19738, 0.63112, 0.15154, 0.52849, 0.12863, 0.3898, 0.15154, 0.26359, 0.21702, 0.1665, 0.3398, 0.1138, 0.49532, 0.15402, 0.62792, 0.24833, 0.73105, 0.38702, 0.77688, 0.51878, 0.78343, 0.64083, 0.74742, 0.75594, 0.65738, 0.84055, 0.51987, 0.86828, 0.38727, 0.8239, 0.27923, 0.73237], "triangles": [27, 20, 21, 28, 21, 0, 28, 0, 1, 27, 21, 28, 27, 26, 19, 27, 19, 20, 18, 19, 26, 29, 28, 1, 29, 1, 2, 25, 18, 26, 17, 18, 25, 30, 29, 2, 30, 2, 3, 24, 17, 25, 16, 17, 24, 30, 3, 4, 31, 30, 4, 25, 28, 24, 28, 23, 24, 16, 24, 23, 15, 16, 23, 31, 4, 5, 29, 22, 23, 14, 15, 23, 32, 31, 5, 22, 14, 23, 26, 27, 28, 6, 32, 5, 29, 23, 28, 25, 26, 28, 30, 22, 29, 31, 22, 30, 37, 31, 32, 22, 31, 37, 32, 36, 37, 7, 33, 32, 13, 14, 22, 13, 22, 37, 6, 7, 32, 33, 36, 32, 33, 35, 36, 12, 13, 37, 12, 37, 36, 34, 35, 33, 8, 33, 7, 34, 33, 8, 11, 12, 36, 11, 36, 35, 9, 34, 8, 10, 35, 34, 10, 34, 9, 11, 35, 10], "vertices": [-72.95, 454.25, -55.58, 445.51, -42.6, 432.47, -31.08, 418.4, -21.03, 394.47, -18.4, 373.25, -19.72, 357.04, -22.54, 344.75, -36.35, 325.41, -49.93, 314.54, -73.77, 314.53, -95.15, 325.27, -109.36, 334.99, -118.39, 344.91, -128.61, 362.29, -134.08, 375.93, -139.62, 398.27, -137.06, 416.71, -129.49, 439.19, -118.29, 449.27, -106.12, 455.36, -88.71, 456.92, -115.62, 366.67, -121.21, 381.45, -124, 401.42, -121.21, 419.6, -113.22, 433.58, -98.24, 441.16, -79.27, 435.37, -63.09, 421.79, -50.51, 401.82, -44.92, 382.85, -44.12, 365.27, -48.51, 348.7, -59.5, 336.51, -76.27, 332.52, -92.45, 338.91, -105.63, 352.09], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 44], "width": 122, "height": 144}}, "S": {"S": {"type": "mesh", "uvs": [0.09071, 0.00733, 0.98129, 0.69284, 0.98278, 0.95514, 0.90607, 0.98333, 0.02679, 0.25403, 0.02446, 0.04566], "triangles": [4, 5, 0, 1, 4, 0, 3, 1, 2, 3, 4, 1], "vertices": [-25.03, 5.09, 27.54, 5.95, 31.02, -1.4, 27.63, -3.93, -24.96, -3.3, -27.78, 2.51], "hull": 6, "edges": [0, 10, 2, 4, 4, 6, 8, 10, 6, 8, 0, 2], "width": 54, "height": 31}}}}], "animations": {"idle": {"bones": {"S": {"rotate": [{"value": 10.84, "curve": [0.444, 10.84, 0.889, -9.87]}, {"time": 1.3333, "value": -9.87, "curve": [1.778, -9.87, 2.222, 10.84]}, {"time": 2.6667, "value": 10.84, "curve": [3.111, 10.84, 3.556, -9.87]}, {"time": 4, "value": -9.87, "curve": [4.444, -9.87, 4.889, 10.84]}, {"time": 5.3333, "value": 10.84, "curve": [5.778, 10.84, 6.222, -9.87]}, {"time": 6.6667, "value": -9.87, "curve": [7.111, -9.87, 7.556, 10.84]}, {"time": 8, "value": 10.84, "curve": [8.444, 10.84, 8.889, -9.87]}, {"time": 9.3333, "value": -9.87, "curve": [9.778, -9.87, 10.222, 10.84]}, {"time": 10.6667, "value": 10.84}]}, "O_Ab": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Aa": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Fa": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Fb": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Rdown6": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Rdown8": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Rdown2": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Rdown4": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_R": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Aa2": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "O_Ab2": {"rotate": [{}, {"time": 10.6667, "value": -720}]}, "light": {"scale": [{"x": 0.687, "y": 0.472, "curve": [0.222, 0.687, 0.444, 1, 0.222, 0.472, 0.444, 1]}, {"time": 0.6667, "curve": [0.889, 1, 1.111, 0.687, 0.889, 1, 1.111, 0.472]}, {"time": 1.3333, "x": 0.687, "y": 0.472, "curve": [1.556, 0.687, 1.778, 1, 1.556, 0.472, 1.778, 1]}, {"time": 2, "curve": [2.222, 1, 2.444, 0.687, 2.222, 1, 2.444, 0.472]}, {"time": 2.6667, "x": 0.687, "y": 0.472, "curve": [2.889, 0.687, 3.111, 1, 2.889, 0.472, 3.111, 1]}, {"time": 3.3333, "curve": [3.556, 1, 3.778, 0.687, 3.556, 1, 3.778, 0.472]}, {"time": 4, "x": 0.687, "y": 0.472, "curve": [4.222, 0.687, 4.444, 1, 4.222, 0.472, 4.444, 1]}, {"time": 4.6667, "curve": [4.889, 1, 5.111, 0.687, 4.889, 1, 5.111, 0.472]}, {"time": 5.3333, "x": 0.687, "y": 0.472, "curve": [5.556, 0.687, 5.778, 1, 5.556, 0.472, 5.778, 1]}, {"time": 6, "curve": [6.222, 1, 6.444, 0.687, 6.222, 1, 6.444, 0.472]}, {"time": 6.6667, "x": 0.687, "y": 0.472, "curve": [6.889, 0.687, 7.111, 1, 6.889, 0.472, 7.111, 1]}, {"time": 7.3333, "curve": [7.556, 1, 7.778, 0.687, 7.556, 1, 7.778, 0.472]}, {"time": 8, "x": 0.687, "y": 0.472, "curve": [8.222, 0.687, 8.444, 1, 8.222, 0.472, 8.444, 1]}, {"time": 8.6667, "curve": [8.889, 1, 9.111, 0.687, 8.889, 1, 9.111, 0.472]}, {"time": 9.3333, "x": 0.687, "y": 0.472, "curve": [9.556, 0.687, 9.778, 1, 9.556, 0.472, 9.778, 1]}, {"time": 10, "curve": [10.222, 1, 10.444, 0.687, 10.222, 1, 10.444, 0.472]}, {"time": 10.6667, "x": 0.687, "y": 0.472}]}, "light2": {"scale": [{"time": 2.6667, "curve": [2.711, 1, 2.756, 1.378, 2.711, 1, 2.756, 1.378]}, {"time": 2.8, "x": 1.378, "y": 1.378, "curve": [2.844, 1.378, 2.889, 0.721, 2.844, 1.378, 2.889, 0.721]}, {"time": 2.9333, "x": 0.721, "y": 0.721, "curve": [2.978, 0.721, 3.022, 1, 2.978, 0.721, 3.022, 1]}, {"time": 3.0667, "curve": [3.111, 1, 3.156, 0.846, 3.111, 1, 3.156, 0.846]}, {"time": 3.2, "x": 0.846, "y": 0.846, "curve": [3.244, 0.846, 3.289, 1.174, 3.244, 0.846, 3.289, 1.174]}, {"time": 3.3333, "x": 1.174, "y": 1.174, "curve": [3.378, 1.174, 3.422, 1, 3.378, 1.174, 3.422, 1]}, {"time": 3.4667, "curve": "stepped"}, {"time": 4, "curve": [4.044, 1, 4.089, 1.378, 4.044, 1, 4.089, 1.378]}, {"time": 4.1333, "x": 1.378, "y": 1.378, "curve": [4.178, 1.378, 4.222, 0.721, 4.178, 1.378, 4.222, 0.721]}, {"time": 4.2667, "x": 0.721, "y": 0.721, "curve": [4.311, 0.721, 4.356, 1, 4.311, 0.721, 4.356, 1]}, {"time": 4.4, "curve": [4.444, 1, 4.489, 0.846, 4.444, 1, 4.489, 0.846]}, {"time": 4.5333, "x": 0.846, "y": 0.846, "curve": [4.578, 0.846, 4.622, 1.174, 4.578, 0.846, 4.622, 1.174]}, {"time": 4.6667, "x": 1.174, "y": 1.174, "curve": [4.711, 1.174, 4.756, 1, 4.711, 1.174, 4.756, 1]}, {"time": 4.8}]}, "I": {"scale": [{"time": 2.6667, "curve": [2.722, 1, 2.778, 1.163, 2.722, 1, 2.778, 1.163]}, {"time": 2.8333, "x": 1.163, "y": 1.163, "curve": [2.889, 1.164, 2.944, 1.125, 2.889, 1.164, 2.944, 1.125]}, {"time": 3, "x": 1.125, "y": 1.125, "curve": [3.056, 1.125, 3.111, 1.158, 3.056, 1.125, 3.111, 1.158]}, {"time": 3.1667, "x": 1.158, "y": 1.158, "curve": [3.222, 1.158, 3.278, 1.147, 3.222, 1.158, 3.278, 1.147]}, {"time": 3.3333, "x": 1.147, "y": 1.147, "curve": "stepped"}, {"time": 4.8333, "x": 1.147, "y": 1.147, "curve": [5, 1.147, 5.167, 1, 5, 1.147, 5.167, 1]}, {"time": 5.3333}]}, "light3": {"rotate": [{"time": 5, "curve": [5.333, 0.09, 5.667, -350]}, {"time": 6, "value": -350}, {"time": 10.6667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 5, "x": 0, "y": 0, "curve": [5.167, 0, 5.333, 1.841, 5.167, 0, 5.333, 1.84]}, {"time": 5.5, "x": 1.841, "y": 1.841, "curve": [5.667, 1.841, 5.833, 0, 5.667, 1.841, 5.833, 0]}, {"time": 6, "x": 0, "y": 0}]}}}}}