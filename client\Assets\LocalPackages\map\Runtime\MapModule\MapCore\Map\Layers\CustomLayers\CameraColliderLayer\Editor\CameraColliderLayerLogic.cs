﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.12.4
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum CameraColliderEditorToolType
    {
        CreateOutline,
        EditVertex,
        MoveAndRotate,
    }

    [ExecuteInEditMode]
    [Black]
    public class CameraColliderLayerLogic : MapLayerLogic
    {
        public CameraColliderEditorToolType operation = CameraColliderEditorToolType.CreateOutline;

        public CameraColliderLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as CameraColliderLayer;
                return layer;
            }
        }
    }
}


#endif