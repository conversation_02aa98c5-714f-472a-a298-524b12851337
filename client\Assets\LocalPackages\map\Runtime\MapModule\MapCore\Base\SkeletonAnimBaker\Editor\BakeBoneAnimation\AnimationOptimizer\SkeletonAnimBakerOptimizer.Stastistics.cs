﻿#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    //优化前后对比
    public static partial class SkeletonAnimBakerOptimizer
    {
        public class TextureStats
        {
            public TextureStats(int textureMemorySize, string texturePath)
            {
                this.textureMemorySize = textureMemorySize;
                this.texturePath = texturePath;
            }

            public int textureMemorySize;
            public string texturePath;
        }

        static void CollectOriginalStats(string bakeFolderPath)
        {
            mOriginalTextures.Clear();

            var entries = Directory.EnumerateFileSystemEntries(bakeFolderPath, "*.*", SearchOption.AllDirectories);
            foreach (var path in entries)
            {
                if (path.EndsWith(".meta"))
                {
                    continue;
                }
                string validPath = Utils.ConvertToUnityAssetsPath(path.Replace('\\', '/'));
                string name = Utils.GetPathName(validPath, false);
                if (name == MapCoreDef.BAKED_ANIM_TEXTURE_NAME)
                {
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(validPath);
                    if (texture != null)
                    {
                        int memorySize = 0;
                        if (texture.format == TextureFormat.RGBA32)
                        {
                            memorySize = texture.width * texture.height * 4;
                        }
                        else
                        {
                            memorySize = texture.width * texture.height * 4 * 2;
                        }
                        mOriginalTextures.Add(new TextureStats(memorySize, validPath));
                    }
                }
            }
        }

        //优化时删除的贴图信息
        static List<TextureStats> mRemovedTextures = new List<TextureStats>();
        static List<TextureStats> mOriginalTextures = new List<TextureStats>();
    }
}

#endif