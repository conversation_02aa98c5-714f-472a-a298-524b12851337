%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: T_Bld_Portal_02
  m_Shader: {fileID: 4800000, guid: d6d86b0a4afcc46459a903f798f412df, type: 3}
  m_ShaderKeywords: _ALPHAPREMULTIPLY_ON _USE8NEIGHBOURHOOD_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    AlphaDepth: true
    IGNOREPROJECTOR: true
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ExtraLightTex:
        m_Texture: {fileID: 2800000, guid: 9b05e731b32018f41ad9a3918b7acc78, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 358ef570ae4b92e4882f3c432df7bb8d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - DayNightToggle: 0
    - PixelSnap: 0
    - _BlendAmount: 0
    - _Brightness: 1
    - _BumpScale: 1
    - _Cull: 0
    - _CustomRenderQueue: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 10
    - _EnableExternalAlpha: 0
    - _ExtraLightIntensity: 1
    - _ExtraLightTexClip: 0.01
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Hue: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineMipLevel: 0
    - _OutlineReferenceTexWidth: 1024
    - _OutlineSmoothness: 1
    - _OutlineWidth: 3
    - _Parallax: 0.02
    - _RenderQueue: 0
    - _Saturation: 1
    - _ShadowAlphaCutoff: 0.1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilComp: 8
    - _StencilRef: 1
    - _ThresholdEnd: 0.25
    - _UVSec: 0
    - _Use8Neighbourhood: 1
    - _ZWrite: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 0, a: 1}
    - _OverlayColor: {r: 0, g: 0, b: 0, a: 0}
