﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerView : MapLayerView
    {
        public void DestroySplitData()
        {
            foreach (var p in mRectMeshViews)
            {
                Utils.DestroyObject(p.Value);
            }

            for (int i = 0; i < mRectObjectPool.Count; ++i)
            {
                Utils.DestroyObject(mRectObjectPool[i]);
            }

            if (mPlaneMesh != null)
            {
                Utils.DestroyObject(mPlaneMesh);
                mPlaneMesh = null;
            }
        }

        void CreateMesh()
        {
            if (mPlaneMesh == null)
            {
                mPlaneMesh = new Mesh();
                mPlaneMesh.vertices = new Vector3[4]
                {
                    new Vector3(-0.5f, 0, -0.5f),
                    new Vector3(-0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, -0.5f),
                };

                mPlaneMesh.subMeshCount = 2;
                var indices = new int[6] { 0, 1, 2, 0, 2, 3 };

                var layerData = mLayerData as SplitFogLayerData;
                string prefabPath = layerData.GetAssetPath(15);
                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);
                mPlaneMaterials = prefab.GetComponent<MeshRenderer>().sharedMaterials;
                mPlaneMesh.uv = prefab.GetComponent<MeshFilter>().sharedMesh.uv;
                mPlaneMesh.uv2 = prefab.GetComponent<MeshFilter>().sharedMesh.uv2;
                mPlaneMesh.SetIndices(indices, MeshTopology.Triangles, 0);
                mPlaneMesh.SetIndices(indices, MeshTopology.Triangles, 1);
                mPlaneMesh.RecalculateNormals();
                mPlaneMesh.RecalculateBounds();
                mPlaneMesh.UploadMeshData(true);
            }
        }

        GameObject CreateRect(int id, Vector3 pos, float width, float height)
        {
            var layerData = mLayerData as SplitFogLayerData;
            GameObject obj = null;
            if (mRectObjectPool.Count > 0)
            {
                obj = mRectObjectPool[mRectObjectPool.Count - 1];
                mRectObjectPool.RemoveAt(mRectObjectPool.Count - 1);
            }
            else
            {
                obj = new GameObject();
                var filter = obj.AddComponent<MeshFilter>();
                filter.sharedMesh = mPlaneMesh;
                var renderer = obj.AddComponent<MeshRenderer>();
                renderer.sharedMaterials = mPlaneMaterials;
            }
#if UNITY_EDITOR
            obj.name = $"fog rect {id}";
#endif
            obj.SetActive(true);
            pos.y = layerData.fogHeight;
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(width, 1, height);
            return obj;
        }

        void DestroyRect(GameObject rect)
        {
            mRectObjectPool.Add(rect);
            rect.SetActive(false);
        }

        public void OnCreateRectFog(FogRect fog)
        {
            if (!CanControlInPlaneLOD())
            {
                return;
            }

            GameObject obj = CreateRect(fog.id, fog.center, fog.width, fog.height);
            mRectMeshViews.Add(fog.id, obj);
        }

        public void OnRemoveRectFog(FogRect fog)
        {
            if (!CanControlInPlaneLOD())
            {
                return;
            }

            GameObject obj;
            mRectMeshViews.TryGetValue(fog.id, out obj);
            if (obj != null)
            {
                DestroyRect(obj);
                mRectMeshViews.Remove(fog.id);
            }
        }

        public void OnRectFogVisibilityChange(FogRect fog, bool visible)
        {
            if (!CanControlInPlaneLOD())
            {
                return;
            }

            if (visible)
            {
                OnCreateRectFog(fog);
            }
            else
            {
                OnRemoveRectFog(fog);
            }
        }

        List<GameObject> mRectObjectPool = new List<GameObject>();
        Dictionary<int, GameObject> mRectMeshViews = new Dictionary<int, GameObject>();
        Mesh mPlaneMesh;
        Material[] mPlaneMaterials;
    }
}
