﻿






#if !UNITY_WEBGL

//#define UNITY_IPHONE

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEditor.Callbacks;
using UnityEditor.PackageManager;
using UnityEngine;

namespace TFW.SDK.Editor
{
    // 防止平台的package被unity给裁剪掉
    public class ScriptStripProcess : IPreprocessBuildWithReport, IPostprocessBuildWithReport
    {
        public int callbackOrder => 0;

        public static string TemporaryFolder
        {
            get { return $"{Application.dataPath}/Temporary/"; }
        }

        /// <summary>
        /// 由于Pacakge的Link.xml文件再Pacakge文件夹内无法生效，故打包的时候额定将所有Package的link.xml文件都合并后拷贝进Assets目录下
        /// </summary>
        public static string LinkFilePath
        {
            get { return $"{TemporaryFolder}link.xml"; }
        }

        // build 开始时
        public void OnPreprocessBuild(UnityEditor.Build.Reporting.BuildReport buildReport)
        {
            CreateMergedLinkFromPackages();

            AssetDatabase.Refresh();
        }

        // build 完成时
        public void OnPostprocessBuild(UnityEditor.Build.Reporting.BuildReport buildReport)
        {
            if (File.Exists(LinkFilePath))
                File.Delete(LinkFilePath);
            if (!Directory.EnumerateFiles(TemporaryFolder, "*").Any())
                Directory.Delete(TemporaryFolder);

            AssetDatabase.Refresh();
        }

        private static void CreateMergedLinkFromPackages()
        {
            var request = Client.List();
            do
            {
            } while (!request.IsCompleted);

            if (request.Status == StatusCode.Success)
            {
                List<string> xmlPathList = new List<string>();
                foreach (var package in request.Result)
                {
                    var path = package.resolvedPath;
                    xmlPathList.AddRange(Directory.EnumerateFiles(path, "link.xml", SearchOption.AllDirectories)
                        .ToList());
                }

                if (xmlPathList.Count <= 0)
                    return;

                var xmlList = xmlPathList.Select(XDocument.Load).ToArray();

                var combinedXml = xmlList.First();
                foreach (var xDocument in xmlList.Where(xml => xml != combinedXml))
                {
                    combinedXml.Root.Add(xDocument.Root.Elements());
                }

                if (!Directory.Exists(TemporaryFolder))
                    Directory.CreateDirectory(TemporaryFolder);
                combinedXml.Save(LinkFilePath);
            }
            else if (request.Status >= StatusCode.Failure)
            {
                Debug.LogError(request.Error.message);
            }
        }
    }
}
#endif