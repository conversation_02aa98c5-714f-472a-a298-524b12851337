﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class MapLayerXZRectangleCoordinateConverter : MapLayerCoordinateConverter
    {
        public override Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            float dx = position.x - mLayerData.layerOffset.x;
            float dz = position.z - mLayerData.layerOffset.z;
            return new Vector2Int(Mathf.FloorToInt(dx / tileWidth), Mathf.FloorToInt(dz / tileHeight));
        }

        public override Vector2Int FromWorldPositionToCoordinateUpperBounds(Vector3 position)
        {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            float dx = position.x - mLayerData.layerOffset.x;
            float dz = position.z - mLayerData.layerOffset.z;
            return new Vector2Int(Mathf.CeilToInt(dx / tileWidth), Mathf.CeilToInt(dz / tileHeight));
        }

        public override Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            return new Vector3(x * tileWidth, 0, y * tileHeight) + mLayerData.layerOffset;
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            return new Vector3(x * tileWidth + tileWidth * 0.5f, 0, y * tileHeight + tileHeight * 0.5f) + mLayerData.layerOffset;
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height)
        {
            float tileWidth = mLayerData.tileWidth;
            float tileHeight = mLayerData.tileHeight;
            var lt = FromCoordinateToWorldPosition(x, y);
            var rb = FromCoordinateToWorldPosition(x + width - 1, y + height - 1) + new Vector3(tileWidth, 0, tileHeight);
            return (lt + rb) * 0.5f;
        }
    }
}
