﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    class DrawBounds : MonoBeh<PERSON>our
    {
        private void OnDrawGizmos()
        {
            Color oldColor = Gizmos.color;
            Gizmos.color = color;
            var worldSpaceBounds = bounds;
            if (!worldSpace)
            {
                worldSpaceBounds = Utils.TransformBounds(bounds, transform);
            }
            Gizmos.DrawWireCube(worldSpaceBounds.center, worldSpaceBounds.size);
            Gizmos.color = oldColor;
        }

        public Bounds bounds;
        public Color color = Color.white;
        public bool worldSpace = true;
    }
}
