﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadMapCollisionLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new MapCollisionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.MapCollisionLayerData LoadMapCollisionLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            PrefabOutlineType displayType = PrefabOutlineType.NavMeshObstacle;
            float displayVertexRadius = 5.0f;

            displayType = (PrefabOutlineType)reader.ReadInt32();
            displayVertexRadius = reader.ReadSingle();
            float maxConnectionDistance = 10.0f;
            if (version.minorVersion >= 3)
            {
                maxConnectionDistance = reader.ReadSingle();
            }

            int n = reader.ReadInt32();
            config.MapCollisionData[] objects = new config.MapCollisionData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCollisionData(reader);
            }

            config.Detector[] detectors = new config.Detector[0];
            if (version.minorVersion >= 2)
            {
                //load detectors
                int detectorCount = reader.ReadInt32();
                detectors = new config.Detector[detectorCount];
                for (int i = 0; i < detectorCount; ++i)
                {
                    var pos = Utils.ReadVector3(reader);
                    var type = reader.ReadInt32();
                    detectors[i] = new config.Detector(pos, type);
                }
            }

            var layer = new config.MapCollisionLayerData(layerID, layerName, layerOffset, null, width, height, objects, displayType, displayVertexRadius, detectors, maxConnectionDistance);
            layer.active = active;
            return layer;
        }

        static config.MapCollisionData LoadCollisionData(BinaryReader reader)
        {
            var data = new config.MapCollisionData();
            data.SetID(AllocateID());
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.navMeshObstacleOutlines.Add(Utils.ReadVector2(reader));
            }

            n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.objectPlacementOutlines.Add(Utils.ReadVector2(reader));
            }

            data.radius = reader.ReadSingle();
            data.isExtandable = reader.ReadBoolean();
            data.attribute = (CollisionAttribute)reader.ReadInt32();
            data.attribute |= CollisionAttribute.IsAutoExpandingObstacle;
            data.type = reader.ReadInt32();

            return data;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        //load objects in range
        public void LoadObjectsInRange(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            LoadObjects(reader);

            reader.Close();
        }

        void LoadObjects(BinaryReader reader)
        {
            mLayerData.isLoading = true;
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var collisionData = LoadCollisionData(reader);
                var outlineDatas = new OutlineData[2];
                var vertices = Utils.ConvertToVector3List(collisionData.navMeshObstacleOutlines);
                outlineDatas[0] = new OutlineData(vertices);
                var placementOutlineVertices = Utils.ConvertToVector3List(collisionData.objectPlacementOutlines);
                if (placementOutlineVertices.Count == 0)
                {
                    outlineDatas[1] = new OutlineData(vertices);
                }
                else
                {
                    outlineDatas[1] = new OutlineData(placementOutlineVertices);
                }

                var obj = new MapCollisionData(collisionData.id, map, outlineDatas, collisionData.radius, collisionData.isExtandable, collisionData.attribute, collisionData.type, false);
                mLayerData.AddObjectData(obj);
            }

            mLayerData.isLoading = false;

            RefreshObjectsInViewport();
        }
    }
}

#endif