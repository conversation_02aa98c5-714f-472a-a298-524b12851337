﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        //保存山体与相机的碰撞mesh
        void Save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.<PERSON><PERSON><PERSON><PERSON>, writer);

            writer.Write(VersionSetting.CameraColliderStructVersion);

            //-------------------version 1 start------------------------------
            var cameraColliderLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER) as CameraColliderLayer;
            writer.Write(cameraColliderLayer != null ? true : false);
            if (cameraColliderLayer != null)
            {
                Vector3[] vertices;
                int[] indices;
                cameraColliderLayer.GetCombinedMesh(out vertices, out indices);
                Utils.WriteVector3Array(writer, vertices);
                Utils.WriteIntArray(writer, indices);
            }
            //-------------------version 1 end------------------------------
        }
    }
}

#endif