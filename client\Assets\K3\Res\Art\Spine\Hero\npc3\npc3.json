{"skeleton": {"hash": "zeABcOZ53b4", "spine": "4.2.33", "x": -327.68, "y": -67.99, "width": 709.93, "height": 2357.84, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "All", "parent": "root", "length": 100, "x": 460.75, "y": 1277.03}, {"name": "All2", "parent": "All", "x": -373.91, "y": 5.45, "icon": "arrowsB"}, {"name": "body", "parent": "All2", "length": 126.01, "rotation": 84.91, "x": 0.4, "y": 17.62}, {"name": "body2", "parent": "body", "length": 423.46, "rotation": 103.75, "x": 126.01, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 116.81, "rotation": 90, "x": 423.46, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 259.86, "rotation": 1.92, "x": 116.81}, {"name": "eyebrow_L3", "parent": "head", "length": 14.48, "rotation": 45.43, "x": 110.71, "y": -108.27}, {"name": "eyebrow_L2", "parent": "eyebrow_L3", "length": 29.99, "rotation": 58.37, "x": 14.48}, {"name": "eyebrow_L", "parent": "eyebrow_L2", "length": 19.44, "rotation": 8.99, "x": 29.99}, {"name": "eyebrow_R3", "parent": "head", "length": 24.7, "rotation": -67.1, "x": 117.66, "y": 49.08}, {"name": "eyebrow_R2", "parent": "eyebrow_R3", "length": 30.5, "rotation": -45.25, "x": 24.7}, {"name": "eyebrow_R", "parent": "eyebrow_R2", "length": 16.84, "rotation": 2, "x": 30.5}, {"name": "eye_L", "parent": "head", "x": 87.92, "y": -70.89}, {"name": "eye_R", "parent": "head", "x": 92.66, "y": 19.26}, {"name": "sh_R", "parent": "body2", "length": 207.32, "rotation": 82.8, "x": 368.59, "y": -4.8}, {"name": "arm_R", "parent": "sh_R", "length": 350.01, "rotation": 71.92, "x": 207.32, "inherit": "noScale"}, {"name": "arm_R2", "parent": "arm_R", "length": 366.9, "rotation": 40.09, "x": 350.01, "inherit": "noScale"}, {"name": "sh_L", "parent": "body2", "length": 112, "rotation": -117.28, "x": 352.41, "y": -68.63}, {"name": "tun", "parent": "All2", "length": 278.69, "rotation": -95.53, "x": -0.85, "y": -18.2}, {"name": "leg_L", "parent": "tun", "x": 102, "y": 135.04}, {"name": "hand_L2", "parent": "leg_L", "rotation": -6.67, "x": -6.43, "y": 103.68}, {"name": "hand_L", "parent": "hand_L2", "length": 107.71, "rotation": 17.88, "x": 1.82, "y": 1.05}, {"name": "hand_L3", "parent": "hand_L", "length": 121.12, "rotation": -17.88, "x": 107.71}, {"name": "arm_L", "parent": "hand_L2", "length": 330.57, "rotation": -133.75, "x": -1.78, "y": -1.24}, {"name": "leg_R", "parent": "tun", "x": 100.89, "y": -172.33}, {"name": "leg_R2", "parent": "leg_R", "length": 604.85, "rotation": 27.57, "x": 129.65, "y": -25.03}, {"name": "leg_R3", "parent": "leg_R2", "length": 639.8, "rotation": -28.75, "x": 604.85, "inherit": "noScale"}, {"name": "leg_L2", "parent": "leg_L", "length": 1193.54, "rotation": -7.4, "x": 120.97, "y": 3.04}, {"name": "leg_L3", "parent": "root", "x": -65.17, "y": -134.24, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_RR", "parent": "leg_R", "length": 660.81, "rotation": 38.11, "x": 130.17, "y": -27.69}, {"name": "leg_RR2", "parent": "leg_RR", "length": 676.23, "rotation": -49.58, "x": 660.81, "color": "abe323ff"}, {"name": "leg_R4", "parent": "root", "x": 19.51, "y": -142.22, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "root", "x": 94.25, "y": 493.19, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "body2", "rotation": -103.75, "x": 394.57, "y": 200.88, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_L2", "parent": "body2", "rotation": -103.75, "x": 301.08, "y": -168.17, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_RR", "parent": "sh_R", "length": 417.76, "rotation": 58.85, "x": 207.32, "y": -0.01}, {"name": "arm_RR2", "parent": "arm_RR", "length": 404.59, "rotation": 68.41, "x": 417.76, "color": "abe323ff"}, {"name": "arm_R3", "parent": "leg_R", "rotation": 95.53, "x": 83.18, "y": 18.31, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "root", "x": -260.46, "y": 1418.18, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L2", "parent": "sh_L2", "x": -61.75, "y": -335.99, "color": "ff3f00ff", "icon": "ik"}, {"name": "headround3", "parent": "head", "x": 712.91, "y": -32.73, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -90.44, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 634.14, "y": -123.17, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 131.21, "y": -529.03, "color": "abe323ff", "icon": "arrows"}, {"name": "bodyround2", "parent": "body2", "x": 41.35, "y": -529.03, "icon": "warning"}, {"name": "tunround", "parent": "All2", "x": 496.75, "y": -145.07, "color": "abe323ff", "icon": "arrows"}, {"name": "tunround2", "parent": "All2", "x": 496.75, "y": -241.1, "icon": "warning"}, {"name": "body3", "parent": "body2", "rotation": -9.22, "x": 179.29, "y": 24.03}, {"name": "body4", "parent": "body3", "x": -17.36, "y": -5}, {"name": "body5", "parent": "body4", "x": -20.7, "y": -6.2}, {"name": "body6", "parent": "body2", "rotation": -8.77, "x": 147.74, "y": -160.68}, {"name": "body7", "parent": "body6", "x": -23.13, "y": -35.96}, {"name": "body8", "parent": "body7", "x": -20.05, "y": -36.83}], "slots": [{"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "arm_R", "bone": "root", "attachment": "arm_R"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair", "bone": "root", "attachment": "hair"}, {"name": "hat", "bone": "root", "attachment": "hat"}, {"name": "arm_L2", "bone": "root", "attachment": "arm_L"}], "ik": [{"name": "arm_L", "order": 2, "bones": ["arm_L"], "target": "arm_L2", "compress": true, "stretch": true}, {"name": "arm_R", "order": 6, "bones": ["arm_RR", "arm_RR2"], "target": "arm_R3"}, {"name": "arm_R1", "order": 8, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 9, "bones": ["arm_R2"], "target": "arm_R3", "compress": true, "stretch": true}, {"name": "leg_L", "order": 10, "bones": ["leg_L2"], "target": "leg_L3", "compress": true, "stretch": true}, {"name": "leg_R", "order": 11, "bones": ["leg_RR", "leg_RR2"], "target": "leg_R4", "bendPositive": false}, {"name": "leg_R1", "order": 13, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 14, "bones": ["leg_R3"], "target": "leg_R4", "compress": true, "stretch": true}, {"name": "sh_L", "order": 15, "bones": ["sh_L"], "target": "sh_L2", "compress": true, "stretch": true}, {"name": "sh_R", "order": 5, "bones": ["sh_R"], "target": "sh_R2", "compress": true, "stretch": true}], "transform": [{"name": "arm_R3", "order": 7, "bones": ["arm_R1"], "target": "arm_RR2", "rotation": 45.33, "x": 52.11, "y": 101.81, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 16, "bones": ["bodyround2"], "target": "bodyround", "x": -89.87, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 4, "bones": ["sh_R2"], "target": "bodyround", "rotation": -103.75, "x": 263.36, "y": 729.91, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "bones": ["sh_L2"], "target": "bodyround", "rotation": -103.75, "x": 169.86, "y": 360.86, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 17, "bones": ["headround2"], "target": "headround", "x": -78.77, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 18, "bones": ["eyebrow_R3"], "target": "headround", "rotation": -67.1, "x": -595.25, "y": 172.25, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 19, "bones": ["eyebrow_L3"], "target": "headround", "rotation": 45.43, "x": -602.2, "y": 14.9, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_R3", "order": 12, "bones": ["leg_R1"], "target": "leg_RR2", "rotation": 107.9, "x": 48.62, "y": -124.22, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 20, "bones": ["tunround2"], "target": "tunround", "y": -96.03, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 3, "bones": ["leg_R"], "target": "tunround", "rotation": -95.53, "x": -678.85, "y": 43.07, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 1, "bones": ["leg_L"], "target": "tunround", "rotation": -95.53, "x": -373.02, "y": 12.32, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.25246, 0, 0.32224, 0.04467, 0.40173, 0.09284, 0.47774, 0.14001, 0.53364, 0.19179, 0.56661, 0.25239, 0.6607, 0.36436, 0.7026, 0.40623, 0.75082, 0.44457, 0.8005, 0.4797, 0.85224, 0.5116, 0.8644, 0.55567, 0.87899, 0.61357, 0.90544, 0.66372, 0.94005, 0.71387, 0.97802, 0.75934, 0.95717, 0.7913, 1, 0.87256, 0.83202, 0.94304, 0.73883, 0.96455, 0.67227, 0.99829, 0.62433, 0.99708, 0.59511, 0.92348, 0.55488, 0.8913, 0.56383, 0.81988, 0.57027, 0.76844, 0.56198, 0.72246, 0.54718, 0.66655, 0.6004, 0.60408, 0.62262, 0.57421, 0.62005, 0.56027, 0.58815, 0.53121, 0.54207, 0.50206, 0.47895, 0.46829, 0.36261, 0.40314, 0.23482, 0.32451, 0.15817, 0.27504, 0.10088, 0.22223, 0.04251, 0.16591, 0, 0.12366], "triangles": [19, 20, 22, 20, 21, 22, 19, 22, 18, 17, 18, 16, 23, 24, 22, 18, 22, 24, 16, 18, 24, 24, 25, 16, 16, 25, 14, 13, 14, 26, 13, 26, 12, 28, 12, 27, 28, 29, 12, 16, 14, 15, 25, 26, 14, 12, 26, 27, 29, 11, 12, 11, 30, 10, 30, 11, 29, 10, 30, 9, 30, 31, 9, 31, 8, 9, 31, 32, 8, 32, 7, 8, 32, 33, 7, 33, 6, 7, 33, 34, 6, 34, 5, 6, 34, 35, 5, 5, 35, 4, 35, 36, 4, 36, 3, 4, 3, 37, 2, 3, 36, 37, 2, 38, 1, 2, 37, 38, 38, 39, 1, 39, 0, 1], "vertices": [1, 24, 280.98, -39.12, 1, 1, 24, 253.17, -42.14, 1, 1, 24, 222.57, -46.31, 1, 1, 24, 192.86, -50, 1, 1, 24, 164.25, -48.13, 1, 1, 24, 135.47, -38.99, 1, 1, 24, 77.48, -29.21, 1, 1, 24, 54.82, -27, 1, 2, 21, -43.92, -6.09, 0.00484, 24, 32.64, -27.09, 0.99516, 3, 21, -30.23, 10.04, 0.0788, 24, 11.53, -28.35, 0.86626, 22, -27.74, 18.39, 0.05494, 3, 21, -18.16, 26.36, 0.1736, 24, -8.61, -30.91, 0.40254, 22, -11.24, 30.22, 0.42386, 3, 21, 1.77, 33.89, 0.10427, 24, -27.83, -21.73, 0.05196, 22, 10.03, 31.27, 0.84377, 2, 21, 28.02, 43.43, 0.01157, 22, 37.95, 32.29, 0.98843, 3, 21, 50, 55.19, 0.0001, 22, 62.48, 36.73, 0.99636, 23, -54.33, 21.06, 0.00354, 2, 22, 87.22, 43.28, 0.86203, 23, -32.79, 34.89, 0.13797, 2, 22, 109.82, 50.91, 0.56631, 23, -13.63, 49.1, 0.43369, 2, 22, 124.49, 44.03, 0.3148, 23, 2.45, 47.05, 0.6852, 2, 22, 164.24, 51.22, 0.00849, 23, 38.07, 66.1, 0.99151, 1, 23, 80.19, 30.69, 1, 1, 23, 95.34, 9.28, 1, 1, 23, 114.75, -4.17, 1, 1, 23, 116.8, -16.42, 1, 1, 23, 84.02, -31.26, 1, 2, 22, 161.74, -64.39, 0.0034, 23, 71.19, -44.69, 0.9966, 2, 22, 127.99, -58.7, 0.12823, 23, 37.33, -49.64, 0.87177, 2, 22, 103.69, -54.61, 0.45982, 23, 12.94, -53.21, 0.54018, 2, 22, 81.61, -54.57, 0.79155, 23, -8.09, -59.95, 0.20845, 2, 22, 54.64, -55.74, 0.96317, 23, -33.4, -69.34, 0.03683, 3, 24, -8.72, 47.88, 0.01728, 22, 26.29, -39.06, 0.98262, 23, -65.49, -62.18, 0.0001, 3, 21, 23.67, -25.45, 0.00052, 24, -0.11, 35.12, 0.16942, 22, 12.65, -31.92, 0.83005, 3, 21, 17.29, -27.5, 0.00225, 24, 5.78, 31.94, 0.36065, 22, 5.95, -31.93, 0.6371, 3, 21, 5.46, -38.51, 0.00077, 24, 21.92, 31.01, 0.78588, 22, -8.69, -38.77, 0.21335, 2, 24, 40.15, 33.1, 0.9681, 22, -23.74, -49.27, 0.0319, 2, 24, 62.68, 37.6, 0.99992, 22, -41.42, -63.94, 8e-05, 1, 24, 105.35, 45.13, 1, 1, 24, 155.02, 51.51, 1, 1, 24, 185.73, 54.72, 1, 1, 24, 214.95, 52.88, 1, 1, 24, 245.72, 50.34, 1, 1, 24, 268.62, 48.15, 1], "hull": 40, "edges": [6, 8, 10, 12, 12, 14, 18, 20, 28, 30, 30, 32, 36, 38, 42, 44, 58, 60, 60, 62, 66, 68, 76, 78, 72, 74, 74, 76, 4, 6, 0, 2, 2, 4, 68, 70, 70, 72, 78, 0, 8, 10, 14, 16, 16, 18, 20, 22, 22, 24, 62, 64, 64, 66, 54, 56, 56, 58, 52, 54, 50, 52, 46, 48, 48, 50, 44, 46, 40, 42, 38, 40, 34, 36, 32, 34, 24, 26, 26, 28], "width": 259, "height": 478}}, "arm_L2": {"arm_L": {"type": "mesh", "uvs": [0.8644, 0.55567, 0.87899, 0.61357, 0.90544, 0.66372, 0.94005, 0.71387, 0.97802, 0.75934, 0.95717, 0.7913, 1, 0.87256, 0.83202, 0.94304, 0.73883, 0.96455, 0.67227, 0.99829, 0.62433, 0.99708, 0.59511, 0.92348, 0.55488, 0.8913, 0.56383, 0.81988, 0.57027, 0.76844, 0.56198, 0.72246, 0.54718, 0.66655, 0.6004, 0.60408, 0.62262, 0.57421, 0.62005, 0.56027], "triangles": [8, 9, 11, 9, 10, 11, 8, 11, 7, 6, 7, 5, 12, 13, 11, 7, 11, 13, 5, 7, 13, 13, 14, 5, 5, 14, 3, 2, 3, 15, 2, 15, 1, 17, 1, 16, 17, 18, 1, 5, 3, 4, 14, 15, 3, 1, 15, 16, 18, 0, 1, 18, 19, 0], "vertices": [3, 21, 1.77, 33.89, 0.10427, 24, -27.83, -21.73, 0.05196, 22, 10.03, 31.27, 0.84377, 2, 21, 28.02, 43.43, 0.01157, 22, 37.95, 32.29, 0.98843, 3, 21, 50, 55.19, 0.0001, 22, 62.48, 36.73, 0.99636, 23, -54.33, 21.06, 0.00354, 2, 22, 87.22, 43.28, 0.86203, 23, -32.79, 34.89, 0.13797, 2, 22, 109.82, 50.91, 0.56631, 23, -13.63, 49.1, 0.43369, 2, 22, 124.49, 44.03, 0.3148, 23, 2.45, 47.05, 0.6852, 2, 22, 164.24, 51.22, 0.00849, 23, 38.07, 66.1, 0.99151, 1, 23, 80.19, 30.69, 1, 1, 23, 95.34, 9.28, 1, 1, 23, 114.75, -4.17, 1, 1, 23, 116.8, -16.42, 1, 1, 23, 84.02, -31.26, 1, 2, 22, 161.74, -64.39, 0.0034, 23, 71.19, -44.69, 0.9966, 2, 22, 127.99, -58.7, 0.12823, 23, 37.33, -49.64, 0.87177, 2, 22, 103.69, -54.61, 0.45982, 23, 12.94, -53.21, 0.54018, 2, 22, 81.61, -54.57, 0.79155, 23, -8.09, -59.95, 0.20845, 2, 22, 54.64, -55.74, 0.96317, 23, -33.4, -69.34, 0.03683, 3, 24, -8.72, 47.88, 0.01728, 22, 26.29, -39.06, 0.98262, 23, -65.49, -62.18, 0.0001, 3, 21, 23.67, -25.45, 0.00052, 24, -0.11, 35.12, 0.16942, 22, 12.65, -31.92, 0.83005, 3, 21, 17.29, -27.5, 0.00225, 24, 5.78, 31.94, 0.36065, 22, 5.95, -31.93, 0.6371], "hull": 20, "edges": [6, 8, 8, 10, 14, 16, 20, 22, 36, 38, 0, 2, 32, 34, 34, 36, 30, 32, 28, 30, 24, 26, 26, 28, 22, 24, 18, 20, 16, 18, 12, 14, 10, 12, 2, 4, 4, 6, 38, 0], "width": 259, "height": 478}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.59138, 0.03706, 0.69944, 0.37106, 0.74984, 0.45493, 1, 0.81621, 1, 0.81981, 0.69449, 1, 0.17523, 0.43632, 0.05453, 0.27958, 0.01915, 0.21004, 1e-05, 0.16333, 0.56473, 0], "triangles": [8, 9, 10, 0, 7, 8, 0, 8, 10, 6, 7, 0, 1, 6, 0, 5, 2, 3, 2, 5, 6, 2, 6, 1, 4, 5, 3], "vertices": [1, 17, 85.73, 44.34, 1, 1, 17, 164.71, 23.87, 1, 1, 17, 186.58, 22.47, 1, 1, 17, 283.67, 21.75, 1, 1, 17, 284.42, 21.34, 1, 1, 17, 295.21, -48.18, 1, 1, 17, 132.44, -67.79, 1, 1, 17, 89.25, -69.43, 1, 1, 17, 71.68, -67.24, 1, 1, 17, 60.28, -65.02, 1, 1, 17, 75.69, 44.25, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 8, 10, 4, 6], "width": 183, "height": 237}}, "body": {"body": {"type": "mesh", "uvs": [0.32439, 0.0131, 0.33881, 8e-05, 0.42234, 0.00026, 0.60158, 0.00064, 0.65875, 0.00076, 0.67185, 0.01392, 0.68495, 0.02708, 0.69018, 0.03998, 0.70246, 0.05116, 0.72822, 0.06257, 0.74268, 0.07441, 0.73925, 0.08582, 0.71692, 0.09615, 0.73117, 0.10591, 0.73107, 0.11699, 0.76061, 0.12176, 0.77588, 0.12935, 0.77535, 0.138, 0.74468, 0.14535, 0.74385, 0.15382, 0.79159, 0.16382, 0.80686, 0.1788, 0.81101, 0.1898, 0.9142, 0.21544, 0.96432, 0.22832, 1, 0.24238, 0.99918, 0.26675, 0.96818, 0.28448, 0.90566, 0.29973, 0.86419, 0.30251, 0.86233, 0.31628, 0.84635, 0.33559, 0.82818, 0.35329, 0.82473, 0.36569, 0.84178, 0.38715, 0.84355, 0.40493, 0.87276, 0.42118, 0.89566, 0.44074, 0.90724, 0.46692, 0.875, 0.47629, 0.79157, 0.49441, 0.67293, 0.51708, 0.59036, 0.53286, 0.56034, 0.53612, 0.58992, 0.5552, 0.6026, 0.56838, 0.64001, 0.61415, 0.66855, 0.66372, 0.68282, 0.6885, 0.69709, 0.71329, 0.69883, 0.7305, 0.68934, 0.7548, 0.67533, 0.77225, 0.66132, 0.78711, 0.65309, 0.81549, 0.64425, 0.86285, 0.6119, 0.9261, 0.59376, 0.96803, 0.5932, 0.9839, 0.59503, 0.99867, 0.45303, 1, 0.45723, 0.98436, 0.45539, 0.96843, 0.42077, 0.91467, 0.40148, 0.8685, 0.39789, 0.83836, 0.41527, 0.81202, 0.45957, 0.77232, 0.46555, 0.76198, 0.45612, 0.7496, 0.43819, 0.73891, 0.40818, 0.72186, 0.37741, 0.70591, 0.29947, 0.6698, 0.20603, 0.62199, 0.12906, 0.57748, 0.09073, 0.53456, 0.08617, 0.50465, 0.10551, 0.46876, 0.14889, 0.44195, 0.18493, 0.42529, 0.23484, 0.407, 0.28987, 0.39289, 0.32842, 0.38386, 0.35588, 0.36533, 0.36205, 0.35431, 0.38767, 0.34838, 0.38484, 0.34155, 0.35751, 0.32539, 0.3286, 0.31115, 0.29912, 0.29574, 0.2738, 0.28124, 0.25612, 0.27098, 0.24197, 0.28018, 0.22782, 0.29085, 0.2142, 0.30567, 0.19475, 0.31713, 0.20516, 0.32706, 0.20449, 0.33633, 0.21429, 0.34466, 0.22588, 0.35173, 0.22115, 0.35751, 0.18924, 0.36971, 0.14261, 0.37698, 0.10396, 0.37679, 0.07172, 0.37501, 0.04559, 0.36902, 0.03714, 0.35744, 0.0283, 0.34803, 0.01101, 0.33834, 1e-05, 0.32601, 0.02694, 0.32433, 0.02916, 0.31414, 0.03617, 0.30135, 0.04853, 0.28552, 0.08073, 0.22119, 0.03655, 0.21026, 0.03719, 0.20055, 0.07319, 0.19379, 0.0552, 0.18437, 0.05383, 0.16803, 0.04985, 0.15548, 0.0597, 0.14757, 0.12039, 0.13992, 0.10101, 0.12923, 0.11327, 0.11806, 0.15436, 0.11227, 0.16478, 0.10228, 0.20547, 0.09135, 0.21306, 0.07865, 0.24147, 0.06558, 0.26459, 0.05832, 0.28762, 0.04383, 0.30845, 0.02728, 0.10454, 0.18073, 0.12287, 0.1671, 0.14401, 0.15772, 0.19741, 0.14948, 0.26625, 0.14604, 0.34283, 0.14153, 0.38006, 0.12929, 0.39762, 0.1164, 0.39627, 0.10474, 0.39135, 0.08949, 0.38292, 0.07295, 0.57535, 0.07274, 0.56622, 0.09143, 0.5613, 0.10711, 0.55709, 0.12364, 0.57676, 0.12816, 0.60275, 0.14448, 0.67255, 0.15094, 0.09118, 0.19597, 0.2964, 0.23917, 0.27597, 0.25541, 0.33478, 0.24282, 0.34466, 0.2701, 0.4016, 0.29229, 0.47393, 0.30521, 0.63086, 0.3008, 0.68626, 0.28943, 0.78077, 0.29218, 0.81672, 0.30013, 0.56623, 0.30701, 0.35991, 0.21535, 0.42253, 0.19596, 0.49873, 0.18871, 0.57738, 0.1909, 0.6421, 0.19007, 0.75689, 0.18398, 0.69904, 0.18453, 0.51677, 0.24988, 0.58668, 0.2469, 0.46163, 0.25992, 0.46456, 0.27551, 0.51996, 0.28263, 0.65562, 0.24725, 0.59446, 0.28159, 0.66075, 0.274, 0.72058, 0.26803, 0.80627, 0.26776, 0.88182, 0.27409, 0.72742, 0.25114, 0.80605, 0.25043, 0.94714, 0.27336, 0.87752, 0.24459, 0.95182, 0.24365, 0.45732, 0.21918, 0.54765, 0.21284, 0.65425, 0.2172, 0.74768, 0.21561, 0.82753, 0.21124, 0.39559, 0.243, 0.39915, 0.26876, 0.43234, 0.28696, 0.49236, 0.29586, 0.57557, 0.29586, 0.70454, 0.27996, 0.64322, 0.28979, 0.79169, 0.28109, 0.85306, 0.28904, 0.91443, 0.28967, 0.21713, 0.17271, 0.30879, 0.16362, 0.4136, 0.15872, 0.48595, 0.17475, 0.53862, 0.13653, 0.51112, 0.156, 0.59694, 0.1525, 0.63857, 0.16849, 0.56859, 0.1717, 0.69595, 0.16956, 0.75115, 0.17051, 0.40733, 0.17698, 0.32663, 0.18573, 0.25601, 0.19296, 0.29006, 0.21617, 0.18154, 0.18896, 0.1705, 0.20697, 0.10073, 0.28989, 0.08697, 0.30676, 0.08045, 0.32185, 0.07673, 0.33524, 0.08511, 0.34805, 0.09908, 0.36399, 0.168, 0.35745, 0.15496, 0.34549, 0.14751, 0.33267, 0.14937, 0.321, 0.15496, 0.30989, 0.16241, 0.29389, 0.22836, 0.2107, 0.21477, 0.23743, 0.15401, 0.23117, 0.19617, 0.25814, 0.12474, 0.24956, 0.4391, 0.1443, 0.4681, 0.12854, 0.46327, 0.10908, 0.45682, 0.09011, 0.44876, 0.07287, 0.52691, 0.07263, 0.52449, 0.09085, 0.52449, 0.1076, 0.53013, 0.12484, 0.72895, 0.13168, 0.64843, 0.11133, 0.6609, 0.1314, 0.64472, 0.09331, 0.65119, 0.0755, 0.58861, 0.05665, 0.59751, 0.03835, 0.60155, 0.01955, 0.64281, 0.05419, 0.38833, 0.05436, 0.40208, 0.03531, 0.4134, 0.01824, 0.18015, 0.13359, 0.23517, 0.12518, 0.28209, 0.11182, 0.3104, 0.09649, 0.32092, 0.08066, 0.34357, 0.06693, 0.56471, 0.32769, 0.57106, 0.35075, 0.57231, 0.37564, 0.5704, 0.39482, 0.70269, 0.39868, 0.77974, 0.39566, 0.43596, 0.38207, 0.43497, 0.36636, 0.45473, 0.35217, 0.44584, 0.33253, 0.42608, 0.3132, 0.72184, 0.30946, 0.71591, 0.32728, 0.71393, 0.35054, 0.70899, 0.3747, 0.80087, 0.33121, 0.78605, 0.35295, 0.78407, 0.375, 0.8126, 0.31376, 0.40711, 0.40622, 0.54797, 0.4216, 0.7014, 0.42545, 0.79698, 0.41814, 0.2624, 0.55656, 0.23523, 0.52843, 0.22377, 0.50049, 0.22139, 0.47598, 0.23378, 0.45205, 0.27104, 0.42712, 0.31922, 0.41133, 0.52465, 0.44341, 0.49479, 0.46752, 0.4903, 0.49443, 0.52651, 0.51661, 0.61753, 0.52149, 0.64897, 0.49919, 0.67538, 0.47458, 0.69173, 0.44959, 0.81146, 0.46986, 0.80895, 0.44294, 0.46825, 0.54949, 0.4305, 0.52862, 0.38987, 0.50479, 0.3634, 0.48305, 0.36427, 0.46137, 0.38742, 0.44114, 0.41388, 0.42648, 0.48815, 0.69709, 0.52242, 0.71564, 0.53881, 0.73546, 0.54061, 0.76867, 0.53234, 0.78355, 0.61308, 0.78856, 0.64421, 0.75437, 0.65174, 0.73133, 0.62769, 0.69151, 0.64182, 0.70981, 0.62978, 0.77131, 0.54306, 0.75106, 0.34776, 0.62469, 0.55091, 0.61167, 0.51064, 0.81275, 0.60756, 0.81437, 0.48541, 0.85903, 0.52657, 0.96742, 0.5903, 0.86025], "triangles": [102, 103, 225, 105, 224, 104, 225, 103, 224, 103, 104, 224, 105, 106, 224, 102, 225, 101, 106, 107, 224, 107, 223, 224, 224, 226, 225, 224, 223, 226, 101, 225, 100, 225, 99, 100, 225, 226, 99, 107, 108, 223, 108, 222, 223, 223, 227, 226, 223, 222, 227, 108, 109, 222, 226, 98, 99, 226, 227, 98, 109, 111, 222, 109, 110, 111, 98, 227, 97, 222, 221, 227, 222, 111, 221, 170, 211, 212, 169, 212, 21, 212, 20, 21, 211, 19, 212, 212, 19, 20, 209, 151, 211, 211, 151, 19, 19, 151, 18, 150, 247, 151, 151, 245, 18, 151, 247, 245, 18, 245, 17, 17, 245, 16, 245, 15, 16, 245, 14, 15, 245, 247, 14, 214, 215, 203, 215, 202, 203, 214, 203, 213, 213, 203, 204, 136, 137, 202, 202, 138, 203, 202, 137, 138, 203, 139, 204, 203, 138, 139, 136, 123, 137, 123, 257, 137, 138, 137, 258, 137, 257, 258, 139, 138, 259, 138, 258, 259, 139, 259, 140, 123, 124, 257, 124, 125, 257, 125, 126, 257, 257, 126, 258, 126, 127, 258, 127, 128, 258, 258, 128, 259, 59, 61, 58, 58, 61, 327, 61, 59, 60, 61, 62, 327, 58, 327, 57, 62, 63, 327, 57, 327, 56, 328, 56, 327, 328, 327, 326, 63, 326, 327, 56, 328, 55, 63, 64, 326, 64, 65, 326, 55, 328, 54, 328, 325, 54, 326, 324, 328, 328, 324, 325, 326, 65, 324, 324, 65, 66, 325, 315, 54, 54, 315, 53, 324, 314, 325, 325, 314, 315, 314, 324, 67, 324, 66, 67, 314, 313, 315, 315, 320, 53, 315, 313, 320, 53, 320, 52, 314, 67, 313, 67, 68, 313, 320, 316, 52, 52, 316, 51, 316, 320, 321, 320, 313, 321, 313, 68, 321, 68, 69, 321, 316, 317, 51, 316, 321, 312, 51, 317, 50, 317, 316, 312, 69, 312, 321, 69, 70, 312, 70, 311, 312, 70, 71, 311, 317, 312, 319, 312, 311, 319, 317, 49, 50, 317, 319, 49, 71, 310, 311, 71, 72, 310, 311, 318, 319, 311, 310, 318, 319, 48, 49, 319, 318, 48, 72, 73, 310, 73, 322, 310, 310, 323, 318, 310, 322, 323, 318, 47, 48, 47, 323, 46, 47, 318, 323, 73, 74, 322, 74, 286, 322, 322, 303, 323, 303, 286, 304, 303, 322, 286, 74, 75, 286, 323, 45, 46, 45, 303, 44, 45, 323, 303, 286, 76, 287, 286, 75, 76, 304, 287, 305, 304, 286, 287, 303, 43, 44, 303, 304, 296, 287, 77, 288, 287, 76, 77, 304, 305, 295, 287, 288, 305, 288, 306, 305, 305, 306, 295, 288, 78, 289, 288, 77, 78, 288, 289, 306, 306, 307, 294, 306, 289, 307, 78, 79, 289, 289, 290, 307, 289, 79, 290, 40, 301, 39, 39, 301, 38, 300, 302, 301, 38, 302, 37, 38, 301, 302, 284, 285, 302, 302, 36, 37, 302, 285, 36, 285, 35, 36, 285, 268, 35, 307, 308, 294, 293, 308, 309, 290, 291, 307, 307, 291, 308, 79, 80, 290, 290, 80, 291, 291, 292, 308, 308, 292, 309, 80, 81, 291, 291, 81, 292, 292, 282, 309, 81, 82, 292, 292, 83, 282, 292, 82, 83, 282, 83, 269, 303, 296, 43, 43, 296, 42, 42, 297, 41, 42, 296, 297, 304, 295, 296, 41, 297, 298, 297, 296, 298, 41, 298, 40, 296, 295, 298, 298, 299, 40, 299, 298, 294, 298, 295, 294, 295, 306, 294, 40, 299, 301, 294, 293, 299, 299, 300, 301, 299, 293, 300, 294, 308, 293, 293, 283, 300, 300, 284, 302, 300, 283, 284, 293, 309, 283, 309, 282, 283, 285, 267, 268, 285, 284, 267, 283, 266, 284, 284, 266, 267, 283, 282, 266, 266, 282, 269, 97, 227, 228, 97, 228, 96, 227, 221, 228, 111, 112, 221, 221, 220, 228, 221, 112, 220, 228, 229, 96, 228, 220, 229, 96, 229, 95, 112, 113, 220, 220, 219, 229, 229, 230, 95, 229, 219, 230, 220, 113, 219, 95, 230, 94, 113, 114, 219, 93, 94, 230, 234, 93, 230, 234, 230, 219, 219, 235, 234, 219, 114, 235, 114, 115, 235, 93, 234, 92, 92, 234, 154, 235, 233, 234, 234, 232, 154, 234, 233, 232, 154, 232, 153, 235, 115, 233, 232, 216, 153, 232, 231, 216, 232, 233, 231, 233, 218, 231, 233, 115, 218, 115, 116, 152, 152, 116, 117, 115, 152, 218, 152, 117, 118, 231, 215, 216, 216, 215, 214, 218, 217, 231, 231, 217, 215, 218, 152, 217, 152, 134, 217, 152, 118, 134, 118, 119, 134, 217, 202, 215, 134, 135, 217, 217, 135, 202, 202, 135, 136, 119, 120, 134, 134, 120, 135, 120, 121, 135, 136, 135, 122, 135, 121, 122, 122, 123, 136, 261, 144, 143, 261, 262, 144, 246, 248, 12, 260, 129, 261, 261, 129, 130, 11, 12, 249, 146, 145, 248, 12, 248, 249, 248, 145, 249, 242, 241, 146, 146, 241, 145, 143, 240, 239, 239, 240, 241, 143, 144, 240, 11, 249, 10, 130, 131, 261, 261, 131, 262, 145, 250, 249, 250, 253, 249, 249, 9, 10, 249, 8, 9, 249, 253, 8, 144, 254, 240, 144, 262, 254, 241, 240, 255, 145, 241, 250, 250, 241, 255, 131, 132, 262, 262, 132, 254, 255, 240, 254, 250, 251, 253, 250, 255, 251, 254, 132, 255, 255, 132, 133, 253, 251, 7, 253, 7, 8, 6, 251, 252, 6, 252, 5, 7, 251, 6, 255, 256, 251, 251, 256, 252, 133, 0, 255, 255, 0, 256, 256, 2, 252, 252, 4, 5, 252, 3, 4, 252, 2, 3, 0, 1, 256, 256, 1, 2, 206, 237, 244, 236, 140, 237, 206, 148, 149, 206, 244, 148, 149, 246, 247, 247, 246, 14, 140, 259, 141, 140, 141, 237, 141, 260, 142, 142, 260, 143, 259, 260, 141, 141, 238, 237, 244, 238, 243, 244, 237, 238, 148, 147, 149, 149, 147, 246, 248, 147, 146, 147, 248, 246, 260, 261, 143, 244, 243, 148, 148, 243, 147, 14, 246, 13, 141, 142, 238, 259, 128, 260, 246, 12, 13, 142, 239, 238, 243, 238, 242, 243, 242, 147, 242, 238, 239, 147, 242, 146, 142, 143, 239, 128, 129, 260, 242, 239, 241, 159, 263, 163, 159, 275, 263, 87, 88, 272, 30, 278, 281, 88, 273, 272, 272, 158, 263, 272, 273, 158, 281, 278, 274, 263, 158, 163, 278, 275, 274, 275, 159, 274, 88, 89, 273, 30, 281, 29, 274, 161, 281, 281, 162, 29, 281, 161, 162, 89, 157, 273, 273, 157, 158, 89, 90, 157, 159, 160, 274, 274, 160, 161, 158, 195, 163, 163, 196, 159, 163, 195, 196, 157, 194, 158, 158, 194, 195, 162, 200, 29, 29, 200, 28, 159, 198, 160, 159, 196, 198, 200, 162, 199, 28, 201, 27, 28, 200, 201, 195, 175, 196, 196, 177, 198, 196, 175, 177, 194, 174, 195, 195, 174, 175, 90, 156, 157, 90, 91, 156, 156, 193, 157, 157, 193, 194, 160, 197, 161, 162, 161, 199, 161, 197, 199, 160, 198, 197, 198, 177, 178, 200, 181, 201, 201, 184, 27, 201, 181, 184, 197, 198, 178, 199, 180, 200, 200, 180, 181, 194, 193, 174, 27, 184, 26, 177, 175, 171, 171, 175, 173, 171, 172, 177, 178, 172, 176, 178, 177, 172, 156, 92, 154, 156, 91, 92, 197, 179, 199, 199, 179, 180, 197, 178, 179, 193, 173, 174, 175, 174, 173, 184, 181, 185, 178, 176, 179, 181, 180, 185, 184, 186, 26, 184, 185, 186, 154, 155, 156, 154, 153, 155, 156, 192, 193, 156, 155, 192, 193, 192, 173, 179, 182, 180, 179, 176, 182, 182, 183, 180, 180, 183, 185, 26, 186, 25, 173, 192, 171, 183, 182, 190, 190, 182, 189, 183, 191, 185, 183, 190, 191, 192, 187, 171, 171, 188, 172, 171, 187, 188, 172, 189, 176, 182, 176, 189, 172, 188, 189, 186, 185, 24, 185, 191, 23, 186, 24, 25, 24, 185, 23, 155, 164, 192, 192, 164, 187, 155, 153, 164, 153, 216, 164, 164, 165, 187, 187, 166, 188, 187, 165, 166, 188, 167, 189, 167, 168, 189, 189, 170, 190, 189, 168, 170, 216, 214, 164, 190, 169, 191, 190, 170, 169, 164, 214, 165, 188, 166, 167, 169, 22, 191, 214, 213, 165, 165, 205, 166, 165, 213, 205, 166, 210, 167, 167, 210, 168, 210, 209, 168, 170, 209, 211, 170, 168, 209, 169, 21, 22, 23, 191, 22, 166, 205, 210, 170, 212, 169, 213, 204, 205, 205, 204, 207, 205, 207, 210, 207, 204, 236, 210, 208, 209, 210, 207, 208, 209, 208, 151, 204, 139, 236, 236, 139, 140, 236, 237, 207, 207, 206, 208, 206, 207, 237, 208, 150, 151, 208, 206, 150, 206, 149, 150, 150, 149, 247, 269, 84, 270, 84, 269, 83, 268, 34, 35, 266, 265, 267, 267, 277, 268, 267, 265, 277, 268, 280, 34, 268, 277, 280, 266, 269, 265, 280, 33, 34, 84, 85, 270, 85, 86, 270, 269, 270, 265, 270, 271, 265, 271, 264, 265, 265, 264, 277, 280, 279, 33, 280, 277, 279, 277, 276, 279, 277, 264, 276, 270, 86, 271, 33, 279, 32, 31, 32, 278, 32, 279, 278, 279, 276, 278, 271, 87, 272, 264, 271, 263, 271, 272, 263, 275, 264, 263, 276, 264, 275, 276, 275, 278, 271, 86, 87, 31, 278, 30], "vertices": [2, 5, 277.11, 106.17, 0.01055, 6, 163.77, 100.74, 0.98945, 2, 5, 305.91, 96.42, 0.00151, 6, 192.22, 90.03, 0.99849, 1, 6, 189.94, 33.62, 1, 2, 6, 185.03, -87.46, 0.98206, 42, -527.88, 35.71, 0.01794, 3, 5, 304.39, -119.86, 0.00273, 6, 183.46, -126.07, 0.98604, 42, -529.45, -2.9, 0.01124, 3, 5, 275.3, -128.72, 0.01237, 6, 154.09, -133.95, 0.97955, 42, -558.82, -10.78, 0.00808, 3, 5, 246.21, -137.57, 0.03992, 6, 124.72, -141.83, 0.95525, 42, -588.19, -18.66, 0.00483, 3, 5, 217.69, -141.11, 0.09146, 6, 96.1, -144.41, 0.90519, 42, -616.81, -21.24, 0.00335, 2, 5, 192.96, -149.41, 0.16734, 6, 71.1, -151.88, 0.83266, 2, 5, 167.74, -166.82, 0.25944, 6, 45.31, -168.43, 0.74056, 3, 5, 141.57, -176.6, 0.32293, 6, 18.83, -177.33, 0.65348, 18, 45.02, 210.64, 0.02358, 3, 5, 116.33, -174.28, 0.36347, 6, -6.32, -174.17, 0.56441, 18, 48.67, 185.56, 0.07212, 3, 5, 93.5, -159.19, 0.41317, 6, -28.63, -158.32, 0.46889, 18, 39.33, 159.84, 0.11794, 3, 5, 71.92, -168.82, 0.40442, 6, -50.52, -167.22, 0.37427, 18, 53.75, 141.1, 0.22131, 3, 5, 47.43, -168.75, 0.36771, 6, -74.99, -166.33, 0.2779, 18, 59.41, 117.28, 0.35438, 3, 5, 36.88, -188.72, 0.26131, 6, -86.2, -185.94, 0.11366, 18, 81.29, 111.7, 0.62503, 3, 5, 20.08, -199.04, 0.15849, 6, -103.33, -195.69, 0.02578, 18, 95.26, 97.78, 0.81573, 2, 5, 0.95, -198.68, 0.07185, 18, 99.38, 79.1, 0.92815, 2, 5, -15.28, -177.95, 0.00434, 18, 83.02, 58.47, 0.99566, 1, 18, 86.86, 40.12, 1, 1, 18, 123.41, 26.18, 1, 2, 4, 284.48, -192.47, 0.60699, 18, 141.2, -3.62, 0.39301, 3, 4, 260.18, -189.41, 0.69714, 18, 149.61, -26.61, 0.10286, 51, 115.51, -11.24, 0.2, 4, 3, 383.16, -169.74, 0.00138, 4, 188.55, -243.69, 0.79307, 52, 76.13, -39.86, 0.19861, 44, 57.33, 285.34, 0.00694, 5, 3, 357.79, -206.01, 0.00718, 4, 152.82, -269.83, 0.79356, 52, 44.81, -71.14, 0.08897, 53, 64.85, -34.31, 0.09886, 44, 21.61, 259.2, 0.01144, 4, 3, 328.97, -232.79, 0.0138, 4, 116.9, -285.87, 0.77465, 53, 31.79, -55.64, 0.19711, 44, -14.32, 243.16, 0.01443, 4, 3, 275.25, -237.03, 0.03207, 4, 64.69, -272.52, 0.7567, 53, -21.84, -50.42, 0.19719, 44, -66.52, 256.5, 0.01404, 4, 3, 234.34, -219.63, 0.05624, 4, 31.59, -242.85, 0.73487, 52, -79.12, -62.97, 0.19778, 44, -99.62, 286.18, 0.01112, 4, 3, 197.01, -180.53, 0.11272, 4, 8.9, -193.79, 0.68336, 51, -132.17, -53.9, 0.19902, 44, -122.32, 335.24, 0.0049, 3, 3, 188.41, -153.15, 0.19012, 4, 9.59, -165.09, 0.60988, 51, -135.86, -25.43, 0.2, 3, 3, 157.97, -154.6, 0.36908, 4, -19.68, -156.63, 0.53092, 51, -166.08, -21.54, 0.1, 4, 2, 157.61, 118.55, 0.00058, 3, 114.49, -147.63, 0.62339, 4, -58.58, -136, 0.37372, 19, -151.4, 144.53, 0.00231, 4, 2, 145.33, 79.42, 0.00652, 3, 74.42, -138.87, 0.78991, 4, -93.67, -114.76, 0.17903, 19, -111.26, 136.08, 0.02454, 5, 2, 142.99, 51.99, 0.01679, 3, 46.89, -138.98, 0.76481, 4, -119.76, -105.98, 0.073, 19, -83.73, 136.4, 0.06797, 20, -185.73, 1.36, 0.07743, 5, 2, 154.52, 4.54, 0.0283, 3, 0.66, -154.67, 0.51309, 4, -168.59, -105.9, 0.00851, 19, -37.62, 152.45, 0.14493, 20, -139.62, 17.41, 0.30517, 5, 2, 155.71, -34.77, 0.02492, 3, -38.4, -159.35, 0.28017, 4, -207.06, -97.71, 0.00011, 19, 1.39, 157.43, 0.15394, 20, -100.6, 22.39, 0.54086, 4, 2, 175.46, -70.7, 0.01083, 3, -72.43, -182.2, 0.1083, 19, 35.25, 180.55, 0.05723, 20, -66.75, 45.51, 0.82364, 4, 2, 190.94, -113.95, 0.00222, 3, -114.14, -201.47, 0.02592, 20, -25.19, 65.09, 0.97186, 26, 47.7, 423.5, 0, 1, 20, 31.66, 78.46, 1, 4, 19, 156.38, 193.81, 0.05731, 20, 54.38, 58.77, 0.93468, 26, 115.31, 381.07, 5e-05, 46, -319.77, -47.48, 0.00796, 4, 19, 201.69, 141.54, 0.4126, 20, 99.7, 6.5, 0.56121, 26, 131.29, 313.76, 9e-05, 46, -376.17, -87.54, 0.0261, 3, 19, 259.33, 66.55, 0.90113, 20, 157.33, -68.5, 0.08448, 46, -456.37, -137.67, 0.01438, 3, 19, 299.43, 14.36, 0.83473, 26, 159.06, 155.78, 0.16271, 46, -512.19, -172.56, 0.00256, 2, 19, 308.56, -5.15, 0.72054, 26, 158.13, 134.27, 0.27946, 2, 19, 348.63, 18.82, 0.43339, 26, 204.74, 136.97, 0.56661, 2, 19, 376.8, 30.17, 0.27032, 26, 234.96, 133.99, 0.72968, 2, 19, 475.09, 65.1, 0.0387, 26, 338.25, 119.46, 0.9613, 2, 19, 582.31, 94.87, 0.00049, 26, 447.08, 96.23, 0.99951, 1, 26, 501.49, 84.61, 1, 2, 26, 555.91, 72.99, 0.99643, 27, -78.01, 40.46, 0.00357, 2, 26, 591.63, 59.8, 0.86965, 27, -40.35, 46.07, 0.13035, 2, 26, 639.02, 33.7, 0.1742, 27, 13.75, 45.98, 0.8258, 2, 26, 671.24, 10.44, 0.0008, 27, 53.2, 41.08, 0.9992, 1, 27, 86.93, 35.51, 1, 1, 27, 149.89, 37.32, 1, 1, 27, 254.59, 43.61, 1, 1, 27, 396.04, 38.23, 1, 1, 27, 489.53, 36.88, 1, 1, 27, 524.42, 40.6, 1, 1, 27, 556.72, 45.65, 1, 1, 27, 570.85, -49.35, 1, 1, 27, 536.17, -50.57, 1, 1, 27, 501.35, -55.91, 1, 1, 27, 386.03, -93.04, 1, 1, 27, 286.16, -117.92, 1, 1, 27, 220.26, -128.11, 1, 2, 26, 686.79, -185.51, 0.0005, 27, 161.06, -123.24, 0.9995, 2, 26, 616.65, -124.81, 0.14699, 27, 70.37, -103.76, 0.85301, 2, 26, 596.98, -112.49, 0.31356, 27, 47.2, -102.41, 0.68644, 2, 26, 569.22, -108.13, 0.57553, 27, 20.77, -111.94, 0.42447, 2, 26, 542.76, -110.5, 0.76576, 27, -1.3, -126.74, 0.23424, 2, 26, 500.21, -115.16, 0.92697, 27, -36.37, -151.29, 0.07303, 2, 26, 459.72, -121.21, 0.98521, 27, -68.95, -176.07, 0.01479, 1, 26, 365.94, -140.09, 1, 1, 26, 244.26, -158.98, 1, 1, 26, 133.5, -170.29, 1, 2, 25, 234.87, -149.12, 0.00112, 26, 35.83, -158.71, 0.99888, 2, 25, 169.33, -158.57, 0.04133, 26, -26.64, -136.74, 0.95867, 2, 25, 89.09, -153.21, 0.24417, 26, -95.28, -94.86, 0.75583, 2, 25, 27.25, -129.75, 0.5399, 26, -139.24, -45.43, 0.4601, 4, 3, -122.74, 280.13, 0.00434, 4, -144.94, 345.45, 6e-05, 25, -11.76, -109.05, 0.74763, 26, -164.24, -9.03, 0.24797, 6, 3, -79.48, 250.11, 0.03776, 4, -113.69, 303.07, 0.00259, 16, 503.23, 124.66, 0.00013, 19, 45.63, -251.7, 0.00086, 25, -55.26, -79.37, 0.89305, 26, -189.06, 37.41, 0.06561, 6, 3, -45.09, 215.83, 0.15254, 4, -92.23, 259.52, 0.01842, 16, 465.22, 154.87, 0.00171, 19, 10.99, -217.68, 0.04145, 25, -89.9, -45.35, 0.7825, 26, -204.03, 83.6, 0.00338, 4, 3, -22.89, 191.64, 0.27981, 4, -79.02, 229.46, 0.04703, 19, -11.4, -193.67, 0.09624, 25, -112.29, -21.34, 0.57692, 4, 3, 19.55, 176.79, 0.47239, 4, -43.66, 201.7, 0.13416, 19, -53.95, -179.14, 0.10867, 25, -154.85, -6.81, 0.28478, 4, 3, 44.19, 174.79, 0.53373, 4, -20.97, 191.85, 0.2052, 19, -78.62, -177.34, 0.0894, 25, -179.51, -5, 0.17167, 4, 3, 58.8, 158.71, 0.56803, 4, -12.34, 171.91, 0.30563, 19, -93.35, -161.37, 0.06678, 25, -194.24, 10.96, 0.05957, 3, 3, 73.65, 161.95, 0.53543, 4, 2.76, 170.18, 0.42331, 19, -108.17, -164.72, 0.04126, 3, 3, 107.62, 183.53, 0.36369, 4, 41.88, 179.63, 0.62784, 19, -141.97, -186.56, 0.00847, 3, 3, 137.25, 205.78, 0.25015, 4, 77.1, 191.12, 0.74871, 19, -171.43, -209.05, 0.00114, 3, 3, 169.4, 228.66, 0.14696, 4, 114.92, 202.39, 0.72676, 16, 253.52, 118.07, 0.12628, 3, 3, 199.81, 248.55, 0.07563, 4, 150.13, 211.39, 0.5799, 16, 225.52, 94.89, 0.34447, 3, 3, 221.36, 262.48, 0.03319, 4, 175.02, 217.61, 0.37122, 16, 205.67, 78.64, 0.59559, 3, 3, 200.24, 270.19, 0.01794, 4, 157.52, 231.74, 0.21912, 16, 227.53, 73.33, 0.76294, 3, 3, 175.9, 277.63, 0.00962, 4, 136.89, 246.63, 0.12562, 16, 252.54, 68.68, 0.86475, 3, 3, 142.44, 283.89, 0.00415, 4, 107.24, 263.37, 0.0627, 16, 286.5, 66.21, 0.93315, 4, 3, 116.04, 294.73, 4e-05, 4, 85.76, 282.16, 0.00094, 16, 313.95, 58.39, 0.81394, 17, 10.02, 67.89, 0.18508, 3, 4, 62.77, 280.54, 0, 16, 334.05, 69.67, 0.61166, 17, 32.66, 63.58, 0.38834, 2, 16, 354.22, 73.33, 0.33066, 17, 50.44, 53.39, 0.66934, 2, 16, 370.96, 83.5, 0.05508, 17, 69.8, 50.39, 0.94492, 1, 17, 87.27, 49.8, 1, 1, 17, 96.96, 40.89, 1, 1, 17, 110.34, 9.05, 1, 1, 17, 109.39, -26.32, 1, 1, 17, 96.53, -49.07, 1, 1, 17, 82.67, -66.33, 1, 1, 17, 62.59, -75.52, 1, 1, 17, 37.37, -68.3, 1, 2, 16, 403.38, -38.2, 0.04643, 17, 16.24, -63.6, 0.95357, 2, 16, 384.74, -53.93, 0.14636, 17, -8.16, -63.63, 0.85364, 2, 16, 359.5, -66.67, 0.41422, 17, -35.67, -57.12, 0.58578, 2, 16, 352.23, -49.58, 0.47416, 17, -30.23, -39.36, 0.52584, 2, 16, 329.86, -52.61, 0.69519, 17, -49.3, -27.28, 0.30481, 2, 16, 301.2, -53.62, 0.98148, 17, -71.87, -9.59, 0.01852, 1, 16, 265.22, -52.44, 1, 1, 16, 121.51, -59.54, 1, 1, 16, 103.81, -93.63, 1, 1, 16, 82.68, -97.5, 1, 1, 16, 63.18, -76.64, 1, 1, 16, 45.19, -92.72, 1, 2, 16, 9.99, -100.85, 0.91763, 15, 306.29, -21.8, 0.08237, 2, 16, -16.66, -109.03, 0.80979, 15, 305.79, -49.68, 0.19021, 2, 16, -35.13, -106, 0.75432, 15, 297.18, -66.29, 0.24568, 2, 16, -59.9, -69.19, 0.45583, 15, 254.5, -78.42, 0.54417, 3, 5, 20.35, 257.17, 0.05552, 16, -80.44, -86.75, 0.28843, 15, 264.81, -103.39, 0.65605, 4, 5, 45.06, 248.89, 0.14286, 6, -63.38, 251.15, 0.0637, 16, -106.31, -83.57, 0.20629, 15, 253.76, -126.99, 0.58715, 4, 5, 57.86, 221.11, 0.20331, 6, -51.52, 222.96, 0.1328, 16, -124.4, -58.91, 0.15087, 15, 224.71, -136.54, 0.51302, 4, 5, 79.94, 214.06, 0.32882, 6, -29.68, 215.18, 0.20277, 16, -147.44, -56.42, 0.08692, 15, 215.19, -157.67, 0.38149, 4, 5, 104.11, 186.55, 0.40036, 6, -6.45, 186.88, 0.32323, 16, -176.62, -34.3, 0.02299, 15, 185.1, -178.55, 0.25342, 4, 5, 132.18, 181.42, 0.40773, 6, 21.43, 180.81, 0.46842, 16, -205.15, -34.88, 0.00509, 15, 176.8, -205.85, 0.11876, 4, 5, 161.09, 162.22, 0.33207, 6, 49.69, 160.65, 0.59451, 16, -237.32, -21.85, 4e-05, 15, 154.43, -232.38, 0.07338, 3, 5, 177.14, 146.59, 0.26208, 6, 65.21, 144.49, 0.70754, 15, 137.07, -246.54, 0.03038, 2, 5, 209.18, 131.03, 0.1336, 6, 96.71, 127.86, 0.8664, 2, 5, 245.76, 116.94, 0.0438, 6, 132.79, 112.56, 0.9562, 1, 16, 30.64, -61.65, 1, 2, 16, -1.37, -55.53, 0.86808, 15, 259.68, -18.54, 0.13192, 2, 16, -24.54, -45.67, 0.64857, 15, 243.12, -37.5, 0.35143, 2, 16, -49.61, -13.95, 0.35023, 15, 205.18, -51.49, 0.64977, 2, 16, -66.36, 30.13, 0.00437, 15, 158.08, -53.73, 0.99563, 3, 4, 439.09, 92.64, 0.10568, 5, -6.84, 93.7, 0.19221, 15, 105.51, -57.73, 0.70211, 3, 4, 459.4, 61.76, 0.40592, 5, 20.23, 68.53, 0.48551, 15, 77.42, -81.75, 0.10857, 3, 4, 484.26, 43.45, 0.0513, 5, 48.72, 56.66, 0.73403, 6, -66.15, 58.91, 0.21467, 2, 5, 74.5, 57.58, 0.71462, 6, -40.36, 58.96, 0.28538, 2, 5, 108.21, 60.9, 0.58307, 6, -6.55, 61.16, 0.41693, 2, 5, 144.78, 66.6, 0.28033, 6, 30.19, 65.63, 0.71967, 2, 5, 145.26, -63.48, 0.24179, 6, 26.31, -64.4, 0.75821, 2, 5, 103.94, -57.31, 0.59893, 6, -14.78, -56.85, 0.40107, 3, 4, 477.91, -68.9, 0.00354, 5, 69.27, -53.98, 0.74028, 6, -49.32, -52.36, 0.25617, 2, 4, 443.07, -57.44, 0.31086, 5, 32.7, -51.14, 0.68914, 2, 4, 430.22, -67.99, 0.38351, 5, 22.73, -64.43, 0.61649, 2, 4, 390.99, -76.48, 0.92053, 5, -13.36, -82, 0.07947, 3, 4, 365.9, -118.92, 0.21055, 5, -27.64, -129.19, 0.03298, 18, 38.51, 35.04, 0.75647, 1, 16, 65.48, -63.76, 1, 4, 3, 293.84, 241.59, 0.01345, 4, 236.86, 174.44, 0.54472, 16, 131.31, 91.26, 0.41036, 44, 105.65, 703.47, 0.03147, 4, 3, 256.83, 252.16, 0.02562, 4, 205.26, 196.4, 0.4616, 16, 169.27, 84.9, 0.49358, 44, 74.04, 725.43, 0.01921, 4, 3, 288.09, 215.04, 0.01924, 4, 222.85, 151.16, 0.74876, 48, 22.61, 132.47, 0.192, 44, 91.63, 680.19, 0.04, 4, 3, 228.6, 203.03, 0.06335, 4, 162.66, 159.01, 0.70465, 48, -38.05, 130.57, 0.192, 44, 31.45, 688.04, 0.04, 4, 3, 183.15, 160.33, 0.12264, 4, 105.86, 133.28, 0.64536, 48, -89.99, 96.07, 0.192, 44, -25.35, 662.31, 0.04, 5, 3, 159.05, 109.1, 0.16476, 4, 66.51, 92.58, 0.6029, 19, -193.97, -112.53, 0.00034, 48, -122.32, 49.59, 0.192, 44, -64.71, 621.61, 0.04, 3, 4, 50.76, -12.78, 0.768, 48, -120.96, -56.94, 0.192, 44, -80.45, 516.25, 0.04, 4, 3, 206.53, -30.78, 0.02085, 4, 66.27, -55.13, 0.74715, 48, -98.87, -96.25, 0.192, 44, -64.95, 473.9, 0.04, 4, 3, 206.15, -94.95, 0.12873, 4, 45.18, -115.74, 0.64816, 51, -108.21, 28.77, 0.19422, 44, -86.03, 413.29, 0.02888, 4, 3, 190.8, -120.72, 0.19524, 4, 22.33, -135.17, 0.58634, 51, -127.83, 6.08, 0.19539, 44, -108.88, 393.86, 0.02303, 4, 3, 160.62, 46.6, 0.0838, 4, 47.8, 32.92, 0.6842, 48, -131.21, -12.3, 0.192, 44, -83.41, 561.95, 0.04, 5, 3, 350.1, 203.5, 0.00164, 4, 277.81, 120.22, 0.76463, 16, 71.13, 122.8, 0.00172, 48, 81.83, 110.74, 0.192, 44, 146.6, 649.25, 0.04, 3, 4, 309.38, 68.92, 0.768, 48, 121.21, 65.16, 0.192, 44, 178.17, 597.95, 0.04, 3, 4, 312.72, 15.07, 0.768, 48, 133.14, 12.55, 0.192, 44, 181.51, 544.1, 0.04, 3, 4, 295.38, -35.42, 0.768, 48, 124.12, -40.07, 0.192, 44, 164.16, 493.61, 0.04, 3, 4, 286.76, -78.35, 0.7724, 51, 124.83, 102.57, 0.1931, 44, 155.55, 450.67, 0.0345, 4, 4, 281.4, -156.93, 0.67493, 18, 111.03, -22.64, 0.11482, 51, 131.52, 24.09, 0.19744, 44, 150.18, 372.1, 0.01281, 4, 4, 289.5, -118.66, 0.72627, 18, 73.29, -32.98, 0.05347, 51, 133.69, 63.16, 0.19493, 44, 158.29, 410.37, 0.02533, 4, 3, 283.45, 91.11, 0.00245, 4, 178.43, 35.38, 0.74141, 50, 35.39, 22.27, 0.18597, 44, 47.22, 564.41, 0.07017, 3, 4, 173.6, -12.1, 0.74196, 50, 38.23, -25.37, 0.18549, 44, 42.39, 516.93, 0.07255, 4, 3, 258.04, 126.26, 0.01723, 4, 165.74, 76.85, 0.72837, 50, 16.21, 61.17, 0.1864, 44, 34.53, 605.88, 0.068, 4, 3, 223.89, 121.23, 0.04079, 4, 131.8, 83.12, 0.70417, 50, -18.3, 61.92, 0.18624, 44, 0.58, 612.15, 0.06881, 4, 3, 211.53, 82.53, 0.02906, 4, 107.6, 50.49, 0.71383, 50, -36.95, 25.83, 0.18572, 44, -23.62, 579.52, 0.07138, 3, 4, 161.78, -57.18, 0.74915, 50, 33.79, -71.76, 0.18729, 44, 30.56, 471.85, 0.06356, 4, 3, 218.28, 32.57, 0.00021, 4, 97.85, 1.02, 0.74489, 50, -38.65, -24.56, 0.18627, 44, -33.37, 530.05, 0.06863, 4, 3, 238.98, -10.57, 0.00049, 4, 103.51, -46.49, 0.75003, 50, -25.45, -70.55, 0.18763, 44, -27.71, 482.54, 0.06185, 4, 3, 255.72, -49.68, 0.01215, 4, 106.72, -88.91, 0.74604, 50, -15.48, -111.91, 0.18955, 44, -24.5, 440.12, 0.05225, 4, 3, 261.46, -107.33, 0.04396, 4, 93.53, -145.32, 0.71856, 53, -12.74, 79.69, 0.19063, 44, -37.69, 383.71, 0.04684, 4, 3, 252.06, -159.44, 0.06279, 4, 67.8, -191.61, 0.70145, 53, -31.11, 30.03, 0.19106, 44, -63.41, 337.42, 0.0447, 4, 3, 293.33, -50.97, 0.00383, 4, 141.9, -102.28, 0.75406, 50, 21.39, -119.47, 0.18947, 44, 10.68, 426.75, 0.05263, 4, 3, 299.6, -103.78, 0.01507, 4, 130.77, -154.28, 0.74285, 53, 25.43, 76.52, 0.18948, 44, -0.45, 374.74, 0.0526, 4, 3, 257.58, -203.28, 0.0463, 4, 58.87, -234.88, 0.72486, 53, -33.33, -14.1, 0.19279, 44, -72.34, 294.15, 0.03606, 4, 3, 316.75, -150.76, 0.01412, 4, 131.83, -204.28, 0.74694, 53, 34.1, 27.27, 0.19026, 44, 0.61, 324.75, 0.04868, 4, 3, 323.28, -200.6, 0.01351, 4, 121.91, -253.57, 0.75896, 53, 31.82, -22.95, 0.19312, 44, -9.3, 275.46, 0.03442, 4, 3, 347.51, 137.16, 0.0002, 4, 253.93, 58.27, 0.7557, 49, 85.54, 50.76, 0.18897, 44, 122.72, 587.3, 0.05513, 3, 4, 253.04, -4.37, 0.75763, 49, 94.7, -11.21, 0.18941, 44, 121.82, 524.66, 0.05296, 3, 4, 226.54, -72.08, 0.76247, 49, 79.41, -82.29, 0.19062, 44, 95.33, 456.95, 0.04692, 3, 4, 214.94, -134.26, 0.76745, 52, 85.52, 72.32, 0.19186, 44, 83.73, 394.77, 0.04068, 4, 3, 387.2, -110.56, 7e-05, 4, 211.49, -188.99, 0.77925, 52, 90.45, 17.71, 0.19483, 44, 80.27, 340.04, 0.02586, 4, 3, 291.34, 174.05, 0.012, 4, 212.69, 111.33, 0.74285, 49, 36.33, 96.52, 0.18871, 44, 81.47, 640.35, 0.05643, 4, 3, 234.82, 166.6, 0.04824, 4, 156.79, 122.53, 0.70719, 49, -20.64, 98.62, 0.18886, 44, 25.58, 651.56, 0.05571, 4, 3, 196.74, 140.68, 0.08969, 4, 112.38, 110.3, 0.66703, 49, -62.52, 79.42, 0.18918, 44, -18.84, 639.33, 0.0541, 4, 3, 180.73, 98.52, 0.0939, 4, 83.61, 75.57, 0.65988, 49, -85.34, 40.53, 0.18845, 44, -47.6, 604.6, 0.05777, 4, 3, 185.73, 42.49, 0.01792, 4, 70.24, 20.93, 0.7333, 49, -89.78, -15.54, 0.18781, 44, -60.97, 549.96, 0.06097, 4, 3, 228.48, -41.23, 0.01931, 4, 83.67, -72.11, 0.74056, 49, -61.62, -105.23, 0.18997, 44, -47.55, 456.91, 0.05015, 4, 3, 203.15, -1.86, 0.00072, 4, 72.4, -26.68, 0.75513, 49, -80.02, -62.19, 0.18896, 44, -58.81, 502.35, 0.0552, 4, 3, 231.23, -100.13, 0.07909, 4, 67.25, -128.75, 0.68757, 52, -61.29, 55.24, 0.19167, 44, -63.97, 400.28, 0.04167, 4, 3, 217.41, -143.01, 0.12087, 4, 40.31, -164.87, 0.65236, 52, -82.4, 15.44, 0.19331, 44, -90.9, 364.16, 0.03347, 4, 3, 219.69, -184.46, 0.088, 4, 29.09, -204.83, 0.69153, 52, -87.4, -25.77, 0.19488, 44, -102.13, 324.2, 0.0256, 4, 4, 392.31, 191.57, 0.00019, 16, -1.93, 9.38, 0.38239, 15, 197.8, 1.08, 0.59273, 44, 261.1, 720.6, 0.02469, 2, 15, 133.95, -11.83, 0.97767, 44, 265.9, 655.63, 0.02233, 3, 4, 390.8, 55.2, 0.38088, 15, 62.32, -14.51, 0.59511, 44, 259.58, 584.23, 0.02401, 2, 4, 344.75, 16.12, 0.96335, 44, 213.53, 545.15, 0.03665, 3, 4, 418.37, -38.54, 0.73196, 5, 4.22, -38.65, 0.25161, 44, 287.16, 490.48, 0.01643, 2, 4, 380.98, -10.26, 0.97179, 44, 249.77, 518.77, 0.02821, 2, 4, 374.69, -68.45, 0.98232, 44, 243.48, 460.58, 0.01768, 3, 4, 333.67, -87.38, 0.78233, 18, 25.25, -8.06, 0.19532, 44, 202.46, 441.65, 0.02235, 2, 4, 338.02, -39.74, 0.96897, 44, 206.8, 489.29, 0.03103, 3, 4, 322.15, -124.49, 0.43929, 18, 63.52, -1.29, 0.53924, 44, 190.93, 404.54, 0.02147, 2, 18, 100.3, 5.39, 0.97653, 44, 180.02, 368.79, 0.02347, 3, 4, 352.59, 68.92, 0.77842, 15, 71.14, 25.11, 0.18443, 44, 221.38, 597.95, 0.03715, 4, 4, 346.77, 126.51, 0.34569, 16, 11.46, 87.66, 0.18512, 15, 127.54, 38.1, 0.43191, 44, 215.56, 655.54, 0.03729, 4, 4, 342.59, 176.68, 0.08555, 16, 36.67, 44.08, 0.6732, 15, 176.79, 48.54, 0.20778, 44, 211.38, 705.71, 0.03347, 4, 3, 344.09, 250.37, 0.00202, 4, 287.26, 166.52, 0.44381, 16, 82.36, 76.9, 0.51784, 44, 156.05, 695.55, 0.03633, 2, 16, 38.06, -7.01, 0.97565, 44, 231.94, 752.51, 0.02435, 2, 16, 78.59, -6.36, 0.97843, 44, 195.02, 769.22, 0.02157, 2, 16, 267.64, -15.93, 0.99055, 44, 28.15, 858.61, 0.00945, 3, 16, 306.05, -17.58, 0.91989, 17, -44.95, 14.86, 0.07627, 44, -5.87, 876.51, 0.00384, 2, 16, 339.63, -15.23, 0.58938, 17, -17.75, -4.96, 0.41062, 2, 16, 369.12, -11.78, 0.29554, 17, 7.04, -21.32, 0.70446, 2, 16, 395.75, -0.56, 0.05243, 17, 34.63, -29.89, 0.94757, 1, 17, 70.11, -38.45, 1, 1, 17, 79.67, 9.4, 1, 2, 16, 380.76, 44.57, 0.09493, 17, 52.23, 14.3, 0.90507, 2, 16, 354, 33.97, 0.45014, 17, 24.93, 23.41, 0.54986, 2, 16, 328.46, 30.04, 0.7058, 17, 2.86, 36.86, 0.2942, 5, 3, 129.59, 322.95, 3e-05, 4, 107.69, 304.48, 0.00081, 16, 303.65, 28.84, 0.94435, 17, -16.9, 51.91, 0.04659, 44, -23.52, 833.51, 0.00822, 4, 3, 165.28, 321.07, 0.00125, 4, 140.87, 291.18, 0.01905, 16, 267.97, 26.7, 0.96404, 44, 9.65, 820.21, 0.01567, 4, 3, 352.44, 292.99, 0.00027, 4, 308.93, 204.16, 0.13308, 16, 78.84, 33.61, 0.83134, 44, 177.72, 733.19, 0.0353, 4, 3, 292.76, 296.9, 0.0043, 4, 253.71, 227.13, 0.15623, 16, 138.58, 36.42, 0.80861, 44, 122.5, 756.16, 0.03085, 2, 16, 133.23, -6.59, 0.98031, 44, 145.7, 792.77, 0.01969, 4, 3, 246.02, 305.35, 0.00739, 4, 212.21, 250.23, 0.12582, 16, 185.97, 33.26, 0.84396, 44, 80.99, 779.26, 0.02283, 2, 16, 177.02, -17.85, 0.98423, 44, 110.91, 821.65, 0.01577, 3, 4, 417.67, 30.89, 0.8959, 15, 41.56, -44.22, 0.08094, 44, 286.46, 559.91, 0.02317, 2, 5, 21.89, 9.02, 0.98086, 44, 315.65, 532.59, 0.01914, 3, 5, 64.91, 12.29, 0.83319, 6, -51.46, 14.02, 0.15421, 44, 358.21, 525.54, 0.01261, 3, 5, 106.84, 16.64, 0.71486, 6, -9.41, 16.97, 0.27392, 44, 399.98, 519.8, 0.01122, 3, 5, 144.96, 22.09, 0.09169, 6, 28.87, 21.13, 0.89538, 44, 438.3, 516.03, 0.01293, 3, 5, 145.5, -30.73, 0.12849, 6, 27.65, -31.68, 0.8615, 44, 426.27, 464.59, 0.01, 3, 5, 105.21, -29.1, 0.67571, 6, -12.57, -28.7, 0.31348, 44, 387.52, 475.76, 0.01081, 4, 4, 482.76, -44.47, 0.0073, 5, 68.18, -29.1, 0.82998, 6, -49.58, -27.46, 0.15059, 44, 351.55, 484.56, 0.01214, 3, 4, 444.83, -39.11, 0.31145, 5, 30.06, -32.91, 0.67525, 44, 313.62, 489.92, 0.01329, 3, 5, 14.94, -167.31, 0.20528, 6, -107.42, -163.81, 0.06714, 18, 65.62, 85.35, 0.72758, 4, 5, 59.94, -112.88, 0.43836, 6, -60.62, -110.92, 0.38457, 18, 2.17, 116.38, 0.17457, 42, -773.53, 12.25, 0.0025, 3, 5, 15.55, -121.31, 0.3846, 6, -105.27, -117.85, 0.14048, 18, 20.75, 75.19, 0.47491, 5, 5, 99.77, -110.37, 0.43334, 6, -20.73, -109.74, 0.46261, 18, -9.59, 154.51, 0.09878, 42, -733.64, 13.43, 0.00429, 43, -654.87, 13.43, 0.00097, 4, 5, 139.14, -114.75, 0.31382, 6, 18.48, -115.43, 0.65306, 18, -14.55, 193.82, 0.02607, 42, -694.43, 7.74, 0.00704, 3, 5, 180.83, -72.45, 0.09699, 6, 61.56, -74.55, 0.89387, 42, -651.36, 48.62, 0.00914, 3, 5, 221.3, -78.46, 0.03308, 6, 101.8, -81.92, 0.95302, 42, -611.11, 41.25, 0.01391, 3, 5, 262.86, -81.2, 0.00665, 6, 143.25, -86.04, 0.97678, 42, -569.66, 37.13, 0.01657, 4, 5, 186.26, -109.09, 0.13555, 6, 65.76, -111.35, 0.84858, 42, -647.15, 11.82, 0.01239, 43, -568.38, 11.82, 0.00347, 2, 5, 185.9, 62.95, 0.08436, 6, 71.16, 60.6, 0.91564, 2, 5, 228.01, 53.65, 0.01712, 6, 112.93, 49.9, 0.98288, 2, 5, 265.74, 45.99, 0.00175, 6, 150.39, 40.98, 0.99825, 4, 5, 10.72, 203.67, 0.00533, 16, -81.7, -32.4, 0.31899, 15, 212.76, -87.72, 0.66924, 42, -812.12, 330.28, 0.00645, 5, 5, 29.31, 166.48, 0.19333, 6, -81.88, 169.32, 0.11401, 16, -107.35, 0.32, 0.1029, 15, 173.69, -101.95, 0.57593, 42, -794.79, 292.49, 0.01384, 5, 5, 58.84, 134.76, 0.3568, 6, -53.42, 136.63, 0.28897, 16, -142.63, 25.49, 0.01024, 15, 138.81, -127.67, 0.32658, 42, -766.33, 259.8, 0.0174, 5, 5, 92.75, 115.62, 0.44969, 6, -20.18, 116.36, 0.37226, 16, -179.67, 37.47, 0.0003, 15, 115.93, -159.17, 0.15684, 42, -733.09, 239.53, 0.02091, 4, 5, 127.75, 108.51, 0.38068, 6, 14.57, 108.09, 0.50363, 15, 104.87, -193.13, 0.09157, 42, -698.34, 231.25, 0.02412, 4, 5, 158.11, 93.2, 0.24027, 6, 44.4, 91.76, 0.7086, 15, 86.2, -221.55, 0.0281, 42, -668.51, 214.93, 0.02302, 5, 3, 114.97, 43.56, 0.53395, 4, 3.63, 44.79, 0.43032, 19, -150.4, -46.65, 0.00032, 44, -127.59, 573.82, 0.03469, 46, -529.53, 281.07, 0.00073, 6, 2, -28.49, 85.02, 0.00162, 3, 64.57, 34.76, 0.89034, 4, -46.92, 52.74, 0.06677, 19, -100.07, -37.47, 0.00526, 44, -178.13, 581.77, 0.02527, 46, -525.24, 230.08, 0.01073, 6, 2, -27.64, 29.98, 0.0877, 3, 9.83, 29.03, 0.84501, 4, -100.58, 65, 0.01085, 19, -45.38, -31.31, 0.01817, 44, -231.79, 594.03, 0.01653, 46, -524.39, 175.05, 0.02174, 6, 2, -28.94, -12.42, 0.09072, 3, -32.53, 26.56, 0.22056, 4, -141.46, 76.34, 0.00242, 19, -3.04, -28.51, 0.64674, 44, -272.67, 605.37, 0.0093, 46, -525.68, 132.65, 0.03025, 6, 2, 60.49, -20.95, 0.10372, 3, -33.08, -63.28, 0.29763, 19, -3.18, 61.32, 0.53081, 20, -105.18, -73.72, 0.02968, 44, -302.21, 520.52, 0.00708, 46, -436.25, 124.12, 0.03108, 7, 2, 112.58, -14.27, 0.04112, 3, -21.81, -114.57, 0.42884, 4, -176.89, -60.69, 0.00174, 19, -14.85, 112.52, 0.23603, 20, -116.85, -22.52, 0.26295, 44, -308.11, 468.34, 0.00406, 46, -384.16, 130.8, 0.02526, 6, 3, -12.5, 119.58, 0.44468, 4, -92.46, 157.9, 0.06443, 19, -22.35, -121.69, 0.22858, 25, -123.24, 50.64, 0.22807, 44, -223.68, 686.93, 0.00655, 46, -616.56, 160.85, 0.02769, 6, 3, 22.03, 123.33, 0.57751, 4, -58.57, 150.3, 0.14075, 19, -56.85, -125.7, 0.13166, 25, -157.74, 46.63, 0.11613, 44, -189.79, 679.33, 0.01325, 46, -617.23, 195.57, 0.0207, 5, 3, 54.48, 112.81, 0.6539, 4, -31.26, 129.86, 0.24615, 19, -89.38, -115.43, 0.06557, 44, -162.47, 658.89, 0.02011, 46, -603.87, 226.96, 0.01427, 5, 3, 97.18, 122.65, 0.48674, 4, 12.33, 125.38, 0.46124, 19, -132, -125.6, 0.01808, 44, -118.88, 654.41, 0.02831, 46, -609.88, 270.37, 0.00563, 5, 3, 138.57, 139.74, 0.26581, 4, 57.02, 128.2, 0.59958, 19, -173.26, -143.02, 0.00257, 48, -137.39, 83.22, 0.09644, 44, -74.19, 657.23, 0.0356, 5, 3, 164.55, -58.66, 0.23417, 4, 17.53, -67.97, 0.63941, 48, -144.92, -116.73, 0.0484, 51, -142.83, 71.77, 0.04598, 44, -113.68, 461.06, 0.03205, 3, 3, 124.95, -58.17, 0.57562, 4, -19.79, -54.71, 0.39458, 44, -151, 474.32, 0.0298, 6, 2, 68.09, 85.49, 0.00292, 3, 73.61, -61.4, 0.88546, 4, -69.42, -41.19, 0.07856, 19, -109.86, 58.62, 0.00451, 44, -200.63, 487.84, 0.01885, 46, -428.65, 230.56, 0.0097, 6, 2, 64.76, 32.07, 0.05141, 3, 20.1, -62.82, 0.82212, 4, -120.52, -25.25, 0.00456, 19, -56.36, 60.45, 0.08996, 44, -251.73, 503.78, 0.01151, 46, -431.99, 177.14, 0.02043, 5, 2, 126.86, 128.23, 0.00023, 3, 121.4, -116.14, 0.60138, 4, -41.87, -108.43, 0.37985, 19, -158.07, 112.99, 0.00098, 44, -173.08, 420.6, 0.01755, 6, 2, 116.84, 80.15, 0.00648, 3, 72.62, -110.43, 0.80341, 4, -86.19, -87.27, 0.14553, 19, -109.24, 107.66, 0.02229, 44, -217.41, 441.76, 0.0136, 46, -379.9, 225.22, 0.0087, 7, 2, 115.51, 31.4, 0.02726, 3, 23.94, -113.43, 0.71556, 4, -133.23, -74.39, 0.02883, 19, -60.59, 111.03, 0.11191, 20, -162.59, -24.01, 0.09145, 44, -264.44, 454.64, 0.00735, 46, -381.24, 176.47, 0.01764, 4, 3, 160.53, -120.62, 0.35173, 4, -6.29, -125.31, 0.53117, 51, -157.62, 11.46, 0.0981, 44, -137.5, 403.72, 0.019, 5, 3, -67.43, 134.27, 0.1858, 4, -139.7, 189.54, 0.01556, 19, 32.69, -135.95, 0.25282, 25, -68.2, 36.38, 0.51132, 46, -636.06, 107.44, 0.03451, 4, 3, -92.85, 36.4, 0.03854, 4, -195.37, 105.14, 0.0001, 19, 57.35, -37.89, 0.9178, 46, -540.85, 73.44, 0.04356, 5, 2, 59.62, -80.13, 0.02577, 3, -92.11, -67.66, 0.04064, 19, 55.81, 66.16, 0.71534, 20, -46.19, -68.88, 0.17832, 46, -437.13, 64.93, 0.03993, 5, 2, 124.23, -63.98, 0.02316, 3, -70.29, -130.58, 0.17089, 19, 33.5, 128.92, 0.21216, 20, -68.49, -6.13, 0.56633, 46, -372.51, 81.09, 0.02745, 2, 26, 124.45, -69.38, 0.97263, 46, -733.89, -224.96, 0.02737, 2, 26, 59.9, -63.07, 0.97567, 46, -752.25, -162.75, 0.02433, 3, 25, 151.2, -66.88, 0.0212, 26, -0.27, -47.07, 0.95782, 46, -760, -100.97, 0.02099, 3, 25, 97.41, -73.7, 0.21317, 26, -51.1, -28.23, 0.76925, 46, -761.61, -46.78, 0.01758, 3, 25, 43.95, -70.47, 0.55043, 26, -97, -0.62, 0.43109, 46, -753.23, 6.12, 0.01848, 6, 3, -121.62, 221.79, 0.01321, 4, -162.72, 289.88, 0.00064, 16, 541.92, 157.53, 1e-05, 25, -13.34, -50.72, 0.86822, 26, -138.64, 43.4, 0.10043, 46, -728.05, 61.23, 0.01749, 6, 3, -83.96, 192.44, 0.09197, 4, -136.56, 249.94, 0.00822, 16, 501.21, 182.47, 0.00063, 19, 49.67, -193.99, 0.03189, 25, -51.22, -21.66, 0.84645, 46, -695.48, 96.14, 0.02085, 5, 3, -142.28, 47.82, 0.00723, 19, 106.87, -48.93, 0.83881, 25, 5.98, 123.4, 0.09268, 26, -40.93, 188.81, 0.01598, 46, -556.61, 25.21, 0.0453, 4, 19, 161.86, -63.88, 0.69497, 25, 60.97, 108.45, 0.16081, 26, 0.9, 150.11, 0.09826, 46, -576.79, -28.08, 0.04595, 4, 19, 221.39, -61.17, 0.68963, 25, 120.5, 111.17, 0.05231, 26, 54.92, 124.96, 0.22061, 46, -579.83, -87.59, 0.03745, 4, 19, 267.84, -32.07, 0.81729, 25, 166.95, 140.26, 0.00471, 26, 109.57, 129.25, 0.15596, 46, -555.35, -136.63, 0.02203, 3, 19, 272.64, 30.21, 0.94185, 26, 142.65, 182.24, 0.04652, 46, -493.82, -147.42, 0.01163, 3, 19, 221.51, 46.61, 0.96934, 26, 104.92, 220.44, 6e-05, 46, -472.57, -98.11, 0.0306, 4, 19, 165.63, 59.13, 0.88771, 20, 63.64, -75.91, 0.07297, 26, 61.18, 257.4, 3e-05, 46, -454.71, -43.7, 0.03928, 6, 2, 53.08, -133.5, 0.00223, 3, -145.85, -65.89, 0.00049, 19, 109.56, 64.8, 0.75511, 20, 7.57, -70.24, 0.20438, 26, 14.1, 288.38, 1e-05, 46, -443.66, 11.56, 0.03777, 4, 19, 146.37, 149.69, 0.16161, 20, 44.38, 14.64, 0.80633, 26, 86.02, 346.59, 5e-05, 46, -362.72, -33.26, 0.03201, 4, 2, 132.32, -118.82, 0.01076, 3, -124.19, -143.51, 0.08022, 20, -14.69, 7.21, 0.87635, 46, -364.42, 26.25, 0.03268, 3, 19, 343.99, -64.26, 0.28045, 26, 162.17, 65.47, 0.69191, 46, -594.74, -209.33, 0.02764, 4, 19, 300.53, -94.11, 0.2851, 25, 199.64, 78.22, 0.00331, 26, 109.83, 59.12, 0.6812, 46, -620.26, -163.18, 0.03039, 4, 19, 250.73, -126.53, 0.21634, 25, 149.84, 45.8, 0.03792, 26, 50.68, 53.44, 0.71218, 46, -647.72, -110.49, 0.03356, 4, 19, 204.6, -148.97, 0.16071, 25, 103.71, 23.36, 0.18185, 26, -0.6, 54.89, 0.62464, 46, -665.61, -62.42, 0.03281, 4, 19, 156.84, -153.02, 0.14484, 25, 55.95, 19.31, 0.49556, 26, -44.8, 73.41, 0.32058, 46, -665.03, -14.49, 0.03901, 5, 3, -145.51, 140.67, 0.00731, 19, 110.82, -141.75, 0.17101, 25, 9.93, 30.58, 0.73168, 26, -80.39, 104.7, 0.05402, 46, -649.37, 30.23, 0.03598, 7, 3, -111.63, 125.73, 0.05768, 4, -184.29, 195.74, 0.0019, 16, 521.23, 251.86, 0.00012, 19, 76.82, -127.07, 0.29335, 25, -24.07, 45.26, 0.605, 26, -103.73, 133.45, 0.0057, 46, -631.49, 62.66, 0.03625, 3, 26, 469.73, -44.5, 0.9728, 27, -97.07, -104, 0.0063, 46, -581.28, -535.68, 0.0209, 3, 26, 516.44, -38.42, 0.9381, 27, -59.04, -76.2, 0.03785, 46, -558.12, -576.69, 0.02405, 3, 26, 561.21, -44.58, 0.78386, 27, -16.82, -60.08, 0.19171, 46, -547.03, -620.5, 0.02443, 3, 26, 629.72, -71, 0.11871, 27, 55.96, -50.29, 0.85765, 46, -545.82, -693.92, 0.02364, 3, 26, 658.13, -88.53, 0.03624, 27, 89.29, -52, 0.94055, 46, -551.41, -726.83, 0.02321, 2, 27, 93.93, 3.5, 0.975, 46, -496.83, -737.91, 0.025, 3, 26, 626.69, 5.78, 0.08342, 27, 16.37, 15.57, 0.89356, 46, -475.78, -662.3, 0.02303, 3, 26, 581.39, 29.61, 0.89963, 27, -34.81, 14.68, 0.07999, 46, -470.69, -611.37, 0.02038, 2, 26, 493.68, 47.57, 0.9795, 46, -486.95, -523.33, 0.0205, 2, 26, 534.77, 41.24, 0.97966, 46, -477.4, -563.79, 0.02034, 2, 27, 54.72, 10.25, 0.97598, 46, -485.54, -699.77, 0.02402, 3, 26, 594.25, -54.86, 0.44087, 27, 17.09, -53.2, 0.5351, 46, -544.16, -654.99, 0.02403, 2, 26, 285.73, -72.41, 0.9744, 46, -676.19, -375.58, 0.0256, 3, 19, 475.43, 4.62, 0.03673, 26, 310.57, 65.69, 0.94147, 46, -538.85, -346.8, 0.0218, 2, 27, 155.12, -59.03, 0.97588, 46, -566.08, -791.39, 0.02412, 2, 27, 151.03, 6.46, 0.98037, 46, -500.56, -794.98, 0.01963, 2, 27, 258.74, -64.02, 0.9809, 46, -583.13, -893.71, 0.0191, 1, 27, 493.5, -8.39, 1, 2, 27, 253.13, 6.72, 0.98533, 46, -512.23, -896.4, 0.01467], "hull": 134, "edges": [12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 52, 54, 56, 58, 58, 60, 64, 66, 66, 68, 68, 70, 76, 78, 78, 80, 90, 92, 98, 100, 100, 102, 110, 112, 112, 114, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 170, 172, 172, 174, 174, 176, 198, 200, 204, 206, 206, 208, 208, 210, 210, 212, 218, 220, 220, 222, 222, 224, 232, 234, 234, 236, 236, 238, 242, 244, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 2, 0, 266, 0, 262, 264, 264, 266, 256, 258, 258, 260, 260, 262, 238, 240, 240, 242, 230, 232, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 230, 304, 304, 268, 216, 218, 212, 214, 214, 216, 224, 226, 226, 228, 228, 230, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 200, 202, 202, 204, 184, 186, 186, 188, 180, 182, 182, 184, 176, 178, 178, 180, 168, 170, 92, 94, 94, 96, 96, 98, 102, 104, 104, 106, 106, 108, 108, 110, 118, 120, 120, 122, 122, 124, 114, 116, 116, 118, 86, 88, 84, 86, 88, 90, 80, 82, 82, 84, 72, 74, 74, 76, 70, 72, 60, 62, 62, 64, 48, 50, 46, 48, 8, 10, 10, 12, 306, 308, 308, 184, 310, 312, 312, 314, 314, 316, 318, 320, 320, 322, 322, 324, 316, 326, 326, 318, 310, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 340, 340, 338, 342, 344, 342, 346, 346, 348, 348, 350, 344, 352, 350, 354, 354, 356, 356, 358, 358, 360, 360, 362, 324, 58, 352, 364, 364, 366, 368, 362, 366, 370, 370, 372, 54, 56, 50, 52, 374, 376, 376, 378, 378, 380, 380, 382, 374, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 396, 396, 394, 394, 398, 398, 400, 400, 402, 44, 46, 410, 414, 414, 412, 464, 462, 436, 466, 460, 468, 468, 464, 438, 470, 470, 466, 6, 8, 2, 4, 4, 6, 244, 246, 534, 536, 534, 532, 538, 532, 564, 566, 566, 568, 568, 570, 572, 644, 644, 620, 606, 646, 646, 636, 648, 652, 652, 654, 654, 656, 656, 650, 382, 338], "width": 676, "height": 2211}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04122, 0.35717, 0.46946, 0.05752, 0.95781, 0.14125, 0.9672, 0.72954, 0.68546, 0.95648, 0.04122, 0.84191], "triangles": [4, 0, 1, 3, 1, 2, 3, 4, 1, 4, 5, 0], "vertices": [2, 6, 92.12, -44.1, 0.96, 42, -620.79, 79.07, 0.04, 2, 6, 106.82, -70.73, 0.96, 42, -606.09, 52.44, 0.04, 2, 6, 101.47, -100.36, 0.97279, 42, -611.44, 22.81, 0.02721, 2, 6, 70.88, -99.91, 0.97188, 42, -642.03, 23.26, 0.02812, 2, 6, 59.66, -82.34, 0.96, 42, -653.25, 40.83, 0.04, 2, 6, 66.93, -43.26, 0.96, 42, -645.98, 79.91, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 61, "height": 52}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.04458, 0.22072, 0.03239, 0.73932, 0.40066, 0.95766, 0.95225, 0.86687, 0.97026, 0.34156, 0.4514, 0.04756], "triangles": [4, 2, 5, 5, 1, 0, 3, 2, 4, 2, 1, 5], "vertices": [2, 6, 106.02, 45.88, 0.96, 42, -606.9, 169.04, 0.04, 2, 6, 78.57, 47.65, 0.96, 42, -634.34, 170.82, 0.04, 2, 6, 66.14, 22.27, 0.96, 42, -646.77, 145.44, 0.04, 2, 6, 69.66, -16.48, 0.96, 42, -643.25, 106.69, 0.04, 2, 6, 97.44, -18.67, 0.96, 42, -615.47, 104.5, 0.04, 2, 6, 114.23, 17.11, 0.96, 42, -598.68, 140.28, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 70, "height": 53}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 13, -11.47, -10.2, 0.96272, 42, -636.46, 42.08, 0.03728, 2, 13, -10.77, 10.79, 0.96, 42, -635.76, 63.07, 0.04, 2, 13, 10.22, 10.09, 0.96, 42, -614.77, 62.37, 0.04, 2, 13, 9.52, -10.9, 0.96287, 42, -615.48, 41.38, 0.03713], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 21}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.50629, 0.02243, 0.65195, 1e-05, 0.8137, 0.01785, 0.96558, 0.15455, 1, 0.34225, 0.96604, 0.52759, 0.91684, 0.68206, 0.75686, 0.88369, 0.64127, 0.96288, 0.52609, 0.88945, 0.4127, 0.88833, 0.31718, 0.86039, 0.21621, 0.81155, 0.1117, 0.8026, 0.03055, 0.79965, 0.03067, 0.68823, 0.10327, 0.53633, 0.21247, 0.3795, 0.32735, 0.26089, 0.37723, 0.07668, 0.08761, 0.70427, 0.153, 0.5589, 0.23591, 0.43814, 0.34078, 0.33543, 0.45845, 0.27135, 0.56374, 0.27333, 0.65365, 0.31721, 0.7826, 0.52461, 0.72877, 0.41493, 0.73834, 0.63991, 0.65907, 0.72566, 0.55023, 0.75757, 0.44849, 0.77153, 0.34439, 0.76156, 0.25329, 0.72567, 0.16102, 0.71569], "triangles": [24, 19, 0, 18, 19, 24, 25, 0, 1, 24, 0, 25, 26, 1, 2, 25, 1, 26, 23, 18, 24, 17, 18, 23, 28, 26, 2, 28, 2, 3, 28, 3, 4, 22, 17, 23, 27, 28, 4, 5, 27, 4, 21, 16, 17, 22, 21, 17, 29, 28, 27, 6, 27, 5, 29, 27, 6, 20, 15, 16, 20, 16, 21, 34, 35, 21, 20, 21, 35, 30, 26, 28, 30, 28, 29, 33, 34, 22, 34, 21, 22, 31, 24, 25, 31, 25, 26, 31, 26, 30, 32, 23, 24, 23, 33, 22, 23, 32, 33, 24, 31, 32, 14, 15, 20, 13, 20, 35, 14, 20, 13, 12, 35, 34, 13, 35, 12, 11, 34, 33, 12, 34, 11, 7, 29, 6, 30, 29, 7, 10, 33, 32, 11, 33, 10, 9, 32, 31, 10, 32, 9, 8, 31, 30, 8, 30, 7, 9, 31, 8], "vertices": [2, 6, 101.75, -78.8, 0.96, 42, -611.16, 44.37, 0.04, 2, 6, 102.25, -87.42, 0.96413, 42, -610.66, 35.75, 0.03587, 2, 6, 101.31, -96.93, 0.96976, 42, -611.6, 26.24, 0.03024, 2, 6, 96.22, -105.73, 0.9752, 42, -616.69, 17.44, 0.0248, 2, 6, 89.59, -107.54, 0.97635, 42, -623.32, 15.63, 0.02365, 2, 6, 83.17, -105.32, 0.97551, 42, -629.74, 17.85, 0.02449, 2, 6, 77.87, -102.24, 0.9741, 42, -635.04, 20.93, 0.0259, 2, 6, 71.13, -92.57, 0.96923, 42, -641.78, 30.6, 0.03077, 2, 6, 68.59, -85.66, 0.96544, 42, -644.32, 37.51, 0.03456, 2, 6, 71.38, -78.95, 0.96124, 42, -641.53, 44.22, 0.03876, 2, 6, 71.65, -72.27, 0.96, 42, -641.26, 50.9, 0.04, 2, 6, 72.81, -66.67, 0.96, 42, -640.1, 56.5, 0.04, 2, 6, 74.72, -60.77, 0.96, 42, -638.19, 62.4, 0.04, 2, 6, 75.24, -54.62, 0.96, 42, -637.67, 68.55, 0.04, 2, 6, 75.5, -49.84, 0.96, 42, -637.41, 73.33, 0.04, 2, 6, 79.4, -49.98, 0.96, 42, -633.51, 73.19, 0.04, 2, 6, 84.57, -54.43, 0.96, 42, -628.34, 68.73, 0.04, 2, 6, 89.84, -61.06, 0.96, 42, -623.07, 62.11, 0.04, 2, 6, 93.76, -67.97, 0.96, 42, -619.15, 55.2, 0.04, 2, 6, 100.11, -71.13, 0.96, 42, -612.8, 52.04, 0.04, 2, 6, 78.73, -53.31, 0.96, 42, -634.18, 69.85, 0.04, 2, 6, 83.68, -57.34, 0.96, 42, -629.23, 65.83, 0.04, 2, 6, 87.74, -62.37, 0.96, 42, -625.17, 60.8, 0.04, 2, 6, 91.13, -68.68, 0.96, 42, -621.78, 54.49, 0.04, 2, 6, 93.14, -75.69, 0.96, 42, -619.77, 47.48, 0.04, 2, 6, 92.86, -81.9, 0.9612, 42, -620.05, 41.27, 0.0388, 2, 6, 91.15, -87.15, 0.96438, 42, -621.76, 36.02, 0.03562, 2, 6, 83.64, -94.51, 0.96913, 42, -629.27, 28.66, 0.03087, 2, 6, 87.58, -91.46, 0.96711, 42, -625.33, 31.71, 0.03289, 2, 6, 79.69, -91.76, 0.96788, 42, -633.22, 31.41, 0.03212, 2, 6, 76.85, -86.99, 0.96534, 42, -636.06, 36.18, 0.03466, 2, 6, 75.95, -80.53, 0.96171, 42, -636.96, 42.64, 0.03829, 2, 6, 75.66, -74.52, 0.96, 42, -637.25, 48.65, 0.04, 2, 6, 76.22, -68.39, 0.96, 42, -636.69, 54.78, 0.04, 2, 6, 77.65, -63.06, 0.96, 42, -635.26, 60.11, 0.04, 2, 6, 78.18, -57.63, 0.96, 42, -634.73, 65.54, 0.04], "hull": 20, "edges": [6, 8, 12, 14, 14, 16, 28, 30, 26, 28, 20, 22, 36, 38, 34, 36, 16, 18, 18, 20, 2, 4, 8, 10, 10, 12, 32, 34, 22, 24, 24, 26, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 56, 56, 54, 54, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 40, 2, 0, 0, 38, 4, 6, 30, 32, 42, 40], "width": 59, "height": 35}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 14, -11.16, -9.46, 0.96, 42, -631.42, 132.97, 0.04, 2, 14, -10.5, 10.52, 0.96, 42, -630.75, 152.96, 0.04, 2, 14, 9.49, 9.86, 0.96, 42, -610.76, 152.29, 0.04, 2, 14, 8.82, -10.13, 0.96, 42, -611.43, 132.3, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 20}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.46932, 0.00631, 0.64331, 0.07957, 0.76768, 0.26981, 0.85747, 0.43177, 0.91691, 0.58603, 0.97029, 0.71211, 0.97124, 0.79451, 0.90294, 0.84059, 0.7949, 0.87253, 0.66468, 0.89321, 0.53557, 0.96239, 0.40358, 0.96831, 0.26283, 0.90503, 0.14837, 0.79057, 0.0518, 0.60085, 0.01119, 0.31404, 0.1252, 0.1871, 0.21773, 0.07837, 0.34244, 0.04358, 0.19067, 0.53186, 0.27399, 0.41876, 0.38219, 0.33498, 0.50282, 0.29728, 0.62096, 0.30566, 0.72169, 0.3664, 0.81745, 0.50045, 0.88585, 0.62193, 0.92937, 0.72875, 0.87839, 0.75807, 0.79133, 0.76435, 0.66324, 0.76645, 0.55007, 0.78111, 0.4282, 0.77273, 0.32747, 0.72665, 0.24042, 0.63449], "triangles": [22, 0, 1, 23, 22, 1, 23, 1, 2, 21, 18, 0, 21, 0, 22, 24, 23, 2, 21, 20, 17, 21, 17, 18, 16, 17, 20, 24, 2, 3, 25, 24, 3, 19, 16, 20, 15, 16, 19, 25, 3, 4, 14, 15, 19, 26, 25, 4, 34, 19, 20, 33, 20, 21, 34, 20, 33, 27, 4, 5, 26, 4, 27, 26, 29, 25, 28, 26, 27, 25, 30, 24, 28, 29, 26, 30, 23, 24, 29, 30, 25, 32, 21, 22, 33, 21, 32, 22, 31, 32, 23, 31, 22, 23, 30, 31, 13, 14, 19, 13, 19, 34, 27, 5, 6, 7, 28, 27, 7, 27, 6, 8, 29, 28, 8, 28, 7, 8, 9, 30, 8, 30, 29, 31, 30, 9, 12, 34, 33, 13, 34, 12, 10, 32, 31, 10, 31, 9, 11, 33, 32, 11, 32, 10, 12, 33, 11], "vertices": [2, 6, 109.67, 21.82, 0.96, 42, -603.24, 144.99, 0.04, 2, 6, 106.52, 10.79, 0.96, 42, -606.4, 133.96, 0.04, 2, 6, 99.02, 3.08, 0.96, 42, -613.89, 126.24, 0.04, 2, 6, 92.68, -2.46, 0.96, 42, -620.23, 120.71, 0.04, 2, 6, 86.69, -6.07, 0.96, 42, -626.22, 117.1, 0.04, 2, 6, 81.79, -9.32, 0.96, 42, -631.12, 113.85, 0.04, 2, 6, 78.66, -9.28, 0.96, 42, -634.25, 113.89, 0.04, 2, 6, 77.06, -4.85, 0.96, 42, -635.85, 118.32, 0.04, 2, 6, 76.07, 2.1, 0.96, 42, -636.84, 125.27, 0.04, 2, 6, 75.57, 10.46, 0.96, 42, -637.34, 133.63, 0.04, 2, 6, 73.22, 18.8, 0.96, 42, -639.69, 141.97, 0.04, 2, 6, 73.28, 27.25, 0.96, 42, -639.64, 150.42, 0.04, 2, 6, 75.98, 36.18, 0.96, 42, -636.93, 159.34, 0.04, 2, 6, 80.57, 43.35, 0.96, 42, -632.34, 166.52, 0.04, 2, 6, 87.98, 49.29, 0.96, 42, -624.93, 172.46, 0.04, 2, 6, 98.96, 51.52, 0.96, 42, -613.95, 174.69, 0.04, 2, 6, 103.54, 44.07, 0.96, 42, -609.37, 167.24, 0.04, 2, 6, 107.47, 38.01, 0.96, 42, -605.44, 161.18, 0.04, 2, 6, 108.53, 29.99, 0.96, 42, -604.38, 153.16, 0.04, 2, 6, 90.31, 40.32, 0.96, 42, -622.6, 163.49, 0.04, 2, 6, 94.42, 34.84, 0.96, 42, -618.49, 158.01, 0.04, 2, 6, 97.37, 27.82, 0.96, 42, -615.54, 150.98, 0.04, 2, 6, 98.55, 20.05, 0.96, 42, -614.36, 143.22, 0.04, 2, 6, 97.98, 12.51, 0.96, 42, -614.93, 135.67, 0.04, 2, 6, 95.45, 6.14, 0.96, 42, -617.46, 129.31, 0.04, 2, 6, 90.16, 0.19, 0.96, 42, -622.75, 123.35, 0.04, 2, 6, 85.4, -4.04, 0.96, 42, -627.51, 119.13, 0.04, 2, 6, 81.25, -6.68, 0.96, 42, -631.66, 116.49, 0.04, 2, 6, 80.24, -3.38, 0.96, 42, -632.67, 119.78, 0.04, 2, 6, 80.19, 2.19, 0.96, 42, -632.72, 125.36, 0.04, 2, 6, 80.39, 10.39, 0.96, 42, -632.52, 133.56, 0.04, 2, 6, 80.07, 17.64, 0.96, 42, -632.84, 140.81, 0.04, 2, 6, 80.65, 25.43, 0.96, 42, -632.26, 148.6, 0.04, 2, 6, 82.62, 31.81, 0.96, 42, -630.29, 154.98, 0.04, 2, 6, 86.3, 37.27, 0.96, 42, -626.61, 160.43, 0.04], "hull": 19, "edges": [0, 36, 0, 2, 10, 12, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 6, 8, 8, 10, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 38], "width": 64, "height": 38}}, "hair": {"hair": {"type": "mesh", "uvs": [0.92867, 0.08804, 0.88838, 0.17636, 0.81825, 0.25838, 0.68066, 0.32305, 0.51771, 0.36531, 0.59828, 0.42101, 0.69941, 0.48721, 0.74262, 0.53836, 0.71879, 0.58566, 0.60172, 0.65689, 0.56443, 0.70712, 0.59693, 0.74978, 0.68877, 0.78327, 0.8645, 0.82642, 0.97095, 0.88036, 0.99777, 0.93205, 0.93499, 0.97995, 0.80847, 0.99999, 0.59614, 0.96872, 0.63677, 0.9404, 0.6224, 0.90977, 0.5678, 0.87682, 0.48871, 0.85348, 0.36682, 0.82668, 0.19711, 0.77808, 0.09977, 0.71827, 0.07387, 0.65269, 0.11444, 0.57607, 0.02905, 0.48899, 0.00223, 0.39117, 0.02604, 0.31303, 0.09015, 0.23854, 0.19487, 0.16426, 0.28171, 0.07225, 0.40039, 0.00497, 0.67451, 0.00376, 0.94862, 0.00255, 0.17386, 0.47048, 0.33853, 0.39832, 0.4061, 0.44453, 0.43726, 0.50902, 0.36605, 0.59194, 0.28594, 0.67639, 0.36605, 0.75316, 0.19824, 0.35988, 0.31841, 0.29846, 0.48752, 0.22015, 0.57653, 0.14031, 0.63884, 0.0666], "triangles": [17, 19, 16, 20, 21, 13, 19, 20, 14, 10, 42, 9, 43, 10, 11, 42, 26, 27, 43, 42, 10, 23, 43, 11, 25, 26, 42, 25, 42, 43, 18, 19, 17, 22, 23, 11, 24, 25, 43, 24, 43, 23, 21, 22, 12, 12, 22, 11, 16, 19, 15, 15, 19, 14, 21, 12, 13, 20, 13, 14, 42, 41, 9, 41, 27, 40, 40, 5, 6, 7, 40, 6, 41, 40, 8, 9, 41, 8, 8, 40, 7, 41, 42, 27, 27, 37, 40, 37, 39, 40, 28, 29, 37, 40, 39, 5, 37, 38, 39, 29, 30, 44, 38, 44, 45, 44, 30, 45, 4, 38, 45, 46, 47, 2, 47, 33, 48, 1, 47, 0, 0, 35, 36, 2, 47, 1, 0, 47, 48, 48, 35, 0, 48, 34, 35, 3, 46, 2, 46, 32, 47, 32, 33, 47, 33, 34, 48, 45, 46, 3, 31, 32, 46, 4, 45, 3, 45, 31, 46, 30, 31, 45, 37, 44, 38, 5, 39, 4, 39, 38, 4, 37, 29, 44, 27, 28, 37], "vertices": [3, 6, 157.53, 29.39, 0.95703, 5, 273.26, 34.65, 0.00297, 42, -555.38, 152.56, 0.04, 3, 6, 124.95, 35.65, 0.83286, 5, 240.5, 39.81, 0.12739, 42, -587.96, 158.81, 0.03975, 3, 6, 94.84, 45.63, 0.72362, 5, 210.07, 48.78, 0.2411, 42, -618.07, 168.8, 0.03528, 3, 6, 71.45, 64.04, 0.64964, 5, 186.08, 66.4, 0.32032, 42, -641.46, 187.21, 0.03004, 3, 6, 56.48, 85.41, 0.6223, 5, 170.4, 87.25, 0.35298, 42, -656.43, 208.58, 0.02472, 4, 6, 35.48, 75.8, 0.51516, 5, 149.73, 76.94, 0.43031, 15, 71, -211.37, 0.02912, 42, -677.43, 198.96, 0.02541, 4, 6, 10.5, 63.68, 0.34731, 5, 125.17, 64, 0.52286, 15, 60.94, -185.49, 0.11027, 42, -702.41, 186.85, 0.01956, 4, 6, -8.65, 58.79, 0.24008, 5, 106.2, 58.46, 0.56956, 15, 57.61, -166.01, 0.17353, 42, -721.56, 181.96, 0.01683, 5, 6, -26.09, 62.42, 0.15857, 5, 88.65, 61.51, 0.55336, 4, 524.19, 38.68, 0.0244, 15, 62.64, -148.92, 0.24782, 42, -739, 185.59, 0.01585, 6, 6, -52, 78.28, 0.09076, 5, 62.22, 76.5, 0.39281, 4, 502.08, 59.52, 0.17756, 15, 80.54, -124.38, 0.32136, 42, -764.91, 201.45, 0.01564, 44, 370.87, 588.55, 0.00186, 6, 6, -70.46, 83.68, 0.05695, 5, 43.59, 81.27, 0.27878, 4, 485.12, 68.58, 0.27466, 15, 87.41, -106.41, 0.37065, 42, -783.37, 206.85, 0.01371, 44, 353.9, 597.61, 0.00525, 6, 6, -86.42, 80.05, 0.01894, 5, 27.76, 77.11, 0.18659, 4, 468.75, 68.3, 0.38006, 15, 85.09, -90.21, 0.39735, 42, -799.33, 203.22, 0.00851, 44, 337.54, 597.33, 0.00854, 5, 5, 15.34, 65.36, 0.10999, 4, 453.89, 59.84, 0.48758, 15, 74.82, -76.53, 0.38723, 42, -812.14, 191.89, 0.00368, 44, 322.68, 588.87, 0.01152, 4, 5, -0.67, 42.86, 0.01243, 4, 432.99, 41.8, 0.68478, 15, 54.3, -58.06, 0.28773, 44, 301.78, 570.82, 0.01506, 3, 4, 410.32, 33.32, 0.71461, 15, 43.05, -36.62, 0.26687, 44, 279.1, 562.35, 0.01852, 3, 4, 390.87, 34.54, 0.63489, 15, 41.83, -17.18, 0.34206, 44, 259.66, 563.57, 0.02305, 3, 4, 375.52, 46.57, 0.6983, 15, 51.84, -0.44, 0.2723, 44, 244.31, 575.6, 0.0294, 3, 4, 372.15, 64.07, 0.59289, 15, 68.78, 5.1, 0.37602, 44, 240.94, 593.1, 0.03109, 3, 4, 389.88, 87.71, 0.18326, 15, 94.45, -9.52, 0.79424, 44, 258.66, 616.74, 0.02249, 4, 5, -42.96, 72.01, 0.00117, 4, 398.85, 80.16, 0.11866, 15, 88.09, -19.37, 0.85837, 44, 267.63, 609.19, 0.02181, 4, 5, -31.6, 73.85, 0.00902, 4, 410.32, 79.25, 0.20836, 15, 88.62, -30.87, 0.76287, 44, 279.11, 608.28, 0.01975, 4, 5, -19.37, 80.84, 0.03082, 4, 423.86, 83.13, 0.30633, 15, 94.17, -43.81, 0.6459, 44, 292.64, 612.16, 0.01695, 5, 5, -10.71, 90.96, 0.05951, 4, 434.67, 90.9, 0.30594, 15, 103.24, -53.57, 0.61888, 42, -837.32, 218.35, 0.00127, 44, 303.46, 619.93, 0.0144, 6, 6, -113.95, 110.44, 0.04156, 5, -0.77, 106.57, 0.10695, 4, 448.04, 103.7, 0.19853, 15, 117.6, -65.23, 0.63712, 42, -826.86, 233.61, 0.00492, 44, 316.83, 632.73, 0.01092, 5, 6, -95.2, 131.55, 0.14829, 5, 17.26, 128.29, 0.18942, 15, 137.13, -85.62, 0.64639, 42, -808.11, 254.72, 0.01056, 44, 339.51, 649.54, 0.00534, 5, 6, -72.61, 143.26, 0.22436, 5, 39.45, 140.75, 0.23275, 15, 146.97, -109.08, 0.52759, 42, -785.52, 266.43, 0.01496, 44, 364.02, 656.37, 0.00035, 4, 6, -48.18, 145.76, 0.30429, 5, 63.78, 144.06, 0.28629, 15, 147.49, -133.63, 0.39442, 42, -761.09, 268.93, 0.015, 4, 6, -19.94, 139.62, 0.38937, 5, 92.21, 138.87, 0.33643, 15, 139.09, -161.28, 0.26099, 42, -732.85, 262.79, 0.01321, 4, 6, 12.71, 149.46, 0.58585, 5, 124.51, 149.8, 0.32131, 15, 146.26, -194.62, 0.07961, 42, -700.2, 272.63, 0.01323, 3, 6, 49.1, 151.68, 0.74577, 5, 160.8, 153.23, 0.24323, 42, -663.81, 274.84, 0.011, 3, 6, 77.97, 147.66, 0.82768, 5, 189.79, 150.19, 0.16431, 42, -634.94, 270.83, 0.00801, 3, 6, 105.32, 138.53, 0.90242, 5, 217.43, 141.98, 0.09264, 42, -607.6, 261.7, 0.00494, 2, 6, 132.41, 124.21, 0.9782, 5, 244.99, 128.58, 0.0218, 1, 6, 166.16, 111.96, 1, 1, 6, 190.59, 95.94, 1, 2, 6, 189.87, 60.86, 0.97213, 42, -523.04, 184.03, 0.02787, 2, 6, 189.14, 25.78, 0.96, 42, -523.77, 148.95, 0.04, 4, 6, 18.95, 130.7, 0.54884, 5, 131.38, 131.27, 0.35314, 15, 127.06, -199.33, 0.07836, 42, -693.96, 253.87, 0.01966, 3, 6, 45.01, 108.74, 0.62224, 5, 158.15, 110.19, 0.35227, 42, -667.9, 231.91, 0.02549, 4, 6, 27.58, 100.67, 0.51335, 5, 141.01, 101.54, 0.39974, 15, 96.43, -205.51, 0.06176, 42, -685.33, 223.84, 0.02515, 4, 6, 3.54, 97.49, 0.37974, 5, 117.08, 97.55, 0.4256, 15, 95.2, -181.28, 0.17244, 42, -709.37, 220.66, 0.02222, 4, 6, -26.9, 107.63, 0.27264, 5, 86.32, 106.67, 0.40547, 15, 107.77, -151.76, 0.30542, 42, -739.81, 230.8, 0.01647, 5, 6, -57.87, 118.92, 0.21597, 5, 54.99, 116.92, 0.32183, 15, 121.53, -121.8, 0.44997, 42, -770.78, 242.09, 0.01177, 44, 373.45, 629.53, 0.00044, 6, 6, -86.68, 109.63, 0.10627, 5, 26.5, 106.67, 0.22932, 4, 474.56, 97.31, 0.16097, 15, 114.59, -92.33, 0.48938, 42, -799.59, 232.8, 0.00763, 44, 343.34, 626.34, 0.00642, 3, 6, 59.86, 126.21, 0.71062, 5, 172.41, 128.14, 0.26812, 42, -653.05, 249.38, 0.02126, 3, 6, 82.12, 110.08, 0.75584, 5, 195.2, 112.76, 0.22354, 42, -630.79, 233.25, 0.02062, 3, 6, 110.43, 87.47, 0.821, 5, 224.25, 91.12, 0.15098, 42, -602.48, 210.64, 0.02802, 3, 6, 139.66, 75.09, 0.91864, 5, 253.87, 79.72, 0.05274, 42, -573.25, 198.26, 0.02863, 2, 6, 166.72, 66.2, 0.97206, 42, -546.19, 189.37, 0.02794], "hull": 37, "edges": [4, 6, 6, 8, 20, 22, 28, 30, 30, 32, 34, 36, 36, 38, 48, 50, 54, 56, 56, 58, 66, 68, 62, 64, 64, 66, 2, 4, 0, 2, 72, 0, 58, 60, 60, 62, 50, 52, 52, 54, 46, 48, 42, 44, 44, 46, 38, 40, 40, 42, 8, 10, 10, 12, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 32, 34, 12, 14, 14, 16, 68, 70, 70, 72], "width": 128, "height": 371}}, "hat": {"hat": {"type": "mesh", "uvs": [0.65266, 1e-05, 0.96912, 0.21112, 0.99999, 0.59308, 0.92804, 0.70766, 0.93284, 0.74729, 0.92155, 0.77785, 0.97515, 0.84751, 0.98084, 0.89112, 0.94316, 0.94195, 0.80925, 0.96553, 0.67736, 0.97147, 0.55025, 0.96579, 0.42493, 0.94451, 0.30638, 0.90552, 0.23936, 0.85993, 0.16775, 0.99947, 0.14726, 0.84958, 0.06205, 0.77707, 1e-05, 0.67069, 0.26077, 0.17398, 0.15983, 0.74573, 0.2274, 0.61051, 0.36, 0.47856, 0.52977, 0.40709, 0.5915, 0.56887, 0.7042, 0.56783, 0.7589, 0.42307, 0.75045, 0.2224, 0.54268, 0.21415, 0.65345, 0.18267, 0.65091, 0.39434, 0.84893, 0.48399, 0.88408, 0.55905, 0.88681, 0.68267, 0.89389, 0.76207, 0.81761, 0.60467, 0.6495, 0.58382, 0.47547, 0.60602, 0.31845, 0.68402, 0.21272, 0.77494, 0.33702, 0.7754, 0.48244, 0.69853, 0.65749, 0.66085, 0.81217, 0.69099], "triangles": [0, 27, 29, 28, 19, 0, 29, 28, 0, 1, 27, 0, 30, 28, 29, 30, 29, 27, 28, 22, 19, 23, 28, 30, 31, 26, 27, 30, 27, 26, 23, 22, 28, 1, 31, 27, 32, 31, 1, 25, 30, 26, 24, 23, 30, 36, 24, 30, 25, 36, 30, 32, 1, 2, 35, 26, 31, 35, 31, 32, 25, 26, 35, 37, 22, 23, 37, 23, 24, 21, 19, 22, 42, 36, 25, 21, 18, 19, 3, 33, 32, 35, 32, 33, 38, 21, 22, 38, 22, 37, 43, 25, 35, 43, 35, 33, 42, 25, 43, 41, 37, 24, 38, 37, 41, 42, 41, 24, 42, 24, 36, 2, 3, 32, 20, 18, 21, 34, 33, 3, 34, 3, 4, 43, 33, 34, 39, 20, 21, 39, 21, 38, 40, 38, 41, 39, 38, 40, 17, 18, 20, 5, 34, 4, 16, 17, 20, 16, 20, 39, 14, 39, 40, 16, 39, 14, 5, 6, 7, 34, 5, 7, 13, 14, 40, 8, 34, 7, 43, 34, 8, 12, 40, 41, 13, 40, 12, 43, 10, 42, 9, 43, 8, 11, 41, 42, 10, 11, 42, 12, 41, 11, 43, 9, 10, 15, 16, 14], "vertices": [2, 6, 334.67, -48.88, 0.94578, 42, -378.24, 74.29, 0.05422, 2, 6, 286.28, -156.5, 0.95657, 42, -426.63, -33.33, 0.04343, 2, 6, 205, -164.43, 0.98783, 42, -507.92, -41.26, 0.01217, 2, 6, 181.55, -138.81, 0.99418, 42, -531.36, -15.64, 0.00582, 2, 6, 173.1, -140.18, 0.99353, 42, -539.81, -17.01, 0.00647, 2, 6, 166.75, -136.08, 0.99445, 42, -546.16, -12.91, 0.00555, 2, 6, 151.37, -154.06, 0.98567, 42, -561.54, -30.89, 0.01433, 2, 6, 142.07, -155.71, 0.97502, 42, -570.84, -32.54, 0.02498, 2, 6, 131.73, -142.36, 0.95997, 42, -581.18, -19.19, 0.04003, 2, 6, 128.28, -96.02, 0.94199, 42, -584.63, 27.15, 0.05801, 2, 6, 128.55, -50.5, 0.94086, 42, -584.36, 72.67, 0.05914, 2, 6, 131.22, -6.71, 0.94116, 42, -581.69, 116.46, 0.05884, 2, 6, 137.18, 36.35, 0.95103, 42, -575.74, 159.51, 0.04897, 2, 6, 146.81, 76.95, 0.97163, 42, -566.1, 200.12, 0.02837, 2, 6, 157.24, 99.73, 0.98374, 42, -555.67, 222.9, 0.01626, 1, 6, 128.5, 125.42, 1, 1, 6, 160.49, 131.41, 1, 2, 6, 176.84, 160.28, 0.99007, 43, -457.3, 283.45, 0.00993, 2, 6, 200.1, 180.92, 0.98177, 43, -434.04, 304.09, 0.01823, 2, 6, 302.33, 87.48, 0.96817, 42, -410.58, 210.65, 0.03183, 2, 6, 182.35, 126.35, 0.993, 42, -530.56, 249.51, 0.007, 2, 6, 210.22, 102.09, 0.98057, 42, -502.69, 225.26, 0.01943, 2, 6, 236.65, 55.43, 0.96611, 42, -476.26, 178.6, 0.03389, 2, 6, 249.83, -3.61, 0.95712, 42, -463.08, 119.55, 0.04288, 2, 6, 214.84, -23.75, 0.95931, 42, -498.07, 99.42, 0.04069, 2, 6, 213.76, -62.62, 0.95947, 42, -499.15, 60.55, 0.04053, 2, 6, 243.8, -82.51, 0.95718, 42, -469.11, 40.66, 0.04282, 2, 6, 286.42, -81.02, 0.9524, 42, -426.49, 42.15, 0.0476, 2, 6, 290.56, -9.44, 0.95164, 42, -422.35, 113.73, 0.04836, 2, 6, 295.96, -47.85, 0.94955, 42, -416.96, 75.32, 0.05045, 2, 6, 251.14, -45.48, 0.95148, 42, -461.77, 77.69, 0.04852, 2, 6, 229.86, -113.12, 0.96711, 42, -483.06, 10.05, 0.03289, 2, 6, 213.54, -124.7, 0.97883, 42, -499.37, -1.54, 0.02117, 2, 6, 187.32, -124.77, 0.98468, 42, -525.59, -1.6, 0.01532, 2, 6, 170.42, -126.65, 0.98608, 42, -542.49, -3.48, 0.01392, 2, 6, 204.65, -101.46, 0.96759, 42, -508.26, 21.71, 0.03241, 2, 6, 211.01, -43.65, 0.95893, 42, -501.9, 79.52, 0.04107, 2, 6, 208.31, 16.52, 0.96235, 42, -504.6, 139.69, 0.03765, 2, 6, 193.6, 71.21, 0.96888, 42, -519.31, 194.38, 0.03112, 2, 6, 175.56, 108.31, 0.98597, 42, -537.36, 231.48, 0.01403, 2, 6, 174.02, 65.46, 0.96723, 42, -538.89, 188.63, 0.03277, 2, 6, 188.63, 14.77, 0.95975, 42, -524.28, 137.94, 0.04025, 2, 6, 194.59, -45.85, 0.95729, 42, -518.32, 77.31, 0.04271, 2, 6, 186.42, -98.97, 0.96639, 42, -526.49, 24.19, 0.03361], "hull": 20, "edges": [2, 4, 4, 6, 26, 28, 36, 38, 0, 2, 6, 8, 8, 10, 10, 12, 30, 32, 32, 34, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 38, 0, 34, 36, 28, 30, 40, 42, 42, 44, 44, 46, 52, 54, 46, 56, 0, 58, 58, 60, 62, 64, 62, 52, 64, 66, 66, 68, 66, 70, 70, 50, 48, 72, 72, 50, 48, 74, 74, 76, 76, 78, 28, 80, 80, 82, 82, 84, 84, 86, 86, 68, 86, 16, 68, 14], "width": 345, "height": 212}}, "head": {"head": {"type": "mesh", "uvs": [0.03287, 0.09265, 0.05721, 0.0043, 0.20951, 0.00407, 0.3548, 0.00386, 0.53337, 0.00359, 0.67864, 0.00337, 0.80188, 0.00319, 0.92512, 0.003, 0.95531, 0.10202, 0.98018, 0.21577, 0.98543, 0.30506, 0.98326, 0.37923, 0.99306, 0.46979, 0.99588, 0.567, 0.9752, 0.62396, 0.94224, 0.68221, 0.89761, 0.80902, 0.84527, 0.86391, 0.71711, 0.96331, 0.64649, 0.99383, 0.54564, 0.99542, 0.43257, 0.96437, 0.29757, 0.90674, 0.1632, 0.82319, 0.11263, 0.77342, 0.06177, 0.66539, 0.02156, 0.5586, 0.00597, 0.45676, 0.00471, 0.36866, 0.00655, 0.29656, 0.01321, 0.20736, 0.19067, 0.30258, 0.53694, 0.31746, 0.53299, 0.28371, 0.33373, 0.22997, 0.28654, 0.23135, 0.23385, 0.25197, 0.24234, 0.28404, 0.28566, 0.26511, 0.31363, 0.2658, 0.51289, 0.3333, 0.43116, 0.25857, 0.41326, 0.30093, 0.36344, 0.28267, 0.38354, 0.24341, 0.48097, 0.27201, 0.46394, 0.31712, 0.18915, 0.28479, 0.68705, 0.32622, 0.70173, 0.2892, 0.88077, 0.23599, 0.92773, 0.24062, 0.95855, 0.26433, 0.95194, 0.2892, 0.92113, 0.27416, 0.89471, 0.27532, 0.71787, 0.33374, 0.78978, 0.26086, 0.80629, 0.30453, 0.74502, 0.27416, 0.76282, 0.32029, 0.84977, 0.28819, 0.83527, 0.24698, 0.20529, 0.3838, 0.24386, 0.3552, 0.29077, 0.33931, 0.3443, 0.33234, 0.39721, 0.33952, 0.44366, 0.35951, 0.48799, 0.402, 0.50763, 0.42613, 0.22542, 0.39649, 0.25442, 0.36978, 0.29728, 0.35489, 0.34596, 0.34802, 0.38737, 0.35406, 0.42515, 0.36916, 0.47482, 0.40992, 0.48883, 0.42893, 0.24757, 0.4133, 0.28502, 0.43064, 0.33198, 0.4386, 0.3825, 0.4372, 0.43065, 0.43298, 0.46334, 0.43251, 0.45207, 0.38837, 0.20334, 0.42111, 0.26162, 0.454, 0.34004, 0.46647, 0.42134, 0.4574, 0.49688, 0.44606, 0.69007, 0.43272, 0.70927, 0.41147, 0.73573, 0.39, 0.77152, 0.36994, 0.84257, 0.35825, 0.87982, 0.36668, 0.90741, 0.38299, 0.92776, 0.40256, 0.7191, 0.43398, 0.80518, 0.36104, 0.75255, 0.4369, 0.79394, 0.44098, 0.84016, 0.44125, 0.87982, 0.43527, 0.91189, 0.42004, 0.67936, 0.43034, 0.70328, 0.40681, 0.73299, 0.38011, 0.76464, 0.3591, 0.80174, 0.34562, 0.84583, 0.34325, 0.46832, 0.38163, 0.8873, 0.34973, 0.92894, 0.37161, 0.95614, 0.39174, 0.68337, 0.44551, 0.75517, 0.45895, 0.8361, 0.46658, 0.91259, 0.45345, 0.95947, 0.42219, 0.64661, 0.39994, 0.64205, 0.44027, 0.66123, 0.495, 0.66763, 0.53821, 0.69321, 0.57134, 0.64387, 0.6059, 0.6414, 0.64809, 0.69047, 0.65343, 0.72244, 0.62607, 0.72062, 0.5843, 0.69595, 0.54973, 0.69047, 0.59762, 0.56868, 0.5915, 0.56861, 0.56269, 0.59591, 0.53461, 0.58874, 0.4914, 0.58506, 0.43523, 0.57409, 0.39706, 0.53483, 0.5458, 0.50011, 0.58541, 0.49828, 0.63222, 0.53574, 0.6567, 0.63986, 0.57221, 0.63466, 0.52962, 0.62739, 0.48949, 0.61596, 0.43379, 0.5363, 0.12742, 0.68611, 0.131, 0.83593, 0.11669, 0.34702, 0.10595, 0.18813, 0.1143, 0.13082, 0.22279, 0.09753, 0.30133, 0.09904, 0.38364, 0.10946, 0.49408, 0.14152, 0.61554, 0.25386, 0.55641, 0.23208, 0.74382, 0.32262, 0.83762, 0.43392, 0.90369, 0.54883, 0.93582, 0.65022, 0.93582, 0.39083, 0.55929, 0.35783, 0.68173, 0.5475, 0.85973, 0.65559, 0.86196, 0.73301, 0.90508, 0.82538, 0.83245, 0.87843, 0.75195, 0.8272, 0.55773, 0.94391, 0.55668, 0.91606, 0.65181, 0.61914, 0.7161, 0.5725, 0.70712, 0.5364, 0.72274, 0.49607, 0.74331, 0.46218, 0.7619, 0.4431, 0.75796, 0.43032, 0.74364, 0.4061, 0.7622, 0.42521, 0.79014, 0.44239, 0.77924, 0.45667, 0.78743, 0.47899, 0.81848, 0.53215, 0.84319, 0.60801, 0.83997, 0.66889, 0.84247, 0.71123, 0.82821, 0.73767, 0.80462, 0.7502, 0.77939, 0.76741, 0.78512, 0.76423, 0.7493, 0.75159, 0.76294, 0.72819, 0.74135, 0.69175, 0.71687, 0.65765, 0.7026, 0.43568, 0.76933, 0.46827, 0.77519, 0.51087, 0.77197, 0.56216, 0.75061, 0.55111, 0.7689, 0.61628, 0.75105, 0.66654, 0.75018, 0.70465, 0.76846, 0.66102, 0.76802, 0.60966, 0.77238, 0.73194, 0.77293, 0.75717, 0.77192, 0.77772, 0.7688, 0.59892, 0.64585, 0.81031, 0.68018], "triangles": [23, 24, 158, 22, 23, 159, 23, 158, 159, 21, 160, 161, 21, 22, 160, 22, 159, 160, 159, 158, 180, 184, 181, 183, 183, 181, 182, 181, 184, 160, 181, 197, 182, 197, 178, 177, 160, 165, 161, 162, 161, 165, 160, 185, 165, 160, 184, 185, 184, 199, 185, 184, 198, 199, 165, 185, 186, 185, 201, 186, 166, 187, 167, 166, 186, 187, 188, 205, 204, 189, 188, 204, 18, 167, 17, 189, 190, 191, 190, 208, 191, 191, 208, 209, 190, 207, 208, 207, 193, 208, 208, 193, 209, 168, 169, 16, 114, 53, 10, 52, 9, 10, 50, 8, 9, 44, 150, 147, 147, 4, 5, 150, 2, 3, 0, 1, 151, 154, 153, 31, 47, 153, 152, 152, 153, 30, 151, 152, 0, 152, 30, 0, 153, 29, 30, 28, 153, 154, 24, 25, 158, 27, 154, 155, 63, 154, 31, 26, 155, 156, 155, 154, 86, 68, 67, 46, 67, 75, 66, 25, 156, 158, 156, 155, 157, 86, 154, 63, 63, 64, 72, 80, 72, 73, 71, 63, 72, 73, 72, 65, 73, 65, 66, 87, 155, 86, 86, 71, 79, 86, 79, 87, 79, 72, 80, 112, 68, 40, 76, 68, 85, 68, 76, 67, 76, 75, 67, 157, 155, 87, 87, 80, 88, 87, 79, 80, 80, 73, 81, 156, 157, 164, 157, 87, 88, 178, 197, 179, 197, 180, 179, 177, 178, 179, 69, 112, 40, 85, 68, 112, 88, 81, 82, 82, 75, 76, 82, 74, 75, 81, 74, 82, 82, 76, 83, 77, 83, 85, 88, 82, 89, 89, 82, 83, 163, 88, 89, 90, 89, 84, 89, 83, 84, 84, 77, 78, 164, 157, 163, 157, 88, 163, 201, 200, 206, 201, 199, 200, 200, 199, 176, 90, 84, 78, 70, 78, 69, 206, 202, 205, 206, 200, 202, 176, 179, 141, 141, 179, 164, 164, 163, 141, 209, 192, 169, 193, 194, 192, 176, 142, 175, 176, 141, 142, 175, 142, 174, 141, 163, 140, 136, 137, 146, 137, 138, 146, 122, 146, 121, 146, 138, 121, 121, 48, 108, 139, 90, 136, 173, 127, 196, 173, 210, 127, 134, 139, 135, 139, 136, 135, 135, 136, 145, 145, 122, 123, 136, 146, 145, 108, 107, 121, 108, 109, 94, 111, 61, 113, 110, 58, 111, 144, 123, 124, 117, 123, 116, 123, 122, 116, 116, 91, 99, 143, 124, 125, 123, 131, 124, 117, 116, 99, 101, 99, 92, 113, 96, 111, 96, 95, 111, 128, 132, 129, 126, 143, 132, 132, 143, 125, 132, 125, 130, 125, 124, 131, 99, 101, 117, 101, 94, 102, 97, 113, 114, 11, 114, 10, 119, 120, 12, 120, 11, 12, 119, 105, 120, 104, 97, 105, 104, 96, 97, 105, 98, 120, 98, 115, 120, 120, 115, 11, 105, 97, 98, 98, 114, 115, 98, 97, 114, 115, 114, 11, 96, 113, 97, 16, 169, 15, 192, 211, 169, 15, 211, 172, 15, 169, 211, 194, 195, 211, 211, 128, 129, 211, 195, 128, 15, 172, 14, 172, 211, 170, 211, 129, 170, 172, 171, 14, 172, 170, 171, 132, 130, 129, 129, 130, 170, 14, 171, 13, 130, 131, 170, 171, 12, 13, 170, 118, 171, 118, 119, 171, 131, 117, 170, 170, 117, 118, 171, 119, 12, 118, 104, 119, 118, 103, 104, 117, 102, 118, 118, 102, 103, 117, 101, 102, 104, 105, 119, 96, 104, 103, 96, 103, 95, 103, 100, 95, 103, 102, 100, 125, 131, 130, 117, 131, 123, 95, 110, 111, 114, 113, 53, 102, 94, 100, 100, 110, 95, 116, 106, 91, 101, 92, 93, 101, 93, 94, 91, 92, 99, 92, 91, 107, 92, 107, 93, 93, 108, 94, 94, 109, 100, 122, 106, 116, 91, 106, 107, 106, 121, 107, 107, 108, 93, 108, 56, 109, 109, 110, 100, 109, 60, 110, 196, 128, 195, 196, 127, 128, 127, 126, 128, 126, 132, 128, 133, 143, 126, 133, 134, 143, 134, 135, 143, 135, 144, 143, 143, 144, 124, 135, 145, 144, 144, 145, 123, 127, 210, 126, 210, 133, 126, 145, 146, 122, 106, 122, 121, 173, 196, 203, 203, 195, 194, 203, 196, 195, 174, 210, 173, 174, 142, 210, 142, 141, 133, 133, 141, 140, 142, 133, 210, 134, 140, 139, 140, 134, 133, 140, 163, 139, 139, 163, 89, 90, 137, 136, 193, 207, 194, 207, 204, 194, 204, 203, 194, 192, 194, 211, 202, 173, 203, 202, 200, 173, 200, 174, 173, 200, 175, 174, 205, 203, 204, 205, 202, 203, 70, 138, 137, 121, 138, 48, 139, 89, 90, 137, 90, 70, 90, 78, 70, 70, 69, 138, 200, 176, 175, 84, 83, 77, 78, 77, 69, 77, 112, 69, 138, 69, 40, 199, 198, 176, 198, 177, 176, 176, 177, 179, 158, 156, 164, 80, 81, 88, 158, 164, 180, 83, 76, 85, 85, 112, 77, 180, 164, 179, 81, 73, 74, 86, 63, 71, 79, 71, 72, 74, 73, 66, 75, 74, 66, 64, 63, 31, 72, 64, 65, 64, 37, 65, 65, 39, 66, 67, 66, 43, 25, 26, 156, 26, 27, 155, 27, 28, 154, 28, 29, 153, 151, 36, 152, 151, 2, 150, 151, 1, 2, 150, 35, 151, 4, 150, 3, 150, 4, 147, 147, 5, 148, 148, 5, 6, 33, 147, 148, 148, 6, 149, 149, 6, 7, 149, 57, 148, 149, 7, 8, 50, 149, 8, 167, 168, 17, 168, 188, 189, 189, 191, 168, 168, 167, 188, 17, 168, 16, 191, 209, 168, 168, 209, 169, 193, 192, 209, 189, 204, 207, 189, 207, 190, 187, 188, 167, 187, 186, 205, 186, 206, 205, 188, 187, 205, 18, 162, 167, 162, 166, 167, 166, 162, 165, 165, 186, 166, 186, 201, 206, 185, 199, 201, 183, 198, 184, 183, 182, 198, 182, 197, 198, 198, 197, 177, 159, 181, 160, 159, 180, 181, 181, 180, 197, 20, 161, 19, 20, 21, 161, 19, 161, 162, 19, 162, 18, 31, 37, 64, 37, 38, 65, 65, 38, 39, 153, 47, 31, 31, 47, 37, 47, 36, 37, 47, 152, 36, 37, 36, 38, 38, 35, 39, 38, 36, 35, 35, 36, 151, 42, 67, 43, 66, 39, 43, 42, 41, 45, 42, 43, 41, 39, 34, 43, 43, 44, 41, 43, 34, 44, 45, 41, 147, 39, 35, 34, 41, 44, 147, 34, 150, 44, 150, 34, 35, 40, 32, 138, 68, 46, 40, 40, 46, 33, 40, 33, 32, 33, 46, 45, 48, 32, 33, 46, 42, 45, 33, 45, 147, 67, 42, 46, 138, 32, 48, 108, 48, 56, 56, 60, 109, 110, 60, 58, 60, 56, 49, 56, 48, 49, 49, 48, 33, 49, 59, 60, 60, 59, 58, 59, 49, 148, 49, 33, 148, 54, 113, 55, 113, 61, 55, 111, 58, 61, 59, 57, 58, 58, 57, 61, 57, 62, 61, 61, 62, 55, 62, 50, 55, 54, 55, 51, 51, 55, 50, 62, 57, 149, 62, 149, 50, 51, 50, 9, 59, 148, 57, 54, 53, 113, 53, 52, 10, 53, 54, 52, 54, 51, 52, 52, 51, 9], "vertices": [2, 6, 168.24, 79.33, 0.98873, 42, -544.67, 202.49, 0.01127, 2, 6, 190.59, 73.68, 0.98752, 42, -522.32, 196.85, 0.01248, 2, 6, 189.63, 43.09, 0.96873, 42, -523.28, 166.25, 0.03127, 2, 6, 188.71, 13.9, 0.96, 42, -524.2, 137.07, 0.04, 2, 6, 187.57, -21.98, 0.96, 42, -525.34, 101.19, 0.04, 2, 6, 186.65, -51.16, 0.96, 42, -526.26, 72.01, 0.04, 2, 6, 185.87, -75.92, 0.96901, 42, -527.04, 47.25, 0.03099, 2, 6, 185.09, -100.68, 0.9855, 42, -527.82, 22.49, 0.0145, 2, 6, 159.65, -105.9, 0.98668, 42, -553.26, 17.27, 0.01332, 2, 6, 130.49, -109.93, 0.98718, 42, -582.42, 13.24, 0.01282, 2, 6, 107.7, -110.22, 0.98751, 42, -605.21, 12.95, 0.01249, 2, 6, 88.81, -109.15, 0.98778, 42, -624.1, 14.02, 0.01222, 2, 6, 65.66, -110.35, 0.98692, 42, -647.25, 12.82, 0.01308, 2, 6, 40.87, -110.08, 0.98441, 42, -672.04, 13.09, 0.01559, 2, 6, 26.49, -105.44, 0.9833, 42, -686.42, 17.73, 0.0167, 2, 6, 11.87, -98.32, 0.98259, 42, -701.04, 24.85, 0.01741, 2, 6, -20.15, -88.27, 0.98209, 42, -733.06, 34.89, 0.01791, 2, 6, -33.78, -77.29, 0.97799, 42, -746.69, 45.88, 0.02201, 2, 6, -58.26, -50.7, 0.96648, 42, -771.17, 72.47, 0.03352, 2, 6, -65.56, -36.25, 0.96304, 42, -778.47, 86.92, 0.03696, 2, 6, -65.29, -15.98, 0.96321, 42, -778.2, 107.19, 0.03679, 2, 6, -56.61, 6.47, 0.96795, 42, -769.52, 129.64, 0.03205, 2, 6, -41.01, 33.1, 0.97798, 42, -753.93, 156.27, 0.02202, 2, 6, -18.82, 59.38, 0.9879, 42, -731.73, 182.55, 0.0121, 2, 6, -5.79, 69.11, 0.98916, 42, -718.71, 192.28, 0.01084, 2, 6, 22.08, 78.41, 0.98946, 42, -690.83, 201.58, 0.01054, 2, 6, 49.57, 85.57, 0.98903, 42, -663.34, 208.74, 0.01097, 2, 6, 75.63, 87.84, 0.98944, 42, -637.28, 211.01, 0.01056, 2, 6, 98.09, 87.34, 0.98944, 42, -614.82, 210.51, 0.01056, 2, 6, 116.45, 86.35, 0.9897, 42, -596.46, 209.52, 0.0103, 2, 6, 139.14, 84.25, 0.98973, 42, -573.77, 207.42, 0.01027, 5, 7, 114.43, 108.54, 0.00023, 9, 109.03, -45.79, 0.00073, 12, -47.4, -19.72, 0.00401, 11, -16.18, -21.36, 0.00569, 10, -1.86, -3.54, 0.98934, 4, 7, 60.66, 64.18, 0.00091, 9, 47.39, -13.24, 0.05359, 12, 19.82, -1.31, 0.94354, 10, 59.72, -36.2, 0.00197, 4, 8, 77.3, -14.17, 0.00927, 9, 44.51, -21.39, 0.06532, 12, 16.35, 6.61, 0.89117, 11, 46.61, 7.18, 0.03424, 5, 8, 112.13, -38.21, 0.00299, 9, 75.16, -50.59, 0.00033, 12, -25.98, 6.94, 0.00533, 11, 4.3, 6.03, 0.93117, 10, 32.01, 1.19, 0.06017, 4, 8, 121.36, -40.45, 0.00075, 12, -34.87, 3.61, 0.0001, 11, -4.47, 2.39, 0.25409, 10, 23.25, 4.86, 0.74506, 2, 8, 132.98, -38.26, 2e-05, 10, 11.44, 4.53, 0.99998, 5, 7, 109.99, 98.02, 0.00012, 9, 97.61, -45.74, 0.0002, 12, -39.05, -11.95, 0.00264, 11, -8.1, -13.3, 0.0282, 10, 9.55, -3.61, 0.96885, 4, 7, 106.86, 88.57, 4e-05, 12, -32.31, -4.61, 0.0006, 11, -1.63, -5.74, 0.11824, 10, 19.48, -2.88, 0.88112, 4, 7, 102.6, 84.89, 5e-05, 12, -26.92, -3, 0.00011, 11, 3.7, -3.94, 0.69668, 10, 24.51, -5.4, 0.30317, 5, 7, 61.48, 70.42, 0.00283, 9, 53.47, -11.59, 0.04044, 12, 16.52, -6.67, 0.94455, 11, 47.24, -6.09, 0.00088, 10, 53.64, -37.84, 0.0113, 4, 8, 95.26, -25.89, 0.00519, 9, 60.42, -35.77, 0.00285, 12, -5.09, 6.22, 0.16468, 11, 25.2, 6.03, 0.82727, 5, 7, 81.8, 77.92, 0.00048, 9, 68.21, -27.47, 5e-05, 12, -5.09, -5.17, 0.13768, 11, 25.59, -5.34, 0.83729, 10, 38.93, -21.94, 0.02449, 4, 7, 92.32, 81.28, 0.00017, 12, -16.06, -3.92, 0.00232, 11, 14.59, -4.48, 0.9482, 10, 31.79, -13.51, 0.04931, 4, 8, 103.42, -32.2, 0.00443, 9, 67.5, -43.29, 0.00134, 12, -15.4, 6.86, 0.02542, 11, 14.88, 6.32, 0.96881, 4, 8, 86.55, -19.88, 0.00676, 9, 52.76, -28.47, 0.00925, 12, 5.49, 6.13, 0.79186, 11, 35.77, 6.32, 0.19214, 5, 7, 71.51, 74.05, 0.00107, 9, 60.68, -19.46, 0.00214, 12, 5.88, -5.86, 0.90512, 11, 36.58, -5.65, 0.07731, 10, 46.44, -29.96, 0.01436, 1, 10, -0.23, 0.71, 1, 5, 7, 36.96, 45.38, 0.00316, 9, 20.91, 1.4, 0.97506, 12, 49.16, 6.12, 0.01157, 10, 86.17, -50.89, 0.00031, 42, -608.6, 73.07, 0.0099, 5, 8, 45.03, -3.63, 0.02371, 9, 14.29, -5.94, 0.91706, 12, 48.97, 16.01, 0.0433, 11, 78.89, 17.7, 0.00617, 42, -599.26, 69.81, 0.00976, 5, 7, 23.9, 2.08, 0.02377, 8, 6.71, -6.94, 0.9642, 12, 78.82, 40.26, 0.00302, 11, 107.87, 42.98, 0.004, 42, -586.91, 33.39, 0.005, 5, 7, 16.16, -3.45, 0.55903, 8, -2.06, -3.24, 0.43868, 12, 88.15, 42.12, 0.00057, 11, 117.13, 45.17, 0.00123, 42, -588.4, 23.99, 0.00049, 3, 7, 7.51, -3.2, 0.98591, 11, 125.04, 41.67, 3e-05, 43, -515.88, 18, 0.01406, 4, 7, 4.19, 2.36, 0.94287, 8, -3.38, 10, 0.04313, 9, -31.4, 15.09, 0.00381, 43, -522.17, 19.54, 0.01019, 4, 7, 11.34, 3.74, 0.60854, 8, 1.54, 4.63, 0.388, 9, -27.37, 9.02, 0.00245, 42, -596.91, 25.6, 0.00101, 4, 7, 15.05, 7.55, 0.16274, 8, 6.73, 3.48, 0.82867, 9, -22.43, 7.07, 0.0035, 42, -597.02, 30.92, 0.00509, 4, 7, 31.1, 42.59, 0.01738, 9, 16.09, 5.74, 0.97162, 10, 90.99, -55.23, 0.00064, 42, -610.72, 66.94, 0.01036, 5, 8, 26.03, -5.79, 0.82003, 9, -4.81, -5.1, 0.15556, 12, 63.47, 28.46, 0.00841, 11, 92.95, 30.65, 0.00537, 42, -592.63, 51.88, 0.01063, 4, 7, 23.07, 25.07, 0.0383, 8, 25.86, 5.83, 0.70659, 9, -3.17, 6.4, 0.24547, 42, -603.87, 48.93, 0.00963, 5, 8, 35.61, -4.96, 0.16215, 9, 4.78, -5.78, 0.80386, 12, 56.01, 22.4, 0.01793, 11, 85.7, 24.33, 0.00642, 42, -595.72, 60.98, 0.00964, 5, 7, 26.78, 33.95, 0.02779, 8, 35.36, 7.33, 0.07123, 9, 6.45, 6.4, 0.89075, 10, 100.62, -55.91, 0.00013, 42, -607.6, 57.8, 0.0101, 4, 7, 19.47, 16.09, 0.05587, 8, 16.32, 4.19, 0.92086, 9, -12.85, 6.27, 0.01577, 42, -600, 40.06, 0.0075, 5, 8, 16.27, -6.72, 0.97627, 9, -14.6, -4.49, 0.00584, 12, 71.03, 34.71, 0.00506, 11, 100.28, 37.16, 0.00494, 42, -589.4, 42.62, 0.00789, 2, 6, 92.88, 47.17, 0.961, 42, -620.03, 170.34, 0.039, 2, 6, 99.91, 39.18, 0.96, 42, -613, 162.35, 0.04, 2, 6, 103.64, 29.62, 0.96, 42, -609.27, 152.79, 0.04, 2, 6, 105.06, 18.81, 0.96, 42, -607.85, 141.98, 0.04, 2, 6, 102.87, 8.24, 0.96, 42, -610.04, 131.41, 0.04, 2, 6, 97.47, -0.92, 0.96, 42, -615.44, 122.25, 0.04, 2, 6, 86.34, -9.46, 0.96, 42, -626.57, 113.71, 0.04, 2, 6, 80.06, -13.2, 0.96, 42, -632.85, 109.97, 0.04, 2, 6, 89.51, 43.24, 0.96, 42, -623.4, 166.41, 0.04, 2, 6, 96.12, 37.18, 0.96, 42, -616.79, 160.35, 0.04, 2, 6, 99.63, 28.45, 0.96, 42, -613.28, 151.62, 0.04, 2, 6, 101.05, 18.61, 0.96, 42, -611.86, 141.78, 0.04, 2, 6, 99.23, 10.34, 0.96, 42, -613.68, 133.51, 0.04, 2, 6, 95.13, 2.88, 0.96, 42, -617.78, 126.05, 0.04, 2, 6, 84.41, -6.75, 0.96, 42, -628.5, 116.42, 0.04, 2, 6, 79.47, -9.4, 0.96, 42, -633.44, 113.77, 0.04, 2, 6, 85.08, 38.93, 0.96, 42, -627.83, 162.1, 0.04, 2, 6, 80.41, 31.56, 0.96, 42, -632.5, 154.73, 0.04, 2, 6, 78.06, 22.19, 0.96, 42, -634.85, 145.36, 0.04, 2, 6, 78.08, 12.03, 0.96, 42, -634.83, 135.2, 0.04, 2, 6, 78.83, 2.32, 0.96, 42, -634.08, 125.49, 0.04, 2, 6, 78.73, -4.25, 0.96, 42, -634.18, 118.92, 0.04, 2, 6, 90.06, -2.36, 0.96, 42, -622.85, 120.81, 0.04, 2, 6, 83.39, 47.88, 0.96215, 42, -629.52, 171.05, 0.03785, 2, 6, 74.61, 36.46, 0.96, 42, -638.3, 159.63, 0.04, 2, 6, 70.9, 20.81, 0.96, 42, -642.01, 143.98, 0.04, 2, 6, 72.67, 4.4, 0.96, 42, -640.24, 127.57, 0.04, 2, 6, 75.05, -10.87, 0.96, 42, -637.86, 112.3, 0.04, 2, 6, 77.15, -49.79, 0.96, 42, -635.76, 73.37, 0.04, 2, 6, 82.44, -53.83, 0.96, 42, -630.47, 69.34, 0.04, 2, 6, 87.73, -59.33, 0.96, 42, -625.18, 63.84, 0.04, 2, 6, 92.6, -66.69, 0.96, 42, -620.31, 56.48, 0.04, 2, 6, 95.1, -81.07, 0.96, 42, -617.81, 42.1, 0.04, 2, 6, 92.71, -88.48, 0.96276, 42, -620.2, 34.69, 0.03724, 2, 6, 88.36, -93.88, 0.96822, 42, -624.55, 29.29, 0.03178, 2, 6, 83.24, -97.8, 0.9724, 42, -629.67, 25.37, 0.0276, 2, 6, 76.63, -55.62, 0.96, 42, -636.28, 67.55, 0.04, 2, 6, 94.65, -73.53, 0.96, 42, -618.26, 49.64, 0.04, 2, 6, 75.67, -62.31, 0.96, 42, -637.24, 60.86, 0.04, 2, 6, 74.35, -70.59, 0.96, 42, -638.56, 52.58, 0.04, 2, 6, 73.97, -79.87, 0.96, 42, -638.94, 43.3, 0.04, 2, 6, 75.22, -87.89, 0.96301, 42, -637.69, 35.28, 0.03699, 2, 6, 78.89, -94.46, 0.96929, 42, -634.02, 28.7, 0.03071, 2, 6, 77.83, -47.66, 0.96, 42, -635.08, 75.5, 0.04, 2, 6, 83.66, -52.67, 0.96, 42, -629.25, 70.5, 0.04, 2, 6, 90.27, -58.87, 0.96, 42, -622.64, 64.3, 0.04, 2, 6, 95.41, -65.4, 0.96, 42, -617.5, 57.77, 0.04, 2, 6, 98.6, -72.97, 0.96, 42, -614.31, 50.2, 0.04, 2, 6, 98.9, -81.85, 0.96, 42, -614.01, 41.32, 0.04, 2, 6, 91.66, -5.68, 0.96, 42, -621.25, 117.48, 0.04, 2, 6, 96.97, -90.12, 0.96357, 42, -615.94, 33.05, 0.03643, 2, 6, 91.12, -98.3, 0.96977, 42, -621.79, 24.87, 0.03023, 2, 6, 85.8, -103.59, 0.97838, 42, -627.11, 19.57, 0.02162, 2, 6, 73.94, -48.34, 0.96, 42, -638.97, 74.83, 0.04, 2, 6, 70.03, -62.65, 0.96, 42, -642.88, 60.52, 0.04, 2, 6, 67.54, -78.84, 0.96, 42, -645.37, 44.33, 0.04, 2, 6, 70.37, -94.32, 0.96897, 42, -642.54, 28.85, 0.03103, 2, 6, 78.02, -104, 0.97926, 42, -634.89, 19.17, 0.02074, 2, 6, 85.8, -41.34, 0.959, 42, -627.11, 81.82, 0.041, 2, 6, 75.55, -40.08, 0.958, 42, -637.36, 83.09, 0.042, 2, 6, 61.47, -43.47, 0.956, 42, -651.44, 79.7, 0.044, 2, 6, 50.42, -44.39, 0.955, 42, -662.49, 78.78, 0.045, 2, 6, 41.8, -49.24, 0.955, 42, -671.11, 73.93, 0.045, 2, 6, 33.32, -39.04, 0.955, 42, -679.59, 84.13, 0.045, 2, 6, 22.59, -38.18, 0.96, 42, -690.32, 84.99, 0.04, 2, 6, 20.9, -47.99, 0.96, 42, -692.01, 75.18, 0.04, 2, 6, 27.66, -54.65, 0.96, 42, -685.25, 68.52, 0.04, 2, 6, 38.31, -54.64, 0.96, 42, -674.6, 68.53, 0.04, 2, 6, 47.29, -49.98, 0.96, 42, -665.62, 73.19, 0.04, 2, 6, 35.12, -48.47, 0.955, 42, -677.79, 74.7, 0.045, 2, 6, 37.5, -24.05, 0.955, 42, -675.41, 99.12, 0.045, 2, 6, 44.84, -24.29, 0.955, 42, -668.07, 98.88, 0.045, 2, 6, 51.82, -30.01, 0.955, 42, -661.09, 93.16, 0.045, 2, 6, 62.88, -28.94, 0.956, 42, -650.03, 94.23, 0.044, 2, 6, 77.22, -28.68, 0.958, 42, -635.69, 94.49, 0.042, 2, 6, 87.02, -26.8, 0.959, 42, -625.89, 96.37, 0.041, 2, 6, 49.38, -17.64, 0.96, 42, -663.53, 105.53, 0.04, 2, 6, 39.52, -10.33, 0.96, 42, -673.4, 112.84, 0.04, 2, 6, 27.6, -9.56, 0.96, 42, -685.31, 113.6, 0.04, 2, 6, 21.11, -16.88, 0.96, 42, -691.8, 106.29, 0.04, 2, 6, 41.94, -38.52, 0.952, 42, -670.97, 84.65, 0.048, 2, 6, 52.83, -37.84, 0.953, 42, -660.08, 85.33, 0.047, 2, 6, 63.11, -36.72, 0.954, 42, -649.81, 86.45, 0.046, 2, 6, 77.38, -34.9, 0.957, 42, -635.53, 88.27, 0.043, 2, 6, 155.99, -21.51, 0.96, 42, -556.92, 101.66, 0.04, 2, 6, 154.07, -51.57, 0.96, 42, -558.84, 71.59, 0.04, 2, 6, 156.71, -81.79, 0.9692, 42, -556.2, 41.38, 0.0308, 2, 6, 162.74, 16.33, 0.96, 42, -550.17, 139.5, 0.04, 2, 6, 161.68, 48.32, 0.96611, 42, -551.23, 171.49, 0.03389, 2, 6, 134.42, 60.76, 0.96733, 42, -578.49, 183.93, 0.03267, 2, 6, 114.62, 68.12, 0.96949, 42, -598.29, 191.29, 0.03051, 2, 6, 93.64, 68.52, 0.9688, 42, -619.27, 191.69, 0.0312, 2, 6, 65.42, 67.37, 0.96733, 42, -647.49, 190.54, 0.03267, 2, 6, 34.25, 61.96, 0.96947, 42, -678.66, 185.13, 0.03053, 2, 6, 48.56, 38.89, 0.96, 42, -664.35, 162.06, 0.04, 2, 6, 0.95, 44.87, 0.97189, 42, -711.96, 168.04, 0.02811, 2, 6, -23.57, 27.48, 0.96723, 42, -736.48, 150.65, 0.03277, 2, 6, -41.15, 5.68, 0.96285, 42, -754.07, 128.85, 0.03715, 2, 6, -50.12, -17.13, 0.96, 42, -763.03, 106.04, 0.04, 2, 6, -50.8, -37.5, 0.96, 42, -763.71, 85.67, 0.04, 2, 6, 46.91, 11.4, 0.96, 42, -666, 134.57, 0.04, 2, 6, 15.92, 19.07, 0.96, 42, -696.99, 142.24, 0.04, 2, 6, -30.72, -17.51, 0.96, 42, -743.63, 105.66, 0.04, 2, 6, -32.01, -39.2, 0.96, 42, -744.92, 83.97, 0.04, 2, 6, -43.52, -54.39, 0.96412, 42, -756.43, 68.78, 0.03588, 2, 6, -25.63, -73.56, 0.97142, 42, -738.54, 49.61, 0.02858, 2, 6, -5.47, -84.91, 0.9746, 42, -718.39, 38.26, 0.0254, 2, 6, 44.37, -76.28, 0.96, 42, -668.54, 46.89, 0.04, 2, 6, 43.85, -99.73, 0.97464, 42, -669.06, 23.44, 0.02536, 2, 6, 19.79, -93.32, 0.97517, 42, -693.12, 29.85, 0.02483, 2, 6, 5.41, -33.13, 0.96, 42, -707.5, 90.04, 0.04, 2, 6, 8.01, -23.83, 0.96, 42, -704.9, 99.33, 0.04, 2, 6, 4.27, -16.45, 0.96, 42, -708.64, 106.72, 0.04, 2, 6, -0.7, -8.17, 0.96, 42, -713.61, 115, 0.04, 2, 6, -5.21, -1.21, 0.96, 42, -718.12, 121.96, 0.04, 2, 6, -4.08, 2.59, 0.96, 42, -716.99, 125.76, 0.04, 2, 6, -0.34, 5.04, 0.96, 42, -713.25, 128.21, 0.04, 2, 6, -4.91, 10.06, 0.96, 42, -717.82, 133.23, 0.04, 2, 6, -12.16, 6.46, 0.96, 42, -725.07, 129.63, 0.04, 2, 6, -9.5, 2.92, 0.96, 42, -722.41, 126.09, 0.04, 2, 6, -11.68, 0.12, 0.96, 42, -724.59, 123.29, 0.04, 2, 6, -19.74, -4.1, 0.96, 42, -732.65, 119.07, 0.04, 2, 6, -26.4, -14.57, 0.96, 42, -739.31, 108.6, 0.04, 2, 6, -26.09, -29.83, 0.96, 42, -739, 93.33, 0.04, 2, 6, -27.14, -42.04, 0.96, 42, -740.05, 81.13, 0.04, 2, 6, -23.78, -50.67, 0.96, 42, -736.69, 72.5, 0.04, 2, 6, -17.95, -56.18, 0.96, 42, -730.86, 66.99, 0.04, 2, 6, -11.61, -58.91, 0.96, 42, -724.52, 64.25, 0.04, 2, 6, -13.18, -62.32, 0.96, 42, -726.09, 60.85, 0.04, 2, 6, -4.03, -61.99, 0.96, 42, -716.94, 61.18, 0.04, 2, 6, -7.42, -59.33, 0.96, 42, -720.33, 63.83, 0.04, 2, 6, -1.76, -54.82, 0.96, 42, -714.67, 68.35, 0.04, 2, 6, 4.72, -47.71, 0.96, 42, -708.19, 75.46, 0.04, 2, 6, 8.59, -40.98, 0.96, 42, -704.32, 82.19, 0.04, 2, 6, -6.93, 4.18, 0.96, 42, -719.84, 127.35, 0.04, 2, 6, -8.64, -2.31, 0.96, 42, -721.55, 120.85, 0.04, 2, 6, -8.1, -10.9, 0.96, 42, -721.01, 112.27, 0.04, 2, 6, -3.01, -21.39, 0.96, 42, -715.92, 101.78, 0.04, 2, 6, -7.59, -19.01, 0.96, 42, -720.5, 104.16, 0.04, 2, 6, -3.48, -32.25, 0.96, 42, -716.39, 90.91, 0.04, 2, 6, -3.6, -42.36, 0.96, 42, -716.51, 80.81, 0.04, 2, 6, -8.51, -49.86, 0.96, 42, -721.42, 73.31, 0.04, 2, 6, -8.11, -41.1, 0.96, 42, -721.02, 82.07, 0.04, 2, 6, -8.87, -30.74, 0.96, 42, -721.78, 92.43, 0.04, 2, 6, -9.84, -55.3, 0.96, 42, -722.75, 67.87, 0.04, 2, 6, -9.75, -60.38, 0.96, 42, -722.66, 62.79, 0.04, 2, 6, -9.09, -64.53, 0.96, 42, -722, 58.63, 0.04, 2, 6, 23.45, -29.66, 0.96, 42, -689.46, 93.5, 0.04, 2, 6, 13.28, -71.84, 0.96, 42, -699.64, 51.33, 0.04], "hull": 31, "edges": [32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 30, 32, 18, 20, 20, 22, 22, 24, 24, 26, 14, 16, 16, 18, 26, 28, 28, 30, 40, 42, 42, 44, 54, 56, 56, 58, 58, 60, 2, 0, 0, 60, 68, 70, 70, 72, 74, 76, 76, 78, 78, 86, 86, 84, 68, 88, 88, 82, 66, 90, 90, 82, 80, 92, 92, 84, 66, 64, 64, 80, 74, 62, 62, 94, 94, 72, 100, 102, 108, 110, 98, 118, 118, 114, 112, 120, 120, 116, 110, 122, 122, 116, 100, 124, 124, 114, 98, 96, 96, 112, 102, 104, 106, 108, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 154, 156, 142, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 156, 152, 170, 170, 154, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 190, 192, 192, 194, 194, 196, 182, 198, 188, 200, 200, 190, 198, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 196, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 136, 224, 224, 138, 222, 226, 226, 228, 228, 230, 232, 234, 234, 236, 236, 238, 238, 240, 242, 244, 244, 246, 246, 248, 248, 250, 254, 256, 256, 258, 258, 260, 260, 262, 250, 264, 264, 252, 252, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 278, 280, 280, 282, 282, 284, 286, 288, 288, 290, 290, 292, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 312, 316, 316, 318, 318, 320, 322, 324, 326, 314, 330, 332, 334, 336, 336, 338, 340, 342, 344, 342, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 386, 388, 388, 390, 390, 392, 392, 346, 394, 396, 396, 398, 398, 400, 398, 402, 400, 404, 404, 406, 406, 408, 408, 410, 410, 412, 412, 402, 408, 414, 414, 416, 254, 420, 420, 284], "width": 201, "height": 255}}, "head2": {"head": {"type": "mesh", "uvs": [0.23208, 0.74382, 0.35783, 0.68173, 0.49828, 0.63222, 0.53574, 0.6567, 0.59892, 0.64585, 0.6414, 0.64809, 0.69047, 0.65343, 0.72244, 0.62607, 0.81031, 0.68018, 0.87843, 0.75195, 0.89761, 0.80902, 0.84527, 0.86391, 0.71711, 0.96331, 0.64649, 0.99383, 0.54564, 0.99542, 0.43257, 0.96437, 0.29757, 0.90674, 0.1632, 0.82319, 0.32262, 0.83762, 0.43392, 0.90369, 0.54883, 0.93582, 0.65022, 0.93582, 0.5475, 0.85973, 0.65559, 0.86196, 0.73301, 0.90508, 0.82538, 0.83245, 0.61914, 0.7161, 0.5725, 0.70712, 0.5364, 0.72274, 0.49607, 0.74331, 0.46218, 0.7619, 0.4431, 0.75796, 0.43032, 0.74364, 0.4061, 0.7622, 0.42521, 0.79014, 0.44239, 0.77924, 0.45667, 0.78743, 0.47899, 0.81848, 0.53215, 0.84319, 0.60801, 0.83997, 0.66889, 0.84247, 0.71123, 0.82821, 0.73767, 0.80462, 0.7502, 0.77939, 0.76741, 0.78512, 0.76423, 0.7493, 0.75159, 0.76294, 0.72819, 0.74135, 0.69175, 0.71687, 0.65765, 0.7026, 0.43568, 0.76933, 0.46827, 0.77519, 0.51087, 0.77197, 0.56216, 0.75061, 0.55111, 0.7689, 0.61628, 0.75105, 0.66654, 0.75018, 0.70465, 0.76846, 0.66102, 0.76802, 0.60966, 0.77238, 0.73194, 0.77293, 0.75717, 0.77192, 0.77772, 0.7688, 0.54302, 0.80087, 0.60919, 0.80593, 0.67151, 0.8034], "triangles": [14, 20, 13, 14, 15, 20, 13, 21, 12, 13, 20, 21, 15, 19, 20, 15, 16, 19, 12, 24, 11, 24, 25, 11, 12, 21, 24, 21, 23, 24, 23, 21, 22, 19, 22, 20, 21, 20, 22, 16, 18, 19, 16, 17, 18, 25, 41, 42, 42, 44, 25, 23, 40, 24, 40, 41, 24, 25, 24, 41, 18, 34, 19, 19, 38, 22, 19, 37, 38, 37, 34, 36, 36, 34, 35, 34, 37, 19, 11, 25, 10, 22, 39, 23, 23, 39, 40, 22, 38, 39, 38, 63, 39, 38, 37, 63, 39, 64, 40, 40, 65, 41, 40, 64, 65, 39, 63, 64, 17, 0, 18, 18, 33, 34, 18, 0, 33, 44, 62, 25, 25, 9, 10, 25, 62, 9, 41, 65, 42, 42, 57, 60, 57, 42, 65, 36, 51, 37, 37, 52, 63, 37, 51, 52, 64, 58, 65, 64, 59, 58, 63, 54, 64, 64, 54, 59, 42, 43, 44, 42, 60, 43, 65, 58, 57, 63, 52, 54, 34, 50, 35, 34, 33, 50, 36, 35, 51, 43, 61, 44, 44, 61, 62, 43, 60, 61, 35, 50, 51, 51, 50, 30, 50, 31, 30, 52, 51, 29, 60, 46, 61, 46, 60, 47, 54, 53, 59, 59, 55, 58, 59, 53, 55, 51, 30, 29, 54, 52, 53, 61, 46, 62, 31, 50, 32, 53, 52, 29, 53, 29, 28, 46, 45, 62, 62, 45, 9, 58, 56, 57, 60, 57, 47, 57, 56, 47, 58, 55, 56, 46, 47, 45, 0, 1, 33, 50, 33, 32, 33, 1, 32, 29, 30, 32, 30, 31, 32, 45, 8, 9, 55, 26, 56, 55, 53, 26, 53, 27, 26, 53, 28, 27, 26, 49, 56, 56, 48, 47, 56, 49, 48, 45, 47, 8, 29, 32, 2, 29, 3, 28, 29, 2, 3, 2, 32, 1, 47, 48, 8, 28, 3, 27, 49, 6, 48, 8, 6, 7, 8, 48, 6, 27, 4, 26, 26, 5, 49, 26, 4, 5, 27, 3, 4, 49, 5, 6], "vertices": [2, 6, 0.95, 44.87, 0.97189, 42, -711.96, 168.04, 0.02811, 2, 6, 15.92, 19.07, 0.96, 42, -696.99, 142.24, 0.04, 2, 6, 27.6, -9.56, 0.96, 42, -685.31, 113.6, 0.04, 2, 6, 21.11, -16.88, 0.96, 42, -691.8, 106.29, 0.04, 2, 6, 23.45, -29.66, 0.96, 42, -689.46, 93.5, 0.04, 2, 6, 22.59, -38.18, 0.96, 42, -690.32, 84.99, 0.04, 2, 6, 20.9, -47.99, 0.96, 42, -692.01, 75.18, 0.04, 2, 6, 27.66, -54.65, 0.96, 42, -685.25, 68.52, 0.04, 2, 6, 13.28, -71.84, 0.96, 42, -699.64, 51.33, 0.04, 2, 6, -5.47, -84.91, 0.9746, 42, -718.39, 38.26, 0.0254, 2, 6, -20.15, -88.27, 0.98209, 42, -733.06, 34.89, 0.01791, 2, 6, -33.78, -77.29, 0.97799, 42, -746.69, 45.88, 0.02201, 2, 6, -58.26, -50.7, 0.96648, 42, -771.17, 72.47, 0.03352, 2, 6, -65.56, -36.25, 0.96304, 42, -778.47, 86.92, 0.03696, 2, 6, -65.29, -15.98, 0.96321, 42, -778.2, 107.19, 0.03679, 2, 6, -56.61, 6.47, 0.96795, 42, -769.52, 129.64, 0.03205, 2, 6, -41.01, 33.1, 0.97798, 42, -753.93, 156.27, 0.02202, 2, 6, -18.82, 59.38, 0.9879, 42, -731.73, 182.55, 0.0121, 2, 6, -23.57, 27.48, 0.96723, 42, -736.48, 150.65, 0.03277, 2, 6, -41.15, 5.68, 0.96285, 42, -754.07, 128.85, 0.03715, 2, 6, -50.12, -17.13, 0.96, 42, -763.03, 106.04, 0.04, 2, 6, -50.8, -37.5, 0.96, 42, -763.71, 85.67, 0.04, 2, 6, -30.72, -17.51, 0.96, 42, -743.63, 105.66, 0.04, 2, 6, -32.01, -39.2, 0.96, 42, -744.92, 83.97, 0.04, 2, 6, -43.52, -54.39, 0.96412, 42, -756.43, 68.78, 0.03588, 2, 6, -25.63, -73.56, 0.97142, 42, -738.54, 49.61, 0.02858, 2, 6, 5.41, -33.13, 0.96, 42, -707.5, 90.04, 0.04, 2, 6, 8.01, -23.83, 0.96, 42, -704.9, 99.33, 0.04, 2, 6, 4.27, -16.45, 0.96, 42, -708.64, 106.72, 0.04, 2, 6, -0.7, -8.17, 0.96, 42, -713.61, 115, 0.04, 2, 6, -5.21, -1.21, 0.96, 42, -718.12, 121.96, 0.04, 2, 6, -4.08, 2.59, 0.96, 42, -716.99, 125.76, 0.04, 2, 6, -0.34, 5.04, 0.96, 42, -713.25, 128.21, 0.04, 2, 6, -4.91, 10.06, 0.96, 42, -717.82, 133.23, 0.04, 2, 6, -12.16, 6.46, 0.96, 42, -725.07, 129.63, 0.04, 2, 6, -9.5, 2.92, 0.96, 42, -722.41, 126.09, 0.04, 2, 6, -11.68, 0.12, 0.96, 42, -724.59, 123.29, 0.04, 2, 6, -19.74, -4.1, 0.96, 42, -732.65, 119.07, 0.04, 2, 6, -26.4, -14.57, 0.96, 42, -739.31, 108.6, 0.04, 2, 6, -26.09, -29.83, 0.96, 42, -739, 93.33, 0.04, 2, 6, -27.14, -42.04, 0.96, 42, -740.05, 81.13, 0.04, 2, 6, -23.78, -50.67, 0.96, 42, -736.69, 72.5, 0.04, 2, 6, -17.95, -56.18, 0.96, 42, -730.86, 66.99, 0.04, 2, 6, -11.61, -58.91, 0.96, 42, -724.52, 64.25, 0.04, 2, 6, -13.18, -62.32, 0.96, 42, -726.09, 60.85, 0.04, 2, 6, -4.03, -61.99, 0.96, 42, -716.94, 61.18, 0.04, 2, 6, -7.42, -59.33, 0.96, 42, -720.33, 63.83, 0.04, 2, 6, -1.76, -54.82, 0.96, 42, -714.67, 68.35, 0.04, 2, 6, 4.72, -47.71, 0.96, 42, -708.19, 75.46, 0.04, 2, 6, 8.59, -40.98, 0.96, 42, -704.32, 82.19, 0.04, 2, 6, -6.93, 4.18, 0.96, 42, -719.84, 127.35, 0.04, 2, 6, -8.64, -2.31, 0.96, 42, -721.55, 120.85, 0.04, 2, 6, -8.1, -10.9, 0.96, 42, -721.01, 112.27, 0.04, 2, 6, -3.01, -21.39, 0.96, 42, -715.92, 101.78, 0.04, 2, 6, -7.59, -19.01, 0.96, 42, -720.5, 104.16, 0.04, 2, 6, -3.48, -32.25, 0.96, 42, -716.39, 90.91, 0.04, 2, 6, -3.6, -42.36, 0.96, 42, -716.51, 80.81, 0.04, 2, 6, -8.51, -49.86, 0.96, 42, -721.42, 73.31, 0.04, 2, 6, -8.11, -41.1, 0.96, 42, -721.02, 82.07, 0.04, 2, 6, -8.87, -30.74, 0.96, 42, -721.78, 92.43, 0.04, 2, 6, -9.84, -55.3, 0.96, 42, -722.75, 67.87, 0.04, 2, 6, -9.75, -60.38, 0.96, 42, -722.66, 62.79, 0.04, 2, 6, -9.09, -64.53, 0.96, 42, -722, 58.63, 0.04, 2, 6, -15.69, -17.11, 0.96, 42, -728.6, 106.06, 0.04, 2, 6, -17.42, -30.36, 0.96, 42, -730.33, 92.81, 0.04, 2, 6, -17.2, -42.9, 0.96, 42, -730.11, 80.27, 0.04], "hull": 18, "edges": [20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 28, 30, 30, 32, 10, 12, 12, 14, 4, 6, 0, 36, 36, 38, 40, 42, 44, 46, 48, 50, 50, 18, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 92, 94, 94, 96, 96, 98, 98, 52, 100, 102, 102, 104, 104, 106, 104, 108, 106, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 108, 114, 120, 120, 122, 10, 8, 8, 6, 2, 4, 2, 0, 0, 34, 14, 16, 16, 18, 18, 20, 126, 128, 128, 130], "width": 201, "height": 255}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.77648, 0.01124, 0.9582, 0, 0.96514, 0.01647, 0.97924, 0.0503, 0.99771, 0.09463, 0.99753, 0.14688, 0.97738, 0.19404, 0.92234, 0.25288, 0.86101, 0.3132, 0.72885, 0.40945, 0.64653, 0.4741, 0.6151, 0.50866, 0.58516, 0.54199, 0.56078, 0.56979, 0.52714, 0.59558, 0.49835, 0.61827, 0.48199, 0.64688, 0.46707, 0.68483, 0.43886, 0.73971, 0.32375, 0.8808, 0.27579, 0.94275, 0.26148, 0.97085, 0.24989, 0.99924, 0.00135, 0.99947, 0.01972, 0.96602, 0.02942, 0.93427, 0.04054, 0.84893, 0.03917, 0.76909, 0.0449, 0.72607, 0.07872, 0.66587, 0.10013, 0.63057, 0.13166, 0.59318, 0.12713, 0.56178, 0.1336, 0.53514, 0.14482, 0.50929, 0.17375, 0.43468, 0.18426, 0.34381, 0.20834, 0.22914, 0.23115, 0.17653, 0.22583, 0.16472, 0.2809, 0.08863, 0.34075, 0.04056, 0.52025, 0.02725, 0.38761, 0.14726, 0.59426, 0.11603, 0.80191, 0.08313, 0.36566, 0.18916, 0.58364, 0.20576, 0.83595, 0.20896, 0.82393, 0.14683, 0.58707, 0.15655, 0.40168, 0.23799, 0.2802, 0.51992, 0.27466, 0.54812, 0.26673, 0.57845, 0.24603, 0.60932, 0.2239, 0.641, 0.37962, 0.64837, 0.39771, 0.62149, 0.43001, 0.58916, 0.4543, 0.56091, 0.48495, 0.5326, 0.72005, 0.25146, 0.61702, 0.38492, 0.32883, 0.3731, 0.19524, 0.67202, 0.15338, 0.74318, 0.36477, 0.68248, 0.33338, 0.75643, 0.16175, 0.93294], "triangles": [45, 3, 4, 3, 45, 0, 0, 1, 2, 3, 0, 2, 42, 0, 45, 50, 44, 49, 39, 40, 43, 43, 40, 44, 50, 43, 44, 44, 42, 45, 44, 41, 42, 41, 44, 40, 50, 46, 43, 44, 45, 49, 6, 49, 5, 50, 49, 48, 47, 46, 50, 43, 46, 39, 46, 38, 39, 5, 49, 4, 47, 50, 48, 37, 38, 46, 48, 49, 6, 23, 24, 22, 22, 24, 21, 24, 25, 69, 21, 24, 69, 21, 69, 20, 20, 69, 19, 25, 26, 69, 19, 69, 68, 19, 68, 18, 66, 68, 69, 66, 69, 26, 26, 27, 66, 27, 28, 66, 68, 66, 67, 68, 67, 18, 67, 66, 65, 66, 28, 65, 65, 28, 29, 18, 67, 17, 67, 57, 17, 17, 57, 16, 65, 56, 67, 67, 56, 57, 56, 65, 30, 65, 29, 30, 57, 58, 16, 58, 57, 55, 16, 58, 15, 57, 56, 55, 56, 30, 55, 30, 31, 55, 55, 54, 58, 58, 59, 15, 58, 54, 59, 15, 59, 14, 55, 31, 54, 59, 60, 14, 14, 60, 13, 31, 32, 54, 59, 54, 60, 54, 53, 60, 54, 32, 53, 60, 61, 13, 13, 61, 12, 32, 33, 53, 61, 60, 52, 60, 53, 52, 53, 33, 52, 12, 61, 11, 33, 34, 52, 11, 61, 10, 10, 61, 63, 52, 34, 35, 52, 64, 61, 63, 61, 64, 35, 64, 52, 10, 63, 9, 35, 36, 64, 9, 63, 8, 64, 51, 63, 63, 51, 62, 63, 62, 8, 62, 51, 47, 64, 36, 51, 36, 37, 51, 8, 62, 7, 62, 48, 7, 7, 48, 6, 62, 47, 48, 37, 46, 51, 51, 46, 47, 49, 45, 4], "vertices": [3, 19, 130.45, 135.84, 0.05532, 20, 28.45, 0.8, 0.91284, 46, -374.97, -16.08, 0.03183, 1, 20, 7.97, 72.03, 1, 1, 20, 27.41, 76.7, 1, 2, 19, 169.38, 221.3, 0.00286, 20, 67.38, 86.26, 0.99714, 3, 28, -13.54, 94.78, 0.56947, 19, 221.74, 233.82, 0.05339, 20, 119.74, 98.77, 0.37714, 3, 28, 47.74, 108.78, 0.80464, 19, 284.32, 239.81, 0.04108, 20, 182.32, 104.77, 0.15429, 3, 28, 104.84, 113.59, 0.90204, 19, 341.56, 237.24, 0.02653, 20, 239.56, 102.19, 0.07143, 1, 28, 178.77, 107.93, 1, 1, 28, 254.99, 100.19, 1, 1, 28, 379.71, 74.45, 1, 1, 28, 462.9, 59.69, 1, 1, 28, 506.24, 56.7, 1, 1, 28, 548.01, 53.97, 1, 1, 28, 582.79, 51.93, 1, 1, 28, 616.04, 45.72, 1, 1, 28, 645.23, 40.58, 1, 1, 28, 680.25, 41.89, 1, 1, 28, 726.08, 46.28, 1, 1, 28, 792.96, 50.02, 1, 1, 28, 968.71, 43.02, 1, 1, 28, 1045.66, 40.95, 1, 1, 28, 1079.89, 42.92, 1, 1, 28, 1114.21, 46.04, 1, 1, 28, 1136.78, -51.04, 1, 1, 28, 1095.91, -52.87, 1, 1, 28, 1057.82, -57.62, 1, 1, 28, 956.76, -76.25, 1, 1, 28, 863.28, -98.28, 1, 1, 28, 812.32, -107.62, 1, 1, 28, 738.7, -110.61, 1, 1, 28, 695.39, -111.75, 1, 1, 28, 648.72, -109.49, 1, 1, 28, 612.31, -119.71, 1, 1, 28, 580.5, -124.35, 1, 1, 28, 549.18, -126.93, 1, 1, 28, 459.1, -135.71, 1, 1, 28, 351.62, -156.06, 1, 2, 28, 215.01, -177.52, 0.78571, 19, 413.34, -65.64, 0.21429, 2, 28, 151.27, -182.77, 0.69429, 19, 349.46, -62.64, 0.30571, 2, 28, 137.9, -188.03, 0.67143, 19, 335.52, -66.13, 0.32857, 2, 28, 43.75, -186.99, 0.27714, 19, 242.29, -52.98, 0.72286, 1, 19, 182.41, -34.67, 1, 3, 19, 159.54, 35.43, 0.89284, 20, 57.54, -99.61, 0.06868, 46, -477.71, -35.34, 0.03848, 3, 28, 102.92, -129.5, 0.33006, 19, 308.36, -3.58, 0.65729, 46, -530.9, -179.71, 0.01265, 3, 19, 262.98, 75.27, 0.86432, 20, 160.98, -59.77, 0.11153, 46, -448.04, -142.14, 0.02415, 4, 28, -9.46, 15.16, 0.38114, 19, 215.54, 154.34, 0.18411, 20, 113.55, 19.29, 0.40454, 46, -364.77, -102.56, 0.03021, 3, 28, 154.02, -126.79, 0.55756, 19, 359.38, -7.48, 0.42803, 46, -539.7, -230.12, 0.01441, 3, 28, 153.91, -37.14, 0.62501, 19, 370.82, 81.44, 0.34723, 46, -452.3, -250.08, 0.02776, 3, 28, 135.03, 62.34, 0.80036, 19, 364.9, 182.52, 0.1791, 46, -351.12, -253.93, 0.02054, 4, 28, 63.26, 40.91, 0.62538, 19, 290.97, 170.51, 0.17934, 20, 188.97, 35.47, 0.16985, 46, -355.94, -179.19, 0.02543, 3, 28, 95.91, -49.04, 0.43555, 19, 311.77, 77.11, 0.53542, 46, -450.92, -190.89, 0.02902, 3, 28, 208.04, -99.57, 0.7869, 19, 416.46, 12.56, 0.18975, 46, -525.26, -288.86, 0.02335, 2, 28, 549.49, -71.15, 0.97282, 46, -573.97, -628.02, 0.02718, 2, 28, 583.06, -65.73, 0.97082, 46, -576.2, -661.95, 0.02918, 2, 28, 619.33, -60.66, 0.96957, 46, -579.38, -698.43, 0.03043, 2, 28, 657.39, -60.44, 0.97045, 46, -587.68, -735.57, 0.02955, 2, 28, 696.51, -60.57, 0.97256, 46, -596.55, -773.67, 0.02744, 2, 28, 691.18, 2.28, 0.96879, 46, -534.11, -782.55, 0.03121, 2, 28, 658.03, 2.11, 0.96939, 46, -526.85, -750.2, 0.03061, 2, 28, 617.24, 6.03, 0.97281, 46, -513.9, -711.32, 0.02719, 2, 28, 581.93, 7.92, 0.97388, 46, -504.16, -677.32, 0.02612, 2, 28, 545.99, 12.28, 0.97533, 46, -491.87, -643.27, 0.02467, 3, 28, 195.26, 28.48, 0.88739, 19, 420.28, 141.19, 0.07716, 46, -397.6, -305.07, 0.03544, 2, 28, 360.99, 24.14, 0.96794, 46, -438.91, -465.62, 0.03206, 2, 28, 372.98, -91.67, 0.97272, 46, -554.47, -451.39, 0.02728, 2, 28, 735.45, -63.41, 0.97514, 46, -608.04, -810.99, 0.02486, 2, 28, 822.64, -60.62, 0.97671, 46, -624.83, -896.6, 0.02329, 2, 28, 732.5, 5.66, 0.96839, 46, -540.06, -823.58, 0.03161, 2, 28, 822.03, 13.3, 0.97651, 46, -552.65, -912.54, 0.02349, 1, 28, 1044.38, -6.26, 1], "hull": 43, "edges": [8, 10, 10, 12, 14, 16, 16, 18, 26, 28, 36, 38, 40, 42, 52, 54, 54, 56, 60, 62, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 80, 82, 2, 0, 0, 84, 12, 14, 2, 4, 4, 6, 6, 8, 66, 68, 62, 64, 64, 66, 24, 26, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 56, 58, 58, 60, 50, 52, 46, 48, 48, 50, 38, 40, 42, 44, 44, 46, 68, 70, 122, 126, 126, 124, 102, 128, 128, 104, 130, 132, 134, 136, 136, 138, 138, 132], "width": 401, "height": 1203}}}}], "animations": {"idle": {"bones": {"All": {"translate": [{"x": 10.95, "curve": [0.444, 10.95, 0.889, -10.71, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -10.71, "curve": [1.778, -10.71, 2.222, 10.95, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 10.95, "curve": [3.111, 10.95, 3.556, -10.71, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -10.71, "curve": [4.444, -10.71, 4.889, 10.95, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 10.95, "curve": [5.667, 10.95, 6, -10.71, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -10.71, "curve": [6.778, -10.71, 7.222, 0.12, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 0.12, "curve": [8.111, 0.12, 8.556, -10.71, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -10.71, "curve": [9.444, -10.71, 9.889, 10.95, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 10.95, "curve": [10.778, 10.95, 11.222, -10.71, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -10.71, "curve": [12.111, -10.71, 12.556, 10.95, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 10.95}]}, "All2": {"translate": [{"y": -19.63, "curve": [0.444, 0, 0.889, 0, 0.444, -19.63, 0.889, 22.25]}, {"time": 1.3333, "y": 22.25, "curve": [1.778, 0, 2.222, 0, 1.778, 22.25, 2.222, -19.63]}, {"time": 2.6667, "y": -19.63, "curve": [3.111, 0, 3.556, 0, 3.111, -19.63, 3.556, 22.25]}, {"time": 4, "y": 22.25, "curve": [4.444, 0, 4.889, 0, 4.444, 22.25, 4.889, -19.63]}, {"time": 5.3333, "y": -19.63, "curve": [5.667, 0, 6, 0, 5.667, -19.63, 6, 22.25]}, {"time": 6.3333, "y": 22.25, "curve": [6.778, 0, 7.222, 0, 6.778, 22.25, 7.222, 1.31]}, {"time": 7.6667, "y": 1.31, "curve": [8.111, 0, 8.556, 0, 8.111, 1.31, 8.556, 22.25]}, {"time": 9, "y": 22.25, "curve": [9.444, 0, 9.889, 0, 9.444, 22.25, 9.889, -19.63]}, {"time": 10.3333, "y": -19.63, "curve": [10.778, 0, 11.222, 0, 10.778, -19.63, 11.222, 22.25]}, {"time": 11.6667, "y": 22.25, "curve": [12.111, 0, 12.556, 0, 12.111, 22.25, 12.556, -19.63]}, {"time": 13, "y": -19.63}]}, "body": {"rotate": [{"value": 2.99, "curve": [0.057, 3.16, 0.112, 3.29]}, {"time": 0.1667, "value": 3.29, "curve": [0.611, 3.29, 1.056, -3.01]}, {"time": 1.5, "value": -3.01, "curve": [1.944, -3.01, 2.389, 3.29]}, {"time": 2.8333, "value": 3.29, "curve": [3.278, 3.29, 3.722, -3.01]}, {"time": 4.1667, "value": -3.01, "curve": [4.611, -3.01, 5.056, 3.29]}, {"time": 5.5, "value": 3.29, "curve": [5.833, 3.29, 6.167, -3.01]}, {"time": 6.5, "value": -3.01, "curve": [6.944, -3.01, 7.389, 0.14]}, {"time": 7.8333, "value": 0.14, "curve": [8.278, 0.14, 8.722, -3.01]}, {"time": 9.1667, "value": -3.01, "curve": [9.611, -3.01, 10.056, 3.29]}, {"time": 10.5, "value": 3.29, "curve": [10.944, 3.29, 11.389, -3.01]}, {"time": 11.8333, "value": -3.01, "curve": [12.223, -3.01, 12.613, 1.8]}, {"time": 13, "value": 2.99}], "translate": [{"y": -15.04, "curve": [0.444, 0, 0.889, 0, 0.444, -15.04, 0.889, 15.04]}, {"time": 1.3333, "y": 15.04, "curve": [1.778, 0, 2.222, 0, 1.778, 15.04, 2.222, -15.04]}, {"time": 2.6667, "y": -15.04, "curve": [3.111, 0, 3.556, 0, 3.111, -15.04, 3.556, 15.04]}, {"time": 4, "y": 15.04, "curve": [4.444, 0, 4.889, 0, 4.444, 15.04, 4.889, -15.04]}, {"time": 5.3333, "y": -15.04, "curve": [5.667, 0, 6, 0, 5.667, -15.04, 6, 15.04]}, {"time": 6.3333, "y": 15.04, "curve": [6.778, 0, 7.222, 0, 6.778, 15.04, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0, 8.111, 0, 8.556, 15.04]}, {"time": 9, "y": 15.04, "curve": [9.444, 0, 9.889, 0, 9.444, 15.04, 9.889, -15.04]}, {"time": 10.3333, "y": -15.04, "curve": [10.778, 0, 11.222, 0, 10.778, -15.04, 11.222, 15.04]}, {"time": 11.6667, "y": 15.04, "curve": [12.111, 0, 12.556, 0, 12.111, 15.04, 12.556, -15.04]}, {"time": 13, "y": -15.04}], "scale": [{"y": 1.042, "curve": [0.444, 1, 0.889, 1, 0.444, 1.042, 0.889, 0.946]}, {"time": 1.3333, "y": 0.946, "curve": [1.778, 1, 2.222, 1, 1.778, 0.946, 2.222, 1.042]}, {"time": 2.6667, "y": 1.042, "curve": [3.111, 1, 3.556, 1, 3.111, 1.042, 3.556, 0.946]}, {"time": 4, "y": 0.946, "curve": [4.444, 1, 4.889, 1, 4.444, 0.946, 4.889, 1.042]}, {"time": 5.3333, "y": 1.042, "curve": [5.667, 1, 6, 1, 5.667, 1.042, 6, 0.946]}, {"time": 6.3333, "y": 0.946, "curve": [6.778, 1, 7.222, 1, 6.778, 0.946, 7.222, 0.994]}, {"time": 7.6667, "y": 0.994, "curve": [8.111, 1, 8.556, 1, 8.111, 0.994, 8.556, 0.946]}, {"time": 9, "y": 0.946, "curve": [9.444, 1, 9.889, 1, 9.444, 0.946, 9.889, 1.042]}, {"time": 10.3333, "y": 1.042, "curve": [10.778, 1, 11.222, 1, 10.778, 1.042, 11.222, 0.946]}, {"time": 11.6667, "y": 0.946, "curve": [12.111, 1, 12.556, 1, 12.111, 0.946, 12.556, 1.042]}, {"time": 13, "y": 1.042}]}, "body2": {"rotate": [{"value": -1.78, "curve": [0.114, -2.24, 0.224, -2.58]}, {"time": 0.3333, "value": -2.58, "curve": [0.778, -2.58, 1.222, 2.41]}, {"time": 1.6667, "value": 2.41, "curve": [2.111, 2.41, 2.556, -2.58]}, {"time": 3, "value": -2.58, "curve": [3.444, -2.58, 3.889, 2.41]}, {"time": 4.3333, "value": 2.41, "curve": [4.778, 2.41, 5.222, -2.58]}, {"time": 5.6667, "value": -2.58, "curve": [6, -2.58, 6.333, 2.41]}, {"time": 6.6667, "value": 2.41, "curve": [7.111, 2.41, 7.556, -0.09]}, {"time": 8, "value": -0.09, "curve": [8.444, -0.09, 8.889, 2.41]}, {"time": 9.3333, "value": 2.41, "curve": [9.778, 2.41, 10.222, -2.58]}, {"time": 10.6667, "value": -2.58, "curve": [11.111, -2.58, 11.556, 2.41]}, {"time": 12, "value": 2.41, "curve": [12.335, 2.41, 12.67, -0.39]}, {"time": 13, "value": -1.78}], "translate": [{"x": -8.35, "curve": [0.114, -10.92, 0.224, -12.83, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -12.83, "curve": [0.778, -12.83, 1.222, 15.14, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 15.14, "curve": [2.111, 15.14, 2.556, -12.83, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -12.83, "curve": [3.444, -12.83, 3.889, 15.14, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 15.14, "curve": [4.778, 15.14, 5.222, -12.83, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -12.83, "curve": [6, -12.83, 6.333, 15.14, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 15.14, "curve": [7.111, 15.14, 7.556, 1.16, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 1.16, "curve": [8.444, 1.16, 8.889, 15.14, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 15.14, "curve": [9.778, 15.14, 10.222, -12.83, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -12.83, "curve": [11.111, -12.83, 11.556, 15.14, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 15.14, "curve": [12.335, 15.14, 12.67, -0.52, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -8.35}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.026, 0.778, 1, 1.222, 1.026]}, {"time": 1.6667, "x": 1.026, "y": 1.026, "curve": [2.111, 1.026, 2.556, 1, 2.111, 1.026, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.026, 3.444, 1, 3.889, 1.026]}, {"time": 4.3333, "x": 1.026, "y": 1.026, "curve": [4.778, 1.026, 5.222, 1, 4.778, 1.026, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.026, 6, 1, 6.333, 1.026]}, {"time": 6.6667, "x": 1.026, "y": 1.026, "curve": [7.111, 1.026, 7.556, 1.013, 7.111, 1.026, 7.556, 1.013]}, {"time": 8, "x": 1.013, "y": 1.013, "curve": [8.444, 1.013, 8.889, 1.026, 8.444, 1.013, 8.889, 1.026]}, {"time": 9.3333, "x": 1.026, "y": 1.026, "curve": [9.778, 1.026, 10.222, 1, 9.778, 1.026, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.026, 11.111, 1, 11.556, 1.026]}, {"time": 12, "x": 1.026, "y": 1.026, "curve": [12.335, 1.026, 12.67, 1.011, 12.335, 1.026, 12.67, 1.011]}, {"time": 13, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.1, "curve": [0.168, -2.18, 0.334, -3.08]}, {"time": 0.5, "value": -3.08, "curve": [0.779, -3.08, 1.057, -0.65]}, {"time": 1.3333, "value": 1.17, "curve": [1.501, 2.26, 1.667, 3.16]}, {"time": 1.8333, "value": 3.16, "curve": [2.278, 3.16, 2.722, -3.08]}, {"time": 3.1667, "value": -3.08, "curve": [3.611, -3.08, 4.056, 3.16]}, {"time": 4.5, "value": 3.16, "curve": [4.944, 3.16, 5.389, -3.08]}, {"time": 5.8333, "value": -3.08, "curve": [6.278, -3.08, 6.722, 3.16]}, {"time": 7.1667, "value": 3.16, "curve": [7.5, 3.16, 7.833, -3.08]}, {"time": 8.1667, "value": -3.08, "curve": [8.611, -3.08, 9.056, 0.04]}, {"time": 9.5, "value": 0.04, "curve": [9.944, 0.04, 10.389, -3.08]}, {"time": 10.8333, "value": -3.08, "curve": [11.278, -3.08, 11.722, 3.16]}, {"time": 12.1667, "value": 3.16, "curve": [12.445, 3.16, 12.724, 0.73]}, {"time": 13, "value": -1.1}]}, "head": {"rotate": [{"value": 0.04, "curve": [0.225, -1.51, 0.446, -3.08]}, {"time": 0.6667, "value": -3.08, "curve": [0.89, -3.08, 1.114, -1.53]}, {"time": 1.3333, "value": 0.04, "curve": [1.559, 1.59, 1.779, 3.16]}, {"time": 2, "value": 3.16, "curve": [2.444, 3.16, 2.889, -3.08]}, {"time": 3.3333, "value": -3.08, "curve": [3.778, -3.08, 4.222, 3.16]}, {"time": 4.6667, "value": 3.16, "curve": [5.111, 3.16, 5.556, -3.08]}, {"time": 6, "value": -3.08, "curve": [6.444, -3.08, 6.889, 3.16]}, {"time": 7.3333, "value": 3.16, "curve": [7.667, 3.16, 8, -3.08]}, {"time": 8.3333, "value": -3.08, "curve": [8.778, -3.08, 9.222, 0.04]}, {"time": 9.6667, "value": 0.04, "curve": [10.111, 0.04, 10.556, -3.08]}, {"time": 11, "value": -3.08, "curve": [11.444, -3.08, 11.889, 3.16]}, {"time": 12.3333, "value": 3.16, "curve": [12.557, 3.16, 12.781, 1.61]}, {"time": 13, "value": 0.04}]}, "eyebrow_L3": {"translate": [{"curve": [1.944, 0, 5.222, 0, 1.944, 0, 5.222, 0]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, 2.37, 5.944, 0, 6.056, 0]}, {"time": 6.1667, "x": 2.37, "curve": [7.222, 2.37, 8.278, 2.37, 7.222, 0, 8.278, 0]}, {"time": 9.3333, "x": 2.37, "curve": [9.5, 2.37, 9.667, 0, 10.017, 0, 8.917, 0]}, {"time": 9.8333}]}, "eyebrow_L2": {"rotate": [{"curve": [1.944, 0, 5.222, -0.01]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, -5.27]}, {"time": 6.1667, "value": -5.27, "curve": "stepped"}, {"time": 9.3333, "value": -5.27, "curve": [9.5, -5.27, 9.667, 0]}, {"time": 9.8333}], "scale": [{"curve": [1.944, 1, 5.222, 1, 1.944, 1, 5.222, 1]}, {"time": 5.8333, "curve": [5.944, 1, 6.056, 0.966, 5.944, 1, 6.056, 1]}, {"time": 6.1667, "x": 0.966, "curve": "stepped"}, {"time": 9.3333, "x": 0.966, "curve": [9.5, 0.966, 9.667, 1, 10.389, 1, 8.917, 1]}, {"time": 9.8333}]}, "eyebrow_L": {"rotate": [{"curve": [1.944, 0, 5.222, -0.01]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, -9]}, {"time": 6.1667, "value": -9, "curve": [7.222, -9, 8.278, -8.98]}, {"time": 9.3333, "value": -9, "curve": [9.5, -9, 9.667, 0]}, {"time": 9.8333}]}, "eyebrow_R3": {"translate": [{"curve": [1.944, 0, 5.222, 0, 1.944, 0, 5.222, 0]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, 2.37, 5.944, 0, 6.056, 0]}, {"time": 6.1667, "x": 2.37, "curve": [7.222, 2.37, 8.278, 2.37, 7.222, 0, 8.278, 0]}, {"time": 9.3333, "x": 2.37, "curve": [9.5, 2.37, 9.667, 0, 10.017, 0, 8.917, 0]}, {"time": 9.8333}]}, "eyebrow_R2": {"rotate": [{"curve": [1.944, 0, 5.222, 0.01]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, 6.5]}, {"time": 6.1667, "value": 6.5, "curve": "stepped"}, {"time": 9.3333, "value": 6.5, "curve": [9.5, 6.5, 9.667, 0]}, {"time": 9.8333}], "scale": [{"curve": [1.944, 1, 5.222, 1, 1.944, 1, 5.222, 1]}, {"time": 5.8333, "curve": [5.944, 1, 6.056, 0.966, 5.944, 1, 6.056, 1]}, {"time": 6.1667, "x": 0.966, "curve": "stepped"}, {"time": 9.3333, "x": 0.966, "curve": [9.5, 0.966, 9.667, 1, 10.389, 1, 8.917, 1]}, {"time": 9.8333}]}, "eyebrow_R": {"rotate": [{"curve": [1.944, 0, 5.222, 0.01]}, {"time": 5.8333, "curve": [5.944, 0, 6.056, 6.77]}, {"time": 6.1667, "value": 6.77, "curve": [7.222, 6.77, 8.278, 6.76]}, {"time": 9.3333, "value": 6.77, "curve": [9.5, 6.78, 9.667, 0]}, {"time": 9.8333}]}, "eye_L": {"translate": [{"curve": [2, 0, 2.826, -0.08, 2, 0, 2.826, 0.13]}, {"time": 6, "curve": [6.033, 0, 6.067, -3.36, 6.033, 0, 6.067, 5.32]}, {"time": 6.1, "x": -3.36, "y": 5.32, "curve": [6.511, -3.36, 6.922, -3.36, 6.511, 5.32, 6.922, 5.32]}, {"time": 7.3333, "x": -3.36, "y": 5.32, "curve": [7.367, -3.36, 7.4, -1.18, 7.367, 5.32, 7.4, 6.63]}, {"time": 7.4333, "x": -1.18, "y": 6.63, "curve": [7.622, -1.18, 7.811, -1.18, 7.622, 6.63, 7.811, 6.63]}, {"time": 8, "x": -1.18, "y": 6.63, "curve": [8.033, -1.18, 8.067, -2.55, 8.033, 6.63, 8.067, 4.88]}, {"time": 8.1, "x": -2.55, "y": 4.88, "curve": [8.408, -2.55, 9.025, -2.55, 8.408, 4.88, 9.025, 4.87]}, {"time": 9.3333, "x": -2.55, "y": 4.88, "curve": [9.367, -2.55, 9.4, 0, 9.367, 4.88, 9.4, 0]}, {"time": 9.4333}]}, "eye_R": {"translate": [{"curve": [2, 0, 2.826, -0.08, 2, 0, 2.826, 0.13]}, {"time": 6, "curve": [6.033, 0, 6.067, -3.36, 6.033, 0, 6.067, 5.32]}, {"time": 6.1, "x": -3.36, "y": 5.32, "curve": [6.511, -3.36, 6.922, -3.36, 6.511, 5.32, 6.922, 5.32]}, {"time": 7.3333, "x": -3.36, "y": 5.32, "curve": [7.367, -3.36, 7.4, -1.18, 7.367, 5.32, 7.4, 6.63]}, {"time": 7.4333, "x": -1.18, "y": 6.63, "curve": [7.622, -1.18, 7.811, -1.18, 7.622, 6.63, 7.811, 6.63]}, {"time": 8, "x": -1.18, "y": 6.63, "curve": [8.033, -1.18, 8.067, -2.55, 8.033, 6.63, 8.067, 4.88]}, {"time": 8.1, "x": -2.55, "y": 4.88, "curve": [8.408, -2.55, 9.025, -2.55, 8.408, 4.88, 9.025, 4.87]}, {"time": 9.3333, "x": -2.55, "y": 4.88, "curve": [9.367, -2.55, 9.4, 0, 9.367, 4.88, 9.4, 0]}, {"time": 9.4333}]}, "sh_R": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.12]}, {"time": 7.6667, "value": -0.12, "curve": [8.111, -0.12, 8.556, 0]}, {"time": 9}]}, "arm_R": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.31]}, {"time": 7.6667, "value": 0.31, "curve": [8.111, 0.31, 8.556, 0]}, {"time": 9}]}, "arm_R2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.49]}, {"time": 7.6667, "value": 0.49, "curve": [8.111, 0.49, 8.556, 0]}, {"time": 9}]}, "sh_L": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.24]}, {"time": 7.6667, "value": 0.24, "curve": [8.111, 0.24, 8.556, 0]}, {"time": 9}]}, "tun": {"rotate": [{"value": 3.29, "curve": [0.444, 3.29, 0.889, -3.01]}, {"time": 1.3333, "value": -3.01, "curve": [1.778, -3.01, 2.222, 3.29]}, {"time": 2.6667, "value": 3.29, "curve": [3.111, 3.29, 3.556, -3.01]}, {"time": 4, "value": -3.01, "curve": [4.444, -3.01, 4.889, 3.29]}, {"time": 5.3333, "value": 3.29, "curve": [5.667, 3.29, 6, -3.01]}, {"time": 6.3333, "value": -3.01, "curve": [6.778, -3.01, 7.222, 0.14]}, {"time": 7.6667, "value": 0.14, "curve": [8.111, 0.14, 8.556, -3.01]}, {"time": 9, "value": -3.01, "curve": [9.444, -3.01, 9.889, 3.29]}, {"time": 10.3333, "value": 3.29, "curve": [10.778, 3.29, 11.222, -3.01]}, {"time": 11.6667, "value": -3.01, "curve": [12.111, -3.01, 12.556, 3.29]}, {"time": 13, "value": 3.29}]}, "hand_L": {"rotate": [{"value": 4.32, "curve": [0.114, 5.21, 0.224, 5.87]}, {"time": 0.3333, "value": 5.87, "curve": [0.778, 5.87, 1.222, -3.8]}, {"time": 1.6667, "value": -3.8, "curve": [2.111, -3.8, 2.556, 5.87]}, {"time": 3, "value": 5.87, "curve": [3.444, 5.87, 3.889, -3.8]}, {"time": 4.3333, "value": -3.8, "curve": [4.778, -3.8, 5.222, 5.87]}, {"time": 5.6667, "value": 5.87, "curve": [6, 5.87, 6.333, -3.8]}, {"time": 6.6667, "value": -3.8, "curve": [7.111, -3.8, 7.556, 1.03]}, {"time": 8, "value": 1.03, "curve": [8.444, 1.03, 8.889, -3.8]}, {"time": 9.3333, "value": -3.8, "curve": [9.778, -3.8, 10.222, 5.87]}, {"time": 10.6667, "value": 5.87, "curve": [11.111, 5.87, 11.556, -3.8]}, {"time": 12, "value": -3.8, "curve": [12.335, -3.8, 12.67, 1.61]}, {"time": 13, "value": 4.32}]}, "hand_L3": {"rotate": [{"value": 1.03, "curve": [0.225, 3.43, 0.446, 5.87]}, {"time": 0.6667, "value": 5.87, "curve": [1.111, 5.87, 1.556, -3.8]}, {"time": 2, "value": -3.8, "curve": [2.444, -3.8, 2.889, 5.87]}, {"time": 3.3333, "value": 5.87, "curve": [3.778, 5.87, 4.222, -3.8]}, {"time": 4.6667, "value": -3.8, "curve": [5.111, -3.8, 5.556, 5.87]}, {"time": 6, "value": 5.87, "curve": [6.333, 5.87, 6.667, -3.8]}, {"time": 7, "value": -3.8, "curve": [7.444, -3.8, 7.889, 1.03]}, {"time": 8.3333, "value": 1.03, "curve": [8.778, 1.03, 9.222, -3.8]}, {"time": 9.6667, "value": -3.8, "curve": [10.111, -3.8, 10.556, 5.87]}, {"time": 11, "value": 5.87, "curve": [11.444, 5.87, 11.889, -3.8]}, {"time": 12.3333, "value": -3.8, "curve": [12.557, -3.8, 12.781, -1.4]}, {"time": 13, "value": 1.03}]}, "arm_L": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.43]}, {"time": 7.6667, "value": -0.43, "curve": [8.111, -0.43, 8.556, 0]}, {"time": 9}]}, "leg_R2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, -0.24]}, {"time": 7.6667, "value": -0.24, "curve": [8.111, -0.24, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.12]}, {"time": 7.6667, "value": 0.12, "curve": [8.111, 0.12, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, -0.16]}, {"time": 7.6667, "value": -0.16, "curve": [8.111, -0.16, 8.556, 0]}, {"time": 9}]}, "leg_RR": {"rotate": [{"value": 0.73, "curve": "stepped"}, {"time": 6.3333, "value": 0.73, "curve": [6.778, 0.73, 7.222, 0.49]}, {"time": 7.6667, "value": 0.49, "curve": [8.111, 0.49, 8.556, 0.73]}, {"time": 9, "value": 0.73}]}, "leg_RR2": {"rotate": [{"value": -1.62, "curve": "stepped"}, {"time": 6.3333, "value": -1.62, "curve": [6.778, -1.62, 7.222, -1.48]}, {"time": 7.6667, "value": -1.48, "curve": [8.111, -1.48, 8.556, -1.62]}, {"time": 9, "value": -1.62}]}, "sh_R2": {"translate": [{"x": -4.32, "y": 0.75, "curve": [0.114, -5.58, 0.224, -6.52, 0.114, 1.06, 0.224, 1.29]}, {"time": 0.3333, "x": -6.52, "y": 1.29, "curve": [0.778, -6.52, 1.222, 7.26, 0.778, 1.29, 1.222, -2.1]}, {"time": 1.6667, "x": 7.26, "y": -2.1, "curve": [2.111, 7.26, 2.556, -6.52, 2.111, -2.1, 2.556, 1.29]}, {"time": 3, "x": -6.52, "y": 1.29, "curve": [3.444, -6.52, 3.889, 7.26, 3.444, 1.29, 3.889, -2.1]}, {"time": 4.3333, "x": 7.26, "y": -2.1, "curve": [4.778, 7.26, 5.222, -6.52, 4.778, -2.1, 5.222, 1.29]}, {"time": 5.6667, "x": -6.52, "y": 1.29, "curve": [6, -6.52, 6.333, 7.26, 6, 1.29, 6.333, -2.1]}, {"time": 6.6667, "x": 7.26, "y": -2.1, "curve": [7.111, 7.26, 7.556, 0.37, 7.111, -2.1, 7.556, -0.41]}, {"time": 8, "x": 0.37, "y": -0.41, "curve": [8.444, 0.37, 8.889, 7.26, 8.444, -0.41, 8.889, -2.1]}, {"time": 9.3333, "x": 7.26, "y": -2.1, "curve": [9.778, 7.26, 10.222, -6.52, 9.778, -2.1, 10.222, 1.29]}, {"time": 10.6667, "x": -6.52, "y": 1.29, "curve": [11.111, -6.52, 11.556, 7.26, 11.111, 1.29, 11.556, -2.1]}, {"time": 12, "x": 7.26, "y": -2.1, "curve": [12.335, 7.26, 12.67, -0.46, 12.335, -2.1, 12.67, -0.2]}, {"time": 13, "x": -4.32, "y": 0.75}]}, "sh_L2": {"translate": [{"x": -4.32, "y": 0.75, "curve": [0.114, -5.58, 0.224, -6.52, 0.114, 1.06, 0.224, 1.29]}, {"time": 0.3333, "x": -6.52, "y": 1.29, "curve": [0.778, -6.52, 1.222, 7.26, 0.778, 1.29, 1.222, -2.1]}, {"time": 1.6667, "x": 7.26, "y": -2.1, "curve": [2.111, 7.26, 2.556, -6.52, 2.111, -2.1, 2.556, 1.29]}, {"time": 3, "x": -6.52, "y": 1.29, "curve": [3.444, -6.52, 3.889, 7.26, 3.444, 1.29, 3.889, -2.1]}, {"time": 4.3333, "x": 7.26, "y": -2.1, "curve": [4.778, 7.26, 5.222, -6.52, 4.778, -2.1, 5.222, 1.29]}, {"time": 5.6667, "x": -6.52, "y": 1.29, "curve": [6, -6.52, 6.333, 7.26, 6, 1.29, 6.333, -2.1]}, {"time": 6.6667, "x": 7.26, "y": -2.1, "curve": [7.111, 7.26, 7.556, 0.37, 7.111, -2.1, 7.556, -0.41]}, {"time": 8, "x": 0.37, "y": -0.41, "curve": [8.444, 0.37, 8.889, 7.26, 8.444, -0.41, 8.889, -2.1]}, {"time": 9.3333, "x": 7.26, "y": -2.1, "curve": [9.778, 7.26, 10.222, -6.52, 9.778, -2.1, 10.222, 1.29]}, {"time": 10.6667, "x": -6.52, "y": 1.29, "curve": [11.111, -6.52, 11.556, 7.26, 11.111, 1.29, 11.556, -2.1]}, {"time": 12, "x": 7.26, "y": -2.1, "curve": [12.335, 7.26, 12.67, -0.46, 12.335, -2.1, 12.67, -0.2]}, {"time": 13, "x": -4.32, "y": 0.75}]}, "arm_RR": {"rotate": [{"value": -0.77, "curve": "stepped"}, {"time": 6.3333, "value": -0.77, "curve": [6.778, -0.77, 7.222, -0.55]}, {"time": 7.6667, "value": -0.55, "curve": [8.111, -0.55, 8.556, -0.77]}, {"time": 9, "value": -0.77}]}, "arm_RR2": {"rotate": [{"value": 1.63, "curve": "stepped"}, {"time": 6.3333, "value": 1.63, "curve": [6.778, 1.63, 7.222, 1.98]}, {"time": 7.6667, "value": 1.98, "curve": [8.111, 1.98, 8.556, 1.63]}, {"time": 9, "value": 1.63}]}, "arm_L2": {"translate": [{"x": -13.58, "y": -0.61, "curve": [0.444, -13.58, 0.889, 17.2, 0.444, -0.61, 0.889, -0.72]}, {"time": 1.3333, "x": 17.2, "y": -0.72, "curve": [1.778, 17.2, 2.222, -13.58, 1.778, -0.72, 2.222, -0.61]}, {"time": 2.6667, "x": -13.58, "y": -0.61, "curve": [3.111, -13.58, 3.556, 17.2, 3.111, -0.61, 3.556, -0.72]}, {"time": 4, "x": 17.2, "y": -0.72, "curve": [4.444, 17.2, 4.889, -13.58, 4.444, -0.72, 4.889, -0.61]}, {"time": 5.3333, "x": -13.58, "y": -0.61, "curve": [5.667, -13.58, 6, 17.2, 5.667, -0.61, 6, -0.72]}, {"time": 6.3333, "x": 17.2, "y": -0.72, "curve": [6.778, 17.2, 7.222, 1.81, 6.778, -0.72, 7.222, -0.67]}, {"time": 7.6667, "x": 1.81, "y": -0.67, "curve": [8.111, 1.81, 8.556, 17.2, 8.111, -0.67, 8.556, -0.72]}, {"time": 9, "x": 17.2, "y": -0.72, "curve": [9.444, 17.2, 9.889, -13.58, 9.444, -0.72, 9.889, -0.61]}, {"time": 10.3333, "x": -13.58, "y": -0.61, "curve": [10.778, -13.58, 11.222, 17.2, 10.778, -0.61, 11.222, -0.72]}, {"time": 11.6667, "x": 17.2, "y": -0.72, "curve": [12.111, 17.2, 12.556, -13.58, 12.111, -0.72, 12.556, -0.61]}, {"time": 13, "x": -13.58, "y": -0.61}]}, "headround3": {"translate": [{"x": -35.75, "curve": [0.279, -155.43, 0.556, -314.59, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -314.59, "curve": [1.278, -314.59, 1.722, 94.26, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 94.26, "curve": [2.611, 94.26, 3.056, -314.59, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -314.59, "curve": [3.944, -314.59, 4.389, 94.26, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 94.26, "curve": [5.278, 94.26, 5.722, -314.59, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -314.59, "curve": [6.5, -314.59, 6.833, 94.26, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 94.26, "curve": [7.611, 94.26, 8.056, -110.17, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -110.17, "curve": [8.944, -110.17, 9.389, 94.26, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 94.26, "curve": [10.278, 94.26, 10.722, -314.59, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -314.59, "curve": [11.611, -314.59, 12.056, 94.26, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 94.26, "curve": [12.667, 94.26, 12.835, 36.56, 12.667, 0, 12.835, 0]}, {"time": 13, "x": -35.75}]}, "headround": {"translate": [{"y": -129.72, "curve": [0.336, 0, 0.668, 0, 0.336, -17.7, 0.668, 203.38]}, {"time": 1, "y": 203.38, "curve": [1.444, 0, 1.889, 0, 1.444, 203.38, 1.889, -193.16]}, {"time": 2.3333, "y": -193.16, "curve": [2.778, 0, 3.222, 0, 2.778, -193.16, 3.222, 203.38]}, {"time": 3.6667, "y": 203.38, "curve": [4.111, 0, 4.556, 0, 4.111, 203.38, 4.556, -193.16]}, {"time": 5, "y": -193.16, "curve": [5.444, 0, 5.889, 0, 5.444, -193.16, 5.889, 203.38]}, {"time": 6.3333, "y": 203.38, "curve": [6.667, 0, 7, 0, 6.667, 203.38, 7, -193.16]}, {"time": 7.3333, "y": -193.16, "curve": [7.778, 0, 8.222, 0, 7.778, -193.16, 8.222, 5.11]}, {"time": 8.6667, "y": 5.11, "curve": [9.111, 0, 9.556, 0, 9.111, 5.11, 9.556, -193.16]}, {"time": 10, "y": -193.16, "curve": [10.444, 0, 10.889, 0, 10.444, -193.16, 10.889, 203.38]}, {"time": 11.3333, "y": 203.38, "curve": [11.778, 0, 12.222, 0, 11.778, 203.38, 12.222, -193.16]}, {"time": 12.6667, "y": -193.16, "curve": [12.779, 0, 12.892, 0, 12.779, -193.16, 12.892, -167.72]}, {"time": 13, "y": -129.72}]}, "bodyround": {"translate": [{"y": 123.52, "curve": [0.168, 0, 0.334, 0, 0.168, 248.61, 0.334, 351.24]}, {"time": 0.5, "y": 351.24, "curve": [0.944, 0, 1.389, 0, 0.944, 351.24, 1.389, -364.85]}, {"time": 1.8333, "y": -364.85, "curve": [2.278, 0, 2.722, 0, 2.278, -364.85, 2.722, 351.24]}, {"time": 3.1667, "y": 351.24, "curve": [3.611, 0, 4.056, 0, 3.611, 351.24, 4.056, -364.85]}, {"time": 4.5, "y": -364.85, "curve": [4.944, 0, 5.389, 0, 4.944, -364.85, 5.389, 351.24]}, {"time": 5.8333, "y": 351.24, "curve": [6.167, 0, 6.5, 0, 6.167, 351.24, 6.5, -364.85]}, {"time": 6.8333, "y": -364.85, "curve": [7.278, 0, 7.722, 0, 7.278, -364.85, 7.722, -6.81]}, {"time": 8.1667, "y": -6.81, "curve": [8.611, 0, 9.056, 0, 8.611, -6.81, 9.056, -364.85]}, {"time": 9.5, "y": -364.85, "curve": [9.944, 0, 10.389, 0, 9.944, -364.85, 10.389, 351.24]}, {"time": 10.8333, "y": 351.24, "curve": [11.278, 0, 11.722, 0, 11.278, 351.24, 11.722, -364.85]}, {"time": 12.1667, "y": -364.85, "curve": [12.445, 0, 12.724, 0, 12.445, -364.85, 12.724, -86.45]}, {"time": 13, "y": 123.52}]}, "tunround": {"translate": [{"x": -354.42, "curve": [0.444, -354.42, 0.889, 348.15, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 348.15, "curve": [1.778, 348.15, 2.222, -354.42, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -354.42, "curve": [3.111, -354.42, 3.556, 348.15, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 348.15, "curve": [4.444, 348.15, 4.889, -354.42, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -354.42, "curve": [5.667, -354.42, 6, 348.15, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 348.15, "curve": [6.778, 348.15, 7.222, -3.14, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -3.14, "curve": [8.111, -3.14, 8.556, 348.15, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 348.15, "curve": [9.444, 348.15, 9.889, -354.42, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -354.42, "curve": [10.778, -354.42, 11.222, 348.15, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 348.15, "curve": [12.111, 348.15, 12.556, -354.42, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -354.42}]}, "body3": {"translate": [{"x": -13.02, "y": 2.11, "curve": [0.168, -24.12, 0.334, -33.23, 0.168, 3.92, 0.334, 5.4]}, {"time": 0.5, "x": -33.23, "y": 5.4, "curve": [0.944, -33.23, 1.389, 30.3, 0.944, 5.4, 1.389, -4.92]}, {"time": 1.8333, "x": 30.32, "y": -4.92, "curve": [2.278, 30.33, 2.722, -33.21, 2.278, -4.93, 2.722, 5.39]}, {"time": 3.1667, "x": -33.23, "y": 5.4, "curve": [3.611, -33.25, 4.056, 30.3, 3.611, 5.4, 4.056, -4.92]}, {"time": 4.5, "x": 30.32, "y": -4.92, "curve": [4.944, 30.33, 5.389, -33.21, 4.944, -4.93, 5.389, 5.39]}, {"time": 5.8333, "x": -33.23, "y": 5.4, "curve": [6.167, -33.25, 6.5, 30.3, 6.167, 5.4, 6.5, -4.92]}, {"time": 6.8333, "x": 30.32, "y": -4.92, "curve": [7.278, 30.33, 7.722, -33.21, 7.278, -4.93, 7.722, 5.39]}, {"time": 8.1667, "x": -33.23, "y": 5.4, "curve": [8.611, -33.25, 9.056, 30.3, 8.611, 5.4, 9.056, -4.92]}, {"time": 9.5, "x": 30.32, "y": -4.92, "curve": [9.944, 30.33, 10.389, -33.21, 9.944, -4.93, 10.389, 5.39]}, {"time": 10.8333, "x": -33.23, "y": 5.4, "curve": [11.278, -33.25, 11.722, 30.3, 11.278, 5.4, 11.722, -4.92]}, {"time": 12.1667, "x": 30.32, "y": -4.92, "curve": [12.445, 30.33, 12.724, 5.62, 12.445, -4.93, 12.724, -0.91]}, {"time": 13, "x": -13.02, "y": 2.11}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.167, 1.038, 0.333, 0.945, 0.167, 0.963, 0.333, 1.063]}, {"time": 0.5, "x": 0.945, "y": 1.063, "curve": [0.722, 0.945, 0.944, 1.111, 0.722, 1.063, 0.944, 0.883]}, {"time": 1.1667, "x": 1.111, "y": 0.883, "curve": [1.389, 1.111, 1.611, 0.945, 1.389, 0.883, 1.611, 1.063]}, {"time": 1.8333, "x": 0.945, "y": 1.063, "curve": [2.056, 0.945, 2.278, 1.111, 2.056, 1.063, 2.278, 0.883]}, {"time": 2.5, "x": 1.111, "y": 0.883, "curve": [2.722, 1.111, 2.944, 0.945, 2.722, 0.883, 2.944, 1.063]}, {"time": 3.1667, "x": 0.945, "y": 1.063, "curve": [3.389, 0.945, 3.611, 1.111, 3.389, 1.063, 3.611, 0.883]}, {"time": 3.8333, "x": 1.111, "y": 0.883, "curve": [4.056, 1.111, 4.278, 0.945, 4.056, 0.883, 4.278, 1.063]}, {"time": 4.5, "x": 0.945, "y": 1.063, "curve": [4.722, 0.945, 4.944, 1.111, 4.722, 1.063, 4.944, 0.883]}, {"time": 5.1667, "x": 1.111, "y": 0.883, "curve": [5.389, 1.111, 5.611, 0.945, 5.389, 0.883, 5.611, 1.063]}, {"time": 5.8333, "x": 0.945, "y": 1.063, "curve": [6, 0.945, 6.167, 1.111, 6, 1.063, 6.167, 0.883]}, {"time": 6.3333, "x": 1.111, "y": 0.883, "curve": [6.5, 1.111, 6.667, 0.945, 6.5, 0.883, 6.667, 1.063]}, {"time": 6.8333, "x": 0.945, "y": 1.063, "curve": [7.056, 0.945, 7.278, 1.111, 7.056, 1.063, 7.278, 0.883]}, {"time": 7.5, "x": 1.111, "y": 0.883, "curve": [7.722, 1.111, 7.944, 0.945, 7.722, 0.883, 7.944, 1.063]}, {"time": 8.1667, "x": 0.945, "y": 1.063, "curve": [8.389, 0.945, 8.611, 1.111, 8.389, 1.063, 8.611, 0.883]}, {"time": 8.8333, "x": 1.111, "y": 0.883, "curve": [9.056, 1.111, 9.278, 0.945, 9.056, 0.883, 9.278, 1.063]}, {"time": 9.5, "x": 0.945, "y": 1.063, "curve": [9.722, 0.945, 9.944, 1.111, 9.722, 1.063, 9.944, 0.883]}, {"time": 10.1667, "x": 1.111, "y": 0.883, "curve": [10.389, 1.111, 10.611, 0.945, 10.389, 0.883, 10.611, 1.063]}, {"time": 10.8333, "x": 0.945, "y": 1.063, "curve": [11.056, 0.945, 11.278, 1.111, 11.056, 1.063, 11.278, 0.883]}, {"time": 11.5, "x": 1.111, "y": 0.883, "curve": [11.722, 1.111, 11.944, 0.945, 11.722, 0.883, 11.944, 1.063]}, {"time": 12.1667, "x": 0.945, "y": 1.063, "curve": [12.389, 0.945, 12.611, 1.111, 12.389, 1.063, 12.611, 0.883]}, {"time": 12.8333, "x": 1.111, "y": 0.883, "curve": [12.889, 1.111, 12.944, 1.1, 12.889, 0.883, 12.944, 0.895]}, {"time": 13, "x": 1.084, "y": 0.912}]}, "body4": {"translate": [{"x": 1.49, "curve": [0.225, -14.59, 0.446, -30.9, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.9, "curve": [1.111, -30.9, 1.556, 33.86, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 33.88, "curve": [2.444, 33.9, 2.889, -30.89, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.9, "curve": [3.778, -30.92, 4.222, 33.86, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 33.88, "curve": [5.111, 33.9, 5.556, -30.88, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.9, "curve": [6.333, -30.92, 6.667, 33.87, 6.444, 0, 6.556, 0]}, {"time": 7, "x": 33.88, "curve": [7.444, 33.9, 7.889, -30.89, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -30.9, "curve": [8.778, -30.92, 9.222, 33.86, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 33.88, "curve": [10.111, 33.9, 10.556, -30.89, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.9, "curve": [11.444, -30.92, 11.889, 33.86, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 33.88, "curve": [12.557, 33.89, 12.781, 17.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 1.49}], "scale": [{"x": 1.111, "y": 0.883, "curve": [0.222, 1.111, 0.444, 0.945, 0.222, 0.883, 0.444, 1.063]}, {"time": 0.6667, "x": 0.945, "y": 1.063, "curve": [0.889, 0.945, 1.111, 1.111, 0.889, 1.063, 1.111, 0.883]}, {"time": 1.3333, "x": 1.111, "y": 0.883, "curve": [1.556, 1.111, 1.778, 0.945, 1.556, 0.883, 1.778, 1.063]}, {"time": 2, "x": 0.945, "y": 1.063, "curve": [2.222, 0.945, 2.444, 1.111, 2.222, 1.063, 2.444, 0.883]}, {"time": 2.6667, "x": 1.111, "y": 0.883, "curve": [2.889, 1.111, 3.111, 0.945, 2.889, 0.883, 3.111, 1.063]}, {"time": 3.3333, "x": 0.945, "y": 1.063, "curve": [3.556, 0.945, 3.778, 1.111, 3.556, 1.063, 3.778, 0.883]}, {"time": 4, "x": 1.111, "y": 0.883, "curve": [4.222, 1.111, 4.444, 0.945, 4.222, 0.883, 4.444, 1.063]}, {"time": 4.6667, "x": 0.945, "y": 1.063, "curve": [4.889, 0.945, 5.111, 1.111, 4.889, 1.063, 5.111, 0.883]}, {"time": 5.3333, "x": 1.111, "y": 0.883, "curve": [5.556, 1.111, 5.778, 0.945, 5.556, 0.883, 5.778, 1.063]}, {"time": 6, "x": 0.945, "y": 1.063, "curve": [6.167, 0.945, 6.333, 1.111, 6.167, 1.063, 6.333, 0.883]}, {"time": 6.5, "x": 1.111, "y": 0.883, "curve": [6.667, 1.111, 6.833, 0.945, 6.667, 0.883, 6.833, 1.063]}, {"time": 7, "x": 0.945, "y": 1.063, "curve": [7.222, 0.945, 7.444, 1.111, 7.222, 1.063, 7.444, 0.883]}, {"time": 7.6667, "x": 1.111, "y": 0.883, "curve": [7.889, 1.111, 8.111, 0.945, 7.889, 0.883, 8.111, 1.063]}, {"time": 8.3333, "x": 0.945, "y": 1.063, "curve": [8.556, 0.945, 8.778, 1.111, 8.556, 1.063, 8.778, 0.883]}, {"time": 9, "x": 1.111, "y": 0.883, "curve": [9.222, 1.111, 9.444, 0.945, 9.222, 0.883, 9.444, 1.063]}, {"time": 9.6667, "x": 0.945, "y": 1.063, "curve": [9.889, 0.945, 10.111, 1.111, 9.889, 1.063, 10.111, 0.883]}, {"time": 10.3333, "x": 1.111, "y": 0.883, "curve": [10.556, 1.111, 10.778, 0.945, 10.556, 0.883, 10.778, 1.063]}, {"time": 11, "x": 0.945, "y": 1.063, "curve": [11.222, 0.945, 11.444, 1.111, 11.222, 1.063, 11.444, 0.883]}, {"time": 11.6667, "x": 1.111, "y": 0.883, "curve": [11.889, 1.111, 12.111, 0.945, 11.889, 0.883, 12.111, 1.063]}, {"time": 12.3333, "x": 0.945, "y": 1.063, "curve": [12.556, 0.945, 12.778, 1.111, 12.556, 1.063, 12.778, 0.883]}, {"time": 13, "x": 1.111, "y": 0.883}]}, "body5": {"translate": [{"x": 13.04, "curve": [0.279, -5.96, 0.556, -31.25, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -31.25, "curve": [1.278, -31.25, 1.722, 33.67, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 33.69, "curve": [2.611, 33.7, 3.056, -31.23, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -31.25, "curve": [3.944, -31.27, 4.389, 33.67, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 33.69, "curve": [5.278, 33.7, 5.722, -31.23, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -31.25, "curve": [6.5, -31.27, 6.833, 33.68, 6.611, 0, 6.722, 0]}, {"time": 7.1667, "x": 33.69, "curve": [7.611, 33.7, 8.056, -31.23, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -31.25, "curve": [8.944, -31.27, 9.389, 33.67, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 33.69, "curve": [10.278, 33.7, 10.722, -31.23, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -31.25, "curve": [11.611, -31.27, 12.056, 33.67, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 33.69, "curve": [12.667, 33.69, 12.835, 24.53, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.04}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.056, 1.1, 0.111, 1.111, 0.056, 0.895, 0.111, 0.883]}, {"time": 0.1667, "x": 1.111, "y": 0.883, "curve": [0.389, 1.111, 0.611, 0.945, 0.389, 0.883, 0.611, 1.063]}, {"time": 0.8333, "x": 0.945, "y": 1.063, "curve": [1.056, 0.945, 1.278, 1.111, 1.056, 1.063, 1.278, 0.883]}, {"time": 1.5, "x": 1.111, "y": 0.883, "curve": [1.722, 1.111, 1.944, 0.945, 1.722, 0.883, 1.944, 1.063]}, {"time": 2.1667, "x": 0.945, "y": 1.063, "curve": [2.389, 0.945, 2.611, 1.111, 2.389, 1.063, 2.611, 0.883]}, {"time": 2.8333, "x": 1.111, "y": 0.883, "curve": [3.056, 1.111, 3.278, 0.945, 3.056, 0.883, 3.278, 1.063]}, {"time": 3.5, "x": 0.945, "y": 1.063, "curve": [3.722, 0.945, 3.944, 1.111, 3.722, 1.063, 3.944, 0.883]}, {"time": 4.1667, "x": 1.111, "y": 0.883, "curve": [4.389, 1.111, 4.611, 0.945, 4.389, 0.883, 4.611, 1.063]}, {"time": 4.8333, "x": 0.945, "y": 1.063, "curve": [5.056, 0.945, 5.278, 1.111, 5.056, 1.063, 5.278, 0.883]}, {"time": 5.5, "x": 1.111, "y": 0.883, "curve": [5.722, 1.111, 5.944, 0.945, 5.722, 0.883, 5.944, 1.063]}, {"time": 6.1667, "x": 0.945, "y": 1.063, "curve": [6.333, 0.945, 6.5, 1.111, 6.333, 1.063, 6.5, 0.883]}, {"time": 6.6667, "x": 1.111, "y": 0.883, "curve": [6.833, 1.111, 7, 0.945, 6.833, 0.883, 7, 1.063]}, {"time": 7.1667, "x": 0.945, "y": 1.063, "curve": [7.389, 0.945, 7.611, 1.111, 7.389, 1.063, 7.611, 0.883]}, {"time": 7.8333, "x": 1.111, "y": 0.883, "curve": [8.056, 1.111, 8.278, 0.945, 8.056, 0.883, 8.278, 1.063]}, {"time": 8.5, "x": 0.945, "y": 1.063, "curve": [8.722, 0.945, 8.944, 1.111, 8.722, 1.063, 8.944, 0.883]}, {"time": 9.1667, "x": 1.111, "y": 0.883, "curve": [9.389, 1.111, 9.611, 0.945, 9.389, 0.883, 9.611, 1.063]}, {"time": 9.8333, "x": 0.945, "y": 1.063, "curve": [10.056, 0.945, 10.278, 1.111, 10.056, 1.063, 10.278, 0.883]}, {"time": 10.5, "x": 1.111, "y": 0.883, "curve": [10.722, 1.111, 10.944, 0.945, 10.722, 0.883, 10.944, 1.063]}, {"time": 11.1667, "x": 0.945, "y": 1.063, "curve": [11.389, 0.945, 11.611, 1.111, 11.389, 1.063, 11.611, 0.883]}, {"time": 11.8333, "x": 1.111, "y": 0.883, "curve": [12.056, 1.111, 12.278, 0.945, 12.056, 0.883, 12.278, 1.063]}, {"time": 12.5, "x": 0.945, "y": 1.063, "curve": [12.667, 0.945, 12.833, 1.037, 12.667, 1.063, 12.833, 0.963]}, {"time": 13, "x": 1.084, "y": 0.912}]}, "body6": {"translate": [{"x": -13.03, "y": 2.01, "curve": [0.168, -24.15, 0.334, -33.27, 0.168, 3.73, 0.334, 5.14]}, {"time": 0.5, "x": -33.27, "y": 5.14, "curve": [0.944, -33.27, 1.389, 30.34, 0.944, 5.14, 1.389, -4.68]}, {"time": 1.8333, "x": 30.35, "y": -4.69, "curve": [2.278, 30.37, 2.722, -33.26, 2.278, -4.69, 2.722, 5.13]}, {"time": 3.1667, "x": -33.27, "y": 5.14, "curve": [3.611, -33.29, 4.056, 30.34, 3.611, 5.14, 4.056, -4.68]}, {"time": 4.5, "x": 30.35, "y": -4.69, "curve": [4.944, 30.37, 5.389, -33.25, 4.944, -4.69, 5.389, 5.13]}, {"time": 5.8333, "x": -33.27, "y": 5.14, "curve": [6.167, -33.29, 6.5, 30.34, 6.167, 5.14, 6.5, -4.68]}, {"time": 6.8333, "x": 30.35, "y": -4.69, "curve": [7.278, 30.37, 7.722, -33.26, 7.278, -4.69, 7.722, 5.13]}, {"time": 8.1667, "x": -33.27, "y": 5.14, "curve": [8.611, -33.29, 9.056, 30.34, 8.611, 5.14, 9.056, -4.68]}, {"time": 9.5, "x": 30.35, "y": -4.69, "curve": [9.944, 30.37, 10.389, -33.26, 9.944, -4.69, 10.389, 5.13]}, {"time": 10.8333, "x": -33.27, "y": 5.14, "curve": [11.278, -33.29, 11.722, 30.34, 11.278, 5.14, 11.722, -4.68]}, {"time": 12.1667, "x": 30.35, "y": -4.69, "curve": [12.445, 30.36, 12.724, 5.63, 12.445, -4.69, 12.724, -0.87]}, {"time": 13, "x": -13.03, "y": 2.01}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.167, 1.038, 0.333, 0.945, 0.167, 0.963, 0.333, 1.063]}, {"time": 0.5, "x": 0.945, "y": 1.063, "curve": [0.722, 0.945, 0.944, 1.111, 0.722, 1.063, 0.944, 0.883]}, {"time": 1.1667, "x": 1.111, "y": 0.883, "curve": [1.389, 1.111, 1.611, 0.945, 1.389, 0.883, 1.611, 1.063]}, {"time": 1.8333, "x": 0.945, "y": 1.063, "curve": [2.056, 0.945, 2.278, 1.111, 2.056, 1.063, 2.278, 0.883]}, {"time": 2.5, "x": 1.111, "y": 0.883, "curve": [2.722, 1.111, 2.944, 0.945, 2.722, 0.883, 2.944, 1.063]}, {"time": 3.1667, "x": 0.945, "y": 1.063, "curve": [3.389, 0.945, 3.611, 1.111, 3.389, 1.063, 3.611, 0.883]}, {"time": 3.8333, "x": 1.111, "y": 0.883, "curve": [4.056, 1.111, 4.278, 0.945, 4.056, 0.883, 4.278, 1.063]}, {"time": 4.5, "x": 0.945, "y": 1.063, "curve": [4.722, 0.945, 4.944, 1.111, 4.722, 1.063, 4.944, 0.883]}, {"time": 5.1667, "x": 1.111, "y": 0.883, "curve": [5.389, 1.111, 5.611, 0.945, 5.389, 0.883, 5.611, 1.063]}, {"time": 5.8333, "x": 0.945, "y": 1.063, "curve": [6, 0.945, 6.167, 1.111, 6, 1.063, 6.167, 0.883]}, {"time": 6.3333, "x": 1.111, "y": 0.883, "curve": [6.5, 1.111, 6.667, 0.945, 6.5, 0.883, 6.667, 1.063]}, {"time": 6.8333, "x": 0.945, "y": 1.063, "curve": [7.056, 0.945, 7.278, 1.111, 7.056, 1.063, 7.278, 0.883]}, {"time": 7.5, "x": 1.111, "y": 0.883, "curve": [7.722, 1.111, 7.944, 0.945, 7.722, 0.883, 7.944, 1.063]}, {"time": 8.1667, "x": 0.945, "y": 1.063, "curve": [8.389, 0.945, 8.611, 1.111, 8.389, 1.063, 8.611, 0.883]}, {"time": 8.8333, "x": 1.111, "y": 0.883, "curve": [9.056, 1.111, 9.278, 0.945, 9.056, 0.883, 9.278, 1.063]}, {"time": 9.5, "x": 0.945, "y": 1.063, "curve": [9.722, 0.945, 9.944, 1.111, 9.722, 1.063, 9.944, 0.883]}, {"time": 10.1667, "x": 1.111, "y": 0.883, "curve": [10.389, 1.111, 10.611, 0.945, 10.389, 0.883, 10.611, 1.063]}, {"time": 10.8333, "x": 0.945, "y": 1.063, "curve": [11.056, 0.945, 11.278, 1.111, 11.056, 1.063, 11.278, 0.883]}, {"time": 11.5, "x": 1.111, "y": 0.883, "curve": [11.722, 1.111, 11.944, 0.945, 11.722, 0.883, 11.944, 1.063]}, {"time": 12.1667, "x": 0.945, "y": 1.063, "curve": [12.389, 0.945, 12.611, 1.111, 12.389, 1.063, 12.611, 0.883]}, {"time": 12.8333, "x": 1.111, "y": 0.883, "curve": [12.889, 1.111, 12.944, 1.1, 12.889, 0.883, 12.944, 0.895]}, {"time": 13, "x": 1.084, "y": 0.912}]}, "body7": {"translate": [{"x": 1.49, "curve": [0.225, -14.59, 0.446, -30.9, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -30.9, "curve": [1.111, -30.9, 1.556, 33.86, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 33.88, "curve": [2.444, 33.9, 2.889, -30.89, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -30.9, "curve": [3.778, -30.92, 4.222, 33.86, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 33.88, "curve": [5.111, 33.9, 5.556, -30.88, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -30.9, "curve": [6.333, -30.92, 6.667, 33.87, 6.444, 0, 6.556, 0]}, {"time": 7, "x": 33.88, "curve": [7.444, 33.9, 7.889, -30.89, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -30.9, "curve": [8.778, -30.92, 9.222, 33.86, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 33.88, "curve": [10.111, 33.9, 10.556, -30.89, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -30.9, "curve": [11.444, -30.92, 11.889, 33.86, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 33.88, "curve": [12.557, 33.89, 12.781, 17.8, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 1.49}], "scale": [{"x": 1.111, "y": 0.883, "curve": [0.222, 1.111, 0.444, 0.945, 0.222, 0.883, 0.444, 1.063]}, {"time": 0.6667, "x": 0.945, "y": 1.063, "curve": [0.889, 0.945, 1.111, 1.111, 0.889, 1.063, 1.111, 0.883]}, {"time": 1.3333, "x": 1.111, "y": 0.883, "curve": [1.556, 1.111, 1.778, 0.945, 1.556, 0.883, 1.778, 1.063]}, {"time": 2, "x": 0.945, "y": 1.063, "curve": [2.222, 0.945, 2.444, 1.111, 2.222, 1.063, 2.444, 0.883]}, {"time": 2.6667, "x": 1.111, "y": 0.883, "curve": [2.889, 1.111, 3.111, 0.945, 2.889, 0.883, 3.111, 1.063]}, {"time": 3.3333, "x": 0.945, "y": 1.063, "curve": [3.556, 0.945, 3.778, 1.111, 3.556, 1.063, 3.778, 0.883]}, {"time": 4, "x": 1.111, "y": 0.883, "curve": [4.222, 1.111, 4.444, 0.945, 4.222, 0.883, 4.444, 1.063]}, {"time": 4.6667, "x": 0.945, "y": 1.063, "curve": [4.889, 0.945, 5.111, 1.111, 4.889, 1.063, 5.111, 0.883]}, {"time": 5.3333, "x": 1.111, "y": 0.883, "curve": [5.556, 1.111, 5.778, 0.945, 5.556, 0.883, 5.778, 1.063]}, {"time": 6, "x": 0.945, "y": 1.063, "curve": [6.167, 0.945, 6.333, 1.111, 6.167, 1.063, 6.333, 0.883]}, {"time": 6.5, "x": 1.111, "y": 0.883, "curve": [6.667, 1.111, 6.833, 0.945, 6.667, 0.883, 6.833, 1.063]}, {"time": 7, "x": 0.945, "y": 1.063, "curve": [7.222, 0.945, 7.444, 1.111, 7.222, 1.063, 7.444, 0.883]}, {"time": 7.6667, "x": 1.111, "y": 0.883, "curve": [7.889, 1.111, 8.111, 0.945, 7.889, 0.883, 8.111, 1.063]}, {"time": 8.3333, "x": 0.945, "y": 1.063, "curve": [8.556, 0.945, 8.778, 1.111, 8.556, 1.063, 8.778, 0.883]}, {"time": 9, "x": 1.111, "y": 0.883, "curve": [9.222, 1.111, 9.444, 0.945, 9.222, 0.883, 9.444, 1.063]}, {"time": 9.6667, "x": 0.945, "y": 1.063, "curve": [9.889, 0.945, 10.111, 1.111, 9.889, 1.063, 10.111, 0.883]}, {"time": 10.3333, "x": 1.111, "y": 0.883, "curve": [10.556, 1.111, 10.778, 0.945, 10.556, 0.883, 10.778, 1.063]}, {"time": 11, "x": 0.945, "y": 1.063, "curve": [11.222, 0.945, 11.444, 1.111, 11.222, 1.063, 11.444, 0.883]}, {"time": 11.6667, "x": 1.111, "y": 0.883, "curve": [11.889, 1.111, 12.111, 0.945, 11.889, 0.883, 12.111, 1.063]}, {"time": 12.3333, "x": 0.945, "y": 1.063, "curve": [12.556, 0.945, 12.778, 1.111, 12.556, 1.063, 12.778, 0.883]}, {"time": 13, "x": 1.111, "y": 0.883}]}, "body8": {"translate": [{"x": 13.04, "curve": [0.279, -5.96, 0.556, -31.25, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -31.25, "curve": [1.278, -31.25, 1.722, 33.67, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 33.69, "curve": [2.611, 33.7, 3.056, -31.23, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -31.25, "curve": [3.944, -31.27, 4.389, 33.67, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 33.69, "curve": [5.278, 33.7, 5.722, -31.23, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -31.25, "curve": [6.5, -31.27, 6.833, 33.68, 6.611, 0, 6.722, 0]}, {"time": 7.1667, "x": 33.69, "curve": [7.611, 33.7, 8.056, -31.23, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -31.25, "curve": [8.944, -31.27, 9.389, 33.67, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 33.69, "curve": [10.278, 33.7, 10.722, -31.23, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -31.25, "curve": [11.611, -31.27, 12.056, 33.67, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 33.69, "curve": [12.667, 33.69, 12.835, 24.53, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 13.04}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.056, 1.1, 0.111, 1.111, 0.056, 0.895, 0.111, 0.883]}, {"time": 0.1667, "x": 1.111, "y": 0.883, "curve": [0.389, 1.111, 0.611, 0.945, 0.389, 0.883, 0.611, 1.063]}, {"time": 0.8333, "x": 0.945, "y": 1.063, "curve": [1.056, 0.945, 1.278, 1.111, 1.056, 1.063, 1.278, 0.883]}, {"time": 1.5, "x": 1.111, "y": 0.883, "curve": [1.722, 1.111, 1.944, 0.945, 1.722, 0.883, 1.944, 1.063]}, {"time": 2.1667, "x": 0.945, "y": 1.063, "curve": [2.389, 0.945, 2.611, 1.111, 2.389, 1.063, 2.611, 0.883]}, {"time": 2.8333, "x": 1.111, "y": 0.883, "curve": [3.056, 1.111, 3.278, 0.945, 3.056, 0.883, 3.278, 1.063]}, {"time": 3.5, "x": 0.945, "y": 1.063, "curve": [3.722, 0.945, 3.944, 1.111, 3.722, 1.063, 3.944, 0.883]}, {"time": 4.1667, "x": 1.111, "y": 0.883, "curve": [4.389, 1.111, 4.611, 0.945, 4.389, 0.883, 4.611, 1.063]}, {"time": 4.8333, "x": 0.945, "y": 1.063, "curve": [5.056, 0.945, 5.278, 1.111, 5.056, 1.063, 5.278, 0.883]}, {"time": 5.5, "x": 1.111, "y": 0.883, "curve": [5.722, 1.111, 5.944, 0.945, 5.722, 0.883, 5.944, 1.063]}, {"time": 6.1667, "x": 0.945, "y": 1.063, "curve": [6.333, 0.945, 6.5, 1.111, 6.333, 1.063, 6.5, 0.883]}, {"time": 6.6667, "x": 1.111, "y": 0.883, "curve": [6.833, 1.111, 7, 0.945, 6.833, 0.883, 7, 1.063]}, {"time": 7.1667, "x": 0.945, "y": 1.063, "curve": [7.389, 0.945, 7.611, 1.111, 7.389, 1.063, 7.611, 0.883]}, {"time": 7.8333, "x": 1.111, "y": 0.883, "curve": [8.056, 1.111, 8.278, 0.945, 8.056, 0.883, 8.278, 1.063]}, {"time": 8.5, "x": 0.945, "y": 1.063, "curve": [8.722, 0.945, 8.944, 1.111, 8.722, 1.063, 8.944, 0.883]}, {"time": 9.1667, "x": 1.111, "y": 0.883, "curve": [9.389, 1.111, 9.611, 0.945, 9.389, 0.883, 9.611, 1.063]}, {"time": 9.8333, "x": 0.945, "y": 1.063, "curve": [10.056, 0.945, 10.278, 1.111, 10.056, 1.063, 10.278, 0.883]}, {"time": 10.5, "x": 1.111, "y": 0.883, "curve": [10.722, 1.111, 10.944, 0.945, 10.722, 0.883, 10.944, 1.063]}, {"time": 11.1667, "x": 0.945, "y": 1.063, "curve": [11.389, 0.945, 11.611, 1.111, 11.389, 1.063, 11.611, 0.883]}, {"time": 11.8333, "x": 1.111, "y": 0.883, "curve": [12.056, 1.111, 12.278, 0.945, 12.056, 0.883, 12.278, 1.063]}, {"time": 12.5, "x": 0.945, "y": 1.063, "curve": [12.667, 0.945, 12.833, 1.037, 12.667, 1.063, 12.833, 0.963]}, {"time": 13, "x": 1.084, "y": 0.912}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "vertices": [-16.55725, -0.15225, -16.5542, -0.1524, -11.43481, 0.34769, -11.43213, 0.34764, -5.96875, 1.10834, -5.96704, 1.10832, -1.36646, 0.04216, -1.36548, 0.04218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.07886, 0.3555, -3.07837, 0.35541, -9.24158, 0.31439, -9.23926, 0.31424, -13.91931, 0.66333, -13.9165, 0.66317, -15.94934, 0.43523, -15.94604, 0.43508, 0, 0, 0, 0, -5.75562, -0.01303, -5.75439, -0.0132, -10.47375, 0.23726, -10.47046, 0.23702, -15.20532, 0.67839, -15.20239, 0.67824, -18.23926, 0.6651, -18.2356, 0.66517, -17.3551, -0.14136, -17.35181, -0.14155, -14.74072, 0.90228, -14.73828, 0.90219, 0, 0, 0, 0, -8.75537, 0.35565, -8.75342, 0.35556], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 5.8333, "curve": [5.889, 0, 5.944, 1]}, {"time": 6, "vertices": [-16.55725, -0.15225, -16.5542, -0.1524, -11.43481, 0.34769, -11.43213, 0.34764, -5.96875, 1.10834, -5.96704, 1.10832, -1.36646, 0.04216, -1.36548, 0.04218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.07886, 0.3555, -3.07837, 0.35541, -9.24158, 0.31439, -9.23926, 0.31424, -13.91931, 0.66333, -13.9165, 0.66317, -15.94934, 0.43523, -15.94604, 0.43508, 0, 0, 0, 0, -5.75562, -0.01303, -5.75439, -0.0132, -10.47375, 0.23726, -10.47046, 0.23702, -15.20532, 0.67839, -15.20239, 0.67824, -18.23926, 0.6651, -18.2356, 0.66517, -17.3551, -0.14136, -17.35181, -0.14155, -14.74072, 0.90228, -14.73828, 0.90219, 0, 0, 0, 0, -8.75537, 0.35565, -8.75342, 0.35556], "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "curve": "stepped"}, {"time": 9.3333, "curve": [9.389, 0, 9.444, 1]}, {"time": 9.5, "vertices": [-16.55725, -0.15225, -16.5542, -0.1524, -11.43481, 0.34769, -11.43213, 0.34764, -5.96875, 1.10834, -5.96704, 1.10832, -1.36646, 0.04216, -1.36548, 0.04218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.07886, 0.3555, -3.07837, 0.35541, -9.24158, 0.31439, -9.23926, 0.31424, -13.91931, 0.66333, -13.9165, 0.66317, -15.94934, 0.43523, -15.94604, 0.43508, 0, 0, 0, 0, -5.75562, -0.01303, -5.75439, -0.0132, -10.47375, 0.23726, -10.47046, 0.23702, -15.20532, 0.67839, -15.20239, 0.67824, -18.23926, 0.6651, -18.2356, 0.66517, -17.3551, -0.14136, -17.35181, -0.14155, -14.74072, 0.90228, -14.73828, 0.90219, 0, 0, 0, 0, -8.75537, 0.35565, -8.75342, 0.35556], "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "vertices": [-18.15942, 0.93494, -18.14893, 0.93503, -18.38342, 1.9429, -18.38013, 1.94275, -14.43335, 2.76544, -14.43091, 2.76525, -9.00708, 2.13928, -9.0061, 2.13914, -3.06665, 0.46494, -3.06787, 0.46487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33655, 0.38416, -1.33472, 0.38419, -2.13684, 1.20618, -2.1333, 1.20613, -4.98108, 0.2132, -4.9729, 0.21314, -11.54163, 1.58566, -11.53271, 1.58585, 0, 0, 0, 0, -9.41626, 1.85138, -9.41235, 1.8515, -15.87378, 2.2397, -15.86377, 2.23986, -18.90723, 1.3161, -18.89722, 1.31623, -18.56189, 2.07481, -18.55908, 2.07469, -15.72729, 2.00421, -15.72266, 2.00396, -10.54321, 1.4792, -10.53931, 1.47897, -5.56604, 0.6633, -5.56616, 0.6632], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 5.8333, "curve": [5.889, 0, 5.944, 1]}, {"time": 6, "vertices": [-18.15942, 0.93494, -18.14893, 0.93503, -18.38342, 1.9429, -18.38013, 1.94275, -14.43335, 2.76544, -14.43091, 2.76525, -9.00708, 2.13928, -9.0061, 2.13914, -3.06665, 0.46494, -3.06787, 0.46487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33655, 0.38416, -1.33472, 0.38419, -2.13684, 1.20618, -2.1333, 1.20613, -4.98108, 0.2132, -4.9729, 0.21314, -11.54163, 1.58566, -11.53271, 1.58585, 0, 0, 0, 0, -9.41626, 1.85138, -9.41235, 1.8515, -15.87378, 2.2397, -15.86377, 2.23986, -18.90723, 1.3161, -18.89722, 1.31623, -18.56189, 2.07481, -18.55908, 2.07469, -15.72729, 2.00421, -15.72266, 2.00396, -10.54321, 1.4792, -10.53931, 1.47897, -5.56604, 0.6633, -5.56616, 0.6632], "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "curve": "stepped"}, {"time": 9.3333, "curve": [9.389, 0, 9.444, 1]}, {"time": 9.5, "vertices": [-18.15942, 0.93494, -18.14893, 0.93503, -18.38342, 1.9429, -18.38013, 1.94275, -14.43335, 2.76544, -14.43091, 2.76525, -9.00708, 2.13928, -9.0061, 2.13914, -3.06665, 0.46494, -3.06787, 0.46487, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33655, 0.38416, -1.33472, 0.38419, -2.13684, 1.20618, -2.1333, 1.20613, -4.98108, 0.2132, -4.9729, 0.21314, -11.54163, 1.58566, -11.53271, 1.58585, 0, 0, 0, 0, -9.41626, 1.85138, -9.41235, 1.8515, -15.87378, 2.2397, -15.86377, 2.23986, -18.90723, 1.3161, -18.89722, 1.31623, -18.56189, 2.07481, -18.55908, 2.07469, -15.72729, 2.00421, -15.72266, 2.00396, -10.54321, 1.4792, -10.53931, 1.47897, -5.56604, 0.6633, -5.56616, 0.6632], "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667}]}}, "head": {"head": {"deform": [{"time": 1.6667, "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "offset": 432, "vertices": [-5.52686, 1.04739, -5.52563, 1.04735, -13.85645, 3.20006, -13.85425, 3.19994, -18.85376, 2.20634, -18.84814, 2.20616, -17.43054, 2.30426, -17.42847, 2.30418, -13.38879, 2.06364, -13.3855, 2.06349, -2.99121, 0.05356, -2.98901, 0.05346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.17468, 1.77388, -8.17187, 1.77377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.052, 0.00751, -3.05029, 0.00748, -8.67236, 0.86421, -8.67041, 0.86423, -14.40845, 1.606, -14.40601, 1.60603, -17.79346, 1.21017, -17.79004, 1.21015, -13.11487, 1.69525, -13.1123, 1.69525, -3.74158, 0.06696, -3.74072, 0.06693, 0, 0, 0, 0, 0, 0, 0, 0, -17.15796, 1.41647, -17.15259, 1.4164], "curve": [1.889, 0, 1.944, 1]}, {"time": 2, "curve": "stepped"}, {"time": 5.8333, "curve": [5.889, 0, 5.944, 1]}, {"time": 6, "offset": 432, "vertices": [-5.52686, 1.04739, -5.52563, 1.04735, -13.85645, 3.20006, -13.85425, 3.19994, -18.85376, 2.20634, -18.84814, 2.20616, -17.43054, 2.30426, -17.42847, 2.30418, -13.38879, 2.06364, -13.3855, 2.06349, -2.99121, 0.05356, -2.98901, 0.05346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.17468, 1.77388, -8.17187, 1.77377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.052, 0.00751, -3.05029, 0.00748, -8.67236, 0.86421, -8.67041, 0.86423, -14.40845, 1.606, -14.40601, 1.60603, -17.79346, 1.21017, -17.79004, 1.21015, -13.11487, 1.69525, -13.1123, 1.69525, -3.74158, 0.06696, -3.74072, 0.06693, 0, 0, 0, 0, 0, 0, 0, 0, -17.15796, 1.41647, -17.15259, 1.4164], "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "curve": "stepped"}, {"time": 9.3333, "curve": [9.389, 0, 9.444, 1]}, {"time": 9.5, "offset": 432, "vertices": [-5.52686, 1.04739, -5.52563, 1.04735, -13.85645, 3.20006, -13.85425, 3.19994, -18.85376, 2.20634, -18.84814, 2.20616, -17.43054, 2.30426, -17.42847, 2.30418, -13.38879, 2.06364, -13.3855, 2.06349, -2.99121, 0.05356, -2.98901, 0.05346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.17468, 1.77388, -8.17187, 1.77377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.052, 0.00751, -3.05029, 0.00748, -8.67236, 0.86421, -8.67041, 0.86423, -14.40845, 1.606, -14.40601, 1.60603, -17.79346, 1.21017, -17.79004, 1.21015, -13.11487, 1.69525, -13.1123, 1.69525, -3.74158, 0.06696, -3.74072, 0.06693, 0, 0, 0, 0, 0, 0, 0, 0, -17.15796, 1.41647, -17.15259, 1.4164], "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667}]}}, "head2": {"head": {"deform": [{"time": 5.8333, "curve": [5.944, 0, 6.056, 1]}, {"time": 6.1667, "offset": 104, "vertices": [-3.07874, 0.18143, -3.07178, 0.17979, -2.29089, 0.09711, -2.2832, 0.09531, -0.70349, 0.32533, -0.70264, 0.32497, -0.06482, 0.82455, -0.06567, 0.82422, 0.01672, 1.87602, 0.01514, 1.87534, 0.21619, 2.0369, 0.2146, 2.03624, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 1.34802, 1.99478, 1.34644, 1.99411, 1.17603, 0.953, 1.17529, 0.95262, 0.70703, 0.10571, 0.70728, 0.10548, 0.14978, -0.01604, 0.1499, -0.01608, 0.59827, -0.06401, 0.59888, -0.06421, 0.72534, -0.28984, 0.72534, -0.2901, 1.45728, -0.51959, 1.45752, -0.52014, 0.84583, -1.15063, 0.84546, -1.15115, 0.48706, -1.11227, 0.48657, -1.11263, 0.48706, -1.11227, 0.48657, -1.11263, 0.21667, -1.08333, 0.21606, -1.08372, -1.14001, -0.46692, -1.13989, -0.46744, -2.42542, 0.21747, -2.42358, 0.21678, -3.77942, -0.12497, -3.77222, -0.12691, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.39539, 0.86639, 0.39478, 0.86617, -3.07874, 0.18143, -3.07178, 0.17979, 0.88245, -0.24593, 0.88281, -0.24612, -3.81238, 0.26001, -3.8042, 0.25813, -3.07874, 0.18143, -3.07178, 0.17979, 0.26746, -0.33153, 0.26733, -0.33165, 0.88245, -0.24593, 0.88281, -0.24612, 0.88245, -0.24593, 0.88281, -0.24612, 0.25146, -0.48126, 0.25122, -0.48143, 0.48706, -1.11227, 0.48657, -1.11263, 0.48706, -1.11227, 0.48657, -1.11263, 0.44922, -0.0481, 0.44946, -0.04819, 0.44922, -0.0481, 0.44946, -0.04819, 0.44922, -0.0481, 0.44946, -0.04819], "curve": "stepped"}, {"time": 9.3333, "offset": 104, "vertices": [-3.07874, 0.18143, -3.07178, 0.17979, -2.29089, 0.09711, -2.2832, 0.09531, -0.70349, 0.32533, -0.70264, 0.32497, -0.06482, 0.82455, -0.06567, 0.82422, 0.01672, 1.87602, 0.01514, 1.87534, 0.21619, 2.0369, 0.2146, 2.03624, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 1.34802, 1.99478, 1.34644, 1.99411, 1.17603, 0.953, 1.17529, 0.95262, 0.70703, 0.10571, 0.70728, 0.10548, 0.14978, -0.01604, 0.1499, -0.01608, 0.59827, -0.06401, 0.59888, -0.06421, 0.72534, -0.28984, 0.72534, -0.2901, 1.45728, -0.51959, 1.45752, -0.52014, 0.84583, -1.15063, 0.84546, -1.15115, 0.48706, -1.11227, 0.48657, -1.11263, 0.48706, -1.11227, 0.48657, -1.11263, 0.21667, -1.08333, 0.21606, -1.08372, -1.14001, -0.46692, -1.13989, -0.46744, -2.42542, 0.21747, -2.42358, 0.21678, -3.77942, -0.12497, -3.77222, -0.12691, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.39539, 0.86639, 0.39478, 0.86617, -3.07874, 0.18143, -3.07178, 0.17979, 0.88245, -0.24593, 0.88281, -0.24612, -3.81238, 0.26001, -3.8042, 0.25813, -3.07874, 0.18143, -3.07178, 0.17979, 0.26746, -0.33153, 0.26733, -0.33165, 0.88245, -0.24593, 0.88281, -0.24612, 0.88245, -0.24593, 0.88281, -0.24612, 0.25146, -0.48126, 0.25122, -0.48143, 0.48706, -1.11227, 0.48657, -1.11263, 0.48706, -1.11227, 0.48657, -1.11263, 0.44922, -0.0481, 0.44946, -0.04819, 0.44922, -0.0481, 0.44946, -0.04819, 0.44922, -0.0481, 0.44946, -0.04819], "curve": [9.5, 0, 9.667, 1]}, {"time": 9.8333}]}}}}}, "touch": {"bones": {"All": {"translate": [{"x": 10.95, "curve": [0.022, 10.95, 0.047, -27.84, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -27.84, "curve": [0.544, -27.84, 0.856, 10.95, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": 10.95}]}, "All2": {"translate": [{"y": -19.63, "curve": [0.078, 0, 0.156, 0, 0.022, -19.63, 0.047, 42.24]}, {"time": 0.2333, "y": 42.24, "curve": [0.731, 0, 0.856, 0, 0.544, 42.24, 0.856, -19.63]}, {"time": 1.1667, "y": -19.63}]}, "body": {"rotate": [{"value": 2.99, "curve": [0.022, 2.99, 0.047, -1.32]}, {"time": 0.2333, "value": -1.32, "curve": [0.544, -1.32, 0.856, 2.99]}, {"time": 1.1667, "value": 2.99}], "translate": [{"y": -15.04, "curve": [0.078, 0, 0.156, 0, 0.022, -15.04, 0.047, 21.26]}, {"time": 0.2333, "y": 21.26, "curve": [0.731, 0, 0.856, 0, 0.544, 21.26, 0.856, -15.04]}, {"time": 1.1667, "y": -15.04}], "scale": [{"y": 1.042, "curve": [0.078, 1, 0.156, 1, 0.022, 1.042, 0.047, 0.926]}, {"time": 0.2333, "y": 0.926, "curve": [0.731, 1, 0.856, 1, 0.544, 0.926, 0.856, 1.042]}, {"time": 1.1667, "y": 1.042}]}, "body2": {"rotate": [{"value": -1.78, "curve": [0.025, -1.78, 0.053, 2.95]}, {"time": 0.2667, "value": 2.95, "curve": [0.567, 2.95, 0.867, -1.78]}, {"time": 1.1667, "value": -1.78}], "translate": [{"x": -8.35, "curve": [0.025, -8.35, 0.053, 24.08, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 24.08, "curve": [0.567, 24.08, 0.867, -8.35, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -8.35}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.025, 1.004, 0.053, 1.026, 0.025, 1.004, 0.053, 1.026]}, {"time": 0.2667, "x": 1.026, "y": 1.026, "curve": [0.567, 1.026, 0.867, 1.004, 0.567, 1.026, 0.867, 1.004]}, {"time": 1.1667, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.1, "curve": [0.028, -1.1, 0.06, 7.07]}, {"time": 0.3, "value": 7.07, "curve": [0.589, 7.07, 0.878, -1.1]}, {"time": 1.1667, "value": -1.1}]}, "head": {"rotate": [{"value": 0.04, "curve": [0.031, 0.04, 0.067, 7.07]}, {"time": 0.3333, "value": 7.07, "curve": [0.611, 7.07, 0.889, 0.04]}, {"time": 1.1667, "value": 0.04}]}, "eyebrow_L3": {"translate": [{"curve": [0.031, 0, 0.067, -6.31, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -6.31, "curve": [0.611, -6.31, 0.889, 0, 1.389, 0, 0.889, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.031, 0, 0.067, -14.41]}, {"time": 0.3333, "value": -14.41, "curve": [0.611, -14.41, 0.889, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.031, 1, 0.067, 0.966, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.966, "curve": [0.611, 0.966, 0.889, 1, 0.611, 1, 0.889, 1]}, {"time": 1.1667}]}, "eyebrow_L": {"rotate": [{"curve": [0.031, 0, 0.067, -9]}, {"time": 0.3333, "value": -9, "curve": [0.611, -9, 0.889, 0]}, {"time": 1.1667}]}, "eyebrow_R3": {"translate": [{"curve": [0.031, 0, 0.067, -6.31, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -6.31, "curve": [0.611, -6.31, 0.889, 0, 1.389, 0, 0.889, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.031, 0, 0.067, 13.36]}, {"time": 0.3333, "value": 13.36, "curve": [0.611, 13.36, 0.889, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.031, 1, 0.067, 0.966, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.966, "curve": [0.611, 0.966, 0.889, 1, 0.611, 1, 0.889, 1]}, {"time": 1.1667}]}, "eyebrow_R": {"rotate": [{"curve": [0.031, 0, 0.067, 6.77]}, {"time": 0.3333, "value": 6.77, "curve": [0.611, 6.77, 0.889, 0]}, {"time": 1.1667}]}, "sh_R": {"rotate": [{}]}, "arm_R": {"rotate": [{}]}, "arm_R2": {"rotate": [{}]}, "sh_L": {"rotate": [{}]}, "tun": {"rotate": [{"value": 3.29, "curve": [0.022, 3.29, 0.047, -1.32]}, {"time": 0.2333, "value": -1.32, "curve": [0.544, -1.32, 0.856, 3.29]}, {"time": 1.1667, "value": 3.29}]}, "hand_L": {"rotate": [{"value": 4.32, "curve": [0.05, 5.21, 0.085, 5.87]}, {"time": 0.1333, "value": 5.87, "curve": [0.329, 5.87, 0.538, -3.8]}, {"time": 0.7333, "value": -3.8, "curve": [0.881, -3.8, 1.021, 1.61]}, {"time": 1.1667, "value": 4.32}]}, "hand_L3": {"rotate": [{"value": 1.03, "curve": [0.099, 3.43, 0.203, 5.87]}, {"time": 0.3, "value": 5.87, "curve": [0.496, 5.87, 0.671, -3.8]}, {"time": 0.8667, "value": -3.8, "curve": [0.965, -3.8, 1.07, -1.4]}, {"time": 1.1667, "value": 1.03}]}, "arm_L": {"rotate": [{}]}, "leg_R3": {"rotate": [{}]}, "leg_RR": {"rotate": [{"value": 0.73}]}, "leg_RR2": {"rotate": [{"value": -1.62}]}, "sh_R2": {"translate": [{"x": -4.32, "y": 0.75, "curve": [0.025, -4.32, 0.053, 15.35, 0.025, 0.75, 0.053, -8.06]}, {"time": 0.2667, "x": 15.35, "y": -8.06, "curve": [0.567, 15.35, 0.867, -4.32, 0.567, -8.06, 0.867, 0.75]}, {"time": 1.1667, "x": -4.32, "y": 0.75}]}, "sh_L2": {"translate": [{"x": -4.32, "y": 0.75, "curve": [0.025, -4.32, 0.053, 12.29, 0.025, 0.75, 0.053, -4.07]}, {"time": 0.2667, "x": 12.29, "y": -4.07, "curve": [0.567, 12.29, 0.867, -4.32, 0.567, -4.07, 0.867, 0.75]}, {"time": 1.1667, "x": -4.32, "y": 0.75}]}, "arm_RR": {"rotate": [{"value": -0.77}]}, "arm_RR2": {"rotate": [{"value": 1.63}]}, "arm_L2": {"translate": [{"x": -13.58, "y": -0.61, "curve": [0.031, -13.58, 0.067, 12.52, 0.031, -0.61, 0.067, -1.71]}, {"time": 0.3333, "x": 12.52, "y": -1.71, "curve": [0.611, 12.52, 0.889, -13.58, 0.611, -1.71, 0.889, -0.61]}, {"time": 1.1667, "x": -13.58, "y": -0.61}]}, "headround3": {"translate": [{"x": -35.75, "curve": [0.031, -35.75, 0.067, -588.5, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -588.5, "curve": [0.611, -588.5, 0.889, -35.74, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": -35.75}]}, "headround": {"translate": [{"y": -129.72, "curve": [0.111, 0, 0.222, 0, 0.031, -129.72, 0.067, -573.69]}, {"time": 0.3333, "y": -573.69, "curve": [0.611, 0, 0.889, 0, 0.611, -573.69, 0.889, -129.71]}, {"time": 1.1667, "y": -129.72}]}, "bodyround": {"translate": [{"y": 123.52, "curve": [0.111, 0, 0.222, 0, 0.031, 123.52, 0.067, -531.29]}, {"time": 0.3333, "y": -531.29, "curve": [0.611, 0, 0.889, 0, 0.611, -531.29, 0.889, 123.53]}, {"time": 1.1667, "y": 123.52}]}, "tunround": {"translate": [{"x": -354.42, "curve": [0.031, -354.42, 0.067, 534.82, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 534.82, "curve": [0.611, 534.82, 0.889, -354.44, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": -354.42}]}, "body3": {"translate": [{"x": -13.02, "y": 2.11, "curve": [0.073, -24.12, 0.161, -33.23, 0.073, 3.92, 0.161, -7.6]}, {"time": 0.2333, "x": -33.23, "y": -7.6, "curve": [0.427, -33.23, 0.607, 30.3, 0.427, -7.6, 0.607, 20.89]}, {"time": 0.8, "x": 30.32, "y": 20.88, "curve": [0.921, 30.33, 1.044, 5.52, 0.921, 20.88, 1.044, -0.9]}, {"time": 1.1667, "x": -13.02, "y": 2.11}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.073, 1.038, 0.161, 0.945, 0.073, 0.963, 0.161, 1.063]}, {"time": 0.2333, "x": 0.945, "y": 1.063, "curve": [0.33, 0.945, 0.403, 1.111, 0.33, 1.063, 0.403, 0.883]}, {"time": 0.5, "x": 1.111, "y": 0.883, "curve": [0.597, 1.111, 0.703, 0.945, 0.597, 0.883, 0.703, 1.063]}, {"time": 0.8, "x": 0.945, "y": 1.063, "curve": [0.897, 0.945, 1.003, 1.111, 0.897, 1.063, 1.003, 0.883]}, {"time": 1.1, "x": 1.111, "y": 0.883, "curve": [1.125, 1.111, 1.144, 1.098, 1.125, 0.883, 1.144, 0.897]}, {"time": 1.1667, "x": 1.084, "y": 0.912}]}, "body4": {"translate": [{"x": 1.49, "curve": [0.098, -14.59, 0.204, -43.65, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -43.65, "curve": [0.493, -43.65, 0.673, 89.12, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 89.14, "curve": [0.964, 89.15, 1.067, 17.91, 0.964, 0, 1.067, 0]}, {"time": 1.1667, "x": 1.49}], "scale": [{"x": 1.111, "y": 0.883, "curve": [0.097, 1.111, 0.203, 0.945, 0.097, 0.883, 0.203, 1.063]}, {"time": 0.3, "x": 0.945, "y": 1.063, "curve": [0.397, 0.945, 0.47, 1.111, 0.397, 1.063, 0.47, 0.883]}, {"time": 0.5667, "x": 1.111, "y": 0.883, "curve": [0.663, 1.111, 0.77, 0.945, 0.663, 0.883, 0.77, 1.063]}, {"time": 0.8667, "x": 0.945, "y": 1.063, "curve": [0.963, 0.945, 1.067, 1.111, 0.963, 1.063, 1.067, 0.883]}, {"time": 1.1667, "x": 1.111, "y": 0.883}]}, "body5": {"translate": [{"x": 13.04, "curve": [0.122, -5.96, 0.246, -31.25, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -31.25, "curve": [0.56, -31.25, 0.74, 33.67, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 33.69, "curve": [1.006, 33.69, 1.089, 25.21, 1.006, 0, 1.089, 0]}, {"time": 1.1667, "x": 13.04}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.024, 1.1, 0.042, 1.111, 0.024, 0.895, 0.042, 0.883]}, {"time": 0.0667, "x": 1.111, "y": 0.883, "curve": [0.163, 1.111, 0.27, 0.945, 0.163, 0.883, 0.27, 1.063]}, {"time": 0.3667, "x": 0.945, "y": 1.063, "curve": [0.463, 0.945, 0.57, 1.111, 0.463, 1.063, 0.57, 0.883]}, {"time": 0.6667, "x": 1.111, "y": 0.883, "curve": [0.763, 1.111, 0.837, 0.945, 0.763, 0.883, 0.837, 1.063]}, {"time": 0.9333, "x": 0.945, "y": 1.063, "curve": [1.006, 0.945, 1.089, 1.034, 1.006, 1.063, 1.089, 0.966]}, {"time": 1.1667, "x": 1.084, "y": 0.912}]}, "body6": {"translate": [{"x": -13.03, "y": 2.01, "curve": [0.073, -24.15, 0.161, -33.27, 0.073, 3.73, 0.161, -7.86]}, {"time": 0.2333, "x": -33.27, "y": -7.86, "curve": [0.427, -33.27, 0.607, 30.34, 0.427, -7.86, 0.607, 21.12]}, {"time": 0.8, "x": 30.35, "y": 21.12, "curve": [0.921, 30.36, 1.044, 5.53, 0.921, 21.12, 1.044, -0.85]}, {"time": 1.1667, "x": -13.03, "y": 2.01}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.073, 1.038, 0.161, 0.945, 0.073, 0.963, 0.161, 1.063]}, {"time": 0.2333, "x": 0.945, "y": 1.063, "curve": [0.33, 0.945, 0.403, 1.111, 0.33, 1.063, 0.403, 0.883]}, {"time": 0.5, "x": 1.111, "y": 0.883, "curve": [0.597, 1.111, 0.703, 0.945, 0.597, 0.883, 0.703, 1.063]}, {"time": 0.8, "x": 0.945, "y": 1.063, "curve": [0.897, 0.945, 1.003, 1.111, 0.897, 1.063, 1.003, 0.883]}, {"time": 1.1, "x": 1.111, "y": 0.883, "curve": [1.125, 1.111, 1.144, 1.098, 1.125, 0.883, 1.144, 0.897]}, {"time": 1.1667, "x": 1.084, "y": 0.912}]}, "body7": {"translate": [{"x": 1.49, "curve": [0.098, -14.59, 0.204, -43.65, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -43.65, "curve": [0.493, -43.65, 0.673, 89.12, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 89.14, "curve": [0.964, 89.15, 1.067, 17.91, 0.964, 0, 1.067, 0]}, {"time": 1.1667, "x": 1.49}], "scale": [{"x": 1.111, "y": 0.883, "curve": [0.097, 1.111, 0.203, 0.945, 0.097, 0.883, 0.203, 1.063]}, {"time": 0.3, "x": 0.945, "y": 1.063, "curve": [0.397, 0.945, 0.47, 1.111, 0.397, 1.063, 0.47, 0.883]}, {"time": 0.5667, "x": 1.111, "y": 0.883, "curve": [0.663, 1.111, 0.77, 0.945, 0.663, 0.883, 0.77, 1.063]}, {"time": 0.8667, "x": 0.945, "y": 1.063, "curve": [0.963, 0.945, 1.067, 1.111, 0.963, 1.063, 1.067, 0.883]}, {"time": 1.1667, "x": 1.111, "y": 0.883}]}, "body8": {"translate": [{"x": 13.04, "curve": [0.122, -5.96, 0.246, -31.25, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -31.25, "curve": [0.56, -31.25, 0.74, 33.67, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 33.69, "curve": [1.006, 33.69, 1.089, 25.21, 1.006, 0, 1.089, 0]}, {"time": 1.1667, "x": 13.04}], "scale": [{"x": 1.084, "y": 0.912, "curve": [0.024, 1.1, 0.042, 1.111, 0.024, 0.895, 0.042, 0.883]}, {"time": 0.0667, "x": 1.111, "y": 0.883, "curve": [0.163, 1.111, 0.27, 0.945, 0.163, 0.883, 0.27, 1.063]}, {"time": 0.3667, "x": 0.945, "y": 1.063, "curve": [0.463, 0.945, 0.57, 1.111, 0.463, 1.063, 0.57, 0.883]}, {"time": 0.6667, "x": 1.111, "y": 0.883, "curve": [0.763, 1.111, 0.837, 0.945, 0.763, 0.883, 0.837, 1.063]}, {"time": 0.9333, "x": 0.945, "y": 1.063, "curve": [1.006, 0.945, 1.089, 1.034, 1.006, 1.063, 1.089, 0.966]}, {"time": 1.1667, "x": 1.084, "y": 0.912}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.033, 0, 0.076, 0.99]}, {"time": 0.3333, "vertices": [-2.67161, 0.13167, -2.67082, 0.13171, -2.95964, 0.21758, -2.96026, 0.21714, -1.98304, 0.35348, -1.98384, 0.35305, -0.24414, 0.00753, -0.24397, 0.00754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.31108, 0.13987, -0.31123, 0.13992, -0.98161, 0.37105, -0.98205, 0.37109, -1.55918, 0.57658, -1.5599, 0.57662, -2.84961, 0.07776, -2.84902, 0.07773, 0, 0, 0, 0, -0.63637, 0.10256, -0.63627, 0.1025, -1.20175, 0.35727, -1.20202, 0.35729, -2.04713, 0.43609, -2.04746, 0.43612, -2.52229, 0.41461, -2.5225, 0.41474, -2.64191, 0.06044, -2.64169, 0.0604, -3.06922, 0.35817, -3.06952, 0.35809, 0, 0, 0, 0, -1.88863, 0.0495, -1.88865, 0.04937], "curve": [0.611, 0.01, 0.889, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.033, 0, 0.076, 0.99]}, {"time": 0.3333, "vertices": [-3.24448, 0.16704, -3.24261, 0.16706, -3.66426, 0.4559, -3.66428, 0.45587, -3.90102, 1.21547, -3.90254, 1.21488, -2.3047, 0.74141, -2.30538, 0.74108, -0.54791, 0.08307, -0.54813, 0.08306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.2388, 0.06864, -0.23847, 0.06864, -0.38178, 0.2155, -0.38115, 0.21549, -0.88995, 0.03809, -0.88849, 0.03808, -2.0621, 0.2833, -2.06051, 0.28334, 0, 0, 0, 0, -1.68237, 0.33078, -1.68167, 0.3308, -2.83611, 0.40016, -2.83432, 0.40019, -4.04215, 0.42551, -4.04134, 0.42523, -4.73973, 0.77872, -4.74216, 0.77827, -4.45484, 0.8294, -4.45682, 0.82899, -2.47393, 0.43347, -2.47409, 0.43319, -1.21578, 0.18199, -1.21617, 0.18185], "curve": [0.611, 0.01, 0.889, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.033, 0, 0.076, 0.99]}, {"time": 0.3333, "offset": 432, "vertices": [-2.14701, 0.0401, -2.14789, 0.03954, -3.37205, 0.50882, -3.37202, 0.50873, -4.88538, 0.66874, -4.8856, 0.66834, -4.6311, 0.68623, -4.63195, 0.68585, -3.57218, 0.70635, -3.5722, 0.70608, -1.37012, 0.37963, -1.37046, 0.37906, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29624, 0.68699, -2.29647, 0.68642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54529, 0.00134, -0.54499, 0.00134, -1.54946, 0.15441, -1.54911, 0.15441, -2.57431, 0.28694, -2.57387, 0.28694, -2.76857, 0.26837, -2.76882, 0.26785, -2.58904, 0.42975, -2.5898, 0.42984, -1.88346, 0.30334, -1.8866, 0.30358, 0, 0, 0, 0, 0, 0, 0, 0, -3.06555, 0.25308, -3.06459, 0.25306], "curve": [0.611, 0.01, 0.889, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.033, 0, 0.076, 0.99]}, {"time": 0.3333, "offset": 88, "vertices": [0.61926, -0.05038, 0.61914, -0.05066, 0.61926, -0.05038, 0.61914, -0.05066, 0, 0, 0, 0, 0, 0, 0, 0, -3.07874, 0.18143, -3.07178, 0.17979, -2.29089, 0.09711, -2.2832, 0.09531, -0.70349, 0.32533, -0.70264, 0.32497, -0.99902, 0.64963, -1.00049, 0.64917, -0.44043, 1.94463, -0.44458, 1.94382, -0.24097, 2.1055, -0.24512, 2.10472, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.95166, 1.8354, 0.95068, 1.83466, 2.21216, 1.98978, 2.21094, 1.98862, 1.72827, 0.6684, 1.72754, 0.66783, 2.8092, -0.62231, 2.80713, -0.62242, 2.28564, -0.62688, 2.2832, -0.62704, 2.49976, -0.60783, 2.4978, -0.60803, 1.83606, 0.79744, 1.83569, 0.79694, 1.97949, 1.71866, 1.98096, 1.71765, 0.35327, 1.04636, 0.35376, 1.04538, -0.00549, 1.08472, -0.00513, 1.08391, -0.00549, 1.08472, -0.00513, 1.08391, -0.58228, 1.20137, -0.58228, 1.20033, -1.55872, 1.57373, -1.55786, 1.57236, -2.42542, 0.21747, -2.42358, 0.21678, -3.77942, -0.12497, -3.77222, -0.12691, 0.6571, 1.89857, 0.65576, 1.89801, 0.6571, 1.89857, 0.65576, 1.89801, 0.39539, 0.86639, 0.39478, 0.86617, -3.07874, 0.18143, -3.07178, 0.17979, 0.88245, -0.24593, 0.88281, -0.24612, -3.81238, 0.26001, -3.8042, 0.25813, -3.07874, 0.18143, -3.07178, 0.17979, 0.24304, 0.23619, 0.24316, 0.23598, 0.88245, -0.24593, 0.88281, -0.24612, 1.17114, -0.32845, 1.17114, -0.32864, 0.12878, 1.04913, 0.12915, 1.04872, -0.00549, 1.08472, -0.00513, 1.08391, -0.00549, 1.08472, -0.00513, 1.08391, 1.93103, -0.09808, 1.92944, -0.09824, 3.23987, -0.09863, 3.23706, -0.09885, 2.47424, 0.31372, 2.47241, 0.31326], "curve": [0.611, 0.01, 0.889, 1]}, {"time": 1.1667}]}}}}}}}