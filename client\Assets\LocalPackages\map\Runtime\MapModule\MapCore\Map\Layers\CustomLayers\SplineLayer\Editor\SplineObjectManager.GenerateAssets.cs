﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public partial class SplineObjectManager
    {
        public void GenerateAssets()
        {
            string assetFolder = MapCoreDef.GetGeneratedSplineAssetFolderPath(SLGMakerEditor.instance.exportFolder);
            Utils.ClearFolderContent(assetFolder);

            if (!Directory.Exists(assetFolder))
            {
                Directory.CreateDirectory(assetFolder);
            }

            for (int i = 0; i < mSplineObjects.Count; ++i)
            {
                if (mSplineObjects[i].attributes.HasFlag(SplineObject.Attribute.Shadow))
                {
                    continue;
                }
                List<SplineObject.ExportSplineSegmentInfo> segments = SplitSpline(i, mMaxExportSegmentLength);
                mSplineObjects[i].segments = segments;
                for (int s = 0; s < segments.Count; ++s)
                {
                    string prefix = $"{assetFolder}/spline_{segments[s].splineIndex}_{segments[s].startControlPointIndex}_{segments[s].endControlPointIndex}";
                    segments[s].prefabPath = prefix + ".prefab";
                    GenerateAssetsForSplineSegment(segments[s], prefix);
                }
            }
            //必须强制保存一次
            SLGMakerEditor.instance.SaveMap();
        }

        //将一条spline分成多段,方便动态加载视野内的spline
        //maxExportSegmentLength: 一段的提示长度
        List<SplineObject.ExportSplineSegmentInfo> SplitSpline(int splineIndex, float maxExportSegmentLength)
        {
            var spline = mSplineObjects[splineIndex];
            List<SplineObject.ExportSplineSegmentInfo> segments = new List<SplineObject.ExportSplineSegmentInfo>();
            var controlPoints = spline.controlPoints;
            //int virtualControlPointCount = controlPoints.Count + (spline.isLoop ? 1 : 0);
            int virtualControlPointCount = controlPoints.Count;
            float totalLengthSoFar = 0;
            List<int> segmentEndIndices = new List<int>();

            if (maxExportSegmentLength > 0)
            {
                for (int p = 0; p < virtualControlPointCount - 1; ++p)
                {
                    var cur = controlPoints[p].pos;
                    int nextIndex = (p + 1) % controlPoints.Count;
                    var next = controlPoints[nextIndex].pos;
                    var distance = (next - cur).magnitude;
                    if (totalLengthSoFar + distance >= maxExportSegmentLength)
                    {
                        segmentEndIndices.Add(p + 1);
                        totalLengthSoFar = 0;
                    }

                    totalLengthSoFar += distance;
                }
            }

            if (segmentEndIndices.Count == 0 || segmentEndIndices[segmentEndIndices.Count - 1] != virtualControlPointCount - 1)
            {
                segmentEndIndices.Add(virtualControlPointCount - 1);
            }

            int lastEndIndex = 0;
            for (int s = 0; s < segmentEndIndices.Count; ++s)
            {
                var si = new SplineObject.ExportSplineSegmentInfo();
                si.startControlPointIndex = lastEndIndex;
                si.endControlPointIndex = segmentEndIndices[s];
                lastEndIndex = si.endControlPointIndex;
                si.splineIndex = splineIndex;
                segments.Add(si);
            }

            return segments;
        }

        //生成spline某一段的prefab
        void GenerateAssetsForSplineSegment(SplineObject.ExportSplineSegmentInfo si, string path)
        {
            si.position = GenerateAssetsForOneSpline(mSplineObjects[si.splineIndex], path, false, si.startControlPointIndex, si.endControlPointIndex);
        }

        public Vector3 GenerateAssetsForOneSpline(SplineObject spline, string path, bool worldSpace, int startControlPointIndex, int endControlPointIndex)
        {
            var nameWithoutExt = Utils.RemoveExtension(path);
            var newObj = Object.Instantiate<GameObject>(spline.meshGameObject);
            //save mesh
            string meshPath = nameWithoutExt + ".asset";
            var mesh = newObj.GetComponent<MeshFilter>().sharedMesh;
            Vector3 center = Vector3.zero;
            if (!worldSpace)
            {
                mesh = CreateLocalMesh(spline, startControlPointIndex, endControlPointIndex, mesh, out center);
            }
            AssetDatabase.CreateAsset(mesh, meshPath);
            newObj.GetComponent<MeshFilter>().sharedMesh = mesh;

            //save prefab
            string prefabPath = nameWithoutExt + ".prefab";
            PrefabUtility.SaveAsPrefabAsset(newObj, prefabPath);

            UnityEngine.Object.DestroyImmediate(newObj);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            return center;
        }

        Mesh CreateLocalMesh(SplineObject spline, int startControlPointIndex, int endControlPointIndex, Mesh originalMesh, out Vector3 center)
        {
            var originalVertices = originalMesh.vertices;
            var originalUVs = originalMesh.uv;
            var originalUV2s = originalMesh.uv2;
            var originalColors = originalMesh.colors;

            center = Utils.CalculateCentroid(originalVertices);

            int startVertexIndex = spline.GetVertexIndex(startControlPointIndex);
            int endVertexIndex = spline.GetVertexIndex(endControlPointIndex);
            int vertexCount = endVertexIndex - startVertexIndex + 1;
            Vector3[] segmentVertices = new Vector3[vertexCount];
            Vector2[] segmentUVs = new Vector2[vertexCount];
            Vector2[] segmentUV2s = new Vector2[vertexCount];
            Color[] segmentColors = new Color[vertexCount];
            int idx = 0;
            for (int i = startVertexIndex; i <= endVertexIndex; ++i)
            {
                segmentVertices[idx] = originalVertices[i] - center;
                segmentUVs[idx] = originalUVs[i];
                if (originalUV2s.Length > 0)
                {
                    segmentUV2s[idx] = originalUV2s[i];
                }
                segmentColors[idx] = originalColors[i];
                ++idx;
            }

            int segmentCount = vertexCount / 2 - 1;
            int[] segmentIndices = new int[segmentCount * 6];
            for (int s = 0; s < segmentCount; ++s)
            {
                int offset = s * 2;
                segmentIndices[s * 6] = offset;
                segmentIndices[s * 6 + 1] = 1 + offset;
                segmentIndices[s * 6 + 2] = 2 + offset;
                segmentIndices[s * 6 + 3] = 1 + offset;
                segmentIndices[s * 6 + 4] = 3 + offset;
                segmentIndices[s * 6 + 5] = 2 + offset;
            }

            Mesh mesh = new Mesh();
            mesh.vertices = segmentVertices;
            mesh.uv = segmentUVs;
            mesh.uv2 = segmentUV2s;
            mesh.colors = segmentColors;
            mesh.triangles = segmentIndices;
            return mesh;
        }

    }
}

#endif