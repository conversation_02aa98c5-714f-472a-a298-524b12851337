%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &8861876668509796812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2267092902520544930}
  - component: {fileID: 1186743609371878812}
  m_Layer: 0
  m_Name: ObstacleOutline 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2267092902520544930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8861876668509796812}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1186743609371878812
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8861876668509796812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -84.16678, y: 0, z: -41.0664}
    - {x: -76.04892, y: 0, z: -37.15551}
    - {x: -64.21032, y: 0, z: -43.03161}
    - {x: -61.301384, y: 0, z: -48.888077}
    - {x: -65.99961, y: 0, z: -54.052933}
    - {x: -81.449745, y: 0, z: -51.130215}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -65.8902, y: 0, z: -56.089}
    - {x: -62.8753, y: 0, z: -54.5414}
    - {x: -62.3037, y: 0, z: -54.0319}
    - {x: -59.7623, y: 0, z: -50.227}
    - {x: -59.504, y: 0, z: -49.3644}
    - {x: -59.5146, y: 0, z: -47.8416}
    - {x: -59.6723, y: 0, z: -47.1781}
    - {x: -61.1208, y: 0, z: -44.2518}
    - {x: -61.142, y: 0, z: -44.2104}
    - {x: -62.4137, y: 0, z: -41.8301}
    - {x: -63.1016, y: 0, z: -41.1698}
    - {x: -74.4452, y: 0, z: -35.6941}
    - {x: -74.451, y: 0, z: -35.6913}
    - {x: -75.4445, y: 0, z: -35.217}
    - {x: -76.6852, y: 0, z: -35.1706}
    - {x: -84.8159, y: 0, z: -38.3311}
    - {x: -85.8705, y: 0, z: -39.965}
    - {x: -85.4603, y: 0, z: -47.6022}
    - {x: -85.2071, y: 0, z: -48.3657}
    - {x: -82.4389, y: 0, z: -52.5297}
    - {x: -81.669, y: 0, z: -53.1292}
    - {x: -80.012, y: 0, z: -53.7138}
    - {x: -80.012, y: 0, z: -53.7139}
    - {x: -75.9736, y: 0, z: -55.1391}
    - {x: -75.6467, y: 0, z: -55.2148}
    - {x: -66.7675, y: 0, z: -56.2479}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
