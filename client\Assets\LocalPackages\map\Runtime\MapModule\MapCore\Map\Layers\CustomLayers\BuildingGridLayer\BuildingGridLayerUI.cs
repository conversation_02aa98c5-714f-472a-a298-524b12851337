﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(BuildingGridLayerLogic))]
    public partial class BuildingGridLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as BuildingGridLayerLogic;
            mLogic.UpdateGizmoVisibilityState();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                CreateLayerNames();

                int newLayer = EditorGUILayout.Popup("Layers", mLogic.selectedLayerIndex, mLayerNames);
                if (newLayer != mLogic.selectedLayerIndex)
                {
                    SetLayer(newLayer);
                }
                //EditorGUILayout.BeginHorizontal();
                //if (GUILayout.Button("Add Layer"))
                //{
                //    AddLayer();
                //}
                //if (GUILayout.Button("Remove Layer"))
                //{
                //    RemoveLayer();
                //}
                //EditorGUILayout.EndHorizontal();

                if (GUILayout.Button("Export"))
                {
                    var path = SLGMakerEditor.instance.exportFolder + "/" + MapCoreDef.BUILDING_GRID_LAYER_EXPORT_DATA_FILE_NAME;
                    mLogic.layer.Export(path);
                }

                EditorGUILayout.BeginHorizontal();

                var layer = mLogic.layer;
                var subLayer = layer.layerData.GetLayer(mLogic.selectedLayerIndex);
                if (mLogic.selectedRegionIndex == -1 && subLayer.grids.Count > 0)
                {
                    mLogic.selectedRegionIndex = 0;
                }

                mLogic.brushSize = EditorGUILayout.IntField("Brush Size", mLogic.brushSize);
                mLogic.brushSize = Mathf.Clamp(mLogic.brushSize, 1, 20);
                EditorGUILayout.EndHorizontal();

                DrawBrushes();

                if (GUILayout.Button(new GUIContent("Clear All Grids", "清理所有用Set Brush绘制的格子")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone!", "Yes", "No"))
                    {
                        ClearAllGrids();
                    }
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Grid Width", subLayer.tileWidth.ToString());
                EditorGUILayout.LabelField("Grid Height", subLayer.tileHeight.ToString());
                EditorGUILayout.LabelField("Horizontal Region Count", subLayer.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Vertical Region Count", subLayer.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void AddLayer()
        {
            var layer0 = mLogic.layer.layerData.GetLayer(0);

            var dlg = EditorUtils.CreateInputDialog("Add New Layer");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "New Layer"),
                    new InputDialog.StringItem("Horizontal Tile Count", "", $"{layer0.horizontalTileCount}"),
                    new InputDialog.StringItem("Vertical Tile Count", "", $"{layer0.verticalTileCount}"),
                    new InputDialog.StringItem("Tile Width", "", $"{layer0.tileWidth}"),
                    new InputDialog.StringItem("Tile Height", "", $"{layer0.tileHeight}"),
                };
            dlg.Show(items, OnClickAddLayer);
        }

        bool OnClickAddLayer(List<InputDialog.Item> parameters)
        {
            var nameStr = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(nameStr))
            {
                EditorUtility.DisplayDialog("Error", "Invalid layer name!", "OK");
                return false;
            }

            bool found = mLogic.layer.layerData.HasLayer(nameStr);
            if (found)
            {
                EditorUtility.DisplayDialog("Error", "Name already exists!", "OK");
                return false;
            }


            int horizontalTileCount;
            int verticalTileCount;
            float tileWidth;
            float tileHeight;
            bool suc = int.TryParse((parameters[1] as InputDialog.StringItem).text, out horizontalTileCount);
            suc &= int.TryParse((parameters[2] as InputDialog.StringItem).text, out verticalTileCount);
            suc &= float.TryParse((parameters[3] as InputDialog.StringItem).text, out tileWidth);
            suc &= float.TryParse((parameters[4] as InputDialog.StringItem).text, out tileHeight);
            if (!suc || horizontalTileCount <= 0 || verticalTileCount <= 0 || tileWidth <= 0 || tileHeight <= 0)
            {
                return false;
            }

            var gridIDs = new int[verticalTileCount, horizontalTileCount];
            var layer = new TextureGridLayerData.Layer(nameStr, horizontalTileCount, verticalTileCount, tileWidth, tileHeight, gridIDs, new List<TextureGridData>(), Vector3.zero);
            mLogic.layer.layerData.AddLayer(layer);
            SetLayer(mLogic.layer.layerData.layerCount - 1);
            return true;
        }

        void RemoveLayer()
        {
            var layer = mLogic.layer;
            if (layer.layerData.layerCount > 1)
            {
                if (EditorUtility.DisplayDialog("Warning", "This operation can't be undone, are you sure?", "Yes", "No"))
                {
                    layer.layerData.RemoveLayer(mLogic.selectedLayerIndex);
                    SetLayer(mLogic.selectedLayerIndex - 1);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Must at least have 1 layer!", "OK");
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            var layer = mLogic.layer.layerData;

            var gridStartPos = mLogic.layer.gridStartPosition;
            var subLayer = mLogic.layer.layerData.GetLayer(mLogic.selectedLayerIndex);
            if (mLeftButtonDown)
            {
                bool set = false;
                int type = 0;
                if (mLogic.selectedRegionIndex >= 0)
                {
                    if (!currentEvent.control)
                    {
                        type = subLayer.grids[mLogic.selectedRegionIndex].id;
                    }
                    set = true;
                }
                else if (currentEvent.control)
                {
                    set = true;
                }
                if (set)
                {
                    layer.SetGrid1(mLogic.selectedLayerIndex, pos, mLogic.brushSize, type);
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    mLogic.brushSize = mLogic.brushSize + 1;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    mLogic.brushSize = mLogic.brushSize - 1;
                    Repaint();
                }
            }

            var coord = layer.FromWorldPositionToCoordinate1(mLogic.selectedLayerIndex, pos);
            DrawBrush(coord);

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void DrawBrush(Vector2Int centerCoord)
        {
            int brushSize = mLogic.brushSize;
            int startX = centerCoord.x - brushSize / 2;
            int startY = centerCoord.y - brushSize / 2;
            int endX = startX + brushSize;
            int endY = startY + brushSize;
            var layer = mLogic.layer;
            var startPos = layer.layerData.FromCoordinateToWorldPosition1(mLogic.selectedLayerIndex, startX, startY);
            var endPos = layer.layerData.FromCoordinateToWorldPosition1(mLogic.selectedLayerIndex, endX, endY);

            Handles.color = Color.black;
            Handles.RectangleHandleCap(0, (startPos + endPos) * 0.5f, Quaternion.Euler(90, 0, 0), (endPos - startPos).x * 0.5f, EventType.Repaint);
        }

        void ClearAllGrids()
        {
            var layer = mLogic.layer.layerData;
            var subLayer = layer.GetLayer(mLogic.selectedLayerIndex);
            int horizontalGridCount = subLayer.horizontalTileCount;
            int verticalGridCount = subLayer.verticalTileCount;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    subLayer.gridIDs[i, j] = 0;
                }
            }

            mLogic.layer.layerView.RefreshTexture(mLogic.selectedLayerIndex);
        }

        void DrawBrushes()
        {
            mShowTemplate = EditorGUILayout.Foldout(mShowTemplate, "Brush Setting");
            if (mShowTemplate)
            {
                var layer = mLogic.layer.layerData;
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add"))
                {
                    AddTemplate();
                }
                if (GUILayout.Button("Remove"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure delete this? this action can't be redone!", "Yes", "No"))
                    {
                        RemoveTemplate();
                    }
                }
                if (GUILayout.Button("Change Type"))
                {
                    ChangeTemplateType();
                }
                EditorGUILayout.EndHorizontal();

                var subLayer = layer.GetLayer(mLogic.selectedLayerIndex);
                var templates = subLayer.grids;
                int selectedRegionIndex = -1;
                bool selectionChange = false;
                for (int i = 0; i < templates.Count; ++i)
                {
                    selectionChange = DrawTemplate(templates[i] as BuildingGridData, i);
                    if (selectionChange)
                    {
                        selectedRegionIndex = i;
                    }
                }
                if (selectedRegionIndex >= 0)
                {
                    mLogic.selectedRegionIndex = selectedRegionIndex;
                }
                EditorGUILayout.EndVertical();
            }
        }

        bool DrawTemplate(BuildingGridData template, int i)
        {
            EditorGUIUtility.labelWidth = 40;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal("GroupBox");
            bool nowSelected = mLogic.selectedRegionIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            EditorGUIUtility.fieldWidth = 100;
            EditorGUILayout.IntField("ID", template.id);
            EditorGUIUtility.fieldWidth = 0;

            EditorGUIUtility.fieldWidth = 100;
            var newColor = EditorGUILayout.ColorField("", template.color);
            EditorGUIUtility.fieldWidth = 0;
            if (newColor != template.color)
            {
                template.color = newColor;
            }

            EditorGUIUtility.fieldWidth = 100;
            template.walkable = EditorGUILayout.Toggle("Walkable", template.walkable);
            EditorGUIUtility.fieldWidth = 0;
            if (GUILayout.Button("Change Color"))
            {
                mLogic.layer.layerView.RefreshTexture(mLogic.selectedLayerIndex);
            }
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.labelWidth = 0;

            return selectionChange;
        }

        void AddTemplate()
        {
            var layer = mLogic.layer.layerData;
            var subLayer = layer.GetLayer(mLogic.selectedLayerIndex);
            var templates = subLayer.grids;
            int type = 1;
            if (templates.Count > 0)
            {
                type = templates[templates.Count - 1].id + 1;
            }
            var dlg = EditorUtils.CreateInputDialog("Add Region Brush");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Type", "", type.ToString()),
                };
            dlg.Show(items, OnClickAdd);
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            string typeStr = (parameters[0] as InputDialog.StringItem).text;
            int type;
            bool suc = Utils.ParseInt(typeStr, out type);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }

            if (type <= 0 || type > short.MaxValue)
            {
                EditorUtility.DisplayDialog("Error", $"type must be > 0 and <= {short.MaxValue}", "OK");
                return false;
            }

            var editor = mLogic.layer.layerData;
            var temp = editor.FindGrid(mLogic.selectedLayerIndex, type);
            if (temp != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {type} already existed!", "OK");
                return false;
            }

            var region = new BuildingGridData(type, Color.white, false);
            editor.AddGrid(mLogic.selectedLayerIndex, region);
            var subLayer = editor.GetLayer(mLogic.selectedLayerIndex);
            mLogic.selectedRegionIndex = subLayer.grids.Count - 1;

            return true;
        }

        void RemoveTemplate()
        {
            if (mLogic.selectedRegionIndex >= 0)
            {
                var editor = mLogic.layer.layerData;
                var subLayer = editor.GetLayer(mLogic.selectedLayerIndex);
                var template = subLayer.grids[mLogic.selectedRegionIndex];
                var objects = subLayer.gridIDs;
                int rows = objects.GetLength(0);
                int cols = objects.GetLength(1);
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        if (objects[i, j] == template.id)
                        {
                            objects[i, j] = 0;
                        }
                    }
                }

                editor.RemoveGrid(mLogic.selectedLayerIndex, mLogic.selectedRegionIndex);
                mLogic.selectedRegionIndex = subLayer.grids.Count - 1;

                mLogic.layer.layerView.RefreshTexture(mLogic.selectedLayerIndex);
            }
        }

        void ChangeTemplateType()
        {
            if (mLogic.selectedRegionIndex >= 0)
            {
                var subLayer = mLogic.layer.layerData.GetLayer(mLogic.selectedLayerIndex);
                var templates = subLayer.grids;
                var dlg = EditorUtils.CreateInputDialog("Change Region Type");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Type", "", templates[mLogic.selectedRegionIndex].id.ToString()),
                };
                dlg.Show(items, OnClickChangeTemplateType);
            }
        }

        bool OnClickChangeTemplateType(List<InputDialog.Item> parameters)
        {
            int newType;
            bool suc = Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newType);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }
            if (newType <= 0 || newType > short.MaxValue)
            {
                EditorUtility.DisplayDialog("Error", $"type must be > 0 and <= {short.MaxValue}", "OK");
                return false;
            }

            var editor = mLogic.layer.layerData;
            if (editor.FindGrid(mLogic.selectedLayerIndex, newType) != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {newType} is already used!", "OK");
                return false;
            }

            var subLayer = editor.GetLayer(mLogic.selectedLayerIndex);
            var template = subLayer.grids[mLogic.selectedRegionIndex];
            var objects = subLayer.gridIDs;
            int rows = objects.GetLength(0);
            int cols = objects.GetLength(1);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (objects[i, j] == template.id)
                    {
                        objects[i, j] = newType;
                    }
                }
            }
            template.id = newType;

            return true;
        }

        void SetLayer(int newLayer)
        {
            Debug.Assert(newLayer >= 0 && mLogic.selectedLayerIndex != newLayer);

            mLogic.selectedLayerIndex = newLayer;
            mLogic.layer.SetLayer(newLayer);
            mLogic.selectedRegionIndex = -1;
        }

        void CreateLayerNames()
        {
            var layerData = mLogic.layer.layerData;
            int n = layerData.layerCount;
            if (mLayerNames == null || mLayerNames.Length != n)
            {
                mLayerNames = new string[n];
                for (int i = 0; i < n; ++i)
                {
                    mLayerNames[i] = layerData.GetLayer(i).name;
                }
            }
        }

        BuildingGridLayerLogic mLogic;

        bool mLeftButtonDown = false;
        bool mShowTemplate = true;
        int mBrushStartX;
        int mBrushStartY;
        int mBrushEndX;
        int mBrushEndY;

        string[] mLayerNames;
    }
}

#endif