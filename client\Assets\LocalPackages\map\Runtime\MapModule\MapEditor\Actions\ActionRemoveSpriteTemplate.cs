﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map {
    //删除sprite template的命令
    [Black]
    public class ActionRemoveSpriteTemplate : EditorAction {
        public ActionRemoveSpriteTemplate(int spriteTemplateID) {
            mSpriteTemplateID = spriteTemplateID;

            var map = Map.currentMap;
            int n = map.GetMapLayerCount();
            for (int i = 0; i < n; ++i) {
                var layer = map.GetMapLayerByIndex(i);
                if (layer is SpriteTileLayer) {
                    AddCoordinatesInTiles(layer as SpriteTileLayer);
                }
            }

            var spriteTemplate = Map.currentMap.FindObject(mSpriteTemplateID) as SpriteTemplate;
            mDescription = string.Format("remove sprite template {0}", spriteTemplate.name);
        }

        public override bool Do() {
            var map = Map.currentMap;

            //删除引用了该sprite template的所有tile
            foreach (var p in mSpriteTemplateTiles) {
                var layerData = map.FindObject(p.Key) as SpriteTileLayerData;
                var coordiantes = p.Value;
                foreach (var coord in p.Value) {
                    layerData.ClearTile(coord.x, coord.y);
                }
            }

            //删除sprite template
            var editorMapData = Map.currentMap.data as EditorMapData;
            editorMapData.RemoveSpriteTemplate(mSpriteTemplateID);

            return true;
        }

        public override bool Undo() {
            Debug.Assert(false, "todo");
            return true;
        }

        void AddCoordinatesInTiles(SpriteTileLayer layer) {
            List<Vector2Int> coordinates = new List<Vector2Int>();
            var layerData = layer.layerData;
            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            for (int i = 0; i < rows; ++i) {
                for (int j = 0; j < cols; ++j) {
                    var tile = layerData.GetTile(j, i) as SpriteTileData;
                    if (tile != null && tile.spriteTemplate != null && tile.spriteTemplate.id == mSpriteTemplateID) {
                        coordinates.Add(new Vector2Int(j, i));
                    }
                }
            }

            mSpriteTemplateTiles[layerData.id] = coordinates;
        }

        public override string description {
            get {
                return mDescription;
            }
        }

        int mSpriteTemplateID;
        string mDescription;
        Dictionary<int, List<Vector2Int>> mSpriteTemplateTiles = new Dictionary<int, List<Vector2Int>>();
    }
}

#endif