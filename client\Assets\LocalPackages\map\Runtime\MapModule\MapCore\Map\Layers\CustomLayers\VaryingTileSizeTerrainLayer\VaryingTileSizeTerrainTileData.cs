﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public class VaryingTileSizeTerrainTileData : ModelData
    {
        //modelTemplateID: 地表tile使用的模型的配置id
        public VaryingTileSizeTerrainTileData(int id, Map map, Vector3 position, ModelTemplate modelTemplate, int index, int type, int tileID, VaryingTileSizeTerrainLayerData.BigTileData bigTile) : base(id, map, 0, position, Quaternion.identity, Vector3.one, modelTemplate, false)
        {
            mIndex = index;
            mType = type;
            mTileID = tileID;
            mBigTileData = bigTile;
        }

        //直接设置这个tile的图集信息
        public void SetTile(int tileIndex, int tileType, int tileID, VaryingTileSizeTerrainLayerData.BigTileData bigTile)
        {
            mType = tileType;
            mIndex = tileIndex;
            mTileID = tileID;
            mBigTileData = bigTile;
        }

        public int index { get { return mIndex; } }
        public int type { get { return mType; } }
        public int tileID { get { return mTileID; } }
        public VaryingTileSizeTerrainLayerData.BigTileData bigTile { get { return mBigTileData; } }
        public Vector2Int size { 
            get
            {
                if (mBigTileData != null)
                {
                    return new Vector2Int(mBigTileData.width, mBigTileData.height);
                }
                return Vector2Int.one;
            } 
        }

        int mType = -1;
        int mIndex = 0;
        //used in map editor only, this is the id of prefab group item
        int mTileID = 0;
        VaryingTileSizeTerrainLayerData.BigTileData mBigTileData;
    }
}
