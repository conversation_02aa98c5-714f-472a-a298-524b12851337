﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //绘制河流的贴图
    public class PaintRiverTexture : RiverEditorTool
    {
        public PaintRiverTexture(RiverEditor editor, BrushSetting brushSetting) : base(editor)
        {
            mBrushSetting = brushSetting;
            var handlers = editor.layer.GetEventHandlers();
            handlers.onGenerateRiver = OnGenerateRiver;
        }

        public override void OnDestroy()
        {
        }

        public override void OnDisabled()
        {
            SetSelectedSection(-1);
            var indicator = SLGMakerEditor.instance.brushIndicator;
            indicator.SetActive(false);
            indicator.SetTexture(null);
        }

        public override void OnEnabled()
        {
            var indicator = SLGMakerEditor.instance.brushIndicator;
            var brush = mEditor.layer.brushManager.GetActiveBrush();
            if (brush != null)
            {
                indicator.SetTexture(brush.GetTexture(false));
                indicator.SetActive(true);
            }
        }

        void OnGenerateRiver(int dataID)
        {
            SetSelectedSection(0);
        }

        public override void DrawScene()
        {
        }

        protected override void DrawGUIImpl()
        {
            int shaderLOD = EditorGUILayout.IntField(new GUIContent("Shader LOD", "设置当前river shader的maximum lod值"), mEditor.layer.GetShaderLOD());
            mEditor.layer.SetShaderLOD(shaderLOD);
            mBrushSetting.channel = (TextureChannel)EditorGUILayout.EnumPopup(new GUIContent("Paint Channel", "选择绘制mask贴图的通道"), mBrushSetting.channel);
            mBrushSetting.increaseAlpha = EditorGUILayout.Toggle(new GUIContent("Increment", "绘制还是擦除mask贴图数据"), mBrushSetting.increaseAlpha);
            var newSize = EditorGUILayout.IntField(new GUIContent("Brush Size", "笔刷大小"), mBrushSetting.size);
            if (newSize != mBrushSetting.size)
            {
                ChangeBrushSize(newSize);
            }
            mBrushSetting.strength = EditorGUILayout.IntSlider(new GUIContent("Brush Strength", "笔刷强度"), mBrushSetting.strength, 1, 100);

            var brushManager = mEditor.layer.brushManager;
            BrushManagerUI.Draw(brushManager);

            if (mEditor.selectedObjectID != 0)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                var riverData = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                var sections = riverData.sections;
                for (int i = 0; i < sections.Count; ++i)
                {
                    DrawSection(sections[i], i);
                }
                DrawMaterialEditor();
                EditorGUILayout.EndVertical();
            }
        }

        public override void DrawMenu()
        {
        }

        void DrawSection(PolygonRiverSectionData section, int idx)
        {
            if (section.texture != null)
            {
                EditorGUILayout.BeginHorizontal();
                bool selected = (idx == mSelectedSection);
                bool newSelectionState = GUILayout.Toggle(selected, "");
                if (newSelectionState != selected)
                {
                    SetSelectedSection(idx);
                }
                GUILayout.Label("River Section Texture " + idx.ToString());
                GUILayout.Label(section.texture, GUILayout.Width(100), GUILayout.Height(100));
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
            }
        }

        void SetSelectedSection(int idx)
        {
            mSelectedSection = idx;
            if (mMaterialEditor != null)
            {
                Object.DestroyImmediate(mMaterialEditor);
                mMaterialEditor = null;
            }
            if (idx >= 0)
            {
                var mtl = mEditor.layer.GetRiverMaterial(mEditor.selectedObjectID, idx);
                mMaterialEditor = MaterialEditor.CreateEditor(mtl) as MaterialEditor;
            }
        }

        void DrawMaterialEditor()
        {
            if (mEditor.selectedObjectID != 0 && mMaterialEditor != null)
            {
                mShowMaterialEditor = EditorGUILayout.Foldout(mShowMaterialEditor, "Material Editor");
                if (mShowMaterialEditor)
                {
                    bool changed = mMaterialEditor.PropertiesGUI();
                    if (changed)
                    {
                        //copy properties to other section materials
                        CopyMaterials();
                    }
                }
            }
        }

        void CopyMaterials()
        {
            Debug.Assert(mSelectedSection >= 0);
            var river = mEditor.layer.GetRiver(mEditor.selectedObjectID);
            int sectionCount = river.sections.Count;
            var mtl = mEditor.layer.GetRiverMaterial(mEditor.selectedObjectID, mSelectedSection);
            for (int i = 0; i < sectionCount; ++i)
            {
                if (i != mSelectedSection)
                {
                    var otherMtl = mEditor.layer.GetRiverMaterial(mEditor.selectedObjectID, i);
                    Utils.CopyRiverMaterial(mtl, otherMtl);
                }
            }
        }

        void ChangeBrushSize(int newSize)
        {
            mBrushSetting.size = newSize;
            var brushIndicator = SLGMakerEditor.instance.brushIndicator;
            brushIndicator.SetSize(newSize);
        }

        public override void Update(Event e)
        {
            if (e.type == EventType.MouseDown && e.button == 0 && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.type == EventType.MouseUp && e.button == 0)
            {
                mLeftButtonDown = false;
                if (mAction != null)
                {
                    mAction.FinishPainting();
                    ActionManager.instance.PushAction(mAction, true, false);
                    mAction = null;
                }
            }

            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            UpdateIndicator(pos);

            if (mLeftButtonDown)
            {
                if (mEditor.selectedObjectID == 0)
                {
                    var brush = mEditor.layer.brushManager.GetActiveBrush();
                    if (brush != null)
                    {
                        var min = pos - new Vector3(mBrushSetting.size * 0.5f, 0, mBrushSetting.size * 0.5f);
                        var max = pos + new Vector3(mBrushSetting.size * 0.5f, 0, mBrushSetting.size * 0.5f);
                        PickRiver(min, max);
                    }
                }

                if (mEditor.selectedObjectID != 0)
                {
                    var river = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                    var brush = mEditor.layer.brushManager.GetActiveBrush();
                    Paint(mBrushSetting, brush, pos, river);
                }
            }

            if (mSelectedSection == -1 && mEditor.selectedObjectID != 0)
            {
                SetSelectedSection(0);
            }

            HandleUtility.AddDefaultControl(0);
        }

        //拾取brush相交的河流
        void PickRiver(Vector3 brushMinPos, Vector3 brushMaxPos)
        {
            var objects = mEditor.layer.layerData.objects;
            if (objects.Count > 0)
            {
                var brushVertices = new List<Vector3>()
            {
                brushMinPos,
                new Vector3(brushMinPos.x, 0, brushMaxPos.y),
                brushMaxPos,
                new Vector3(brushMaxPos.x, 0, brushMinPos.y),
            };
                foreach (var p in objects)
                {
                    var river = p.Value as PolygonRiverData;
                    bool overlap = PolygonCollisionCheck.Overlap(river.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle), brushVertices);
                    if (overlap)
                    {
                        mEditor.SetSelection(river, 0);
                        mEditor.layer.SetSelected(river.id, true);
                        break;
                    }
                }
            }
        }

        void Paint(BrushSetting setting, Brush brush, Vector3 pos, PolygonRiverData river)
        {
            if (brush == null)
            {
                return;
            }

            var sections = river.sections;
            if (sections.Count > 0)
            {
                if (mLastClickPos != pos)
                {
                    mLastClickPos = pos;

                    for (int s = 0; s < sections.Count; ++s)
                    {
                        var section = sections[s];
                        if (section.texture != null)
                        {
                            var brushSize = setting.size;
                            int mipmap = 0;
                            var riverTextureSize = section.texture.width;
                            Color[] riverTextureData = section.textureData;
                            var riverBounds = section.bounds;
                            var brushMinPos = Utils.ToVector2(pos - new Vector3(brushSize * 0.5f, 0, brushSize * 0.5f));
                            var brushMaxPos = Utils.ToVector2(pos + new Vector3(brushSize * 0.5f, 0, brushSize * 0.5f));

                            Vector2 intersectedMinPos;
                            Vector2 intersectedMaxPos;
                            Vector2 riverMin = riverBounds.min;
                            Vector2 riverMax = riverBounds.max;
                            float step = 0.2f;
                            int channelIndex = (int)setting.channel;
                            //获取笔刷和水的bounds的交集
                            bool intersected = GetIntersection(riverMin, riverMax, brushMinPos, brushMaxPos, out intersectedMinPos, out intersectedMaxPos);
                            if (intersected)
                            {
                                if (mAction == null)
                                {
                                    mAction = new ActionChangeRiverTextureData(river.id);
                                }

                                Vector2Int pixelStart = MapToPixelCoord(intersectedMinPos, riverMin, riverMax, riverTextureSize);
                                Vector2Int pixelEnd = MapToPixelCoord(intersectedMaxPos, riverMin, riverMax, riverTextureSize);

                                pixelStart.x = Mathf.Clamp(pixelStart.x, 0, riverTextureSize - 1);
                                pixelStart.y = Mathf.Clamp(pixelStart.y, 0, riverTextureSize - 1);
                                pixelEnd.x = Mathf.Clamp(pixelEnd.x, 0, riverTextureSize - 1);
                                pixelEnd.y = Mathf.Clamp(pixelEnd.y, 0, riverTextureSize - 1);

                                int blockWidth = pixelEnd.x - pixelStart.x + 1;
                                int blockHeight = pixelEnd.y - pixelStart.y + 1;
                                Color[] subTextureData = mColorArrayPool.Rent(blockWidth * blockHeight);

                                for (int i = pixelStart.y; i <= pixelEnd.y; ++i)
                                {
                                    for (int j = pixelStart.x; j <= pixelEnd.x; ++j)
                                    {
                                        var pixelWorldPos = MapToWorldPos(j, i, riverTextureSize, riverMin, riverMax);
                                        float rx = (pixelWorldPos.x - brushMinPos.x) / (brushMaxPos.x - brushMinPos.x);
                                        float ry = (pixelWorldPos.y - brushMinPos.y) / (brushMaxPos.y - brushMinPos.y);
                                        Color brushAlpha;
                                        var idx = i * riverTextureSize + j;
                                        var originalColor = riverTextureData[idx];
                                        var subIdx = (i - pixelStart.y) * blockWidth + (j - pixelStart.x);
                                        bool valid = brush.Sample(mipmap, rx, ry, out brushAlpha, false);
                                        if (valid && brushAlpha.a != 0)
                                        {
                                            float val = brushAlpha.a * setting.strength / 100.0f;
                                            float modifiedValue = originalColor[channelIndex];
                                            //每次递减一些alpha值
                                            if (setting.increaseAlpha)
                                            {
                                                modifiedValue += val * step;
                                            }
                                            else
                                            {
                                                modifiedValue -= val * step;
                                            }
                                            modifiedValue = Mathf.Clamp01(modifiedValue);

                                            originalColor[channelIndex] = modifiedValue;
                                            riverTextureData[idx] = originalColor;

                                            subTextureData[subIdx] = originalColor;
                                        }
                                        else
                                        {
                                            subTextureData[subIdx] = originalColor;
                                        }
                                    }
                                }

                                if (riverTextureData != null)
                                {
                                    section.UpdateTextureData(subTextureData, pixelStart.x, pixelStart.y, blockWidth, blockHeight, false);
                                }

                                mColorArrayPool.Return(subTextureData);
                            }
                        }
                    }
                }
            }
        }

        //获取两个矩形的交集
        bool GetIntersection(Vector2 riverMin, Vector2 riverMax, Vector2 brushMin, Vector2 brushMax, out Vector2 intersectedMin, out Vector2 intersectedMax)
        {
            float minX = Mathf.Max(riverMin.x, brushMin.x);
            float minY = Mathf.Max(riverMin.y, brushMin.y);
            float maxX = Mathf.Min(riverMax.x, brushMax.x);
            float maxY = Mathf.Min(riverMax.y, brushMax.y);

            intersectedMin = new Vector2(minX, minY);
            intersectedMax = new Vector2(maxX, maxY);

            if (minX <= maxX && minY <= maxY)
            {
                return true;
            }
            return false;
        }

        Vector2Int MapToPixelCoord(Vector2 pos, Vector2 boundsMin, Vector2 boundsMax, int textureSize)
        {
            float rx = (pos.x - boundsMin.x) / (boundsMax.x - boundsMin.x);
            float ry = (pos.y - boundsMin.y) / (boundsMax.y - boundsMin.y);
            return new Vector2Int(Mathf.RoundToInt(rx * textureSize), Mathf.RoundToInt(ry * textureSize));
        }

        Vector2 MapToWorldPos(int x, int y, int riverTextureSize, Vector2 riverMin, Vector2 riverMax)
        {
            float rx = x / (float)riverTextureSize;
            float ry = y / (float)riverTextureSize;
            float px = riverMin.x + rx * (riverMax.x - riverMin.x);
            float py = riverMin.y + ry * (riverMax.y - riverMin.y);
            return new Vector2(px, py);
        }

        void UpdateIndicator(Vector3 pos)
        {
#if false
            var indicator = SLGMakerEditor.instance.brushIndicator;
            var brush = mEditor.layer.brushManager.GetActiveBrush();
            indicator.SetTexture(brush.texture);
            indicator.SetActive(true);

            indicator.SetPosition(pos);
            indicator.SetSize(mBrushSetting.size);
#else
            Handles.DrawWireDisc(pos, Vector3.up, mBrushSetting.size * 0.5f);
            mEditor.RepaintScene();
#endif
        }

        public override RiverEditorToolType type { get { return RiverEditorToolType.PaintTexture; } }

        Vector3 mLastClickPos;
        BrushSetting mBrushSetting;
        bool mLeftButtonDown;
        ActionChangeRiverTextureData mAction;
        MaterialEditor mMaterialEditor;
        int mSelectedSection = -1;
        bool mShowMaterialEditor = true;
        ArrayPool<Color> mColorArrayPool = ArrayPool<Color>.Create();
    }
}
#endif