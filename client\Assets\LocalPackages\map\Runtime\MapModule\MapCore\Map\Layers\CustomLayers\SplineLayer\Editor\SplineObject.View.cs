﻿ 



 
 


//created by wzw at 2020/6/16

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum TangentMoveType
    {
        Free,
        Rotate,
        RotateAndScale,
    }
    //多边形的模型
    public partial class SplineObject
    {
        Mesh mMesh;
        GameObject mRoot;
        GameObject mMeshGameObject;
        GameObject mControlPointTemplate;
        List<GameObject> mControlPointGameObjects = new List<GameObject>();
        GameObject[] mTangents = new GameObject[2];

        public GameObject rootGameObject { get { return mRoot; } }
        public GameObject meshGameObject { get { return mMeshGameObject; } }
        public GameObject waterGameObject { get { return mRiverData.waterObject; } }
        public GameObject stencilMaskGameObject { get { return mRiverData.stencilMaskObject; } }

        public void SetDisplayRadius(float radius) {
            if (mControlPointTemplate != null)
            {
                mControlPointTemplate.transform.localScale = Vector3.one * radius * 2;
            }
            for (int i = 0; i < mControlPointGameObjects.Count; ++i)
            {
                mControlPointGameObjects[i].transform.localScale = Vector3.one * radius * 2;
            }
            for (int i = 0; i < mTangents.Length; ++i)
            {
                if (mTangents[i] != null)
                {
                    mTangents[i].transform.localScale = Vector3.one * radius * 2;
                }
            }
        }

        public void OnDestroy()
        {
            Object.DestroyImmediate(mControlPointTemplate);
            for (int i = 0; i < mControlPointGameObjects.Count; ++i)
            {
                Object.DestroyImmediate(mControlPointGameObjects[i]);
            }
            mControlPointGameObjects = null;
            Object.DestroyImmediate(mMesh);
            Object.DestroyImmediate(mMeshGameObject);
            GameObject.DestroyImmediate(mRoot);
            for (int i = 0; i < mTangents.Length; ++i)
            {
                GameObject.DestroyImmediate(mTangents[i]);
            }

            Utils.DestroyObject(mRiverData.waterObject);
            Utils.DestroyObject(mRiverData.stencilMaskObject);
        }

        public void SetTextureCoord(int controlPointIndex, float texCoord)
        {
            var lastControlPoint = mControlPoints[(controlPointIndex - 1) % mControlPoints.Count];
            var uvDistance = texCoord - lastControlPoint.horizontalTextureCoord;
            Debug.Assert(uvDistance > 0);
            float distance = TextureCoordToDistance(controlPointIndex, uvDistance);
            var d = mControlPoints[controlPointIndex].pos - lastControlPoint.pos;
            d.Normalize();
            mControlPoints[controlPointIndex].pos = lastControlPoint.pos + d * distance;
            mControlPointGameObjects[controlPointIndex].transform.position = mControlPoints[controlPointIndex].pos;
            CreateSplineMesh();
        }

        //用于非loop的spline起始和终点的fading
        List<Color> CreateMeshVertexColor(List<Vector3> vertices)
        {
            List<Color> vertexColor = new List<Color>();
            for (int i = 0; i < vertices.Count; ++i)
            {
                vertexColor.Add(Color.white);
            }
            if (!isLoop && fadeoutEndPoints)
            {
                int last = vertexColor.Count - 1;
                vertexColor[0] = new Color(vertexColor[0].r, vertexColor[0].g, vertexColor[0].b, 0);
                vertexColor[1] = new Color(vertexColor[1].r, vertexColor[1].g, vertexColor[1].b, 0);
                vertexColor[last] = new Color(vertexColor[last].r, vertexColor[last].g, vertexColor[last].b, 0);
                vertexColor[last - 1] = new Color(vertexColor[last - 1].r, vertexColor[last - 1].g, vertexColor[last - 1].b, 0);
            }

            return vertexColor;
        }

        void CreateMeshGameObject()
        {
            MeshFilter filter = null;
            if (mMeshGameObject == null)
            {
                mMeshGameObject = new GameObject("SplineMesh");
                mMeshGameObject.transform.SetParent(mRoot.transform);
                Utils.HideGameObject(mMeshGameObject);

                var renderer = mMeshGameObject.AddComponent<MeshRenderer>();
                filter = mMeshGameObject.AddComponent<MeshFilter>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mMaterialPath);
            }
            else
            {
                filter = mMeshGameObject.GetComponent<MeshFilter>();
            }
            filter.sharedMesh = mMesh;
        }

        void CreateRiverObject(Mesh mesh)
        {
            MeshFilter filter = null;
            if (mRiverData.waterObject == null)
            {
                mRiverData.waterObject = new GameObject("Water");
                mRiverData.waterObject.transform.SetParent(mRoot.transform);

                var renderer = mRiverData.waterObject.AddComponent<MeshRenderer>();
                filter = mRiverData.waterObject.AddComponent<MeshFilter>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mRiverData.waterMaterialPath);
            }
            else
            {
                filter = mRiverData.waterObject.GetComponent<MeshFilter>();
            }
            filter.sharedMesh = mesh;
        }

        void CreateStencilMaskObject(Mesh mesh)
        {
            MeshFilter filter = null;
            if (mRiverData.stencilMaskObject == null)
            {
                mRiverData.stencilMaskObject = new GameObject("Stencil Mask");
                mRiverData.stencilMaskObject.transform.SetParent(mRoot.transform);

                var renderer = mRiverData.stencilMaskObject.AddComponent<MeshRenderer>();
                filter = mRiverData.stencilMaskObject.AddComponent<MeshFilter>();
                renderer.sharedMaterial = MapModuleResourceMgr.LoadMaterial(mRiverData.stencilMaskMaterialPath);
            }
            else
            {
                filter = mRiverData.stencilMaskObject.GetComponent<MeshFilter>();
            }
            filter.sharedMesh = mesh;
        }

        public void Move(Vector3 delta)
        {
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                mControlPoints[i].pos += delta;
                for (int k = 0; k < mControlPoints[i].tangents.Length; ++k)
                {
                    mControlPoints[i].tangents[k] += delta;
                }
                mControlPointGameObjects[i].transform.position = mControlPoints[i].pos;
            }

            CreateSplineMesh();
        }

        public void MoveVertex(int vertexIndex, Vector3 delta)
        {
            Debug.Assert(false, "todo");
            //if (isLoop)
            //{
            //    if (vertexIndex == mMeshVertices.Length - 1 || vertexIndex == 1)
            //    {
            //        mMeshVertices[mMeshVertices.Length - 1] += delta;
            //        mMeshVertices[1] += delta;
            //    }
            //    else if (vertexIndex == mMeshVertices.Length - 2 || vertexIndex == 0)
            //    {
            //        mMeshVertices[mMeshVertices.Length - 2] += delta;
            //        mMeshVertices[0] += delta;
            //    }
            //    else
            //    {
            //        mMeshVertices[vertexIndex] += delta;
            //    }
            //}
            //else
            //{
            //    mMeshVertices[vertexIndex] += delta;
            //}
            //mMesh.vertices = mMeshVertices;
            //var uvs = CreateSplineMeshUV(mMeshVertices);
            //mMesh.UploadMeshData(false);
            //mMesh.RecalculateBounds();
        }

        public void MoveControlPoint(int controlPointIndex, Vector3 delta)
        {
            //temp code
            if (false)
            {
                //RotateControlPoint(controlPointIndex, delta);
            }
            else
            {
                mControlPoints[controlPointIndex].pos += delta;
            }
            mControlPointGameObjects[controlPointIndex].transform.position = mControlPoints[controlPointIndex].pos;
            CreateSplineMesh();
        }

        public void MoveTangent(int controlPointIndex, int tangentIndex, Vector3 delta, TangentMoveType type)
        {
            //temp code
            if (false)
            {
                //RotateControlPoint(controlPointIndex, delta);
            }
            else
            {
                var cp = mControlPoints[controlPointIndex];
                if (type == TangentMoveType.Free)
                {
                    cp.tangents[tangentIndex] += delta;
                    mTangents[tangentIndex].transform.position = cp.tangents[tangentIndex];
                }
                else if (type == TangentMoveType.Rotate)
                {
                    int other = (tangentIndex + 1) % 2;
                    var oldDir = cp.tangents[tangentIndex] - cp.pos;
                    float len = oldDir.magnitude;
                    cp.tangents[tangentIndex] += delta;
                    var newDir = (cp.tangents[tangentIndex] - cp.pos).normalized;
                    oldDir.Normalize();
                    cp.tangents[tangentIndex] = newDir * len + cp.pos;
                    float angle = Utils.Angle(oldDir, newDir);

                    float rightLen = (cp.tangents[other] - cp.pos).magnitude;
                    var oldRightDir = (cp.tangents[other] - cp.pos).normalized;
                    var newRightDir = Utils.RotateY(oldRightDir, angle);
                    cp.tangents[other] = newRightDir * rightLen + cp.pos;

                    for (int k = 0; k < mTangents.Length; ++k)
                    {
                        mTangents[k].transform.position = cp.tangents[k];
                    }
                }
                else if (type == TangentMoveType.RotateAndScale)
                {
                    int other = (tangentIndex + 1) % 2;
                    var oldDir = cp.tangents[tangentIndex] - cp.pos;
                    float len = oldDir.magnitude;
                    cp.tangents[tangentIndex] += delta;
                    float newLen = (cp.tangents[tangentIndex] - cp.pos).magnitude;
                    float deltaLen = newLen - len;
                    var newDir = (cp.tangents[tangentIndex] - cp.pos).normalized;
                    oldDir.Normalize();
                    cp.tangents[tangentIndex] = newDir * newLen + cp.pos;
                    float angle = Utils.Angle(oldDir, newDir);

                    float rightLen = (cp.tangents[other] - cp.pos).magnitude + deltaLen;
                    var oldRightDir = (cp.tangents[other] - cp.pos).normalized;
                    var newRightDir = Utils.RotateY(oldRightDir, angle);
                    cp.tangents[other] = newRightDir * rightLen + cp.pos;

                    for (int k = 0; k < mTangents.Length; ++k)
                    {
                        mTangents[k].transform.position = cp.tangents[k];
                    }
                }
            }
            
            CreateSplineMesh();
        }

        //旋转一个control point会导致所有control point受影响
        void RotateControlPoint(int controlPointIndex, Vector3 delta)
        {
            float len = (mControlPoints[controlPointIndex].pos - mControlPoints[controlPointIndex - 1].pos).magnitude;
            var newPos = mControlPoints[controlPointIndex].pos + delta;
            var d = newPos - mControlPoints[controlPointIndex - 1].pos;
            d.Normalize();
            mControlPoints[controlPointIndex].pos = mControlPoints[controlPointIndex - 1].pos + d * len;
        }

        public void InsertControlPoint(int index, Vector3 pos, float width, int pointCountInOneSegment)
        {
            var controlPoint = new ControlPoint()
            {
                pos = pos,
            };
            controlPoint.tangents[0] = pos + Vector3.left * 5;
            controlPoint.tangents[1] = pos + Vector3.right * 5;
            controlPoint.width = width;
            controlPoint.pointCountInSegment = pointCountInOneSegment;
            mControlPoints.Insert(index, controlPoint);
            var controlPointObj = CreateControlPointGameObject(index, controlPoint, mRoot.transform);
            mControlPointGameObjects.Insert(index, controlPointObj);

            HideTangent();

            CreateSplineMesh();
        }

        public void RemoveControlPoint(int index)
        {
            mControlPoints.RemoveAt(index);
            var obj = mControlPointGameObjects[index];
            GameObject.DestroyImmediate(obj);
            mControlPointGameObjects.RemoveAt(index);

            HideTangent();
            CreateSplineMesh();
        }

        public void HideTangent()
        {
            for (int i = 0; i < mTangents.Length; ++i)
            {
                mTangents[i].SetActive(false);
            }
        }

        public bool IsTangentVisible()
        {
            return mTangents[0] != null ? mTangents[0].activeSelf : false;
        }

        public void ShowTangent(int controlPointIndex)
        {
            for (int i = 0; i < mTangents.Length; ++i)
            {
                mTangents[i].transform.position = mControlPoints[controlPointIndex].tangents[i];
                mTangents[i].SetActive(true);
            }
        }

        public void ApplyTangent(int controlPointIndex, int srcIdx, int targetIdx)
        {
            var cp = mControlPoints[controlPointIndex];
            var d = cp.pos - cp.tangents[srcIdx];
            cp.tangents[targetIdx] = cp.pos + d;
            mTangents[targetIdx].transform.position = cp.tangents[targetIdx];

            CreateSplineMesh();
        }

        GameObject CreateTangentGameObject(string name, Color color)
        {
            var obj = GameObject.Instantiate<GameObject>(mControlPointTemplate);
            obj.name = name;
            obj.transform.SetParent(mRoot.transform, false);
            obj.SetActive(false);
            Utils.HideGameObject(obj);
            var meshRenderer = obj.GetComponent<MeshRenderer>();
            var mtl = Object.Instantiate<Material>(meshRenderer.sharedMaterial);
            meshRenderer.sharedMaterial = mtl;
            mtl.color = color;
            return obj;
        }

        GameObject CreateControlPointGameObject(int index, ControlPoint p, Transform rootTransform)
        {
            var obj = GameObject.Instantiate<GameObject>(mControlPointTemplate);
            obj.name = $"control point {index}";
            obj.transform.position = p.pos;
            obj.transform.SetParent(rootTransform, false);
            obj.SetActive(true);
            Utils.HideGameObject(obj);
            return obj;
        }

        void CreateControlPointGameObjects()
        {
            if (mControlPointTemplate == null)
            {
                mControlPointTemplate = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                var collider = mControlPointTemplate.GetComponent<Collider>();
                Object.DestroyImmediate(collider);
                var renderer = mControlPointTemplate.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/ColorNoCull"));
                Utils.HideGameObject(mControlPointTemplate);
                mControlPointTemplate.transform.SetParent(mRoot.transform);
                mControlPointTemplate.SetActive(false);
            }
            Debug.Assert(mControlPointGameObjects.Count == 0);
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                var obj = CreateControlPointGameObject(i, mControlPoints[i], mRoot.transform);
                mControlPointGameObjects.Add(obj);
            }
            mTangents[0] = CreateTangentGameObject("left tangent", Color.red);
            mTangents[1] = CreateTangentGameObject("right tangent", Color.green);
        }

        float GetMaxWidth()
        {
            float maxWidth = float.MinValue;
            for (int i = 0; i < mControlPoints.Count; ++i)
            {
                if (mControlPoints[i].width > maxWidth)
                {
                    maxWidth = mControlPoints[i].width;
                }
            }
            return maxWidth;
        }

        float GetRatio()
        {
            if (mRatio != 0)
            {
                return mRatio;
            }
            if (mRiverData.isRiverObject)
            {
                return 1;
            }
            var mtl = AssetDatabase.LoadAssetAtPath<Material>(mMaterialPath);
            var texture = mtl.GetTexture("_MainTex");
            if (texture == null)
            {
                Debug.LogError($"invalid material {mMaterialPath}, there must be a property named _MainTex in shader!");
                return 1.0f;
            }
            return texture.width / (float)texture.height;
        }
    }
}

#endif