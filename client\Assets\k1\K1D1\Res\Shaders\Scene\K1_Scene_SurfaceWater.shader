﻿Shader "K1/Scene/SurfaceWater"
{
    Properties
    {
		_MainBaseTex("海底色贴图", 2D) = "white" {}
		_NoiseTex("海岸线波动噪声图", 2D) = "white" {}
		_WaveSpeed("海岸线波动速度", Range(0, 2)) = 0.325
		_WaveAmplitude("海岸线波动幅度", Range(0, 0.2)) = 0.005
		_NoiseScale("海岸线扰动系数", Range(0, 0.8)) = 0.35
		_MainBaseBright("海底色基础混合亮度", Range(0, 1)) = 0.7

        _MainTex("海贴图", 2D) = "white" {}
		_BumpTex("扰动贴图", 2D) = "bump" {}
		_BumpIntensity("扰动强度", Range(0.001, 1)) = 1
		_MaskTex("遮罩贴图", 2D) = "white" {}
		_Color("浅水颜色", Color) = (1, 1, 1, 1)
		_Speed("水流速度", Range(0.001, 1)) = 1
		_Alpha("透明度", Range(0.001, 1)) = 1

		[HideInInspector]_WaterNoiseScale("海面波动比", Range(0,1)) = 0
    }
    SubShader
    {
		Tags
		{
			"Queue" = "Transparent"
			"IgnoreProjector" = "True"
			"RenderType" = "Transparent"
			"PreviewType" = "Plane"
			"CanUseSpriteAtlas" = "True"
		}

		Cull Off
		Lighting Off
		ZWrite Off
		ZTest LEqual
		Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

			sampler2D _MainBaseTex;
			sampler2D _NoiseTex;
			sampler2D _MainTex;
			sampler2D _BumpTex;
			half4 _BumpTex_ST;
			sampler2D _MaskTex;
			fixed4 _Color;
			fixed _Speed;
			fixed _BumpIntensity;
			fixed _Alpha;

			float _WaveSpeed;
			float _WaveAmplitude;
			float _NoiseScale;
			float _MainBaseBright;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _BumpTex);

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed3 noiseCol = UnpackNormal(tex2D(_NoiseTex, i.uv + _Time.x * _WaveSpeed)) * _NoiseScale;
				float2 sampleUv = float2(i.uv.x, i.uv.y + _WaveAmplitude * (sin(noiseCol.r)) * step(0.7, i.uv.y));
				fixed4 waveCol = tex2D(_MainBaseTex, sampleUv);
				fixed4 finalCol = waveCol;

				fixed3 maskCol = tex2D(_MaskTex, i.uv);
				
				fixed3 bumpOffsetOne = UnpackNormal(tex2D(_BumpTex, i.uv + _Time.x * _Speed));
                fixed3 bumpOffsetTwo = UnpackNormal(tex2D(_BumpTex, i.uv - _Time.x * _Speed));
				fixed3 bumpCol = normalize(bumpOffsetOne + bumpOffsetTwo);
				bumpCol.xy = bumpCol.xy * _BumpIntensity * maskCol.r;
				fixed3 mainCol = tex2D(_MainTex, i.uv + bumpCol.xy) * maskCol.r + _Color.rgb * (1 - maskCol.r);
				finalCol = fixed4(waveCol.rgb * _MainBaseBright + mainCol * _Alpha, waveCol.a);
                return finalCol;
            }
            ENDCG
        }
    }
}
