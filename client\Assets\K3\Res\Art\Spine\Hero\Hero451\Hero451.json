{"skeleton": {"hash": "LSlTCfejdfk", "spine": "4.2.33", "x": -309.86, "y": -24.67, "width": 618.7, "height": 1715.69, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 251.07, "y": 1128.17}, {"name": "ALL2", "parent": "ALL", "x": -170.98, "y": -30.01, "icon": "arrows"}, {"name": "tun", "parent": "ALL2", "length": 179.73, "rotation": -95.17, "y": -16.75}, {"name": "body", "parent": "ALL2", "length": 101.36, "rotation": 92.75, "y": 9.54}, {"name": "body2", "parent": "body", "length": 254.39, "rotation": 114.29, "x": 101.36, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 82, "rotation": 88.16, "x": 254.39, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 174, "rotation": -11.3, "x": 82}, {"name": "leg_R", "parent": "tun", "x": 31.11, "y": -74.29}, {"name": "leg_L", "parent": "tun", "x": 13.63, "y": 74.06}, {"name": "leg_R2", "parent": "leg_R", "length": 437.66, "rotation": 16.67, "x": 81.87, "y": -34.21}, {"name": "leg_R3", "parent": "leg_R2", "length": 367.84, "rotation": -7.12, "x": 437.66, "inherit": "noScale"}, {"name": "leg_R4", "parent": "leg_R3", "length": 197.67, "rotation": -80.97, "x": 367.84, "inherit": "onlyTranslation"}, {"name": "leg_L2", "parent": "leg_L", "length": 420.84, "rotation": -11.86, "x": 90.55, "y": 30.89}, {"name": "leg_L3", "parent": "leg_L2", "length": 351.84, "rotation": 3.11, "x": 420.84, "inherit": "noScale"}, {"name": "foot_L", "parent": "root", "length": 72.12, "rotation": 112.08, "x": -35.87, "y": 14.43}, {"name": "foot_L2", "parent": "foot_L", "length": 145.54, "rotation": 78.25, "x": 72.12, "inherit": "noRotationOrReflection"}, {"name": "sh_L", "parent": "body2", "x": 209.96, "y": -120.14, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 261.12, "y": 125.42, "inherit": "noScale"}, {"name": "arm_L", "parent": "sh_L", "length": 177.04, "rotation": -127.09, "x": -22.22, "y": -4.67}, {"name": "arm_L2", "parent": "arm_L", "length": 246.54, "rotation": 152.38, "x": 177.04, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 208.27, "rotation": 157.5, "x": -19.82, "y": 4.38}, {"name": "arm_R2", "parent": "arm_R", "length": 235.81, "rotation": -8.52, "x": 208.27, "inherit": "noScale"}, {"name": "weapon", "parent": "root", "length": 919.56, "rotation": -99.85, "x": -143.13, "y": 918.58}, {"name": "arm_R3", "parent": "weapon", "length": 118, "rotation": 16.97, "x": -10.84, "y": -22.57}, {"name": "RU_R", "parent": "body2", "length": 34, "rotation": -7.17, "x": 104.29, "y": 15.54}, {"name": "RU_R2", "parent": "RU_R", "length": 34, "x": -5.72, "y": 2.45}, {"name": "RU_R3", "parent": "RU_R2", "length": 34, "x": -7.02, "y": 2.81}, {"name": "RU_L", "parent": "body2", "length": 34, "rotation": -7.17, "x": 86.62, "y": -90.43}, {"name": "RU_L2", "parent": "RU_L", "length": 34, "x": -17.4, "y": -34.14}, {"name": "RU_L3", "parent": "RU_L2", "length": 34, "x": -13.32, "y": -26.33}, {"name": "eyebrow_L", "parent": "head", "length": 6.21, "rotation": 77.37, "x": 78.89, "y": -76.8}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 13.02, "rotation": 32.58, "x": 6.21}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 11.01, "rotation": 4.3, "x": 13.02}, {"name": "eyebrow_R", "parent": "head", "length": 16.56, "rotation": -62.02, "x": 72.03, "y": 25.13}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 21.28, "rotation": -37.23, "x": 16.56}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 12.9, "rotation": -12.26, "x": 21.28}, {"name": "eye_L", "parent": "head", "x": 59.2, "y": -55.72}, {"name": "eye_R", "parent": "head", "x": 59.18, "y": 2.12}, {"name": "hand_L", "parent": "head", "length": 72.53, "rotation": -157.41, "x": 123.17, "y": -87.01}, {"name": "hair_F", "parent": "head", "x": 149.77, "y": -23.98}, {"name": "hair_F2", "parent": "hair_F", "length": 24.58, "rotation": -176.48, "x": -6.13, "y": -4.81}, {"name": "hair_F3", "parent": "hair_F2", "length": 27.99, "rotation": -12.81, "x": 24.58, "color": "abe323ff"}, {"name": "hair_F4", "parent": "hair_F3", "length": 22.06, "rotation": -6.53, "x": 27.99, "color": "abe323ff"}, {"name": "hair_F5", "parent": "hair_F", "length": 35.07, "rotation": 121.57, "x": 7.63, "y": 21.18}, {"name": "hair_F6", "parent": "hair_F5", "length": 38.05, "rotation": 27, "x": 35.07, "color": "abe323ff"}, {"name": "hair_F7", "parent": "hair_F6", "length": 48.86, "rotation": 17.57, "x": 38.05, "color": "abe323ff"}, {"name": "hair_F8", "parent": "hair_F7", "length": 39.98, "rotation": 22.28, "x": 48.86, "color": "abe323ff"}, {"name": "hair_F9", "parent": "hair_F8", "length": 31.17, "rotation": 23.15, "x": 39.98, "color": "abe323ff"}, {"name": "hair_F10", "parent": "hair_F9", "length": 30.42, "rotation": 16.11, "x": 31.17, "color": "abe323ff"}, {"name": "hair_F11", "parent": "hair_F10", "length": 24.81, "rotation": -15.21, "x": 30.42, "color": "abe323ff"}, {"name": "hair_F12", "parent": "hair_F11", "length": 20.16, "rotation": -15.83, "x": 24.81, "color": "abe323ff"}, {"name": "sh_L2", "parent": "sh_L", "length": 198.46, "rotation": -129.37, "x": -21.26, "y": -4.46}, {"name": "sh_L3", "parent": "sh_L2", "length": 266.68, "rotation": 155.28, "x": 198.46, "color": "abe323ff"}, {"name": "arm_L3", "parent": "hand_L", "rotation": 80.55, "x": 72.66, "y": 0.02, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "sh_L", "rotation": -114.29, "x": -128.98, "y": -145.9, "color": "ff3f00ff", "icon": "ik"}, {"name": "weapon2", "parent": "root", "x": -300.51, "y": 12.59, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L4", "parent": "leg_L", "length": 774.69, "rotation": -10.47, "x": 90.54, "y": 30.88}, {"name": "leg_L5", "parent": "foot_L2", "rotation": -78.25, "x": 146.22, "y": -0.54, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L4", "rotation": 105.61, "x": 421.97, "y": -10.38, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R5", "parent": "leg_R", "length": 806.18, "rotation": 13.39, "x": 81.86, "y": -34.2}, {"name": "leg_R6", "parent": "root", "x": 77.17, "y": 183.02, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "leg_R5", "rotation": 81.75, "x": 436.95, "y": 24.83, "color": "ff3f00ff", "icon": "ik"}, {"name": "headround3", "parent": "head", "x": 339.15, "y": 1.35, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -53.83, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 290.95, "y": -52.48, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 65.49, "y": -406.19, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 14.75, "y": -406.19, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 321.83, "y": -43.78, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 321.83, "y": -97.07, "icon": "warning"}, {"name": "hair_R", "parent": "body2", "length": 85.17, "rotation": 67.5, "x": 287.32, "y": 33.07, "color": "abe323ff"}, {"name": "leg_L6", "parent": "leg_L2", "length": 132.84}, {"name": "sh_R2", "parent": "sh_R", "length": 213.71, "rotation": 167.47, "x": -18.71, "y": 4.2}, {"name": "sh_R3", "parent": "sh_R2", "length": 242.9, "rotation": -27.3, "x": 213.71, "color": "abe323ff"}, {"name": "arm_R4", "parent": "weapon", "rotation": 99.85, "x": -10.84, "y": -22.59, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "root", "x": -135.84, "y": 1167.29, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "order": 2, "bones": ["sh_L2", "sh_L3"], "target": "arm_L3"}, {"name": "arm_L1", "order": 4, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 5, "bones": ["arm_L2"], "target": "arm_L3", "compress": true, "stretch": true}, {"name": "arm_R", "order": 7, "bones": ["sh_R2", "sh_R3"], "target": "arm_R4", "bendPositive": false}, {"name": "arm_R1", "order": 9, "bones": ["arm_R"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 10, "bones": ["arm_R2"], "target": "arm_R4", "compress": true, "stretch": true}, {"name": "leg_L", "order": 12, "bones": ["leg_L4"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 13, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 14, "bones": ["leg_L3"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_R", "order": 16, "bones": ["leg_R5"], "target": "leg_R6", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 17, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 18, "bones": ["leg_R3"], "target": "leg_R6", "compress": true, "stretch": true}, {"name": "weapon", "order": 19, "bones": ["weapon"], "target": "weapon2"}], "transform": [{"name": "arm_L3", "order": 3, "bones": ["arm_L1"], "target": "sh_L3", "rotation": -140.21, "x": 21.57, "y": 2.84, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 8, "bones": ["arm_R1"], "target": "sh_R3", "rotation": 105.39, "x": 9.8, "y": -35.46, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 20, "bones": ["bodyround2"], "target": "bodyround", "x": -50.74, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 6, "bones": ["sh_R"], "target": "bodyround", "x": 195.63, "y": 531.6, "mixRotate": 0, "mixX": 0.025, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 1, "bones": ["sh_L"], "target": "bodyround", "x": 144.47, "y": 286.04, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 21, "bones": ["headround2"], "target": "headround", "x": -48.2, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 22, "bones": ["eyebrow_R"], "target": "headround", "rotation": -62.02, "x": -267.12, "y": 77.6, "mixRotate": 0, "mixX": 0.035, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 23, "bones": ["eyebrow_L"], "target": "headround", "rotation": 77.37, "x": -260.26, "y": -24.32, "mixRotate": 0, "mixX": 0.025, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "bones": ["hand_L"], "target": "headround", "rotation": -157.41, "x": -215.98, "y": -34.53, "mixRotate": 0, "mixX": 0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 24, "bones": ["hair_F"], "target": "headround", "x": -189.39, "y": 28.5, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 25, "bones": ["tunround2"], "target": "tunround", "y": -53.29, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 15, "bones": ["leg_R"], "target": "tunround", "rotation": -95.17, "x": -398.62, "y": 2.74, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 11, "bones": ["leg_L"], "target": "tunround", "rotation": -95.17, "x": -249.3, "y": 6.78, "mixRotate": 0, "mixX": -0.007, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_F3", "order": 26, "bone": "hair_F3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F4", "order": 27, "bone": "hair_F4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F6", "order": 28, "bone": "hair_F6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F7", "order": 29, "bone": "hair_F7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F8", "order": 30, "bone": "hair_F8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F9", "order": 31, "bone": "hair_F9", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F10", "order": 32, "bone": "hair_F10", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F11", "order": 33, "bone": "hair_F11", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_F12", "order": 34, "bone": "hair_F12", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.31654, 0.07495, 0.35059, 0.02601, 0.36575, 0.00702, 0.38589, 0.00632, 0.41822, 0.0435, 0.4394, 0.0973, 0.46519, 0.10058, 0.50191, 0.14711, 0.53863, 0.19364, 0.64951, 0.23266, 0.77737, 0.2927, 0.84862, 0.29393, 0.91359, 0.31495, 0.98678, 0.42011, 0.99825, 0.47499, 0.99734, 0.59714, 0.97556, 0.68772, 0.94085, 0.75846, 0.88584, 0.80932, 0.82427, 0.8384, 0.72246, 0.83969, 0.61328, 0.80423, 0.45112, 0.70557, 0.3972, 0.66236, 0.34811, 0.69613, 0.28973, 0.75779, 0.2371, 0.82452, 0.1235, 0.99871, 0, 0.40793, 0.27296, 0.11835, 0.3741, 0.11542, 0.39245, 0.28042, 0.40729, 0.47065, 0.34947, 0.52812, 0.32521, 0.26221, 0.45289, 0.31035, 0.5118, 0.35169, 0.75493, 0.43903, 0.48347, 0.53345, 0.73116, 0.64204, 0.78808, 0.65361, 0.86857, 0.64102, 0.94295, 0.54338, 0.87832, 0.4552, 0.80698, 0.44733], "triangles": [16, 42, 15, 15, 42, 14, 43, 12, 42, 42, 13, 14, 42, 12, 13, 22, 38, 21, 38, 35, 36, 5, 30, 4, 20, 40, 19, 20, 39, 40, 20, 21, 39, 19, 41, 18, 19, 40, 41, 18, 41, 17, 21, 38, 39, 17, 42, 16, 17, 41, 42, 39, 37, 40, 40, 44, 41, 40, 37, 44, 38, 36, 39, 39, 36, 37, 41, 44, 43, 37, 10, 44, 37, 36, 9, 36, 8, 9, 37, 9, 10, 35, 7, 36, 36, 7, 8, 35, 6, 7, 35, 5, 6, 44, 10, 11, 44, 11, 43, 41, 43, 42, 43, 11, 12, 22, 23, 38, 31, 5, 35, 32, 35, 38, 34, 30, 31, 30, 34, 0, 1, 2, 30, 30, 2, 3, 29, 0, 34, 30, 0, 1, 30, 3, 4, 5, 31, 30, 32, 31, 35, 34, 26, 29, 33, 34, 31, 23, 32, 38, 24, 33, 23, 24, 25, 33, 34, 25, 26, 27, 28, 26, 26, 28, 29, 23, 33, 32, 33, 31, 32, 33, 25, 34], "vertices": [2, 5, 220.87, -110.5, 0.03739, 17, 10.91, 9.64, 0.96261, 1, 17, 11.98, -3.09, 1, 2, 17, 12.13, -8.62, 0.88107, 19, -17.57, 29.79, 0.11893, 2, 17, 9.5, -14.68, 0.55428, 19, -11.15, 31.34, 0.44572, 2, 17, 0.83, -22.4, 0.08642, 19, 0.24, 29.08, 0.91358, 1, 19, 8.53, 23.95, 1, 1, 19, 16.87, 25.42, 1, 1, 19, 29.92, 22.32, 1, 1, 19, 42.97, 19.23, 1, 1, 19, 79.54, 22.45, 1, 1, 19, 122.12, 24.3, 1, 2, 19, 144.95, 29.32, 0.93656, 20, 42.28, -11.07, 0.06344, 2, 19, 166.32, 31.44, 0.50797, 20, 24.23, -22.87, 0.49203, 2, 19, 192.69, 23.73, 0.00867, 20, -2.86, -28.29, 0.99133, 2, 19, 197.9, 17.77, 0.02549, 20, -10.29, -25.42, 0.97451, 2, 19, 201.05, 2.57, 0.24341, 20, -20.19, -13.43, 0.75659, 2, 19, 196.63, -10.23, 0.60136, 20, -22.23, -0.04, 0.39864, 2, 19, 187.51, -21.51, 0.92522, 20, -19.37, 14.19, 0.07478, 1, 19, 171.35, -31.8, 1, 1, 19, 152.47, -39.88, 1, 1, 19, 119.94, -47.43, 1, 2, 5, 96.41, -161.11, 0.04623, 19, 84.03, -50.97, 0.95377, 2, 5, 129.71, -117.78, 0.27429, 19, 29.38, -50.53, 0.72571, 2, 5, 141.99, -103.92, 0.97067, 17, -67.97, 16.22, 0.02933, 1, 5, 144.7, -87.48, 1, 1, 5, 145.44, -66.81, 1, 1, 5, 144.82, -47.59, 1, 1, 5, 139.73, 2.14, 1, 1, 5, 224.78, 8.19, 1, 2, 5, 221.72, -95.21, 0.16186, 17, 11.77, 24.94, 0.83814, 2, 17, -1.54, -5.45, 0.99137, 66, 142.93, 280.59, 0.00863, 3, 5, 186.84, -122.46, 0.32545, 17, -23.12, -2.32, 0.66505, 66, 121.35, 283.72, 0.0095, 3, 5, 162.82, -116.95, 0.67111, 17, -47.14, 3.19, 0.32418, 66, 97.33, 289.23, 0.00471, 3, 5, 163.97, -96.67, 0.86609, 17, -45.99, 23.48, 0.12778, 66, 98.48, 309.52, 0.00613, 3, 5, 198.02, -103.31, 0.35434, 17, -11.93, 16.84, 0.63715, 66, 132.54, 302.88, 0.00851, 3, 17, -34.74, -18.82, 0.01687, 19, 18.83, -1.45, 0.96718, 66, 109.73, 267.22, 0.01595, 2, 19, 38.84, -2.29, 0.98415, 66, 97, 251.77, 0.01585, 2, 19, 119.06, 4.55, 0.99757, 66, 54.08, 183.65, 0.00243, 2, 19, 34.89, -26.86, 0.985, 66, 79.78, 269.74, 0.015, 2, 19, 117.17, -22.32, 0.99755, 66, 33.79, 201.36, 0.00245, 1, 19, 135.7, -19.62, 1, 1, 19, 161.09, -12.21, 1, 2, 19, 182.14, 5.28, 0.26575, 20, -2.08, -7.05, 0.73425, 2, 19, 158.98, 11.51, 0.64804, 20, 21.45, -1.81, 0.35196, 1, 19, 135.94, 7.3, 1], "hull": 30, "edges": [4, 6, 32, 34, 34, 36, 40, 42, 46, 48, 48, 50, 50, 52, 56, 58, 16, 18, 18, 20, 20, 22, 22, 24, 6, 8, 8, 10, 10, 12, 0, 58, 54, 56, 52, 54, 42, 44, 44, 46, 0, 2, 2, 4, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 12, 14, 14, 16, 72, 74, 76, 78], "width": 328, "height": 127}}, "body": {"body": {"type": "mesh", "uvs": [0.63146, 0.00023, 0.65189, 0.00069, 0.6764, 0.00373, 0.70684, 0.01202, 0.73089, 0.02387, 0.75429, 0.03998, 0.76896, 0.05817, 0.77565, 0.0772, 0.76907, 0.0978, 0.74818, 0.10853, 0.71609, 0.11219, 0.72902, 0.11809, 0.73018, 0.1251, 0.71542, 0.12939, 0.6906, 0.12785, 0.70301, 0.13315, 0.74366, 0.12958, 0.76988, 0.12924, 0.78218, 0.13475, 0.79446, 0.14832, 0.80209, 0.16015, 0.81238, 0.16861, 0.83262, 0.17288, 0.89203, 0.18088, 0.92522, 0.18733, 0.96123, 0.19811, 0.97581, 0.20907, 0.97686, 0.22358, 0.96353, 0.23643, 0.9322, 0.24863, 0.8829, 0.25635, 0.87797, 0.27474, 0.86552, 0.29127, 0.85386, 0.30577, 0.86295, 0.31405, 0.89253, 0.33006, 0.92044, 0.34598, 0.9383, 0.35853, 0.97041, 0.37695, 0.98372, 0.39374, 0.99288, 0.40727, 0.99895, 0.42246, 1, 0.43917, 0.99802, 0.45572, 0.92623, 0.45737, 0.81143, 0.45999, 0.73356, 0.46177, 0.71475, 0.46542, 0.69294, 0.46455, 0.70468, 0.47512, 0.71212, 0.4958, 0.71854, 0.53856, 0.72959, 0.53861, 0.73024, 0.60242, 0.73532, 0.62556, 0.73915, 0.64204, 0.73811, 0.65623, 0.73663, 0.66693, 0.73316, 0.67935, 0.73819, 0.69425, 0.75185, 0.7231, 0.75248, 0.75267, 0.74919, 0.79596, 0.75411, 0.83031, 0.76311, 0.85433, 0.76768, 0.86878, 0.77139, 0.8755, 0.78448, 0.88469, 0.7883, 0.89225, 0.7872, 0.90228, 0.79557, 0.90989, 0.80075, 0.91613, 0.80799, 0.93, 0.81723, 0.95908, 0.82646, 0.96604, 0.85367, 0.97793, 0.87367, 0.99595, 0.8429, 0.99939, 0.76223, 1, 0.69853, 0.99252, 0.70075, 0.96855, 0.68469, 0.96372, 0.68485, 0.94508, 0.68028, 0.93358, 0.65674, 0.90962, 0.66837, 0.89518, 0.68228, 0.88609, 0.67823, 0.88049, 0.68183, 0.87361, 0.67586, 0.86397, 0.65498, 0.83761, 0.56415, 0.74785, 0.56049, 0.72658, 0.57163, 0.70762, 0.58207, 0.69312, 0.59008, 0.67992, 0.5802, 0.66465, 0.57943, 0.64997, 0.56456, 0.63173, 0.47623, 0.5647, 0.48491, 0.56206, 0.40566, 0.49065, 0.3825, 0.4628, 0.38063, 0.44141, 0.39326, 0.41633, 0.41933, 0.39527, 0.44886, 0.37768, 0.49056, 0.3598, 0.52533, 0.34074, 0.56343, 0.32685, 0.57676, 0.31739, 0.57248, 0.30778, 0.5443, 0.2945, 0.50302, 0.27739, 0.46417, 0.26515, 0.42683, 0.2527, 0.40808, 0.24589, 0.40558, 0.25536, 0.39668, 0.28618, 0.3939, 0.29708, 0.39452, 0.30722, 0.39383, 0.31538, 0.39451, 0.32733, 0.35134, 0.39542, 0.33522, 0.43642, 0.31198, 0.43886, 0.31201, 0.44277, 0.31204, 0.44668, 0.3269, 0.45433, 0.33231, 0.46188, 0.33158, 0.47099, 0.33084, 0.47827, 0.34735, 0.49143, 0.3447, 0.50521, 0.32106, 0.50971, 0.3035, 0.50827, 0.25023, 0.59934, 0.25442, 0.60139, 0.25154, 0.61412, 0.27823, 0.61581, 0.27664, 0.62049, 0.31885, 0.63722, 0.31763, 0.64262, 0.2916, 0.64786, 0.27632, 0.67293, 0.25617, 0.69859, 0.26554, 0.71297, 0.24844, 0.7194, 0.2225, 0.75425, 0.14122, 0.86345, 0.08617, 0.93563, 0.06504, 0.95065, 0.02331, 0.97779, 0.01388, 0.97713, 0.00178, 0.94721, 0.00115, 0.92976, 0.0267, 0.85667, 0.07219, 0.74513, 0.09266, 0.71228, 0.08077, 0.7038, 0.10617, 0.69119, 0.1212, 0.64687, 0.10023, 0.62837, 0.105, 0.62556, 0.16597, 0.61475, 0.17135, 0.61005, 0.19663, 0.6109, 0.20777, 0.59941, 0.21473, 0.59771, 0.26334, 0.50711, 0.24035, 0.50151, 0.20589, 0.48486, 0.22675, 0.46659, 0.23576, 0.4499, 0.23797, 0.44263, 0.24095, 0.43556, 0.21954, 0.432, 0.23556, 0.39167, 0.23582, 0.32261, 0.24604, 0.31401, 0.24734, 0.30598, 0.2606, 0.29607, 0.25743, 0.2903, 0.26167, 0.28203, 0.25524, 0.24586, 0.24784, 0.228, 0.2423, 0.20031, 0.24626, 0.18662, 0.25333, 0.17293, 0.27043, 0.16324, 0.28681, 0.16148, 0.31087, 0.15936, 0.28992, 0.15404, 0.29309, 0.14419, 0.31953, 0.13645, 0.29798, 0.13486, 0.28053, 0.12766, 0.2971, 0.1105, 0.31426, 0.11859, 0.3343, 0.12147, 0.35789, 0.11929, 0.37021, 0.11263, 0.37083, 0.10307, 0.34346, 0.09165, 0.33059, 0.07964, 0.33618, 0.06659, 0.3657, 0.053, 0.41393, 0.0375, 0.44644, 0.02446, 0.47908, 0.01359, 0.52108, 0.00465, 0.5643, 0.00138, 0.02764, 0.95828, 0.02678, 0.9341, 0.05761, 0.93596, 0.14364, 0.69394, 0.2129, 0.69749, 0.15013, 0.6855, 0.17065, 0.68634, 0.23523, 0.6638, 0.12854, 0.72498, 0.19314, 0.72784, 0.12662, 0.63777, 0.15903, 0.6802, 0.16972, 0.68103, 0.21389, 0.66446, 0.27766, 0.64517, 0.19563, 0.61758, 0.24883, 0.62024, 0.21158, 0.61377, 0.22496, 0.5986, 0.23926, 0.5996, 0.23603, 0.61534, 0.2774, 0.48107, 0.28917, 0.47674, 0.30784, 0.49159, 0.30137, 0.48392, 0.2218, 0.61953, 0.247, 0.65381, 0.1375, 0.64851, 0.31344, 0.43201, 0.24223, 0.43027, 0.41347, 0.22932, 0.40817, 0.21292, 0.37319, 0.19199, 0.33077, 0.17248, 0.51465, 0.39204, 0.58259, 0.41156, 0.6274, 0.42877, 0.66743, 0.44924, 0.54474, 0.37728, 0.57503, 0.35819, 0.62743, 0.32421, 0.6264, 0.31268, 0.62016, 0.29859, 0.60873, 0.28472, 0.68047, 0.27528, 0.69169, 0.29236, 0.70122, 0.3103, 0.69898, 0.32475, 0.68425, 0.34505, 0.66707, 0.40236, 0.61694, 0.39428, 0.82906, 0.26971, 0.82204, 0.29152, 0.81672, 0.30912, 0.82134, 0.32453, 0.61255, 0.33907, 0.65792, 0.36952, 0.7468, 0.4048, 0.83571, 0.34363, 0.85268, 0.36483, 0.86292, 0.38911, 0.7681, 0.3752, 0.76852, 0.35047, 0.76591, 0.33, 0.76596, 0.30943, 0.76526, 0.28725, 0.76247, 0.26496, 0.81422, 0.39899, 0.91013, 0.3746, 0.89182, 0.35501, 0.86898, 0.33663, 0.84529, 0.321, 0.73223, 0.43267, 0.72078, 0.4514, 0.75584, 0.44977, 0.81534, 0.43028, 0.87881, 0.41079, 0.92604, 0.39573, 0.48977, 0.40486, 0.47373, 0.42553, 0.46679, 0.44797, 0.47848, 0.47855, 0.55069, 0.55533, 0.65171, 0.54577, 0.61379, 0.46457, 0.60067, 0.44488, 0.59668, 0.42993, 0.62296, 0.48368, 0.81829, 0.44496, 0.92676, 0.43703, 0.78559, 0.44003, 0.85408, 0.42632, 0.92704, 0.41527, 0.61789, 0.63216, 0.62688, 0.65063, 0.63249, 0.66666, 0.6426, 0.682, 0.64485, 0.69908, 0.69539, 0.69768, 0.69789, 0.68121, 0.70155, 0.66442, 0.70101, 0.64715, 0.69251, 0.62829, 0.63048, 0.72915, 0.63551, 0.75692, 0.70689, 0.83303, 0.7089, 0.75411, 0.70588, 0.72666, 0.29586, 0.17947, 0.28514, 0.19388, 0.28821, 0.22496, 0.35089, 0.20139, 0.29537, 0.28165, 0.29964, 0.29116, 0.29608, 0.30065, 0.29252, 0.31058, 0.28826, 0.32249, 0.35013, 0.32448, 0.35725, 0.31455, 0.35654, 0.30351, 0.35867, 0.2927, 0.36151, 0.28277, 0.36649, 0.23134, 0.36365, 0.21607, 0.36564, 0.24763, 0.29094, 0.24444, 0.45963, 0.23855, 0.49754, 0.25841, 0.54668, 0.27028, 0.62659, 0.27322, 0.70244, 0.26478, 0.76199, 0.24789, 0.82931, 0.2574, 0.45493, 0.21657, 0.48644, 0.19456, 0.56006, 0.18337, 0.63952, 0.18438, 0.69197, 0.19413, 0.68783, 0.18222, 0.72343, 0.17016, 0.65016, 0.0801, 0.61189, 0.10426, 0.60194, 0.1151, 0.5944, 0.12739, 0.59887, 0.1408, 0.607, 0.15862, 0.64322, 0.1547, 0.70758, 0.14822, 0.75177, 0.14137, 0.54526, 0.16202, 0.50823, 0.16484, 0.41322, 0.1707, 0.36431, 0.15415, 0.44091, 0.14709, 0.49553, 0.14697, 0.51669, 0.12841, 0.52794, 0.14591, 0.51584, 0.11441, 0.51682, 0.09999, 0.52794, 0.07338, 0.52238, 0.08626, 0.62713, 0.09346, 0.57746, 0.14419, 0.57678, 0.12859, 0.58298, 0.11469, 0.59193, 0.10272, 0.6002, 0.08989, 0.61122, 0.07814, 0.49092, 0.07074, 0.48679, 0.08848, 0.49643, 0.10301, 0.5047, 0.12033, 0.49368, 0.13424, 0.65801, 0.13608, 0.64499, 0.12466, 0.67217, 0.11094, 0.69201, 0.09956, 0.71244, 0.08464, 0.44856, 0.18459, 0.52659, 0.1748, 0.58096, 0.17245, 0.63788, 0.16918, 0.68043, 0.16456, 0.74367, 0.1561, 0.94933, 0.21739, 0.61731, 0.23649, 0.53407, 0.20501, 0.61001, 0.19476, 0.6788, 0.20092, 0.71826, 0.2159, 0.72548, 0.20193, 0.75619, 0.21559, 0.76501, 0.23302, 0.72451, 0.23675, 0.69803, 0.2549, 0.63741, 0.26389, 0.57047, 0.26251, 0.51814, 0.24746, 0.50514, 0.22507, 0.77224, 0.16711, 0.56535, 0.21816, 0.6229, 0.2115, 0.67234, 0.22064, 0.68179, 0.23701, 0.66481, 0.25027, 0.60655, 0.25571, 0.55968, 0.24985, 0.54642, 0.23295, 0.9224, 0.19448, 0.89076, 0.20149, 0.8827, 0.21445, 0.89541, 0.23122, 0.92818, 0.23967, 0.79541, 0.22102, 0.82093, 0.2402, 0.8732, 0.25006, 0.78665, 0.2011, 0.84877, 0.18007, 0.8091, 0.18653, 0.37045, 0.1329, 0.42402, 0.12619, 0.43999, 0.11074, 0.43154, 0.09557, 0.41368, 0.07733, 0.42684, 0.05896, 0.46067, 0.04117, 0.4945, 0.02544, 0.54619, 0.01524, 0.64797, 0.00887, 0.64224, 0.00365, 0.59852, 0.0096, 0.6865, 0.06364, 0.70114, 0.04653, 0.682, 0.01893, 0.7017, 0.03063, 0.5017, 0.05095, 0.51858, 0.03524, 0.72743, 0.06977, 0.73024, 0.05458, 0.72461, 0.04288], "triangles": [301, 300, 303, 45, 300, 44, 289, 38, 39, 40, 304, 289, 40, 289, 39, 288, 289, 304, 304, 40, 41, 303, 288, 304, 301, 303, 304, 301, 304, 41, 301, 41, 42, 43, 301, 42, 44, 301, 43, 44, 300, 301, 45, 286, 300, 86, 87, 66, 68, 86, 67, 85, 69, 84, 70, 83, 84, 86, 66, 67, 69, 86, 68, 69, 85, 86, 82, 83, 72, 69, 70, 84, 71, 83, 70, 72, 83, 71, 82, 72, 73, 80, 81, 82, 73, 80, 82, 75, 78, 74, 77, 75, 76, 74, 78, 80, 74, 80, 73, 77, 78, 75, 79, 80, 78, 57, 312, 56, 58, 312, 57, 95, 96, 307, 311, 307, 312, 311, 312, 58, 308, 307, 311, 95, 307, 308, 311, 58, 59, 310, 308, 311, 310, 311, 59, 309, 308, 310, 310, 59, 60, 94, 95, 308, 94, 308, 309, 93, 94, 309, 319, 310, 60, 315, 93, 309, 92, 93, 315, 309, 310, 319, 315, 309, 319, 91, 92, 315, 319, 60, 61, 318, 319, 61, 315, 319, 318, 316, 315, 318, 91, 315, 316, 62, 318, 61, 318, 62, 317, 318, 317, 316, 317, 62, 63, 90, 316, 317, 91, 316, 90, 317, 63, 64, 89, 90, 317, 89, 317, 64, 89, 64, 65, 88, 89, 65, 88, 65, 66, 66, 87, 88, 55, 314, 54, 105, 106, 246, 290, 105, 246, 290, 246, 247, 104, 105, 290, 291, 104, 290, 103, 104, 291, 292, 103, 291, 291, 290, 247, 102, 103, 292, 297, 292, 298, 298, 291, 247, 298, 292, 291, 296, 292, 297, 296, 293, 292, 102, 292, 293, 293, 296, 299, 101, 102, 293, 299, 49, 50, 295, 299, 50, 295, 50, 51, 294, 293, 299, 294, 299, 295, 101, 293, 294, 100, 101, 294, 314, 294, 295, 51, 53, 295, 52, 53, 51, 305, 98, 294, 99, 100, 294, 98, 99, 294, 314, 305, 294, 97, 98, 305, 306, 305, 313, 97, 305, 306, 96, 97, 306, 307, 306, 312, 96, 306, 307, 53, 314, 295, 54, 314, 53, 305, 314, 313, 314, 55, 313, 56, 313, 55, 312, 313, 56, 306, 313, 312, 282, 35, 36, 281, 282, 36, 270, 282, 281, 281, 36, 37, 271, 270, 281, 280, 281, 37, 271, 281, 280, 280, 37, 38, 272, 271, 280, 289, 280, 38, 272, 280, 289, 288, 272, 289, 247, 246, 262, 267, 108, 109, 251, 108, 267, 107, 108, 251, 251, 267, 268, 250, 107, 251, 106, 107, 250, 246, 106, 250, 268, 250, 251, 262, 250, 268, 246, 250, 262, 303, 287, 288, 300, 302, 303, 288, 287, 279, 298, 247, 248, 296, 297, 249, 274, 270, 271, 273, 271, 272, 274, 268, 260, 273, 274, 271, 273, 268, 274, 279, 273, 272, 261, 268, 273, 269, 261, 273, 262, 268, 261, 279, 269, 273, 261, 247, 262, 248, 247, 261, 269, 279, 287, 284, 261, 269, 284, 269, 287, 248, 261, 284, 302, 284, 287, 249, 297, 298, 249, 248, 284, 249, 298, 248, 286, 284, 302, 285, 249, 284, 286, 285, 284, 300, 286, 302, 46, 285, 286, 46, 286, 45, 48, 249, 285, 47, 48, 285, 296, 249, 48, 46, 47, 285, 299, 48, 49, 299, 296, 48, 279, 272, 288, 302, 287, 303, 14, 10, 11, 13, 14, 12, 385, 14, 15, 360, 16, 17, 360, 17, 18, 15, 16, 360, 359, 15, 360, 385, 15, 359, 360, 18, 19, 395, 359, 360, 395, 360, 19, 14, 11, 12, 385, 386, 14, 267, 109, 252, 32, 263, 31, 264, 277, 263, 32, 264, 263, 255, 257, 254, 33, 264, 32, 111, 112, 254, 264, 276, 277, 265, 264, 33, 265, 276, 264, 258, 257, 276, 277, 257, 278, 276, 257, 277, 254, 257, 258, 253, 254, 258, 111, 254, 253, 110, 111, 253, 33, 34, 283, 33, 283, 265, 259, 252, 253, 110, 253, 252, 266, 265, 283, 258, 259, 253, 276, 259, 258, 275, 259, 276, 276, 265, 266, 275, 276, 266, 283, 34, 35, 282, 283, 35, 266, 283, 282, 252, 109, 110, 270, 266, 282, 275, 266, 270, 260, 252, 259, 260, 259, 275, 267, 252, 260, 274, 275, 270, 260, 275, 274, 268, 267, 260, 112, 255, 254, 129, 234, 128, 130, 234, 129, 131, 234, 130, 236, 234, 131, 235, 236, 131, 234, 230, 169, 170, 233, 169, 230, 236, 231, 235, 231, 236, 230, 168, 169, 236, 230, 234, 136, 231, 135, 230, 229, 167, 230, 167, 168, 166, 167, 229, 229, 230, 231, 137, 231, 136, 138, 231, 137, 232, 229, 231, 138, 232, 231, 166, 164, 165, 227, 166, 229, 227, 164, 166, 237, 229, 232, 227, 229, 237, 228, 232, 138, 140, 228, 138, 237, 232, 228, 139, 140, 138, 222, 163, 164, 162, 163, 222, 141, 226, 140, 142, 226, 141, 161, 162, 222, 143, 226, 142, 227, 239, 222, 227, 222, 164, 161, 222, 239, 228, 226, 238, 237, 228, 238, 226, 228, 140, 227, 237, 239, 238, 226, 143, 219, 238, 143, 223, 239, 237, 219, 225, 238, 223, 237, 225, 144, 219, 143, 238, 225, 237, 224, 223, 225, 223, 161, 239, 217, 161, 223, 218, 224, 225, 223, 224, 218, 217, 223, 218, 218, 225, 219, 160, 161, 217, 215, 160, 217, 216, 218, 219, 216, 219, 144, 145, 216, 144, 215, 158, 159, 215, 159, 160, 146, 147, 216, 146, 216, 145, 220, 158, 215, 215, 217, 218, 215, 218, 216, 221, 215, 216, 221, 216, 147, 220, 215, 221, 157, 158, 220, 148, 221, 147, 157, 220, 156, 221, 214, 220, 220, 213, 156, 220, 214, 213, 213, 155, 156, 221, 149, 214, 149, 221, 148, 150, 214, 149, 154, 155, 213, 151, 214, 150, 154, 213, 212, 214, 212, 213, 214, 151, 212, 153, 154, 212, 152, 153, 212, 152, 212, 151, 174, 125, 126, 174, 127, 173, 127, 174, 126, 128, 173, 127, 128, 234, 173, 172, 173, 234, 233, 172, 234, 234, 169, 233, 171, 172, 233, 132, 235, 131, 170, 171, 233, 133, 235, 132, 133, 135, 235, 134, 135, 133, 135, 231, 235, 327, 180, 326, 327, 326, 331, 179, 180, 327, 330, 331, 120, 327, 331, 330, 121, 330, 120, 328, 179, 327, 329, 328, 327, 178, 179, 328, 330, 329, 327, 177, 178, 328, 328, 123, 177, 329, 122, 123, 121, 329, 330, 122, 329, 121, 123, 328, 329, 240, 241, 177, 123, 240, 177, 176, 177, 241, 123, 124, 240, 175, 176, 241, 175, 241, 240, 125, 175, 240, 125, 240, 124, 174, 175, 125, 187, 188, 320, 321, 187, 320, 186, 187, 321, 323, 320, 244, 321, 320, 323, 335, 323, 243, 322, 321, 323, 322, 323, 335, 186, 321, 322, 185, 186, 322, 334, 335, 242, 322, 335, 334, 337, 322, 334, 185, 322, 337, 184, 185, 337, 116, 334, 242, 336, 337, 334, 336, 334, 116, 117, 336, 116, 324, 337, 336, 184, 337, 324, 183, 184, 324, 333, 324, 336, 333, 336, 117, 118, 333, 117, 325, 324, 333, 332, 325, 333, 332, 333, 118, 325, 181, 182, 119, 332, 118, 324, 182, 183, 182, 324, 325, 326, 181, 325, 332, 326, 325, 331, 332, 119, 331, 326, 332, 180, 181, 326, 331, 119, 120, 244, 320, 245, 244, 245, 363, 196, 197, 198, 199, 200, 431, 195, 196, 198, 195, 198, 199, 194, 195, 199, 431, 194, 199, 364, 194, 431, 364, 431, 365, 193, 194, 364, 191, 192, 193, 364, 191, 193, 363, 364, 365, 245, 191, 364, 245, 364, 363, 190, 191, 245, 320, 190, 245, 189, 190, 320, 188, 189, 320, 431, 432, 365, 277, 278, 263, 323, 244, 243, 116, 242, 338, 243, 244, 390, 345, 243, 346, 244, 363, 390, 411, 21, 22, 358, 385, 359, 365, 384, 366, 23, 429, 22, 366, 384, 368, 358, 356, 385, 357, 356, 358, 374, 356, 357, 361, 368, 374, 361, 374, 357, 394, 358, 359, 362, 366, 368, 362, 368, 361, 393, 357, 358, 393, 358, 394, 362, 363, 365, 362, 365, 366, 392, 361, 357, 392, 357, 393, 391, 362, 361, 391, 361, 392, 429, 411, 22, 350, 394, 351, 393, 394, 350, 347, 391, 392, 348, 393, 350, 392, 393, 348, 347, 392, 348, 390, 363, 362, 390, 362, 391, 430, 411, 429, 351, 411, 430, 350, 402, 349, 351, 428, 350, 348, 350, 349, 420, 23, 24, 346, 390, 391, 346, 391, 347, 399, 347, 348, 420, 24, 25, 400, 348, 349, 399, 348, 400, 430, 428, 351, 421, 429, 23, 421, 23, 420, 428, 402, 350, 400, 349, 402, 398, 346, 347, 398, 347, 399, 413, 399, 400, 412, 398, 399, 243, 390, 346, 421, 430, 429, 421, 428, 430, 422, 428, 421, 403, 402, 428, 401, 400, 402, 401, 402, 403, 345, 346, 398, 396, 420, 25, 396, 25, 26, 421, 420, 396, 422, 421, 396, 413, 412, 399, 414, 413, 400, 414, 400, 401, 425, 428, 422, 403, 428, 425, 396, 26, 27, 410, 345, 398, 410, 398, 412, 423, 422, 396, 426, 425, 422, 419, 410, 412, 404, 403, 425, 28, 396, 27, 424, 423, 396, 397, 412, 413, 397, 413, 414, 419, 412, 397, 404, 405, 401, 404, 401, 403, 414, 401, 405, 415, 414, 405, 397, 414, 415, 338, 345, 410, 28, 424, 396, 423, 426, 422, 404, 425, 426, 409, 410, 419, 338, 410, 409, 343, 405, 404, 343, 404, 426, 29, 424, 28, 418, 419, 397, 409, 419, 418, 427, 426, 423, 427, 423, 424, 427, 424, 29, 416, 397, 415, 406, 415, 405, 406, 405, 343, 416, 415, 406, 417, 418, 397, 417, 397, 416, 30, 427, 29, 344, 426, 427, 344, 427, 30, 343, 426, 344, 339, 338, 409, 115, 338, 339, 408, 418, 417, 409, 418, 408, 407, 417, 416, 407, 416, 406, 408, 417, 407, 342, 406, 343, 407, 406, 342, 278, 343, 344, 342, 343, 278, 114, 115, 339, 263, 278, 344, 408, 339, 409, 340, 339, 408, 341, 408, 407, 340, 408, 341, 341, 407, 342, 30, 263, 344, 31, 263, 30, 256, 341, 342, 113, 339, 340, 114, 339, 113, 255, 340, 341, 278, 257, 256, 112, 113, 340, 112, 340, 255, 256, 255, 341, 342, 278, 256, 368, 375, 374, 356, 386, 385, 374, 375, 356, 351, 394, 395, 351, 395, 411, 411, 20, 21, 394, 359, 395, 411, 395, 20, 395, 19, 20, 242, 345, 338, 242, 243, 345, 115, 116, 338, 335, 243, 242, 257, 255, 256, 387, 388, 10, 377, 370, 372, 382, 381, 370, 434, 381, 382, 202, 203, 434, 353, 377, 373, 433, 434, 382, 202, 434, 433, 353, 373, 387, 201, 202, 433, 369, 382, 370, 376, 369, 370, 377, 376, 370, 354, 376, 377, 353, 354, 377, 354, 353, 387, 383, 382, 369, 433, 382, 383, 386, 354, 387, 432, 201, 433, 432, 433, 383, 200, 201, 432, 355, 376, 354, 355, 354, 386, 375, 367, 369, 376, 375, 369, 383, 369, 367, 355, 375, 376, 431, 200, 432, 384, 432, 383, 384, 383, 367, 356, 355, 386, 375, 355, 356, 368, 367, 375, 384, 367, 368, 365, 432, 384, 386, 387, 14, 14, 387, 10, 387, 373, 388, 10, 388, 9, 441, 0, 1, 440, 441, 1, 440, 1, 2, 442, 211, 0, 439, 210, 211, 439, 211, 442, 445, 2, 3, 440, 2, 445, 445, 3, 4, 439, 438, 209, 439, 209, 210, 208, 209, 438, 446, 445, 4, 448, 438, 439, 451, 446, 4, 437, 208, 438, 437, 438, 448, 207, 208, 437, 5, 451, 4, 446, 442, 445, 444, 446, 451, 447, 437, 448, 450, 451, 5, 444, 451, 450, 450, 5, 6, 436, 207, 437, 436, 437, 447, 206, 207, 436, 444, 442, 446, 443, 444, 450, 449, 443, 450, 449, 450, 6, 380, 436, 447, 448, 379, 447, 371, 447, 379, 380, 447, 371, 442, 0, 441, 442, 441, 440, 442, 440, 445, 444, 439, 442, 449, 6, 7, 435, 206, 436, 435, 436, 380, 205, 206, 435, 439, 444, 448, 443, 379, 444, 444, 379, 448, 204, 205, 435, 352, 379, 443, 389, 443, 449, 389, 449, 7, 352, 443, 389, 372, 380, 371, 378, 372, 371, 381, 435, 380, 381, 380, 372, 379, 378, 371, 203, 204, 435, 373, 379, 352, 378, 379, 373, 434, 435, 381, 203, 435, 434, 8, 389, 7, 388, 352, 389, 388, 389, 8, 373, 352, 388, 370, 381, 372, 378, 377, 372, 377, 378, 373, 9, 388, 8], "vertices": [2, 7, 167.6, -13.26, 0.99293, 65, -123.36, 39.22, 0.00707, 3, 7, 169.29, -23.98, 0.9935, 6, 243.31, -56.68, 0, 65, -121.66, 28.49, 0.0065, 2, 7, 167.18, -37.81, 0.99388, 65, -123.77, 14.66, 0.00612, 3, 7, 157.07, -56.74, 0.99398, 6, 224.91, -86.41, 0.00012, 65, -133.88, -4.26, 0.0059, 3, 7, 140.25, -73.76, 0.9902, 6, 205.08, -99.8, 0.0043, 65, -150.7, -21.28, 0.0055, 3, 7, 116.27, -92.09, 0.97223, 6, 177.98, -113.08, 0.02406, 65, -174.68, -39.61, 0.00372, 4, 7, 87.79, -106.72, 0.92236, 6, 147.18, -121.85, 0.07429, 17, 122.89, -54.08, 0.00245, 65, -203.16, -54.24, 0.0009, 3, 7, 56.94, -117.56, 0.78997, 6, 114.81, -126.44, 0.15764, 17, 91.81, -43.94, 0.05239, 4, 7, 21.88, -122.16, 0.57594, 6, 79.53, -124.08, 0.29466, 5, 271.13, -146.43, 0.00021, 17, 61.17, -26.28, 0.12919, 4, 7, 1.53, -115.54, 0.47513, 6, 60.86, -113.6, 0.32095, 5, 258.99, -128.8, 0.00919, 17, 49.03, -8.65, 0.19473, 4, 7, -8.44, -100.4, 0.36754, 6, 54.05, -96.8, 0.35154, 5, 260.28, -110.72, 0.03383, 17, 50.32, 9.43, 0.24709, 4, 7, -16.68, -109.36, 0.22699, 6, 44.21, -103.97, 0.31134, 5, 248.28, -112.82, 0.0705, 17, 38.33, 7.32, 0.39117, 4, 7, -28.21, -112.68, 0.14588, 6, 32.26, -104.97, 0.25772, 5, 237.11, -108.45, 0.10397, 17, 27.16, 11.69, 0.49243, 4, 7, -37.12, -106.73, 0.09144, 6, 24.68, -97.39, 0.22251, 5, 233.65, -98.31, 0.14885, 17, 23.69, 21.83, 0.5372, 4, 7, -37.55, -93.32, 0.01993, 6, 26.89, -84.16, 0.20595, 5, 241.46, -87.4, 0.21091, 17, 31.5, 32.74, 0.56321, 3, 5, 230.51, -89.67, 0.28308, 17, 20.55, 30.47, 0.71692, 8, -402.48, 22.64, 0, 2, 5, 227.19, -111.81, 0.05867, 17, 17.24, 8.33, 0.94133, 1, 17, 12.06, -4.58, 1, 2, 17, 0.79, -6.65, 0.99167, 66, 145.26, 279.39, 0.00833, 6, 5, 186.95, -123.19, 0.3154, 21, -46.58, 254.51, 1e-05, 17, -23.01, -3.05, 0.62418, 8, -381.04, 73.25, 3e-05, 28, 103.63, -19.98, 0.04997, 66, 121.46, 283, 0.0104, 6, 5, 166.86, -118.56, 0.58257, 21, -26.29, 257.92, 2e-05, 17, -43.1, 1.59, 0.31286, 8, -361.27, 79.1, 6e-05, 28, 83.13, -17.89, 0.09995, 66, 101.37, 287.63, 0.00453, 5, 5, 151.45, -117.58, 0.68529, 21, -11.7, 262.93, 4e-05, 17, -58.5, 2.56, 0.10978, 28, 67.72, -18.85, 0.1998, 66, 85.96, 288.6, 0.0051, 6, 5, 140.39, -124.36, 0.79029, 21, -4.1, 273.42, 6e-05, 17, -69.56, -4.22, 0.01367, 29, 74.99, 7.18, 0.13988, 28, 57.59, -26.96, 0.05155, 66, 74.91, 281.82, 0.00455, 5, 5, 114.98, -147.44, 0.7964, 21, 10.52, 304.47, 8e-05, 8, -330.31, 129.76, 0.00014, 29, 52.65, -18.88, 0.19986, 66, 49.49, 258.75, 0.00351, 6, 5, 97.7, -158.94, 0.80376, 21, 22.06, 321.71, 9e-05, 8, -320.92, 148.28, 0.00015, 30, 50.26, -6.12, 0.09979, 29, 36.94, -32.45, 0.08981, 66, 32.21, 247.25, 0.0064, 5, 5, 73.07, -168.76, 0.79019, 21, 41.03, 340.21, 0.0001, 8, -304.31, 168.94, 0.00015, 30, 27.05, -18.94, 0.1996, 66, 7.58, 237.43, 0.00996, 5, 5, 52.83, -168.1, 0.78821, 21, 59.95, 347.35, 0.0001, 8, -286.36, 178.32, 0.00015, 30, 6.88, -20.8, 0.19951, 66, -12.66, 238.09, 0.01203, 5, 5, 30.01, -158.41, 0.78925, 21, 84.69, 347.14, 0.0001, 8, -261.73, 181.12, 0.00015, 30, -16.96, -14.04, 0.19956, 66, -35.47, 247.77, 0.01094, 4, 5, 12.92, -142.94, 0.79362, 21, 106.36, 339.39, 0.0001, 30, -35.85, -0.83, 0.19978, 66, -52.56, 263.24, 0.0065, 4, 5, 0.76, -119.24, 0.79618, 21, 126.64, 322.15, 9e-05, 29, -64.19, -5.16, 0.2, 66, -64.73, 286.95, 0.00373, 4, 5, -0.52, -89.99, 0.71368, 21, 139, 295.62, 7e-05, 4, 133.93, -83.89, 0.08625, 28, -86.51, -10.43, 0.2, 3, 5, -28.06, -74.69, 0.54329, 21, 170.25, 292.03, 3e-05, 4, 102.69, -79.78, 0.45668, 3, 5, -51.08, -57.06, 0.24059, 21, 198.21, 284.56, 1e-05, 4, 74.8, -71.84, 0.7594, 4, 5, -71.12, -41.24, 0.04204, 21, 222.74, 277.61, 0, 4, 50.35, -64.47, 0.85806, 9, -98.57, -19.51, 0.09991, 4, 5, -85.98, -39.81, 0.00939, 21, 236.99, 281.99, 0, 4, 36.01, -68.61, 0.78139, 9, -84.93, -13.43, 0.20921, 3, 4, 7.93, -82.96, 0.47001, 3, -45.47, 78.71, 0.00549, 9, -59.1, 4.65, 0.5245, 3, 4, -19.93, -96.43, 0.15711, 9, -33.36, 21.83, 0.79146, 71, -119.4, -34.33, 0.05143, 3, 4, -41.8, -104.86, 0.02117, 9, -12.85, 33.19, 0.84908, 71, -101.67, -18.99, 0.12976, 2, 9, 16.94, 52.97, 0.64343, 71, -76.58, 6.49, 0.35657, 3, 3, 58.5, 136.64, 0.00042, 9, 44.86, 62.58, 0.35284, 71, -51.22, 21.63, 0.64674, 2, 9, 67.44, 69.5, 0.14376, 71, -30.55, 33.04, 0.85624, 2, 9, 93, 75.05, 0.02215, 71, -6.67, 43.72, 0.97785, 1, 71, 20.44, 52.61, 1, 1, 71, 47.79, 59.88, 1, 2, 71, 61.62, 24.32, 0.98099, 68, -221.49, -152.22, 0.01901, 2, 71, 83.72, -32.54, 0.98091, 68, -282.33, -156.7, 0.01909, 2, 3, 186.17, 15.07, 0.94982, 71, 98.71, -71.11, 0.05018, 1, 3, 193.27, 5.7, 1, 2, 3, 192.85, -5.95, 0.95021, 10, 106.24, 75.31, 0.04979, 2, 3, 210.26, 1.88, 0.59766, 10, 125.22, 77.81, 0.40234, 2, 3, 245.09, 8.99, 0.24864, 10, 160.72, 74.63, 0.75136, 2, 3, 317.52, 18.96, 0.01679, 10, 233.16, 63.39, 0.98321, 2, 3, 317.07, 24.8, 0.01557, 10, 234.41, 69.11, 0.98443, 1, 10, 341.58, 47.7, 1, 1, 10, 380.95, 42.45, 1, 2, 10, 409.01, 38.83, 0.97574, 11, -33.06, 34.99, 0.02426, 2, 10, 432.71, 33.45, 0.74457, 11, -9.01, 32.59, 0.25543, 2, 10, 450.52, 29.04, 0.33772, 11, 9.1, 30.4, 0.66228, 2, 10, 470.99, 23, 0.0225, 11, 30.04, 26.95, 0.9775, 1, 11, 55.53, 27.66, 1, 1, 11, 105.04, 31.1, 1, 1, 11, 155.26, 27.57, 1, 1, 11, 228.62, 20.18, 1, 1, 11, 287.13, 18.28, 1, 1, 11, 328.25, 19.9, 1, 2, 11, 352.98, 20.43, 0.82666, 12, -13.21, 21.57, 0.17334, 2, 11, 364.53, 21.51, 0.50725, 12, -1.57, 21.71, 0.49275, 2, 11, 380.65, 27.22, 0.15548, 12, 15.02, 26.09, 0.84452, 2, 11, 393.65, 28.26, 0.05499, 12, 28.1, 26.07, 0.94501, 2, 11, 410.63, 26.36, 0.00466, 12, 44.92, 22.8, 0.99534, 1, 12, 58.45, 25.14, 1, 1, 12, 69.4, 26.18, 1, 1, 12, 93.41, 26.25, 1, 1, 12, 143.23, 23.29, 1, 1, 12, 155.74, 26.25, 1, 1, 12, 178.06, 37.31, 1, 1, 12, 210.13, 42.94, 1, 1, 12, 213.36, 25.91, 1, 1, 12, 207.68, -16.47, 1, 1, 12, 189.76, -47.81, 1, 1, 12, 149.52, -40.22, 1, 1, 12, 140.04, -47.33, 1, 1, 12, 108.6, -42.25, 1, 1, 12, 88.82, -41.56, 1, 1, 12, 46.44, -47.45, 1, 2, 11, 393.76, -35.5, 0.04856, 12, 23.05, -37.49, 0.95144, 2, 11, 378.9, -26.96, 0.23969, 12, 8.88, -27.77, 0.76031, 2, 11, 369.24, -28.38, 0.47094, 12, -0.9, -28.4, 0.52906, 2, 11, 357.69, -25.57, 0.75306, 12, -12.21, -24.66, 0.24694, 2, 11, 341.09, -27.47, 0.9724, 12, -28.97, -25.2, 0.0276, 1, 11, 295.51, -35.05, 1, 1, 11, 139.48, -71.32, 1, 1, 11, 103.22, -70.47, 1, 2, 10, 501.3, -70.52, 0.00058, 11, 71.48, -62.11, 0.99942, 2, 10, 478.08, -60.16, 0.03695, 11, 47.3, -54.7, 0.96305, 2, 10, 456.78, -51.5, 0.19616, 11, 25.22, -48.74, 0.80384, 2, 10, 430.11, -51.43, 0.59552, 11, -1.1, -51.96, 0.40448, 2, 10, 405.38, -46.82, 0.89168, 11, -26.06, -50.45, 0.10832, 2, 10, 373.19, -48.33, 0.99748, 11, -57.62, -55.93, 0.00252, 1, 10, 251.33, -71.36, 1, 1, 10, 247.83, -65.96, 1, 1, 10, 119.58, -82.78, 1, 1, 10, 70.38, -85.31, 1, 1, 10, 34.29, -79, 1, 2, 8, 94.03, -97.26, 0.02655, 10, -6.47, -63.89, 0.97345, 2, 8, 56.95, -86.74, 0.17144, 10, -39.05, -43.17, 0.82856, 2, 8, 25.63, -73.86, 0.47673, 10, -65.44, -21.84, 0.52327, 3, 4, -32.6, 132.28, 0.02436, 8, -6.78, -54.61, 0.77521, 10, -91.02, 5.9, 0.20043, 5, 5, -53.92, 142.04, 0.00702, 21, 276.93, 101.72, 0.00036, 4, -0.95, 112.31, 0.18734, 8, -40.87, -39.19, 0.78206, 10, -119.33, 30.46, 0.02322, 5, 5, -40.6, 113.87, 0.04436, 21, 253.88, 122.64, 0.00355, 4, 21.78, 91, 0.46104, 3, -35.21, -95.51, 0.00205, 8, -66.32, -21.22, 0.489, 4, 5, -28.78, 100.78, 0.11856, 21, 237.98, 130.21, 0.01096, 4, 37.58, 83.17, 0.60802, 8, -83.05, -15.63, 0.26246, 3, 5, -12.89, 96.1, 0.27383, 4, 54.07, 84.65, 0.61552, 8, -99.18, -19.37, 0.11065, 2, 5, 13.93, 100.38, 0.56585, 4, 77.45, 98.48, 0.43415, 2, 5, 49.57, 108.29, 0.84052, 4, 107.69, 118.93, 0.15948, 3, 5, 77.09, 118.47, 0.85906, 21, 147.1, 73.33, 0.11889, 4, 129.55, 138.5, 0.02205, 2, 5, 104.62, 127.75, 0.69618, 21, 125.27, 54.22, 0.30382, 2, 5, 119.3, 132.03, 0.44453, 21, 113.36, 44.65, 0.55547, 2, 5, 105.12, 139.88, 0.23551, 21, 129.44, 42.82, 0.76449, 3, 5, 59.07, 165.84, 0.00104, 21, 181.83, 36.47, 0.95349, 22, -31.64, 32.14, 0.04547, 2, 21, 200.36, 34.42, 0.72186, 22, -12.95, 32.86, 0.27814, 2, 21, 217.65, 34.21, 0.34691, 22, 4.23, 35.22, 0.65309, 2, 21, 231.55, 33.4, 0.11964, 22, 18.14, 36.49, 0.88036, 2, 21, 251.93, 33.13, 0.00317, 22, 38.4, 39.25, 0.99683, 1, 22, 156.76, 30.18, 1, 3, 22, 227.43, 29.92, 0.88046, 24, -0.96, 31.06, 0.06787, 23, -20.82, 6.85, 0.05166, 3, 22, 233.02, 18.18, 0.63151, 24, 1.65, 18.32, 0.26281, 23, -14.6, -4.57, 0.10568, 3, 22, 239.66, 18.98, 0.36742, 24, 8.28, 17.51, 0.41799, 23, -8.03, -3.41, 0.21459, 3, 22, 246.3, 19.78, 0.15278, 24, 14.9, 16.7, 0.46661, 23, -1.46, -2.25, 0.38062, 2, 22, 258.37, 29.14, 0.00172, 23, 10.07, 7.75, 0.99828, 2, 23, 22.27, 12.77, 1, 8, 174.41, -122.42, 0, 2, 23, 37.68, 15.06, 1, 8, 189.95, -121.4, 0, 2, 23, 50, 16.8, 1, 8, 202.37, -120.67, 0, 3, 24, 93.06, 25.8, 0.71233, 23, 70.64, 29.27, 0.28767, 8, 223.96, -109.94, 0, 2, 24, 116.25, 21.49, 0.99243, 23, 94.08, 31.91, 0.00757, 1, 24, 122.32, 8.11, 1, 3, 24, 118.73, -0.82, 0.92627, 23, 102.96, 11.3, 0.07373, 8, 254.71, -130.49, 0, 1, 23, 261.04, 10.1, 1, 1, 23, 264.12, 12.88, 1, 1, 23, 285.8, 15.1, 1, 1, 23, 286.23, 29.53, 1, 1, 23, 294.24, 30.07, 1, 2, 23, 318.57, 57, 1, 8, 473.33, -102.54, 0, 2, 23, 327.76, 57.95, 1, 8, 482.57, -102.35, 0, 2, 23, 338.95, 45.89, 1, 8, 492.73, -115.28, 0, 2, 23, 382.52, 45.23, 1, 8, 536.1, -119.49, 0, 2, 23, 427.52, 42.21, 1, 8, 580.71, -126.18, 0, 2, 23, 450.87, 51.31, 1, 8, 604.72, -119.01, 0, 2, 23, 463.25, 44.26, 1, 8, 616.48, -127.05, 0, 2, 23, 524.25, 40.9, 1, 8, 677.01, -135.38, 0, 2, 23, 715.38, 30.38, 1, 8, 866.64, -161.47, 0, 1, 23, 841.83, 22.73, 1, 1, 23, 869.02, 16.09, 1, 1, 23, 918.48, 2.23, 1, 1, 23, 918.23, -2.88, 1, 1, 23, 868.98, -17.95, 1, 1, 23, 839.68, -23.37, 1, 1, 23, 714.36, -31.4, 1, 1, 23, 522.53, -40.25, 1, 1, 23, 465.39, -39.17, 1, 1, 23, 452.21, -47.86, 1, 1, 23, 428.68, -38.28, 1, 1, 23, 352.73, -43.39, 1, 1, 23, 323.5, -59.74, 1, 1, 23, 318.35, -58.07, 1, 1, 23, 294.61, -29.4, 1, 1, 23, 286.23, -27.96, 1, 1, 23, 285.36, -14.51, 1, 1, 23, 265.02, -12.05, 1, 1, 23, 261.52, -8.92, 1, 1, 23, 104.65, -10.02, 1, 2, 23, 97.31, -23.66, 1, 8, 246.22, -164.86, 0, 3, 24, 72.64, -47.2, 0.57858, 23, 72.41, -46.52, 0.42142, 8, 219.54, -185.62, 0, 2, 24, 43.05, -32.37, 1, 8, 187.48, -177.42, 0, 3, 22, 256.53, -19.72, 0.03924, 24, 15.36, -24.1, 0.96076, 8, 158.66, -175.23, 0, 3, 22, 244.02, -20.02, 0.31601, 24, 3.17, -21.4, 0.68399, 8, 146.17, -175.18, 0, 3, 22, 231.84, -19.87, 0.72323, 24, -8.61, -18.34, 0.27677, 8, 134.01, -174.7, 0, 3, 22, 227.13, -31.85, 0.90083, 24, -16.05, -28.84, 0.09917, 8, 128.98, -186.55, 0, 2, 22, 157.6, -31.51, 1, 8, 59.6, -184.3, 0, 3, 21, 241.27, -50.68, 0.02081, 22, 40.27, -45.22, 0.97919, 8, -57.88, -194.8, 0, 3, 21, 226.77, -44.81, 0.12298, 22, 25.02, -41.57, 0.87702, 8, -73, -190.72, 0, 3, 21, 213.12, -43.7, 0.30475, 22, 11.3, -42.49, 0.69525, 8, -86.71, -191.28, 0, 3, 21, 196.44, -36.15, 0.69044, 22, -6.36, -37.5, 0.30956, 8, -104.21, -185.8, 0, 3, 21, 186.56, -37.52, 0.851, 22, -15.97, -40.33, 0.149, 8, -113.87, -188.36, 0, 2, 21, 172.53, -34.83, 0.96755, 22, -30.28, -39.76, 0.03245, 1, 21, 110.78, -36.32, 1, 1, 21, 80.22, -39.29, 1, 3, 18, -34.71, 54.66, 0.00791, 21, 32.95, -40.76, 0.99209, 8, -266.22, -210.2, 0, 3, 18, -14.25, 43.12, 0.13615, 21, 9.67, -37.93, 0.86385, 8, -289.7, -210.22, 0, 3, 18, 5.51, 30.09, 0.6288, 21, -13.54, -33.46, 0.3712, 8, -313.32, -208.59, 0, 3, 18, 16.88, 15.02, 0.95306, 21, -29.78, -23.88, 0.04694, 8, -330.63, -201.06, 0, 2, 18, 16.04, 5.87, 1, 8, -334.4, -192.69, 0, 2, 5, 275.2, 118.18, 0.0117, 18, 14.09, -7.24, 0.9883, 4, 6, -24.63, 126.65, 0.00845, 5, 288.07, 124.55, 0.00855, 18, 26.95, -0.86, 0.92465, 70, 84.81, 34.33, 0.05836, 4, 6, -7.77, 125.51, 0.08625, 5, 302.7, 116.1, 0.01079, 18, 41.59, -9.31, 0.69352, 70, 82.6, 17.58, 0.20944, 5, 7, -96.56, 94.85, 0.00256, 6, 5.89, 111.93, 0.17607, 5, 308.98, 97.9, 0.01366, 18, 47.87, -27.52, 0.54413, 70, 68.18, 4.8, 0.26358, 5, 7, -96.5, 106.6, 0.00073, 6, 8.25, 123.44, 0.18693, 5, 316.17, 107.18, 0.00452, 18, 55.05, -18.23, 0.47604, 70, 79.51, 1.72, 0.33177, 4, 6, 20.24, 133.07, 0.19642, 5, 331.18, 110.55, 0.00025, 18, 70.06, -14.87, 0.40063, 70, 88.37, -10.85, 0.4027, 4, 7, -56.1, 116.5, 0.00104, 6, 49.81, 125.24, 0.23429, 18, 93.16, -34.93, 0.44354, 70, 78.68, -39.87, 0.32112, 5, 7, -67.48, 104.51, 0.00374, 6, 36.3, 115.71, 0.24063, 5, 337.94, 87.89, 0.0015, 18, 76.83, -37.53, 0.43764, 70, 70.02, -25.78, 0.31649, 5, 7, -69.87, 93.05, 0.01301, 6, 31.72, 104.93, 0.27467, 5, 329.08, 80.23, 0.00786, 18, 67.96, -45.18, 0.4372, 70, 59.56, -20.52, 0.26726, 5, 7, -63.39, 81.72, 0.04597, 6, 35.84, 92.56, 0.38045, 5, 327.33, 67.3, 0.01383, 18, 66.22, -58.11, 0.3866, 70, 46.95, -23.86, 0.17315, 5, 7, -50.82, 77.95, 0.13085, 6, 47.43, 86.39, 0.50683, 5, 335.02, 56.67, 0.01011, 18, 73.91, -68.75, 0.28704, 70, 40.06, -35.03, 0.06517, 4, 7, -34.86, 81.33, 0.30507, 6, 63.75, 86.59, 0.52879, 5, 349.76, 49.66, 0.00166, 18, 88.64, -75.76, 0.16448, 3, 7, -19.16, 99.89, 0.509, 6, 82.78, 101.71, 0.40933, 18, 112.39, -70.57, 0.08167, 3, 7, -0.74, 111.2, 0.63, 6, 103.06, 109.19, 0.32971, 18, 133.9, -72.79, 0.04029, 2, 7, 21.65, 113.38, 0.74122, 6, 125.45, 106.94, 0.25878, 2, 7, 47.8, 103.42, 0.84488, 6, 149.14, 92.05, 0.15512, 4, 7, 79.4, 84.55, 0.95666, 6, 176.42, 67.35, 0.04266, 8, -551.37, -144.67, 0, 65, -211.55, 137.02, 0.00068, 4, 7, 105.01, 72.83, 0.99126, 6, 199.24, 50.85, 0.00529, 8, -575.11, -129.52, 0, 65, -185.94, 125.31, 0.00346, 3, 7, 127.02, 60.21, 0.99499, 8, -595.16, -113.96, 0, 65, -163.93, 112.68, 0.00501, 4, 7, 146.95, 42, 0.99386, 6, 234.33, 12.4, 0, 8, -612.37, -93.17, 0, 65, -144, 94.47, 0.00614, 4, 7, 157.59, 20.96, 0.99271, 6, 240.64, -10.32, 0, 8, -619.99, -70.86, 0, 65, -133.37, 73.43, 0.00729, 1, 23, 885.27, -1.21, 1, 1, 23, 844.66, -8.72, 1, 1, 23, 844.99, 7.92, 1, 1, 23, 429.9, -17.91, 1, 2, 23, 429.6, 19.3, 1, 8, 580.91, -149.18, 0, 1, 23, 415.11, -16.99, 1, 1, 23, 414.66, -6.02, 1, 2, 23, 370.87, 21.11, 1, 8, 522.52, -142.58, 0, 1, 23, 483.51, -16.72, 1, 2, 23, 482.47, 17.85, 1, 8, 633.48, -154.94, 0, 1, 23, 336.94, -43.21, 1, 1, 23, 405.39, -13.88, 1, 1, 23, 405.81, -8.06, 1, 2, 23, 373.92, 10.16, 1, 8, 524.67, -153.74, 0, 2, 23, 335.68, 37.82, 1, 8, 488.82, -123.05, 0, 1, 23, 296.69, -13.08, 1, 1, 23, 296.34, 15.48, 1, 1, 23, 288.83, -5.86, 1, 1, 23, 262.09, -3.31, 1, 1, 23, 262.48, 4.44, 1, 1, 23, 289.26, 7.36, 1, 2, 24, 70.91, -8.79, 1, 8, 209.68, -148.45, 0, 3, 24, 64.35, -1.69, 0.84084, 23, 51.2, -5.41, 0.15916, 8, 201.76, -142.91, 0, 2, 24, 90.74, 4.99, 1, 8, 226.13, -130.77, 0, 2, 24, 77.31, 3.21, 0.04599, 23, 62.17, 3.06, 0.95401, 2, 23, 297.6, 1.15, 1, 8, 447.87, -156.49, 0, 2, 23, 353, 24.34, 1, 8, 504.98, -137.91, 0, 1, 23, 354.02, -34.39, 1, 3, 22, 221.29, 17.58, 0.90739, 24, -9.87, 20.54, 0.05029, 23, -26.27, -5.81, 0.04231, 3, 22, 222.78, -20.25, 0.89031, 24, -17.49, -16.55, 0.10969, 8, 124.96, -174.84, 0, 4, 5, 143.92, 117.78, 0.70076, 18, -117.19, -7.64, 0.02296, 21, 85.2, 48.38, 0.26499, 66, 78.44, 523.96, 0.01129, 4, 5, 170.62, 108.81, 0.67835, 18, -90.5, -16.61, 0.1797, 21, 57.16, 46.44, 0.1219, 66, 105.13, 515, 0.02006, 4, 5, 210.83, 111, 0.46475, 18, -50.28, -14.42, 0.44288, 21, 20.91, 29.03, 0.07016, 66, 145.34, 517.18, 0.02222, 4, 5, 250.45, 117.78, 0.06644, 18, -10.66, -7.64, 0.62959, 21, -13.04, 7.59, 0.29221, 66, 184.97, 523.96, 0.01176, 4, 3, 78.02, -111.22, 0.02787, 8, 46.91, -36.93, 0.50594, 10, -34.36, 7.43, 0.44521, 68, -439.62, -40.65, 0.02098, 4, 3, 107.97, -72.35, 0.45819, 8, 76.86, 1.94, 0.24924, 10, 5.6, 36.06, 0.27231, 68, -403.62, -73.98, 0.02026, 4, 3, 135.1, -46.05, 0.79813, 8, 104, 28.24, 0.04268, 10, 39.23, 53.47, 0.14259, 68, -379.87, -103.37, 0.0166, 4, 3, 168.01, -21.77, 0.92209, 8, 136.91, 52.52, 0.00032, 10, 77.83, 67.28, 0.06791, 68, -358.65, -138.34, 0.00969, 4, 3, 51.47, -97.61, 0.00716, 8, 20.36, -23.32, 0.87212, 10, -55.94, 28.09, 0.10059, 68, -423.68, -15.44, 0.02012, 4, 5, -91.91, 130.29, 0.00097, 4, -31.98, 87.43, 0.09074, 8, -13.56, -10.27, 0.88431, 68, -407.62, 17.18, 0.02398, 7, 5, -50.45, 81.1, 0.05394, 21, 250.44, 156.68, 0.00421, 4, 24.65, 56.91, 0.67931, 3, -42.75, -62.13, 0.01465, 8, -73.86, 12.16, 0.22244, 66, -115.93, 487.29, 0.01807, 68, -379.85, 75.21, 0.00737, 7, 5, -32.27, 73.49, 0.12895, 21, 230.77, 156.75, 0.00803, 4, 44.35, 56.51, 0.73353, 3, -62.32, -64.45, 0.00056, 8, -93.43, 9.84, 0.1026, 66, -97.76, 479.68, 0.02378, 68, -380.4, 94.91, 0.00256, 3, 5, -8.98, 66.61, 0.32442, 4, 68.54, 58.66, 0.64436, 66, -74.47, 472.79, 0.03123, 4, 5, 15.11, 62.38, 0.55237, 4, 92.5, 63.57, 0.37468, 25, -94.33, 35.34, 0.03995, 66, -50.37, 468.56, 0.033, 4, 5, 14.15, 21.1, 0.71022, 4, 106.77, 24.82, 0.20239, 25, -90.13, -5.74, 0.0499, 66, -51.33, 427.28, 0.03748, 4, 5, -14.88, 27.68, 0.18525, 4, 77.35, 20.28, 0.77419, 66, -80.37, 433.87, 0.03968, 68, -345.79, 129.6, 0.00089, 5, 5, -44.89, 35.68, 0.03175, 21, 227.95, 196.52, 0.00177, 4, 46.51, 16.7, 0.93008, 66, -110.37, 441.86, 0.03389, 68, -340.74, 98.97, 0.00251, 5, 5, -66.89, 46.91, 0.00874, 21, 252.53, 194.56, 0.00056, 4, 21.92, 19.07, 0.95648, 66, -132.37, 453.1, 0.02533, 68, -341.93, 74.29, 0.00888, 6, 5, -95.29, 68.3, 0.00054, 4, -12.35, 28.53, 0.5378, 3, -10.01, -28.93, 0.3206, 8, -41.12, 45.36, 0.10774, 66, -160.77, 474.49, 0.01317, 68, -349.74, 39.61, 0.02016, 3, 3, 88.29, -29.17, 0.88787, 8, 57.19, 45.12, 0.08317, 68, -358.84, -58.28, 0.02897, 3, 3, 76.94, -56.88, 0.55332, 8, 45.83, 17.41, 0.41675, 68, -385.41, -44.47, 0.02993, 5, 5, -9.57, -54.6, 0.58498, 21, 160.87, 266.39, 3e-05, 4, 112.51, -54.3, 0.33791, 28, -99.91, 23.56, 0.04993, 66, -75.06, 351.59, 0.02716, 4, 5, -41.99, -35.88, 0.18187, 21, 197.93, 261.51, 0, 4, 75.48, -48.79, 0.78849, 66, -107.48, 370.3, 0.02963, 6, 5, -68.24, -20.94, 0.01669, 21, 227.85, 257.76, 0, 4, 45.58, -44.54, 0.95479, 9, -91.09, -38.59, 0.0003, 66, -133.72, 385.25, 0.02579, 68, -279.53, 100.98, 0.00244, 5, 4, 19.19, -45.72, 0.91966, 3, -51.48, 40.27, 0.02064, 9, -65.11, -33.78, 0.03134, 66, -158.71, 393.84, 0.01973, 68, -277.08, 74.67, 0.00862, 7, 5, -70.33, 98.73, 0.01627, 21, 275.52, 148.01, 0.0011, 4, -0.32, 66, 0.42284, 3, -16.77, -67.7, 0.06087, 8, -47.88, 6.59, 0.47442, 66, -135.82, 504.91, 0.00944, 68, -387.74, 49.84, 0.01506, 5, 4, -53.42, 44.48, 0.03178, 3, 32.86, -39.06, 0.57702, 8, 1.75, 35.23, 0.35561, 66, -193.11, 504.4, 0.00103, 68, -363.69, -2.17, 0.03456, 2, 3, 88.63, 13.28, 0.9712, 68, -316.59, -62.43, 0.0288, 5, 4, -13.78, -51.77, 0.62059, 3, -19.66, 50.8, 0.23888, 9, -33.3, -23.26, 0.11066, 66, -191.59, 400.32, 0.01165, 68, -269.46, 42.03, 0.01821, 5, 4, -50.37, -59.01, 0.0694, 3, 15.58, 63.02, 0.4389, 9, 1.95, -11.04, 0.46109, 66, -228.29, 407.02, 0.00062, 68, -260.47, 5.83, 0.02999, 4, 3, 56.4, 72.16, 0.4775, 9, 42.76, -1.9, 0.45393, 71, -40.03, -41.9, 0.04171, 68, -255.04, -35.64, 0.02686, 3, 3, 37.26, 19.97, 0.96348, 66, -225.98, 455.16, 0.00126, 68, -305.3, -11.88, 0.03525, 4, 4, -23.73, -15.64, 0.26599, 3, -4.83, 16.39, 0.70177, 66, -187.58, 437.58, 0.01085, 68, -305.07, 30.36, 0.02139, 4, 4, 11.26, -15.93, 0.95779, 3, -39.52, 11.86, 0.01242, 66, -155.14, 424.46, 0.01981, 68, -306.46, 65.32, 0.00997, 3, 4, 46.34, -17.64, 0.97266, 66, -123.14, 409.99, 0.02714, 68, -306.43, 100.45, 0.0002, 3, 5, -22.96, -11.45, 0.14575, 4, 84.2, -19.09, 0.82414, 66, -88.45, 394.73, 0.03012, 5, 5, 12.35, -25.77, 0.81751, 4, 122.31, -19.43, 0.09936, 25, -86.07, -52.46, 0.02496, 28, -81.75, 54.89, 0.02433, 66, -53.14, 380.42, 0.03384, 2, 3, 75.53, 47.98, 0.97164, 68, -280.86, -52.51, 0.02836, 4, 3, 29.46, 94.85, 0.01484, 9, 15.83, 20.79, 0.8509, 71, -71.05, -25.23, 0.11516, 68, -230.02, -10.86, 0.0191, 3, 4, -34.61, -80.54, 0.10292, 9, -16.63, 8.11, 0.87451, 68, -239.73, 22.6, 0.02256, 5, 4, -2.68, -69.95, 0.42814, 3, -33.16, 67.28, 0.02527, 9, -46.79, -6.77, 0.52583, 66, -187.95, 379.33, 0.00817, 68, -251.83, 53.99, 0.01259, 6, 5, -92.96, -26.4, 0.0014, 4, 24.59, -58.69, 0.75765, 3, -58.62, 52.37, 0.00491, 9, -72.25, -21.68, 0.21174, 66, -158.44, 379.79, 0.01812, 68, -264.39, 80.69, 0.00618, 2, 3, 136.74, 9.89, 0.97975, 68, -324.31, -110.05, 0.02025, 4, 3, 169.13, 6.72, 0.91787, 10, 87.11, 94.26, 0.02829, 71, 83.75, -82.78, 0.04397, 68, -330.38, -142.02, 0.00987, 3, 3, 164.7, 24.98, 0.9075, 71, 75.66, -65.82, 0.0827, 68, -311.8, -139.25, 0.0098, 3, 3, 128.7, 53.39, 0.80493, 71, 34.59, -45.42, 0.17815, 68, -280.26, -105.96, 0.01691, 4, 3, 92.52, 83.89, 0.47777, 9, 78.88, 9.83, 0.16956, 71, -7.09, -23, 0.33187, 68, -246.62, -72.67, 0.02081, 4, 3, 64.63, 106.5, 0.04146, 9, 51, 32.44, 0.39187, 71, -39.02, -6.6, 0.54713, 68, -221.59, -46.94, 0.01954, 3, 8, 69.9, -48.09, 0.16822, 10, -15.49, -9.86, 0.81291, 68, -452.81, -62.54, 0.01888, 3, 8, 105.83, -53.37, 0.0034, 10, 17.51, -25.23, 0.97766, 68, -461.31, -97.85, 0.01894, 2, 10, 54.43, -36.49, 0.97999, 68, -464.99, -136.18, 0.02001, 2, 10, 106.98, -40.83, 0.97884, 68, -458.79, -188.39, 0.02116, 2, 10, 243.5, -29.49, 0.98278, 68, -420.52, -319.54, 0.01722, 3, 3, 332.97, -15.21, 0.00454, 10, 238.17, 26.22, 0.98043, 68, -366.98, -303.22, 0.01503, 3, 3, 196.65, -47.72, 0.36503, 10, 97.87, 34.2, 0.61275, 68, -387.08, -164.52, 0.02221, 4, 3, 163.8, -57.68, 0.4571, 8, 132.69, 16.61, 0.00314, 10, 63.44, 34.1, 0.52071, 68, -394.04, -130.9, 0.01905, 4, 3, 138.55, -62.09, 0.53832, 8, 107.45, 12.2, 0.03912, 10, 37.93, 37.12, 0.40549, 68, -396.15, -105.36, 0.01707, 3, 3, 228.72, -39.94, 0.19808, 10, 130.91, 32.45, 0.78095, 68, -382.22, -197.16, 0.02097, 3, 3, 153.53, 57.2, 0.3746, 71, 58.11, -36.58, 0.60752, 68, -278.7, -131.03, 0.01788, 2, 71, 28.32, 14.42, 0.98071, 68, -221.2, -117.49, 0.01929, 3, 3, 146.7, 39.18, 0.89313, 71, 55.13, -55.62, 0.09361, 68, -296.03, -122.6, 0.01326, 3, 3, 120.11, 73.23, 0.55963, 71, 22.11, -27.77, 0.4214, 68, -259.73, -99.2, 0.01897, 3, 9, 84.2, 35.98, 0.10243, 71, -7.25, 3.68, 0.87809, 68, -221.06, -80.32, 0.01949, 2, 10, 379.57, -20.78, 0.98798, 68, -384.91, -450.77, 0.01202, 3, 10, 411.52, -22.41, 0.92175, 11, -23.01, -25.46, 0.06573, 68, -380.15, -482.32, 0.01252, 3, 10, 439.02, -24.95, 0.45981, 11, 4.43, -24.59, 0.52785, 68, -377.17, -509.7, 0.01234, 3, 10, 465.83, -24.93, 0.06764, 11, 30.87, -21.25, 0.91895, 68, -371.81, -535.89, 0.01341, 3, 10, 494.73, -29.58, 0.00143, 11, 59.95, -22.3, 0.98501, 68, -370.62, -565.06, 0.01355, 2, 11, 59.62, 4.59, 0.98413, 68, -343.83, -562.68, 0.01587, 3, 10, 470.37, 4.05, 0.0013, 11, 31.77, 8.06, 0.98375, 68, -342.51, -534.55, 0.01495, 3, 10, 442.57, 11.68, 0.41099, 11, 3.41, 12.19, 0.57599, 68, -340.57, -505.86, 0.01302, 3, 10, 413.53, 17.28, 0.96533, 11, -25.93, 14.17, 0.02298, 68, -340.86, -476.36, 0.01169, 2, 10, 380.99, 19.3, 0.98785, 68, -345.36, -444.16, 0.01215, 2, 11, 110.42, -33.82, 0.98863, 68, -378.23, -616.43, 0.01137, 2, 11, 157.75, -34.79, 0.99411, 68, -375.57, -663.85, 0.00589, 1, 11, 289.83, -7.03, 1, 2, 11, 155.95, 4.35, 0.99236, 68, -336.67, -659.06, 0.00764, 2, 11, 109.23, 6.35, 0.98713, 68, -338.27, -612.17, 0.01287, 3, 18, -13.95, 14.14, 0.20517, 21, -1.69, -11.27, 0.78152, 66, 181.68, 545.75, 0.01332, 3, 18, -34.03, 29.44, 0.0065, 21, 22.69, -17.72, 0.9786, 66, 161.6, 561.05, 0.0149, 2, 21, 75.7, -17.74, 0.98412, 66, 112.55, 581.4, 0.01588, 3, 5, 201.06, 128.38, 0.19854, 21, 36.56, 16.71, 0.78457, 66, 135.57, 534.56, 0.0169, 3, 21, 172.45, -16.96, 0.99582, 8, -130.39, -169.67, 0, 66, 22.72, 617.79, 0.00418, 3, 21, 188.71, -15.21, 0.93813, 22, -17.14, -17.94, 0.06187, 8, -114.43, -165.95, 0, 3, 21, 204.83, -17.59, 0.60691, 22, -0.8, -17.91, 0.39309, 8, -98.12, -166.37, 0, 3, 21, 221.7, -20, 0.16397, 22, 16.3, -17.79, 0.83603, 8, -81.05, -166.72, 0, 3, 21, 241.94, -22.9, 0.00543, 22, 36.81, -17.64, 0.99457, 8, -60.58, -167.13, 0, 1, 22, 36.32, 15.32, 1, 2, 21, 229.53, 14.07, 0.07041, 22, 19.01, 17.07, 0.92959, 2, 21, 210.71, 14.28, 0.44759, 22, 0.31, 14.49, 0.55241, 2, 21, 192.32, 15.98, 0.9097, 22, -18.2, 13.44, 0.0903, 3, 5, 72.05, 180.43, 0.00103, 21, 175.44, 18.02, 0.99492, 66, 6.56, 586.62, 0.00405, 3, 5, 151.02, 141.89, 0.16359, 21, 87.87, 23.39, 0.82126, 66, 85.54, 548.08, 0.01515, 3, 5, 175.42, 132.53, 0.22891, 21, 61.8, 22.69, 0.75561, 66, 109.93, 538.72, 0.01548, 3, 5, 125.85, 153.75, 0.07665, 21, 115.62, 22.07, 0.91057, 66, 60.36, 559.94, 0.01278, 2, 21, 108.95, -17.33, 0.98557, 66, 81.63, 593.78, 0.01444, 3, 5, 119.5, 101.96, 0.7752, 25, 4.3, 87.64, 0.19894, 66, 54.01, 508.15, 0.02586, 4, 5, 80.31, 97.6, 0.76658, 4, 140.21, 120.28, 0.01225, 25, -34.03, 78.43, 0.1991, 66, 14.83, 503.79, 0.02207, 4, 5, 51.11, 82.21, 0.66623, 4, 118.71, 95.24, 0.11033, 25, -61.08, 59.51, 0.199, 66, -14.37, 488.4, 0.02443, 4, 5, 29.12, 45.67, 0.61311, 4, 111.66, 53.18, 0.15363, 25, -78.35, 20.52, 0.19857, 66, -36.37, 451.86, 0.03468, 3, 5, 25.72, 3.1, 0.76611, 25, -76.41, -22.15, 0.19855, 66, -39.77, 409.28, 0.03535, 4, 5, 39.02, -37.53, 0.7799, 25, -58.14, -60.8, 0.08945, 28, -53.83, 46.55, 0.09939, 66, -26.47, 368.65, 0.03126, 5, 5, 9.53, -63.37, 0.67531, 21, 139.9, 267.18, 5e-05, 4, 133.5, -55.44, 0.09869, 28, -79.86, 17.23, 0.19889, 66, -55.96, 342.82, 0.02706, 3, 5, 154.74, 88.79, 0.76812, 25, 40.91, 78.97, 0.19863, 66, 89.25, 494.97, 0.03325, 3, 5, 182.12, 58.1, 0.76488, 25, 71.91, 51.94, 0.19849, 66, 116.64, 464.29, 0.03663, 3, 5, 183.5, 14.67, 0.76902, 25, 78.69, 9.02, 0.19867, 66, 118.01, 420.86, 0.03231, 3, 5, 164.6, -23, 0.77269, 25, 64.64, -30.72, 0.19883, 66, 99.11, 383.18, 0.02848, 4, 5, 137.97, -41.49, 0.78412, 25, 40.53, -52.38, 0.09948, 28, 44.85, 54.97, 0.08953, 66, 72.49, 364.7, 0.02687, 3, 5, 157.43, -47.86, 0.77505, 28, 64.95, 51.08, 0.19894, 66, 91.94, 358.33, 0.02601, 4, 5, 168.43, -73.52, 0.76426, 17, -41.52, 46.62, 0.01521, 28, 79.07, 26.98, 0.19913, 66, 102.95, 332.66, 0.02141, 3, 7, 37.01, -53.91, 0.83844, 6, 107.73, -60.12, 0.16156, 8, -490.2, -13.42, 0, 4, 7, -7.8, -43.54, 0.30993, 6, 65.82, -41.17, 0.67886, 5, 295.34, -65.95, 0.01121, 8, -447.26, -29.9, 0, 5, 7, -27.03, -42.62, 0.0887, 6, 47.14, -36.5, 0.85299, 5, 280.63, -53.53, 0.04684, 17, 70.68, 66.61, 0.01147, 8, -428.34, -33.48, 0, 3, 6, 26.05, -33.18, 0.74981, 5, 263.16, -41.26, 0.21139, 17, 53.2, 78.89, 0.0388, 3, 6, 3.23, -36.28, 0.18822, 5, 241.31, -33.99, 0.78199, 17, 31.35, 86.15, 0.0298, 2, 5, 211.79, -25.39, 0.98274, 66, 146.31, 380.79, 0.01726, 3, 5, 210, -45.65, 0.92282, 17, 0.04, 74.49, 0.06219, 66, 144.51, 360.54, 0.01499, 4, 5, 206.05, -81.29, 0.53932, 17, -3.91, 38.85, 0.44776, 8, -377.06, 27.38, 1e-05, 66, 140.56, 324.89, 0.01291, 5, 5, 207.08, -107.45, 0.21244, 21, -59.13, 232.27, 0, 17, -2.88, 12.69, 0.77692, 8, -390.82, 49.64, 1e-05, 66, 141.59, 298.74, 0.01063, 2, 5, 219.95, 6.82, 0.98113, 66, 154.47, 413.01, 0.01887, 3, 5, 223.64, 26.69, 0.94941, 18, -37.48, -98.73, 0.03046, 66, 158.15, 432.88, 0.02014, 3, 5, 235.25, 76.7, 0.56547, 18, -25.87, -48.72, 0.41035, 66, 169.76, 482.89, 0.02418, 3, 6, -23.55, 87.24, 0.0176, 5, 271.67, 88.7, 0.14209, 18, 10.56, -36.72, 0.8403, 4, 6, -10.19, 47.05, 0.16746, 5, 265.96, 46.73, 0.43165, 18, 4.85, -78.69, 0.38601, 66, 200.48, 452.92, 0.01488, 4, 6, -9.06, 18.13, 0.09499, 5, 254.24, 20.26, 0.81329, 18, -6.88, -105.15, 0.08698, 66, 188.75, 426.45, 0.00474, 3, 6, 22.98, 7.94, 0.98411, 18, 17.4, -128.42, 0.0004, 66, 213.03, 403.19, 0.01549, 3, 6, -6.7, 1.01, 0.0437, 5, 248.82, 3.86, 0.94211, 66, 183.33, 410.05, 0.01419, 2, 6, 46.87, 9.15, 0.98567, 66, 235.01, 393.76, 0.01433, 3, 7, -12.15, 7.18, 0.12191, 6, 71.49, 9.42, 0.86355, 66, 257.23, 383.15, 0.01454, 3, 7, 33.45, 11.77, 0.9743, 6, 117.11, 4.99, 0.00933, 66, 296.23, 359.08, 0.01637, 3, 7, 11.36, 9.64, 0.87711, 6, 95.03, 7.23, 0.10717, 66, 277.4, 370.82, 0.01572, 4, 7, 12.01, -47.22, 0.6225, 6, 84.52, -48.65, 0.37556, 5, 308.84, -80.91, 0.00194, 8, -466.37, -23.52, 0, 3, 6, -2.93, -25.12, 0.08521, 5, 240.69, -21.27, 0.90426, 66, 175.21, 384.92, 0.01053, 3, 6, 23.7, -23.9, 0.8143, 5, 265.14, -31.9, 0.1751, 66, 199.65, 374.29, 0.01061, 5, 7, -28.63, -32.67, 0.03969, 6, 47.53, -26.43, 0.92364, 5, 285.41, -44.66, 0.02604, 8, -428.14, -43.56, 0, 66, 219.93, 361.53, 0.01063, 5, 7, -7.64, -32.64, 0.28572, 6, 68.11, -30.51, 0.70032, 5, 302.1, -57.4, 0.00344, 8, -448.93, -40.67, 0, 66, 236.61, 348.79, 0.01052, 4, 7, 14.69, -31.93, 0.70935, 6, 90.15, -34.19, 0.27989, 8, -471.14, -38.29, 0, 66, 254.77, 335.78, 0.01076, 4, 7, 35.57, -33.05, 0.90408, 6, 110.41, -39.38, 0.08342, 8, -491.67, -34.28, 0, 66, 270.67, 322.2, 0.0125, 2, 7, 33.39, 31.91, 0.93905, 6, 121, 24.75, 0.06095, 3, 7, 3.38, 27.15, 0.63, 6, 90.64, 25.96, 0.36826, 66, 281.71, 389.57, 0.00173, 4, 7, -19.63, 16.53, 0.11426, 6, 65.99, 20.06, 0.87399, 64, -358.79, 69.01, 0.00185, 66, 256.98, 395.13, 0.00991, 3, 6, 36.57, 14.73, 0.98823, 64, -386.59, 58.02, 0.00149, 66, 228.22, 403.3, 0.01028, 4, 6, 12.64, 19.81, 0.71909, 5, 274.46, 12.21, 0.2024, 18, 13.34, -113.2, 0.06823, 66, 208.97, 418.4, 0.01027, 5, 6, 12.3, -67.35, 0.11771, 5, 235.76, -65.88, 0.48386, 17, 25.8, 54.26, 0.38161, 8, -395.35, -0.66, 0, 66, 170.27, 340.31, 0.01682, 6, 7, -37.74, -68.55, 0.05587, 6, 31.56, -59.82, 0.4585, 5, 256.37, -67.61, 0.22409, 17, 46.42, 52.53, 0.24921, 64, -376.89, -16.07, 0.00022, 66, 190.88, 338.58, 0.01211, 6, 7, -11.64, -77.25, 0.31436, 6, 55.45, -73.47, 0.46558, 5, 271.81, -90.39, 0.04409, 17, 61.85, 29.76, 0.16479, 64, -350.79, -24.77, 0.00392, 66, 206.32, 315.8, 0.00726, 7, 7, 9.67, -83.07, 0.50585, 6, 75.21, -83.35, 0.38799, 5, 285.19, -107.96, 0.00858, 17, 75.24, 12.18, 0.08939, 8, -459.09, 11.66, 0, 64, -329.48, -30.59, 0.00593, 66, 219.7, 298.23, 0.00227, 5, 7, 36.95, -87.82, 0.74074, 6, 101.02, -93.36, 0.22326, 17, 94, -8.17, 0.02781, 8, -485.44, 20.15, 0, 64, -302.21, -35.35, 0.0082, 4, 5, 205.91, 69.39, 0.77244, 18, -55.21, -56.02, 0.09713, 25, 94.1, 66.11, 0.09968, 66, 140.42, 475.58, 0.03075, 3, 5, 204.14, 24.82, 0.87516, 25, 97.91, 21.66, 0.09974, 66, 138.65, 431.01, 0.0251, 3, 5, 195.94, -3.1, 0.87624, 25, 93.25, -7.06, 0.09975, 66, 130.45, 403.09, 0.02401, 3, 5, 188.62, -32.89, 0.87792, 28, 94.03, 69.82, 0.09977, 66, 123.13, 373.3, 0.02231, 4, 5, 186.53, -56.69, 0.83483, 17, -23.42, 63.45, 0.04601, 28, 94.93, 45.94, 0.0998, 66, 121.05, 349.49, 0.01935, 6, 5, 185.92, -93.19, 0.53201, 21, -34.16, 227.19, 1e-05, 17, -24.04, 26.96, 0.34997, 8, -365.39, 47.63, 3e-05, 28, 98.87, 9.66, 0.09982, 66, 120.43, 313, 0.01816, 4, 5, 45.65, -149.47, 0.72335, 21, 73.68, 332.89, 9e-05, 30, -2.56, -3.21, 0.24819, 66, -19.83, 256.72, 0.02837, 3, 5, 88.31, 24.35, 0.68229, 27, -4.22, 1.48, 0.24521, 66, 22.82, 430.53, 0.0725, 3, 5, 155.48, 42.44, 0.75338, 26, 53.14, 30.61, 0.19798, 66, 89.99, 448.62, 0.04865, 3, 5, 154.88, -1.46, 0.75623, 26, 58.03, -13.01, 0.1981, 66, 89.39, 404.73, 0.04566, 3, 5, 130.29, -30.35, 0.75868, 26, 37.23, -44.75, 0.19821, 66, 64.8, 375.83, 0.0431, 3, 5, 98.36, -38.89, 0.75859, 26, 6.62, -57.2, 0.19821, 66, 32.87, 367.3, 0.0432, 4, 5, 118.53, -52.2, 0.78469, 25, 22.58, -65.43, 0.09949, 28, 26.9, 41.92, 0.08954, 66, 53.05, 353.99, 0.02628, 4, 5, 90.57, -57.43, 0.78416, 25, -4.51, -74.11, 0.09948, 28, -0.19, 33.24, 0.08953, 66, 25.09, 348.75, 0.02684, 4, 5, 61.51, -49.44, 0.78208, 25, -34.34, -69.81, 0.09944, 28, -30.02, 37.54, 0.08949, 66, -3.98, 356.74, 0.02899, 3, 5, 64.53, -27.25, 0.75617, 26, -28.4, -49.88, 0.1981, 66, -0.96, 378.93, 0.04572, 3, 5, 42.06, -1.71, 0.75298, 26, -53.88, -27.34, 0.19796, 66, -23.43, 404.47, 0.04906, 4, 5, 41.27, 33.89, 0.72475, 4, 127.3, 46.68, 0.02652, 26, -59.1, 7.89, 0.19788, 66, -24.21, 440.08, 0.05086, 4, 5, 58.03, 65.26, 0.70375, 4, 131.36, 82.01, 0.04808, 26, -46.39, 41.1, 0.19791, 66, -7.46, 471.44, 0.05026, 3, 5, 92.86, 79.96, 0.75338, 26, -13.67, 60.04, 0.19798, 66, 27.37, 486.15, 0.04865, 3, 5, 130.56, 70.5, 0.75344, 26, 24.92, 55.35, 0.19798, 66, 65.07, 476.69, 0.04858, 5, 5, 162.54, -99.25, 0.71329, 21, -14.92, 241.74, 3e-05, 17, -47.42, 20.9, 0.07156, 28, 76.43, 0.73, 0.19936, 66, 97.05, 306.94, 0.01576, 3, 5, 128.18, 36.57, 0.73908, 27, 33.81, 18.58, 0.19732, 66, 62.69, 442.75, 0.06361, 3, 5, 125.99, 4.09, 0.74102, 27, 35.7, -13.92, 0.19741, 66, 60.51, 410.27, 0.06157, 3, 5, 100.99, -13.37, 0.74204, 27, 13.06, -34.36, 0.19746, 66, 35.5, 392.81, 0.06051, 3, 5, 73.45, -6.44, 0.74047, 27, -15.12, -30.92, 0.19738, 66, 7.97, 399.75, 0.06215, 3, 5, 56.51, 11.08, 0.7394, 27, -34.12, -15.65, 0.19733, 66, -8.98, 417.27, 0.06326, 3, 5, 60.74, 43.05, 0.73902, 27, -33.91, 16.6, 0.19732, 66, -4.74, 449.24, 0.06366, 3, 5, 80.08, 61.57, 0.74018, 27, -17.03, 37.39, 0.19737, 66, 14.6, 467.76, 0.06245, 3, 5, 109.28, 56.11, 0.73965, 27, 12.62, 35.61, 0.19735, 66, 43.79, 462.29, 0.06301, 5, 5, 87.19, -152.56, 0.77833, 21, 34.19, 319.84, 9e-05, 8, -308.64, 147.89, 0.00015, 30, 39.04, -1.1, 0.19909, 66, 21.71, 253.63, 0.02234, 4, 5, 83.17, -132.35, 0.76599, 21, 45.62, 302.7, 8e-05, 30, 32.53, 18.45, 0.19854, 66, 17.69, 273.84, 0.03538, 4, 5, 64.75, -119.34, 0.76245, 21, 67.58, 297.74, 9e-05, 30, 12.63, 29.06, 0.19839, 66, -0.73, 286.84, 0.03908, 4, 5, 35.87, -113.7, 0.76422, 21, 96.38, 303.59, 9e-05, 30, -16.73, 31.06, 0.19847, 66, -29.62, 292.49, 0.03722, 4, 5, 15.57, -123.59, 0.77513, 21, 111.32, 320.5, 9e-05, 30, -35.64, 18.71, 0.19895, 66, -49.91, 282.6, 0.02583, 4, 5, 73.57, -72.56, 0.76423, 21, 77.34, 251.15, 7e-05, 29, 2.22, 50.24, 0.19846, 66, 8.08, 333.62, 0.03724, 4, 5, 38.14, -71.41, 0.76614, 21, 110.45, 263.65, 8e-05, 29, -33.08, 46.97, 0.19855, 66, -27.35, 334.78, 0.03523, 5, 5, 11.39, -89.73, 0.74381, 21, 128.11, 290.82, 8e-05, 4, 144.91, -79.28, 0.03225, 29, -57.33, 25.45, 0.19898, 66, -54.1, 316.45, 0.02488, 4, 5, 106.48, -82.32, 0.76558, 21, 43.25, 247.57, 4e-05, 29, 36.1, 44.66, 0.19852, 66, 41, 323.86, 0.03585, 4, 5, 125.68, -127.11, 0.7808, 21, 8.43, 281.6, 7e-05, 29, 60.73, 2.62, 0.19919, 66, 60.19, 279.07, 0.01994, 4, 5, 124.28, -103.41, 0.7711, 21, 18.77, 260.24, 5e-05, 29, 56.39, 25.96, 0.19877, 66, 58.79, 302.77, 0.03008, 6, 7, -84.52, 69.95, 0.01602, 6, 12.82, 85.15, 0.28469, 5, 303.41, 70.8, 0.04622, 18, 42.29, -54.62, 0.49438, 70, 41.02, -0.42, 0.15715, 64, -423.67, 122.43, 0.00155, 7, 7, -66.91, 44.91, 0.03884, 6, 25.19, 57.15, 0.57953, 5, 302.17, 40.21, 0.06442, 18, 41.05, -85.21, 0.27766, 70, 12.28, -10.98, 0.02357, 64, -406.06, 97.38, 0.0016, 66, 236.68, 446.4, 0.01438, 6, 7, -39.28, 42.66, 0.13475, 6, 51.84, 49.53, 0.74292, 5, 322.75, 21.63, 0.00732, 18, 61.63, -103.78, 0.09689, 64, -378.43, 95.14, 0.00477, 66, 257.26, 427.82, 0.01335, 5, 7, -15.08, 52.92, 0.40013, 6, 77.58, 54.84, 0.55695, 18, 87.08, -110.35, 0.02644, 64, -354.23, 105.39, 0.00855, 66, 282.71, 421.25, 0.00793, 4, 7, 13.11, 69.21, 0.69979, 6, 108.42, 65.3, 0.28765, 64, -326.04, 121.69, 0.01199, 66, 315, 417.05, 0.00058, 3, 7, 45.26, 69.55, 0.878, 6, 140.01, 59.34, 0.10763, 64, -293.9, 122.03, 0.01437, 4, 7, 78.92, 59, 0.96466, 6, 170.95, 42.39, 0.01847, 8, -547.36, -119.43, 0, 64, -260.23, 111.47, 0.01687, 3, 7, 109.16, 47.65, 0.98038, 8, -575.73, -104, 0, 64, -229.99, 100.12, 0.01962, 4, 7, 132.36, 24.93, 0.98259, 6, 216.68, -1.49, 0, 8, -595.56, -78.29, 0, 64, -206.79, 77.41, 0.01741, 2, 7, 155.22, -25.13, 0.97672, 64, -183.94, 27.34, 0.02328, 2, 7, 163.2, -20.15, 0.98489, 64, -175.95, 32.33, 0.01511, 3, 7, 148.05, 0.11, 0.98294, 8, -607.66, -51.53, 0, 64, -191.1, 52.59, 0.01706, 3, 7, 68.76, -66.28, 0.93045, 6, 136.44, -78.47, 0.06955, 8, -519.93, 3.23, 0, 2, 7, 98.99, -67.19, 0.97514, 6, 165.9, -85.28, 0.02486, 3, 7, 142.58, -46.6, 0.97672, 6, 212.68, -73.63, 0.00023, 64, -196.57, 5.87, 0.02305, 3, 7, 125.49, -61.31, 0.97615, 6, 193.04, -84.71, 0.00548, 64, -213.66, -8.84, 0.01837, 3, 7, 67.59, 34.02, 0.99492, 6, 154.95, 20.12, 0.00508, 8, -532.68, -96.27, 0, 2, 7, 95.77, 31.41, 1, 8, -560.22, -89.78, 0, 3, 7, 63.48, -89.79, 0.87237, 6, 126.66, -100.48, 0.11648, 64, -275.67, -37.31, 0.01116, 3, 7, 89.09, -85.34, 0.93369, 6, 152.65, -101.14, 0.05233, 64, -250.06, -32.86, 0.01398, 3, 7, 107.88, -77.89, 0.96129, 6, 172.53, -97.52, 0.02324, 64, -231.27, -25.42, 0.01547], "hull": 212, "edges": [2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 42, 44, 50, 52, 60, 62, 66, 68, 92, 94, 94, 96, 96, 98, 102, 104, 104, 106, 118, 120, 126, 128, 128, 130, 130, 132, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 178, 180, 180, 182, 182, 184, 184, 186, 194, 196, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 258, 260, 260, 262, 262, 264, 264, 266, 282, 284, 284, 286, 290, 292, 292, 294, 304, 306, 314, 316, 316, 318, 318, 320, 346, 348, 348, 350, 354, 356, 366, 368, 368, 370, 370, 372, 372, 374, 388, 390, 390, 392, 392, 394, 408, 410, 34, 36, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 20, 22, 24, 22, 40, 42, 36, 38, 38, 40, 44, 46, 56, 58, 62, 64, 64, 66, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 98, 100, 100, 102, 196, 198, 190, 192, 192, 194, 186, 188, 188, 190, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 240, 238, 224, 222, 222, 220, 220, 218, 218, 216, 216, 214, 214, 212, 212, 210, 210, 208, 244, 246, 246, 248, 248, 250, 254, 256, 256, 258, 350, 352, 352, 354, 250, 252, 252, 254, 342, 344, 344, 346, 330, 332, 274, 276, 276, 278, 332, 334, 334, 336, 336, 338, 270, 272, 272, 274, 328, 330, 278, 280, 280, 282, 286, 288, 288, 290, 320, 322, 310, 312, 312, 314, 306, 308, 308, 310, 300, 302, 302, 304, 298, 300, 294, 296, 296, 298, 424, 426, 424, 428, 322, 434, 434, 436, 286, 438, 438, 436, 426, 440, 440, 430, 428, 442, 442, 432, 326, 328, 322, 324, 324, 326, 326, 444, 446, 448, 448, 450, 452, 282, 458, 460, 462, 464, 338, 340, 340, 342, 266, 268, 268, 270, 338, 466, 466, 468, 470, 270, 460, 468, 468, 472, 472, 470, 472, 462, 474, 446, 450, 476, 476, 452, 456, 476, 444, 478, 478, 446, 454, 478, 240, 242, 242, 244, 364, 366, 360, 362, 362, 364, 356, 358, 358, 360, 236, 238, 382, 380, 380, 378, 374, 376, 376, 378, 232, 234, 234, 236, 230, 232, 228, 230, 224, 226, 226, 228, 484, 232, 484, 486, 486, 488, 488, 490, 490, 380, 492, 494, 494, 496, 496, 498, 498, 96, 566, 66, 566, 564, 564, 562, 562, 560, 500, 502, 502, 534, 534, 504, 504, 506, 506, 508, 514, 516, 516, 518, 518, 520, 520, 536, 536, 524, 554, 552, 552, 550, 550, 548, 548, 546, 546, 538, 528, 530, 530, 532, 532, 540, 540, 542, 542, 544, 92, 572, 574, 576, 576, 578, 584, 586, 586, 588, 592, 594, 498, 596, 584, 582, 592, 598, 598, 590, 90, 92, 86, 88, 88, 90, 572, 604, 604, 574, 604, 606, 606, 608, 82, 84, 84, 86, 588, 610, 610, 612, 612, 614, 614, 616, 616, 618, 620, 622, 622, 624, 624, 626, 626, 628, 618, 630, 630, 632, 632, 634, 634, 636, 636, 638, 638, 620, 172, 174, 174, 176, 176, 178, 120, 122, 122, 124, 124, 126, 132, 134, 134, 136, 142, 144, 136, 138, 138, 140, 140, 142, 590, 628, 642, 644, 666, 672, 672, 668, 644, 674, 674, 648, 640, 642, 676, 678, 678, 680, 680, 682, 682, 684, 684, 686, 686, 688, 676, 690, 690, 692, 692, 694, 694, 696, 696, 698, 688, 60, 698, 700, 700, 702, 708, 710, 710, 712, 714, 716, 716, 718, 722, 724, 724, 726, 736, 734, 734, 738, 738, 740, 706, 708, 740, 744, 744, 742, 704, 746, 746, 706, 748, 750, 750, 752, 752, 754, 754, 756, 756, 758, 760, 762, 762, 764, 764, 766, 10, 12, 12, 14, 6, 8, 8, 10, 420, 422, 416, 418, 418, 420, 410, 412, 412, 414, 414, 416, 406, 408, 402, 404, 404, 406, 398, 400, 400, 402, 394, 396, 396, 398, 384, 386, 386, 388, 382, 384, 796, 798, 798, 800, 800, 802, 698, 804, 804, 806, 686, 808, 808, 806, 802, 810, 812, 814, 814, 816, 816, 818, 818, 820, 820, 796, 702, 822, 824, 826, 810, 812, 826, 828, 828, 830, 830, 832, 832, 834, 834, 836, 836, 838, 838, 824, 50, 840, 840, 842, 842, 844, 844, 846, 56, 848, 848, 846, 58, 60, 52, 54, 54, 56, 850, 852, 852, 854, 854, 58, 850, 856, 858, 46, 46, 48, 48, 50, 858, 822, 856, 860, 860, 858, 860, 702, 856, 700, 2, 0, 422, 0], "width": 530, "height": 1708}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.09142, 0.26187, 0.06106, 0.66808, 0.48384, 0.91584, 0.81553, 0.80348, 0.94167, 0.28781, 0.60764, 0.06886], "triangles": [1, 0, 2, 2, 5, 3, 2, 0, 5, 3, 5, 4], "vertices": [2, 7, 59.94, -42.93, 0.97423, 64, -279.21, 9.55, 0.02577, 2, 7, 47.82, -44.61, 0.97428, 64, -291.33, 7.87, 0.02572, 2, 7, 44.14, -61.53, 0.97652, 64, -295.01, -9.05, 0.02348, 2, 7, 50.21, -72.71, 0.98077, 64, -288.94, -20.24, 0.01923, 2, 7, 66.34, -73.74, 0.9827, 64, -272.81, -21.27, 0.0173, 2, 7, 69.93, -60.21, 0.97668, 64, -269.23, -7.74, 0.02332], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 37, "height": 30}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.14016, 0.08198, 0.04845, 0.49701, 0.31496, 0.91509, 0.82747, 0.87221, 0.95587, 0.55979, 0.65592, 0.12486], "triangles": [2, 1, 5, 2, 5, 3, 5, 1, 0, 3, 5, 4], "vertices": [2, 7, 64.78, 16.95, 0.96815, 64, -274.38, 69.43, 0.03185, 2, 7, 51.33, 17.96, 0.96789, 64, -287.82, 70.43, 0.03211, 2, 7, 41.37, 3.59, 0.96743, 64, -297.78, 56.07, 0.03257, 2, 7, 47.79, -18.07, 0.96892, 64, -291.36, 34.41, 0.03108, 2, 7, 58.51, -21.37, 0.96896, 64, -280.64, 31.11, 0.03104, 2, 7, 68.64, -5.45, 0.96779, 64, -270.51, 47.03, 0.03221], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 44, "height": 31}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 37, -5.65, -8.85, 0.97759, 64, -285.6, -12.09, 0.02241, 2, 37, -9.06, 5.76, 0.97506, 64, -289.01, 2.52, 0.02494, 2, 37, 5.54, 9.17, 0.975, 64, -274.4, 5.93, 0.025, 2, 37, 8.95, -5.44, 0.97736, 64, -270.99, -8.68, 0.02264], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 15}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.56941, 0.07307, 0.68049, 0.13122, 0.80511, 0.05015, 0.86182, 0.17842, 0.9808, 0.29253, 0.842, 0.46232, 0.80382, 0.65775, 0.72319, 0.83116, 0.59153, 0.95115, 0.38659, 0.93116, 0.25383, 0.74099, 0.1302, 0.62085, 0.02266, 0.54798, 0.02436, 0.42291, 0.08838, 0.28227, 0.1801, 0.1551, 0.2882, 0.0757, 0.42346, 0.05825, 0.09017, 0.4844, 0.14761, 0.34774, 0.22863, 0.25107, 0.34453, 0.19107, 0.46966, 0.19607, 0.5753, 0.25774, 0.65632, 0.35274, 0.7035, 0.44774, 0.6512, 0.57107, 0.55684, 0.65607, 0.4553, 0.67274, 0.34248, 0.64774, 0.24299, 0.58107, 0.15786, 0.51607], "triangles": [9, 28, 8, 28, 27, 8, 8, 27, 7, 10, 29, 9, 9, 29, 28, 27, 26, 7, 7, 26, 6, 11, 30, 10, 10, 30, 29, 23, 27, 28, 26, 25, 6, 6, 25, 5, 28, 29, 22, 26, 27, 24, 23, 28, 22, 29, 21, 22, 29, 30, 21, 12, 18, 11, 11, 31, 30, 11, 18, 31, 30, 19, 20, 30, 20, 21, 26, 24, 25, 24, 27, 23, 12, 13, 18, 18, 19, 31, 30, 31, 19, 13, 14, 18, 18, 14, 19, 5, 3, 4, 5, 25, 3, 25, 24, 3, 3, 1, 2, 1, 3, 24, 24, 23, 1, 20, 19, 15, 19, 14, 15, 22, 0, 23, 23, 0, 1, 20, 16, 21, 20, 15, 16, 21, 17, 22, 22, 17, 0, 21, 16, 17], "vertices": [2, 7, 66.95, -63.71, 0.97795, 64, -272.2, -11.23, 0.02205, 2, 7, 66.58, -68.24, 0.97937, 64, -272.57, -15.77, 0.02063, 2, 7, 69.58, -72.53, 0.98052, 64, -269.57, -20.06, 0.01948, 2, 7, 67.08, -75.39, 0.98155, 64, -272.07, -22.91, 0.01845, 2, 7, 65.47, -80.53, 0.98322, 64, -273.68, -28.05, 0.01678, 2, 7, 60.27, -76.18, 0.98198, 64, -278.88, -23.71, 0.01802, 2, 7, 55.37, -75.8, 0.98174, 64, -283.79, -23.32, 0.01826, 2, 7, 50.6, -73.68, 0.9808, 64, -288.55, -21.21, 0.0192, 2, 7, 46.63, -69.34, 0.97921, 64, -292.53, -16.86, 0.02079, 2, 7, 45.28, -61.44, 0.97672, 64, -293.88, -8.97, 0.02328, 2, 7, 48.54, -55.36, 0.9758, 64, -290.61, -2.89, 0.0242, 2, 7, 50.26, -50.01, 0.97482, 64, -288.9, 2.46, 0.02518, 2, 7, 51.01, -45.53, 0.97413, 64, -288.15, 6.95, 0.02587, 2, 7, 53.94, -44.91, 0.97413, 64, -285.21, 7.56, 0.02587, 2, 7, 57.8, -46.58, 0.9744, 64, -281.35, 5.9, 0.0256, 2, 7, 61.58, -49.37, 0.97504, 64, -277.57, 3.11, 0.02496, 2, 7, 64.4, -53.04, 0.97564, 64, -274.75, -0.56, 0.02436, 2, 7, 66.01, -58.08, 0.97623, 64, -273.15, -5.61, 0.02377, 2, 7, 53.09, -47.75, 0.97446, 64, -286.06, 4.73, 0.02554, 2, 7, 56.79, -49.18, 0.97486, 64, -282.36, 3.29, 0.02514, 2, 7, 59.77, -51.73, 0.97544, 64, -279.38, 0.74, 0.02456, 2, 7, 62.2, -55.81, 0.97594, 64, -276.95, -3.33, 0.02406, 2, 7, 63.19, -60.59, 0.97708, 64, -275.96, -8.11, 0.02292, 2, 7, 62.69, -64.94, 0.97845, 64, -276.46, -12.46, 0.02155, 2, 7, 61.19, -68.53, 0.97959, 64, -277.97, -16.06, 0.02041, 2, 7, 59.38, -70.84, 0.98031, 64, -279.77, -18.37, 0.01969, 2, 7, 56.04, -69.53, 0.97982, 64, -283.11, -17.05, 0.02018, 2, 7, 53.22, -66.41, 0.97873, 64, -285.94, -13.93, 0.02127, 2, 7, 51.93, -62.64, 0.9775, 64, -287.23, -10.17, 0.0225, 2, 7, 51.51, -58.22, 0.97621, 64, -287.64, -5.75, 0.02379, 2, 7, 52.19, -54.08, 0.97568, 64, -286.97, -1.6, 0.02432, 2, 7, 52.95, -50.49, 0.97499, 64, -286.2, 1.98, 0.02501], "hull": 18, "edges": [4, 6, 6, 8, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 22, 24, 18, 20, 20, 22, 12, 14, 8, 10, 10, 12, 30, 32, 32, 34, 4, 2, 2, 0, 0, 34, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 36], "width": 39, "height": 24}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 38, -5.69, -9.2, 0.96756, 64, -285.67, 45.4, 0.03244, 2, 38, -9.1, 5.41, 0.96582, 64, -289.08, 60.01, 0.03418, 2, 38, 5.5, 8.82, 0.96612, 64, -274.47, 63.41, 0.03388, 2, 38, 8.91, -5.79, 0.96706, 64, -271.06, 48.81, 0.03294], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 15}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.36221, 0.16938, 0.52902, 0.15995, 0.68119, 0.24674, 0.78444, 0.3671, 0.87517, 0.52009, 0.93601, 0.67551, 0.97871, 0.75742, 0.96142, 0.8499, 0.88216, 0.8537, 0.79332, 0.87944, 0.6885, 0.9287, 0.55041, 0.98018, 0.34336, 0.95908, 0.17703, 0.824, 0.07987, 0.60125, 0.05517, 0.40496, 0.11785, 0.36772, 0.01186, 0.14906, 0.11617, 0.06051, 0.25603, 0.04914, 0.88819, 0.72204, 0.81901, 0.57665, 0.7352, 0.43553, 0.62743, 0.33504, 0.4904, 0.30083, 0.36801, 0.31152, 0.26291, 0.40131, 0.31878, 0.55954, 0.4026, 0.67714, 0.52898, 0.75625, 0.66735, 0.76694, 0.79506, 0.7477], "triangles": [24, 0, 1, 25, 0, 24, 23, 1, 2, 24, 1, 23, 16, 18, 19, 19, 26, 16, 0, 26, 19, 17, 18, 16, 0, 25, 26, 22, 2, 3, 23, 2, 22, 27, 26, 25, 21, 3, 4, 22, 3, 21, 14, 15, 16, 28, 25, 24, 27, 25, 28, 20, 4, 5, 21, 4, 20, 21, 30, 22, 31, 21, 20, 29, 24, 23, 30, 29, 23, 22, 30, 23, 28, 24, 29, 21, 31, 30, 26, 14, 16, 27, 14, 26, 13, 27, 28, 27, 13, 14, 6, 20, 5, 7, 20, 6, 8, 31, 20, 8, 20, 7, 9, 30, 31, 9, 31, 8, 10, 30, 9, 12, 13, 28, 12, 28, 29, 11, 29, 30, 11, 30, 10, 12, 29, 11], "vertices": [2, 7, 64.03, 10.48, 0.96649, 64, -275.13, 62.95, 0.03351, 2, 7, 65.99, 3.23, 0.96659, 64, -273.16, 55.7, 0.03341, 2, 7, 65.18, -3.99, 0.96698, 64, -273.97, 48.48, 0.03302, 2, 7, 62.95, -9.28, 0.96766, 64, -276.2, 43.19, 0.03234, 2, 7, 59.71, -14.23, 0.9683, 64, -279.44, 38.24, 0.0317, 2, 7, 56.09, -17.89, 0.96876, 64, -283.06, 34.59, 0.03124, 2, 7, 54.3, -20.28, 0.96906, 64, -284.86, 32.2, 0.03094, 2, 7, 51.6, -20.11, 0.96903, 64, -287.55, 32.36, 0.03097, 2, 7, 50.68, -16.66, 0.9686, 64, -288.47, 35.81, 0.0314, 2, 7, 49.07, -12.93, 0.96814, 64, -290.08, 39.54, 0.03186, 2, 7, 46.66, -8.65, 0.96759, 64, -292.49, 43.82, 0.03241, 2, 7, 43.84, -2.93, 0.96685, 64, -295.31, 49.55, 0.03315, 2, 7, 42.3, 6.28, 0.96655, 64, -296.85, 58.75, 0.03345, 2, 7, 44.28, 14.43, 0.96666, 64, -294.87, 66.9, 0.03334, 2, 7, 49.36, 20.1, 0.96727, 64, -289.79, 72.58, 0.03273, 2, 7, 54.46, 22.43, 0.96751, 64, -284.69, 74.91, 0.03249, 2, 7, 56.12, 19.92, 0.96725, 64, -283.03, 72.4, 0.03275, 2, 7, 61, 25.96, 0.96782, 64, -278.16, 78.44, 0.03218, 2, 7, 64.48, 21.95, 0.96737, 64, -274.68, 74.43, 0.03263, 2, 7, 66.22, 15.9, 0.96674, 64, -272.93, 68.37, 0.03326, 2, 7, 54.34, -16.09, 0.96854, 64, -284.82, 36.39, 0.03146, 2, 7, 57.59, -12.13, 0.96805, 64, -281.56, 40.34, 0.03195, 2, 7, 60.58, -7.56, 0.96747, 64, -278.57, 44.92, 0.03253, 2, 7, 62.22, -2.2, 0.96681, 64, -276.93, 50.28, 0.03319, 2, 7, 61.75, 4.02, 0.96667, 64, -277.4, 56.5, 0.03333, 2, 7, 60.21, 9.32, 0.96658, 64, -278.94, 61.8, 0.03342, 2, 7, 56.69, 13.35, 0.96662, 64, -282.47, 65.83, 0.03338, 2, 7, 52.94, 9.9, 0.96662, 64, -286.21, 62.37, 0.03338, 2, 7, 50.59, 5.48, 0.96671, 64, -288.56, 57.95, 0.03329, 2, 7, 49.73, -0.56, 0.96683, 64, -289.42, 51.91, 0.03317, 2, 7, 50.85, -6.7, 0.96738, 64, -288.3, 45.78, 0.03262, 2, 7, 52.68, -12.17, 0.96806, 64, -286.47, 40.31, 0.03194], "hull": 20, "edges": [8, 10, 10, 12, 12, 14, 22, 24, 34, 36, 36, 38, 0, 38, 0, 2, 2, 4, 32, 34, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 40, 4, 6, 6, 8, 16, 18, 18, 20], "width": 45, "height": 28}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.3169, 0.00087, 0.35462, 0.02528, 0.36819, 0.0542, 0.36874, 0.07237, 0.407, 0.08223, 0.43114, 0.10394, 0.47623, 0.11347, 0.54171, 0.19301, 0.55318, 0.27252, 0.54646, 0.36637, 0.54442, 0.39475, 0.55491, 0.41255, 0.57199, 0.43074, 0.59292, 0.40553, 0.71578, 0.54516, 0.89458, 0.70339, 0.9464, 0.76853, 0.97719, 0.82601, 0.99907, 0.87862, 0.99824, 0.92785, 0.97891, 0.96674, 0.94186, 0.9973, 0.91798, 0.97118, 0.86698, 0.94867, 0.83456, 0.94547, 0.79543, 0.91011, 0.74957, 0.84848, 0.66536, 0.70674, 0.52346, 0.5398, 0.52918, 0.51912, 0.50909, 0.49419, 0.49349, 0.47261, 0.4797, 0.45388, 0.46591, 0.43168, 0.45876, 0.40544, 0.42998, 0.44518, 0.44207, 0.4004, 0.44939, 0.36198, 0.41731, 0.34196, 0.3518, 0.35892, 0.28621, 0.31212, 0.2343, 0.30556, 0.21751, 0.27947, 0.2079, 0.33756, 0.20678, 0.3822, 0.21586, 0.43154, 0.23051, 0.47131, 0.21445, 0.52576, 0.21209, 0.58211, 0.21431, 0.63738, 0.21817, 0.69045, 0.21575, 0.7246, 0.20858, 0.72679, 0.20344, 0.68965, 0.19172, 0.6503, 0.1747, 0.60994, 0.14174, 0.55039, 0.11511, 0.51386, 0.08642, 0.45576, 0.07158, 0.39209, 0.07251, 0.3111, 0.09805, 0.23284, 0.06408, 0.25607, 0.03205, 0.26048, 1e-05, 0.24983, 0.03577, 0.24232, 0.06785, 0.22318, 0.0909, 0.18884, 0.12386, 0.12131, 0.16051, 0.06651, 0.20427, 0.02372, 0.26007, 0.00061, 0.23101, 0.22874, 0.25498, 0.17216, 0.28274, 0.1284, 0.34595, 0.07698, 0.95762, 0.91799, 0.93444, 0.86344, 0.54084, 0.49963, 0.69928, 0.68002, 0.56086, 0.4567, 0.72827, 0.61758, 0.54627, 0.44297, 0.53134, 0.42747, 0.52107, 0.40485, 0.4937, 0.42831, 0.50508, 0.45287, 0.52069, 0.47732, 0.80242, 0.84872, 0.84481, 0.8937, 0.86601, 0.74492, 0.91033, 0.80633, 0.88849, 0.90927, 0.47227, 0.36252, 0.47941, 0.3121, 0.47853, 0.25867, 0.47117, 0.22416, 0.4588, 0.20988, 0.44702, 0.17022, 0.46498, 0.18371, 0.45626, 0.15304, 0.5154, 0.37532, 0.52919, 0.27348, 0.49685, 0.19385, 0.36151, 0.12141, 0.33876, 0.16645, 0.37228, 0.09419, 0.31703, 0.09619, 0.31255, 0.21241, 0.27521, 0.25635, 0.37786, 0.30524, 0.39531, 0.24813, 0.40316, 0.19168, 0.39837, 0.13916, 0.42653, 0.31525, 0.3171, 0.27974, 0.35158, 0.23006, 0.37478, 0.18152, 0.43528, 0.25608, 0.43776, 0.19753, 0.43181, 0.14439, 0.40712, 0.10627, 0.33543, 0.04451, 0.29937, 0.04921, 0.24935, 0.06723, 0.20864, 0.11109, 0.18188, 0.16748, 0.15571, 0.23014, 0.14408, 0.2881, 0.1435, 0.34449, 0.1464, 0.4095, 0.16502, 0.47529, 0.19061, 0.53482], "triangles": [123, 0, 122, 123, 71, 0, 124, 71, 123, 125, 124, 74, 69, 70, 125, 67, 68, 127, 127, 68, 126, 68, 69, 126, 126, 69, 125, 125, 70, 124, 124, 70, 71, 74, 124, 123, 24, 89, 23, 23, 92, 22, 22, 92, 76, 21, 22, 76, 20, 21, 76, 23, 89, 92, 24, 25, 89, 25, 88, 89, 25, 26, 88, 88, 26, 79, 89, 88, 90, 83, 10, 11, 82, 83, 11, 82, 11, 12, 86, 85, 83, 80, 82, 12, 31, 32, 86, 87, 86, 83, 87, 83, 82, 31, 86, 87, 30, 31, 87, 78, 87, 82, 78, 82, 80, 78, 30, 87, 29, 30, 78, 15, 81, 14, 80, 12, 13, 13, 14, 80, 81, 80, 14, 81, 78, 80, 79, 78, 81, 28, 29, 78, 78, 27, 28, 79, 27, 78, 90, 81, 15, 79, 81, 90, 91, 90, 15, 16, 91, 15, 91, 16, 17, 27, 79, 26, 90, 88, 79, 77, 91, 17, 77, 17, 18, 91, 89, 90, 77, 92, 91, 76, 77, 18, 92, 77, 76, 19, 76, 18, 20, 76, 19, 92, 89, 91, 112, 113, 120, 121, 5, 120, 103, 99, 100, 7, 103, 6, 97, 119, 98, 99, 97, 98, 96, 99, 103, 97, 99, 96, 118, 119, 97, 118, 97, 96, 118, 111, 119, 95, 96, 103, 118, 96, 95, 102, 103, 7, 102, 7, 8, 95, 103, 102, 94, 95, 102, 114, 111, 118, 38, 110, 114, 118, 94, 114, 94, 118, 95, 37, 114, 94, 38, 114, 37, 93, 37, 94, 9, 102, 8, 101, 94, 102, 101, 93, 94, 102, 9, 101, 10, 101, 9, 84, 101, 10, 34, 37, 93, 36, 37, 34, 83, 84, 10, 85, 93, 101, 85, 101, 84, 85, 84, 83, 34, 93, 85, 33, 34, 85, 35, 36, 34, 32, 33, 85, 32, 85, 86, 119, 111, 112, 98, 119, 120, 6, 103, 100, 119, 112, 120, 6, 100, 5, 100, 120, 5, 98, 120, 100, 98, 100, 99, 109, 108, 115, 115, 116, 110, 40, 109, 115, 41, 109, 40, 39, 115, 110, 39, 110, 38, 40, 115, 39, 111, 117, 112, 108, 74, 105, 73, 74, 108, 116, 105, 117, 108, 105, 116, 116, 117, 111, 109, 73, 108, 115, 108, 116, 110, 116, 111, 110, 111, 114, 53, 49, 50, 51, 53, 50, 52, 53, 51, 55, 48, 49, 54, 55, 49, 53, 54, 49, 47, 131, 46, 132, 131, 47, 56, 131, 132, 48, 132, 47, 55, 56, 132, 55, 132, 48, 131, 130, 45, 131, 45, 46, 58, 130, 131, 57, 58, 131, 56, 57, 131, 129, 60, 128, 129, 128, 43, 44, 129, 43, 59, 60, 129, 130, 129, 44, 59, 129, 130, 130, 44, 45, 58, 59, 130, 62, 66, 61, 62, 65, 66, 63, 65, 62, 60, 61, 128, 63, 64, 65, 127, 126, 72, 42, 127, 72, 42, 72, 109, 128, 61, 127, 128, 127, 42, 41, 42, 109, 43, 128, 42, 61, 67, 127, 66, 67, 61, 72, 126, 73, 72, 73, 109, 126, 125, 73, 73, 125, 74, 74, 123, 107, 75, 122, 2, 75, 2, 3, 106, 3, 4, 106, 75, 3, 107, 75, 104, 122, 1, 2, 107, 123, 122, 107, 122, 75, 74, 107, 105, 106, 104, 75, 106, 4, 121, 113, 106, 121, 104, 106, 113, 105, 107, 104, 117, 104, 113, 105, 104, 117, 113, 121, 120, 121, 4, 5, 122, 0, 1, 117, 113, 112], "vertices": [4, 40, 22.24, 26.99, 0.308, 50, -162.41, 116.32, 0, 7, 172.01, 3.01, 0.66285, 65, -118.94, 55.48, 0.02915, 3, 40, 18.26, 9.05, 0.60199, 7, 168.02, -14.93, 0.36285, 65, -122.93, 37.55, 0.03516, 3, 40, 10.43, 1.11, 0.82721, 7, 160.2, -22.87, 0.14375, 65, -130.75, 29.6, 0.02904, 3, 40, 4.72, -0.47, 0.92952, 7, 154.48, -24.45, 0.04278, 65, -136.47, 28.02, 0.0277, 3, 40, 5.4, -17.56, 0.84692, 39, -47.01, -29.69, 0.12584, 65, -135.78, 10.94, 0.02724, 3, 44, -39.66, 32.24, 0.00046, 50, -114.1, 152.52, 0, 39, -38.28, -20.4, 0.99954, 3, 44, -57.45, 41.45, 0.00012, 50, -104.61, 170.17, 0, 39, -31.97, -1.38, 0.99988, 2, 44, -76.52, 75.14, 0, 39, -1.67, 22.72, 1, 1, 39, 24.72, 23.43, 1, 2, 39, 54.42, 15.49, 0.97987, 20, 250.55, -23.59, 0.02013, 2, 39, 63.4, 13.09, 0.74056, 20, 245.2, -15.97, 0.25944, 2, 39, 69.88, 16.68, 0.30062, 20, 237.89, -14.54, 0.69938, 3, 50, 6.91, 175.6, 0, 39, 76.96, 23.11, 0.01761, 20, 228.28, -14.9, 0.98239, 2, 50, 2.19, 186.99, 0, 20, 226.61, -27.11, 1, 1, 20, 155.63, -27.47, 1, 1, 20, 61.9, -39.15, 1, 1, 20, 30.63, -37.76, 1, 1, 20, 8.06, -32.27, 1, 2, 20, -10.49, -25.45, 0.92104, 19, 198.09, 17.7, 0.07896, 2, 20, -20.68, -13, 0.71539, 19, 201.29, 1.97, 0.28461, 2, 20, -22.46, 2.15, 0.45464, 19, 195.82, -12.27, 0.54536, 2, 20, -16.51, 20.28, 0.16608, 19, 182.16, -25.59, 0.83392, 2, 20, -2.93, 20.61, 0.04278, 19, 170.05, -19.61, 0.95722, 2, 20, 18.99, 29.55, 0.17373, 19, 146.6, -17.41, 0.82627, 2, 20, 30.56, 37.99, 0.37307, 19, 132.49, -19.54, 0.62693, 2, 20, 51.23, 40.37, 0.98434, 19, 113.18, -12.11, 0.01566, 1, 20, 79.75, 38.14, 1, 1, 20, 138.19, 26.97, 1, 2, 42, 80.97, 109.44, 0, 20, 221.38, 25.98, 1, 2, 42, 73.78, 109.19, 0, 20, 223.86, 19.22, 1, 3, 42, 69.63, 97.94, 1e-05, 39, 92.83, -7.53, 0.05144, 20, 235.92, 18.76, 0.94855, 5, 45, 14.48, 166.49, 0, 46, 27.78, 165.84, 0, 47, 43.37, 161.45, 0, 39, 84.76, -13.13, 0.32041, 20, 245.75, 17.85, 0.67959, 6, 45, 14.38, 157.89, 0, 46, 25.09, 157.67, 0, 47, 37.79, 154.91, 0, 42, 62.41, 81, 0.00079, 39, 77.75, -18.1, 0.68175, 20, 254.36, 17.13, 0.31745, 6, 45, 13.47, 148.5, 0, 46, 21.39, 149, 1e-05, 47, 31.07, 148.29, 0, 42, 58.03, 72.64, 0.00519, 39, 69.62, -22.88, 0.95353, 20, 263.72, 15.56, 0.04127, 5, 45, 9.58, 140.26, 1e-05, 46, 15.19, 142.31, 1e-05, 47, 22.8, 144.45, 0, 42, 51.32, 66.48, 0.01761, 39, 60.66, -24.57, 0.98238, 5, 45, 27.67, 140.35, 1e-05, 46, 32.47, 136.94, 1e-05, 47, 36.75, 132.93, 0, 42, 68.11, 59.74, 0.01318, 39, 71.37, -39.16, 0.9868, 6, 45, 13.55, 133.89, 1e-05, 46, 17.05, 135.04, 1e-05, 47, 21.77, 137.02, 0, 42, 52.59, 59.08, 0.0203, 43, 17.73, 61.49, 0.02378, 39, 57.84, -31.53, 0.95589, 6, 45, 2.37, 127.39, 2e-05, 46, 4.43, 132.22, 4e-05, 47, 9.02, 139.19, 0, 42, 39.79, 57.27, 0.06823, 43, 5.21, 58.24, 0.07639, 39, 46.01, -26.3, 0.85532, 7, 45, 7.6, 112.78, 5e-05, 46, 5.01, 116.71, 0.00012, 47, 3.68, 124.62, 0, 41, 71.99, 32.05, 0.00054, 42, 39.13, 41.76, 0.19797, 43, 6.32, 42.76, 0.29244, 39, 37.26, -39.13, 0.50887, 7, 44, 19.67, 100.09, 0, 45, 31.72, 96.17, 0.00021, 46, 22.99, 93.59, 0.00048, 47, 11.55, 96.41, 0, 42, 55.21, 17.29, 0.06729, 43, 25.08, 20.27, 0.84896, 39, 37.99, -68.4, 0.08307, 6, 45, 41.06, 64.95, 0.04395, 46, 22.47, 61.01, 0.1039, 47, -1.29, 66.46, 0.00035, 42, 52.09, -15.15, 0.02495, 43, 25.67, -12.31, 0.82669, 39, 18.22, -94.3, 0.00016, 6, 45, 55.52, 47.21, 0.09685, 46, 30.91, 39.73, 0.41333, 47, -1.55, 43.57, 0.02519, 42, 58.81, -37.03, 0.05076, 43, 34.83, -33.29, 0.41372, 39, 12.36, -116.43, 0.00015, 10, 45, 54.63, 35.99, 0.08093, 46, 26.67, 29.31, 0.59511, 47, -9.42, 35.53, 0.08463, 48, -31.45, 52.09, 0.00106, 49, -45.71, 67.42, 8e-05, 50, -91.15, 45.08, 0, 51, -123.86, 11.74, 0, 42, 53.76, -47.09, 0.03571, 43, 30.95, -43.85, 0.20241, 39, 2.76, -122.3, 8e-05, 10, 45, 71.09, 46.27, 0.00384, 46, 45.47, 34.14, 0.37733, 47, 9.8, 32.88, 0.5167, 48, -14.82, 42.1, 0.05223, 49, -32.5, 53.21, 0.01288, 50, -74.68, 34.83, 0, 51, -105.22, 6.37, 0, 42, 72.88, -43.76, 0.00213, 43, 49.57, -38.38, 0.03487, 39, 20.75, -129.57, 2e-05, 8, 46, 58.66, 40.31, 0.12817, 47, 24.34, 33.59, 0.575, 48, -1.17, 37.03, 0.21614, 49, -20.79, 44.55, 0.07253, 50, -61.11, 29.55, 0, 51, -90.72, 4.99, 1e-05, 43, 62.54, -31.77, 0.00815, 39, 35.03, -132.45, 1e-05, 8, 46, 71.18, 51.16, 0.02234, 47, 40.05, 38.88, 0.27905, 48, 15.35, 35.72, 0.41152, 49, -5.29, 38.71, 0.28631, 50, -44.61, 27.99, 0, 51, -74.43, 7.98, 2e-05, 43, 74.68, -20.49, 0.00076, 39, 51.55, -131.16, 0, 8, 46, 79.81, 62.78, 0.00297, 47, 52.44, 46.36, 0.11995, 48, 29.68, 37.72, 0.34257, 49, 9.04, 36.66, 0.53446, 50, -30.25, 29.76, 0, 51, -61.09, 13.61, 3e-05, 43, 82.91, -8.59, 1e-05, 39, 65.39, -126.94, 0, 6, 47, 70.71, 40.79, 0.02314, 48, 44.29, 25.42, 0.1058, 49, 19.66, 20.78, 0.87014, 50, -15.83, 17.23, 0.00084, 51, -43.8, 5.49, 7e-05, 39, 81.74, -136.81, 0, 4, 47, 89.1, 41.27, 6e-05, 49, 34.2, 9.51, 0.4516, 50, 1.16, 10.17, 0.54795, 51, -25.53, 3.33, 0.00039, 2, 47, 106.98, 43.72, 0, 50, 18.48, 5.12, 1, 2, 49, 64.81, -8.32, 0, 51, 9.88, 3.84, 1, 2, 51, 20.93, 2.1, 1, 39, 145.78, -146.89, 0, 1, 51, 21.45, -1.09, 1, 1, 51, 9.23, -2.6, 1, 3, 50, 19.17, -5.63, 0.94709, 51, -3.89, -6.95, 0.05291, 39, 120.16, -153.32, 0, 3, 49, 32.37, -9.15, 0.26534, 50, 4.28, -8.32, 0.73466, 39, 105.95, -158.53, 0, 3, 48, 41.82, -7.4, 0.07122, 49, 8.17, -10.06, 0.92878, 39, 84.42, -169.62, 0, 2, 48, 26.82, -14.72, 0.84785, 49, -8.26, -12.94, 0.15215, 2, 47, 52.59, -17.1, 0.28929, 48, 4.87, -20.68, 0.71071, 3, 47, 32.44, -25.3, 0.89857, 48, -16.88, -20.3, 0.10143, 39, 28.46, -191.52, 0, 4, 46, 64.76, -22.73, 0.14882, 47, 6.09, -27.06, 0.64851, 7, 49.14, 84.5, 0.19851, 65, -241.82, 136.97, 0.00417, 7, 46, 36.93, -24.32, 0.57968, 47, -20.26, -17.99, 0.03177, 42, 59.71, -101.36, 1e-05, 43, 43.03, -97.1, 5e-05, 39, -20.84, -171.54, 0, 7, 76.53, 79.38, 0.38187, 65, -214.42, 131.86, 0.00662, 7, 46, 50.45, -34.18, 0.75232, 47, -11.49, -32.23, 6e-05, 42, 72.41, -112.26, 2e-05, 43, 56.88, -106.49, 0.0001, 39, -15.82, -187.49, 0, 7, 65.76, 92.18, 0.23777, 65, -225.19, 144.66, 0.00973, 8, 45, 107.35, -26.36, 0, 46, 58.12, -46.05, 0.92539, 47, -8.9, -46.12, 4e-05, 42, 79.1, -124.71, 3e-05, 43, 64.95, -118.09, 0.00013, 39, -16.71, -201.6, 0, 7, 61.17, 105.55, 0.06052, 65, -229.78, 158.02, 0.01389, 6, 46, 61.41, -60.16, 0.98141, 47, -11.2, -60.42, 9e-05, 42, 81.26, -139.04, 3e-05, 43, 68.72, -132.08, 0.00014, 39, -22.44, -214.9, 0, 65, -229.6, 172.51, 0.01833, 8, 45, 101.99, -29.35, 0, 46, 52.1, -47.28, 0.81649, 47, -14.93, -44.98, 3e-05, 42, 73, -125.46, 2e-05, 43, 58.97, -119.53, 0.00011, 39, -22.28, -199.01, 0, 7, 67.31, 105.3, 0.16859, 65, -223.64, 157.78, 0.01476, 8, 45, 87.66, -23.7, 0.00016, 46, 40.15, -37.57, 0.56615, 47, -22.31, -31.46, 0.00216, 42, 61.86, -114.82, 1e-05, 43, 46.7, -110.23, 7e-05, 39, -26.12, -184.1, 0, 7, 76.58, 93.01, 0.42036, 65, -214.37, 145.48, 0.01109, 7, 45, 72.58, -24.34, 0.00912, 46, 25.58, -33.63, 0.34371, 42, 47.65, -109.74, 1e-05, 43, 32, -106.79, 6e-05, 39, -35.51, -172.28, 0, 7, 89.79, 85.7, 0.63713, 65, -201.17, 138.17, 0.00997, 8, 45, 46.75, -29.48, 0.05519, 46, -0.6, -30.73, 0.07364, 47, -57.43, -9.68, 0, 42, 21.78, -104.76, 0, 43, 5.74, -104.79, 1e-05, 39, -54.85, -154.39, 0, 7, 114.51, 76.61, 0.86123, 65, -176.44, 129.09, 0.00993, 2, 7, 135.57, 65, 0.99025, 65, -155.39, 117.48, 0.00975, 2, 7, 153.52, 49.46, 0.98901, 65, -137.43, 101.94, 0.01099, 3, 40, 16.66, 51.3, 0.13928, 7, 166.42, 27.32, 0.84205, 65, -124.53, 79.8, 0.01867, 7, 44, 56.56, 43.05, 0.00248, 45, 38.69, 28.61, 0.25967, 46, 9.25, 27.08, 0.45504, 47, -26.39, 40.07, 0.00679, 42, 36.21, -47.92, 0.09628, 43, 13.61, -46.68, 0.17967, 39, -12.58, -113.74, 6e-05, 8, 40, -38.32, 40.77, 0.00372, 44, 40.74, 28.89, 0.06863, 45, 18.17, 23.16, 0.56588, 46, -11.96, 28.08, 0.10163, 41, 29.33, -47.46, 0.01538, 42, 15.15, -45.23, 0.17106, 43, -7.62, -46.4, 0.07367, 39, -29.04, -100.33, 2e-05, 11, 40, -21.65, 32.14, 0.25533, 44, 24.67, 19.21, 0.28692, 45, -0.55, 21.84, 0.20248, 46, -30.21, 32.47, 0.00627, 41, 13.22, -37.83, 0.04421, 42, -2.69, -39.4, 0.09661, 43, -26.01, -42.64, 0.00787, 39, -41.11, -85.97, 0, 7, 128.11, 8.16, 0.09099, 64, -211.04, 60.64, 0.00384, 65, -162.84, 60.64, 0.00547, 4, 40, 0.98, 8.93, 0.8564, 7, 150.74, -15.05, 0.1158, 64, -188.41, 37.43, 0.00486, 65, -140.21, 37.43, 0.02294, 2, 20, -4.94, -3.88, 0.63403, 19, 183.19, 1.15, 0.36597, 1, 20, 14.46, -10.81, 1, 2, 42, 65.96, 111.5, 0, 20, 224.09, 11.07, 1, 1, 20, 132.49, 10.68, 1, 3, 50, 13.27, 168.18, 0, 39, 84.51, 16.89, 0.00265, 20, 226.5, -5.28, 0.99735, 1, 20, 136.03, -13.06, 1, 3, 50, 6.93, 163.62, 0, 39, 79.04, 11.31, 0.03396, 20, 234.32, -4.54, 0.96604, 3, 50, -0.01, 159.11, 0, 39, 72.98, 5.68, 0.31016, 20, 242.63, -4.13, 0.68984, 2, 39, 64.96, 2.44, 0.9652, 20, 250.9, -6.81, 0.0348, 6, 45, 4.13, 156.42, 0, 46, 14.87, 159.37, 0, 47, 28.97, 160.35, 0, 42, 52.36, 83.5, 0.00136, 39, 70.53, -10.67, 0.80495, 20, 255.1, 6.8, 0.19369, 6, 45, 6.33, 165.6, 0, 46, 19.74, 167.45, 0, 47, 36.54, 165.99, 0, 42, 57.85, 91.17, 4e-05, 39, 79.25, -7.06, 0.35146, 20, 246.06, 9.65, 0.64851, 2, 39, 88.24, -1.6, 0.03751, 20, 235.61, 11.27, 0.96249, 1, 20, 61.94, 23.15, 1, 2, 20, 38.13, 22.23, 0.93016, 19, 133.14, -2.09, 0.06984, 1, 20, 62.66, -20.71, 1, 1, 20, 34.71, -18.1, 1, 2, 20, 20.14, 13.65, 0.36837, 19, 152.96, -2.79, 0.63163, 4, 45, -4.55, 134.67, 1e-05, 46, 0.03, 141.25, 2e-05, 47, 8.37, 149.22, 0, 39, 47.84, -16.42, 0.99997, 4, 45, -18.46, 125.37, 1e-05, 46, -16.04, 136.58, 2e-05, 47, -8.27, 150.99, 0, 39, 32.14, -10.63, 0.99998, 5, 44, -43.45, 86.67, 0, 45, -30.6, 112.87, 0, 46, -31.38, 128.33, 1e-05, 47, -25.6, 149.17, 0, 39, 14.89, -8.16, 0.99999, 6, 44, -43.94, 74.98, 1e-05, 45, -36.35, 102.68, 0, 46, -39.94, 120.34, 0, 47, -36.54, 145.03, 0, 50, -71.3, 156.13, 0, 39, 3.26, -9.5, 0.99999, 6, 44, -40.26, 68.85, 2e-05, 45, -35.86, 95.54, 0, 46, -41.62, 113.39, 1e-05, 47, -40.73, 139.23, 0, 50, -77.49, 152.54, 0, 39, -2.22, -14.09, 0.99997, 6, 44, -39.44, 54.95, 9e-05, 45, -41.44, 82.78, 0, 46, -50.79, 102.91, 0, 47, -53.2, 133.02, 0, 50, -91.4, 151.95, 0, 39, -15.82, -17.07, 0.99991, 6, 44, -45.53, 61.61, 4e-05, 45, -43.84, 91.49, 0, 46, -50.46, 111.94, 0, 47, -49.46, 141.24, 0, 50, -84.64, 157.93, 0, 39, -10.19, -10.01, 0.99996, 6, 44, -45.06, 50.91, 0.00011, 45, -48.28, 81.74, 0, 46, -57.63, 103.99, 0, 47, -59.11, 136.6, 0, 50, -95.34, 157.63, 0, 39, -20.68, -12.15, 0.99989, 1, 39, 55.06, 1.56, 1, 1, 39, 23.3, 12.99, 1, 3, 44, -57.75, 69.17, 1e-05, 50, -76.89, 170.04, 0, 39, -4.64, 3.24, 0.99999, 6, 40, -11.57, -1.02, 0.81942, 44, -8.86, 27.98, 0.00457, 45, -26.44, 44.87, 0.00061, 41, 5.2, -4.12, 0.15491, 42, -17.99, -8.31, 0.0008, 65, -152.76, 27.48, 0.01969, 7, 40, -28.14, 5.37, 0.42179, 44, 5.26, 38.75, 0.03889, 45, -8.97, 48.06, 0.02574, 46, -30.32, 60.01, 0.00203, 41, 21.35, -11.51, 0.24898, 42, -0.6, -11.94, 0.25194, 65, -169.33, 33.87, 0.01062, 2, 40, -1.86, -3.6, 0.97521, 65, -143.04, 24.89, 0.02479, 9, 40, -8.01, 19.87, 0.69294, 44, 7.07, 14.01, 0.11375, 45, -18.59, 25.19, 0.00593, 41, 0.36, -24.74, 0.01965, 42, -18.14, -29.5, 0.00886, 39, -49, -69.4, 0, 7, 141.76, -4.11, 0.13655, 64, -197.39, 48.36, 0.00691, 65, -149.19, 48.36, 0.01542, 9, 40, -45.35, 13.17, 0.02933, 44, 20.92, 49.33, 0.03828, 45, 9.78, 50.38, 0.10658, 46, -11.74, 56.56, 0.03489, 41, 38.04, -20.35, 0.03196, 42, 17.64, -16.86, 0.65001, 43, -8.37, -17.93, 0.10806, 39, -11.95, -77.56, 1e-05, 65, -186.53, 41.67, 0.00088, 7, 44, 40.99, 57.73, 0.00673, 45, 31.49, 48.75, 0.14187, 46, 8.46, 48.46, 0.18333, 47, -19.01, 60.15, 0.00058, 42, 37.13, -26.55, 0.18943, 43, 12.1, -25.34, 0.47795, 39, -0.51, -96.08, 0.0001, 7, 45, 11.22, 92.04, 6e-05, 46, 2.21, 95.84, 0.00014, 47, -6.83, 106.38, 0, 41, 63.08, 12.97, 0.00067, 42, 34.67, 21.19, 0.28026, 43, 4.23, 21.81, 0.53586, 39, 22.61, -54.24, 0.18301, 8, 44, -9.87, 71.86, 0, 45, -7.42, 84.43, 1e-05, 46, -17.86, 94.22, 2e-05, 47, -26.02, 112.48, 0, 41, 43.45, 17.42, 0.08352, 42, 14.54, 21.17, 0.55312, 43, -15.77, 19.5, 0.12154, 39, 5.5, -43.63, 0.24179, 11, 40, -29.73, -24.03, 0.20079, 44, -18.96, 55.49, 1e-05, 45, -22.95, 73.98, 0, 46, -35.82, 88.94, 0, 47, -44.64, 114.4, 0, 50, -91.17, 131.46, 0, 41, 24.73, 17.74, 0.35978, 42, -3.79, 17.33, 0.24916, 43, -33.54, 13.61, 0.00486, 39, -12.08, -37.21, 0.17545, 65, -170.91, 4.47, 0.00994, 8, 40, -13.53, -18.09, 0.56241, 44, -22.38, 38.59, 2e-05, 47, -61.53, 110.9, 0, 50, -108.02, 135.15, 0, 41, 8.2, 12.8, 0.33164, 42, -18.81, 8.86, 0.01637, 39, -29.32, -36.47, 0.07116, 65, -154.71, 10.41, 0.0184, 8, 44, -15.95, 96.96, 0, 45, -1.44, 109.55, 4e-05, 46, -4.58, 116.36, 9e-05, 47, -5.34, 127.94, 0, 41, 62.73, 34.59, 0.00655, 42, 29.54, 42.18, 0.20753, 43, -3.26, 42.08, 0.21969, 39, 29.34, -33.7, 0.5661, 5, 44, 25.96, 70.78, 0.00101, 45, 24.02, 67.2, 0.01761, 46, 6.91, 68.3, 0.02042, 42, 37.16, -6.64, 0.01206, 43, 9.87, -5.56, 0.94889, 5, 40, -47.06, -4.82, 0.00808, 47, -30.31, 92.86, 0, 42, 16.42, 1.17, 0.98378, 39, -3.46, -61.6, 0.006, 65, -188.24, 23.68, 0.00214, 8, 40, -29.33, -11.14, 0.34666, 47, -46.92, 101.71, 0, 50, -98.42, 120.8, 0, 41, 23.55, 4.9, 0.39923, 42, -2.09, 4.55, 0.22154, 43, -30.41, 1.1, 6e-05, 39, -17.4, -48.96, 0.02153, 65, -170.52, 17.35, 0.01099, 7, 44, -25.7, 79.87, 0, 45, -17.88, 98.75, 1e-05, 46, -23.52, 111.03, 3e-05, 47, -24.88, 130.18, 0, 42, 10.24, 38.37, 0.20721, 43, -22, 36.11, 0.09945, 39, 10.94, -26.75, 0.69329, 10, 40, -28.13, -39.25, 0.01098, 44, -32.77, 62.11, 3e-05, 45, -32.24, 86.14, 0, 46, -41.02, 103.33, 1e-05, 47, -43.99, 129.7, 0, 50, -84.34, 145.16, 0, 41, 24.07, 33.03, 0.14563, 42, -7.82, 32.1, 0.09869, 43, -39.23, 27.82, 0.01257, 39, -7.71, -22.54, 0.73208, 6, 40, -11.86, -32.77, 0.06064, 44, -35.77, 44.85, 0.00014, 47, -61.04, 125.67, 0, 50, -101.56, 148.43, 0, 41, 7.43, 27.56, 0.23949, 39, -25.23, -22.27, 0.69972, 6, 40, -2.22, -19.39, 0.62201, 50, -116.87, 142.32, 0, 41, -3.01, 14.8, 0.25574, 42, -30.19, 8.31, 0.00156, 39, -39.27, -30.92, 0.09714, 65, -143.4, 9.11, 0.02355, 4, 40, 10.24, 15.83, 0.63204, 7, 160, -8.15, 0.33965, 64, -179.15, 44.33, 0.00597, 65, -130.95, 44.33, 0.02233, 6, 40, 5.15, 30.9, 0.30427, 44, 9.58, -2.98, 0.13525, 50, -150.08, 103.85, 0, 7, 154.91, 6.92, 0.53119, 64, -184.24, 59.39, 0.01406, 65, -136.04, 59.39, 0.01523, 6, 40, -5.56, 50.95, 0.04051, 44, 32.27, -4.35, 0.29341, 45, -4.47, -2.6, 0.07798, 7, 144.2, 26.97, 0.5732, 64, -194.95, 79.44, 0.01128, 65, -146.75, 79.44, 0.00362, 4, 45, 18.26, -5.3, 0.43909, 47, -63.81, 27.14, 0, 7, 126.22, 41.12, 0.55012, 64, -212.94, 93.6, 0.01079, 4, 45, 39.6, -0.77, 0.2155, 46, 1.24, -1.2, 0.32954, 7, 105.64, 48.38, 0.4458, 64, -233.51, 100.86, 0.00916, 3, 46, 24.66, -2.17, 0.69985, 7, 83.14, 54.93, 0.29549, 64, -256.01, 107.4, 0.00466, 10, 46, 43.81, 1.86, 0.8284, 47, -3.97, 3.63, 0.13913, 48, -38.98, 20.62, 2e-05, 50, -99.17, 13.73, 0, 51, -123.02, -20.61, 0, 42, 68.66, -75.81, 6e-05, 43, 49.02, -70.7, 0.00127, 39, 0.25, -154.56, 0, 7, 63.58, 55.61, 0.03011, 64, -275.57, 108.08, 0.00103, 9, 46, 60.31, 9.98, 0.04342, 47, 14.38, 4.89, 0.95144, 48, -21.62, 14.56, 0.00202, 49, -46.67, 28.64, 0.00081, 50, -81.91, 7.4, 0, 51, -104.69, -21.99, 0, 43, 65.23, -62.03, 0.00181, 39, 18.34, -157.83, 0, 64, -293.54, 104.15, 0.00049, 9, 46, 78.62, 20.73, 0.00586, 47, 35.39, 7.9, 0.68839, 48, -1.11, 9.07, 0.29462, 49, -28.49, 17.67, 0.01076, 50, -61.49, 1.59, 0, 51, -83.46, -22.01, 0, 43, 83.15, -50.65, 0.00024, 39, 39.45, -160.05, 0, 64, -313.88, 98.09, 0.00013, 7, 46, 94.02, 37.75, 0.00043, 47, 56.1, 17.81, 0.04388, 48, 21.82, 10.04, 0.7552, 49, -6.19, 12.24, 0.20048, 50, -38.54, 2.2, 0, 51, -61.55, -15.17, 1e-05, 39, 61.95, -155.52, 0, 4, 47, 74.51, 30.6, 0.00778, 48, 43.79, 14.56, 0.02896, 49, 16.16, 10.49, 0.9632, 51, -41.5, -5.14, 6e-05], "hull": 72, "edges": [12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 48, 50, 68, 70, 92, 94, 94, 96, 100, 102, 102, 104, 108, 110, 116, 118, 126, 128, 132, 134, 138, 140, 140, 142, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 118, 120, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 134, 136, 136, 138, 0, 142, 4, 6, 6, 8, 8, 10, 10, 12, 84, 144, 144, 146, 146, 148, 150, 6, 0, 2, 2, 4, 72, 74, 70, 72, 66, 68, 62, 64, 64, 66, 60, 62, 56, 58, 58, 60, 20, 22, 22, 24, 16, 18, 18, 20, 54, 56, 40, 42, 50, 52, 52, 54, 46, 48, 42, 44, 44, 46, 32, 34, 34, 36, 156, 158, 160, 162, 160, 164, 164, 166, 166, 168, 170, 172, 172, 174, 174, 156, 158, 176, 176, 178, 162, 180, 180, 182, 178, 184, 68, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 10, 202, 204, 204, 206, 148, 214, 214, 150, 78, 80, 80, 82, 74, 76, 76, 78], "width": 439, "height": 326}}, "head": {"head": {"type": "mesh", "uvs": [0.41776, 0.02553, 0.54214, 0.04423, 0.66652, 0.06292, 0.81296, 0.08493, 0.90474, 0.09872, 0.95941, 0.10693, 0.98911, 0.20068, 0.99548, 0.31861, 0.99365, 0.41223, 0.96361, 0.49263, 0.9433, 0.57089, 0.92814, 0.67817, 0.87641, 0.76613, 0.81948, 0.84255, 0.72071, 0.93091, 0.62867, 0.99279, 0.52825, 0.99354, 0.42994, 0.9515, 0.29742, 0.86537, 0.17857, 0.75685, 0.14109, 0.68914, 0.12306, 0.56357, 0.05451, 0.56118, 0.00646, 0.47374, 0.00638, 0.38151, 0.04231, 0.29363, 0.098, 0.29732, 0.11886, 0.198, 0.18164, 0.08403, 0.24792, 1e-05, 0.79355, 0.44029, 0.93605, 0.41711, 0.96733, 0.42593, 0.97395, 0.41416, 0.94808, 0.39258, 0.79526, 0.40092, 0.77935, 0.41612, 0.87769, 0.3916, 0.83647, 0.39626, 0.9144, 0.39127, 0.87687, 0.42453, 0.834, 0.43339, 0.90826, 0.42008, 0.61883, 0.37904, 0.58782, 0.35328, 0.42285, 0.29463, 0.53168, 0.33016, 0.47902, 0.31144, 0.35092, 0.30184, 0.28774, 0.33331, 0.29301, 0.34523, 0.36379, 0.3233, 0.41059, 0.3233, 0.59077, 0.40008, 0.61417, 0.40055, 0.508, 0.35739, 0.46397, 0.3382, 0.5526, 0.37707, 0.34671, 0.41896, 0.38025, 0.40691, 0.42289, 0.40459, 0.46836, 0.41201, 0.50247, 0.42915, 0.53146, 0.45464, 0.54965, 0.47828, 0.52237, 0.47967, 0.48314, 0.48384, 0.43482, 0.48337, 0.39333, 0.47272, 0.35979, 0.44676, 0.33589, 0.39764, 0.38137, 0.37957, 0.43083, 0.37725, 0.47801, 0.38559, 0.51837, 0.41108, 0.54509, 0.44491, 0.57124, 0.47781, 0.73812, 0.52543, 0.76023, 0.49351, 0.78607, 0.48131, 0.81355, 0.47634, 0.85126, 0.47655, 0.88309, 0.48907, 0.90787, 0.50955, 0.75462, 0.52996, 0.73961, 0.51083, 0.78416, 0.54031, 0.81866, 0.54637, 0.85581, 0.54745, 0.88474, 0.54139, 0.90347, 0.52547, 0.72902, 0.5075, 0.75618, 0.48635, 0.78415, 0.47181, 0.81577, 0.46156, 0.85387, 0.46189, 0.89238, 0.47544, 0.92522, 0.49494, 0.7248, 0.52852, 0.76168, 0.54306, 0.79654, 0.56388, 0.83047, 0.58202, 0.87912, 0.58202, 0.91357, 0.56318, 0.93263, 0.5374, 0.56235, 0.49534, 0.52141, 0.49765, 0.46101, 0.51153, 0.38721, 0.51054, 0.33775, 0.49798, 0.30248, 0.46295, 0.28749, 0.42991, 0.70261, 0.47726, 0.70087, 0.53755, 0.70743, 0.5915, 0.72262, 0.63401, 0.68453, 0.64471, 0.64913, 0.64029, 0.61386, 0.61803, 0.63153, 0.58186, 0.64257, 0.53258, 0.64605, 0.473, 0.67473, 0.62, 0.67419, 0.57672, 0.67526, 0.52995, 0.67848, 0.47157, 0.57928, 0.58816, 0.54175, 0.62619, 0.54496, 0.66116, 0.57446, 0.67514, 0.63395, 0.68472, 0.69652, 0.69479, 0.72854, 0.66972, 0.80814, 0.29538, 0.82039, 0.18117, 0.62996, 0.26655, 0.65173, 0.15788, 0.43682, 0.22774, 0.48035, 0.13127, 0.26272, 0.20778, 0.32665, 0.11685, 0.20695, 0.31534, 0.18519, 0.39628, 0.18383, 0.48388, 0.2124, 0.56593, 0.0995, 0.39432, 0.12535, 0.4797, 0.28901, 0.58541, 0.29158, 0.68308, 0.33205, 0.80298, 0.43888, 0.89103, 0.53904, 0.94222, 0.6326, 0.9457, 0.43781, 0.59612, 0.42576, 0.67587, 0.57705, 0.54291, 0.70833, 0.90079, 0.78997, 0.818, 0.83012, 0.75363, 0.88231, 0.65761, 0.79131, 0.64097, 0.76224, 0.71781, 0.45766, 0.75674, 0.49932, 0.74886, 0.55245, 0.72967, 0.60196, 0.72277, 0.62913, 0.74296, 0.66415, 0.73065, 0.69374, 0.7464, 0.71366, 0.76904, 0.71729, 0.78725, 0.75231, 0.77938, 0.75412, 0.80891, 0.68416, 0.83859, 0.64544, 0.85813, 0.59532, 0.85911, 0.5434, 0.8473, 0.4963, 0.82121, 0.45645, 0.77642, 0.41841, 0.77445, 0.44196, 0.74295, 0.44438, 0.76166, 0.47275, 0.77199, 0.50717, 0.77445, 0.53796, 0.77495, 0.58144, 0.76953, 0.61706, 0.78331, 0.65363, 0.78405, 0.67113, 0.79168, 0.69818, 0.81041, 0.71756, 0.80859, 0.73359, 0.7971, 0.64573, 0.79587, 0.61344, 0.79562, 0.57661, 0.78824, 0.55828, 0.80942, 0.60865, 0.81985, 0.65502, 0.81952, 0.5561, 0.86982, 0.63128, 0.87487, 0.92531, 0.30488, 0.91949, 0.19004], "triangles": [137, 48, 139, 139, 140, 137, 28, 29, 140, 140, 29, 0, 137, 140, 138, 135, 47, 137, 140, 0, 138, 137, 138, 135, 138, 0, 1, 136, 2, 3, 136, 3, 134, 50, 142, 141, 26, 27, 141, 141, 27, 139, 24, 25, 145, 146, 23, 145, 73, 55, 74, 72, 56, 73, 61, 72, 73, 71, 52, 72, 53, 75, 74, 146, 22, 23, 111, 50, 70, 59, 70, 71, 60, 72, 61, 61, 73, 74, 62, 61, 74, 69, 58, 59, 111, 70, 58, 63, 66, 62, 64, 63, 75, 144, 146, 143, 143, 111, 110, 69, 59, 68, 110, 58, 69, 109, 69, 68, 68, 59, 60, 144, 21, 146, 20, 21, 144, 147, 144, 109, 20, 148, 19, 108, 67, 107, 107, 67, 66, 105, 64, 76, 76, 54, 121, 36, 112, 125, 16, 151, 15, 15, 151, 152, 17, 150, 151, 181, 180, 162, 178, 181, 182, 181, 162, 182, 177, 179, 178, 179, 177, 150, 178, 182, 177, 177, 182, 183, 182, 163, 183, 154, 153, 127, 127, 153, 126, 153, 155, 126, 126, 155, 119, 15, 152, 14, 151, 198, 152, 198, 199, 152, 152, 156, 14, 152, 199, 156, 151, 150, 198, 13, 14, 156, 199, 174, 156, 174, 173, 156, 150, 176, 198, 150, 177, 176, 198, 175, 199, 199, 175, 174, 198, 176, 175, 176, 195, 175, 175, 196, 174, 175, 195, 196, 176, 177, 195, 184, 195, 177, 184, 177, 183, 195, 194, 196, 195, 184, 194, 194, 184, 185, 185, 184, 164, 164, 184, 163, 184, 183, 163, 128, 164, 163, 163, 154, 128, 164, 128, 129, 154, 127, 128, 129, 128, 127, 127, 126, 118, 99, 113, 98, 99, 114, 113, 119, 120, 123, 119, 155, 120, 123, 124, 113, 123, 120, 124, 99, 86, 100, 100, 86, 87, 98, 77, 99, 77, 84, 99, 99, 84, 86, 84, 78, 86, 86, 78, 79, 98, 113, 91, 120, 121, 124, 77, 85, 84, 84, 85, 78, 112, 91, 113, 124, 121, 125, 98, 91, 77, 112, 113, 124, 77, 91, 85, 78, 85, 92, 85, 91, 92, 118, 126, 119, 196, 194, 193, 174, 196, 197, 194, 185, 186, 100, 114, 99, 118, 119, 122, 119, 123, 122, 100, 87, 101, 196, 193, 192, 122, 123, 114, 186, 185, 166, 185, 165, 166, 130, 129, 117, 174, 197, 173, 196, 192, 197, 193, 186, 192, 13, 156, 157, 156, 172, 157, 172, 173, 190, 190, 191, 172, 172, 156, 173, 190, 173, 189, 12, 157, 158, 12, 13, 157, 173, 197, 189, 197, 188, 189, 197, 192, 188, 158, 157, 171, 190, 189, 170, 191, 171, 172, 157, 172, 171, 189, 188, 170, 190, 170, 191, 191, 170, 171, 192, 187, 188, 192, 186, 187, 170, 188, 169, 169, 188, 168, 170, 169, 171, 186, 166, 187, 188, 187, 168, 187, 167, 168, 187, 166, 167, 171, 161, 158, 171, 169, 161, 169, 168, 161, 11, 12, 159, 158, 161, 159, 12, 158, 159, 159, 161, 160, 168, 131, 161, 168, 167, 131, 166, 165, 167, 165, 130, 167, 167, 130, 131, 131, 132, 161, 161, 132, 160, 131, 130, 116, 130, 117, 116, 131, 116, 132, 11, 159, 10, 160, 101, 159, 101, 102, 159, 116, 115, 132, 132, 115, 160, 117, 122, 116, 116, 122, 115, 160, 100, 101, 100, 160, 114, 122, 114, 115, 114, 160, 115, 165, 129, 130, 117, 118, 122, 129, 118, 117, 193, 194, 186, 185, 164, 165, 164, 129, 165, 123, 113, 114, 118, 129, 127, 91, 112, 92, 78, 92, 79, 182, 162, 163, 162, 180, 163, 163, 180, 154, 155, 153, 107, 155, 105, 120, 178, 179, 181, 179, 180, 181, 180, 179, 148, 124, 125, 112, 112, 36, 92, 180, 148, 154, 149, 179, 150, 149, 148, 179, 154, 148, 153, 17, 18, 150, 18, 149, 150, 153, 108, 107, 147, 153, 148, 108, 153, 147, 155, 106, 105, 105, 76, 120, 120, 76, 121, 121, 54, 125, 106, 155, 107, 107, 66, 106, 106, 64, 105, 18, 19, 149, 106, 65, 64, 76, 64, 75, 19, 148, 149, 16, 17, 151, 106, 66, 65, 65, 63, 64, 63, 65, 66, 62, 66, 61, 66, 67, 61, 147, 148, 144, 108, 147, 109, 108, 68, 67, 108, 109, 68, 144, 148, 20, 144, 110, 109, 67, 60, 61, 68, 60, 67, 144, 143, 110, 109, 110, 69, 143, 142, 111, 110, 111, 58, 63, 62, 75, 58, 70, 59, 59, 71, 60, 76, 75, 53, 62, 74, 75, 60, 71, 72, 21, 22, 146, 143, 146, 142, 71, 70, 51, 146, 145, 142, 145, 23, 24, 111, 142, 50, 142, 145, 141, 145, 25, 26, 145, 26, 141, 27, 28, 139, 49, 141, 139, 139, 28, 140, 135, 136, 134, 135, 138, 136, 136, 1, 2, 138, 1, 136, 101, 87, 88, 87, 86, 80, 10, 159, 102, 101, 88, 102, 102, 88, 89, 88, 87, 81, 89, 90, 103, 89, 88, 82, 82, 88, 81, 83, 97, 104, 90, 82, 83, 80, 94, 81, 81, 94, 95, 93, 30, 94, 8, 9, 32, 8, 34, 7, 200, 37, 133, 133, 201, 200, 134, 3, 4, 7, 34, 200, 200, 6, 7, 200, 201, 6, 133, 134, 201, 201, 5, 6, 134, 4, 201, 201, 4, 5, 97, 96, 31, 83, 82, 97, 82, 96, 97, 82, 81, 96, 104, 97, 9, 9, 97, 32, 81, 95, 96, 95, 40, 96, 94, 41, 95, 133, 135, 134, 43, 135, 133, 80, 93, 94, 103, 90, 104, 89, 82, 90, 90, 83, 104, 10, 104, 9, 87, 80, 81, 93, 92, 30, 102, 89, 103, 103, 104, 10, 10, 102, 103, 86, 79, 80, 92, 93, 79, 79, 93, 80, 8, 32, 33, 32, 97, 31, 32, 31, 33, 31, 34, 33, 31, 39, 34, 33, 34, 8, 31, 96, 42, 96, 40, 42, 40, 37, 42, 40, 38, 37, 42, 39, 31, 42, 37, 39, 38, 133, 37, 39, 37, 200, 34, 39, 200, 95, 41, 40, 92, 36, 30, 94, 30, 41, 41, 30, 35, 30, 36, 35, 41, 38, 40, 41, 35, 38, 36, 43, 35, 38, 35, 133, 35, 43, 133, 76, 53, 54, 53, 74, 57, 54, 43, 36, 74, 55, 57, 54, 53, 43, 57, 44, 53, 53, 44, 43, 43, 44, 135, 57, 46, 44, 44, 46, 135, 125, 54, 36, 73, 56, 55, 72, 52, 56, 55, 46, 57, 56, 47, 55, 55, 47, 46, 47, 56, 45, 46, 47, 135, 56, 52, 45, 45, 52, 48, 47, 45, 137, 50, 141, 49, 70, 50, 51, 71, 51, 52, 50, 49, 51, 49, 48, 51, 48, 49, 139, 52, 51, 48, 45, 48, 137], "vertices": [2, 7, 130.53, 20.49, 0.97777, 64, -208.62, 72.97, 0.02223, 2, 7, 131.43, 1.54, 0.97522, 64, -207.73, 54.02, 0.02478, 2, 7, 132.32, -17.41, 0.97625, 64, -206.84, 35.07, 0.02375, 2, 7, 133.37, -39.72, 0.98117, 64, -205.79, 12.76, 0.01883, 2, 7, 134.02, -53.7, 0.98505, 64, -205.13, -1.23, 0.01495, 2, 7, 134.42, -62.03, 0.99, 64, -204.74, -9.56, 0.01, 2, 7, 118.63, -70.29, 0.98961, 64, -220.52, -17.81, 0.01039, 2, 7, 97.72, -76.15, 0.98922, 64, -241.44, -23.68, 0.01078, 2, 7, 80.88, -79.8, 0.98934, 64, -258.27, -27.33, 0.01066, 2, 7, 65.45, -78.78, 0.98948, 64, -273.7, -26.3, 0.01052, 2, 7, 50.73, -79.08, 0.98926, 64, -288.42, -26.61, 0.01074, 2, 7, 30.99, -81.36, 0.9865, 64, -308.16, -28.88, 0.0135, 2, 7, 13.47, -77.48, 0.9838, 64, -325.68, -25, 0.0162, 2, 7, -2.17, -72.36, 0.98252, 64, -341.32, -19.88, 0.01748, 2, 7, -21.37, -61.62, 0.97998, 64, -360.52, -9.15, 0.02002, 2, 7, -35.59, -50.77, 0.97591, 64, -374.74, 1.71, 0.02409, 2, 7, -39.15, -36.13, 0.97217, 64, -378.3, 16.35, 0.02783, 2, 7, -34.97, -20.01, 0.97305, 64, -374.12, 32.46, 0.02695, 2, 7, -24.05, 2.95, 0.97588, 64, -363.21, 55.42, 0.02412, 2, 7, -8.66, 24.85, 0.97895, 64, -347.81, 77.32, 0.02105, 2, 7, 2.19, 33.15, 0.98019, 64, -336.96, 85.63, 0.01981, 2, 7, 24.08, 41.04, 0.98253, 64, -315.07, 93.52, 0.01747, 2, 7, 22.17, 51.15, 0.98648, 64, -316.98, 103.63, 0.01352, 2, 7, 36.2, 61.83, 0.98922, 64, -302.95, 114.3, 0.01078, 2, 7, 52.72, 65.7, 0.99, 64, -286.43, 118.17, 0.01, 2, 7, 69.69, 64.12, 0.99, 64, -269.46, 116.6, 0.01, 2, 7, 70.93, 55.84, 0.9883, 64, -268.22, 108.31, 0.0117, 2, 7, 89.44, 56.94, 0.99, 64, -249.71, 109.42, 0.01, 2, 7, 112, 52.54, 0.99, 64, -227.15, 105.01, 0.01, 2, 7, 129.32, 46.37, 0.99, 64, -209.84, 98.85, 0.01, 4, 33, 8.94, 3.41, 0.98475, 32, 21.68, 4.07, 0.00164, 31, 22.29, 15.1, 0.01145, 64, -270.12, 0.73, 0.00216, 4, 33, -12.85, 3.34, 0.00658, 32, -0.05, 2.37, 0.25167, 31, 4.89, 1.97, 0.73547, 65, -212.91, -19.12, 0.00628, 4, 33, -17.15, 5.84, 0.00578, 32, -4.52, 4.54, 0.02056, 31, -0.04, 1.39, 0.95637, 65, -213.42, -24.05, 0.01728, 2, 31, 0.01, -0.99, 0.98124, 65, -211.09, -24.53, 0.01876, 3, 32, -2.38, -1.9, 0.26612, 31, 5.23, -2.88, 0.72663, 65, -208.1, -19.85, 0.00725, 3, 33, 7.29, -3.65, 0.95218, 32, 20.56, -3.1, 0.04477, 64, -263.01, 2.13, 0.00305, 3, 33, 10.17, -1.37, 0.99134, 32, 23.26, -0.6, 0.00522, 64, -266.28, 3.81, 0.00343, 4, 33, -5.17, -2.95, 0.02588, 32, 8.08, -3.33, 0.97387, 64, -258.53, -9.52, 7e-05, 65, -210.33, -9.52, 0.00018, 3, 33, 1.06, -3.3, 0.6148, 32, 14.32, -3.21, 0.38359, 64, -260.77, -3.7, 0.00161, 3, 32, 2.61, -2.74, 0.93822, 31, 9.88, -0.9, 0.06052, 65, -209.02, -14.87, 0.00127, 4, 33, -3.88, 2.97, 0.0809, 32, 8.92, 2.67, 0.87482, 31, 12.29, 7.06, 0.04403, 65, -216.26, -10.78, 0.00025, 4, 33, 2.74, 3.33, 0.83145, 32, 15.5, 3.53, 0.14525, 31, 17.38, 11.32, 0.02236, 64, -267.51, -4.89, 0.00094, 4, 33, -8.66, 3.07, 0.01363, 32, 4.15, 2.42, 0.86137, 31, 8.41, 4.27, 0.1239, 65, -214.39, -15.18, 0.0011, 2, 36, 13.1, 2.65, 0.99452, 65, -216.9, 28.81, 0.00548, 3, 35, 28.54, 2.42, 0.00515, 36, 6.58, 3.9, 0.99042, 65, -213.35, 34.42, 0.00443, 2, 34, 19.6, 1.43, 0.1561, 35, 1.55, 2.98, 0.8439, 3, 35, 19.13, 3.14, 0.85325, 36, -2.77, 2.62, 0.14416, 65, -211.12, 43.59, 0.00259, 2, 35, 10.51, 3.32, 0.99913, 65, -209.56, 52.06, 0.00087, 2, 34, 8.83, 2.92, 0.9994, 65, -212.2, 71.18, 0.0006, 4, 34, -1.82, -0.25, 0.99218, 35, -14.48, -11.32, 0.00089, 36, -32.54, -18.66, 0.00173, 65, -220, 79.09, 0.00519, 4, 34, -1.62, -2.58, 0.97885, 35, -12.92, -13.05, 0.00907, 36, -30.65, -20.02, 0.00746, 65, -221.95, 77.82, 0.00462, 4, 34, 9.68, -1.4, 0.97206, 35, -4.64, -5.28, 0.02572, 36, -24.21, -10.66, 0.00199, 65, -215.61, 68.4, 0.00023, 3, 34, 16.47, -3.19, 0.28364, 35, 1.85, -2.6, 0.71262, 36, -18.43, -6.67, 0.00374, 4, 34, 38.97, -23.78, 0.00582, 35, 32.22, -5.38, 0.00857, 36, 11.84, -2.93, 0.98096, 65, -221.63, 32.03, 0.00465, 4, 34, 42.34, -24.76, 0.00188, 35, 35.5, -4.12, 0.00037, 36, 14.77, -1.01, 0.99241, 65, -220.92, 28.59, 0.00534, 4, 34, 28.98, -13, 0.01262, 35, 17.75, -2.84, 0.85159, 36, -2.84, -3.53, 0.1337, 65, -216.8, 45.91, 0.00209, 4, 34, 23.5, -7.9, 0.02609, 35, 10.3, -2.09, 0.96425, 36, -10.28, -4.37, 0.00897, 65, -214.87, 53.14, 0.00069, 4, 34, 34.52, -18.22, 0.00694, 35, 25.32, -3.64, 0.06015, 36, 4.72, -2.7, 0.92942, 65, -218.81, 38.57, 0.00349, 2, 7, 57.62, 14.42, 0.96681, 64, -281.54, 66.89, 0.03319, 2, 7, 60.92, 10.02, 0.96649, 64, -278.23, 62.5, 0.03351, 2, 7, 62.79, 3.89, 0.96712, 64, -276.36, 56.37, 0.03288, 2, 7, 63.01, -3.06, 0.96781, 64, -276.14, 49.41, 0.03219, 2, 7, 61.1, -8.76, 0.96829, 64, -278.05, 43.71, 0.03171, 2, 7, 57.52, -14.06, 0.9686, 64, -281.63, 38.41, 0.0314, 2, 7, 53.91, -17.71, 0.96895, 64, -285.25, 34.77, 0.03105, 2, 7, 52.73, -13.78, 0.96826, 64, -286.43, 38.7, 0.03174, 2, 7, 50.64, -8.22, 0.96758, 64, -288.51, 44.25, 0.03242, 2, 7, 49.08, -1.15, 0.96677, 64, -290.07, 51.33, 0.03323, 2, 7, 49.57, 5.36, 0.96614, 64, -289.58, 57.84, 0.03386, 2, 7, 53.08, 11.35, 0.96632, 64, -286.07, 63.82, 0.03368, 2, 7, 61.07, 16.89, 0.96725, 64, -278.09, 69.37, 0.03275, 2, 7, 65.86, 11, 0.96679, 64, -273.3, 63.48, 0.03321, 2, 7, 67.96, 3.88, 0.96739, 64, -271.19, 56.35, 0.03261, 2, 7, 68.07, -3.37, 0.9681, 64, -271.08, 49.11, 0.0319, 2, 7, 64.88, -10.33, 0.96861, 64, -274.27, 42.15, 0.03139, 2, 7, 59.73, -15.65, 0.96889, 64, -279.42, 36.83, 0.03111, 2, 7, 54.73, -20.84, 0.96959, 64, -284.43, 31.63, 0.03041, 2, 7, 51.88, -47.21, 0.97458, 64, -287.27, 5.27, 0.02542, 2, 7, 58.36, -49.1, 0.97518, 64, -280.8, 3.37, 0.02482, 2, 7, 61.42, -52.37, 0.97593, 64, -277.73, 0.11, 0.02407, 2, 7, 63.25, -56.17, 0.97674, 64, -275.9, -3.7, 0.02326, 2, 7, 64.5, -61.69, 0.97785, 64, -274.65, -9.22, 0.02215, 2, 7, 63.34, -66.87, 0.97911, 64, -275.81, -14.39, 0.02089, 2, 7, 60.52, -71.34, 0.9803, 64, -278.64, -18.87, 0.0197, 2, 7, 51.64, -49.81, 0.97508, 64, -287.52, 2.67, 0.02492, 2, 7, 54.55, -46.82, 0.97461, 64, -284.6, 5.66, 0.02539, 2, 7, 50.79, -54.56, 0.97598, 64, -288.37, -2.08, 0.02402, 2, 7, 50.88, -59.85, 0.97701, 64, -288.27, -7.37, 0.02299, 2, 7, 51.95, -65.32, 0.97811, 64, -287.2, -12.85, 0.02189, 2, 7, 54.02, -69.29, 0.97937, 64, -285.13, -16.82, 0.02063, 2, 7, 57.51, -71.36, 0.98016, 64, -281.64, -18.89, 0.01984, 2, 7, 54.79, -45.13, 0.9743, 64, -284.37, 7.35, 0.0257, 2, 7, 59.5, -48.21, 0.97504, 64, -279.65, 4.26, 0.02496, 2, 7, 63.06, -51.69, 0.97587, 64, -276.09, 0.79, 0.02413, 2, 7, 65.97, -55.88, 0.9768, 64, -273.18, -3.41, 0.0232, 2, 7, 67.21, -61.46, 0.97793, 64, -271.94, -8.99, 0.02207, 2, 7, 66.1, -67.65, 0.97906, 64, -273.05, -15.18, 0.02094, 2, 7, 63.73, -73.26, 0.98205, 64, -275.43, -20.79, 0.01795, 2, 7, 50.88, -45.39, 0.9742, 64, -288.28, 7.08, 0.0258, 2, 7, 49.53, -51.39, 0.97533, 64, -289.62, 1.09, 0.02467, 2, 7, 46.99, -57.35, 0.97644, 64, -292.17, -4.88, 0.02356, 2, 7, 44.89, -63.07, 0.9775, 64, -294.26, -10.59, 0.0225, 2, 7, 46.55, -70.17, 0.97893, 64, -292.6, -17.7, 0.02107, 2, 7, 51.1, -74.42, 0.98263, 64, -288.05, -21.94, 0.01737, 2, 7, 56.37, -76.12, 0.98574, 64, -282.78, -23.65, 0.01426, 2, 7, 51.28, -20.28, 0.96936, 64, -287.87, 32.2, 0.03064, 2, 7, 49.47, -14.39, 0.96816, 64, -289.68, 38.08, 0.03184, 2, 7, 44.93, -6.15, 0.967, 64, -294.23, 46.33, 0.033, 2, 7, 42.59, 4.67, 0.96572, 64, -296.57, 57.15, 0.03428, 2, 7, 43.15, 12.42, 0.96629, 64, -296, 64.9, 0.03371, 2, 7, 48.23, 19.04, 0.96761, 64, -290.93, 71.51, 0.03239, 2, 7, 53.64, 22.61, 0.96886, 64, -285.52, 75.09, 0.03114, 2, 7, 59.3, -40.01, 0.97141, 64, -279.85, 12.47, 0.02859, 2, 7, 48.44, -42.28, 0.96943, 64, -290.71, 10.2, 0.03057, 2, 7, 39, -45.49, 0.96828, 64, -300.16, 6.99, 0.03172, 2, 7, 31.9, -49.49, 0.96874, 64, -307.25, 2.99, 0.03126, 2, 7, 28.68, -44.37, 0.967, 64, -310.47, 8.11, 0.033, 2, 7, 28.27, -39.01, 0.96604, 64, -310.88, 13.46, 0.03396, 2, 7, 31.05, -32.93, 0.96561, 64, -308.1, 19.54, 0.03439, 2, 7, 38.14, -34, 0.96629, 64, -301.01, 18.48, 0.03371, 2, 7, 47.34, -33.55, 0.96783, 64, -291.81, 18.92, 0.03217, 2, 7, 58.14, -31.57, 0.96981, 64, -281.01, 20.91, 0.03019, 2, 7, 32.78, -41.9, 0.96616, 64, -306.38, 10.57, 0.03384, 2, 7, 40.51, -40.02, 0.96741, 64, -298.64, 12.46, 0.03259, 2, 7, 48.93, -38.22, 0.9688, 64, -290.22, 14.26, 0.0312, 2, 7, 59.5, -36.25, 0.97078, 64, -279.65, 16.23, 0.02922, 2, 7, 35.23, -26.63, 0.97011, 64, -303.92, 25.84, 0.02989, 2, 7, 27.13, -22.74, 0.96908, 64, -312.02, 29.74, 0.03092, 2, 7, 20.98, -24.67, 0.96915, 64, -318.17, 27.8, 0.03085, 2, 7, 19.48, -29.56, 0.96972, 64, -319.68, 22.91, 0.03028, 2, 7, 19.79, -38.66, 0.97133, 64, -319.36, 13.82, 0.02867, 2, 7, 20.12, -48.22, 0.97256, 64, -319.04, 4.26, 0.02744, 2, 7, 25.7, -51.85, 0.97164, 64, -313.45, 0.63, 0.02836, 2, 7, 95.49, -47.82, 0.97623, 64, -243.66, 4.66, 0.02377, 2, 7, 116.37, -44.83, 0.97921, 64, -222.78, 7.65, 0.02079, 2, 7, 94.58, -20.58, 0.9709, 64, -244.57, 31.89, 0.0291, 2, 7, 114.8, -19.22, 0.97302, 64, -224.36, 33.26, 0.02698, 2, 7, 94.95, 9.25, 0.96817, 64, -244.2, 61.73, 0.03183, 2, 7, 113.72, 6.93, 0.97191, 64, -225.43, 59.4, 0.02809, 2, 7, 92.59, 35.52, 0.97828, 64, -246.56, 87.99, 0.02172, 2, 7, 111.06, 29.98, 0.97912, 64, -228.09, 82.46, 0.02088, 2, 7, 71.42, 39.17, 0.97777, 64, -267.73, 91.64, 0.02223, 2, 7, 56.17, 38.96, 0.97806, 64, -282.98, 91.44, 0.02194, 2, 7, 40.43, 35.5, 0.97623, 64, -298.72, 87.97, 0.02377, 2, 7, 26.7, 27.89, 0.97212, 64, -312.45, 80.37, 0.02788, 2, 7, 53.6, 51.56, 0.98628, 64, -285.55, 104.03, 0.01372, 2, 7, 39.18, 44.21, 0.98258, 64, -299.97, 96.69, 0.01742, 2, 7, 25.82, 15.88, 0.96586, 64, -313.33, 68.36, 0.03414, 2, 7, 8.41, 11.42, 0.96698, 64, -330.74, 63.9, 0.03302, 2, 7, -11.69, 0.5, 0.97022, 64, -350.85, 52.97, 0.02978, 2, 7, -23.83, -18.79, 0.97061, 64, -362.98, 33.69, 0.02939, 2, 7, -29.59, -35.56, 0.96962, 64, -368.74, 16.91, 0.03038, 2, 7, -27.02, -49.37, 0.97235, 64, -366.17, 3.1, 0.02765, 2, 7, 28.98, -6.3, 0.96594, 64, -310.17, 46.18, 0.03406, 2, 7, 14.28, -7.87, 0.96586, 64, -324.88, 44.6, 0.03414, 2, 7, 43.26, -24.41, 0.96992, 64, -295.89, 28.06, 0.03008, 2, 7, -16.39, -58.56, 0.97633, 64, -355.54, -6.08, 0.02367, 2, 7, 1.23, -67.02, 0.97898, 64, -337.93, -14.54, 0.02102, 2, 7, 14.13, -70.19, 0.98018, 64, -325.02, -17.72, 0.01982, 2, 7, 33.11, -73.8, 0.98163, 64, -306.04, -21.33, 0.01837, 2, 7, 32.99, -59.81, 0.97647, 64, -306.16, -7.34, 0.02353, 2, 7, 18.24, -58.78, 0.97587, 64, -320.92, -6.3, 0.02413, 2, 7, 0.87, -15.92, 0.96732, 64, -338.28, 36.56, 0.03268, 2, 7, 3.71, -21.67, 0.96785, 64, -335.45, 30.8, 0.03215, 2, 7, 8.96, -28.63, 0.96821, 64, -330.2, 23.85, 0.03179, 2, 7, 11.88, -35.57, 0.96875, 64, -327.27, 16.9, 0.03125, 2, 7, 9.19, -40.39, 0.9694, 64, -329.96, 12.09, 0.0306, 2, 7, 12.59, -44.99, 0.97036, 64, -326.56, 7.49, 0.02964, 2, 7, 10.78, -49.97, 0.97164, 64, -328.38, 2.51, 0.02836, 2, 7, 7.4, -53.83, 0.97272, 64, -331.75, -1.35, 0.02728, 2, 7, 4.26, -55.12, 0.97312, 64, -334.89, -2.64, 0.02688, 2, 7, 6.86, -59.9, 0.97445, 64, -332.29, -7.43, 0.02555, 2, 7, 1.63, -61.4, 0.97463, 64, -337.52, -8.93, 0.02537, 2, 7, -6.07, -52.42, 0.9721, 64, -345.22, 0.05, 0.0279, 2, 7, -10.89, -47.59, 0.97044, 64, -350.04, 4.89, 0.02956, 2, 7, -12.78, -40.31, 0.96881, 64, -351.93, 12.17, 0.03119, 2, 7, -12.43, -32.23, 0.96802, 64, -351.58, 20.25, 0.03198, 2, 7, -9.36, -24.26, 0.96769, 64, -348.51, 28.22, 0.03231, 2, 7, -2.69, -16.56, 0.96727, 64, -341.85, 35.91, 0.03273, 2, 7, -3.64, -10.92, 0.96681, 64, -342.79, 41.55, 0.03319, 2, 7, 2.81, -13.05, 0.96707, 64, -336.34, 39.43, 0.03293, 2, 7, -0.46, -14.18, 0.96709, 64, -339.61, 38.29, 0.03291, 2, 7, -1.34, -18.76, 0.96755, 64, -340.5, 33.72, 0.03245, 2, 7, -0.61, -23.89, 0.96812, 64, -339.76, 28.59, 0.03188, 2, 7, 0.35, -28.41, 0.96864, 64, -338.8, 24.07, 0.03136, 2, 7, 2.8, -34.53, 0.96939, 64, -336.35, 17.94, 0.03061, 2, 7, 1.55, -40.31, 0.96995, 64, -337.61, 12.16, 0.03005, 2, 7, 2.66, -45.68, 0.97102, 64, -336.49, 6.79, 0.02898, 2, 7, 1.89, -48.56, 0.97166, 64, -337.26, 3.92, 0.02834, 2, 7, -0.54, -53.29, 0.97271, 64, -339.69, -0.82, 0.02729, 2, 7, 0.44, -56.05, 0.97337, 64, -338.71, -3.57, 0.02663, 2, 7, 3.05, -57.91, 0.97384, 64, -336.1, -5.43, 0.02616, 2, 7, 0.28, -45.03, 0.97079, 64, -338.88, 7.45, 0.02921, 2, 7, -0.78, -40.3, 0.96988, 64, -339.93, 12.18, 0.03012, 2, 7, -0.71, -34.61, 0.96926, 64, -339.87, 17.87, 0.03074, 2, 7, -5.13, -32.82, 0.96794, 64, -344.29, 19.66, 0.03206, 2, 7, -5.29, -40.61, 0.96849, 64, -344.44, 11.87, 0.03151, 2, 7, -3.65, -47.37, 0.97017, 64, -342.8, 5.11, 0.02983, 2, 7, -16.03, -35.02, 0.96904, 64, -355.18, 17.45, 0.03096, 2, 7, -14.37, -46.22, 0.97083, 64, -353.53, 6.26, 0.02917, 2, 7, 97.79, -65.33, 0.98217, 64, -241.37, -12.85, 0.01783, 2, 7, 118.16, -59.68, 0.98417, 64, -220.99, -7.2, 0.01583], "hull": 30, "edges": [10, 12, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 50, 52, 52, 54, 54, 56, 56, 58, 12, 14, 14, 16, 46, 48, 48, 50, 38, 40, 40, 42, 22, 24, 24, 26, 16, 18, 18, 20, 20, 22, 62, 64, 66, 68, 70, 72, 72, 60, 70, 76, 76, 74, 68, 78, 78, 74, 60, 82, 82, 80, 62, 84, 84, 80, 86, 88, 88, 92, 90, 94, 94, 92, 90, 96, 96, 98, 100, 102, 102, 104, 100, 98, 106, 108, 108, 86, 104, 112, 112, 110, 106, 114, 114, 110, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 116, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 154, 168, 154, 170, 170, 156, 168, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 166, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 244, 246, 246, 248, 250, 248, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 278, 280, 0, 58, 280, 0, 278, 282, 282, 284, 284, 286, 286, 288, 52, 290, 290, 292, 292, 288, 294, 296, 296, 298, 298, 300, 302, 304, 302, 300, 306, 308, 304, 312, 312, 314, 314, 316, 316, 318, 320, 322, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 376, 378, 378, 380, 380, 382, 376, 384, 384, 386, 386, 388, 388, 368, 378, 346, 390, 392, 392, 394, 396, 398, 4, 6, 0, 2, 2, 4, 6, 8, 8, 10, 26, 28, 28, 30], "width": 150, "height": 184}}, "head2": {"head": {"type": "mesh", "uvs": [0.29158, 0.68308, 0.43781, 0.59612, 0.57928, 0.58816, 0.63153, 0.58186, 0.67419, 0.57672, 0.70743, 0.5915, 0.79131, 0.64097, 0.88231, 0.65761, 0.92814, 0.67817, 0.87641, 0.76613, 0.81948, 0.84255, 0.72071, 0.93091, 0.62867, 0.99279, 0.52825, 0.99354, 0.42994, 0.9515, 0.29742, 0.86537, 0.17857, 0.75685, 0.72262, 0.63401, 0.68453, 0.64471, 0.64913, 0.64029, 0.61386, 0.61803, 0.67473, 0.62, 0.54175, 0.62619, 0.54496, 0.66116, 0.57446, 0.67514, 0.63395, 0.68472, 0.69652, 0.69479, 0.72854, 0.66972, 0.33205, 0.80298, 0.43888, 0.89103, 0.53904, 0.94222, 0.6326, 0.9457, 0.42576, 0.67587, 0.70833, 0.90079, 0.78997, 0.818, 0.83012, 0.75363, 0.76224, 0.71781, 0.45766, 0.75674, 0.49932, 0.74886, 0.55245, 0.72967, 0.60196, 0.72277, 0.62913, 0.74296, 0.66415, 0.73065, 0.69374, 0.7464, 0.71366, 0.76904, 0.71729, 0.78725, 0.75231, 0.77938, 0.75412, 0.80891, 0.68416, 0.83859, 0.64544, 0.85813, 0.59532, 0.85911, 0.5434, 0.8473, 0.4963, 0.82121, 0.45645, 0.77642, 0.41841, 0.77445, 0.44196, 0.74295, 0.44438, 0.76166, 0.47275, 0.77199, 0.50717, 0.77445, 0.53796, 0.77495, 0.58144, 0.76953, 0.61706, 0.78331, 0.65363, 0.78405, 0.67113, 0.79168, 0.69818, 0.81041, 0.71756, 0.80859, 0.73359, 0.7971, 0.64573, 0.79587, 0.61344, 0.79562, 0.57661, 0.78824, 0.55828, 0.80942, 0.60865, 0.81985, 0.65502, 0.81952, 0.5561, 0.86982, 0.63128, 0.87487], "triangles": [20, 2, 3, 21, 4, 5, 3, 4, 21, 20, 3, 21, 22, 1, 2, 22, 2, 20, 6, 17, 5, 21, 5, 17, 19, 20, 21, 18, 21, 17, 19, 21, 18, 24, 23, 22, 27, 17, 6, 18, 17, 27, 20, 24, 22, 24, 20, 19, 32, 1, 22, 32, 22, 23, 32, 0, 1, 25, 24, 19, 26, 18, 27, 25, 19, 18, 26, 25, 18, 36, 27, 6, 26, 27, 36, 40, 24, 25, 39, 23, 24, 39, 24, 40, 38, 32, 23, 42, 25, 26, 40, 25, 42, 38, 55, 32, 41, 40, 42, 43, 42, 26, 43, 26, 36, 23, 39, 38, 7, 36, 6, 9, 35, 7, 35, 36, 7, 37, 55, 38, 56, 55, 37, 8, 9, 7, 44, 43, 36, 60, 39, 40, 60, 40, 41, 57, 37, 38, 56, 37, 57, 59, 58, 38, 39, 59, 38, 57, 38, 58, 55, 54, 0, 55, 0, 32, 54, 55, 56, 60, 59, 39, 53, 56, 57, 46, 44, 36, 46, 36, 35, 61, 60, 41, 62, 41, 42, 62, 42, 43, 63, 62, 43, 61, 41, 62, 45, 44, 46, 44, 63, 43, 69, 59, 60, 69, 60, 61, 45, 63, 44, 68, 69, 61, 67, 61, 62, 68, 61, 67, 67, 62, 63, 66, 45, 46, 28, 0, 54, 16, 0, 28, 65, 45, 66, 64, 63, 45, 34, 47, 46, 66, 46, 47, 70, 59, 69, 65, 64, 45, 35, 34, 46, 72, 67, 63, 72, 63, 64, 71, 69, 68, 70, 69, 71, 71, 68, 67, 71, 67, 72, 52, 57, 58, 53, 57, 52, 48, 72, 64, 9, 10, 34, 9, 34, 35, 59, 52, 58, 59, 70, 52, 51, 52, 70, 49, 71, 72, 49, 72, 48, 50, 70, 71, 50, 71, 49, 51, 70, 50, 15, 16, 28, 73, 51, 50, 74, 50, 49, 73, 50, 74, 54, 52, 29, 53, 54, 56, 52, 54, 53, 29, 52, 51, 29, 51, 73, 28, 54, 29, 15, 28, 29, 65, 48, 64, 47, 33, 48, 49, 48, 33, 74, 49, 33, 65, 66, 47, 47, 48, 65, 33, 47, 34, 10, 11, 33, 10, 33, 34, 30, 29, 73, 31, 74, 33, 31, 33, 11, 73, 74, 31, 30, 73, 31, 14, 15, 29, 14, 29, 30, 12, 30, 31, 12, 31, 11, 13, 14, 30, 13, 30, 12], "vertices": [2, 7, 8.41, 11.42, 0.96698, 64, -330.74, 63.9, 0.03302, 2, 7, 28.98, -6.3, 0.96594, 64, -310.17, 46.18, 0.03406, 2, 7, 35.23, -26.63, 0.97011, 64, -303.92, 25.84, 0.02989, 2, 7, 38.14, -34, 0.96629, 64, -301.01, 18.48, 0.03371, 2, 7, 40.51, -40.02, 0.96741, 64, -298.64, 12.46, 0.03259, 2, 7, 39, -45.49, 0.96828, 64, -300.16, 6.99, 0.03172, 2, 7, 32.99, -59.81, 0.97647, 64, -306.16, -7.34, 0.02353, 2, 7, 33.11, -73.8, 0.98163, 64, -306.04, -21.33, 0.01837, 2, 7, 30.99, -81.36, 0.9865, 64, -308.16, -28.88, 0.0135, 2, 7, 13.47, -77.48, 0.9838, 64, -325.68, -25, 0.0162, 2, 7, -2.17, -72.36, 0.98252, 64, -341.32, -19.88, 0.01748, 2, 7, -21.37, -61.62, 0.97998, 64, -360.52, -9.15, 0.02002, 2, 7, -35.59, -50.77, 0.97591, 64, -374.74, 1.71, 0.02409, 2, 7, -39.15, -36.13, 0.97217, 64, -378.3, 16.35, 0.02783, 2, 7, -34.97, -20.01, 0.97305, 64, -374.12, 32.46, 0.02695, 2, 7, -24.05, 2.95, 0.97588, 64, -363.21, 55.42, 0.02412, 2, 7, -8.66, 24.85, 0.97895, 64, -347.81, 77.32, 0.02105, 2, 7, 31.9, -49.49, 0.96874, 64, -307.25, 2.99, 0.03126, 2, 7, 28.68, -44.37, 0.967, 64, -310.47, 8.11, 0.033, 2, 7, 28.27, -39.01, 0.96604, 64, -310.88, 13.46, 0.03396, 2, 7, 31.05, -32.93, 0.96561, 64, -308.1, 19.54, 0.03439, 2, 7, 32.78, -41.9, 0.96616, 64, -306.38, 10.57, 0.03384, 2, 7, 27.13, -22.74, 0.96908, 64, -312.02, 29.74, 0.03092, 2, 7, 20.98, -24.67, 0.96915, 64, -318.17, 27.8, 0.03085, 2, 7, 19.48, -29.56, 0.96972, 64, -319.68, 22.91, 0.03028, 2, 7, 19.79, -38.66, 0.97133, 64, -319.36, 13.82, 0.02867, 2, 7, 20.12, -48.22, 0.97256, 64, -319.04, 4.26, 0.02744, 2, 7, 25.7, -51.85, 0.97164, 64, -313.45, 0.63, 0.02836, 2, 7, -11.69, 0.5, 0.97022, 64, -350.85, 52.97, 0.02978, 2, 7, -23.83, -18.79, 0.97061, 64, -362.98, 33.69, 0.02939, 2, 7, -29.59, -35.56, 0.96962, 64, -368.74, 16.91, 0.03038, 2, 7, -27.02, -49.37, 0.97235, 64, -366.17, 3.1, 0.02765, 2, 7, 14.28, -7.87, 0.96586, 64, -324.88, 44.6, 0.03414, 2, 7, -16.39, -58.56, 0.97633, 64, -355.54, -6.08, 0.02367, 2, 7, 1.23, -67.02, 0.97898, 64, -337.93, -14.54, 0.02102, 2, 7, 14.13, -70.19, 0.98018, 64, -325.02, -17.72, 0.01982, 2, 7, 18.24, -58.78, 0.97587, 64, -320.92, -6.3, 0.02413, 2, 7, 0.87, -15.92, 0.96732, 64, -338.28, 36.56, 0.03268, 2, 7, 3.71, -21.67, 0.96785, 64, -335.45, 30.8, 0.03215, 2, 7, 8.96, -28.63, 0.96821, 64, -330.2, 23.85, 0.03179, 2, 7, 11.88, -35.57, 0.96875, 64, -327.27, 16.9, 0.03125, 2, 7, 9.19, -40.39, 0.9694, 64, -329.96, 12.09, 0.0306, 2, 7, 12.59, -44.99, 0.97036, 64, -326.56, 7.49, 0.02964, 2, 7, 10.78, -49.97, 0.97164, 64, -328.38, 2.51, 0.02836, 2, 7, 7.4, -53.83, 0.97272, 64, -331.75, -1.35, 0.02728, 2, 7, 4.26, -55.12, 0.97312, 64, -334.89, -2.64, 0.02688, 2, 7, 6.86, -59.9, 0.97445, 64, -332.29, -7.43, 0.02555, 2, 7, 1.63, -61.4, 0.97463, 64, -337.52, -8.93, 0.02537, 2, 7, -6.07, -52.42, 0.9721, 64, -345.22, 0.05, 0.0279, 2, 7, -10.89, -47.59, 0.97044, 64, -350.04, 4.89, 0.02956, 2, 7, -12.78, -40.31, 0.96881, 64, -351.93, 12.17, 0.03119, 2, 7, -12.43, -32.23, 0.96802, 64, -351.58, 20.25, 0.03198, 2, 7, -9.36, -24.26, 0.96769, 64, -348.51, 28.22, 0.03231, 2, 7, -2.69, -16.56, 0.96727, 64, -341.85, 35.91, 0.03273, 2, 7, -3.64, -10.92, 0.96681, 64, -342.79, 41.55, 0.03319, 2, 7, 2.81, -13.05, 0.96707, 64, -336.34, 39.43, 0.03293, 2, 7, -0.46, -14.18, 0.96709, 64, -339.61, 38.29, 0.03291, 2, 7, -1.34, -18.76, 0.96755, 64, -340.5, 33.72, 0.03245, 2, 7, -0.61, -23.89, 0.96812, 64, -339.76, 28.59, 0.03188, 2, 7, 0.35, -28.41, 0.96864, 64, -338.8, 24.07, 0.03136, 2, 7, 2.8, -34.53, 0.96939, 64, -336.35, 17.94, 0.03061, 2, 7, 1.55, -40.31, 0.96995, 64, -337.61, 12.16, 0.03005, 2, 7, 2.66, -45.68, 0.97102, 64, -336.49, 6.79, 0.02898, 2, 7, 1.89, -48.56, 0.97166, 64, -337.26, 3.92, 0.02834, 2, 7, -0.54, -53.29, 0.97271, 64, -339.69, -0.82, 0.02729, 2, 7, 0.44, -56.05, 0.97337, 64, -338.71, -3.57, 0.02663, 2, 7, 3.05, -57.91, 0.97384, 64, -336.1, -5.43, 0.02616, 2, 7, 0.28, -45.03, 0.97079, 64, -338.88, 7.45, 0.02921, 2, 7, -0.78, -40.3, 0.96988, 64, -339.93, 12.18, 0.03012, 2, 7, -0.71, -34.61, 0.96926, 64, -339.87, 17.87, 0.03074, 2, 7, -5.13, -32.82, 0.96794, 64, -344.29, 19.66, 0.03206, 2, 7, -5.29, -40.61, 0.96849, 64, -344.44, 11.87, 0.03151, 2, 7, -3.65, -47.37, 0.97017, 64, -342.8, 5.11, 0.02983, 2, 7, -16.03, -35.02, 0.96904, 64, -355.18, 17.45, 0.03096, 2, 7, -14.37, -46.22, 0.97083, 64, -353.53, 6.26, 0.02917], "hull": 17, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 16, 18, 18, 20, 10, 34, 34, 36, 36, 38, 38, 40, 40, 6, 42, 8, 4, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 56, 56, 58, 60, 62, 60, 58, 2, 64, 62, 66, 66, 68, 68, 70, 70, 14, 12, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 126, 134, 134, 136, 136, 138, 138, 118, 128, 96, 140, 142, 142, 144, 146, 148, 20, 22, 22, 24, 0, 32, 2, 0, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 150, "height": 184}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.98794, 0, 0.99601, 0.02482, 0.99776, 0.0569, 0.99644, 0.08334, 0.98449, 0.12617, 0.95421, 0.17143, 0.89914, 0.22011, 0.83438, 0.26185, 0.84337, 0.26928, 0.72969, 0.34563, 0.67778, 0.38039, 0.65013, 0.40221, 0.63581, 0.42327, 0.6126, 0.44265, 0.60011, 0.45624, 0.60001, 0.47811, 0.59519, 0.50701, 0.56657, 0.55352, 0.52133, 0.60106, 0.41004, 0.70046, 0.36984, 0.73887, 0.35124, 0.75862, 0.33994, 0.77092, 0.33158, 0.7863, 0.32927, 0.80322, 0.2988, 0.85954, 0.26996, 0.89126, 0.25666, 0.91008, 0.25542, 0.92148, 0.26647, 0.93984, 0.28801, 0.97376, 0.27852, 0.98917, 0.24712, 1, 0.22143, 0.99999, 0.16726, 0.98958, 0.09787, 0.9648, 0.06768, 0.94622, 0.05629, 0.92886, 0.06634, 0.91021, 0.08281, 0.89209, 0.0872, 0.87187, 0.07772, 0.85356, 0.02386, 0.88411, 0.00116, 0.87653, 0.03809, 0.84494, 0.03927, 0.84022, 0.04446, 0.80756, 0.0712, 0.78347, 0.1212, 0.76949, 0.15364, 0.76548, 0.17604, 0.75897, 0.18821, 0.74629, 0.20257, 0.72976, 0.22446, 0.68066, 0.24275, 0.59085, 0.24976, 0.53655, 0.26777, 0.49776, 0.31421, 0.46129, 0.34896, 0.4373, 0.36702, 0.41835, 0.38142, 0.39993, 0.3921, 0.37925, 0.4015, 0.35584, 0.42493, 0.31062, 0.47495, 0.23473, 0.48671, 0.19659, 0.51152, 0.14359, 0.5328, 0.10939, 0.55614, 0.09088, 0.60076, 0.0734, 0.66166, 0.05674, 0.77144, 0.0322, 0.88306, 0.01381, 0.89237, 0.04968, 0.88082, 0.08611, 0.86581, 0.12355, 0.81306, 0.18853, 0.77374, 0.23034, 0.60587, 0.21305, 0.62691, 0.17713, 0.66973, 0.12227, 0.70475, 0.09101, 0.73596, 0.06115, 0.4767, 0.36025, 0.44509, 0.38743, 0.42941, 0.41096, 0.42628, 0.43558, 0.41374, 0.45911, 0.49995, 0.46732, 0.51249, 0.44379, 0.55011, 0.42026, 0.56892, 0.39783, 0.59087, 0.37047, 0.35707, 0.51565, 0.3367, 0.58076, 0.32259, 0.6809, 0.43075, 0.58952, 0.49031, 0.52276], "triangles": [82, 70, 71, 36, 28, 29, 36, 37, 28, 35, 36, 29, 34, 35, 29, 29, 30, 34, 31, 34, 30, 33, 34, 31, 32, 33, 31, 48, 46, 47, 25, 41, 46, 45, 46, 41, 44, 45, 41, 24, 49, 23, 49, 46, 48, 25, 40, 41, 49, 24, 46, 46, 24, 25, 43, 44, 41, 42, 43, 41, 26, 40, 25, 39, 40, 26, 27, 39, 26, 27, 38, 39, 28, 38, 27, 28, 37, 38, 86, 59, 85, 89, 86, 85, 58, 59, 86, 89, 90, 13, 14, 89, 13, 87, 58, 86, 87, 86, 89, 57, 58, 87, 88, 87, 89, 88, 89, 14, 15, 88, 14, 16, 88, 15, 93, 57, 87, 56, 57, 93, 88, 93, 87, 97, 88, 16, 97, 93, 88, 55, 56, 93, 17, 97, 16, 94, 55, 93, 94, 93, 97, 96, 94, 97, 17, 96, 97, 54, 55, 94, 18, 96, 17, 95, 54, 94, 95, 94, 96, 53, 54, 95, 95, 96, 18, 19, 95, 18, 20, 95, 19, 20, 52, 95, 52, 53, 95, 21, 52, 20, 21, 51, 52, 22, 51, 21, 50, 51, 22, 23, 50, 22, 23, 49, 50, 69, 70, 81, 72, 0, 1, 73, 72, 1, 71, 72, 73, 73, 1, 2, 3, 73, 2, 73, 82, 71, 74, 73, 3, 74, 82, 73, 81, 70, 82, 81, 82, 74, 81, 68, 69, 80, 68, 81, 67, 68, 80, 75, 81, 74, 80, 81, 75, 4, 74, 3, 75, 74, 4, 66, 67, 80, 5, 75, 4, 79, 66, 80, 76, 80, 75, 76, 75, 5, 79, 80, 76, 65, 66, 79, 78, 65, 79, 6, 76, 5, 77, 79, 76, 77, 76, 6, 78, 79, 77, 64, 65, 78, 7, 77, 6, 77, 92, 78, 9, 77, 7, 9, 7, 8, 78, 63, 64, 78, 83, 63, 83, 62, 63, 92, 83, 78, 77, 9, 92, 61, 62, 83, 10, 92, 9, 84, 61, 83, 91, 83, 92, 11, 91, 92, 84, 83, 91, 60, 61, 84, 10, 11, 92, 85, 60, 84, 90, 85, 84, 59, 60, 85, 91, 90, 84, 12, 91, 11, 90, 91, 12, 13, 90, 12, 90, 89, 85], "vertices": [2, 13, -29.23, 33.86, 0.85624, 9, 68.9, 70.03, 0.14376, 2, 13, -6.92, 43.56, 0.97785, 9, 92.73, 74.94, 0.02215, 1, 13, 22.78, 53.28, 1, 1, 13, 47.54, 60.39, 1, 1, 13, 88.62, 68.72, 1, 2, 13, 133.78, 71.79, 0.9899, 68, -197.24, -235.12, 0.0101, 1, 13, 184.6, 67.77, 1, 1, 13, 229.92, 58.62, 1, 1, 13, 235.95, 63.66, 1, 1, 13, 318.38, 48.49, 1, 1, 13, 355.91, 41.53, 1, 2, 13, 378.99, 38.76, 0.9937, 14, -39.69, 40.97, 0.0063, 2, 13, 400.02, 40.11, 0.87635, 14, -18.61, 41.18, 0.12365, 2, 13, 420.39, 38.09, 0.52194, 14, 1.62, 38.06, 0.47806, 2, 13, 434.28, 37.91, 0.19359, 14, 15.48, 37.13, 0.80641, 2, 13, 454.66, 44.11, 0.01377, 14, 36.16, 42.22, 0.98623, 1, 14, 63.89, 47.4, 1, 1, 14, 110.2, 48.86, 1, 1, 14, 158.84, 45.07, 1, 1, 14, 261.93, 31.64, 1, 1, 14, 301.52, 27.37, 1, 2, 14, 321.72, 25.86, 0.9987, 16, 175.33, -27.53, 0.0013, 2, 14, 334.27, 25.01, 0.93769, 16, 162.83, -26.2, 0.06231, 2, 14, 349.49, 25.86, 0.5785, 16, 147.58, -26.47, 0.4215, 2, 14, 365.68, 29.07, 0.15567, 16, 131.28, -29.06, 0.84433, 1, 16, 75.47, -30.09, 1, 2, 16, 43.22, -26.78, 0.99058, 15, 93.12, -46.3, 0.00942, 2, 16, 24.36, -26.08, 0.86254, 15, 77.84, -35.22, 0.13746, 2, 16, 13.4, -27.93, 0.62534, 15, 67.71, -30.66, 0.37466, 2, 16, -3.35, -35.25, 0.16816, 15, 49.72, -27.42, 0.83184, 1, 15, 16.35, -21.79, 1, 1, 15, 3.66, -13.15, 1, 1, 15, -2.1, 0.7, 1, 1, 15, 1.19, 8.79, 1, 1, 15, 17.5, 22.05, 1, 2, 16, -38.82, 15.92, 0.03988, 15, 48.74, 34.84, 0.96012, 2, 16, -23.19, 29.66, 0.27798, 15, 69.37, 37.55, 0.72202, 2, 16, -7.42, 36.89, 0.57452, 15, 86.5, 34.78, 0.42548, 2, 16, 11.06, 37.25, 0.87028, 15, 102.05, 24.78, 0.12972, 2, 16, 29.48, 35.36, 0.99674, 15, 116.3, 12.96, 0.00326, 1, 16, 49.06, 37.91, 1, 1, 16, 65.87, 44.69, 1, 1, 16, 33.01, 56.56, 1, 1, 16, 38.66, 65.62, 1, 2, 14, 428.94, -57.25, 0, 16, 71.34, 59.59, 1, 2, 14, 424.38, -57.97, 0.00047, 16, 75.93, 60.14, 0.99953, 2, 14, 393.09, -63.91, 0.03427, 16, 107.42, 64.89, 0.96573, 2, 14, 368.12, -60.73, 0.13663, 16, 132.25, 60.76, 0.86337, 2, 14, 350.82, -47.5, 0.31739, 16, 149.04, 46.89, 0.68261, 2, 14, 344.37, -37.74, 0.51258, 16, 155.12, 36.89, 0.48742, 2, 14, 336.38, -31.87, 0.76238, 16, 162.87, 30.72, 0.23762, 2, 14, 323.39, -30.83, 0.94802, 16, 175.81, 29.19, 0.05198, 1, 14, 306.6, -29.96, 1, 1, 14, 258.39, -34.25, 1, 1, 14, 171.98, -49.26, 1, 1, 14, 120.07, -59.67, 1, 1, 14, 81.92, -62.82, 1, 2, 13, 467.46, -53.59, 0.01361, 14, 43.65, -56.04, 0.98639, 2, 13, 441.66, -49.14, 0.1696, 14, 18.13, -50.19, 0.8304, 2, 13, 422.21, -48.67, 0.47795, 14, -1.27, -48.68, 0.52205, 2, 13, 403.61, -49.25, 0.79126, 14, -19.87, -48.24, 0.20874, 2, 13, 383.3, -51.67, 0.96245, 14, -40.28, -49.56, 0.03755, 2, 13, 360.55, -55.3, 0.99963, 14, -63.19, -51.95, 0.00037, 1, 13, 316.11, -60.58, 1, 1, 13, 240.46, -65.96, 1, 2, 13, 203.76, -73.02, 0.94286, 3, 288.59, -8.38, 0.05714, 2, 13, 151.94, -80.07, 0.82571, 3, 236.42, -4.64, 0.17429, 2, 13, 117.97, -82.9, 0.62286, 3, 202.59, -0.43, 0.37714, 3, 13, 98.41, -80.59, 0.51355, 3, 183.92, 5.85, 0.48502, 68, -332.58, -156.67, 0.00143, 3, 13, 77.68, -71.08, 0.39075, 3, 165.6, 19.42, 0.60028, 68, -317.41, -139.64, 0.00897, 3, 13, 56.11, -56.03, 0.24585, 3, 147.57, 38.58, 0.74089, 68, -296.7, -123.42, 0.01326, 3, 13, 22.32, -27.34, 0.4214, 3, 120.4, 73.6, 0.55963, 68, -259.38, -99.51, 0.01897, 3, 13, -5.92, 3.7, 0.9588, 9, 85.51, 35.73, 0.02172, 68, -221.43, -81.61, 0.01949, 2, 13, 26.56, 16.96, 0.98185, 68, -218.26, -116.54, 0.01815, 2, 13, 61.63, 23.59, 0.98144, 68, -222.19, -152.02, 0.01856, 2, 13, 97.99, 29.39, 0.9799, 68, -227.3, -188.49, 0.0201, 2, 13, 163.76, 30.78, 0.97741, 68, -245.23, -251.78, 0.02259, 2, 13, 206.61, 29.92, 0.98031, 68, -258.6, -292.5, 0.01969, 2, 13, 207.22, -29.58, 0.97836, 68, -315.67, -275.66, 0.02164, 2, 13, 171.68, -32.99, 0.97722, 68, -308.52, -240.68, 0.02278, 2, 13, 116.32, -34.72, 0.97835, 68, -293.96, -187.24, 0.02165, 3, 13, 83.72, -32.25, 0.87626, 3, 179.49, 56.18, 0.10358, 68, -282.05, -156.79, 0.02015, 3, 13, 52.81, -30.62, 0.72393, 3, 149.57, 64.13, 0.25814, 68, -271.44, -127.72, 0.01793, 2, 13, 357.18, -29.59, 0.98026, 68, -359.59, -419.04, 0.01974, 3, 13, 385.64, -32.11, 0.94863, 14, -36.89, -30.16, 0.03327, 68, -370.34, -445.51, 0.0181, 3, 13, 409.11, -30.5, 0.7218, 14, -13.36, -29.82, 0.26037, 68, -375.67, -468.43, 0.01783, 3, 13, 432.35, -24.49, 0.22854, 14, 10.17, -25.08, 0.75296, 68, -376.74, -492.41, 0.0185, 3, 13, 455.51, -21.86, 0.01564, 14, 33.44, -23.71, 0.96764, 68, -381, -515.33, 0.01671, 2, 14, 34.15, 6.67, 0.98136, 68, -351.69, -523.32, 0.01864, 3, 13, 431.41, 5.87, 0.03285, 14, 10.88, 5.29, 0.94717, 68, -347.42, -500.4, 0.01998, 3, 13, 405.76, 11.39, 0.90783, 14, -14.44, 12.19, 0.07417, 68, -334.63, -477.49, 0.01799, 2, 13, 382.99, 11.11, 0.98162, 68, -328.24, -455.64, 0.01838, 2, 13, 355.32, 10.44, 0.98217, 68, -320.77, -428.99, 0.01783, 2, 14, 91.53, -29.16, 0.98501, 68, -400.26, -570.39, 0.01499, 2, 14, 154.76, -20.62, 0.99218, 68, -407.19, -633.81, 0.00782, 1, 14, 250.58, -1.81, 1, 2, 14, 155.34, 12.47, 0.99131, 68, -375.22, -642.34, 0.00869, 2, 14, 87.35, 16.48, 0.9845, 68, -354.96, -577.32, 0.0155], "hull": 73, "edges": [0, 2, 22, 24, 32, 34, 34, 36, 42, 44, 54, 56, 60, 62, 62, 64, 64, 66, 66, 68, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 98, 100, 110, 112, 118, 120, 126, 128, 128, 130, 2, 4, 4, 6, 6, 8, 8, 10, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 0, 130, 132, 132, 134, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 30, 32, 112, 114, 114, 116, 116, 118, 120, 122, 122, 124, 124, 126, 18, 20, 20, 22, 94, 96, 96, 98, 48, 50, 44, 46, 46, 48, 104, 106, 100, 102, 102, 104, 36, 38, 38, 40, 40, 42, 106, 108, 108, 110, 56, 58, 58, 60, 76, 78, 78, 80, 72, 74, 74, 76, 68, 70, 70, 72, 50, 52, 52, 54, 90, 92, 92, 94, 152, 154, 156, 158, 158, 160, 160, 162, 152, 150, 150, 148, 156, 166, 166, 168, 168, 170, 170, 172, 172, 174, 176, 178, 178, 180, 180, 182, 182, 184, 184, 154, 174, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 176], "width": 340, "height": 974}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": -13.44, "curve": [0.444, -13.44, 0.889, 13.87, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 13.88, "curve": [1.778, 13.88, 2.222, -13.44, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -13.44, "curve": [3.111, -13.45, 3.556, 13.87, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 13.88, "curve": [4.444, 13.88, 4.889, -13.43, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -13.44, "curve": [5.667, -13.45, 6, 19.34, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 19.34, "curve": [6.778, 19.35, 7.222, 2.95, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 2.95, "curve": [8.111, 2.94, 8.556, 19.33, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 19.34, "curve": [9.444, 19.35, 9.889, -13.44, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -13.44, "curve": [10.778, -13.45, 11.222, 13.87, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 13.88, "curve": [12.111, 13.88, 12.556, -13.44, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -13.44}]}, "ALL2": {"translate": [{"y": -31.27, "curve": [0.444, 0, 0.889, 0, 0.444, -31.27, 0.889, -0.01]}, {"time": 1.3333, "curve": [1.778, 0, 2.222, 0, 1.778, 0.01, 2.222, -31.26]}, {"time": 2.6667, "y": -31.27, "curve": [3.111, 0, 3.556, 0, 3.111, -31.27, 3.556, -0.01]}, {"time": 4, "curve": [4.444, 0, 4.889, 0, 4.444, 0.01, 4.889, -31.26]}, {"time": 5.3333, "y": -31.27, "curve": [5.667, 0, 6, 0, 5.667, -31.28, 6, 3.03]}, {"time": 6.3333, "y": 3.03, "curve": [6.778, 0, 7.222, 0, 6.778, 3.03, 7.222, -14.11]}, {"time": 7.6667, "y": -14.12, "curve": [8.111, 0, 8.556, 0, 8.111, -14.12, 8.556, 3.02]}, {"time": 9, "y": 3.03, "curve": [9.444, 0, 9.889, 0, 9.444, 3.04, 9.889, -31.26]}, {"time": 10.3333, "y": -31.27, "curve": [10.778, 0, 11.222, 0, 10.778, -31.27, 11.222, -0.01]}, {"time": 11.6667, "curve": [12.111, 0, 12.556, 0, 12.111, 0.01, 12.556, -31.27]}, {"time": 13, "y": -31.27}]}, "tun": {"rotate": [{"value": 1.33, "curve": [0.444, 1.33, 0.889, -1.32]}, {"time": 1.3333, "value": -1.32, "curve": [1.778, -1.32, 2.222, 1.33]}, {"time": 2.6667, "value": 1.33, "curve": [3.111, 1.33, 3.556, -1.32]}, {"time": 4, "value": -1.32, "curve": [4.444, -1.32, 4.889, 1.33]}, {"time": 5.3333, "value": 1.33, "curve": [5.667, 1.33, 6, 3.65]}, {"time": 6.3333, "value": 3.65, "curve": [6.778, 3.66, 7.222, 2.49]}, {"time": 7.6667, "value": 2.49, "curve": [8.111, 2.49, 8.556, 3.65]}, {"time": 9, "value": 3.65, "curve": [9.444, 3.66, 9.889, 1.33]}, {"time": 10.3333, "value": 1.33, "curve": [10.778, 1.33, 11.222, -1.32]}, {"time": 11.6667, "value": -1.32, "curve": [12.111, -1.32, 12.556, 1.33]}, {"time": 13, "value": 1.33}]}, "body": {"rotate": [{"value": -2.41, "curve": [0.444, -2.41, 0.889, 1.67]}, {"time": 1.3333, "value": 1.67, "curve": [1.778, 1.68, 2.222, -2.41]}, {"time": 2.6667, "value": -2.41, "curve": [3.111, -2.41, 3.556, 1.67]}, {"time": 4, "value": 1.67, "curve": [4.444, 1.68, 4.889, -2.41]}, {"time": 5.3333, "value": -2.41, "curve": [5.667, -2.41, 6, -0.68]}, {"time": 6.3333, "value": -0.68, "curve": [6.778, -0.68, 7.222, -1.55]}, {"time": 7.6667, "value": -1.55, "curve": [8.111, -1.55, 8.556, -0.68]}, {"time": 9, "value": -0.68, "curve": [9.444, -0.68, 9.889, -2.41]}, {"time": 10.3333, "value": -2.41, "curve": [10.778, -2.41, 11.222, 1.67]}, {"time": 11.6667, "value": 1.67, "curve": [12.111, 1.68, 12.556, -2.41]}, {"time": 13, "value": -2.41}], "translate": [{"y": -5.11, "curve": [0.057, 0, 0.112, 0, 0.057, -5.33, 0.112, -5.5]}, {"time": 0.1667, "y": -5.5, "curve": [0.611, 0, 1.056, 0, 0.611, -5.5, 1.056, 2.75]}, {"time": 1.5, "y": 2.75, "curve": [1.944, 0, 2.389, 0, 1.944, 2.75, 2.389, -5.5]}, {"time": 2.8333, "y": -5.5, "curve": [3.278, 0, 3.722, 0, 3.278, -5.5, 3.722, 2.75]}, {"time": 4.1667, "y": 2.75, "curve": [4.611, 0, 5.056, 0, 4.611, 2.75, 5.056, -5.5]}, {"time": 5.5, "y": -5.5, "curve": [5.833, 0, 6.167, 0, 5.833, -5.5, 6.167, 2.75]}, {"time": 6.5, "y": 2.75, "curve": [6.944, 0, 7.389, 0, 6.944, 2.75, 7.389, -1.37]}, {"time": 7.8333, "y": -1.38, "curve": [8.278, 0, 8.722, 0, 8.278, -1.38, 8.722, 2.75]}, {"time": 9.1667, "y": 2.75, "curve": [9.611, 0, 10.056, 0, 9.611, 2.75, 10.056, -5.5]}, {"time": 10.5, "y": -5.5, "curve": [10.944, 0, 11.389, 0, 10.944, -5.5, 11.389, 2.75]}, {"time": 11.8333, "y": 2.75, "curve": [12.223, 0, 12.613, 0, 12.223, 2.75, 12.613, -3.55]}, {"time": 13, "y": -5.11}], "scale": [{"y": 1.042, "curve": [0.057, 1, 0.112, 1, 0.057, 1.044, 0.112, 1.046]}, {"time": 0.1667, "y": 1.046, "curve": [0.611, 1, 1.056, 1, 0.611, 1.046, 1.056, 0.963]}, {"time": 1.5, "y": 0.963, "curve": [1.944, 1, 2.389, 1, 1.944, 0.963, 2.389, 1.046]}, {"time": 2.8333, "y": 1.046, "curve": [3.278, 1, 3.722, 1, 3.278, 1.046, 3.722, 0.963]}, {"time": 4.1667, "y": 0.963, "curve": [4.611, 1, 5.056, 1, 4.611, 0.963, 5.056, 1.046]}, {"time": 5.5, "y": 1.046, "curve": [5.833, 1, 6.167, 1, 5.833, 1.046, 6.167, 0.963]}, {"time": 6.5, "y": 0.963, "curve": [6.944, 1, 7.389, 1, 6.944, 0.963, 7.389, 1.005]}, {"time": 7.8333, "y": 1.005, "curve": [8.278, 1, 8.722, 1, 8.278, 1.005, 8.722, 0.963]}, {"time": 9.1667, "y": 0.963, "curve": [9.611, 1, 10.056, 1, 9.611, 0.963, 10.056, 1.046]}, {"time": 10.5, "y": 1.046, "curve": [10.944, 1, 11.389, 1, 10.944, 1.046, 11.389, 0.963]}, {"time": 11.8333, "y": 0.963, "curve": [12.223, 1, 12.613, 1, 12.223, 0.963, 12.613, 1.026]}, {"time": 13, "y": 1.042}]}, "body2": {"rotate": [{"value": -2.22, "curve": [0.057, -2.33, 0.112, -2.41]}, {"time": 0.1667, "value": -2.41, "curve": [0.611, -2.42, 1.056, 1.69]}, {"time": 1.5, "value": 1.69, "curve": [1.944, 1.69, 2.389, -2.41]}, {"time": 2.8333, "value": -2.41, "curve": [3.278, -2.42, 3.722, 1.69]}, {"time": 4.1667, "value": 1.69, "curve": [4.611, 1.69, 5.056, -2.42]}, {"time": 5.5, "value": -2.41, "curve": [5.833, -2.41, 6.167, -14.05]}, {"time": 6.5, "value": -14.05, "curve": [6.944, -14.05, 7.389, -9.34]}, {"time": 7.8333, "value": -9.34, "curve": [8.278, -9.34, 8.722, -14.05]}, {"time": 9.1667, "value": -14.05, "curve": [9.611, -14.05, 10.056, -2.41]}, {"time": 10.5, "value": -2.41, "curve": [10.944, -2.42, 11.389, 1.69]}, {"time": 11.8333, "value": 1.69, "curve": [12.223, 1.69, 12.613, -1.44]}, {"time": 13, "value": -2.22}], "translate": [{"x": -3.58, "curve": [0.114, -4.27, 0.224, -4.78, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -4.78, "curve": [0.778, -4.78, 1.222, 2.73, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 2.73, "curve": [2.111, 2.74, 2.556, -4.78, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -4.78, "curve": [3.444, -4.78, 3.889, 2.73, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 2.73, "curve": [4.778, 2.74, 5.222, -4.78, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -4.78, "curve": [6, -4.78, 6.333, 2.73, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 2.73, "curve": [7.111, 2.73, 7.556, -1.02, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.02, "curve": [8.444, -1.03, 8.889, 2.73, 8.444, 0, 9, 0]}, {"time": 9.3333, "x": 2.73, "curve": [9.778, 2.74, 10.222, -4.78, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -4.78, "curve": [11.111, -4.78, 11.556, 2.73, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 2.73, "curve": [12.335, 2.74, 12.67, -1.47, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.58}], "scale": [{"x": 1.002, "y": 1.002, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.015, 0.778, 1, 1.222, 1.015]}, {"time": 1.6667, "x": 1.015, "y": 1.015, "curve": [2.111, 1.015, 2.556, 1, 2.111, 1.015, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.015, 3.444, 1, 3.889, 1.015]}, {"time": 4.3333, "x": 1.015, "y": 1.015, "curve": [4.778, 1.015, 5.222, 1, 4.778, 1.015, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.015, 6, 1, 6.333, 1.015]}, {"time": 6.6667, "x": 1.015, "y": 1.015, "curve": [7.111, 1.015, 7.556, 1.008, 7.111, 1.015, 7.556, 1.008]}, {"time": 8, "x": 1.008, "y": 1.008, "curve": [8.444, 1.008, 8.889, 1.015, 8.444, 1.008, 8.889, 1.015]}, {"time": 9.3333, "x": 1.015, "y": 1.015, "curve": [9.778, 1.015, 10.222, 1, 9.778, 1.015, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.015, 11.111, 1, 11.556, 1.015]}, {"time": 12, "x": 1.015, "y": 1.015, "curve": [12.335, 1.015, 12.67, 1.007, 12.335, 1.015, 12.67, 1.007]}, {"time": 13, "x": 1.002, "y": 1.002}]}, "neck": {"rotate": [{"value": 0.91, "curve": [0.114, 1.22, 0.224, 1.45]}, {"time": 0.3333, "value": 1.45, "curve": [0.778, 1.45, 1.222, -1.93]}, {"time": 1.6667, "value": -1.93, "curve": [2.111, -1.93, 2.556, 1.45]}, {"time": 3, "value": 1.45, "curve": [3.444, 1.45, 3.889, -1.93]}, {"time": 4.3333, "value": -1.93, "curve": [4.778, -1.93, 5.222, 1.45]}, {"time": 5.6667, "value": 1.45, "curve": [6, 1.45, 6.333, 6.02]}, {"time": 6.6667, "value": 6.02, "curve": [7.111, 6.03, 7.556, 3.74]}, {"time": 8, "value": 3.74, "curve": [8.444, 3.74, 8.889, 6.02]}, {"time": 9.3333, "value": 6.02, "curve": [9.778, 6.03, 10.222, 1.45]}, {"time": 10.6667, "value": 1.45, "curve": [11.111, 1.45, 11.556, -1.93]}, {"time": 12, "value": -1.93, "curve": [12.335, -1.93, 12.67, -0.04]}, {"time": 13, "value": 0.91}]}, "head": {"rotate": [{"value": 0.37, "curve": [0.168, 0.96, 0.334, 1.45]}, {"time": 0.5, "value": 1.45, "curve": [0.944, 1.45, 1.389, -1.93]}, {"time": 1.8333, "value": -1.93, "curve": [2.278, -1.93, 2.722, 1.45]}, {"time": 3.1667, "value": 1.45, "curve": [3.611, 1.45, 4.056, -1.93]}, {"time": 4.5, "value": -1.93, "curve": [4.944, -1.93, 5.389, 1.45]}, {"time": 5.8333, "value": 1.45, "curve": [6.167, 1.45, 6.5, 6.02]}, {"time": 6.8333, "value": 6.03, "curve": [7.278, 6.03, 7.722, 3.74]}, {"time": 8.1667, "value": 3.74, "curve": [8.611, 3.74, 9.056, 6.02]}, {"time": 9.5, "value": 6.03, "curve": [9.944, 6.03, 10.389, 1.45]}, {"time": 10.8333, "value": 1.45, "curve": [11.278, 1.45, 11.722, -1.93]}, {"time": 12.1667, "value": -1.93, "curve": [12.445, -1.93, 12.724, -0.62]}, {"time": 13, "value": 0.37}]}, "leg_R2": {"rotate": [{"value": 0.01}]}, "leg_R3": {"rotate": [{"value": 0.02}]}, "leg_L3": {"rotate": [{}]}, "foot_L": {"rotate": [{"value": -2.7, "curve": [0.444, -2.7, 0.889, 1.89]}, {"time": 1.3333, "value": 1.89, "curve": [1.778, 1.89, 2.222, -2.7]}, {"time": 2.6667, "value": -2.7, "curve": [3.111, -2.7, 3.556, 1.89]}, {"time": 4, "value": 1.89, "curve": [4.444, 1.89, 4.889, -2.7]}, {"time": 5.3333, "value": -2.7, "curve": [5.667, -2.7, 6, -5.11]}, {"time": 6.3333, "value": -5.11, "curve": [6.778, -5.11, 7.222, -3.91]}, {"time": 7.6667, "value": -3.91, "curve": [8.111, -3.91, 8.556, -5.11]}, {"time": 9, "value": -5.11, "curve": [9.444, -5.11, 9.889, -2.7]}, {"time": 10.3333, "value": -2.7, "curve": [10.778, -2.7, 11.222, 1.89]}, {"time": 11.6667, "value": 1.89, "curve": [12.111, 1.89, 12.556, -2.7]}, {"time": 13, "value": -2.7}]}, "foot_L2": {"rotate": [{"value": -4.09, "curve": [0.444, -4.09, 0.889, 3.23]}, {"time": 1.3333, "value": 3.24, "curve": [1.778, 3.24, 2.222, -4.09]}, {"time": 2.6667, "value": -4.09, "curve": [3.111, -4.09, 3.556, 3.23]}, {"time": 4, "value": 3.24, "curve": [4.444, 3.24, 4.889, -4.09]}, {"time": 5.3333, "value": -4.09, "curve": [5.667, -4.09, 6, 0.43]}, {"time": 6.3333, "value": 0.43, "curve": [6.778, 0.43, 7.222, -1.83]}, {"time": 7.6667, "value": -1.83, "curve": [8.111, -1.83, 8.556, 0.43]}, {"time": 9, "value": 0.43, "curve": [9.444, 0.43, 9.889, -4.09]}, {"time": 10.3333, "value": -4.09, "curve": [10.778, -4.09, 11.222, 3.23]}, {"time": 11.6667, "value": 3.24, "curve": [12.111, 3.24, 12.556, -4.09]}, {"time": 13, "value": -4.09}]}, "sh_L": {"translate": [{"x": -4.02, "curve": [0.114, -4.91, 0.224, -5.57, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.57, "curve": [0.778, -5.57, 1.222, 4.1, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 4.11, "curve": [2.111, 4.11, 2.556, -5.57, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.57, "curve": [3.444, -5.57, 3.889, 4.1, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 4.11, "curve": [4.778, 4.11, 5.222, -5.57, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.57, "curve": [6, -5.57, 6.333, 4.1, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 4.11, "curve": [7.111, 4.11, 7.556, -0.73, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -0.73, "curve": [8.444, -0.73, 8.889, 4.1, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 4.11, "curve": [9.778, 4.11, 10.222, -5.57, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.57, "curve": [11.111, -5.57, 11.556, 4.1, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 4.11, "curve": [12.335, 4.11, 12.67, -1.31, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.02}]}, "sh_R": {"translate": [{"x": -4.02, "curve": [0.114, -4.91, 0.224, -5.57, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.57, "curve": [0.778, -5.57, 1.222, 4.1, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 4.11, "curve": [2.111, 4.11, 2.556, -5.57, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.57, "curve": [3.444, -5.57, 3.889, 4.1, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 4.11, "curve": [4.778, 4.11, 5.222, -5.57, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.57, "curve": [6, -5.57, 6.333, 4.1, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 4.11, "curve": [7.111, 4.11, 7.556, -0.73, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -0.73, "curve": [8.444, -0.73, 8.889, 4.1, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 4.11, "curve": [9.778, 4.11, 10.222, -5.57, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.57, "curve": [11.111, -5.57, 11.556, 4.1, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 4.11, "curve": [12.335, 4.11, 12.67, -1.31, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.02}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{"value": -0.04}]}, "arm_R": {"rotate": [{"value": -0.01}]}, "arm_R2": {"rotate": [{"value": -0.02}]}, "weapon": {"rotate": [{"time": 4.6667, "curve": [5.111, 0, 5.556, 0]}, {"time": 6, "curve": [6.333, 0, 6.667, -3.26]}, {"time": 7, "value": -3.26, "curve": [7.444, -3.27, 7.889, 0.15]}, {"time": 8.3333, "value": 0.15, "curve": [8.778, 0.15, 9.222, -3.26]}, {"time": 9.6667, "value": -3.26, "curve": [10.111, -3.27, 10.556, 0]}, {"time": 11}], "translate": [{"x": -36.19, "y": 4.67, "curve": [0.114, -40.14, 0.224, -43.08, 0.114, 5.18, 0.224, 5.56]}, {"time": 0.3333, "x": -43.08, "y": 5.56, "curve": [0.778, -43.08, 1.222, -0.01, 0.778, 5.56, 1.222, 0]}, {"time": 1.6667, "curve": [2.111, 0.01, 2.556, -43.07, 2.111, 0, 2.556, 5.56]}, {"time": 3, "x": -43.08, "y": 5.56, "curve": [3.444, -43.09, 3.889, -0.01, 3.444, 5.56, 3.889, 0]}, {"time": 4.3333, "curve": [4.778, 0.01, 5.222, -43.06, 4.778, 0, 5.222, 5.59]}, {"time": 5.6667, "x": -43.08, "y": 5.56, "curve": [6, -43.1, 6.333, 33.41, 6, 5.54, 6.333, 90.22]}, {"time": 6.6667, "x": 33.41, "y": 90.23, "curve": [7.111, 33.42, 7.556, 4.27, 7.111, 90.24, 7.556, 47.9]}, {"time": 8, "x": 4.27, "y": 47.89, "curve": [8.444, 4.26, 8.889, 33.39, 8.444, 47.88, 8.889, 90.21]}, {"time": 9.3333, "x": 33.41, "y": 90.23, "curve": [9.778, 33.43, 10.222, -43.07, 9.778, 90.25, 10.222, 5.56]}, {"time": 10.6667, "x": -43.08, "y": 5.56, "curve": [11.111, -43.09, 11.556, -0.01, 11.111, 5.56, 11.556, 0]}, {"time": 12, "curve": [12.335, 0.01, 12.67, -24.12, 12.335, 0, 12.67, 3.11]}, {"time": 13, "x": -36.19, "y": 4.67}]}, "RU_R": {"translate": [{"x": -14.76, "curve": [0.168, -26.49, 0.334, -36.12, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -36.12, "curve": [0.944, -36.12, 1.389, 31, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 31.02, "curve": [2.278, 31.03, 2.722, -36.1, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -36.12, "curve": [3.611, -36.13, 4.056, 31, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 31.02, "curve": [4.944, 31.03, 5.389, -36.09, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -36.12, "curve": [6.167, -36.13, 6.5, 31, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 31.02, "curve": [7.278, 31.03, 7.722, -36.1, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -36.12, "curve": [8.611, -36.13, 9.056, 31, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 31.02, "curve": [9.944, 31.03, 10.389, -36.1, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -36.12, "curve": [11.278, -36.13, 11.722, 31, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 31.02, "curve": [12.445, 31.03, 12.724, 4.93, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -14.76}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.167, 1.006, 0.333, 0.924, 0.167, 0.984, 0.333, 1.028]}, {"time": 0.5, "x": 0.924, "y": 1.028, "curve": [0.722, 0.924, 0.944, 1.072, 0.722, 1.028, 0.944, 0.949]}, {"time": 1.1667, "x": 1.072, "y": 0.949, "curve": [1.389, 1.072, 1.611, 0.924, 1.389, 0.949, 1.611, 1.028]}, {"time": 1.8333, "x": 0.924, "y": 1.028, "curve": [2.056, 0.924, 2.278, 1.072, 2.056, 1.028, 2.278, 0.949]}, {"time": 2.5, "x": 1.072, "y": 0.949, "curve": [2.722, 1.072, 2.944, 0.924, 2.722, 0.949, 2.944, 1.028]}, {"time": 3.1667, "x": 0.924, "y": 1.028, "curve": [3.389, 0.924, 3.611, 1.072, 3.389, 1.028, 3.611, 0.949]}, {"time": 3.8333, "x": 1.072, "y": 0.949, "curve": [4.056, 1.072, 4.278, 0.924, 4.056, 0.949, 4.278, 1.028]}, {"time": 4.5, "x": 0.924, "y": 1.028, "curve": [4.722, 0.924, 4.944, 1.072, 4.722, 1.028, 4.944, 0.949]}, {"time": 5.1667, "x": 1.072, "y": 0.949, "curve": [5.389, 1.072, 5.611, 0.924, 5.389, 0.949, 5.611, 1.028]}, {"time": 5.8333, "x": 0.924, "y": 1.028, "curve": [6, 0.924, 6.167, 1.072, 6, 1.028, 6.167, 0.949]}, {"time": 6.3333, "x": 1.072, "y": 0.949, "curve": [6.5, 1.072, 6.667, 0.924, 6.5, 0.949, 6.667, 1.028]}, {"time": 6.8333, "x": 0.924, "y": 1.028, "curve": [7.056, 0.924, 7.278, 1.072, 7.056, 1.028, 7.278, 0.949]}, {"time": 7.5, "x": 1.072, "y": 0.949, "curve": [7.722, 1.072, 7.944, 0.924, 7.722, 0.949, 7.944, 1.028]}, {"time": 8.1667, "x": 0.924, "y": 1.028, "curve": [8.389, 0.924, 8.611, 1.072, 8.389, 1.028, 8.611, 0.949]}, {"time": 8.8333, "x": 1.072, "y": 0.949, "curve": [9.056, 1.072, 9.278, 0.924, 9.056, 0.949, 9.278, 1.028]}, {"time": 9.5, "x": 0.924, "y": 1.028, "curve": [9.722, 0.924, 9.944, 1.072, 9.722, 1.028, 9.944, 0.949]}, {"time": 10.1667, "x": 1.072, "y": 0.949, "curve": [10.389, 1.072, 10.611, 0.924, 10.389, 0.949, 10.611, 1.028]}, {"time": 10.8333, "x": 0.924, "y": 1.028, "curve": [11.056, 0.924, 11.278, 1.072, 11.056, 1.028, 11.278, 0.949]}, {"time": 11.5, "x": 1.072, "y": 0.949, "curve": [11.722, 1.072, 11.944, 0.924, 11.722, 0.949, 11.944, 1.028]}, {"time": 12.1667, "x": 0.924, "y": 1.028, "curve": [12.389, 0.924, 12.611, 1.072, 12.389, 1.028, 12.611, 0.949]}, {"time": 12.8333, "x": 1.072, "y": 0.949, "curve": [12.889, 1.072, 12.944, 1.062, 12.889, 0.949, 12.944, 0.954]}, {"time": 13, "x": 1.048, "y": 0.962}]}, "RU_R2": {"translate": [{"x": 3.38, "curve": [0.225, -10.55, 0.446, -24.67, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -24.67, "curve": [1.111, -24.67, 1.556, 31.41, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 31.42, "curve": [2.444, 31.44, 2.889, -24.65, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -24.67, "curve": [3.778, -24.68, 4.222, 31.41, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 31.42, "curve": [5.111, 31.44, 5.556, -24.65, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -24.67, "curve": [6.333, -24.68, 6.667, 31.41, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 31.42, "curve": [7.444, 31.44, 7.889, -24.65, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -24.67, "curve": [8.778, -24.68, 9.222, 31.41, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 31.42, "curve": [10.111, 31.44, 10.556, -24.65, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -24.67, "curve": [11.444, -24.68, 11.889, 31.41, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 31.42, "curve": [12.557, 31.43, 12.781, 17.5, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 3.38}], "scale": [{"x": 1.072, "y": 0.949, "curve": [0.222, 1.072, 0.444, 0.924, 0.222, 0.949, 0.444, 1.028]}, {"time": 0.6667, "x": 0.924, "y": 1.028, "curve": [0.889, 0.924, 1.111, 1.072, 0.889, 1.028, 1.111, 0.949]}, {"time": 1.3333, "x": 1.072, "y": 0.949, "curve": [1.556, 1.072, 1.778, 0.924, 1.556, 0.949, 1.778, 1.028]}, {"time": 2, "x": 0.924, "y": 1.028, "curve": [2.222, 0.924, 2.444, 1.072, 2.222, 1.028, 2.444, 0.949]}, {"time": 2.6667, "x": 1.072, "y": 0.949, "curve": [2.889, 1.072, 3.111, 0.924, 2.889, 0.949, 3.111, 1.028]}, {"time": 3.3333, "x": 0.924, "y": 1.028, "curve": [3.556, 0.924, 3.778, 1.072, 3.556, 1.028, 3.778, 0.949]}, {"time": 4, "x": 1.072, "y": 0.949, "curve": [4.222, 1.072, 4.444, 0.924, 4.222, 0.949, 4.444, 1.028]}, {"time": 4.6667, "x": 0.924, "y": 1.028, "curve": [4.889, 0.924, 5.111, 1.072, 4.889, 1.028, 5.111, 0.949]}, {"time": 5.3333, "x": 1.072, "y": 0.949, "curve": [5.556, 1.072, 5.778, 0.924, 5.556, 0.949, 5.778, 1.028]}, {"time": 6, "x": 0.924, "y": 1.028, "curve": [6.167, 0.924, 6.333, 1.072, 6.167, 1.028, 6.333, 0.949]}, {"time": 6.5, "x": 1.072, "y": 0.949, "curve": [6.667, 1.072, 6.833, 0.924, 6.667, 0.949, 6.833, 1.028]}, {"time": 7, "x": 0.924, "y": 1.028, "curve": [7.222, 0.924, 7.444, 1.072, 7.222, 1.028, 7.444, 0.949]}, {"time": 7.6667, "x": 1.072, "y": 0.949, "curve": [7.889, 1.072, 8.111, 0.924, 7.889, 0.949, 8.111, 1.028]}, {"time": 8.3333, "x": 0.924, "y": 1.028, "curve": [8.556, 0.924, 8.778, 1.072, 8.556, 1.028, 8.778, 0.949]}, {"time": 9, "x": 1.072, "y": 0.949, "curve": [9.222, 1.072, 9.444, 0.924, 9.222, 0.949, 9.444, 1.028]}, {"time": 9.6667, "x": 0.924, "y": 1.028, "curve": [9.889, 0.924, 10.111, 1.072, 9.889, 1.028, 10.111, 0.949]}, {"time": 10.3333, "x": 1.072, "y": 0.949, "curve": [10.556, 1.072, 10.778, 0.924, 10.556, 0.949, 10.778, 1.028]}, {"time": 11, "x": 0.924, "y": 1.028, "curve": [11.222, 0.924, 11.444, 1.072, 11.222, 1.028, 11.444, 0.949]}, {"time": 11.6667, "x": 1.072, "y": 0.949, "curve": [11.889, 1.072, 12.111, 0.924, 11.889, 0.949, 12.111, 1.028]}, {"time": 12.3333, "x": 0.924, "y": 1.028, "curve": [12.556, 0.924, 12.778, 1.072, 12.556, 1.028, 12.778, 0.949]}, {"time": 13, "x": 1.072, "y": 0.949}]}, "RU_R3": {"translate": [{"x": 17.16, "curve": [0.279, -0.04, 0.556, -22.93, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -22.93, "curve": [1.278, -22.93, 1.722, 35.83, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 35.85, "curve": [2.611, 35.86, 3.056, -22.91, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -22.93, "curve": [3.944, -22.94, 4.389, 35.83, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 35.85, "curve": [5.278, 35.86, 5.722, -22.91, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -22.93, "curve": [6.5, -22.94, 6.833, 35.84, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 35.85, "curve": [7.611, 35.86, 8.056, -22.91, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -22.93, "curve": [8.944, -22.94, 9.389, 35.83, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 35.85, "curve": [10.278, 35.86, 10.722, -22.91, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -22.93, "curve": [11.611, -22.94, 12.056, 35.83, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 35.85, "curve": [12.667, 35.85, 12.835, 27.56, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 17.16}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.056, 1.062, 0.111, 1.072, 0.056, 0.954, 0.111, 0.949]}, {"time": 0.1667, "x": 1.072, "y": 0.949, "curve": [0.389, 1.072, 0.611, 0.924, 0.389, 0.949, 0.611, 1.028]}, {"time": 0.8333, "x": 0.924, "y": 1.028, "curve": [1.056, 0.924, 1.278, 1.072, 1.056, 1.028, 1.278, 0.949]}, {"time": 1.5, "x": 1.072, "y": 0.949, "curve": [1.722, 1.072, 1.944, 0.924, 1.722, 0.949, 1.944, 1.028]}, {"time": 2.1667, "x": 0.924, "y": 1.028, "curve": [2.389, 0.924, 2.611, 1.072, 2.389, 1.028, 2.611, 0.949]}, {"time": 2.8333, "x": 1.072, "y": 0.949, "curve": [3.056, 1.072, 3.278, 0.924, 3.056, 0.949, 3.278, 1.028]}, {"time": 3.5, "x": 0.924, "y": 1.028, "curve": [3.722, 0.924, 3.944, 1.072, 3.722, 1.028, 3.944, 0.949]}, {"time": 4.1667, "x": 1.072, "y": 0.949, "curve": [4.389, 1.072, 4.611, 0.924, 4.389, 0.949, 4.611, 1.028]}, {"time": 4.8333, "x": 0.924, "y": 1.028, "curve": [5.056, 0.924, 5.278, 1.072, 5.056, 1.028, 5.278, 0.949]}, {"time": 5.5, "x": 1.072, "y": 0.949, "curve": [5.722, 1.072, 5.944, 0.924, 5.722, 0.949, 5.944, 1.028]}, {"time": 6.1667, "x": 0.924, "y": 1.028, "curve": [6.333, 0.924, 6.5, 1.072, 6.333, 1.028, 6.5, 0.949]}, {"time": 6.6667, "x": 1.072, "y": 0.949, "curve": [6.833, 1.072, 7, 0.924, 6.833, 0.949, 7, 1.028]}, {"time": 7.1667, "x": 0.924, "y": 1.028, "curve": [7.389, 0.924, 7.611, 1.072, 7.389, 1.028, 7.611, 0.949]}, {"time": 7.8333, "x": 1.072, "y": 0.949, "curve": [8.056, 1.072, 8.278, 0.924, 8.056, 0.949, 8.278, 1.028]}, {"time": 8.5, "x": 0.924, "y": 1.028, "curve": [8.722, 0.924, 8.944, 1.072, 8.722, 1.028, 8.944, 0.949]}, {"time": 9.1667, "x": 1.072, "y": 0.949, "curve": [9.389, 1.072, 9.611, 0.924, 9.389, 0.949, 9.611, 1.028]}, {"time": 9.8333, "x": 0.924, "y": 1.028, "curve": [10.056, 0.924, 10.278, 1.072, 10.056, 1.028, 10.278, 0.949]}, {"time": 10.5, "x": 1.072, "y": 0.949, "curve": [10.722, 1.072, 10.944, 0.924, 10.722, 0.949, 10.944, 1.028]}, {"time": 11.1667, "x": 0.924, "y": 1.028, "curve": [11.389, 0.924, 11.611, 1.072, 11.389, 1.028, 11.611, 0.949]}, {"time": 11.8333, "x": 1.072, "y": 0.949, "curve": [12.056, 1.072, 12.278, 0.924, 12.056, 0.949, 12.278, 1.028]}, {"time": 12.5, "x": 0.924, "y": 1.028, "curve": [12.667, 0.924, 12.833, 1.006, 12.667, 1.028, 12.833, 0.984]}, {"time": 13, "x": 1.048, "y": 0.962}]}, "RU_L": {"translate": [{"x": -15.58, "curve": [0.168, -26.86, 0.334, -36.12, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -36.12, "curve": [0.944, -36.12, 1.389, 28.42, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 28.44, "curve": [2.278, 28.46, 2.722, -36.1, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -36.12, "curve": [3.611, -36.13, 4.056, 28.42, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 28.44, "curve": [4.944, 28.46, 5.389, -36.09, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -36.12, "curve": [6.167, -36.13, 6.5, 28.43, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 28.44, "curve": [7.278, 28.46, 7.722, -36.1, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -36.12, "curve": [8.611, -36.13, 9.056, 28.42, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 28.44, "curve": [9.944, 28.46, 10.389, -36.1, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -36.12, "curve": [11.278, -36.13, 11.722, 28.42, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 28.44, "curve": [12.445, 28.45, 12.724, 3.35, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -15.58}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.167, 1.006, 0.333, 0.924, 0.167, 0.984, 0.333, 1.028]}, {"time": 0.5, "x": 0.924, "y": 1.028, "curve": [0.722, 0.924, 0.944, 1.072, 0.722, 1.028, 0.944, 0.949]}, {"time": 1.1667, "x": 1.072, "y": 0.949, "curve": [1.389, 1.072, 1.611, 0.924, 1.389, 0.949, 1.611, 1.028]}, {"time": 1.8333, "x": 0.924, "y": 1.028, "curve": [2.056, 0.924, 2.278, 1.072, 2.056, 1.028, 2.278, 0.949]}, {"time": 2.5, "x": 1.072, "y": 0.949, "curve": [2.722, 1.072, 2.944, 0.924, 2.722, 0.949, 2.944, 1.028]}, {"time": 3.1667, "x": 0.924, "y": 1.028, "curve": [3.389, 0.924, 3.611, 1.072, 3.389, 1.028, 3.611, 0.949]}, {"time": 3.8333, "x": 1.072, "y": 0.949, "curve": [4.056, 1.072, 4.278, 0.924, 4.056, 0.949, 4.278, 1.028]}, {"time": 4.5, "x": 0.924, "y": 1.028, "curve": [4.722, 0.924, 4.944, 1.072, 4.722, 1.028, 4.944, 0.949]}, {"time": 5.1667, "x": 1.072, "y": 0.949, "curve": [5.389, 1.072, 5.611, 0.924, 5.389, 0.949, 5.611, 1.028]}, {"time": 5.8333, "x": 0.924, "y": 1.028, "curve": [6, 0.924, 6.167, 1.072, 6, 1.028, 6.167, 0.949]}, {"time": 6.3333, "x": 1.072, "y": 0.949, "curve": [6.5, 1.072, 6.667, 0.924, 6.5, 0.949, 6.667, 1.028]}, {"time": 6.8333, "x": 0.924, "y": 1.028, "curve": [7.056, 0.924, 7.278, 1.072, 7.056, 1.028, 7.278, 0.949]}, {"time": 7.5, "x": 1.072, "y": 0.949, "curve": [7.722, 1.072, 7.944, 0.924, 7.722, 0.949, 7.944, 1.028]}, {"time": 8.1667, "x": 0.924, "y": 1.028, "curve": [8.389, 0.924, 8.611, 1.072, 8.389, 1.028, 8.611, 0.949]}, {"time": 8.8333, "x": 1.072, "y": 0.949, "curve": [9.056, 1.072, 9.278, 0.924, 9.056, 0.949, 9.278, 1.028]}, {"time": 9.5, "x": 0.924, "y": 1.028, "curve": [9.722, 0.924, 9.944, 1.072, 9.722, 1.028, 9.944, 0.949]}, {"time": 10.1667, "x": 1.072, "y": 0.949, "curve": [10.389, 1.072, 10.611, 0.924, 10.389, 0.949, 10.611, 1.028]}, {"time": 10.8333, "x": 0.924, "y": 1.028, "curve": [11.056, 0.924, 11.278, 1.072, 11.056, 1.028, 11.278, 0.949]}, {"time": 11.5, "x": 1.072, "y": 0.949, "curve": [11.722, 1.072, 11.944, 0.924, 11.722, 0.949, 11.944, 1.028]}, {"time": 12.1667, "x": 0.924, "y": 1.028, "curve": [12.389, 0.924, 12.611, 1.072, 12.389, 1.028, 12.611, 0.949]}, {"time": 12.8333, "x": 1.072, "y": 0.949, "curve": [12.889, 1.072, 12.944, 1.062, 12.889, 0.949, 12.944, 0.954]}, {"time": 13, "x": 1.048, "y": 0.962}]}, "RU_L2": {"translate": [{"x": 5.58, "curve": [0.225, -5.22, 0.446, -16.18, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -16.18, "curve": [1.111, -16.18, 1.556, 27.33, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 27.34, "curve": [2.444, 27.36, 2.889, -16.17, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -16.18, "curve": [3.778, -16.19, 4.222, 27.33, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 27.34, "curve": [5.111, 27.36, 5.556, -16.17, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -16.18, "curve": [6.333, -16.19, 6.667, 27.34, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 27.34, "curve": [7.444, 27.36, 7.889, -16.17, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -16.18, "curve": [8.778, -16.19, 9.222, 27.33, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 27.34, "curve": [10.111, 27.36, 10.556, -16.17, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -16.18, "curve": [11.444, -16.19, 11.889, 27.33, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 27.34, "curve": [12.557, 27.35, 12.781, 16.54, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 5.58}], "scale": [{"x": 1.072, "y": 0.949, "curve": [0.222, 1.072, 0.444, 0.924, 0.222, 0.949, 0.444, 1.028]}, {"time": 0.6667, "x": 0.924, "y": 1.028, "curve": [0.889, 0.924, 1.111, 1.072, 0.889, 1.028, 1.111, 0.949]}, {"time": 1.3333, "x": 1.072, "y": 0.949, "curve": [1.556, 1.072, 1.778, 0.924, 1.556, 0.949, 1.778, 1.028]}, {"time": 2, "x": 0.924, "y": 1.028, "curve": [2.222, 0.924, 2.444, 1.072, 2.222, 1.028, 2.444, 0.949]}, {"time": 2.6667, "x": 1.072, "y": 0.949, "curve": [2.889, 1.072, 3.111, 0.924, 2.889, 0.949, 3.111, 1.028]}, {"time": 3.3333, "x": 0.924, "y": 1.028, "curve": [3.556, 0.924, 3.778, 1.072, 3.556, 1.028, 3.778, 0.949]}, {"time": 4, "x": 1.072, "y": 0.949, "curve": [4.222, 1.072, 4.444, 0.924, 4.222, 0.949, 4.444, 1.028]}, {"time": 4.6667, "x": 0.924, "y": 1.028, "curve": [4.889, 0.924, 5.111, 1.072, 4.889, 1.028, 5.111, 0.949]}, {"time": 5.3333, "x": 1.072, "y": 0.949, "curve": [5.556, 1.072, 5.778, 0.924, 5.556, 0.949, 5.778, 1.028]}, {"time": 6, "x": 0.924, "y": 1.028, "curve": [6.167, 0.924, 6.333, 1.072, 6.167, 1.028, 6.333, 0.949]}, {"time": 6.5, "x": 1.072, "y": 0.949, "curve": [6.667, 1.072, 6.833, 0.924, 6.667, 0.949, 6.833, 1.028]}, {"time": 7, "x": 0.924, "y": 1.028, "curve": [7.222, 0.924, 7.444, 1.072, 7.222, 1.028, 7.444, 0.949]}, {"time": 7.6667, "x": 1.072, "y": 0.949, "curve": [7.889, 1.072, 8.111, 0.924, 7.889, 0.949, 8.111, 1.028]}, {"time": 8.3333, "x": 0.924, "y": 1.028, "curve": [8.556, 0.924, 8.778, 1.072, 8.556, 1.028, 8.778, 0.949]}, {"time": 9, "x": 1.072, "y": 0.949, "curve": [9.222, 1.072, 9.444, 0.924, 9.222, 0.949, 9.444, 1.028]}, {"time": 9.6667, "x": 0.924, "y": 1.028, "curve": [9.889, 0.924, 10.111, 1.072, 9.889, 1.028, 10.111, 0.949]}, {"time": 10.3333, "x": 1.072, "y": 0.949, "curve": [10.556, 1.072, 10.778, 0.924, 10.556, 0.949, 10.778, 1.028]}, {"time": 11, "x": 0.924, "y": 1.028, "curve": [11.222, 0.924, 11.444, 1.072, 11.222, 1.028, 11.444, 0.949]}, {"time": 11.6667, "x": 1.072, "y": 0.949, "curve": [11.889, 1.072, 12.111, 0.924, 11.889, 0.949, 12.111, 1.028]}, {"time": 12.3333, "x": 0.924, "y": 1.028, "curve": [12.556, 0.924, 12.778, 1.072, 12.556, 1.028, 12.778, 0.949]}, {"time": 13, "x": 1.072, "y": 0.949}]}, "RU_L3": {"translate": [{"x": 16.03, "curve": [0.279, 3.76, 0.556, -12.56, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -12.56, "curve": [1.278, -12.56, 1.722, 29.34, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 29.35, "curve": [2.611, 29.36, 3.056, -12.55, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -12.56, "curve": [3.944, -12.58, 4.389, 29.34, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 29.35, "curve": [5.278, 29.36, 5.722, -12.55, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -12.56, "curve": [6.5, -12.58, 6.833, 29.34, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 29.35, "curve": [7.611, 29.36, 8.056, -12.55, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -12.56, "curve": [8.944, -12.58, 9.389, 29.34, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 29.35, "curve": [10.278, 29.36, 10.722, -12.55, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -12.56, "curve": [11.611, -12.58, 12.056, 29.34, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 29.35, "curve": [12.667, 29.36, 12.835, 23.44, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 16.03}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.056, 1.062, 0.111, 1.072, 0.056, 0.954, 0.111, 0.949]}, {"time": 0.1667, "x": 1.072, "y": 0.949, "curve": [0.389, 1.072, 0.611, 0.924, 0.389, 0.949, 0.611, 1.028]}, {"time": 0.8333, "x": 0.924, "y": 1.028, "curve": [1.056, 0.924, 1.278, 1.072, 1.056, 1.028, 1.278, 0.949]}, {"time": 1.5, "x": 1.072, "y": 0.949, "curve": [1.722, 1.072, 1.944, 0.924, 1.722, 0.949, 1.944, 1.028]}, {"time": 2.1667, "x": 0.924, "y": 1.028, "curve": [2.389, 0.924, 2.611, 1.072, 2.389, 1.028, 2.611, 0.949]}, {"time": 2.8333, "x": 1.072, "y": 0.949, "curve": [3.056, 1.072, 3.278, 0.924, 3.056, 0.949, 3.278, 1.028]}, {"time": 3.5, "x": 0.924, "y": 1.028, "curve": [3.722, 0.924, 3.944, 1.072, 3.722, 1.028, 3.944, 0.949]}, {"time": 4.1667, "x": 1.072, "y": 0.949, "curve": [4.389, 1.072, 4.611, 0.924, 4.389, 0.949, 4.611, 1.028]}, {"time": 4.8333, "x": 0.924, "y": 1.028, "curve": [5.056, 0.924, 5.278, 1.072, 5.056, 1.028, 5.278, 0.949]}, {"time": 5.5, "x": 1.072, "y": 0.949, "curve": [5.722, 1.072, 5.944, 0.924, 5.722, 0.949, 5.944, 1.028]}, {"time": 6.1667, "x": 0.924, "y": 1.028, "curve": [6.333, 0.924, 6.5, 1.072, 6.333, 1.028, 6.5, 0.949]}, {"time": 6.6667, "x": 1.072, "y": 0.949, "curve": [6.833, 1.072, 7, 0.924, 6.833, 0.949, 7, 1.028]}, {"time": 7.1667, "x": 0.924, "y": 1.028, "curve": [7.389, 0.924, 7.611, 1.072, 7.389, 1.028, 7.611, 0.949]}, {"time": 7.8333, "x": 1.072, "y": 0.949, "curve": [8.056, 1.072, 8.278, 0.924, 8.056, 0.949, 8.278, 1.028]}, {"time": 8.5, "x": 0.924, "y": 1.028, "curve": [8.722, 0.924, 8.944, 1.072, 8.722, 1.028, 8.944, 0.949]}, {"time": 9.1667, "x": 1.072, "y": 0.949, "curve": [9.389, 1.072, 9.611, 0.924, 9.389, 0.949, 9.611, 1.028]}, {"time": 9.8333, "x": 0.924, "y": 1.028, "curve": [10.056, 0.924, 10.278, 1.072, 10.056, 1.028, 10.278, 0.949]}, {"time": 10.5, "x": 1.072, "y": 0.949, "curve": [10.722, 1.072, 10.944, 0.924, 10.722, 0.949, 10.944, 1.028]}, {"time": 11.1667, "x": 0.924, "y": 1.028, "curve": [11.389, 0.924, 11.611, 1.072, 11.389, 1.028, 11.611, 0.949]}, {"time": 11.8333, "x": 1.072, "y": 0.949, "curve": [12.056, 1.072, 12.278, 0.924, 12.056, 0.949, 12.278, 1.028]}, {"time": 12.5, "x": 0.924, "y": 1.028, "curve": [12.667, 0.924, 12.833, 1.006, 12.667, 1.028, 12.833, 0.984]}, {"time": 13, "x": 1.048, "y": 0.962}]}, "eyebrow_L2": {"rotate": [{"curve": [0.5, 0, 5.5, -0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, -5.13]}, {"time": 6.3333, "value": -5.13, "curve": "stepped"}, {"time": 9.5, "value": -5.13, "curve": [9.667, -5.13, 9.833, 0]}, {"time": 10}]}, "eyebrow_L3": {"rotate": [{"curve": [0.5, 0, 5.5, -0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, -7.53]}, {"time": 6.3333, "value": -7.53, "curve": "stepped"}, {"time": 9.5, "value": -7.53, "curve": [9.667, -7.54, 9.833, 0]}, {"time": 10}]}, "eyebrow_R2": {"rotate": [{"curve": [0.5, 0, 5.5, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 2.74]}, {"time": 6.3333, "value": 2.74, "curve": "stepped"}, {"time": 9.5, "value": 2.74, "curve": [9.667, 2.74, 9.833, 0]}, {"time": 10}]}, "eyebrow_R3": {"rotate": [{"curve": [0.5, 0, 5.5, 0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, 7.09]}, {"time": 6.3333, "value": 7.09, "curve": "stepped"}, {"time": 9.5, "value": 7.09, "curve": [9.667, 7.09, 9.833, 0]}, {"time": 10}]}, "eye_L": {"translate": [{"curve": [0.778, 0, 1.191, -0.01, 0.778, 0, 1.191, -0.01]}, {"time": 2.3333, "curve": [2.367, 0, 2.4, -0.74, 2.367, 0, 2.4, -0.74]}, {"time": 2.4333, "x": -0.74, "y": -0.74, "curve": [2.733, -0.74, 3.033, -0.74, 2.733, -0.74, 3.033, -0.74]}, {"time": 3.3333, "x": -0.74, "y": -0.74, "curve": [3.367, -0.74, 3.4, -0.74, 3.367, -0.74, 3.4, -1.78]}, {"time": 3.4333, "x": -0.74, "y": -1.78, "curve": [3.589, -0.74, 3.744, -0.74, 3.589, -1.78, 3.744, -1.78]}, {"time": 3.9, "x": -0.74, "y": -1.78, "curve": [3.933, -0.74, 3.967, 0, 3.933, -1.78, 3.967, 0]}, {"time": 4, "curve": [4.722, 0, 5.444, -0.01, 4.722, 0, 5.444, 0.02]}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.94, 6.2, 0, 6.233, 3.92]}, {"time": 6.2667, "x": -1.94, "y": 3.92, "curve": [6.567, -1.94, 6.867, -1.93, 6.567, 3.92, 6.867, 3.92]}, {"time": 7.1667, "x": -1.94, "y": 3.92, "curve": [7.2, -1.94, 7.233, -0.67, 7.2, 3.92, 7.233, 3.92]}, {"time": 7.2667, "x": -0.67, "y": 3.92, "curve": [7.533, -0.67, 7.8, -0.67, 7.533, 3.92, 7.8, 3.92]}, {"time": 8.0667, "x": -0.67, "y": 3.92, "curve": [8.1, -0.67, 8.133, -2.11, 8.1, 3.92, 8.133, 2.91]}, {"time": 8.1667, "x": -2.11, "y": 2.91, "curve": [8.3, -2.11, 8.433, -2.11, 8.3, 2.91, 8.433, 2.91]}, {"time": 8.5667, "x": -2.11, "y": 2.91, "curve": [8.6, -2.11, 8.633, -1.94, 8.6, 2.91, 8.633, 3.92]}, {"time": 8.6667, "x": -1.94, "y": 3.92, "curve": [9.056, -1.94, 9.444, -1.93, 9.056, 3.92, 9.444, 3.91]}, {"time": 9.8333, "x": -1.94, "y": 3.92, "curve": [9.867, -1.94, 9.9, 0, 9.867, 3.92, 9.9, 0]}, {"time": 9.9333}]}, "eye_R": {"translate": [{"curve": [0.778, 0, 1.191, -0.01, 0.778, 0, 1.191, -0.01]}, {"time": 2.3333, "curve": [2.367, 0, 2.4, -0.74, 2.367, 0, 2.4, -0.74]}, {"time": 2.4333, "x": -0.74, "y": -0.74, "curve": [2.733, -0.74, 3.033, -0.74, 2.733, -0.74, 3.033, -0.74]}, {"time": 3.3333, "x": -0.74, "y": -0.74, "curve": [3.367, -0.74, 3.4, -0.74, 3.367, -0.74, 3.4, -1.78]}, {"time": 3.4333, "x": -0.74, "y": -1.78, "curve": [3.589, -0.74, 3.744, -0.74, 3.589, -1.78, 3.744, -1.78]}, {"time": 3.9, "x": -0.74, "y": -1.78, "curve": [3.933, -0.74, 3.967, 0, 3.933, -1.78, 3.967, 0]}, {"time": 4, "curve": [4.722, 0, 5.444, -0.01, 4.722, 0, 5.444, 0.02]}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.94, 6.2, 0, 6.233, 4.18]}, {"time": 6.2667, "x": -1.94, "y": 4.18, "curve": [6.567, -1.94, 6.867, -1.93, 6.567, 4.18, 6.867, 4.18]}, {"time": 7.1667, "x": -1.94, "y": 4.18, "curve": [7.2, -1.94, 7.233, -0.67, 7.2, 4.18, 7.233, 4.18]}, {"time": 7.2667, "x": -0.67, "y": 4.18, "curve": [7.533, -0.67, 7.8, -0.67, 7.533, 4.18, 7.8, 4.17]}, {"time": 8.0667, "x": -0.67, "y": 4.18, "curve": [8.1, -0.67, 8.133, -2.11, 8.1, 4.18, 8.133, 2.91]}, {"time": 8.1667, "x": -2.11, "y": 2.91, "curve": [8.3, -2.11, 8.433, -2.11, 8.3, 2.91, 8.433, 2.92]}, {"time": 8.5667, "x": -2.11, "y": 2.91, "curve": [8.6, -2.11, 8.633, -1.94, 8.6, 2.91, 8.633, 4.18]}, {"time": 8.6667, "x": -1.94, "y": 4.18, "curve": [9.056, -1.94, 9.444, -1.93, 9.056, 4.18, 9.444, 4.16]}, {"time": 9.8333, "x": -1.94, "y": 4.18, "curve": [9.867, -1.94, 9.9, 0, 9.867, 4.18, 9.9, 0]}, {"time": 9.9333}]}, "hair_F2": {"rotate": [{"value": -0.93, "curve": [0.279, -0.02, 0.556, 1.21]}, {"time": 0.8333, "value": 1.21, "curve": [1.278, 1.21, 1.722, -1.93]}, {"time": 2.1667, "value": -1.93, "curve": [2.611, -1.93, 3.056, 1.21]}, {"time": 3.5, "value": 1.21, "curve": [3.944, 1.21, 4.389, -1.93]}, {"time": 4.8333, "value": -1.93, "curve": [5.278, -1.93, 5.722, 1.21]}, {"time": 6.1667, "value": 1.21, "curve": [6.5, 1.21, 6.833, -1.93]}, {"time": 7.1667, "value": -1.93, "curve": [7.611, -1.93, 8.056, 1.21]}, {"time": 8.5, "value": 1.21, "curve": [8.944, 1.21, 9.389, -1.93]}, {"time": 9.8333, "value": -1.93, "curve": [10.278, -1.93, 10.722, 1.21]}, {"time": 11.1667, "value": 1.21, "curve": [11.611, 1.21, 12.056, -1.93]}, {"time": 12.5, "value": -1.93, "curve": [12.667, -1.93, 12.835, -1.49]}, {"time": 13, "value": -0.93}]}, "hair_F5": {"rotate": [{"value": -0.93, "curve": [0.279, -0.02, 0.556, 1.21]}, {"time": 0.8333, "value": 1.21, "curve": [1.278, 1.21, 1.722, -1.93]}, {"time": 2.1667, "value": -1.93, "curve": [2.611, -1.93, 3.056, 1.21]}, {"time": 3.5, "value": 1.21, "curve": [3.944, 1.21, 4.389, -1.93]}, {"time": 4.8333, "value": -1.93, "curve": [5.278, -1.93, 5.722, 1.21]}, {"time": 6.1667, "value": 1.21, "curve": [6.5, 1.21, 6.833, -1.93]}, {"time": 7.1667, "value": -1.93, "curve": [7.611, -1.93, 8.056, 1.21]}, {"time": 8.5, "value": 1.21, "curve": [8.944, 1.21, 9.389, -1.93]}, {"time": 9.8333, "value": -1.93, "curve": [10.278, -1.93, 10.722, 1.21]}, {"time": 11.1667, "value": 1.21, "curve": [11.611, 1.21, 12.056, -1.93]}, {"time": 12.5, "value": -1.93, "curve": [12.667, -1.93, 12.835, -1.49]}, {"time": 13, "value": -0.93}]}, "sh_L2": {"rotate": [{}]}, "sh_L3": {"rotate": [{}]}, "leg_L4": {"rotate": [{"value": 0.03}]}, "leg_L1": {"translate": [{"y": 31.21, "curve": [0.444, 0, 0.889, 0, 0.444, 31.21, 0.889, 0.01]}, {"time": 1.3333, "curve": [1.778, 0, 2.222, 0, 1.778, -0.01, 2.222, 31.21]}, {"time": 2.6667, "y": 31.21, "curve": [3.111, 0, 3.556, 0, 3.111, 31.22, 3.556, 0.01]}, {"time": 4, "curve": [4.444, 0, 4.889, 0, 4.444, -0.01, 4.889, 31.21]}, {"time": 5.3333, "y": 31.21, "curve": [5.667, 0, 6, 0, 5.667, 31.22, 6, 7.28]}, {"time": 6.3333, "y": 7.28, "curve": [6.778, 0, 7.222, 0, 6.778, 7.28, 7.222, 19.24]}, {"time": 7.6667, "y": 19.25, "curve": [8.111, 0, 8.556, 0, 8.111, 19.25, 8.556, 7.29]}, {"time": 9, "y": 7.28, "curve": [9.444, 0, 9.889, 0, 9.444, 7.27, 9.889, 31.21]}, {"time": 10.3333, "y": 31.21, "curve": [10.778, 0, 11.222, 0, 10.778, 31.22, 11.222, 0.01]}, {"time": 11.6667, "curve": [12.111, 0, 12.556, 0, 12.111, -0.01, 12.556, 31.21]}, {"time": 13, "y": 31.21}]}, "leg_R5": {"rotate": [{"value": 0.03}]}, "headround3": {"translate": [{"x": -70.65, "curve": [0.225, -184.29, 0.446, -299.49, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -299.49, "curve": [1.111, -299.49, 1.556, 157.99, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 158.11, "curve": [2.444, 158.22, 2.889, -299.38, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -299.49, "curve": [3.778, -299.61, 4.222, 157.99, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 158.11, "curve": [5.111, 158.22, 5.556, -299.34, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -299.49, "curve": [6.333, -299.61, 6.667, 158.07, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 158.11, "curve": [7.444, 158.16, 7.889, -37.96, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -38, "curve": [8.778, -38.05, 9.222, 157.99, 8.778, 0, 9.333, 0]}, {"time": 9.6667, "x": 158.11, "curve": [10.111, 158.22, 10.556, -299.49, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -299.49, "curve": [11.444, -299.49, 11.889, 157.99, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 158.11, "curve": [12.557, 158.17, 12.781, 44.52, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -70.65}]}, "headround": {"translate": [{"y": 110.19, "curve": [0.279, 0, 0.556, 0, 0.279, -44.39, 0.556, -250.02]}, {"time": 0.8333, "y": -250.02, "curve": [1.278, 0, 1.722, 0, 1.278, -250.02, 1.722, 277.92]}, {"time": 2.1667, "y": 278.06, "curve": [2.611, 0, 3.056, 0, 2.611, 278.19, 3.056, -249.89]}, {"time": 3.5, "y": -250.02, "curve": [3.944, 0, 4.389, 0, 3.944, -250.16, 4.389, 277.92]}, {"time": 4.8333, "y": 278.06, "curve": [5.278, 0, 5.722, 0, 5.278, 278.19, 5.722, -249.84]}, {"time": 6.1667, "y": -250.02, "curve": [6.5, 0, 6.833, 0, 6.5, -250.16, 6.833, 278.23]}, {"time": 7.1667, "y": 278.25, "curve": [7.611, 0, 8.056, 0, 7.611, 278.28, 8.056, 163.44]}, {"time": 8.5, "y": 163.42, "curve": [8.944, 0, 9.5, 0, 8.944, 163.39, 9.389, 278.12]}, {"time": 9.8333, "y": 278.25, "curve": [10.278, 0, 10.722, 0, 10.278, 278.39, 10.722, -250.02]}, {"time": 11.1667, "y": -250.02, "curve": [11.611, 0, 12.056, 0, 11.611, -250.02, 12.056, 277.92]}, {"time": 12.5, "y": 278.06, "curve": [12.667, 0, 12.835, 0, 12.667, 278.11, 12.835, 203.6]}, {"time": 13, "y": 110.19}]}, "bodyround": {"translate": [{"x": -33.85, "y": -97.3, "curve": [0.168, -59.87, 0.334, -81.21, 0.168, -178.81, 0.334, -245.69]}, {"time": 0.5, "x": -81.21, "y": -245.69, "curve": [0.944, -81.21, 1.389, 67.64, 0.944, -245.69, 1.389, 220.71]}, {"time": 1.8333, "x": 67.67, "y": 220.83, "curve": [2.278, 67.71, 2.722, -81.17, 2.278, 220.95, 2.722, -245.57]}, {"time": 3.1667, "x": -81.21, "y": -245.69, "curve": [3.611, -81.25, 4.056, 67.64, 3.611, -245.81, 4.056, 220.71]}, {"time": 4.5, "x": 67.67, "y": 220.83, "curve": [4.944, 67.71, 5.389, -81.16, 4.944, 220.95, 5.389, -245.53]}, {"time": 5.8333, "x": -81.21, "y": -245.69, "curve": [6.167, -81.25, 6.5, 67.65, 6.167, -245.81, 6.5, 220.74]}, {"time": 6.8333, "x": 67.67, "y": 220.83, "curve": [7.278, 67.71, 7.722, -81.17, 7.278, 220.95, 7.722, -245.57]}, {"time": 8.1667, "x": -81.21, "y": -245.69, "curve": [8.611, -81.25, 9.056, 67.64, 8.611, -245.81, 9.056, 220.71]}, {"time": 9.5, "x": 67.67, "y": 220.83, "curve": [9.944, 67.71, 10.389, -81.17, 9.944, 220.95, 10.389, -245.57]}, {"time": 10.8333, "x": -81.21, "y": -245.69, "curve": [11.278, -81.25, 11.722, 67.64, 11.278, -245.81, 11.722, 220.71]}, {"time": 12.1667, "x": 67.67, "y": 220.83, "curve": [12.445, 67.7, 12.724, 9.81, 12.445, 220.91, 12.724, 39.52]}, {"time": 13, "x": -33.85, "y": -97.3}]}, "tunround": {"translate": [{"x": 177.72, "curve": [0.057, 189.36, 0.112, 198.23, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 198.23, "curve": [0.611, 198.23, 1.056, -237.88, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -237.99, "curve": [1.944, -238.1, 2.389, 198.12, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 198.23, "curve": [3.278, 198.34, 3.722, -237.88, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -237.99, "curve": [4.611, -238.1, 5.056, 198.09, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 198.23, "curve": [5.833, 198.34, 6.167, -237.91, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -237.99, "curve": [6.944, -238.1, 7.389, 198.12, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 198.23, "curve": [8.278, 198.34, 8.722, -237.88, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -237.99, "curve": [9.611, -238.1, 10.056, 198.12, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 198.23, "curve": [10.944, 198.34, 11.389, -237.88, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -237.99, "curve": [12.223, -238.09, 12.613, 95.01, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 177.72}]}, "hair_R": {"rotate": [{"value": 2.32, "curve": [0.168, 6.62, 0.334, 10.15]}, {"time": 0.5, "value": 10.15, "curve": [0.944, 10.15, 1.389, -14.48]}, {"time": 1.8333, "value": -14.48, "curve": [2.278, -14.49, 2.722, 10.15]}, {"time": 3.1667, "value": 10.15, "curve": [3.611, 10.16, 4.056, -14.48]}, {"time": 4.5, "value": -14.48, "curve": [4.944, -14.49, 5.389, 10.14]}, {"time": 5.8333, "value": 10.15, "curve": [6.167, 10.16, 6.5, -14.48]}, {"time": 6.8333, "value": -14.48, "curve": [7.278, -14.49, 7.722, 10.15]}, {"time": 8.1667, "value": 10.15, "curve": [8.611, 10.16, 9.056, -14.48]}, {"time": 9.5, "value": -14.48, "curve": [9.944, -14.49, 10.389, 10.15]}, {"time": 10.8333, "value": 10.15, "curve": [11.278, 10.16, 11.722, -14.48]}, {"time": 12.1667, "value": -14.48, "curve": [12.445, -14.49, 12.724, -4.91]}, {"time": 13, "value": 2.32}]}, "arm_R1": {"translate": [{"x": -51.97, "curve": [0.114, -57.64, 0.224, -61.86, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -61.86, "curve": [0.778, -61.86, 1.222, 0, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "curve": [2.111, 0, 2.556, -61.86, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -61.86, "curve": [3.444, -61.86, 3.889, 0, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "curve": [4.778, 0, 5.222, -61.86, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -61.86, "curve": [6, -61.86, 6.333, 0, 6, 0, 6.333, 0]}, {"time": 6.6667, "curve": [7.111, 0, 7.556, -61.86, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -61.86, "curve": [8.444, -61.86, 8.889, 0, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "curve": [9.778, 0, 10.222, -61.86, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -61.86, "curve": [11.111, -61.86, 11.556, 0, 11.111, 0, 11.556, 0]}, {"time": 12, "curve": [12.335, 0, 12.67, -34.65, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -51.97}]}}, "ik": {"weapon": [{"curve": [1.794, 1, 3.872, 1, 1.794, 0, 3.872, 0]}, {"time": 5.6667, "curve": [6, 1, 6.333, 0, 6, 0, 6.333, 0]}, {"time": 6.6667, "mix": 0, "curve": [7.556, 0, 8.444, 0, 7.556, 0, 8.444, 0]}, {"time": 9.3333, "mix": 0, "curve": [9.778, 0, 10.222, 1, 9.778, 0, 10.222, 0]}, {"time": 10.6667}]}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-10.42773, -0.72342, -10.42542, -0.72324, -7.79077, -0.60703, -7.78723, -0.60693, -6.90405, 0.39075, -6.90088, 0.39026, -5.06628, -0.276, -5.06445, -0.27631, -3.09131, 0.0824, -3.0896, 0.08209, -3.09131, 0.0824, -3.0896, 0.08209, -2.06506, -0.05569, -2.06396, -0.05606, -0.56348, 0.05429, -0.56323, 0.0542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60999, -0.43436, -2.60974, -0.43454, -5.72961, -0.2515, -5.72766, -0.25174, -8.99744, -0.30518, -8.99585, -0.30496, -10.74084, -0.03964, -10.73901, -0.03946, 0, 0, 0, 0, -3.93323, -0.90314, -3.93286, -0.9032, -7.49658, -0.77094, -7.49487, -0.77054, -10.72644, -0.60681, -10.72351, -0.60678, -11.73767, -0.38327, -11.73535, -0.38248, -9.96204, -0.6539, -9.95911, -0.6535, -7.12073, -0.26749, -7.1189, -0.26709, -3.47852, -0.45483, -3.47705, -0.45493, -1.2373, -0.16077, -1.23669, -0.16086], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-10.42773, -0.72342, -10.42542, -0.72324, -7.79077, -0.60703, -7.78723, -0.60693, -6.90405, 0.39075, -6.90088, 0.39026, -5.06628, -0.276, -5.06445, -0.27631, -3.09131, 0.0824, -3.0896, 0.08209, -3.09131, 0.0824, -3.0896, 0.08209, -2.06506, -0.05569, -2.06396, -0.05606, -0.56348, 0.05429, -0.56323, 0.0542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60999, -0.43436, -2.60974, -0.43454, -5.72961, -0.2515, -5.72766, -0.25174, -8.99744, -0.30518, -8.99585, -0.30496, -10.74084, -0.03964, -10.73901, -0.03946, 0, 0, 0, 0, -3.93323, -0.90314, -3.93286, -0.9032, -7.49658, -0.77094, -7.49487, -0.77054, -10.72644, -0.60681, -10.72351, -0.60678, -11.73767, -0.38327, -11.73535, -0.38248, -9.96204, -0.6539, -9.95911, -0.6535, -7.12073, -0.26749, -7.1189, -0.26709, -3.47852, -0.45483, -3.47705, -0.45493, -1.2373, -0.16077, -1.23669, -0.16086], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-10.42773, -0.72342, -10.42542, -0.72324, -7.79077, -0.60703, -7.78723, -0.60693, -6.90405, 0.39075, -6.90088, 0.39026, -5.06628, -0.276, -5.06445, -0.27631, -3.09131, 0.0824, -3.0896, 0.08209, -3.09131, 0.0824, -3.0896, 0.08209, -2.06506, -0.05569, -2.06396, -0.05606, -0.56348, 0.05429, -0.56323, 0.0542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60999, -0.43436, -2.60974, -0.43454, -5.72961, -0.2515, -5.72766, -0.25174, -8.99744, -0.30518, -8.99585, -0.30496, -10.74084, -0.03964, -10.73901, -0.03946, 0, 0, 0, 0, -3.93323, -0.90314, -3.93286, -0.9032, -7.49658, -0.77094, -7.49487, -0.77054, -10.72644, -0.60681, -10.72351, -0.60678, -11.73767, -0.38327, -11.73535, -0.38248, -9.96204, -0.6539, -9.95911, -0.6535, -7.12073, -0.26749, -7.1189, -0.26709, -3.47852, -0.45483, -3.47705, -0.45493, -1.2373, -0.16077, -1.23669, -0.16086], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-7.66833, -0.59109, -7.66626, -0.59009, -12.24683, -0.57303, -12.24438, -0.5726, -11.42444, 0.25031, -11.42102, 0.25061, -8.20642, 0.17987, -8.20569, 0.18015, -3.4541, -0.29007, -3.4541, -0.29025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1698, -0.10199, -1.16931, -0.1019, -3.07727, 0.06027, -3.07532, 0.06052, -2.01807, 0.12988, -2.01672, 0.13007, -3.83997, -0.15445, -3.83826, -0.15396, -4.70154, -0.32358, -4.69971, -0.32281, -5.03357, 0.24188, -5.03076, 0.24292, 0, 0, 0, 0, -5.19287, -0.4986, -5.19287, -0.49887, -9.41748, -0.41409, -9.41553, -0.41418, -12.48804, -0.17255, -12.48499, -0.17209, -11.8678, -0.65085, -11.86536, -0.65063, -8.89758, -0.1424, -8.89502, -0.1423, -2.67407, 0.11807, -2.67322, 0.11807, -1.07556, -0.01447, -1.07495, -0.01443], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-7.66833, -0.59109, -7.66626, -0.59009, -12.24683, -0.57303, -12.24438, -0.5726, -11.42444, 0.25031, -11.42102, 0.25061, -8.20642, 0.17987, -8.20569, 0.18015, -3.4541, -0.29007, -3.4541, -0.29025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1698, -0.10199, -1.16931, -0.1019, -3.07727, 0.06027, -3.07532, 0.06052, -2.01807, 0.12988, -2.01672, 0.13007, -3.83997, -0.15445, -3.83826, -0.15396, -4.70154, -0.32358, -4.69971, -0.32281, -5.03357, 0.24188, -5.03076, 0.24292, 0, 0, 0, 0, -5.19287, -0.4986, -5.19287, -0.49887, -9.41748, -0.41409, -9.41553, -0.41418, -12.48804, -0.17255, -12.48499, -0.17209, -11.8678, -0.65085, -11.86536, -0.65063, -8.89758, -0.1424, -8.89502, -0.1423, -2.67407, 0.11807, -2.67322, 0.11807, -1.07556, -0.01447, -1.07495, -0.01443], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-7.66833, -0.59109, -7.66626, -0.59009, -12.24683, -0.57303, -12.24438, -0.5726, -11.42444, 0.25031, -11.42102, 0.25061, -8.20642, 0.17987, -8.20569, 0.18015, -3.4541, -0.29007, -3.4541, -0.29025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1698, -0.10199, -1.16931, -0.1019, -3.07727, 0.06027, -3.07532, 0.06052, -2.01807, 0.12988, -2.01672, 0.13007, -3.83997, -0.15445, -3.83826, -0.15396, -4.70154, -0.32358, -4.69971, -0.32281, -5.03357, 0.24188, -5.03076, 0.24292, 0, 0, 0, 0, -5.19287, -0.4986, -5.19287, -0.49887, -9.41748, -0.41409, -9.41553, -0.41418, -12.48804, -0.17255, -12.48499, -0.17209, -11.8678, -0.65085, -11.86536, -0.65063, -8.89758, -0.1424, -8.89502, -0.1423, -2.67407, 0.11807, -2.67322, 0.11807, -1.07556, -0.01447, -1.07495, -0.01443], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 308, "vertices": [-1.6189, 0.17233, -1.61841, 0.17233, -7.05469, 0.15076, -7.05347, 0.15054, -11.23645, -0.05786, -11.23389, -0.05795, -11.68677, -0.09714, -11.68286, -0.09729, -8.09912, -0.06259, -8.09717, -0.06265, -2.83337, -0.09064, -2.83215, -0.09058, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.11853, -0.74286, -4.1178, -0.74289, -7.86548, -0.54749, -7.86353, -0.54736, -10.10303, -0.46408, -10.10071, -0.46414, -10.94678, 0.21152, -10.94397, 0.21133, -8.40405, 0.03278, -8.40125, 0.03271, -2.55347, -0.10101, -2.55261, -0.10098, 0, 0, 0, 0, -0.87463, -0.16351, -0.87451, -0.16348, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92444, 0.00638, -0.92407], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 308, "vertices": [-1.6189, 0.17233, -1.61841, 0.17233, -7.05469, 0.15076, -7.05347, 0.15054, -11.23645, -0.05786, -11.23389, -0.05795, -11.68677, -0.09714, -11.68286, -0.09729, -8.09912, -0.06259, -8.09717, -0.06265, -2.83337, -0.09064, -2.83215, -0.09058, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.11853, -0.74286, -4.1178, -0.74289, -7.86548, -0.54749, -7.86353, -0.54736, -10.10303, -0.46408, -10.10071, -0.46414, -10.94678, 0.21152, -10.94397, 0.21133, -8.40405, 0.03278, -8.40125, 0.03271, -2.55347, -0.10101, -2.55261, -0.10098, 0, 0, 0, 0, -0.87463, -0.16351, -0.87451, -0.16348, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92444, 0.00638, -0.92407], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "offset": 308, "vertices": [-1.6189, 0.17233, -1.61841, 0.17233, -7.05469, 0.15076, -7.05347, 0.15054, -11.23645, -0.05786, -11.23389, -0.05795, -11.68677, -0.09714, -11.68286, -0.09729, -8.09912, -0.06259, -8.09717, -0.06265, -2.83337, -0.09064, -2.83215, -0.09058, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.11853, -0.74286, -4.1178, -0.74289, -7.86548, -0.54749, -7.86353, -0.54736, -10.10303, -0.46408, -10.10071, -0.46414, -10.94678, 0.21152, -10.94397, 0.21133, -8.40405, 0.03278, -8.40125, 0.03271, -2.55347, -0.10101, -2.55261, -0.10098, 0, 0, 0, 0, -0.87463, -0.16351, -0.87451, -0.16348, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92444, 0.00638, -0.92407], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 148, "vertices": [2.00354, 0.16022, 2.00439, 0.16034, 0.75, 0.05995, 0.75085, 0.06009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.05995, 0.75085, 0.06009, 1.50366, 0.12025, 1.50525, 0.12038, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.33691, 0.18686, 2.33777, 0.18703, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.33691, 0.18686, 2.33777, 0.18703, 2.33679, 0.18686, 2.33765, 0.18701, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.39685, 0.19167, 2.39783, 0.19182, 1.40259, 0.11215, 1.40295, 0.11227, 0.9491, 0.07588, 0.94995, 0.07602, -0.73145, -0.05844, -0.7301, -0.05843, 0.07983, 0.00638, 0.07983, 0.00641, 0.32959, 0.02635, 0.33057, 0.02646, 1.02783, 0.08215, 1.0282, 0.08235, 2.37622, 0.19002, 2.37683, 0.19017, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 1.42358, 0.11382, 1.42358, 0.11395, 1.82349, 0.1458, 1.82397, 0.14594, 1.70313, 0.13618, 1.70398, 0.13632, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 1.00317, 0.08017, 1.00415, 0.08034, 1.00317, 0.08017, 1.00415, 0.08034], "curve": "stepped"}, {"time": 9.6667, "offset": 148, "vertices": [2.00354, 0.16022, 2.00439, 0.16034, 0.75, 0.05995, 0.75085, 0.06009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.05995, 0.75085, 0.06009, 1.50366, 0.12025, 1.50525, 0.12038, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.33691, 0.18686, 2.33777, 0.18703, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.33691, 0.18686, 2.33777, 0.18703, 2.33679, 0.18686, 2.33765, 0.18701, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.39685, 0.19167, 2.39783, 0.19182, 1.40259, 0.11215, 1.40295, 0.11227, 0.9491, 0.07588, 0.94995, 0.07602, -0.73145, -0.05844, -0.7301, -0.05843, 0.07983, 0.00638, 0.07983, 0.00641, 0.32959, 0.02635, 0.33057, 0.02646, 1.02783, 0.08215, 1.0282, 0.08235, 2.37622, 0.19002, 2.37683, 0.19017, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 1.42358, 0.11382, 1.42358, 0.11395, 1.82349, 0.1458, 1.82397, 0.14594, 1.70313, 0.13618, 1.70398, 0.13632, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 1.00317, 0.08017, 1.00415, 0.08034, 1.00317, 0.08017, 1.00415, 0.08034], "curve": [9.778, 0, 9.889, 1]}, {"time": 10}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": -13.44, "curve": [0.023, -13.44, 0.056, 19.34, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 19.34, "curve": [0.544, 19.34, 0.856, -13.44, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": -13.44}]}, "ALL2": {"translate": [{"y": -31.27, "curve": [0.078, 0, 0.156, 0, 0.023, -31.27, 0.056, 13.15]}, {"time": 0.2333, "y": 13.15, "curve": [0.731, 0, 0.856, 0, 0.544, 13.15, 0.856, -31.27]}, {"time": 1.1667, "y": -31.27}]}, "tun": {"rotate": [{"value": 1.33, "curve": [0.023, 1.33, 0.056, 3.65]}, {"time": 0.2333, "value": 3.65, "curve": [0.544, 3.65, 0.856, 1.33]}, {"time": 1.1667, "value": 1.33}]}, "body": {"rotate": [{"value": -2.41, "curve": [0.026, -2.41, 0.064, -0.68]}, {"time": 0.2667, "value": -0.68, "curve": [0.567, -0.68, 0.867, -2.41]}, {"time": 1.1667, "value": -2.41}], "translate": [{"y": -5.11, "curve": [0.089, 0, 0.178, 0, 0.026, -5.11, 0.064, 5.53]}, {"time": 0.2667, "y": 5.53, "curve": [0.747, 0, 0.867, 0, 0.567, 5.53, 0.867, -5.11]}, {"time": 1.1667, "y": -5.11}], "scale": [{"y": 1.042, "curve": [0.089, 1, 0.178, 1, 0.026, 1.042, 0.064, 0.963]}, {"time": 0.2667, "y": 0.963, "curve": [0.747, 1, 0.867, 1, 0.567, 0.963, 0.867, 1.042]}, {"time": 1.1667, "y": 1.042}]}, "body2": {"rotate": [{"value": -2.22, "curve": [0.026, -2.22, 0.064, -11.75]}, {"time": 0.2667, "value": -11.75, "curve": [0.567, -11.75, 0.867, -2.22]}, {"time": 1.1667, "value": -2.22}], "translate": [{"x": -3.58, "curve": [0.026, -3.58, 0.064, 6.91, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 6.91, "curve": [0.567, 6.91, 0.867, -3.58, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.58}], "scale": [{"x": 1.002, "y": 1.002, "curve": [0.026, 1.002, 0.064, 1.015, 0.026, 1.002, 0.064, 1.015]}, {"time": 0.2667, "x": 1.015, "y": 1.015, "curve": [0.567, 1.015, 0.867, 1.002, 0.567, 1.015, 0.867, 1.002]}, {"time": 1.1667, "x": 1.002, "y": 1.002}]}, "neck": {"rotate": [{"value": 0.91, "curve": [0.029, 0.91, 0.073, 8.69]}, {"time": 0.3, "value": 8.69, "curve": [0.589, 8.69, 0.878, 0.91]}, {"time": 1.1667, "value": 0.91}]}, "head": {"rotate": [{"value": 0.37, "curve": [0.029, 0.37, 0.073, 8.69]}, {"time": 0.3, "value": 8.69, "curve": [0.589, 8.69, 0.878, 0.37]}, {"time": 1.1667, "value": 0.37}]}, "leg_R2": {"rotate": [{"value": 0.01}]}, "leg_R3": {"rotate": [{"value": 0.02}]}, "leg_L3": {"rotate": [{}]}, "foot_L": {"rotate": [{"value": -2.7, "curve": [0.023, -2.7, 0.056, -11.89]}, {"time": 0.2333, "value": -11.89, "curve": [0.544, -11.89, 0.856, -2.7]}, {"time": 1.1667, "value": -2.7}]}, "foot_L2": {"rotate": [{"value": -4.09, "curve": [0.023, -4.09, 0.056, -0.72]}, {"time": 0.2333, "value": -0.72, "curve": [0.544, -0.72, 0.856, -4.09]}, {"time": 1.1667, "value": -4.09}]}, "sh_L": {"translate": [{"x": -4.02, "curve": [0.026, -4.02, 0.064, 15.3, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 15.3, "curve": [0.567, 15.3, 0.867, -4.02, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.02}]}, "sh_R": {"translate": [{"x": -4.02, "curve": [0.026, -4.02, 0.064, 5.21, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 5.21, "curve": [0.567, 5.21, 0.867, -4.02, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.02}]}, "arm_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{"value": -0.04}]}, "arm_R": {"rotate": [{"value": -0.01}]}, "arm_R2": {"rotate": [{"value": -0.02}]}, "weapon": {"rotate": [{"curve": [0.026, 0, 0.064, -6.34]}, {"time": 0.2667, "value": -6.34, "curve": [0.567, -6.34, 0.867, 0]}, {"time": 1.1667}], "translate": [{"x": -36.19, "y": 4.67, "curve": [0.026, -36.19, 0.064, 40.27, 0.026, 4.67, 0.064, 103.95]}, {"time": 0.2667, "x": 40.27, "y": 103.95, "curve": [0.567, 40.27, 0.867, -36.19, 0.567, 103.95, 0.867, 4.67]}, {"time": 1.1667, "x": -36.19, "y": 4.67}]}, "RU_R": {"translate": [{"x": -14.76, "curve": [0.073, -26.49, 0.161, -36.12, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -36.12, "curve": [0.427, -36.12, 0.607, 31, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 31.02, "curve": [0.921, 31.03, 1.047, 4.93, 0.921, 0, 1.047, 0]}, {"time": 1.1667, "x": -14.76}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.072, 1.006, 0.161, 0.924, 0.072, 0.984, 0.161, 1.028]}, {"time": 0.2333, "x": 0.924, "y": 1.028, "curve": [0.33, 0.924, 0.403, 1.072, 0.33, 1.028, 0.403, 0.949]}, {"time": 0.5, "x": 1.072, "y": 0.949, "curve": [0.597, 1.072, 0.703, 0.924, 0.597, 0.949, 0.703, 1.028]}, {"time": 0.8, "x": 0.924, "y": 1.028, "curve": [0.897, 0.924, 1.003, 1.072, 0.897, 1.028, 1.003, 0.949]}, {"time": 1.1, "x": 1.072, "y": 0.949, "curve": [1.124, 1.072, 1.143, 1.062, 1.124, 0.949, 1.143, 0.954]}, {"time": 1.1667, "x": 1.048, "y": 0.962}]}, "RU_R2": {"translate": [{"x": 3.38, "curve": [0.098, -10.55, 0.204, -24.67, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -24.67, "curve": [0.493, -24.67, 0.673, 31.41, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 31.42, "curve": [0.964, 31.43, 1.071, 17.5, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 3.39}], "scale": [{"x": 1.072, "y": 0.949, "curve": [0.097, 1.072, 0.203, 0.924, 0.097, 0.949, 0.203, 1.028]}, {"time": 0.3, "x": 0.924, "y": 1.028, "curve": [0.397, 0.924, 0.47, 1.072, 0.397, 1.028, 0.47, 0.949]}, {"time": 0.5667, "x": 1.072, "y": 0.949, "curve": [0.663, 1.072, 0.77, 0.924, 0.663, 0.949, 0.77, 1.028]}, {"time": 0.8667, "x": 0.924, "y": 1.028, "curve": [0.963, 0.924, 1.07, 1.072, 0.963, 1.028, 1.07, 0.949]}, {"time": 1.1667, "x": 1.072, "y": 0.949}]}, "RU_R3": {"translate": [{"x": 17.16, "curve": [0.121, -0.04, 0.246, -22.93, 0.121, 0, 0.246, 0]}, {"time": 0.3667, "x": -22.93, "curve": [0.56, -22.93, 0.74, 35.83, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 35.85, "curve": [1.006, 35.85, 1.095, 27.56, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 17.17}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.024, 1.062, 0.043, 1.072, 0.024, 0.954, 0.043, 0.949]}, {"time": 0.0667, "x": 1.072, "y": 0.949, "curve": [0.163, 1.072, 0.27, 0.924, 0.163, 0.949, 0.27, 1.028]}, {"time": 0.3667, "x": 0.924, "y": 1.028, "curve": [0.463, 0.924, 0.57, 1.072, 0.463, 1.028, 0.57, 0.949]}, {"time": 0.6667, "x": 1.072, "y": 0.949, "curve": [0.763, 1.072, 0.837, 0.924, 0.763, 0.949, 0.837, 1.028]}, {"time": 0.9333, "x": 0.924, "y": 1.028, "curve": [1.006, 0.924, 1.095, 1.007, 1.006, 1.028, 1.095, 0.984]}, {"time": 1.1667, "x": 1.048, "y": 0.962}]}, "RU_L": {"translate": [{"x": -15.58, "curve": [0.073, -26.86, 0.161, -36.12, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -36.12, "curve": [0.427, -36.12, 0.607, 28.42, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 28.44, "curve": [0.921, 28.45, 1.047, 3.36, 0.921, 0, 1.047, 0]}, {"time": 1.1667, "x": -15.58}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.072, 1.006, 0.161, 0.924, 0.072, 0.984, 0.161, 1.028]}, {"time": 0.2333, "x": 0.924, "y": 1.028, "curve": [0.33, 0.924, 0.403, 1.072, 0.33, 1.028, 0.403, 0.949]}, {"time": 0.5, "x": 1.072, "y": 0.949, "curve": [0.597, 1.072, 0.703, 0.924, 0.597, 0.949, 0.703, 1.028]}, {"time": 0.8, "x": 0.924, "y": 1.028, "curve": [0.897, 0.924, 1.003, 1.072, 0.897, 1.028, 1.003, 0.949]}, {"time": 1.1, "x": 1.072, "y": 0.949, "curve": [1.124, 1.072, 1.143, 1.062, 1.124, 0.949, 1.143, 0.954]}, {"time": 1.1667, "x": 1.048, "y": 0.962}]}, "RU_L2": {"translate": [{"x": 5.58, "curve": [0.098, -5.22, 0.204, -16.18, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -16.18, "curve": [0.493, -16.18, 0.673, 27.33, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 27.34, "curve": [0.964, 27.35, 1.071, 16.54, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 5.59}], "scale": [{"x": 1.072, "y": 0.949, "curve": [0.097, 1.072, 0.203, 0.924, 0.097, 0.949, 0.203, 1.028]}, {"time": 0.3, "x": 0.924, "y": 1.028, "curve": [0.397, 0.924, 0.47, 1.072, 0.397, 1.028, 0.47, 0.949]}, {"time": 0.5667, "x": 1.072, "y": 0.949, "curve": [0.663, 1.072, 0.77, 0.924, 0.663, 0.949, 0.77, 1.028]}, {"time": 0.8667, "x": 0.924, "y": 1.028, "curve": [0.963, 0.924, 1.07, 1.072, 0.963, 1.028, 1.07, 0.949]}, {"time": 1.1667, "x": 1.072, "y": 0.949}]}, "RU_L3": {"translate": [{"x": 16.03, "curve": [0.121, 3.76, 0.246, -12.56, 0.121, 0, 0.246, 0]}, {"time": 0.3667, "x": -12.56, "curve": [0.56, -12.56, 0.74, 29.34, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 29.35, "curve": [1.006, 29.36, 1.095, 23.44, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 16.03}], "scale": [{"x": 1.048, "y": 0.962, "curve": [0.024, 1.062, 0.043, 1.072, 0.024, 0.954, 0.043, 0.949]}, {"time": 0.0667, "x": 1.072, "y": 0.949, "curve": [0.163, 1.072, 0.27, 0.924, 0.163, 0.949, 0.27, 1.028]}, {"time": 0.3667, "x": 0.924, "y": 1.028, "curve": [0.463, 0.924, 0.57, 1.072, 0.463, 1.028, 0.57, 0.949]}, {"time": 0.6667, "x": 1.072, "y": 0.949, "curve": [0.763, 1.072, 0.837, 0.924, 0.763, 0.949, 0.837, 1.028]}, {"time": 0.9333, "x": 0.924, "y": 1.028, "curve": [1.006, 0.924, 1.095, 1.007, 1.006, 1.028, 1.095, 0.984]}, {"time": 1.1667, "x": 1.048, "y": 0.962}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.06, -2.44, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.44, "curve": [0.544, -2.44, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.06, -13.32]}, {"time": 0.2333, "value": -13.32, "curve": [0.544, -13.32, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.06, 0.991, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.991, "curve": [0.544, 0.991, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.06, -15.34]}, {"time": 0.2333, "value": -15.34, "curve": [0.544, -15.34, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0, 0.06, -2.44, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -2.44, "curve": [0.544, -2.44, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.06, 5.7]}, {"time": 0.2333, "value": 5.7, "curve": [0.544, 5.7, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.06, 0.991, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.991, "curve": [0.544, 0.991, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0, 0.06, 17.79]}, {"time": 0.2333, "value": 17.79, "curve": [0.544, 17.79, 0.856, 0]}, {"time": 1.1667}]}, "hair_F2": {"rotate": [{"value": -0.93, "curve": [0.121, -0.02, 0.246, 1.21]}, {"time": 0.3667, "value": 1.21, "curve": [0.56, 1.21, 0.74, -1.93]}, {"time": 0.9333, "value": -1.93, "curve": [1.006, -1.93, 1.095, -1.49]}, {"time": 1.1667, "value": -0.94}]}, "hair_F5": {"rotate": [{"value": -0.93, "curve": [0.121, -0.02, 0.246, 1.21]}, {"time": 0.3667, "value": 1.21, "curve": [0.56, 1.21, 0.74, -1.93]}, {"time": 0.9333, "value": -1.93, "curve": [1.006, -1.93, 1.095, -1.49]}, {"time": 1.1667, "value": -0.94}]}, "sh_L2": {"rotate": [{}]}, "sh_L3": {"rotate": [{}]}, "leg_L4": {"rotate": [{"value": 0.03}]}, "leg_L1": {"translate": [{"y": 31.21, "curve": [0.078, 0, 0.156, 0, 0.023, 31.21, 0.056, 7.28]}, {"time": 0.2333, "y": 7.28, "curve": [0.731, 0, 0.856, 0, 0.544, 7.28, 0.856, 31.21]}, {"time": 1.1667, "y": 31.21}]}, "leg_R5": {"rotate": [{"value": 0.03}]}, "headround3": {"translate": [{"x": -70.65, "curve": [0.033, -70.65, 0.081, 249.2, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 249.2, "curve": [0.611, 249.2, 0.889, -70.65, 0.778, 0, 0.889, 0]}, {"time": 1.1667, "x": -70.65}]}, "headround": {"translate": [{"y": 110.19, "curve": [0.111, 0, 0.222, 0, 0.033, 110.19, 0.081, 532.45]}, {"time": 0.3333, "y": 532.45, "curve": [0.778, 0, 0.889, 0, 0.611, 532.45, 0.889, 110.19]}, {"time": 1.1667, "y": 110.19}]}, "bodyround": {"translate": [{"x": -33.85, "y": -97.3, "curve": [0.026, -33.85, 0.064, 85.41, 0.026, -97.3, 0.064, 357.71]}, {"time": 0.2667, "x": 85.41, "y": 357.71, "curve": [0.567, 85.41, 0.867, -33.85, 0.567, 357.71, 0.867, -97.3]}, {"time": 1.1667, "x": -33.85, "y": -97.3}]}, "tunround": {"translate": [{"x": 177.72, "curve": [0.023, 177.72, 0.056, -356.96, 0.023, 0, 0.056, -9.33]}, {"time": 0.2333, "x": -356.96, "y": -9.33, "curve": [0.544, -356.96, 0.856, 177.72, 0.544, -9.33, 0.856, 0]}, {"time": 1.1667, "x": 177.72}]}, "hair_R": {"rotate": [{"value": 2.32, "curve": [0.073, 6.62, 0.161, 10.15]}, {"time": 0.2333, "value": 10.15, "curve": [0.427, 10.15, 0.607, -14.48]}, {"time": 0.8, "value": -14.48, "curve": [0.921, -14.49, 1.047, -4.91]}, {"time": 1.1667, "value": 2.31}]}, "arm_R1": {"translate": [{"x": -51.97}]}}, "ik": {"weapon": [{"curve": [0.026, 1, 0.064, 0, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "mix": 0, "curve": [0.567, 0, 0.867, 1, 0.567, 0, 0.867, 0]}, {"time": 1.1667}]}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.025, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.72511, -0.18905, -2.72451, -0.18901, -2.03599, -0.15864, -2.03506, -0.15861, -1.80426, 0.10212, -1.80343, 0.10199, -1.32399, -0.07213, -1.32351, -0.07221, -0.80786, 0.02153, -0.80741, 0.02145, -0.80786, 0.02153, -0.80741, 0.02145, -0.53967, -0.01455, -0.53938, -0.01465, -0.14726, 0.01419, -0.14719, 0.01416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.68208, -0.11351, -0.68201, -0.11356, -1.49734, -0.06572, -1.49683, -0.06579, -2.35133, -0.07975, -2.35091, -0.0797, -2.80694, -0.01036, -2.80646, -0.01031, 0, 0, 0, 0, -1.02788, -0.23602, -1.02779, -0.23604, -1.9591, -0.20147, -1.95866, -0.20137, -2.80317, -0.15858, -2.80241, -0.15857, -3.06744, -0.10016, -3.06684, -0.09995, -2.60341, -0.17089, -2.60264, -0.17078, -1.86088, -0.0699, -1.8604, -0.0698, -0.90905, -0.11886, -0.90867, -0.11889, -0.32335, -0.04201, -0.32319, -0.04204], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.025, 0, 0.058, 1]}, {"time": 0.2333, "vertices": [-2.00399, -0.15447, -2.00345, -0.15421, -3.2005, -0.14975, -3.19986, -0.14964, -2.98558, 0.06541, -2.98469, 0.06549, -2.14461, 0.04701, -2.14442, 0.04708, -0.90267, -0.0758, -0.90267, -0.07585, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30571, -0.02665, -0.30558, -0.02663, -0.80419, 0.01575, -0.80368, 0.01581, -0.52739, 0.03394, -0.52704, 0.03399, -1.00351, -0.04036, -1.00306, -0.04024, -1.22867, -0.08456, -1.22819, -0.08436, -1.31544, 0.06321, -1.3147, 0.06348, 0, 0, 0, 0, -1.35707, -0.1303, -1.35707, -0.13037, -2.4611, -0.10822, -2.46059, -0.10824, -3.26354, -0.04509, -3.26274, -0.04497, -3.10145, -0.17009, -3.10081, -0.17003, -2.32523, -0.03721, -2.32456, -0.03719, -0.69882, 0.03086, -0.6986, 0.03086, -0.28108, -0.00378, -0.28092], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.025, 0, 0.058, 1]}, {"time": 0.2333, "offset": 308, "vertices": [-0.42307, 0.04504, -0.42294, 0.04504, -1.84362, 0.0394, -1.8433, 0.03934, -2.93646, -0.01512, -2.93579, -0.01515, -3.05414, -0.02539, -3.05312, -0.02543, -2.11657, -0.01636, -2.11606, -0.01637, -0.74045, -0.02369, -0.74014, -0.02367, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.07631, -0.19413, -1.07612, -0.19414, -2.05551, -0.14308, -2.055, -0.14304, -2.64025, -0.12128, -2.63965, -0.1213, -2.86075, 0.05528, -2.86002, 0.05523, -2.19626, 0.00857, -2.19552, 0.00855, -0.66731, -0.0264, -0.66708, -0.02639, 0, 0, 0, 0, -0.22857, -0.04273, -0.22854, -0.04272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.24159, 0.00167, -0.24149], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.025, 0, 0.058, 1]}, {"time": 0.2333, "offset": 148, "vertices": [2.00354, 0.16022, 2.00439, 0.16034, 0.75, 0.05995, 0.75085, 0.06009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.05995, 0.75085, 0.06009, 1.50366, 0.12025, 1.50525, 0.12038, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.33691, 0.18686, 2.33777, 0.18703, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.27661, 0.18205, 2.27795, 0.18222, 2.33691, 0.18686, 2.33777, 0.18703, 2.33679, 0.18686, 2.33765, 0.18701, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 2.33679, 0.18686, 2.33765, 0.18701, 2.39685, 0.19167, 2.39783, 0.19182, 1.40259, 0.11215, 1.40295, 0.11227, 0.9491, 0.07588, 0.94995, 0.07602, -0.73145, -0.05844, -0.7301, -0.05843, 0.07983, 0.00638, 0.07983, 0.00641, 0.32959, 0.02635, 0.33057, 0.02646, 1.02783, 0.08215, 1.0282, 0.08235, 2.37622, 0.19002, 2.37683, 0.19017, 2.33679, 0.18686, 2.33765, 0.18701, 2.00354, 0.16022, 2.00439, 0.16034, 1.42358, 0.11382, 1.42358, 0.11395, 1.82349, 0.1458, 1.82397, 0.14594, 1.70313, 0.13618, 1.70398, 0.13632, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 2.39697, 0.19167, 2.39795, 0.19183, 1.00317, 0.08017, 1.00415, 0.08034, 1.00317, 0.08017, 1.00415, 0.08034], "curve": [0.544, 0, 0.856, 1]}, {"time": 1.1667}]}}}}}}}