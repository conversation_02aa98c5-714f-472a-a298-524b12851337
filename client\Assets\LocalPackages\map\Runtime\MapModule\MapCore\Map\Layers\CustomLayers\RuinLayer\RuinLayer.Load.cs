﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class RuinLayer : ModelLayer, IOverlapObjectAtPosition
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new RuinLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static List<config.RuinSetting> LoadRuinObjectTypes(BinaryReader reader, Version version)
        {
            int n = reader.ReadInt32();
            List<config.RuinSetting> ruinObjectTypes = new List<config.RuinSetting>(n);
            for (int i = 0; i < n; ++i)
            {
                string name = Utils.ReadString(reader);
                Color color = Color.black;

                color = Utils.ReadColor(reader);

                PropertyDatas properties = new PropertyDatas(null);

                properties = Utils.ReadProperties(reader);

                float colliderRadius = 5.0f;

                colliderRadius = reader.ReadSingle();

                string prefabGUID = "";
                if (version.minorVersion >= 2)
                {
                    prefabGUID = Utils.ReadString(reader);
                }

                config.RuinSpecialRegionSetting setting = new config.RuinSpecialRegionSetting();
                if (version.minorVersion >= 6)
                {
                    setting.bigGridWidth = reader.ReadSingle();
                    setting.bigGridHeight = reader.ReadSingle();
                    setting.startX = reader.ReadSingle();
                    setting.startZ = reader.ReadSingle();
                    setting.pointCount = reader.ReadInt32();
                    setting.horizontalBigGridCount = reader.ReadInt32();
                    setting.verticalBigGridCount = reader.ReadInt32();
                    setting.invalidCircles = Utils.ReadIntList(reader);
                }

                ruinObjectTypes.Add(new config.RuinSetting(name, color, properties, colliderRadius, prefabGUID, setting));
            }

            return ruinObjectTypes;
        }

        static config.RuinLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            //var regionSetting = new config.SpecialRegionSetting();
            if (version.minorVersion >= 4)
            {
                /*regionSetting.bigGridWidth = */reader.ReadSingle();
                /*regionSetting.bigGridHeight = */reader.ReadSingle();
                /*regionSetting.startX = */reader.ReadSingle();
                /*regionSetting.startZ = */reader.ReadSingle();
                reader.ReadInt32();
                /*regionSetting.pointCount = */reader.ReadInt32();
                /*regionSetting.horizontalBigGridCount = */reader.ReadInt32();
                /*regionSetting.verticalBigGridCount = */reader.ReadInt32();
            }
            if (version.minorVersion >= 5)
            {
                /*regionSetting.invalidCircles = */Utils.ReadIntList(reader);
            }
            bool exportInStrictOrder = false;
            if (version.minorVersion >= 7)
            {
                exportInStrictOrder = reader.ReadBoolean();
            }

            var ruinSettings = LoadRuinObjectTypes(reader, version);

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRuinData(reader, version);
            }

            int hordeCount = reader.ReadInt32();
            float cellWidth = reader.ReadSingle();
            float displayRadius = reader.ReadSingle();
            bool textVisible = reader.ReadBoolean();
            bool shareProperties = true;
            if (version.minorVersion >= 3)
            {
                shareProperties = reader.ReadBoolean();
            }

            var layer = new config.RuinLayerData(layerID, layerName, layerOffset, null, width, height, objects, cellWidth, hordeCount, displayRadius, textVisible, ruinSettings, shareProperties, exportInStrictOrder);
            layer.active = active;
            return layer;
        }

        static config.RuinData LoadRuinData(BinaryReader reader, Version version)
        {
            var ruinData = new config.RuinData();

            ruinData.SetID(AllocateID());
            ruinData.flag = reader.ReadInt32();
            ruinData.position = Utils.ReadVector3(reader);
            ruinData.scale = Utils.ReadVector3(reader);
            Quaternion rotation = Quaternion.identity;
            if (version.minorVersion >= 2)
            {
                rotation = Utils.ReadQuaternion(reader);
            }
            ruinData.rotation = rotation;

            ruinData.type = reader.ReadInt32();
            ruinData.level = reader.ReadInt32();
            ruinData.objectType = Utils.ReadString(reader);
            ruinData.radius = reader.ReadSingle();

            if (version.minorVersion >= 3)
            {
                bool hasObjectProperties = reader.ReadBoolean();
                if (hasObjectProperties)
                {
                    ruinData.properties = Utils.ReadProperties(reader);
                }
            }

            return ruinData;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif