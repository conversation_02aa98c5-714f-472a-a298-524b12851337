﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class TileIndicator
    {
        public TileIndicator(bool originIsLowerLeft)
        {
            mIndicator = CreateGameObject(Color.white, originIsLowerLeft);
        }

        public void OnDestroy()
        {
            if (mIndicator != null)
            {
                GameObject.DestroyImmediate(mIndicator);
                mIndicator = null;
            }

            if (mIndicatorMaterial != null)
            {
                GameObject.DestroyImmediate(mIndicatorMaterial);
                mIndicatorMaterial = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mIndicator != null)
            {
                mIndicator.transform.position = pos;
            }
        }

        public void SetScale(float scale)
        {
            mIndicator.transform.localScale = new Vector3(scale, scale, scale);
        }

        public void SetColor(Color32 color)
        {
            if (mIndicator != null)
            {
                var renderer = mIndicator.GetComponent<Renderer>();
                renderer.sharedMaterial.color = color;
            }
        }

        public void SetActive(bool active)
        {
            if (mIndicator != null)
            {
                mIndicator.SetActive(active);
            }
        }

        GameObject CreateGameObject(Color32 color, bool originIsLowerLeft)
        {
            var gameObject = GameObject.CreatePrimitive(PrimitiveType.Quad);
            gameObject.transform.rotation = Quaternion.Euler(90, 0, 0);
            var renderer = gameObject.GetComponent<MeshRenderer>();
            mIndicatorMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            mIndicatorMaterial.color = color;
            renderer.sharedMaterial = mIndicatorMaterial;
            Utils.HideGameObject(gameObject);
            gameObject.transform.parent = SLGMakerEditor.instance.gameObject.transform;

            if (originIsLowerLeft)
            {
                var mesh = new Mesh();
                mesh.vertices = new Vector3[4] { 
                    new Vector3(0, 0, 0),
                    new Vector3(0, 1, 0),
                    new Vector3(1, 1, 0),
                    new Vector3(1, 0, 0),
                };
                mesh.triangles = new int[6]
                {
                    0,1,2,0,2,3,
                };
                mesh.RecalculateBounds();
                var filter = gameObject.GetComponent<MeshFilter>();
                filter.sharedMesh = mesh;
            }

            return gameObject;
        }

        GameObject mIndicator;
        Material mIndicatorMaterial;
    }
}


#endif