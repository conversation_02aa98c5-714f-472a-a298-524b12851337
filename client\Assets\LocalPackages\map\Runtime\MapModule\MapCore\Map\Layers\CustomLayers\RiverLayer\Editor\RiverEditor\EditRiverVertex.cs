﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class EditRiverVertex : RiverEditorTool
    {
        public EditRiverVertex(RiverEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onMoveVertex = OnMoveVertex;
            handlers.onInsertVertex = OnInsertVertex;
            handlers.onRemoveVertex = OnRemoveVertex;
        }

        public override void OnDestroy()
        {
        }

        public override void OnEnabled()
        {
        }
        public override void OnDisabled()
        {
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.button == 0 && e.type == EventType.MouseUp)
            {
                mLeftButtonDown = false;
                if (mVertexMover != null)
                {
                    mVertexMover.Stop(pos);
                    mVertexMover = null;
                }
                mMover.Reset();
            }

            if (e.type == EventType.MouseDown && e.button == 0 && e.alt == false)
            {
                if (e.control == false)
                {
                    mEditor.Pick(pos);
                }
                else
                {
                    if (e.shift)
                    {
                        mEditor.Pick(pos);
                        RemoveVertex();
                    }
                    else
                    {
                        AddVertex(pos);
                    }
                }
            }

            if (mLeftButtonDown && e.control == false && e.shift == false)
            {
                MoveVertex(pos);
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void DrawScene()
        {
        }

        protected override void DrawGUIImpl()
        {
            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "河流顶点的显示大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }

            EditorGUILayout.LabelField("Add Vertex: Ctrl + Mouse Left Button");
            EditorGUILayout.LabelField("Remove Vertex: Ctrl + Shift + Mouse Left Button");
        }

        void OnMoveVertex(int dataID, int vertexIndex)
        {
            mEditor.RepaintScene();
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void MoveVertex(Vector3 pos)
        {
            if (mEditor.selectedObjectID != 0 && mEditor.selectedVertexIndex >= 0)
            {
                UnityEngine.Debug.Assert(mEditor.selectedVertexIndex >= 0);

                if (mVertexMover == null)
                {
                    mVertexMover = new PolygonRiverVertexMover(mEditor.layer.id, mEditor.selectedObjectID, mEditor.selectedVertexIndex, pos, mEditor.layer.displayType);
                }

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                var oldPos = collisionData.GetOutlineVertices(mEditor.layer.displayType)[mEditor.selectedVertexIndex];
                var newPos = mMover.GetDelta() + oldPos;

                mEditor.layer.SetVertexPosition(mEditor.layer.displayType, mEditor.selectedObjectID, mEditor.selectedVertexIndex, newPos);

                mEditor.RepaintScene();
            }
        }

        void AddVertex(Vector3 localPos)
        {
            if (mEditor.selectedObjectID != 0)
            {
                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                var idx = Utils.FindNearestEdgeDistance(localPos, collisionData.GetOutlineVertices(mEditor.layer.displayType));
                InsertVertex(idx, localPos);
            }
        }

        void OnInsertVertex(int dataID, int index)
        {
            mEditor.SetSelection(Map.currentMap.FindObject(dataID) as PolygonRiverData, index);
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var action = new ActionAddPolygonRiverVertex(mEditor.layer.id, mEditor.selectedObjectID, index, localPos, mEditor.layer.displayType);
            ActionManager.instance.PushAction(action);
        }

        void OnRemoveVertex(int dataID, int index)
        {
            mEditor.ClearVertexSelection();
            mEditor.RepaintGUI();
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void RemoveVertex()
        {
            if (mEditor.selectedVertexIndex >= 0)
            {
                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                var vertices = collisionData.GetOutlineVertices(mEditor.layer.displayType);
                if (vertices.Count > 3)
                {
                    var action = new ActionRemovePolygonRiverVertex(mEditor.layer.id, mEditor.selectedObjectID, mEditor.selectedVertexIndex, mEditor.layer.displayType);
                    ActionManager.instance.PushAction(action);
                }
            }
        }

        public override RiverEditorToolType type { get { return RiverEditorToolType.EditVertex; } }

        bool mLeftButtonDown = false;
        PolygonRiverVertexMover mVertexMover;
        MouseMover mMover = new MouseMover();
    }
}

#endif