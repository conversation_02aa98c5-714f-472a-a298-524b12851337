﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class RuntimeRegionColorData
    {
        public RuntimeRegionColorData(int id, Color color)
        {
            this.id = id;
            this.color = color;
        }

        public int id;
        public Color color = Color.white;
        public PropertyDatas properties = new PropertyDatas(null);
    }

    //npc刷新区域的地图层
    public class RuntimeRegionColorLayerData : MapLayerData
    {
        struct QueryData
        {
            public QueryData(RuntimeRegionColorData data, float weight)
            {
                this.data = data;
                this.weight = weight;
            }

            public RuntimeRegionColorData data;
            public float weight;
        }

        public class Layer
        {
            public string name;
            public int horizontalTileCount;
            public int verticalTileCount;
            public float tileWidth;
            public float tileHeight;
            public short[] regionIDs;
            public RuntimeRegionColorData[] regions;

            public void SetRegionProperties(int regionID, PropertyDatas properties)
            {
                for (int i = 0; i < regions.Length; ++i)
                {
                    if (regions[i].id == regionID)
                    {
                        regions[i].properties = properties;
                        break;
                    }
                }
            }

            public bool CheckInBounds(int x, int y)
            {
                return x >= 0 && x < horizontalTileCount &&
                       y >= 0 && y < verticalTileCount;
            }
        }

        public RuntimeRegionColorLayerData(MapLayerDataHeader header, Map map, List<Layer> layers) : base(header, null, map)
        {
            mLayers = layers;
            mWeightTable[0, 0] = 0.077847f;
            mWeightTable[0, 1] = 0.123317f;
            mWeightTable[0, 2] = 0.077847f;

            mWeightTable[1, 0] = 0.123317f;
            mWeightTable[1, 1] = 0.195346f;
            mWeightTable[1, 2] = 0.123317f;

            mWeightTable[2, 0] = 0.077847f;
            mWeightTable[2, 1] = 0.123317f;
            mWeightTable[2, 2] = 0.077847f;
        }

        public override void OnDestroy()
        {
        }

        public RuntimeRegionColorData FindRegion(int layerIdx, int id)
        {
            var subLayer = GetLayer(layerIdx);
            if (subLayer != null)
            {
                var regions = subLayer.regions;
                for (int i = 0; i < regions.Length; ++i)
                {
                    if (regions[i].id == id)
                    {
                        return regions[i];
                    }
                }
            }
            return null;
        }

        public RuntimeRegionColorData GetRegion(int layerIdx, int x, int y)
        {
            var subLayer = GetLayer(layerIdx);
            if (subLayer != null && subLayer.CheckInBounds(x, y))
                return FindRegion(layerIdx, subLayer.regionIDs[y * subLayer.horizontalTileCount + x]);
            else
                return null;
        }

        public int GetRegionID(int layerIdx, int x, int y)
        {
            var subLayer = GetLayer(layerIdx);
            if (subLayer != null && subLayer.CheckInBounds(x, y))
                return subLayer.regionIDs[y * subLayer.horizontalTileCount + x];
            else
                return 0;
        }

        public Layer GetLayer(int idx)
        {
            if (idx >= 0 && idx < mLayers.Count)
                return mLayers[idx];
            else
                return null;
        }

        public Vector2Int FromWorldPositionToCoordinate(int layerIdx, Vector3 position)
        {
            var layer = GetLayer(layerIdx);
            if (layer == null)
            {
                return Vector2Int.zero;
            }
            return new Vector2Int(Mathf.FloorToInt(position.x / layer.tileWidth), Mathf.FloorToInt(position.z / layer.tileHeight));
        }

        public void SetOnPixelsChangeCallback(System.Action<int, int, int, int, Color32[]> onSetPixels)
        {
            mOnSetPixels = onSetPixels;
        }

        public void BeginQueryInterpolatedProperty(int layerIdx, Vector3 worldPos)
        {
            mQueryRegions.Clear();
            Vector2Int coord = FromWorldPositionToCoordinate(layerIdx, worldPos);

            int range = 1;
            int minX = coord.x - range;
            int minY = coord.y - range;
            int maxX = coord.x + range;
            int maxY = coord.y + range;


            for (int y = minY; y <= maxY; ++y)
            {
                for (int x = minX; x <= maxX; ++x)
                {
                    var region = GetRegion(layerIdx, x, y);
                    if (region != null)
                    {
                        int lx = x - minX;
                        int ly = y - minY;

                        mQueryRegions.Add(new QueryData(region, mWeightTable[ly, lx]));
                    }
                }
            }
        }

        public float QueryInterpolatedFloat(string propName)
        {
            float val = 0;
            if (mQueryRegions.Count > 0)
            {
                for (int i = 0; i < mQueryRegions.Count; ++i)
                {
                    var r = mQueryRegions[i];
                    val += r.data.properties.GetFloat(propName) * r.weight;
                }
            }
            return val;
        }

        public Color QueryInterpolatedColor(string propName)
        {
            Color color = new Color(0, 0, 0, 0);
            if (mQueryRegions.Count > 0)
            {
                for (int i = 0; i < mQueryRegions.Count; ++i)
                {
                    var r = mQueryRegions[i];
                    color += r.data.properties.GetColor(propName) * r.weight;
                }
            }
            return color;
        }

        public Vector4 QueryInterpolatedVector4(string propName)
        {
            Vector4 val = new Vector4(0, 0, 0, 0);
            if (mQueryRegions.Count > 0)
            {
                for (int i = 0; i < mQueryRegions.Count; ++i)
                {
                    var r = mQueryRegions[i];
                    val += r.data.properties.GetVector4(propName) * r.weight;
                }
            }
            return val;
        }

        public override bool isGameLayer => false;
        public override bool UpdateViewport(Rect newViewport, float newZoom) { return false; }
        public override void RefreshObjectsInViewport() { }
        public override bool Contains(int objectID) { return false; }

        public int layerCount { get { return mLayers.Count; } }

        List<Layer> mLayers = new List<Layer>();
        List<QueryData> mQueryRegions = new List<QueryData>();
        System.Action<int, int, int, int, Color32[]> mOnSetPixels;

        float[,] mWeightTable = new float[3, 3];
    }
}