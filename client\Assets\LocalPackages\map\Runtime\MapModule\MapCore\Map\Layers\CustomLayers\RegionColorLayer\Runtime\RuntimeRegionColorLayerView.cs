﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public partial class RuntimeRegionColorLayerView : MapLayerView
    {
        public RuntimeRegionColorLayerView(MapLayerData layerData, bool showRegionInGame) : base(layerData, false)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            mShowRegionInGame = showRegionInGame;
            var data = layerData as RuntimeRegionColorLayerData;
            var subLayer = data.GetLayer(0);
            CreateTexturePlane(0, subLayer.tileWidth * subLayer.horizontalTileCount, subLayer.tileHeight * subLayer.verticalTileCount);
            root.AddComponent<RuntimeRegionColorLayerEditor>();
#endif
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
#if UNITY_EDITOR && TFW_MAP_DEBUG
            DestroyTexturePlane(true);
#endif
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        void DestroyTexturePlane(bool destroyAll)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            if (destroyAll)
            {
                Object.DestroyImmediate(mMaterial);
            }
            Object.DestroyImmediate(mMesh);
            mMesh = null;

            Object.DestroyImmediate(mGridTexture);
            Utils.DestroyObject(mPlaneObject);
            mPlaneObject = null;
            mGridTexture = null;
#endif
        }

        void CreateTexturePlane(int layerIdx, float width, float height)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            var map = mLayerData.map;
            if (map != null)
            {
                var npcLayerData = layerData as RuntimeRegionColorLayerData;
                var layer = npcLayerData.GetLayer(layerIdx);
                int horizontalGridCount = layer.horizontalTileCount;
                int verticalGridCount = layer.verticalTileCount;
                if (mPlaneObject == null && horizontalGridCount > 0)
                {
                    //create region texture and materials
                    mGridTexture = new Texture2D(horizontalGridCount, verticalGridCount, TextureFormat.RGBA32, false, false);
                    Color32[] gridColors = new Color32[horizontalGridCount * verticalGridCount];
                    var grids = layer.regionIDs;
                    for (int i = 0; i < verticalGridCount; ++i)
                    {
                        for (int j = 0; j < horizontalGridCount; ++j)
                        {
                            var brushID = grids[i * horizontalGridCount + j];
                            var brush = npcLayerData.FindRegion(layerIdx, brushID);
                            if (brush != null)
                            {
                                gridColors[i * horizontalGridCount + j] = brush.color;
                            }
                        }
                    }

                    mGridTexture.filterMode = FilterMode.Point;
                    mGridTexture.SetPixels32(gridColors);
                    mGridTexture.Apply();

                    //create plane
                    mPlaneObject = new GameObject("region plane");
                    mPlaneObject.SetActive(mShowRegionInGame);
                    //Utils.HideGameObject(mPlaneObject);
                    mPlaneObject.transform.parent = root.transform;
                    var meshRenderer = mPlaneObject.AddComponent<MeshRenderer>();
                    var meshFilter = mPlaneObject.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = CreateMesh(width, height);
                    meshRenderer.sharedMaterial = CreateMaterial();
                    meshRenderer.sharedMaterial.SetTexture("_MainTex", mGridTexture);
                }
            }
#endif
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            Object.DestroyImmediate(mMesh);
            mMesh = new Mesh();
            mMesh.vertices = new Vector3[]{
                new Vector3(0, 0, 0),
                new Vector3(0, 0, mapHeight),
                new Vector3(mapWidth, 0, mapHeight),
                new Vector3(mapWidth, 0, 0),
            };
            mMesh.uv = new Vector2[] {
                new Vector2(0, 0),
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };
            mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            return mMesh;
#else
            return null;
#endif
        }

        Material CreateMaterial()
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
            }
            return mMaterial;
#else
            return null;
#endif
        }

        public void OnSetPixels(int x, int y, int width, int height, Color32[] pixels)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            mGridTexture.SetPixels32(x, y, width, height, pixels);
            mGridTexture.Apply();
#endif
        }

        public void OnSwitchLayer(int newLayer)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            DestroyTexturePlane(false);
            if (newLayer >= 0)
            {
                var data = layerData as RuntimeRegionColorLayerData;
                var subLayer = data.GetLayer(newLayer);
                CreateTexturePlane(newLayer, subLayer.tileWidth * subLayer.horizontalTileCount, subLayer.tileHeight * subLayer.verticalTileCount);
            }
#endif
        }

        public void RefreshTexture(int layerIdx)
        {
#if UNITY_EDITOR && TFW_MAP_DEBUG
            var npcLayerData = layerData as RuntimeRegionColorLayerData;
            var layer = npcLayerData.GetLayer(layerIdx);
            int verticalGridCount = layer.verticalTileCount;
            int horizontalGridCount = layer.horizontalTileCount;
            Color32[] colors = new Color32[verticalGridCount * horizontalGridCount];
            Color32 black = new Color32(0, 0, 0, 0);
            int idx = 0;
            var grids = layer.regionIDs;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    var brushID = grids[i * horizontalGridCount + j];
                    var brush = npcLayerData.FindRegion(layerIdx, brushID);
                    if (brush != null)
                    {
                        colors[idx] = brush.color;
                    }
                    else
                    {
                        colors[idx] = black;
                    }
                    ++idx;
                }
            }
            OnSetPixels(0, 0, horizontalGridCount, verticalGridCount, colors);
#endif
        }

        GameObject mPlaneObject;
        Mesh mMesh;
        Material mMaterial;
        Texture2D mGridTexture;
        bool mShowRegionInGame;
    }
}