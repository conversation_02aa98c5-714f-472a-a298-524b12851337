Shader "K1/Particle/AddBlend"
{
    
Properties {
    [Header(Base Effect Control)]
    _TintColor ("DiffuseColor", Color) = (0.5,0.5,0.5,0.5)
    _MainTex ("MainTex", 2D) = "white" {}
    // _Intensity("Intensity",Range(1,2)) = 1

    [Header(UV Flow Speed)]
    _TexControl("XY=UV Speed,ZW=TillingXY",vector) = (0,0,1,1)
    //[Header(DayToNight Fuction)]
	[Header(Blend Model)]
	[KeywordEnum(OFF, ON)] ALPHA_FOR_R("ALPHA_FOR_R", float) = 0
    [Enum(UnityEngine.Rendering.BlendMode)] _SrcBlend ("SrcBlend", Float) = 5.0
    [Enum(UnityEngine.Rendering.BlendMode)] _DstBlend ("DstBlend", Float) = 10.0
    [Enum(Off,0,On,1)] _ZWrite ("ZWrite", Float) = 0  //特效用不到
    [Enum(UnityEngine.Rendering.CompareFunction)] _ZTest ("ZTest", Float) = 4
	[Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", float) = 2
}

    SubShader {
        Tags { "Queue"="Transparent+500" "IgnoreProjector"="True" "RenderType"="Transparent" "PreviewType"="Plane" }

            //Blend SrcAlpha OneMinusSrcAlpha
            // ZWrite Off
            Blend [_SrcBlend] [_DstBlend]
            ZWrite [_ZWrite]  //特效用不到
            ZTest[_ZTest]
            Cull[_Cull]
            Lighting Off

            LOD 100
        Pass {

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            //#pragma multi_compile_particles
            #include "UnityCG.cginc"
			#pragma multi_compile ALPHA_FOR_R_OFF ALPHA_FOR_R_ON

            sampler2D _MainTex;
			float4 _MainTex_ST;

            float4 _TintColor;
            // float _Intensity;
            float4 _TexControl;
            
            struct appdata_t {
                float4 vertex : POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
            };

         
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.color = v.color * _TintColor;
                float2 UVoffset = float2(_TexControl.x,_TexControl.y)*_Time.yyyy;
                o.texcoord = TRANSFORM_TEX(v.texcoord , _MainTex)+ UVoffset;
                return o;
            }

            float4 frag (v2f i) : SV_Target{

				float4 col = i.color;
				float4 texCol = tex2D(_MainTex, i.texcoord.xy) * 2.0;
				col *= texCol;
                // col *= _Intensity;
            //when use addtive trigger it on use r channel for alpha
			#ifdef ALPHA_FOR_R_ON
				col.a *= texCol.r;
			#else
			 	col.a *= texCol.a;
			#endif

                return col;
            }
            ENDCG
        }
    }
}
