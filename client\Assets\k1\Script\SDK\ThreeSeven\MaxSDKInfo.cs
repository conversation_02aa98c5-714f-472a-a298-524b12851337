﻿
using System;
using System.Collections.Generic;


/**
 *  XXXInfo，代表入参规范类名
 */
namespace maxsdk
{

    //通用请求参数，封装对象 MaxSDKBaseInfo
    public class MaxSDKBaseInfo
    {
        public string extData;  //扩展字段，json格式

    }

    //初始化接口，请求info
    public class MaxSDKInitInfo : MaxSDKBaseInfo
    {
        public string appId;  //应用ID
    }

    //登录接口，请求info
    public class MaxSDKLoginInfo : MaxSDKBaseInfo
    {
        //TODO
    }


    // 支付接口，订单info
    public class MaxSDKPayInfo : MaxSDKBaseInfo
    {
        public string productId;       //商品ID
        public string productName; //商品名称
        public string serverId;    //区服ID
        public string serverName;  //区服名称
        public string roleId;      //角色ID
        public string roleName;    //角色名称
        public string roleType = "";    // 角色类型，战士/道士/法师…………
        public int roleLevel;     // 角色等级
        public string roleBronLevel = "";  //转生等级，默认传0
        public string roleLevelMTime;  //角色等级变化时间（单位：秒）
        public string partyName;   //帮派名
        public string cpOrderId;   //研发订单号
        public string orderTime;   //订单服务器时间
        public string sign;    //订单签名，由服务器生成，参与sign的订单时间和orderTime保持一致
        public int radio = 10; //充值比例
        public float money;    //支付金额
        public int gameCoin = -1; //游戏币，用于生成sign
        public bool subscription;   //该商品是否为订阅制商品，默认为false 
    }

    [System.Serializable]
    public class MaxSDKRoleInfo : MaxSDKBaseInfo
    {

        public static readonly int TYPE_ROLE_INFO_CREATE_ROLE = 1000;       // 创建角色
        public static readonly int TYPE_ROLE_INFO_SELECT_SERVICE = 1001;    // 选服页
        public static readonly int TYPE_ROLE_INFO_ENTER_GAME = 1002;        // 提交角色
        public static readonly int TYPE_ROLE_INFO_ROLE_UPGRADE = 1003;      // 升级角色
        public static readonly int TYPE_ROLE_INFO_SELECT_ROLE = 1004;        // 选角页
        public static readonly int TYPE_ROLE_INFO_COMPLETE_TUTORIAL = 1005; // 完成新手教程
        public static readonly int TYPE_ROLE_INFO_ROLE_LOGOUT = 1006;       // 角色退出游戏


        public int dataType;    //上传角色数据，事件类型（选服、创角、进游、升级等）

        public string roleId;    // 角色ID
        public string roleName;   // 角色名称
        public int roleLevel;     // 角色等级
        public string serverId;     // 服务器ID
        public string serverName;   // 服务器名称

        public float balance;     //用户游戏币余额（RMB 购买的游戏币）
        public string partyName;   //帮派名
        public int vipLevel;    //当前用户vip等级，必须为整型字符串
        public string roleCreateTime;   //角色创建时间戳（单位：秒）（新建角色必填）
        public string roleUpgradeTime;  //角色等级变化时间（单位：秒）（创建角色和进入游戏时传 -1）
        public long power = 0;         //角色战力，无则填写0

        public string roleType = "";    // 角色类型，战士/道士/法师 等
        public string roleBronLevel = "";  //转生等级，默认传0
    }

    //切换帐号接口
    public class MaxSDKSwitchAccountInfo : MaxSDKBaseInfo
    {
        //TODO 
    }


    //登出接口
    public class MaxSDKLogoutInfo : MaxSDKBaseInfo
    {
        //TODO 
    }

    //退出游戏接口
    public class MaxSDKExitInfo : MaxSDKBaseInfo
    {
        //TODO 
    }

    //数据埋点bean
    public class MaxSDKReportEventInfo : MaxSDKBaseInfo
    {

        public static readonly string EVENT_ID_GAME_INIT_START = "5001";  //游戏初始化开始
        public static readonly string EVENT_ID_GAME_INIT_END = "5002";  //游戏初始化结束


        public string eventId;      //事件ID
        public string eventName;    //事件名称
        public string eventTime;    //事件发生时间戳（毫秒）
        public string eventData;    //事件相关数据
        public string eventDescribe; // 事件描述

    }


    // 行为扩展接口参数bean
    public class MaxSDKActionInfo : MaxSDKBaseInfo
    {

        public int type;         //行为类型
        public string data;       //行为数据，不同的行为，data也不一样，详情见下表
    }


    public class MaxSDKShareInfo : MaxSDKBaseInfo
    {
        /** 分享的类型 */
        public MaxSDKShareType shareType;
        /** 分享的平台 */
        public MaxSDKSharePlatform sharePlatform;

        /** 分享的标题 */
        public string title;
        /** 分享的描述 */
        public string describe;
        /** 分享的链接 */
        public string url;
        /** 链接封面缩略图（本地路径） */
        public string thumbLocalPath;

        /** 分享的图片（本地路径） */
        public string imageLocalPath;
    }



    public class MaxSDKCustomEventInfo
    {
        /** 自定义事件名称 */
        public string eventName;
        /** 自定义事件描述 */
        public string eventDescribe;
        /** 自定义事件参数列表 */
        public Dictionary<string, object> eventParams;
    }

    public class MaxSDKLocaleInfo
    {
        /** 语言（必填） */
        public string language;

        /** 地区（必填） */
        public string country;
    }


    public class MaxSDKDispatchInfo : MaxSDKBaseInfo
    {
        public string apiName;
    }
}

