﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public abstract class MapObjectViewBase
    {
        public abstract void OnDestroy();
    }

    //地图对象的视图的基类
    public abstract class MapObjectView : MapObjectViewBase
    {
        public MapObjectView(IMapObjectData data, MapLayerView layerView)
            : base()
        {
            mLayerView = layerView;
            mObjectDataID = data.GetEntityID();

            layerView.layerData.map.view.AddMapObjectView(mObjectDataID, this);
        }
        public override void OnDestroy()
        {
            DestroyModelImpl();
            mModel = null;

            //object is destroyed
            var map = mLayerView.layerData.map;
            map.loadingTaskManager.CancelTask(mObjectDataID);
            map.view.RemoveMapObjectView(mObjectDataID, this);
        }

        protected abstract void DestroyModelImpl();

        //创建视图
        public virtual void CreateModel(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);

            //由子类来决定所使用的模型的类型
            mModel = CreateModelInternal(data, newLOD);

            var trans = mModel.transform;
            if (trans != null)
            {
                SetPosition(data.GetPosition());
                SetScale(data.GetScale());
                SetRotation(data.GetRotation());
            }

            PostCreateModel(data);
        }

        public void SetPosition(Vector3 pos)
        {
            if (transform != null)
            {
                transform.localPosition = pos;
            }
        }
        public void SetRotation(Quaternion rot)
        {
            if (transform != null)
            {
                transform.rotation = rot;
            }
        }
        public virtual void SetScale(Vector3 scale)
        {
            if (transform != null)
            {
                transform.localScale = scale;
            }
        }
        public virtual void SetActive(bool active)
        {
            if (mModel != null)
            {
                mModel.active = active;
            }
        }

        public void SetModel(ModelBase model)
        {
            DestroyModelImpl();
            mModel = model;
        }

        public void Refresh(IMapObjectData data, int lod)
        {
            DestroyModelImpl();
            CreateModel(data, lod);
        }

        protected abstract ModelBase CreateModelInternal(IMapObjectData data, int newLOD);
        public abstract void SetZoom(float zoom, bool lodChanged);
        protected virtual void PostCreateModel(IMapObjectData data) { }

        public int objectDataID { get { return mObjectDataID; } }
        public ModelBase model { get { return mModel; } }
        public Transform transform
        {
            get
            {
                if (mModel != null)
                {
                    return mModel.transform;
                }
                return null;
            }
        }
        public MapLayerView layerView { get { return mLayerView; } }
        public bool active
        {
            get
            {
                if (mModel != null)
                {
                    return mModel.active;
                }
                return false;
            }
        }

        //视图使用的模型
        protected ModelBase mModel;
        //视图所属的地图层
        protected MapLayerView mLayerView;
        //视图对应的对象数据的id
        int mObjectDataID;
    };
}
