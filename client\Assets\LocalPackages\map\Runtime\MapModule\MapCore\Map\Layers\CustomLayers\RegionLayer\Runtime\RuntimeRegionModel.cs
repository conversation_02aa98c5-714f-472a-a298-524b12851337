﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //使用对象池的模型
    public class RuntimeRegionModel : ModelBase
    {
        public void Init(RuntimeRegionData data)
        {
            mRegionData = data;
            var pool = data.map.view.reusableGameObjectPool;
            //从对象池中拿出一个模型
            mGameObject = pool.Require(MapCoreDef.DUMMY_OBJECT_NAME, Vector3.zero, Vector3.one, Quaternion.identity);
            mRegionPrefabPath = data.GetAssetPath(0);

            mRegionObject = pool.Require(mRegionPrefabPath, Vector3.zero, Vector3.one, Quaternion.identity);
            mRegionObject.transform.parent = mGameObject.transform;
            mRegionObject.SetActive(data.regionMeshVisible);

            if (!string.IsNullOrEmpty(data.borderLinePrefabPath))
            {
                mBorderLineObject = pool.Require(data.borderLinePrefabPath, Vector3.zero, Vector3.one, Quaternion.identity);
                mBorderLineObject.transform.parent = mGameObject.transform;
                mBorderLineObject.SetActive(data.borderLineVisible);
                mBorderLinePrefabPath = data.borderLinePrefabPath;
            }
        }

        public void ShowRegionMesh(bool show)
        {
            mRegionObject?.SetActive(show);
        }

        public void ShowBorderLineMesh(bool show)
        {
            mBorderLineObject?.SetActive(show);
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
            var map = mRegionData.map;
            if (mGameObject != null)
            {
                if (mBorderLineObject != null)
                {
                    map.view.reusableGameObjectPool.Release(mBorderLinePrefabPath, mBorderLineObject, map);
                }
                map.view.reusableGameObjectPool.Release(mRegionPrefabPath, mRegionObject, map);
                map.view.reusableGameObjectPool.Release(MapCoreDef.DUMMY_OBJECT_NAME, mGameObject, map);
            }
        }

        public override void Release()
        {
            ReleaseToPool(this);
        }

        public static RuntimeRegionModel Require(RuntimeRegionData data)
        {
            var model = mPool.Require();
            model.Init(data);
            return model;
        }

        static void ReleaseToPool(RuntimeRegionModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        string mRegionPrefabPath;
        string mBorderLinePrefabPath;
        GameObject mRegionObject;
        GameObject mBorderLineObject;
        RuntimeRegionData mRegionData;
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<RuntimeRegionModel> mPool = new ObjectPool<RuntimeRegionModel>(100, () => new RuntimeRegionModel());
    }
}
