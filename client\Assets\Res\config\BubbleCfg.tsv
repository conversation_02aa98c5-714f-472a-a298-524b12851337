A_INT_id	C_INT_episode_id	C_INT_order	C_INT_episode_delay	C_INT_act_delay	C_INT_delay	C_INT_search_time	C_INT_true_order	C_INT_cut_in	C_ARR_cut_in_param	C_INT_cut_out	C_ARR_cut_out_param	C_INT_cut_out_delay	C_INT_act_trigger_type	C_STR_text	C_FLT_bubbles_shift	C_STR_text_color	C_INT_role_position	C_STR_image	C_ARR_dialog_options	C_STR_role_name	C_INT_dubbing	C_ARR_role	C_ARR_cutting_area	C_ARR_role_rotate	C_ARR_act_pos	C_STR_act_txt	C_ARR_act	C_ARR_role_act	C_ARR_act_v_f_x	C_INT_guide_action	C_ARR_gesture_offset	C_ARR_gesture_rotate	C_ARR_gesture_scale	C_INT_gesture_focus	C_INT_gesture_focus_scale	C_INT_drag_city_b_g	C_STR_class_name	C_STR_targat	C_INT_close_u_i_type	C_INT_mask	C_STR_event_key	A_STR_event_param	C_ARR_options_card
1	0	1	0	900	0	1500	0	1	["100"]	1	["300"]	0	2	dialog_desc_1	285	Db7777	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_02	["dialog_opts_11"]	dialog_role_name_000		[]	[0,0,3000]	[0,0,0]	[0,-125]	dialog_act_1	["Assets/K3/Res/Art/Spine/Guide/Pinch.prefab","(0,0,0)","(1,1,1)"]	["touch","0","199007","None","0","0"]	["Assets/K3/Res/Effect/UI/Eff_ui_xue.prefab","(0,0,0)","(0,0,0)"]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010101	10101	1	0	800	0	1500	1	0	[]	0	[]	0	2	dialog_desc_1010101	285	Db7777	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_02	[]	dialog_role_name_000	213001	[]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010102	10101	2	0	2140	0	1500	1	1	["1000"]	0	[]	0	2	dialog_desc_1010102	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_06	[]	dialog_role_name_440	200001	["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010103	10101	3	0	1220	0	1500	1	0	[]	0	[]	0	2	dialog_desc_1010103	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_06	["dialog_opts_10101031"]	dialog_role_name_440	200002	["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010104	10101	4	0	800	0	1500	1	0	[]	1	["500"]	0	2	dialog_desc_1010104	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_06	[]	dialog_role_name_440	200003	["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010301	10103	1	0	700	0	1500	3	1	["500"]	1	["500"]	1500	1		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/EndTouch	0	1			[0]
1010401	10104	1	0	1580	0	1500	4	1	["500"]	0	[]	0	2	dialog_desc_1010401	285	FFCC00	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_02	[]	dialog_role_name_000		[]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1010501	10105	1	1300	**************	5	0	[]	1	["500"]	1500	1		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	NewHeroShow	Root/BtnGet	0	1			[0]
1010601	10106	1	0	1800	0	1500	6	1	["1000"]	0	[]	0	2	dialog_desc_1010601	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_06	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1020101	10201	1	1000	700	0	1500	7	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	1	5	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000001/root/Item/canDone/btnCatch	0	1			[0]
1020201	10202	1	0	800	0	1500	8	0	[]	0	[]	0	2	dialog_desc_1020201	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1030201	10302	1	0	700	0	1500	11	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	1	5	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1030202	10302	2	0	700	0	1500	11	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1030203	10302	3	1500	**************	11	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100101/ButtonClaim	0	1			[0]
1030204	10302	4	0	100	0	1500	11	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100102/ButtonGo	0	1			[0]
1040201	10402	1	0	700	0	1500	13	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	1	5	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1040202	10402	2	0	100	0	1500	13	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1040203	10402	3	0	100	0	1500	13	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1050101	10501	1	0	700	0	1500	14	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000002/root/Item/canDone/btnCatch	0	1			[0]
1050102	10501	2	0	100	0	1500	14	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1050201	10502	1	0	700	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000003/root/Item/canDone/btnCatch	0	1			[0]
1050202	10502	2	0	100	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1050203	10502	3	0	700	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1050204	10502	4	1500	100	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100102/ButtonClaim	0	1			[0]
1050205	10502	5	1000	100	0	3000	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1050206	10502	6	0	100	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1050207	10502	7	0	100	0	1500	15	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100103/ButtonGo	0	1			[0]
1060101	10601	1	0	700	0	1500	16	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1060102	10601	2	0	100	0	1500	16	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1060201	10602	1	0	700	0	1500	17	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000004/root/Item/canDone/btnCatch	0	1			[0]
1060202	10602	2	0	100	0	1500	17	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1060301	10603	1	0	700	0	1500	18	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000005/root/Item/canDone/btnCatch	0	1			[0]
1060302	10603	2	0	100	0	1500	18	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1060303	10603	3	0	700	0	1500	18	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1060304	10603	4	1500	100	0	1500	18	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100103/ButtonClaim	0	1			[0]
1060305	10603	5	0	100	0	1500	18	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100104/ButtonGo	0	1			[0]
1070101	10701	1	0	700	0	1500	19	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Hero	0	1			[0]
1070201	10702	1	0	800	0	1500	20	0	[]	0	[]	0	2	dialog_desc_1070201	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
1070301	10703	1	0	700	0	1500	21	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroList	Auto:440	0	1			[0]
1070302	10703	2	0	700	0	1500	21	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	1	5	0	MyHeroShowView	Root/HeroInfo/LevelUpBtn	0	1			[0]
1070401	10704	1	0	1700	1000	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	MyHeroShowView	PlayVideoRoot/BtnClose	0	1			[0]
1070402	10704	2	0	100	0	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroList	Root/TopBar/Close	0	1			[0]
1070403	10704	3	0	700	0	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1070404	10704	4	1500	100	0	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100104/ButtonClaim	0	1			[0]
1070405	10704	5	1000	100	0	3000	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1070406	10704	6	0	100	0	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1070407	10704	7	0	100	0	1500	22	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100105/ButtonGo	0	1			[0]
1080101	10801	1	0	700	0	1500	23	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1080102	10801	2	0	100	0	1500	23	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1080201	10802	1	0	700	0	1500	24	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000006/root/Item/canDone/btnCatch	0	1			[0]
1080202	10802	2	0	100	0	1500	24	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1080301	10803	1	0	700	0	1500	25	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000007/root/Item/canDone/btnCatch	0	1			[0]
1080302	10803	2	0	100	0	1500	25	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1080303	10803	3	0	700	0	1500	25	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1080304	10803	4	1500	100	0	1500	25	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100105/ButtonClaim	0	1			[0]
1080305	10803	5	0	100	0	1500	25	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100106/ButtonGo	0	1			[0]
1090101	10901	1	0	700	0	1500	26	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1090102	10901	2	0	100	0	1500	26	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1090201	10902	1	0	700	0	1500	27	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000008/root/Item/canDone/btnCatch	0	1			[0]
1090202	10902	2	0	100	0	1500	27	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1090301	10903	1	0	700	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000009/root/Item/canDone/btnCatch	0	1			[0]
1090302	10903	2	0	100	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1090303	10903	3	0	700	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1090304	10903	4	1500	100	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100106/ButtonClaim	0	1			[0]
1090305	10903	5	1000	100	0	3000	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1090306	10903	6	0	100	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1090307	10903	7	0	100	0	1500	28	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100107/ButtonGo	0	1			[0]
1100101	11001	1	0	700	0	1500	29	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1100102	11001	2	0	100	0	1500	29	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1100201	11002	1	0	700	0	1500	30	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000010/root/Item/canDone/btnCatch	0	1			[0]
1100202	11002	2	0	100	0	1500	30	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1100301	11003	1	0	700	0	1500	31	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000011/root/Item/canDone/btnCatch	0	1			[0]
1100302	11003	2	0	100	0	1500	31	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1100303	11003	3	0	700	0	1500	31	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1100304	11003	4	1500	100	0	1500	31	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100107/ButtonClaim	0	1			[0]
1100305	11003	5	0	100	0	1500	31	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100108/ButtonGo	0	1			[0]
1110101	11101	1	0	700	0	1500	34	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
1110102	11101	2	0	100	0	1500	34	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1110201	11102	1	0	700	0	1500	35	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000012/root/Item/canDone/btnCatch	0	1			[0]
1110202	11102	2	0	100	0	1500	35	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
1110301	11103	1	0	700	0	1500	36	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000013/root/Item/canDone/btnCatch	0	1			[0]
1110302	11103	2	0	100	0	1500	36	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
1110303	11103	3	0	700	0	1500	36	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1110304	11103	4	1500	100	0	1500	36	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100108/ButtonClaim	0	1			[0]
1110305	11103	5	0	100	0	1500	36	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100109/ButtonGo	0	1			[0]
1120101	11201	1	0	700	0	1500	37	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Hero	0	1			[0]
1120102	11201	2	0	700	0	1500	37	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroList	Auto:440	0	1			[0]
1120103	11201	3	0	700	0	1500	37	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	MyHeroShowView	Root/HeroInfo/LevelUpBtn	0	1			[0]
1120201	11202	1	0	1700	1000	1500	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	MyHeroShowView	PlayVideoRoot/BtnClose	0	1			[0]
1120202	11202	2	0	100	0	1500	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroList	Root/TopBar/Close	0	1			[0]
1120203	11202	3	0	700	0	1500	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
1120204	11202	4	1500	100	0	1500	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100109/ButtonClaim	0	1			[0]
1120205	11202	5	0	100	0	3000	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[30,20,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/StepTouch	0	1			[0]
1120206	11202	6	0	700	0	1500	38	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIHeroSkinPreview	Root/GroupEvent/EndTouch	0	1			[0]
2010201	20102	1	0	100	0	1500	40	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIGetRewardListNew	Root/GameObject/Button/btnYellow1	0	1			[0]
2010202	20102	2	1300	**************	40	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	NewHeroShow	Root/BtnGet	0	1			[0]
2020101	20201	1	0	**************	41	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100201/ButtonGo	0	1			[0]
2020201	20202	1	0	700	0	1500	42	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
2020301	20203	1	0	900	0	1500	43	0	[]	0	[]	0	2	dialog_desc_2020301	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
2020401	20204	1	0	700	0	1500	44	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	1	6	0	UIMerge	Root/bottomBtns/boxes/SpecialBox11	0	1			[0]
2020402	20204	2	0	100	0	1500	44	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox11	0	1			[0]
2020403	20204	3	0	100	0	1500	44	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox11	0	1			[0]
2030101	20301	1	0	800	0	1500	45	0	[]	0	[]	0	2	dialog_desc_2030101	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
2030201	20302	1	0	700	0	1500	46	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
2030301	20303	1	0	800	0	1500	47	0	[]	0	[]	0	2	dialog_desc_2030301	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_447		["Assets/K3/Res/Art/Spine/Hero/Hero447.prefab","(20,-1450,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
2030302	20303	2	0	880	0	1500	47	0	[]	0	[]	0	2	dialog_desc_2030302	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_447		["Assets/K3/Res/Art/Spine/Hero/Hero447.prefab","(20,-1450,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
2030401	20304	1	0	700	0	1500	48	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
2030402	20304	2	0	100	0	1500	48	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
3010101	30101	1	0	100	0	1500	49	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIGetRewardListNew	Root/GameObject/Button/btnYellow1	0	1			[0]
3010102	30101	2	1300	**************	49	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	NewHeroShow	Root/BtnGet	0	1			[0]
3010103	30101	3	0	**************	49	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100301/ButtonGo	0	1			[0]
3010201	30102	1	3500	100	0	1500	50	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[80,260,0]	[0,0,0]	[1,1,1]	1	5	5	UIMainCity	FullScreenBgCanvas/Root/BG1/Content/Building/build_5	0	1			[0]
3010301	30103	1	0	820	0	1500	51	0	[]	0	[]	0	2	dialog_desc_3010301	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
3010302	30103	2	0	800	0	1500	51	0	[]	0	[]	0	2	dialog_desc_3010302	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
3010303	30103	3	0	800	0	1500	51	0	[]	0	[]	0	2	dialog_desc_3010303	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
3010401	30104	1	0	100	0	1500	52	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[25,10,0]	[0,0,0]	[1,1,1]	0	0	0	UIStargazingPlatform	Auto:2	0	1			[0]
3010402	30104	2	0	100	0	1500	52	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIStargazingPlatformDetail	Root/TaskDetails/BtnGo	0	1			[0]
3010403	30104	3	0	300	200	1500	52	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMaze	Root/Button/GaravanBtn	0	1			[0]
9010101	90101	1	0	800	0	1500	53	0	[]	0	[]	0	2	dialog_desc_9010101	285	0	1		[]	dialog_role_name_447		["Assets/K3/Res/Art/Spine/Hero/Hero447.prefab","(20,-1450,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9010201	90102	1	1300	100	0	1500	54	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	NewHeroShow	Root/BtnGet	0	1			[0]
9010202	90102	2	0	1200	500	1500	54	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[10,30,0]	[0,180,90]	[1,1,1]	1	5	0	UIMerge	Root/bottomBtns/btnHeroAdd	0	1			[0]
9010203	90102	3	0	100	0	1500	54	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[10,30,0]	[0,180,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/btnHeroAdd	0	1			[0]
9010301	90103	1	0	900	0	1500	55	0	[]	0	[]	0	2	dialog_desc_9010301	285	0	1		[]	dialog_role_name_447		["Assets/K3/Res/Art/Spine/Hero/Hero447.prefab","(20,-1450,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9010401	90104	1	0	100	0	1500	56	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12/box/heroInfo/btnExchange	0	1			[0]
9010402	90104	2	0	100	0	1500	56	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroExChangeDialog	Root/ConfirmBtn	0	1			[0]
9010403	90104	3	0	100	0	1500	56	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroExChangeConfirmDialog	Root/ConfirmBtn	0	1			[0]
9010501	90105	1	0	100	0	1500	57	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[-40,0,0]	[0,180,0]	[1,1,1]	1	5	0	UIMerge	Root/mageSpace/FirstCharge3day	0	1			[0]
9020101	90201	1	0	800	0	1500	58	0	[]	0	[]	0	2	dialog_desc_9020101	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9020102	90201	2	0	800	0	1500	58	0	[]	0	[]	0	2	dialog_desc_9020102	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	["dialog_opts_90201021"]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9020201	90202	1	0	700	0	1500	59	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,180,90]	[1,1,1]	1	5	0	UIMain2	content/MenuRoot/World	0	1			[0]
9020202	90202	2	0	100	0	1500	59	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIBuildingHullTeleport	centre_other/animation/Button/UseBtn	0	1			[0]
9030101	90301	1	0	820	0	1500	32	0	[]	0	[]	0	2	dialog_desc_9030101	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9030201	90302	1	0	100	0	1500	33	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMergeCoolingSpeed	Root/Content/Button/UseBtn	0	1			[0]
9050101	90501	1	0	700	0	1500	60	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000017/root/Item/canDone/btnCatch	0	1			[0]
9050201	90502	1	0	800	0	1500	61	0	[]	0	[]	0	2	dialog_desc_9050201	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9050301	90503	1	0	1700	1000	1500	62	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[20,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000018/root/Item/needRes/item2	0	1			[0]
9050401	90504	1	0	880	0	1500	63	0	[]	0	[]	0	2	dialog_desc_9050401	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9050501	90505	1	0	100	0	1500	64	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMergeGoodInfo	Root/WhereBtn	0	1			[0]
9050502	90505	2	0	100	0	1500	64	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMerge	Root/bottomBtns/boxes/SpecialBox12	0	1			[0]
9040101	90401	1	0	100	0	1500	69	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,40,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Battle	0	1			[0]
9040102	90401	2	0	100	0	1500	69	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
9040103	90401	3	1500	100	0	1500	69	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100307/ButtonClaim	0	1			[0]
9040104	90401	4	0	100	0	1500	69	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100308/ButtonGo	0	1			[0]
9060101	90601	1	0	100	0	1500	70	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Hero	0	1			[0]
9060102	90601	2	0	100	0	1500	70	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIHeroList	Auto:435	0	1			[0]
9060103	90601	3	0	100	0	1500	70	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	1	5	0	MyHeroShowView	Root/Tab/tab3	0	1			[0]
9060104	90601	4	0	600	500	1500	70	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	1	5	0	MyHeroShowView	Root/HeroGood/itemGrid/item1/select/CommonBtn	0	1			[0]
9060105	90601	5	3000	600	500	1500	70	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	MyHeroShowView	Root/HeroGood/itemGrid/item1/select/CommonBtn	0	1			[0]
9070101	90701	1	0	800	0	1500	71	0	[]	0	[]	0	2	dialog_desc_9070101	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9070102	90701	2	0	800	0	1500	71	0	[]	0	[]	0	2	dialog_desc_9070102	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9070201	90702	1	0	700	0	1500	72	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,180,0]	[1,1,1]	1	5	0	MyHeroShowView	Root/HeroGood/VideoGroupBtns/BtnSkin01	0	1			[0]
9080101	90801	1	0	700	0	1500	65	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/Scroll View/Viewport/Content/3000018/root/Item/canDone/btnCatch	0	1			[0]
9080102	90801	2	0	100	0	1500	65	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMerge	Root/TaskPanel/girlItem/root/btnHeroAdd	0	1			[0]
9080103	90801	3	0	100	0	1500	65	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
9080104	90801	4	1500	100	0	1500	65	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100203/ButtonClaim	0	1			[0]
9080105	90801	5	0	700	0	1500	65	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,0]	[1,1,1]	1	5	0	UIChapterTask	Root/ChapterTask/ScrollView/ViewPort/Content/100204/ButtonGo	0	1			[0]
9080201	90802	1	0	700	0	1500	66	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[0,0,0]	[0,0,90]	[1,1,1]	0	0	0	UIMain2	content/MenuRoot/Castle	0	1			[0]
9080202	90802	2	0	100	0	1500	66	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[40,0,0]	[0,0,0]	[1,1,1]	0	0	1	UIMainCity	FullScreenBgCanvas/Root/BG1/Content/Building/build_1	0	1			[0]
9080203	90802	3	0	100	0	1500	66	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	1	[0,0,0]	[0,0,0]	[1,1,1]	0	0	0	UICityLevelUp	Root/mBtnRoot/Button	0	1			[0]
9080301	90803	1	0	800	0	1500	67	0	[]	0	[]	0	2	dialog_desc_9080301	285	0	1	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9080302	90803	2	0	800	0	1500	67	0	[]	0	[]	0	2	dialog_desc_9080302	285	0	0	Assets/K3/Res/Art/Textures/Guide/dialog_bg_01	[]	dialog_role_name_440		["Assets/K3/Res/Art/Spine/Hero/Hero440.prefab","(30,-1700,0)","(1.4,1.4,1.4)","01"]	[0,0,3000]	[0,0,0]	[]	0	[]	[]	[]	0	[]	[0,0,0]	[1,1,1]	0	0	0			0	1			[0]
9080401	90804	1	0	700	0	1500	68	0	[]	0	[]	0	2		0	0	0		[]			[]	[]	[0,0,0]	[]	0	[]	[]	[]	2	[40,0,0]	[0,0,0]	[1,1,1]	0	0	0	UIMain2	content/LeftNewBtns/Task/taskImg	0	1			[0]
