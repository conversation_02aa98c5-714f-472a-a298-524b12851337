﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class GlobalGrid
    {
        public void OnDestroy()
        {
            if (mGridObject != null)
            {
                GameObject.DestroyImmediate(mGridObject);
                mGridObject = null;
            }
        }

        public void SetActive(bool active)
        {
            mGridObject.SetActive(active);
        }

        //public void ShowGridIndex(bool show)
        //{
        //    mGridIndexObject.SetActive(show);
        //}

        public void Create(bool visible, float totalWidth, float totalHeight, float gridWidth, float gridHeight, Color color)
        {
            mTotalWidth = totalWidth;
            mTotalHeight = totalHeight;
            mGridWidth = gridWidth;
            mGridHeight = gridHeight;
            mColor = color;

            if (mGridObject != null)
            {
                GameObject.DestroyImmediate(mGridObject);
                mGridObject = null;
            }

            var gridCreator = new MapLayerXZRectangleGridCreator(mTotalWidth, mTotalHeight, mGridWidth, mGridHeight);
            mGridObject = gridCreator.CreateGrid(mColor);
            mGridObject.SetActive(visible);
            Utils.HideGameObject(mGridObject);
            mGridObject.transform.SetParent(Map.currentMap.root.transform);
            mGridObject.transform.position = new Vector3(0, 0.3f, 0);

#if false
            //create grid index
            mGridIndexObject = new GameObject("Grid Index");
            mGridIndexObject.transform.SetParent(mGridObject.transform, false);
            int horizontalGridCount = Mathf.CeilToInt(totalWidth / gridWidth);
            int verticalGridCount = Mathf.CeilToInt(totalHeight / gridHeight);
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    var tileIndexText = Utils.CreateTextGameObject($"tile_index_text_{j}_{i}", $"{j},{i}", Color.red, 32, 90);
                    float ratio = 5.0f * (gridWidth / 180.0f);
                    tileIndexText.transform.localScale = Vector3.one * ratio;
                    var tileStartPos = new Vector3(j * gridWidth, 0, i * gridHeight);
                    tileIndexText.transform.position = new Vector3(tileStartPos.x + gridWidth * 0.5f, 2, tileStartPos.z + gridWidth * 0.5f);
                    tileIndexText.transform.SetParent(mGridIndexObject.transform, false);
                }
            }
#endif
        }

        public float totalWidth { get { return mTotalWidth; } }
        public float totalHeight { get { return mTotalHeight; } }
        public float gridWidth { get { return mGridWidth; } }
        public float gridHeight { get { return mGridHeight; } }
        public Color color { get { return mColor; } }
        public bool isActive { get { return mGridObject.activeSelf; } }
        //public bool showGridIndex { get { return mGridIndexObject.activeSelf; } }

        GameObject mGridObject;
        //GameObject mGridIndexObject;
        float mTotalWidth;
        float mTotalHeight;
        float mGridWidth;
        float mGridHeight;
        Color mColor;
    }
}


#endif