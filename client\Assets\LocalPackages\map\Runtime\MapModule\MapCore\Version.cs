﻿ 



 
 


/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map
{
    public struct Version
    {
        public Version(int majorVersion, int minorVersion)
        {
            mMajorVersion = majorVersion;
            mMinorVersion = minorVersion;
            mVersionStr = $"{mMajorVersion}.{mMinorVersion}";
        }

        public override string ToString()
        {
            return mVersionStr;
        }

        public int majorVersion { get { return mMajorVersion; } }
        public int minorVersion { get { return mMinorVersion; } }

        int mMajorVersion;
        int mMinorVersion;

        string mVersionStr;
    }

    //管理编辑器的版本设置
    public static class VersionSetting
    {
        //编辑器使用的版本!
        public static Version EditorVersion = new Version(2, 15);
        //烘培动画数据的版本
        public static Version BakedAnimationDataVersion = new Version(1, 2);
        public static Version BigTileDataVersion = new Version(1, 1);
        public static Version AllBigTileDataVersion = new Version(1, 2);
        public static Version FakePrefabVersion = new Version(1, 1);
        //NavMesh数据的版本
        public static Version NavMeshVersion = new Version(1, 4);
        //Detail Object出生点数据的版本
        public static Version DetailSpriteSpawnPointVersion = new Version(1, 1);
        //Detail Object使用的prefab数据的版本
        public static Version DetailSpritePathVersion = new Version(1, 1);
        //complex grid model layer导出数据的header version
        public static Version ComplexGridObjectLayerHeaderVersion = new Version(1, 4);
        public static Version ComplexGridObjectLayerRemovableObjectsVersion = new Version(1, 1);

        public static int SettingStructVersion = 6;
        public static int ModelTemplateStructVersion = 2;
        public static int TerrainPrefabManagerStructVersion = 3;
        public static int VaryingTileSizeTerrainPrefabManagerStructVersion = 1;
        public static int BlendTerrainLayerStructVersion = 11;
        public static int VaryingTileSizeTerrainLayerStructVersion = 1;
        public static int SimpleBlendTerrainLayerStructVersion = 3;
        public static int TileBlockTerrainLayerStructVersion = 1;
        public static int SplitFogLayerStructVersion = 1;
        public static int CameraColliderStructVersion = 1;
        public static int CameraStructVersion = 1;
        public static int CircleBorderLayerStructVersion = 2;
        public static int ComplextGridModelLayerStructVersion = 3;
        public static int GlobalObstacleStructVersion = 2;
        public static int GridModelLayerStructVersion = 2;
        public static int GridRegionSettingStructVersion = 1;
        public static int LocalObstacleStructVersion = 1;
        public static int LODLayerStructVersion = 2;
        public static int ModelLayerStructVersion = 2;
        public static int PolygonRiverLayerStructVersion = 2;
        public static int RailwayLayerStructVersion = 2;
        public static int CityTerritoryLayerStructVersion = 3;
        public static int NewCityTerritoryLayerStructVersion = 1;
        public static int RegionLayerStructVersion = 1;
        public static int RegionColorLayerStructVersion = 2;
        public static int PluginLayerListStructVersion = 1;

        public static int GroundTileMakerVersion = 9;
        public static int GroundTileAtlasInfoVersion = 1;
        public static Version ComplexGridModelLayerEditorDataVersion = new Version(1, 9);
        public static Version EditorGridModelLayerEditorDataVersion = new Version(1, 3);
        public static Version CircleBorderLayerEditorDataVersion = new Version(1, 3);
        public static Version BlendTerrainLayerEditorDataVersion = new Version(1, 13);
        public static Version VaryingTileSizeTerrainLayerEditorDataVersion = new Version(1, 1);
        public static Version RegionLayerEditorDataVersion = new Version(1, 1);
        public static Version SplitFogLayerEditorDataVersion = new Version(1, 2);
        public static Version PolygonRiverLayerEditorDataVersion = new Version(1, 10);
        public static Version MapCollisionLayerEditorDataVersion = new Version(1, 3);
        public static Version CameraColliderLayerEditorDataVersion = new Version(1, 1);
        public static int RuinLayerHeaderVersion = 1;
        public static Version RuinLayerEditorDataVersion = new Version(1, 7);
        public static Version RailwayLayerEditorDataVersion = new Version(1, 4);
        public static Version NPCRegionLayerEditorDataVersion = new Version(1, 4);
        public static Version GridRegionEditorDataVersion = new Version(1, 1);
        public static Version SplineObjectManagerEditorDataVersion = new Version(1, 9);
        public static Version DetailSpritesEditorDataVersion = new Version(1, 1);
        public static Version LODLayerEditorDataVersion = new Version(1, 3);
        public static Version EntitySpawnLayerEditorDataVersion = new Version(1, 4);
        public static Version RegionColorLayerEditorDataVersion = new Version(1, 2);
        public static Version NavMeshLayerEditorDataVersion = new Version(1, 1);
        public static Version SplineLayerDataVersion = new Version(1, 1);
        public static Version EditorTerritoryLayerEditorDataVersion = new Version(1, 10);
        public static Version BuildingGridLayerEditorDataVersion = new Version(1, 1);

        public static int OptimizedFrontLayerDataVersion = 3;
        public static int GroundTileVariationVersion = 1;
        public static int RenderTextureFileVersion = 1;

        public static int ObjectPlacementEditorVersion = 9;

        public static int CameraLookAtAreaFileVersion = 1;

        public static int RiverPrefabManifestFileVersion = 1;

        public static int TerrainLODDataVersion = 1;

        public static int BuildingGridLayerDataVersion = 1;
    }
}