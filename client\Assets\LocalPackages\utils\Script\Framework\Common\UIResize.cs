﻿#if !UNITY_WEBGL
using System;
using System.Collections;
using System.Collections.Generic;
using System.Web.UI.WebControls;
using UnityEngine;

[RequireComponent(typeof(RectTransform))]
public class UIResize : MonoBehaviour
{
	private RectTransform _rectTran;
    private Rect lastSafeArea = new Rect(0, 0, 0, 0);

    void Awake()
	{
		_rectTran = GetComponent<RectTransform>();
        ApplySafeArea();
    }


    void Update()
    {
        // 检查安全区域是否变化（如设备旋转）
        if (Screen.safeArea != lastSafeArea)
        {
            ApplySafeArea();
        }
    }

    private void ApplySafeArea()
    {
        lastSafeArea = Screen.safeArea;

        // 转换安全区域坐标（从屏幕空间到UI画布空间）
        Vector2 anchorMin = lastSafeArea.position;
        Vector2 anchorMax = lastSafeArea.position + lastSafeArea.size;

        // 归一化锚点位置（0-1范围）
        anchorMin.x /= Screen.width;
        anchorMin.y /= Screen.height;
        anchorMax.x /= Screen.width;
        anchorMax.y /= Screen.height;

        // 应用到RectTransform
        _rectTran.anchorMin = anchorMin;
        _rectTran.anchorMax = anchorMax;

        Debug.Log($"应用安全区域: {lastSafeArea}");
    }

 
}
#else
using Public;
using UnityEngine;
using UnityEngine.UI;
#if USE_WXSDK && UNITY_WEBGL
using WeChatWASM;
#endif

[RequireComponent(typeof(RectTransform))]
[DisallowMultipleComponent]
[ExecuteAlways]
public class UIResize : MonoBehaviour
{
    private RectTransform rect;
    private static CanvasScaler scaler; //���ܲ���ͬһ�����Ź���  ����������

    public static void Init(CanvasScaler scaler)
    {
        UIResize.scaler = scaler;
    }

    private void Awake()
    {
        if (scaler == null)
        {
            UIResize.Init(this.GetComponentInParent<CanvasScaler>());
        }

        rect = GetComponent<RectTransform>();
        Adapt();
    }

    private void OnEnable()
    {
        Adapt();
    }

    private void Update()
    {
#if UNITY_EDITOR
        Adapt();
#endif
    }

    public void Adapt()
    {
        if (scaler == null) return;

#if UNITY_WEBGL && !UNITY_EDITOR
            //var info = WX.GetWindowInfo();
            //if(info == null) return;

            //var screenWidth=(float) info.screenWidth;
            //var screenHeight = (float)info.screenHeight;
            //var buttonPx=(float) info.statusBarHeight;


            int width = (int)(scaler.referenceResolution.x * (1 - scaler.matchWidthOrHeight) +
                scaler.referenceResolution.y * Screen.width / Screen.height * scaler.matchWidthOrHeight);
            int height = (int)(scaler.referenceResolution.y * scaler.matchWidthOrHeight -
              scaler.referenceResolution.x * Screen.height / Screen.width * (scaler.matchWidthOrHeight - 1));
            float ratio = scaler.referenceResolution.y * scaler.matchWidthOrHeight / Screen.height -
                scaler.referenceResolution.x * (scaler.matchWidthOrHeight - 1) / Screen.width;
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = new Vector2(0,0);
            rect.offsetMax = new Vector2(0,-180f);
#else
        var safeArea = Screen.safeArea;
        int width = (int)(scaler.referenceResolution.x * (1 - scaler.matchWidthOrHeight) +
                          scaler.referenceResolution.y * Screen.width / Screen.height * scaler.matchWidthOrHeight);
        int height = (int)(scaler.referenceResolution.y * scaler.matchWidthOrHeight -
                           scaler.referenceResolution.x * Screen.height / Screen.width *
                           (scaler.matchWidthOrHeight - 1));
        float ratio = scaler.referenceResolution.y * scaler.matchWidthOrHeight / Screen.height -
                      scaler.referenceResolution.x * (scaler.matchWidthOrHeight - 1) / Screen.width;
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = new Vector2(safeArea.position.x * ratio, safeArea.position.y * ratio);
        rect.offsetMax = new Vector2(safeArea.position.x * ratio + safeArea.width * ratio - width,
            -(height - safeArea.position.y * ratio - safeArea.height * ratio));
#endif
    }
}
#endif