﻿ 



 
 


using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public interface IMapResourceMgr
    {
        T LoadResource<T>(string path) where T : Object;
        void LoadResourceAsync<T>(string path, string defaultAsset, System.Action<bool, T> action) where T : Object;
        UniTask<T> LoadResourceAsync<T>(string path, string defaultAsset) where T : Object;
        GameObject LoadGameObject(string path);
        void LoadGameObjectAsync(string path, string defaultAsset, System.Action<bool, GameObject> action);
        UniTask<GameObject> LoadGameObjectAsync(string path, string defaultAsset);
        GameObject LoadPrefab(string path);
        void LoadPrefabAsync(string path, string defaultAsset, System.Action<bool, GameObject> action);
        Material LoadMaterial(string path);
        void LoadMaterialAsync(string path, string defaultAsset, System.Action<bool, Material> action);
        UniTask<Material> LoadMaterialAsync(string path, string defaultAsset);
        UniTask<byte[]> LoadTextBytesAsync(string assetPath);
        UniTask<string> LoadTextStringAsync(string assetPath);
        Texture2D LoadTexture(string path);
        byte[] LoadTextBytes(string assetPath, bool unloadTextAsset);
        bool Exists(string assetPath);
        Stream LoadTextStream(string assetPath, bool unloadTextAsset);
        UniTask<Stream> LoadTextStreamAsync(string assetPath);
        void UnloadAsset(string assetPath);
    }

    public static class MapModuleResourceMgr
    {
        public static void SetResourceManagerImpl(IMapResourceMgr mgr)
        {
            mResMgrImpl = mgr;
        }

        public static T LoadResource<T>(string path) where T : Object
        {
            return mResMgrImpl.LoadResource<T>(path);
        }

        public static async UniTask<T> LoadResourceAsync<T>(string path, string defaultAsset) where T : Object
        {
            return await mResMgrImpl.LoadResourceAsync<T>(path, defaultAsset);
        }

        public static void LoadResourceAsync<T>(string path, string defaultAsset, System.Action<bool, T> action) where T : Object
        {
            mResMgrImpl.LoadResourceAsync<T>(path, defaultAsset, action);
        }

        public static GameObject LoadGameObject(string path)
        {
            var obj = mResMgrImpl.LoadGameObject(path);
 
            return obj;
        }

        public static void LoadGameObjectAsync(string path, string defaultAsset, System.Action<string, GameObject> action)
        {
            mResMgrImpl.LoadGameObjectAsync(path, defaultAsset, (b, result) =>
            {
                var obj = result;
                action.Invoke(path, obj);
            });
        }

        public static async UniTask<GameObject> LoadGameObjectAsync(string path, string defaultAsset)
        {
            return await mResMgrImpl.LoadGameObjectAsync(path, defaultAsset);
        }

        public static GameObject LoadPrefab(string path)
        {
            var prefab = mResMgrImpl.LoadPrefab(path);
 
            return prefab;
        }
 

        public static Material LoadMaterial(string path)
        {
            return mResMgrImpl.LoadMaterial(path);
        }

        public static void LoadMaterialAsync(string path, string defaultAsset, System.Action<bool, Material> action)
        {
            mResMgrImpl.LoadMaterialAsync(path, defaultAsset, action);
        }

        public static async UniTask<Material> LoadMaterialAsync(string path, string defaultAsset)
        {
            return await mResMgrImpl.LoadMaterialAsync(path, defaultAsset);
        }

        public static Texture2D LoadTexture(string path)
        {
            return mResMgrImpl.LoadTexture(path);
        }

        public static byte[] LoadTextBytes(string assetPath, bool unloadTextAsset = false)
        {
            return mResMgrImpl.LoadTextBytes(assetPath, unloadTextAsset);
        }

        public static void LoadTextBytesAsync(string assetPath, System.Action<byte[]> action)
        {
            mResMgrImpl.LoadTextBytesAsync(assetPath).ContinueWith(x => action?.Invoke(x)).Forget();
        }

        public static UniTask<byte[]> LoadTextBytesAsync(string assetPath)
        {
            return mResMgrImpl.LoadTextBytesAsync(assetPath);
        }
        
        public static UniTask<string> LoadTextStringAsync(string assetPath)
        {
            return mResMgrImpl.LoadTextStringAsync(assetPath);
        }

        public static Stream LoadTextStream(string assetPath, bool unloadTextAsset)
        {
            return mResMgrImpl.LoadTextStream(assetPath, unloadTextAsset);
        }

        public static void LoadTextStreamAsync(string assetPath, System.Action<string, Stream> onLoaded)
        {
            mResMgrImpl.LoadTextStreamAsync(assetPath).ContinueWith(x => onLoaded?.Invoke(assetPath, x)).Forget();
        }

        public static UniTask<Stream> LoadTextStreamAsync(string assetPath)
        {
            return mResMgrImpl.LoadTextStreamAsync(assetPath);
        } 

        public static void UnloadAsset(string assetPath)
        {
            if (!string.IsNullOrEmpty(assetPath))
            {
                mResMgrImpl.UnloadAsset(assetPath);
            }
        } 
   

        public static bool Exists(string assetPath)
        {
            return mResMgrImpl.Exists(assetPath);
        }
         
        static IMapResourceMgr mResMgrImpl;
        static GameObject mErrorPlaceholderPrefab;
    }
}