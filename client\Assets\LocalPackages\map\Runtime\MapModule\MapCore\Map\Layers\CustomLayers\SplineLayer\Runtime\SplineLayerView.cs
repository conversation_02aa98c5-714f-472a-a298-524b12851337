﻿ 



 
 



/*
 * created by wzw at 2020.6.19
 */

using UnityEngine;

namespace TFW.Map
{
    public class SplineLayerView : MapObjectLayerView
    {
        public SplineLayerView(MapLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new SplineView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }
    }
}
