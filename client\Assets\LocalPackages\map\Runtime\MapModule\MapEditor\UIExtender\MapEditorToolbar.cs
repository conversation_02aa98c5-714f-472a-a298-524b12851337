﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityToolbarExtender;

namespace TFW.Map
{
	[InitializeOnLoad]
	public class MapEditorToolbar
	{
		static MapEditorToolbar()
		{
#if MAP_PACKAGE_DEV
			mMapEditorIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/map_editor_icon.png", typeof(Texture2D)), "Start Map Editor");
			mCreateGroundLODIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/create_lod_icon.png", typeof(Texture2D)), "Create Ground LOD Data");
			mChangeGroundTileMeshIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/change_ground_tile.png", typeof(Texture2D)), "Change Ground Tile Mesh");
			mSaveMapIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/Save.png", typeof(Texture2D)), "Save Map");
			mDrawNavMeshIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/draw_navmesh_icon.png", typeof(Texture2D)), "Draw NavMesh Or Obstacle");
			mDocumentIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/document_icon.png", typeof(Texture2D)), "Open Map Editor Documentation");
			mBakeSkeletonAnimationIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Assets/tfwmap/Runtime/MapModuleRes/EditorRes/bake_icon.png", typeof(Texture2D)), "Bake Skeleton Animation");
#else
			mMapEditorIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/map_editor_icon.png", typeof(Texture2D)), "Start Map Editor");
			mCreateGroundLODIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/create_lod_icon.png", typeof(Texture2D)), "Create Ground LOD Data");
			mChangeGroundTileMeshIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/change_ground_tile.png", typeof(Texture2D)), "Change Ground Tile Mesh");
			mSaveMapIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/Save.png", typeof(Texture2D)), "Save Map");
			mDrawNavMeshIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/draw_navmesh_icon.png", typeof(Texture2D)), "Draw NavMesh Or Obstacle");
			mDocumentIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/document_icon.png", typeof(Texture2D)), "Open Map Editor Documentation");
			mBakeSkeletonAnimationIcon = new GUIContent((Texture2D)AssetDatabase.LoadAssetAtPath($"Packages/com.tfw.map/Runtime/MapModuleRes/EditorRes/bake_icon.png", typeof(Texture2D)), "Bake Skeleton Animation");
#endif
			ToolbarExtender.LeftToolbarGUI.Add(OnLeftToolbarGUI);
			ToolbarExtender.RightToolbarGUI.Add(OnRightToolbarGUI);
		}

		static class ToolbarStyles
		{
			public static readonly GUIStyle commandButtonStyle;

			static ToolbarStyles()
			{
				commandButtonStyle = new GUIStyle("Command")
				{
					fontSize = 20,
					alignment = TextAnchor.MiddleCenter,
					imagePosition = ImagePosition.ImageOnly,
					fontStyle = FontStyle.Bold
				};
			}
		}

		static void OnLeftToolbarGUI()
		{
			if (showToolbar)
			{
				GUILayout.FlexibleSpace();
			}
		}

		static void OnRightToolbarGUI()
		{
			if (showToolbar)
			{
				GUILayout.Space(100);

				if (GUILayout.Button(mMapEditorIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.OpenScene();
				}

				if (GUILayout.Button(mCreateGroundLODIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.CreateTerrainLODData();
				}

				if (GUILayout.Button(mChangeGroundTileMeshIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.ChangeGroundTileMesh();
				}

				if (GUILayout.Button(mDrawNavMeshIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.DrawNavMeshOrObstacle();
				}

				if (GUILayout.Button(mBakeSkeletonAnimationIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.OpenBakeWindow();
				}

				if (GUILayout.Button(mSaveMapIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.SaveMap();
				}

				if (GUILayout.Button(mDocumentIcon, ToolbarStyles.commandButtonStyle))
				{
					SLGMakerEditorUI.OpenHelpPage();
				}
			}
		}

		static GUIContent mMapEditorIcon;
		static GUIContent mCreateGroundLODIcon;
		static GUIContent mChangeGroundTileMeshIcon;
		static GUIContent mSaveMapIcon;
		static GUIContent mDrawNavMeshIcon;
		static GUIContent mDocumentIcon;
		static GUIContent mBakeSkeletonAnimationIcon;
		static bool mShowToolbar = false;

		public static bool showToolbar 
		{ 
			get { return mShowToolbar; } 
			set {
				mShowToolbar = value;
				UnityToolbarExtender.ToolbarCallback.Repaint();
			} 
		}
	}
}


#endif