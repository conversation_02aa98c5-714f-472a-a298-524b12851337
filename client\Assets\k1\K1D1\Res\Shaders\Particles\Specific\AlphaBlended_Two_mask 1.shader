// Shader created with Shader Forge v1.38 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:3,bdst:7,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:False,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:34793,y:32873,varname:node_4795,prsc:2|emission-4156-OUT,alpha-9634-OUT,voffset-1051-OUT;n:type:ShaderForge.SFN_Tex2d,id:7126,x:32539,y:32437,ptovrint:False,ptlb:Texture,ptin:_Texture,varname:node_1635,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-4420-OUT;n:type:ShaderForge.SFN_Color,id:2031,x:32600,y:32841,ptovrint:False,ptlb:Diffuse_Color,ptin:_Diffuse_Color,varname:node_5612,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Multiply,id:5303,x:34189,y:32694,varname:node_5303,prsc:2|A-2507-OUT,B-2031-RGB,C-3225-RGB,D-883-OUT;n:type:ShaderForge.SFN_Multiply,id:6758,x:33445,y:32995,varname:node_6758,prsc:2|A-485-OUT,B-2031-A;n:type:ShaderForge.SFN_Desaturate,id:2507,x:33160,y:32477,varname:node_2507,prsc:2|COL-7126-RGB,DES-7780-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7780,x:33000,y:32578,ptovrint:False,ptlb:Desaturate,ptin:_Desaturate,varname:_Desaturate_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Power,id:725,x:32963,y:32639,varname:node_725,prsc:2|VAL-3694-OUT,EXP-7230-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7230,x:32779,y:32730,ptovrint:False,ptlb:Power,ptin:_Power,varname:node_2840,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:3694,x:32779,y:32563,ptovrint:False,ptlb:Alpha_Switch,ptin:_Alpha_Switch,varname:node_4099,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:True|A-7126-R,B-7126-A;n:type:ShaderForge.SFN_Multiply,id:9634,x:33881,y:33072,varname:node_9634,prsc:2|A-6758-OUT,B-9976-OUT,C-301-OUT,D-6817-OUT;n:type:ShaderForge.SFN_TexCoord,id:4639,x:31708,y:32924,varname:node_4639,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_TexCoord,id:1481,x:31951,y:32447,varname:node_1481,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:7728,x:31895,y:32696,varname:node_7728,prsc:2|A-5616-OUT,B-1936-OUT;n:type:ShaderForge.SFN_Add,id:4420,x:32295,y:32437,varname:node_4420,prsc:2|A-1481-UVOUT,B-7058-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6891,x:31443,y:32641,ptovrint:False,ptlb:U_speed,ptin:_U_speed,varname:_Texture_U_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:4817,x:31455,y:32817,ptovrint:False,ptlb:V_speed,ptin:_V_speed,varname:_Texture_V_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:5616,x:31681,y:32641,varname:node_5616,prsc:2|A-6891-OUT,B-9748-T;n:type:ShaderForge.SFN_Time,id:9748,x:31339,y:32688,varname:node_9748,prsc:2;n:type:ShaderForge.SFN_Multiply,id:1936,x:31681,y:32783,varname:node_1936,prsc:2|A-9748-T,B-4817-OUT;n:type:ShaderForge.SFN_Append,id:8046,x:31923,y:32944,varname:node_8046,prsc:2|A-4639-U,B-4639-V;n:type:ShaderForge.SFN_SwitchProperty,id:7058,x:32073,y:32696,ptovrint:False,ptlb:Custom_UV,ptin:_Custom_UV,varname:_Custom_UV_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7728-OUT,B-8046-OUT;n:type:ShaderForge.SFN_Multiply,id:485,x:33361,y:32721,varname:node_485,prsc:2|A-725-OUT,B-326-OUT;n:type:ShaderForge.SFN_Tex2d,id:8723,x:32537,y:33197,ptovrint:False,ptlb:Mask_Tex,ptin:_Mask_Tex,varname:node_8723,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-4672-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:326,x:33201,y:32850,ptovrint:False,ptlb:Mask_Switch,ptin:_Mask_Switch,varname:node_326,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-8723-R,B-8723-A;n:type:ShaderForge.SFN_ValueProperty,id:301,x:33653,y:32794,ptovrint:False,ptlb:Intensity,ptin:_Intensity,varname:node_301,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Fresnel,id:9976,x:33483,y:33152,varname:node_9976,prsc:2|EXP-6675-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6675,x:33263,y:33278,ptovrint:False,ptlb:Fresnel,ptin:_Fresnel,varname:node_6675,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Tex2d,id:233,x:33947,y:33349,ptovrint:False,ptlb:Nois_Tex,ptin:_Nois_Tex,varname:node_233,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-6196-OUT;n:type:ShaderForge.SFN_Vector4Property,id:2546,x:33797,y:33682,ptovrint:False,ptlb:pianyi,ptin:_pianyi,varname:node_2546,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0,v2:0,v3:0,v4:0;n:type:ShaderForge.SFN_Multiply,id:1051,x:34329,y:33493,varname:node_1051,prsc:2|A-2177-OUT,B-2546-XYZ,C-256-OUT;n:type:ShaderForge.SFN_TexCoord,id:9024,x:33395,y:33370,varname:node_9024,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:207,x:33445,y:33542,varname:node_207,prsc:2|A-3622-OUT,B-320-OUT;n:type:ShaderForge.SFN_Add,id:6196,x:33707,y:33420,varname:node_6196,prsc:2|A-9024-UVOUT,B-207-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7715,x:32769,y:33406,ptovrint:False,ptlb:U_speed_nois,ptin:_U_speed_nois,varname:_U_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:5801,x:32798,y:33725,ptovrint:False,ptlb:V_speed_nois,ptin:_V_speed_nois,varname:_V_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:3622,x:33155,y:33496,varname:node_3622,prsc:2|A-7715-OUT,B-3598-T;n:type:ShaderForge.SFN_Time,id:3598,x:32570,y:33516,varname:node_3598,prsc:2;n:type:ShaderForge.SFN_Multiply,id:320,x:33155,y:33647,varname:node_320,prsc:2|A-3598-T,B-5801-OUT;n:type:ShaderForge.SFN_ValueProperty,id:256,x:34174,y:33720,ptovrint:False,ptlb:Nois_Intensity,ptin:_Nois_Intensity,varname:node_256,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_TexCoord,id:7984,x:31790,y:33136,varname:node_7984,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:9926,x:32135,y:33390,varname:node_9926,prsc:2|A-8823-OUT,B-2620-OUT;n:type:ShaderForge.SFN_Add,id:4672,x:32261,y:33184,varname:node_4672,prsc:2|A-7984-UVOUT,B-9926-OUT;n:type:ShaderForge.SFN_Multiply,id:8823,x:31921,y:33335,varname:node_8823,prsc:2|A-4515-OUT,B-486-T;n:type:ShaderForge.SFN_Time,id:486,x:31579,y:33382,varname:node_486,prsc:2;n:type:ShaderForge.SFN_Multiply,id:2620,x:31921,y:33477,varname:node_2620,prsc:2|A-486-T,B-9241-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9241,x:31579,y:33605,ptovrint:False,ptlb:Mask_v,ptin:_Mask_v,varname:node_9241,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:4515,x:31579,y:33297,ptovrint:False,ptlb:Mask_U,ptin:_Mask_U,varname:node_4515,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_SwitchProperty,id:6817,x:33665,y:34148,ptovrint:False,ptlb:Diss_switch,ptin:_Diss_switch,varname:node_6817,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-3225-A,B-9226-OUT;n:type:ShaderForge.SFN_If,id:9346,x:33370,y:35062,varname:node_9346,prsc:2|A-3225-A,B-9424-R,GT-4677-OUT,EQ-4677-OUT,LT-3723-OUT;n:type:ShaderForge.SFN_Tex2d,id:9424,x:32183,y:35082,ptovrint:False,ptlb:Dissolve_Tex,ptin:_Dissolve_Tex,varname:node_1812,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,tex:28c7aad1372ff114b90d330f8a2dd938,ntxv:0,isnm:False|UVIN-6554-OUT;n:type:ShaderForge.SFN_Vector1,id:4677,x:33130,y:34950,varname:node_4677,prsc:2,v1:1;n:type:ShaderForge.SFN_Vector1,id:3723,x:33130,y:35011,varname:node_3723,prsc:2,v1:0;n:type:ShaderForge.SFN_Add,id:7561,x:32737,y:34446,varname:node_7561,prsc:2|A-6638-OUT,B-3225-A;n:type:ShaderForge.SFN_ValueProperty,id:6638,x:32364,y:34550,ptovrint:False,ptlb:Diss_soft,ptin:_Diss_soft,varname:node_1901,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_If,id:692,x:33355,y:34813,varname:node_692,prsc:2|A-361-OUT,B-9424-R,GT-4677-OUT,EQ-4677-OUT,LT-3723-OUT;n:type:ShaderForge.SFN_Subtract,id:1215,x:33675,y:34875,varname:node_1215,prsc:2|A-692-OUT,B-9346-OUT;n:type:ShaderForge.SFN_Multiply,id:9359,x:33999,y:34932,varname:node_9359,prsc:2|A-1215-OUT,B-5161-RGB;n:type:ShaderForge.SFN_Color,id:5161,x:33563,y:35221,ptovrint:False,ptlb:Dissolve_color,ptin:_Dissolve_color,varname:node_7622,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.2688679,c2:0.7343383,c3:1,c4:1;n:type:ShaderForge.SFN_Add,id:4156,x:34475,y:34229,varname:node_4156,prsc:2|A-5303-OUT,B-5769-OUT;n:type:ShaderForge.SFN_Multiply,id:9226,x:33353,y:34403,varname:node_9226,prsc:2|A-692-OUT,B-7126-R;n:type:ShaderForge.SFN_Multiply,id:361,x:33023,y:34603,varname:node_361,prsc:2|A-7561-OUT,B-3225-A;n:type:ShaderForge.SFN_VertexColor,id:3225,x:31449,y:33897,varname:node_3225,prsc:2;n:type:ShaderForge.SFN_Vector1,id:9607,x:33869,y:34415,varname:node_9607,prsc:2,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:883,x:33733,y:34496,ptovrint:False,ptlb:Diss_Minswitch_Alpha,ptin:_Diss_Minswitch_Alpha,varname:_Diss_switch_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-3225-A,B-9346-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:5769,x:34250,y:34513,ptovrint:False,ptlb:Diss_Minswitch,ptin:_Diss_Minswitch,varname:_Diss_Minswitch_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-9607-OUT,B-9359-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:2177,x:34236,y:33303,ptovrint:False,ptlb:Nois_Switch,ptin:_Nois_Switch,varname:node_2177,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-2042-OUT,B-233-RGB;n:type:ShaderForge.SFN_Vector1,id:2042,x:34093,y:33182,varname:node_2042,prsc:2,v1:1;n:type:ShaderForge.SFN_TexCoord,id:5648,x:31424,y:34926,varname:node_5648,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:5810,x:31769,y:35180,varname:node_5810,prsc:2|A-4622-OUT,B-5023-OUT;n:type:ShaderForge.SFN_Add,id:6554,x:31895,y:34974,varname:node_6554,prsc:2|A-5648-UVOUT,B-5810-OUT;n:type:ShaderForge.SFN_Multiply,id:4622,x:31555,y:35125,varname:node_4622,prsc:2|A-8027-OUT,B-899-T;n:type:ShaderForge.SFN_Time,id:899,x:31213,y:35172,varname:node_899,prsc:2;n:type:ShaderForge.SFN_Multiply,id:5023,x:31555,y:35267,varname:node_5023,prsc:2|A-899-T,B-4340-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4340,x:31213,y:35395,ptovrint:False,ptlb:Dissolve_V,ptin:_Dissolve_V,varname:_Mask_v_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:8027,x:31213,y:35087,ptovrint:False,ptlb:Dissolve_U,ptin:_Dissolve_U,varname:_Mask_U_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_TexCoord,id:240,x:31918,y:33264,varname:node_240,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:9861,x:32263,y:33518,varname:node_9861,prsc:2|A-8884-OUT,B-4346-OUT;n:type:ShaderForge.SFN_Add,id:6787,x:32389,y:33312,varname:node_6787,prsc:2|A-240-UVOUT,B-9861-OUT;n:type:ShaderForge.SFN_Multiply,id:8884,x:32049,y:33463,varname:node_8884,prsc:2|A-2081-OUT,B-8131-T;n:type:ShaderForge.SFN_Time,id:8131,x:31707,y:33510,varname:node_8131,prsc:2;n:type:ShaderForge.SFN_Multiply,id:4346,x:32049,y:33605,varname:node_4346,prsc:2|A-8131-T,B-6805-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6805,x:31707,y:33733,ptovrint:False,ptlb:Mask_v_copy_copy,ptin:_Mask_v_copy_copy,varname:_Mask_v_copy_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:2081,x:31707,y:33425,ptovrint:False,ptlb:Mask_U_copy,ptin:_Mask_U_copy,varname:_Mask_U_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_TexCoord,id:5880,x:31982,y:33328,varname:node_5880,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:4799,x:32327,y:33582,varname:node_4799,prsc:2|A-4336-OUT,B-7460-OUT;n:type:ShaderForge.SFN_Add,id:4441,x:32453,y:33376,varname:node_4441,prsc:2|A-5880-UVOUT,B-4799-OUT;n:type:ShaderForge.SFN_Multiply,id:4336,x:32113,y:33527,varname:node_4336,prsc:2|A-3243-OUT,B-4618-T;n:type:ShaderForge.SFN_Time,id:4618,x:31771,y:33574,varname:node_4618,prsc:2;n:type:ShaderForge.SFN_Multiply,id:7460,x:32113,y:33669,varname:node_7460,prsc:2|A-4618-T,B-721-OUT;n:type:ShaderForge.SFN_ValueProperty,id:721,x:31771,y:33797,ptovrint:False,ptlb:Mask_v_copy_copy_copy,ptin:_Mask_v_copy_copy_copy,varname:_Mask_v_copy_copy_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:3243,x:31771,y:33489,ptovrint:False,ptlb:Mask_U_copy_copy,ptin:_Mask_U_copy_copy,varname:_Mask_U_copy_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;proporder:7780-301-2031-3694-7126-7058-6891-4817-7230-326-8723-4515-9241-6675-2177-256-233-7715-5801-2546-5769-883-6817-9424-6638-5161-4340-8027;pass:END;sub:END;*/

Shader "Core/ZTest/AlphaBlended_Two_mask222" {
    Properties {
        _Desaturate ("Desaturate", Float ) = 0
        _Intensity ("Intensity", Float ) = 1
        [HDR]_Diffuse_Color ("Diffuse_Color", Color) = (0.5,0.5,0.5,1)
        [MaterialToggle] _Alpha_Switch ("Alpha_Switch", Float ) = 0
        _Texture ("Texture", 2D) = "white" {}
        [MaterialToggle] _Custom_UV ("Custom_UV", Float ) = 0
        _U_speed ("U_speed", Float ) = 0
        _V_speed ("V_speed", Float ) = 0
        _Power ("Power", Float ) = 1
        [MaterialToggle] _Mask_Switch ("Mask_Switch", Float ) = 0
        _Mask_Tex ("Mask_Tex", 2D) = "white" {}
        _Mask_U ("Mask_U", Float ) = 0
        _Mask_v ("Mask_v", Float ) = 0
        _Fresnel ("Fresnel", Float ) = 0
        [MaterialToggle] _Nois_Switch ("Nois_Switch", Float ) = 1
        _Nois_Intensity ("Nois_Intensity", Float ) = 0
        _Nois_Tex ("Nois_Tex", 2D) = "white" {}
        _U_speed_nois ("U_speed_nois", Float ) = 0
        _V_speed_nois ("V_speed_nois", Float ) = 0
        _pianyi ("pianyi", Vector) = (0,0,0,0)
        [MaterialToggle] _Diss_Minswitch ("Diss_Minswitch", Float ) = 1
        [MaterialToggle] _Diss_Minswitch_Alpha ("Diss_Minswitch_Alpha", Float ) = 0
        [MaterialToggle] _Diss_switch ("Diss_switch", Float ) = 0
        _Dissolve_Tex ("Dissolve_Tex", 2D) = "white" {}
        _Diss_soft ("Diss_soft", Float ) = 1
        [HDR]_Dissolve_color ("Dissolve_color", Color) = (0.2688679,0.7343383,1,1)
        _Dissolve_V ("Dissolve_V", Float ) = 0
        _Dissolve_U ("Dissolve_U", Float ) = 0
        [HideInInspector]_Cutoff ("Alpha cutoff", Range(0,1)) = 0.5
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma only_renderers d3d9 d3d11 glcore gles gles3 metal d3d11_9x xboxone ps4 psp2 n3ds wiiu 
            #pragma target 3.0
            uniform sampler2D _Texture; uniform float4 _Texture_ST;
            uniform float4 _Diffuse_Color;
            uniform float _Desaturate;
            uniform float _Power;
            uniform fixed _Alpha_Switch;
            uniform float _U_speed;
            uniform float _V_speed;
            uniform fixed _Custom_UV;
            uniform sampler2D _Mask_Tex; uniform float4 _Mask_Tex_ST;
            uniform fixed _Mask_Switch;
            uniform float _Intensity;
            uniform float _Fresnel;
            uniform sampler2D _Nois_Tex; uniform float4 _Nois_Tex_ST;
            uniform float4 _pianyi;
            uniform float _U_speed_nois;
            uniform float _V_speed_nois;
            uniform float _Nois_Intensity;
            uniform float _Mask_v;
            uniform float _Mask_U;
            uniform fixed _Diss_switch;
            uniform sampler2D _Dissolve_Tex; uniform float4 _Dissolve_Tex_ST;
            uniform float _Diss_soft;
            uniform float4 _Dissolve_color;
            uniform fixed _Diss_Minswitch_Alpha;
            uniform fixed _Diss_Minswitch;
            uniform fixed _Nois_Switch;
            uniform float _Dissolve_V;
            uniform float _Dissolve_U;
            struct VertexInput {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 posWorld : TEXCOORD2;
                float3 normalDir : TEXCOORD3;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                float4 node_3598 = _Time;
                float2 node_6196 = (o.uv0+float2((_U_speed_nois*node_3598.g),(node_3598.g*_V_speed_nois)));
                float4 _Nois_Tex_var = tex2Dlod(_Nois_Tex,float4(TRANSFORM_TEX(node_6196, _Nois_Tex),0.0,0));
                v.vertex.xyz += (lerp( 1.0, _Nois_Tex_var.rgb, _Nois_Switch )*_pianyi.rgb*_Nois_Intensity);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                i.normalDir = normalize(i.normalDir);
                i.normalDir *= faceSign;
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
////// Lighting:
////// Emissive:
                float4 node_9748 = _Time;
                float2 node_4420 = (i.uv0+lerp( float2((_U_speed*node_9748.g),(node_9748.g*_V_speed)), float2(i.uv1.r,i.uv1.g), _Custom_UV ));
                float4 _Texture_var = tex2D(_Texture,TRANSFORM_TEX(node_4420, _Texture));
                float4 node_899 = _Time;
                float2 node_6554 = (i.uv0+float2((_Dissolve_U*node_899.g),(node_899.g*_Dissolve_V)));
                float4 _Dissolve_Tex_var = tex2D(_Dissolve_Tex,TRANSFORM_TEX(node_6554, _Dissolve_Tex));
                float node_9346_if_leA = step(i.vertexColor.a,_Dissolve_Tex_var.r);
                float node_9346_if_leB = step(_Dissolve_Tex_var.r,i.vertexColor.a);
                float node_3723 = 0.0;
                float node_4677 = 1.0;
                float node_9346 = lerp((node_9346_if_leA*node_3723)+(node_9346_if_leB*node_4677),node_4677,node_9346_if_leA*node_9346_if_leB);
                float node_692_if_leA = step(((_Diss_soft+i.vertexColor.a)*i.vertexColor.a),_Dissolve_Tex_var.r);
                float node_692_if_leB = step(_Dissolve_Tex_var.r,((_Diss_soft+i.vertexColor.a)*i.vertexColor.a));
                float node_692 = lerp((node_692_if_leA*node_3723)+(node_692_if_leB*node_4677),node_4677,node_692_if_leA*node_692_if_leB);
                float3 emissive = ((lerp(_Texture_var.rgb,dot(_Texture_var.rgb,float3(0.3,0.59,0.11)),_Desaturate)*_Diffuse_Color.rgb*i.vertexColor.rgb*lerp( i.vertexColor.a, node_9346, _Diss_Minswitch_Alpha ))+lerp( 1.0, ((node_692-node_9346)*_Dissolve_color.rgb), _Diss_Minswitch ));
                float3 finalColor = emissive;
                float4 node_486 = _Time;
                float2 node_4672 = (i.uv0+float2((_Mask_U*node_486.g),(node_486.g*_Mask_v)));
                float4 _Mask_Tex_var = tex2D(_Mask_Tex,TRANSFORM_TEX(node_4672, _Mask_Tex));
                return fixed4(finalColor,(((pow(lerp( _Texture_var.r, _Texture_var.a, _Alpha_Switch ),_Power)*lerp( _Mask_Tex_var.r, _Mask_Tex_var.a, _Mask_Switch ))*_Diffuse_Color.a)*pow(1.0-max(0,dot(normalDirection, viewDirection)),_Fresnel)*_Intensity*lerp( i.vertexColor.a, (node_692*_Texture_var.r), _Diss_switch )));
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
