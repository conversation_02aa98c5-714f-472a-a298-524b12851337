﻿using System.Threading;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class LoadingTask
    {
        public LoadingTask(System.Action action)
        {
            Debug.Assert(action != null);
            mAction = action;
        }

        public void Run()
        {
            mAction();
        }

        System.Action mAction;
    }

    public static class LoadingTaskManager
    {
        //called in main thread
        public static void Init()
        {
            Debug.Assert(mTaskThread == null);
            mTasks = new List<LoadingTask>();
            mQuitEvent = new ManualResetEvent(false);
            mTaskThread = new Thread(Update);
            mTaskThread.Start();
        }

        //kill thread
        public static void Uninit()
        {
            lock (mTasks)
            {
                Debug.Assert(mTasks.Count == 0, "there are still tasks running, please make sure call this after all tasks are finished!");
            }
            
            mQuitEvent.Set();
            mTaskThread = null;
        }

        //called in main thread
        public static void AddTask(LoadingTask task)
        {
            if (mTaskThread == null)
            {
                task.Run();
                return;
            }

            lock (mTasks)
            {
                mTasks.Add(task);   
            }
        }

        //called in main thread
        public static void Wait()
        {
            if (mTaskThread != null)
            {
                lock (mTasks)
                {
                    for (int i = 0; i < mTasks.Count; ++i)
                    {
                        mTasks[i].Run();
                    }
                    mTasks.Clear();
                }
                mQuitEvent.Set();
                mTaskThread.Join();
                mTaskThread = null;
            }
        }

        //running in working thread
        static void Update()
        {
            while (true)
            {
                LoadingTask task = null;
                lock (mTasks)
                {
                    if (mTasks.Count > 0)
                    {
                        task = mTasks[mTasks.Count - 1];
                        mTasks.RemoveAt(mTasks.Count - 1);
                    }
                }
                if (task != null)
                {
                    task.Run();
                }

                bool quit = mQuitEvent.WaitOne(0);
                if (quit)
                {
                    mQuitEvent = null;
                    break;
                }
            }
        }

        //调用isFinished之前必须要保证先调用了MapMgr.LoadMap或MapMgr.LoadAssociateMap地图
        public static bool isLoadingFinished {
            get
            {
                bool finished = false;
                lock (mTasks)
                {
                    finished = mTasks.Count == 0;
                }
                return finished;
            } 
        }

        static ManualResetEvent mQuitEvent;
        static Thread mTaskThread;
        static List<LoadingTask> mTasks;
    }
}
