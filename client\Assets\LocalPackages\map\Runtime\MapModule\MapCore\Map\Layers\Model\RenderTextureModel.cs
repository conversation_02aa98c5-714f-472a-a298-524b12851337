﻿ 



 
 

﻿/*
 * created by wzw at 2019.7.17
 */

using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //使用render texture来替代模型的model,render texture渲染自model template
    public class RenderTextureModel : ModelBase
    {
        public void Init(int modelTemplateID, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            mModelTemplateID = modelTemplateID;
            //从对象池中拿出一个模型
            mGameObject = Map.currentMap.view.modelTemplateRenderToTextureGameObjectManager.Require(modelTemplateID, position, scale, rotation);

            Utils.HideGameObject(mGameObject);
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
            var map = Map.currentMap;
            if (mGameObject != null)
            {
                map.view.modelTemplateRenderToTextureGameObjectManager.Release(mModelTemplateID, mGameObject);
            }
        }

        public override void Release()
        {
            ReleaseToPool(this);
        }

        public static RenderTextureModel Require(int modelTemplateID, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            var model = mPool.Require();
            model.Init(modelTemplateID, position, scale, rotation);
            return model;
        }

        static void ReleaseToPool(RenderTextureModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        int mModelTemplateID;
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<RenderTextureModel> mPool = new ObjectPool<RenderTextureModel>(1000, ()=>new RenderTextureModel());
    }
}
