﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.12.12
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum ComplexGridModelOperationType
    {
        kSelectObject,
        kCreateObject,
        kRemoveObject,
        kSelectObjectInRange,
    }

    [ExecuteInEditMode]
    [Black]
    public class ComplexGridModelLayerLogic : MapLayerLogic
    {
        public enum PanelOperation
        {
            PlaceOneObject,
            PlaceMultipleObjects,
        }

        public string selectedPrefabGUID { set; get; }
        public ComplexGridModelOperationType operationType { set; get; }
        public int selectedLOD = 0;
        public GameObject prefab;
        public bool viewportSettingFoldState = true;
        public PanelOperation panelOperation = PanelOperation.PlaceOneObject;
        public PrefabRotationSetting rotationSetting = new PrefabRotationSetting();

        public string selectedObjectTag
        {
            get
            {
                if (mSelectedObjectTag >= 0)
                {
                    return mObjectTagNames[mSelectedObjectTag];
                }
                return "";
            }
        }

        public ComplexGridObjectLayerData layerData
        {
            get
            {
                var layerData = Map.currentMap.FindObject(layerID) as ComplexGridObjectLayerData;
                return layerData;
            }
        }

        public EditorComplexGridModelLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as EditorComplexGridModelLayer;
                return layer;
            }
        }

        void OnDestroy()
        {
            mRotationSetting.OnDestroy();
        }

        public Vector3 CalculatePosAlignedToGrid(Vector3 pos)
        {
            if (panelOperation == PanelOperation.PlaceMultipleObjects)
            {
                return pos;
            }

            float gridSize = layerData.objectPlacementSetting.alignGridSize;
            int gridX = Mathf.FloorToInt(pos.x / gridSize);
            int gridY = Mathf.FloorToInt(pos.z / gridSize);
            return new Vector3(gridX * gridSize + 0.5f * gridSize, 0, gridY * gridSize + 0.5f * gridSize);
        }

        public void DrawObjectTagUI()
        {
            mTagSettingFoldState = EditorGUILayout.Foldout(mTagSettingFoldState, "Tag Setting");

            if (mTagSettingFoldState)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                EditorGUILayout.LabelField("You can give every object a tag, only objects with visible tag will be displayed in map editor!");
                if (mObjectTagNames != null && mObjectTagNames.Length > 0)
                {
                    EditorGUILayout.BeginHorizontal("");
                    EditorGUIUtility.fieldWidth = 200;
                    mSelectedObjectTag = EditorGUILayout.Popup("Object Tags", mSelectedObjectTag, mObjectTagNames);
                    EditorGUIUtility.fieldWidth = 0;
                    if (mSelectedObjectTag >= 0)
                    {
                        bool isTagVisible = layer.IsTagVisible(mObjectTagNames[mSelectedObjectTag]);
                        bool newVisibleState = EditorGUILayout.ToggleLeft("Visible", isTagVisible);
                        if (newVisibleState != isTagVisible)
                        {
                            layer.SetObjectsOfTagVisible(mObjectTagNames[mSelectedObjectTag], newVisibleState);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Tag"))
                {
                    AddTag();
                }
                if (mObjectTagNames != null)
                {
                    if (GUILayout.Button("Remove Tag"))
                    {
                        RemoveTag();
                    }
                }
                EditorGUILayout.EndHorizontal();
                if (GUILayout.Button("Remove All LOD Objects Of Selected Tag"))
                {
                    RemoveObjectsOfTag(false);
                }

                if (GUILayout.Button("Remove Current LOD Objects Of Selected Tag"))
                {
                    RemoveObjectsOfTag(true);
                }
                EditorGUILayout.EndVertical();
            }
        }

        void AddTag()
        {
            var dlg = EditorUtils.CreateInputDialog("Add New Tag");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "NewTag"),
                };
            dlg.Show(items, OnClickAddObjectTag);
        }

        bool OnClickAddObjectTag(List<InputDialog.Item> parameters)
        {
            string tag = (parameters[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(tag))
            {
                return false;
            }

            bool added = layer.AddObjectTag(tag, true);
            if (!added)
            {
                return false;
            }
            mSelectedObjectTag = layer.GetObjectTags().Length - 1;
            mObjectTagNames = layer.GetObjectTags();
            return true;
        }

        void RemoveTag()
        {
            layer.RemoveObjectTag(mObjectTagNames[mSelectedObjectTag]);
            mObjectTagNames = layer.GetObjectTags();
            if (mObjectTagNames.Length == 0)
            {
                mSelectedObjectTag = -1;
            }
            else
            {
                mSelectedObjectTag = mSelectedObjectTag - 1;
                mSelectedObjectTag = Mathf.Max(0, mSelectedObjectTag);
            }
        }

        void RemoveObjectsOfTag(bool currentLOD)
        {
            string tag = mObjectTagNames[mSelectedObjectTag];
            var objects = layer.GetObjectsOfTag(currentLOD ? selectedLOD : -1, tag);
            for (int i = 0; i < objects.Count; ++i)
            {
                layer.RemoveObject(objects[i].GetEntityID());
            }

            ActionManager.instance.Clear();
        }

        public string GetObjectTag()
        {
            if (mSelectedObjectTag >= 0)
            {
                return mObjectTagNames[mSelectedObjectTag];
            }

            return "";
        }

        public void InitTags()
        {
            mObjectTagNames = layer.GetObjectTags();
            if (mObjectTagNames.Length > 0)
            {
                mSelectedObjectTag = 0;
            }
            else
            {
                mSelectedObjectTag = -1;
            }

            layer.layerData.UpdateObjectVisibleStateByTag();
        }

        int mSelectedObjectTag = -1;
        string[] mObjectTagNames;
        bool mTagSettingFoldState = true;
        PrefabRotationSetting mRotationSetting = new PrefabRotationSetting();
    }
}

#endif