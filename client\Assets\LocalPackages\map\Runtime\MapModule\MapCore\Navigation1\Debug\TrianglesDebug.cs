﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map.Nav
{
    public partial class NavigationDebugger
    {
        class TriangleObject
        {
            public GameObject obj;
            public int index;
        }

        public void HighlightTriangle(Geo.Coord pos)
        {
            var obj = GetTriangleObject(pos);
            if (obj != null && obj != mHighlightedTriangle)
            {
                if (mHighlightedTriangle != null)
                {
                    DisableTriangleHighlighting();
                }
                EnableTriangleHighlighting(obj);
            }
        }

        public void ShowTriangle(Geo.Coord pos)
        {
            var index = GetTriangleIndex(pos);
            for (int i = 0; i < mTriangles.Count; ++i)
            {
                if (mTriangles[i].index == index)
                {
                    mTriangles[i].obj.SetActive(true);
                }
            }
        }

        public void HideAllTriangles()
        {
            DisableTriangleHighlighting();
            for (int i = 0; i < mTriangles.Count; ++i)
            {
                mTriangles[i].obj.SetActive(false);
            }
        }

        public void ShowAllTriangles()
        {
            DisableTriangleHighlighting();
            for (int i = 0; i < mTriangles.Count; ++i)
            {
                mTriangles[i].obj.SetActive(true);
            }
        }

        int GetTriangleIndex(Geo.Coord pos)
        {
            var triangles = mNavMgr.mesh.triangles;
            for (int i = 0; i < triangles.Length; ++i)
            {
                if (triangles[i].IsCoordInside(pos))
                {
                    return triangles[i].Index;
                }
            }
            return -1;
        }

        TriangleObject GetTriangleObject(Geo.Coord pos)
        {
            var idx = GetTriangleIndex(pos);
            if (idx >= 0)
            {
                for (int i = 0; i < mTriangles.Count; ++i)
                {
                    if (mTriangles[i].index == idx)
                    {
                        return mTriangles[i];
                    }
                }
            }
            return null;
        }

        void DestroyTriangles()
        {
            for (int i = 0; i < mTriangles.Count; ++i)
            {
                var mesh = mTriangles[i].obj.GetComponent<MeshFilter>().sharedMesh;
                var mtl = mTriangles[i].obj.GetComponent<MeshRenderer>().sharedMaterial;
                GameObject.DestroyImmediate(mesh);
                GameObject.DestroyImmediate(mtl);
                GameObject.DestroyImmediate(mTriangles[i].obj);
            }
            mTriangles = null;
        }

        void CreateTriangles(GameObject root)
        {
            var mesh = mNavMgr.mesh;
            var triangles = mesh.triangles;
            for (int i = 0; i < triangles.Length; ++i)
            {
                mTriangles.Add(CreateTriangleObject(triangles[i], root));
            }
        }

        TriangleObject CreateTriangleObject(Geo.Triangle t, GameObject root)
        {
            string name;
            Vector3 center;
            var mesh = CreateTriangleMesh(t, out center, out name);
            var obj = new GameObject(name);
            obj.transform.parent = root.transform;
            obj.SetActive(false);
            var filter = obj.AddComponent<MeshFilter>();
            var renderer = obj.AddComponent<MeshRenderer>();

            filter.sharedMesh = mesh;
            renderer.sharedMaterial = CreateTriangleMaterial();
            TriangleObject tri = new TriangleObject();
            tri.index = t.Index;
            tri.obj = obj;
            center.y = 1.0f;
            obj.transform.position = center;

            Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
            var textObj = new GameObject();
            var textObjRenderer = textObj.AddComponent<MeshRenderer>();
            textObjRenderer.sharedMaterial = ArialFont.material;
            var textMesh = textObj.AddComponent<TextMesh>();
            textMesh.text = $"{t.Index}";
            textMesh.fontSize = 32;
            textObj.transform.parent = obj.transform;
            textObj.transform.position = obj.transform.position;
            textObj.transform.rotation = Quaternion.Euler(70, 0, 0);
            textObj.transform.localScale = Vector3.one * 10.0f;
            textObj.SetActive(false);
            return tri;
        }

        UnityEngine.Mesh CreateTriangleMesh(Geo.Triangle t, out Vector3 center, out string name)
        {
            UnityEngine.Mesh m = new UnityEngine.Mesh();
            var allVertices = Mesh.currentMeshVertices;
            Vector3[] vertices = new Vector3[3]
            {
                Geo.GeoUtils.CoordToVector3(allVertices[t.v0].Coord),
                Geo.GeoUtils.CoordToVector3(allVertices[t.v1].Coord),
                Geo.GeoUtils.CoordToVector3(allVertices[t.v2].Coord),
            };

            name = $"triangle {t.Index}, v0: {vertices[0].ToString()} v1: {vertices[1].ToString()} v2: {vertices[2].ToString()}";

            center = Vector3.zero;
            for (int i = 0; i < vertices.Length; ++i)
            {
                center += vertices[i];
            }
            center /= 3;
            for (int i = 0; i < vertices.Length; ++i)
            {
                vertices[i] -= center;
            }
            m.vertices = vertices;
            m.triangles = new int[] { 0, 1, 2 };

            return m;
        }

        Material CreateTriangleMaterial()
        {
            var mtl = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            var color = Color.yellow;
            color.a = 0.3f;
            mtl.color = color;
            mtl.renderQueue = 4999;
            return mtl;
        }

        void EnableTriangleHighlighting(TriangleObject obj)
        {
            //Debug.Log($"Enable Highlight {obj.index}");
            Debug.Assert(mHighlightedTriangle == null);
            mHighlightedTriangle = obj;
            var mtl = obj.obj.GetComponent<MeshRenderer>().sharedMaterial;
            mTriangleDefaultColor = mtl.color;
            var triangles = mNavMgr.mesh.triangles;
            //if (triangles[obj.index].IsWalkable())
            {
                mtl.color = mTriangleHighlightColor;
            }
            //else
            //{
            //    mtl.color = mObstacleTriangleHighlightColor;
            //}
            mHighlightedTriangle.obj.SetActive(true);
        }

        public void DisableTriangleHighlighting()
        {
            if (mHighlightedTriangle != null)
            {
                //Debug.Log($"Disable Highlight {mHighlightedTriangle.index}");
                mHighlightedTriangle.obj.GetComponent<MeshRenderer>().sharedMaterial.color = mTriangleDefaultColor;
                mHighlightedTriangle = null;
            }
        }

        Color mTriangleHighlightColor = new Color(1, 0, 0, 0.3f);
        Color mObstacleTriangleHighlightColor = new Color(0, 0, 0, 0.7f);
        Color mTriangleDefaultColor;
        List<TriangleObject> mTriangles = new List<TriangleObject>();
        TriangleObject mHighlightedTriangle;
    }
}

#endif