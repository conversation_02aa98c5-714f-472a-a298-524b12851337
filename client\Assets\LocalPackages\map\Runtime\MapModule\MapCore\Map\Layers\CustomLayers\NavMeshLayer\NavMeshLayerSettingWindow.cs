﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map {
    public class NavMeshLayerSettingWindow : EditorWindow {
        void OnEnable() {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI() {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Map Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Map Height", mLayerHeight);
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Create")) {
                var map = Map.currentMap as EditorMap;
                map.CreateNavMeshLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH, mLayerWidth, mLayerHeight);
                
                Close();
            }
        }

        bool CheckParameter() {
            if (mLayerWidth <= 0 || mLayerHeight <= 0) {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
    }
}

#endif