﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public class CityTerritoryView : ModelView
    {
        public CityTerritoryView(IMapObjectData data, MapLayerView layerView)
            : base(data, layerView)
        {
        }

        public void SetMaterial(Material mtl)
        {
            if (!(mModel is SharedModel))
            {
                mModel.gameObject.GetComponent<MeshRenderer>().sharedMaterial = mtl;
            }
        }

        public override void CreateModel(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);

            //由子类来决定所使用的模型的类型
            mModel = CreateModelInternal(data, newLOD);
        }

        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            ModelBase model = null;
            if (newLOD == 0)
            {
                if (data.GetModelTemplate() == null)
                {
                    model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                }
                else
                {
                    model = base.CreateModelInternal(data, newLOD);
                }
            }
            else
            {
                //create shared model for lod1
                Debug.Assert(mModel == null);

                bool active = data.IsObjActive();
                if (active)
                {
                    var cityLayerView = layerView as CityTerritoryLayerView;
                    model = cityLayerView.RequireSharedModel(data.GetEntityID());
                    if (model != null)
                    {
                        OnModelReady(model);
                    }
                    else
                    {
                        model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                    }
                }
                else
                {
                    model = ModelPlaceholder.Require(data.GetPosition(), data.GetScale(), data.GetRotation());
                    OnModelPlaceholderReady(model);
                }
            }

            if (!(model is ModelPlaceholder))
            {
                var cityData = data as CityTerritoryDataBase;
                var renderer = model.gameObject.GetComponent<MeshRenderer>();
                if (newLOD == 1)
                {
                    renderer.sharedMaterial.SetTexture("_Mask", cityData.maskTexture);
                }
                else
                {
                    renderer.sharedMaterial = cityData.material;
                    if (renderer.sharedMaterial != null)
                    {
                        renderer.sharedMaterial.color = cityData.color;
                    }
                }
            }

            return model;
        }

        protected override int CalculateLOD()
        {
            return layerView.layerData.currentLOD;
        }
    }
}
