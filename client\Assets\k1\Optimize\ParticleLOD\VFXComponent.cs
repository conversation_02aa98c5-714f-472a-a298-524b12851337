using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
#endif

namespace NexgenDragon
{
    public enum QualityLevel
    {
        NONE = -1,
        LOW = 0,
        MIDDLE,
        HIGH,
        VERYHIGH,
        SUPER_LOW = 99,
    }

    [ExecuteInEditMode]
#if UNITY_EDITOR
    public class VFXComponent : SerializedMonoBehaviour
#else
    public class VFXComponent : MonoBehaviour
#endif
    {
        public static Action<VFXComponent> s_OpenAction;


#if UNITY_EDITOR
        [Title("Objs")]
#endif
        [HideInInspector]
        public Renderer[] Objects;

#if UNITY_EDITOR
        [Title("ObjTags")]
#endif
        [HideInInspector]
        public int[] ObjectTags;

        public Dictionary<Renderer, int> CacheObjectTag = new Dictionary<Renderer, int>();

        /// <summary>
        /// 参考 GFXVFXPanel
        /// </summary>
        public static bool s_StatisticsEnable = false;

        public static int s_vfxCount = 0;
        public static int s_vfxCountView = 0;
        public static Vector4 viewBound = Vector4.zero;
        public static float viewBoundSphereRadius = 10.0f;
        public static int s_FullVFXMaxCount = 50;
        public static int s_VFXMaxCountToRadomThrowAway = 200;
        public static int s_RandomRadioInv = 5;
        public static bool s_bUseLow = false;
        public static bool s_bUseViewBoundCulling = false;
        public static bool s_bUseFunSceneViewBound = true;
        public int currentVFXCount = 0;
        static bool s_ViewBoundDirty = true;
        static bool s_bRadomThrowAway = false;


        private void OnEnable()
        {
            if (s_bRadomThrowAway)
            {
                int r = UnityEngine.Random.Range(1, s_RandomRadioInv);
                if (r != 1)
                    gameObject.SetActive(false);
            }
            GetObjects();
            if (Application.isPlaying)
            {
                if (s_bUseLow)
                {
                    _SetByLevel(QualityLevel.LOW);
                }
                else
                {
                    var l = PerformanceModule.currentLODLevel;
                    _SetByLevel(l);
                }
            }
            else
            {
#if UNITY_EDITOR
                _AddAllGameobjs();
                BuildMenuTree();
#endif
            }
        }

        public Vector4 ViewBound = new Vector4(1, 1, 1, 1);

        static public void SetViewBound(Vector4 b)
        {
            viewBound = b;
            viewBound.x -= viewBoundSphereRadius;
            viewBound.y -= viewBoundSphereRadius;
            viewBound.z += viewBoundSphereRadius * 2.0f;
            viewBound.w += viewBoundSphereRadius * 2.0f;
        }

        // Update is called once per frame
        void Update()
        {
            if (!s_StatisticsEnable)
                return;
            if (!s_bUseViewBoundCulling)
            {
                s_vfxCount++;
                return;
            }

            if (s_bUseFunSceneViewBound && s_ViewBoundDirty)
            {
                SetViewBound(ViewBound);
                s_ViewBoundDirty = false;
            }

            Vector3 pos = transform.position;
            if (pos.x > viewBound.x && pos.x < viewBound.x + viewBound.z
                                    && pos.z > viewBound.y && pos.z < viewBound.y + viewBound.w)
            {
                s_vfxCount++;
            }
        }

        void LateUpdate()
        {
            if (!s_StatisticsEnable)
            {
                s_bRadomThrowAway = false;
                s_bUseLow = false;
                return;
            }

            s_bUseLow = s_vfxCount > s_FullVFXMaxCount;
            s_bRadomThrowAway = s_vfxCount > s_VFXMaxCountToRadomThrowAway;
            currentVFXCount = s_vfxCount;
            s_vfxCountView = s_vfxCount;
            s_ViewBoundDirty = false;
        }

        private void OnRenderObject()
        {
            s_vfxCount = 0;
        }

        private void GetObjects()
        {
            var renderers = GetComponentsInChildren<Renderer>(true);
            // 过滤掉根物体
            Objects = renderers.Where(renderer => renderer.gameObject != this.gameObject).ToArray();
        }

        private void SkipObjectTags()
        {
            if (Objects == null)
            {
                GetObjects();
            }

            if (Objects == null)
            {
                return;
            }

            if (ObjectTags != null && Objects.Length == ObjectTags.Length)
            {
                return;
            }

            int[] newObjectTags = new int[Objects.Length];

            if (ObjectTags == null)
            {
            }
            else if (Objects.Length > ObjectTags.Length)
            {
                // Objects.Length 比 ObjectTags.Length 长时
                for (int i = 0; i < ObjectTags.Length; i++)
                {
                    newObjectTags[i] = ObjectTags[i];
                }
            }
            else
            {
                // Objects.Length 比 ObjectTags.Length 短时，从后往前取
                int startIndex = ObjectTags.Length - Objects.Length;
                for (int i = 0; i < Objects.Length; i++)
                {
                    newObjectTags[i] = ObjectTags[startIndex + i];
                }
            }

            ObjectTags = newObjectTags;
        }


        void _SetByLevel(QualityLevel level)
        {
            if (Objects != null && ObjectTags != null)
            {
                if (Objects.Length != ObjectTags.Length)
                {
                    GetObjects();
                    SkipObjectTags();
                }

                for (int i = 0; i < Objects.Length; i++)
                {
                    bool isShow = (int)level >= ObjectTags[i];
                    if (Objects[i])
                    {
                        Objects[i].enabled = isShow;
                    }
                }
            }
        }

#if UNITY_EDITOR

        private bool isHotKey = false;

        [OnInspectorGUI]
        void __Tree()
        {
            // Draw stuff
            if (lastTree == null)
            {
                return;
            }

            lastTree.DrawMenuTree();
            lastTree.HandleKeybaordMenuNavigation();
            isHotKey = CheckKeyDown(KeyCode.O);
            if (isHotKey)
            {
                __PlaySingle();
                isHotKey = false;
            }
        }

        bool CheckKeyDown(KeyCode keyCode)
        {
            Event e = Event.current;

#if UNITY_2017_3_OR_NEWER
            if (e.type == EventType.KeyDown)
#else
            if (e.type == EventType.keyDown)
#endif
            {
                return e.keyCode == keyCode;
            }

            return false;
        }

        /*
        [Button("编辑")]
        void _EditVFX()
        {
            s_OpenAction?.Invoke(this);
        }
        */

        [BoxGroup("设置")]
        [HorizontalGroup("设置/H")]
        [Button("刷新")]
        void __SetRefresh()
        {
            BuildMenuTree();
        }

        [BoxGroup("设置")]
        [HorizontalGroup("设置/H")]
        [Button("设成超高")]
        void __SetVeryHigh()
        {
            if (lastSelects != null)
            {
                foreach (var ls in lastSelects)
                {
                    if (ls != null)
                    {
                        CacheObjectTag[ls] = (int)QualityLevel.VERYHIGH;
                    }
                }
            }

            __Sync();
        }

        [BoxGroup("设置")]
        [HorizontalGroup("设置/H")]
        [Button("设成高")]
        void __SetHigh()
        {
            if (lastSelects != null)
            {
                foreach (var ls in lastSelects)
                {
                    if (ls != null)
                    {
                        CacheObjectTag[ls] = (int)QualityLevel.HIGH;
                    }
                }
            }

            __Sync();
        }

        [BoxGroup("设置")]
        [HorizontalGroup("设置/H")]
        [Button("设成中")]
        void __SetMid()
        {
            if (lastSelects != null)
            {
                foreach (var ls in lastSelects)
                {
                    if (ls != null)
                    {
                        CacheObjectTag[ls] = (int)QualityLevel.MIDDLE;
                    }
                }
            }

            __Sync();
        }

        [BoxGroup("设置")]
        [HorizontalGroup("设置/H")]
        [Button("设成低")]
        void __SetLow()
        {
            if (lastSelects != null)
            {
                foreach (var ls in lastSelects)
                {
                    if (ls != null)
                    {
                        CacheObjectTag[ls] = (int)QualityLevel.LOW;
                    }
                }
            }

            __Sync();
        }


        ParticleSystem system
        {
            get
            {
                if (_CachedSystem == null)
                    _CachedSystem = GetComponent<ParticleSystem>();
                return _CachedSystem;
            }
        }

        private ParticleSystem _CachedSystem;

        private bool isAddonPs = false;
        private ParticleSystem addOnPs = null;

        void __PlayParticle(bool isPlay = true)
        {
            if (system != null)
            {
                system.Clear(true);
                if (isPlay)
                {
                    system.Play(true);
                }
            }
            else if (addOnPs == null)
            {
                addOnPs = gameObject.AddComponent<ParticleSystem>();
                addOnPs.GetComponent<Renderer>().enabled = false;
                var ae = addOnPs.emission;
                ae.enabled = false;

                if (!isPlay)
                {
                    addOnPs.Clear(true);
                    addOnPs.Play(true);
                }

                isAddonPs = true;
            }
        }

        [BoxGroup("播放")]
        [HorizontalGroup("播放/H")]
        [Button("播放超高")]
        void __PlayVeryHigh()
        {
            _SetByLevel(QualityLevel.VERYHIGH);
            __PlayParticle();
        }

        [BoxGroup("播放")]
        [HorizontalGroup("播放/H")]
        [Button("播放高")]
        void __PlayHigh()
        {
            _SetByLevel(QualityLevel.HIGH);
            __PlayParticle();
        }

        [BoxGroup("播放")]
        [HorizontalGroup("播放/H")]
        [Button("播放中")]
        void __PlayMid()
        {
            _SetByLevel(QualityLevel.MIDDLE);
            __PlayParticle();
        }

        [BoxGroup("播放")]
        [HorizontalGroup("播放/H")]
        [Button("播放低")]
        void __PlayLow()
        {
            _SetByLevel(QualityLevel.LOW);
            __PlayParticle();
        }

        [BoxGroup("播放")]
        [HorizontalGroup("播放/H")]
        [Button("重置")]
        void __PlayReset()
        {
            _AddAllGameobjs(true);
            BuildMenuTree();
        }

        void __DisableAll()
        {
            foreach (var qc in queryCollected)
            {
                qc.Key.enabled = false;
            }
        }


        void __SetLayer(QualityLevel l)
        {
            __DisableAll();

            foreach (var kv in CacheObjectTag)
            {
                kv.Key.enabled = kv.Value >= (int)l;
            }

            gameObject.SetActive(true);
        }
        /*
        [BoxGroup("播放单层")]
        [HorizontalGroup("播放单层/H")]
        [Button("播放高")]
        void __PlayHighL()
        {
            __SetLayer(PerformanceModule.QualityLevel.HIGH);
            system.Stop(true);
            system.Play(true);
        }

        [BoxGroup("播放单层")]
        [HorizontalGroup("播放单层/H")]
        [Button("播放中")]
        void __PlayMidL()
        {
            __SetLayer(PerformanceModule.QualityLevel.MIDDLE);
            system.Stop(true);
            system.Play(true);
        }

        [BoxGroup("播放单层")]
        [HorizontalGroup("播放单层/H")]
        [Button("播放低")]
        void __PlayLowL()
        {
            __SetLayer(PerformanceModule.QualityLevel.LOW);
            system.Stop(true);
            system.Play(true);
        }
        */

        private ParticleSystem lastPs;

        [BoxGroup("特殊")]
        [HorizontalGroup("特殊/H")]
        [Button("播放单个选中(O)")]
        void __PlaySingle()
        {
            __PlayParticle(false);
            if (lastSelects == null) return;
            lastSelects.ForEach((t) =>
            {
                if (t != null)
                {
                    t.enabled = true;
                    t.GetComponent<ParticleSystem>()?.Play(true);
                }
            });

            //var pss = lastSelects.Select(t => t.GetComponent<Renderer>()).Where(t => t != null).ToList();
            //foreach (var ps in pss)
            //{
            //    ps.enabled = true;
            //    ps.GetComponent<ParticleSystem>()?.Play(true);
            //}
        }

        [BoxGroup("特殊")]
        [HorizontalGroup("特殊/H")]
        [Button("保存粒子(Save)")]
        void _SavePartilceSys()
        {
            if (addOnPs != null && isAddonPs)
            {
                DestroyImmediate(addOnPs);
            }

            PrefabUtility.ApplyPrefabInstance(gameObject, InteractionMode.AutomatedAction);
        }

        struct MPair
        {
            public string menuPath;
            public Renderer menuObject;
            public int idx; //childIndex
        }

        List<MPair> collected = new List<MPair>();

        private Dictionary<Renderer, string> queryCollected = new Dictionary<Renderer, string>();
        private Dictionary<Renderer, bool> initStatus = new Dictionary<Renderer, bool>();

        void __ReCollect(GameObject root)
        {
            collected.Clear();
            queryCollected.Clear();
            bool isCollectStatus = initStatus.Count == 0;
            __RecurseAdd(root, "", isCollectStatus, 0);
        }

        //MAX+ :idx表示此trm的子类的idx，idx是-1表示是根目录,0701宇航需求，要显示完整的子subParticle，之前有错误
        void __RecurseAdd(GameObject root, string parent, bool isCollectStatus, int idx)
        {
            var mPath = !string.IsNullOrEmpty(parent) ? $"{parent}/{root.name}" : root.name;
            var mObj = root.GetComponent<Renderer>();
            if (mObj != null)
            {
                mPath += _GetLODTag(mObj) + "-(" + idx + ")"; //MAX+

                collected.Add(new MPair() { menuObject = mObj, menuPath = mPath, idx = idx });
                var lastIndex = mPath.LastIndexOf('/');
                queryCollected[mObj] = lastIndex > 0 ? mPath.Substring(lastIndex + 1) : mPath;
                if (isCollectStatus)
                {
                    initStatus[mObj] = mObj.enabled;
                }
            }


            for (int i = 0; i < root.transform.childCount; i++)
            {
                var tr = root.transform.GetChild(i);
                __RecurseAdd(tr.gameObject, mPath, isCollectStatus, i);
            }
        }

        string _GetLODTag(Renderer root)
        {
            if (CacheObjectTag.TryGetValue(root, out var tag))
            {
                string tagStr = "-";
                if (tag >= (int)QualityLevel.VERYHIGH)
                {
                    tagStr += "- [超高]";
                }
                else if (tag >= (int)QualityLevel.HIGH)
                {
                    tagStr += "- [高]";
                }
                else if (tag >= (int)QualityLevel.MIDDLE)
                {
                    tagStr += "- [中]";
                }
                else
                {
                    tagStr += "- [低]";
                }

                return tagStr;
            }

            return "";
        }


        protected void _AddAllGameobjs(bool isForce = false)
        {
            GetObjects();
            if (isForce)
            {
                ObjectTags = null;
            }

            SkipObjectTags();
            CacheObjectTag = new Dictionary<Renderer, int>();
            for (int i = 0; i < ObjectTags.Length; i++)
            {
                CacheObjectTag[Objects[i]] = ObjectTags[i];
            }
        }


        private OdinMenuTree lastTree;

        protected OdinMenuTree BuildMenuTree()
        {
            OdinMenuTree nTree = new OdinMenuTree();
            nTree.DefaultMenuStyle = OdinMenuStyle.TreeViewStyle;


            __ReCollect(gameObject);

            foreach (var kv in collected)
            {
                //Debug.Log(kv.menuPath+"                ::"+kv.menuObject.name);
                nTree.Add(kv.menuPath, kv.menuObject);
            }

            nTree.Selection.SelectionChanged += OnChangedMenuItem;
            nTree.Selection.SupportsMultiSelect = true;

            lastTree = nTree;


            var menuItems = new List<OdinMenuItem>();
            {
                void __RecurseAddItems(OdinMenuItem root)
                {
                    menuItems.Add(root);
                    foreach (var t in root.ChildMenuItems)
                    {
                        __RecurseAddItems(t);
                    }
                }

                __RecurseAddItems(nTree.RootMenuItem);
            }


            menuItems.ForEach(t => t.OnDrawItem = item =>
            {
                var qObj = item.Value as Renderer;
                if (qObj != null)
                {
                    queryCollected.TryGetValue(qObj, out var tName);
                    item.Name = tName;
                }
            });


            return nTree;
        }


        private Renderer[] lastSelects = null;

        void OnChangedMenuItem(SelectionChangedType scType)
        {
            if (lastTree.Selection.SelectedValues != null)
            {
                lastSelects = lastTree.Selection.SelectedValues.ToList().Select(t => (Renderer)t).ToArray();
                UnityEngine.Debug.Log($"changed menu item {scType} -> {lastSelects}");
            }
        }

        void __Sync()
        {
            var count = CacheObjectTag.Count;
            Objects = new Renderer[count];
            ObjectTags = new int[count];
            int i = 0;
            foreach (var kvp in CacheObjectTag)
            {
                Objects[i] = kvp.Key;
                ObjectTags[i] = kvp.Value;
                i++;
            }

            //__SetRefresh();
            __ReCollect(gameObject);
        }
#endif
    }
}