﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class ActionWindow : EditorWindow
    {
        void OnEnable()
        {
            var actionManager = ActionManager.instance;
            if (actionManager != null)
            {
                actionManager.createActionEvent -= OnCreateAction;
                actionManager.removeActionEvent -= OnRemoveAction;
                actionManager.setActionPointerEvent -= OnSetActionPointer;

                actionManager.createActionEvent += OnCreateAction;
                actionManager.removeActionEvent += OnRemoveAction;
                actionManager.setActionPointerEvent += OnSetActionPointer;
            }
        }

        void OnCreateAction(int index)
        {
            Repaint();
        }

        void OnRemoveAction(int startIndex, int count)
        {
            Repaint();
        }

        void OnSetActionPointer(int newIndex)
        {
            Repaint();
        }

        void OnInspectorUpdate()
        {
            Repaint();
        }

        void Undo()
        {
            if (ActionManager.instance != null)
            {
                ActionManager.instance.Undo();
                Repaint();
            }
        }

        void Redo()
        {
            if (ActionManager.instance != null)
            {
                ActionManager.instance.Redo();
                Repaint();
            }
        }

        void Clear()
        {
            if (ActionManager.instance != null)
            {
                ActionManager.instance.Clear();
                Repaint();
            }
        }

        void OnGUI()
        {
            if (ActionManager.instance != null)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Undo"))
                {
                    Undo();
                }

                if (GUILayout.Button("Redo"))
                {
                    Redo();
                }

                if (GUILayout.Button("Clear"))
                {
                    Clear();
                }
                EditorGUILayout.EndHorizontal();

                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);

                var actionManager = ActionManager.instance;
                int n = actionManager.count;
                var actionPointer = actionManager.currentAction;
                for (int i = 0; i < n; ++i)
                {
                    var aciton = actionManager.GetAction(i);

                    EditorGUILayout.BeginHorizontal();
                    if (actionPointer == i)
                    {
                        EditorGUILayout.Toggle(true);
                    }
                    else
                    {
                        if (i < actionPointer)
                        {
                            if (GUILayout.Button("Undo", GUILayout.MaxWidth(40)))
                            {
                                int delta = actionPointer - i;
                                for (int k = 0; k < delta; ++k)
                                {
                                    actionManager.Undo();
                                }
                            }
                        }
                        else
                        {
                            if (GUILayout.Button("Redo", GUILayout.MaxWidth(40)))
                            {
                                int delta = i - actionPointer;
                                for (int k = 0; k < delta; ++k)
                                {
                                    actionManager.Redo();
                                }
                            }
                        }
                    }
                    
                    EditorGUILayout.TextField(aciton.description);
                    
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndScrollView();
            }
        }

        Vector2 scrollPos;
    }
}

#endif