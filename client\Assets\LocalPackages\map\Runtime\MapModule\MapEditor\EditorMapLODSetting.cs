﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class EditorMapLODSetting
    {
        public EditorMapLODSetting()
        {
            var editorMap = Map.currentMap as EditorMap;
            editorMap.exportMapEvent -= CalculateLoadRanges;
            editorMap.exportMapEvent += CalculateLoadRanges;
            editorMap.saveMapEvent -= CalculateLoadRanges;
            editorMap.saveMapEvent += CalculateLoadRanges;
        }

        public void LoadConfig(string path)
        {
            if (!File.Exists(path))
            {
                path = EditorUtility.OpenFilePanel("Select intuitive_zoom.tsv", MapModule.configResDirectory, "tsv");
            }

            bool suc = TSVReader.Load(path);
            if (suc)
            {
                var rows = TSVReader.rows;
                var lodManager = Map.currentMap.data.lodManager;
                if (rows.Count != lodManager.lodCount)
                {
                    lodManager.SetLODCount(rows.Count);
                }
                for (int i = 0; i < rows.Count; ++i)
                {
                    var lod = lodManager.GetLOD(i);
                    lod.cameraHeight = (float)(TSVReader.GetFloat(i, "height_ge", out suc) / 1000.0f);
                    lod.viewHeight = TSVReader.GetInt(i, "height", out suc) / 1000.0f;
                    lod.viewWidth = TSVReader.GetInt(i, "width", out suc) / 1000.0f;
                    lod.showTerritory = TSVReader.GetBool(i, "territory", out suc);
                    string name = TSVReader.GetString(i, "name", out suc);
                    if (!string.IsNullOrEmpty(name))
                    {
                        lod.name = name;
                    }
                    var units = TSVReader.GetArray(i, MapModule.intuitiveConfigUnitName, out suc);
                    if (units != null)
                    {
                        lod.displayingUnits = new List<MapLODManager.MapLODUnit>(units.Count);
                        for (int k = 0; k < units.Count; ++k)
                        {
                            string unit = "";
                            string relation = "";
                            var unitConfig = units[k] as Dictionary<string, object>;
                            object unitObj;
                            bool found = unitConfig.TryGetValue("unit", out unitObj);
                            if (found)
                            {
                                unit = unitObj as string;
                            }
                            object relationObj;
                            found = unitConfig.TryGetValue("relation", out relationObj);
                            if (found)
                            {
                                relation = relationObj as string;
                            }
                            lod.displayingUnits.Add(new MapLODManager.MapLODUnit(unit, relation));
                        }
                    }
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Can't find map lod config file!", "OK");
            }
        }

        //导出lod配置文件
        public void ExportLODConfig(string filePath)
        {
            SortMapLOD();

            StringBuilder builder = new StringBuilder();

            string[] headers = new string[] {
                "A_INT_id",
                "A_ARR_unit",
                "C_FLT_zoom",
                "A_FLT_height_ge",
                "A_INT_width",
                "A_INT_height",
                "S_BOL_territory",
                "C_STR_name",
            };

            for (int i = 0; i < headers.Length; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Length - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            var lodManager = Map.currentMap.data.lodManager;
            int n = lodManager.lodCount;
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                //id
                builder.AppendFormat("{0}\t", 13221001 + i);

                //unit
                string unitString = lod.GetUnitString();
                builder.AppendFormat("{0}\t", unitString);

                //zoom
                builder.AppendFormat("{0}\t", i);

                //height
                builder.AppendFormat("{0}\t", Utils.F2I(lod.cameraHeight));

                //view width
                builder.AppendFormat("{0}\t", Utils.F2I(lod.viewWidth));

                //view height
                builder.AppendFormat("{0}\t", Utils.F2I(lod.viewHeight));

                //show territory
                builder.AppendFormat("{0}\t", lod.showTerritory ? "True" : "False");

                //name
                builder.AppendFormat("{0}\n", lod.name);
            }

            var str = builder.ToString();
            File.WriteAllText(filePath, str);
        }

        public void Draw(MapLODManager lodManager)
        {
            mShowZoomConverter = EditorGUILayout.Foldout(mShowZoomConverter, new GUIContent("Camera Zoom And Height Relationship", "相机高度和zoom值之间的关系计算,输入相机高度算出lod的zoom值"));
            if (mShowZoomConverter)
            {
                mCameraHeight = EditorGUILayout.FloatField("Camera Height", mCameraHeight);
                float zoom = Map.currentMap.CalculateCameraZoom(mCameraHeight);
                EditorGUILayout.FloatField("Camera Zoom", zoom);
            }

            mShowFold = EditorGUILayout.Foldout(mShowFold, new GUIContent("Map LOD Setting", "地图lod高度设置,这里是lod的总数量,每个layer可以独立决定使用哪些lod高度,例如总共5个lod高度, front layer可以选择使用第1和第3个高度作为2个lod, Ground Layer可以选择第2和第4个高度作为2个lod等"));
            if (mShowFold)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                if (mLODNames == null)
                {
                    CreateLODNames(lodManager.lodCount);
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Sort", "按高度来排序lod设置")))
                {
                    SortMapLOD();
                }
                if (GUILayout.Button(new GUIContent("Calculate Load Range", "根据lod高度和Load Range Calculator里的设置来计算每个lod的Viewport Width和Viewport Height")))
                {
                    CalculateLoadRanges();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Load LOD Config", "读取intuitive_zoom.tsv配置表")))
                {
                    LoadConfig("");
                }
                if (GUILayout.Button(new GUIContent("Export LOD Config", "生成一张和intuitive_zoom.tsv相同格式的配置表")))
                {
                    string filePath = EditorUtility.SaveFilePanel("Select intuitive_zoom.tsv", MapModule.configResDirectory, "intuitive_zoom", "tsv");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        ExportLODConfig(filePath);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.IntField(new GUIContent("LOD Count", "LOD总数"), lodManager.lodCount);
                if (GUILayout.Button(new GUIContent("Change", "修改lod数量")))
                {
                    var dlg = EditorUtils.CreateInputDialog("New LOD Count");
                    var items = new List<InputDialog.Item> {
                                new InputDialog.StringItem("New LOD Count", "", lodManager.lodCount.ToString()),
                            };
                    dlg.Show(items,
                        (List<InputDialog.Item> parameters) =>
                        {
                            int lodCount;
                            Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out lodCount);
                            if (lodCount <= 0 || lodCount == lodManager.lodCount)
                            {
                                return false;
                            }

                            SetLODCount(lodManager, lodCount);
                            CreateLODNames(lodCount);
                            return true;
                        }
                        );

                }
                EditorGUILayout.EndHorizontal();


                if (mLODNames.Length != lodManager.lodCount)
                {
                    CreateLODNames(lodManager.lodCount);
                }

                DrawLODs(lodManager);
                EditorGUILayout.EndVertical();
            }
        }

        void SetLODCount(MapLODManager lodManager, int lodCount)
        {
            lodManager.SetLODCount(lodCount);
        }

        void DrawLODs(MapLODManager lodManager)
        {
            int n = lodManager.lodCount;
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                mFoldState[i] = EditorGUILayout.Foldout(mFoldState[i], mLODNames[i]);
                if (mFoldState[i])
                {
                    if (i > 0)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.TextField(new GUIContent("Name", "lod的名称"), lod.name);
                        if (GUILayout.Button("Change"))
                        {
                            var dlg = EditorUtils.CreateInputDialog("Change LOD Name");
                            var items = new List<InputDialog.Item> {
                                new InputDialog.StringItem("New Name", "", lod.name),
                            };
                            dlg.Show(items,
                                (List<InputDialog.Item> parameters) =>
                                {
                                    string name = (parameters[0] as InputDialog.StringItem).text;
                                    if (string.IsNullOrEmpty(name))
                                    {
                                        return false;
                                    }

                                    var lodConfig = Map.currentMap.data.lodManager.GetLOD(name);
                                    if (lodConfig != null)
                                    {
                                        return false;
                                    }

                                    string oldName = lod.name;
                                    if (name == oldName)
                                    {
                                        return false;
                                    }
                                    //change map lod name
                                    lod.name = name;

                                    //change map layer lod name
                                    var map = Map.currentMap;
                                    int layerCount = map.GetMapLayerCount();
                                    for (int k = 0; k < layerCount; ++k)
                                    {
                                        var layerData = map.GetMapLayerByIndex(k).GetLayerData();
                                        if (layerData != null)
                                        {
                                            var layerLODConfig = layerData.lodConfig;
                                            if (layerLODConfig != null)
                                            {
                                                layerLODConfig.ChangeLODName(oldName, name);
                                            }
                                        }
                                    }

                                    var editorMapData = map.data as EditorMapData;
                                    editorMapData.splineObjectManager.lodConfig.ChangeLODName(oldName, name);
                                    return true;
                                }
                                );
                        }
                        EditorGUILayout.EndHorizontal();
                        lod.cameraHeight = EditorGUILayout.FloatField(new GUIContent("Camera Height(m)", "该lod对应的相机高度"), lod.cameraHeight);
                    }
                    lod.viewWidth = EditorGUILayout.FloatField(new GUIContent("Viewport Width(m)", "客户端向服务器请求游戏数据区域的宽度"), lod.viewWidth);
                    lod.viewHeight = EditorGUILayout.FloatField(new GUIContent("Viewport Height(m)", "客户端向服务器请求游戏数据区域的高度"), lod.viewHeight);
                    lod.showTerritory = EditorGUILayout.ToggleLeft(new GUIContent("Show Territory", "是否显示Territory, 这个Territory不是由map package生成"), lod.showTerritory);
#if true
                    mShowDisplayUnitType = EditorGUILayout.Foldout(mShowDisplayUnitType, new GUIContent("Show Unit Type"));
                    if (mShowDisplayUnitType)
                    {
                        EditorGUI.indentLevel++;
                        int count = EditorGUILayout.IntField(new GUIContent("Display Unit Type", "这个lod应该显示的NPC类型"), lod.displayingUnits.Count);
                        if (count != lod.displayingUnits.Count)
                        {
                            lod.SetDisplayingUnitCount(count);
                        }
                        EditorGUI.indentLevel++;
                        for (int k = 0; k < lod.displayingUnits.Count; ++k)
                        {
                            EditorGUILayout.BeginHorizontal();
                            EditorGUIUtility.labelWidth = 80;
                            lod.displayingUnits[k].unit = EditorGUILayout.TextField(new GUIContent("unit", "NPC类型"), lod.displayingUnits[k].unit);
                            lod.displayingUnits[k].relation = EditorGUILayout.TextField(new GUIContent("relation", "阵营"), lod.displayingUnits[k].relation);
                            EditorGUIUtility.labelWidth = 0;
                            EditorGUILayout.EndHorizontal();
                        }
                        EditorGUI.indentLevel--;
                        EditorGUI.indentLevel--;
                    }
#endif
                }
            }
        }

        void SortMapLOD()
        {
            Map.currentMap.data.lodManager.Sort();
        }

        void CalculateLoadRanges()
        {
            var data = (Map.currentMap.data as EditorMapData).loadRangeData;
            var lodManager = Map.currentMap.data.lodManager;
            int lodCount = lodManager.lodCount;
            for (int i = 0; i < lodCount; ++i)
            {
                var lodConfig = lodManager.GetLOD(i);
                var loadSize = MapEditor.instance.GetLoadSize(data.cameraFov, data.resolution.x / data.resolution.y, data.cameraRotationX, lodConfig.cameraHeight, data.loadRangeScale);
                lodConfig.viewWidth = Mathf.Clamp(loadSize.x, 0, Map.currentMap.mapWidth);
                lodConfig.viewHeight = Mathf.Clamp(loadSize.y, 0, Map.currentMap.mapHeight);
            }
        }

        void CreateLODNames(int n)
        {
            mLODNames = new string[n];
            for (int i = 0; i < n; ++i)
            {
                mLODNames[i] = string.Format("LOD {0}", i);
            }
            mFoldState = new bool[n];
            for (int i = 0; i < n; ++i)
            {
                mFoldState[i] = true;
            }
        }

        public bool enableLOD { get { return mEnableLOD; } }

        string[] mLODNames;
        bool[] mFoldState;
        bool mShowFold = true;
        bool mEnableLOD = false;
        bool mShowZoomConverter = false;
        bool mShowDisplayUnitType = false;
        float mCameraHeight = 0;
    }
}

#endif