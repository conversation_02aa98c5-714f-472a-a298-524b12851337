﻿








using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace TFW
{
    public class FrameUpdateMgr
    {
        #region 字段

        //static long m_FixedSeq = 0;
        //static long m_RenderSeq = 0;
        //static long m_LateSeq = 0;

        static Dictionary<string, Action<float>> m_FixedFunList = new Dictionary<string, Action<float>>();
        static Dictionary<object, Action<float>> m_PerSecondFunList = new Dictionary<object, Action<float>>();
        static Dictionary<object, Action<float>> m_RenderFunList = new Dictionary<object, Action<float>>();
        static Dictionary<object, Action<float>> m_LateFunList = new Dictionary<object, Action<float>>();

        static Dictionary<string, Action<float>> m_toAddFixedFunList = new Dictionary<string, Action<float>>();
        static Dictionary<object, Action<float>> m_toAddPerSecondFunList = new Dictionary<object, Action<float>>();
        static Dictionary<object, Action<float>> m_toAddRenderFunList = new Dictionary<object, Action<float>>();
        static Dictionary<object, Action<float>> m_toAddLateFunList = new Dictionary<object, Action<float>>();

        static List<string> m_toRemoveFixedFunList = new List<string>();
        static List<object> m_toRemovePerSecondFunList = new List<object>();
        static List<object> m_toRemoveRenderFunList = new List<object>();
        static List<object> m_toRemoveLateFunList = new List<object>();

        #endregion

        #region 私有方法

        #endregion

        #region 公开方法

        public static void Init()
        {
            //m_FixedSeq = 0;
            //m_RenderSeq = 0;
            //m_LateSeq = 0;

            m_FixedFunList.Clear();
            m_PerSecondFunList.Clear();
            m_RenderFunList.Clear();
            m_LateFunList.Clear();

            m_toAddFixedFunList.Clear();
            m_toAddPerSecondFunList.Clear();
            m_toAddRenderFunList.Clear();
            m_toAddLateFunList.Clear();

            m_toRemoveFixedFunList.Clear();
            m_toRemovePerSecondFunList.Clear();
            m_toRemoveRenderFunList.Clear();
            m_toRemoveLateFunList.Clear();
        }

        //public static long gGetFixedFrameCount()
        //{
        //    return m_FixedSeq;
        //}

        //public static long gGetFrameCount()
        //{
        //    return m_RenderSeq;
        //}

        //public static long gGetLateFrameCount()
        //{
        //    return m_LateSeq;
        //}


        /// <summary>
        /// 直接完成所有的等待命令,不能在帧回调内部调用
        /// </summary>
        public static void CompletePendingCommands()
        {
            FinishTodoList();
        }

        public static void RegisterFixedUpdate(string name, Action<float> handler)
        {
           // D.Warning?.Assert(!string.IsNullOrEmpty(name), "[FrameUpdateMgr] Nil name");
            D.Warning?.Log($"[FrameUpdateMgr] RegisterFixedUpdate name={name}");
            if (m_toAddFixedFunList.TryGetValue(name, out var ret))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 FixedUpdate 回调, name = {name}");
            }
            else
            {
                m_toAddFixedFunList.Add(name, handler);
            }
        }

        public static void UnregisterFixedUpdate(string name)
        {
           // D.Warning?.Assert(!string.IsNullOrEmpty(name), "[FrameUpdateMgr] Nil name");
            m_toRemoveFixedFunList.Add(name);
        }

        public static void RegisterPerSecondFunListUpdate(object listener, Action<float> handler)
        {
            if (m_toAddPerSecondFunList.ContainsKey(listener))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 PerSecondFunListUpdate 回调, name = {listener}");
                m_toAddPerSecondFunList[listener] = handler;
            }
            else
            {
                m_toAddPerSecondFunList.Add(listener, handler);
            }
        }

        public static void RegisterPerSecondFunListUpdate(object listener, Func<float, UniTaskVoid> callback)
        {
            if (m_toAddPerSecondFunList.ContainsKey(listener))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 PerSecondFunListUpdate 回调, name = {listener}");
                m_toAddPerSecondFunList[listener] = (args) => { callback?.Invoke(args); };
            }
            else
            {
                m_toAddPerSecondFunList.Add(listener, (args) => { callback?.Invoke(args); });  
            }
        }

        public static void UnregisterPerSecondFunListUpdate(object listener)
        {
            m_toRemovePerSecondFunList.Add(listener);
        }

        public static void RegisterRenderingUpdate(object listener, Action<float> handler)
        {
           // D.Warning?.Assert(listener != null, "[FrameUpdateMgr] Nil name");
            if (m_toAddRenderFunList.TryGetValue(listener, out var ret))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 PerSecondFunListUpdate 回调, name = {listener}");
            }
            else
            {
                m_toAddRenderFunList.Add(listener, handler);
            }
        }


        public static void UnregisterRenderingUpdate(object listener)
        {
           // D.Warning?.Assert(listener != null, "[FrameUpdateMgr] Nil name");
            m_toRemoveRenderFunList.Add(listener);
        }


        public static void RegisterLateUpdate(object listener, Action<float> handler)
        {
           // D.Warning?.Assert(listener != null, "[FrameUpdateMgr] Nil name");
            if (m_toAddLateFunList.TryGetValue(listener, out var ret))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 LateUpdate 回调, name = {listener}");
            }
            else
            {
                m_toAddLateFunList.Add(listener, handler);
            }
        }

        public static void RegisterLateUpdate(object listener, Func<float, UniTaskVoid> handler)
        {
            // D.Warning?.Assert(listener != null, "[FrameUpdateMgr] Nil name");
            if (m_toAddLateFunList.TryGetValue(listener, out var ret))
            {
                D.Warning?.Log($"[FrameUpdateMgr] 已有同名 LateUpdate 回调, name = {listener}");
            }
            else
            {
                m_toAddLateFunList.Add(listener, (args) => { handler?.Invoke(args); });
            }
        }
     


        public static void UnregisterLateUpdate(object listener)
        {
           // D.Warning?.Assert(listener != null, "[FrameUpdateMgr] Nil name");
            m_toRemoveLateFunList.Add(listener);
        }

        #endregion

        #region 驱动

        private static bool m_LockFixedUpdate = false;

        public static void FixedUpdate(float deltaTime)
        {

            //m_FixedSeq += 1;
            m_LockFixedUpdate = true;
            foreach (var kv in m_FixedFunList)
            {
                //P.B(kv.Value);
                try
                {
                    kv.Value.Invoke(deltaTime);
                }
                catch (Exception e)
                {
                    D.Error?.Log($"kv.Key={kv.Key},Exception={e.StackTrace}");
                    //UnregisterFixedUpdate(kv.Key);
                    break;
                }
                //P.E();
            }

            m_LockFixedUpdate = false;

            //有可能上述操作会移除一些update操作,需要立即移除,如果残留到下一帧可能会crash,比如UI已经销毁,还多调用一次更新
            FinishTodoList();
        }

        static float m_interval = 0.0f;
        private static bool m_LockRenderingUpdate = false;
        private static bool m_LockPerSecondUpdate = false;
        public static void RenderingUpdate(float deltaTime)
        {
            //m_RenderSeq += 1;
            //有可能上述操作会移除一些update操作,需要立即移除,如果残留到下一帧可能会crash,比如UI已经销毁,还多调用一次更新
            FinishTodoList();

            if (m_RenderFunList.Count > 0)
            {
                m_LockRenderingUpdate = true;
                //渲染帧更新
                foreach (var kv in m_RenderFunList)
                {
                    ////P.B(kv.Key.ToString());
                    //P.B(kv.Value);
                    try
                    {
                        kv.Value.Invoke(deltaTime);
                    }
                    catch (Exception e)
                    {
                        D.Error?.Log($"kv.Key={kv.Key},Exception={e.StackTrace}");
                        //如果某个注册事件，被注销了 就会导致无法操作,例如RMap 初始化得时候 FrameUpdateMgr.RegisterRenderingUpdate("RMap", Update);
                        //但如果不注销,可能某个模块出错 每帧Update 会报错 也会出现卡死或者崩溃,但这样应该可以通过bugly能明显看出异常得位置
                        //20231011 fix by zjw 移除注销, 由于注销了事件 导致大地图无法操作
                        //UnregisterRenderingUpdate(kv.Key);
                        break;
                    }
                    //P.E();
                }
                m_LockRenderingUpdate = false;

                //有可能上述操作会移除一些update操作,需要立即移除,如果残留到下一帧可能会crash,比如UI已经销毁,还多调用一次更新
                FinishTodoList();
            }

            if ((m_interval += deltaTime) > 1f)
            {
                m_interval = 0f;

                if (m_PerSecondFunList.Count > 0)
                {
                    m_LockPerSecondUpdate = true;
                    foreach (var kv in m_PerSecondFunList)
                    {
                        ////P.B(kv.Key.ToString());
                        //P.B(kv.Value);
                        try
                        {
                            kv.Value.Invoke(m_interval);
                        }
                        catch (Exception e)
                        {
                            D.Error?.Log($"kv.Key={kv.Key},Exception={e.StackTrace}");
                            //UnregisterPerSecondFunListUpdate(kv.Key);
                            break;
                        }
                        //P.E();
                    }
                    m_LockPerSecondUpdate = false;

                    //有可能上述操作会移除一些update操作,需要立即移除,如果残留到下一帧可能会crash,比如UI已经销毁,还多调用一次更新
                    FinishTodoList();
                }
            }
        }

        private static bool m_LockLateUpdate = false;
        public static void LateUpdate(float deltaTime)
        {
            //完成 各类to do list
            //m_LateSeq += 1;
            m_LockLateUpdate = true;
            foreach (var kv in m_LateFunList)
            {
                ////P.B(kv.Key.ToString());
                //P.B(kv.Value);
                try
                {
                    kv.Value.Invoke(deltaTime);
                }
                catch (Exception e)
                {
                    D.Error?.Log($"kv.Key={kv.Key},Exception={e.StackTrace}");
                    //UnregisterLateUpdate(kv.Key);
                    break;
                }
                //P.E();
            }
            m_LockLateUpdate = false;
            //有可能上述操作会移除一些update操作,需要立即移除,如果残留到下一帧可能会crash,比如UI已经销毁,还多调用一次更新
            FinishTodoList();
        }

        /// <summary>
        /// 移动todolist每一项到内部队列
        /// </summary>
        private static void FinishTodoList()
        {
            if (!m_LockFixedUpdate)
            {
                if (m_toAddFixedFunList.Count > 0)
                {
                    foreach (var todoItem in m_toAddFixedFunList)
                    {
                        if (m_FixedFunList.TryGetValue(todoItem.Key, out var _))
                        {
                            D.Warning?.Log($"[FrameUpdateMgr] 已有同名 FixedUpdate回调 {todoItem.Key}");
                        }
                        else
                        {
                            m_FixedFunList.Add(todoItem.Key, todoItem.Value);
                        }
                    }
                    m_toAddFixedFunList.Clear();
                }

                if (m_toRemoveFixedFunList.Count > 0)
                {
                    foreach (var todoItem in m_toRemoveFixedFunList)
                    {
                        if (m_FixedFunList.TryGetValue(todoItem, out var _))
                        {
                            m_FixedFunList.Remove(todoItem);
                        }
                        else
                        {
                            D.Debug?.Log($"[FrameUpdateMgr] 不存在 FixedUpdate回调 {todoItem}");
                        }
                    }
                    m_toRemoveFixedFunList.Clear();
                }
            }

            if (!m_LockLateUpdate)
            {
                if (m_toAddLateFunList.Count > 0)
                {
                    foreach (var todoItem in m_toAddLateFunList)
                    {
                        if (m_LateFunList.TryGetValue(todoItem.Key, out var _))
                        {
                            D.Warning?.Log($"[FrameUpdateMgr] 已有同名 LateUpdate {todoItem.Key}");
                        }
                        else
                        {
                            m_LateFunList.Add(todoItem.Key, todoItem.Value);
                        }
                    }
                    m_toAddLateFunList.Clear();
                }

                if (m_toRemoveLateFunList.Count > 0)
                {
                    foreach (var todoItem in m_toRemoveLateFunList)
                    {
                        if (m_LateFunList.TryGetValue(todoItem, out var _))
                        {
                            m_LateFunList.Remove(todoItem);
                        }
                        else
                        {
                            D.Debug?.Log($"[FrameUpdateMgr] 不存在 LateUpdate {todoItem}");
                        }
                    }
                    m_toRemoveLateFunList.Clear();
                }
            }

            if (!m_LockPerSecondUpdate)
            {
                if (m_toAddPerSecondFunList.Count > 0)
                {
                    foreach (var todoItem in m_toAddPerSecondFunList)
                    {
                        if (m_PerSecondFunList.ContainsKey(todoItem.Key))
                        {
                            D.Warning?.Log($"[FrameUpdateMgr] 已有同名 PerSecondUpdate {todoItem.Key}");
                            m_PerSecondFunList[todoItem.Key] = todoItem.Value;
                        }
                        else
                        {
                            m_PerSecondFunList.Add(todoItem.Key, todoItem.Value);
                        }
                    }
                    m_toAddPerSecondFunList.Clear();
                }

                if (m_toRemovePerSecondFunList.Count > 0)
                {
                    foreach (var todoItem in m_toRemovePerSecondFunList)
                    {
                        if (m_PerSecondFunList.TryGetValue(todoItem, out var _))
                        {
                            m_PerSecondFunList.Remove(todoItem);
                        }
                        else
                        {
                            //D.Debug?.Log($"[FrameUpdateMgr] 不存在 PerSecondUpdate {todoItem}");
                        }
                    }
                    m_toRemovePerSecondFunList.Clear();
                }
            }

            if (!m_LockRenderingUpdate)
            {
                if (m_toAddRenderFunList.Count > 0)
                {
                    foreach (var todoItem in m_toAddRenderFunList)
                    {
                        if (m_RenderFunList.TryGetValue(todoItem.Key, out var _))
                        {
                            D.Warning?.Log($"[FrameUpdateMgr] 已有同名 RenderUpdate {todoItem.Key}");
                        }
                        else
                        {
                            m_RenderFunList.Add(todoItem.Key, todoItem.Value);
                        }
                    }
                    m_toAddRenderFunList.Clear();
                }

                if (m_toRemoveRenderFunList.Count > 0)
                {
                    foreach (var todoItem in m_toRemoveRenderFunList)
                    {
                        if (m_RenderFunList.TryGetValue(todoItem, out var _))
                        {
                            m_RenderFunList.Remove(todoItem);
                        }
                        else
                        {
                            D.Debug?.Log($"[FrameUpdateMgr] 不存在 RenderUpdate {todoItem}");
                        }
                    }
                    m_toRemoveRenderFunList.Clear();
                }
            }
        }

        #endregion
    }
}