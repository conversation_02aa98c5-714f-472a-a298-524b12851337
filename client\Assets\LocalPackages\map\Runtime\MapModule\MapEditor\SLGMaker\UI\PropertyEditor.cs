﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class PropertyEditor : EditorWindow
    {
        public void Show(int id, int objectPropertySetID, System.Action<int> onSetProperty)
        {
            mObjectID = id;
            mOnSetProperty = onSetProperty;

            var map = Map.currentMap;
            var editorMapData = map.data as EditorMapData;
            var propertyManager = editorMapData.propertySets as PropertySetManager;

            if (propertyManager.count > 0)
            {
                if (mObjectID != 0)
                {
                    var obj = Map.currentMap.FindObject(mObjectID);
                    if (obj != null)
                    {
                        for (int i = 0; i < propertyManager.count; ++i)
                        {
                            if (propertyManager.GetPropertySet(i).id == objectPropertySetID)
                            {
                                mIndex = i;
                            }
                        }
                    }
                }
            }
            else
            {
                mIndex = -1;
            }

            mTypes = new string[] {
                "int",
                "float",
                "string",
                "bool",
                "Vector2",
                "Vector3",
                "Vector4",
                "Color",
                "int array",
            };

            Show();
        }

        private void OnGUI()
        {
            var map = Map.currentMap;
            var editorMapData = map.data as EditorMapData;
            var propertyManager = editorMapData.propertySets;

            EditorGUILayout.BeginHorizontal();

            EditorGUILayout.BeginVertical("GroupBox");

            EditorGUILayout.BeginHorizontal();
            if (mIndex >= 0 || propertyManager.count > 0)
            {
                var index = EditorGUILayout.Popup("Selected Property Set", mIndex, propertyManager.names);
                if (mIndex != index)
                {
                    OnPropertySelectionChange(index);
                }
            }

            var name = EditorGUILayout.TextField(mPropertyTemplateName);

            if (mIndex >= 0 && mIndex < propertyManager.names.Length)
            {
                if (name != propertyManager.names[mIndex])
                {
                    OnPropertyNameChange(name);
                }
            }
            else
            {
                mPropertyTemplateName = name;
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Property Set"))
            {
                AddPropertyTemplate();
            }
            if (GUILayout.Button("Remove Property Set"))
            {
                RemovePropertyTemplate();
            }
            if (GUILayout.Button("Rename Property Set"))
            {
                RenamePropertyTemplate();
            }
            if (GUILayout.Button("Clone Property Set"))
            {
                ClonePropertyTemplate();
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Set Property"))
            {
                var selectedPS = GetSelectedPropertySet();
                if (selectedPS != null)
                {
                    if (mOnSetProperty != null)
                    {
                        mOnSetProperty(selectedPS.id);
                    }
                    Close();
                }
            }

            EditorGUILayout.EndVertical();

            var propertySet = GetSelectedPropertySet();
            if (propertySet != null)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginVertical("GroupBox");
                DrawPropertyListUI(propertySet);
                EditorGUILayout.EndVertical();

                EditorGUILayout.BeginHorizontal();
                mPropertyName = EditorGUILayout.TextField("Property Name", mPropertyName);
                mSelectedType = EditorGUILayout.Popup("Property Type", mSelectedType, mTypes);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Property"))
                {
                    AddProperty();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndHorizontal();
        }

        void SetSelectedIndex(int index)
        {
            if (mIndex != index)
            {
                OnPropertySelectionChange(index);
            }
        }

        void AddPropertyTemplate()
        {
            if (mPropertyTemplateName.Length > 0)
            {
                var map = Map.currentMap;

                bool isValidName = CheckValidName();

                if (isValidName)
                {
                    var editorMapData = map.data as EditorMapData;
                    var propertyManager = editorMapData.propertySets;
                    PropertySet ps = new PropertySet(map.nextCustomObjectID, map, mPropertyTemplateName, null);
                    propertyManager.AddPropertySet(ps);

                    SetSelectedIndex(mIndex + 1);

                    Repaint();

                    return;
                }
            }

            EditorUtility.DisplayDialog("Error", "Input valid property template name", "OK");
        }

        void RemovePropertyTemplate()
        {
            if (mIndex >= 0)
            {
                if (EditorUtility.DisplayDialog("Remove Property Set", "Are you sure ?", "Yes", "No"))
                {
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var propertyManager = editorMapData.propertySets;
                    propertyManager.RemovePropertySet(mIndex);
                    if (propertyManager.count == 0)
                    {
                        SetSelectedIndex(-1);
                    }
                    else
                    {
                        int index = mIndex - 1;
                        if (index < 0)
                        {
                            index = 0;
                        }
                        SetSelectedIndex(index);
                    }
                }
            }
        }

        void ClonePropertyTemplate()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var manager = editorMapData.propertySets;
            int n = manager.count;
            if (mIndex >= 0 && mIndex < n)
            {
                var ps = manager.GetPropertySet(mIndex);
                var newPS = manager.ClonePropertySet(ps);
                if (newPS != null)
                {
                    SetSelectedIndex(manager.count - 1);
                }
            }
        }

        void RenamePropertyTemplate()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var manager = editorMapData.propertySets;
            int n = manager.count;
            if (mIndex >= 0 && mIndex < n)
            {
                var ps = manager.GetPropertySet(mIndex);
                if (ps != null)
                {
                    bool isValidName = CheckValidName();
                    if (isValidName)
                    {
                        manager.RenameProperty(mIndex, mPropertyTemplateName);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Input valid property template name", "OK");
                    }
                }
                Repaint();
            }
        }

        void AddProperty()
        {
            if (mPropertyName.Length > 0)
            {
                var ps = GetSelectedPropertySet();
                if (ps != null)
                {
                    var properties = ps.properties as PropertyDatas;
                    if (mPropertyName.Length > 0 && properties.FindProperty(mPropertyName) == null)
                    {
                        PropertyBase prop = null;
                        switch (mTypes[mSelectedType])
                        {
                            case "int":
                                prop = new PropertyData<int>(mPropertyName, PropertyType.kPropertyInt, 0);
                                break;
                            case "int array":
                                prop = new PropertyData<int[]>(mPropertyName, PropertyType.kPropertyIntArray, new int[0]);
                                break;
                            case "float":
                                prop = new PropertyData<float>(mPropertyName, PropertyType.kPropertyFloat, 0);
                                break;
                            case "string":
                                prop = new PropertyData<string>(mPropertyName, PropertyType.kPropertyString, "");
                                break;
                            case "bool":
                                prop = new PropertyData<bool>(mPropertyName, PropertyType.kPropertyBool, false);
                                break;
                            case "Vector2":
                                prop = new PropertyData<Vector2>(mPropertyName, PropertyType.kPropertyVector2, Vector2.zero);
                                break;
                            case "Vector3":
                                prop = new PropertyData<Vector3>(mPropertyName, PropertyType.kPropertyVector3, Vector3.zero);
                                break;
                            case "Vector4":
                                prop = new PropertyData<Vector4>(mPropertyName, PropertyType.kPropertyVector4, Vector4.zero);
                                break;
                            case "Color":
                                prop = new PropertyData<Color>(mPropertyName, PropertyType.kPropertyColor, Color.white);
                                break;
                            default:
                                Debug.Assert(false, "unknown property type!");
                                break;
                        }
                        if (prop != null)
                        {
                            ps.properties.AddProperty(prop);
                            return;
                        }
                    }
                }
            }

            EditorUtility.DisplayDialog("Error", "Input valid property name", "OK");
        }

        bool CheckValidName()
        {
            if (mPropertyTemplateName.Length == 0)
            {
                return false;
            }

            var editorMapData = Map.currentMap.data as EditorMapData;
            var ps = editorMapData.propertySets.FindProperty(mPropertyTemplateName);
            if (ps == null)
            {
                return true;
            }
            return false;
        }

        void OnPropertySelectionChange(int index)
        {
            mIndex = index;
            var ps = GetSelectedPropertySet();
            if (ps != null)
            {
                mPropertyTemplateName = ps.name;
            }
        }

        void OnPropertyNameChange(string name)
        {
            mPropertyTemplateName = name;
        }

        PropertySet GetSelectedPropertySet()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var manager = editorMapData.propertySets;
            int n = manager.count;
            if (mIndex >= 0 && mIndex < n)
            {
                var ps = manager.GetPropertySet(mIndex);
                return ps;
            }
            return null;
        }

        void DrawPropertyListUI(PropertySet ps)
        {
            EditorGUILayout.LabelField("Properties In Property Set: ");

            var propertyData = ps.properties as PropertyDatas;
            if (propertyData != null)
            {
                var properties = propertyData.properties;
                for (int i = 0; i < properties.Count; ++i)
                {
                    if (properties[i].GetType() == typeof(PropertyData<int>))
                    {
                        DrawIntProperty(properties[i].name, properties[i] as PropertyData<int>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<int[]>))
                    {
                        DrawIntArrayProperty(properties[i].name, properties[i] as PropertyData<int[]>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<float>))
                    {
                        DrawFloatProperty(properties[i].name, properties[i] as PropertyData<float>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<string>))
                    {
                        DrawStringProperty(properties[i].name, properties[i] as PropertyData<string>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<Vector2>))
                    {
                        DrawVector2Property(properties[i].name, properties[i] as PropertyData<Vector2>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<Vector3>))
                    {
                        DrawVector3Property(properties[i].name, properties[i] as PropertyData<Vector3>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<Vector4>))
                    {
                        DrawVector4Property(properties[i].name, properties[i] as PropertyData<Vector4>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<Color>))
                    {
                        DrawColorProperty(properties[i].name, properties[i] as PropertyData<Color>, propertyData);
                    }
                    else if (properties[i].GetType() == typeof(PropertyData<bool>))
                    {
                        DrawBoolProperty(properties[i].name, properties[i] as PropertyData<bool>, propertyData);
                    }
                }
            }
        }

        void AddPropertyButtons(PropertyBase property, PropertyDatas properties)
        {
            if (GUILayout.Button("Rename"))
            {
                if (properties.FindProperty(mPropertyName) == null)
                {
                    property.name = mPropertyName;
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid property name", "OK");
                }
            }
            if (GUILayout.Button("Remove"))
            {
                if (EditorUtility.DisplayDialog("Remove Property", "Are you sure ?", "Yes", "No"))
                {
                    var ps = GetSelectedPropertySet();
                    ps.properties.RemoveProperty(property);
                }
            }
        }

        void DrawIntProperty(string name, PropertyData<int> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.IntField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawIntArrayProperty(string name, PropertyData<int[]> property, PropertyDatas properties)
        {
            var value = property.value;
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(name);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
            int newSize = EditorGUILayout.IntField("Array Size", value.Length);
            newSize = Mathf.Max(0, newSize);
            if (newSize != value.Length)
            {
                int minSize = Mathf.Min(newSize, value.Length);
                var newValues = new int[newSize];
                for (int i = 0; i < minSize; ++i)
                {
                    newValues[i] = value[i];
                }
                property.value = newValues;
            }
            for (int i = 0; i < newSize; ++i)
            {
                EditorGUI.indentLevel++;
                property.value[i] = EditorGUILayout.IntField($"Value {i}", property.value[i]);
                EditorGUI.indentLevel--;
            }
        }

        void DrawFloatProperty(string name, PropertyData<float> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.FloatField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawStringProperty(string name, PropertyData<string> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.TextField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector2Property(string name, PropertyData<Vector2> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector2Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector3Property(string name, PropertyData<Vector3> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector3Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawVector4Property(string name, PropertyData<Vector4> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Vector4Field(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawColorProperty(string name, PropertyData<Color> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.ColorField(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        void DrawBoolProperty(string name, PropertyData<bool> property, PropertyDatas properties)
        {
            EditorGUILayout.BeginHorizontal();
            property.value = EditorGUILayout.Toggle(name, property.value);
            AddPropertyButtons(property, properties);
            EditorGUILayout.EndHorizontal();
        }

        int mObjectID = 0;
        int mIndex = -1;
        int mSelectedType = 0;
        string mPropertyTemplateName = "";
        string mPropertyName = "";
        string mPropertyNameString;
        string[] mTypes;
        System.Action<int> mOnSetProperty;
    }
}

#endif