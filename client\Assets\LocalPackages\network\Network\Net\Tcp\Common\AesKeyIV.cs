﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using System;
using System.Net.Sockets;
using UnityEngine;

namespace Net.Tcp.Common
{
    public class AesKeyIV
    {
        /// <summary>
        /// 老版本加密长度
        /// </summary>
        public const int KeyIVLen_V0 = 16;
        /// <summary>
        /// 新版本加密长度
        /// </summary>
        public const int KeyIVLen_V1 = 17;

        /// <summary>
        /// 通讯协议版本 v0
        /// </summary>
        const byte PROTOCOL_V0 = 0;
        /// <summary>
        /// 通讯协议版本 v1
        /// </summary>
        const byte PROTOCOL_V1 = 1;

        /// <summary>
        /// 使用新的加密key
        /// </summary>
        public const bool USE_KEY_V1 = true;


        internal static byte[] GenKeyIV()
        {
            var bytes = new byte[USE_KEY_V1 ? KeyIVLen_V1 : KeyIVLen_V0];
            var rand = new System.Random(DateTime.Now.Millisecond);
            for (var i = 0; i < bytes.Length; ++i)
            {
                bytes[i] = (byte) rand.Next(0, 10);
            }

            //设置版本号
            if (USE_KEY_V1)
                bytes[0] = PROTOCOL_V1;

            return bytes;
        }

        internal static bool Check(byte[] bytes)
        {
            return bytes != null && (bytes.Length == KeyIVLen_V0 || bytes.Length == KeyIVLen_V1);
        }
        internal static byte[] PraseKey(byte[] bytes)
        {
            var key = new byte[USE_KEY_V1 ? KeyIVLen_V1 : KeyIVLen_V0];
            Buffer.BlockCopy(bytes, 0, key, 0, key.Length);
            return key;
        }
        internal static byte[] PraseIV(byte[] bytes)
        {
            var iv = new byte[USE_KEY_V1 ? KeyIVLen_V1 : KeyIVLen_V0];
            if(USE_KEY_V1)
            {
                Buffer.BlockCopy(bytes, KeyIVLen_V1, iv, 0, iv.Length);
            }
            else
            {
                Buffer.BlockCopy(bytes, KeyIVLen_V0, iv, 0, iv.Length);
            }
            return iv;
        }

        internal static void SendAesKeyIVRsa(Socket sock, string rsaPub, byte[] kiv)
        {
            if (sock == null)
            {
                //Debug.LogError("Null socket");
                return;
            }

            if (!sock.Connected)
            {
                //Debug.LogError("Socket is not connected");
                return;
            }

            if (!Rsa.Encrypt(rsaPub, kiv, out kiv))
            {
                sock.Close();
                throw new Exception("Rsa Encrypt error");
            }

            var len = NetHelper.ToBytes(kiv.Length);
            sock.Send(len);
            sock.Send(kiv);
        }

        internal static void SendAesKeyIVAes(Socket sock, byte[] aesKIV, byte[] kiv)
        {
            if (sock == null || !sock.Connected)
            {
                return;
            }

            var encrypt = new AesEncryptor(aesKIV, aesKIV, USE_KEY_V1);
            kiv = encrypt.Encrypt(kiv, 0, kiv.Length);
            byte[] len = NetHelper.ToBytes(kiv.Length);
            sock.Send(len);
            sock.Send(kiv);
        }
    }
}
#endif