﻿ 



 
 


using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadRegionColorLayer(BinaryReader reader)
        {
            var pos = GetSectionDataStartPosition(MapDataSectionType.RegionColorLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);
            bool showRegionInGame = true;
            if (version >= 2)
            {
                showRegionInGame = reader.ReadBoolean();
            }

            int layerCount = reader.ReadInt32();
            config.RuntimeRegionColorSubLayerData[] layers = new config.RuntimeRegionColorSubLayerData[layerCount];

            for (int s = 0; s < layerCount; ++s)
            {
                var subLayer = new config.RuntimeRegionColorSubLayerData();
                subLayer.name = Utils.ReadString(reader);
                subLayer.horizontalTileCount = reader.ReadInt32();
                subLayer.verticalTileCount = reader.ReadInt32();
                subLayer.tileWidth = reader.ReadSingle();
                subLayer.tileHeight = reader.ReadSingle();

                int count = reader.ReadInt32();
                subLayer.regionIDs = new short[count];
                for (int i = 0; i < count; ++i)
                {
                    subLayer.regionIDs[i] = reader.ReadInt16();
                }

                int n = reader.ReadInt32();
                var grids = new config.RuntimeRegionColorData[n];
                for (int i = 0; i < n; ++i)
                {
                    var color = Utils.ReadColor32(reader);
                    int id = reader.ReadInt32();
                    grids[i] = new config.RuntimeRegionColorData(id, color);
                }
                subLayer.regions = grids;
                layers[s] = subLayer;
            }
            //-----------------------version 1 end----------------------------------
            var layer = new config.RuntimeRegionColorLayerData(layerID, name, offset, layers, showRegionInGame);
            return layer;
        }
    }
}
