﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    public class NavMeshLayerView : MapLayerView
    {
        NavMeshLayerView(MapLayerData layerData) : base(layerData, false)
        {
            var navMeshLayer = layerData as NavMeshLayerData;
            var navMeshDatas = navMeshLayer.navMeshDatas;
            if (navMeshDatas != null)
            {
                mMeshViewer.Create(root, "nav mesh", navMeshDatas[0].vertices, navMeshDatas[0].indices, true, new Color32(51, 196, 79, 150));
            }
        }

        public static NavMeshLayerView Create(MapLayerData layerData)
        {
            var view = new NavMeshLayerView(layerData);
            return view;
        }

        public override void OnDestroy()
        {
            mMeshViewer.OnDestroy();
        }

        public override void SetZoom(float zoom, bool lodChanged)
        {
        }

        public void ShowNavMesh(bool visible)
        {
            mMeshViewer.active = visible;
        }

        public bool IsNavMeshVisible()
        {
            if (mMeshViewer.mesh == null)
            {
                return false;
            }
            return mMeshViewer.active;
        }

        public NavMeshBlock[] CreateNavMesh(int xTileCount, int yTileCount, PrefabOutlineType outlineType, float radius, float minimumAngle, float maximumArea, bool useDelaunay, NavigationCreateMode createMode, bool oceanAreaWalkable)
        {
            var editorMap = Map.currentMap as EditorMap;
            var meshBlocks = Utils.CreateNavMesh(LayerTypeMask.kGridModelLayer | LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kModelLayer | LayerTypeMask.kCollisionLayer, xTileCount, yTileCount, outlineType, useDelaunay, createMode, false, oceanAreaWalkable, true, editorMap.removeSameHoles, radius, minimumAngle, maximumArea);
            if (meshBlocks != null)
            {
                if (meshBlocks[0].indices == null)
                {
                    return null;
                }
                mMeshViewer.Create(root, "nav mesh", meshBlocks[0].vertices, meshBlocks[0].indices, true, new Color32(51, 196, 79, 150));
            }
            return meshBlocks;
        }

        public Mesh GetNavMesh(int i)
        {
            return mMeshViewer.mesh;
        }

        public override void ReloadVisibleViews()
        {
        }

        BigMeshViewer mMeshViewer = new BigMeshViewer();
    }
}

#endif