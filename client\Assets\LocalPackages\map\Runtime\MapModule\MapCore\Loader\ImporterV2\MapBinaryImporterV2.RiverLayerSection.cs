﻿ 



 
 


using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.RiverLayerData LoadRiverLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.PolygonRiverLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            float width = reader.ReadSingle();
            float height = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRiverDataV1(reader);
            }

            var config = LoadRiverLayerLODConfigV1(reader);
            //-------------------------version 1 end--------------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadRiverLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layer = new config.RiverLayerData(layerID, name, offset, config, null, width, height, objects);
            return layer;
        }

        config.RiverData LoadRiverDataV1(BinaryReader reader)
        {
            var riverData = new config.RiverData();
            riverData.SetID(reader.ReadInt32());
            riverData.position = Utils.ReadVector3(reader);
            riverData.modelTemplateID = reader.ReadInt32();
            riverData.hideLOD = reader.ReadInt32();
            return riverData;
        }

        config.MapLayerLODConfig LoadRiverLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadRiverLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
