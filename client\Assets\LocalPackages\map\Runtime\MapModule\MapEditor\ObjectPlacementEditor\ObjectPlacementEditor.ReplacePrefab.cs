﻿ 



 
 

#if UNITY_EDITOR

using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        enum ReplaceMode
        {
            SingleFile,
            FollowPattern,
        }

        public void Replace()
        {
            if (prefabCount > 0 || mReplaceMode == ReplaceMode.FollowPattern)
            {
                var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
                if (stage == null)
                {
                    return;
                }

                var rootPrefab = stage.prefabContentsRoot;
                int n = rootPrefab.transform.childCount;
                for (int i = n - 1; i >= 0; --i)
                {
                    EditorUtility.DisplayProgressBar("Replacing Prefab", "Replacing Prefab...", (n - 1 - i) / (float)n);
                    var childTransform = rootPrefab.transform.GetChild(i);
                    var childPrefabInstance = childTransform.gameObject;

                    //获取prefab instance使用的prefab
                    var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);

                    var childPrefabAssetPath = AssetDatabase.GetAssetPath(childPrefab);
                    if (!string.IsNullOrEmpty(childPrefabAssetPath))
                    {
                        var targetPrefabPath = GetTargetPrefabPath(childPrefabAssetPath);
                        if (!string.IsNullOrEmpty(targetPrefabPath) && targetPrefabPath != childPrefabAssetPath)
                        {
                            var targetPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(targetPrefabPath);
                            if (targetPrefab != null)
                            {
                                Vector3 position = childTransform.position;
                                Quaternion rotation = childTransform.rotation;
                                Vector3 scale = childTransform.localScale;
                                string tag = childTransform.tag;
                                //delete original
                                Undo.DestroyObjectImmediate(childTransform.gameObject);

                                //create new
                                var newChildPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(targetPrefab);
                                newChildPrefabInstance.transform.position = position;
                                newChildPrefabInstance.transform.rotation = rotation;
                                newChildPrefabInstance.transform.localScale = scale;
                                newChildPrefabInstance.tag = tag;
                                newChildPrefabInstance.transform.SetParent(rootPrefab.transform, true);
                                newChildPrefabInstance.transform.SetSiblingIndex(i);
                                EditorUtility.SetDirty(rootPrefab.transform.gameObject);

                                Undo.RegisterCreatedObjectUndo(newChildPrefabInstance, "Created Prefab");
                            }
                        }
                    }
                }
                EditorUtility.ClearProgressBar();
            }
        }

        string GetChildPrefabPath(GameObject prefab)
        {
            string path = AssetDatabase.GetAssetPath(prefab);
            if (string.IsNullOrEmpty(path))
            {
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(prefab);
                path = AssetDatabase.GetAssetPath(childPrefab);
            }
            return path;
        }

        string GetTargetPrefabPath(string sourcePrefabPath)
        {
            if (mReplaceMode == ReplaceMode.FollowPattern)
            {
                string prefabPath = GetTargetPrefabPathInPattern(sourcePrefabPath);
                if (!string.IsNullOrEmpty(prefabPath))
                {
                    return prefabPath;
                }
            }
            else
            {
                for (int i = 0; i < mSourcePrefabs.Length; ++i)
                {
                    if (mSourcePrefabs[i] != null)
                    {
                        string path = GetChildPrefabPath(mSourcePrefabs[i]);
                        if (path == sourcePrefabPath)
                        {
                            return GetChildPrefabPath(mTargetPrefabs[i]);
                        }
                    }
                }
            }
            return sourcePrefabPath;
        }

        string GetTargetPrefabPathInPattern(string prefabPath)
        {
            string ext = Utils.GetExtension(prefabPath);
            string pathWithoutExt = Utils.RemoveExtension(prefabPath);
            int idx = pathWithoutExt.IndexOf(mSourceRegexPattern);
            if (idx >= 0)
            {
                string prefix = pathWithoutExt.Substring(0, idx);
                string targetPath = prefix + mTargetRegexPattern + "." + ext;
                if (File.Exists(targetPath))
                {
                    return targetPath;
                }
            }

            return "";
        }

        void DrawReplacePrefabGUI()
        {
            mReplaceMode = (ReplaceMode)EditorGUILayout.EnumPopup("Replace Mode", mReplaceMode);

            if (mReplaceMode == ReplaceMode.SingleFile)
            {
                EditorGUILayout.BeginHorizontal();
                int newPrefabCount = EditorGUILayout.IntField("Replace Prefab Count", prefabCount);
                if (newPrefabCount != prefabCount)
                {
                    prefabCount = newPrefabCount;
                }
                if (GUILayout.Button("Replace"))
                {
                    Replace();
                }
                EditorGUILayout.EndHorizontal();

                DrawPrefabs(mSourcePrefabs, mTargetPrefabs);
            }
            else
            {
                EditorGUILayout.TextArea("批量将以source pattern结尾的prefab替换成target pattern结尾的prefab.\n例如如果source pattern是_lod00,target pattern是_lod01,则会将XXX/tree_lod00.prefab替换成XXX/tree_lod01.prefab");

                mSourceRegexPattern = EditorGUILayout.TextField("Source Pattern", mSourceRegexPattern);
                mTargetRegexPattern = EditorGUILayout.TextField("Target Pattern", mTargetRegexPattern);
                if (GUILayout.Button("Replace"))
                {
                    Replace();
                }
            }
        }

        void DrawPrefabs(GameObject[] sourcePrefabs, GameObject[] targetPrefabs)
        {
            for (int i = 0; i < sourcePrefabs.Length; ++i)
            {
                EditorGUILayout.BeginHorizontal();
                var prefab = EditorGUILayout.ObjectField("", sourcePrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular && sourcePrefabs[i] != prefab)
                {
                    sourcePrefabs[i] = prefab;
                }

                prefab = EditorGUILayout.ObjectField("", targetPrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular && targetPrefabs[i] != prefab)
                {
                    targetPrefabs[i] = prefab;
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
            }
        }

        public int prefabCount
        {
            get
            {
                return mSourcePrefabs.Length;
            }
            set
            {
                int minCount = Mathf.Min(value, mSourcePrefabs.Length);
                GameObject[] newSourcePrefabs = new GameObject[value];
                GameObject[] newTargetPrefabs = new GameObject[value];
                for (int i = 0; i < minCount; ++i)
                {
                    newSourcePrefabs[i] = mSourcePrefabs[i];
                    newTargetPrefabs[i] = mTargetPrefabs[i];
                }
                mSourcePrefabs = newSourcePrefabs;
                mTargetPrefabs = newTargetPrefabs;
            }
        }

        GameObject[] mSourcePrefabs = new GameObject[0];
        GameObject[] mTargetPrefabs = new GameObject[0];
        //根据pattern替换某个文件夹的所有prefab,例如_lod0到_lod1
        string mSourceRegexPattern = "_lod00";
        string mTargetRegexPattern = "_lod01";
        ReplaceMode mReplaceMode = ReplaceMode.SingleFile;
    }
}


#endif