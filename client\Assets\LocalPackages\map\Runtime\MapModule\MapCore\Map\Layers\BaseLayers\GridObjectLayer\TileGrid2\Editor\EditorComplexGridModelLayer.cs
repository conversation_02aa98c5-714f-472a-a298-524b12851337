﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class EditorComplexGridModelLayer : ComplexGridModelLayer
    {
        public EditorComplexGridModelLayer(Map map) : base(map) { }

        public void AddDestroyedObject(int objectID)
        {
            mDestroyedObjectIDs.Add(objectID);
        }

        //更新transform被改变后的物体
        public void UpdateTransformChangedObjects()
        {
            var objInfos = mLayerView.GetTransformChangedObjectIDs();
            mLayerData.UpdateTransformChangedObjects(objInfos);
        }

        //更新待删除的物体
        public void UpdateDestroyedObjects()
        {
            for (int i = 0; i < mDestroyedObjectIDs.Count; ++i)
            {
                RemoveObject(mDestroyedObjectIDs[i]);
            }
            mDestroyedObjectIDs.Clear();
        }

        public List<IComplexGridModelData> GetObjectsOfTag(int lod, string tag)
        {
            if (lod >= 0)
            {
                List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
                mLayerData.GetObjectsOfLOD(objects, lod);
                List<IComplexGridModelData> results = new List<IComplexGridModelData>(objects.Count);
                for (int i = objects.Count - 1; i >= 0; --i)
                {
                    if (objects[i].objectTag == tag)
                    {
                        results.Add(objects[i]);
                    }
                }
                return results;
            }
            else
            {
                List<IComplexGridModelData> results = new List<IComplexGridModelData>();
                int lodCount = mLayerData.lodCount;
                List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
                for (int k = 0; k < lodCount; ++k)
                {
                    mLayerData.GetObjectsOfLOD(objects, k);
                    for (int i = objects.Count - 1; i >= 0; --i)
                    {
                        if (objects[i].objectTag == tag)
                        {
                            results.Add(objects[i]);
                        }
                    }
                }
                return results;
            }
        }

        public List<IComplexGridModelData> GetObjectsInGrid(int lod, int x, int y)
        {
            var objectDatas = mLayerData.GetObjectsInGrid(lod, x, y);
            if (objectDatas == null)
            {
                objectDatas = new List<IComplexGridModelData>();
            }
            return objectDatas;
        }

        public bool IsGridDirty(int lod, int x, int y)
        {
            return mLayerData.IsGridDirty(lod, x, y);
        }

        public void SetGridDirty(int lod, int x, int y, bool dirty)
        {
            mLayerData.SetGridDirty(lod, x, y, dirty);
        }

        public GameObject GetGameObject(int objectID)
        {
            return mLayerView.GetGameObject(objectID);
        }

        public bool CheckObstacle(string prefabPath, Vector3 pos, Quaternion rotation, Vector3 scale)
        {
            var objPool = Map.currentMap.view.reusableGameObjectPool;
            var prefabRoot = objPool.Require(prefabPath, pos, scale, rotation);
            var hull = ConvexHullBuilder.CreateGameObject2DConvexHullInXZPlane(prefabRoot, true);
            objPool.Release(prefabPath, prefabRoot, Map.currentMap);

            //删除与不能放置装饰物的障碍物相交的prefab
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (collisionLayer != null)
            {
                List<ObstacleObject> collisions = new List<ObstacleObject>();
                collisionLayer.GetCollisionsOfType(PrefabOutlineType.NavMeshObstacle, collisions, CollisionAttribute.TestCollisionWhenPlaceDecorationObject);
                for (int i = 0; i < collisions.Count; ++i)
                {
                    if (PolygonCollisionCheck.Overlap(hull, collisions[i].polygon))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        List<int> mDestroyedObjectIDs = new List<int>();
    }
}

#endif