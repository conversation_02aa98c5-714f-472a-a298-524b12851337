﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    namespace config
    {

        [Black]
        public class Property
        {
            public string name;
            public PropertyType type;
            public object value;
        };
        [Black]
        public class Properties
        {
            public List<Property> properties = new List<Property>();
        };
        [Black]
        public class ModelTemplatePropertyManager
        {
            public KeyValuePair<string, int>[] modelProperties = new KeyValuePair<string, int>[0];
        }
        [Black]
        public class SpriteTemplate : BaseObject
        {
            public SpriteTemplate(int id, int propertySetID) : base(id)
            {
                this.propertySetID = propertySetID;
            }
            public string name;
            public int propertySetID;
            public int width = 1;
            public int height = 1;
            public Color color = Color.white;
        };
        [Black]
        public class SpriteTileLayerData : MapLayerData
        {
            public SpriteTileLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, int[] tiles)
                : base(layerID, name, offset, config, null)
            {
                this.tiles = tiles;
                this.xTileCount = cols;
                this.zTileCount = rows;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.gridType = gridType;
            }
            public int[] tiles;
            public int xTileCount;
            public int zTileCount;
            public float tileWidth;
            public float tileHeight;
            public GridType gridType;
        };

        public class NPCRegionTemplate
        {
            public NPCRegionTemplate(string name, int bandID, int level, Color32 color, int tileType)
            {
                this.name = name;
                this.bandID = bandID;
                this.tileType = tileType;
                this.level = level;
                this.color = color;
            }

            public string name;
            public int bandID;
            public int tileType;
            public int level = 1;
            public Color32 color;
        }

        [Black]
        public class NPCRegionLayerData : MapLayerData
        {
            public class Layer
            {
                public string name;
                public int overridenWidth;
                public int overridenHeight;
                public float overridenTileWidth;
                public float overridenTileHeight;
                public bool export;
                public int[] tiles;
                public NPCRegionTemplate[] regionTemplates = new NPCRegionTemplate[0];
            }

            public NPCRegionLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, List<Layer> layers) : base(layerID, name, offset, config, null)
            {
                this.layers = layers;
                this.xTileCount = cols;
                this.zTileCount = rows;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
            }

            public List<Layer> layers = new List<Layer>();
            public int xTileCount;
            public int zTileCount;
            public float tileWidth;
            public float tileHeight;
        }

        public class TextureGridData
        {
            public TextureGridData(int id, Color color)
            {
                this.id = id;
                this.color = color;
            }
            public int id;
            public Color color;
        }
        
        public class TextureGridLayer
        {
            public TextureGridLayer(string name, int horizontalTileCount, int verticalTileCount, float tileWidth, float tileHeight, int[,] gridIDs, List<TextureGridData> grids)
            {
                this.name = name;
                this.horizontalTileCount = horizontalTileCount;
                this.verticalTileCount = verticalTileCount;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                if (gridIDs == null)
                {
                    gridIDs = new int[verticalTileCount, horizontalTileCount];
                }
                this.gridIDs = gridIDs;
                if (grids == null)
                {
                    grids = new List<TextureGridData>();
                }
                this.grids = grids;
            }

            public string name;
            public int horizontalTileCount;
            public int verticalTileCount;
            public float tileWidth;
            public float tileHeight;
            public int[,] gridIDs;
            public List<TextureGridData> grids;
        }

        public class RegionColorData : TextureGridData
        {
            public RegionColorData(int id, Color color) : base(id, color)
            {
            }
        }

        public class RegionColorLayerData : MapLayerData
        {
            public RegionColorLayerData(int layerID, string name, Vector3 offset, TextureGridLayer[] layers, bool showRegionInGame) : base(layerID, name, offset, null, null)
            {
                this.layers = layers;
                this.showRegionInGame = showRegionInGame;
            }

            public TextureGridLayer[] layers;
            public bool showRegionInGame;
        }

        public class BuildingGridData : TextureGridData
        {
            public BuildingGridData(int id, Color color, bool walkable) : base(id, color)
            {
                this.walkable = walkable;
            }

            public bool walkable;
        }

        public class BuildingGridLayerData : MapLayerData
        {
            public BuildingGridLayerData(int layerID, string name, Vector3 offset, TextureGridLayer[] layers, Vector3 gridStartPosition) : base(layerID, name, offset, null, null)
            {
                this.layers = layers;
                this.gridStartPosition = gridStartPosition;
            }

            public TextureGridLayer[] layers;
            public Vector3 gridStartPosition;
        }

        public class EntitySpawnLayerData : MapLayerData
        {
            public EntitySpawnLayerData(int layerID, string name, Vector3 offset, int horizontalRegionCount, int verticalRegionCount, Vector2 regionSize, Vector2 npcMoveRange, List<EntitySpawnRegion> regions, List<Vector2> npcMoveWaypoints, ISpawnPointGenerationStrategy strategy, int[,] regionBrushIDs, List<EntitySpawnRegionBrush> regionBrushes, string spawnPointReferenceNPCReginLayerName) : base(layerID, name, offset, null, null)
            {
                this.horizontalRegionCount = horizontalRegionCount;
                this.verticalRegionCount = verticalRegionCount;
                this.regionSize = regionSize;
                this.npcMoveRange = npcMoveRange;
                this.npcWaypoints = npcMoveWaypoints;
                this.regions = regions;
                this.strategy = strategy;
                this.regionBrushIDs = regionBrushIDs;
                this.regionBrushes = regionBrushes;
                this.spawnPointReferenceNPCReginLayerName = spawnPointReferenceNPCReginLayerName;
            }

            public int horizontalRegionCount;
            public int verticalRegionCount;
            public Vector2 regionSize;
            public Vector2 npcMoveRange;
            public List<Vector2> npcWaypoints;
            public List<EntitySpawnRegion> regions;
            public ISpawnPointGenerationStrategy strategy;
            public int[,] regionBrushIDs;
            public List<EntitySpawnRegionBrush> regionBrushes;
            public string spawnPointReferenceNPCReginLayerName;
        }
        [Black]
        public class EntitySpawnRegion
        {
            public List<Vector2> spawnPoints = new List<Vector2>();
            public bool visible = false;
            public int seed = 0;
        }
        [Black]
        public class NavMeshData
        {
            public Vector3[] vertices;
            public int[] triangles;
            public ushort[] triangleTypes;
            public bool[] triangleStates;
        }
        [Black]
        public class NavMeshLayerData : MapLayerData
        {
            public NavMeshLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager, bool visible, float width, float height, NavMeshData[] navMeshDatas, bool oceanAreaWalkable)
                : base(layerID, name, offset, config, lodGroupManager)
            {
                this.navMeshDatas = navMeshDatas;
                this.width = width;
                this.height = height;
                this.visible = visible;
                this.oceanAreaWalkable = oceanAreaWalkable;
            }
            public NavMeshData[] navMeshDatas;
            public float width;
            public float height;
            public bool visible = true;
            public bool oceanAreaWalkable = true;
        }

        public class EditorCityTerritoryData
        {
            public EditorCityTerritoryData(int id, string name, Color color, PropertyDatas properties, Vector3[] buildingPositions)
            {
                this.color = color;
                this.id = id;
                this.name = name;
                this.properties = properties;

                this.buildingPositions = buildingPositions;
            }
            public Color color;
            public int id;
            public string name;
            public PropertyDatas properties;
            public Vector3[] buildingPositions;
        }

        public class TerritoryMeshGenerationParam
        {
            public int cornerSegment = 4;
            public float borderSizeRatio = 0.3f;
            public float uvScale = 10;
            public bool curveCorner = true;
            public string territoryMeshMaterialGuid = "";
        }

        public class TerritoryBlockInfo
        {
            public string prefabPath;
            public Bounds bounds;
            public int maskTextureWidth;
            public int maskTextureHeight;
            public Color32[] maskTextureData;
            public TerritoryBlockRegionInfo[] regions;
            public TerritoryBlockEdgeInfo[] edges;
        }

        public class TerritoryBlockRegionInfo
        {
            public int territoryID;
        }

        public class TerritoryBlockEdgeInfo
        {
            public int territoryID;
            public int neighbourTerritoryID;
        }

        public class TerritorySharedEdgeInfo
        {
            public int territoryID;
            public int neighbourTerritoryID;
            public string prefabPath;
            public Material material;
        }

        public class EditorTerritorySubLayerData
        {
            public EditorTerritorySubLayerData(string name, bool active, float tileWidth, float tileHeight, int xTileCount, int zTileCount, float displayRadius, int[,] grids, EditorCityTerritoryData[] territories, TerritoryMeshGenerationParam[] meshGenerationParams, string exportFileName, CurveRegionMeshGenerationParam curveRegionMeshGenerationParam, TerritorySharedEdgeInfo[] sharedEdges, TerritoryBlockInfo[] blocks, int maskTextureWidth, int maskTextureHeight, Color32[] maskTextureData)
            {
                this.name = name;
                this.active = active;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.xTileCount = xTileCount;
                this.zTileCount = zTileCount;
                this.displayRadius = displayRadius;
                this.grids = grids;
                this.territories = territories;
                this.meshGenerationParams = meshGenerationParams;
                this.exportFileName = exportFileName;
                this.curveRegionMeshGenerationParam = curveRegionMeshGenerationParam;
                this.sharedEdges = sharedEdges;
                this.blocks = blocks;
                if (this.blocks == null)
                {
                    this.blocks = new TerritoryBlockInfo[0];
                }
                this.maskTextureWidth = maskTextureWidth;
                this.maskTextureHeight = maskTextureHeight;
                this.maskTextureData = maskTextureData;
            }

            public string name;
            public bool active;
            public float tileWidth;
            public float tileHeight;
            public int xTileCount;
            public int zTileCount;
            public float displayRadius;
            public int[,] grids;
            public EditorCityTerritoryData[] territories = new EditorCityTerritoryData[0];
            public TerritoryMeshGenerationParam[] meshGenerationParams;
            public CurveRegionMeshGenerationParam curveRegionMeshGenerationParam;
            public TerritorySharedEdgeInfo[] sharedEdges;
            public TerritoryBlockInfo[] blocks;
            public string exportFileName;
            public int maskTextureWidth;
            public int maskTextureHeight;
            public Color32[] maskTextureData;
        }

        public class EditorTerritoryLayerData : MapLayerData
        {
            public EditorTerritoryLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, EditorTerritorySubLayerData[] subLayers) : base(layerID, name, offset, config, null)
            {
                this.subLayers = subLayers;
            }

            public EditorTerritorySubLayerData[] subLayers;
        };

        [Black]
        public class SpriteTemplates
        {
            public SpriteTemplate[] templates = new SpriteTemplate[0];
        }
        [Black]
        public class PropertySet : NamedObject
        {
            public PropertySet(int id, Properties properties, string name)
                : base(id, name)
            {
                this.properties = properties;
            }

            public Properties properties;
        };

        [Black]
        public class PropertySets
        {
            public PropertySet[] propertySets = new PropertySet[0];
        };
        [Black]
        public class NavMeshObstacle
        {
            public string assetGUID;
            public Vector3[] outlineVertices;
        }
        [Black]
        public class NavMeshObstacles
        {
            public NavMeshObstacle[] obstacles = new NavMeshObstacle[0];
        }
        [Black]
        public class MapGridSetting
        {
            public bool visible;
            public float totalWidth;
            public float totalHeight;
            public float gridWidth;
            public float gridHeight;
            public Color32 color;
        }

        [Black]
        public class GridTemplate
        {
            public Color color;
            public string name;
            public int type;
        }

        [Black]
        public class GridRegionEditorSetting
        {
            public float gridWidth = 10;
            public float gridHeight = 10;
            public int verticalGridCount = 720;
            public int horizontalGridCount = 720;
            public bool showGrid = true;
            public GridTemplate[] gridTemplates = new GridTemplate[0];
            public int[,] grids;
        }

        public class LoadRangeData
        {
            public float cameraFov = 30;
            public Vector2 resolution = new Vector2(1920, 1080);
            public float cameraRotationX = 45;
            public float cameraHeight = 50;
            public Vector2 loadRangeScale = Vector2.one;
        }

        [Black]
        public class EditorMapData : SLGMakerData
        {
            public EditorMapData(float width, float height, float borderHeight, bool isCircle, float backEndExtendedSize, float farClipOffset, bool navMeshRegionVisible, NavigationCreateMode navMeshMode, NavigationCreateMode globalObstacleMode, float groundTileSize, float frontTileSize, BackgroundSetting backgroundSetting, Version version, Bounds mapDataGenerationRange) : base(width, height, borderHeight, isCircle, backEndExtendedSize, farClipOffset, navMeshRegionVisible, groundTileSize, frontTileSize, backgroundSetting, version, mapDataGenerationRange)
            {
                viewportSize = new Vector2(width, height);
                viewCenter = new Vector3(width * 0.5f, 0, height * 0.5f);
                this.navMeshMode = navMeshMode;
                this.globalObstacleMode = globalObstacleMode;
            }

            public int mapType;
            public Vector2 viewportSize;
            public Vector3 viewCenter;
            public NavigationCreateMode navMeshMode;
            public NavigationCreateMode globalObstacleMode;
            public MapGridSetting grid = new MapGridSetting();
            public SpriteTemplates spriteTemplates = new SpriteTemplates();
            public PropertySets propertySets = new PropertySets();
            public NavMeshObstacles navMeshObstacles = new NavMeshObstacles();
            public ModelTemplatePropertyManager modelPropertyManager = new ModelTemplatePropertyManager();
            public PrefabManager gridModelLayerPrefabManager = new PrefabManager();
            public PrefabManager modelLayerPrefabManager = new PrefabManager();
            public PrefabManager circleBorderLayerPrefabManager = new PrefabManager();
            public PrefabManager complexGridModelLayerPrefabManager = new PrefabManager();
            public GridRegionEditorSetting gridRegionEditorSetting = new GridRegionEditorSetting();
            public LoadRangeData loadRangeData = new LoadRangeData();
        };
    }
}

#endif
