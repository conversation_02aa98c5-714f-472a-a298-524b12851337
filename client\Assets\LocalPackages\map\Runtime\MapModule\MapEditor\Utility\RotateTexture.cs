﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RotateTexture
    {
        public void OnDestroy()
        {
            Utils.DestroyObject(mPlane);
            if (mCamera != null)
            {
                Utils.DestroyObject(mCamera.gameObject);
            }
            Utils.DestroyObject(mOneChannelMaterial);
            Utils.DestroyObject(mFourChannelMaterial);
        }

        public Texture2D GetRotateTexture(Texture2D originalTexture, float yRotation, bool rgb)
        {
            if (mPlane == null)
            {
                Init(rgb);
            }

            if (rgb)
            {
                mPlane.GetComponent<MeshRenderer>().sharedMaterial = mFourChannelMaterial;
                mPlane.GetComponent<MeshRenderer>().sharedMaterial.SetColor("_Color", new Color(1, 1, 1, 1));
            }
            else
            {
                mPlane.GetComponent<MeshRenderer>().sharedMaterial = mOneChannelMaterial;
                mPlane.GetComponent<MeshRenderer>().sharedMaterial.SetColor("_Color", new Color(0, 0, 0, 0));
            }

            float originalWidth = originalTexture.width;
            float originalHeight = originalTexture.height;
            float left = -originalWidth * 0.5f;
            float right = originalWidth * 0.5f;
            float bottom = -originalHeight * 0.5f;
            float top = originalHeight * 0.5f;

            var lb = new Vector3(left, 0, bottom);
            var rb = new Vector3(right, 0, bottom);
            var lt = new Vector3(left, 0, top);
            var rt = new Vector3(right, 0, top);

            Quaternion rot = Quaternion.Euler(0, yRotation, 0);

            Bounds bounds = new Bounds();
            bounds.Encapsulate(rot * lb);
            bounds.Encapsulate(rot * rb);
            bounds.Encapsulate(rot * lt);
            bounds.Encapsulate(rot * rt);

            int rotatedWidth = Mathf.FloorToInt(bounds.size.x);
            int rotatedHeight = Mathf.FloorToInt(bounds.size.z);

            //添加创建RenderTexture图片大小设置检测
            if (rotatedWidth <= 0)
                rotatedWidth = 256;
            if (rotatedHeight <= 0)
                rotatedHeight = 256;

            mCamera.orthographicSize = rotatedHeight * 0.5f;
            mCamera.aspect = rotatedWidth / (float)rotatedHeight;
            mCamera.targetTexture = RenderTexture.GetTemporary(rotatedWidth, rotatedHeight, 0, RenderTextureFormat.ARGB32);
            mCamera.enabled = true;

            mPlane.SetActive(true);
            mPlane.transform.rotation = rot;
            mPlane.transform.localScale = new Vector3(rotatedWidth * 0.1f, 1, rotatedHeight * 0.1f);
            mPlane.GetComponent<MeshRenderer>().sharedMaterial.mainTexture = originalTexture;

            mCamera.Render();

            mPlane.SetActive(false);
            mCamera.enabled = false;

            RenderTexture.active = mCamera.targetTexture;
            Texture2D result = new Texture2D(mCamera.targetTexture.width, mCamera.targetTexture.height);
            result.ReadPixels(new Rect(0, 0, mCamera.targetTexture.width, mCamera.targetTexture.height), 0, 0);
            result.Apply();
            RenderTexture r = mCamera.targetTexture;
            mCamera.targetTexture = null;
            RenderTexture.ReleaseTemporary(r);

            return result;
        }

        void Init(bool rgb)
        {
            var cameraObj = new GameObject("camera obj");
            Utils.HideGameObject(cameraObj);
            mCamera = cameraObj.AddComponent<Camera>();
            mCamera.transform.position = new Vector3(0, 100, 0);
            mCamera.transform.LookAt(Vector3.zero, Vector3.back);
            mCamera.orthographic = true;
            mCamera.cullingMask = LayerMask.GetMask("RenderToTexture");
            mCamera.enabled = false;
            mCamera.clearFlags = CameraClearFlags.SolidColor;
            mCamera.backgroundColor = new Color32(0, 0, 0, 0);

            mPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
            mPlane.layer = LayerMask.NameToLayer("RenderToTexture");
            mPlane.SetActive(false);
            mOneChannelMaterial = new Material(Shader.Find("SLGMaker/Brush"));
            mFourChannelMaterial = new Material(Shader.Find("SLGMaker/TextureTransparent"));
            Utils.HideGameObject(mPlane);
        }

        Camera mCamera;
        GameObject mPlane;
        Material mOneChannelMaterial;
        Material mFourChannelMaterial;
    }
}

#endif