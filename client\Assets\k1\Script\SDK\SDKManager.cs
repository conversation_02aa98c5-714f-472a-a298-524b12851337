﻿using Common;
using K3;
using maxsdk;
using maxsdkHWExt;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using TFW;

public class SDKManager : MonoBehaviour
{

 
    private SDK37 sdk;
 

    public  SDK2CLoginInfo loginInfo;
    public  SDK2CInitInfo sDK2CInitInfo;
    public SDK2CBindSuccess bindSuccess;

    public Action<string> SDKLoginCallBack;
    public Action<bool> BuyItemResultCallBack;
    public Action InfoGameButtonRefreshCallBack;
    public Action ReStartGameCallBack;

    public string FcmPushToken { set; get; } = "";

    public List<MaxSDKHWInGameButtonBean> InfoGameButtons = new List<MaxSDKHWInGameButtonBean>();


    public Remark remark;
    public Dictionary<string, SDK2CGoodsInfo> currencyDic = new Dictionary<string, SDK2CGoodsInfo>();

     

    public  string media_source;
    public  string campaign;
    public  string adgroup;
    public  string deep_link_value;

     
    #region 单例
    private static SDKManager _instance;
    public static SDKManager instance
    { 
        get 
        {
            if (_instance == null)
            {
                var sdk = new GameObject("SDKManager");
                _instance= sdk.AddComponent<SDKManager>();
            }

            return _instance;
        }
    }
    #endregion

    private void Awake()
    { 
#if !UNITY_EDITOR && USE_SDK
        sdk= this.gameObject.AddComponent<SDK37>();
#endif
        DontDestroyOnLoad(gameObject);
    }

    private void OnDestroy()
    {

    }


    #region Login

    
    /// <summary>
    /// SDK自动登录
    /// </summary>
    public void SDKAutoLogin(bool useCache)
    { 
        if (useCache && loginInfo != null)
        {
            SDKLoginCallBack?.Invoke(string.Empty); 
        }
        else
        { 
#if USE_SDK
            Debug.Log($"37SDK登录！");
            MaxSDK.GetInstance().SetChangeAccountCallback();
            MaxSDK.GetInstance().AutoLogin();
#endif

            K3GameEvent.I.BiLogLoginFunnel("custom_loss","sdk_callup");
        }
    }

    public void RecvSuccessLoginMsg(SDK2CLoginInfo newloginInfo)
    { 
        if (loginInfo == null)//|| loginInfo.userId == newloginInfo.userId  运营要求 同账号登录返回依然重启游戏
        {
            loginInfo = newloginInfo;

            //37SDK登录成功 
            K3GameEvent.I.BiLogLoginFunnel("custom_loss", "sdk_login_succeed");
            SDKLoginCallBack?.Invoke(string.Empty); 
#if USE_SDK
            //sdk.SDKRoleSource();//获取用户的来源素材

            //SetLanguage();

            SDKRequestGoogleSkuDetail();

            SetBtnInfo();
#endif
        }
        else
        {
            //切换账号成功
            loginInfo = newloginInfo;

            //TODO 重启
            ReStartGameCallBack?.Invoke();
        }

    }

    private void SetBtnInfo()
    {
        MaxSDK.GetInstance().SetInGameButtonInfoUpdateCallback();
    }

    public void RecvFailLoginMsg(string msg)
    { 
        if (loginInfo != null)
        {
            SDKLoginCallBack?.Invoke(string.Empty);
        }
        else
        {
            K3GameEvent.I.BiLogLoginFunnel("custom_loss", "sdk_login_fail");

            SDKLoginCallBack?.Invoke(msg);
        }
    }

    #endregion



    public void SDKUserProfile()
    {
        //D.Error?.Log($"个人中心");
#if USE_SDK
        if (sdk == null) return;
        MaxSDK.GetInstance().OpenUserCenter();
#endif
    }//个人中心
    public void SDKServerCode()
    {

    }//入服统计
    public void SDKRoleLevel()
    {

    }//等级上报


    private string curCpProductID;
    public void SDKRcharge(string serverId, string roleId, string roleName, int roleLevel, string cpProductId, string productId, string orderId, string remark)
    {
#if USE_SDK
        if (sdk == null) return;

        curCpProductID=cpProductId;

        MaxSDK.GetInstance().HWPay(new MaxSDKHWPayInfo() { 
        productId = productId,
        serverId = serverId,
        roleId = roleId,
        roleName = roleName,
        roleLevel=roleLevel,
        cpProductId=cpProductId,
        cpOrderId=orderId,
        remark=remark
        }); 
#endif
    }//储值
    public void SDKOpenLink(string url)
    {
#if USE_SDK
        if (sdk == null) return;
        MaxSDK.GetInstance().OpenUrl(url,false);
#endif
    }//打开链接
    public void SDKRchargeBySubs(string serverId, string roleId, string roleName, string roleLevel, string productId, string orderId, string remark)
    {
       
        //sdk.RchargeBySubs(serverId, roleId, roleName, roleLevel, productId, orderId, remark);
    }//储值(订阅)
    /// <summary>
    /// 本地推送
    /// </summary>
    /// <param name="notifyType">1.资源类通知2.活动类通知3.体力类通知4.</param>
    /// <param name="notifyTitle"></param>
    /// <param name="notifyText"></param>
    /// <param name="delay"></param>
    /// <param name="gamePushId"></param>
    public void SDKDelayLocalPush(string notifyType, string notifyTitle, string notifyText, string delay, string gamePushId)
    {
        
        //sdk.SDKDelayLocalPush(notifyType, notifyTitle, notifyText, delay, gamePushId);
    }
    public void SDKShareToSocialAPP(string awardId, string imagePath, string serverId, string roleId, string roleName)
    {
        
        //sdk.SDKShareToSocialAPP(awardId, imagePath, serverId, roleId, roleName);
    }
    public void SDKShareToFacebook(string title, string des)
    {
#if USE_SDK
        if (sdk == null) return;

        MaxSDK.GetInstance().Share(new MaxSDKHWShareInfo()
        {
            sharePlatform= MaxSDKSharePlatform.SHARE_PLATFORM_FACEBOOK,
            title = title,
            describe = des
        });

#endif
    }
     


    public bool RewardADLoaded = false;

    public void SDKADLoaded(string adLoaded)
    {
        bool.TryParse(adLoaded, out RewardADLoaded);
    }


//    public void SDKSendGameEvent(string eventId, string param = "")
//    {
//#if USE_SDK
//        D.Warning?.Log($"eventName:{$"{eventId}"} eventType:{eventId} eventValue:{param}");

//        if (sdk == null) return;
//        MaxSDK.GetInstance().ReportEvent(new MaxSDKHWReportEventInfo() 
//        { 
//         eventType = eventId,
//         eventName = eventId,
//         eventValue = param
//        });

//#endif
//    }

    public void SDKRequestGoogleSkuDetail()
    {
#if USE_SDK
        List<string> productIDs = new List<string>()
        {
            "cs2.gem099",
            "cs2.pack002",
            "cs2.gem499",
            "cs2.gem999",
            "cs2.gem1999",
            "cs2.gem4999",
            "cs2.gem9999",
            "cs2.pack001",
            "cs2.pack002",
            "cs2.pack003",
            "cs2.pack004",
            "cs2.pack005",
            "cs2.pack006",
            "cs2.pack007",
        };
        //var list = Cfg.C.CD2GiftPrice.RawList();
        //foreach (var item in list)
        //{
        //    if (!string.IsNullOrEmpty(item.Productid))
        //    {
        //        productIDs.Add(item.Productid);
        //    }
        //}

        if (sdk == null) return;
        D.Warning?.Log($"SDKRequestGoogleSkuDetail:{SDK37.SerializeToStr(productIDs)}");
        MaxSDK.GetInstance().GetInAppSkuDetail(HWIAPType.InAppPurchase, productIDs);
#endif
    }
    public void SDKReportServer(string serverId, string roleId, string roleName, int roleLevel, int roleVIPLevel, long roleDiamonds, int CASTLE_LEVEL)
    {
        D.Warning?.Log($"SDKReportServer ServerID:{serverId}  PlayerID:{roleId}  PlayerName:{roleName}");
         
        if (sdk == null) return;
        
        MaxSDK.GetInstance().ReportEnterServer(new MaxSDKHWRoleInfo() { 
        serverId=serverId,
        roleId=roleId,
        roleName=roleName,
        roleLevel=roleLevel,
        vipLevel=roleVIPLevel,
        roleDiamonds=(int)roleDiamonds,
        castleLevel=CASTLE_LEVEL
        });
 
    }

    public void SDKReportCreateRole(string serverId, string roleId, string roleName)
    {
 
        if (sdk == null) return;

        MaxSDK.GetInstance().ReportCreateRole(new MaxSDKHWRoleInfo()
        {
            serverId = serverId,
            roleId = roleId,
            roleName = roleName
        });
 
    }

    public void SDKCustomerServiceView()
    {
 
        if (sdk == null) return;

        SDKUserProfile();

        // MaxSDK.GetInstance().OpenFAQ(); //运营要求暂时关闭客服 
 
    }

 
    /// <summary>
    /// 
    /// </summary>
    /// <param name="LanguageId"></param>
    private void ChangeLanguage(HWSDKLanguageType LanguageId)
    {
        D.Warning?.Log($"ChangeLanguage: LanguageId:{LanguageId} ");
        if (sdk == null) return;
       
        MaxSDK.GetInstance().SetSDKLanguage(LanguageId);
    }

 
    public void SDKPresentFAQView()
    {
 
        if (sdk == null) return;
       
        MaxSDK.GetInstance().OpenOfflineFAQ();
 
    }


    public void SDKTrackGameEvent(string eventType, string eventName,  string eventValue)
    {
        D.Warning?.Log($"eventType:{eventType} eventName:{$"{eventName}"}  eventValue:{eventValue}");
 
        if (sdk == null)
        {
            return;
        }
        MaxSDK.GetInstance().HWReportEvent(new MaxSDKHWReportEventInfo()
        {
             eventType= eventType,
             eventName= eventName,
             eventValue = eventValue
        });
        //sdk.SDKTrackGameEvent($"{eventName}", eventKey, eventValue);
 
    }
      


   

     

    #region  广告 相关

    private Action<AdResult> adCallBack;

    public enum AdResult
    {
        Ok,
        CloseVideo,
        LoadError,
        VIP
    }

    public void RecvRoleAdGroup(string strs)
    {
        //填充用户属性 进去用户的素材
        var str = strs.Split('|');

        if (str.Length == 4)
        {
            media_source = str[0];
            campaign = str[1];
            adgroup = str[2];
            deep_link_value = str[3];
        }
    }

    public void ShowAd(string adSource, Action<AdResult> callBack)
    {

        //if (Logic.MonthCardMgr.I.GetHavaCard())
        //{
        //    callBack?.Invoke(AdResult.VIP);
        //    return;
        //}


        adCallBack = callBack;
      

        adCallBack?.Invoke(AdResult.Ok);

//        Action doAction = new Action(() =>
//        {

//            var adInfo = new AdInfo();
//            adInfo.uid = loginInfo?.userId ?? "";//Logic.LPlayer.I.PlayerName;
//            adInfo.roleId = Logic.LPlayer.I.PlayerID.ToString();
//            adInfo.serverid = Logic.LoginMgr.I.loginData.serverid.ToString();
//            adInfo.position = adSource;
//            var adInfoJson = LitJson.JsonMapper.ToJson(adInfo);

//            Debug.Log($"{adInfoJson}");

//#if UNITY_EDITOR
//            adCallBack?.Invoke(AdResult.Ok);
//#else
//          sdk.PlayAD(adInfoJson);
//#endif

//        });

//        if (ThreadMgr.IsMainThread())
//        {
//            doAction();
//        }
//        else
//        {
//            Debug.LogWarning("从其他线程触发 ShowAd");
//            ThreadMgr.RunInMainThread(doAction);
//        }

    }


    public void CheckAD()
    {
        //if (LoadAdErrorNum < 0) //尝试加载广告已超次数，不再尝试拉取
        //{
        //    return;
        //}

        //Action doAction = new Action(() =>
        //{
        //    //sdk.CheckAD();
        //});

        //if (ThreadMgr.IsMainThread())
        //{
        //    doAction();
        //}
        //else
        //{
        //    Debug.LogWarning("从其他线程触发 CheckAD");
        //    ThreadMgr.RunInMainThread(doAction);
        //}

    }
      
    public void PlayAdEnd(string adResult)
    {
        //Action doAction = new Action(() =>
        //{
        //    var AdResult = (AdResult)(int.Parse(adResult));
        //    Debug.LogWarning($"play Ad end:{adResult}");
        //    if (AdResult == AdResult.Ok)
        //    {
        //        LoadAdErrorNum = 3;
        //    }

        //    adCallBack?.Invoke(AdResult);
        //});

        //if (ThreadMgr.IsMainThread())
        //{
        //    doAction();
        //}
        //else
        //{
        //    Debug.LogWarning("从其他线程触发 PlayAdEnd");
        //    ThreadMgr.RunInMainThread(doAction);
        //}
    }

    private int LoadAdErrorNum = 3;

    public void LoadADError(string errorCode)
    {

        Action doAction = new Action(() =>
        {
            LoadAdErrorNum--;
            //var adEvent = new ADEvent() { EventKey = "ADEvent_loadReason", EventValue = errorCode };
            //K3GameEvent.I.TaLog(adEvent);
        });

        //if (ThreadMgr.IsMainThread())
        //{
        //    doAction();
        //}
        //else
        //{
        //    Debug.LogWarning("从其他线程触发 LoadADError");
        //    ThreadMgr.RunInMainThread(doAction);
        //}
    }

    #endregion

     
    public void RecvPaySuccessMsg(string str)
    {
        D.Warning?.Log($"RecvPaySuccessMsg: {str} ");
        Action doAction = new Action(() =>
        {
            //支付成功
            var res = new StartPaySuccess();
            //SDKSendGameEvent("startCallPaySuccess", JsonUtility.ToJson((res)));
            D.Warning?.Log("startCallPaySuccess");


            var shopEvent = new K3.ShopEvent();
            shopEvent.EventKey = $"purchase_gift_{curCpProductID}";
            K3.K3GameEvent.I.BiLog(shopEvent, "custom_money_recharge");

            BuyItemResultCallBack?.Invoke(true);
        });

        if (ThreadMgr.IsMainThread())
        {
            doAction();
        }
        else
        {
            Debug.LogWarning("从其他线程触发 RecvPaySuccessMsg");
            ThreadMgr.RunInMainThread(doAction);
        }
    }
 
    public void RecvPayFailMsg(MaxSDKFailBean str)
    {
        D.Warning?.Log($"RecvPayFailMsg: {str} ");
        Action doAction = new Action(() =>
        {

            //支付失败
            var res = new StartPayFail();
            res.StateCode = str.code;
            res.ErrorMsg = str.msg;
            Debug.Log(res.ErrorMsg + " :::: " + res.ErrorMsg[0]);
            if ('['.Equals(res.ErrorMsg[0]) || res.ErrorMsg.Contains("["))
            {
                var regex = new Regex("[0-9]+");
                res.GoogleErrorCode = regex.Match(res.ErrorMsg).Value;
            }
            else
            {
                res.GoogleErrorCode = "";
            }
            //SDKSendGameEvent("startCallPayFail", JsonUtility.ToJson(res));
            D.Warning?.Log("startCallPayFail：：：" + JsonUtility.ToJson(res));

            BuyItemResultCallBack?.Invoke(false);
        });

        if (ThreadMgr.IsMainThread())
        {
            doAction();
        }
        else
        {
            Debug.LogWarning("从其他线程触发 RecvPayFailMsg");
            ThreadMgr.RunInMainThread(doAction);
        }
    }

 

    public void SetLanguage(string curLanguage)
    {
        switch (curLanguage)//
        {
            case "en":
                ChangeLanguage(HWSDKLanguageType.English);
                break;
            case "cn":
                ChangeLanguage(HWSDKLanguageType.SimplifiedChinese);
                break;
            case "zh":
                ChangeLanguage(HWSDKLanguageType.TraditionalChinese);
                break;
            case "vn":
                ChangeLanguage(HWSDKLanguageType.Vietnamese);
                break;
            case "th":
                ChangeLanguage(HWSDKLanguageType.Thai);
                break;
            case "tr":
                ChangeLanguage(HWSDKLanguageType.Turkish);
                break;
            case "jp":
                ChangeLanguage(HWSDKLanguageType.Japanese);
                break;
            case "fr":
                ChangeLanguage(HWSDKLanguageType.French);
                break;
            case "de":
                ChangeLanguage(HWSDKLanguageType.German);
                break;
            case "pt":
                ChangeLanguage(HWSDKLanguageType.Portuguese);
                break;
            case "es":
                ChangeLanguage(HWSDKLanguageType.Spanish);
                break;
            case "ru":
                ChangeLanguage(HWSDKLanguageType.Russian);
                break;
            case "ms":
                ChangeLanguage(HWSDKLanguageType.Malaysian);
                break;
            default:
                ChangeLanguage(HWSDKLanguageType.English);
                break;
        }
    }

 
    public void RecvGoodsInfoFail(string str)
    {
    }

    public void FristBindSuccess(string str)
    {
        GetBindSuccessInfo(str);
    }


    public SDK2CLoginInfo GetLoginInfo(MaxSDKLoginBean str)
    {
        var extData = maxsdk.SimpleJSON.JSONNode.Parse(str.extData);
        SDK2CLoginInfo loginInfo = new SDK2CLoginInfo();  
       
        loginInfo.userType = str.uname;
        loginInfo.userId = extData["userId"];
        loginInfo.sign = extData["sign"];
        loginInfo.timeStamp = extData["timeStamp"];
        loginInfo.area = extData["area"];
         
        return loginInfo;
    }
    public RchargeInfo GetRchargeInfo(string str)
    {
        RchargeInfo rchargeInfo = new RchargeInfo();
        var strs = str.Split('|');
        rchargeInfo.statusCode = strs[0];
        rchargeInfo.productId = strs[1];
        rchargeInfo.error_message = strs[2];
        return rchargeInfo;
    }
    public SDK2CInitInfo GetSDK2CInitInfo(string str)
    {
        sDK2CInitInfo = new SDK2CInitInfo();

        var extData = maxsdk.SimpleJSON.JSONNode.Parse(str);
       
        sDK2CInitInfo.packageName = extData["packageName"];
        sDK2CInitInfo.gameId = extData["gid"];
        sDK2CInitInfo.platformId = extData["pid"];
        sDK2CInitInfo.platformCode = extData["ptCode"];
        sDK2CInitInfo.dev = extData["device"];
        return sDK2CInitInfo;
    }

    public SDK2CGoodsInfo GetGoodsInfo(MaxSDKHWSkuDetailBean str)
    {
        SDK2CGoodsInfo sDK2CGoodsInfo = new SDK2CGoodsInfo();
        
        sDK2CGoodsInfo.productId = str.productId;
        sDK2CGoodsInfo.description = str.description;
        sDK2CGoodsInfo.title = str.title;
        sDK2CGoodsInfo.price = str.price;
        sDK2CGoodsInfo.price_currency_code = str.currencyCode;
        return sDK2CGoodsInfo;
    }
    public void GetBindSuccessInfo(string str)
    {
        bindSuccess = new SDK2CBindSuccess();
        var strs = str.Split('|');
        bindSuccess.is_gp_bind = strs[0];
        bindSuccess.is_fb_bind = strs[1];
        bindSuccess.is_tw_bind = strs[2];
        bindSuccess.is_line_bind = strs[3];
        bindSuccess.is_naver_bind = strs[4];
        bindSuccess.gp_name = strs[5];
        bindSuccess.tw_name = strs[6];
        bindSuccess.fb_name = strs[7];
        bindSuccess.line_name = strs[8];
        bindSuccess.naver_name = strs[9];
    }
 
}


public class SDKLocale
{
    /**
    * 简中
    */
    public static int SIMPLIFIED_CHINESE = 1;
    /**
    * 英文
    */
    public static int US = 2;
    /**
    * 繁体
    */
    public static int TRADITIONAL_CHINESE = 3;
    /**
    * 越南
    */
    public static int VIETNAM = 4;
    /**
    * 泰国
    */
    public static int THAILAND = 5;
    /**
    * 韩国
    */
    public static int KOREA = 6;
    /**
    * 土耳其
    */
    public static int TURKEY = 7;
    /**
    * 日本
    */
    public static int JAPAN = 8;
    /**
    * 法国
    */
    public static int FRANCE = 9;
    /**
    * 德国
    */
    public static int GERMANY = 10;
    /**
    * 葡萄牙
    */
    public static int PORTUGAL = 11;
    /**
    * 西班牙
    */
    public static int SPAIN = 12;
    /**
    * 俄罗斯
    */
    public static int RUSSIA = 13;
    /**
     * 意大利
     */
    public static int ITALY = 14;
}

public class AdInfo
{
    /// <summary>
    /// 角色名称
    /// </summary>
    public string uid;
    /// <summary>
    /// 服务器编号
    /// </summary>
    public string serverid;
    /// <summary>
    /// 角色Id
    /// </summary>
    public string roleId;
    /// <summary>
    /// 广告点位-当前是什么位置出发的广告
    /// </summary>
    public string position;
}

public class SDK2CLoginInfo
{
    public string statusCode;//状态码
    public string userType;//登录方式
    public string userId;//用户id
    public string sign;
    public string timeStamp;//时间戳
    public string dev;//设备类型这里返回"android"
    public string gameCode;//游戏简码
    public string channelId;//发布渠道默认为"googlePlay"
    public string is_gp_bind;//是否绑定google play 1代表绑定，0代表未绑定
    public string is_fb_bind;//是否绑定facebook 1代表绑定，0代表未绑定
    public string is_tw_bind;//是否绑定twitter 1代表绑定，0代表未绑定
    public string is_line_bind;//是否绑定line 1代表绑定，0代表未绑定
    public string is_naver_bind;//是否绑定naver 1代表绑定，0代表未绑定
    public string gp_name;//绑定的google play用户名
    public string tw_name;//绑定的twitter用户名
    public string fb_name;//绑定的facebook用户名
    public string line_name;//绑定的line用户名
    public string naver_name; //绑定的naver用户名
    public string token;//当前客户端的推送标识
    public string appsflyerId;//adid
    public string deviceID;//GPID UUID 
    public string area;
}
public class RchargeInfo
{
    public string statusCode;//状态码
    public string productId;//商品id
    public string error_message;//错误码
}
public class S2CLoginInfo
{
    public string server_id;//服务器id
    public string server_ip;//服务器ip
    public string server_port;//服务器端口
    public string server_zone;
    public string chat_ip;//聊天服务器ip
    public string chat_port;//聊天服务器端口
    public int maintaince_time;//维护版本
    public int ServerTime;//系统时间
    public int maintaince_endtime;
    public int isUpgrade;//0 正常 1推荐 2 强制 3 维护
    public string upgrade_url;//
    public string message;
    public string appVersion;//版本号
    public string userId;//用户id
    public int group;//登录策略组
    public string repair_title;
    public string repair_url;
}
public class SDKParamRemark
{
    public string castle_level;//大本id
    public string game_account_id;//游戏账号ID
    public string game_version;//游戏版本
    public string purchase_id;//礼包id
    public string purchase_type;//礼包页签类型
    public string order_id;//订单号
    public string purchase_real_type;//礼包类型
    public string purchase_content;//礼包内容
    public string purchase_gold;//礼包金币
}
public class SDK2CInitInfo
{
    public string packageName;//游戏包名
    public string gameId;//游戏id
    public string platformId;//平台id
    public string platformCode;//平台简码
    public string dev;//设备类型
}
public class SDK2CGoodsInfo
{
    public string productId;//商品id
    public string description;//描述
    public string title;//标题
    public string price;//金额
    public string price_currency_code;//币种

    public string GetFormattedPrice()
    {
        return price;
    }
}
public class SDK2CBindSuccess
{
    public string is_gp_bind;//是否绑定google play 1代表绑定，0代表未绑定
    public string is_fb_bind;//是否绑定facebook 1代表绑定，0代表未绑定
    public string is_tw_bind;//是否绑定twitter 1代表绑定，0代表未绑定
    public string is_line_bind;//是否绑定line 1代表绑定，0代表未绑定
    public string is_naver_bind;//是否绑定naver 1代表绑定，0代表未绑定
    public string gp_name;//绑定的google play用户名
    public string tw_name;//绑定的twitter用户名
    public string fb_name;//绑定的facebook用户名
    public string line_name;//绑定的line用户名
    public string naver_name; //绑定的naver用户名
}
public class PhoneInfo
{
    public string cpu_name;//CPU型号
    public string number_of_cpu_cores;//CPU核心数
    public string cpu_max_freq_khz;//CPU最大主频
    public string total_memory;//RAM容量
}
public class IOSSDKLoginInfo
{
    public string FBName;
    public string GCName;
    public string LineName;
    public string NaverName;
    public string PID;
    public string SID;
    public string TWName;
    public string afid;
    public string channelId;
    public string dev;
    public string gameCode;
    public string isFBBind;
    public string isGCBind;
    public string isLineBind;
    public string isNaverBind;
    public string isTWBind;
    public string ptCode;
    public string remark;
    public string sign;
    public string timeStamp;
    public string uid;
    public string uniqueId;
    public string userId;
    public string userType;
}
public class Remark
{
    public string SESSION_ID;
}
public class IOSSDKInitInfo
{
    public string ptCode;
    public string device;
    public string gid;
    public string bundleld;
    public string pid;
    public string curLanguage;
    public string curCountry;
    public string os;
    public string osVersion;
}
public class IOSSDKProductInfo
{
    public string credit1;
    public string credit2;
    public string credit3;
    public string credit4;
    public string credit5;
    public string credit6;
    public string credit7;
}
public class IOSSDKInitFail
{
    public string msg;
    public int code;
}