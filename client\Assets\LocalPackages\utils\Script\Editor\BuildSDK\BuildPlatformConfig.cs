﻿








using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using Unity.Collections;
using UnityEditor;
using UnityEngine;

namespace TFW.Build
{
    public class BuildPlatformConfig
    {
        /// <summary>
        /// 获取项目根目录
        /// </summary>
        /// <returns></returns>
        public static string GetPrjRootPath()
        {
            string currentDir = Application.dataPath;
            int index = currentDir.LastIndexOf("/", System.StringComparison.Ordinal);
            currentDir = currentDir.Remove(index);
            return currentDir;
        }

        public static string GetTargetRootPath()
        {
            return GetPrjRootPath() + "/Assets/TempPackage";          
        }
        
 
        public static string GetPackagePath()
        {
            return GetPrjRootPath() + "/Library/PackageCache";          
        }


        public static string FindPapckagePath(string keyWord)
        {
            var packagePath = GetPackagePath();
            string[] dirPaths = Directory.GetDirectories(packagePath);
            var targetDir = string.Empty;

            foreach (var dir in dirPaths)
            {
                if (dir.Contains("com.tfw.sdk.chat"))
                {
                    targetDir =  dir;
                    break;
                }
            }
            
            Debug.Log("bashPath========================================================== targetDir =" + targetDir);	            

            return targetDir;
        }
        
              
        
    }
}