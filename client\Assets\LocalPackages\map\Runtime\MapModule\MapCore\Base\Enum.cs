﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System;

namespace TFW.Map
{
    //地图层的类型,主要用于save和load地图数据时来判读地图层的类型
    public class MapLayerType
    {
        public const int kGridModelLayer = 1;
        public const int kModelLayer = 2;
        public const int kSpriteTileLayer = 3;
        public const int kNavMeshLayer = 4;
        public const int kNPCRegionLayer = 5;
        public const int kBlendTerrainLayer = 6;
        public const int kEntitySpawnLayer = 7;
        public const int kRailwayLayer = 8;
        public const int kLODLayer = 9;
        public const int kCircleBorderLayer = 10;
        public const int kCollisionLayer = 11;
        public const int kRuinLayer = 12;
        public const int kRiverLayer = 13;
        public const int kCameraColliderLayer = 14;
        public const int kComplexGridModelLayer = 15;
        public const int kRegionLayer = 16;
        public const int kSplitFogLayer = 17;
        public const int kEditorTerritoryLayer = 18;
        public const int kRegionColorLayer = 19;
        public const int kBuildingGridLayer = 20;
        public const int kDecorationBorderLayer = 21;
        public const int kVaryingTileSizeTerrainLayer = 22;
    }

    //地图层格子的类型
    public enum GridType
    {
        //矩形
        Rectangle,
        //六边形
        Hexagonal,
    }

    [Flags]
    public enum ObjectFlag
    {
        //对象是否已被删除
        kObjectDestroyed = 1,
        //是否加入对象列表
        kAddToObjectList = 2,
        //是否使用render texture的model
        kUseRenderTextureModel = 4,
    }

    [System.Flags]
    public enum PrefabOutlineType
    {
        //用来生成导航网格
        NavMeshObstacle = 0,
        //用来生成障碍物
        ObjectPlacementObstacle = 1,
    }
}
