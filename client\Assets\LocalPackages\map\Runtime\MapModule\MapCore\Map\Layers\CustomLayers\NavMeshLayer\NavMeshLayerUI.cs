﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;

namespace TFW.Map
{
    [CustomEditor(typeof(NavMeshLayerLogic))]
    public partial class NavMeshLayerUI : UnityEditor.Editor
    {
        enum PlacePointState
        {
            WaitingForStartPos,
            WaitingForEndPos,
            ReadyForPathFinding,
        }

        void OnEnable()
        {
            mLogic = target as NavMeshLayerLogic;

            mTypeNames = System.Enum.GetNames(typeof(PrefabOutlineType));
            mNavMeshTypeNames = new string[]
            {
                "Walkable Area",
                "Obstacle Area",
            };

            mLogic.UpdateGizmoVisibilityState();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                EditorGUILayout.BeginVertical("GroupBox");
                var layerID = mLogic.layerID;
                var layerData = map.FindObject(layerID) as NavMeshLayerData;
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());

                var layer = map.GetMapLayerByID(layerID) as NavMeshLayer;

                EditorGUILayout.EndVertical();
                mLogic.mode = (NavigationCreateMode)EditorGUILayout.EnumPopup("Mode", mLogic.mode);

                if (mLogic.mode == NavigationCreateMode.CreateIslandAndOceanNavigationMesh)
                {
                    layer.layerData.oceanAreaWalkable = EditorGUILayout.ToggleLeft("Ocean Area Walkable", layer.layerData.oceanAreaWalkable);
                }

                var show = EditorGUILayout.ToggleLeft("Show Navigation Mesh", mShowNavMesh);
                if (mShowNavMesh != show)
                {
                    mShowNavMesh = show;
                    layer.ShowNavMesh(mShowNavMesh);
                }

                if (mLogic.mode != NavigationCreateMode.FindPath)
                {
                    mFoldState = EditorGUILayout.Foldout(mFoldState, "NavMesh Setting");
                    if (mFoldState)
                    {
                        EditorGUILayout.BeginVertical("GroupBox");
                        EditorGUILayout.BeginVertical("GroupBox");
                        mLogic.BeginAdd();
                        int nLayers = map.GetMapLayerCount();
                        for (int i = 0; i < nLayers; ++i)
                        {
                            var mapLayer = map.GetMapLayerByIndex(i);
                            if (mapLayer is ModelLayer || mapLayer is EditorGridModelLayer)
                            {
                                mLogic.AddValidMapLayer(mapLayer);
                            }
                        }

                        mSelectedNavMeshType = EditorGUILayout.Popup(mSelectedNavMeshType, mNavMeshTypeNames);

                        if (mSelectedNavMeshType == 0)
                        {
                            EditorGUILayout.BeginVertical();
                            mLogic.radius = EditorGUILayout.FloatField("Radius", mLogic.radius);
                            mLogic.maximumArea = EditorGUILayout.FloatField("Maximum Triangle Area", mLogic.maximumArea);
                            mLogic.minimumAngle = EditorGUILayout.FloatField("Minimum Triangle Angle", mLogic.minimumAngle);
                            mLogic.minimumAngle = Mathf.Clamp(mLogic.minimumAngle, 40.0f, 120.0f);
                            if (mLogic.maximumArea < 100)
                            {
                                mLogic.maximumArea = 100;
                            }
                            EditorGUILayout.EndVertical();
                        }

                        EditorGUILayout.BeginHorizontal();
                        if (GUILayout.Button("Create"))
                        {
                            PrefabOutlineType type = PrefabOutlineType.NavMeshObstacle;
                            if (mLogic.mode == NavigationCreateMode.CreateIslandAndOceanObstacles ||
                                mLogic.mode == NavigationCreateMode.CreateLandObstacles)
                            {
                                type = PrefabOutlineType.ObjectPlacementObstacle;
                            }
                            var mesh = CreateOutlineNavMesh(type, mLogic.mode);
                            if (mesh != null)
                            {
                                var layerView = layer.layerView;
                                layerView.ShowNavMesh(true);
                            }
                        }
                        EditorGUILayout.EndHorizontal();

                        EditorGUILayout.EndVertical();

                        EditorGUILayout.EndVertical();
                    }
                }
                else if (mLogic.mode == NavigationCreateMode.FindPath)
                {
                    DrawPathFindingUI();
                }

                int vertexCount = 0;
                int indexCount = 0;
                mLogic.layer.GetTotalVertexCount(out vertexCount, out indexCount);
                if (vertexCount != 0)
                {
                    EditorGUILayout.BeginVertical();
                    EditorGUILayout.IntField("Vertex Count", vertexCount);
                    EditorGUILayout.IntField("Triangle Count", (int)(indexCount / 3));
                    EditorGUILayout.EndVertical();
                }

                if (GUILayout.Button("Export NavMesh"))
                {
                    var folder = EditorUtility.SaveFolderPanel("Select export folder", "", "");
                    CreateOutlineNavMesh(PrefabOutlineType.NavMeshObstacle, mLogic.mode);
                    NavMeshLayerLogic.ExportNavMesh(mLogic.layer, folder);
                }
            }
        }

        void DrawMapLayerItem(MapLayerBase layer, bool selected)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(layer.name);
            bool newState = EditorGUILayout.Toggle(selected);
            if (newState != selected)
            {
                mLogic.SetSelection(layer.id, newState);
            }
            EditorGUILayout.EndHorizontal();
        }

        NavMeshBlock[] CreateOutlineNavMesh(PrefabOutlineType type, NavigationCreateMode createMode)
        {
            var meshies = mLogic.layer.CreateNavMesh(2, 2, type, mLogic.radius, mLogic.minimumAngle, mLogic.maximumArea, false, createMode);
            return meshies;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            bool rightButtonUp = false;
            bool leftButtonUp = false;
            if (currentEvent.type == EventType.MouseUp)
            {
                if (currentEvent.button == 0)
                {
                    leftButtonUp = true;
                }
                else if (currentEvent.button == 1)
                {
                    rightButtonUp = true;
                }
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (mLogic.mode == NavigationCreateMode.FindPath)
            {
                if (leftButtonUp)
                {
                    if (mState == PlacePointState.WaitingForStartPos)
                    {
                        SetStartPos(pos.x, pos.z);
                    }
                    else if (mState == PlacePointState.WaitingForEndPos)
                    {
                        SetEndPos(pos.x, pos.z);
                    }
                }
                else if (rightButtonUp)
                {
                    ResetPathFinding();
                }

                if (mHighlightTriangle)
                {
                    mLogic.layer.navigationDebugger?.HighlightTriangle(Geo.GeoUtils.Vector3ToCoord(pos));
                }
                else
                {
                    mLogic.layer.navigationDebugger?.DisableTriangleHighlighting();
                }
                if (mHighlightConvex)
                {
                    bool toggleWalkable = false;
                    if (currentEvent.type == EventType.MouseDown && currentEvent.button == 1)
                    {
                        toggleWalkable = true;
                    }
                    mLogic.layer.navigationDebugger?.HighlightConvex(Geo.GeoUtils.Vector3ToCoord(pos), toggleWalkable);
                }
                else
                {
                    mLogic.layer.navigationDebugger?.DisableConvexHighlighting();
                }

                HandleUtility.AddDefaultControl(0);
            }

            DrawPathInfo();
        }

        NavMeshLayerLogic mLogic;
        string[] mTypeNames;
        string[] mNavMeshTypeNames;
        int mSelectedNavMeshType = 0;
        bool mFoldState = true;
        bool mShowNavMesh = true;
    }
}

#endif