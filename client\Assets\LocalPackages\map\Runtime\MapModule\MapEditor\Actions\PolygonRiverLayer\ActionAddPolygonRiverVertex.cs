﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class ActionAddPolygonRiverVertex : EditorAction
    {
        public ActionAddPolygonRiverVertex(int layerID, int dataID, int index, Vector3 pos, PrefabOutlineType type)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mPosition = pos;
            mOutlineType = type;
            mIndex = index;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.InsertCollisionVertex(mOutlineType, mDataID, mIndex, mPosition);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RemoveCollsionVertex(mOutlineType, mDataID, mIndex);
            return true;
        }

        int mLayerID;
        int mDataID;
        int mIndex;
        Vector3 mPosition;
        PrefabOutlineType mOutlineType;
    }
}


#endif