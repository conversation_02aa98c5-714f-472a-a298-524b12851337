﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using TFW.Map.Geo;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        //创建带关卡的海岛地图的navmesh
        static void CreateIslandGateNavMesh(PrefabOutlineType type, Vector3 min, Vector3 max, float agentRadius, float minimumAngle, float maximumArea, bool oceanAreaEnabled, bool useDelaunay, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            meshVertices = null;
            meshIndices = null;
            triangleTypes = null;
            triangleStates = null;

            var map = Map.currentMap;
            var collisionLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (collisionLayer == null)
            {
                return;
            }

            var combineMeshies = new List<MeshItem>();

            Vector3[] verticesStep0;
            int[] indicesStep0;
            CreateNavMeshInSpecialRegionsWithIDOfZero(useDelaunay, minimumAngle, maximumArea, out verticesStep0, out indicesStep0);

            List<ushort> triangleTypeList;
            List<bool> triangleStateList;
            //创建岛的navmesh
            Vector3[] meshVerticesStep1;
            int[] meshIndicesStep1;
            CreateSpecialRegionNavMesh(type, agentRadius, true, out meshVerticesStep1, out meshIndicesStep1, out triangleTypeList, out triangleStateList);
            triangleTypes = triangleTypeList.ToArray();
            triangleStates = triangleStateList.ToArray();
            if (meshVerticesStep1 != null)
            {
                combineMeshies.Add(new MeshItem(meshVerticesStep1, meshIndicesStep1));
            }

            //closed regions
            List<ClosedAreaDetector.Area> closedRegions = collisionLayer.regionDetector.CalculateRegions(verticesStep0, indicesStep0);
            for (int i = 0; i < closedRegions.Count; ++i)
            {
                combineMeshies.Add(new MeshItem(closedRegions[i].meshVertices, closedRegions[i].meshIndices));
                int triangleCount = closedRegions[i].meshIndices.Length / 3;
                for (int t = 0; t < triangleCount; ++t)
                {
                    triangleTypeList.Add((ushort)closedRegions[i].type);
                    triangleStateList.Add(true);
                }

#if false
                //temp code
                BigMeshViewer viewer = new BigMeshViewer();
                viewer.Create(null, $"closedRegion {i}", closedRegions[i].meshVertices, closedRegions[i].meshIndices, false, Color.blue);
#endif
            }

            CombineMesh(combineMeshies, out meshVertices, out meshIndices);
            if (meshVertices == null || meshVertices.Length == 0)
            {
                meshVertices = verticesStep0;
                meshIndices = indicesStep0;
                triangleTypes = null;
                triangleStates = null;
            }
            else
            {
                triangleTypes = triangleTypeList.ToArray();
                triangleStates = triangleStateList.ToArray();
            }
        }

        //创建area type为0的special region内的导航网格,其他区域都算障碍物
        static void CreateNavMeshInSpecialRegionsWithIDOfZero(bool useDelaunay, float minimumAngle, float maximumArea, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshIndices = null;
            meshVertices = null;
            //得到area type为0的special region
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            List<MapCollisionData> collisionsWithAreaTypeZero0 = new List<MapCollisionData>();
            collisionLayer.GetCollisionsOfType(collisionsWithAreaTypeZero0, CollisionAttribute.SpecialRegion);
            List<List<Vector3>> polygonVertices = new List<List<Vector3>>();
            for (int i = collisionsWithAreaTypeZero0.Count - 1; i >= 0; --i)
            {
                if (collisionsWithAreaTypeZero0[i].type != 0)
                {
                    collisionsWithAreaTypeZero0.RemoveAt(i);
                }
                else
                {
                    List<Vector3> polygon = collisionsWithAreaTypeZero0[i].GetOutlineVerticesCopy(PrefabOutlineType.NavMeshObstacle);
                    polygonVertices.Add(polygon);
                }
            }

            //获取地图与边框的交集
            var range = Map.currentMap.data.mapDataGenerationRange;
            float minX = range.min.x;
            float minZ = range.min.z;
            float maxX = range.max.x;
            float maxZ = range.max.z;
            List<List<Vector3>> holes;
            List<List<Vector3>> noneHoles;
            List<List<Vector3>> intersections = PolygonAlgorithm.GetPolygonIntersections(minX, minZ, maxX, maxZ, polygonVertices, out holes);

            //获取除开0号special region的所有障碍物
            List<IObstacle> obstacles = Utils.CreateObstacles(LayerTypeMask.kCollisionLayer, false, PrefabOutlineType.NavMeshObstacle, CheckMapCollisionOperation.kGenerateNavMesh, true, true, true);
            List<List<Vector3>> obstaclePolygons = new List<List<Vector3>>();
            for (int i = 0; i < obstacles.Count; ++i)
            {
                obstaclePolygons.Add(obstacles[i].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle));
            }
            //计算有效区域减去障碍物后的区域
            PolygonAlgorithm.GetDifferencePolygons(intersections, obstaclePolygons, out noneHoles, out holes);

            if (noneHoles.Count > 0 || holes.Count > 0)
            {
                Triangulator.TriangulatePolygons(noneHoles, holes, useDelaunay, minimumAngle, maximumArea, null, out meshVertices, out meshIndices);
            }
        }
    }
}


#endif