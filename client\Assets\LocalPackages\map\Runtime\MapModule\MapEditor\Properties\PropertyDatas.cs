﻿ 



 
 


/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public enum PropertyType
    {
        kPropertyFloat,
        kPropertyInt,
        kPropertyBool,
        kPropertyString,
        kPropertyVector2,
        kPropertyVector3,
        kPropertyVector4,
        k<PERSON>ropertyColor,
        kPropertyIntArray,
    }

    public abstract class PropertyBase
    {
        public PropertyBase(string name)
        {
            mName = name;
        }
        public abstract PropertyBase Clone();
        public abstract PropertyType type { get; }
        public string name { get { return mName; } set { mName = value; } }
        protected string mName;
    }

    public class PropertyData<T> : PropertyBase
    {
        public PropertyData(string name, PropertyType type, T value) : base(name)
        {
            mValue = value;
            mType = type;
        }

        public override PropertyType type
        {
            get { return mType; }
        }

        public override PropertyBase Clone()
        {
            return new PropertyData<T>(mName, mType, mValue);
        }

        public T value { get { return mValue; } set { mValue = value; } }

        PropertyType mType;
        T mValue;
    }

    public class PropertyDatas
    {
        public PropertyDatas(PropertyBase[] properties)
        {
            if (properties != null)
            {
                for (int i = 0; i < properties.Length; ++i)
                {
                    AddProperty(properties[i]);
                }
            }
        }

        public void AddProperty(PropertyBase prop)
        {
            if (mProperties.Contains(prop) == false)
            {
                mProperties.Add(prop);
            }
        }

        public void RemoveProperty(string name)
        {
            var prop = FindProperty(name);
            if (prop != null)
            {
                mProperties.Remove(prop);
            }
        }

        public void RemoveProperty(PropertyBase property)
        {
            mProperties.Remove(property);
        }

        public int GetPropertyCount()
        {
            return mProperties.Count;
        }

        public PropertyBase GetProperty(int index)
        {
            if (index >= 0 && index < mProperties.Count)
            {
                return mProperties[index];
            }
            return null;
        }

        public PropertyDatas Clone()
        {
            PropertyBase[] newProperties = new PropertyBase[mProperties.Count];
            for (int i = 0; i < mProperties.Count; ++i)
            {
                if (IsArrayProperty(mProperties[i]))
                {
                    newProperties[i] = CloneArrayProperty(mProperties[i]);
                }
                else
                {
                    newProperties[i] = mProperties[i].Clone();
                }
            }
            PropertyDatas pd = new PropertyDatas(newProperties);
            return pd;
        }

        public int GetInt(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyInt)
            {
                return (prop as PropertyData<int>).value;
            }
            return 0;
        }

        public void SetInt(string name, int value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<int>(name, PropertyType.kPropertyInt, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<int>;
                p.value = value;
            }
        }

        public float GetFloat(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyFloat)
            {
                return (prop as PropertyData<float>).value;
            }
            return 0f;
        }

        public void SetFloat(string name, float value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<float>(name, PropertyType.kPropertyFloat, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<float>;
                p.value = value;
            }
        }

        public string GetString(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyString)
            {
                return (prop as PropertyData<string>).value;
            }
            return "";
        }

        public void SetString(string name, string value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<string>(name, PropertyType.kPropertyString, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<string>;
                p.value = value;
            }
        }

        public bool GetBool(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyBool)
            {
                return (prop as PropertyData<bool>).value;
            }
            return false;
        }

        public void SetBool(string name, bool val)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyBool)
            {
                var p = prop as PropertyData<bool>;
                p.value = val;
            }
        }

        public Vector3 GetVector3(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyVector3)
            {
                return (prop as PropertyData<Vector3>).value;
            }
            return Vector3.zero;
        }

        public void SetVector3(string name, Vector3 value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<Vector3>(name, PropertyType.kPropertyVector3, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<Vector3>;
                p.value = value;
            }
        }

        public Vector2 GetVector2(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyVector2)
            {
                return (prop as PropertyData<Vector2>).value;
            }
            return Vector2.zero;
        }

        public void SetVector2(string name, Vector2 value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<Vector2>(name, PropertyType.kPropertyVector2, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<Vector2>;
                p.value = value;
            }
        }

        public Vector4 GetVector4(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyVector4)
            {
                return (prop as PropertyData<Vector4>).value;
            }
            return Vector4.zero;
        }

        public void SetVector4(string name, Vector4 value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<Vector4>(name, PropertyType.kPropertyVector4, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<Vector4>;
                p.value = value;
            }
        }

        public Color GetColor(string name)
        {
            PropertyBase prop = FindProperty(name);
            if (prop != null && prop.type == PropertyType.kPropertyColor)
            {
                return (prop as PropertyData<Color>).value;
            }
            return new Color(1, 1, 1, 1);
        }

        public void SetColor(string name, Color32 value)
        {
            PropertyBase prop = FindProperty(name);
            if (prop == null)
            {
                prop = new PropertyData<Color>(name, PropertyType.kPropertyColor, value);
                AddProperty(prop);
            }
            else
            {
                var p = prop as PropertyData<Color>;
                p.value = value;
            }
        }

        public PropertyBase FindProperty(string name)
        {
            int n = mProperties.Count;
            for (int i = 0; i < n; ++i)
            {
                if (mProperties[i].name == name)
                {
                    return mProperties[i];
                }
            }
            return null;
        }

        public bool EqualTypeWith(PropertyDatas p)
        {
            if (mProperties.Count != p.GetPropertyCount())
            {
                return false;
            }
            for (int i = 0; i < mProperties.Count; ++i)
            {
                if (mProperties[i].type != p.properties[i].type ||
                    mProperties[i].name != p.properties[i].name)
                {
                    return false;
                }
            }

            return true;
        }

        public string ConvertToJsonString()
        {
            Dictionary<string, object> json = new Dictionary<string, object>();

            for (int i = 0; i < mProperties.Count; ++i)
            {
                if (mProperties[i].type == PropertyType.kPropertyBool)
                {
                    json[mProperties[i].name] = (mProperties[i] as PropertyData<bool>).value;
                }
                else if (mProperties[i].type == PropertyType.kPropertyFloat)
                {
                    json[mProperties[i].name] = (mProperties[i] as PropertyData<float>).value;
                }
                else if (mProperties[i].type == PropertyType.kPropertyInt)
                {
                    json[mProperties[i].name] = (mProperties[i] as PropertyData<int>).value;
                }
                else if (mProperties[i].type == PropertyType.kPropertyIntArray)
                {
                    json[mProperties[i].name] = (mProperties[i] as PropertyData<int[]>).value;
                }
                else if (mProperties[i].type == PropertyType.kPropertyString)
                {
                    json[mProperties[i].name] = (mProperties[i] as PropertyData<string>).value;
                }
                else if (mProperties[i].type == PropertyType.kPropertyVector2)
                {
                    Dictionary<string, object> container = new Dictionary<string, object>();
                    var val = (mProperties[i] as PropertyData<Vector2>).value;
                    container["x"] = val.x;
                    container["y"] = val.y;
                    json[mProperties[i].name] = container;
                }
                else if (mProperties[i].type == PropertyType.kPropertyVector3)
                {
                    Dictionary<string, object> container = new Dictionary<string, object>();
                    var val = (mProperties[i] as PropertyData<Vector3>).value;
                    container["x"] = val.x;
                    container["y"] = val.y;
                    container["z"] = val.z;
                    json[mProperties[i].name] = container;
                }
                else if (mProperties[i].type == PropertyType.kPropertyVector4)
                {
                    Dictionary<string, object> container = new Dictionary<string, object>();
                    var val = (mProperties[i] as PropertyData<Vector4>).value;
                    container["x"] = val.x;
                    container["y"] = val.y;
                    container["z"] = val.z;
                    container["w"] = val.w;
                    json[mProperties[i].name] = container;
                }
                else if (mProperties[i].type == PropertyType.kPropertyColor)
                {
                    Dictionary<string, object> container = new Dictionary<string, object>();
                    var val = (mProperties[i] as PropertyData<Color>).value;
                    container["r"] = val.r;
                    container["g"] = val.g;
                    container["b"] = val.b;
                    container["a"] = val.a;
                    json[mProperties[i].name] = container;
                }
                else
                {
                    Debug.Assert(false);
                }
            }

            return JSONParser.Serialize(json);
        }

        bool IsArrayProperty(PropertyBase p)
        {
            return p.type == PropertyType.kPropertyIntArray;
        }

        PropertyBase CloneArrayProperty(PropertyBase p)
        {
            if (p.type == PropertyType.kPropertyIntArray)
            {
                PropertyData<int[]> newP = new PropertyData<int[]>(p.name, p.type, (p as PropertyData<int[]>).value.Clone() as int[]);
                return newP;
            }
            else
            {
                Debug.Assert(false);
            }
            return null;
        }

        public List<PropertyBase> properties { get { return mProperties; } }

        List<PropertyBase> mProperties = new List<PropertyBase>();
    }
}