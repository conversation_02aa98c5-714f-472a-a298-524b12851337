﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;

namespace TFW.Map
{
    public interface TexturePackStrategy
    {
        void OnDestroy();

        bool Pack(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, bool deleteTextureAtlas, out TexturePackResult output);
        bool CanPackInOneTexture(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, out TexturePackResult output);
        int GetMaxPackCountInOneTexture(int textureSize, int borderSize, int padding, int maxTextureSize);
    }
}

#endif