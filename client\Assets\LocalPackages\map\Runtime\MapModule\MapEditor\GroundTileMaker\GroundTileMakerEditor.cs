﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [CustomEditor(typeof(GroundTileMaker))]
    public class GroundTileMakerEditor : UnityEditor.Editor
    {
        void OnEnable()
        {
            mChannelNames = new string[] { "Red", "Green", "Blue", "Alpha", "RGB", "RGBA" };
        }

        void OnDestroy()
        {
            mIndicator.OnDestroy();
        }

        void OnSceneGUI()
        {
            var maker = target as GroundTileMaker;

            if (!maker.inited)
            {
                return;
            }

            var currentEvent = Event.current;
            var camera = SceneView.GetAllSceneCameras()[0];
            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, camera);
            var worldPos = Utils.FromScreenToWorldPosition(screenPos, camera);

            float brushSize = maker.GetBrushSizeInWorldUnits();
            if (maker.operation == GroundTileMaker.Operation.PaintTexture ||
                maker.operation == GroundTileMaker.Operation.PaintEdge)
            {
                Handles.DrawWireDisc(worldPos, Vector3.up, brushSize * 0.5f);
                mIndicator.SetActive(true);
                worldPos.y += 0.2f;
                mIndicator.SetPosition(worldPos);
                var size = maker.GetBrushSizeInWorldUnits();
                mIndicator.SetSize(size);
                var texture = maker.brushManager.GetActiveBrush().GetTexture(maker.useRotatedBrush);
                mIndicator.SetTexture(texture);
            }
            if (currentEvent.type == EventType.MouseDrag || currentEvent.type == EventType.MouseMove)
            {
                SceneView.RepaintAll();
            }

            bool isMouseDown = currentEvent.type == EventType.MouseDown;
            if ((currentEvent.type == EventType.MouseDrag || isMouseDown) &&
                currentEvent.button == 0 && currentEvent.alt == false)
            {
                if (maker.operation == GroundTileMaker.Operation.PaintTexture)
                {
                    if (isMouseDown)
                    {
                        maker.StartRecording();
                    }
                    maker.Paint(worldPos, currentEvent.control, maker.maskTextureIndex);
                }
                else if (maker.operation == GroundTileMaker.Operation.PaintEdge)
                {
                    if (isMouseDown)
                    {
                        maker.StartRecording();
                    }
                    maker.PaintEdge(worldPos, currentEvent.control, maker.maskTextureIndex);
                }
            }

            if (currentEvent.button == 1 && currentEvent.type == EventType.MouseDown)
            {
                mMarkerWorldPos = worldPos;
                CreateContextMenu();
                if (mMenu != null)
                {
                    mMenu.ShowAsContext();
                }
            }

            if (currentEvent.type == EventType.MouseUp)
            {
                if (maker.isPainting)
                {
                    maker.StopRecording();
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    maker.brushSize = maker.brushSize + maker.brushSizeStep;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    maker.brushSize = maker.brushSize - maker.brushSizeStep;
                    Repaint();
                }
                if (currentEvent.keyCode == KeyCode.LeftArrow)
                {
                    maker.strength = maker.strength - maker.strengthStep;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.RightArrow)
                {
                    maker.strength = maker.strength + maker.strengthStep;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.Space)
                {
                    if (maker.useRandomBrushRotation)
                    {
                        float randomAngle = Random.Range(0.0f, 360.0f);
                        if (maker.fixedBrushRotation != 0)
                        {
                            randomAngle = maker.fixedBrushRotation;
                        }
                        maker.brushManager.CreateRotatedTexture(randomAngle, maker.channel >= 4);
                    }
                }
                else if (currentEvent.keyCode == KeyCode.R && currentEvent.shift == false)
                {
                    maker.RefreshMaskTextures(false);
                }
                else if (currentEvent.keyCode == KeyCode.E)
                {
                    if (maker.operation == GroundTileMaker.Operation.PaintEdge)
                    {
                        maker.SetOperation(GroundTileMaker.Operation.PaintTexture);
                        Repaint();
                    }
                    else if (maker.operation == GroundTileMaker.Operation.PaintTexture)
                    {
                        maker.SetOperation(GroundTileMaker.Operation.PaintEdge);
                        Repaint();
                    }
                }
            }

            if (maker.operation == GroundTileMaker.Operation.PaintTexture ||
                maker.operation == GroundTileMaker.Operation.PaintEdge)
            {
                HandleUtility.AddDefaultControl(0);
            }
        }

        public override void OnInspectorGUI()
        {
            var maker = target as GroundTileMaker;
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("New"))
            {
                CreateTiles();
            }
            if (GUILayout.Button("Load"))
            {
                LoadTiles();
            }
            if (maker.inited)
            {
                if (GUILayout.Button("Save"))
                {
                    maker.Save();
                }
            }
            EditorGUILayout.EndHorizontal();

            if (maker.inited)
            {
                if (mLODNames == null || mLODNames.Length != maker.lodCount)
                {
                    CreateLODNames(maker.lodCount);
                }

                EditorGUILayout.BeginHorizontal();
                int newLOD = EditorGUILayout.Popup("LOD", maker.currentLOD, mLODNames);
                if (newLOD != maker.currentLOD)
                {
                    maker.SetCurrentLOD(newLOD);
                }
                if (GUILayout.Button("Add LOD"))
                {
                    AddLOD();
                }

                if (maker.currentLOD > 0 && maker.currentLOD == maker.lodCount - 1)
                {
                    if (GUILayout.Button("Remove LOD"))
                    {
                        if (EditorUtility.DisplayDialog("Warning", "This action can't be undone! continue?", "Yes", "No"))
                        {
                            RemoveLOD();
                        }
                    }
                }
                EditorGUILayout.EndHorizontal();

                var setting = maker.GetMaskTextureSetting(maker.currentLOD);
                if (mMaskTextureNames == null || mMaskTextureNames.Length != setting.Count)
                {
                    mMaskTextureNames = new string[setting.Count];
                    for (int i = 0; i < setting.Count; ++i)
                    {
                        mMaskTextureNames[i] = setting[i].shaderPropertyName;
                    }
                }

                var operation = (GroundTileMaker.Operation)EditorGUILayout.EnumPopup("Operation", maker.operation);
                if (operation != maker.operation)
                {
                    maker.SetOperation(operation);
                }

                GroundTileMaker.PaintMode paintMode;
                if (maker.tileCount == 16)
                {
                    paintMode = (GroundTileMaker.PaintMode)EditorGUILayout.EnumPopup("Paint Tiles", maker.paintMode);
                }
                else
                {
                    paintMode = GroundTileMaker.PaintMode.AllTiles;
                }
                if (paintMode != maker.paintMode)
                {
                    maker.SetPaintMode(paintMode);
                }
                DrawTileGenerationSetting();

                if (paintMode == GroundTileMaker.PaintMode.Paint1_2_4_8_3_5_10_12_15)
                {
                    for (int k = 0; k < mTile124835101215.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile124835101215[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile124835101215[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_1_2_4_8)
                {
                    for (int k = 0; k < mTile1248.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile1248[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile1248[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_1_2_4_8_12_3)
                {
                    for (int k = 0; k < mTile1_2_4_8_12_3.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile1_2_4_8_12_3[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile1_2_4_8_12_3[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_1_2_4_8_5_10)
                {
                    for (int k = 0; k < mTile1_2_4_8_5_10.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile1_2_4_8_5_10[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile1_2_4_8_5_10[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_1_2_6_8)
                {
                    for (int k = 0; k < mTile1_2_6_8.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile1_2_6_8[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile1_2_6_8[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_9_2_4_8)
                {
                    for (int k = 0; k < mTile9_2_4_8.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile9_2_4_8[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile9_2_4_8[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_14_9_7)
                {
                    for (int k = 0; k < mTile14_9_7.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile14_9_7[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile14_9_7[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_13_11_6)
                {
                    for (int k = 0; k < mTile13_11_6.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile13_11_6[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile13_11_6[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_11_9_6)
                {
                    for (int k = 0; k < mTile11_9_6.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile11_9_6[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile11_9_6[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_11_7_14_13)
                {
                    for (int k = 0; k < mTile11_7_14_13.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile11_7_14_13[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile11_7_14_13[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_10_5)
                {
                    for (int k = 0; k < mTile10_5.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile10_5[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile10_5[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_10_1)
                {
                    for (int k = 0; k < mTile10_1.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile10_1[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile10_1[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }
                else if (paintMode == GroundTileMaker.PaintMode.Paint_10_4)
                {
                    for (int k = 0; k < mTile10_4.Length; ++k)
                    {
                        EditorGUI.indentLevel++;
                        var tile = maker.GetTile(mTile10_4[k]);
                        tile.writable = EditorGUILayout.ToggleLeft($"Paint Tile {mTile10_4[k]}", tile.writable);
                        EditorGUI.indentLevel--;
                    }
                }

                bool showGrid = EditorGUILayout.ToggleLeft("Show Grid", maker.showGrid);
                if (showGrid != maker.showGrid)
                {
                    maker.showGrid = showGrid;
                }
                bool showEdge = EditorGUILayout.ToggleLeft("Show Edge", maker.showEdge);
                if (showEdge != maker.showEdge)
                {
                    maker.showEdge = showEdge;
                    SceneView.RepaintAll();
                }
                bool showEdgeText = EditorGUILayout.ToggleLeft("Show Edge Text", maker.showEdgeText);
                if (showEdgeText != maker.showEdgeText)
                {
                    maker.showEdgeText = showEdgeText;
                    SceneView.RepaintAll();
                }
                bool showTileText = EditorGUILayout.ToggleLeft("Show Tile Index", maker.showTileText);
                if (showTileText != maker.showTileText)
                {
                    maker.showTileText = showTileText;
                    SceneView.RepaintAll();
                }
                bool showGuideline = EditorGUILayout.ToggleLeft("Show Paint Area Guideline", maker.showGuideline);
                if (showGuideline != maker.showGuideline)
                {
                    maker.showGuideline = showGuideline;
                    SceneView.RepaintAll();
                }
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Tile Name", maker.tileSetName);
                if (GUILayout.Button("Change Name"))
                {
                    ChangeTileSetName();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.LabelField("Tile Size", maker.tileSize.ToString());
                maker.edgeSize = EditorGUILayout.FloatField("Edge Size", maker.edgeSize);
                EditorGUILayout.IntField("Mask Texture Resolution", maker.GetMaskTextureSetting(maker.currentLOD)[maker.maskTextureIndex].resolution);
                //draw rotation
                EditorGUILayout.BeginHorizontal("GroupBox");
                bool useRandomRotation = EditorGUILayout.ToggleLeft("Random Brush Rotation", maker.useRandomBrushRotation);
                if (useRandomRotation != maker.useRandomBrushRotation)
                {
                    if (useRandomRotation)
                    {
                        maker.fixedBrushRotation = 0;
                    }
                    maker.useRandomBrushRotation = useRandomRotation;
                }
                if (!maker.useRandomBrushRotation)
                {
                    EditorGUIUtility.labelWidth = 150;
                    float angle = EditorGUILayout.FloatField("Brush Rotation Angle", maker.fixedBrushRotation);
                    if (angle != maker.fixedBrushRotation)
                    {
                        maker.fixedBrushRotation = angle;
                        maker.brushManager.CreateRotatedTexture(angle, maker.channel >= 4);
                    }
                    EditorGUIUtility.labelWidth = 0;
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                maker.brushSize = EditorGUILayout.IntField("Brush Size", maker.brushSize);
                maker.brushSizeStep = EditorGUILayout.IntField("Brush Size Step", maker.brushSizeStep);
                maker.strength = Mathf.Clamp01(EditorGUILayout.FloatField("Strength", maker.strength));
                maker.strengthStep = Mathf.Clamp01(EditorGUILayout.FloatField("Strength Step", maker.strengthStep));
                maker.channel = EditorGUILayout.Popup("Paint Channel", maker.channel, mChannelNames);
                maker.maskTextureIndex = EditorGUILayout.Popup("Mask Texture", maker.maskTextureIndex, mMaskTextureNames);
                var material = AssetDatabase.LoadAssetAtPath<Material>(maker.GetMaterialPath(maker.currentLOD));
                var newMaterial = EditorGUILayout.ObjectField("Material", material, typeof(Material), false, null) as Material;
                if (newMaterial != material && newMaterial != null)
                {
                    maker.ChangeMaterial(maker.currentLOD, newMaterial);
                }

                var maskTextureSetting = maker.GetMaskTextureSetting(maker.currentLOD);
                if (maskTextureSetting.Count == 1)
                {
                    maker.packTextures = EditorGUILayout.ToggleLeft("Pack Texture", maker.packTextures);
                    if (maker.packTextures)
                    {
                        maskTextureSetting[0].uvChannel = (GroundTileMaker.AtlasUVChannel)EditorGUILayout.EnumPopup("Mesh UV Channel For Atlas Texture", maskTextureSetting[0].uvChannel);
                        maker.atlasShader = EditorGUILayout.ObjectField("Atlas Shader", maker.atlasShader, typeof(Shader), false, null) as Shader;
                    }
                }

                maker.shareMesh = EditorGUILayout.ToggleLeft("Share Mesh", maker.shareMesh);
                maker.useExtraSubmeshMaterialAsFirstMaterial = EditorGUILayout.ToggleLeft("Use Extra Submesh Material As First Material", maker.useExtraSubmeshMaterialAsFirstMaterial);
                maker.extraSubmeshMaterial = EditorGUILayout.ObjectField("Extra Submesh Material", maker.extraSubmeshMaterial, typeof(Material), false, null) as Material;

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Reset Texture Data"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "This operation can't be undone, are you sure continue?", "Yes", "No"))
                    {
                        maker.ResetTextureData(maker.maskTextureIndex);
                    }
                }
                if (GUILayout.Button("Generate Modified Assets"))
                {
                    maker.CreatePrefabs(false);
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button("Reset Channel Data"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "This operation can't be undone, are you sure continue?", "Yes", "No"))
                    {
                        maker.ResetChannelData(maker.maskTextureIndex, maker.channel);
                    }
                }

                if (GUILayout.Button("Force Save All Textures To Disk"))
                {
                    maker.RefreshMaskTextures(true);
                }
                if (GUILayout.Button("Force Generate All Assets"))
                {
                    maker.CreatePrefabs(true);
                }

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.TextField("Mask Texture Folder", maker.editorAssetOutputFolder);
                if (GUILayout.Button("Open"))
                {
                    EditorUtils.OpenFolder(maker.editorAssetOutputFolder);
                }
                if (GUILayout.Button("Select"))
                {
                    EditorUtils.SelectFolder(maker.editorAssetOutputFolder);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.TextField("Game Assets Folder", maker.runtimeAssetOutputFolder);
                if (GUILayout.Button("Open"))
                {
                    EditorUtils.OpenFolder(maker.runtimeAssetOutputFolder);
                }
                if (GUILayout.Button("Select"))
                {
                    EditorUtils.SelectFolder(maker.runtimeAssetOutputFolder);
                }
                EditorGUILayout.EndHorizontal();

                BrushManagerUI.Draw(maker.brushManager);

                DrawSetsme();
            }
        }

        void DrawSetsme()
        {
            EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Mouse Button' to paint.\n 'Ctrl + Left Mouse Button' to erase.\n 'R' to refresh mask textures before editing in photoshop.\n 'E' to switch paint mode.\n 'Left Arrow' and 'Right Arrow' to change brush strength.\n 'Up Arrow' and 'Down Arrow' to change brush size.\n 'Shift+Z' to undo painting.\n 'Shift+R' to redo painting.\n 'Space' to rotate brush if select random brush rotation.");
        }

        void FixEdgePixels()
        {
            var maker = target as GroundTileMaker;
            int tileX, tileY;
            GroundTileMaker.EdgeDirection direction;
            int tileIndex;
            maker.PickEdge(mMarkerWorldPos, out tileX, out tileY, out direction, out tileIndex);
            if (!(tileIndex == 6 || tileIndex == 9 || tileIndex == 7 || tileIndex == 11 || tileIndex == 13 || tileIndex == 14))
            {
                var edgeID = maker.GetTile(tileIndex).edgeIDs[(int)direction];
                if (edgeID != GroundTileMaker.EdgeID.Empty)
                {
                    maker.CopyEdgePixels(edgeID, direction, tileX, tileY, maker.currentLOD);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Can't fix edge in this tile!", "OK");
            }
        }

        void CreateContextMenu()
        {
            mMenu = null;
            var maker = target as GroundTileMaker;

            bool hitEdge = maker.PickEdge(mMarkerWorldPos, out mMarkerTileX, out mMarkerTileY, out mMarkerDirection, out mMarkerTileIndex);
            if (hitEdge)
            {
                if (mMenu == null)
                {
                    mMenu = new GenericMenu();
                }

                mMenu.AddItem(new GUIContent("Fix Edge Pixels"), false, FixEdgePixels);
            }
            else
            {
                if (mMenu == null)
                {
                    mMenu = new GenericMenu();
                }

                mMarkerTileIndex = maker.PickTile(mMarkerWorldPos, out mMarkerTileX, out mMarkerTileY);
                if (mMarkerTileIndex >= 0)
                {
                    var tile = maker.GetTile(mMarkerTileIndex);
                    var variations = tile.variations;
                    for (int v = 0; v < variations.Count; ++v)
                    {
                        mMenu.AddItem(new GUIContent($"Tile {mMarkerTileIndex} Variation {variations[v].name}"), v == tile.currentVariationIndex, SetVariation, v);
                    }

                    if (tile.writable)
                    {
                        mMenu.AddItem(new GUIContent("Add Variation"), false, AddVariation);
                        mMenu.AddItem(new GUIContent("Remove Variation"), false, RemoveVariation);
                    }
                }
            }
        }

        void SetVariation(object variationIndex)
        {
            var maker = target as GroundTileMaker;
            maker.SetVariation(mMarkerTileIndex, (int)variationIndex);
        }

        void AddVariation()
        {
            var nameDlg = EditorUtils.CreateInputDialog("Input variation name");
            var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Name", "", "variation"),
                    };
            nameDlg.Show(items, OnClickAddVariation);
        }

        bool OnClickAddVariation(List<InputDialog.Item> parameters)
        {
            var maker = target as GroundTileMaker;
            var name = (parameters[0] as InputDialog.StringItem).text;
            //check if name is valid
            if (name == null)
            {
                EditorUtility.DisplayDialog("Erorr", "Name can't be null", "OK");
                return false;
            }

            if (name.IndexOf("_") >= 0)
            {
                EditorUtility.DisplayDialog("Erorr", "_ can't be used in name", "OK");
                return false;
            }

            if (name[0] >= '0' && name[0] <= '9')
            {
                EditorUtility.DisplayDialog("Erorr", "name can't start with number", "OK");
                return false;
            }

            var instance = maker.GetInstance(mMarkerTileX, mMarkerTileY);
            var tile = maker.GetTile(instance.tileIndex);

            var variations = tile.variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].name == name)
                {
                    EditorUtility.DisplayDialog("Erorr", $"Name {name} is already existed!", "OK");
                    return false;
                }
            }

            var textureSetting = maker.GetMaskTextureSetting(maker.currentLOD);
            List<List<Color32[]>> maskTextureDatas = new List<List<Color32[]>>();
            int lodCount = maker.lodCount;
            for (int lod = 0; lod < lodCount; ++lod)
            {
                List<Color32[]> maskTextureData = new List<Color32[]>();
                maskTextureDatas.Add(maskTextureData);
                for (int t = 0; t < textureSetting.Count; ++t)
                {
                    Color32[] pixels = variations[tile.currentVariationIndex].GetLOD(lod).maskTextures[t].texture.GetPixels32();
                    maskTextureData.Add(pixels);
                }
            }

            var action = new ActionAddVariation(maker, mMarkerTileIndex, name, maker.GetNextInstanceID(), maskTextureDatas);
            ActionManager.instance.PushAction(action);

            return true;
        }

        void RemoveVariation()
        {
#if false
            if (EditorUtility.DisplayDialog("Warning", "Remove variation. this action can't be undone! continue?", "Yes", "No"))
            {
                var maker = target as GroundTileMaker;
                var index = maker.GetCurrentVariationIndex(mMarkerTileX, mMarkerTileY);
                maker.RemoveVariation(mMarkerTileIndex, index);
            }
#else
            var maker = target as GroundTileMaker;
            var index = maker.GetCurrentVariationIndex(mMarkerTileX, mMarkerTileY);
            var instanceID = maker.GetVariationInstanceID(mMarkerTileIndex, index);
            var action = new ActionRemoveVariation(maker, mMarkerTileIndex, instanceID);
            ActionManager.instance.PushAction(action);
#endif
        }

        void CreateTiles()
        {
            var dlg = EditorUtils.PopupDialog<GroundTileMakerSettingWindow>("Setting");
            dlg.minSize = new Vector2(500, 500);
            dlg.maxSize = new Vector2(500, 1000);
            dlg.Show(target as GroundTileMaker);
        }

        void LoadTiles()
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }

            var maker = target as GroundTileMaker;
            string folder = maker.editorAssetOutputFolder;
            if (string.IsNullOrEmpty(folder))
            {
                folder = EditorUtility.OpenFolderPanel("Select Folder", "", "");
            }
            if (!string.IsNullOrEmpty(folder))
            {
                string filePath = folder + "/" + MapCoreDef.MAP_GROUND_TILE_ASSETS_FILE;
                maker.Load(filePath);
            }
        }

        void CreateLODNames(int lodCount)
        {
            mLODNames = new string[lodCount];
            for (int i = 0; i < lodCount; ++i)
            {
                mLODNames[i] = i.ToString();
            }
        }

        void AddLOD()
        {
            var dlg = EditorUtils.PopupDialog<GroundTileMakerAddLODDialog>("Add LOD");
            dlg.minSize = new Vector2(500, 500);
            dlg.maxSize = new Vector2(500, 1000);
            dlg.Show(target as GroundTileMaker);
        }

        void RemoveLOD()
        {
            var maker = target as GroundTileMaker;
            maker.RemoveLOD();
        }

        void DrawTileGenerationSetting()
        {
            var maker = target as GroundTileMaker;
            mShowTileAssetsGenerationSetting = EditorGUILayout.Foldout(mShowTileAssetsGenerationSetting, "Generate Tile Assets");
            if (mShowTileAssetsGenerationSetting)
            {
                for (int i = 0; i < 16; ++i)
                {
                    EditorGUI.indentLevel++;
                    var tile = maker.GetTile(i);
                    tile.generateAssets = EditorGUILayout.ToggleLeft($"Generate Assets For Tile {i}", tile.generateAssets);
                    EditorGUI.indentLevel--;
                }
            }
        }

        void ChangeTileSetName()
        {
            var maker = target as GroundTileMaker;
            var dlg = EditorUtils.CreateInputDialog("Change Tile Name");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Name", "", maker.tileSetName),
                };
            dlg.Show(items, OnClickChangeTileSetName);
        }

        bool OnClickChangeTileSetName(List<InputDialog.Item> param)
        {
            string text = (param[0] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            var maker = target as GroundTileMaker;
            maker.tileSetName = text;

            return true;
        }

        int[] mTile124835101215 = new int[] { 1, 2, 4, 8, 3, 5, 10, 12, 15 };
        int[] mTile1248 = new int[] { 1, 2, 4, 8 };
        int[] mTile1_2_4_8_12_3 = new int[] { 1, 2, 4, 8, 12, 3 };
        int[] mTile1_2_4_8_5_10 = new int[] { 1, 2, 4, 8, 5, 10 };
        int[] mTile1_2_6_8 = new int[] { 1, 2, 6, 8 };
        int[] mTile9_2_4_8 = new int[] { 2, 4, 8, 9 };
        int[] mTile14_9_7 = new int[] { 14, 9, 7 };
        int[] mTile13_11_6 = new int[] { 13, 11, 6 };
        int[] mTile11_9_6 = new int[] { 11, 9, 6 };
        int[] mTile11_7_14_13 = new int[] { 11, 7, 14, 13 };
        int[] mTile10_5 = new int[] { 10, 5 };
        int[] mTile10_1 = new int[] { 10, 1 };
        int[] mTile10_4 = new int[] { 10, 4 };

        string[] mChannelNames;
        string[] mMaskTextureNames;
        string[] mLODNames;
        GenericMenu mMenu;
        Vector3 mMarkerWorldPos;
        int mMarkerTileX;
        int mMarkerTileY;
        GroundTileMaker.EdgeDirection mMarkerDirection;
        int mMarkerTileIndex;
        int mMarkerEdgeID;
        bool mShowTileAssetsGenerationSetting = false;
        BrushIndicator mIndicator = new BrushIndicator();
    }
}

#endif