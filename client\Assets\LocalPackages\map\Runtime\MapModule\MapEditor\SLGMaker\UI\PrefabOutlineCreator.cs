﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System.Collections.Generic;

namespace TFW.Map
{
    //创建180米prefab里的选中物体的prefab outline
    public static class PrefabOutlineCreator
    {
        public static void Create()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            if (stage == null)
            {
                return;
            }
            var assetPath = stage.prefabAssetPath;
            if (string.IsNullOrEmpty(assetPath))
            {
                EditorUtility.DisplayDialog("Error", "Error!", "OK");
                return;
            }

            var selectedObjects = Selection.transforms;
            if (selectedObjects.Length == 0)
            {
                return;
            }

            CreatePrefab(stage.prefabContentsRoot, assetPath, selectedObjects);
        }

        static void CreatePrefab(GameObject parent, string newPrefabPath, Transform[] selectedObjects)
        {
            EditorUtility.DisplayProgressBar("Creating Prefab Outline", "Calculating Outline...", 0.5f);
            var obj = new GameObject("ObstacleOutline");
            obj.tag = MapCoreDef.IGNORED_OBJECT_TAG;
            obj.transform.parent = parent.transform;
            var prefabOutline = obj.AddComponent<PrefabOutline>();
            List<GameObject> gameObjects = new List<GameObject>(selectedObjects.Length);
            for (int i = 0; i < selectedObjects.Length; ++i)
            {
                gameObjects.Add(selectedObjects[i].gameObject);
            }
            prefabOutline.CreateFrom(gameObjects);
            Selection.activeTransform = obj.transform;
            EditorUtility.ClearProgressBar();
        }
    }
}

#endif