﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    class CameraColliderMeshModel
    {
        public CameraColliderMeshModel(Vector3[] vertices, int[] indices)
        {
            mMesh = new Mesh();
            mMesh.vertices = vertices;
            mMesh.triangles = indices;
            mMesh.RecalculateNormals();
            mGameObject = new GameObject("camera collider mesh");
            var renderer = mGameObject.AddComponent<MeshRenderer>();
            var filter = mGameObject.AddComponent<MeshFilter>();
            mMaterial = new Material(Shader.Find("SLGMaker/SurfaceShader"));
            mMaterial.color = new Color(0.5f, 0.5f, 0.5f, 0.6f);
            mMaterial.renderQueue = 3300;
            renderer.sharedMaterial = mMaterial;
            filter.sharedMesh = mMesh;
        }

        public void OnDestroy()
        {
            Object.DestroyImmediate(mGameObject);
            mGameObject = null;
            Object.DestroyImmediate(mMesh);
            mMesh = null;
            Object.DestroyImmediate(mMaterial);
            mMaterial = null;
        }

        public void Update(Vector3[] vertices)
        {
            mMesh.vertices = vertices;
            mMesh.RecalculateNormals();
            mMesh.RecalculateBounds();
        }

        public void SetParent(Transform parent)
        {
            mGameObject.transform.SetParent(parent);
        }

        GameObject mGameObject;
        Mesh mMesh;
        Material mMaterial;
    }

    public class CameraColliderModel : PolygonObjectModel
    {
        public CameraColliderModel(CameraColliderData data) : base(data, MapCoreDef.CAMERA_COLLIDER_MODEL_NAME)
        {
            CreateTopOutline(data);
            if (data.HasMeshData())
            {
                mColliderMesh = new CameraColliderMeshModel(data.vertices, data.indices);
                mColliderMesh.SetParent(mGameObject.transform);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            DestroyColliderMesh();
            DestroyTopOutline();
        }

        void DestroyColliderMesh()
        {
            mColliderMesh?.OnDestroy();
            mColliderMesh = null;
        }

        void DestroyTopOutline()
        {
            mTopOutline?.OnDestroy();
            mTopOutline = null;
        }

        public void CreateTopOutline(CameraColliderData data)
        {
            DestroyTopOutline();
            DestroyColliderMesh();
            if (data.topOutline != null)
            {
                mTopOutline = new CollisionGameObjects(PrefabOutlineType.NavMeshObstacle, data.topOutline.outline, data.displayRadius, data.color);
                mTopOutline.SetParent(mGameObject.transform);
                mTopOutline.Show(true);
            }
        }

        public void UpdateVertex(int index, CameraColliderData data)
        {
            int n = data.GetBottomOutlineVertexCount();
            if (index >= 0 && index < n)
            {
                base.UpdateVertex(PrefabOutlineType.NavMeshObstacle, index, data);
            }
            else
            {
                mTopOutline?.UpdateVertex(index - n, data.topOutline.outline[index - n]);
            }
        }

        public override void Show(PrefabOutlineType type)
        {
            base.Show(type);
            mTopOutline?.Show(true);
        }

        public override void Hide()
        {
            base.Hide();
            mTopOutline?.Show(false);
        }

        public override void SetVertexRadius(float radius)
        {
            base.SetVertexRadius(radius);
            mTopOutline?.SetVertexRadius(radius);
        }

        public override void Update(PolygonObjectData data)
        {
            var colliderData = data as CameraColliderData;
            base.Update(data);
            mTopOutline?.Update(PrefabOutlineType.NavMeshObstacle, colliderData.topOutline.outline, colliderData.displayRadius);
            mColliderMesh?.Update(colliderData.vertices);
        }

        public void CreateCollider(CameraColliderData data)
        {
            DestroyColliderMesh();
            mColliderMesh = new CameraColliderMeshModel(data.vertices, data.indices);
            mColliderMesh.SetParent(mGameObject.transform);
        }

        CameraColliderMeshModel mColliderMesh;
        CollisionGameObjects mTopOutline;
    }
}
#endif

