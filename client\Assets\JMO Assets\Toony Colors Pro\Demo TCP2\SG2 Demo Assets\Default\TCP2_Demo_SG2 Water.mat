%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo_SG2 Water
  m_Shader: {fileID: 4800000, guid: bdc5b3d9efb08bd4eb0d5746ae2be22d, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FoamTex:
        m_Texture: {fileID: 2800000, guid: 532c9e2e0905df744860e5e5c36ada26, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 5930a2cf4b06d4747878f6bdaad8d95f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0
    - _DepthAlphaDistance: 0.07
    - _DepthAlphaMin: 0
    - _DepthColorDistance: 1.5
    - _DepthDistance: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _FoamSmooth: 0.02
    - _FoamSmoothness: 0.032
    - _FoamSpread: 2
    - _FoamStrength: 0.8
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _RampSmooth: 0.1
    - _RampSmoothing: 0.283
    - _RampThreshold: 0.5
    - _RimMax: 1
    - _RimMin: 0.5
    - _SineCount2: 2
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _UVWaveAmplitude: 0.05
    - _UVWaveFrequency: 1
    - _UVWaveSpeed: 1
    - _WaterTime: 0
    - _WaveFrequency: 1
    - _WaveHeight: 0.1
    - _WaveSpeed: 2
    - _WavesFrequency: 4
    - _WavesHeight: 0.2
    - _WavesSpeed: 2
    - _ZWrite: 1
    - __dummy__: 0
    m_Colors:
    - _Color: {r: 0.1462264, g: 0.47909743, b: 1, a: 1}
    - _CustomTime: {r: 0.05, g: 1, b: 2, a: 3}
    - _DepthColor: {r: 0, g: 0.36385673, b: 0.77699995, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FoamColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
    - _FoamSpeed: {r: 2, g: 2, b: 2, a: 2}
    - _FoamTex_SC: {r: 0.05, g: 0.05, b: 0, a: 0}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainTex_SinAnimParams: {r: 2, g: 0.03, b: 1, a: 0}
    - _RimColor: {r: 0.09202562, g: 0.3612496, b: 0.41509432, a: 0.6}
    - _SColor: {r: 0, g: 0, b: 0, a: 1}
    - _WaterColor: {r: 0.26599997, g: 0.7630727, b: 1, a: 1}
    - _WavesPhaseOffsets1: {r: 0.67, g: 1.35, b: 1.1, a: 0.9}
    - _WavesSinOffsets1: {r: 0.88, g: 1.7, b: 2.1, a: 1.7}
