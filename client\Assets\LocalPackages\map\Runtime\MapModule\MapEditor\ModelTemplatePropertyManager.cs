﻿ 



 
 



#if UNITY_EDITOR

using System.Collections.Generic;

namespace TFW.Map {
    [Black]
    public class ModelTemplatePropertyManager {
        public PropertySet GetPropertySet(string assetGUID) {
            PropertySet ps = null;
            int propertySetID = 0;
            mModelProperties.TryGetValue(assetGUID, out propertySetID);
            if (propertySetID != 0) {
                ps = Map.currentMap.FindObject(propertySetID) as PropertySet;
            }
            return ps;
        }

        public void SetPropertySet(string assetGUID, int propertySetID) {
            mModelProperties[assetGUID] = propertySetID;
        }

        public Dictionary<string, int> properties { get { return mModelProperties; } }

        public Dictionary<string, int> mModelProperties = new Dictionary<string, int>();
    }
}


#endif