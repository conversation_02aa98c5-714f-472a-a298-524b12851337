%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Height-based Blending
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17102\n1280.5;1;1278;1369;-764.8537;728.8295;1.036422;True;False\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;7;413,-443;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1;164.1368,-535.239;Inherit;False;Alpha;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;162.6766,-437.1684;Inherit;False;Height;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;5;236.3459,-320.3895;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;6;47.11577,-341;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;22.54278,-256.0706;Inherit;False;Blend
    Factor;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;8;886.6812,-337.8587;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;9;1092.221,-338.2482;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;10;663.4116,-374.5408;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;27.04797,-99.6286;Inherit;False;Blend
    Falloff;1;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;11;422.4116,-240.5408;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;14;1512.113,-459.3596;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;12;1247.828,-566.1115;Inherit;False;Bottom;5;1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;13;1246.79,-467.6507;Inherit;False;Top;5;0;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;15;1711.107,-460.3962;Inherit;False;False;Result;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1519.228,-232.7731;Inherit;False;True;Alpha;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;7;0;1;0\nWireConnection;7;1;4;0\nWireConnection;7;2;5;0\nWireConnection;5;0;6;0\nWireConnection;5;1;2;0\nWireConnection;8;0;10;0\nWireConnection;8;1;11;0\nWireConnection;9;0;8;0\nWireConnection;10;0;7;0\nWireConnection;11;0;3;0\nWireConnection;14;0;12;0\nWireConnection;14;1;13;0\nWireConnection;14;2;9;0\nWireConnection;15;0;14;0\nWireConnection;0;0;9;0\nASEEND*/\n//CHKSM=EAEFB89C4A5CD1A06A1578CB7E39D908044F001C"
  m_functionName: 
  m_description: Implements height-based blending to allow blending a bottom layer
    to a top layer using a height map and a few control parameters, including alpha,
    which can be from vertex color channel.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
