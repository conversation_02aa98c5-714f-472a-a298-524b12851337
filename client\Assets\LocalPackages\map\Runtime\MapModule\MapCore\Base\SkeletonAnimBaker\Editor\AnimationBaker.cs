﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract partial class AnimationBaker
    {
        public class BakeOption
        {
            public GameObject prefab;
            public string outputFolder;
            public AnimationBlendType blendType;
            public bool useAnimationBlending;
            public bool deleteBipBones;
            public bool generatePrefab;
            public bool clearOutputFolder;
            public bool useSRPBatcher;
        }

        public abstract GameObject Bake(BakeOption option);
        public abstract string Check(GameObject prefab);

        public abstract BakeMaterialManager materialManager { get; }
        public abstract List<Renderer> renderers { get; }

        public static string ErrorMsg;
    }
}
#endif