﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public enum RuinType
    {
        Normal,
        NeedScout,
        Defender,
    }

    [ExecuteInEditMode]
    public class RuinLayerLogic : MapLayerLogic
    {
        void OnEnable()
        {
            operationType = ModelOperationType.kSelectObject;
        }

        public QuadTreeObjectLayerData layerData
        {
            get
            {
                var layerData = Map.currentMap.FindObject(layerID) as QuadTreeObjectLayerData;
                return layerData;
            }
        }

        protected override void OnInit()
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as RuinLayer;
            layer.selectedObjectTypeIndex = layer.ruinObjectTypes.Count - 1;
        }

        protected override MoveAxis moveAxis { get { return MoveAxis.Y; } }

        public ModelOperationType operationType { set; get; }
        public string selectedPrefabGUID { set; get; }
        public int startID = 100000;

        public float lastRegionWidth { set; get; }
        public float lastRegionHeight { set; get; }
        public MapRegionManager lastRegionManager { set; get; }
    }
}

#endif