﻿
using UnityEngine;
using UnityEditor;
using System;
using RiverLog;
using UnityEditor.VersionControl;
using System.Runtime.Remoting.Messaging;
using System.IO;

public class MaxSDKSetting : EditorWindow
{
    private static string unityAndroidSdkPath = "";

    private const string PPK_SDK_PATH = "UnityAndroidSdkPath";

    private const string PPK_AGP_VERSION = "AgpVersion";

    private const string PPK_GRADLE_VERSION = "GradleVersion";

    //AGP版本
    private const string AGPVersion = "7.4.2";

    // gradle 版本
    private const string GradleVersion = "gradle-7.5-all";

    private string agpVersion = "";

    private string gradleVersion = "";

    [MenuItem("Edit/MaxSdkSetting")]
    public static void ShowWindow()
    {
        GetWindow<MaxSDKSetting>("MaxSdkSetting");
    }


    //窗口启用时初始化一次
    private void OnEnable()
    {
        unityAndroidSdkPath = GetunityAndroidSdkPath();
        agpVersion = GetAGPVersion();
        gradleVersion = GetGradleVersion();
    }

    //每次绘制界面时都会触发
    private void OnGUI()
    {
        GUIStyle centerStyle = new GUIStyle(GUI.skin.label);
        centerStyle.alignment = TextAnchor.MiddleCenter;
        GUILayout.Label("==========    Android Setting  ==========", centerStyle);
        GUILayout.Label("Android sdk path:", EditorStyles.boldLabel);

        GUILayout.BeginHorizontal();
        unityAndroidSdkPath = EditorGUILayout.TextField(unityAndroidSdkPath);

        if (GUILayout.Button("Browse", GUILayout.Width(80)))
        {
            string path = EditorUtility.OpenFilePanel("Select sdk Path", "", "zip");
            if (!string.IsNullOrEmpty(path))
            {
                
                unityAndroidSdkPath = path;

            }
        }
        GUILayout.EndHorizontal();
        GUILayout.Space(10);

        GUILayout.BeginHorizontal();
        GUILayout.Label("Android Gradle Plugin:");
        GUILayout.EndHorizontal();


        GUILayout.BeginHorizontal();
        agpVersion = EditorGUILayout.TextField(agpVersion, GUILayout.Width(50));
        GUILayout.EndHorizontal();
        GUILayout.Space(10);


        GUILayout.BeginHorizontal();
        GUILayout.Label("Gradle Version:");
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        gradleVersion = EditorGUILayout.TextField(gradleVersion, GUILayout.Width(200));
        GUILayout.EndHorizontal();
        GUILayout.Space(10);

        if (GUILayout.Button("Confirm"))
        {
            saveLocalInfo(unityAndroidSdkPath, agpVersion, gradleVersion);
            setEmptySign();
            
        }

    }

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="unityAndroidSdkPath">选择的sdk</param>
    /// <param name="agpVersion">android gradle version版本</param>
    /// <param name="gradleVersion">gradle版本</param>
    private void saveLocalInfo(string unityAndroidSdkPath, string agpVersion, string gradleVersion)
    {
        PlayerPrefs.SetString(PPK_SDK_PATH, unityAndroidSdkPath);
        PlayerPrefs.SetString(PPK_AGP_VERSION, agpVersion);
        PlayerPrefs.SetString(PPK_GRADLE_VERSION, gradleVersion);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// 设置空的签名
    /// </summary>
    private void setEmptySign()
    {
        PlayerSettings.Android.keystoreName = "";
        PlayerSettings.Android.keyaliasName = "";
        PlayerSettings.Android.keystorePass = "";
        PlayerSettings.Android.keyaliasPass = "";
    }

    /// <summary>
    /// 返回sdk路径
    /// </summary>
    /// <returns></returns>
    public static string GetunityAndroidSdkPath()
    {
        if (string.IsNullOrEmpty(unityAndroidSdkPath))
        {
            unityAndroidSdkPath = PlayerPrefs.GetString(PPK_SDK_PATH);
        }
        return unityAndroidSdkPath;
    }

    /// <summary>
    /// 返回AGP版本
    /// </summary>
    /// <returns></returns>
    public static string GetAGPVersion()
    {

        string agpVerson = PlayerPrefs.GetString(PPK_AGP_VERSION);
        if (string.IsNullOrEmpty(agpVerson))
        {
            agpVerson = AGPVersion;
        }

        return agpVerson;
    }

    /// <summary>
    /// 获取gradle版本
    /// </summary>
    /// <returns></returns>
    public static string GetGradleVersion()
    {
        string gradleVersion = PlayerPrefs.GetString(PPK_GRADLE_VERSION);
        if (string.IsNullOrEmpty(gradleVersion))
        {
            gradleVersion = GradleVersion;
        }
        return gradleVersion;
    }
}

