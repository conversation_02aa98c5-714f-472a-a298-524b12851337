{"skeleton": {"hash": "r0h0m/eRPeU", "spine": "4.2.33", "x": -354.2, "y": -205.4, "width": 694, "height": 826, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "boat_A", "parent": "root", "length": 100, "x": 34.31, "y": -22.72}, {"name": "boat", "parent": "boat_A", "length": 556.31, "rotation": 90, "x": 4.85, "y": -29.12}, {"name": "hat", "parent": "boat", "length": 49.53, "rotation": -15.29, "x": 587.96, "y": -109.89}, {"name": "hat2", "parent": "hat", "x": -13.01, "y": 52.12}, {"name": "hat3", "parent": "hat", "x": -33.6, "y": 2.51}, {"name": "hat4", "parent": "hat", "x": -20.53, "y": -55.45}, {"name": "boat2", "parent": "boat", "x": 630.21, "y": 3.1}, {"name": "boat3", "parent": "boat2", "x": -1.2, "y": -12.44}, {"name": "boat4", "parent": "boat3", "x": -4.28, "y": -12.3}, {"name": "boat5", "parent": "boat4", "x": -8.16, "y": -8.42}, {"name": "boat6", "parent": "boat5", "x": -9.36, "y": -7.09}, {"name": "boat7", "parent": "boat6", "x": -10.3, "y": -7.35}, {"name": "boat17", "parent": "boat", "x": 488.77, "y": 27.13}, {"name": "boat16", "parent": "boat17", "length": 169.43, "rotation": -107.19, "x": 3.8, "y": 73.52}, {"name": "boat8", "parent": "boat16", "length": 145.86, "rotation": -1.74, "x": 9.33, "y": -57.1}, {"name": "boat9", "parent": "boat16", "length": 155.89, "rotation": -2.83, "x": -1.16, "y": -107.33}, {"name": "boat10", "parent": "boat16", "length": 166.65, "rotation": -5.51, "x": -4.79, "y": -155.11}, {"name": "boat11", "parent": "boat16", "rotation": 107.19, "x": -44.7, "y": -53.17}, {"name": "boat12", "parent": "boat16", "rotation": 107.19, "x": -71.55, "y": -110.03}, {"name": "boat13", "parent": "boat16", "rotation": 107.19, "x": -80.39, "y": -163.56}, {"name": "boat14", "parent": "boat", "length": 86.8, "rotation": -98.64, "x": 310.69, "y": 267.46}, {"name": "boat15", "parent": "boat", "length": 173.88, "rotation": -117.41, "x": 284.71, "y": 149.8}, {"name": "gun", "parent": "boat", "length": 166.6, "rotation": 54.99, "x": 197.09, "y": 242.93, "color": "abe323ff"}, {"name": "boat19", "parent": "boat", "rotation": -9.89, "x": 198.67, "y": 96.57}, {"name": "boat18", "parent": "boat19", "length": 61.03, "x": 10.49, "y": -5.12}, {"name": "boat20", "parent": "boat19", "length": 36.89, "rotation": 154.39, "x": -15.31, "y": 43.59}, {"name": "boat21", "parent": "boat20", "length": 29.31, "rotation": -29.04, "x": 36.89}, {"name": "boat22", "parent": "boat21", "length": 25.71, "rotation": -68.15, "x": 29.31}, {"name": "boat23", "parent": "boat22", "length": 32.18, "rotation": -37.55, "x": 25.71}, {"name": "boat24", "parent": "boat23", "length": 16.91, "rotation": 55.26, "x": 32.18}, {"name": "boat25", "parent": "boat24", "length": 18.58, "rotation": 67.68, "x": 16.91}, {"name": "boat26", "parent": "boat19", "length": 36.96, "rotation": -169.79, "x": -15.61, "y": 19.4}, {"name": "boat27", "parent": "boat26", "length": 25.15, "rotation": -41.6, "x": 36.96}, {"name": "boat28", "parent": "boat27", "length": 25.18, "rotation": -17.02, "x": 25.15}, {"name": "boat29", "parent": "boat28", "length": 21.15, "rotation": 51.46, "x": 25.18}, {"name": "boat30", "parent": "boat19", "length": 37.44, "rotation": -159.12, "x": -11.77, "y": -5.08}, {"name": "boat31", "parent": "boat30", "length": 29.4, "rotation": 4.33, "x": 37.44}, {"name": "boat32", "parent": "boat31", "length": 22.56, "rotation": 23.25, "x": 29.4}, {"name": "boat33", "parent": "boat32", "length": 21.34, "rotation": 41.22, "x": 22.56}, {"name": "boat34", "parent": "boat33", "length": 12.6, "rotation": 76.63, "x": 21.51, "y": 0.24}, {"name": "boat35", "parent": "boat34", "length": 16.19, "rotation": 71.41, "x": 12.6}, {"name": "boat36", "parent": "boat35", "length": 18.91, "rotation": 14.49, "x": 16.19}, {"name": "boat37", "parent": "boat19", "length": 34.52, "rotation": -73.7, "x": -0.16, "y": -30.84}, {"name": "boat38", "parent": "boat37", "length": 30.6, "rotation": -53.83, "x": 34.52}, {"name": "boat39", "parent": "boat38", "length": 32.62, "rotation": -3.64, "x": 30.6}, {"name": "boat40", "parent": "boat39", "length": 21.19, "rotation": -43.88, "x": 32.62}, {"name": "boat41", "parent": "boat40", "length": 20.67, "rotation": -40.85, "x": 21.19}, {"name": "boat42", "parent": "boat41", "length": 12.18, "rotation": -45.16, "x": 20.67}, {"name": "boat43", "parent": "boat42", "length": 12.46, "rotation": -51.99, "x": 12.18}, {"name": "boat44", "parent": "boat19", "length": 35.75, "rotation": 118.43, "x": 4.1, "y": 34.61}, {"name": "boat45", "parent": "boat44", "length": 36.82, "rotation": 14.54, "x": 35.75}, {"name": "boat46", "parent": "boat45", "length": 30.6, "rotation": -8.38, "x": 36.82}, {"name": "boat47", "parent": "boat46", "length": 21.58, "rotation": 6.45, "x": 30.6}, {"name": "boat48", "parent": "boat47", "length": 14.64, "rotation": -75.03, "x": 21.58}, {"name": "boat49", "parent": "boat48", "length": 18.28, "rotation": -43.58, "x": 14.64}, {"name": "boat50", "parent": "boat19", "length": 24.76, "rotation": 77.74, "x": 9.87, "y": 31.91}, {"name": "boat51", "parent": "boat50", "length": 21.37, "rotation": -37.63, "x": 24.76}, {"name": "boat52", "parent": "boat51", "length": 18.98, "rotation": 11.31, "x": 21.37}, {"name": "boat53", "parent": "boat52", "length": 14.61, "rotation": 49.26, "x": 18.98}, {"name": "boat54", "parent": "boat19", "length": 27.78, "rotation": -145.94, "x": -17.68, "y": -32.26}, {"name": "boat55", "parent": "boat54", "length": 29.19, "rotation": 38.56, "x": 27.78}, {"name": "boat56", "parent": "boat55", "length": 21.33, "rotation": 46.38, "x": 29.19}, {"name": "boat57", "parent": "boat56", "length": 17.09, "rotation": 56.69, "x": 21.33}, {"name": "boat58", "parent": "boat57", "length": 16.4, "rotation": 24, "x": 17.09}, {"name": "bone", "parent": "root", "x": -277.08, "y": 100.85, "color": "0000ffff"}, {"name": "bone2", "parent": "root", "x": -309.35, "y": 44.49, "color": "0000ffff"}, {"name": "bone3", "parent": "root", "x": -264.55, "y": 45.46, "color": "0000ffff"}, {"name": "bone4", "parent": "root", "x": -301.16, "y": -39.32, "color": "0000ffff"}, {"name": "bone5", "parent": "root", "x": -246.25, "y": -28.73, "color": "0000ffff"}, {"name": "bone6", "parent": "root", "x": -215.9, "y": -103.87, "color": "0000ffff"}, {"name": "bone7", "parent": "root", "x": -112.82, "y": -138.55, "color": "0000ffff"}, {"name": "bone8", "parent": "root", "x": 7.6, "y": -135.18, "color": "0000ffff"}, {"name": "bone9", "parent": "root", "x": 109.24, "y": -109.65, "color": "0000ffff"}, {"name": "bone10", "parent": "root", "x": 194.02, "y": -67.74, "color": "0000ffff"}, {"name": "bone11", "parent": "root", "x": 255.21, "y": -22.64, "color": "0000ffff"}, {"name": "bone12", "parent": "root", "x": 250.12, "y": 39.7, "color": "0000ffff"}, {"name": "bone13", "parent": "root", "x": 297.58, "y": 37.27, "color": "0000ffff"}, {"name": "bone14", "parent": "root", "x": 266.75, "y": 85.44, "color": "0000ffff"}, {"name": "bone15", "parent": "root", "x": -312.46, "y": -12.81, "color": "24ff00ff"}, {"name": "bone16", "parent": "root", "x": -252.13, "y": -36.06, "color": "24ff00ff"}, {"name": "bone17", "parent": "root", "x": -197.32, "y": -58.72, "color": "24ff00ff"}, {"name": "bone18", "parent": "root", "x": -136.15, "y": -67.37, "color": "24ff00ff"}, {"name": "bone19", "parent": "root", "x": -80.04, "y": -94.32, "color": "24ff00ff"}, {"name": "bone20", "parent": "root", "x": -12.82, "y": -71.5, "color": "24ff00ff"}, {"name": "bone21", "parent": "root", "x": 49.11, "y": -69.66, "color": "24ff00ff"}, {"name": "bone22", "parent": "root", "x": 106.41, "y": -53.62, "color": "24ff00ff"}, {"name": "bone23", "parent": "root", "x": 162.17, "y": -34.34, "color": "24ff00ff"}, {"name": "bone24", "parent": "root", "x": 209.86, "y": -2.06, "color": "24ff00ff"}, {"name": "bone25", "parent": "root", "x": 246.38, "y": 38.07, "color": "24ff00ff"}, {"name": "bone26", "parent": "root", "x": 289.76, "y": 63.61, "color": "24ff00ff"}, {"name": "bone27", "parent": "root", "x": -285.53, "y": -68.14, "color": "24ff00ff"}, {"name": "bone28", "parent": "root", "x": -216.92, "y": -114.73, "color": "24ff00ff"}, {"name": "bone29", "parent": "root", "x": -154.24, "y": -143.1, "color": "24ff00ff"}, {"name": "bone30", "parent": "root", "x": -66.58, "y": -154.96, "color": "24ff00ff"}, {"name": "bone31", "parent": "root", "x": 17.7, "y": -149.88, "color": "24ff00ff"}, {"name": "bone32", "parent": "root", "x": 98.59, "y": -135.48, "color": "24ff00ff"}, {"name": "bone33", "parent": "root", "x": 170.59, "y": -115.57, "color": "24ff00ff"}, {"name": "bone34", "parent": "root", "x": 227.76, "y": -81.69, "color": "24ff00ff"}, {"name": "bone35", "parent": "root", "x": 278.16, "y": -38.5, "color": "24ff00ff"}, {"name": "bone36", "parent": "root", "x": 298.91, "y": 13.6, "color": "24ff00ff"}], "slots": [{"name": "Castle", "bone": "root", "attachment": "Castle"}, {"name": "boat", "bone": "boat", "attachment": "boat"}, {"name": "gun", "bone": "gun", "attachment": "gun"}, {"name": "octopus_C", "bone": "root", "attachment": "octopus_C"}, {"name": "octopus_B", "bone": "root", "attachment": "octopus_B"}, {"name": "octopus_A", "bone": "root", "attachment": "octopus_A"}, {"name": "octopus", "bone": "root", "attachment": "octopus"}, {"name": "hat", "bone": "hat", "attachment": "hat"}, {"name": "flag", "bone": "root", "attachment": "flag"}, {"name": "water_up", "bone": "root", "attachment": "water_up"}], "physics": [{"name": "gun", "bone": "gun", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"boat": {"boat": {"type": "mesh", "uvs": [0.75085, 1e-05, 0.77174, 0.0634, 0.76983, 0.12866, 0.77813, 0.13822, 0.97361, 0.18377, 0.97724, 0.19297, 0.969, 0.20453, 0.95298, 0.2012, 0.93564, 0.22769, 0.92774, 0.26217, 0.92255, 0.29643, 0.95319, 0.33185, 0.95728, 0.38914, 0.97595, 0.40068, 0.99668, 0.42892, 0.99999, 0.5092, 0.99683, 0.57873, 0.97033, 0.63163, 0.95294, 0.69389, 0.93628, 0.71203, 0.89164, 0.80122, 0.82028, 0.88476, 0.75859, 0.92202, 0.63668, 0.97314, 0.49464, 1, 0.35559, 1, 0.26398, 0.98082, 0.15783, 0.93686, 0.08799, 0.89168, 0.04373, 0.83434, 0.01475, 0.77617, 0.00042, 0.76584, 0.00021, 0.7265, 0.01465, 0.71322, 0.00205, 0.64008, 0.00977, 0.58597, 0.02738, 0.5737, 0.16354, 0.57514, 0.16597, 0.48834, 0.12398, 0.46035, 0.13464, 0.38042, 0.16119, 0.37988, 0.17233, 0.45039, 0.17729, 0.27432, 0.19319, 0.27916, 0.20836, 0.45918, 0.25572, 0.47838, 0.25473, 0.49721, 0.24166, 0.50153, 0.20636, 0.48831, 0.20763, 0.54112, 0.393, 0.5134, 0.39264, 0.47296, 0.47134, 0.45492, 0.4751, 0.33131, 0.58447, 0.29697, 0.58434, 0.26493, 0.58727, 0.23344, 0.59508, 0.19831, 0.60691, 0.15725, 0.6181, 0.13812, 0.63757, 0.12704, 0.63836, 0.11585, 0.65525, 0.10761, 0.34252, 0.76414, 0.37942, 0.76711, 0.48485, 0.75537, 0.5201, 0.74703, 0.44224, 0.76043, 0.57047, 0.72823, 0.60282, 0.70949, 0.61435, 0.6956, 0.64076, 0.64585, 0.66382, 0.5932], "triangles": [63, 0, 1, 2, 63, 1, 7, 3, 4, 7, 4, 5, 61, 63, 2, 61, 62, 63, 2, 3, 61, 61, 3, 7, 6, 7, 5, 8, 61, 7, 9, 61, 8, 61, 58, 59, 10, 55, 56, 42, 44, 45, 43, 44, 42, 40, 41, 42, 39, 40, 42, 45, 38, 42, 49, 45, 46, 38, 39, 42, 49, 38, 45, 46, 48, 49, 47, 48, 46, 51, 52, 53, 38, 49, 50, 37, 38, 50, 14, 15, 12, 61, 59, 60, 10, 12, 55, 12, 10, 11, 9, 58, 61, 9, 10, 58, 10, 57, 58, 57, 10, 56, 12, 73, 55, 53, 54, 55, 73, 53, 55, 73, 12, 15, 14, 12, 13, 16, 17, 15, 17, 73, 15, 73, 51, 53, 72, 51, 73, 17, 19, 73, 19, 72, 73, 72, 69, 51, 72, 71, 69, 17, 18, 19, 36, 34, 35, 37, 33, 34, 37, 34, 36, 70, 69, 71, 67, 51, 69, 66, 51, 67, 68, 51, 66, 64, 50, 51, 65, 64, 51, 37, 50, 64, 33, 37, 64, 30, 31, 32, 68, 65, 51, 30, 33, 64, 30, 32, 33, 20, 72, 19, 71, 72, 20, 29, 30, 64, 21, 71, 20, 70, 71, 21, 28, 29, 64, 22, 70, 21, 69, 70, 22, 27, 28, 64, 23, 69, 22, 67, 69, 23, 26, 27, 64, 25, 26, 64, 65, 25, 64, 66, 67, 23, 24, 66, 23, 68, 66, 24, 65, 68, 24, 25, 65, 24], "vertices": [572.43, -107.6, 532.88, -118.93, 492.15, -117.89, 486.19, -122.39, 457.76, -228.34, 452.02, -230.31, 444.81, -225.84, 446.88, -217.16, 430.36, -207.75, 408.84, -203.48, 387.46, -200.66, 365.36, -217.27, 329.61, -219.48, 322.41, -229.61, 304.79, -240.84, 254.7, -242.63, 211.31, -240.92, 178.29, -226.56, 139.45, -217.14, 128.13, -208.1, 72.47, -183.91, 20.34, -145.23, -2.91, -111.8, -34.81, -45.72, -51.57, 31.27, -51.57, 106.63, -39.59, 156.28, -12.17, 213.82, 16.03, 251.67, 51.8, 275.66, 88.11, 291.37, 94.55, 299.13, 119.1, 299.25, 127.39, 291.42, 173.03, 298.25, 206.79, 294.07, 214.44, 284.52, 213.55, 210.72, 267.71, 209.4, 285.18, 232.16, 335.05, 226.38, 335.39, 212, 291.39, 205.96, 401.26, 203.27, 398.24, 194.65, 285.91, 186.43, 273.93, 160.76, 262.17, 161.3, 259.48, 168.38, 267.73, 187.51, 234.78, 186.83, 252.07, 86.36, 277.31, 86.55, 288.57, 43.89, 365.7, 41.86, 387.13, -17.42, 407.12, -17.35, 426.77, -18.94, 448.69, -23.18, 474.31, -29.59, 486.25, -35.65, 493.16, -46.2, 500.14, -46.63, 505.29, -55.78, 95.61, 113.71, 93.76, 93.71, 101.08, 36.57, 106.29, 17.46, 97.92, 59.66, 118.02, -9.83, 129.71, -27.37, 138.38, -33.62, 169.42, -47.93, 202.28, -60.43], "hull": 64, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 98, 100, 102, 104, 104, 106, 108, 110, 124, 126, 90, 92, 92, 94, 94, 96, 96, 98, 74, 76, 72, 74, 60, 62, 56, 58, 58, 60, 48, 50, 36, 38, 38, 40, 32, 34, 34, 36, 10, 12, 12, 14, 126, 0, 122, 124, 118, 120, 120, 122, 116, 118, 112, 114, 114, 116, 110, 112, 16, 18, 18, 20, 122, 14, 106, 108, 100, 102, 128, 130, 132, 134, 130, 136, 136, 132, 134, 138, 138, 140, 140, 142, 142, 144, 144, 146, 28, 30, 128, 52, 146, 38], "width": 542, "height": 624}}, "Castle": {"Castle": {"type": "mesh", "uvs": [0.17657, 0, 0.79544, 0.00059, 0.82832, 0.02517, 0.8612, 0.04976, 0.88771, 0.07109, 0.92531, 0.11157, 0.95793, 0.1472, 0.97163, 0.19204, 0.98637, 0.25475, 0.99965, 0.32987, 1, 0.40326, 1, 0.48419, 0.9843, 0.56974, 0.95935, 0.6455, 0.93441, 0.72126, 0.89923, 0.78831, 0.86575, 0.82914, 0.81622, 0.86567, 0.76792, 0.89444, 0.7219, 0.92006, 0.67153, 0.94516, 0.62039, 0.96243, 0.57072, 0.97544, 0.51431, 0.98361, 0.47463, 0.98935, 0.43451, 0.99515, 0.38775, 0.99999, 0.33789, 0.98911, 0.31325, 0.97822, 0.27738, 0.95352, 0.24152, 0.92882, 0.21389, 0.90013, 0.18627, 0.87145, 0.15864, 0.84276, 0.13556, 0.8121, 0.10011, 0.76501, 0.07036, 0.72549, 0.04327, 0.67834, 0.02226, 0.5958, 0.00675, 0.52361, 0, 0.46003, 0, 0.39313, 0.00839, 0.32764, 0.02068, 0.25313, 0.0361, 0.19253, 0.06017, 0.12002, 0.08287, 0.06772, 0.1078, 0.03931, 0.14093, 0.01662, 0.17834, 0.0763, 0.17568, 0.1552, 0.18696, 0.2535, 0.2009, 0.34146, 0.21881, 0.42165, 0.25265, 0.48891, 0.32763, 0.56781, 0.36677, 0.59239, 0.29014, 0.52836, 0.40924, 0.63636, 0.46364, 0.65059, 0.52203, 0.64024, 0.57511, 0.61955, 0.62753, 0.59239, 0.68326, 0.56264, 0.72838, 0.52771, 0.77064, 0.45882, 0.79375, 0.37655, 0.81002, 0.3045, 0.83388, 0.23798, 0.83679, 0.15784, 0.83191, 0.08952, 0.81322, 0.03732, 0.13987, 0.07604, 0.09057, 0.15656, 0.14475, 0.16226, 0.06357, 0.25435, 0.10642, 0.25344, 0.14683, 0.25283, 0.04964, 0.35292, 0.10605, 0.34747, 0.15276, 0.34601, 0.04498, 0.44016, 0.10372, 0.43289, 0.15687, 0.41835, 0.05943, 0.52754, 0.11304, 0.50664, 0.17738, 0.49119, 0.09113, 0.61834, 0.14102, 0.56653, 0.12889, 0.69649, 0.16852, 0.63288, 0.21281, 0.55472, 0.25383, 0.60107, 0.21654, 0.67923, 0.18064, 0.76102, 0.23615, 0.80923, 0.26925, 0.71744, 0.30421, 0.64383, 0.34428, 0.68314, 0.32423, 0.76948, 0.29766, 0.86581, 0.3536, 0.89671, 0.37971, 0.79129, 0.40069, 0.71041, 0.4212, 0.91125, 0.43519, 0.81492, 0.44777, 0.73313, 0.48712, 0.90671, 0.48945, 0.81855, 0.49411, 0.72676, 0.54819, 0.7204, 0.55052, 0.8131, 0.55565, 0.89489, 0.59061, 0.70041, 0.6032, 0.79129, 0.61252, 0.87581, 0.64376, 0.66505, 0.65122, 0.76774, 0.66147, 0.86498, 0.69001, 0.63469, 0.71238, 0.72597, 0.73334, 0.82814, 0.74662, 0.60063, 0.77108, 0.696, 0.79484, 0.77774, 0.79624, 0.55568, 0.83327, 0.62652, 0.85983, 0.70553, 0.82629, 0.48484, 0.87381, 0.54069, 0.91434, 0.61426, 0.84306, 0.40316, 0.90455, 0.44676, 0.94439, 0.49308, 0.87167, 0.33283, 0.94435, 0.36553, 0.88564, 0.24428, 0.93664, 0.24958, 0.90085, 0.16669, 0.86556, 0.10014], "triangles": [38, 84, 87, 124, 127, 16, 136, 69, 138, 113, 62, 116, 116, 62, 119, 110, 61, 113, 103, 58, 106, 103, 98, 58, 97, 55, 98, 91, 54, 92, 139, 4, 138, 69, 139, 138, 137, 6, 7, 135, 137, 8, 137, 7, 8, 10, 11, 133, 12, 133, 11, 13, 130, 12, 127, 130, 14, 16, 127, 15, 18, 124, 17, 19, 118, 121, 20, 118, 19, 21, 115, 118, 22, 115, 21, 23, 112, 22, 25, 104, 24, 27, 101, 26, 29, 100, 28, 29, 30, 100, 30, 95, 100, 31, 95, 30, 33, 94, 32, 34, 89, 94, 35, 89, 34, 36, 87, 89, 36, 37, 87, 37, 38, 87, 38, 39, 84, 41, 42, 78, 78, 43, 75, 43, 44, 75, 75, 44, 73, 73, 44, 45, 73, 72, 74, 72, 73, 46, 70, 2, 3, 71, 1, 2, 68, 69, 136, 67, 66, 1, 66, 65, 64, 122, 119, 63, 122, 63, 64, 64, 63, 1, 62, 61, 1, 110, 60, 61, 109, 59, 60, 61, 60, 56, 60, 59, 56, 98, 56, 58, 56, 1, 61, 98, 55, 56, 92, 57, 97, 57, 1, 56, 57, 54, 51, 53, 52, 54, 54, 52, 51, 57, 49, 1, 49, 0, 1, 48, 0, 49, 72, 48, 49, 83, 52, 53, 91, 53, 54, 92, 54, 57, 97, 57, 55, 55, 57, 56, 56, 59, 58, 106, 58, 59, 113, 61, 62, 63, 62, 1, 62, 63, 119, 64, 65, 125, 65, 66, 128, 1, 66, 64, 66, 67, 131, 68, 67, 69, 69, 67, 71, 70, 69, 71, 69, 70, 139, 67, 1, 71, 71, 2, 70, 72, 47, 48, 72, 46, 47, 45, 46, 73, 42, 43, 78, 40, 41, 81, 81, 41, 78, 39, 40, 81, 35, 36, 89, 33, 34, 94, 31, 32, 95, 32, 94, 95, 27, 28, 101, 28, 100, 101, 26, 104, 25, 26, 101, 104, 24, 107, 23, 24, 104, 107, 22, 112, 115, 21, 118, 20, 19, 121, 18, 18, 121, 124, 17, 124, 16, 15, 127, 14, 14, 130, 13, 130, 133, 12, 133, 135, 10, 135, 9, 10, 135, 8, 9, 137, 138, 6, 6, 138, 5, 138, 4, 5, 70, 3, 139, 139, 3, 4, 77, 74, 50, 80, 77, 51, 57, 51, 49, 77, 50, 51, 80, 51, 52, 50, 49, 51, 50, 72, 49, 74, 72, 50, 83, 80, 52, 106, 59, 109, 109, 60, 110, 125, 65, 128, 128, 66, 131, 67, 68, 134, 23, 107, 112, 39, 81, 84, 136, 138, 137, 131, 134, 132, 137, 134, 136, 131, 67, 134, 134, 68, 136, 132, 134, 135, 134, 137, 135, 126, 129, 130, 126, 128, 129, 130, 129, 133, 133, 129, 132, 128, 131, 129, 129, 131, 132, 133, 132, 135, 121, 123, 124, 124, 126, 127, 124, 123, 126, 120, 122, 123, 127, 126, 130, 123, 125, 126, 123, 122, 125, 125, 128, 126, 122, 64, 125, 118, 115, 117, 115, 114, 117, 121, 117, 120, 121, 118, 117, 121, 120, 123, 114, 113, 117, 113, 116, 117, 120, 116, 119, 120, 117, 116, 120, 119, 122, 112, 108, 111, 108, 112, 107, 104, 105, 107, 107, 105, 108, 115, 111, 114, 115, 112, 111, 105, 106, 108, 108, 109, 111, 108, 106, 109, 109, 110, 111, 114, 110, 113, 114, 111, 110, 101, 102, 104, 104, 102, 105, 100, 99, 101, 101, 99, 102, 100, 96, 99, 102, 103, 105, 105, 103, 106, 99, 98, 102, 102, 98, 103, 96, 97, 99, 99, 97, 98, 95, 96, 100, 94, 93, 95, 95, 93, 96, 89, 90, 94, 94, 90, 93, 93, 92, 96, 89, 88, 90, 90, 91, 93, 93, 91, 92, 96, 92, 97, 91, 88, 86, 91, 90, 88, 88, 85, 86, 85, 82, 83, 85, 83, 86, 91, 86, 53, 86, 83, 53, 89, 87, 88, 87, 85, 88, 87, 84, 85, 84, 82, 85, 84, 81, 82, 79, 75, 76, 83, 79, 80, 79, 83, 82, 80, 79, 77, 79, 76, 77, 76, 74, 77, 81, 78, 82, 82, 78, 79, 78, 75, 79, 75, 73, 76, 76, 73, 74], "vertices": [1, 0, -231.67, 150.6, 1, 1, 0, 197.83, 150.39, 1, 1, 0, 220.65, 141.63, 1, 1, 0, 243.47, 132.88, 1, 1, 0, 261.87, 125.29, 1, 1, 0, 287.96, 110.88, 1, 1, 0, 310.6, 98.19, 1, 1, 0, 320.11, 82.23, 1, 1, 0, 330.34, 59.9, 1, 1, 0, 339.55, 33.16, 1, 1, 0, 339.8, 7.04, 1, 1, 0, 339.8, -21.78, 1, 1, 0, 328.9, -52.23, 1, 1, 0, 311.59, -79.2, 1, 1, 0, 294.27, -106.18, 1, 1, 0, 269.86, -130.04, 1, 1, 0, 246.63, -144.58, 1, 1, 0, 212.25, -157.58, 1, 1, 0, 178.73, -167.82, 1, 1, 0, 146.8, -176.95, 1, 1, 0, 111.84, -185.88, 1, 1, 0, 76.35, -192.03, 1, 1, 0, 41.88, -196.66, 1, 1, 0, 2.73, -199.57, 1, 1, 0, -24.81, -201.61, 1, 1, 0, -52.66, -203.68, 1, 1, 0, -85.1, -205.4, 1, 1, 0, -119.71, -201.53, 1, 1, 0, -136.81, -197.65, 1, 1, 0, -161.7, -188.86, 1, 1, 0, -186.59, -180.07, 1, 1, 0, -205.76, -169.85, 1, 1, 0, -224.94, -159.64, 1, 1, 0, -244.11, -149.43, 1, 1, 0, -260.12, -138.51, 1, 1, 0, -284.73, -121.75, 1, 1, 0, -305.38, -107.68, 1, 1, 0, -324.17, -90.89, 1, 1, 0, -338.76, -61.51, 1, 1, 0, -349.52, -35.81, 1, 1, 0, -354.2, -13.18, 1, 1, 0, -354.2, 10.64, 1, 1, 0, -348.38, 33.96, 1, 1, 0, -339.85, 60.48, 1, 1, 0, -329.15, 82.06, 1, 1, 0, -312.45, 107.87, 1, 1, 0, -296.69, 126.49, 1, 1, 0, -279.39, 136.6, 1, 1, 0, -256.4, 144.68, 1, 1, 0, -230.44, 123.43, 1, 1, 0, -232.28, 95.34, 1, 1, 0, -224.45, 60.35, 1, 1, 0, -214.78, 29.04, 1, 1, 0, -202.35, 0.49, 1, 1, 0, -178.86, -23.46, 1, 1, 0, -126.83, -51.55, 1, 1, 0, -99.66, -60.29, 1, 1, 0, -152.85, -37.5, 1, 1, 0, -70.19, -75.95, 1, 1, 0, -32.44, -81.02, 1, 1, 0, 8.09, -77.33, 1, 1, 0, 44.92, -69.96, 1, 1, 0, 81.3, -60.29, 1, 1, 0, 119.98, -49.7, 1, 1, 0, 151.29, -37.27, 1, 1, 0, 180.62, -12.75, 1, 1, 0, 196.66, 16.54, 1, 1, 0, 207.95, 42.19, 1, 1, 0, 224.51, 65.87, 1, 1, 0, 226.53, 94.4, 1, 1, 0, 223.14, 118.73, 1, 1, 0, 210.17, 137.31, 1, 9, 65, 19.94, 22.68, 0.31501, 67, 7.42, 78.07, 0.03646, 69, -10.89, 152.25, 0.00393, 71, -144.32, 262.08, 0.00102, 72, -264.74, 258.71, 0.00134, 73, -366.38, 233.18, 0.00046, 76, -507.26, 83.82, 0.00241, 78, -523.89, 38.09, 0.00075, 0, -257.14, 123.53, 0.63862, 4, 65, -14.27, -5.99, 0.50963, 66, 18, 50.37, 0.1068, 67, -26.79, 49.4, 0.02147, 0, -291.35, 94.86, 0.3621, 10, 65, 23.33, -8.02, 0.4183, 66, 55.6, 48.34, 0.00076, 67, 10.8, 47.38, 0.18971, 69, -7.5, 121.56, 0.00862, 71, -140.93, 231.38, 0.0019, 72, -261.35, 228.01, 0.00198, 73, -362.99, 202.48, 0.00064, 76, -503.87, 53.13, 0.00329, 78, -520.5, 7.4, 0.00102, 0, -253.75, 92.83, 0.37377, 4, 65, -33.01, -40.8, 0.09778, 66, -0.74, 15.56, 0.52618, 67, -45.54, 14.59, 0.02849, 0, -310.09, 60.05, 0.34755, 5, 65, -3.27, -40.48, 0.21285, 66, 29.01, 15.88, 0.26014, 67, -15.79, 14.92, 0.527, 76, -530.47, 20.67, 1e-05, 78, -547.1, -25.06, 0, 10, 65, 24.77, -40.26, 0.13391, 66, 57.05, 16.1, 0.00397, 67, 12.25, 15.13, 0.50709, 69, -6.06, 89.31, 0.01616, 71, -139.48, 199.14, 0.00196, 72, -259.91, 195.77, 0.0015, 73, -361.55, 170.24, 0.00042, 76, -502.43, 20.89, 0.00223, 78, -519.06, -24.85, 0.00068, 0, -252.31, 60.59, 0.33206, 5, 66, -10.4, -19.54, 0.58182, 67, -55.2, -20.5, 0.01704, 68, -18.59, 64.28, 0.1464, 69, -73.5, 53.68, 0.00605, 0, -319.75, 24.96, 0.24869, 4, 66, 28.75, -17.59, 0.28862, 67, -16.05, -18.56, 0.53693, 68, 20.56, 66.22, 0.08286, 69, -34.35, 55.62, 0.09158, 11, 65, 28.89, -73.43, 0.00833, 66, 61.17, -17.08, 0.00249, 67, 16.37, -18.04, 0.57772, 68, 52.98, 66.74, 0.00774, 69, -1.94, 56.14, 0.1953, 71, -135.37, 165.97, 0.00339, 72, -255.79, 162.59, 0.0016, 73, -357.43, 137.06, 0.00033, 76, -498.31, -12.29, 0.002, 78, -514.94, -58.02, 0.00059, 0, -248.19, 27.42, 0.20051, 5, 66, -13.64, -50.59, 0.30916, 67, -58.43, -51.56, 0.01649, 68, -21.82, 33.22, 0.43645, 69, -76.74, 22.62, 0.00728, 0, -322.99, -6.1, 0.23062, 4, 66, 27.13, -48.01, 0.17815, 67, -17.67, -48.97, 0.21635, 68, 18.94, 35.81, 0.33299, 69, -35.97, 25.21, 0.27251, 12, 65, 31.74, -99.19, 0.00196, 66, 64.01, -42.83, 0.01056, 67, 19.21, -43.79, 0.3461, 68, 55.82, 40.98, 0.02069, 69, 0.91, 30.39, 0.53023, 70, -29.44, 105.53, 0.00054, 71, -132.52, 140.21, 0.00442, 72, -252.94, 136.84, 0.00158, 73, -354.58, 111.31, 0.00026, 76, -495.46, -38.04, 0.00183, 78, -512.09, -83.77, 0.00053, 0, -245.34, 1.66, 0.08129, 4, 66, -3.61, -81.7, 0.05251, 67, -48.4, -82.67, 0.00104, 68, -11.79, 2.11, 0.75159, 0, -312.96, -37.21, 0.19486, 5, 66, 33.6, -74.26, 0.02408, 67, -11.2, -75.22, 0.04184, 68, 25.41, 9.55, 0.49118, 69, -29.5, -1.04, 0.4309, 70, -59.85, 74.1, 0.01201, 10, 65, 45.97, -125.12, 0.00252, 67, 33.45, -69.72, 0.08652, 69, 15.15, 4.46, 0.80237, 70, -15.2, 79.6, 0.03919, 71, -118.28, 114.28, 0.0114, 72, -238.71, 110.91, 0.0023, 73, -340.34, 85.38, 0.00028, 76, -481.22, -63.97, 0.00235, 78, -497.86, -109.71, 0.00067, 0, -231.1, -24.27, 0.05241, 4, 68, 10.21, -30.21, 0.56444, 69, -44.71, -40.81, 0.08969, 70, -75.05, 34.34, 0.14956, 0, -290.96, -69.53, 0.19631, 3, 68, 44.82, -11.77, 0.2362, 69, -10.09, -22.37, 0.60362, 70, -40.44, 52.78, 0.16018, 4, 68, 36.41, -58.03, 0.21771, 69, -18.5, -68.63, 0.07887, 70, -48.85, 6.51, 0.37966, 0, -264.75, -97.36, 0.32376, 8, 67, 27.3, -120.16, 0.00046, 68, 63.91, -35.39, 0.10914, 69, 9, -45.98, 0.36562, 70, -21.35, 29.16, 0.5228, 71, -124.43, 63.84, 0.00163, 72, -244.85, 60.47, 8e-05, 76, -487.37, -114.41, 0.00022, 78, -504, -160.14, 5e-05, 11, 65, 70.56, -147.74, 0.00435, 67, 58.04, -92.34, 0.05865, 68, 94.65, -7.56, 0.00024, 69, 39.73, -18.16, 0.51973, 70, 9.39, 56.98, 0.24893, 71, -93.69, 91.67, 0.04714, 72, -214.12, 88.29, 0.00529, 73, -315.75, 62.76, 0.00046, 76, -456.64, -86.59, 0.00477, 78, -473.27, -132.32, 0.00133, 0, -206.51, -46.88, 0.10911, 10, 65, 99.03, -164.24, 0.00689, 67, 86.51, -108.84, 0.06116, 69, 68.21, -34.66, 0.29006, 70, 37.86, 40.48, 0.33614, 71, -65.22, 75.17, 0.14737, 72, -185.65, 71.79, 0.01136, 73, -287.28, 46.26, 0.00079, 76, -428.17, -103.09, 0.00786, 78, -444.8, -148.82, 0.00218, 0, -178.04, -63.39, 0.1362, 9, 65, 73.15, -192.06, 0.00053, 67, 60.63, -136.66, 0.00913, 69, 42.32, -62.48, 0.12269, 70, 11.98, 12.66, 0.80425, 71, -91.11, 47.34, 0.06085, 72, -211.53, 43.97, 0.00085, 73, -313.17, 18.44, 0, 76, -454.05, -130.91, 0.00134, 78, -470.68, -176.64, 0.00035, 5, 68, 72.32, -81, 0.02922, 69, 17.41, -91.6, 0.00291, 70, -12.94, -16.46, 0.60111, 71, -116.02, 18.22, 0.01274, 0, -228.84, -120.33, 0.35401, 7, 67, 74.24, -182.95, 0.00023, 69, 55.93, -108.77, 0.00041, 70, 25.58, -33.62, 0.50367, 71, -77.5, 1.06, 0.21792, 76, -440.44, -177.19, 3e-05, 78, -457.07, -222.93, 0, 0, -190.32, -137.49, 0.27774, 9, 65, 109.73, -205.66, 0.0017, 67, 97.21, -150.27, 0.01797, 69, 78.9, -76.09, 0.07741, 70, 48.56, -0.95, 0.51964, 71, -54.53, 33.74, 0.37653, 72, -174.95, 30.36, 0.00285, 73, -276.59, 4.83, 2e-05, 76, -417.47, -144.52, 0.00309, 78, -434.1, -190.25, 0.0008, 10, 65, 134, -179.46, 0.0084, 67, 121.47, -124.06, 0.05451, 69, 103.17, -49.88, 0.15155, 70, 72.82, 25.26, 0.21669, 71, -30.26, 59.94, 0.33504, 72, -150.68, 56.57, 0.02749, 73, -252.32, 31.04, 0.00131, 76, -393.2, -118.31, 0.01078, 78, -409.84, -164.04, 0.00297, 0, -143.08, -78.61, 0.19126, 10, 65, 161.8, -193.45, 0.00871, 67, 149.28, -138.06, 0.04715, 69, 130.98, -63.88, 0.09779, 70, 100.63, 11.27, 0.10632, 71, -2.45, 45.95, 0.56285, 72, -122.88, 42.58, 0.07048, 73, -224.51, 17.05, 0.0018, 76, -365.4, -132.31, 0.01313, 78, -382.03, -178.04, 0.00356, 0, -115.27, -92.6, 0.08821, 8, 65, 147.89, -224.19, 0.00131, 67, 135.37, -168.79, 0.0099, 69, 117.06, -94.61, 0.02762, 70, 86.72, -19.47, 0.14397, 71, -16.37, 15.21, 0.80839, 72, -136.79, 11.84, 0.00598, 76, -379.31, -163.04, 0.00226, 78, -395.94, -208.77, 0.00059, 5, 67, 116.93, -203.09, 0, 69, 98.62, -128.91, 0.0002, 70, 68.28, -53.76, 0.18057, 71, -34.81, -19.08, 0.53043, 0, -147.63, -157.63, 0.2888, 4, 70, 107.1, -64.76, 0.0178, 71, 4.02, -30.08, 0.63528, 72, -116.41, -33.45, 0.04761, 0, -108.8, -168.63, 0.29932, 8, 65, 186.39, -231.95, 0.00171, 67, 173.87, -176.56, 0.00847, 69, 155.56, -102.38, 0.013, 70, 125.22, -27.23, 0.00038, 71, 22.13, 7.45, 0.8219, 72, -98.29, 4.08, 0.15036, 76, -340.81, -170.81, 0.00333, 78, -357.44, -216.54, 0.00085, 10, 65, 200.95, -203.16, 0.00785, 67, 188.43, -147.76, 0.03457, 69, 170.12, -73.58, 0.05046, 70, 139.78, 1.56, 0.01039, 71, 36.69, 36.24, 0.50936, 72, -83.73, 32.87, 0.23958, 73, -185.37, 7.34, 0.00323, 76, -326.25, -142.01, 0.01498, 78, -342.88, -187.75, 0.00397, 0, -76.13, -102.31, 0.12561, 8, 65, 215.19, -274.66, 1e-05, 67, 202.66, -219.27, 0.00011, 69, 184.36, -145.08, 7e-05, 71, 50.93, -35.26, 0.45085, 72, -69.49, -38.63, 0.31243, 76, -312.01, -213.51, 2e-05, 78, -328.64, -259.25, 0, 0, -61.89, -173.81, 0.2365, 7, 65, 224.89, -240.37, 0.00144, 67, 212.37, -184.97, 0.00654, 69, 194.06, -110.79, 0.00805, 71, 60.64, -0.96, 0.48462, 72, -59.79, -4.34, 0.49535, 76, -302.31, -179.22, 0.00322, 78, -318.94, -224.95, 0.00079, 10, 65, 233.63, -211.25, 0.006, 67, 221.1, -155.85, 0.02383, 69, 202.8, -81.67, 0.02867, 70, 172.45, -6.53, 0.00029, 71, 69.37, 28.15, 0.32581, 72, -51.05, 24.78, 0.47729, 73, -152.69, -0.75, 0.00555, 76, -293.57, -150.1, 0.01428, 78, -310.2, -195.83, 0.00366, 0, -43.45, -110.4, 0.11462, 4, 71, 96.68, -33.64, 0.16281, 72, -23.74, -37.01, 0.60978, 73, -125.38, -62.54, 0.00434, 0, -16.14, -172.19, 0.22307, 7, 65, 262.55, -241.66, 0.00059, 67, 250.03, -186.27, 0.00251, 69, 231.73, -112.08, 0.00291, 71, 98.3, -2.26, 0.15053, 72, -22.13, -5.63, 0.84165, 76, -264.65, -180.51, 0.00147, 78, -281.28, -226.25, 0.00036, 10, 65, 265.79, -208.98, 0.00504, 67, 253.26, -153.59, 0.01793, 69, 234.96, -79.41, 0.01843, 71, 101.53, 30.42, 0.10797, 72, -18.89, 27.05, 0.61191, 73, -120.53, 1.52, 0.02573, 74, -205.31, -40.39, 0.00011, 76, -261.41, -147.83, 0.0157, 78, -278.04, -193.57, 0.00389, 0, -11.29, -108.13, 0.19328, 10, 65, 303.32, -206.72, 0.0042, 67, 290.79, -151.32, 0.01334, 69, 272.49, -77.14, 0.01128, 71, 139.06, 32.68, 0.01122, 72, 18.64, 29.31, 0.61023, 73, -83, 3.78, 0.18905, 74, -167.78, -38.13, 0.00071, 76, -223.88, -145.57, 0.02004, 78, -240.51, -191.3, 0.00453, 0, 26.24, -105.87, 0.13541, 7, 65, 304.94, -239.72, 0.00021, 67, 292.41, -184.32, 0.00063, 69, 274.11, -110.14, 0.00042, 72, 20.26, -3.69, 0.83677, 73, -81.38, -29.22, 0.16043, 76, -222.26, -178.57, 0.0013, 78, -238.89, -224.3, 0.00025, 4, 71, 144.24, -29.44, 0.00791, 72, 23.81, -32.81, 0.55004, 73, -77.82, -58.34, 0.13387, 0, 31.42, -167.99, 0.30819, 10, 65, 332.76, -199.6, 0.00416, 67, 320.24, -144.21, 0.01252, 69, 301.93, -70.03, 0.00953, 71, 168.5, 39.8, 0.00241, 72, 48.08, 36.43, 0.38282, 73, -53.56, 10.9, 0.44297, 74, -138.34, -31.01, 0.00398, 76, -194.44, -138.45, 0.02695, 78, -211.07, -184.19, 0.00563, 0, 55.68, -98.75, 0.10903, 7, 65, 341.5, -231.95, 0.0005, 67, 328.97, -176.56, 0.00155, 69, 310.67, -102.38, 0.00107, 72, 56.82, 4.08, 0.44547, 73, -44.82, -21.45, 0.54774, 76, -185.7, -170.81, 0.00308, 78, -202.34, -216.54, 0.00059, 4, 72, 63.29, -26.01, 0.30297, 73, -38.35, -51.54, 0.3606, 74, -123.13, -93.45, 0.00053, 0, 70.89, -161.19, 0.3359, 10, 65, 369.64, -187.01, 0.00324, 67, 357.12, -131.62, 0.00923, 69, 338.81, -57.44, 0.00637, 71, 205.39, 52.39, 0.0004, 72, 84.96, 49.02, 0.09176, 73, -16.67, 23.49, 0.62869, 74, -101.45, -18.42, 0.04124, 76, -157.56, -125.86, 0.03485, 78, -174.19, -171.6, 0.0061, 0, 92.57, -86.16, 0.17813, 7, 65, 374.82, -223.57, 0.00012, 67, 362.3, -168.18, 0.00039, 69, 343.99, -94, 0.00027, 72, 90.14, 12.46, 0.11676, 73, -11.5, -13.07, 0.88174, 76, -152.38, -162.42, 0.00059, 78, -169.01, -208.16, 0.00013, 4, 72, 97.26, -22.16, 0.11165, 73, -4.38, -47.69, 0.47559, 74, -89.16, -89.6, 0.02301, 0, 104.86, -157.34, 0.38975, 10, 65, 401.74, -176.21, 0.00231, 67, 389.22, -120.81, 0.00634, 69, 370.92, -46.63, 0.00403, 71, 237.49, 63.2, 4e-05, 72, 117.06, 59.82, 0.011, 73, 15.43, 34.29, 0.48686, 74, -69.35, -7.61, 0.2167, 76, -125.45, -115.06, 0.04443, 78, -142.09, -160.79, 0.00579, 0, 124.67, -75.36, 0.2225, 7, 65, 417.26, -208.7, 0.00012, 67, 404.74, -153.3, 0.00035, 69, 386.44, -79.12, 0.0002, 73, 30.95, 1.8, 0.68865, 74, -53.83, -40.11, 0.30686, 76, -109.94, -147.55, 0.00356, 78, -126.57, -193.29, 0.00025, 5, 72, 147.13, -9.04, 0.00588, 73, 45.5, -34.57, 0.41665, 74, -39.28, -76.48, 0.22349, 75, -100.48, -121.59, 0.00181, 0, 154.74, -144.22, 0.35216, 10, 65, 441.03, -164.08, 0.0013, 67, 428.5, -108.69, 0.00353, 69, 410.2, -34.51, 0.00209, 72, 156.35, 71.95, 0.00111, 73, 54.71, 46.42, 0.18962, 74, -30.07, 4.51, 0.5823, 75, -91.26, -40.59, 0.00217, 76, -86.17, -102.93, 0.05048, 78, -102.8, -148.67, 0.00391, 0, 163.95, -63.23, 0.16348, 7, 65, 458, -198.03, 1e-05, 67, 445.48, -142.64, 4e-05, 69, 427.17, -68.45, 2e-05, 73, 71.69, 12.47, 0.24637, 74, -13.09, -29.44, 0.74363, 75, -74.29, -74.54, 0.00965, 76, -69.2, -136.88, 0.00029, 4, 73, 88.17, -16.63, 0.20052, 74, 3.4, -58.54, 0.50452, 75, -57.8, -103.64, 0.04698, 0, 197.41, -126.28, 0.24797, 10, 65, 475.46, -148.08, 0.00068, 67, 462.94, -92.68, 0.00182, 69, 444.63, -18.5, 0.00102, 72, 190.78, 87.95, 0.00023, 73, 89.14, 62.42, 0.00981, 74, 4.37, 20.52, 0.72053, 75, -56.83, -24.59, 0.15757, 76, -51.74, -86.93, 0.0817, 78, -68.37, -132.66, 0.00151, 0, 198.38, -47.23, 0.02513, 4, 73, 114.85, 37.2, 0.00472, 74, 30.07, -4.7, 0.68379, 75, -31.12, -49.81, 0.31077, 76, -26.03, -112.15, 0.00072, 5, 73, 133.28, 9.07, 0.02936, 74, 48.5, -32.83, 0.54572, 75, -12.69, -77.94, 0.31671, 77, -55.06, -137.84, 0.00136, 0, 242.52, -100.57, 0.10685, 10, 65, 496.32, -122.86, 0.00075, 67, 483.79, -67.46, 0.00199, 69, 465.49, 6.72, 0.0011, 72, 211.64, 113.17, 0.00027, 73, 110, 87.64, 0.00358, 74, 25.22, 45.74, 0.28833, 75, -35.97, 0.63, 0.45981, 76, -30.88, -61.71, 0.20163, 78, -47.51, -107.44, 0.0011, 0, 219.24, -22.01, 0.04146, 3, 74, 58.2, 25.85, 0.17752, 75, -2.99, -19.25, 0.81339, 77, -45.36, -79.16, 0.00909, 4, 74, 86.33, -0.34, 0.16609, 75, 25.14, -45.44, 0.50245, 77, -17.23, -105.35, 0.0457, 0, 280.35, -68.08, 0.28576, 11, 65, 507.96, -93.78, 0.00076, 67, 495.43, -38.39, 0.002, 69, 477.13, 35.79, 0.0011, 72, 223.28, 142.25, 0.00046, 73, 121.64, 116.72, 0.00338, 74, 36.86, 74.81, 0.07282, 75, -24.33, 29.71, 0.33494, 77, -66.7, -30.2, 0.00356, 76, -19.24, -32.63, 0.49566, 78, -35.87, -78.37, 0.00181, 0, 230.88, 7.07, 0.08351, 3, 75, 18.35, 14.19, 0.68202, 77, -24.02, -45.72, 0.2221, 76, 23.44, -48.15, 0.09588, 5, 74, 107.18, 42.8, 0.01408, 75, 45.99, -2.3, 0.4766, 77, 3.62, -62.21, 0.24873, 76, 51.08, -64.64, 0.00271, 0, 301.2, -24.94, 0.25789, 9, 65, 527.81, -68.74, 2e-05, 67, 515.29, -13.35, 7e-05, 69, 496.98, 60.83, 4e-05, 72, 243.13, 167.29, 0, 73, 141.49, 141.76, 0.0001, 74, 56.72, 99.85, 0.00354, 75, -4.48, 54.74, 0.07635, 77, -46.85, -5.16, 0.03692, 76, 0.61, -7.6, 0.88297, 4, 75, 45.96, 43.1, 0.13052, 77, 3.59, -16.8, 0.62861, 76, 51.05, -19.24, 0.02006, 0, 301.17, 20.47, 0.22081, 9, 65, 537.51, -37.22, 0.00015, 67, 524.99, 18.17, 0.00039, 69, 506.68, 92.36, 0.0002, 72, 252.83, 198.81, 0.00013, 73, 151.19, 173.28, 0.00035, 74, 66.42, 131.37, 0.00043, 77, -37.15, 26.36, 0.09937, 76, 10.31, 23.93, 0.41988, 78, -6.32, -21.81, 0.4791, 4, 77, -1.75, 24.48, 0.45747, 76, 45.71, 22.04, 0.02406, 78, 29.07, -23.69, 0.23976, 0, 295.83, 61.74, 0.2787, 7, 65, 548.06, -9.6, 2e-05, 67, 535.54, 45.8, 4e-05, 69, 517.24, 119.98, 2e-05, 72, 263.38, 226.43, 0, 77, -26.59, 53.99, 0.01559, 78, 4.23, 5.82, 0.65056, 0, 270.99, 91.25, 0.33377, 9, 65, 523.57, 14.09, 0.00079, 67, 511.05, 69.49, 0.00199, 69, 492.74, 143.67, 0.00105, 72, 238.89, 250.12, 0.00072, 73, 137.26, 224.59, 0.00165, 74, 52.48, 182.69, 0.00161, 76, -3.63, 75.24, 0.04558, 78, -20.26, 29.51, 0.24176, 0, 246.5, 114.94, 0.70484], "hull": 49, "edges": [22, 24, 32, 34, 50, 52, 86, 88, 2, 0, 0, 96, 92, 94, 94, 96, 88, 90, 90, 92, 82, 84, 84, 86, 78, 80, 80, 82, 74, 76, 76, 78, 72, 74, 70, 72, 66, 68, 68, 70, 64, 66, 60, 62, 62, 64, 56, 58, 58, 60, 52, 54, 54, 56, 48, 50, 46, 48, 42, 44, 44, 46, 40, 42, 38, 40, 34, 36, 36, 38, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 18, 20, 16, 18, 12, 14, 14, 16, 8, 10, 10, 12, 6, 8, 0, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 110, 112, 108, 114, 114, 110, 112, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 2, 4, 4, 6, 140, 142, 142, 2], "width": 694, "height": 356}}, "flag": {"flag": {"type": "mesh", "uvs": [0.76178, 0, 0.75746, 0.03461, 0.78202, 0.03866, 0.80819, 0.04285, 0.83786, 0.04948, 0.86658, 0.0593, 0.89789, 0.0759, 0.91119, 0.09729, 0.91857, 0.12679, 0.92688, 0.14401, 0.93591, 0.16521, 0.90982, 0.1592, 0.89255, 0.15182, 0.87379, 0.14344, 0.85491, 0.13779, 0.82823, 0.13456, 0.79884, 0.13777, 0.77062, 0.14387, 0.75134, 0.15081, 0.82306, 0.1613, 0.8635, 0.1806, 0.87122, 0.20435, 0.81816, 0.26701, 0.77078, 0.29257, 0.86053, 0.43544, 0.99991, 0.47001, 1, 0.47278, 1, 0.49979, 0.95829, 0.49837, 0.9454, 0.51744, 0.92189, 0.5528, 0.89864, 0.58847, 0.86913, 0.63759, 0.84463, 0.69811, 0.8254, 0.76189, 0.81344, 0.83696, 0.80999, 0.90643, 0.81684, 0.99999, 0.76079, 0.96246, 0.70041, 0.92195, 0.63816, 0.88878, 0.57311, 0.86475, 0.49324, 0.84018, 0.41854, 0.82579, 0.35308, 0.82407, 0.31988, 0.77198, 0.32066, 0.72897, 0.33845, 0.67725, 0.37194, 0.62091, 0.40801, 0.5706, 0.42767, 0.55364, 0.44699, 0.51655, 0.47854, 0.47203, 0.50785, 0.42404, 0.51203, 0.40178, 0.50592, 0.39854, 0.47404, 0.42103, 0.44473, 0.43424, 0.39772, 0.47226, 0.36089, 0.51439, 0.32483, 0.55904, 0.29256, 0.60779, 0.27539, 0.64492, 0.25521, 0.68413, 0.24306, 0.73372, 0.245, 0.77568, 0.2778, 0.79744, 0.24162, 0.79453, 0.19643, 0.78228, 0.13793, 0.76828, 0.09231, 0.75865, 0.05327, 0.76238, 0.03841, 0.77295, 0.02709, 0.80162, 0.00971, 0.77605, 1e-05, 0.73623, 0, 0.68635, 0.00956, 0.66352, 0.05358, 0.60585, 0.10476, 0.55348, 0.17251, 0.49922, 0.2372, 0.45984, 0.30902, 0.42068, 0.38876, 0.39646, 0.43749, 0.3886, 0.48621, 0.38073, 0.505, 0.3626, 0.6093, 0.37863, 0.61584, 0.37847, 0.67428, 0.28574, 0.6324, 0.21547, 0.61844, 0.16477, 0.71456, 0.15268, 0.72208, 0.06556, 0.7054, 0.05443, 0.71709, 1e-05, 0.43911, 0.79114, 0.57286, 0.81724, 0.70259, 0.85959, 0.44636, 0.74356, 0.70904, 0.80042, 0.58253, 0.77025, 0.4665, 0.69251, 0.59865, 0.71746, 0.72515, 0.74472, 0.49183, 0.64182, 0.73678, 0.69171, 0.52859, 0.58325, 0.75582, 0.62966, 0.55921, 0.5351, 0.77758, 0.57165, 0.58693, 0.49411, 0.78984, 0.53399, 0.62351, 0.45696, 0.80918, 0.49659, 0.61481, 0.66563, 0.6277, 0.614, 0.66155, 0.56063, 0.64158, 0.42982, 0.83027, 0.47066, 0.06534, 0.73228, 0.16703, 0.73409, 0.08604, 0.67667, 0.17407, 0.68423, 0.16226, 0.62839, 0.20297, 0.58179, 0.2466, 0.53254], "triangles": [73, 74, 72, 67, 65, 66, 67, 68, 65, 69, 121, 68, 68, 64, 65, 68, 121, 64, 74, 75, 72, 72, 75, 71, 69, 70, 121, 71, 120, 70, 71, 75, 120, 70, 120, 121, 75, 76, 120, 120, 122, 121, 121, 123, 64, 120, 76, 122, 121, 122, 123, 64, 123, 63, 122, 76, 77, 122, 124, 123, 123, 124, 63, 63, 124, 62, 77, 78, 122, 122, 78, 124, 78, 79, 124, 124, 125, 62, 62, 125, 61, 124, 79, 125, 125, 126, 61, 61, 126, 60, 79, 80, 125, 125, 80, 126, 60, 126, 59, 80, 81, 126, 126, 81, 59, 81, 82, 59, 59, 82, 58, 82, 83, 58, 58, 83, 57, 83, 84, 57, 38, 36, 37, 38, 39, 36, 43, 44, 96, 44, 45, 96, 39, 98, 36, 39, 40, 98, 36, 98, 35, 98, 40, 97, 42, 97, 41, 40, 41, 97, 98, 100, 35, 98, 97, 100, 43, 96, 42, 42, 96, 97, 35, 100, 34, 97, 101, 100, 97, 96, 101, 100, 104, 34, 100, 101, 104, 96, 99, 101, 96, 45, 99, 45, 46, 99, 101, 99, 103, 101, 103, 104, 103, 99, 102, 46, 47, 99, 99, 47, 102, 102, 105, 103, 47, 48, 102, 104, 106, 34, 34, 106, 33, 104, 103, 106, 103, 115, 106, 103, 105, 115, 106, 108, 33, 33, 108, 32, 102, 48, 105, 106, 115, 108, 108, 115, 116, 115, 105, 116, 116, 105, 107, 48, 49, 105, 105, 49, 107, 107, 49, 50, 50, 51, 107, 108, 110, 32, 32, 110, 31, 116, 117, 108, 108, 117, 110, 107, 109, 116, 116, 109, 117, 110, 112, 31, 31, 112, 30, 107, 51, 109, 110, 117, 112, 109, 111, 117, 117, 111, 112, 112, 114, 30, 51, 52, 109, 109, 52, 111, 111, 113, 112, 52, 53, 111, 30, 114, 29, 112, 113, 114, 114, 119, 29, 29, 119, 28, 28, 26, 27, 119, 24, 28, 28, 25, 26, 28, 24, 25, 113, 118, 114, 114, 118, 119, 111, 53, 113, 119, 118, 24, 113, 53, 118, 118, 53, 54, 118, 23, 24, 57, 84, 56, 54, 87, 118, 87, 88, 118, 23, 118, 88, 56, 85, 55, 56, 84, 85, 87, 54, 86, 85, 86, 55, 54, 55, 86, 11, 9, 10, 11, 12, 9, 9, 12, 8, 12, 13, 8, 8, 13, 7, 7, 13, 14, 7, 14, 6, 6, 14, 5, 14, 15, 5, 15, 4, 5, 15, 3, 4, 3, 17, 2, 3, 16, 17, 16, 3, 15, 22, 18, 19, 90, 91, 92, 92, 18, 22, 92, 23, 90, 94, 95, 1, 23, 88, 89, 92, 22, 23, 90, 23, 89, 1, 95, 0, 93, 94, 1, 1, 18, 93, 92, 93, 18, 1, 2, 17, 18, 1, 17, 21, 22, 19, 21, 19, 20], "vertices": [1, 13, 183.67, -15.3, 1, 1, 13, 167.23, -13.82, 1, 4, 13, 165.3, -22.22, 0.47244, 7, 23.86, 1.81, 0.40012, 8, 25.06, 14.25, 0.11611, 9, 29.34, 26.55, 0.01134, 4, 13, 163.31, -31.17, 0.12792, 7, 21.87, -7.14, 0.44111, 8, 23.07, 5.3, 0.36102, 9, 27.35, 17.6, 0.06995, 3, 7, 18.72, -17.28, 0.18506, 8, 19.92, -4.85, 0.54152, 9, 24.2, 7.45, 0.27342, 4, 7, 14.05, -27.11, 0.00696, 8, 15.26, -14.67, 0.45976, 9, 19.54, -2.37, 0.46823, 10, 27.69, 6.06, 0.06505, 3, 8, 7.38, -25.38, 0.13964, 9, 11.65, -13.08, 0.56144, 10, 19.81, -4.65, 0.29892, 3, 9, 1.49, -17.62, 0.41454, 10, 9.65, -9.2, 0.48874, 11, 19.01, -2.11, 0.09672, 3, 9, -12.52, -20.15, 0.01559, 10, -4.36, -11.72, 0.16587, 11, 5, -4.64, 0.81854, 2, 11, -3.18, -7.48, 0.30392, 12, 7.11, -0.12, 0.69608, 4, 13, 105.19, -74.85, 0, 8, -35.05, -38.38, 1e-05, 12, -2.95, -3.21, 0.99999, 17, 87.66, 305.63, 0, 2, 11, -10.4, -1.64, 0.06509, 12, -0.1, 5.71, 0.93491, 6, 13, 111.55, -60.02, 0, 8, -28.69, -23.55, 0.00217, 10, -16.25, -2.83, 0.04379, 11, -6.89, 4.26, 0.67426, 12, 3.4, 11.62, 0.27978, 17, 71.53, 305.78, 0, 7, 13, 115.53, -53.61, 0.00105, 7, -25.91, -29.57, 0.00504, 8, -24.71, -17.14, 0.03218, 9, -20.43, -4.83, 0.053, 10, -12.27, 3.59, 0.25342, 11, -2.91, 10.68, 0.65531, 17, 64.07, 306.97, 0, 7, 13, 118.22, -47.15, 0.0047, 7, -23.23, -23.11, 0.02975, 8, -22.02, -10.68, 0.108, 9, -17.75, 1.63, 0.21914, 10, -9.59, 10.05, 0.32333, 11, -0.23, 17.14, 0.31508, 17, 57.08, 306.96, 0, 7, 13, 119.75, -38.02, 0.02828, 7, -21.69, -13.99, 0.21485, 8, -20.49, -1.55, 0.2916, 9, -16.21, 10.75, 0.22748, 10, -8.05, 19.17, 0.17409, 11, 1.31, 26.26, 0.06369, 17, 48.07, 304.86, 0, 7, 13, 118.22, -27.97, 0.09541, 7, -23.22, -3.94, 0.55402, 8, -22.02, 8.5, 0.2339, 9, -17.74, 20.8, 0.0615, 10, -9.58, 29.22, 0.04709, 11, -0.22, 36.31, 0.00808, 17, 39.38, 299.57, 0, 7, 13, 115.33, -18.32, 0.53675, 7, -26.11, 5.71, 0.41414, 8, -24.91, 18.15, 0.03734, 9, -20.63, 30.45, 0.0055, 10, -12.47, 38.87, 0.00589, 11, -3.11, 45.96, 0.00038, 17, 31.6, 293.18, 0, 1, 13, 112.03, -11.73, 1, 1, 13, 107.05, -36.25, 1, 1, 13, 97.88, -50.09, 1, 1, 13, 86.6, -52.73, 1, 1, 13, 56.84, -34.58, 1, 1, 13, 44.69, -18.38, 1, 1, 14, 125.08, 10.46, 1, 1, 14, 175.47, 8.85, 1, 1, 14, 175.89, 7.6, 1, 1, 14, 179.68, -4.65, 1, 1, 14, 165.86, -8.22, 1, 5, 14, 164.32, -18.18, 0.7718, 15, 153.74, 43.6, 0.22644, 16, 160.88, 97.22, 0.00174, 17, 155.2, 152.52, 1e-05, 22, 112.87, 218.44, 0, 5, 14, 161.6, -36.6, 0.37493, 15, 151.58, 25.11, 0.62147, 16, 159.07, 78.69, 0.00356, 17, 154.26, 133.93, 3e-05, 22, 113.46, 199.83, 0, 2, 14, 159.02, -55.14, 0.0351, 15, 149.56, 6.5, 0.9649, 5, 14, 156.27, -80.41, 0.00154, 15, 147.58, -18.84, 0.68251, 16, 155.91, 34.67, 0.31342, 17, 153.15, 89.81, 0.00252, 22, 115.99, 155.77, 1e-05, 3, 14, 156.76, -110.35, 0.00011, 15, 148.97, -48.75, 0.0258, 16, 157.88, 4.79, 0.97408, 5, 14, 159.43, -141.24, 1e-05, 15, 152.58, -79.54, 0.00279, 16, 162.07, -25.93, 0.4926, 17, 162.13, 29.56, 0.50331, 22, 129.9, 96.47, 0.00129, 2, 17, 172.11, -4.91, 0.96537, 22, 142.68, 62.93, 0.03463, 5, 14, 174.68, -208.38, 0, 15, 169.86, -146.19, 1e-05, 16, 180.62, -92.24, 0.0016, 17, 183.75, -35.81, 0.38978, 22, 156.83, 33.1, 0.6086, 1, 22, 179.37, -5.27, 1, 5, 14, 166.47, -238.78, 0, 15, 162.57, -176.83, 0, 16, 173.93, -123.01, 8e-05, 17, 178.5, -66.86, 0.02507, 22, 154.15, 1.72, 0.97484, 5, 14, 141.05, -226.5, 0, 15, 136.8, -165.33, 0, 16, 147.94, -112, 0.00062, 17, 152.02, -57.07, 0.49745, 22, 126.96, 9.3, 0.50193, 5, 14, 116.06, -217.74, 0, 15, 111.55, -157.32, 0, 16, 122.54, -104.48, 0.00019, 17, 126.31, -50.74, 0.81788, 22, 100.81, 13.48, 0.18192, 4, 15, 86.8, -153.74, 0, 16, 97.73, -101.37, 2e-05, 17, 101.38, -48.79, 0.89303, 22, 75.8, 13.37, 0.10695, 2, 17, 71.67, -48.57, 0.74388, 22, 46.18, 11.15, 0.25612, 2, 17, 45.47, -52.11, 0.3457, 22, 20.35, 5.46, 0.6543, 2, 15, 9.36, -159.87, 0, 22, 0.1, -4.12, 1, 4, 14, -4.33, -196.9, 0, 15, -9.41, -140.15, 0, 17, 4.47, -41.55, 0.35891, 22, -21.37, 12.62, 0.64109, 4, 14, -10.11, -177.3, 0, 15, -15.78, -120.73, 0, 17, -3.16, -22.6, 0.72795, 22, -30.54, 30.87, 0.27205, 2, 16, -8.17, -45.17, 0.04065, 17, -7.02, 2.41, 0.95935, 2, 16, -6.57, -16.1, 0.69766, 17, -6.78, 31.52, 0.30234, 3, 14, -3.8, -96.61, 0, 15, -11.92, -39.89, 0.1004, 16, -3.16, 10.57, 0.8996, 3, 14, 0.25, -86.93, 0, 15, -8.18, -30.09, 0.31792, 16, 0.4, 20.44, 0.68208, 2, 15, -7.64, -11.28, 0.79251, 16, 0.58, 39.26, 0.20749, 3, 14, 5.41, -44.75, 0.13468, 15, -4.29, 12.23, 0.86532, 22, -38.8, 164.07, 0, 3, 14, 8.25, -20.01, 0.67123, 15, -2.2, 37.04, 0.32877, 22, -40.4, 188.92, 0, 1, 14, 6.5, -9.49, 1, 1, 14, 4.04, -8.63, 1, 6, 13, -16.32, 83.11, 0, 7, -157.76, 107.14, 0, 14, -3.22, -22.06, 0.69628, 17, -11.19, 132.59, 0, 22, -51.32, 184.87, 0, 18, 17.46, -48.82, 0.30371, 5, 13, -22.6, 93.13, 0, 7, -164.04, 117.17, 0, 14, -10.94, -31.02, 0.46426, 17, -18.02, 122.93, 0, 18, 11.18, -38.8, 0.53573, 6, 13, -40.66, 109.21, 0, 7, -182.1, 133.24, 0, 14, -20.96, -53.03, 0.10154, 17, -25.89, 100.06, 0, 18, -6.88, -22.72, 0.887, 19, 39.51, -65.18, 0.01145, 4, 13, -60.67, 121.81, 0, 14, -27.08, -75.86, 0.00054, 18, -26.89, -10.13, 0.75747, 19, 19.51, -52.58, 0.24198, 3, 18, -48.1, 2.21, 0.34708, 19, -1.7, -40.25, 0.63655, 20, 46.82, -64.51, 0.01637, 4, 18, -71.26, 13.24, 0.06039, 19, -24.86, -29.21, 0.70842, 20, 23.66, -53.47, 0.22548, 21, 83.1, 86.51, 0.00571, 4, 18, -88.89, 19.11, 0.00609, 19, -42.5, -23.34, 0.45845, 20, 6.02, -47.6, 0.4794, 21, 79.95, 68.2, 0.05606, 3, 19, -61.12, -16.44, 0.17741, 20, -12.6, -40.7, 0.57517, 21, 75.92, 48.74, 0.24742, 3, 19, -84.68, -12.28, 0.02241, 20, -36.16, -36.54, 0.27605, 21, 75.35, 24.83, 0.70154, 3, 19, -104.61, -12.94, 0.00053, 20, -56.09, -37.2, 0.01049, 21, 79, 5.23, 0.98898, 3, 19, -114.94, -24.16, 0, 20, -66.42, -48.42, 1e-05, 21, 91.64, -3.3, 0.99999, 1, 21, 79.21, -3.8, 1, 2, 20, -59.22, -20.59, 0.0118, 21, 63.05, -0.37, 0.9882, 2, 20, -52.57, -0.59, 0.19044, 21, 42.27, 3.2, 0.80956, 2, 20, -48, 15.01, 0.18877, 21, 26.16, 5.38, 0.81123, 2, 20, -49.77, 28.37, 0.03634, 21, 13.22, 1.62, 0.96366, 1, 21, 8.96, -4.11, 1, 2, 20, -68.41, 37.32, 0.00181, 21, 7.17, -18.15, 0.99819, 1, 21, -0.53, -7.03, 1, 2, 20, -37.35, 46.58, 0.12007, 21, -6.65, 11.17, 0.87993, 2, 20, -13.66, 46.58, 0.51008, 21, -10.21, 34.59, 0.48992, 3, 19, -51.33, 67.57, 0.00707, 20, -2.81, 43.31, 0.65634, 21, -8.61, 45.8, 0.33659, 3, 19, -23.94, 52.52, 0.18884, 20, 24.58, 28.26, 0.77575, 21, 2.16, 75.15, 0.0354, 2, 19, 0.94, 35.02, 0.63297, 20, 49.46, 10.76, 0.36703, 3, 18, -19.68, 54.3, 0.14909, 19, 26.71, 11.85, 0.845, 20, 75.23, -12.41, 0.00591, 2, 18, -0.98, 32.18, 0.59781, 19, 45.42, -10.28, 0.40219, 6, 13, -16.16, 139.55, 0, 7, -157.6, 163.58, 0, 14, -57.18, -38.58, 0.02054, 17, -63.32, 110.97, 0, 18, 17.62, 7.61, 0.96382, 19, 64.02, -34.84, 0.01564, 5, 13, -4.65, 112.27, 0, 7, -146.1, 136.31, 0, 14, -34.53, -19.53, 0.32704, 17, -42.6, 132.1, 0, 18, 29.12, -19.66, 0.67296, 5, 13, -0.92, 95.61, 0, 7, -142.36, 119.64, 0, 14, -19.71, -11.04, 0.61175, 17, -28.67, 141.98, 0, 18, 32.86, -36.32, 0.38825, 1, 14, -4.89, -2.55, 1, 1, 14, -1.3, 7.58, 1, 1, 14, 35.03, 10.85, 1, 1, 14, 37.14, 11.58, 1, 1, 13, 47.94, 14.63, 1, 1, 13, 81.32, 28.95, 1, 1, 13, 105.4, 33.72, 1, 1, 13, 111.14, 0.85, 1, 1, 13, 152.53, -1.72, 1, 1, 13, 157.81, 3.99, 1, 1, 13, 183.66, -0.01, 1, 2, 17, 45.6, -34.22, 0.55345, 22, 19.02, 23.31, 0.44655, 3, 16, 89.92, -80.2, 0, 17, 92.59, -28.01, 0.918, 22, 65.34, 33.37, 0.082, 5, 14, 133.01, -197.98, 0, 15, 127.9, -137.06, 0, 16, 138.5, -83.91, 0.00025, 17, 141.29, -29.45, 0.82467, 22, 113.98, 35.94, 0.17508, 2, 17, 39.18, -12.41, 0.86761, 22, 10.82, 44.51, 0.13239, 2, 17, 132.48, -2.67, 0.9947, 22, 103, 61.9, 0.0053, 2, 17, 87.03, -6.14, 0.98617, 22, 57.99, 54.7, 0.01383, 3, 15, 25.78, -88.18, 0, 16, 35.46, -36.98, 0.21338, 17, 36.18, 12.62, 0.78662, 2, 16, 81.98, -32.65, 0.35524, 17, 82.44, 19.12, 0.64476, 5, 14, 124.26, -143.57, 0, 15, 117.5, -82.95, 0.00034, 16, 127.07, -30, 0.44402, 17, 127.36, 23.86, 0.55558, 22, 95.71, 87.92, 6e-05, 2, 16, 35.35, -11.4, 0.82553, 17, 34.88, 38.18, 0.17447, 3, 16, 122.18, -4.98, 0.95344, 17, 121.31, 48.63, 0.04655, 22, 87.65, 112.11, 1e-05, 3, 14, 37.38, -90.16, 0, 15, 29.04, -32.2, 0.33857, 16, 37.65, 19.05, 0.66143, 5, 14, 118.13, -88.26, 0, 15, 109.7, -27.85, 0.45432, 16, 118.21, 24.94, 0.54552, 17, 115.95, 78.33, 0.00016, 22, 79.86, 141.27, 0, 2, 15, 31.52, -7.16, 0.90577, 16, 39.65, 44.12, 0.09423, 2, 14, 117.1, -59.74, 0.00182, 15, 107.8, 0.63, 0.99818, 2, 14, 43.92, -43.82, 0.20065, 15, 34.18, 14.33, 0.79935, 3, 14, 115.82, -41.41, 0.2542, 15, 105.96, 18.91, 0.7458, 17, 109.15, 124.75, 0, 2, 14, 50.66, -23.26, 0.63058, 15, 40.29, 35.08, 0.36942, 4, 14, 116.89, -22.49, 0.6433, 15, 106.46, 37.86, 0.35657, 16, 113.71, 90.57, 0.00013, 17, 108.4, 143.69, 0, 2, 16, 78.75, -7.62, 0.91343, 17, 78.05, 43.96, 0.08657, 2, 15, 65.84, -35.02, 0.27588, 16, 74.49, 16.93, 0.72412, 2, 15, 68.56, -7.28, 0.91311, 16, 76.69, 44.71, 0.08689, 1, 14, 52.76, -9.12, 1, 1, 14, 120.14, -8.59, 1, 2, 20, -35.47, 24.24, 0.234, 21, 15.16, 16.38, 0.766, 3, 19, -84.85, 13.72, 0.00211, 20, -36.33, -10.54, 0.32235, 21, 49.67, 20.75, 0.67554, 3, 19, -57.58, 41.42, 0.00029, 20, -9.06, 17.16, 0.74765, 21, 18.19, 43.56, 0.25206, 3, 19, -61.17, 11.31, 0.04161, 20, -12.65, -12.95, 0.71967, 21, 48.49, 44.53, 0.23871, 2, 19, -34.65, 15.35, 0.27049, 20, 13.87, -8.91, 0.72951, 2, 19, -12.51, 1.43, 0.78862, 20, 36.01, -22.83, 0.21138, 2, 18, -35.51, 28.96, 0.23505, 19, 10.88, -13.49, 0.76495], "hull": 96, "edges": [0, 190, 0, 2, 18, 20, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 66, 68, 68, 70, 70, 72, 72, 74, 78, 80, 80, 82, 82, 84, 88, 90, 140, 142, 146, 148, 148, 150, 162, 164, 172, 174, 174, 176, 176, 178, 182, 184, 184, 186, 186, 188, 188, 190, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 164, 166, 170, 172, 142, 144, 144, 146, 138, 140, 136, 138, 132, 134, 134, 136, 132, 130, 130, 128, 128, 126, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 84, 86, 86, 88, 76, 78, 74, 76, 62, 64, 64, 66, 48, 50, 42, 44, 20, 22, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 30, 32, 32, 34, 4, 6, 6, 8, 2, 36, 24, 26, 26, 28, 180, 182, 178, 180, 110, 170, 56, 58, 60, 62, 58, 60, 222, 104, 60, 224, 224, 222, 226, 106, 58, 228, 228, 226, 236, 108, 56, 238, 238, 236, 166, 168, 168, 170, 122, 124, 124, 126], "width": 342, "height": 475}}, "gun": {"gun": {"type": "mesh", "uvs": [0.17786, 0.02139, 0.24207, 0.11772, 0.42644, 0.24063, 0.47669, 0.15427, 0.66858, 0.29995, 0.70493, 0.27043, 0.92091, 0.4011, 0.97655, 0.46416, 0.99739, 0.62394, 0.95409, 0.81983, 0.87867, 0.96161, 0.83276, 1, 0.51086, 0.8955, 0.45349, 0.89624, 0.39845, 0.84964, 0.40436, 0.65374, 0.37398, 0.57997, 0.309, 0.49857, 0.1637, 0.41418, 0.09177, 0.40006, 0.02974, 0.35741, 0, 0.27853, 0.00577, 0.13374, 0.05653, 0.02773, 0.11383, 0, 0.43944, 0.49772, 0.47414, 0.36019, 0.24273, 0.2129, 0.20802, 0.33224, 0.14892, 0.34046, 0.18599, 0.24953, 0.19703, 0.11427, 0.1071, 0.12697, 0.08961, 0.25862, 0.73469, 0.48586, 0.69137, 0.66498, 0.58276, 0.76954], "triangles": [1, 31, 0, 32, 23, 24, 32, 24, 0, 32, 0, 31, 22, 23, 32, 2, 27, 1, 30, 32, 31, 33, 22, 32, 33, 32, 30, 21, 22, 33, 31, 27, 30, 27, 31, 1, 28, 30, 27, 29, 33, 30, 29, 30, 28, 20, 21, 33, 19, 20, 33, 26, 2, 3, 26, 3, 4, 27, 2, 26, 29, 19, 33, 18, 29, 28, 19, 29, 18, 34, 5, 6, 4, 5, 34, 26, 4, 34, 26, 28, 27, 25, 28, 26, 17, 28, 25, 18, 28, 17, 16, 17, 25, 6, 7, 8, 34, 6, 8, 15, 16, 25, 34, 25, 26, 35, 34, 8, 35, 25, 34, 36, 15, 25, 35, 36, 25, 9, 35, 8, 15, 13, 14, 15, 36, 13, 36, 12, 13, 10, 35, 9, 36, 35, 10, 11, 36, 10, 12, 36, 11], "vertices": [161.5, -26.82, 144.86, -23.54, 108.26, -30.11, 107.03, -44.37, 67.65, -49.37, 64.35, -56.26, 22.46, -65.34, 9.53, -64.63, -5.24, -50.19, -13.02, -25.27, -12.05, -2.61, -7.97, 6.21, 47.89, 29.14, 56.44, 35.24, 68.08, 36.17, 81.47, 15.17, 91.4, 10.69, 107.07, 9.05, 135, 15.53, 146.81, 21.61, 159.21, 23.69, 169.42, 18.61, 179.1, 2.94, 179.22, -13.42, 172.65, -22.32, 87.58, -4.74, 92.4, -22.69, 137.82, -13.71, 134.33, 2.35, 142.59, 9.41, 143.66, -3.94, 151.86, -19.17, 164.41, -8.4, 157.44, 7.13, 44.2, -36.97, 37.63, -13.79, 46.29, 8.49], "hull": 25, "edges": [0, 48, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 36, 38, 38, 40, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 6, 8, 8, 10, 10, 12, 52, 54, 50, 56, 58, 60, 60, 62, 64, 66, 16, 68, 68, 52, 18, 70, 70, 50, 20, 72, 72, 30], "width": 183, "height": 127}}, "hat": {"hat": {"type": "mesh", "uvs": [0.69516, 0, 0.77309, 0.14014, 0.78021, 0.35321, 0.80066, 0.51805, 0.8646, 0.66087, 1, 0.81154, 1, 0.93796, 0.93254, 0.97109, 0.81257, 1, 0.67697, 1, 0.30538, 0.90584, 0.10712, 0.78662, 0, 0.62364, 0.00013, 0.46358, 0.05091, 0.40183, 0.17054, 0.40187, 0.25753, 0.29693, 0.32391, 0.17645, 0.40981, 0.06678, 0.51914, 0, 0.26851, 0.54491, 0.44811, 0.67684, 0.71955, 0.71431, 0.4157, 0.44555, 0.61286, 0.48464, 0.55571, 0.30562, 0.58916, 0.13847], "triangles": [4, 7, 8, 8, 9, 22, 8, 22, 4, 6, 7, 5, 7, 4, 5, 10, 21, 9, 9, 21, 22, 11, 20, 10, 10, 20, 21, 11, 12, 20, 20, 12, 13, 15, 13, 14, 13, 15, 20, 15, 16, 20, 21, 24, 22, 22, 3, 4, 22, 24, 3, 20, 23, 21, 21, 23, 24, 20, 16, 23, 24, 2, 3, 23, 25, 24, 24, 25, 2, 2, 26, 1, 26, 2, 25, 16, 17, 23, 23, 17, 25, 17, 18, 25, 25, 18, 26, 26, 18, 19, 26, 0, 1, 26, 19, 0], "vertices": [1, 3, 53.98, -6.07, 1, 2, 3, 43.27, -18.45, 0.99861, 6, 63.8, 37, 0.00139, 3, 3, 23.55, -24.7, 0.94794, 5, 57.16, -27.21, 0.00035, 6, 44.09, 30.75, 0.05171, 3, 3, 8.76, -31.22, 0.72504, 5, 42.36, -33.73, 0.0327, 6, 29.29, 24.22, 0.24226, 3, 3, -2.63, -42.09, 0.32403, 5, 30.97, -44.6, 0.0617, 6, 17.9, 13.36, 0.61427, 3, 3, -12.55, -61.23, 0.00828, 5, 21.05, -63.73, 0.00011, 6, 7.98, -5.78, 0.99161, 1, 6, -3.85, -9.01, 1, 3, 3, -29.56, -57.69, 0.00087, 5, 4.04, -60.2, 0.01681, 6, -9.03, -2.24, 0.98232, 3, 3, -35.97, -44.89, 0.03845, 5, -2.37, -47.4, 0.16773, 6, -15.44, 10.56, 0.79383, 3, 3, -40.15, -29.59, 0.07925, 5, -6.55, -32.09, 0.43549, 6, -19.62, 25.86, 0.48527, 3, 3, -42.8, 14.76, 0.00194, 4, -29.79, -37.37, 0.13812, 5, -9.2, 12.25, 0.85994, 3, 3, -37.76, 40.18, 0.00745, 4, -24.75, -11.94, 0.64815, 5, -4.16, 37.68, 0.3444, 2, 4, -12.8, 4.32, 0.94459, 5, 7.79, 53.93, 0.05541, 1, 4, 2.18, 8.39, 1, 2, 3, -3.49, 56.37, 0.01498, 4, 9.52, 4.24, 0.98502, 3, 3, 0.2, 42.86, 0.27002, 4, 13.21, -9.26, 0.69861, 5, 33.8, 40.36, 0.03137, 3, 3, 12.7, 35.73, 0.68065, 4, 25.71, -16.4, 0.30743, 5, 46.3, 33.22, 0.01192, 2, 3, 26.02, 31.32, 0.88314, 4, 39.03, -20.81, 0.11686, 2, 3, 38.93, 24.43, 0.96624, 4, 51.94, -27.7, 0.03376, 2, 3, 48.55, 13.8, 0.99508, 4, 61.57, -38.33, 0.00492, 3, 3, -10.17, 28.15, 0.35306, 4, 2.85, -23.98, 0.46038, 5, 23.44, 25.64, 0.18656, 4, 3, -16.97, 4.51, 0.4522, 4, -3.96, -47.62, 0.05511, 5, 16.63, 2, 0.47981, 6, 3.56, 59.95, 0.01288, 3, 3, -12.1, -27.09, 0.40663, 5, 21.5, -29.59, 0.18926, 6, 8.43, 28.36, 0.40411, 3, 3, 3.67, 14.08, 0.83676, 4, 16.69, -38.05, 0.12368, 5, 37.27, 11.57, 0.03956, 3, 3, 6.09, -9.17, 0.94565, 5, 39.7, -11.68, 0.00914, 6, 26.63, 46.27, 0.0452, 2, 3, 21.08, 1.85, 0.99764, 4, 34.1, -50.27, 0.00236, 2, 3, 37.75, 2.35, 0.99926, 4, 50.77, -49.77, 0.00074], "hull": 20, "edges": [0, 38, 0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 36, 38, 24, 26, 2, 4, 4, 6, 32, 34, 34, 36], "width": 117, "height": 97}}, "octopus": {"octopus": {"type": "mesh", "uvs": [0.62936, 0.00065, 0.69493, 0.05187, 0.73577, 0.11463, 0.75916, 0.20285, 0.75802, 0.28995, 0.74712, 0.32987, 0.79151, 0.33741, 0.84134, 0.37409, 0.87465, 0.41924, 0.89539, 0.46741, 0.92002, 0.51821, 0.94688, 0.56275, 0.98006, 0.62455, 1, 0.68085, 0.99999, 0.74285, 0.99163, 0.79844, 0.97297, 0.84893, 0.9441, 0.88943, 0.90494, 0.91686, 0.87065, 0.91662, 0.84689, 0.89222, 0.83275, 0.85637, 0.83527, 0.80612, 0.85039, 0.80172, 0.8722, 0.85336, 0.89352, 0.86288, 0.92227, 0.83981, 0.94582, 0.80356, 0.95326, 0.76291, 0.95008, 0.70453, 0.92277, 0.67747, 0.89203, 0.6284, 0.85411, 0.59141, 0.82362, 0.54418, 0.79858, 0.49657, 0.75322, 0.50975, 0.71678, 0.52074, 0.73264, 0.55589, 0.74206, 0.60716, 0.75966, 0.65403, 0.78172, 0.6826, 0.7966, 0.69432, 0.78693, 0.78916, 0.76844, 0.7844, 0.73274, 0.7573, 0.69878, 0.71299, 0.67424, 0.65806, 0.65887, 0.59398, 0.64276, 0.59837, 0.64276, 0.65074, 0.64995, 0.71116, 0.66036, 0.77561, 0.67994, 0.83493, 0.70052, 0.89279, 0.73175, 0.92026, 0.75729, 0.92209, 0.77018, 0.89902, 0.75654, 0.87375, 0.72903, 0.85837, 0.70745, 0.83355, 0.68813, 0.79831, 0.69234, 0.77488, 0.72456, 0.79831, 0.75213, 0.81805, 0.7853, 0.83676, 0.80733, 0.87381, 0.81499, 0.91306, 0.81037, 0.96114, 0.78209, 0.99539, 0.73898, 0.99999, 0.6965, 0.99121, 0.64917, 0.95434, 0.614, 0.90868, 0.59137, 0.8374, 0.56875, 0.76611, 0.56042, 0.6947, 0.55704, 0.62292, 0.53844, 0.62292, 0.53373, 0.69176, 0.51103, 0.76223, 0.48361, 0.803, 0.45091, 0.85161, 0.40059, 0.87249, 0.36265, 0.8963, 0.34543, 0.93358, 0.34242, 0.9892, 0.30502, 0.94814, 0.30241, 0.89544, 0.30599, 0.83923, 0.32559, 0.78557, 0.35788, 0.75586, 0.38837, 0.69288, 0.42283, 0.65406, 0.42047, 0.5872, 0.41366, 0.53504, 0.38639, 0.55409, 0.35615, 0.63209, 0.31446, 0.69972, 0.27166, 0.73918, 0.21815, 0.75385, 0.17755, 0.74999, 0.13118, 0.7138, 0.09981, 0.65442, 0.08026, 0.60103, 0.07829, 0.51321, 0.10173, 0.48565, 0.09889, 0.43259, 0.08207, 0.4155, 0.06435, 0.40743, 0.05798, 0.42951, 0, 0.46235, 0, 0.41452, 0.0059, 0.37274, 0.03731, 0.33758, 0.07408, 0.32557, 0.11205, 0.35037, 0.13522, 0.40279, 0.14926, 0.45395, 0.15566, 0.53784, 0.17373, 0.59009, 0.2122, 0.62369, 0.24879, 0.60653, 0.28986, 0.55628, 0.31721, 0.48733, 0.35007, 0.40068, 0.39898, 0.35114, 0.43977, 0.31135, 0.43525, 0.23328, 0.44427, 0.15156, 0.49707, 0.03914, 0.55895, 0.00193, 0.54102, 0.52921, 0.64571, 0.5177, 0.67784, 0.35297, 0.62915, 0.37024, 0.54102, 0.37311, 0.47869, 0.35009, 0.43925, 0.43785, 0.53782, 0.45492, 0.63715, 0.44054, 0.69858, 0.43933, 0.47613, 0.15727, 0.63205, 0.17781, 0.6427, 0.28576, 0.53145, 0.3369, 0.49063, 0.32554, 0.45601, 0.24031, 0.54254, 0.16602, 0.51423, 0.08289, 0.57298, 0.06984, 0.65668, 0.10444, 0.6943, 0.19349, 0.69218, 0.28989], "triangles": [112, 113, 108, 109, 111, 112, 108, 109, 112, 110, 111, 109, 108, 113, 114, 107, 108, 114, 115, 107, 114, 115, 106, 107, 116, 106, 115, 106, 116, 117, 105, 106, 117, 105, 117, 118, 118, 103, 104, 118, 104, 105, 103, 118, 119, 102, 103, 119, 101, 102, 119, 101, 119, 120, 100, 101, 120, 99, 100, 120, 121, 122, 97, 98, 121, 97, 120, 121, 98, 98, 99, 120, 137, 124, 125, 94, 124, 137, 95, 123, 124, 94, 95, 124, 122, 123, 95, 96, 122, 95, 97, 122, 96, 44, 45, 39, 44, 39, 40, 43, 44, 40, 43, 40, 41, 42, 43, 41, 132, 140, 36, 45, 38, 39, 47, 132, 36, 46, 47, 38, 45, 46, 38, 47, 37, 38, 47, 36, 37, 48, 132, 47, 21, 23, 24, 23, 21, 22, 20, 21, 24, 19, 20, 24, 19, 24, 25, 17, 18, 25, 19, 25, 18, 16, 27, 15, 26, 27, 16, 17, 26, 16, 17, 25, 26, 14, 29, 13, 28, 29, 14, 15, 28, 14, 27, 28, 15, 31, 32, 10, 31, 10, 11, 30, 31, 11, 12, 30, 11, 29, 30, 12, 13, 29, 12, 34, 7, 8, 34, 8, 9, 33, 34, 9, 32, 33, 9, 10, 32, 9, 34, 6, 7, 35, 6, 34, 36, 140, 35, 140, 5, 35, 35, 5, 6, 132, 139, 140, 139, 133, 140, 60, 61, 62, 59, 60, 62, 58, 62, 63, 59, 62, 58, 57, 63, 64, 58, 63, 57, 56, 57, 64, 56, 64, 65, 56, 65, 66, 67, 56, 66, 55, 56, 67, 68, 55, 67, 70, 53, 54, 69, 54, 55, 69, 55, 68, 70, 54, 69, 72, 73, 52, 71, 72, 52, 53, 71, 52, 70, 71, 53, 75, 49, 50, 74, 75, 50, 74, 50, 51, 73, 74, 51, 73, 51, 52, 83, 87, 88, 84, 87, 83, 86, 87, 84, 85, 86, 84, 81, 90, 91, 82, 90, 81, 89, 90, 82, 83, 88, 89, 89, 82, 83, 79, 92, 78, 80, 92, 79, 91, 92, 80, 80, 81, 91, 76, 48, 49, 75, 76, 49, 76, 131, 48, 139, 131, 138, 77, 131, 76, 131, 139, 132, 48, 131, 132, 131, 137, 138, 94, 131, 93, 137, 131, 94, 131, 92, 93, 77, 92, 131, 78, 92, 77, 135, 144, 134, 134, 139, 135, 135, 139, 138, 138, 136, 135, 137, 136, 138, 137, 125, 126, 140, 133, 5, 5, 152, 4, 135, 136, 144, 136, 145, 144, 136, 126, 145, 149, 130, 0, 148, 129, 130, 149, 148, 130, 150, 0, 1, 149, 0, 150, 141, 128, 129, 148, 141, 129, 147, 148, 149, 141, 148, 147, 142, 149, 150, 147, 149, 142, 2, 151, 150, 2, 150, 1, 142, 150, 151, 151, 2, 3, 146, 128, 141, 127, 128, 146, 143, 142, 151, 152, 143, 151, 152, 151, 3, 4, 152, 3, 126, 127, 146, 147, 146, 141, 145, 146, 147, 126, 146, 145, 144, 145, 147, 143, 144, 147, 143, 147, 142, 133, 143, 152, 134, 144, 143, 134, 143, 133, 133, 152, 5, 139, 134, 133, 137, 126, 136], "vertices": [1, 25, 61.84, 1.64, 1, 1, 25, 55.88, -16.7, 1, 1, 25, 46.83, -29.06, 1, 1, 25, 32.57, -37.72, 1, 3, 25, 17.42, -40.06, 0.9288, 24, 27.91, -45.18, 0.0712, 32, -31.38, 71.27, 0, 3, 25, 10.01, -38.47, 0.80481, 24, 20.5, -43.59, 0.19519, 32, -24.37, 68.4, 0, 4, 25, 10.69, -50.07, 0.32535, 32, -22.97, 79.93, 0, 43, 29.36, 13.64, 0.64435, 44, -14.06, 3.89, 0.03029, 4, 25, 6.55, -63.95, 0.0003, 42, 15.84, -83.48, 0, 43, 41.51, 5.78, 0.28821, 44, -0.54, 9.06, 0.71148, 3, 32, -8.45, 101.46, 0, 42, 4.49, -80.46, 0, 44, 11.17, 10.06, 1, 4, 32, 0.06, 106.81, 0, 42, -4.23, -75.46, 0, 44, 21.07, 8.29, 0.99941, 45, -10.04, 7.67, 0.00059, 4, 32, 9.04, 113.16, 0, 42, -14.05, -70.51, 0, 44, 31.98, 6.96, 0.3675, 45, 0.94, 7.03, 0.6325, 2, 32, 16.92, 120.1, 0, 45, 11.43, 7.54, 1, 2, 45, 25.31, 7.41, 1, 26, -62.92, 142.56, 0, 3, 45, 36.28, 5.22, 0.34324, 46, -0.98, 6.3, 0.65676, 26, -57.87, 152.53, 0, 2, 32, 48.69, 133.73, 0, 46, 9.89, 7.23, 1, 3, 32, 58.46, 131.5, 0, 46, 19.83, 5.91, 0.77358, 47, -4.9, 3.58, 0.22642, 3, 42, -53.28, -25.36, 0, 46, 29.1, 1.85, 0.02536, 47, 4.78, 6.57, 0.97464, 2, 42, -49.94, -15.56, 0, 47, 15.13, 6.45, 1, 2, 47, 25.79, 2.81, 0.13537, 48, 1.62, 5.61, 0.86463, 2, 48, 10.54, 5.72, 0.83643, 49, -5.52, 2.23, 0.16357, 2, 48, 16.78, 1.53, 0.18114, 49, 1.63, 4.57, 0.81886, 2, 42, -21.6, -7.27, 0, 49, 8.88, 3.7, 1, 1, 49, 15.55, -2.15, 1, 1, 49, 13.79, -5.75, 1, 3, 32, 67.95, 100.39, 0, 48, 10.32, -5.42, 0.36775, 49, 3.12, -4.8, 0.63225, 4, 32, 69.66, 105.92, 0, 47, 21.3, -6.07, 0.11254, 48, 4.75, -3.83, 0.86235, 49, -1.55, -8.21, 0.02511, 3, 32, 65.64, 113.42, 0, 47, 13.11, -3.77, 0.97916, 48, -2.66, -8.02, 0.02084, 2, 46, 21.75, -5.88, 0.19152, 47, 4.27, -4.07, 0.80848, 3, 42, -41.71, -36.39, 0, 46, 14.46, -4.57, 0.94328, 47, -2.1, -7.86, 0.05672, 3, 42, -36.2, -45.1, 0, 45, 31.36, -7.5, 0.39233, 46, 4.29, -6.28, 0.60767, 2, 45, 23.19, -10.03, 0.95302, 46, 0.16, -13.76, 0.04698, 2, 44, 41.34, -11.52, 0.06328, 45, 11.45, -10.82, 0.93672, 3, 43, 40.54, -32.6, 0.00067, 44, 29.87, -14.38, 0.60028, 45, 0.19, -14.4, 0.39905, 4, 43, 33.59, -23.45, 0.05704, 44, 18.39, -14.59, 0.89943, 45, -11.26, -15.34, 0.04209, 60, 17.27, 33.47, 0.00145, 3, 43, 28.06, -14.4, 0.418, 44, 7.81, -13.71, 0.55917, 60, 6.96, 30.97, 0.02283, 3, 43, 16.08, -15.39, 0.71069, 44, 1.54, -23.97, 0.09158, 60, 4.25, 19.26, 0.19773, 3, 43, 6.44, -16.25, 0.32921, 44, -3.45, -32.25, 0.00709, 60, 2.14, 9.82, 0.66369, 5, 43, 9.85, -22.86, 0.06627, 44, 3.9, -33.4, 0.00043, 60, 9.47, 11.05, 0.93131, 61, -7.43, 20.06, 0.00199, 26, -35.41, 83.17, 0, 4, 43, 11.28, -32.1, 0.00282, 60, 18.7, 9.59, 0.89172, 61, -1.12, 13.16, 0.10546, 26, -29.48, 90.4, 0, 2, 60, 28.1, 10.39, 0.27227, 61, 6.73, 7.92, 0.72773, 2, 60, 35.04, 13.56, 0.00423, 61, 14.13, 6.08, 0.99577, 1, 61, 18.51, 6.02, 1, 3, 42, -5.56, -12.21, 0, 61, 23.93, -9.97, 1, 26, -10.18, 118.5, 0, 3, 42, -0.91, -10.72, 0, 61, 19.27, -11.43, 1, 26, -8.07, 114.1, 0, 2, 42, 9.52, -10.63, 0, 61, 8.83, -11.44, 1, 2, 60, 31.09, -8.3, 0.44493, 61, -2.59, -8.55, 0.55507, 2, 36, 36.67, 17.86, 0.01294, 60, 19.66, -10.16, 0.98706, 2, 36, 24.83, 16.09, 0.26233, 60, 7.73, -9.19, 0.73767, 3, 36, 24.79, 11.83, 0.61943, 37, -11.71, 12.75, 0.00207, 60, 6.72, -13.33, 0.37851, 3, 36, 33.84, 10.07, 0.70478, 37, -2.82, 10.32, 0.23949, 60, 15.13, -17.1, 0.05573, 3, 36, 44.64, 9.88, 0.06654, 37, 7.93, 9.31, 0.93201, 60, 25.6, -19.75, 0.00144, 3, 37, 19.58, 8.92, 0.95378, 38, -5.5, 12.07, 0.04622, 26, 6.99, 90.33, 0, 3, 37, 31, 11.07, 0.23567, 38, 5.84, 9.54, 0.76433, 26, 12.53, 100.53, 0, 3, 38, 17.14, 7.37, 0.76033, 39, 0.78, 9.12, 0.23967, 42, 5.87, 14.38, 0, 4, 38, 25.98, 10.71, 0.06242, 39, 9.62, 5.8, 0.93461, 41, 9, 13.52, 0.00293, 42, -3.57, 14.89, 3e-05, 4, 39, 16.22, 6.66, 0.61854, 40, 5.02, 6.63, 0.25631, 41, 3.87, 9.3, 0.12475, 42, -9.6, 12.09, 0.0004, 4, 39, 18.79, 11.25, 0.10417, 40, 10.08, 5.18, 0.30559, 41, 4.11, 4.04, 0.58937, 42, -10.68, 6.94, 0.00086, 2, 41, 9.72, 3.13, 0.99938, 42, -5.48, 4.64, 0.00062, 3, 40, 12.36, 17.85, 6e-05, 41, 16.84, 5.92, 0.27549, 42, 2.12, 5.57, 0.72444, 2, 40, 14.12, 24.74, 0, 42, 9.11, 4.31, 1, 2, 40, 17.79, 31.83, 0, 42, 16.44, 1.15, 1, 1, 42, 17.39, -3.01, 1, 1, 42, 8.05, -3.25, 1, 2, 41, 17.15, -3.37, 0.51082, 42, 0.09, -3.5, 0.48918, 1, 41, 8.55, -6.72, 1, 2, 40, 18.01, -1.9, 0.286, 41, -0.07, -5.73, 0.714, 2, 40, 12.48, -6.48, 0.79447, 41, -6.19, -1.95, 0.20553, 2, 39, 31.02, 2.34, 0.00288, 40, 4.24, -8.77, 0.99712, 2, 39, 24.85, -4.89, 0.47181, 40, -4.23, -4.44, 0.52819, 1, 39, 13.96, -7.68, 1, 2, 38, 30.03, -4.25, 0.16787, 39, 2.82, -8.11, 0.83213, 2, 38, 17.28, -9.82, 0.97321, 39, -10.44, -3.91, 0.02679, 2, 37, 38.98, -8.9, 0.10502, 38, 5.3, -11.96, 0.89498, 2, 37, 25.33, -11.25, 0.86254, 38, -8.18, -8.73, 0.13746, 2, 36, 50.1, -12.69, 0.0199, 37, 11.67, -13.61, 0.9801, 4, 32, 39.57, 19.49, 0.0172, 36, 37.35, -12.42, 0.46426, 37, -1.02, -12.38, 0.51854, 42, 54.32, 0.42, 0, 4, 32, 26.93, 18.68, 0.25007, 36, 24.78, -10.87, 0.7394, 37, -13.44, -9.89, 0.01053, 42, 60.97, -10.36, 0, 5, 32, 26.9, 13.85, 0.61835, 36, 23.86, -15.62, 0.36873, 33, -16.72, 3.68, 0.01292, 42, 65.25, -8.11, 0, 26, 3.52, 48.91, 0, 3, 32, 39.01, 12.55, 0.55632, 36, 35.52, -19.13, 0.05167, 33, -6.8, 10.75, 0.39201, 3, 32, 51.38, 6.58, 0.03902, 36, 46.57, -27.29, 0.00032, 33, 6.41, 14.49, 0.96066, 2, 33, 16.51, 13.87, 0.94244, 34, -12.32, 10.73, 0.05756, 2, 33, 28.55, 13.12, 0.46989, 34, -0.59, 13.55, 0.53011, 3, 33, 39.94, 5.72, 0.00365, 34, 12.47, 9.8, 0.96552, 35, -0.25, 16.04, 0.03083, 2, 34, 23.07, 8.18, 0.46379, 35, 5.08, 6.75, 0.53621, 2, 34, 30.32, 11.41, 0.01846, 35, 12.13, 3.08, 0.98154, 1, 35, 21.94, 3.47, 1, 2, 35, 15.93, -7.04, 1, 42, 92.41, 70.77, 0, 2, 34, 36.31, -0.18, 0.01948, 35, 6.8, -8.82, 0.98052, 2, 34, 30.32, -8.11, 0.54989, 35, -3.14, -9.08, 0.45011, 3, 33, 41.31, -19.03, 0.00496, 34, 21.02, -13.47, 0.96687, 35, -13.12, -5.14, 0.02817, 3, 32, 50.03, -33.23, 0.00574, 33, 31.84, -16.17, 0.11942, 34, 11.13, -13.5, 0.87484, 5, 32, 38.99, -25.24, 0.10409, 33, 18.28, -17.53, 0.60201, 34, -1.44, -18.77, 0.29372, 42, 94.08, 20.92, 0, 26, 36.2, 24.3, 0.00018, 5, 32, 32.21, -16.24, 0.45716, 33, 7.23, -15.3, 0.49403, 34, -12.65, -19.87, 0.03669, 42, 89.32, 10.7, 0, 26, 25.44, 27.62, 0.01212, 5, 32, 20.44, -16.79, 0.78903, 33, -1.21, -23.52, 0.07475, 34, -18.31, -30.21, 0.00041, 42, 95.33, 0.57, 0, 26, 16.21, 20.29, 0.13581, 4, 32, 11.25, -18.51, 0.46755, 33, -6.93, -30.91, 0.00457, 42, 101.16, -6.74, 0, 26, 9.77, 13.52, 0.52788, 3, 32, 14.56, -25.62, 0.08376, 42, 105.88, -0.48, 0, 26, 16.61, 9.69, 0.91624, 4, 32, 28.25, -33.56, 0.00098, 42, 106.47, 15.33, 0, 26, 32.36, 11.26, 0.82855, 27, -9.43, 7.65, 0.17047, 2, 26, 48.34, 9.35, 0.04915, 27, 5.48, 13.74, 0.95085, 2, 27, 18.51, 15.22, 0.99715, 28, -18.15, -4.35, 0.00285, 2, 27, 32.18, 11.57, 0.63592, 28, -9.67, 6.98, 0.36408, 3, 42, 137.95, 55.28, 0, 27, 41.42, 6.42, 0.13405, 28, -1.45, 13.63, 0.86595, 3, 42, 151.59, 55.24, 0, 28, 11.73, 17.12, 0.99579, 29, -21.52, 5.06, 0.00421, 3, 42, 163.67, 49.77, 0, 28, 24.81, 14.97, 0.74799, 29, -9.83, 11.32, 0.25201, 3, 42, 172.53, 43.81, 0, 28, 34.92, 11.51, 0.23573, 29, 0.29, 14.74, 0.76427, 2, 42, 180.17, 30.36, 0, 29, 15.61, 12.62, 1, 2, 29, 19.35, 5.79, 0.99479, 30, -2.55, 13.84, 0.00521, 2, 29, 28.68, 4.93, 0.57638, 30, 2.06, 5.68, 0.42362, 3, 29, 32.39, 8.73, 0.06645, 30, 7.29, 4.8, 0.93337, 31, 0.79, 10.72, 0.00017, 2, 30, 12.07, 5.46, 0.59289, 31, 3.21, 6.55, 0.40711, 2, 30, 11.93, 9.68, 0.20187, 31, 7.07, 8.28, 0.79813, 1, 31, 22.06, 2.31, 1, 2, 42, 206.26, 24.43, 0, 31, 16.35, -3.88, 1, 2, 42, 208.32, 17.21, 0, 31, 10.24, -8.24, 1, 2, 30, 23.63, -2.72, 0.30126, 31, 0.04, -7.25, 0.69874, 2, 30, 15.86, -8.67, 0.89469, 31, -8.42, -2.32, 0.10531, 2, 29, 42.36, -0.9, 0.00075, 30, 5.07, -8.88, 0.99925, 2, 29, 32.25, -5.27, 0.74388, 30, -4.29, -3.06, 0.25612, 1, 29, 22.75, -7.34, 1, 2, 28, 28.05, -9.96, 0.13018, 29, 7.92, -6.47, 0.86982, 3, 27, 30.22, -19.42, 0.01822, 28, 18.36, -6.38, 0.88928, 29, -1.94, -9.54, 0.09251, 2, 27, 23.73, -9.78, 0.37128, 28, 7, -8.82, 0.62872, 3, 26, 44.9, -14.07, 0.01722, 27, 13.84, -8.41, 0.89503, 28, 2.05, -17.49, 0.08774, 2, 26, 31.5, -10.52, 0.6926, 27, 0.4, -11.81, 0.3074, 1, 26, 17.49, -11.77, 1, 3, 25, -19.99, 61.09, 0.05733, 24, -9.51, 55.97, 0.12632, 26, 0.12, -13.67, 0.81635, 3, 25, -9.22, 50.05, 0.28654, 24, 1.27, 44.94, 0.4566, 26, -14.37, -8.38, 0.25686, 2, 25, -0.5, 40.81, 0.70185, 24, 9.99, 35.69, 0.29815, 2, 25, 12.84, 44.33, 0.94802, 24, 23.32, 39.21, 0.05198, 1, 25, 27.41, 44.49, 1, 1, 25, 49.26, 34.36, 1, 1, 25, 58.47, 19.64, 1, 3, 24, -23.26, 3.18, 0.01538, 32, 10.41, 14.61, 0.41503, 36, 7.8, -11.82, 0.56959, 4, 25, -27.08, -18.17, 0.00096, 36, 11, 15.29, 0.40468, 43, -11.86, -13.66, 0.07704, 60, -5.92, -6.82, 0.51732, 3, 25, 2.91, -21.43, 0.71089, 24, 13.4, -26.55, 0.28911, 32, -20.4, 50.36, 0, 3, 25, -2.25, -9.48, 0.46066, 24, 8.23, -14.59, 0.53909, 60, -31.36, -0.12, 0.00025, 2, 25, -6.69, 13.01, 0.36022, 24, 3.8, 7.89, 0.63978, 2, 25, -5.48, 29.67, 0.57751, 24, 5.01, 24.55, 0.42249, 5, 25, -22.46, 37.12, 0.04198, 24, -11.97, 32, 0.11117, 32, -5.82, -11.76, 0.48285, 42, 103.22, -24.98, 0, 26, -8.02, 9, 0.36399, 3, 24, -10.53, 6.24, 0.48919, 32, -2.67, 13.85, 0.23364, 36, -5.19, -10.14, 0.27717, 5, 25, -14.08, -13.65, 0.25296, 24, -3.6, -18.77, 0.11107, 36, -2.75, 15.7, 0.31166, 43, -12.55, 0.09, 0.15814, 60, -19.22, -3.29, 0.16617, 4, 42, 43.38, -56.07, 0, 43, 3.34, -1.49, 0.82231, 44, -17.2, -26.04, 0.00039, 60, -12.87, 11.37, 0.1773, 1, 25, 27.84, 36.15, 1, 1, 25, 31.24, -4.4, 1, 1, 25, 13, -10.39, 1, 2, 25, -0.83, 16.55, 0.67807, 24, 9.65, 11.44, 0.32193, 2, 25, -0.69, 27.35, 0.71785, 24, 9.8, 22.24, 0.28215, 2, 25, 12.54, 38.8, 0.98552, 24, 23.03, 33.68, 0.01448, 1, 25, 29.29, 18.88, 1, 1, 25, 42.44, 28.64, 1, 1, 25, 47.32, 13.99, 1, 1, 25, 45.06, -8.49, 1, 1, 25, 31.3, -20.82, 1, 1, 25, 14.49, -23.19, 1], "hull": 131, "edges": [0, 260, 4, 6, 24, 26, 34, 36, 36, 38, 138, 140, 148, 150, 170, 172, 200, 202, 206, 208, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 252, 254, 254, 256, 256, 258, 258, 260, 194, 196, 194, 192, 192, 190, 190, 188, 188, 186, 186, 184, 184, 182, 182, 180, 176, 178, 180, 178, 172, 174, 174, 176, 166, 168, 168, 170, 162, 164, 164, 166, 160, 162, 158, 160, 158, 156, 156, 154, 154, 152, 152, 150, 144, 146, 146, 148, 140, 142, 142, 144, 134, 136, 136, 138, 132, 134, 124, 122, 122, 120, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 124, 126, 126, 128, 116, 118, 118, 120, 128, 130, 130, 132, 214, 216, 216, 218, 218, 220, 208, 210, 210, 212, 212, 214, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 284, 286, 286, 288, 288, 290, 290, 292, 292, 282, 282, 294, 294, 284, 202, 204, 204, 206, 196, 198, 198, 200], "width": 260, "height": 176}}, "octopus_A": {"octopus_A": {"type": "mesh", "uvs": [0.87847, 0, 0.94516, 0.05451, 0.97258, 0.1578, 1, 0.26109, 1, 0.43371, 1, 0.60628, 0.89286, 0.80563, 0.73471, 0.88701, 0.51512, 1, 0.24851, 1, 0, 0.97607, 0.06684, 0.56737, 0.30613, 0.63327, 0.47319, 0.6579, 0.61626, 0.60362, 0.70194, 0.52811, 0.78017, 0.41839, 0.78016, 0.31528, 0.78016, 0.21217, 0.78015, 0.09973, 0.78014, 0], "triangles": [17, 18, 3, 18, 2, 3, 2, 19, 1, 1, 19, 0, 19, 2, 18, 19, 20, 0, 6, 15, 5, 15, 16, 5, 16, 4, 5, 4, 17, 3, 4, 16, 17, 9, 13, 8, 7, 13, 14, 7, 8, 13, 9, 12, 13, 7, 14, 6, 14, 15, 6, 9, 10, 12, 10, 11, 12], "vertices": [1, 64, 10.93, -0.7, 1, 1, 64, 8.26, -3.01, 1, 2, 63, 22.03, -1.52, 0.00156, 64, 3.9, -3.4, 0.99844, 2, 63, 18.2, -3.64, 0.48345, 64, -0.47, -3.78, 0.51655, 1, 63, 11.34, -5.38, 1, 2, 62, 29.74, -0.16, 0.00113, 63, 4.48, -7.12, 0.99887, 2, 62, 22.91, -6.45, 0.81545, 63, -4.52, -4.86, 0.18455, 1, 62, 15.69, -7.48, 1, 2, 61, 39.55, -2.04, 0.00046, 62, 5.67, -8.91, 0.99954, 2, 61, 29.83, -7.05, 0.79121, 62, -4.66, -5.33, 0.20879, 1, 61, 20.32, -10.85, 1, 1, 61, 15.08, 5.31, 1, 2, 61, 25.04, 7.4, 0.63468, 62, 2.5, 8.1, 0.36532, 2, 61, 31.59, 9.64, 0.04873, 62, 8.64, 4.91, 0.95127, 2, 62, 14.91, 5.09, 0.88423, 63, 0.73, 8.16, 0.11577, 2, 62, 19.24, 6.86, 0.34848, 63, 4.59, 5.52, 0.65152, 3, 62, 23.74, 10.07, 0.0089, 63, 9.74, 3.51, 0.98539, 64, -5.29, 6.2, 0.0057, 2, 63, 13.83, 4.55, 0.76721, 64, -1.12, 5.48, 0.23279, 2, 63, 17.93, 5.59, 0.16574, 64, 3.05, 4.76, 0.83426, 2, 63, 22.4, 6.72, 0.00033, 64, 7.59, 3.97, 0.99967, 1, 64, 11.62, 3.28, 1], "hull": 21, "edges": [0, 40, 0, 2, 38, 40, 36, 38, 2, 4, 4, 6, 6, 8, 32, 34, 34, 36, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 20, 22], "width": 41, "height": 41}}, "octopus_B": {"octopus_B": {"type": "mesh", "uvs": [0.70169, 0.08548, 0.77783, 0.05481, 0.86157, 1e-05, 0.94802, 1e-05, 1, 0.05308, 1, 0.17569, 0.97241, 0.36069, 0.89841, 0.38876, 0.82008, 0.40513, 0.75498, 0.41376, 0.70134, 0.47855, 0.60238, 0.55289, 0.56243, 0.64064, 0.48733, 0.70006, 0.43352, 0.75741, 0.35325, 0.7954, 0.2892, 0.8275, 0.23697, 0.84993, 0.19055, 0.94272, 0.12864, 1, 0.07029, 1, 0.02394, 0.90819, 0, 0.7962, 0, 0.68402, 0, 0.57185, 0.0208, 0.52989, 0.04249, 0.53385, 0.05404, 0.59386, 0.06071, 0.69791, 0.08596, 0.75977, 0.12858, 0.7561, 0.17769, 0.70006, 0.22742, 0.59799, 0.26849, 0.53229, 0.34476, 0.49348, 0.42457, 0.43249, 0.51617, 0.28676, 0.60945, 0.14542], "triangles": [26, 23, 24, 22, 23, 28, 25, 26, 24, 27, 23, 26, 28, 23, 27, 29, 22, 28, 29, 21, 22, 20, 21, 29, 20, 29, 19, 18, 31, 17, 30, 31, 18, 19, 30, 18, 29, 30, 19, 15, 34, 14, 33, 34, 15, 16, 32, 33, 15, 16, 33, 17, 31, 32, 16, 17, 32, 11, 36, 37, 12, 35, 36, 11, 12, 36, 13, 35, 12, 14, 35, 13, 34, 35, 14, 3, 4, 5, 3, 5, 2, 5, 7, 2, 6, 7, 5, 8, 1, 2, 7, 8, 2, 9, 0, 1, 9, 1, 8, 10, 37, 0, 10, 0, 9, 10, 11, 37], "vertices": [1, 50, 14.03, -11.43, 1, 1, 50, 2.04, -9.83, 1, 1, 50, -11.66, -9.57, 1, 1, 50, -24.44, -5.29, 1, 1, 50, -30.87, 1.07, 1, 1, 50, -27.94, 9.78, 1, 1, 50, -19.45, 21.57, 1, 1, 50, -7.84, 19.9, 1, 1, 50, 4.14, 17.18, 1, 2, 50, 13.97, 14.56, 0.99636, 51, -17.42, 19.56, 0.00364, 2, 50, 23.45, 16.51, 0.9018, 51, -7.76, 19.07, 0.0982, 2, 50, 39.86, 16.89, 0.19705, 51, 8.22, 15.31, 0.80295, 2, 50, 47.86, 21.14, 0.01755, 51, 17.03, 17.43, 0.98245, 2, 51, 29.28, 14.77, 0.92343, 52, -9.6, 13.51, 0.07657, 2, 51, 38.66, 13.79, 0.51354, 52, -0.18, 13.91, 0.48646, 2, 51, 50.71, 9.34, 0.01621, 52, 12.39, 11.27, 0.98379, 2, 52, 22.47, 9.28, 0.98303, 53, -7.04, 10.14, 0.01697, 2, 52, 30.58, 7.41, 0.44138, 53, 0.8, 7.36, 0.55862, 2, 52, 40.06, 10.7, 0.00021, 53, 10.6, 9.57, 0.99979, 2, 53, 21.09, 8.25, 0.90212, 54, -8.1, 1.66, 0.09788, 2, 53, 28.88, 3.54, 0.17593, 54, -1.54, 7.97, 0.82407, 1, 54, 8.45, 8.01, 1, 2, 54, 16.96, 4.55, 0.46833, 55, -1.46, 4.89, 0.53167, 1, 55, 6.95, 4.52, 1, 1, 55, 15.35, 4.15, 1, 1, 55, 18.35, 0.77, 1, 1, 55, 17.91, -2.6, 1, 1, 55, 13.33, -4.2, 1, 3, 53, 18.44, -16.62, 0.01141, 54, 15.24, -7.33, 0.19381, 55, 5.49, -4.89, 0.79478, 3, 53, 17.47, -10.61, 0.21782, 54, 9.19, -6.72, 0.675, 55, 0.68, -8.62, 0.10718, 3, 53, 11.63, -7.41, 0.82943, 54, 4.59, -11.52, 0.16992, 55, 0.66, -15.28, 0.00066, 3, 52, 34.28, -6.67, 0.18563, 53, 2.9, -7.04, 0.81261, 54, 1.98, -19.86, 0.00176, 2, 52, 24.04, -10.38, 0.97454, 53, -7.69, -9.58, 0.02546, 1, 52, 16.15, -12.18, 1, 2, 51, 39.46, -10.35, 0.21093, 52, 4.13, -9.86, 0.78907, 2, 51, 26.53, -7.39, 0.98849, 52, -9.09, -8.81, 0.01151, 2, 50, 46.27, -6.31, 0.00722, 51, 8.6, -8.75, 0.99278, 2, 50, 29.1, -11.74, 0.9742, 51, -9.38, -9.69, 0.0258], "hull": 38, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 58, 60, 52, 54, 54, 56, 56, 58, 44, 46, 46, 48, 60, 62, 62, 64, 64, 66, 30, 32, 32, 34, 26, 28, 28, 30, 66, 68, 68, 70, 70, 72, 72, 74, 24, 26, 20, 22, 22, 24, 0, 74, 12, 14, 0, 2, 2, 4, 14, 16, 16, 18], "width": 156, "height": 75}}, "octopus_C": {"octopus_C": {"type": "mesh", "uvs": [0.18489, 0, 0.2629, 0, 0.35262, 0.08275, 0.43045, 0.1789, 0.50514, 0.29242, 0.56966, 0.4347, 0.6506, 0.5135, 0.75396, 0.59632, 0.88159, 0.62612, 1, 0.68385, 1, 0.88162, 0.90658, 0.99746, 0.80052, 0.98984, 0.69038, 0.95414, 0.54043, 0.85887, 0.42331, 0.73567, 0.35915, 0.57936, 0.30218, 0.42306, 0.27473, 0.27461, 0.22554, 0.18653, 0.15563, 0.15279, 0.07782, 0.1528, 0, 0.15282, 0, 0.06581, 0.05344, 0.03291, 0.10688, 0], "triangles": [20, 0, 19, 22, 23, 21, 23, 24, 21, 20, 21, 25, 21, 24, 25, 20, 25, 0, 4, 18, 3, 18, 2, 3, 18, 19, 2, 19, 1, 2, 19, 0, 1, 15, 5, 6, 15, 16, 5, 5, 17, 4, 5, 16, 17, 4, 17, 18, 11, 12, 10, 13, 7, 12, 12, 8, 10, 12, 7, 8, 13, 14, 7, 8, 9, 10, 14, 6, 7, 14, 15, 6], "vertices": [2, 58, 23.89, -1.31, 0.03864, 59, 2.21, -4.58, 0.96136, 2, 58, 20.12, -5.57, 0.75678, 59, -3.48, -4.5, 0.24322, 1, 58, 12.12, -7.24, 1, 2, 57, 26.92, -6.77, 0.07564, 58, 4.11, -7.73, 0.92436, 2, 57, 18.38, -8.12, 0.84392, 58, -4.52, -7.37, 0.15608, 2, 56, 26.84, -11.65, 0.08475, 57, 8.76, -7.96, 0.91525, 2, 56, 19.61, -9.57, 0.62473, 57, 1.77, -10.73, 0.37527, 2, 56, 10.78, -7.89, 0.98621, 57, -6.25, -14.79, 0.01379, 1, 56, 1.49, -9.78, 1, 1, 56, -7.8, -9.88, 1, 1, 56, -12.2, 0.92, 1, 1, 56, -8.46, 9.83, 1, 1, 56, -1.12, 12.33, 1, 1, 56, 7.12, 13.41, 1, 2, 56, 19.38, 12.33, 0.96988, 57, -11.79, 6.48, 0.03012, 2, 56, 30.04, 8.82, 0.33465, 57, -1.21, 10.21, 0.66535, 3, 56, 37.85, 2.05, 0.00029, 57, 9.12, 9.61, 0.99939, 58, -10.13, 11.83, 0.00031, 2, 57, 19.18, 8.57, 0.72007, 58, -0.47, 8.83, 0.27993, 2, 57, 27.76, 5.89, 0.02607, 58, 7.42, 4.52, 0.97393, 2, 58, 13.69, 3.77, 0.91809, 59, -0.6, 6.47, 0.08191, 2, 58, 18.56, 6.27, 0.17589, 59, 4.48, 4.41, 0.82411, 1, 59, 10.16, 4.33, 1, 1, 59, 15.84, 4.25, 1, 1, 59, 15.76, -0.88, 1, 1, 59, 11.84, -2.77, 1, 1, 59, 7.91, -4.66, 1], "hull": 26, "edges": [2, 4, 18, 20, 20, 22, 22, 24, 24, 26, 38, 40, 44, 46, 46, 48, 48, 50, 40, 42, 42, 44, 2, 0, 0, 50, 4, 6, 36, 38, 34, 36, 6, 8, 8, 10, 10, 12, 30, 32, 32, 34, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30], "width": 73, "height": 59}}, "water_up": {"water_up": {"type": "mesh", "uvs": [0.95895, 0, 0.97778, 1e-05, 0.9891, 0.06478, 0.99888, 0.12957, 1, 0.2216, 1, 0.34176, 0.98446, 0.45025, 0.96003, 0.54538, 0.9356, 0.64052, 0.90279, 0.72519, 0.85752, 0.79321, 0.82057, 0.82408, 0.78361, 0.85496, 0.72961, 0.89378, 0.68029, 0.92316, 0.63693, 0.94609, 0.59825, 0.95957, 0.55565, 0.97441, 0.50691, 0.98295, 0.45817, 0.99148, 0.40953, 1, 0.3881, 1, 0.34084, 0.99087, 0.29406, 0.95314, 0.24727, 0.9154, 0.20827, 0.86603, 0.16927, 0.81665, 0.12548, 0.74319, 0.0817, 0.66973, 0.04266, 0.5951, 0.02733, 0.50739, 0.01201, 0.41969, 0.00234, 0.34264, 0, 0.26441, 0.00939, 0.29381, 0.0292, 0.30819, 0.04064, 0.24333, 0.07063, 0.30824, 0.07497, 0.29356, 0.10745, 0.31525, 0.13992, 0.33695, 0.17473, 0.38962, 0.19254, 0.44004, 0.22336, 0.43863, 0.2424, 0.48067, 0.26899, 0.50847, 0.28113, 0.50887, 0.30401, 0.47658, 0.32, 0.47665, 0.34073, 0.51152, 0.36788, 0.54532, 0.39607, 0.57135, 0.42082, 0.58475, 0.4481, 0.58774, 0.45674, 0.56584, 0.45651, 0.51608, 0.48796, 0.49444, 0.5194, 0.4728, 0.56247, 0.47609, 0.60253, 0.50049, 0.62605, 0.46435, 0.63967, 0.4258, 0.67453, 0.43282, 0.69581, 0.38779, 0.72915, 0.38762, 0.7406, 0.37078, 0.74084, 0.34015, 0.75598, 0.31575, 0.77232, 0.31317, 0.80453, 0.25601, 0.8147, 0.21303, 0.83447, 0.18471, 0.85666, 0.09918, 0.88907, 0.05892, 0.91745, 0.03633, 0.92755, 0.01174, 0.938, 1e-05, 0.04364, 0.37546, 0.06352, 0.48595, 0.08422, 0.38891, 0.12156, 0.43535, 0.10289, 0.56016, 0.16765, 0.50211, 0.1484, 0.62546, 0.21958, 0.56016, 0.19449, 0.69658, 0.268, 0.61385, 0.24642, 0.77494, 0.31468, 0.60515, 0.30593, 0.73431, 0.29951, 0.85186, 0.36602, 0.66029, 0.35669, 0.86492, 0.36135, 0.76043, 0.4162, 0.69658, 0.41445, 0.86492, 0.45879, 0.70964, 0.45704, 0.86347, 0.49438, 0.59934, 0.50313, 0.86347, 0.49963, 0.72125, 0.54397, 0.58918, 0.54922, 0.71834, 0.55447, 0.84896, 0.59622, 0.59789, 0.59739, 0.71399, 0.60264, 0.84025, 0.64392, 0.55435, 0.65034, 0.69367, 0.65618, 0.82719, 0.7081, 0.78075, 0.70052, 0.669, 0.69177, 0.53984, 0.73739, 0.50646, 0.74965, 0.62256, 0.76248, 0.73141, 0.81907, 0.67771, 0.80157, 0.5558, 0.78349, 0.4397, 0.87318, 0.6188, 0.8475, 0.48383, 0.82942, 0.35757, 0.86496, 0.2788, 0.89296, 0.39345, 0.92272, 0.5081, 0.9478, 0.37748, 0.91863, 0.26574, 0.88888, 0.17721, 0.96211, 0.17815, 0.92199, 0.12273, 0.9583, 0.07969, 0.96693, 0.28102], "triangles": [130, 0, 1, 27, 83, 85, 32, 33, 34, 77, 31, 32, 130, 1, 2, 3, 128, 130, 131, 128, 4, 7, 124, 6, 9, 119, 8, 11, 116, 10, 12, 115, 11, 13, 110, 115, 13, 115, 12, 14, 109, 110, 15, 109, 14, 16, 103, 106, 16, 106, 15, 17, 103, 16, 18, 99, 17, 19, 99, 18, 21, 92, 95, 20, 21, 95, 22, 90, 92, 23, 90, 22, 23, 24, 90, 87, 25, 85, 25, 26, 85, 26, 27, 85, 27, 28, 83, 30, 31, 77, 29, 30, 78, 28, 29, 81, 25, 87, 24, 24, 87, 90, 22, 92, 21, 20, 95, 19, 19, 97, 99, 99, 103, 17, 15, 106, 109, 14, 110, 13, 115, 116, 11, 10, 116, 119, 10, 119, 9, 119, 124, 8, 8, 124, 7, 124, 125, 6, 6, 125, 5, 125, 131, 5, 131, 4, 5, 128, 3, 4, 3, 130, 2, 35, 32, 34, 77, 32, 35, 78, 30, 77, 29, 78, 81, 28, 81, 83, 95, 97, 19, 130, 76, 0, 130, 74, 75, 75, 76, 130, 73, 74, 129, 130, 129, 74, 129, 130, 128, 125, 126, 131, 126, 128, 131, 126, 129, 128, 126, 127, 129, 122, 127, 126, 122, 71, 127, 71, 72, 127, 127, 73, 129, 127, 72, 73, 68, 69, 121, 121, 69, 122, 122, 70, 71, 70, 122, 69, 120, 123, 124, 124, 123, 125, 120, 121, 123, 121, 122, 123, 123, 126, 125, 123, 122, 126, 116, 117, 119, 117, 120, 119, 119, 120, 124, 117, 118, 120, 118, 121, 120, 118, 65, 68, 65, 67, 68, 65, 118, 113, 67, 65, 66, 63, 64, 113, 113, 64, 65, 118, 68, 121, 111, 107, 112, 59, 60, 107, 60, 61, 107, 107, 62, 112, 107, 61, 62, 112, 63, 113, 112, 62, 63, 110, 111, 115, 111, 114, 115, 114, 116, 115, 116, 114, 117, 117, 114, 118, 111, 112, 114, 112, 113, 114, 118, 114, 113, 106, 105, 109, 105, 108, 109, 109, 108, 110, 108, 111, 110, 105, 104, 108, 104, 107, 108, 111, 108, 107, 102, 101, 104, 101, 58, 104, 104, 59, 107, 104, 58, 59, 101, 57, 58, 96, 98, 100, 100, 98, 101, 96, 53, 98, 98, 53, 54, 54, 56, 98, 98, 57, 101, 98, 56, 57, 54, 55, 56, 97, 100, 99, 99, 102, 103, 99, 100, 102, 103, 105, 106, 103, 102, 105, 100, 101, 102, 102, 104, 105, 92, 93, 95, 97, 95, 96, 95, 93, 94, 97, 96, 100, 96, 95, 94, 93, 91, 94, 94, 53, 96, 94, 52, 53, 91, 51, 94, 94, 51, 52, 91, 88, 50, 91, 50, 51, 92, 90, 93, 90, 87, 89, 90, 89, 93, 89, 87, 86, 89, 86, 88, 93, 89, 91, 89, 88, 91, 86, 45, 46, 88, 86, 46, 50, 88, 49, 46, 47, 88, 88, 48, 49, 88, 47, 48, 86, 84, 45, 45, 84, 44, 82, 42, 84, 84, 43, 44, 84, 42, 43, 87, 85, 86, 86, 85, 84, 85, 83, 84, 83, 82, 84, 82, 41, 42, 82, 80, 41, 79, 39, 80, 80, 40, 41, 80, 39, 40, 83, 81, 82, 81, 78, 80, 81, 80, 82, 80, 78, 79, 78, 77, 79, 77, 37, 79, 79, 37, 39, 39, 37, 38, 77, 35, 37, 37, 35, 36], "vertices": [3, 100, 12.4, 60, 0.04565, 90, 21.54, 9.98, 0.26399, 0, 311.31, 73.6, 0.69035, 1, 0, 324.38, 73.59, 1, 1, 0, 332.23, 55.52, 1, 1, 0, 339.02, 37.45, 1, 1, 0, 339.8, 11.77, 1, 1, 0, 339.8, -21.76, 1, 1, 0, 329.01, -52.02, 1, 1, 0, 312.06, -78.57, 1, 1, 0, 295.1, -105.11, 1, 1, 0, 272.33, -128.73, 1, 1, 0, 240.92, -147.71, 1, 1, 0, 215.27, -156.32, 1, 1, 0, 189.62, -164.94, 1, 1, 0, 152.14, -175.77, 1, 1, 0, 117.91, -183.97, 1, 1, 0, 87.83, -190.36, 1, 1, 0, 60.98, -194.12, 1, 1, 0, 31.42, -198.27, 1, 1, 0, -2.41, -200.65, 1, 1, 0, -36.23, -203.03, 1, 1, 0, -69.99, -205.4, 1, 1, 0, -84.87, -205.4, 1, 1, 0, -117.66, -202.86, 1, 1, 0, -150.13, -192.33, 1, 1, 0, -182.6, -181.8, 1, 1, 0, -209.66, -168.03, 1, 1, 0, -236.73, -154.25, 1, 1, 0, -267.12, -133.76, 1, 1, 0, -297.51, -113.26, 1, 1, 0, -324.6, -92.44, 1, 1, 0, -335.23, -67.97, 1, 1, 0, -345.87, -43.5, 1, 1, 0, -352.58, -22, 1, 1, 0, -354.2, -0.18, 1, 3, 79, -35.23, 4.43, 0.24322, 91, -62.16, 59.76, 0.01256, 0, -347.69, -8.38, 0.74422, 3, 79, -21.48, 0.42, 0.62673, 91, -48.41, 55.75, 0.02657, 0, -333.94, -12.39, 0.3467, 2, 79, -13.54, 18.52, 0.91476, 0, -326, 5.71, 0.08524, 3, 79, 7.27, 0.4, 0.9056, 91, -19.65, 55.74, 0.02923, 80, -53.05, 23.66, 0.06517, 3, 79, 10.29, 4.5, 0.85788, 91, -16.64, 59.83, 0.03695, 80, -50.04, 27.75, 0.10517, 3, 79, 32.82, -1.55, 0.43618, 91, 5.9, 53.78, 0.08687, 80, -27.5, 21.7, 0.47695, 3, 79, 55.36, -7.6, 0.08153, 91, 28.43, 47.73, 0.01295, 80, -4.96, 15.65, 0.90552, 4, 91, 52.59, 33.03, 0.00352, 80, 19.19, 0.95, 0.82411, 92, -16.02, 79.62, 0.01891, 81, -35.62, 23.61, 0.15346, 4, 91, 64.95, 18.97, 0.02514, 80, 31.55, -13.12, 0.39847, 92, -3.66, 65.55, 0.06033, 81, -23.26, 9.55, 0.51605, 2, 80, 52.94, -12.72, 0.04169, 81, -1.86, 9.94, 0.95831, 4, 92, 30.95, 54.21, 0.04492, 81, 11.35, -1.79, 0.83433, 82, -49.83, 6.86, 0.10004, 93, -31.73, 82.59, 0.02071, 4, 92, 49.4, 46.46, 0.07308, 81, 29.8, -9.55, 0.41334, 82, -31.38, -0.9, 0.44316, 93, -13.28, 74.83, 0.07042, 4, 92, 57.82, 46.35, 0.04763, 81, 38.22, -9.66, 0.23949, 82, -22.95, -1.01, 0.64501, 93, -4.86, 74.72, 0.06786, 3, 92, 73.7, 55.36, 0.00445, 81, 54.1, -0.65, 0.03247, 82, -7.08, 8, 0.96308, 2, 82, 4.02, 7.98, 0.99478, 83, -52.09, 34.93, 0.00522, 3, 82, 18.41, -1.75, 0.7481, 93, 36.5, 73.98, 0.02824, 83, -37.7, 25.2, 0.22366, 3, 82, 37.25, -11.18, 0.33228, 93, 55.35, 64.55, 0.03463, 83, -18.86, 15.77, 0.63308, 3, 82, 56.82, -18.44, 0.02342, 83, 0.71, 8.51, 0.97401, 84, -66.51, -14.31, 0.00257, 4, 83, 17.88, 4.77, 0.75045, 94, 4.42, 65.41, 0.07957, 95, -79.86, 60.33, 0.02398, 84, -49.34, -18.05, 0.146, 4, 83, 36.82, 3.94, 0.35771, 94, 23.36, 64.58, 0.11068, 95, -60.92, 59.49, 0.06272, 84, -30.4, -18.88, 0.46888, 4, 83, 42.81, 10.05, 0.18939, 94, 29.35, 70.69, 0.06492, 95, -54.93, 65.6, 0.04821, 84, -24.41, -12.77, 0.69748, 4, 83, 42.65, 23.93, 0.09142, 94, 29.19, 84.57, 0.02654, 95, -55.08, 79.49, 0.01241, 84, -24.56, 1.11, 0.86963, 1, 84, -2.74, 7.15, 1, 3, 95, -11.44, 91.56, 0.00461, 84, 19.08, 13.18, 0.68952, 85, -42.85, 11.34, 0.30587, 3, 95, 18.45, 90.64, 0.00158, 84, 48.97, 12.27, 0.16261, 85, -12.96, 10.42, 0.83581, 4, 95, 46.25, 83.84, 0.00244, 85, 14.84, 3.61, 0.71267, 96, -34.64, 69.44, 0.04845, 86, -42.47, -12.43, 0.23645, 3, 85, 31.17, 13.7, 0.28008, 96, -18.31, 79.52, 0.03716, 86, -26.14, -2.34, 0.68275, 3, 85, 40.61, 24.45, 0.10945, 96, -8.87, 90.28, 0.00729, 86, -16.69, 8.41, 0.88326, 3, 97, -56.67, 68.41, 0.01369, 86, 7.5, 6.45, 0.85882, 87, -48.25, -12.82, 0.12749, 3, 97, -41.9, 80.98, 0.02205, 86, 22.27, 19.02, 0.53401, 87, -33.48, -0.26, 0.44394, 3, 97, -18.76, 81.02, 0.01673, 86, 45.41, 19.07, 0.14823, 87, -10.34, -0.21, 0.83504, 3, 86, 53.36, 23.76, 0.00164, 87, -2.4, 4.49, 0.98549, 88, -50.08, -27.79, 0.01287, 4, 97, -10.65, 94.27, 2e-05, 87, -2.23, 13.03, 0.92124, 98, -67.82, 60.39, 0.00133, 88, -49.91, -19.24, 0.07742, 3, 87, 8.28, 19.84, 0.79796, 98, -57.31, 67.19, 0.00914, 88, -39.41, -12.43, 0.1929, 3, 87, 19.62, 20.56, 0.57842, 98, -45.98, 67.91, 0.02753, 88, -28.07, -11.71, 0.39405, 2, 87, 41.97, 36.51, 0.01678, 88, -5.71, 4.23, 0.98322, 4, 99, -66.96, 52.66, 0.00201, 88, 1.34, 16.23, 0.84124, 89, -35.18, -23.91, 0.15625, 100, -87.71, 0.56, 0.00049, 4, 99, -53.24, 60.56, 0.01895, 88, 15.06, 24.13, 0.50151, 89, -21.46, -16.01, 0.46983, 100, -73.99, 8.47, 0.00971, 2, 89, -6.06, 7.85, 0.99353, 90, -49.45, -17.69, 0.00647, 3, 89, 16.43, 19.09, 0.58708, 100, -36.1, 43.56, 0.02441, 90, -26.96, -6.46, 0.3885, 2, 89, 36.13, 25.39, 0.08768, 90, -7.26, -0.16, 0.91232, 2, 90, -0.25, 6.71, 0.94424, 0, 289.52, 70.32, 0.05576, 3, 100, -2.14, 60, 0.03087, 90, 7, 9.98, 0.73914, 0, 296.77, 73.59, 0.22999, 4, 79, -11.46, -18.35, 0.59212, 91, -38.39, 36.98, 0.14112, 80, -71.78, 4.9, 0.00158, 0, -323.92, -31.16, 0.26518, 5, 79, 2.34, -49.18, 0.20468, 91, -24.59, 6.16, 0.55311, 80, -57.98, -25.93, 0.00392, 92, -93.2, 52.74, 0.00057, 0, -310.12, -61.99, 0.23771, 3, 79, 16.71, -22.1, 0.49699, 91, -10.22, 33.23, 0.31891, 80, -43.62, 1.15, 0.1841, 5, 79, 42.62, -35.06, 0.06734, 91, 15.69, 20.27, 0.39163, 80, -17.71, -11.81, 0.52934, 92, -52.92, 66.86, 0.00807, 81, -72.52, 10.85, 0.00362, 5, 91, 2.73, -14.55, 0.73351, 80, -30.66, -46.63, 0.00791, 92, -65.87, 32.04, 0.09319, 81, -85.47, -23.97, 0.00485, 0, -282.8, -82.69, 0.16054, 4, 91, 47.68, 1.65, 0.17276, 80, 14.28, -30.43, 0.38377, 92, -20.93, 48.23, 0.18635, 81, -40.53, -7.77, 0.25712, 5, 91, 34.32, -32.77, 0.32824, 80, 0.92, -64.85, 0.05155, 92, -34.29, 13.82, 0.45766, 81, -53.89, -42.19, 0.04052, 0, -251.21, -100.91, 0.12203, 6, 91, 83.72, -14.55, 0.01122, 80, 50.32, -46.63, 0.0286, 92, 15.11, 32.04, 0.3294, 81, -4.49, -23.97, 0.54333, 82, -65.67, -15.32, 0.05781, 93, -47.57, 60.41, 0.02964, 4, 91, 66.3, -52.61, 0.00991, 92, -2.3, -6.02, 0.77814, 93, -64.98, 22.35, 0.0209, 0, -219.23, -120.75, 0.19105, 5, 92, 48.71, 17.06, 0.18306, 81, 29.12, -38.95, 0.22569, 82, -32.06, -30.3, 0.32092, 93, -13.97, 45.43, 0.26076, 83, -88.17, -3.35, 0.00958, 5, 92, 33.73, -27.89, 0.28767, 81, 14.13, -83.89, 0.00775, 82, -47.04, -75.25, 0.01001, 93, -28.95, 0.49, 0.47262, 0, -183.19, -142.61, 0.22195, 6, 92, 81.11, 19.49, 0.01747, 81, 61.51, -36.52, 0.04221, 82, 0.33, -27.87, 0.54749, 93, 18.43, 47.86, 0.2255, 83, -55.78, -0.92, 0.15548, 94, -69.24, 59.72, 0.01185, 6, 92, 75.03, -16.55, 0.00821, 81, 55.43, -72.56, 0.0114, 82, -5.74, -63.91, 0.10645, 93, 12.35, 11.82, 0.73997, 83, -61.85, -36.96, 0.07924, 94, -75.31, 23.68, 0.05473, 5, 92, 70.58, -49.35, 0.00785, 93, 7.9, -20.97, 0.54953, 83, -66.31, -69.75, 0.00436, 94, -79.77, -9.11, 0.09017, 0, -146.34, -164.07, 0.34809, 5, 81, 97.14, -51.91, 0.0001, 82, 35.96, -43.26, 0.13782, 93, 54.06, 32.47, 0.15681, 83, -20.15, -16.31, 0.57006, 94, -33.61, 44.33, 0.13521, 5, 82, 29.48, -100.35, 0.00223, 93, 47.58, -24.62, 0.30311, 83, -26.63, -73.4, 0.02694, 94, -40.09, -12.76, 0.38946, 0, -106.66, -167.72, 0.27826, 4, 82, 32.72, -71.2, 0.04391, 93, 50.82, 4.54, 0.32687, 83, -23.39, -44.25, 0.22926, 94, -36.85, 16.39, 0.39997, 6, 82, 70.78, -53.38, 0.00136, 93, 88.88, 22.35, 0.01463, 83, 14.67, -26.43, 0.47633, 94, 1.21, 34.21, 0.38423, 95, -83.06, 29.13, 0.03394, 84, -52.55, -49.25, 0.08951, 4, 93, 87.66, -24.62, 0.03152, 94, 0, -12.76, 0.7128, 95, -84.28, -17.84, 0.0335, 0, -66.58, -167.72, 0.22218, 5, 83, 44.23, -30.07, 0.20952, 94, 30.77, 30.57, 0.33851, 95, -53.51, 25.48, 0.21757, 84, -22.99, -52.89, 0.2321, 85, -84.92, -54.74, 0.0023, 5, 83, 43.02, -72.99, 0.00574, 94, 29.56, -12.35, 0.51093, 95, -54.72, -17.44, 0.25216, 84, -24.2, -95.81, 0.01567, 0, -37.02, -167.31, 0.2155, 5, 83, 68.93, 0.7, 0.06967, 94, 55.47, 61.34, 0.04751, 95, -28.81, 56.26, 0.14713, 84, 1.71, -22.12, 0.67434, 85, -60.22, -23.97, 0.06135, 6, 83, 75, -72.99, 0.00349, 94, 61.54, -12.35, 0.20856, 95, -22.73, -17.44, 0.55455, 84, 7.79, -95.81, 0.01132, 96, -103.62, -31.83, 0.0022, 0, -5.03, -167.31, 0.21988, 6, 83, 72.57, -33.31, 0.06434, 94, 59.11, 27.33, 0.1532, 95, -25.16, 22.25, 0.51292, 84, 5.36, -56.13, 0.2321, 85, -56.57, -57.98, 0.03744, 96, -106.05, 7.85, 0, 6, 83, 103.35, 3.53, 0.00311, 94, 89.89, 64.17, 0.00182, 95, 5.61, 59.09, 0.17381, 84, 36.13, -19.28, 0.32348, 85, -25.8, -21.13, 0.47085, 96, -75.28, 44.69, 0.02693, 7, 83, 106.99, -32.5, 0.00439, 94, 93.53, 28.14, 0.00222, 95, 9.25, 23.05, 0.62993, 84, 39.77, -55.32, 0.09722, 85, -22.16, -57.17, 0.17141, 96, -71.64, 8.66, 0.09467, 86, -79.46, -73.21, 0.00017, 5, 94, 97.17, -8.3, 0.01019, 95, 12.9, -13.39, 0.61808, 85, -18.51, -93.61, 0.00518, 96, -67.99, -27.79, 0.10632, 0, 30.6, -163.26, 0.26023, 6, 95, 41.87, 56.66, 0.10012, 84, 72.39, -21.71, 0.01717, 85, 10.46, -23.56, 0.59821, 96, -39.02, 42.26, 0.18757, 97, -111.01, 22.36, 2e-05, 86, -46.84, -39.6, 0.09692, 5, 95, 42.68, 24.27, 0.31024, 84, 73.2, -54.11, 0.01243, 85, 11.27, -55.95, 0.21524, 96, -38.21, 9.87, 0.44051, 86, -46.03, -71.99, 0.02158, 5, 95, 46.33, -10.96, 0.31077, 85, 14.92, -91.18, 0.0155, 96, -34.56, -25.36, 0.37403, 97, -106.56, -45.26, 3e-05, 0, 64.03, -160.83, 0.29966, 6, 95, 74.98, 68.81, 0.0046, 85, 43.57, -11.41, 0.2414, 96, -5.91, 54.41, 0.19902, 97, -77.91, 34.5, 0.02921, 86, -13.73, -27.45, 0.52243, 87, -69.49, -46.73, 0.00335, 6, 95, 79.43, 29.94, 0.01544, 85, 48.02, -50.28, 0.07952, 96, -1.46, 15.54, 0.75878, 97, -73.45, -4.37, 0.04148, 86, -9.28, -66.32, 0.10289, 87, -65.03, -85.6, 0.0019, 4, 95, 83.48, -7.31, 0.05407, 96, 2.59, -21.71, 0.54179, 97, -69.4, -41.62, 0.04374, 0, 101.18, -157.19, 0.36041, 6, 95, 119.52, 5.64, 3e-05, 96, 38.63, -8.76, 0.37195, 97, -33.37, -28.66, 0.34489, 86, 30.8, -90.62, 0.00763, 87, -24.95, -109.9, 0.00012, 0, 137.22, -144.23, 0.27538, 5, 85, 82.85, -43.4, 0.00844, 96, 33.36, 22.42, 0.38265, 97, -38.63, 2.52, 0.43327, 86, 25.54, -59.44, 0.14199, 87, -30.21, -78.72, 0.03365, 5, 85, 76.77, -7.36, 0.01613, 96, 27.29, 58.46, 0.11993, 97, -44.71, 38.55, 0.18624, 86, 19.47, -23.4, 0.528, 87, -36.29, -42.68, 0.1497, 6, 96, 58.96, 67.77, 0.0157, 97, -13.04, 47.87, 0.26302, 86, 51.13, -14.09, 0.1871, 87, -4.62, -33.37, 0.48315, 98, -70.21, 13.99, 0.04866, 88, -52.31, -65.64, 0.00238, 6, 96, 67.46, 35.38, 0.01722, 97, -4.54, 15.47, 0.78513, 86, 59.64, -46.48, 0.05922, 87, 3.88, -65.76, 0.10785, 98, -61.71, -18.41, 0.03027, 88, -43.81, -98.03, 0.00031, 4, 96, 76.37, 5.01, 0.04348, 97, 4.37, -14.89, 0.66098, 98, -52.8, -48.77, 0.0412, 0, 174.96, -130.47, 0.25435, 5, 97, 43.65, 0.09, 0.31241, 87, 52.07, -81.15, 0.00292, 98, -13.53, -33.79, 0.40861, 99, -63.92, -76.99, 0.00304, 0, 214.23, -115.49, 0.27301, 5, 97, 31.5, 34.1, 0.2527, 86, 95.67, -27.86, 0.00261, 87, 39.92, -47.14, 0.15261, 98, -25.67, 0.22, 0.56016, 88, -7.77, -79.41, 0.03193, 6, 97, 18.95, 66.49, 0.07205, 86, 83.12, 4.53, 0.00169, 87, 27.37, -14.74, 0.51647, 98, -38.23, 32.61, 0.22006, 99, -88.62, -10.59, 0.00783, 88, -20.32, -47.02, 0.1819, 4, 97, 81.19, 16.52, 0.04459, 98, 24.02, -17.36, 0.51785, 99, -26.38, -60.55, 0.13685, 0, 251.78, -99.05, 0.30071, 5, 87, 71.8, -27.06, 0.03907, 98, 6.2, 20.3, 0.63772, 99, -44.19, -22.9, 0.2024, 88, 24.11, -59.33, 0.12011, 89, -12.41, -99.47, 0.00071, 6, 87, 59.24, 8.17, 0.096, 98, -6.35, 55.53, 0.1674, 99, -56.75, 12.33, 0.13361, 88, 11.56, -24.1, 0.57768, 89, -24.96, -64.24, 0.02093, 100, -77.5, -39.76, 0.00439, 6, 87, 83.91, 30.15, 0.00018, 98, 18.32, 77.5, 0.02556, 99, -32.08, 34.31, 0.22253, 88, 36.22, -2.12, 0.40182, 89, -0.3, -42.26, 0.24966, 100, -52.83, -17.79, 0.10025, 6, 87, 103.34, -1.84, 0.00101, 98, 37.75, 45.52, 0.08828, 99, -12.65, 2.32, 0.77611, 88, 55.66, -34.11, 0.09148, 89, 19.14, -74.25, 0.02294, 100, -33.4, -49.77, 0.02018, 6, 97, 115.57, 47.41, 2e-05, 98, 58.4, 13.53, 0.14856, 99, 8, -29.67, 0.40162, 88, 76.31, -66.1, 9e-05, 100, -12.75, -81.76, 0.00588, 0, 286.16, -68.16, 0.44383, 5, 98, 75.81, 49.97, 0.00301, 99, 25.41, 6.77, 0.37753, 89, 57.2, -69.79, 6e-05, 100, 4.66, -45.32, 0.18044, 0, 303.57, -31.72, 0.43897, 4, 99, 5.17, 37.95, 0.26727, 88, 73.47, 1.52, 0.04584, 89, 36.95, -38.62, 0.10004, 100, -15.58, -14.14, 0.58684, 5, 99, -15.48, 62.65, 0.04871, 88, 52.82, 26.22, 0.06917, 89, 16.3, -13.92, 0.57797, 100, -36.23, 10.56, 0.23411, 90, -27.09, -39.46, 0.07004, 4, 99, 35.34, 62.39, 0.00479, 100, 14.59, 10.29, 0.45777, 90, 23.73, -39.72, 0.11087, 0, 313.5, 23.89, 0.42658, 4, 88, 75.8, 41.42, 0.00133, 89, 39.28, 1.28, 0.20163, 100, -13.25, 25.76, 0.3454, 90, -4.11, -24.26, 0.45163, 4, 89, 64.48, 13.29, 0.00101, 100, 11.95, 37.76, 0.1575, 90, 21.09, -12.25, 0.31802, 0, 310.86, 51.36, 0.52347, 4, 99, 38.69, 33.69, 0.11302, 100, 17.94, -18.41, 0.33386, 90, 27.08, -68.42, 0.00343, 0, 316.85, -4.81, 0.54969], "hull": 77, "edges": [6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 40, 42, 42, 44, 56, 58, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84, 84, 86, 90, 92, 92, 94, 94, 96, 106, 108, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 124, 126, 126, 128, 128, 130, 130, 132, 136, 138, 138, 140, 140, 142, 142, 144, 66, 68, 62, 64, 64, 66, 58, 60, 60, 62, 52, 54, 54, 56, 48, 50, 50, 52, 44, 46, 46, 48, 38, 40, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 22, 24, 12, 14, 14, 16, 2, 4, 4, 6, 0, 2, 150, 152, 144, 146, 146, 148, 148, 150, 132, 134, 134, 136, 122, 124, 110, 112, 112, 114, 104, 106, 102, 104, 96, 98, 98, 100, 100, 102, 86, 88, 88, 90, 76, 78, 78, 80, 152, 0], "width": 694, "height": 279}}}}], "animations": {"idle": {"bones": {"boat": {"translate": [{"y": -5.93, "curve": [0.114, 0, 0.224, 0, 0.114, -7.68, 0.224, -8.97]}, {"time": 0.3333, "y": -8.97, "curve": [0.778, 0, 1.222, 0, 0.778, -8.97, 1.222, 10.03]}, {"time": 1.6667, "y": 10.03, "curve": [2.111, 0, 2.556, 0, 2.111, 10.03, 2.556, -8.97]}, {"time": 3, "y": -8.97, "curve": [3.444, 0, 3.889, 0, 3.444, -8.97, 3.889, 10.03]}, {"time": 4.3333, "y": 10.03, "curve": [4.778, 0, 5.222, 0, 4.778, 10.03, 5.222, -8.97]}, {"time": 5.6667, "y": -8.97, "curve": [6.111, 0, 6.556, 0, 6.111, -8.97, 6.556, 10.03]}, {"time": 7, "y": 10.03, "curve": [7.444, 0, 7.889, 0, 7.444, 10.03, 7.889, -8.97]}, {"time": 8.3333, "y": -8.97, "curve": [8.778, 0, 9.222, 0, 8.778, -8.97, 9.222, 10.03]}, {"time": 9.6667, "y": 10.03, "curve": [10.001, 0, 10.336, 0, 10.001, 10.03, 10.336, -0.61]}, {"time": 10.6667, "y": -5.93}]}, "hat": {"translate": [{"x": 0.36, "curve": [0.225, -1.7, 0.446, -3.79, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -3.79, "curve": [1.111, -3.79, 1.556, 4.5, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 4.5, "curve": [2.444, 4.5, 2.889, -3.79, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -3.79, "curve": [3.778, -3.79, 4.222, 4.5, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 4.5, "curve": [5.111, 4.5, 5.556, -3.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -3.79, "curve": [6.444, -3.79, 6.889, 4.5, 6.444, 0, 6.889, 0]}, {"time": 7.3333, "x": 4.5, "curve": [7.778, 4.5, 8.222, -3.79, 7.778, 0, 8.222, 0]}, {"time": 8.6667, "x": -3.79, "curve": [9.111, -3.79, 9.556, 4.5, 9.111, 0, 9.556, 0]}, {"time": 10, "x": 4.5, "curve": [10.224, 4.5, 10.447, 2.44, 10.224, 0, 10.447, 0]}, {"time": 10.6667, "x": 0.36}]}, "hat2": {"translate": [{"x": 13.21, "y": 3.2, "curve": [0.444, 13.21, 0.889, -6.46, 0.444, 3.2, 0.889, -4.82]}, {"time": 1.3333, "x": -6.46, "y": -4.82, "curve": [1.778, -6.46, 2.222, 13.21, 1.778, -4.82, 2.222, 3.2]}, {"time": 2.6667, "x": 13.21, "y": 3.2, "curve": [3.111, 13.21, 3.556, -6.46, 3.111, 3.2, 3.556, -4.82]}, {"time": 4, "x": -6.46, "y": -4.82, "curve": [4.444, -6.46, 4.889, 13.21, 4.444, -4.82, 4.889, 3.2]}, {"time": 5.3333, "x": 13.21, "y": 3.2, "curve": [5.778, 13.21, 6.222, -6.46, 5.778, 3.2, 6.222, -4.82]}, {"time": 6.6667, "x": -6.46, "y": -4.82, "curve": [7.111, -6.46, 7.556, 13.21, 7.111, -4.82, 7.556, 3.2]}, {"time": 8, "x": 13.21, "y": 3.2, "curve": [8.444, 13.21, 8.889, -6.46, 8.444, 3.2, 8.889, -4.82]}, {"time": 9.3333, "x": -6.46, "y": -4.82, "curve": [9.778, -6.46, 10.222, 13.21, 9.778, -4.82, 10.222, 3.2]}, {"time": 10.6667, "x": 13.21, "y": 3.2}]}, "hat3": {"translate": [{"x": 6.62, "curve": [0.336, 1.63, 0.668, -8.23, 0.336, 0, 0.668, 0]}, {"time": 1, "x": -8.23, "curve": [1.444, -8.23, 1.889, 9.45, 1.444, 0, 1.889, 0]}, {"time": 2.3333, "x": 9.45, "curve": [2.778, 9.45, 3.222, -8.23, 2.778, 0, 3.222, 0]}, {"time": 3.6667, "x": -8.23, "curve": [4.111, -8.23, 4.556, 9.45, 4.111, 0, 4.556, 0]}, {"time": 5, "x": 9.45, "curve": [5.444, 9.45, 5.889, -8.23, 5.444, 0, 5.889, 0]}, {"time": 6.3333, "x": -8.23, "curve": [6.778, -8.23, 7.222, 9.45, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 9.45, "curve": [8.111, 9.45, 8.556, -8.23, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -8.23, "curve": [9.444, -8.23, 9.889, 9.45, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 9.45, "curve": [10.446, 9.45, 10.559, 8.32, 10.446, 0, 10.559, 0]}, {"time": 10.6667, "x": 6.62}]}, "hat4": {"translate": [{"x": 13.65, "y": -3.66, "curve": [0.444, 13.65, 0.889, -6.46, 0.444, -3.66, 0.889, 4.36]}, {"time": 1.3333, "x": -6.46, "y": 4.36, "curve": [1.778, -6.46, 2.222, 13.65, 1.778, 4.36, 2.222, -3.66]}, {"time": 2.6667, "x": 13.65, "y": -3.66, "curve": [3.111, 13.65, 3.556, -6.46, 3.111, -3.66, 3.556, 4.36]}, {"time": 4, "x": -6.46, "y": 4.36, "curve": [4.444, -6.46, 4.889, 13.65, 4.444, 4.36, 4.889, -3.66]}, {"time": 5.3333, "x": 13.65, "y": -3.66, "curve": [5.778, 13.65, 6.222, -6.46, 5.778, -3.66, 6.222, 4.36]}, {"time": 6.6667, "x": -6.46, "y": 4.36, "curve": [7.111, -6.46, 7.556, 13.65, 7.111, 4.36, 7.556, -3.66]}, {"time": 8, "x": 13.65, "y": -3.66, "curve": [8.444, 13.65, 8.889, -6.46, 8.444, -3.66, 8.889, 4.36]}, {"time": 9.3333, "x": -6.46, "y": 4.36, "curve": [9.778, -6.46, 10.222, 13.65, 9.778, 4.36, 10.222, -3.66]}, {"time": 10.6667, "x": 13.65, "y": -3.66}]}, "boat2": {"rotate": [{"value": -4.13, "curve": [0.222, -4.13, 0.444, 3.65]}, {"time": 0.6667, "value": 3.65, "curve": [0.889, 3.65, 1.111, -4.13]}, {"time": 1.3333, "value": -4.13, "curve": [1.556, -4.13, 1.778, 3.65]}, {"time": 2, "value": 3.65, "curve": [2.222, 3.65, 2.444, -4.13]}, {"time": 2.6667, "value": -4.13, "curve": [2.889, -4.13, 3.111, 3.65]}, {"time": 3.3333, "value": 3.65, "curve": [3.556, 3.65, 3.778, -4.13]}, {"time": 4, "value": -4.13, "curve": [4.222, -4.13, 4.444, 3.65]}, {"time": 4.6667, "value": 3.65, "curve": [4.889, 3.65, 5.111, -4.13]}, {"time": 5.3333, "value": -4.13, "curve": [5.556, -4.13, 5.778, 3.65]}, {"time": 6, "value": 3.65, "curve": [6.222, 3.65, 6.444, -4.13]}, {"time": 6.6667, "value": -4.13, "curve": [6.889, -4.13, 7.111, 3.65]}, {"time": 7.3333, "value": 3.65, "curve": [7.556, 3.65, 7.778, -4.13]}, {"time": 8, "value": -4.13, "curve": [8.222, -4.13, 8.444, 3.65]}, {"time": 8.6667, "value": 3.65, "curve": [8.889, 3.65, 9.111, -4.13]}, {"time": 9.3333, "value": -4.13, "curve": [9.556, -4.13, 9.778, 3.65]}, {"time": 10, "value": 3.65, "curve": [10.222, 3.65, 10.444, -4.13]}, {"time": 10.6667, "value": -4.13}], "translate": [{"x": -4.43, "curve": [0.222, -4.43, 0.444, 4.41, 0.222, 0, 0.444, 0]}, {"time": 0.6667, "x": 4.41, "curve": [0.889, 4.41, 1.111, -4.43, 0.889, 0, 1.111, 0]}, {"time": 1.3333, "x": -4.43, "curve": [1.556, -4.43, 1.778, 4.41, 1.556, 0, 1.778, 0]}, {"time": 2, "x": 4.41, "curve": [2.222, 4.41, 2.444, -4.43, 2.222, 0, 2.444, 0]}, {"time": 2.6667, "x": -4.43, "curve": [2.889, -4.43, 3.111, 4.41, 2.889, 0, 3.111, 0]}, {"time": 3.3333, "x": 4.41, "curve": [3.556, 4.41, 3.778, -4.43, 3.556, 0, 3.778, 0]}, {"time": 4, "x": -4.43, "curve": [4.222, -4.43, 4.444, 4.41, 4.222, 0, 4.444, 0]}, {"time": 4.6667, "x": 4.41, "curve": [4.889, 4.41, 5.111, -4.43, 4.889, 0, 5.111, 0]}, {"time": 5.3333, "x": -4.43, "curve": [5.556, -4.43, 5.778, 4.41, 5.556, 0, 5.778, 0]}, {"time": 6, "x": 4.41, "curve": [6.222, 4.41, 6.444, -4.43, 6.222, 0, 6.444, 0]}, {"time": 6.6667, "x": -4.43, "curve": [6.889, -4.43, 7.111, 4.41, 6.889, 0, 7.111, 0]}, {"time": 7.3333, "x": 4.41, "curve": [7.556, 4.41, 7.778, -4.43, 7.556, 0, 7.778, 0]}, {"time": 8, "x": -4.43, "curve": [8.222, -4.43, 8.444, 4.41, 8.222, 0, 8.444, 0]}, {"time": 8.6667, "x": 4.41, "curve": [8.889, 4.41, 9.111, -4.43, 8.889, 0, 9.111, 0]}, {"time": 9.3333, "x": -4.43, "curve": [9.556, -4.43, 9.778, 4.41, 9.556, 0, 9.778, 0]}, {"time": 10, "x": 4.41, "curve": [10.222, 4.41, 10.444, -4.43, 10.222, 0, 10.444, 0]}, {"time": 10.6667, "x": -4.43}]}, "boat3": {"rotate": [{"value": -2.88, "curve": [0.056, -3.61, 0.111, -4.13]}, {"time": 0.1667, "value": -4.13, "curve": [0.389, -4.13, 0.611, 3.65]}, {"time": 0.8333, "value": 3.65, "curve": [1.056, 3.65, 1.278, -4.13]}, {"time": 1.5, "value": -4.13, "curve": [1.722, -4.13, 1.944, 3.65]}, {"time": 2.1667, "value": 3.65, "curve": [2.389, 3.65, 2.611, -4.13]}, {"time": 2.8333, "value": -4.13, "curve": [3.056, -4.13, 3.278, 3.65]}, {"time": 3.5, "value": 3.65, "curve": [3.722, 3.65, 3.944, -4.13]}, {"time": 4.1667, "value": -4.13, "curve": [4.389, -4.13, 4.611, 3.65]}, {"time": 4.8333, "value": 3.65, "curve": [5.056, 3.65, 5.278, -4.13]}, {"time": 5.5, "value": -4.13, "curve": [5.722, -4.13, 5.944, 3.65]}, {"time": 6.1667, "value": 3.65, "curve": [6.389, 3.65, 6.611, -4.13]}, {"time": 6.8333, "value": -4.13, "curve": [7.056, -4.13, 7.278, 3.65]}, {"time": 7.5, "value": 3.65, "curve": [7.722, 3.65, 7.944, -4.13]}, {"time": 8.1667, "value": -4.13, "curve": [8.389, -4.13, 8.611, 3.65]}, {"time": 8.8333, "value": 3.65, "curve": [9.056, 3.65, 9.278, -4.13]}, {"time": 9.5, "value": -4.13, "curve": [9.722, -4.13, 9.944, 3.65]}, {"time": 10.1667, "value": 3.65, "curve": [10.333, 3.65, 10.5, -0.7]}, {"time": 10.6667, "value": -2.88}], "translate": [{"x": -4.78, "y": 1.15, "curve": [0.056, -5.93, 0.111, -6.75, 0.056, 1.28, 0.111, 1.37]}, {"time": 0.1667, "x": -6.75, "y": 1.37, "curve": [0.389, -6.75, 0.611, 5.51, 0.389, 1.37, 0.611, 0]}, {"time": 0.8333, "x": 5.51, "curve": [1.056, 5.51, 1.278, -6.75, 1.056, 0, 1.278, 1.37]}, {"time": 1.5, "x": -6.75, "y": 1.37, "curve": [1.722, -6.75, 1.944, 5.51, 1.722, 1.37, 1.944, 0]}, {"time": 2.1667, "x": 5.51, "curve": [2.389, 5.51, 2.611, -6.75, 2.389, 0, 2.611, 1.37]}, {"time": 2.8333, "x": -6.75, "y": 1.37, "curve": [3.056, -6.75, 3.278, 5.51, 3.056, 1.37, 3.278, 0]}, {"time": 3.5, "x": 5.51, "curve": [3.722, 5.51, 3.944, -6.75, 3.722, 0, 3.944, 1.37]}, {"time": 4.1667, "x": -6.75, "y": 1.37, "curve": [4.389, -6.75, 4.611, 5.51, 4.389, 1.37, 4.611, 0]}, {"time": 4.8333, "x": 5.51, "curve": [5.056, 5.51, 5.278, -6.75, 5.056, 0, 5.278, 1.37]}, {"time": 5.5, "x": -6.75, "y": 1.37, "curve": [5.722, -6.75, 5.944, 5.51, 5.722, 1.37, 5.944, 0]}, {"time": 6.1667, "x": 5.51, "curve": [6.389, 5.51, 6.611, -6.75, 6.389, 0, 6.611, 1.37]}, {"time": 6.8333, "x": -6.75, "y": 1.37, "curve": [7.056, -6.75, 7.278, 5.51, 7.056, 1.37, 7.278, 0]}, {"time": 7.5, "x": 5.51, "curve": [7.722, 5.51, 7.944, -6.75, 7.722, 0, 7.944, 1.37]}, {"time": 8.1667, "x": -6.75, "y": 1.37, "curve": [8.389, -6.75, 8.611, 5.51, 8.389, 1.37, 8.611, 0]}, {"time": 8.8333, "x": 5.51, "curve": [9.056, 5.51, 9.278, -6.75, 9.056, 0, 9.278, 1.37]}, {"time": 9.5, "x": -6.75, "y": 1.37, "curve": [9.722, -6.75, 9.944, 5.51, 9.722, 1.37, 9.944, 0]}, {"time": 10.1667, "x": 5.51, "curve": [10.333, 5.51, 10.5, -1.34, 10.333, 0, 10.5, 0.77]}, {"time": 10.6667, "x": -4.78, "y": 1.15}]}, "boat4": {"rotate": [{"value": -0.24, "curve": [0.111, -2.18, 0.222, -4.13]}, {"time": 0.3333, "value": -4.13, "curve": [0.556, -4.13, 0.778, 3.65]}, {"time": 1, "value": 3.65, "curve": [1.222, 3.65, 1.444, -4.13]}, {"time": 1.6667, "value": -4.13, "curve": [1.889, -4.13, 2.111, 3.65]}, {"time": 2.3333, "value": 3.65, "curve": [2.556, 3.65, 2.778, -4.13]}, {"time": 3, "value": -4.13, "curve": [3.222, -4.13, 3.444, 3.65]}, {"time": 3.6667, "value": 3.65, "curve": [3.889, 3.65, 4.111, -4.13]}, {"time": 4.3333, "value": -4.13, "curve": [4.556, -4.13, 4.778, 3.65]}, {"time": 5, "value": 3.65, "curve": [5.222, 3.65, 5.444, -4.13]}, {"time": 5.6667, "value": -4.13, "curve": [5.889, -4.13, 6.111, 3.65]}, {"time": 6.3333, "value": 3.65, "curve": [6.556, 3.65, 6.778, -4.13]}, {"time": 7, "value": -4.13, "curve": [7.222, -4.13, 7.444, 3.65]}, {"time": 7.6667, "value": 3.65, "curve": [7.889, 3.65, 8.111, -4.13]}, {"time": 8.3333, "value": -4.13, "curve": [8.556, -4.13, 8.778, 3.65]}, {"time": 9, "value": 3.65, "curve": [9.222, 3.65, 9.444, -4.13]}, {"time": 9.6667, "value": -4.13, "curve": [9.889, -4.13, 10.111, 3.65]}, {"time": 10.3333, "value": 3.65, "curve": [10.444, 3.65, 10.556, 1.7]}, {"time": 10.6667, "value": -0.24}], "translate": [{"x": 0.85, "y": 1.15, "curve": [0.111, -1.89, 0.222, -4.64, 0.111, 2.26, 0.222, 3.37]}, {"time": 0.3333, "x": -4.64, "y": 3.37, "curve": [0.556, -4.64, 0.778, 6.34, 0.556, 3.37, 0.778, -1.08]}, {"time": 1, "x": 6.34, "y": -1.08, "curve": [1.222, 6.34, 1.444, -4.64, 1.222, -1.08, 1.444, 3.37]}, {"time": 1.6667, "x": -4.64, "y": 3.37, "curve": [1.889, -4.64, 2.111, 6.34, 1.889, 3.37, 2.111, -1.08]}, {"time": 2.3333, "x": 6.34, "y": -1.08, "curve": [2.556, 6.34, 2.778, -4.64, 2.556, -1.08, 2.778, 3.37]}, {"time": 3, "x": -4.64, "y": 3.37, "curve": [3.222, -4.64, 3.444, 6.34, 3.222, 3.37, 3.444, -1.08]}, {"time": 3.6667, "x": 6.34, "y": -1.08, "curve": [3.889, 6.34, 4.111, -4.64, 3.889, -1.08, 4.111, 3.37]}, {"time": 4.3333, "x": -4.64, "y": 3.37, "curve": [4.556, -4.64, 4.778, 6.34, 4.556, 3.37, 4.778, -1.08]}, {"time": 5, "x": 6.34, "y": -1.08, "curve": [5.222, 6.34, 5.444, -4.64, 5.222, -1.08, 5.444, 3.37]}, {"time": 5.6667, "x": -4.64, "y": 3.37, "curve": [5.889, -4.64, 6.111, 6.34, 5.889, 3.37, 6.111, -1.08]}, {"time": 6.3333, "x": 6.34, "y": -1.08, "curve": [6.556, 6.34, 6.778, -4.64, 6.556, -1.08, 6.778, 3.37]}, {"time": 7, "x": -4.64, "y": 3.37, "curve": [7.222, -4.64, 7.444, 6.34, 7.222, 3.37, 7.444, -1.08]}, {"time": 7.6667, "x": 6.34, "y": -1.08, "curve": [7.889, 6.34, 8.111, -4.64, 7.889, -1.08, 8.111, 3.37]}, {"time": 8.3333, "x": -4.64, "y": 3.37, "curve": [8.556, -4.64, 8.778, 6.34, 8.556, 3.37, 8.778, -1.08]}, {"time": 9, "x": 6.34, "y": -1.08, "curve": [9.222, 6.34, 9.444, -4.64, 9.222, -1.08, 9.444, 3.37]}, {"time": 9.6667, "x": -4.64, "y": 3.37, "curve": [9.889, -4.64, 10.111, 6.34, 9.889, 3.37, 10.111, -1.08]}, {"time": 10.3333, "x": 6.34, "y": -1.08, "curve": [10.444, 6.34, 10.556, 3.59, 10.444, -1.08, 10.556, 0.04]}, {"time": 10.6667, "x": 0.85, "y": 1.15}]}, "boat5": {"rotate": [{"value": 2.4, "curve": [0.167, 0.22, 0.333, -4.13]}, {"time": 0.5, "value": -4.13, "curve": [0.722, -4.13, 0.944, 3.65]}, {"time": 1.1667, "value": 3.65, "curve": [1.389, 3.65, 1.611, -4.13]}, {"time": 1.8333, "value": -4.13, "curve": [2.056, -4.13, 2.278, 3.65]}, {"time": 2.5, "value": 3.65, "curve": [2.722, 3.65, 2.944, -4.13]}, {"time": 3.1667, "value": -4.13, "curve": [3.389, -4.13, 3.611, 3.65]}, {"time": 3.8333, "value": 3.65, "curve": [4.056, 3.65, 4.278, -4.13]}, {"time": 4.5, "value": -4.13, "curve": [4.722, -4.13, 4.944, 3.65]}, {"time": 5.1667, "value": 3.65, "curve": [5.389, 3.65, 5.611, -4.13]}, {"time": 5.8333, "value": -4.13, "curve": [6.056, -4.13, 6.278, 3.65]}, {"time": 6.5, "value": 3.65, "curve": [6.722, 3.65, 6.944, -4.13]}, {"time": 7.1667, "value": -4.13, "curve": [7.389, -4.13, 7.611, 3.65]}, {"time": 7.8333, "value": 3.65, "curve": [8.056, 3.65, 8.278, -4.13]}, {"time": 8.5, "value": -4.13, "curve": [8.722, -4.13, 8.944, 3.65]}, {"time": 9.1667, "value": 3.65, "curve": [9.389, 3.65, 9.611, -4.13]}, {"time": 9.8333, "value": -4.13, "curve": [10.056, -4.13, 10.278, 3.65]}, {"time": 10.5, "value": 3.65, "curve": [10.556, 3.65, 10.611, 3.13]}, {"time": 10.6667, "value": 2.4}], "translate": [{"x": 4.28, "y": -2.02, "curve": [0.167, 1.08, 0.333, -5.27, 0.167, -0.5, 0.333, 2.53]}, {"time": 0.5, "x": -5.27, "y": 2.53, "curve": [0.722, -5.27, 0.944, 6.1, 0.722, 2.53, 0.944, -2.89]}, {"time": 1.1667, "x": 6.1, "y": -2.89, "curve": [1.389, 6.1, 1.611, -5.27, 1.389, -2.89, 1.611, 2.53]}, {"time": 1.8333, "x": -5.27, "y": 2.53, "curve": [2.056, -5.27, 2.278, 6.1, 2.056, 2.53, 2.278, -2.89]}, {"time": 2.5, "x": 6.1, "y": -2.89, "curve": [2.722, 6.1, 2.944, -5.27, 2.722, -2.89, 2.944, 2.53]}, {"time": 3.1667, "x": -5.27, "y": 2.53, "curve": [3.389, -5.27, 3.611, 6.1, 3.389, 2.53, 3.611, -2.89]}, {"time": 3.8333, "x": 6.1, "y": -2.89, "curve": [4.056, 6.1, 4.278, -5.27, 4.056, -2.89, 4.278, 2.53]}, {"time": 4.5, "x": -5.27, "y": 2.53, "curve": [4.722, -5.27, 4.944, 6.1, 4.722, 2.53, 4.944, -2.89]}, {"time": 5.1667, "x": 6.1, "y": -2.89, "curve": [5.389, 6.1, 5.611, -5.27, 5.389, -2.89, 5.611, 2.53]}, {"time": 5.8333, "x": -5.27, "y": 2.53, "curve": [6.056, -5.27, 6.278, 6.1, 6.056, 2.53, 6.278, -2.89]}, {"time": 6.5, "x": 6.1, "y": -2.89, "curve": [6.722, 6.1, 6.944, -5.27, 6.722, -2.89, 6.944, 2.53]}, {"time": 7.1667, "x": -5.27, "y": 2.53, "curve": [7.389, -5.27, 7.611, 6.1, 7.389, 2.53, 7.611, -2.89]}, {"time": 7.8333, "x": 6.1, "y": -2.89, "curve": [8.056, 6.1, 8.278, -5.27, 8.056, -2.89, 8.278, 2.53]}, {"time": 8.5, "x": -5.27, "y": 2.53, "curve": [8.722, -5.27, 8.944, 6.1, 8.722, 2.53, 8.944, -2.89]}, {"time": 9.1667, "x": 6.1, "y": -2.89, "curve": [9.389, 6.1, 9.611, -5.27, 9.389, -2.89, 9.611, 2.53]}, {"time": 9.8333, "x": -5.27, "y": 2.53, "curve": [10.056, -5.27, 10.278, 6.1, 10.056, 2.53, 10.278, -2.89]}, {"time": 10.5, "x": 6.1, "y": -2.89, "curve": [10.556, 6.1, 10.611, 5.35, 10.556, -2.89, 10.611, -2.53]}, {"time": 10.6667, "x": 4.28, "y": -2.02}]}, "boat6": {"rotate": [{"value": 3.65, "curve": [0.222, 3.65, 0.444, -4.13]}, {"time": 0.6667, "value": -4.13, "curve": [0.889, -4.13, 1.111, 3.65]}, {"time": 1.3333, "value": 3.65, "curve": [1.556, 3.65, 1.778, -4.13]}, {"time": 2, "value": -4.13, "curve": [2.222, -4.13, 2.444, 3.65]}, {"time": 2.6667, "value": 3.65, "curve": [2.889, 3.65, 3.111, -4.13]}, {"time": 3.3333, "value": -4.13, "curve": [3.556, -4.13, 3.778, 3.65]}, {"time": 4, "value": 3.65, "curve": [4.222, 3.65, 4.444, -4.13]}, {"time": 4.6667, "value": -4.13, "curve": [4.889, -4.13, 5.111, 3.65]}, {"time": 5.3333, "value": 3.65, "curve": [5.556, 3.65, 5.778, -4.13]}, {"time": 6, "value": -4.13, "curve": [6.222, -4.13, 6.444, 3.65]}, {"time": 6.6667, "value": 3.65, "curve": [6.889, 3.65, 7.111, -4.13]}, {"time": 7.3333, "value": -4.13, "curve": [7.556, -4.13, 7.778, 3.65]}, {"time": 8, "value": 3.65, "curve": [8.222, 3.65, 8.444, -4.13]}, {"time": 8.6667, "value": -4.13, "curve": [8.889, -4.13, 9.111, 3.65]}, {"time": 9.3333, "value": 3.65, "curve": [9.556, 3.65, 9.778, -4.13]}, {"time": 10, "value": -4.13, "curve": [10.222, -4.13, 10.444, 3.65]}, {"time": 10.6667, "value": 3.65}], "translate": [{"x": 5.99, "y": -4.44, "curve": [0.222, 5.99, 0.444, -4.95, 0.222, -4.44, 0.444, 3.16]}, {"time": 0.6667, "x": -4.95, "y": 3.16, "curve": [0.889, -4.95, 1.111, 5.99, 0.889, 3.16, 1.111, -4.44]}, {"time": 1.3333, "x": 5.99, "y": -4.44, "curve": [1.556, 5.99, 1.778, -4.95, 1.556, -4.44, 1.778, 3.16]}, {"time": 2, "x": -4.95, "y": 3.16, "curve": [2.222, -4.95, 2.444, 5.99, 2.222, 3.16, 2.444, -4.44]}, {"time": 2.6667, "x": 5.99, "y": -4.44, "curve": [2.889, 5.99, 3.111, -4.95, 2.889, -4.44, 3.111, 3.16]}, {"time": 3.3333, "x": -4.95, "y": 3.16, "curve": [3.556, -4.95, 3.778, 5.99, 3.556, 3.16, 3.778, -4.44]}, {"time": 4, "x": 5.99, "y": -4.44, "curve": [4.222, 5.99, 4.444, -4.95, 4.222, -4.44, 4.444, 3.16]}, {"time": 4.6667, "x": -4.95, "y": 3.16, "curve": [4.889, -4.95, 5.111, 5.99, 4.889, 3.16, 5.111, -4.44]}, {"time": 5.3333, "x": 5.99, "y": -4.44, "curve": [5.556, 5.99, 5.778, -4.95, 5.556, -4.44, 5.778, 3.16]}, {"time": 6, "x": -4.95, "y": 3.16, "curve": [6.222, -4.95, 6.444, 5.99, 6.222, 3.16, 6.444, -4.44]}, {"time": 6.6667, "x": 5.99, "y": -4.44, "curve": [6.889, 5.99, 7.111, -4.95, 6.889, -4.44, 7.111, 3.16]}, {"time": 7.3333, "x": -4.95, "y": 3.16, "curve": [7.556, -4.95, 7.778, 5.99, 7.556, 3.16, 7.778, -4.44]}, {"time": 8, "x": 5.99, "y": -4.44, "curve": [8.222, 5.99, 8.444, -4.95, 8.222, -4.44, 8.444, 3.16]}, {"time": 8.6667, "x": -4.95, "y": 3.16, "curve": [8.889, -4.95, 9.111, 5.99, 8.889, 3.16, 9.111, -4.44]}, {"time": 9.3333, "x": 5.99, "y": -4.44, "curve": [9.556, 5.99, 9.778, -4.95, 9.556, -4.44, 9.778, 3.16]}, {"time": 10, "x": -4.95, "y": 3.16, "curve": [10.222, -4.95, 10.444, 5.99, 10.222, 3.16, 10.444, -4.44]}, {"time": 10.6667, "x": 5.99, "y": -4.44}]}, "boat7": {"rotate": [{"value": 2.4, "curve": [0.056, 3.13, 0.111, 3.65]}, {"time": 0.1667, "value": 3.65, "curve": [0.389, 3.65, 0.611, -4.13]}, {"time": 0.8333, "value": -4.13, "curve": [1.056, -4.13, 1.278, 3.65]}, {"time": 1.5, "value": 3.65, "curve": [1.722, 3.65, 1.944, -4.13]}, {"time": 2.1667, "value": -4.13, "curve": [2.389, -4.13, 2.611, 3.65]}, {"time": 2.8333, "value": 3.65, "curve": [3.056, 3.65, 3.278, -4.13]}, {"time": 3.5, "value": -4.13, "curve": [3.722, -4.13, 3.944, 3.65]}, {"time": 4.1667, "value": 3.65, "curve": [4.389, 3.65, 4.611, -4.13]}, {"time": 4.8333, "value": -4.13, "curve": [5.056, -4.13, 5.278, 3.65]}, {"time": 5.5, "value": 3.65, "curve": [5.722, 3.65, 5.944, -4.13]}, {"time": 6.1667, "value": -4.13, "curve": [6.389, -4.13, 6.611, 3.65]}, {"time": 6.8333, "value": 3.65, "curve": [7.056, 3.65, 7.278, -4.13]}, {"time": 7.5, "value": -4.13, "curve": [7.722, -4.13, 7.944, 3.65]}, {"time": 8.1667, "value": 3.65, "curve": [8.389, 3.65, 8.611, -4.13]}, {"time": 8.8333, "value": -4.13, "curve": [9.056, -4.13, 9.278, 3.65]}, {"time": 9.5, "value": 3.65, "curve": [9.722, 3.65, 9.944, -4.13]}, {"time": 10.1667, "value": -4.13, "curve": [10.333, -4.13, 10.5, 0.22]}, {"time": 10.6667, "value": 2.4}], "translate": [{"x": 4.99, "y": -3.99, "curve": [0.056, 5.94, 0.111, 6.6, 0.056, -4.99, 0.111, -5.7]}, {"time": 0.1667, "x": 6.6, "y": -5.7, "curve": [0.389, 6.6, 0.611, -3.48, 0.389, -5.7, 0.611, 4.95]}, {"time": 0.8333, "x": -3.48, "y": 4.95, "curve": [1.056, -3.48, 1.278, 6.6, 1.056, 4.95, 1.278, -5.7]}, {"time": 1.5, "x": 6.6, "y": -5.7, "curve": [1.722, 6.6, 1.944, -3.48, 1.722, -5.7, 1.944, 4.95]}, {"time": 2.1667, "x": -3.48, "y": 4.95, "curve": [2.389, -3.48, 2.611, 6.6, 2.389, 4.95, 2.611, -5.7]}, {"time": 2.8333, "x": 6.6, "y": -5.7, "curve": [3.056, 6.6, 3.278, -3.48, 3.056, -5.7, 3.278, 4.95]}, {"time": 3.5, "x": -3.48, "y": 4.95, "curve": [3.722, -3.48, 3.944, 6.6, 3.722, 4.95, 3.944, -5.7]}, {"time": 4.1667, "x": 6.6, "y": -5.7, "curve": [4.389, 6.6, 4.611, -3.48, 4.389, -5.7, 4.611, 4.95]}, {"time": 4.8333, "x": -3.48, "y": 4.95, "curve": [5.056, -3.48, 5.278, 6.6, 5.056, 4.95, 5.278, -5.7]}, {"time": 5.5, "x": 6.6, "y": -5.7, "curve": [5.722, 6.6, 5.944, -3.48, 5.722, -5.7, 5.944, 4.95]}, {"time": 6.1667, "x": -3.48, "y": 4.95, "curve": [6.389, -3.48, 6.611, 6.6, 6.389, 4.95, 6.611, -5.7]}, {"time": 6.8333, "x": 6.6, "y": -5.7, "curve": [7.056, 6.6, 7.278, -3.48, 7.056, -5.7, 7.278, 4.95]}, {"time": 7.5, "x": -3.48, "y": 4.95, "curve": [7.722, -3.48, 7.944, 6.6, 7.722, 4.95, 7.944, -5.7]}, {"time": 8.1667, "x": 6.6, "y": -5.7, "curve": [8.389, 6.6, 8.611, -3.48, 8.389, -5.7, 8.611, 4.95]}, {"time": 8.8333, "x": -3.48, "y": 4.95, "curve": [9.056, -3.48, 9.278, 6.6, 9.056, 4.95, 9.278, -5.7]}, {"time": 9.5, "x": 6.6, "y": -5.7, "curve": [9.722, 6.6, 9.944, -3.48, 9.722, -5.7, 9.944, 4.95]}, {"time": 10.1667, "x": -3.48, "y": 4.95, "curve": [10.333, -3.48, 10.5, 2.15, 10.333, 4.95, 10.5, -1]}, {"time": 10.6667, "x": 4.99, "y": -3.99}]}, "boat8": {"translate": [{"x": 14.67, "curve": [0.444, 14.67, 0.889, -11.94, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -11.94, "curve": [1.778, -11.94, 2.222, 14.67, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 14.67, "curve": [3.111, 14.67, 3.556, -11.94, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -11.94, "curve": [4.444, -11.94, 4.889, 14.67, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 14.67, "curve": [5.778, 14.67, 6.222, -11.94, 5.778, 0, 6.222, 0]}, {"time": 6.6667, "x": -11.94, "curve": [7.111, -11.94, 7.556, 14.67, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 14.67, "curve": [8.444, 14.67, 8.889, -11.94, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": -11.94, "curve": [9.778, -11.94, 10.222, 14.67, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": 14.67}]}, "boat9": {"translate": [{"x": 10.41, "curve": [0.114, 12.85, 0.224, 14.67, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": 14.67, "curve": [0.778, 14.67, 1.222, -11.94, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": -11.94, "curve": [2.111, -11.94, 2.556, 14.67, 2.111, 0, 2.556, 0]}, {"time": 3, "x": 14.67, "curve": [3.444, 14.67, 3.889, -11.94, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": -11.94, "curve": [4.778, -11.94, 5.222, 14.67, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": 14.67, "curve": [6.111, 14.67, 6.556, -11.94, 6.111, 0, 6.556, 0]}, {"time": 7, "x": -11.94, "curve": [7.444, -11.94, 7.889, 14.67, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": 14.67, "curve": [8.778, 14.67, 9.222, -11.94, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": -11.94, "curve": [10.001, -11.94, 10.336, 2.96, 10.001, 0, 10.336, 0]}, {"time": 10.6667, "x": 10.41}]}, "boat10": {"translate": [{"x": 1.36, "curve": [0.225, 7.97, 0.446, 14.67, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": 14.67, "curve": [1.111, 14.67, 1.556, -11.94, 1.111, 0, 1.556, 0]}, {"time": 2, "x": -11.94, "curve": [2.444, -11.94, 2.889, 14.67, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": 14.67, "curve": [3.778, 14.67, 4.222, -11.94, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": -11.94, "curve": [5.111, -11.94, 5.556, 14.67, 5.111, 0, 5.556, 0]}, {"time": 6, "x": 14.67, "curve": [6.444, 14.67, 6.889, -11.94, 6.444, 0, 6.889, 0]}, {"time": 7.3333, "x": -11.94, "curve": [7.778, -11.94, 8.222, 14.67, 7.778, 0, 8.222, 0]}, {"time": 8.6667, "x": 14.67, "curve": [9.111, 14.67, 9.556, -11.94, 9.111, 0, 9.556, 0]}, {"time": 10, "x": -11.94, "curve": [10.224, -11.94, 10.447, -5.33, 10.224, 0, 10.447, 0]}, {"time": 10.6667, "x": 1.36}]}, "boat11": {"translate": [{"x": 6.21, "curve": [0.168, 10.85, 0.334, 14.67, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": 14.67, "curve": [0.944, 14.67, 1.389, -11.94, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": -11.94, "curve": [2.278, -11.94, 2.722, 14.67, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": 14.67, "curve": [3.611, 14.67, 4.056, -11.94, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": -11.94, "curve": [4.944, -11.94, 5.389, 14.67, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": 14.67, "curve": [6.278, 14.67, 6.722, -11.94, 6.278, 0, 6.722, 0]}, {"time": 7.1667, "x": -11.94, "curve": [7.611, -11.94, 8.056, 14.67, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 14.67, "curve": [8.944, 14.67, 9.389, -11.94, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": -11.94, "curve": [10.112, -11.94, 10.39, -1.6, 10.112, 0, 10.39, 0]}, {"time": 10.6667, "x": 6.21}]}, "boat12": {"translate": [{"x": -3.48, "curve": [0.279, 4.31, 0.556, 14.67, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": 14.67, "curve": [1.278, 14.67, 1.722, -11.94, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": -11.94, "curve": [2.611, -11.94, 3.056, 14.67, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": 14.67, "curve": [3.944, 14.67, 4.389, -11.94, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": -11.94, "curve": [5.278, -11.94, 5.722, 14.67, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": 14.67, "curve": [6.611, 14.67, 7.056, -11.94, 6.611, 0, 7.056, 0]}, {"time": 7.5, "x": -11.94, "curve": [7.944, -11.94, 8.389, 14.67, 7.944, 0, 8.389, 0]}, {"time": 8.8333, "x": 14.67, "curve": [9.278, 14.67, 9.722, -11.94, 9.278, 0, 9.722, 0]}, {"time": 10.1667, "x": -11.94, "curve": [10.334, -11.94, 10.501, -8.18, 10.334, 0, 10.501, 0]}, {"time": 10.6667, "x": -3.48}]}, "boat13": {"translate": [{"x": -10.69, "curve": [0.39, -5.55, 0.779, 14.67, 0.39, 0, 0.779, 0]}, {"time": 1.1667, "x": 14.67, "curve": [1.611, 14.67, 2.056, -11.94, 1.611, 0, 2.056, 0]}, {"time": 2.5, "x": -11.94, "curve": [2.944, -11.94, 3.389, 14.67, 2.944, 0, 3.389, 0]}, {"time": 3.8333, "x": 14.67, "curve": [4.278, 14.67, 4.722, -11.94, 4.278, 0, 4.722, 0]}, {"time": 5.1667, "x": -11.94, "curve": [5.611, -11.94, 6.056, 14.67, 5.611, 0, 6.056, 0]}, {"time": 6.5, "x": 14.67, "curve": [6.944, 14.67, 7.389, -11.94, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -11.94, "curve": [8.278, -11.94, 8.722, 14.67, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 14.67, "curve": [9.611, 14.67, 10.056, -11.94, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -11.94, "curve": [10.556, -11.94, 10.613, -11.43, 10.556, 0, 10.613, 0]}, {"time": 10.6667, "x": -10.69}]}, "boat19": {"translate": [{"x": 0.16, "curve": [0.225, -0.85, 0.446, -1.87, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -1.87, "curve": [1.111, -1.87, 1.556, 2.18, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 2.18, "curve": [2.444, 2.18, 2.889, -1.87, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -1.87, "curve": [3.778, -1.87, 4.222, 2.18, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 2.18, "curve": [5.111, 2.18, 5.556, -1.87, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -1.87, "curve": [6.444, -1.87, 6.889, 2.18, 6.444, 0, 6.889, 0]}, {"time": 7.3333, "x": 2.18, "curve": [7.778, 2.18, 8.222, -1.87, 7.778, 0, 8.222, 0]}, {"time": 8.6667, "x": -1.87, "curve": [9.111, -1.87, 9.556, 2.18, 9.111, 0, 9.556, 0]}, {"time": 10, "x": 2.18, "curve": [10.224, 2.18, 10.447, 1.17, 10.224, 0, 10.447, 0]}, {"time": 10.6667, "x": 0.16}]}, "boat18": {"rotate": [{"value": -2.17, "curve": [0.336, -0.45, 0.668, 2.94]}, {"time": 1, "value": 2.94, "curve": [1.444, 2.94, 1.889, -3.15]}, {"time": 2.3333, "value": -3.15, "curve": [2.778, -3.15, 3.222, 2.94]}, {"time": 3.6667, "value": 2.94, "curve": [4.111, 2.94, 4.556, -3.15]}, {"time": 5, "value": -3.15, "curve": [5.444, -3.15, 5.889, 2.94]}, {"time": 6.3333, "value": 2.94, "curve": [6.778, 2.94, 7.222, -3.15]}, {"time": 7.6667, "value": -3.15, "curve": [8.111, -3.15, 8.556, 2.94]}, {"time": 9, "value": 2.94, "curve": [9.444, 2.94, 9.889, -3.15]}, {"time": 10.3333, "value": -3.15, "curve": [10.446, -3.15, 10.559, -2.75]}, {"time": 10.6667, "value": -2.17}]}, "boat20": {"rotate": [{"value": -0.16, "curve": [0.225, 1.73, 0.446, 3.66]}, {"time": 0.6667, "value": 3.66, "curve": [1.111, 3.66, 1.556, -3.99]}, {"time": 2, "value": -3.99, "curve": [2.444, -3.99, 2.889, 3.66]}, {"time": 3.3333, "value": 3.66, "curve": [3.778, 3.66, 4.222, -3.99]}, {"time": 4.6667, "value": -3.99, "curve": [5.111, -3.99, 5.556, 3.66]}, {"time": 6, "value": 3.66, "curve": [6.444, 3.66, 6.889, -3.99]}, {"time": 7.3333, "value": -3.99, "curve": [7.778, -3.99, 8.222, 3.66]}, {"time": 8.6667, "value": 3.66, "curve": [9.111, 3.66, 9.556, -3.99]}, {"time": 10, "value": -3.99, "curve": [10.224, -3.99, 10.447, -2.09]}, {"time": 10.6667, "value": -0.16}]}, "boat21": {"rotate": [{"value": -2.76, "curve": [0.336, -0.6, 0.668, 3.66]}, {"time": 1, "value": 3.66, "curve": [1.444, 3.66, 1.889, -3.99]}, {"time": 2.3333, "value": -3.99, "curve": [2.778, -3.99, 3.222, 3.66]}, {"time": 3.6667, "value": 3.66, "curve": [4.111, 3.66, 4.556, -3.99]}, {"time": 5, "value": -3.99, "curve": [5.444, -3.99, 5.889, 3.66]}, {"time": 6.3333, "value": 3.66, "curve": [6.778, 3.66, 7.222, -3.99]}, {"time": 7.6667, "value": -3.99, "curve": [8.111, -3.99, 8.556, 3.66]}, {"time": 9, "value": 3.66, "curve": [9.444, 3.66, 9.889, -3.99]}, {"time": 10.3333, "value": -3.99, "curve": [10.446, -3.99, 10.559, -3.5]}, {"time": 10.6667, "value": -2.76}]}, "boat22": {"rotate": [{"value": -3.99, "curve": [0.444, -3.99, 0.889, 3.66]}, {"time": 1.3333, "value": 3.66, "curve": [1.778, 3.66, 2.222, -3.99]}, {"time": 2.6667, "value": -3.99, "curve": [3.111, -3.99, 3.556, 3.66]}, {"time": 4, "value": 3.66, "curve": [4.444, 3.66, 4.889, -3.99]}, {"time": 5.3333, "value": -3.99, "curve": [5.778, -3.99, 6.222, 3.66]}, {"time": 6.6667, "value": 3.66, "curve": [7.111, 3.66, 7.556, -3.99]}, {"time": 8, "value": -3.99, "curve": [8.444, -3.99, 8.889, 3.66]}, {"time": 9.3333, "value": 3.66, "curve": [9.778, 3.66, 10.222, -3.99]}, {"time": 10.6667, "value": -3.99}]}, "boat23": {"rotate": [{"value": -2.76, "curve": [0.114, -3.47, 0.224, -3.99]}, {"time": 0.3333, "value": -3.99, "curve": [0.778, -3.99, 1.222, 3.66]}, {"time": 1.6667, "value": 3.66, "curve": [2.111, 3.66, 2.556, -3.99]}, {"time": 3, "value": -3.99, "curve": [3.444, -3.99, 3.889, 3.66]}, {"time": 4.3333, "value": 3.66, "curve": [4.778, 3.66, 5.222, -3.99]}, {"time": 5.6667, "value": -3.99, "curve": [6.111, -3.99, 6.556, 3.66]}, {"time": 7, "value": 3.66, "curve": [7.444, 3.66, 7.889, -3.99]}, {"time": 8.3333, "value": -3.99, "curve": [8.778, -3.99, 9.222, 3.66]}, {"time": 9.6667, "value": 3.66, "curve": [10.001, 3.66, 10.336, -0.62]}, {"time": 10.6667, "value": -2.76}]}, "boat24": {"rotate": [{"value": -0.16, "curve": [0.225, -2.06, 0.446, -3.99]}, {"time": 0.6667, "value": -3.99, "curve": [1.111, -3.99, 1.556, 3.66]}, {"time": 2, "value": 3.66, "curve": [2.444, 3.66, 2.889, -3.99]}, {"time": 3.3333, "value": -3.99, "curve": [3.778, -3.99, 4.222, 3.66]}, {"time": 4.6667, "value": 3.66, "curve": [5.111, 3.66, 5.556, -3.99]}, {"time": 6, "value": -3.99, "curve": [6.444, -3.99, 6.889, 3.66]}, {"time": 7.3333, "value": 3.66, "curve": [7.778, 3.66, 8.222, -3.99]}, {"time": 8.6667, "value": -3.99, "curve": [9.111, -3.99, 9.556, 3.66]}, {"time": 10, "value": 3.66, "curve": [10.224, 3.66, 10.447, 1.76]}, {"time": 10.6667, "value": -0.16}]}, "boat25": {"rotate": [{"value": 2.44, "curve": [0.336, 0.28, 0.668, -3.99]}, {"time": 1, "value": -3.99, "curve": [1.444, -3.99, 1.889, 3.66]}, {"time": 2.3333, "value": 3.66, "curve": [2.778, 3.66, 3.222, -3.99]}, {"time": 3.6667, "value": -3.99, "curve": [4.111, -3.99, 4.556, 3.66]}, {"time": 5, "value": 3.66, "curve": [5.444, 3.66, 5.889, -3.99]}, {"time": 6.3333, "value": -3.99, "curve": [6.778, -3.99, 7.222, 3.66]}, {"time": 7.6667, "value": 3.66, "curve": [8.111, 3.66, 8.556, -3.99]}, {"time": 9, "value": -3.99, "curve": [9.444, -3.99, 9.889, 3.66]}, {"time": 10.3333, "value": 3.66, "curve": [10.446, 3.66, 10.559, 3.17]}, {"time": 10.6667, "value": 2.44}]}, "boat26": {"rotate": [{"value": -1.77, "curve": [0.279, 0.06, 0.556, 2.49]}, {"time": 0.8333, "value": 2.49, "curve": [1.278, 2.49, 1.722, -3.76]}, {"time": 2.1667, "value": -3.76, "curve": [2.611, -3.76, 3.056, 2.49]}, {"time": 3.5, "value": 2.49, "curve": [3.944, 2.49, 4.389, -3.76]}, {"time": 4.8333, "value": -3.76, "curve": [5.278, -3.76, 5.722, 2.49]}, {"time": 6.1667, "value": 2.49, "curve": [6.611, 2.49, 7.056, -3.76]}, {"time": 7.5, "value": -3.76, "curve": [7.944, -3.76, 8.389, 2.49]}, {"time": 8.8333, "value": 2.49, "curve": [9.278, 2.49, 9.722, -3.76]}, {"time": 10.1667, "value": -3.76, "curve": [10.334, -3.76, 10.501, -2.88]}, {"time": 10.6667, "value": -1.77}]}, "boat27": {"rotate": [{"value": -3.47, "curve": [0.39, -2.26, 0.779, 2.49]}, {"time": 1.1667, "value": 2.49, "curve": [1.611, 2.49, 2.056, -3.76]}, {"time": 2.5, "value": -3.76, "curve": [2.944, -3.76, 3.389, 2.49]}, {"time": 3.8333, "value": 2.49, "curve": [4.278, 2.49, 4.722, -3.76]}, {"time": 5.1667, "value": -3.76, "curve": [5.611, -3.76, 6.056, 2.49]}, {"time": 6.5, "value": 2.49, "curve": [6.944, 2.49, 7.389, -3.76]}, {"time": 7.8333, "value": -3.76, "curve": [8.278, -3.76, 8.722, 2.49]}, {"time": 9.1667, "value": 2.49, "curve": [9.611, 2.49, 10.056, -3.76]}, {"time": 10.5, "value": -3.76, "curve": [10.556, -3.76, 10.613, -3.64]}, {"time": 10.6667, "value": -3.47}]}, "boat28": {"rotate": [{"value": -3.47, "curve": [0.057, -3.63, 0.112, -3.76]}, {"time": 0.1667, "value": -3.76, "curve": [0.611, -3.76, 1.056, 2.49]}, {"time": 1.5, "value": 2.49, "curve": [1.944, 2.49, 2.389, -3.76]}, {"time": 2.8333, "value": -3.76, "curve": [3.278, -3.76, 3.722, 2.49]}, {"time": 4.1667, "value": 2.49, "curve": [4.611, 2.49, 5.056, -3.76]}, {"time": 5.5, "value": -3.76, "curve": [5.944, -3.76, 6.389, 2.49]}, {"time": 6.8333, "value": 2.49, "curve": [7.278, 2.49, 7.722, -3.76]}, {"time": 8.1667, "value": -3.76, "curve": [8.611, -3.76, 9.056, 2.49]}, {"time": 9.5, "value": 2.49, "curve": [9.89, 2.49, 10.279, -2.28]}, {"time": 10.6667, "value": -3.47}]}, "boat29": {"rotate": [{"value": -1.77, "curve": [0.168, -2.87, 0.334, -3.76]}, {"time": 0.5, "value": -3.76, "curve": [0.944, -3.76, 1.389, 2.49]}, {"time": 1.8333, "value": 2.49, "curve": [2.278, 2.49, 2.722, -3.76]}, {"time": 3.1667, "value": -3.76, "curve": [3.611, -3.76, 4.056, 2.49]}, {"time": 4.5, "value": 2.49, "curve": [4.944, 2.49, 5.389, -3.76]}, {"time": 5.8333, "value": -3.76, "curve": [6.278, -3.76, 6.722, 2.49]}, {"time": 7.1667, "value": 2.49, "curve": [7.611, 2.49, 8.056, -3.76]}, {"time": 8.5, "value": -3.76, "curve": [8.944, -3.76, 9.389, 2.49]}, {"time": 9.8333, "value": 2.49, "curve": [10.112, 2.49, 10.39, 0.06]}, {"time": 10.6667, "value": -1.77}]}, "boat30": {"rotate": [{"value": 3.6, "curve": [0.336, 1.05, 0.668, -3.97]}, {"time": 1, "value": -3.97, "curve": [1.444, -3.97, 1.889, 5.04]}, {"time": 2.3333, "value": 5.04, "curve": [2.778, 5.04, 3.222, -3.97]}, {"time": 3.6667, "value": -3.97, "curve": [4.111, -3.97, 4.556, 5.04]}, {"time": 5, "value": 5.04, "curve": [5.444, 5.04, 5.889, -3.97]}, {"time": 6.3333, "value": -3.97, "curve": [6.778, -3.97, 7.222, 5.04]}, {"time": 7.6667, "value": 5.04, "curve": [8.111, 5.04, 8.556, -3.97]}, {"time": 9, "value": -3.97, "curve": [9.444, -3.97, 9.889, 5.04]}, {"time": 10.3333, "value": 5.04, "curve": [10.446, 5.04, 10.559, 4.46]}, {"time": 10.6667, "value": 3.6}]}, "boat31": {"rotate": [{"value": 5.04, "curve": [0.444, 5.04, 0.889, -3.97]}, {"time": 1.3333, "value": -3.97, "curve": [1.778, -3.97, 2.222, 5.04]}, {"time": 2.6667, "value": 5.04, "curve": [3.111, 5.04, 3.556, -3.97]}, {"time": 4, "value": -3.97, "curve": [4.444, -3.97, 4.889, 5.04]}, {"time": 5.3333, "value": 5.04, "curve": [5.778, 5.04, 6.222, -3.97]}, {"time": 6.6667, "value": -3.97, "curve": [7.111, -3.97, 7.556, 5.04]}, {"time": 8, "value": 5.04, "curve": [8.444, 5.04, 8.889, -3.97]}, {"time": 9.3333, "value": -3.97, "curve": [9.778, -3.97, 10.222, 5.04]}, {"time": 10.6667, "value": 5.04}]}, "boat32": {"rotate": [{"value": 3.6, "curve": [0.114, 4.43, 0.224, 5.04]}, {"time": 0.3333, "value": 5.04, "curve": [0.778, 5.04, 1.222, -3.97]}, {"time": 1.6667, "value": -3.97, "curve": [2.111, -3.97, 2.556, 5.04]}, {"time": 3, "value": 5.04, "curve": [3.444, 5.04, 3.889, -3.97]}, {"time": 4.3333, "value": -3.97, "curve": [4.778, -3.97, 5.222, 5.04]}, {"time": 5.6667, "value": 5.04, "curve": [6.111, 5.04, 6.556, -3.97]}, {"time": 7, "value": -3.97, "curve": [7.444, -3.97, 7.889, 5.04]}, {"time": 8.3333, "value": 5.04, "curve": [8.778, 5.04, 9.222, -3.97]}, {"time": 9.6667, "value": -3.97, "curve": [10.001, -3.97, 10.336, 1.07]}, {"time": 10.6667, "value": 3.6}]}, "boat33": {"rotate": [{"value": 0.53, "curve": [0.225, 2.77, 0.446, 5.04]}, {"time": 0.6667, "value": 5.04, "curve": [1.111, 5.04, 1.556, -3.97]}, {"time": 2, "value": -3.97, "curve": [2.444, -3.97, 2.889, 5.04]}, {"time": 3.3333, "value": 5.04, "curve": [3.778, 5.04, 4.222, -3.97]}, {"time": 4.6667, "value": -3.97, "curve": [5.111, -3.97, 5.556, 5.04]}, {"time": 6, "value": 5.04, "curve": [6.444, 5.04, 6.889, -3.97]}, {"time": 7.3333, "value": -3.97, "curve": [7.778, -3.97, 8.222, 5.04]}, {"time": 8.6667, "value": 5.04, "curve": [9.111, 5.04, 9.556, -3.97]}, {"time": 10, "value": -3.97, "curve": [10.224, -3.97, 10.447, -1.74]}, {"time": 10.6667, "value": 0.53}]}, "boat34": {"rotate": [{"value": 3.6, "curve": [0.336, 1.05, 0.668, -3.97]}, {"time": 1, "value": -3.97, "curve": [1.444, -3.97, 1.889, 5.04]}, {"time": 2.3333, "value": 5.04, "curve": [2.778, 5.04, 3.222, -3.97]}, {"time": 3.6667, "value": -3.97, "curve": [4.111, -3.97, 4.556, 5.04]}, {"time": 5, "value": 5.04, "curve": [5.444, 5.04, 5.889, -3.97]}, {"time": 6.3333, "value": -3.97, "curve": [6.778, -3.97, 7.222, 5.04]}, {"time": 7.6667, "value": 5.04, "curve": [8.111, 5.04, 8.556, -3.97]}, {"time": 9, "value": -3.97, "curve": [9.444, -3.97, 9.889, 5.04]}, {"time": 10.3333, "value": 5.04, "curve": [10.446, 5.04, 10.559, 4.46]}, {"time": 10.6667, "value": 3.6}]}, "boat35": {"rotate": [{"value": -3.97, "curve": [0.444, -3.97, 0.889, 5.04]}, {"time": 1.3333, "value": 5.04, "curve": [1.778, 5.04, 2.222, -3.97]}, {"time": 2.6667, "value": -3.97, "curve": [3.111, -3.97, 3.556, 5.04]}, {"time": 4, "value": 5.04, "curve": [4.444, 5.04, 4.889, -3.97]}, {"time": 5.3333, "value": -3.97, "curve": [5.778, -3.97, 6.222, 5.04]}, {"time": 6.6667, "value": 5.04, "curve": [7.111, 5.04, 7.556, -3.97]}, {"time": 8, "value": -3.97, "curve": [8.444, -3.97, 8.889, 5.04]}, {"time": 9.3333, "value": 5.04, "curve": [9.778, 5.04, 10.222, -3.97]}, {"time": 10.6667, "value": -3.97}]}, "boat36": {"rotate": [{"value": -2.53, "curve": [0.114, -3.36, 0.224, -3.97]}, {"time": 0.3333, "value": -3.97, "curve": [0.778, -3.97, 1.222, 5.04]}, {"time": 1.6667, "value": 5.04, "curve": [2.111, 5.04, 2.556, -3.97]}, {"time": 3, "value": -3.97, "curve": [3.444, -3.97, 3.889, 5.04]}, {"time": 4.3333, "value": 5.04, "curve": [4.778, 5.04, 5.222, -3.97]}, {"time": 5.6667, "value": -3.97, "curve": [6.111, -3.97, 6.556, 5.04]}, {"time": 7, "value": 5.04, "curve": [7.444, 5.04, 7.889, -3.97]}, {"time": 8.3333, "value": -3.97, "curve": [8.778, -3.97, 9.222, 5.04]}, {"time": 9.6667, "value": 5.04, "curve": [10.001, 5.04, 10.336, -0.01]}, {"time": 10.6667, "value": -2.53}]}, "boat37": {"rotate": [{"value": 1.25, "curve": [0.279, -0.71, 0.556, -3.31]}, {"time": 0.8333, "value": -3.31, "curve": [1.278, -3.31, 1.722, 3.37]}, {"time": 2.1667, "value": 3.37, "curve": [2.611, 3.37, 3.056, -3.31]}, {"time": 3.5, "value": -3.31, "curve": [3.944, -3.31, 4.389, 3.37]}, {"time": 4.8333, "value": 3.37, "curve": [5.278, 3.37, 5.722, -3.31]}, {"time": 6.1667, "value": -3.31, "curve": [6.611, -3.31, 7.056, 3.37]}, {"time": 7.5, "value": 3.37, "curve": [7.944, 3.37, 8.389, -3.31]}, {"time": 8.8333, "value": -3.31, "curve": [9.278, -3.31, 9.722, 3.37]}, {"time": 10.1667, "value": 3.37, "curve": [10.334, 3.37, 10.501, 2.43]}, {"time": 10.6667, "value": 1.25}]}, "boat38": {"rotate": [{"value": -3, "curve": [0.39, -1.71, 0.779, 3.37]}, {"time": 1.1667, "value": 3.37, "curve": [1.611, 3.37, 2.056, -3.31]}, {"time": 2.5, "value": -3.31, "curve": [2.944, -3.31, 3.389, 3.37]}, {"time": 3.8333, "value": 3.37, "curve": [4.278, 3.37, 4.722, -3.31]}, {"time": 5.1667, "value": -3.31, "curve": [5.611, -3.31, 6.056, 3.37]}, {"time": 6.5, "value": 3.37, "curve": [6.944, 3.37, 7.389, -3.31]}, {"time": 7.8333, "value": -3.31, "curve": [8.278, -3.31, 8.722, 3.37]}, {"time": 9.1667, "value": 3.37, "curve": [9.611, 3.37, 10.056, -3.31]}, {"time": 10.5, "value": -3.31, "curve": [10.556, -3.31, 10.613, -3.19]}, {"time": 10.6667, "value": -3}]}, "boat39": {"rotate": [{"value": -1.19, "curve": [0.279, 0.77, 0.556, 3.37]}, {"time": 0.8333, "value": 3.37, "curve": [1.278, 3.37, 1.722, -3.31]}, {"time": 2.1667, "value": -3.31, "curve": [2.611, -3.31, 3.056, 3.37]}, {"time": 3.5, "value": 3.37, "curve": [3.944, 3.37, 4.389, -3.31]}, {"time": 4.8333, "value": -3.31, "curve": [5.278, -3.31, 5.722, 3.37]}, {"time": 6.1667, "value": 3.37, "curve": [6.611, 3.37, 7.056, -3.31]}, {"time": 7.5, "value": -3.31, "curve": [7.944, -3.31, 8.389, 3.37]}, {"time": 8.8333, "value": 3.37, "curve": [9.278, 3.37, 9.722, -3.31]}, {"time": 10.1667, "value": -3.31, "curve": [10.334, -3.31, 10.501, -2.37]}, {"time": 10.6667, "value": -1.19}]}, "boat40": {"rotate": [{"value": 1.25, "curve": [0.168, 2.41, 0.334, 3.37]}, {"time": 0.5, "value": 3.37, "curve": [0.944, 3.37, 1.389, -3.31]}, {"time": 1.8333, "value": -3.31, "curve": [2.278, -3.31, 2.722, 3.37]}, {"time": 3.1667, "value": 3.37, "curve": [3.611, 3.37, 4.056, -3.31]}, {"time": 4.5, "value": -3.31, "curve": [4.944, -3.31, 5.389, 3.37]}, {"time": 5.8333, "value": 3.37, "curve": [6.278, 3.37, 6.722, -3.31]}, {"time": 7.1667, "value": -3.31, "curve": [7.611, -3.31, 8.056, 3.37]}, {"time": 8.5, "value": 3.37, "curve": [8.944, 3.37, 9.389, -3.31]}, {"time": 9.8333, "value": -3.31, "curve": [10.112, -3.31, 10.39, -0.71]}, {"time": 10.6667, "value": 1.25}]}, "boat41": {"rotate": [{"value": 3.06, "curve": [0.057, 3.24, 0.112, 3.37]}, {"time": 0.1667, "value": 3.37, "curve": [0.611, 3.37, 1.056, -3.31]}, {"time": 1.5, "value": -3.31, "curve": [1.944, -3.31, 2.389, 3.37]}, {"time": 2.8333, "value": 3.37, "curve": [3.278, 3.37, 3.722, -3.31]}, {"time": 4.1667, "value": -3.31, "curve": [4.611, -3.31, 5.056, 3.37]}, {"time": 5.5, "value": 3.37, "curve": [5.944, 3.37, 6.389, -3.31]}, {"time": 6.8333, "value": -3.31, "curve": [7.278, -3.31, 7.722, 3.37]}, {"time": 8.1667, "value": 3.37, "curve": [8.611, 3.37, 9.056, -3.31]}, {"time": 9.5, "value": -3.31, "curve": [9.89, -3.31, 10.279, 1.79]}, {"time": 10.6667, "value": 3.06}]}, "boat42": {"rotate": [{"value": 3.06, "curve": [0.39, 1.77, 0.779, -3.31]}, {"time": 1.1667, "value": -3.31, "curve": [1.611, -3.31, 2.056, 3.37]}, {"time": 2.5, "value": 3.37, "curve": [2.944, 3.37, 3.389, -3.31]}, {"time": 3.8333, "value": -3.31, "curve": [4.278, -3.31, 4.722, 3.37]}, {"time": 5.1667, "value": 3.37, "curve": [5.611, 3.37, 6.056, -3.31]}, {"time": 6.5, "value": -3.31, "curve": [6.944, -3.31, 7.389, 3.37]}, {"time": 7.8333, "value": 3.37, "curve": [8.278, 3.37, 8.722, -3.31]}, {"time": 9.1667, "value": -3.31, "curve": [9.611, -3.31, 10.056, 3.37]}, {"time": 10.5, "value": 3.37, "curve": [10.556, 3.37, 10.613, 3.24]}, {"time": 10.6667, "value": 3.06}]}, "boat43": {"rotate": [{"value": -3, "curve": [0.057, -3.18, 0.112, -3.31]}, {"time": 0.1667, "value": -3.31, "curve": [0.611, -3.31, 1.056, 3.37]}, {"time": 1.5, "value": 3.37, "curve": [1.944, 3.37, 2.389, -3.31]}, {"time": 2.8333, "value": -3.31, "curve": [3.278, -3.31, 3.722, 3.37]}, {"time": 4.1667, "value": 3.37, "curve": [4.611, 3.37, 5.056, -3.31]}, {"time": 5.5, "value": -3.31, "curve": [5.944, -3.31, 6.389, 3.37]}, {"time": 6.8333, "value": 3.37, "curve": [7.278, 3.37, 7.722, -3.31]}, {"time": 8.1667, "value": -3.31, "curve": [8.611, -3.31, 9.056, 3.37]}, {"time": 9.5, "value": 3.37, "curve": [9.89, 3.37, 10.279, -1.73]}, {"time": 10.6667, "value": -3}]}, "boat44": {"rotate": [{"value": 1.23, "curve": [0.168, 2.56, 0.334, 3.66]}, {"time": 0.5, "value": 3.66, "curve": [0.944, 3.66, 1.389, -3.99]}, {"time": 1.8333, "value": -3.99, "curve": [2.278, -3.99, 2.722, 3.66]}, {"time": 3.1667, "value": 3.66, "curve": [3.611, 3.66, 4.056, -3.99]}, {"time": 4.5, "value": -3.99, "curve": [4.944, -3.99, 5.389, 3.66]}, {"time": 5.8333, "value": 3.66, "curve": [6.278, 3.66, 6.722, -3.99]}, {"time": 7.1667, "value": -3.99, "curve": [7.611, -3.99, 8.056, 3.66]}, {"time": 8.5, "value": 3.66, "curve": [8.944, 3.66, 9.389, -3.99]}, {"time": 9.8333, "value": -3.99, "curve": [10.112, -3.99, 10.39, -1.01]}, {"time": 10.6667, "value": 1.23}]}, "boat45": {"rotate": [{"value": -1.56, "curve": [0.279, 0.68, 0.556, 3.66]}, {"time": 0.8333, "value": 3.66, "curve": [1.278, 3.66, 1.722, -3.99]}, {"time": 2.1667, "value": -3.99, "curve": [2.611, -3.99, 3.056, 3.66]}, {"time": 3.5, "value": 3.66, "curve": [3.944, 3.66, 4.389, -3.99]}, {"time": 4.8333, "value": -3.99, "curve": [5.278, -3.99, 5.722, 3.66]}, {"time": 6.1667, "value": 3.66, "curve": [6.611, 3.66, 7.056, -3.99]}, {"time": 7.5, "value": -3.99, "curve": [7.944, -3.99, 8.389, 3.66]}, {"time": 8.8333, "value": 3.66, "curve": [9.278, 3.66, 9.722, -3.99]}, {"time": 10.1667, "value": -3.99, "curve": [10.334, -3.99, 10.501, -2.91]}, {"time": 10.6667, "value": -1.56}]}, "boat46": {"rotate": [{"value": -3.63, "curve": [0.39, -2.15, 0.779, 3.66]}, {"time": 1.1667, "value": 3.66, "curve": [1.611, 3.66, 2.056, -3.99]}, {"time": 2.5, "value": -3.99, "curve": [2.944, -3.99, 3.389, 3.66]}, {"time": 3.8333, "value": 3.66, "curve": [4.278, 3.66, 4.722, -3.99]}, {"time": 5.1667, "value": -3.99, "curve": [5.611, -3.99, 6.056, 3.66]}, {"time": 6.5, "value": 3.66, "curve": [6.944, 3.66, 7.389, -3.99]}, {"time": 7.8333, "value": -3.99, "curve": [8.278, -3.99, 8.722, 3.66]}, {"time": 9.1667, "value": 3.66, "curve": [9.611, 3.66, 10.056, -3.99]}, {"time": 10.5, "value": -3.99, "curve": [10.556, -3.99, 10.613, -3.84]}, {"time": 10.6667, "value": -3.63}]}, "boat47": {"rotate": [{"value": -3.63, "curve": [0.057, -3.83, 0.112, -3.99]}, {"time": 0.1667, "value": -3.99, "curve": [0.611, -3.99, 1.056, 3.66]}, {"time": 1.5, "value": 3.66, "curve": [1.944, 3.66, 2.389, -3.99]}, {"time": 2.8333, "value": -3.99, "curve": [3.278, -3.99, 3.722, 3.66]}, {"time": 4.1667, "value": 3.66, "curve": [4.611, 3.66, 5.056, -3.99]}, {"time": 5.5, "value": -3.99, "curve": [5.944, -3.99, 6.389, 3.66]}, {"time": 6.8333, "value": 3.66, "curve": [7.278, 3.66, 7.722, -3.99]}, {"time": 8.1667, "value": -3.99, "curve": [8.611, -3.99, 9.056, 3.66]}, {"time": 9.5, "value": 3.66, "curve": [9.89, 3.66, 10.279, -2.18]}, {"time": 10.6667, "value": -3.63}]}, "boat48": {"rotate": [{"value": -1.56, "curve": [0.168, -2.89, 0.334, -3.99]}, {"time": 0.5, "value": -3.99, "curve": [0.944, -3.99, 1.389, 3.66]}, {"time": 1.8333, "value": 3.66, "curve": [2.278, 3.66, 2.722, -3.99]}, {"time": 3.1667, "value": -3.99, "curve": [3.611, -3.99, 4.056, 3.66]}, {"time": 4.5, "value": 3.66, "curve": [4.944, 3.66, 5.389, -3.99]}, {"time": 5.8333, "value": -3.99, "curve": [6.278, -3.99, 6.722, 3.66]}, {"time": 7.1667, "value": 3.66, "curve": [7.611, 3.66, 8.056, -3.99]}, {"time": 8.5, "value": -3.99, "curve": [8.944, -3.99, 9.389, 3.66]}, {"time": 9.8333, "value": 3.66, "curve": [10.112, 3.66, 10.39, 0.69]}, {"time": 10.6667, "value": -1.56}]}, "boat49": {"rotate": [{"value": 1.23, "curve": [0.279, -1.01, 0.556, -3.99]}, {"time": 0.8333, "value": -3.99, "curve": [1.278, -3.99, 1.722, 3.66]}, {"time": 2.1667, "value": 3.66, "curve": [2.611, 3.66, 3.056, -3.99]}, {"time": 3.5, "value": -3.99, "curve": [3.944, -3.99, 4.389, 3.66]}, {"time": 4.8333, "value": 3.66, "curve": [5.278, 3.66, 5.722, -3.99]}, {"time": 6.1667, "value": -3.99, "curve": [6.611, -3.99, 7.056, 3.66]}, {"time": 7.5, "value": 3.66, "curve": [7.944, 3.66, 8.389, -3.99]}, {"time": 8.8333, "value": -3.99, "curve": [9.278, -3.99, 9.722, 3.66]}, {"time": 10.1667, "value": 3.66, "curve": [10.334, 3.66, 10.501, 2.58]}, {"time": 10.6667, "value": 1.23}]}, "boat50": {"rotate": [{"value": -5.96, "curve": [0.39, -3.32, 0.779, 7.06]}, {"time": 1.1667, "value": 7.06, "curve": [1.611, 7.06, 2.056, -6.61]}, {"time": 2.5, "value": -6.61, "curve": [2.944, -6.61, 3.389, 7.06]}, {"time": 3.8333, "value": 7.06, "curve": [4.278, 7.06, 4.722, -6.61]}, {"time": 5.1667, "value": -6.61, "curve": [5.611, -6.61, 6.056, 7.06]}, {"time": 6.5, "value": 7.06, "curve": [6.944, 7.06, 7.389, -6.61]}, {"time": 7.8333, "value": -6.61, "curve": [8.278, -6.61, 8.722, 7.06]}, {"time": 9.1667, "value": 7.06, "curve": [9.611, 7.06, 10.056, -6.61]}, {"time": 10.5, "value": -6.61, "curve": [10.556, -6.61, 10.613, -6.35]}, {"time": 10.6667, "value": -5.96}]}, "boat51": {"rotate": [{"value": -5.96, "curve": [0.057, -6.33, 0.112, -6.61]}, {"time": 0.1667, "value": -6.61, "curve": [0.611, -6.61, 1.056, 7.06]}, {"time": 1.5, "value": 7.06, "curve": [1.944, 7.06, 2.389, -6.61]}, {"time": 2.8333, "value": -6.61, "curve": [3.278, -6.61, 3.722, 7.06]}, {"time": 4.1667, "value": 7.06, "curve": [4.611, 7.06, 5.056, -6.61]}, {"time": 5.5, "value": -6.61, "curve": [5.944, -6.61, 6.389, 7.06]}, {"time": 6.8333, "value": 7.06, "curve": [7.278, 7.06, 7.722, -6.61]}, {"time": 8.1667, "value": -6.61, "curve": [8.611, -6.61, 9.056, 7.06]}, {"time": 9.5, "value": 7.06, "curve": [9.89, 7.06, 10.279, -3.37]}, {"time": 10.6667, "value": -5.96}]}, "boat52": {"rotate": [{"value": -2.26, "curve": [0.168, -4.65, 0.334, -6.61]}, {"time": 0.5, "value": -6.61, "curve": [0.944, -6.61, 1.389, 7.06]}, {"time": 1.8333, "value": 7.06, "curve": [2.278, 7.06, 2.722, -6.61]}, {"time": 3.1667, "value": -6.61, "curve": [3.611, -6.61, 4.056, 7.06]}, {"time": 4.5, "value": 7.06, "curve": [4.944, 7.06, 5.389, -6.61]}, {"time": 5.8333, "value": -6.61, "curve": [6.278, -6.61, 6.722, 7.06]}, {"time": 7.1667, "value": 7.06, "curve": [7.611, 7.06, 8.056, -6.61]}, {"time": 8.5, "value": -6.61, "curve": [8.944, -6.61, 9.389, 7.06]}, {"time": 9.8333, "value": 7.06, "curve": [10.112, 7.06, 10.39, 1.75]}, {"time": 10.6667, "value": -2.26}]}, "boat53": {"rotate": [{"value": 2.72, "curve": [0.279, -1.29, 0.556, -6.61]}, {"time": 0.8333, "value": -6.61, "curve": [1.278, -6.61, 1.722, 7.06]}, {"time": 2.1667, "value": 7.06, "curve": [2.611, 7.06, 3.056, -6.61]}, {"time": 3.5, "value": -6.61, "curve": [3.944, -6.61, 4.389, 7.06]}, {"time": 4.8333, "value": 7.06, "curve": [5.278, 7.06, 5.722, -6.61]}, {"time": 6.1667, "value": -6.61, "curve": [6.611, -6.61, 7.056, 7.06]}, {"time": 7.5, "value": 7.06, "curve": [7.944, 7.06, 8.389, -6.61]}, {"time": 8.8333, "value": -6.61, "curve": [9.278, -6.61, 9.722, 7.06]}, {"time": 10.1667, "value": 7.06, "curve": [10.334, 7.06, 10.501, 5.13]}, {"time": 10.6667, "value": 2.72}]}, "boat54": {"rotate": [{"value": 0.53, "curve": [0.225, -1.71, 0.446, -3.97]}, {"time": 0.6667, "value": -3.97, "curve": [1.111, -3.97, 1.556, 5.04]}, {"time": 2, "value": 5.04, "curve": [2.444, 5.04, 2.889, -3.97]}, {"time": 3.3333, "value": -3.97, "curve": [3.778, -3.97, 4.222, 5.04]}, {"time": 4.6667, "value": 5.04, "curve": [5.111, 5.04, 5.556, -3.97]}, {"time": 6, "value": -3.97, "curve": [6.444, -3.97, 6.889, 5.04]}, {"time": 7.3333, "value": 5.04, "curve": [7.778, 5.04, 8.222, -3.97]}, {"time": 8.6667, "value": -3.97, "curve": [9.111, -3.97, 9.556, 5.04]}, {"time": 10, "value": 5.04, "curve": [10.224, 5.04, 10.447, 2.8]}, {"time": 10.6667, "value": 0.53}]}, "boat55": {"rotate": [{"value": 3.6, "curve": [0.336, 1.05, 0.668, -3.97]}, {"time": 1, "value": -3.97, "curve": [1.444, -3.97, 1.889, 5.04]}, {"time": 2.3333, "value": 5.04, "curve": [2.778, 5.04, 3.222, -3.97]}, {"time": 3.6667, "value": -3.97, "curve": [4.111, -3.97, 4.556, 5.04]}, {"time": 5, "value": 5.04, "curve": [5.444, 5.04, 5.889, -3.97]}, {"time": 6.3333, "value": -3.97, "curve": [6.778, -3.97, 7.222, 5.04]}, {"time": 7.6667, "value": 5.04, "curve": [8.111, 5.04, 8.556, -3.97]}, {"time": 9, "value": -3.97, "curve": [9.444, -3.97, 9.889, 5.04]}, {"time": 10.3333, "value": 5.04, "curve": [10.446, 5.04, 10.559, 4.46]}, {"time": 10.6667, "value": 3.6}]}, "boat56": {"rotate": [{"value": 5.04, "curve": [0.444, 5.04, 0.889, -3.97]}, {"time": 1.3333, "value": -3.97, "curve": [1.778, -3.97, 2.222, 5.04]}, {"time": 2.6667, "value": 5.04, "curve": [3.111, 5.04, 3.556, -3.97]}, {"time": 4, "value": -3.97, "curve": [4.444, -3.97, 4.889, 5.04]}, {"time": 5.3333, "value": 5.04, "curve": [5.778, 5.04, 6.222, -3.97]}, {"time": 6.6667, "value": -3.97, "curve": [7.111, -3.97, 7.556, 5.04]}, {"time": 8, "value": 5.04, "curve": [8.444, 5.04, 8.889, -3.97]}, {"time": 9.3333, "value": -3.97, "curve": [9.778, -3.97, 10.222, 5.04]}, {"time": 10.6667, "value": 5.04}]}, "boat57": {"rotate": [{"value": 3.6, "curve": [0.114, 4.43, 0.224, 5.04]}, {"time": 0.3333, "value": 5.04, "curve": [0.778, 5.04, 1.222, -3.97]}, {"time": 1.6667, "value": -3.97, "curve": [2.111, -3.97, 2.556, 5.04]}, {"time": 3, "value": 5.04, "curve": [3.444, 5.04, 3.889, -3.97]}, {"time": 4.3333, "value": -3.97, "curve": [4.778, -3.97, 5.222, 5.04]}, {"time": 5.6667, "value": 5.04, "curve": [6.111, 5.04, 6.556, -3.97]}, {"time": 7, "value": -3.97, "curve": [7.444, -3.97, 7.889, 5.04]}, {"time": 8.3333, "value": 5.04, "curve": [8.778, 5.04, 9.222, -3.97]}, {"time": 9.6667, "value": -3.97, "curve": [10.001, -3.97, 10.336, 1.07]}, {"time": 10.6667, "value": 3.6}]}, "boat58": {"rotate": [{"value": 0.53, "curve": [0.225, 2.77, 0.446, 5.04]}, {"time": 0.6667, "value": 5.04, "curve": [1.111, 5.04, 1.556, -3.97]}, {"time": 2, "value": -3.97, "curve": [2.444, -3.97, 2.889, 5.04]}, {"time": 3.3333, "value": 5.04, "curve": [3.778, 5.04, 4.222, -3.97]}, {"time": 4.6667, "value": -3.97, "curve": [5.111, -3.97, 5.556, 5.04]}, {"time": 6, "value": 5.04, "curve": [6.444, 5.04, 6.889, -3.97]}, {"time": 7.3333, "value": -3.97, "curve": [7.778, -3.97, 8.222, 5.04]}, {"time": 8.6667, "value": 5.04, "curve": [9.111, 5.04, 9.556, -3.97]}, {"time": 10, "value": -3.97, "curve": [10.224, -3.97, 10.447, -1.74]}, {"time": 10.6667, "value": 0.53}]}, "bone": {"translate": [{"y": -9.68, "curve": [0.292, 0, 0.579, 0, 0.292, -0.8, 0.579, 11.84]}, {"time": 0.8667, "y": 11.84, "curve": [1.311, 0, 1.756, 0, 1.311, 11.84, 1.756, -18.21]}, {"time": 2.2, "y": -18.21, "curve": [2.644, 0, 3.089, 0, 2.644, -18.21, 3.089, 11.84]}, {"time": 3.5333, "y": 11.84, "curve": [3.978, 0, 4.422, 0, 3.978, 11.84, 4.422, -18.21]}, {"time": 4.8667, "y": -18.21, "curve": [5.311, 0, 5.756, 0, 5.311, -18.21, 5.756, 11.84]}, {"time": 6.2, "y": 11.84, "curve": [6.644, 0, 7.089, 0, 6.644, 11.84, 7.089, -18.21]}, {"time": 7.5333, "y": -18.21, "curve": [7.978, 0, 8.422, 0, 7.978, -18.21, 8.422, 11.84]}, {"time": 8.8667, "y": 11.84, "curve": [9.311, 0, 9.756, 0, 9.311, 11.84, 9.756, -18.21]}, {"time": 10.2, "y": -18.21, "curve": [10.357, 0, 10.514, 0, 10.357, -18.21, 10.514, -14.53]}, {"time": 10.6667, "y": -9.68}]}, "bone2": {"translate": [{"y": -15.65, "curve": [0.079, 0, 0.156, 0, 0.079, -17.15, 0.156, -18.21]}, {"time": 0.2333, "y": -18.21, "curve": [0.678, 0, 1.122, 0, 0.678, -18.21, 1.122, 11.84]}, {"time": 1.5667, "y": 11.84, "curve": [2.011, 0, 2.456, 0, 2.011, 11.84, 2.456, -18.21]}, {"time": 2.9, "y": -18.21, "curve": [3.344, 0, 3.789, 0, 3.344, -18.21, 3.789, 11.84]}, {"time": 4.2333, "y": 11.84, "curve": [4.678, 0, 5.122, 0, 4.678, 11.84, 5.122, -18.21]}, {"time": 5.5667, "y": -18.21, "curve": [6.011, 0, 6.456, 0, 6.011, -18.21, 6.456, 11.84]}, {"time": 6.9, "y": 11.84, "curve": [7.344, 0, 7.789, 0, 7.344, 11.84, 7.789, -18.21]}, {"time": 8.2333, "y": -18.21, "curve": [8.678, 0, 9.122, 0, 8.678, -18.21, 9.122, 11.84]}, {"time": 9.5667, "y": 11.84, "curve": [9.934, 0, 10.301, 0, 9.934, 11.84, 10.301, -8.54]}, {"time": 10.6667, "y": -15.65}]}, "bone3": {"translate": [{"y": -15.65, "curve": [0.368, 0, 0.734, 0, 0.368, -8.46, 0.734, 11.84]}, {"time": 1.1, "y": 11.84, "curve": [1.544, 0, 1.989, 0, 1.544, 11.84, 1.989, -18.21]}, {"time": 2.4333, "y": -18.21, "curve": [2.878, 0, 3.322, 0, 2.878, -18.21, 3.322, 11.84]}, {"time": 3.7667, "y": 11.84, "curve": [4.211, 0, 4.656, 0, 4.211, 11.84, 4.656, -18.21]}, {"time": 5.1, "y": -18.21, "curve": [5.544, 0, 5.989, 0, 5.544, -18.21, 5.989, 11.84]}, {"time": 6.4333, "y": 11.84, "curve": [6.878, 0, 7.322, 0, 6.878, 11.84, 7.322, -18.21]}, {"time": 7.7667, "y": -18.21, "curve": [8.211, 0, 8.656, 0, 8.211, -18.21, 8.656, 11.84]}, {"time": 9.1, "y": 11.84, "curve": [9.544, 0, 9.989, 0, 9.544, 11.84, 9.989, -18.21]}, {"time": 10.4333, "y": -18.21, "curve": [10.512, 0, 10.59, 0, 10.512, -18.21, 10.59, -17.2]}, {"time": 10.6667, "y": -15.65}]}, "bone4": {"translate": [{"y": -9.62, "curve": [0.08, 0, 0.157, 0, 0.08, -11.97, 0.157, -14.16]}, {"time": 0.2333, "y": -15.65, "curve": [0.313, 0, 0.39, 0, 0.313, -17.15, 0.39, -18.21]}, {"time": 0.4667, "y": -18.21, "curve": [0.911, 0, 1.356, 0, 0.911, -18.21, 1.356, 11.84]}, {"time": 1.8, "y": 11.84, "curve": [2.244, 0, 2.689, 0, 2.244, 11.84, 2.689, -18.21]}, {"time": 3.1333, "y": -18.21, "curve": [3.578, 0, 4.022, 0, 3.578, -18.21, 4.022, 11.84]}, {"time": 4.4667, "y": 11.84, "curve": [4.911, 0, 5.356, 0, 4.911, 11.84, 5.356, -18.21]}, {"time": 5.8, "y": -18.21, "curve": [6.244, 0, 6.689, 0, 6.244, -18.21, 6.689, 11.84]}, {"time": 7.1333, "y": 11.84, "curve": [7.578, 0, 8.022, 0, 7.578, 11.84, 8.022, -18.21]}, {"time": 8.4667, "y": -18.21, "curve": [8.911, 0, 9.356, 0, 8.911, -18.21, 9.356, 11.84]}, {"time": 9.8, "y": 11.84, "curve": [10.09, 0, 10.381, 0, 10.09, 11.84, 10.381, -0.77]}, {"time": 10.6667, "y": -9.62}]}, "bone5": {"translate": [{"y": -18.21, "curve": [0.444, 0, 0.889, 0, 0.444, -18.21, 0.889, 11.84]}, {"time": 1.3333, "y": 11.84, "curve": [1.778, 0, 2.222, 0, 1.778, 11.84, 2.222, -18.21]}, {"time": 2.6667, "y": -18.21, "curve": [3.111, 0, 3.556, 0, 3.111, -18.21, 3.556, 11.84]}, {"time": 4, "y": 11.84, "curve": [4.444, 0, 4.889, 0, 4.444, 11.84, 4.889, -18.21]}, {"time": 5.3333, "y": -18.21, "curve": [5.778, 0, 6.222, 0, 5.778, -18.21, 6.222, 11.84]}, {"time": 6.6667, "y": 11.84, "curve": [7.111, 0, 7.556, 0, 7.111, 11.84, 7.556, -18.21]}, {"time": 8, "y": -18.21, "curve": [8.444, 0, 8.889, 0, 8.444, -18.21, 8.889, 11.84]}, {"time": 9.3333, "y": 11.84, "curve": [9.778, 0, 10.222, 0, 9.778, 11.84, 10.222, -18.21]}, {"time": 10.6667, "y": -18.21}]}, "bone6": {"translate": [{"y": -15.65, "curve": [0.079, 0, 0.156, 0, 0.079, -17.15, 0.156, -18.21]}, {"time": 0.2333, "y": -18.21, "curve": [0.678, 0, 1.122, 0, 0.678, -18.21, 1.122, 11.84]}, {"time": 1.5667, "y": 11.84, "curve": [2.011, 0, 2.456, 0, 2.011, 11.84, 2.456, -18.21]}, {"time": 2.9, "y": -18.21, "curve": [3.344, 0, 3.789, 0, 3.344, -18.21, 3.789, 11.84]}, {"time": 4.2333, "y": 11.84, "curve": [4.678, 0, 5.122, 0, 4.678, 11.84, 5.122, -18.21]}, {"time": 5.5667, "y": -18.21, "curve": [6.011, 0, 6.456, 0, 6.011, -18.21, 6.456, 11.84]}, {"time": 6.9, "y": 11.84, "curve": [7.344, 0, 7.789, 0, 7.344, 11.84, 7.789, -18.21]}, {"time": 8.2333, "y": -18.21, "curve": [8.678, 0, 9.122, 0, 8.678, -18.21, 9.122, 11.84]}, {"time": 9.5667, "y": 11.84, "curve": [9.934, 0, 10.301, 0, 9.934, 11.84, 10.301, -8.54]}, {"time": 10.6667, "y": -15.65}]}, "bone7": {"translate": [{"y": -9.68, "curve": [0.159, 0, 0.313, 0, 0.159, -14.39, 0.313, -18.21]}, {"time": 0.4667, "y": -18.21, "curve": [0.911, 0, 1.356, 0, 0.911, -18.21, 1.356, 11.84]}, {"time": 1.8, "y": 11.84, "curve": [2.244, 0, 2.689, 0, 2.244, 11.84, 2.689, -18.21]}, {"time": 3.1333, "y": -18.21, "curve": [3.578, 0, 4.022, 0, 3.578, -18.21, 4.022, 11.84]}, {"time": 4.4667, "y": 11.84, "curve": [4.911, 0, 5.356, 0, 4.911, 11.84, 5.356, -18.21]}, {"time": 5.8, "y": -18.21, "curve": [6.244, 0, 6.689, 0, 6.244, -18.21, 6.689, 11.84]}, {"time": 7.1333, "y": 11.84, "curve": [7.578, 0, 8.022, 0, 7.578, 11.84, 8.022, -18.21]}, {"time": 8.4667, "y": -18.21, "curve": [8.911, 0, 9.356, 0, 8.911, -18.21, 9.356, 11.84]}, {"time": 9.8, "y": 11.84, "curve": [10.09, 0, 10.381, 0, 10.09, 11.84, 10.381, -0.78]}, {"time": 10.6667, "y": -9.68}]}, "bone8": {"translate": [{"y": -2.07, "curve": [0.235, 0, 0.467, 0, 0.235, -9.92, 0.467, -18.21]}, {"time": 0.7, "y": -18.21, "curve": [1.144, 0, 1.589, 0, 1.144, -18.21, 1.589, 11.84]}, {"time": 2.0333, "y": 11.84, "curve": [2.478, 0, 2.922, 0, 2.478, 11.84, 2.922, -18.21]}, {"time": 3.3667, "y": -18.21, "curve": [3.811, 0, 4.256, 0, 3.811, -18.21, 4.256, 11.84]}, {"time": 4.7, "y": 11.84, "curve": [5.144, 0, 5.589, 0, 5.144, 11.84, 5.589, -18.21]}, {"time": 6.0333, "y": -18.21, "curve": [6.478, 0, 6.922, 0, 6.478, -18.21, 6.922, 11.84]}, {"time": 7.3667, "y": 11.84, "curve": [7.811, 0, 8.256, 0, 7.811, 11.84, 8.256, -18.21]}, {"time": 8.7, "y": -18.21, "curve": [9.144, 0, 9.589, 0, 9.144, -18.21, 9.589, 11.84]}, {"time": 10.0333, "y": 11.84, "curve": [10.245, 0, 10.457, 0, 10.245, 11.84, 10.457, 5.07]}, {"time": 10.6667, "y": -2.07}]}, "bone9": {"translate": [{"y": 5.35, "curve": [0.314, 0, 0.624, 0, 0.314, -3.5, 0.624, -18.21]}, {"time": 0.9333, "y": -18.21, "curve": [1.378, 0, 1.822, 0, 1.378, -18.21, 1.822, 11.84]}, {"time": 2.2667, "y": 11.84, "curve": [2.711, 0, 3.156, 0, 2.711, 11.84, 3.156, -18.21]}, {"time": 3.6, "y": -18.21, "curve": [4.044, 0, 4.489, 0, 4.044, -18.21, 4.489, 11.84]}, {"time": 4.9333, "y": 11.84, "curve": [5.378, 0, 5.822, 0, 5.378, 11.84, 5.822, -18.21]}, {"time": 6.2667, "y": -18.21, "curve": [6.711, 0, 7.156, 0, 6.711, -18.21, 7.156, 11.84]}, {"time": 7.6, "y": 11.84, "curve": [8.044, 0, 8.489, 0, 8.044, 11.84, 8.489, -18.21]}, {"time": 8.9333, "y": -18.21, "curve": [9.378, 0, 9.822, 0, 9.378, -18.21, 9.822, 11.84]}, {"time": 10.2667, "y": 11.84, "curve": [10.401, 0, 10.536, 0, 10.401, 11.84, 10.536, 9.2]}, {"time": 10.6667, "y": 5.35}]}, "bone10": {"translate": [{"y": 10.42, "curve": [0.39, 0, 0.779, 0, 0.39, 4.62, 0.779, -18.21]}, {"time": 1.1667, "y": -18.21, "curve": [1.611, 0, 2.056, 0, 1.611, -18.21, 2.056, 11.84]}, {"time": 2.5, "y": 11.84, "curve": [2.944, 0, 3.389, 0, 2.944, 11.84, 3.389, -18.21]}, {"time": 3.8333, "y": -18.21, "curve": [4.278, 0, 4.722, 0, 4.278, -18.21, 4.722, 11.84]}, {"time": 5.1667, "y": 11.84, "curve": [5.611, 0, 6.056, 0, 5.611, 11.84, 6.056, -18.21]}, {"time": 6.5, "y": -18.21, "curve": [6.944, 0, 7.389, 0, 6.944, -18.21, 7.389, 11.84]}, {"time": 7.8333, "y": 11.84, "curve": [8.278, 0, 8.722, 0, 8.278, 11.84, 8.722, -18.21]}, {"time": 9.1667, "y": -18.21, "curve": [9.611, 0, 10.056, 0, 9.611, -18.21, 10.056, 11.84]}, {"time": 10.5, "y": 11.84, "curve": [10.556, 0, 10.613, 0, 10.556, 11.84, 10.613, 11.27]}, {"time": 10.6667, "y": 10.42}]}, "bone11": {"translate": [{"y": 11.41, "curve": [0.025, 0, 0.046, 0, 0.025, 11.54, 0.046, 11.84]}, {"time": 0.0667, "y": 11.84, "curve": [0.511, 0, 0.956, 0, 0.511, 11.84, 0.956, -18.21]}, {"time": 1.4, "y": -18.21, "curve": [1.844, 0, 2.289, 0, 1.844, -18.21, 2.289, 11.84]}, {"time": 2.7333, "y": 11.84, "curve": [3.178, 0, 3.622, 0, 3.178, 11.84, 3.622, -18.21]}, {"time": 4.0667, "y": -18.21, "curve": [4.511, 0, 4.956, 0, 4.511, -18.21, 4.956, 11.84]}, {"time": 5.4, "y": 11.84, "curve": [5.844, 0, 6.289, 0, 5.844, 11.84, 6.289, -18.21]}, {"time": 6.7333, "y": -18.21, "curve": [7.178, 0, 7.622, 0, 7.178, -18.21, 7.622, 11.84]}, {"time": 8.0667, "y": 11.84, "curve": [8.511, 0, 8.956, 0, 8.511, 11.84, 8.956, -18.21]}, {"time": 9.4, "y": -18.21, "curve": [9.824, 0, 10.247, 0, 9.824, -18.21, 10.247, 8.87]}, {"time": 10.6667, "y": 11.41}]}, "bone12": {"translate": [{"y": 7.87, "curve": [0.101, 0, 0.201, 0, 0.101, 10.2, 0.201, 11.84]}, {"time": 0.3, "y": 11.84, "curve": [0.744, 0, 1.189, 0, 0.744, 11.84, 1.189, -18.21]}, {"time": 1.6333, "y": -18.21, "curve": [2.078, 0, 2.522, 0, 2.078, -18.21, 2.522, 11.84]}, {"time": 2.9667, "y": 11.84, "curve": [3.411, 0, 3.856, 0, 3.411, 11.84, 3.856, -18.21]}, {"time": 4.3, "y": -18.21, "curve": [4.744, 0, 5.189, 0, 4.744, -18.21, 5.189, 11.84]}, {"time": 5.6333, "y": 11.84, "curve": [6.078, 0, 6.522, 0, 6.078, 11.84, 6.522, -18.21]}, {"time": 6.9667, "y": -18.21, "curve": [7.411, 0, 7.856, 0, 7.411, -18.21, 7.856, 11.84]}, {"time": 8.3, "y": 11.84, "curve": [8.744, 0, 9.189, 0, 8.744, 11.84, 9.189, -18.21]}, {"time": 9.6333, "y": -18.21, "curve": [9.979, 0, 10.324, 0, 9.979, -18.21, 10.324, -0.23]}, {"time": 10.6667, "y": 7.87}]}, "bone13": {"translate": [{"y": -6.52, "curve": [0.257, 0, 0.512, 0, 0.257, 1.91, 0.512, 11.84]}, {"time": 0.7667, "y": 11.84, "curve": [1.211, 0, 1.656, 0, 1.211, 11.84, 1.656, -18.21]}, {"time": 2.1, "y": -18.21, "curve": [2.544, 0, 2.989, 0, 2.544, -18.21, 2.989, 11.84]}, {"time": 3.4333, "y": 11.84, "curve": [3.878, 0, 4.322, 0, 3.878, 11.84, 4.322, -18.21]}, {"time": 4.7667, "y": -18.21, "curve": [5.211, 0, 5.656, 0, 5.211, -18.21, 5.656, 11.84]}, {"time": 6.1, "y": 11.84, "curve": [6.544, 0, 6.989, 0, 6.544, 11.84, 6.989, -18.21]}, {"time": 7.4333, "y": -18.21, "curve": [7.878, 0, 8.322, 0, 7.878, -18.21, 8.322, 11.84]}, {"time": 8.7667, "y": 11.84, "curve": [9.211, 0, 9.656, 0, 9.211, 11.84, 9.656, -18.21]}, {"time": 10.1, "y": -18.21, "curve": [10.29, 0, 10.479, 0, 10.29, -18.21, 10.479, -12.79]}, {"time": 10.6667, "y": -6.52}]}, "bone14": {"translate": [{"y": 1.26, "curve": [0.181, 0, 0.357, 0, 0.181, 6.96, 0.357, 11.84]}, {"time": 0.5333, "y": 11.84, "curve": [0.978, 0, 1.422, 0, 0.978, 11.84, 1.422, -18.21]}, {"time": 1.8667, "y": -18.21, "curve": [2.311, 0, 2.756, 0, 2.311, -18.21, 2.756, 11.84]}, {"time": 3.2, "y": 11.84, "curve": [3.644, 0, 4.089, 0, 3.644, 11.84, 4.089, -18.21]}, {"time": 4.5333, "y": -18.21, "curve": [4.978, 0, 5.422, 0, 4.978, -18.21, 5.422, 11.84]}, {"time": 5.8667, "y": 11.84, "curve": [6.311, 0, 6.756, 0, 6.311, 11.84, 6.756, -18.21]}, {"time": 7.2, "y": -18.21, "curve": [7.644, 0, 8.089, 0, 7.644, -18.21, 8.089, 11.84]}, {"time": 8.5333, "y": 11.84, "curve": [8.978, 0, 9.422, 0, 8.978, 11.84, 9.422, -18.21]}, {"time": 9.8667, "y": -18.21, "curve": [10.135, 0, 10.403, 0, 10.135, -18.21, 10.403, -7.42]}, {"time": 10.6667, "y": 1.26}]}, "bone15": {"translate": [{"y": -5.81, "curve": [0.444, 0, 0.889, 0, 0.444, -5.81, 0.889, 22.29]}, {"time": 1.3333, "y": 22.29, "curve": [1.778, 0, 2.222, 0, 1.778, 22.29, 2.222, -5.81]}, {"time": 2.6667, "y": -5.81, "curve": [3.111, 0, 3.556, 0, 3.111, -5.81, 3.556, 22.29]}, {"time": 4, "y": 22.29, "curve": [4.444, 0, 4.889, 0, 4.444, 22.29, 4.889, -5.81]}, {"time": 5.3333, "y": -5.81, "curve": [5.778, 0, 6.222, 0, 5.778, -5.81, 6.222, 22.29]}, {"time": 6.6667, "y": 22.29, "curve": [7.111, 0, 7.556, 0, 7.111, 22.29, 7.556, -5.81]}, {"time": 8, "y": -5.81, "curve": [8.444, 0, 8.889, 0, 8.444, -5.81, 8.889, 22.29]}, {"time": 9.3333, "y": 22.29, "curve": [9.778, 0, 10.222, 0, 9.778, 22.29, 10.222, -5.81]}, {"time": 10.6667, "y": -5.81}]}, "bone16": {"translate": [{"y": -4.49, "curve": [0.057, 0, 0.112, 0, 0.057, -5.24, 0.112, -5.81]}, {"time": 0.1667, "y": -5.81, "curve": [0.611, 0, 1.056, 0, 0.611, -5.81, 1.056, 22.29]}, {"time": 1.5, "y": 22.29, "curve": [1.944, 0, 2.389, 0, 1.944, 22.29, 2.389, -5.81]}, {"time": 2.8333, "y": -5.81, "curve": [3.278, 0, 3.722, 0, 3.278, -5.81, 3.722, 22.29]}, {"time": 4.1667, "y": 22.29, "curve": [4.611, 0, 5.056, 0, 4.611, 22.29, 5.056, -5.81]}, {"time": 5.5, "y": -5.81, "curve": [5.944, 0, 6.389, 0, 5.944, -5.81, 6.389, 22.29]}, {"time": 6.8333, "y": 22.29, "curve": [7.278, 0, 7.722, 0, 7.278, 22.29, 7.722, -5.81]}, {"time": 8.1667, "y": -5.81, "curve": [8.611, 0, 9.056, 0, 8.611, -5.81, 9.056, 22.29]}, {"time": 9.5, "y": 22.29, "curve": [9.89, 0, 10.279, 0, 9.89, 22.29, 10.279, 0.84]}, {"time": 10.6667, "y": -4.49}]}, "bone17": {"translate": [{"y": -1.31, "curve": [0.114, 0, 0.224, 0, 0.114, -3.89, 0.224, -5.81]}, {"time": 0.3333, "y": -5.81, "curve": [0.778, 0, 1.222, 0, 0.778, -5.81, 1.222, 22.29]}, {"time": 1.6667, "y": 22.29, "curve": [2.111, 0, 2.556, 0, 2.111, 22.29, 2.556, -5.81]}, {"time": 3, "y": -5.81, "curve": [3.444, 0, 3.889, 0, 3.444, -5.81, 3.889, 22.29]}, {"time": 4.3333, "y": 22.29, "curve": [4.778, 0, 5.222, 0, 4.778, 22.29, 5.222, -5.81]}, {"time": 5.6667, "y": -5.81, "curve": [6.111, 0, 6.556, 0, 6.111, -5.81, 6.556, 22.29]}, {"time": 7, "y": 22.29, "curve": [7.444, 0, 7.889, 0, 7.444, 22.29, 7.889, -5.81]}, {"time": 8.3333, "y": -5.81, "curve": [8.778, 0, 9.222, 0, 8.778, -5.81, 9.222, 22.29]}, {"time": 9.6667, "y": 22.29, "curve": [10.001, 0, 10.336, 0, 10.001, 22.29, 10.336, 6.56]}, {"time": 10.6667, "y": -1.31}]}, "bone18": {"translate": [{"y": 3.13, "curve": [0.168, 0, 0.334, 0, 0.168, -1.78, 0.334, -5.81]}, {"time": 0.5, "y": -5.81, "curve": [0.944, 0, 1.389, 0, 0.944, -5.81, 1.389, 22.29]}, {"time": 1.8333, "y": 22.29, "curve": [2.278, 0, 2.722, 0, 2.278, 22.29, 2.722, -5.81]}, {"time": 3.1667, "y": -5.81, "curve": [3.611, 0, 4.056, 0, 3.611, -5.81, 4.056, 22.29]}, {"time": 4.5, "y": 22.29, "curve": [4.944, 0, 5.389, 0, 4.944, 22.29, 5.389, -5.81]}, {"time": 5.8333, "y": -5.81, "curve": [6.278, 0, 6.722, 0, 6.278, -5.81, 6.722, 22.29]}, {"time": 7.1667, "y": 22.29, "curve": [7.611, 0, 8.056, 0, 7.611, 22.29, 8.056, -5.81]}, {"time": 8.5, "y": -5.81, "curve": [8.944, 0, 9.389, 0, 8.944, -5.81, 9.389, 22.29]}, {"time": 9.8333, "y": 22.29, "curve": [10.112, 0, 10.39, 0, 10.112, 22.29, 10.39, 11.37]}, {"time": 10.6667, "y": 3.13}]}, "bone19": {"translate": [{"y": 8.24, "curve": [0.225, 0, 0.446, 0, 0.225, 1.26, 0.446, -5.81]}, {"time": 0.6667, "y": -5.81, "curve": [1.111, 0, 1.556, 0, 1.111, -5.81, 1.556, 22.29]}, {"time": 2, "y": 22.29, "curve": [2.444, 0, 2.889, 0, 2.444, 22.29, 2.889, -5.81]}, {"time": 3.3333, "y": -5.81, "curve": [3.778, 0, 4.222, 0, 3.778, -5.81, 4.222, 22.29]}, {"time": 4.6667, "y": 22.29, "curve": [5.111, 0, 5.556, 0, 5.111, 22.29, 5.556, -5.81]}, {"time": 6, "y": -5.81, "curve": [6.444, 0, 6.889, 0, 6.444, -5.81, 6.889, 22.29]}, {"time": 7.3333, "y": 22.29, "curve": [7.778, 0, 8.222, 0, 7.778, 22.29, 8.222, -5.81]}, {"time": 8.6667, "y": -5.81, "curve": [9.111, 0, 9.556, 0, 9.111, -5.81, 9.556, 22.29]}, {"time": 10, "y": 22.29, "curve": [10.224, 0, 10.447, 0, 10.224, 22.29, 10.447, 15.31]}, {"time": 10.6667, "y": 8.24}]}, "bone20": {"translate": [{"y": 13.36, "curve": [0.279, 0, 0.556, 0, 0.279, 5.13, 0.556, -5.81]}, {"time": 0.8333, "y": -5.81, "curve": [1.278, 0, 1.722, 0, 1.278, -5.81, 1.722, 22.29]}, {"time": 2.1667, "y": 22.29, "curve": [2.611, 0, 3.056, 0, 2.611, 22.29, 3.056, -5.81]}, {"time": 3.5, "y": -5.81, "curve": [3.944, 0, 4.389, 0, 3.944, -5.81, 4.389, 22.29]}, {"time": 4.8333, "y": 22.29, "curve": [5.278, 0, 5.722, 0, 5.278, 22.29, 5.722, -5.81]}, {"time": 6.1667, "y": -5.81, "curve": [6.611, 0, 7.056, 0, 6.611, -5.81, 7.056, 22.29]}, {"time": 7.5, "y": 22.29, "curve": [7.944, 0, 8.389, 0, 7.944, 22.29, 8.389, -5.81]}, {"time": 8.8333, "y": -5.81, "curve": [9.278, 0, 9.722, 0, 9.278, -5.81, 9.722, 22.29]}, {"time": 10.1667, "y": 22.29, "curve": [10.334, 0, 10.501, 0, 10.334, 22.29, 10.501, 18.33]}, {"time": 10.6667, "y": 13.36}]}, "bone21": {"translate": [{"y": 17.8, "curve": [0.336, 0, 0.668, 0, 0.336, 9.86, 0.668, -5.81]}, {"time": 1, "y": -5.81, "curve": [1.444, 0, 1.889, 0, 1.444, -5.81, 1.889, 22.29]}, {"time": 2.3333, "y": 22.29, "curve": [2.778, 0, 3.222, 0, 2.778, 22.29, 3.222, -5.81]}, {"time": 3.6667, "y": -5.81, "curve": [4.111, 0, 4.556, 0, 4.111, -5.81, 4.556, 22.29]}, {"time": 5, "y": 22.29, "curve": [5.444, 0, 5.889, 0, 5.444, 22.29, 5.889, -5.81]}, {"time": 6.3333, "y": -5.81, "curve": [6.778, 0, 7.222, 0, 6.778, -5.81, 7.222, 22.29]}, {"time": 7.6667, "y": 22.29, "curve": [8.111, 0, 8.556, 0, 8.111, 22.29, 8.556, -5.81]}, {"time": 9, "y": -5.81, "curve": [9.444, 0, 9.889, 0, 9.444, -5.81, 9.889, 22.29]}, {"time": 10.3333, "y": 22.29, "curve": [10.446, 0, 10.559, 0, 10.446, 22.29, 10.559, 20.49]}, {"time": 10.6667, "y": 17.8}]}, "bone22": {"translate": [{"y": 20.97, "curve": [0.39, 0, 0.779, 0, 0.39, 15.54, 0.779, -5.81]}, {"time": 1.1667, "y": -5.81, "curve": [1.611, 0, 2.056, 0, 1.611, -5.81, 2.056, 22.29]}, {"time": 2.5, "y": 22.29, "curve": [2.944, 0, 3.389, 0, 2.944, 22.29, 3.389, -5.81]}, {"time": 3.8333, "y": -5.81, "curve": [4.278, 0, 4.722, 0, 4.278, -5.81, 4.722, 22.29]}, {"time": 5.1667, "y": 22.29, "curve": [5.611, 0, 6.056, 0, 5.611, 22.29, 6.056, -5.81]}, {"time": 6.5, "y": -5.81, "curve": [6.944, 0, 7.389, 0, 6.944, -5.81, 7.389, 22.29]}, {"time": 7.8333, "y": 22.29, "curve": [8.278, 0, 8.722, 0, 8.278, 22.29, 8.722, -5.81]}, {"time": 9.1667, "y": -5.81, "curve": [9.611, 0, 10.056, 0, 9.611, -5.81, 10.056, 22.29]}, {"time": 10.5, "y": 22.29, "curve": [10.556, 0, 10.613, 0, 10.556, 22.29, 10.613, 21.76]}, {"time": 10.6667, "y": 20.97}]}, "bone23": {"translate": [{"y": 22.29, "curve": [0.444, 0, 0.889, 0, 0.444, 22.29, 0.889, -5.81]}, {"time": 1.3333, "y": -5.81, "curve": [1.778, 0, 2.222, 0, 1.778, -5.81, 2.222, 22.29]}, {"time": 2.6667, "y": 22.29, "curve": [3.111, 0, 3.556, 0, 3.111, 22.29, 3.556, -5.81]}, {"time": 4, "y": -5.81, "curve": [4.444, 0, 4.889, 0, 4.444, -5.81, 4.889, 22.29]}, {"time": 5.3333, "y": 22.29, "curve": [5.778, 0, 6.222, 0, 5.778, 22.29, 6.222, -5.81]}, {"time": 6.6667, "y": -5.81, "curve": [7.111, 0, 7.556, 0, 7.111, -5.81, 7.556, 22.29]}, {"time": 8, "y": 22.29, "curve": [8.444, 0, 8.889, 0, 8.444, 22.29, 8.889, -5.81]}, {"time": 9.3333, "y": -5.81, "curve": [9.778, 0, 10.222, 0, 9.778, -5.81, 10.222, 22.29]}, {"time": 10.6667, "y": 22.29}]}, "bone24": {"translate": [{"y": 20.97, "curve": [0.057, 0, 0.112, 0, 0.057, 21.72, 0.112, 22.29]}, {"time": 0.1667, "y": 22.29, "curve": [0.611, 0, 1.056, 0, 0.611, 22.29, 1.056, -5.81]}, {"time": 1.5, "y": -5.81, "curve": [1.944, 0, 2.389, 0, 1.944, -5.81, 2.389, 22.29]}, {"time": 2.8333, "y": 22.29, "curve": [3.278, 0, 3.722, 0, 3.278, 22.29, 3.722, -5.81]}, {"time": 4.1667, "y": -5.81, "curve": [4.611, 0, 5.056, 0, 4.611, -5.81, 5.056, 22.29]}, {"time": 5.5, "y": 22.29, "curve": [5.944, 0, 6.389, 0, 5.944, 22.29, 6.389, -5.81]}, {"time": 6.8333, "y": -5.81, "curve": [7.278, 0, 7.722, 0, 7.278, -5.81, 7.722, 22.29]}, {"time": 8.1667, "y": 22.29, "curve": [8.611, 0, 9.056, 0, 8.611, 22.29, 9.056, -5.81]}, {"time": 9.5, "y": -5.81, "curve": [9.89, 0, 10.279, 0, 9.89, -5.81, 10.279, 15.64]}, {"time": 10.6667, "y": 20.97}]}, "bone25": {"translate": [{"y": 17.8, "curve": [0.114, 0, 0.224, 0, 0.114, 20.37, 0.224, 22.29]}, {"time": 0.3333, "y": 22.29, "curve": [0.778, 0, 1.222, 0, 0.778, 22.29, 1.222, -5.81]}, {"time": 1.6667, "y": -5.81, "curve": [2.111, 0, 2.556, 0, 2.111, -5.81, 2.556, 22.29]}, {"time": 3, "y": 22.29, "curve": [3.444, 0, 3.889, 0, 3.444, 22.29, 3.889, -5.81]}, {"time": 4.3333, "y": -5.81, "curve": [4.778, 0, 5.222, 0, 4.778, -5.81, 5.222, 22.29]}, {"time": 5.6667, "y": 22.29, "curve": [6.111, 0, 6.556, 0, 6.111, 22.29, 6.556, -5.81]}, {"time": 7, "y": -5.81, "curve": [7.444, 0, 7.889, 0, 7.444, -5.81, 7.889, 22.29]}, {"time": 8.3333, "y": 22.29, "curve": [8.778, 0, 9.222, 0, 8.778, 22.29, 9.222, -5.81]}, {"time": 9.6667, "y": -5.81, "curve": [10.001, 0, 10.336, 0, 10.001, -5.81, 10.336, 9.93]}, {"time": 10.6667, "y": 17.8}]}, "bone26": {"translate": [{"y": 13.36, "curve": [0.168, 0, 0.334, 0, 0.168, 18.27, 0.334, 22.29]}, {"time": 0.5, "y": 22.29, "curve": [0.944, 0, 1.389, 0, 0.944, 22.29, 1.389, -5.81]}, {"time": 1.8333, "y": -5.81, "curve": [2.278, 0, 2.722, 0, 2.278, -5.81, 2.722, 22.29]}, {"time": 3.1667, "y": 22.29, "curve": [3.611, 0, 4.056, 0, 3.611, 22.29, 4.056, -5.81]}, {"time": 4.5, "y": -5.81, "curve": [4.944, 0, 5.389, 0, 4.944, -5.81, 5.389, 22.29]}, {"time": 5.8333, "y": 22.29, "curve": [6.278, 0, 6.722, 0, 6.278, 22.29, 6.722, -5.81]}, {"time": 7.1667, "y": -5.81, "curve": [7.611, 0, 8.056, 0, 7.611, -5.81, 8.056, 22.29]}, {"time": 8.5, "y": 22.29, "curve": [8.944, 0, 9.389, 0, 8.944, 22.29, 9.389, -5.81]}, {"time": 9.8333, "y": -5.81, "curve": [10.112, 0, 10.39, 0, 10.112, -5.81, 10.39, 5.12]}, {"time": 10.6667, "y": 13.36}]}, "bone27": {"translate": [{"y": -1.31, "curve": [0.114, 0, 0.224, 0, 0.114, -3.89, 0.224, -5.81]}, {"time": 0.3333, "y": -5.81, "curve": [0.778, 0, 1.222, 0, 0.778, -5.81, 1.222, 22.29]}, {"time": 1.6667, "y": 22.29, "curve": [2.111, 0, 2.556, 0, 2.111, 22.29, 2.556, -5.81]}, {"time": 3, "y": -5.81, "curve": [3.444, 0, 3.889, 0, 3.444, -5.81, 3.889, 22.29]}, {"time": 4.3333, "y": 22.29, "curve": [4.778, 0, 5.222, 0, 4.778, 22.29, 5.222, -5.81]}, {"time": 5.6667, "y": -5.81, "curve": [6.111, 0, 6.556, 0, 6.111, -5.81, 6.556, 22.29]}, {"time": 7, "y": 22.29, "curve": [7.444, 0, 7.889, 0, 7.444, 22.29, 7.889, -5.81]}, {"time": 8.3333, "y": -5.81, "curve": [8.778, 0, 9.222, 0, 8.778, -5.81, 9.222, 22.29]}, {"time": 9.6667, "y": 22.29, "curve": [10.001, 0, 10.336, 0, 10.001, 22.29, 10.336, 6.56]}, {"time": 10.6667, "y": -1.31}]}, "bone28": {"translate": [{"y": 3.13, "curve": [0.168, 0, 0.334, 0, 0.168, -1.78, 0.334, -5.81]}, {"time": 0.5, "y": -5.81, "curve": [0.944, 0, 1.389, 0, 0.944, -5.81, 1.389, 22.29]}, {"time": 1.8333, "y": 22.29, "curve": [2.278, 0, 2.722, 0, 2.278, 22.29, 2.722, -5.81]}, {"time": 3.1667, "y": -5.81, "curve": [3.611, 0, 4.056, 0, 3.611, -5.81, 4.056, 22.29]}, {"time": 4.5, "y": 22.29, "curve": [4.944, 0, 5.389, 0, 4.944, 22.29, 5.389, -5.81]}, {"time": 5.8333, "y": -5.81, "curve": [6.278, 0, 6.722, 0, 6.278, -5.81, 6.722, 22.29]}, {"time": 7.1667, "y": 22.29, "curve": [7.611, 0, 8.056, 0, 7.611, 22.29, 8.056, -5.81]}, {"time": 8.5, "y": -5.81, "curve": [8.944, 0, 9.389, 0, 8.944, -5.81, 9.389, 22.29]}, {"time": 9.8333, "y": 22.29, "curve": [10.112, 0, 10.39, 0, 10.112, 22.29, 10.39, 11.37]}, {"time": 10.6667, "y": 3.13}]}, "bone29": {"translate": [{"y": 8.24, "curve": [0.225, 0, 0.446, 0, 0.225, 1.26, 0.446, -5.81]}, {"time": 0.6667, "y": -5.81, "curve": [1.111, 0, 1.556, 0, 1.111, -5.81, 1.556, 22.29]}, {"time": 2, "y": 22.29, "curve": [2.444, 0, 2.889, 0, 2.444, 22.29, 2.889, -5.81]}, {"time": 3.3333, "y": -5.81, "curve": [3.778, 0, 4.222, 0, 3.778, -5.81, 4.222, 22.29]}, {"time": 4.6667, "y": 22.29, "curve": [5.111, 0, 5.556, 0, 5.111, 22.29, 5.556, -5.81]}, {"time": 6, "y": -5.81, "curve": [6.444, 0, 6.889, 0, 6.444, -5.81, 6.889, 22.29]}, {"time": 7.3333, "y": 22.29, "curve": [7.778, 0, 8.222, 0, 7.778, 22.29, 8.222, -5.81]}, {"time": 8.6667, "y": -5.81, "curve": [9.111, 0, 9.556, 0, 9.111, -5.81, 9.556, 22.29]}, {"time": 10, "y": 22.29, "curve": [10.224, 0, 10.447, 0, 10.224, 22.29, 10.447, 15.31]}, {"time": 10.6667, "y": 8.24}]}, "bone30": {"translate": [{"y": 13.36, "curve": [0.279, 0, 0.556, 0, 0.279, 5.13, 0.556, -5.81]}, {"time": 0.8333, "y": -5.81, "curve": [1.278, 0, 1.722, 0, 1.278, -5.81, 1.722, 22.29]}, {"time": 2.1667, "y": 22.29, "curve": [2.611, 0, 3.056, 0, 2.611, 22.29, 3.056, -5.81]}, {"time": 3.5, "y": -5.81, "curve": [3.944, 0, 4.389, 0, 3.944, -5.81, 4.389, 22.29]}, {"time": 4.8333, "y": 22.29, "curve": [5.278, 0, 5.722, 0, 5.278, 22.29, 5.722, -5.81]}, {"time": 6.1667, "y": -5.81, "curve": [6.611, 0, 7.056, 0, 6.611, -5.81, 7.056, 22.29]}, {"time": 7.5, "y": 22.29, "curve": [7.944, 0, 8.389, 0, 7.944, 22.29, 8.389, -5.81]}, {"time": 8.8333, "y": -5.81, "curve": [9.278, 0, 9.722, 0, 9.278, -5.81, 9.722, 22.29]}, {"time": 10.1667, "y": 22.29, "curve": [10.334, 0, 10.501, 0, 10.334, 22.29, 10.501, 18.33]}, {"time": 10.6667, "y": 13.36}]}, "bone31": {"translate": [{"y": 17.8, "curve": [0.336, 0, 0.668, 0, 0.336, 9.86, 0.668, -5.81]}, {"time": 1, "y": -5.81, "curve": [1.444, 0, 1.889, 0, 1.444, -5.81, 1.889, 22.29]}, {"time": 2.3333, "y": 22.29, "curve": [2.778, 0, 3.222, 0, 2.778, 22.29, 3.222, -5.81]}, {"time": 3.6667, "y": -5.81, "curve": [4.111, 0, 4.556, 0, 4.111, -5.81, 4.556, 22.29]}, {"time": 5, "y": 22.29, "curve": [5.444, 0, 5.889, 0, 5.444, 22.29, 5.889, -5.81]}, {"time": 6.3333, "y": -5.81, "curve": [6.778, 0, 7.222, 0, 6.778, -5.81, 7.222, 22.29]}, {"time": 7.6667, "y": 22.29, "curve": [8.111, 0, 8.556, 0, 8.111, 22.29, 8.556, -5.81]}, {"time": 9, "y": -5.81, "curve": [9.444, 0, 9.889, 0, 9.444, -5.81, 9.889, 22.29]}, {"time": 10.3333, "y": 22.29, "curve": [10.446, 0, 10.559, 0, 10.446, 22.29, 10.559, 20.49]}, {"time": 10.6667, "y": 17.8}]}, "bone32": {"translate": [{"y": 20.97, "curve": [0.39, 0, 0.779, 0, 0.39, 15.54, 0.779, -5.81]}, {"time": 1.1667, "y": -5.81, "curve": [1.611, 0, 2.056, 0, 1.611, -5.81, 2.056, 22.29]}, {"time": 2.5, "y": 22.29, "curve": [2.944, 0, 3.389, 0, 2.944, 22.29, 3.389, -5.81]}, {"time": 3.8333, "y": -5.81, "curve": [4.278, 0, 4.722, 0, 4.278, -5.81, 4.722, 22.29]}, {"time": 5.1667, "y": 22.29, "curve": [5.611, 0, 6.056, 0, 5.611, 22.29, 6.056, -5.81]}, {"time": 6.5, "y": -5.81, "curve": [6.944, 0, 7.389, 0, 6.944, -5.81, 7.389, 22.29]}, {"time": 7.8333, "y": 22.29, "curve": [8.278, 0, 8.722, 0, 8.278, 22.29, 8.722, -5.81]}, {"time": 9.1667, "y": -5.81, "curve": [9.611, 0, 10.056, 0, 9.611, -5.81, 10.056, 22.29]}, {"time": 10.5, "y": 22.29, "curve": [10.556, 0, 10.613, 0, 10.556, 22.29, 10.613, 21.76]}, {"time": 10.6667, "y": 20.97}]}, "bone33": {"translate": [{"y": 22.29, "curve": [0.444, 0, 0.889, 0, 0.444, 22.29, 0.889, -5.81]}, {"time": 1.3333, "y": -5.81, "curve": [1.778, 0, 2.222, 0, 1.778, -5.81, 2.222, 22.29]}, {"time": 2.6667, "y": 22.29, "curve": [3.111, 0, 3.556, 0, 3.111, 22.29, 3.556, -5.81]}, {"time": 4, "y": -5.81, "curve": [4.444, 0, 4.889, 0, 4.444, -5.81, 4.889, 22.29]}, {"time": 5.3333, "y": 22.29, "curve": [5.778, 0, 6.222, 0, 5.778, 22.29, 6.222, -5.81]}, {"time": 6.6667, "y": -5.81, "curve": [7.111, 0, 7.556, 0, 7.111, -5.81, 7.556, 22.29]}, {"time": 8, "y": 22.29, "curve": [8.444, 0, 8.889, 0, 8.444, 22.29, 8.889, -5.81]}, {"time": 9.3333, "y": -5.81, "curve": [9.778, 0, 10.222, 0, 9.778, -5.81, 10.222, 22.29]}, {"time": 10.6667, "y": 22.29}]}, "bone34": {"translate": [{"y": 20.97, "curve": [0.057, 0, 0.112, 0, 0.057, 21.72, 0.112, 22.29]}, {"time": 0.1667, "y": 22.29, "curve": [0.611, 0, 1.056, 0, 0.611, 22.29, 1.056, -5.81]}, {"time": 1.5, "y": -5.81, "curve": [1.944, 0, 2.389, 0, 1.944, -5.81, 2.389, 22.29]}, {"time": 2.8333, "y": 22.29, "curve": [3.278, 0, 3.722, 0, 3.278, 22.29, 3.722, -5.81]}, {"time": 4.1667, "y": -5.81, "curve": [4.611, 0, 5.056, 0, 4.611, -5.81, 5.056, 22.29]}, {"time": 5.5, "y": 22.29, "curve": [5.944, 0, 6.389, 0, 5.944, 22.29, 6.389, -5.81]}, {"time": 6.8333, "y": -5.81, "curve": [7.278, 0, 7.722, 0, 7.278, -5.81, 7.722, 22.29]}, {"time": 8.1667, "y": 22.29, "curve": [8.611, 0, 9.056, 0, 8.611, 22.29, 9.056, -5.81]}, {"time": 9.5, "y": -5.81, "curve": [9.89, 0, 10.279, 0, 9.89, -5.81, 10.279, 15.64]}, {"time": 10.6667, "y": 20.97}]}, "bone35": {"translate": [{"y": 17.8, "curve": [0.114, 0, 0.224, 0, 0.114, 20.37, 0.224, 22.29]}, {"time": 0.3333, "y": 22.29, "curve": [0.778, 0, 1.222, 0, 0.778, 22.29, 1.222, -5.81]}, {"time": 1.6667, "y": -5.81, "curve": [2.111, 0, 2.556, 0, 2.111, -5.81, 2.556, 22.29]}, {"time": 3, "y": 22.29, "curve": [3.444, 0, 3.889, 0, 3.444, 22.29, 3.889, -5.81]}, {"time": 4.3333, "y": -5.81, "curve": [4.778, 0, 5.222, 0, 4.778, -5.81, 5.222, 22.29]}, {"time": 5.6667, "y": 22.29, "curve": [6.111, 0, 6.556, 0, 6.111, 22.29, 6.556, -5.81]}, {"time": 7, "y": -5.81, "curve": [7.444, 0, 7.889, 0, 7.444, -5.81, 7.889, 22.29]}, {"time": 8.3333, "y": 22.29, "curve": [8.778, 0, 9.222, 0, 8.778, 22.29, 9.222, -5.81]}, {"time": 9.6667, "y": -5.81, "curve": [10.001, 0, 10.336, 0, 10.001, -5.81, 10.336, 9.93]}, {"time": 10.6667, "y": 17.8}]}, "bone36": {"translate": [{"y": 13.36, "curve": [0.168, 0, 0.334, 0, 0.168, 18.27, 0.334, 22.29]}, {"time": 0.5, "y": 22.29, "curve": [0.944, 0, 1.389, 0, 0.944, 22.29, 1.389, -5.81]}, {"time": 1.8333, "y": -5.81, "curve": [2.278, 0, 2.722, 0, 2.278, -5.81, 2.722, 22.29]}, {"time": 3.1667, "y": 22.29, "curve": [3.611, 0, 4.056, 0, 3.611, 22.29, 4.056, -5.81]}, {"time": 4.5, "y": -5.81, "curve": [4.944, 0, 5.389, 0, 4.944, -5.81, 5.389, 22.29]}, {"time": 5.8333, "y": 22.29, "curve": [6.278, 0, 6.722, 0, 6.278, 22.29, 6.722, -5.81]}, {"time": 7.1667, "y": -5.81, "curve": [7.611, 0, 8.056, 0, 7.611, -5.81, 8.056, 22.29]}, {"time": 8.5, "y": 22.29, "curve": [8.944, 0, 9.389, 0, 8.944, 22.29, 9.389, -5.81]}, {"time": 9.8333, "y": -5.81, "curve": [10.112, 0, 10.39, 0, 10.112, -5.81, 10.39, 5.12]}, {"time": 10.6667, "y": 13.36}]}, "boat_A": {"rotate": [{"time": 4.5, "curve": [4.6, 0, 4.7, 1.1]}, {"time": 4.8, "value": 1.1, "curve": [4.911, 1.1, 5.022, -1.34]}, {"time": 5.1333, "value": -1.34, "curve": [5.244, -1.34, 5.356, 1.89]}, {"time": 5.4667, "value": 1.89, "curve": [5.578, 1.89, 5.689, 0]}, {"time": 5.8}], "translate": [{"time": 4.3333, "curve": [4.389, 0, 4.444, 0, 4.389, 0, 4.444, -10.13]}, {"time": 4.5, "y": -10.13, "curve": [4.6, 0, 4.7, -3.69, 4.6, -10.13, 4.7, 7.76]}, {"time": 4.8, "x": -3.69, "y": 7.76, "curve": [4.911, -3.69, 5.022, 2.21, 4.911, 7.76, 5.022, -4.96]}, {"time": 5.1333, "x": 2.21, "y": -4.96, "curve": [5.244, 2.21, 5.356, -1.48, 5.244, -4.96, 5.356, 7.76]}, {"time": 5.4667, "x": -1.48, "y": 7.76, "curve": [5.578, -1.48, 5.689, 0, 5.578, 7.76, 5.689, 0]}, {"time": 5.8}]}}}}}