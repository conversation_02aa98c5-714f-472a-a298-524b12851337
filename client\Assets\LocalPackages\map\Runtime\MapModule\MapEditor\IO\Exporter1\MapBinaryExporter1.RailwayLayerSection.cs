﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveRailwayLayer(BinaryWriter writer, RailwayLayer layer)
        {
            BeginSection(MapDataSectionType.RailwayLayer, writer);

            writer.Write(VersionSetting.RailwayLayerStructVersion);

            //-----------------version 1 start---------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveRailObjectDataV1(writer, objData as RailObjectData);
            }

            //save map layer lod config
            SaveRailwayLayerLODConfigV1(writer, layer.layerData);
            SaveModelLODGroupManagerV1(writer, layer.layerData.lodGroupManager);

            writer.Write(layer.railLayerData.railCount);
            writer.Write(layer.railLayerData.railWidth);
            writer.Write(layer.railLayerData.railTotalLength);
            Utils.WriteVector3(writer, layer.railLayerData.railwayCenter);
            writer.Write(layer.railLayerData.railPrefabLength);
            //-----------------version 1 end---------------------------
            //-----------------version 2 start------------------------------
            SaveRailwayLayerLODConfigV2(writer, layer.GetLayerData());
            //-----------------version 2 end------------------------------
        }

        void SaveRailObjectDataV1(BinaryWriter writer, RailObjectData data)
        {
            var railData = data as RailObjectData;
            bool isDefaultRotation = (data.GetRotation() == Quaternion.identity);
            bool isDefaultScale = (data.GetScale() == Vector3.one);

            mIDExport.Export(writer, data.id);
            Utils.WriteVector3(writer, data.GetPosition());
            writer.Write(isDefaultRotation);
            if (isDefaultRotation == false)
            {
                Utils.WriteQuaternion(writer, data.GetRotation());
            }
            writer.Write(isDefaultScale);
            if (isDefaultScale == false)
            {
                Utils.WriteVector3(writer, data.GetScale());
            }

            mIDExport.Export(writer, railData.GetModelTemplateID());
            writer.Write((int)railData.type);
            writer.Write(railData.isGroupLeader);
            int id = railData.GetModelLODGroupID();
            mIDExport.Export(writer, id);
            writer.Write(railData.railIndex);
            writer.Write(railData.segmentIndex);
        }

        void SaveModelLODGroupManagerV1(BinaryWriter writer, ModelLODGroupManager groupManager)
        {
            int nGroups = groupManager.groups.Count;
            writer.Write(nGroups);

            for (int i = 0; i < nGroups; ++i)
            {
                var group = groupManager.groups[i];
                mIDExport.Export(writer, group.id);

                writer.Write(group.combineModels);
                mIDExport.Export(writer, group.leaderObjectID);
                writer.Write(group.lod);
            }
        }

        void SaveRailwayLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SaveRailwayLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif