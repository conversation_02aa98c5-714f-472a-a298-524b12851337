﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Linq;
using System.IO.Compression;

namespace TFW.Map
{
    [Black]
    public static class EditorUtils
    {
        public static MapLayerTileMeshCreator GetMapLayerTileMeshCreator(GridType shape)
        {
            switch (shape)
            {
                case GridType.Hexagonal:
                    return new MapLayerHexagonalTileMeshCreator();
                case GridType.Rectangle:
                    return new MapLayerRectangleTileMeshCreator();
                default:
                    Debug.Assert(false, "Unknown grid type");
                    break;
            }
            return null;
        }

        public static MapLayerBoundsCreator GetMapLayerBoundsCreator()
        {
            return new MapLayerXZRectangleBoundsCreator();
        }

        public static MapLayerGridCreator GetMapLayerGridCreator(GridType shape)
        {
            switch (shape)
            {
                case GridType.Hexagonal:
                    return new MapLayerXZHexagonalGridCreator();
                case GridType.Rectangle:
                    return new MapLayerXZRectangleGridCreator();
                default:
                    Debug.Assert(false, "Unknown grid type");
                    break;
            }
            return null;
        }

        public static Vector2 ConvertScreenPosition(Vector2 screenPos, Camera camera)
        {
            if (camera == null)
            {
                return Vector2.zero;
            }
            return HandleUtility.GUIPointToScreenPixelCoordinate(screenPos);
        }

        public static Vector3 FromEditorScreenToWorldPosition(Vector2 screenPos, Camera camera, float planeHeight = 0)
        {
            screenPos = ConvertScreenPosition(screenPos, camera);
            var worldPos = Utils.FromScreenToWorldPosition(screenPos, camera, planeHeight);
            return worldPos;
        }

        public static string ConvertSlash(string val)
        {
            return val.Replace('/', '\\');
        }

        public static void OpenFolder(string folderPath)
        {
            if (folderPath.Length > 0)
            {
                folderPath = ConvertSlash(folderPath);
                System.Diagnostics.Process.Start("explorer.exe", folderPath);
            }
        }

        public static bool PointInPolygon2D(Vector3 p, Vector3[] polygon)
        {
            if (polygon == null || polygon.Length < 3)
            {
                return false;
            }

            int numVerts = polygon.Length;
            Vector3 p0 = polygon[numVerts - 1];
            bool bYFlag0 = (p0.z >= p.z);

            bool bInside = false;
            for (int j = 0; j < numVerts; ++j)
            {
                Vector3 p1 = polygon[j];
                bool bYFlag1 = (p1.z >= p.z);
                if (bYFlag0 != bYFlag1)
                {
                    if (((p1.z - p.z) * (p0.x - p1.x) >= (p1.x - p.x) * (p0.z - p1.z)) == bYFlag1)
                    {
                        bInside = !bInside;
                    }
                }

                // Move to the next pair of vertices, retaining info as possible.
                bYFlag0 = bYFlag1;
                p0 = p1;
            }

            return bInside;
        }

        public static bool PointInPolygon2D(Vector3 p, List<Vector3> polygon)
        {
            if (polygon == null || polygon.Count < 3)
            {
                return false;
            }

            int numVerts = polygon.Count;
            Vector3 p0 = polygon[numVerts - 1];
            bool bYFlag0 = (p0.z >= p.z);

            bool bInside = false;
            for (int j = 0; j < numVerts; ++j)
            {
                Vector3 p1 = polygon[j];
                bool bYFlag1 = (p1.z >= p.z);
                if (bYFlag0 != bYFlag1)
                {
                    if (((p1.z - p.z) * (p0.x - p1.x) >= (p1.x - p.x) * (p0.z - p1.z)) == bYFlag1)
                    {
                        bInside = !bInside;
                    }
                }

                // Move to the next pair of vertices, retaining info as possible.
                bYFlag0 = bYFlag1;
                p0 = p1;
            }

            return bInside;
        }

        public static float ClampRotation(float rot)
        {
            while (rot > 360.0f)
            {
                rot -= 360.0f;
            }
            while (rot < 0)
            {
                rot += 360.0f;
            }
            return rot;
        }

        public static void CallAllMethodWithAttribute(Type attributeType, params object[] args)
        {
            List<Type> types = GetAllTypes();

            foreach (var method in (
                from type in types
                from method in type.GetMethods(BindingFlags.Static | BindingFlags.NonPublic)
                where method.IsDefined(attributeType, false)
                select method))
            {
                method.Invoke(null, args);
            }
        }

        public static List<Type> GetAllTypes()
        {
            List<Type> allTypes = new List<Type>();
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            for (int i = 0; i < assemblies.Length; i++)
            {
                try
                {
                    allTypes.AddRange(assemblies[i].GetTypes());
                }
                catch (Exception)
                {
                }
            }

            return allTypes;
        }

        public static void CenterWindow(EditorWindow window, float minWidth = -1, float minHeight = -1, float maxWidth = -1, float maxHeight = -1)
        {
            if (minWidth > 0)
            {
                window.minSize = new Vector2(minWidth, minHeight);
                window.maxSize = new Vector2(maxWidth, maxHeight);
            }
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;
        }

        public static void AddInverseCircleCollsion(MapCollisionLayer layer, float segment, float radius, float vertexDisplayRadius)
        {
            CompoundAction actions = new CompoundAction("Add Map Circle Border Collision");
            var center = new Vector3(Map.currentMap.mapWidth * 0.5f, 0, Map.currentMap.mapHeight * 0.5f);

            Vector2[] sign = new Vector2[]
            {
                    new Vector2(1, 1),
                    new Vector2(1, -1),
                    new Vector2(-1, -1),
                    new Vector2(-1, 1),
            };
            Vector2[] offset = new Vector2[]
            {
                    new Vector2(0, 0),
                    new Vector2(0, 0),
                    new Vector2(0, 0),
                    new Vector2(0, 0),
            };
            for (int q = 0; q < 4; ++q)
            {
                List<Vector3> quaterVertices = new List<Vector3>();
                float deltaAngle = 90.0f / (segment - 1);
                for (int i = 0; i < segment; ++i)
                {
                    float angle = q * 90 + deltaAngle * i;
                    float z = Mathf.Cos(angle * Mathf.Deg2Rad) * radius + offset[q].y;
                    float x = Mathf.Sin(angle * Mathf.Deg2Rad) * radius + offset[q].x;
                    float y = 0;
                    quaterVertices.Add(new Vector3(x, y, z) + center);
                }
                quaterVertices.Add(new Vector3(sign[q].x * radius, 0, sign[q].y * radius) + center);

                var action = new ActionAddMapCollision(layer.id, Map.currentMap.nextCustomObjectID, quaterVertices, false, 0, 0, 0);
                actions.Add(action);
            }
            ActionManager.instance.PushAction(actions);
        }

        public static DrawPolygon CreateDrawPolygon(string name, List<Vector3> vertices, float radius = 1, bool drawVertex = true)
        {
            var obj = new GameObject(name);
            var dp = obj.AddComponent<DrawPolygon>();
            dp.SetVertices(vertices);
            dp.radius = radius;
            dp.drawVertex = drawVertex;
            return dp;
        }

        public static DrawLineStrip CreateDrawLineStrip(string name, List<Vector3> vertices, float radius = 1, bool drawVertex = true)
        {
            var obj = new GameObject(name);
            var dp = obj.AddComponent<DrawLineStrip>();
            dp.SetVertices(vertices);
            dp.radius = radius;
            dp.drawVertex = drawVertex;
            return dp;
        }

        public static float CalculatePolygonArea(List<Vector3> polygon)
        {
            int n = polygon.Count;
            float area = 0;         // Accumulates area in the loop
            int j = n - 1;  // The last vertex is the 'previous' one to the first

            for (int i = 0; i < n; i++)
            {
                area = area + (polygon[j].x + polygon[i].x) * (polygon[j].z - polygon[i].z);
                j = i;  //j is previous vertex to i
            }
            return Mathf.Abs(area) / 2;
        }

        public static float CalculatePolygonArea(Vector3[] polygon)
        {
            int n = polygon.Length;
            float area = 0;         // Accumulates area in the loop
            int j = n - 1;  // The last vertex is the 'previous' one to the first

            for (int i = 0; i < n; i++)
            {
                area = area + (polygon[j].x + polygon[i].x) * (polygon[j].z - polygon[i].z);
                j = i;  //j is previous vertex to i
            }
            return Mathf.Abs(area) / 2;
        }

        public static InputDialog CreateInputDialog(string title, bool setSize = true)
        {
            var inputDialog = EditorWindow.GetWindow<InputDialog>(title);
            if (setSize)
            {
                inputDialog.minSize = new Vector2(200, 500);
                inputDialog.maxSize = new Vector2(300, 500);
            }
            var position = inputDialog.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            inputDialog.position = position;

            return inputDialog;
        }

        public static T CreateDialog<T>(string title, bool setSize = true) where T : EditorWindow
        {
            var dialog = EditorWindow.GetWindow<T>(title);
            if (setSize)
            {
                dialog.minSize = new Vector2(200, 500);
                dialog.maxSize = new Vector2(300, 500);
            }
            var position = dialog.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dialog.position = position;

            return dialog;
        }

        public static string GetDefaultRiverMaterialPath()
        {
            var assetGUIDs = AssetDatabase.FindAssets("_default_water_mtl t:Material");
            if (assetGUIDs.Length > 0)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGUIDs[0]);
                return assetPath;
            }
            return "";
        }

        public static RiverSectionData CreateRiverSectionData(PolygonRiverSectionData section, Material riverMaterial)
        {
            var sectionData = new RiverSectionData();
            sectionData.id = section.id;
            if (section.texture != null)
            {
                sectionData.textureData = section.texture.GetPixels();
            }
            sectionData.outline = section.outlineCopy;
            sectionData.materialPropeties = UnityEngine.Object.Instantiate<Material>(riverMaterial);
            return sectionData;
        }

        public static T PopupDialog<T>(string title, bool limitSize = true) where T : EditorWindow
        {
            var dlg = EditorWindow.GetWindow<T>(title);
            if (limitSize)
            {
                dlg.minSize = new Vector2(200, 100);
                dlg.maxSize = new Vector2(300, 100);
            }
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            return dlg;
        }

        public static void SavePrefab(string outputFolder, string name, GameObject obj)
        {
            if (obj == null || string.IsNullOrEmpty(outputFolder) || string.IsNullOrEmpty(name))
            {
                return;
            }

            var newObj = UnityEngine.Object.Instantiate<GameObject>(obj);
            //save mesh
            string meshPath = outputFolder + "/" + name + ".asset";
            var mesh = obj.GetComponent<MeshFilter>().sharedMesh;
            AssetDatabase.CreateAsset(mesh, meshPath);

            //save prefab
            string prefabPath = outputFolder + "/" + name + ".prefab";
            PrefabUtility.SaveAsPrefabAsset(newObj, prefabPath);

            UnityEngine.Object.DestroyImmediate(newObj);
        }

        /// https://support.unity3d.com/hc/en-us/articles/206486626-How-can-I-get-pixels-from-unreadable-textures-
        public static Texture2D CreateTexture(string assetPath, bool clamp)
        {
            var target = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
            if (target == null)
            {
                return null;
            }
            RenderTexture tmp = RenderTexture.GetTemporary(
                          target.width, target.height,
                          0,
                          RenderTextureFormat.Default, RenderTextureReadWrite.Linear);
            var oldMode = target.wrapMode;
            if (clamp)
            {
                target.wrapMode = TextureWrapMode.Clamp;
            }
            Graphics.Blit(target, tmp);
            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = tmp;
            if (clamp)
            {
                target.wrapMode = oldMode;
            }

            Texture2D myTexture2D = new Texture2D(target.width, target.height, TextureFormat.RGBA32, false);
            myTexture2D.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);
            myTexture2D.Apply();
            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(tmp);

            return myTexture2D;
        }

        public static List<T> FindAssetsByType<T>() where T : UnityEngine.Object
        {
            List<T> assets = new List<T>();
            string[] guids = AssetDatabase.FindAssets(string.Format("t:{0}", typeof(T)));
            for (int i = 0; i < guids.Length; i++)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guids[i]);
                T asset = AssetDatabase.LoadAssetAtPath<T>(assetPath);
                if (asset != null)
                {
                    assets.Add(asset);
                }
            }
            return assets;
        }

        public static void SelectFolder(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            path = Utils.ConvertToUnityAssetsPath(path);

            // Check the path has no '/' at the end, if it dose remove it,
            // Obviously in this example it doesn't but it might
            // if your getting the path some other way.

            if (path[path.Length - 1] == '/')
                path = path.Substring(0, path.Length - 1);

            // Load object
            UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath(path, typeof(UnityEngine.Object));

            // Select the object in the project folder
            Selection.activeObject = obj;

            // Also flash the folder yellow to highlight it
            EditorGUIUtility.PingObject(obj);
        }

        public static Color32 Color32Field(string label, string tooltip, Color32 color)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label(new GUIContent(label, tooltip));
            GUILayout.Label("R");
            var r = EditorGUILayout.IntField(color.r);
            GUILayout.Label("G");
            var g = EditorGUILayout.IntField(color.g);
            GUILayout.Label("B");
            var b = EditorGUILayout.IntField(color.b);

            r = Mathf.Clamp(r, 0, 255);
            g = Mathf.Clamp(g, 0, 255);
            b = Mathf.Clamp(b, 0, 255);
            color = new Color32((byte)r, (byte)g, (byte)b, 255);
            EditorGUILayout.ColorField(color);
            EditorGUILayout.EndHorizontal();
            return color;
        }

        public static Vector2 Vector2Field(string xLabel, string xLabelTooltip, string yLabel, string yLabelTooltip, Vector2 val)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent(xLabel, xLabelTooltip), GUILayout.MaxWidth(100));
            float x = EditorGUILayout.FloatField(val.x);
            EditorGUILayout.LabelField(new GUIContent(yLabel, yLabelTooltip), GUILayout.MaxWidth(100));
            float y = EditorGUILayout.FloatField(val.y);
            EditorGUILayout.EndHorizontal();
            return new Vector2(x, y);
        }

        public static string FolderSelectionField(string name, string curPath)
        {
            EditorGUILayout.BeginHorizontal();
            curPath = EditorGUILayout.TextField(name, curPath);
            if (GUILayout.Button("Select"))
            {
                curPath = EditorUtility.OpenFolderPanel($"Select {name}", curPath, "");
            }
            if (GUILayout.Button("Find"))
            {
                SelectFolder(curPath);
            }
            EditorGUILayout.EndHorizontal();
            return curPath;
        }

        public static void ShowProgressiveBar(bool showProgressBar, string title, string info, float progress)
        {
            if (showProgressBar)
            {
                EditorUtility.DisplayProgressBar(title, info, progress);
            }
        }

        public static void CompressStream(Stream originalStream)
        {
#if false
            MemoryStream compressedStreamData = new MemoryStream();
            DeflateStream gzipStream = new DeflateStream(compressedStreamData, CompressionMode.Compress);

            var bytes = compressStream.ToArray();
            //记录原来的文件大小
            Utils.WriteAndJump(headerWriter, originalFileSizePos, bytes.Length);
            //压缩
            gzipStream.Write(bytes, 0, bytes.Length);
            gzipStream.Close();

            var comparessedBytes = compressedStreamData.ToArray();
            FileStream file = new FileStream(dataPath, FileMode.Create);
            //记录压缩后文件的大小
            Utils.WriteAndJump(headerWriter, compressFileSizePos, comparessedBytes.Length);

            var headerData = stream.ToArray();
            file.Write(headerData, 0, headerData.Length);
            file.Write(comparessedBytes, 0, comparessedBytes.Length);
            file.Close();
#endif
        }
    }
}

#endif