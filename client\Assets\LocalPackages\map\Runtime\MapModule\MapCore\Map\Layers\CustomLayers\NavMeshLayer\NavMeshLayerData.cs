﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class NavMeshData
    {
        public NavMeshData(Vector3[] vertices, int[] indices, ushort[] triangleTypes, bool[] triangleStates)
        {
            this.vertices = vertices;
            this.indices = indices;
            this.triangleStates = triangleStates;
            this.triangleTypes = triangleTypes;
        }

        public Vector3[] vertices;
        public int[] indices;
        //每个三角形的类型
        public ushort[] triangleTypes;
        //每个三角形的状态
        public bool[] triangleStates;
    }

    public enum TriangleWindingOrder
    {
        Unknown,
        Clockwise,
        CounterClockwise,
    }

    [Black]
    public class NavMeshLayerData : MapLayerData
    {
        public NavMeshLayerData(MapLayerDataHeader header, Map map, NavMeshData[] navMeshDatas, bool oceanAreaWalkable) : base(header, null, map)
        {
            mNavMeshDatas = navMeshDatas;
            mOceanAreaWalkable = oceanAreaWalkable;
            if (navMeshDatas != null)
            {
                CheckWindingOrder(navMeshDatas[0]);
            }
        }

        public override void OnDestroy()
        {
            if (mNavMgr != null)
            {
                mNavMgr.OnDestroy();
                mNavMgr = null;
            }
        }

        public override bool isGameLayer => false;

        public override void RefreshObjectsInViewport() { }
        public override bool UpdateViewport(Rect newViewport, float newZoom) { return false; }
        public override bool Contains(int objectID)
        {
            return false;
        }

        public void CreateNavMeshData(NavMeshBlock[] meshies)
        {
            mNavMeshDatas = null;
            if (meshies != null)
            {
                mNavMeshDatas = new NavMeshData[meshies.Length];
                for (int i = 0; i < mNavMeshDatas.Length; ++i)
                {
                    mNavMeshDatas[i] = new NavMeshData(meshies[i].vertices, meshies[i].indices, meshies[i].triangleTypes, meshies[i].triangleStates);
                }

                CheckWindingOrder(mNavMeshDatas[0]);

                InitNavigation(mNavMeshDatas[0].vertices, mNavMeshDatas[0].indices, mNavMeshDatas[0].triangleTypes, mNavMeshDatas[0].triangleStates);
            }
        }

        void CheckWindingOrder(NavMeshData data)
        {
            if (data.indices == null || data.indices.Length == 0)
            {
                return;
            }
            var v0 = data.indices[0];
            var v1 = data.indices[1];
            var v2 = data.indices[2];
            var p0 = data.vertices[v0];
            var p1 = data.vertices[v1];
            var p2 = data.vertices[v2];
            var p10 = p1 - p0;
            var p20 = p2 - p0;
            var c = Vector3.Cross(p10, p20);
            if (c.y > 0)
            {
                mWindingOrder = TriangleWindingOrder.Clockwise;
            }
            else
            {
                mWindingOrder = TriangleWindingOrder.CounterClockwise;
            }
        }

        Nav.MeshCfg CreateMeshConfig(Vector3[] vertices, int[] indices, ushort[] triangleTypes, bool[] triangleStates)
        {
            List<int> regionTriangleEndIndex;
            List<int> regionIDs;
            Utils.CreateRegions(indices, triangleTypes, out regionTriangleEndIndex, out regionIDs);

            bool visible = (Map.currentMap as EditorMap).navMeshRegionVisible;
            Nav.MeshCfg config = new Nav.MeshCfg();
            config.CreateNavMeshRegions(regionTriangleEndIndex, regionIDs, vertices, indices, true, visible);
            config.ID = 1;
            config.Triangles = indices;
            config.Vertices = new Geo.Coord[vertices.Length];
            config.TriangleTypes = new ushort[indices.Length / 3];
            if (triangleTypes != null)
            {
                for (int i = 0; i < triangleTypes.Length; ++i)
                {
                    config.TriangleTypes[i] = triangleTypes[i];
                }
            }
            config.DefaultTypeSettings = Utils.CreateTriangleTypeSettings(MapCoreDef.MAP_MAX_REGION_TYPE_ID, triangleTypes, triangleStates);
            for (int i = 0; i < vertices.Length; ++i)
            {
                config.Vertices[i] = Geo.GeoUtils.Vector3ToCoord(vertices[i]);
            }
            return config;
        }

        public void InitNavigation(Vector3[] vertices, int[] indices, ushort[] triangleTypes, bool[] triangleStates)
        {
            if (vertices != null)
            {
                var config = CreateMeshConfig(vertices, indices, triangleTypes, triangleStates);
                int width = Utils.F2I(GetLayerWidthInMeter());
                int height = Utils.F2I(GetLayerHeightInMeter());
                if (mNavMgr != null)
                {
                    mNavMgr.OnDestroy();
                }
                mNavMgr = Nav.NaviMgr.Create(0, 0, width, height, config, 0);
            }
        }

        public List<Vector2> FindPath(Vector2 start, Vector2 end)
        {
            if (mNavMgr != null)
            {
                Vector2 endPosition = mNavMgr.FindEstimatedWalkablePosition(start, end, 10);
                var startCoord = new Geo.Coord(Utils.F2I(start.x), Utils.F2I(start.y));
                var endCoord = new Geo.Coord(Utils.F2I(endPosition.x), Utils.F2I(endPosition.y));
                var path = mNavMgr.Route(startCoord, endCoord);
                var ret = new List<Vector2>();
                for (int i = 0; i < path.Count; ++i)
                {
                    ret.Add(new Vector2(Utils.I2F(path[i].X), Utils.I2F(path[i].Z)));
                }
                return ret;
            }
            return null;
        }

        public void Resize(float newWidth, float newHeight)
        {
            mTileWidth = newWidth;
            mTileHeight = newHeight;
            mLayerOrigin = Vector3.zero;
        }

        public void ShowNavMeshRegions(bool show)
        {
            if (mNavMgr != null)
            {
                mNavMgr.ShowNavMeshRegions(show);
            }
        }

        public NavMeshData[] navMeshDatas { get { return mNavMeshDatas; } }
        public int navMeshCount
        {
            get
            {
                if (mNavMeshDatas == null)
                {
                    return 0;
                }
                return mNavMeshDatas.Length;
            }
        }

        public Nav.NaviMgr navMgr { get { return mNavMgr; } }
        public TriangleWindingOrder windingOrder
        {
            get
            {
                return mWindingOrder;
            }
        }

        public bool oceanAreaWalkable { get { return mOceanAreaWalkable; } set { mOceanAreaWalkable = value; } }

        TriangleWindingOrder mWindingOrder = TriangleWindingOrder.Unknown;
        bool mOceanAreaWalkable = true;
        NavMeshData[] mNavMeshDatas;
        Nav.NaviMgr mNavMgr;
    }
}
#endif