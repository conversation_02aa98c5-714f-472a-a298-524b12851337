%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: ComputeFilterWidth
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=14001\n389;92;1064;673;507.116;202.918;1;True;False\nNode;AmplifyShaderEditor.DotProductOpNode;4;-216,-85;Float;False;2;0;FLOAT3;0,0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;7;263,-7;Float;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ConditionalIfNode;6;38,-37;Float;False;False;5;0;FLOAT;0.0;False;1;FLOAT;0.0;False;2;FLOAT;0.0;False;3;FLOAT;0.0;False;4;FLOAT;0.0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;5;-185,84;Float;False;2;0;FLOAT3;0,0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;2;-398,-75;Float;False;1;0;FLOAT3;0.0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;1;-610,-13;Float;False;In;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DdyOpNode;3;-372,94;Float;False;1;0;FLOAT3;0.0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;411,14;Float;False;True;Result;0;False;1;0;FLOAT;0.0;False;1;FLOAT;0\nWireConnection;4;0;2;0\nWireConnection;4;1;2;0\nWireConnection;7;0;6;0\nWireConnection;6;0;4;0\nWireConnection;6;1;5;0\nWireConnection;6;2;4;0\nWireConnection;6;3;5;0\nWireConnection;6;4;5;0\nWireConnection;5;0;3;0\nWireConnection;5;1;3;0\nWireConnection;2;0;1;0\nWireConnection;3;0;1;0\nWireConnection;0;0;7;0\nASEEND*/\n//CHKSM=492807FC2E3C8BD5838861DEE6C6EDB10A2FF2D2"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_nodeCategory: 7
  m_customNodeCategory: 
