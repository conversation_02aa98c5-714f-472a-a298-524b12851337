﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [System.Flags]
    public enum MapLayerLODConfigFlag
    {
        None = 0,
        //使用特殊类型的高度缓冲区
        UseSpecialHeightBuffer = 1,
        //使用米作为缓冲取单位
        UseMeterThreshold = 2,
    }
    //地图层的lod设置
    public class MapLayerLODConfig
    {
        public class LOD
        {
            public LOD() { }

            public LOD(string name, float changeZoom, float changeZoomThreshold, bool hideObject = false, int shaderLOD = 0, bool useRenderTexture = false, MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None, int terrainLODTileCount = 0)
            {
                this.name = name;
                this.changeZoom = changeZoom;
                this.changeZoomThreshold = changeZoomThreshold;
                this.hideObject = hideObject;
                this.shaderLOD = shaderLOD;
                this.useRenderTexture = useRenderTexture;
                this.flag = flag;
                this.terrainLODTileCount = terrainLODTileCount;
            }

            public LOD Clone()
            {
                var lod = new LOD(this.name, this.changeZoom, this.changeZoomThreshold, this.hideObject, this.shaderLOD, this.useRenderTexture, this.flag, this.terrainLODTileCount);
                return lod;
            }
            //改变lod等级时的zoom,因为可能不同的MapLayer切换lod的时机不同
            public float changeZoom;
            //改变lod等级时的zoom缓冲值,避免在一个局部高度中频繁切换lod
            public float changeZoomThreshold;
            //在这个lod是否隐藏物体
            public bool hideObject;
            //改变lod等级时同时设置shader的lod,只对河流有用,0表示不设置
            public int shaderLOD;
            //在这lod是否使用render texture,目前用于河流层
            public bool useRenderTexture;
            public MapLayerLODConfigFlag flag;
            //用于设置生成lod的tile数
            public int terrainLODTileCount;
            //编辑器里用名字来索引,游戏中用change zoom
            public string name;
            //编辑器使用的变量
            public int nameIndex = -1;
        }

        public MapLayerLODConfig(Map map, LOD[] configs = null)
        {
            mMap = map;
            if (configs != null && configs.Length > 0)
            {
                mLODConfigs = configs;
            }
            else
            {
                mLODConfigs = new LOD[1]
                {
                    new LOD(GetDefaultLODName(0), 0, 0, false, 100, false, 0, 0),
                };
            }
        }

        public static string GetDefaultLODName(int lod)
        {
            return $"lod height {lod}";
        }

        public MapLayerLODConfig Clone()
        {
            MapLayerLODConfig newConfig = new MapLayerLODConfig(mMap);
            LOD[] newLODs = new LOD[mLODConfigs.Length];
            for (int i = 0; i < mLODConfigs.Length; ++i)
            {
                newLODs[i] = mLODConfigs[i].Clone();
            }
            newConfig.mLODConfigs = newLODs;
            return newConfig;
        }

        public MapLayerLODConfig.LOD GetLOD(string name)
        {
            for (int i = 0; i < mLODConfigs.Length; ++i)
            {
                if (mLODConfigs[i].name == name)
                {
                    return mLODConfigs[i];
                }
            }
            return null;
        }

        public int GetLODFromZoom(float zoom)
        {
            for (int i = mLODConfigs.Length - 1; i >= 0; --i)
            {
                if (zoom >= mLODConfigs[i].changeZoom)
                {
                    //zoom值超过缓冲值了
                    return i;
                }
            }
            return -1;
        }

        public int GetLODLevel(int currentLOD, float zoom)
        {
            for (int i = mLODConfigs.Length - 1; i >= 0; --i)
            {
                if (zoom >= mLODConfigs[i].changeZoom)
                {
                    if (mLODConfigs[i].flag.HasFlag(MapLayerLODConfigFlag.UseSpecialHeightBuffer))
                    {
                        return GetLODLevelSpecial(currentLOD, zoom);
                    }
                    else
                    {
                        return GetLODLevelNormal(currentLOD, zoom);
                    }
                }
            }
            return currentLOD;
        }

        //特殊的缓冲区,向上在高度切换lod,向下在高度+缓冲高度切换lod
        int GetLODLevelSpecial(int currentLOD, float zoom)
        {
            bool risingCamera = zoom > mLastCheckSpecialZoom;

            int lod = currentLOD;
            for (int i = mLODConfigs.Length - 1; i >= 0; --i)
            {
                if (zoom >= mLODConfigs[i].changeZoom)
                {
                    //zoom值超过缓冲值了
                    if (i != currentLOD)
                    {
                        //第一次进入地图时不需要判断LOD缓冲
                        float ds = zoom - mLODConfigs[i].changeZoom;
                        float threshold = risingCamera ? 0 : mLODConfigs[i].changeZoomThreshold;
                        if (ds >= threshold || currentLOD == -1)
                        {
                            lod = i;
                        }
                        else
                        {
                            lod = i - 1;
                        }
                    }

                    lod = Mathf.Clamp(lod, 0, mLODConfigs.Length - 1);
                    break;
                }
            }

            if (lod != currentLOD)
            {
                mLastCheckSpecialZoom = zoom;
                return lod;
            }

            //判断zoom值是否在当前lod的缓冲中
            if (!risingCamera)
            {
                if (IsInLODThreshold(currentLOD, zoom) && lod == currentLOD)
                {
                    lod = Mathf.Clamp(currentLOD - 1, 0, mLODConfigs.Length - 1);
                }
            }
            mLastCheckSpecialZoom = zoom;

            return lod;
        }

        //正常的缓冲区,向上在高度+缓冲高度切换lod,向下在高度切换lod
        int GetLODLevelNormal(int currentLOD, float zoom)
        {
            mLastCheckSpecialZoom = 0;
            //判断zoom值是否在当前lod的缓冲中
            if (IsInLODThreshold(currentLOD, zoom))
            {
                return currentLOD;
            }

            int lod = currentLOD;
            for (int i = mLODConfigs.Length - 1; i >= 0; --i)
            {
                if (zoom >= mLODConfigs[i].changeZoom)
                {
                    //zoom值超过缓冲值了
                    if (i != currentLOD)
                    {
                        //第一次进入地图时不需要判断LOD缓冲
                        float ds = zoom - mLODConfigs[i].changeZoom;
                        if (ds >= mLODConfigs[i].changeZoomThreshold || currentLOD == -1)
                        {
                            lod = i;
                        }
                        else
                        {
                            lod = i - 1;
                        }
                    }

                    lod = Mathf.Clamp(lod, 0, mLODConfigs.Length - 1);
                    break;
                }
            }
            return lod;
        }

        bool IsInLODThreshold(int lod, float zoom)
        {
            if (lod == -1)
            {
                return false;
            }
            else
            {
                return zoom >= mLODConfigs[lod].changeZoom && zoom <= (mLODConfigs[lod].changeZoom + mLODConfigs[lod].changeZoomThreshold);
            }
        }

        public void SetLODCount(int newCount)
        {
            int mapLODCount = mMap.data.lodManager.lodCount;
            newCount = Mathf.Clamp(newCount, 0, mapLODCount);

            if (newCount < 0)
            {
                return;
            }

            int oldCount = mLODConfigs.Length;
            if (newCount == oldCount)
            {
                return;
            }

            var newConfigs = new LOD[newCount];

            int delta = newCount - oldCount;
            if (delta > 0)
            {
                for (int i = 0; i < oldCount; ++i)
                {
                    newConfigs[i] = new LOD(mLODConfigs[i].name, mLODConfigs[i].changeZoom, mLODConfigs[i].changeZoomThreshold, mLODConfigs[i].hideObject, mLODConfigs[i].shaderLOD, mLODConfigs[i].useRenderTexture, mLODConfigs[i].flag, mLODConfigs[i].terrainLODTileCount);
                }

                float lastChangeZoom = 0.0f;
                if (oldCount > 0)
                {
                    lastChangeZoom = mLODConfigs[oldCount - 1].changeZoom;
                }
                for (int i = 0; i < delta; ++i)
                {
                    int idx = i + oldCount;
                    var mapLOD = mMap.data.lodManager.GetLOD(idx);
                    string name = mapLOD != null ? mapLOD.name : GetDefaultLODName(idx);
                    newConfigs[idx] = new LOD(name, lastChangeZoom + 1.0f, 0, false, 100, false, MapLayerLODConfigFlag.None, 0);
                    lastChangeZoom = newConfigs[idx].changeZoom;
                }
            }
            else if (delta < 0)
            {
                for (int i = 0; i < newCount; ++i)
                {
                    newConfigs[i] = new LOD(mLODConfigs[i].name, mLODConfigs[i].changeZoom, mLODConfigs[i].changeZoomThreshold, mLODConfigs[i].hideObject, mLODConfigs[i].shaderLOD, mLODConfigs[i].useRenderTexture, mLODConfigs[i].flag, mLODConfigs[i].terrainLODTileCount);
                }
            }
            else
            {
                Debug.Assert(false);
            }

            mLODConfigs = newConfigs;
        }

        //获取某个lod对应的zoom
        //lodIdx: lod数组的索引
        //considerThreshold: 是否考虑缓冲
        public float GetLODChangeZoom(int lodIdx, bool considerThreshold)
        {
            if (lodIdx >= 0 && lodIdx < lodConfigs.Length)
            {
                if (considerThreshold)
                {
                    return lodConfigs[lodIdx].changeZoom + lodConfigs[lodIdx].changeZoomThreshold;
                }
                else
                {
                    return lodConfigs[lodIdx].changeZoom;
                }
            }

            Debug.Assert(false);
            return 0;
        }

        public void ChangeLODName(string oldName, string newName)
        {
            var lod = GetLOD(oldName);
            if (lod != null)
            {
                lod.name = newName;
            }
        }

        public bool CheckZoom()
        {
            var lodManager = mMap.data.lodManager;
            for (int i = 1; i < mLODConfigs.Length; ++i)
            {
                float zoom = lodManager.ConvertNameToZoom(mLODConfigs[i].name);
                if (zoom < 0)
                {
                    return false;
                }
            }

            return true;
        }

        public LOD GetLOD(int idx)
        {
            if (idx >= 0 && idx < mLODConfigs.Length)
            {
                return mLODConfigs[idx];
            }
            return null;
        }

        public LOD[] lodConfigs { get { return mLODConfigs; } }

        LOD[] mLODConfigs;
        float mLastCheckSpecialZoom = 0;
        Map mMap;
    }
}
