﻿ 



 
 



namespace TFW.Map.Geo
{
    public class Vertice
    {
        //all vertex表的索引
        public int Index;
        public Coord Coord;

        public Vertice() { }

        public Vertice(Coord coord)
        {
            Coord = coord;
        }

        public Vertice(int index, Coord coord)
        {
            this.Index = index;
            this.Coord = coord;
        }
    }

    public class Edge
    {
        public Coord WtCoord;       // 重心点,初始是边的中心点
        public Vertice Vertice0;
        public Vertice Vertice1;
        public int AdjacentTriangle0 = -1; //最多相邻的两个三角形
        public int AdjacentTriangle1 = -1; //最多相邻的两个三角形
        // 拐点数组
        public Vertice Inflect0;
        public Vertice Inflect1;

        public Edge()
        {
            Inflect0 = new Vertice();
            Inflect1 = new Vertice();
        }

        // 计算边的终点
        public Coord CalMidCoord()
        {
            return GeoUtils.CalMidCoord(Vertice0.Coord, Vertice1.Coord);
        }

        // 生成边的序号
        public int GenKey()
        {
            return GenEdgeKey(Vertice0.Index, Vertice1.Index);
        }

        // 生成边的序号
        public int GenEdgeKey(int i, int j)
        {
            if (i < j)
            {
                return 10000 * i + j;
            }
            return 10000 * j + i;
        }

        public bool IsWalkable(Nav.TriangleTypeSetting[] setting, Triangle[] triangles)
        {
            if (AdjacentTriangle0 >= 0)
            {
                var adjTriangle0 = triangles[AdjacentTriangle0];
                if (!setting[adjTriangle0.GetCustomType()].walkable)
                {
                    return false;
                }
            }

            if (AdjacentTriangle1 >= 0)
            {
                var adjTriangle1 = triangles[AdjacentTriangle1];
                if (!setting[adjTriangle1.GetCustomType()].walkable)
                {
                    return false;
                }
            }

            return true;
        }

        public void AddAdjacentTriangle(Triangle t)
        {
            if (AdjacentTriangle0 < 0)
            {
                AdjacentTriangle0 = t.Index;
            }
            else
            {
                AdjacentTriangle1 = t.Index;
            }
        }

        public int GetAdjacentTriangleCount()
        {
            int n = 0;
            if (AdjacentTriangle0 >= 0)
            {
                ++n;
            }
            if (AdjacentTriangle1 >= 0)
            {
                ++n;
            }
            return n;
        }
    }
}

