﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionTilingBlendTerrainLayer : EditorAction
    {
        class TileData
        {
            public TileData(int index, int type, int subTypeIndex)
            {
                tileIndex = index;
                tileType = type;
                this.subTypeIndex = subTypeIndex;
            }

            public int tileIndex;
            public int tileType;
            public int subTypeIndex;
        }

        //从prefabPaths中选取tile
        public ActionTilingBlendTerrainLayer(int layerID, int prefabGroupIndex, List<Vector2Int> tiles, List<int> prefabIndices)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);

            mPrefabGroupIndex = prefabGroupIndex;
            mLayerID = layerID;
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            mTileIndices = new List<Vector2Int>();
            mTileIndices.AddRange(tiles);
            mOldTiles = new Dictionary<Vector2Int, TileData>();
            mNewTiles = new Dictionary<Vector2Int, TileData>();
            for (int k = 0; k < tiles.Count; ++k)
            {
                var tile = layer.GetTile(tiles[k].x, tiles[k].y);
                if (tile != null)
                {
                    mOldTiles[tiles[k]] = new TileData(tile.index, tile.type, tile.subTypeIndex);
                }

                mNewTiles[tiles[k]] = new TileData(prefabIndices[Random.Range(0, prefabIndices.Count)], group.groupID, 0);
            }

            mDescription = string.Format("tiling terrain layer {0}", layer.name);
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);
                if (group != null)
                {
                    for (int i = 0; i < mTileIndices.Count; ++i)
                    {
                        TileData newTileData;
                        mNewTiles.TryGetValue(mTileIndices[i], out newTileData);
                        layer.SetTile(mTileIndices[i].x, mTileIndices[i].y, newTileData.tileIndex, newTileData.tileType, newTileData.subTypeIndex);
                    }

                    return true;
                }
            }
            return false;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                int horizontalTileCount = layer.horizontalTileCount;
                int verticalTileCount = layer.verticalTileCount;

                for (int i = 0; i < mTileIndices.Count; ++i)
                {
                    int x = mTileIndices[i].x;
                    int y = mTileIndices[i].y;
                    TileData oldTileData;
                    mOldTiles.TryGetValue(mTileIndices[i], out oldTileData);
                    if (oldTileData == null)
                    {
                        layer.SetTile(x, y, 0, -1, -1);
                    }
                    else
                    {
                        layer.SetTile(x, y, oldTileData.tileIndex, oldTileData.tileType, oldTileData.subTypeIndex);
                    }
                }

                return true;
            }
            return false;
        }

        int mPrefabGroupIndex;
        int mLayerID;
        string mDescription;
        List<Vector2Int> mTileIndices;
        Dictionary<Vector2Int, TileData> mOldTiles;
        Dictionary<Vector2Int, TileData> mNewTiles;
    }
}

#endif