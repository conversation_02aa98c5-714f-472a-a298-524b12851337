{"skeleton": {"hash": "0fL39Ot7/es", "spine": "4.2.33", "x": -278.16, "y": -33.23, "width": 510.08, "height": 1799.23, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 352.23, "y": 1105.86}, {"name": "all", "parent": "ALL", "x": -361.35, "y": -38.4, "icon": "arrows"}, {"name": "body", "parent": "all", "length": 92.03, "rotation": 96.74, "x": -1.69, "y": 16.73}, {"name": "body2", "parent": "body", "length": 279.18, "rotation": 90.17, "x": 92.03, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 83.09, "rotation": 89.43, "x": 279.18, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 220.81, "rotation": -1.69, "x": 83.09}, {"name": "tun", "parent": "all", "length": 195.47, "rotation": -82.85, "x": 1.13, "y": -10.99}, {"name": "leg_L", "parent": "tun", "x": 36.83, "y": 97.9}, {"name": "leg_R", "parent": "tun", "x": 39.78, "y": -88.42}, {"name": "leg_R2", "parent": "leg_R", "length": 471.65, "rotation": 6.07, "x": 107.72, "y": -29.72}, {"name": "leg_R3", "parent": "leg_R2", "length": 539.76, "rotation": -11.13, "x": 471.65, "inherit": "noScale"}, {"name": "leg_L2", "parent": "leg_L", "length": 1052.38, "rotation": -14.78, "x": 105.08, "y": 34.04}, {"name": "sh_L", "parent": "body2", "length": 128.85, "rotation": -88.32, "x": 235.8, "y": -12.56}, {"name": "arm_L", "parent": "sh_L", "length": 281.91, "rotation": -83.66, "x": 128.85, "inherit": "noScale"}, {"name": "arm_L2", "parent": "arm_L", "length": 202, "rotation": -90.18, "x": 281.91, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "length": 130, "rotation": 91.78, "x": 235.65, "y": 16.98}, {"name": "arm_R", "parent": "sh_R", "length": 290, "rotation": -96.59, "x": 130, "inherit": "onlyTranslation"}, {"name": "arm_R2", "parent": "arm_R", "length": 244, "rotation": 103.37, "x": 290, "inherit": "onlyTranslation"}, {"name": "arm_R3", "parent": "arm_R2", "length": 67, "rotation": -34.72, "x": 244}, {"name": "arm_R4", "parent": "arm_R3", "length": 76, "rotation": -58.03, "x": 67}, {"name": "arm_L3", "parent": "arm_L2", "length": 68, "rotation": -25.89, "x": 202, "inherit": "noScale"}, {"name": "arm_L4", "parent": "arm_L3", "length": 70, "rotation": 57.55, "x": 68}, {"name": "eyebrow_L3", "parent": "head", "length": 14.48, "rotation": 67, "x": 83.74, "y": -50.13}, {"name": "eyebrow_L2", "parent": "eyebrow_L3", "length": 19.02, "rotation": 40.05, "x": 14.48}, {"name": "eyebrow_L", "parent": "eyebrow_L2", "length": 13.75, "rotation": 11.23, "x": 19.02}, {"name": "eyebrow_R3", "parent": "head", "length": 13.77, "rotation": -69.69, "x": 85.19, "y": 57.73}, {"name": "eyebrow_R2", "parent": "eyebrow_R3", "length": 16.81, "rotation": -38.54, "x": 13.77}, {"name": "eyebrow_R", "parent": "eyebrow_R2", "length": 12.39, "rotation": -3.33, "x": 16.81}, {"name": "eye_L", "parent": "head", "x": 64.66, "y": -28.79}, {"name": "eye_R", "parent": "head", "x": 64.92, "y": 36.26}, {"name": "headround3", "parent": "head", "x": 408.61, "y": -0.58, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -49.49, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 368.98, "y": -50.07, "icon": "warning"}, {"name": "sh_L2", "parent": "body2", "rotation": -90.17, "x": 239.57, "y": -141.36, "color": "ff3f00ff", "icon": "ik"}, {"name": "sh_R2", "parent": "body2", "rotation": -90.17, "x": 231.61, "y": 146.92, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_LL", "parent": "sh_L", "length": 331.34, "rotation": -75.51, "x": 128.87, "y": -0.01}, {"name": "arm_LL2", "parent": "arm_LL", "length": 256.02, "rotation": -108.52, "x": 331.34, "color": "abe323ff"}, {"name": "arm_L5", "parent": "body", "rotation": -96.74, "x": 27.54, "y": 26.97, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "all", "x": 168.31, "y": 69.09, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L3", "parent": "root", "x": 0.9, "y": -110.99, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_RR", "parent": "leg_R", "length": 490.51, "rotation": 14.18, "x": 107.71, "y": -29.72}, {"name": "leg_RR2", "parent": "leg_RR", "length": 553.15, "rotation": -26.14, "x": 490.51, "color": "abe323ff", "icon": "ik"}, {"name": "leg_R4", "parent": "root", "x": 20.76, "y": -103.15, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "root", "x": 1.03, "y": 436.27, "color": "ff3f00ff", "icon": "ik"}, {"name": "bodyround", "parent": "body2", "x": 182.14, "y": -384.49, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 118.11, "y": -384.49, "icon": "warning"}, {"name": "tunround", "parent": "all", "x": 389.37, "y": -62.73, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "all", "x": 389.37, "y": -130.57, "icon": "warning"}, {"name": "RU_R", "parent": "body2", "x": 100.8, "y": 95.16}, {"name": "RU_R2", "parent": "RU_R", "x": -11.56, "y": 10.91}, {"name": "RU_R3", "parent": "RU_R2", "x": -10.9, "y": 12.04}, {"name": "RU_L", "parent": "body2", "x": 108.64, "y": -58.22}, {"name": "RU_L2", "parent": "RU_L", "x": -12.04, "y": -9.02}, {"name": "RU_L3", "parent": "RU_L2", "x": -13.49, "y": -11.99}, {"name": "hair_L", "parent": "head", "length": 45.1, "rotation": -164.53, "x": 113.78, "y": -67.1}, {"name": "hair_L2", "parent": "hair_L", "length": 37.19, "rotation": 17.99, "x": 45.1, "color": "abe323ff"}, {"name": "hair_L3", "parent": "hair_L2", "length": 32.58, "rotation": 0.3, "x": 37.19, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 29.14, "rotation": -29.74, "x": 32.58, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 26.46, "rotation": -30.06, "x": 29.14, "color": "abe323ff"}, {"name": "hair_R", "parent": "head", "length": 46.33, "rotation": 172.8, "x": 112.9, "y": 73}, {"name": "hair_R2", "parent": "hair_R", "length": 49.96, "rotation": -20.09, "x": 46.33, "color": "abe323ff"}, {"name": "hair_R3", "parent": "hair_R2", "length": 42.08, "rotation": 10.93, "x": 49.96, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 30.95, "rotation": 40.74, "x": 42.08, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 30.58, "rotation": 28.84, "x": 30.95, "color": "abe323ff"}, {"name": "hair_R6", "parent": "hair_R5", "length": 23.34, "rotation": 35.75, "x": 30.58, "color": "abe323ff"}], "slots": [{"name": "arm_Ra", "bone": "root", "attachment": "arm_Ra"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "body2", "bone": "root", "attachment": "body"}, {"name": "arm_L2", "bone": "root", "attachment": "arm_L"}, {"name": "ear_R", "bone": "root", "attachment": "ear_R"}, {"name": "ear_L", "bone": "root", "attachment": "ear_L"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair", "bone": "root", "attachment": "hair"}, {"name": "arm_Rb", "bone": "root", "attachment": "arm_Rb"}], "ik": [{"name": "arm_L", "order": 1, "bones": ["arm_LL", "arm_LL2"], "target": "arm_L5", "bendPositive": false}, {"name": "arm_L1", "order": 3, "bones": ["arm_L"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 4, "bones": ["arm_L2"], "target": "arm_L5", "compress": true, "stretch": true}, {"name": "leg_L", "order": 5, "bones": ["leg_L2"], "target": "leg_L3", "compress": true, "stretch": true}, {"name": "leg_R", "order": 6, "bones": ["leg_RR", "leg_RR2"], "target": "leg_R4", "bendPositive": false}, {"name": "leg_R1", "order": 8, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 9, "bones": ["leg_R3"], "target": "leg_R4", "compress": true, "stretch": true}, {"name": "sh_L", "bones": ["sh_L"], "target": "sh_L2", "compress": true, "stretch": true}, {"name": "sh_R", "order": 10, "bones": ["sh_R"], "target": "sh_R2", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 2, "bones": ["arm_L1"], "target": "arm_LL2", "rotation": -177.77, "x": 57.24, "y": -35.92, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 11, "bones": ["bodyround2"], "target": "bodyround", "x": -64.03, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 12, "bones": ["headround2"], "target": "headround", "x": -39.63, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 13, "bones": ["eyebrow_L3"], "target": "headround", "rotation": 67, "x": -324.87, "y": -0.06, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 14, "bones": ["eyebrow_R3"], "target": "headround", "rotation": -69.69, "x": -323.42, "y": 107.8, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "leg_R3", "order": 7, "bones": ["leg_R1"], "target": "leg_RR2", "rotation": 97.1, "x": 20.31, "y": -86.24, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "tunround", "order": 15, "bones": ["tunround2"], "target": "tunround", "y": -67.84, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_L2", "order": 21, "bone": "hair_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 22, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 23, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 24, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R2", "order": 16, "bone": "hair_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 17, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 18, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 19, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R6", "order": 20, "bone": "hair_R6", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.83273, 1e-05, 0.84244, 0.00917, 0.85244, 0.02329, 0.88845, 0.10334, 0.93759, 0.1964, 0.99999, 0.29507, 0.96654, 0.33414, 0.92104, 0.37155, 0.94249, 0.51365, 0.96361, 0.65608, 0.97056, 0.73484, 0.97473, 0.79612, 0.974, 0.84585, 0.94848, 0.87756, 0.9014, 0.88512, 0.85255, 0.90045, 0.76235, 0.92281, 0.66502, 0.93402, 0.54797, 0.93617, 0.43309, 0.93018, 0.3884, 0.92785, 0.3545, 0.92929, 0.32552, 0.93614, 0.28647, 0.93484, 0.23327, 0.9239, 0.18909, 0.91985, 0.1672, 0.9229, 0.17665, 0.9561, 0.16882, 0.9915, 0.12303, 0.9983, 0.04638, 0.97344, 0, 0.90553, 0.05606, 0.81334, 0.1004, 0.78067, 0.14474, 0.74799, 0.18593, 0.75549, 0.21331, 0.77303, 0.28805, 0.81175, 0.31807, 0.82214, 0.35093, 0.82555, 0.39001, 0.8209, 0.42553, 0.81392, 0.56091, 0.78291, 0.64725, 0.74773, 0.74036, 0.71025, 0.76856, 0.70345, 0.75595, 0.67198, 0.73651, 0.62299, 0.71643, 0.56273, 0.68203, 0.46148, 0.65969, 0.34918, 0.64514, 0.24915, 0.63763, 0.15256, 0.6615, 0.09455, 0.73084, 0.02861, 0.79795, 0.00287, 0.91, 0.32168, 0.84511, 0.35668, 0.87499, 0.53, 0.89743, 0.6623, 0.90098, 0.71358, 0.90666, 0.7768, 0.87399, 0.81965, 0.81586, 0.84219, 0.78035, 0.76422, 0.82935, 0.74666, 0.84213, 0.71084, 0.83219, 0.66869, 0.78781, 0.55245, 0.75381, 0.41361, 0.79781, 0.05945, 0.77371, 0.11033, 0.74766, 0.19389, 0.72667, 0.25842, 0.82718, 0.14005, 0.83968, 0.24468, 0.73497, 0.32017], "triangles": [52, 53, 72, 51, 52, 72, 35, 36, 33, 35, 33, 34, 36, 26, 33, 32, 33, 26, 31, 32, 26, 30, 31, 26, 29, 30, 26, 26, 27, 29, 28, 29, 27, 24, 25, 36, 25, 26, 36, 37, 24, 36, 23, 24, 37, 23, 37, 38, 38, 22, 23, 21, 38, 39, 21, 22, 38, 64, 45, 65, 44, 45, 64, 63, 64, 65, 63, 65, 62, 62, 61, 12, 13, 62, 12, 14, 62, 13, 15, 63, 62, 15, 62, 14, 64, 43, 44, 16, 64, 63, 16, 63, 15, 16, 43, 64, 20, 39, 40, 21, 39, 20, 18, 19, 41, 40, 41, 19, 20, 40, 19, 17, 43, 16, 42, 43, 17, 42, 18, 41, 17, 18, 42, 73, 72, 75, 50, 51, 73, 70, 3, 74, 74, 3, 4, 75, 74, 4, 76, 73, 75, 56, 75, 4, 56, 4, 5, 6, 56, 5, 50, 73, 76, 57, 75, 56, 76, 75, 57, 7, 56, 6, 57, 56, 7, 69, 76, 57, 50, 76, 69, 49, 50, 69, 58, 57, 7, 58, 7, 8, 69, 57, 58, 68, 69, 58, 49, 69, 68, 48, 49, 68, 47, 48, 68, 58, 8, 9, 59, 58, 9, 67, 68, 58, 67, 47, 68, 58, 59, 67, 46, 47, 67, 45, 46, 67, 66, 67, 59, 45, 67, 66, 60, 59, 9, 66, 59, 60, 60, 9, 10, 65, 45, 66, 61, 60, 10, 61, 10, 11, 65, 60, 61, 60, 65, 66, 62, 65, 61, 12, 61, 11, 72, 74, 75, 73, 51, 72, 72, 71, 74, 72, 53, 71, 2, 70, 1, 70, 54, 55, 55, 0, 1, 70, 55, 1, 3, 70, 2, 71, 54, 70, 71, 70, 74, 53, 54, 71], "vertices": [2, 13, 144.71, 15.92, 0.87143, 4, 255.95, -156.74, 0.12857, 2, 13, 148.14, 12.43, 0.87429, 4, 252.56, -160.27, 0.12571, 3, 13, 151.62, 7.1, 0.77618, 14, -4.55, 23.42, 0.09143, 4, 247.34, -163.91, 0.13239, 3, 13, 163.81, -22.84, 0.07782, 14, 26.57, 32.22, 0.90208, 4, 217.76, -176.97, 0.02009, 1, 14, 63.11, 45.08, 1, 1, 14, 102.39, 62.44, 1, 1, 14, 114.92, 48.3, 1, 1, 14, 126.22, 29.89, 1, 1, 14, 179.24, 30.18, 1, 1, 14, 232.35, 30.32, 1, 2, 14, 261.48, 28.69, 0.99439, 15, -28.63, -20.51, 0.00561, 2, 14, 284.08, 26.97, 0.76277, 15, -26.98, 2.09, 0.23723, 2, 14, 302.2, 24.1, 0.44254, 15, -24.16, 20.22, 0.55746, 2, 14, 312.46, 13.21, 0.25939, 15, -13.3, 30.52, 0.74061, 2, 14, 312.77, -4.2, 0.07113, 15, 4.11, 30.88, 0.92887, 1, 15, 22.55, 34, 1, 1, 15, 56.3, 37.57, 1, 1, 15, 92.06, 36.71, 1, 1, 15, 134.48, 31.54, 1, 1, 15, 175.69, 23.51, 1, 2, 15, 191.73, 20.38, 0.91511, 21, -18.14, 13.85, 0.08489, 2, 15, 204.05, 19.18, 0.49482, 21, -6.53, 18.15, 0.50518, 2, 15, 214.88, 20.21, 0.15802, 21, 2.76, 23.8, 0.84198, 3, 15, 228.93, 17.75, 0.01093, 21, 16.47, 27.72, 0.97694, 22, -4.26, 58.36, 0.01214, 2, 21, 36.19, 29.84, 0.85142, 22, 8.12, 42.85, 0.14858, 2, 21, 52, 33.37, 0.47312, 22, 19.58, 31.41, 0.52688, 2, 21, 59.26, 36.89, 0.19629, 22, 26.44, 27.17, 0.80371, 2, 21, 52.21, 47.49, 0.05302, 22, 31.61, 38.81, 0.94698, 2, 21, 50.92, 60.81, 0.01786, 22, 42.15, 47.04, 0.98214, 2, 21, 66.06, 68.32, 0.00324, 22, 56.61, 38.3, 0.99676, 1, 22, 72.29, 13.38, 1, 1, 22, 69.33, -16.72, 1, 1, 22, 31.86, -29.84, 1, 2, 21, 98.57, -5.57, 0.02167, 22, 11.71, -28.79, 0.97833, 2, 21, 86.87, -22.01, 0.24794, 22, -8.45, -27.74, 0.75206, 2, 21, 71.72, -23.99, 0.55509, 22, -18.25, -16.01, 0.44491, 2, 21, 60.22, -20.9, 0.83836, 22, -21.82, -4.65, 0.16164, 1, 21, 29.87, -15.68, 1, 2, 15, 211.71, -21.83, 0.05463, 21, 18.26, -15.4, 0.94537, 2, 15, 200, -18.91, 0.43043, 21, 6.46, -17.88, 0.56957, 2, 15, 185.64, -18.62, 0.90896, 21, -6.59, -23.89, 0.09104, 2, 15, 172.44, -19.36, 0.99572, 21, -18.13, -30.32, 0.00428, 1, 15, 121.91, -23.8, 1, 2, 14, 249.38, -88.79, 0.01085, 15, 88.89, -32.26, 0.98915, 2, 14, 240.53, -53.19, 0.2626, 15, 53.31, -41.21, 0.7374, 2, 14, 239.51, -42.64, 0.53408, 15, 42.77, -42.26, 0.46592, 2, 14, 227.36, -45.54, 0.80349, 15, 45.71, -54.4, 0.19651, 2, 14, 208.46, -49.99, 0.9476, 15, 50.21, -73.3, 0.0524, 2, 14, 185.4, -54.07, 0.9944, 15, 54.37, -96.34, 0.0056, 2, 13, 84.24, -152.5, 0.00098, 14, 146.64, -61.18, 0.99902, 3, 13, 77.43, -110.82, 0.03512, 14, 104.46, -63.35, 0.75726, 4, 127.3, -93.2, 0.20762, 3, 13, 73.31, -73.76, 0.1044, 14, 67.17, -63.35, 0.40287, 4, 164.22, -88, 0.49272, 2, 13, 71.72, -38.04, 0.16099, 4, 199.87, -85.36, 0.83901, 2, 13, 81.12, -16.93, 0.27833, 4, 221.25, -94.14, 0.72167, 2, 13, 107.2, 6.57, 0.69155, 4, 245.51, -119.52, 0.30845, 2, 13, 131.99, 15.28, 0.83967, 4, 254.93, -144.04, 0.16033, 1, 14, 107.43, 28.53, 1, 2, 14, 116.84, 3.24, 0.99016, 45, -57.82, 223.62, 0.00984, 2, 14, 181.7, 4.93, 0.99323, 45, -121.8, 212.9, 0.00677, 2, 14, 231.19, 6.08, 0.99926, 45, -170.65, 204.86, 0.00074, 1, 14, 250.1, 4.67, 1, 1, 14, 273.48, 3.4, 1, 1, 15, 10.64, 5.56, 1, 1, 15, 32.81, 10.84, 1, 2, 14, 262.32, -41.57, 0.19135, 15, 41.63, -19.46, 0.80865, 2, 14, 258.46, -22.95, 0.50968, 15, 23.02, -23.38, 0.49032, 2, 14, 246.04, -16.45, 0.81097, 15, 16.56, -35.82, 0.18903, 3, 14, 230.13, -17.82, 0.90423, 15, 17.98, -51.72, 0.09374, 45, -172.93, 228.67, 0.00203, 3, 14, 185.36, -27.75, 0.99031, 15, 28.04, -96.46, 0.00344, 45, -129.99, 244.75, 0.00625, 3, 13, 111, -135.69, 0.00178, 14, 132.89, -32.73, 0.98505, 45, -78.72, 257, 0.01316, 3, 13, 131.26, -5.59, 0.91754, 4, 234.05, -143.93, 0.0767, 45, 51.91, 240.56, 0.00577, 3, 13, 121.87, -24.07, 0.81058, 4, 215.31, -135.08, 0.17642, 45, 33.16, 249.41, 0.013, 4, 13, 111.37, -54.58, 0.23322, 14, 52.32, -23.4, 0.49623, 4, 184.5, -125.48, 0.25179, 45, 2.36, 259.01, 0.01876, 4, 13, 102.94, -78.13, 0.03785, 14, 74.79, -34.38, 0.8099, 4, 160.71, -117.75, 0.13091, 45, -21.43, 266.74, 0.02134, 3, 14, 36.79, 8.15, 0.90077, 4, 204.28, -154.56, 0.08993, 45, 22.14, 229.92, 0.0093, 2, 14, 75.65, 7.17, 0.98906, 45, -16.48, 225.48, 0.01094, 4, 13, 105.24, -101, 0.03508, 14, 97.78, -34.63, 0.91092, 4, 137.92, -120.71, 0.03675, 45, -44.22, 263.78, 0.01725], "hull": 56, "edges": [0, 110, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 32, 34, 56, 58, 58, 60, 62, 64, 68, 70, 74, 76, 76, 78, 78, 80, 84, 86, 86, 88, 88, 90, 102, 104, 104, 106, 106, 108, 108, 110, 4, 6, 6, 8, 8, 10, 98, 100, 100, 102, 26, 28, 44, 46, 40, 42, 42, 44, 34, 36, 36, 38, 38, 40, 80, 82, 82, 84, 90, 92, 92, 94, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 60, 62, 70, 72, 72, 74, 94, 96, 96, 98, 22, 24, 18, 20, 20, 22, 28, 30, 30, 32, 114, 116, 116, 118, 134, 136, 136, 138, 0, 2, 2, 4], "width": 365, "height": 369}}, "arm_L2": {"arm_L": {"type": "mesh", "uvs": [0.82935, 0.74666, 0.90666, 0.7768, 0.97473, 0.79612, 0.974, 0.84585, 0.94848, 0.87756, 0.9014, 0.88512, 0.85255, 0.90045, 0.76235, 0.92281, 0.66502, 0.93402, 0.54797, 0.93617, 0.43309, 0.93018, 0.3884, 0.92785, 0.3545, 0.92929, 0.32552, 0.93614, 0.28647, 0.93484, 0.23327, 0.9239, 0.18909, 0.91985, 0.1672, 0.9229, 0.17665, 0.9561, 0.16882, 0.9915, 0.12303, 0.9983, 0.04638, 0.97344, 0, 0.90553, 0.05606, 0.81334, 0.1004, 0.78067, 0.14474, 0.74799, 0.18593, 0.75549, 0.21331, 0.77303, 0.28805, 0.81175, 0.31807, 0.82214, 0.35093, 0.82555, 0.39001, 0.8209, 0.42553, 0.81392, 0.56091, 0.78291, 0.64725, 0.74773, 0.74036, 0.71025, 0.76856, 0.70345, 0.87399, 0.81965, 0.81586, 0.84219, 0.78035, 0.76422], "triangles": [26, 27, 24, 26, 24, 25, 27, 17, 24, 23, 24, 17, 22, 23, 17, 21, 22, 17, 20, 21, 17, 17, 18, 20, 19, 20, 18, 15, 16, 27, 16, 17, 27, 28, 15, 27, 14, 15, 28, 14, 28, 29, 29, 13, 14, 12, 29, 30, 12, 13, 29, 39, 36, 0, 35, 36, 39, 38, 39, 0, 38, 0, 37, 37, 1, 3, 4, 37, 3, 5, 37, 4, 6, 38, 37, 6, 37, 5, 39, 34, 35, 7, 39, 38, 7, 38, 6, 7, 34, 39, 11, 30, 31, 12, 30, 11, 9, 10, 32, 31, 32, 10, 11, 31, 10, 8, 34, 7, 33, 34, 8, 33, 9, 32, 8, 9, 33, 37, 0, 1, 3, 1, 2], "vertices": [2, 14, 258.46, -22.95, 0.50968, 15, 23.02, -23.38, 0.49032, 1, 14, 273.48, 3.4, 1, 2, 14, 284.08, 26.97, 0.76277, 15, -26.98, 2.09, 0.23723, 2, 14, 302.2, 24.1, 0.44254, 15, -24.16, 20.22, 0.55746, 2, 14, 312.46, 13.21, 0.25939, 15, -13.3, 30.52, 0.74061, 2, 14, 312.77, -4.2, 0.07113, 15, 4.11, 30.88, 0.92887, 1, 15, 22.55, 34, 1, 1, 15, 56.3, 37.57, 1, 1, 15, 92.06, 36.71, 1, 1, 15, 134.48, 31.54, 1, 1, 15, 175.69, 23.51, 1, 2, 15, 191.73, 20.38, 0.91511, 21, -18.14, 13.85, 0.08489, 2, 15, 204.05, 19.18, 0.49482, 21, -6.53, 18.15, 0.50518, 2, 15, 214.88, 20.21, 0.15802, 21, 2.76, 23.8, 0.84198, 3, 15, 228.93, 17.75, 0.01093, 21, 16.47, 27.72, 0.97694, 22, -4.26, 58.36, 0.01214, 2, 21, 36.19, 29.84, 0.85142, 22, 8.12, 42.85, 0.14858, 2, 21, 52, 33.37, 0.47312, 22, 19.58, 31.41, 0.52688, 2, 21, 59.26, 36.89, 0.19629, 22, 26.44, 27.17, 0.80371, 2, 21, 52.21, 47.49, 0.05302, 22, 31.61, 38.81, 0.94698, 2, 21, 50.92, 60.81, 0.01786, 22, 42.15, 47.04, 0.98214, 2, 21, 66.06, 68.32, 0.00324, 22, 56.61, 38.3, 0.99676, 1, 22, 72.29, 13.38, 1, 1, 22, 69.33, -16.72, 1, 1, 22, 31.86, -29.84, 1, 2, 21, 98.57, -5.57, 0.02167, 22, 11.71, -28.79, 0.97833, 2, 21, 86.87, -22.01, 0.24794, 22, -8.45, -27.74, 0.75206, 2, 21, 71.72, -23.99, 0.55509, 22, -18.25, -16.01, 0.44491, 2, 21, 60.22, -20.9, 0.83836, 22, -21.82, -4.65, 0.16164, 1, 21, 29.87, -15.68, 1, 2, 15, 211.71, -21.83, 0.05463, 21, 18.26, -15.4, 0.94537, 2, 15, 200, -18.91, 0.43043, 21, 6.46, -17.88, 0.56957, 2, 15, 185.64, -18.62, 0.90896, 21, -6.59, -23.89, 0.09104, 2, 15, 172.44, -19.36, 0.99572, 21, -18.13, -30.32, 0.00428, 1, 15, 121.91, -23.8, 1, 2, 14, 249.38, -88.79, 0.01085, 15, 88.89, -32.26, 0.98915, 2, 14, 240.53, -53.19, 0.2626, 15, 53.31, -41.21, 0.7374, 2, 14, 239.51, -42.64, 0.53408, 15, 42.77, -42.26, 0.46592, 1, 15, 10.64, 5.56, 1, 1, 15, 32.81, 10.84, 1, 2, 14, 262.32, -41.57, 0.19135, 15, 41.63, -19.46, 0.80865], "hull": 37, "edges": [6, 8, 14, 16, 38, 40, 40, 42, 44, 46, 50, 52, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 8, 10, 26, 28, 22, 24, 24, 26, 16, 18, 18, 20, 20, 22, 62, 64, 64, 66, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 46, 48, 48, 50, 42, 44, 52, 54, 54, 56, 4, 6, 10, 12, 12, 14, 72, 0, 0, 2, 2, 4], "width": 365, "height": 369}}, "arm_Ra": {"arm_Ra": {"type": "mesh", "uvs": [0.62576, 0.00102, 0.94767, 0.0676, 1, 0.11952, 1, 0.1627, 0.98327, 0.2386, 0.94307, 0.33399, 0.90257, 0.42644, 0.86203, 0.52368, 0.80452, 0.6232, 0.75668, 0.74971, 0.70091, 0.81963, 0.58504, 0.90777, 0.46008, 0.96683, 0.36118, 0.99473, 0.2421, 0.9941, 0.18062, 0.95406, 0.14989, 0.8766, 0.14186, 0.78865, 0.16674, 0.59477, 0.19218, 0.43684, 0.06103, 0.39358, 1e-05, 0.33581, 0.13989, 0.26099, 0.27871, 0.17726, 0.37282, 0.10422, 0.48342, 0.02828, 0.21405, 0.3522, 0.39871, 0.39405, 0.33099, 0.60912, 0.30439, 0.78697, 0.51237, 0.80496, 0.60669, 0.62409, 0.66715, 0.45851, 0.36485, 0.87985, 0.30923, 0.94554, 0.43911, 0.2929, 0.71239, 0.35589, 0.75592, 0.2695, 0.80187, 0.18671, 0.58905, 0.12372, 0.49957, 0.20021], "triangles": [14, 34, 13, 14, 15, 34, 15, 16, 34, 28, 17, 18, 16, 29, 33, 29, 17, 28, 13, 34, 12, 11, 12, 33, 12, 34, 33, 33, 30, 11, 11, 30, 10, 33, 29, 30, 10, 30, 9, 9, 30, 31, 30, 29, 31, 29, 28, 31, 9, 31, 8, 7, 8, 31, 32, 7, 31, 18, 19, 28, 32, 31, 28, 27, 28, 19, 27, 32, 28, 7, 32, 6, 32, 36, 6, 32, 27, 36, 19, 26, 27, 19, 20, 26, 6, 36, 5, 27, 35, 36, 27, 26, 35, 20, 21, 26, 36, 37, 5, 36, 35, 37, 21, 22, 26, 26, 22, 35, 5, 37, 4, 22, 23, 35, 35, 40, 37, 35, 23, 40, 37, 38, 4, 37, 40, 38, 4, 38, 3, 23, 24, 40, 40, 39, 38, 40, 24, 39, 24, 25, 39, 38, 39, 1, 16, 17, 29, 34, 16, 33, 3, 38, 2, 2, 38, 1, 39, 0, 1, 39, 25, 0], "vertices": [2, 16, 125.31, -27.75, 0.97415, 17, -28.14, 0.52, 0.02585, 1, 16, 84.91, -3.46, 1, 2, 16, 78.82, 14.62, 0.87303, 17, 6.86, 52.78, 0.12697, 2, 16, 79.33, 29.47, 0.66324, 17, 21.62, 54.48, 0.33676, 2, 16, 82.36, 55.49, 0.28859, 17, 47.8, 55.35, 0.71141, 2, 16, 88.62, 88.11, 0.05805, 17, 80.99, 54, 0.94195, 2, 16, 94.89, 119.72, 0.00435, 17, 113.18, 52.5, 0.99565, 1, 17, 147, 51.18, 1, 1, 17, 181.85, 47.79, 1, 1, 17, 225.79, 46.7, 1, 1, 17, 250.5, 42.37, 1, 1, 17, 282.32, 31.11, 1, 2, 17, 304.34, 17.55, 0.79653, 18, -19.47, -11.6, 0.20347, 2, 17, 315.33, 6.08, 0.47973, 18, -25.88, 2.93, 0.52027, 2, 17, 316.86, -9.09, 0.25804, 18, -22.14, 17.71, 0.74196, 2, 17, 304.08, -18.49, 0.08741, 18, -6.92, 22.18, 0.91259, 2, 17, 278.06, -25.45, 0.44857, 18, 19.91, 19.85, 0.55143, 2, 17, 248.12, -29.94, 0.84, 18, 49.59, 13.85, 0.16, 1, 17, 181.5, -34.43, 1, 2, 17, 127.16, -37.42, 0.9871, 18, 165.84, -20.41, 0.0129, 2, 17, 114.3, -55.8, 0.99965, 18, 184.2, -7.52, 0.00035, 1, 17, 95.45, -65.84, 1, 1, 17, 67.83, -51.01, 1, 1, 17, 37.18, -36.66, 1, 2, 16, 158.88, 6.62, 0.00104, 17, 10.84, -27.57, 0.99896, 2, 16, 143.84, -19, 0.57965, 17, -16.73, -16.51, 0.42035, 2, 17, 97.91, -37.98, 0.99995, 18, 193.52, -29.87, 5e-05, 3, 17, 109.5, -12.85, 0.98552, 18, 174.04, -49.54, 0.00022, 45, -57.76, 557.05, 0.01426, 2, 17, 183.99, -12.97, 0.99111, 45, -131.72, 565.94, 0.00889, 3, 17, 245.16, -9.34, 0.87337, 18, 45.34, -6.53, 0.12108, 45, -192.89, 569.52, 0.00555, 2, 17, 248.26, 17.81, 0.9947, 45, -199.16, 542.92, 0.0053, 2, 17, 185.06, 22.67, 0.9921, 45, -136.97, 530.66, 0.0079, 2, 17, 127.59, 23.83, 0.98796, 45, -80.04, 522.75, 0.01204, 2, 17, 276.01, 2.01, 0.99751, 45, -224.86, 561.88, 0.00249, 2, 17, 299.28, -2.47, 0.34101, 18, -7.88, 5.49, 0.65899, 2, 17, 74.34, -11.7, 0.98729, 45, -22.98, 551.77, 0.01271, 3, 16, 118.39, 94.63, 0.0118, 17, 91.86, 25.53, 0.97161, 45, -44.75, 516.86, 0.0166, 3, 16, 111.81, 65.12, 0.08612, 17, 61.7, 27.66, 0.89468, 45, -15.05, 511.2, 0.0192, 3, 16, 104.96, 36.86, 0.35484, 17, 32.73, 30.24, 0.62526, 45, 13.41, 505.23, 0.0199, 3, 16, 131.44, 14.27, 0.00941, 17, 14.33, 0.69, 0.97976, 45, 35.16, 532.41, 0.01083, 2, 17, 41.78, -7.67, 0.98923, 45, 8.88, 543.94, 0.01077], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 32, 34, 22, 24, 24, 26, 8, 10, 10, 12, 12, 14, 50, 0, 54, 56, 34, 36, 36, 38, 14, 16, 16, 18, 56, 58, 60, 62, 62, 64, 72, 64, 54, 70, 70, 80, 74, 72], "width": 128, "height": 344}}, "arm_Rb": {"arm_Rb": {"type": "mesh", "uvs": [0.93341, 0.02764, 0.99774, 0.1023, 0.90908, 0.13357, 0.73023, 0.15176, 0.54775, 0.15279, 0.51415, 0.18301, 0.4497, 0.22498, 0.35154, 0.25438, 0.30163, 0.27769, 0.30714, 0.30838, 0.3234, 0.3413, 0.43038, 0.47483, 0.54069, 0.55646, 0.75929, 0.66982, 0.83495, 0.72704, 0.85593, 0.7721, 0.85916, 0.82278, 0.82326, 0.87595, 0.76784, 0.92861, 0.72602, 0.97249, 0.64065, 0.99473, 0.51498, 0.99505, 0.4313, 0.94343, 0.35044, 0.90346, 0.2141, 0.81894, 0.15993, 0.70225, 0.01998, 0.34517, 0.00468, 0.30695, 0.00335, 0.26562, 0.03459, 0.22936, 0.08003, 0.19727, 0.20304, 0.08873, 0.25194, 0.04558, 0.37019, 0.02812, 0.55766, 0.00044], "triangles": [19, 22, 18, 33, 4, 31, 33, 31, 32, 3, 4, 34, 4, 33, 34, 2, 3, 0, 2, 0, 1, 0, 3, 34, 28, 29, 8, 7, 8, 30, 8, 29, 30, 7, 30, 6, 6, 30, 5, 30, 31, 5, 5, 31, 4, 24, 15, 16, 24, 14, 15, 21, 22, 20, 20, 22, 19, 22, 23, 18, 16, 23, 24, 24, 25, 13, 25, 12, 13, 25, 11, 12, 11, 26, 10, 11, 25, 26, 26, 9, 10, 26, 27, 9, 27, 8, 9, 27, 28, 8, 24, 13, 14, 17, 23, 16, 18, 23, 17], "vertices": [1, 20, 75.35, 12.81, 1, 1, 20, 78.18, -15.38, 1, 2, 20, 65.27, -24.54, 0.99714, 17, -0.02, 3.39, 0.00286, 1, 20, 42.26, -26.95, 1, 2, 19, 57.93, -29.18, 0.34962, 20, 19.95, -23.15, 0.65038, 2, 19, 46.17, -29.3, 0.7262, 20, 13.82, -33.19, 0.2738, 3, 18, 252.24, -39.07, 0.00069, 19, 29.03, -27.42, 0.94485, 20, 3.15, -46.73, 0.05445, 3, 18, 244.65, -24.75, 0.09176, 19, 14.63, -19.98, 0.90448, 20, -10.79, -55, 0.00376, 2, 18, 237.82, -16.77, 0.49031, 19, 4.47, -17.3, 0.50969, 2, 18, 226.8, -14.85, 0.93379, 19, -5.68, -22, 0.06621, 1, 18, 214.68, -14.04, 1, 1, 18, 164.32, -15.7, 1, 1, 18, 132.25, -22.14, 1, 1, 18, 85.83, -38.96, 1, 1, 18, 63.4, -43.27, 1, 2, 18, 46.84, -42.01, 0.95714, 17, 231.63, 23.5, 0.04286, 2, 18, 28.8, -38.13, 0.81668, 17, 249.91, 26.01, 0.18332, 2, 18, 11, -29.33, 0.481, 17, 269.65, 23.81, 0.519, 2, 18, -6.06, -18.21, 0.13081, 17, 289.48, 19.18, 0.86919, 2, 18, -20.4, -9.47, 0.25564, 17, 305.94, 15.86, 0.74436, 2, 18, -25.82, 2.71, 0.44757, 17, 315.2, 6.27, 0.55243, 2, 18, -22.33, 17.89, 0.63666, 17, 317.1, -9.2, 0.36334, 2, 18, -1.65, 23.64, 0.92886, 17, 299.62, -21.66, 0.07114, 1, 18, 14.82, 30.03, 1, 1, 18, 48.66, 39.36, 1, 1, 18, 91.54, 36.07, 1, 1, 18, 222.01, 22.89, 1, 2, 18, 235.98, 21.52, 0.93411, 19, -18.85, 13.12, 0.06589, 2, 18, 250.66, 18.2, 0.48393, 19, -4.89, 18.75, 0.51607, 2, 18, 262.6, 11.38, 0.06732, 19, 8.81, 19.94, 0.93268, 1, 19, 21.74, 18.95, 1, 2, 19, 64.09, 19.12, 0.83077, 20, -17.76, 7.66, 0.16923, 2, 19, 80.93, 19.19, 0.34538, 20, -8.9, 21.97, 0.65462, 2, 19, 92.18, 7.85, 0.05917, 20, 6.68, 25.52, 0.94083, 1, 20, 31.39, 31.13, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 36, 38, 38, 40, 40, 42, 48, 50, 12, 14, 14, 16, 18, 20, 20, 22, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 8, 10, 10, 12, 22, 24, 24, 26, 26, 28, 46, 48, 42, 44, 44, 46, 32, 34, 34, 36, 28, 30, 30, 32], "width": 124, "height": 364}}, "body": {"body": {"type": "mesh", "uvs": [0.62766, 0.01031, 0.64672, 0.02242, 0.66363, 0.03511, 0.68915, 0.0497, 0.70469, 0.06287, 0.70578, 0.07968, 0.7015, 0.09359, 0.69723, 0.10751, 0.72028, 0.11519, 0.76726, 0.11882, 0.80638, 0.11384, 0.79128, 0.12899, 0.81934, 0.14588, 0.80803, 0.15975, 0.7542, 0.16514, 0.73584, 0.17482, 0.78234, 0.18189, 0.79481, 0.19344, 0.76969, 0.21084, 0.72657, 0.21781, 0.74936, 0.23593, 0.74347, 0.25646, 0.71127, 0.27239, 0.68012, 0.28998, 0.65789, 0.30748, 0.65607, 0.32155, 0.67532, 0.33186, 0.70991, 0.34272, 0.75734, 0.35452, 0.80652, 0.36915, 0.86106, 0.38892, 0.9118, 0.41109, 0.95578, 0.43295, 0.98616, 0.45482, 0.99888, 0.48233, 0.99477, 0.50944, 0.97268, 0.53373, 0.9001, 0.53498, 0.79489, 0.53715, 0.69184, 0.53743, 0.60865, 0.53612, 0.52545, 0.53501, 0.52889, 0.55443, 0.5296, 0.58094, 0.53673, 0.62241, 0.54051, 0.66106, 0.54077, 0.68023, 0.55007, 0.70122, 0.55623, 0.7174, 0.55534, 0.73458, 0.5533, 0.7513, 0.55456, 0.76768, 0.58659, 0.84001, 0.59329, 0.89483, 0.59611, 0.9473, 0.59892, 0.99977, 0.44822, 1, 0.42126, 0.95013, 0.36559, 0.87318, 0.33851, 0.82406, 0.34253, 0.77511, 0.33823, 0.7585, 0.32896, 0.74082, 0.31647, 0.72441, 0.29786, 0.70966, 0.27434, 0.69414, 0.21242, 0.65327, 0.14067, 0.60027, 0.09917, 0.56469, 0.08495, 0.53495, 0.07938, 0.50864, 0.08325, 0.48311, 0.0964, 0.45692, 0.11707, 0.43211, 0.1424, 0.40883, 0.16896, 0.38538, 0.19922, 0.36966, 0.22068, 0.35623, 0.23222, 0.34025, 0.23226, 0.32825, 0.21545, 0.31489, 0.17724, 0.29957, 0.14246, 0.28837, 0.08676, 0.27907, 0.0528, 0.26463, 0.04247, 0.24837, 0.05108, 0.23224, 0.07912, 0.21878, 0.00688, 0.21203, 0.01772, 0.19756, 1e-05, 0.18602, 0.01035, 0.16969, 0.0207, 0.15489, 0.0554, 0.14641, 0.09913, 0.14297, 0.13842, 0.13636, 0.09987, 0.12304, 0.11013, 0.11225, 0.14305, 0.10345, 0.16249, 0.09368, 0.17185, 0.07992, 0.19947, 0.06963, 0.2249, 0.06116, 0.24145, 0.05136, 0.25301, 0.03763, 0.26275, 0.02563, 0.27273, 0.01368, 0.2745, 0.00365, 0.30894, 0.00329, 0.34337, 0.00294, 0.53506, 0.00097, 0.57149, 0.00059, 0.60791, 0.00022, 0.23247, 0.29411, 0.32689, 0.29139, 0.40729, 0.28498, 0.4905, 0.2909, 0.57969, 0.29312, 0.64976, 0.28696, 0.1453, 0.19582, 0.13807, 0.18145, 0.33517, 0.07072, 0.36332, 0.12644, 0.35831, 0.09862, 0.34856, 0.08524, 0.36551, 0.11379, 0.53588, 0.07134, 0.51995, 0.1263, 0.52145, 0.0989, 0.52867, 0.08512, 0.5167, 0.11268, 0.55492, 0.13585, 0.62013, 0.13894, 0.75734, 0.14721, 0.7907, 0.15263, 0.68566, 0.14259, 0.31493, 0.13752, 0.15951, 0.1456, 0.24273, 0.14084, 0.10049, 0.15254, 0.11628, 0.16981, 0.18761, 0.12212, 0.27661, 0.12133, 0.25461, 0.10551, 0.26961, 0.09337, 0.27105, 0.07904, 0.3336, 0.05484, 0.3376, 0.03928, 0.34176, 0.02729, 0.28507, 0.06591, 0.54662, 0.05684, 0.55094, 0.04143, 0.54806, 0.02754, 0.54228, 0.0133, 0.34329, 0.0134, 0.62343, 0.12407, 0.61365, 0.10906, 0.61631, 0.09546, 0.62254, 0.07951, 0.62343, 0.06521, 0.61543, 0.05185, 0.60654, 0.03825, 0.70354, 0.12933, 0.68532, 0.20059, 0.21566, 0.1865, 0.28937, 0.18643, 0.38137, 0.21223, 0.35614, 0.19877, 0.40416, 0.19884, 0.46268, 0.18575, 0.54685, 0.18106, 0.62358, 0.18598, 0.62324, 0.25171, 0.16441, 0.25601, 0.45688, 0.22075, 0.51174, 0.20837, 0.59456, 0.20474, 0.38621, 0.23576, 0.67662, 0.21472, 0.71981, 0.23733, 0.71444, 0.25851, 0.66016, 0.27666, 0.56837, 0.28295, 0.48384, 0.27843, 0.40065, 0.26919, 0.32664, 0.27922, 0.23866, 0.28486, 0.15257, 0.28153, 0.09169, 0.26948, 0.07447, 0.24751, 0.09063, 0.22811, 0.14604, 0.21416, 0.22741, 0.21062, 0.31029, 0.2238, 0.54744, 0.23701, 0.62018, 0.22988, 0.6752, 0.23553, 0.6696, 0.26554, 0.60992, 0.2712, 0.5605, 0.26899, 0.53532, 0.25558, 0.68825, 0.25128, 0.18557, 0.23375, 0.12869, 0.23827, 0.10724, 0.25131, 0.12589, 0.26828, 0.16566, 0.27414, 0.23467, 0.27119, 0.25984, 0.25742, 0.24772, 0.24045, 0.39319, 0.25464, 0.19504, 0.16418, 0.29581, 0.16196, 0.40339, 0.16166, 0.46866, 0.16072, 0.56051, 0.15727, 0.65495, 0.16104, 0.3994, 0.1463, 0.40169, 0.1274, 0.40245, 0.11373, 0.40016, 0.09926, 0.39711, 0.08458, 0.39483, 0.0689, 0.47867, 0.06894, 0.47638, 0.08462, 0.47409, 0.0999, 0.47104, 0.11377, 0.47181, 0.12784, 0.47257, 0.14659, 0.38101, 0.18453, 0.3497, 0.3088, 0.36585, 0.3242, 0.37703, 0.34058, 0.37827, 0.35959, 0.36958, 0.37926, 0.3584, 0.40023, 0.35467, 0.42475, 0.3584, 0.45294, 0.35715, 0.47096, 0.51619, 0.49095, 0.63038, 0.4615, 0.79301, 0.43332, 0.22268, 0.46278, 0.48404, 0.30391, 0.48901, 0.32062, 0.49771, 0.3357, 0.51013, 0.35438, 0.53125, 0.37322, 0.55486, 0.39485, 0.57474, 0.41877, 0.60642, 0.44502, 0.74371, 0.40566, 0.7052, 0.38338, 0.66295, 0.36306, 0.62319, 0.34831, 0.59834, 0.33193, 0.58965, 0.31816, 0.58592, 0.30604, 0.28276, 0.31196, 0.29395, 0.32769, 0.29518, 0.3408, 0.28773, 0.3585, 0.27283, 0.37488, 0.25295, 0.39553, 0.24425, 0.41847, 0.24922, 0.44174, 0.6639, 0.4804, 0.5187, 0.51133, 0.8221, 0.45518, 0.82563, 0.50947, 0.8375, 0.48047, 0.65444, 0.51057, 0.5019, 0.4736, 0.48344, 0.45058, 0.38858, 0.51251, 0.39128, 0.53345, 0.20237, 0.51315, 0.20169, 0.48443, 0.36557, 0.48861, 0.20322, 0.54287, 0.21933, 0.57143, 0.3628, 0.68946, 0.37753, 0.70466, 0.3905, 0.7229, 0.40022, 0.73886, 0.41102, 0.75539, 0.41318, 0.77248, 0.50933, 0.76935, 0.50825, 0.75225, 0.50825, 0.73572, 0.50933, 0.71948, 0.50177, 0.70067, 0.48989, 0.68215, 0.28527, 0.62884, 0.41318, 0.56312, 0.45315, 0.62125, 0.40958, 0.82159, 0.50674, 0.94972, 0.52916, 0.82435, 0.59564, 0.02579, 0.58548, 0.01214, 0.29075, 0.0526, 0.29906, 0.03822, 0.30368, 0.02652, 0.30553, 0.0136, 0.77067, 0.15702], "triangles": [31, 241, 251, 31, 251, 30, 241, 31, 32, 250, 251, 241, 268, 241, 32, 268, 32, 33, 240, 250, 241, 240, 241, 268, 266, 240, 268, 270, 268, 33, 266, 268, 270, 270, 33, 34, 35, 270, 34, 269, 266, 270, 269, 270, 35, 271, 266, 269, 35, 37, 269, 36, 37, 35, 271, 40, 267, 269, 39, 271, 38, 269, 37, 39, 40, 271, 269, 38, 39, 297, 53, 54, 289, 290, 49, 284, 283, 289, 62, 283, 284, 50, 289, 49, 288, 289, 50, 284, 289, 288, 285, 284, 288, 61, 62, 284, 61, 284, 285, 288, 50, 51, 287, 288, 51, 285, 288, 287, 286, 285, 287, 61, 285, 286, 60, 61, 286, 296, 60, 286, 59, 60, 296, 298, 287, 51, 296, 286, 287, 298, 296, 287, 298, 51, 52, 58, 59, 296, 298, 52, 297, 296, 297, 58, 297, 296, 298, 53, 297, 52, 57, 58, 297, 297, 54, 55, 56, 57, 297, 56, 297, 55, 73, 264, 265, 265, 236, 237, 242, 72, 73, 265, 242, 73, 242, 265, 237, 238, 242, 237, 277, 71, 72, 242, 277, 72, 70, 71, 277, 274, 278, 239, 277, 278, 276, 238, 277, 242, 278, 277, 238, 276, 278, 274, 70, 277, 276, 275, 274, 267, 69, 70, 276, 275, 267, 41, 275, 279, 276, 275, 276, 274, 69, 276, 279, 294, 275, 41, 294, 41, 42, 68, 69, 279, 294, 280, 279, 294, 279, 275, 68, 279, 280, 294, 42, 43, 67, 68, 280, 295, 294, 43, 295, 43, 44, 293, 280, 294, 293, 294, 295, 67, 280, 293, 66, 67, 293, 45, 292, 295, 45, 295, 44, 292, 45, 46, 281, 293, 295, 281, 66, 293, 295, 292, 281, 65, 66, 281, 291, 292, 46, 282, 281, 292, 291, 46, 47, 291, 282, 292, 64, 65, 281, 64, 281, 282, 290, 291, 47, 290, 47, 48, 283, 282, 291, 290, 283, 291, 63, 64, 282, 283, 63, 282, 49, 290, 48, 289, 283, 290, 62, 63, 283, 77, 78, 261, 262, 77, 261, 76, 77, 262, 234, 261, 233, 262, 261, 234, 263, 76, 262, 75, 76, 263, 235, 262, 234, 263, 262, 235, 74, 75, 263, 264, 74, 263, 264, 263, 235, 236, 264, 235, 73, 74, 264, 265, 264, 236, 254, 26, 27, 253, 254, 27, 253, 27, 28, 247, 254, 253, 252, 253, 28, 252, 28, 29, 248, 253, 252, 251, 252, 29, 251, 29, 30, 249, 252, 251, 248, 247, 253, 249, 248, 252, 234, 233, 247, 250, 249, 251, 234, 248, 235, 248, 234, 247, 249, 235, 248, 273, 249, 250, 235, 249, 236, 273, 236, 249, 237, 236, 273, 272, 273, 250, 272, 250, 240, 238, 273, 272, 273, 238, 237, 239, 278, 238, 239, 272, 240, 239, 240, 266, 239, 238, 272, 271, 239, 266, 267, 239, 271, 274, 239, 267, 40, 41, 267, 300, 111, 112, 300, 112, 0, 153, 110, 111, 153, 111, 300, 154, 108, 109, 304, 107, 108, 304, 108, 154, 106, 107, 304, 299, 300, 0, 299, 0, 1, 303, 106, 304, 105, 106, 303, 148, 304, 154, 303, 304, 148, 152, 153, 300, 152, 300, 299, 302, 105, 303, 104, 105, 302, 161, 299, 1, 161, 1, 2, 147, 303, 148, 302, 303, 147, 161, 151, 152, 161, 152, 299, 160, 161, 2, 160, 2, 3, 151, 161, 160, 301, 104, 302, 103, 104, 301, 301, 302, 147, 146, 301, 147, 154, 110, 153, 151, 222, 152, 150, 151, 160, 159, 160, 3, 159, 3, 4, 150, 160, 159, 149, 103, 301, 149, 301, 146, 102, 103, 149, 152, 154, 153, 110, 154, 109, 222, 151, 223, 152, 148, 154, 152, 147, 148, 222, 147, 152, 146, 147, 222, 150, 223, 151, 121, 146, 222, 149, 146, 121, 126, 223, 150, 126, 150, 159, 145, 102, 149, 145, 149, 121, 101, 102, 145, 158, 126, 159, 159, 4, 5, 158, 159, 5, 221, 222, 223, 124, 121, 222, 224, 221, 223, 224, 223, 126, 129, 224, 126, 129, 126, 158, 221, 124, 222, 145, 121, 124, 145, 100, 101, 144, 145, 124, 144, 100, 145, 6, 158, 5, 157, 129, 158, 157, 158, 6, 123, 124, 221, 128, 224, 129, 220, 221, 224, 99, 100, 144, 144, 124, 123, 128, 129, 157, 123, 221, 220, 225, 220, 224, 225, 224, 128, 143, 99, 144, 7, 157, 6, 156, 128, 157, 156, 157, 7, 130, 225, 128, 130, 128, 156, 219, 220, 225, 226, 219, 225, 226, 225, 130, 125, 123, 220, 125, 220, 219, 123, 143, 144, 125, 143, 123, 125, 142, 143, 143, 98, 99, 141, 143, 142, 141, 98, 143, 155, 156, 7, 127, 130, 156, 127, 156, 155, 226, 130, 127, 122, 142, 125, 219, 122, 125, 218, 122, 219, 227, 226, 127, 226, 218, 219, 226, 227, 218, 97, 98, 141, 96, 97, 141, 95, 96, 141, 138, 141, 142, 137, 95, 141, 138, 137, 141, 94, 95, 137, 139, 94, 137, 93, 94, 139, 211, 137, 138, 211, 138, 212, 140, 139, 137, 140, 137, 211, 120, 140, 211, 92, 93, 139, 212, 164, 211, 164, 120, 211, 119, 120, 164, 91, 140, 90, 120, 89, 90, 92, 139, 140, 140, 91, 92, 140, 120, 90, 119, 89, 120, 162, 155, 7, 11, 9, 10, 8, 162, 7, 132, 155, 162, 135, 132, 162, 9, 162, 8, 11, 162, 9, 133, 11, 12, 11, 133, 162, 135, 162, 133, 134, 133, 12, 305, 133, 134, 13, 134, 12, 305, 134, 13, 216, 132, 135, 215, 132, 216, 14, 135, 133, 14, 133, 305, 216, 135, 14, 14, 305, 13, 15, 216, 14, 171, 216, 15, 163, 171, 15, 163, 15, 16, 163, 16, 17, 131, 127, 155, 136, 142, 122, 132, 131, 155, 138, 142, 136, 217, 122, 218, 136, 122, 217, 228, 227, 127, 228, 127, 131, 215, 131, 132, 228, 131, 215, 228, 218, 227, 214, 228, 215, 228, 217, 218, 214, 217, 228, 213, 217, 214, 212, 138, 136, 212, 136, 217, 212, 217, 213, 170, 214, 215, 171, 170, 215, 229, 212, 213, 169, 213, 214, 169, 214, 170, 229, 213, 169, 216, 171, 215, 165, 212, 229, 212, 165, 164, 167, 165, 229, 168, 229, 169, 167, 229, 168, 176, 170, 171, 176, 171, 163, 175, 169, 170, 175, 170, 176, 168, 169, 175, 192, 164, 165, 192, 165, 167, 119, 164, 192, 18, 163, 17, 166, 167, 168, 191, 119, 192, 178, 176, 163, 19, 178, 163, 18, 19, 163, 89, 119, 87, 87, 119, 191, 88, 89, 87, 174, 168, 175, 166, 168, 174, 193, 192, 167, 193, 167, 166, 190, 87, 191, 195, 176, 178, 86, 87, 190, 202, 191, 192, 209, 202, 192, 203, 190, 191, 196, 195, 178, 179, 196, 178, 177, 166, 174, 193, 166, 177, 19, 179, 178, 194, 175, 176, 194, 176, 195, 174, 175, 194, 20, 179, 19, 202, 203, 191, 193, 209, 192, 189, 86, 190, 189, 190, 203, 85, 86, 189, 201, 196, 179, 204, 189, 203, 172, 195, 196, 172, 196, 201, 194, 195, 172, 177, 200, 210, 194, 177, 174, 194, 200, 177, 200, 194, 172, 173, 203, 202, 173, 202, 209, 204, 203, 173, 21, 179, 20, 180, 201, 179, 177, 208, 209, 177, 209, 193, 208, 177, 210, 173, 209, 208, 21, 180, 179, 84, 85, 189, 188, 84, 189, 197, 172, 201, 197, 201, 180, 205, 204, 173, 204, 188, 189, 199, 200, 172, 198, 199, 172, 184, 210, 200, 205, 188, 204, 207, 173, 208, 197, 198, 172, 180, 181, 197, 22, 180, 21, 206, 173, 207, 205, 173, 206, 181, 198, 197, 22, 181, 180, 183, 184, 200, 183, 200, 199, 83, 84, 188, 185, 208, 210, 185, 210, 184, 207, 208, 185, 187, 205, 206, 188, 205, 187, 182, 199, 198, 183, 199, 182, 182, 198, 181, 186, 207, 185, 206, 207, 186, 187, 206, 186, 115, 184, 183, 185, 184, 115, 118, 182, 181, 118, 181, 22, 187, 83, 188, 82, 83, 187, 23, 118, 22, 116, 183, 182, 115, 183, 116, 114, 185, 115, 186, 185, 114, 117, 182, 118, 116, 182, 117, 113, 187, 186, 113, 186, 114, 82, 187, 113, 81, 82, 113, 243, 115, 116, 24, 257, 117, 24, 118, 23, 24, 117, 118, 230, 114, 115, 230, 115, 243, 258, 113, 114, 258, 114, 230, 80, 81, 113, 80, 113, 258, 117, 243, 116, 257, 243, 117, 256, 257, 24, 243, 257, 244, 244, 257, 256, 231, 230, 243, 25, 256, 24, 244, 231, 243, 259, 258, 230, 259, 230, 231, 79, 80, 258, 79, 258, 259, 255, 256, 25, 255, 25, 26, 245, 244, 256, 245, 256, 255, 232, 231, 244, 232, 244, 245, 260, 259, 231, 232, 260, 231, 78, 79, 259, 260, 78, 259, 254, 255, 26, 246, 245, 255, 246, 255, 254, 261, 78, 260, 246, 233, 232, 246, 232, 245, 247, 246, 254, 260, 233, 261, 233, 260, 232, 233, 246, 247], "vertices": [2, 5, 221.31, -83.24, 0.00516, 6, 140.61, -79.13, 0.99484, 2, 5, 200.5, -92.12, 0.02327, 6, 120.07, -88.62, 0.97673, 2, 5, 178.68, -100.03, 0.06529, 6, 98.49, -97.17, 0.93471, 3, 4, 431.35, -113.88, 0.00247, 5, 153.64, -111.9, 0.14484, 6, 73.81, -109.77, 0.8527, 3, 4, 408.61, -120.88, 0.01218, 5, 130.99, -119.19, 0.22862, 6, 51.39, -117.73, 0.7592, 4, 4, 379.61, -121.29, 0.04368, 13, 112.89, 140.57, 0.0261, 5, 102, -119.98, 0.34227, 6, 22.43, -119.37, 0.58795, 4, 4, 355.62, -119.28, 0.08849, 13, 110.18, 116.64, 0.17431, 5, 77.98, -118.27, 0.38016, 6, -1.63, -118.37, 0.35704, 4, 4, 331.62, -117.26, 0.20086, 13, 107.46, 92.72, 0.32873, 5, 53.96, -116.57, 0.29211, 6, -25.69, -117.38, 0.17829, 4, 4, 318.34, -127.71, 0.28396, 13, 117.52, 79.13, 0.45762, 5, 40.81, -127.19, 0.18888, 6, -38.52, -128.38, 0.06954, 4, 4, 312.02, -149.07, 0.32135, 13, 138.68, 72.19, 0.5222, 5, 34.77, -148.63, 0.13771, 6, -43.92, -149.99, 0.01873, 4, 4, 320.55, -166.89, 0.34837, 13, 156.74, 80.2, 0.44262, 5, 43.54, -166.34, 0.19242, 6, -34.64, -167.43, 0.01658, 4, 4, 294.44, -159.94, 0.3009, 13, 149.03, 54.31, 0.62254, 5, 17.34, -159.73, 0.07479, 6, -61.02, -161.6, 0.00177, 3, 4, 265.27, -172.62, 0.24633, 13, 160.86, 24.77, 0.73915, 5, -11.67, -172.79, 0.01453, 2, 4, 241.36, -167.41, 0.1682, 13, 154.94, 1.02, 0.8318, 3, 4, 232.12, -142.88, 0.05396, 13, 130.16, -7.49, 0.93945, 45, 49.98, 241.6, 0.00659, 3, 4, 215.46, -134.48, 0.16946, 13, 121.27, -23.9, 0.81766, 45, 33.31, 250, 0.01287, 4, 4, 203.2, -155.6, 0.25655, 13, 142.02, -36.77, 0.71745, 52, 94.57, -97.38, 0.01988, 45, 21.06, 228.88, 0.00613, 4, 4, 183.26, -161.22, 0.36667, 13, 147.05, -56.87, 0.54726, 52, 74.62, -103, 0.07947, 45, 1.12, 223.27, 0.0066, 5, 4, 153.27, -149.7, 0.58561, 13, 134.66, -86.5, 0.2723, 8, -290.67, 70.62, 0, 52, 44.64, -91.48, 0.12819, 45, -28.87, 234.79, 0.01389, 5, 4, 141.31, -130.04, 0.71392, 13, 114.67, -97.89, 0.06636, 8, -281.18, 49.66, 0.00028, 52, 32.68, -71.83, 0.19514, 45, -40.83, 254.44, 0.0243, 4, 4, 110.02, -140.32, 0.78121, 8, -248.87, 56.06, 0.00244, 52, 1.38, -82.1, 0.19591, 45, -72.12, 244.17, 0.02044, 4, 4, 74.62, -137.53, 0.78367, 8, -214.07, 48.99, 0.00795, 52, -34.02, -79.32, 0.1979, 45, -107.52, 246.95, 0.01048, 4, 3, 124.85, -127.39, 0.03063, 4, 47.17, -122.8, 0.74909, 8, -188.62, 31.03, 0.02028, 52, -61.46, -64.58, 0.2, 4, 3, 96.39, -109.76, 0.18499, 4, 16.89, -108.54, 0.65652, 8, -160.29, 13.19, 0.05849, 52, -91.75, -50.32, 0.1, 3, 3, 67.6, -96.17, 0.42313, 4, -13.27, -98.33, 0.42155, 8, -131.6, -0.6, 0.15531, 4, 3, 43.59, -92.5, 0.51456, 4, -37.55, -97.43, 0.18019, 7, -70.78, 93.46, 0.00555, 8, -107.61, -4.44, 0.29971, 4, 3, 24.89, -99.11, 0.44968, 4, -55.36, -106.14, 0.07057, 7, -52.04, 99.93, 0.01807, 8, -88.87, 2.04, 0.46168, 4, 3, 4.44, -112.54, 0.29214, 4, -74.14, -121.82, 0.02029, 7, -31.5, 113.22, 0.0193, 8, -68.32, 15.32, 0.66827, 4, 3, -18.3, -131.58, 0.12848, 4, -94.55, -143.34, 0.00307, 8, -45.45, 34.2, 0.85816, 12, -145.59, -38.23, 0.01029, 3, 3, -45.98, -150.84, 0.02827, 8, -17.63, 53.27, 0.86087, 12, -123.55, -12.71, 0.11085, 3, 3, -82.76, -171.48, 0.00069, 8, 19.3, 73.64, 0.6073, 12, -93.04, 16.41, 0.392, 2, 8, 60.12, 91.79, 0.25487, 12, -58.2, 44.38, 0.74513, 2, 8, 100.02, 106.95, 0.05049, 12, -23.49, 69.21, 0.94951, 2, 8, 139.18, 115.98, 0.00131, 12, 12.07, 87.92, 0.99869, 2, 8, 187, 115.81, 4e-05, 12, 58.35, 99.96, 0.99996, 2, 8, 233.16, 108.14, 2e-05, 12, 104.95, 104.31, 0.99998, 2, 8, 273.49, 92.95, 1e-05, 12, 147.81, 99.91, 0.99999, 3, 8, 271.51, 59.91, 1e-05, 12, 154.32, 67.46, 0.98522, 47, -193.29, -234.56, 0.01477, 3, 8, 269.26, 11.95, 0, 12, 164.38, 20.52, 0.97665, 47, -241.15, -238.3, 0.02335, 4, 7, 300.73, 63.26, 0.12345, 10, 171.57, 164.17, 0.02946, 12, 171.09, -25.9, 0.82221, 47, -288.04, -238.78, 0.02489, 4, 7, 293.79, 25.98, 0.33548, 10, 160.72, 127.84, 0.14939, 12, 173.88, -63.71, 0.49582, 47, -325.9, -236.54, 0.01932, 3, 7, 287.17, -11.34, 0.34725, 10, 150.19, 91.43, 0.49694, 12, 177, -101.49, 0.15581, 3, 7, 320.61, -13.95, 0.15279, 10, 183.16, 85.29, 0.8027, 12, 210, -95.49, 0.04451, 3, 7, 366.02, -19.32, 0.03517, 10, 227.75, 75.14, 0.95931, 12, 255.28, -89.1, 0.00552, 2, 7, 437.41, -25.01, 0.00041, 10, 298.14, 61.93, 0.99959, 1, 10, 363.43, 48.36, 1, 1, 10, 395.65, 40.91, 1, 1, 10, 431.87, 36.75, 1, 2, 10, 459.69, 33.09, 0.8444, 11, -18.13, 30.16, 0.1556, 2, 10, 488.44, 25.92, 0.24662, 11, 11.47, 28.67, 0.75338, 1, 11, 40.26, 26.69, 1, 1, 11, 68.52, 26.23, 1, 2, 11, 193.72, 36.23, 0.50274, 12, 694.78, -4.08, 0.49726, 1, 12, 788.11, 11.49, 1, 1, 12, 877.65, 24.78, 1, 1, 11, 469.33, 31.77, 1, 1, 12, 976.68, -29.85, 1, 2, 11, 380.8, -45.88, 0.9969, 12, 893.04, -53.43, 0.0031, 1, 11, 247.23, -66.34, 1, 1, 11, 162.11, -75.56, 1, 2, 10, 534.34, -84.33, 0.01856, 11, 77.79, -70.64, 0.98144, 2, 10, 506.01, -79.68, 0.12136, 11, 49.09, -71.55, 0.87864, 2, 10, 475.35, -76.81, 0.38753, 11, 18.46, -74.65, 0.61247, 2, 10, 446.49, -75.87, 0.69548, 11, -10.04, -79.3, 0.30452, 2, 10, 419.8, -78.29, 0.89362, 11, -35.77, -86.83, 0.10638, 2, 10, 391.28, -82.59, 0.97988, 11, -62.92, -96.54, 0.02012, 1, 10, 316.2, -93.89, 1, 1, 10, 219.74, -104.76, 1, 1, 10, 155.66, -109.1, 1, 1, 10, 104.25, -103.67, 1, 1, 10, 59.49, -95.75, 1, 2, 9, 133.52, -111.41, 0.01178, 10, 17.01, -83.96, 0.98822, 2, 9, 89.43, -99.85, 0.12593, 10, -25.6, -67.8, 0.87407, 2, 9, 48.14, -85.2, 0.40744, 10, -65.11, -48.86, 0.59256, 3, 3, -78.49, 157.28, 0.00388, 9, 9.73, -68.76, 0.72507, 10, -101.57, -28.46, 0.27105, 3, 3, -39.75, 140.53, 0.07799, 9, -28.89, -51.74, 0.86581, 10, -138.17, -7.44, 0.0562, 5, 3, -14.44, 123.67, 0.22471, 4, -119.92, 110.68, 0.00129, 7, -14.3, -123.12, 0.0059, 9, -54.08, -34.7, 0.76281, 10, -161.42, 12.16, 0.00529, 4, 3, 7.43, 111.26, 0.40093, 4, -96.78, 100.84, 0.01415, 7, -36.07, -110.55, 0.0172, 9, -75.86, -22.13, 0.56771, 4, 3, 34.19, 102.8, 0.54753, 4, -69.23, 95.51, 0.07384, 7, -62.78, -101.9, 0.01115, 9, -102.56, -13.48, 0.36748, 4, 3, 54.74, 100.36, 0.55275, 4, -48.53, 95.44, 0.2027, 7, -83.31, -99.32, 0.00211, 9, -123.1, -10.9, 0.24244, 3, 3, 78.52, 105.25, 0.42084, 4, -25.47, 103.02, 0.4473, 9, -146.91, -15.62, 0.13186, 4, 3, 106.81, 119.41, 0.17575, 4, 1.02, 120.32, 0.67137, 9, -175.3, -29.58, 0.05288, 49, -99.78, 25.16, 0.1, 5, 3, 127.85, 132.86, 0.04742, 4, 20.38, 136.09, 0.73041, 5, -260.54, 132.72, 0.00017, 9, -196.43, -42.88, 0.02201, 49, -80.43, 40.93, 0.2, 5, 4, 36.51, 161.38, 0.7814, 5, -244.74, 158.22, 0.00149, 9, -215.52, -66.03, 0.00891, 49, -64.29, 66.23, 0.19795, 45, -145.63, 545.87, 0.01025, 5, 4, 61.46, 176.76, 0.77846, 5, -219.99, 173.92, 0.00426, 9, -242.15, -78.26, 0.00371, 49, -39.34, 81.61, 0.19661, 45, -120.68, 561.25, 0.01696, 4, 4, 89.52, 181.38, 0.78432, 9, -270.56, -79.43, 0.00136, 49, -11.28, 86.22, 0.19642, 45, -92.62, 565.86, 0.0179, 4, 4, 117.34, 177.38, 0.78327, 9, -297.69, -72.08, 0.0003, 49, 16.54, 82.22, 0.19589, 45, -64.8, 561.86, 0.02054, 4, 4, 140.52, 164.55, 0.70936, 16, 150.45, 90.5, 0.07352, 49, 39.71, 69.39, 0.19572, 45, -41.63, 549.03, 0.02141, 4, 4, 152.26, 197.38, 0.60832, 16, 182.9, 77.75, 0.28427, 49, 51.45, 102.23, 0.09918, 45, -29.88, 581.87, 0.00824, 4, 4, 177.21, 192.38, 0.47314, 16, 177.13, 52.96, 0.47116, 49, 76.41, 97.22, 0.0497, 45, -4.93, 576.87, 0.00599, 3, 4, 197.13, 200.38, 0.45926, 16, 184.5, 32.8, 0.53731, 45, 14.99, 584.86, 0.00343, 3, 4, 225.29, 195.59, 0.36933, 16, 178.84, 4.8, 0.62808, 45, 43.15, 580.07, 0.00259, 2, 4, 250.8, 190.8, 0.35991, 16, 173.26, -20.55, 0.64009, 3, 4, 265.39, 174.97, 0.31932, 16, 156.99, -34.63, 0.67714, 5, -16.06, 174.78, 0.00354, 3, 4, 271.25, 155.06, 0.26895, 16, 136.9, -39.88, 0.71741, 5, -9.93, 154.94, 0.01364, 4, 4, 282.61, 137.15, 0.26467, 16, 118.64, -50.67, 0.65895, 5, 1.65, 137.18, 0.06673, 6, -85.45, 134.72, 0.00965, 4, 4, 305.64, 154.62, 0.23585, 16, 135.39, -74.23, 0.4966, 5, 24.45, 154.95, 0.21452, 6, -63.18, 153.15, 0.05302, 4, 4, 324.23, 149.89, 0.2238, 16, 130.09, -92.67, 0.37844, 5, 43.11, 150.46, 0.31389, 6, -44.4, 149.22, 0.08387, 4, 4, 339.38, 134.87, 0.18636, 16, 114.6, -107.34, 0.28018, 5, 58.44, 135.64, 0.3879, 6, -28.63, 134.86, 0.14556, 4, 4, 356.2, 125.97, 0.13687, 16, 105.19, -123.87, 0.16411, 5, 75.38, 126.96, 0.44, 6, -11.45, 126.68, 0.25902, 4, 4, 379.91, 121.65, 0.08819, 16, 100.12, -147.44, 0.00379, 5, 99.15, 122.94, 0.454, 6, 12.43, 123.36, 0.45403, 3, 4, 397.63, 109.03, 0.04708, 5, 117.03, 110.56, 0.36816, 6, 30.67, 111.51, 0.58476, 3, 4, 412.21, 97.41, 0.0208, 5, 131.76, 99.13, 0.25806, 6, 45.72, 100.52, 0.72114, 3, 4, 429.09, 89.83, 0.00578, 5, 148.74, 91.77, 0.1423, 6, 62.91, 93.66, 0.85192, 3, 4, 452.76, 84.5, 0.00012, 5, 172.47, 86.75, 0.04906, 6, 86.78, 89.34, 0.95083, 2, 5, 193.22, 82.52, 0.01471, 6, 107.65, 85.73, 0.98529, 2, 5, 213.87, 78.19, 0.00223, 6, 128.42, 82, 0.99777, 2, 5, 231.19, 77.55, 5e-05, 6, 145.75, 81.88, 0.99995, 2, 6, 146.98, 66.25, 0.98012, 33, -222, 116.32, 0.01988, 1, 6, 148.21, 50.62, 1, 1, 6, 155.04, -36.4, 1, 2, 6, 156.34, -52.93, 0.98007, 33, -212.63, -2.87, 0.01993, 2, 5, 238.62, -74.08, 0.00059, 6, 157.64, -69.47, 0.99941, 5, 3, 113.22, 93.35, 0.14651, 4, 10.37, 95.16, 0.58382, 9, -181.52, -3.47, 0.04687, 49, -90.43, 0.01, 0.1943, 45, -171.77, 479.65, 0.02851, 5, 3, 112.83, 50.14, 0.12862, 4, 14.92, 52.19, 0.60993, 9, -180.82, 39.74, 0.02944, 49, -85.88, -42.97, 0.192, 45, -167.22, 436.67, 0.04, 5, 4, 25.87, 15.57, 0.77438, 9, -187.24, 77.41, 0.00322, 52, -82.76, 73.79, 0.0864, 49, -74.93, -79.58, 0.096, 45, -156.27, 400.06, 0.04, 5, 3, 104.94, -23.89, 0.07231, 4, 15.55, -22.25, 0.68443, 8, -169.44, -72.61, 0.01127, 52, -93.09, 35.96, 0.192, 45, -166.59, 362.23, 0.04, 5, 3, 96.36, -63.74, 0.19057, 4, 11.59, -62.83, 0.52726, 8, -160.59, -32.82, 0.05425, 52, -97.04, -4.61, 0.19302, 45, -170.55, 321.66, 0.0349, 5, 3, 103.17, -96.65, 0.14348, 4, 22.13, -94.74, 0.58974, 8, -167.16, 0.14, 0.04846, 52, -86.51, -36.52, 0.19542, 45, -160.02, 289.75, 0.0229, 4, 4, 180.03, 134.32, 0.54221, 16, 119.01, 51.95, 0.27899, 49, 79.23, 39.16, 0.14492, 45, -2.11, 518.81, 0.03388, 4, 4, 204.83, 137.54, 0.20529, 16, 121.45, 27.06, 0.76522, 49, 104.03, 42.38, 0.0098, 45, 22.69, 522.02, 0.01969, 3, 4, 395.58, 47.29, 0.00535, 5, 115.78, 48.8, 0.19748, 6, 31.23, 49.74, 0.79717, 3, 4, 299.42, 34.77, 0.47246, 16, 15.79, -64.29, 0.04306, 5, 19.79, 35.03, 0.48448, 3, 4, 347.41, 36.9, 0.03308, 5, 67.75, 37.78, 0.66299, 6, -16.45, 37.32, 0.30393, 3, 4, 370.51, 41.27, 0.0127, 5, 90.79, 42.45, 0.42098, 6, 6.44, 42.66, 0.56632, 3, 4, 321.23, 33.7, 0.20273, 5, 41.61, 34.25, 0.70635, 6, -42.48, 33.01, 0.09091, 3, 4, 394.23, -44.03, 0.00172, 5, 115.62, -42.53, 0.17012, 6, 33.76, -41.56, 0.82816, 3, 4, 299.45, -36.5, 0.49535, 13, 25.79, 62.92, 0.04176, 5, 20.74, -36.24, 0.46289, 3, 4, 346.71, -37.33, 0.0234, 5, 68.01, -36.45, 0.63815, 6, -14, -36.88, 0.33845, 3, 4, 370.47, -40.68, 0.00653, 5, 91.81, -39.49, 0.39583, 6, 9.88, -39.22, 0.59765, 3, 4, 322.94, -35.09, 0.14222, 5, 44.22, -34.52, 0.69383, 6, -37.84, -35.65, 0.16395, 3, 4, 282.92, -52.36, 0.64471, 13, 41.16, 45.93, 0.23231, 5, 4.42, -52.31, 0.12298, 4, 4, 277.51, -82.02, 0.4869, 13, 70.65, 39.66, 0.46986, 5, -0.6, -82.03, 0.03397, 45, 95.37, 302.47, 0.00927, 2, 4, 263.05, -144.4, 0.14985, 13, 132.58, 23.38, 0.85015, 2, 4, 253.67, -159.56, 0.14459, 13, 147.45, 13.56, 0.85541, 3, 4, 271.13, -111.81, 0.28164, 13, 100.25, 32.4, 0.71117, 5, -6.6, -111.91, 0.00718, 3, 4, 280.37, 56.84, 0.60267, 16, 38.45, -45.93, 0.29264, 5, 0.45, 56.85, 0.10469, 4, 4, 266.64, 127.6, 0.23893, 16, 109.6, -34.41, 0.74953, 5, -14.19, 127.42, 0.00584, 45, 84.5, 512.08, 0.0057, 4, 4, 274.73, 89.71, 0.42167, 16, 71.47, -41.32, 0.54988, 5, -5.61, 89.64, 0.01835, 45, 92.59, 474.19, 0.01011, 3, 4, 254.74, 154.49, 0.18087, 16, 136.84, -23.35, 0.81672, 45, 72.6, 538.97, 0.00242, 3, 4, 224.94, 147.39, 0.06333, 16, 130.68, 6.65, 0.92796, 45, 42.8, 531.88, 0.00871, 5, 4, 307.1, 114.69, 0.27966, 16, 95.44, -74.45, 0.46831, 5, 26.43, 115.04, 0.17894, 6, -60.02, 113.32, 0.06808, 45, 124.96, 499.18, 0.00501, 5, 4, 308.35, 74.19, 0.33588, 16, 54.92, -74.44, 0.26611, 5, 28.2, 74.57, 0.30632, 6, -57.06, 72.92, 0.08287, 45, 126.21, 458.68, 0.00882, 6, 4, 335.67, 84.12, 0.16856, 16, 63.99, -102.06, 0.16314, 5, 55.4, 84.85, 0.43229, 6, -30.18, 84, 0.22883, 33, -399.16, 134.06, 0.00209, 45, 153.53, 468.61, 0.0051, 6, 4, 356.58, 77.24, 0.08331, 16, 56.46, -122.74, 0.00702, 5, 76.4, 78.23, 0.52719, 6, -9, 78, 0.37512, 33, -377.97, 128.07, 0.00425, 45, 174.44, 461.72, 0.00311, 5, 4, 381.3, 76.51, 0.03666, 5, 101.13, 77.82, 0.38071, 6, 15.73, 78.32, 0.57578, 33, -353.24, 128.39, 0.00672, 45, 199.16, 460.99, 0.00012, 3, 4, 422.96, 47.92, 0.00066, 5, 143.15, 49.78, 0.07307, 6, 58.57, 51.53, 0.92628, 2, 5, 170.01, 48.23, 0.02067, 6, 85.46, 50.77, 0.97933, 2, 5, 190.71, 46.54, 0.00406, 6, 106.2, 49.69, 0.99594, 4, 4, 403.94, 70.06, 0.01207, 5, 123.84, 71.67, 0.22111, 6, 38.62, 72.84, 0.75777, 33, -330.35, 122.91, 0.00905, 2, 5, 140.68, -47.17, 0.07381, 6, 58.95, -45.45, 0.92619, 2, 5, 167.28, -48.87, 0.02869, 6, 85.59, -46.37, 0.97131, 2, 5, 191.22, -47.32, 0.00829, 6, 109.47, -44.12, 0.99171, 2, 5, 215.75, -44.45, 0.00065, 6, 133.91, -40.52, 0.99935, 1, 6, 130.17, 49.94, 1, 5, 4, 303.16, -83.59, 0.34998, 13, 72.97, 65.25, 0.34117, 5, 25.06, -83.28, 0.22956, 6, -55.55, -84.95, 0.07029, 45, 121.02, 300.89, 0.00899, 6, 4, 329.06, -79.22, 0.18358, 13, 69.36, 91.27, 0.16033, 5, 50.9, -78.57, 0.42514, 6, -29.86, -79.48, 0.22129, 33, -398.84, -29.41, 0.00125, 45, 146.92, 305.26, 0.0084, 6, 4, 352.52, -80.51, 0.08239, 13, 71.33, 114.68, 0.02676, 5, 74.38, -79.55, 0.49574, 6, -6.37, -79.77, 0.38505, 33, -375.35, -29.7, 0.00355, 45, 170.37, 303.98, 0.00652, 5, 4, 380.01, -83.42, 0.03089, 5, 101.91, -82.1, 0.33964, 6, 21.22, -81.51, 0.61953, 33, -347.75, -31.45, 0.00623, 45, 197.87, 301.07, 0.00371, 5, 4, 404.68, -83.9, 0.00823, 5, 126.58, -82.26, 0.20576, 6, 45.89, -80.94, 0.77624, 33, -323.08, -30.88, 0.00867, 45, 222.54, 300.59, 0.0011, 4, 4, 427.75, -80.32, 0.00106, 5, 149.6, -78.39, 0.11042, 6, 68.78, -76.4, 0.8775, 33, -300.19, -26.33, 0.01101, 3, 5, 173.01, -74.11, 0.04894, 6, 92.07, -71.43, 0.93767, 33, -276.91, -21.36, 0.0134, 5, 4, 293.97, -120.02, 0.29325, 13, 109.11, 55, 0.60065, 5, 16.35, -119.82, 0.08339, 6, -63.19, -121.73, 0.01851, 45, 111.83, 264.47, 0.0042, 4, 4, 171.07, -111.36, 0.55128, 13, 96.86, -67.6, 0.24376, 52, 62.43, -53.15, 0.17452, 45, -11.07, 273.12, 0.03044, 4, 4, 196.02, 102.26, 0.49905, 16, 86.46, 36.96, 0.3652, 49, 95.22, 7.1, 0.09603, 45, 13.88, 486.74, 0.03973, 4, 4, 196.04, 68.72, 0.70512, 16, 52.94, 37.99, 0.15888, 49, 95.23, -26.44, 0.096, 45, 13.9, 453.21, 0.04, 4, 4, 151.41, 26.99, 0.77355, 52, 42.78, 85.21, 0.0955, 49, 50.61, -68.16, 0.08595, 45, -30.73, 411.48, 0.045, 4, 4, 174.66, 38.41, 0.79027, 16, 23.31, 60.3, 0.01613, 49, 73.85, -56.75, 0.1536, 45, -7.48, 422.89, 0.04, 4, 4, 174.47, 16.56, 0.79488, 13, -30.9, -60.46, 0.01152, 52, 65.83, 74.77, 0.1536, 45, -7.68, 401.04, 0.04, 4, 4, 196.97, -10.14, 0.80802, 13, -3.56, -38.74, 0.04638, 52, 88.34, 48.08, 0.1056, 45, 14.83, 374.35, 0.04, 4, 4, 204.95, -48.46, 0.70776, 13, 34.98, -31.89, 0.15624, 52, 96.31, 9.76, 0.096, 45, 22.8, 336.03, 0.04, 4, 4, 196.36, -83.35, 0.54327, 13, 69.6, -41.49, 0.31431, 52, 87.73, -25.13, 0.10599, 45, 14.22, 301.14, 0.03642, 4, 4, 82.97, -82.85, 0.69616, 8, -229, -4.27, 0.0057, 54, -0.14, -3.63, 0.23395, 45, -99.17, 301.63, 0.06419, 4, 4, 76.17, 125.94, 0.69651, 9, -250.57, -26.02, 0.00405, 51, -2.18, 7.82, 0.23352, 45, -105.97, 510.42, 0.06593, 3, 4, 136.61, -7.32, 0.756, 53, 40.01, 59.92, 0.189, 45, -45.54, 377.17, 0.055, 3, 4, 157.88, -32.34, 0.756, 53, 61.29, 34.89, 0.189, 45, -24.26, 352.14, 0.055, 4, 4, 164.04, -70.04, 0.73116, 13, 55.36, -73.41, 0.02596, 53, 67.44, -2.81, 0.18928, 45, -18.1, 314.44, 0.0536, 5, 4, 110.81, 24.91, 0.76543, 9, -272.69, 78.46, 2e-05, 53, 14.22, 92.15, 0.08505, 50, 21.57, -81.16, 0.0945, 45, -71.33, 409.4, 0.055, 5, 4, 146.7, -107.33, 0.72881, 13, 92.12, -91.83, 0.03454, 8, -289.29, 27.77, 0.00015, 53, 50.11, -40.1, 0.19087, 45, -35.44, 277.15, 0.04563, 4, 4, 107.66, -126.87, 0.76486, 8, -248.16, 42.42, 0.00255, 53, 11.06, -59.63, 0.19185, 45, -74.49, 257.62, 0.04074, 4, 4, 71.11, -124.32, 0.75811, 8, -212.2, 35.45, 0.00957, 53, -25.48, -57.08, 0.19192, 45, -111.03, 260.17, 0.0404, 5, 3, 120.27, -103.43, 0.05797, 4, 39.88, -99.53, 0.6777, 8, -184.21, 7.04, 0.02898, 53, -56.72, -32.29, 0.19116, 45, -142.26, 284.96, 0.0442, 5, 3, 114.4, -60.69, 0.06862, 4, 29.16, -57.73, 0.65952, 8, -178.65, -35.74, 0.02979, 53, -67.44, 9.51, 0.18948, 45, -152.98, 326.76, 0.05259, 4, 4, 37.07, -19.29, 0.75139, 8, -191.16, -72.94, 0.00461, 53, -59.53, 47.94, 0.189, 45, -145.08, 365.19, 0.055, 5, 4, 53.12, 18.51, 0.76401, 9, -214.64, 77.8, 0.00144, 53, -43.48, 85.75, 0.08505, 50, -36.13, -87.56, 0.0945, 45, -129.03, 403, 0.055, 4, 4, 35.92, 52.24, 0.74129, 9, -201.67, 42.24, 0.01471, 50, -53.33, -53.83, 0.189, 45, -146.22, 436.72, 0.055, 5, 3, 128.73, 88.68, 0.06192, 4, 26.31, 92.3, 0.66594, 9, -197, 1.31, 0.02961, 50, -62.94, -13.77, 0.18937, 45, -155.83, 476.78, 0.05316, 6, 3, 139.03, 126.91, 0.0157, 4, 32.16, 131.45, 0.72923, 5, -248.7, 128.23, 0.00033, 9, -207.57, -36.84, 0.01736, 50, -57.08, 25.38, 0.19065, 45, -149.98, 515.94, 0.04673, 5, 4, 53.04, 159.09, 0.75693, 5, -228.18, 156.14, 0.00246, 9, -231.64, -61.74, 0.00591, 50, -36.21, 53.02, 0.19132, 45, -129.1, 543.58, 0.04339, 4, 4, 90.96, 166.81, 0.764, 9, -270.22, -64.8, 0.00137, 50, 1.72, 60.74, 0.19134, 45, -91.18, 551.3, 0.04329, 4, 4, 124.41, 159.36, 0.76328, 9, -302.52, -53.34, 0.00013, 50, 35.16, 53.29, 0.19085, 45, -57.73, 543.85, 0.04574, 4, 4, 148.39, 134.08, 0.72103, 16, 119.75, 83.58, 0.03798, 50, 59.15, 28.01, 0.18975, 45, -33.75, 518.56, 0.05124, 4, 4, 154.38, 97.04, 0.74736, 16, 82.54, 78.74, 0.00864, 50, 65.14, -9.03, 0.189, 45, -27.76, 481.52, 0.055, 4, 4, 131.55, 59.4, 0.75598, 9, -297.46, 46.75, 2e-05, 50, 42.3, -46.67, 0.189, 45, -50.59, 443.88, 0.055, 4, 4, 108.43, -48.44, 0.74723, 8, -258.46, -35.33, 0.00077, 54, 25.32, 30.78, 0.187, 45, -73.71, 336.04, 0.065, 4, 4, 120.64, -81.57, 0.75096, 8, -266.55, -0.96, 0.0009, 54, 37.53, -2.35, 0.18796, 45, -61.5, 302.91, 0.06018, 4, 4, 110.81, -106.58, 0.75397, 8, -253.75, 22.66, 0.00207, 54, 27.7, -27.35, 0.18901, 45, -71.34, 277.91, 0.05494, 4, 4, 59.05, -103.88, 0.74088, 8, -202.71, 13.69, 0.01604, 54, -24.06, -24.65, 0.18923, 45, -123.09, 280.61, 0.05385, 5, 3, 132.31, -81.84, 0.0065, 4, 49.37, -76.69, 0.7263, 8, -196.4, -14.46, 0.01999, 54, -33.74, 2.53, 0.1882, 45, -132.77, 307.79, 0.05901, 4, 4, 53.26, -54.22, 0.73669, 8, -202.99, -36.3, 0.01237, 54, -29.85, 25, 0.18726, 45, -128.88, 330.27, 0.06368, 4, 4, 76.42, -42.83, 0.74463, 8, -227.36, -44.79, 0.00337, 54, -6.69, 36.39, 0.187, 45, -105.73, 341.65, 0.065, 4, 4, 83.63, -112.44, 0.75082, 8, -226.07, 25.18, 0.00662, 54, 0.52, -33.21, 0.18936, 45, -98.51, 272.05, 0.0532, 4, 4, 114.55, 116.19, 0.74804, 9, -287.49, -11.69, 0.00062, 51, 36.21, -1.92, 0.18716, 45, -67.59, 500.68, 0.06418, 4, 4, 106.82, 142.1, 0.75214, 9, -282.96, -38.34, 0.0008, 51, 28.47, 23.98, 0.18823, 45, -75.32, 526.58, 0.05883, 4, 4, 84.36, 151.92, 0.75288, 9, -261.86, -50.82, 0.00221, 51, 6.01, 33.81, 0.18877, 45, -97.78, 536.41, 0.05614, 5, 4, 55.06, 143.52, 0.7455, 5, -225.96, 140.6, 0.00198, 9, -231.76, -46.04, 0.00731, 51, -23.29, 25.41, 0.1887, 45, -127.08, 528.01, 0.05651, 5, 4, 44.89, 125.46, 0.7386, 5, -235.89, 122.41, 0.00074, 9, -219.47, -29.35, 0.01345, 51, -33.45, 7.34, 0.1882, 45, -137.25, 509.94, 0.05902, 5, 4, 49.89, 94.05, 0.73473, 5, -230.49, 91.06, 0.0003, 9, -220.62, 2.44, 0.01377, 51, -28.46, -24.07, 0.1872, 45, -132.25, 478.53, 0.064, 4, 4, 73.62, 82.52, 0.74292, 9, -242.77, 16.76, 0.00508, 51, -4.73, -35.59, 0.187, 45, -108.52, 467.01, 0.065, 4, 4, 102.91, 87.95, 0.74669, 9, -272.5, 14.93, 0.00131, 51, 24.56, -30.16, 0.187, 45, -79.23, 472.43, 0.065, 5, 4, 78.23, 21.83, 0.79509, 9, -239.97, 77.56, 0.00052, 54, -4.88, 101.06, 0.0752, 51, -0.12, -96.28, 0.06918, 45, -103.92, 406.32, 0.06, 3, 4, 234.54, 111.53, 0.17964, 16, 94.53, -1.82, 0.79921, 45, 52.4, 496.01, 0.02114, 3, 4, 238.24, 65.66, 0.48153, 16, 48.58, -4.09, 0.49214, 45, 56.09, 450.15, 0.02633, 3, 4, 238.6, 16.71, 0.8569, 16, -0.36, -2.94, 0.11587, 45, 56.46, 401.2, 0.02723, 3, 4, 240.14, -12.99, 0.90311, 13, 0.55, 4.33, 0.06988, 45, 58, 371.5, 0.02701, 3, 4, 245.97, -54.8, 0.61132, 13, 42.51, 8.93, 0.36317, 45, 63.83, 329.69, 0.02551, 3, 4, 239.34, -97.75, 0.28727, 13, 85.26, 1.04, 0.69339, 45, 57.2, 286.74, 0.01934, 3, 4, 265.1, 18.45, 0.92051, 16, 0.55, -29.48, 0.05757, 45, 82.96, 402.94, 0.02192, 3, 4, 297.7, 17.31, 0.44292, 5, 18.3, 17.55, 0.53487, 45, 115.56, 401.8, 0.02221, 3, 4, 321.28, 16.9, 0.22209, 5, 41.88, 17.44, 0.75555, 45, 139.14, 401.38, 0.02236, 4, 4, 346.25, 17.86, 0.00592, 5, 66.84, 18.73, 0.7242, 6, -16.8, 18.24, 0.2467, 45, 164.11, 402.35, 0.02318, 4, 4, 371.57, 19.17, 0.00069, 5, 92.14, 20.37, 0.3328, 6, 8.44, 20.63, 0.64264, 45, 189.43, 403.66, 0.02387, 3, 5, 119.17, 21.68, 0.04229, 6, 35.43, 22.74, 0.93342, 45, 216.48, 404.62, 0.02429, 3, 5, 119.49, -16.46, 0.03965, 6, 36.87, -15.38, 0.93702, 45, 216.3, 366.47, 0.02333, 3, 5, 92.43, -15.69, 0.26975, 6, 9.8, -15.41, 0.70724, 45, 189.26, 367.59, 0.02301, 3, 5, 66.07, -14.92, 0.75651, 6, -16.58, -15.41, 0.22069, 45, 162.9, 368.71, 0.0228, 4, 4, 321.12, -14.31, 0.10991, 5, 42.13, -13.77, 0.76015, 6, -40.54, -14.97, 0.10742, 45, 138.98, 370.17, 0.02251, 3, 4, 296.85, -14.59, 0.43076, 5, 17.86, -14.36, 0.54669, 45, 114.7, 369.9, 0.02255, 3, 4, 264.51, -14.84, 0.95532, 13, 3.12, 28.63, 0.02255, 45, 82.37, 369.65, 0.02213, 3, 4, 199.19, 27.02, 0.91415, 16, 11.16, 36.13, 0.05025, 45, 17.05, 411.5, 0.0356, 4, 3, 81.8, 43.36, 0.51817, 4, -15.13, 41.9, 0.37093, 9, -149.74, 46.3, 0.0712, 45, -197.27, 426.39, 0.0397, 5, 3, 54.55, 39.18, 0.75213, 4, -41.72, 34.63, 0.08335, 9, -122.46, 50.28, 0.1262, 45, -223.86, 419.12, 0.03348, 47, -436.37, 129.03, 0.00484, 6, 3, 25.88, 37.44, 0.774, 4, -70, 29.63, 0.00281, 7, -54, -36.6, 0.02137, 9, -93.79, 51.82, 0.16253, 45, -252.14, 414.11, 0.02702, 47, -431.28, 100.77, 0.01226, 5, 3, -6.75, 40.73, 0.52077, 7, -21.4, -40.12, 0.1975, 9, -61.18, 48.3, 0.24109, 45, -284.93, 413.64, 0.01969, 47, -430.72, 67.98, 0.02095, 5, 3, -39.97, 48.64, 0.13342, 7, 11.76, -48.27, 0.42237, 9, -28.02, 40.15, 0.40174, 45, -318.84, 417.7, 0.01247, 47, -434.67, 34.06, 0.03, 6, 3, -75.3, 57.94, 0.01079, 7, 47.03, -57.82, 0.36807, 9, 7.25, 30.6, 0.56247, 10, -93.53, 70.61, 0.01397, 45, -355, 422.9, 0.00508, 47, -439.76, -2.12, 0.03961, 4, 7, 88.79, -64.77, 0.38285, 9, 49.01, 23.65, 0.37885, 10, -52.74, 59.28, 0.19931, 47, -441.46, -44.42, 0.03899, 4, 7, 137.24, -69.14, 0.3883, 9, 97.46, 19.28, 0.09389, 10, -5.02, 49.81, 0.48387, 47, -439.76, -93.04, 0.03394, 4, 7, 168.02, -73.57, 0.33195, 9, 128.24, 14.85, 0.01284, 10, 25.12, 42.15, 0.62903, 47, -440.33, -124.13, 0.02617, 4, 7, 211.24, -6.06, 0.86325, 10, 75.24, 104.7, 0.08964, 12, 102.23, -115.75, 0.02854, 47, -367.97, -158.61, 0.01856, 4, 7, 167.3, 51.82, 0.61066, 8, 130.47, -46.08, 0.0155, 12, 44.98, -71, 0.35001, 47, -316.01, -107.81, 0.02384, 4, 7, 128.28, 131.28, 0.02028, 8, 91.45, 33.39, 0.0825, 12, -13.01, -4.11, 0.87298, 47, -242.01, -59.2, 0.02424, 3, 9, 106.63, -44.1, 0.016, 10, -2.6, -14.19, 0.96731, 47, -501.51, -110.03, 0.01669, 4, 3, 83, -18.34, 0.59748, 4, -6.88, -19.25, 0.33999, 8, -147.55, -78.32, 0.02264, 45, -189.02, 365.24, 0.03989, 5, 3, 54.1, -17.2, 0.81748, 4, -35.72, -21.43, 0.0909, 8, -118.66, -79.66, 0.05314, 45, -217.86, 363.06, 0.03352, 47, -380.33, 135.2, 0.00495, 5, 3, 27.81, -18.08, 0.86114, 4, -61.73, -25.31, 0.01889, 8, -92.36, -78.97, 0.08046, 45, -243.87, 359.18, 0.02756, 47, -376.37, 109.2, 0.01195, 5, 3, -4.85, -19.91, 0.80691, 4, -93.97, -30.86, 0.00176, 8, -59.69, -77.37, 0.1504, 45, -276.11, 353.62, 0.02027, 47, -370.72, 76.98, 0.02067, 5, 3, -38.26, -25.64, 0.09327, 7, 10.58, 26.01, 0.73121, 8, -26.24, -71.88, 0.13287, 45, -308.64, 344.11, 0.01294, 47, -361.11, 44.47, 0.02972, 6, 3, -76.57, -31.93, 0.01087, 7, 48.94, 32.03, 0.73963, 8, 12.11, -65.87, 0.19364, 12, -64.41, -120.32, 0.01131, 45, -345.99, 333.48, 0.00469, 47, -350.37, 7.16, 0.03987, 4, 7, 91.01, 35.87, 0.74433, 8, 54.19, -62.03, 0.1383, 12, -24.71, -105.87, 0.07761, 47, -341.32, -34.1, 0.03977, 4, 7, 137.74, 44.54, 0.69256, 8, 100.91, -53.36, 0.05129, 12, 18.26, -85.57, 0.22362, 47, -326.91, -79.39, 0.03252, 4, 7, 78.15, 114.97, 0.07463, 8, 41.32, 17.07, 0.55439, 12, -57.33, -32.67, 0.34357, 47, -264.44, -11.49, 0.0274, 3, 8, 0.99, 4.47, 0.95576, 12, -93.1, -55.14, 0.01653, 47, -281.97, 26.95, 0.02772, 6, 3, -27.89, -87.2, 0.18065, 4, -109.16, -100.35, 0.00352, 7, 0.65, 87.65, 0.09765, 8, -36.17, -10.24, 0.69258, 45, -291.3, 284.13, 0.00665, 47, -301.19, 62, 0.01895, 6, 3, -0.5, -72.22, 0.40401, 4, -83.66, -82.34, 0.02295, 7, -26.84, 72.87, 0.09151, 8, -63.67, -25.03, 0.45569, 45, -265.81, 302.15, 0.01264, 47, -319.28, 87.44, 0.0132, 6, 3, 28.9, -64.31, 0.56931, 4, -55.36, -71.12, 0.08996, 7, -56.29, 65.17, 0.01998, 8, -93.12, -32.73, 0.29583, 45, -237.51, 313.37, 0.01926, 47, -330.58, 115.7, 0.00566, 5, 3, 52.94, -63.17, 0.55435, 4, -31.61, -67.23, 0.2246, 7, -80.34, 64.2, 0.00087, 8, -117.17, -33.7, 0.19489, 45, -213.75, 317.26, 0.02529, 4, 3, 73.91, -63.94, 0.43875, 4, -10.69, -65.59, 0.40852, 8, -138.14, -32.78, 0.12224, 45, -192.83, 318.89, 0.0305, 4, 3, 79.95, 74.24, 0.45419, 4, -20.5, 72.37, 0.3895, 9, -148.12, 15.4, 0.12441, 45, -202.64, 456.86, 0.0319, 6, 3, 52.41, 72.37, 0.59899, 4, -47.65, 67.36, 0.15399, 7, -80.78, -71.35, 0.00371, 9, -120.56, 17.07, 0.21636, 45, -229.79, 451.85, 0.02432, 47, -469.08, 123.01, 0.00264, 6, 3, 29.89, 74.47, 0.59303, 4, -70.26, 66.87, 0.05169, 7, -58.27, -73.6, 0.02151, 9, -98.05, 14.82, 0.3063, 45, -252.4, 451.35, 0.01895, 47, -468.52, 100.4, 0.00852, 6, 3, -0.03, 81.42, 0.43023, 4, -100.78, 70.35, 0.00706, 7, -28.4, -80.76, 0.05937, 9, -68.19, 7.66, 0.47525, 45, -282.92, 454.83, 0.01246, 47, -471.91, 69.87, 0.01562, 6, 3, -27.31, 91.47, 0.2167, 4, -129.02, 77.22, 0, 7, -1.2, -91.01, 0.05362, 9, -40.98, -2.59, 0.70104, 45, -311.16, 461.7, 0.00691, 47, -478.7, 41.61, 0.02172, 4, 3, -61.61, 104.63, 0.03114, 9, -6.77, -16, 0.90549, 10, -112.4, 25.76, 0.03421, 47, -487.74, 5.99, 0.02917, 4, 7, 71.78, -113.27, 0.02037, 9, 32, -24.85, 0.67002, 10, -74.78, 12.85, 0.28155, 47, -491.7, -33.58, 0.02807, 4, 7, 111.89, -116.02, 0.04411, 9, 72.11, -27.6, 0.28731, 10, -35.19, 5.87, 0.64685, 47, -489.44, -73.72, 0.02173, 4, 7, 201.55, 62.89, 0.42106, 8, 164.73, -35.01, 0.00017, 12, 75.28, -51.55, 0.5544, 47, -300.76, -140.42, 0.02437, 4, 7, 246.26, -9.3, 0.57405, 10, 109.72, 97.78, 0.27574, 12, 136.92, -109.95, 0.13425, 47, -366.82, -193.76, 0.01597, 2, 12, 22.61, 14.02, 0.97818, 47, -228.77, -96.91, 0.02182, 3, 8, 223.63, 31.77, 0, 12, 115.2, 28.04, 0.97663, 47, -227.17, -190.55, 0.02336, 2, 12, 64.91, 26.75, 0.97734, 47, -221.77, -140.54, 0.02266, 4, 7, 252.65, 52.14, 0.3186, 10, 122.58, 158.2, 0.03287, 12, 127.43, -48.91, 0.62434, 47, -305.06, -192.46, 0.02419, 4, 7, 180.73, -8.79, 0.93218, 9, 140.95, 79.63, 0.00019, 10, 44.61, 105.22, 0.03834, 47, -374.47, -128.68, 0.02928, 4, 7, 140.3, -12.18, 0.91479, 9, 100.51, 76.24, 0.00948, 10, 4.05, 106.13, 0.03962, 47, -382.86, -88.98, 0.03611, 4, 7, 240.91, -68.3, 0.23153, 10, 98.16, 39.68, 0.74019, 12, 146.79, -168.36, 0.00594, 47, -426.03, -195.8, 0.02234, 4, 7, 276.91, -71.57, 0.12556, 10, 133.61, 32.61, 0.84028, 12, 182.44, -162.35, 0.01074, 47, -424.8, -231.93, 0.02343, 2, 10, 79.86, -43.06, 0.97711, 47, -510.75, -196.91, 0.02289, 2, 10, 31.56, -32.03, 0.98245, 47, -511.06, -147.37, 0.01755, 3, 7, 198.7, -73.55, 0.28986, 10, 55.63, 38.92, 0.68652, 47, -436.49, -154.57, 0.02362, 2, 10, 129.86, -54.41, 0.97427, 47, -510.37, -248.17, 0.02573, 2, 10, 179.5, -58.54, 0.97257, 47, -503.04, -297.44, 0.02743, 3, 10, 392.62, -41.56, 0.965, 11, -69.52, -56.03, 0.01121, 47, -437.76, -501.04, 0.02379, 3, 10, 419.69, -41.03, 0.91894, 11, -43.06, -50.29, 0.06178, 47, -431.05, -527.27, 0.01928, 3, 10, 451.67, -42.49, 0.69839, 11, -11.41, -45.54, 0.28577, 47, -425.15, -558.73, 0.01585, 3, 10, 479.48, -44.48, 0.32276, 11, 16.26, -42.13, 0.66164, 47, -420.73, -586.25, 0.0156, 3, 10, 508.35, -46.21, 0.07676, 11, 44.93, -38.26, 0.9073, 47, -415.82, -614.76, 0.01594, 3, 10, 537.29, -52, 0.00744, 11, 74.44, -38.35, 0.97827, 47, -414.83, -644.26, 0.01429, 2, 11, 70.63, 5.56, 0.98802, 47, -371.08, -638.85, 0.01198, 2, 11, 41.14, 6.15, 0.98695, 47, -371.58, -609.36, 0.01305, 3, 10, 485.46, 4.61, 0.119, 11, 12.65, 7.19, 0.86749, 47, -371.58, -580.85, 0.01351, 3, 10, 458.29, 11.5, 0.89718, 11, -15.33, 8.71, 0.08885, 47, -371.08, -552.83, 0.01398, 2, 10, 425.92, 15.57, 0.9813, 47, -374.53, -520.39, 0.0187, 2, 10, 393.58, 17.62, 0.97986, 47, -379.93, -488.44, 0.02014, 2, 10, 282.76, -51.98, 0.97446, 47, -473.03, -396.47, 0.02554, 4, 7, 328.93, -68.06, 0.04007, 10, 185.72, 30.61, 0.92867, 12, 231.85, -145.68, 0.00631, 47, -414.83, -283.11, 0.02496, 3, 7, 430.69, -62.49, 0.00019, 10, 287.49, 25.37, 0.9752, 47, -396.64, -383.38, 0.02461, 2, 11, 159.02, -43.09, 0.98977, 47, -416.47, -728.96, 0.01023, 1, 11, 381.52, -6.99, 1, 3, 11, 165.77, 11.11, 0.94717, 12, 671.47, -33.57, 0.04294, 47, -362.06, -733.72, 0.00989, 3, 5, 194.46, -68.94, 0.01781, 6, 113.35, -65.63, 0.96659, 33, -255.63, -15.56, 0.0156, 3, 5, 217.95, -64.08, 0.00277, 6, 136.69, -60.08, 0.97923, 33, -232.29, -10.01, 0.018, 4, 4, 426.89, 67.41, 0.00261, 5, 146.83, 69.32, 0.10698, 6, 61.67, 71.16, 0.87905, 33, -307.31, 121.23, 0.01136, 3, 5, 171.67, 65.78, 0.03527, 6, 86.6, 68.36, 0.95086, 33, -282.38, 118.43, 0.01387, 3, 5, 191.87, 63.88, 0.00998, 6, 106.85, 67.06, 0.97413, 33, -262.13, 117.13, 0.01589, 3, 5, 214.15, 63.26, 0.00115, 6, 129.14, 67.1, 0.98073, 33, -239.83, 117.16, 0.01812, 3, 4, 246.11, -150.42, 0.10777, 13, 138.1, 6.27, 0.88949, 45, 63.97, 234.07, 0.00273], "hull": 113, "edges": [0, 224, 6, 8, 14, 16, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 46, 48, 50, 52, 52, 54, 66, 68, 68, 70, 70, 72, 72, 74, 82, 84, 88, 90, 90, 92, 92, 94, 100, 102, 102, 104, 104, 106, 116, 118, 118, 120, 120, 122, 126, 128, 144, 146, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 198, 200, 204, 206, 212, 214, 210, 212, 206, 208, 208, 210, 200, 202, 202, 204, 194, 196, 196, 198, 180, 182, 182, 184, 178, 180, 176, 178, 152, 154, 146, 148, 148, 150, 140, 142, 142, 144, 136, 138, 138, 140, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 20, 22, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 44, 46, 42, 44, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 164, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 44, 238, 240, 242, 248, 248, 246, 244, 250, 250, 246, 252, 258, 258, 256, 254, 260, 260, 256, 268, 266, 270, 266, 242, 292, 292, 294, 294, 296, 252, 300, 300, 302, 302, 304, 218, 220, 220, 306, 306, 304, 218, 308, 308, 296, 326, 38, 238, 174, 328, 330, 330, 334, 334, 332, 332, 336, 336, 338, 338, 340, 340, 342, 342, 326, 328, 238, 348, 350, 350, 352, 348, 354, 352, 356, 356, 358, 358, 360, 360, 362, 364, 366, 366, 368, 368, 370, 370, 372, 374, 376, 376, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 354, 388, 390, 390, 392, 396, 398, 396, 394, 388, 400, 400, 398, 364, 362, 392, 402, 402, 394, 374, 372, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 418, 404, 78, 80, 80, 82, 74, 76, 76, 78, 476, 478, 134, 136, 132, 134, 94, 96, 96, 98, 98, 100, 122, 124, 124, 126, 128, 130, 130, 132, 564, 566, 566, 568, 568, 570, 570, 572, 574, 576, 576, 578, 578, 580, 580, 582, 582, 584, 560, 586, 586, 562, 562, 564, 588, 590, 590, 584, 84, 86, 86, 88, 572, 592, 592, 594, 594, 596, 596, 574, 110, 112, 112, 114, 114, 116, 106, 108, 108, 110, 220, 222, 222, 224, 214, 216, 216, 218], "width": 455, "height": 1725}}, "body2": {"body": {"type": "mesh", "uvs": [0.62766, 0.01031, 0.64672, 0.02242, 0.66363, 0.03511, 0.68915, 0.0497, 0.70469, 0.06287, 0.70578, 0.07968, 0.7015, 0.09359, 0.69723, 0.10751, 0.72028, 0.11519, 0.76726, 0.11882, 0.80638, 0.11384, 0.79128, 0.12899, 0.81934, 0.14588, 0.7907, 0.15263, 0.77067, 0.15702, 0.7542, 0.16514, 0.73584, 0.17482, 0.78234, 0.18189, 0.79481, 0.19344, 0.76969, 0.21084, 0.72657, 0.21781, 0.74936, 0.23593, 0.74347, 0.25646, 0.71127, 0.27239, 0.68012, 0.28998, 0.65789, 0.30748, 0.65607, 0.32155, 0.67532, 0.33186, 0.70991, 0.34272, 0.75734, 0.35452, 0.80652, 0.36915, 0.86106, 0.38892, 0.9118, 0.41109, 0.95578, 0.43295, 0.98616, 0.45482, 0.99888, 0.48233, 0.99477, 0.50944, 0.97268, 0.53373, 0.9001, 0.53498, 0.79489, 0.53715, 0.69184, 0.53743, 0.60865, 0.53612, 0.52545, 0.53501, 0.52889, 0.55443, 0.5296, 0.58094, 0.53673, 0.62241, 0.54051, 0.66106, 0.54077, 0.68023, 0.55007, 0.70122, 0.55623, 0.7174, 0.55534, 0.73458, 0.5533, 0.7513, 0.55456, 0.76768, 0.58659, 0.84001, 0.59329, 0.89483, 0.59611, 0.9473, 0.59892, 0.99977, 0.44822, 1, 0.42126, 0.95013, 0.36559, 0.87318, 0.33851, 0.82406, 0.34253, 0.77511, 0.33823, 0.7585, 0.32896, 0.74082, 0.31647, 0.72441, 0.29786, 0.70966, 0.27434, 0.69414, 0.21242, 0.65327, 0.14067, 0.60027, 0.09917, 0.56469, 0.08495, 0.53495, 0.07938, 0.50864, 0.08325, 0.48311, 0.0964, 0.45692, 0.11707, 0.43211, 0.1424, 0.40883, 0.16896, 0.38538, 0.19922, 0.36966, 0.22068, 0.35623, 0.23222, 0.34025, 0.23226, 0.32825, 0.21545, 0.31489, 0.17724, 0.29957, 0.14246, 0.28837, 0.08676, 0.27907, 0.0528, 0.26463, 0.04247, 0.24837, 0.05108, 0.23224, 0.07912, 0.21878, 0.00688, 0.21203, 0.01772, 0.19756, 1e-05, 0.18602, 0.01035, 0.16969, 0.0207, 0.15489, 0.0554, 0.14641, 0.09913, 0.14297, 0.13842, 0.13636, 0.09987, 0.12304, 0.11013, 0.11225, 0.14305, 0.10345, 0.16249, 0.09368, 0.17185, 0.07992, 0.19947, 0.06963, 0.2249, 0.06116, 0.24145, 0.05136, 0.25301, 0.03763, 0.26275, 0.02563, 0.27273, 0.01368, 0.2745, 0.00365, 0.30894, 0.00329, 0.34337, 0.00294, 0.53506, 0.00097, 0.57149, 0.00059, 0.60791, 0.00022, 0.23247, 0.29411, 0.32689, 0.29139, 0.40729, 0.28498, 0.4905, 0.2909, 0.57969, 0.29312, 0.64976, 0.28696, 0.1453, 0.19582, 0.13807, 0.18145, 0.33517, 0.07072, 0.36332, 0.12644, 0.35831, 0.09862, 0.34856, 0.08524, 0.36551, 0.11379, 0.53588, 0.07134, 0.51995, 0.1263, 0.52145, 0.0989, 0.52867, 0.08512, 0.5167, 0.11268, 0.55492, 0.13585, 0.62013, 0.13894, 0.75734, 0.14721, 0.68566, 0.14259, 0.31493, 0.13752, 0.15951, 0.1456, 0.24273, 0.14084, 0.10049, 0.15254, 0.11628, 0.16981, 0.18761, 0.12212, 0.27661, 0.12133, 0.25461, 0.10551, 0.26961, 0.09337, 0.27105, 0.07904, 0.3336, 0.05484, 0.3376, 0.03928, 0.34176, 0.02729, 0.28507, 0.06591, 0.54662, 0.05684, 0.55094, 0.04143, 0.54806, 0.02754, 0.54228, 0.0133, 0.34329, 0.0134, 0.62343, 0.12407, 0.61365, 0.10906, 0.61631, 0.09546, 0.62254, 0.07951, 0.62343, 0.06521, 0.61543, 0.05185, 0.60654, 0.03825, 0.70354, 0.12933, 0.68532, 0.20059, 0.21566, 0.1865, 0.28937, 0.18643, 0.38137, 0.21223, 0.35614, 0.19877, 0.40416, 0.19884, 0.46268, 0.18575, 0.54685, 0.18106, 0.62358, 0.18598, 0.62324, 0.25171, 0.16441, 0.25601, 0.45688, 0.22075, 0.51174, 0.20837, 0.59456, 0.20474, 0.38621, 0.23576, 0.67662, 0.21472, 0.71981, 0.23733, 0.71444, 0.25851, 0.66016, 0.27666, 0.56837, 0.28295, 0.48384, 0.27843, 0.40065, 0.26919, 0.32664, 0.27922, 0.23866, 0.28486, 0.15257, 0.28153, 0.09169, 0.26948, 0.07447, 0.24751, 0.09063, 0.22811, 0.14604, 0.21416, 0.22741, 0.21062, 0.31029, 0.2238, 0.54744, 0.23701, 0.62018, 0.22988, 0.6752, 0.23553, 0.6696, 0.26554, 0.60992, 0.2712, 0.5605, 0.26899, 0.53532, 0.25558, 0.68825, 0.25128, 0.18557, 0.23375, 0.12869, 0.23827, 0.10724, 0.25131, 0.12589, 0.26828, 0.16566, 0.27414, 0.23467, 0.27119, 0.25984, 0.25742, 0.24772, 0.24045, 0.39319, 0.25464, 0.19504, 0.16418, 0.29581, 0.16196, 0.40339, 0.16166, 0.46866, 0.16072, 0.56051, 0.15727, 0.65495, 0.16104, 0.3994, 0.1463, 0.40169, 0.1274, 0.40245, 0.11373, 0.40016, 0.09926, 0.39711, 0.08458, 0.39483, 0.0689, 0.47867, 0.06894, 0.47638, 0.08462, 0.47409, 0.0999, 0.47104, 0.11377, 0.47181, 0.12784, 0.47257, 0.14659, 0.38101, 0.18453, 0.3497, 0.3088, 0.36585, 0.3242, 0.37703, 0.34058, 0.37827, 0.35959, 0.36958, 0.37926, 0.3584, 0.40023, 0.35467, 0.42475, 0.3584, 0.45294, 0.35715, 0.47096, 0.51619, 0.49095, 0.63038, 0.4615, 0.79301, 0.43332, 0.22268, 0.46278, 0.48404, 0.30391, 0.48901, 0.32062, 0.49771, 0.3357, 0.51013, 0.35438, 0.53125, 0.37322, 0.55486, 0.39485, 0.57474, 0.41877, 0.60642, 0.44502, 0.74371, 0.40566, 0.7052, 0.38338, 0.66295, 0.36306, 0.62319, 0.34831, 0.59834, 0.33193, 0.58965, 0.31816, 0.58592, 0.30604, 0.28276, 0.31196, 0.29395, 0.32769, 0.29518, 0.3408, 0.28773, 0.3585, 0.27283, 0.37488, 0.25295, 0.39553, 0.24425, 0.41847, 0.24922, 0.44174, 0.6639, 0.4804, 0.5187, 0.51133, 0.8221, 0.45518, 0.82563, 0.50947, 0.8375, 0.48047, 0.65444, 0.51057, 0.5019, 0.4736, 0.48344, 0.45058, 0.38858, 0.51251, 0.39128, 0.53345, 0.20237, 0.51315, 0.20169, 0.48443, 0.36557, 0.48861, 0.20322, 0.54287, 0.21933, 0.57143, 0.3628, 0.68946, 0.37753, 0.70466, 0.3905, 0.7229, 0.40022, 0.73886, 0.41102, 0.75539, 0.41318, 0.77248, 0.50933, 0.76935, 0.50825, 0.75225, 0.50825, 0.73572, 0.50933, 0.71948, 0.50177, 0.70067, 0.48989, 0.68215, 0.28527, 0.62884, 0.41318, 0.56312, 0.45315, 0.62125, 0.40958, 0.82159, 0.50674, 0.94972, 0.52916, 0.82435, 0.59564, 0.02579, 0.58548, 0.01214, 0.29075, 0.0526, 0.29906, 0.03822, 0.30368, 0.02652, 0.30553, 0.0136], "triangles": [32, 241, 251, 32, 251, 31, 241, 32, 33, 250, 251, 241, 268, 241, 33, 268, 33, 34, 240, 250, 241, 240, 241, 268, 266, 240, 268, 270, 268, 34, 266, 268, 270, 270, 34, 35, 36, 270, 35, 269, 266, 270, 269, 270, 36, 271, 266, 269, 36, 38, 269, 37, 38, 36, 271, 41, 267, 269, 40, 271, 39, 269, 38, 40, 41, 271, 269, 39, 40, 297, 54, 55, 289, 290, 50, 284, 283, 289, 63, 283, 284, 51, 289, 50, 288, 289, 51, 284, 289, 288, 285, 284, 288, 62, 63, 284, 62, 284, 285, 288, 51, 52, 287, 288, 52, 285, 288, 287, 286, 285, 287, 62, 285, 286, 61, 62, 286, 296, 61, 286, 60, 61, 296, 298, 287, 52, 296, 286, 287, 298, 296, 287, 298, 52, 53, 59, 60, 296, 298, 53, 297, 296, 297, 59, 297, 296, 298, 54, 297, 53, 58, 59, 297, 297, 55, 56, 57, 58, 297, 57, 297, 56, 74, 264, 265, 265, 236, 237, 242, 73, 74, 265, 242, 74, 242, 265, 237, 238, 242, 237, 277, 72, 73, 242, 277, 73, 71, 72, 277, 274, 278, 239, 277, 278, 276, 238, 277, 242, 278, 277, 238, 276, 278, 274, 71, 277, 276, 275, 274, 267, 70, 71, 276, 275, 267, 42, 275, 279, 276, 275, 276, 274, 70, 276, 279, 294, 275, 42, 294, 42, 43, 69, 70, 279, 294, 280, 279, 294, 279, 275, 69, 279, 280, 294, 43, 44, 68, 69, 280, 295, 294, 44, 295, 44, 45, 293, 280, 294, 293, 294, 295, 68, 280, 293, 67, 68, 293, 46, 292, 295, 46, 295, 45, 292, 46, 47, 281, 293, 295, 281, 67, 293, 295, 292, 281, 66, 67, 281, 291, 292, 47, 282, 281, 292, 291, 47, 48, 291, 282, 292, 65, 66, 281, 65, 281, 282, 290, 291, 48, 290, 48, 49, 283, 282, 291, 290, 283, 291, 64, 65, 282, 283, 64, 282, 50, 290, 49, 289, 283, 290, 63, 64, 283, 78, 79, 261, 262, 78, 261, 77, 78, 262, 234, 261, 233, 262, 261, 234, 263, 77, 262, 76, 77, 263, 235, 262, 234, 263, 262, 235, 75, 76, 263, 264, 75, 263, 264, 263, 235, 236, 264, 235, 74, 75, 264, 265, 264, 236, 254, 27, 28, 253, 254, 28, 253, 28, 29, 247, 254, 253, 252, 253, 29, 252, 29, 30, 248, 253, 252, 251, 252, 30, 251, 30, 31, 249, 252, 251, 248, 247, 253, 249, 248, 252, 234, 233, 247, 250, 249, 251, 234, 248, 235, 248, 234, 247, 249, 235, 248, 273, 249, 250, 235, 249, 236, 273, 236, 249, 237, 236, 273, 272, 273, 250, 272, 250, 240, 238, 273, 272, 273, 238, 237, 239, 278, 238, 239, 272, 240, 239, 240, 266, 239, 238, 272, 271, 239, 266, 267, 239, 271, 274, 239, 267, 41, 42, 267, 300, 112, 113, 300, 113, 0, 153, 111, 112, 153, 112, 300, 154, 109, 110, 304, 108, 109, 304, 109, 154, 107, 108, 304, 299, 300, 0, 299, 0, 1, 303, 107, 304, 106, 107, 303, 148, 304, 154, 303, 304, 148, 152, 153, 300, 152, 300, 299, 302, 106, 303, 105, 106, 302, 161, 299, 1, 161, 1, 2, 147, 303, 148, 302, 303, 147, 161, 151, 152, 161, 152, 299, 160, 161, 2, 160, 2, 3, 151, 161, 160, 301, 105, 302, 104, 105, 301, 301, 302, 147, 146, 301, 147, 154, 111, 153, 151, 222, 152, 150, 151, 160, 159, 160, 3, 159, 3, 4, 150, 160, 159, 149, 104, 301, 149, 301, 146, 103, 104, 149, 152, 154, 153, 111, 154, 110, 222, 151, 223, 152, 148, 154, 152, 147, 148, 222, 147, 152, 146, 147, 222, 150, 223, 151, 122, 146, 222, 149, 146, 122, 127, 223, 150, 127, 150, 159, 145, 103, 149, 145, 149, 122, 102, 103, 145, 158, 127, 159, 159, 4, 5, 158, 159, 5, 221, 222, 223, 125, 122, 222, 224, 221, 223, 224, 223, 127, 130, 224, 127, 130, 127, 158, 221, 125, 222, 145, 122, 125, 145, 101, 102, 144, 145, 125, 144, 101, 145, 6, 158, 5, 157, 130, 158, 157, 158, 6, 124, 125, 221, 129, 224, 130, 220, 221, 224, 100, 101, 144, 144, 125, 124, 129, 130, 157, 124, 221, 220, 225, 220, 224, 225, 224, 129, 143, 100, 144, 7, 157, 6, 156, 129, 157, 156, 157, 7, 131, 225, 129, 131, 129, 156, 219, 220, 225, 226, 219, 225, 226, 225, 131, 126, 124, 220, 126, 220, 219, 124, 143, 144, 126, 143, 124, 126, 142, 143, 143, 99, 100, 141, 143, 142, 141, 99, 143, 155, 156, 7, 128, 131, 156, 128, 156, 155, 226, 131, 128, 123, 142, 126, 219, 123, 126, 218, 123, 219, 227, 226, 128, 226, 218, 219, 226, 227, 218, 98, 99, 141, 97, 98, 141, 96, 97, 141, 138, 141, 142, 137, 96, 141, 138, 137, 141, 95, 96, 137, 139, 95, 137, 94, 95, 139, 211, 137, 138, 211, 138, 212, 140, 139, 137, 140, 137, 211, 121, 140, 211, 93, 94, 139, 212, 164, 211, 164, 121, 211, 120, 121, 164, 92, 140, 91, 121, 90, 91, 93, 139, 140, 140, 92, 93, 140, 121, 91, 120, 90, 121, 162, 155, 7, 11, 9, 10, 8, 162, 7, 133, 155, 162, 135, 133, 162, 9, 162, 8, 11, 162, 9, 134, 11, 12, 11, 134, 162, 135, 162, 134, 13, 134, 12, 14, 134, 13, 216, 133, 135, 215, 133, 216, 15, 135, 134, 15, 134, 14, 216, 135, 15, 16, 216, 15, 171, 216, 16, 163, 171, 16, 163, 16, 17, 163, 17, 18, 132, 128, 155, 136, 142, 123, 133, 132, 155, 138, 142, 136, 217, 123, 218, 136, 123, 217, 228, 227, 128, 228, 128, 132, 215, 132, 133, 228, 132, 215, 228, 218, 227, 214, 228, 215, 228, 217, 218, 214, 217, 228, 213, 217, 214, 212, 138, 136, 212, 136, 217, 212, 217, 213, 170, 214, 215, 171, 170, 215, 229, 212, 213, 169, 213, 214, 169, 214, 170, 229, 213, 169, 216, 171, 215, 165, 212, 229, 212, 165, 164, 167, 165, 229, 168, 229, 169, 167, 229, 168, 176, 170, 171, 176, 171, 163, 175, 169, 170, 175, 170, 176, 168, 169, 175, 192, 164, 165, 192, 165, 167, 120, 164, 192, 19, 163, 18, 166, 167, 168, 191, 120, 192, 178, 176, 163, 20, 178, 163, 19, 20, 163, 90, 120, 88, 88, 120, 191, 89, 90, 88, 174, 168, 175, 166, 168, 174, 193, 192, 167, 193, 167, 166, 190, 88, 191, 195, 176, 178, 87, 88, 190, 202, 191, 192, 209, 202, 192, 203, 190, 191, 196, 195, 178, 179, 196, 178, 177, 166, 174, 193, 166, 177, 20, 179, 178, 194, 175, 176, 194, 176, 195, 174, 175, 194, 21, 179, 20, 202, 203, 191, 193, 209, 192, 189, 87, 190, 189, 190, 203, 86, 87, 189, 201, 196, 179, 204, 189, 203, 172, 195, 196, 172, 196, 201, 194, 195, 172, 177, 200, 210, 194, 177, 174, 194, 200, 177, 200, 194, 172, 173, 203, 202, 173, 202, 209, 204, 203, 173, 22, 179, 21, 180, 201, 179, 177, 208, 209, 177, 209, 193, 208, 177, 210, 173, 209, 208, 22, 180, 179, 85, 86, 189, 188, 85, 189, 197, 172, 201, 197, 201, 180, 205, 204, 173, 204, 188, 189, 199, 200, 172, 198, 199, 172, 184, 210, 200, 205, 188, 204, 207, 173, 208, 197, 198, 172, 180, 181, 197, 23, 180, 22, 206, 173, 207, 205, 173, 206, 181, 198, 197, 23, 181, 180, 183, 184, 200, 183, 200, 199, 84, 85, 188, 185, 208, 210, 185, 210, 184, 207, 208, 185, 187, 205, 206, 188, 205, 187, 182, 199, 198, 183, 199, 182, 182, 198, 181, 186, 207, 185, 206, 207, 186, 187, 206, 186, 116, 184, 183, 185, 184, 116, 119, 182, 181, 119, 181, 23, 187, 84, 188, 83, 84, 187, 24, 119, 23, 117, 183, 182, 116, 183, 117, 115, 185, 116, 186, 185, 115, 118, 182, 119, 117, 182, 118, 114, 187, 186, 114, 186, 115, 83, 187, 114, 82, 83, 114, 243, 116, 117, 25, 257, 118, 25, 119, 24, 25, 118, 119, 230, 115, 116, 230, 116, 243, 258, 114, 115, 258, 115, 230, 81, 82, 114, 81, 114, 258, 118, 243, 117, 257, 243, 118, 256, 257, 25, 243, 257, 244, 244, 257, 256, 231, 230, 243, 26, 256, 25, 244, 231, 243, 259, 258, 230, 259, 230, 231, 80, 81, 258, 80, 258, 259, 255, 256, 26, 255, 26, 27, 245, 244, 256, 245, 256, 255, 232, 231, 244, 232, 244, 245, 260, 259, 231, 232, 260, 231, 79, 80, 259, 260, 79, 259, 254, 255, 27, 246, 245, 255, 246, 255, 254, 261, 79, 260, 246, 233, 232, 246, 232, 245, 247, 246, 254, 260, 233, 261, 233, 260, 232, 233, 246, 247], "vertices": [2, 5, 221.31, -83.24, 0.00516, 6, 140.61, -79.13, 0.99484, 2, 5, 200.5, -92.12, 0.02327, 6, 120.07, -88.62, 0.97673, 2, 5, 178.68, -100.03, 0.06529, 6, 98.49, -97.17, 0.93471, 3, 4, 431.35, -113.88, 0.00247, 5, 153.64, -111.9, 0.14484, 6, 73.81, -109.77, 0.8527, 3, 4, 408.61, -120.88, 0.01218, 5, 130.99, -119.19, 0.22862, 6, 51.39, -117.73, 0.7592, 4, 4, 379.61, -121.29, 0.04368, 13, 112.89, 140.57, 0.0261, 5, 102, -119.98, 0.34227, 6, 22.43, -119.37, 0.58795, 4, 4, 355.62, -119.28, 0.08849, 13, 110.18, 116.64, 0.17431, 5, 77.98, -118.27, 0.38016, 6, -1.63, -118.37, 0.35704, 4, 4, 331.62, -117.26, 0.20086, 13, 107.46, 92.72, 0.32873, 5, 53.96, -116.57, 0.29211, 6, -25.69, -117.38, 0.17829, 4, 4, 318.34, -127.71, 0.28396, 13, 117.52, 79.13, 0.45762, 5, 40.81, -127.19, 0.18888, 6, -38.52, -128.38, 0.06954, 4, 4, 312.02, -149.07, 0.32135, 13, 138.68, 72.19, 0.5222, 5, 34.77, -148.63, 0.13771, 6, -43.92, -149.99, 0.01873, 4, 4, 320.55, -166.89, 0.34837, 13, 156.74, 80.2, 0.44262, 5, 43.54, -166.34, 0.19242, 6, -34.64, -167.43, 0.01658, 4, 4, 294.44, -159.94, 0.3009, 13, 149.03, 54.31, 0.62254, 5, 17.34, -159.73, 0.07479, 6, -61.02, -161.6, 0.00177, 3, 4, 265.27, -172.62, 0.24633, 13, 160.86, 24.77, 0.73915, 5, -11.67, -172.79, 0.01453, 2, 4, 253.67, -159.56, 0.14459, 13, 147.45, 13.56, 0.85541, 3, 4, 246.11, -150.42, 0.10777, 13, 138.1, 6.27, 0.88949, 45, 63.97, 234.07, 0.00273, 3, 4, 232.12, -142.88, 0.05396, 13, 130.16, -7.49, 0.93945, 45, 49.98, 241.6, 0.00659, 3, 4, 215.46, -134.48, 0.16946, 13, 121.27, -23.9, 0.81766, 45, 33.31, 250, 0.01287, 4, 4, 203.2, -155.6, 0.25655, 13, 142.02, -36.77, 0.71745, 52, 94.57, -97.38, 0.01988, 45, 21.06, 228.88, 0.00613, 4, 4, 183.26, -161.22, 0.36667, 13, 147.05, -56.87, 0.54726, 52, 74.62, -103, 0.07947, 45, 1.12, 223.27, 0.0066, 5, 4, 153.27, -149.7, 0.58561, 13, 134.66, -86.5, 0.2723, 8, -290.67, 70.62, 0, 52, 44.64, -91.48, 0.12819, 45, -28.87, 234.79, 0.01389, 5, 4, 141.31, -130.04, 0.71392, 13, 114.67, -97.89, 0.06636, 8, -281.18, 49.66, 0.00028, 52, 32.68, -71.83, 0.19514, 45, -40.83, 254.44, 0.0243, 4, 4, 110.02, -140.32, 0.78121, 8, -248.87, 56.06, 0.00244, 52, 1.38, -82.1, 0.19591, 45, -72.12, 244.17, 0.02044, 4, 4, 74.62, -137.53, 0.78367, 8, -214.07, 48.99, 0.00795, 52, -34.02, -79.32, 0.1979, 45, -107.52, 246.95, 0.01048, 4, 3, 124.85, -127.39, 0.03063, 4, 47.17, -122.8, 0.74909, 8, -188.62, 31.03, 0.02028, 52, -61.46, -64.58, 0.2, 4, 3, 96.39, -109.76, 0.18499, 4, 16.89, -108.54, 0.65652, 8, -160.29, 13.19, 0.05849, 52, -91.75, -50.32, 0.1, 3, 3, 67.6, -96.17, 0.42313, 4, -13.27, -98.33, 0.42155, 8, -131.6, -0.6, 0.15531, 4, 3, 43.59, -92.5, 0.51456, 4, -37.55, -97.43, 0.18019, 7, -70.78, 93.46, 0.00555, 8, -107.61, -4.44, 0.29971, 4, 3, 24.89, -99.11, 0.44968, 4, -55.36, -106.14, 0.07057, 7, -52.04, 99.93, 0.01807, 8, -88.87, 2.04, 0.46168, 4, 3, 4.44, -112.54, 0.29214, 4, -74.14, -121.82, 0.02029, 7, -31.5, 113.22, 0.0193, 8, -68.32, 15.32, 0.66827, 4, 3, -18.3, -131.58, 0.12848, 4, -94.55, -143.34, 0.00307, 8, -45.45, 34.2, 0.85816, 12, -145.59, -38.23, 0.01029, 3, 3, -45.98, -150.84, 0.02827, 8, -17.63, 53.27, 0.86087, 12, -123.55, -12.71, 0.11085, 3, 3, -82.76, -171.48, 0.00069, 8, 19.3, 73.64, 0.6073, 12, -93.04, 16.41, 0.392, 2, 8, 60.12, 91.79, 0.25487, 12, -58.2, 44.38, 0.74513, 2, 8, 100.02, 106.95, 0.05049, 12, -23.49, 69.21, 0.94951, 2, 8, 139.18, 115.98, 0.00131, 12, 12.07, 87.92, 0.99869, 2, 8, 187, 115.81, 4e-05, 12, 58.35, 99.96, 0.99996, 2, 8, 233.16, 108.14, 2e-05, 12, 104.95, 104.31, 0.99998, 2, 8, 273.49, 92.95, 1e-05, 12, 147.81, 99.91, 0.99999, 3, 8, 271.51, 59.91, 1e-05, 12, 154.32, 67.46, 0.98522, 47, -193.29, -234.56, 0.01477, 3, 8, 269.26, 11.95, 0, 12, 164.38, 20.52, 0.97665, 47, -241.15, -238.3, 0.02335, 4, 7, 300.73, 63.26, 0.12345, 10, 171.57, 164.17, 0.02946, 12, 171.09, -25.9, 0.82221, 47, -288.04, -238.78, 0.02489, 4, 7, 293.79, 25.98, 0.33548, 10, 160.72, 127.84, 0.14939, 12, 173.88, -63.71, 0.49582, 47, -325.9, -236.54, 0.01932, 3, 7, 287.17, -11.34, 0.34725, 10, 150.19, 91.43, 0.49694, 12, 177, -101.49, 0.15581, 3, 7, 320.61, -13.95, 0.15279, 10, 183.16, 85.29, 0.8027, 12, 210, -95.49, 0.04451, 3, 7, 366.02, -19.32, 0.03517, 10, 227.75, 75.14, 0.95931, 12, 255.28, -89.1, 0.00552, 2, 7, 437.41, -25.01, 0.00041, 10, 298.14, 61.93, 0.99959, 1, 10, 363.43, 48.36, 1, 1, 10, 395.65, 40.91, 1, 1, 10, 431.87, 36.75, 1, 2, 10, 459.69, 33.09, 0.8444, 11, -18.13, 30.16, 0.1556, 2, 10, 488.44, 25.92, 0.24662, 11, 11.47, 28.67, 0.75338, 1, 11, 40.26, 26.69, 1, 1, 11, 68.52, 26.23, 1, 2, 11, 193.72, 36.23, 0.50274, 12, 694.78, -4.08, 0.49726, 1, 12, 788.11, 11.49, 1, 1, 12, 877.65, 24.78, 1, 1, 11, 469.33, 31.77, 1, 1, 12, 976.68, -29.85, 1, 2, 11, 380.8, -45.88, 0.9969, 12, 893.04, -53.43, 0.0031, 1, 11, 247.23, -66.34, 1, 1, 11, 162.11, -75.56, 1, 2, 10, 534.34, -84.33, 0.01856, 11, 77.79, -70.64, 0.98144, 2, 10, 506.01, -79.68, 0.12136, 11, 49.09, -71.55, 0.87864, 2, 10, 475.35, -76.81, 0.38753, 11, 18.46, -74.65, 0.61247, 2, 10, 446.49, -75.87, 0.69548, 11, -10.04, -79.3, 0.30452, 2, 10, 419.8, -78.29, 0.89362, 11, -35.77, -86.83, 0.10638, 2, 10, 391.28, -82.59, 0.97988, 11, -62.92, -96.54, 0.02012, 1, 10, 316.2, -93.89, 1, 1, 10, 219.74, -104.76, 1, 1, 10, 155.66, -109.1, 1, 1, 10, 104.25, -103.67, 1, 1, 10, 59.49, -95.75, 1, 2, 9, 133.52, -111.41, 0.01178, 10, 17.01, -83.96, 0.98822, 2, 9, 89.43, -99.85, 0.12593, 10, -25.6, -67.8, 0.87407, 2, 9, 48.14, -85.2, 0.40744, 10, -65.11, -48.86, 0.59256, 3, 3, -78.49, 157.28, 0.00388, 9, 9.73, -68.76, 0.72507, 10, -101.57, -28.46, 0.27105, 3, 3, -39.75, 140.53, 0.07799, 9, -28.89, -51.74, 0.86581, 10, -138.17, -7.44, 0.0562, 5, 3, -14.44, 123.67, 0.22471, 4, -119.92, 110.68, 0.00129, 7, -14.3, -123.12, 0.0059, 9, -54.08, -34.7, 0.76281, 10, -161.42, 12.16, 0.00529, 4, 3, 7.43, 111.26, 0.40093, 4, -96.78, 100.84, 0.01415, 7, -36.07, -110.55, 0.0172, 9, -75.86, -22.13, 0.56771, 4, 3, 34.19, 102.8, 0.54753, 4, -69.23, 95.51, 0.07384, 7, -62.78, -101.9, 0.01115, 9, -102.56, -13.48, 0.36748, 4, 3, 54.74, 100.36, 0.55275, 4, -48.53, 95.44, 0.2027, 7, -83.31, -99.32, 0.00211, 9, -123.1, -10.9, 0.24244, 3, 3, 78.52, 105.25, 0.42084, 4, -25.47, 103.02, 0.4473, 9, -146.91, -15.62, 0.13186, 4, 3, 106.81, 119.41, 0.17575, 4, 1.02, 120.32, 0.67137, 9, -175.3, -29.58, 0.05288, 49, -99.78, 25.16, 0.1, 5, 3, 127.85, 132.86, 0.04742, 4, 20.38, 136.09, 0.73041, 5, -260.54, 132.72, 0.00017, 9, -196.43, -42.88, 0.02201, 49, -80.43, 40.93, 0.2, 5, 4, 36.51, 161.38, 0.7814, 5, -244.74, 158.22, 0.00149, 9, -215.52, -66.03, 0.00891, 49, -64.29, 66.23, 0.19795, 45, -145.63, 545.87, 0.01025, 5, 4, 61.46, 176.76, 0.77846, 5, -219.99, 173.92, 0.00426, 9, -242.15, -78.26, 0.00371, 49, -39.34, 81.61, 0.19661, 45, -120.68, 561.25, 0.01696, 4, 4, 89.52, 181.38, 0.78432, 9, -270.56, -79.43, 0.00136, 49, -11.28, 86.22, 0.19642, 45, -92.62, 565.86, 0.0179, 4, 4, 117.34, 177.38, 0.78327, 9, -297.69, -72.08, 0.0003, 49, 16.54, 82.22, 0.19589, 45, -64.8, 561.86, 0.02054, 4, 4, 140.52, 164.55, 0.70936, 16, 150.45, 90.5, 0.07352, 49, 39.71, 69.39, 0.19572, 45, -41.63, 549.03, 0.02141, 4, 4, 152.26, 197.38, 0.60832, 16, 182.9, 77.75, 0.28427, 49, 51.45, 102.23, 0.09918, 45, -29.88, 581.87, 0.00824, 4, 4, 177.21, 192.38, 0.47314, 16, 177.13, 52.96, 0.47116, 49, 76.41, 97.22, 0.0497, 45, -4.93, 576.87, 0.00599, 3, 4, 197.13, 200.38, 0.45926, 16, 184.5, 32.8, 0.53731, 45, 14.99, 584.86, 0.00343, 3, 4, 225.29, 195.59, 0.36933, 16, 178.84, 4.8, 0.62808, 45, 43.15, 580.07, 0.00259, 2, 4, 250.8, 190.8, 0.35991, 16, 173.26, -20.55, 0.64009, 3, 4, 265.39, 174.97, 0.31932, 16, 156.99, -34.63, 0.67714, 5, -16.06, 174.78, 0.00354, 3, 4, 271.25, 155.06, 0.26895, 16, 136.9, -39.88, 0.71741, 5, -9.93, 154.94, 0.01364, 4, 4, 282.61, 137.15, 0.26467, 16, 118.64, -50.67, 0.65895, 5, 1.65, 137.18, 0.06673, 6, -85.45, 134.72, 0.00965, 4, 4, 305.64, 154.62, 0.23585, 16, 135.39, -74.23, 0.4966, 5, 24.45, 154.95, 0.21452, 6, -63.18, 153.15, 0.05302, 4, 4, 324.23, 149.89, 0.2238, 16, 130.09, -92.67, 0.37844, 5, 43.11, 150.46, 0.31389, 6, -44.4, 149.22, 0.08387, 4, 4, 339.38, 134.87, 0.18636, 16, 114.6, -107.34, 0.28018, 5, 58.44, 135.64, 0.3879, 6, -28.63, 134.86, 0.14556, 4, 4, 356.2, 125.97, 0.13687, 16, 105.19, -123.87, 0.16411, 5, 75.38, 126.96, 0.44, 6, -11.45, 126.68, 0.25902, 4, 4, 379.91, 121.65, 0.08819, 16, 100.12, -147.44, 0.00379, 5, 99.15, 122.94, 0.454, 6, 12.43, 123.36, 0.45403, 3, 4, 397.63, 109.03, 0.04708, 5, 117.03, 110.56, 0.36816, 6, 30.67, 111.51, 0.58476, 3, 4, 412.21, 97.41, 0.0208, 5, 131.76, 99.13, 0.25806, 6, 45.72, 100.52, 0.72114, 3, 4, 429.09, 89.83, 0.00578, 5, 148.74, 91.77, 0.1423, 6, 62.91, 93.66, 0.85192, 3, 4, 452.76, 84.5, 0.00012, 5, 172.47, 86.75, 0.04906, 6, 86.78, 89.34, 0.95083, 2, 5, 193.22, 82.52, 0.01471, 6, 107.65, 85.73, 0.98529, 2, 5, 213.87, 78.19, 0.00223, 6, 128.42, 82, 0.99777, 2, 5, 231.19, 77.55, 5e-05, 6, 145.75, 81.88, 0.99995, 2, 6, 146.98, 66.25, 0.98012, 33, -222, 116.32, 0.01988, 1, 6, 148.21, 50.62, 1, 1, 6, 155.04, -36.4, 1, 2, 6, 156.34, -52.93, 0.98007, 33, -212.63, -2.87, 0.01993, 2, 5, 238.62, -74.08, 0.00059, 6, 157.64, -69.47, 0.99941, 5, 3, 113.22, 93.35, 0.14651, 4, 10.37, 95.16, 0.58382, 9, -181.52, -3.47, 0.04687, 49, -90.43, 0.01, 0.1943, 45, -171.77, 479.65, 0.02851, 5, 3, 112.83, 50.14, 0.12862, 4, 14.92, 52.19, 0.60993, 9, -180.82, 39.74, 0.02944, 49, -85.88, -42.97, 0.192, 45, -167.22, 436.67, 0.04, 5, 4, 25.87, 15.57, 0.77438, 9, -187.24, 77.41, 0.00322, 52, -82.76, 73.79, 0.0864, 49, -74.93, -79.58, 0.096, 45, -156.27, 400.06, 0.04, 5, 3, 104.94, -23.89, 0.07231, 4, 15.55, -22.25, 0.68443, 8, -169.44, -72.61, 0.01127, 52, -93.09, 35.96, 0.192, 45, -166.59, 362.23, 0.04, 5, 3, 96.36, -63.74, 0.19057, 4, 11.59, -62.83, 0.52726, 8, -160.59, -32.82, 0.05425, 52, -97.04, -4.61, 0.19302, 45, -170.55, 321.66, 0.0349, 5, 3, 103.17, -96.65, 0.14348, 4, 22.13, -94.74, 0.58974, 8, -167.16, 0.14, 0.04846, 52, -86.51, -36.52, 0.19542, 45, -160.02, 289.75, 0.0229, 4, 4, 180.03, 134.32, 0.54221, 16, 119.01, 51.95, 0.27899, 49, 79.23, 39.16, 0.14492, 45, -2.11, 518.81, 0.03388, 4, 4, 204.83, 137.54, 0.20529, 16, 121.45, 27.06, 0.76522, 49, 104.03, 42.38, 0.0098, 45, 22.69, 522.02, 0.01969, 3, 4, 395.58, 47.29, 0.00535, 5, 115.78, 48.8, 0.19748, 6, 31.23, 49.74, 0.79717, 3, 4, 299.42, 34.77, 0.47246, 16, 15.79, -64.29, 0.04306, 5, 19.79, 35.03, 0.48448, 3, 4, 347.41, 36.9, 0.03308, 5, 67.75, 37.78, 0.66299, 6, -16.45, 37.32, 0.30393, 3, 4, 370.51, 41.27, 0.0127, 5, 90.79, 42.45, 0.42098, 6, 6.44, 42.66, 0.56632, 3, 4, 321.23, 33.7, 0.20273, 5, 41.61, 34.25, 0.70635, 6, -42.48, 33.01, 0.09091, 3, 4, 394.23, -44.03, 0.00172, 5, 115.62, -42.53, 0.17012, 6, 33.76, -41.56, 0.82816, 3, 4, 299.45, -36.5, 0.49535, 13, 25.79, 62.92, 0.04176, 5, 20.74, -36.24, 0.46289, 3, 4, 346.71, -37.33, 0.0234, 5, 68.01, -36.45, 0.63815, 6, -14, -36.88, 0.33845, 3, 4, 370.47, -40.68, 0.00653, 5, 91.81, -39.49, 0.39583, 6, 9.88, -39.22, 0.59765, 3, 4, 322.94, -35.09, 0.14222, 5, 44.22, -34.52, 0.69383, 6, -37.84, -35.65, 0.16395, 3, 4, 282.92, -52.36, 0.64471, 13, 41.16, 45.93, 0.23231, 5, 4.42, -52.31, 0.12298, 4, 4, 277.51, -82.02, 0.4869, 13, 70.65, 39.66, 0.46986, 5, -0.6, -82.03, 0.03397, 45, 95.37, 302.47, 0.00927, 2, 4, 263.05, -144.4, 0.14985, 13, 132.58, 23.38, 0.85015, 3, 4, 271.13, -111.81, 0.28164, 13, 100.25, 32.4, 0.71117, 5, -6.6, -111.91, 0.00718, 3, 4, 280.37, 56.84, 0.60267, 16, 38.45, -45.93, 0.29264, 5, 0.45, 56.85, 0.10469, 4, 4, 266.64, 127.6, 0.23893, 16, 109.6, -34.41, 0.74953, 5, -14.19, 127.42, 0.00584, 45, 84.5, 512.08, 0.0057, 4, 4, 274.73, 89.71, 0.42167, 16, 71.47, -41.32, 0.54988, 5, -5.61, 89.64, 0.01835, 45, 92.59, 474.19, 0.01011, 3, 4, 254.74, 154.49, 0.18087, 16, 136.84, -23.35, 0.81672, 45, 72.6, 538.97, 0.00242, 3, 4, 224.94, 147.39, 0.06333, 16, 130.68, 6.65, 0.92796, 45, 42.8, 531.88, 0.00871, 5, 4, 307.1, 114.69, 0.27966, 16, 95.44, -74.45, 0.46831, 5, 26.43, 115.04, 0.17894, 6, -60.02, 113.32, 0.06808, 45, 124.96, 499.18, 0.00501, 5, 4, 308.35, 74.19, 0.33588, 16, 54.92, -74.44, 0.26611, 5, 28.2, 74.57, 0.30632, 6, -57.06, 72.92, 0.08287, 45, 126.21, 458.68, 0.00882, 6, 4, 335.67, 84.12, 0.16856, 16, 63.99, -102.06, 0.16314, 5, 55.4, 84.85, 0.43229, 6, -30.18, 84, 0.22883, 33, -399.16, 134.06, 0.00209, 45, 153.53, 468.61, 0.0051, 6, 4, 356.58, 77.24, 0.08331, 16, 56.46, -122.74, 0.00702, 5, 76.4, 78.23, 0.52719, 6, -9, 78, 0.37512, 33, -377.97, 128.07, 0.00425, 45, 174.44, 461.72, 0.00311, 5, 4, 381.3, 76.51, 0.03666, 5, 101.13, 77.82, 0.38071, 6, 15.73, 78.32, 0.57578, 33, -353.24, 128.39, 0.00672, 45, 199.16, 460.99, 0.00012, 3, 4, 422.96, 47.92, 0.00066, 5, 143.15, 49.78, 0.07307, 6, 58.57, 51.53, 0.92628, 2, 5, 170.01, 48.23, 0.02067, 6, 85.46, 50.77, 0.97933, 2, 5, 190.71, 46.54, 0.00406, 6, 106.2, 49.69, 0.99594, 4, 4, 403.94, 70.06, 0.01207, 5, 123.84, 71.67, 0.22111, 6, 38.62, 72.84, 0.75777, 33, -330.35, 122.91, 0.00905, 2, 5, 140.68, -47.17, 0.07381, 6, 58.95, -45.45, 0.92619, 2, 5, 167.28, -48.87, 0.02869, 6, 85.59, -46.37, 0.97131, 2, 5, 191.22, -47.32, 0.00829, 6, 109.47, -44.12, 0.99171, 2, 5, 215.75, -44.45, 0.00065, 6, 133.91, -40.52, 0.99935, 1, 6, 130.17, 49.94, 1, 5, 4, 303.16, -83.59, 0.34998, 13, 72.97, 65.25, 0.34117, 5, 25.06, -83.28, 0.22956, 6, -55.55, -84.95, 0.07029, 45, 121.02, 300.89, 0.00899, 6, 4, 329.06, -79.22, 0.18358, 13, 69.36, 91.27, 0.16033, 5, 50.9, -78.57, 0.42514, 6, -29.86, -79.48, 0.22129, 33, -398.84, -29.41, 0.00125, 45, 146.92, 305.26, 0.0084, 6, 4, 352.52, -80.51, 0.08239, 13, 71.33, 114.68, 0.02676, 5, 74.38, -79.55, 0.49574, 6, -6.37, -79.77, 0.38505, 33, -375.35, -29.7, 0.00355, 45, 170.37, 303.98, 0.00652, 5, 4, 380.01, -83.42, 0.03089, 5, 101.91, -82.1, 0.33964, 6, 21.22, -81.51, 0.61953, 33, -347.75, -31.45, 0.00623, 45, 197.87, 301.07, 0.00371, 5, 4, 404.68, -83.9, 0.00823, 5, 126.58, -82.26, 0.20576, 6, 45.89, -80.94, 0.77624, 33, -323.08, -30.88, 0.00867, 45, 222.54, 300.59, 0.0011, 4, 4, 427.75, -80.32, 0.00106, 5, 149.6, -78.39, 0.11042, 6, 68.78, -76.4, 0.8775, 33, -300.19, -26.33, 0.01101, 3, 5, 173.01, -74.11, 0.04894, 6, 92.07, -71.43, 0.93767, 33, -276.91, -21.36, 0.0134, 5, 4, 293.97, -120.02, 0.29325, 13, 109.11, 55, 0.60065, 5, 16.35, -119.82, 0.08339, 6, -63.19, -121.73, 0.01851, 45, 111.83, 264.47, 0.0042, 4, 4, 171.07, -111.36, 0.55128, 13, 96.86, -67.6, 0.24376, 52, 62.43, -53.15, 0.17452, 45, -11.07, 273.12, 0.03044, 4, 4, 196.02, 102.26, 0.49905, 16, 86.46, 36.96, 0.3652, 49, 95.22, 7.1, 0.09603, 45, 13.88, 486.74, 0.03973, 4, 4, 196.04, 68.72, 0.70512, 16, 52.94, 37.99, 0.15888, 49, 95.23, -26.44, 0.096, 45, 13.9, 453.21, 0.04, 4, 4, 151.41, 26.99, 0.77355, 52, 42.78, 85.21, 0.0955, 49, 50.61, -68.16, 0.08595, 45, -30.73, 411.48, 0.045, 4, 4, 174.66, 38.41, 0.79027, 16, 23.31, 60.3, 0.01613, 49, 73.85, -56.75, 0.1536, 45, -7.48, 422.89, 0.04, 4, 4, 174.47, 16.56, 0.79488, 13, -30.9, -60.46, 0.01152, 52, 65.83, 74.77, 0.1536, 45, -7.68, 401.04, 0.04, 4, 4, 196.97, -10.14, 0.80802, 13, -3.56, -38.74, 0.04638, 52, 88.34, 48.08, 0.1056, 45, 14.83, 374.35, 0.04, 4, 4, 204.95, -48.46, 0.70776, 13, 34.98, -31.89, 0.15624, 52, 96.31, 9.76, 0.096, 45, 22.8, 336.03, 0.04, 4, 4, 196.36, -83.35, 0.54327, 13, 69.6, -41.49, 0.31431, 52, 87.73, -25.13, 0.10599, 45, 14.22, 301.14, 0.03642, 4, 4, 82.97, -82.85, 0.69616, 8, -229, -4.27, 0.0057, 54, -0.14, -3.63, 0.23395, 45, -99.17, 301.63, 0.06419, 4, 4, 76.17, 125.94, 0.69651, 9, -250.57, -26.02, 0.00405, 51, -2.18, 7.82, 0.23352, 45, -105.97, 510.42, 0.06593, 3, 4, 136.61, -7.32, 0.756, 53, 40.01, 59.92, 0.189, 45, -45.54, 377.17, 0.055, 3, 4, 157.88, -32.34, 0.756, 53, 61.29, 34.89, 0.189, 45, -24.26, 352.14, 0.055, 4, 4, 164.04, -70.04, 0.73116, 13, 55.36, -73.41, 0.02596, 53, 67.44, -2.81, 0.18928, 45, -18.1, 314.44, 0.0536, 5, 4, 110.81, 24.91, 0.76543, 9, -272.69, 78.46, 2e-05, 53, 14.22, 92.15, 0.08505, 50, 21.57, -81.16, 0.0945, 45, -71.33, 409.4, 0.055, 5, 4, 146.7, -107.33, 0.72881, 13, 92.12, -91.83, 0.03454, 8, -289.29, 27.77, 0.00015, 53, 50.11, -40.1, 0.19087, 45, -35.44, 277.15, 0.04563, 4, 4, 107.66, -126.87, 0.76486, 8, -248.16, 42.42, 0.00255, 53, 11.06, -59.63, 0.19185, 45, -74.49, 257.62, 0.04074, 4, 4, 71.11, -124.32, 0.75811, 8, -212.2, 35.45, 0.00957, 53, -25.48, -57.08, 0.19192, 45, -111.03, 260.17, 0.0404, 5, 3, 120.27, -103.43, 0.05797, 4, 39.88, -99.53, 0.6777, 8, -184.21, 7.04, 0.02898, 53, -56.72, -32.29, 0.19116, 45, -142.26, 284.96, 0.0442, 5, 3, 114.4, -60.69, 0.06862, 4, 29.16, -57.73, 0.65952, 8, -178.65, -35.74, 0.02979, 53, -67.44, 9.51, 0.18948, 45, -152.98, 326.76, 0.05259, 4, 4, 37.07, -19.29, 0.75139, 8, -191.16, -72.94, 0.00461, 53, -59.53, 47.94, 0.189, 45, -145.08, 365.19, 0.055, 5, 4, 53.12, 18.51, 0.76401, 9, -214.64, 77.8, 0.00144, 53, -43.48, 85.75, 0.08505, 50, -36.13, -87.56, 0.0945, 45, -129.03, 403, 0.055, 4, 4, 35.92, 52.24, 0.74129, 9, -201.67, 42.24, 0.01471, 50, -53.33, -53.83, 0.189, 45, -146.22, 436.72, 0.055, 5, 3, 128.73, 88.68, 0.06192, 4, 26.31, 92.3, 0.66594, 9, -197, 1.31, 0.02961, 50, -62.94, -13.77, 0.18937, 45, -155.83, 476.78, 0.05316, 6, 3, 139.03, 126.91, 0.0157, 4, 32.16, 131.45, 0.72923, 5, -248.7, 128.23, 0.00033, 9, -207.57, -36.84, 0.01736, 50, -57.08, 25.38, 0.19065, 45, -149.98, 515.94, 0.04673, 5, 4, 53.04, 159.09, 0.75693, 5, -228.18, 156.14, 0.00246, 9, -231.64, -61.74, 0.00591, 50, -36.21, 53.02, 0.19132, 45, -129.1, 543.58, 0.04339, 4, 4, 90.96, 166.81, 0.764, 9, -270.22, -64.8, 0.00137, 50, 1.72, 60.74, 0.19134, 45, -91.18, 551.3, 0.04329, 4, 4, 124.41, 159.36, 0.76328, 9, -302.52, -53.34, 0.00013, 50, 35.16, 53.29, 0.19085, 45, -57.73, 543.85, 0.04574, 4, 4, 148.39, 134.08, 0.72103, 16, 119.75, 83.58, 0.03798, 50, 59.15, 28.01, 0.18975, 45, -33.75, 518.56, 0.05124, 4, 4, 154.38, 97.04, 0.74736, 16, 82.54, 78.74, 0.00864, 50, 65.14, -9.03, 0.189, 45, -27.76, 481.52, 0.055, 4, 4, 131.55, 59.4, 0.75598, 9, -297.46, 46.75, 2e-05, 50, 42.3, -46.67, 0.189, 45, -50.59, 443.88, 0.055, 4, 4, 108.43, -48.44, 0.74723, 8, -258.46, -35.33, 0.00077, 54, 25.32, 30.78, 0.187, 45, -73.71, 336.04, 0.065, 4, 4, 120.64, -81.57, 0.75096, 8, -266.55, -0.96, 0.0009, 54, 37.53, -2.35, 0.18796, 45, -61.5, 302.91, 0.06018, 4, 4, 110.81, -106.58, 0.75397, 8, -253.75, 22.66, 0.00207, 54, 27.7, -27.35, 0.18901, 45, -71.34, 277.91, 0.05494, 4, 4, 59.05, -103.88, 0.74088, 8, -202.71, 13.69, 0.01604, 54, -24.06, -24.65, 0.18923, 45, -123.09, 280.61, 0.05385, 5, 3, 132.31, -81.84, 0.0065, 4, 49.37, -76.69, 0.7263, 8, -196.4, -14.46, 0.01999, 54, -33.74, 2.53, 0.1882, 45, -132.77, 307.79, 0.05901, 4, 4, 53.26, -54.22, 0.73669, 8, -202.99, -36.3, 0.01237, 54, -29.85, 25, 0.18726, 45, -128.88, 330.27, 0.06368, 4, 4, 76.42, -42.83, 0.74463, 8, -227.36, -44.79, 0.00337, 54, -6.69, 36.39, 0.187, 45, -105.73, 341.65, 0.065, 4, 4, 83.63, -112.44, 0.75082, 8, -226.07, 25.18, 0.00662, 54, 0.52, -33.21, 0.18936, 45, -98.51, 272.05, 0.0532, 4, 4, 114.55, 116.19, 0.74804, 9, -287.49, -11.69, 0.00062, 51, 36.21, -1.92, 0.18716, 45, -67.59, 500.68, 0.06418, 4, 4, 106.82, 142.1, 0.75214, 9, -282.96, -38.34, 0.0008, 51, 28.47, 23.98, 0.18823, 45, -75.32, 526.58, 0.05883, 4, 4, 84.36, 151.92, 0.75288, 9, -261.86, -50.82, 0.00221, 51, 6.01, 33.81, 0.18877, 45, -97.78, 536.41, 0.05614, 5, 4, 55.06, 143.52, 0.7455, 5, -225.96, 140.6, 0.00198, 9, -231.76, -46.04, 0.00731, 51, -23.29, 25.41, 0.1887, 45, -127.08, 528.01, 0.05651, 5, 4, 44.89, 125.46, 0.7386, 5, -235.89, 122.41, 0.00074, 9, -219.47, -29.35, 0.01345, 51, -33.45, 7.34, 0.1882, 45, -137.25, 509.94, 0.05902, 5, 4, 49.89, 94.05, 0.73473, 5, -230.49, 91.06, 0.0003, 9, -220.62, 2.44, 0.01377, 51, -28.46, -24.07, 0.1872, 45, -132.25, 478.53, 0.064, 4, 4, 73.62, 82.52, 0.74292, 9, -242.77, 16.76, 0.00508, 51, -4.73, -35.59, 0.187, 45, -108.52, 467.01, 0.065, 4, 4, 102.91, 87.95, 0.74669, 9, -272.5, 14.93, 0.00131, 51, 24.56, -30.16, 0.187, 45, -79.23, 472.43, 0.065, 5, 4, 78.23, 21.83, 0.79509, 9, -239.97, 77.56, 0.00052, 54, -4.88, 101.06, 0.0752, 51, -0.12, -96.28, 0.06918, 45, -103.92, 406.32, 0.06, 3, 4, 234.54, 111.53, 0.17964, 16, 94.53, -1.82, 0.79921, 45, 52.4, 496.01, 0.02114, 3, 4, 238.24, 65.66, 0.48153, 16, 48.58, -4.09, 0.49214, 45, 56.09, 450.15, 0.02633, 3, 4, 238.6, 16.71, 0.8569, 16, -0.36, -2.94, 0.11587, 45, 56.46, 401.2, 0.02723, 3, 4, 240.14, -12.99, 0.90311, 13, 0.55, 4.33, 0.06988, 45, 58, 371.5, 0.02701, 3, 4, 245.97, -54.8, 0.61132, 13, 42.51, 8.93, 0.36317, 45, 63.83, 329.69, 0.02551, 3, 4, 239.34, -97.75, 0.28727, 13, 85.26, 1.04, 0.69339, 45, 57.2, 286.74, 0.01934, 3, 4, 265.1, 18.45, 0.92051, 16, 0.55, -29.48, 0.05757, 45, 82.96, 402.94, 0.02192, 3, 4, 297.7, 17.31, 0.44292, 5, 18.3, 17.55, 0.53487, 45, 115.56, 401.8, 0.02221, 3, 4, 321.28, 16.9, 0.22209, 5, 41.88, 17.44, 0.75555, 45, 139.14, 401.38, 0.02236, 4, 4, 346.25, 17.86, 0.00592, 5, 66.84, 18.73, 0.7242, 6, -16.8, 18.24, 0.2467, 45, 164.11, 402.35, 0.02318, 4, 4, 371.57, 19.17, 0.00069, 5, 92.14, 20.37, 0.3328, 6, 8.44, 20.63, 0.64264, 45, 189.43, 403.66, 0.02387, 3, 5, 119.17, 21.68, 0.04229, 6, 35.43, 22.74, 0.93342, 45, 216.48, 404.62, 0.02429, 3, 5, 119.49, -16.46, 0.03965, 6, 36.87, -15.38, 0.93702, 45, 216.3, 366.47, 0.02333, 3, 5, 92.43, -15.69, 0.26975, 6, 9.8, -15.41, 0.70724, 45, 189.26, 367.59, 0.02301, 3, 5, 66.07, -14.92, 0.75651, 6, -16.58, -15.41, 0.22069, 45, 162.9, 368.71, 0.0228, 4, 4, 321.12, -14.31, 0.10991, 5, 42.13, -13.77, 0.76015, 6, -40.54, -14.97, 0.10742, 45, 138.98, 370.17, 0.02251, 3, 4, 296.85, -14.59, 0.43076, 5, 17.86, -14.36, 0.54669, 45, 114.7, 369.9, 0.02255, 3, 4, 264.51, -14.84, 0.95532, 13, 3.12, 28.63, 0.02255, 45, 82.37, 369.65, 0.02213, 3, 4, 199.19, 27.02, 0.91415, 16, 11.16, 36.13, 0.05025, 45, 17.05, 411.5, 0.0356, 4, 3, 81.8, 43.36, 0.51817, 4, -15.13, 41.9, 0.37093, 9, -149.74, 46.3, 0.0712, 45, -197.27, 426.39, 0.0397, 5, 3, 54.55, 39.18, 0.75213, 4, -41.72, 34.63, 0.08335, 9, -122.46, 50.28, 0.1262, 45, -223.86, 419.12, 0.03348, 47, -436.37, 129.03, 0.00484, 6, 3, 25.88, 37.44, 0.774, 4, -70, 29.63, 0.00281, 7, -54, -36.6, 0.02137, 9, -93.79, 51.82, 0.16253, 45, -252.14, 414.11, 0.02702, 47, -431.28, 100.77, 0.01226, 5, 3, -6.75, 40.73, 0.52077, 7, -21.4, -40.12, 0.1975, 9, -61.18, 48.3, 0.24109, 45, -284.93, 413.64, 0.01969, 47, -430.72, 67.98, 0.02095, 5, 3, -39.97, 48.64, 0.13342, 7, 11.76, -48.27, 0.42237, 9, -28.02, 40.15, 0.40174, 45, -318.84, 417.7, 0.01247, 47, -434.67, 34.06, 0.03, 6, 3, -75.3, 57.94, 0.01079, 7, 47.03, -57.82, 0.36807, 9, 7.25, 30.6, 0.56247, 10, -93.53, 70.61, 0.01397, 45, -355, 422.9, 0.00508, 47, -439.76, -2.12, 0.03961, 4, 7, 88.79, -64.77, 0.38285, 9, 49.01, 23.65, 0.37885, 10, -52.74, 59.28, 0.19931, 47, -441.46, -44.42, 0.03899, 4, 7, 137.24, -69.14, 0.3883, 9, 97.46, 19.28, 0.09389, 10, -5.02, 49.81, 0.48387, 47, -439.76, -93.04, 0.03394, 4, 7, 168.02, -73.57, 0.33195, 9, 128.24, 14.85, 0.01284, 10, 25.12, 42.15, 0.62903, 47, -440.33, -124.13, 0.02617, 4, 7, 211.24, -6.06, 0.86325, 10, 75.24, 104.7, 0.08964, 12, 102.23, -115.75, 0.02854, 47, -367.97, -158.61, 0.01856, 4, 7, 167.3, 51.82, 0.61066, 8, 130.47, -46.08, 0.0155, 12, 44.98, -71, 0.35001, 47, -316.01, -107.81, 0.02384, 4, 7, 128.28, 131.28, 0.02028, 8, 91.45, 33.39, 0.0825, 12, -13.01, -4.11, 0.87298, 47, -242.01, -59.2, 0.02424, 3, 9, 106.63, -44.1, 0.016, 10, -2.6, -14.19, 0.96731, 47, -501.51, -110.03, 0.01669, 4, 3, 83, -18.34, 0.59748, 4, -6.88, -19.25, 0.33999, 8, -147.55, -78.32, 0.02264, 45, -189.02, 365.24, 0.03989, 5, 3, 54.1, -17.2, 0.81748, 4, -35.72, -21.43, 0.0909, 8, -118.66, -79.66, 0.05314, 45, -217.86, 363.06, 0.03352, 47, -380.33, 135.2, 0.00495, 5, 3, 27.81, -18.08, 0.86114, 4, -61.73, -25.31, 0.01889, 8, -92.36, -78.97, 0.08046, 45, -243.87, 359.18, 0.02756, 47, -376.37, 109.2, 0.01195, 5, 3, -4.85, -19.91, 0.80691, 4, -93.97, -30.86, 0.00176, 8, -59.69, -77.37, 0.1504, 45, -276.11, 353.62, 0.02027, 47, -370.72, 76.98, 0.02067, 5, 3, -38.26, -25.64, 0.09327, 7, 10.58, 26.01, 0.73121, 8, -26.24, -71.88, 0.13287, 45, -308.64, 344.11, 0.01294, 47, -361.11, 44.47, 0.02972, 6, 3, -76.57, -31.93, 0.01087, 7, 48.94, 32.03, 0.73963, 8, 12.11, -65.87, 0.19364, 12, -64.41, -120.32, 0.01131, 45, -345.99, 333.48, 0.00469, 47, -350.37, 7.16, 0.03987, 4, 7, 91.01, 35.87, 0.74433, 8, 54.19, -62.03, 0.1383, 12, -24.71, -105.87, 0.07761, 47, -341.32, -34.1, 0.03977, 4, 7, 137.74, 44.54, 0.69256, 8, 100.91, -53.36, 0.05129, 12, 18.26, -85.57, 0.22362, 47, -326.91, -79.39, 0.03252, 4, 7, 78.15, 114.97, 0.07463, 8, 41.32, 17.07, 0.55439, 12, -57.33, -32.67, 0.34357, 47, -264.44, -11.49, 0.0274, 3, 8, 0.99, 4.47, 0.95576, 12, -93.1, -55.14, 0.01653, 47, -281.97, 26.95, 0.02772, 6, 3, -27.89, -87.2, 0.18065, 4, -109.16, -100.35, 0.00352, 7, 0.65, 87.65, 0.09765, 8, -36.17, -10.24, 0.69258, 45, -291.3, 284.13, 0.00665, 47, -301.19, 62, 0.01895, 6, 3, -0.5, -72.22, 0.40401, 4, -83.66, -82.34, 0.02295, 7, -26.84, 72.87, 0.09151, 8, -63.67, -25.03, 0.45569, 45, -265.81, 302.15, 0.01264, 47, -319.28, 87.44, 0.0132, 6, 3, 28.9, -64.31, 0.56931, 4, -55.36, -71.12, 0.08996, 7, -56.29, 65.17, 0.01998, 8, -93.12, -32.73, 0.29583, 45, -237.51, 313.37, 0.01926, 47, -330.58, 115.7, 0.00566, 5, 3, 52.94, -63.17, 0.55435, 4, -31.61, -67.23, 0.2246, 7, -80.34, 64.2, 0.00087, 8, -117.17, -33.7, 0.19489, 45, -213.75, 317.26, 0.02529, 4, 3, 73.91, -63.94, 0.43875, 4, -10.69, -65.59, 0.40852, 8, -138.14, -32.78, 0.12224, 45, -192.83, 318.89, 0.0305, 4, 3, 79.95, 74.24, 0.45419, 4, -20.5, 72.37, 0.3895, 9, -148.12, 15.4, 0.12441, 45, -202.64, 456.86, 0.0319, 6, 3, 52.41, 72.37, 0.59899, 4, -47.65, 67.36, 0.15399, 7, -80.78, -71.35, 0.00371, 9, -120.56, 17.07, 0.21636, 45, -229.79, 451.85, 0.02432, 47, -469.08, 123.01, 0.00264, 6, 3, 29.89, 74.47, 0.59303, 4, -70.26, 66.87, 0.05169, 7, -58.27, -73.6, 0.02151, 9, -98.05, 14.82, 0.3063, 45, -252.4, 451.35, 0.01895, 47, -468.52, 100.4, 0.00852, 6, 3, -0.03, 81.42, 0.43023, 4, -100.78, 70.35, 0.00706, 7, -28.4, -80.76, 0.05937, 9, -68.19, 7.66, 0.47525, 45, -282.92, 454.83, 0.01246, 47, -471.91, 69.87, 0.01562, 6, 3, -27.31, 91.47, 0.2167, 4, -129.02, 77.22, 0, 7, -1.2, -91.01, 0.05362, 9, -40.98, -2.59, 0.70104, 45, -311.16, 461.7, 0.00691, 47, -478.7, 41.61, 0.02172, 4, 3, -61.61, 104.63, 0.03114, 9, -6.77, -16, 0.90549, 10, -112.4, 25.76, 0.03421, 47, -487.74, 5.99, 0.02917, 4, 7, 71.78, -113.27, 0.02037, 9, 32, -24.85, 0.67002, 10, -74.78, 12.85, 0.28155, 47, -491.7, -33.58, 0.02807, 4, 7, 111.89, -116.02, 0.04411, 9, 72.11, -27.6, 0.28731, 10, -35.19, 5.87, 0.64685, 47, -489.44, -73.72, 0.02173, 4, 7, 201.55, 62.89, 0.42106, 8, 164.73, -35.01, 0.00017, 12, 75.28, -51.55, 0.5544, 47, -300.76, -140.42, 0.02437, 4, 7, 246.26, -9.3, 0.57405, 10, 109.72, 97.78, 0.27574, 12, 136.92, -109.95, 0.13425, 47, -366.82, -193.76, 0.01597, 2, 12, 22.61, 14.02, 0.97818, 47, -228.77, -96.91, 0.02182, 3, 8, 223.63, 31.77, 0, 12, 115.2, 28.04, 0.97663, 47, -227.17, -190.55, 0.02336, 2, 12, 64.91, 26.75, 0.97734, 47, -221.77, -140.54, 0.02266, 4, 7, 252.65, 52.14, 0.3186, 10, 122.58, 158.2, 0.03287, 12, 127.43, -48.91, 0.62434, 47, -305.06, -192.46, 0.02419, 4, 7, 180.73, -8.79, 0.93218, 9, 140.95, 79.63, 0.00019, 10, 44.61, 105.22, 0.03834, 47, -374.47, -128.68, 0.02928, 4, 7, 140.3, -12.18, 0.91479, 9, 100.51, 76.24, 0.00948, 10, 4.05, 106.13, 0.03962, 47, -382.86, -88.98, 0.03611, 4, 7, 240.91, -68.3, 0.23153, 10, 98.16, 39.68, 0.74019, 12, 146.79, -168.36, 0.00594, 47, -426.03, -195.8, 0.02234, 4, 7, 276.91, -71.57, 0.12556, 10, 133.61, 32.61, 0.84028, 12, 182.44, -162.35, 0.01074, 47, -424.8, -231.93, 0.02343, 2, 10, 79.86, -43.06, 0.97711, 47, -510.75, -196.91, 0.02289, 2, 10, 31.56, -32.03, 0.98245, 47, -511.06, -147.37, 0.01755, 3, 7, 198.7, -73.55, 0.28986, 10, 55.63, 38.92, 0.68652, 47, -436.49, -154.57, 0.02362, 2, 10, 129.86, -54.41, 0.97427, 47, -510.37, -248.17, 0.02573, 2, 10, 179.5, -58.54, 0.97257, 47, -503.04, -297.44, 0.02743, 3, 10, 392.62, -41.56, 0.965, 11, -69.52, -56.03, 0.01121, 47, -437.76, -501.04, 0.02379, 3, 10, 419.69, -41.03, 0.91894, 11, -43.06, -50.29, 0.06178, 47, -431.05, -527.27, 0.01928, 3, 10, 451.67, -42.49, 0.69839, 11, -11.41, -45.54, 0.28577, 47, -425.15, -558.73, 0.01585, 3, 10, 479.48, -44.48, 0.32276, 11, 16.26, -42.13, 0.66164, 47, -420.73, -586.25, 0.0156, 3, 10, 508.35, -46.21, 0.07676, 11, 44.93, -38.26, 0.9073, 47, -415.82, -614.76, 0.01594, 3, 10, 537.29, -52, 0.00744, 11, 74.44, -38.35, 0.97827, 47, -414.83, -644.26, 0.01429, 2, 11, 70.63, 5.56, 0.98802, 47, -371.08, -638.85, 0.01198, 2, 11, 41.14, 6.15, 0.98695, 47, -371.58, -609.36, 0.01305, 3, 10, 485.46, 4.61, 0.119, 11, 12.65, 7.19, 0.86749, 47, -371.58, -580.85, 0.01351, 3, 10, 458.29, 11.5, 0.89718, 11, -15.33, 8.71, 0.08885, 47, -371.08, -552.83, 0.01398, 2, 10, 425.92, 15.57, 0.9813, 47, -374.53, -520.39, 0.0187, 2, 10, 393.58, 17.62, 0.97986, 47, -379.93, -488.44, 0.02014, 2, 10, 282.76, -51.98, 0.97446, 47, -473.03, -396.47, 0.02554, 4, 7, 328.93, -68.06, 0.04007, 10, 185.72, 30.61, 0.92867, 12, 231.85, -145.68, 0.00631, 47, -414.83, -283.11, 0.02496, 3, 7, 430.69, -62.49, 0.00019, 10, 287.49, 25.37, 0.9752, 47, -396.64, -383.38, 0.02461, 2, 11, 159.02, -43.09, 0.98977, 47, -416.47, -728.96, 0.01023, 1, 11, 381.52, -6.99, 1, 3, 11, 165.77, 11.11, 0.94717, 12, 671.47, -33.57, 0.04294, 47, -362.06, -733.72, 0.00989, 3, 5, 194.46, -68.94, 0.01781, 6, 113.35, -65.63, 0.96659, 33, -255.63, -15.56, 0.0156, 3, 5, 217.95, -64.08, 0.00277, 6, 136.69, -60.08, 0.97923, 33, -232.29, -10.01, 0.018, 4, 4, 426.89, 67.41, 0.00261, 5, 146.83, 69.32, 0.10698, 6, 61.67, 71.16, 0.87905, 33, -307.31, 121.23, 0.01136, 3, 5, 171.67, 65.78, 0.03527, 6, 86.6, 68.36, 0.95086, 33, -282.38, 118.43, 0.01387, 3, 5, 191.87, 63.88, 0.00998, 6, 106.85, 67.06, 0.97413, 33, -262.13, 117.13, 0.01589, 3, 5, 214.15, 63.26, 0.00115, 6, 129.14, 67.1, 0.98073, 33, -239.83, 117.16, 0.01812], "hull": 114, "edges": [0, 226, 6, 8, 14, 16, 16, 18, 18, 20, 22, 24, 30, 32, 36, 38, 48, 50, 52, 54, 54, 56, 68, 70, 70, 72, 72, 74, 74, 76, 84, 86, 90, 92, 92, 94, 94, 96, 102, 104, 104, 106, 106, 108, 118, 120, 120, 122, 122, 124, 128, 130, 146, 148, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 206, 208, 214, 216, 212, 214, 208, 210, 210, 212, 202, 204, 204, 206, 196, 198, 198, 200, 182, 184, 184, 186, 180, 182, 178, 180, 154, 156, 148, 150, 150, 152, 142, 144, 144, 146, 138, 140, 140, 142, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 20, 22, 32, 34, 34, 36, 38, 40, 40, 42, 42, 44, 46, 48, 44, 46, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 166, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 46, 240, 242, 244, 250, 250, 248, 246, 252, 252, 248, 254, 260, 260, 258, 256, 262, 262, 258, 26, 268, 270, 268, 244, 292, 292, 294, 294, 296, 254, 300, 300, 302, 302, 304, 220, 222, 222, 306, 306, 304, 220, 308, 308, 296, 326, 40, 240, 176, 328, 330, 330, 334, 334, 332, 332, 336, 336, 338, 338, 340, 340, 342, 342, 326, 328, 240, 348, 350, 350, 352, 348, 354, 352, 356, 356, 358, 358, 360, 360, 362, 364, 366, 366, 368, 368, 370, 370, 372, 374, 376, 376, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 354, 388, 390, 390, 392, 396, 398, 396, 394, 388, 400, 400, 398, 364, 362, 392, 402, 402, 394, 374, 372, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 418, 404, 80, 82, 82, 84, 76, 78, 78, 80, 476, 478, 136, 138, 134, 136, 96, 98, 98, 100, 100, 102, 124, 126, 126, 128, 130, 132, 132, 134, 564, 566, 566, 568, 568, 570, 570, 572, 574, 576, 576, 578, 578, 580, 580, 582, 582, 584, 560, 586, 586, 562, 562, 564, 588, 590, 590, 584, 86, 88, 88, 90, 572, 592, 592, 594, 594, 596, 596, 574, 112, 114, 114, 116, 116, 118, 108, 110, 110, 112, 222, 224, 224, 226, 216, 218, 218, 220, 28, 30, 28, 26, 26, 24], "width": 455, "height": 1725}}, "ear_L": {"ear_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [2, 6, 14.79, -66.5, 0.99, 33, -354.19, -16.43, 0.01, 2, 6, 13.88, -43.52, 0.99, 33, -355.09, 6.55, 0.01, 2, 6, 85.83, -40.68, 0.99, 33, -283.15, 9.39, 0.01, 2, 6, 86.73, -63.66, 0.99, 33, -282.24, -13.59, 0.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 72}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 3, 1, 2, 3], "vertices": [2, 6, 13.21, 49.53, 0.99, 33, -355.76, 99.6, 0.01, 2, 6, 12.42, 69.51, 0.99, 33, -356.55, 119.58, 0.01, 2, 6, 86.36, 72.43, 0.99, 33, -282.61, 122.5, 0.01, 2, 6, 87.15, 52.45, 0.99, 33, -281.82, 102.51, 0.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 74}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04581, 0.35333, 0.05159, 0.78712, 0.549, 0.92786, 0.94519, 0.6618, 0.94519, 0.15668, 0.47381, 0.06221], "triangles": [3, 2, 5, 5, 2, 0, 2, 1, 0, 5, 4, 3], "vertices": [2, 6, 65.75, -6.64, 0.96, 32, -342.86, 43.43, 0.04, 2, 6, 50.15, -7.54, 0.96, 32, -358.46, 42.53, 0.04, 2, 6, 46.03, -31.59, 0.96, 32, -362.58, 18.47, 0.04, 2, 6, 56.35, -50.22, 0.96536, 32, -352.26, -0.15, 0.03464, 2, 6, 74.52, -49.5, 0.96555, 32, -334.09, 0.57, 0.03445, 2, 6, 77.03, -26.76, 0.96, 32, -331.58, 23.31, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 48, "height": 36}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.05792, 0.18911, 0.04758, 0.68058, 0.43891, 0.94507, 0.941, 0.82314, 0.94838, 0.42359, 0.50684, 0.05217], "triangles": [4, 2, 5, 5, 1, 0, 3, 2, 4, 2, 1, 5], "vertices": [2, 6, 72.96, 57.17, 0.96551, 32, -335.65, 107.24, 0.03449, 2, 6, 54.77, 56.94, 0.96567, 32, -353.84, 107, 0.03433, 2, 6, 45.72, 38.17, 0.96, 32, -362.89, 88.24, 0.04, 2, 6, 51.16, 14.77, 0.96, 32, -357.45, 64.84, 0.04, 2, 6, 65.94, 15.01, 0.96, 32, -342.67, 65.07, 0.04, 2, 6, 78.85, 36.28, 0.96, 32, -329.76, 86.35, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 47, "height": 37}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 29, -8.96, -9.08, 0.96, 32, -352.92, 12.2, 0.04, 2, 29, -9.67, 8.91, 0.96, 32, -353.63, 30.19, 0.04, 2, 29, 8.31, 9.62, 0.96, 32, -335.64, 30.9, 0.04, 2, 29, 9.02, -8.37, 0.96, 32, -334.93, 12.91, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.72554, 0.1125, 0.89408, 0.21124, 1, 0.35191, 0.89371, 0.54273, 0.85722, 0.74441, 0.75248, 0.88156, 0.59971, 0.97238, 0.45363, 0.97834, 0.30321, 0.93865, 0.19178, 0.82808, 0.08352, 0.77443, 0.01636, 0.75435, 0.01624, 0.66159, 0.07591, 0.54261, 0.17346, 0.39939, 0.26154, 0.27982, 0.36888, 0.19788, 0.51897, 0.18199, 0.60864, 0.02539, 0.09829, 0.6481, 0.1426, 0.55595, 0.21792, 0.45528, 0.31362, 0.37305, 0.42882, 0.34894, 0.54912, 0.37646, 0.65655, 0.43827, 0.74162, 0.56445, 0.1653, 0.68355, 0.25746, 0.7431, 0.37354, 0.7828, 0.48254, 0.78847, 0.59951, 0.75302, 0.68256, 0.67448], "triangles": [8, 29, 7, 7, 30, 6, 7, 29, 30, 30, 31, 6, 6, 31, 5, 9, 28, 8, 8, 28, 29, 31, 32, 5, 5, 32, 4, 10, 27, 9, 9, 27, 28, 31, 30, 24, 24, 30, 23, 30, 29, 23, 28, 22, 29, 29, 22, 23, 10, 19, 27, 19, 10, 12, 10, 11, 12, 31, 25, 32, 31, 24, 25, 32, 26, 4, 4, 26, 3, 27, 21, 28, 28, 21, 22, 19, 20, 27, 27, 20, 21, 32, 25, 26, 12, 13, 19, 19, 13, 20, 3, 26, 1, 20, 14, 21, 20, 13, 14, 3, 1, 2, 1, 26, 25, 21, 15, 22, 21, 14, 15, 25, 0, 1, 25, 24, 0, 23, 17, 24, 0, 17, 18, 0, 24, 17, 22, 16, 23, 22, 15, 16, 23, 16, 17], "vertices": [2, 6, 76.53, -42.87, 0.96372, 32, -332.08, 7.19, 0.03628, 2, 6, 73.89, -51.07, 0.96618, 32, -334.72, -1.01, 0.03382, 2, 6, 69.87, -56.32, 0.96773, 32, -338.74, -6.25, 0.03227, 2, 6, 63.95, -51.45, 0.96605, 32, -344.66, -1.38, 0.03395, 2, 6, 57.84, -49.94, 0.96539, 32, -350.77, 0.13, 0.03461, 2, 6, 53.53, -45.08, 0.96376, 32, -355.08, 4.99, 0.03624, 2, 6, 50.52, -37.86, 0.96182, 32, -358.09, 12.21, 0.03818, 2, 6, 50.06, -30.86, 0.96, 32, -358.55, 19.21, 0.04, 2, 6, 50.97, -23.59, 0.96, 32, -357.64, 26.47, 0.04, 2, 6, 54.07, -18.12, 0.96, 32, -354.54, 31.95, 0.04, 2, 6, 55.47, -12.86, 0.96, 32, -353.14, 37.2, 0.04, 2, 6, 55.95, -9.62, 0.96, 32, -352.66, 40.45, 0.04, 2, 6, 58.73, -9.5, 0.96, 32, -349.88, 40.56, 0.04, 2, 6, 62.41, -12.22, 0.96, 32, -346.2, 37.84, 0.04, 2, 6, 66.88, -16.73, 0.96, 32, -341.73, 33.33, 0.04, 2, 6, 70.64, -20.82, 0.96, 32, -337.97, 29.25, 0.04, 2, 6, 73.3, -25.87, 0.96, 32, -335.31, 24.2, 0.04, 2, 6, 74.06, -33.05, 0.9607, 32, -334.55, 17.02, 0.0393, 2, 6, 78.92, -37.16, 0.96198, 32, -329.69, 12.9, 0.03802, 2, 6, 59.29, -13.42, 0.96, 32, -349.32, 36.65, 0.04, 2, 6, 62.13, -15.44, 0.96, 32, -346.48, 34.63, 0.04, 2, 6, 65.29, -18.93, 0.96, 32, -343.32, 31.14, 0.04, 2, 6, 67.94, -23.43, 0.96, 32, -340.67, 26.64, 0.04, 2, 6, 68.88, -28.92, 0.96, 32, -339.73, 21.15, 0.04, 2, 6, 68.28, -34.72, 0.96114, 32, -340.33, 15.34, 0.03886, 2, 6, 66.63, -39.95, 0.96269, 32, -341.98, 10.12, 0.03731, 2, 6, 63.01, -44.18, 0.96382, 32, -345.6, 5.89, 0.03618, 2, 6, 58.35, -16.68, 0.96, 32, -350.26, 33.39, 0.04, 2, 6, 56.74, -21.17, 0.96, 32, -351.87, 28.9, 0.04, 2, 6, 55.77, -26.78, 0.96, 32, -352.84, 23.28, 0.04, 2, 6, 55.81, -32.02, 0.96, 32, -352.8, 18.05, 0.04, 2, 6, 57.09, -37.59, 0.96163, 32, -351.52, 12.48, 0.03837, 2, 6, 59.6, -41.48, 0.96289, 32, -349.01, 8.59, 0.03711], "hull": 19, "edges": [2, 4, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 20, 22, 18, 20, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 36, 14, 16, 16, 18, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 38, 54, 54, 56, 56, 58, 58, 60, 60, 62, 52, 64, 64, 62], "width": 48, "height": 30}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 30, -8.79, -9.06, 0.96, 32, -352.48, 77.27, 0.04, 2, 30, -9.5, 8.93, 0.96, 32, -353.19, 95.26, 0.04, 2, 30, 8.49, 9.64, 0.96, 32, -335.21, 95.97, 0.04, 2, 30, 9.2, -8.35, 0.96, 32, -334.5, 77.98, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 18}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.53428, 0.11582, 0.60115, 0.24699, 0.72681, 0.33359, 0.82956, 0.43567, 0.91969, 0.58712, 0.99018, 0.73414, 0.94929, 0.80456, 0.87428, 0.84626, 0.7502, 0.8769, 0.60696, 0.97222, 0.42363, 0.97371, 0.27153, 0.88692, 0.16669, 0.71626, 0.13711, 0.54339, 0.03137, 0.46186, 0.0696, 0.28515, 0.2002, 0.18451, 0.33733, 0.08963, 0.26246, 0.55081, 0.3155, 0.68294, 0.41316, 0.78795, 0.56063, 0.81508, 0.34266, 0.43967, 0.44744, 0.40379, 0.56516, 0.38454, 0.6887, 0.41692, 0.79671, 0.50705, 0.86599, 0.61731, 0.90739, 0.73282, 0.82913, 0.76169, 0.70041, 0.80282], "triangles": [11, 20, 10, 10, 21, 9, 10, 20, 21, 9, 30, 8, 9, 21, 30, 11, 19, 20, 11, 12, 19, 8, 29, 7, 8, 30, 29, 7, 28, 6, 7, 29, 28, 20, 23, 21, 21, 25, 30, 21, 24, 25, 21, 23, 24, 6, 28, 5, 30, 26, 29, 30, 25, 26, 20, 19, 23, 23, 19, 22, 29, 27, 28, 29, 26, 27, 28, 4, 5, 28, 27, 4, 12, 18, 19, 12, 13, 18, 19, 18, 22, 27, 26, 4, 26, 3, 4, 22, 18, 16, 14, 15, 13, 18, 13, 16, 13, 15, 16, 26, 25, 3, 23, 22, 17, 25, 2, 3, 2, 25, 1, 22, 16, 17, 24, 23, 1, 23, 17, 0, 25, 24, 1, 1, 23, 0], "vertices": [2, 6, 78.68, 40.53, 0.96028, 32, -329.93, 90.59, 0.03972, 2, 6, 74.34, 37.28, 0.96, 32, -334.27, 87.34, 0.04, 2, 6, 71.63, 31.38, 0.96, 32, -336.98, 81.45, 0.04, 2, 6, 68.35, 26.52, 0.96, 32, -340.26, 76.59, 0.04, 2, 6, 63.37, 22.18, 0.96, 32, -345.24, 72.25, 0.04, 2, 6, 58.5, 18.74, 0.96, 32, -350.11, 68.81, 0.04, 2, 6, 56.03, 20.53, 0.96, 32, -352.58, 70.59, 0.04, 2, 6, 54.48, 23.92, 0.96, 32, -354.13, 73.99, 0.04, 2, 6, 53.21, 29.58, 0.96, 32, -355.4, 79.65, 0.04, 2, 6, 49.72, 36.04, 0.96, 32, -358.89, 86.1, 0.04, 2, 6, 49.33, 44.46, 0.96215, 32, -359.28, 94.53, 0.03785, 2, 6, 52.01, 51.57, 0.9642, 32, -356.6, 101.64, 0.0358, 2, 6, 57.61, 56.62, 0.96552, 32, -351, 106.68, 0.03448, 2, 6, 63.43, 58.21, 0.96588, 32, -345.18, 108.28, 0.03412, 2, 6, 66.01, 63.18, 0.96736, 32, -342.6, 113.25, 0.03264, 2, 6, 72.08, 61.66, 0.96683, 32, -336.53, 111.73, 0.03317, 2, 6, 75.74, 55.79, 0.96499, 32, -332.87, 105.86, 0.03501, 2, 6, 79.21, 49.61, 0.96299, 32, -329.4, 99.68, 0.03701, 2, 6, 63.41, 52.44, 0.96412, 32, -345.2, 102.5, 0.03588, 2, 6, 59.02, 49.82, 0.96342, 32, -349.59, 99.89, 0.03658, 2, 6, 55.63, 45.19, 0.96209, 32, -352.98, 95.26, 0.03791, 2, 6, 54.97, 38.38, 0.96009, 32, -353.64, 88.44, 0.03991, 2, 6, 67.33, 48.9, 0.963, 32, -341.28, 98.97, 0.037, 2, 6, 68.74, 44.13, 0.96153, 32, -339.87, 94.2, 0.03847, 2, 6, 69.61, 38.75, 0.96, 32, -339, 88.81, 0.04, 2, 6, 68.73, 33.02, 0.96, 32, -339.88, 83.09, 0.04, 2, 6, 65.86, 27.94, 0.96, 32, -342.75, 78.01, 0.04, 2, 6, 62.24, 24.61, 0.96, 32, -346.37, 74.67, 0.04, 2, 6, 58.39, 22.55, 0.96, 32, -350.22, 72.62, 0.04, 2, 6, 57.27, 26.11, 0.96, 32, -351.34, 76.18, 0.04, 2, 6, 55.64, 31.97, 0.96, 32, -352.97, 82.04, 0.04], "hull": 18, "edges": [0, 34, 10, 12, 30, 32, 32, 34, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 6, 8, 8, 10, 0, 2, 2, 4, 4, 6, 22, 24, 24, 26, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 36, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 42], "width": 46, "height": 34}}, "hair": {"hair": {"type": "mesh", "uvs": [0.63409, 0.02048, 0.8054, 0.09235, 0.9152, 0.1823, 0.83497, 0.3271, 0.85945, 0.39161, 0.86793, 0.45969, 0.87777, 0.52777, 0.89651, 0.58313, 0.93242, 0.64281, 0.97448, 0.70123, 0.99795, 0.76927, 0.98907, 0.8238, 0.96452, 0.87612, 0.92767, 0.9051, 0.8746, 0.8863, 0.90175, 0.85848, 0.91797, 0.82119, 0.91565, 0.77587, 0.89328, 0.73762, 0.84903, 0.68648, 0.80409, 0.6366, 0.75662, 0.5829, 0.70787, 0.53181, 0.69034, 0.47485, 0.6846, 0.40023, 0.65795, 0.36548, 0.59547, 0.32801, 0.49673, 0.31576, 0.39758, 0.31714, 0.36327, 0.33154, 0.33985, 0.37141, 0.33427, 0.45762, 0.32269, 0.54691, 0.29647, 0.61974, 0.24737, 0.67998, 0.18686, 0.73587, 0.1503, 0.78907, 0.19194, 0.8314, 0.18877, 0.90385, 0.23502, 0.94515, 0.29889, 0.91895, 0.34249, 0.99193, 0.26644, 1, 0.19601, 0.95746, 0.13724, 0.91062, 0.07875, 0.87317, 0.01693, 0.83649, 0, 0.77658, 0.01864, 0.71716, 0.07419, 0.65832, 0.1192, 0.60081, 0.15111, 0.54887, 0.17536, 0.48736, 0.1922, 0.41844, 0.22012, 0.32712, 0.14776, 0.17889, 0.24428, 0.09686, 0.39936, 0.01917, 0.52085, 0, 0.23496, 0.34939, 0.31657, 0.26675, 0.40702, 0.21681, 0.52402, 0.20228, 0.63119, 0.21862, 0.7364, 0.27129, 0.81899, 0.34212, 0.34462, 0.29727, 0.40263, 0.2673, 0.52553, 0.27184, 0.60711, 0.29507, 0.71253, 0.32665, 0.76105, 0.38786, 0.78945, 0.46437, 0.8072, 0.53105, 0.84626, 0.59553, 0.90543, 0.64472, 0.28662, 0.35496, 0.26413, 0.43584, 0.2452, 0.51673, 0.20969, 0.5681, 0.15407, 0.60854], "triangles": [42, 40, 41, 42, 39, 40, 43, 39, 42, 44, 38, 43, 43, 38, 39, 38, 44, 37, 37, 44, 45, 45, 36, 37, 45, 46, 36, 46, 47, 36, 47, 48, 36, 48, 49, 36, 35, 49, 80, 35, 36, 49, 49, 50, 80, 35, 80, 34, 80, 79, 34, 34, 79, 33, 33, 79, 32, 32, 79, 78, 50, 51, 80, 80, 51, 79, 78, 79, 52, 79, 51, 52, 32, 78, 31, 78, 52, 77, 14, 15, 13, 13, 15, 12, 15, 16, 12, 12, 16, 11, 10, 11, 17, 11, 16, 17, 10, 17, 9, 17, 18, 9, 18, 75, 9, 75, 8, 9, 18, 19, 75, 19, 74, 75, 75, 7, 8, 20, 74, 19, 74, 7, 75, 20, 21, 74, 21, 73, 74, 74, 6, 7, 74, 73, 6, 21, 22, 73, 22, 72, 73, 22, 23, 72, 73, 5, 6, 73, 72, 5, 78, 77, 31, 52, 53, 77, 23, 71, 72, 23, 24, 71, 72, 4, 5, 72, 71, 4, 31, 77, 30, 53, 59, 77, 77, 76, 30, 77, 59, 76, 53, 54, 59, 71, 24, 70, 71, 65, 4, 65, 3, 4, 24, 25, 70, 71, 70, 65, 30, 76, 29, 26, 69, 25, 25, 69, 70, 29, 76, 66, 66, 76, 60, 76, 59, 60, 59, 54, 60, 3, 65, 64, 29, 66, 28, 27, 68, 26, 26, 68, 69, 54, 55, 60, 65, 70, 64, 3, 64, 2, 70, 69, 64, 28, 67, 27, 28, 66, 67, 27, 67, 68, 66, 60, 67, 69, 63, 64, 69, 68, 63, 67, 61, 68, 61, 62, 68, 68, 62, 63, 64, 1, 2, 64, 63, 1, 67, 60, 61, 55, 56, 60, 60, 56, 61, 63, 0, 1, 63, 62, 0, 56, 57, 61, 61, 57, 62, 57, 58, 62, 62, 58, 0], "vertices": [2, 6, 223.09, -23.53, 0.95997, 32, -185.52, 26.54, 0.04003, 2, 6, 203.35, -71.97, 0.97356, 32, -205.26, -21.9, 0.02644, 2, 6, 177.5, -103.54, 0.99214, 32, -231.11, -53.47, 0.00786, 1, 6, 133.07, -82.97, 1, 2, 6, 113.94, -90.54, 0.69703, 55, 6.1, 22.63, 0.30297, 3, 6, 93.56, -93.7, 0.41104, 55, 26.59, 20.25, 0.57335, 56, -11.36, 24.98, 0.01561, 3, 6, 73.19, -97.24, 0.11897, 55, 47.16, 18.23, 0.33588, 56, 7.59, 16.7, 0.54515, 3, 55, 64.57, 19.49, 0.00818, 56, 24.54, 12.52, 0.97796, 57, -12.59, 12.59, 0.01386, 2, 56, 45.08, 11.76, 0.08407, 57, 7.95, 11.71, 0.91593, 2, 57, 29.05, 12.49, 0.81463, 58, -9.27, 9.09, 0.18537, 1, 58, 11.4, 14.99, 1, 2, 58, 27.73, 12.01, 0.793, 59, -7.24, 9.69, 0.207, 2, 58, 43.26, 4.71, 0.00491, 59, 9.87, 11.15, 0.99509, 1, 59, 22.4, 6.27, 1, 1, 59, 24.42, -9.41, 1, 2, 58, 37.42, -12.57, 0.00592, 59, 13.46, -6.73, 0.99408, 2, 58, 26.34, -7.72, 0.51211, 59, 1.44, -8.08, 0.48789, 4, 57, 39.66, -13.19, 0.04112, 58, 12.69, -7.94, 0.95553, 59, -10.26, -15.11, 0.00145, 32, -409.63, -60.64, 0.0019, 3, 57, 26.59, -12.48, 0.73919, 58, 0.99, -13.8, 0.25607, 32, -398.37, -53.97, 0.00474, 3, 56, 44.31, -14.88, 0.19001, 57, 7.04, -14.92, 0.79859, 32, -383.47, -41.07, 0.0114, 3, 56, 25, -17.79, 0.93389, 57, -12.29, -17.72, 0.04658, 32, -368.96, -28, 0.01953, 4, 6, 55.28, -64.24, 0.0368, 55, 55.62, -18.35, 0.26816, 56, 4.34, -20.7, 0.66843, 32, -353.33, -14.18, 0.0266, 5, 6, 70.11, -50.1, 0.28335, 55, 37.56, -28.03, 0.58436, 56, -15.84, -24.33, 0.0959, 60, 27.02, 127.49, 0, 32, -338.5, -0.03, 0.03639, 5, 6, 87.05, -44.55, 0.52894, 55, 19.75, -28.86, 0.42944, 56, -33.03, -19.62, 0.00329, 60, 10.91, 119.86, 0, 32, -321.56, 5.52, 0.03834, 4, 6, 109.43, -42.07, 0.94416, 55, -2.48, -25.28, 0.01991, 60, -10.99, 114.59, 0, 32, -299.18, 8, 0.03593, 2, 6, 119.59, -34.25, 0.96422, 32, -289.02, 15.82, 0.03578, 2, 6, 130.17, -16.45, 0.9652, 32, -278.44, 33.62, 0.0348, 2, 6, 132.77, 11.12, 0.96178, 32, -275.84, 61.19, 0.03822, 2, 6, 131.27, 38.65, 0.96943, 32, -277.34, 88.72, 0.03057, 2, 6, 126.57, 48.01, 0.96876, 32, -282.04, 98.08, 0.03124, 3, 6, 114.32, 54.04, 0.9406, 60, -3.79, 18.63, 0.02482, 32, -294.29, 104.11, 0.03458, 5, 6, 88.33, 54.57, 0.56513, 55, -7.92, -124.04, 0, 60, 22.06, 21.37, 0.39682, 61, -30.13, 11.73, 0.00066, 32, -320.28, 104.63, 0.03739, 4, 6, 61.34, 56.72, 0.19347, 60, 49.11, 22.61, 0.3957, 61, -5.16, 22.19, 0.37432, 32, -347.27, 106.79, 0.03652, 4, 60, 71.92, 19.02, 0.03362, 61, 17.5, 26.66, 0.93828, 62, -26.82, 32.33, 0.00036, 32, -369.46, 113.21, 0.02773, 3, 61, 40.01, 23.73, 0.79932, 62, -5.28, 25.18, 0.18537, 32, -388.11, 126.13, 0.0153, 5, 61, 62.94, 17.39, 0.06495, 62, 16.03, 14.62, 0.92749, 63, -10.2, 28.07, 0.00142, 65, -17.12, 67.08, 0, 32, -405.59, 142.28, 0.00614, 4, 62, 34.46, 10.1, 0.65109, 63, 0.81, 12.63, 0.34603, 65, -26.34, 50.51, 0, 32, -421.99, 151.8, 0.00288, 5, 62, 42.83, 25.14, 0.03825, 63, 16.97, 18.56, 0.89936, 64, -3.29, 23, 0.06088, 65, -14.05, 38.46, 2e-05, 32, -434.26, 139.73, 0.00151, 3, 63, 36.84, 9.54, 0.07349, 64, 9.76, 5.51, 0.92646, 65, -13.68, 16.64, 5e-05, 3, 63, 53.2, 16.77, 0, 64, 27.58, 3.96, 0.67374, 65, -0.12, 4.97, 0.32626, 2, 63, 52.58, 36.19, 0, 65, 17.15, 13.86, 1, 2, 64, 59.65, 11.85, 0, 65, 30.51, -7.37, 1, 1, 65, 9.55, -11.01, 1, 1, 64, 21.49, -5.75, 1, 2, 63, 33.34, -4.5, 0.46655, 64, -0.08, -5.1, 0.53345, 2, 62, 64.8, -0.66, 0.02236, 63, 16.78, -15.32, 0.97764, 2, 62, 59.82, -20.47, 0.38662, 63, 0.08, -27.09, 0.61338, 2, 62, 44.24, -30.69, 0.74396, 63, -18.4, -24.66, 0.25604, 2, 62, 25.64, -31.5, 0.95905, 63, -33.02, -13.13, 0.04095, 2, 61, 58.08, -21.37, 0.21581, 62, 3.92, -22.52, 0.78419, 2, 61, 36.85, -19.02, 0.93944, 62, -16.48, -16.19, 0.06056, 3, 6, 58.87, 104.36, 0.04322, 60, 57.53, -24.34, 0.06936, 61, 18.88, -19.02, 0.88741, 3, 6, 77.64, 98.35, 0.32471, 60, 38.16, -20.74, 0.43868, 61, -0.56, -22.28, 0.23661, 3, 6, 98.55, 94.5, 0.6264, 60, 16.92, -19.53, 0.37029, 61, -20.91, -28.44, 0.00331, 1, 6, 126.33, 87.82, 1, 2, 6, 170.11, 109.69, 0.99274, 32, -238.5, 159.75, 0.00726, 2, 6, 195.85, 83.85, 0.97514, 32, -212.76, 133.92, 0.02486, 2, 6, 220.91, 41.69, 0.95997, 32, -187.7, 91.76, 0.04003, 2, 6, 228.01, 8.17, 0.95679, 32, -180.6, 58.24, 0.04321, 2, 6, 119.79, 83.44, 0.99696, 32, -288.82, 133.51, 0.00304, 2, 6, 145.54, 61.75, 0.97416, 32, -263.07, 111.82, 0.02584, 2, 6, 161.55, 37.22, 0.96885, 32, -247.06, 87.28, 0.03115, 2, 6, 167.21, 4.89, 0.96479, 32, -241.4, 54.96, 0.03521, 2, 6, 163.47, -25.08, 0.96748, 32, -245.14, 24.99, 0.03252, 2, 6, 148.78, -54.93, 0.97549, 32, -259.83, -4.86, 0.02451, 2, 6, 128.38, -78.71, 0.99739, 32, -280.23, -28.64, 0.00261, 2, 6, 136.67, 53.6, 0.96879, 32, -271.94, 103.66, 0.03121, 2, 6, 146.32, 37.84, 0.96588, 32, -262.29, 87.91, 0.03412, 2, 6, 146.3, 3.64, 0.96, 32, -262.31, 53.71, 0.04, 2, 6, 140.21, -19.3, 0.96057, 32, -268.4, 30.77, 0.03943, 4, 6, 131.87, -48.95, 0.95875, 55, -22.27, -12.66, 0.00794, 60, -34.11, 118.61, 0, 32, -276.74, 1.12, 0.03331, 4, 6, 113.99, -63.16, 0.86888, 55, -1.25, -3.74, 0.10301, 60, -18.15, 134.94, 0, 32, -294.62, -13.09, 0.02811, 5, 6, 91.29, -71.96, 0.46838, 55, 22.97, -1.32, 0.49532, 56, -21.45, 5.58, 0.00986, 60, 3.27, 146.52, 0, 32, -317.32, -21.89, 0.02644, 5, 6, 71.43, -77.68, 0.18504, 55, 43.64, -1.1, 0.35345, 56, -1.73, -0.59, 0.43987, 60, 22.25, 154.69, 0, 32, -337.18, -27.61, 0.02164, 6, 6, 52.46, -89.29, 0.03664, 55, 65.02, 5.04, 0.07322, 56, 20.5, -1.36, 0.85011, 57, -16.7, -1.27, 0.02422, 60, 39.61, 168.59, 0, 32, -356.15, -39.22, 0.01582, 6, 6, 38.32, -106.31, 0.00468, 55, 83.19, 17.67, 0.00935, 56, 41.68, 5.04, 0.20135, 57, 4.52, 5.02, 0.77897, 60, 51.51, 187.25, 0, 32, -370.29, -56.25, 0.00564, 3, 6, 118.68, 69.02, 0.96387, 60, -6.24, 3.22, 0.01031, 32, -289.93, 119.09, 0.02583, 5, 6, 94.11, 74.31, 0.60146, 55, -18.75, -141.53, 0, 60, 18.8, 1.06, 0.37074, 61, -26.22, -8.47, 0.00192, 32, -314.5, 124.38, 0.02588, 4, 6, 69.57, 78.61, 0.2558, 60, 43.68, -0.13, 0.40932, 61, -2.44, -1.04, 0.31039, 32, -339.04, 128.68, 0.02449, 5, 6, 53.73, 87.86, 0.05456, 60, 60.56, -7.33, 0.09831, 61, 15.88, -2, 0.8315, 62, -33.85, 4.5, 0.00012, 32, -354.88, 137.93, 0.01552, 5, 6, 40.96, 102.83, 0.00402, 60, 75.11, -20.58, 0.00725, 61, 34.09, -9.44, 0.89634, 62, -17.37, -6.26, 0.08569, 32, -367.65, 152.9, 0.0067], "hull": 59, "edges": [0, 116, 0, 2, 2, 4, 6, 8, 12, 14, 18, 20, 20, 22, 26, 28, 32, 34, 34, 36, 64, 66, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 92, 94, 100, 102, 110, 112, 112, 114, 114, 116, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 84, 86, 86, 88, 88, 90, 90, 92, 98, 100, 102, 104, 104, 106, 106, 108, 108, 110, 62, 64, 94, 96, 96, 98, 82, 84, 4, 6, 8, 10, 10, 12, 44, 46, 46, 48, 48, 50, 50, 52, 42, 44, 40, 42, 36, 38, 38, 40, 14, 16, 16, 18, 22, 24, 24, 26, 28, 30, 30, 32, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130], "width": 278, "height": 301}}, "head": {"head": {"type": "mesh", "uvs": [0.6763, 0.01823, 0.85268, 0.0554, 0.93287, 0.20585, 0.95919, 0.2926, 0.98315, 0.39405, 0.99274, 0.4781, 0.98801, 0.57159, 0.94225, 0.67908, 0.87066, 0.83578, 0.79201, 0.89298, 0.60762, 0.97049, 0.52712, 0.99614, 0.42039, 0.99533, 0.33985, 0.96912, 0.15953, 0.86656, 0.10544, 0.80847, 0.04885, 0.66381, 0.0136, 0.57673, 0.00628, 0.45566, 0.01518, 0.36853, 0.0397, 0.25188, 0.08508, 0.15479, 0.1385, 0.06907, 0.28667, 0.01524, 0.46186, 0.00231, 0.1064, 0.35494, 0.14752, 0.33882, 0.19818, 0.33414, 0.38393, 0.38927, 0.29032, 0.36378, 0.24388, 0.35, 0.33749, 0.37861, 0.4111, 0.38407, 0.40376, 0.35338, 0.23122, 0.30346, 0.31749, 0.32842, 0.27435, 0.31594, 0.36062, 0.3409, 0.18937, 0.30242, 0.15119, 0.31438, 0.10713, 0.3409, 0.58514, 0.36482, 0.57193, 0.40279, 0.61451, 0.40539, 0.7988, 0.35962, 0.84799, 0.36743, 0.91334, 0.38199, 0.91261, 0.37263, 0.86635, 0.34142, 0.81422, 0.3253, 0.77531, 0.32374, 0.67802, 0.34012, 0.70372, 0.37965, 0.65838, 0.39252, 0.752, 0.36886, 0.72703, 0.33037, 0.63048, 0.35195, 0.25818, 0.10659, 0.44502, 0.104, 0.61271, 0.11433, 0.78488, 0.15555, 0.8067, 0.2406, 0.60099, 0.2344, 0.417, 0.22304, 0.20462, 0.21607, 0.60342, 0.50408, 0.62038, 0.48117, 0.65431, 0.4579, 0.69989, 0.441, 0.75342, 0.43988, 0.8006, 0.44551, 0.83611, 0.45827, 0.86473, 0.47855, 0.63417, 0.50821, 0.6718, 0.51534, 0.71791, 0.52247, 0.77621, 0.52285, 0.8165, 0.51159, 0.84618, 0.49694, 0.89655, 0.46482, 0.85325, 0.43999, 0.80445, 0.42441, 0.74808, 0.42016, 0.69388, 0.42642, 0.64321, 0.44792, 0.61062, 0.4692, 0.58106, 0.50279, 0.59763, 0.51291, 0.67256, 0.53482, 0.75779, 0.54699, 0.84577, 0.53774, 0.90213, 0.49781, 0.38413, 0.50239, 0.31333, 0.51748, 0.23635, 0.53501, 0.15937, 0.52673, 0.11699, 0.49818, 0.10052, 0.46205, 0.13082, 0.45111, 0.16168, 0.43289, 0.20323, 0.42285, 0.25116, 0.42231, 0.30028, 0.43061, 0.34016, 0.45205, 0.36146, 0.47347, 0.37315, 0.49538, 0.35058, 0.49922, 0.31172, 0.50382, 0.26743, 0.50934, 0.21299, 0.50943, 0.16877, 0.49618, 0.13975, 0.47426, 0.39114, 0.48983, 0.37148, 0.4659, 0.34489, 0.4443, 0.30881, 0.42265, 0.25999, 0.40586, 0.19991, 0.40306, 0.15033, 0.41449, 0.10682, 0.4327, 0.43945, 0.45437, 0.44412, 0.50561, 0.43362, 0.56759, 0.40445, 0.61057, 0.43945, 0.64032, 0.50362, 0.64115, 0.53979, 0.61387, 0.51529, 0.57007, 0.51179, 0.50726, 0.52462, 0.45602, 0.38711, 0.59837, 0.35992, 0.62204, 0.35837, 0.65781, 0.40187, 0.68037, 0.54483, 0.68312, 0.58212, 0.66166, 0.5829, 0.62369, 0.55182, 0.59837, 0.44084, 0.68112, 0.50525, 0.68236, 0.44781, 0.60981, 0.49563, 0.61117, 0.90093, 0.56957, 0.85971, 0.67959, 0.69555, 0.8656, 0.31136, 0.76634, 0.32392, 0.77079, 0.35218, 0.75124, 0.39457, 0.72594, 0.43107, 0.71347, 0.46915, 0.72825, 0.5084, 0.7163, 0.55118, 0.7327, 0.58358, 0.75367, 0.62198, 0.78161, 0.63775, 0.77952, 0.64992, 0.76507, 0.68328, 0.77813, 0.6562, 0.79787, 0.64324, 0.79287, 0.62637, 0.79954, 0.5966, 0.82982, 0.5514, 0.85542, 0.5039, 0.86265, 0.44428, 0.86232, 0.39024, 0.85342, 0.34677, 0.82409, 0.32221, 0.78837, 0.3018, 0.77808, 0.28531, 0.77947, 0.30886, 0.75417, 0.281, 0.75695, 0.30298, 0.76946, 0.32574, 0.78142, 0.38815, 0.78058, 0.42308, 0.76585, 0.46665, 0.77113, 0.50943, 0.76974, 0.53377, 0.77808, 0.54829, 0.78781, 0.62091, 0.78975, 0.64328, 0.78531, 0.40326, 0.77252, 0.41892, 0.79293, 0.46837, 0.79682, 0.52489, 0.79487, 0.39686, 0.80952, 0.44448, 0.82052, 0.54853, 0.81539, 0.50192, 0.82139, 0.58352, 0.9308, 0.51641, 0.95165, 0.4312, 0.95164, 0.09752, 0.5632, 0.13934, 0.67735, 0.26656, 0.86124, 0.36101, 0.92637, 0.38348, 0.88312, 0.43713, 0.87436, 0.51007, 0.8754, 0.55953, 0.88728, 0.24182, 0.62736, 0.71603, 0.63416], "triangles": [58, 23, 24, 57, 22, 23, 57, 23, 58, 59, 24, 0, 58, 24, 59, 21, 22, 57, 60, 0, 1, 59, 0, 60, 60, 1, 2, 64, 21, 57, 63, 57, 58, 64, 57, 63, 62, 58, 59, 62, 59, 60, 63, 58, 62, 61, 60, 2, 62, 60, 61, 20, 21, 64, 61, 2, 3, 64, 39, 20, 63, 36, 64, 61, 55, 62, 3, 48, 61, 33, 63, 62, 40, 19, 20, 4, 47, 3, 117, 30, 116, 118, 26, 117, 101, 117, 116, 116, 29, 115, 102, 101, 116, 100, 117, 101, 118, 117, 100, 82, 54, 81, 83, 52, 82, 115, 102, 116, 119, 25, 118, 19, 25, 119, 99, 118, 100, 119, 118, 99, 69, 82, 81, 81, 45, 80, 68, 83, 82, 68, 82, 69, 114, 115, 28, 102, 115, 114, 70, 69, 81, 70, 81, 80, 84, 53, 83, 84, 83, 68, 98, 119, 99, 103, 102, 114, 129, 120, 32, 18, 19, 119, 67, 84, 68, 71, 70, 80, 97, 18, 119, 97, 119, 98, 79, 80, 46, 79, 46, 4, 72, 71, 80, 120, 113, 114, 103, 114, 113, 85, 43, 84, 85, 84, 67, 129, 42, 85, 104, 103, 113, 111, 98, 99, 97, 98, 111, 79, 4, 5, 79, 72, 80, 66, 85, 67, 114, 28, 120, 112, 113, 120, 104, 113, 112, 105, 104, 112, 110, 99, 100, 111, 99, 110, 78, 71, 72, 91, 79, 5, 72, 79, 91, 78, 72, 91, 96, 97, 111, 96, 111, 110, 104, 107, 103, 106, 104, 105, 92, 105, 112, 86, 129, 85, 86, 85, 66, 107, 102, 103, 106, 107, 104, 65, 86, 66, 128, 121, 120, 129, 128, 120, 112, 120, 121, 92, 112, 121, 86, 128, 129, 73, 66, 67, 65, 66, 73, 108, 101, 102, 108, 102, 107, 109, 100, 101, 109, 101, 108, 110, 100, 109, 77, 70, 71, 77, 71, 78, 87, 86, 65, 74, 67, 68, 73, 67, 74, 93, 107, 106, 108, 107, 93, 92, 93, 106, 92, 106, 105, 75, 68, 69, 74, 68, 75, 69, 76, 75, 70, 76, 69, 70, 77, 76, 95, 96, 110, 95, 110, 109, 88, 74, 75, 73, 74, 88, 87, 65, 73, 88, 87, 73, 94, 109, 108, 94, 108, 93, 95, 109, 94, 90, 77, 78, 90, 78, 91, 89, 75, 76, 88, 75, 89, 90, 89, 76, 90, 76, 77, 18, 96, 17, 96, 18, 97, 193, 96, 95, 122, 92, 121, 127, 122, 121, 142, 90, 91, 127, 128, 86, 127, 86, 87, 127, 121, 128, 6, 91, 5, 142, 91, 6, 193, 17, 96, 137, 127, 87, 137, 87, 88, 130, 92, 122, 93, 92, 130, 141, 140, 122, 123, 130, 122, 140, 123, 122, 127, 141, 122, 126, 141, 127, 137, 126, 127, 131, 93, 130, 131, 130, 123, 94, 93, 131, 136, 137, 88, 126, 137, 136, 201, 94, 131, 202, 88, 89, 136, 88, 202, 124, 123, 140, 125, 141, 126, 124, 140, 141, 125, 124, 141, 132, 201, 131, 135, 126, 136, 135, 136, 202, 125, 126, 135, 16, 17, 193, 193, 201, 194, 95, 201, 193, 94, 201, 95, 16, 193, 194, 7, 142, 6, 142, 89, 90, 143, 142, 7, 142, 202, 89, 143, 202, 142, 132, 123, 124, 123, 132, 131, 133, 132, 124, 138, 124, 125, 133, 124, 138, 134, 139, 125, 138, 125, 139, 135, 134, 125, 149, 133, 138, 151, 139, 134, 148, 132, 133, 148, 133, 149, 150, 138, 139, 150, 139, 151, 149, 138, 150, 152, 134, 135, 151, 134, 152, 132, 170, 201, 147, 132, 148, 201, 171, 194, 156, 153, 135, 152, 135, 153, 132, 147, 170, 170, 171, 201, 202, 156, 135, 157, 156, 202, 175, 148, 149, 175, 149, 150, 182, 147, 148, 146, 145, 170, 172, 171, 170, 172, 170, 145, 177, 151, 152, 178, 177, 152, 150, 151, 177, 176, 175, 150, 147, 146, 170, 177, 176, 150, 175, 182, 148, 153, 178, 152, 172, 169, 171, 143, 157, 202, 168, 169, 172, 156, 154, 153, 174, 147, 182, 173, 146, 147, 174, 173, 147, 172, 145, 146, 173, 172, 146, 155, 154, 156, 181, 155, 156, 181, 156, 157, 179, 178, 153, 179, 153, 154, 173, 168, 172, 167, 168, 173, 180, 179, 154, 180, 154, 155, 180, 155, 181, 159, 180, 181, 183, 182, 175, 183, 175, 176, 174, 182, 183, 185, 177, 178, 185, 178, 179, 184, 176, 177, 184, 177, 185, 183, 176, 184, 158, 181, 157, 159, 181, 158, 160, 180, 159, 15, 16, 194, 195, 15, 194, 194, 169, 195, 186, 174, 183, 188, 179, 180, 161, 188, 180, 185, 179, 188, 187, 183, 184, 186, 183, 187, 189, 184, 185, 189, 185, 188, 187, 184, 189, 174, 167, 173, 166, 174, 186, 166, 167, 174, 160, 161, 180, 8, 143, 7, 144, 157, 143, 165, 166, 186, 165, 186, 187, 162, 188, 161, 189, 188, 162, 171, 169, 194, 164, 165, 187, 164, 187, 189, 163, 189, 162, 164, 189, 163, 8, 144, 143, 158, 157, 144, 14, 15, 195, 198, 165, 164, 199, 163, 162, 164, 163, 199, 198, 164, 199, 197, 166, 165, 197, 165, 198, 200, 162, 161, 199, 162, 200, 9, 144, 8, 197, 195, 166, 169, 168, 167, 167, 166, 169, 166, 195, 169, 197, 196, 195, 144, 190, 200, 144, 200, 161, 160, 158, 161, 159, 158, 160, 144, 161, 158, 192, 197, 198, 196, 197, 192, 191, 199, 200, 191, 200, 190, 192, 198, 199, 191, 192, 199, 13, 195, 196, 13, 196, 192, 14, 195, 13, 10, 190, 144, 191, 190, 10, 10, 144, 9, 12, 13, 192, 11, 191, 10, 12, 192, 191, 11, 12, 191, 38, 39, 64, 27, 38, 34, 39, 38, 27, 39, 40, 20, 26, 39, 27, 26, 40, 39, 25, 40, 26, 25, 19, 40, 26, 27, 117, 25, 26, 118, 36, 34, 64, 38, 64, 34, 35, 36, 63, 30, 34, 36, 27, 34, 30, 29, 36, 35, 30, 36, 29, 29, 35, 31, 117, 27, 30, 116, 30, 29, 37, 35, 63, 33, 37, 63, 41, 33, 62, 31, 35, 37, 32, 33, 41, 28, 37, 33, 28, 33, 32, 31, 37, 28, 115, 29, 31, 28, 115, 31, 120, 28, 32, 56, 62, 51, 56, 41, 62, 53, 56, 51, 42, 32, 41, 43, 41, 56, 43, 56, 53, 42, 41, 43, 83, 53, 52, 84, 43, 53, 42, 129, 32, 85, 42, 43, 49, 50, 61, 50, 55, 61, 51, 62, 55, 44, 50, 49, 54, 55, 50, 54, 50, 44, 52, 51, 55, 52, 55, 54, 53, 51, 52, 82, 52, 54, 54, 44, 81, 61, 48, 49, 45, 44, 49, 48, 45, 49, 47, 48, 3, 45, 48, 47, 4, 46, 47, 45, 47, 46, 81, 44, 45, 80, 45, 46], "vertices": [2, 6, 150.45, -17.09, 0.97492, 32, -258.16, 32.98, 0.02508, 2, 6, 144.26, -41.34, 0.99, 32, -264.35, 8.73, 0.01, 2, 6, 115.83, -53.38, 0.99, 32, -292.78, -3.31, 0.01, 2, 6, 99.32, -57.61, 0.99, 32, -309.29, -7.54, 0.01, 2, 6, 79.99, -61.63, 0.99, 32, -328.62, -11.57, 0.01, 2, 6, 63.92, -63.57, 0.99, 32, -344.69, -13.51, 0.01, 2, 6, 45.95, -63.64, 0.99, 32, -362.66, -13.57, 0.01, 2, 6, 25.09, -58.24, 0.99, 32, -383.52, -8.17, 0.01, 2, 6, -5.36, -49.69, 0.99, 32, -413.97, 0.38, 0.01, 2, 6, -16.76, -39.44, 0.984, 32, -425.37, 10.63, 0.016, 2, 6, -32.61, -14.97, 0.97095, 32, -441.22, 35.1, 0.02905, 2, 6, -37.97, -4.22, 0.96684, 32, -446.58, 45.85, 0.03316, 2, 6, -38.38, 10.29, 0.96527, 32, -446.99, 60.36, 0.03473, 2, 6, -33.79, 21.43, 0.96999, 32, -442.4, 71.5, 0.03001, 2, 6, -15.08, 46.71, 0.98613, 32, -423.69, 96.78, 0.01387, 2, 6, -4.22, 54.5, 0.99, 32, -412.83, 104.57, 0.01, 2, 6, 23.23, 63.29, 0.99, 32, -385.38, 113.36, 0.01, 2, 6, 39.74, 68.74, 0.99, 32, -368.87, 118.81, 0.01, 2, 6, 62.93, 70.65, 0.99, 32, -345.68, 120.72, 0.01, 2, 6, 79.69, 70.1, 0.99, 32, -328.92, 120.17, 0.01, 2, 6, 102.2, 67.65, 0.99, 32, -306.41, 117.72, 0.01, 2, 6, 121.08, 62.22, 0.99, 32, -287.53, 112.29, 0.01, 2, 6, 137.81, 55.61, 0.99, 32, -270.8, 105.68, 0.01, 2, 6, 148.93, 35.88, 0.97684, 32, -259.68, 85.95, 0.02316, 2, 6, 152.35, 12.17, 0.96805, 32, -256.26, 62.24, 0.03195, 5, 25, 67.79, -35.29, 0.00067, 28, -26.23, -12.43, 0.00663, 27, -10.1, -10.88, 0.00582, 26, -0.91, -2.22, 0.9686, 33, -286.19, 107.88, 0.01828, 5, 25, 61.41, -35.62, 7e-05, 28, -22.37, -7.34, 0.00182, 27, -5.94, -6.03, 0.01015, 26, 5.37, -1.01, 0.97929, 33, -282.87, 102.41, 0.00868, 4, 28, -16.43, -3.73, 0.00394, 27, 0.19, -2.77, 0.33752, 26, 12.2, -2.29, 0.65628, 33, -281.7, 95.56, 0.00225, 5, 23, 62.04, 32.91, 0.00015, 25, 36.76, -12.81, 0.00682, 28, 10.96, -3.21, 0.98292, 27, 27.56, -3.84, 0.00108, 26, 32.94, -20.18, 0.00902, 3, 28, -2.66, -3.88, 0.22183, 27, 13.93, -3.71, 0.74136, 26, 22.35, -11.58, 0.03681, 3, 28, -9.51, -4.01, 0.02808, 27, 7.08, -3.45, 0.8656, 26, 17.17, -7.11, 0.10632, 5, 23, 68.62, 33.75, 1e-05, 25, 41.54, -17.42, 8e-05, 28, 4.36, -3.89, 0.88953, 27, 20.93, -4.13, 0.09126, 26, 27.57, -16.28, 0.01912, 4, 23, 59.12, 30.43, 8e-05, 25, 33, -12.09, 0.03264, 28, 13.94, -0.8, 0.96557, 26, 36.76, -20.37, 0.00171, 4, 24, 53.21, -11.38, 0.00755, 25, 31.32, -17.82, 0.04151, 28, 10.64, 4.18, 0.93047, 27, 27.68, 3.56, 0.02046, 6, 24, 73.45, -26.64, 0.00569, 25, 48.2, -36.73, 0.00031, 28, -14.69, 3.47, 5e-05, 27, 2.34, 4.32, 0.83856, 26, 18.3, 1.92, 0.15299, 33, -275.64, 91.3, 0.0024, 4, 24, 63.33, -19.01, 0.00649, 25, 39.76, -27.28, 0.00344, 28, -2.03, 3.83, 0.27268, 27, 15.01, 3.94, 0.7174, 4, 24, 68.39, -22.83, 0.006, 25, 43.98, -32, 0.00121, 28, -8.36, 3.65, 0.00978, 27, 8.67, 4.13, 0.98302, 4, 24, 58.27, -15.2, 0.00718, 25, 35.54, -22.55, 0.01012, 28, 4.31, 4.01, 0.87964, 27, 21.34, 3.75, 0.10305, 4, 24, 78.91, -28.29, 0.00333, 27, -3.06, 2.51, 0.23297, 26, 12.95, 3.87, 0.76065, 33, -275.66, 97, 0.00305, 3, 24, 84.51, -27.39, 0.00109, 26, 7.3, 3.3, 0.98981, 33, -278.16, 102.1, 0.0091, 3, 24, 91.6, -24, 1e-05, 26, 0.02, 0.31, 0.98131, 33, -283.49, 107.88, 0.01867, 4, 24, 29.92, -2.96, 0.013, 25, 10.11, -5.03, 0.91651, 28, 34.1, 12.14, 0.06397, 27, 51.55, 10.14, 0.00653, 4, 23, 37.81, 24.34, 0.00129, 25, 14.92, 0.73, 0.98598, 28, 35.4, 4.74, 0.01244, 26, 56.44, -30.57, 0.00029, 4, 23, 32.36, 22.32, 0.01096, 24, 28.05, 5.59, 0.01359, 25, 9.94, 3.72, 0.97514, 26, 61.8, -32.84, 0.00031, 4, 23, 13.44, 3.68, 0.4886, 24, 1.57, 3.49, 0.49933, 25, -16.44, 6.82, 0.01014, 33, -283.37, 13.75, 0.00194, 4, 23, 6.75, 2.18, 0.95485, 24, -4.51, 6.64, 0.0306, 25, -21.79, 11.1, 0.00769, 33, -284.6, 7, 0.00686, 4, 23, -2.48, 0.92, 0.96956, 24, -12.39, 11.62, 0.00218, 25, -28.55, 17.51, 0.00898, 33, -287.05, -1.99, 0.01928, 4, 23, -1.62, -0.67, 0.97902, 25, -29.25, 15.85, 0.00183, 27, 93.79, 24.33, 2e-05, 33, -285.25, -1.82, 0.01913, 4, 23, 6.62, -3.4, 0.98869, 28, 67.27, 31.7, 1e-05, 27, 85.8, 27.74, 0.00083, 33, -279.52, 4.71, 0.01047, 5, 23, 14.36, -3.17, 0.60861, 24, -2.14, -2.35, 0.38384, 28, 59.53, 31.66, 0.00084, 27, 78.08, 28.16, 0.0026, 33, -276.7, 11.91, 0.0041, 5, 23, 19.27, -1.19, 0.08002, 24, 2.9, -3.99, 0.91093, 28, 54.57, 29.8, 0.00263, 27, 73.02, 26.58, 0.00446, 33, -276.61, 17.21, 0.00197, 4, 24, 16.5, -4.32, 0.7943, 25, -3.32, -3.75, 0.18656, 28, 43.74, 21.58, 0.01211, 27, 61.72, 19, 0.00704, 3, 23, 23.49, 12.68, 0.03072, 24, 15.06, 3.9, 0.77162, 25, -3.13, 4.6, 0.19766, 4, 23, 28.01, 17.54, 0.01792, 24, 21.65, 4.72, 0.16129, 25, 3.49, 4.12, 0.82076, 26, 68.23, -32.34, 3e-05, 3, 23, 18.44, 8, 0.0762, 24, 8.18, 3.58, 0.88569, 25, -9.94, 5.62, 0.03811, 5, 24, 9.58, -4.43, 0.98414, 25, -10.13, -2.51, 0.00299, 28, 49.08, 25.98, 0.00593, 27, 67.31, 23.09, 0.00608, 33, -278.14, 23.72, 0.00086, 4, 24, 23.33, -3.78, 0.12055, 25, 3.49, -4.54, 0.84626, 28, 38.74, 16.89, 0.02587, 27, 56.46, 14.61, 0.00732, 2, 6, 131.25, 39.06, 0.97068, 32, -277.36, 89.13, 0.02932, 2, 6, 132.75, 13.69, 0.96247, 32, -275.86, 63.76, 0.03753, 2, 6, 131.67, -9.18, 0.96278, 32, -276.94, 40.89, 0.03722, 2, 6, 124.68, -32.89, 0.97138, 32, -283.93, 17.18, 0.02862, 2, 6, 108.48, -36.49, 0.96663, 32, -300.13, 13.58, 0.03337, 2, 6, 108.57, -8.49, 0.96, 32, -300.04, 41.58, 0.04, 2, 6, 109.76, 16.6, 0.96, 32, -298.85, 66.67, 0.04, 2, 6, 109.96, 45.51, 0.9664, 32, -298.65, 95.58, 0.0336, 2, 6, 56.84, -10.86, 0.96, 32, -351.77, 39.2, 0.04, 2, 6, 61.33, -13, 0.96, 32, -347.28, 37.07, 0.04, 2, 6, 65.98, -17.43, 0.96, 32, -342.63, 32.64, 0.04, 2, 6, 69.46, -23.5, 0.96, 32, -339.15, 26.57, 0.04, 2, 6, 69.97, -30.76, 0.96, 32, -338.64, 19.31, 0.04, 2, 6, 69.14, -37.22, 0.96193, 32, -339.47, 12.85, 0.03807, 2, 6, 66.88, -42.14, 0.9643, 32, -341.73, 7.93, 0.0357, 2, 6, 63.14, -46.18, 0.96635, 32, -345.47, 3.89, 0.03365, 2, 6, 56.22, -15.07, 0.96, 32, -352.39, 34.99, 0.04, 2, 6, 55.05, -20.24, 0.96, 32, -353.56, 29.83, 0.04, 2, 6, 53.93, -26.56, 0.96, 32, -354.68, 23.51, 0.04, 2, 6, 54.17, -34.49, 0.96034, 32, -354.44, 15.58, 0.03966, 2, 6, 56.55, -39.88, 0.96311, 32, -352.06, 10.19, 0.03689, 2, 6, 59.52, -43.8, 0.96518, 32, -349.09, 6.27, 0.03482, 2, 6, 65.95, -50.4, 0.96863, 32, -342.66, -0.33, 0.03137, 2, 6, 70.48, -44.33, 0.96575, 32, -338.13, 5.74, 0.03425, 2, 6, 73.21, -37.58, 0.9624, 32, -335.4, 12.49, 0.0376, 2, 6, 73.72, -29.89, 0.96, 32, -334.89, 20.18, 0.04, 2, 6, 72.23, -22.57, 0.96, 32, -336.38, 27.5, 0.04, 2, 6, 67.83, -15.85, 0.96, 32, -340.78, 34.22, 0.04, 2, 6, 63.57, -11.58, 0.96, 32, -345.04, 38.49, 0.04, 2, 6, 56.97, -7.82, 0.96, 32, -351.64, 42.25, 0.04, 2, 6, 55.12, -10.14, 0.96, 32, -353.49, 39.92, 0.04, 2, 6, 51.32, -20.49, 0.96, 32, -357.29, 29.58, 0.04, 2, 6, 49.44, -32.17, 0.96, 32, -359.17, 17.9, 0.04, 2, 6, 51.69, -44.05, 0.96547, 32, -356.92, 6.02, 0.03453, 2, 6, 59.65, -51.41, 0.96919, 32, -348.96, -1.34, 0.03081, 2, 6, 55.99, 18.95, 0.96, 32, -352.62, 69.02, 0.04, 2, 6, 52.72, 28.46, 0.96, 32, -355.89, 78.52, 0.04, 2, 6, 48.94, 38.78, 0.96, 32, -359.67, 88.85, 0.04, 2, 6, 50.12, 49.31, 0.96359, 32, -358.49, 99.38, 0.03641, 2, 6, 55.37, 55.28, 0.96715, 32, -353.24, 105.35, 0.03285, 2, 6, 62.21, 57.79, 0.96801, 32, -346.4, 107.86, 0.03199, 2, 6, 64.47, 53.76, 0.96533, 32, -344.14, 103.83, 0.03467, 2, 6, 68.13, 49.7, 0.96257, 32, -340.48, 99.77, 0.03743, 2, 6, 70.28, 44.13, 0.96, 32, -338.33, 94.2, 0.04, 2, 6, 70.64, 37.62, 0.96, 32, -337.97, 87.69, 0.04, 2, 6, 69.31, 30.89, 0.96, 32, -339.3, 80.96, 0.04, 2, 6, 65.41, 25.31, 0.96, 32, -343.2, 75.37, 0.04, 2, 6, 61.42, 22.25, 0.96, 32, -347.19, 72.32, 0.04, 2, 6, 57.28, 20.49, 0.96, 32, -351.33, 70.56, 0.04, 2, 6, 56.42, 23.53, 0.96, 32, -352.19, 73.6, 0.04, 2, 6, 55.33, 28.78, 0.96, 32, -353.28, 78.85, 0.04, 2, 6, 54.03, 34.75, 0.96, 32, -354.58, 84.82, 0.04, 2, 6, 53.72, 42.15, 0.96, 32, -354.89, 92.22, 0.04, 2, 6, 56.03, 48.26, 0.96276, 32, -352.58, 98.33, 0.03724, 2, 6, 60.08, 52.37, 0.96487, 32, -348.53, 102.44, 0.03513, 2, 6, 58.44, 18.09, 0.96, 32, -350.17, 68.16, 0.04, 2, 6, 62.92, 20.94, 0.96, 32, -345.69, 71.01, 0.04, 2, 6, 66.93, 24.72, 0.96, 32, -341.68, 74.79, 0.04, 2, 6, 70.89, 29.79, 0.96, 32, -337.73, 79.86, 0.04, 2, 6, 73.85, 36.55, 0.96, 32, -334.76, 86.62, 0.04, 2, 6, 74.06, 44.74, 0.96066, 32, -334.55, 94.8, 0.03934, 2, 6, 71.6, 51.39, 0.96386, 32, -337.01, 101.45, 0.03614, 2, 6, 67.88, 57.16, 0.96723, 32, -340.73, 107.23, 0.03277, 2, 6, 65.5, 11.79, 0.958, 32, -343.11, 61.86, 0.042, 2, 6, 55.7, 10.77, 0.956, 32, -352.91, 60.84, 0.044, 2, 6, 43.75, 11.73, 0.954, 32, -364.86, 61.8, 0.046, 2, 6, 35.35, 15.37, 0.954, 32, -373.26, 65.44, 0.046, 2, 6, 29.83, 10.39, 0.954, 32, -378.78, 60.45, 0.046, 2, 6, 30.01, 1.66, 0.954, 32, -378.6, 51.73, 0.046, 2, 6, 35.44, -3.05, 0.954, 32, -373.17, 47.02, 0.046, 2, 6, 43.71, 0.61, 0.954, 32, -364.9, 50.68, 0.046, 2, 6, 55.74, 1.56, 0.956, 32, -352.87, 51.63, 0.044, 2, 6, 65.64, 0.21, 0.958, 32, -342.97, 50.28, 0.042, 2, 6, 37.59, 17.82, 0.96, 32, -371.02, 67.88, 0.04, 2, 6, 32.91, 21.33, 0.96, 32, -375.7, 71.4, 0.04, 2, 6, 26.04, 21.27, 0.96, 32, -382.57, 71.34, 0.04, 2, 6, 21.94, 15.19, 0.96, 32, -386.67, 65.26, 0.04, 2, 6, 22.18, -4.26, 0.96, 32, -386.43, 45.81, 0.04, 2, 6, 26.5, -9.16, 0.96, 32, -382.11, 40.91, 0.04, 2, 6, 33.79, -8.98, 0.96, 32, -374.82, 41.09, 0.04, 2, 6, 38.48, -4.57, 0.96, 32, -370.13, 45.5, 0.04, 2, 6, 22.01, 9.89, 0.96, 32, -386.6, 59.96, 0.04, 2, 6, 22.11, 1.13, 0.96, 32, -386.5, 51.19, 0.04, 2, 6, 35.72, 9.48, 0.951, 32, -372.89, 59.55, 0.049, 2, 6, 35.72, 2.97, 0.951, 32, -372.89, 53.04, 0.049, 2, 6, 45.88, -51.79, 0.96974, 32, -362.74, -1.72, 0.03026, 2, 6, 24.55, -47.02, 0.96912, 32, -384.06, 3.05, 0.03088, 2, 6, -12.02, -26.12, 0.96654, 32, -420.63, 23.95, 0.03346, 2, 6, 4.96, 26.84, 0.96186, 32, -403.65, 76.91, 0.03814, 2, 6, 4.18, 25.1, 0.96163, 32, -404.43, 75.17, 0.03837, 2, 6, 8.08, 21.41, 0.96105, 32, -400.53, 71.47, 0.03895, 2, 6, 13.16, 15.84, 0.96015, 32, -395.45, 65.91, 0.03985, 2, 6, 15.75, 10.97, 0.96, 32, -392.86, 61.04, 0.04, 2, 6, 13.12, 5.68, 0.96, 32, -395.49, 55.75, 0.04, 2, 6, 15.62, 0.44, 0.96, 32, -392.99, 50.51, 0.04, 2, 6, 12.7, -5.5, 0.96042, 32, -395.91, 44.57, 0.03958, 2, 6, 8.85, -10.06, 0.96107, 32, -399.76, 40.01, 0.03893, 2, 6, 3.7, -15.49, 0.96186, 32, -404.91, 34.58, 0.03814, 2, 6, 4.18, -17.62, 0.96217, 32, -404.43, 32.45, 0.03783, 2, 6, 7.02, -19.16, 0.9624, 32, -401.59, 30.91, 0.0376, 2, 6, 4.69, -23.79, 0.96308, 32, -403.92, 26.28, 0.03692, 2, 6, 0.76, -20.26, 0.96256, 32, -407.85, 29.81, 0.03744, 2, 6, 1.65, -18.46, 0.96228, 32, -406.96, 31.61, 0.03772, 2, 6, 0.28, -16.22, 0.96197, 32, -408.33, 33.85, 0.03803, 2, 6, -5.69, -12.4, 0.96142, 32, -414.3, 37.66, 0.03858, 2, 6, -10.84, -6.46, 0.96054, 32, -419.45, 43.61, 0.03946, 2, 6, -12.48, -0.06, 0.96, 32, -421.09, 50.01, 0.04, 2, 6, -12.74, 8.05, 0.96, 32, -421.35, 58.12, 0.04, 2, 6, -11.32, 15.46, 0.96053, 32, -419.93, 65.53, 0.03947, 2, 6, -5.93, 21.59, 0.96125, 32, -414.54, 71.66, 0.03875, 2, 6, 0.79, 25.2, 0.96169, 32, -407.82, 75.27, 0.03831, 2, 6, 2.66, 28.05, 0.96206, 32, -405.95, 78.12, 0.03794, 2, 6, 2.3, 30.28, 0.96238, 32, -406.31, 80.35, 0.03762, 2, 6, 7.28, 27.27, 0.96187, 32, -401.33, 77.34, 0.03813, 2, 6, 6.6, 31.04, 0.9624, 32, -402.01, 81.1, 0.0376, 2, 6, 4.32, 27.95, 0.96202, 32, -404.29, 78.02, 0.03798, 2, 6, 2.15, 24.77, 0.96162, 32, -406.46, 74.84, 0.03838, 2, 6, 2.64, 16.3, 0.96044, 32, -405.97, 66.36, 0.03956, 2, 6, 5.66, 11.66, 0.96, 32, -402.95, 61.73, 0.04, 2, 6, 4.88, 5.7, 0.96, 32, -403.73, 55.77, 0.04, 2, 6, 5.37, -0.1, 0.96, 32, -403.24, 49.96, 0.04, 2, 6, 3.9, -3.47, 0.96009, 32, -404.71, 46.59, 0.03991, 2, 6, 2.11, -5.52, 0.96038, 32, -406.5, 44.55, 0.03962, 2, 6, 2.13, -15.4, 0.96183, 32, -406.48, 34.66, 0.03817, 2, 6, 3.1, -18.41, 0.96228, 32, -405.51, 31.66, 0.03772, 2, 6, 4.27, 14.3, 0.96014, 32, -404.34, 64.37, 0.03986, 2, 6, 0.44, 12.02, 0.96, 32, -408.17, 62.09, 0.04, 2, 6, -0.04, 5.27, 0.96, 32, -408.65, 55.34, 0.04, 2, 6, 0.63, -2.4, 0.96, 32, -407.98, 47.67, 0.04, 2, 6, -2.86, 14.89, 0.9603, 32, -411.47, 64.96, 0.0397, 2, 6, -4.72, 8.34, 0.96, 32, -413.33, 58.41, 0.04, 2, 6, -3.18, -5.76, 0.96045, 32, -411.79, 44.31, 0.03955, 2, 6, -4.58, 0.53, 0.96, 32, -413.19, 50.59, 0.04, 2, 6, -25.13, -11.39, 0.96271, 32, -433.74, 38.68, 0.03729, 2, 6, -29.49, -2.43, 0.96, 32, -438.1, 47.64, 0.04, 2, 6, -29.94, 9.15, 0.96, 32, -438.55, 59.22, 0.04, 2, 6, 42.79, 57.44, 0.96878, 32, -365.82, 107.51, 0.03122, 2, 6, 21.11, 50.89, 0.96551, 32, -387.5, 100.96, 0.03449, 2, 6, -13.48, 32.21, 0.96553, 32, -422.1, 82.28, 0.03447, 2, 6, -25.47, 18.88, 0.9622, 32, -434.08, 68.95, 0.0378, 2, 6, -17.06, 16.15, 0.96, 32, -425.67, 66.22, 0.04, 2, 6, -15.09, 8.93, 0.96, 32, -423.7, 59, 0.04, 2, 6, -14.9, -0.99, 0.96, 32, -423.51, 49.08, 0.04, 2, 6, -16.91, -7.8, 0.96, 32, -425.52, 42.27, 0.04, 2, 6, 31.25, 37.34, 0.96, 32, -377.36, 87.41, 0.04, 2, 6, 32.49, -27.15, 0.96, 32, -376.12, 22.92, 0.04], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 16, 18, 22, 24, 28, 30, 38, 40, 44, 46, 46, 48, 30, 32, 32, 34, 12, 14, 14, 16, 34, 36, 36, 38, 40, 42, 42, 44, 4, 6, 6, 8, 8, 10, 10, 12, 24, 26, 26, 28, 18, 20, 20, 22, 50, 52, 52, 54, 54, 60, 60, 58, 56, 62, 62, 58, 64, 56, 64, 66, 68, 72, 72, 70, 66, 74, 74, 70, 68, 76, 76, 78, 78, 80, 80, 50, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 86, 106, 106, 104, 88, 108, 108, 104, 100, 110, 110, 102, 82, 112, 112, 102, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 130, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 144, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 196, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 260, 262, 262, 264, 264, 266, 268, 270, 270, 272, 272, 274, 266, 276, 268, 278, 278, 276, 280, 282, 284, 286, 288, 286, 290, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 344, 346, 346, 348, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 348, 364, 364, 350, 348, 366, 366, 368, 368, 370, 370, 358, 372, 374, 374, 378, 378, 376, 288, 380, 382, 384, 386, 388, 388, 390, 390, 392, 394, 396, 396, 398, 398, 400], "width": 136, "height": 192}}, "head2": {"head": {"type": "mesh", "uvs": [0.13934, 0.67735, 0.24182, 0.62736, 0.35992, 0.62204, 0.35837, 0.65781, 0.40187, 0.68037, 0.44084, 0.68112, 0.50525, 0.68236, 0.54483, 0.68312, 0.58212, 0.66166, 0.5829, 0.62369, 0.71603, 0.63416, 0.85971, 0.67959, 0.87066, 0.83578, 0.79201, 0.89298, 0.60762, 0.97049, 0.52712, 0.99614, 0.42039, 0.99533, 0.33985, 0.96912, 0.15953, 0.86656, 0.10544, 0.80847, 0.69555, 0.8656, 0.31136, 0.76634, 0.32392, 0.77079, 0.35218, 0.75124, 0.39457, 0.72594, 0.43107, 0.71347, 0.46915, 0.72825, 0.5084, 0.7163, 0.55118, 0.7327, 0.58358, 0.75367, 0.62198, 0.78161, 0.63775, 0.77952, 0.64992, 0.76507, 0.68328, 0.77813, 0.6562, 0.79787, 0.64324, 0.79287, 0.62637, 0.79954, 0.5966, 0.82982, 0.5514, 0.85542, 0.5039, 0.86265, 0.44428, 0.86232, 0.39024, 0.85342, 0.34677, 0.82409, 0.32221, 0.78837, 0.3018, 0.77808, 0.28531, 0.77947, 0.30886, 0.75417, 0.281, 0.75695, 0.30298, 0.76946, 0.32574, 0.78142, 0.38815, 0.78058, 0.42308, 0.76585, 0.46665, 0.77113, 0.50943, 0.76974, 0.53377, 0.77808, 0.54829, 0.78781, 0.62091, 0.78975, 0.64328, 0.78531, 0.40326, 0.77252, 0.41892, 0.79293, 0.46837, 0.79682, 0.52489, 0.79487, 0.39686, 0.80952, 0.44448, 0.82052, 0.54853, 0.81539, 0.50192, 0.82139, 0.58352, 0.9308, 0.51641, 0.95165, 0.4312, 0.95164, 0.26656, 0.86124, 0.36101, 0.92637, 0.38348, 0.88312, 0.43713, 0.87436, 0.51007, 0.8754, 0.55953, 0.88728], "triangles": [3, 1, 2, 8, 9, 10, 25, 4, 5, 27, 6, 7, 24, 3, 4, 24, 4, 25, 26, 5, 6, 26, 6, 27, 25, 5, 26, 28, 7, 8, 27, 7, 28, 3, 46, 1, 23, 3, 24, 1, 47, 0, 32, 29, 8, 28, 8, 29, 3, 23, 46, 46, 47, 1, 10, 32, 8, 33, 32, 10, 51, 24, 25, 51, 25, 26, 58, 23, 24, 22, 21, 46, 48, 47, 46, 48, 46, 21, 53, 27, 28, 54, 53, 28, 26, 27, 53, 52, 51, 26, 23, 22, 46, 53, 52, 26, 51, 58, 24, 29, 54, 28, 48, 45, 47, 11, 33, 10, 44, 45, 48, 32, 30, 29, 50, 23, 58, 49, 22, 23, 50, 49, 23, 48, 21, 22, 49, 48, 22, 31, 30, 32, 57, 31, 32, 57, 32, 33, 55, 54, 29, 55, 29, 30, 49, 44, 48, 43, 44, 49, 56, 55, 30, 56, 30, 31, 56, 31, 57, 35, 56, 57, 59, 58, 51, 59, 51, 52, 50, 58, 59, 61, 53, 54, 61, 54, 55, 60, 52, 53, 60, 53, 61, 59, 52, 60, 34, 57, 33, 35, 57, 34, 36, 56, 35, 69, 19, 0, 0, 45, 69, 62, 50, 59, 64, 55, 56, 37, 64, 56, 61, 55, 64, 63, 59, 60, 62, 59, 63, 65, 60, 61, 65, 61, 64, 63, 60, 65, 50, 43, 49, 42, 50, 62, 42, 43, 50, 36, 37, 56, 20, 33, 11, 41, 42, 62, 41, 62, 63, 38, 64, 37, 65, 64, 38, 47, 45, 0, 40, 41, 63, 40, 63, 65, 39, 65, 38, 40, 65, 39, 12, 20, 11, 34, 33, 20, 18, 19, 69, 72, 41, 40, 73, 39, 38, 40, 39, 73, 72, 40, 73, 71, 42, 41, 71, 41, 72, 74, 38, 37, 73, 38, 74, 13, 20, 12, 71, 69, 42, 45, 44, 43, 43, 42, 45, 42, 69, 45, 71, 70, 69, 20, 66, 74, 20, 74, 37, 36, 34, 37, 35, 34, 36, 20, 37, 34, 68, 71, 72, 70, 71, 68, 67, 73, 74, 67, 74, 66, 68, 72, 73, 67, 68, 73, 17, 69, 70, 17, 70, 68, 18, 69, 17, 14, 66, 20, 67, 66, 14, 14, 20, 13, 16, 17, 68, 15, 67, 14, 16, 68, 67, 15, 16, 67], "vertices": [2, 6, 21.11, 50.89, 0.96551, 32, -387.5, 100.96, 0.03449, 2, 6, 31.25, 37.34, 0.96, 32, -377.36, 87.41, 0.04, 2, 6, 32.91, 21.33, 0.96, 32, -375.7, 71.4, 0.04, 2, 6, 26.04, 21.27, 0.96, 32, -382.57, 71.34, 0.04, 2, 6, 21.94, 15.19, 0.96, 32, -386.67, 65.26, 0.04, 2, 6, 22.01, 9.89, 0.96, 32, -386.6, 59.96, 0.04, 2, 6, 22.11, 1.13, 0.96, 32, -386.5, 51.19, 0.04, 2, 6, 22.18, -4.26, 0.96, 32, -386.43, 45.81, 0.04, 2, 6, 26.5, -9.16, 0.96, 32, -382.11, 40.91, 0.04, 2, 6, 33.79, -8.98, 0.96, 32, -374.82, 41.09, 0.04, 2, 6, 32.49, -27.15, 0.96, 32, -376.12, 22.92, 0.04, 2, 6, 24.55, -47.02, 0.96912, 32, -384.06, 3.05, 0.03088, 2, 6, -5.36, -49.69, 0.99, 32, -413.97, 0.38, 0.01, 2, 6, -16.76, -39.44, 0.984, 32, -425.37, 10.63, 0.016, 2, 6, -32.61, -14.97, 0.97095, 32, -441.22, 35.1, 0.02905, 2, 6, -37.97, -4.22, 0.96684, 32, -446.58, 45.85, 0.03316, 2, 6, -38.38, 10.29, 0.96527, 32, -446.99, 60.36, 0.03473, 2, 6, -33.79, 21.43, 0.96999, 32, -442.4, 71.5, 0.03001, 2, 6, -15.08, 46.71, 0.98613, 32, -423.69, 96.78, 0.01387, 2, 6, -4.22, 54.5, 0.99, 32, -412.83, 104.57, 0.01, 2, 6, -12.02, -26.12, 0.96654, 32, -420.63, 23.95, 0.03346, 2, 6, 4.96, 26.84, 0.96186, 32, -403.65, 76.91, 0.03814, 2, 6, 4.18, 25.1, 0.96163, 32, -404.43, 75.17, 0.03837, 2, 6, 8.08, 21.41, 0.96105, 32, -400.53, 71.47, 0.03895, 2, 6, 13.16, 15.84, 0.96015, 32, -395.45, 65.91, 0.03985, 2, 6, 15.75, 10.97, 0.96, 32, -392.86, 61.04, 0.04, 2, 6, 13.12, 5.68, 0.96, 32, -395.49, 55.75, 0.04, 2, 6, 15.62, 0.44, 0.96, 32, -392.99, 50.51, 0.04, 2, 6, 12.7, -5.5, 0.96042, 32, -395.91, 44.57, 0.03958, 2, 6, 8.85, -10.06, 0.96107, 32, -399.76, 40.01, 0.03893, 2, 6, 3.7, -15.49, 0.96186, 32, -404.91, 34.58, 0.03814, 2, 6, 4.18, -17.62, 0.96217, 32, -404.43, 32.45, 0.03783, 2, 6, 7.02, -19.16, 0.9624, 32, -401.59, 30.91, 0.0376, 2, 6, 4.69, -23.79, 0.96308, 32, -403.92, 26.28, 0.03692, 2, 6, 0.76, -20.26, 0.96256, 32, -407.85, 29.81, 0.03744, 2, 6, 1.65, -18.46, 0.96228, 32, -406.96, 31.61, 0.03772, 2, 6, 0.28, -16.22, 0.96197, 32, -408.33, 33.85, 0.03803, 2, 6, -5.69, -12.4, 0.96142, 32, -414.3, 37.66, 0.03858, 2, 6, -10.84, -6.46, 0.96054, 32, -419.45, 43.61, 0.03946, 2, 6, -12.48, -0.06, 0.96, 32, -421.09, 50.01, 0.04, 2, 6, -12.74, 8.05, 0.96, 32, -421.35, 58.12, 0.04, 2, 6, -11.32, 15.46, 0.96053, 32, -419.93, 65.53, 0.03947, 2, 6, -5.93, 21.59, 0.96125, 32, -414.54, 71.66, 0.03875, 2, 6, 0.79, 25.2, 0.96169, 32, -407.82, 75.27, 0.03831, 2, 6, 2.66, 28.05, 0.96206, 32, -405.95, 78.12, 0.03794, 2, 6, 2.3, 30.28, 0.96238, 32, -406.31, 80.35, 0.03762, 2, 6, 7.28, 27.27, 0.96187, 32, -401.33, 77.34, 0.03813, 2, 6, 6.6, 31.04, 0.9624, 32, -402.01, 81.1, 0.0376, 2, 6, 4.32, 27.95, 0.96202, 32, -404.29, 78.02, 0.03798, 2, 6, 2.15, 24.77, 0.96162, 32, -406.46, 74.84, 0.03838, 2, 6, 2.64, 16.3, 0.96044, 32, -405.97, 66.36, 0.03956, 2, 6, 5.66, 11.66, 0.96, 32, -402.95, 61.73, 0.04, 2, 6, 4.88, 5.7, 0.96, 32, -403.73, 55.77, 0.04, 2, 6, 5.37, -0.1, 0.96, 32, -403.24, 49.96, 0.04, 2, 6, 3.9, -3.47, 0.96009, 32, -404.71, 46.59, 0.03991, 2, 6, 2.11, -5.52, 0.96038, 32, -406.5, 44.55, 0.03962, 2, 6, 2.13, -15.4, 0.96183, 32, -406.48, 34.66, 0.03817, 2, 6, 3.1, -18.41, 0.96228, 32, -405.51, 31.66, 0.03772, 2, 6, 4.27, 14.3, 0.96014, 32, -404.34, 64.37, 0.03986, 2, 6, 0.44, 12.02, 0.96, 32, -408.17, 62.09, 0.04, 2, 6, -0.04, 5.27, 0.96, 32, -408.65, 55.34, 0.04, 2, 6, 0.63, -2.4, 0.96, 32, -407.98, 47.67, 0.04, 2, 6, -2.86, 14.89, 0.9603, 32, -411.47, 64.96, 0.0397, 2, 6, -4.72, 8.34, 0.96, 32, -413.33, 58.41, 0.04, 2, 6, -3.18, -5.76, 0.96045, 32, -411.79, 44.31, 0.03955, 2, 6, -4.58, 0.53, 0.96, 32, -413.19, 50.59, 0.04, 2, 6, -25.13, -11.39, 0.96271, 32, -433.74, 38.68, 0.03729, 2, 6, -29.49, -2.43, 0.96, 32, -438.1, 47.64, 0.04, 2, 6, -29.94, 9.15, 0.96, 32, -438.55, 59.22, 0.04, 2, 6, -13.48, 32.21, 0.96553, 32, -422.1, 82.28, 0.03447, 2, 6, -25.47, 18.88, 0.9622, 32, -434.08, 68.95, 0.0378, 2, 6, -17.06, 16.15, 0.96, 32, -425.67, 66.22, 0.04, 2, 6, -15.09, 8.93, 0.96, 32, -423.7, 59, 0.04, 2, 6, -14.9, -0.99, 0.96, 32, -423.51, 49.08, 0.04, 2, 6, -16.91, -7.8, 0.96, 32, -425.52, 42.27, 0.04], "hull": 20, "edges": [24, 26, 30, 32, 36, 38, 32, 34, 34, 36, 26, 28, 28, 30, 4, 6, 6, 8, 14, 16, 16, 18, 8, 10, 14, 12, 12, 10, 40, 22, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 100, 116, 116, 102, 100, 118, 118, 120, 120, 122, 122, 110, 124, 126, 126, 130, 130, 128, 40, 132, 134, 136, 0, 138, 138, 140, 142, 144, 144, 146, 146, 148, 38, 0, 0, 2, 2, 4, 18, 20, 20, 22, 22, 24], "width": 136, "height": 192}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.76169, 0.02312, 0.82806, 0.04619, 0.90764, 0.08081, 0.96686, 0.11617, 0.99475, 0.15878, 0.99617, 0.20036, 0.97474, 0.23705, 0.93966, 0.27218, 0.86929, 0.30946, 0.71424, 0.39129, 0.61145, 0.44712, 0.54432, 0.49007, 0.51401, 0.51373, 0.49241, 0.53989, 0.47572, 0.57161, 0.45495, 0.60537, 0.45235, 0.61999, 0.45724, 0.64451, 0.46267, 0.71559, 0.43595, 0.77668, 0.32943, 0.91656, 0.28464, 0.9991, 0.00738, 0.99928, 0.00715, 0.82583, 0.01499, 0.73683, 0.06632, 0.63148, 0.06953, 0.60535, 0.06391, 0.57689, 0.06344, 0.54448, 0.07204, 0.51281, 0.07432, 0.47939, 0.07074, 0.43765, 0.07589, 0.36219, 0.09517, 0.27953, 0.10881, 0.24319, 0.13207, 0.19968, 0.14926, 0.17659, 0.1474, 0.16364, 0.13292, 0.13937, 0.11169, 0.11181, 0.09046, 0.08425, 0.0738, 0.06262, 0.27922, 0.04192, 0.52051, 0.01759, 0.69505, 0, 0.31846, 0.06397, 0.34286, 0.0938, 0.36061, 0.13136, 0.40277, 0.16428, 0.65348, 0.08876, 0.60245, 0.06294, 0.56917, 0.04236, 0.38435, 0.19934, 0.29166, 0.29, 0.23446, 0.37997, 0.69428, 0.12346, 0.73483, 0.16644, 0.62187, 0.30276, 0.71456, 0.2121, 0.39304, 0.49259, 0.39087, 0.52141, 0.38609, 0.55092, 0.36939, 0.58293, 0.34712, 0.61359, 0.21546, 0.48843, 0.19378, 0.51809, 0.18293, 0.54624, 0.18077, 0.5774, 0.1851, 0.61259, 0.53564, 0.38968, 0.33801, 0.24467, 0.66821, 0.25743, 0.18858, 0.64081, 0.13536, 0.71758, 0.17127, 0.91703, 0.3364, 0.72443, 0.33936, 0.64355], "triangles": [22, 74, 21, 22, 23, 74, 21, 74, 20, 20, 74, 19, 19, 74, 75, 23, 73, 74, 75, 74, 73, 24, 73, 23, 19, 75, 18, 24, 25, 73, 18, 76, 17, 18, 75, 76, 75, 73, 76, 73, 72, 76, 73, 25, 72, 76, 16, 17, 76, 63, 16, 76, 72, 63, 25, 68, 72, 72, 68, 63, 25, 26, 68, 16, 63, 15, 63, 62, 15, 63, 68, 62, 26, 67, 68, 68, 67, 62, 15, 62, 14, 26, 27, 67, 62, 61, 14, 62, 67, 61, 67, 66, 61, 67, 27, 66, 27, 28, 66, 14, 61, 13, 61, 66, 60, 61, 60, 13, 60, 66, 65, 66, 28, 65, 28, 29, 65, 13, 60, 12, 65, 64, 60, 60, 59, 12, 60, 64, 59, 65, 29, 64, 12, 59, 11, 29, 30, 64, 11, 59, 10, 59, 69, 10, 69, 59, 54, 54, 59, 64, 54, 64, 31, 64, 30, 31, 10, 69, 9, 31, 32, 54, 8, 9, 57, 9, 69, 57, 57, 69, 53, 69, 54, 53, 54, 32, 53, 32, 33, 53, 57, 71, 8, 8, 71, 7, 53, 70, 57, 57, 70, 71, 33, 34, 53, 53, 34, 70, 7, 71, 6, 70, 52, 71, 71, 58, 6, 71, 52, 58, 34, 35, 70, 70, 35, 52, 6, 58, 5, 52, 48, 58, 58, 56, 5, 58, 48, 56, 56, 4, 5, 48, 55, 56, 56, 3, 4, 56, 55, 3, 48, 47, 55, 47, 49, 55, 47, 46, 49, 55, 2, 3, 55, 49, 2, 46, 50, 49, 49, 1, 2, 49, 50, 1, 50, 0, 1, 50, 45, 51, 45, 43, 51, 45, 42, 43, 50, 51, 0, 51, 44, 0, 51, 43, 44, 35, 36, 52, 52, 36, 48, 48, 36, 47, 36, 37, 47, 37, 38, 47, 38, 39, 47, 39, 46, 47, 39, 40, 46, 40, 45, 46, 50, 46, 45, 40, 41, 45, 41, 42, 45], "vertices": [2, 8, 15.81, 65.77, 0.69061, 12, -94.41, 7.91, 0.30939, 2, 8, 52.58, 83.99, 0.32764, 12, -63.51, 34.9, 0.67236, 2, 8, 91.93, 99.02, 0.07379, 12, -29.29, 59.48, 0.92621, 2, 8, 131.45, 108.93, 0.0031, 12, 6.39, 79.13, 0.9969, 1, 12, 50.83, 92.09, 1, 1, 12, 95.05, 98.37, 1, 1, 12, 134.81, 98.31, 1, 1, 12, 173.37, 94.66, 1, 1, 12, 215.38, 82.61, 1, 1, 12, 307.61, 56.01, 1, 1, 12, 370.44, 38.6, 1, 1, 12, 418.38, 28.15, 1, 1, 12, 444.56, 24.04, 1, 1, 12, 473.13, 22.45, 1, 1, 12, 507.45, 22.85, 1, 1, 12, 544.07, 22.53, 1, 1, 12, 559.71, 23.98, 1, 1, 12, 585.66, 28.68, 1, 1, 12, 661.14, 40.15, 1, 1, 12, 727.05, 42.27, 1, 1, 12, 879.47, 35.92, 1, 1, 12, 968.82, 36.63, 1, 1, 12, 978.18, -31.77, 1, 1, 12, 793.54, -56.56, 1, 1, 12, 698.55, -67.31, 1, 1, 12, 584.7, -69.66, 1, 1, 12, 556.79, -72.59, 1, 1, 12, 526.68, -78.03, 1, 1, 12, 492.19, -82.77, 1, 1, 12, 458.2, -85.16, 1, 1, 12, 422.55, -89.36, 1, 2, 7, 483.09, -57.54, 0.00077, 12, 378.22, -96.2, 0.99923, 2, 7, 402.84, -46.19, 0.02999, 12, 297.73, -105.68, 0.97001, 2, 7, 315.35, -30.38, 0.23272, 12, 209.11, -112.71, 0.76728, 2, 7, 277.05, -22.15, 0.43732, 12, 169.97, -114.52, 0.56268, 2, 7, 231.4, -10.59, 0.78503, 12, 122.88, -114.99, 0.21497, 2, 7, 207.33, -3.25, 0.96855, 12, 97.74, -114.03, 0.03145, 1, 7, 193.47, -1.98, 1, 1, 7, 167.16, -2.31, 1, 1, 7, 137.13, -3.88, 1, 1, 7, 102.71, -6.31, 1, 3, 7, 65.99, -13.97, 0.99916, 8, 29.16, -111.86, 0.00083, 12, -36.2, -160.44, 1e-05, 4, 7, 50.29, 39.55, 0.59552, 8, 13.46, -58.34, 0.35719, 12, -65.03, -112.7, 0.01347, 47, -342.74, 6.76, 0.03382, 3, 8, -4.98, 4.52, 0.97205, 12, -98.89, -56.61, 0.00293, 47, -282.66, 32.89, 0.02502, 2, 8, -17.85, 46.13, 0.89707, 12, -121.95, -19.66, 0.10293, 4, 7, 87.47, 50.92, 0.57852, 8, 50.64, -46.98, 0.28905, 12, -31.98, -92.23, 0.09928, 47, -326.84, -28.71, 0.03315, 4, 7, 125.11, 55.65, 0.58739, 8, 88.28, -42.25, 0.11908, 12, 3.21, -78.05, 0.26481, 47, -317.46, -65.47, 0.02872, 4, 7, 165.67, 55.01, 0.58351, 8, 128.84, -42.88, 0.01749, 12, 42.6, -68.32, 0.37215, 47, -313.04, -105.8, 0.02684, 4, 7, 202.06, 61.03, 0.43814, 8, 165.23, -36.87, 0.0002, 12, 76.25, -53.22, 0.53723, 47, -302.54, -141.16, 0.02443, 4, 7, 129.35, 133.06, 0.01085, 8, 92.53, 35.17, 0.07552, 12, -12.43, -2.11, 0.8861, 47, -240.11, -60.05, 0.02753, 4, 7, 100.26, 123.91, 0.04467, 8, 63.43, 26.01, 0.35789, 12, -38.23, -18.39, 0.56897, 47, -252.82, -32.32, 0.02848, 4, 7, 69.69, 114.64, 0.03691, 8, 32.87, 16.74, 0.69907, 12, -65.41, -35.15, 0.23752, 47, -265.82, -3.15, 0.0265, 3, 7, 238.86, 51.79, 0.38126, 12, 114.19, -52.77, 0.59582, 47, -307.13, -178.82, 0.02292, 3, 7, 332.6, 16.77, 0.12804, 12, 213.76, -62.72, 0.84952, 47, -330.21, -276.19, 0.02243, 3, 7, 426.7, -9.39, 0.01171, 12, 311.42, -64.01, 0.96662, 47, -344.45, -372.81, 0.02166, 2, 12, 23.17, 12.9, 0.97517, 47, -229.95, -97.32, 0.02483, 2, 12, 67.58, 29.04, 0.97465, 47, -219.86, -143.48, 0.02535, 2, 12, 216.43, 20.59, 0.97986, 47, -247.99, -289.89, 0.02014, 2, 12, 116.86, 30.54, 0.97697, 47, -224.91, -192.52, 0.02303, 2, 12, 426.06, -8.82, 0.9826, 47, -304.96, -493.76, 0.0174, 2, 12, 456.81, -5.25, 0.98484, 47, -305.5, -524.71, 0.01516, 2, 12, 488.38, -2.22, 0.98564, 47, -306.69, -556.41, 0.01436, 2, 12, 523.01, -1.78, 0.98603, 47, -310.85, -590.79, 0.01397, 2, 12, 556.39, -2.91, 0.98832, 47, -316.4, -623.72, 0.01168, 2, 12, 427.5, -53.24, 0.97679, 47, -349.18, -489.3, 0.02321, 2, 12, 459.79, -54.37, 0.97826, 47, -354.58, -521.15, 0.02174, 2, 12, 490.11, -53.03, 0.9807, 47, -357.28, -551.38, 0.0193, 2, 12, 523.36, -49.12, 0.98352, 47, -357.82, -584.85, 0.01648, 2, 12, 560.67, -43.03, 0.98568, 47, -356.74, -622.64, 0.01432, 2, 12, 311.8, 11.7, 0.97822, 47, -269.46, -383.24, 0.02178, 3, 7, 285.73, 34.28, 0.25683, 12, 163.97, -57.75, 0.72204, 47, -318.67, -227.5, 0.02113, 2, 12, 166.64, 25.57, 0.97811, 47, -236.45, -241.21, 0.02189, 2, 12, 590.6, -38.15, 0.98474, 47, -355.87, -652.95, 0.01526, 2, 12, 674.08, -40.34, 0.99123, 47, -369.13, -735.4, 0.00877, 1, 12, 885.2, -3.05, 1, 2, 12, 674.73, 10.25, 0.99154, 47, -319.07, -742.77, 0.00846, 2, 12, 588.53, -0.55, 0.9886, 47, -318.33, -655.9, 0.0114], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 72, 74, 74, 76, 80, 82, 76, 78, 78, 80, 68, 70, 70, 72, 4, 6, 6, 8, 10, 12, 12, 14, 66, 68, 64, 66, 62, 64, 60, 62, 56, 58, 58, 60, 54, 56, 50, 52, 52, 54, 48, 50, 32, 34, 34, 36, 26, 28, 28, 30, 16, 18, 18, 20, 44, 46, 82, 84, 72, 94, 84, 86, 86, 88, 106, 108, 128, 108, 114, 138, 138, 118, 104, 140, 140, 106, 114, 142, 142, 116, 144, 146, 146, 148, 148, 150, 150, 152], "width": 249, "height": 1074}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 7.13, "curve": [0.444, 7.13, 0.889, -6.11, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -6.11, "curve": [1.778, -6.11, 2.222, 7.13, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 7.13, "curve": [3.111, 7.13, 3.556, -6.11, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -6.11, "curve": [4.444, -6.11, 4.889, 7.13, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 7.13, "curve": [5.667, 7.13, 6, -6.11, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -6.11, "curve": [6.778, -6.11, 7.222, -2.65, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -2.65, "curve": [8.111, -2.65, 8.556, -6.11, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -6.11, "curve": [9.444, -6.11, 9.889, 7.13, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 7.13, "curve": [10.778, 7.13, 11.222, -6.11, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -6.11, "curve": [12.111, -6.11, 12.556, 7.13, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 7.13}]}, "all": {"translate": [{"y": -14.24, "curve": [0.444, 0, 0.889, 0, 0.444, -14.24, 0.889, 13.43]}, {"time": 1.3333, "y": 13.43, "curve": [1.778, 0, 2.222, 0, 1.778, 13.43, 2.222, -14.24]}, {"time": 2.6667, "y": -14.24, "curve": [3.111, 0, 3.556, 0, 3.111, -14.24, 3.556, 13.43]}, {"time": 4, "y": 13.43, "curve": [4.444, 0, 4.889, 0, 4.444, 13.43, 4.889, -14.24]}, {"time": 5.3333, "y": -14.24, "curve": [5.667, 0, 6, 0, 5.667, -14.24, 6, 13.43]}, {"time": 6.3333, "y": 13.43, "curve": [6.778, 0, 7.222, 0, 6.778, 13.43, 7.222, 6.19]}, {"time": 7.6667, "y": 6.19, "curve": [8.111, 0, 8.556, 0, 8.111, 6.19, 8.556, 13.43]}, {"time": 9, "y": 13.43, "curve": [9.444, 0, 9.889, 0, 9.444, 13.43, 9.889, -14.24]}, {"time": 10.3333, "y": -14.24, "curve": [10.778, 0, 11.222, 0, 10.778, -14.24, 11.222, 13.43]}, {"time": 11.6667, "y": 13.43, "curve": [12.111, 0, 12.556, 0, 12.111, 13.43, 12.556, -14.24]}, {"time": 13, "y": -14.24}]}, "body": {"rotate": [{"value": -2.36, "curve": [0.444, -2.36, 0.889, -7.05]}, {"time": 1.3333, "value": -7.05, "curve": [1.778, -7.05, 2.222, -2.36]}, {"time": 2.6667, "value": -2.36, "curve": [3.111, -2.36, 3.556, -7.05]}, {"time": 4, "value": -7.05, "curve": [4.444, -7.05, 4.889, -2.36]}, {"time": 5.3333, "value": -2.36, "curve": [5.667, -2.36, 6, -7.05]}, {"time": 6.3333, "value": -7.05, "curve": [6.778, -7.05, 7.222, -5.82]}, {"time": 7.6667, "value": -5.82, "curve": [8.111, -5.82, 8.556, -7.05]}, {"time": 9, "value": -7.05, "curve": [9.444, -7.05, 9.889, -2.36]}, {"time": 10.3333, "value": -2.36, "curve": [10.778, -2.36, 11.222, -7.05]}, {"time": 11.6667, "value": -7.05, "curve": [12.111, -7.05, 12.556, -2.36]}, {"time": 13, "value": -2.36}], "translate": [{"y": -12.29, "curve": [0.057, 0, 0.112, 0, 0.057, -12.98, 0.112, -13.5]}, {"time": 0.1667, "y": -13.5, "curve": [0.611, 0, 1.056, 0, 0.611, -13.5, 1.056, 12.27]}, {"time": 1.5, "y": 12.27, "curve": [1.944, 0, 2.389, 0, 1.944, 12.27, 2.389, -13.5]}, {"time": 2.8333, "y": -13.5, "curve": [3.278, 0, 3.722, 0, 3.278, -13.5, 3.722, 12.27]}, {"time": 4.1667, "y": 12.27, "curve": [4.611, 0, 5.056, 0, 4.611, 12.27, 5.056, -13.5]}, {"time": 5.5, "y": -13.5, "curve": [5.833, 0, 6.167, 0, 5.833, -13.5, 6.167, 12.27]}, {"time": 6.5, "y": 12.27, "curve": [6.944, 0, 7.389, 0, 6.944, 12.27, 7.389, 5.54]}, {"time": 7.8333, "y": 5.54, "curve": [8.278, 0, 8.722, 0, 8.278, 5.54, 8.722, 12.27]}, {"time": 9.1667, "y": 12.27, "curve": [9.611, 0, 10.056, 0, 9.611, 12.27, 10.056, -13.5]}, {"time": 10.5, "y": -13.5, "curve": [10.944, 0, 11.389, 0, 10.944, -13.5, 11.389, 12.27]}, {"time": 11.8333, "y": 12.27, "curve": [12.223, 0, 12.613, 0, 12.223, 12.27, 12.613, -7.4]}, {"time": 13, "y": -12.29}], "scale": [{"y": 1.05, "curve": [0.057, 1, 0.112, 1, 0.057, 1.054, 0.112, 1.056]}, {"time": 0.1667, "y": 1.056, "curve": [0.611, 1, 1.056, 1, 0.611, 1.056, 1.056, 0.939]}, {"time": 1.5, "y": 0.939, "curve": [1.944, 1, 2.389, 1, 1.944, 0.939, 2.389, 1.056]}, {"time": 2.8333, "y": 1.056, "curve": [3.278, 1, 3.722, 1, 3.278, 1.056, 3.722, 0.939]}, {"time": 4.1667, "y": 0.939, "curve": [4.611, 1, 5.056, 1, 4.611, 0.939, 5.056, 1.056]}, {"time": 5.5, "y": 1.056, "curve": [5.833, 1, 6.167, 1, 5.833, 1.056, 6.167, 0.939]}, {"time": 6.5, "y": 0.939, "curve": [6.944, 1, 7.389, 1, 6.944, 0.939, 7.389, 0.969]}, {"time": 7.8333, "y": 0.969, "curve": [8.278, 1, 8.722, 1, 8.278, 0.969, 8.722, 0.939]}, {"time": 9.1667, "y": 0.939, "curve": [9.611, 1, 10.056, 1, 9.611, 0.939, 10.056, 1.056]}, {"time": 10.5, "y": 1.056, "curve": [10.944, 1, 11.389, 1, 10.944, 1.056, 11.389, 0.939]}, {"time": 11.8333, "y": 0.939, "curve": [12.223, 1, 12.613, 1, 12.223, 0.939, 12.613, 1.028]}, {"time": 13, "y": 1.05}]}, "body2": {"rotate": [{"value": -4.63, "curve": [0.444, -4.63, 0.889, 0.08]}, {"time": 1.3333, "value": 0.08, "curve": [1.778, 0.08, 2.222, -4.63]}, {"time": 2.6667, "value": -4.63, "curve": [3.111, -4.63, 3.556, 0.08]}, {"time": 4, "value": 0.08, "curve": [4.444, 0.08, 4.889, -4.63]}, {"time": 5.3333, "value": -4.63, "curve": [5.667, -4.63, 6, 0.08]}, {"time": 6.3333, "value": 0.08, "curve": [6.778, 0.08, 7.222, -1.15]}, {"time": 7.6667, "value": -1.15, "curve": [8.111, -1.15, 8.556, 0.08]}, {"time": 9, "value": 0.08, "curve": [9.444, 0.08, 9.889, -4.63]}, {"time": 10.3333, "value": -4.63, "curve": [10.778, -4.63, 11.222, 0.08]}, {"time": 11.6667, "value": 0.08, "curve": [12.111, 0.08, 12.556, -4.63]}, {"time": 13, "value": -4.63}], "translate": [{"x": -5.31, "curve": [0.114, -7.28, 0.224, -8.74, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -8.74, "curve": [0.778, -8.74, 1.222, 12.7, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 12.7, "curve": [2.111, 12.7, 2.556, -8.74, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -8.74, "curve": [3.444, -8.74, 3.889, 12.7, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 12.7, "curve": [4.778, 12.7, 5.222, -8.74, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -8.74, "curve": [6, -8.74, 6.333, 12.7, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 12.7, "curve": [7.111, 12.7, 7.556, 7.1, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 7.1, "curve": [8.444, 7.1, 8.889, 12.7, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 12.7, "curve": [9.778, 12.7, 10.222, -8.74, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -8.74, "curve": [11.111, -8.74, 11.556, 12.7, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 12.7, "curve": [12.335, 12.7, 12.67, 0.69, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.31}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.021, 0.778, 1, 1.222, 1.021]}, {"time": 1.6667, "x": 1.021, "y": 1.021, "curve": [2.111, 1.021, 2.556, 1, 2.111, 1.021, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.021, 3.444, 1, 3.889, 1.021]}, {"time": 4.3333, "x": 1.021, "y": 1.021, "curve": [4.778, 1.021, 5.222, 1, 4.778, 1.021, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.021, 6, 1, 6.333, 1.021]}, {"time": 6.6667, "x": 1.021, "y": 1.021, "curve": [7.111, 1.021, 7.556, 1.016, 7.111, 1.021, 7.556, 1.016]}, {"time": 8, "x": 1.016, "y": 1.016, "curve": [8.444, 1.016, 8.889, 1.021, 8.444, 1.016, 8.889, 1.021]}, {"time": 9.3333, "x": 1.021, "y": 1.021, "curve": [9.778, 1.021, 10.222, 1, 9.778, 1.021, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.021, 11.111, 1, 11.556, 1.021]}, {"time": 12, "x": 1.021, "y": 1.021, "curve": [12.335, 1.021, 12.67, 1.009, 12.335, 1.021, 12.67, 1.009]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.98, "curve": [0.168, 1.63, 0.334, 2.17]}, {"time": 0.5, "value": 2.17, "curve": [0.944, 2.17, 1.389, -1.59]}, {"time": 1.8333, "value": -1.59, "curve": [2.278, -1.6, 2.722, 2.17]}, {"time": 3.1667, "value": 2.17, "curve": [3.611, 2.17, 4.056, -1.59]}, {"time": 4.5, "value": -1.59, "curve": [4.944, -1.6, 5.389, 2.17]}, {"time": 5.8333, "value": 2.17, "curve": [6.167, 2.18, 6.5, -4.28]}, {"time": 6.8333, "value": -4.28, "curve": [7.278, -4.28, 7.722, -1.85]}, {"time": 8.1667, "value": -1.85, "curve": [8.611, -1.85, 9.056, -4.28]}, {"time": 9.5, "value": -4.28, "curve": [9.944, -4.28, 10.389, 2.17]}, {"time": 10.8333, "value": 2.17, "curve": [11.278, 2.17, 11.722, -1.59]}, {"time": 12.1667, "value": -1.59, "curve": [12.445, -1.59, 12.724, -0.13]}, {"time": 13, "value": 0.98}]}, "head": {"rotate": [{"value": 0.29, "curve": [0.225, 1.22, 0.446, 2.17]}, {"time": 0.6667, "value": 2.17, "curve": [1.111, 2.17, 1.556, -1.59]}, {"time": 2, "value": -1.59, "curve": [2.444, -1.59, 2.889, 2.17]}, {"time": 3.3333, "value": 2.17, "curve": [3.778, 2.17, 4.222, -1.59]}, {"time": 4.6667, "value": -1.59, "curve": [5.111, -1.59, 5.556, 2.17]}, {"time": 6, "value": 2.17, "curve": [6.333, 2.17, 6.667, -3.71]}, {"time": 7, "value": -3.71, "curve": [7.444, -3.71, 7.889, -0.6]}, {"time": 8.3333, "value": -0.6, "curve": [8.778, -0.6, 9.222, -3.71]}, {"time": 9.6667, "value": -3.71, "curve": [10.111, -3.71, 10.556, 2.17]}, {"time": 11, "value": 2.17, "curve": [11.444, 2.17, 11.889, -1.59]}, {"time": 12.3333, "value": -1.59, "curve": [12.557, -1.59, 12.781, -0.66]}, {"time": 13, "value": 0.29}]}, "tun": {"rotate": [{"value": 1.65, "curve": [0.444, 1.65, 0.889, -3.58]}, {"time": 1.3333, "value": -3.58, "curve": [1.778, -3.58, 2.222, 1.65]}, {"time": 2.6667, "value": 1.65, "curve": [3.111, 1.65, 3.556, -3.58]}, {"time": 4, "value": -3.58, "curve": [4.444, -3.58, 4.889, 1.65]}, {"time": 5.3333, "value": 1.65, "curve": [5.667, 1.65, 6, -3.58]}, {"time": 6.3333, "value": -3.58, "curve": [6.778, -3.58, 7.222, -2.21]}, {"time": 7.6667, "value": -2.21, "curve": [8.111, -2.21, 8.556, -3.58]}, {"time": 9, "value": -3.58, "curve": [9.444, -3.58, 9.889, 1.65]}, {"time": 10.3333, "value": 1.65, "curve": [10.778, 1.65, 11.222, -3.58]}, {"time": 11.6667, "value": -3.58, "curve": [12.111, -3.58, 12.556, 1.65]}, {"time": 13, "value": 1.65}]}, "leg_R2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 1.54]}, {"time": 7.6667, "value": 1.54, "curve": [8.111, 1.54, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.74]}, {"time": 7.6667, "value": 1.74, "curve": [8.111, 1.74, 8.556, 0]}, {"time": 9}]}, "leg_L2": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 2.62]}, {"time": 7.6667, "value": 2.62, "curve": [8.111, 2.62, 8.556, 0]}, {"time": 9}]}, "sh_L": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 1.21]}, {"time": 7.6667, "value": 1.21, "curve": [8.111, 1.21, 8.556, 0]}, {"time": 9}]}, "arm_L": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, -2.08]}, {"time": 7.6667, "value": -2.08, "curve": [8.111, -2.08, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"value": 0.01, "curve": "stepped"}, {"time": 6.3333, "value": 0.01, "curve": [6.778, 0.01, 7.222, 2.44]}, {"time": 7.6667, "value": 2.44, "curve": [8.111, 2.44, 8.556, 0.01]}, {"time": 9, "value": 0.01}]}, "sh_R": {"rotate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, -1.21]}, {"time": 7.6667, "value": -1.21, "curve": [8.111, -1.21, 8.556, 0]}, {"time": 9}]}, "arm_R": {"rotate": [{"value": -0.03, "curve": [0.168, 0.68, 0.334, 1.27]}, {"time": 0.5, "value": 1.27, "curve": [0.944, 1.27, 1.389, -2.84]}, {"time": 1.8333, "value": -2.84, "curve": [2.278, -2.84, 2.722, 1.27]}, {"time": 3.1667, "value": 1.27, "curve": [3.611, 1.27, 4.056, -2.84]}, {"time": 4.5, "value": -2.84, "curve": [4.944, -2.84, 5.389, 1.27]}, {"time": 5.8333, "value": 1.27, "curve": [6.167, 1.27, 6.5, -2.84]}, {"time": 6.8333, "value": -2.84, "curve": [7.278, -2.84, 7.722, -1.76]}, {"time": 8.1667, "value": -1.76, "curve": [8.611, -1.76, 9.056, -2.84]}, {"time": 9.5, "value": -2.84, "curve": [9.944, -2.84, 10.389, 1.27]}, {"time": 10.8333, "value": 1.27, "curve": [11.278, 1.27, 11.722, -2.84]}, {"time": 12.1667, "value": -2.84, "curve": [12.445, -2.84, 12.724, -1.24]}, {"time": 13, "value": -0.03}]}, "arm_R2": {"rotate": [{"value": -0.58, "curve": [0.168, -1.19, 0.334, -1.68]}, {"time": 0.5, "value": -1.68, "curve": [0.944, -1.68, 1.389, 1.78]}, {"time": 1.8333, "value": 1.78, "curve": [2.278, 1.78, 2.722, -1.68]}, {"time": 3.1667, "value": -1.68, "curve": [3.611, -1.68, 4.056, 1.78]}, {"time": 4.5, "value": 1.78, "curve": [4.944, 1.78, 5.389, -1.68]}, {"time": 5.8333, "value": -1.68, "curve": [6.167, -1.68, 6.5, 1.78]}, {"time": 6.8333, "value": 1.78, "curve": [7.278, 1.78, 7.722, 0.88]}, {"time": 8.1667, "value": 0.88, "curve": [8.611, 0.88, 9.056, 1.78]}, {"time": 9.5, "value": 1.78, "curve": [9.944, 1.78, 10.389, -1.68]}, {"time": 10.8333, "value": -1.68, "curve": [11.278, -1.68, 11.722, 1.78]}, {"time": 12.1667, "value": 1.78, "curve": [12.445, 1.78, 12.724, 0.43]}, {"time": 13, "value": -0.58}]}, "arm_R3": {"rotate": [{"value": -0.09, "curve": [0.225, -2.39, 0.446, -4.72]}, {"time": 0.6667, "value": -4.72, "curve": [1.111, -4.72, 1.556, 4.55]}, {"time": 2, "value": 4.55, "curve": [2.444, 4.55, 2.889, -4.72]}, {"time": 3.3333, "value": -4.72, "curve": [3.778, -4.72, 4.222, 4.55]}, {"time": 4.6667, "value": 4.55, "curve": [5.111, 4.55, 5.556, -4.72]}, {"time": 6, "value": -4.72, "curve": [6.333, -4.72, 6.667, 4.55]}, {"time": 7, "value": 4.55, "curve": [7.444, 4.55, 7.889, 2.12]}, {"time": 8.3333, "value": 2.12, "curve": [8.778, 2.12, 9.222, 4.55]}, {"time": 9.6667, "value": 4.55, "curve": [10.111, 4.55, 10.556, -4.72]}, {"time": 11, "value": -4.72, "curve": [11.444, -4.72, 11.889, 4.55]}, {"time": 12.3333, "value": 4.55, "curve": [12.557, 4.55, 12.781, 2.24]}, {"time": 13, "value": -0.09}]}, "arm_R4": {"rotate": [{"value": 1.6, "curve": [0.279, -1.11, 0.556, -4.72]}, {"time": 0.8333, "value": -4.72, "curve": [1.278, -4.72, 1.722, 4.55]}, {"time": 2.1667, "value": 4.55, "curve": [2.611, 4.55, 3.056, -4.72]}, {"time": 3.5, "value": -4.72, "curve": [3.944, -4.72, 4.389, 4.55]}, {"time": 4.8333, "value": 4.55, "curve": [5.278, 4.55, 5.722, -4.72]}, {"time": 6.1667, "value": -4.72, "curve": [6.5, -4.72, 6.833, 4.55]}, {"time": 7.1667, "value": 4.55, "curve": [7.611, 4.55, 8.056, 2.12]}, {"time": 8.5, "value": 2.12, "curve": [8.944, 2.12, 9.389, 4.55]}, {"time": 9.8333, "value": 4.55, "curve": [10.278, 4.55, 10.722, -4.72]}, {"time": 11.1667, "value": -4.72, "curve": [11.611, -4.72, 12.056, 4.55]}, {"time": 12.5, "value": 4.55, "curve": [12.667, 4.55, 12.835, 3.24]}, {"time": 13, "value": 1.6}]}, "arm_L3": {"rotate": [{"value": -1.68, "curve": [0.114, -2.21, 0.224, -2.61]}, {"time": 0.3333, "value": -2.61, "curve": [0.778, -2.61, 1.222, 3.2]}, {"time": 1.6667, "value": 3.2, "curve": [2.111, 3.2, 2.556, -2.61]}, {"time": 3, "value": -2.61, "curve": [3.444, -2.61, 3.889, 3.2]}, {"time": 4.3333, "value": 3.2, "curve": [4.778, 3.2, 5.222, -2.61]}, {"time": 5.6667, "value": -2.61, "curve": [6, -2.61, 6.333, 3.2]}, {"time": 6.6667, "value": 3.2, "curve": [7.111, 3.2, 7.556, -2.61]}, {"time": 8, "value": -2.61, "curve": [8.444, -2.61, 8.889, 3.2]}, {"time": 9.3333, "value": 3.2, "curve": [9.778, 3.2, 10.222, -2.61]}, {"time": 10.6667, "value": -2.61, "curve": [11.111, -2.61, 11.556, 3.2]}, {"time": 12, "value": 3.2, "curve": [12.335, 3.2, 12.67, -0.05]}, {"time": 13, "value": -1.68}]}, "arm_L4": {"rotate": [{"value": -0.76, "curve": [0.168, -1.77, 0.334, -2.61]}, {"time": 0.5, "value": -2.61, "curve": [0.944, -2.61, 1.389, 3.2]}, {"time": 1.8333, "value": 3.2, "curve": [2.278, 3.2, 2.722, -2.61]}, {"time": 3.1667, "value": -2.61, "curve": [3.611, -2.61, 4.056, 3.2]}, {"time": 4.5, "value": 3.2, "curve": [4.944, 3.2, 5.389, -2.61]}, {"time": 5.8333, "value": -2.61, "curve": [6.167, -2.61, 6.5, 3.2]}, {"time": 6.8333, "value": 3.2, "curve": [7.278, 3.2, 7.722, -2.61]}, {"time": 8.1667, "value": -2.61, "curve": [8.611, -2.61, 9.056, 3.2]}, {"time": 9.5, "value": 3.2, "curve": [9.944, 3.2, 10.389, -2.61]}, {"time": 10.8333, "value": -2.61, "curve": [11.278, -2.61, 11.722, 3.2]}, {"time": 12.1667, "value": 3.2, "curve": [12.445, 3.2, 12.724, 0.94]}, {"time": 13, "value": -0.76}]}, "eyebrow_L3": {"translate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 2.6, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": 2.6, "curve": "stepped"}, {"time": 9.7, "x": 2.6, "curve": [9.9, 2.6, 10.1, 0, 9.9, 0, 10.1, 0]}, {"time": 10.3}]}, "eyebrow_L2": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, -6.62]}, {"time": 6.5, "value": -6.62, "curve": "stepped"}, {"time": 9.7, "value": -6.62, "curve": [9.9, -6.62, 10.1, 0]}, {"time": 10.3}]}, "eyebrow_L": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, -6.62]}, {"time": 6.5, "value": -6.62, "curve": "stepped"}, {"time": 9.7, "value": -6.62, "curve": [9.9, -6.62, 10.1, 0]}, {"time": 10.3}]}, "eyebrow_R3": {"translate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 2.6, 6.278, 0, 6.389, 0]}, {"time": 6.5, "x": 2.6, "curve": "stepped"}, {"time": 9.7, "x": 2.6, "curve": [9.9, 2.6, 10.1, 0, 9.9, 0, 10.1, 0]}, {"time": 10.3}]}, "eyebrow_R2": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 3.65]}, {"time": 6.5, "value": 3.65, "curve": "stepped"}, {"time": 9.7, "value": 3.65, "curve": [9.9, 3.65, 10.1, 0]}, {"time": 10.3}]}, "eyebrow_R": {"rotate": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 3.65]}, {"time": 6.5, "value": 3.65, "curve": "stepped"}, {"time": 9.7, "value": 3.65, "curve": [9.9, 3.65, 10.1, 0]}, {"time": 10.3}]}, "eye_L": {"translate": [{"time": 1.6667, "curve": [1.7, 0, 1.733, -2.53, 1.7, 0, 1.733, -0.5]}, {"time": 1.7667, "x": -2.53, "y": -0.5, "curve": "stepped"}, {"time": 2.6667, "x": -2.53, "y": -0.5, "curve": [2.7, -2.53, 2.733, -1.64, 2.7, -0.5, 2.733, 0.99]}, {"time": 2.7667, "x": -1.64, "y": 0.99, "curve": "stepped"}, {"time": 3.2333, "x": -1.64, "y": 0.99, "curve": [3.267, -1.64, 3.3, 0, 3.267, 0.99, 3.3, 0]}, {"time": 3.3333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, 0.02, 6.2, 0, 6.233, -3.45]}, {"time": 6.2667, "x": 0.02, "y": -3.45, "curve": "stepped"}, {"time": 7.1667, "x": 0.02, "y": -3.45, "curve": [7.2, 0.02, 7.233, -0.73, 7.2, -3.45, 7.233, -5.04]}, {"time": 7.2667, "x": -0.73, "y": -5.04, "curve": "stepped"}, {"time": 7.7333, "x": -0.73, "y": -5.04, "curve": [7.767, -0.73, 7.8, -1.63, 7.767, -5.04, 7.8, -3.12]}, {"time": 7.8333, "x": -1.63, "y": -3.12, "curve": "stepped"}, {"time": 8.6667, "x": -1.63, "y": -3.12, "curve": [8.7, -1.63, 8.733, 0.02, 8.7, -3.12, 8.733, -3.45]}, {"time": 8.7667, "x": 0.02, "y": -3.45, "curve": "stepped"}, {"time": 10, "x": 0.02, "y": -3.45, "curve": [10.033, 0.02, 10.067, 0, 10.033, -3.45, 10.067, 0]}, {"time": 10.1}]}, "eye_R": {"translate": [{"time": 1.6667, "curve": [1.7, 0, 1.733, -2.53, 1.7, 0, 1.733, -0.5]}, {"time": 1.7667, "x": -2.53, "y": -0.5, "curve": "stepped"}, {"time": 2.6667, "x": -2.53, "y": -0.5, "curve": [2.7, -2.53, 2.733, -1.64, 2.7, -0.5, 2.733, 0.99]}, {"time": 2.7667, "x": -1.64, "y": 0.99, "curve": "stepped"}, {"time": 3.2333, "x": -1.64, "y": 0.99, "curve": [3.267, -1.64, 3.3, 0, 3.267, 0.99, 3.3, 0]}, {"time": 3.3333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.2, 0, 6.233, 0.02, 6.2, 0, 6.233, -3.45]}, {"time": 6.2667, "x": 0.02, "y": -3.45, "curve": "stepped"}, {"time": 7.1667, "x": 0.02, "y": -3.45, "curve": [7.2, 0.02, 7.233, -0.73, 7.2, -3.45, 7.233, -5.04]}, {"time": 7.2667, "x": -0.73, "y": -5.04, "curve": "stepped"}, {"time": 7.7333, "x": -0.73, "y": -5.04, "curve": [7.767, -0.73, 7.8, -1.63, 7.767, -5.04, 7.8, -3.12]}, {"time": 7.8333, "x": -1.63, "y": -3.12, "curve": "stepped"}, {"time": 8.6667, "x": -1.63, "y": -3.12, "curve": [8.7, -1.63, 8.733, 0.02, 8.7, -3.12, 8.733, -3.45]}, {"time": 8.7667, "x": 0.02, "y": -3.45, "curve": "stepped"}, {"time": 10, "x": 0.02, "y": -3.45, "curve": [10.033, 0.02, 10.067, 0, 10.033, -3.45, 10.067, 0]}, {"time": 10.1}]}, "headround3": {"translate": [{"x": 47.14, "curve": [0.279, -71.58, 0.556, -229.47, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -229.47, "curve": [1.278, -229.47, 1.722, 176.12, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 176.12, "curve": [2.611, 176.12, 3.056, -229.47, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -229.47, "curve": [3.944, -229.47, 4.389, 176.12, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 176.12, "curve": [5.278, 176.12, 5.722, -229.47, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -229.47, "curve": [6.5, -229.47, 6.833, 176.12, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 176.12, "curve": [7.611, 176.12, 8.056, 70.12, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": 70.12, "curve": [8.944, 70.12, 9.389, 176.12, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 176.12, "curve": [10.278, 176.12, 10.722, -229.47, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -229.47, "curve": [11.611, -229.47, 12.056, 176.12, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 176.12, "curve": [12.667, 176.12, 12.835, 118.88, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 47.14}]}, "headround": {"translate": [{"y": -68.22, "curve": [0.336, 0, 0.668, 0, 0.336, -16.06, 0.668, 86.89]}, {"time": 1, "y": 86.89, "curve": [1.444, 0, 1.889, 0, 1.444, 86.89, 1.889, -97.76]}, {"time": 2.3333, "y": -97.76, "curve": [2.778, 0, 3.222, 0, 2.778, -97.76, 3.222, 86.89]}, {"time": 3.6667, "y": 86.89, "curve": [4.111, 0, 4.556, 0, 4.111, 86.89, 4.556, -97.72]}, {"time": 5, "y": -97.76, "curve": [5.444, 0, 5.889, 0, 5.444, -97.81, 5.889, 93.46]}, {"time": 6.3333, "y": 86.89, "curve": [6.667, 0, 7, 0, 6.453, 85.12, 6.577, -280.13]}, {"time": 7.3333, "y": -281.92, "curve": [7.767, 0, 8.2, 0, 7.767, -282.94, 8.2, -198.9]}, {"time": 8.6333, "y": -198.88, "curve": [9.078, 0, 9.522, 0, 9.078, -198.86, 9.522, -281.83]}, {"time": 9.9667, "y": -281.92, "curve": [10.422, 0, 10.878, 0, 10.422, -282.01, 10.878, 86.84]}, {"time": 11.3333, "y": 86.89, "curve": [11.778, 0, 12.222, 0, 11.778, 86.94, 12.222, -97.76]}, {"time": 12.6667, "y": -97.76, "curve": [12.779, 0, 12.892, 0, 12.779, -97.76, 12.892, -85.92]}, {"time": 13, "y": -68.22}]}, "sh_L2": {"translate": [{"x": -4.87, "curve": [0.114, -6.08, 0.224, -6.98, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -6.98, "curve": [0.778, -6.98, 1.222, 6.17, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.17, "curve": [2.111, 6.17, 2.556, -6.98, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -6.98, "curve": [3.444, -6.98, 3.889, 6.17, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.17, "curve": [4.778, 6.17, 5.222, -6.98, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -6.98, "curve": [6, -6.98, 6.333, 6.17, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.17, "curve": [7.111, 6.17, 7.556, 2.74, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 2.74, "curve": [8.444, 2.74, 8.889, 6.17, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.17, "curve": [9.778, 6.17, 10.222, -6.98, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -6.98, "curve": [11.111, -6.98, 11.556, 6.17, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.17, "curve": [12.335, 6.17, 12.67, -1.19, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.87}]}, "sh_R2": {"translate": [{"x": -4.87, "curve": [0.114, -6.08, 0.224, -6.98, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -6.98, "curve": [0.778, -6.98, 1.222, 6.17, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.17, "curve": [2.111, 6.17, 2.556, -6.98, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -6.98, "curve": [3.444, -6.98, 3.889, 6.17, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.17, "curve": [4.778, 6.17, 5.222, -6.98, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -6.98, "curve": [6, -6.98, 6.333, 6.17, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.17, "curve": [7.111, 6.17, 7.556, 2.74, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 2.74, "curve": [8.444, 2.74, 8.889, 6.17, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.17, "curve": [9.778, 6.17, 10.222, -6.98, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -6.98, "curve": [11.111, -6.98, 11.556, 6.17, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.17, "curve": [12.335, 6.17, 12.67, -1.19, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -4.87}]}, "arm_LL": {"rotate": [{"value": 0.5, "curve": "stepped"}, {"time": 6.3333, "value": 0.5, "curve": [6.778, 0.5, 7.222, -0.84]}, {"time": 7.6667, "value": -0.84, "curve": [8.111, -0.84, 8.556, 0.5]}, {"time": 9, "value": 0.5}]}, "arm_LL2": {"rotate": [{"value": -0.54, "curve": "stepped"}, {"time": 6.3333, "value": -0.54, "curve": [6.778, -0.54, 7.222, 1.67]}, {"time": 7.6667, "value": 1.67, "curve": [8.111, 1.67, 8.556, -0.54]}, {"time": 9, "value": -0.54}]}, "arm_L5": {"translate": [{"x": 4.78, "y": 5.16, "curve": [0.114, 5.98, 0.224, 6.87, 0.114, 6.46, 0.224, 7.42]}, {"time": 0.3333, "x": 6.87, "y": 7.42, "curve": [0.778, 6.87, 1.222, -6.22, 0.778, 7.42, 1.222, -6.7]}, {"time": 1.6667, "x": -6.22, "y": -6.7, "curve": [2.111, -6.22, 2.556, 6.87, 2.111, -6.7, 2.556, 7.42]}, {"time": 3, "x": 6.87, "y": 7.42, "curve": [3.444, 6.87, 3.889, -6.22, 3.444, 7.42, 3.889, -6.7]}, {"time": 4.3333, "x": -6.22, "y": -6.7, "curve": [4.778, -6.22, 5.222, 6.87, 4.778, -6.7, 5.222, 7.42]}, {"time": 5.6667, "x": 6.87, "y": 7.42, "curve": [6, 6.87, 6.333, -6.22, 6, 7.42, 6.333, -6.7]}, {"time": 6.6667, "x": -6.22, "y": -6.7, "curve": [7.111, -6.22, 7.556, -2.8, 7.111, -6.7, 7.556, -3.01]}, {"time": 8, "x": -2.8, "y": -3.01, "curve": [8.444, -2.8, 8.889, -6.22, 8.444, -3.01, 8.889, -6.7]}, {"time": 9.3333, "x": -6.22, "y": -6.7, "curve": [9.778, -6.22, 10.222, 6.87, 9.778, -6.7, 10.222, 7.42]}, {"time": 10.6667, "x": 6.87, "y": 7.42, "curve": [11.111, 6.87, 11.556, -6.22, 11.111, 7.42, 11.556, -6.7]}, {"time": 12, "x": -6.22, "y": -6.7, "curve": [12.335, -6.22, 12.67, 1.11, 12.335, -6.7, 12.67, 1.21]}, {"time": 13, "x": 4.78, "y": 5.16}]}, "leg_RR": {"rotate": [{"value": 2.22, "curve": "stepped"}, {"time": 6.3333, "value": 2.22, "curve": [6.778, 2.22, 7.222, 2.15]}, {"time": 7.6667, "value": 2.15, "curve": [8.111, 2.15, 8.556, 2.22]}, {"time": 9, "value": 2.22}]}, "leg_RR2": {"rotate": [{"value": -4.5, "curve": "stepped"}, {"time": 6.3333, "value": -4.5, "curve": [6.778, -4.5, 7.222, 0.57]}, {"time": 7.6667, "value": 0.57, "curve": [8.111, 0.57, 8.556, -4.5]}, {"time": 9, "value": -4.5}]}, "leg_R1": {"translate": [{"curve": [0.444, 0, 0.889, 61.73, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 61.73, "curve": [1.778, 61.73, 2.222, 0, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 61.73, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 61.73, "curve": [4.444, 61.73, 4.889, 0, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "curve": [5.667, 0, 6, 61.73, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 61.73, "curve": [6.778, 61.73, 7.222, 45.6, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 45.6, "curve": [8.111, 45.6, 8.556, 61.73, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 61.73, "curve": [9.444, 61.73, 9.889, 0, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "curve": [10.778, 0, 11.222, 61.73, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 61.73, "curve": [12.111, 61.73, 12.556, 0, 12.111, 0, 12.556, 0]}, {"time": 13}]}, "bodyround": {"translate": [{"y": 67.2, "curve": [0.168, 0, 0.334, 0, 0.168, 130.1, 0.334, 181.7]}, {"time": 0.5, "y": 181.7, "curve": [0.944, 0, 1.389, 0, 0.944, 181.7, 1.389, -178.36]}, {"time": 1.8333, "y": -178.36, "curve": [2.278, 0, 2.722, 0, 2.278, -178.36, 2.722, 181.7]}, {"time": 3.1667, "y": 181.7, "curve": [3.611, 0, 4.056, 0, 3.611, 181.7, 4.056, -178.36]}, {"time": 4.5, "y": -178.36, "curve": [4.944, 0, 5.389, 0, 4.944, -178.36, 5.389, 181.7]}, {"time": 5.8333, "y": 181.7, "curve": [6.167, 0, 6.5, 0, 6.167, 181.7, 6.5, -178.36]}, {"time": 6.8333, "y": -178.36, "curve": [7.278, 0, 7.722, 0, 7.278, -178.36, 7.722, -84.26]}, {"time": 8.1667, "y": -84.26, "curve": [8.611, 0, 9.056, 0, 8.611, -84.26, 9.056, -178.36]}, {"time": 9.5, "y": -178.36, "curve": [9.944, 0, 10.389, 0, 9.944, -178.36, 10.389, 181.7]}, {"time": 10.8333, "y": 181.7, "curve": [11.278, 0, 11.722, 0, 11.278, 181.7, 11.722, -178.36]}, {"time": 12.1667, "y": -178.36, "curve": [12.445, 0, 12.724, 0, 12.445, -178.36, 12.724, -38.37]}, {"time": 13, "y": 67.2}]}, "tunround": {"translate": [{"x": 167.24, "curve": [0.057, 177.79, 0.112, 185.83, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 185.83, "curve": [0.611, 185.83, 1.056, -209.75, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -209.75, "curve": [1.944, -209.75, 2.389, 185.83, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 185.83, "curve": [3.278, 185.83, 3.722, -209.75, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -209.75, "curve": [4.611, -209.75, 5.056, 185.83, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 185.83, "curve": [5.833, 185.83, 6.167, -209.75, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": -209.75, "curve": [6.944, -209.75, 7.389, -106.37, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": -106.37, "curve": [8.278, -106.37, 8.722, -209.75, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -209.75, "curve": [9.611, -209.75, 10.056, 185.83, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 185.83, "curve": [10.944, 185.83, 11.389, -209.75, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -209.75, "curve": [12.223, -209.75, 12.613, 92.25, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 167.24}]}, "RU_R": {"translate": [{"x": -8.51, "curve": [0.168, -22.7, 0.334, -34.34, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.34, "curve": [0.944, -34.34, 1.389, 46.83, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 46.85, "curve": [2.278, 46.87, 2.722, -34.31, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -34.34, "curve": [3.611, -34.36, 4.056, 46.83, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 46.85, "curve": [4.944, 46.87, 5.389, -34.31, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -34.34, "curve": [6.167, -34.36, 6.5, 46.84, 6.278, 0, 6.389, 0]}, {"time": 6.8333, "x": 46.85, "curve": [7.278, 46.87, 7.722, -34.31, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -34.34, "curve": [8.611, -34.36, 9.056, 46.83, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 46.85, "curve": [9.944, 46.87, 10.389, -34.31, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -34.34, "curve": [11.278, -34.36, 11.722, 46.83, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 46.85, "curve": [12.445, 46.87, 12.724, 15.3, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -8.51}], "scale": [{"x": 1.087, "y": 0.955, "curve": [0.167, 1.013, 0.333, 0.867, 0.167, 1.007, 0.333, 1.108]}, {"time": 0.5, "x": 0.867, "y": 1.108, "curve": [0.722, 0.867, 0.944, 1.129, 0.722, 1.108, 0.944, 0.926]}, {"time": 1.1667, "x": 1.129, "y": 0.926, "curve": [1.389, 1.129, 1.611, 0.867, 1.389, 0.926, 1.611, 1.108]}, {"time": 1.8333, "x": 0.867, "y": 1.108, "curve": [2.056, 0.867, 2.278, 1.129, 2.056, 1.108, 2.278, 0.926]}, {"time": 2.5, "x": 1.129, "y": 0.926, "curve": [2.722, 1.129, 2.944, 0.867, 2.722, 0.926, 2.944, 1.108]}, {"time": 3.1667, "x": 0.867, "y": 1.108, "curve": [3.389, 0.867, 3.611, 1.129, 3.389, 1.108, 3.611, 0.926]}, {"time": 3.8333, "x": 1.129, "y": 0.926, "curve": [4.056, 1.129, 4.278, 0.867, 4.056, 0.926, 4.278, 1.108]}, {"time": 4.5, "x": 0.867, "y": 1.108, "curve": [4.722, 0.867, 4.944, 1.129, 4.722, 1.108, 4.944, 0.926]}, {"time": 5.1667, "x": 1.129, "y": 0.926, "curve": [5.389, 1.129, 5.611, 0.867, 5.389, 0.926, 5.611, 1.108]}, {"time": 5.8333, "x": 0.867, "y": 1.108, "curve": [6, 0.867, 6.167, 1.129, 6, 1.108, 6.167, 0.926]}, {"time": 6.3333, "x": 1.129, "y": 0.926, "curve": [6.5, 1.129, 6.667, 0.867, 6.5, 0.926, 6.667, 1.108]}, {"time": 6.8333, "x": 0.867, "y": 1.108, "curve": [7.056, 0.867, 7.278, 1.129, 7.056, 1.108, 7.278, 0.926]}, {"time": 7.5, "x": 1.129, "y": 0.926, "curve": [7.722, 1.129, 7.944, 0.867, 7.722, 0.926, 7.944, 1.108]}, {"time": 8.1667, "x": 0.867, "y": 1.108, "curve": [8.389, 0.867, 8.611, 1.129, 8.389, 1.108, 8.611, 0.926]}, {"time": 8.8333, "x": 1.129, "y": 0.926, "curve": [9.056, 1.129, 9.278, 0.867, 9.056, 0.926, 9.278, 1.108]}, {"time": 9.5, "x": 0.867, "y": 1.108, "curve": [9.722, 0.867, 9.944, 1.129, 9.722, 1.108, 9.944, 0.926]}, {"time": 10.1667, "x": 1.129, "y": 0.926, "curve": [10.389, 1.129, 10.611, 0.867, 10.389, 0.926, 10.611, 1.108]}, {"time": 10.8333, "x": 0.867, "y": 1.108, "curve": [11.056, 0.867, 11.278, 1.129, 11.056, 1.108, 11.278, 0.926]}, {"time": 11.5, "x": 1.129, "y": 0.926, "curve": [11.722, 1.129, 11.944, 0.867, 11.722, 0.926, 11.944, 1.108]}, {"time": 12.1667, "x": 0.867, "y": 1.108, "curve": [12.389, 0.867, 12.611, 1.129, 12.389, 1.108, 12.611, 0.926]}, {"time": 12.8333, "x": 1.129, "y": 0.926, "curve": [12.889, 1.129, 12.944, 1.112, 12.889, 0.926, 12.944, 0.938]}, {"time": 13, "x": 1.087, "y": 0.955}]}, "RU_R2": {"translate": [{"x": 0.6, "curve": [0.225, -15.5, 0.446, -31.81, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -31.81, "curve": [1.111, -31.81, 1.556, 32.99, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 33.01, "curve": [2.444, 33.03, 2.889, -31.8, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -31.81, "curve": [3.778, -31.83, 4.222, 32.99, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 33.01, "curve": [5.111, 33.03, 5.556, -31.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -31.81, "curve": [6.333, -31.83, 6.667, 33, 6.444, 0, 6.556, 0]}, {"time": 7, "x": 33.01, "curve": [7.444, 33.03, 7.889, -31.8, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -31.81, "curve": [8.778, -31.83, 9.222, 32.99, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 33.01, "curve": [10.111, 33.03, 10.556, -31.8, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -31.81, "curve": [11.444, -31.83, 11.889, 32.99, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 33.01, "curve": [12.557, 33.02, 12.781, 16.92, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 0.6}], "scale": [{"x": 1.129, "y": 0.926, "curve": [0.222, 1.129, 0.444, 0.867, 0.222, 0.926, 0.444, 1.108]}, {"time": 0.6667, "x": 0.867, "y": 1.108, "curve": [0.889, 0.867, 1.111, 1.129, 0.889, 1.108, 1.111, 0.926]}, {"time": 1.3333, "x": 1.129, "y": 0.926, "curve": [1.556, 1.129, 1.778, 0.867, 1.556, 0.926, 1.778, 1.108]}, {"time": 2, "x": 0.867, "y": 1.108, "curve": [2.222, 0.867, 2.444, 1.129, 2.222, 1.108, 2.444, 0.926]}, {"time": 2.6667, "x": 1.129, "y": 0.926, "curve": [2.889, 1.129, 3.111, 0.867, 2.889, 0.926, 3.111, 1.108]}, {"time": 3.3333, "x": 0.867, "y": 1.108, "curve": [3.556, 0.867, 3.778, 1.129, 3.556, 1.108, 3.778, 0.926]}, {"time": 4, "x": 1.129, "y": 0.926, "curve": [4.222, 1.129, 4.444, 0.867, 4.222, 0.926, 4.444, 1.108]}, {"time": 4.6667, "x": 0.867, "y": 1.108, "curve": [4.889, 0.867, 5.111, 1.129, 4.889, 1.108, 5.111, 0.926]}, {"time": 5.3333, "x": 1.129, "y": 0.926, "curve": [5.556, 1.129, 5.778, 0.867, 5.556, 0.926, 5.778, 1.108]}, {"time": 6, "x": 0.867, "y": 1.108, "curve": [6.167, 0.867, 6.333, 1.129, 6.167, 1.108, 6.333, 0.926]}, {"time": 6.5, "x": 1.129, "y": 0.926, "curve": [6.667, 1.129, 6.833, 0.867, 6.667, 0.926, 6.833, 1.108]}, {"time": 7, "x": 0.867, "y": 1.108, "curve": [7.222, 0.867, 7.444, 1.129, 7.222, 1.108, 7.444, 0.926]}, {"time": 7.6667, "x": 1.129, "y": 0.926, "curve": [7.889, 1.129, 8.111, 0.867, 7.889, 0.926, 8.111, 1.108]}, {"time": 8.3333, "x": 0.867, "y": 1.108, "curve": [8.556, 0.867, 8.778, 1.129, 8.556, 1.108, 8.778, 0.926]}, {"time": 9, "x": 1.129, "y": 0.926, "curve": [9.222, 1.129, 9.444, 0.867, 9.222, 0.926, 9.444, 1.108]}, {"time": 9.6667, "x": 0.867, "y": 1.108, "curve": [9.889, 0.867, 10.111, 1.129, 9.889, 1.108, 10.111, 0.926]}, {"time": 10.3333, "x": 1.129, "y": 0.926, "curve": [10.556, 1.129, 10.778, 0.867, 10.556, 0.926, 10.778, 1.108]}, {"time": 11, "x": 0.867, "y": 1.108, "curve": [11.222, 0.867, 11.444, 1.129, 11.222, 1.108, 11.444, 0.926]}, {"time": 11.6667, "x": 1.129, "y": 0.926, "curve": [11.889, 1.129, 12.111, 0.867, 11.889, 0.926, 12.111, 1.108]}, {"time": 12.3333, "x": 0.867, "y": 1.108, "curve": [12.556, 0.867, 12.778, 1.129, 12.556, 1.108, 12.778, 0.926]}, {"time": 13, "x": 1.129, "y": 0.926}]}, "RU_R3": {"translate": [{"x": 15.86, "curve": [0.279, -4.16, 0.556, -30.79, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -30.79, "curve": [1.278, -30.79, 1.722, 37.58, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 37.6, "curve": [2.611, 37.62, 3.056, -30.77, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -30.79, "curve": [3.944, -30.81, 4.389, 37.58, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 37.6, "curve": [5.278, 37.62, 5.722, -30.77, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -30.79, "curve": [6.5, -30.81, 6.833, 37.59, 6.611, 0, 6.722, 0]}, {"time": 7.1667, "x": 37.6, "curve": [7.611, 37.62, 8.056, -30.77, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -30.79, "curve": [8.944, -30.81, 9.389, 37.58, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 37.6, "curve": [10.278, 37.62, 10.722, -30.77, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -30.79, "curve": [11.611, -30.81, 12.056, 37.58, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 37.6, "curve": [12.667, 37.6, 12.835, 27.95, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 15.86}], "scale": [{"x": 1.087, "y": 0.956, "curve": [0.056, 1.111, 0.111, 1.129, 0.056, 0.938, 0.111, 0.926]}, {"time": 0.1667, "x": 1.129, "y": 0.926, "curve": [0.389, 1.129, 0.611, 0.867, 0.389, 0.926, 0.611, 1.108]}, {"time": 0.8333, "x": 0.867, "y": 1.108, "curve": [1.056, 0.867, 1.278, 1.129, 1.056, 1.108, 1.278, 0.926]}, {"time": 1.5, "x": 1.129, "y": 0.926, "curve": [1.722, 1.129, 1.944, 0.867, 1.722, 0.926, 1.944, 1.108]}, {"time": 2.1667, "x": 0.867, "y": 1.108, "curve": [2.389, 0.867, 2.611, 1.129, 2.389, 1.108, 2.611, 0.926]}, {"time": 2.8333, "x": 1.129, "y": 0.926, "curve": [3.056, 1.129, 3.278, 0.867, 3.056, 0.926, 3.278, 1.108]}, {"time": 3.5, "x": 0.867, "y": 1.108, "curve": [3.722, 0.867, 3.944, 1.129, 3.722, 1.108, 3.944, 0.926]}, {"time": 4.1667, "x": 1.129, "y": 0.926, "curve": [4.389, 1.129, 4.611, 0.867, 4.389, 0.926, 4.611, 1.108]}, {"time": 4.8333, "x": 0.867, "y": 1.108, "curve": [5.056, 0.867, 5.278, 1.129, 5.056, 1.108, 5.278, 0.926]}, {"time": 5.5, "x": 1.129, "y": 0.926, "curve": [5.722, 1.129, 5.944, 0.867, 5.722, 0.926, 5.944, 1.108]}, {"time": 6.1667, "x": 0.867, "y": 1.108, "curve": [6.333, 0.867, 6.5, 1.129, 6.333, 1.108, 6.5, 0.926]}, {"time": 6.6667, "x": 1.129, "y": 0.926, "curve": [6.833, 1.129, 7, 0.867, 6.833, 0.926, 7, 1.108]}, {"time": 7.1667, "x": 0.867, "y": 1.108, "curve": [7.389, 0.867, 7.611, 1.129, 7.389, 1.108, 7.611, 0.926]}, {"time": 7.8333, "x": 1.129, "y": 0.926, "curve": [8.056, 1.129, 8.278, 0.867, 8.056, 0.926, 8.278, 1.108]}, {"time": 8.5, "x": 0.867, "y": 1.108, "curve": [8.722, 0.867, 8.944, 1.129, 8.722, 1.108, 8.944, 0.926]}, {"time": 9.1667, "x": 1.129, "y": 0.926, "curve": [9.389, 1.129, 9.611, 0.867, 9.389, 0.926, 9.611, 1.108]}, {"time": 9.8333, "x": 0.867, "y": 1.108, "curve": [10.056, 0.867, 10.278, 1.129, 10.056, 1.108, 10.278, 0.926]}, {"time": 10.5, "x": 1.129, "y": 0.926, "curve": [10.722, 1.129, 10.944, 0.867, 10.722, 0.926, 10.944, 1.108]}, {"time": 11.1667, "x": 0.867, "y": 1.108, "curve": [11.389, 0.867, 11.611, 1.129, 11.389, 1.108, 11.611, 0.926]}, {"time": 11.8333, "x": 1.129, "y": 0.926, "curve": [12.056, 1.129, 12.278, 0.867, 12.056, 0.926, 12.278, 1.108]}, {"time": 12.5, "x": 0.867, "y": 1.108, "curve": [12.667, 0.867, 12.833, 1.013, 12.667, 1.108, 12.833, 1.007]}, {"time": 13, "x": 1.087, "y": 0.956}]}, "RU_L": {"translate": [{"x": -8.51, "curve": [0.168, -22.7, 0.334, -34.34, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -34.34, "curve": [0.944, -34.34, 1.389, 46.83, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 46.85, "curve": [2.278, 46.87, 2.722, -34.31, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -34.34, "curve": [3.611, -34.36, 4.056, 46.83, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 46.85, "curve": [4.944, 46.87, 5.389, -34.31, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -34.34, "curve": [6.167, -34.36, 6.5, 46.84, 6.278, 0, 6.389, 0]}, {"time": 6.8333, "x": 46.85, "curve": [7.278, 46.87, 7.722, -34.31, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -34.34, "curve": [8.611, -34.36, 9.056, 46.83, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 46.85, "curve": [9.944, 46.87, 10.389, -34.31, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -34.34, "curve": [11.278, -34.36, 11.722, 46.83, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 46.85, "curve": [12.445, 46.87, 12.724, 15.3, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -8.51}], "scale": [{"x": 1.087, "y": 0.955, "curve": [0.167, 1.013, 0.333, 0.867, 0.167, 1.007, 0.333, 1.108]}, {"time": 0.5, "x": 0.867, "y": 1.108, "curve": [0.722, 0.867, 0.944, 1.129, 0.722, 1.108, 0.944, 0.926]}, {"time": 1.1667, "x": 1.129, "y": 0.926, "curve": [1.389, 1.129, 1.611, 0.867, 1.389, 0.926, 1.611, 1.108]}, {"time": 1.8333, "x": 0.867, "y": 1.108, "curve": [2.056, 0.867, 2.278, 1.129, 2.056, 1.108, 2.278, 0.926]}, {"time": 2.5, "x": 1.129, "y": 0.926, "curve": [2.722, 1.129, 2.944, 0.867, 2.722, 0.926, 2.944, 1.108]}, {"time": 3.1667, "x": 0.867, "y": 1.108, "curve": [3.389, 0.867, 3.611, 1.129, 3.389, 1.108, 3.611, 0.926]}, {"time": 3.8333, "x": 1.129, "y": 0.926, "curve": [4.056, 1.129, 4.278, 0.867, 4.056, 0.926, 4.278, 1.108]}, {"time": 4.5, "x": 0.867, "y": 1.108, "curve": [4.722, 0.867, 4.944, 1.129, 4.722, 1.108, 4.944, 0.926]}, {"time": 5.1667, "x": 1.129, "y": 0.926, "curve": [5.389, 1.129, 5.611, 0.867, 5.389, 0.926, 5.611, 1.108]}, {"time": 5.8333, "x": 0.867, "y": 1.108, "curve": [6, 0.867, 6.167, 1.129, 6, 1.108, 6.167, 0.926]}, {"time": 6.3333, "x": 1.129, "y": 0.926, "curve": [6.5, 1.129, 6.667, 0.867, 6.5, 0.926, 6.667, 1.108]}, {"time": 6.8333, "x": 0.867, "y": 1.108, "curve": [7.056, 0.867, 7.278, 1.129, 7.056, 1.108, 7.278, 0.926]}, {"time": 7.5, "x": 1.129, "y": 0.926, "curve": [7.722, 1.129, 7.944, 0.867, 7.722, 0.926, 7.944, 1.108]}, {"time": 8.1667, "x": 0.867, "y": 1.108, "curve": [8.389, 0.867, 8.611, 1.129, 8.389, 1.108, 8.611, 0.926]}, {"time": 8.8333, "x": 1.129, "y": 0.926, "curve": [9.056, 1.129, 9.278, 0.867, 9.056, 0.926, 9.278, 1.108]}, {"time": 9.5, "x": 0.867, "y": 1.108, "curve": [9.722, 0.867, 9.944, 1.129, 9.722, 1.108, 9.944, 0.926]}, {"time": 10.1667, "x": 1.129, "y": 0.926, "curve": [10.389, 1.129, 10.611, 0.867, 10.389, 0.926, 10.611, 1.108]}, {"time": 10.8333, "x": 0.867, "y": 1.108, "curve": [11.056, 0.867, 11.278, 1.129, 11.056, 1.108, 11.278, 0.926]}, {"time": 11.5, "x": 1.129, "y": 0.926, "curve": [11.722, 1.129, 11.944, 0.867, 11.722, 0.926, 11.944, 1.108]}, {"time": 12.1667, "x": 0.867, "y": 1.108, "curve": [12.389, 0.867, 12.611, 1.129, 12.389, 1.108, 12.611, 0.926]}, {"time": 12.8333, "x": 1.129, "y": 0.926, "curve": [12.889, 1.129, 12.944, 1.112, 12.889, 0.926, 12.944, 0.938]}, {"time": 13, "x": 1.087, "y": 0.955}]}, "RU_L2": {"translate": [{"x": 0.6, "curve": [0.225, -15.5, 0.446, -31.81, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -31.81, "curve": [1.111, -31.81, 1.556, 32.99, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 33.01, "curve": [2.444, 33.03, 2.889, -31.8, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -31.81, "curve": [3.778, -31.83, 4.222, 32.99, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 33.01, "curve": [5.111, 33.03, 5.556, -31.79, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -31.81, "curve": [6.333, -31.83, 6.667, 33, 6.444, 0, 6.556, 0]}, {"time": 7, "x": 33.01, "curve": [7.444, 33.03, 7.889, -31.8, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -31.81, "curve": [8.778, -31.83, 9.222, 32.99, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 33.01, "curve": [10.111, 33.03, 10.556, -31.8, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -31.81, "curve": [11.444, -31.83, 11.889, 32.99, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 33.01, "curve": [12.557, 33.02, 12.781, 16.92, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 0.6}], "scale": [{"x": 1.129, "y": 0.926, "curve": [0.222, 1.129, 0.444, 0.867, 0.222, 0.926, 0.444, 1.108]}, {"time": 0.6667, "x": 0.867, "y": 1.108, "curve": [0.889, 0.867, 1.111, 1.129, 0.889, 1.108, 1.111, 0.926]}, {"time": 1.3333, "x": 1.129, "y": 0.926, "curve": [1.556, 1.129, 1.778, 0.867, 1.556, 0.926, 1.778, 1.108]}, {"time": 2, "x": 0.867, "y": 1.108, "curve": [2.222, 0.867, 2.444, 1.129, 2.222, 1.108, 2.444, 0.926]}, {"time": 2.6667, "x": 1.129, "y": 0.926, "curve": [2.889, 1.129, 3.111, 0.867, 2.889, 0.926, 3.111, 1.108]}, {"time": 3.3333, "x": 0.867, "y": 1.108, "curve": [3.556, 0.867, 3.778, 1.129, 3.556, 1.108, 3.778, 0.926]}, {"time": 4, "x": 1.129, "y": 0.926, "curve": [4.222, 1.129, 4.444, 0.867, 4.222, 0.926, 4.444, 1.108]}, {"time": 4.6667, "x": 0.867, "y": 1.108, "curve": [4.889, 0.867, 5.111, 1.129, 4.889, 1.108, 5.111, 0.926]}, {"time": 5.3333, "x": 1.129, "y": 0.926, "curve": [5.556, 1.129, 5.778, 0.867, 5.556, 0.926, 5.778, 1.108]}, {"time": 6, "x": 0.867, "y": 1.108, "curve": [6.167, 0.867, 6.333, 1.129, 6.167, 1.108, 6.333, 0.926]}, {"time": 6.5, "x": 1.129, "y": 0.926, "curve": [6.667, 1.129, 6.833, 0.867, 6.667, 0.926, 6.833, 1.108]}, {"time": 7, "x": 0.867, "y": 1.108, "curve": [7.222, 0.867, 7.444, 1.129, 7.222, 1.108, 7.444, 0.926]}, {"time": 7.6667, "x": 1.129, "y": 0.926, "curve": [7.889, 1.129, 8.111, 0.867, 7.889, 0.926, 8.111, 1.108]}, {"time": 8.3333, "x": 0.867, "y": 1.108, "curve": [8.556, 0.867, 8.778, 1.129, 8.556, 1.108, 8.778, 0.926]}, {"time": 9, "x": 1.129, "y": 0.926, "curve": [9.222, 1.129, 9.444, 0.867, 9.222, 0.926, 9.444, 1.108]}, {"time": 9.6667, "x": 0.867, "y": 1.108, "curve": [9.889, 0.867, 10.111, 1.129, 9.889, 1.108, 10.111, 0.926]}, {"time": 10.3333, "x": 1.129, "y": 0.926, "curve": [10.556, 1.129, 10.778, 0.867, 10.556, 0.926, 10.778, 1.108]}, {"time": 11, "x": 0.867, "y": 1.108, "curve": [11.222, 0.867, 11.444, 1.129, 11.222, 1.108, 11.444, 0.926]}, {"time": 11.6667, "x": 1.129, "y": 0.926, "curve": [11.889, 1.129, 12.111, 0.867, 11.889, 0.926, 12.111, 1.108]}, {"time": 12.3333, "x": 0.867, "y": 1.108, "curve": [12.556, 0.867, 12.778, 1.129, 12.556, 1.108, 12.778, 0.926]}, {"time": 13, "x": 1.129, "y": 0.926}]}, "RU_L3": {"translate": [{"x": 15.86, "curve": [0.279, -4.16, 0.556, -30.79, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -30.79, "curve": [1.278, -30.79, 1.722, 37.58, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 37.6, "curve": [2.611, 37.62, 3.056, -30.77, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -30.79, "curve": [3.944, -30.81, 4.389, 37.58, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 37.6, "curve": [5.278, 37.62, 5.722, -30.77, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -30.79, "curve": [6.5, -30.81, 6.833, 37.59, 6.611, 0, 6.722, 0]}, {"time": 7.1667, "x": 37.6, "curve": [7.611, 37.62, 8.056, -30.77, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -30.79, "curve": [8.944, -30.81, 9.389, 37.58, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 37.6, "curve": [10.278, 37.62, 10.722, -30.77, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -30.79, "curve": [11.611, -30.81, 12.056, 37.58, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 37.6, "curve": [12.667, 37.6, 12.835, 27.95, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 15.86}], "scale": [{"x": 1.087, "y": 0.956, "curve": [0.056, 1.111, 0.111, 1.129, 0.056, 0.938, 0.111, 0.926]}, {"time": 0.1667, "x": 1.129, "y": 0.926, "curve": [0.389, 1.129, 0.611, 0.867, 0.389, 0.926, 0.611, 1.108]}, {"time": 0.8333, "x": 0.867, "y": 1.108, "curve": [1.056, 0.867, 1.278, 1.129, 1.056, 1.108, 1.278, 0.926]}, {"time": 1.5, "x": 1.129, "y": 0.926, "curve": [1.722, 1.129, 1.944, 0.867, 1.722, 0.926, 1.944, 1.108]}, {"time": 2.1667, "x": 0.867, "y": 1.108, "curve": [2.389, 0.867, 2.611, 1.129, 2.389, 1.108, 2.611, 0.926]}, {"time": 2.8333, "x": 1.129, "y": 0.926, "curve": [3.056, 1.129, 3.278, 0.867, 3.056, 0.926, 3.278, 1.108]}, {"time": 3.5, "x": 0.867, "y": 1.108, "curve": [3.722, 0.867, 3.944, 1.129, 3.722, 1.108, 3.944, 0.926]}, {"time": 4.1667, "x": 1.129, "y": 0.926, "curve": [4.389, 1.129, 4.611, 0.867, 4.389, 0.926, 4.611, 1.108]}, {"time": 4.8333, "x": 0.867, "y": 1.108, "curve": [5.056, 0.867, 5.278, 1.129, 5.056, 1.108, 5.278, 0.926]}, {"time": 5.5, "x": 1.129, "y": 0.926, "curve": [5.722, 1.129, 5.944, 0.867, 5.722, 0.926, 5.944, 1.108]}, {"time": 6.1667, "x": 0.867, "y": 1.108, "curve": [6.333, 0.867, 6.5, 1.129, 6.333, 1.108, 6.5, 0.926]}, {"time": 6.6667, "x": 1.129, "y": 0.926, "curve": [6.833, 1.129, 7, 0.867, 6.833, 0.926, 7, 1.108]}, {"time": 7.1667, "x": 0.867, "y": 1.108, "curve": [7.389, 0.867, 7.611, 1.129, 7.389, 1.108, 7.611, 0.926]}, {"time": 7.8333, "x": 1.129, "y": 0.926, "curve": [8.056, 1.129, 8.278, 0.867, 8.056, 0.926, 8.278, 1.108]}, {"time": 8.5, "x": 0.867, "y": 1.108, "curve": [8.722, 0.867, 8.944, 1.129, 8.722, 1.108, 8.944, 0.926]}, {"time": 9.1667, "x": 1.129, "y": 0.926, "curve": [9.389, 1.129, 9.611, 0.867, 9.389, 0.926, 9.611, 1.108]}, {"time": 9.8333, "x": 0.867, "y": 1.108, "curve": [10.056, 0.867, 10.278, 1.129, 10.056, 1.108, 10.278, 0.926]}, {"time": 10.5, "x": 1.129, "y": 0.926, "curve": [10.722, 1.129, 10.944, 0.867, 10.722, 0.926, 10.944, 1.108]}, {"time": 11.1667, "x": 0.867, "y": 1.108, "curve": [11.389, 0.867, 11.611, 1.129, 11.389, 1.108, 11.611, 0.926]}, {"time": 11.8333, "x": 1.129, "y": 0.926, "curve": [12.056, 1.129, 12.278, 0.867, 12.056, 0.926, 12.278, 1.108]}, {"time": 12.5, "x": 0.867, "y": 1.108, "curve": [12.667, 0.867, 12.833, 1.013, 12.667, 1.108, 12.833, 1.007]}, {"time": 13, "x": 1.087, "y": 0.956}]}, "hair_L": {"rotate": [{"value": 3.14, "curve": [0.336, 0.58, 0.668, -4.47]}, {"time": 1, "value": -4.47, "curve": [1.444, -4.47, 1.889, 4.59]}, {"time": 2.3333, "value": 4.59, "curve": [2.778, 4.59, 3.222, -4.46]}, {"time": 3.6667, "value": -4.47, "curve": [4.111, -4.47, 4.556, 4.59]}, {"time": 5, "value": 4.59, "curve": [5.444, 4.59, 5.889, -4.46]}, {"time": 6.3333, "value": -4.47, "curve": [6.667, -4.47, 7, 4.59]}, {"time": 7.3333, "value": 4.59, "curve": [7.778, 4.59, 8.222, -4.46]}, {"time": 8.6667, "value": -4.47, "curve": [9.111, -4.47, 9.556, 4.59]}, {"time": 10, "value": 4.59, "curve": [10.444, 4.59, 10.889, -4.46]}, {"time": 11.3333, "value": -4.47, "curve": [11.778, -4.47, 12.222, 4.59]}, {"time": 12.6667, "value": 4.59, "curve": [12.779, 4.59, 12.892, 4.01]}, {"time": 13, "value": 3.14}]}, "hair_R": {"rotate": [{"value": -3.02, "curve": [0.336, -0.46, 0.668, 4.59]}, {"time": 1, "value": 4.59, "curve": [1.444, 4.59, 1.889, -4.46]}, {"time": 2.3333, "value": -4.47, "curve": [2.778, -4.47, 3.222, 4.59]}, {"time": 3.6667, "value": 4.59, "curve": [4.111, 4.59, 4.556, -4.46]}, {"time": 5, "value": -4.47, "curve": [5.444, -4.47, 5.889, 4.59]}, {"time": 6.3333, "value": 4.59, "curve": [6.667, 4.59, 7, -4.47]}, {"time": 7.3333, "value": -4.47, "curve": [7.778, -4.47, 8.222, 4.59]}, {"time": 8.6667, "value": 4.59, "curve": [9.111, 4.59, 9.556, -4.46]}, {"time": 10, "value": -4.47, "curve": [10.444, -4.47, 10.889, 4.59]}, {"time": 11.3333, "value": 4.59, "curve": [11.778, 4.59, 12.222, -4.46]}, {"time": 12.6667, "value": -4.47, "curve": [12.779, -4.47, 12.892, -3.89]}, {"time": 13, "value": -3.02}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.5, "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "vertices": [-9.28186, 0.24323, -9.26782, 0.24287, -3.16614, -0.09132, -3.15649, -0.09162, -2.96375, -0.49182, -2.95911, -0.49202, -1.42285, 0.05069, -1.42065, 0.05061, -0.58093, -0.04732, -0.5791, -0.04739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.6637, -0.05408, -0.66199, -0.05415, -5.92053, -0.06598, -5.90942, -0.06632, -10.20288, 0.33447, -10.19446, 0.33426, -13.03503, 0.27026, -13.02209, 0.26984, -13.03503, 0.27026, -13.02209, 0.26984, -13.15869, 0.76038, -13.14526, 0.75994, 0, 0, 0, 0, -3.57556, -0.20918, -3.57129, -0.20921, -7.86731, 0.26588, -7.86108, 0.26583, -12.00195, 0.34109, -11.99341, 0.34105, -13.6665, 0.85759, -13.6615, 0.85742, -12.56946, 0.83771, -12.56299, 0.83739, -8.7677, -0.05789, -8.76392, -0.05807, -2.37988, -0.1942, -2.37708, -0.19423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8208, -0.06697, -0.81958, -0.06699], "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "vertices": [-9.28186, 0.24323, -9.26782, 0.24287, -3.16614, -0.09132, -3.15649, -0.09162, -2.96375, -0.49182, -2.95911, -0.49202, -1.42285, 0.05069, -1.42065, 0.05061, -0.58093, -0.04732, -0.5791, -0.04739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.6637, -0.05408, -0.66199, -0.05415, -5.92053, -0.06598, -5.90942, -0.06632, -10.20288, 0.33447, -10.19446, 0.33426, -13.03503, 0.27026, -13.02209, 0.26984, -13.03503, 0.27026, -13.02209, 0.26984, -13.15869, 0.76038, -13.14526, 0.75994, 0, 0, 0, 0, -3.57556, -0.20918, -3.57129, -0.20921, -7.86731, 0.26588, -7.86108, 0.26583, -12.00195, 0.34109, -11.99341, 0.34105, -13.6665, 0.85759, -13.6615, 0.85742, -12.56946, 0.83771, -12.56299, 0.83739, -8.7677, -0.05789, -8.76392, -0.05807, -2.37988, -0.1942, -2.37708, -0.19423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8208, -0.06697, -0.81958, -0.06699], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-9.28186, 0.24323, -9.26782, 0.24287, -3.16614, -0.09132, -3.15649, -0.09162, -2.96375, -0.49182, -2.95911, -0.49202, -1.42285, 0.05069, -1.42065, 0.05061, -0.58093, -0.04732, -0.5791, -0.04739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.6637, -0.05408, -0.66199, -0.05415, -5.92053, -0.06598, -5.90942, -0.06632, -10.20288, 0.33447, -10.19446, 0.33426, -13.03503, 0.27026, -13.02209, 0.26984, -13.03503, 0.27026, -13.02209, 0.26984, -13.15869, 0.76038, -13.14526, 0.75994, 0, 0, 0, 0, -3.57556, -0.20918, -3.57129, -0.20921, -7.86731, 0.26588, -7.86108, 0.26583, -12.00195, 0.34109, -11.99341, 0.34105, -13.6665, 0.85759, -13.6615, 0.85742, -12.56946, 0.83771, -12.56299, 0.83739, -8.7677, -0.05789, -8.76392, -0.05807, -2.37988, -0.1942, -2.37708, -0.19423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.8208, -0.06697, -0.81958, -0.06699], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.5, "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "vertices": [-15.82886, 1.77332, -15.8125, 1.77301, -14.36279, 1.25703, -14.33972, 1.25615, -12.02783, 0.47589, -12.01257, 0.47521, -8.27417, 0.13448, -8.26355, 0.13414, -2.42993, -0.03604, -2.42261, -0.03642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.01355, -0.16408, -2.0083, -0.16432, -3.07471, 0.24432, -3.06592, 0.24403, -3.08582, 0.07219, -3.07922, 0.07198, -4.56006, 0.27562, -4.55225, 0.27545, -8.29395, 0.29485, -8.28247, 0.29439, -12.04626, 0.21429, -12.03076, 0.21397, -3.75439, 0.34131, -3.74951, 0.34113, -1.30786, 0.13622, -1.30518, 0.13609, 0, 0, 0, 0, 0, 0, 0, 0, -10.18201, 0.70718, -10.17151, 0.70684, -13.30859, 1.34225, -13.29956, 1.34187, -14.97046, 0.52882, -14.96228, 0.52854, -13.59741, 0.05725, -13.58984, 0.05708, -9.32654, -0.17755, -9.32019, -0.17777, -4.58459, -0.04062, -4.58032, -0.04076], "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "vertices": [-15.82886, 1.77332, -15.8125, 1.77301, -14.36279, 1.25703, -14.33972, 1.25615, -12.02783, 0.47589, -12.01257, 0.47521, -8.27417, 0.13448, -8.26355, 0.13414, -2.42993, -0.03604, -2.42261, -0.03642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.01355, -0.16408, -2.0083, -0.16432, -3.07471, 0.24432, -3.06592, 0.24403, -3.08582, 0.07219, -3.07922, 0.07198, -4.56006, 0.27562, -4.55225, 0.27545, -8.29395, 0.29485, -8.28247, 0.29439, -12.04626, 0.21429, -12.03076, 0.21397, -3.75439, 0.34131, -3.74951, 0.34113, -1.30786, 0.13622, -1.30518, 0.13609, 0, 0, 0, 0, 0, 0, 0, 0, -10.18201, 0.70718, -10.17151, 0.70684, -13.30859, 1.34225, -13.29956, 1.34187, -14.97046, 0.52882, -14.96228, 0.52854, -13.59741, 0.05725, -13.58984, 0.05708, -9.32654, -0.17755, -9.32019, -0.17777, -4.58459, -0.04062, -4.58032, -0.04076], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "vertices": [-15.82886, 1.77332, -15.8125, 1.77301, -14.36279, 1.25703, -14.33972, 1.25615, -12.02783, 0.47589, -12.01257, 0.47521, -8.27417, 0.13448, -8.26355, 0.13414, -2.42993, -0.03604, -2.42261, -0.03642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.01355, -0.16408, -2.0083, -0.16432, -3.07471, 0.24432, -3.06592, 0.24403, -3.08582, 0.07219, -3.07922, 0.07198, -4.56006, 0.27562, -4.55225, 0.27545, -8.29395, 0.29485, -8.28247, 0.29439, -12.04626, 0.21429, -12.03076, 0.21397, -3.75439, 0.34131, -3.74951, 0.34113, -1.30786, 0.13622, -1.30518, 0.13609, 0, 0, 0, 0, 0, 0, 0, 0, -10.18201, 0.70718, -10.17151, 0.70684, -13.30859, 1.34225, -13.29956, 1.34187, -14.97046, 0.52882, -14.96228, 0.52854, -13.59741, 0.05725, -13.58984, 0.05708, -9.32654, -0.17755, -9.32019, -0.17777, -4.58459, -0.04062, -4.58032, -0.04076], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head": {"head": {"deform": [{"time": 1.5, "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "offset": 398, "vertices": [-1.06458, -0.08681, -1.06262, -0.08681, -6.74731, -0.43169, -6.74304, -0.43185, -11.50293, -0.46318, -11.49817, -0.46331, -12.23291, -0.2847, -12.22546, -0.28486, -10.35156, -0.01216, -10.34521, -0.0124, -5.8114, -0.23639, -5.80774, -0.23647, -0.43359, -0.26262, -0.43286, -0.26261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.56506, -0.04605, -0.56433, -0.04608, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.46619, 0.2641, -2.46277, 0.26395, -8.55151, 0.69785, -8.54541, 0.69763, -12.74744, 1.05748, -12.74121, 1.05732, -14.19434, 0.50681, -14.18579, 0.50645, -12.12036, 0.20041, -12.1106, 0.2002, -7.14417, 0.95549, -7.13904, 0.95541, -2.10242, 0.03827, -2.10059, 0.03819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57849, -0.04716, -0.57739, -0.0472], "curve": [1.722, 0, 1.778, 1]}, {"time": 1.8333, "curve": "stepped"}, {"time": 6.1667, "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "offset": 398, "vertices": [-1.06458, -0.08681, -1.06262, -0.08681, -6.74731, -0.43169, -6.74304, -0.43185, -11.50293, -0.46318, -11.49817, -0.46331, -12.23291, -0.2847, -12.22546, -0.28486, -10.35156, -0.01216, -10.34521, -0.0124, -5.8114, -0.23639, -5.80774, -0.23647, -0.43359, -0.26262, -0.43286, -0.26261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.56506, -0.04605, -0.56433, -0.04608, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.46619, 0.2641, -2.46277, 0.26395, -8.55151, 0.69785, -8.54541, 0.69763, -12.74744, 1.05748, -12.74121, 1.05732, -14.19434, 0.50681, -14.18579, 0.50645, -12.12036, 0.20041, -12.1106, 0.2002, -7.14417, 0.95549, -7.13904, 0.95541, -2.10242, 0.03827, -2.10059, 0.03819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57849, -0.04716, -0.57739, -0.0472], "curve": [6.389, 0, 6.444, 1]}, {"time": 6.5, "curve": "stepped"}, {"time": 9.8333, "curve": [9.889, 0, 9.944, 1]}, {"time": 10, "offset": 398, "vertices": [-1.06458, -0.08681, -1.06262, -0.08681, -6.74731, -0.43169, -6.74304, -0.43185, -11.50293, -0.46318, -11.49817, -0.46331, -12.23291, -0.2847, -12.22546, -0.28486, -10.35156, -0.01216, -10.34521, -0.0124, -5.8114, -0.23639, -5.80774, -0.23647, -0.43359, -0.26262, -0.43286, -0.26261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.56506, -0.04605, -0.56433, -0.04608, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.46619, 0.2641, -2.46277, 0.26395, -8.55151, 0.69785, -8.54541, 0.69763, -12.74744, 1.05748, -12.74121, 1.05732, -14.19434, 0.50681, -14.18579, 0.50645, -12.12036, 0.20041, -12.1106, 0.2002, -7.14417, 0.95549, -7.13904, 0.95541, -2.10242, 0.03827, -2.10059, 0.03819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57849, -0.04716, -0.57739, -0.0472], "curve": [10.056, 0, 10.111, 1]}, {"time": 10.1667}]}}, "head2": {"head": {"deform": [{"time": 6.1667, "curve": [6.278, 0, 6.389, 1]}, {"time": 6.5, "offset": 84, "vertices": [1.62927, 1.60512, 1.63098, 1.60455, 2.18469, 1.14285, 2.18579, 1.14237, 0.46118, 0.21664, 0.46204, 0.21625, -1.63562, -0.00952, -1.63513, -0.01012, -3.07214, 0.0643, -3.07129, 0.06342, -2.71765, 0.01183, -2.71692, 0.01096, -3.07214, 0.0643, -3.07129, 0.06342, -1.53406, 0.25543, -1.53308, 0.25491, 0.00537, -0.13229, 0.00671, -0.13271, 1.95923, -0.0786, 1.96021, -0.07912, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.95923, -0.0786, 1.96021, -0.07912, 3.20435, -0.00851, 3.20508, -0.00915, 3.48633, 0.36723, 3.48657, 0.36649, 2.62939, 0.2827, 2.62964, 0.2821, 2.62939, 0.2827, 2.62964, 0.2821, 3.48633, 0.36723, 3.48657, 0.36649, 3.24854, 0.61871, 3.2489, 0.61806, 2.18469, 1.14285, 2.18579, 1.14237, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 2.18469, 1.14285, 2.18579, 1.14237, 0.93604, 0.17911, 0.93616, 0.17899, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -0.69812, 0.33749, -0.69739, 0.33706, 0.93604, 0.17911, 0.93616, 0.17899, 1.95923, -0.0786, 1.96021, -0.07912, 1.82642, -0.63802, 1.82813, -0.63858, -0.79968, 0.07254, -0.79944, 0.07203, 2.37061, 0.25717, 2.37085, 0.25661, 2.04895, 0.28914, 2.04907, 0.28866, 2.04895, 0.28914, 2.04907, 0.28866, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39063, 0.03854, 0.39063, 0.03847, 1.57556, 0.02397, 1.57556, 0.02369, 1.57556, 0.02397, 1.57556, 0.02369, 0.39063, 0.03854, 0.39063, 0.03847], "curve": "stepped"}, {"time": 9.7, "offset": 84, "vertices": [1.62927, 1.60512, 1.63098, 1.60455, 2.18469, 1.14285, 2.18579, 1.14237, 0.46118, 0.21664, 0.46204, 0.21625, -1.63562, -0.00952, -1.63513, -0.01012, -3.07214, 0.0643, -3.07129, 0.06342, -2.71765, 0.01183, -2.71692, 0.01096, -3.07214, 0.0643, -3.07129, 0.06342, -1.53406, 0.25543, -1.53308, 0.25491, 0.00537, -0.13229, 0.00671, -0.13271, 1.95923, -0.0786, 1.96021, -0.07912, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.82642, -0.63802, 1.82813, -0.63858, 1.95923, -0.0786, 1.96021, -0.07912, 3.20435, -0.00851, 3.20508, -0.00915, 3.48633, 0.36723, 3.48657, 0.36649, 2.62939, 0.2827, 2.62964, 0.2821, 2.62939, 0.2827, 2.62964, 0.2821, 3.48633, 0.36723, 3.48657, 0.36649, 3.24854, 0.61871, 3.2489, 0.61806, 2.18469, 1.14285, 2.18579, 1.14237, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 1.62927, 1.60512, 1.63098, 1.60455, 2.18469, 1.14285, 2.18579, 1.14237, 0.93604, 0.17911, 0.93616, 0.17899, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -0.69812, 0.33749, -0.69739, 0.33706, 0.93604, 0.17911, 0.93616, 0.17899, 1.95923, -0.0786, 1.96021, -0.07912, 1.82642, -0.63802, 1.82813, -0.63858, -0.79968, 0.07254, -0.79944, 0.07203, 2.37061, 0.25717, 2.37085, 0.25661, 2.04895, 0.28914, 2.04907, 0.28866, 2.04895, 0.28914, 2.04907, 0.28866, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39063, 0.03854, 0.39063, 0.03847, 1.57556, 0.02397, 1.57556, 0.02369, 1.57556, 0.02397, 1.57556, 0.02369, 0.39063, 0.03854, 0.39063, 0.03847], "curve": [9.9, 0, 10.1, 1]}, {"time": 10.3}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 7.13, "curve": [0.023, 7.13, 0.062, -6.11, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -6.11, "curve": [0.544, -6.12, 0.856, 7.13, 0.731, 0, 0.856, 0]}, {"time": 1.1667, "x": 7.13}]}, "all": {"translate": [{"y": -14.24, "curve": [0.078, 0, 0.156, 0, 0.023, -14.24, 0.062, 18.69]}, {"time": 0.2333, "y": 18.69, "curve": [0.731, 0, 0.856, 0, 0.544, 18.7, 0.856, -14.24]}, {"time": 1.1667, "y": -14.24}]}, "body": {"rotate": [{"value": -2.36, "curve": [0.023, -2.36, 0.062, -7.05]}, {"time": 0.2333, "value": -7.05, "curve": [0.544, -7.05, 0.856, -2.36]}, {"time": 1.1667, "value": -2.36}], "translate": [{"y": -12.29, "curve": [0.078, 0, 0.156, 0, 0.023, -12.29, 0.062, 16.55]}, {"time": 0.2333, "y": 16.55, "curve": [0.731, 0, 0.856, 0, 0.544, 16.56, 0.856, -12.29]}, {"time": 1.1667, "y": -12.29}], "scale": [{"y": 1.05, "curve": [0.078, 1, 0.156, 1, 0.023, 1.05, 0.062, 0.926]}, {"time": 0.2333, "y": 0.926, "curve": [0.731, 1, 0.856, 1, 0.544, 0.925, 0.856, 1.05]}, {"time": 1.1667, "y": 1.05}]}, "body2": {"rotate": [{"value": -4.63, "curve": [0.026, -4.63, 0.071, 0.08]}, {"time": 0.2667, "value": 0.08, "curve": [0.567, 0.08, 0.867, -4.63]}, {"time": 1.1667, "value": -4.63}], "translate": [{"x": -5.31, "curve": [0.026, -5.31, 0.071, 16.23, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 16.24, "curve": [0.567, 16.24, 0.867, -5.31, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -5.31}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.026, 1.003, 0.071, 1.029, 0.026, 1.003, 0.071, 1.029]}, {"time": 0.2667, "x": 1.029, "y": 1.029, "curve": [0.567, 1.029, 0.867, 1.003, 0.567, 1.029, 0.867, 1.003]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": 0.98, "curve": [0.029, 0.98, 0.08, -2.74]}, {"time": 0.3, "value": -2.74, "curve": [0.589, -2.74, 0.878, 0.98]}, {"time": 1.1667, "value": 0.98}]}, "head": {"rotate": [{"value": 0.29, "curve": [0.029, 0.29, 0.08, -2.74]}, {"time": 0.3, "value": -2.74, "curve": [0.589, -2.74, 0.878, 0.29]}, {"time": 1.1667, "value": 0.29}]}, "tun": {"rotate": [{"value": 1.65, "curve": [0.023, 1.65, 0.062, -3.58]}, {"time": 0.2333, "value": -3.58, "curve": [0.544, -3.58, 0.856, 1.65]}, {"time": 1.1667, "value": 1.65}]}, "leg_R3": {"rotate": [{}]}, "sh_L": {"rotate": [{}]}, "arm_L2": {"rotate": [{"value": 0.01}]}, "arm_R": {"rotate": [{"value": -0.03, "curve": [0.033, -0.03, 0.089, -5.1]}, {"time": 0.3333, "value": -5.1, "curve": [0.611, -5.1, 0.889, -0.03]}, {"time": 1.1667, "value": -0.03}]}, "arm_R2": {"rotate": [{"value": -0.58, "curve": [0.033, -0.58, 0.089, 3.37]}, {"time": 0.3333, "value": 3.37, "curve": [0.611, 3.37, 0.889, -0.58]}, {"time": 1.1667, "value": -0.58}]}, "arm_R3": {"rotate": [{"value": -0.09, "curve": [0.098, -2.39, 0.204, -4.72]}, {"time": 0.3, "value": -4.72, "curve": [0.494, -4.72, 0.673, 4.55]}, {"time": 0.8667, "value": 4.55, "curve": [0.964, 4.55, 1.071, 2.24]}, {"time": 1.1667, "value": -0.09}]}, "arm_R4": {"rotate": [{"value": 1.6, "curve": [0.122, -1.11, 0.246, -4.72]}, {"time": 0.3667, "value": -4.72, "curve": [0.561, -4.72, 0.739, 4.55]}, {"time": 0.9333, "value": 4.55, "curve": [1.006, 4.55, 1.095, 3.24]}, {"time": 1.1667, "value": 1.6}]}, "arm_L3": {"rotate": [{"value": -1.68, "curve": [0.05, -2.21, 0.085, -2.61]}, {"time": 0.1333, "value": -2.61, "curve": [0.327, -2.61, 0.539, 3.2]}, {"time": 0.7333, "value": 3.2, "curve": [0.88, 3.2, 1.022, -0.05]}, {"time": 1.1667, "value": -1.68}]}, "arm_L4": {"rotate": [{"value": -0.76, "curve": [0.073, -1.77, 0.161, -2.61]}, {"time": 0.2333, "value": -2.61, "curve": [0.427, -2.61, 0.606, 3.2]}, {"time": 0.8, "value": 3.2, "curve": [0.922, 3.2, 1.046, 0.94]}, {"time": 1.1667, "value": -0.76}]}, "eyebrow_L3": {"translate": [{"curve": [0.023, 0, 0.062, 1.79, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.79, "curve": [0.544, 1.79, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.023, 0, 0.062, -11.41]}, {"time": 0.2333, "value": -11.41, "curve": [0.544, -11.41, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.023, 1, 0.062, 0.952, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.952, "curve": [0.544, 0.952, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L": {"rotate": [{"curve": [0.023, 0, 0.062, -13.63]}, {"time": 0.2333, "value": -13.63, "curve": [0.544, -13.63, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R3": {"translate": [{"curve": [0.023, 0, 0.062, 1.79, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.79, "curve": [0.544, 1.79, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.023, 0, 0.062, 8.23]}, {"time": 0.2333, "value": 8.23, "curve": [0.544, 8.23, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.023, 1, 0.062, 0.952, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.952, "curve": [0.544, 0.952, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R": {"rotate": [{"curve": [0.023, 0, 0.062, 14.06]}, {"time": 0.2333, "value": 14.06, "curve": [0.544, 14.07, 0.856, 0]}, {"time": 1.1667}]}, "headround3": {"translate": [{"x": 47.14, "curve": [0.033, 47.14, 0.088, 273.35, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 273.4, "curve": [0.611, 273.45, 0.889, 47.14, 0.778, 0, 0.889, 0]}, {"time": 1.1667, "x": 47.14}]}, "headround": {"translate": [{"y": -68.22, "curve": [0.111, 0, 0.222, 0, 0.033, -68.22, 0.088, -205.4]}, {"time": 0.3333, "y": -205.43, "curve": [0.778, 0, 0.889, 0, 0.611, -205.47, 0.889, -68.22]}, {"time": 1.1667, "y": -68.22}]}, "sh_L2": {"translate": [{"x": -4.87, "curve": [0.026, -4.87, 0.071, 8.58, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 8.58, "curve": [0.567, 8.58, 0.867, -4.87, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.87}]}, "sh_R2": {"translate": [{"x": -4.87, "curve": [0.026, -4.87, 0.071, 18.26, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 18.26, "curve": [0.567, 18.27, 0.867, -4.87, 0.747, 0, 0.867, 0]}, {"time": 1.1667, "x": -4.87}]}, "arm_LL": {"rotate": [{"value": 0.5}]}, "arm_LL2": {"rotate": [{"value": -0.54}]}, "arm_L5": {"translate": [{"x": 4.78, "y": 5.16, "curve": [0.029, 4.78, 0.08, -17.8, 0.029, 5.16, 0.08, -27.97]}, {"time": 0.3, "x": -17.81, "y": -27.97, "curve": [0.589, -17.81, 0.878, 4.78, 0.589, -27.98, 0.878, 5.16]}, {"time": 1.1667, "x": 4.78, "y": 5.16}]}, "leg_RR": {"rotate": [{"value": 2.22}]}, "leg_RR2": {"rotate": [{"value": -4.5}]}, "leg_R1": {"translate": [{"curve": [0.023, 0, 0.062, 104.99, 0.023, 0, 0.062, 105.22]}, {"time": 0.2333, "x": 105.01, "y": 105.24, "curve": [0.544, 105.04, 0.856, 0, 0.544, 105.27, 0.856, 0]}, {"time": 1.1667}]}, "bodyround": {"translate": [{"y": 67.2, "curve": [0.026, 0, 0.071, 68.05, 0.027, 67.2, 0.07, -261.35]}, {"time": 0.2667, "x": 68.06, "y": -261.41, "curve": [0.567, 68.08, 0.867, 0, 0.567, -261.5, 0.867, 67.2]}, {"time": 1.1667, "y": 67.2}]}, "tunround": {"translate": [{"x": 167.24, "curve": [0.024, 167.24, 0.061, -279.21, 0.023, 0, 0.062, -114.53]}, {"time": 0.2333, "x": -279.28, "y": -114.55, "curve": [0.544, -279.4, 0.856, 167.24, 0.544, -114.58, 0.856, 0]}, {"time": 1.1667, "x": 167.24}]}, "RU_R": {"translate": [{"x": -8.51, "curve": [0.073, -22.7, 0.161, -34.34, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -34.34, "curve": [0.427, -34.34, 0.606, 46.83, 0.427, 0, 0.606, 0]}, {"time": 0.8, "x": 46.85, "curve": [0.922, 46.87, 1.046, 15.31, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -8.5}], "scale": [{"x": 1.087, "y": 0.955, "curve": [0.073, 1.013, 0.161, 0.867, 0.073, 1.007, 0.161, 1.108]}, {"time": 0.2333, "x": 0.867, "y": 1.108, "curve": [0.33, 0.867, 0.403, 1.129, 0.33, 1.108, 0.403, 0.926]}, {"time": 0.5, "x": 1.129, "y": 0.926, "curve": [0.597, 1.129, 0.703, 0.867, 0.597, 0.926, 0.703, 1.108]}, {"time": 0.8, "x": 0.867, "y": 1.108, "curve": [0.897, 0.867, 1.003, 1.129, 0.897, 1.108, 1.003, 0.926]}, {"time": 1.1, "x": 1.129, "y": 0.926, "curve": [1.125, 1.129, 1.143, 1.112, 1.125, 0.926, 1.143, 0.938]}, {"time": 1.1667, "x": 1.087, "y": 0.955}]}, "RU_R2": {"translate": [{"x": 0.6, "curve": [0.098, -15.5, 0.204, -31.81, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -31.81, "curve": [0.494, -31.81, 0.673, 32.99, 0.494, 0, 0.673, 0]}, {"time": 0.8667, "x": 33.01, "curve": [0.964, 33.02, 1.071, 16.92, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 0.61}], "scale": [{"x": 1.129, "y": 0.926, "curve": [0.097, 1.129, 0.203, 0.867, 0.097, 0.926, 0.203, 1.108]}, {"time": 0.3, "x": 0.867, "y": 1.108, "curve": [0.397, 0.867, 0.47, 1.129, 0.397, 1.108, 0.47, 0.926]}, {"time": 0.5667, "x": 1.129, "y": 0.926, "curve": [0.664, 1.129, 0.77, 0.867, 0.664, 0.926, 0.77, 1.108]}, {"time": 0.8667, "x": 0.867, "y": 1.108, "curve": [0.964, 0.867, 1.07, 1.129, 0.964, 1.108, 1.07, 0.926]}, {"time": 1.1667, "x": 1.129, "y": 0.926}]}, "RU_R3": {"translate": [{"x": 15.86, "curve": [0.122, -4.16, 0.246, -30.79, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -30.79, "curve": [0.561, -30.79, 0.739, 37.58, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 37.6, "curve": [1.006, 37.6, 1.095, 27.96, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 15.86}], "scale": [{"x": 1.087, "y": 0.956, "curve": [0.024, 1.111, 0.042, 1.129, 0.024, 0.938, 0.042, 0.926]}, {"time": 0.0667, "x": 1.129, "y": 0.926, "curve": [0.164, 1.129, 0.27, 0.867, 0.164, 0.926, 0.27, 1.108]}, {"time": 0.3667, "x": 0.867, "y": 1.108, "curve": [0.464, 0.867, 0.57, 1.129, 0.464, 1.108, 0.57, 0.926]}, {"time": 0.6667, "x": 1.129, "y": 0.926, "curve": [0.764, 1.129, 0.836, 0.867, 0.764, 0.926, 0.836, 1.108]}, {"time": 0.9333, "x": 0.867, "y": 1.108, "curve": [1.006, 0.867, 1.095, 1.013, 1.006, 1.108, 1.095, 1.006]}, {"time": 1.1667, "x": 1.087, "y": 0.956}]}, "RU_L": {"translate": [{"x": -8.51, "curve": [0.073, -22.7, 0.161, -34.34, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -34.34, "curve": [0.427, -34.34, 0.606, 46.83, 0.427, 0, 0.606, 0]}, {"time": 0.8, "x": 46.85, "curve": [0.922, 46.87, 1.046, 15.31, 0.922, 0, 1.046, 0]}, {"time": 1.1667, "x": -8.5}], "scale": [{"x": 1.087, "y": 0.955, "curve": [0.073, 1.013, 0.161, 0.867, 0.073, 1.007, 0.161, 1.108]}, {"time": 0.2333, "x": 0.867, "y": 1.108, "curve": [0.33, 0.867, 0.403, 1.129, 0.33, 1.108, 0.403, 0.926]}, {"time": 0.5, "x": 1.129, "y": 0.926, "curve": [0.597, 1.129, 0.703, 0.867, 0.597, 0.926, 0.703, 1.108]}, {"time": 0.8, "x": 0.867, "y": 1.108, "curve": [0.897, 0.867, 1.003, 1.129, 0.897, 1.108, 1.003, 0.926]}, {"time": 1.1, "x": 1.129, "y": 0.926, "curve": [1.125, 1.129, 1.143, 1.112, 1.125, 0.926, 1.143, 0.938]}, {"time": 1.1667, "x": 1.087, "y": 0.955}]}, "RU_L2": {"translate": [{"x": 0.6, "curve": [0.098, -15.5, 0.204, -31.81, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -31.81, "curve": [0.494, -31.81, 0.673, 32.99, 0.494, 0, 0.673, 0]}, {"time": 0.8667, "x": 33.01, "curve": [0.964, 33.02, 1.071, 16.92, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 0.61}], "scale": [{"x": 1.129, "y": 0.926, "curve": [0.097, 1.129, 0.203, 0.867, 0.097, 0.926, 0.203, 1.108]}, {"time": 0.3, "x": 0.867, "y": 1.108, "curve": [0.397, 0.867, 0.47, 1.129, 0.397, 1.108, 0.47, 0.926]}, {"time": 0.5667, "x": 1.129, "y": 0.926, "curve": [0.664, 1.129, 0.77, 0.867, 0.664, 0.926, 0.77, 1.108]}, {"time": 0.8667, "x": 0.867, "y": 1.108, "curve": [0.964, 0.867, 1.07, 1.129, 0.964, 1.108, 1.07, 0.926]}, {"time": 1.1667, "x": 1.129, "y": 0.926}]}, "RU_L3": {"translate": [{"x": 15.86, "curve": [0.122, -4.16, 0.246, -30.79, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -30.79, "curve": [0.561, -30.79, 0.739, 37.58, 0.561, 0, 0.739, 0]}, {"time": 0.9333, "x": 37.6, "curve": [1.006, 37.6, 1.095, 27.96, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 15.86}], "scale": [{"x": 1.087, "y": 0.956, "curve": [0.024, 1.111, 0.042, 1.129, 0.024, 0.938, 0.042, 0.926]}, {"time": 0.0667, "x": 1.129, "y": 0.926, "curve": [0.164, 1.129, 0.27, 0.867, 0.164, 0.926, 0.27, 1.108]}, {"time": 0.3667, "x": 0.867, "y": 1.108, "curve": [0.464, 0.867, 0.57, 1.129, 0.464, 1.108, 0.57, 0.926]}, {"time": 0.6667, "x": 1.129, "y": 0.926, "curve": [0.764, 1.129, 0.836, 0.867, 0.764, 0.926, 0.836, 1.108]}, {"time": 0.9333, "x": 0.867, "y": 1.108, "curve": [1.006, 0.867, 1.095, 1.013, 1.006, 1.108, 1.095, 1.006]}, {"time": 1.1667, "x": 1.087, "y": 0.956}]}, "hair_L": {"rotate": [{"value": 3.14, "curve": [0.147, 0.58, 0.288, -4.47]}, {"time": 0.4333, "value": -4.47, "curve": [0.627, -4.47, 0.839, 4.59]}, {"time": 1.0333, "value": 4.59, "curve": [1.083, 4.59, 1.119, 4.01]}, {"time": 1.1667, "value": 3.14}]}, "hair_R": {"rotate": [{"value": -3.02, "curve": [0.147, -0.46, 0.288, 4.59]}, {"time": 0.4333, "value": 4.59, "curve": [0.627, 4.59, 0.839, -4.46]}, {"time": 1.0333, "value": -4.47, "curve": [1.083, -4.47, 1.119, -3.89]}, {"time": 1.1667, "value": -3.02}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.022, 0, 0.066, 0.98]}, {"time": 0.2333, "vertices": [-2.42566, 0.06356, -2.42199, 0.06347, -0.82742, -0.02387, -0.8249, -0.02394, -0.77452, -0.12853, -0.77331, -0.12858, -0.37184, 0.01325, -0.37126, 0.01322, -0.15182, -0.01237, -0.15134, -0.01239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.17345, -0.01413, -0.173, -0.01415, -1.54723, -0.01724, -1.54433, -0.01733, -2.66635, 0.08741, -2.66415, 0.08735, -3.40649, 0.07063, -3.4031, 0.07052, -3.40649, 0.07063, -3.4031, 0.07052, -3.4388, 0.19871, -3.43529, 0.1986, 0, 0, 0, 0, -0.93441, -0.05467, -0.9333, -0.05467, -2.05599, 0.06948, -2.05436, 0.06947, -3.13651, 0.08914, -3.13427, 0.08913, -3.57151, 0.22412, -3.5702, 0.22407, -3.28481, 0.21892, -3.28312, 0.21884, -2.29129, -0.01513, -2.2903, -0.01518, -0.62194, -0.05075, -0.62121, -0.05076, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.2145, -0.0175, -0.21418, -0.01751], "curve": [0.544, 0.03, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.022, 0, 0.066, 0.98]}, {"time": 0.2333, "vertices": [-4.14194, 0.46402, -4.13766, 0.46394, -3.75832, 0.32893, -3.75228, 0.3287, -3.14733, 0.12453, -3.14333, 0.12435, -2.1651, 0.03519, -2.16232, 0.0351, -0.63584, -0.00943, -0.63392, -0.00953, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.52689, -0.04293, -0.52551, -0.043, -0.80456, 0.06393, -0.80226, 0.06385, -0.80747, 0.01889, -0.80574, 0.01884, -1.19323, 0.07212, -1.19119, 0.07208, -2.17028, 0.07715, -2.16728, 0.07703, -3.15215, 0.05607, -3.14809, 0.05599, -0.98241, 0.08931, -0.98114, 0.08926, -0.34223, 0.03564, -0.34153, 0.03561, 0, 0, 0, 0, 0, 0, 0, 0, -2.66433, 0.18505, -2.66158, 0.18496, -3.48246, 0.35123, -3.4801, 0.35113, -3.91732, 0.13838, -3.91518, 0.1383, -3.55804, 0.01498, -3.55606, 0.01494, -2.44048, -0.04646, -2.43882, -0.04652, -1.19965, -0.01063, -1.19853, -0.01066], "curve": [0.544, 0.03, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.022, 0, 0.066, 0.98]}, {"time": 0.2333, "offset": 398, "vertices": [-0.27857, -0.02271, -0.27806, -0.02271, -1.76557, -0.11296, -1.76445, -0.113, -3.00997, -0.1212, -3.00873, -0.12123, -3.20099, -0.0745, -3.19904, -0.07454, -2.7087, -0.00318, -2.70703, -0.00324, -1.52067, -0.06186, -1.51971, -0.06188, -0.11346, -0.06872, -0.11327, -0.06872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14786, -0.01205, -0.14767, -0.01206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.64533, 0.06911, -0.64443, 0.06907, -2.23768, 0.18261, -2.23608, 0.18255, -3.33562, 0.27671, -3.334, 0.27667, -3.71424, 0.13262, -3.712, 0.13252, -3.17154, 0.05244, -3.16898, 0.05239, -1.86942, 0.25002, -1.86807, 0.25, -0.55014, 0.01001, -0.54966, 0.00999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.15137, -0.01234, -0.15109, -0.01235], "curve": [0.544, 0.03, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.022, 0, 0.066, 0.98]}, {"time": 0.2333, "offset": 84, "vertices": [-1.15833, 0.70998, -1.15808, 0.70956, 0.32898, 0.26932, 0.32886, 0.26893, -0.42944, -0.11119, -0.42908, -0.11151, -1.63562, -0.00952, -1.63513, -0.01012, -3.07214, 0.0643, -3.07129, 0.06342, -2.71765, 0.01183, -2.71692, 0.01096, -3.07214, 0.0643, -3.07129, 0.06342, -1.53406, 0.25543, -1.53308, 0.25491, -0.96741, 0.15237, -0.96643, 0.15202, -0.24536, 0.87154, -0.24487, 0.87114, -1.01758, 0.43382, -1.0166, 0.43344, -2.0238, 0.19435, -2.02344, 0.19403, -2.0238, 0.19435, -2.02344, 0.19403, -2.0238, 0.19435, -2.02344, 0.19403, -1.01758, 0.43382, -1.0166, 0.43344, -0.24536, 0.87154, -0.24487, 0.87114, 2.23157, 0.27615, 2.23193, 0.27558, 3.48633, 0.36723, 3.48657, 0.36649, 2.62939, 0.2827, 2.62964, 0.2821, 2.62939, 0.2827, 2.62964, 0.2821, 3.48633, 0.36723, 3.48657, 0.36649, 2.35791, 0.29088, 2.35779, 0.2903, 0.32898, 0.26932, 0.32886, 0.26893, -1.15833, 0.70998, -1.15808, 0.70956, -1.68201, 0.74338, -1.68201, 0.74298, -1.68201, 0.74338, -1.68201, 0.74298, -1.68201, 0.74338, -1.68201, 0.74298, -1.15833, 0.70998, -1.15808, 0.70956, 0.32898, 0.26932, 0.32886, 0.26893, 0.93604, 0.17911, 0.93616, 0.17899, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -2.20203, 0.06241, -2.20154, 0.06161, -0.69812, 0.33749, -0.69739, 0.33706, 0.93604, 0.17911, 0.93616, 0.17899, -0.24536, 0.87154, -0.24487, 0.87114, -1.01758, 0.43382, -1.0166, 0.43344, -0.79968, 0.07254, -0.79944, 0.07203, 2.37061, 0.25717, 2.37085, 0.25661, 2.04895, 0.28914, 2.04907, 0.28866, 2.04895, 0.28914, 2.04907, 0.28866, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 3.05872, 0.38882, 3.05884, 0.38818, 2.7373, 0.3571, 2.73743, 0.35653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39063, 0.03854, 0.39063, 0.03847, 1.57556, 0.02397, 1.57556, 0.02369, 1.57556, 0.02397, 1.57556, 0.02369, 0.39063, 0.03854, 0.39063, 0.03847], "curve": [0.544, 0.03, 0.856, 1]}, {"time": 1.1667}]}}}}}}}