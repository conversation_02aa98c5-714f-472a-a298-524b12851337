﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    //烘培的顶点动画数据
    public class BakedVertexAnimationData : BakedAnimationDataBase
    {
        public Vector3[] cpuDrivenCustomBoneTranslations;
        public Quaternion[] cpuDrivenCustomBoneRotations;
        public Vector3[] cpuDrivenCustomBoneScalings;
        public string[] cpuDrivenCustomBoneNames;
        public int totalFrameCount;

        public override string[] GetCPUDrivenCustomBoneNames()
        {
            return cpuDrivenCustomBoneNames;
        }
    }
}
