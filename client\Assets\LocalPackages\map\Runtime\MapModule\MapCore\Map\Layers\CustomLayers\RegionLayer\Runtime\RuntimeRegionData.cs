﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public enum RegionType
    {
        Outer,
        Inner,
    }

    public class RuntimeRegionData : ModelData
    {
        public RuntimeRegionData(int id, Map map, int number, ModelTemplate modelTemplate, Rect bounds, string borderLinePrefabPath) : base(id, map, 0, Vector3.zero, Quaternion.identity, Vector3.one, modelTemplate, false)
        {
            mBounds = bounds;
            mNumber = number;
            mBorderLinePrefabPath = borderLinePrefabPath;
        }

        public int number { get { return mNumber; } }
        public string borderLinePrefabPath { get { return mBorderLinePrefabPath; } }
        public bool regionMeshVisible { get { return mRegionMeshVisible; } set { mRegionMeshVisible = value; } }
        public bool borderLineVisible { get { return mBorderLineVisible; } set { mBorderLineVisible = value; } }

        int mNumber;
        string mBorderLinePrefabPath;
        bool mRegionMeshVisible = true;
        bool mBorderLineVisible = false;
    }
}
