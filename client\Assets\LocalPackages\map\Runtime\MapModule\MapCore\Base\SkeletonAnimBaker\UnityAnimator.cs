﻿ 



 
 

using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;

namespace TFW.Map
{
    public class UnityAnimator : AnimatorBase
    {
        public UnityAnimator(GameObject obj) : base(obj)
        {
            mAnimator = obj.GetComponent<Animator>();
            var param = mAnimator.parameters;
            mParameters = new AnimationParameterInfo[param.Length];
            for (int i = 0; i < mParameters.Length; ++i)
            {
                mParameters[i] = new AnimationParameterInfo();
                mParameters[i].defaultBool = param[i].defaultBool;
                mParameters[i].defaultFloat = param[i].defaultFloat;
                mParameters[i].defaultInt = param[i].defaultInt;
                mParameters[i].name = param[i].name;
                mParameters[i].nameHash = param[i].nameHash;
                mParameters[i].type = param[i].type;
            }
#if UNITY_EDITOR
            if (mAnimator == null)
            {
                Debug.Assert(false, $"{obj.name} animator not found!");
            }
#endif
        }

        public override void OnEnable() { }
        public override void OnDisable() { }
        public override void OnDestroy() { }

        public override void RegisterAnimationEventCallback(System.Action<string, string> stringEventCallback, System.Action<string, int> intEventCallback, System.Action<string, float> floatEventCallback)
        {
            Debug.Assert(false, "not implemented, please use unity's way");
        }
        public override void UnregisterAnimationEventCallback()
        {
            Debug.Assert(false, "not implemented, please use unity's way");
        }
        public override void UnregisterAnimationIntEventCallback()
        {
            Debug.Assert(false, "not implemented, please use unity's way");
        }
        public override void UnregisterAnimationFloatEventCallback()
        {
            Debug.Assert(false, "not implemented, please use unity's way");
        }
        public override void UnregisterAnimationStringEventCallback()
        {
            Debug.Assert(false, "not implemented, please use unity's way");
        }

        public override bool enableAnimationBlending { get { return true; } }
        public override bool stabilizeFeet { get { return mAnimator.stabilizeFeet; } set { mAnimator.stabilizeFeet = value; } }
        public override Quaternion bodyRotation { get { return mAnimator.bodyRotation; } set { mAnimator.bodyRotation = value; } }
        public override Vector3 bodyPosition { get { return mAnimator.bodyPosition; } set { mAnimator.bodyPosition = value; } }
        public override float gravityWeight { get { return mAnimator.gravityWeight; } }
        public override bool hasTransformHierarchy { get { return mAnimator.hasTransformHierarchy; } }
        public override AnimatorUpdateMode updateMode { get { return mAnimator.updateMode; } set { mAnimator.updateMode = value; } }
        public override bool applyRootMotion { get { return mAnimator.applyRootMotion; } set { mAnimator.applyRootMotion = value; } }
        public override Quaternion rootRotation { get { return mAnimator.rootRotation; } set { mAnimator.rootRotation = value; } }
        public override Vector3 rootPosition { get { return mAnimator.rootPosition; } set { mAnimator.rootPosition = value; } }
        public override Vector3 angularVelocity { get { return mAnimator.angularVelocity; } }
        public override Vector3 velocity { get { return mAnimator.velocity; } }
        public override Quaternion deltaRotation { get { return mAnimator.deltaRotation; } }
        public override Vector3 deltaPosition { get { return mAnimator.deltaPosition; } }
        public override bool isInitialized { get { return mAnimator.isInitialized; } }
        public override float humanScale { get { return mAnimator.humanScale; } }
        public override bool hasRootMotion { get { return mAnimator.hasRootMotion; } }
        public override bool isHuman { get { return mAnimator.isHuman; } }
        public override int layerCount { get { return mAnimator.layerCount; } }
        public override bool isOptimizable { get { return mAnimator.isOptimizable; } }
        public override AnimationParameterInfo[] parameters { get { return mParameters; } }
        public override float feetPivotActive { get { return mAnimator.feetPivotActive; } set { mAnimator.feetPivotActive = value; } }
        public override bool logWarnings { get { return mAnimator.logWarnings; } set { mAnimator.logWarnings = value; } }
        public override float rightFeetBottomHeight { get { return mAnimator.rightFeetBottomHeight; } }
        public override float leftFeetBottomHeight { get { return mAnimator.leftFeetBottomHeight; } }
        public override bool layersAffectMassCenter { get { return mAnimator.layersAffectMassCenter; } set { mAnimator.layersAffectMassCenter = value; } }
        public override PlayableGraph playableGraph { get { return mAnimator.playableGraph; } }
        public override Avatar avatar { get { return mAnimator.avatar; } set { mAnimator.avatar = value; } }
        public override bool hasBoundPlayables { get { return mAnimator.hasBoundPlayables; } }
        public override RuntimeAnimatorController runtimeAnimatorController { get { return mAnimator.runtimeAnimatorController; } set { mAnimator.runtimeAnimatorController = value; } }
        public override AnimatorRecorderMode recorderMode { get { return mAnimator.recorderMode; } }
        public override float recorderStopTime { get { return mAnimator.recorderStopTime; } set { mAnimator.recorderStopTime = value; } }
        public override float recorderStartTime { get { return mAnimator.recorderStartTime; } set { mAnimator.recorderStartTime = value; } }
        public override float playbackTime { get { return mAnimator.playbackTime; } set { mAnimator.playbackTime = value; } }
        public override AnimatorCullingMode cullingMode { get { return mAnimator.cullingMode; } set { mAnimator.cullingMode = value; } }
        public override Quaternion targetRotation { get { return mAnimator.targetRotation; } }
        public override Vector3 targetPosition { get { return mAnimator.targetPosition; } }
        public override float speed { get { return mAnimator.speed; } set { mAnimator.speed = value; } }
        public override bool isMatchingTarget { get { return mAnimator.isMatchingTarget; } }
        public override Vector3 pivotPosition { get { return mAnimator.pivotPosition; } }
        public override float pivotWeight { get { return mAnimator.pivotWeight; } }
        public override int parameterCount { get { return mAnimator.parameterCount; } }
        public override InterruptionType interruptionType { get; set; }
        public override bool fireEvents { get { return mAnimator.fireEvents; } set { mAnimator.fireEvents = value; } }
        public override bool keepAnimatorControllerStateOnDisable {
            get 
            { 
                return mAnimator.keepAnimatorStateOnDisable; 
            } 
            set 
            { 
                mAnimator.keepAnimatorStateOnDisable = value;
            }
        }
        public override void ApplyBuiltinRootMotion()
        {
            mAnimator.ApplyBuiltinRootMotion();
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer, normalizedTimeOffset, normalizedTransitionTime);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer, normalizedTimeOffset);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer, normalizedTimeOffset, normalizedTransitionTime);
        }
        public override void CrossFade(string stateName, float normalizedTransitionDuration, int layer)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration);
        }
        public override void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer, normalizedTimeOffset);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer, fixedTimeOffset);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer, fixedTimeOffset, normalizedTransitionTime);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer);
        }
        public override void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer, fixedTimeOffset);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer, fixedTimeOffset, normalizedTransitionTime);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer);
        }
        public override void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration);
        }
        public override AnimatorTransitionInfo GetAnimatorTransitionInfo(int layerIndex)
        {
            return mAnimator.GetAnimatorTransitionInfo(layerIndex);
        }
        public override T GetBehaviour<T>()
        {
            return mAnimator.GetBehaviour<T>();
        }
        public override StateMachineBehaviour[] GetBehaviours(int fullPathHash, int layerIndex)
        {
            return mAnimator.GetBehaviours(fullPathHash, layerIndex);
        }
        public override T[] GetBehaviours<T>()
        {
            return mAnimator.GetBehaviours<T>();
        }
        public override Transform GetBoneTransform(HumanBodyBones humanBoneId)
        {
            return mAnimator.GetBoneTransform(humanBoneId);
        }
        public override bool GetBool(string name)
        {
            return mAnimator.GetBool(name);
        }
        public override bool GetBool(int id)
        {
            return mAnimator.GetBool(id);
        }
        public override AnimatorClipInfo[] GetCurrentAnimatorClipInfo(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorClipInfo(layerIndex);
        }
        public override void GetCurrentAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips)
        {
            mAnimator.GetCurrentAnimatorClipInfo(layerIndex, clips);
        }
        public override int GetCurrentAnimatorClipInfoCount(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorClipInfoCount(layerIndex);
        }
        public override AnimatorStateInfo GetCurrentAnimatorStateInfo(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorStateInfo(layerIndex);
        }
        public override float GetFloat(int id)
        {
            return mAnimator.GetFloat(id);
        }
        public override float GetFloat(string name)
        {
            return mAnimator.GetFloat(name);
        }
        public override Vector3 GetIKHintPosition(AvatarIKHint hint)
        {
            return mAnimator.GetIKHintPosition(hint);
        }
        public override float GetIKHintPositionWeight(AvatarIKHint hint)
        {
            return mAnimator.GetIKHintPositionWeight(hint);
        }
        public override Vector3 GetIKPosition(AvatarIKGoal goal)
        {
            return mAnimator.GetIKPosition(goal);
        }
        public override float GetIKPositionWeight(AvatarIKGoal goal)
        {
            return mAnimator.GetIKPositionWeight(goal);
        }
        public override Quaternion GetIKRotation(AvatarIKGoal goal)
        {
            return mAnimator.GetIKRotation(goal);
        }
        public override float GetIKRotationWeight(AvatarIKGoal goal)
        {
            return mAnimator.GetIKRotationWeight(goal);
        }
        public override int GetInteger(string name)
        {
            return mAnimator.GetInteger(name);
        }
        public override int GetInteger(int id)
        {
            return mAnimator.GetInteger(id);
        }
        public override int GetLayerIndex(string layerName)
        {
            return mAnimator.GetLayerIndex(layerName);
        }
        public override string GetLayerName(int layerIndex)
        {
            return mAnimator.GetLayerName(layerIndex);
        }
        public override float GetLayerWeight(int layerIndex)
        {
            return mAnimator.GetLayerWeight(layerIndex);
        }
        public override AnimatorClipInfo[] GetNextAnimatorClipInfo(int layerIndex)
        {
            return mAnimator.GetNextAnimatorClipInfo(layerIndex);
        }
        public override void GetNextAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips)
        {
            mAnimator.GetNextAnimatorClipInfo(layerIndex, clips);
        }
        public override int GetNextAnimatorClipInfoCount(int layerIndex)
        {
            return mAnimator.GetNextAnimatorClipInfoCount(layerIndex);
        }
        public override AnimatorStateInfo GetNextAnimatorStateInfo(int layerIndex)
        {
            return mAnimator.GetNextAnimatorStateInfo(layerIndex);
        }
        public override AnimationParameterInfo GetParameter(int index)
        {
            if (index >= 0 && index < mParameters.Length)
            {
                return mParameters[index];
            }
            return null;
        }
        public override bool HasState(int layerIndex, int stateID)
        {
            return mAnimator.HasState(layerIndex, stateID);
        }
        public override void InterruptMatchTarget()
        {
            mAnimator.InterruptMatchTarget();
        }
        public override void InterruptMatchTarget(bool completeMatch)
        {
            mAnimator.InterruptMatchTarget(completeMatch);
        }
        public override bool IsInTransition(int layerIndex)
        {
            return mAnimator.IsInTransition(layerIndex);
        }
        public override bool IsParameterControlledByCurve(int id)
        {
            return mAnimator.IsParameterControlledByCurve(id);
        }
        public override bool IsParameterControlledByCurve(string name)
        {
            return mAnimator.IsParameterControlledByCurve(name);
        }
        public override void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime)
        {
            mAnimator.MatchTarget(matchPosition, matchRotation, targetBodyPart, weightMask, startNormalizedTime);
        }
        public override void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime, float targetNormalizedTime)
        {
            mAnimator.MatchTarget(matchPosition, matchRotation, targetBodyPart, weightMask, startNormalizedTime, targetNormalizedTime);
        }
        public override void Play(string stateName, int layer)
        {
            mAnimator.Play(stateName, layer);
        }
        public override void Play(int stateNameHash, int layer, float normalizedTime)
        {
            mAnimator.Play(stateNameHash, layer, normalizedTime);
        }
        public override void Play(string stateName, int layer, float normalizedTime)
        {
            mAnimator.Play(stateName, layer, normalizedTime);
        }
        public override void Play(string stateName)
        {
            mAnimator.Play(stateName);
        }
        public override void Play(int stateNameHash)
        {
            mAnimator.Play(stateNameHash);
        }
        public override void Play(int stateNameHash, int layer)
        {
            mAnimator.Play(stateNameHash, layer);
        }
        public override void PlayInFixedTime(int stateNameHash, int layer, float fixedTime)
        {
            mAnimator.PlayInFixedTime(stateNameHash, layer, fixedTime);
        }
        public override void PlayInFixedTime(string stateName, int layer, float fixedTime)
        {
            mAnimator.PlayInFixedTime(stateName, layer, fixedTime);
        }
        public override void PlayInFixedTime(int stateNameHash)
        {
            mAnimator.PlayInFixedTime(stateNameHash);
        }
        public override void PlayInFixedTime(int stateNameHash, int layer)
        {
            mAnimator.PlayInFixedTime(stateNameHash, layer);
        }
        public override void PlayInFixedTime(string stateName, int layer)
        {
            mAnimator.PlayInFixedTime(stateName, layer);
        }
        public override void PlayInFixedTime(string stateName)
        {
            mAnimator.PlayInFixedTime(stateName);
        }
        public override void Rebind()
        {
            mAnimator.Rebind();
        }
        public override void ResetTrigger(string name)
        {
            mAnimator.ResetTrigger(name);
        }
        public override void ResetTrigger(int id)
        {
            mAnimator.ResetTrigger(id);
        }
        public override void SetBoneLocalRotation(HumanBodyBones humanBoneId, Quaternion rotation)
        {
            mAnimator.SetBoneLocalRotation(humanBoneId, rotation);
        }
        public override void SetBool(string name, bool value)
        {
            mAnimator.SetBool(name, value);
        }
        public override void SetBool(int id, bool value)
        {
            mAnimator.SetBool(id, value);
        }
        public override void SetFloat(int id, float value)
        {
            mAnimator.SetFloat(id, value);
        }
        public override void SetFloat(string name, float value, float dampTime, float deltaTime)
        {
            mAnimator.SetFloat(name, value, dampTime, deltaTime);
        }
        public override void SetFloat(string name, float value)
        {
            mAnimator.SetFloat(name, value);
        }
        public override void SetFloat(int id, float value, float dampTime, float deltaTime)
        {
            mAnimator.SetFloat(id, value, dampTime, deltaTime);
        }
        public override void SetIKHintPosition(AvatarIKHint hint, Vector3 hintPosition)
        {
            mAnimator.SetIKHintPosition(hint, hintPosition);
        }
        public override void SetIKHintPositionWeight(AvatarIKHint hint, float value)
        {
            mAnimator.SetIKHintPositionWeight(hint, value);
        }
        public override void SetIKPosition(AvatarIKGoal goal, Vector3 goalPosition)
        {
            mAnimator.SetIKPosition(goal, goalPosition);
        }
        public override void SetIKPositionWeight(AvatarIKGoal goal, float value)
        {
            mAnimator.SetIKPositionWeight(goal, value);
        }
        public override void SetIKRotation(AvatarIKGoal goal, Quaternion goalRotation)
        {
            mAnimator.SetIKRotation(goal, goalRotation);
        }
        public override void SetIKRotationWeight(AvatarIKGoal goal, float value)
        {
            mAnimator.SetIKRotationWeight(goal, value);
        }
        public override void SetInteger(int id, int value)
        {
            mAnimator.SetInteger(id, value);
        }
        public override void SetInteger(string name, int value)
        {
            mAnimator.SetInteger(name, value);
        }
        public override void SetLayerWeight(int layerIndex, float weight)
        {
            mAnimator.SetLayerWeight(layerIndex, weight);
        }
        public override void SetLookAtPosition(Vector3 lookAtPosition)
        {
            mAnimator.SetLookAtPosition(lookAtPosition);
        }
        public override void SetLookAtWeight(float weight)
        {
            mAnimator.SetLookAtWeight(weight);
        }
        public override void SetLookAtWeight(float weight, float bodyWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight);
        }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight);
        }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight, eyesWeight);
        }
        public override void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight, float clampWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight, eyesWeight, clampWeight);
        }
        public override void SetTarget(AvatarTarget targetIndex, float targetNormalizedTime)
        {
            mAnimator.SetTarget(targetIndex, targetNormalizedTime);
        }
        public override void SetTrigger(int id)
        {
            mAnimator.SetTrigger(id);
        }
        public override void SetTrigger(string name)
        {
            mAnimator.SetTrigger(name);
        }
        public override void StartPlayback()
        {
            mAnimator.StartPlayback();
        }
        public override void StartRecording(int frameCount)
        {
            mAnimator.StartRecording(frameCount);
        }
        public override void StopPlayback()
        {
            mAnimator.StopPlayback();
        }
        public override void StopRecording()
        {
            mAnimator.StopRecording();
        }
        public override void UpdateAnimator(float deltaTime)
        {
            mAnimator.Update(deltaTime);
        }
        public override void WriteDefaultValues()
        {
            mAnimator.WriteDefaultValues();
        }
        public override bool Update(GlobalAnimationBlendingState state)
        {
            return false;
        }

        public override string[] GetAnimationNames()
        {
            if (mAnimator == null)
            {
                return new string[] { };
            }

            var controller = mAnimator.runtimeAnimatorController;
            var clips = controller.animationClips;
            var ret = new string[clips.Length];
            for (var i = 0; i < ret.Length; i++)
            {
                ret[i] = clips[i].name;
            }

            return ret;
        }

        public override bool IsPlayingAnimation(string aniName)
        {
            if (mAnimator == null)
            {
                return false;
            }

            bool current = mAnimator.GetCurrentAnimatorStateInfo(0).IsName(aniName);
            bool next = mAnimator.GetNextAnimatorStateInfo(0).IsName(aniName);
            var flag = current || next;
            return flag;
        }

        public override float GetCurrentNormalizedTime()
        {
            return mAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime;
        }

        public override string GetCurrentStateName()
        {
#if UNITY_EDITOR
            var controller = mAnimator.runtimeAnimatorController as UnityEditor.Animations.AnimatorController;
            var stateMachine = controller.layers[0].stateMachine;
            var states = stateMachine.states;
            var curState = mAnimator.GetCurrentAnimatorStateInfo(0);
            for (int i = 0; i < states.Length; ++i)
            {
                var name = states[i].state.name;
                if (curState.IsName(name))
                {
                    return name;
                }
            }
            return "";
#else
            Debug.LogError("Not implemented!");
            return "";
#endif
        }

        public override string GetNextStateName()
        {
#if UNITY_EDITOR
            var controller = mAnimator.runtimeAnimatorController as UnityEditor.Animations.AnimatorController;
            var stateMachine = controller.layers[0].stateMachine;
            var states = stateMachine.states;
            var curState = mAnimator.GetNextAnimatorStateInfo(0);
            for (int i = 0; i < states.Length; ++i)
            {
                var name = states[i].state.name;
                if (curState.IsName(name))
                {
                    return name;
                }
            }
            return "";
#else
            Debug.LogError("Not implemented!");
            return "";
#endif
        }

        //unity的参数是只读的,无法模拟,只能使用不同的数据结构
        AnimationParameterInfo[] mParameters;
        Animator mAnimator;
    }
}
