﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveMapCollisionAttribute : EditorAction
    {
        public ActionRemoveMapCollisionAttribute(int layerID, MapCollisionData data, CollisionAttribute attribute)
        {
            mLayerID = layerID;
            mAttribute = attribute;
            mCollisionDataID = data.id;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.RemoveCollisionAttribute(mCollisionDataID, mAttribute);
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.AddCollisionAttribute(mCollisionDataID, mAttribute);
        }

        int mCollisionDataID;
        int mLayerID;
        CollisionAttribute mAttribute;
    }
}


#endif