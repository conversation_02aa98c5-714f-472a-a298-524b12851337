﻿ 



 
 


//#define SHOW_TILE_OBJECT_BOUNDS

using UnityEngine;

namespace TFW.Map
{
    public class TileObjectData2 : BaseObject, IMapObjectData
    {
        public TileObjectData2(int id, Map map) : base(id, map)
        {
        }

        public void Set(int id, int viewID, TileObjectType type, BigTileChildPrefabData2 prefabData, string prefabPath, Rect worldBounds, Vector3 position, Vector3 scaling, Quaternion rotation, int localIndex, int tileIndex, int lod, float baseScale, PrefabInitInfo2 prefabInfo, System.Action<TileObjectData2> onObjectScaleChangeCallback, bool useCullManager, TileGridObjectLayerData2.SpecialArea area)
        {
            int x = (int)position.x;
            int z = (int)position.z;

            mPrefabInfo = prefabInfo;
            mSpecialArea = area;

            this.id = id;
            mViewID = viewID;
            mPrefabData = prefabData;
            mPrefabPath = prefabPath;
            this.worldBounds = worldBounds;
            mLOD = lod;
            mIsActive = false;
            mLocalIndex = localIndex;
            mObjectType = type;
            mTileIndex = tileIndex;
            mBaseScale = baseScale;
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;

            mPosition = position;
            mRotation = rotation;
            mScale = scaling;

            mUseCullManager = useCullManager;

#if SHOW_TILE_OBJECT_BOUNDS
            //if (mPrefabPath.IndexOf("Spacestation_lod0_01") >= 0)
            {
                if (mDebugObject == null)
                {
                    mDebugObject = new GameObject("tile object debug");
                    mDebugObject.AddComponent<DrawBounds>();
                }
                var drawBounds = mDebugObject.GetComponent<DrawBounds>();
                drawBounds.bounds = Utils.RectToBounds(this.worldBounds);
                mDebugObject.transform.position = drawBounds.bounds.center;
                mDebugObject.name = Utils.GetPathName(prefabPath, false);
            }
#endif
        }

        public override void OnDestroy()
        {
            mIsActive = false;
        }

        public BigTileChildPrefabData2 GetPrefabData()
        {
            return mPrefabData;
        }
        public bool IsDynamicEntity() { return false; }
        public int GetEntityID() { return id; }
        public ModelTemplate GetModelTemplate() { Debug.Assert(false, "Can't be here"); return null; }
        public int GetModelTemplateID() { Debug.Assert(false, "Can't be here"); return 0; }
        //当Entity被隐藏时调用
        public void OnHide() { }
        //当Entity被显示时调用
        public void OnShow() { }
        //当Entity的GameObject创建完成时调用
        public void OnInit(GameObject obj) { }
        //当LOD Zoom变化时被调用
        public void OnZoomChange(float zoom) { }
        //Entity是否忽略视野范围的管理,如果为true,则不管视野框如何变化,Entity的显示状态都不会被改变
        public bool IgnoreViewport() { return false; }
        //Entity的prefab路径
        public string GetAssetPath(int lod = 0)
        {
            return mPrefabPath;
        }
        
        //改变物体类型时使用
        public void SetAssetPath(string path)
        {
            mPrefabPath = path;
        }
        
        //返回bounding box
        public Rect GetBounds()
        {
            return worldBounds;
        }

        //Entity的位置
        public Vector3 GetPosition()
        {
            return mPosition;
        }
        public void SetPosition(Vector3 pos)
        {
            mPosition = pos;
        }
        //Entity的缩放
        public Vector3 GetScale()
        {
            return mScale;
        }
        public void SetScale(Vector3 scale)
        {
            mScale = scale;
        }
        public void SetScale(float scale)
        {
            mScale = Vector3.one * scale;
            mOnObjectScaleChangeCallback(this);
        }
        //Entity的旋转
        public Quaternion GetRotation()
        {
            return mRotation;
        }
        public void SetRotation(Quaternion rot)
        {
            mRotation = rot;
        }

        public bool HasFlag(int flag)
        {
            return HasObjectFlag((ObjectFlag)flag);
        }

        public void SetFlag(int flag)
        {
            SetObjectFlag((ObjectFlag)flag);
        }

        public void AddFlag(int flag)
        {
            AddObjectFlag((ObjectFlag)flag);
        }

        public void RemoveFlag(int flag)
        {
            RemoveObjectFlag((ObjectFlag)flag);
        }

        public int GetFlag()
        {
            return (int)flag;
        }

        public int GetModelLODGroupID()
        {
            return 0;
        }

        public bool IsObjActive() { return mIsActive; }
        public bool SetObjActive(bool active)
        {
            if (mIsActive != active)
            {
                mIsActive = active;
                return true;
            }
            return false;
        }

        public int localIndex { get { return mLocalIndex; } }
        public int tileIndex { get { return mTileIndex; } }
        public TileObjectType objectType { get { return mObjectType; } }
        public int lod { get { return mLOD; } }
        public float baseScale { get { return mBaseScale; } }
        public long cullObjectID { set { mCullObjectID = value; } get { return mCullObjectID; } }
        public int viewID { get { return mViewID; } }
        public bool collidesWithNPC { get { return mCollidesWithNPC; } set { mCollidesWithNPC = value; } }
        public bool useCullManager { get { return mUseCullManager; } }
        public PrefabInitInfo2 prefabInfo { get { return mPrefabInfo; } }
        public TileGridObjectLayerData2.SpecialArea specialArea { get { return mSpecialArea; } }

        BigTileChildPrefabData2 mPrefabData;
        TileGridObjectLayerData2.SpecialArea mSpecialArea;

        public Rect worldBounds;
        int mLocalIndex;
        int mTileIndex;
        int mLOD;

        float mBaseScale;
        PrefabInitInfo2 mPrefabInfo;

        long mCullObjectID;

        Vector3 mPosition;
        Vector3 mScale;
        Quaternion mRotation;
        string mPrefabPath;

        int mViewID;

        System.Action<TileObjectData2> mOnObjectScaleChangeCallback;

        bool mIsActive = false;
        bool mUseCullManager;
        //是否与npc碰撞
        bool mCollidesWithNPC = false;
        //TileObject的类型
        TileObjectType mObjectType;

#if SHOW_TILE_OBJECT_BOUNDS
        //temp code
        GameObject mDebugObject;
#endif
    }
}
