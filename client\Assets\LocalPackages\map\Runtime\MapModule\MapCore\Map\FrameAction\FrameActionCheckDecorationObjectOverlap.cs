﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //判断地图上的装饰物与npc是否重叠
    public class FrameActionCheckDecorationObjectOverlap : FrameAction
    {
        public static FrameActionCheckDecorationObjectOverlap Require(TileGridObjectLayerData layerData, long npcID, Vector3 center, float radius)
        {
            var act = mPool.Require();
            act.Init(layerData, npcID, center, radius);
            return act;
        }

        void Init(TileGridObjectLayerData layerData, long npcID, Vector3 center, float radius)
        {
            InitAction();

            mCenter = center;
            mRadius = radius;
            mNPCID = npcID;
            mLayerData = layerData;
        }

        protected override void DoImpl()
        {
            mLayerData.HideDecorationObject(mNPCID, mCenter, mRadius);
        }

        protected override void OnDestroyImpl() {
            mPool.Release(this);
        }

        public static long MakeActionKey(int id)
        {
            return id;
        }

        public override long key { get { return mNPCID; } }
        public override FrameActionType type => FrameActionType.CheckDecorationObjectOverlap;
        public override string debugInfo
        {
            get
            {
                return "check overlap";
            }
        }
        public override string name => "Check Decoration Object Overlap";

        static ObjectPool<FrameActionCheckDecorationObjectOverlap> mPool = new ObjectPool<FrameActionCheckDecorationObjectOverlap>(1000, () => new FrameActionCheckDecorationObjectOverlap());

        float mRadius;
        Vector3 mCenter;
        long mNPCID;
        TileGridObjectLayerData mLayerData;
    }
}
