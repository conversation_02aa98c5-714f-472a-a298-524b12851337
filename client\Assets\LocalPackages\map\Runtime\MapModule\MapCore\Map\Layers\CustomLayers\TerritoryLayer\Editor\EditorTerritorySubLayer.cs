﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using System.Text;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class EditorTerritorySubLayer
    {
        public void OnDestroy()
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                mTerritories[i].OnDestroy();
            }
            mTerritories = null;
            mGrids = null;

            DestroyTexturePlane();
            DestroyPreviewObjects();

            Utils.DestroyObject(mTileRoot);

            for (int i = 0; i < mCreatorsForLODs.Count; ++i)
            {
                mCreatorsForLODs[i].OnDestroy();
            }
        }

        public void Load(config.EditorTerritorySubLayerData subLayerData, Transform parent)
        {
            int rows = subLayerData.zTileCount;
            int cols = subLayerData.xTileCount;
            mGrids = new int[rows, cols];
            mGridWidth = subLayerData.tileWidth;
            mGridHeight = subLayerData.tileHeight;
            mHorizontalGridCount = cols;
            mVerticalGridCount = rows;
            if (subLayerData.meshGenerationParams == null)
            {
                subLayerData.meshGenerationParams = new config.TerritoryMeshGenerationParam[1] { new config.TerritoryMeshGenerationParam() };
            }

            for (int i = 0; i < subLayerData.meshGenerationParams.Length; ++i)
            {
                EditorTerritoryMeshGenerationParam meshGenerationParam = new EditorTerritoryMeshGenerationParam(subLayerData.meshGenerationParams[i].cornerSegment, subLayerData.meshGenerationParams[i].borderSizeRatio, subLayerData.meshGenerationParams[i].uvScale, subLayerData.meshGenerationParams[i].curveCorner, subLayerData.meshGenerationParams[i].territoryMeshMaterialGuid);

                mMeshGenerationParamForLODs.Add(meshGenerationParam);
            }

            mCurveRegionMeshGenerationParam = subLayerData.curveRegionMeshGenerationParam;
            if (mCurveRegionMeshGenerationParam == null)
            {
                float vertexDisplayRadius = 0.5f;
                float segmentLengthRatioRandomRange = 0.1f;
                float tangentRotationRandomRange = 20f;
                var paramLOD0 = GetDefaultLODParam(true);
                var paramLOD1 = GetDefaultLODParam(false);

                mCurveRegionMeshGenerationParam = new CurveRegionMeshGenerationParam(vertexDisplayRadius, segmentLengthRatioRandomRange, tangentRotationRandomRange, new List<CurveRegionMeshGenerationLODParam>() { paramLOD0, paramLOD1});
            }

            mTileRoot = new GameObject(subLayerData.name);
            mTileRoot.transform.SetParent(parent, true);
            mTileRoot.AddComponent<DisableKeyboardDelete>();
            mTileRoot.SetActive(false);

            if (subLayerData.territories != null)
            {
                for (int i = 0; i < subLayerData.territories.Length; ++i)
                {
                    var territory = subLayerData.territories[i];
                    AddTerritory(territory.name, territory.id, territory.color, territory.properties, territory.buildingPositions);
                }
            }

            if (subLayerData.grids != null)
            {
                for (int i = 0; i < mVerticalGridCount; ++i)
                {
                    for (int j = 0; j < mHorizontalGridCount; ++j)
                    {
                        mGrids[i, j] = subLayerData.grids[i, j];
                    }
                }
            }

            CreateTexturePlane(mHorizontalGridCount * mGridWidth, mVerticalGridCount * mGridHeight);

            displayRadius = subLayerData.displayRadius;
            //active = subLayerData.active;

            mExportFileName = subLayerData.exportFileName;

            for (int i = 0; i < mCurveRegionMeshGenerationParam.lodParams.Count; ++i)
            {
                mCreatorsForLODs.Add(new CurveRegionCreator(mTileRoot.transform));
            }
            List<CurveRegionCreator.EdgeAssetInfo> edges = new List<CurveRegionCreator.EdgeAssetInfo>();
            for (int i = 0; i < subLayerData.sharedEdges.Length; ++i)
            {
                var data = subLayerData.sharedEdges[i];
                var edge = new CurveRegionCreator.EdgeAssetInfo(data.territoryID, data.neighbourTerritoryID, data.prefabPath, data.material);
                edges.Add(edge);
            }
            //only need lod0 edge assets info
            mCreatorsForLODs[0].edgeAssetsInfo = edges;
            if (mCreatorsForLODs.Count > 1)
            {
                List<CurveRegionCreator.Block> blocks = new List<CurveRegionCreator.Block>();
                for (int i = 0; i < subLayerData.blocks.Length; ++i)
                {
                    var data = subLayerData.blocks[i];
                    var block = new CurveRegionCreator.Block();
                    block.bounds = data.bounds;
                    block.prefabPath = data.prefabPath;
                    block.edges = new List<CurveRegionCreator.BlockEdge>();
                    foreach (var edge in data.edges)
                    {
                        var blockEdge = new CurveRegionCreator.BlockEdge();
                        blockEdge.territoryID = edge.territoryID;
                        blockEdge.neighbourTerritoryID = edge.neighbourTerritoryID;
                        block.edges.Add(blockEdge);
                    }
                    block.regions = new List<CurveRegionCreator.BlockRegion>();
                    foreach (var region in data.regions)
                    {
                        var blockRegion = new CurveRegionCreator.BlockRegion();
                        blockRegion.territoryID = region.territoryID;
                        block.regions.Add(blockRegion);
                    }
                    blocks.Add(block);
                }
                //只是为了导出时能有数据
                mCreatorsForLODs[1].blocks = blocks;
                mCreatorsForLODs[1].lod1MaskTextureWidth = subLayerData.maskTextureWidth;
                mCreatorsForLODs[1].lod1MaskTextureHeight = subLayerData.maskTextureHeight;
                mCreatorsForLODs[1].lod1MaskTextureData = subLayerData.maskTextureData;
            }
        }

        public void SetGrid(Vector3 pos, int brushSize, int type)
        {
            if (showGrid == false)
            {
                return;
            }

            Vector2Int coord = FromPositionToCoordinate(pos);
            int startX = coord.x - brushSize / 2;
            int startY = coord.y - brushSize / 2;
            int endX = startX + brushSize - 1;
            int endY = startY + brushSize - 1;

            if (endX < 0 || endY < 0 || startX >= mHorizontalGridCount || startY >= mVerticalGridCount)
            {
                return;
            }

            startX = Mathf.Clamp(startX, 0, mHorizontalGridCount - 1);
            startY = Mathf.Clamp(startY, 0, mVerticalGridCount - 1);
            endX = Mathf.Clamp(endX, 0, mHorizontalGridCount - 1);
            endY = Mathf.Clamp(endY, 0, mVerticalGridCount - 1);

            int width = endX - startX + 1;
            int height = endY - startY + 1;
            var pixels = mColorArrayPool.Rent(width * height);
            int idx = 0;
            var template = GetTerritory(type);
            Color32 color = new Color32(0, 0, 0, 0);
            if (template != null)
            {
                color = template.GetColor();
            }
            for (int i = startY; i <= endY; ++i)
            {
                for (int j = startX; j <= endX; ++j)
                {
                    mGrids[i, j] = type;
                    pixels[idx] = color;
                    ++idx;
                }
            }
            SetPixels(startX, startY, width, height, pixels);
            mColorArrayPool.Return(pixels);
        }

        public void SetGridData(int x, int y, int type)
        {
            if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
            {
                if (mGrids[y, x] != type)
                {
                    mGrids[y, x] = type;
                }
            }
        }

        public int GetGridData(int x, int y)
        {
            if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
            {
                return mGrids[y, x];
            }
            return 0;
        }

        public int GetGridData(Vector3 pos)
        {
            var coord = FromPositionToCoordinate(pos);
            if (coord.x >= 0 && coord.x < mHorizontalGridCount && coord.y >= 0 && coord.y < mVerticalGridCount)
            {
                return mGrids[coord.y, coord.x];
            }
            return 0;
        }

        public EditorTerritory FindTerritory(int type)
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                if (mTerritories[i].id == type)
                {
                    return mTerritories[i];
                }
            }
            return null;
        }

        public void AddBuilding(Vector3 pos, int type)
        {
            var territory = FindTerritory(type);
            if (territory != null)
            {
                var building = new EditorTerritoryBuilding(type, mTileRoot, pos, territory.GetColor(), mDisplayRadius);
                territory.AddBuilding(building);
            }
        }

        public EditorTerritory AddTerritory(string name, int type, Color color, PropertyDatas properties, Vector3[] buildingPositions)
        {
            var template = new EditorTerritory(type, name, color, properties);
            mTerritories.Add(template);

            if (buildingPositions != null)
            {
                for (int i = 0; i < buildingPositions.Length; ++i)
                {
                    var building = new EditorTerritoryBuilding(type, mTileRoot, buildingPositions[i], color, mDisplayRadius);
                    template.AddBuilding(building);
                }
            }

            return template;
        }

        public void RemoveTerritory(int index)
        {
            if (index >= 0 && index < mTerritories.Count)
            {
                mTerritories.RemoveAt(index);
            }
        }

        public EditorTerritory GetTerritory(int type)
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                if (mTerritories[i].id == type)
                {
                    return mTerritories[i];
                }
            }
            return null;
        }

        public bool RemoveTerritoryBuilding(EditorTerritoryBuilding building, bool destroyGameObject)
        {
            var territory = FindTerritory(building.id);
            if (territory != null)
            {
                return territory.RemoveBuilding(building, destroyGameObject);
            }
            return false;
        }

        public Vector3 FromCoordinateToPositionCenter(int x, int y)
        {
            return new Vector3(x * mGridWidth + mGridWidth * 0.5f, 0, y * mGridHeight + mGridHeight * 0.5f);
        }

        public Vector3 FromCoordinateToPosition(int x, int y)
        {
            return new Vector3(x * mGridWidth, 0, y * mGridHeight);
        }

        public Vector2Int FromPositionToCoordinate(Vector3 pos)
        {
            return new Vector2Int(Mathf.FloorToInt(pos.x / mGridWidth), Mathf.FloorToInt(pos.z / mGridHeight));
        }

        public Vector3 GetTerritoryCenter(int id)
        {
            var bounds = GetTerritoryWorldBounds(id);
            var center = bounds.center;
            return new Vector3(center.x, 0, center.y);
        }

        public Rect GetTerritoryWorldBounds(int id)
        {
            //注意,目前只对整数坐标有效
            PackRect r = new PackRect(0, 0, 0, 0);
            var coords = GetTerritoryCoordinates(id);
            for (int i = 0; i < coords.Count; ++i)
            {
                var minPos = FromCoordinateToPosition(coords[i].x, coords[i].y);
                var maxPos = FromCoordinateToPosition(coords[i].x + 1, coords[i].y + 1);
                r.Add(new PackRect((int)minPos.x, (int)minPos.z, (int)(maxPos.x - minPos.x), (int)(maxPos.z - minPos.z)));
            }

            return new Rect(r.minX, r.minY, r.width, r.height);
        }

        public bool HasRegionData()
        {
            for (int i = 0; i < mVerticalGridCount; ++i)
            {
                for (int j = 0; j < mHorizontalGridCount; ++j)
                {
                    if (mGrids[i, j] != 0)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public Color GetRegionColor(int id)
        {
            var t = FindTerritory(id);
            if (t != null)
            {
                return t.GetColor();
            }
            return Color.white;
        }

        public List<Vector2Int> GetTerritoryCoordinates(int id)
        {
            List<Vector2Int> coordinates = new List<Vector2Int>();
            for (int i = 0; i < mVerticalGridCount; ++i)
            {
                for (int j = 0; j < mHorizontalGridCount; ++j)
                {
                    if (mGrids[i, j] == id)
                    {
                        coordinates.Add(new Vector2Int(j, i));
                    }
                }
            }
            return coordinates;
        }

        string ConvertToStringList(List<Vector2Int> coordinates)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("[");
            for (int i = 0; i < coordinates.Count; ++i)
            {
                int v = coordinates[i].x * 10000 + coordinates[i].y;
                builder.Append(v);
                if (i != coordinates.Count - 1)
                {
                    builder.Append(",");
                }
            }
            builder.Append("]");
            return builder.ToString();
        }

        public bool RemoveGridOfType(int templateType)
        {
            EditorTerritory template = FindTerritory(templateType);
            if (template == null)
            {
                return false;
            }
            int rows = mGrids.GetLength(0);
            int cols = mGrids.GetLength(1);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (mGrids[i, j] == template.id)
                    {
                        SetGridData(j, i, 0);
                    }
                }
            }

            RefreshTexture();
            return true;
        }

        //only import coordinates
        public void Import(string filePath)
        {
            bool suc = TSVReader.Load(filePath);
            if (suc)
            {
                var rows = TSVReader.rows;
                for (int i = 0; i < rows.Count; ++i)
                {
                    bool ok;
                    int id = (int)TSVReader.GetInt(i, "id", out ok);
                    var coordinates = TSVReader.GetArray(i, "INT_piece_poses", out ok);
                    ok = RemoveGridOfType(id);
                    if (ok)
                    {
                        foreach(var p in coordinates)
                        {
                            int coord = System.Convert.ToInt32(p);
                            int x = coord / 10000;
                            int y = coord % 10000;
                            SetGridData(x, y, id);
                        }
                        RefreshTexture();
                    }
                }
            }
        }

        public void Export(string folder)
        {
            string filePath = $"{folder}/{mExportFileName}.tsv";

            StringBuilder builder = new StringBuilder();

            var headers = new List<string> {
                "A_INT_id",
                "A_ARR_INT_piece_poses",
                "A_MAP_Coord_position",
            };

            if (mTerritories.Count > 0)
            {
                var properties = mTerritories[0].properties;
                int propCount = properties.GetPropertyCount();
                for (int i = 0; i < propCount; ++i)
                {
                    var prop = properties.GetProperty(i);
                    string typeName = Utils.ConvertToServerTypeName(prop.type);
                    headers.Add($"A_{typeName}_{prop.name}");
                }
            }

            for (int i = 0; i < headers.Count; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Count - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            for (int i = 0; i < mTerritories.Count; ++i)
            {
                var properties = territories[i].properties;
                int propCount = properties.GetPropertyCount();

                builder.AppendFormat("{0}\t", territories[i].id);
                List<Vector2Int> coordinates = GetTerritoryCoordinates(territories[i].id);
                string list = ConvertToStringList(coordinates);
                builder.AppendFormat("{0}\t", list);

                Vector3 pos = Vector3.zero;
                var buildings = territories[i].buildings;
                if (buildings.Count > 0)
                {
                    pos = buildings[0].gameObject.transform.position;
                }
                var s = string.Format("\"x\":{0},\"z\":{1}", Utils.F2I(pos.x), Utils.F2I(pos.z));
                s = "{" + s + "}" + (propCount == 0 ? "\n" : "\t");
                builder.Append(s);

                for (int p = 0; p < propCount; ++p)
                {
                    var property = properties.GetProperty(p);
                    switch (property.type)
                    {
                        case PropertyType.kPropertyFloat:
                            builder.AppendFormat("{0}{1}", (property as PropertyData<float>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                            break;
                        case PropertyType.kPropertyInt:
                            builder.AppendFormat("{0}{1}", (property as PropertyData<int>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                            break;
                        case PropertyType.kPropertyIntArray:
                            builder.AppendFormat("{0}{1}", Utils.IntArrayToStringList((property as PropertyData<int[]>).value), p == propCount - 1 ? '\n' : '\t');
                            break;
                        case PropertyType.kPropertyString:
                            builder.AppendFormat("{0}{1}", (property as PropertyData<string>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                            break;
                        default:
                            Debug.Assert(false, "not supported now!");
                            break;
                    }
                }
            }

            var str = builder.ToString();
            File.WriteAllText(filePath, str);
        }

        public EditorTerritoryMeshGenerationParam GetMeshGenerationParam(int lod)
        {
            if (lod >= 0 && lod < mMeshGenerationParamForLODs.Count)
            {
                return mMeshGenerationParamForLODs[lod];
            }
            return null;
        }

        public void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            if (newLODCount > oldLODCount)
            {
                int delta = newLODCount - oldLODCount;
                for (int i = 0; i < delta; ++i)
                {
                    mMeshGenerationParamForLODs.Add(new EditorTerritoryMeshGenerationParam(3, 0.3f, 10, false, ""));
                    var param = GetDefaultLODParam(false);
                    mCurveRegionMeshGenerationParam.lodParams.Add(param);
                    mCreatorsForLODs.Add(new CurveRegionCreator(mTileRoot.transform));
                }
            }
            else
            {
                int delta = oldLODCount - newLODCount;
                for (int i = 0; i < delta; ++i)
                {
                    mMeshGenerationParamForLODs.RemoveAt(mMeshGenerationParamForLODs.Count - 1);
                    mCurveRegionMeshGenerationParam.lodParams.RemoveAt(mCurveRegionMeshGenerationParam.lodParams.Count - 1);
                    mCreatorsForLODs[mCreatorsForLODs.Count - 1].OnDestroy();
                    mCreatorsForLODs.RemoveAt(mCreatorsForLODs.Count - 1);
                }
            }
        }

        public short GetTerritoryIndex(int id)
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                if (id == mTerritories[i].id)
                {
                    return (short)i;
                }
            }
            return -1;
        }

        public bool active
        {
            get
            {
                return mTileRoot.activeSelf;
            }

            set
            {
                if (mTileRoot.activeSelf != value)
                {
                    mTileRoot.SetActive(value);
                }
            }
        }

        public List<EditorTerritory> territories { get { return mTerritories; } }
        public int territoryCount { get { return mTerritories.Count; } }
        public int[,] grids { get { return mGrids; } }

        
        //显示地图层中所有的对象,而不考虑视野的范围.给编辑器使用
        //返回地图层的总宽度
        public float GetTotalWidth() { return mHorizontalGridCount * mGridWidth; }
        //返回地图层的总高度
        public float GetTotalHeight() { return mVerticalGridCount * mGridHeight; }

        //地图层的名称
        public string name { get { return mTileRoot.name; } }

        //x方向上格子的数量
        public int horizontalTileCount { get { return mHorizontalGridCount; } }
        //z方向上格子的数量
        public int verticalTileCount { get { return mVerticalGridCount; } }
        //格子的宽
        public float tileWidth { get { return mGridWidth; } }
        //格子的高
        public float tileHeight { get { return mGridHeight; } }

        public GameObject gameObject => mTileRoot;

        public float displayRadius
        {
            get { return mDisplayRadius; }
            set
            {
                mDisplayRadius = value;
                for (int i = 0; i < mTerritories.Count; ++i)
                {
                    mTerritories[i].SetRadius(mDisplayRadius);
                }
            }
        }

        public bool showGrid
        {
            get
            {
                return mPlaneObject.activeSelf;
            }
            set
            {
                if (mPlaneObject.activeSelf != value)
                {
                    mPlaneObject.SetActive(value);
                }
            }
        }

        public List<EditorTerritoryMeshGenerationParam> meshGenerationParams { get { return mMeshGenerationParamForLODs; } }
        public string exportFileName { get { return mExportFileName; } }
        public CurveRegionMeshGenerationParam curveRegionMeshGenerationParam { get { return mCurveRegionMeshGenerationParam; } }

        List<EditorTerritory> mTerritories = new List<EditorTerritory>();
        int[,] mGrids;
        float mGridWidth;
        float mGridHeight;
        int mHorizontalGridCount;
        int mVerticalGridCount;
        float mDisplayRadius = 10;
        string mExportFileName;
        GameObject mTileRoot;
        List<EditorTerritoryMeshGenerationParam> mMeshGenerationParamForLODs = new List<EditorTerritoryMeshGenerationParam>();
        ArrayPool<Color32> mColorArrayPool = ArrayPool<Color32>.Create();

        CurveRegionMeshGenerationParam mCurveRegionMeshGenerationParam;

        //editor data
        public int brushSize = 1;
        public EditorTerritoryLayerOperation operation = EditorTerritoryLayerOperation.SetGrid;
        public int selectedIndex = -1;
        public int selectedLODIndex = 0;
    }
}

#endif