﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    class TemplateParser
    {
        class TextNode
        {
            public TextNode(string text)
            {
                this.text = text;
            }
            public string text;
        }

        public void SetReplacement(string name, string value)
        {
            mReplacements[name] = value;
        }
        public void SetInput(string input)
        {
            mText = input;
        }
        public string Generate()
        {
            CreateTextNodes();
            ReplaceTemplateNode();
            string text = Combine();
            Clear();
            return text;
        }

        void Clear()
        {
            mAllNodes.Clear();
            mReplacementNodes.Clear();
        }
        void CreateTextNodes()
        {
            int startPos = 0;
            while (true)
            {
                int pos = (int)mText.IndexOf("$", startPos);
                if (pos != -1)
                {
                    var plainTextNode = new TextNode(mText.Substring(startPos, pos - startPos));
                    mAllNodes.Add(plainTextNode);

                    startPos = pos + 1;
                    pos = (int)mText.IndexOf("$", startPos);
                    Debug.Assert(pos != -1);
                    var replacementNode = new TextNode(mText.Substring(startPos, pos - startPos));
                    mAllNodes.Add(replacementNode);
                    AddReplacementNode(replacementNode.text, replacementNode);
                    startPos = pos + 1;
                }
                else
                {
                    var plainTextNode = new TextNode(mText.Substring(startPos));
                    mAllNodes.Add(plainTextNode);
                    break;
                }
            }
        }
        void ReplaceTemplateNode()
        {
            foreach (var pair in mReplacementNodes)
            {
                var nodes = pair.Value;
                string value = GetReplacement(pair.Key);
                foreach (var node in nodes)
                {
                    node.text = value;
                }
            }
        }
        string GetReplacement(string templateText)
        {
            string replacement = null;
            mReplacements.TryGetValue(templateText, out replacement);
            Debug.Assert(replacement != null);
            return replacement;
        }
        string Combine()
        {
            string text = "";
            foreach (var n in mAllNodes)
            {
                text += n.text;
            }
            return text;
        }
        void AddReplacementNode(string name, TextNode node)
        {
            List<TextNode> nodes;
            mReplacementNodes.TryGetValue(name, out nodes);
            if (nodes == null)
            {
                nodes = new List<TextNode>();
                mReplacementNodes.Add(name, nodes);
            }
            nodes.Add(node);
        }

        string mText;
        Dictionary<string, string> mReplacements = new Dictionary<string, string>();
        List<TextNode> mAllNodes = new List<TextNode>();
        Dictionary<string, List<TextNode>> mReplacementNodes = new Dictionary<string, List<TextNode>>();
    };
}
