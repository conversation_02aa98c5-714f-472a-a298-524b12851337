﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionClearBlendTerrainLayerTiles : EditorAction
    {
        class TileData
        {
            public TileData(int index, int type, int subTypeIndex)
            {
                tileIndex = index;
                tileType = type;
                this.subTypeIndex = subTypeIndex;
            }

            public int tileIndex;
            public int tileType;
            public int subTypeIndex;
        }

        public ActionClearBlendTerrainLayerTiles(int layerID, Vector2Int[] pickedTiles, bool[] drawMasks)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as BlendTerrainLayer;
            mPickedTiles = (Vector2Int[])pickedTiles.Clone();
            mOldTileDatas = new TileData[4];
            mDrawMasks = drawMasks;
            mLayerID = layerID;
            for (int i = 0; i < pickedTiles.Length; ++i)
            {
                var tile = layer.GetTile(pickedTiles[i].x, pickedTiles[i].y);
                if (tile != null)
                {
                    mOldTileDatas[i] = new TileData(tile.index, tile.type, tile.subTypeIndex);
                }
                else
                {
                    mOldTileDatas[i] = new TileData(0, -1, 0);
                }
            }

            mDescription = string.Format("clear terrain layer {0} tiles", layer.name);
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                for (int i = 0; i < 4; ++i)
                {
                    if (mDrawMasks[i])
                    {
                        layer.PopTile(mPickedTiles[i].x, mPickedTiles[i].y, mTileIndex[i]);
                    }
                }
                return true;

            }
            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                for (int i = 0; i < 4; ++i)
                {
                    if (mOldTileDatas[i] != null)
                    {
                        if (mDrawMasks[i])
                        {
                            layer.SetTile(mPickedTiles[i].x, mPickedTiles[i].y, mOldTileDatas[i].tileIndex, mOldTileDatas[i].tileType, mOldTileDatas[i].subTypeIndex);
                        }
                    }
                }
                return true;
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };

        TileData[] mOldTileDatas;
        Vector2Int[] mPickedTiles;
        bool[] mDrawMasks;
        int mLayerID;
        string mDescription;
    }
}

#endif