﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class PolygonRiverLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new PolygonRiverLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.MapLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            float displayVertexRadius = reader.ReadSingle();
            int bakedTextureSize = reader.ReadInt32();
            bool onlyGenerateLOD0Assets = false;
            if (version.minorVersion >= 2)
            {
                onlyGenerateLOD0Assets = reader.ReadBoolean();
            }
            List<int> groundTileTypeClipExceptions = new List<int>();
            bool useUV2 = false;
            bool generateOBJ = false;
            if (version.minorVersion >= 8)
            {
                useUV2 = reader.ReadBoolean();
                generateOBJ = reader.ReadBoolean();
            }
            if (version.minorVersion >= 7)
            {
                groundTileTypeClipExceptions = Utils.ReadIntList(reader);
            }
            int expandingTileCount = 0;
            if (version.minorVersion >= 9)
            {
                expandingTileCount = reader.ReadInt32();
            }
            string riverMaskTextureSaveFolder = "";
            if (version.minorVersion >= 10)
            {
                riverMaskTextureSaveFolder = Utils.ReadString(reader);
            }

            int n = reader.ReadInt32();
            config.PolygonRiverData[] objects = new config.PolygonRiverData[n];
            int riverIDOffset = Map.currentMap.nextCustomObjectID;
            if (n > 0)
            {
                int minID = int.MaxValue;
                for (int i = 0; i < n; ++i)
                {
                    //a little tricky, custom object id are negative
                    var riverID = riverIDOffset - reader.ReadInt32();
                    if (riverID < minID)
                    {
                        minID = riverID;
                    }
                    objects[i] = LoadPolygonRiverData(reader, riverID, version);
                }
                Map.currentMap.nextCustomObjectID = minID;
            }

            var riverPrefabs = LoadRiverPrefabInfos(reader, riverIDOffset);

            var config = LoadMapLayerLODConfig(reader, version);
            var layer = new config.PolygonRiverLayerData(layerID, layerName, layerOffset, config, width, height, objects, displayVertexRadius, riverPrefabs, bakedTextureSize, onlyGenerateLOD0Assets, groundTileTypeClipExceptions, useUV2, generateOBJ, expandingTileCount, riverMaskTextureSaveFolder);
            layer.active = active;
            return layer;
        }

        static config.PolygonRiverData LoadPolygonRiverData(BinaryReader reader, int riverID, Version version)
        {
            var data = new config.PolygonRiverData();
            data.SetID(riverID);
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.outline.Add(Utils.ReadVector2(reader));
            }

            int nSections = reader.ReadInt32();
            data.sections = new config.PolygonRiverSectionData[nSections];
            for (int i = 0; i < nSections; ++i)
            {
                data.sections[i] = new config.PolygonRiverSectionData();
                var color = Utils.ReadColor32Array(reader);
                var textureData = Utils.Color32ToColorArray(color);
                data.sections[i].textureData = textureData;
                data.sections[i].outline = Utils.ReadVector3List(reader);
            }

            int nSplitters = reader.ReadInt32();
            data.splitters = new config.PolygonRiverSplitterData[nSplitters];
            for (int i = 0; i < nSplitters; ++i)
            {
                data.splitters[i] = new config.PolygonRiverSplitterData();
                data.splitters[i].startPos = Utils.ReadVector3(reader);
                data.splitters[i].endPos = Utils.ReadVector3(reader);
            }

            data.radius = reader.ReadSingle();
            data.textureSize = reader.ReadInt32();
            string guidOrPath = Utils.ReadString(reader);
            //temp fix,有些水引用了地图package之前的资源,只有临时处理一下
            //if (guidOrPath == "Assets/Framework/Script/Map/MapModuleRes/RuntimeRes/water/_default_water_mtl.mat")
            //{
            //    guidOrPath = "Assets/Res/map/water/_default_water_mtl.mat";
            //}
            //先测试guid是否是路径
            if (MapModuleResourceMgr.LoadMaterial(guidOrPath) != null)
            {
                //是path
                data.materialPath = guidOrPath;
            }
            else
            {
                //是无效的path或者guid
                var path = AssetDatabase.GUIDToAssetPath(guidOrPath);
                if (!string.IsNullOrEmpty(path))
                {
                    //是guid
                    data.materialPath = path;
                }
                else
                {
                    //是无效的path
                    var guid = AssetDatabase.AssetPathToGUID(guidOrPath);
                    data.materialPath = AssetDatabase.GUIDToAssetPath(guid);
                }
            }

            data.hideLOD = reader.ReadInt32();

            if (version.minorVersion >= 5)
            {
                data.height = reader.ReadSingle();
            }

            if (version.minorVersion >= 6)
            {
                data.generateRiverMaterial = reader.ReadBoolean();
            }

            return data;
        }

        static config.RiverPrefab[] LoadRiverPrefabInfos(BinaryReader reader, int riverIDOffset)
        {
            int n = reader.ReadInt32();
            config.RiverPrefab[] prefabs = new config.RiverPrefab[n];
            for (int i = 0; i < n; ++i)
            {
                int riverID = riverIDOffset - reader.ReadInt32();
                int sectionIndex = reader.ReadInt32();
                string prefabPath = Utils.ReadString(reader);
                prefabs[i] = new config.RiverPrefab(riverID, sectionIndex, prefabPath);
            }
            return prefabs;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                string name = "";
                if (version.minorVersion >= 3)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                if (version.minorVersion >= 4)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(zoom);
                }
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif