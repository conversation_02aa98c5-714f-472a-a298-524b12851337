﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class CameraColliderLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID());

            reader.Close();

            var layer = new CameraColliderLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.CameraColliderLayerData LoadLayerData(BinaryReader reader, int layerID)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            float displayVertexRadius = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.CameraColliderData[] objects = new config.CameraColliderData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadCameraColliderData(reader);
            }

            var layer = new config.CameraColliderLayerData(layerID, layerName, layerOffset, null, width, height, objects, displayVertexRadius);
            layer.active = active;
            return layer;
        }

        static config.CameraColliderData LoadCameraColliderData(BinaryReader reader)
        {
            var data = new config.CameraColliderData();
            data.SetID(AllocateID());
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.bottomOutline.Add(Utils.ReadVector2(reader));
            }

            n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                data.topOutline.Add(Utils.ReadVector2(reader));
            }

            data.radius = reader.ReadSingle();
            data.height = reader.ReadSingle();

            int vertexCount = reader.ReadInt32();
            data.vertices = new Vector3[vertexCount];
            for (int i = 0; i < vertexCount; ++i)
            {
                data.vertices[i] = Utils.ReadVector3(reader);
            }

            int indexCount = reader.ReadInt32();
            data.indices = new int[indexCount];
            for (int i = 0; i < indexCount; ++i)
            {
                data.indices[i] = reader.ReadInt32();
            }

            return data;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif