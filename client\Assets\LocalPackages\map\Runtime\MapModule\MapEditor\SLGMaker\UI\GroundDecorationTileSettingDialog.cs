﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class GroundDecorationTileSettingDialog : EditorWindow
    {
        public void Show(PrefabGroup.Item item)
        {
            mItem = item;
        }

        void OnGUI()
        {
            EditorGUILayout.BeginHorizontal();

            mItem.decorationPrefab.fixedIndex = EditorGUILayout.IntField("Fixed Index", mItem.decorationPrefab.fixedIndex);
            EditorGUILayout.IntField("Object Count", mItem.decorationPrefab.prefabGUIDs.Count);
            if (GUILayout.Button("Change"))
            {
                var inputDialog = EditorUtils.CreateInputDialog("Change Decoration Prefab Count");

                var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Count", "", mItem.decorationPrefab.prefabGUIDs.Count.ToString()),
                    };
                inputDialog.Show(items, OnClickChangePrefabCount);
            }

            EditorGUILayout.EndHorizontal();

            //draw prefabs
            var prefabs = mItem.decorationPrefab.prefabGUIDs;
            for (int i = 0; i < prefabs.Count;)
            {
                bool removed = DrawPrefab(i, prefabs[i]);
                if (removed)
                {
                    prefabs.RemoveAt(i);
                }
                else
                {
                    ++i;
                }
            }
        }

        bool DrawPrefab(int index, string guid)
        {
            bool removed = false;
            EditorGUILayout.BeginHorizontal();
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GUIDToAssetPath(guid));
            EditorGUIUtility.labelWidth = 20;
            int n = mItem.decorationPrefab.prefabGUIDs.Count;
            var newPrefab = EditorGUILayout.ObjectField(index.ToString(), prefab, typeof(GameObject), false, null) as GameObject;
            EditorGUIUtility.labelWidth = 0;
            if (newPrefab != null && PrefabUtility.GetPrefabAssetType(newPrefab) == PrefabAssetType.Regular)
            {
                var assetPath = AssetDatabase.GetAssetPath(newPrefab);
                mItem.decorationPrefab.prefabGUIDs[index] = AssetDatabase.AssetPathToGUID(assetPath);
            }
            if (GUILayout.Button("Remove"))
            {
                removed = true;
            }
            EditorGUILayout.EndHorizontal();
            return removed;
        }

        bool OnClickChangePrefabCount(List<InputDialog.Item> parameters)
        {
            int newCount;
            Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newCount);
            if (newCount == mItem.decorationPrefab.prefabGUIDs.Count || newCount < 0)
            {
                return false;
            }

            mItem.decorationPrefab.ChangeDecorationPrefabCount(newCount);
            return true;
        }

        PrefabGroup.Item mItem;
    }
}


#endif