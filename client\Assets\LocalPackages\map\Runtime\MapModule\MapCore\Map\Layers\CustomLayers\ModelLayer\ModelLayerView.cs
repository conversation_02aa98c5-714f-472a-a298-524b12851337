﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    //地图对象的模型层
    public class ModelLayerView : MapObjectLayerView {
        public ModelLayerView(MapLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading) {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data) {
            var view = new ModelView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public void SwitchModel(IMapObjectData data, bool toTexture)
        {
            var view = GetObjectView(data.GetEntityID());
            if (view != null)
            {
                view.Refresh(data, layerData.currentLOD);
            }
        }

        public virtual void SetLookAtPosition(Vector3 pos) { }
        public virtual void HideOverlappedObject(string objectType, Vector3 center, float radius) { }
    }
}
