using System;
using System.IO;

namespace GameServerConnection
{
    internal class MessagePacker
    {
        //使用数据信息(消息最大长度信息)
        byte[] bytes = new byte[4096];

        //占位indexId 数组
        byte[] ib = BitConverter.GetBytes(0);

        /// <summary>
        /// 数据加密
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="output"></param>
        /// <param name="offset"></param>
        /// <param name="size"></param>
        /// <param name="encryptor"></param>
        /// <param name="sidx"></param>
        /// <param name="cccflag"></param>
        internal void Encode(byte[] arr, Stream output, int offset, int size, AesEncryptor encryptor)
        {
            if (bytes.Length < size + 2)
                bytes = new byte[size + 2];

            Array.Copy(ib, 0, bytes, 0, 2);
            Array.Copy(arr, 0, bytes, 2, size);

            arr = encryptor.Encrypt(bytes, offset, size + 2);

            output.Write(arr, 0, arr.Length);
        }

        /// <summary>
        /// 数据解密
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="output"></param>
        /// <param name="offset"></param>
        /// <param name="size"></param>
        /// <param name="decryptor"></param>
        /// <param name="ridx"></param>
        internal void Decode(byte[] buffer, Stream output, int offset, int size, AesDecryptor decryptor)
        {
            var data = decryptor.Decrypt(buffer, offset, size);
            output.Write(data, 2, data.Length - 2);
        }
    }
}