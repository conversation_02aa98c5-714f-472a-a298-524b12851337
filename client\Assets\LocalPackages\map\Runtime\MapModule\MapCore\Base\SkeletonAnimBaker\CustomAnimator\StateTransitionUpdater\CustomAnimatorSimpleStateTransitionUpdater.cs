﻿using UnityEngine;

namespace TFW.Map
{
    public interface ICustomAnimatorStateTransitionUpdater
    {
        void Update(float playSpeed, AnimationParameterInfo[] parameters, CustomAnimationStateMachine statemachine);
        void SetState(CustomAnimationState state, float nextStateStartOffset, float transitionDuration, float curStateTransitionStartTime, InterruptionType interruptionType);
        CustomAnimationState currentState { get; }
        CustomAnimationState nextState { get; }
        //返回transition duration的当前归一化时间
        float normalizedDurationRatio { get; }
        //返回transition duration期间current state的帧
        float currentStateFrameInTransitionDuration { get; }
        //返回transition duration期间next state的帧
        float nextStateFrameInTransitionDuration { get; }
        //返回transition duration期间next state的当前时间
        float nextStateTimeInTransitionDuration { get; }
        bool IsInTransition { get; }
    }

    //不带动画过渡,直接硬切动画
    public class CustomAnimatorSimpleStateTransitionUpdater : ICustomAnimatorStateTransitionUpdater
    {
        public CustomAnimatorSimpleStateTransitionUpdater(CustomAnimationState initState)
        {
            mCurrentState = initState;
            mNextState = null;
        }

        public void Update(float playSpeed, AnimationParameterInfo[] parameters, CustomAnimationStateMachine statemachine)
        {
            SwitchToNextState();

            float transitionDuration;
            float transitionStartTime;
            var nextState = mCurrentState?.Update(playSpeed, parameters, false, statemachine, out transitionDuration, out transitionStartTime);
            if (nextState != null)
            {
                SetState(nextState, 0, 0, 0, InterruptionType.PlayNextAnimationImmediatly);
            }
        }

        void SwitchToNextState()
        {
            if (mNextState != null)
            {
                if (mCurrentState != null)
                {
                    mCurrentState.Reset(0);
                }
                mCurrentState = mNextState;
                mNextState.Reset(mNextStateStartOffset);
                mNextState = null;
            }
        }

        public void SetState(CustomAnimationState state, float nextStateStartOffset, float transitionDuration, float curStateTransitionStartTime, InterruptionType interruptionType)
        {
            mNextStateStartOffset = nextStateStartOffset;
            if (mNextState != null)
            {
                mNextState = state;
                SwitchToNextState();
            }
            else
            {
                if (state != mCurrentState)
                {
                    mNextState = state;
                }
            }
        }

        public CustomAnimationState currentState { get { return mCurrentState; } }
        public CustomAnimationState nextState { get { return mNextState; } }
        public float normalizedDurationRatio { get { return 0; } }
        public float currentStateFrameInTransitionDuration { get { return 0; } }
        public float nextStateFrameInTransitionDuration { get { return 0; } }
        public float nextStateTimeInTransitionDuration { get { return 0; } }
        public bool IsInTransition { get { return false; } }

        CustomAnimationState mCurrentState;
        CustomAnimationState mNextState;
        float mNextStateStartOffset = 0;
    }
}
