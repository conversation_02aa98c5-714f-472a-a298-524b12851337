﻿using System;
using UnityEngine;
using TFW;
#if UNITY_EDITOR_OSX || UNITY_IOS
using System.Runtime.InteropServices;
#endif

/// <summary>
/// 平台相关工具
/// <AUTHOR>
/// @date 2020/5/12 13:29:14
/// @ver 1.0
/// </summary>
public static class PlatformUtils
{
    /// <summary>
    /// 获取安卓系统版本
    /// </summary>
    /// <returns></returns>
    public static string GetOSVersion()
    {
        var versionName = "1.0.0";
        try
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                AndroidJavaClass javaClass = new AndroidJavaClass("com.common.android.AndroidUtils");
                versionName = javaClass.CallStatic<string>("getVersionName");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("GetOSVersion error " + ex);
        }

        //Didn't find class "android.os.Build.VERSION" on path: DexPathList
        //versionInfo = new AndroidJavaClass("android.os.Build.VERSION");
        //SystemInfo.operatingSystem
        // Prints "Windows 7 (6.1.7601) 64bit" on 64 bit Windows 7
        // Prints "Mac OS X 10.10.4" on Mac OS X Yosemite
        // Prints "iPhone OS 8.4" on iOS 8.4
        // Prints "Android OS API-22" on Android 5.1
        //Android OS 5.1.1 / API - 22(JLS36C / 500191010)
        //Android OS 6.0.1 / API-23 (V417IR/eng.root.20191206.084438)
        return versionName;
    }

    /// <summary>
    /// 重启App
    /// </summary>
    public static void RestartApp(object parameter)
    {
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;

#else
        RestartApplicationSelf();
#endif
    }


    /// <summary>
    /// 重启游戏
    /// </summary>
    /// <returns></returns>
    private static void RestartApplicationSelf()
    {
# if UNITY_ANDROID

        try
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                AndroidJavaClass UnityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                AndroidJavaObject currentActivity = UnityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

                AndroidJavaClass javaClass = new AndroidJavaClass("com.common.android.AndroidUtils");
                //直接关闭，不再重启
                javaClass.CallStatic("gameClose", currentActivity);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Restart Game Fail " + ex);
        }

#elif UNITY_IOS

        PlatformUtility.QuitGame();

#endif
    }



    /// <summary>
    /// 获取当前版本的version code
    /// </summary>
    /// <returns></returns>
    public static string GetVersionCode()
    {
        var code = "0";

        try
        {

#if UNITY_EDITOR
            
            code = UnityEditor.PlayerSettings.Android.bundleVersionCode.ToString();

#elif UNITY_ANDROID

            AndroidJavaClass UnityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = UnityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

            AndroidJavaClass javaClass = new AndroidJavaClass("com.common.android.AndroidUtils");
            code = javaClass.CallStatic<string>("getVersionCode", currentActivity);

#elif UNITY_IOS

            code = "0";
            
#endif

        }
        catch (Exception ex)
        {
            Debug.LogError("Get Version Code Error " + ex);
        }


        return code;
    }


#region 原生分享链接

#if UNITY_EDITOR_OSX || UNITY_IOS
    [DllImport ("__Internal")]
    private static extern void IOSSocialShare(string text, string url, string texData);
#endif

    /// <summary>
    /// 分享文本和链接
    /// </summary>
    /// <param name="title"></param>
    /// <param name="content"></param>
    public static void ShareTextAndLink(string title, string content)
    {
#if UNITY_ANDROID && !UNITY_EDITOR

        try
        {
            AndroidJavaClass UnityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = UnityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

            AndroidJavaClass javaClass = new AndroidJavaClass("com.common.android.AndroidUtils");
            javaClass.CallStatic("ShareText", currentActivity, title, content);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"share text {ex}");
        }


#elif UNITY_EDITOR_OSX || UNITY_IOS

        try
        {
            IOSSocialShare(title, content, null);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"share text {ex}");
        }
#endif
    }


    #endregion


    #region 模拟器检测

    /// <summary>
    /// 当前是否为模拟器
    /// </summary>
    public static bool IsSimulator { get; set; } = false;

    
    #endregion


}
