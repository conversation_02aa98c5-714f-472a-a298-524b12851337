﻿//#define USE_RGBA32_TEXTURE

//插值旋转和位移来过渡动画

#define TEXTURE_SKIN_APP_DATA \
			float4 uv3 : TEXCOORD2;	\
			float4 uv4 : TEXCOORD3;	\
			UNITY_VERTEX_INPUT_INSTANCE_ID

sampler2D_half _AnimationData;
float4 _AnimTextureSize;
float _BoneCount;

//获取某个像素的uv坐标
float4 GetUV(float pixelOffset, float2 textureSize) {
	float u = (fmod(pixelOffset, textureSize.x)) / textureSize.x;
	float v = (floor(pixelOffset / textureSize.x)) / textureSize.y;
	return float4(u, v, 0, 0);
}

#ifdef USE_RGBA32_TEXTURE
float4 DecodeMatrixColumn(float4 c0, float4 c1)
{
	float4 col;

	col.x = DecodeFloatRG(c0.xy);
	col.y = DecodeFloatRG(c0.zw);
	col.z = DecodeFloatRG(c1.xy);
	col.w = DecodeFloatRG(c1.zw);

	col.xyz = (col.xyz * 2 - 1) * pow(2, col.w * 255);
	return float4(col.xyz, 0);
}

float4 DecodeRotation(float4 c0, float4 c1)
{
	float4 col;

	col.x = DecodeFloatRG(c0.xy);
	col.y = DecodeFloatRG(c0.zw);
	col.z = DecodeFloatRG(c1.xy);
	col.w = DecodeFloatRG(c1.zw);
	col.xyzw = col.xyzw * 2 - 1;
	return col;
}

//获取某帧某个骨骼的transform
void LoadBoneTransformFull(float animStartOffset, float frame, float boneIndex, out float4 rotation, out float4 translation, out float4x4 bindPose) {
	//一根骨骼transform占12个像素
	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 12.0;
	float4 uv00 = GetUV(pixelOffset, _AnimTextureSize.xy);
	float4 uv01 = GetUV(pixelOffset + 1, _AnimTextureSize.xy);
	float4 uv10 = GetUV(pixelOffset + 2, _AnimTextureSize.xy);
	float4 uv11 = GetUV(pixelOffset + 3, _AnimTextureSize.xy);
	float4 uv20 = GetUV(pixelOffset + 4, _AnimTextureSize.xy);
	float4 uv21 = GetUV(pixelOffset + 5, _AnimTextureSize.xy);
	float4 uv30 = GetUV(pixelOffset + 6, _AnimTextureSize.xy);
	float4 uv31 = GetUV(pixelOffset + 7, _AnimTextureSize.xy);

	float4 col00 = tex2Dlod(_AnimationData, uv00);
	float4 col01 = tex2Dlod(_AnimationData, uv01);
	float4 col0 = DecodeMatrixColumn(col00, col01);

	float4 col10 = tex2Dlod(_AnimationData, uv10);
	float4 col11 = tex2Dlod(_AnimationData, uv11);
	float4 col1 = DecodeMatrixColumn(col10, col11);

	float4 col20 = tex2Dlod(_AnimationData, uv20);
	float4 col21 = tex2Dlod(_AnimationData, uv21);
	float4 col2 = DecodeMatrixColumn(col20, col21);

	float4 col30 = tex2Dlod(_AnimationData, uv30);
	float4 col31 = tex2Dlod(_AnimationData, uv31);
	float4 col3 = DecodeMatrixColumn(col30, col31);
	col3.w = 1;

	bindPose._11_21_31_41 = col0;
	bindPose._12_22_32_42 = col1;
	bindPose._13_23_33_43 = col2;
	bindPose._14_24_34_44 = col3;

	//decode rotation and translation
	float4 uv40 = GetUV(pixelOffset + 8, _AnimTextureSize.xy);
	float4 uv41 = GetUV(pixelOffset + 9, _AnimTextureSize.xy);
	float4 uv50 = GetUV(pixelOffset + 10, _AnimTextureSize.xy);
	float4 uv51 = GetUV(pixelOffset + 11, _AnimTextureSize.xy);
	float4 col40 = tex2Dlod(_AnimationData, uv40);
	float4 col41 = tex2Dlod(_AnimationData, uv41);
	float4 col50 = tex2Dlod(_AnimationData, uv50);
	float4 col51 = tex2Dlod(_AnimationData, uv51);
	rotation = DecodeRotation(col40, col41);
	translation = DecodeMatrixColumn(col50, col51);
}

void LoadBoneTransform(float animStartOffset, float frame, float boneIndex, out float4 rotation, out float4 translation) {
	//一根骨骼transform占12个像素
	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 12.0;

	//decode rotation and translation
	float4 uv40 = GetUV(pixelOffset + 8, _AnimTextureSize.xy);
	float4 uv41 = GetUV(pixelOffset + 9, _AnimTextureSize.xy);
	float4 uv50 = GetUV(pixelOffset + 10, _AnimTextureSize.xy);
	float4 uv51 = GetUV(pixelOffset + 11, _AnimTextureSize.xy);
	float4 col40 = tex2Dlod(_AnimationData, uv40);
	float4 col41 = tex2Dlod(_AnimationData, uv41);
	float4 col50 = tex2Dlod(_AnimationData, uv50);
	float4 col51 = tex2Dlod(_AnimationData, uv51);
	rotation = DecodeRotation(col40, col41);
	translation = DecodeMatrixColumn(col50, col51);
}
#else

//获取某帧某个骨骼的transform
void LoadBoneTransformFull(float animStartOffset, float frame, float boneIndex, out float4 rotation, out float4 translation, out float4x4 bindPose) {
	//一根骨骼transform占2个像素
	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 6.0;
	float4 uv0 = GetUV(pixelOffset, _AnimTextureSize.xy);
	float4 uv1 = GetUV(pixelOffset + 1, _AnimTextureSize.xy);
	float4 uv2 = GetUV(pixelOffset + 2, _AnimTextureSize.xy);
	float4 uv3 = GetUV(pixelOffset + 3, _AnimTextureSize.xy);
	float4 uv4 = GetUV(pixelOffset + 4, _AnimTextureSize.xy);
	float4 uv5 = GetUV(pixelOffset + 5, _AnimTextureSize.xy);
	float4 row0 = tex2Dlod(_AnimationData, uv0);
	float4 row1 = tex2Dlod(_AnimationData, uv1);
	float4 row2 = tex2Dlod(_AnimationData, uv2);
	float4 row3 = tex2Dlod(_AnimationData, uv3);
	bindPose = float4x4(row0, row1, row2, row3);
	rotation = tex2Dlod(_AnimationData, uv4);
	translation = tex2Dlod(_AnimationData, uv5);
}

void LoadBoneTransform(float animStartOffset, float frame, float boneIndex, out float4 rotation, out float4 translation) {
	//一根骨骼transform占2个像素
	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 6.0;
	float4 uv4 = GetUV(pixelOffset + 4, _AnimTextureSize.xy);
	float4 uv5 = GetUV(pixelOffset + 5, _AnimTextureSize.xy);
	rotation = tex2Dlod(_AnimationData, uv4);
	translation = tex2Dlod(_AnimationData, uv5);
}

#endif

float4x4 QuaternionToMatrix(float4 q, float4 t) {
	float4x4 m;

	float wx, wy, wz, xx, yy, yz, xy, xz, zz, x2, y2, z2;
	x2 = q.x + q.x;
	y2 = q.y + q.y;
	z2 = q.z + q.z;

	xx = q.x * x2;
	xy = q.x * y2;
	xz = q.x * z2;

	yy = q.y * y2;
	yz = q.y * z2;

	zz = q.z * z2;

	wx = q.w * x2;
	wy = q.w * y2;
	wz = q.w * z2;

	m[0][0] = 1.0 - (yy + zz);
	m[0][1] = xy - wz;
	m[0][2] = xz + wy;
	m[0][3] = t.x;

	m[1][0] = xy + wz;
	m[1][1] = 1.0 - (xx + zz);
	m[1][2] = yz - wx;
	m[1][3] = t.y;

	m[2][0] = xz - wy;
	m[2][1] = yz + wx;
	m[2][2] = 1.0 - (xx + yy);
	m[2][3] = t.z;

	m[3][0] = 0;
	m[3][1] = 0;
	m[3][2] = 0;
	m[3][3] = 1.0;

	return m;
}

float4 NormalizeQuaternion(float4 q) {
	float sqrNorm = q.x * q.x + q.y * q.y + q.z * q.z + q.w * q.w;
	if (abs(sqrNorm) > 0.0001)
	{
		float4 normInverse = 1.0 / sqrt(sqrNorm);
		q.x = q.x * normInverse;
		q.y = q.y * normInverse;
		q.z = q.z * normInverse;
		q.w = q.w * normInverse;
	}
	return q;
}

float4 LerpQuat(float4 q1, float4 q2, float t)
{
	float4 tmpQuat;
	// if (dot < 0), q1 and q2 are more than 360 deg apart.
	// The problem is that quaternions are 720deg of freedom.
	// so we - all components when lerping
	if (dot(q1, q2) < 0.0)
	{
		tmpQuat = float4(q1.x + t * (-q2.x - q1.x),
			q1.y + t * (-q2.y - q1.y),
			q1.z + t * (-q2.z - q1.z),
			q1.w + t * (-q2.w - q1.w));
	}
	else
	{
		tmpQuat = float4(q1.x + t * (q2.x - q1.x),
			q1.y + t * (q2.y - q1.y),
			q1.z + t * (q2.z - q1.z),
			q1.w + t * (q2.w - q1.w));
	}
	return NormalizeQuaternion(tmpQuat);
}

float4 Slerp(float4 q1, float4 q2, float t)
{
	float d = dot(q1, q2);
	// dot = cos(theta)
	// if (dot < 0), q1 and q2 are more than 90 degrees apart,
	// so we can invert one to reduce spinning
	float4 tmpQuat;
	if (d < 0.0)
	{
		d = -d;
		tmpQuat = float4(-q2.x,
			-q2.y,
			-q2.z,
			-q2.w);
	}
	else
		tmpQuat = q2;

	if (d < 0.95)
	{
		float angle = acos(d);
		float sinadiv, sinat, sinaomt;
		sinadiv = 1.0 / sin(angle);
		sinat = sin(angle*t);
		sinaomt = sin(angle*(1.0 - t));
		tmpQuat = float4((q1.x*sinaomt + tmpQuat.x*sinat)*sinadiv,
			(q1.y*sinaomt + tmpQuat.y*sinat)*sinadiv,
			(q1.z*sinaomt + tmpQuat.z*sinat)*sinadiv,
			(q1.w*sinaomt + tmpQuat.w*sinat)*sinadiv);
		return tmpQuat;

	}
	// if the angle is small, use linear interpolation
	else
	{
		return LerpQuat(q1, tmpQuat, t);
	}

}

float4 Lerp(float4 a, float4 b, float t) {
	return a * (1 - t) + b * t;
}

half3 CustomSkinWithBlendingImpl(float3 localPos, float4 indices, float4 weights) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);

	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	float4x4 bindPose0, bindPose1, bindPose2, bindPose3;
	float4 rotation0, rotation1, rotation2, rotation3;
	float4 translation0, translation1, translation2, translation3;
	LoadBoneTransformFull(startOffset, frame, indices.x, rotation0, translation0, bindPose0);
	LoadBoneTransformFull(startOffset, frame, indices.y, rotation1, translation1, bindPose1);
	LoadBoneTransformFull(startOffset, frame, indices.z, rotation2, translation2, bindPose2);
	LoadBoneTransformFull(startOffset, frame, indices.w, rotation3, translation3, bindPose3);

	//获取transform
	float frame1 = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float startOffset1 = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);

	float4 rotation4, rotation5, rotation6, rotation7;
	float4 translation4, translation5, translation6, translation7;
	LoadBoneTransform(startOffset1, frame1, indices.x, rotation4, translation4);
	LoadBoneTransform(startOffset1, frame1, indices.y, rotation5, translation5);
	LoadBoneTransform(startOffset1, frame1, indices.z, rotation6, translation6);
	LoadBoneTransform(startOffset1, frame1, indices.w, rotation7, translation7);

	float4 localPos4 = float4(localPos, 1.0);

	float4 rotationA = Slerp(rotation0, rotation4, blendFactor);
	float4 rotationB = Slerp(rotation1, rotation5, blendFactor);
	float4 rotationC = Slerp(rotation2, rotation6, blendFactor);
	float4 rotationD = Slerp(rotation3, rotation7, blendFactor);
	float4 translationA = Lerp(translation0, translation4, blendFactor);
	float4 translationB = Lerp(translation1, translation5, blendFactor);
	float4 translationC = Lerp(translation2, translation6, blendFactor);
	float4 translationD = Lerp(translation3, translation7, blendFactor);

	float4x4 m0 = mul(QuaternionToMatrix(rotationA, translationA), bindPose0);
	float4x4 m1 = mul(QuaternionToMatrix(rotationB, translationB), bindPose1);
	float4x4 m2 = mul(QuaternionToMatrix(rotationC, translationC), bindPose2);
	float4x4 m3 = mul(QuaternionToMatrix(rotationD, translationD), bindPose3);
	return
		mul(m0, localPos4).xyz * weights.x +
		mul(m1, localPos4).xyz * weights.y +
		mul(m2, localPos4).xyz * weights.z +
		mul(m3, localPos4).xyz * weights.w;
}


half3 CustomSkinImpl(float3 localPos, float4 indices, float4 weights) {
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	float4x4 bindPose0, bindPose1, bindPose2, bindPose3;
	float4 rotation0, rotation1, rotation2, rotation3;
	float4 translation0, translation1, translation2, translation3;
	LoadBoneTransformFull(startOffset, frame, indices.x, rotation0, translation0, bindPose0);
	LoadBoneTransformFull(startOffset, frame, indices.y, rotation1, translation1, bindPose1);
	LoadBoneTransformFull(startOffset, frame, indices.z, rotation2, translation2, bindPose2);
	LoadBoneTransformFull(startOffset, frame, indices.w, rotation3, translation3, bindPose3);

	float4 localPos4 = float4(localPos, 1.0);

	float4x4 m0 = mul(QuaternionToMatrix(rotation0, translation0), bindPose0);
	float4x4 m1 = mul(QuaternionToMatrix(rotation1, translation1), bindPose1);
	float4x4 m2 = mul(QuaternionToMatrix(rotation2, translation2), bindPose2);
	float4x4 m3 = mul(QuaternionToMatrix(rotation3, translation3), bindPose3);
	return
		mul(m0, localPos4).xyz * weights.x +
		mul(m1, localPos4).xyz * weights.y +
		mul(m2, localPos4).xyz * weights.z +
		mul(m3, localPos4).xyz * weights.w;
}

half3 CustomSkin2BoneWithBlendingImpl(float3 localPos, float4 indices, float4 weights) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);

	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	float4x4 bindPose0, bindPose1;
	float4 rotation0, rotation1;
	float4 translation0, translation1;
	LoadBoneTransformFull(startOffset, frame, indices.x, rotation0, translation0, bindPose0);
	LoadBoneTransformFull(startOffset, frame, indices.y, rotation1, translation1, bindPose1);

	//获取transform
	float frame1 = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float startOffset1 = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);

	float4 rotation4, rotation5;
	float4 translation4, translation5;
	LoadBoneTransform(startOffset1, frame1, indices.x, rotation4, translation4);
	LoadBoneTransform(startOffset1, frame1, indices.y, rotation5, translation5);

	float4 localPos4 = float4(localPos, 1.0);

	float4 rotationA = Slerp(rotation0, rotation4, blendFactor);
	float4 rotationB = Slerp(rotation1, rotation5, blendFactor);
	float4 translationA = Lerp(translation0, translation4, blendFactor);
	float4 translationB = Lerp(translation1, translation5, blendFactor);

	float4x4 m0 = mul(QuaternionToMatrix(rotationA, translationA), bindPose0);
	float4x4 m1 = mul(QuaternionToMatrix(rotationB, translationB), bindPose1);
	return
		mul(m0, localPos4).xyz * weights.x +
		mul(m1, localPos4).xyz * weights.y;
}

half3 CustomSkin2BoneImpl(float3 localPos, float4 indices, float4 weights) {
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	float4x4 bindPose0, bindPose1;
	float4 rotation0, rotation1;
	float4 translation0, translation1;
	LoadBoneTransformFull(startOffset, frame, indices.x, rotation0, translation0, bindPose0);
	LoadBoneTransformFull(startOffset, frame, indices.y, rotation1, translation1, bindPose1);

	float4 localPos4 = float4(localPos, 1.0);

	float4x4 m0 = mul(QuaternionToMatrix(rotation0, translation0), bindPose0);
	float4x4 m1 = mul(QuaternionToMatrix(rotation1, translation1), bindPose1);
	return
		mul(m0, localPos4).xyz * weights.x +
		mul(m1, localPos4).xyz * weights.y;
}

//刚体动画
half3 RigidTransformWithBlendingImpl(half3 localPos, float boneIndex) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);

	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	float4 rotation0, translation0;
	LoadBoneTransform(startOffset + _AnimTextureSize.z, frame, boneIndex, rotation0, translation0);

	float nextStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float nextStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);
	float4 rotation1, translation1;
	LoadBoneTransform(nextStateStartOffset + _AnimTextureSize.z, nextStateFrame, boneIndex, rotation1, translation1);

	float4 rotationA = Slerp(rotation0, rotation1, blendFactor);
	float4 translationA = Lerp(translation0, translation1, blendFactor);

	float4x4 m = QuaternionToMatrix(rotationA, translationA);
	return mul(m, float4(localPos, 1.0)).xyz;
}

half3 RigidTransformImpl(half3 localPos, float boneIndex) {
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	float4 rotation0, translation0;
	LoadBoneTransform(startOffset + _AnimTextureSize.z, frame, boneIndex, rotation0, translation0);

	float4x4 m = QuaternionToMatrix(rotation0, translation0);
	return mul(m, float4(localPos, 1.0)).xyz;
}