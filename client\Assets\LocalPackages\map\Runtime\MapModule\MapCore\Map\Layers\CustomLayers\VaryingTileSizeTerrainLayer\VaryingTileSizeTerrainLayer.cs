﻿ 



 
 


using UnityEngine;
using System.IO;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public partial class VaryingTileSizeTerrainLayer : MapLayerBase, IBlendTerrainLayer
    {
        public VaryingTileSizeTerrainLayer(Map map) : base(map)
        {
#if UNITY_EDITOR
            if (map != null && map.isEditorMode)
            {
                (map as EditorMap).exportMapEvent -= ProcessBeforeExport;
                (map as EditorMap).exportMapEvent += ProcessBeforeExport;
            }
#endif
        }

        //加载地图层
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.VaryingTileSizeTerrainLayerData;
            if (sourceLayer == null)
            {
                return;
            }
            int rows = sourceLayer.rows;
            int cols = sourceLayer.cols;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, rows, cols, sourceLayer.tileWidth, sourceLayer.tileHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            int lodCount = 1;
            if (layerData.config != null)
            {
                lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            List<ModelData[,]> lodTiles = null;
            VaryingTileSizeTerrainRenderTextureLODSetting[] lodSetting = null;

            if (map.isEditorMode == false && sourceLayer.useGeneratedLOD && lodCount > 1)
            {
                var assetPath = map.dataFolder + "/Res/terrain_lod_data_ex.bytes";
                if (MapModuleResourceMgr.Exists(assetPath))
                {
                    LoadLODDataNew(assetPath, out lodTiles, out lodSetting);
                }
            }

            mLayerData = new RenderTextureVaryingTileSizeTerrainLayerData(header, config, map, null, lodTiles, lodSetting, sourceLayer.useGeneratedLOD);

            mLayerData.isLoading = true;

            List<VaryingTileSizeTerrainLayerData.BigTileData> bigTiles = new List<VaryingTileSizeTerrainLayerData.BigTileData>();
            if (sourceLayer.bigTiles != null)
            {
                for (int i = 0; i < sourceLayer.bigTiles.Length; ++i)
                {
                    var sourceTile = sourceLayer.bigTiles[i];
                    var bigTileData = new VaryingTileSizeTerrainLayerData.BigTileData(i, sourceTile.width, sourceTile.height, sourceTile.x, sourceTile.y);
                    bigTiles.Add(bigTileData);
                }
            }

            if (sourceLayer.tiles != null)
            {
                for (int y = 0; y < rows; ++y)
                {
                    for (int x = 0; x < cols; ++x)
                    {
                        int idx = y * cols + x;
                        var tile = sourceLayer.tiles[idx];
                        if (tile != null)
                        {
                            VaryingTileSizeTerrainLayerData.BigTileData bigTile = null;
                            if (tile.bigTileIndex >= 0)
                            {
                                bigTile = bigTiles[tile.bigTileIndex];
                            }
                            InitTile(x, y, tile.index, tile.type, tile.tileID, bigTile);
                        }
                    }
                }
            }

            mLayerData.ResetIsChangedFlags();

            var layerView = new VaryingTileSizeTerrainLayerView(mLayerData, false);
            mLayerView = layerView;
            mLayerView.active = layerData.active;
            mLayerData.SetTerrainObjectActiveCallback(mLayerView.OnObjectActiveStateChange);

            var pos = mLayerView.root.transform.position;
            pos.y = sourceLayer.layerPosition.y;
            mLayerView.root.transform.position = pos;

            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        //卸载地图数据
        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            if (mTransition != null)
            {
                mTransition.OnDestroy();
            }

#if UNITY_EDITOR
            if (map.isEditorMode)
            {
                (map as EditorMap).exportMapEvent -= ProcessBeforeExport;
            }
#endif
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        void LoadLODDataNew(string path, out List<ModelData[,]> lodTiles, out VaryingTileSizeTerrainRenderTextureLODSetting[] lodSettings)
        {
            lodSettings = null;
            lodTiles = null;

            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream == null)
            {
                return;
            }

            using BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();
            bool wrongLODOrientation = reader.ReadBoolean();

            //load prefabs
            List<string> prefabPaths = new List<string>();
            int prefabCount = reader.ReadInt32();
            for (int i = 0; i < prefabCount; ++i)
            {
                prefabPaths.Add(Utils.ReadString(reader));
            }

            //load lod tile data
            lodTiles = new List<ModelData[,]>();
            int lodCount = reader.ReadInt32();
            for (int i = 0; i < lodCount; ++i)
            {
                int rows = reader.ReadInt32();
                int cols = reader.ReadInt32();
                ModelData[,] tiles = new ModelData[rows, cols];
                lodTiles.Add(tiles);

                for (int r = 0; r < rows; ++r)
                {
                    for (int c = 0; c < cols; ++c)
                    {
                        var position = Utils.ReadVector3(reader);
                        var prefabIndex = reader.ReadInt32();
                        if (prefabIndex >= 0)
                        {
                            int objectDataID = map.nextCustomObjectID;
                            var modelTemplate = map.GetOrCreateModelTemplate(objectDataID, prefabPaths[prefabIndex], false, true);
                            var tile = new ModelData(objectDataID, map, 0, position, wrongLODOrientation ? Quaternion.Euler(0, 180, 0) : Quaternion.identity, Vector3.one, modelTemplate, false);
                            tiles[r, c] = tile;
                        }
                    }
                }
            }

            //load lod settings
            int lodSettingCount = reader.ReadInt32();
            lodSettings = new VaryingTileSizeTerrainRenderTextureLODSetting[lodSettingCount];
            for (int i = 0; i < lodSettingCount; ++i)
            {
                float cameraHeight = reader.ReadSingle();
                int blockSize = reader.ReadInt32();
                int realBlockSize = reader.ReadInt32();
                var setting = new VaryingTileSizeTerrainRenderTextureLODSetting(cameraHeight, blockSize, null);
                setting.realBlockSize = realBlockSize;
                lodSettings[i] = setting;
            }

            reader.Close();

            //preload prefabs
            for (int i = 0; i < prefabPaths.Count; ++i)
            {
                var prefab = MapModuleResourceMgr.LoadPrefab(prefabPaths[i]);
                Debug.Assert(prefab != null);
            }
        }

        public void SetTile(int x, int y, int tileIndex, int tileType, int tileID)
        {
            Vector2Int size = GetTileSize(tileIndex, tileType);
            for (int i = 0; i < size.y; ++i)
            {
                for (int j = 0; j < size.x; ++j)
                {
                    ClearTile(x + j, y + i);
                }
            }

            if (tileIndex > 0)
            {
                int maxX = x;
                int maxY = y;
                VaryingTileSizeTerrainLayerData.BigTileData bigTileData = null;
                if (size != Vector2Int.one)
                {
                    //create a big tile
                    bigTileData = new VaryingTileSizeTerrainLayerData.BigTileData(Map.currentMap.nextCustomObjectID, size.x, size.y, x, y);
                    maxX = x + size.x - 1;
                    maxY = y + size.y - 1;
                }

                for (int i = y; i <= maxY; ++i)
                {
                    for (int j = x; j <= maxX; ++j)
                    {
                        var tile = mLayerData.SetTile(j, i, tileIndex, tileType, tileID, bigTileData);
                        if (tile != null && tile.IsObjActive())
                        {
                            mLayerView.ShowObject(tile, j, i, 0);
                        }
                    }
                }
            }
        }

        public void InitTile(int x, int y, int tileIndex, int tileType, int tileID, VaryingTileSizeTerrainLayerData.BigTileData bigTile)
        {
            mLayerData.SetTile(x, y, tileIndex, tileType, tileID, bigTile);   
        }

        void ClearTile(int x, int y)
        {
            var tile = mLayerData.GetTile(x, y);
            int minX = x;
            int minY = y;
            int maxX = x;
            int maxY = y;
            if (tile != null && tile.bigTile != null)
            {
                minX = tile.bigTile.x;
                minY = tile.bigTile.y;
                maxX = minX + tile.bigTile.width - 1;
                maxY = minY + tile.bigTile.height - 1;
            }

            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    tile = mLayerData.GetTile(j, i);
                    if (tile != null && tile.IsObjActive())
                    {
                        mLayerView.HideObject(tile, j, i, 0);
                    }
                    mLayerData.ClearTile(j, i);
                }
            }
        }

        //获取tile数据
        public VaryingTileSizeTerrainTileData GetTile(int x, int y)
        {
            return mLayerData.GetTile(x, y);
        }

        //返回地图的根game object
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override int id { get { return mLayerData.id; } }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateLOD(newCameraZoom);

            if (!lodChanged)
            {
                mLayerData.UpdateViewport(newViewport);
            }
            else
            {
                mLayerData.OnLODChanged(mLayerData.lastLOD, mLayerData.currentLOD, newViewport);
                OnLODChange(mLayerData.lastLOD, mLayerData.currentLOD, map.viewCenter);
            }

            return lodChanged;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            //use MoveAndResize
#if UNITY_EDITOR
            return MoveAndResize(newWidth, newHeight, ResizeAlignment.StayAndOffsetLayer);
#else
            return false;
#endif
        }

        //加载完地图时刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void ClearAllTiles()
        {
            mLayerData.ClearAllTiles();
            mLayerView.Clear();
#if UNITY_EDITOR
            ActionManager.instance.Clear();
#endif
        }

        public int GetLODFromCameraHeight(float cameraHeight)
        {
            return mLayerData.GetLODFromCameraHeight(cameraHeight);
        }

        public Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return mLayerData.FromCoordinateToWorldPosition(x, y);
        }

        public string GetTilePrefabPath(int x, int y, int lod)
        {
            var tile = mLayerData.GetTile(x, y);
            if (tile != null)
            {
                return tile.GetAssetPath(lod);
            }
            return null;
        }

        public bool HasTile(int x, int y)
        {
            return mLayerData.GetTile(x, y) != null;
        }

        //获取使用了的prefab path
        public List<string> GetUsedTilePrefabs()
        {
            List<string> prefabPaths = new List<string>();
            int v = verticalTileCount;
            int h = horizontalTileCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    var tile = GetTile(j, i);
                    if (tile != null)
                    {
                        string path = tile.GetAssetPath(0);
                        if (!prefabPaths.Contains(path))
                        {
                            prefabPaths.Add(path);
                        }
                    }
                }
            }
            return prefabPaths;
        }

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }

        public void UpdateTerrainGeneratedLODHeight(int lod, float height)
        {
        }

        public void GetTerrainTileMeshAndGameObject(int x, int y, out Mesh mesh, out GameObject gameObject)
        {
            mLayerView.GetTerrainTileMeshAndGameObject(x, y, out mesh, out gameObject);
        }

        Vector2Int GetTileSize(int tileIndex, int tileType)
        {
            if (tileIndex == 0)
            {
                return Vector2Int.one;
            }

            var prefabManager = map.data.varyingTileSizeTerrainPrefabManager;
            var size = prefabManager.GetPrefabSizeByID(tileType, tileIndex);
            return size;
        }

        public int GetValidTileCount()
        {
            return mLayerData.GetValidTileCount();
        }

        //地图层的名称
        public override string name
        {
            get
            {
                return mLayerData?.name;
            }
            set
            {
                mLayerData.name = value;
            }
        }
        //地图层的格子类型
        public override GridType gridType { get { return GridType.Rectangle; } }
        //x方向上格子的数量
        public override int horizontalTileCount { get { return mLayerData.horizontalTileCount; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { return mLayerData.verticalTileCount; } }
        //格子的宽
        public override float tileWidth { get { return mLayerData.tileWidth; } }
        //格子的高
        public override float tileHeight { get { return mLayerData.tileHeight; } }
        //地图层的偏移值
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public bool asyncLoading { get { return mLayerView.asyncLoading; } set { mLayerView.asyncLoading = value; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int lodCount => mLayerData.lodCount;
        public MapLayerLODConfig lodConfig { get { return mLayerData.lodConfig; } }
        public bool supportGeneratingLOD { get { return true; } }

        public MapLayerData layerData { get { return mLayerData; } }
        public MapLayerView layerView { get { return mLayerView; } }

        //拼接地表层的tile数据
        RenderTextureVaryingTileSizeTerrainLayerData mLayerData;
        //地表层的模型管理
        VaryingTileSizeTerrainLayerView mLayerView;
    }
}
