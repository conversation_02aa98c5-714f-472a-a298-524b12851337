﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class EditorMap : Map
    {
        public EditorMap(string mapName, GameMapType mapType, MapEventListener listener) : base(mapName, null, true, listener)
        {
            currentMap = this;
            mMapType = mapType;
            mGenerateNPCSpawnPointsInBorderLine = false;
            mNavMeshRegionVisible = false;
        }

        protected override void OnUnload()
        {
            mGrid.OnDestroy();
            mGrid = null;
        }

        public EditorGridModelLayer CreateEditorGridModelLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, GridType gridType, int layerID = 0, bool useOffset = true)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = useOffset ? (mapWidth - layerWidth) * 0.5f : 0;
            var oz = useOffset ? (mapHeight - layerHeight) * 0.5f : 0;
            var layerData = new config.GridModelLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, gridType, null);
            var layer = new EditorGridModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public BuildingGridLayer CreateBuildingGridLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = 0;
            var oz = 0;

            config.TextureGridLayer[] grids = new config.TextureGridLayer[1]
            {
                new config.TextureGridLayer("City Grid", cols, rows, tileWidth, tileHeight, null, null)
            };

            var layerData = new config.BuildingGridLayerData(layerID, name, new Vector3(ox, 0, oz), grids, Vector3.zero);
            var layer = new BuildingGridLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.gameObject;

            return layer;
        }

        public EditorTerritoryLayer CreateEditorTerritoryLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = 0;
            var oz = 0;
            var subLayers = new config.EditorTerritorySubLayerData[1];
            subLayers[0] = new config.EditorTerritorySubLayerData("layer 0", true, tileWidth, tileHeight, cols, rows, 10, null, null, new config.TerritoryMeshGenerationParam[1] { new config.TerritoryMeshGenerationParam() }, "ia_point_city_zone", null, new config.TerritorySharedEdgeInfo[0], null, 0, 0, null);
            var layerData = new config.EditorTerritoryLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), subLayers) ;
            var layer = new EditorTerritoryLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.gameObject;

            return layer;
        }

        public RegionColorLayer CreateRegionColorLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = 0;
            var oz = 0;

            config.TextureGridLayer[] grids = new config.TextureGridLayer[1]
            {
                new config.TextureGridLayer("Default", cols, rows, tileWidth, tileHeight, null, null)
            };

            var layerData = new config.RegionColorLayerData(layerID, name, new Vector3(ox, 0, oz), grids, true);
            var layer = new RegionColorLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.gameObject;

            return layer;
        }

        public NavMeshLayer CreateNavMeshLayer(string name, float layerWidth, float layerHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.NavMeshLayerData(layerID, name, offset, null, null, true, layerWidth, layerHeight, null, true);
            var layer = new NavMeshLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        //创建一个使用格子管理对象的地图层
        public NPCRegionLayer CreateNPCRegionLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var layerConfig = new config.NPCRegionLayerData.Layer();
            layerConfig.tiles = new int[rows * cols];
            layerConfig.name = "DefaultLayer";
            var layerData = new config.NPCRegionLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, new List<config.NPCRegionLayerData.Layer>() { layerConfig });
            var layer = new NPCRegionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            layer.InitTiles();
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        //创建NPC移动的模板
        List<Vector2> CreateMoveInfo(Vector2 npcMoveRange, int waypointCount)
        {
            float nearest = Mathf.Min(npcMoveRange.x, npcMoveRange.y);
            float max = Mathf.Max(npcMoveRange.x, npcMoveRange.y);
            if (nearest < 0)
            {
                nearest = 0;
            }
            if (max < 0)
            {
                max = 0;
            }
            List<Vector2> waypoints = new List<Vector2>();
            for (int i = 0; i < waypointCount; ++i)
            {
                float randomDistance = Random.Range(nearest, max);
                var point = Random.insideUnitCircle;
                point.Normalize();
                point *= randomDistance;
                waypoints.Add(point);
            }
            return waypoints;
        }

        public EntitySpawnLayer CreateEntitySpawnLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, Vector2 npcMoveRange, int npcWaypointCount, ISpawnPointGenerationStrategy strategy, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            int horizontalRegionCount = Mathf.CeilToInt(layerWidth / tileWidth);
            int verticalRegionCount = Mathf.CeilToInt(layerHeight / tileHeight);

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);

            var points = CreateMoveInfo(npcMoveRange, npcWaypointCount);

            var layerData = new config.EntitySpawnLayerData(layerID, "Entity Spawn Layer", offset, horizontalRegionCount, verticalRegionCount, new Vector2(tileWidth, tileHeight), npcMoveRange, null, points, strategy, null, null, "");
            var layer = new EntitySpawnLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public LODLayer CreateLODLayer(string name, float layerWidth, float layerHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.LODLayerData(layerID, name, offset, new config.MapLayerLODConfig(), layerWidth, layerHeight);
            var layer = new LODLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public MapCollisionLayer CreateCollisionLayer(string name, float layerWidth, float layerHeight, int layerID = 0, PrefabOutlineType displayType = PrefabOutlineType.NavMeshObstacle, float displayVertexRadius = 5.0f)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.MapCollisionLayerData(layerID, name, offset, new config.MapLayerLODConfig(), layerWidth, layerHeight, null, displayType, displayVertexRadius, new config.Detector[0], 10);
            var layer = new MapCollisionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public RegionLayer CreateRegionLayer(string name, float layerWidth, float layerHeight, int layerID = 0, float displayVertexRadius = 5.0f)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.RegionLayerData(layerID, name, offset, layerWidth, layerHeight, null, displayVertexRadius, "", 0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight, new Vector3[0], new int[0], "", false, "", 1, true, true);
            var layer = new RegionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public PolygonRiverLayer CreatePolygonRiverLayer(string name, float layerWidth, float layerHeight, int layerID = 0, float displayVertexRadius = 50.0f)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.PolygonRiverLayerData(layerID, name, offset, new config.MapLayerLODConfig(), layerWidth, layerHeight, null, displayVertexRadius, null, 256, false, new List<int>(), false, false, 0, "");
            var layer = new PolygonRiverLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public EditorComplexGridModelLayer CreateEditorComplexGridModelLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, GridType gridType, DecorationObjectPlacementSetting objectPlacementSetting, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var layerData = new config.ComplexGridModelLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, gridType, null, true, new Vector3(0, 0, 0), new Vector2(0, 0), new ObjectTagSetting[0], objectPlacementSetting, new Rect(ox, oz, layerWidth, layerHeight), true, false);
            var layer = new EditorComplexGridModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            return layer;
        }

        public DecorationBorderLayer CreateDecorationBorderLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, GridType gridType, DecorationObjectPlacementSetting objectPlacementSetting, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var layerData = new config.ComplexGridModelLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, gridType, null, true, new Vector3(0, 0, 0), new Vector2(0, 0), new ObjectTagSetting[0], objectPlacementSetting, new Rect(ox, oz, layerWidth, layerHeight), false, false);
            var layer = new DecorationBorderLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            return layer;
        }

        public CameraColliderLayer CreateCameraColliderLayer(string name, float layerWidth, float layerHeight, int layerID = 0, float displayVertexRadius = 30.0f)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.CameraColliderLayerData(layerID, name, offset, new config.MapLayerLODConfig(), layerWidth, layerHeight, null, displayVertexRadius);
            var layer = new CameraColliderLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        public RuinLayer CreateRuinLayer(string name, float layerWidth, float layerHeight, bool shareProperty, float cellWidth = 120, int hordeCount = 5, float displayRadius = 50, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }

            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var ruinSettings = new List<config.RuinSetting>() { new config.RuinSetting("Ruin", Color.black, new PropertyDatas(null), 5.0f, null, new config.RuinSpecialRegionSetting()) };
            var layerData = new config.RuinLayerData(layerID, name, offset, null, layerWidth, layerHeight, null, cellWidth, hordeCount, displayRadius, true, ruinSettings, shareProperty, false);
            var layer = new RuinLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            Selection.activeObject = layer.layerView.root;

            return layer;
        }

        GridRegionEditor CreateGridRegionEditor(config.GridRegionEditorSetting setting)
        {
            List<GridTemplate> templates = new List<GridTemplate>();
            for (int i = 0; i < setting.gridTemplates.Length; ++i)
            {
                var t = new GridTemplate(setting.gridTemplates[i].type, setting.gridTemplates[i].name, setting.gridTemplates[i].color);
                templates.Add(t);
            }

            GridRegionEditor editor = new GridRegionEditor(setting.horizontalGridCount, setting.verticalGridCount, setting.gridWidth, setting.gridHeight, templates, setting.grids, setting.showGrid);
            return editor;
        }

        LoadRangeData CreateLoadRangeData(config.LoadRangeData loadRangeData)
        {
            LoadRangeData data = new LoadRangeData(loadRangeData.cameraFov, loadRangeData.resolution, loadRangeData.cameraRotationX, loadRangeData.cameraHeight, loadRangeData.loadRangeScale);
            return data;
        }

        public override void LoadMapData(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter, GameObject root)
        {
            this.dataFolder = editorData.setting.exportFolder;

            var source = editorData.map;

            var editorMapData = editorData as config.EditorMapData;

            mMapType = (GameMapType)editorMapData.mapType;
            mNavMeshRegionVisible = editorMapData.map.navMeshRegionVisible;
            this.riverMaterialsFolder = editorMapData.setting.riverMaterialFolder;
            this.saveRiverMaterials = editorMapData.setting.saveRiverMaterials;
            mNavMeshMode = editorMapData.navMeshMode;
            mGlobalObstacleMode = editorMapData.globalObstacleMode;

            var lodManager = CreateLODManager(editorData);
            var obstacleManager = CreateObstacleManager(editorData);
            var globalObstacleManager = CreateGlobalObstacleManager(editorData);
            var detailSprites = CreateDetailSprites(editorData.detailSpritesSetting, editorData.map.width, editorData.map.height);
            var regionEditor = CreateGridRegionEditor(new config.GridRegionEditorSetting());
            var loadRangeData = CreateLoadRangeData(editorMapData.loadRangeData);

            mData = new EditorMapData(this, viewCenter, editorMapData.viewportSize.x, editorMapData.viewportSize.y, source.width, source.height, source.borderHeight, new List<ModelTemplate>(), editorData.setting.cameraMoveRange, lodManager, obstacleManager, globalObstacleManager, editorData.map.isCircle, source.groundTileSize, source.frontTileSize, editorData.version, editorData.map.backExtendedSize, editorData.map.farClipOffset, new List<SpriteTemplate>(), detailSprites, regionEditor, loadRangeData, editorMapData.backgroundSetting, source.mapDataGenerationRange, source.maxCameraColliderHeight);

            mGrid = new GlobalGrid();
            float totalWidth = editorMapData.grid.totalWidth;
            float totalHeight = editorMapData.grid.totalHeight;
            float gridWidth = editorMapData.grid.gridWidth;
            float gridHeight = editorMapData.grid.gridHeight;
            bool visible = editorMapData.grid.visible;
            var color = editorMapData.grid.color;
            if (totalWidth == 0)
            {
                visible = false;
                totalWidth = mData.mapWidth;
                totalHeight = mData.mapHeight;
                gridWidth = 180;
                gridHeight = 180;
                color = Color.blue;
            }
            mGrid.Create(visible, totalWidth, totalHeight, gridWidth, gridHeight, color);
            LoadCamera(editorData, camera);
            //LoadPropertySets(editorMapData);
            //LoadSpriteTemplates(editorMapData);
            LoadModelTemplates(editorMapData);
            //LoadModelProperties(editorMapData);
            //only used in major version 1
            LoadPrefabManagers(editorMapData);
            LoadMapLayers(editorMapData);

            this.generateNPCSpawnPointsInBorderLine = source.generateNPCSpawnPointsInBorderLine;
            this.removeSameHoles = source.removeSameHoles;
        }

        MapLODManager CreateLODManager(config.SLGMakerData editorData)
        {
            int n = editorData.map.lodConfig.lods.Length;
            var lods = new MapLODManager.LOD[n];
            for (int i = 0; i < n; ++i)
            {
                var srcLOD = editorData.map.lodConfig.lods[i];
                lods[i] = new MapLODManager.LOD(srcLOD.name, srcLOD.cameraHeight, srcLOD.showTerritory);
                lods[i].viewWidth = srcLOD.viewWidth;
                lods[i].viewHeight = srcLOD.viewHeight;
                int unitCount = srcLOD.displayingUnits.Count;
                lods[i].displayingUnits = new List<MapLODManager.MapLODUnit>(unitCount);
                for (int k = 0; k < unitCount; ++k)
                {
                    lods[i].displayingUnits.Add(new MapLODManager.MapLODUnit(srcLOD.displayingUnits[k].unit, srcLOD.displayingUnits[k].relation));
                }
            }
            var lodManager = new MapLODManager(this, lods);
            return lodManager;
        }

        MapLocalObstacleManager CreateObstacleManager(config.SLGMakerData editorData)
        {
            var d = editorData.localObstacleManager;

            Dictionary<int, KeyValuePair<Vector3[], int[]>> obstacles = null;
            if (d.obstacles != null)
            {
                obstacles = new Dictionary<int, KeyValuePair<Vector3[], int[]>>();
                for (int i = 0; i < d.obstacles.Length; ++i)
                {
                    var modelTemplateID = d.obstacles[i].id;
                    var vertices = d.obstacles[i].vertices;
                    var indices = d.obstacles[i].triangleIndices;

                    var pair = new KeyValuePair<Vector3[], int[]>(vertices, indices);
                    obstacles[modelTemplateID] = pair;
                }
            }

            var obstacleManager = new MapLocalObstacleManager(this, editorData.map.width, editorData.map.height, d.regionWidth, d.regionHeight, d.tiles, obstacles, d.obstacleMaterialPath, editorData.map.isCircle);
            return obstacleManager;
        }

        MapGlobalObstacleManager CreateGlobalObstacleManager(config.SLGMakerData editorData)
        {
            var d = editorData.globalObstacleManager;
            return new MapGlobalObstacleManager("global obstacles", this, editorData.map.width, editorData.map.height, editorData.map.isCircle, d.obstacleMaterialPath, d.obstacleVertices, d.obstacleIndices, d.gridObstacles, d.gridSize);
        }

        public static DetailSpritesSetting CreateDetailSprites(config.DetailSpritesSetting ds, float mapWidth, float mapHeight)
        {
            if (ds != null)
            {
                var setting = new DetailSpritesSetting(mapWidth, mapHeight);
                setting.alpha0Height = ds.alpha0Height;
                setting.alpha1Height = ds.alpha1Height;
                setting.crossfading = ds.crossfading;
                setting.updateScale = ds.updateScale;
                setting.horizontalGridCount = ds.horizontaolGridCount;
                setting.verticalGridCount = ds.verticalGridCount;

                int nGroups = ds.groups.Length;
                setting.spriteGroups = new List<DetailSpriteGroup>(nGroups);
                for (int i = 0; i < nGroups; ++i)
                {
                    var groupData = ds.groups[i];
                    int nSprites = groupData.detailSpritesGUIDs.Length;
                    var detailSpritesGUIDs = new string[nSprites];
                    for (int k = 0; k < nSprites; ++k)
                    {
                        detailSpritesGUIDs[k] = groupData.detailSpritesGUIDs[k];
                    }
                    var group = new DetailSpriteGroup(groupData.name, groupData.color, detailSpritesGUIDs);
                    setting.spriteGroups.Add(group);
                }
                return setting;
            }
            return null;
        }

        void LoadCamera(config.SLGMakerData editorData, MapCameras camera)
        {
            var cameraData = editorData.map.camera;
            SceneView.lastActiveSceneView.rotation = cameraData.rotation;
            SceneView.lastActiveSceneView.pivot = cameraData.position;
        }

        PropertyBase LoadOneProperty(config.Property sourceProperty)
        {
            PropertyBase targetProperty = null;
            switch (sourceProperty.type)
            {
                case PropertyType.kPropertyBool:
                    targetProperty = new PropertyData<bool>(sourceProperty.name, sourceProperty.type, (bool)sourceProperty.value);
                    break;
                case PropertyType.kPropertyFloat:
                    targetProperty = new PropertyData<float>(sourceProperty.name, sourceProperty.type, (float)sourceProperty.value);
                    break;
                case PropertyType.kPropertyInt:
                    targetProperty = new PropertyData<int>(sourceProperty.name, sourceProperty.type, (int)sourceProperty.value);
                    break;
                case PropertyType.kPropertyIntArray:
                    targetProperty = new PropertyData<int[]>(sourceProperty.name, sourceProperty.type, (int[])sourceProperty.value);
                    break;
                case PropertyType.kPropertyString:
                    targetProperty = new PropertyData<string>(sourceProperty.name, sourceProperty.type, sourceProperty.value as string);
                    break;
                case PropertyType.kPropertyVector3:
                    targetProperty = new PropertyData<Vector3>(sourceProperty.name, sourceProperty.type, (Vector3)sourceProperty.value);
                    break;
                case PropertyType.kPropertyVector2:
                    targetProperty = new PropertyData<Vector2>(sourceProperty.name, sourceProperty.type, (Vector2)sourceProperty.value);
                    break;
                case PropertyType.kPropertyVector4:
                    targetProperty = new PropertyData<Vector4>(sourceProperty.name, sourceProperty.type, (Vector4)sourceProperty.value);
                    break;
                case PropertyType.kPropertyColor:
                    targetProperty = new PropertyData<Color>(sourceProperty.name, sourceProperty.type, (Color)sourceProperty.value);
                    break;
                default:
                    Debug.Assert(false);
                    break;
            }
            return targetProperty;
        }

        PropertyDatas LoadProperties(config.Properties source)
        {
            int n = 0;
            if (source != null)
            {
                n = source.properties.Count;
            }
            PropertyBase[] targetProperties = new PropertyBase[n];
            for (int i = 0; i < n; ++i)
            {
                var sourceProperty = source.properties[i];
                targetProperties[i] = LoadOneProperty(sourceProperty);
            }
            var properties = new PropertyDatas(targetProperties);
            return properties;
        }

        void LoadMapLayers(config.EditorMapData data)
        {
            int layerCount = data.map.mapLayers.Length;
            for (int i = 0; i < layerCount; ++i)
            {
                var sourceLayer = data.map.mapLayers[i];
                MapLayerBase targetLayer = null;
                if (sourceLayer.GetType() == typeof(config.NPCRegionLayerData))
                {
                    targetLayer = LoadNPCRegionLayer(sourceLayer as config.NPCRegionLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.SpriteTileLayerData))
                {
                    targetLayer = LoadSpriteTileLayer(sourceLayer as config.SpriteTileLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.GridModelLayerData))
                {
                    targetLayer = LoadGridModelLayer(sourceLayer as config.GridModelLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.ComplexGridModelLayerData))
                {
                    targetLayer = LoadComplexGridModelLayer(sourceLayer as config.ComplexGridModelLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.ModelLayerData))
                {
                    targetLayer = LoadModelLayer(sourceLayer as config.ModelLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.CircleBorderLayerData))
                {
                    targetLayer = LoadCircleBorderLayer(sourceLayer as config.CircleBorderLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.NavMeshLayerData))
                {
                    targetLayer = LoadNavMeshLayer(sourceLayer as config.NavMeshLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.BlendTerrainLayerData))
                {
                    targetLayer = LoadBlendTerrainLayer(sourceLayer as config.BlendTerrainLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.EntitySpawnLayerData))
                {
                    targetLayer = LoadEntitySpawnLayer(sourceLayer as config.EntitySpawnLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.RailwayLayerData))
                {
                    targetLayer = LoadRailwayLayer(sourceLayer as config.RailwayLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.LODLayerData))
                {
                    targetLayer = LoadLODLayer(sourceLayer as config.LODLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.MapCollisionLayerData))
                {
                    targetLayer = LoadMapCollisionLayer(sourceLayer as config.MapCollisionLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.RegionLayerData))
                {
                    targetLayer = LoadRegionLayer(sourceLayer as config.RegionLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.CameraColliderLayerData))
                {
                    targetLayer = LoadCameraColliderLayer(sourceLayer as config.CameraColliderLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.RuinLayerData))
                {
                    targetLayer = LoadRuinLayer(sourceLayer as config.RuinLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.PolygonRiverLayerData))
                {
                    targetLayer = LoadPolygonRiverLayer(sourceLayer as config.PolygonRiverLayerData);
                }
                else
                {
                    if (sourceLayer != null)
                    {
                        Debug.Log("Unknown layer " + sourceLayer.GetType().Name);
                    }
                }
                if (targetLayer != null)
                {
                    Map.currentMap.AddMapLayer(targetLayer);
                }
            }
        }

        MapLayerBase LoadSpriteTileLayer(config.SpriteTileLayerData sourceLayer)
        {
            var layer = new SpriteTileLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadNPCRegionLayer(config.NPCRegionLayerData sourceLayer)
        {
            var targetLayer = new NPCRegionLayer(Map.currentMap);
            targetLayer.Load(sourceLayer, null, false);
            return targetLayer;
        }

        MapLayerBase LoadGridModelLayer(config.GridModelLayerData sourceLayer)
        {
            var layer = new EditorGridModelLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadComplexGridModelLayer(config.ComplexGridModelLayerData sourceLayer)
        {
            var layer = new EditorComplexGridModelLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadModelLayer(config.ModelLayerData sourceLayer)
        {
            var layer = new ModelLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadCircleBorderLayer(config.CircleBorderLayerData sourceLayer)
        {
            var layer = new CircleBorderLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        BlendTerrainLayer LoadBlendTerrainLayer(config.BlendTerrainLayerData sourceLayer)
        {
            var layer = new BlendTerrainLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadNavMeshLayer(config.NavMeshLayerData sourceLayer)
        {
            var layer = new NavMeshLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadEntitySpawnLayer(config.EntitySpawnLayerData sourceLayer)
        {
            var layer = new EntitySpawnLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadRailwayLayer(config.RailwayLayerData sourceLayer)
        {
            var layer = new RailwayLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadLODLayer(config.LODLayerData sourceLayer)
        {
            var layer = new LODLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadMapCollisionLayer(config.MapCollisionLayerData sourceLayer)
        {
            var layer = new MapCollisionLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadRegionLayer(config.RegionLayerData sourceLayer)
        {
            var layer = new RegionLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadCameraColliderLayer(config.CameraColliderLayerData sourceLayer)
        {
            var layer = new CameraColliderLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadPolygonRiverLayer(config.PolygonRiverLayerData sourceLayer)
        {
            var layer = new PolygonRiverLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        MapLayerBase LoadRuinLayer(config.RuinLayerData sourceLayer)
        {
            var layer = new RuinLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            return layer;
        }

        void LoadSpriteTemplates(config.EditorMapData editorMapData)
        {
            int nTemplates = editorMapData.spriteTemplates.templates.Length;
            for (int i = 0; i < nTemplates; ++i)
            {
                var sourceTemplate = editorMapData.spriteTemplates.templates[i];

                var spriteTemplate = new SpriteTemplate(sourceTemplate.id, this, sourceTemplate.name, sourceTemplate.propertySetID, sourceTemplate.width, sourceTemplate.height, sourceTemplate.color);

                var editorData = mData as EditorMapData;
                editorData.AddSpriteTemplate(spriteTemplate);
            }
        }

        void LoadModelTemplates(config.EditorMapData editorMapData)
        {
            int nTemplates = editorMapData.modelTemplates.modelTemplates.Length;
            for (int i = 0; i < nTemplates; ++i)
            {
                var template = editorMapData.modelTemplates.modelTemplates[i];

                if (template is config.ModelTemplate)
                {
                    var meshModelTemplate = template as config.ModelTemplate;
                    Map.currentMap.AddModelTemplate(meshModelTemplate.prefabPath, this, Utils.BoundsToRect(meshModelTemplate.bounds), meshModelTemplate.isTileModelTemplate, new List<List<ModelTemplate>>(), new List<List<ChildPrefabTransform>>(), null, meshModelTemplate.id, template.preload);
                }
                else
                {
                    Debug.Assert(false, "Unknown template type");
                }
            }
        }

        void LoadPrefabManagers(config.EditorMapData editorMapData)
        {
            var mapData = mData as EditorMapData;
            if (mapData.version.majorVersion == 1)
            {
                CreatePrefabManager(mapData.gridModelLayerPrefabManager, editorMapData.gridModelLayerPrefabManager, false, true, false);
                CreatePrefabManager(mapData.modelLayerPrefabManager, editorMapData.modelLayerPrefabManager, false, true, false);
                CreatePrefabManager(mapData.circleBorderLayerPrefabManager, editorMapData.circleBorderLayerPrefabManager, false, true, false);
                CreatePrefabManager(mapData.complexGridModelLayerPrefabManager, editorMapData.complexGridModelLayerPrefabManager, false, true, true);
                CreatePrefabManager(mapData.editorTerrainPrefabManager, editorMapData.terrainPrefabManager, true, false, false);
                //CreatePrefabManager(mapData.varyingTileSizeTerrainPrefabManager, editorMapData.varyingTileSizeTerrainPrefabManager, true, false, false);

                int nGroups = mapData.editorTerrainPrefabManager.groupCount;
                for (int i = 0; i < nGroups; ++i)
                {
                    var group = mapData.editorTerrainPrefabManager.GetGroupByIndex(i);
                    int nPrefabs = group.count;
                    for (int k = 0; k < nPrefabs; ++k)
                    {
                        mapData.terrainPrefabManager.SetGroupPrefabByID(group.groupID, k, 0, group.GetPrefabPath(k));
                        var subGroupPrefabPaths = group.GetSubGroupPrefabPaths(k);
                        if (subGroupPrefabPaths != null)
                        {
                            int n = subGroupPrefabPaths.Length;
                            for (int x = 1; x < n; ++x)
                            {
                                mData.terrainPrefabManager.SetGroupPrefabByID(group.groupID, k, x, subGroupPrefabPaths[x]);
                            }
                        }
                    }
                }
            }
        }

        public static void CreatePrefabManager(PrefabManager prefabManager, config.PrefabManager source, bool checkVariation, bool forceDontAddPrefabSet, bool canOnlyAddOnPrefab)
        {
            if (prefabManager.groupCount == 0)
            {
                prefabManager.nextGroupID = source.nextGroupID;
                int n = source.groups.Length;
                for (int i = 0; i < n; ++i)
                {
                    var groupData = source.groups[i];
                    bool addPrefabSet = groupData.addPrefabSet;
                    if (forceDontAddPrefabSet)
                    {
                        addPrefabSet = false;
                    }
                    var group = prefabManager.AddGroup(groupData.id, groupData.name, groupData.color, addPrefabSet, false, 0);
                    int nPrefabs = groupData.prefabPaths.Length;
                    for (int j = 0; j < nPrefabs; ++j)
                    {
                        string prefabPath = groupData.prefabPaths[j].prefabPath;

                        //去掉无效的variation
                        var subGroupPrefabPaths = groupData.prefabPaths[j].subGroupPrefabPaths;
                        var validVariations = new string[subGroupPrefabPaths.Length];
                        for (int v = 0; v < subGroupPrefabPaths.Length; ++v)
                        {
                            if (File.Exists(subGroupPrefabPaths[v]))
                            {
                                validVariations[v] = subGroupPrefabPaths[v];
                            }
                            else
                            {
                                validVariations[v] = "";
                            }
                        }
                        var itemID = groupData.prefabPaths[j].id;
                        if (itemID == 0)
                        {
                            itemID = prefabManager.nextItemID;
                        }
                        group.AddPrefab(prefabPath, validVariations, false, false, canOnlyAddOnPrefab, null, groupData.prefabPaths[j].size, itemID);

                        //add decoration prefab
                        int decorationPrefabCount = groupData.prefabPaths[j].decorationPrefabGuids.Length;
                        for (int k = 0; k < decorationPrefabCount; ++k)
                        {
                            group.GetDecorationPrefabInfo(j).AddDecorationPrefab(groupData.prefabPaths[j].decorationPrefabGuids[k]);
                        }
                    }

                    if (checkVariation)
                    {
                        group.LoadVariationList();
                    }
                }
            }
        }

        void LoadPropertySets(config.EditorMapData editorData)
        {
            var mapData = mData as EditorMapData;
            int n = editorData.propertySets.propertySets.Length;
            for (int i = 0; i < n; ++i)
            {
                var sourcePropertySet = editorData.propertySets.propertySets[i];
                var properties = LoadProperties(sourcePropertySet.properties);
                var propertySet = new PropertySet(sourcePropertySet.id, this, sourcePropertySet.name, properties);
                mapData.propertySets.AddPropertySet(propertySet);
            }
        }

        public void SetSplineObjectManager(SplineObjectManager manager)
        {
            var editorMapData = data as EditorMapData;
            editorMapData.splineObjectManager = manager;
        }

        public void SetGridRegionEditor(GridRegionEditor editor)
        {
            var editorMapData = data as EditorMapData;
            var oldEditor = editorMapData.gridRegionEditor;
            if (oldEditor != null)
            {
                oldEditor.OnDestroy();
            }
            editorMapData.gridRegionEditor = editor;
        }

        public void SetDetailSprites(DetailSpritesSetting setting)
        {
            var editorMapData = data as EditorMapData;
            editorMapData.detailSpritesSetting = setting;
        }

        public void Resize(float newWidth, float newHeight)
        {
            mData.Resize(newWidth, newHeight);
            mView.Resize(newWidth, newHeight);

            int layerCount = GetMapLayerCount();
            for (int i = 0; i < layerCount; ++i)
            {
                var layer = GetMapLayerByIndex(i);
                layer.Resize(newWidth, newHeight, false);
            }

            ActionManager.instance.Clear();
        }

        public void InvokeExportMapEvent()
        {
            if (exportMapEvent != null)
            {
                exportMapEvent();
            }
        }

        public void InvokeSaveMapEvent()
        {
            if (saveMapEvent != null)
            {
                saveMapEvent();
            }
        }

        public void UpdateLayerInEditor()
        {
            int layerCount = GetMapLayerCount();
            for (int i = layerCount - 1; i >= 0; --i)
            {
                var layer = GetMapLayerByIndex(i);
                layer.UpdateInEditor();
            }
        }

        public bool navMeshRegionVisible
        {
            get
            {
                return mNavMeshRegionVisible;
            }
            set
            {
                mNavMeshRegionVisible = value;
                var layer = GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;
                if (layer != null)
                {
                    layer.ShowNavMeshRegions(value);
                }
            }
        }

        public override bool updateViewport => false;

        public GlobalGrid grid { get { return mGrid; } }
        public GameMapType mapType { get { return mMapType; } }
        public bool generateNPCSpawnPointsInBorderLine
        {
            get { return mGenerateNPCSpawnPointsInBorderLine; }
            set
            {
                mGenerateNPCSpawnPointsInBorderLine = value;
            }
        }

        public bool removeSameHoles
        {
            get { return mRemoveSameHoles; }
            set
            {
                mRemoveSameHoles = value;
            }
        }

        public NavigationCreateMode navMeshMode { get { return mNavMeshMode; } set { mNavMeshMode = value; } }
        public NavigationCreateMode globalObstacleMode { get { return mGlobalObstacleMode; } set { mGlobalObstacleMode = value; } }

        GlobalGrid mGrid;

        GameMapType mMapType = GameMapType.Empty;
        bool mGenerateNPCSpawnPointsInBorderLine;
        bool mRemoveSameHoles;

        public bool saveRiverMaterials = false;
        public string riverMaterialsFolder;
        bool mNavMeshRegionVisible = false;
        NavigationCreateMode mNavMeshMode;
        NavigationCreateMode mGlobalObstacleMode;

        public event System.Action exportMapEvent;
        public event System.Action saveMapEvent;
    }
}

#endif

