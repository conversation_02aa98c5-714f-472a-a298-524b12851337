﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.RailwayLayerData LoadRailwayLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.RailwayLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //----------------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            int n = reader.ReadInt32();
            config.MapObjectData[] objects = new config.MapObjectData[n];
            for (int i = 0; i < n; ++i)
            {
                objects[i] = LoadRailObjectDataV1(reader);
            }

            var config = LoadRailwayLayerLODConfigV1(reader);

            var lodGroupManager = LoadModelLODGroupManagerV1(reader);

            int count = reader.ReadInt32();
            float width = reader.ReadSingle();
            float radius = reader.ReadSingle();
            Vector3 center = Utils.ReadVector3(reader);
            float railPrefabLength = reader.ReadSingle();
            //---------------------------version 1 end-------------------------------
            //-------------------version 2 start-----------------------
            if (version >= 2)
            {
                LoadRailwayLayerLODConfigV2(reader, config);
            }
            //-------------------version 2 end-----------------------

            var layerData = new config.RailwayLayerData(layerID, name, offset, config, lodGroupManager, layerWidth, layerHeight, objects, count, width, radius, center, railPrefabLength);
            return layerData;
        }

        config.RailObjectData LoadRailObjectDataV1(BinaryReader reader)
        {
            var modelData = new config.RailObjectData();
            bool isDefaultRotation;
            bool isDefaultScale;

            modelData.SetID(reader.ReadInt32());

            modelData.position = Utils.ReadVector3(reader);
            isDefaultRotation = reader.ReadBoolean();
            if (!isDefaultRotation)
            {
                modelData.rotation = Utils.ReadQuaternion(reader);
            }
            isDefaultScale = reader.ReadBoolean();
            if (!isDefaultScale)
            {
                modelData.scale = Utils.ReadVector3(reader);
            }

            modelData.modelTemplateID = reader.ReadInt32();
            modelData.type = (RailObjectType)reader.ReadInt32();
            modelData.isGroupLeader = reader.ReadBoolean();
            modelData.groupID = reader.ReadInt32();
            modelData.railIndex = reader.ReadInt32();
            modelData.segmentIndex = reader.ReadInt32();

            return modelData;
        }

        config.ModelLODGroupManager LoadModelLODGroupManagerV1(BinaryReader reader)
        {
            config.ModelLODGroupManager lodGroupManager = new config.ModelLODGroupManager();
            int nGroups = reader.ReadInt32();
            lodGroupManager.groups = new config.ModelLODGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                var group = new config.ModelLODGroup();
                var id = reader.ReadInt32();
                group.SetID(id);
                group.combineModels = reader.ReadBoolean();
                group.leaderObjectID = reader.ReadInt32();
                group.lod = reader.ReadInt32();

                lodGroupManager.groups[i] = group;
            }
            return lodGroupManager;
        }

        config.MapLayerLODConfig LoadRailwayLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, false, MapLayerLODConfigFlag.None, 0);
            }
            return config;
        }

        void LoadRailwayLayerLODConfigV2(BinaryReader reader, config.MapLayerLODConfig config)
        {
            int n = config.lodConfigs.Length;
            for (int i = 0; i < n; ++i)
            {
                config.lodConfigs[i].useRenderTexture = reader.ReadBoolean();
            }
        }
    }
}
