// Shader created with Shader Forge v1.38 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:3,bdst:7,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:False,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:34501,y:32757,varname:node_4795,prsc:2|emission-5303-OUT,alpha-9634-OUT,voffset-1051-OUT;n:type:ShaderForge.SFN_Tex2d,id:7126,x:32544,y:32467,ptovrint:False,ptlb:Texture,ptin:_Texture,varname:node_1635,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-4420-OUT;n:type:ShaderForge.SFN_Color,id:2031,x:32600,y:32841,ptovrint:False,ptlb:Diffuse_Color,ptin:_Diffuse_Color,varname:node_5612,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Multiply,id:5303,x:33453,y:32559,varname:node_5303,prsc:2|A-2507-OUT,B-2031-RGB,C-9576-RGB;n:type:ShaderForge.SFN_VertexColor,id:9576,x:32581,y:33018,varname:node_9576,prsc:2;n:type:ShaderForge.SFN_Multiply,id:6758,x:33445,y:32995,varname:node_6758,prsc:2|A-485-OUT,B-2031-A,C-9576-A;n:type:ShaderForge.SFN_DepthBlend,id:7540,x:33361,y:33139,varname:node_7540,prsc:2|DIST-889-OUT;n:type:ShaderForge.SFN_ValueProperty,id:889,x:33155,y:33139,ptovrint:False,ptlb:DepthBlend_Power,ptin:_DepthBlend_Power,varname:node_3396,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Desaturate,id:2507,x:33160,y:32477,varname:node_2507,prsc:2|COL-7126-RGB,DES-7780-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7780,x:33000,y:32578,ptovrint:False,ptlb:Desaturate,ptin:_Desaturate,varname:_Desaturate_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Power,id:725,x:32963,y:32639,varname:node_725,prsc:2|VAL-3694-OUT,EXP-7230-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7230,x:32779,y:32730,ptovrint:False,ptlb:Power,ptin:_Power,varname:node_2840,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:3694,x:32779,y:32563,ptovrint:False,ptlb:Alpha_Switch,ptin:_Alpha_Switch,varname:node_4099,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:True|A-7126-R,B-7126-A;n:type:ShaderForge.SFN_Multiply,id:9634,x:33765,y:32991,varname:node_9634,prsc:2|A-6758-OUT,B-7540-OUT,C-301-OUT,D-9976-OUT,E-6817-OUT;n:type:ShaderForge.SFN_TexCoord,id:4639,x:31708,y:32924,varname:node_4639,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_TexCoord,id:1481,x:31951,y:32447,varname:node_1481,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:7728,x:31895,y:32696,varname:node_7728,prsc:2|A-5616-OUT,B-1936-OUT;n:type:ShaderForge.SFN_Add,id:4420,x:32317,y:32484,varname:node_4420,prsc:2|A-1481-UVOUT,B-7058-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6891,x:31443,y:32641,ptovrint:False,ptlb:U_speed,ptin:_U_speed,varname:_Texture_U_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:4817,x:31455,y:32817,ptovrint:False,ptlb:V_speed,ptin:_V_speed,varname:_Texture_V_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:5616,x:31681,y:32641,varname:node_5616,prsc:2|A-6891-OUT,B-9748-T;n:type:ShaderForge.SFN_Time,id:9748,x:31339,y:32688,varname:node_9748,prsc:2;n:type:ShaderForge.SFN_Multiply,id:1936,x:31681,y:32783,varname:node_1936,prsc:2|A-9748-T,B-4817-OUT;n:type:ShaderForge.SFN_Append,id:8046,x:31923,y:32944,varname:node_8046,prsc:2|A-4639-U,B-4639-V;n:type:ShaderForge.SFN_SwitchProperty,id:7058,x:32073,y:32696,ptovrint:False,ptlb:Custom_UV,ptin:_Custom_UV,varname:_Custom_UV_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-7728-OUT,B-8046-OUT;n:type:ShaderForge.SFN_Multiply,id:485,x:33371,y:32719,varname:node_485,prsc:2|A-725-OUT,B-326-OUT;n:type:ShaderForge.SFN_Tex2d,id:8723,x:32537,y:33197,ptovrint:False,ptlb:Mask,ptin:_Mask,varname:node_8723,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-4672-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:326,x:33201,y:32850,ptovrint:False,ptlb:Mask_Switch,ptin:_Mask_Switch,varname:node_326,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-8723-R,B-8723-A;n:type:ShaderForge.SFN_ValueProperty,id:301,x:33653,y:32794,ptovrint:False,ptlb:Intensity,ptin:_Intensity,varname:node_301,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Fresnel,id:9976,x:33534,y:33222,varname:node_9976,prsc:2|EXP-6675-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6675,x:33263,y:33278,ptovrint:False,ptlb:Fresnel,ptin:_Fresnel,varname:node_6675,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Tex2d,id:233,x:33995,y:33411,ptovrint:False,ptlb:nois,ptin:_nois,varname:node_233,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-6196-OUT;n:type:ShaderForge.SFN_Vector4Property,id:2546,x:33797,y:33682,ptovrint:False,ptlb:pianyi,ptin:_pianyi,varname:node_2546,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0,v2:0,v3:0,v4:0;n:type:ShaderForge.SFN_Multiply,id:1051,x:34329,y:33493,varname:node_1051,prsc:2|A-233-RGB,B-2546-XYZ,C-256-OUT;n:type:ShaderForge.SFN_TexCoord,id:9024,x:33395,y:33370,varname:node_9024,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:207,x:33445,y:33542,varname:node_207,prsc:2|A-3622-OUT,B-320-OUT;n:type:ShaderForge.SFN_Add,id:6196,x:33707,y:33420,varname:node_6196,prsc:2|A-9024-UVOUT,B-207-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7715,x:32769,y:33406,ptovrint:False,ptlb:U_speed_nois,ptin:_U_speed_nois,varname:_U_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:5801,x:32798,y:33725,ptovrint:False,ptlb:V_speed_nois,ptin:_V_speed_nois,varname:_V_speed_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:3622,x:33155,y:33496,varname:node_3622,prsc:2|A-7715-OUT,B-3598-T;n:type:ShaderForge.SFN_Time,id:3598,x:32570,y:33516,varname:node_3598,prsc:2;n:type:ShaderForge.SFN_Multiply,id:320,x:33155,y:33647,varname:node_320,prsc:2|A-3598-T,B-5801-OUT;n:type:ShaderForge.SFN_ValueProperty,id:256,x:34174,y:33720,ptovrint:False,ptlb:niuqu_qiangdu,ptin:_niuqu_qiangdu,varname:node_256,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_TexCoord,id:7984,x:31790,y:33136,varname:node_7984,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:9926,x:32135,y:33390,varname:node_9926,prsc:2|A-8823-OUT,B-2620-OUT;n:type:ShaderForge.SFN_Add,id:4672,x:32261,y:33184,varname:node_4672,prsc:2|A-7984-UVOUT,B-9926-OUT;n:type:ShaderForge.SFN_Multiply,id:8823,x:31921,y:33335,varname:node_8823,prsc:2|A-4515-OUT,B-486-T;n:type:ShaderForge.SFN_Time,id:486,x:31579,y:33382,varname:node_486,prsc:2;n:type:ShaderForge.SFN_Multiply,id:2620,x:31921,y:33477,varname:node_2620,prsc:2|A-486-T,B-9241-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9241,x:31579,y:33605,ptovrint:False,ptlb:Mask_v,ptin:_Mask_v,varname:node_9241,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:4515,x:31579,y:33297,ptovrint:False,ptlb:Mask_U,ptin:_Mask_U,varname:node_4515,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:9982,x:32019,y:34139,ptovrint:False,ptlb:Dissolution_soft,ptin:_Dissolution_soft,varname:_Dissolution_soft_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Multiply,id:4264,x:32688,y:33939,varname:node_4264,prsc:2|A-3154-R,B-9982-OUT;n:type:ShaderForge.SFN_TexCoord,id:8482,x:31850,y:34323,varname:node_8482,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_Vector1,id:1117,x:32053,y:34265,varname:node_1117,prsc:2,v1:-1.5;n:type:ShaderForge.SFN_Lerp,id:186,x:32733,y:34169,varname:node_186,prsc:2|A-9982-OUT,B-1117-OUT,T-2750-OUT;n:type:ShaderForge.SFN_Subtract,id:5990,x:33006,y:34080,varname:node_5990,prsc:2|A-4264-OUT,B-186-OUT;n:type:ShaderForge.SFN_Clamp01,id:1077,x:33232,y:34039,varname:node_1077,prsc:2|IN-5990-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:2750,x:32388,y:34325,ptovrint:False,ptlb:Diss_CutUV_Switch(X),ptin:_Diss_CutUV_SwitchX,varname:_DissolutionY,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-9576-A,B-8482-U;n:type:ShaderForge.SFN_Tex2d,id:3154,x:32080,y:33817,ptovrint:False,ptlb:Dissolution_Tex,ptin:_Dissolution_Tex,varname:_Dissolution_Tex_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Vector1,id:2672,x:32960,y:33880,varname:node_2672,prsc:2,v1:1;n:type:ShaderForge.SFN_SwitchProperty,id:6817,x:33583,y:34007,ptovrint:False,ptlb:Diss_Switch,ptin:_Diss_Switch,varname:node_6817,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-2672-OUT,B-1077-OUT;proporder:7780-2031-3694-7126-7058-6891-4817-7230-889-326-8723-4515-9241-301-233-7715-5801-2546-256-6817-2750-3154-9982-6675;pass:END;sub:END;*/

Shader "Core/ZTest/AlphaBlended_Two_mask" {
    Properties {
        _Desaturate ("Desaturate", Float ) = 0
        [HDR]_Diffuse_Color ("Diffuse_Color", Color) = (0.5,0.5,0.5,1)
        [MaterialToggle] _Alpha_Switch ("Alpha_Switch", Float ) = 0
        _Texture ("Texture", 2D) = "white" {}
        [MaterialToggle] _Custom_UV ("Custom_UV", Float ) = 0
        _U_speed ("U_speed", Float ) = 0
        _V_speed ("V_speed", Float ) = 0
        _Power ("Power", Float ) = 1
        _DepthBlend_Power ("DepthBlend_Power", Float ) = 0
        [MaterialToggle] _Mask_Switch ("Mask_Switch", Float ) = 0
        _Mask ("Mask", 2D) = "white" {}
        _Mask_U ("Mask_U", Float ) = 0
        _Mask_v ("Mask_v", Float ) = 0
        _Intensity ("Intensity", Float ) = 1
        _nois ("nois", 2D) = "white" {}
        _U_speed_nois ("U_speed_nois", Float ) = 0
        _V_speed_nois ("V_speed_nois", Float ) = 0
        _pianyi ("pianyi", Vector) = (0,0,0,0)
        _niuqu_qiangdu ("niuqu_qiangdu", Float ) = 0
        [MaterialToggle] _Diss_Switch ("Diss_Switch", Float ) = 1
        [MaterialToggle] _Diss_CutUV_SwitchX ("Diss_CutUV_Switch(X)", Float ) = 0
        _Dissolution_Tex ("Dissolution_Tex", 2D) = "white" {}
        _Dissolution_soft ("Dissolution_soft", Float ) = 1
        _Fresnel ("Fresnel", Float ) = 0
        [HideInInspector]_Cutoff ("Alpha cutoff", Range(0,1)) = 0.5
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma only_renderers d3d9 d3d11 glcore gles gles3 metal d3d11_9x xboxone ps4 psp2 n3ds wiiu 
            #pragma target 3.0
            uniform sampler2D _CameraDepthTexture;
            uniform sampler2D _Texture; uniform float4 _Texture_ST;
            uniform float4 _Diffuse_Color;
            uniform float _DepthBlend_Power;
            uniform float _Desaturate;
            uniform float _Power;
            uniform fixed _Alpha_Switch;
            uniform float _U_speed;
            uniform float _V_speed;
            uniform fixed _Custom_UV;
            uniform sampler2D _Mask; uniform float4 _Mask_ST;
            uniform fixed _Mask_Switch;
            uniform float _Intensity;
            uniform float _Fresnel;
            uniform sampler2D _nois; uniform float4 _nois_ST;
            uniform float4 _pianyi;
            uniform float _U_speed_nois;
            uniform float _V_speed_nois;
            uniform float _niuqu_qiangdu;
            uniform float _Mask_v;
            uniform float _Mask_U;
            uniform float _Dissolution_soft;
            uniform fixed _Diss_CutUV_SwitchX;
            uniform sampler2D _Dissolution_Tex; uniform float4 _Dissolution_Tex_ST;
            uniform fixed _Diss_Switch;
            struct VertexInput {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 posWorld : TEXCOORD2;
                float3 normalDir : TEXCOORD3;
                float4 vertexColor : COLOR;
                float4 projPos : TEXCOORD4;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                float4 node_3598 = _Time;
                float2 node_6196 = (o.uv0+float2((_U_speed_nois*node_3598.g),(node_3598.g*_V_speed_nois)));
                float4 _nois_var = tex2Dlod(_nois,float4(TRANSFORM_TEX(node_6196, _nois),0.0,0));
                v.vertex.xyz += (_nois_var.rgb*_pianyi.rgb*_niuqu_qiangdu);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos = UnityObjectToClipPos( v.vertex );
                o.projPos = ComputeScreenPos (o.pos);
                COMPUTE_EYEDEPTH(o.projPos.z);
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                i.normalDir = normalize(i.normalDir);
                i.normalDir *= faceSign;
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
                float sceneZ = max(0,LinearEyeDepth (UNITY_SAMPLE_DEPTH(tex2Dproj(_CameraDepthTexture, UNITY_PROJ_COORD(i.projPos)))) - _ProjectionParams.g);
                float partZ = max(0,i.projPos.z - _ProjectionParams.g);
////// Lighting:
////// Emissive:
                float4 node_9748 = _Time;
                float2 node_4420 = (i.uv0+lerp( float2((_U_speed*node_9748.g),(node_9748.g*_V_speed)), float2(i.uv1.r,i.uv1.g), _Custom_UV ));
                float4 _Texture_var = tex2D(_Texture,TRANSFORM_TEX(node_4420, _Texture));
                float3 emissive = (lerp(_Texture_var.rgb,dot(_Texture_var.rgb,float3(0.3,0.59,0.11)),_Desaturate)*_Diffuse_Color.rgb*i.vertexColor.rgb);
                float3 finalColor = emissive;
                float4 node_486 = _Time;
                float2 node_4672 = (i.uv0+float2((_Mask_U*node_486.g),(node_486.g*_Mask_v)));
                float4 _Mask_var = tex2D(_Mask,TRANSFORM_TEX(node_4672, _Mask));
                float node_2672 = 1.0;
                float4 _Dissolution_Tex_var = tex2D(_Dissolution_Tex,TRANSFORM_TEX(i.uv0, _Dissolution_Tex));
                float node_1077 = saturate(((_Dissolution_Tex_var.r*_Dissolution_soft)-lerp(_Dissolution_soft,(-1.5),lerp( i.vertexColor.a, i.uv1.r, _Diss_CutUV_SwitchX ))));
                return fixed4(finalColor,(((pow(lerp( _Texture_var.r, _Texture_var.a, _Alpha_Switch ),_Power)*lerp( _Mask_var.r, _Mask_var.a, _Mask_Switch ))*_Diffuse_Color.a*i.vertexColor.a)*saturate((sceneZ-partZ)/_DepthBlend_Power)*_Intensity*pow(1.0-max(0,dot(normalDirection, viewDirection)),_Fresnel)*lerp( node_2672, node_1077, _Diss_Switch )));
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
