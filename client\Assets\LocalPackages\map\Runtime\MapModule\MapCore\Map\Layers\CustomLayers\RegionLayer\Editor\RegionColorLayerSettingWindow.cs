﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class RegionColorLayerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Create"))
            {
                var map = Map.currentMap as EditorMap;
                bool valid = CheckParameter();
                if (valid)
                {
                    var layer = map.CreateRegionColorLayer(MapCoreDef.MAP_LAYER_NODE_REGION_COLOR, mLayerWidth, mLayerHeight, mTileWidth, mTileHeight);
                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public float mTileWidth = 12;
        public float mTileHeight = 12;
    }
}

#endif