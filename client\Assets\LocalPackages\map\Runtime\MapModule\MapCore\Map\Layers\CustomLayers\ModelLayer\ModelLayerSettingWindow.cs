﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System;

namespace TFW.Map {
    public class ModelLayerSettingWindow : EditorWindow {
        public void Show(string layerName, Func<string, float, float, ModelLayer> createLayer)
        {
            mLayerName = layerName;
            mCreateLayer = createLayer;
        }

        void OnEnable() {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI() {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Height", mLayerHeight);
            GUILayout.EndHorizontal();

            if (GUILayout.Button("Create")) {
                bool valid = CheckParameter();
                if (valid) {
                    var layer = mCreateLayer(mLayerName, mLayerWidth, mLayerHeight);
                    layer.asyncLoading = false;

                    Close();
                }
                else {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter() {
            if (mLayerWidth <= 0 || mLayerHeight <= 0) {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public string mLayerName;
        Func<string, float, float, ModelLayer> mCreateLayer;
    }
}

#endif