; FBX 7.4.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2019
		Month: 8
		Day: 29
		Hour: 13
		Minute: 13
		Second: 43
		Millisecond: 980
	}
	Creator: "FBX SDK/FBX Plugins version 2014.0.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\Mesh_06_zx.FBX"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\Mesh_06_zx.FBX"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "3ds Max"
			P: "Original|ApplicationVersion", "KString", "", "", "2014"
			P: "Original|DateTime_GMT", "DateTime", "", "", "29/08/2019 05:13:43.969"
			P: "Original|FileName", "KString", "", "", "C:\Users\<USER>\Desktop\Mesh_06_zx.FBX"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "3ds Max"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2014"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "29/08/2019 05:13:43.969"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",2
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",6
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",153953860000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1064764736, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1009345008, "Geometry::", "Mesh" {
		Properties70:  {
			P: "Color", "ColorRGB", "Color", "",0.87843137254902,0.776470588235294,0.341176470588235
		}
		Vertices: *252 {
			a: -50.6474456787109,13.9636087417603,-15.1771640777588,-52.0317764282227,7.27084589004517,-15.1771640777588,-52.534538269043,0.52059018611908,-15.1771640777588,-52.1692123413086,-6.20344161987305,-15.1771640777588,-50.9492950439453,-12.8175315856934,-15.1771640777588,-48.8882675170898,-19.2379722595215,-15.1771640777588,-45.9996337890625,-25.3810367584229,-15.1771621704102,-42.3037719726563,-31.1535396575928,-15.1771659851074,-37.9190826416016,-36.3636207580566,-15.1771659851074,-32.9399871826172,-40.9278717041016,-15.1771659851074,-27.4387855529785,-44.8019981384277,-15.1771659851074,-21.4878025054932,-47.941707611084,-15.1771659851074,-15.1593465805054,-50.3027267456055,-15.1771659851074,-8.52572727203369,-51.840763092041,-15.1771640777588,-1.70415592193604,-52.5096130371094,-15.1771659851074,5.08065700531006,-52.2910079956055,-15.1771659851074,11.7364320755005,-51.2091293334961,-15.1771659851074,18.1824798583984,-49.2900276184082,-15.1771659851074,24.3380832672119,-46.5597038269043,-15.1771659851074,30.1225395202637,-43.0442047119141,-15.1771659851074,35.4551467895508,-38.7695503234863,-15.1771659851074,-50.6474456787109,13.9636087417603,15.1771621704102,-52.0317764282227,7.27084589004517,15.1771621704102,-52.534538269043,0.52059018611908,15.1771621704102,-52.1692123413086,-6.20344161987305,15.1771621704102,-50.9492950439453,-12.8175315856934,15.1771621704102,-48.8882675170898,-19.2379722595215,15.1771621704102,-45.9996337890625,-25.3810367584229,15.1771621704102,-42.3037719726563,-31.1535396575928,15.1771659851074,-37.9190826416016,-36.3636207580566,15.1771659851074,-32.9399871826172,-40.9278717041016,15.1771659851074,-27.4387855529785,-44.8019981384277,15.1771659851074,-21.4878025054932,-47.941707611084,15.1771659851074,-15.1593465805054,-50.3027267456055,15.1771659851074,-8.52572727203369,-51.840763092041,15.1771621704102,-1.70415592193604,-52.5096130371094,15.1771659851074,5.08065700531006,-52.2910079956055,15.1771659851074,11.7364320755005,-51.2091293334961,15.1771659851074,18.1824798583984,-49.2900276184082,15.1771659851074,
24.3380832672119,-46.5597038269043,15.1771659851074,30.1225395202637,-43.0442047119141,15.1771659851074,35.4551467895508,-38.7695503234863,15.1771659851074,-65.2787017822266,17.9975337982178,0,-67.0628662109375,9.37147998809814,0,-67.7109680175781,0.670014500617981,0,-67.2401504516602,-7.9959602355957,0,-65.6680374145508,-16.5195598602295,0,-63.0115585327148,-24.7948570251465,0,-59.2878723144531,-32.7137718200684,0,-54.5250854492188,-40.152759552002,0,-48.8730773925781,-46.8686790466309,0,-42.4551010131836,-52.751953125,0,-35.3654937744141,-57.7447052001953,0,-27.6962184906006,-61.7909660339355,0,-19.5389099121094,-64.8342742919922,0,-10.9879999160767,-66.8168640136719,0,-2.19709873199463,-67.6787719726563,0,6.5490779876709,-67.39697265625,0,15.1277103424072,-66.0025634765625,0,23.4347553253174,-63.5294075012207,0,31.3681259155273,-60.0105361938477,0,38.8246154785156,-55.4788436889648,0,45.697639465332,-49.9694442749023,0,-36.0161819458008,9.92968368530273,0,-37.0006866455078,5.17021179199219,0,-37.3581085205078,0.371165990829468,0,-37.098274230957,-4.41092300415039,0,-36.2305526733398,-9.11550331115723,0,-34.7649765014648,-13.6810874938965,0,-32.7113952636719,-18.0483016967773,0,-30.082462310791,-22.1543197631836,0,-26.9650802612305,-25.8585624694824,0,-23.4248733520508,-29.1037883758545,0,-19.5120792388916,-31.8592910766602,0,-15.2793865203857,-34.0924491882324,0,-10.7797832489014,-35.771183013916,0,-6.06345462799072,-36.8646659851074,0,-1.21121323108673,-37.3404541015625,0,3.61223602294922,-37.1850471496582,0,8.34515380859375,-36.415699005127,0,12.9302043914795,-35.0506477355957,0,17.3080406188965,-33.1088714599609,0,21.4204635620117,-30.6095657348633,0,25.2126579284668,-27.5696544647217,0
		} 
		PolygonVertexIndex: *240 {
			a: 21,0,-23,1,22,-1,22,1,-24,2,23,-2,23,2,-25,3,24,-3,24,3,-26,4,25,-4,25,4,-27,5,26,-5,26,5,-28,6,27,-6,27,6,-29,7,28,-7,28,7,-30,8,29,-8,29,8,-31,9,30,-9,30,9,-32,10,31,-10,31,10,-33,11,32,-11,32,11,-34,12,33,-12,33,12,-35,13,34,-13,34,13,-36,14,35,-14,35,14,-37,15,36,-15,36,15,-38,16,37,-16,37,16,-39,17,38,-17,38,17,-40,18,39,-18,39,18,-41,19,40,-19,40,19,-42,20,41,-20,63,42,-65,43,64,-43,64,43,-66,44,65,-44,65,44,-67,45,66,-45,66,45,-68,46,67,-46,67,46,-69,47,68,-47,68,47,-70,48,69,-48,69,48,-71,49,70,-49,70,49,-72,50,71,-50,71,50,-73,51,72,-51,72,51,-74,52,73,-52,73,52,-75,53,74,-53,74,53,-76,54,75,-54,75,54,-77,55,76,-55,76,55,-78,56,77,-56,77,56,-79,57,78,-57,78,57,-80,58,79,-58,79,58,-81,59,80,-59,80,59,-82,60,81,-60,81,60,-83,61,82,-61,82,61,-84,62,83,-62
		} 
		Edges: *162 {
			a: 0,1,2,3,5,7,8,9,11,13,14,15,17,19,20,21,23,25,26,27,29,31,32,33,35,37,38,39,41,43,44,45,47,49,50,51,53,55,56,57,59,61,62,63,65,67,68,69,71,73,74,75,77,79,80,81,83,85,86,87,89,91,92,93,95,97,98,99,101,103,104,105,107,109,110,111,113,115,116,117,119,120,121,122,123,125,127,128,129,131,133,134,135,137,139,140,141,143,145,146,147,149,151,152,153,155,157,158,159,161,163,164,165,167,169,170,171,173,175,176,177,179,181,182,183,185,187,188,189,191,193,194,195,197,199,200,201,203,205,206,207,209,211,212,213,215,217,218,219,221,223,224,225,227,229,230,231,233,235,236,237,239
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *720 {
				a: -0.979271411895752,0.202552452683449,0,-0.979271471500397,0.202552452683449,0,-0.99033385515213,0.138704791665077,0,-0.99033385515213,0.138704791665077,0,-0.99033385515213,0.138704791665077,0,-0.979271471500397,0.202552452683449,0,-0.99033385515213,0.138704791665077,0,-0.99033385515213,0.138704791665077,0,-0.99994969367981,0.0100323976948857,0,-0.99994969367981,0.0100324079394341,0,-0.99994969367981,0.0100323976948857,0,-0.99033385515213,0.138704791665077,0,-0.99994969367981,0.0100323976948857,0,-0.99994969367981,0.0100324079394341,0,-0.993006527423859,-0.118059210479259,0,-0.993006587028503,-0.118059203028679,0,-0.993006527423859,-0.118059210479259,0,-0.99994969367981,0.0100324079394341,0,-0.993006527423859,-0.118059210479259,0,-0.993006587028503,-0.118059203028679,0,-0.969770967960358,-0.24401681125164,0,-0.969771027565002,-0.244016826152802,0,-0.969770967960358,-0.24401681125164,0,-0.993006587028503,-0.118059203028679,0,-0.969770967960358,-0.24401681125164,0,-0.969771027565002,-0.244016826152802,0,-0.930477201938629,-0.366349697113037,0,-0.930477201938629,-0.366349637508392,0,-0.930477201938629,-0.366349697113037,0,-0.969771027565002,-0.244016826152802,0,-0.930477201938629,-0.366349697113037,0,-0.930477201938629,-0.366349637508392,0,-0.875406742095947,-0.483386963605881,0,-0.875406682491302,-0.483386963605881,0,-0.875406742095947,-0.483386963605881,0,-0.930477201938629,-0.366349637508392,0,-0.875406742095947,-0.483386963605881,0,-0.875406682491302,-0.483386963605881,0,-0.805345416069031,-0.592805862426758,0,-0.805345416069031,-0.592805922031403,0,-0.805345416069031,-0.592805862426758,0,-0.875406682491302,-0.483386963605881,0,-0.805345416069031,-0.592805862426758,0,-0.805345416069031,-0.592805922031403,0,-0.721927642822266,-0.691968560218811,0,-0.721927642822266,-0.691968560218811,0,-0.721927642822266,-0.691968560218811,0,-0.805345416069031,-0.592805922031403,0,-0.721927642822266,-0.691968560218811,0,-0.721927642822266,-0.691968560218811,0,-0.627048552036285,-0.778980195522308,0,-0.627048552036285,-0.778980195522308,0,
-0.627048552036285,-0.778980195522308,0,-0.721927642822266,-0.691968560218811,0,-0.627048552036285,-0.778980195522308,0,-0.627048552036285,-0.778980195522308,0,-0.522278010845184,-0.8527752161026,0,-0.522278070449829,-0.852775275707245,0,-0.522278010845184,-0.8527752161026,0,-0.627048552036285,-0.778980195522308,0,-0.522278010845184,-0.8527752161026,0,-0.522278070449829,-0.852775275707245,0,-0.408931314945221,-0.912565231323242,0,-0.408931285142899,-0.912565171718597,0,-0.408931314945221,-0.912565231323242,0,-0.522278070449829,-0.852775275707245,0,-0.408931314945221,-0.912565231323242,0,-0.408931285142899,-0.912565171718597,0,-0.288306444883347,-0.957538187503815,0,-0.288306474685669,-0.957538187503815,0,-0.288306444883347,-0.957538187503815,0,-0.408931285142899,-0.912565171718597,0,-0.288306444883347,-0.957538187503815,0,-0.288306474685669,-0.957538187503815,0,-0.162065088748932,-0.986780047416687,0,-0.162065103650093,-0.986780107021332,0,-0.162065088748932,-0.986780047416687,0,-0.288306474685669,-0.957538187503815,0,-0.162065088748932,-0.986780047416687,0,-0.162065103650093,-0.986780107021332,0,-0.0327582433819771,-0.999463319778442,0,-0.0327582471072674,-0.999463319778442,0,-0.0327582433819771,-0.999463319778442,0,-0.162065103650093,-0.986780107021332,0,-0.0327582433819771,-0.999463319778442,0,-0.0327582471072674,-0.999463319778442,0,0.096522830426693,-0.995330810546875,0,0.0965228080749512,-0.99533075094223,0,0.096522830426693,-0.995330810546875,0,-0.0327582471072674,-0.999463319778442,0,0.096522830426693,-0.995330810546875,0,0.0965228080749512,-0.99533075094223,0,0.223349809646606,-0.974738299846649,0,0.223349839448929,-0.974738299846649,0,0.223349809646606,-0.974738299846649,0,0.0965228080749512,-0.99533075094223,0,0.223349809646606,-0.974738299846649,0,0.223349839448929,-0.974738299846649,0,0.346108168363571,-0.938194572925568,0,0.346108198165894,-0.938194572925568,0,0.346108168363571,-0.938194572925568,0,0.223349839448929,-0.974738299846649,0,0.346108168363571,-0.938194572925568,0,0.346108198165894,-0.938194572925568,0,
0.463364362716675,-0.886167883872986,0,0.463364362716675,-0.886167883872986,0,0.463364362716675,-0.886167883872986,0,0.346108198165894,-0.938194572925568,0,0.463364362716675,-0.886167883872986,0,0.463364362716675,-0.886167883872986,0,0.573612213134766,-0.819126963615417,0,0.57361227273941,-0.819126963615417,0,0.573612213134766,-0.819126963615417,0,0.463364362716675,-0.886167883872986,0,0.573612213134766,-0.819126963615417,0,0.57361227273941,-0.819126963615417,0,0.62545919418335,-0.780256867408752,0,0.62545919418335,-0.780256867408752,0,0.62545919418335,-0.780256867408752,0,0.57361227273941,-0.819126963615417,0,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355,0,0,1,0,0,1,0,0,1,0,0,0.999999940395355
			} 
			NormalsW: *240 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVChannel_1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *260 {
				a: 0,0,1,0,0,0,0.0500000007450581,0,0.100000001490116,0,0.150000005960464,0,0.200000002980232,0,0.25,0,0.300000011920929,0,0.350000023841858,0,0.400000035762787,0,0.450000047683716,0,0.500000059604645,0,0.550000071525574,0,0.600000083446503,0,0.650000095367432,0,0.700000107288361,0,0.75000011920929,0,0.800000131130219,0,0.850000143051147,0,0.900000154972076,0,0.950000166893005,0,1.00000011920929,0,0,0,0.0500000007450581,0,0.100000001490116,0,0.150000005960464,0,0.200000002980232,0,0.25,0,0.300000011920929,0,0.350000023841858,0,0.400000035762787,0,0.450000047683716,0,0.500000059604645,0,0.550000071525574,0,0.600000083446503,0,0.650000095367432,0,0.700000107288361,0,0.75000011920929,0,0.800000131130219,0,0.850000143051147,0,0.900000154972076,0,0.950000166893005,0,1.00000011920929,0,0,1,0.0500000007450581,1,0.100000001490116,1,0.150000005960464,1,0.200000002980232,1,0.25,1,0.300000011920929,1,0.350000023841858,1,0.400000035762787,1,0.450000047683716,1,0.500000059604645,1,0.550000071525574,1,0.600000083446503,1,0.650000095367432,1,0.700000107288361,1,0.75000011920929,1,0.800000131130219,1,0.850000143051147,1,0.900000154972076,1,0.950000166893005,1,1.00000011920929,1,0,0,1,0,0,0,0.0500000007450581,0,0.100000001490116,0,0.150000005960464,0,0.200000002980232,0,0.25,0,0.300000011920929,0,0.350000023841858,0,0.400000035762787,0,0.450000047683716,0,0.500000059604645,0,0.550000071525574,0,0.600000083446503,0,0.650000095367432,0,0.700000107288361,0,0.75000011920929,0,0.800000131130219,0,0.850000143051147,0,0.900000154972076,0,0.950000166893005,0,1.00000011920929,0,0,0,0.0500000007450581,0,0.100000001490116,0,0.150000005960464,0,0.200000002980232,0,0.25,0,0.300000011920929,0,0.350000023841858,0,0.400000035762787,0,0.450000047683716,0,0.500000059604645,0,0.550000071525574,0,0.600000083446503,0,0.650000095367432,0,0.700000107288361,0,0.75000011920929,0,0.800000131130219,0,0.850000143051147,0,0.900000154972076,0,0.950000166893005,0,1.00000011920929,0,0,1,0.0500000007450581,1,0.100000001490116,1,0.150000005960464,
1,0.200000002980232,1,0.25,1,0.300000011920929,1,0.350000023841858,1,0.400000035762787,1,0.450000047683716,1,0.500000059604645,1,0.550000071525574,1,0.600000083446503,1,0.650000095367432,1,0.700000107288361,1,0.75000011920929,1,0.800000131130219,1,0.850000143051147,1,0.900000154972076,1,0.950000166893005,1,1.00000011920929,1
			} 
			UVIndex: *240 {
				a: 44,23,45,24,45,23,45,24,46,25,46,24,46,25,47,26,47,25,47,26,48,27,48,26,48,27,49,28,49,27,49,28,50,29,50,28,50,29,51,30,51,29,51,30,52,31,52,30,52,31,53,32,53,31,53,32,54,33,54,32,54,33,55,34,55,33,55,34,56,35,56,34,56,35,57,36,57,35,57,36,58,37,58,36,58,37,59,38,59,37,59,38,60,39,60,38,60,39,61,40,61,39,61,40,62,41,62,40,62,41,63,42,63,41,63,42,64,43,64,42,109,88,110,89,110,88,110,89,111,90,111,89,111,90,112,91,112,90,112,91,113,92,113,91,113,92,114,93,114,92,114,93,115,94,115,93,115,94,116,95,116,94,116,95,117,96,117,95,117,96,118,97,118,96,118,97,119,98,119,97,119,98,120,99,120,98,120,99,121,100,121,99,121,100,122,101,122,100,122,101,123,102,123,101,123,102,124,103,124,102,124,103,125,104,125,103,125,104,126,105,126,104,126,105,127,106,127,105,127,106,128,107,128,106,128,107,129,108,129,107
			} 
		}
		LayerElementVisibility: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByEdge"
			ReferenceInformationType: "Direct"
			Visibility: *162 {
				a: 1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementVisibility"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 1009002992, "Model::Mesh_06_zx", "Mesh" {
		Version: 232
		Properties70:  {
			P: "PreRotation", "Vector3D", "Vector", "",-90,-0,0
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A",0,-6.29001149653252e-032,-0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",2.53999996185303,2.53999996185303,2.53999996185303
			P: "UDP3DSMAX", "KString", "", "U", "MapChannel:1 = UVChannel_1&cr;&lf;"
			P: "MaxHandle", "int", "Integer", "UH",2
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 583736720, "Material::zhenji_jiao1", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "ShadingModel", "KString", "", "", "phong"
			P: "EmissiveFactor", "Number", "", "A",0
			P: "AmbientColor", "Color", "", "A",0.588235318660736,0.588235318660736,0.588235318660736
			P: "DiffuseColor", "Color", "", "A",0.588235318660736,0.588235318660736,0.588235318660736
			P: "TransparentColor", "Color", "", "A",1,1,1
			P: "SpecularColor", "Color", "", "A",0.899999976158142,0.899999976158142,0.899999976158142
			P: "SpecularFactor", "Number", "", "A",0
			P: "ShininessExponent", "Number", "", "A",1.99999988079071
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0.588235318660736,0.588235318660736,0.588235318660736
			P: "Diffuse", "Vector3D", "Vector", "",0.588235318660736,0.588235318660736,0.588235318660736
			P: "Specular", "Vector3D", "Vector", "",0,0,0
			P: "Shininess", "double", "Number", "",1.99999988079071
			P: "Opacity", "double", "Number", "",1
			P: "Reflectivity", "double", "Number", "",0
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh_06_zx, Model::RootNode
	C: "OO",1009002992,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",1056854912,1056865616
	
	;Geometry::, Model::Mesh_06_zx
	C: "OO",1009345008,1009002992
	
	;Material::zhenji_jiao1, Model::Mesh_06_zx
	C: "OO",583736720,1009002992
}
