﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveCameraColliderLayer : EditorAction
    {
        public ActionRemoveCameraColliderLayer(int layerID)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as CameraColliderLayer;
            mDisplayVertexRadius = layer.displayVertexRadius;
            mLayerID = layerID;
            mLayerWidth = layer.GetTotalWidth();
            mLayerHeight = layer.GetTotalHeight();

            List<IMapObjectData> collisions = new List<IMapObjectData>();
            layer.GetAllObjects(collisions);
            for (int i = 0; i < collisions.Count; ++i)
            {
                var action = new ActionRemoveCameraCollider(layerID, collisions[i].GetEntityID());
                mRemoveActions.Add(action);
            }
        }

        public override bool Do()
        {
            bool suc = true;
            for (int i = mRemoveActions.Count - 1; i >= 0; --i)
            {
                suc &= mRemoveActions[i].Do();
            }

            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            int index = Map.currentMap.GetMapLayerIndex(layer);
            Map.currentMap.RemoveMapLayerByIndex(index);
            return suc;
        }

        public override bool Undo()
        {
            var map = Map.currentMap as EditorMap;
            var layer = map.CreateCameraColliderLayer(MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER, mLayerWidth, mLayerHeight, mLayerID, mDisplayVertexRadius);
            Map.currentMap.AddMapLayer(layer);

            bool suc = true;
            for (int i = 0; i < mRemoveActions.Count; ++i)
            {
                suc &= mRemoveActions[i].Undo();
            }

            Selection.activeObject = layer.layerView.root;
            return suc;
        }

        List<ActionRemoveCameraCollider> mRemoveActions = new List<ActionRemoveCameraCollider>();
        int mLayerID;
        float mLayerWidth;
        float mLayerHeight;
        float mDisplayVertexRadius;

    }
}

#endif