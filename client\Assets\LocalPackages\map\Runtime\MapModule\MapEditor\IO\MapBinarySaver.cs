﻿ 



 
 


#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using UnityEngine;

namespace TFW.Map
{
    public static class MapBinarySaver
    {
        public static void Save(string projectFolderPath)
        {
            mIDExport = new IDExporter();

            var dataPath = projectFolderPath + "/" + "mapData.config";
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.EditorVersion.majorVersion);
            writer.Write(VersionSetting.EditorVersion.minorVersion);

            bool compress = false;
            writer.Write(compress);
            MemoryStream compressStream = null;

            long originalFileSizePos = 0;
            long compressFileSizePos = 0;
            BinaryWriter headerWriter = writer;
            if (compress)
            {
                compressStream = new MemoryStream();
                BinaryWriter compressWriter = new BinaryWriter(compressStream);
                int compressFileSize = 0;
                int originalFileSize = 0;
                compressFileSizePos = writer.BaseStream.Position;
                writer.Write(compressFileSize);
                originalFileSizePos = writer.BaseStream.Position;
                writer.Write(originalFileSize);

                //change to compress writer
                writer = compressWriter;
            }

            SaveSetting(writer);
            SaveNavMeshObstacles(writer);
            SaveSpriteTemplates(writer);
            SaveModelTemplates(writer);
            SavePropertySets(writer);
            SaveModelProperties(writer);
            SavePrefabManagers(writer);
            SaveCamera(writer);
            SaveMap(writer);
            SaveMapObstacles(writer);

            //important!!!!!!!!!!!!!!!!!!!!!!
            //save path mapper at the end of file
            SavePathMapper(writer);

            if (compress)
            {
                MemoryStream compressedStreamData = new MemoryStream();
                DeflateStream gzipStream = new DeflateStream(compressedStreamData, CompressionMode.Compress);

                var bytes = compressStream.ToArray();
                //记录原来的文件大小
                Utils.WriteAndJump(headerWriter, originalFileSizePos, bytes.Length);
                //压缩
                gzipStream.Write(bytes, 0, bytes.Length);
                gzipStream.Close();

                var comparessedBytes = compressedStreamData.ToArray();
                FileStream file = new FileStream(dataPath, FileMode.Create);
                //记录压缩后文件的大小
                Utils.WriteAndJump(headerWriter, compressFileSizePos, comparessedBytes.Length);

                var headerData = stream.ToArray();
                file.Write(headerData, 0, headerData.Length);
                file.Write(comparessedBytes, 0, comparessedBytes.Length);
                file.Close();
            }
            else
            {
                var data = stream.ToArray();
                File.WriteAllBytes(dataPath, data);
            }

            writer.Close();
        }

        static void SaveSetting(BinaryWriter writer)
        {
            var data = Map.currentMap.data as EditorMapData;
            Utils.WriteVector2(writer, Vector2.zero);
            Utils.WriteString(writer, SLGMakerEditor.instance.exportFolder);

            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);

            var map = Map.currentMap as EditorMap;
            writer.Write(map.saveRiverMaterials);
            Utils.WriteString(writer, map.riverMaterialsFolder);

            SaveLoadRangeData(writer, (map.data as EditorMapData).loadRangeData);

            writer.Write(map.data.farClipOffset);
            writer.Write(map.data.maxCameraColliderHeight);
        }

        static void SaveLoadRangeData(BinaryWriter writer, LoadRangeData data)
        {
            writer.Write(data.cameraFov);
            writer.Write(data.cameraHeight);
            writer.Write(data.cameraRotationX);
            Utils.WriteVector2(writer, data.resolution);
            Utils.WriteVector2(writer, data.loadRangeScale);
        }

        static List<ModelTemplate> GetUsedModelTemplates()
        {
            var map = Map.currentMap;
            var templates = map.data.GetUsedModelTemplates();
            return templates;
        }

        static void SaveModelTemplates(BinaryWriter writer)
        {
            writer.Write(0);
        }

        static void SaveModelProperties(BinaryWriter writer)
        {
            writer.Write(0);
        }

        static void SavePrefabManagers(BinaryWriter writer)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            //changed
            SavePrefabManager(writer, editorMapData.modelLayerPrefabManager);
        }

        static void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        static void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            writer.Write(group.groupID);
            Utils.WriteString(writer, group.name);
            Utils.WriteColor32(writer, group.color);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, mPathMapper.Map(group.GetPrefabPath(i)));
                //save subgroup prefab paths
                var subgroupPrefabs = group.GetSubGroupPrefabPaths(i);
                int subGroupPrefabCount = subgroupPrefabs == null ? 0 : subgroupPrefabs.Length;
                writer.Write(subGroupPrefabCount);
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    Utils.WriteString(writer, mPathMapper.Map(subgroupPrefabs[k]));
                }
            }
        }

        static void SaveSpriteTemplates(BinaryWriter writer)
        {
            writer.Write(0);
        }

        static void SaveMap(BinaryWriter writer)
        {
            var map = Map.currentMap as EditorMap;
            writer.Write((int)map.mapType);

            writer.Write(map.mapWidth);
            writer.Write(map.mapHeight);
            writer.Write(map.data.borderHeight);
            writer.Write(map.data.isCircleMap);
            writer.Write(map.data.backExtendedSize);
            writer.Write(map.generateNPCSpawnPointsInBorderLine);
            writer.Write(map.removeSameHoles);
            writer.Write(map.navMeshRegionVisible);
            writer.Write((int)map.navMeshMode);
            writer.Write((int)map.globalObstacleMode);
            writer.Write(map.groundTileSize);
            writer.Write(map.frontTileSize);

            SaveBackground(writer, map);

            Utils.WriteBounds(writer, map.data.mapDataGenerationRange);

            //global grid
            var globalGrid = map.grid;
            writer.Write(globalGrid.isActive);
            writer.Write(globalGrid.totalWidth);
            writer.Write(globalGrid.totalHeight);
            writer.Write(globalGrid.gridWidth);
            writer.Write(globalGrid.gridHeight);
            Utils.WriteColor32(writer, globalGrid.color);

            var mapData = Map.currentMap.data as EditorMapData;
            Utils.WriteVector3(writer, mapData.viewCenter);
            var viewport = map.viewport;
            Utils.WriteVector2(writer, viewport.size);

            SaveMapLayers(writer);
            SaveMapLODConfig(writer);
        }

        static void SaveMapLODConfig(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var lodManager = map.data.lodManager;
            int n = lodManager.lodCount;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                writer.Write(lod.cameraHeight);
                //version 8
                writer.Write(lod.viewWidth);
                writer.Write(lod.viewHeight);
                int unitCount = 0;
                if (lod.displayingUnits != null)
                {
                    unitCount = lod.displayingUnits.Count;
                }
                writer.Write(unitCount);
                for (int k = 0; k < unitCount; ++k)
                {
                    Utils.WriteString(writer, lod.displayingUnits[k].unit);
                    Utils.WriteString(writer, lod.displayingUnits[k].relation);
                }
                Utils.WriteString(writer, lod.name);
                writer.Write(lod.showTerritory);
            }
        }

        static List<MapLayerBase> GetValidLayers()
        {
            List<MapLayerBase> layers = new List<MapLayerBase>();
            var map = Map.currentMap;
            int layerCount = map.GetMapLayerCount();
            for (int i = 0; i < layerCount; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (layer.GetType() == typeof(EditorComplexGridModelLayer) ||
                    layer.GetType() == typeof(DecorationBorderLayer) ||
                    layer.GetType() == typeof(MapCollisionLayer) ||
                    layer.GetType() == typeof(CameraColliderLayer) ||
                    layer.GetType() == typeof(RuinLayer) ||
                    layer.GetType() == typeof(NPCRegionLayer) ||
                    layer.GetType() == typeof(EditorGridModelLayer) ||
                    layer.GetType() == typeof(BlendTerrainLayer) ||
                    layer.GetType() == typeof(VaryingTileSizeTerrainLayer) ||
                    layer.GetType() == typeof(LODLayer) ||
                    layer.GetType() == typeof(EntitySpawnLayer) ||
                    layer.GetType() == typeof(NavMeshLayer) ||
                    layer.GetType() == typeof(PolygonRiverLayer) ||
                    layer.GetType() == typeof(CircleBorderLayer) ||
                    layer.GetType() == typeof(SplitFogLayer) ||
                    layer.GetType() == typeof(EditorTerritoryLayer) ||
                    layer.GetType() == typeof(RailwayLayer) ||
                    layer.GetType() == typeof(RegionLayer) ||
                    layer.GetType() == typeof(BuildingGridLayer) ||
                    layer.GetType() == typeof(RegionColorLayer) ||
                    MapPlugin.IsPluginLayer(layer.name))
                {
                    continue;
                }
                layers.Add(layer);
            }
            return layers;
        }

        static void SaveMapLayers(BinaryWriter writer)
        {
            var map = Map.currentMap;
            List<MapLayerBase> validMapLayers = GetValidLayers();
            writer.Write(validMapLayers.Count);
            for (int i = 0; i < validMapLayers.Count; ++i)
            {
                var layer = validMapLayers[i];
                var layerType = GetMapLayerType(layer);
                writer.Write(layerType);
                mIDExport.Export(writer, layer.id);
                Utils.WriteString(writer, layer.name);
                Utils.WriteVector3(writer, layer.layerOffset);
                writer.Write(layer.active);
            }
        }

        static void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                }
            }
        }

        static void SaveRegionLayer(BinaryWriter writer, RegionLayer layer)
        {
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            writer.Write((int)layer.displayType);
            writer.Write(layer.displayVertexRadius);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveRegionData(writer, objData as RegionData);
            }
        }

        static void SaveModelLayer(BinaryWriter writer, ModelLayer layer)
        {
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveModelData(writer, objData as ModelData);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
        }

        static void SaveMapObjectBaseData(BinaryWriter writer, MapObjectData data)
        {
            bool isDefaultRotation = (data.GetRotation() == Quaternion.identity);
            bool isDefaultScale = (data.GetScale() == Vector3.one);
            int flag = data.GetFlag();

            mIDExport.Export(writer, data.id);
            writer.Write(flag);
            Utils.WriteVector3(writer, data.GetPosition());
            writer.Write(isDefaultRotation);
            if (isDefaultRotation == false)
            {
                Utils.WriteQuaternion(writer, data.GetRotation());
            }
            writer.Write(isDefaultScale);
            if (isDefaultScale == false)
            {
                Utils.WriteVector3(writer, data.GetScale());
            }
        }

        static void SaveModelData(BinaryWriter writer, ModelData data)
        {
            SaveMapObjectBaseData(writer, data);
            mIDExport.Export(writer, data.GetModelTemplateID());
        }

        static void SaveRegionData(BinaryWriter writer, RegionData data)
        {
            mIDExport.Export(writer, data.id);

            var navMeshObstacleVertices = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            int n = navMeshObstacleVertices.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var pos = navMeshObstacleVertices[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            var radius = data.displayRadius;
            var extandable = data.IsExtendable();
            writer.Write(radius);
            writer.Write(extandable);

            Utils.WriteVector3Array(writer, data.meshVertices);
            Utils.WriteIntArray(writer, data.meshIndices);
            Utils.WriteColor(writer, data.color);
        }

        static void SaveSpriteTileLayer(BinaryWriter writer, SpriteTileLayer layer)
        {
            int nCols = layer.horizontalTileCount;
            int nRows = layer.verticalTileCount;

            writer.Write(nRows);
            writer.Write(nCols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            writer.Write((int)layer.layerData.gridType);

            var layerData = layer.layerData;
            int spriteTemplateID = 0;

            for (int i = 0; i < nRows; ++i)
            {
                for (int j = 0; j < nCols; ++j)
                {
                    var tile = layerData.GetTile(j, i) as SpriteTileData;
                    if (tile != null && tile.spriteTemplate != null)
                    {
                        spriteTemplateID = tile.spriteTemplate.id;
                    }
                    else
                    {
                        spriteTemplateID = 0;
                    }
                    mIDExport.Export(writer, spriteTemplateID);
                }
            }
        }

        static int GetMapLayerType(MapLayerBase layer)
        {
            if (layer.GetType() == typeof(EditorGridModelLayer))
            {
                return MapLayerType.kGridModelLayer;
            }
            else if (layer.GetType() == typeof(ModelLayer))
            {
                return MapLayerType.kModelLayer;
            }
            else if (layer.GetType() == typeof(SpriteTileLayer))
            {
                return MapLayerType.kSpriteTileLayer;
            }
            else if (layer.GetType() == typeof(NavMeshLayer))
            {
                return MapLayerType.kNavMeshLayer;
            }
            else if (layer.GetType() == typeof(NPCRegionLayer))
            {
                return MapLayerType.kNPCRegionLayer;
            }
            else if (layer.GetType() == typeof(BlendTerrainLayer))
            {
                return MapLayerType.kBlendTerrainLayer;
            }
            else if (layer.GetType() == typeof(EntitySpawnLayer))
            {
                return MapLayerType.kEntitySpawnLayer;
            }
            else if (layer.GetType() == typeof(RailwayLayer))
            {
                return MapLayerType.kRailwayLayer;
            }
            else if (layer.GetType() == typeof(LODLayer))
            {
                return MapLayerType.kLODLayer;
            }
            else if (layer.GetType() == typeof(CircleBorderLayer))
            {
                return MapLayerType.kCircleBorderLayer;
            }
            else if (layer.GetType() == typeof(MapCollisionLayer))
            {
                return MapLayerType.kCollisionLayer;
            }
            else if (layer.GetType() == typeof(RegionLayer))
            {
                return MapLayerType.kRegionLayer;
            }
            else if (layer.GetType() == typeof(RuinLayer))
            {
                return MapLayerType.kRuinLayer;
            }
            else if (layer.GetType() == typeof(CameraColliderLayer))
            {
                return MapLayerType.kCameraColliderLayer;
            }
            else if (layer.GetType() == typeof(PolygonRiverLayer))
            {
                return MapLayerType.kRiverLayer;
            }
            else if (layer.GetType() == typeof(EditorComplexGridModelLayer))
            {
                return MapLayerType.kComplexGridModelLayer;
            }
            else if (layer.GetType() == typeof(SplitFogLayer))
            {
                return MapLayerType.kSplitFogLayer;
            }
            else if (layer.GetType() == typeof(EditorTerritoryLayer))
            {
                return MapLayerType.kEditorTerritoryLayer;
            }
            else if (layer.GetType() == typeof(RegionColorLayer))
            {
                return MapLayerType.kRegionColorLayer;
            }
            else if (layer.GetType() == typeof(BuildingGridLayer))
            {
                return MapLayerType.kBuildingGridLayer;
            }
            else if (layer.GetType() == typeof(DecorationBorderLayer))
            {
                return MapLayerType.kDecorationBorderLayer;
            }
            else if (layer.GetType() == typeof(VaryingTileSizeTerrainLayer))
            {
                return MapLayerType.kVaryingTileSizeTerrainLayer;
            }
            else
            {
                Debug.Assert(false, "unknown layer type");
            }
            return -1;
        }

        static void SaveCamera(BinaryWriter writer)
        {
            var camera = Map.currentMap.camera;
            //Utils.WriteVector3(writer, camera.transform.position);
            Utils.WriteVector3(writer, Vector3.zero);
            //Utils.WriteQuaternion(writer, camera.transform.rotation);
            Utils.WriteQuaternion(writer, Quaternion.identity);
            writer.Write(false);
            writer.Write((float)0);
            writer.Write((float)0);
            //writer.Write(camera.orthographic);
            //writer.Write(camera.orthographicSize);
            //writer.Write(camera.fieldOfView);
        }

        static void SaveProperty(BinaryWriter writer, PropertyBase prop)
        {
            Utils.WriteString(writer, prop.name);
            writer.Write((int)prop.type);
            switch (prop.type)
            {
                case PropertyType.kPropertyBool:
                    {
                        var p = prop as PropertyData<bool>;
                        writer.Write(p.value);
                        break;
                    }
                case PropertyType.kPropertyFloat:
                    {
                        var p = prop as PropertyData<float>;
                        writer.Write(p.value);
                        break;
                    }
                case PropertyType.kPropertyInt:
                    {
                        var p = prop as PropertyData<int>;
                        writer.Write(p.value);
                        break;
                    }
                case PropertyType.kPropertyIntArray:
                    {
                        var p = prop as PropertyData<int[]>;
                        Utils.WriteIntArray(writer, p.value);
                        break;
                    }
                case PropertyType.kPropertyVector3:
                    {
                        var p = prop as PropertyData<Vector3>;
                        Utils.WriteVector3(writer, p.value);
                        break;
                    }
                case PropertyType.kPropertyVector2:
                    {
                        var p = prop as PropertyData<Vector2>;
                        Utils.WriteVector2(writer, p.value);
                        break;
                    }
                case PropertyType.kPropertyVector4:
                    {
                        var p = prop as PropertyData<Vector4>;
                        Utils.WriteVector4(writer, p.value);
                        break;
                    }
                case PropertyType.kPropertyColor:
                    {
                        var p = prop as PropertyData<Color>;
                        Utils.WriteColor32(writer, p.value);
                        break;
                    }
                case PropertyType.kPropertyString:
                    {
                        var p = prop as PropertyData<string>;
                        Utils.WriteString(writer, p.value);
                        break;
                    }
                default:
                    Debug.Assert(false, "unknown property type");
                    break;
            }
        }

        static void SavePropertySet(BinaryWriter writer, PropertySet temp)
        {
            mIDExport.Export(writer, temp.id);
            Utils.WriteString(writer, temp.name);
            var properties = temp.properties;
            int n = properties.GetPropertyCount();
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                var prop = properties.GetProperty(i);
                SaveProperty(writer, prop);
            }
        }

        static void SavePropertySets(BinaryWriter writer)
        {
            var mapData = Map.currentMap.data as EditorMapData;
            var propertySets = mapData.propertySets;

            int n = propertySets.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var ps = propertySets.GetPropertySet(i);
                SavePropertySet(writer, ps);
            }
        }

        static void SaveNavMeshObstacles(BinaryWriter writer)
        {
            int n = 0;
            writer.Write(n);
        }

        //保存地图上prefab的障碍物数据
        static void SaveMapObstacles(BinaryWriter writer)
        {
            SaveLocalObstacles(writer);
            SaveGlobalObstacles(writer);
        }

        static void SaveLocalObstacles(BinaryWriter writer)
        {
            var obstacleManager = Map.currentMap.data.localObstacleManager;
            var obstacles = obstacleManager.prefabTileDatas;
            var tiles = obstacleManager.tiles;
            int n = 0;
            if (tiles != null)
            {
                n = tiles.Length;
            }

            writer.Write(obstacleManager.regionWidth);
            writer.Write(obstacleManager.regionHeight);
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                mIDExport.Export(writer, tiles[i]);
            }

            int obstacleCount = 0;
            if (obstacles != null)
            {
                obstacleCount = obstacles.Count;
            }
            writer.Write(obstacleCount);
            if (obstacles != null)
            {
                foreach (var p in obstacles)
                {
                    var modelTemplateID = p.Key;
                    var data = p.Value;
                    var vertices = data.vertices;
                    var triangles = data.triangles;

                    //save id
                    mIDExport.Export(writer, modelTemplateID);
                    //save vertices
                    int count = vertices.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        Utils.WriteVector3(writer, vertices[i]);
                    }
                    //save indices
                    count = triangles.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        writer.Write(triangles[i]);
                    }
                }
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
        }

        static void SaveGlobalObstacles(BinaryWriter writer)
        {
            var obstacleManager = Map.currentMap.data.globalObstacleManager;
            var obj = obstacleManager.GetObstacleView();
            Vector3[] vertices = new Vector3[0];
            int[] indices = new int[0];
            if (obj != null)
            {
                var filter = obj.GetComponent<MeshFilter>();
                var sharedMesh = filter.sharedMesh;
                vertices = sharedMesh.vertices;
                indices = sharedMesh.triangles;
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
            int n = vertices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, vertices[i]);
            }
            n = indices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(indices[i]);
            }

            //grid obstacle
            int gridObstacleCount = 0;
            if (obstacleManager.obstacleGrids != null)
            {
                gridObstacleCount = obstacleManager.obstacleGrids.Length;
            }
            writer.Write(gridObstacleCount);
            if (obstacleManager.obstacleGrids != null)
            {
                writer.Write(obstacleManager.obstacleGrids);
            }
            writer.Write(obstacleManager.gridSize);
        }

        static void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }
        }

        static void SaveBackground(BinaryWriter writer, Map map)
        {
            var background = map.view.background;
            Utils.WriteVector3(writer, background.transform.position);
            Utils.WriteVector3(writer, background.transform.localScale);
            Utils.WriteString(writer, (map.data as EditorMapData).backgroundTexturePath);
        }

        static IDExporter mIDExport = null;
        static long mPathMapperPosition;
        static PathMapper mPathMapper = new PathMapper();
    }
}
#endif