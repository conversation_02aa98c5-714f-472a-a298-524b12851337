﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;


namespace TFW.Map
{
    //将多个使用同一个材质的prefab合并成一个,只支持使用一个material的prefab
    public static class PrefabCombiner
    {
        public static void Combine(string outputPath, List<GameObject> prefabs)
        {
            bool valid = CheckValidPrefabs(prefabs);
            if (!valid)
            {
                EditorUtility.DisplayDialog("Error", "Can't combine prefabs", "OK");
                return;
            }

            List<CombineInstance> instances = new List<CombineInstance>();
            var mesh = new Mesh();
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var meshFilters = prefabs[i].GetComponentsInChildren<MeshFilter>();
                for (int k = 0; k < meshFilters.Length; ++k)
                {
                    var inst = new CombineInstance();
                    inst.mesh = meshFilters[k].sharedMesh;
                    inst.transform = meshFilters[k].transform.localToWorldMatrix;
                    instances.Add(inst);
                }
            }
            mesh.CombineMeshes(instances.ToArray(), true);

            var obj = new GameObject("combined");
            var filter = obj.AddComponent<MeshFilter>();
            filter.sharedMesh = mesh;
            var renderer = obj.AddComponent<MeshRenderer>();
            var renderers = prefabs[0].GetComponentsInChildren<MeshRenderer>();
            renderer.sharedMaterial = renderers[0].sharedMaterial;

            EditorUtils.SavePrefab(outputPath, "combined", obj);

            Object.DestroyImmediate(obj);
        }

        static bool CheckValidPrefabs(List<GameObject> prefabs)
        {
            Material oneMtl = null;
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var renderers = prefabs[i].GetComponentsInChildren<MeshRenderer>();
                if (renderers != null)
                {
                    for (int k = 0; k < renderers.Length; ++k)
                    {
                        //只合并使用一个material的prefab
                        if (renderers[k].sharedMaterials.Length > 1)
                        {
                            return false;
                        }
                        if (oneMtl == null)
                        {
                            oneMtl = renderers[k].sharedMaterial;
                        }
                        //检测material是否相同
                        if (!Object.ReferenceEquals(oneMtl, renderers[k].sharedMaterial))
                        {
                            return false;
                        }
                    }
                }

                var filters = prefabs[i].GetComponentsInChildren<MeshFilter>();
                if (filters != null)
                {
                    for (int k = 0; k < filters.Length; ++k)
                    {
                        //只合并使用一个submesh的prefab
                        if (filters[k].sharedMesh.subMeshCount > 1)
                        {
                            return false;
                        }
                        if (!filters[k].sharedMesh.isReadable)
                        {
                            EditorUtility.DisplayDialog("Error", "mesh is not readable!", "OK");
                            return false;
                        }
                    }
                }
            }
            return true;
        }
    }
}

#endif