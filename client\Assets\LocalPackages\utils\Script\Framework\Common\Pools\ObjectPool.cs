﻿







using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace Common.Pool
{
    /// <summary>
    /// 对象池Unity-Technologies-UI
    /// (不会自动检测数据，需手动释放清理数据)
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ObjectPool<T> where T : new()
    {
        #region 属性和字段

        /// <summary>
        /// 堆栈信息数据
        /// </summary>
        private Stack<T> _stack = new Stack<T>();

        /// <summary>
        /// 获取对象时回调
        /// </summary>
        private UnityAction<T> _actionOnGet;

        /// <summary>
        /// 回收对象时回调
        /// </summary>
        private UnityAction<T> _actionOnRelease;

        /// <summary>
        /// 当前所有的对象数量（包含活动和非活动状态）
        /// </summary>
        public int CountAll { get; private set; }

        /// <summary>
        /// 活动状态对象数量
        /// </summary>
        public int CountActive
        {
            get { return CountAll - CountInactive; }
        }

        /// <summary>
        /// 非活动状态数量
        /// </summary>
        public int CountInactive
        {
            get { return _stack.Count; }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="actionOnGet"></param>
        /// <param name="actionOnRelease"></param>
        public ObjectPool(UnityAction<T> actionOnGet, UnityAction<T> actionOnRelease)
        {
            _actionOnGet = actionOnGet;
            _actionOnRelease = actionOnRelease;
        }

        #endregion

        #region 对象池逻辑处理

        /// <summary>
        /// 数据信息清理
        /// </summary>
        public void Clear()
        {
            if(_stack != null)
            {
                _stack.Clear();
                _stack = null;
            }
            _actionOnGet = null;
            _actionOnRelease = null;
        }

        /// <summary>
        /// 获取一个数据信息
        /// </summary>
        /// <returns></returns>
        public T Get()
        {
            T element;
            if (_stack.Count == 0)
            {
                element = new T();
                CountAll++;
            }
            else
            {
                element = _stack.Pop();
            }

            if (_actionOnGet != null)
                _actionOnGet(element);
            return element;
        }

        /// <summary>
        /// 回收一个数据信息
        /// </summary>
        /// <param name="element"></param>
        public void Release(T element)
        {
            if (_stack.Count > 0 && ReferenceEquals(_stack.Peek(), element))
            {
                Debug.LogError("Internal error. Trying to destroy object that is already released to pool.");
            }

            _actionOnRelease?.Invoke(element);
            _stack.Push(element);
        }

        #endregion
    }

    

    


}