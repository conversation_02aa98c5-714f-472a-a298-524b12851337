﻿using Common;
using DeepUI;
using Game.Config;
using Logic;
using Render;
using System;
using TFW.Localization;
using TFW.Map;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    public class MessageCoordinateItemUI : MonoBehaviour
    {
        [SerializeField]
        Text unitText;

        [SerializeField]
        TFW.UI.TFWText coordText;

        [SerializeField]
        GameObject coordTextLine;

        [SerializeField]
        GameObject rallyImgObj;

        [SerializeField]
        GameObject gatherImgObj;

        [SerializeField]
        GameObject cityImgObj;

        [SerializeField]
        GameObject mailImgObj;

        [SerializeField]
        GameObject heroIconObj;

        [SerializeField]
        GameObject heroQuality;

        [SerializeField]
        GameObject heroIcon;

        [SerializeField]
        GameObject ThroneTowerIcon;

        [SerializeField]
        GameObject ThroneIcon;

        [SerializeField]
        TFW.EventTriggerListener _jumpButton;

        CustomContent customContent;

        public void SetData(CustomContent customContent)
        {
            this.customContent = customContent;
            this.Refresh();
        }

        void Refresh()
        {
            if (this.isActiveAndEnabled && this.customContent != null)
            {
                var coordShareContent = customContent.shareCoordinateContentValue;
                if (coordShareContent == null)
                {
                    D.Warning?.Log("coordShareContent is null");
                    return;
                }
                var coordShareData = coordShareContent.shareCoordinateData;
                if (coordShareData == null)
                {
                    D.Warning?.Log("coordShareData is null");
                    return;
                }

                if (coordText)
                    coordText.gameObject.SetActive(true);
                if (coordTextLine)
                    coordTextLine.gameObject.SetActive(true);
                //commonBG?.SetActive(true);
                heroIconObj?.SetActive(false);

                //跟进分享的类型，设置分享的图片显示
                SetCoordTypeImage(coordShareData,
                    cityImgObj,
                    rallyImgObj,
                    gatherImgObj,
                    mailImgObj,
                    ThroneTowerIcon,
                    ThroneIcon);

                var coord = string.Format("X:{0} Y:{1}", coordShareData.coordX, coordShareData.coordY);
                var coordinateStr = coord;// string.Format(LocalizationMgr.GetUIString("Share_chatdes_2"), coord);
                coordText.text = coordinateStr;
                //Debug.LogError(coordinateStr + "--------------");
                //var color = coordText.color;
                //if (coordText.HaveLink())
                //{
                //    //zjw fix 20201112 已存在就不要加偏移值了。导致偏移出问题
                //    //又改回原本的颜色了
                //    UITools.ResetLink(coordText, coordText.color, false);//,.5f
                //}
                //else
                //{
                //    UITools.CreateLink(coordText, coordText.color, false, .5f);                 
                //}


                if (!string.IsNullOrEmpty(coordShareData.descKey))
                {
                    if (coordShareData.level > 0)
                    {
                        var str = string.Format("Lv.{0} {1}", coordShareData.level, LocalizationMgr.Get(coordShareData.descKey));
                        unitText.text = LocalizationMgr.Format("Share_chatdes_1", "", str);
                    }
                    else
                    {
                        unitText.text = LocalizationMgr.Get(coordShareData.descKey);
                    }
                }
                else
                {
                    unitText.text = coordShareData.unitName;
                }

                var worldPos = new Vector3(coordShareData.coordX, 0, coordShareData.coordY);
                this._jumpButton?.RemoveListener("onClick");

                if (coordShareData.RallyID > 0)
                {
                    var rallyID = coordShareData.RallyID;

                    this._jumpButton?.AddListener("onClick", (go, args) =>
                    {
                        if (coordShareData.shareServerId != -1
                            && coordShareData.shareServerId != LPlayer.I.CrossServerId)
                        {
                            FloatTips.I.FloatMsg(LocalizationMgr.Get("ChatTips_1"));
                            return;
                        }

                        var rallyList = Game.Data.GameData.I.RallyData.GetAllRally();
                        foreach (var item in rallyList)
                        {
                            if (item.rallyTroopID == rallyID)
                            {
                                PopupManager.I.ClosePopup<UIChat>();
                                DeepUI.PopupManager.I.ShowPanel<UIRallyTroop>(new UIRallyTroopData() { tab = 0, rally = item });
                                return;
                            }
                        }

                        FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("ERRCODE_rallytroopiderr"));
                    });
                }
                else
                {
                    if (DeadBeat.I.CheckIsDebt())
                        return;
                    this._jumpButton?.AddListener("onClick", (go, args) =>
                    {
                        if (coordShareData.shareServerId != -1
                            && coordShareData.shareServerId != LPlayer.I.CrossServerId)
                        {
                            FloatTips.I.FloatMsg(LocalizationMgr.Get("ChatTips_1"));
                            return;
                        }


                        if (coordShareData.shareType == (int)ShareDataTypeEnum.MoveCityInvite)
                        {
                            var targetPos = new Vector3(coordShareData.coordX, 0, coordShareData.coordY);
                            var pos = LPlayer.I.GetMainCityPosition();
                            var dis = Math.Ceiling(Vector3.Distance(pos, targetPos));
                            var range = MetaConfig.Far_From_Main_Castle;
                            D.Debug?.Log("range={0},dis={1},targetPos={2}", dis, range, targetPos);
                            if (dis < range)
                            {
                                //Nearby_msg    您已经在此目标点附近。  You are already nearby the location.  您已經在此目標點附近。
                                //不用迁城
                                FloatTips.I.FloatMsg(LocalizationMgr.Get("Nearby_msg"));
                                return;
                            }

                            PopupManager.I.ClosePopup<UI.Alliance.UIAllianceMain>();
                            PopupManager.I.ClosePopup<UIChat>();
                            //EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                            //JumpToPos(worldPos.x, worldPos.z);

                            //LAllianceMgr.I.ReqUnionRandomCoord();
                            CityTeleportManager.I.TryStartTeleport(worldPos);
                            EventMgr.FireEvent(TEventType.UIMain_Update_PlayerCityViewPort);
                            //EventMgr.FireEvent(TEventType.UIMain_Update_AllianceMoveCity_Active, true);
                        }
                        else
                        {
                            PopupManager.I.ClosePopup<UI.Alliance.UIAllianceMain>();
                            PopupManager.I.ClosePopup<UIChat>();
                            //UIMain.I?.TrySwitch(MainMenuConst.WORLD);//切到大世界，然后跳转到相应坐标位置
                            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                            JumpToPos(worldPos.x, worldPos.z);
                            EventMgr.FireEvent(TEventType.UIMain_Update_PlayerCityViewPort);
                        }

                        //UIMain.I?.OnPlayerCityEnterLeaveViewport();
                    });
                }
            }
        }

        /// <summary>
        /// 设置分享坐标类型消息体的图片类型
        /// </summary>
        private void SetCoordTypeImage(ShareCoordinateData shareCoordinateData,
            GameObject cityImgObj,
            GameObject rallyImgObj,
            GameObject gatherImgObj,
            GameObject mailObj,
            GameObject towerObj,
            GameObject throne)
        {
            mailObj?.SetActive(false);

            var iconType = (ShareDataTypeEnum)shareCoordinateData.shareType;
            switch (iconType)
            {
                case ShareDataTypeEnum.Gather:
                    rallyImgObj?.SetActive(false);
                    cityImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(true);
                    towerObj?.SetActive(false);
                    throne?.SetActive(false);
                    break;
                case ShareDataTypeEnum.PlayerCastle:
                case ShareDataTypeEnum.Pos:
                case ShareDataTypeEnum.FavoritesPos:
                case ShareDataTypeEnum.MoveCityInvite:
                case ShareDataTypeEnum.DragonWar:
                    rallyImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(false);
                    cityImgObj?.SetActive(true);
                    throne?.SetActive(false);
                    towerObj?.SetActive(false);
                    break;
                case ShareDataTypeEnum.Rally:
                    cityImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(false);
                    rallyImgObj?.SetActive(true);
                    throne?.SetActive(false);
                    towerObj?.SetActive(false);
                    break;
                case ShareDataTypeEnum.JoinRally:
                    cityImgObj?.SetActive(shareCoordinateData.targetType == 1);
                    gatherImgObj?.SetActive(false);
                    rallyImgObj?.SetActive(shareCoordinateData.targetType == 0);
                    towerObj?.SetActive(shareCoordinateData.targetType == 2);
                    throne?.SetActive(shareCoordinateData.targetType == 3);
                    break;
                case ShareDataTypeEnum.ThroneTower:
                    cityImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(false);
                    rallyImgObj?.SetActive(false);
                    throne?.SetActive(false);
                    towerObj?.SetActive(true);
                    break;
                case ShareDataTypeEnum.ThroneWar:
                    cityImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(false);
                    rallyImgObj?.SetActive(false);
                    throne?.SetActive(true);
                    towerObj?.SetActive(false);
                    break;
                default:
                    cityImgObj?.SetActive(false);
                    gatherImgObj?.SetActive(false);
                    rallyImgObj?.SetActive(true);
                    throne?.SetActive(false);
                    towerObj?.SetActive(false);
                    break;
            }

        }

        private void JumpToPos(float posX, float posZ)
        {
            if (!DeadBeat.I.IsDeadBeat)
            {
                MapCameraMgr.MoveCameraToTarget(posX, posZ, "city_world", 0.5f, 0.6f, () => { }, true, true);
            }
            ////如果当前lod不在2-4范围内，则会zoom到lod3
            //if (MapMgr.CurrentLod < 2 || MapMgr.CurrentLod > 4)
            //{
            //    MapCameraMgr.MoveCameraToTarget(posX, posZ, "city_world", 0.5f, 0.8f, () => { }, true, true);
            //    // MapCameraMgr.MoveCameraToTarget(posX, posZ, 3, 0.3f, 0.3f, null);
            //}
            //else
            //{
            //    MapCameraMgr.MoveCameraToTarget(posX, posZ, "city_world", 0.5f, 0.8f, () => { }, true, true);
            //    //MapCameraMgr.MoveCameraToTarget(posX, posZ, 0.3f, null);
            //}
        }

        private void OnEnable()
        {
            this.Refresh();
        }

        [ContextMenu(nameof(AutoFindReference))]
        void AutoFindReference()
        {
            //this.commonBG = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/BG");
            this.unitText = Public.UIHelper.GetComponent<Text>(this.gameObject, "Share/Coordinate/Unit");
            this.coordText = Public.UIHelper.GetComponent<TFW.UI.TFWText>(this.gameObject, "Share/Coordinate/Coord");
            this.coordTextLine = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Coord/Line");
            this.rallyImgObj = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_1");
            this.gatherImgObj = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_2");
            this.cityImgObj = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_3");
            this.mailImgObj = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_4");
            this.heroIconObj = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_Hero");
            this.heroQuality = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_Hero/Quality");
            this.heroIcon = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_Hero/Icon");
            this.ThroneTowerIcon = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_ThroneTowerWar");
            this.ThroneIcon = Public.UIHelper.GetChild(this.gameObject, "Share/Coordinate/Icon/Icon_Throne");
        }
    }
}