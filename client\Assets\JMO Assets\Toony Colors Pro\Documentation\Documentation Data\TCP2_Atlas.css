.Error { background-position:  -0px -0px; height: 160px; width: 160px; }
.icon_cog { background-position:  -162px -0px; height: 16px; width: 16px; }
.icon_ext1 { background-position:  -162px -18px; height: 16px; width: 16px; }
.icon_ext2 { background-position:  -162px -36px; height: 16px; width: 16px; }
.icon_help { background-position:  -162px -54px; height: 16px; width: 16px; }
.icon_info { background-position:  -162px -72px; height: 16px; width: 16px; }
.icon_warning { background-position:  -162px -90px; height: 16px; width: 16px; }
.Inspector { background-position:  -0px -162px; height: 742px; width: 354px; }
.PBS_RampSmooth_M01A01 { background-position:  -180px -0px; height: 160px; width: 160px; }
.PBS_RampSmooth_M01A10 { background-position:  -342px -0px; height: 160px; width: 160px; }
.PBS_RampSmooth_M10A01 { background-position:  -504px -0px; height: 160px; width: 160px; }
.PBS_StylizedFresnel_Off { background-position:  -666px -0px; height: 160px; width: 160px; }
.PBS_StylizedFresnel_On { background-position:  -828px -0px; height: 160px; width: 160px; }
.PBS_StylizedSpecular_Hybrid { background-position:  -990px -0px; height: 160px; width: 160px; }
.PBS_StylizedSpecular_Off { background-position:  -1152px -0px; height: 160px; width: 160px; }
.PBS_StylizedSpecular_On { background-position:  -1314px -0px; height: 160px; width: 160px; }
.RampGenerator { background-position:  -356px -162px; height: 202px; width: 368px; }
.RampGenerator_Edit { background-position:  -356px -366px; height: 228px; width: 368px; }
.SG10_Alpha { background-position:  -1476px -0px; height: 160px; width: 160px; }
.SG11_Alpha { background-position:  -1638px -0px; height: 160px; width: 160px; }
.SG12_DirAmb { background-position:  -1800px -0px; height: 160px; width: 160px; }
.SG13_Subsurface { background-position:  -1962px -0px; height: 160px; width: 160px; }
.SG14_DiffTint { background-position:  -0px -906px; height: 160px; width: 160px; }
.SG14_DiffTint_1 { background-position:  -162px -906px; height: 160px; width: 160px; }
.SG15_RampControl_Global { background-position:  -0px -1068px; height: 160px; width: 160px; }
.SG15_RampControl_MainOther { background-position:  -162px -1068px; height: 160px; width: 160px; }
.SG15_RampControl_MainType { background-position:  -0px -1230px; height: 160px; width: 160px; }
.SG16_WrappedLighting_Custom { background-position:  -162px -1230px; height: 160px; width: 160px; }
.SG16_WrappedLighting_Half { background-position:  -0px -1392px; height: 160px; width: 160px; }
.SG16_WrappedLighting_Off { background-position:  -162px -1392px; height: 160px; width: 160px; }
.SG17_TexBlending_Height { background-position:  -0px -1554px; height: 300px; width: 300px; }
.SG17_TexBlending_Linear { background-position:  -0px -1856px; height: 300px; width: 300px; }
.SG17_TexBlending_Map { background-position:  -356px -596px; height: 300px; width: 300px; }
.SG18_Triplanar { background-position:  -356px -898px; height: 300px; width: 300px; }
.SG18_Triplanar_Ceiling { background-position:  -356px -1200px; height: 300px; width: 300px; }
.SG19_StencilOutline { background-position:  -726px -162px; height: 160px; width: 160px; }
.SG19_StencilOutline_Diff { background-position:  -888px -162px; height: 160px; width: 160px; }
.SG19_StencilOutline_Same { background-position:  -1050px -162px; height: 160px; width: 160px; }
.SG1_TexThr_1 { background-position:  -1212px -162px; height: 160px; width: 160px; }
.SG1_TexThr_2 { background-position:  -1374px -162px; height: 160px; width: 160px; }
.SG1_TexThr_2tex { background-position:  -1536px -162px; height: 160px; width: 160px; }
.SG1_TexThr_3 { background-position:  -1698px -162px; height: 160px; width: 160px; }
.SG1_TexThr_3tex { background-position:  -1860px -162px; height: 160px; width: 160px; }
.SG2_DetailTex { background-position:  -2022px -162px; height: 160px; width: 160px; }
.SG3_VColors { background-position:  -726px -366px; height: 160px; width: 160px; }
.SG4_VColBlend { background-position:  -888px -366px; height: 160px; width: 160px; }
.SG5_SelfIllumin { background-position:  -1050px -366px; height: 160px; width: 160px; }
.SG5_SelfIllumin2 { background-position:  -1212px -366px; height: 160px; width: 160px; }
.SG6b_Spec { background-position:  -1374px -366px; height: 160px; width: 160px; }
.SG6_IndShadows { background-position:  -1536px -366px; height: 160px; width: 160px; }
.SG6_IndShadows_2 { background-position:  -1698px -366px; height: 160px; width: 160px; }
.SG7_Refl { background-position:  -1860px -366px; height: 160px; width: 160px; }
.SG7_Refl_2 { background-position:  -2022px -366px; height: 160px; width: 160px; }
.SG8_Amb { background-position:  -356px -1502px; height: 160px; width: 160px; }
.SG9_Sketch { background-position:  -356px -1664px; height: 160px; width: 160px; }
.SG9_Sketch_2 { background-position:  -356px -1826px; height: 160px; width: 160px; }
.SG9_Sketch_3 { background-position:  -356px -1988px; height: 160px; width: 160px; }
.SG9_Sketch_4 { background-position:  -658px -596px; height: 160px; width: 160px; }
.SG9_Sketch_5 { background-position:  -820px -596px; height: 160px; width: 160px; }
.SNormalsCube_1 { background-position:  -658px -758px; height: 220px; width: 220px; }
.SNormalsCube_2 { background-position:  -658px -980px; height: 220px; width: 220px; }
.SphDiffuse { background-position:  -982px -596px; height: 160px; width: 160px; }
.SphTCP2 { background-position:  -1144px -596px; height: 160px; width: 160px; }
.SphTCP2_BumpRim { background-position:  -1306px -596px; height: 160px; width: 160px; }
.SphTCP2_DetailHalftone { background-position:  -1468px -596px; height: 160px; width: 160px; }
.SphTCP2_HardLine { background-position:  -1630px -596px; height: 160px; width: 160px; }
.SphTCP2_Ramp { background-position:  -1792px -596px; height: 160px; width: 160px; }
.SphTCP2_RimRefl { background-position:  -1954px -596px; height: 160px; width: 160px; }
.SphTCP2_SketchOutline { background-position:  -658px -1202px; height: 160px; width: 160px; }
.SphTCP2_TexturedRamp { background-position:  -658px -1364px; height: 160px; width: 160px; }
.US1_Color_A { background-position:  -658px -1526px; height: 160px; width: 160px; }
.US1_Color_B { background-position:  -658px -1688px; height: 160px; width: 160px; }
.US1_Color_C { background-position:  -658px -1850px; height: 160px; width: 160px; }
.US1_Color_D { background-position:  -658px -2012px; height: 160px; width: 160px; }
.US2_Ramp_p3p5 { background-position:  -880px -758px; height: 160px; width: 160px; }
.US2_Ramp_p5p1 { background-position:  -1042px -758px; height: 160px; width: 160px; }
.US2_Ramp_p75p01 { background-position:  -1204px -758px; height: 160px; width: 160px; }
.US2_Ramp_rgb1 { background-position:  -1366px -758px; height: 160px; width: 160px; }
.US2_Ramp_rgb2 { background-position:  -1528px -758px; height: 160px; width: 160px; }
.US2_Ramp_rgb3 { background-position:  -1690px -758px; height: 160px; width: 160px; }
.US2_Ramp_t1 { background-position:  -1852px -758px; height: 160px; width: 160px; }
.US2_Ramp_t2 { background-position:  -2014px -758px; height: 160px; width: 160px; }
.US2_Ramp_t3 { background-position:  -880px -920px; height: 160px; width: 160px; }
.US3_Bump { background-position:  -880px -1082px; height: 160px; width: 160px; }
.US3_Bump_2 { background-position:  -880px -1244px; height: 160px; width: 160px; }
.US4_Spec_s1 { background-position:  -880px -1406px; height: 160px; width: 160px; }
.US4_Spec_s1s1 { background-position:  -880px -1568px; height: 160px; width: 160px; }
.US5b_MatCap { background-position:  -880px -1730px; height: 160px; width: 160px; }
.US5b_MatCapTex { background-position:  -880px -1892px; height: 160px; width: 160px; }
.US5b_MatCapTex_2 { background-position:  -1042px -920px; height: 160px; width: 160px; }
.US5b_MatCap_2 { background-position:  -1204px -920px; height: 160px; width: 160px; }
.US5_Refl { background-position:  -1366px -920px; height: 160px; width: 160px; }
.US5_Refl_2 { background-position:  -1528px -920px; height: 160px; width: 160px; }
.US6_Rim { background-position:  -1690px -920px; height: 160px; width: 160px; }
.US6_Rim_2 { background-position:  -1852px -920px; height: 160px; width: 160px; }
.US6_Rim_3 { background-position:  -2014px -920px; height: 160px; width: 160px; }
.US6_Rim_4 { background-position:  -1042px -1082px; height: 160px; width: 160px; }
.US6_Rim_5 { background-position:  -1042px -1244px; height: 160px; width: 160px; }
.US7_Outline_1 { background-position:  -1042px -1406px; height: 160px; width: 160px; }
.US7_Outline_2 { background-position:  -1042px -1568px; height: 160px; width: 160px; }
.US7_Outline_3 { background-position:  -1042px -1730px; height: 160px; width: 160px; }
.US9_Wrapped_Diffuse_1 { background-position:  -1042px -1892px; height: 160px; width: 160px; }
.US9_Wrapped_Diffuse_2 { background-position:  -1204px -1082px; height: 160px; width: 160px; }
