﻿ 



 
 



using UnityEngine;
using System.Text;

namespace TFW.Map
{
    //相机双指缩放+双指移动+双指旋转
    public class CameraZoomAndPan : ZoomActionBase
    {
        enum State
        {
            Idle,
            Moving,
            Rotating,
        }

        public CameraZoomAndPan(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
            on = true;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mTargetPosition = currentCameraPos;
            int touchCount = MapTouchManager.touchCount;
            if (on && touchCount == 2)
            {
                IMapTouch touch0 = MapTouchManager.GetTouch(0);
                IMapTouch touch1 = MapTouchManager.GetTouch(1);

                var touch0Pos = touch0.position;
                var touch1Pos = touch1.position;
                float touchDelta = Vector2.Distance(touch0Pos, touch1Pos);

                if (mScrollRateModifyCallback != null)
                {
                    if (mPassHeight)
                    {
                        mPassHeight = false;
                        //切换相机的缩放算法
                        InitZooming(touchDelta);
                        enabled = true;
                        //Debug.Log("InitZooming");
                        if (currentCameraPos.y > mZoomAlgorithmSwitchHeight)
                        {
                            mUseOldZoomMethod = false;
                        }
                        else
                        {
                            mUseOldZoomMethod = true;
                        }
                    }
                }

                if (mTouch0PressTime == 0)
                {
                    mTouch0PressTime = Time.time;
                    mTouch1PressTime = Time.time;
                }

                Vector2 dir0 = touch0Pos - touch0.lastPosition;
                Vector2 dir1 = touch1Pos - touch1.lastPosition;
                float movedDistance0 = dir0.magnitude;
                float movedDistance1 = dir1.magnitude;
                dir0.Normalize();
                dir1.Normalize();
                float angle = Utils.GetAngleBetween(dir0, dir1);
                bool isDir0Zero = dir0 == Vector2.zero;
                bool isDir1Zero = dir1 == Vector2.zero;

                if ((touch0.state == MapTouchState.Touch && isDir0Zero) ||
                    (touch1.state == MapTouchState.Touch && isDir1Zero))
                {
                    return;
                }

                if ((isDir0Zero || isDir1Zero) && isDir0Zero != isDir1Zero)
                {
                    //有一根手指没有动时
                    if (mState == State.Moving)
                    {
                        //如果正在pan的状态,则继续pan
                        enabled = true;
                        mTargetPosition = OnMove(touch0, touch1);
                        isTwoTouchZooming = false;
                        ResetDragDir();
                    }
                    else if (mState != State.Rotating)
                    {
                        //如果正在zoom的状态,则继续zoom
                        if (!isTwoTouchZooming)
                        {
                            InitZooming(touchDelta);
                        }
                        OnZoom(touch0, touch1);
                    }
                }
                else
                {
                    //Debug.LogError($"Check: {mState}");
                    if (angle <= 45.0f)
                    {
                        //当两根手指移动方向角度小于某个值时,判断为pan
                        enabled = true;
                        mTargetPosition = OnMove(touch0, touch1);
                        isTwoTouchZooming = false;
                        ResetDragDir();
                        mState = State.Moving;
                    }
                    else
                    {
                        bool rotate = false;
                        float maxRotateAngle = MapCameraMgr.cameraSetting.horizontalRotationRange;
                        if (mCanRotate && maxRotateAngle > 0 && currentCameraPos.y < MapCameraMgr.cameraSetting.maxHorizontalRotationHeight)
                        {
                            if (mState == State.Rotating)
                            {
                                Debug.Assert(mDragDir != 0);
                                rotate = true;
                            }

                            Vector2 leftDir, rightDir;
                            if (touch0Pos.x < touch1Pos.x)
                            {
                                leftDir = dir0;
                                rightDir = dir1;
                            }
                            else
                            {
                                leftDir = dir1;
                                rightDir = dir0;
                            }

                            if (leftDir.y < 0 && rightDir.y > 0)
                            {
                                float verticalAngle0 = Vector2.Angle(leftDir, Vector2.down);
                                float verticalAngle1 = Vector2.Angle(rightDir, Vector2.up);
                                if (verticalAngle0 <= 30 && verticalAngle1 <= 30)
                                {
                                    mDragDir = 1.0f;
                                    rotate = true;
                                    //Debug.LogError("Set Drag Dir -1");
                                }
                            }
                            else if (rightDir.y < 0 && leftDir.y > 0)
                            {
                                float verticalAngle0 = Vector2.Angle(leftDir, Vector2.up);
                                float verticalAngle1 = Vector2.Angle(rightDir, Vector2.down);
                                if (verticalAngle0 <= 30 && verticalAngle1 <= 30)
                                {
                                    mDragDir = -1.0f;
                                    rotate = true;
                                    //Debug.LogError("Set Drag Dir 1");
                                }
                            }
                        }
                        
                        if (rotate)
                        {
                            mState = State.Rotating;
                            isTwoTouchZooming = false;
                            enabled = true;
                            float offset = Utils.CalculateWorldDistance(Mathf.Max(movedDistance0, movedDistance1));
                            mTargetPosition = OnRotate(currentCameraPos, mDragDir, offset);
                        }
                        else
                        {
                            //当两根手指移动方向角度大于某个值时,判断为zoom
                            if (!isTwoTouchZooming)
                            {
                                InitZooming(touchDelta);
                            }

                            OnZoom(touch0, touch1);
                        }
                    }
                }
            }
            else if (isTwoTouchZooming)
            {
                isTwoTouchZooming = false;
                isFinished = true;
            }
            else
            {
                mState = State.Idle;
            }
        }

        public void OnCameraHeightPassZoomAlgorithmChangeHeight()
        {
            mPassHeight = true;
        }

        void InitZooming(float touchDelta)
        {
            //初始化zoom的一些值
            isTwoTouchZooming = true;
            mLastTwoTouchDist = touchDelta;
            mFirstTwoTouchDist = touchDelta;
            mLastScrollRate = 1;
            ResetDragDir();
            mState = State.Idle;

            Reset();
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            float minCameraHeight = MapCameraMgr.currentMinimumHeight;
            if (mTargetPosition.y > minCameraHeight)
            {
                //更新相机应该移动的高度
                MapCameraMgr.SetCameraHeightChangeTargetHeight(mTargetPosition.y);
            }
            return mTargetPosition;
        }

        //相机双指移动
        Vector3 OnMove(IMapTouch touch0, IMapTouch touch1)
        {
            var camera = Map.currentMap.camera.firstCamera;
            if (!MapCameraMgr.enableCameraXAngleChange) {
                //如果x angle可以改变,则不能修改view center,所以move无效
                var worldCenter0 = (Utils.FromScreenToWorldPosition(touch0.position, camera) + Utils.FromScreenToWorldPosition(touch1.position, camera)) * 0.5f;
                var worldCenter1 = (Utils.FromScreenToWorldPosition(touch0.lastPosition, camera) + Utils.FromScreenToWorldPosition(touch1.lastPosition, camera)) * 0.5f;
                var delta = worldCenter1 - worldCenter0;
                MapCameraMgr.UpdateRotateCenter();
                return camera.transform.position + delta;
            }
            return camera.transform.position;
        }

        //相机双指旋转
        Vector3 OnRotate(Vector3 currentCameraPos, float dragDir, float offset)
        {
            //拖动相机中
            var viewCenter = Map.currentMap.viewCenter;
            var cameraToViewCenter = currentCameraPos - viewCenter;
            float viewDistance = cameraToViewCenter.magnitude;
            var cameraRoot = MapCameraMgr.MapCameraRoot.transform;
            //根据拖动距离计算应该旋转多少度
            float rotationX = cameraRoot.rotation.eulerAngles.x;
            float rotationRadius = viewDistance * Mathf.Cos(rotationX * Mathf.Deg2Rad);
            float arcLength = offset * 3;

            float speed = 1.0f;
            if (mRotateSpeedModifierCallback != null)
            {
                speed = mRotateSpeedModifierCallback(currentCameraPos.y);
            }
            float angle = arcLength / rotationRadius * Mathf.Rad2Deg * dragDir * speed;

            float maxAngle = MapCameraMgr.cameraSetting.horizontalRotationRange;
            float currentAngle = MapCameraMgr.updatedCameraYRotation;
            //clamp angles
            float newAngle = currentAngle + angle;
            float deltaAngle = Mathf.Abs(angle);
            if (newAngle < -maxAngle)
            {
                deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle + maxAngle);
            }
            else if (newAngle > maxAngle)
            {
                deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle - maxAngle);
            }
            deltaAngle *= Mathf.Sign(angle);
            currentAngle += deltaAngle;

            Quaternion rotation = Quaternion.Euler(0, deltaAngle, 0);
            var cameraTargetPos = viewCenter + rotation * cameraToViewCenter.normalized * viewDistance;
            MapCameraMgr.updatedCameraYRotation = currentAngle;

            return cameraTargetPos;
        }

        //相机双指缩放
        void OnZoom(IMapTouch touch0, IMapTouch touch1)
        {
            float touchDelta = Vector2.Distance(touch0.position, touch1.position);
            float f = mLastTwoTouchDist - touchDelta;

            float minCameraHeight = MapCameraMgr.currentMinimumHeight;

            if (mHasHeightLimit)
            {
                if (mTargetPosition.y <= minCameraHeight)
                {
                    if (f < 0)
                    {
                        enabled = false;
                        return;
                    }
                }
            }

            if (((Mathf.Abs(f) > 1f) && ((touch0.state == MapTouchState.Touching) || (touch1.state == MapTouchState.Touching))) && (mLastTwoTouchDist > 0f && mFirstTwoTouchDist > 0))
            {
                Vector2 center = (touch0.position + touch1.position) * 0.5f;
                enabled = true;

                if (mUseOldZoomMethod)
                {
                    //使用精确的计算方式
                    mLastScrollRate = mFirstTwoTouchDist / mLastTwoTouchDist;
                    TouchZoomed(center.x, center.y, mLastScrollRate);
                }
                else
                {
                    //使用外部修改速率的计算方式
                    float scrollRateMultiplier = 1.0f;
                    if (mScrollRateModifyCallback != null)
                    {
                        scrollRateMultiplier = mScrollRateModifyCallback(mTargetPosition.y);
                    }
                    TouchZoomedAccumulate(center.x, center.y, f * scrollRateMultiplier);
                }
                
                MapCameraMgr.ResetAutoUpdateTargetHeight();

                mLastTwoTouchDist = touchDelta;
                mTargetPosition = GetCameraPos();

                mState = State.Idle;
            }
        }

        public override void OnCameraSettingChange(MapCameraSetting newSetting)
        {
            Reset();
        }

        public void SetScrollRateModifyCallback(System.Func<float, float> callback)
        {
            mScrollRateModifyCallback = callback;
        }

        void ResetDragDir()
        {
            mDragDir = 0;
            //Debug.LogError("Reset Drag Dir");
        }

        public override string GetDebugMessage()
        {
            var builder = new StringBuilder();
            builder.AppendLine($"on: {on}");
            builder.AppendLine($"enabled: {enabled}");
            return builder.ToString();
        }

        public bool on
        {
            set
            {
                mOn = value;
                if (!mOn)
                {
                    isFinished = true;
                }
            }
            get
            {
                return mOn;
            }
        }
        public bool hasHeightLimit { set { mHasHeightLimit = value; } get { return mHasHeightLimit; } }
        public float zoomAlgorithmSwitchHeight { get { return mZoomAlgorithmSwitchHeight; } set { mZoomAlgorithmSwitchHeight = value; } }
        public bool enableRotate { get { return mCanRotate; } set { mCanRotate = value; } }
        public System.Func<float, float> rotateSpeedModifierCallback { set { mRotateSpeedModifierCallback = value; } get { return mRotateSpeedModifierCallback; } }

        Vector3 mTargetPosition;

        float mFirstTwoTouchDist = 0f;

        bool mIsTwoTouchZooming = false;

        bool isTwoTouchZooming
        {
            get
            {
                return mIsTwoTouchZooming;
            }
            set
            {
                MapCameraMgr.IsTwoTouchZooming = value;
                mIsTwoTouchZooming = value;
            }
        }

        float mLastTwoTouchDist = 0f;
        float mLastScrollRate;
        State mState = State.Idle;
        float mDragDir = 1.0f;
        bool mHasHeightLimit = false;
        bool mOn = true;

        float mTouch0PressTime;
        float mTouch1PressTime;
        
        float mZoomAlgorithmSwitchHeight = 500;
        bool mUseOldZoomMethod = true;
        bool mPassHeight = false;
        System.Func<float, float> mScrollRateModifyCallback;

        //rotation parameter
        bool mCanRotate = false;
        System.Func<float, float> mRotateSpeedModifierCallback;
    }
}
