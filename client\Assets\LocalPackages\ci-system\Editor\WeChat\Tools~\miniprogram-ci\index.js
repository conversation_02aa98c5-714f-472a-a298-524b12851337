const ci = require('miniprogram-ci')
;(async () => {
  const project = new ci.Project({
    appid: process.argv[2],
    type: 'miniGame',
    projectPath: process.argv[3],
    privateKeyPath: process.argv[4],
    ignores: ['node_modules/**/*'],
  })
  const previewResult = await ci.preview({
    project,
    desc: 'hello', // 此备注将显示在“小程序助手”开发版列表中
    setting: {
      es6: true,
      es7: true,
    },
    qrcodeFormat: 'image',
    qrcodeOutputDest: process.argv[5],
    onProgressUpdate: console.log,
    // pagePath: 'pages/index/index', // 预览页面
    // searchQuery: 'a=1&b=2',  // 预览参数 [注意!]这里的`&`字符在命令行中应写成转义字符`\&`
  })
  console.log(previewResult)
})()