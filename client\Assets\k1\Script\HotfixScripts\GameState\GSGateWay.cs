﻿using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using k1;
using K3;
using Logic;
using MainScripts;
using Render;
using System;
using System.Net.Sockets;
using ThinkingAnalytics;
using UI;


namespace GameState
{
    public class GSGateWay : GSBase<HLNone>
    {
        public static event Action onConnect;

        public GSGateWay() : base(false)
        {
           
        } 

        public override string processName => "GateWaying";


        private long _beginTime;
        private const int TIMEOUT = 10 * 1000;
       
        private bool checkTimeOut=false;


        public override UniTask Prepare()
        {
            return base.Prepare();
        }

        public override UniTask Exit()
        {
            //BILogInfo.UpdateFTEStageParameter("GSGateway exit");
            //LoadingLog.AddLoadingBILog("GSGateway: exit");
            checkTimeOut = false;

            //K3GameEvent.I.TaLog(new LoginEvent() { EventKey = "GateWaying_exit" });

            return UniTask.CompletedTask;
        }

        public override UniTask Enter()
        {
            checkTimeOut = true;
            _beginTime = GameTime.LocalTime;

            Loading_Res.CurLoadingProcess = LogoLoadingProcessEnum.GSGateWay;

            ThinkingAnalyticsAPI.SetDynamicSuperProperties(new DynamicProp());

            LNetShowMgr.I.Init();

            //K3GameEvent.I.TaLog(new LoginEvent() { EventKey = "GateWaying" });

            return this.EnterAsync();
        }

        public override void Update()
        {
            if (checkTimeOut && _beginTime > 0 && (GameTime.LocalTime - _beginTime) > TIMEOUT)
            {
                _beginTime = GameTime.LocalTime;
                ReconnectManager.I.ShowNetReconnectMark(false);

                var dialog = PopupManager.I.FindPopup<UILoginTipsDialog>();
                if (dialog != null && dialog.IsShow)
                {
                    return;
                }

                LoginMgr.ShowLogfailWindow(LoginFailEnum.GSGatewayFail);

                K3GameEvent.I.TaLog(new LoginFailEvent() { EventKey = "login_gatewaytimeout" });
            }
        }
         
        async UniTask EnterAsync()
        {
            //LogoLoading.ins.UpdateLoadingStep(LogoLoadingStepEnum.CheckCurLanguage);
            Loading_Res.ShowLoading();
            
            //设置当前处于loading状态
            GameConfig.IsLoading = true;

            //设置重新加载标记
            GameConfig.IsEnterGameReq = false;

            //AudioManager.EnabelAudioListener(true);
            //播放loading是的背景音乐
            GameAudio.PlayAudio(AudioConst.LOADING_BG);


            //BILogInfo.UpdateFTEStageParameter("GSGateway Prepare");
            //LoadingLog.AddLoadingBILog("GSGateway: Prepare");
            //显示loadingtips信息
            //LogoLoading.LoadingTipsActive(true).Forget();
            GameServerConnection.GameServerConnection.Disconnect();

       
            var connectGatewaySuccess = false;
            using (new LaunchTimeLogger("连接网关"))
            {
                //启动网关登录
                connectGatewaySuccess = await DoConnectGate();
            }

            //初始化开始loading时各个模块
            LoadingModuleMgr.I.Init();

            await CallbackInvoker.InvokeCallbacksBeforeLogin();
            
            HandleGatewayLogin(connectGatewaySuccess);

            PreloadHelper.PreLoadUI().Forget(); 
        }
        
        /// <summary>
        /// 链接网关服务器
        /// </summary>
        private async UniTask<bool> DoConnectGate()
        {
          
            var gate_addr =    K1D2Config.I.GetGateAddr();
             

            var (isCancel, socketError) = await MessageMgr.ConnectAsync(gate_addr).SuppressCancellationThrow();

            D.Info?.Log($"连接服务器结果:{gate_addr}  isCancel:{isCancel} socketError:{socketError}");


            return !isCancel && socketError == SocketError.Success;
        }

        void HandleGatewayLogin(bool IsSuccess)
        {
            D.Warning?.Log($"loading_3+++++++++++++ {IsSuccess}");
            //BILogInfo.UpdateFTEStageParameter("HLGateway: GatewayLogin");
            //LoadingLog.AddLoadingBILog("HLGateway: GatewayLogin");
            ////BILog.UserFte(
            //    $"loading_3",
            //    $"2"
            //);
            //LoadingLog.TraceBILog("loading_4_gateway_handle");
            var isok = IsSuccess;

            if (isok)
            {
                var now_type = "sq_game";
                D.Debug?.Log("  sdk login now_type " + now_type);
                //BILogInfo.UpdateFTEStageParameter($"HLGateway: sdk login now_type {now_type}");
                //LoadingLog.AddLoadingBILog($"HLGateway: sdk login now_type {now_type}");

                ToAuth(); 
                
                
            }
            else
            {
                //BILogInfo.UpdateFTEStageParameter("HLGateway: GatewayLogin isokfalse");
                //LoadingLog.AddLoadingBILog("HLGateway: GatewayLogin isokfalse");
                ReconnectManager.I.ShowNetReconnectMark(false);
                //登录网关失败 继续登录网关
                LoginMgr.ShowLogfailWindow( 
                LoginFailEnum.HLGatewayFail
                );
                //FPS.Instance.CheckGameCaton("HLGateway=====82");
            }
        }

        void ToAuth()
        {
            GSSwitchMgr.AddStage(new GSAuth(new AuthReq
            {
                userType = LoginMgr.I.loginData.logintype,
                userName = LoginMgr.I.loginData.passport,
                password = LoginMgr.I.loginData.password,
                clientState = 1,
                timestamp = LoginMgr.I.loginData.timestamp,
                cInfo = LoginMgr.I.GenerateClientInfo(),
                signature = LoginMgr.I.loginData.sign
            }, true));

            
        }
    }
}