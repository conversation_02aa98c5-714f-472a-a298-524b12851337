﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map {
    public class PropertySet : BaseObject {
        public PropertySet(int id, Map map, string name, PropertyDatas properties) : base(id, map) {
            if (properties == null) {
                properties = new PropertyDatas(null);
            }
            mProperties = properties;
            mName = name;
        }

        public override void OnDestroy() {
        }

        public PropertySet Clone() {
            var newProperties = mProperties.Clone();
            PropertySet newPS = new PropertySet(map.nextCustomObjectID, map, name + "_clone", newProperties);
            return newPS;
        }

        public string name { get { return mName; } set { mName = value; } }
        public override int propertySetID { get { return id; } }
        public PropertyDatas properties { get { return mProperties; } }

        PropertyDatas mProperties;
        string mName;
    }
}

#endif