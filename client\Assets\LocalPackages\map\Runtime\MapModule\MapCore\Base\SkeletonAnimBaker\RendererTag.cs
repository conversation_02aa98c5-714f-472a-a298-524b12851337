﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public enum RendererShaderType
    {
        //骨骼动画
        SkinShader,
        //刚体动画
        RigidShader,
    }

    //标记renderer应该使用哪个shader
    class RendererTag : MonoBehaviour
    {
        //used in skeleton animation
        public RendererShaderType shaderType = RendererShaderType.SkinShader;
        //used in vertex animation
        public int meshIndex;
    }
}
