﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadRegionLayer(BinaryReader reader)
        {
            var pos = GetSectionDataStartPosition(MapDataSectionType.RegionLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;
            int version = reader.ReadInt32();

            //-------------------version 1 start------------------------------
            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            int layerID = reader.ReadInt32();
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();

            int regionCount = reader.ReadInt32();
            config.RuntimeRegionData[] regions = new config.RuntimeRegionData[regionCount];
            for (int i = 0; i < regionCount; ++i)
            {
                //asset path
                string assetPath = Utils.ReadString(reader);
                //mesh bounds
                var bounds = Utils.ReadRect(reader);
                int number = reader.ReadInt32();
                string borderLinePrefabPath = "";
                bool hasBorderLine = reader.ReadBoolean();
                if (hasBorderLine)
                {
                    borderLinePrefabPath = Utils.ReadString(reader);
                }
                regions[i] = new config.RuntimeRegionData(assetPath, bounds, number, borderLinePrefabPath);
            }

            bool hasBorder = reader.ReadBoolean();
            string borderPrefabPath = "";
            if (hasBorder)
            {
                borderPrefabPath = Utils.ReadString(reader);
            }
            //-----------------------version 1 end----------------------------------
            var layer = new config.RuntimeRegionLayerData(layerID, layerName, layerOffset, layerWidth, layerHeight, regions, borderPrefabPath);
            return layer;
        }
    }
}
