﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public class ActionRemoveMapCollisionLayer : EditorAction
    {
        public ActionRemoveMapCollisionLayer(int layerID)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as MapCollisionLayer;
            mDisplayType = layer.displayType;
            mDisplayVertexRadius = layer.displayVertexRadius;
            mLayerID = layerID;
            mLayerWidth = layer.GetTotalWidth();
            mLayerHeight = layer.GetTotalHeight();

            List<IMapObjectData> collisions = new List<IMapObjectData>();
            layer.GetAllObjects(collisions);
            for (int i = 0; i < collisions.Count; ++i)
            {
                var action = new ActionRemoveMapCollision(layerID, collisions[i].GetEntityID());
                mRemoveActions.Add(action);
            }
        }

        public override bool Do()
        {
            bool suc = true;
            for (int i = mRemoveActions.Count - 1; i >= 0; --i)
            {
                suc &= mRemoveActions[i].Do();
            }

            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            int index = Map.currentMap.GetMapLayerIndex(layer);
            Map.currentMap.RemoveMapLayerByIndex(index);
            return suc;
        }

        public override bool Undo()
        {
            var map = Map.currentMap as EditorMap;
            var layer = map.CreateCollisionLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION, mLayerWidth, mLayerHeight, mLayerID, mDisplayType, mDisplayVertexRadius);
            Map.currentMap.AddMapLayer(layer);

            bool suc = true;
            for (int i = 0; i < mRemoveActions.Count; ++i)
            {
                suc &= mRemoveActions[i].Undo();
            }

            Selection.activeObject = layer.layerView.root;
            return suc;
        }

        List<ActionRemoveMapCollision> mRemoveActions = new List<ActionRemoveMapCollision>();
        int mLayerID;
        float mLayerWidth;
        float mLayerHeight;
        PrefabOutlineType mDisplayType;
        float mDisplayVertexRadius;

    }
}

#endif