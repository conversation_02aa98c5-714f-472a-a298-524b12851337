﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class CameraColliderLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.CameraColliderLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(displayVertexRadius);

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveCameraColliderData(writer, objData as CameraColliderData);
            }
        }

        static void SaveCameraColliderData(BinaryWriter writer, CameraColliderData data)
        {
            //save bottom outline
            var bottomOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            int n = bottomOutline.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var pos = bottomOutline[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            //save top outline
            int topOutlineVertexCount = 0;
            if (data.topOutline != null)
            {
                topOutlineVertexCount = data.topOutline.outline.Count;
            }
            writer.Write(topOutlineVertexCount);
            for (int i = 0; i < topOutlineVertexCount; ++i)
            {
                var pos = data.topOutline.outline[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            var radius = data.displayRadius;
            writer.Write(radius);
            writer.Write(data.height);

            //save collider mesh
            int meshVertexCount = 0;
            if (data.vertices != null)
            {
                meshVertexCount = data.vertices.Length;
            }
            writer.Write(meshVertexCount);
            for (int i = 0; i < meshVertexCount; ++i)
            {
                Utils.WriteVector3(writer, data.vertices[i]);
            }
            int meshIndexCount = 0;
            if (data.indices != null)
            {
                meshIndexCount = data.indices.Length;
            }
            writer.Write(meshIndexCount);
            for (int i = 0; i < meshIndexCount; ++i)
            {
                writer.Write(data.indices[i]);
            }
        }
    }
}

#endif