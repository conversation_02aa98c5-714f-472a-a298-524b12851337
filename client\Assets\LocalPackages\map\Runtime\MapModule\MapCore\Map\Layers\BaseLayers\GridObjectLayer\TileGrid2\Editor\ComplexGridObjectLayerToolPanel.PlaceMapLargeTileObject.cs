﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class ComplexGridObjectLayerToolPanel : EditorWindow
    {
        public void PlaceMapLargeTile(Vector3 worldPos)
        {
            worldPos = mLogic.CalculatePosAlignedToGrid(worldPos);

            var modelTemplate = GetModelTemplate();
            if (modelTemplate == null)
            {
                return;
            }
            
            if (modelTemplate != null)
            {
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;
                var obj = layer.FindObjectOfPrefabPath(0, modelTemplate.GetLODPrefabPath(0));
                if (obj == null && layer.layerData.IsValidPosition(worldPos))
                {
                    float boundsWidth = modelTemplate.bounds.width;
                    float boundsHeight = modelTemplate.bounds.height;
                    float minX = worldPos.x - boundsWidth * 0.5f;
                    float minZ = worldPos.z - boundsHeight * 0.5f;
                    float maxX = worldPos.x + boundsWidth * 0.5f;
                    float maxZ = worldPos.z + boundsHeight * 0.5f;
                    int occupiedGridCount = layer.CalculateOccupiedGridCount(minX, minZ, maxX, maxZ);
                    var act = new ActionAddComplexGridModel(mLogic.layerID, map.nextCustomObjectID, modelTemplate.id, worldPos, Quaternion.identity, Vector3.one, occupiedGridCount, 0, modelTemplate.GetLODPrefabPath(0), "", false);
                    ActionManager.instance.PushAction(act);
                }
            }            
        }
    }
}

#endif