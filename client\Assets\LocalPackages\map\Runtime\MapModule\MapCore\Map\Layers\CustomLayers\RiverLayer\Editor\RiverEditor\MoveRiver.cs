﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MoveRiver : RiverEditorTool
    {
        public MoveRiver(RiverEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onMoveCollision = OnMoveCollision;
        }

        public override void OnDestroy()
        {
        }

        public override void OnDisabled()
        {
            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.type == EventType.MouseUp)
            {
                if (e.button == 0)
                {
                    mLeftButtonDown = false;

                    if (mCollisionMover != null)
                    {
                        mCollisionMover.Stop(pos);
                        mCollisionMover = null;
                    }
                }

                mPickWhenMovingCollision = true;
                mMover.Reset();
            }

            if (mLeftButtonDown)
            {
                if (mPickWhenMovingCollision == true)
                {
                    mPickWhenMovingCollision = false;
                    mEditor.Pick(pos);
                }
                MoveCollision(pos);
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void OnEnabled()
        {
        }

        void OnMoveCollision(int dataID)
        {
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
            mEditor.RepaintScene();
        }

        void MoveCollision(Vector3 pos)
        {
            if (mEditor.selectedObjectID != 0)
            {
                if (mCollisionMover == null)
                {
                    mCollisionMover = new PolygonRiverMover(mEditor.layer.id, mEditor.selectedObjectID, pos);
                }

                UnityEngine.Debug.Assert(mEditor.selectedVertexIndex >= 0);

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as PolygonRiverData;
                var offset = mMover.GetDelta();

                mEditor.layer.MoveObject(PrefabOutlineType.NavMeshObstacle, mEditor.selectedObjectID, offset);
                //mEditor.layer.MoveObject(PrefabOutlineType.ObjectPlacementObstacle, mEditor.selectedObjectID, offset);
#if false
                //temp code, 测试多边形之间的碰撞
                List<IMapObjectData> objects = new List<IMapObjectData>();
                mLogic.layer.GetAllObjects(objects);
                foreach(var obj in objects)
                {
                    var o = obj as PolygonRiverData;
                    mLogic.layer.SetDisplayColor(o.id, mDefaultColor);
                }

                System.Action<PolygonObjectData, PolygonObjectData> onCollide = (PolygonObjectData a, PolygonObjectData b) =>
                {
                    mLogic.layer.SetDisplayColor(a.id, Color.yellow);
                    mLogic.layer.SetDisplayColor(b.id, Color.yellow);
                };
                mLogic.layer.CheckCollision(onCollide);
#endif

                mEditor.RepaintScene();
            }
        }

        public override void DrawScene()
        {
            Handles.DrawPolyLine(mDisplayedVertices);
        }

        protected override void DrawGUIImpl()
        {
            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "河流顶点的显示大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }
        }

        public override RiverEditorToolType type { get { return RiverEditorToolType.Move; } }

        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
        bool mLeftButtonDown = false;
        bool mPickWhenMovingCollision = true;
        MouseMover mMover = new MouseMover();
        PolygonRiverMover mCollisionMover;
    }
}


#endif