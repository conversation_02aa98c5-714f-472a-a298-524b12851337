﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public static class HexagonalUtils {
        public static Vector3 flat_hex_corner_xy(Vector3 center, float size, int i) {
            float angle_deg = 60.0f * i;
            float angle_rad = Mathf.PI / 180.0f * angle_deg;
            return new Vector3(center.x + size * Mathf.Cos(angle_rad), center.y + size * Mathf.Sin(angle_rad), center.z);
        }

        public static Vector3 flat_hex_corner_xz(Vector3 center, float size, int i) {
            float angle_deg = 60.0f * i;
            float angle_rad = Mathf.PI / 180.0f * angle_deg;
            return new Vector3(center.x + size * Mathf.Cos(angle_rad), center.y, center.z + size * Mathf.Sin(angle_rad));
        }

        public static Vector3 pointy_hex_corner_xz(Vector3 center, float size, int i) {
            var angle_deg = 60 * i - 30;
            var angle_rad = angle_deg * Mathf.Deg2Rad;
            return new Vector3(center.x + size * Mathf.Cos(angle_rad), center.y, center.z + size * Mathf.Sin(angle_rad));
        }

        public static Vector3 pointy_hex_corner_xy(Vector3 center, float size, int i) {
            var angle_deg = 60 * i - 30;
            var angle_rad = angle_deg * Mathf.Deg2Rad;
            return new Vector3(center.x + size * Mathf.Cos(angle_rad), center.y + size * Mathf.Sin(angle_rad), center.z);
        }

        public static Vector2Int cube_to_oddr(int x, int y, int z) {
            var col = x + (z - (z & 1)) / 2;
            var row = z;
            return new Vector2Int(col, row);
        }

        public static Vector3 oddr_to_cube(int col, int row) {
            var x = col - (row - (row & 1)) / 2;
            var z = row;
            var y = -x - z;
            return new Vector3(x, y, z);
        }

        public static int cube_distance(int ax, int ay, int az, int bx, int by, int bz) {
            return (Mathf.Abs(ax - bx) + Mathf.Abs(ay - by) + Mathf.Abs(az - bz)) / 2;
        }

        public static Vector3 cube_round(float x, float y, float z) {
            var rx = Mathf.Round(x);
            var ry = Mathf.Round(y);
            var rz = Mathf.Round(z);

            var x_diff = Mathf.Abs(rx - x);
            var y_diff = Mathf.Abs(ry - y);
            var z_diff = Mathf.Abs(rz - z);

            if (x_diff > y_diff && x_diff > z_diff)
                rx = -ry - rz;
            else if (y_diff > z_diff)
                ry = -rx - rz;
            else
                rz = -rx - ry;

            return new Vector3(rx, ry, rz);
        }
    }
}
