﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map {
    public class NPCRegionLayerSettingWindow : EditorWindow {
        void OnEnable() {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight; 
        }

        void OnGUI() {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mHorizontalRegionCount = EditorGUILayout.IntField("X Region Tile Count", mHorizontalRegionCount);
            mVerticalRegionCount = EditorGUILayout.IntField("Z Region Tile Count", mVerticalRegionCount);
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Create")) {
                var map = Map.currentMap as EditorMap;
                bool valid = CheckParameter();
                if (valid) {
                    float tileWidth = mLayerWidth / (float)mHorizontalRegionCount;
                    float tileHeight = mLayerHeight / (float)mVerticalRegionCount;
                    map.CreateNPCRegionLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION, mLayerWidth, mLayerHeight, tileWidth, tileHeight);
                    
                    Close();
                }
                else {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        bool CheckParameter() {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mHorizontalRegionCount <= 0 || mVerticalRegionCount <= 0) {
                return false;
            }
            return true;
        }

        public float mLayerWidth;
        public float mLayerHeight;
        public int mHorizontalRegionCount = 40;
        public int mVerticalRegionCount = 40;
    }
}

#endif