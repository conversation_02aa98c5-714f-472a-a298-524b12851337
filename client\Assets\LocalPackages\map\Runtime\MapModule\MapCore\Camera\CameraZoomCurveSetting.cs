﻿using UnityEngine;

namespace TFW.Map
{
    [CreateAssetMenu(fileName = "camera_zoom_curve_setting", menuName = "Assets/Create Camera Zoom Curve Setting")]
    public class CameraZoomCurveSetting : ScriptableObject
    {
        //相机在maxCurveMovementHeight以下时曲线运动轨迹
        public AnimationCurve forwardDistanceCurveBelow;
        public AnimationCurve forwardDistanceCurveAbove;
        //相机超过这个距离就不再沿曲线运动
        public float curveMovementSplitHeight;
        public float curveMovementMaxHeight;
        public float xRotationWhenReachCurveMovementMaxHeight;
    }
}
