﻿ 



 
 

#if UNITY_EDITOR

using System;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class BakeSkeletonAnimationWindow : EditorWindow
    {
        public class Entry
        {
            public GameObject prefab;
            public AnimationBaker baker;
            public string name;
            public bool display;
            public BakePrefabSettingWindow gui;
            //切换动画时是否使用动画融合过渡
            public AnimationBlendType blendType = AnimationBlendType.Fast;
            public bool useAnimationBlending = false;
            //used in vertex animation baking
            public int sampleFrameInterval = 4;
            public bool useVertexAnimation 
            { 
                get 
                { 
                    return mUseVertexAnimation;
                }
                set
                {
                    if (mUseVertexAnimation != value)
                    {
                        AnimationBaker tempBaker = null;
                        if (value)
                        {
                            tempBaker = new VertexAnimationBaker();
                        }
                        else
                        {
                            tempBaker = new SkeletonAnimationBaker();
                        }
                        string error = tempBaker.Check(prefab);
                        if (string.IsNullOrEmpty(error))
                        {
                            mUseVertexAnimation = value;
                            baker = tempBaker;
                        }
                        else
                        {
                            Debug.LogError(error);
                        }
                    }
                }
            }

            bool mUseVertexAnimation = false;
        }

        public class SortEntry : IComparer<Entry>
        {
            public int Compare(Entry x, Entry y)
            {
                if (x.prefab == null)
                {
                    return 1;
                }
                if (y.prefab == null)
                {
                    return -1;
                }
                return x.prefab.name.CompareTo(y.prefab.name);
            }
        }

        class BakeInfo
        {
            public GameObject prefab;
            public AnimationBaker baker;
            public AnimationBlendType blendType = AnimationBlendType.Fast;
            public bool useAnimationBlending = false;
        }

        private void OnDisable()
        {
            Reset();
            mPrefabCount = 0;
        }

        void Reset()
        {
            mEntries = null;
        }

        void OnGUI()
        {
            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);
            EditorStyles.textField.wordWrap = true;
            EditorGUILayout.TextArea("推荐模型骨骼名称都使用bip开头(大小写无所谓),这样可以在烘培时删除所有的bip开头的骨骼(需要勾选Delete Game Objects Of Name Start With \"BIP\").\n否则烘培后只会删除skin修改器中使用的骨骼");
            EditorGUILayout.TextArea("注意,为了性能考虑,一般不需要动画状态切换时使用动画融合过渡.如果要使用,Shader里的动画采样会翻倍.\n优先考虑使用Fast类型.只有在Unity动画系统无法使用时或者Fast类型的融合效果不佳时才推荐使用Slerp类型.\nSlerp类型动动画过渡在Shader上有大量的计算和内存增大的开销,并且只支持骨骼Scale为1的情况!\n在游戏里可以使用AnimatorWrapper.SetGlobalAnimationBlendingState来开关动画融合,\n如果烘培时Animation Blend Type选择None,开启融合后会使用Fast模式.Use SRP Batcher开启后就不使用GPU Instancing了,但是需要修改shader代码");

            EditorGUILayout.BeginHorizontal();
            mUseBakedAnimation = EditorGUILayout.ToggleLeft(new GUIContent("Use Baked Animation In Game", "是否在游戏中使用烘培动画,把BakedAnim1.bytes文件删除也不会使用烘培动画"), mUseBakedAnimation);
            EditorGUILayout.EndHorizontal();
            mUseRGBA32Texture = EditorGUILayout.ToggleLeft(new GUIContent("Use RGBA32 Texture", "是否使用RGBA32格式的贴图,如果不勾选,就生成RGBAHalf格式的贴图,但是需要在TextureSkinLinearBlendingImpl.cginc和TextureSkinSlerpBlendingImpl.cginc文件中注释或打开#define USE_RGBA32_TEXTURE"), mUseRGBA32Texture);
            mDeleteBipBones = EditorGUILayout.ToggleLeft(new GUIContent("Delete Game Objects Of Name Start With \"BIP\"", "烘培后删除不使用的bip开头的game object"), mDeleteBipBones);
            mUseSRPBatcher = EditorGUILayout.ToggleLeft(new GUIContent("Use SRP Batcher", "是否使用SRP Batcher,如果开启,每个prefab的game object实例都使用单独的材质,然后利用SRP Batcher的轻量级状态切换渲染.否则同类的prefab共用一个材质,并用gpu instancing渲染"), mUseSRPBatcher);
            mSeparateCPUDrivenBoneTransformData = EditorGUILayout.ToggleLeft(new GUIContent("Use Separate CPU Driven Bone Transform Data", "是否把带CPUAnimationTag的game object的动画数据保存到一个单独文件中,这样方便多个prefab之间共享"), mSeparateCPUDrivenBoneTransformData);
            mDefaultSkinShader = EditorGUILayout.ObjectField(new GUIContent("Default Skin Shader", "默认的skin动画材质,如果烘培时没选择shader默认使用这个"), mDefaultSkinShader, typeof(Shader), false) as Shader;
            mDefaultRigidShader = EditorGUILayout.ObjectField(new GUIContent("Default Rigid Shader", "默认的刚体动画材质,如果烘培时没选择shader默认使用这个.所谓的刚体动画就是挂接在骨骼节点上的使用mesh renderer的game object,例如手上的剑等,这些mesh在shader里不用走skinning动画流程,参考示例shader中的RigidTransform"), mDefaultRigidShader, typeof(Shader), false) as Shader;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Prefab Count", mPrefabCount.ToString());
            if (GUILayout.Button("Change"))
            {
                var dlg = EditorUtils.CreateInputDialog("Set Prefab Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Prefab Count", "", "10"),
                };
                dlg.Show(items, OnClickChangeCount);
            }
            EditorGUILayout.EndHorizontal();

            if (mPrefabCount > 0)
            {
                mSampleFPS = EditorGUILayout.FloatField(new GUIContent("Sample FPS", "烘培动画每秒采样数"), mSampleFPS);
                if (mSampleFPS <= 0)
                {
                    mSampleFPS = 30.0f;
                }

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.TextField("Output Folder", mOutputFolder);
                if (GUILayout.Button("Choose Folder"))
                {
                    mOutputFolder = EditorUtility.SaveFolderPanel("Select output folder", "", "");
                    if (!string.IsNullOrEmpty(mOutputFolder))
                    {
                        mOutputFolder = Utils.ConvertToUnityAssetsPath(mOutputFolder);
                    }
                }
                if (GUILayout.Button("Select Asset"))
                {
                    if (!string.IsNullOrEmpty(mOutputFolder))
                    {
                        EditorUtils.SelectFolder(mOutputFolder);
                    }
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.BeginHorizontal();

            if (mPrefabCount > 0)
            {
                if (!string.IsNullOrEmpty(mOutputFolder))
                {
                    if (GUILayout.Button("Bake All Prefabs"))
                    {
                        List<BakeInfo> validPrefabs = new List<BakeInfo>();
                        for (int k = 0; k < mEntries.Length; ++k)
                        {
                            if (mEntries[k].prefab != null)
                            {
                                var info = new BakeInfo();
                                info.prefab = mEntries[k].prefab;
                                info.baker = mEntries[k].baker;
                                info.blendType = mEntries[k].blendType;
                                info.useAnimationBlending = mEntries[k].useAnimationBlending;
                                validPrefabs.Add(info);
                            }
                        }

                        if (validPrefabs.Count > 0)
                        {
                            for (int i = 0; i < validPrefabs.Count; ++i)
                            {
                                string prefabBakeFolder = $"{mOutputFolder}/{validPrefabs[i].prefab.name}_baked";
                                if (EditorUtility.DisplayCancelableProgressBar(
                                    "Baking...",
                                    $"Processing[{i + 1}/{validPrefabs.Count}]: {prefabBakeFolder}",
                                    1f * (i + 1) / validPrefabs.Count))
                                {
                                    break;
                                }
                                if (!Directory.Exists(prefabBakeFolder))
                                {
                                    Directory.CreateDirectory(prefabBakeFolder);
                                }
                                try
                                {
                                    var bakeOption = new SkeletonAnimationBaker.BakeSkeletonOption();
                                    bakeOption.prefab = validPrefabs[i].prefab;
                                    bakeOption.sampleFPS = mSampleFPS;
                                    bakeOption.outputFolder = prefabBakeFolder;
                                    bakeOption.useRGBA32Texture = mUseRGBA32Texture;
                                    bakeOption.blendType = validPrefabs[i].blendType;
                                    bakeOption.useAnimationBlending = validPrefabs[i].useAnimationBlending;
                                    bakeOption.deleteBipBones = mDeleteBipBones;
                                    bakeOption.generatePrefab = true;
                                    bakeOption.clearOutputFolder = true;
                                    bakeOption.defaultRigidAnimShader = mDefaultRigidShader;
                                    bakeOption.defaultSkinAnimShader = mDefaultSkinShader;
                                    bakeOption.useSRPBatcher = mUseSRPBatcher;
                                    bakeOption.useSeparateCPUDrivenBoneTransformData = mSeparateCPUDrivenBoneTransformData;
                                    validPrefabs[i].baker.Bake(bakeOption);
                                }
                                catch (Exception ex)
                                {
                                    Debug.LogException(ex);
                                    string msg = $"Bake {validPrefabs[i].prefab.name} failed because {AnimationBaker.ErrorMsg}, {ex.Message} ";
                                    EditorUtility.DisplayDialog("Error", msg, "OK");
                                    break;
                                }
                            }
                            EditorUtility.ClearProgressBar();
                        }

                        Export1();
                    }

                    if (GUILayout.Button("Refresh Bake List File"))
                    {
                        Export1();
                    }
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            mBakingList = EditorGUILayout.ObjectField("Baking List", mBakingList, typeof(PrefabBakingList), false, null) as PrefabBakingList;
            if (GUILayout.Button("Load"))
            {
                LoadBakingList();
            }
            if (GUILayout.Button("Save"))
            {
                SaveBakingList();
            }
            EditorGUILayout.EndHorizontal();

            if (mPrefabCount > 0)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                mSearchName = EditorGUILayout.TextField("Search Name", mSearchName);
                if (GUILayout.Button("Sort"))
                {
                    Sort();
                }
                if (!string.IsNullOrEmpty(mSearchName))
                {
                    for (int i = 0; i < mEntries.Length; ++i)
                    {
                        if (mEntries[i].prefab != null)
                        {
                            string name = mEntries[i].prefab.name;
                            if (name.IndexOf(mSearchName) >= 0)
                            {
                                DrawBakePrefab(i);
                            }
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < mPrefabCount; ++i)
                    {
                        DrawBakePrefab(i);
                    }
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndScrollView();
        }

        void MoveUp(int idx)
        {
            if (idx > 0)
            {
                var t = mEntries[idx];
                mEntries[idx] = mEntries[idx - 1];
                mEntries[idx - 1] = t;
            }
        }

        void MoveDown(int idx)
        {
            if (idx < mEntries.Length - 1)
            {
                var t = mEntries[idx];
                mEntries[idx] = mEntries[idx + 1];
                mEntries[idx + 1] = t;
            }
        }

        void DrawBakePrefab(int i)
        {
            if (mEntries != null)
            {
                EditorGUILayout.BeginHorizontal();

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal();

                var prefab = EditorGUILayout.ObjectField(mEntries[i].name, mEntries[i].prefab, typeof(GameObject), true, null) as GameObject;
                if (prefab != null)
                {
                    var type = PrefabUtility.GetPrefabAssetType(prefab);
                    if (type == PrefabAssetType.Regular || type == PrefabAssetType.Variant)
                    {
                        if (mEntries[i].prefab != prefab)
                        {
                            bool found = false;
                            for (int k = 0; k < mEntries.Length; ++k)
                            {
                                if (mEntries[k].prefab == prefab)
                                {
                                    EditorUtility.DisplayDialog("Error", $"{prefab.name} is already added to bake list!", "OK");
                                    found = true;
                                    break;
                                }
                            }

                            if (!found)
                            {
                                AnimationBaker baker = null;
                                if (mEntries[i].useVertexAnimation)
                                {
                                    baker = new VertexAnimationBaker();
                                }
                                else
                                {
                                    baker = new SkeletonAnimationBaker();
                                }
                                string err = baker.Check(prefab);
                                if (string.IsNullOrEmpty(err))
                                {
                                    mEntries[i].prefab = prefab;
                                    mEntries[i].baker = baker;
                                }
                                else
                                {
                                    EditorUtility.DisplayDialog("Error", $"Can't bake prefab {prefab.name} because of {err}", "OK");
                                }
                            }
                        }
                    }
                }
                else if (prefab == null)
                {
                    mEntries[i].prefab = null;
                    mEntries[i].baker = null;
                }

                if (mEntries[i].prefab != null)
                {
                    //bake one prefab
                    if (GUILayout.Button("Bake"))
                    {
                        string prefabBakeFolder = $"{mOutputFolder}/{mEntries[i].prefab.name}_baked";
                        if (!Directory.Exists(prefabBakeFolder))
                        {
                            Directory.CreateDirectory(prefabBakeFolder);
                        }
                        try
                        {
                            if (mEntries[i].useVertexAnimation)
                            {
                                var bakeOption = new VertexAnimationBaker.BakeVertexOption();
                                bakeOption.prefab = mEntries[i].prefab;
                                bakeOption.sampleFrameInterval = mEntries[i].sampleFrameInterval;
                                bakeOption.outputFolder = prefabBakeFolder;
                                bakeOption.blendType = mEntries[i].blendType;
                                bakeOption.useAnimationBlending = mEntries[i].useAnimationBlending;
                                bakeOption.deleteBipBones = mDeleteBipBones;
                                bakeOption.generatePrefab = true;
                                bakeOption.clearOutputFolder = true;
                                bakeOption.defaultShader = mDefaultSkinShader;
                                bakeOption.useSRPBatcher = mUseSRPBatcher;
                                mEntries[i].baker.Bake(bakeOption);
                            }
                            else
                            {
                                var bakeOption = new SkeletonAnimationBaker.BakeSkeletonOption();
                                bakeOption.prefab = mEntries[i].prefab;
                                bakeOption.sampleFPS = mSampleFPS;
                                bakeOption.outputFolder = prefabBakeFolder;
                                bakeOption.useRGBA32Texture = mUseRGBA32Texture;
                                bakeOption.blendType = mEntries[i].blendType;
                                bakeOption.useAnimationBlending = mEntries[i].useAnimationBlending;
                                bakeOption.deleteBipBones = mDeleteBipBones;
                                bakeOption.generatePrefab = true;
                                bakeOption.clearOutputFolder = true;
                                bakeOption.defaultRigidAnimShader = mDefaultRigidShader;
                                bakeOption.defaultSkinAnimShader = mDefaultSkinShader;
                                bakeOption.useSRPBatcher = mUseSRPBatcher;
                                bakeOption.useSeparateCPUDrivenBoneTransformData = mSeparateCPUDrivenBoneTransformData;
                                mEntries[i].baker.Bake(bakeOption);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogException(ex);
                            string msg = $"Bake {mEntries[i].prefab.name} failed because {AnimationBaker.ErrorMsg}, {ex.Message} ";
                            EditorUtility.DisplayDialog("Error", msg, "OK");
                        }

                        Export1();
                    }

                    if (GUILayout.Button("Select Baked Prefab"))
                    {
                        if (string.IsNullOrEmpty(mOutputFolder))
                        {
                            EditorUtility.DisplayDialog("Error", "Select output folder first!", "OK");
                        }
                        else
                        {
                            string prefabPath = Utils.ConvertToUnityAssetsPath($"{mOutputFolder}/{mEntries[i].prefab.name}_baked/{mEntries[i].prefab.name}.prefab");
                            var selectedPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                            Selection.activeObject = selectedPrefab;
                            EditorGUIUtility.PingObject(selectedPrefab);
                        }
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (mEntries[i].prefab != null)
                {
                    mEntries[i].display = EditorGUILayout.Foldout(mEntries[i].display, "Bake Setting");
                    if (mEntries[i].display)
                    {
                        mEntries[i].gui.Draw(mEntries[i].prefab, mEntries[i].baker, mEntries[i]);
                    }
                }

                EditorGUILayout.EndVertical();

                EditorGUILayout.EndHorizontal();
            }
        }

        void SetPrefabCount(int prefabCount)
        {
            if (mEntries == null || prefabCount != mEntries.Length)
            {
                Entry[] newEntries = new Entry[prefabCount];
                for (int i = 0; i < prefabCount; ++i)
                {
                    newEntries[i] = new Entry();
                }
                
                int n = Mathf.Min(prefabCount, mPrefabCount);
                int next = 0;
                for (int i = 0; i < n; ++i)
                {
                    newEntries[i].display = mEntries[i].display;
                    newEntries[i].prefab = mEntries[i].prefab;
                    newEntries[i].name = mEntries[i].name;
                    newEntries[i].gui = mEntries[i].gui;
                    newEntries[i].baker = mEntries[i].baker;
                    ++next;
                }
                int delta = prefabCount - mPrefabCount;
                if (delta > 0)
                {
                    for (int i = 0; i < delta; ++i)
                    {
                        newEntries[next].display = false;
                        newEntries[next].gui = ScriptableObject.CreateInstance<BakePrefabSettingWindow>();
                        newEntries[next].prefab = null;
                        newEntries[next].name = $"Prefab {next}";
                        ++next;
                    }
                }

                mEntries = newEntries;
                mPrefabCount = prefabCount;
            }
        }

        bool OnClickChangeCount(List<InputDialog.Item> param)
        {
            int prefabCount;
            string text = (param[0] as InputDialog.StringItem).text;
            Utils.ParseInt(text, out prefabCount);
            if (prefabCount <= 0)
            {
                return false;
            }

            SetPrefabCount(prefabCount);
            return true;
        }

        //把所有烘培的animData路径保存,以便于加载时生成对应的动画贴图
        //导出有版本号管理的版本
        void Export1()
        {
            var mapconfigFilePath = Utils.TryToGetValidConfigFilePath();
            if (string.IsNullOrEmpty(mapconfigFilePath))
            {
                EditorUtility.DisplayDialog("Error", "map config file not found!", "OK");
                return;
            }

            if (string.IsNullOrEmpty(mOutputFolder))
            {
                EditorUtility.DisplayDialog("Error", "Select bake folder!", "OK");
                return;
            }

            var animDatas = EditorUtils.FindAssetsByType<BakedAnimationData>();
            List<string> paths = new List<string>();
            List<string> bakedPrefabNames = new List<string>();
            for (int i = 0; i < animDatas.Count; ++i)
            {
                paths.Add(AssetDatabase.GetAssetPath(animDatas[i]));
                bakedPrefabNames.Add(animDatas[i].prefabName);
            }

            //find hooks
            var hooks = AssetDatabase.FindAssets("t:SkeletonAnimationExportHook");
            if (hooks.Length > 0)
            {
                for (int i = 0; i < hooks.Length; ++i)
                {
                    var hookPath = AssetDatabase.GUIDToAssetPath(hooks[i]);
                    var hook = AssetDatabase.LoadAssetAtPath<SkeletonAnimationExportHook>(hookPath);
                    hook.Export(paths, bakedPrefabNames);
                }
            }

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.BakedAnimationDataVersion.majorVersion);
            writer.Write(VersionSetting.BakedAnimationDataVersion.minorVersion);

            writer.Write(mUseBakedAnimation);
            Utils.WriteStringList(writer, paths);
            var assetPath = Utils.ConvertToUnityAssetsPath(mOutputFolder);
            Debug.Assert(!string.IsNullOrEmpty(assetPath), "Invalid bake output folder!");
            Utils.WriteString(writer, assetPath);
            Utils.WriteStringList(writer, bakedPrefabNames);
            writer.Write(mUseRGBA32Texture);

            var data = stream.ToArray();

            var mapConfig = MapConfig.CreateFromFile(mapconfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());

            File.WriteAllBytes(mapConfig.bakedAnimDataPath, data);
            writer.Close();

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        void LoadBakingList()
        {
            if (mBakingList == null)
            {
                EditorUtility.DisplayDialog("Error", "Select a baking list", "OK");
                return;
            }

            Reset();
            mPrefabCount = 0;
            mOutputFolder = mBakingList.outputFolder;
            mSampleFPS = mBakingList.sampledFPS;
            mDeleteBipBones = mBakingList.deleteGameObjectsOfNameStartWithBIP;
            mUseRGBA32Texture = mBakingList.useRGBA32Texture;
            mUseSRPBatcher = mBakingList.useSRPBatcher;
            mSeparateCPUDrivenBoneTransformData = mBakingList.separateCPUDrivenBoneTransformData;
            mDefaultRigidShader = mBakingList.defaultRigidShader;
            mDefaultSkinShader = mBakingList.defaultSkinShader;

            List<PrefabBakeSetting> prefabs = new List<PrefabBakeSetting>();
            for (int i = 0; i < mBakingList.prefabs.Length; ++i)
            {
                var path = AssetDatabase.GUIDToAssetPath(mBakingList.prefabs[i].guid);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab != null)
                {
                    prefabs.Add(mBakingList.prefabs[i]);
                }
            }
            SetPrefabCount(prefabs.Count);
            for (int i = 0; i < prefabs.Count; ++i)
            {
                var path = AssetDatabase.GUIDToAssetPath(prefabs[i].guid);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                bool found = false;
                for (int k = 0; k < mEntries.Length; ++k)
                {
                    if (mEntries[k].prefab == prefab)
                    {
                        EditorUtility.DisplayDialog("Error", $"{prefab.name} is already added to bake list!", "OK");
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    AnimationBaker baker = null;
                    if (mEntries[i].useVertexAnimation)
                    {
                        baker = new VertexAnimationBaker();
                    }
                    else
                    {
                        baker = new SkeletonAnimationBaker();
                    }
                    string err = baker.Check(prefab);
                    if (string.IsNullOrEmpty(err))
                    {
                        mEntries[i].prefab = prefab;
                        mEntries[i].baker = baker;
                        mEntries[i].blendType = prefabs[i].blendType;
                        mEntries[i].useAnimationBlending = prefabs[i].useAnimationBlending;
                        mEntries[i].useVertexAnimation = prefabs[i].useVertexAnimation;
                        mEntries[i].sampleFrameInterval = prefabs[i].sampleFrameInterval;

                        foreach (var p in prefabs[i].materialShaders)
                        {
                            var mtlPath = AssetDatabase.GUIDToAssetPath(p.Key);
                            var mtl = AssetDatabase.LoadAssetAtPath<Material>(mtlPath);
                            var shaderPath = AssetDatabase.GUIDToAssetPath(p.Value);
                            var shader = AssetDatabase.LoadAssetAtPath<Shader>(shaderPath);
                            if (shader != null && mtl != null)
                            {
                                mEntries[i].baker.materialManager.SetMaterialShader(mtl, shader);
                            }
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", $"Can't bake prefab {prefab.name} because of {err}", "OK");
                    }
                }
            }
        }

        void SaveBakingList()
        {
            string filePath = null;
            if (mBakingList == null)
            {
                filePath = EditorUtility.SaveFilePanel("Open File List", "", "prefab_baking_list", "asset");
                filePath = Utils.ConvertToUnityAssetsPath(filePath);
            }
            else
            {
                filePath = AssetDatabase.GetAssetPath(mBakingList);
            }

            if (!string.IsNullOrEmpty(filePath))
            {
                var obj = ScriptableObject.CreateInstance<PrefabBakingList>();
                List<int> validIndex = new List<int>();
                int n = mEntries != null ? mEntries.Length : 0;
                for (int i = 0; i < n; ++i)
                {
                    if (mEntries[i].prefab != null)
                    {
                        validIndex.Add(i);
                    }
                }

                obj.prefabs = new PrefabBakeSetting[validIndex.Count];
                obj.outputFolder = mOutputFolder;
                obj.sampledFPS = mSampleFPS;
                obj.deleteGameObjectsOfNameStartWithBIP = mDeleteBipBones;
                obj.useRGBA32Texture = mUseRGBA32Texture;
                obj.useSRPBatcher = mUseSRPBatcher;
                obj.separateCPUDrivenBoneTransformData = mSeparateCPUDrivenBoneTransformData;
                obj.defaultSkinShader = mDefaultSkinShader;
                obj.defaultRigidShader = mDefaultRigidShader;
                for (int i = 0; i < validIndex.Count; ++i)
                {
                    var entry = mEntries[validIndex[i]];
                    obj.prefabs[i] = new PrefabBakeSetting();
                    obj.prefabs[i].guid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(entry.prefab));
                    obj.prefabs[i].materialShaders = new MaterialSetting();
                    obj.prefabs[i].blendType = entry.blendType;
                    obj.prefabs[i].useAnimationBlending = entry.useAnimationBlending;
                    obj.prefabs[i].useVertexAnimation = entry.useVertexAnimation;
                    obj.prefabs[i].sampleFrameInterval = entry.sampleFrameInterval;
                    var processingMaterials = entry.baker.materialManager.processingMaterials;
                    foreach (var p in processingMaterials)
                    {
                        var materialGUID = p.Key;
                        var shaderGUID = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(p.Value));
                        obj.prefabs[i].materialShaders.Add(materialGUID, shaderGUID);
                    }
                }

                AssetDatabase.CreateAsset(obj, filePath);
                mBakingList = AssetDatabase.LoadAssetAtPath<PrefabBakingList>(filePath);
            }
        }

        void Sort()
        {
            var list = new List<Entry>();
            list.AddRange(mEntries);
            list.Sort(new SortEntry());
            mEntries = list.ToArray();
        }

        int mPrefabCount = 0;
        float mSampleFPS = 60;
        Entry[] mEntries;
        string mOutputFolder;
        PrefabBakingList mBakingList;
        Vector2 mScrollPos;
        bool mUseBakedAnimation = true;
        bool mUseRGBA32Texture = true;
        bool mUseSRPBatcher = false;
        bool mDeleteBipBones = false;
        bool mSeparateCPUDrivenBoneTransformData = false;
        string mSearchName;
        Shader mDefaultRigidShader;
        Shader mDefaultSkinShader;
    }
}
#endif
