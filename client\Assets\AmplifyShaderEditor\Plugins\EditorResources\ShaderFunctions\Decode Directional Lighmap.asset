%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Decode Directional Lighmap
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17701\n412;73;1609;802;1389.521;361.4048;1;True;False\nNode;AmplifyShaderEditor.CustomExpressionNode;1;-135.5,-181;Float;False;return
    DecodeDirectionalLightmap( Color,DirTex,NormalWorld)@;3;False;3;True;Color;FLOAT3;0,0,0;In;;Float;False;True;DirTex;FLOAT4;0,0,0,0;In;;Float;False;True;NormalWorld;FLOAT3;0,0,0;In;;Float;False;ASEDecodeDirectionalLightmap;False;False;0;3;0;FLOAT3;0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;23;-622.1301,-200.5;Inherit;False;inputColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;4;-466.5,-12;Inherit;False;NormalWorld;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;9;-480,240;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;13;293.7467,121.8659;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;16;356.1465,405.266;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;17;190.3352,367.6559;Inherit;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;False;0;0.0001;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;18;176.3352,467.6559;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;20;-527.6785,372.98;Inherit;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;15;502.8605,106.8558;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;5;682.8613,-173.207;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;6;-480,160;Inherit;False;FLOAT3;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;7;-144,64;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;8;-320,176;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;12;-140.6092,173.8985;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;11;48,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-857.1957,-104.1015;Inherit;False;DirTex;4;1;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;3;-852.2914,-189.0047;Inherit;False;Color;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;24;40.90209,216.6535;Inherit;False;23;inputColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;916.3615,-183.7863;Inherit;False;True;-1;Output;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;1;0;23;0\nWireConnection;1;1;2;0\nWireConnection;1;2;4;0\nWireConnection;23;0;3;0\nWireConnection;13;0;11;0\nWireConnection;13;1;24;0\nWireConnection;16;0;17;0\nWireConnection;16;1;18;0\nWireConnection;18;0;20;0\nWireConnection;20;0;2;0\nWireConnection;15;0;13;0\nWireConnection;15;1;16;0\nWireConnection;5;0;1;0\nWireConnection;5;3;1;0\nWireConnection;5;1;15;0\nWireConnection;5;2;15;0\nWireConnection;6;0;2;0\nWireConnection;7;0;4;0\nWireConnection;7;1;8;0\nWireConnection;8;0;6;0\nWireConnection;8;1;9;0\nWireConnection;11;0;7;0\nWireConnection;11;1;12;0\nWireConnection;0;0;5;0\nASEEND*/\n//CHKSM=887A7A2BA5CFB5CBB9854D026B260CA7971382E5"
  m_functionName: 
  m_description: 'Calls Unity internal DecodeDirectionalLightmap function.

    Uses custom graph on all other pipelines'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
