﻿ 
using Config;
using Game.Config;
using Game.Data;
using Logic;
using System.Collections.Generic;
using System.Linq;
using Cfg.G;
using Cysharp.Threading.Tasks;
using K3;
using TFW.Localization;
using UI;
using UnityEngine;
using Public;

namespace cspb
{

    public partial class D2Quest
    {
         
        public Cfg.G.CChapterQuest ConfigData
        {
            get
            {
                if (questType == QuestType.QuestTypeMain)
                {
                    return Cfg.C.CChapterQuest.I(cfgID);
                }

                return null;
            }
        }

        public Cfg.G.CNewQuestCommon DailyConfigData
        {
            get
            {
                if (questType == QuestType.QuestTypeDaily)
                {
                    return Cfg.C.CNewQuestCommon.I(cfgID);
                }

                return null;
            }
        }


        //public void TaskGoTo()
        //{
        //    if (!GuidManage.TriggerGuid(GuidManage.GuidTriggerType.ChapterTaskGuide, ConfigData.ChapterID, ConfigData.Id))
        //    {
        //        TaskUtils.GoTo(ConfigData);
        //    }
        //}

        string ClientKey
        {
            get
            {
                return string.Format("{0}_{1}", LPlayer.I.PlayerID, cfgID); 
            }
        }

        public long Process
        {
            get
            {
                if (ConfigData != null)
                {
                    if (ChapterTaskGameData.I.QuestTypeClientProcess.Contains(ConfigData.QuestType))
                    {
                        long clientProcess=long.Parse(PlayerTriggerPrefMgr.I.GetStr(ClientKey));
                        status = status> clientProcess?status: clientProcess;
                        UpdateProccess(status);
                    }
                }

                return status;
            }
        }

        public void UpdateProccess(long process)
        {
            if ( UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ChatperFunc) && LSwitchMgr.I.IsFunctionOpen(SwitchConfig.ChapterQuest))//GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.ChapterQuestOpenLV) &&
            {
                if (state == QuestState.QuestStateGoOn)
                {
                    status = process;

                    //Debug.LogWarningFormat($"Client更新任务进度{ConfigData.Id}:{ConfigData.QuestType} {process}");

                    PlayerTriggerPrefMgr.I.Save(ClientKey, status.ToString());
                    Common.EventMgr.FireEvent(Common.TEventType.ChapterQuestProcessChange, this);

                    if (ConfigData?.Count <= status && state == QuestState.QuestStateGoOn)
                    {
                        state = QuestState.QuestStateFinish;
                        Common.EventMgr.FireEvent(Common.TEventType.ChapterQuestStateChange, this);

                        Common.MessageMgr.Send(new AdventureQuestSyncReq() { questId = this.cfgID });
                    }
                }
            }
        }

        public async UniTask<string> GetDes()
        {
            string str="";
            if (questType == QuestType.QuestTypeMain)
            {
                var configData= await Cfg.C.CChapterQuest.GetConfigAsync(cfgID);
                List<CD2CityBuilding> cityBuilding;
                switch (configData.QuestType)
                {
                  
                    case 403:
                        if (configData.Params.Count > 0)
                        {
                            if (int.TryParse(configData.Params[0], out int parValue))
                            {
                                switch (parValue)
                                {
                                    case 0:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count, LocalizationMgr.Get("Soldier_anyone"));
                                        break;
                                    case 1:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count, LocalizationMgr.Get("Skill_title_1"));
                                        break;
                                    case 2:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count, LocalizationMgr.Get("Skill_title_2"));
                                        break;
                                    case 3:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count, LocalizationMgr.Get("Skill_title_3"));
                                        break;
                                    case 4:
                                        str = LocalizationMgr.Format(configData.QuestDes, configData.Count, LocalizationMgr.Get("Skill_title_4"));
                                        break;
                                    default:
                                        break;
                                }
                            }
                           
                        }
                        else
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }
                        break;
                    case 505:
                    case 702:
                        //采集金币
                        str = LocalizationMgr.Format(configData.QuestDes, UIStringUtils.ExchangeValue(configData.Count));
                        break;
                    case 103:
                        str = LocalizationMgr.Format(configData.QuestDes, UIStringUtils.ExchangeValue(configData.Count), configData.Params[0]);
                        break;
                    case 116:
                        var cityType = int.Parse(configData.Params[0]);

                        cityBuilding = (await Cfg.C.CD2CityBuilding.RawDictAsync()).Select(x => x.Value).ToList();
                        foreach (var item in cityBuilding)
                        {
                            if (item.Type == cityType)
                            {
                                str = LocalizationMgr.Format(configData.QuestDes,configData.Count, LocalizationMgr.Get(item.Name));
                                break;
                            }
                        }
                        break;
                    case 316:
                    case 121:
                    case 122:
                    case 120:
                    case 314:
                        var cityType2 = int.Parse(configData.Params[0]);
                        cityBuilding = (await Cfg.C.CD2CityBuilding.RawDictAsync()).Select(x => x.Value).ToList();
                        foreach (var item in cityBuilding)
                        {
                            if (item.Type == cityType2)
                            {
                                str = LocalizationMgr.Format(configData.QuestDes, configData.Count,LocalizationMgr.Get(item.Name));
                                break;
                            }
                        }
                        break;
                    case 201:
                    case 203:
                    case 301:
                    case 501:
                    case 503:
                    default:
                        if (configData.Params?.Count > 0)
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count, configData.Params[0]);
                        }
                        else
                        {
                            str = LocalizationMgr.Format(configData.QuestDes, configData.Count);
                        }
                      
                        break;
                }
             
               
            }

            return str;
        }


        public async UniTask<string> GetFinishDesc()
        {
            string str = "";
            if (questType == QuestType.QuestTypeMain)
            {
                var configData = await Cfg.C.CChapterQuest.GetConfigAsync(cfgID);

                var reward = ChapterTaskMgr.I.GetCurrentChapterConfig();
                if (reward != null)
                {
                    Cfg.G.CD2Hero hero = await Cfg.C.CD2Hero.GetConfigAsync(reward.Hero);

                    str = LocalizationMgr.Format(configData.ActInfo, hero.Name.ToLocal());
                }
            }

            return str;
        }
    }
}