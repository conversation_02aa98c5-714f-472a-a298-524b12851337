﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //p2的地图圆形边界层
    public partial class CircleBorderLayer : ModelLayer
    {
        public CircleBorderLayer(Map map) : base(map) { }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.CircleBorderLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, 1);
                }
                config = new MapLayerLODConfig(map, lods);
            }
            mLayerData = new CircleBorderLayerData(header, config, map, sourceLayer.combineBorderLOD);
            mLayerView = new CircleBorderLayerView(mLayerData as CircleBorderLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            if (sourceLayer.objects != null)
            {
                int n = sourceLayer.objects.Length;
                for (int i = 0; i < n; ++i)
                {
                    var model = sourceLayer.objects[i] as config.CircleBorderData;
                    var modelTemplate = map.FindObject(model.modelTemplateID) as ModelTemplate;
                    if (modelTemplate == null)
                    {
                        UnityEngine.Debug.Assert(false, $"model template{model.modelTemplateID} is not found!");
                    }
                    else
                    {
                        var modelData = new CircleBorderData(model.id, map, 0, model.position, model.rotation, model.scale, modelTemplate, model.isAlwaysVisibleAtHigherLODs);
                        mLayerData.AddObjectData(modelData);
                    }
                }
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);

            mLayerView.asyncLoading = false;
        }

        //返回第一个prefab path
        public string GetBorderPrefabPath()
        {
            var objects = mLayerData.objects;
            foreach (var obj in objects)
            {
                var objectData = obj.Value;
                return objectData.GetAssetPath(0);
            }
            return null;
        }
    }
}
