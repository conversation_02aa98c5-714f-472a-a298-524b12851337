%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo_WaterReflection
  m_Shader: {fileID: 4800000, guid: 7bcde38e6d372b9438d01e647bd5dfec, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: c9de4a6ca27ab52479c42e95c4ec1194, type: 3}
        m_Scale: {x: 8, y: 4}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FoamTex:
        m_Texture: {fileID: 2800000, guid: 869319dead08c3b4a9c074af06b585f4, type: 3}
        m_Scale: {x: 4, y: 4}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - _Mask1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DepthAlpha: 1.5
    - _DepthDistance: 0.6
    - _DepthMinAlpha: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendTCP2: 10
    - _FoamSmooth: 0.078
    - _FoamSpread: 2
    - _FoamStrength: 0.8
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _NormalDepthInfluence: 0.119
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _RR: 3
    - _RampSmooth: 0.641
    - _RampThreshold: 0.5
    - _ReflRoughness: 5.28
    - _ReflStrength: 0.708
    - _RimMax: 1
    - _RimMin: 0
    - _Shininess: 10
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendTCP2: 5
    - _UVSec: 0
    - _UVWaveAmplitude: 0.01
    - _UVWaveFrequency: 0.5
    - _UVWaveSpeed: 1.5
    - _WaveFrequency: 2
    - _WaveHeight: 0
    - _WaveSpeed: 1
    - _ZWrite: 1
    - __dummy__: 0
    m_Colors:
    - _BumpSpeed: {r: 0.1, g: 0.1, b: -0.1, a: -0.1}
    - _Color: {r: 0.13754325, g: 0.29054993, b: 0.35294116, a: 1}
    - _DepthColor: {r: 0.066733986, g: 0.29797322, b: 0.547, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FoamColor: {r: 0.9, g: 0.9, b: 0.9, a: 0.922}
    - _FoamSpeed: {r: 1, g: 1, b: -1, a: -1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _ReflectColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.30147058, g: 0.2948205, b: 0.2948205, a: 1}
    - _SColor: {r: 0.039468, g: 0.344062, b: 0.53676474, a: 1}
    - _SpecColor: {r: 0.1297578, g: 0.14705884, b: 0.14013842, a: 1}
