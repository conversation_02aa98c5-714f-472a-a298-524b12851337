using UnityEditor.PackageManager;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;

namespace CISystem
{
    internal class PackageExcluder : IPreprocessBuildWithReport, IPostprocessBuildWithReport
    {
        private static readonly List<string> removedPackages = new List<string>();

        public int callbackOrder => 0;

        public void OnPreprocessBuild(BuildReport report)
        {
            RemovePackages(report.summary.platform);
        }

        public void OnPostprocessBuild(BuildReport report)
        {
            RestorePackages();
        }

        private void RemovePackages(BuildTarget buildTarget)
        {
            removedPackages.Clear();
            
            var settings = CISystemProjectSettings.instance;
            var excludePackages = settings.GetExculePackages(buildTarget);
            
            foreach (var packageName in excludePackages)
            {
                var request = Client.List();
                while (!request.IsCompleted)
                {
                }

                var package = request.Result.FirstOrDefault(p => p.name == packageName);
                if (package != null)
                {
                    removedPackages.Add(package.packageId);
                    Client.Remove(packageName);
                }
            }
            
            UnityEngine.Debug.Log($"Removed packages: {string.Join(", ", removedPackages)}");
        }

        private void RestorePackages()
        {
            foreach (var packageId in removedPackages)
            {
                Client.Add(packageId);
            }

            removedPackages.Clear();
            
            UnityEngine.Debug.Log("Restored removed packages");
        }
    }
}