﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    //编辑器的数据导出器
    [Black]
    public class MapBinaryExporter
    {
        public bool Save(string projectFolderPath, int minorVersion)
        {
            string errorMsg = CheckCorrectness();
            if (errorMsg.Length > 0)
            {
                EditorUtility.DisplayDialog("Exporting Failed", errorMsg, "OK");
                return false;
            }

            mProjectFolder = projectFolderPath;

            mIDExport = new IDExporter();

            var dataPath = projectFolderPath + "/" + "mapData.bytes";
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteString(writer, "ncmp");

            writer.Write(majorVersion);
            writer.Write(minorVersion);

            SaveSetting(writer, projectFolderPath);
            GenerateRiverAssets();
            SaveModelTemplates(writer);
            SaveTerrainPrefabManager(writer);
            SaveCamera(writer);
            SaveMap(writer);
            SaveMapObstacles(writer);
            SaveGridRegionSetting(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);

            writer.Close();

            return true;
        }

        //检测正确性
        string CheckCorrectness()
        {
            string errorMessage = "";
            var map = Map.currentMap;

            int nLayers = map.GetMapLayerCount();
            for (int i = 0; i < nLayers; ++i)
            {
                float nextLOD = -1;
                var layer = map.GetMapLayerByIndex(i);
                var lodConfig = layer.GetLayerData().lodConfig;
                if (lodConfig != null)
                {
                    var lodSetting = lodConfig.lodConfigs;
                    for (int k = 0; k < lodSetting.Length; ++k)
                    {
                        if (lodSetting[k].changeZoom < nextLOD + 1)
                        {
                            errorMessage += string.Format("Invalid lod start index {0} for map layer \"{1}\"\n", lodSetting[k].changeZoom, layer.name);
                        }
                        nextLOD = lodSetting[k].changeZoom;
                    }
                }
            }

            //check river layer
            var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (riverLayer != null)
            {
                if (riverLayer.needGenerateAssets)
                {
                    errorMessage += "You need generate river assets before exporting!\n";
                }
            }

            return errorMessage;
        }

        void SaveSetting(BinaryWriter writer, string exportFolder)
        {
            var data = Map.currentMap.data as EditorMapData;
            //Utils.WriteVector2(writer, new Vector2(data.cameraMinHeight, data.cameraMaxHeight));
            //no more camera setting in editor!
            Utils.WriteVector2(writer, Vector2.zero);
            var dataFolder = Utils.ConvertToUnityAssetsPath(exportFolder);
            Utils.WriteString(writer, dataFolder);
        }

        void GenerateRiverAssets()
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
            if (layer == null)
            {
                return;
            }

            //make sure we export river model templates
            foreach (var p in layer.riverPrefabInfos)
            {
                Map.currentMap.GetOrCreateModelTemplate(p.Key, p.Value, false, false);
            }
        }

        void SaveTerrainPrefabManager(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var prefabManager = map.data.terrainPrefabManager;
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetOrCreateGroup(i);
                int nPrefabs = group.prefabCount;
                writer.Write(nPrefabs);
                for (int k = 0; k < nPrefabs; ++k)
                {
                    var prefabPath = group.GetPrefabPath(k, 0);
                    Utils.WriteString(writer, prefabPath);
                }
            }
        }

        List<ModelTemplate> GetUsedModelTemplates()
        {
            var map = Map.currentMap;
            var templates = map.data.GetUsedModelTemplates();
            return templates;
        }

        void SaveStringTable(BinaryWriter writer)
        {
            mStringTableIndices.Clear();
            var templates = GetUsedModelTemplates();
            List<string> uniqueStrings = new List<string>();
            for (int i = 0; i < templates.Count; ++i)
            {
                CalculateTemplateStringTable(writer, templates[i] as ModelTemplate, uniqueStrings);
            }

            int n = uniqueStrings.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, uniqueStrings[i]);
            }
        }

        void CalculateTemplateStringTable(BinaryWriter writer, ModelTemplate temp, List<string> stringTable)
        {
            var prefabPath = temp.GetLODPrefabPath(0);
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.lodCount;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.GetChildPrefabTransform(lod);
                    int n = childPrefabTransformList.Count;
                    for (int i = 0; i < n; ++i)
                    {
                        if (!stringTable.Contains(childPrefabTransformList[i].path))
                        {
                            mStringTableIndices[childPrefabTransformList[i].path] = stringTable.Count;
                            stringTable.Add(childPrefabTransformList[i].path);
                        }
                        if (!stringTable.Contains(childPrefabTransformList[i].tag))
                        {
                            mStringTableIndices[childPrefabTransformList[i].tag] = stringTable.Count;
                            stringTable.Add(childPrefabTransformList[i].tag);
                        }
                    }
                }
            }

            //save lod info
            int nLOD = 0;
            if (temp.lodPrefabPaths != null)
            {
                nLOD = temp.lodPrefabPaths.Count;
            }
            for (int i = 0; i < nLOD; ++i)
            {
                if (!stringTable.Contains(temp.lodPrefabPaths[i]))
                {
                    mStringTableIndices[temp.lodPrefabPaths[i]] = stringTable.Count;
                    stringTable.Add(temp.lodPrefabPaths[i]);
                }
            }
        }

        void SaveModelTemplates(BinaryWriter writer)
        {
            //计算路径的string table
            SaveStringTable(writer);
            //get the actually used model templates
            var templates = GetUsedModelTemplates();
            writer.Write(templates.Count);
            for (int i = 0; i < templates.Count; ++i)
            {
                SaveModelTemplate(writer, templates[i] as ModelTemplate);
            }
        }

        void SaveModelTemplate(BinaryWriter writer, ModelTemplate temp)
        {
            mIDExport.Export(writer, temp.id);

            var prefabPath = temp.GetLODPrefabPath(0);

            GameObject gameObject = null;
            if (!string.IsNullOrEmpty(prefabPath))
            {
                gameObject = MapModuleResourceMgr.LoadPrefab(prefabPath);
            }
            Bounds bounds = Utils.RectToBounds(temp.bounds);
            if (gameObject != null)
            {
                bounds = GameObjectBoundsCalculator.CalculateBounds(gameObject);
            }

            Utils.WriteBounds(writer, bounds);
            Utils.WriteString(writer, prefabPath);

            writer.Write(temp.isTileModelTemplate);
            if (temp.isTileModelTemplate)
            {
                //保存model template使用的每一层lod的子prefab信息
                int nLODs = temp.lodCount;
                writer.Write(nLODs);
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //保存子prefeb的信息
                    var childPrefabTransformList = temp.GetChildPrefabTransform(lod);
                    int n = childPrefabTransformList.Count;
                    writer.Write(n);
                    for (int i = 0; i < n; ++i)
                    {
                        int idx = mStringTableIndices[childPrefabTransformList[i].path];
                        writer.Write(idx);
                        //Utils.WriteString(writer, childPrefabTransformList[i].path);
                        Utils.WriteRect(writer, childPrefabTransformList[i].localBoundsInPrefab);
                        idx = mStringTableIndices[childPrefabTransformList[i].tag];
                        writer.Write(idx);
                        //Utils.WriteString(writer, childPrefabTransformList[i].tag);
                        Utils.WriteVector3(writer, childPrefabTransformList[i].position);
                        Utils.WriteVector3(writer, childPrefabTransformList[i].editorScaling);
                        Utils.WriteQuaternion(writer, childPrefabTransformList[i].editorRotation);
                    }
                }
            }

            //save lod info
            int nLOD = 0;
            if (temp.lodPrefabPaths != null)
            {
                nLOD = temp.lodPrefabPaths.Count;
            }
            writer.Write(nLOD);
            for (int i = 0; i < nLOD; ++i)
            {
                var idx = mStringTableIndices[temp.lodPrefabPaths[i]];
                writer.Write(idx);
                //Utils.WriteString(writer, temp.lodPrefabPaths[i]);
            }
            nLOD = 0;
            if (temp.existedLODs != null)
            {
                nLOD = temp.existedLODs.Count;
            }
            writer.Write(nLOD);
            for (int i = 0; i < nLOD; ++i)
            {
                writer.Write(temp.existedLODs[i]);
            }

            writer.Write(temp.preload);
        }

        void SaveMap(BinaryWriter writer)
        {
            var map = Map.currentMap;
            writer.Write(map.mapWidth);
            writer.Write(map.mapHeight);
            writer.Write(map.data.borderHeight);
            writer.Write(map.data.isCircleMap);
            SaveMapLayers(writer);
            SaveMapLODConfig(writer);
        }

        void SaveMapLODConfig(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var lodManager = map.data.lodManager;
            int n = lodManager.lodCount;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                writer.Write(lod.cameraHeight);
            }
        }

        void SaveMapLayers(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var validLayers = GetValidMapLayers(map);
            int nLayers = validLayers.Count;
            writer.Write(nLayers);
            for (int i = 0; i < nLayers; ++i)
            {
                var layer = validLayers[i];

                var layerType = GetMapLayerType(layer);
                writer.Write(layerType);
                mIDExport.Export(writer, layer.id);
                Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
                Utils.WriteVector3(writer, layer.layerOffset);

                if (layer.GetType() == typeof(EditorGridModelLayer))
                {
                    SaveGridModelLayer(writer, layer as EditorGridModelLayer);
                }
                else if (layer.GetType() == typeof(ModelLayer))
                {
                    SaveModelLayer(writer, layer as ModelLayer);
                }
                else if (layer.GetType() == typeof(CircleBorderLayer))
                {
                    SaveCircleBorderLayer(writer, layer as CircleBorderLayer);
                }
                else if (layer.GetType() == typeof(BlendTerrainLayer))
                {
                    SaveBlendTerrainLayer(writer, layer as BlendTerrainLayer);
                }
                else if (layer.GetType() == typeof(RailwayLayer))
                {
                    SaveRailwayLayer(writer, layer as RailwayLayer);
                }
                else if (layer.GetType() == typeof(LODLayer))
                {
                    SaveLODLayer(writer, layer as LODLayer);
                }
                else if (layer.GetType() == typeof(PolygonRiverLayer))
                {
                    //导出河流层,这一层会创建一个游戏中使用的河流层,和编辑器的河流层不同
                    SavePolygonRiverLayer(writer, layer as PolygonRiverLayer);
                }
                else if (layer.GetType() == typeof(EditorComplexGridModelLayer))
                {
                    SaveComplexGridModelLayer(writer, layer as EditorComplexGridModelLayer);
                }
                else
                {
                    Debug.Assert(false, "unknown map layer type!");
                }
            }
        }

        string ConvertToRuntimeLayerName(string editorLayerName)
        {
            if (editorLayerName == MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER)
            {
                return MapCoreDef.MAP_LAYER_NODE_RUNTIME_RIVER;
            }
            return editorLayerName;
        }

        List<MapLayerBase> GetValidMapLayers(Map map)
        {
            List<MapLayerBase> validMapLayers = new List<MapLayerBase>();
            int n = map.GetMapLayerCount();
            for (int i = 0; i < n; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (layer is NavMeshLayer ||
                    layer is SpriteTileLayer ||
                    layer is NPCRegionLayer ||
                    layer is EntitySpawnLayer ||
                    layer is RuinLayer ||
                    layer is CameraColliderLayer ||
                    layer is MapCollisionLayer)
                {
                    continue;
                }
                validMapLayers.Add(layer);
            }
            return validMapLayers;
        }

        void SaveGridModelLayer(BinaryWriter writer, EditorGridModelLayer layer)
        {
            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            writer.Write((int)layer.layerData.gridType);

            HashSet<int> savedIDs = new HashSet<int>();
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var data = layer.GetObjectData(j, i) as ModelData;
                    if (data != null && !savedIDs.Contains(data.id))
                    {
                        savedIDs.Add(data.id);
                        SaveGridModelData(writer, data, layer);
                    }
                    else
                    {
                        mIDExport.Export(writer, 0);
                    }
                }
            }
            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
        }

        void SaveLODLayer(BinaryWriter writer, LODLayer layer)
        {
            writer.Write(layer.GetLayerData().tileWidth);
            writer.Write(layer.GetLayerData().tileHeight);

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.GetLayerData());
        }

        void SavePolygonRiverLayer(BinaryWriter writer, PolygonRiverLayer layer)
        {
            List<IMapObjectData> polygonRivers = new List<IMapObjectData>();
            layer.GetAllObjects(polygonRivers);
            writer.Write(layer.GetLayerData().tileWidth);
            writer.Write(layer.GetLayerData().tileHeight);
            int n = polygonRivers.Count;
            var countPos = writer.BaseStream.Position;
            writer.Write(n);
            int realObjectCount = 0;

            var rivePrefabInfos = layer.riverPrefabInfos;
            for (int i = 0; i < n; ++i)
            {
                var river = polygonRivers[i] as PolygonRiverData;
                var sections = river.sections;
                for (int s = 0; s < sections.Count; ++s)
                {
                    ++realObjectCount;
                    //export every section of river
                    var objectID = Map.currentMap.nextCustomObjectID;
                    var bounds = sections[s].bounds;
                    var position = Utils.ToVector3(bounds.center);
                    var prefabPath = rivePrefabInfos[sections[s].id];
                    var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(sections[s].id, prefabPath, false, false);

                    mIDExport.Export(writer, objectID);
                    Utils.WriteVector3(writer, position);
                    mIDExport.Export(writer, modelTemplate.id);
                    writer.Write(river.hideLOD);
                }
            }
            Utils.WriteAndJump(writer, countPos, realObjectCount);
            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.GetLayerData());
        }

        void SaveBlendTerrainLayer(BinaryWriter writer, BlendTerrainLayer layer)
        {
            var map = Map.currentMap;

            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tile = layer.GetTile(j, i);
                    if (tile != null)
                    {
                        SaveBlendTerrainTileData(writer, tile);
                    }
                    else
                    {
                        mIDExport.Export(writer, 0);
                    }
                }
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
        }

        void SaveBlendTerrainTileData(BinaryWriter writer, BlendTerrainTileData tileData)
        {
            mIDExport.Export(writer, tileData.id);
            mIDExport.Export(writer, tileData.GetModelTemplateID());
            writer.Write(tileData.type);
            writer.Write(tileData.index);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                }
            }
        }

        void SaveCircleBorderLayer(BinaryWriter writer, CircleBorderLayer layer)
        {
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveCircleBorderData(writer, objData as CircleBorderData);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);

            var layerData = layer.layerData as CircleBorderLayerData;
            writer.Write(layerData.combineBorderLOD);
        }

        void SaveCircleBorderData(BinaryWriter writer, CircleBorderData data)
        {
            SaveMapObjectBaseData(writer, data);
            mIDExport.Export(writer, data.GetModelTemplateID());
            writer.Write(data.isAlwaysVisibleAtHigherLODs);
        }

        void SaveRailwayLayer(BinaryWriter writer, RailwayLayer layer)
        {
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveRailObjectData(writer, objData as RailObjectData);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
            SaveModelLODGroupManager(writer, layer.layerData.lodGroupManager);

            writer.Write(layer.railLayerData.railCount);
            writer.Write(layer.railLayerData.railWidth);
            writer.Write(layer.railLayerData.railTotalLength);
            Utils.WriteVector3(writer, layer.railLayerData.railwayCenter);
            writer.Write(layer.railLayerData.railPrefabLength);
        }

        void SaveComplexGridModelLayer(BinaryWriter writer, EditorComplexGridModelLayer layer)
        {
            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveComplexGridModelData(writer, objData as ComplexGridModelData);
            }
            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
        }

        void SaveComplexGridModelData(BinaryWriter writer, ComplexGridModelData data)
        {
            SaveMapObjectBaseData(writer, data);
            mIDExport.Export(writer, data.GetModelTemplateID());
            writer.Write(data.occupiedGridCount);
        }

        void SaveModelLayer(BinaryWriter writer, ModelLayer layer)
        {
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);

            var allObjects = layer.layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                SaveModelData(writer, objData as ModelData);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layer.layerData);
        }

        void SaveMapObjectBaseData(BinaryWriter writer, MapObjectData data)
        {
            bool isDefaultRotation = (data.GetRotation() == Quaternion.identity);
            bool isDefaultScale = (data.GetScale() == Vector3.one);

            mIDExport.Export(writer, data.id);
            Utils.WriteVector3(writer, data.GetPosition());
            writer.Write(isDefaultRotation);
            if (isDefaultRotation == false)
            {
                Utils.WriteQuaternion(writer, data.GetRotation());
            }
            writer.Write(isDefaultScale);
            if (isDefaultScale == false)
            {
                Utils.WriteVector3(writer, data.GetScale());
            }
        }

        void SaveGridModelData(BinaryWriter writer, ModelData data, EditorGridModelLayer layer)
        {
            SaveMapObjectBaseData(writer, data);
            var coord = (layer.layerData as SparseGridObjectLayerData).GetObjectCoordinate(data);
            var pos = layer.layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);
            bool isDefaultPosition = pos == data.GetPosition();
            writer.Write(isDefaultPosition);
            mIDExport.Export(writer, data.GetModelTemplateID());
        }

        void SaveModelData(BinaryWriter writer, ModelData data)
        {
            var modelData = data as ModelData;
            SaveMapObjectBaseData(writer, data);
            mIDExport.Export(writer, modelData.GetModelTemplateID());
        }

        void SaveRailObjectData(BinaryWriter writer, RailObjectData data)
        {
            var railData = data as RailObjectData;
            SaveMapObjectBaseData(writer, railData);
            mIDExport.Export(writer, railData.GetModelTemplateID());
            writer.Write((int)railData.type);
            writer.Write(railData.isGroupLeader);
            int id = railData.GetModelLODGroupID();
            mIDExport.Export(writer, id);
            writer.Write(railData.railIndex);
            writer.Write(railData.segmentIndex);
        }

        int GetMapLayerType(MapLayerBase layer)
        {
            if (layer.GetType() == typeof(EditorGridModelLayer))
            {
                return MapLayerType.kGridModelLayer;
            }
            else if (layer.GetType() == typeof(ModelLayer))
            {
                return MapLayerType.kModelLayer;
            }
            else if (layer.GetType() == typeof(BlendTerrainLayer))
            {
                return MapLayerType.kBlendTerrainLayer;
            }
            else if (layer.GetType() == typeof(LODLayer))
            {
                return MapLayerType.kLODLayer;
            }
            else if (layer.GetType() == typeof(CircleBorderLayer))
            {
                return MapLayerType.kCircleBorderLayer;
            }
            else if (layer.GetType() == typeof(RailwayLayer))
            {
                return MapLayerType.kRailwayLayer;
            }
            else if (layer.GetType() == typeof(PolygonRiverLayer))
            {
                return MapLayerType.kRiverLayer;
            }
            else if (layer.GetType() == typeof(EditorComplexGridModelLayer))
            {
                return MapLayerType.kComplexGridModelLayer;
            }
            else
            {
                Debug.Assert(false, "unknown layer type");
            }
            return -1;
        }

        void SaveCamera(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var camera = map.camera;

            var cameraPos = camera.transform.position;
            Utils.WriteVector3(writer, cameraPos);
            Utils.WriteQuaternion(writer, camera.transform.rotation);
            writer.Write(camera.orthographic);
            writer.Write(camera.orthographicSize);
            writer.Write(camera.fieldOfView);
        }

        //保存地图上prefab的障碍物数据
        void SaveMapObstacles(BinaryWriter writer)
        {
            SaveLocalObstacles(writer);
            SaveGlobalObstacles(writer);
            SaveCameraCollider(writer);
        }

        void SaveGridRegionSetting(BinaryWriter writer)
        {
            var gridRegionEditor = (Map.currentMap.data as EditorMapData).gridRegionEditor;
            //检测到底是否使用了region编辑
            bool hasRegionData = gridRegionEditor.HasRegionData();
            writer.Write(hasRegionData);
            if (hasRegionData)
            {
                var grids = gridRegionEditor.grids;
                writer.Write(gridRegionEditor.gridWidth);
                writer.Write(gridRegionEditor.gridHeight);
                writer.Write(gridRegionEditor.horizontalGridCount);
                writer.Write(gridRegionEditor.verticalGridCount);
                short[] gridData = ConvertToShortGrids(grids);
                writer.Write(gridData.Length);
                for (int i = 0; i < gridData.Length; ++i)
                {
                    writer.Write(gridData[i]);
                }

                //save grid templates
                var templates = gridRegionEditor.templates;
                int n = templates.Count;
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    Utils.WriteColor32(writer, templates[i].color);
                    writer.Write((byte)templates[i].type);
                }
            }
        }

        short[] ConvertToShortGrids(int[,] grids)
        {
            int rows = grids.GetLength(0);
            int cols = grids.GetLength(1);
            short[] data = new short[rows * cols];
            int idx = 0;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    data[idx] = (short)grids[i, j];
                    ++idx;
                }
            }
            return data;
        }

        byte[] ConvertTo4BitGrids(int[,] grids)
        {
            int rows = grids.GetLength(0);
            int cols = grids.GetLength(1);
            int byteCount = rows * cols / 2;
            byte[] array = new byte[byteCount];
            bool higher = false;
            int byteIdx = 0;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    Set4Bit(array, byteIdx, higher, (byte)grids[i, j]);
                    higher = !higher;
                    if (higher == false)
                    {
                        ++byteIdx;
                    }
                }
            }
            return array;
        }

        void Set4Bit(byte[] array, int byteIdx, bool higher4Bit, byte val)
        {
            byte mask = 0xf;
            if (higher4Bit)
            {
                var t = (byte)((val & mask) << 4);
                array[byteIdx] |= (byte)((val & mask) << 4);
            }
            else
            {
                var t = (byte)(val & mask);
                array[byteIdx] = (byte)(val & mask);
            }
        }

        //保存山体与相机的碰撞mesh
        void SaveCameraCollider(BinaryWriter writer)
        {
            var cameraColliderLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER) as CameraColliderLayer;
            writer.Write(cameraColliderLayer != null ? true : false);
            if (cameraColliderLayer != null)
            {
                Vector3[] vertices;
                int[] indices;
                cameraColliderLayer.GetCombinedMesh(out vertices, out indices);
                Utils.WriteVector3Array(writer, vertices);
                Utils.WriteIntArray(writer, indices);
            }
        }

        void SaveLocalObstacles(BinaryWriter writer)
        {
            var obstacleManager = Map.currentMap.data.localObstacleManager;
            var obstacles = obstacleManager.prefabTileDatas;
            var tiles = obstacleManager.tiles;
            int n = 0;
            if (tiles != null)
            {
                n = tiles.Length;
            }

            writer.Write(obstacleManager.regionWidth);
            writer.Write(obstacleManager.regionHeight);
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                mIDExport.Export(writer, tiles[i]);
            }

            int obstacleCount = 0;
            if (obstacles != null)
            {
                obstacleCount = obstacles.Count;
            }
            writer.Write(obstacleCount);
            if (obstacles != null)
            {
                foreach (var p in obstacles)
                {
                    var modelTemplateID = p.Key;
                    var data = p.Value;
                    var vertices = data.vertices;
                    var triangles = data.triangles;

                    //save id
                    mIDExport.Export(writer, modelTemplateID);
                    //save vertices
                    int count = vertices.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        Utils.WriteVector3(writer, vertices[i]);
                    }
                    //save indices
                    count = triangles.Length;
                    writer.Write(count);
                    for (int i = 0; i < count; ++i)
                    {
                        writer.Write(triangles[i]);
                    }
                }
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
        }

        void SaveGlobalObstacles(BinaryWriter writer)
        {
            var obstacleManager = Map.currentMap.data.globalObstacleManager;
            var obj = obstacleManager.GetObstacleView();
            Vector3[] vertices = new Vector3[0];
            int[] indices = new int[0];
            if (obj != null)
            {
                var filter = obj.GetComponent<MeshFilter>();
                var sharedMesh = filter.sharedMesh;
                vertices = sharedMesh.vertices;
                indices = sharedMesh.triangles;
            }

            Utils.WriteString(writer, obstacleManager.obstacleMaterialPath);
            int n = vertices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, vertices[i]);
            }
            n = indices.Length;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(indices[i]);
            }
        }

        void SaveModelLODGroupManager(BinaryWriter writer, ModelLODGroupManager groupManager)
        {
            int nGroups = groupManager.groups.Count;
            writer.Write(nGroups);

            for (int i = 0; i < nGroups; ++i)
            {
                var group = groupManager.groups[i];
                mIDExport.Export(writer, group.id);

                writer.Write(group.combineModels);
                mIDExport.Export(writer, group.leaderObjectID);
                writer.Write(group.lod);
            }
        }

        IDExporter mIDExport = null;
        int majorVersion = 1;
        string mProjectFolder;
        Dictionary<string, int> mStringTableIndices = new Dictionary<string, int>();
    }

}

#endif