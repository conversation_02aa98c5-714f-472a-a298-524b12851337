﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class ActionUpdateRiverTextureData : EditorAction
    {
        public ActionUpdateRiverTextureData(int dataID, int section, Color[] newMaskTextureData)
        {
            mDataID = dataID;
            mNewTextureData = (Color[])newMaskTextureData.Clone();
            mSectionIndex = section;

            var river = Map.currentMap.FindObject(mDataID) as PolygonRiverData;
            int textureSize = river.textureSize * river.textureSize;
            mOldTextureData = new Color[textureSize];
            var colors = river.sections[section].textureData;
            for (int k = 0; k < textureSize; ++k)
            {
                mOldTextureData[k] = colors[k];
            }
        }

        bool SetTextureData(Color[] val)
        {
            var river = Map.currentMap.FindObject(mDataID) as PolygonRiverData;
            if (river == null)
            {
                return false;
            }

            int textureSize = Mathf.FloorToInt(Mathf.Sqrt(val.Length));
            river.sections[0].UpdateTextureData(val, 0, 0, textureSize, textureSize, true);
            return true;
        }

        public override bool Do()
        {
            return SetTextureData(mNewTextureData);
        }

        public override bool Undo()
        {
            return SetTextureData(mOldTextureData);
        }

        int mDataID;
        int mSectionIndex;
        Color[] mNewTextureData;
        Color[] mOldTextureData;
    }
}


#endif