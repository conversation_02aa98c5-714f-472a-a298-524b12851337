﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RuinView : MapObjectView
    {
        public RuinView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            var ruinData = data as RuinData;
            var layer = Map.currentMap.GetMapLayerByID(ruinData.layerID) as RuinLayer;
            var ruinType = layer.GetRuinSetting(ruinData.objectType);
            var model = new RuinModel(ruinData, ruinType, layerView.root.transform);
            return model;
        }

        public void ChangePrefab(RuinSetting ruinType)
        {
            if (mModel != null)
            {
                var data = Map.currentMap.FindObject(objectDataID) as RuinData;
                var model = mModel as RuinModel;
                model.UsePrefab(data, ruinType, layerView.root.transform);
            }
        }

        public void ShowText(bool show)
        {
            (mModel as RuinModel).ShowText(show);
        }

        public void SetColliderRadius(float radius)
        {
            (mModel as RuinModel).SetColliderRadius(radius);
        }

        public override void SetScale(Vector3 scale)
        {
            (mModel as RuinModel).SetScale(scale);
        }

        public void SetColor(Color color)
        {
            (mModel as RuinModel).SetColor(color);
        }

        protected override void DestroyModelImpl()
        {
            if (mModel != null)
            {
                mModel.Release();
                mModel = null;
            }
        }
        public override void SetZoom(float zoom, bool lodChanged) { }
    }
}

#endif