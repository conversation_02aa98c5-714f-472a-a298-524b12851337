﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //当地块隐藏时去掉管理的其子节点
    public class FrameActionSetObjectActive : FrameAction
    {
        public static FrameActionSetObjectActive Require(MapObjectLayerData layerData, IMapObjectData objectData, bool visible)
        {
            var act = mPool.Require();
            act.Init(layerData, objectData, visible);
            return act;
        }

        public bool visible { get { return mVisible; } set { mVisible = value; } }

        void Init(MapObjectLayerData layerData, IMapObjectData objectData, bool visible)
        {
            InitAction();
            mLayerData = layerData;
            mVisible = visible;
            mObjectData = objectData;
            mKey = MakeActionKey(objectData.GetEntityID(), FrameActionType.SetObjectActive);
        }

        protected override void DoImpl()
        {
            mLayerData.SetObjectActiveFromAction(mObjectData, mVisible, 0);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mObjectData = null;
            mPool.Release(this);
        }

        public static long MakeActionKey(int id, FrameActionType type)
        {
            return MakeKeyHelper(id, type);
        }

        public override long key { get { return mKey; } }
        public override FrameActionType type => FrameActionType.SetObjectActive;
        public override string debugInfo
        {
            get
            {
                return "FrameActionSetObjectActive";
            }
        }

        public override string name => "Set Object Active";

        MapObjectLayerData mLayerData;
        IMapObjectData mObjectData;
        bool mVisible;
        long mKey;

        static ObjectPool<FrameActionSetObjectActive> mPool = new ObjectPool<FrameActionSetObjectActive>(1000, () => new FrameActionSetObjectActive());
    }
}