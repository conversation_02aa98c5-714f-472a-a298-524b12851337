﻿ 



 
 

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class BlendTerrainLayerRenderTextures
    {
        public bool Load(string projectFolder, string texturePropertyName)
        {
            string textureFolder = $"{projectFolder}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME}";
            if (!Directory.Exists(textureFolder))
            {
                return false;
            }

            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            if (blendTerrainLayer == null)
            {
                return false;
            }

            string filePath = $"{projectFolder}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_DATA_NAME}";
            if (!File.Exists(filePath))
            {
                return false;
            }
            var bytes = File.ReadAllBytes(filePath);
            if (bytes == null)
            {
                return false;
            }

            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();
            int bigTileCount = reader.ReadInt32();

            reader.Close();

            var shader = MapModuleResourceMgr.LoadResource<Shader>(MapModule.defaultGroundBakingShader);
            CreateGameObjects(blendTerrainLayer.layerView.root.transform, blendTerrainLayer.layerData as BlendTerrainLayerData, bigTileCount, 2048, shader, texturePropertyName);

            Texture2D tex = new Texture2D(1, 1, TextureFormat.RGBA32, false);
            int n = bigTileCount * bigTileCount;
            for (int i = 0; i < n; ++i)
            {
                mBigTileDirty[i] = false;

                string texturePath = $"{textureFolder}/render_texture_{i}.png";
                if (File.Exists(texturePath))
                {
                    var texData = File.ReadAllBytes(texturePath);
                    tex.LoadImage(texData);

                    RenderTexture.active = mBigTileRenderTextures[i];
                    Graphics.Blit(tex, mBigTileRenderTextures[i]);
                }
            }

            return true;
        }
    }
}
