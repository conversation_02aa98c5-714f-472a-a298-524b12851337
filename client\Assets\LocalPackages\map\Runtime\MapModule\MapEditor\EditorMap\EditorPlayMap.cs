﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [Black]
    public class EditorPlayMap : Map
    {
        public EditorPlayMap() : base("", null, true, null)
        {
        }

        public override void LoadMapData(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter, GameObject root)
        {
            base.LoadMapData(editorData, camera, viewCenter, root);

            LoadMapLayers(editorData);
        }

        void LoadMapLayers(config.SLGMakerData data)
        {
            int layerCount = data.map.mapLayers.Length;
            for (int i = 0; i < layerCount; ++i)
            {
                var sourceLayer = data.map.mapLayers[i];
                MapLayerBase targetLayer = null;
                if (sourceLayer.GetType() == typeof(config.GridModelLayerData))
                {
                    targetLayer = LoadGridModelLayer(sourceLayer as config.GridModelLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.ModelLayerData))
                {
                    targetLayer = LoadModelLayer(sourceLayer as config.ModelLayerData);
                }
                else if (sourceLayer.GetType() == typeof(config.BlendTerrainLayerData))
                {
                    targetLayer = LoadBlendTerrainLayer(sourceLayer as config.BlendTerrainLayerData);
                }
                else
                {
                    if (sourceLayer != null)
                    {
                        Debug.Log("Unknown layer " + sourceLayer.GetType().Name);
                    }
                }
                if (targetLayer != null)
                {
                    Map.currentMap.AddMapLayer(targetLayer);
                }
            }
        }

        MapLayerBase LoadGridModelLayer(config.GridModelLayerData sourceLayer)
        {
            return null;
            //var layer = new TileModelLayer();
            //layer.Load(sourceLayer);
            //layer.asyncLoading = false;
            //return layer;
        }

        MapLayerBase LoadModelLayer(config.ModelLayerData sourceLayer)
        {
            var layer = new ModelLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            layer.asyncLoading = false;
            return layer;
        }

        BlendTerrainLayer LoadBlendTerrainLayer(config.BlendTerrainLayerData sourceLayer)
        {
            var layer = new BlendTerrainLayer(Map.currentMap);
            layer.Load(sourceLayer, null, false);
            layer.asyncLoading = false;
            return layer;
        }

        public override bool updateViewport => true;
    }
}

#endif