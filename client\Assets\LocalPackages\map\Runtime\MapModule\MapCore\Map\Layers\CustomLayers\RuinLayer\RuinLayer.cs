﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public partial class RuinLayer : ModelLayer, IOverlapObjectAtPosition
    {
        public RuinLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            base.OnDestroy();
            GameObject.DestroyImmediate(mGridObject);
            mGridObject = null;
            SetDirty();

            Cleanup();
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                Object.DestroyImmediate(mRuinObjectTypes[i].mtl);
            }

            Object.DestroyImmediate(mShowRegionsWithNoRuinPointRoot);
            Object.DestroyImmediate(mRegionMaterial);
        }

        void Cleanup()
        {
            for (int i = 0; i < mHordeObjects.Count; ++i)
            {
                GameObject.DestroyImmediate(mHordeObjects[i]);
                GameObject.DestroyImmediate(mHordeMaterials[i]);
            }
            mHordeObjects.Clear();
            mHordeMaterials.Clear();
            GameObject.DestroyImmediate(mHordeMesh);
            mHordeMesh = null;
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.RuinLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            mLayerData = new QuadTreeObjectLayerData(header, null, map, null, null);
            mLayerView = new RuinLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mShareProperties = sourceLayer.shareProperties;
            mCellWidth = sourceLayer.cellWidth;
            mShowText = sourceLayer.textVisible;
            mRuinDisplayRadius = sourceLayer.displayRadius;

            mRuinObjectTypes = CreateRuinSetting(sourceLayer.ruinObjectTypes);

            mLayerData.isLoading = true;
            if (sourceLayer.objects != null)
            {
                int n = sourceLayer.objects.Length;
                for (int i = 0; i < n; ++i)
                {
                    var model = sourceLayer.objects[i] as config.RuinData;
                    var ruinSetting = GetRuinSetting(model.objectType);
                    string prefabPath = AssetDatabase.GetAssetPath(ruinSetting.prefab);
                    var modelTemplate = map.GetOrCreateModelTemplate(model.id, prefabPath, false, false);

                    model.modelTemplateID = modelTemplate == null ? 0 : modelTemplate.id;
                    var modelData = new RuinData(model.id, mLayerData.id, map, 0, model.position, model.rotation, model.scale, modelTemplate, (RuinType)model.type, model.level, model.objectType, Color.black, model.properties == null ? ruinSetting.properties : model.properties);
                    mLayerData.AddObjectData(modelData);
                }
            }
            mLayerData.isLoading = false;
            mLayerView.root.transform.position = sourceLayer.origin;
            mExportInHierarchyOrder = sourceLayer.exportInHierarchyOrder;

            map.AddMapLayer(this);

            SetDirty();

            CreateGrid();
            SetHordeCount(sourceLayer.hordeCount);
        }

        List<RuinSetting> CreateRuinSetting(List<config.RuinSetting> ruinSettings)
        {
            List<RuinSetting> settings = new List<RuinSetting>();
            for (int i = 0; i < ruinSettings.Count; ++i)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(ruinSettings[i].prefabGUID);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                if (prefab == null && !string.IsNullOrEmpty(ruinSettings[i].prefabGUID))
                {
                    Debug.LogError($"ruin prefab {assetPath} not found!");
                }
                settings.Add(new RuinSetting(ruinSettings[i].name, ruinSettings[i].color, new Material(Shader.Find("SLGMaker/Color")), false, ruinSettings[i].properties, ruinSettings[i].colliderRadius, prefab, ruinSettings[i].regionSetting));
            }
            return settings;
        }

        public void ExportConfigs()
        {
            Debug.Assert(false, "todo");
        }

        void CreateGrid()
        {
            if (mGridObject != null)
            {
                GameObject.DestroyImmediate(mGridObject);
                mGridObject = null;
            }
            var gridCreator = new MapLayerXZRectangleGridCreator(mLayerData.GetLayerWidthInMeter(), mLayerData.GetLayerHeightInMeter(), mCellWidth, mCellWidth);
            mGridObject = gridCreator.CreateGrid(Color.yellow);
            mGridObject.SetActive(false);
            Utils.HideGameObject(mGridObject);
            mGridObject.transform.SetParent(mLayerView.root.transform);
            mGridObject.transform.position = new Vector3(0, 0.3f, 0);
        }

        GameObject CreateHorde(int hordeCount, int idx, Material mtl, Vector3 position, Quaternion rotation)
        {
            var gameObject = GameObject.CreatePrimitive(PrimitiveType.Cube);
            Utils.HideGameObject(gameObject);
            if (mHordeMesh == null)
            {
                mHordeMesh = CreateHordeMesh(hordeCount);
            }
            var meshFilter = gameObject.GetComponent<MeshFilter>();
            meshFilter.sharedMesh = mHordeMesh;
            var meshRenderer = gameObject.GetComponent<MeshRenderer>();
            meshRenderer.sharedMaterial = mtl;
            gameObject.transform.position = position;
            gameObject.transform.rotation = rotation;
            gameObject.transform.SetParent(mLayerView.root.transform);

            var textObj = Utils.CreateTextGameObject("text", idx.ToString(), Color.red);
            textObj.transform.SetParent(gameObject.transform);
            textObj.transform.localPosition = mHordeMeshCenter;
            textObj.transform.localScale = Vector3.one * map.mapWidth * 0.01f;

            gameObject.SetActive(false);

            return gameObject;
        }

        Mesh CreateHordeMesh(int hordeCount)
        {
            float angle = 360 / (int)hordeCount;
            int segment = 10;
            int sideVertex = segment + 1;
            float deltaAngle = angle / segment;
            int vertexCount = sideVertex + 1;
            int indexCount = segment * 3;
            float radius = map.mapWidth * 0.5f;
            Vector3[] vertices = new Vector3[vertexCount];
            vertices[0] = Vector3.zero;

            mHordeMeshCenter = Vector3.zero;
            for (int i = 1; i <= sideVertex; ++i)
            {
                float x = -Mathf.Cos((i - 1) * deltaAngle * Mathf.Deg2Rad) * radius;
                float z = Mathf.Sin((i - 1) * deltaAngle * Mathf.Deg2Rad) * radius;
                float y = 0;
                vertices[i] = new Vector3(x, y, z);

                mHordeMeshCenter += vertices[i];
            }

            mHordeMeshCenter /= vertexCount;

            int[] indices = new int[indexCount];
            for (int i = 0; i < segment; ++i)
            {
                indices[i * 3] = 0;
                indices[i * 3 + 1] = i + 1;
                indices[i * 3 + 2] = i + 2;
            }

            Mesh mesh = new Mesh();
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        Material CreateHordeMaterial(Color color)
        {
            color = new Color(color.r, color.g, color.b, 0.3f);
            var mtl = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            mtl.color = color;
            return mtl;
        }

        public void SetHordeCount(int n)
        {
            if (hordeCount != n)
            {
                if (hordeCount > 0)
                {
                    RemoveAllObjects();
                }
                Cleanup();

                var pos = new Vector3(map.mapWidth * 0.5f, 0, map.mapHeight * 0.5f);
                float angle = 360 / (float)n;
                for (int i = 0; i < n; ++i)
                {
                    var mtl = CreateHordeMaterial(UnityEngine.Random.ColorHSV(0, 1, 0.4f, 1, 0.5f, 1));
                    var rotation = Quaternion.Euler(new Vector3(0, (i - 0.5f) * angle, 0));
                    var hordeGameObject = CreateHorde(n, i, mtl, pos, rotation);
                    mHordeObjects.Add(hordeGameObject);
                    mHordeMaterials.Add(mtl);
                }
            }
        }

        void SetDirty()
        {
#if UNITY_EDITOR
            EditorConfig.dirtyFlag |= DirtyMask.RuinConfig;
#endif
        }

        public void SetVisible(bool visible)
        {
            if (mGridObject != null)
            {
                mGridObject.SetActive(visible);
            }
        }

        public int GetHordeIndex(Vector3 pos)
        {
            var posToCenter = pos - map.center;
            posToCenter.Normalize();
            float angle = 360.0f / mHordeObjects.Count;
            Vector3 startDir = Quaternion.Euler(0, -angle * 0.5f, 0) * Vector3.left;
            var rot = Quaternion.FromToRotation(startDir, posToCenter);

            int horde = Mathf.FloorToInt(rot.eulerAngles.y / angle);
            return horde;
        }

        public void RemoveObjectsInHorde(int hordeIndex, string objectType)
        {
            var objects = GetObjectsInsideHorde(hordeIndex, objectType);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }
        }

        public List<IMapObjectData> GetObjectsInsideHorde(int hordeIndex, string objectType)
        {
            if (mHordeObjects.Count == 0)
            {
                return null;
            }

            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            layerData.GetAllObjects(allObjects);

            List<IMapObjectData> objectsInHorde = new List<IMapObjectData>();
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var ruinObject = allObjects[i] as RuinData;
                if (ruinObject.objectType == objectType)
                {
                    var pos = ruinObject.GetPosition();
                    var objectHordeIndex = GetHordeIndex(pos);
                    if (objectHordeIndex == hordeIndex)
                    {
                        objectsInHorde.Add(allObjects[i]);
                    }
                }
            }
            return objectsInHorde;
        }

        public float GetHordeAngle(int idx)
        {
            float angle = 360.0f / mHordeObjects.Count;

            var startEulerAngle = Quaternion.LookRotation(Vector3.left).eulerAngles.y - angle * 0.5f;
            return startEulerAngle + angle * idx;
        }

        public void ShowHorde(bool show)
        {
            for (int i = 0; i < mHordeObjects.Count; ++i)
            {
                mHordeObjects[i].SetActive(show);
            }
        }

        public void ShowText(bool showText)
        {
            if (mShowText != showText)
            {
                mShowText = showText;
                List<IMapObjectData> allObjects = new List<IMapObjectData>();
                GetAllObjects(allObjects);
                for (int i = 0; i < allObjects.Count; ++i)
                {
                    var view = mLayerView.GetObjectView(allObjects[i].GetEntityID()) as RuinView;
                    if (view != null)
                    {
                        view.ShowText(showText);
                    }
                }
            }
        }

        public void SetColliderRadius(float radius)
        {
            var objects = layerView.allViews;
            foreach (var p in objects)
            {
                var view = p.Value as RuinView;
                if (view != null)
                {
                    view.SetColliderRadius(radius);
                }
            }
        }

        public bool IsTextVisible()
        {
            return mShowText;
        }

        public bool IsHordeVisible()
        {
            if (mHordeObjects.Count == 0)
            {
                return false;
            }
            return mHordeObjects[0].activeSelf;
        }

        public void ShowObjectsOfType(string objectType)
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);

            RuinSetting ruinSetting = GetRuinSetting(objectType);
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                var objRuinSetting = GetRuinSetting(ruinData.objectType);
                if (ruinData.objectType == objectType)
                {
                    ShowObject(ruinData.id, ruinSetting.color);
                }
                else if (objRuinSetting.alwaysDisplay == false)
                {
                    HideObject(ruinData.id);
                }
            }
        }

        public void ShowAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                RuinSetting ruinSetting = GetRuinSetting(ruinData.objectType);
                ShowObject(ruinData.id, ruinSetting.color);
            }
        }

        public void ShowObject(int objectDataID, Color color)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, mLayerData.currentLOD);
                SetColor(objData as RuinData, color);
            }
        }

        void SetColor(RuinData data, Color color)
        {
            data.color = color;
            var view = mLayerView.GetObjectView(data.id) as RuinView;
            if (view != null)
            {
                view.SetColor(color);
            }
        }

        public List<IMapObjectData> GetObjectsOfType(string objectType)
        {
            List<IMapObjectData> objectsOfType = new List<IMapObjectData>();
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                if (ruinData.objectType == objectType)
                {
                    objectsOfType.Add(objects[i]);
                }
            }
            return objectsOfType;
        }

        public void RemoveAllObjectsOfType(string objectType)
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                if (ruinData.objectType == objectType)
                {
                    RemoveObject(objects[i].GetEntityID());
                }
            }
        }

        //找到同一个坐标下的所有物体,只保留其中一个
        public void RemoveDuplicatedObjects()
        {
            List<IMapObjectData> removedObjects = new List<IMapObjectData>();

            float mapWidth = map.mapWidth;
            float mapHeight = map.mapHeight;
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var obj0 = objects[i] as RuinData;
                for (int j = 0; j < objects.Count; ++j)
                {
                    if (i != j)
                    {
                        var obj1 = objects[j] as RuinData;
                        var pos = obj1.GetPosition();
                        bool inRange = pos.x >= 0 && pos.x <= mapWidth && pos.z >= 0 && pos.z <= mapHeight;
                        if ((obj0.objectType == obj1.objectType && obj0.GetPosition() == obj1.GetPosition()) ||
                            !inRange)
                        {
                            if (!removedObjects.Contains(objects[j]))
                            {
                                removedObjects.Add(objects[j]);
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < removedObjects.Count; ++i)
            {
                RemoveObject(removedObjects[i].GetEntityID());
            }
        }

        public override void RefreshObjectsInViewport()
        {
            base.RefreshObjectsInViewport();

            var editorMapData = map.data as EditorMapData;
            if (mRuinObjectTypes.Count > 0)
            {
                var objectType = mRuinObjectTypes[mRuinObjectTypes.Count - 1].name;
                ShowObjectsOfType(objectType);
            }
        }

        public bool OverlapObjectAtPosition(Vector3 pos, float radius)
        {
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var obj = p.Value;
                var ruinData = obj as RuinData;
                var objPos = ruinData.GetPosition();
                if (Utils.IsCircleCircleOverlap(pos.x, pos.z, radius, objPos.x, objPos.z, ruinData.radius))
                {
                    return true;
                }
            }
            return false;
        }


        public int hordeCount { get { return mHordeObjects.Count; } }
        public float cellWidth { get { return mCellWidth; } set { mCellWidth = value; } }
        public float ruinDisplayRadius
        {
            get { return mRuinDisplayRadius; }
            set
            {
                if (mRuinDisplayRadius != value)
                {
                    mRuinDisplayRadius = value;
                    List<IMapObjectData> objects = new List<IMapObjectData>();
                    mLayerData.GetAllObjects(objects);
                    for (int i = 0; i < objects.Count; ++i)
                    {
                        bool isActive = objects[i].IsObjActive();
                        mLayerData.SetObjectScale(objects[i].GetEntityID(), Vector3.one * mRuinDisplayRadius * 2f);
                        mLayerData.SetObjectActive(objects[i], isActive, 0);
                    }
                }
            }
        }

        List<string> CreateHeaders(PropertyDatas properties)
        {
            var headers = new List<string> {
                "S_INT_id",
                "S_STR_type",
                "S_MAP_position",
                "S_FLT_Rotation",
                "S_INT_RegionID",
                "S_FLT_radius",
            };

            if (mShareProperties)
            {
                int propCount = properties.GetPropertyCount();
                for (int i = 0; i < propCount; ++i)
                {
                    var prop = properties.GetProperty(i);
                    string typeName = Utils.ConvertToServerTypeName(prop.type);
                    headers.Add($"S_{typeName}_{prop.name}");
                }
            }
            else
            {
                headers.Add($"S_STR_CustomProperties");
            }

            return headers;
        }

        public void ImportData(string filePath)
        {
            bool suc = TSVReader.Load(filePath);
            if (suc)
            {
                ActionManager.instance.Clear();
                RemoveAllObjects();

                var rows = TSVReader.rows;
                for (int i = 0; i < rows.Count; ++i)
                {
                    bool get;
                    string type = TSVReader.GetString(i, "type", out get);
                    var pos = TSVReader.GetCoord(i, "position", out get);
                    float x = pos.x / 1000f;
                    float z = pos.y / 1000f;
                    float rotation = (float)TSVReader.GetFloat(i, "Rotation", out get);

                    var ruinSetting = GetRuinSetting(type);

                    string prefabPath = null;
                    if (ruinSetting.prefab == null)
                    {
                        if (ruinType == RuinType.Normal)
                        {
                            prefabPath = MapCoreDef.SPHERE_OBJECT_NAME;
                        }
                        else
                        {
                            Debug.Assert(false, "unknown ruin type!");
                        }
                    }
                    else
                    {
                        prefabPath = AssetDatabase.GetAssetPath(ruinSetting.prefab);
                    }

                    if (prefabPath != null)
                    {
                        var modelTemplate = map.GetOrCreateModelTemplate(0, prefabPath, false, false);

                        var act = new ActionAddRuin(mLayerData.id, map.nextCustomObjectID, modelTemplate.id, new Vector3(x, 0, z), Quaternion.Euler
                            (0, rotation, 0), Vector3.one * ruinDisplayRadius * 2, ruinType, level, ruinSetting.name, ruinSetting.color, null, false);
                        ActionManager.instance.PushAction(act, false, true);
                    }
                }
            }
        }

        //strictOrderInHierarchy:是否按照GameObject在Hierarchy中的顺序导出
        public void ExportData(List<RuinSetting> ruinSettings, string filepath, int startID)
        {
            if (ruinSettings == null || ruinSettings.Count == 0)
            {
                return;
            }

            StringBuilder builder = new StringBuilder();
            List<IMapObjectData> allRuins = new List<IMapObjectData>();
            if (mExportInHierarchyOrder)
            {
                List<IMapObjectData> objects = new List<IMapObjectData>();
                GetAllObjects(objects);
                SortObjectByChildrenOrder(objects);
                allRuins.AddRange(objects);
            }
            else 
            {
                for (int i = 0; i < ruinSettings.Count; ++i)
                {
                    var objects = GetObjectsOfType(ruinSettings[i].name);
                    SortObjectByChildrenOrder(objects);
                    allRuins.AddRange(objects);
                }
            }

            PropertyDatas properties = ruinSettings[0].properties;
            List<string> headers = CreateHeaders(properties);

            for (int i = 0; i < headers.Count; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Count - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            var navMeshLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;

            for (int i = 0; i < allRuins.Count; ++i)
            {
                var ruin = allRuins[i] as RuinData;
                var pos = ruin.GetPosition();

                builder.AppendFormat("{0}\t", startID);
                ++startID;

                builder.AppendFormat("{0}\t", ruin.objectType);

                var s = string.Format("\"x\":{0},\"z\":{1}", Utils.F2I(pos.x), Utils.F2I(pos.z));
                s = "{" + s + "}\t";
                builder.Append(s);

                var rotation = ruin.GetRotation();
                builder.AppendFormat("{0}\t", rotation.eulerAngles.y);

                properties = ruin.properties;
                int propCount = properties.GetPropertyCount();

                //region id
                int regionID = 0;
                if (navMeshLayer != null)
                {
                    regionID = navMeshLayer.GetNavMeshRegionID(pos);
                }
                builder.AppendFormat("{0}\t", regionID);

                builder.AppendFormat("{0}{1}", ruin.radius, propCount == 0 ? '\n' : '\t');

                if (mShareProperties)
                {
                    for (int p = 0; p < propCount; ++p)
                    {
                        var property = properties.GetProperty(p);
                        switch (property.type)
                        {
                            case PropertyType.kPropertyFloat:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<float>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyInt:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<int>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyIntArray:
                                builder.AppendFormat("{0}{1}", Utils.IntArrayToStringList((property as PropertyData<int[]>).value), p == propCount - 1 ? '\n' : '\t');
                                break;
                            case PropertyType.kPropertyString:
                                builder.AppendFormat("{0}{1}", (property as PropertyData<string>).value.ToString(), p == propCount - 1 ? '\n' : '\t');
                                break;
                            default:
                                Debug.Assert(false, "not supported now!");
                                break;
                        }
                    }
                }
                else
                {
                    var customPropertiesStr = properties.ConvertToJsonString();
                    builder.AppendFormat("{0}\n", customPropertiesStr);
                }
            }

            var str = builder.ToString();
            File.WriteAllText(filepath, str);
        }

        public void ExportRuinDataForClient(List<RuinSetting> ruinSettings, string filepath)
        {
            if (ruinSettings == null || ruinSettings.Count == 0)
            {
                return;
            }

            List<IMapObjectData> allRuins = new List<IMapObjectData>();
            for (int i = 0; i < ruinSettings.Count; ++i)
            {
                var objects = GetObjectsOfType(ruinSettings[i].name);
                SortObjectByChildrenOrder(objects);
                allRuins.AddRange(objects);   
            }

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(allRuins.Count);
            for (int i = 0; i < allRuins.Count; ++i)
            {
                var ruin = allRuins[i] as RuinData;
                
                Utils.WriteString(writer, ruin.objectType);
                Utils.WriteVector3(writer, ruin.GetPosition());
                Utils.WriteQuaternion(writer, ruin.GetRotation());
                writer.Write(ruin.radius);
            }

            File.WriteAllBytes(filepath, stream.ToArray());
            writer.Close();
        }

        public RuinSetting AddRuinObjectType(string type, Color color, PropertyDatas properties, float colliderRadius, config.RuinSpecialRegionSetting setting)
        {
            Material mtl = new Material(Shader.Find("SLGMaker/Color"));
            mtl.color = color;
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                if (mRuinObjectTypes[i].name == type)
                {
                    return null;
                }
            }

            var ruin = new RuinSetting(type, color, mtl, false, properties, colliderRadius, null, setting);
            mRuinObjectTypes.Add(ruin);
            return ruin;
        }

        public void RemoveRuinObjectType(string type)
        {
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                if (mRuinObjectTypes[i].name == type)
                {
                    mRuinObjectTypes.RemoveAt(i);
                    break;
                }
            }
        }

        public bool ExistsRuinObjectType(string type)
        {
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                if (mRuinObjectTypes[i].name == type)
                {
                    return true;
                }
            }
            return false;
        }

        public RuinSetting GetRuinSetting(string type)
        {
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                if (mRuinObjectTypes[i].name == type)
                {
                    return mRuinObjectTypes[i];
                }
            }
            return null;
        }

        public int GetRuinSettingIndex(string type)
        {
            for (int i = 0; i < mRuinObjectTypes.Count; ++i)
            {
                if (mRuinObjectTypes[i].name == type)
                {
                    return i;
                }
            }
            return -1;
        }

        public void OnRuinPrefabChanged(string name, GameObject newPrefab)
        {
            var setting = GetRuinSetting(name);
            Debug.Assert(setting != null);
            setting.prefab = newPrefab;

            var ruinLayerView = mLayerView as RuinLayerView;
            ruinLayerView.ChangePrefab(setting);
        }

        //更新transform被改变后的物体
        public void UpdateTransformChangedObjects()
        {
            var objInfos = (mLayerView as RuinLayerView).GetTransformChangedObjectIDs();
            UpdateTransformChangedObjects(objInfos);
        }

        void UpdateTransformChangedObjects(List<ObjectTransformInfo> objectInfos)
        {
            for (int i = 0; i < objectInfos.Count; ++i)
            {
                var obj = mLayerData.GetObjectData(objectInfos[i].objectID);
                obj.SetPosition(objectInfos[i].position);
                obj.SetRotation(objectInfos[i].rotation);
            }
            if (objectInfos.Count > 0)
            {
                ActionManager.instance.Clear();
            }
        }

        //更新待删除的物体
        public void UpdateDestroyedObjects()
        {
            bool removed = false;
            for (int i = 0; i < mDestroyedObjectIDs.Count; ++i)
            {
                RemoveObject(mDestroyedObjectIDs[i]);
                removed = true;
            }
            mDestroyedObjectIDs.Clear();
            if (removed)
            {
                ActionManager.instance.Clear();
            }
        }

        public void AddDestroyedObject(int objectID)
        {
            mDestroyedObjectIDs.Add(objectID);
        }

        public void AddObject(string objectType, Vector3 worldPos, CompoundAction actions, bool checkSamePos, bool selectRuin)
        {
            var ruinSetting = GetRuinSetting(objectType);

            string prefabPath = null;
            if (ruinSetting.prefab == null)
            {
                if (ruinType == RuinType.Normal)
                {
                    prefabPath = MapCoreDef.SPHERE_OBJECT_NAME;
                }
                else
                {
                    Debug.Assert(false, "unknown ruin type!");
                }
            }
            else
            {
                prefabPath = AssetDatabase.GetAssetPath(ruinSetting.prefab);
            }

            if (prefabPath != null)
            {
                var modelTemplate = map.GetOrCreateModelTemplate(0, prefabPath, false, false);
                if (modelTemplate != null)
                {
                    if (!checkSamePos || FindObjectAtExactSamePosition(worldPos) == null)
                    {
                        PropertyDatas objectProperties = ruinSetting.properties;
                        if (!mShareProperties)
                        {
                            objectProperties = objectProperties.Clone();
                        }

                        var act = new ActionAddRuin(mLayerData.id, map.nextCustomObjectID, modelTemplate.id, worldPos, Quaternion.identity, Vector3.one * ruinDisplayRadius * 2, ruinType, level, ruinSetting.name, ruinSetting.color, objectProperties, selectRuin);
                        if (actions == null)
                        {
                            ActionManager.instance.PushAction(act);
                        }
                        else
                        {
                            actions.Add(act);
                        }
                    }
                }
            }
        }

        public void HideRegionMarks()
        {
            if (mShowRegionsWithNoRuinPointRoot != null)
            {
                mShowRegionsWithNoRuinPointRoot.SetActive(false);
            }
        }

        public List<int> GetEmptyRegions(string objectType, float regionWidth, float regionHeight)
        {
            List<int> emptyRegions = new List<int>();
            int horizontalCount = Mathf.CeilToInt(layerData.GetLayerWidthInMeter() / regionWidth);
            int verticalCount = Mathf.CeilToInt(layerData.GetLayerHeightInMeter() / regionHeight);

            bool[,] marks = new bool[verticalCount, horizontalCount];
            var objects = GetObjectsOfType(objectType);
            for (int k = 0; k < objects.Count; ++k)
            {
                var pos = objects[k].GetPosition();
                int col = Mathf.FloorToInt(pos.x / regionWidth);
                int row = Mathf.FloorToInt(pos.z / regionHeight);
                if (col >= 0 && col < horizontalCount && row >= 0 && row < verticalCount)
                {
                    marks[row, col] = true;
                }
            }

            for (int i = 0; i < verticalCount; ++i)
            {
                for (int j = 0; j < horizontalCount; ++j)
                {
                    if (marks[i, j] == false)
                    {
                        emptyRegions.Add(i * horizontalCount + j);
                    }
                }
            }

            return emptyRegions;
        }

        public void ShowRegionWithNoRuinPoint(string objectType, float regionWidth, float regionHeight)
        {
            if (regionWidth > 0 && regionHeight > 0)
            {
                Utils.DestroyObject(mShowRegionsWithNoRuinPointRoot);
                mShowRegionsWithNoRuinPointRoot = new GameObject();
                Utils.HideGameObject(mShowRegionsWithNoRuinPointRoot);

                if (mRegionMaterial == null)
                {
                    mRegionMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                    mRegionMaterial.SetColor("_Color", new Color(0.5f, 1.0f, 0.2f, 0.5f));
                }

                int horizontalCount = Mathf.CeilToInt(layerData.GetLayerWidthInMeter() / regionWidth);
                int verticalCount = Mathf.CeilToInt(layerData.GetLayerHeightInMeter() / regionHeight);

                bool[,] marks = new bool[verticalCount, horizontalCount];
                var objects = GetObjectsOfType(objectType);
                for (int k = 0; k < objects.Count; ++k)
                {
                    var pos = objects[k].GetPosition();
                    int col = Mathf.FloorToInt(pos.x / regionWidth);
                    int row = Mathf.FloorToInt(pos.z / regionHeight);
                    if (col >= 0 && col < horizontalCount && row >= 0 && row < verticalCount)
                    {
                        marks[row, col] = true;
                    }
                }

                for (int i = 0; i < verticalCount; ++i)
                {
                    for (int j = 0; j < horizontalCount; ++j)
                    {
                        if (marks[i, j] == false)
                        {
                            GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Plane);
                            obj.transform.localScale = new Vector3(regionWidth, 1, regionHeight) * 0.1f;
                            var renderer = obj.GetComponent<MeshRenderer>();
                            renderer.sharedMaterial = mRegionMaterial;
                            obj.transform.position = new Vector3((j + 0.5f) * regionWidth, 0, (i + 0.5f) * regionHeight);
                            obj.transform.parent = mShowRegionsWithNoRuinPointRoot.transform;
                        }
                    }
                }
            }
        }

        public void ResizeInvalidCircleList(string objectType, int newCount)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                if (newCount != setting.invalidCircles.Count)
                {
                    int delta = newCount - setting.invalidCircles.Count;
                    if (delta > 0)
                    {
                        for (int i = 0; i < delta; ++i)
                        {
                            setting.invalidCircles.Add(-1);
                        }
                    }
                    else
                    {
                        delta = -delta;
                        for (int i = 0; i < delta; ++i)
                        {
                            setting.invalidCircles.RemoveAt(setting.invalidCircles.Count - 1);
                        }
                    }
                }
            }
        }

        public float GetBigGridWidth(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null) { 
                return setting.bigGridWidth;
            }
            return 0;
        }

        public void SetBigGridWidth(string objectType, float width)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.bigGridWidth = width;
            }
        }

        public float GetBigGridHeight(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.bigGridHeight;
            }
            return 0;
        }

        public void SetBigGridHeight(string objectType, float height)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.bigGridHeight = height;
            }
        }

        public float GetStartX(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.startX;
            }
            return 0;
        }

        public void SetStartX(string objectType, float x)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.startX = x;
            }
        }

        public float GetStartZ(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.startZ;
            }
            return 0;
        }

        public void SetStartZ(string objectType, float z)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.startZ = z;
            }
        }

        public int GetPointCount(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.pointCount;
            }
            return 0;
        }

        public void SetPointCount(string objectType, int n)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.pointCount= n;
            }
        }

        public int GetHorizontalGridCount(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.horizontalBigGridCount;
            }
            return 0;
        }

        public void SetHorizontalGridCount(string objectType, int n)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.horizontalBigGridCount = n;
            }
        }

        public int GetVerticalGridCount(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.verticalBigGridCount;
            }
            return 0;
        }

        public void SetVerticalGridCount(string objectType, int n)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                setting.verticalBigGridCount = n;
            }
        }

        public List<int> GetInvalidCircles(string objectType)
        {
            var setting = GetRuinSetting(objectType);
            if (setting != null)
            {
                return setting.invalidCircles;
            }
            return null;
        }

        void SortObjectByChildrenOrder(List<IMapObjectData> ruins)
        {
            ruins.Sort((IMapObjectData a, IMapObjectData b) =>
            {
                var viewA = mLayerView.GetObjectView(a.GetEntityID());
                var viewB = mLayerView.GetObjectView(a.GetEntityID());
                int indexA = viewA.transform.GetSiblingIndex();
                int indexB = viewB.transform.GetSiblingIndex();
                return indexA - indexB;
            });
        }

        public List<RuinSetting> ruinObjectTypes { get { return mRuinObjectTypes; } }
        public bool shareProperties { get { return mShareProperties; } }
        public bool exportInHierarchyOrder { get { return mExportInHierarchyOrder; } set { mExportInHierarchyOrder = value; } }

        float mCellWidth = 120.0f;
        float mRuinDisplayRadius = 50.0f;
        //是否object和ruin type共享同一个propertydatas
        bool mShareProperties;
        bool mExportInHierarchyOrder = false;

        GameObject mGridObject;
        List<GameObject> mHordeObjects = new List<GameObject>();
        List<Material> mHordeMaterials = new List<Material>();
        List<GameObject> mRuinObjectLevels = new List<GameObject>();
        Mesh mHordeMesh;
        Vector3 mHordeMeshCenter;
        bool mShowText = false;
        List<RuinSetting> mRuinObjectTypes;
        List<int> mDestroyedObjectIDs = new List<int>();
        GameObject mShowRegionsWithNoRuinPointRoot;
        Material mRegionMaterial;

        //editor variables
        public RuinType ruinType = RuinType.Normal;
        public int level = 1;
        public int selectedObjectTypeIndex;
        public bool exportAsGridData = false;
    }
}

#endif