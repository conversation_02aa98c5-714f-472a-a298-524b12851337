fileFormatVersion: 2
guid: 48c1f37bca6845a4a99e33c6725ca4a8
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 3172466048368850041
    second: ruin_0
  - first:
      213: 769690925915973973
    second: ruin_1
  - first:
      213: 4129164301437084405
    second: ruin_2
  - first:
      213: -329226578863359943
    second: ruin_3
  - first:
      213: -3913339079254701058
    second: ruin_4
  - first:
      213: -5955391173547098061
    second: ruin_5
  - first:
      213: 161973610554796631
    second: ruin_6
  - first:
      213: -7100641044384481733
    second: ruin_7
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 51
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: ruin_0
      rect:
        serializedVersion: 2
        x: 70
        y: 1019
        width: 163
        height: 239
      alignment: 9
      pivot: {x: 0.5282729, y: 0.114351794}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9743303001dd60c20800000000000000
      internalID: 3172466048368850041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_1
      rect:
        serializedVersion: 2
        x: 271
        y: 1021
        width: 143
        height: 195
      alignment: 9
      pivot: {x: 0.4806636, y: 0.1455003}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5592012b1dd7eaa00800000000000000
      internalID: 769690925915973973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_2
      rect:
        serializedVersion: 2
        x: 538
        y: 1009
        width: 307
        height: 221
      alignment: 9
      pivot: {x: 0.4429566, y: 0.23725332}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5f2a16e0e0dbd4930800000000000000
      internalID: 4129164301437084405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_3
      rect:
        serializedVersion: 2
        x: 41
        y: 521
        width: 272
        height: 484
      alignment: 9
      pivot: {x: 0.5108603, y: 0.16431673}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9387b3cbb2a5e6bf0800000000000000
      internalID: -329226578863359943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_4
      rect:
        serializedVersion: 2
        x: 355
        y: 545
        width: 218
        height: 335
      alignment: 9
      pivot: {x: 0.52032566, y: 0.12964614}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: efb8e5757d601b9c0800000000000000
      internalID: -3913339079254701058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_5
      rect:
        serializedVersion: 2
        x: 623
        y: 538
        width: 258
        height: 457
      alignment: 9
      pivot: {x: 0.5189628, y: 0.13868791}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3308a3ad7433a5da0800000000000000
      internalID: -5955391173547098061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_6
      rect:
        serializedVersion: 2
        x: 1432
        y: 939
        width: 474
        height: 335
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 75a2b1004227f3200800000000000000
      internalID: 161973610554796631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ruin_7
      rect:
        serializedVersion: 2
        x: 52
        y: 294
        width: 505
        height: 216
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3e77e74794757d90800000000000000
      internalID: -7100641044384481733
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5674e864d73ee4843abb1f72606718fe
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      ruin_0: 3172466048368850041
      ruin_1: 769690925915973973
      ruin_2: 4129164301437084405
      ruin_3: -329226578863359943
      ruin_4: -3913339079254701058
      ruin_5: -5955391173547098061
      ruin_6: 161973610554796631
      ruin_7: -7100641044384481733
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
