{"skeleton": {"hash": "R78fZl8q+OM", "spine": "4.2.33", "x": -409.7, "y": -58.83, "width": 719.18, "height": 1567.19, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 298.78, "y": 915.96}, {"name": "ALL2", "parent": "ALL", "x": -256.32, "y": -19.47, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 110.38, "rotation": 80.42, "x": 1.65, "y": 8.17}, {"name": "body2", "parent": "body", "length": 245.56, "rotation": 105.42, "x": 110.38, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 89.2, "rotation": 92.62, "x": 245.56, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 153.24, "rotation": 9.63, "x": 89.2}, {"name": "sh_L", "parent": "body2", "x": 178.86, "y": -100.06, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 259.4, "y": 107.32, "inherit": "noScale"}, {"name": "arm_R", "parent": "sh_R", "length": 225.93, "rotation": -107.53, "x": -8.48, "y": 12.73, "inherit": "noRotationOrReflection"}, {"name": "arm_R2", "parent": "arm_R", "length": 213.42, "rotation": -8.47, "x": 225.93, "inherit": "noScale"}, {"name": "arm_R3", "parent": "arm_R2", "length": 106.17, "rotation": 5.14, "x": 213.42}, {"name": "line_R", "parent": "arm_R3", "length": 66.07, "rotation": 51.59, "x": 95.63, "y": 28.85, "color": "abe323ff"}, {"name": "line_R2", "parent": "line_R", "length": 70.6, "rotation": -16.9, "x": 66.07, "color": "abe323ff"}, {"name": "line_R3", "parent": "line_R2", "length": 61.61, "rotation": -18.6, "x": 70.6, "color": "abe323ff"}, {"name": "line_R4", "parent": "line_R3", "length": 67.54, "rotation": -11.06, "x": 61.61, "color": "abe323ff"}, {"name": "arm_L", "parent": "sh_L", "length": 260.8, "rotation": -84.18, "x": -14.25, "y": 5.72, "inherit": "noRotationOrReflection"}, {"name": "arm_L2", "parent": "arm_L", "length": 221.61, "rotation": 13.52, "x": 260.8, "inherit": "noScale"}, {"name": "arm_L3", "parent": "arm_L2", "length": 93.13, "rotation": 44.56, "x": 221.61}, {"name": "hair", "parent": "head", "x": 114.25, "y": -23.24}, {"name": "hair_L", "parent": "hair", "length": 27.78, "rotation": -138.47, "x": 13.46, "y": -29.61}, {"name": "hair_L2", "parent": "hair_L", "length": 42.84, "rotation": -24.11, "x": 27.78, "color": "abe323ff"}, {"name": "hair_L3", "parent": "hair_L2", "length": 49.75, "rotation": -9.93, "x": 42.84, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 49.28, "rotation": 1.19, "x": 49.75, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 42.44, "rotation": -22.56, "x": 49.28, "color": "abe323ff"}, {"name": "hair_L6", "parent": "hair_L5", "length": 33.41, "rotation": -32.64, "x": 42.44, "color": "abe323ff"}, {"name": "hair_L7", "parent": "hair_L6", "length": 31.64, "rotation": -37.3, "x": 33.41, "color": "abe323ff"}, {"name": "hair_R", "parent": "hair", "length": 36.19, "rotation": 129.47, "x": 20.77, "y": 31.74}, {"name": "hair_R2", "parent": "hair_R", "length": 46.4, "rotation": 23.27, "x": 36.19, "color": "abe323ff"}, {"name": "hair_R3", "parent": "hair_R2", "length": 56.45, "rotation": -4.47, "x": 46.4, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 44.82, "rotation": 19.46, "x": 56.45, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 44.89, "rotation": 31.14, "x": 44.82, "color": "abe323ff"}, {"name": "hair_R6", "parent": "hair_R5", "length": 41.6, "rotation": 14.25, "x": 44.89, "color": "abe323ff"}, {"name": "hair_R7", "parent": "hair_R6", "length": 34.02, "rotation": -54.19, "x": 41.6, "color": "abe323ff"}, {"name": "hair_R8", "parent": "hair_R7", "length": 37.6, "rotation": -56, "x": 34.02, "color": "abe323ff"}, {"name": "eyebrow_L", "parent": "head", "length": 13.35, "rotation": 63.92, "x": 66.42, "y": -67.51}, {"name": "eyebrow_L2", "parent": "eyebrow_L", "length": 17.21, "rotation": 41.01, "x": 13.35}, {"name": "eyebrow_L3", "parent": "eyebrow_L2", "length": 12.62, "rotation": 5.42, "x": 17.21}, {"name": "eyebrow_R", "parent": "head", "length": 12.8, "rotation": -70.16, "x": 72.4, "y": 38.08}, {"name": "eyebrow_R2", "parent": "eyebrow_R", "length": 19.86, "rotation": -37.63, "x": 12.8}, {"name": "eyebrow_R3", "parent": "eyebrow_R2", "length": 12.63, "rotation": -1.24, "x": 19.86}, {"name": "eye_L", "parent": "head", "x": 50.32, "y": -42.96}, {"name": "eye_R", "parent": "head", "x": 52.77, "y": 20.05}, {"name": "tun", "parent": "ALL2", "length": 198.03, "rotation": -105.44, "x": -3.29, "y": -10.11}, {"name": "leg_R", "parent": "tun", "x": 52.71, "y": -99.3}, {"name": "leg_L", "parent": "tun", "x": 48.19, "y": 85.35}, {"name": "leg_R2", "parent": "leg_R", "length": 454.96, "rotation": 27.91, "x": 72.17, "y": -35.78}, {"name": "leg_R3", "parent": "leg_R2", "length": 477.15, "rotation": -11.69, "x": 454.96, "inherit": "noScale"}, {"name": "leg_L2", "parent": "leg_L", "length": 392.33, "rotation": 1.46, "x": 77.1, "y": 24.6}, {"name": "leg_L3", "parent": "leg_L2", "length": 473, "rotation": 16.01, "x": 392.33, "inherit": "noScale"}, {"name": "head_R", "parent": "head", "x": 116.98, "y": 72.85}, {"name": "hair_RR", "parent": "head_R", "length": 97.64, "rotation": -99.83, "x": -20.71, "y": 9.23, "inherit": "noRotationOrReflection"}, {"name": "hair_RR2", "parent": "hair_RR", "length": 94.92, "rotation": -5.44, "x": 97.64, "color": "abe323ff"}, {"name": "hair_RR3", "parent": "hair_RR2", "length": 92.18, "rotation": -16.2, "x": 94.93, "color": "abe323ff"}, {"name": "hair_RR4", "parent": "hair_RR3", "length": 81.63, "rotation": 3.05, "x": 92.18, "color": "abe323ff"}, {"name": "hair_RR5", "parent": "hair_RR4", "length": 63.94, "rotation": 21.77, "x": 81.63, "color": "abe323ff"}, {"name": "hair_RR6", "parent": "hair_RR5", "length": 69.14, "rotation": -8.49, "x": 63.94, "color": "abe323ff"}, {"name": "head_L", "parent": "head", "x": 117.93, "y": -77.02}, {"name": "hair_LL", "parent": "head_L", "length": 94.36, "rotation": -80.94, "x": -27.8, "y": -1.28, "inherit": "noRotationOrReflection"}, {"name": "hair_LL2", "parent": "hair_LL", "length": 96.77, "rotation": -15.54, "x": 94.36, "color": "abe323ff"}, {"name": "hair_LL3", "parent": "hair_LL2", "length": 88.67, "rotation": 16.77, "x": 96.77, "color": "abe323ff"}, {"name": "hair_LL4", "parent": "hair_LL3", "length": 77.21, "rotation": 5.34, "x": 88.67, "color": "abe323ff"}, {"name": "hair_LL5", "parent": "hair_LL4", "length": 61.49, "rotation": -17.49, "x": 77.21, "color": "abe323ff"}, {"name": "hair_LL6", "parent": "hair_LL5", "length": 65.49, "rotation": 4.45, "x": 61.49, "color": "abe323ff"}, {"name": "hand_L", "parent": "arm_L3", "x": 39.53, "y": -29.12}, {"name": "line_L", "parent": "hand_L", "length": 71.88, "rotation": -86.31, "x": 34.73, "y": -18.41, "inherit": "noRotationOrReflection", "color": "abe323ff"}, {"name": "line_L2", "parent": "line_L", "length": 70.19, "rotation": -26.31, "x": 71.88, "color": "abe323ff"}, {"name": "line_L3", "parent": "line_L2", "length": 60.39, "rotation": -5.58, "x": 70.19, "color": "abe323ff"}, {"name": "line_L4", "parent": "line_L3", "length": 76.91, "rotation": 49.36, "x": 60.39, "color": "abe323ff"}, {"name": "line_L5", "parent": "line_L4", "length": 69.74, "rotation": -31.35, "x": 76.91, "color": "abe323ff"}, {"name": "RU_R", "parent": "body2", "x": 114.05, "y": 35.13}, {"name": "RU_R2", "parent": "RU_R", "x": -4.1, "y": 7.93}, {"name": "RU_R3", "parent": "RU_R2", "x": -3.21, "y": 8.18}, {"name": "RU_L", "parent": "body2", "x": 69.84, "y": -101.4}, {"name": "RU_L2", "parent": "RU_L", "x": -14.33, "y": -36.21}, {"name": "RU_L3", "parent": "RU_L2", "x": -11.45, "y": -28.02}, {"name": "leg_L4", "parent": "leg_L", "length": 864.99, "rotation": 10.34, "x": 77.09, "y": 24.57}, {"name": "leg_L5", "parent": "root", "x": 33.8, "y": -117.06, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "leg_L4", "rotation": 95.22, "x": 391.4, "y": -59.7, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R4", "parent": "leg_R", "length": 938.74, "rotation": 21.81, "x": 72.18, "y": -35.79}, {"name": "leg_R5", "parent": "root", "x": -19.54, "y": -119.36, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "leg_R4", "rotation": 83.51, "x": 458.08, "y": 47.46, "color": "ff3f00ff", "icon": "ik"}, {"name": "bodyround", "parent": "body2", "x": 57.57, "y": -373.73, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": -8.44, "y": -373.73, "icon": "warning"}, {"name": "headround3", "parent": "head", "x": 369.04, "y": 5.55, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -60.57, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 313.55, "y": -55.02, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 341.36, "y": -58.53, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 341.36, "y": -117.24, "icon": "warning"}, {"name": "hand_R", "parent": "arm_R3", "x": 46.52, "y": -24.03}, {"name": "head_R2a", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -12.53, "y": 10.33}, {"name": "head_R2b", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -85.48, "y": 53.16}, {"name": "head_R2c", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -158.44, "y": 96}, {"name": "head_R2d", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -231.4, "y": 138.84}, {"name": "head_R2e", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -304.36, "y": 181.68}, {"name": "head_R2f", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -377.31, "y": 224.51}, {"name": "head_R2g", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -450.27, "y": 267.35}, {"name": "head_R2h", "parent": "head_R", "length": 84.6, "rotation": 149.58, "x": -523.23, "y": 310.19}, {"name": "arm_R4", "parent": "sh_R", "length": 100, "rotation": 147.05, "x": -140.38, "y": 74.38, "color": "003dffff"}, {"name": "hand_R2", "parent": "hand_R", "length": 68.91, "rotation": -53.04, "x": 32.44, "y": -42.86, "color": "003dffff"}, {"name": "head_L2a", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -28.99, "y": -1.13}, {"name": "head_L2b", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -115.5, "y": -2.99}, {"name": "head_L2c", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -202.01, "y": -4.86}, {"name": "head_L2d", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -288.51, "y": -6.72}, {"name": "head_L2e", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -375.02, "y": -8.58}, {"name": "head_L2f", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -461.53, "y": -10.45}, {"name": "head_L2g", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -548.04, "y": -12.31}, {"name": "head_L2h", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -634.54, "y": -14.18}, {"name": "head_L2i", "parent": "head_L", "length": 86.53, "rotation": -178.77, "x": -721.05, "y": -16.04}, {"name": "sh_L2", "parent": "sh_L", "length": 72.64, "rotation": -99.98, "x": -173.6, "y": 49.17, "color": "003dffff"}, {"name": "leg_R6", "parent": "leg_R2", "x": 77.75, "y": -66.71}], "slots": [{"name": "line_L", "bone": "root", "attachment": "line_L"}, {"name": "hair_B", "bone": "root", "attachment": "hair_B"}, {"name": "line_R", "bone": "root", "attachment": "line_R"}, {"name": "arm_L", "bone": "root", "attachment": "arm_L"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "leg_R", "bone": "root", "attachment": "leg_R"}, {"name": "leg_L", "bone": "root", "attachment": "leg_L"}, {"name": "ear_L", "bone": "root", "attachment": "ear_L"}, {"name": "ear_R", "bone": "root", "attachment": "ear_R"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}, {"name": "head_R", "bone": "head_R", "attachment": "head_R"}, {"name": "head_L", "bone": "head_L", "attachment": "head_L"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L4"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 2, "bones": ["leg_L2"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 3, "bones": ["leg_L3"], "target": "leg_L5", "compress": true, "stretch": true}, {"name": "leg_R", "order": 5, "bones": ["leg_R4"], "target": "leg_R5", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 6, "bones": ["leg_R2"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 7, "bones": ["leg_R3"], "target": "leg_R5", "compress": true, "stretch": true}], "transform": [{"name": "bodyround", "order": 8, "bones": ["bodyround2"], "target": "bodyround", "x": -66.01, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround1", "order": 9, "bones": ["sh_R"], "target": "bodyround", "x": 201.83, "y": 481.05, "mixRotate": 0, "mixX": 0.03, "mixScaleX": 0, "mixShearY": 0}, {"name": "bodyround2", "order": 10, "bones": ["sh_L"], "target": "bodyround", "x": 121.29, "y": 273.67, "mixRotate": 0, "mixX": -0.02, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 11, "bones": ["headround2"], "target": "headround", "x": -55.49, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 12, "bones": ["eyebrow_R"], "target": "headround", "rotation": -70.16, "x": -296.64, "y": 93.1, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 13, "bones": ["eyebrow_L"], "target": "headround", "rotation": 63.92, "x": -302.62, "y": -12.48, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 14, "bones": ["hair"], "target": "headround", "x": -254.8, "y": 31.78, "mixRotate": 0, "mixX": 0.04, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround4", "order": 15, "bones": ["hair_RR"], "target": "headround", "rotation": 157.92, "x": -272.77, "y": 137.11, "mixRotate": 0, "mixX": -0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround5", "order": 16, "bones": ["hair_LL"], "target": "headround", "rotation": 176.8, "x": -278.91, "y": -23.28, "mixRotate": 0, "mixX": -0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 17, "bones": ["tunround2"], "target": "tunround", "y": -58.71, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 4, "bones": ["leg_R"], "target": "tunround", "rotation": -105.44, "x": -454.39, "y": 24.04, "mixRotate": 0, "mixX": 0.01, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "bones": ["leg_L"], "target": "tunround", "rotation": -105.44, "x": -275.21, "y": -20.75, "mixRotate": 0, "mixX": -0.007, "mixScaleX": 0, "mixShearY": 0}], "path": [{"name": "head_L", "order": 18, "bones": ["head_L2a", "head_L2b", "head_L2c", "head_L2d", "head_L2e", "head_L2f", "head_L2g", "head_L2h", "head_L2i"], "target": "head_L", "spacingMode": "percent", "rotateMode": "chainScale", "spacing": 0.1113}, {"name": "head_R", "order": 19, "bones": ["head_R2a", "head_R2b", "head_R2c", "head_R2d", "head_R2e", "head_R2f", "head_R2g", "head_R2h"], "target": "head_R", "spacingMode": "percent", "rotateMode": "chainScale", "spacing": 0.125}], "physics": [{"name": "hair_L2", "order": 20, "bone": "hair_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 21, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 22, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 23, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L6", "order": 24, "bone": "hair_L6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L7", "order": 25, "bone": "hair_L7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL2", "order": 26, "bone": "hair_LL2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL3", "order": 27, "bone": "hair_LL3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL4", "order": 28, "bone": "hair_LL4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL5", "order": 29, "bone": "hair_LL5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL6", "order": 30, "bone": "hair_LL6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R2", "order": 31, "bone": "hair_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 32, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 33, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 34, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R6", "order": 35, "bone": "hair_R6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R7", "order": 36, "bone": "hair_R7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R8", "order": 37, "bone": "hair_R8", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR2", "order": 38, "bone": "hair_RR2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR3", "order": 39, "bone": "hair_RR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR4", "order": 40, "bone": "hair_RR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR5", "order": 41, "bone": "hair_RR5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR6", "order": 42, "bone": "hair_RR6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_L2", "order": 43, "bone": "line_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_L3", "order": 44, "bone": "line_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_L4", "order": 45, "bone": "line_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_L5", "order": 46, "bone": "line_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_R2", "order": 47, "bone": "line_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_R3", "order": 48, "bone": "line_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "line_R4", "order": 49, "bone": "line_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.0421, 0.00833, 0.09833, 0.00198, 0.15531, 0.00053, 0.20287, 0.00622, 0.25856, 0.01639, 0.30925, 0.04338, 0.3333, 0.07944, 0.34067, 0.12374, 0.34846, 0.17581, 0.36835, 0.29022, 0.37863, 0.32516, 0.40709, 0.41846, 0.42421, 0.44771, 0.44226, 0.49419, 0.45271, 0.5211, 0.48024, 0.55886, 0.52785, 0.66752, 0.56916, 0.7075, 0.63377, 0.7894, 0.66611, 0.81663, 0.67025, 0.8286, 0.70487, 0.84057, 0.73206, 0.85678, 0.77959, 0.86932, 0.81511, 0.87498, 0.91448, 0.88686, 0.93476, 0.89621, 0.999, 0.9581, 0.98727, 0.98594, 0.9767, 1, 0.96762, 1, 0.93111, 0.96696, 0.89131, 0.96327, 0.82067, 0.94353, 0.70227, 0.9417, 0.64373, 0.93271, 0.61193, 0.94263, 0.58447, 0.92469, 0.55001, 0.91331, 0.53193, 0.8999, 0.48902, 0.8863, 0.38995, 0.80745, 0.23517, 0.65374, 0.19039, 0.58543, 0.17391, 0.54771, 0.16766, 0.5073, 0.16192, 0.46885, 0.14421, 0.43356, 0.10544, 0.33126, 0.09101, 0.2958, 0.05009, 0.19956, 0.03207, 0.1572, 0.01522, 0.11276, 0.00398, 0.07257, 0.00129, 0.03265, 0.70874, 0.89831, 0.65916, 0.88927, 0.61857, 0.87551, 0.58504, 0.85501, 0.24074, 0.06106, 0.22281, 0.03157, 0.25247, 0.10028, 0.25642, 0.14641, 0.25355, 0.19524, 0.33905, 0.45006, 0.35052, 0.48405, 0.37374, 0.52403, 0.25065, 0.49176, 0.24257, 0.45453, 0.14492, 0.20151, 0.12162, 0.15547, 0.10377, 0.10586, 0.09873, 0.05947, 0.13304, 0.02621, 0.26709, 0.53506, 0.34126, 0.57285], "triangles": [52, 53, 71, 73, 0, 1, 73, 54, 0, 72, 54, 73, 53, 54, 72, 53, 72, 71, 59, 72, 60, 72, 59, 71, 57, 21, 22, 56, 57, 22, 55, 56, 22, 55, 22, 23, 38, 39, 57, 38, 57, 56, 37, 38, 56, 35, 37, 56, 35, 56, 55, 34, 35, 55, 36, 37, 35, 33, 24, 25, 33, 25, 26, 55, 23, 24, 33, 55, 24, 34, 55, 33, 32, 33, 26, 26, 31, 32, 27, 31, 26, 28, 31, 27, 30, 31, 28, 29, 30, 28, 66, 65, 13, 66, 13, 14, 66, 74, 67, 45, 67, 74, 44, 45, 74, 75, 74, 66, 43, 44, 74, 43, 74, 75, 42, 43, 75, 16, 75, 15, 15, 66, 14, 15, 75, 66, 17, 42, 16, 41, 17, 18, 16, 42, 75, 17, 41, 42, 58, 41, 18, 58, 18, 19, 58, 19, 20, 57, 58, 20, 57, 20, 21, 40, 41, 58, 39, 40, 58, 57, 39, 58, 51, 52, 71, 62, 71, 61, 69, 50, 70, 61, 59, 6, 62, 61, 7, 59, 5, 6, 61, 6, 7, 62, 7, 8, 63, 70, 62, 63, 62, 8, 63, 69, 70, 63, 8, 9, 49, 50, 69, 69, 63, 68, 69, 48, 49, 69, 68, 48, 47, 48, 68, 10, 64, 63, 63, 9, 10, 11, 64, 10, 64, 11, 12, 63, 64, 68, 46, 47, 68, 65, 64, 12, 65, 67, 68, 65, 68, 64, 46, 68, 67, 65, 12, 13, 45, 46, 67, 66, 67, 65, 70, 51, 71, 50, 51, 70, 71, 62, 70, 61, 71, 59, 59, 60, 5, 73, 1, 2, 60, 3, 4, 60, 4, 5, 73, 2, 3, 73, 3, 60, 60, 72, 73], "vertices": [3, 7, 17.66, 33.66, 0.23935, 16, -26.8, -32.86, 0.16677, 4, 196.52, -66.4, 0.59388, 3, 7, 17.62, 19.9, 0.45064, 16, -29.05, -19.29, 0.14351, 4, 196.48, -80.16, 0.40586, 3, 7, 14.84, 6.72, 0.817, 16, -28.52, -5.83, 0.04435, 4, 193.7, -93.34, 0.13866, 1, 7, 8.72, -3.24, 1, 2, 7, -0.36, -14.36, 0.84214, 16, -17.04, 17.49, 0.15786, 2, 7, -18.41, -21.8, 0.30288, 16, -0.49, 27.83, 0.69712, 2, 7, -39.76, -21.79, 0.03888, 16, 20.57, 31.39, 0.96112, 1, 16, 45.91, 30.55, 1, 1, 16, 75.68, 29.36, 1, 1, 16, 141.15, 27.41, 1, 1, 16, 161.24, 27.79, 1, 1, 16, 214.92, 29.07, 1, 2, 16, 231.94, 31.4, 0.96185, 17, -20.71, 37.27, 0.03815, 2, 16, 258.78, 32.94, 0.47757, 17, 5.74, 32.5, 0.52243, 2, 16, 274.32, 33.84, 0.12868, 17, 21.06, 29.74, 0.87132, 2, 16, 296.43, 38.11, 0.0008, 17, 43.55, 28.73, 0.9992, 1, 17, 105.82, 18.78, 1, 1, 17, 130.58, 20.42, 1, 1, 17, 179.76, 19.32, 1, 2, 17, 196.96, 21.37, 0.93416, 18, -2.57, 32.52, 0.06584, 2, 17, 203.73, 20.03, 0.81557, 18, 1.32, 26.81, 0.18443, 2, 17, 212.89, 25.47, 0.46974, 18, 11.66, 24.27, 0.53026, 2, 17, 223.74, 28.46, 0.14799, 18, 21.49, 18.78, 0.85201, 2, 17, 234.22, 36.67, 0.01291, 18, 34.72, 17.28, 0.98709, 1, 18, 43.67, 18.07, 1, 1, 18, 67.71, 22.29, 1, 1, 18, 74.36, 19.6, 1, 1, 18, 103.52, -5.47, 1, 1, 18, 108.02, -20.96, 1, 1, 18, 109.31, -29.27, 1, 1, 18, 107.39, -30.21, 1, 1, 18, 91.35, -17.06, 1, 1, 18, 81.99, -19.29, 1, 1, 18, 62.06, -16.51, 1, 1, 18, 36.51, -27.86, 1, 2, 17, 257.75, -5.57, 0.00337, 18, 21.84, -29.33, 0.99663, 2, 17, 260.61, -14.52, 0.01002, 18, 17.6, -37.71, 0.98998, 2, 17, 248.79, -17.25, 0.07923, 18, 7.27, -31.37, 0.92077, 2, 17, 239.97, -22.77, 0.24496, 18, -2.89, -29.11, 0.75504, 2, 17, 231.33, -24.26, 0.46602, 18, -10.09, -24.11, 0.53398, 2, 17, 220.65, -31.24, 0.76633, 18, -22.6, -21.59, 0.23367, 1, 17, 170.43, -38.39, 1, 1, 17, 75.52, -43.79, 1, 2, 16, 304.58, -31.48, 0.00235, 17, 35.21, -40.84, 0.99765, 2, 16, 282.76, -33.16, 0.10373, 17, 13.6, -37.38, 0.89627, 2, 16, 259.65, -32.29, 0.57166, 17, -8.66, -31.13, 0.42834, 2, 16, 237.68, -31.41, 0.94921, 17, -29.82, -25.13, 0.05079, 1, 16, 217.2, -33.52, 1, 1, 16, 158.16, -36.7, 1, 1, 16, 137.67, -38.03, 1, 2, 16, 82.02, -42.07, 0.88573, 4, 90.75, -39.18, 0.11427, 2, 16, 57.53, -43.84, 0.64758, 4, 115.2, -41.52, 0.35242, 3, 7, -38.14, 55.63, 0.00556, 16, 31.88, -45.22, 0.40407, 4, 140.72, -44.43, 0.59037, 3, 7, -15.31, 52.09, 0.0338, 16, 8.78, -45.54, 0.2067, 4, 163.55, -47.97, 0.7595, 3, 7, 6.83, 46.64, 0.09869, 16, -13.95, -43.85, 0.14736, 4, 185.69, -53.42, 0.75396, 1, 18, 26.98, -4.94, 1, 1, 18, 14.2, -5.45, 1, 2, 17, 224.96, -0.35, 0.26242, 18, 2.14, -2.6, 0.73758, 1, 17, 211.3, -3.94, 1, 4, 7, -23.84, -3.53, 0.1902, 16, 7.91, 10.72, 0.22543, 4, 155.03, -103.59, 0.57061, 82, 97.46, 270.14, 0.01376, 3, 7, -6.48, -3.93, 0.68951, 4, 172.38, -103.99, 0.30244, 82, 114.82, 269.74, 0.00805, 4, 7, -46.16, -0.24, 0.09107, 16, 30.48, 11.2, 0.61274, 4, 132.7, -100.3, 0.27683, 82, 75.13, 273.43, 0.01936, 3, 16, 56.77, 9.46, 0.96602, 4, 107.06, -94.19, 0.01478, 82, 49.49, 279.53, 0.0192, 2, 16, 84.44, 5.95, 0.98116, 82, 22.79, 287.6, 0.01884, 3, 16, 231.24, 11.27, 0.99547, 17, -26.1, 17.87, 0.00198, 82, -122.83, 306.84, 0.00256, 2, 16, 250.82, 11.99, 0.8444, 17, -6.9, 13.99, 0.1556, 2, 16, 274.09, 15.13, 0.06062, 17, 16.46, 11.6, 0.93938, 2, 16, 252.81, -11.9, 0.86103, 17, -10.55, -9.71, 0.13897, 2, 16, 231.47, -11.64, 0.99685, 82, -119.24, 329.47, 0.00315, 2, 16, 85.4, -19.91, 0.98033, 82, 26.16, 313.27, 0.01967, 3, 16, 58.69, -22.72, 0.74935, 4, 110.53, -62.15, 0.23148, 82, 52.97, 311.58, 0.01916, 4, 7, -39.9, 34.44, 0.00296, 16, 30.09, -24.03, 0.53205, 4, 138.96, -65.62, 0.44545, 82, 81.39, 308.1, 0.01954, 4, 7, -14.05, 28.54, 0.08117, 16, 3.61, -22.53, 0.36821, 4, 164.81, -71.52, 0.53145, 82, 107.24, 302.21, 0.01917, 4, 7, 2.11, 15.68, 0.41534, 16, -14.46, -12.55, 0.21458, 4, 180.97, -84.38, 0.35165, 82, 123.4, 289.35, 0.01843, 2, 16, 277.8, -10.55, 0.05336, 17, 14.07, -14.24, 0.94664, 1, 17, 40.22, -4.86, 1], "hull": 55, "edges": [2, 4, 8, 10, 10, 12, 18, 20, 28, 30, 30, 32, 32, 34, 34, 36, 44, 46, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 70, 72, 80, 82, 82, 84, 88, 90, 96, 98, 2, 0, 0, 108, 106, 108, 102, 104, 104, 106, 12, 14, 14, 16, 16, 18, 98, 100, 100, 102, 24, 26, 26, 28, 20, 22, 22, 24, 94, 96, 90, 92, 92, 94, 84, 86, 86, 88, 46, 48, 48, 50, 42, 44, 66, 68, 68, 70, 72, 74, 74, 76, 36, 38, 38, 40, 40, 42, 76, 78, 78, 80, 4, 6, 6, 8, 126, 128, 136, 138], "width": 236, "height": 571}}, "body": {"body": {"type": "mesh", "uvs": [0.54588, 0.00471, 0.61516, 0.00285, 0.65462, 0.00179, 0.65312, 0.02478, 0.65189, 0.04378, 0.65066, 0.06263, 0.65, 0.07864, 0.66492, 0.08253, 0.6659, 0.11721, 0.67862, 0.12752, 0.7161, 0.13895, 0.75861, 0.14244, 0.78725, 0.14636, 0.7952, 0.16405, 0.80315, 0.18173, 0.86247, 0.19264, 0.91807, 0.21356, 0.96179, 0.22018, 0.98116, 0.24159, 0.98576, 0.26003, 1, 0.28384, 0.99701, 0.31796, 0.97387, 0.35347, 0.92471, 0.37781, 0.87091, 0.38387, 0.86267, 0.41526, 0.8409, 0.44797, 0.82248, 0.47632, 0.81837, 0.49478, 0.82874, 0.52317, 0.84144, 0.56581, 0.86011, 0.62039, 0.86426, 0.64714, 0.86806, 0.68726, 0.87036, 0.72979, 0.87108, 0.76823, 0.79686, 0.76064, 0.70671, 0.75122, 0.62484, 0.74266, 0.58834, 0.75232, 0.56121, 0.75223, 0.52922, 0.73939, 0.51824, 0.72671, 0.42868, 0.7087, 0.31836, 0.68651, 0.23788, 0.67032, 0.26029, 0.62991, 0.28757, 0.59408, 0.32878, 0.56224, 0.3671, 0.53607, 0.41487, 0.50815, 0.47778, 0.47382, 0.52057, 0.44746, 0.52476, 0.43572, 0.51201, 0.41214, 0.49206, 0.38961, 0.47349, 0.36218, 0.43982, 0.33414, 0.41136, 0.30758, 0.39668, 0.28846, 0.38519, 0.30821, 0.33939, 0.37077, 0.32501, 0.39098, 0.31236, 0.41143, 0.30158, 0.4311, 0.27106, 0.48383, 0.21565, 0.5372, 0.18094, 0.58343, 0.16517, 0.59598, 0.1601, 0.60943, 0.1481, 0.6221, 0.14602, 0.63902, 0.13548, 0.68593, 0.12575, 0.70519, 0.14932, 0.72344, 0.17871, 0.74969, 0.20388, 0.77648, 0.22805, 0.80898, 0.24261, 0.84262, 0.24428, 0.87491, 0.22967, 0.91273, 0.20036, 0.94753, 0.17357, 0.96967, 0.15399, 0.9849, 0.14873, 0.99999, 0.13882, 0.98913, 0.13671, 0.97398, 0.14343, 0.95307, 0.14871, 0.93217, 0.14529, 0.90878, 0.1377, 0.87912, 0.12521, 0.84649, 0.11401, 0.81527, 0.10191, 0.78551, 0.08307, 0.75816, 0.06557, 0.73466, 0.04921, 0.7353, 0.01081, 0.70986, 0.00317, 0.68415, 0, 0.66458, 0.02844, 0.64109, 0.04925, 0.62342, 0.05174, 0.61167, 0.06126, 0.59338, 0.07518, 0.57482, 0.11419, 0.51025, 0.16063, 0.4308, 0.19094, 0.39793, 0.20657, 0.38047, 0.22182, 0.36426, 0.23501, 0.34109, 0.25863, 0.29928, 0.27829, 0.25418, 0.28891, 0.22013, 0.29953, 0.18608, 0.3169, 0.15151, 0.33596, 0.13035, 0.36488, 0.11489, 0.39465, 0.11301, 0.44644, 0.11776, 0.4857, 0.11268, 0.50311, 0.10031, 0.50441, 0.07932, 0.51582, 0.07502, 0.51298, 0.05907, 0.50622, 0.04034, 0.49847, 0.02324, 0.49012, 0.00621, 0.42075, 0.23666, 0.42859, 0.20068, 0.42355, 0.15846, 0.40467, 0.12824, 0.46276, 0.20773, 0.4342, 0.25179, 0.43644, 0.29916, 0.47284, 0.34836, 0.55234, 0.37369, 0.6234, 0.37223, 0.69115, 0.35167, 0.73034, 0.31018, 0.74266, 0.28191, 0.76173, 0.31218, 0.78984, 0.35624, 0.83408, 0.3779, 0.52445, 0.17135, 0.60103, 0.1647, 0.67694, 0.18222, 0.71913, 0.20909, 0.7281, 0.18339, 0.76419, 0.17789, 0.74048, 0.23986, 0.96604, 0.28726, 0.56262, 0.28446, 0.80818, 0.21676, 0.90438, 0.2484, 0.79822, 0.26049, 0.81255, 0.30088, 0.85998, 0.20352, 0.84591, 0.34144, 0.88757, 0.28293, 0.90717, 0.31753, 0.95096, 0.23962, 0.95664, 0.3319, 0.91316, 0.36322, 0.56936, 0.19644, 0.64856, 0.21315, 0.54911, 0.23541, 0.6118, 0.24187, 0.63619, 0.27266, 0.62983, 0.31192, 0.58703, 0.3305, 0.53086, 0.32581, 0.50275, 0.29541, 0.50488, 0.2575, 0.6942, 0.25383, 0.69383, 0.30771, 0.63338, 0.35061, 0.54576, 0.35315, 0.48392, 0.32428, 0.46102, 0.27275, 0.48968, 0.22006, 0.4589, 0.13605, 0.47472, 0.16466, 0.51101, 0.1385, 0.62268, 0.14825, 0.67162, 0.15145, 0.71719, 0.15417, 0.75888, 0.15846, 0.56594, 0.14581, 0.54757, 0.10348, 0.55443, 0.0746, 0.61795, 0.07649, 0.62248, 0.10445, 0.5537, 0.05601, 0.55103, 0.03805, 0.54769, 0.02008, 0.6165, 0.01833, 0.6165, 0.03805, 0.61717, 0.0582, 0.53202, 0.12194, 0.6321, 0.1264, 0.58599, 0.12537, 0.36924, 0.13646, 0.34765, 0.15936, 0.33214, 0.1897, 0.39914, 0.16754, 0.39432, 0.1909, 0.38787, 0.21334, 0.36766, 0.26985, 0.30934, 0.25761, 0.26052, 0.35015, 0.31011, 0.36368, 0.37932, 0.24027, 0.32103, 0.22581, 0.27459, 0.37542, 0.26015, 0.39354, 0.24571, 0.41578, 0.62322, 0.39668, 0.6257, 0.43467, 0.60998, 0.46614, 0.59591, 0.4987, 0.57357, 0.54754, 0.56115, 0.60649, 0.56529, 0.65099, 0.65963, 0.66944, 0.72169, 0.62983, 0.76059, 0.57664, 0.8301, 0.40913, 0.80941, 0.44821, 0.79865, 0.48348, 0.78459, 0.52147, 0.61411, 0.66946, 0.73822, 0.39916, 0.72498, 0.4404, 0.71256, 0.4773, 0.70015, 0.51529, 0.68195, 0.56521, 0.65216, 0.62219, 0.56273, 0.40387, 0.5677, 0.43806, 0.55611, 0.45976, 0.53129, 0.48364, 0.48991, 0.51837, 0.50563, 0.57784, 0.52218, 0.64769, 0.45182, 0.53638, 0.40714, 0.56569, 0.37428, 0.59111, 0.34093, 0.6422, 0.69568, 0.6756, 0.65819, 0.71998, 0.5169, 0.69534, 0.59306, 0.71196, 0.75112, 0.63022, 0.80326, 0.57216, 0.80698, 0.61437, 0.80619, 0.65626, 0.80364, 0.69025, 0.7331, 0.68151, 0.7208, 0.71412, 0.45395, 0.64246, 0.47615, 0.60842, 0.44003, 0.67787, 0.80217, 0.72142], "triangles": [84, 85, 83, 85, 86, 83, 83, 86, 82, 86, 87, 82, 82, 87, 81, 87, 88, 81, 81, 88, 80, 88, 89, 80, 79, 80, 90, 80, 89, 90, 79, 90, 78, 90, 91, 78, 91, 77, 78, 91, 92, 77, 92, 76, 77, 92, 93, 76, 93, 75, 76, 93, 94, 75, 96, 97, 95, 95, 97, 73, 97, 98, 73, 73, 98, 72, 98, 100, 72, 72, 100, 71, 71, 100, 101, 98, 99, 100, 71, 101, 70, 101, 102, 70, 102, 103, 70, 74, 95, 73, 74, 94, 95, 94, 74, 75, 70, 103, 69, 103, 104, 69, 69, 104, 68, 68, 104, 67, 66, 67, 105, 67, 104, 105, 66, 105, 65, 65, 105, 106, 216, 106, 107, 106, 216, 65, 65, 216, 64, 64, 216, 63, 107, 108, 216, 216, 215, 63, 216, 108, 215, 63, 215, 62, 108, 109, 215, 62, 215, 214, 215, 109, 214, 214, 211, 62, 62, 211, 61, 109, 210, 214, 214, 210, 211, 61, 211, 60, 109, 110, 210, 208, 211, 209, 211, 210, 209, 209, 210, 111, 110, 111, 210, 209, 111, 112, 211, 208, 60, 60, 208, 59, 212, 128, 208, 59, 208, 128, 208, 209, 212, 209, 213, 212, 213, 209, 113, 209, 112, 113, 212, 207, 128, 212, 213, 207, 213, 113, 204, 213, 204, 207, 204, 113, 114, 207, 206, 129, 207, 204, 206, 206, 205, 129, 204, 203, 206, 206, 203, 205, 203, 204, 115, 204, 114, 115, 203, 202, 205, 115, 116, 203, 203, 116, 202, 116, 117, 202, 37, 259, 36, 36, 34, 35, 36, 263, 34, 36, 259, 263, 34, 263, 33, 263, 257, 33, 263, 259, 257, 45, 46, 44, 44, 248, 262, 44, 46, 248, 248, 247, 260, 46, 47, 248, 248, 47, 247, 47, 48, 247, 43, 44, 262, 262, 248, 260, 149, 187, 13, 187, 12, 13, 186, 11, 187, 187, 11, 12, 186, 10, 11, 205, 202, 131, 205, 130, 129, 205, 131, 130, 130, 181, 182, 181, 131, 119, 181, 130, 131, 202, 117, 131, 181, 119, 120, 117, 118, 131, 131, 118, 119, 129, 130, 182, 182, 181, 183, 257, 258, 256, 256, 31, 32, 256, 255, 31, 256, 253, 255, 254, 255, 253, 255, 30, 31, 253, 226, 254, 255, 254, 30, 226, 230, 254, 254, 29, 30, 254, 230, 29, 256, 32, 33, 257, 256, 33, 258, 253, 256, 260, 247, 261, 261, 246, 243, 246, 245, 243, 245, 242, 243, 243, 242, 221, 48, 49, 246, 246, 49, 245, 242, 241, 221, 49, 50, 245, 245, 50, 242, 50, 51, 242, 242, 51, 241, 247, 246, 261, 247, 48, 246, 244, 261, 243, 37, 250, 259, 259, 250, 258, 253, 225, 226, 221, 241, 220, 38, 39, 252, 39, 40, 252, 40, 41, 252, 38, 250, 37, 38, 252, 250, 41, 42, 252, 43, 251, 42, 42, 251, 252, 252, 231, 250, 250, 224, 249, 250, 231, 224, 251, 223, 252, 252, 223, 231, 244, 251, 260, 251, 244, 223, 249, 224, 225, 224, 231, 237, 231, 223, 237, 224, 237, 225, 244, 222, 223, 223, 222, 237, 222, 244, 243, 237, 236, 225, 225, 236, 226, 237, 222, 236, 222, 221, 236, 222, 243, 221, 236, 235, 226, 221, 220, 236, 236, 220, 235, 249, 225, 253, 258, 250, 249, 258, 249, 253, 260, 261, 244, 260, 251, 262, 43, 262, 251, 257, 259, 258, 226, 235, 230, 230, 28, 29, 235, 234, 230, 230, 229, 28, 230, 234, 229, 220, 219, 235, 235, 219, 234, 241, 240, 220, 220, 240, 219, 28, 229, 27, 241, 52, 240, 234, 233, 229, 229, 228, 27, 229, 233, 228, 219, 218, 234, 234, 218, 233, 27, 228, 26, 240, 239, 219, 219, 239, 218, 240, 52, 239, 239, 52, 53, 233, 232, 228, 228, 227, 26, 228, 232, 227, 26, 227, 25, 232, 233, 217, 53, 238, 239, 239, 238, 218, 53, 54, 238, 238, 217, 218, 233, 218, 217, 227, 232, 143, 51, 52, 241, 25, 227, 24, 227, 143, 24, 143, 232, 142, 217, 238, 137, 238, 54, 136, 149, 13, 14, 153, 149, 157, 157, 15, 16, 148, 149, 153, 154, 153, 157, 161, 16, 17, 150, 147, 153, 154, 161, 151, 161, 18, 19, 163, 162, 22, 163, 160, 162, 22, 162, 21, 21, 162, 151, 162, 160, 151, 21, 151, 20, 160, 159, 151, 151, 19, 20, 151, 161, 19, 120, 121, 199, 183, 120, 199, 201, 189, 192, 58, 59, 134, 179, 134, 133, 132, 133, 128, 128, 129, 132, 138, 217, 137, 217, 138, 232, 54, 55, 136, 232, 138, 142, 141, 138, 139, 138, 141, 142, 137, 238, 136, 55, 56, 136, 24, 163, 23, 163, 24, 158, 24, 143, 158, 143, 142, 158, 23, 163, 22, 136, 135, 177, 136, 177, 137, 137, 176, 138, 137, 177, 176, 158, 160, 163, 57, 135, 56, 177, 135, 178, 135, 136, 56, 142, 156, 158, 142, 141, 156, 177, 170, 176, 178, 171, 177, 177, 171, 170, 138, 175, 139, 138, 176, 175, 170, 169, 176, 176, 169, 175, 178, 135, 134, 160, 156, 159, 160, 158, 156, 135, 57, 134, 169, 170, 152, 178, 172, 171, 170, 171, 152, 171, 172, 152, 172, 178, 179, 156, 141, 140, 169, 168, 175, 169, 152, 168, 141, 139, 140, 139, 175, 140, 175, 174, 140, 175, 168, 174, 140, 155, 156, 156, 155, 159, 178, 134, 179, 172, 173, 152, 172, 179, 173, 173, 166, 152, 152, 167, 168, 152, 166, 167, 174, 150, 140, 140, 150, 155, 173, 179, 180, 168, 167, 174, 173, 180, 166, 167, 165, 174, 174, 147, 150, 174, 165, 147, 166, 164, 167, 167, 164, 165, 166, 180, 164, 180, 144, 164, 180, 132, 144, 164, 145, 165, 165, 146, 147, 165, 145, 146, 147, 146, 148, 132, 182, 144, 164, 144, 145, 146, 186, 148, 145, 184, 146, 184, 185, 146, 146, 185, 186, 144, 188, 145, 182, 183, 144, 144, 183, 188, 145, 188, 184, 185, 10, 186, 184, 200, 185, 185, 200, 9, 185, 9, 10, 9, 200, 8, 188, 201, 184, 184, 201, 200, 183, 199, 188, 188, 199, 201, 201, 192, 200, 200, 192, 8, 199, 189, 201, 132, 129, 182, 132, 180, 133, 179, 133, 180, 58, 134, 57, 134, 59, 133, 128, 133, 59, 199, 121, 189, 181, 120, 183, 159, 154, 151, 159, 155, 154, 161, 17, 18, 154, 16, 161, 154, 155, 153, 155, 150, 153, 154, 157, 16, 153, 147, 148, 157, 14, 15, 149, 14, 157, 148, 187, 149, 148, 186, 187, 128, 207, 129, 192, 7, 8, 192, 6, 7, 191, 189, 190, 189, 191, 192, 123, 189, 121, 123, 121, 122, 192, 191, 6, 189, 123, 190, 6, 191, 5, 190, 198, 191, 191, 198, 5, 190, 123, 193, 123, 124, 193, 190, 193, 198, 5, 198, 4, 124, 194, 193, 124, 125, 194, 193, 197, 198, 198, 197, 4, 193, 194, 197, 4, 197, 3, 125, 195, 194, 125, 126, 195, 197, 194, 196, 194, 195, 196, 197, 196, 3, 3, 196, 2, 126, 0, 195, 196, 1, 2, 195, 0, 1, 126, 127, 0, 195, 1, 196], "vertices": [4, 6, 13.03, 18.3, 0.71094, 5, 98.99, 20.22, 0.26781, 4, 346.57, -2.21, 0, 82, 289, 371.51, 0.02124, 3, 6, 6.09, -21.39, 0.76524, 5, 98.79, -20.07, 0.21451, 82, 279.89, 332.27, 0.02025, 2, 6, 2.14, -43.99, 0.79845, 5, 98.68, -43.01, 0.20155, 2, 6, -17.58, -38.82, 0.39173, 5, 78.38, -41.21, 0.60827, 3, 6, -33.88, -34.54, 0.09326, 5, 61.59, -39.72, 0.90602, 4, 296.82, -52.38, 0.00073, 2, 5, 44.94, -38.25, 0.97245, 4, 280.92, -47.25, 0.02755, 2, 5, 30.79, -37.22, 0.85881, 4, 267.34, -43.12, 0.14119, 2, 5, 26.95, -45.72, 0.78543, 4, 261.71, -50.55, 0.21457, 2, 5, -3.77, -44.88, 0.23918, 4, 231.94, -42.93, 0.76082, 3, 5, -13.24, -51.85, 0.0555, 4, 221.17, -47.62, 0.85886, 7, 42.31, 52.43, 0.08564, 3, 5, -24.34, -73.13, 0.01298, 4, 205.62, -65.92, 0.55266, 7, 26.76, 34.14, 0.43436, 2, 4, 196.07, -88.91, 0.15424, 7, 17.2, 11.15, 0.84576, 1, 7, 9.43, -3.97, 1, 3, 4, 171.96, -104.31, 0.29685, 7, -6.9, -4.25, 0.6951, 82, 114.39, 269.41, 0.00805, 4, 4, 155.63, -104.6, 0.54561, 7, -23.23, -4.54, 0.244, 73, 85.79, -3.19, 0.1974, 82, 98.06, 269.13, 0.01299, 5, 4, 137.14, -135.25, 0.72321, 7, -41.72, -35.19, 0.07428, 74, 81.64, 2.36, 0.09846, 73, 67.31, -33.85, 0.08861, 82, 79.58, 238.47, 0.01545, 3, 4, 110.69, -161.47, 0.78588, 74, 55.18, -23.86, 0.19647, 82, 53.12, 212.26, 0.01765, 4, 4, 98.28, -184.39, 0.79844, 75, 54.22, -18.76, 0.04944, 74, 42.77, -46.78, 0.1409, 82, 40.71, 189.34, 0.01122, 4, 4, 77, -190.19, 0.80266, 75, 32.95, -24.56, 0.14165, 74, 21.5, -52.58, 0.0497, 82, 19.43, 183.53, 0.00599, 3, 4, 60.54, -188.43, 0.79286, 75, 16.49, -22.8, 0.19821, 82, 2.98, 185.3, 0.00893, 3, 4, 38.01, -190.79, 0.7924, 75, -6.05, -25.16, 0.1981, 82, -19.56, 182.93, 0.0095, 3, 4, 9.33, -181.08, 0.79319, 75, -34.73, -15.45, 0.1983, 82, -48.24, 192.65, 0.00851, 3, 4, -17.43, -159.75, 0.79242, 74, -72.93, -22.15, 0.1981, 82, -74.99, 213.97, 0.00948, 5, 4, -30.63, -126.48, 0.70979, 3, 136.07, -127.57, 0.09178, 74, -86.13, 11.13, 0.09896, 73, -100.46, -25.08, 0.08906, 82, -88.19, 247.24, 0.01041, 3, 4, -27.49, -94.92, 0.59145, 3, 125.58, -97.64, 0.20855, 73, -97.32, 6.48, 0.2, 2, 4, -53.02, -82.91, 0.42474, 3, 97.36, -97.55, 0.57526, 3, 4, -77.6, -63.01, 0.16114, 3, 66.68, -89.91, 0.81682, 45, -142.64, -2.82, 0.02204, 4, 4, -98.97, -46.02, 0.02037, 3, 40.13, -83.53, 0.80823, 43, -67.38, 78.89, 0.00185, 45, -115.58, -6.45, 0.16955, 4, 4, -114.09, -39.36, 0.00185, 3, 23.61, -83.89, 0.69276, 43, -50.99, 80.94, 0.01549, 45, -99.18, -4.41, 0.28991, 3, 3, -0.2, -94.02, 0.45121, 43, -28.34, 93.44, 0.03009, 45, -76.53, 8.1, 0.51869, 3, 3, -36.22, -107.59, 0.15471, 43, 6.11, 110.62, 0.00643, 45, -42.09, 25.27, 0.83885, 3, 3, -82.09, -126.33, 0.00339, 45, 1.64, 48.6, 0.90786, 48, -74.83, 25.91, 0.08875, 2, 45, 23.85, 57.23, 0.8127, 48, -52.4, 33.98, 0.1873, 2, 45, 57.52, 68.81, 0.34414, 48, -18.45, 44.7, 0.65586, 2, 45, 93.49, 80.13, 0.06571, 48, 17.8, 55.1, 0.93429, 1, 48, 50.74, 63.74, 1, 2, 48, 54.64, 20.27, 0.96945, 87, -265.55, -159.53, 0.03055, 3, 43, 185.29, 78.89, 0.39769, 48, 59.19, -32.57, 0.56231, 87, -317.93, -151.19, 0.04, 2, 43, 190.65, 31.02, 0.98981, 87, -365.5, -143.61, 0.01019, 2, 43, 204.54, 12.86, 0.99754, 87, -386.7, -152.16, 0.00246, 1, 43, 208.66, -2.36, 1, 2, 43, 202.64, -23.3, 0.99368, 87, -421.05, -140.71, 0.00632, 3, 43, 193.51, -32.44, 0.98797, 44, 140.8, 66.86, 0.00109, 87, -427.43, -129.47, 0.01095, 4, 43, 191.97, -86.85, 0.35544, 44, 139.27, 12.45, 0.00282, 46, 81.87, 11.22, 0.60174, 87, -479.46, -113.51, 0.04, 2, 46, 48.83, -47.12, 0.97253, 87, -543.56, -93.85, 0.02747, 2, 46, 24.72, -89.68, 0.95038, 110, -53.02, -22.97, 0.04962, 3, 44, 98.03, -100.44, 0.07119, 46, -7.42, -69.23, 0.89643, 110, -85.17, -2.52, 0.03239, 3, 44, 63.21, -93.61, 0.23645, 46, -34.99, -46.9, 0.74702, 110, -112.74, 19.81, 0.01653, 2, 44, 29.64, -78.04, 0.50378, 46, -57.37, -17.43, 0.49622, 3, 3, -56.1, 168.55, 0.00164, 44, 1.36, -62.75, 0.74968, 46, -75.2, 9.32, 0.24867, 6, 4, -63.17, 189.78, 0.00095, 3, -27.08, 145.3, 0.04496, 43, 22.84, -141.88, 0.00011, 44, -29.87, -42.58, 0.8836, 46, -93.36, 41.76, 0.07029, 9, 301.49, 112.31, 0.0001, 5, 4, -43.57, 146.45, 0.02625, 3, 8.99, 114.31, 0.31437, 43, -16.21, -114.74, 0.05338, 44, -68.92, -15.44, 0.60255, 9, 261.48, 138.01, 0.00345, 4, 4, -27.67, 116.28, 0.12959, 3, 36.15, 93.69, 0.58543, 43, -45.34, -96.99, 0.04668, 44, -98.05, 2.3, 0.2383, 4, 4, -18.29, 111.16, 0.23567, 3, 46.82, 93.02, 0.61835, 43, -56.01, -97.41, 0.02611, 44, -108.72, 1.88, 0.11988, 3, 4, 3.82, 112.75, 0.48153, 3, 66.18, 103.8, 0.51452, 43, -74.18, -110.12, 0.00395, 2, 4, 26.14, 118.61, 0.67113, 3, 83.94, 118.55, 0.32887, 2, 4, 52.45, 122.55, 0.85224, 3, 106.11, 133.24, 0.14776, 2, 4, 81.6, 134.8, 0.93842, 9, 150.11, 79.71, 0.06158, 2, 4, 108.67, 144.49, 0.79688, 9, 132.66, 56.85, 0.20312, 2, 4, 127.27, 148.2, 0.47778, 9, 119.07, 43.62, 0.52222, 3, 4, 112.18, 159.29, 0.24653, 8, -147.22, 51.97, 0.00315, 9, 137.77, 42.53, 0.75031, 2, 9, 198.64, 33.84, 0.9846, 10, -31.99, 29.45, 0.0154, 2, 9, 218.22, 31.27, 0.73746, 10, -12.23, 29.79, 0.26254, 2, 9, 237.72, 29.72, 0.28872, 10, 7.28, 31.13, 0.71128, 2, 9, 256.22, 28.99, 0.03285, 10, 25.69, 33.14, 0.96715, 1, 10, 75.45, 37.68, 1, 1, 10, 132.06, 29.46, 1, 2, 10, 177.72, 29.3, 0.99283, 11, -32.93, 32.38, 0.00717, 2, 10, 191.73, 25.93, 0.92185, 11, -19.28, 27.77, 0.07815, 2, 10, 203.73, 28.51, 0.72664, 11, -7.1, 29.26, 0.27336, 2, 10, 216.88, 27.16, 0.34787, 11, 5.88, 26.75, 0.65213, 2, 10, 230.88, 32.65, 0.07921, 11, 20.32, 30.95, 0.92079, 1, 11, 61.34, 40.03, 1, 1, 11, 79.29, 40.82, 1, 2, 11, 89.53, 59.37, 0.77429, 12, 20.13, 23.74, 0.22571, 3, 11, 105.18, 83.61, 0.41429, 12, 48.84, 26.53, 0.35639, 13, -24.2, 20.37, 0.22932, 3, 11, 122.16, 105.73, 0.18857, 12, 76.72, 26.97, 0.2013, 13, 2.35, 28.9, 0.61013, 2, 13, 33.67, 35.64, 0.98819, 14, -46.38, 22, 0.01181, 2, 13, 64.63, 36.73, 0.69766, 14, -17.38, 32.91, 0.30234, 2, 13, 92.64, 30.83, 0.13858, 14, 11.05, 36.25, 0.86142, 2, 14, 45.15, 30.58, 0.94854, 15, -22.02, 26.85, 0.05146, 2, 14, 77.29, 16.16, 0.18528, 15, 12.29, 18.87, 0.81472, 1, 15, 35.4, 9.25, 1, 2, 14, 112.52, -7.93, 2e-05, 15, 51.49, 1.98, 0.99998, 1, 15, 65.19, 2.69, 1, 1, 15, 57.5, -5.47, 1, 2, 14, 103.71, -18.74, 0, 15, 44.92, -10.31, 1, 1, 15, 26.04, -11.61, 1, 2, 14, 66.21, -14.87, 0.29269, 15, 7.38, -13.71, 0.70731, 2, 14, 45.73, -18.57, 0.95054, 15, -12.01, -21.27, 0.04946, 2, 13, 81.45, -30.18, 0.1485, 14, 19.91, -25.15, 0.8515, 2, 13, 51.64, -30.32, 0.84681, 14, -8.3, -34.78, 0.15319, 3, 11, 172.86, 69.17, 0.16, 12, 79.57, -35.47, 0.07446, 13, 23.23, -30.02, 0.76554, 3, 11, 150.73, 53.21, 0.46, 12, 53.32, -28.04, 0.25779, 13, -4.05, -30.54, 0.28221, 3, 11, 131.98, 34.35, 0.7598, 12, 26.89, -25.07, 0.21845, 13, -30.2, -35.38, 0.02175, 1, 11, 116.14, 17.44, 1, 1, 11, 120.06, 8.76, 1, 1, 11, 106.94, -20.11, 1, 1, 11, 87.23, -32.37, 1, 1, 11, 71.68, -40.27, 1, 2, 10, 262.48, -27.95, 0.00242, 11, 46.35, -32.23, 0.99758, 2, 10, 243.1, -23.94, 0.05941, 11, 27.41, -26.51, 0.94059, 2, 10, 233.11, -27.2, 0.15932, 11, 17.17, -28.86, 0.84068, 2, 10, 216.12, -29.34, 0.47288, 11, 0.06, -29.46, 0.52712, 2, 10, 197.8, -29.27, 0.8521, 11, -18.18, -27.76, 0.1479, 1, 10, 136.44, -33.99, 1, 1, 10, 61.34, -40.59, 1, 2, 9, 247.56, -41.16, 0.06452, 10, 27.45, -37.52, 0.93548, 2, 9, 230.07, -37.16, 0.31822, 10, 9.56, -36.14, 0.68178, 2, 9, 213.7, -33.03, 0.68115, 10, -7.23, -34.47, 0.31885, 2, 9, 191.82, -31.91, 0.96786, 10, -29.04, -36.59, 0.03214, 1, 9, 152.36, -29.97, 1, 1, 9, 110.82, -31.12, 1, 1, 9, 80.19, -34.32, 1, 1, 9, 49.57, -37.52, 1, 2, 8, -2.83, 53.3, 0.0036, 9, 17.32, -37.12, 0.9964, 2, 8, 12.3, 37.64, 0.10611, 9, -3.89, -32.21, 0.89389, 2, 8, 21.03, 17.8, 0.44385, 9, -22.01, -20.3, 0.55615, 2, 8, 18.04, 0.69, 0.88153, 9, -28.81, -4.32, 0.11847, 2, 4, 265.38, 80.11, 0.19269, 8, 5.99, -27.21, 0.80731, 3, 5, 5.02, 59.52, 0.15047, 4, 263.65, 56.93, 0.44908, 8, 4.25, -50.39, 0.40044, 3, 5, 15.51, 48.91, 0.59292, 4, 271.53, 44.26, 0.33326, 8, 12.13, -63.06, 0.07382, 2, 5, 34.06, 47.31, 0.90819, 4, 289.26, 38.59, 0.09181, 2, 5, 37.56, 40.52, 0.94962, 4, 291.16, 31.19, 0.05038, 2, 5, 51.75, 41.52, 0.99722, 4, 305.22, 29.02, 0.00278, 2, 6, -12.92, 47.52, 0.1382, 5, 68.51, 44.68, 0.8618, 3, 6, 2.84, 48.7, 0.40341, 5, 83.85, 48.48, 0.59659, 4, 338.07, 28.7, 0, 3, 6, 18.61, 50.24, 0.77104, 5, 99.14, 52.64, 0.22896, 4, 353.9, 29.37, 0, 4, 4, 167.79, 122.52, 0.66479, 8, -91.6, 15.2, 0.05347, 9, 71.1, 43.14, 0.25272, 82, 110.23, 496.24, 0.02901, 4, 4, 197.31, 109.65, 0.47496, 8, -62.08, 2.33, 0.3228, 9, 39.32, 37.88, 0.17406, 82, 139.75, 483.37, 0.02819, 4, 4, 234.15, 102.52, 0.17195, 8, -25.24, -4.8, 0.72504, 9, 4.53, 23.83, 0.08096, 82, 176.59, 476.25, 0.02204, 3, 4, 262.88, 105.98, 0.00196, 8, 3.48, -1.34, 0.98637, 82, 205.31, 479.71, 0.01166, 4, 4, 186.01, 92.17, 0.59063, 8, -73.38, -15.14, 0.17938, 70, 71.96, 57.04, 0.1925, 82, 128.44, 465.9, 0.0375, 4, 4, 152.79, 118.55, 0.72424, 8, -106.6, 11.23, 0.05068, 70, 38.74, 83.41, 0.19373, 82, 95.23, 492.27, 0.03134, 3, 4, 111.99, 128.45, 0.78146, 70, -2.06, 93.32, 0.19537, 82, 54.43, 502.18, 0.02317, 4, 4, 64.35, 119.66, 0.70036, 3, 118.12, 135.65, 0.08488, 70, -49.7, 84.53, 0.19631, 82, 6.78, 493.39, 0.01845, 4, 4, 30.43, 81.1, 0.61421, 3, 103.68, 86.36, 0.16109, 70, -83.62, 45.97, 0.19383, 82, -27.14, 454.83, 0.03087, 5, 4, 20.7, 40.96, 0.60901, 3, 111.82, 45.87, 0.13359, 9, 150.18, 191.58, 0.0254, 70, -93.35, 5.82, 0.192, 82, -36.87, 414.68, 0.04, 4, 4, 27.79, -1.83, 0.70656, 3, 136.34, 10.09, 0.06144, 70, -86.26, -36.96, 0.192, 82, -29.78, 371.9, 0.04, 3, 4, 57.17, -33.56, 0.768, 70, -56.88, -68.69, 0.192, 82, -0.4, 340.17, 0.04, 4, 4, 79.41, -47.12, 0.7776, 70, -34.64, -82.25, 0.096, 73, 9.58, 54.29, 0.0864, 82, 21.85, 326.61, 0.04, 3, 4, 50.62, -50.66, 0.768, 73, -19.22, 50.74, 0.192, 82, -6.95, 323.06, 0.04, 4, 4, 8.64, -56.03, 0.69683, 3, 141.89, -47.13, 0.07117, 73, -61.2, 45.37, 0.192, 82, -48.93, 317.69, 0.04, 4, 4, -16.7, -75.7, 0.61619, 3, 127.24, -75.66, 0.16516, 73, -86.53, 25.7, 0.19534, 82, -74.26, 298.03, 0.02331, 4, 4, 207.55, 49.05, 0.67742, 8, -51.84, -58.27, 0.09058, 70, 93.5, 13.92, 0.192, 82, 149.99, 422.78, 0.04, 4, 4, 201.4, 4.59, 0.74606, 8, -57.99, -102.73, 0.02194, 70, 87.35, -30.54, 0.192, 82, 143.84, 378.32, 0.04, 3, 4, 174.71, -33.8, 0.768, 70, 60.66, -68.93, 0.192, 82, 117.14, 339.93, 0.04, 4, 4, 145.24, -51.1, 0.7776, 70, 31.19, -86.23, 0.096, 73, 75.4, 50.31, 0.0864, 82, 87.67, 322.63, 0.04, 3, 4, 165.81, -62.18, 0.7742, 73, 95.97, 39.23, 0.19355, 82, 108.24, 311.55, 0.03226, 4, 4, 164.93, -83.68, 0.54989, 7, -13.93, 16.38, 0.22826, 73, 95.09, 17.72, 0.19454, 82, 107.36, 290.04, 0.02731, 4, 4, 115.66, -55.8, 0.7776, 70, 1.61, -90.94, 0.096, 73, 45.82, 45.6, 0.0864, 82, 58.09, 317.92, 0.04, 3, 4, 40.33, -170.97, 0.71855, 75, -3.73, -5.34, 0.23952, 82, -17.24, 202.76, 0.04193, 3, 4, 105.05, 54.32, 0.68625, 72, -1.69, 3.08, 0.22875, 82, 47.49, 428.05, 0.085, 3, 4, 124.93, -99.16, 0.75999, 74, 69.43, 38.45, 0.19, 82, 67.37, 274.56, 0.05002, 3, 4, 83.04, -145.59, 0.75868, 75, 38.99, 20.04, 0.18967, 82, 25.48, 228.14, 0.05165, 3, 4, 89.12, -83.28, 0.76, 74, 33.61, 54.33, 0.19, 82, 31.55, 290.45, 0.05, 3, 4, 52.41, -81.79, 0.76, 74, -3.1, 55.82, 0.19, 82, -5.16, 291.93, 0.05, 3, 4, 128.24, -131.29, 0.77081, 74, 72.73, 6.32, 0.1927, 82, 70.67, 242.43, 0.03649, 4, 4, 12.61, -90.92, 0.74413, 3, 160.23, -77.07, 0.01804, 74, -42.89, 46.69, 0.19054, 82, -44.95, 282.81, 0.04729, 3, 4, 56.15, -128.04, 0.75623, 75, 12.1, 37.6, 0.18906, 82, -1.41, 245.69, 0.05471, 3, 4, 23.57, -130.86, 0.75581, 75, -20.49, 34.77, 0.18895, 82, -34, 242.87, 0.05523, 3, 4, 83.35, -173.74, 0.77654, 75, 39.3, -8.11, 0.19414, 82, 25.79, 199.98, 0.02932, 3, 4, 3.66, -155.18, 0.76709, 75, -40.4, 10.45, 0.19177, 82, -53.91, 218.54, 0.04114, 4, 4, -16.37, -123.45, 0.73191, 3, 147.71, -118.81, 0.0411, 74, -71.88, 14.16, 0.19325, 82, -73.94, 250.27, 0.03373, 4, 4, 179.19, 29.81, 0.74126, 8, -80.21, -77.51, 0.01074, 71, 69.24, -13.26, 0.188, 82, 121.62, 403.53, 0.06, 3, 4, 152.68, -10.62, 0.752, 71, 42.73, -53.68, 0.188, 82, 95.11, 363.11, 0.06, 3, 4, 149.03, 50.33, 0.74, 72, 42.29, -0.91, 0.185, 82, 91.46, 424.06, 0.075, 3, 4, 133.82, 16.74, 0.74, 72, 27.08, -34.5, 0.185, 82, 76.26, 390.47, 0.075, 3, 4, 103.76, 10.34, 0.74, 72, -2.98, -40.9, 0.185, 82, 46.19, 384.06, 0.075, 3, 4, 71.21, 23.15, 0.74, 72, -35.53, -28.09, 0.185, 82, 13.64, 396.87, 0.075, 3, 4, 61.95, 51.5, 0.74, 72, -44.79, 0.26, 0.185, 82, 4.39, 425.23, 0.075, 3, 4, 74.64, 81.85, 0.74, 72, -32.1, 30.61, 0.185, 82, 17.07, 455.58, 0.075, 3, 4, 104.95, 90.43, 0.74, 72, -1.8, 39.19, 0.185, 82, 47.38, 464.16, 0.075, 3, 4, 137, 80.31, 0.74, 72, 30.26, 29.07, 0.185, 82, 79.43, 454.03, 0.075, 3, 4, 110.88, -26.59, 0.752, 71, 0.93, -69.66, 0.188, 82, 53.31, 347.13, 0.06, 3, 4, 64.92, -13.69, 0.752, 71, -45.03, -56.75, 0.188, 82, 7.35, 360.04, 0.06, 4, 4, 37.62, 30.28, 0.72577, 3, 131.68, 43.34, 0.02623, 71, -72.33, -12.79, 0.188, 82, -19.95, 404, 0.06, 4, 4, 48.99, 79.95, 0.708, 3, 120.99, 93.16, 0.04525, 71, -60.96, 36.88, 0.18831, 82, -8.57, 453.67, 0.05845, 3, 4, 83.2, 107.78, 0.75594, 71, -26.75, 64.71, 0.18898, 82, 25.64, 481.51, 0.05508, 3, 4, 130.75, 108.47, 0.75415, 71, 20.81, 65.4, 0.18854, 82, 73.19, 482.19, 0.05731, 4, 4, 171.33, 80, 0.71412, 8, -88.07, -27.32, 0.03788, 71, 61.38, 36.94, 0.188, 82, 113.76, 453.73, 0.06, 3, 4, 247.84, 77.45, 0.27031, 8, -11.56, -29.87, 0.71399, 82, 190.27, 451.17, 0.0157, 4, 4, 220.96, 75.32, 0.4581, 8, -38.44, -32, 0.46324, 70, 106.91, 40.19, 0.04849, 82, 163.39, 449.05, 0.03017, 3, 4, 237.69, 48.83, 0.6995, 8, -21.71, -58.48, 0.27596, 82, 180.12, 422.56, 0.02455, 2, 4, 212.11, -11.41, 0.9702, 82, 154.54, 362.31, 0.0298, 2, 4, 201.81, -38.07, 0.97806, 82, 144.24, 335.66, 0.02194, 3, 4, 192.45, -62.95, 0.7269, 7, 13.59, 37.11, 0.25507, 82, 134.88, 310.78, 0.01802, 3, 4, 182.34, -85.29, 0.35964, 7, 3.48, 14.77, 0.62015, 82, 124.78, 288.44, 0.02021, 3, 4, 222.96, 19.79, 0.86217, 8, -36.43, -87.53, 0.10812, 82, 165.4, 393.52, 0.02971, 3, 5, 11.52, 23.24, 0.6539, 4, 261.95, 20.11, 0.32541, 82, 204.38, 393.84, 0.02069, 2, 5, 36.91, 18.09, 0.97892, 82, 227.99, 383.19, 0.02108, 3, 5, 33.54, -18.7, 0.93422, 4, 274.13, -25.67, 0.04451, 82, 216.56, 348.06, 0.02127, 3, 5, 8.67, -20.19, 0.49934, 4, 249.55, -21.61, 0.4804, 82, 191.98, 352.11, 0.02026, 2, 5, 53.38, 17.76, 0.97898, 82, 243.98, 379.22, 0.02102, 3, 6, -16.47, 21.64, 0.15506, 5, 69.35, 18.58, 0.82438, 82, 259.74, 376.48, 0.02056, 4, 6, -0.5, 20.16, 0.44677, 5, 85.33, 19.79, 0.53225, 4, 333.16, 0.39, 0, 82, 275.59, 374.12, 0.02098, 3, 6, -7.48, -19.24, 0.49781, 5, 85.06, -20.22, 0.48193, 82, 266.46, 335.16, 0.02026, 3, 6, -24.54, -15.53, 0.15593, 5, 67.61, -19.42, 0.82322, 82, 249.62, 339.81, 0.02085, 3, 5, 49.75, -18.99, 0.97344, 4, 289.87, -29.54, 0.00549, 82, 232.3, 344.18, 0.02107, 4, 5, -4.4, 33.01, 0.09258, 4, 248.59, 33.17, 0.79506, 8, -10.81, -74.15, 0.09216, 82, 191.02, 406.89, 0.0202, 3, 5, -11.01, -24.89, 0.01724, 4, 229.31, -21.83, 0.96205, 82, 171.74, 351.89, 0.02071, 3, 4, 237.32, 3.75, 0.95877, 8, -22.08, -103.57, 0.01957, 82, 179.75, 377.47, 0.02166, 3, 8, 1.94, 20.44, 0.18217, 9, -4.55, -12.13, 0.80181, 82, 203.76, 501.48, 0.01603, 2, 9, 18.58, -17.99, 0.98383, 82, 187.54, 518.97, 0.01617, 2, 9, 46.92, -18.49, 0.98319, 82, 164.03, 534.81, 0.01681, 4, 4, 230.17, 118.33, 0.10622, 8, -29.23, 11.01, 0.29833, 9, 16.47, 12.72, 0.57208, 82, 172.6, 492.06, 0.02337, 4, 4, 210.97, 126.54, 0.17347, 8, -48.43, 19.22, 0.15722, 9, 37.05, 16.28, 0.64429, 82, 153.4, 500.26, 0.02502, 4, 4, 192.79, 135.44, 0.18738, 8, -66.6, 28.12, 0.07457, 9, 57.14, 18.7, 0.71149, 82, 135.23, 509.16, 0.02655, 4, 4, 147.65, 160.07, 0.11227, 8, -111.74, 52.75, 0.00521, 9, 108.42, 22.58, 0.86233, 82, 90.09, 533.8, 0.02018, 2, 9, 108.29, -13, 0.98263, 82, 109.55, 563.58, 0.01737, 3, 9, 195.01, -15.36, 0.9703, 10, -28.33, -19.75, 0.02537, 82, 38.06, 612.72, 0.00433, 3, 9, 197.76, 15.73, 0.99414, 10, -30.18, 11.41, 0.00195, 82, 18.84, 588.14, 0.0039, 4, 4, 171.11, 146.57, 0.16738, 8, -88.28, 39.25, 0.02454, 9, 81.39, 21.15, 0.78475, 82, 113.55, 520.3, 0.02333, 2, 9, 79.37, -15.01, 0.98272, 82, 134.9, 549.54, 0.01728, 2, 9, 213.9, -0.82, 0.98635, 10, -11.78, -2.58, 0.01365, 2, 9, 231.74, -3.99, 0.24681, 10, 6.33, -3.09, 0.75319, 2, 9, 253.05, -6.05, 0.00035, 10, 27.71, -1.99, 0.99965, 5, 4, -0.16, 46.82, 0.41404, 3, 90.44, 42.37, 0.52128, 9, 170.87, 198, 0.02232, 82, -57.73, 420.55, 0.03958, 87, -366.44, 162.93, 0.00277, 6, 4, -32.99, 54.38, 0.11645, 3, 57.49, 35.35, 0.82666, 43, -72.52, -41.14, 0.00487, 9, 202.53, 209.51, 0.01022, 82, -90.56, 428.11, 0.03111, 87, -364.99, 129.28, 0.01068, 7, 4, -57.44, 70.61, 0.03856, 3, 28.48, 39.71, 0.83191, 43, -43.21, -42.52, 0.06162, 44, -95.92, 56.78, 0.02165, 9, 231.87, 209.2, 0.00459, 82, -115.01, 444.33, 0.02311, 87, -374.13, 101.39, 0.01856, 7, 4, -83.08, 86.16, 0.01013, 3, -1.33, 42.97, 0.50308, 43, -13.23, -42.72, 0.29678, 44, -65.94, 56.58, 0.14696, 9, 261.84, 210.09, 0.00135, 82, -140.64, 459.88, 0.01508, 87, -382.3, 72.54, 0.02662, 7, 4, -121.34, 110.18, 8e-05, 3, -46.16, 48.57, 0.05681, 43, 31.94, -43.71, 0.5849, 44, -20.77, 55.59, 0.31617, 9, 307.01, 210.74, 1e-05, 82, -178.91, 483.9, 0.00326, 87, -395.28, 29.27, 0.03876, 3, 43, 84.2, -36.76, 0.75273, 44, 31.49, 62.54, 0.20727, 87, -402.5, -22.96, 0.04, 3, 43, 121.56, -23.95, 0.91162, 44, 68.85, 75.35, 0.05155, 87, -400.09, -62.38, 0.03683, 2, 43, 122.73, 33.23, 0.96644, 87, -345.28, -78.73, 0.03356, 3, 43, 79.3, 58.65, 0.79445, 45, 31.11, -26.7, 0.16622, 87, -309.22, -43.63, 0.03933, 5, 3, -53.5, -62.86, 0.16587, 43, 27.87, 67.89, 0.29742, 45, -20.33, -17.46, 0.50082, 82, -232.66, 386.01, 0.00296, 87, -286.62, 3.49, 0.03293, 3, 4, -42.76, -66.11, 0.41063, 3, 99.56, -77.99, 0.56854, 82, -100.33, 307.61, 0.02083, 5, 4, -72.94, -45.32, 0.10603, 3, 63.43, -71.9, 0.86364, 45, -137.57, -20.41, 0.00871, 82, -130.5, 328.41, 0.01909, 87, -258.26, 117.28, 0.00253, 6, 4, -101.4, -30.98, 0.00553, 3, 31.57, -70.94, 0.85156, 43, -57.59, 67.24, 0.00923, 45, -105.78, -18.11, 0.10966, 82, -158.97, 342.74, 0.01287, 87, -264.51, 86.03, 0.01114, 5, 3, -2.97, -68.48, 0.6264, 43, -22.97, 68.32, 0.08539, 45, -71.16, -17.03, 0.25661, 82, -189.24, 359.57, 0.01008, 87, -272.68, 52.38, 0.02151, 2, 43, 129.79, 7.75, 0.96354, 87, -371.73, -78.75, 0.03646, 4, 4, -20.04, -17, 0.26993, 3, 99.4, -23.88, 0.68924, 82, -77.61, 356.72, 0.03997, 87, -299.62, 160.74, 0.00086, 3, 3, 62.09, -22.38, 0.9555, 82, -110.79, 373.86, 0.03377, 87, -307.31, 124.2, 0.01073, 3, 3, 28.65, -20.7, 0.95563, 82, -140.39, 389.5, 0.02464, 87, -314.53, 91.51, 0.01973, 4, 3, -5.74, -19.19, 0.74268, 43, -15.19, 19.57, 0.21293, 82, -170.91, 405.4, 0.01543, 87, -321.74, 57.85, 0.02897, 4, 3, -51.12, -16.12, 0.02308, 43, 30.26, 21.15, 0.93339, 82, -210.74, 427.36, 0.00353, 87, -332.31, 13.62, 0.04, 2, 43, 83.53, 17.91, 0.96, 87, -349.62, -36.87, 0.04, 5, 4, 3.05, 82.39, 0.44986, 3, 78.32, 75.96, 0.5197, 43, -89.09, -83.66, 0.00161, 82, -54.52, 456.12, 0.02608, 87, -401.58, 156.57, 0.00276, 6, 4, -26.92, 87.67, 0.18235, 3, 48.93, 68.08, 0.70665, 43, -60.66, -72.82, 0.024, 44, -113.37, 26.48, 0.05194, 82, -84.49, 461.39, 0.02375, 87, -398.69, 126.28, 0.01131, 6, 4, -43.67, 99.27, 0.0867, 3, 28.84, 71.51, 0.63285, 43, -40.33, -74.19, 0.0655, 44, -93.04, 25.11, 0.18069, 82, -101.23, 473, 0.01883, 87, -405.42, 107.04, 0.01543, 7, 4, -60.23, 118.8, 0.03391, 3, 5.58, 82.22, 0.40871, 43, -16.1, -82.46, 0.1144, 44, -68.81, 16.84, 0.40598, 9, 260.42, 170.27, 0.0045, 82, -117.79, 492.53, 0.01359, 87, -419.85, 85.89, 0.01893, 7, 4, -83.5, 150.16, 0.00428, 3, -28.76, 100.8, 0.12482, 43, 19.96, -97.44, 0.07011, 44, -32.75, 1.85, 0.76998, 9, 297, 156.61, 0.00052, 82, -141.06, 523.88, 0.00431, 87, -443.89, 55.12, 0.02596, 4, 3, -79.19, 83.03, 0.0021, 43, 68.31, -74.61, 0.28412, 44, 15.6, 24.68, 0.67378, 87, -434.75, 2.43, 0.04, 3, 43, 125.41, -48.87, 0.81533, 44, 72.7, 50.43, 0.15196, 87, -425.14, -59.46, 0.03272, 5, 4, -93, 175.73, 0.00024, 3, -48.18, 119.96, 0.02054, 44, -11.47, -15.23, 0.94927, 9, 318.89, 140.32, 2e-05, 87, -466.02, 39.16, 0.02993, 3, 44, 20.46, -33.34, 0.78416, 46, -44.55, 26.36, 0.18557, 87, -491.98, 13.19, 0.03027, 3, 44, 47.26, -45.75, 0.37651, 46, -26.69, 2.86, 0.5927, 87, -511.07, -9.33, 0.03079, 2, 46, 13.33, -25.84, 0.96987, 87, -530.44, -54.6, 0.03013, 2, 43, 122.41, 54.87, 0.96938, 87, -324.34, -84.19, 0.03062, 2, 43, 166.12, 44.35, 0.97686, 87, -346.12, -123.51, 0.02314, 3, 43, 166.92, -40.59, 0.95654, 44, 114.22, 58.71, 0.01954, 87, -428.2, -101.68, 0.02392, 2, 43, 169.34, 5.98, 0.97289, 87, -383.96, -116.4, 0.02711, 3, 43, 75.09, 75.22, 0.40592, 45, 26.89, -10.13, 0.55889, 87, -292.12, -43.98, 0.03518, 5, 3, -45.46, -86.65, 0.17265, 43, 17.43, 90.73, 0.0366, 45, -30.76, 5.38, 0.76146, 82, -235.42, 361.06, 0.00314, 87, -261.83, 7.46, 0.02616, 2, 45, 4.72, 17.42, 0.97335, 87, -259.67, -29.94, 0.02665, 4, 43, 88.81, 112.2, 0.03075, 45, 40.62, 26.85, 0.81736, 48, -36.42, 3.19, 0.12416, 87, -260.13, -67.05, 0.02772, 3, 45, 70.04, 33.45, 0.31683, 48, -6.84, 9.03, 0.65434, 87, -261.61, -97.17, 0.02883, 3, 43, 121.68, 77.23, 0.83338, 45, 73.48, -8.12, 0.13567, 87, -302.59, -89.42, 0.03095, 4, 43, 151.43, 78.03, 0.61961, 45, 103.24, -7.32, 0.04136, 48, 25.32, -32.57, 0.30404, 87, -309.74, -118.32, 0.03499, 4, 43, 131.5, -88.32, 0.56989, 44, 78.79, 10.98, 0.34303, 46, 27.74, 38.23, 0.04708, 87, -464.78, -54.83, 0.04, 3, 43, 99, -83.91, 0.36376, 44, 46.29, 15.38, 0.59624, 87, -451.88, -24.67, 0.04, 4, 43, 163.89, -87.76, 0.54552, 44, 111.19, 11.53, 0.0594, 46, 56.62, 23.55, 0.35508, 87, -472.87, -86.2, 0.04, 3, 45, 96.88, 39.97, 0.06934, 48, 20.17, 14.87, 0.90142, 87, -262.47, -124.78, 0.02924], "hull": 128, "edges": [12, 14, 14, 16, 16, 18, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 90, 92, 92, 94, 104, 106, 110, 112, 112, 114, 128, 130, 130, 132, 152, 154, 158, 160, 160, 162, 170, 172, 176, 178, 184, 186, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 210, 212, 218, 220, 220, 222, 222, 224, 228, 230, 230, 232, 232, 234, 238, 240, 246, 248, 252, 254, 248, 250, 250, 252, 244, 246, 4, 6, 6, 8, 8, 10, 10, 12, 240, 242, 242, 244, 22, 24, 18, 20, 20, 22, 234, 236, 236, 238, 118, 120, 114, 116, 116, 118, 212, 214, 120, 122, 122, 124, 124, 126, 126, 128, 214, 216, 216, 218, 140, 142, 200, 202, 202, 204, 204, 206, 138, 140, 206, 208, 208, 210, 136, 138, 132, 134, 134, 136, 142, 144, 144, 146, 146, 148, 186, 188, 188, 190, 148, 150, 150, 152, 154, 156, 156, 158, 162, 164, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 182, 184, 178, 180, 180, 182, 28, 30, 30, 32, 50, 52, 52, 54, 56, 58, 106, 108, 108, 110, 100, 102, 102, 104, 98, 100, 58, 60, 60, 62, 76, 78, 78, 80, 80, 82, 82, 84, 68, 70, 66, 68, 256, 118, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 48, 264, 288, 288, 290, 290, 292, 292, 294, 296, 298, 298, 28, 294, 300, 300, 280, 306, 310, 310, 312, 306, 314, 314, 32, 312, 316, 308, 318, 38, 40, 318, 320, 38, 322, 322, 308, 320, 324, 324, 42, 32, 34, 36, 38, 34, 36, 44, 326, 326, 316, 306, 296, 314, 298, 328, 330, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 332, 296, 294, 330, 348, 348, 350, 350, 352, 354, 356, 356, 358, 360, 328, 352, 354, 360, 358, 24, 26, 26, 28, 380, 386, 386, 388, 388, 390, 0, 254, 390, 0, 0, 2, 2, 4, 2, 392, 392, 394, 394, 396, 396, 382, 378, 380, 382, 384, 262, 260, 260, 258, 258, 256, 262, 234, 418, 420, 422, 416, 424, 416, 408, 426, 426, 418, 224, 226, 226, 228, 434, 436, 436, 438, 438, 440, 440, 442, 442, 444, 444, 446, 446, 462, 462, 448, 448, 450, 450, 452, 452, 460, 460, 458, 458, 456, 456, 454, 486, 484, 486, 488, 88, 90, 76, 500, 500, 498, 84, 502, 502, 488, 498, 506, 506, 508, 508, 58, 74, 76, 94, 96, 96, 98, 84, 86, 86, 88, 520, 502, 516, 500, 62, 64, 64, 66, 72, 74, 70, 72], "width": 581, "height": 886}}, "ear_L": {"ear_L": {"type": "mesh", "uvs": [0.79884, 0.02492, 0.9585, 0.32022, 0.9728, 0.57709, 0.90224, 0.87943, 0.64125, 1, 0.27603, 0.94309, 0.12117, 0.81462, 0.03414, 0.59985, 0.09562, 0.22769, 0.39862, 0.0166], "triangles": [4, 5, 3, 5, 6, 3, 3, 6, 2, 6, 7, 2, 1, 7, 8, 7, 1, 2, 1, 9, 0, 1, 8, 9], "vertices": [2, 6, 57.06, -79.09, 0.99516, 86, -256.49, -24.07, 0.00484, 2, 6, 39.18, -79.29, 0.99379, 86, -274.37, -24.27, 0.00621, 2, 6, 24.3, -76.42, 0.99363, 86, -289.25, -21.4, 0.00637, 2, 6, 7.24, -70.91, 0.99294, 86, -306.31, -15.89, 0.00706, 2, 6, 1.68, -63.02, 0.99317, 86, -311.87, -8, 0.00683, 2, 6, 6.9, -54.82, 0.99325, 86, -306.65, 0.21, 0.00675, 2, 6, 15.13, -52.64, 0.99317, 86, -298.42, 2.38, 0.00683, 2, 6, 27.97, -53.21, 0.99349, 86, -285.58, 1.82, 0.00651, 2, 6, 49.1, -59.37, 0.99398, 86, -264.45, -4.35, 0.00602, 2, 6, 59.66, -69.42, 0.99561, 86, -253.89, -14.39, 0.00439], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 25, "height": 59}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [0.52495, 0.07861, 0.87527, 0.43047, 1, 0.81739, 0.83921, 0.97077, 0.61434, 0.97612, 0.2493, 0.73251, 0.024, 0.48176, 0.0272, 0.07545, 0.25539, 1e-05], "triangles": [3, 4, 2, 2, 4, 5, 5, 1, 2, 1, 5, 6, 1, 6, 0, 0, 7, 8, 7, 0, 6], "vertices": [2, 6, 56.84, 56.11, 0.98885, 85, -312.21, 111.14, 0.01115, 2, 6, 34.9, 47.97, 0.98745, 85, -334.14, 103, 0.01255, 2, 6, 12.78, 48.19, 0.98788, 85, -356.27, 103.21, 0.01212, 2, 6, 5.61, 55.67, 0.98952, 85, -363.43, 110.69, 0.01048, 2, 6, 7.04, 63.64, 0.991, 85, -362, 118.67, 0.009, 2, 6, 23.16, 73.59, 0.99261, 85, -345.88, 128.61, 0.00739, 2, 6, 38.6, 78.53, 0.99335, 85, -330.44, 133.55, 0.00665, 2, 6, 60.81, 73.58, 0.99216, 85, -308.23, 128.61, 0.00784, 2, 6, 63.2, 64.66, 0.99041, 85, -305.85, 119.68, 0.00959], "hull": 9, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 2, 4, 4, 6, 16, 0], "width": 36, "height": 56}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.04611, 0.54227, 0.11937, 0.92192, 0.70106, 0.9362, 0.94749, 0.60793, 0.82983, 0.07699, 0.42575, 0.07985], "triangles": [2, 1, 0, 3, 2, 5, 3, 5, 4, 2, 0, 5], "vertices": [2, 6, 52.16, -23.9, 0.96, 85, -316.88, 31.13, 0.04, 2, 6, 38.47, -24.3, 0.96, 85, -330.57, 30.73, 0.04, 2, 6, 32.43, -49.77, 0.96008, 85, -336.61, 5.26, 0.03992, 2, 6, 41.3, -63.05, 0.96607, 85, -327.74, -8.02, 0.03393, 2, 6, 60.58, -61.82, 0.96474, 85, -308.46, -6.79, 0.03526, 2, 6, 64.35, -44.03, 0.96, 85, -304.7, 11, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 45, "height": 35}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.04873, 0.30815, 0.07423, 0.8617, 0.42923, 0.94, 0.94577, 0.68078, 0.91814, 0.26495, 0.43135, 0.07054], "triangles": [2, 1, 0, 5, 2, 0, 2, 5, 4, 2, 4, 3], "vertices": [2, 6, 61.94, 37.21, 0.96251, 85, -307.11, 92.23, 0.03749, 2, 6, 41.67, 40.38, 0.96288, 85, -327.37, 95.41, 0.03712, 2, 6, 35.29, 24.69, 0.96, 85, -333.75, 79.72, 0.04, 2, 6, 39.51, -1.07, 0.96, 85, -329.53, 53.96, 0.04, 2, 6, 54.82, -3.06, 0.96, 85, -314.22, 51.96, 0.04, 2, 6, 66.71, 17.76, 0.96, 85, -302.33, 72.79, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 47, "height": 37}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 41, -9.77, -5.97, 0.96, 85, -328.48, 6.1, 0.04, 2, 41, -6.37, 9.67, 0.96, 85, -325.09, 21.73, 0.04, 2, 41, 9.27, 6.27, 0.96, 85, -309.45, 18.33, 0.04, 2, 41, 5.87, -9.37, 0.96, 85, -312.85, 2.7, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.28063, 0.33786, 0.41662, 0.25888, 0.45893, 0.02684, 0.6523, 0.0597, 0.85813, 0.11919, 0.98297, 0.31548, 0.80409, 0.55231, 0.84126, 0.67759, 0.71884, 0.88567, 0.52239, 0.95956, 0.40171, 0.96353, 0.26926, 0.92164, 0.15287, 0.89566, 0.04053, 0.89903, 0.03311, 0.82535, 0.08369, 0.64693, 0.16056, 0.48197, 0.10318, 0.78938, 0.13653, 0.66797, 0.21045, 0.53743, 0.31321, 0.43429, 0.44842, 0.37033, 0.55478, 0.37555, 0.64402, 0.42907, 0.70352, 0.50871, 0.67016, 0.63143, 0.59354, 0.74761, 0.46735, 0.8168, 0.31411, 0.82072, 0.19242, 0.80114], "triangles": [1, 22, 21, 2, 22, 1, 2, 3, 22, 23, 22, 3, 23, 3, 4, 24, 23, 4, 20, 0, 1, 20, 1, 21, 19, 16, 0, 4, 6, 24, 20, 19, 0, 5, 6, 4, 25, 23, 24, 18, 15, 16, 19, 18, 16, 25, 26, 22, 25, 22, 23, 17, 15, 18, 29, 18, 19, 17, 18, 29, 26, 27, 21, 26, 21, 22, 20, 21, 27, 28, 19, 20, 28, 29, 19, 20, 27, 28, 14, 15, 17, 25, 6, 7, 6, 25, 24, 8, 25, 7, 26, 25, 8, 12, 17, 29, 13, 14, 17, 13, 17, 12, 11, 29, 28, 12, 29, 11, 9, 27, 26, 9, 26, 8, 10, 28, 27, 10, 27, 9, 11, 28, 10], "vertices": [2, 6, 56.6, -41.97, 0.96, 85, -312.44, 13.06, 0.04, 2, 6, 57.63, -48.03, 0.96, 85, -311.41, 6.99, 0.04, 2, 6, 63.83, -51.2, 0.96, 85, -305.21, 3.83, 0.04, 2, 6, 61.17, -58.93, 0.96322, 85, -307.87, -3.91, 0.03678, 2, 6, 57.65, -67.01, 0.96692, 85, -311.39, -11.99, 0.03308, 2, 6, 50.97, -70.93, 0.96901, 85, -318.07, -15.9, 0.03099, 2, 6, 45.86, -62.13, 0.96522, 85, -323.18, -7.1, 0.03478, 2, 6, 41.98, -62.88, 0.96529, 85, -327.07, -7.86, 0.03471, 2, 6, 37.17, -56.58, 0.96221, 85, -331.87, -1.55, 0.03779, 2, 6, 36.83, -48.06, 0.96, 85, -332.21, 6.97, 0.04, 2, 6, 37.79, -43.08, 0.96, 85, -331.25, 11.94, 0.04, 2, 6, 40.16, -37.9, 0.96, 85, -328.88, 17.12, 0.04, 2, 6, 41.94, -33.29, 0.96, 85, -327.1, 21.74, 0.04, 2, 6, 42.84, -28.65, 0.96, 85, -326.2, 26.37, 0.04, 2, 6, 45, -28.8, 0.96, 85, -324.04, 26.22, 0.04, 2, 6, 49.6, -31.98, 0.96, 85, -319.44, 23.05, 0.04, 2, 6, 53.59, -36.15, 0.96, 85, -315.45, 18.88, 0.04, 2, 6, 45.39, -31.9, 0.96, 85, -323.65, 23.12, 0.04, 2, 6, 48.54, -34.02, 0.96, 85, -320.51, 21.01, 0.04, 2, 6, 51.58, -37.86, 0.96, 85, -317.47, 17.17, 0.04, 2, 6, 53.58, -42.71, 0.96, 85, -315.46, 12.32, 0.04, 2, 6, 54.19, -48.65, 0.96, 85, -314.85, 6.37, 0.04, 2, 6, 53.09, -52.98, 0.96135, 85, -315.95, 2.04, 0.03865, 2, 6, 50.78, -56.32, 0.96281, 85, -318.26, -1.29, 0.03719, 2, 6, 47.99, -58.27, 0.96364, 85, -321.05, -3.24, 0.03636, 2, 6, 44.81, -56.14, 0.96265, 85, -324.23, -1.12, 0.03735, 2, 6, 42.2, -52.28, 0.96089, 85, -326.84, 2.74, 0.03911, 2, 6, 41.37, -46.68, 0.96, 85, -327.67, 8.35, 0.04, 2, 6, 42.62, -40.37, 0.96, 85, -326.42, 14.66, 0.04, 2, 6, 44.26, -35.49, 0.96, 85, -324.78, 19.53, 0.04], "hull": 17, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 30, 32, 4, 6, 6, 8, 2, 0, 0, 32, 2, 4, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 34], "width": 42, "height": 29}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 42, -9.8, -6.06, 0.96, 85, -326.07, 69.02, 0.04, 2, 42, -6.4, 9.58, 0.96, 85, -322.67, 84.65, 0.04, 2, 42, 9.24, 6.18, 0.96, 85, -307.03, 81.25, 0.04, 2, 42, 5.84, -9.45, 0.96, 85, -310.43, 65.62, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.53009, 0.2357, 0.69279, 0.25609, 0.81678, 0.36255, 0.89499, 0.49363, 0.95934, 0.60484, 0.95961, 0.66797, 0.88157, 0.69475, 0.78161, 0.7736, 0.66999, 0.88879, 0.515, 0.96614, 0.36895, 0.97061, 0.21331, 0.86914, 0.20943, 0.74417, 0.12092, 0.74027, 0.01509, 0.60102, 0.05418, 0.34597, 0.19322, 0.20609, 0.33225, 0.06621, 0.49704, 0.02872, 0.88377, 0.5946, 0.84043, 0.5066, 0.75293, 0.40126, 0.65412, 0.35319, 0.51079, 0.35669, 0.81407, 0.61334, 0.73407, 0.66934, 0.6299, 0.77067, 0.5099, 0.80001, 0.3863, 0.78832, 0.3113, 0.71098, 0.27213, 0.61765, 0.3263, 0.51498, 0.40797, 0.42165], "triangles": [9, 28, 27, 9, 10, 28, 10, 11, 28, 28, 11, 29, 9, 26, 8, 9, 27, 26, 8, 25, 7, 8, 26, 25, 11, 12, 29, 28, 32, 27, 26, 27, 23, 27, 32, 23, 29, 31, 28, 28, 31, 32, 7, 24, 6, 7, 25, 24, 26, 22, 25, 26, 23, 22, 12, 30, 29, 12, 13, 30, 13, 14, 30, 29, 30, 31, 5, 19, 4, 5, 6, 19, 6, 24, 19, 25, 21, 24, 25, 22, 21, 14, 15, 30, 15, 16, 30, 30, 16, 31, 24, 20, 19, 24, 21, 20, 19, 3, 4, 19, 20, 3, 31, 16, 32, 21, 2, 20, 20, 2, 3, 16, 17, 32, 23, 32, 0, 22, 1, 21, 21, 1, 2, 0, 32, 17, 23, 0, 22, 0, 17, 18, 22, 0, 1], "vertices": [2, 6, 60.36, 18.97, 0.96, 85, -308.68, 73.99, 0.04, 2, 6, 58.1, 11.47, 0.96, 85, -310.94, 66.49, 0.04, 2, 6, 53.72, 6.33, 0.96, 85, -315.32, 61.36, 0.04, 2, 6, 49.08, 3.5, 0.96, 85, -319.96, 58.52, 0.04, 2, 6, 45.16, 1.19, 0.96, 85, -323.88, 56.21, 0.04, 2, 6, 43.31, 1.58, 0.96, 85, -325.73, 56.6, 0.04, 2, 6, 43.32, 5.41, 0.96, 85, -325.72, 60.43, 0.04, 2, 6, 42.03, 10.6, 0.96, 85, -327.02, 65.62, 0.04, 2, 6, 39.79, 16.57, 0.96, 85, -329.26, 71.59, 0.04, 2, 6, 39.1, 24.33, 0.96, 85, -329.94, 79.36, 0.04, 2, 6, 40.46, 31.21, 0.96043, 85, -328.58, 86.24, 0.03957, 2, 6, 45.02, 37.86, 0.96238, 85, -324.02, 92.89, 0.03762, 2, 6, 48.72, 37.25, 0.96239, 85, -320.32, 92.28, 0.03761, 2, 6, 49.74, 41.38, 0.96349, 85, -319.3, 96.4, 0.03651, 2, 6, 54.9, 45.45, 0.96468, 85, -314.14, 100.48, 0.03532, 2, 6, 61.98, 41.99, 0.9638, 85, -307.06, 97.02, 0.0362, 2, 6, 64.66, 34.58, 0.96183, 85, -304.38, 89.61, 0.03817, 2, 6, 67.34, 27.17, 0.96, 85, -301.7, 82.19, 0.04, 2, 6, 66.76, 19.2, 0.96, 85, -302.28, 74.23, 0.04, 2, 6, 46.23, 4.67, 0.96, 85, -322.81, 59.69, 0.04, 2, 6, 49.25, 6.14, 0.96, 85, -319.79, 61.16, 0.04, 2, 6, 53.23, 9.57, 0.96, 85, -315.81, 64.6, 0.04, 2, 6, 55.65, 13.9, 0.96, 85, -313.39, 68.93, 0.04, 2, 6, 57.01, 20.65, 0.96, 85, -312.03, 75.67, 0.04, 2, 6, 46.39, 8.06, 0.96, 85, -322.65, 63.08, 0.04, 2, 6, 45.57, 12.17, 0.96, 85, -323.47, 67.19, 0.04, 2, 6, 43.66, 17.7, 0.96, 85, -325.38, 72.72, 0.04, 2, 6, 44.02, 23.51, 0.96, 85, -325.02, 78.54, 0.04, 2, 6, 45.62, 29.24, 0.9602, 85, -323.42, 84.26, 0.0398, 2, 6, 48.66, 32.26, 0.9611, 85, -320.39, 87.29, 0.0389, 2, 6, 51.79, 33.5, 0.96151, 85, -317.25, 88.53, 0.03849, 2, 6, 54.25, 30.31, 0.96073, 85, -314.79, 85.33, 0.03927, 2, 6, 56.15, 25.88, 0.96, 85, -312.89, 80.91, 0.04], "hull": 19, "edges": [18, 20, 26, 28, 28, 30, 34, 36, 6, 8, 8, 10, 6, 4, 0, 36, 0, 2, 2, 4, 10, 12, 12, 14, 14, 16, 16, 18, 24, 26, 20, 22, 22, 24, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 38, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 46], "width": 48, "height": 30}}, "hair_B": {"hair_B": {"type": "mesh", "uvs": [0.78868, 0.03003, 0.81512, 0.05578, 0.84307, 0.10569, 0.85863, 0.15409, 0.86896, 0.21603, 0.86832, 0.28197, 0.8683, 0.34396, 0.86973, 0.41267, 0.88141, 0.46748, 0.91124, 0.53292, 0.94741, 0.59159, 0.97327, 0.64021, 0.99351, 0.69554, 1, 0.76179, 0.99074, 0.80592, 0.96046, 0.84946, 0.94763, 0.87168, 0.95586, 0.89186, 0.98386, 0.89517, 0.98609, 0.92059, 0.92809, 0.92055, 0.8757, 0.89554, 0.83485, 0.94028, 0.7654, 0.97475, 0.70243, 1, 0.63083, 0.98646, 0.56656, 0.99043, 0.4817, 0.99849, 0.43452, 0.98135, 0.36363, 0.96908, 0.28643, 0.96525, 0.1766, 0.98303, 0.14839, 0.94997, 0.10637, 0.95733, 0.02954, 0.95582, 0.01216, 0.90134, 0.00481, 0.85894, 0.01621, 0.82683, 0, 0.78804, 0.00453, 0.74097, 0.05216, 0.68495, 0.07822, 0.63619, 0.07805, 0.59667, 0.11821, 0.56599, 0.20575, 0.52982, 0.25414, 0.5004, 0.29596, 0.45103, 0.32166, 0.39777, 0.34544, 0.33692, 0.36536, 0.27431, 0.38258, 0.20691, 0.39774, 0.14627, 0.414, 0.09614, 0.43569, 0.05795, 0.53063, 0.01451, 0.69246, 5e-05, 0.15179, 0.90573, 0.15309, 0.83602, 0.17528, 0.77527, 0.19094, 0.70058, 0.22487, 0.63684, 0.27316, 0.57211, 0.14931, 0.59642, 0.62967, 0.08155, 0.51262, 0.13387, 0.48085, 0.21298, 0.46914, 0.29592, 0.45744, 0.39034, 0.44908, 0.48349, 0.42233, 0.56516, 0.40226, 0.65192, 0.37885, 0.74379, 0.35711, 0.83184, 0.35377, 0.89819, 0.72498, 0.13065, 0.73835, 0.20211, 0.7417, 0.29781, 0.735, 0.405, 0.73334, 0.49048, 0.74337, 0.55811, 0.75675, 0.62829, 0.7841, 0.71235, 0.79369, 0.78821, 0.76781, 0.92641, 0.58619, 0.56502, 0.58619, 0.65434, 0.58452, 0.73218, 0.58082, 0.80871, 0.57194, 0.87898, 0.86105, 0.85616, 0.77797, 0.86885, 0.57308, 0.94514], "triangles": [23, 24, 83, 26, 27, 91, 26, 91, 25, 91, 27, 28, 83, 24, 25, 88, 91, 28, 23, 83, 22, 83, 25, 91, 28, 73, 88, 91, 88, 83, 83, 90, 22, 21, 90, 89, 21, 22, 90, 83, 88, 90, 20, 17, 19, 17, 18, 19, 20, 21, 17, 73, 72, 88, 21, 16, 17, 21, 89, 16, 88, 87, 90, 88, 72, 87, 16, 89, 15, 90, 87, 82, 82, 87, 86, 90, 82, 89, 15, 89, 14, 14, 89, 82, 72, 71, 87, 87, 71, 86, 86, 81, 82, 14, 82, 13, 82, 81, 13, 81, 12, 13, 81, 86, 85, 81, 85, 80, 81, 11, 12, 11, 80, 10, 11, 81, 80, 80, 85, 84, 80, 9, 10, 80, 84, 79, 80, 79, 9, 84, 78, 79, 84, 68, 78, 79, 8, 9, 79, 78, 8, 78, 68, 77, 78, 7, 8, 78, 77, 7, 76, 77, 66, 77, 6, 7, 77, 76, 6, 6, 76, 5, 76, 75, 5, 5, 75, 4, 75, 3, 4, 75, 74, 3, 74, 2, 3, 74, 1, 2, 74, 0, 1, 31, 32, 30, 30, 73, 29, 28, 29, 73, 32, 56, 30, 30, 56, 73, 32, 33, 56, 35, 56, 33, 35, 33, 34, 56, 57, 73, 57, 56, 36, 36, 56, 35, 57, 36, 37, 73, 57, 72, 57, 58, 72, 57, 37, 58, 72, 58, 71, 37, 38, 58, 38, 39, 58, 39, 40, 58, 58, 59, 71, 58, 40, 59, 59, 60, 71, 71, 60, 70, 40, 41, 59, 41, 62, 59, 59, 62, 60, 60, 62, 61, 41, 42, 62, 42, 43, 62, 62, 43, 44, 71, 70, 86, 86, 70, 85, 70, 69, 85, 69, 84, 85, 60, 61, 70, 70, 61, 69, 62, 44, 61, 44, 45, 61, 61, 45, 69, 45, 46, 69, 69, 68, 84, 69, 46, 68, 77, 68, 67, 77, 67, 66, 46, 47, 68, 68, 47, 67, 47, 48, 67, 67, 48, 66, 48, 49, 66, 76, 66, 65, 76, 65, 64, 66, 49, 65, 65, 49, 50, 50, 51, 65, 65, 51, 64, 51, 52, 64, 74, 55, 0, 52, 53, 64, 64, 53, 54, 75, 64, 74, 64, 75, 76, 64, 63, 74, 64, 54, 63, 63, 55, 74, 63, 54, 55], "vertices": [1, 57, 1.02, 5.14, 1, 2, 57, -23.37, -4.93, 0.11157, 58, -4.63, 3.39, 0.88843, 1, 58, 23.48, 13.06, 1, 1, 58, 50.95, 15.33, 1, 2, 58, 85.46, 14.24, 0.95241, 59, -12.39, 11.34, 0.04759, 1, 59, 23.87, 15.18, 1, 2, 59, 57.93, 19.03, 0.99904, 60, -31.69, 29.43, 0.00096, 2, 59, 95.61, 23.92, 0.39447, 60, 5.79, 23.23, 0.60553, 1, 60, 36.5, 22.65, 1, 2, 60, 74.35, 28.57, 0.76026, 61, -11.59, 29.78, 0.23974, 3, 60, 109.01, 37.78, 0.0425, 61, 23.77, 35.72, 0.95746, 62, -61.7, 18.01, 5e-05, 2, 61, 52.61, 38.98, 0.92489, 62, -35.18, 29.79, 0.07511, 2, 61, 84.37, 38.96, 0.4283, 62, -4.87, 39.31, 0.5717, 3, 61, 120.39, 31.72, 0.01493, 62, 31.65, 43.23, 0.93951, 63, -26.4, 45.41, 0.04556, 2, 62, 56.17, 40.11, 0.69847, 63, -2.19, 40.4, 0.30153, 2, 62, 80.65, 28.11, 0.13534, 63, 21.28, 26.54, 0.86466, 6, 53, 59.35, 341.56, 0, 54, -14.61, 342.82, 0, 55, 37.77, 354.06, 1e-05, 56, -78.12, 346.33, 1e-05, 62, 93.11, 23.1, 0.01251, 63, 33.31, 20.58, 0.98747, 6, 53, 67.05, 350.34, 4e-05, 54, -6.46, 351.18, 6e-05, 55, 48.45, 358.8, 8e-05, 56, -68.26, 352.59, 9e-05, 62, 104.14, 26.93, 0.00014, 63, 44.61, 23.54, 0.99959, 5, 53, 62.45, 361.38, 6e-05, 54, -10.47, 362.45, 0.0001, 55, 48.91, 370.75, 0.00013, 56, -69.57, 364.48, 0.00015, 63, 46.98, 35.26, 0.99955, 5, 53, 73.95, 369.52, 7e-05, 54, 1.45, 369.96, 0.00011, 55, 62.76, 373.31, 0.00014, 56, -56.25, 369.05, 0.00016, 63, 61.06, 35.56, 0.99951, 5, 53, 86.71, 348.62, 4e-05, 54, 13.08, 348.42, 7e-05, 55, 65.57, 349, 9e-05, 56, -49.88, 345.42, 0.0001, 63, 59.93, 11.11, 0.99969, 5, 53, 86.45, 322.55, 0.00196, 54, 11.43, 322.4, 0.00303, 55, 54.39, 325.44, 0.00404, 56, -57.46, 320.47, 0.00457, 63, 45.11, -10.35, 0.9864, 5, 53, 116.55, 320.76, 0.00935, 54, 41.4, 319.01, 0.01577, 55, 80.96, 311.18, 0.0217, 56, -29.08, 310.28, 0.02524, 63, 69.05, -28.69, 0.92795, 7, 53, 148.1, 305.7, 0.0175, 54, 72.1, 302.29, 0.0304, 55, 103.27, 284.27, 0.0425, 56, -3.04, 286.96, 0.05063, 61, 207.1, -95.37, 0.00219, 62, 152.55, -51.92, 0.00435, 63, 86.75, -58.83, 0.85244, 7, 53, 173.88, 290.32, 0.02359, 54, 97.03, 285.56, 0.04196, 55, 120.22, 259.48, 0.05951, 56, 17.38, 264.95, 0.0724, 61, 213.38, -124.73, 0.00572, 62, 167.36, -78.04, 0.01009, 63, 99.5, -86.01, 0.78673, 7, 53, 183.26, 260.64, 0.03263, 54, 104.82, 255.43, 0.05871, 55, 116.28, 228.61, 0.08453, 56, 18.04, 233.83, 0.10589, 61, 198.03, -151.81, 0.01443, 62, 160.86, -108.48, 0.02474, 63, 90.65, -115.86, 0.67907, 8, 53, 199.29, 238.65, 0.03953, 54, 119.65, 232.61, 0.07697, 55, 121.59, 201.92, 0.11627, 56, 27.23, 208.22, 0.15436, 60, 297.26, -159.8, 1e-05, 61, 192.83, -178.51, 0.02061, 62, 163.92, -135.51, 0.03127, 63, 91.61, -143.05, 0.56097, 7, 53, 221.78, 210.43, 0.04115, 54, 140.61, 203.24, 0.09142, 55, 130.16, 166.86, 0.14998, 56, 40.87, 174.81, 0.21877, 61, 187.46, -214.2, 0.0224, 62, 169.53, -171.16, 0.03026, 63, 94.43, -179.03, 0.44601, 8, 53, 224.08, 188.5, 0.04025, 54, 141.74, 181.22, 0.09706, 55, 123.04, 145.99, 0.16912, 56, 36.91, 153.12, 0.26595, 60, 282.35, -213.72, 0.00011, 61, 172.96, -230.81, 0.02252, 62, 160.7, -191.36, 0.02865, 63, 84.06, -198.49, 0.37634, 8, 53, 233.91, 159.44, 0.03179, 54, 150.01, 151.67, 0.094, 55, 119.76, 115.49, 0.19381, 56, 38.17, 122.46, 0.38448, 60, 270.32, -241.95, 0.00012, 61, 158.37, -257.8, 0.01856, 62, 154.88, -221.49, 0.02173, 63, 75.92, -228.07, 0.25552, 7, 53, 249.1, 130.54, 0.01561, 54, 163.64, 122.01, 0.06321, 55, 121.42, 82.89, 0.17175, 56, 44.62, 90.46, 0.58755, 61, 147.54, -288.59, 0.01043, 62, 153.81, -254.11, 0.0117, 63, 72.33, -260.51, 0.13974, 7, 53, 281.67, 96.13, 0.00206, 54, 194.34, 85.92, 0.01766, 55, 136.54, 37.99, 0.06001, 56, 66.21, 48.28, 0.86206, 61, 144.51, -335.88, 0.00295, 62, 165.14, -300.12, 0.00346, 63, 80.04, -307.26, 0.05179, 7, 53, 272.29, 76.44, 0.00077, 54, 183.92, 66.75, 0.00891, 55, 119.76, 24.05, 0.03048, 56, 51.67, 32.02, 0.92878, 61, 123.7, -342.41, 0.00153, 62, 147.25, -312.61, 0.0018, 63, 61.24, -318.32, 0.02774, 7, 53, 285.02, 63.44, 0, 54, 195.94, 53.09, 0.00099, 55, 125.86, 6.91, 0.00036, 56, 60.22, 15.97, 0.99289, 61, 122.84, -360.59, 0.00024, 62, 151.89, -330.2, 0.00029, 63, 64.5, -336.22, 0.00524, 7, 53, 301.23, 35.35, 0, 54, 210.63, 24.18, 1e-05, 55, 128.77, -25.39, 0, 56, 67.88, -15.55, 0.9999, 61, 113.29, -391.58, 0, 62, 152.1, -362.63, 0, 63, 62.2, -368.57, 8e-05, 7, 53, 279.36, 13.37, 0, 54, 187.63, 3.39, 0, 55, 99.7, -36.16, 0.01426, 56, 40.71, -30.49, 0.98572, 61, 82.3, -390.52, 0, 62, 122.23, -370.94, 0, 63, 31.77, -374.53, 1e-05, 6, 54, 168.47, -10.49, 0, 55, 76.77, -41.95, 0.21652, 56, 18.88, -39.6, 0.78348, 61, 58.89, -387.18, 0, 62, 98.89, -374.79, 0, 63, 8.2, -376.56, 0, 4, 55, 58.57, -39.23, 0.59507, 56, 0.48, -39.59, 0.40493, 62, 80.99, -370.56, 0, 63, -9.31, -370.95, 0, 3, 54, 134.95, -30.93, 0.00463, 55, 38.05, -48.5, 0.90391, 56, -18.44, -51.79, 0.09146, 3, 54, 111.15, -41.63, 0.10943, 55, 11.98, -49.61, 0.88427, 56, -44.07, -56.74, 0.00631, 2, 54, 74.34, -38.69, 0.71492, 55, -21.12, -33.23, 0.28508, 4, 53, 139.73, -39.37, 0.00835, 54, 45.39, -41.84, 0.98421, 55, -49.17, -25.42, 0.00744, 61, -51.38, -324.14, 0, 4, 53, 121.12, -50.84, 0.07192, 54, 26.2, -52.3, 0.92806, 55, -70.87, -28.02, 1e-05, 61, -72.44, -318.32, 0, 4, 53, 97.81, -45.24, 0.26702, 54, 3.22, -45.47, 0.73297, 55, -89.68, -13.15, 0, 61, -84.21, -297.42, 0, 4, 53, 61.47, -24.16, 0.96628, 54, -31.95, -22.49, 0.03372, 55, -113.82, 21.23, 0, 61, -93.51, -256.46, 0, 1, 53, 36.93, -15.23, 1, 8, 6, -71.62, 176.13, 3e-05, 52, 95.15, -15.09, 0.29564, 53, 4.43, -14.42, 0.70413, 55, -161.5, 54.01, 0, 60, -16.64, -218.79, 0.00013, 61, -125.2, -208.05, 5e-05, 62, -130.53, -259.26, 1e-05, 63, -211.56, -243.58, 1e-05, 1, 52, 63.88, -12.38, 1, 1, 52, 28.78, -11.55, 1, 2, 51, 89.64, -11.84, 0.88788, 52, -6.83, -12.55, 0.11212, 1, 51, 51.68, -11.04, 1, 1, 51, 17.55, -10.45, 1, 2, 50, -7.41, 12.92, 0.42864, 51, -10.94, -8.42, 0.57136, 3, 6, 128.27, 72.35, 0.04251, 50, 11.29, -0.5, 0.95749, 57, 10.34, 149.37, 0, 3, 6, 143.24, 28.1, 0.6913, 50, 26.25, -44.76, 0.30865, 57, 25.31, 105.12, 4e-05, 4, 6, 136.54, -40.33, 0.54266, 50, 19.56, -113.19, 0.00031, 57, 18.61, 36.69, 0.44526, 58, -44.22, -40.49, 0.01177, 7, 53, 250.67, 64.9, 0.00097, 54, 161.72, 56.37, 0.01029, 55, 95.3, 22.65, 0.06463, 56, 27.67, 27.03, 0.90032, 61, 100.53, -334.43, 0.00142, 62, 122.75, -311.97, 0.00156, 63, 36.87, -315.78, 0.02082, 8, 53, 217.5, 45.25, 0.00171, 54, 127.55, 38.52, 0.02582, 55, 56.94, 18.74, 0.73081, 56, -9.68, 17.5, 0.22722, 60, 182.03, -316.2, 9e-05, 61, 63.55, -323.51, 0.00152, 62, 84.2, -312.66, 0.00139, 63, -1.62, -313.48, 0.01145, 8, 53, 183.96, 35.7, 0.00508, 54, 93.55, 30.77, 0.19373, 55, 22.49, 24.15, 0.77081, 56, -44.56, 17.77, 0.01211, 60, 150.66, -300.97, 0.0006, 61, 33.73, -305.44, 0.00299, 62, 50.32, -304.38, 0.00235, 63, -34.75, -302.6, 0.01233, 8, 53, 145.28, 19.79, 0.00881, 54, 54.08, 16.93, 0.89703, 55, -19.3, 25.94, 0.08282, 56, -86.16, 13.38, 0.00023, 60, 111.2, -287.08, 0.00103, 61, -4.27, -287.94, 0.00264, 62, 8.83, -299.11, 0.00172, 63, -75.72, -294.12, 0.00572, 7, 53, 107.74, 13.61, 0.06192, 54, 16.26, 12.76, 0.92022, 55, -55.97, 36.09, 0.00845, 60, 79.08, -266.69, 0.00184, 61, -34.35, -264.64, 0.00281, 62, -26.86, -285.94, 0.00149, 63, -110.27, -278.22, 0.00327, 8, 6, -135.01, 199.76, 3e-05, 53, 66.57, 12.31, 0.97828, 54, -24.92, 13.65, 0.00916, 55, -93.88, 52.19, 0.00176, 60, 47.51, -240.24, 0.00408, 61, -63.32, -235.37, 0.00343, 62, -63.3, -266.72, 0.00137, 63, -145.11, -256.24, 0.00189, 4, 53, 105.31, -25.26, 0.18499, 54, 11.77, -25.92, 0.81501, 55, -74.49, 1.84, 0, 61, -64.47, -289.32, 0, 3, 6, 98.13, -4.87, 0.97606, 57, -19.8, 72.15, 0.01259, 58, -3.89, -73.76, 0.01136, 4, 6, 80.35, 49.54, 0.41376, 50, -36.63, -23.31, 0.14509, 57, -37.58, 126.56, 0, 51, 2.52, 36.15, 0.44114, 5, 6, 40.45, 71.93, 0.2555, 50, -76.54, -0.92, 0.00044, 51, 47.91, 30.4, 0.73478, 52, -52.38, 25.55, 0.0088, 60, -132.21, -118.48, 0.00049, 5, 6, -3.32, 86.5, 0.19983, 51, 93.95, 33.35, 0.44039, 52, -6.83, 32.85, 0.34318, 59, 50.52, -151.33, 0.00164, 60, -87.96, -131.54, 0.01495, 9, 6, -53.3, 102.42, 0.11055, 51, 146.24, 37.39, 0.01429, 52, 44.85, 41.83, 0.77785, 53, -59.76, 26.19, 0.01986, 59, 102.96, -150.36, 0.00909, 60, -37.47, -145.75, 0.06437, 61, -139.15, -133.38, 0.00375, 62, -166.27, -192.24, 0.00027, 63, -241.99, -173.98, 0, 10, 6, -102.89, 116.8, 0.04486, 52, 95.47, 51.97, 0.45845, 53, -13.98, 50.06, 0.33758, 54, -103.34, 55.64, 0.0034, 55, -151.14, 120.27, 0.00148, 59, 154.54, -148.06, 0.00392, 60, 12.58, -158.43, 0.11563, 61, -90.5, -150.67, 0.02644, 62, -114.67, -194.1, 0.00526, 63, -190.69, -179.85, 0.00298, 11, 6, -144.62, 137.43, 0.01203, 52, 142.01, 52.97, 0.10514, 53, 30.44, 64, 0.63647, 54, -58.25, 67.19, 0.04994, 55, -104.98, 114.28, 0.01251, 56, -183.92, 88.1, 0.00104, 59, 200.68, -154.19, 5e-05, 60, 54.99, -177.62, 0.09663, 61, -50.05, -173.71, 0.0529, 62, -69.17, -203.93, 0.01674, 63, -146.09, -193.18, 0.01656, 10, 6, -189.71, 155.89, 0.00179, 52, 190.53, 57.42, 0.0161, 53, 75.79, 81.82, 0.46474, 54, -12.02, 82.57, 0.22945, 55, -56.34, 111.41, 0.06033, 56, -135.39, 92.45, 0.01192, 60, 100.68, -194.53, 0.06146, 61, -6.13, -194.81, 0.06874, 62, -20.94, -210.85, 0.03291, 63, -98.54, -203.81, 0.05256, 9, 52, 242.14, 61.26, 0.0008, 53, 124.28, 99.9, 0.19498, 54, 37.37, 98.05, 0.3227, 55, -4.73, 107.47, 0.1862, 56, -83.77, 96.17, 0.06279, 60, 148.9, -213.34, 0.02625, 61, 40.13, -218.02, 0.05603, 62, 30.16, -219.08, 0.03885, 63, -48.24, -215.99, 0.11139, 8, 53, 170.6, 117.49, 0.0734, 54, 84.56, 113.15, 0.20167, 55, 44.69, 103.99, 0.27971, 56, -34.38, 100.01, 0.20652, 60, 195.16, -231.07, 0.00771, 61, 84.54, -239.98, 0.03336, 62, 79.12, -226.68, 0.03024, 63, -0.02, -227.36, 0.16739, 8, 53, 202.64, 135.43, 0.03972, 54, 117.51, 129.36, 0.11941, 55, 81.3, 106.83, 0.23441, 56, 1.41, 108.22, 0.34935, 60, 231.01, -239.03, 0.00194, 61, 119.49, -251.23, 0.02204, 62, 115.84, -226.91, 0.02344, 63, 36.57, -230.44, 0.2097, 4, 6, 63.06, -38.41, 0.49778, 50, -53.93, -111.26, 0, 57, -54.87, 38.61, 0.0186, 58, 29.26, -38.32, 0.48362, 3, 6, 23.24, -35.53, 0.51996, 58, 69.17, -38.97, 0.42811, 59, -13.83, -44.3, 0.05192, 6, 6, -28.77, -25.67, 0.41069, 52, -36.09, 144.09, 0.01032, 53, -166.01, 101.81, 0.0001, 58, 121.65, -45.92, 0.01256, 59, 38.59, -36.93, 0.56019, 60, -66.36, -18.57, 0.00614, 7, 6, -86.09, -10.33, 0.13172, 51, 134.25, 154.19, 4e-05, 52, 21.84, 156.96, 0.06332, 53, -113.98, 130.34, 0.01087, 59, 97.81, -33.06, 0.39199, 60, -8.55, -31.95, 0.40133, 61, -99.77, -22.77, 0.00072, 11, 6, -132.14, 0.4, 0.04154, 52, 67.64, 168.72, 0.06585, 53, -73.28, 154.41, 0.03944, 54, -157.01, 162.99, 0.00164, 55, -161.17, 239.87, 0.00063, 56, -258.03, 204.03, 0, 59, 144.86, -28.43, 0.01843, 60, 37.84, -41.1, 0.79112, 61, -54.43, -36.19, 0.03856, 62, -114.67, -74.08, 0.00242, 63, -181.38, -60.19, 0.00037, 10, 6, -169.59, 4.21, 0.01277, 52, 102.6, 182.65, 0.04089, 53, -43.59, 177.54, 0.05615, 54, -126.13, 184.5, 0.00621, 55, -124.51, 248.4, 0.00276, 56, -223.03, 217.88, 0.00037, 60, 75.39, -43.63, 0.60564, 61, -17.28, -42.2, 0.25579, 62, -77.43, -68.65, 0.01558, 63, -143.83, -57.66, 0.00385, 10, 6, -208.71, 6.94, 0.00252, 52, 138.56, 198.3, 0.01855, 53, -13.43, 202.6, 0.05828, 54, -94.68, 207.93, 0.01273, 55, -86.61, 258.49, 0.00674, 56, -187.04, 233.45, 0.00221, 60, 114.58, -45.01, 0.18337, 61, 21.62, -47.23, 0.62116, 62, -38.82, -61.75, 0.07747, 63, -104.81, -53.78, 0.01696, 10, 6, -256.59, 5.53, 1e-05, 52, 180.37, 221.67, 0.00379, 53, 20.2, 236.71, 0.03789, 54, -59.28, 240.2, 0.01513, 55, -41.77, 275.33, 0.01031, 56, -145.17, 256.72, 0.00573, 60, 162.39, -41.97, 0.01933, 61, 69.5, -48.65, 0.43852, 62, 7.27, -48.72, 0.40464, 63, -57.84, -44.36, 0.06465, 9, 52, 219.78, 236.61, 0.00044, 53, 53.88, 262.05, 0.02398, 54, -24.31, 263.72, 0.01551, 55, -0.57, 284.2, 0.01376, 56, -105.73, 271.57, 0.01091, 60, 204.38, -45.49, 0.00262, 61, 110.98, -56.06, 0.0921, 62, 49.06, -43.32, 0.52551, 63, -15.75, -42.22, 0.31517, 7, 53, 124.77, 292.62, 0.01629, 54, 48.11, 290.47, 0.02551, 55, 76.61, 282.19, 0.03434, 56, -29.1, 280.97, 0.03968, 61, 181.64, -87.18, 0.00504, 62, 125.8, -51.77, 0.01895, 63, 60.1, -56.6, 0.86019, 11, 6, -159.23, 69.84, 0.03438, 52, 123.75, 119.66, 0.14482, 53, -5.71, 122.95, 0.25008, 54, -91.21, 127.98, 0.02833, 55, -113.04, 182.96, 0.01121, 56, -202.04, 154.84, 0.0019, 59, 192.82, -85.49, 0.00191, 60, 67.29, -109.57, 0.31955, 61, -31.48, -107.1, 0.1541, 62, -71.47, -134.82, 0.03368, 63, -143.02, -124.09, 0.02004, 10, 6, -207.5, 80.33, 0.00668, 52, 171.4, 132.66, 0.04755, 53, 36.42, 148.73, 0.245, 54, -47.76, 151.48, 0.07387, 55, -63.98, 188.67, 0.03586, 56, -154.35, 167.73, 0.01312, 60, 115.88, -118.4, 0.16853, 61, 16.09, -120.42, 0.23421, 62, -22.1, -133.22, 0.096, 63, -93.68, -126.34, 0.07919, 10, 6, -249.41, 90.16, 0.00065, 52, 213.11, 143.3, 0.01264, 53, 73.51, 170.59, 0.17857, 54, -9.57, 171.34, 0.10227, 55, -21.14, 192.94, 0.067, 56, -112.62, 178.28, 0.03755, 60, 158.11, -126.8, 0.07018, 61, 57.35, -132.71, 0.19302, 62, 20.94, -132.54, 0.14642, 63, -50.71, -129, 0.19171, 9, 52, 254.35, 152.93, 0.00177, 53, 110.42, 191.34, 0.1094, 54, 28.4, 190.1, 0.10276, 55, 21.07, 196.28, 0.09432, 56, -71.35, 187.81, 0.07623, 60, 199.46, -135.91, 0.02345, 61, 97.68, -145.62, 0.10934, 62, 63.29, -132.74, 0.13145, 63, -8.51, -132.48, 0.35128, 9, 52, 292.83, 159.54, 2e-05, 53, 145.53, 208.42, 0.06555, 54, 64.36, 205.29, 0.09058, 55, 60.11, 197.05, 0.11009, 56, -32.86, 194.33, 0.11811, 60, 237.03, -146.54, 0.00626, 61, 134.09, -159.71, 0.05187, 62, 102.25, -135.23, 0.07663, 63, 30.14, -137.99, 0.48089, 7, 53, 71.09, 305.91, 0.00294, 54, -4.78, 306.6, 0.0033, 55, 33.47, 316.78, 0.00391, 56, -76.87, 308.82, 0.00405, 61, 154.83, -38.82, 0.00205, 62, 85.7, -13.7, 0.03453, 63, 23.08, -15.53, 0.94922, 8, 53, 95.38, 279.66, 0.01824, 54, 18.07, 279.1, 0.02061, 55, 44.49, 282.76, 0.02434, 56, -60.95, 276.8, 0.02541, 60, 247.07, -60, 0.00027, 61, 152.13, -74.48, 0.01981, 62, 93.85, -48.51, 0.11033, 63, 28.49, -50.88, 0.78099, 8, 53, 176.49, 227.92, 0.04316, 54, 96.31, 223.12, 0.07748, 55, 96.39, 201.76, 0.11175, 56, 2.33, 204.34, 0.14176, 60, 273.11, -152.62, 0.00077, 61, 169.45, -169.11, 0.02513, 62, 138.8, -133.57, 0.03917, 63, 66.71, -139.17, 0.56077], "hull": 56, "edges": [0, 110, 0, 2, 2, 4, 26, 28, 30, 32, 38, 40, 40, 42, 46, 48, 52, 54, 58, 60, 60, 62, 64, 66, 70, 72, 74, 76, 78, 80, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 104, 106, 106, 108, 108, 110, 4, 6, 6, 8, 100, 102, 102, 104, 98, 100, 96, 98, 8, 10, 10, 12, 12, 14, 14, 16, 90, 92, 16, 18, 18, 20, 20, 22, 80, 82, 76, 78, 72, 74, 68, 70, 66, 68, 62, 64, 56, 58, 54, 56, 50, 52, 48, 50, 44, 46, 42, 44, 36, 38, 22, 24, 24, 26, 28, 30, 32, 34, 34, 36], "width": 422, "height": 553}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.49072, 0.00077, 0.55478, 0.00801, 0.61739, 0.02689, 0.68347, 0.05935, 0.72961, 0.0986, 0.77563, 0.14572, 0.81957, 0.19524, 0.87755, 0.25115, 0.92604, 0.30906, 0.95142, 0.37541, 0.94128, 0.42733, 0.90823, 0.45881, 0.95991, 0.50905, 0.99427, 0.56054, 0.99742, 0.60138, 0.9778, 0.6362, 0.93062, 0.66294, 0.90835, 0.6974, 0.87725, 0.73225, 0.84001, 0.76229, 0.79569, 0.7861, 0.74479, 0.79879, 0.69319, 0.78837, 0.68747, 0.74874, 0.68176, 0.70911, 0.72009, 0.70316, 0.75705, 0.68262, 0.78366, 0.65892, 0.76207, 0.64439, 0.78956, 0.6114, 0.78791, 0.56549, 0.76654, 0.50563, 0.73671, 0.43638, 0.73003, 0.39864, 0.70329, 0.31917, 0.68553, 0.25801, 0.66184, 0.2056, 0.63131, 0.15328, 0.59586, 0.10199, 0.56464, 0.07249, 0.52879, 0.05657, 0.4959, 0.05595, 0.47505, 0.07669, 0.46857, 0.10903, 0.44941, 0.11313, 0.43911, 0.08606, 0.41081, 0.06947, 0.37681, 0.07091, 0.34261, 0.0899, 0.31327, 0.12641, 0.28815, 0.18118, 0.27113, 0.24308, 0.26546, 0.31111, 0.25281, 0.39349, 0.22804, 0.46596, 0.18283, 0.52062, 0.19199, 0.5769, 0.21359, 0.62182, 0.25217, 0.6665, 0.3071, 0.7099, 0.35363, 0.75768, 0.38862, 0.80627, 0.40806, 0.84578, 0.40187, 0.88677, 0.3493, 0.92971, 0.30879, 0.95126, 0.26731, 0.97964, 0.2125, 0.99735, 0.15584, 0.99042, 0.13809, 0.97278, 0.15171, 0.93589, 0.18861, 0.93005, 0.21771, 0.90981, 0.23578, 0.88809, 0.23002, 0.85652, 0.19068, 0.81466, 0.13237, 0.77962, 0.1025, 0.76314, 0.05457, 0.71704, 0.02548, 0.66573, 0.00747, 0.60465, 0.00341, 0.53897, 0.01606, 0.47212, 0.0421, 0.40271, 0.07192, 0.3359, 0.09464, 0.26045, 0.11576, 0.19134, 0.15576, 0.1276, 0.20638, 0.08265, 0.26041, 0.04755, 0.31343, 0.02638, 0.36743, 0.01127, 0.43354, 0.00284, 0.72881, 0.74552, 0.77874, 0.73377, 0.81589, 0.71116, 0.84259, 0.6777, 0.86698, 0.64243, 0.88555, 0.59722, 0.8751, 0.5511, 0.85304, 0.49594, 0.83331, 0.44524, 0.84143, 0.38917, 0.82053, 0.33311, 0.79383, 0.27071, 0.75668, 0.21965, 0.72068, 0.1663, 0.68121, 0.12199, 0.64234, 0.07966, 0.59666, 0.04796, 0.54335, 0.03056, 0.49553, 0.02524, 0.44668, 0.03877, 0.45858, 0.08166, 0.39317, 0.03484, 0.34586, 0.04702, 0.29841, 0.06902, 0.26174, 0.10053, 0.22204, 0.15028, 0.19063, 0.21818, 0.17391, 0.28502, 0.15942, 0.36054, 0.13267, 0.43592, 0.10704, 0.49582, 0.09812, 0.55366, 0.11038, 0.6153, 0.13825, 0.67432, 0.18617, 0.7238, 0.24301, 0.75765, 0.29205, 0.80594, 0.31991, 0.86237, 0.31434, 0.9075], "triangles": [20, 21, 93, 21, 22, 93, 22, 23, 93, 93, 94, 20, 20, 94, 19, 23, 24, 93, 24, 25, 93, 93, 25, 94, 25, 26, 94, 94, 26, 95, 94, 95, 19, 19, 95, 18, 95, 96, 18, 18, 96, 17, 26, 27, 95, 95, 27, 96, 96, 97, 17, 17, 97, 16, 96, 27, 97, 27, 29, 97, 27, 28, 29, 15, 16, 98, 16, 97, 98, 97, 29, 98, 15, 98, 14, 29, 30, 98, 98, 13, 14, 30, 99, 98, 98, 99, 13, 30, 100, 99, 99, 12, 13, 99, 100, 12, 30, 31, 100, 100, 11, 12, 31, 101, 100, 31, 32, 101, 100, 101, 11, 10, 11, 102, 11, 101, 102, 101, 32, 102, 32, 33, 102, 10, 102, 9, 33, 103, 102, 102, 8, 9, 33, 34, 103, 102, 103, 8, 34, 104, 103, 103, 7, 8, 103, 104, 7, 34, 35, 104, 35, 105, 104, 104, 6, 7, 104, 105, 6, 35, 36, 105, 105, 5, 6, 36, 106, 105, 105, 106, 5, 36, 37, 106, 37, 107, 106, 106, 4, 5, 106, 107, 4, 107, 37, 38, 107, 3, 4, 66, 67, 71, 68, 69, 71, 67, 68, 71, 71, 69, 70, 71, 72, 66, 66, 72, 65, 65, 72, 131, 65, 131, 64, 131, 72, 73, 64, 131, 63, 131, 130, 63, 131, 73, 130, 73, 74, 130, 63, 130, 62, 74, 129, 130, 130, 61, 62, 130, 129, 61, 74, 75, 129, 75, 128, 129, 75, 76, 128, 129, 60, 61, 129, 128, 60, 76, 127, 128, 128, 59, 60, 59, 128, 58, 128, 127, 58, 76, 77, 127, 77, 126, 127, 77, 78, 126, 127, 126, 58, 78, 79, 126, 79, 125, 126, 126, 57, 58, 126, 125, 57, 79, 80, 125, 125, 56, 57, 80, 124, 125, 125, 124, 56, 80, 81, 124, 124, 55, 56, 124, 123, 55, 124, 81, 123, 81, 82, 123, 55, 123, 122, 123, 82, 122, 54, 55, 122, 82, 83, 122, 54, 122, 53, 122, 121, 53, 122, 83, 121, 83, 84, 121, 53, 121, 52, 121, 120, 52, 121, 84, 120, 84, 85, 120, 52, 120, 51, 120, 119, 51, 120, 85, 119, 85, 86, 119, 51, 119, 50, 119, 86, 118, 119, 118, 50, 118, 86, 87, 107, 108, 3, 109, 1, 2, 50, 118, 49, 111, 0, 1, 49, 116, 48, 117, 89, 116, 43, 44, 113, 44, 45, 113, 43, 113, 42, 113, 45, 112, 42, 113, 112, 112, 45, 46, 42, 112, 41, 46, 114, 112, 112, 111, 41, 114, 92, 112, 112, 0, 111, 112, 92, 0, 47, 114, 46, 47, 115, 114, 116, 90, 115, 116, 89, 90, 115, 91, 114, 115, 90, 91, 114, 91, 92, 88, 89, 117, 48, 115, 47, 48, 116, 115, 117, 116, 49, 40, 41, 111, 110, 40, 111, 118, 117, 49, 118, 88, 117, 87, 88, 118, 110, 111, 1, 40, 110, 39, 39, 110, 109, 110, 1, 109, 38, 39, 109, 109, 2, 108, 38, 109, 108, 108, 2, 3, 107, 38, 108], "vertices": [2, 19, 27.82, -14.23, 0.98602, 86, -171.48, 17.55, 0.01398, 2, 19, 22.31, -28.96, 0.98571, 86, -177, 2.82, 0.01429, 3, 19, 13.32, -42.58, 0.83924, 20, 8.7, 9.61, 0.14601, 86, -185.98, -10.79, 0.01475, 4, 19, 0.01, -56.12, 0.47374, 20, 27.64, 10.93, 0.3228, 21, -4.59, 9.92, 0.18527, 86, -199.29, -24.34, 0.01819, 3, 19, -14.33, -64.47, 0.14769, 21, 11.6, 13.6, 0.83196, 86, -213.64, -32.69, 0.02034, 3, 21, 29.91, 16.03, 0.94041, 22, -15.5, 13.56, 0.0376, 86, -230.38, -40.5, 0.02199, 3, 21, 48.62, 17.66, 0.28246, 22, 2.65, 18.39, 0.69447, 86, -247.74, -47.65, 0.02307, 4, 21, 70.75, 21.26, 0.00115, 22, 23.83, 25.76, 0.94827, 23, -25.38, 26.29, 0.02426, 86, -267.78, -57.71, 0.02632, 3, 22, 44.81, 30.74, 0.61788, 23, -4.3, 30.84, 0.35352, 86, -287.94, -65.39, 0.0286, 3, 22, 66.38, 29.55, 0.16141, 23, 17.24, 29.2, 0.81434, 86, -309.48, -67.02, 0.02425, 3, 22, 80.8, 21.76, 0.01898, 23, 31.49, 21.11, 0.96645, 86, -324.79, -61.17, 0.01457, 3, 23, 37.79, 10.1, 0.97415, 24, -14.48, 4.92, 0.02229, 86, -332.68, -51.24, 0.00356, 3, 23, 56.92, 16.23, 0.1723, 24, 0.83, 17.92, 0.82375, 86, -350.66, -60.18, 0.00395, 1, 24, 16.65, 26.72, 1, 2, 24, 29.37, 27.84, 0.97658, 25, -26.02, 16.4, 0.02342, 2, 24, 40.37, 23.39, 0.87727, 25, -14.36, 18.58, 0.12273, 2, 24, 49.03, 12.16, 0.43147, 25, -1.01, 13.8, 0.56853, 2, 24, 59.93, 7.06, 0.01595, 25, 10.92, 15.38, 0.98405, 2, 25, 24.16, 15.25, 0.96578, 26, -16.6, 6.53, 0.03422, 2, 25, 37, 13.05, 0.54861, 26, -5.05, 12.56, 0.45139, 2, 25, 49.21, 8.33, 0.07355, 26, 7.51, 16.2, 0.92645, 1, 26, 20.5, 16.04, 1, 1, 26, 31.37, 8.99, 1, 1, 26, 28.78, -3.18, 1, 2, 25, 44.94, -28.07, 0.01161, 26, 26.18, -15.35, 0.98839, 3, 24, 63.02, -38.62, 0.0011, 25, 38.16, -21.42, 0.10459, 26, 16.76, -14.16, 0.89432, 3, 24, 56.36, -29.82, 0.02969, 25, 27.81, -17.61, 0.48873, 26, 6.21, -17.4, 0.48158, 3, 24, 48.79, -23.57, 0.17924, 25, 18.06, -16.42, 0.71948, 26, -2.26, -22.37, 0.10128, 4, 23, 79.18, -43.76, 0.00042, 24, 44.4, -28.94, 0.30885, 25, 17.26, -23.31, 0.65012, 26, 1.28, -28.33, 0.04061, 4, 23, 71.95, -33.84, 0.01873, 24, 33.92, -22.56, 0.60218, 25, 4.99, -23.59, 0.37306, 26, -8.31, -35.98, 0.00602, 3, 23, 58.43, -29.1, 0.16755, 24, 19.62, -23.36, 0.7797, 25, -6.62, -31.98, 0.05275, 3, 23, 39.13, -27.28, 0.69142, 24, 1.09, -29.08, 0.30846, 25, -19.13, -46.79, 0.00012, 3, 22, 66.66, -25.98, 0.0685, 23, 16.36, -26.33, 0.91623, 24, -20.3, -36.94, 0.01527, 2, 22, 55.03, -23.53, 0.31385, 23, 4.79, -23.63, 0.68615, 2, 22, 29.49, -21.27, 0.97999, 23, -20.69, -20.84, 0.02001, 2, 21, 49.51, -20.34, 0.15805, 22, 10.08, -18.88, 0.84195, 2, 21, 32.45, -17.24, 0.85288, 22, -7.26, -18.78, 0.14712, 3, 19, -25.93, -37.51, 0.27409, 20, 34.72, -20.2, 0.04669, 21, 14.59, -15.6, 0.67922, 3, 19, -8.46, -32.49, 0.63125, 20, 18.32, -12.38, 0.29562, 21, -3.58, -15.17, 0.07313, 3, 19, 2.14, -27.03, 0.872, 20, 6.76, -9.44, 0.12792, 21, -15.33, -17.2, 8e-05, 1, 19, 8.84, -19.57, 1, 1, 19, 10.73, -11.8, 1, 1, 19, 5.48, -5.48, 1, 1, 19, -4.04, -1.79, 1, 1, 19, -4.3, 3.03, 1, 1, 19, 4.48, 3.68, 1, 1, 19, 11, 9.3, 1, 1, 19, 12.31, 17.47, 1, 3, 19, 8.29, 26.85, 0.90874, 27, 4.16, 12.74, 0.09121, 20, -33.56, -45.69, 4e-05, 3, 19, -1.33, 36.23, 0.5919, 27, 17.52, 14.19, 0.39227, 28, -11.55, 20.41, 0.01583, 3, 19, -16.73, 45.83, 0.23393, 27, 34.71, 19.98, 0.28009, 28, 6.54, 18.94, 0.48598, 3, 27, 52.44, 28.7, 0.00809, 28, 26.27, 19.94, 0.93416, 29, -21.63, 18.31, 0.05775, 2, 28, 47.13, 24.1, 0.38696, 29, -1.16, 24.09, 0.61304, 3, 28, 72.75, 27.78, 0.00764, 29, 24.1, 29.75, 0.96318, 30, -20.59, 38.83, 0.02918, 2, 29, 47.42, 31.61, 0.67479, 30, 2.02, 32.81, 0.32521, 3, 29, 67.16, 26.94, 0.15581, 30, 19.07, 21.83, 0.79737, 31, -10.75, 32, 0.04682, 3, 29, 82.98, 34.88, 0.00365, 30, 36.63, 24.05, 0.57097, 31, 5.43, 24.83, 0.42538, 3, 30, 50.65, 29.3, 0.14542, 31, 20.14, 22.07, 0.82451, 32, -18.55, 27.48, 0.03008, 3, 30, 64.59, 38.68, 0.00889, 31, 36.92, 22.88, 0.62139, 32, -2.09, 24.14, 0.36973, 2, 31, 55.41, 27.31, 0.07675, 32, 16.92, 23.88, 0.92325, 2, 32, 35.44, 21.2, 0.8804, 33, -20.8, 7.41, 0.1196, 2, 32, 52.14, 16.38, 0.30912, 33, -7.12, 18.13, 0.69088, 2, 32, 64.16, 10.93, 0.03431, 33, 4.34, 24.69, 0.96569, 1, 33, 17.21, 25.16, 1, 2, 33, 32.4, 14.58, 0.94079, 34, -12.99, 6.81, 0.05921, 2, 33, 40.55, 5.88, 0.37667, 34, -1.22, 8.71, 0.62333, 1, 34, 11.67, 12.42, 1, 1, 34, 26.07, 11.75, 1, 1, 34, 37.61, 3.93, 1, 1, 34, 39.17, -2.88, 1, 1, 34, 31.27, -11.89, 1, 3, 32, 44.66, -45.24, 0.00057, 33, 38.48, -23.99, 0.0212, 34, 22.39, -9.72, 0.97823, 3, 32, 45.26, -35.78, 0.01517, 33, 31.16, -17.97, 0.21921, 34, 13.3, -12.42, 0.76562, 3, 32, 43.63, -27.87, 0.08658, 33, 23.79, -14.67, 0.6072, 34, 6.44, -16.68, 0.30623, 3, 32, 35.71, -21.84, 0.39822, 33, 14.27, -17.56, 0.56618, 34, 3.51, -26.19, 0.0356, 3, 31, 68.76, -13.81, 0.01684, 32, 19.73, -19.26, 0.89546, 33, 2.82, -29, 0.0877, 3, 31, 52.07, -20.28, 0.40447, 32, 1.97, -21.43, 0.59385, 33, -5.81, -44.67, 0.00168, 2, 31, 43.92, -23.84, 0.70299, 32, -6.81, -22.86, 0.29701, 2, 31, 25.58, -26.37, 0.98346, 32, -25.2, -20.8, 0.01654, 2, 30, 64.35, -16.41, 0.10479, 31, 8.22, -24.14, 0.89521, 3, 30, 45.29, -20.79, 0.70976, 31, -10.35, -18.03, 0.28594, 86, -330.66, 172.32, 0.00429, 2, 30, 24.8, -21.77, 0.98771, 86, -310.42, 168.93, 0.01229, 3, 29, 66.4, -16.32, 0.20901, 30, 3.94, -18.7, 0.77006, 86, -290.7, 161.5, 0.02092, 3, 29, 43.87, -17.57, 0.94149, 30, -17.72, -12.37, 0.03485, 86, -270.88, 150.71, 0.02366, 3, 28, 66.76, -19.32, 0.00241, 29, 21.8, -17.68, 0.97304, 86, -252.05, 139.21, 0.02455, 3, 28, 42.6, -20.08, 0.57253, 29, -2.23, -20.32, 0.39921, 86, -230.22, 128.81, 0.02826, 3, 28, 20.44, -20.7, 0.9602, 29, -24.27, -22.66, 0.00763, 86, -210.24, 119.22, 0.03217, 4, 19, 6.44, 73.71, 0.21164, 27, 41.51, -15.63, 0.2714, 28, -1.28, -16.46, 0.48463, 86, -192.87, 105.5, 0.03233, 4, 19, 17.53, 58.72, 0.57375, 27, 22.88, -14.65, 0.39291, 28, -18.01, -8.2, 0.00589, 86, -181.77, 90.5, 0.02744, 3, 19, 25.44, 43.56, 0.88875, 27, 6.15, -11.13, 0.09067, 86, -173.86, 75.34, 0.02059, 2, 19, 29.16, 29.57, 0.98407, 86, -170.14, 61.35, 0.01593, 2, 19, 30.98, 15.74, 0.98609, 86, -168.32, 47.53, 0.01391, 2, 19, 30.14, -0.51, 0.98387, 86, -169.16, 31.27, 0.01613, 2, 25, 47.89, -12.23, 0.00299, 26, 18.93, -0.95, 0.99701, 3, 24, 72.16, -24.1, 0.00019, 25, 38.03, -4.27, 0.01352, 26, 6.26, -0.59, 0.98629, 3, 24, 64.86, -15.28, 0.00071, 25, 27.12, -0.78, 0.97366, 26, -4.54, -4.43, 0.02563, 3, 24, 54.24, -9.09, 0.00817, 25, 14.84, -1.29, 0.98711, 26, -13.99, -12.28, 0.00472, 3, 23, 87.72, -19.73, 0.00017, 24, 43.07, -3.48, 0.3749, 25, 2.41, -2.59, 0.62493, 1, 24, 28.84, 0.64, 1, 3, 23, 61.81, -7.71, 0.01093, 24, 14.53, -2.31, 0.98802, 25, -22.26, -17, 0.00105, 2, 23, 43.82, -6.56, 0.80892, 24, -2.52, -8.15, 0.19108, 2, 23, 27.33, -5.39, 0.98693, 24, -18.2, -13.4, 0.01307, 2, 23, 11.7, 2.71, 0.99859, 86, -308, -39.99, 0.00141, 3, 22, 43.21, 4.08, 0.94291, 23, -6.45, 4.21, 0.05245, 86, -289.83, -38.74, 0.00463, 2, 22, 22.7, 4.55, 0.99228, 86, -269.43, -36.54, 0.00772, 3, 21, 47.67, 0.61, 0.10355, 22, 4.65, 1.43, 0.88893, 86, -251.94, -31.1, 0.00751, 2, 21, 28.88, 1.25, 0.99154, 86, -233.82, -26.08, 0.00846, 4, 19, -18.97, -51.43, 0.22988, 20, 38.74, -5.17, 0.00091, 21, 12.12, -0.24, 0.76153, 86, -218.27, -19.65, 0.00768, 4, 19, -4.06, -45, 0.53813, 20, 23.32, -0.09, 0.4193, 21, -4.03, -1.9, 0.03518, 86, -203.36, -13.22, 0.00739, 3, 19, 7.97, -36.26, 0.83264, 20, 8.52, 1.34, 0.16027, 86, -191.34, -4.48, 0.00708, 2, 19, 16.02, -24.75, 0.99288, 86, -183.28, 7.03, 0.00712, 2, 19, 20.11, -13.75, 0.99326, 86, -179.19, 18.03, 0.00674, 2, 19, 18.51, -1.25, 0.99547, 86, -180.8, 30.53, 0.00453, 1, 19, 4.82, -1.24, 1, 2, 19, 22.47, 11.19, 0.99336, 86, -176.84, 42.98, 0.00664, 2, 19, 21.2, 23.24, 0.99333, 86, -178.11, 55.02, 0.00667, 3, 19, 16.94, 35.96, 0.85336, 27, 5.69, 0.27, 0.1383, 86, -182.37, 67.74, 0.00834, 3, 19, 9.22, 46.76, 0.60046, 27, 18.93, -0.64, 0.3878, 86, -190.08, 78.54, 0.01174, 4, 19, -3.9, 59.48, 0.24293, 27, 37.09, 1.4, 0.29744, 28, 1.38, 0.93, 0.4463, 86, -203.2, 91.26, 0.01333, 2, 28, 23.82, -0.96, 0.98946, 86, -222.28, 103.22, 0.01054, 3, 28, 45.02, 0.51, 0.60919, 29, -1.42, 0.4, 0.38515, 86, -241.8, 111.62, 0.00566, 1, 29, 21.97, 4.93, 1, 2, 29, 46.31, 6.64, 0.91564, 30, -7.35, 9.64, 0.08436, 3, 29, 66, 6.99, 0.01871, 30, 11.33, 3.41, 0.98045, 31, -26.9, 20.24, 0.00084, 3, 29, 83.74, 10.96, 0.00054, 30, 29.38, 1.24, 0.99113, 31, -12.57, 9.05, 0.00833, 2, 30, 48.61, 4.22, 0.08619, 31, 5.43, 1.66, 0.91381, 1, 31, 24.69, -2.07, 1, 2, 31, 43.92, -0.09, 0.57679, 32, -0.95, 0.15, 0.42321, 2, 31, 60.11, 6.27, 4e-05, 32, 16.3, 2.33, 0.99996, 1, 32, 35.36, -0.02, 1, 1, 33, 12.73, 4.31, 1, 2, 33, 26.85, 5.12, 0.99933, 34, -8.25, -3.07, 0.00067], "hull": 93, "edges": [2, 4, 4, 6, 16, 18, 26, 28, 28, 30, 68, 70, 110, 112, 132, 134, 134, 136, 136, 138, 138, 140, 160, 162, 162, 164, 172, 174, 182, 184, 86, 88, 84, 86, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 2, 0, 0, 184, 74, 76, 66, 68, 62, 64, 64, 66, 58, 60, 60, 62, 56, 58, 52, 54, 54, 56, 40, 42, 42, 44, 48, 50, 50, 52, 36, 38, 38, 40, 34, 36, 30, 32, 32, 34, 22, 24, 24, 26, 18, 20, 20, 22, 6, 8, 14, 16, 12, 14, 98, 100, 100, 102, 70, 72, 72, 74, 8, 10, 10, 12, 102, 104, 106, 108, 108, 110, 104, 106, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 140, 142, 142, 144, 144, 146, 146, 148, 154, 156, 152, 154, 148, 150, 150, 152, 156, 158, 158, 160, 178, 180, 180, 182, 174, 176, 176, 178, 170, 172, 168, 170, 164, 166, 166, 168, 44, 46, 46, 48], "width": 243, "height": 312}}, "head": {"head": {"type": "mesh", "uvs": [0.5828, 0.00171, 0.69729, 0.00875, 0.78992, 0.04543, 0.85394, 0.12449, 0.89635, 0.20795, 0.94463, 0.31462, 0.97155, 0.41216, 0.9889, 0.51036, 0.98232, 0.69167, 0.94529, 0.78359, 0.80847, 0.92557, 0.72, 0.98219, 0.59594, 0.99615, 0.44382, 0.95526, 0.2363, 0.87729, 0.14452, 0.80212, 0.07894, 0.69227, 0.02691, 0.55765, 0.0112, 0.44892, 0.01006, 0.27915, 0.05335, 0.18313, 0.14044, 0.10469, 0.2829, 0.03632, 0.40824, 0.01015, 0.18499, 0.5115, 0.21326, 0.48774, 0.25271, 0.46696, 0.29629, 0.45974, 0.34634, 0.45974, 0.38815, 0.47077, 0.4176, 0.48774, 0.43939, 0.50811, 0.21031, 0.53186, 0.25095, 0.54502, 0.29864, 0.54502, 0.34045, 0.53483, 0.37873, 0.51956, 0.4123, 0.51235, 0.16175, 0.49888, 0.197, 0.47347, 0.24918, 0.4517, 0.29982, 0.44406, 0.34988, 0.4466, 0.39286, 0.46357, 0.42466, 0.48267, 0.4504, 0.50482, 0.6756, 0.47328, 0.68676, 0.44915, 0.71253, 0.42533, 0.74775, 0.40552, 0.78984, 0.39655, 0.83493, 0.39748, 0.86671, 0.40583, 0.89395, 0.41852, 0.87892, 0.44296, 0.85143, 0.46245, 0.81192, 0.47576, 0.77026, 0.48102, 0.72989, 0.47978, 0.70069, 0.47514, 0.66312, 0.4717, 0.67834, 0.44493, 0.70904, 0.41761, 0.74509, 0.3974, 0.78681, 0.38586, 0.83532, 0.38381, 0.87712, 0.39143, 0.91146, 0.40519, 0.16107, 0.54549, 0.21857, 0.56514, 0.28842, 0.56858, 0.35041, 0.55305, 0.40352, 0.53063, 0.44988, 0.51799, 0.669, 0.48364, 0.71668, 0.48848, 0.76822, 0.49831, 0.83332, 0.49741, 0.93075, 0.44122, 0.89937, 0.47703, 0.13011, 0.42342, 0.20745, 0.38778, 0.46065, 0.4107, 0.12007, 0.41282, 0.18656, 0.35767, 0.23531, 0.34875, 0.45515, 0.36436, 0.47289, 0.38666, 0.24866, 0.38728, 0.35732, 0.40151, 0.35789, 0.35526, 0.30048, 0.35133, 0.40726, 0.35842, 0.41048, 0.40721, 0.30259, 0.39577, 0.62794, 0.34761, 0.80219, 0.28043, 0.84824, 0.28526, 0.91683, 0.30741, 0.92218, 0.32503, 0.85129, 0.31622, 0.81731, 0.32054, 0.63859, 0.39305, 0.62336, 0.37734, 0.71163, 0.31402, 0.72957, 0.35832, 0.66902, 0.33081, 0.68864, 0.37565, 0.77714, 0.33765, 0.75576, 0.29723, 0.51102, 0.59815, 0.49469, 0.64709, 0.51812, 0.67677, 0.6251, 0.67192, 0.71383, 0.6458, 0.7155, 0.60934, 0.52399, 0.45524, 0.60875, 0.44999, 0.54723, 0.51061, 0.65347, 0.61685, 0.61742, 0.50754, 0.5529, 0.59856, 0.67433, 0.58667, 0.63925, 0.54853, 0.5559, 0.55507, 0.68188, 0.57614, 0.6209, 0.58717, 0.59639, 0.62288, 0.59953, 0.54753, 0.58662, 0.50346, 0.57235, 0.44765, 0.50093, 0.14318, 0.53762, 0.13407, 0.61731, 0.11038, 0.73495, 0.11038, 0.41263, 0.1343, 0.22465, 0.14623, 0.19828, 0.24923, 0.41017, 0.24668, 0.59928, 0.23154, 0.77227, 0.20486, 0.47396, 0.77961, 0.51258, 0.75781, 0.55955, 0.7251, 0.59503, 0.71044, 0.63939, 0.71908, 0.6707, 0.70179, 0.70515, 0.70104, 0.74377, 0.72096, 0.77873, 0.74051, 0.4801, 0.8038, 0.52716, 0.82843, 0.57323, 0.83806, 0.6297, 0.8427, 0.68766, 0.83557, 0.73472, 0.81844, 0.76642, 0.79024, 0.78574, 0.76241, 0.44257, 0.77181, 0.44161, 0.80556, 0.46121, 0.79075, 0.48654, 0.79213, 0.53434, 0.78214, 0.56636, 0.77043, 0.59934, 0.75873, 0.64618, 0.757, 0.68107, 0.74874, 0.70267, 0.74874, 0.71606, 0.75597, 0.74904, 0.75735, 0.77867, 0.75356, 0.79396, 0.74633, 0.80113, 0.7298, 0.80591, 0.75528, 0.68981, 0.7694, 0.65253, 0.7787, 0.6076, 0.78179, 0.57223, 0.78317, 0.59066, 0.80524, 0.65757, 0.80145, 0.7111, 0.78768, 0.62401, 0.85712, 0.69355, 0.85016, 0.52878, 0.87937, 0.76139, 0.84621, 0.60341, 0.93999, 0.70974, 0.92658, 0.92653, 0.54318, 0.82644, 0.56428, 0.1923, 0.64273, 0.35335, 0.63818, 0.25508, 0.76207, 0.34925, 0.83876, 0.48028, 0.91546, 0.38887, 0.72588, 0.83244, 0.64945, 0.91225, 0.67135, 0.88139, 0.78546, 0.7933, 0.87879], "triangles": [5, 98, 4, 99, 5, 6, 18, 19, 83, 80, 38, 18, 104, 139, 140, 86, 138, 139, 137, 20, 136, 137, 136, 138, 20, 21, 136, 136, 21, 22, 138, 136, 135, 136, 22, 135, 22, 23, 135, 132, 23, 0, 133, 0, 1, 12, 185, 11, 11, 186, 10, 9, 10, 198, 196, 9, 197, 9, 198, 197, 169, 148, 149, 170, 169, 149, 157, 156, 169, 157, 169, 170, 180, 169, 156, 184, 155, 156, 197, 184, 156, 186, 182, 184, 147, 167, 166, 174, 166, 167, 165, 166, 174, 179, 174, 180, 154, 179, 180, 182, 154, 155, 182, 153, 154, 181, 182, 186, 185, 181, 186, 153, 178, 179, 181, 152, 153, 182, 181, 153, 185, 183, 181, 152, 178, 153, 183, 152, 181, 193, 183, 185, 12, 193, 185, 13, 193, 12, 151, 161, 162, 160, 141, 161, 150, 160, 161, 159, 160, 150, 159, 151, 183, 151, 159, 150, 193, 159, 183, 159, 192, 158, 192, 159, 193, 13, 192, 193, 14, 15, 191, 14, 191, 192, 38, 80, 39, 68, 17, 38, 40, 39, 81, 38, 39, 25, 39, 40, 26, 40, 88, 41, 24, 38, 25, 68, 24, 32, 25, 39, 26, 32, 24, 25, 33, 32, 25, 68, 32, 69, 43, 42, 93, 69, 32, 33, 70, 189, 69, 141, 158, 142, 162, 142, 163, 161, 141, 142, 162, 161, 142, 158, 194, 142, 158, 192, 194, 192, 191, 194, 71, 70, 34, 70, 71, 190, 71, 34, 35, 44, 82, 116, 30, 29, 44, 31, 30, 44, 37, 36, 30, 45, 31, 44, 73, 37, 31, 45, 44, 116, 73, 31, 45, 142, 194, 112, 142, 112, 143, 117, 130, 102, 118, 116, 130, 111, 190, 110, 110, 190, 72, 129, 118, 130, 61, 117, 102, 129, 130, 117, 121, 110, 124, 124, 110, 73, 124, 73, 118, 127, 121, 126, 113, 127, 119, 127, 111, 121, 112, 127, 113, 127, 112, 111, 60, 120, 117, 128, 120, 123, 126, 128, 123, 126, 121, 124, 127, 126, 119, 61, 102, 62, 60, 117, 61, 62, 63, 49, 60, 61, 47, 74, 120, 60, 64, 108, 65, 63, 105, 64, 48, 62, 49, 47, 61, 48, 46, 60, 47, 48, 58, 47, 74, 60, 46, 75, 123, 74, 125, 123, 75, 122, 125, 115, 125, 188, 115, 114, 115, 195, 51, 50, 65, 75, 59, 58, 76, 125, 75, 51, 65, 66, 67, 66, 99, 52, 51, 66, 55, 56, 50, 49, 50, 56, 56, 57, 49, 77, 56, 55, 77, 55, 79, 76, 58, 57, 76, 57, 56, 76, 56, 77, 187, 79, 7, 77, 79, 187, 188, 76, 77, 188, 77, 187, 188, 125, 76, 195, 188, 187, 115, 188, 195, 196, 195, 187, 8, 187, 7, 196, 187, 8, 172, 195, 196, 9, 196, 8, 53, 52, 66, 67, 99, 6, 67, 53, 66, 78, 67, 6, 53, 67, 78, 54, 52, 53, 54, 53, 78, 54, 55, 51, 54, 51, 52, 55, 50, 51, 79, 54, 78, 55, 54, 79, 78, 6, 7, 79, 78, 7, 57, 58, 48, 75, 58, 76, 58, 59, 47, 75, 74, 59, 50, 64, 65, 66, 65, 100, 63, 64, 50, 49, 63, 50, 46, 47, 59, 57, 48, 49, 74, 46, 59, 122, 123, 125, 119, 122, 115, 62, 107, 63, 61, 62, 48, 114, 119, 115, 148, 114, 195, 123, 120, 74, 126, 123, 122, 119, 126, 122, 113, 119, 114, 128, 129, 120, 124, 118, 128, 124, 128, 126, 120, 129, 117, 118, 129, 128, 147, 146, 114, 147, 114, 148, 121, 111, 110, 114, 146, 113, 144, 112, 113, 146, 145, 113, 147, 166, 146, 172, 148, 195, 167, 147, 148, 145, 144, 113, 166, 145, 146, 143, 112, 144, 164, 144, 145, 143, 144, 164, 194, 190, 111, 194, 111, 112, 130, 116, 87, 45, 116, 118, 73, 45, 118, 110, 72, 73, 163, 142, 143, 164, 163, 143, 37, 30, 31, 72, 36, 37, 73, 72, 37, 71, 36, 72, 190, 71, 72, 30, 36, 29, 35, 28, 36, 71, 35, 36, 189, 70, 190, 191, 190, 194, 191, 189, 190, 35, 27, 28, 34, 27, 35, 29, 43, 44, 36, 28, 29, 28, 42, 43, 29, 28, 43, 44, 43, 82, 26, 27, 34, 70, 33, 34, 28, 41, 42, 27, 41, 28, 26, 34, 33, 69, 33, 70, 27, 40, 41, 26, 40, 27, 189, 68, 69, 16, 189, 191, 33, 25, 26, 42, 41, 89, 68, 38, 24, 189, 17, 68, 189, 16, 17, 15, 16, 191, 18, 38, 17, 14, 192, 13, 160, 158, 141, 159, 158, 160, 150, 161, 151, 162, 163, 177, 178, 151, 162, 177, 178, 162, 152, 151, 178, 183, 151, 152, 177, 163, 164, 178, 177, 176, 164, 145, 165, 176, 164, 165, 176, 177, 164, 179, 178, 176, 165, 145, 166, 175, 165, 174, 176, 165, 175, 179, 175, 174, 179, 176, 175, 153, 179, 154, 148, 169, 168, 168, 167, 148, 174, 167, 168, 180, 174, 168, 180, 168, 169, 155, 180, 156, 154, 180, 155, 182, 155, 184, 149, 148, 172, 171, 149, 172, 170, 149, 171, 172, 197, 173, 171, 172, 173, 157, 170, 171, 157, 171, 173, 196, 197, 172, 156, 157, 173, 173, 197, 156, 198, 184, 197, 186, 184, 198, 186, 198, 10, 185, 186, 11, 134, 2, 3, 140, 134, 3, 140, 3, 4, 134, 1, 2, 133, 1, 134, 134, 140, 133, 133, 140, 139, 96, 140, 4, 132, 0, 133, 139, 132, 133, 132, 131, 23, 131, 132, 139, 131, 135, 23, 138, 135, 131, 138, 131, 139, 19, 20, 137, 84, 19, 137, 138, 91, 137, 92, 90, 138, 92, 138, 86, 95, 87, 86, 89, 90, 92, 93, 92, 86, 93, 86, 87, 89, 92, 93, 82, 93, 87, 42, 89, 93, 116, 82, 87, 82, 43, 93, 86, 139, 95, 91, 85, 137, 90, 91, 138, 84, 137, 85, 88, 85, 91, 88, 81, 85, 94, 91, 90, 88, 91, 94, 89, 94, 90, 41, 88, 94, 41, 94, 89, 81, 84, 85, 83, 19, 84, 83, 84, 81, 80, 83, 81, 81, 88, 40, 39, 80, 81, 80, 18, 83, 130, 87, 103, 95, 139, 106, 107, 106, 105, 95, 106, 107, 103, 95, 107, 103, 87, 95, 102, 103, 107, 107, 105, 63, 102, 107, 62, 130, 103, 102, 109, 140, 96, 109, 104, 140, 109, 96, 101, 106, 139, 104, 108, 109, 101, 104, 109, 108, 105, 104, 108, 106, 104, 105, 108, 101, 65, 105, 108, 64, 97, 96, 4, 97, 4, 98, 100, 97, 98, 101, 96, 97, 101, 97, 100, 99, 98, 5, 100, 98, 99, 65, 101, 100, 99, 66, 100], "vertices": [2, 6, 133.02, -36.37, 0.9607, 85, -236.03, 18.65, 0.0393, 2, 6, 128.48, -51.09, 0.96909, 85, -240.56, 3.94, 0.03091, 2, 6, 119.17, -61.77, 0.98082, 85, -249.87, -6.74, 0.01918, 2, 6, 102.98, -67.03, 0.98794, 85, -266.06, -12, 0.01206, 2, 6, 86.61, -69.28, 0.98498, 85, -282.44, -14.26, 0.01502, 2, 6, 65.85, -71.39, 0.9832, 85, -303.2, -16.37, 0.0168, 2, 6, 47.35, -71.06, 0.98001, 85, -321.69, -16.04, 0.01999, 2, 6, 29.01, -69.46, 0.97979, 85, -340.03, -14.43, 0.02021, 2, 6, -3.76, -61.43, 0.98171, 85, -372.8, -6.41, 0.01829, 2, 6, -19.41, -52.95, 0.97687, 85, -388.46, 2.07, 0.02313, 2, 6, -41.33, -29.43, 0.96599, 85, -410.37, 25.6, 0.03401, 2, 6, -49.1, -15.61, 0.96122, 85, -418.14, 39.42, 0.03878, 2, 6, -48.1, 1.19, 0.9615, 85, -417.15, 56.21, 0.0385, 2, 6, -36.34, 19.49, 0.96521, 85, -405.39, 74.52, 0.03479, 2, 6, -16.27, 43.59, 0.97806, 85, -385.31, 98.61, 0.02194, 2, 6, 0.01, 52.63, 0.98233, 85, -369.03, 107.66, 0.01767, 2, 6, 21.84, 56.88, 0.98422, 85, -347.2, 111.91, 0.01578, 2, 6, 47.79, 58.38, 0.98678, 85, -321.25, 113.4, 0.01322, 2, 6, 68, 56.14, 0.98681, 85, -301.04, 111.16, 0.01319, 2, 6, 98.89, 49.58, 0.98625, 85, -270.15, 104.61, 0.01375, 2, 6, 115.11, 40.12, 0.97991, 85, -253.93, 95.15, 0.02009, 2, 6, 126.89, 25.62, 0.96854, 85, -242.16, 80.64, 0.03146, 2, 6, 135.26, 4.26, 0.96177, 85, -233.78, 59.29, 0.03823, 2, 6, 136.45, -13.18, 0.96, 85, -232.59, 41.84, 0.04, 2, 6, 51.68, 35.85, 0.96262, 85, -317.36, 90.88, 0.03738, 2, 6, 55.19, 31.21, 0.96037, 85, -313.85, 86.24, 0.03963, 2, 6, 57.85, 25.23, 0.96, 85, -311.19, 80.25, 0.04, 2, 6, 57.92, 19.24, 0.96, 85, -311.12, 74.26, 0.04, 2, 6, 56.49, 12.68, 0.96, 85, -312.55, 67.71, 0.04, 2, 6, 53.3, 7.64, 0.96, 85, -315.74, 62.67, 0.04, 2, 6, 49.38, 4.46, 0.96, 85, -319.67, 59.48, 0.04, 2, 6, 45.06, 2.41, 0.96, 85, -323.99, 57.43, 0.04, 2, 6, 47.26, 33.34, 0.96093, 85, -321.79, 88.37, 0.03907, 2, 6, 43.71, 28.54, 0.96025, 85, -325.33, 83.57, 0.03975, 2, 6, 42.35, 22.3, 0.96, 85, -326.69, 77.32, 0.04, 2, 6, 43.01, 16.42, 0.96, 85, -326.03, 71.44, 0.04, 2, 6, 44.7, 10.8, 0.96, 85, -324.34, 65.83, 0.04, 2, 6, 45.06, 6.12, 0.96, 85, -323.99, 61.15, 0.04, 2, 6, 54.63, 38.4, 0.96523, 85, -314.41, 93.42, 0.03477, 2, 6, 58.25, 32.78, 0.96112, 85, -310.79, 87.8, 0.03888, 2, 6, 60.72, 25.09, 0.96, 85, -308.32, 80.11, 0.04, 2, 6, 60.67, 18.15, 0.96, 85, -308.37, 73.18, 0.04, 2, 6, 58.78, 11.7, 0.96, 85, -310.26, 66.72, 0.04, 2, 6, 54.47, 6.74, 0.96, 85, -314.57, 61.77, 0.04, 2, 6, 50.1, 3.33, 0.96, 85, -318.94, 58.36, 0.04, 2, 6, 45.34, 0.84, 0.96, 85, -323.7, 55.86, 0.04, 2, 6, 44.66, -29.9, 0.96, 85, -324.38, 25.13, 0.04, 2, 6, 48.73, -32.31, 0.96, 85, -320.31, 22.71, 0.04, 2, 6, 52.33, -36.63, 0.96, 85, -316.71, 18.4, 0.04, 2, 6, 54.93, -42.02, 0.96, 85, -314.12, 13, 0.04, 2, 6, 55.36, -47.89, 0.96, 85, -313.68, 7.14, 0.04, 2, 6, 53.91, -53.76, 0.96062, 85, -315.14, 1.27, 0.03938, 2, 6, 51.48, -57.59, 0.96294, 85, -317.56, -2.56, 0.03706, 2, 6, 48.4, -60.65, 0.9645, 85, -320.64, -5.63, 0.0355, 2, 6, 44.39, -57.72, 0.963, 85, -324.65, -2.69, 0.037, 2, 6, 41.63, -53.35, 0.96059, 85, -327.42, 1.68, 0.03941, 2, 6, 40.33, -47.65, 0.96, 85, -328.71, 7.37, 0.04, 2, 6, 40.56, -41.99, 0.96, 85, -328.48, 13.04, 0.04, 2, 6, 41.94, -36.75, 0.96, 85, -327.11, 18.27, 0.04, 2, 6, 43.61, -33.11, 0.96, 85, -325.43, 21.92, 0.04, 2, 6, 45.31, -28.33, 0.96, 85, -323.74, 26.7, 0.04, 2, 6, 49.74, -31.38, 0.96, 85, -319.3, 23.65, 0.04, 2, 6, 53.83, -36.48, 0.96, 85, -315.21, 18.55, 0.04, 2, 6, 56.48, -41.99, 0.96, 85, -312.56, 13.03, 0.04, 2, 6, 57.39, -47.91, 0.96, 85, -311.65, 7.11, 0.04, 2, 6, 56.38, -54.35, 0.9606, 85, -312.66, 0.68, 0.0394, 2, 6, 53.8, -59.52, 0.96377, 85, -315.24, -4.49, 0.03623, 2, 6, 50.33, -63.47, 0.96827, 85, -318.71, -8.45, 0.03173, 2, 6, 46.18, 40.33, 0.96616, 85, -322.86, 95.35, 0.03384, 2, 6, 40.97, 33.58, 0.96, 85, -328.07, 88.6, 0.04, 2, 6, 38.36, 24.57, 0.96, 85, -330.68, 79.59, 0.04, 2, 6, 39.42, 15.83, 0.96, 85, -329.62, 70.86, 0.04, 2, 6, 41.98, 7.99, 0.96, 85, -327.06, 63.02, 0.04, 2, 6, 42.96, 1.43, 0.96, 85, -326.08, 56.45, 0.04, 2, 6, 42.97, -28.62, 0.96, 85, -326.07, 26.4, 0.04, 2, 6, 40.73, -34.68, 0.96, 85, -328.31, 20.35, 0.04, 2, 6, 37.48, -41.04, 0.96, 85, -331.56, 13.99, 0.04, 2, 6, 35.79, -49.6, 0.96, 85, -333.25, 5.43, 0.04, 2, 6, 43.23, -64.57, 0.96987, 85, -325.81, -9.55, 0.03013, 2, 6, 37.61, -59.05, 0.96331, 85, -331.43, -4.03, 0.03669, 4, 38, -2.47, -2.46, 0.97774, 39, -10.59, -11.27, 0.00853, 40, -30.2, -11.93, 0.00692, 86, -244.3, 94.59, 0.00681, 4, 38, 9.83, -2.35, 0.83119, 39, -0.91, -3.67, 0.16652, 40, -20.69, -4.12, 0.00179, 86, -240.02, 83.05, 0.00049, 3, 38, 36.31, -24, 0.01084, 39, 33.27, -4.65, 0.00103, 40, 13.51, -4.36, 0.98813, 3, 38, -2.56, -0.08, 0.99077, 40, -31.76, -10.13, 0.00169, 86, -242.09, 95.48, 0.00754, 3, 38, 10.44, 3.88, 0.86541, 39, -4.24, 1.63, 0.1338, 86, -233.96, 84.6, 0.00078, 2, 38, 16.86, 1.81, 0.08576, 39, 2.11, 3.91, 0.91424, 2, 39, 31.71, 3.86, 0.00744, 40, 11.76, 4.11, 0.99256, 2, 38, 40.07, -21.08, 0.00026, 40, 14.61, 0.28, 0.99974, 3, 38, 14.56, -5.21, 0.17298, 39, 4.58, -3.05, 0.82288, 40, -15.21, -3.38, 0.00414, 3, 38, 25.49, -15.19, 0.02463, 39, 19.33, -4.28, 0.52231, 40, -0.44, -4.29, 0.45306, 2, 39, 18.57, 4.29, 0.63495, 40, -1.38, 4.26, 0.36505, 2, 39, 10.85, 4.28, 0.98636, 40, -9.11, 4.08, 0.01364, 2, 39, 25.21, 4.34, 0.06905, 40, 5.26, 4.46, 0.93095, 3, 38, 30.96, -19.87, 0.01486, 39, 26.52, -4.65, 0.04593, 40, 6.76, -4.5, 0.93921, 3, 38, 19.84, -10.39, 0.05573, 39, 11.92, -3.92, 0.91015, 40, -7.85, -4.1, 0.03412, 2, 36, 26.87, -3.62, 0.04033, 37, 9.28, -4.51, 0.95967, 3, 35, 16.31, -2.81, 0.3773, 36, 0.39, -4.06, 0.62048, 86, -237.44, 0.93, 0.00222, 3, 35, 10.1, -3.41, 0.9654, 36, -4.69, -0.44, 0.02683, 86, -239.63, -4.91, 0.00777, 2, 35, 0.19, -1.6, 0.97585, 86, -245.6, -13.01, 0.02415, 4, 35, -1.28, 1.41, 0.96212, 36, -10.12, 10.67, 0.00597, 37, -26.2, 13.2, 0.00787, 86, -248.96, -13.02, 0.02403, 4, 35, 8.33, 2.09, 0.924, 36, -2.42, 4.87, 0.06348, 37, -19.08, 6.7, 0.00548, 86, -245.34, -4.08, 0.00703, 4, 35, 12.56, 3.95, 0.40134, 36, 2, 3.5, 0.58727, 37, -14.81, 4.92, 0.00839, 86, -245.16, 0.54, 0.003, 3, 35, 32.6, 22.77, 0.00752, 36, 29.46, 4.55, 0.00022, 37, 12.63, 3.37, 0.99226, 2, 36, 29.94, 1.02, 0.0002, 37, 12.77, -0.19, 0.9998, 2, 36, 14.04, -4.05, 0.85327, 37, -3.54, -3.73, 0.14673, 3, 35, 22.3, 13.59, 0.03674, 36, 15.67, 4.38, 0.59116, 37, -1.12, 4.51, 0.3721, 2, 36, 20.54, -3.88, 0.20115, 37, 2.96, -4.18, 0.79885, 3, 35, 26.86, 18.02, 0.01848, 36, 22.02, 4.74, 0.06996, 37, 5.24, 4.26, 0.91155, 3, 35, 17.03, 8.33, 0.10047, 36, 8.24, 3.87, 0.8677, 37, -8.56, 4.7, 0.03182, 3, 35, 21.61, 1.71, 0.00067, 36, 7.35, -4.12, 0.99632, 37, -10.2, -3.17, 0.00302, 2, 6, 26.65, -3.41, 0.96, 85, -342.39, 51.61, 0.04, 2, 6, 18.22, 0.66, 0.96, 85, -350.82, 55.68, 0.04, 2, 6, 12.16, -1.24, 0.959, 85, -356.88, 53.79, 0.041, 2, 6, 10, -15.44, 0.959, 85, -359.05, 39.59, 0.041, 2, 6, 12.22, -28.09, 0.959, 85, -356.82, 26.94, 0.041, 2, 6, 18.8, -29.75, 0.96, 85, -350.24, 25.28, 0.04, 2, 6, 52.26, -10.76, 0.958, 85, -316.78, 44.27, 0.042, 2, 6, 50.8, -22.06, 0.958, 85, -318.24, 32.96, 0.042, 2, 6, 41.53, -11.61, 0.957, 85, -327.51, 43.41, 0.043, 2, 6, 19.2, -21.33, 0.954, 85, -349.84, 33.7, 0.046, 2, 6, 40.09, -20.93, 0.957, 85, -328.95, 34.1, 0.043, 2, 6, 25.38, -8.88, 0.955, 85, -343.66, 46.14, 0.045, 2, 6, 24.09, -25.25, 0.955, 85, -344.95, 29.77, 0.045, 2, 6, 32.02, -22.17, 0.956, 85, -337.02, 32.86, 0.044, 2, 6, 33.2, -10.99, 0.956, 85, -335.84, 44.03, 0.044, 2, 6, 25.79, -26.66, 0.96, 85, -343.25, 28.37, 0.04, 2, 6, 25.52, -18.24, 0.952, 85, -343.52, 36.79, 0.048, 2, 6, 19.73, -13.62, 0.954, 85, -349.32, 41.41, 0.046, 2, 6, 33.33, -17, 0.954, 85, -335.71, 38.02, 0.046, 2, 6, 41.71, -17.05, 0.956, 85, -327.33, 37.97, 0.044, 2, 6, 52.26, -17.39, 0.958, 85, -316.78, 37.63, 0.042, 2, 6, 109.63, -20.07, 0.96, 85, -259.41, 34.96, 0.04, 2, 6, 110.24, -25.23, 0.96, 85, -258.8, 29.8, 0.04, 2, 6, 112.28, -36.6, 0.96, 85, -256.76, 18.42, 0.04, 2, 6, 108.93, -52, 0.96575, 85, -260.11, 3.02, 0.03425, 2, 6, 113.76, -8.85, 0.96, 85, -255.28, 46.17, 0.04, 2, 6, 116.94, 16.23, 0.96333, 85, -252.1, 71.26, 0.03667, 2, 6, 98.97, 23.75, 0.96331, 85, -270.07, 78.78, 0.03669, 2, 6, 93.4, -4.09, 0.96, 85, -275.64, 50.93, 0.04, 2, 6, 90.77, -29.45, 0.96, 85, -278.27, 25.57, 0.04, 2, 6, 90.7, -53.16, 0.96392, 85, -278.34, 1.87, 0.03608, 2, 6, -5.28, 8.61, 0.96, 85, -374.32, 63.63, 0.04, 2, 6, -2.41, 2.69, 0.95927, 85, -371.45, 57.71, 0.04073, 2, 6, 2.2, -4.75, 0.95842, 85, -366.85, 50.27, 0.04158, 2, 6, 3.85, -9.98, 0.95817, 85, -365.19, 45.05, 0.04183, 2, 6, 1.02, -15.45, 0.95736, 85, -368.02, 39.58, 0.04264, 2, 6, 3.27, -20.23, 0.95833, 85, -365.77, 34.79, 0.04167, 2, 6, 2.43, -24.77, 0.95889, 85, -366.62, 30.25, 0.04111, 2, 6, -2.29, -29.04, 0.95918, 85, -371.34, 25.99, 0.04082, 2, 6, -6.84, -32.85, 0.96, 85, -375.89, 22.18, 0.04, 2, 6, -9.85, 8.76, 0.96, 85, -378.89, 63.78, 0.04, 2, 6, -15.66, 3.57, 0.9595, 85, -384.71, 58.59, 0.0405, 2, 6, -18.73, -2.08, 0.9587, 85, -387.77, 52.94, 0.0413, 2, 6, -21.18, -9.29, 0.95822, 85, -390.22, 45.73, 0.04178, 2, 6, -21.53, -17.17, 0.95832, 85, -390.57, 37.86, 0.04168, 2, 6, -19.75, -24, 0.95882, 85, -388.8, 31.02, 0.04118, 2, 6, -15.53, -29.27, 0.95934, 85, -384.57, 25.76, 0.04066, 2, 6, -11.02, -32.9, 0.96, 85, -380.06, 22.13, 0.04, 2, 6, -2.97, 12.41, 0.96, 85, -372.01, 67.43, 0.04, 2, 6, -9.07, 13.87, 0.96, 85, -378.11, 68.89, 0.04, 2, 6, -6.94, 10.72, 0.96, 85, -375.98, 65.74, 0.04, 2, 6, -7.91, 7.45, 0.96, 85, -376.95, 62.48, 0.04, 2, 6, -7.45, 0.8, 0.96, 85, -376.5, 55.83, 0.04, 2, 6, -6.24, -3.85, 0.96, 85, -375.28, 51.17, 0.04, 2, 6, -5.05, -8.64, 0.96, 85, -374.09, 46.39, 0.04, 2, 6, -6.07, -14.84, 0.96, 85, -375.11, 40.19, 0.04, 2, 6, -5.56, -19.73, 0.96, 85, -374.6, 35.29, 0.04, 2, 6, -6.17, -22.56, 0.96, 85, -375.22, 32.46, 0.04, 2, 6, -7.87, -24.03, 0.96, 85, -376.91, 31, 0.04, 2, 6, -9.06, -28.29, 0.96, 85, -378.1, 26.73, 0.04, 2, 6, -9.21, -32.32, 0.96, 85, -378.25, 22.7, 0.04, 2, 6, -8.33, -34.61, 0.96, 85, -377.38, 20.41, 0.04, 2, 6, -5.53, -36.2, 0.96, 85, -374.58, 18.82, 0.04, 2, 6, -10.3, -35.82, 0.96, 85, -379.34, 19.2, 0.04, 2, 6, -9.56, -20.06, 0.96, 85, -378.6, 34.96, 0.04, 2, 6, -10.19, -14.81, 0.96, 85, -379.23, 40.21, 0.04, 2, 6, -9.48, -8.81, 0.96, 85, -378.52, 46.22, 0.04, 2, 6, -8.72, -4.12, 0.96, 85, -377.76, 50.9, 0.04, 2, 6, -13.26, -5.66, 0.95809, 85, -382.3, 49.36, 0.04191, 2, 6, -14.47, -14.57, 0.95743, 85, -383.51, 40.45, 0.04257, 2, 6, -13.49, -22.13, 0.95796, 85, -382.53, 32.9, 0.04204, 2, 6, -23.63, -7.98, 0.95956, 85, -392.68, 47.05, 0.04044, 2, 6, -24.35, -17.36, 0.95958, 85, -393.39, 37.66, 0.04042, 2, 6, -24.97, 5.37, 0.9604, 85, -394.01, 60.39, 0.0396, 2, 6, -25.56, -26.4, 0.96035, 85, -394.6, 28.63, 0.03965, 2, 6, -38.11, -2.01, 0.95832, 85, -407.15, 53.02, 0.04168, 2, 6, -38.7, -16.46, 0.95834, 85, -407.74, 38.56, 0.04166, 2, 6, 24.82, -59.99, 0.9648, 85, -344.22, -4.97, 0.0352, 2, 6, 23.83, -46.05, 0.96, 85, -345.21, 8.97, 0.04, 2, 6, 27.62, 40.08, 0.96377, 85, -341.42, 95.11, 0.03623, 2, 6, 23.86, 18.81, 0.96, 85, -345.18, 73.84, 0.04, 2, 6, 4.14, 36.57, 0.96383, 85, -364.9, 91.6, 0.03617, 2, 6, -12.48, 27.27, 0.96202, 85, -381.52, 82.3, 0.03798, 2, 6, -30.15, 13.15, 0.9607, 85, -399.19, 68.17, 0.0393, 2, 6, 6.91, 17.63, 0.96, 85, -362.13, 72.65, 0.04, 2, 6, 8.18, -43.48, 0.96, 85, -360.86, 11.55, 0.04, 2, 6, 1.93, -53.06, 0.9648, 85, -367.11, 1.96, 0.0352, 2, 6, -17.94, -44.51, 0.96406, 85, -386.98, 10.51, 0.03594, 2, 6, -32.39, -29.29, 0.96156, 85, -401.43, 25.73, 0.03844], "hull": 24, "edges": [2, 4, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 38, 40, 40, 42, 42, 44, 44, 46, 30, 32, 32, 34, 34, 36, 36, 38, 2, 0, 46, 0, 4, 6, 10, 12, 12, 14, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 48, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 62, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 92, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 152, 154, 154, 158, 158, 156, 160, 162, 166, 168, 168, 170, 172, 174, 174, 164, 162, 176, 170, 182, 182, 180, 172, 184, 184, 180, 164, 186, 186, 178, 176, 188, 188, 178, 160, 166, 192, 194, 194, 196, 198, 200, 200, 202, 190, 206, 206, 204, 190, 212, 212, 208, 204, 214, 214, 210, 202, 216, 216, 210, 192, 218, 218, 208, 198, 196, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 232, 236, 234, 240, 240, 246, 246, 244, 236, 248, 248, 242, 230, 250, 244, 238, 238, 254, 254, 242, 252, 256, 256, 258, 258, 260, 262, 264, 264, 266, 266, 268, 268, 6, 262, 270, 270, 272, 272, 40, 6, 8, 8, 10, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 298, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 344, 298, 346, 314, 282, 316, 300, 318, 336, 348, 348, 350, 350, 352, 352, 354, 354, 324, 356, 358, 358, 360, 362, 364, 362, 366, 364, 368, 370, 372, 382, 384, 384, 386, 386, 370, 388, 380, 378, 382, 390, 376, 374, 392, 392, 394, 394, 396, 396, 372], "width": 134, "height": 186}}, "head2": {"head": {"type": "mesh", "uvs": [0.34925, 0.83876, 0.38887, 0.72588, 0.51812, 0.67677, 0.6251, 0.67192, 0.71383, 0.6458, 0.83244, 0.64945, 0.91225, 0.67135, 0.94529, 0.78359, 0.80847, 0.92557, 0.72, 0.98219, 0.59594, 0.99615, 0.44382, 0.95526, 0.47396, 0.77961, 0.51258, 0.75781, 0.55955, 0.7251, 0.59503, 0.71044, 0.63939, 0.71908, 0.6707, 0.70179, 0.70515, 0.70104, 0.74377, 0.72096, 0.77873, 0.74051, 0.4801, 0.8038, 0.52716, 0.82843, 0.57323, 0.83806, 0.6297, 0.8427, 0.68766, 0.83557, 0.73472, 0.81844, 0.76642, 0.79024, 0.78574, 0.76241, 0.44257, 0.77181, 0.44161, 0.80556, 0.46121, 0.79075, 0.48654, 0.79213, 0.53434, 0.78214, 0.56636, 0.77043, 0.59934, 0.75873, 0.64618, 0.757, 0.68107, 0.74874, 0.70267, 0.74874, 0.71606, 0.75597, 0.74904, 0.75735, 0.77867, 0.75356, 0.79396, 0.74633, 0.80113, 0.7298, 0.80591, 0.75528, 0.68981, 0.7694, 0.65253, 0.7787, 0.6076, 0.78179, 0.57223, 0.78317, 0.59066, 0.80524, 0.65757, 0.80145, 0.7111, 0.78768, 0.62401, 0.85712, 0.69355, 0.85016, 0.52878, 0.87937, 0.76139, 0.84621, 0.60341, 0.93999, 0.70974, 0.92658, 0.48028, 0.91546, 0.88139, 0.78546, 0.7933, 0.87879], "triangles": [4, 17, 3, 18, 17, 4, 15, 2, 3, 16, 15, 3, 17, 16, 3, 19, 4, 5, 18, 4, 19, 14, 2, 15, 43, 19, 5, 43, 5, 6, 20, 19, 43, 42, 20, 43, 18, 37, 17, 38, 18, 19, 37, 16, 17, 18, 38, 37, 19, 40, 39, 41, 20, 42, 39, 38, 19, 43, 59, 44, 42, 43, 44, 40, 19, 20, 36, 16, 37, 41, 40, 20, 13, 1, 2, 13, 2, 14, 35, 15, 16, 35, 16, 36, 14, 15, 35, 34, 13, 14, 28, 41, 42, 28, 42, 44, 45, 37, 38, 45, 38, 39, 36, 37, 45, 35, 34, 14, 29, 1, 13, 46, 36, 45, 12, 29, 13, 47, 35, 36, 47, 36, 46, 48, 34, 35, 33, 13, 34, 32, 12, 13, 47, 48, 35, 33, 34, 48, 6, 59, 43, 6, 7, 59, 51, 45, 39, 51, 39, 40, 28, 27, 40, 28, 40, 41, 51, 40, 27, 31, 29, 12, 33, 32, 13, 31, 12, 32, 50, 46, 45, 50, 45, 51, 21, 31, 32, 49, 48, 47, 50, 49, 47, 50, 47, 46, 29, 0, 1, 30, 29, 31, 30, 31, 21, 26, 51, 27, 22, 32, 33, 21, 32, 22, 49, 22, 33, 25, 50, 51, 25, 51, 26, 48, 49, 33, 23, 22, 49, 30, 0, 29, 24, 49, 50, 24, 50, 25, 23, 49, 24, 55, 26, 27, 27, 28, 44, 53, 25, 26, 53, 26, 55, 52, 23, 24, 53, 52, 24, 53, 24, 25, 44, 59, 27, 59, 55, 27, 60, 55, 59, 54, 22, 23, 54, 23, 52, 30, 22, 54, 22, 30, 21, 58, 30, 54, 0, 30, 58, 7, 8, 60, 7, 60, 59, 57, 53, 55, 57, 55, 60, 57, 60, 8, 56, 54, 52, 58, 54, 56, 52, 53, 57, 56, 52, 57, 11, 0, 58, 9, 57, 8, 56, 57, 9, 10, 58, 56, 10, 56, 9, 11, 58, 10], "vertices": [2, 6, -12.48, 27.27, 0.96202, 85, -381.52, 82.3, 0.03798, 2, 6, 6.91, 17.63, 0.96, 85, -362.13, 72.65, 0.04, 2, 6, 12.16, -1.24, 0.959, 85, -356.88, 53.79, 0.041, 2, 6, 10, -15.44, 0.959, 85, -359.05, 39.59, 0.041, 2, 6, 12.22, -28.09, 0.959, 85, -356.82, 26.94, 0.041, 2, 6, 8.18, -43.48, 0.96, 85, -360.86, 11.55, 0.04, 2, 6, 1.93, -53.06, 0.9648, 85, -367.11, 1.96, 0.0352, 2, 6, -19.41, -52.95, 0.97687, 85, -388.46, 2.07, 0.02313, 2, 6, -41.33, -29.43, 0.96599, 85, -410.37, 25.6, 0.03401, 2, 6, -49.1, -15.61, 0.96122, 85, -418.14, 39.42, 0.03878, 2, 6, -48.1, 1.19, 0.9615, 85, -417.15, 56.21, 0.0385, 2, 6, -36.34, 19.49, 0.96521, 85, -405.39, 74.52, 0.03479, 2, 6, -5.28, 8.61, 0.96, 85, -374.32, 63.63, 0.04, 2, 6, -2.41, 2.69, 0.95927, 85, -371.45, 57.71, 0.04073, 2, 6, 2.2, -4.75, 0.95842, 85, -366.85, 50.27, 0.04158, 2, 6, 3.85, -9.98, 0.95817, 85, -365.19, 45.05, 0.04183, 2, 6, 1.02, -15.45, 0.95736, 85, -368.02, 39.58, 0.04264, 2, 6, 3.27, -20.23, 0.95833, 85, -365.77, 34.79, 0.04167, 2, 6, 2.43, -24.77, 0.95889, 85, -366.62, 30.25, 0.04111, 2, 6, -2.29, -29.04, 0.95918, 85, -371.34, 25.99, 0.04082, 2, 6, -6.84, -32.85, 0.96, 85, -375.89, 22.18, 0.04, 2, 6, -9.85, 8.76, 0.96, 85, -378.89, 63.78, 0.04, 2, 6, -15.66, 3.57, 0.9595, 85, -384.71, 58.59, 0.0405, 2, 6, -18.73, -2.08, 0.9587, 85, -387.77, 52.94, 0.0413, 2, 6, -21.18, -9.29, 0.95822, 85, -390.22, 45.73, 0.04178, 2, 6, -21.53, -17.17, 0.95832, 85, -390.57, 37.86, 0.04168, 2, 6, -19.75, -24, 0.95882, 85, -388.8, 31.02, 0.04118, 2, 6, -15.53, -29.27, 0.95934, 85, -384.57, 25.76, 0.04066, 2, 6, -11.02, -32.9, 0.96, 85, -380.06, 22.13, 0.04, 2, 6, -2.97, 12.41, 0.96, 85, -372.01, 67.43, 0.04, 2, 6, -9.07, 13.87, 0.96, 85, -378.11, 68.89, 0.04, 2, 6, -6.94, 10.72, 0.96, 85, -375.98, 65.74, 0.04, 2, 6, -7.91, 7.45, 0.96, 85, -376.95, 62.48, 0.04, 2, 6, -7.45, 0.8, 0.96, 85, -376.5, 55.83, 0.04, 2, 6, -6.24, -3.85, 0.96, 85, -375.28, 51.17, 0.04, 2, 6, -5.05, -8.64, 0.96, 85, -374.09, 46.39, 0.04, 2, 6, -6.07, -14.84, 0.96, 85, -375.11, 40.19, 0.04, 2, 6, -5.56, -19.73, 0.96, 85, -374.6, 35.29, 0.04, 2, 6, -6.17, -22.56, 0.96, 85, -375.22, 32.46, 0.04, 2, 6, -7.87, -24.03, 0.96, 85, -376.91, 31, 0.04, 2, 6, -9.06, -28.29, 0.96, 85, -378.1, 26.73, 0.04, 2, 6, -9.21, -32.32, 0.96, 85, -378.25, 22.7, 0.04, 2, 6, -8.33, -34.61, 0.96, 85, -377.38, 20.41, 0.04, 2, 6, -5.53, -36.2, 0.96, 85, -374.58, 18.82, 0.04, 2, 6, -10.3, -35.82, 0.96, 85, -379.34, 19.2, 0.04, 2, 6, -9.56, -20.06, 0.96, 85, -378.6, 34.96, 0.04, 2, 6, -10.19, -14.81, 0.96, 85, -379.23, 40.21, 0.04, 2, 6, -9.48, -8.81, 0.96, 85, -378.52, 46.22, 0.04, 2, 6, -8.72, -4.12, 0.96, 85, -377.76, 50.9, 0.04, 2, 6, -13.26, -5.66, 0.95809, 85, -382.3, 49.36, 0.04191, 2, 6, -14.47, -14.57, 0.95743, 85, -383.51, 40.45, 0.04257, 2, 6, -13.49, -22.13, 0.95796, 85, -382.53, 32.9, 0.04204, 2, 6, -23.63, -7.98, 0.95956, 85, -392.68, 47.05, 0.04044, 2, 6, -24.35, -17.36, 0.95958, 85, -393.39, 37.66, 0.04042, 2, 6, -24.97, 5.37, 0.9604, 85, -394.01, 60.39, 0.0396, 2, 6, -25.56, -26.4, 0.96035, 85, -394.6, 28.63, 0.03965, 2, 6, -38.11, -2.01, 0.95832, 85, -407.15, 53.02, 0.04168, 2, 6, -38.7, -16.46, 0.95834, 85, -407.74, 38.56, 0.04166, 2, 6, -30.15, 13.15, 0.9607, 85, -399.19, 68.17, 0.0393, 2, 6, -17.94, -44.51, 0.96406, 85, -386.98, 10.51, 0.03594, 2, 6, -32.39, -29.29, 0.96156, 85, -401.43, 25.73, 0.03844], "hull": 12, "edges": [14, 16, 16, 18, 18, 20, 20, 22, 4, 6, 6, 8, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 86, 40, 88, 56, 24, 58, 42, 60, 78, 90, 90, 92, 92, 94, 94, 96, 96, 66, 98, 100, 100, 102, 104, 106, 104, 108, 106, 110, 112, 114, 0, 116, 116, 112, 12, 118, 118, 120, 120, 114, 0, 22, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14], "width": 134, "height": 186}}, "head_L": {"head_L": {"type": "path", "lengths": [792.54, 1617.19], "vertexCount": 6, "vertices": [1, 57, 203.01, -28.01, 1, 1, 57, -28.99, -1.13, 1, 2, 57, -321.27, 32.74, 0.7, 109, -19.27, 120.3, 0.3, 2, 64, -145.17, 3.55, 0.7, 109, -33.26, -277.97, 0.3, 1, 64, -0.98, -0.02, 1, 1, 64, 232.51, -5.81, 1]}}, "head_R": {"head_R": {"type": "path", "lengths": [691.62, 1386.59], "vertexCount": 6, "vertices": [1, 89, -875.29, -55.99, 1, 1, 50, -13.13, 11.27, 1, 2, 50, -313.54, 161.19, 0.7, 98, -1.91, -52.55, 0.3, 2, 89, -247.23, 128.12, 0.7, 98, 98.48, 64.13, 0.3, 1, 89, -0.16, 0.02, 1, 1, 89, 179.58, -93.17, 1]}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.80725, 0.01428, 0.97576, 0.00226, 0.98844, 0.05019, 0.99428, 0.09855, 0.98857, 0.15341, 0.97056, 0.19397, 0.91234, 0.25206, 0.80831, 0.32559, 0.60783, 0.42578, 0.56353, 0.4518, 0.53539, 0.47524, 0.52852, 0.50454, 0.54373, 0.52612, 0.60567, 0.55049, 0.66512, 0.57924, 0.69539, 0.61395, 0.7016, 0.65421, 0.65949, 0.73052, 0.63279, 0.78266, 0.53095, 1, 0.52298, 1, 0.19489, 0.96509, 0.19277, 0.88197, 0.17795, 0.78203, 0.04681, 0.66134, 0.07707, 0.57542, 0.07595, 0.55052, 0.03992, 0.52853, 0.01856, 0.50676, 0.01782, 0.47725, 0.01247, 0.44768, 0.0026, 0.4018, 0.01055, 0.34379, 0.0728, 0.22392, 0.08516, 0.16679, 0.08889, 0.1339, 0.14465, 0.10763, 0.27168, 0.07828, 0.40581, 0.0563, 0.57388, 0.03847, 0.2142, 0.5778, 0.1898, 0.54644, 0.16032, 0.51806, 0.1492, 0.49125, 0.1492, 0.46227, 0.14321, 0.40734, 0.27472, 0.21966, 0.29037, 0.15607, 0.28952, 0.11858, 0.82192, 0.05205, 0.81882, 0.10148, 0.80018, 0.14655, 0.7329, 0.23121, 0.43204, 0.41121, 0.38224, 0.46075, 0.35369, 0.5205, 0.37379, 0.54645, 0.43083, 0.57429, 0.36221, 0.48982, 0.62493, 0.31441, 0.19586, 0.31224, 0.57099, 0.06636, 0.55586, 0.10292, 0.54133, 0.14544, 0.50862, 0.21887, 0.41052, 0.3107, 0.29424, 0.40423, 0.23291, 0.6625, 0.37727, 0.9281, 0.30478, 0.77153, 0.49584, 0.7517, 0.52549, 0.63376], "triangles": [39, 0, 49, 0, 1, 2, 37, 38, 61, 61, 38, 39, 18, 19, 68, 21, 68, 20, 19, 20, 68, 21, 22, 68, 70, 18, 68, 68, 22, 69, 68, 69, 70, 22, 23, 69, 18, 70, 17, 23, 67, 69, 23, 24, 67, 69, 67, 70, 17, 70, 71, 70, 67, 71, 17, 71, 16, 24, 40, 67, 71, 40, 57, 71, 67, 40, 24, 25, 40, 71, 15, 16, 71, 14, 15, 71, 57, 14, 57, 13, 14, 25, 41, 40, 40, 56, 57, 40, 41, 56, 25, 26, 41, 13, 56, 12, 13, 57, 56, 41, 26, 42, 41, 55, 56, 56, 55, 12, 26, 27, 42, 41, 42, 55, 27, 28, 42, 55, 11, 12, 55, 42, 58, 49, 0, 2, 35, 36, 48, 62, 37, 61, 55, 58, 11, 28, 43, 42, 42, 43, 58, 28, 29, 43, 11, 58, 10, 29, 44, 43, 43, 44, 58, 58, 54, 10, 58, 44, 54, 29, 30, 44, 10, 54, 9, 30, 45, 44, 44, 66, 54, 44, 45, 66, 54, 53, 9, 54, 66, 53, 9, 53, 8, 30, 31, 45, 8, 59, 7, 8, 53, 59, 66, 65, 53, 53, 65, 59, 60, 66, 45, 45, 31, 32, 45, 32, 60, 66, 60, 65, 32, 33, 60, 7, 59, 6, 65, 64, 59, 59, 52, 6, 59, 64, 52, 60, 46, 65, 60, 33, 46, 65, 46, 64, 6, 52, 5, 64, 63, 52, 52, 51, 5, 52, 63, 51, 33, 34, 46, 46, 34, 47, 46, 47, 64, 47, 34, 35, 64, 47, 63, 5, 51, 4, 35, 48, 47, 47, 48, 63, 51, 50, 4, 4, 50, 3, 63, 62, 51, 51, 62, 50, 63, 48, 62, 62, 61, 50, 50, 49, 3, 50, 61, 49, 49, 2, 3, 61, 39, 49, 48, 37, 62, 36, 37, 48], "vertices": [4, 48, -33.41, 5.18, 0.15382, 43, 91.76, 114.28, 0.02778, 45, 43.56, 28.93, 0.79068, 87, -258.92, -70.45, 0.02772, 2, 48, -50.98, 34.5, 0.20572, 45, 25.25, 57.78, 0.79428, 2, 48, -13.02, 46.48, 0.65714, 45, 62.9, 70.73, 0.34286, 2, 48, 25.61, 57.27, 0.91429, 45, 101.25, 82.5, 0.08571, 1, 48, 70.01, 67.18, 1, 1, 48, 103.49, 71.91, 1, 1, 48, 152.95, 72.58, 1, 1, 48, 216.97, 67.72, 1, 1, 48, 306.96, 50.04, 1, 2, 48, 329.96, 46.91, 0.99024, 49, -47.01, 62.29, 0.00976, 2, 48, 350.15, 46.3, 0.91114, 49, -27.78, 56.14, 0.08886, 2, 48, 374.04, 50.88, 0.59949, 49, -3.55, 53.95, 0.40051, 2, 48, 390.68, 58.06, 0.27765, 49, 14.43, 56.27, 0.72235, 2, 48, 407.38, 74.6, 0.07, 49, 35.04, 67.56, 0.93, 2, 48, 427.72, 91.55, 0.0086, 49, 59.27, 78.24, 0.9914, 1, 49, 88.24, 83.09, 1, 1, 49, 121.64, 83.11, 1, 1, 49, 184.57, 72.71, 1, 1, 49, 227.59, 66, 1, 1, 49, 406.95, 39.87, 1, 1, 49, 406.9, 38.33, 1, 1, 49, 375.72, -24.26, 1, 1, 49, 306.84, -22.23, 1, 1, 49, 223.94, -22.17, 1, 1, 49, 123.05, -44.05, 1, 1, 49, 52.07, -35.66, 1, 2, 48, 432.23, -25.11, 0.03737, 49, 31.43, -35.14, 0.96263, 2, 48, 416.23, -36.3, 0.23062, 49, 12.96, -41.48, 0.76938, 2, 48, 399.72, -44.68, 0.52731, 49, -5.22, -44.98, 0.47269, 2, 48, 376.01, -50.73, 0.87449, 49, -29.68, -44.26, 0.12551, 2, 48, 352.47, -57.66, 0.99075, 49, -54.21, -44.43, 0.00925, 1, 48, 316.03, -68.7, 1, 1, 48, 268.99, -78.82, 1, 1, 48, 169.64, -91.1, 1, 2, 48, 123.11, -100.22, 0.94857, 43, 250.91, 12.9, 0.05143, 2, 48, 96.47, -106.1, 0.78857, 43, 224.44, 6.34, 0.21143, 3, 48, 72.73, -100.86, 0.63217, 43, 200.57, 10.97, 0.36414, 87, -387.46, -147.84, 0.0037, 3, 48, 43.17, -82.82, 0.39215, 43, 170.56, 28.25, 0.59528, 87, -362.82, -123.5, 0.01257, 3, 48, 19.2, -61.98, 0.19333, 43, 146.06, 48.48, 0.78732, 87, -336.8, -105.28, 0.01936, 3, 43, 123.14, 75.97, 0.82231, 45, 74.94, -9.38, 0.14674, 87, -304.19, -90.5, 0.03095, 2, 49, 54.98, -9.14, 0.97758, 87, -373.97, -537.6, 0.02242, 2, 49, 28.83, -12.95, 0.97615, 87, -378.7, -511.6, 0.02385, 3, 48, 402.17, -15.73, 0.24703, 49, 5.12, -17.83, 0.72887, 87, -384.42, -488.08, 0.0241, 3, 48, 381.12, -23.19, 0.82261, 49, -17.17, -19.2, 0.15435, 87, -386.58, -465.85, 0.02304, 3, 48, 357.81, -29, 0.96692, 49, -41.18, -18.35, 0.01079, 87, -386.58, -441.83, 0.02229, 2, 48, 313.9, -41.12, 0.97819, 87, -387.74, -396.29, 0.02181, 3, 48, 156.76, -53.94, 0.95337, 43, 283.37, 60.01, 0.01663, 87, -362.23, -240.71, 0.03, 3, 48, 104.87, -63.73, 0.78507, 43, 231.75, 48.91, 0.18587, 87, -359.19, -187.99, 0.02906, 3, 48, 74.75, -71.4, 0.57171, 43, 201.84, 40.47, 0.40438, 87, -359.36, -156.91, 0.02391, 3, 48, -3.72, 15.51, 0.64851, 45, 72.99, 40, 0.32149, 87, -256.07, -101.75, 0.03, 3, 48, 36.19, 24.82, 0.83697, 45, 112.64, 50.33, 0.13303, 87, -256.68, -142.73, 0.03, 2, 48, 73.32, 30.34, 0.97, 87, -260.29, -180.09, 0.03, 2, 48, 144.58, 34.62, 0.97, 87, -273.34, -250.28, 0.03, 2, 48, 303.48, 14.03, 0.97747, 87, -331.71, -399.5, 0.02253, 3, 48, 345.66, 14.57, 0.96796, 49, -40.84, 26.88, 0.00988, 87, -341.37, -440.57, 0.02216, 3, 48, 395.07, 21.16, 0.29574, 49, 8.46, 19.59, 0.6807, 87, -346.91, -490.1, 0.02356, 3, 48, 415, 30.14, 0.05832, 49, 30.1, 22.72, 0.91849, 87, -343.01, -511.61, 0.02319, 3, 48, 434.72, 46.45, 0.01202, 49, 53.56, 32.96, 0.96601, 87, -331.94, -534.69, 0.02197, 3, 48, 369.98, 16.62, 0.88375, 49, -16.9, 22.14, 0.09339, 87, -345.26, -464.66, 0.02286, 2, 48, 216.57, 30.96, 0.97264, 87, -294.29, -319.25, 0.02736, 2, 48, 234.92, -50.25, 0.97408, 87, -377.53, -317.45, 0.02592, 4, 48, 19.55, -28.86, 0.65933, 43, 145.58, 81.59, 0.17431, 45, 97.38, -3.76, 0.12772, 87, -304.75, -113.62, 0.03864, 4, 48, 49.67, -24.39, 0.85037, 43, 175.57, 86.83, 0.08033, 45, 127.38, 1.48, 0.03029, 87, -307.69, -143.93, 0.03901, 3, 48, 84.55, -18.62, 0.91974, 43, 210.29, 93.49, 0.04118, 87, -310.51, -179.17, 0.03908, 2, 48, 145.16, -10.07, 0.96043, 87, -316.85, -240.05, 0.03957, 2, 48, 223.63, -10.15, 0.97, 87, -335.89, -316.18, 0.03, 2, 48, 304.32, -13.31, 0.9724, 87, -358.44, -393.72, 0.0276, 2, 49, 125.29, -8, 0.98342, 87, -370.34, -607.82, 0.01658, 1, 49, 346.33, 12.19, 1, 2, 49, 216.11, 2.73, 0.98946, 87, -356.4, -698.21, 0.01054, 2, 49, 200.99, 40.36, 0.99147, 87, -319.33, -681.76, 0.00853, 2, 49, 103.49, 49.57, 0.98118, 87, -313.58, -583.99, 0.01882], "hull": 40, "edges": [2, 4, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 62, 64, 64, 66, 70, 72, 72, 74, 74, 76, 2, 0, 76, 78, 78, 0, 66, 68, 68, 70, 4, 6, 6, 8, 24, 26, 26, 28, 54, 56, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 14, 16, 16, 18, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 102, 104, 106, 108, 110, 112, 112, 114, 108, 116, 116, 110, 104, 118, 118, 106, 90, 120, 120, 92, 126, 128, 128, 130, 130, 132, 80, 134, 134, 138, 138, 136, 136, 140, 140, 142, 142, 114, 74, 122], "width": 194, "height": 829}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.36558, 0.02687, 0.57064, 0.07548, 0.69194, 0.1116, 0.76449, 0.13871, 0.82746, 0.17107, 0.89188, 0.22346, 0.92695, 0.26619, 0.94704, 0.311, 0.99273, 0.4129, 0.99795, 0.45489, 0.9928, 0.49206, 0.98697, 0.51751, 0.98666, 0.53941, 0.98141, 0.56007, 0.938, 0.59229, 0.92751, 0.61429, 0.93044, 0.63772, 0.93317, 0.6989, 0.92652, 0.7969, 0.95977, 0.9802, 0.96226, 0.99911, 0.64003, 0.99791, 0.61147, 0.96467, 0.39901, 0.74587, 0.37047, 0.69018, 0.46193, 0.62155, 0.50738, 0.59412, 0.53925, 0.57377, 0.55339, 0.55489, 0.54897, 0.52929, 0.53765, 0.51083, 0.5145, 0.49138, 0.44851, 0.4562, 0.23067, 0.35192, 0.13966, 0.30503, 0.04248, 0.24198, 0, 0.18699, 0.00827, 0.1321, 0.05818, 0.08307, 0.13689, 0.0374, 0.24526, 0.00141, 0.28625, 0.05187, 0.23255, 0.08314, 0.21918, 0.12806, 0.22948, 0.1755, 0.26967, 0.23934, 0.63946, 0.46149, 0.55878, 0.11976, 0.61124, 0.15832, 0.6898, 0.22689, 0.86525, 0.4554, 0.68367, 0.49076, 0.70569, 0.51914, 0.70326, 0.54901, 0.70132, 0.57955, 0.6653, 0.60662, 0.87447, 0.48793, 0.88398, 0.51943, 0.88589, 0.55187, 0.85164, 0.58098, 0.82691, 0.6139, 0.38746, 0.32478, 0.76971, 0.30524, 0.59892, 0.65231, 0.57047, 0.71143, 0.78925, 0.91693, 0.824, 0.71483, 0.81973, 0.65854], "triangles": [47, 1, 2, 48, 2, 3, 21, 19, 20, 21, 22, 19, 22, 65, 19, 65, 18, 19, 22, 23, 65, 66, 18, 65, 23, 64, 65, 66, 65, 64, 67, 64, 63, 64, 67, 66, 18, 66, 17, 23, 24, 64, 66, 67, 17, 64, 24, 63, 67, 16, 17, 24, 25, 63, 16, 60, 15, 16, 67, 60, 63, 55, 67, 67, 55, 60, 63, 25, 55, 55, 25, 26, 15, 60, 14, 55, 54, 60, 60, 59, 14, 60, 54, 59, 26, 27, 55, 55, 27, 54, 14, 59, 13, 54, 53, 59, 59, 53, 58, 59, 58, 13, 27, 28, 54, 54, 28, 53, 13, 58, 12, 48, 3, 4, 57, 53, 52, 53, 57, 58, 28, 29, 53, 58, 57, 12, 53, 29, 52, 12, 57, 11, 29, 30, 52, 52, 56, 57, 11, 57, 10, 30, 51, 52, 52, 51, 56, 10, 57, 56, 30, 31, 51, 10, 56, 9, 31, 46, 51, 31, 32, 46, 56, 51, 50, 51, 46, 50, 56, 50, 9, 50, 46, 62, 33, 61, 32, 61, 62, 46, 46, 32, 61, 50, 8, 9, 8, 62, 7, 8, 50, 62, 33, 34, 61, 34, 45, 61, 61, 49, 62, 61, 45, 49, 62, 6, 7, 6, 49, 5, 6, 62, 49, 34, 35, 45, 45, 35, 44, 35, 36, 44, 45, 48, 49, 45, 44, 48, 49, 4, 5, 49, 48, 4, 36, 37, 44, 37, 43, 44, 44, 47, 48, 44, 43, 47, 48, 47, 2, 37, 38, 43, 43, 42, 47, 43, 38, 42, 47, 42, 1, 1, 42, 41, 38, 39, 42, 42, 39, 41, 41, 0, 1, 0, 41, 40, 41, 39, 40], "vertices": [3, 46, -27.46, 0.22, 0.56598, 44, 47.8, -48.44, 0.40323, 87, -513.81, -9.14, 0.03079, 4, 46, 26.02, 36.49, 0.0576, 43, 130.79, -90.66, 0.54983, 44, 78.08, 8.64, 0.35257, 87, -466.85, -53.52, 0.04, 2, 43, 155.19, -55.1, 0.9665, 87, -439.07, -86.5, 0.0335, 3, 46, 91.96, 67.37, 0.13538, 43, 174.61, -32.5, 0.8407, 87, -422.46, -111.24, 0.02392, 3, 46, 123.93, 75.06, 0.48167, 43, 199.26, -10.74, 0.50833, 87, -408.04, -140.8, 0.01, 2, 46, 173.82, 79.14, 0.89174, 43, 241.44, 16.21, 0.10826, 1, 46, 213.64, 78.55, 1, 1, 46, 254.58, 74.21, 1, 1, 46, 347.68, 64.33, 1, 1, 46, 385.38, 57.22, 1, 2, 46, 418.26, 48.74, 0.99021, 47, -45.82, 40.29, 0.00979, 2, 46, 440.65, 42.42, 0.85472, 47, -22.61, 38.64, 0.14528, 2, 46, 460.16, 38.03, 0.49731, 47, -2.62, 38.29, 0.50269, 2, 46, 478.32, 32.78, 0.15935, 47, 16.23, 36.84, 0.84065, 1, 47, 45.51, 26.5, 1, 1, 47, 65.56, 23.82, 1, 1, 47, 86.96, 24.2, 1, 1, 47, 142.82, 24.07, 1, 1, 47, 232.27, 21.33, 1, 1, 47, 399.71, 26.68, 1, 1, 47, 416.98, 27.01, 1, 1, 47, 414.89, -46.76, 1, 1, 47, 384.45, -52.88, 1, 1, 47, 184.04, -98.82, 1, 1, 47, 133.11, -104.67, 1, 2, 46, 507.43, -95.5, 0.00353, 47, 70.74, -82.88, 0.99647, 2, 46, 485.23, -79.93, 0.04678, 47, 45.85, -72.13, 0.95322, 2, 46, 468.66, -68.79, 0.16148, 47, 27.36, -64.58, 0.83852, 2, 46, 452.53, -61.9, 0.37425, 47, 10.17, -61.11, 0.62575, 2, 46, 429.49, -57.84, 0.72351, 47, -13.21, -61.8, 0.27649, 2, 46, 412.47, -56.73, 0.89445, 47, -30.1, -64.17, 0.10555, 2, 46, 393.99, -58.07, 0.97537, 47, -47.93, -69.23, 0.02463, 2, 46, 359.37, -65.89, 0.99722, 110, 281.62, 0.82, 0.00278, 2, 46, 255.63, -94.04, 0.9717, 110, 177.88, -27.33, 0.0283, 2, 46, 209.33, -105.14, 0.96055, 110, 131.59, -38.43, 0.03945, 2, 46, 148.31, -114.44, 0.94087, 110, 70.57, -47.73, 0.05913, 2, 46, 97.19, -113.11, 0.93113, 110, 19.44, -46.4, 0.06887, 2, 46, 48.67, -100.42, 0.93979, 110, -29.08, -33.71, 0.06021, 3, 46, 7.44, -79.59, 0.92128, 44, 116, -102.63, 0.03839, 110, -70.31, -12.88, 0.04033, 3, 46, -29.38, -52.99, 0.75789, 44, 71.01, -96.36, 0.22093, 110, -107.13, 13.72, 0.02118, 3, 46, -56.11, -21.66, 0.49152, 44, 32.72, -81.19, 0.50475, 110, -133.86, 45.05, 0.00373, 2, 46, -9.1, -22.44, 0.97, 87, -531.97, -31.96, 0.03, 2, 46, 16.12, -40.62, 0.96961, 87, -544.27, -60.51, 0.03039, 2, 46, 55.51, -52.47, 0.96879, 87, -547.33, -101.53, 0.03121, 2, 46, 98.3, -59.52, 0.96833, 87, -544.98, -144.84, 0.03167, 2, 46, 157.2, -63.12, 0.96705, 87, -535.77, -203.13, 0.03295, 2, 46, 373.53, -24.24, 0.97247, 87, -451.09, -405.95, 0.02753, 2, 46, 64.9, 25.1, 0.96674, 87, -469.57, -93.95, 0.03326, 2, 46, 101.87, 29.23, 0.96661, 87, -457.55, -129.16, 0.03339, 2, 46, 166.88, 33.28, 0.9667, 87, -439.56, -191.76, 0.0333, 2, 46, 379.26, 27.45, 0.97218, 87, -399.38, -400.38, 0.02782, 3, 46, 401.8, -20.12, 0.96177, 47, -47.97, -30.48, 0.01038, 87, -440.96, -432.67, 0.02785, 3, 46, 428.2, -20.8, 0.88178, 47, -21.99, -25.79, 0.09028, 87, -435.92, -458.58, 0.02795, 3, 46, 454.7, -27.23, 0.40779, 47, 5.27, -26.72, 0.56451, 87, -436.48, -485.85, 0.0277, 3, 46, 481.83, -33.69, 0.06704, 47, 33.14, -27.54, 0.90506, 87, -436.92, -513.73, 0.0279, 3, 46, 504.18, -47.08, 0.01162, 47, 57.74, -36.12, 0.96057, 87, -445.17, -538.45, 0.02782, 2, 46, 408.72, 23.09, 0.97208, 87, -397.27, -430.09, 0.02792, 3, 46, 437.27, 19.01, 0.90911, 47, -21.18, 15.03, 0.06295, 87, -395.09, -458.84, 0.02794, 2, 46, 466.29, 13.04, 0.18119, 47, 8.45, 15.06, 0.81881, 2, 47, 34.92, 6.86, 0.97152, 87, -402.5, -515.04, 0.02848, 2, 47, 64.9, 0.79, 0.97165, 87, -408.16, -545.1, 0.02835, 2, 46, 239.19, -53.63, 0.96765, 87, -508.8, -281.13, 0.03235, 2, 46, 240.68, 35.69, 0.96726, 87, -421.26, -263.29, 0.03274, 2, 47, 99.25, -51.89, 0.97447, 87, -460.37, -580.17, 0.02553, 2, 47, 153.14, -59.14, 0.98089, 87, -466.89, -634.14, 0.01911, 1, 47, 341.42, -11.58, 1, 2, 47, 157.03, -1.13, 0.98165, 87, -408.83, -637.25, 0.01835, 2, 47, 105.63, -1.41, 0.97492, 87, -409.81, -585.86, 0.02508], "hull": 41, "edges": [10, 12, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 56, 58, 62, 64, 78, 80, 74, 76, 76, 78, 72, 74, 68, 70, 70, 72, 64, 66, 66, 68, 0, 80, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 58, 60, 60, 62, 48, 50, 50, 52, 52, 54, 54, 56, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 88, 90, 96, 98, 92, 102, 102, 104, 104, 106, 106, 108, 108, 110, 100, 112, 112, 114, 114, 116, 116, 118, 118, 120, 90, 122, 122, 92, 98, 124, 124, 100, 12, 14, 14, 16, 110, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 120], "width": 229, "height": 913}}, "line_L": {"line_L": {"type": "mesh", "uvs": [0.0012, 0.00113, 0.03754, 0.00037, 0.0801, 0.00092, 0.1419, 0.0081, 0.20602, 0.02573, 0.24202, 0.0455, 0.26436, 0.06539, 0.28309, 0.08804, 0.29967, 0.11185, 0.3106, 0.13904, 0.31863, 0.16994, 0.32566, 0.19984, 0.33643, 0.22969, 0.34698, 0.2667, 0.36119, 0.30361, 0.37951, 0.34099, 0.39747, 0.37609, 0.41977, 0.41107, 0.45066, 0.45226, 0.48553, 0.48645, 0.5287, 0.52532, 0.58058, 0.5662, 0.62532, 0.59923, 0.66746, 0.62345, 0.73691, 0.64755, 0.82531, 0.66804, 0.92216, 0.68942, 0.9639, 0.71279, 0.99801, 0.75068, 0.99904, 0.78078, 0.98165, 0.80285, 0.92623, 0.832, 0.84389, 0.85933, 0.80113, 0.87928, 0.78893, 0.894, 0.79038, 0.91045, 0.80642, 0.93458, 0.80634, 0.96146, 0.76693, 0.98405, 0.70462, 0.99981, 0.72795, 0.98091, 0.72508, 0.95901, 0.69813, 0.94124, 0.64595, 0.92633, 0.56829, 0.91119, 0.53621, 0.89741, 0.53806, 0.87925, 0.57428, 0.85848, 0.62509, 0.8323, 0.66119, 0.80782, 0.685, 0.7824, 0.69625, 0.75793, 0.69527, 0.73451, 0.66616, 0.72017, 0.59391, 0.7019, 0.53239, 0.68321, 0.4561, 0.65071, 0.3991, 0.61742, 0.35246, 0.58124, 0.31641, 0.54135, 0.28812, 0.50147, 0.26464, 0.46032, 0.25066, 0.41659, 0.24327, 0.37696, 0.2407, 0.34332, 0.24042, 0.30798, 0.23541, 0.26932, 0.2273, 0.23191, 0.22046, 0.19968, 0.20698, 0.1714, 0.19349, 0.14313, 0.18326, 0.12167, 0.16561, 0.10238, 0.13713, 0.08491, 0.1064, 0.06849, 0.06872, 0.05198, 0.02459, 0.03865], "triangles": [39, 40, 38, 37, 38, 41, 38, 40, 41, 37, 41, 36, 41, 42, 36, 36, 42, 35, 42, 43, 35, 35, 43, 34, 34, 43, 44, 34, 44, 46, 44, 45, 46, 46, 47, 34, 34, 47, 33, 47, 48, 33, 33, 48, 32, 48, 49, 32, 32, 49, 31, 30, 31, 50, 31, 49, 50, 30, 50, 29, 50, 51, 29, 51, 28, 29, 54, 24, 25, 51, 52, 28, 52, 27, 28, 52, 26, 27, 26, 52, 53, 53, 54, 25, 26, 53, 25, 55, 23, 24, 54, 55, 24, 55, 56, 23, 56, 22, 23, 56, 57, 22, 57, 21, 22, 57, 58, 21, 58, 20, 21, 58, 59, 20, 20, 60, 19, 20, 59, 60, 19, 61, 18, 19, 60, 61, 61, 17, 18, 61, 62, 17, 62, 16, 17, 62, 63, 16, 63, 15, 16, 63, 64, 15, 64, 65, 15, 65, 14, 15, 14, 65, 66, 13, 67, 12, 13, 66, 67, 67, 11, 12, 67, 68, 11, 14, 66, 13, 68, 10, 11, 68, 69, 10, 10, 69, 9, 69, 70, 9, 70, 71, 9, 71, 8, 9, 71, 72, 8, 72, 7, 8, 72, 73, 7, 73, 74, 6, 74, 75, 5, 3, 75, 76, 75, 4, 5, 75, 3, 4, 3, 76, 2, 2, 76, 1, 76, 0, 1, 74, 5, 6, 73, 6, 7], "vertices": [2, 57, 44.85, 52.69, 0.9874, 86, -150.77, 30.69, 0.0126, 2, 57, 43.37, 41.12, 0.98702, 86, -152.25, 19.12, 0.01298, 2, 57, 39.95, 27.92, 0.98739, 86, -155.67, 5.92, 0.01261, 2, 57, 27.56, 10.3, 0.98718, 86, -168.06, -11.69, 0.01282, 2, 57, 2.85, -5.51, 0.98732, 86, -192.77, -27.5, 0.01268, 3, 57, -22.51, -11.99, 0.92776, 100, -7.56, 10.01, 0.05987, 86, -218.13, -33.98, 0.01237, 3, 57, -47.12, -14.16, 0.66022, 100, 16.11, 15.09, 0.32846, 86, -242.75, -36.16, 0.01132, 3, 57, -74.71, -14.53, 0.48933, 100, 42.87, 18.72, 0.50094, 86, -270.33, -36.53, 0.00973, 4, 57, -103.51, -13.95, 0.30595, 100, 70.92, 21.56, 0.62331, 101, -15.61, 21.55, 0.06235, 86, -299.13, -35.95, 0.00838, 4, 57, -135.87, -10.78, 0.09846, 100, 102.73, 22.24, 0.07619, 101, 16.18, 22.25, 0.81912, 86, -331.48, -32.78, 0.00623, 2, 101, 52.21, 21.64, 0.99523, 86, -367.98, -27.8, 0.00477, 3, 101, 87.04, 20.81, 0.48537, 102, 0.67, 20.81, 0.51126, 86, -403.24, -22.75, 0.00338, 3, 102, 35.5, 20.9, 0.99204, 109, 0.84, 201.47, 0.00705, 86, -438.69, -18.89, 0.00091, 3, 102, 78.59, 20.12, 0.745, 103, -7.61, 20.25, 0.22866, 109, 0.41, 157.33, 0.02634, 2, 103, 35.43, 19.93, 0.95901, 109, 1.15, 113.21, 0.04099, 3, 103, 79.15, 20.86, 0.69418, 104, -6.83, 21.06, 0.25519, 109, 3.15, 68.43, 0.05063, 2, 104, 34.24, 21.05, 0.94496, 109, 5.27, 26.36, 0.05504, 3, 104, 75.37, 22.43, 0.76793, 105, -10.23, 22.89, 0.17678, 109, 8.79, -15.7, 0.05529, 2, 105, 38.52, 23.88, 0.94886, 109, 14.41, -65.34, 0.05114, 3, 105, 79.5, 27.6, 0.62547, 106, -5.13, 28.04, 0.33139, 109, 22.02, -106.8, 0.04314, 2, 106, 42.13, 29.98, 0.97028, 109, 31.79, -154.04, 0.02972, 3, 106, 92.35, 34.02, 0.29572, 107, 10.25, 32.88, 0.68699, 109, 44.13, -203.9, 0.01729, 3, 107, 50.86, 31.28, 0.97752, 108, -23.5, 42.09, 0.01439, 109, 55.01, -244.26, 0.00809, 3, 107, 81.73, 32.87, 0.45762, 108, 6.81, 32.53, 0.47696, 64, -62.62, 58.72, 0.06543, 3, 107, 115.69, 42.6, 0.00395, 108, 42.91, 29.51, 0.58037, 64, -29.93, 43.05, 0.41568, 3, 108, 79.97, 33.85, 0.26073, 64, 6.27, 33.92, 0.71671, 65, -59.55, 1.3, 0.02256, 3, 108, 119.62, 39.47, 0.00182, 64, 45.36, 25.05, 0.99421, 65, -32.43, 30.82, 0.00397, 1, 64, 69.74, 6.18, 1, 3, 64, 99.66, -29.22, 0.46981, 65, 41.64, 50.99, 0.50276, 66, -49.7, 32.3, 0.02743, 3, 64, 115.98, -61.09, 0.23502, 65, 77.4, 49.31, 0.43531, 66, -16.91, 46.65, 0.32967, 3, 64, 122.74, -87.05, 0.0302, 65, 103.29, 42.29, 0.20138, 66, 9.42, 51.83, 0.76842, 4, 65, 136.91, 22.65, 0.00408, 66, 48.25, 49.12, 0.9045, 67, -26.61, 46.76, 0.09134, 68, -21.17, 96.47, 9e-05, 3, 66, 88.48, 37.64, 0.22953, 67, 14.54, 39.24, 0.70269, 68, -0.08, 60.34, 0.06778, 3, 66, 115.66, 34.36, 0.01184, 67, 41.92, 38.62, 0.56418, 68, 17.28, 39.17, 0.42398, 2, 67, 59.15, 43.6, 0.2087, 68, 32.29, 29.33, 0.7913, 2, 67, 76.09, 53.4, 0.02622, 68, 50.75, 22.86, 0.97378, 2, 68, 79.4, 17.54, 0.599, 69, -7, 16.27, 0.401, 1, 69, 24.4, 22.17, 1, 2, 68, 129.97, -15.05, 0.0001, 69, 53.14, 14.75, 0.9999, 1, 69, 75.25, -1.37, 1, 2, 68, 122.08, -25.41, 0.00012, 69, 51.79, 1.8, 0.99988, 1, 69, 26.37, -3.93, 1, 2, 68, 74.58, -17.68, 0.51473, 69, 7.2, -16.32, 0.48527, 2, 68, 52.09, -27.04, 0.97637, 69, -7.13, -36.01, 0.02363, 2, 67, 110.97, -8.53, 0.02081, 68, 26.47, -43.94, 0.97919, 2, 67, 101.53, -25.39, 0.1206, 68, 7.53, -47.76, 0.8794, 2, 67, 82.3, -35.24, 0.35871, 68, -12.47, -39.58, 0.64129, 3, 66, 121.4, -42.11, 0.00125, 67, 55.07, -36.93, 0.8349, 68, -31.49, -20.02, 0.16385, 2, 66, 86.39, -39.37, 0.17149, 67, 19.96, -37.61, 0.82851, 4, 64, 33.67, -138.21, 0.03924, 65, 103.45, -60.43, 0.02196, 66, 55.09, -40.17, 0.69961, 67, -11.12, -41.45, 0.23918, 5, 108, 149.66, -91.21, 0.00075, 64, 26.97, -107.78, 0.14612, 65, 73.71, -51.13, 0.23453, 66, 24.3, -45.02, 0.60926, 67, -41.28, -49.27, 0.00935, 4, 108, 130.68, -68.88, 0.02501, 64, 17.17, -80.15, 0.30871, 65, 44.86, -45.91, 0.50462, 66, -3.87, -53.13, 0.16166, 4, 108, 109.98, -50.27, 0.12111, 64, 4.42, -55.39, 0.54999, 65, 17.04, -44.67, 0.319, 66, -29.36, -64.35, 0.0099, 4, 108, 91.13, -45.58, 0.26037, 64, -11.54, -44.3, 0.58916, 65, -0.51, -53.01, 0.14994, 66, -41.39, -79.61, 0.00053, 4, 108, 59.52, -47.91, 0.55755, 64, -41.93, -35.22, 0.40262, 65, -23.49, -74.88, 0.02633, 109, 34.45, -365.02, 0.0135, 5, 107, 129.78, -34.02, 0.02282, 108, 29.85, -47.37, 0.77406, 64, -69.48, -24.15, 0.16749, 65, -46.78, -93.3, 0.00027, 109, 16.76, -341.17, 0.03536, 4, 107, 86.03, -42, 0.58782, 108, -15.08, -39.2, 0.31896, 64, -108.6, -0.52, 0.02939, 109, -4.23, -300.57, 0.06383, 3, 106, 136.1, -37.33, 0.00138, 107, 43.7, -43.91, 0.91655, 109, -18.98, -259.56, 0.08207, 3, 106, 91.6, -41.13, 0.38911, 107, -0.5, -41.45, 0.51941, 109, -30.14, -215.41, 0.09147, 4, 105, 132.76, -37.3, 0.00582, 106, 43.75, -40.56, 0.89097, 107, -47.42, -34.19, 0.00413, 109, -37.53, -167.17, 0.09908, 3, 105, 85.65, -37.75, 0.50662, 106, -3.5, -37.59, 0.38683, 109, -42.46, -119.14, 0.10655, 3, 104, 125.31, -34.75, 0.00339, 105, 37.35, -36.4, 0.88527, 109, -45.72, -69.74, 0.11134, 3, 104, 74.5, -32.08, 0.6762, 105, -13.36, -31.52, 0.21402, 109, -45.69, -17.56, 0.10978, 3, 103, 115.92, -27.18, 0.00677, 104, 28.66, -27.98, 0.88957, 109, -43.97, 29.59, 0.10366, 3, 103, 77.06, -23.59, 0.68654, 104, -10.07, -23.33, 0.21978, 109, -41.34, 69.5, 0.09368, 2, 103, 36.66, -21.19, 0.9193, 109, -39.94, 110.97, 0.0807, 3, 102, 78.88, -21.03, 0.71112, 103, -7.97, -20.9, 0.22477, 109, -40.74, 156.72, 0.06411, 3, 101, 123.1, -23.3, 0.00017, 102, 36.34, -23.59, 0.95227, 109, -43.64, 200.26, 0.04757, 3, 101, 85.54, -25.95, 0.53771, 102, -1.18, -25.94, 0.4309, 109, -46.28, 238.66, 0.03138, 2, 101, 53.3, -27.42, 0.98031, 109, -47.76, 271.62, 0.01969, 4, 57, -135.54, 38.93, 0.08332, 100, 108.17, -27.15, 0.03937, 101, 21.64, -27.15, 0.86874, 109, -47.48, 303.98, 0.00857, 3, 57, -109.38, 34.98, 0.25894, 100, 82.29, -26.33, 0.46933, 101, -4.22, -26.34, 0.27173, 3, 57, -84.64, 32.38, 0.42239, 100, 57.96, -26.68, 0.57232, 101, -28.55, -26.69, 0.00529, 2, 57, -61.61, 32.28, 0.60011, 100, 35.56, -29.32, 0.39989, 2, 57, -40.42, 36.07, 0.89759, 100, 15.42, -35.59, 0.10241, 1, 57, -18.74, 43.87, 1, 1, 57, -0.35, 54.47, 1], "hull": 77, "edges": [0, 152, 10, 12, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 52, 54, 54, 56, 56, 58, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 86, 88, 88, 90, 104, 106, 110, 112, 120, 122, 128, 130, 134, 136, 146, 148, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 148, 150, 150, 152, 142, 144, 144, 146, 12, 14, 14, 16, 16, 18, 140, 142, 136, 138, 138, 140, 18, 20, 20, 22, 22, 24, 24, 26, 130, 132, 132, 134, 126, 128, 26, 28, 28, 30, 122, 124, 124, 126, 32, 34, 34, 36, 116, 118, 118, 120, 112, 114, 114, 116, 44, 46, 50, 52, 48, 50, 106, 108, 108, 110, 102, 104, 100, 102, 98, 100, 58, 60, 60, 62, 96, 98, 94, 96, 90, 92, 92, 94, 82, 84, 84, 86, 78, 80, 80, 82], "width": 320, "height": 1189}}, "line_R": {"line_R": {"type": "mesh", "uvs": [0.90774, 0.00432, 0.93374, 0.01427, 0.94573, 0.02384, 0.97396, 0.03839, 0.99863, 0.06033, 0.99773, 0.07815, 0.96743, 0.05972, 0.9269, 0.04399, 0.894, 0.0391, 0.85798, 0.03982, 0.81462, 0.04396, 0.77029, 0.05664, 0.74399, 0.07082, 0.7144, 0.09759, 0.69989, 0.12358, 0.691, 0.15047, 0.66968, 0.16869, 0.64733, 0.20429, 0.63057, 0.24969, 0.61902, 0.2949, 0.60832, 0.34332, 0.61024, 0.39481, 0.62102, 0.45177, 0.62923, 0.50733, 0.63391, 0.56527, 0.63351, 0.62592, 0.62829, 0.67233, 0.60675, 0.71928, 0.57604, 0.74756, 0.50889, 0.79683, 0.4581, 0.83852, 0.40837, 0.89018, 0.3466, 0.9173, 0.24926, 0.9507, 0.16867, 0.97885, 0.09113, 0.99819, 0.04882, 0.9995, 0.00691, 0.96268, 0, 0.94716, 0.03622, 0.88969, 0.08016, 0.84814, 0.15413, 0.79609, 0.23258, 0.75378, 0.30798, 0.71226, 0.36796, 0.67398, 0.40067, 0.64192, 0.4298, 0.60831, 0.45552, 0.56183, 0.47166, 0.5042, 0.48757, 0.44452, 0.50334, 0.38923, 0.51718, 0.34073, 0.53873, 0.28965, 0.55653, 0.24105, 0.5757, 0.18997, 0.59028, 0.15144, 0.60871, 0.10065, 0.63748, 0.0595, 0.71443, 0.01678, 0.7593, 0.00512, 0.80742, 0.00341, 0.86559, 0, 0.6757, 0.14168, 0.66811, 0.1232, 0.67211, 0.08992, 0.69368, 0.05688, 0.73306, 0.03251, 0.7894, 0.01471, 0.84693, 0.01009, 0.88906, 0.01146, 0.92422, 0.0177], "triangles": [36, 37, 35, 35, 37, 34, 33, 34, 39, 38, 39, 34, 34, 37, 38, 39, 40, 33, 32, 33, 41, 33, 40, 41, 32, 41, 31, 41, 42, 31, 31, 42, 30, 42, 43, 30, 30, 43, 29, 43, 44, 29, 29, 44, 28, 28, 44, 27, 44, 45, 27, 27, 45, 26, 45, 46, 26, 26, 46, 25, 25, 46, 47, 25, 47, 24, 24, 47, 48, 23, 24, 48, 48, 22, 23, 48, 49, 22, 49, 21, 22, 49, 50, 21, 21, 51, 20, 21, 50, 51, 51, 52, 20, 53, 54, 17, 52, 53, 19, 19, 53, 18, 18, 53, 17, 20, 52, 19, 17, 54, 16, 16, 62, 15, 55, 63, 62, 55, 56, 63, 15, 62, 14, 62, 63, 14, 13, 14, 64, 56, 57, 64, 12, 13, 65, 13, 64, 65, 64, 57, 65, 65, 57, 58, 12, 65, 66, 14, 63, 64, 63, 56, 64, 16, 55, 62, 54, 55, 16, 69, 0, 70, 11, 12, 66, 65, 58, 66, 10, 11, 67, 11, 66, 67, 10, 68, 9, 10, 67, 68, 9, 69, 8, 9, 68, 69, 66, 59, 67, 66, 58, 59, 67, 60, 68, 67, 59, 60, 68, 61, 69, 69, 61, 0, 68, 60, 61, 5, 6, 4, 6, 3, 4, 6, 7, 3, 8, 70, 7, 7, 2, 3, 7, 70, 2, 8, 69, 70, 70, 1, 2, 70, 0, 1], "vertices": [3, 57, 39.75, 23, 0.47115, 6, 157.68, -54.02, 0.51082, 86, -155.87, 1.01, 0.01803, 3, 57, 29.36, 12.89, 0.62494, 6, 147.29, -64.13, 0.3584, 86, -166.26, -9.11, 0.01666, 3, 57, 20.66, 9.07, 0.75244, 6, 138.59, -67.95, 0.23669, 85, -230.45, -12.92, 0.01087, 3, 57, 6.44, -1.27, 0.97619, 6, 124.37, -78.29, 0.01536, 85, -244.67, -23.26, 0.00845, 3, 57, -13.23, -8.73, 0.98891, 6, 104.69, -85.75, 8e-05, 85, -264.35, -30.73, 0.01101, 3, 57, -27.14, -5.28, 0.98725, 6, 90.79, -82.3, 0.00016, 85, -278.26, -27.28, 0.01259, 3, 57, -9.68, 5.34, 0.97722, 6, 108.25, -71.68, 0.00015, 85, -260.79, -16.65, 0.02264, 3, 57, 6.68, 21.07, 0.7571, 6, 124.61, -55.95, 0.21416, 85, -244.43, -0.93, 0.02873, 3, 57, 13.78, 35.19, 0.47985, 6, 131.71, -41.83, 0.48752, 85, -237.33, 13.19, 0.03263, 3, 57, 16.77, 51.68, 0.22295, 6, 134.7, -25.34, 0.74124, 85, -234.35, 29.68, 0.03581, 3, 57, 17.8, 72.08, 0.0255, 6, 135.73, -4.94, 0.93852, 85, -233.32, 50.09, 0.03598, 3, 6, 130.15, 17.37, 0.6519, 50, 13.16, -55.48, 0.3136, 85, -238.9, 72.4, 0.0345, 3, 6, 121.6, 31.75, 0.35861, 50, 4.62, -41.11, 0.60861, 85, -247.44, 86.77, 0.03279, 3, 6, 103.49, 49.76, 0.07777, 50, -13.49, -23.09, 0.89479, 85, -265.55, 104.78, 0.02744, 3, 6, 84.51, 60.79, 0.00315, 50, -32.48, -12.06, 0.97491, 85, -284.54, 115.82, 0.02194, 3, 6, 64.26, 69.42, 1e-05, 50, -52.73, -3.43, 0.9859, 85, -304.79, 124.45, 0.01409, 4, 6, 52.04, 82.22, 4e-05, 50, -64.94, 9.37, 0.78089, 90, 44.88, 23.51, 0.20333, 85, -317, 137.25, 0.01573, 4, 6, 26.29, 98.45, 0, 90, 74.22, 19.62, 0.47821, 91, -9.37, 20.19, 0.50774, 85, -342.76, 153.47, 0.01405, 2, 91, 27.14, 18.45, 0.99151, 85, -376.77, 168.84, 0.00849, 2, 91, 63.11, 19.07, 0.99741, 85, -411.16, 181.81, 0.00259, 3, 6, -79.09, 139.92, 0, 91, 101.51, 20.51, 0.02693, 92, 17.69, 19.73, 0.97307, 3, 6, -119.73, 147.84, 0, 92, 57.65, 25.55, 0.97799, 93, -26.79, 25.73, 0.02201, 2, 92, 101.39, 36, 0.11254, 93, 17.01, 35.89, 0.88746, 2, 93, 59.84, 44.73, 0.87658, 94, -28.12, 42.76, 0.12342, 3, 93, 104.71, 52.15, 0.18734, 94, 16.49, 53.51, 0.80982, 95, -76.53, 40.27, 0.00284, 4, 6, -303.61, 176.73, 0, 93, 151.94, 57.47, 0.00032, 94, 63.63, 62.35, 0.7769, 95, -31.64, 57.54, 0.22279, 3, 6, -339.56, 187.03, 0, 94, 100.12, 66.87, 0.34722, 95, 3.52, 68.62, 0.65278, 4, 6, -374.31, 204.83, 0, 94, 138.4, 64.02, 0.0657, 95, 41.75, 72.77, 0.8995, 96, -57.55, 61.05, 0.0348, 3, 94, 162.97, 54.19, 0.00818, 95, 67.68, 67.57, 0.82413, 96, -31.3, 61.94, 0.16769, 2, 95, 115.1, 52.58, 0.22183, 96, 17.9, 58.21, 0.77817, 3, 95, 154.23, 42.51, 0.00493, 96, 57.99, 57.38, 0.86946, 97, -36.01, 51.53, 0.12561, 2, 96, 104.31, 61.45, 0.31199, 97, 8.48, 64.08, 0.68801, 2, 96, 137.7, 49.84, 0.04884, 97, 43.08, 58.81, 0.95116, 2, 97, 92.41, 45.26, 0.95042, 99, -69.14, 43.09, 0.04958, 2, 97, 133.53, 34.32, 0.91485, 99, -26.86, 54.44, 0.08515, 2, 97, 168.78, 19.44, 0.88487, 99, 12.09, 59.38, 0.11513, 2, 97, 182.86, 6.12, 0.86266, 99, 31.29, 54.94, 0.13734, 2, 97, 175.57, -28.52, 0.79798, 99, 41.8, 21.09, 0.20202, 2, 97, 169.1, -39.54, 0.77683, 99, 41.43, 8.21, 0.22317, 2, 97, 125.65, -59.84, 0.74774, 99, 12.43, -31.51, 0.25226, 3, 96, 160.39, -83.97, 0.00608, 97, 88.63, -68.62, 0.75458, 99, -16.46, -57.95, 0.23934, 3, 96, 107.65, -78.86, 0.15787, 97, 36.28, -73.32, 0.65901, 99, -61.11, -88.61, 0.18312, 4, 95, 128.52, -79.79, 0.01188, 96, 60.12, -67.65, 0.6062, 97, -12.05, -71.06, 0.2686, 99, -105.59, -111.17, 0.11331, 4, 95, 85.48, -58.99, 0.2778, 96, 13.88, -57.26, 0.6652, 97, -58.98, -69.36, 0.01806, 99, -148.54, -133.52, 0.03895, 3, 94, 123.42, -51.84, 0.00165, 95, 47.37, -43.95, 0.85094, 96, -26.33, -51.35, 0.14741, 3, 94, 95.75, -41.66, 0.13922, 95, 18.31, -38.97, 0.85269, 96, -55.55, -53.17, 0.00809, 2, 94, 67.18, -33.35, 0.70846, 95, -11.3, -35.98, 0.29154, 2, 93, 111.16, -30.59, 0.0178, 94, 28.9, -28.52, 0.9822, 2, 93, 65.47, -28.36, 0.92297, 94, -17.22, -29.72, 0.07703, 2, 92, 102.96, -26.31, 0.07376, 93, 18.19, -26.43, 0.92624, 2, 92, 59.09, -24.33, 0.98765, 93, -25.66, -24.16, 0.01235, 3, 91, 106.26, -21.65, 0.01103, 92, 20.61, -22.6, 0.98701, 86, -381.62, 235.91, 0.00196, 3, 91, 64.99, -18.45, 0.97415, 92, -20.29, -17.55, 0.01826, 86, -343.62, 217.4, 0.0076, 2, 91, 25.92, -16.65, 0.99058, 86, -307.19, 201.01, 0.00942, 4, 50, -72.38, 55.71, 0.10157, 90, 70.37, -15.4, 0.68241, 91, -15.18, -14.55, 0.20352, 86, -268.95, 183.58, 0.0125, 3, 50, -43.54, 42.5, 0.53172, 90, 39.56, -15.54, 0.45294, 86, -240.11, 170.38, 0.01534, 2, 50, -5.46, 25.46, 0.98114, 86, -202.03, 153.33, 0.01886, 3, 6, 141.02, 78.21, 0.08484, 50, 24.03, 5.36, 0.89396, 86, -172.54, 133.23, 0.0212, 3, 6, 166.98, 35.95, 0.61957, 50, 49.99, -36.9, 0.36138, 86, -146.57, 90.98, 0.01906, 3, 6, 171.7, 13.57, 0.87545, 50, 54.72, -59.29, 0.10786, 86, -141.85, 68.59, 0.01669, 3, 57, 50.36, 68.43, 0.04042, 6, 168.29, -8.59, 0.94337, 86, -145.26, 46.44, 0.0162, 3, 57, 47.3, 41.42, 0.26712, 6, 165.23, -35.6, 0.71682, 86, -148.32, 19.42, 0.01606, 2, 50, -44.32, 2.02, 0.98785, 85, -296.37, 129.9, 0.01215, 2, 50, -29.04, 2.31, 0.9933, 85, -281.1, 130.19, 0.0067, 3, 6, 113.69, 67.67, 0.01296, 50, -3.29, -5.18, 0.98093, 85, -255.35, 122.69, 0.00611, 3, 6, 137.52, 52.22, 0.25869, 50, 20.54, -20.63, 0.73101, 85, -231.52, 107.25, 0.0103, 3, 6, 152.78, 30.17, 0.62332, 50, 35.8, -42.69, 0.36554, 85, -216.26, 85.19, 0.01114, 4, 57, 43.27, 78.55, 2e-05, 6, 161.2, 1.53, 0.97871, 50, 44.21, -71.32, 0.00912, 85, -207.85, 56.56, 0.01215, 3, 57, 41.22, 51.62, 0.18069, 6, 159.15, -25.4, 0.80657, 85, -209.9, 29.63, 0.01274, 3, 57, 35.98, 32.71, 0.39912, 6, 153.91, -44.31, 0.58863, 85, -215.13, 10.72, 0.01225, 3, 57, 27.61, 17.8, 0.6082, 6, 145.54, -59.22, 0.38027, 85, -223.51, -4.19, 0.01153], "hull": 62, "edges": [8, 10, 18, 20, 38, 40, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 76, 78, 78, 80, 86, 88, 88, 90, 90, 92, 92, 94, 112, 114, 114, 116, 116, 118, 28, 30, 24, 26, 26, 28, 12, 14, 10, 12, 6, 8, 2, 4, 4, 6, 2, 0, 0, 122, 118, 120, 120, 122, 110, 112, 108, 110, 30, 32, 32, 34, 106, 108, 102, 104, 104, 106, 34, 36, 36, 38, 100, 102, 98, 100, 96, 98, 94, 96, 84, 86, 80, 82, 82, 84, 72, 74, 74, 76, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 48, 50, 44, 46, 46, 48, 40, 42, 42, 44, 30, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 4, 20, 22, 22, 24, 14, 16, 16, 18], "width": 465, "height": 804}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": -9.86, "curve": [0.444, -9.86, 0.889, 14.41, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": 14.41, "curve": [1.778, 14.42, 2.222, -9.85, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": -9.86, "curve": [3.111, -9.87, 3.556, 14.41, 3.111, 0, 3.556, 0]}, {"time": 4, "x": 14.41, "curve": [4.444, 14.42, 4.889, -9.85, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": -9.86, "curve": [5.667, -9.87, 6, 14.41, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": 14.41, "curve": [6.778, 14.41, 7.222, 2.28, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": 2.28, "curve": [8.111, 2.27, 8.556, 14.41, 8.111, 0, 8.556, 0]}, {"time": 9, "x": 14.41, "curve": [9.444, 14.42, 9.889, -9.85, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": -9.86, "curve": [10.778, -9.87, 11.222, 14.41, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": 14.41, "curve": [12.111, 14.42, 12.556, -9.86, 12.111, 0, 12.556, 0]}, {"time": 13, "x": -9.86}]}, "ALL2": {"translate": [{"y": -13.22, "curve": [0.444, 0, 0.889, 0, 0.444, -13.22, 0.889, 16.15]}, {"time": 1.3333, "y": 16.16, "curve": [1.778, 0, 2.222, 0, 1.778, 16.17, 2.222, -13.22]}, {"time": 2.6667, "y": -13.22, "curve": [3.111, 0, 3.556, 0, 3.111, -13.23, 3.556, 16.15]}, {"time": 4, "y": 16.16, "curve": [4.444, 0, 4.889, 0, 4.444, 16.17, 4.889, -13.21]}, {"time": 5.3333, "y": -13.22, "curve": [5.667, 0, 6, 0, 5.667, -13.23, 6, 16.16]}, {"time": 6.3333, "y": 16.16, "curve": [6.778, 0, 7.222, 0, 6.778, 16.17, 7.222, 1.47]}, {"time": 7.6667, "y": 1.47, "curve": [8.111, 0, 8.556, 0, 8.111, 1.47, 8.556, 16.15]}, {"time": 9, "y": 16.16, "curve": [9.444, 0, 9.889, 0, 9.444, 16.17, 9.889, -13.22]}, {"time": 10.3333, "y": -13.22, "curve": [10.778, 0, 11.222, 0, 10.778, -13.23, 11.222, 16.15]}, {"time": 11.6667, "y": 16.16, "curve": [12.111, 0, 12.556, 0, 12.111, 16.17, 12.556, -13.22]}, {"time": 13, "y": -13.22}]}, "body": {"rotate": [{"value": -2.32, "curve": [0.444, -2.32, 0.889, 1.11]}, {"time": 1.3333, "value": 1.11, "curve": [1.778, 1.11, 2.222, -2.32]}, {"time": 2.6667, "value": -2.32, "curve": [3.111, -2.32, 3.556, 1.11]}, {"time": 4, "value": 1.11, "curve": [4.444, 1.11, 4.889, -2.32]}, {"time": 5.3333, "value": -2.32, "curve": [5.667, -2.32, 6, 6.2]}, {"time": 6.3333, "value": 6.2, "curve": [6.778, 6.21, 7.222, 1.94]}, {"time": 7.6667, "value": 1.94, "curve": [8.111, 1.94, 8.556, 6.2]}, {"time": 9, "value": 6.2, "curve": [9.444, 6.21, 9.889, -2.32]}, {"time": 10.3333, "value": -2.32, "curve": [10.778, -2.32, 11.222, 1.11]}, {"time": 11.6667, "value": 1.11, "curve": [12.111, 1.11, 12.556, -2.32]}, {"time": 13, "value": -2.32}], "translate": [{"y": -10.97, "curve": [0.057, 0, 0.112, 0, 0.057, -11.59, 0.112, -12.07]}, {"time": 0.1667, "y": -12.07, "curve": [0.611, 0, 1.056, 0, 0.611, -12.07, 1.056, 11.43]}, {"time": 1.5, "y": 11.44, "curve": [1.944, 0, 2.389, 0, 1.944, 11.44, 2.389, -12.06]}, {"time": 2.8333, "y": -12.07, "curve": [3.278, 0, 3.722, 0, 3.278, -12.08, 3.722, 11.43]}, {"time": 4.1667, "y": 11.44, "curve": [4.611, 0, 5.056, 0, 4.611, 11.44, 5.056, -12.06]}, {"time": 5.5, "y": -12.07, "curve": [5.833, 0, 6.167, 0, 5.833, -12.08, 6.167, 11.43]}, {"time": 6.5, "y": 11.44, "curve": [6.944, 0, 7.389, 0, 6.944, 11.44, 7.389, -0.31]}, {"time": 7.8333, "y": -0.32, "curve": [8.278, 0, 8.722, 0, 8.278, -0.32, 8.722, 11.43]}, {"time": 9.1667, "y": 11.44, "curve": [9.611, 0, 10.056, 0, 9.611, 11.44, 10.056, -12.06]}, {"time": 10.5, "y": -12.07, "curve": [10.944, 0, 11.389, 0, 10.944, -12.08, 11.389, 11.43]}, {"time": 11.8333, "y": 11.44, "curve": [12.223, 0, 12.613, 0, 12.223, 11.44, 12.613, -6.51]}, {"time": 13, "y": -10.97}], "scale": [{"y": 1.041, "curve": [0.057, 1, 0.112, 1, 0.057, 1.043, 0.112, 1.045]}, {"time": 0.1667, "y": 1.045, "curve": [0.611, 1, 1.056, 1, 0.611, 1.045, 1.056, 0.975]}, {"time": 1.5, "y": 0.975, "curve": [1.944, 1, 2.389, 1, 1.944, 0.975, 2.389, 1.045]}, {"time": 2.8333, "y": 1.045, "curve": [3.278, 1, 3.722, 1, 3.278, 1.045, 3.722, 0.975]}, {"time": 4.1667, "y": 0.975, "curve": [4.611, 1, 5.056, 1, 4.611, 0.975, 5.056, 1.045]}, {"time": 5.5, "y": 1.045, "curve": [5.833, 1, 6.167, 1, 5.833, 1.045, 6.167, 0.975]}, {"time": 6.5, "y": 0.975, "curve": [6.944, 1, 7.389, 1, 6.944, 0.975, 7.389, 1.01]}, {"time": 7.8333, "y": 1.01, "curve": [8.278, 1, 8.722, 1, 8.278, 1.01, 8.722, 0.975]}, {"time": 9.1667, "y": 0.975, "curve": [9.611, 1, 10.056, 1, 9.611, 0.975, 10.056, 1.045]}, {"time": 10.5, "y": 1.045, "curve": [10.944, 1, 11.389, 1, 10.944, 1.045, 11.389, 0.975]}, {"time": 11.8333, "y": 0.975, "curve": [12.223, 1, 12.613, 1, 12.223, 0.975, 12.613, 1.028]}, {"time": 13, "y": 1.041}]}, "body2": {"rotate": [{"value": 1.33, "curve": [0.057, 1.41, 0.112, 1.48]}, {"time": 0.1667, "value": 1.48, "curve": [0.611, 1.48, 1.056, -1.67]}, {"time": 1.5, "value": -1.67, "curve": [1.944, -1.67, 2.389, 1.48]}, {"time": 2.8333, "value": 1.48, "curve": [3.278, 1.48, 3.722, -1.67]}, {"time": 4.1667, "value": -1.67, "curve": [4.611, -1.67, 5.056, 1.48]}, {"time": 5.5, "value": 1.48, "curve": [5.833, 1.48, 6.167, -3.56]}, {"time": 6.5, "value": -3.56, "curve": [6.944, -3.56, 7.389, -1.04]}, {"time": 7.8333, "value": -1.04, "curve": [8.278, -1.04, 8.722, -3.56]}, {"time": 9.1667, "value": -3.56, "curve": [9.611, -3.56, 10.056, 1.48]}, {"time": 10.5, "value": 1.48, "curve": [10.944, 1.48, 11.389, -1.67]}, {"time": 11.8333, "value": -1.67, "curve": [12.223, -1.67, 12.613, 0.73]}, {"time": 13, "value": 1.33}], "translate": [{"x": -3.13, "curve": [0.114, -3.99, 0.224, -4.63, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -4.63, "curve": [0.778, -4.63, 1.222, 4.73, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 4.73, "curve": [2.111, 4.73, 2.556, -4.63, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -4.63, "curve": [3.444, -4.63, 3.889, 4.73, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 4.73, "curve": [4.778, 4.73, 5.222, -4.62, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -4.63, "curve": [6, -4.63, 6.333, 4.73, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 4.73, "curve": [7.111, 4.73, 7.556, 0.05, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.05, "curve": [8.444, 0.05, 8.889, 4.73, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 4.73, "curve": [9.778, 4.73, 10.222, -4.63, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -4.63, "curve": [11.111, -4.63, 11.556, 4.73, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 4.73, "curve": [12.335, 4.73, 12.67, -0.51, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.13}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.114, 1.001, 0.224, 1, 0.114, 1.001, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.021, 0.778, 1, 1.222, 1.021]}, {"time": 1.6667, "x": 1.021, "y": 1.021, "curve": [2.111, 1.021, 2.556, 1, 2.111, 1.021, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.021, 3.444, 1, 3.889, 1.021]}, {"time": 4.3333, "x": 1.021, "y": 1.021, "curve": [4.778, 1.021, 5.222, 1, 4.778, 1.021, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.021, 6, 1, 6.333, 1.021]}, {"time": 6.6667, "x": 1.021, "y": 1.021, "curve": [7.111, 1.021, 7.556, 1.011, 7.111, 1.021, 7.556, 1.011]}, {"time": 8, "x": 1.011, "y": 1.011, "curve": [8.444, 1.011, 8.889, 1.021, 8.444, 1.011, 8.889, 1.021]}, {"time": 9.3333, "x": 1.021, "y": 1.021, "curve": [9.778, 1.021, 10.222, 1, 9.778, 1.021, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.021, 11.111, 1, 11.556, 1.021]}, {"time": 12, "x": 1.021, "y": 1.021, "curve": [12.335, 1.021, 12.67, 1.009, 12.335, 1.021, 12.67, 1.009]}, {"time": 13, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": -0.42, "curve": [0.168, -1, 0.334, -1.48]}, {"time": 0.5, "value": -1.48, "curve": [0.944, -1.48, 1.389, 1.84]}, {"time": 1.8333, "value": 1.84, "curve": [2.278, 1.84, 2.722, -1.48]}, {"time": 3.1667, "value": -1.48, "curve": [3.611, -1.48, 4.056, 1.84]}, {"time": 4.5, "value": 1.84, "curve": [4.944, 1.84, 5.389, -1.48]}, {"time": 5.8333, "value": -1.48, "curve": [6.167, -1.48, 6.5, -8.53]}, {"time": 6.8333, "value": -8.53, "curve": [7.278, -8.53, 7.722, -5]}, {"time": 8.1667, "value": -5, "curve": [8.611, -5, 9.056, -8.53]}, {"time": 9.5, "value": -8.53, "curve": [9.944, -8.53, 10.389, -1.48]}, {"time": 10.8333, "value": -1.48, "curve": [11.278, -1.48, 11.722, 1.84]}, {"time": 12.1667, "value": 1.84, "curve": [12.445, 1.84, 12.724, 0.55]}, {"time": 13, "value": -0.42}]}, "head": {"rotate": [{"value": 0.18, "curve": [0.225, -0.64, 0.446, -1.48]}, {"time": 0.6667, "value": -1.48, "curve": [1.111, -1.48, 1.556, 1.84]}, {"time": 2, "value": 1.84, "curve": [2.444, 1.84, 2.889, -1.48]}, {"time": 3.3333, "value": -1.48, "curve": [3.778, -1.48, 4.222, 1.84]}, {"time": 4.6667, "value": 1.84, "curve": [5.111, 1.84, 5.556, -1.48]}, {"time": 6, "value": -1.48, "curve": [6.333, -1.48, 6.667, -8.53]}, {"time": 7, "value": -8.53, "curve": [7.444, -8.53, 7.889, -5]}, {"time": 8.3333, "value": -5, "curve": [8.778, -5, 9.222, -8.53]}, {"time": 9.6667, "value": -8.53, "curve": [10.111, -8.53, 10.556, -1.48]}, {"time": 11, "value": -1.48, "curve": [11.444, -1.48, 11.889, 1.84]}, {"time": 12.3333, "value": 1.84, "curve": [12.557, 1.84, 12.781, 1.02]}, {"time": 13, "value": 0.18}]}, "sh_L": {"translate": [{"x": -5.23, "curve": [0.114, -6.32, 0.224, -7.14, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -7.14, "curve": [0.778, -7.14, 1.222, 4.77, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 4.77, "curve": [2.111, 4.77, 2.556, -7.14, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -7.14, "curve": [3.444, -7.14, 3.889, 4.77, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 4.77, "curve": [4.778, 4.77, 5.222, -7.13, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -7.14, "curve": [6, -7.14, 6.333, 4.77, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 4.77, "curve": [7.111, 4.77, 7.556, -1.18, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -1.18, "curve": [8.444, -1.18, 8.889, 4.77, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 4.77, "curve": [9.778, 4.77, 10.222, -7.14, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -7.14, "curve": [11.111, -7.14, 11.556, 4.77, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 4.77, "curve": [12.335, 4.77, 12.67, -1.9, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -5.23}]}, "sh_R": {"translate": [{"x": -1.31, "curve": [0.114, -1.68, 0.224, -1.95, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -1.95, "curve": [0.778, -1.95, 1.222, 2.04, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 2.04, "curve": [2.111, 2.04, 2.556, -1.95, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -1.95, "curve": [3.444, -1.95, 3.889, 2.04, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 2.04, "curve": [4.778, 2.04, 5.222, -1.95, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -1.95, "curve": [6, -1.95, 6.333, 2.04, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 2.04, "curve": [7.111, 2.04, 7.556, 0.05, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.04, "curve": [8.444, 0.04, 8.889, 2.04, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 2.04, "curve": [9.778, 2.04, 10.222, -1.95, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -1.95, "curve": [11.111, -1.95, 11.556, 2.04, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 2.04, "curve": [12.335, 2.04, 12.67, -0.19, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -1.31}]}, "arm_R": {"rotate": [{"value": 0.46, "curve": [0.168, 1.07, 0.334, 1.58]}, {"time": 0.5, "value": 1.58, "curve": [0.944, 1.58, 1.389, -1.93]}, {"time": 1.8333, "value": -1.93, "curve": [2.278, -1.93, 2.722, 1.58]}, {"time": 3.1667, "value": 1.58, "curve": [3.611, 1.58, 4.056, -1.93]}, {"time": 4.5, "value": -1.93, "curve": [4.944, -1.93, 5.389, 1.57]}, {"time": 5.8333, "value": 1.58, "curve": [6.167, 1.58, 6.5, -1.93]}, {"time": 6.8333, "value": -1.93, "curve": [7.278, -1.93, 7.722, -0.18]}, {"time": 8.1667, "value": -0.18, "curve": [8.611, -0.18, 9.056, -1.93]}, {"time": 9.5, "value": -1.93, "curve": [9.944, -1.93, 10.389, 1.58]}, {"time": 10.8333, "value": 1.58, "curve": [11.278, 1.58, 11.722, -1.93]}, {"time": 12.1667, "value": -1.93, "curve": [12.445, -1.93, 12.724, -0.57]}, {"time": 13, "value": 0.46}]}, "arm_R2": {"rotate": [{"value": -0.18, "curve": [0.225, 0.69, 0.446, 1.58]}, {"time": 0.6667, "value": 1.58, "curve": [1.111, 1.58, 1.556, -1.93]}, {"time": 2, "value": -1.93, "curve": [2.444, -1.93, 2.889, 1.58]}, {"time": 3.3333, "value": 1.58, "curve": [3.778, 1.58, 4.222, -1.93]}, {"time": 4.6667, "value": -1.93, "curve": [5.111, -1.93, 5.556, 1.57]}, {"time": 6, "value": 1.58, "curve": [6.333, 1.58, 6.667, -1.93]}, {"time": 7, "value": -1.93, "curve": [7.444, -1.93, 7.889, -0.18]}, {"time": 8.3333, "value": -0.18, "curve": [8.778, -0.18, 9.222, -1.93]}, {"time": 9.6667, "value": -1.93, "curve": [10.111, -1.93, 10.556, 1.58]}, {"time": 11, "value": 1.58, "curve": [11.444, 1.58, 11.889, -1.93]}, {"time": 12.3333, "value": -1.93, "curve": [12.557, -1.93, 12.781, -1.06]}, {"time": 13, "value": -0.18}]}, "arm_R3": {"rotate": [{"value": -3.92, "curve": [0.279, 0.36, 0.556, 6.04]}, {"time": 0.8333, "value": 6.04, "curve": [1.278, 6.04, 1.722, -8.55]}, {"time": 2.1667, "value": -8.55, "curve": [2.611, -8.56, 3.056, 6.03]}, {"time": 3.5, "value": 6.04, "curve": [3.944, 6.04, 4.389, -8.55]}, {"time": 4.8333, "value": -8.55, "curve": [5.278, -8.56, 5.722, 6.03]}, {"time": 6.1667, "value": 6.04, "curve": [6.5, 6.04, 6.833, -8.55]}, {"time": 7.1667, "value": -8.55, "curve": [7.611, -8.56, 8.056, 6.03]}, {"time": 8.5, "value": 6.04, "curve": [8.944, 6.04, 9.389, -8.55]}, {"time": 9.8333, "value": -8.55, "curve": [10.278, -8.56, 10.722, 6.04]}, {"time": 11.1667, "value": 6.04, "curve": [11.611, 6.04, 12.056, -8.55]}, {"time": 12.5, "value": -8.55, "curve": [12.667, -8.55, 12.835, -6.5]}, {"time": 13, "value": -3.92}]}, "line_R": {"rotate": [{"value": -2.8, "curve": [0.336, 0.32, 0.668, 6.48]}, {"time": 1, "value": 6.48, "curve": [1.444, 6.48, 1.889, -4.56]}, {"time": 2.3333, "value": -4.56, "curve": [2.778, -4.57, 3.222, 6.48]}, {"time": 3.6667, "value": 6.48, "curve": [4.111, 6.48, 4.556, -4.56]}, {"time": 5, "value": -4.56, "curve": [5.444, -4.57, 5.889, 6.47]}, {"time": 6.3333, "value": 6.48, "curve": [6.667, 6.48, 7, -4.56]}, {"time": 7.3333, "value": -4.56, "curve": [7.778, -4.57, 8.222, 6.48]}, {"time": 8.6667, "value": 6.48, "curve": [9.111, 6.48, 9.556, -4.56]}, {"time": 10, "value": -4.56, "curve": [10.444, -4.57, 10.889, 6.48]}, {"time": 11.3333, "value": 6.48, "curve": [11.778, 6.48, 12.222, -4.56]}, {"time": 12.6667, "value": -4.56, "curve": [12.779, -4.56, 12.892, -3.86]}, {"time": 13, "value": -2.8}]}, "arm_L": {"rotate": [{"value": -0.38, "curve": [0.168, -1, 0.334, -1.5]}, {"time": 0.5, "value": -1.5, "curve": [0.944, -1.5, 1.389, 2.02]}, {"time": 1.8333, "value": 2.02, "curve": [2.278, 2.02, 2.722, -1.5]}, {"time": 3.1667, "value": -1.5, "curve": [3.611, -1.51, 4.056, 2.02]}, {"time": 4.5, "value": 2.02, "curve": [4.944, 2.02, 5.389, -1.5]}, {"time": 5.8333, "value": -1.5, "curve": [6.167, -1.51, 6.5, 2.02]}, {"time": 6.8333, "value": 2.02, "curve": [7.278, 2.02, 7.722, 0.26]}, {"time": 8.1667, "value": 0.26, "curve": [8.611, 0.26, 9.056, 2.02]}, {"time": 9.5, "value": 2.02, "curve": [9.944, 2.02, 10.389, -1.5]}, {"time": 10.8333, "value": -1.5, "curve": [11.278, -1.51, 11.722, 2.02]}, {"time": 12.1667, "value": 2.02, "curve": [12.445, 2.02, 12.724, 0.65]}, {"time": 13, "value": -0.38}]}, "arm_L2": {"rotate": [{"value": 0.26, "curve": [0.225, -0.62, 0.446, -1.5]}, {"time": 0.6667, "value": -1.5, "curve": [1.111, -1.5, 1.556, 2.02]}, {"time": 2, "value": 2.02, "curve": [2.444, 2.02, 2.889, -1.5]}, {"time": 3.3333, "value": -1.5, "curve": [3.778, -1.51, 4.222, 2.02]}, {"time": 4.6667, "value": 2.02, "curve": [5.111, 2.02, 5.556, -1.5]}, {"time": 6, "value": -1.5, "curve": [6.333, -1.51, 6.667, 2.02]}, {"time": 7, "value": 2.02, "curve": [7.444, 2.02, 7.889, 0.26]}, {"time": 8.3333, "value": 0.26, "curve": [8.778, 0.26, 9.222, 2.02]}, {"time": 9.6667, "value": 2.02, "curve": [10.111, 2.02, 10.556, -1.5]}, {"time": 11, "value": -1.5, "curve": [11.444, -1.51, 11.889, 2.02]}, {"time": 12.3333, "value": 2.02, "curve": [12.557, 2.02, 12.781, 1.15]}, {"time": 13, "value": 0.26}]}, "arm_L3": {"rotate": [{"value": 1.79, "curve": [0.279, -2.57, 0.556, -8.36]}, {"time": 0.8333, "value": -8.36, "curve": [1.278, -8.36, 1.722, 6.51]}, {"time": 2.1667, "value": 6.52, "curve": [2.611, 6.52, 3.056, -8.36]}, {"time": 3.5, "value": -8.36, "curve": [3.944, -8.37, 4.389, 6.51]}, {"time": 4.8333, "value": 6.52, "curve": [5.278, 6.52, 5.722, -8.36]}, {"time": 6.1667, "value": -8.36, "curve": [6.5, -8.37, 6.833, 6.52]}, {"time": 7.1667, "value": 6.52, "curve": [7.611, 6.52, 8.056, -8.36]}, {"time": 8.5, "value": -8.36, "curve": [8.944, -8.37, 9.389, 6.51]}, {"time": 9.8333, "value": 6.52, "curve": [10.278, 6.52, 10.722, -8.36]}, {"time": 11.1667, "value": -8.36, "curve": [11.611, -8.37, 12.056, 6.51]}, {"time": 12.5, "value": 6.52, "curve": [12.667, 6.52, 12.835, 4.42]}, {"time": 13, "value": 1.79}]}, "hair_L": {"rotate": [{"value": 1.5, "curve": [0.279, -1.07, 0.556, -4.49]}, {"time": 0.8333, "value": -4.49, "curve": [1.278, -4.49, 1.722, 4.29]}, {"time": 2.1667, "value": 4.29, "curve": [2.611, 4.29, 3.056, -4.49]}, {"time": 3.5, "value": -4.49, "curve": [3.944, -4.49, 4.389, 4.29]}, {"time": 4.8333, "value": 4.29, "curve": [5.278, 4.29, 5.722, -4.49]}, {"time": 6.1667, "value": -4.49, "curve": [6.5, -4.49, 6.833, 4.29]}, {"time": 7.1667, "value": 4.29, "curve": [7.611, 4.29, 8.056, -0.1]}, {"time": 8.5, "value": -0.1, "curve": [8.944, -0.1, 9.389, 4.29]}, {"time": 9.8333, "value": 4.29, "curve": [10.278, 4.29, 10.722, -4.49]}, {"time": 11.1667, "value": -4.49, "curve": [11.611, -4.49, 12.056, 4.29]}, {"time": 12.5, "value": 4.29, "curve": [12.667, 4.29, 12.835, 3.05]}, {"time": 13, "value": 1.5}]}, "hair_R": {"rotate": [{"value": -1.7, "curve": [0.279, 0.87, 0.556, 4.29]}, {"time": 0.8333, "value": 4.29, "curve": [1.278, 4.29, 1.722, -4.49]}, {"time": 2.1667, "value": -4.49, "curve": [2.611, -4.49, 3.056, 4.29]}, {"time": 3.5, "value": 4.29, "curve": [3.944, 4.29, 4.389, -4.49]}, {"time": 4.8333, "value": -4.49, "curve": [5.278, -4.49, 5.722, 4.29]}, {"time": 6.1667, "value": 4.29, "curve": [6.5, 4.29, 6.833, -4.49]}, {"time": 7.1667, "value": -4.49, "curve": [7.611, -4.49, 8.056, -0.1]}, {"time": 8.5, "value": -0.1, "curve": [8.944, -0.1, 9.389, -4.49]}, {"time": 9.8333, "value": -4.49, "curve": [10.278, -4.49, 10.722, 4.29]}, {"time": 11.1667, "value": 4.29, "curve": [11.611, 4.29, 12.056, -4.49]}, {"time": 12.5, "value": -4.49, "curve": [12.667, -4.49, 12.835, -3.25]}, {"time": 13, "value": -1.7}]}, "eyebrow_L": {"translate": [{"curve": [2, 0, 4, 0.01, 2, 0, 4, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.76, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.76, "curve": "stepped"}, {"time": 9.5, "x": 1.76, "curve": [9.667, 1.76, 9.833, 0, 9.667, 0, 9.833, 0]}, {"time": 10}]}, "eyebrow_L2": {"rotate": [{"curve": [2, 0, 4, -0.02]}, {"time": 6, "curve": [6.111, 0, 6.222, -4.39]}, {"time": 6.3333, "value": -4.39, "curve": "stepped"}, {"time": 9.5, "value": -4.39, "curve": [9.667, -4.39, 9.833, 0]}, {"time": 10}], "scale": [{"curve": [2, 1, 4, 1, 2, 1, 4, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.989, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.989, "curve": "stepped"}, {"time": 9.5, "x": 0.989, "curve": [9.667, 0.989, 9.833, 1, 9.667, 1, 9.833, 1]}, {"time": 10}]}, "eyebrow_L3": {"rotate": [{"curve": [2, 0, 4, -0.03]}, {"time": 6, "curve": [6.111, 0, 6.222, -6.24]}, {"time": 6.3333, "value": -6.24, "curve": "stepped"}, {"time": 9.5, "value": -6.24, "curve": [9.667, -6.24, 9.833, 0]}, {"time": 10}]}, "eyebrow_R": {"translate": [{"curve": [2, 0, 4, 0.01, 2, 0, 4, 0]}, {"time": 6, "curve": [6.111, 0, 6.222, 1.76, 6.111, 0, 6.222, 0]}, {"time": 6.3333, "x": 1.76, "curve": "stepped"}, {"time": 9.5, "x": 1.76, "curve": [9.667, 1.76, 9.833, 0, 9.667, 0, 9.833, 0]}, {"time": 10}]}, "eyebrow_R2": {"rotate": [{"curve": [2, 0, 4, 0.01]}, {"time": 6, "curve": [6.111, 0, 6.222, 2.2]}, {"time": 6.3333, "value": 2.2, "curve": "stepped"}, {"time": 9.5, "value": 2.2, "curve": [9.667, 2.2, 9.833, 0]}, {"time": 10}], "scale": [{"curve": [2, 1, 4, 1, 2, 1, 4, 1]}, {"time": 6, "curve": [6.111, 1, 6.222, 0.989, 6.111, 1, 6.222, 1]}, {"time": 6.3333, "x": 0.989, "curve": "stepped"}, {"time": 9.5, "x": 0.989, "curve": [9.667, 0.989, 9.833, 1, 9.667, 1, 9.833, 1]}, {"time": 10}]}, "eyebrow_R3": {"rotate": [{"curve": [2, 0, 4, 0.02]}, {"time": 6, "curve": [6.111, 0, 6.222, 4.65]}, {"time": 6.3333, "value": 4.65, "curve": "stepped"}, {"time": 9.5, "value": 4.65, "curve": [9.667, 4.65, 9.833, 0]}, {"time": 10}]}, "tun": {"rotate": [{"value": -3.41, "curve": [0.444, -3.41, 0.889, 3.22]}, {"time": 1.3333, "value": 3.22, "curve": [1.778, 3.23, 2.222, -3.41]}, {"time": 2.6667, "value": -3.41, "curve": [3.111, -3.41, 3.556, 3.22]}, {"time": 4, "value": 3.22, "curve": [4.444, 3.23, 4.889, -3.4]}, {"time": 5.3333, "value": -3.41, "curve": [5.667, -3.41, 6, 8.32]}, {"time": 6.3333, "value": 8.32, "curve": [6.778, 8.32, 7.222, 2.46]}, {"time": 7.6667, "value": 2.46, "curve": [8.111, 2.45, 8.556, 8.32]}, {"time": 9, "value": 8.32, "curve": [9.444, 8.32, 9.889, -3.41]}, {"time": 10.3333, "value": -3.41, "curve": [10.778, -3.41, 11.222, 3.22]}, {"time": 11.6667, "value": 3.22, "curve": [12.111, 3.23, 12.556, -3.41]}, {"time": 13, "value": -3.41}]}, "leg_R2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -3.03]}, {"time": 7.6667, "value": -3.03, "curve": [8.111, -3.03, 8.556, 0]}, {"time": 9}]}, "leg_R3": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 5.3333, "value": -0.01, "curve": [5.667, -0.01, 6, -0.01]}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, 0.55]}, {"time": 7.6667, "value": 0.55, "curve": [8.111, 0.55, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "leg_L2": {"rotate": [{"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -1.96]}, {"time": 7.6667, "value": -1.96, "curve": [8.111, -1.96, 8.556, 0]}, {"time": 9}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 5.3333, "curve": [5.667, 0, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -1.94]}, {"time": 7.6667, "value": -1.94, "curve": [8.111, -1.94, 8.556, 0]}, {"time": 9}]}, "hair_RR": {"rotate": [{"value": -3.01, "curve": [0.336, -0.24, 0.668, 5.23]}, {"time": 1, "value": 5.24, "curve": [1.444, 5.24, 1.889, -4.57]}, {"time": 2.3333, "value": -4.58, "curve": [2.778, -4.58, 3.222, 5.23]}, {"time": 3.6667, "value": 5.24, "curve": [4.111, 5.24, 4.556, -4.57]}, {"time": 5, "value": -4.58, "curve": [5.444, -4.58, 5.889, 5.23]}, {"time": 6.3333, "value": 5.24, "curve": [6.667, 5.24, 7, -4.58]}, {"time": 7.3333, "value": -4.58, "curve": [7.778, -4.58, 8.222, 5.23]}, {"time": 8.6667, "value": 5.24, "curve": [9.111, 5.24, 9.556, -4.57]}, {"time": 10, "value": -4.58, "curve": [10.444, -4.58, 10.889, 5.23]}, {"time": 11.3333, "value": 5.24, "curve": [11.778, 5.24, 12.222, -4.58]}, {"time": 12.6667, "value": -4.58, "curve": [12.779, -4.58, 12.892, -3.95]}, {"time": 13, "value": -3.01}]}, "hair_LL": {"rotate": [{"value": 2.43, "curve": [0.336, 0.37, 0.668, -3.71]}, {"time": 1, "value": -3.71, "curve": [1.444, -3.71, 1.889, 3.6]}, {"time": 2.3333, "value": 3.6, "curve": [2.778, 3.6, 3.222, -3.71]}, {"time": 3.6667, "value": -3.71, "curve": [4.111, -3.71, 4.556, 3.6]}, {"time": 5, "value": 3.6, "curve": [5.444, 3.6, 5.889, -3.71]}, {"time": 6.3333, "value": -3.71, "curve": [6.667, -3.71, 7, 3.6]}, {"time": 7.3333, "value": 3.6, "curve": [7.778, 3.6, 8.222, -3.71]}, {"time": 8.6667, "value": -3.71, "curve": [9.111, -3.71, 9.556, 3.6]}, {"time": 10, "value": 3.6, "curve": [10.444, 3.6, 10.889, -3.71]}, {"time": 11.3333, "value": -3.71, "curve": [11.778, -3.71, 12.222, 3.6]}, {"time": 12.6667, "value": 3.6, "curve": [12.779, 3.6, 12.892, 3.13]}, {"time": 13, "value": 2.43}]}, "line_L": {"rotate": [{"value": 4.71, "curve": [0.336, 1.59, 0.668, -4.56]}, {"time": 1, "value": -4.56, "curve": [1.444, -4.56, 1.889, 6.48]}, {"time": 2.3333, "value": 6.48, "curve": [2.778, 6.48, 3.222, -4.56]}, {"time": 3.6667, "value": -4.56, "curve": [4.111, -4.57, 4.556, 6.48]}, {"time": 5, "value": 6.48, "curve": [5.444, 6.48, 5.889, -4.56]}, {"time": 6.3333, "value": -4.56, "curve": [6.667, -4.57, 7, 6.48]}, {"time": 7.3333, "value": 6.48, "curve": [7.778, 6.48, 8.222, -4.56]}, {"time": 8.6667, "value": -4.56, "curve": [9.111, -4.57, 9.556, 6.48]}, {"time": 10, "value": 6.48, "curve": [10.444, 6.48, 10.889, -4.56]}, {"time": 11.3333, "value": -4.56, "curve": [11.778, -4.57, 12.222, 6.48]}, {"time": 12.6667, "value": 6.48, "curve": [12.779, 6.48, 12.892, 5.77]}, {"time": 13, "value": 4.71}]}, "RU_R": {"translate": [{"x": -10.37, "y": 3.95, "curve": [0.168, -23.96, 0.334, -35.1, 0.168, 7.64, 0.334, 10.66]}, {"time": 0.5, "x": -35.1, "y": 10.66, "curve": [0.944, -35.1, 1.389, 42.61, 0.944, 10.66, 1.389, -10.43]}, {"time": 1.8333, "x": 42.63, "y": -10.43, "curve": [2.278, 42.65, 2.722, -35.08, 2.278, -10.44, 2.722, 10.66]}, {"time": 3.1667, "x": -35.1, "y": 10.66, "curve": [3.611, -35.12, 4.056, 42.61, 3.611, 10.67, 4.056, -10.43]}, {"time": 4.5, "x": 42.63, "y": -10.43, "curve": [4.944, 42.65, 5.389, -35.07, 4.944, -10.44, 5.389, 10.66]}, {"time": 5.8333, "x": -35.1, "y": 10.66, "curve": [6.167, -35.12, 6.5, 42.63, 6.167, 10.67, 6.5, -10.43]}, {"time": 6.8333, "x": 42.63, "y": -10.43, "curve": [7.278, 42.64, 7.722, 3.78, 7.278, -10.44, 7.722, 0.11]}, {"time": 8.1667, "x": 3.77, "y": 0.12, "curve": [8.611, 3.76, 9.056, 42.61, 8.611, 0.12, 9.056, -10.43]}, {"time": 9.5, "x": 42.63, "y": -10.43, "curve": [9.944, 42.65, 10.389, -35.08, 9.944, -10.44, 10.389, 10.66]}, {"time": 10.8333, "x": -35.1, "y": 10.66, "curve": [11.278, -35.12, 11.722, 42.61, 11.278, 10.67, 11.722, -10.43]}, {"time": 12.1667, "x": 42.63, "y": -10.43, "curve": [12.445, 42.65, 12.724, 12.42, 12.445, -10.44, 12.724, -2.23]}, {"time": 13, "x": -10.37, "y": 3.95}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.167, 1.007, 0.333, 0.921, 0.167, 0.995, 0.333, 1.053]}, {"time": 0.5, "x": 0.921, "y": 1.053, "curve": [0.722, 0.921, 0.944, 1.075, 0.722, 1.053, 0.944, 0.949]}, {"time": 1.1667, "x": 1.075, "y": 0.949, "curve": [1.389, 1.075, 1.611, 0.921, 1.389, 0.949, 1.611, 1.053]}, {"time": 1.8333, "x": 0.921, "y": 1.053, "curve": [2.056, 0.921, 2.278, 1.075, 2.056, 1.053, 2.278, 0.949]}, {"time": 2.5, "x": 1.075, "y": 0.949, "curve": [2.722, 1.075, 2.944, 0.921, 2.722, 0.949, 2.944, 1.053]}, {"time": 3.1667, "x": 0.921, "y": 1.053, "curve": [3.389, 0.921, 3.611, 1.075, 3.389, 1.053, 3.611, 0.949]}, {"time": 3.8333, "x": 1.075, "y": 0.949, "curve": [4.056, 1.075, 4.278, 0.921, 4.056, 0.949, 4.278, 1.053]}, {"time": 4.5, "x": 0.921, "y": 1.053, "curve": [4.722, 0.921, 4.944, 1.075, 4.722, 1.053, 4.944, 0.949]}, {"time": 5.1667, "x": 1.075, "y": 0.949, "curve": [5.389, 1.075, 5.611, 0.921, 5.389, 0.949, 5.611, 1.053]}, {"time": 5.8333, "x": 0.921, "y": 1.053, "curve": [6, 0.921, 6.167, 1.075, 6, 1.053, 6.167, 0.949]}, {"time": 6.3333, "x": 1.075, "y": 0.949, "curve": [6.5, 1.075, 6.667, 0.921, 6.5, 0.949, 6.667, 1.053]}, {"time": 6.8333, "x": 0.921, "y": 1.053, "curve": [7.056, 0.921, 7.278, 1.075, 7.056, 1.053, 7.278, 0.949]}, {"time": 7.5, "x": 1.075, "y": 0.949, "curve": [7.722, 1.075, 7.944, 0.921, 7.722, 0.949, 7.944, 1.053]}, {"time": 8.1667, "x": 0.921, "y": 1.053, "curve": [8.389, 0.921, 8.611, 1.075, 8.389, 1.053, 8.611, 0.949]}, {"time": 8.8333, "x": 1.075, "y": 0.949, "curve": [9.056, 1.075, 9.278, 0.921, 9.056, 0.949, 9.278, 1.053]}, {"time": 9.5, "x": 0.921, "y": 1.053, "curve": [9.722, 0.921, 9.944, 1.075, 9.722, 1.053, 9.944, 0.949]}, {"time": 10.1667, "x": 1.075, "y": 0.949, "curve": [10.389, 1.075, 10.611, 0.921, 10.389, 0.949, 10.611, 1.053]}, {"time": 10.8333, "x": 0.921, "y": 1.053, "curve": [11.056, 0.921, 11.278, 1.075, 11.056, 1.053, 11.278, 0.949]}, {"time": 11.5, "x": 1.075, "y": 0.949, "curve": [11.722, 1.075, 11.944, 0.921, 11.722, 0.949, 11.944, 1.053]}, {"time": 12.1667, "x": 0.921, "y": 1.053, "curve": [12.389, 0.921, 12.611, 1.075, 12.389, 1.053, 12.611, 0.949]}, {"time": 12.8333, "x": 1.075, "y": 0.949, "curve": [12.889, 1.075, 12.944, 1.065, 12.889, 0.949, 12.944, 0.956]}, {"time": 13, "x": 1.05, "y": 0.966}]}, "RU_R2": {"translate": [{"x": 4.51, "y": -0.07, "curve": [0.225, -15.16, 0.446, -35.1, 0.225, 5.26, 0.446, 10.66]}, {"time": 0.6667, "x": -35.1, "y": 10.66, "curve": [1.111, -35.1, 1.556, 44.08, 1.111, 10.66, 1.556, -10.79]}, {"time": 2, "x": 44.1, "y": -10.79, "curve": [2.444, 44.12, 2.889, -35.08, 2.444, -10.8, 2.889, 10.66]}, {"time": 3.3333, "x": -35.1, "y": 10.66, "curve": [3.778, -35.12, 4.222, 44.08, 3.778, 10.67, 4.222, -10.79]}, {"time": 4.6667, "x": 44.1, "y": -10.79, "curve": [5.111, 44.12, 5.556, -35.07, 5.111, -10.8, 5.556, 10.66]}, {"time": 6, "x": -35.1, "y": 10.66, "curve": [6.333, -35.12, 6.667, 44.09, 6.333, 10.67, 6.667, -10.79]}, {"time": 7, "x": 44.1, "y": -10.79, "curve": [7.444, 44.11, 7.889, 4.51, 7.444, -10.79, 7.889, -0.07]}, {"time": 8.3333, "x": 4.5, "y": -0.06, "curve": [8.778, 4.49, 9.222, 44.08, 8.778, -0.06, 9.222, -10.79]}, {"time": 9.6667, "x": 44.1, "y": -10.79, "curve": [10.111, 44.12, 10.556, -35.08, 10.111, -10.8, 10.556, 10.66]}, {"time": 11, "x": -35.1, "y": 10.66, "curve": [11.444, -35.12, 11.889, 44.08, 11.444, 10.67, 11.889, -10.79]}, {"time": 12.3333, "x": 44.1, "y": -10.79, "curve": [12.557, 44.11, 12.781, 24.44, 12.557, -10.79, 12.781, -5.47]}, {"time": 13, "x": 4.51, "y": -0.07}], "scale": [{"x": 1.075, "y": 0.949, "curve": [0.222, 1.075, 0.444, 0.921, 0.222, 0.949, 0.444, 1.053]}, {"time": 0.6667, "x": 0.921, "y": 1.053, "curve": [0.889, 0.921, 1.111, 1.075, 0.889, 1.053, 1.111, 0.949]}, {"time": 1.3333, "x": 1.075, "y": 0.949, "curve": [1.556, 1.075, 1.778, 0.921, 1.556, 0.949, 1.778, 1.053]}, {"time": 2, "x": 0.921, "y": 1.053, "curve": [2.222, 0.921, 2.444, 1.075, 2.222, 1.053, 2.444, 0.949]}, {"time": 2.6667, "x": 1.075, "y": 0.949, "curve": [2.889, 1.075, 3.111, 0.921, 2.889, 0.949, 3.111, 1.053]}, {"time": 3.3333, "x": 0.921, "y": 1.053, "curve": [3.556, 0.921, 3.778, 1.075, 3.556, 1.053, 3.778, 0.949]}, {"time": 4, "x": 1.075, "y": 0.949, "curve": [4.222, 1.075, 4.444, 0.921, 4.222, 0.949, 4.444, 1.053]}, {"time": 4.6667, "x": 0.921, "y": 1.053, "curve": [4.889, 0.921, 5.111, 1.075, 4.889, 1.053, 5.111, 0.949]}, {"time": 5.3333, "x": 1.075, "y": 0.949, "curve": [5.556, 1.075, 5.778, 0.921, 5.556, 0.949, 5.778, 1.053]}, {"time": 6, "x": 0.921, "y": 1.053, "curve": [6.167, 0.921, 6.333, 1.075, 6.167, 1.053, 6.333, 0.949]}, {"time": 6.5, "x": 1.075, "y": 0.949, "curve": [6.667, 1.075, 6.833, 0.921, 6.667, 0.949, 6.833, 1.053]}, {"time": 7, "x": 0.921, "y": 1.053, "curve": [7.222, 0.921, 7.444, 1.075, 7.222, 1.053, 7.444, 0.949]}, {"time": 7.6667, "x": 1.075, "y": 0.949, "curve": [7.889, 1.075, 8.111, 0.921, 7.889, 0.949, 8.111, 1.053]}, {"time": 8.3333, "x": 0.921, "y": 1.053, "curve": [8.556, 0.921, 8.778, 1.075, 8.556, 1.053, 8.778, 0.949]}, {"time": 9, "x": 1.075, "y": 0.949, "curve": [9.222, 1.075, 9.444, 0.921, 9.222, 0.949, 9.444, 1.053]}, {"time": 9.6667, "x": 0.921, "y": 1.053, "curve": [9.889, 0.921, 10.111, 1.075, 9.889, 1.053, 10.111, 0.949]}, {"time": 10.3333, "x": 1.075, "y": 0.949, "curve": [10.556, 1.075, 10.778, 0.921, 10.556, 0.949, 10.778, 1.053]}, {"time": 11, "x": 0.921, "y": 1.053, "curve": [11.222, 0.921, 11.444, 1.075, 11.222, 1.053, 11.444, 0.949]}, {"time": 11.6667, "x": 1.075, "y": 0.949, "curve": [11.889, 1.075, 12.111, 0.921, 11.889, 0.949, 12.111, 1.053]}, {"time": 12.3333, "x": 0.921, "y": 1.053, "curve": [12.556, 0.921, 12.778, 1.075, 12.556, 1.053, 12.778, 0.949]}, {"time": 13, "x": 1.075, "y": 0.949}]}, "RU_R3": {"translate": [{"x": 16.5, "y": -3.35, "curve": [0.279, -6.21, 0.556, -36.42, 0.279, 2.83, 0.556, 11.07]}, {"time": 0.8333, "x": -36.42, "y": 11.07, "curve": [1.278, -36.42, 1.722, 41.14, 1.278, 11.07, 1.722, -10.07]}, {"time": 2.1667, "x": 41.16, "y": -10.07, "curve": [2.611, 41.18, 3.056, -36.4, 2.611, -10.08, 3.056, 11.06]}, {"time": 3.5, "x": -36.42, "y": 11.07, "curve": [3.944, -36.44, 4.389, 41.14, 3.944, 11.07, 4.389, -10.07]}, {"time": 4.8333, "x": 41.16, "y": -10.07, "curve": [5.278, 41.18, 5.722, -36.4, 5.278, -10.08, 5.722, 11.06]}, {"time": 6.1667, "x": -36.42, "y": 11.07, "curve": [6.5, -36.44, 6.833, 41.16, 6.5, 11.07, 6.833, -10.07]}, {"time": 7.1667, "x": 41.16, "y": -10.07, "curve": [7.611, 41.17, 8.056, 2.38, 7.611, -10.08, 8.056, 0.49]}, {"time": 8.5, "x": 2.37, "y": 0.5, "curve": [8.944, 2.36, 9.389, 41.14, 8.944, 0.5, 9.389, -10.07]}, {"time": 9.8333, "x": 41.16, "y": -10.07, "curve": [10.278, 41.18, 10.722, -36.4, 10.278, -10.08, 10.722, 11.06]}, {"time": 11.1667, "x": -36.42, "y": 11.07, "curve": [11.611, -36.44, 12.056, 41.14, 11.611, 11.07, 12.056, -10.07]}, {"time": 12.5, "x": 41.16, "y": -10.07, "curve": [12.667, 41.17, 12.835, 30.22, 12.667, -10.08, 12.835, -7.09]}, {"time": 13, "x": 16.5, "y": -3.35}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.056, 1.065, 0.111, 1.075, 0.056, 0.956, 0.111, 0.949]}, {"time": 0.1667, "x": 1.075, "y": 0.949, "curve": [0.389, 1.075, 0.611, 0.921, 0.389, 0.949, 0.611, 1.053]}, {"time": 0.8333, "x": 0.921, "y": 1.053, "curve": [1.056, 0.921, 1.278, 1.075, 1.056, 1.053, 1.278, 0.949]}, {"time": 1.5, "x": 1.075, "y": 0.949, "curve": [1.722, 1.075, 1.944, 0.921, 1.722, 0.949, 1.944, 1.053]}, {"time": 2.1667, "x": 0.921, "y": 1.053, "curve": [2.389, 0.921, 2.611, 1.075, 2.389, 1.053, 2.611, 0.949]}, {"time": 2.8333, "x": 1.075, "y": 0.949, "curve": [3.056, 1.075, 3.278, 0.921, 3.056, 0.949, 3.278, 1.053]}, {"time": 3.5, "x": 0.921, "y": 1.053, "curve": [3.722, 0.921, 3.944, 1.075, 3.722, 1.053, 3.944, 0.949]}, {"time": 4.1667, "x": 1.075, "y": 0.949, "curve": [4.389, 1.075, 4.611, 0.921, 4.389, 0.949, 4.611, 1.053]}, {"time": 4.8333, "x": 0.921, "y": 1.053, "curve": [5.056, 0.921, 5.278, 1.075, 5.056, 1.053, 5.278, 0.949]}, {"time": 5.5, "x": 1.075, "y": 0.949, "curve": [5.722, 1.075, 5.944, 0.921, 5.722, 0.949, 5.944, 1.053]}, {"time": 6.1667, "x": 0.921, "y": 1.053, "curve": [6.333, 0.921, 6.5, 1.075, 6.333, 1.053, 6.5, 0.949]}, {"time": 6.6667, "x": 1.075, "y": 0.949, "curve": [6.833, 1.075, 7, 0.921, 6.833, 0.949, 7, 1.053]}, {"time": 7.1667, "x": 0.921, "y": 1.053, "curve": [7.389, 0.921, 7.611, 1.075, 7.389, 1.053, 7.611, 0.949]}, {"time": 7.8333, "x": 1.075, "y": 0.949, "curve": [8.056, 1.075, 8.278, 0.921, 8.056, 0.949, 8.278, 1.053]}, {"time": 8.5, "x": 0.921, "y": 1.053, "curve": [8.722, 0.921, 8.944, 1.075, 8.722, 1.053, 8.944, 0.949]}, {"time": 9.1667, "x": 1.075, "y": 0.949, "curve": [9.389, 1.075, 9.611, 0.921, 9.389, 0.949, 9.611, 1.053]}, {"time": 9.8333, "x": 0.921, "y": 1.053, "curve": [10.056, 0.921, 10.278, 1.075, 10.056, 1.053, 10.278, 0.949]}, {"time": 10.5, "x": 1.075, "y": 0.949, "curve": [10.722, 1.075, 10.944, 0.921, 10.722, 0.949, 10.944, 1.053]}, {"time": 11.1667, "x": 0.921, "y": 1.053, "curve": [11.389, 0.921, 11.611, 1.075, 11.389, 1.053, 11.611, 0.949]}, {"time": 11.8333, "x": 1.075, "y": 0.949, "curve": [12.056, 1.075, 12.278, 0.921, 12.056, 0.949, 12.278, 1.053]}, {"time": 12.5, "x": 0.921, "y": 1.053, "curve": [12.667, 0.921, 12.833, 1.007, 12.667, 1.053, 12.833, 0.995]}, {"time": 13, "x": 1.05, "y": 0.966}]}, "RU_L": {"translate": [{"x": -10.38, "y": 3.95, "curve": [0.168, -23.96, 0.334, -35.1, 0.168, 7.64, 0.334, 10.66]}, {"time": 0.5, "x": -35.1, "y": 10.66, "curve": [0.944, -35.1, 1.389, 42.61, 0.944, 10.66, 1.389, -10.43]}, {"time": 1.8333, "x": 42.63, "y": -10.43, "curve": [2.278, 42.65, 2.722, -35.08, 2.278, -10.44, 2.722, 10.66]}, {"time": 3.1667, "x": -35.1, "y": 10.66, "curve": [3.611, -35.12, 4.056, 42.61, 3.611, 10.67, 4.056, -10.43]}, {"time": 4.5, "x": 42.63, "y": -10.43, "curve": [4.944, 42.65, 5.389, -35.07, 4.944, -10.44, 5.389, 10.66]}, {"time": 5.8333, "x": -35.1, "y": 10.66, "curve": [6.167, -35.12, 6.5, 42.62, 6.167, 10.67, 6.5, -10.43]}, {"time": 6.8333, "x": 42.63, "y": -10.43, "curve": [7.278, 42.64, 7.722, 3.78, 7.278, -10.44, 7.722, 0.11]}, {"time": 8.1667, "x": 3.77, "y": 0.12, "curve": [8.611, 3.76, 9.056, 42.61, 8.611, 0.12, 9.056, -10.43]}, {"time": 9.5, "x": 42.63, "y": -10.43, "curve": [9.944, 42.65, 10.389, -35.08, 9.944, -10.44, 10.389, 10.66]}, {"time": 10.8333, "x": -35.1, "y": 10.66, "curve": [11.278, -35.12, 11.722, 42.61, 11.278, 10.67, 11.722, -10.43]}, {"time": 12.1667, "x": 42.63, "y": -10.43, "curve": [12.445, 42.64, 12.724, 12.42, 12.445, -10.44, 12.724, -2.23]}, {"time": 13, "x": -10.38, "y": 3.95}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.167, 1.007, 0.333, 0.921, 0.167, 0.995, 0.333, 1.053]}, {"time": 0.5, "x": 0.921, "y": 1.053, "curve": [0.722, 0.921, 0.944, 1.075, 0.722, 1.053, 0.944, 0.949]}, {"time": 1.1667, "x": 1.075, "y": 0.949, "curve": [1.389, 1.075, 1.611, 0.921, 1.389, 0.949, 1.611, 1.053]}, {"time": 1.8333, "x": 0.921, "y": 1.053, "curve": [2.056, 0.921, 2.278, 1.075, 2.056, 1.053, 2.278, 0.949]}, {"time": 2.5, "x": 1.075, "y": 0.949, "curve": [2.722, 1.075, 2.944, 0.921, 2.722, 0.949, 2.944, 1.053]}, {"time": 3.1667, "x": 0.921, "y": 1.053, "curve": [3.389, 0.921, 3.611, 1.075, 3.389, 1.053, 3.611, 0.949]}, {"time": 3.8333, "x": 1.075, "y": 0.949, "curve": [4.056, 1.075, 4.278, 0.921, 4.056, 0.949, 4.278, 1.053]}, {"time": 4.5, "x": 0.921, "y": 1.053, "curve": [4.722, 0.921, 4.944, 1.075, 4.722, 1.053, 4.944, 0.949]}, {"time": 5.1667, "x": 1.075, "y": 0.949, "curve": [5.389, 1.075, 5.611, 0.921, 5.389, 0.949, 5.611, 1.053]}, {"time": 5.8333, "x": 0.921, "y": 1.053, "curve": [6, 0.921, 6.167, 1.075, 6, 1.053, 6.167, 0.949]}, {"time": 6.3333, "x": 1.075, "y": 0.949, "curve": [6.5, 1.075, 6.667, 0.921, 6.5, 0.949, 6.667, 1.053]}, {"time": 6.8333, "x": 0.921, "y": 1.053, "curve": [7.056, 0.921, 7.278, 1.075, 7.056, 1.053, 7.278, 0.949]}, {"time": 7.5, "x": 1.075, "y": 0.949, "curve": [7.722, 1.075, 7.944, 0.921, 7.722, 0.949, 7.944, 1.053]}, {"time": 8.1667, "x": 0.921, "y": 1.053, "curve": [8.389, 0.921, 8.611, 1.075, 8.389, 1.053, 8.611, 0.949]}, {"time": 8.8333, "x": 1.075, "y": 0.949, "curve": [9.056, 1.075, 9.278, 0.921, 9.056, 0.949, 9.278, 1.053]}, {"time": 9.5, "x": 0.921, "y": 1.053, "curve": [9.722, 0.921, 9.944, 1.075, 9.722, 1.053, 9.944, 0.949]}, {"time": 10.1667, "x": 1.075, "y": 0.949, "curve": [10.389, 1.075, 10.611, 0.921, 10.389, 0.949, 10.611, 1.053]}, {"time": 10.8333, "x": 0.921, "y": 1.053, "curve": [11.056, 0.921, 11.278, 1.075, 11.056, 1.053, 11.278, 0.949]}, {"time": 11.5, "x": 1.075, "y": 0.949, "curve": [11.722, 1.075, 11.944, 0.921, 11.722, 0.949, 11.944, 1.053]}, {"time": 12.1667, "x": 0.921, "y": 1.053, "curve": [12.389, 0.921, 12.611, 1.075, 12.389, 1.053, 12.611, 0.949]}, {"time": 12.8333, "x": 1.075, "y": 0.949, "curve": [12.889, 1.075, 12.944, 1.065, 12.889, 0.949, 12.944, 0.956]}, {"time": 13, "x": 1.05, "y": 0.966}]}, "RU_L2": {"translate": [{"x": 4.51, "y": -0.07, "curve": [0.225, -15.16, 0.446, -35.1, 0.225, 5.26, 0.446, 10.66]}, {"time": 0.6667, "x": -35.1, "y": 10.66, "curve": [1.111, -35.1, 1.556, 44.08, 1.111, 10.66, 1.556, -10.79]}, {"time": 2, "x": 44.1, "y": -10.79, "curve": [2.444, 44.12, 2.889, -35.08, 2.444, -10.8, 2.889, 10.66]}, {"time": 3.3333, "x": -35.1, "y": 10.66, "curve": [3.778, -35.12, 4.222, 44.08, 3.778, 10.67, 4.222, -10.79]}, {"time": 4.6667, "x": 44.1, "y": -10.79, "curve": [5.111, 44.12, 5.556, -35.07, 5.111, -10.8, 5.556, 10.66]}, {"time": 6, "x": -35.1, "y": 10.66, "curve": [6.333, -35.12, 6.667, 44.1, 6.333, 10.67, 6.667, -10.79]}, {"time": 7, "x": 44.1, "y": -10.79, "curve": [7.444, 44.11, 7.889, 4.51, 7.444, -10.8, 7.889, -0.07]}, {"time": 8.3333, "x": 4.5, "y": -0.06, "curve": [8.778, 4.49, 9.222, 44.08, 8.778, -0.06, 9.222, -10.79]}, {"time": 9.6667, "x": 44.1, "y": -10.79, "curve": [10.111, 44.12, 10.556, -35.08, 10.111, -10.8, 10.556, 10.66]}, {"time": 11, "x": -35.1, "y": 10.66, "curve": [11.444, -35.12, 11.889, 44.08, 11.444, 10.67, 11.889, -10.79]}, {"time": 12.3333, "x": 44.1, "y": -10.79, "curve": [12.557, 44.11, 12.781, 24.44, 12.557, -10.8, 12.781, -5.47]}, {"time": 13, "x": 4.51, "y": -0.07}], "scale": [{"x": 1.075, "y": 0.949, "curve": [0.222, 1.075, 0.444, 0.921, 0.222, 0.949, 0.444, 1.053]}, {"time": 0.6667, "x": 0.921, "y": 1.053, "curve": [0.889, 0.921, 1.111, 1.075, 0.889, 1.053, 1.111, 0.949]}, {"time": 1.3333, "x": 1.075, "y": 0.949, "curve": [1.556, 1.075, 1.778, 0.921, 1.556, 0.949, 1.778, 1.053]}, {"time": 2, "x": 0.921, "y": 1.053, "curve": [2.222, 0.921, 2.444, 1.075, 2.222, 1.053, 2.444, 0.949]}, {"time": 2.6667, "x": 1.075, "y": 0.949, "curve": [2.889, 1.075, 3.111, 0.921, 2.889, 0.949, 3.111, 1.053]}, {"time": 3.3333, "x": 0.921, "y": 1.053, "curve": [3.556, 0.921, 3.778, 1.075, 3.556, 1.053, 3.778, 0.949]}, {"time": 4, "x": 1.075, "y": 0.949, "curve": [4.222, 1.075, 4.444, 0.921, 4.222, 0.949, 4.444, 1.053]}, {"time": 4.6667, "x": 0.921, "y": 1.053, "curve": [4.889, 0.921, 5.111, 1.075, 4.889, 1.053, 5.111, 0.949]}, {"time": 5.3333, "x": 1.075, "y": 0.949, "curve": [5.556, 1.075, 5.778, 0.921, 5.556, 0.949, 5.778, 1.053]}, {"time": 6, "x": 0.921, "y": 1.053, "curve": [6.167, 0.921, 6.333, 1.075, 6.167, 1.053, 6.333, 0.949]}, {"time": 6.5, "x": 1.075, "y": 0.949, "curve": [6.667, 1.075, 6.833, 0.921, 6.667, 0.949, 6.833, 1.053]}, {"time": 7, "x": 0.921, "y": 1.053, "curve": [7.222, 0.921, 7.444, 1.075, 7.222, 1.053, 7.444, 0.949]}, {"time": 7.6667, "x": 1.075, "y": 0.949, "curve": [7.889, 1.075, 8.111, 0.921, 7.889, 0.949, 8.111, 1.053]}, {"time": 8.3333, "x": 0.921, "y": 1.053, "curve": [8.556, 0.921, 8.778, 1.075, 8.556, 1.053, 8.778, 0.949]}, {"time": 9, "x": 1.075, "y": 0.949, "curve": [9.222, 1.075, 9.444, 0.921, 9.222, 0.949, 9.444, 1.053]}, {"time": 9.6667, "x": 0.921, "y": 1.053, "curve": [9.889, 0.921, 10.111, 1.075, 9.889, 1.053, 10.111, 0.949]}, {"time": 10.3333, "x": 1.075, "y": 0.949, "curve": [10.556, 1.075, 10.778, 0.921, 10.556, 0.949, 10.778, 1.053]}, {"time": 11, "x": 0.921, "y": 1.053, "curve": [11.222, 0.921, 11.444, 1.075, 11.222, 1.053, 11.444, 0.949]}, {"time": 11.6667, "x": 1.075, "y": 0.949, "curve": [11.889, 1.075, 12.111, 0.921, 11.889, 0.949, 12.111, 1.053]}, {"time": 12.3333, "x": 0.921, "y": 1.053, "curve": [12.556, 0.921, 12.778, 1.075, 12.556, 1.053, 12.778, 0.949]}, {"time": 13, "x": 1.075, "y": 0.949}]}, "RU_L3": {"translate": [{"x": 16.5, "y": -3.35, "curve": [0.279, -6.21, 0.556, -36.42, 0.279, 2.83, 0.556, 11.07]}, {"time": 0.8333, "x": -36.42, "y": 11.07, "curve": [1.278, -36.42, 1.722, 41.14, 1.278, 11.07, 1.722, -10.07]}, {"time": 2.1667, "x": 41.16, "y": -10.07, "curve": [2.611, 41.18, 3.056, -36.4, 2.611, -10.08, 3.056, 11.06]}, {"time": 3.5, "x": -36.42, "y": 11.07, "curve": [3.944, -36.44, 4.389, 41.14, 3.944, 11.07, 4.389, -10.07]}, {"time": 4.8333, "x": 41.16, "y": -10.07, "curve": [5.278, 41.18, 5.722, -36.4, 5.278, -10.08, 5.722, 11.06]}, {"time": 6.1667, "x": -36.42, "y": 11.07, "curve": [6.5, -36.44, 6.833, 41.16, 6.5, 11.07, 6.833, -10.07]}, {"time": 7.1667, "x": 41.16, "y": -10.07, "curve": [7.611, 41.17, 8.056, 2.38, 7.611, -10.08, 8.056, 0.49]}, {"time": 8.5, "x": 2.37, "y": 0.5, "curve": [8.944, 2.36, 9.389, 41.14, 8.944, 0.5, 9.389, -10.07]}, {"time": 9.8333, "x": 41.16, "y": -10.07, "curve": [10.278, 41.18, 10.722, -36.4, 10.278, -10.08, 10.722, 11.06]}, {"time": 11.1667, "x": -36.42, "y": 11.07, "curve": [11.611, -36.44, 12.056, 41.14, 11.611, 11.07, 12.056, -10.07]}, {"time": 12.5, "x": 41.16, "y": -10.07, "curve": [12.667, 41.17, 12.835, 30.22, 12.667, -10.08, 12.835, -7.09]}, {"time": 13, "x": 16.5, "y": -3.35}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.056, 1.065, 0.111, 1.075, 0.056, 0.956, 0.111, 0.949]}, {"time": 0.1667, "x": 1.075, "y": 0.949, "curve": [0.389, 1.075, 0.611, 0.921, 0.389, 0.949, 0.611, 1.053]}, {"time": 0.8333, "x": 0.921, "y": 1.053, "curve": [1.056, 0.921, 1.278, 1.075, 1.056, 1.053, 1.278, 0.949]}, {"time": 1.5, "x": 1.075, "y": 0.949, "curve": [1.722, 1.075, 1.944, 0.921, 1.722, 0.949, 1.944, 1.053]}, {"time": 2.1667, "x": 0.921, "y": 1.053, "curve": [2.389, 0.921, 2.611, 1.075, 2.389, 1.053, 2.611, 0.949]}, {"time": 2.8333, "x": 1.075, "y": 0.949, "curve": [3.056, 1.075, 3.278, 0.921, 3.056, 0.949, 3.278, 1.053]}, {"time": 3.5, "x": 0.921, "y": 1.053, "curve": [3.722, 0.921, 3.944, 1.075, 3.722, 1.053, 3.944, 0.949]}, {"time": 4.1667, "x": 1.075, "y": 0.949, "curve": [4.389, 1.075, 4.611, 0.921, 4.389, 0.949, 4.611, 1.053]}, {"time": 4.8333, "x": 0.921, "y": 1.053, "curve": [5.056, 0.921, 5.278, 1.075, 5.056, 1.053, 5.278, 0.949]}, {"time": 5.5, "x": 1.075, "y": 0.949, "curve": [5.722, 1.075, 5.944, 0.921, 5.722, 0.949, 5.944, 1.053]}, {"time": 6.1667, "x": 0.921, "y": 1.053, "curve": [6.333, 0.921, 6.5, 1.075, 6.333, 1.053, 6.5, 0.949]}, {"time": 6.6667, "x": 1.075, "y": 0.949, "curve": [6.833, 1.075, 7, 0.921, 6.833, 0.949, 7, 1.053]}, {"time": 7.1667, "x": 0.921, "y": 1.053, "curve": [7.389, 0.921, 7.611, 1.075, 7.389, 1.053, 7.611, 0.949]}, {"time": 7.8333, "x": 1.075, "y": 0.949, "curve": [8.056, 1.075, 8.278, 0.921, 8.056, 0.949, 8.278, 1.053]}, {"time": 8.5, "x": 0.921, "y": 1.053, "curve": [8.722, 0.921, 8.944, 1.075, 8.722, 1.053, 8.944, 0.949]}, {"time": 9.1667, "x": 1.075, "y": 0.949, "curve": [9.389, 1.075, 9.611, 0.921, 9.389, 0.949, 9.611, 1.053]}, {"time": 9.8333, "x": 0.921, "y": 1.053, "curve": [10.056, 0.921, 10.278, 1.075, 10.056, 1.053, 10.278, 0.949]}, {"time": 10.5, "x": 1.075, "y": 0.949, "curve": [10.722, 1.075, 10.944, 0.921, 10.722, 0.949, 10.944, 1.053]}, {"time": 11.1667, "x": 0.921, "y": 1.053, "curve": [11.389, 0.921, 11.611, 1.075, 11.389, 1.053, 11.611, 0.949]}, {"time": 11.8333, "x": 1.075, "y": 0.949, "curve": [12.056, 1.075, 12.278, 0.921, 12.056, 0.949, 12.278, 1.053]}, {"time": 12.5, "x": 0.921, "y": 1.053, "curve": [12.667, 0.921, 12.833, 1.007, 12.667, 1.053, 12.833, 0.995]}, {"time": 13, "x": 1.05, "y": 0.966}]}, "leg_L4": {"rotate": [{"value": -0.13, "curve": "stepped"}, {"time": 5.3333, "value": -0.13, "curve": [5.667, -0.13, 6, -0.13]}, {"time": 6.3333, "value": -0.13, "curve": [6.778, -0.12, 7.222, -3.13]}, {"time": 7.6667, "value": -3.13, "curve": [8.111, -3.13, 8.556, -0.13]}, {"time": 9, "value": -0.13}]}, "leg_L1": {"translate": [{"y": -15.73, "curve": [0.444, 0, 0.889, 0, 0.444, -15.73, 0.889, 7.75]}, {"time": 1.3333, "y": 7.76, "curve": [1.778, 0, 2.222, 0, 1.778, 7.76, 2.222, -15.73]}, {"time": 2.6667, "y": -15.73, "curve": [3.111, 0, 3.556, 0, 3.111, -15.74, 3.556, 7.75]}, {"time": 4, "y": 7.76, "curve": [4.444, 0, 4.889, 0, 4.444, 7.76, 4.889, -15.72]}, {"time": 5.3333, "y": -15.73, "curve": [5.667, 0, 6, -2.76, 5.667, -15.74, 6, 29.79]}, {"time": 6.3333, "x": -2.76, "y": 29.79, "curve": [6.778, -2.76, 7.222, -1.38, 6.778, 29.8, 7.222, 7.04]}, {"time": 7.6667, "x": -1.38, "y": 7.03, "curve": [8.111, -1.38, 8.556, -2.76, 8.111, 7.03, 8.556, 29.78]}, {"time": 9, "x": -2.76, "y": 29.79, "curve": [9.444, -2.76, 9.889, 0, 9.444, 29.81, 9.889, -15.73]}, {"time": 10.3333, "y": -15.73, "curve": [10.778, 0, 11.222, 0, 10.778, -15.74, 11.222, 7.75]}, {"time": 11.6667, "y": 7.76, "curve": [12.111, 0, 12.556, 0, 12.111, 7.76, 12.556, -15.73]}, {"time": 13, "y": -15.73}]}, "leg_R4": {"rotate": [{"value": 0.12, "curve": "stepped"}, {"time": 5.3333, "value": 0.12, "curve": [5.667, 0.12, 6, 0.11]}, {"time": 6.3333, "value": 0.12, "curve": [6.778, 0.12, 7.222, -2.68]}, {"time": 7.6667, "value": -2.68, "curve": [8.111, -2.68, 8.556, 0.12]}, {"time": 9, "value": 0.12}]}, "leg_R1": {"translate": [{"y": 3.12, "curve": [0.444, 0, 0.889, 0, 0.444, 3.12, 0.889, -6.2]}, {"time": 1.3333, "y": -6.2, "curve": [1.778, 0, 2.222, 0, 1.778, -6.2, 2.222, 3.12]}, {"time": 2.6667, "y": 3.12, "curve": [3.111, 0, 3.556, 0, 3.111, 3.13, 3.556, -6.2]}, {"time": 4, "y": -6.2, "curve": [4.444, 0, 4.889, 0, 4.444, -6.2, 4.889, 3.12]}, {"time": 5.3333, "y": 3.12, "curve": [5.667, 0, 6, -0.12, 5.667, 3.13, 6, -7.58]}, {"time": 6.3333, "x": -0.12, "y": -7.58, "curve": [6.778, -0.12, 7.222, -0.06, 6.778, -7.58, 7.222, -2.23]}, {"time": 7.6667, "x": -0.06, "y": -2.23, "curve": [8.111, -0.06, 8.556, -0.12, 8.111, -2.23, 8.556, -7.58]}, {"time": 9, "x": -0.12, "y": -7.58, "curve": [9.444, -0.12, 9.889, 0, 9.444, -7.59, 9.889, 3.12]}, {"time": 10.3333, "y": 3.12, "curve": [10.778, 0, 11.222, 0, 10.778, 3.13, 11.222, -6.2]}, {"time": 11.6667, "y": -6.2, "curve": [12.111, 0, 12.556, 0, 12.111, -6.2, 12.556, 3.12]}, {"time": 13, "y": 3.12}]}, "bodyround": {"translate": [{"x": -19.04, "y": -81.13, "curve": [0.168, -35.24, 0.334, -48.54, 0.168, -156.08, 0.334, -217.57]}, {"time": 0.5, "x": -48.54, "y": -217.57, "curve": [0.944, -48.54, 1.389, 44.18, 0.944, -217.57, 1.389, 211.3]}, {"time": 1.8333, "x": 44.21, "y": 211.41, "curve": [2.278, 44.23, 2.722, -48.51, 2.278, 211.52, 2.722, -217.46]}, {"time": 3.1667, "x": -48.54, "y": -217.57, "curve": [3.611, -48.56, 4.056, 44.18, 3.611, -217.68, 4.056, 211.3]}, {"time": 4.5, "x": 44.21, "y": 211.41, "curve": [4.944, 44.23, 5.389, -48.5, 4.944, 211.52, 5.389, -217.43]}, {"time": 5.8333, "x": -48.54, "y": -217.57, "curve": [6.167, -48.56, 6.5, 44.19, 6.167, -217.68, 6.5, 211.33]}, {"time": 6.8333, "x": 44.21, "y": 211.41, "curve": [7.278, 44.23, 7.722, -48.51, 7.278, 211.52, 7.722, -217.46]}, {"time": 8.1667, "x": -48.54, "y": -217.57, "curve": [8.611, -48.56, 9.056, 44.18, 8.611, -217.68, 9.056, 211.3]}, {"time": 9.5, "x": 44.21, "y": 211.41, "curve": [9.944, 44.23, 10.389, -48.51, 9.944, 211.52, 10.389, -217.46]}, {"time": 10.8333, "x": -48.54, "y": -217.57, "curve": [11.278, -48.56, 11.722, 44.18, 11.278, -217.68, 11.722, 211.3]}, {"time": 12.1667, "x": 44.21, "y": 211.41, "curve": [12.445, 44.22, 12.724, 8.16, 12.445, 211.47, 12.724, 44.68]}, {"time": 13, "x": -19.04, "y": -81.13}]}, "headround3": {"translate": [{"x": 29.33, "curve": [0.279, -35.26, 0.556, -121.18, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -121.18, "curve": [1.278, -121.18, 1.722, 99.42, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 99.47, "curve": [2.611, 99.53, 3.056, -121.13, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -121.18, "curve": [3.944, -121.24, 4.389, 99.42, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 99.47, "curve": [5.278, 99.53, 5.722, -121.11, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -121.18, "curve": [6.5, -121.24, 6.833, 99.43, 6.611, 0, 6.722, 0]}, {"time": 7.1667, "x": 99.47, "curve": [7.611, 99.53, 8.056, -121.13, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -121.18, "curve": [8.944, -121.24, 9.389, 99.42, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 99.47, "curve": [10.278, 99.53, 10.722, -121.13, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -121.18, "curve": [11.611, -121.24, 12.056, 99.42, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 99.47, "curve": [12.667, 99.49, 12.835, 68.36, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 29.33}]}, "headround": {"translate": [{"y": 75.51, "curve": [0.336, 0, 0.668, 0, 0.336, 14.05, 0.668, -107.3]}, {"time": 1, "y": -107.3, "curve": [1.444, 0, 1.889, 0, 1.444, -107.3, 1.889, 110.25]}, {"time": 2.3333, "y": 110.3, "curve": [2.778, 0, 3.222, 0, 2.778, 110.36, 3.222, -107.25]}, {"time": 3.6667, "y": -107.3, "curve": [4.111, 0, 4.556, 0, 4.111, -107.36, 4.556, 110.25]}, {"time": 5, "y": 110.3, "curve": [5.444, 0, 5.889, 0, 5.444, 110.36, 5.889, -107.23]}, {"time": 6.3333, "y": -107.3, "curve": [6.778, 0, 6.889, 0, 6.667, -107.36, 7, 110.26]}, {"time": 7.3333, "y": 110.3, "curve": [7.778, 0, 8.222, 0, 7.778, 110.36, 8.222, -107.25]}, {"time": 8.6667, "y": -107.3, "curve": [9.111, 0, 9.556, 0, 9.111, -107.36, 9.556, 110.25]}, {"time": 10, "y": 110.3, "curve": [10.444, 0, 10.889, 0, 10.444, 110.36, 10.889, -107.25]}, {"time": 11.3333, "y": -107.3, "curve": [11.778, 0, 12.222, 0, 11.778, -107.36, 12.222, 110.25]}, {"time": 12.6667, "y": 110.3, "curve": [12.779, 0, 12.892, 0, 12.779, 110.32, 12.892, 96.36]}, {"time": 13, "y": 75.51}]}, "tunround": {"translate": [{"x": 211.71, "curve": [0.057, 224.07, 0.112, 233.49, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": 233.49, "curve": [0.611, 233.49, 1.056, -229.64, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": -229.76, "curve": [1.944, -229.87, 2.389, 233.37, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": 233.49, "curve": [3.278, 233.61, 3.722, -229.64, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": -229.76, "curve": [4.611, -229.87, 5.056, 233.34, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": 233.49, "curve": [5.833, 233.61, 6.167, -229.67, 5.944, 0, 6.056, 0]}, {"time": 6.5, "x": -229.76, "curve": [6.944, -229.87, 7.389, 233.37, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 233.49, "curve": [8.278, 233.61, 8.722, -229.64, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": -229.76, "curve": [9.611, -229.87, 10.056, 233.37, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": 233.49, "curve": [10.944, 233.61, 11.389, -229.64, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": -229.76, "curve": [12.223, -229.86, 12.613, 123.87, 12.223, 0, 12.613, 0]}, {"time": 13, "x": 211.71}]}, "head_R2a": {"rotate": [{"value": 5.56, "curve": "stepped"}, {"time": 5.3333, "value": 5.56, "curve": [5.667, 5.56, 6, 5.56]}, {"time": 6.3333, "value": 5.56, "curve": [6.778, 5.56, 7.222, 8.15]}, {"time": 7.6667, "value": 8.15, "curve": [8.111, 8.15, 8.556, 5.56]}, {"time": 9, "value": 5.56}], "translate": [{"x": -0.61, "y": 0.95, "curve": "stepped"}, {"time": 6.3333, "x": -0.61, "y": 0.95, "curve": [6.778, -0.61, 7.222, -0.61, 6.778, 0.95, 7.222, 0.95]}, {"time": 7.6667, "x": -0.61, "y": 0.95, "curve": [8.111, -0.61, 8.556, -0.61, 8.111, 0.95, 8.556, 0.95]}, {"time": 9, "x": -0.61, "y": 0.95}]}, "head_R2b": {"rotate": [{"value": 8.78, "curve": "stepped"}, {"time": 5.3333, "value": 8.78, "curve": [5.667, 8.78, 6, 8.78]}, {"time": 6.3333, "value": 8.78, "curve": [6.778, 8.78, 7.222, 12.89]}, {"time": 7.6667, "value": 12.89, "curve": [8.111, 12.89, 8.556, 8.78]}, {"time": 9, "value": 8.78}], "translate": [{"x": -6.67, "y": -5.27, "curve": "stepped"}, {"time": 5.3333, "x": -6.67, "y": -5.27, "curve": [5.667, -6.67, 6, -6.67, 5.667, -5.27, 6, -5.27]}, {"time": 6.3333, "x": -6.67, "y": -5.27, "curve": [6.778, -6.67, 7.222, -10.28, 6.778, -5.27, 7.222, -8.05]}, {"time": 7.6667, "x": -10.28, "y": -8.05, "curve": [8.111, -10.28, 8.556, -6.67, 8.111, -8.05, 8.556, -5.27]}, {"time": 9, "x": -6.67, "y": -5.27}]}, "head_R2c": {"rotate": [{"value": 11.31, "curve": "stepped"}, {"time": 5.3333, "value": 11.31, "curve": [5.667, 11.31, 6, 11.31]}, {"time": 6.3333, "value": 11.31, "curve": [6.778, 11.31, 7.222, 17.22]}, {"time": 7.6667, "value": 17.23, "curve": [8.111, 17.23, 8.556, 11.31]}, {"time": 9, "value": 11.31}], "translate": [{"x": -14.03, "y": -16.25, "curve": "stepped"}, {"time": 5.3333, "x": -14.03, "y": -16.25, "curve": [5.667, -14.03, 6, -14.03, 5.667, -16.25, 6, -16.25]}, {"time": 6.3333, "x": -14.03, "y": -16.25, "curve": [6.778, -14.03, 7.222, -21.81, 6.778, -16.25, 7.222, -24.19]}, {"time": 7.6667, "x": -21.81, "y": -24.2, "curve": [8.111, -21.81, 8.556, -14.03, 8.111, -24.2, 8.556, -16.25]}, {"time": 9, "x": -14.03, "y": -16.25}]}, "head_R2d": {"rotate": [{"value": 11.68, "curve": "stepped"}, {"time": 5.3333, "value": 11.68, "curve": [5.667, 11.68, 6, 11.68]}, {"time": 6.3333, "value": 11.68, "curve": [6.778, 11.68, 7.222, 19.66]}, {"time": 7.6667, "value": 19.66, "curve": [8.111, 19.67, 8.556, 11.68]}, {"time": 9, "value": 11.68}], "translate": [{"x": -23.09, "y": -30.67, "curve": "stepped"}, {"time": 5.3333, "x": -23.09, "y": -30.67, "curve": [5.667, -23.09, 6, -23.1, 5.667, -30.67, 6, -30.67]}, {"time": 6.3333, "x": -23.09, "y": -30.67, "curve": [6.778, -23.09, 7.222, -35.62, 6.778, -30.67, 7.222, -46.69]}, {"time": 7.6667, "x": -35.62, "y": -46.69, "curve": [8.111, -35.62, 8.556, -23.09, 8.111, -46.7, 8.556, -30.67]}, {"time": 9, "x": -23.09, "y": -30.67}]}, "head_R2e": {"rotate": [{"value": 7.51, "curve": "stepped"}, {"time": 5.3333, "value": 7.51, "curve": [5.667, 7.51, 6, 7.51]}, {"time": 6.3333, "value": 7.51, "curve": [6.778, 7.5, 7.222, 17.78]}, {"time": 7.6667, "value": 17.78, "curve": [8.111, 17.79, 8.556, 7.51]}, {"time": 9, "value": 7.51}], "translate": [{"x": -32.35, "y": -45.62, "curve": "stepped"}, {"time": 5.3333, "x": -32.35, "y": -45.62, "curve": [5.667, -32.35, 6, -32.36, 5.667, -45.62, 6, -45.63]}, {"time": 6.3333, "x": -32.35, "y": -45.62, "curve": [6.778, -32.35, 7.222, -50.12, 6.778, -45.61, 7.222, -72.91]}, {"time": 7.6667, "x": -50.12, "y": -72.92, "curve": [8.111, -50.13, 8.556, -32.35, 8.111, -72.92, 8.556, -45.62]}, {"time": 9, "x": -32.35, "y": -45.62}]}, "head_R2f": {"rotate": [{"value": -2.79, "curve": "stepped"}, {"time": 5.3333, "value": -2.79, "curve": [5.667, -2.79, 6, -2.78]}, {"time": 6.3333, "value": -2.79, "curve": [6.778, -2.79, 7.222, 9.42]}, {"time": 7.6667, "value": 9.43, "curve": [8.111, 9.43, 8.556, -2.79]}, {"time": 9, "value": -2.79}], "translate": [{"x": -38.67, "y": -54.95, "curve": "stepped"}, {"time": 5.3333, "x": -38.67, "y": -54.95, "curve": [5.667, -38.67, 6, -38.68, 5.667, -54.95, 6, -54.96]}, {"time": 6.3333, "x": -38.67, "y": -54.95, "curve": [6.778, -38.67, 7.222, -63.41, 6.778, -54.94, 7.222, -96.41]}, {"time": 7.6667, "x": -63.42, "y": -96.42, "curve": [8.111, -63.42, 8.556, -38.67, 8.111, -96.43, 8.556, -54.95]}, {"time": 9, "x": -38.67, "y": -54.95}]}, "head_R2g": {"rotate": [{"value": -15.84, "curve": "stepped"}, {"time": 5.3333, "value": -15.84, "curve": [5.667, -15.84, 6, -15.84]}, {"time": 6.3333, "value": -15.84, "curve": [6.778, -15.84, 7.222, -3.64]}, {"time": 7.6667, "value": -3.64, "curve": [8.111, -3.64, 8.556, -15.84]}, {"time": 9, "value": -15.84}], "translate": [{"x": -37.62, "y": -50.72, "curve": "stepped"}, {"time": 5.3333, "x": -37.62, "y": -50.72, "curve": [5.667, -37.62, 6, -37.63, 5.667, -50.72, 6, -50.73]}, {"time": 6.3333, "x": -37.62, "y": -50.72, "curve": [6.778, -37.62, 7.222, -72.89, 6.778, -50.71, 7.222, -107.61]}, {"time": 7.6667, "x": -72.89, "y": -107.63, "curve": [8.111, -72.9, 8.556, -37.62, 8.111, -107.64, 8.556, -50.72]}, {"time": 9, "x": -37.62, "y": -50.72}]}, "head_R2h": {"rotate": [{"value": -26.23, "curve": "stepped"}, {"time": 5.3333, "value": -26.23, "curve": [5.667, -26.23, 6, -26.23]}, {"time": 6.3333, "value": -26.23, "curve": [6.778, -26.23, 7.222, -15.67]}, {"time": 7.6667, "value": -15.66, "curve": [8.111, -15.66, 8.556, -26.23]}, {"time": 9, "value": -26.23}], "translate": [{"x": -24.48, "y": -31.06, "curve": "stepped"}, {"time": 5.3333, "x": -24.48, "y": -31.06, "curve": [5.667, -24.48, 6, -24.49, 5.667, -31.06, 6, -31.07]}, {"time": 6.3333, "x": -24.48, "y": -31.06, "curve": [6.778, -24.47, 7.222, -73.28, 6.778, -31.04, 7.222, -100.86]}, {"time": 7.6667, "x": -73.29, "y": -100.88, "curve": [8.111, -73.3, 8.556, -24.48, 8.111, -100.89, 8.556, -31.06]}, {"time": 9, "x": -24.48, "y": -31.06}]}, "arm_R4": {"rotate": [{"value": -6.62, "curve": [0.279, 14.02, 0.556, 41.47]}, {"time": 0.8333, "value": 41.47, "curve": [1.278, 41.47, 1.722, -29.01]}, {"time": 2.1667, "value": -29.03, "curve": [2.611, -29.04, 3.056, 41.45]}, {"time": 3.5, "value": 41.47, "curve": [3.944, 41.49, 4.389, -29.01]}, {"time": 4.8333, "value": -29.03, "curve": [5.278, -29.04, 5.722, 41.45]}, {"time": 6.1667, "value": 41.47, "curve": [6.5, 41.49, 6.833, -29.01]}, {"time": 7.1667, "value": -29.03, "curve": [7.611, -29.04, 8.056, 41.45]}, {"time": 8.5, "value": 41.47, "curve": [8.944, 41.49, 9.389, -29.01]}, {"time": 9.8333, "value": -29.03, "curve": [10.278, -29.04, 10.722, 41.45]}, {"time": 11.1667, "value": 41.47, "curve": [11.611, 41.49, 12.056, -29.01]}, {"time": 12.5, "value": -29.03, "curve": [12.667, -29.03, 12.835, -19.09]}, {"time": 13, "value": -6.62}], "translate": [{"x": 11.04, "y": 23.08, "curve": [0.279, -31.8, 0.556, -88.8, 0.279, -11.32, 0.556, -57.09]}, {"time": 0.8333, "x": -88.8, "y": -57.09, "curve": [1.278, -88.8, 1.722, 57.54, 1.278, -57.09, 1.722, 60.41]}, {"time": 2.1667, "x": 57.58, "y": 60.44, "curve": [2.611, 57.61, 3.056, -88.77, 2.611, 60.47, 3.056, -57.06]}, {"time": 3.5, "x": -88.8, "y": -57.09, "curve": [3.944, -88.84, 4.389, 57.54, 3.944, -57.12, 4.389, 60.41]}, {"time": 4.8333, "x": 57.58, "y": 60.44, "curve": [5.278, 57.61, 5.722, -88.75, 5.278, 60.47, 5.722, -57.05]}, {"time": 6.1667, "x": -88.8, "y": -57.09, "curve": [6.5, -88.84, 6.833, 57.55, 6.5, -57.12, 6.833, 60.42]}, {"time": 7.1667, "x": 57.58, "y": 60.44, "curve": [7.611, 57.61, 8.056, -88.77, 7.611, 60.47, 8.056, -57.06]}, {"time": 8.5, "x": -88.8, "y": -57.09, "curve": [8.944, -88.84, 9.389, 57.54, 8.944, -57.12, 9.389, 60.41]}, {"time": 9.8333, "x": 57.58, "y": 60.44, "curve": [10.278, 57.61, 10.722, -88.77, 10.278, 60.47, 10.722, -57.06]}, {"time": 11.1667, "x": -88.8, "y": -57.09, "curve": [11.611, -88.84, 12.056, 57.54, 11.611, -57.12, 12.056, 60.41]}, {"time": 12.5, "x": 57.58, "y": 60.44, "curve": [12.667, 57.59, 12.835, 36.94, 12.667, 60.45, 12.835, 43.87]}, {"time": 13, "x": 11.04, "y": 23.08}]}, "hand_R2": {"rotate": [{"value": -36.2, "curve": [0.336, -7.46, 0.668, 49.28]}, {"time": 1, "value": 49.28, "curve": [1.444, 49.28, 1.889, -52.44]}, {"time": 2.3333, "value": -52.47, "curve": [2.778, -52.49, 3.222, 49.25]}, {"time": 3.6667, "value": 49.28, "curve": [4.111, 49.3, 4.556, -52.44]}, {"time": 5, "value": -52.47, "curve": [5.444, -52.49, 5.889, 49.24]}, {"time": 6.3333, "value": 49.28, "curve": [6.667, 49.3, 7, -52.45]}, {"time": 7.3333, "value": -52.47, "curve": [7.778, -52.49, 8.222, 49.25]}, {"time": 8.6667, "value": 49.28, "curve": [9.111, 49.3, 9.556, -52.44]}, {"time": 10, "value": -52.47, "curve": [10.444, -52.49, 10.889, 49.28]}, {"time": 11.3333, "value": 49.28, "curve": [11.778, 49.28, 12.222, -52.44]}, {"time": 12.6667, "value": -52.47, "curve": [12.779, -52.48, 12.892, -45.95]}, {"time": 13, "value": -36.2}]}, "head_L2a": {"rotate": [{"value": 351.97, "curve": "stepped"}, {"time": 5.3333, "value": 351.97, "curve": [5.667, 351.97, 6, 351.97]}, {"time": 6.3333, "value": 351.97, "curve": [6.778, 351.97, 7.222, 354.82]}, {"time": 7.6667, "value": 354.82, "curve": [8.111, 354.82, 8.556, 351.97]}, {"time": 9, "value": 351.97}], "translate": [{"time": 6.3333, "curve": [6.778, 0, 7.222, 0, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "curve": [8.111, 0, 8.556, 0, 8.111, 0, 8.556, 0]}, {"time": 9}]}, "head_L2b": {"rotate": [{"value": 351.94, "curve": "stepped"}, {"time": 5.3333, "value": 351.94, "curve": [5.667, 351.94, 6, 351.95]}, {"time": 6.3333, "value": 351.94, "curve": [6.778, 351.94, 7.222, 357.01]}, {"time": 7.6667, "value": 357.01, "curve": [8.111, 357.01, 8.556, 351.94]}, {"time": 9, "value": 351.94}], "translate": [{"x": -1.29, "y": 12.33, "curve": "stepped"}, {"time": 5.3333, "x": -1.29, "y": 12.33, "curve": [5.667, -1.29, 6, -1.29, 5.667, 12.33, 6, 12.33]}, {"time": 6.3333, "x": -1.29, "y": 12.33, "curve": [6.778, -1.29, 7.222, -0.99, 6.778, 12.33, 7.222, 7.9]}, {"time": 7.6667, "x": -0.99, "y": 7.9, "curve": [8.111, -0.99, 8.556, -1.29, 8.111, 7.9, 8.556, 12.33]}, {"time": 9, "x": -1.29, "y": 12.33}]}, "head_L2c": {"rotate": [{"value": 352.4, "curve": "stepped"}, {"time": 5.3333, "value": 352.4, "curve": [5.667, 352.4, 6, 352.33]}, {"time": 6.3333, "value": 352.4, "curve": [6.778, 352.49, 7.222, -0.75]}, {"time": 7.6667, "value": -0.84, "curve": [8.111, -0.93, 8.556, 352.4]}, {"time": 9, "value": 352.4}], "translate": [{"x": -2.62, "y": 24.7, "curve": "stepped"}, {"time": 5.3333, "x": -2.62, "y": 24.7, "curve": [5.667, -2.62, 6, -2.62, 5.667, 24.7, 6, 24.7]}, {"time": 6.3333, "x": -2.62, "y": 24.7, "curve": [6.778, -2.62, 7.222, -2.17, 6.778, 24.71, 7.222, 12.45]}, {"time": 7.6667, "x": -2.17, "y": 12.45, "curve": [8.111, -2.17, 8.556, -2.62, 8.111, 12.44, 8.556, 24.7]}, {"time": 9, "x": -2.62, "y": 24.7}]}, "head_L2d": {"rotate": [{"value": 353.33, "curve": "stepped"}, {"time": 5.3333, "value": 353.33, "curve": [5.667, 353.33, 6, 353.26]}, {"time": 6.3333, "value": 353.33, "curve": [6.778, 353.42, 7.222, 1.56]}, {"time": 7.6667, "value": 1.47, "curve": [8.111, 1.38, 8.556, 353.33]}, {"time": 9, "value": 353.33}], "translate": [{"x": -4.16, "y": 36.39, "curve": "stepped"}, {"time": 5.3333, "x": -4.16, "y": 36.39, "curve": [5.667, -4.16, 6, -4.16, 5.667, 36.39, 6, 36.39]}, {"time": 6.3333, "x": -4.16, "y": 36.39, "curve": [6.778, -4.16, 7.222, -3.52, 6.778, 36.4, 7.222, 13.71]}, {"time": 7.6667, "x": -3.52, "y": 13.71, "curve": [8.111, -3.52, 8.556, -4.16, 8.111, 13.7, 8.556, 36.39]}, {"time": 9, "x": -4.16, "y": 36.39}]}, "head_L2e": {"rotate": [{"value": 354.86, "curve": "stepped"}, {"time": 5.3333, "value": 354.86, "curve": [5.667, 354.86, 6, 354.79]}, {"time": 6.3333, "value": 354.86, "curve": [6.778, 354.95, 7.222, 4.26]}, {"time": 7.6667, "value": 4.18, "curve": [8.111, 4.09, 8.556, 354.86]}, {"time": 9, "value": 354.86}], "translate": [{"x": -5.97, "y": 46.66, "curve": "stepped"}, {"time": 5.3333, "x": -5.97, "y": 46.66, "curve": [5.667, -5.97, 6, -5.97, 5.667, 46.66, 6, 46.66]}, {"time": 6.3333, "x": -5.97, "y": 46.66, "curve": [6.778, -5.97, 7.222, -4.87, 6.778, 46.67, 7.222, 11.43]}, {"time": 7.6667, "x": -4.87, "y": 11.42, "curve": [8.111, -4.87, 8.556, -5.97, 8.111, 11.41, 8.556, 46.66]}, {"time": 9, "x": -5.97, "y": 46.66}]}, "head_L2f": {"rotate": [{"value": 357.29, "curve": "stepped"}, {"time": 5.3333, "value": 357.29, "curve": [5.667, 357.29, 6, 357.22]}, {"time": 6.3333, "value": 357.29, "curve": [6.778, 357.38, 7.222, 7.74]}, {"time": 7.6667, "value": 7.65, "curve": [8.111, 7.56, 8.556, 357.29]}, {"time": 9, "value": 357.29}], "translate": [{"x": -7.99, "y": 54.58, "curve": "stepped"}, {"time": 5.3333, "x": -7.99, "y": 54.58, "curve": [5.667, -7.99, 6, -7.99, 5.667, 54.58, 6, 54.57]}, {"time": 6.3333, "x": -7.99, "y": 54.58, "curve": [6.778, -7.99, 7.222, -5.96, 6.778, 54.59, 7.222, 5]}, {"time": 7.6667, "x": -5.96, "y": 4.99, "curve": [8.111, -5.96, 8.556, -7.99, 8.111, 4.97, 8.556, 54.58]}, {"time": 9, "x": -7.99, "y": 54.58}]}, "head_L2g": {"rotate": [{"value": 1.34, "curve": "stepped"}, {"time": 5.3333, "value": 1.34, "curve": [5.667, 1.34, 6, 1.35]}, {"time": 6.3333, "value": 1.34, "curve": [6.778, 1.34, 7.222, 12.67]}, {"time": 7.6667, "value": 12.67, "curve": [8.111, 12.67, 8.556, 1.34]}, {"time": 9, "value": 1.34}], "translate": [{"x": -10.11, "y": 58.73, "curve": "stepped"}, {"time": 5.3333, "x": -10.11, "y": 58.73, "curve": [5.667, -10.11, 6, -10.11, 5.667, 58.73, 6, 58.72]}, {"time": 6.3333, "x": -10.11, "y": 58.73, "curve": [6.778, -10.11, 7.222, -6.28, 6.778, 58.74, 7.222, -6.71]}, {"time": 7.6667, "x": -6.28, "y": -6.72, "curve": [8.111, -6.28, 8.556, -10.11, 8.111, -6.74, 8.556, 58.73]}, {"time": 9, "x": -10.11, "y": 58.73}]}, "head_L2h": {"rotate": [{"value": 9.23, "curve": "stepped"}, {"time": 5.3333, "value": 9.23, "curve": [5.667, 9.23, 6, 9.23]}, {"time": 6.3333, "value": 9.23, "curve": [6.778, 9.23, 7.222, 21.44]}, {"time": 7.6667, "value": 21.44, "curve": [8.111, 21.45, 8.556, 9.23]}, {"time": 9, "value": 9.23}], "translate": [{"x": -11.75, "y": 56.62, "curve": "stepped"}, {"time": 5.3333, "x": -11.75, "y": 56.62, "curve": [5.667, -11.75, 6, -11.75, 5.667, 56.62, 6, 56.61]}, {"time": 6.3333, "x": -11.75, "y": 56.62, "curve": [6.778, -11.76, 7.222, -4.66, 6.778, 56.64, 7.222, -25.86]}, {"time": 7.6667, "x": -4.66, "y": -25.88, "curve": [8.111, -4.66, 8.556, -11.75, 8.111, -25.9, 8.556, 56.62]}, {"time": 9, "x": -11.75, "y": 56.62}]}, "head_L2i": {"rotate": [{"value": 29.59, "curve": "stepped"}, {"time": 5.3333, "value": 29.59, "curve": [5.667, 29.59, 6, 29.59]}, {"time": 6.3333, "value": 29.59, "curve": [6.778, 29.58, 7.222, 41.87]}, {"time": 7.6667, "value": 41.88, "curve": [8.111, 41.88, 8.556, 29.59]}, {"time": 9, "value": 29.59}], "translate": [{"x": -12.77, "y": 42.32, "curve": "stepped"}, {"time": 5.3333, "x": -12.77, "y": 42.32, "curve": [5.667, -12.77, 6, -12.77, 5.667, 42.32, 6, 42.3]}, {"time": 6.3333, "x": -12.77, "y": 42.32, "curve": [6.778, -12.78, 7.222, 0.23, 6.778, 42.34, 7.222, -58.09]}, {"time": 7.6667, "x": 0.24, "y": -58.11, "curve": [8.111, 0.24, 8.556, -12.77, 8.111, -58.14, 8.556, 42.32]}, {"time": 9, "x": -12.77, "y": 42.32}]}, "sh_L2": {"rotate": [{"value": -14.28, "curve": [0.279, -27.43, 0.556, -44.92]}, {"time": 0.8333, "value": -44.92, "curve": [1.278, -44.92, 1.722, -0.01]}, {"time": 2.1667, "curve": [2.611, 0.01, 3.056, -44.9]}, {"time": 3.5, "value": -44.92, "curve": [3.944, -44.93, 4.389, -0.01]}, {"time": 4.8333, "curve": [5.278, 0.01, 5.722, -44.9]}, {"time": 6.1667, "value": -44.92, "curve": [6.5, -44.93, 6.833, -0.01]}, {"time": 7.1667, "curve": [7.611, 0.01, 8.056, -44.9]}, {"time": 8.5, "value": -44.92, "curve": [8.944, -44.93, 9.389, 0]}, {"time": 9.8333, "curve": [10.278, 0, 10.722, -44.9]}, {"time": 11.1667, "value": -44.92, "curve": [11.611, -44.93, 12.056, -0.01]}, {"time": 12.5, "curve": [12.667, 0, 12.835, -6.33]}, {"time": 13, "value": -14.28}], "translate": [{"x": 29.46, "y": -44.89, "curve": [0.279, 6.27, 0.556, -24.59, 0.279, -5.86, 0.556, 46.05]}, {"time": 0.8333, "x": -24.59, "y": 46.05, "curve": [1.278, -24.59, 1.722, 54.63, 1.278, 46.05, 1.722, -87.23]}, {"time": 2.1667, "x": 54.65, "y": -87.27, "curve": [2.611, 54.67, 3.056, -24.57, 2.611, -87.3, 3.056, 46.01]}, {"time": 3.5, "x": -24.59, "y": 46.05, "curve": [3.944, -24.61, 4.389, 54.63, 3.944, 46.08, 4.389, -87.23]}, {"time": 4.8333, "x": 54.65, "y": -87.27, "curve": [5.278, 54.67, 5.722, -24.56, 5.278, -87.3, 5.722, 46]}, {"time": 6.1667, "x": -24.59, "y": 46.05, "curve": [6.5, -24.61, 6.833, 54.63, 6.5, 46.08, 6.833, -87.24]}, {"time": 7.1667, "x": 54.65, "y": -87.27, "curve": [7.611, 54.67, 8.056, -24.57, 7.611, -87.3, 8.056, 46.01]}, {"time": 8.5, "x": -24.59, "y": 46.05, "curve": [8.944, -24.61, 9.389, 54.63, 8.944, 46.08, 9.389, -87.23]}, {"time": 9.8333, "x": 54.65, "y": -87.27, "curve": [10.278, 54.67, 10.722, -24.57, 10.278, -87.3, 10.722, 46.01]}, {"time": 11.1667, "x": -24.59, "y": 46.05, "curve": [11.611, -24.61, 12.056, 54.63, 11.611, 46.08, 12.056, -87.23]}, {"time": 12.5, "x": 54.65, "y": -87.27, "curve": [12.667, 54.65, 12.835, 43.47, 12.667, -87.28, 12.835, -68.47]}, {"time": 13, "x": 29.46, "y": -44.89}]}, "eye_L": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.35, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -1.35, "curve": "stepped"}, {"time": 3, "x": -1.35, "curve": [3.033, -1.35, 3.067, -1.12, 3.033, 0, 3.067, -1.5]}, {"time": 3.1, "x": -1.12, "y": -1.5, "curve": "stepped"}, {"time": 3.5, "x": -1.12, "y": -1.5, "curve": [3.533, -1.12, 3.567, 0, 3.533, -1.5, 3.567, 0]}, {"time": 3.6, "curve": [4.456, 0, 5.313, -0.01, 4.456, 0, 5.313, 0.03]}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.22, 6.2, 0, 6.233, 3.96]}, {"time": 6.2667, "x": -1.22, "y": 3.96, "curve": "stepped"}, {"time": 7.1667, "x": -1.22, "y": 3.96, "curve": [7.2, -1.22, 7.233, -0.13, 7.2, 3.96, 7.233, 4.62]}, {"time": 7.2667, "x": -0.13, "y": 4.62, "curve": "stepped"}, {"time": 8.1667, "x": -0.13, "y": 4.62, "curve": [8.2, -0.13, 8.233, -1.72, 8.2, 4.62, 8.233, 5.72]}, {"time": 8.2667, "x": -1.72, "y": 5.72, "curve": "stepped"}, {"time": 8.6667, "x": -1.72, "y": 5.72, "curve": [8.7, -1.72, 8.733, -1.22, 8.7, 5.72, 8.733, 3.96]}, {"time": 8.7667, "x": -1.22, "y": 3.96, "curve": "stepped"}, {"time": 9.8333, "x": -1.22, "y": 3.96, "curve": [9.867, -1.22, 9.9, 0, 9.867, 3.96, 9.9, 0]}, {"time": 9.9333}]}, "eye_R": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.35, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -1.35, "curve": "stepped"}, {"time": 3, "x": -1.35, "curve": [3.033, -1.35, 3.067, -1.12, 3.033, 0, 3.067, -1.5]}, {"time": 3.1, "x": -1.12, "y": -1.5, "curve": "stepped"}, {"time": 3.5, "x": -1.12, "y": -1.5, "curve": [3.533, -1.12, 3.567, 0, 3.533, -1.5, 3.567, 0]}, {"time": 3.6, "curve": [4.456, 0, 5.313, -0.01, 4.456, 0, 5.313, 0.03]}, {"time": 6.1667, "curve": [6.2, 0, 6.233, -1.22, 6.2, 0, 6.233, 3.96]}, {"time": 6.2667, "x": -1.22, "y": 3.96, "curve": "stepped"}, {"time": 7.1667, "x": -1.22, "y": 3.96, "curve": [7.2, -1.22, 7.233, -0.13, 7.2, 3.96, 7.233, 4.62]}, {"time": 7.2667, "x": -0.13, "y": 4.62, "curve": "stepped"}, {"time": 8.1667, "x": -0.13, "y": 4.62, "curve": [8.2, -0.13, 8.233, -1.72, 8.2, 4.62, 8.233, 5.72]}, {"time": 8.2667, "x": -1.72, "y": 5.72, "curve": "stepped"}, {"time": 8.6667, "x": -1.72, "y": 5.72, "curve": [8.7, -1.72, 8.733, -1.22, 8.7, 5.72, 8.733, 3.96]}, {"time": 8.7667, "x": -1.22, "y": 3.96, "curve": "stepped"}, {"time": 9.8333, "x": -1.22, "y": 3.96, "curve": [9.867, -1.22, 9.9, 0, 9.867, 3.96, 9.9, 0]}, {"time": 9.9333}]}, "leg_R6": {"translate": [{"y": 4.92, "curve": [0.057, 0, 0.112, 0, 0.057, 2.13, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 0, 0.611, 0, 1.056, 104.72]}, {"time": 1.5, "y": 104.72, "curve": [1.944, 0, 2.389, 0, 1.944, 104.72, 2.389, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 0, 3.278, 0, 3.722, 104.72]}, {"time": 4.1667, "y": 104.72, "curve": [4.611, 0, 5.056, 0, 4.611, 104.72, 5.056, 0]}, {"time": 5.5, "curve": [5.833, 0, 6.167, 0, 5.833, 0, 6.167, 174.2]}, {"time": 6.5, "y": 174.2, "curve": [6.944, 0, 7.389, 0, 6.944, 174.2, 7.389, 0]}, {"time": 7.8333, "curve": [8.278, 0, 8.722, 0, 8.278, 0, 8.722, 174.2]}, {"time": 9.1667, "y": 174.2, "curve": [9.611, 0, 10.056, 0, 9.611, 174.2, 10.056, 0]}, {"time": 10.5, "curve": [10.944, 0, 11.389, 0, 10.944, 0, 11.389, 104.72]}, {"time": 11.8333, "y": 104.72, "curve": [12.223, 0, 12.613, 0, 12.223, 104.72, 12.613, 24.77]}, {"time": 13, "y": 4.92}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-12.14246, -1.621, -12.1366, -1.62189, -12.59595, -1.68018, -12.59009, -1.68097, -14.59363, 0.00119, -14.59167, 0.00076, -10.68323, 0.702, -10.68188, 0.70157, -5.83337, 1.21732, -5.83167, 1.21701, -1.71472, 1.07828, -1.71326, 1.07776, -2.04834, -0.12134, -2.04761, -0.12137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4469, 0.01324, -2.44617, 0.01306, -7.75671, -0.55463, -7.75305, -0.55539, 0, 0, 0, 0, -4.1969, -0.32693, -4.19507, -0.32715, -8.65381, -0.5665, -8.65015, -0.56766, -12.15649, -1.59656, -12.15051, -1.59793, -12.86328, -1.52814, -12.85718, -1.52914, -10.64856, -1.38559, -10.64331, -1.38647, -7.34998, -0.40839, -7.34485, -0.40939, -3.33521, -0.4772, -3.33081, -0.47769, -1.30054, -0.20096, -1.29761, -0.20132], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-12.14246, -1.621, -12.1366, -1.62189, -12.59595, -1.68018, -12.59009, -1.68097, -14.59363, 0.00119, -14.59167, 0.00076, -10.68323, 0.702, -10.68188, 0.70157, -5.83337, 1.21732, -5.83167, 1.21701, -1.71472, 1.07828, -1.71326, 1.07776, -2.04834, -0.12134, -2.04761, -0.12137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4469, 0.01324, -2.44617, 0.01306, -7.75671, -0.55463, -7.75305, -0.55539, 0, 0, 0, 0, -4.1969, -0.32693, -4.19507, -0.32715, -8.65381, -0.5665, -8.65015, -0.56766, -12.15649, -1.59656, -12.15051, -1.59793, -12.86328, -1.52814, -12.85718, -1.52914, -10.64856, -1.38559, -10.64331, -1.38647, -7.34998, -0.40839, -7.34485, -0.40939, -3.33521, -0.4772, -3.33081, -0.47769, -1.30054, -0.20096, -1.29761, -0.20132], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-12.14246, -1.621, -12.1366, -1.62189, -12.59595, -1.68018, -12.59009, -1.68097, -14.59363, 0.00119, -14.59167, 0.00076, -10.68323, 0.702, -10.68188, 0.70157, -5.83337, 1.21732, -5.83167, 1.21701, -1.71472, 1.07828, -1.71326, 1.07776, -2.04834, -0.12134, -2.04761, -0.12137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4469, 0.01324, -2.44617, 0.01306, -7.75671, -0.55463, -7.75305, -0.55539, 0, 0, 0, 0, -4.1969, -0.32693, -4.19507, -0.32715, -8.65381, -0.5665, -8.65015, -0.56766, -12.15649, -1.59656, -12.15051, -1.59793, -12.86328, -1.52814, -12.85718, -1.52914, -10.64856, -1.38559, -10.64331, -1.38647, -7.34998, -0.40839, -7.34485, -0.40939, -3.33521, -0.4772, -3.33081, -0.47769, -1.30054, -0.20096, -1.29761, -0.20132], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "vertices": [-12.21558, 0.33466, -12.21338, 0.3338, -9.79431, 0.73685, -9.79248, 0.73633, -5.44397, 0.56094, -5.44299, 0.56012, -1.62183, 0.13782, -1.62122, 0.13754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.09375, 0.03476, -1.09375, 0.03436, -1.7262, 0.19678, -1.7262, 0.19617, -1.23254, -0.1752, -1.2323, -0.17554, -3.53052, 0.0477, -3.52966, 0.04755, -6.66016, -0.25128, -6.65918, -0.25171, -10.93677, 0.23111, -10.93359, 0.23044, -15.04053, 0.42532, -15.03772, 0.42493, -15.43738, 1.26004, -15.43445, 1.25958, 0, 0, 0, 0, -2.87842, 0.01559, -2.8783, 0.01523, -7.42908, 0.01663, -7.42822, 0.0163, -11.11353, 0.12839, -11.11072, 0.12802, -13.427, -0.3819, -13.42517, -0.38229, 0, 0, 0, 0, -0.15259, -0.01093, -0.15247, -0.01096, 0.56335, 0.05792, 0.56421, 0.05792, 0, 0, 0, 0, 0, 0, 0, 0, -1.59998, 0.13678, -1.59924, 0.13651, -3.90393, 0.36273, -3.90295, 0.36227, -8.25879, 0.53348, -8.25793, 0.53326, -11.99792, 0.28244, -11.9967, 0.28214], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "vertices": [-12.21558, 0.33466, -12.21338, 0.3338, -9.79431, 0.73685, -9.79248, 0.73633, -5.44397, 0.56094, -5.44299, 0.56012, -1.62183, 0.13782, -1.62122, 0.13754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.09375, 0.03476, -1.09375, 0.03436, -1.7262, 0.19678, -1.7262, 0.19617, -1.23254, -0.1752, -1.2323, -0.17554, -3.53052, 0.0477, -3.52966, 0.04755, -6.66016, -0.25128, -6.65918, -0.25171, -10.93677, 0.23111, -10.93359, 0.23044, -15.04053, 0.42532, -15.03772, 0.42493, -15.43738, 1.26004, -15.43445, 1.25958, 0, 0, 0, 0, -2.87842, 0.01559, -2.8783, 0.01523, -7.42908, 0.01663, -7.42822, 0.0163, -11.11353, 0.12839, -11.11072, 0.12802, -13.427, -0.3819, -13.42517, -0.38229, 0, 0, 0, 0, -0.15259, -0.01093, -0.15247, -0.01096, 0.56335, 0.05792, 0.56421, 0.05792, 0, 0, 0, 0, 0, 0, 0, 0, -1.59998, 0.13678, -1.59924, 0.13651, -3.90393, 0.36273, -3.90295, 0.36227, -8.25879, 0.53348, -8.25793, 0.53326, -11.99792, 0.28244, -11.9967, 0.28214], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "vertices": [-12.21558, 0.33466, -12.21338, 0.3338, -9.79431, 0.73685, -9.79248, 0.73633, -5.44397, 0.56094, -5.44299, 0.56012, -1.62183, 0.13782, -1.62122, 0.13754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.09375, 0.03476, -1.09375, 0.03436, -1.7262, 0.19678, -1.7262, 0.19617, -1.23254, -0.1752, -1.2323, -0.17554, -3.53052, 0.0477, -3.52966, 0.04755, -6.66016, -0.25128, -6.65918, -0.25171, -10.93677, 0.23111, -10.93359, 0.23044, -15.04053, 0.42532, -15.03772, 0.42493, -15.43738, 1.26004, -15.43445, 1.25958, 0, 0, 0, 0, -2.87842, 0.01559, -2.8783, 0.01523, -7.42908, 0.01663, -7.42822, 0.0163, -11.11353, 0.12839, -11.11072, 0.12802, -13.427, -0.3819, -13.42517, -0.38229, 0, 0, 0, 0, -0.15259, -0.01093, -0.15247, -0.01096, 0.56335, 0.05792, 0.56421, 0.05792, 0, 0, 0, 0, 0, 0, 0, 0, -1.59998, 0.13678, -1.59924, 0.13651, -3.90393, 0.36273, -3.90295, 0.36227, -8.25879, 0.53348, -8.25793, 0.53326, -11.99792, 0.28244, -11.9967, 0.28214], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head": {"head": {"deform": [{"time": 1.3333, "curve": [1.389, 0, 1.444, 1]}, {"time": 1.5, "offset": 96, "vertices": [-1.77698, 0.26971, -1.77673, 0.26965, -7.15674, 0.16867, -7.15454, 0.16797, -11.36487, 0.32132, -11.36218, 0.32071, -11.88672, 0.08542, -11.88416, 0.08511, -9.6261, 0.98431, -9.62366, 0.98395, -6.11304, 0.64023, -6.11157, 0.63983, -2.49463, -0.00824, -2.49365, -0.00851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60327, -0.07275, -2.60229, -0.073, -7.89709, -0.19632, -7.8949, -0.19672, -12.0199, -0.4343, -12.01453, -0.43518, -12.79773, 0.13385, -12.79492, 0.13327, -10.3053, -0.22653, -10.30286, -0.2269, -6.57532, -0.44199, -6.57324, -0.44238, -2.56116, -0.26825, -2.56006, -0.26855, -0.80408, -0.25595, -0.80371, -0.25607], "curve": [1.556, 0, 1.611, 1]}, {"time": 1.6667, "curve": "stepped"}, {"time": 6, "curve": [6.056, 0, 6.111, 1]}, {"time": 6.1667, "offset": 96, "vertices": [-1.77698, 0.26971, -1.77673, 0.26965, -7.15674, 0.16867, -7.15454, 0.16797, -11.36487, 0.32132, -11.36218, 0.32071, -11.88672, 0.08542, -11.88416, 0.08511, -9.6261, 0.98431, -9.62366, 0.98395, -6.11304, 0.64023, -6.11157, 0.63983, -2.49463, -0.00824, -2.49365, -0.00851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60327, -0.07275, -2.60229, -0.073, -7.89709, -0.19632, -7.8949, -0.19672, -12.0199, -0.4343, -12.01453, -0.43518, -12.79773, 0.13385, -12.79492, 0.13327, -10.3053, -0.22653, -10.30286, -0.2269, -6.57532, -0.44199, -6.57324, -0.44238, -2.56116, -0.26825, -2.56006, -0.26855, -0.80408, -0.25595, -0.80371, -0.25607], "curve": [6.222, 0, 6.278, 1]}, {"time": 6.3333, "curve": "stepped"}, {"time": 9.6667, "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333, "offset": 96, "vertices": [-1.77698, 0.26971, -1.77673, 0.26965, -7.15674, 0.16867, -7.15454, 0.16797, -11.36487, 0.32132, -11.36218, 0.32071, -11.88672, 0.08542, -11.88416, 0.08511, -9.6261, 0.98431, -9.62366, 0.98395, -6.11304, 0.64023, -6.11157, 0.63983, -2.49463, -0.00824, -2.49365, -0.00851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.60327, -0.07275, -2.60229, -0.073, -7.89709, -0.19632, -7.8949, -0.19672, -12.0199, -0.4343, -12.01453, -0.43518, -12.79773, 0.13385, -12.79492, 0.13327, -10.3053, -0.22653, -10.30286, -0.2269, -6.57532, -0.44199, -6.57324, -0.44238, -2.56116, -0.26825, -2.56006, -0.26855, -0.80408, -0.25595, -0.80371, -0.25607], "curve": [9.889, 0, 9.944, 1]}, {"time": 10}]}}, "head2": {"head": {"deform": [{"time": 6, "curve": [6.111, 0, 6.222, 1]}, {"time": 6.3333, "offset": 48, "vertices": [0.33309, -0.02285, 0.33367, -0.02281, -0.01943, 0.00133, -0.01842, 0.00135, -0.83686, 0.05742, -0.83478, 0.05744, -1.49549, 0.10263, -1.49232, 0.10261, -1.39331, 0.0956, -1.38993, 0.0956, -1.49549, 0.10263, -1.49232, 0.10261, -1.24565, 0.08552, -1.24436, 0.0855, -0.34547, 0.02374, -0.34489, 0.02376, 0.20206, -0.01384, 0.20328, -0.0138, 1.215, -0.08339, 1.2155, -0.08336, 1.00308, -0.06886, 1.00409, -0.06885, 0.82053, -0.05637, 0.82125, -0.05636, 0.65675, -0.04512, 0.65769, -0.04512, 0.65675, -0.04512, 0.65769, -0.04512, 0.8814, -0.06053, 0.88212, -0.0605, 1.06403, -0.07305, 1.06504, -0.07302, 0.92854, -0.06373, 0.92918, -0.06366, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.24343, -0.0167, 0.24343, -0.01668, -0.77908, 0.05347, -0.77735, 0.05345, -1.70805, 0.11726, -1.70611, 0.11724, -1.66092, 0.11403, -1.65912, 0.114, -1.66128, 0.11405, -1.65883, 0.11402, -1.14247, 0.07843, -1.13995, 0.0784, -0.15572, 0.01069, -0.1555, 0.01069, 0.42606, -0.02924, 0.42606, -0.02921, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.50406, -0.03461, 0.50485, -0.03461, 0.84039, -0.0577, 0.8419, -0.05771, 0.64502, -0.04429, 0.64596, -0.04429, 0.49917, -0.03427, 0.5001, -0.03427, 0.45513, -0.03127, 0.45614, -0.03127, 0.45513, -0.03127, 0.45614, -0.03127, 0.45513, -0.03127, 0.45614, -0.03127, 0.4724, -0.03245, 0.47312, -0.03242, 0.4724, -0.03245, 0.47312, -0.03242], "curve": "stepped"}, {"time": 9.5, "offset": 48, "vertices": [0.33309, -0.02285, 0.33367, -0.02281, -0.01943, 0.00133, -0.01842, 0.00135, -0.83686, 0.05742, -0.83478, 0.05744, -1.49549, 0.10263, -1.49232, 0.10261, -1.39331, 0.0956, -1.38993, 0.0956, -1.49549, 0.10263, -1.49232, 0.10261, -1.24565, 0.08552, -1.24436, 0.0855, -0.34547, 0.02374, -0.34489, 0.02376, 0.20206, -0.01384, 0.20328, -0.0138, 1.215, -0.08339, 1.2155, -0.08336, 1.00308, -0.06886, 1.00409, -0.06885, 0.82053, -0.05637, 0.82125, -0.05636, 0.65675, -0.04512, 0.65769, -0.04512, 0.65675, -0.04512, 0.65769, -0.04512, 0.8814, -0.06053, 0.88212, -0.0605, 1.06403, -0.07305, 1.06504, -0.07302, 0.92854, -0.06373, 0.92918, -0.06366, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.24343, -0.0167, 0.24343, -0.01668, -0.77908, 0.05347, -0.77735, 0.05345, -1.70805, 0.11726, -1.70611, 0.11724, -1.66092, 0.11403, -1.65912, 0.114, -1.66128, 0.11405, -1.65883, 0.11402, -1.14247, 0.07843, -1.13995, 0.0784, -0.15572, 0.01069, -0.1555, 0.01069, 0.42606, -0.02924, 0.42606, -0.02921, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.50406, -0.03461, 0.50485, -0.03461, 0.84039, -0.0577, 0.8419, -0.05771, 0.64502, -0.04429, 0.64596, -0.04429, 0.49917, -0.03427, 0.5001, -0.03427, 0.45513, -0.03127, 0.45614, -0.03127, 0.45513, -0.03127, 0.45614, -0.03127, 0.45513, -0.03127, 0.45614, -0.03127, 0.4724, -0.03245, 0.47312, -0.03242, 0.4724, -0.03245, 0.47312, -0.03242], "curve": [9.667, 0, 9.833, 1]}, {"time": 10}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": -9.86, "curve": [0.024, -9.86, 0.06, 14.41, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 14.41, "curve": [0.544, 14.41, 0.856, -9.86, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": -9.86}]}, "ALL2": {"translate": [{"y": -13.22, "curve": [0.078, 0, 0.156, 0, 0.024, -13.22, 0.06, 24.69]}, {"time": 0.2333, "y": 24.69, "curve": [0.678, 0, 0.856, 0, 0.544, 24.69, 0.856, -13.22]}, {"time": 1.1667, "y": -13.22}]}, "body": {"rotate": [{"value": -2.32, "curve": [0.028, -2.32, 0.069, 7.1]}, {"time": 0.2667, "value": 7.1, "curve": [0.567, 7.1, 0.867, -2.32]}, {"time": 1.1667, "value": -2.32}], "translate": [{"y": -10.97, "curve": [0.089, 0, 0.178, 0, 0.028, -10.97, 0.069, 18.74]}, {"time": 0.2667, "y": 18.74, "curve": [0.695, 0, 0.867, 0, 0.567, 18.74, 0.867, -10.97]}, {"time": 1.1667, "y": -10.97}], "scale": [{"y": 1.041, "curve": [0.089, 1, 0.178, 1, 0.028, 1.041, 0.069, 0.975]}, {"time": 0.2667, "y": 0.975, "curve": [0.695, 1, 0.867, 1, 0.567, 0.975, 0.867, 1.041]}, {"time": 1.1667, "y": 1.041}]}, "body2": {"rotate": [{"value": 1.33, "curve": [0.028, 1.33, 0.069, -3.56]}, {"time": 0.2667, "value": -3.56, "curve": [0.567, -3.56, 0.867, 1.33]}, {"time": 1.1667, "value": 1.33}], "translate": [{"x": -3.13, "curve": [0.028, -3.13, 0.069, 9.6, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 9.6, "curve": [0.567, 9.6, 0.867, -3.13, 0.695, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.13}], "scale": [{"x": 1.003, "y": 1.003, "curve": [0.028, 1.003, 0.069, 1.041, 0.028, 1.003, 0.069, 1.041]}, {"time": 0.2667, "x": 1.041, "y": 1.041, "curve": [0.567, 1.041, 0.867, 1.003, 0.567, 1.041, 0.867, 1.003]}, {"time": 1.1667, "x": 1.003, "y": 1.003}]}, "neck": {"rotate": [{"value": -0.42, "curve": [0.031, -0.42, 0.078, 4.15]}, {"time": 0.3, "value": 4.15, "curve": [0.589, 4.15, 0.878, -0.42]}, {"time": 1.1667, "value": -0.42}]}, "head": {"rotate": [{"value": 0.18, "curve": [0.031, 0.18, 0.078, 1.84]}, {"time": 0.3, "value": 1.84, "curve": [0.589, 1.84, 0.878, 0.18]}, {"time": 1.1667, "value": 0.18}]}, "sh_L": {"translate": [{"x": -5.23, "curve": [0.024, -5.23, 0.06, 9.57, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 9.57, "curve": [0.544, 9.57, 0.856, -5.23, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": -5.23}]}, "sh_R": {"translate": [{"x": -1.31, "curve": [0.024, -1.31, 0.06, 1.08, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": 1.08, "curve": [0.544, 1.08, 0.856, -1.31, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": -1.31}]}, "arm_R": {"rotate": [{"value": 0.46, "curve": [0.074, 1.07, 0.161, 1.58]}, {"time": 0.2333, "value": 1.58, "curve": [0.428, 1.58, 0.605, -1.93]}, {"time": 0.8, "value": -1.93, "curve": [0.922, -1.93, 1.046, -0.57]}, {"time": 1.1667, "value": 0.46}]}, "arm_R2": {"rotate": [{"value": -0.18, "curve": [0.099, 0.69, 0.203, 1.58]}, {"time": 0.3, "value": 1.58, "curve": [0.495, 1.58, 0.672, -1.93]}, {"time": 0.8667, "value": -1.93, "curve": [0.965, -1.93, 1.071, -1.06]}, {"time": 1.1667, "value": -0.18}]}, "arm_R3": {"rotate": [{"value": -3.92, "curve": [0.122, 0.36, 0.245, 6.04]}, {"time": 0.3667, "value": 6.04, "curve": [0.561, 6.04, 0.739, -8.55]}, {"time": 0.9333, "value": -8.55, "curve": [1.007, -8.55, 1.094, -6.5]}, {"time": 1.1667, "value": -3.92}]}, "line_R": {"rotate": [{"value": -2.8, "curve": [0.147, 0.32, 0.288, 6.48]}, {"time": 0.4333, "value": 6.48, "curve": [0.628, 6.48, 0.839, -4.56]}, {"time": 1.0333, "value": -4.56, "curve": [1.083, -4.56, 1.119, -3.86]}, {"time": 1.1667, "value": -2.8}]}, "arm_L": {"rotate": [{"value": -0.38, "curve": [0.074, -1, 0.161, -1.5]}, {"time": 0.2333, "value": -1.5, "curve": [0.428, -1.5, 0.605, 2.02]}, {"time": 0.8, "value": 2.02, "curve": [0.922, 2.02, 1.046, 0.65]}, {"time": 1.1667, "value": -0.38}]}, "arm_L2": {"rotate": [{"value": 0.26, "curve": [0.099, -0.62, 0.203, -1.5]}, {"time": 0.3, "value": -1.5, "curve": [0.495, -1.5, 0.672, 2.02]}, {"time": 0.8667, "value": 2.02, "curve": [0.965, 2.02, 1.071, 1.15]}, {"time": 1.1667, "value": 0.26}]}, "arm_L3": {"rotate": [{"value": 1.79, "curve": [0.122, -2.57, 0.245, -8.36]}, {"time": 0.3667, "value": -8.36, "curve": [0.561, -8.36, 0.739, 6.51]}, {"time": 0.9333, "value": 6.52, "curve": [1.007, 6.52, 1.094, 4.42]}, {"time": 1.1667, "value": 1.79}]}, "hair_L": {"rotate": [{"value": 1.5, "curve": [0.122, -1.07, 0.245, -4.49]}, {"time": 0.3667, "value": -4.49, "curve": [0.561, -4.49, 0.739, 4.29]}, {"time": 0.9333, "value": 4.29, "curve": [1.007, 4.29, 1.094, 3.05]}, {"time": 1.1667, "value": 1.5}]}, "hair_R": {"rotate": [{"value": -1.7, "curve": [0.122, 0.87, 0.245, 4.29]}, {"time": 0.3667, "value": 4.29, "curve": [0.561, 4.29, 0.739, -4.49]}, {"time": 0.9333, "value": -4.49, "curve": [1.007, -4.49, 1.094, -3.25]}, {"time": 1.1667, "value": -1.7}]}, "eyebrow_L": {"translate": [{"curve": [0.024, 0, 0.06, -3.76, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -3.76, "curve": [0.544, -3.76, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_L2": {"rotate": [{"curve": [0.024, 0, 0.06, -12.65]}, {"time": 0.2333, "value": -12.65, "curve": [0.544, -12.65, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.06, 0.936, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.936, "curve": [0.544, 0.936, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_L3": {"rotate": [{"curve": [0.024, 0, 0.06, -12.87]}, {"time": 0.2333, "value": -12.87, "curve": [0.544, -12.87, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R": {"translate": [{"curve": [0.024, 0, 0.06, -3.76, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -3.76, "curve": [0.544, -3.76, 0.856, 0, 0.544, 0, 0.856, 0]}, {"time": 1.1667}]}, "eyebrow_R2": {"rotate": [{"curve": [0.024, 0, 0.06, 8.64]}, {"time": 0.2333, "value": 8.64, "curve": [0.544, 8.64, 0.856, 0]}, {"time": 1.1667}], "scale": [{"curve": [0.024, 1, 0.06, 0.936, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.936, "curve": [0.544, 0.936, 0.856, 1, 0.544, 1, 0.856, 1]}, {"time": 1.1667}]}, "eyebrow_R3": {"rotate": [{"curve": [0.024, 0, 0.06, 11.87]}, {"time": 0.2333, "value": 11.87, "curve": [0.544, 11.87, 0.856, 0]}, {"time": 1.1667}]}, "tun": {"rotate": [{"value": -3.41, "curve": [0.024, -3.41, 0.06, 9.21]}, {"time": 0.2333, "value": 9.21, "curve": [0.544, 9.21, 0.856, -3.41]}, {"time": 1.1667, "value": -3.41}]}, "leg_R3": {"rotate": [{"value": -0.01}]}, "leg_L3": {"rotate": [{}]}, "hair_RR": {"rotate": [{"value": -3.01, "curve": [0.147, -0.24, 0.288, 5.23]}, {"time": 0.4333, "value": 5.24, "curve": [0.628, 5.24, 0.839, -4.57]}, {"time": 1.0333, "value": -4.58, "curve": [1.083, -4.58, 1.119, -3.95]}, {"time": 1.1667, "value": -3.01}]}, "hair_LL": {"rotate": [{"value": 2.43, "curve": [0.147, 0.37, 0.288, -3.71]}, {"time": 0.4333, "value": -3.71, "curve": [0.628, -3.71, 0.839, 3.6]}, {"time": 1.0333, "value": 3.6, "curve": [1.083, 3.6, 1.119, 3.13]}, {"time": 1.1667, "value": 2.43}]}, "line_L": {"rotate": [{"value": 4.71, "curve": [0.147, 1.59, 0.288, -4.56]}, {"time": 0.4333, "value": -4.56, "curve": [0.628, -4.56, 0.839, 6.48]}, {"time": 1.0333, "value": 6.48, "curve": [1.083, 6.48, 1.119, 5.77]}, {"time": 1.1667, "value": 4.71}]}, "RU_R": {"translate": [{"x": -10.37, "y": 3.95, "curve": [0.074, -23.96, 0.16, -35.1, 0.074, 7.64, 0.16, 10.66]}, {"time": 0.2333, "x": -35.1, "y": 10.66, "curve": [0.429, -35.1, 0.604, 42.61, 0.429, 10.66, 0.604, -10.43]}, {"time": 0.8, "x": 42.63, "y": -10.43, "curve": [0.923, 42.65, 1.045, 12.43, 0.923, -10.44, 1.045, -2.24]}, {"time": 1.1667, "x": -10.37, "y": 3.95}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.073, 1.007, 0.16, 0.921, 0.073, 0.995, 0.16, 1.053]}, {"time": 0.2333, "x": 0.921, "y": 1.053, "curve": [0.331, 0.921, 0.402, 1.075, 0.331, 1.053, 0.402, 0.949]}, {"time": 0.5, "x": 1.075, "y": 0.949, "curve": [0.598, 1.075, 0.702, 0.921, 0.598, 0.949, 0.702, 1.053]}, {"time": 0.8, "x": 0.921, "y": 1.053, "curve": [0.898, 0.921, 1.002, 1.075, 0.898, 1.053, 1.002, 0.949]}, {"time": 1.1, "x": 1.075, "y": 0.949, "curve": [1.125, 1.075, 1.143, 1.065, 1.125, 0.949, 1.143, 0.956]}, {"time": 1.1667, "x": 1.05, "y": 0.966}]}, "RU_R2": {"translate": [{"x": 4.51, "y": -0.07, "curve": [0.099, -15.16, 0.203, -35.1, 0.099, 5.26, 0.203, 10.66]}, {"time": 0.3, "x": -35.1, "y": 10.66, "curve": [0.496, -35.1, 0.671, 44.08, 0.496, 10.66, 0.671, -10.79]}, {"time": 0.8667, "x": 44.1, "y": -10.79, "curve": [0.965, 44.11, 1.07, 24.44, 0.965, -10.79, 1.07, -5.47]}, {"time": 1.1667, "x": 4.51, "y": -0.07}], "scale": [{"x": 1.075, "y": 0.949, "curve": [0.098, 1.075, 0.202, 0.921, 0.098, 0.949, 0.202, 1.053]}, {"time": 0.3, "x": 0.921, "y": 1.053, "curve": [0.398, 0.921, 0.502, 1.075, 0.398, 1.053, 0.502, 0.949]}, {"time": 0.6, "x": 1.075, "y": 0.949, "curve": [0.698, 1.075, 0.769, 0.921, 0.698, 0.949, 0.769, 1.053]}, {"time": 0.8667, "x": 0.921, "y": 1.053, "curve": [0.965, 0.921, 1.069, 1.075, 0.965, 1.053, 1.069, 0.949]}, {"time": 1.1667, "x": 1.075, "y": 0.949}]}, "RU_R3": {"translate": [{"x": 16.5, "y": -3.35, "curve": [0.123, -6.21, 0.245, -36.42, 0.123, 2.83, 0.245, 11.07]}, {"time": 0.3667, "x": -36.42, "y": 11.07, "curve": [0.562, -36.42, 0.771, 41.14, 0.562, 11.07, 0.771, -10.07]}, {"time": 0.9667, "x": 41.16, "y": -10.07, "curve": [1.04, 41.17, 1.094, 30.23, 1.04, -10.08, 1.094, -7.09]}, {"time": 1.1667, "x": 16.5, "y": -3.36}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.024, 1.065, 0.042, 1.075, 0.024, 0.956, 0.042, 0.949]}, {"time": 0.0667, "x": 1.075, "y": 0.949, "curve": [0.165, 1.075, 0.269, 0.921, 0.165, 0.949, 0.269, 1.053]}, {"time": 0.3667, "x": 0.921, "y": 1.053, "curve": [0.465, 0.921, 0.569, 1.075, 0.465, 1.053, 0.569, 0.949]}, {"time": 0.6667, "x": 1.075, "y": 0.949, "curve": [0.765, 1.075, 0.869, 0.921, 0.765, 0.949, 0.869, 1.053]}, {"time": 0.9667, "x": 0.921, "y": 1.053, "curve": [1.04, 0.921, 1.094, 1.007, 1.04, 1.053, 1.094, 0.995]}, {"time": 1.1667, "x": 1.05, "y": 0.966}]}, "RU_L": {"translate": [{"x": -10.38, "y": 3.95, "curve": [0.074, -23.96, 0.16, -35.1, 0.074, 7.64, 0.16, 10.66]}, {"time": 0.2333, "x": -35.1, "y": 10.66, "curve": [0.429, -35.1, 0.604, 42.61, 0.429, 10.66, 0.604, -10.43]}, {"time": 0.8, "x": 42.63, "y": -10.43, "curve": [0.923, 42.64, 1.045, 12.43, 0.923, -10.44, 1.045, -2.24]}, {"time": 1.1667, "x": -10.37, "y": 3.95}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.073, 1.007, 0.16, 0.921, 0.073, 0.995, 0.16, 1.053]}, {"time": 0.2333, "x": 0.921, "y": 1.053, "curve": [0.331, 0.921, 0.402, 1.075, 0.331, 1.053, 0.402, 0.949]}, {"time": 0.5, "x": 1.075, "y": 0.949, "curve": [0.598, 1.075, 0.702, 0.921, 0.598, 0.949, 0.702, 1.053]}, {"time": 0.8, "x": 0.921, "y": 1.053, "curve": [0.898, 0.921, 1.002, 1.075, 0.898, 1.053, 1.002, 0.949]}, {"time": 1.1, "x": 1.075, "y": 0.949, "curve": [1.125, 1.075, 1.143, 1.065, 1.125, 0.949, 1.143, 0.956]}, {"time": 1.1667, "x": 1.05, "y": 0.966}]}, "RU_L2": {"translate": [{"x": 4.51, "y": -0.07, "curve": [0.099, -15.16, 0.203, -35.1, 0.099, 5.26, 0.203, 10.66]}, {"time": 0.3, "x": -35.1, "y": 10.66, "curve": [0.496, -35.1, 0.671, 44.08, 0.496, 10.66, 0.671, -10.79]}, {"time": 0.8667, "x": 44.1, "y": -10.79, "curve": [0.965, 44.11, 1.07, 24.45, 0.965, -10.8, 1.07, -5.47]}, {"time": 1.1667, "x": 4.52, "y": -0.07}], "scale": [{"x": 1.075, "y": 0.949, "curve": [0.098, 1.075, 0.202, 0.921, 0.098, 0.949, 0.202, 1.053]}, {"time": 0.3, "x": 0.921, "y": 1.053, "curve": [0.398, 0.921, 0.502, 1.075, 0.398, 1.053, 0.502, 0.949]}, {"time": 0.6, "x": 1.075, "y": 0.949, "curve": [0.698, 1.075, 0.769, 0.921, 0.698, 0.949, 0.769, 1.053]}, {"time": 0.8667, "x": 0.921, "y": 1.053, "curve": [0.965, 0.921, 1.069, 1.075, 0.965, 1.053, 1.069, 0.949]}, {"time": 1.1667, "x": 1.075, "y": 0.949}]}, "RU_L3": {"translate": [{"x": 16.5, "y": -3.35, "curve": [0.123, -6.21, 0.245, -36.42, 0.123, 2.83, 0.245, 11.07]}, {"time": 0.3667, "x": -36.42, "y": 11.07, "curve": [0.562, -36.42, 0.771, 41.14, 0.562, 11.07, 0.771, -10.07]}, {"time": 0.9667, "x": 41.16, "y": -10.07, "curve": [1.04, 41.17, 1.094, 30.23, 1.04, -10.08, 1.094, -7.09]}, {"time": 1.1667, "x": 16.51, "y": -3.35}], "scale": [{"x": 1.05, "y": 0.966, "curve": [0.024, 1.065, 0.042, 1.075, 0.024, 0.956, 0.042, 0.949]}, {"time": 0.0667, "x": 1.075, "y": 0.949, "curve": [0.165, 1.075, 0.269, 0.921, 0.165, 0.949, 0.269, 1.053]}, {"time": 0.3667, "x": 0.921, "y": 1.053, "curve": [0.465, 0.921, 0.569, 1.075, 0.465, 1.053, 0.569, 0.949]}, {"time": 0.6667, "x": 1.075, "y": 0.949, "curve": [0.765, 1.075, 0.869, 0.921, 0.765, 0.949, 0.869, 1.053]}, {"time": 0.9667, "x": 0.921, "y": 1.053, "curve": [1.04, 0.921, 1.094, 1.007, 1.04, 1.053, 1.094, 0.995]}, {"time": 1.1667, "x": 1.05, "y": 0.966}]}, "leg_L4": {"rotate": [{"value": -0.13}]}, "leg_L1": {"translate": [{"y": -15.73, "curve": [0.024, 0, 0.06, -8.23, 0.024, -15.73, 0.06, 34.36]}, {"time": 0.2333, "x": -8.23, "y": 34.36, "curve": [0.544, -8.23, 0.856, 0, 0.544, 34.36, 0.856, -15.73]}, {"time": 1.1667, "y": -15.73}]}, "leg_R4": {"rotate": [{"value": 0.12}]}, "leg_R1": {"translate": [{"y": 3.12, "curve": [0.024, 0, 0.06, -19.44, 0.024, 3.12, 0.06, -18.76]}, {"time": 0.2333, "x": -19.44, "y": -18.76, "curve": [0.544, -19.44, 0.856, 0, 0.544, -18.76, 0.856, 3.12]}, {"time": 1.1667, "y": 3.12}]}, "bodyround": {"translate": [{"x": -19.04, "y": -81.13, "curve": [0.024, -19.04, 0.06, 44.21, 0.024, -81.13, 0.06, 438.86]}, {"time": 0.2333, "x": 44.21, "y": 438.86, "curve": [0.544, 44.21, 0.856, -19.04, 0.544, 438.86, 0.856, -81.13]}, {"time": 1.1667, "x": -19.04, "y": -81.13}]}, "headround3": {"translate": [{"x": 29.33, "curve": [0.035, 29.33, 0.086, 200.47, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 200.47, "curve": [0.611, 200.47, 0.889, 29.33, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": 29.33}]}, "headround": {"translate": [{"y": 75.51, "curve": [0.111, 0, 0.222, 0, 0.035, 75.51, 0.086, 243.88]}, {"time": 0.3333, "y": 243.88, "curve": [0.611, 0, 0.889, 0, 0.611, 243.88, 0.889, 75.51]}, {"time": 1.1667, "y": 75.51}]}, "tunround": {"translate": [{"x": 211.71, "curve": [0.024, 211.71, 0.06, -438.86, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -438.86, "curve": [0.544, -438.86, 0.856, 211.71, 0.678, 0, 0.856, 0]}, {"time": 1.1667, "x": 211.71}]}, "head_R2a": {"rotate": [{"value": 5.56}], "translate": [{"x": -0.61, "y": 0.95}]}, "head_R2b": {"rotate": [{"value": 8.78}], "translate": [{"x": -6.67, "y": -5.27}]}, "head_R2c": {"rotate": [{"value": 11.31}], "translate": [{"x": -14.03, "y": -16.25}]}, "head_R2d": {"rotate": [{"value": 11.68}], "translate": [{"x": -23.09, "y": -30.67}]}, "head_R2e": {"rotate": [{"value": 7.51}], "translate": [{"x": -32.35, "y": -45.62}]}, "head_R2f": {"rotate": [{"value": -2.79}], "translate": [{"x": -38.67, "y": -54.95}]}, "head_R2g": {"rotate": [{"value": -15.84}], "translate": [{"x": -37.62, "y": -50.72}]}, "head_R2h": {"rotate": [{"value": -26.23}], "translate": [{"x": -24.48, "y": -31.06}]}, "arm_R4": {"rotate": [{"value": -6.62, "curve": [0.122, 14.02, 0.245, 41.47]}, {"time": 0.3667, "value": 41.47, "curve": [0.561, 41.47, 0.739, -29.01]}, {"time": 0.9333, "value": -29.03, "curve": [1.007, -29.03, 1.094, -19.09]}, {"time": 1.1667, "value": -6.62}], "translate": [{"x": 11.04, "y": 23.08, "curve": [0.122, -31.8, 0.245, -88.8, 0.122, -11.32, 0.245, -57.09]}, {"time": 0.3667, "x": -88.8, "y": -57.09, "curve": [0.561, -88.8, 0.739, 57.54, 0.561, -57.09, 0.739, 60.41]}, {"time": 0.9333, "x": 57.58, "y": 60.44, "curve": [1.007, 57.59, 1.094, 36.94, 1.007, 60.45, 1.094, 43.87]}, {"time": 1.1667, "x": 11.05, "y": 23.09}]}, "hand_R2": {"rotate": [{"value": -36.2, "curve": [0.147, -7.46, 0.288, 49.28]}, {"time": 0.4333, "value": 49.28, "curve": [0.628, 49.28, 0.839, -52.44]}, {"time": 1.0333, "value": -52.47, "curve": [1.083, -52.48, 1.119, -45.95]}, {"time": 1.1667, "value": -36.2}]}, "head_L2a": {"rotate": [{"value": 351.97}]}, "head_L2b": {"rotate": [{"value": 351.94}], "translate": [{"x": -1.29, "y": 12.33}]}, "head_L2c": {"rotate": [{"value": 352.4}], "translate": [{"x": -2.62, "y": 24.7}]}, "head_L2d": {"rotate": [{"value": 353.33}], "translate": [{"x": -4.16, "y": 36.39}]}, "head_L2e": {"rotate": [{"value": 354.86}], "translate": [{"x": -5.97, "y": 46.66}]}, "head_L2f": {"rotate": [{"value": 357.29}], "translate": [{"x": -7.99, "y": 54.58}]}, "head_L2g": {"rotate": [{"value": 1.34}], "translate": [{"x": -10.11, "y": 58.73}]}, "head_L2h": {"rotate": [{"value": 9.23}], "translate": [{"x": -11.75, "y": 56.62}]}, "head_L2i": {"rotate": [{"value": 29.59}], "translate": [{"x": -12.77, "y": 42.32}]}, "sh_L2": {"rotate": [{"value": -14.28, "curve": [0.122, -27.43, 0.245, -44.92]}, {"time": 0.3667, "value": -44.92, "curve": [0.561, -44.92, 0.739, -0.01]}, {"time": 0.9333, "curve": [1.007, 0, 1.094, -6.33]}, {"time": 1.1667, "value": -14.28}], "translate": [{"x": 29.46, "y": -44.89, "curve": [0.122, 6.27, 0.245, -24.59, 0.122, -5.86, 0.245, 46.05]}, {"time": 0.3667, "x": -24.59, "y": 46.05, "curve": [0.561, -24.59, 0.739, 54.63, 0.561, 46.05, 0.739, -87.23]}, {"time": 0.9333, "x": 54.65, "y": -87.27, "curve": [1.007, 54.65, 1.094, 43.48, 1.007, -87.28, 1.094, -68.47]}, {"time": 1.1667, "x": 29.46, "y": -44.9}]}, "leg_R6": {"translate": [{"y": 4.92, "curve": [0.078, 0, 0.156, 0, 0.024, 4.92, 0.06, 325.82]}, {"time": 0.2333, "y": 325.82, "curve": [0.544, 0, 0.856, 0, 0.544, 325.82, 0.856, 4.92]}, {"time": 1.1667, "y": 4.92}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.027, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-3.56642, -0.47611, -3.56469, -0.47637, -3.69961, -0.49349, -3.69789, -0.49373, -4.28636, 0.00035, -4.28579, 0.00022, -3.13782, 0.20619, -3.13742, 0.20606, -1.71335, 0.35754, -1.71284, 0.35745, -0.50364, 0.31671, -0.50321, 0.31655, -0.60163, -0.03564, -0.60141, -0.03565, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.71869, 0.00389, -0.71847, 0.00384, -2.27826, -0.1629, -2.27718, -0.16313, 0, 0, 0, 0, -1.23269, -0.09603, -1.23215, -0.09609, -2.54175, -0.16639, -2.54067, -0.16673, -3.57054, -0.46893, -3.56878, -0.46934, -3.77813, -0.44884, -3.77634, -0.44913, -3.12764, -0.40697, -3.12609, -0.40723, -2.15879, -0.11995, -2.15729, -0.12024, -0.9796, -0.14016, -0.97831, -0.1403, -0.38199, -0.05902, -0.38113, -0.05913], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.027, 0, 0.059, 0.99]}, {"time": 0.2333, "vertices": [-3.58789, 0.09829, -3.58725, 0.09804, -2.87673, 0.21642, -2.87619, 0.21627, -1.59897, 0.16476, -1.59869, 0.16452, -0.47635, 0.04048, -0.47617, 0.0404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32125, 0.01021, -0.32125, 0.01009, -0.50701, 0.0578, -0.50701, 0.05762, -0.36202, -0.05146, -0.36194, -0.05156, -1.03696, 0.01401, -1.03671, 0.01397, -1.95618, -0.07381, -1.9559, -0.07393, -3.21229, 0.06788, -3.21136, 0.06768, -4.41762, 0.12492, -4.4168, 0.12481, -4.53418, 0.37009, -4.53332, 0.36996, 0, 0, 0, 0, -0.84543, 0.00458, -0.8454, 0.00447, -2.18203, 0.00489, -2.18178, 0.00479, -3.2642, 0.03771, -3.26338, 0.0376, -3.94371, -0.11217, -3.94317, -0.11229, 0, 0, 0, 0, -0.04482, -0.00321, -0.04478, -0.00322, 0.16547, 0.01701, 0.16572, 0.01701, 0, 0, 0, 0, 0, 0, 0, 0, -0.46994, 0.04017, -0.46972, 0.04009, -1.14664, 0.10654, -1.14635, 0.10641, -2.42573, 0.15669, -2.42548, 0.15663, -3.52396, 0.08296, -3.52361, 0.08287], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.027, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 96, "vertices": [-0.52192, 0.07922, -0.52185, 0.0792, -2.10204, 0.04954, -2.10139, 0.04933, -3.33803, 0.09438, -3.33724, 0.0942, -3.4913, 0.02509, -3.49055, 0.025, -2.82732, 0.28911, -2.82661, 0.289, -1.79549, 0.18804, -1.79506, 0.18793, -0.73271, -0.00242, -0.73242, -0.0025, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.76462, -0.02137, -0.76433, -0.02144, -2.31949, -0.05766, -2.31885, -0.05778, -3.53042, -0.12756, -3.52884, -0.12782, -3.75888, 0.03931, -3.75805, 0.03914, -3.02682, -0.06654, -3.0261, -0.06664, -1.93127, -0.12982, -1.93066, -0.12993, -0.75225, -0.07879, -0.75193, -0.07888, -0.23617, -0.07518, -0.23606, -0.07521], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.027, 0, 0.059, 0.99]}, {"time": 0.2333, "offset": 48, "vertices": [0.33309, -0.02285, 0.33367, -0.02281, -0.01943, 0.00133, -0.01842, 0.00135, -0.83686, 0.05742, -0.83478, 0.05744, -1.49549, 0.10263, -1.49232, 0.10261, -1.39331, 0.0956, -1.38993, 0.0956, -1.49549, 0.10263, -1.49232, 0.10261, -1.24565, 0.08552, -1.24436, 0.0855, -0.34547, 0.02374, -0.34489, 0.02376, 0.20206, -0.01384, 0.20328, -0.0138, 1.215, -0.08339, 1.2155, -0.08336, 0.94461, 0.04649, 0.94489, 0.04633, 0.49802, -0.32324, 0.49788, -0.32376, 3.06032, -0.04237, 3.05857, -0.04326, 3.06032, -0.04237, 3.05857, -0.04326, 0.49725, 0.73158, 0.49675, 0.73119, 1.06403, -0.07305, 1.06504, -0.07302, 0.92854, -0.06373, 0.92918, -0.06366, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.85212, -0.05848, 0.85212, -0.05843, 0.24343, -0.0167, 0.24343, -0.01668, -0.52347, 0.07041, -0.52198, 0.07017, -1.87284, 0.07109, -1.87115, 0.07079, -1.75296, 0.44423, -1.75214, 0.44359, -1.90298, 0.29273, -1.90077, 0.29254, -1.29444, 0.49161, -1.29253, 0.49115, -0.15572, 0.01069, -0.1555, 0.01069, 0.42606, -0.02924, 0.42606, -0.02921, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 0.66956, -0.04594, 0.66956, -0.04585, 1.43521, -0.13953, 1.43539, -0.14008, 2.64984, 0.01972, 2.64915, 0.01871, 2.23182, -0.08021, 2.22982, -0.0814, 1.09682, -0.10394, 1.09593, -0.10483, 1.71892, -0.14204, 1.71883, -0.14278, 3.42364, 0.09819, 3.42184, 0.09666, 1.71892, -0.14204, 1.71883, -0.14278, 2.63939, 0.12814, 2.6384, 0.12725, 2.63939, 0.12814, 2.6384, 0.12725, 1.27515, -0.51074, 1.2749, -0.51141, 0.6366, -0.11084, 0.63635, -0.11121], "curve": [0.544, 0.02, 0.856, 1]}, {"time": 1.1667}]}}}}}}}