﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [Black]
    class MapLayerRectangleTileMeshCreator : MapLayerTileMeshCreator {
        public override Mesh CreateLayerTileMesh() {
            var mesh = new Mesh();
            Vector3[] positions = new Vector3[4];
            int[] indices = new int[]{
            3,2,0,2,1,0,
        };

            float width = mLayer.tileWidth;
            float height = mLayer.tileHeight;
            float ox = width * 0.5f;
            float oy = height * 0.5f;

            //bottom
            positions[0] = new Vector3(-ox, 0, -oy);
            //right
            positions[1] = new Vector3(ox, 0, -oy);
            //top
            positions[2] = new Vector3(ox, 0, oy);
            //left
            positions[3] = new Vector3(-ox, 0, oy);

            mesh.vertices = positions;
            mesh.triangles = indices;

            return mesh;
        }
    };
}


#endif