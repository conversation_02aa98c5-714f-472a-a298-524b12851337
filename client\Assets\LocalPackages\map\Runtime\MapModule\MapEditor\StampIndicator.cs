﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class StampIndicator
    {
        public void SetStamp(BlendTerrainLayerUI.Stamp stamp, BlendTerrainLayer layer)
        {
            Utils.DestroyObject(mRoot);
            mRoot = new GameObject("Stamp Brush");
            int width = stamp.GetWidth();
            int height = stamp.GetHeight();
            int ox = width / 2;
            int oy = height / 2;
            for (int i = 0; i < height; ++i)
            {
                for (int j = 0; j < width; ++j)
                {
                    var data = stamp.GetData(j, i);
                    if (data != null)
                    {
                        var obj = AssetDatabase.LoadAssetAtPath<GameObject>(data.prefabPath);
                        if (obj)
                        {
                            obj = GameObject.Instantiate<GameObject>(obj);
                            obj.transform.parent = mRoot.transform;
                            obj.transform.position = layer.FromCoordinateToWorldPosition(j - ox, i - oy);
                        }
                    }
                }
            }
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
        }

        public void ShowStamp()
        {
            if (mRoot != null)
            {
                mRoot.SetActive(true);
            }
        }

        public void HideStamp()
        {
            if (mRoot != null)
            {
                mRoot.SetActive(false);
            }
        }

        public void Update(Vector3 position)
        {
            if (mRoot != null)
            {
                mRoot.transform.position = position;
            }
        }

        GameObject mRoot;
    }
}

#endif