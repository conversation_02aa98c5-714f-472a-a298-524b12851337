﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class GridViewer
    {
        public GridViewer(string name, int minX, int minY, int width, int height, float layerWidth, float layerHeight, float regionWidth, float regionHeight, Color color0, Color color1, bool showText, float planeHeight, Transform parent, bool visible, float offsetX = 0, float offsetZ = 0)
        {
            OnDestroy();

            mRoot = new GameObject(name);
            mRoot.transform.SetParent(parent);
            Utils.HideGameObject(mRoot);

            int k = 0;
            int xRegionCount = Mathf.CeilToInt(layerWidth / regionWidth);
            int yRegionCount = Mathf.CeilToInt(layerHeight / regionHeight);
            var regionSize = new Vector2(regionWidth, regionHeight);

            int maxX = Mathf.Min(xRegionCount, minX + width - 1);
            int maxY = Mathf.Min(yRegionCount, minY + height - 1);
            minX = Mathf.Clamp(minX, 0, maxX);
            minY = Mathf.Clamp(minY, 0, maxY);

            mMaterial0 = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            mMaterial0.color = color0;
            mMaterial1 = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            mMaterial1.color = color1;
            mMaterial0.renderQueue = 3100;
            mMaterial1.renderQueue = 3100;

            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    var pos = new Vector3((j + 0.5f) * regionWidth + offsetX, planeHeight, (i + 0.5f) * regionHeight + offsetZ);
                    if ((i + j) % 2 == 0)
                    {
                        CreateGameObject(j, i, pos, regionSize, mMaterial0, showText);
                    }
                    else
                    {
                        CreateGameObject(j, i, pos, regionSize, mMaterial1, showText);
                    }
                    ++k;
                }
            }

            SetVisible(visible);
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
            if (mMaterial0 != null)
            {
                Utils.DestroyObject(mMaterial0);
                mMaterial0 = null;
            }
            if (mMaterial1 != null)
            {
                Utils.DestroyObject(mMaterial1);
                mMaterial1 = null;
            }
            if (mMesh != null)
            {
                Utils.DestroyObject(mMesh);
                mMesh = null;
            }
        }

        GameObject CreateGameObject(int x, int y, Vector3 pos, Vector2 regionSize, Material mtl, bool showText)
        {
            var obj = new GameObject();
            obj.AddComponent<MeshFilter>();
            obj.AddComponent<MeshRenderer>();
            obj.name = string.Format("Region_{0}_{1}", x, y);
            obj.transform.position = pos;
            obj.transform.localScale = new Vector3(regionSize.x, 1.0f, regionSize.y);
            obj.transform.SetParent(mRoot.transform);
            obj.GetComponent<MeshRenderer>().sharedMaterial = mtl;
            if (mMesh == null)
            {
                mMesh = CreateMesh();
            }
            obj.GetComponent<MeshFilter>().sharedMesh = mMesh;

            if (showText)
            {
                var textObj = new GameObject("Text");
                Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
                var renderer = textObj.AddComponent<MeshRenderer>();
                renderer.sharedMaterial = ArialFont.material;
                var textMesh = textObj.AddComponent<TextMesh>();
                textMesh.text = string.Format("{0}_{1}", x, y);
                textMesh.fontSize = 16;
                var textPos = obj.transform.position;
                textObj.transform.position = new Vector3(textPos.x, 20.0f, textPos.z);
                textObj.transform.localScale = Vector3.one * regionSize.x * 0.1f;
                textObj.transform.rotation = Quaternion.Euler(45, 0, 0);
                textObj.transform.SetParent(mRoot.transform);
                textMesh.anchor = TextAnchor.MiddleCenter;
            }

            return obj;
        }

        Mesh CreateMesh()
        {
            var mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(-0.5f, 0, -0.5f),
                new Vector3(-0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, -0.5f),
            };
            mesh.triangles = new int[]
            {
                0, 1, 2,0,2,3,
            };

            return mesh;
        }

        public void SetVisible(bool visible)
        {
            if (mRoot != null)
            {
                if (visible != mRoot.activeSelf)
                {
                    mRoot.SetActive(visible);
                }
            }
        }

        public bool visible
        {
            get
            {
                if (mRoot != null)
                {
                    return mRoot.activeSelf;
                }
                return false;
            }
        }

        Mesh mMesh;
        GameObject mRoot;
        Material mMaterial0;
        Material mMaterial1;
    }
}