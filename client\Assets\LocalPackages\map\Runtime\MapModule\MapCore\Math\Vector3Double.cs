﻿ 



 
 

using System;


namespace TFW.Map
{
    public class Vector3d
    {
        public Vector3d(double xx, double yy, double zz)
        {
            this.x = xx;
            this.y = yy;
            this.z = zz;
        }

        public double GetMagnitude()
        {
            return Math.Sqrt(x * x + y * y + z * z);
        }

        public double GetMagnitudeSqr()
        {
            return x * x + y * y + z * z;
        }

        public void Normalize()
        {
            double mag = GetMagnitude();
            if (!MathDouble.Approximately(mag, 0))
            {
                x /= mag;
                y /= mag;
                z /= mag;
            }
        }

        public Vector3d GetNormalized()
        {
            double mag = GetMagnitude();
            if (!MathDouble.Approximately(mag, 0))
            {
                return new Vector3d(x / mag, y / mag, z / mag);
            }
            return new Vector3d(0, 0, 0);
        }

        public static double Dot(Vector3d a, Vector3d b)
        {
            return a.x * b.x + a.y * b.y + a.z * b.z;
        }

        public static Vector3d Project(Vector3d a, Vector3d onNormal)
        {
            return onNormal * Dot(a, onNormal);
        }

        public static Vector3d Cross(Vector3d a, Vector3d b)
        {
            double x = a.y * b.z - a.z * b.y;
            double y = a.z * b.x - a.x * b.z;
            double z = a.x * b.y - a.y * b.x;
            return new Vector3d(x, y, z);
        }

        public static Vector3d operator +(Vector3d a, Vector3d b)
        {
            return new Vector3d(a.x + b.x, a.y + b.y, a.z + b.z);
        }

        public static Vector3d operator -(Vector3d a, Vector3d b)
        {
            return new Vector3d(a.x - b.x, a.y - b.y, a.z - b.z);
        }

        public static Vector3d operator *(Vector3d a, double v)
        {
            return new Vector3d(a.x * v, a.y * v, a.z * v);
        }

        public static Vector3d operator *(double v, Vector3d a)
        {
            return new Vector3d(a.x * v, a.y * v, a.z * v);
        }


        public static bool operator == (Vector3d a, Vector3d b)
        {
            return MathDouble.Approximately(a.x, b.x) &&
                MathDouble.Approximately(a.y, b.y) &&
                MathDouble.Approximately(a.z, b.z);
        }

        public static bool operator !=(Vector3d a, Vector3d b)
        {
            return !MathDouble.Approximately(a.x, b.x) ||
                !MathDouble.Approximately(a.y, b.y) ||
                !MathDouble.Approximately(a.z, b.z);
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return x.GetHashCode() + y.GetHashCode() + z.GetHashCode();
        }

        public double x = 0;
        public double y = 0;
        public double z = 0;
    };
}