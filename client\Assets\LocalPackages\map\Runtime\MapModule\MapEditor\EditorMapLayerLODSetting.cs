﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [System.Flags]
    public enum LODDisplayFlag
    {
        ShaderLOD = 1,
        UseRenderTexture = 2,
        SpecialBufferSetting = 4,
        HideObject = 8,
        TerrainLODTile = 16,
    }

    //地图层的lod设置
    public class EditorMapLayerLODSetting
    {
        public void Draw(string title, MapLayerLODConfig lodConfig, LODDisplayFlag displayFlag, System.Func<int, bool> lodChangeConditionCallback, System.Action<int, int> onLODChanged)
        {
            mLayerLODConfig = lodConfig;
            mShowFold = EditorGUILayout.Foldout(mShowFold, new GUIContent(title, "地图层的lod设置"));
            if (mShowFold)
            {
                if (lodConfig != null)
                {
                    EditorGUILayout.BeginVertical("GroupBox");

                    var layerLodConfig = lodConfig.lodConfigs;
                    int newLODCount = layerLodConfig.Length;
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("LOD Count", layerLodConfig.Length.ToString());
                    if (GUILayout.Button("Change"))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Change Map Layer LOD Count");
                        var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Count", "", newLODCount.ToString()),
                        };

                        dlg.Show(items, (List<InputDialog.Item> param) =>
                        {
                            int lodCount;
                            Utils.ParseInt((param[0] as InputDialog.StringItem).text, out lodCount);
                            if (lodCount <= 0)
                            {
                                return false;
                            }

                            int oldLODCount = lodConfig.lodConfigs.Length;

                            bool ok = true;
                            if (lodChangeConditionCallback != null)
                            {
                                ok = lodChangeConditionCallback(lodCount);
                            }
                            if (ok)
                            {
                                lodConfig.SetLODCount(lodCount);
                                int realNewLODCount = lodConfig.lodConfigs.Length;
                                CreateLODNames(realNewLODCount);
                                if (onLODChanged != null)
                                {
                                    onLODChanged(oldLODCount, realNewLODCount);
                                }
                            }
                            return true;
                        });
                    }
                    EditorGUILayout.EndHorizontal();

                    var map = Map.currentMap;
                    var lodManager = map.data.lodManager;
                    if (mLODNames == null || mLODNames.Length != layerLodConfig.Length || newLODCount != layerLodConfig.Length)
                    {
                        //保证地图层的lod设置数量和地图LOD数量一致
                        lodConfig.SetLODCount(newLODCount);
                        if (newLODCount < 0)
                        {
                            newLODCount = 0;
                        }
                        CreateLODNames(newLODCount);
                    }

                    DrawLODs(lodConfig, displayFlag);
                    EditorGUILayout.EndVertical();
                }
            }
        }

        void DrawLODs(MapLayerLODConfig config, LODDisplayFlag displayFlags/*bool showShaderLOD, bool showUseRenderTexture, bool showSpecialBufferSetting*/)
        {
            var configs = config.lodConfigs;
            EditorGUILayout.BeginVertical();
            for (int i = 0; i < configs.Length; ++i)
            {
                mFoldState[i] = EditorGUILayout.Foldout(mFoldState[i], mLODNames[i]);
                if (mFoldState[i])
                {
                    if (i != 0)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.TextField("LOD Name", configs[i].name);
                        if (GUILayout.Button("Change"))
                        {
                            var dlg = EditorUtils.PopupDialog<ChangeMapLayerLODReferenceDialog>("Change LOD Name");
                            dlg.Show(config, configs[i].name, OnChangeLODName);
                        }
                        EditorGUILayout.EndHorizontal();
                        EditorGUILayout.IntField(new GUIContent("Zoom", "LOD值"), (int)Map.currentMap.data.lodManager.ConvertNameToZoom(configs[i].name));
                        configs[i].changeZoomThreshold = EditorGUILayout.FloatField(new GUIContent("Change Threshold", "单位为zoom值, 作为lod切换时的非对称缓冲值,避免游戏中频繁在两个lod之间切换.例如设置的lod高度是100米,如果没有缓冲,则100米高度为lod切换分界线,如果有10米的缓冲,则相机升高到110米才切换更高的lod,下降到100米切换成更低的lod"), configs[i].changeZoomThreshold);
                        float cameraHeightInZoom = Map.currentMap.CalculateCameraHeightFromZoom(configs[i].changeZoom);
                        float cameraHeightInZoomWithTreshold = Map.currentMap.CalculateCameraHeightFromZoom(configs[i].changeZoom + configs[i].changeZoomThreshold);
                        if (!configs[i].flag.HasFlag(MapLayerLODConfigFlag.UseMeterThreshold))
                        {
                            EditorGUILayout.LabelField(new GUIContent("Threshold Distance(m)", "上述change threshold转换成米为单位"), new GUIContent((cameraHeightInZoomWithTreshold - cameraHeightInZoom).ToString()));
                        }
                        if (displayFlags.HasFlag(LODDisplayFlag.HideObject))
                        {
                            configs[i].hideObject = EditorGUILayout.Toggle(new GUIContent("Hide Object", "是否在该lod隐藏物体"), configs[i].hideObject);
                        }
                        if (displayFlags.HasFlag(LODDisplayFlag.TerrainLODTile))
                        {
                            configs[i].terrainLODTileCount = EditorGUILayout.IntField(new GUIContent("Bake Terrain Tile Count", "烘培成几乘几的tile"), configs[i].terrainLODTileCount);
                        }
                        if (displayFlags.HasFlag(LODDisplayFlag.SpecialBufferSetting) && !configs[i].hideObject)
                        {
                            bool useSpecialHeightBuffer = EditorGUILayout.Toggle(new GUIContent("Use Special Height Buffer", "用于地表的transition切换后相机高度变化"), configs[i].flag.HasFlag(MapLayerLODConfigFlag.UseSpecialHeightBuffer));
                            if (useSpecialHeightBuffer)
                            {
                                configs[i].flag |= MapLayerLODConfigFlag.UseSpecialHeightBuffer;
                            }
                            else
                            {
                                configs[i].flag &= ~MapLayerLODConfigFlag.UseSpecialHeightBuffer;
                            }
                        }
                        if (displayFlags.HasFlag(LODDisplayFlag.ShaderLOD) && !configs[i].hideObject)
                        {
                            configs[i].shaderLOD = EditorGUILayout.IntField(new GUIContent("Shader LOD", "当切换到该lod时所设置的river shader的maximum lod值"), configs[i].shaderLOD);
                        }
                        if (displayFlags.HasFlag(LODDisplayFlag.UseRenderTexture) && !configs[i].hideObject)
                        {
                            configs[i].useRenderTexture = EditorGUILayout.Toggle(new GUIContent("Use Render Texture", "是否使用render texture来替代模型"), configs[i].useRenderTexture);
                        }
                    }
                    else
                    {
                        EditorGUILayout.LabelField(new GUIContent("Zoom", "LOD值"), new GUIContent("0"));
                        EditorGUILayout.LabelField(new GUIContent("Change Threshold", "单位为zoom值, 作为lod切换时的非对称缓冲值,避免游戏中频繁在两个lod之间切换.例如设置的lod高度是100米,如果没有缓冲,则100米高度为lod切换分界线,如果有10米的缓冲,则相机升高到110米才切换更高的lod,下降到100米切换成更低的lod"), new GUIContent("0"));
                        if (displayFlags.HasFlag(LODDisplayFlag.ShaderLOD))
                        {
                            configs[i].shaderLOD = EditorGUILayout.IntField(new GUIContent("Shader LOD", "当切换到该lod时所设置的river shader的maximum lod值"), configs[i].shaderLOD);
                        }
                    }
                }
            }
            EditorGUILayout.EndVertical();
        }

        void OnChangeLODName(string oldName, string newName)
        {
            var lod = mLayerLODConfig.GetLOD(oldName);
            lod.name = newName;
        }

        void CreateLODNames(int n)
        {
            mLODNames = new string[n];
            for (int i = 0; i < n; ++i)
            {
                mLODNames[i] = string.Format("LOD {0}", i);
            }

            mFoldState = new bool[n];
            for (int i = 0; i < n; ++i)
            {
                mFoldState[i] = true;
            }
        }

        MapLayerLODConfig mLayerLODConfig;
        string[] mLODNames;
        bool[] mFoldState;
        bool mShowFold = true;
    }
}

#endif