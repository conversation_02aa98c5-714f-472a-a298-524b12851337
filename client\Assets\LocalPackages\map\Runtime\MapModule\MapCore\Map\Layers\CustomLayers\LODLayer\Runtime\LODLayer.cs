﻿ 



 
 



/*
 * created by wzw at 2019.8.5
 */

using System.Collections.Generic;
using TFW.Map.config;
using UnityEngine;

namespace TFW.Map
{
    //用来发送lod切换事件的layer
    public partial class LODLayer : MapLayerBase
    {
        public LODLayer(Map map) : base(map)
        {
        }

        //清理地图层
        public override void OnDestroy()
        {
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }

            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.LODLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.mapWidth, sourceLayer.mapHeight, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            mLayerData = new LODLayerData(header, config, map);
            mLayerView = new DummyLayerView(mLayerData);
            map.AddMapLayer(this);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            return lodChanged;
        }

        public override void RefreshObjectsInViewport() { }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return false;
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void CopyFromMapLODSetting()
        {
            var mapLODManager = map.data.lodManager;
            int lodCount = mapLODManager.lodCount;

            mLayerData.lodConfig = new MapLayerLODConfig(map);
            mLayerData.lodConfig.SetLODCount(lodCount);
            for (int i = 0; i < lodCount; ++i)
            {
                mLayerData.lodConfig.lodConfigs[i] = new MapLayerLODConfig.LOD(mapLODManager.GetLOD(i).name, i, 0, false, 0, false, MapLayerLODConfigFlag.None, 0);
            }
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public MapLayerView layerView { get { return mLayerView; } }
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override int lodCount => mLayerData.lodCount;
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }

        public override bool active { get { return true; } set { } }

        //该层数据的管理
        LODLayerData mLayerData;
        DummyLayerView mLayerView;
    }
}
