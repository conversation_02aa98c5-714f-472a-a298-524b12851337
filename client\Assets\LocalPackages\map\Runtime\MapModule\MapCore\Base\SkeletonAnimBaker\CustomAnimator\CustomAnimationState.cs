﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    //自定义的动画状态
    public class CustomAnimationState
    {
        public CustomAnimationState(int id, AnimationStateInfo stateInfo, AnimationInfo animInfo, int animationIndex/*, int debugID*/)
        {
            mID = id;
            //mDebugID = debugID;
            mAnimInfo = animInfo;
            mAnimStateInfo = stateInfo;
            mTimeStep = animInfo.lengthInSeconds / (animInfo.totalFrames - 1);
            mAnimationIndex = animationIndex;
            mSpeedParameterActive = mAnimStateInfo.speedInfo.speedParameterActive;
            mSpeed = mAnimStateInfo.speedInfo.speed;
            int n = 0;
            if (mAnimStateInfo.events != null)
            {
                n = mAnimStateInfo.events.Length;
            }
            if (n > 0)
            {
                mTriggeredEvents = new bool[n];
            }
        }

        public void PostInit(CustomAnimationStateMachine stateMachine)
        {
            mTransitions = new CustomAnimStateTransition[mAnimStateInfo.transitions.Length];
            for (int i = 0; i < mTransitions.Length; ++i)
            {
                var fromState = stateMachine.GetState(mAnimStateInfo.transitions[i].fromStateName);
                var toState = stateMachine.GetState(mAnimStateInfo.transitions[i].toStateName);
#if UNITY_EDITOR
                if (fromState == null)
                {
                    Debug.LogError($"invalid from state {mAnimStateInfo.transitions[i].fromStateName}");
                }
                if (toState == null)
                {
                    Debug.LogError($"invalid to state {mAnimStateInfo.transitions[i].toStateName}");
                }
#endif
                var conditions = CreateConditions(mAnimStateInfo.transitions[i].conditions);
                mTransitions[i] = new CustomAnimStateTransition(fromState, toState, conditions, mAnimStateInfo.transitions[i].hasExitTime, mAnimStateInfo.transitions[i].exitTime, mAnimStateInfo.transitions[i].transitionOffset, mAnimStateInfo.transitions[i].transitionDuration, mAnimStateInfo.transitions[i].fixedDuration);
            }
        }

        //如果有无条件转移的transition,则返回transition的target state
        public CustomAnimationState Update(float animatorSpeed, AnimationParameterInfo[] parameters, bool useBlending, CustomAnimationStateMachine statemachine, out float transitionDuration, out float startTransitionTime)
        {
            transitionDuration = 0;
            startTransitionTime = 0;
            if (mPaused)
            {
                return null;
            }

            //计算最终播放速度
            float finalSpeed = animatorSpeed * GetStateSpeed(parameters);

            CustomAnimationState nextState = null;

            mCurrentTime += Time.deltaTime * finalSpeed;
            mCurrentFrame = (int)(mCurrentTime / mTimeStep);

            //check events
            UpdateEvents(statemachine);

            //这里如果使用了blending,就必须每帧检测transition.否则可以在动画播放完一次循环后检测transition?
            if (useBlending)
            {
                nextState = CheckTransition(parameters, out transitionDuration);
                if (nextState != null)
                {
                    startTransitionTime = mCurrentTime;
                }
                if (mCurrentTime >= mAnimInfo.lengthInSeconds)
                {
                    if (mAnimInfo.loop)
                    {
                        mCurrentFrame = 0;
                        mCurrentTime = 0;
                        ResetAnimEventTriggerState();
                    }
                    else
                    {
                        mCurrentFrame = mAnimInfo.totalFrames - 1;
                        mCurrentTime = mAnimInfo.lengthInSeconds;
                    }
                }
            }
            else
            {
                if (mCurrentTime >= mAnimInfo.lengthInSeconds)
                {
                    nextState = CheckTransition(parameters, out transitionDuration);
                    if (nextState != null)
                    {
                        startTransitionTime = mCurrentTime;
                    }
                    if (mAnimInfo.loop)
                    {
                        mCurrentFrame = 0;
                        mCurrentTime = 0;
                        ResetAnimEventTriggerState();
                    }
                    else
                    {
                        mCurrentFrame = mAnimInfo.totalFrames - 1;
                        mCurrentTime = mAnimInfo.lengthInSeconds;
                    }
                }
            }

            return nextState;
        }

        float GetStateSpeed(AnimationParameterInfo[] parameters)
        {
            float stateSpeed = mSpeed;
            if (mSpeedParameterActive) {
                if (mSpeedParameter == null)
                {
                    for (int i = 0; i < parameters.Length; ++i)
                    {
                        if (parameters[i].name == mAnimStateInfo.speedInfo.speedParameter)
                        {
                            mSpeedParameter = parameters[i];
                            break;
                        }
                    }
#if UNITY_EDITOR
                    Debug.Assert(mSpeedParameter != null, $"Invalid speed parameter {mAnimStateInfo.speedInfo.speedParameter}");
#endif
                }
                stateSpeed *= mSpeedParameter.defaultFloat;
            }
            return stateSpeed;
        }

        CustomAnimationState CheckFreeTransition(out float transitionDuration)
        {
            transitionDuration = 0;
            float normalizedTime = mCurrentTime / mAnimInfo.lengthInSeconds;

            for (int i = 0; i < mTransitions.Length; ++i)
            {
                if (mTransitions[i].conditions.Length == 0)
                {
                    bool match = mTransitions[i].MatchExitTimeOnly(normalizedTime);
                    if (match)
                    {
                        transitionDuration = mTransitions[i].transitionDuration;
                        return mTransitions[i].toState;
                    }
                }
            }
            return null;
        }

        public void Reset(float startTime)
        {
            mCurrentTime = startTime;
            mCurrentFrame = GetFrame(startTime);
            ResetAnimEventTriggerState();
            //Debug.Log($"reset time {mCurrentTime}, frame: {mCurrentFrame}");
            mPaused = false;
        }

        void ResetAnimEventTriggerState()
        {
            if (mTriggeredEvents != null)
            {
                for (int i = 0; i < mTriggeredEvents.Length; ++i)
                {
                    mTriggeredEvents[i] = false;
                }
            }
        }

        public void SetStartTime(float normalizedTimeOffset)
        {
            mCurrentTime = normalizedTimeOffset * mAnimInfo.lengthInSeconds;
            mCurrentTime = Mathf.Clamp(mCurrentTime, 0, mAnimInfo.lengthInSeconds);
        }

        public void SetFixedStartTime(float fixedOffset)
        {
            mCurrentTime = fixedOffset;
            mCurrentTime = Mathf.Clamp(mCurrentTime, 0, mAnimInfo.lengthInSeconds);
        }

        public float GetNormalizedCurrentTime()
        {
            return Mathf.Clamp01(mCurrentTime / mAnimInfo.lengthInSeconds);
        }

        public void Pause()
        {
            mPaused = true;
        }

        public void Resume()
        {
            mPaused = false;
        }

        //检查是否有满足条件的transition
        public CustomAnimationState CheckTransition(AnimationParameterInfo[] parameters, out float transitionDuration)
        {
            float normalizedTime = mCurrentTime / mAnimInfo.lengthInSeconds;
            for (int i = 0; i < mTransitions.Length; ++i)
            {
                bool match = mTransitions[i].Match(normalizedTime, parameters);
                if (match)
                {
                    transitionDuration = mTransitions[i].transitionDuration;
                    return mTransitions[i].toState;
                }
            }

            if (!mAnimInfo.loop)
            {
                return CheckFreeTransition(out transitionDuration);
            }

            transitionDuration = 0;
            return null;
        }

        public bool GetCycleOffset(AnimationParameterInfo[] parameters, out float cycleOffsetInSeconds)
        {
            cycleOffsetInSeconds = 0;
            if (!mAnimInfo.loop)
            {
                return false;
            }
            //只使用parameter来设置
            if (!mAnimStateInfo.cycleOffsetInfo.cycleOffsetParameterActive) {
                return false;
            }
            string cycleOffsetParmaName = mAnimStateInfo.cycleOffsetInfo.cycleOffsetParameter;
            for (int i = 0; i < parameters.Length; ++i)
            {
                if (parameters[i].name == cycleOffsetParmaName)
                {
                    cycleOffsetInSeconds = mAnimStateInfo.cycleOffsetInfo.cycleOffset * mAnimInfo.lengthInSeconds;
                    return true;
                }
            }
            return false;
        }

        CustomAnimStateTransitionCondition[] CreateConditions(AnimationTransitionConditionInfo[] cond)
        {
            CustomAnimStateTransitionCondition[] conditions = new CustomAnimStateTransitionCondition[cond.Length];
            for (int i = 0; i < cond.Length; ++i)
            {
                conditions[i] = new CustomAnimStateTransitionCondition((TransitionConditionMode)cond[i].mode, cond[i].parameter, cond[i].threshold);
            }
            return conditions;
        }

        public int GetFrame(float time)
        {
            return Mathf.FloorToInt(time / mTimeStep);
        }

        void UpdateEvents(CustomAnimationStateMachine statemachine)
        {
            var events = mAnimStateInfo.events;
            if (events != null)
            {
                for (int i = 0; i < events.Length; ++i)
                {
                    if (mTriggeredEvents[i] == false && mCurrentTime >= events[i].time)
                    {
                        mTriggeredEvents[i] = true;
                        //Debug.Log($"trigger events at {mCurrentTime}");
                        statemachine.TriggerEventCallback(events[i]);
                    }
                }
            }
        }

        public float currentFrame { get { return mCurrentFrame; } }
        public float currentTime { get { return mCurrentTime; } }
        public int animationIndex { get { return mAnimationIndex; } }
        public AnimationInfo animInfo { get { return mAnimInfo; } }
        public AnimationStateInfo animStateInfo { get { return mAnimStateInfo; } }
        public int id { get { return mID; } }

        int mID;
        //int mDebugID;
        AnimationInfo mAnimInfo;
        AnimationStateInfo mAnimStateInfo;
        float mCurrentTime = 0;
        int mCurrentFrame;
        float mTimeStep;
        int mAnimationIndex;
        bool mPaused;
        bool[] mTriggeredEvents;
        bool mSpeedParameterActive;
        float mSpeed;
        AnimationParameterInfo mSpeedParameter;
        CustomAnimStateTransition[] mTransitions;
    }
}