﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class MapObjectLayerData : MapLayerData
    {
        public MapObjectLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, ModelLODGroupManager groupManager) : base(header, config, map)
        {
            mGroupManager = groupManager;
            mFrameActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_TERRAIN_TILE_QUEUE);
        }

        public override void OnDestroy()
        {
            foreach (var obj in mObjects)
            {
                map.DestroyObject(obj.Value.GetEntityID());
            }
            mObjects = null;
        }

        public override bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (map.enableLOD)
            {
                lodChanged = base.SetZoom(zoom);
            }

            return lodChanged;
        }

        //设置对象的可见性
        public virtual void SetObjectActive(IMapObjectData objectData, bool active, int lod)
        {
            if (mUseFrameAction)
            {
                var action = FrameActionSetObjectActive.Require(this, objectData, active);
                map.AddFrameAction(mFrameActionQueueIndex, action, false);
            }
            else
            {
                SetObjectActiveFromAction(objectData, active, lod);
            }
        }

        public bool SetObjectActiveFromAction(IMapObjectData objectData, bool active, int lod)
        {
            bool change = SetObjectActiveImpl(objectData, active);
            if (change)
            {
                mOnActiveStateChangeCallback(objectData, lod);
            }
            return change;
        }

        public void ForceUpdateObjectVisibility(IMapObjectData objectData, int lod)
        {
            mOnActiveStateChangeCallback(objectData, lod);   
        }

        protected bool SetObjectActiveImpl(IMapObjectData objectData, bool newActiveState)
        {
            bool changed = objectData.SetObjActive(newActiveState);
            if (changed)
            {
                if (newActiveState)
                {
                    mVisibleObjects.Add(objectData.GetEntityID(), objectData);
                }
                else
                {
                    mVisibleObjects.Remove(objectData.GetEntityID());
                }
            }
            return changed;
        }

        public bool AddObjectData(IMapObjectData data)
        {
            bool ableToAdd = IsAbleToAdd(data);
            if (ableToAdd)
            {
                if (!mObjects.ContainsKey(data.GetEntityID()))
                {
                    mObjects.Add(data.GetEntityID(), data);
                    var mapData = map.data;
                    mapData.GetOrCreateModelTemplate(data.GetEntityID(), data.GetAssetPath(), false, false, map);

                    OnAddObjectData(data);

                    bool isActive = data.IsObjActive();
                    if (isActive)
                    {
                        if (mVisibleObjects.ContainsKey(data.GetEntityID()) == false)
                        {
                            mVisibleObjects.Add(data.GetEntityID(), data);
                        }
                    }
                    return true;
                }
                else
                {
                    Debug.Assert(false, "Object " + data.GetEntityID() + " is already added!");
                }
            }
            else
            {
                Debug.Assert(false, "Can't add object " + data.GetEntityID() + "!");
            }
            return false;
        }

        public bool RemoveObjectData(int objectDataID)
        {
            var data = GetObjectData(objectDataID);
            if (data != null)
            {
                Debug.Assert(data != null, objectDataID + " is not found!");

                OnRemoveObjectData(data);
                mObjects.Remove(objectDataID);
                mVisibleObjects.Remove(objectDataID);
                map.DestroyObject(data.GetEntityID());
                map.data.modelTemplateManager.SetObjectUsedModelTemplate(objectDataID, null);

                return true;
            }
            return false;
        }

        //get一个地图对象的数据
        public IMapObjectData GetObjectData(int objectID)
        {
            IMapObjectData val;
            mObjects.TryGetValue(objectID, out val);
            return val;
        }

        //设置对象的位置
        public void SetObjectPosition(int objectID, Vector3 newPos)
        {
            var objectData = GetObjectData(objectID);

            objectData.SetPosition(newPos);

            OnPositionChange(objectData);

            mTransformChangeEvent.Set(objectData, id);
            map.BroadcastMessage(mTransformChangeEvent);
        }

        //设置对象的scale
        public virtual void SetObjectScale(int objectID, Vector3 scale)
        {
            var objectData = GetObjectData(objectID);

            objectData.SetScale(scale);

            OnScaleChange(objectData);

            mTransformChangeEvent.Set(objectData, id);
            map.BroadcastMessage(mTransformChangeEvent);
        }

        public virtual void SetObjectRotation(int objectID, Quaternion rotation)
        {
            var objectData = GetObjectData(objectID);
            objectData.SetRotation(rotation);
            OnRotationChange(objectData);

            mTransformChangeEvent.Set(objectData, id);
            map.BroadcastMessage(mTransformChangeEvent);
        }

        public override bool Contains(int objectID)
        {
            return mObjects.ContainsKey(objectID);
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            if (mObjects.Count > 0)
            {
                foreach (var obj in mObjects)
                {
                    objects.Add(obj.Value);
                }
            }
        }

        public void AddModelLODGroup(ModelLODGroup group)
        {
            mGroupManager.AddGroup(group);
        }

        public void SetObjectScaleChangeCallback(System.Action<IMapObjectData> onObjectScaleChangeCallback)
        {
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
        }

        public void SetObjectActiveStateChangeCallback(System.Action<IMapObjectData, int> onObjectActiveStateChangeCallback)
        {
            mOnActiveStateChangeCallback = onObjectActiveStateChangeCallback;
        }

        public abstract IMapObjectData FindObjectAtPosition(Vector3 pos, float radius);
        public IMapObjectData FindObjectAtExactSamePosition(Vector3 pos)
        {
            foreach (var obj in mObjects)
            {
                if (Utils.Approximately(obj.Value.GetPosition(), pos))
                {
                    return obj.Value;
                }
            }
            return null;
        }

        public List<int> Resize(float newWidth, float newHeight)
        {
            mTileWidth = newWidth;
            mTileHeight = newHeight;
            mLayerOrigin = Vector3.zero;
            var layerBounds = new Rect(0, 0, newWidth, newHeight);
            List<int> removedObjects = new List<int>();
            foreach (var p in mObjects)
            {
                var obj = p.Value;
                var objBounds = obj.GetBounds();

                if (!layerBounds.Overlaps(objBounds))
                {
                    removedObjects.Add(obj.GetEntityID());
                }
            }

            for (int i = 0; i < removedObjects.Count; ++i)
            {
                RemoveObjectData(removedObjects[i]);
            }

            return removedObjects;
        }

        protected virtual void OnPositionChange(IMapObjectData objectData) { }
        protected virtual void OnRotationChange(IMapObjectData objectData) { }
        protected virtual void OnScaleChange(IMapObjectData objectData) { }

        protected abstract void OnAddObjectData(IMapObjectData objectData);
        protected abstract void OnRemoveObjectData(IMapObjectData objectData);
        protected abstract bool IsAbleToAdd(IMapObjectData objectData);

        public abstract void GetObjectInBounds(Bounds bounds, List<IMapObjectData> objects);

        public int objectCount { get { return mObjects.Count; } }
        public Dictionary<int, IMapObjectData> objects { get { return mObjects; } }
        public bool isLoading { set; get; }
        public ModelLODGroupManager lodGroupManager { get { return mGroupManager; } }

        //地图物体使用的lod group
        ModelLODGroupManager mGroupManager;
        int mFrameActionQueueIndex;
        protected bool mUseFrameAction = false;

        System.Action<IMapObjectData, int> mOnActiveStateChangeCallback;
        protected System.Action<IMapObjectData> mOnObjectScaleChangeCallback;

        protected Dictionary<int, IMapObjectData> mVisibleObjects = new Dictionary<int, IMapObjectData>();
        protected Dictionary<int, IMapObjectData> mObjects = new Dictionary<int, IMapObjectData>(1000);
        static GMMapObjectTransformChange mTransformChangeEvent = new GMMapObjectTransformChange();
    }
}
