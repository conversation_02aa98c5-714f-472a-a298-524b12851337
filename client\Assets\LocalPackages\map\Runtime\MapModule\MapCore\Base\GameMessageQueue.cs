﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System;
using System.Collections.Generic;

namespace TFW.Map {
    //地图内部使用的message
    public abstract class GameMessage {
        public abstract string type { get; }
    }

    public class GameMessageTemplate<T> : GameMessage
    where T : GameMessage {
        public GameMessageTemplate()
        {
            mType = typeof(T).Name;
        }

        public override string type {
            get { return mType; }
        }
        public static string staticType { get { return typeof(T).Name; } }
        string mType;
    }

    public delegate void GameMessageHandler(GameMessage e);

    //地图内部使用的message queue
    public class GameMessageQueue {
        [Flags]
        enum HandlerFlag {
            kInvalidHandler = 1,
        };
        class MessageHandler {
            public MessageHandler(object receiver, GameMessageHandler handler) {
                this.receiver = receiver;
                this.handler = handler;
            }
            public object receiver;
            public HandlerFlag flag = 0;
            public GameMessageHandler handler;
        };

        class DelayedMessageHandler {
            public DelayedMessageHandler(string eventType, object receiver, GameMessageHandler handler) {
                this.eventType = eventType;
                eventHandler = new MessageHandler(receiver, handler);
            }
            public string eventType;
            public MessageHandler eventHandler;
        };

        public bool RegisterGameMessageHandler(string msg, object receiver, GameMessageHandler handler) {
            if (mIsProcessingMessage != 0) {
                mDelayAddedHandlers.Add(new DelayedMessageHandler(msg, receiver, handler));
                return true;
            }
            else {
                List<MessageHandler> handlers;
                bool found = mHandlers.TryGetValue(msg, out handlers);
                if (found == false) {
                    handlers = new List<MessageHandler>();
                    mHandlers[msg] = handlers;
                }
                int n = handlers.Count;
                for (int i = 0; i < n; ++i) {
                    if (handlers[i].receiver == receiver) {
                        return false;
                    }
                }
                handlers.Add(new MessageHandler(receiver, handler));
                return true;
            }
        }

        public bool RemoveGameMessageHandler(string msg, object receiver) {
            if (mIsProcessingMessage > 0) {
                mDelayRemovedHandlers.Add(new DelayedMessageHandler(msg, receiver, null));
            }
            List<MessageHandler> handlers;
            bool found = mHandlers.TryGetValue(msg, out handlers);
            if (found) {
                for (int i = 0; i < handlers.Count; ++i) {
                    if (handlers[i].receiver == receiver) {
                        if (mIsProcessingMessage > 0) {
                            handlers[i].flag |= HandlerFlag.kInvalidHandler;
                        }
                        else {
                            handlers.RemoveAt(i);
                        }
                        return true;
                    }
                }
            }
            return false;
        }

        public void BroadcastGameMessage(GameMessage e, object receiver = null) {
            ++mIsProcessingMessage;
            List<MessageHandler> handlers;
            bool found = mHandlers.TryGetValue(e.type, out handlers);
            if (found) {
                if (receiver != null) {
                    int n = handlers.Count;
                    for (int i = 0; i < n; ++i) {
                        var h = handlers[i];
                        if (h.receiver == receiver) {
                            if ((h.flag & HandlerFlag.kInvalidHandler) == 0) {
                                h.handler(e);
                            }
                        }
                    }
                }
                else {
                    int n = handlers.Count;
                    for (int i = 0; i < n; ++i) {
                        if ((handlers[i].flag & HandlerFlag.kInvalidHandler) == 0) {
                            handlers[i].handler(e);
                        }
                    }
                }
            }
            --mIsProcessingMessage;
            if (mIsProcessingMessage == 0) {
                //no outer caller
                int n = mDelayAddedHandlers.Count;
                for (int i = 0; i < n; ++i) {
                    var d = mDelayAddedHandlers[i];
                    RegisterGameMessageHandler(d.eventType, d.eventHandler.receiver, d.eventHandler.handler);
                }
                mDelayAddedHandlers.Clear();
                n = mDelayRemovedHandlers.Count;
                for (int i = 0; i < n; ++i) {
                    var d = mDelayRemovedHandlers[i];
                    RemoveGameMessageHandler(d.eventType, d.eventHandler.receiver);
                }
                mDelayRemovedHandlers.Clear();
            }
        }

        Dictionary<string, List<MessageHandler>> mHandlers = new Dictionary<string, List<MessageHandler>>();
        List<DelayedMessageHandler> mDelayAddedHandlers = new List<DelayedMessageHandler>();
        List<DelayedMessageHandler> mDelayRemovedHandlers = new List<DelayedMessageHandler>();
        int mIsProcessingMessage = 0;
    };
}