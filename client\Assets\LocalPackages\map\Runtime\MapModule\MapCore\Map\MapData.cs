﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //地图上物体缩放和旋转的数据
    public class RotateScaleData
    {
        public RotateScaleData(Vector3 scale, Quaternion rotation)
        {
            this.scale = scale;
            this.rotation = rotation;
        }

        public Vector3 scale;
        public Quaternion rotation;
    }

    //地图的数据,内部使用,不暴露给lua接口
    public class MapData
    {
        public MapData(Map map, Vector3 viewCenter, float viewWidth, float viewHeight, float mapWidth, float mapHeight, float borderHeight, List<ModelTemplate> modelTemplates, Vector2 cameraHeightRange, MapLODManager lodManager, MapLocalObstacleManager obstacleManager, MapGlobalObstacleManager globalObstacleManager, MapCameraCollider collider, GridRegionSetting gridRegionSetting, bool isCircleMap, Version version, float backExtendedSize, float farClipOffset, bool useTerrainHeight, float groundTileSize, float frontTileSize, Bounds mapDataGenerationRange, List<string> pluginLayerNames, float maxCameraColliderHeight)
        {
            mPluginLayerNames = pluginLayerNames;
            mGroundTileSize = groundTileSize;
            mFrontTileSize = frontTileSize;
            mFarClipOffset = farClipOffset;
            mBackExtendedSize = backExtendedSize;
            mIsCircleMap = isCircleMap;
            mViewCenter = viewCenter;
            mMaxCameraColliderHeight = maxCameraColliderHeight;
            mModelTemplateManager = new ModelTemplateManager(map, modelTemplates);
            mMapWidth = mapWidth;
            mMapHeight = mapHeight;
            mMapBorderHeight = borderHeight;
            mLODManager = lodManager;
            mUseTerrainHeight = useTerrainHeight;

            mLocalObstacleManager = obstacleManager;
            mGlobalObstacleManager = globalObstacleManager;
            mCameraCollider = collider;
            mGridRegionSetting = gridRegionSetting;
            mVersion = version;
            mMapDataGenerationRange = mapDataGenerationRange;
            if (mMapDataGenerationRange.size == Vector3.zero)
            {
                mMapDataGenerationRange.SetMinMax(Vector3.zero, new Vector3(mapWidth, 0, mapHeight));
            }
        }

        public virtual void OnDestroy()
        {
            mModelTemplateManager.OnDestroy();
            mGlobalObstacleManager.OnDestroy();
            mLocalObstacleManager.OnDestroy();
            mCameraCollider?.OnDestroy();
        }

        public void MoveViewport(Vector3 offset)
        {
            mViewCenter.x += offset.x;
            mViewCenter.z += offset.z;
        }

        public void ReplaceModelTemplatePrefab(string oldPrefabPath, string newPrefabPath)
        {
            mModelTemplateManager.ReplaceModelTemplatePrefab(oldPrefabPath, newPrefabPath);
        }

        //创建或引用一个地图对象模板
        public ModelTemplate GetOrCreateModelTemplate(int objectDataID, string prefabPath, bool tileModelTemplate, bool preload, Map map)
        {
            return mModelTemplateManager.GetOrCreateModelTemplate(objectDataID, prefabPath, tileModelTemplate, preload, map);
        }

        public void GetOrCreateModelTemplateAsync(int objectDataID, string prefabPath, bool tileModelTemplate, bool preload, Map map, System.Action<ModelTemplate> action)
        {
            mModelTemplateManager.GetOrCreateModelTemplateAsync(objectDataID, prefabPath, tileModelTemplate, preload, map, action);
        }

        public void SetObjectUsedModelTemplate(int objectDataID, ModelTemplate modelTemplate)
        {
            mModelTemplateManager.SetObjectUsedModelTemplate(objectDataID, modelTemplate);
        }

        public void AddModelTemplate(ModelTemplate temp)
        {
            mModelTemplateManager.AddModelTemplate(temp);
            //also add children model templates
            int lods = temp.lodCount;
            for (int k = 0; k < lods; ++k)
            {
                var childrenModelTemplates = temp.GetChildrenModelTemplates(k);
                for (int i = 0; i < childrenModelTemplates.Count; ++i)
                {
                    mModelTemplateManager.AddModelTemplate(childrenModelTemplates[i]);
                }
            }
        }

        public ModelTemplate GetEntityModelTemplate(int objectDataID)
        {
            return mModelTemplateManager.GetEntityModelTemplate(objectDataID);
        }

        public List<ModelTemplate> GetUsedModelTemplates()
        {
            return mModelTemplateManager.GetUsedModelTemplates();
        }

        public void SetActive(bool active)
        {
            if (mCameraCollider != null)
            {
                mCameraCollider.SetActive(active);
            }
        }

        public virtual void Resize(float newWidth, float newHeight)
        {
            mMapWidth = newWidth;
            mMapHeight = newHeight;

            //if (mLocalObstacleManager != null)
            //{
            //    mLocalObstacleManager.OnDestroy();
            //}
            //mLocalObstacleManager = new MapLocalObstacleManager();

            //if (mGlobalObstacleManager != null)
            //{
            //    mGlobalObstacleManager.OnDestroy();
            //}
            //mGlobalObstacleManager = new MapGlobalObstacleManager();

            //mGridRegionSetting = new GridRegionSetting();
        }

        public float zoom { set { mZoom = value; } get { return mZoom; } }
        public float mapWidth { get { return mMapWidth; } }
        public float mapHeight { get { return mMapHeight; } }
        public float backExtendedSize { get { return mBackExtendedSize; } set { mBackExtendedSize = value; } }
        public Vector3 viewCenter { get { return mViewCenter; } }
        public Dictionary<int, ModelTemplate> modelTemplates { get { return mModelTemplateManager.modelTemplates; } }
        public ModelTemplateManager modelTemplateManager { get { return mModelTemplateManager; } }
        public MapLODManager lodManager { get { return mLODManager; } }
        //下一个地图对象的id,内部使用
        public int nextCustomObjectID
        {
            get
            {
                var id = mCustomID - 1;
                mCustomID--;
                return id;
            }

            set
            {
                mCustomID = value;
            }
        }
        public TerrainPrefabManager terrainPrefabManager { get { return mTerrainPrefabManager; } }
        public VaryingTileSizeTerrainPrefabManager varyingTileSizeTerrainPrefabManager { get { return mVaryingTileSizeTerrainPrefabManager; } }
        public TerrainPrefabManager splitFogPrefabManager { get { return mSplitFogPrefabManager; } }
        public MapLocalObstacleManager localObstacleManager { get { return mLocalObstacleManager; } }
        public MapGlobalObstacleManager globalObstacleManager { get { return mGlobalObstacleManager; } }
        public GridRegionSetting gridRegionSetting { get { return mGridRegionSetting; } }
        public float borderHeight { set { mMapBorderHeight = value; } get { return mMapBorderHeight; } }
        public float farClipOffset { set { mFarClipOffset = value; } get { return mFarClipOffset; } }
        public float maxCameraColliderHeight { set { mMaxCameraColliderHeight = value; } get { return mMaxCameraColliderHeight; } }
        public bool isCircleMap { get { return mIsCircleMap; } set { mIsCircleMap = value; } }
        public bool useTerrainHeight { get { return mUseTerrainHeight; } }
        public Version version { get { return mVersion; } }
        public float groundTileSize { get { return mGroundTileSize; } set { mGroundTileSize = value; } }
        public float frontTileSize { get { return mFrontTileSize; } set { mFrontTileSize = value; } }
        public Bounds mapDataGenerationRange { get { return mMapDataGenerationRange; } set { mMapDataGenerationRange = value; } }
        public CameraClampInRegion lookAtAreaClamp { get { return mLookAtAreaClamp; } set { mLookAtAreaClamp = value; } }
        public List<string> pluginLayerNames { get { return mPluginLayerNames; } }

        int mCustomID = 0;
        //地图视野的中心点
        Vector3 mViewCenter;
        bool mIsCircleMap;
        //是否使用地图高度
        bool mUseTerrainHeight = false;
        //缩放值
        float mZoom = -1.0f;
        float mMapWidth;
        float mMapHeight;
        float mMapBorderHeight;
        float mFarClipOffset;
        float mFrontTileSize;
        float mGroundTileSize;
        float mMaxCameraColliderHeight;
        //把viewport往z方向扩大一圈,应对很高的山被裁剪的情况
        float mBackExtendedSize = 60;
        //lod层级
        MapLODManager mLODManager;
        //地图上实体使用的prefab配置管理器
        ModelTemplateManager mModelTemplateManager;
        //地表使用的prefab管理器
        TerrainPrefabManager mTerrainPrefabManager = new TerrainPrefabManager(MapModule.useOnly15Prefab);
        VaryingTileSizeTerrainPrefabManager mVaryingTileSizeTerrainPrefabManager = new VaryingTileSizeTerrainPrefabManager();
        TerrainPrefabManager mSplitFogPrefabManager = new TerrainPrefabManager(true);
        //管理地图的障碍物数据
        MapLocalObstacleManager mLocalObstacleManager;
        MapGlobalObstacleManager mGlobalObstacleManager;
        MapCameraCollider mCameraCollider;
        GridRegionSetting mGridRegionSetting;
        CameraClampInRegion mLookAtAreaClamp;
        List<string> mPluginLayerNames;
        //地图的版本号
        Version mVersion;
        //编辑器中生成地图数据都在这个范围内
        Bounds mMapDataGenerationRange;
    };
}
