﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void DrawLineFillToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.alt)
            {
                return;
            }

            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mSetStartPoint)
                {
                    mStartPos = worldPos;
                    mEndPos = worldPos;
                    mSetStartPoint = false;
                }
                else
                {
                    mEndPos = worldPos;
                    FillLineObject();
                    mSetStartPoint = true;
                }
            }
            else if (e.type == EventType.MouseMove)
            {
                mDisplayEndPos = worldPos;
            }
            else if (e.type == EventType.MouseUp && e.button == 1)
            {
                if (mSetStartPoint == false)
                {
                    //cancel
                    mSetStartPoint = true;
                }
            }

            if (!mSetStartPoint)
            {
                Handles.DrawLine(mStartPos, mDisplayEndPos);
            }
        }

        void RegenerateObjectsInLine()
        {
            Undo.PerformUndo();
            FillLineObject();
        }

        void FillLineObject()
        {
            if (mPrefabManager.selectedPrefab != null)
            {
                var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();

                List<Vector3> positions = null;
                if (mUseSameDeltaDistance)
                {
                    positions = GenerateSameDeltaDistancePositions();
                }
                else
                {
                    positions = GenerateLineEdgePoints();
                }
                for (int i = 0; i < positions.Count; ++i)
                {
                    var prefab = mPrefabManager.selectedPrefab;
                    CreateObject(positions[i], prefab, stage.prefabContentsRoot.transform);
                }
            }
        }

        List<Vector3> GenerateSameDeltaDistancePositions()
        {
            List<Vector3> positions = new List<Vector3>();
            var dir = mEndPos - mStartPos;
            float length = dir.magnitude;
            dir.Normalize();
            int count = Mathf.FloorToInt(length / mMinDistance);
            for (int i = 0; i < count; ++i)
            {
                var point = mStartPos + (mMinDistance * i) * dir;
                positions.Add(point);
            }
            return positions;
        }

        List<Vector3> GenerateLineEdgePoints()
        {
            int tryCount = 500;
            List<Vector3> points = new List<Vector3>();

            for (int i = 0; i < mFillCount; ++i)
            {
                for (int t = 0; t < tryCount; ++t)
                {
                    var dir = mEndPos - mStartPos;
                    float distance = UnityEngine.Random.Range(0.0f, 1.0f);
                    var point = mStartPos + distance * dir;
                    dir.Normalize();
                    var perp = new Vector3(dir.z, 0, -dir.x);
                    float halfEdgeSize = mEdgeSize * 0.5f;
                    float perpDistance = UnityEngine.Random.Range(-halfEdgeSize, halfEdgeSize);
                    point += perp * perpDistance;

                    if (IsValid(point, points, mMinDistance))
                    {
                        points.Add(point);
                        break;
                    }
                }
            }

            return points;
        }

        bool mSetStartPoint = true;
        bool mUseSameDeltaDistance = false;
        Vector3 mStartPos;
        Vector3 mEndPos;
        Vector3 mDisplayEndPos;
    }
}


#endif