﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveModel : EditorAction
    {
        public ActionRemoveModel(int objectID, int layerID)
        {
            var obj = Map.currentMap.FindObject(objectID) as ModelData;
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = obj.GetModelTemplateID();
            mRotation = obj.GetRotation();
            mScale = obj.GetScale();
            mPosition = obj.GetPosition();
            var modelTemplate = Map.currentMap.FindObject(mModelTemplateID) as ModelTemplate;
            var prefabName = Path.GetFileName(modelTemplate.GetLODPrefabPath(0));
            mDescription = string.Format("remove {0}", prefabName);
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as ModelLayer;
            if (layer != null)
            {
                layer.RemoveObject(mObjectID);
                return true;
            }

            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                var layer = map.GetMapLayerByID(mLayerID) as ModelLayer;
                if (layer != null)
                {
                    layer.AddObject(modelTemplate.GetLODPrefabPath(0), mPosition, mRotation, mScale, mObjectID);
                    return true;
                }
            }
            return false;
        }

        public override string description { get { return mDescription; } }

        protected int mLayerID;
        protected int mObjectID;
        protected int mModelTemplateID;
        protected Quaternion mRotation;
        protected Vector3 mScale;
        protected Vector3 mPosition;
        protected string mDescription;
    }
}

#endif