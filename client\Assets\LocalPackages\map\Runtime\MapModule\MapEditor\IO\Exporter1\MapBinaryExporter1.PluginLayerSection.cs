﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEditor.VersionControl;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SavePluginLayerList(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.PluginLayerList, writer);
            //版本号
            writer.Write(VersionSetting.PluginLayerListStructVersion);

            //-----------------version 1 start------------------------------
            List<string> pluginLayerNames = GetPluginLayerList();
            writer.Write(pluginLayerNames.Count);
            for (int i = 0; i < pluginLayerNames.Count; ++i)
            {
                Utils.WriteString(writer, pluginLayerNames[i]);
            }
            //-----------------version 1 end------------------------------
        }

        List<string> GetPluginLayerList()
        {
            List<string> pluginLayerNames = new List<string>();
            var map = Map.currentMap;
            int layerCount = map.GetMapLayerCount();
            for (int i = 0; i < layerCount; ++i)
            {
                var layer = map.GetMapLayerByIndex(i);
                if (MapPlugin.IsPluginLayer(layer.name))
                {
                    string runtimeLayerName = MapPlugin.GetRuntimeLayerName(layer.name);
                    pluginLayerNames.Add(runtimeLayerName);
                }
            }
            return pluginLayerNames;
        }
    }
}

#endif