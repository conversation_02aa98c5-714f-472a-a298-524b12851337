﻿ 



 
 

﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class LODFade : MonoBehaviour
    {
        public FadeConfig config;
        Material objectMaterial;
        Renderer[] mRenderers;
        BuildingLODManager mLODManager;

        void OnDestroy()
        {
            if (mLODManager != null)
            {
                mLODManager.RemoveFadeObject(this);
            }
        }

        public void Init(BuildingLODManager lodManager)
        {
            mLODManager = lodManager;
            mLODManager.AddFadeObject(this);
            if (mRenderers == null)
            {
                mRenderers = gameObject.GetComponentsInChildren<Renderer>();
            }
        }

        public void UpdateAlpha()
        {
            if (config != null && mRenderers != null)
            {
                float cameraHeight = MapCameraMgr.currentCameraHeight;
                float t = (cameraHeight - config.minCameraHeight) / (config.maxCameraHeight - config.minCameraHeight);
                t = Mathf.Clamp01(t);

                float alpha = Mathf.Lerp(1, config.minAlpha, t);

                bool castShadows = false;
                if (alpha >= 0.6f)
                {
                    castShadows = true;
                }
                float zWrite = 1.0f;
                if (alpha < 0.1f)
                {
                    zWrite = 0.0f;
                }
                for (int i = 0; i < mRenderers.Length; ++i)
                {
                    if (mRenderers[i].sharedMaterial != null)
                    {
                        mRenderers[i].sharedMaterial.SetFloat("_FadeAlpha", alpha);
                        mRenderers[i].sharedMaterial.SetFloat("_ZWrite", zWrite);
                        mRenderers[i].shadowCastingMode = castShadows ? UnityEngine.Rendering.ShadowCastingMode.On : UnityEngine.Rendering.ShadowCastingMode.Off;
                    }
                }
            }
        }
    }
}
