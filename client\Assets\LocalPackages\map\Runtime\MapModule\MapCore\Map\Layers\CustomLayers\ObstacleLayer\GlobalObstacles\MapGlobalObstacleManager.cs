﻿ 



 
 


using System.Collections;
using UnityEngine;
using System.Collections.Generic;

using System.Threading.Tasks;

namespace TFW.Map
{
    /// <summary>
    /// 管理地图的全局障碍物数据
    /// </summary>
    public partial class MapGlobalObstacleManager
    {
        public MapGlobalObstacleManager(string name, Map map, float mapWidth, float mapHeight, bool isCircleMap,
            string obstacleMaterialPath, Vector3[] obstacleVertices, int[] obstacleIndices, byte[] gridObstacles,
            float gridSize)
        {
            mMap = map;
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -2");
            mIsCircleMap = isCircleMap;
            mMapWidth = mapWidth;
            mMapHeight = mapHeight;
            mObstacleViewMaterialPath = obstacleMaterialPath;
            mName = name;

            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -3");
            if (obstacleVertices != null)
            {
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -4");
                mQuadTree = new TriangleQuadTree();
                mQuadTree.Create(mName, Vector2.zero, new Vector2(mapWidth, mapHeight), obstacleVertices, obstacleIndices, 0,
                    obstacleIndices != null ? obstacleIndices.Length - 1 : 0, 10, false, false, false);
            }
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -6");
            InitGridObstacles(gridObstacles, gridSize);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -7");
            if (obstacleVertices != null)
            {
                CreateObstacleView(obstacleMaterialPath, obstacleVertices, obstacleIndices);
            }
        }

        public MapGlobalObstacleManager(string name, Map map, float mapWidth, float mapHeight, bool isCircleMap,
            string obstacleMaterialPath)
        {
            mMap = map;
            mIsCircleMap = isCircleMap;
            mMapWidth = mapWidth;
            mMapHeight = mapHeight;
            mObstacleViewMaterialPath = obstacleMaterialPath;
            mName = name;
        }

        public IEnumerator InitAsync(Vector3[] obstacleVertices, int[] obstacleIndices, byte[] gridObstacles,
            float gridSize)
        {
            if (obstacleVertices != null)
            {
                var task = Task.Run(() => { CreateQuadTree(obstacleVertices, obstacleIndices); });
                yield return new WaitUntil(() => task.IsCompleted);
            }

            InitGridObstacles(gridObstacles, gridSize);
            if (obstacleVertices != null)
            {
                CreateObstacleView(obstacleMaterialPath, obstacleVertices, obstacleIndices);
            }
        }

        private void CreateQuadTree(Vector3[] obstacleVertices, int[] obstacleIndices)
        {
            mQuadTree = new TriangleQuadTree();
            mQuadTree.Create(mName, Vector2.zero, new Vector2(mMapWidth, mMapHeight), obstacleVertices,
                obstacleIndices, 0,
                obstacleIndices != null ? obstacleIndices.Length - 1 : 0, 10, false, true, false);
        }

        public void OnDestroy()
        {
            DestroyTexturePlane();
            if (mObstacleMaterial != null)
            {
                if (mMap.isEditorMode)
                {
                    Object.DestroyImmediate(mObstacleMaterial);
                }
                else
                {
                    Object.Destroy(mObstacleMaterial);
                }

                mObstacleMaterial = null;
            }

            mQuadTree = null;
            Utils.DestroyObject(mObstacleObject);
            mObstacleObject = null;
        }

        //在编辑器中创建数据,在游戏中加载
        //创建障碍物数据,对每个prefab创建一个障碍物数据,注意创建障碍物时必须在lod0上,考虑在编辑器中禁止lod的改变
        public NavMeshBlock[] CreateObstacles(NavigationCreateMode obstacleMode, LayerTypeMask typeMask = LayerTypeMask.kCollisionLayer | LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kRailwayLayer, bool findOtherPrefabOutlines = true)
        {
#if UNITY_EDITOR
            //create a obstacles mesh first
            var mapWidth = mMap.mapWidth;
            var mapHeight = mMap.mapHeight;
            var map = mMap as EditorMap;
            var meshies = Utils.CreateNavMesh(typeMask,
                    1, 1, PrefabOutlineType.ObjectPlacementObstacle, false, obstacleMode, false, true, findOtherPrefabOutlines, map.removeSameHoles);
            //create a quad tree from the mesh
            mQuadTree = new TriangleQuadTree();
            Vector3[] vertices = null;
            int[] indices = null;
            if (meshies != null && meshies[0] != null)
            {
                vertices = meshies[0].vertices;
                indices = meshies[0].indices;
                if (vertices != null)
                {
                    mQuadTree.Create(mName, Vector2.zero, new Vector2(mapWidth, mapHeight), vertices, indices, 0,
                        indices != null ? indices.Length - 1 : 0, 10, false, false, false);
                    CreateObstacleView(mObstacleViewMaterialPath, vertices, indices);
                }
                else
                {
                    GameObject.DestroyImmediate(mObstacleObject);
                    mObstacleObject = null;
                }
            }

            return meshies;
#else
            return null;
#endif
        }

        public bool IsPointInObstacle(float x, float z)
        {
            if (mIsCircleMap)
            {
                double dx = x - mMapWidth * 0.5f;
                double dz =  z- mMapHeight * 0.5f;
                double validRadius = mMapWidth * 0.5f;
                double dis2 = dx * dx + dz * dz;
                if (dis2 > validRadius * validRadius)
                {
                    return true;
                }
            }
            else
            {
                if (x > mMapWidth || x < 0 || z > mMapHeight ||
                    z < 0)
                {
                    //out of map bounds
                    return true;
                }
            }

            if (mQuadTree != null)
            {
                return mQuadTree.IsTriangleIntersectedWithPoint(x, z);
            }

            Debug.LogError("Create Obstacles first!");
            return false;
        }

        //check if a circle is intersected with any obstacle
        public bool IsIntersectedWithObstacles(float centerX, float centerZ, float radius)
        {
            if (mIsCircleMap)
            {
                double dx = centerX - mMapWidth * 0.5f;
                double dz = centerZ - mMapHeight * 0.5f;
                double validRadius = mMapWidth * 0.5f - radius;
                double dis2 = dx * dx + dz * dz;
                if (dis2 > validRadius * validRadius)
                {
                    return true;
                }
            }
            else
            {
                if (centerX > mMapWidth - radius || centerX < radius || centerZ > mMapHeight - radius ||
                    centerZ < radius)
                {
                    //out of map bounds
                    return true;
                }
            }

            if (mQuadTree != null)
            {
                return mQuadTree.IsTriangleIntersectedWithCircle(centerX, centerZ, radius);
            }

            return false;
        }

        //check if a convex polygon intersected with map obstacles
        public bool IsIntersectedWithObstacles(List<Vector3> convexPolygons, Rect polygonBounds)
        {
            if (mQuadTree != null)
            {
                return mQuadTree.IsTriangleIntersectedWithConvexPolygon(convexPolygons, polygonBounds);
            }

            return false;
        }

        void CreateObstacleView(string mtlPath, Vector3[] vertices, int[] indices)
        {
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -8");
            MeshFilter filter = null;
            MeshRenderer renderer = null;
            if (mObstacleObject == null)
            {
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -9");
                mObstacleObject = new GameObject("Global Obstacle View");
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -10");
                Utils.HideGameObject(mObstacleObject);
               // UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -11");
                mObstacleObject.SetActive(false);
                mObstacleObject.transform.SetParent(mMap.root.transform);
                renderer = mObstacleObject.AddComponent<MeshRenderer>();
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -14");
                filter = mObstacleObject.AddComponent<MeshFilter>();
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -15");
            }
            else
            {
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -16");
                filter = mObstacleObject.GetComponent<MeshFilter>();
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -17");
                renderer = mObstacleObject.GetComponent<MeshRenderer>();
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -18");
                GameObject.DestroyImmediate(filter.sharedMesh);
                //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -19");
            }


            int submeshIndexCount = 30000;
            Mesh mesh = new Mesh();
            mesh.vertices = vertices;
            mesh.indexFormat = UnityEngine.Rendering.IndexFormat.UInt32;
            int k = 0;
            int s = 0;
            List<int[]> allSubmeshIndices = new List<int[]>();
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -20");
            while (true)
            {
                int left = indices.Length - k;
                int indexCount = Mathf.Min(left, submeshIndexCount);
                int[] submeshIndices = new int[indexCount];
                for (int h = 0; h < indexCount; ++h)
                {
                    submeshIndices[h] = indices[h + k];
                }

                allSubmeshIndices.Add(submeshIndices);
                k += indexCount;

                if (left <= submeshIndexCount)
                {
                    break;
                }

                ++s;
            }
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -21");

            mesh.subMeshCount = allSubmeshIndices.Count;
            for (int i = 0; i < allSubmeshIndices.Count; ++i)
            {
                mesh.SetIndices(allSubmeshIndices[i], MeshTopology.Triangles, i);
            }
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -22");
            if (string.IsNullOrEmpty(mtlPath))
            {
                mtlPath = MapModule.defaultObstacleMaterial;
            }
           // UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -23 {mtlPath}");

            Material mtl = null;
//#if UNITY_WEBGL
            if (!string.IsNullOrEmpty(mtlPath))
            {
                MapModuleResourceMgr.LoadMaterialAsync(mtlPath, null, (b, result)=>
                {
                    mtl = result;
                    if (mtl == null)
                    {
                        if (mObstacleMaterial == null)
                        {
                            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -23 -1 {mtlPath}");
                            mObstacleMaterial = new Material(Shader.Find("Unlit/Transparent"));
                            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -23 -2 {mtlPath}");
                        }
                        mtl = mObstacleMaterial;
                    }
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -24");
                    Material[] materials = new Material[allSubmeshIndices.Count];
                    for (int i = 0; i < materials.Length; ++i)
                    {
                        materials[i] = mtl;
                    }
                    renderer.sharedMaterials = materials;
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -25");
                    var bounds = mesh.bounds;
                    var min = bounds.min;
                    var size = bounds.size;
                    int n = vertices.Length;
                    Vector2[] uvs = new Vector2[n];
                    for (int i = 0; i < n; ++i)
                    {
                        float u = (vertices[i].x - min.x);
                        float v = (vertices[i].z - min.z);
                        uvs[i] = new Vector2(u, v);
                    }
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -26");
                    mesh.uv = uvs;
                    filter.sharedMesh = mesh;

                    if (gridSize > 0)
                    {
                        if (Map.currentMap.isEditorMode)
                        {
                            CreateTexturePlane();
                        }
                    }
                    //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -6 -6 -26");
                });
            }
//#else
//            if (!string.IsNullOrEmpty(mtlPath))
//            {
//                mtl = MapModuleResourceMgr.LoadMaterial(mtlPath.ToLower());
//            }

//            if (mtl == null)
//            {
//                if (mObstacleMaterial == null)
//                {
//                    mObstacleMaterial = new Material(Shader.Find("K1/Unlit/Transparent"));
//                }

//                mtl = mObstacleMaterial;
//            }

//            Material[] materials = new Material[allSubmeshIndices.Count];
//            for (int i = 0; i < materials.Length; ++i)
//            {
//                materials[i] = mtl;
//            }

//            renderer.sharedMaterials = materials;

//            var bounds = mesh.bounds;
//            var min = bounds.min;
//            var size = bounds.size;
//            int n = vertices.Length;
//            Vector2[] uvs = new Vector2[n];
//            for (int i = 0; i < n; ++i)
//            {
//                float u = (vertices[i].x - min.x);
//                float v = (vertices[i].z - min.z);
//                uvs[i] = new Vector2(u, v);
//            }

//            mesh.uv = uvs;
//            filter.sharedMesh = mesh;

//            if (gridSize > 0)
//            {
//                if (mMap.isEditorMode)
//                {
//                    CreateTexturePlane();
//                }
//            }
//#endif
        }

        public GameObject GetObstacleView()
        {
            return mObstacleObject;
        }

        public bool IsGlobalObstaclesVisible
        {
            get
            {
                if (mObstacleObject != null)
                {
                    return mObstacleObject.activeSelf;
                }

                return false;
            }
            set
            {
                if (mObstacleObject != null)
                {
                    mObstacleObject.SetActive(value);
                }
            }
        }

        public Mesh obstacleMesh
        {
            get
            {
                if (mObstacleObject != null)
                {
                    return mObstacleObject.GetComponent<MeshFilter>().sharedMesh;
                }

                return null;
            }
        }

        public string obstacleMaterialPath
        {
            get { return mObstacleViewMaterialPath; }
        }

        TriangleQuadTree mQuadTree;
        GameObject mObstacleObject;
        Material mObstacleMaterial;
        float mMapWidth;
        float mMapHeight;
        string mObstacleViewMaterialPath;
        bool mIsCircleMap;
        Map mMap;
        string mName;
    }
}
