﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [Black]
    public abstract class TileView : MapObjectViewBase {
        public TileView(MapLayerView layerView) {
            mLayerView = layerView;
        }
        public override void OnDestroy() {
            if (mGameObject != null) {
                Utils.DestroyObject(mGameObject);
                mGameObject = null;
            }
        }
        public void SetActive(bool active) {
            if (mGameObject != null) {
                mGameObject.SetActive(active);
            }
        }
        public GameObject gameObject {
            get { return mGameObject; }
        }
        public Transform transform {
            get {
                if (mGameObject != null) {
                    return mGameObject.transform;
                }
                return null;
            }
        }

        protected GameObject mGameObject;
        protected MapLayerView mLayerView;
    }
}

#endif