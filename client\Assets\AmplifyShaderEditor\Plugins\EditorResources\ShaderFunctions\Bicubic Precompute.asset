%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Bicubic Precompute
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18600\n0;537;1729;822;2496.342;532.7923;2.001278;True;False\nNode;AmplifyShaderEditor.FunctionInput;5;-931.3976,161.7528;Inherit;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;55;-937.6257,292.313;Inherit;False;Texel
    Size;4;1;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;2;-712.9393,184.7253;Inherit;False;UV
    = UV * TexelSize.zw - 0.5@$float2 f = frac( UV )@$UV -= f@$$float4 xn = float4(
    1.0, 2.0, 3.0, 4.0 ) - f.xxxx@$float4 yn = float4( 1.0, 2.0, 3.0, 4.0 ) - f.yyyy@$$float4
    xs = xn * xn * xn@$float4 ys = yn * yn * yn@$$float3 xv = float3( xs.x, xs.y -
    4.0 * xs.x, xs.z - 4.0 * xs.y + 6.0 * xs.x )@$float3 yv = float3( ys.x, ys.y -
    4.0 * ys.x, ys.z - 4.0 * ys.y + 6.0 * ys.x )@$float4 xc = float4( xv.xyz, 6.0
    - xv.x - xv.y - xv.z )@$float4 yc = float4( yv.xyz, 6.0 - yv.x - yv.y - yv.z )@$$float4
    c = float4( UV.x - 0.5, UV.x + 1.5, UV.y - 0.5, UV.y + 1.5 )@$float4 s = float4(
    xc.x + xc.y, xc.z + xc.w, yc.x + yc.y, yc.z + yc.w )@$$float w0 = s.x / ( s.x
    + s.y )@$float w1 = s.z / ( s.z + s.w )@$$Offsets = ( c + float4( xc.y, xc.w,
    yc.y, yc.w ) / s ) * TexelSize.xyxy@$Weights = float2( w0, w1 )@$;7;False;4;True;UV;FLOAT2;0,0;In;;Float;False;True;TexelSize;FLOAT4;0,0,0,0;In;;Inherit;False;True;Offsets;FLOAT4;0,0,0,0;Out;;Float;False;True;Weights;FLOAT2;0,0;Out;;Float;False;Stochastic
    Tiling;False;False;0;5;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT2;0,0;False;3;FLOAT;0;FLOAT4;4;FLOAT2;5\nNode;AmplifyShaderEditor.FunctionOutput;34;-379.6704,177.7769;Inherit;False;True;-1;Offsets;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;54;-378.9186,285.9598;Inherit;False;False;-1;Weights;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nWireConnection;2;1;5;0\nWireConnection;2;2;55;0\nWireConnection;34;0;2;4\nWireConnection;54;0;2;5\nASEEND*/\n//CHKSM=83289324BA5D4E43B3BB2E090095EB3689FE9CE1"
  m_functionName: 
  m_description: 'Precomputation step (1 of 2) for bicubic sampling.


    IMPORTANT

    This node only precomputes data that is fed into the "Bicubic Sample" nodes using
    Fetch sampling mode. All textures sharing the same precomputation step must also
    share the same size and UV coordinates. '
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 12
  m_customNodeCategory: Rust
  m_previewPosition: 0
  m_hidden: 0
