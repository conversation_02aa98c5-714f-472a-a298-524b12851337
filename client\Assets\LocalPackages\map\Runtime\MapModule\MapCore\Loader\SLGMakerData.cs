﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    namespace config
    {
        //地图的导入数据
        public class BaseObject
        {
            public BaseObject(int id)
            {
                SetID(id);
            }

            public BaseObject() { }

            public void SetID(int id)
            {
                if (mAllObjects.ContainsKey(id) == false)
                {
                    mAllObjects.Add(id, this);
                }
                mID = id;
            }

            public static BaseObject FindObject(int id)
            {
                BaseObject obj;
                mAllObjects.TryGetValue(id, out obj);
                return obj;
            }

            public static void Clear()
            {
                mAllObjects.Clear();
            }

            public int id { get { return mID; } }

            int mID;

            static Dictionary<int, BaseObject> mAllObjects = new Dictionary<int, BaseObject>();
        }

        public class NamedObject : BaseObject
        {
            public NamedObject(int id, string name) : base(id)
            {
                this.name = name;
            }
            public NamedObject() { }
            public string name;
        }

        public class BackgroundSetting
        {
            public Vector3 position = new Vector3(0, -2, 0);
            public Vector3 scale = Vector3.one;
            public string texturePath = "";
        }

        public class EditorSetting
        {
            public EditorSetting()
            {
                cameraMoveRange = new Vector2(1, 500);
            }
            public Vector2 cameraMoveRange;
            public string dataFolder;
            public string exportFolder = "";
            public string riverMaterialFolder;
            public bool saveRiverMaterials = false;
        }

        public class MapLayerLODConfigItem
        {
            public MapLayerLODConfigItem(string name, float zoom, float zoomThreshold, bool hideObject, int shaderLOD, bool useRenderTexture, MapLayerLODConfigFlag flag, int terrainLODTileCount)
            {
                changeZoom = zoom;
                changeZoomThreshold = zoomThreshold;
                this.name = name;
                this.hideObject = hideObject;
                this.shaderLOD = shaderLOD;
                this.useRenderTexture = useRenderTexture;
                this.flag = flag;
                this.terrainLODTileCount = terrainLODTileCount;
            }
            public float changeZoom = 0;
            public float changeZoomThreshold = 0.1f;
            public bool hideObject = false;
            public int shaderLOD;
            public bool useRenderTexture = false;
            public MapLayerLODConfigFlag flag;
            public string name;
            public int terrainLODTileCount;
        }

        public class MapLayerLODConfig
        {
            public MapLayerLODConfig()
            {
                lodConfigs = new MapLayerLODConfigItem[1];
                lodConfigs[0] = new MapLayerLODConfigItem(TFW.Map.MapLayerLODConfig.GetDefaultLODName(0), 0, 0.1f, false, 100, false, MapLayerLODConfigFlag.None, 0);
            }

            public MapLayerLODConfigItem[] lodConfigs;
        }

        public class MapObjectData : NamedObject
        {
            public MapObjectData()
            {
                rotation = Quaternion.identity;
                scale = Vector3.one;
                flag = 0;
            }

            public int flag;
            public Vector3 position;
            public Vector3 scale;
            public Quaternion rotation;
        }

        public class GridMapObjectData : MapObjectData
        {
            public bool isDefaultPosition = false;
        }

        public class GridModelData : GridMapObjectData
        {
            public int modelTemplateID;
        };

        public class ComplexGridModelData : GridModelData
        {
            public ushort occupiedGridCount;
            public string objectTag = "";
            public bool useRenderTextureModel = false;
        };

        public class MapCollisionData : NamedObject
        {
            public List<Vector2> navMeshObstacleOutlines = new List<Vector2>();
            public List<Vector2> objectPlacementOutlines = new List<Vector2>();

            public float radius = 1.0f;
            public bool isExtandable = false;
            public CollisionAttribute attribute;
            public int type;
        }

        public class RegionData : NamedObject
        {
            public List<Vector3> outline = new List<Vector3>();
            public Vector3[] meshVertices;
            public int[] meshIndices;
            public Color[] vertexColors;
            public Color innerColor = new Color(0.5f, 0.5f, 0.5f, 0.6f);
            public Color outerColor = new Color(1, 0, 0, 0.6f);
            public RegionType type = RegionType.Outer;
            public int number = 0;
            public string materialGuid;
        }

        public class PolygonRiverSectionData
        {
            public List<Vector3> outline = new List<Vector3>();
            public Color[] textureData;
        }

        public class PolygonRiverSplitterData
        {
            public Vector3 startPos;
            public Vector3 endPos;
        }

        public class PolygonRiverData : NamedObject
        {
            public List<Vector2> outline = new List<Vector2>();
            public PolygonRiverSectionData[] sections;
            public PolygonRiverSplitterData[] splitters;

            public string materialPath;
            public int textureSize = 256;
            public float radius = 1.0f;
            public int hideLOD = -1;
            public float height = 0;
            public bool generateRiverMaterial = MapModule.generateRiverMaterial;
        }

        public class CameraColliderData : NamedObject
        {
            public List<Vector2> bottomOutline = new List<Vector2>();
            public List<Vector2> topOutline = new List<Vector2>();
            public float radius = 1.0f;
            public float height = 0;
            public Vector3[] vertices;
            public int[] indices;
        }

        public class MapLayerData : NamedObject
        {
            public MapLayerData(int layerID, string name, Vector3 origin, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager) : base(layerID, name)
            {
                this.origin = origin;
                this.config = config;
                this.lodGroupManager = lodGroupManager;
            }
            public Vector3 origin;
            public MapLayerLODConfig config;
            public ModelLODGroupManager lodGroupManager;
            public bool active = true;
        }

        public class LODLayerData : MapLayerData
        {
            public LODLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float mapWidth, float mapHeight) : base(layerID, name, offset, config, null)
            {
                this.mapWidth = mapWidth;
                this.mapHeight = mapHeight;
            }
            public float mapWidth;
            public float mapHeight;
        }

        public class GridObjectLayerData : MapLayerData
        {
            public GridObjectLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, GridMapObjectData[] objects) : base(layerID, name, offset, config, null)
            {
                this.objects = objects;
                this.zTileCount = rows;
                this.xTileCount = cols;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.gridType = gridType;
            }
            public GridMapObjectData[] objects;
            public float tileWidth;
            public float tileHeight;
            public int xTileCount;
            public int zTileCount;
            public GridType gridType;
        };

        public class GridModelLayerData : GridObjectLayerData
        {
            public GridModelLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, GridMapObjectData[] models) : base(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, gridType, models)
            {
            }
        };

        public class GridModelLayerData2 : GridObjectLayerData
        {
            public GridModelLayerData2(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, bool[] tileIsNotEmpty, Rect realLayerBounds, bool enableObjectMaterialChange) : base(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, gridType, null)
            {
                this.tileIsNotEmpty = tileIsNotEmpty;
                this.realLayerBounds = realLayerBounds;
                this.enableObjectMaterialChange = enableObjectMaterialChange;
            }

            public bool[] tileIsNotEmpty;
            public Rect realLayerBounds;
            public bool enableObjectMaterialChange;
        };

        public class ComplexGridModelLayerData : GridObjectLayerData
        {
            public ComplexGridModelLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, GridType gridType, List<GridMapObjectData[]> objectsInLODs, bool exportData, Vector3 localViewCenter, Vector2 localViewSize, ObjectTagSetting[] objectTags, DecorationObjectPlacementSetting objectPlacementSetting, Rect realLayerBounds, bool enableObjectMaterialChange, bool enableCullIntersectedObjects) : base(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, gridType, null)
            {
                this.objectsInLODs = objectsInLODs;
                this.exportData = exportData;
                this.localViewCenter = localViewCenter;
                this.localViewSize = localViewSize;
                this.objectTags = objectTags;
                this.objectPlacementSetting = objectPlacementSetting;
                this.realLayerBounds = realLayerBounds;
                this.enableObjectMaterialChange = enableObjectMaterialChange;
                this.enableCullIntersectedObjects = enableCullIntersectedObjects;
            }

            public bool exportData;
            public bool enableObjectMaterialChange;
            public bool enableCullIntersectedObjects;
            public ObjectTagSetting[] objectTags;
            public List<GridMapObjectData[]> objectsInLODs;
            public Vector3 localViewCenter;
            public Vector2 localViewSize;
            public Rect realLayerBounds;
            public DecorationObjectPlacementSetting objectPlacementSetting;
        };

        public class MapObjectLayerData : MapLayerData
        {
            public MapObjectLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager, float width, float height, MapObjectData[] objects) : base(layerID, name, offset, config, lodGroupManager)
            {
                this.objects = objects;
                this.width = width;
                this.height = height;
            }
            public MapObjectData[] objects;
            public float width;
            public float height;
        }

        public class ModelData : MapObjectData
        {
            public int modelTemplateID;
        };

        public class RiverData : MapObjectData
        {
            public int modelTemplateID;
            public int hideLOD = -1;
        };

        public class RailObjectData : ModelData
        {
            public RailObjectType type = RailObjectType.Rail;
            public int groupID = 0;
            public bool isGroupLeader = false;
            public int railIndex = 0;
            public int segmentIndex = -1;
        };

        public class CircleBorderData : ModelData
        {
            public bool isAlwaysVisibleAtHigherLODs = false;
        };

        public class RuinData : ModelData
        {
            public int type = 0;
            public int level = 1;
            public string objectType = "Ruin";
            public float radius = 5.0f;
            public PropertyDatas properties;
        };

        public class ModelLayerData : MapObjectLayerData
        {
            public ModelLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager, float width, float height, MapObjectData[] models) : base(layerID, name, offset, config, lodGroupManager, width, height, models)
            {
            }
        };

        public class RiverLayerData : MapObjectLayerData
        {
            public RiverLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager, float width, float height, MapObjectData[] models) : base(layerID, name, offset, config, lodGroupManager, width, height, models)
            {
            }
        };

        public class Detector
        {
            public Detector(Vector3 pos, int type)
            {
                this.position = pos;
                this.type = type;
            }

            public Vector3 position;
            public int type;
        }

        public class MapCollisionLayerData : MapLayerData
        {
            public MapCollisionLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float width, float height, MapCollisionData[] datas, PrefabOutlineType displayType, float displayVertexRadius, Detector[] detectors, float maxConnectionDistance) : base(layerID, name, offset, config, null)
            {
                collisions = datas;
                this.width = width;
                this.height = height;
                this.displayType = displayType;
                this.displayVertexRadius = displayVertexRadius;
                this.detectors = detectors;
                this.maxConnectionDistance = maxConnectionDistance;
            }

            public float width;
            public float height;
            public PrefabOutlineType displayType;
            public float displayVertexRadius;
            public MapCollisionData[] collisions;
            public Detector[] detectors;
            public float maxConnectionDistance;
        };

        public class RegionLayerData : MapLayerData
        {
            public RegionLayerData(int layerID, string name, Vector3 offset, float width, float height, RegionData[] datas, float displayVertexRadius, string defaultRegionMaterialGuid, float borderMinX, float borderMinZ, float borderMaxX, float borderMaxZ, Vector3[] borderMeshVertices, int[] borderMeshIndices, string borderMaterialGuid, bool generateBorderLineMesh, string borderLineMaterialGuid, float borderLineWidth, bool showBorderLineMesh, bool showRegionMesh) : base(layerID, name, offset, null, null)
            {
                regions = datas;
                this.width = width;
                this.height = height;
                this.displayVertexRadius = displayVertexRadius;
                this.defaultRegionMaterialGuid = defaultRegionMaterialGuid;
                this.borderMinX = borderMinX;
                this.borderMinZ = borderMinZ;
                this.borderMaxX = borderMaxX;
                this.borderMaxZ = borderMaxZ;
                this.borderMeshVertices = borderMeshVertices;
                this.borderMeshIndices = borderMeshIndices;
                this.borderMaterialGuid = borderMaterialGuid;
                this.generateBorderLineMesh = generateBorderLineMesh;
                this.borderLineMaterialGuid = borderLineMaterialGuid;
                this.borderLineWidth = borderLineWidth;
                this.showBorderLineMesh = showBorderLineMesh;
                this.showRegionMesh = showRegionMesh;
            }

            public bool generateBorderLineMesh;
            public string borderLineMaterialGuid;
            public float borderLineWidth;
            public bool showBorderLineMesh;
            public bool showRegionMesh;
            public float width;
            public float height;
            public float displayVertexRadius;
            public float borderMinX;
            public float borderMinZ;
            public float borderMaxX;
            public float borderMaxZ;
            public Vector3[] borderMeshVertices;
            public int[] borderMeshIndices;
            public string borderMaterialGuid;
            public string defaultRegionMaterialGuid;
            public RegionData[] regions;
        };

        

        public class CameraColliderLayerData : MapLayerData
        {
            public CameraColliderLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float width, float height, CameraColliderData[] datas, float displayVertexRadius) : base(layerID, name, offset, config, null)
            {
                colliders = datas;
                this.width = width;
                this.height = height;
                this.displayVertexRadius = displayVertexRadius;
            }

            public float width;
            public float height;
            public float displayVertexRadius;
            public CameraColliderData[] colliders;
        };

        public class RiverPrefab
        {
            public RiverPrefab(int riverID, int sectionIndex, string prefabPath)
            {
                this.riverID = riverID;
                this.sectionIndex = sectionIndex;
                this.prefabPath = prefabPath;
            }

            //属于哪条河流
            public int riverID;
            //是哪个section
            public int sectionIndex;
            public string prefabPath;
        }

        public class PolygonRiverLayerData : MapLayerData
        {
            public PolygonRiverLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float width, float height, PolygonRiverData[] datas, float displayVertexRadius, RiverPrefab[] riverPrefabs, int bakedTextureSize, bool onlyGenerateLOD0Assets, List<int> groundTileTypeClipExceptions, bool useUV2, bool generateOBJ, int expandingTileCount, string riverMaskTextureSaveFolder) : base(layerID, name, offset, config, null)
            {
                collisions = datas;
                this.width = width;
                this.height = height;
                this.displayVertexRadius = displayVertexRadius;
                this.bakedTextureSize = bakedTextureSize;
                riverPrefabInfos = riverPrefabs;
                this.onlyGenerateLOD0Assets = onlyGenerateLOD0Assets;
                this.groundTileTypeClipExceptions = groundTileTypeClipExceptions;
                this.useUV2 = useUV2;
                this.generateOBJ = generateOBJ;
                this.expandingTileCount = expandingTileCount;
                this.riverMaskTextureSaveFolder = riverMaskTextureSaveFolder;
            }

            public float width;
            public float height;
            public float displayVertexRadius;
            public int bakedTextureSize;
            public int expandingTileCount;
            public string riverMaskTextureSaveFolder;
            public bool onlyGenerateLOD0Assets;
            //导出mesh时是否生成生成uv2
            public bool useUV2;
            //导出mesh时是否生成obj文件
            public bool generateOBJ;
            public PolygonRiverData[] collisions;
            public RiverPrefab[] riverPrefabInfos;
            public List<int> groundTileTypeClipExceptions;
        };

        public class CircleBorderLayerData : ModelLayerData
        {
            public CircleBorderLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float width, float height, MapObjectData[] models, int combineBorderLOD) : base(layerID, name, offset, config, null, width, height, models)
            {
                this.combineBorderLOD = combineBorderLOD;
            }

            public int combineBorderLOD;
        };

        public class RuinSpecialRegionSetting
        {
            public float bigGridWidth = 0;
            public float bigGridHeight = 0;
            public float startX = 0;
            public float startZ = 0;
            public int pointCount = 0;
            public int horizontalBigGridCount = 0;
            public int verticalBigGridCount = 0;
            public List<int> invalidCircles = new List<int>();
        }

        public class RuinLayerData : ModelLayerData
        {
            public RuinLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, float width, float height, MapObjectData[] models, float cellWidth, int hordeCount, float displayRadius, bool textVisible, List<RuinSetting> ruinSettings, bool shareProperties, bool exportInHierarchyOrder) : base(layerID, name, offset, config, null, width, height, models)
            {
                this.cellWidth = cellWidth;
                this.hordeCount = hordeCount;
                this.displayRadius = displayRadius;
                this.textVisible = textVisible;
                this.ruinObjectTypes = ruinSettings;
                this.shareProperties = shareProperties;
                this.exportInHierarchyOrder = exportInHierarchyOrder;
            }

            public float cellWidth;
            public int hordeCount;
            public float displayRadius;
            public bool textVisible;
            public bool shareProperties;
            public bool exportInHierarchyOrder;
            public List<RuinSetting> ruinObjectTypes;
        };

        public class RailwayLayerData : MapObjectLayerData
        {
            public RailwayLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, ModelLODGroupManager lodGroupManager, float width, float height, MapObjectData[] models, int count, float railWidth, float railTotalLength, Vector3 center, float railPrefabLength) : base(layerID, name, offset, config, lodGroupManager, width, height, models)
            {
                this.railWidth = railWidth;
                this.railTotalLength = railTotalLength;
                this.railPrefabLength = railPrefabLength;
                this.count = count;
                this.center = center;
            }

            public float railWidth;
            public float railTotalLength;
            public float railPrefabLength;
            public int count;
            public Vector3 center;
        };

        public class BlendTerrainTileData : NamedObject
        {
            public BlendTerrainTileData(int id, int layerID, int type, int index, int subTypeIndex, int templateID, float[] heights) : base(id, "")
            {
                this.layerID = layerID;
                this.type = type;
                this.index = index;
                this.templateID = templateID;
                this.subTypeIndex = subTypeIndex;
                this.heights = heights;
            }

            public int layerID;
            public int index;
            public int type;
            public int subTypeIndex;
            public int templateID;
            public float[] heights;
        }

        public class VaryingTileSizeTerrainTileData : NamedObject
        {
            public VaryingTileSizeTerrainTileData(int id, int layerID, int type, int index, int tileID, int templateID, int bigTileIndex) : base(id, "")
            {
                this.layerID = layerID;
                this.type = type;
                this.index = index;
                this.templateID = templateID;
                this.bigTileIndex = bigTileIndex;
                this.tileID = tileID;
            }

            public int layerID;
            public int index;
            public int type;
            public int tileID;
            public int templateID;
            public int bigTileIndex;
        }

        public class VaryingTileSizeTerrainLayerBigTileData
        {
            public int x;
            public int y;
            public int width;
            public int height;
        }

        public class VaryingTileSizeTerrainLayerData : MapLayerData
        {
            public VaryingTileSizeTerrainLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, VaryingTileSizeTerrainTileData[] tiles, VaryingTileSizeTerrainLayerBigTileData[] bigTiles, bool useGeneratedLOD, Vector3 layerPosition) : base(layerID, name, offset, config, null)
            {
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.rows = rows;
                this.cols = cols;
                this.tiles = tiles;
                this.useGeneratedLOD = useGeneratedLOD;
                this.layerPosition = layerPosition;
                this.bigTiles = bigTiles;
            }

            public float tileWidth;
            public float tileHeight;
            public int rows;
            public int cols;
            public VaryingTileSizeTerrainTileData[] tiles;
            public bool useGeneratedLOD;            
            public Vector3 layerPosition;
            public VaryingTileSizeTerrainLayerBigTileData[] bigTiles;
        }

        public class BlendTerrainLayerData : MapLayerData
        {
            public BlendTerrainLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, BlendTerrainTileData[] tiles, bool useGeneratedLOD, bool useDecorationObject, string texturePropertyName, string groundTileAtlasSettingGuid, bool useTileHeight, string[] usedTilePrefabPaths, bool generateMeshCollider, bool getGroundHeightInGame, bool optimizeMesh, Vector3 layerPosition) : base(layerID, name, offset, config, null)
            {
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.rows = rows;
                this.cols = cols;
                this.tiles = tiles;
                this.useGeneratedLOD = useGeneratedLOD;
                this.useDecorationObject = useDecorationObject;
                this.groundTileAtlasSettingGuid = groundTileAtlasSettingGuid;
                this.texturePropertyName = texturePropertyName;
                this.useTileHeight = useTileHeight;
                this.usedTilePrefabPaths = usedTilePrefabPaths;
                this.getGroundHeightInGame = getGroundHeightInGame;
                this.generateMeshCollider = generateMeshCollider;
                this.optimizeMesh = optimizeMesh;
                this.layerPosition = layerPosition;
            }

            public float tileWidth;
            public float tileHeight;
            public int rows;
            public int cols;
            public BlendTerrainTileData[] tiles;
            public bool useGeneratedLOD;
            public bool useDecorationObject;
            public bool useTileHeight;
            public bool generateMeshCollider;
            public bool getGroundHeightInGame;
            public bool optimizeMesh;
            public string texturePropertyName;
            public string groundTileAtlasSettingGuid;
            public string[] usedTilePrefabPaths;
            public Vector3 layerPosition;
        }

        public class SimpleBlendTerrainLayerData : MapLayerData
        {
            public SimpleBlendTerrainLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, ushort[] tiles, string[] tilePrefabPaths, bool useGeneratedLOD, Vector3 layerPosition) : base(layerID, name, offset, config, null)
            {
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.rows = rows;
                this.cols = cols;
                this.tiles = tiles;
                this.useGeneratedLOD = useGeneratedLOD;
                this.tilePrefabPaths = tilePrefabPaths;
                this.layerPosition = layerPosition;
            }

            public float tileWidth;
            public float tileHeight;
            public int rows;
            public int cols;
            public ushort[] tiles;
            public string[] tilePrefabPaths;
            public bool useGeneratedLOD;
            public Vector3 layerPosition;
        }

        public class TileBlockTerrainLayerData : MapLayerData
        {
            public TileBlockTerrainLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, ushort[] tiles, string[] tilePrefabPaths, bool useGeneratedLOD, TFW.Map.TileBlockTerrainLayerData.TileBlock[] tileBlocks, Dictionary<int, Vector2[]> singleTileTypeAtlasUVMappings, Dictionary<int, string> singleTileTypeAtlasMaterialMappings) : base(layerID, name, offset, config, null)
            {
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.rows = rows;
                this.cols = cols;
                this.tiles = tiles;
                this.useGeneratedLOD = useGeneratedLOD;
                this.tilePrefabPaths = tilePrefabPaths;
                this.tileBlocks = tileBlocks;
                this.singleTileTypeAtlasMaterialMappings = singleTileTypeAtlasMaterialMappings;
                this.singleTileTypeAtlasUVMappings = singleTileTypeAtlasUVMappings;
            }

            public float tileWidth;
            public float tileHeight;
            public int rows;
            public int cols;
            public ushort[] tiles;
            public string[] tilePrefabPaths;
            public bool useGeneratedLOD;
            public TFW.Map.TileBlockTerrainLayerData.TileBlock[] tileBlocks;
            public Dictionary<int, string> singleTileTypeAtlasMaterialMappings;
            public Dictionary<int, Vector2[]> singleTileTypeAtlasUVMappings;
        }

        public class CityTerritoryBlockInfo
        {
            public Rect bounds;
            public string prefabPath;
        }

        public class CityTerritoryData
        {
            public CityTerritoryData(int territoryID, string assetPath, Vector3 buildingPosition, Vector3 meshPosition, Rect worldBounds, Color color, string materialPath, int blockIndex)
            {
                this.territoryID = territoryID;
                this.assetPath = assetPath;
                this.buildingPosition = buildingPosition;
                this.position = meshPosition;
                this.worldBounds = worldBounds;
                this.color = color;
                this.materialPath = materialPath;
                this.blockIndex = blockIndex;
            }

            public int territoryID;
            public string assetPath;
            public Vector3 buildingPosition;
            public Vector3 position;
            public Rect worldBounds;
            public Color color;
            public string materialPath;
            //used in lod1
            public int blockIndex = -1;
        }

        public class CityTerritorySubLayerEdgeInfo
        {
            public Vector3 position;
            public Rect worldBounds;
            //self region id
            public int leftRegionID;
            //neighbour region id
            public int rightRegionID;
            public string prefabPath;
            public string materialPath;
            //used in lod1
            public int blockIndex = -1;
        }

        public class CityTerritorySubLayerData
        {
            public CityTerritorySubLayerData(int rows, int cols, float tileWidth, float tileHeight, short[,] grids, CityTerritoryData[] territories, CityTerritorySubLayerEdgeInfo[] edgesInfo, List<CityTerritoryDataBlock> blocks, int maskTextureWidth, int maskTextureHeight, Color32[] maskTextureData)
            {
                this.zTileCount = rows;
                this.xTileCount = cols;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.territories = territories;
                this.grids = grids;
                this.edgesInfo = edgesInfo;
                this.blocks = blocks;
                this.maskTextureWidth = maskTextureWidth;
                this.maskTextureHeight = maskTextureHeight;
                this.maskTextureData = maskTextureData;
            }

            public float tileWidth;
            public float tileHeight;
            public int xTileCount;
            public int zTileCount;
            public short[,] grids;
            public CityTerritoryData[] territories = new CityTerritoryData[0];
            public CityTerritorySubLayerEdgeInfo[] edgesInfo;
            public List<CityTerritoryDataBlock> blocks;
            public int maskTextureWidth;
            public int maskTextureHeight;
            public Color32[] maskTextureData;
        }

        public class CityTerritoryLayerData : MapLayerData
        {
            public CityTerritoryLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, List<config.CityTerritorySubLayerData> subLayers) : base(layerID, name, offset, config, null)
            {
                this.subLayers = subLayers;
            }

            public List<config.CityTerritorySubLayerData> subLayers;
        };

        public class RuntimeRegionData
        {
            public RuntimeRegionData(string assetPath, Rect worldBounds, int number, string borderLinePrefabPath)
            {
                this.assetPath = assetPath;
                this.worldBounds = worldBounds;
                this.number = number;
                this.borderLinePrefabPath = borderLinePrefabPath;
            }

            public string assetPath;
            public string borderLinePrefabPath;
            public Rect worldBounds;
            public int number;
        }

        public class RuntimeRegionLayerData : MapLayerData
        {
            public RuntimeRegionLayerData(int layerID, string name, Vector3 offset, float layerWidth, float layerHeight, RuntimeRegionData[] regions, string borderPrefabPath) : base(layerID, name, offset, null, null)
            {
                this.layerWidth = layerWidth;
                this.layerHeight = layerHeight;
                this.regions = regions;
                this.borderPrefabPath = borderPrefabPath;
            }
            public float layerWidth;
            public float layerHeight;
            public RuntimeRegionData[] regions = new RuntimeRegionData[0];
            public string borderPrefabPath;
        };

        public class SplitFogLayerData : MapLayerData
        {
            public SplitFogLayerData(int layerID, string name, Vector3 offset, MapLayerLODConfig config, int rows, int cols, float tileWidth, float tileHeight, int[] tiles, string[] fogTilePrefabPaths, float fogHeight, string fogLOD1PrefabPath, string selectionMaterialPath, string fogMaskTexPropertyName) : base(layerID, name, offset, config, null)
            {
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.rows = rows;
                this.cols = cols;
                this.tiles = tiles;
                this.fogTilePrefabPaths = fogTilePrefabPaths;
                this.fogHeight = fogHeight;
                this.fogLOD1PrefabPath = fogLOD1PrefabPath;
                this.selectionMaterialPath = selectionMaterialPath;
                this.fogMaskTexPropertyName = fogMaskTexPropertyName;
            }

            public float tileWidth;
            public float tileHeight;
            public int rows;
            public int cols;
            public int[] tiles;
            public string[] fogTilePrefabPaths;
            public float fogHeight;
            public string fogLOD1PrefabPath;
            public string selectionMaterialPath;
            public string fogMaskTexPropertyName;
        }

        public class RuntimeRegionColorData
        {
            public RuntimeRegionColorData(int id, Color color)
            {
                this.id = id;
                this.color = color;
            }

            public int id;
            public Color color;
        }

        public class RuntimeRegionColorSubLayerData
        {
            public string name;
            public int horizontalTileCount;
            public int verticalTileCount;
            public float tileWidth;
            public float tileHeight;
            public short[] regionIDs;
            public RuntimeRegionColorData[] regions;
        }

        public class RuntimeRegionColorLayerData : MapLayerData
        {
            public RuntimeRegionColorLayerData(int layerID, string name, Vector3 offset, RuntimeRegionColorSubLayerData[] layers, bool showRegionInGame) : base(layerID, name, offset, null, null)
            {
                this.layers = layers;
                this.showRegionInGame = showRegionInGame;
            }

            public RuntimeRegionColorSubLayerData[] layers;
            public bool showRegionInGame;
        }

        public class Camera
        {
            public Camera()
            {
                rotation = Quaternion.Euler(45, 0, 0);
                position = new Vector3(0, 50, 0);
            }
            public Vector3 position;
            public Quaternion rotation;
            public bool orthographic;
            public float verticalFov = 51;
            public float orthongonalSize;
        };

        public class MapLODUnit
        {
            public MapLODUnit(string unit, string relation)
            {
                this.unit = unit;
                this.relation = relation;
            }

            public string unit;
            public string relation;
        }

        public class MapLOD
        {
            public MapLOD()
            {
                name = TFW.Map.MapLayerLODConfig.GetDefaultLODName(0);
            }

            public string name;
            public float cameraHeight = 0;
            public float viewWidth = 0;
            public float viewHeight = 0;
            public bool showTerritory = false;
            public List<MapLODUnit> displayingUnits = new List<MapLODUnit>();
        }
        public class MapLODConfig
        {
            public MapLODConfig()
            {
                lods = new MapLOD[1];
                lods[0] = new MapLOD();
            }
            public MapLOD[] lods;
        }

        public class MapData
        {
            public MapData(float width, float height, float borderHeight, bool isCircle, float backExtendedSize, float farClipOffset, bool navMeshRegionVisible, float groundTileSize, float frontTileSize, Bounds mapDataGenerationRange)
            {
                this.width = width;
                this.height = height;
                this.borderHeight = borderHeight;
                this.isCircle = isCircle;
                this.backExtendedSize = backExtendedSize;
                this.farClipOffset = farClipOffset;
                this.navMeshRegionVisible = navMeshRegionVisible;
                this.groundTileSize = groundTileSize;
                this.frontTileSize = frontTileSize;
                this.mapDataGenerationRange = mapDataGenerationRange;
            }

            public MapLayerData GetMapLayer(System.Type t)
            {
                for (int i = 0; i < mapLayers.Length; ++i)
                {
                    if (mapLayers[i].GetType() == t)
                    {
                        return mapLayers[i];
                    }
                }
                return null;
            }

            public float width;
            public float height;
            public float borderHeight;
            public bool isCircle;
            public float backExtendedSize;
            public float farClipOffset;
            public float maxCameraColliderHeight = 100;
            public float groundTileSize;
            public float frontTileSize;
            public bool useTerrainHeight = false;
            public bool generateNPCSpawnPointsInBorderLine = false;
            public bool removeSameHoles;
            public bool navMeshRegionVisible = false;
            public Bounds mapDataGenerationRange;
            public MapLayerData[] mapLayers = new MapLayerData[0];
            public MapLODConfig lodConfig = new MapLODConfig();
            public Camera camera = new Camera();
        }

        public class ModelTemplateChildrenPrefabInfo
        {
            public short[] childrenPrefabPathIndices;
            public byte[] childrenPrefabTileObjectType;
            public Rect[] childrenPrefabBounds;
            public Vector3[] childrenPrefabPosition;
            public short[] childrenPrefabScalingIndex;
            public short[] childrenPrefabRotationIndex;
        }

        public class ModelTemplateLODInfo
        {
            public List<int> existedLODs;
            public List<int> lodPrefabPathIndices;
        }

        public class TransformTable
        {
            public Vector3[] scalings;
            public Quaternion[] rotations;
        }

        public class ModelTemplate : NamedObject
        {
            public string prefabPath;
            public Bounds bounds;
            public bool isTileModelTemplate = false;
            public bool generated = false;
            public ModelTemplateChildrenPrefabInfo[] childrenPrefabs = null;
            public ModelTemplateLODInfo lodInfo;
            public bool preload = false;
        };

        public class ModelTemplates
        {
            public string[] stringTables;
            public TransformTable transformTable;
            public ModelTemplate[] modelTemplates = new ModelTemplate[0];
        }

        public class PrefabManager
        {
            public int nextGroupID = -1;
            public PrefabGroup[] groups = new PrefabGroup[0];
        }

        public class PrefabSubGroup
        {
            public string prefabPath;
            public string[] subGroupPrefabPaths = new string[0];
            public string[] decorationPrefabGuids = new string[0];
            public Vector2Int size = new Vector2Int(1, 1);
            public int id;
        }

        public class PrefabGroup
        {
            public int id = -1;
            public string name;
            public Color32 color;
            public bool addPrefabSet = true;
            public PrefabSubGroup[] prefabPaths = new PrefabSubGroup[0];
        }

        public class MapPrefabObstacle
        {
            public int id;
            public Vector3[] vertices;
            public int[] triangleIndices;
        }

        public class CameraCollider
        {
            public Vector3[] vertices;
            public int[] indices;
        }

        public class MapLocalObstacleManager
        {
            public float regionWidth = MapModule.defaultFrontTileSize;
            public float regionHeight = MapModule.defaultFrontTileSize;
            public int[] tiles;
            public MapPrefabObstacle[] obstacles;
            public string obstacleMaterialPath;
        }

        public class MapGlobalObstacleManager
        {
            public Vector3[] obstacleVertices;
            public int[] obstacleIndices;
            public string obstacleMaterialPath;
            public byte[] gridObstacles;
            public float gridSize = MapModule.obstacleGridSize;
        }

        public class PathMapperData
        {
            public KeyValuePair<string, string>[] paths = new KeyValuePair<string, string>[0];
        }

        public class ModelLODGroup : BaseObject
        {
            public int lod = -1;
            public bool combineModels = false;
            public int leaderObjectID = 0;
        }

        public class ModelLODGroupManager
        {
            public ModelLODGroup[] groups = new ModelLODGroup[0];
        }

        public class DetailSpriteGroup
        {
            public string name = "sprite group";
            public Color32 color = new Color32(255, 255, 255, 255);
            public string[] detailSpritesGUIDs = new string[0];
        }

        public class DetailSpritesSetting
        {
            public float mapWidth;
            public float mapHeight;
            public float alpha0Height = 18;
            public float alpha1Height = 17;
            public bool crossfading = true;
            public bool updateScale = true;
            public int horizontaolGridCount;
            public int verticalGridCount;
            public DetailSpriteGroup[] groups = new DetailSpriteGroup[0];
        }

        public class GridRegionTemplate
        {
            public GridRegionTemplate(Color32 color, byte type)
            {
                this.color = color;
                this.type = type;
            }
            public Color32 color;
            public byte type;
        }

        public class GridRegionSetting
        {
            public int horizontalGridCount;
            public int verticalGridCount;
            public float gridWidth;
            public float gridHeight;
            public byte[] gridData;
            public GridRegionTemplate[] templates = new GridRegionTemplate[0];
        }

        public class RuinSetting
        {
            public RuinSetting(string name, Color color, PropertyDatas properties, float colliderRadius, string prefabGUID, RuinSpecialRegionSetting setting)
            {
                this.name = name;
                this.color = color;
                this.properties = properties;
                this.colliderRadius = colliderRadius;
                this.prefabGUID = prefabGUID;
                this.regionSetting = setting;
            }

            public string name;
            public Color color;
            public PropertyDatas properties;
            public float colliderRadius;
            public string prefabGUID;
            public RuinSpecialRegionSetting regionSetting;
        }

        public class MapPluginLayerList
        {
            public List<string> pluginLayerNames = new List<string>();
        }

        public class SLGMakerData
        {
            public SLGMakerData(float width, float height, float borderHeight, bool isCircle, float backExtendedSize, float farClipOffset, bool navMeshRegionVisible, float groundTileSize, float frontTileSize, BackgroundSetting backgroundSetting, Version version, Bounds mapDataGenerationRange)
            {
                map = new MapData(width, height, borderHeight, isCircle, backExtendedSize, farClipOffset, navMeshRegionVisible, groundTileSize, frontTileSize, mapDataGenerationRange);
                this.version = version;
                this.backgroundSetting = backgroundSetting;
            }

            public void OnDestroy()
            {
                BaseObject.Clear();
            }

            //包含子串都可以
            public MapLayerData GetMapLayerData(string name)
            {
                int n = map.mapLayers.Length;
                for (int i = 0; i < n; ++i)
                {
                    if (map.mapLayers[i].name.Contains(name))
                    {
                        return map.mapLayers[i];
                    }
                }
                return null;
            }

            public long[] sectionDataOffset;
            public Version version = new Version(0, 0);
            public EditorSetting setting = new EditorSetting();
            public ModelTemplates modelTemplates = new ModelTemplates();
            public PrefabManager terrainPrefabManager = new PrefabManager();
            public PrefabManager varyingTileSizeTerrainPrefabManager = new PrefabManager();
            public MapLocalObstacleManager localObstacleManager = new MapLocalObstacleManager();
            public MapGlobalObstacleManager globalObstacleManager = new MapGlobalObstacleManager();
            public CameraCollider cameraCollider = new CameraCollider();
            public DetailSpritesSetting detailSpritesSetting = new DetailSpritesSetting();
            public GridRegionSetting gridRegionSetting;
            public MapData map;
            public BackgroundSetting backgroundSetting = new BackgroundSetting();
            public MapPluginLayerList pluginList = new MapPluginLayerList();
        };
    }
}
