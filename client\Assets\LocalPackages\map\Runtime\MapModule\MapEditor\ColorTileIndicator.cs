﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ColorTileIndicator
    {
        public void SetPrefab(int spriteTemplateID, SpriteTileLayerData layerData)
        {
            if (mSpriteTemplateID != spriteTemplateID)
            {
                mSpriteTemplateID = spriteTemplateID;

                if (mIndicator != null)
                {
                    GameObject.DestroyImmediate(mIndicator);
                    mIndicator = null;
                }

                var spriteTemplate = Map.currentMap.FindObject(spriteTemplateID) as SpriteTemplate;
                if (spriteTemplate != null)
                {
                    if (mCache == null)
                    {
                        mCache = new ColorSpriteMeshCache();
                    }
                    mIndicator = mCache.CreateGameObject(spriteTemplate.color, layerData);
                    mIndicator.name = "Color Tile Indicator";
                    Utils.HideGameObject(mIndicator);
                    mIndicator.transform.parent = SLGMakerEditor.instance.gameObject.transform;
                }
            }
        }

        public void OnDestroy()
        {
            if (mIndicator != null)
            {
                GameObject.DestroyImmediate(mIndicator);
                mIndicator = null;
            }

            if (mCache != null)
            {
                mCache.OnDestroy();
                mCache = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mIndicator != null)
            {
                mIndicator.transform.position = pos;
            }
        }

        public void SetColor(Color32 color)
        {
            if (mIndicator != null)
            {
                var renderer = mIndicator.GetComponent<Renderer>();
                renderer.sharedMaterial.color = color;
            }
        }

        public void SetActive(bool active)
        {
            if (mIndicator != null)
            {
                mIndicator.SetActive(active);
            }
        }

        GameObject mIndicator;
        int mSpriteTemplateID = 0;
        ColorSpriteMeshCache mCache;
    }
}


#endif