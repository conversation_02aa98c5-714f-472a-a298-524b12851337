﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class ActionRemoveComplexGridModel : EditorAction
    {
        public ActionRemoveComplexGridModel(int objectID, int layerID, int lod)
        {
            var obj = Map.currentMap.FindObject(objectID) as ComplexGridModelData;
            mLOD = lod;
            mLayerID = layerID;
            mObjectID = objectID;
            mModelTemplateID = obj.GetModelTemplateID();
            mRotation = obj.GetRotation();
            mScale = obj.GetScale();
            mPosition = obj.GetPosition();
            mOccupiedGridCount = obj.occupiedGridCount;
            var modelTemplate = Map.currentMap.FindObject(mModelTemplateID) as ModelTemplate;
            var prefabName = Path.GetFileName(modelTemplate.GetLODPrefabPath(0));
            mDescription = string.Format("remove {0}", prefabName);
            mObjectTag = obj.objectTag;
        }

        public override bool Do()
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as EditorComplexGridModelLayer;
            if (layer != null)
            {
                layer.RemoveObject(mObjectID);
                return true;
            }

            return false;
        }

        public override bool Undo()
        {
            var map = Map.currentMap;
            var modelTemplate = map.FindObject(mModelTemplateID) as ModelTemplate;
            if (modelTemplate != null)
            {
                var layer = map.GetMapLayerByID(mLayerID) as EditorComplexGridModelLayer;
                if (layer != null)
                {
                    layer.AddObject(mLOD, modelTemplate.GetLODPrefabPath(0), mPosition, mRotation, mScale, mOccupiedGridCount, mObjectTag, mObjectID);
                    return true;
                }
            }
            return false;
        }

        public override string description { get { return mDescription; } }

        int mLayerID;
        int mObjectID;
        int mModelTemplateID;
        int mOccupiedGridCount;
        int mLOD;
        Quaternion mRotation;
        Vector3 mScale;
        Vector3 mPosition;
        string mDescription;
        string mObjectTag;
    }
}

#endif