﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public class MapLayerViewport
    {
        //center: viewport初始的中心点
        //viewportSize: viewport初始的尺寸
        //offset: innerViewport和outerViewport的尺寸差别的一半
        //minSize: viewport最小的尺寸
        //maxSize: viewport最大的尺寸
        public MapLayerViewport(Rect viewport, Vector2 offset, Vector2 minSize, Vector2 maxSize)
        {
            var outerSize = viewport.size + offset * 2;
            var innerSize = viewport.size - offset * 2;
            mOuterViewport = new Rect(viewport.xMin - offset.x, viewport.yMin - offset.y, outerSize.x, outerSize.y);
            mInnerViewport = new Rect(viewport.xMin + offset.x, viewport.yMin + offset.y, innerSize.x, innerSize.y);
            mLastOuterViewport = mOuterViewport;
            mLastInnerViewport = mInnerViewport;

            mMinSize = minSize;
            mMaxSize = maxSize;
            mOffset = offset;

#if false
            if (Map.currentMap.isEditorMode == false)
            {
                mDebugObject = new GameObject("Debug Map Layer Viewport");
                {
                    var obj = new GameObject("Outer Bounds");
                    obj.transform.SetParent(mDebugObject.transform);
                    var drawBounds = obj.AddComponent<DrawBounds>();
                    drawBounds.bounds = new Bounds(mOuterViewport.center, new Vector3(outerSize.x, 0, outerSize.y));
                    drawBounds.color = Color.red;
                }
                {
                    var obj = new GameObject("Inner Bounds");
                    obj.transform.SetParent(mDebugObject.transform);
                    var drawBounds = obj.AddComponent<DrawBounds>();
                    drawBounds.bounds = new Bounds(mInnerViewport.center, new Vector3(innerSize.x, 0, innerSize.y));
                    drawBounds.color = Color.blue;
                }
            }
#endif
        }

        public void OnDestroy()
        {
            if (mDebugObject != null)
            {
                GameObject.DestroyImmediate(mDebugObject);
            }
        }

        //return true if updated
        public bool Update(Rect newViewport, out bool passOutline)
        {
            passOutline = false;

            if (!(newViewport.xMin >= mOuterViewport.xMin && newViewport.xMax <= mOuterViewport.xMax &&
                newViewport.yMin >= mOuterViewport.yMin && newViewport.yMax <= mOuterViewport.yMax))
            {
                var newOuterSize = newViewport.size + mOffset;
                var newOuterViewport = new Rect(newViewport.center - newOuterSize * 0.5f, newOuterSize);
                UpdateOuterViewport(newOuterViewport);
                var newInnerSize = newViewport.size - mOffset;
                var newInnerViewport = new Rect(newViewport.center - newInnerSize * 0.5f, newInnerSize);
                UpdateInnerViewport(newInnerViewport);

                passOutline = true;
                return true;
            }
            else if (!(mInnerViewport.xMin >= newViewport.xMin && mInnerViewport.xMax <= newViewport.xMax &&
                mInnerViewport.yMin >= newViewport.yMin && mInnerViewport.yMax <= newViewport.yMax))
            {
                var newInnerSize = newViewport.size - mOffset;
                var newInnerViewport = new Rect(newViewport.center - newInnerSize * 0.5f, newInnerSize);
                UpdateInnerViewport(newInnerViewport);
                var newOuterSize = newViewport.size + mOffset;
                var newOuterViewport = new Rect(newViewport.center - newOuterSize * 0.5f, newOuterSize);
                UpdateOuterViewport(newOuterViewport);
                return true;
            }

            return false;
        }

        void UpdateOuterViewport(Rect newViewport)
        {
            //超出了外界
            Vector2 newViewportSize = CalculateOuterViewportSize(newViewport.size);
            mLastOuterViewport = mOuterViewport;

            mOuterViewport = new Rect(newViewport.center - newViewportSize * 0.5f, newViewportSize);

            if (mDebugObject != null)
            {
                var outerObj = mDebugObject.transform.GetChild(0);
                var drawBounds = outerObj.GetComponent<DrawBounds>();
                drawBounds.bounds = new Bounds(new Vector3(mOuterViewport.center.x, 0, mOuterViewport.center.y), new Vector3(mOuterViewport.size.x, 0, mOuterViewport.size.y));
            }
        }

        void UpdateInnerViewport(Rect newViewport)
        {
            //超过了内界
            Vector2 newViewportSize = CalculateInnerViewportSize(newViewport.size);

            mLastInnerViewport = mInnerViewport;
            mInnerViewport = new Rect(newViewport.center - newViewportSize * 0.5f, newViewportSize);

            if (mDebugObject != null)
            {
                var innerObj = mDebugObject.transform.GetChild(1);
                var drawBounds = innerObj.GetComponent<DrawBounds>();
                drawBounds.bounds = new Bounds(new Vector3(mInnerViewport.center.x, 0, mInnerViewport.center.y), new Vector3(mInnerViewport.size.x, 0, mInnerViewport.size.y));
            }
        }

        Vector2 CalculateOuterViewportSize(Vector2 newSize)
        {
            newSize.x = Mathf.Clamp(newSize.x, mMinSize.x, mMaxSize.x);
            newSize.y = Mathf.Clamp(newSize.y, mMinSize.y, mMaxSize.y);

            return newSize;
        }

        Vector2 CalculateInnerViewportSize(Vector2 newSize)
        {
            newSize.x = Mathf.Clamp(newSize.x, mMinSize.x, mMaxSize.x);
            newSize.y = Mathf.Clamp(newSize.y, mMinSize.y, mMaxSize.y);

            return newSize;
        }

        public Rect lastOuterViewport { get { return mLastOuterViewport; } }
        public Rect lastInnerViewport { get { return mLastInnerViewport; } }
        public Rect innerViewport { get { return mInnerViewport; } }
        public Rect outerViewport { get { return mOuterViewport; } }

        Rect mOuterViewport;
        Rect mInnerViewport;
        Rect mLastOuterViewport;
        Rect mLastInnerViewport;
        Vector2 mMinSize;
        Vector2 mMaxSize;
        Vector2 mOffset;

        //debug
        GameObject mDebugObject = null;
    }
}
