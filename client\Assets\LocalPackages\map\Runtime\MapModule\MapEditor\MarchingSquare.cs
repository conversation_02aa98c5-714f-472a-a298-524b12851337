﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MarchingSquare
    {
        [MenuItem("Map Editor/Test/Marching Square Noise Test")]
        static void NoiseTest()
        {
            int rows = 100;
            int cols = 100;
            float increment = 0.1f;
            float[,] grids = new float[rows, cols];
            float start = Time.time;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    grids[i, j] = Mathf.PerlinNoise(j * increment + start, i * increment + start);
                }
            }

            MarchingSquare m = new MarchingSquare();
            var mesh = m.Generate(grids, 0.1f);
            var obj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            var filter = obj.GetComponent<MeshFilter>();
            filter.sharedMesh = mesh;
            obj.name = "noise";
        }

        [MenuItem("Map Editor/Test/Marching Square Test")]
        static void Test()
        {
            MarchingSquare m = new MarchingSquare();

            List<float[,]> grids = new List<float[,]>();
            {
                var t1 = new float[2, 2];
                t1[0, 0] = 0;
                t1[1, 0] = 0;
                t1[1, 1] = 0;
                t1[0, 1] = 0;
                grids.Add(t1);
            }
            {
                var t2 = new float[2, 2];
                t2[0, 0] = 1;
                t2[1, 0] = 0;
                t2[1, 1] = 0;
                t2[0, 1] = 0;
                grids.Add(t2);
            }
            {
                var t3 = new float[2, 2];
                t3[0, 0] = 0;
                t3[1, 0] = 1;
                t3[1, 1] = 0;
                t3[0, 1] = 0;
                grids.Add(t3);
            }
            {
                var t4 = new float[2, 2];
                t4[0, 0] = 0;
                t4[1, 0] = 0;
                t4[1, 1] = 1;
                t4[0, 1] = 0;
                grids.Add(t4);
            }
            {
                var t5 = new float[2, 2];
                t5[0, 0] = 0;
                t5[1, 0] = 0;
                t5[1, 1] = 0;
                t5[0, 1] = 1;
                grids.Add(t5);
            }
            {
                var t6 = new float[2, 2];
                t6[0, 0] = 1;
                t6[1, 0] = 1;
                t6[1, 1] = 0;
                t6[0, 1] = 0;
                grids.Add(t6);
            }
            {
                var t7 = new float[2, 2];
                t7[0, 0] = 1;
                t7[1, 0] = 0;
                t7[1, 1] = 1;
                t7[0, 1] = 0;
                grids.Add(t7);
            }
            {
                var t8 = new float[2, 2];
                t8[0, 0] = 1;
                t8[1, 0] = 0;
                t8[1, 1] = 0;
                t8[0, 1] = 1;
                grids.Add(t8);
            }
            {
                var t9 = new float[2, 2];
                t9[0, 0] = 0;
                t9[1, 0] = 1;
                t9[1, 1] = 1;
                t9[0, 1] = 0;
                grids.Add(t9);
            }
            {
                var t10 = new float[2, 2];
                t10[0, 0] = 0;
                t10[1, 0] = 1;
                t10[1, 1] = 0;
                t10[0, 1] = 1;
                grids.Add(t10);
            }
            {
                var t11 = new float[2, 2];
                t11[0, 0] = 0;
                t11[1, 0] = 0;
                t11[1, 1] = 1;
                t11[0, 1] = 1;
                grids.Add(t11);
            }
            {
                var t12 = new float[2, 2];
                t12[0, 0] = 1;
                t12[1, 0] = 1;
                t12[1, 1] = 1;
                t12[0, 1] = 0;
                grids.Add(t12);
            }
            {
                var t13 = new float[2, 2];
                t13[0, 0] = 1;
                t13[1, 0] = 0;
                t13[1, 1] = 1;
                t13[0, 1] = 1;
                grids.Add(t13);
            }
            {
                var t13 = new float[2, 2];
                t13[0, 0] = 1;
                t13[1, 0] = 1;
                t13[1, 1] = 0;
                t13[0, 1] = 1;
                grids.Add(t13);
            }
            {
                var t14 = new float[2, 2];
                t14[0, 0] = 0;
                t14[1, 0] = 1;
                t14[1, 1] = 1;
                t14[0, 1] = 1;
                grids.Add(t14);
            }
            {
                var t15 = new float[2, 2];
                t15[0, 0] = 1;
                t15[1, 0] = 1;
                t15[1, 1] = 1;
                t15[0, 1] = 1;
                grids.Add(t15);
            }

            for (int i = 0; i < grids.Count; ++i)
            {
                var mesh = m.Generate(grids[i], 1);
                var obj = GameObject.CreatePrimitive(PrimitiveType.Quad);
                var filter = obj.GetComponent<MeshFilter>();
                filter.sharedMesh = mesh;
                int v0 = Mathf.RoundToInt(grids[i][0, 0]);
                int v1 = Mathf.RoundToInt(grids[i][1, 0]);
                int v2 = Mathf.RoundToInt(grids[i][1, 1]);
                int v3 = Mathf.RoundToInt(grids[i][0, 1]);
                int val = v0 * 1 + v1 * 2 + v2 * 4 + v3 * 8;
                obj.name = $"Marching Square Mesh {val}";
            }
        }

        //grids value must in [0, 1] range
        public Mesh Generate(float[,] grids, float gridSize)
        {
#if true
            return GenerateSmooth(grids, gridSize);
#else
            return GenerateInteger(grids, gridSize);
#endif
        }

        Mesh GenerateInteger(float[,] grids, float gridSize)
        {
            int rows = grids.GetLength(0) - 1;
            int cols = grids.GetLength(1) - 1;
            List<Vector3> vertices = new List<Vector3>();
            for (int r = 0; r < rows; ++r)
            {
                for (int c = 0; c < cols; ++c)
                {
                    int v0 = Mathf.RoundToInt(grids[r, c]);
                    int v1 = Mathf.RoundToInt(grids[r + 1, c]);
                    int v2 = Mathf.RoundToInt(grids[r + 1, c + 1]);
                    int v3 = Mathf.RoundToInt(grids[r, c + 1]);
                    Vector2 c0 = (G2P(r, c, gridSize) + G2P(r, c + 1, gridSize)) * 0.5f;
                    Vector2 c1 = (G2P(r, c, gridSize) + G2P(r + 1, c, gridSize)) * 0.5f;
                    Vector2 c2 = (G2P(r + 1, c, gridSize) + G2P(r + 1, c + 1, gridSize)) * 0.5f;
                    Vector2 c3 = (G2P(r + 1, c + 1, gridSize) + G2P(r, c + 1, gridSize)) * 0.5f;
                    int val = v0 * 1 + v1 * 2 + v2 * 4 + v3 * 8;
                    switch (val)
                    {
                        case 1:
                            vertices.Add(c1);
                            vertices.Add(c0);
                            break;
                        case 2:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            break;
                        case 3:
                            vertices.Add(c0);
                            vertices.Add(c2);
                            break;
                        case 4:
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 5:
                            vertices.Add(c1);
                            vertices.Add(c0);
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 6:
                            vertices.Add(c1);
                            vertices.Add(c3);
                            break;
                        case 7:
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 8:
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 9:
                            vertices.Add(c1);
                            vertices.Add(c3);
                            break;
                        case 10:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 11:
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 12:
                            vertices.Add(c2);
                            vertices.Add(c0);
                            break;
                        case 13:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            break;
                        case 14:
                            vertices.Add(c0);
                            vertices.Add(c1);
                            break;
                    }
                }
            }

            List<int> indices = new List<int>();
            int vertexCount = vertices.Count;
            for (int i = 0; i < vertexCount; ++i)
            {
                indices.Add(i);
            }
            var mesh = new Mesh();
            mesh.SetVertices(vertices);
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            return mesh;
        }

        Mesh GenerateSmooth(float[,] grids, float gridSize)
        {
            int rows = grids.GetLength(0) - 1;
            int cols = grids.GetLength(1) - 1;
            List<Vector3> vertices = new List<Vector3>();
            for (int r = 0; r < rows; ++r)
            {
                for (int c = 0; c < cols; ++c)
                {
                    float v0 = grids[r, c];
                    float v1 = grids[r + 1, c];
                    float v2 = grids[r + 1, c + 1];
                    float v3 = grids[r, c + 1];
                    int i0 = Mathf.RoundToInt(v0);
                    int i1 = Mathf.RoundToInt(v1);
                    int i2 = Mathf.RoundToInt(v2);
                    int i3 = Mathf.RoundToInt(v3);
                    Vector2 c0 = Vector2.Lerp(G2P(r, c, gridSize), G2P(r, c + 1, gridSize), GetRatio(v0, v3, i0, i3));
                    Vector2 c1 = Vector2.Lerp(G2P(r, c, gridSize), G2P(r + 1, c, gridSize), GetRatio(v0, v1, i0, i1));
                    Vector2 c2 = Vector2.Lerp(G2P(r + 1, c, gridSize), G2P(r + 1, c + 1, gridSize), GetRatio(v1, v2, i1, i2));
                    Vector2 c3 = Vector2.Lerp(G2P(r + 1, c + 1, gridSize), G2P(r, c + 1, gridSize), GetRatio(v2, v3, i2, i3));
                    int val = i0 * 1 + i1 * 2 + i2 * 4 + i3 * 8;
                    switch (val)
                    {
                        case 1:
                            vertices.Add(c1);
                            vertices.Add(c0);
                            break;
                        case 2:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            break;
                        case 3:
                            vertices.Add(c0);
                            vertices.Add(c2);
                            break;
                        case 4:
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 5:
                            vertices.Add(c1);
                            vertices.Add(c0);
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 6:
                            vertices.Add(c1);
                            vertices.Add(c3);
                            break;
                        case 7:
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 8:
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 9:
                            vertices.Add(c1);
                            vertices.Add(c3);
                            break;
                        case 10:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            vertices.Add(c3);
                            vertices.Add(c0);
                            break;
                        case 11:
                            vertices.Add(c2);
                            vertices.Add(c3);
                            break;
                        case 12:
                            vertices.Add(c2);
                            vertices.Add(c0);
                            break;
                        case 13:
                            vertices.Add(c1);
                            vertices.Add(c2);
                            break;
                        case 14:
                            vertices.Add(c0);
                            vertices.Add(c1);
                            break;
                    }
                }
            }

            List<int> indices = new List<int>();
            int vertexCount = vertices.Count;
            for (int i = 0; i < vertexCount; ++i)
            {
                indices.Add(i);
            }
            var mesh = new Mesh();
            mesh.SetVertices(vertices);
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            return mesh;
        }

        Vector2 G2P(int r, int c, float gridSize)
        {
            return new Vector2(c * gridSize, r * gridSize);
        }

        float GetRatio(float a, float b, int ia, int ib)
        {
            return (0.5f - a) / (b - a);
        }
    }
}


#endif