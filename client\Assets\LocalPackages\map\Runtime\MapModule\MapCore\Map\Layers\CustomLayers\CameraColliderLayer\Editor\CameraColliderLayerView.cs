﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/12/4

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class CameraColliderLayerView : PolygonObjectLayerView
    {
        public CameraColliderLayerView(MapLayerData layerData, bool asyncLoading) : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new CameraColliderView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public void UpdateVertex(int objectDataID, int vertexIndex, Vector3 pos)
        {
            var view = GetObjectView(objectDataID) as CameraColliderView;
            if (view != null)
            {
                view.UpdateVertex(vertexIndex);
            }
        }
    }
}


#endif