﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class FadingTransition : IGroundLayerLODSwitchTransitionHandler
    {
        //duration:过渡时间
        //texture:平面的贴图
        //size:平面的大小
        //mapCenter:地图中心点
        //renderQueue:渲染顺序
        public FadingTransition(float duration, Texture2D texture, float size, Vector3 mapCenter, int renderQueue)
        {
            if (renderQueue == 0)
            {
                renderQueue = 3000;
            }
            mDuration = duration;
            mPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
            mPlane.name = "Fading Transition";
            GameObject.DestroyImmediate(mPlane.GetComponent<Collider>());
            mRenderer = mPlane.GetComponent<MeshRenderer>();
            mMaterial = new Material(Shader.Find("Unlit/GroundFading"));
            //初始不可见
            mRenderer.sharedMaterial = mMaterial;
            mRenderer.sharedMaterial.color = new Color(1, 1, 1, 0);
            mRenderer.sharedMaterial.SetTexture("_MainTex", texture);
            mRenderer.sharedMaterial.renderQueue = renderQueue;
            mPlane.transform.localScale = Vector3.one * size * 0.1f;
            mPlane.transform.position = mapCenter;
            mPlane.SetActive(false);
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mMaterial);
            Utils.DestroyObject(mPlane);
        }

        public void OnStartTransition(int lastLOD, int curLOD, float forceToCameraHeight, Vector3 newViewCenter)
        {
            if (curLOD > lastLOD)
            {
                mPlane.SetActive(true);

                if (forceToCameraHeight > 0)
                {
                    MapCameraMgr.MoveCameraToTarget(newViewCenter.x, newViewCenter.z, 13, 0, mDuration, null);
                }
            }
            MapCameraMgr.EnableCameraDrag(false);
            MapCameraMgr.EnableCameraZoom(false);
        }

        public void OnFinishTransition(int lastLOD, int curLOD, float forceToCameraHeight)
        {
            if (curLOD < lastLOD)
            {
                mPlane.SetActive(false);
            }
            MapCameraMgr.EnableCameraDrag(true);
            MapCameraMgr.EnableCameraZoom(true);
        }

        public bool UpdateTransition(int lastLOD, int curLOD, float percentage)
        {
            if (curLOD < lastLOD)
            {
                percentage = 1 - percentage;
            }
            SetAlpha(percentage);

            return false;
        }

        public float GetTransitionDuration()
        {
            return mDuration;
        }

        protected virtual void SetAlpha(float alpha)
        {
            var color = mRenderer.sharedMaterial.color;
            mRenderer.sharedMaterial.color = new Color(color.r, color.g, color.b, alpha);
        }

        float mDuration;
        GameObject mPlane;
        Renderer mRenderer;
        Material mMaterial;
    }
}
