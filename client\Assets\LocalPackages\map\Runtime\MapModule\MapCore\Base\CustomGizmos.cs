﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public static class CustomGizmos
    {
        public static void DrawCircle(Vector3 center, float radius, Quaternion rotation, Color color)
        {
            Gizmos.color = color;
#if true
            Gizmos.DrawWireSphere(center, radius);
#else
#if UNITY_EDITOR
            if (mCircleMesh == null)
            {
                CreateCircleMesh();
            }
            Gizmos.DrawMesh(mCircleMesh, center, rotation, Vector3.one * radius);
#endif
#endif
        }

        static void CreateCircleMesh()
        {
            Debug.Assert(mCircleMesh == null);
            mCircleMesh = new Mesh();
            int segment = 16;
            float deltaAngle = 360.0f / segment * Mathf.Deg2Rad;
            float angle = 0;

            Vector3[] positions = new Vector3[segment];
            Vector3[] normals = new Vector3[segment];
            int[] indices = new int[segment + 1];
            for (int i = 0; i < segment; ++i)
            {
                float x = Mathf.Cos(angle);
                float z = Mathf.Sin(angle);
                float y = 0;
                positions[i] = new Vector3(x, y, z);
                normals[i] = Vector3.up;
                angle += deltaAngle;

                indices[i] = i;
            }
            indices[segment] = 0;

            mCircleMesh.vertices = positions;
            mCircleMesh.SetIndices(indices, MeshTopology.LineStrip, 0);
        }

        static Mesh mCircleMesh;
    }
}
