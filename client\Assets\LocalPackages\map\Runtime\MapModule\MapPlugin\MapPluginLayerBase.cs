﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //插件地图层的基类
    public abstract class MapPluginLayerBase : MapLayerBase
    {
        public MapPluginLayerBase(Map map) :base (map) {
            mLODConfig = new MapLayerLODConfig(map);
        }

        //子类必须调用这个函数
        public void CreateGameObject(string name)
        {
            mName = name;
            mLayerID = map.nextCustomObjectID;
            mGameObject = new GameObject(name);
            mGameObject.transform.SetParent(map.view.root.transform);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async) { }

        //卸载地图层
        public override void Unload()
        {
            map.RemoveMapLayerByID(mLayerID);
            Utils.DestroyObject(mGameObject);
            mGameObject = null;
        }

        //修改lod
        protected bool UpdateLODStatus(float zoom)
        {
            bool lodChanged = false;
            if (mLODConfig != null)
            {
                var newLOD = mLODConfig.GetLODLevel(mCurrentLOD, zoom);
                if (newLOD != mCurrentLOD)
                {
                    //改变lod
                    mLastLOD = mCurrentLOD;
                    mCurrentLOD = newLOD;
                    lodChanged = true;
                }
            }
            return lodChanged;
        }

        //返回当前的lod
        public override int GetCurrentLOD()
        {
            return mCurrentLOD;
        }

        public override bool Contains(int objectID) { return false; }
        public override MapLayerData GetLayerData() { return null; }
        public override MapLayerView GetLayerView() { return null; }
        public override int id { get { return mLayerID; } }
        public override bool active { get { return mGameObject.activeSelf; } set { mGameObject.SetActive(value); } }
        public override GameObject gameObject { get { return mGameObject; } }
        public override string name { get { return mName; } }
        public override GridType gridType { get { return GridType.Rectangle; } }
        public override Vector3 layerOffset { get { return Vector3.zero; } }
        public override int lodCount { get { return mLODConfig.lodConfigs.Length; } }
        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            return false;
        }
        //返回地图层的总宽度
        public override float GetTotalWidth()
        {
            return tileWidth * horizontalTileCount;
        }
        //返回地图层的总高度
        public override float GetTotalHeight()
        {
            return tileHeight * verticalTileCount;
        }

        //读取游戏运行时数据接口
        public abstract void LoadGameData();
        //导出游戏数据接口
        public abstract void SaveGameData(string mapRuntimDataFolder);
        //保存地图编辑器数据接口
        public abstract void SaveEditorData(string mapEditorDataFolder);
        //读取地图编辑器数据接口
        public abstract bool LoadEditorData(string mapEditorDataFolder);

        public MapPluginLayerInfo pluginLayerInfo { set { mPluginLayerInfo = value; } get { return mPluginLayerInfo; } }
        public MapLayerLODConfig lodConfig { get { return mLODConfig; } set { mLODConfig = value; } }

        int mLayerID;
        GameObject mGameObject;
        string mName;
        MapPluginLayerInfo mPluginLayerInfo;

        int mCurrentLOD = 0;
        int mLastLOD = 0;
        MapLayerLODConfig mLODConfig;
    }
}
