﻿ 



 
 


using System.Collections.Generic;

namespace TFW.Map.Nav
{
    // 优先队列节点，包括id和权重
    public struct WeightedNode
    {
        public int idx;     // NodeHeap index
        public int ID;      // edge id
        public uint weight; // 边与边的距离(初始时是边的中心点之间的距离)

        public WeightedNode(int id, uint weight, int idx)
        {
            this.idx = idx;
            this.ID = id;
            this.weight = weight;
        }
    }

    // 优先队列
    public class NodeHeap : heap.Interface
    {
        List<WeightedNode> nodes; // 节点队列
        Dictionary<int, WeightedNode> indices = new Dictionary<int, WeightedNode>(); // id -> node

        public NodeHeap(int length)
        {
            nodes = new List<WeightedNode>(length);
        }

        // Reset reset node
        public void Reset()
        {
            nodes.Clear();
            indices.Clear();
        }

        public int Len()
        {
            return nodes.Count;
        }

        public bool Less(int i, int j)
        {
            return nodes[i].weight < nodes[j].weight;
        }

        public void Swap(int i, int j)
        {
            var t1 = nodes[i];
            var t2 = nodes[j];
            nodes[i] = new WeightedNode(t2.ID, t2.weight, i);
            nodes[j] = new WeightedNode(t1.ID, t1.weight, j);
            indices[t1.ID] = nodes[j];
            indices[t2.ID] = nodes[i];
        }

        public void Push(WeightedNode node)
        {
            nodes.Add(node);
            node.idx = nodes.Count - 1;
            indices[node.ID] = node;
        }

        public WeightedNode Pop()
        {
            var n = nodes.Count;
            var x = nodes[n - 1];
            nodes.RemoveAt(n - 1);
            indices.Remove(x.ID);
            return x;
        }

        // 调整权重
        public void DecreaseKey(int id, uint weight, int idx)
        {
            WeightedNode node;
            bool ok = indices.TryGetValue(id, out node);
            if (ok)
            {
                node.weight = weight;
                heap.HeapUtils.Fix(this, node.idx);
            }
            else
            {
                heap.HeapUtils.Push(this, new WeightedNode(id, weight, idx));
            }
        }
    }
}