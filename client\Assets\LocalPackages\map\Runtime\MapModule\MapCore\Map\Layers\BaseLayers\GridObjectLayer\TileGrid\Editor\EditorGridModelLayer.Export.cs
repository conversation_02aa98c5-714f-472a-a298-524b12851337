﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class EditorGridModelLayer : MapLayerBase
    {
        public void Export(string dataPath)
        {
            List<PrefabInitInfo1> allPrefabsInfo = new List<PrefabInitInfo1>();
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    var tileData = GetObjectData(j, i);
                    if (tileData != null)
                    {
                        var modelTemplate = tileData.GetModelTemplate();
                        int nLODs = modelTemplate.lodCount;
                        for (int lod = 0; lod < nLODs; ++lod)
                        {
                            var childPrefabTransforms = modelTemplate.GetChildPrefabTransform(lod);
                            var tileStartPos = FromCoordinateToWorldPosition(j, i);
                            CalculateUniquePrefabInfo(tileStartPos, childPrefabTransforms, allPrefabsInfo);
                        }
                    }
                }
            }

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            //------------------------------version 1 start------------------------------------
            writer.Write(VersionSetting.OptimizedFrontLayerDataVersion);

            int nPrefabs = allPrefabsInfo.Count;
            writer.Write(nPrefabs);
            for (int i = 0; i < nPrefabs; ++i)
            {
                Utils.WriteString(writer, allPrefabsInfo[i].prefabPath);
                writer.Write(allPrefabsInfo[i].boundsMinX);
                writer.Write(allPrefabsInfo[i].boundsMinZ);
                writer.Write(allPrefabsInfo[i].boundsWidth);
                writer.Write(allPrefabsInfo[i].boundsHeight);
                Utils.WriteQuaternion(writer, allPrefabsInfo[i].rotation);
                Utils.WriteVector3(writer, allPrefabsInfo[i].scale);
                writer.Write(allPrefabsInfo[i].y);
            }

            //save tiles
            //key is model template id, value is tile id
            Dictionary<int, int> modelTemplateToOptimizedTileData = CalculateUniqueOptimizedTileData();
            //key is tile id, value is index 
            Dictionary<int, int> tileDataIndices = new Dictionary<int, int>();

            int nTiles = modelTemplateToOptimizedTileData.Count;
            writer.Write(nTiles);
            int idx = 0;
            foreach (var p in modelTemplateToOptimizedTileData)
            {
                var modelTemplate = Map.currentMap.FindObject(p.Key) as ModelTemplate;
                //save id
                int tileID = p.Value;
                writer.Write(tileID);

                tileDataIndices[tileID] = idx;
                ++idx;

                int nLODs = modelTemplate.lodCount;
                writer.Write(nLODs);
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabTransforms = modelTemplate.GetChildPrefabTransform(lod);
                    //save prefab path
                    string prefabPath = modelTemplate.GetLODPrefabPath(lod);
                    Utils.WriteString(writer, prefabPath);

                    int nObjectsInThisLOD = childPrefabTransforms.Count;
                    long offset = writer.BaseStream.Position;
                    int validObjectCount = 0;
                    writer.Write(nObjectsInThisLOD);

                    for (int k = 0; k < nObjectsInThisLOD; ++k)
                    {
                        var childPrefabTransform = childPrefabTransforms[k];
                        if (childPrefabTransform.tag != MapCoreDef.IGNORED_OBJECT_TAG && childPrefabTransform.prefabInitInfoIndex >= 0)
                        {
                            ++validObjectCount;
                            writer.Write(childPrefabTransform.position.x);
                            writer.Write(childPrefabTransform.position.z);
                            writer.Write(childPrefabTransform.prefabInitInfoIndex);
                            writer.Write((byte)childPrefabTransform.objectType);
                        }
                    }

                    Utils.WriteAndJump(writer, offset, validObjectCount);
                }
            }
            Debug.Assert(idx == modelTemplateToOptimizedTileData.Count);

            int rows = verticalTileCount;
            int cols = horizontalTileCount;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    int index = -1;
                    var tileData = GetObjectData(j, i);
                    if (tileData != null)
                    {
                        var modelTemplateID = tileData.GetModelTemplateID();

                        int tileID;
                        bool found = modelTemplateToOptimizedTileData.TryGetValue(modelTemplateID, out tileID);
                        if (found)
                        {
                            found = tileDataIndices.TryGetValue(tileID, out index);
                            Debug.Assert(found);
                        }
                    }
                    writer.Write(index);
                }
            }
            //------------------------------version 1 end------------------------------------

            //------------------------------version 2 start------------------------------------
            for (int i = 0; i < nPrefabs; ++i)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(allPrefabsInfo[i].prefabPath);
                short maxVisibleQuality = short.MaxValue;
                if (prefab != null)
                {
                    var qualityControl = prefab.GetComponentInChildren<ObjectLoadingControl>();
                    if (qualityControl != null)
                    {
                        maxVisibleQuality = qualityControl.maxVisibleQuality;
                    }
                }
                writer.Write(maxVisibleQuality);
            }
            //------------------------------version 2 end------------------------------------

            //------------------------------version 3 start------------------------------------
            //导出tile是否需要update object
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    bool dontUpdateTileBigObjectCulling = IfDoNotUpdateTileBigObjectCulling(j, i);
                    writer.Write(dontUpdateTileBigObjectCulling);
                }
            }
            //------------------------------version 3 end------------------------------------

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        bool IfDoNotUpdateTileBigObjectCulling(int x, int y)
        {
            bool needUpdate = false;
            int nLODs = mLayerData.lodCount;

            var bigTileData = mLayerData.GetObjectData(x, y);
            var bigTilePos = mLayerData.FromCoordinateToWorldPosition(x, y);
            float mapWidth = Map.currentMap.mapWidth;
            float mapHeight = Map.currentMap.mapHeight;
            if (bigTileData != null)
            {
                var modelTemplate = bigTileData.GetModelTemplate();
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabTransforms = modelTemplate.GetChildPrefabTransform(lod);
                    for (int i = 0; i < childPrefabTransforms.Count; ++i)
                    {
                        var bounds = childPrefabTransforms[i].localBoundsInPrefab;
                        var pos = childPrefabTransforms[i].position + bigTilePos;
                        bool isInWorld = pos.x >= 0 && pos.x < mapWidth && pos.z >= 0 && pos.z < mapHeight;

                        bool useCullManager = bounds.width <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && bounds.height <= MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE && isInWorld;
                        //现在只管理山的视野,因为山太大了,不适合用管理小件物体的方法管理
                        if (!useCullManager && childPrefabTransforms[i].objectType != TileObjectType.AlwaysVisible)
                        {
                            needUpdate = true;
                            break;
                        }
                    }
                }
            }

            return !needUpdate;
        }

        Dictionary<int, int> CalculateUniqueOptimizedTileData()
        {
            Dictionary<int, int> modelTemplateToOptimizedTileData = new Dictionary<int, int>();
            int startID = 1;
            int rows = verticalTileCount;
            int cols = horizontalTileCount;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tileData = GetObjectData(j, i);
                    if (tileData != null)
                    {
                        int modelTemplateID = tileData.GetModelTemplateID();
                        int tileID;
                        bool found = modelTemplateToOptimizedTileData.TryGetValue(modelTemplateID, out tileID);
                        if (!found)
                        {
                            tileID = startID;
                            ++startID;
                            modelTemplateToOptimizedTileData[modelTemplateID] = tileID;
                        }
                    }
                }
            }

            return modelTemplateToOptimizedTileData;
        }

        void CalculateUniquePrefabInfo(Vector3 tileStartPos, List<ChildPrefabTransform> objectsInLOD, List<PrefabInitInfo1> allPrefabInfo)
        {
            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            int nObjectsInThisLOD = objectsInLOD.Count;
            for (int k = 0; k < nObjectsInThisLOD; ++k)
            {
                string childPrefabPath = objectsInLOD[k].path;

                //find if have same prefab info
                int prefabInitInfoIndex = -1;
                var pos = objectsInLOD[k].position;
                if (blendTerrainLayer != null)
                {
                    float terrainHeight = blendTerrainLayer.GetHeightAtPos(tileStartPos.x + pos.x, tileStartPos.z + pos.z);
                    if (terrainHeight != 0)
                    {
                        pos.y += terrainHeight;
                    }
                }

                var rot = objectsInLOD[k].editorRotation;
                var scale = objectsInLOD[k].editorScaling;
                for (int i = 0; i < allPrefabInfo.Count; ++i)
                {
                    if (
                        allPrefabInfo[i].prefabPath == childPrefabPath &&
                        allPrefabInfo[i].rotation == rot &&
                        allPrefabInfo[i].scale == scale &&
                        Mathf.Approximately(allPrefabInfo[i].y, pos.y))
                    {
                        prefabInitInfoIndex = i;
                        break;
                    }
                }

                if (prefabInitInfoIndex == -1)
                {
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(childPrefabPath);
                    if (childPrefab != null)
                    {
                        var childBounds = GameObjectBoundsCalculator.CalculateRect(childPrefab, rot, scale);
                        var prefabInfo = new PrefabInitInfo1(childPrefabPath, childBounds.xMin, childBounds.yMin, childBounds.width, childBounds.height, rot, scale, pos.y);
                        allPrefabInfo.Add(prefabInfo);
                        prefabInitInfoIndex = allPrefabInfo.Count - 1;
                    }
                }

                Debug.Assert(prefabInitInfoIndex < 50000);
                objectsInLOD[k].prefabInitInfoIndex = (short)prefabInitInfoIndex;
            }
        }
    }
}

#endif