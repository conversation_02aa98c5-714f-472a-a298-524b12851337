﻿ 



 
 

﻿//是否快速索引一个instance对应的index,用于快速删除物体,避免遍历array
#define USE_SLOT

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class FixedArray<T> where T : class
    {
        public FixedArray(int capacity)
        {
            mInstances = new T[capacity];
#if USE_SLOT
            mSlotMap = new Dictionary<T, int>(capacity);
#endif
        }

        public void Add(T inst)
        {
            if (mSize >= mInstances.Length)
            {
                var newInstances = new T[mInstances.Length * 2];
                mInstances.CopyTo(newInstances, 0);
                mInstances = newInstances;
            }

            mInstances[mSize] = inst;
#if USE_SLOT
            mSlotMap.Add(inst, mSize);
#endif
            ++mSize;            
        }

        public void Remove(T inst)
        {
            int slot = -1;
#if USE_SLOT
            mSlotMap.TryGetValue(inst, out slot);
#else
            for (int i = 0; i < mSize; ++i){
                if (ReferenceEquals(mInstances[i], inst))
                {
                    slot = i;
                    break;
                }
            }
#endif
#if UNITY_EDITOR
            Debug.Assert(slot >= 0, "invalid slot");
#endif
            if (ReferenceEquals(mInstances[slot], inst))
            {
                if (slot != size - 1)
                {
                    mInstances[slot] = mInstances[mSize - 1];
#if USE_SLOT
                    mSlotMap[mInstances[slot]] = slot;
#endif
                }
                mInstances[mSize - 1] = null;
#if USE_SLOT
                mSlotMap.Remove(inst);
#endif
                --mSize;
            }
        }

        public T[] instances { get { return mInstances; } }
        public int size { get { return mSize; } }

        T[] mInstances;
        int mSize;
#if USE_SLOT
        Dictionary<T, int> mSlotMap;
#endif
    }
}

