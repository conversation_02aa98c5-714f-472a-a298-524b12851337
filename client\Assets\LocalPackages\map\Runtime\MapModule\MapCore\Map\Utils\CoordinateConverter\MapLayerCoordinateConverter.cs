﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    //负责在格子坐标和世界坐标之间转换
    public abstract class MapLayerCoordinateConverter {
        public void SetLayerData(MapLayerData layerData) {
            mLayerData = layerData;
            OnSetLayer();
        }
        public abstract Vector2Int FromWorldPositionToCoordinate(Vector3 position);
        public abstract Vector2Int FromWorldPositionToCoordinateUpperBounds(Vector3 position);
        public virtual Vector2Int FromScreenToCoordinate(Vector3 screenPos, Camera camera) {
            var worldPos = Utils.FromScreenToWorldPosition(screenPos, camera);
            return FromWorldPositionToCoordinate(worldPos);
        }
    
        public abstract Vector3 FromCoordinateToWorldPosition(int x, int y);
        public abstract Vector3 FromCoordinateToWorldPositionCenter(int x, int y);
        public abstract Vector3 FromCoordinateToWorldPositionCenter(int x, int y, int width, int height);

        public virtual float GetLayerWidthInMeter(int width) {
            if (width == 0) {
                width = mLayerData.horizontalTileCount;
            }
            return mLayerData.tileWidth * width;
        }

        public virtual float GetLayerHeightInMeter(int height) {
            if (height == 0) {
                height = mLayerData.verticalTileCount;
            }
            return mLayerData.tileHeight * height;
        }

        protected virtual void OnSetLayer() { }

        protected MapLayerData mLayerData;
        static int[] dx = { 1, 0, -1, -1, -1, 0, 1, 1 };
        static int[] dy = { -1, -1, -1, 0, 1, 1, 1, 0 };
    };
}
