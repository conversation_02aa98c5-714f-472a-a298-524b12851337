﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    public class PrefabSubGroupEditor : EditorWindow
    {
        class Entry
        {
            public string name;
            public GameObject prefab;
        }

        void OnDisable()
        {
            Reset();
            mPrefabCount = 0;
        }

        public void Show(int prefabIndex, string[] prefabs, System.Action<int, int, string> changePrefabCallback, System.Action<int, int> changePrefabCountCallback)
        {
            Debug.Assert(prefabs != null);
            mPrefabPaths = new Entry[prefabs.Length];
            for (int i = 0; i < prefabs.Length; ++i)
            {
                mPrefabPaths[i] = new Entry();
                mPrefabPaths[i].name = $"Prefab {i}";
                mPrefabPaths[i].prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabs[i]);
            }
            mChangePrefabCallback = changePrefabCallback;
            mChangePrefabCountCallback = changePrefabCountCallback;
            mPrefabIndex = prefabIndex;
            mPrefabCount = prefabs.Length;
        }

        void Reset()
        {
            mPrefabPaths = null;
        }

        void OnGUI()
        {
            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Variant Prefab Count", mPrefabCount.ToString());
            if (GUILayout.Button("Change"))
            {
                var dlg = EditorUtils.CreateInputDialog("Set Variant Prefab Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Prefab Count", "", "4"),
                };
                dlg.Show(items, OnClickChangeCount);
            }
            EditorGUILayout.EndHorizontal();

            if (mPrefabCount > 0)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                for (int i = 0; i < mPrefabCount; ++i)
                {
                    if (i > 0)
                    {
                        DrawPrefab(i);
                    }
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndScrollView();
        }

        void DrawPrefab(int i)
        {
            if (mPrefabPaths != null)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal();
                var prefab = EditorGUILayout.ObjectField(mPrefabPaths[i].name, mPrefabPaths[i].prefab, typeof(GameObject), false, null) as GameObject;
                if (prefab != null)
                {
                    var type = PrefabUtility.GetPrefabAssetType(prefab);
                    if (type == PrefabAssetType.Regular || type == PrefabAssetType.Variant)
                    {
                        if (mPrefabPaths[i].prefab != prefab)
                        {
                            bool found = false;
                            for (int k = 0; k < mPrefabPaths.Length; ++k)
                            {
                                if (mPrefabPaths[k].prefab == prefab)
                                {
                                    EditorUtility.DisplayDialog("Error", $"{prefab.name} is already added to list!", "OK");
                                    found = true;
                                    break;
                                }
                            }

                            if (!found)
                            {
                                mPrefabPaths[i].prefab = prefab;
                                if (mChangePrefabCallback != null)
                                {
                                    mChangePrefabCallback(mPrefabIndex, i, AssetDatabase.GetAssetPath(prefab));
                                }
                            }
                        }
                    }
                }
                else if (prefab == null)
                {
                    mPrefabPaths[i].prefab = null;
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
            }
        }

        void SetPrefabCount(int prefabCount)
        {
            if (mPrefabPaths == null || prefabCount != mPrefabPaths.Length)
            {
                Entry[] newEntries = new Entry[prefabCount];
                for (int i = 0; i < prefabCount; ++i)
                {
                    newEntries[i] = new Entry();
                }
                int n = Mathf.Min(prefabCount, mPrefabCount);
                int next = 0;
                for (int i = 0; i < n; ++i)
                {
                    newEntries[i].prefab = mPrefabPaths[i].prefab;
                    newEntries[i].name = mPrefabPaths[i].name;
                    ++next;
                }
                int delta = prefabCount - mPrefabCount;
                if (delta > 0)
                {
                    for (int i = 0; i < delta; ++i)
                    {
                        newEntries[next].prefab = null;
                        newEntries[next].name = $"Prefab {next}";
                        ++next;
                    }
                }

                mPrefabPaths = newEntries;
                mPrefabCount = prefabCount;
                if (mChangePrefabCountCallback != null)
                {
                    mChangePrefabCountCallback(mPrefabIndex, prefabCount);
                }
            }
        }

        bool OnClickChangeCount(List<InputDialog.Item> param)
        {
            int prefabCount;
            Utils.ParseInt((param[0] as InputDialog.StringItem).text, out prefabCount);
            if (prefabCount <= 0)
            {
                return false;
            }

            SetPrefabCount(prefabCount);
            return true;
        }

        public List<string> GetPrefabPaths()
        {
            List<string> paths = new List<string>();
            if (mPrefabPaths != null)
            {
                for (int i = 0; i < mPrefabPaths.Length; ++i)
                {
                    paths.Add(AssetDatabase.GetAssetPath(mPrefabPaths[i].prefab));
                }
            }
            return paths;
        }

        int mPrefabCount = 0;
        Entry[] mPrefabPaths;
        Vector2 mScrollPos;
        int mPrefabIndex = 0;
        System.Action<int, int, string> mChangePrefabCallback;
        System.Action<int, int> mChangePrefabCountCallback;
    }
}
#endif
