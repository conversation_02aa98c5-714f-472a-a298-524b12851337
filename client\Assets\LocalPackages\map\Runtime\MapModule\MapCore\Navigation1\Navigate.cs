﻿ 



 
 


using System.Collections.Generic;
using TFW.Map.Geo;
using UnityEngine;

namespace TFW.Map.Nav
{
    //三角形的属性配置
    public class TriangleTypeSetting
    {
        public TriangleTypeSetting(ushort typeID, bool walkable)
        {
            this.typeID = typeID;
            this.walkable = walkable;
        }

        public ushort typeID;
        public bool walkable = true;
    }

    // NaviMgr 导航管理器
    /*
    寻路管理器包含
    1. 网格地图
    2. 邻接表构成的通路矩阵
    3. 四叉树存储三角形
    3. 寻找通路算法
    */
    public class NaviMgr
    {
        Mesh Mesh;
        Matrix Graph;
        PathAlgorithm Algo;
        List<Edge> pathEdges = new List<Edge>();

        // 新建寻路管理器
        public static NaviMgr Create(int minX, int maxX, int width, int height, MeshCfg meshCfg, int searchGridSize)
        {
            var nav = new NaviMgr();
            nav.Mesh = new Mesh(minX, maxX, width, height, searchGridSize);
            nav.Graph = new Matrix();

            if (meshCfg != null)
            {
                // 三角形网格信息和四叉树
                nav.Mesh.Init(meshCfg);
                nav.Graph.CreateMatrix(nav.Mesh); // 三角形边 凸多边形查找
            }
            // 设置寻路算法
            nav.Algo = new VisibleAStar(nav.Graph.nodes.Length + 2); // 边算法
            nav.pathEdges = new List<Edge>(nav.Graph.nodes.Length + 2);
            nav.mesh.Clear();

            Mesh.currentMeshVertices = nav.mesh.vertices;
            return nav;
        }

        public static void SetCurrentNavMgr(NaviMgr mgr)
        {
            Mesh.currentMeshVertices = mgr.mesh.vertices;
        }

        public void OnDestroy()
        {
            Mesh.OnDestroy();
        }

        public void ShowNavMeshRegions(bool show)
        {
            Mesh.ShowNavMeshRegions(show);
        }

        // 获取多边形序号,兼容三角形和凸多边形
        int GetPolygonIndex(Coord pt, TriangleTypeSetting[] typeSetting)
        {
            return Mesh.GetPolygonIndexByCoord(pt, Mesh.polygonGrid, typeSetting);
        }

        public bool IsPointInNavMesh(Coord coord)
        {
            return GetPolygonIndex(coord, mesh.triangleSettings) != Const.StubIndex;
        }

        public Vector2 FindEstimatedWalkablePosition(Vector2 startPosition, Vector2 targetPosition, float testDistance, int maxTryCount = 50, bool tryToGetMoreAccurateResult = true, int maxTryCountForInnerSearch = 10)
        {
            Vector2 dir = startPosition - targetPosition;
            dir.Normalize();
            for (int i = 0; i < maxTryCount; ++i)
            {
                bool valid = IsPointInNavMesh(Geo.GeoUtils.Vector2ToCoord(targetPosition));
                if (valid)
                {
                    if (tryToGetMoreAccurateResult)
                    {
                        Vector2 innerSearchCurPos = targetPosition;
                        Vector2 innerSearchValidPosition = targetPosition;
                        dir = -dir;
                        float delta = testDistance / maxTryCountForInnerSearch;
                        for (int j = 0; j < maxTryCountForInnerSearch; ++j)
                        {
                            innerSearchCurPos += -dir * delta;
                            valid = IsPointInNavMesh(Geo.GeoUtils.Vector2ToCoord(innerSearchCurPos));
                            if (!valid)
                            {
                                return innerSearchValidPosition;
                            }
                            innerSearchValidPosition = innerSearchCurPos;
                        }
                    }
                    return targetPosition;
                }

                targetPosition = targetPosition + dir * testDistance;
            }
            return targetPosition;
        }

        // 寻路函数接口
        /**
        寻路大体分为3个步骤：
        1. 使用算法寻找最短的网格通路
        2. 计算通过所有网格经过的边
        3. 找到一条通过所有边的最短路线
        */
        public List<Coord> Route(Coord srcCoord, Coord dstCoord)
        {
            // 获取源和目的的三角形的序号
            // index表示序号，id表示节点编号
            int srcIndex, dstIndex;
            srcIndex = GetPolygonIndex(srcCoord, mesh.triangleSettings);
            dstIndex = GetPolygonIndex(dstCoord, mesh.triangleSettings);

            if (srcIndex == Const.StubIndex || dstIndex == Const.StubIndex)
            {
                return new List<Coord>();
            }
            if (srcIndex == dstIndex)
            {
                return new List<Coord> { srcCoord, dstCoord };
            }

            var path1 = FindPath(srcCoord, dstCoord, srcIndex, dstIndex, mesh.triangleSettings);
            if (path1 == null)
            {
                return mDummy;
            }

            List<Edge> edgeList = GetPathEdges(path1, srcCoord, dstCoord);
            var ways = new List<Coord> { srcCoord };
            FindWay(edgeList, ways, 0, srcCoord, dstCoord);

            return ways;
        }

        //check if a circle is intersected with any obstacle
        public List<int> GetIntersectedRegionIDs(float centerX, float centerZ, float radius)
        {
            List<int> result = null;
            var regions = mesh.navMeshRegions;
            for (int i = 0; i < regions.Length; ++i)
            {
                bool intersected = regions[i].quadTree.IsTriangleIntersectedWithCircle(centerX, centerZ, radius);
                if (intersected)
                {
                    if (result == null)
                    {
                        result = new List<int>();
                    }

                    result.Add(regions[i].regionID);
                }
            }
            return result;
        }

        public int GetIntersectedRegionID(float centerX, float centerZ)
        {
            var regions = mesh.navMeshRegions;
            for (int i = 0; i < regions.Length; ++i)
            {
                bool intersected = regions[i].quadTree.IsTriangleIntersectedWithPoint(centerX, centerZ);
                if (intersected)
                {
                    return regions[i].regionID;
                }
            }
            return 0;
        }

        //设置三角形的状态
        public void SetRegionState(int regionID, bool enable)
        {
            if (regionID >= 0 && regionID < triangleSettings.Length)
            {
                triangleSettings[regionID].walkable = enable;
            }
        }

        public bool GetRegionState(int regionID)
        {
            if (regionID >= 0 && regionID < triangleSettings.Length)
            {
                return triangleSettings[regionID].walkable;
            }
            return true;
        }

        // 算法搜索最近的通路，返回经过的边的逆序列表
        int[] FindPath(Coord srcCoord, Coord dstCoord, int srcPolygonIndex, int dstPolygonIndex, TriangleTypeSetting[] typeSetting)
        {
            // 经过的顶点编号
            int[] path = null;
            if (srcPolygonIndex != dstPolygonIndex)
            {
                // 起点和终点属于特殊的边，加入特殊的index处理
                Mesh.GenVirtualEdges(Const.StartEdgeID, srcCoord);
                Mesh.GenVirtualEdges(Const.EndEdgeID, dstCoord);
                // 调整矩阵
                Graph.AdjustEdgeMartix(Mesh, srcPolygonIndex, Const.StartEdgeID, srcCoord);
                Graph.AdjustEdgeMartix(Mesh, dstPolygonIndex, Const.EndEdgeID, dstCoord);
                // 寻路
                path = Algo.FindPath(Mesh, Graph, Const.StartEdgeID, Const.EndEdgeID, typeSetting);

                // 撤销调整
                Graph.RevertEdgeMatrix(Mesh, srcPolygonIndex);
                Graph.RevertEdgeMatrix(Mesh, dstPolygonIndex);
            }

            return path;
        }

        // 逆序生成通过的边
        List<Edge> GetPathEdges(int[] path, Coord srcCoord, Coord dstCoord)
        {
            var mesh = Mesh;
            pathEdges.Clear();

            // 逆向查找
            var prevID = Const.EndEdgeID;
            while (true)
            {
                var curID = path[prevID];
                if (curID == Const.StubIndex || curID == Const.StartEdgeID)
                {
                    break;
                }
                var curEdge = mesh.edges[curID];
                if (curEdge != null)
                {
                    pathEdges.Add(curEdge);
                }
                prevID = curID;
            }
            int i = 0;
            int j = pathEdges.Count - 1;
            for (; i < j; i = i + 1, j = j - 1)
            {
                var temp = pathEdges[i];
                pathEdges[i] = pathEdges[j];
                pathEdges[j] = temp;
            }
            // 加入终点
            var newEdge = new Edge();
            newEdge.Vertice0 = new Vertice(Const.StubIndex, dstCoord);
            newEdge.Vertice1 = new Vertice(Const.StubIndex, dstCoord);
            newEdge.Inflect0 = new Vertice(0, dstCoord);
            newEdge.Inflect1 = new Vertice(0, dstCoord);
            pathEdges.Add(newEdge);

            return pathEdges;
        }

        // 根据通过的边确定一条最短路径
        // 返回的路径表里包括起点，终点和一些列拐点，起点和终点都是确定的，关键是找到一系列拐点
        // 返回找到的第一个 没有经过偏移的拐点
        Coord FindWay(List<Edge> edgeList, List<Coord> ways, int startEdge, Coord startCoord, Coord endCoord)
        {
            if (edgeList.Count == 0)
            {
                return new Coord();
            }
            if (startCoord.IsEqual(endCoord))
            {
                return new Coord();
            }
            // 找到拐点
            int edgeIdx;
            var inflectVertice = FindNextInflectVertice(edgeList, startEdge, startCoord, out edgeIdx);
            // 直接连接终点
            if (inflectVertice == null)
            {
                ways.Add(endCoord);
                return endCoord;
            }
            // 递归寻找下一个拐点
            // idx := len(*ways)
            var coord = inflectVertice.GetVertice().Coord;
            ways.Add(coord);

            FindWay(edgeList, ways, inflectVertice.EdgeIdx + 1, coord, endCoord);
            return coord;
        }

        Vector1 FindNextInflectVertice(List<Edge> edgeList, int startEdge, Coord curCoord, out int edgeIndex)
        {
            // 初始左向量和右向量
            edgeIndex = 0;
            Vector1 curLeftVector = null, curRightVector = null;
            for (int i = startEdge; i < edgeList.Count; i++)
            {
                var e = edgeList[i];
                Vector1 leftVector, rightVector;
                GeoUtils.GenLeftAndRightVector(curCoord, e, i, out leftVector, out rightVector);
                // 如果左向量和右向量共线，那边继续寻找下一个边作为出发边
                // Index为-1表示不存在的顶点，比如将终点虚拟成了一个顶点
                if (e.Vertice0.Index != Const.StubIndex && leftVector.Vec.Cross(rightVector.Vec) == 0)
                {
                    continue;
                }
                if (curLeftVector == null || curRightVector == null)
                {
                    // 获得初始向量
                    GeoUtils.GenLeftAndRightVector(curCoord, e, i, out curLeftVector, out curRightVector);
                }
                else
                {
                    bool find;
                    var inflectVertice = FindInflectVertice(ref curLeftVector, ref curRightVector, leftVector, rightVector, out find);
                    if (find)
                    {
                        edgeIndex = inflectVertice.EdgeIdx;
                        return inflectVertice;
                    }
                }
            }
            return null;
        }

        // 寻找拐的顶点
        // 根据传入的两条左右向量，和当前左右向量分别比较位置关系，找出拐点，如果没有拐点，则更新当前左右向量
        Vector1 FindInflectVertice(ref Vector1 curLVec, ref Vector1 curRVec, Vector1 lVec, Vector1 rVec, out bool found)
        {
            // flags 标记向量之间的cross
            //    | l | r
            // cl |
            // cr |
            long v00, v01, v10, v11;
            found = false;
            v00 = curLVec.Vec.Cross(lVec.Vec);
            v01 = curLVec.Vec.Cross(rVec.Vec);
            v10 = curRVec.Vec.Cross(lVec.Vec);
            v11 = curRVec.Vec.Cross(rVec.Vec);

            // 两条向量都位于当前左边向量的左侧，那么当前左边向量的终点为拐点
            if (v00 > 0 && v01 > 0)
            {
                found = true;
                return curLVec;
            }
            // 两条向量都位于当前右边向量的右侧，那么当前右边向量的终点为拐点
            if (v10 < 0 && v11 < 0)
            {
                found = true;
                return curRVec;
            }
            // 如果左向量位于当前左向量的右边且位于当前右向量的左边，则说明位于两者之间
            if (v00 <= 0 && v10 >= 0)
            {
                curLVec = lVec; // 更新当前左边向量
            }
            // 如果右向量位于当前左向量的右边且位于当前右向量的左边，则说明位于两者之间
            if (v01 <= 0 && v11 >= 0)
            {
                curRVec = rVec; // 更新当前右边边向量
            }
            return null;
        }

        public Nav.TriangleTypeSetting[] triangleSettings { get { return mesh.triangleSettings; } }

        public Mesh mesh { get { return Mesh; } }
        public Matrix graph { get { return Graph; } }
        public PathAlgorithm algo { get { return Algo; } }
        public List<Edge> edges { get { return pathEdges; } }

        List<Coord> mDummy = new List<Coord>();
    }
}