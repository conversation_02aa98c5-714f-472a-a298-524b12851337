﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public interface ISpawnPointGenerationStrategy
    {
        List<Vector2> Generate(MapRegion region, float mapRadius, RandomWrapper r, TriangleNet.TrianglePool pool);
        SpawnPointGenerateStrategyType strategy { get; }
    }

    //随机生成某个区域内的npc刷新点
    public class GenerateSpawnPointsRandomly : ISpawnPointGenerationStrategy
    {
        public GenerateSpawnPointsRandomly(float density, float maxMoveRange, int oneMeterHorizontalUnitCount, int oneMeterVerticalUnitCount)
        {
            mDensity = density;
            mMaxMoveRange = maxMoveRange;
            mOneMeterHorizontalUnitCount = oneMeterHorizontalUnitCount;
            mOneMeterVerticalUnitCount = oneMeterVerticalUnitCount;
        }

        public List<Vector2> Generate(MapRegion region, float mapRadius, RandomWrapper r, TriangleNet.TrianglePool pool)
        {
            float validArea = region.GetValidArea();
            float regionArea = region.GetRegionArea();
            int totalNPCCount = Mathf.FloorToInt(regionArea / mDensity);
            int validNPCCount = Mathf.FloorToInt((validArea / regionArea) * totalNPCCount);

            MapRegionPointGenerator gen = new MapRegionPointGenerator(new Rect(0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight));
            //Debug.Log($"generate point in region: {region.index}, {region.startPosition}");
            gen.Create(region.noneHolePolygons, region.holePolygons, validNPCCount, mapRadius, mMaxMoveRange, r, 0, null, 0, pool);
            return gen.generatedPoints;
        }

        public SpawnPointGenerateStrategyType strategy { get { return SpawnPointGenerateStrategyType.Random; } }
        public float density { get { return mDensity; } 
            set { 
                mDensity = value; 
            } }
        public float maxMoveRange { get { return mMaxMoveRange; } set { mMaxMoveRange = value; } }
        public int oneMeterHorizontalUnitCount { get { return mOneMeterHorizontalUnitCount; } set { mOneMeterHorizontalUnitCount = value; } }
        public int oneMeterVerticalUnitCount { get { return mOneMeterVerticalUnitCount; } set { mOneMeterVerticalUnitCount = value; } }

        float mMaxMoveRange;
        float mDensity;
        int mOneMeterHorizontalUnitCount;
        int mOneMeterVerticalUnitCount;

        int mTotalHorizontalUnitCount;
        int mTotalVerticalUnitCount;
    }
}

#endif