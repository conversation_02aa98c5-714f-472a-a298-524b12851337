﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveSetting(BinaryWriter writer, string exportFolder)
        {
            BeginSection(MapDataSectionType.Setting, writer);

            //setting的版本号
            writer.Write(VersionSetting.SettingStructVersion);

            //-------------------version 1 start------------------------------
            var data = Map.currentMap.data as EditorMapData;
            //Utils.WriteVector2(writer, new Vector2(data.cameraMinHeight, data.cameraMaxHeight));
            //no more camera setting in editor!
            Utils.WriteVector2(writer, Vector2.zero);
            var dataFolder = Utils.ConvertToUnityAssetsPath(exportFolder);
            Utils.WriteString(writer, dataFolder);

            var map = Map.currentMap;
            writer.Write(map.mapWidth);
            writer.Write(map.mapHeight);
            writer.Write(map.data.borderHeight);
            writer.Write(map.data.isCircleMap);

            SaveMapLODConfig(writer);
            //-------------------version 1 end------------------------------
            //-------------------version 2 start------------------------------
            writer.Write(map.data.backExtendedSize);
            //-------------------version 2 end------------------------------
            //-------------------version 3 start------------------------------
            writer.Write(map.data.farClipOffset);
            //-------------------version 3 end------------------------------
            //-------------------version 4 start------------------------------
            var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            bool useTerrainHeight = false;
            if (blendTerrainLayer != null)
            {
                useTerrainHeight = blendTerrainLayer.CheckIfUseTerrainHeight();
            }
            writer.Write(useTerrainHeight);
            //-------------------version 4 end------------------------------
            //-------------------version 5 start------------------------------
            writer.Write(map.groundTileSize);
            writer.Write(map.frontTileSize);
            //-------------------version 5 end------------------------------
            //-------------------version 6 start------------------------------
            writer.Write(map.data.maxCameraColliderHeight);
            //-------------------version 6 end------------------------------
        }

        void SaveMapLODConfig(BinaryWriter writer)
        {
            var map = Map.currentMap;
            var lodManager = map.data.lodManager;
            int n = lodManager.lodCount;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var lod = lodManager.GetLOD(i);
                writer.Write(lod.cameraHeight);
            }
        }
    }
}

#endif