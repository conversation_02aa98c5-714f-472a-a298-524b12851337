﻿namespace Common.Pool
{
    /// <summary>
    /// 对象池(不会自动检测数据，需手动释放清理数据)
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public static class Pool<T> where T : new()
    {

        #region 属性和字段

        /// <summary>
        /// 对象池数据存储
        /// </summary>
        private static readonly ObjectPool<T> _objectPool = new ObjectPool<T>(null, null);

        #endregion

        #region 对象池逻辑处理

        /// <summary>
        /// 数据获取
        /// </summary>
        /// <returns></returns>
        public static T Get()
        {
            return _objectPool.Get();
        }

        /// <summary>
        /// 数据回收
        /// </summary>
        /// <param name="element"></param>
        public static void Release(T element)
        {
            _objectPool.Release(element);
        }

        #endregion
    }
}
