﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class VaryingTileSizeTerrainLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.VaryingTileSizeTerrainLayerEditorDataVersion);

            var editorMapData = Map.currentMap.data as EditorMapData;
            //save prefab manager
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            PrepareSaving();
            SaveSetting(writer);
            SavePrefabManager(writer, prefabManager);
            SaveLayerData(writer);
            SavePathMapper(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveSetting(BinaryWriter writer)
        {
            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);
        }

        void SaveLayerData(BinaryWriter writer)
        {
            int cols = horizontalTileCount;
            int rows = verticalTileCount;

            var terrainLayerData = layerData as VaryingTileSizeTerrainLayerData;
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(terrainLayerData.useGeneratedLOD);
            Utils.WriteVector3(writer, mLayerView.root.transform.position);

            //save big tiles
            var bigTiles = mLayerData.bigTiles;
            int bigTileCount = bigTiles.Count;
            writer.Write(bigTileCount);
            for (int i = 0; i < bigTileCount; ++i)
            {
                writer.Write(bigTiles[i].x);
                writer.Write(bigTiles[i].y);
                writer.Write(bigTiles[i].width);
                writer.Write(bigTiles[i].height);
            }

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var data = GetTile(j, i);
                    bool validTile = data != null;
                    writer.Write(validTile);
                    if (validTile)
                    {
                        SaveTerrainTileData(writer, data, bigTiles);
                    }
                }
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, layerData);
        }

        void SaveTerrainTileData(BinaryWriter writer, VaryingTileSizeTerrainTileData tileData, List<VaryingTileSizeTerrainLayerData.BigTileData> bigTiles)
        {
            short prefabPathIndex = GetPrefabPathIndex(tileData.GetModelTemplate());
            writer.Write(prefabPathIndex);
            writer.Write(tileData.type);
            writer.Write(tileData.index);
            writer.Write(tileData.tileID);
            int bigTileIndex = -1;
            if (tileData.bigTile != null)
            {
                bigTileIndex = bigTiles.IndexOf(tileData.bigTile);
            }
            writer.Write(bigTileIndex);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                int flag = 0;
                writer.Write(flag);
                Utils.WriteString(writer, "");
                writer.Write(0);
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                    writer.Write(c.terrainLODTileCount);
                }
            }
        }

        void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            writer.Write(prefabManager.nextGroupID);
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            writer.Write(group.groupID);
            Utils.WriteString(writer, group.name);
            Utils.WriteColor32(writer, group.color);
            writer.Write(group.addPrefabSet);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var item = group.GetItem(i);
                Utils.WriteString(writer, mPathMapper.Map(item.prefabPath));
                writer.Write(item.id);
                Utils.WriteVector2Int(writer, item.size);
            }
        }

        short GetPrefabPathIndex(ModelTemplate modelTemplate)
        {
            if (modelTemplate == null)
            {
                return -1;
            }
            short index;
            bool found = mModelTemplateIDToPrefabPathIndex.TryGetValue(modelTemplate.id, out index);
            if (!found)
            {
                mPrefabPathStringTable.Add(mPathMapper.Map(modelTemplate.GetLODPrefabPath(0)));
                index = (short)(mPrefabPathStringTable.Count - 1);
                mModelTemplateIDToPrefabPathIndex[modelTemplate.id] = index;
            }
            return index;
        }

        void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }

            //save string table
            int pathCount = mPrefabPathStringTable.Count;
            writer.Write(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                Utils.WriteString(writer, mPrefabPathStringTable[i]);
            }
        }

        void PrepareSaving()
        {
            mPathMapper = new PathMapper();
            mModelTemplateIDToPrefabPathIndex = new Dictionary<int, short>();
            mPrefabPathStringTable = new List<string>();
        }

        public void ProcessBeforeExport()
        {
        }

        PathMapper mPathMapper;
        long mPathMapperPosition;
        Dictionary<int, short> mModelTemplateIDToPrefabPathIndex;
        List<string> mPrefabPathStringTable;
    }
}
#endif