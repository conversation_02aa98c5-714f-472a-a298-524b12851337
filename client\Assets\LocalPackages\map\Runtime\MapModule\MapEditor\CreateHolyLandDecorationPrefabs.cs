﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class CreateHolyLandDecorationPrefabs
    {
        class ChildInfo
        {
            public Vector3 position;
            public string prefabPath;
        }

        public static void Create(string configFilePath, string outputPrefabPath, int mapWidth, int mapHeight)
        {
            bool suc = TSVReader.Load(configFilePath);
            if (suc)
            {
                List<ChildInfo> children = new List<ChildInfo>();

                int divider = 10000;
                var rows = TSVReader.rows;
                for (int i = 0; i < rows.Count; ++i)
                {
                    ChildInfo info = new ChildInfo();

                    long x = TSVReader.GetInt(i, "abscissaX", out _);
                    long z = TSVReader.GetInt(i, "ordinateZ", out _);
                    info.position = new Vector3(x / divider, 0, z / divider) - new Vector3(mapWidth * 0.5f, 0, mapHeight * 0.5f);
                    info.prefabPath = TSVReader.GetString(i, "ResourcesPath", out _);
                    children.Add(info);
                }

                CreatePrefab(children, outputPrefabPath);
            }
        }

        static void CreatePrefab(List<ChildInfo> children, string outputPrefabPath)
        {
            string name = "AllHolyLandDecorations_lod0";
            var root = new GameObject(name);
            for (int i = 0; i < children.Count; ++i)
            {
                var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(children[i].prefabPath);
                var prefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(childPrefab, root.transform);
                prefabInstance.transform.position = children[i].position;
            }

            PrefabUtility.SaveAsPrefabAsset(root, $"{outputPrefabPath}/{name}.prefab");
            Utils.DestroyObject(root);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }
    }
}


#endif