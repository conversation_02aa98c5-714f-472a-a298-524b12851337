﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //检测物体的可见性
    public class CullManager
    {
        //gridSize必须要大于所有npc的直径
        public void Init(Map map, Vector3 mapOrigin, string name, int gridSize, float mapWidth, float mapHeight, float updateDeltaDistanceThreshold, bool useRectDifference, bool gridVisible)
        {
            mMap = map;
            mMapOrigin = mapOrigin;
            mUpdateDeltaDistanceThresholdSqr = updateDeltaDistanceThreshold * updateDeltaDistanceThreshold;
            mHorizontalGridCount = Mathf.CeilToInt(mapWidth / gridSize);
            mVerticalGridCount = Mathf.CeilToInt(mapHeight / gridSize);
            int n = mVerticalGridCount * mHorizontalGridCount;
            mGrids = new CullGrid[n];
            for (int i = 0; i < n; ++i)
            {
                mGrids[i] = new CullGrid();
            }
            mGridSize = gridSize;
            mMapWidth = mapWidth;
            mMapHeight = mapHeight;

            if (useRectDifference)
            {
                mRectDiffCalculator = new RectDifference();
                mVisibleRects = new List<RectInt>();
                mInvisibleRects = new List<RectInt>();
                mOldRect = new RectI();
                mNewRect = new RectI();
            }
        }

        public void OnDestroy()
        {
            mObjectPool.OnDestroy();
            mGrids = null;
            mObjects = null;
        }

        //注册一个圆作为物体的替身,当圆的可见性发生改变时调用回调函数通知register
        public long RegisterCircle(float radius, float centerX, float centerZ, System.Action<long, object, bool> visibilityChangeCallback, object customParam, out bool isVisible)
        {
            var idx = GetGridIndex(centerX, centerZ);
            if (idx < 0 || idx >= mGrids.Length)
            {
                Debug.LogError($"invalid coordinate {centerX}_{centerZ}");
                isVisible = false;
                return 0;
            }
            isVisible = IsCircleVisible(radius, centerX, centerZ);
            var obj = mObjectPool.Require();
            obj.halfWidth = radius;
            obj.halfHeight = radius;
            obj.isCircle = true;
            obj.centerX = centerX;
            obj.centerZ = centerZ;
            obj.customParam = customParam;
            obj.visible = isVisible;
            obj.visibilityChangeCallback = visibilityChangeCallback;
            obj.id = ++mNextCullObjectID;
            mObjects.Add(obj.id, obj);
            AddToGrid(idx, obj);

            return obj.id;
        }

        public long RegisterRectangle(float halfWidth, float halfHeight, float centerX, float centerZ, System.Action<long, object, bool> visibilityChangeCallback, object customParam, out bool isVisible)
        {
            var idx = GetGridIndex(centerX, centerZ);
            if (idx < 0 || idx >= mGrids.Length)
            {
                Debug.LogError($"invalid coordinate {centerX}_{centerZ}");
                isVisible = false;
                return 0;
            }
            isVisible = IsRectVisible(halfWidth, halfHeight, centerX, centerZ);
            var obj = mObjectPool.Require();
            obj.halfWidth = halfWidth;
            obj.halfHeight = halfHeight;
            obj.isCircle = false;
            obj.centerX = centerX;
            obj.centerZ = centerZ;
            obj.customParam = customParam;
            obj.visible = isVisible;
            obj.visibilityChangeCallback = visibilityChangeCallback;
            obj.id = ++mNextCullObjectID;
            mObjects.Add(obj.id, obj);
            AddToGrid(idx, obj);

            return obj.id;
        }

        //注册一个圆作为物体的替身,当圆的可见性发生改变时调用回调函数通知register,这个id必须要保证唯一
        public void RegisterCircleWithCustomID(long id, float radius, float centerX, float centerZ, System.Action<long, object, bool> visibilityChangeCallback, object customParam, out bool isVisible)
        {
            var idx = GetGridIndex(centerX, centerZ);
            if (idx < 0 || idx >= mGrids.Length)
            {
                Debug.Log($"invalid coordinate {centerX}_{centerZ}");
                isVisible = false;
                return;
            }
            isVisible = IsCircleVisible(radius, centerX, centerZ);
            var obj = mObjectPool.Require();
            obj.halfWidth = radius;
            obj.halfHeight = radius;
            obj.isCircle = true;
            obj.centerX = centerX;
            obj.centerZ = centerZ;
            obj.customParam = customParam;
            obj.visible = isVisible;
            obj.visibilityChangeCallback = visibilityChangeCallback;
            //这里的id由调用方管理!
            obj.id = id;
            mObjects.Add(obj.id, obj);
            AddToGrid(idx, obj);
        }

        //取消注册
        public bool UnregisterCullObject(long id)
        {
            if (id == 0)
            {
                return false;
            }
            CullObject obj;
            mObjects.TryGetValue(id, out obj);
            if (obj == null)
            {
                Debug.Assert(false, "Invalid cull object id!");
                return false;
            }
            mObjectPool.Release(obj);
            mObjects.Remove(id);
            RemoveFromGrid(obj);
            return true;
        }

        public bool IsCullObjectRegistered(long id)
        {
            return mObjects.ContainsKey(id);
        }

        //if circle is visible in viewport
        public bool IsCircleVisible(float radius, float centerX, float centerZ)
        {
            var viewport = mMap.originalViewport;
            return Utils.IsRectCircleIntersected(viewport.xMin, viewport.yMin, viewport.xMax, viewport.yMax, centerX, centerZ, radius);
        }

        public bool IsRectVisible(float halfWidth, float halfHeight, float centerX, float centerZ)
        {
            var viewport = mMap.originalViewport;
            return Utils.RectOverlap(viewport.xMin, viewport.yMin, viewport.xMax, viewport.yMax, centerX - halfWidth, centerZ - halfHeight, centerX + halfWidth, centerZ + halfHeight);
        }

        //test if object is intersected with any circle!
        public void GetCirclesIntersectedWithObject(float radius, float centerX, float centerZ, List<long> intersectedIDs)
        {
            intersectedIDs.Clear();
            //扩大一圈要测试的格子数
            int xMin = Mathf.FloorToInt((centerX - radius - mMapOrigin.x) / mGridSize) - 1;
            int yMin = Mathf.FloorToInt((centerZ - radius - mMapOrigin.z) / mGridSize) - 1;
            int xMax = Mathf.FloorToInt((centerX + radius - mMapOrigin.x) / mGridSize) + 1;
            int yMax = Mathf.FloorToInt((centerZ + radius - mMapOrigin.z) / mGridSize) + 1;

            for (int i = yMin; i <= yMax; ++i)
            {
                for (int j = xMin; j <= xMax; ++j)
                {
                    int idx = i * mHorizontalGridCount + j;
                    mGrids[idx].GetIntersectedObjects(centerX, centerZ, radius, intersectedIDs);
                }
            }
        }

        public void UpdateViewport(Rect newViewport)
        {
            if (mObjects.Count == 0)
            {
                return;
            }

            if (mRectDiffCalculator == null)
            {
                //扩大viewport1个格子的大小,避免检测不到相邻格子但又在视野范围内的物体
                Rect expandedViewport = CalculateExpandedViewport(newViewport);
                if (!NeedUpdateViewport(expandedViewport))
                {
                    return;
                }
                //永远要检测在视野边界格子内的物体的可见性
                int newStartGridCoordX, newStartGridCoordY;
                int newEndGridCoordX, newEndGridCoordY;
                GetGridCoord(expandedViewport.xMin, expandedViewport.yMin, out newStartGridCoordX, out newStartGridCoordY);
                GetGridCoord(expandedViewport.xMax, expandedViewport.yMax, out newEndGridCoordX, out newEndGridCoordY);

                int originalStartGridCoordX, originalStartGridCoordY;
                int originalEndGridCoordX, originalEndGridCoordY;
                GetGridCoord(newViewport.xMin, newViewport.yMin, out originalStartGridCoordX, out originalStartGridCoordY);
                GetGridCoord(newViewport.xMax, newViewport.yMax, out originalEndGridCoordX, out originalEndGridCoordY);

                //隐藏出视野的格子
                int idx;
                int oldStartGridCoordX, oldStartGridCoordY;
                int oldEndGridCoordX, oldEndGridCoordY;
                GetGridCoord(mLastUpdateViewport.xMin, mLastUpdateViewport.yMin, out oldStartGridCoordX, out oldStartGridCoordY);
                GetGridCoord(mLastUpdateViewport.xMax, mLastUpdateViewport.yMax, out oldEndGridCoordX, out oldEndGridCoordY);
                for (int y = oldStartGridCoordY; y <= oldEndGridCoordY; ++y)
                {
                    for (int x = oldStartGridCoordX; x <= oldEndGridCoordX; ++x)
                    {
                        if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
                        {
                            if (x > newEndGridCoordX || x < newStartGridCoordX || y > newEndGridCoordY || y < newStartGridCoordY)
                            {
                                idx = y * mHorizontalGridCount + x;
                                mGrids[idx].isFullyVisible = false;
                                mGrids[idx].SetObjectVisibility(false);
                            }
                        }
                    }
                }

                float minX = newViewport.xMin;
                float maxX = newViewport.xMax;
                float minY = newViewport.yMin;
                float maxY = newViewport.yMax;
                for (int y = newStartGridCoordY; y <= newEndGridCoordY; ++y)
                {
                    for (int x = newStartGridCoordX; x <= newEndGridCoordX; ++x)
                    {
                        if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
                        {
                            idx = y * mHorizontalGridCount + x;
                            //检测是否是边界格子
                            if (x > originalStartGridCoordX && x < originalEndGridCoordX && y > originalStartGridCoordY && y < originalEndGridCoordY)
                            {
                                //内部格子,特殊处理
                                if (!mGrids[idx].isFullyVisible)
                                {
                                    mGrids[idx].isFullyVisible = true;
                                    //直接显示格子内所有物体
                                    mGrids[idx].SetObjectVisibility(true);
                                }
                                else
                                {
                                    //temp code
                                    //Debug.LogError($"skip internal grid {y},{x}");
                                }
                            }
                            else
                            {
                                if (mGrids[idx].isFullyVisible)
                                {
                                    //之前是内部格子,现在不是了
                                    mGrids[idx].isFullyVisible = false;
                                }
                                mGrids[idx].CheckVisibility(minX, minY, maxX, maxY);
                            }
                        }
                    }
                }

                mLastUpdateViewport = expandedViewport;
            }
            else
            {
                UpdateViewportByRectDifference(newViewport);
            }
        }

        void AddToGrid(int idx, CullObject obj)
        {
            mGrids[idx].Add(obj);
        }

        void RemoveFromGrid(CullObject obj)
        {
            var idx = GetGridIndex(obj.centerX, obj.centerZ);
            mGrids[idx].Remove(obj);
        }

        int GetGridIndex(float x, float z)
        {
            var xc = Mathf.FloorToInt((x - mMapOrigin.x) / mGridSize);
            var yc = Mathf.FloorToInt((z - mMapOrigin.z) / mGridSize);
            return yc * mHorizontalGridCount + xc;
        }

        void GetGridCoord(float x, float z, out int xc, out int yc)
        {
            xc = Mathf.FloorToInt((x - mMapOrigin.x) / mGridSize);
            yc = Mathf.FloorToInt((z - mMapOrigin.z) / mGridSize);
        }

        Rect CalculateExpandedViewport(Rect viewport)
        {
            var expandedSize = viewport.size + new Vector2(mGridSize * 2, mGridSize * 2);
            var min = viewport.center - expandedSize * 0.5f;
            return new Rect(min, expandedSize);
        }

        class CullObject
        {
            public long id;
            public object customParam;
            public float halfWidth;
            public float halfHeight;
            public float centerX;
            public float centerZ;
            public System.Action<long, object, bool> visibilityChangeCallback;
            public bool visible = false;
            public bool isCircle = false;
        }

        class CullGrid
        {
            public void Add(CullObject obj)
            {
                mObjects.Add(obj);
            }

            public void Remove(CullObject obj)
            {
                mObjects.Remove(obj);
                obj.visible = false;
                obj.customParam = null;
            }

            public void SetObjectVisibility(bool visible)
            {
                int n = mObjects.Count;
                for (int i = 0; i < n; ++i)
                {
                    var obj = mObjects[i];
                    obj.visible = visible;
                    obj.visibilityChangeCallback(obj.id, obj.customParam, visible);
                }
            }

            public void CheckVisibility(float viewportMinX, float viewportMinY, float viewportMaxX, float viewportMaxY)
            {
                bool visible = false;
                int n = mObjects.Count;
                for (int i = 0; i < n; ++i)
                {
                    var obj = mObjects[i];
                    if (obj.isCircle)
                    {
                        visible = Utils.IsRectCircleIntersected(viewportMinX, viewportMinY, viewportMaxX, viewportMaxY, obj.centerX, obj.centerZ, obj.halfWidth);
                    }
                    else
                    {
                        visible = Utils.RectOverlap(viewportMinX, viewportMinY, viewportMaxX, viewportMaxY, obj.centerX - obj.halfWidth, obj.centerZ - obj.halfHeight, obj.centerX + obj.halfWidth, obj.centerZ + obj.halfHeight);
                    }
                    
                    if (obj.visible != visible)
                    {
                        obj.visible = visible;
                        obj.visibilityChangeCallback(obj.id, obj.customParam, visible);
                    }
                }
            }

            public void GetIntersectedObjects(float centerX, float centerZ, float radius, List<long> objectIDs)
            {
                bool visible = false;
                int n = mObjects.Count;
                for (int i = 0; i < n; ++i)
                {
                    var obj = mObjects[i];
                    if (obj.isCircle)
                    {
                        visible = Utils.IsCircleCircleOverlap(centerX, centerZ, radius, obj.centerX, obj.centerZ, obj.halfWidth);
                    }
                    else
                    {
                        visible = Utils.IsRectCircleIntersected(obj.centerX - obj.halfWidth, obj.centerZ - obj.halfHeight, obj.centerX + obj.halfWidth, obj.centerZ + obj.halfHeight, centerX, centerZ, radius);
                    }
                    if (visible)
                    {
                        objectIDs.Add(obj.id);
                    }
                }
            }

            public bool isFullyVisible { set { mIsFullyVisible = value; } get { return mIsFullyVisible; } }
            //是否整个grid都在视野内
            bool mIsFullyVisible = false;
            List<CullObject> mObjects = new List<CullObject>();
        }

        bool NeedUpdateViewport(Rect newViewport)
        {
            var centerDelta = mLastUpdateViewport.center - newViewport.center;
            if (centerDelta.sqrMagnitude >= mUpdateDeltaDistanceThresholdSqr)
            {
                return true;
            }

            var sizeDelta = mLastUpdateViewport.size - newViewport.size;
            if (sizeDelta.sqrMagnitude >= mUpdateDeltaDistanceThresholdSqr)
            {
                return true;
            }

            return false;
        }

        void UpdateViewportByRectDifference(Rect newViewport)
        {
            int oldStartCoordX, oldStartCoordY;
            int oldEndCoordX, oldEndCoordY;
            int newStartCoordX, newStartCoordY;
            int newEndCoordX, newEndCoordY;
            GetGridCoord(mLastUpdateViewport.xMin, mLastUpdateViewport.yMin, out oldStartCoordX, out oldStartCoordY);
            GetGridCoord(mLastUpdateViewport.xMax, mLastUpdateViewport.yMax, out oldEndCoordX, out oldEndCoordY);
            GetGridCoord(newViewport.xMin, newViewport.yMin, out newStartCoordX, out newStartCoordY);
            GetGridCoord(newViewport.xMax, newViewport.yMax, out newEndCoordX, out newEndCoordY);
            mOldRect.Set(oldStartCoordX, oldStartCoordY, oldEndCoordX, oldEndCoordY);
            mNewRect.Set(newStartCoordX, newStartCoordY, newEndCoordX, newEndCoordY);
            mRectDiffCalculator.Calculate(mOldRect, mNewRect, mVisibleRects, mInvisibleRects);

            int xMin, yMin, xMax, yMax, idx;
            for (int i = 0; i < mVisibleRects.Count; ++i)
            {
                xMin = mVisibleRects[i].xMin;
                yMin = mVisibleRects[i].yMin;
                xMax = mVisibleRects[i].xMax;
                yMax = mVisibleRects[i].yMax;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
                        {
                            idx = y * mHorizontalGridCount + x;
                            mGrids[idx].SetObjectVisibility(true);
                        }
                    }
                }
            }

            for (int i = 0; i < mInvisibleRects.Count; ++i)
            {
                xMin = mInvisibleRects[i].xMin;
                yMin = mInvisibleRects[i].yMin;
                xMax = mInvisibleRects[i].xMax;
                yMax = mInvisibleRects[i].yMax;
                for (int y = yMin; y <= yMax; ++y)
                {
                    for (int x = xMin; x <= xMax; ++x)
                    {
                        if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
                        {
                            idx = y * mHorizontalGridCount + x;
                            mGrids[idx].SetObjectVisibility(false);
                        }
                    }
                }
            }

            mLastUpdateViewport = newViewport;
        }

        float mGridSize;
        float mMapWidth;
        float mMapHeight;
        int mHorizontalGridCount;
        int mVerticalGridCount;
        Vector3 mMapOrigin;
        Rect mLastUpdateViewport;
        //viewport每隔多少米update一次
        float mUpdateDeltaDistanceThresholdSqr;
        CullGrid[] mGrids;
        ObjectPool<CullObject> mObjectPool = new ObjectPool<CullObject>(10000, () => new CullObject());
        Dictionary<long, CullObject> mObjects = new Dictionary<long, CullObject>();
        static long mNextCullObjectID;

        //使用difference calculator时的变量
        RectDifference mRectDiffCalculator;
        RectI mOldRect;
        RectI mNewRect;
        List<RectInt> mVisibleRects;
        List<RectInt> mInvisibleRects;

        Map mMap;
    }
}
