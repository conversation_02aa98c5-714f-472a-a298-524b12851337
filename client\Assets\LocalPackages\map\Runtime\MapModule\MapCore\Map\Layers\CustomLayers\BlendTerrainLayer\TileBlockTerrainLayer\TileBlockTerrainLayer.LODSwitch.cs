﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public partial class TileBlockTerrainLayer : MapLayerBase
    {
        public void SetLODSwitchHandler(IGroundLayerLODSwitchTransitionHandler handler)
        {
            mTransition.lodSwitchHandler = handler;
        }

        public void SetLODSwitchHeight(float cameraHeight)
        {
            Debug.LogError("todo");
        }

        public void EnableLODSwitchTransition(bool enable)
        {
            mEnableTransition = enable;
        }

        void OnLODChange(int lastLOD, int curLOD, Vector3 newViewCenter)
        {
            if (mForceToCameraHeight > 0)
            {
                //正在强行进行一次lod switch transition,忽略相机高度变化导致的lod切换效果
                return;
            }
            if (mEnableTransition && mTransition.lodSwitchHandler != null)
            {
                //int testLOD = curLOD;
                //Vector3 viewCenter = newViewCenter;
                //if (curLOD < lastLOD)
                //{
                //    testLOD = lastLOD;
                //    viewCenter = mUpViewCenter;
                //}
                if (mLayerData.IsOneTileLOD(curLOD < lastLOD ? lastLOD : curLOD))
                {
                    StartTransition(lastLOD, curLOD, newViewCenter);
                }
                else
                {
                    mLayerView.active = true;
                }

                //if (curLOD > lastLOD)
                //{
                //    mUpViewCenter = newViewCenter;
                //}
            }
        }

        bool StartTransition(int lastLOD, int curLOD, Vector3 newViewCenter, float targetCameraHeight = 0)
        {
            if (mTransition.started)
            {
                return false;
            }
            if (curLOD < lastLOD)
            {
                mLayerView.active = true;
            }
            mTransition.Start(lastLOD, curLOD, targetCameraHeight, newViewCenter);
            return true;
        }

        //强制在任意高度瞬间切换到目标高度
        public void ForceLODSwitchTransition(Vector3 newViewCenter, float targetCameraHeight)
        {
            int curLOD = mLayerData.GetLODFromCameraHeight(targetCameraHeight);
            int lastLOD = mLayerData.currentLOD;
            bool suc = StartTransition(lastLOD, curLOD, newViewCenter, targetCameraHeight);
            if (suc)
            {
                mForceToCameraHeight = targetCameraHeight;
            }
        }

        public void UpdateLODTransition()
        {
            if (mTransition.lodSwitchHandler != null && mTransition.started)
            {
                bool finished = mTransition.Update(mForceToCameraHeight);
                if (finished)
                {
                    if (mTransition.startLOD < mTransition.endLOD)
                    {
                        mLayerView.active = false;
                    }
                    mForceToCameraHeight = 0;
                }
            }
        }

        BlendTerrainLODTransition mTransition = new BlendTerrainLODTransition();
        bool mEnableTransition = true;
        float mForceToCameraHeight = 0;
        Vector3 mUpViewCenter;
    }
}
