﻿ 



 
 

#if UNITY_EDITOR

//create by wzw at 2019/8/20
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    //调试navmesh,主要是给服务器使用
    [Black]
    public static class NavMeshViewer
    {
        public static void Create(string navmeshConfigFilePath)
        {
            Destroy();

            string content = System.IO.File.ReadAllText(navmeshConfigFilePath);
            if (string.IsNullOrEmpty(content))
            {
                return;
            }
            var meshArray = JSONParser.Deserialize(content) as List<object>;
            int n = meshArray.Count;
            for (int i = 0; i < n; ++i)
            {
                var meshConfig = meshArray[i] as Dictionary<string, object>;
                int[] triangleIndices;
                UnityEngine.Vector3[] meshVertices;
                CreateMeshData(meshConfig, out meshVertices, out triangleIndices, UnityEngine.Vector3.zero);

                mMeshViewer = new BigMeshViewer();
                mMeshViewer.Create(null, navmeshConfigFilePath, meshVertices, triangleIndices, false, new Color32(51, 196, 79, 150));
                var gameObject = mMeshViewer.gameObject;
                var debugger = gameObject.AddComponent<NavMeshDebugger>();
                debugger.Create(meshVertices, triangleIndices);
            }
        }

        static void Destroy()
        {
            if (mMeshViewer != null)
            {
                mMeshViewer.OnDestroy();
                mMeshViewer = null;
            }
        }

        static void CreateMeshData(Dictionary<string, object> meshConfig, out UnityEngine.Vector3[] meshVertices, out int[] triangleIndices, UnityEngine.Vector3 offset)
        {
            var triangles = meshConfig["triangles"] as List<object>;
            int nTriangles = triangles.Count;
            triangleIndices = new int[nTriangles * 3];
            for (int t = 0; t < nTriangles; ++t)
            {
                var triangle = triangles[t] as List<object>;
                triangleIndices[t * 3] = Convert.ToInt32(triangle[0]);
                triangleIndices[t * 3 + 1] = Convert.ToInt32(triangle[1]);
                triangleIndices[t * 3 + 2] = Convert.ToInt32(triangle[2]);
            }

            var vertices = meshConfig["vertices"] as List<object>;
            int nVertices = vertices.Count;
            meshVertices = new UnityEngine.Vector3[nVertices];

            float scale = 0.001f;
            for (int v = 0; v < nVertices; ++v)
            {
                var vert = vertices[v] as Dictionary<string, object>;
                meshVertices[v] = new UnityEngine.Vector3(Convert.ToSingle(vert["x"]) * scale, 0, Convert.ToSingle(vert["z"]) * scale) + offset;
            }
        }

        static BigMeshViewer mMeshViewer;
    }
}


#endif