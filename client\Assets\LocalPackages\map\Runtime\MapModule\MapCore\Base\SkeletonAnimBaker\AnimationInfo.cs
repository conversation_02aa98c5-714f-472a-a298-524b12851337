﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    [System.Serializable]
    public class AnimationInfo
    {
        public AnimationInfo(int sampledFrameCount, float lengthInSeconds, float framerate, float framemultiplier, bool loop, int skinAnimOffset, int rigidAnimOffset, int cpuDrivenAnimOffset, string clipGuid, string clipName)
        {
            Debug.Assert(framemultiplier != 0);
            this.clipGuid = clipGuid;
            this.clipName = clipName;
            this.lengthInSeconds = lengthInSeconds;
            this.framerate = framerate;
            this.frameMultiplier = framemultiplier;
            this.loop = loop;
            this.skinAnimStartOffset = skinAnimOffset;
            this.rigidAnimStartOffset = rigidAnimOffset;
            this.cpuDrivenAnimStartOffset = cpuDrivenAnimOffset;
            this.totalFrames = sampledFrameCount;
        }

        public string clipGuid;
        public string clipName;
        public float lengthInSeconds;
        public float framerate;
        public float frameMultiplier;
        public bool loop;
        //动画贴图中某个动画数据的起始位置
        public int rigidAnimStartOffset;
        public int skinAnimStartOffset;
        public int cpuDrivenAnimStartOffset;
        public int totalFrames;

        //因为顶点烘培动画每个mesh一张贴图,所以startOffsets也是多个
        public int[] bakedVertexAnimationStartOffsetsForEachMesh;
        public float[] bakedVertexAnimationStartOffsetRatioForEachMesh;
        //每段动画共占用多长的贴图部分比例,0到1之间
        public float bakedVertexAnimationLengthRatio;
    }

    [System.Serializable]
    public class AnimationStateInfo
    {
        public AnimationStateInfo(string stateName, int animationIndex, AnimationTransitionInfo[] transitions, AnimationEventInfo[] events, AnimationSpeedInfo speedInfo, AnimationCycleOffsetInfo cycleOffsetInfo)
        {
            this.stateName = stateName;
            this.animationIndex = animationIndex;
            this.transitions = transitions;
            this.events = events;
            this.speedInfo = speedInfo;
            this.cycleOffsetInfo = cycleOffsetInfo;
        }

        public string stateName;
        public int animationIndex;
        public AnimationTransitionInfo[] transitions;
        public AnimationEventInfo[] events;
        public AnimationSpeedInfo speedInfo;
        public AnimationCycleOffsetInfo cycleOffsetInfo;
    }

    [System.Serializable]
    public class AnimationTransitionConditionInfo
    {
        public int mode;
        public string parameter;
        public float threshold;
    }

    [System.Serializable]
    public class AnimationTransitionInfo
    {
        public AnimationTransitionInfo(string fromStateName, string toStateName, AnimationTransitionConditionInfo[] conditions, bool hasExitTime, float exitTime, bool fixedDuration, float transitionDuration, float transitionOffset)
        {
            this.fromStateName = fromStateName;
            this.toStateName = toStateName;
            this.conditions = conditions;
            this.hasExitTime = hasExitTime;
            this.exitTime = exitTime;
            this.fixedDuration = fixedDuration;
            this.transitionDuration = transitionDuration;
            this.transitionOffset = transitionOffset;
        }

        public string fromStateName;
        public string toStateName;
        public AnimationTransitionConditionInfo[] conditions;
        public bool hasExitTime;
        public bool fixedDuration;
        public float exitTime;
        public float transitionDuration;
        public float transitionOffset;
    }

    [System.Serializable]
    public class AnimationSpeedInfo
    {
        public float speed = 1.0f;
        public string speedParameter = "";
        public bool speedParameterActive = false;
    }

    [System.Serializable]
    public class AnimationCycleOffsetInfo
    {
        public float cycleOffset = 0;
        public string cycleOffsetParameter = "";
        public bool cycleOffsetParameterActive = false;
    }

    [System.Serializable]
    public class AnimationEventInfo
    {
        public string functionName;
        public string stringParameter;
        public float floatParameter;
        public int intParameter;
        //absolute time
        public float time;
    }

    [System.Serializable]
    public class AnimationParameterInfo
    {
        public string name;
        //     Returns the hash of the parameter based on its name.
        public int nameHash;
        //     The type of the parameter.
        public AnimatorControllerParameterType type;
        //     The default float value for the parameter.
        public float defaultFloat;
        //     The default int value for the parameter.
        public int defaultInt;
        //     The default bool value for the parameter.
        public bool defaultBool;
    }
}
