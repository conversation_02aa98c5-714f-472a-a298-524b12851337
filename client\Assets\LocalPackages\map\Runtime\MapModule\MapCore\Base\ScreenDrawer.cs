﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    public class ScreenPoint : MonoBehaviour
    {
        public void Draw(Vector2 a)
        {
            mPos = a;
        }

        public void SetColor(Color color)
        {
            mColor = color;
        }

        void OnGUI()
        {
#if false
            float size = 20;
            if (mTexture == null)
            {
                mTexture = MapModuleResourceMgr.LoadTexture($"{TFW.Map.GameConfig1.UIResRoot}sprite/Save-icon.png");
            }
            var screenHeight = Screen.height;
            GUI.DrawTexture(new Rect(mPos.x - size * 0.5f, screenHeight - (mPos.y - size * 0.5f), size, size), mTexture, ScaleMode.ScaleToFit, true, 0, mColor, 0, 0);
#endif
        }

        Vector2 mPos;
        Texture mTexture;
        Color mColor = Color.white;
    }
}

