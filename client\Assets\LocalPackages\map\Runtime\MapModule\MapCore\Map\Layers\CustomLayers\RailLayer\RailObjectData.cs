﻿ 



 
 

﻿
using UnityEngine;

namespace TFW.Map
{
    public enum RailObjectType
    {
        Rail,
        Tunnel,
    }

    public class RailObjectData : ModelData
    {
        public RailObjectData(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, RailObjectType type, bool isGroupLeader, int groupID, int railIndex, int segmentIndex)
            : base(id, map, flag, position, rotation, scale, modelTemplate, true)
        {
            mRailIndex = railIndex;
            mType = type;
            mIsGroupLeader = isGroupLeader;
            mLODGroupID = groupID;
            mSegmentIndex = segmentIndex;
        }

        public override int GetModelLODGroupID()
        {
            return mLODGroupID;
        }

        public void SetModelLODGroupID(int id)
        {
            mLODGroupID = id;
        }

        public override Rect GetBounds()
        {
            if (mIsGroupLeader)
            {
                //使用全局的rect
                return new Rect(0, 0, 10000, 10000);
            }
            return base.GetBounds();
        }

        public RailObjectType type { get { return mType; } }
        public bool isGroupLeader { get { return mIsGroupLeader; } set { mIsGroupLeader = value; } }
        public int railIndex { get { return mRailIndex; } set { mRailIndex = value; } }
        public int segmentIndex { get { return mSegmentIndex; } }

        RailObjectType mType;
        bool mIsGroupLeader;
        int mLODGroupID;
        int mRailIndex;
        int mSegmentIndex;
    }
}