%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2999724890211546990
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4290226831567093036}
  - component: {fileID: 4443092874937620017}
  m_Layer: 0
  m_Name: ObstacleOutline
  m_TagString: ignore
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4290226831567093036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2999724890211546990}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4443092874937620017
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2999724890211546990}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -90, y: 0, z: 45.341328}
    - {x: -77.38575, y: 0, z: 48.387672}
    - {x: -65.36668, y: 0, z: 35.045986}
    - {x: -65.46549, y: 0, z: 25.980593}
    - {x: -75.03421, y: 0, z: 24.067501}
    - {x: -90, y: 0, z: 30.085398}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -75.0663, y: 0, z: 21.142}
    - {x: -75.8533, y: 0, z: 21.2132}
    - {x: -90, y: 0, z: 26.9097}
    - {x: -90, y: 0, z: 27.3516}
    - {x: -90, y: 0, z: 29.0875}
    - {x: -90, y: 0, z: 29.7739}
    - {x: -90, y: 0, z: 32.177}
    - {x: -90, y: 0, z: 32.4649}
    - {x: -90, y: 0, z: 44.6765}
    - {x: -90, y: 0, z: 45.9124}
    - {x: -89.8791, y: 0, z: 49.1187}
    - {x: -89.2401, y: 0, z: 49.425}
    - {x: -82.6981, y: 0, z: 50.8322}
    - {x: -82.4606, y: 0, z: 50.8637}
    - {x: -78.4546, y: 0, z: 51.0733}
    - {x: -77.4432, y: 0, z: 50.7641}
    - {x: -71.741, y: 0, z: 46.4683}
    - {x: -71.4839, y: 0, z: 46.2218}
    - {x: -63.371, y: 0, z: 36.352}
    - {x: -63.055, y: 0, z: 35.6875}
    - {x: -62.2994, y: 0, z: 31.9583}
    - {x: -62.2701, y: 0, z: 31.6133}
    - {x: -62.4149, y: 0, z: 26.8916}
    - {x: -62.6188, y: 0, z: 26.1758}
    - {x: -64.0269, y: 0, z: 23.7409}
    - {x: -65.0678, y: 0, z: 22.9899}
    - {x: -68.5056, y: 0, z: 22.2806}
    - {x: -68.5524, y: 0, z: 22.2717}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
