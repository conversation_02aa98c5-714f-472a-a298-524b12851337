﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class ComplexGrid
    {
        class GridLODData
        {
            public List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
            public bool dirty = false;
        }

        public ComplexGrid(int lodCount)
        {
            mObjectsOfEachLOD = new List<GridLODData>(lodCount);
            for (int i = 0; i < lodCount; ++i)
            {
                mObjectsOfEachLOD.Add(new GridLODData());
            }
        }

        public bool CanAddObject(IMapObjectData obj, int lod)
        {
            var objects = mObjectsOfEachLOD[lod].objects;
            int nObjects = objects.Count;
            for (int i = 0; i < nObjects; ++i)
            {
                if (objects[i].GetPosition() == obj.GetPosition())
                {
                    return false;
                }
            }

            return true;
        }

        public void AddObject(IMapObjectData obj, int lod)
        {
#if UNITY_EDITOR
            Debug.Assert(obj is ComplexGridModelData);
            Debug.Assert(mObjectsOfEachLOD[lod].objects.Contains(obj as ComplexGridModelData) == false);
#endif
            mObjectsOfEachLOD[lod].objects.Add(obj as ComplexGridModelData);
            mObjectsOfEachLOD[lod].dirty = true;
        }

        public void RemoveObject(IMapObjectData obj, int lod)
        {
#if UNITY_EDITOR
            Debug.Assert(obj is ComplexGridModelData);
#endif
            mObjectsOfEachLOD[lod].objects.Remove(obj as ComplexGridModelData);
            mObjectsOfEachLOD[lod].dirty = true;
        }

        public List<IComplexGridModelData> GetLODObjects(int lod)
        {
            if (lod >= 0 && lod < mObjectsOfEachLOD.Count)
            {
                return mObjectsOfEachLOD[lod].objects;
            }
            return null;
        }

        public void SetLODCount(int newLODCount)
        {
            int oldCount = mObjectsOfEachLOD.Count;
            if (newLODCount > oldCount)
            {
                for (int i = oldCount; i < newLODCount; ++i)
                {
                    mObjectsOfEachLOD.Add(new GridLODData());
                }
            }
            else if (newLODCount < oldCount)
            {
                for (int i = oldCount - 1; i >= newLODCount; --i)
                {
                    mObjectsOfEachLOD.RemoveAt(i);
                }
            }
        }

        public bool IsLODDirty(int lod)
        {
            if (lod >= 0 && lod < mObjectsOfEachLOD.Count)
            {
                return mObjectsOfEachLOD[lod].dirty;
            }
            return false;
        }

        public void SetLODDirty(int lod, bool dirty)
        {
            if (lod >= 0 && lod < mObjectsOfEachLOD.Count)
            {
                mObjectsOfEachLOD[lod].dirty = dirty;
            }
        }

        //每个lod下的数据
        List<GridLODData> mObjectsOfEachLOD;
    }
}

