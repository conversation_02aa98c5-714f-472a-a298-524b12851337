﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RuinLayerUI : UnityEditor.Editor
    {
        void AddPredefinedType(string name, float radius)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            var ruinSettings = layer.ruinObjectTypes;
            config.RuinSpecialRegionSetting setting = new config.RuinSpecialRegionSetting();
            if (ruinSettings.Count > 0)
            {
                setting.bigGridWidth = ruinSettings[0].bigGridWidth;
                setting.bigGridHeight = ruinSettings[0].bigGridHeight;
                setting.startX = ruinSettings[0].startX;
                setting.startZ = ruinSettings[0].startZ;
                setting.horizontalBigGridCount = ruinSettings[0].horizontalBigGridCount;
                setting.verticalBigGridCount = ruinSettings[0].verticalBigGridCount;
                setting.pointCount = 0;
                setting.invalidCircles = new List<int>();
                setting.invalidCircles.AddRange(ruinSettings[0].invalidCircles);
            }
            
            var ruin = layer.AddRuinObjectType(name, Color.black, new PropertyDatas(null), radius, setting);
            if (ruin != null)
            {
                ruin.alwaysDisplay = true;
                int typeCount = layer.ruinObjectTypes.Count;
                SetCurrentObjectType(typeCount - 1);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", string.Format("{0} is already existed!", name), "OK");
            }
        }

        void InitShadowAreaSettings()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RuinLayer;
            layer.ruinDisplayRadius = 0.5f;

            AddPredefinedType("Small Shadow Area", 75);
            AddPredefinedType("Middle Shadow Area", 150);
            AddPredefinedType("Big Shadow Area", 300);

            RemoveObjectType("Ruin");
        }
    }
}

#endif