﻿ 



 
 

﻿
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadLocalObstacles(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.LocalObstacles);
            int version = reader.ReadInt32();

            //--------------------version 1 start--------------------------
            var localObstacleManager = new config.MapLocalObstacleManager();
            mEditorData.localObstacleManager = localObstacleManager;
            localObstacleManager.regionWidth = reader.ReadSingle();
            localObstacleManager.regionHeight = reader.ReadSingle();
            int tileCount = reader.ReadInt32();
            localObstacleManager.tiles = new int[tileCount];
            for (int i = 0; i < tileCount; ++i)
            {
                localObstacleManager.tiles[i] = reader.ReadInt32();
            }

            int obstacleCount = reader.ReadInt32();
            localObstacleManager.obstacles = new config.MapPrefabObstacle[obstacleCount];
            for (int i = 0; i < obstacleCount; ++i)
            {
                config.MapPrefabObstacle obstacle = new config.MapPrefabObstacle();
                localObstacleManager.obstacles[i] = obstacle;

                obstacle.id = reader.ReadInt32();
                int vertexCount = reader.ReadInt32();
                obstacle.vertices = new Vector3[vertexCount];
                for (int k = 0; k < vertexCount; ++k)
                {
                    obstacle.vertices[k] = Utils.ReadVector3(reader);
                }
                int indexCount = reader.ReadInt32();
                obstacle.triangleIndices = new int[indexCount];
                for (int k = 0; k < indexCount; ++k)
                {
                    obstacle.triangleIndices[k] = reader.ReadInt32();
                }
            }

            localObstacleManager.obstacleMaterialPath = Utils.ReadString(reader);
            //--------------------version 1 end--------------------------
        }

    }
}
