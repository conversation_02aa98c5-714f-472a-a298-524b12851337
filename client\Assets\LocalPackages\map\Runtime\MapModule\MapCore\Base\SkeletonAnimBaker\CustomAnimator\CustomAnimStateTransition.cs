﻿ 



 
 

using UnityEngine;

using System.Collections.Generic;

namespace TFW.Map
{
    public enum TransitionConditionMode
    {
        //     The condition is true when the parameter value is true.
        If = 1,
        //     The condition is true when the parameter value is false.
        IfNot = 2,
        //     The condition is true when parameter value is greater than the threshold.
        Greater = 3,
        //     The condition is true when the parameter value is less than the threshold.
        Less = 4,
        //     The condition is true when parameter value is equal to the threshold.
        Equals = 6,
        //     The condition is true when the parameter value is not equal to the threshold.
        NotEqual = 7
    }

    public class CustomAnimStateTransitionCondition
    {
        public CustomAnimStateTransitionCondition(TransitionConditionMode mode, string parameter, float threshold)
        {
            this.mode = mode;
            this.parameter = parameter;
            this.parameterHash = Animator.StringToHash(parameter);
            this.threshold = threshold;
        }
        public TransitionConditionMode mode;
        public string parameter;
        public int parameterHash;
        public float threshold;
    }

    public class CustomAnimStateTransition
    {
        public CustomAnimStateTransition(CustomAnimationState from, CustomAnimationState to, CustomAnimStateTransitionCondition[] cond, bool hasExitTime, float exitTime, float transitionOffset, float transitionDuration, bool fixedTransitionDuration)
        {
            mFrom = from;
            mTo = to;
            mConditions = cond;
            mHasExitTime = hasExitTime;
            mExitTime = exitTime;
            //直接转换成秒
            mTransitionOffset = transitionOffset * mFrom.animInfo.lengthInSeconds;
            if (fixedTransitionDuration == false)
            {
                mTransitionDuration = mFrom.animInfo.lengthInSeconds * transitionDuration;
            }
            else
            {
                mTransitionDuration = transitionDuration;
            }
        }

        public bool MatchExitTimeOnly(float normalizedTime)
        {
            //只是简单的判断exit time,没有像unity那样考虑loop等,有需求再弄成那样
            if (mHasExitTime && normalizedTime < mExitTime)
            {
                return false;
            }

            return true;
        }

        //只匹配有条件的状态迁移,无条件的状态迁移只在动画播放完后检测!
        public bool Match(float normalizedTime, AnimationParameterInfo[] parameters)
        {
            if (mConditions.Length == 0)
            {
                return false;
            }

            //只是简单的判断exit time,没有像unity那样考虑loop等,有需求再弄成那样
            if (mHasExitTime && normalizedTime < mExitTime)
            {
                return false;
            }

            for (int i = 0; i < mConditions.Length; ++i)
            {
                var cond = mConditions[i];
                AnimationParameterInfo param = GetParameter(parameters, cond.parameter);
                bool match = false;
                bool isTrigger = (param.type == AnimatorControllerParameterType.Trigger);
                if (param.type == AnimatorControllerParameterType.Bool || isTrigger)
                {
                    if (cond.mode == TransitionConditionMode.If && param.defaultBool)
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.IfNot && !param.defaultBool)
                    {
                        match = true;
                    }
                    if (isTrigger)
                    {
                        mMatchedTriggers.Add(param);
                    }
                }
                else if (param.type == AnimatorControllerParameterType.Float)
                {
                    if (cond.mode == TransitionConditionMode.Equals && Mathf.Approximately(cond.threshold, param.defaultFloat))
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.Greater && param.defaultFloat > cond.threshold)
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.Less && param.defaultFloat < cond.threshold)
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.NotEqual && !Mathf.Approximately(param.defaultFloat, cond.threshold))
                    {
                        match = true;
                    }
                }
                else if (param.type == AnimatorControllerParameterType.Int)
                {
                    if (cond.mode == TransitionConditionMode.Equals && Mathf.Approximately(cond.threshold, param.defaultInt))
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.Greater && param.defaultInt > cond.threshold)
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.Less && param.defaultInt < cond.threshold)
                    {
                        match = true;
                    }
                    else if (cond.mode == TransitionConditionMode.NotEqual && !Mathf.Approximately(param.defaultInt, cond.threshold))
                    {
                        match = true;
                    }
                }

                if (!match)
                {
                    return false;
                }
            }

            for (int i = 0; i < mMatchedTriggers.Count; ++i)
            {
                mMatchedTriggers[i].defaultBool = false;
            }
            mMatchedTriggers.Clear();
            return true;
        }

        AnimationParameterInfo GetParameter(AnimationParameterInfo[] parameters, string name)
        {
            for (int i = 0; i < parameters.Length; ++i)
            {
                if (parameters[i].name == name)
                {
                    return parameters[i];
                }
            }
            return null;
        }

        public CustomAnimationState fromState { get { return mFrom; } }
        public CustomAnimationState toState { get { return mTo; } }
        public CustomAnimStateTransitionCondition[] conditions { get { return mConditions; } }
        public float transitionOffset { get { return mTransitionOffset; } }
        public float transitionDuration { get { return mTransitionDuration; } }

        CustomAnimationState mFrom;
        CustomAnimationState mTo;
        CustomAnimStateTransitionCondition[] mConditions;
        List<AnimationParameterInfo> mMatchedTriggers = new List<AnimationParameterInfo>();
        bool mHasExitTime;
        float mExitTime;
        //以秒为单位
        float mTransitionOffset;
        //以秒为单位
        float mTransitionDuration;
    }
}
