﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RegionSelection
    {
        public RegionSelection(int regionID, int vertexIndex)
        {
            this.regionID = regionID;
            this.vertexIndex = vertexIndex;
        }

        public int regionID = 0;
        public int vertexIndex = -1;
    }

    public class RegionSelectionManager
    {
        public RegionSelectionManager(System.Action<int, bool> selectionChangeCallback)
        {
            mSelectionChangeCallback = selectionChangeCallback;
        }

        public void AddSelection(int regionID, int vertexIndex)
        {
            for (int i = 0; i < mSelectedRegions.Count; ++i)
            {
                if (mSelectedRegions[i].regionID == regionID)
                {
                    Debug.Assert(false);
                    return;
                }
            }

            mSelectedRegions.Add(new RegionSelection(regionID, vertexIndex));

            if (mSelectionChangeCallback != null)
            {
                mSelectionChangeCallback(regionID, true);
            }
        }

        public void RemoveSelection(int regionID)
        {
            for (int i = 0; i < mSelectedRegions.Count; ++i)
            {
                if (mSelectedRegions[i].regionID == regionID)
                {
                    if (mSelectionChangeCallback != null)
                    {
                        mSelectionChangeCallback(regionID, false);
                    }
                    mSelectedRegions.RemoveAt(i);
                    return;
                }
            }
        }

        public void UpdateSelectionVertex(int regionID, int vertexIndex)
        {
            for (int i = 0; i < mSelectedRegions.Count; ++i)
            {
                if (mSelectedRegions[i].regionID == regionID)
                {
                    mSelectedRegions[i].vertexIndex = vertexIndex;
                    return;
                }
            }
        }

        public void ClearSelections()
        {
            for (int i = 0; i < mSelectedRegions.Count; ++i)
            {
                if (mSelectionChangeCallback != null)
                {
                    mSelectionChangeCallback(mSelectedRegions[i].regionID, false);
                }
            }
            mSelectedRegions.Clear();
        }

        public void SetSelection(int regionID, int vertexIndex)
        {
            ClearSelections();
            AddSelection(regionID, vertexIndex);
        }

        public List<RegionSelection> selections { get { return mSelectedRegions; } }
        public List<RegionSelection> selectionsCopy
        {
            get
            {
                var temp = new List<RegionSelection>();
                temp.AddRange(mSelectedRegions);
                return temp;
            }
        }

        List<RegionSelection> mSelectedRegions = new List<RegionSelection>();
        System.Action<int, bool> mSelectionChangeCallback;
    }
}

#endif