﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    public partial class BlendTerrainLayerUI : UnityEditor.Editor
    {
        enum Mode
        {
            //增量式修改高度
            ChangeHeight,
            //将tile的顶点高度设置为0
            ResetHeight,
            //将tile的edge顶点高度设置为0
            ResetEdgeHeight,
            //平滑顶点
            Smooth,
            //将某个threshold以下的顶点高度设置为0,以便更好的优化mesh
            SuppressVertex,
            //测试放置物体
            //PlaceObjectAtHeight,
        }

        int mResolution = 18;
        //in meter unit
        float mBrushSize = 70;
        float mTargetHeight = 10;
        float mSupressVertexHeightThreshold = 0.1f;
        float mBrushStrength = 0.05f;
        bool mSmoothBrush = false;
        //不能改变edge vertex的高度
        bool mDontChangeEdgeVertexHeight = false;
        //只在鼠标选中的tile中绘制
        bool mPaintInOneTile = false;
        Mode mMode = Mode.ChangeHeight;
        List<CoordInfo> mTileCoords = new List<CoordInfo>();

        bool LockEdgeVertexHeight()
        {
            return mPaintInOneTile || mDontChangeEdgeVertexHeight;
        }

        void DrawHeightEditorInspector()
        {
            EditorGUILayout.BeginHorizontal();
            mResolution = EditorGUILayout.IntField(new GUIContent("Terrain Resolution", "将地表划分为NxN个格子,N即Resolution"), mResolution);
            EditorGUILayout.EndHorizontal();

            mMode = (Mode)EditorGUILayout.EnumPopup(new GUIContent("Height Mode", "绘制地表高度模式"), mMode);

            if (mMode == Mode.ChangeHeight)
            {
                mBrushSize = EditorGUILayout.FloatField(new GUIContent("Brush Size", "绘制高度笔刷大小"), mBrushSize);
                mBrushSize = Mathf.Max(1, mBrushSize);
                mBrushStrength = EditorGUILayout.Slider(new GUIContent("Brush Strength", "笔刷强度"), mBrushStrength, 0.0f, 1.0f);
                mTargetHeight = EditorGUILayout.FloatField(new GUIContent("Height", "指定的绘制高度"), mTargetHeight);
                mSmoothBrush = EditorGUILayout.ToggleLeft(new GUIContent("Smooth Brush", "是否使用平滑笔刷"), mSmoothBrush);
                mPaintInOneTile = EditorGUILayout.ToggleLeft(new GUIContent("Paint In One Tile", "只绘制鼠标指针所在的一个tile"), mPaintInOneTile);
                mDontChangeEdgeVertexHeight = EditorGUILayout.ToggleLeft(new GUIContent("Lock Edge Vertex Height", "不改变tile边界的顶点高度"), mDontChangeEdgeVertexHeight);
            }
            else if (mMode == Mode.ResetHeight)
            {
                mBrushSize = EditorGUILayout.FloatField(new GUIContent("Brush Size", "绘制高度笔刷大小"), mBrushSize);
                mBrushSize = Mathf.Max(1, mBrushSize);
                mTargetHeight = EditorGUILayout.FloatField(new GUIContent("Height", "指定的绘制高度"), mTargetHeight);
                mPaintInOneTile = EditorGUILayout.ToggleLeft(new GUIContent("Paint In One Tile", "只绘制鼠标指针所在的一个tile"), mPaintInOneTile);
                mDontChangeEdgeVertexHeight = EditorGUILayout.ToggleLeft(new GUIContent("Lock Edge Vertex Height", "不改变tile边界的顶点高度"), mDontChangeEdgeVertexHeight);
            }
            else if (mMode == Mode.SuppressVertex)
            {
                mBrushSize = EditorGUILayout.FloatField(new GUIContent("Brush Size", "笔刷大小"), mBrushSize);
                mBrushSize = Mathf.Max(1, mBrushSize);
                mSupressVertexHeightThreshold = EditorGUILayout.FloatField(new GUIContent("Suppress Vertex Height Threshold", "指定高度以下的顶点高度都会被设置为0"), mSupressVertexHeightThreshold);
                mPaintInOneTile = EditorGUILayout.ToggleLeft(new GUIContent("Paint In One Tile", "只绘制鼠标指针所在的一个tile"), mPaintInOneTile);
                mDontChangeEdgeVertexHeight = EditorGUILayout.ToggleLeft(new GUIContent("Lock Edge Vertex Height", "不改变tile边界的顶点高度"), mDontChangeEdgeVertexHeight);
            }
            else if (mMode == Mode.Smooth)
            {
                mPaintInOneTile = false;
                mBrushSize = EditorGUILayout.FloatField(new GUIContent("Brush Size", "笔刷大小"), mBrushSize);
                mBrushSize = Mathf.Max(1, mBrushSize);
                mBrushStrength = EditorGUILayout.Slider(new GUIContent("Brush Strength", "笔刷强度"), mBrushStrength, 0.0f, 1.0f);
                mDontChangeEdgeVertexHeight = EditorGUILayout.ToggleLeft(new GUIContent("Lock Edge Vertex Height", "不改变tile边界的顶点高度"), mDontChangeEdgeVertexHeight);
            }
            else if (mMode == Mode.ResetEdgeHeight)
            {
                mBrushSize = EditorGUILayout.FloatField(new GUIContent("Brush Size", "笔刷大小"), mBrushSize);
                mBrushSize = Mathf.Max(1, mBrushSize);
            }
            //else if (mMode == Mode.PlaceObjectAtHeight)
            //{
            //}
            else
            {
                Debug.Assert(false, "todo");
            }

#if UNITY_EDITOR_WIN
            if (GUILayout.Button("Fix Edge Normal"))
            {
                var f = new FixTerrainEdgeNormal();
                f.Fix(mLogic.layer);
            }
            if (GUILayout.Button("Suppress All Tile's Vertex Height"))
            {
                SuppressAllTileVertexHeight();
            }
#endif

            BrushManagerUI.Draw(mLogic.brushManager);
        }

        CoordInfo GetCoordinateForSuppressVertexHeight(int x, int y)
        {
            var tile = mLogic.layer.GetTile(x, y);
            if (tile == null || tile.heights == null)
            {
                return null;
            }

            CoordInfo coord = new CoordInfo();
            coord.tileCoord = new Vector2Int(x, y);

            //不影响edge
            int minX = 1;
            int minY = 1;
            int maxX = tile.resolution - 1;
            int maxY = tile.resolution - 1;

            var rangeInTile = new RectInt(minX, minY, maxX - minX, maxY - minY);
            //计算tile于笔刷的顶点intersection
            coord.rangeInTile = rangeInTile;

            coord.brushHeights.Clear();
            int width = rangeInTile.width;
            int height = rangeInTile.height;
            var oldHeights = tile.heights;

            for (int i = 0; i <= height; ++i)
            {
                for (int j = 0; j <= width; ++j)
                {
                    int idx = (i + minY) * (tile.resolution + 1) + j + minX;
                    float oldHeight = oldHeights[idx];
                    if (Mathf.Abs(oldHeight) < mSupressVertexHeightThreshold)
                    {
                        oldHeight = 0;
                    }
                    coord.brushHeights.Add(oldHeight);
                }
            }

            return coord;
        }

        void SuppressAllTileVertexHeight()
        {
            mTileCoords.Clear();
            var layer = mLogic.layer;

            int horizontalTileCount = layer.horizontalTileCount;
            int verticaltileCount = layer.verticalTileCount;
            for (int y = 0; y < verticaltileCount; ++y)
            {
                for (int x = 0; x < horizontalTileCount; ++x)
                {
                    var coord = GetCoordinateForSuppressVertexHeight(x, y);

                    if (coord != null)
                    {
                        mTileCoords.Add(coord);
                    }
                }
            }

            SupressVertexHeight();
        }

        void SupressVertexHeight()
        {
            if (mTileCoords.Count > 0)
            {
                CompoundAction actions = new CompoundAction("Suppress All Tile's Vertex Height");
                var layer = mLogic.layer;
                for (int i = 0; i < mTileCoords.Count; ++i)
                {
                    var tile = layer.GetTile(mTileCoords[i].tileCoord.x, mTileCoords[i].tileCoord.y);
                    SetHeight(actions, mTileCoords[i], tile.resolution);
                }

                //FixEdge(actions, mTileCoords);

                ActionManager.instance.PushAction(actions, true, false);
            }
        }

        class CoordInfo
        {
            public Vector2Int tileCoord;
            public RectInt rangeInTile;
            public List<float> brushHeights = new List<float>();

            public bool IsEdgeModified(int resolution, TerrainTileEdgeCorner edgeOrCorner)
            {
                switch (edgeOrCorner)
                {
                    case TerrainTileEdgeCorner.BottomEdge:
                        return rangeInTile.yMin == 0;
                    case TerrainTileEdgeCorner.LeftBottomCorner:
                        return rangeInTile.xMin == 0 && rangeInTile.yMin == 0;
                    case TerrainTileEdgeCorner.RightBottomCorner:
                        return rangeInTile.xMax == resolution && rangeInTile.yMax == 0;
                    case TerrainTileEdgeCorner.RightEdge:
                        return rangeInTile.xMax == resolution;
                    case TerrainTileEdgeCorner.LeftEdge:
                        return rangeInTile.xMin == 0;
                    case TerrainTileEdgeCorner.LeftTopCorner:
                        return rangeInTile.xMin == 0 && rangeInTile.yMax == resolution;
                    case TerrainTileEdgeCorner.RightTopCorner:
                        return rangeInTile.xMax == resolution && rangeInTile.yMax == resolution;
                    case TerrainTileEdgeCorner.TopEdge:
                        return rangeInTile.yMax == resolution;
                    default:
                        Debug.Assert(false, "todo");
                        break;
                }
                return false;
            }
        }

        void DrawHeightEditorSceneGUI(Event currentEvent)
        {
            var map = Map.currentMap;

            var camera = map.camera.firstCamera;
            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, camera);
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);

            var ray = camera.ScreenPointToRay(screenPos);
            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.B)
                {
                    mSmoothBrush = !mSmoothBrush;
                    Repaint();
                }
#if UNITY_EDITOR_WIN // 临时修复ios打包问题，需要 wangzhiwei review
                if (currentEvent.keyCode == KeyCode.R)
                {
                    var f = new FixTerrainEdgeNormal();
                    f.Fix(mLogic.layer);
                }
#endif
            }

            var origin = camera.transform.position;
            Vector3 intersection;
            Vector3 normal;
            var physxEngine = map.root.GetComponent<PhysxSetup>();
            bool hit = physxEngine.Raycast(origin, ray.direction, out intersection, out normal);
            if (hit)
            {
                worldPos = intersection;
            }

            if ((currentEvent.type == EventType.MouseDown || currentEvent.type == EventType.MouseDrag) && currentEvent.button == 0 && currentEvent.alt == false)
            {
                bool lowerTerrain = currentEvent.control;
                if (mMode == Mode.ChangeHeight)
                {
                    GetCoordinates(worldPos, mBrushSize, lowerTerrain, false);
                    if (mTileCoords.Count > 0)
                    {
                        CompoundAction actions = new CompoundAction("Set Ground Height");

                        for (int i = 0; i < mTileCoords.Count; ++i)
                        {
                            SetHeight(actions, mTileCoords[i], mResolution);
                        }

                        FixEdge(actions, mTileCoords);

                        ActionManager.instance.PushAction(actions, true, false);
                    }
                }
                else if (mMode == Mode.SuppressVertex)
                {
                    mTileCoords.Clear();
                    var coord = mLogic.layer.layerData.FromWorldPositionToCoordinate(worldPos);
                    var coordInfo = GetCoordinateForSuppressVertexHeight(coord.x, coord.y);
                    if (coordInfo != null)
                    {
                        mTileCoords.Add(coordInfo);
                    }
                    SupressVertexHeight();
                }
                else if (mMode == Mode.ResetHeight)
                {
                    GetCoordinates(worldPos, mBrushSize, lowerTerrain, false);
                    if (mTileCoords.Count > 0)
                    {
                        CompoundAction actions = new CompoundAction("Reset Ground Height");

                        for (int i = 0; i < mTileCoords.Count; ++i)
                        {
                            ResetHeight(actions, mTileCoords[i], mResolution);
                        }

                        FixEdge(actions, mTileCoords);

                        ActionManager.instance.PushAction(actions, true, false);
                    }
                }
                else if (mMode == Mode.Smooth)
                {
                    //平滑选中的顶点
                    DoSmooth(worldPos);
                }
                else if (mMode == Mode.ResetEdgeHeight)
                {
                    if (currentEvent.type == EventType.MouseDown)
                    {
                        GetCoordinates(worldPos, mBrushSize, lowerTerrain, false);
                        CompoundAction actions = new CompoundAction("Reset Edge Height");
                        for (int i = 0; i < mTileCoords.Count; ++i)
                        {
                            ResetEdgeHeight(actions, mTileCoords[i]);
                        }
                        ActionManager.instance.PushAction(actions, true, false);
                    }
                }
                //else if (mMode == Mode.PlaceObjectAtHeight)
                //{
                //    var obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                //    float height = mLogic.layer.GetHeightAtPos(worldPos.x, worldPos.z);
                //    obj.transform.position = new Vector3(worldPos.x, height, worldPos.z);
                //}
            }

            //if (mMode != Mode.PlaceObjectAtHeight)
            {
                if (mMode == Mode.Smooth)
                {
                    Handles.DrawWireDisc(worldPos, Vector3.up, mBrushSize * 0.5f);
                }
                else
                {
                    Handles.DrawLine(worldPos, worldPos + normal * 10);
                    Handles.DrawWireDisc(worldPos, normal, mBrushSize * 0.5f);
                }
            }
            SceneView.RepaintAll();

            HandleUtility.AddDefaultControl(0);
        }

        Rect GetTileWorldRect(int x, int y)
        {
            var pos = mLogic.layerData.FromCoordinateToWorldPosition(x, y);
            return new Rect(pos.x, pos.z, mLogic.layerData.tileWidth, mLogic.layerData.tileHeight);
        }

        Vector2Int FromWorldPositionToCoordinate(float x, float z, int resolution)
        {
            float gridSize = mLogic.layerData.tileWidth / resolution;
            return new Vector2Int(Mathf.FloorToInt(x / gridSize), Mathf.FloorToInt(z / gridSize));
        }

        void GetCoordinates(Vector3 worldPos, float brushSize, bool lowerTerrain, bool suppressVertexHeight)
        {
            mTileCoords.Clear();
            var minPosX = worldPos.x - brushSize * 0.5f;
            var minPosZ = worldPos.z - brushSize * 0.5f;
            var maxPosX = worldPos.x + brushSize * 0.5f;
            var maxPosZ = worldPos.z + brushSize * 0.5f;
            var minCoord = mLogic.layerData.FromWorldPositionToCoordinate(new Vector3(minPosX, 0, minPosZ));
            var maxCoord = mLogic.layerData.FromWorldPositionToCoordinate(new Vector3(maxPosX, 0, maxPosZ));
            if (mPaintInOneTile)
            {
                minCoord = mLogic.layerData.FromWorldPositionToCoordinate(worldPos);
                maxCoord = minCoord;
            }
            Rect brushRect = new Rect(minPosX, minPosZ, maxPosX - minPosX, maxPosZ - minPosZ);
            var brush = mLogic.brushManager.GetActiveBrush();
            int mipmap = brush.GetMipmap((int)brushSize, false);
            float gridSize = mLogic.layerData.tileWidth / mResolution;
            float brushRectMinX = brushRect.x;
            float brushRectMinZ = brushRect.y;
            float targetHeight = lowerTerrain ? -mTargetHeight : mTargetHeight;

            Func<int, float, float, bool, float> sampleFunc = null;
            if (mSmoothBrush)
            {
                sampleFunc = (Func<int, float, float, bool, float>)Delegate.CreateDelegate(typeof(Func<int, float, float, bool, float>), brush, typeof(Brush).GetMethod("SampleAlphaBilinear"));
            }
            else
            {
                sampleFunc = (Func<int, float, float, bool, float>)Delegate.CreateDelegate(typeof(Func<int, float, float, bool, float>), brush, typeof(Brush).GetMethod("SampleAlpha"));
            }

            bool lockVertexHeight = LockEdgeVertexHeight();
            for (int y = minCoord.y; y <= maxCoord.y; ++y)
            {
                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                {
                    if (x >= 0 && x < mLogic.layerData.horizontalTileCount && y >= 0 && y < mLogic.layerData.verticalTileCount)
                    {
                        Rect tileRect = GetTileWorldRect(x, y);
                        Rect intersection;
                        bool intersected = Utils.GetRectIntersection(tileRect, brushRect, out intersection);
                        Debug.Assert(intersected);
                        var layerData = mLogic.layerData;
                        var tileStartPos = layerData.FromCoordinateToWorldPosition(x, y);
                        var intersectMinCoord = FromWorldPositionToCoordinate(intersection.xMin - tileStartPos.x, intersection.yMin - tileStartPos.z, mResolution);
                        var intersectMaxCoord = FromWorldPositionToCoordinate(intersection.xMax - tileStartPos.x, intersection.yMax - tileStartPos.z, mResolution);
                        CoordInfo coord = new CoordInfo();
                        coord.tileCoord = new Vector2Int(x, y);

                        int minX = intersectMinCoord.x;
                        int minY = intersectMinCoord.y;
                        int maxX = intersectMaxCoord.x;
                        int maxY = intersectMaxCoord.y;
                        if (lockVertexHeight)
                        {
                            minY = Mathf.Max(1, minY);
                            minX = Mathf.Max(1, minX);
                            maxY = Mathf.Min(mResolution - 1, maxY);
                            maxX = Mathf.Min(mResolution - 1, maxX);
                        }
                        var rangeInTile = new RectInt(minX, minY, maxX - minX, maxY - minY);
                        //计算tile于笔刷的顶点intersection
                        coord.rangeInTile = rangeInTile;

                        coord.brushHeights.Clear();
                        int width = rangeInTile.width;
                        int height = rangeInTile.height;
                        var tile = layerData.GetTile(x, y);
                        var oldHeights = tile.heights;
                        bool invalidOldHeight = false;
                        if (oldHeights == null || oldHeights.Length != (mResolution + 1) * (mResolution + 1))
                        {
                            invalidOldHeight = true;
                        }

                        bool addCoord = true;
                        if (suppressVertexHeight)
                        {
                            if (invalidOldHeight)
                            {
                                addCoord = false;
                            }
                            else
                            {
                                for (int i = 0; i <= height; ++i)
                                {
                                    for (int j = 0; j <= width; ++j)
                                    {
                                        int idx = (i + minY) * (mResolution + 1) + j + minX;
                                        float oldHeight = oldHeights[idx];
                                        if (Mathf.Abs(oldHeight) < mSupressVertexHeightThreshold)
                                        {
                                            oldHeight = 0;
                                        }
                                        coord.brushHeights.Add(oldHeight);
                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i <= height; ++i)
                            {
                                for (int j = 0; j <= width; ++j)
                                {
                                    float posX = tileStartPos.x + (j + rangeInTile.x) * gridSize;
                                    float posZ = tileStartPos.z + (i + rangeInTile.y) * gridSize;
                                    float rx = (posX - brushRectMinX) / brushSize;
                                    float ry = (posZ - brushRectMinZ) / brushSize;
                                    int idx = (i + minY) * (mResolution + 1) + j + minX;
                                    float sampleValue = sampleFunc(mipmap, rx, ry, false);
                                    if (invalidOldHeight)
                                    {
                                        coord.brushHeights.Add(sampleValue * targetHeight * mBrushStrength);
                                    }
                                    else
                                    {
                                        float oldHeight = oldHeights[idx];
                                        coord.brushHeights.Add(oldHeight + sampleValue * targetHeight * mBrushStrength);
                                    }
                                }
                            }
                        }

                        if (addCoord)
                        {
                            mTileCoords.Add(coord);
                        }
                    }
                }
            }
        }

        void SetHeight(CompoundAction actions, CoordInfo tileCoord, int resolution)
        {
            int tileX = tileCoord.tileCoord.x;
            int tileY = tileCoord.tileCoord.y;
            int minX = tileCoord.rangeInTile.min.x;
            int minY = tileCoord.rangeInTile.min.y;
            int maxX = tileCoord.rangeInTile.max.x;
            int maxY = tileCoord.rangeInTile.max.y;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
            var tile = layer.GetTile(tileX, tileY);
            if (tile != null)
            {
                var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, tileX, tileY, minX, minY, maxX, maxY, resolution);
                act.Begin();
                layer.SetHeights(tileX, tileY, minX, minY, maxX, maxY, resolution, tileCoord.brushHeights, true, LockEdgeVertexHeight());
                act.End();
                actions.Add(act);
            }
        }

        void ResetHeight(CompoundAction actions, CoordInfo tileCoord, int resolution)
        {
            int tileX = tileCoord.tileCoord.x;
            int tileY = tileCoord.tileCoord.y;
            int minX = 0;
            int minY = 0;
            int maxX = resolution;
            int maxY = resolution;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
            var tile = layer.GetTile(tileX, tileY);
            if (tile != null)
            {
                var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, tileX, tileY, minX, minY, maxX, maxY, resolution);
                act.Begin();
                layer.SetHeight(tileX, tileY, minX, minY, maxX, maxY, resolution, 0, true, LockEdgeVertexHeight());
                act.End();
                actions.Add(act);
            }
        }

        bool In(int x, int y, List<CoordInfo> coords)
        {
            var v = new Vector2Int(x, y);
            for (int i = 0; i < coords.Count; ++i)
            {
                if (coords[i].tileCoord == v)
                {
                    return true;
                }
            }
            return false;
        }

        void FixEdge(CompoundAction actions, List<CoordInfo> coords)
        {
            if (!mPaintInOneTile)
            {
                FixEdgeWithZeroHeight(actions, coords);
            }
        }

        //将tile和neighbour弄成一样的分辨率
        void MakeSameHeightAndResolution(CompoundAction actions, BlendTerrainLayer layer, int tileX, int tileY, int tx, int ty, int neighbourTileX, int neighbourTileY, int nx, int ny, int newResolution)
        {
            var t = layer.GetTile(tileX, tileY);
            float h = t.GetHeight(tx, ty);
            var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, tileX, tileY, nx, ny, nx, ny, newResolution);
            act.Begin();
            layer.SetHeight(neighbourTileX, neighbourTileY, nx, ny, nx, ny, newResolution, h, true, false);
            act.End();
            actions.Add(act);
        }

        //将tile和neighbour弄成一样的分辨率
        void MakeSameHeightAndResolution(CompoundAction actions, BlendTerrainLayer layer, int tileX, int tileY, int tMinX, int tMinY, int tMaxX, int tMaxY, int neighbourTileX, int neighbourTileY, int nMinX, int nMinY, int newResolution)
        {
            var t = layer.GetTile(tileX, tileY);
            var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, neighbourTileX, neighbourTileY, nMinX, nMinY, nMinX + tMaxX - tMinX, nMinY + tMaxY - tMinY, newResolution);
            act.Begin();
            for (int i = tMinY; i <= tMaxY; ++i)
            {
                for (int j = tMinX; j <= tMaxX; ++j)
                {
                    float h = t.GetHeight(j, i);
                    int x = nMinX + j - tMinX;
                    int y = nMinY + i - tMinY;
                    layer.SetHeight(neighbourTileX, neighbourTileY, x, y, x, y, newResolution, h, true, false);
                }
            }
            act.End();
            actions.Add(act);
        }

        /*
         * 修复的edge的逻辑有2点:
         * 1.如果某个tile的edge顶点被修改了,则将他的neighbour的resolution设置成相同,然后将邻接边的顶点设置为相同高度,否则用2
         * 2.如果这些tiles的相邻tile resolution不同,则将它们的邻接边的edge顶点高度设置为0,避免有接缝问题
        */
        void FixEdgeWithZeroHeight(CompoundAction actions, List<CoordInfo> coords)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
            int h = layer.horizontalTileCount;
            int v = layer.verticalTileCount;
            for (int i = 0; i < coords.Count; ++i)
            {
                var tileCoord = coords[i];
                int tileX = tileCoord.tileCoord.x;
                int tileY = tileCoord.tileCoord.y;
                var tile = layer.GetTile(tileX, tileY);
                if (tile != null)
                {
                    //check 8 neighbours
                    for (int oy = -1; oy <= 1; ++oy)
                    {
                        for (int ox = -1; ox <= 1; ++ox)
                        {
                            var nx = tileX + ox;
                            var ny = tileY + oy;
                            if (nx >= 0 && nx < h && ny >= 0 && ny < v)
                            {
                                if (!In(nx, ny, coords))
                                {
                                    var neighbourTile = layer.GetTile(nx, ny);
                                    if (neighbourTile != null)
                                    {
                                        int nr = neighbourTile.resolution;
                                        int tr = tile.resolution;
                                        if (neighbourTile != null)
                                        {
                                            if (ox == 1 && oy == 1)
                                            {
                                                //right top neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.RightTopCorner))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, tr, tr, nx, ny, 0, 0, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, 0, 0, 0, 0, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, 0, 0, 0, 0, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == 1 && oy == -1)
                                            {
                                                //right bottom neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.RightBottomCorner))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, tr, 0, nx, ny, 0, tr, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, 0, nr, 0, nr, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, 0, nr, 0, nr, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == 1 && oy == 0)
                                            {
                                                //right neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.RightEdge))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, tr, 0, tr, tr, nx, ny, 0, 0, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, 0, 0, 0, nr, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, 0, 0, 0, nr, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == -1 && oy == 1)
                                            {
                                                //left top neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.LeftTopCorner))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, 0, tr, nx, ny, tr, 0, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, nr, 0, nr, 0, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, nr, 0, nr, 0, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == -1 && oy == -1)
                                            {
                                                //left bottom neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.LeftBottomCorner))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, 0, 0, nx, ny, tr, tr, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, nr, nr, nr, nr, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, nr, nr, nr, nr, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == -1 && oy == 0)
                                            {
                                                //left neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.LeftEdge))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, 0, 0, 0, tr, nx, ny, tr, 0, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, nr, 0, nr, nr, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, nr, 0, nr, nr, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == 0 && oy == 1)
                                            {
                                                //top neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.TopEdge))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, 0, tr, tr, tr, nx, ny, 0, 0, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, 0, 0, nr, 0, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, 0, 0, nr, 0, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else if (ox == 0 && oy == -1)
                                            {
                                                //bottom neighbour
                                                if (tileCoord.IsEdgeModified(tr, TerrainTileEdgeCorner.BottomEdge))
                                                {
                                                    MakeSameHeightAndResolution(actions, layer, tileX, tileY, 0, 0, tr, 0, nx, ny, 0, tr, tr);
                                                }
                                                else
                                                {
                                                    if (nr != tr)
                                                    {
                                                        var act = new ActionChangeTerrainHeightsAndResolution(mLogic.layerID, nx, ny, 0, nr, nr, nr, nr);
                                                        act.Begin();
                                                        layer.SetHeight(nx, ny, 0, nr, nr, nr, nr, 0, true, false);
                                                        act.End();
                                                        actions.Add(act);
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                Debug.Assert(false, "unknown");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        void ResetEdgeHeight(CompoundAction actions, CoordInfo tileCoord)
        {
            int tileX = tileCoord.tileCoord.x;
            int tileY = tileCoord.tileCoord.y;
            int minX = 0;
            int minY = 0;
            var resolution = mLogic.layerData.GetTile(tileX, tileY).resolution;
            int maxX = resolution;
            int maxY = resolution;
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as BlendTerrainLayer;
            var act = new ActionChangeTerrainHeights(mLogic.layerID, tileX, tileY, minX, minY, maxX, minY, resolution);
            act.Begin();
            layer.SetHeight(tileX, tileY, minX, minY, maxX, minY, resolution, 0, false, false);
            act.End();
            actions.Add(act);

            var act1 = new ActionChangeTerrainHeights(mLogic.layerID, tileX, tileY, minX, minY, minX, maxY, resolution);
            act1.Begin();
            layer.SetHeight(tileX, tileY, minX, minY, minX, maxY, resolution, 0, false, false);
            act1.End();
            actions.Add(act1);

            var act2 = new ActionChangeTerrainHeights(mLogic.layerID, tileX, tileY, minX, maxY, maxX, maxY, resolution);
            act2.Begin();
            layer.SetHeight(tileX, tileY, minX, maxY, maxX, maxY, resolution, 0, false, false);
            act2.End();
            actions.Add(act2);

            var act3 = new ActionChangeTerrainHeights(mLogic.layerID, tileX, tileY, maxX, minY, maxX, maxY, resolution);
            act3.Begin();
            layer.SetHeight(tileX, tileY, maxX, minY, maxX, maxY, resolution, 0, false, false);
            layer.UpdateMesh(tileX, tileY);
            act3.End();
            actions.Add(act3);
        }

        void DoSmooth(Vector3 worldPos)
        {
            //smooth只发生在和worldPos所在tile的resolution相同的tile上
            mTileCoords.Clear();

            var layerData = mLogic.layerData;
            var cursorCoord = layerData.FromWorldPositionToCoordinate(worldPos);
            var cursorTile = layerData.GetTile(cursorCoord.x, cursorCoord.y);
            if (cursorTile == null)
            {
                return;
            }

            int resolution = cursorTile.resolution;
            if (cursorTile.heights == null)
            {
                return;
            }

            var minPosX = worldPos.x - mBrushSize * 0.5f;
            var minPosZ = worldPos.z - mBrushSize * 0.5f;
            var maxPosX = worldPos.x + mBrushSize * 0.5f;
            var maxPosZ = worldPos.z + mBrushSize * 0.5f;
            var minCoord = layerData.FromWorldPositionToCoordinate(new Vector3(minPosX, 0, minPosZ));
            var maxCoord = layerData.FromWorldPositionToCoordinate(new Vector3(maxPosX, 0, maxPosZ));

            Rect brushRect = new Rect(minPosX, minPosZ, maxPosX - minPosX, maxPosZ - minPosZ);
            var brush = mLogic.brushManager.GetActiveBrush();
            int mipmap = brush.GetMipmap((int)mBrushSize, false);
            float gridSize = mLogic.layerData.tileWidth / resolution;
            float brushRectMinX = brushRect.x;
            float brushRectMinZ = brushRect.y;

            Func<int, float, float, bool, float> sampleFunc = null;
            if (mSmoothBrush)
            {
                sampleFunc = (Func<int, float, float, bool, float>)Delegate.CreateDelegate(typeof(Func<int, float, float, bool, float>), brush, typeof(Brush).GetMethod("SampleAlphaBilinear"));
            }
            else
            {
                sampleFunc = (Func<int, float, float, bool, float>)Delegate.CreateDelegate(typeof(Func<int, float, float, bool, float>), brush, typeof(Brush).GetMethod("SampleAlpha"));
            }

            //计算所选区域的平均高度
            float averageHeight = 0;
            int averageHeightCount = 0;
            for (int y = minCoord.y; y <= maxCoord.y; ++y)
            {
                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                {
                    if (x >= 0 && x < layerData.horizontalTileCount && y >= 0 && y < layerData.verticalTileCount)
                    {
                        var tile = layerData.GetTile(x, y);
                        if (tile.resolution == cursorTile.resolution)
                        {
                            Rect tileRect = GetTileWorldRect(x, y);
                            Rect intersection;
                            bool intersected = Utils.GetRectIntersection(tileRect, brushRect, out intersection);
                            Debug.Assert(intersected);

                            var tileStartPos = layerData.FromCoordinateToWorldPosition(x, y);
                            var intersectMinCoord = FromWorldPositionToCoordinate(intersection.xMin - tileStartPos.x, intersection.yMin - tileStartPos.z, resolution);
                            var intersectMaxCoord = FromWorldPositionToCoordinate(intersection.xMax - tileStartPos.x, intersection.yMax - tileStartPos.z, resolution);
                            CoordInfo coord = new CoordInfo();
                            coord.tileCoord = new Vector2Int(x, y);
                            var rangeInTile = new RectInt(intersectMinCoord, intersectMaxCoord - intersectMinCoord);
                            //计算tile于笔刷的顶点intersection
                            coord.rangeInTile = rangeInTile;
                            mTileCoords.Add(coord);
                            coord.brushHeights.Clear();
                            int width = rangeInTile.width;
                            int height = rangeInTile.height;

                            var oldHeights = tile.heights;

                            int minX = rangeInTile.x;
                            int minY = rangeInTile.y;
                            for (int i = 0; i <= height; ++i)
                            {
                                for (int j = 0; j <= width; ++j)
                                {
                                    int idx = (i + minY) * (resolution + 1) + j + minX;
                                    averageHeight += oldHeights[idx];
                                    ++averageHeightCount;
                                }
                            }
                        }
                    }
                }
            }

            if (averageHeightCount > 0)
            {
                averageHeight /= averageHeightCount;

                for (int k = 0; k < mTileCoords.Count; ++k)
                {
                    var rangeInTile = mTileCoords[k].rangeInTile;
                    int width = rangeInTile.width;
                    int height = rangeInTile.height;
                    int minX = rangeInTile.x;
                    int minY = rangeInTile.y;
                    var brushHeights = mTileCoords[k].brushHeights;
                    int x = mTileCoords[k].tileCoord.x;
                    int y = mTileCoords[k].tileCoord.y;
                    var tile = layerData.GetTile(x, y);
                    var oldHeights = tile.heights;
                    var tileStartPos = layerData.FromCoordinateToWorldPosition(x, y);
                    for (int i = 0; i <= height; ++i)
                    {
                        for (int j = 0; j <= width; ++j)
                        {
                            float posX = tileStartPos.x + (j + rangeInTile.x) * gridSize;
                            float posZ = tileStartPos.z + (i + rangeInTile.y) * gridSize;
                            float rx = (posX - brushRectMinX) / mBrushSize;
                            float ry = (posZ - brushRectMinZ) / mBrushSize;
                            int idx = (i + minY) * (resolution + 1) + j + minX;
                            float sampleValue = sampleFunc(mipmap, rx, ry, false);

                            float s = sampleValue * mBrushStrength * 0.1f;
                            float oldHeight = oldHeights[idx];
                            float delta = averageHeight - oldHeight;
                            brushHeights.Add(oldHeight + delta * s);
                        }
                    }
                }

                if (mTileCoords.Count > 0)
                {
                    CompoundAction actions = new CompoundAction("Smooth Ground Height");

                    for (int i = 0; i < mTileCoords.Count; ++i)
                    {
                        SetHeight(actions, mTileCoords[i], resolution);
                    }

                    ActionManager.instance.PushAction(actions, true, false);
                }
            }
        }
    }
}

#endif