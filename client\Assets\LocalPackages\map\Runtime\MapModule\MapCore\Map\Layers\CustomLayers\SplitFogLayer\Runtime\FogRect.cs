﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class FogRect
    {
        public FogRect(int id, float minX, float minY, float maxX, float maxY)
        {
            this.id = id;
            this.minX = minX;
            this.maxX = maxX;
            this.minY = minY;
            this.maxY = maxY;
        }

        public bool IsEmpty()
        {
            return maxX < minX || maxY < minY;
        }

        public float width { get { return maxX - minX; } }
        public float height { get { return maxY - minY; } }
        public Vector3 center { get { return new Vector3((minX + maxX) * 0.5f, 0, (minY + maxY) * 0.5f); } }
        public static bool operator ==(FogRect a, FogRect b)
        {
            return Mathf.Approximately(a.minX, b.minX) &&
                Mathf.Approximately(a.minY, b.minY) &&
                Mathf.Approximately(a.maxX, b.maxX) &&
                Mathf.Approximately(a.maxY, b.maxY);
        }
        public static bool operator !=(FogRect a, FogRect b)
        {
            return !(a == b);
        }
        public override string ToString()
        {
            return string.Format("{0}, {1}, {2}, {3}", minX, minY, maxX, maxY);
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public bool Contains(float x, float y)
        {
            if (x >= minX && x <= maxX && y >= minY && y <= maxY)
            {
                return true;
            }
            return false;
        }

        public static bool Intersect(FogRect a, FogRect b)
        {
            if (a.minX > b.maxX || a.minY > b.maxY || b.minX > a.maxX || b.minY > a.maxY)
            {
                return false;
            }
            return true;
        }

        public bool Intersect(float minX, float minY, float maxX, float maxY)
        {
            if (minX > this.maxX || minY > this.maxY || this.minX > maxX || this.minY > maxY)
            {
                return false;
            }
            return true;
        }

        public void Add(FogRect f)
        {
            minX = Mathf.Min(minX, f.minX);
            minY = Mathf.Min(minY, f.minY);
            maxX = Mathf.Max(maxX, f.maxX);
            maxY = Mathf.Max(maxY, f.maxY);
        }

        public void Subtract(FogRect f, List<FogRect> result)
        {
            //bottom region
            float h = f.minY - minY;
            if (h > 0)
            {
                result.Add(new FogRect(0, minX, minY, maxX, minY + h));
            }

            //top region
            h = maxY - f.maxY;
            if (h > 0)
            {
                result.Add(new FogRect(0, minX, f.maxY, maxX, maxY));
            }

            //left region
            float w = f.minX - minX;
            if (w > 0)
            {
                result.Add(new FogRect(0, minX, f.minY, f.minX, f.maxY));
            }

            //right region
            w = maxX - f.maxX;
            if (w > 0)
            {
                result.Add(new FogRect(0, f.maxX, f.minY, maxX, f.maxY));
            }
        }

        public float minX = float.MaxValue;
        public float minY = float.MaxValue;
        public float maxX = float.MinValue;
        public float maxY = float.MinValue;
        public int id = 0;
        public bool isMerged = false;
        public bool isIntersectedWithViewport = false;
    }
}