﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //打包算法,适用于tile贴图大小不同的条件
    public class MaxRects : TexturePackStrategy
    {
        class SortInput : IComparer<TexturePackerItem>
        {
            public int Compare(TexturePackerItem a, TexturePackerItem b)
            {
                Texture2D ta = a.texture;
                Texture2D tb = b.texture;
                var areaA = ta.width * ta.height;
                var areaB = tb.width * tb.height;
                return areaB - areaA;
            }
        };

        public void OnDestroy()
        {

        }

        public int GetMaxPackCountInOneTexture(int textureSize, int borderSize, int padding, int maxTextureSize)
        {
            Debug.Assert(false, "not supported!");
            return 0;
        }

        public bool Pack(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, bool deleteTextureAtlas, out TexturePackResult output)
        {
            Debug.Assert(sortInput == false, "todo");

            output = new TexturePackResult();
            Clear(deleteTextureAtlas);
            Debug.Assert(setting.atlasTextureMaxWidth > 0 && setting.atlasTextureMaxHeight > 0);
            Debug.Assert(Mathf.IsPowerOfTwo(setting.atlasTextureMaxWidth) && Mathf.IsPowerOfTwo(setting.atlasTextureMaxHeight));

            int totalWidth = 0;
            int totalHeight = 0;
            for (int i = 0; i < images.Count; ++i)
            {
                var image = images[i];
                totalWidth += image.texture.width;
                totalHeight += image.texture.height;
                Debug.Assert(image.texture.format == TextureFormat.RGBA32);
            }

            if (sortInput)
            {
                images.Sort(new SortInput());
            }

            for (int i = 0; i < images.Count; ++i)
            {
                PackTextureToAtlas(images[i].uniqueID, images[i].texture, setting, out output.missingTextures);
            }

            for (int i = 0; i < mTextureAtlas.Count; ++i)
            {
                mTextureAtlas[i].OptimizeTextureRegion(setting);
            }

            output.atlas = new List<TextureAtlas>();
            output.atlas.AddRange(mTextureAtlas);

            return output.missingTextures.Count == 0;
        }

        public bool CanPackInOneTexture(TexturePackerSetting setting, List<TexturePackerItem> images, bool sortInput, out TexturePackResult output)
        {
            bool success = Pack(setting, images, sortInput, true, out output);
            if (!success)
            {
                return false;
            }
            if (output.atlas.Count != 1)
            {
                return false;
            }

            return true;
        }

        void Clear(bool deleteTextureAtlas)
        {
            if (deleteTextureAtlas)
            {
                for (int i = 0; i < mTextureAtlas.Count; ++i)
                {
                    mTextureAtlas[i].OnDestroy();
                }
            }
            mTextureAtlas.Clear();
            mAlreadyPackedTextures.Clear();
        }

        bool FindEmptyBin(int imageWidth, int imageHeight, TexturePackerSetting setting, out int textureAtlasIndex, out int emptyBinIndex, out bool rotatedImage)
        {
            textureAtlasIndex = -1;
            emptyBinIndex = -1;

            rotatedImage = false;
            for (int i = 0; i < mTextureAtlas.Count; ++i)
            {
                var binIndex = mTextureAtlas[i].FindEmptyBin(imageWidth, imageHeight, setting.borderSize, setting.padding, setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight);
                if (binIndex == -2)
                {
                    //can't pack this texture!
                    return false;
                }
                if (binIndex == -1)
                {
                    if (setting.enableRotate)
                    {
                        //try a rotated image
                        binIndex = mTextureAtlas[i].FindEmptyBin(imageHeight, imageWidth, setting.borderSize, setting.padding, setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight);
                        rotatedImage = true;
                    }
                }

                if (binIndex >= 0)
                {
                    textureAtlasIndex = i;
                    emptyBinIndex = binIndex;
                    break;
                }

                //只使用一张texture atlas
                if (setting.onlyPackOneTexture)
                {
                    break;
                }
            }

            if (textureAtlasIndex < 0)
            {
                if (setting.onlyPackOneTexture == false || mTextureAtlas.Count == 0)
                {
                    //no texture atlas found, create a new one
                    mTextureAtlas.Add(new TextureAtlasMaxRects(setting.atlasTextureMaxWidth, setting.atlasTextureMaxHeight, setting.backgroundColor));
                    textureAtlasIndex = mTextureAtlas.Count - 1;
                    emptyBinIndex = 0;
                }
                else
                {
                    //can't pack this texture
                    emptyBinIndex = -1;
                    textureAtlasIndex = -1;
                    return false;
                }
            }

            Debug.Assert(emptyBinIndex >= 0);
            return true;
        }

        void PackTextureToAtlas(string textureUniqueID, Texture2D input, TexturePackerSetting setting, out List<string> missingTextures)
        {
            missingTextures = new List<string>();
            Debug.Assert(!string.IsNullOrEmpty(textureUniqueID));
            if (!mAlreadyPackedTextures.Contains(textureUniqueID))
            {
                mAlreadyPackedTextures.Add(textureUniqueID);
                var image = input;
                int w = image.width;
                int h = image.height;
                if (w <= setting.atlasTextureMaxWidth && h <= setting.atlasTextureMaxHeight)
                {
                    bool rotatedImage = false;
                    int textureAtlasIndex;
                    int emptyBinIndex;
                    bool validTextureSize = FindEmptyBin(w, h, setting, out textureAtlasIndex, out emptyBinIndex, out rotatedImage);
                    if (!validTextureSize)
                    {
                        missingTextures.Add(textureUniqueID);
                    }
                    else
                    {
                        mTextureAtlas[textureAtlasIndex].AddImage(input.GetInstanceID(), textureUniqueID, image, emptyBinIndex, setting, rotatedImage);
                    }
                }
                else
                {
                    missingTextures.Add(textureUniqueID);
                }
            }
        }

        List<TextureAtlasMaxRects> mTextureAtlas = new List<TextureAtlasMaxRects>();
        HashSet<string> mAlreadyPackedTextures = new HashSet<string>();
    };
}


#endif