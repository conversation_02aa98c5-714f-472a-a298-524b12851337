﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class WallCollider
    {
        public Vector2 worldSpaceSize;
        public Collider wallCollider;
        public Quaternion worldToLocalRotation;
    }

    //城墙的碰撞体
    public class CityWall : MonoBehaviour, ICityWall
    {
        public void Init(System.Action<bool> onVisible)
        {
            mOnVisible += onVisible;

            Debug.Assert(mRenderers.Count == 0);
            var renderers = gameObject.GetComponentsInChildren<Renderer>();
            mRenderers.Clear();
            for (int i = 0; i < renderers.Length; ++i)
            {
                if (renderers[i].CompareTag("wall_ground") == false)
                {
                    mRenderers.Add(renderers[i]);
                }
            }

            mColliders.Clear();
            var boxColliders = gameObject.GetComponentsInChildren<Collider>();
            for (int i = 0; i < boxColliders.Length; ++i)
            {
                var collider = boxColliders[i] as BoxCollider;
                if (collider == null)
                {
                    continue;
                    //Debug.Assert(false, "invalid collider");
                }
                else
                {
                    if (collider.CompareTag(MapCoreDef.WALL_COLLIDER_TAG))
                    {
                        var wallCollider = new WallCollider();
                        var center = collider.center;
                        var size = collider.size;
                        var lossyScale = collider.gameObject.transform.lossyScale;
                        wallCollider.wallCollider = collider;
                        wallCollider.worldSpaceSize = new Vector2(size.x * lossyScale.x, size.z * lossyScale.z);
                        wallCollider.worldToLocalRotation = Quaternion.Inverse(collider.gameObject.transform.rotation);
                        mColliders.Add(wallCollider);
                    }
                }
            }
        }

        public void Uninit()
        {
            mColliders.Clear();
            mRenderers.Clear();
            mOnVisible = null;
        }

        public bool IsCollideWithCircle(Vector3 worldSpaceCenter, float worldSpaceRadius)
        {
            if (worldSpaceRadius == 0)
            {
                //半径为0表示不参与碰撞
                return false;
            }

            for (int i = 0; i < mColliders.Count; ++i)
            {
                var collider = mColliders[i];
                var worldSpaceDelta = worldSpaceCenter - collider.wallCollider.transform.position;
                var localCenter = collider.worldToLocalRotation * worldSpaceDelta;

                var size = collider.worldSpaceSize * 0.5f;
                bool hit = Utils.IsRectCircleIntersected(-size.x, -size.y, size.x, size.y, localCenter.x, localCenter.z, worldSpaceRadius);
                if (hit)
                {
                    return true;
                }
            }

            return false;
        }

        public void SetVisible(bool visible)
        {
            mIsVisible = visible;
            for (int i = 0; i < mRenderers.Count; ++i)
            {
                if (mRenderers[i] != null)
                {
                    mRenderers[i].enabled = visible;
                }
            }

            mOnVisible?.Invoke(mIsVisible);
        }

        public void UpdateCity() { }

        public void ClearPotentialCollidableList()
        {
            mPotentialCollidableBuildings.Clear();
        }

        public void AddPotentialCollidableBuilding(IBuildingElement building)
        {
            mPotentialCollidableBuildings.Add(building);
        }

        public List<IBuildingElement> GetPotentialCollidableBuildingList()
        {
            return mPotentialCollidableBuildings;
        }

        public bool isVisible { get { return mIsVisible; } }
        public int GetPriority()
        {
            return hidePriority;
        }

        public void AddCollision(IBuildingElement building)
        {
        }

        public List<IBuildingElement> GetCollisionList()
        {
            return null;
        }

        public bool isMainBuilding { get { return false; } }
        public bool isAtFinalPosition { get { return true; } }
        public int id { get { return gameObject.GetInstanceID(); } }
        public float upperHeight { get { return 0; } }
        public float lowerHeight { get { return 0; } }

        //在建筑物重叠时hidePriority小的隐藏hidePriority大的建筑
        public int hidePriority;
        List<WallCollider> mColliders = new List<WallCollider>();
        List<Renderer> mRenderers = new List<Renderer>();
        bool mIsVisible = true;
        // 城墙显隐时的回调
        event System.Action<bool> mOnVisible;
        List<IBuildingElement> mPotentialCollidableBuildings = new List<IBuildingElement>();
    }
}
