﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class MapCameraCollider
    {
        public MapCameraCollider(Vector3[] vertices, int[] indices, Transform parent)
        {
            mMesh = new Mesh();
            mMesh.vertices = vertices;
            mMesh.triangles = indices;
            mMesh.UploadMeshData(false);

            mGameObject = new GameObject("Map Camera Collider");
            mGameObject.layer = LayerMask.NameToLayer(MapCoreDef.CAMERA_COLLIDER_LAYER);
            mGameObject.transform.SetParent(parent);
            var collider = mGameObject.AddComponent<MeshCollider>();
            collider.sharedMesh = mMesh;

#if false
            //temp code
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            filter.sharedMesh = mMesh;
            renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            renderer.sharedMaterial.SetColor("_Color", Color.white);
#endif
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mMesh);
            mMesh = null;

            Utils.DestroyObject(mGameObject);
            mGameObject = null;
        }

        public void SetActive(bool active)
        {
            mGameObject.SetActive(active);
        }

        GameObject mGameObject;
        Mesh mMesh;
    }
}
