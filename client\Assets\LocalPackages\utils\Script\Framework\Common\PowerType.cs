﻿namespace SharedDataStructure
{
    public enum PowerType
    {
        #region 服务器发过来的战力，关联 ShowPower 类
        /// <summary>
        /// 兵基础战力
        /// </summary>
        SoliderBase = 1,

        /// <summary>
        /// 士兵进化战力
        /// </summary>
        SoliderEvo = 2,

        /// <summary>
        /// 当前部队英雄基础战力
        /// </summary>
        HeroBase = 3,

        /// <summary>
        /// 当前部队英雄星级战力
        /// </summary>
        HeroStar = 4,

        /// <summary>
        /// 部队技能战力
        /// </summary>
        HeroSkill = 5,

        /// <summary>
        /// 龙符文战力（旧版科技战力）
        /// </summary>
        DragonRune = 6,

        /// <summary>
        /// 皮肤战力
        /// </summary>
        Skin = 7,

        /// <summary>
        /// 王座战附加战力
        /// </summary>
        KingWar = 8,

        /// <summary>
        /// 联盟建筑附加战力
        /// </summary>
        AllianceBuilding = 9,

        /// <summary>
        /// vip战力
        /// </summary>
        Vip = 10,

        /// <summary>
        /// 羁绊战力
        /// </summary>
        Fetter = 11,

        /// <summary>
        /// 科技战力
        /// </summary>
        Tech = 12,

        /// <summary>
        /// 士兵天赋
        /// </summary>
        SoldierTalent = 13,

        /// <summary>
        /// 龙装备提升战力
        /// </summary>
        DragonEquipment = 14,

        /// <summary>
        /// 龙总战力
        /// </summary>
        DragonTotal = 15,

        /// <summary>
        /// 部队总战力
        /// </summary>
        Total = 16,
        #endregion

        #region 客户端算的战力
        Soldier = 17,
        Hero = 18,
        #endregion
        
        /// <summary>
        /// 女巫总战力
        /// </summary>
        WitchMagic = 19,
        /// <summary>
        /// 女巫基础战力
        /// </summary>
        WitchBase = 20,
        /// <summary>
        /// 女巫魔法石战力
        /// </summary>
        WitchStone = 21,
        /// <summary>
        /// 英雄装备总战力
        /// </summary>
        HeroTotalEquip = 22,
        /// <summary>
        /// 英雄装备
        /// </summary>
        HeroEquip = 23,
        /// <summary>
        /// 装备熔炼炉
        /// </summary>
        HeroEquipBuild = 24,
        /// <summary>
        /// 召唤兽
        /// </summary>
        SummonAltar = 25,
        /// <summary>
        /// 图鉴战力
        /// </summary>
        Atlas = 26,

        /// <summary>
        /// Buff战力
        /// </summary>
        Buff = 27,

        /// <summary>
        /// 联盟科技战力
        /// </summary>
        AllianceTech = 28,

        /// <summary>
        /// 联盟占领圣坛/龙巢战力
        /// </summary>
        AllianceNest = 29,

        /// <summary>
        /// 学院科技战力
        /// </summary>
        CollegeScience = 30,

        /// <summary>
        /// 神器战力
        /// </summary>
        Artifact = 31,

        /// <summary>
        /// 新Vip战力
        /// </summary>
        VipNew = 32,

        /// <summary>
        /// 新城堡皮肤/场景皮肤战力
        /// </summary>
        SkinNew = 33,

        /// <summary>
        /// 头像框战力
        /// </summary>
        Frame = 34,

        /// <summary>
        /// 破碎大陆占领Buff战力
        /// </summary>
        BrokenMainland = 35,

        /// <summary>
        /// 道具Buff战力
        /// </summary>
        ItemBuff = 36,

        /// <summary>
        /// kvk科技Buff战力
        /// </summary>
        KVKTechBuff = 37,

        /// <summary>
        /// 远征前夕Buff战力
        /// </summary>
        ExpeditionEveBuff = 38,

        /// <summary>
        /// 英雄宝物战力
        /// </summary>
        HeroTreasure = 39,
        
        /// <summary>
        /// 龙皮肤战力
        /// </summary>
        DragonSkin = 40,


        /// <summary>
        /// 徽章战力
        /// </summary>
        KnightBadge = 41,


        /// <summary>
        /// 英勇徽章
        /// </summary>
        KnightBraveBadge = 42,

        /// <summary>
        /// 神器祝福战力
        /// </summary>
        ArtifactBlessingPower = 43,

        /// <summary>
        /// kvk世界树战力
        /// </summary>
        KvkDonate = 44
    }
}