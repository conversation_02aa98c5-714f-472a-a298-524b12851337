﻿ 



 
 


#if UNITY_EDITOR

//created by wzw at 2019/12/23

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //烘培骨骼动画到贴图
    //skin mesh renderers重复使用的骨骼必须要有相同的bindpose!!!
    //将一个skin mesh的所有动画烘培到一张贴图上,并创建不使用animator和skinnedMeshRenderer的skin mesh
    //skinned mesh renderer变为mesh renderer
    //骨骼自定义挂接点下的mesh renderer
    public partial class SkeletonAnimationBaker : AnimationBaker
    {
        public class BakeSkeletonOption : BakeOption
        {
            public float sampleFPS;
            public bool useRGBA32Texture;
            public Shader defaultRigidAnimShader;
            public Shader defaultSkinAnimShader;
            public bool useSeparateCPUDrivenBoneTransformData;
        }

        void Clear()
        {
            mAnimationData = new List<AnimationData>();
            mSkinMeshies = new Dictionary<int, Mesh>();
            mRigidMeshies = new Dictionary<int, Mesh>();
            mSkinRendererMaterials = new Dictionary<int, Material>();
            mRigidRendererMaterials = new Dictionary<int, Material>();
        }

        //prefab是带有animator的root game object
        //将prefab中的Animator删除,将骨骼节点删除,将SkinnedMeshRenderer替换为MeshRenderer+MeshFilter,其他gameobject保持不变
        //generatePrefab:是否生成prefab文件
        //如果shader为null就使用bakeShader
        //useSRPBatcher:如果为true,会给每个动画模型使用独立的材质,注意要修改shader代码
        public override GameObject Bake(BakeOption option)
        {
            BakeSkeletonOption skeletonOption = option as BakeSkeletonOption;
            ErrorMsg = "";
            string outputFolder = skeletonOption.outputFolder;
#if UNITY_EDITOR
            var prefab = skeletonOption.prefab;
            Debug.Log($"Baking {prefab.name}");
#endif
            Clear();
            Debug.Assert(mShaderDrivenCustomBones != null && mRenderers != null);

            string originalPrefabGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(prefab));

            //process animation transitions
            List<ParameterInfo> parameters;
            string animationControllerGuid;
            var stateInfos = GetAnimationStateInfo(prefab, out parameters, out animationControllerGuid);

            if (skeletonOption.clearOutputFolder)
            {
                Utils.ClearFolderContent(outputFolder);
            }
            if (string.IsNullOrEmpty(outputFolder))
            {
                outputFolder = Utils.GetFolderPath(AssetDatabase.GetAssetPath(prefab));
            }

            var newPrefab = GameObject.Instantiate<GameObject>(prefab);
            CreateIDMap(newPrefab, prefab, mNewGameObjectToOldGameObjectInstanceIDMap);
            newPrefab.name = prefab.name;
            string rootBoneName = mBones.GetRootBoneName();
            //删除新prefab中不要的数据
            FixPrefab(newPrefab, rootBoneName);

            for (int i = 0; i < mRenderers.Count; ++i)
            {
                //创建一些不带有skin信息的mesh,将skinmesh的skin信息存储到其他通道中
                Mesh mesh = null;
                if (mRenderers[i].GetType() == typeof(SkinnedMeshRenderer))
                {
                    var r = mRenderers[i] as SkinnedMeshRenderer;
                    mesh = CreateOrGetSkinMeshAsset(r.sharedMesh, r.bones, outputFolder);
                    GameObject childObjInNewPrefab = Utils.FindGameObject(newPrefab, mRenderers[i].name);
                    Debug.Assert(childObjInNewPrefab.GetComponent<SkinnedMeshRenderer>() == null);
                    //给新的game object添加mesh renderer和mesh filter
                    var renderer = childObjInNewPrefab.GetComponent<MeshRenderer>();
                    var shaderType = SetMaterials(renderer, mRenderers[i].sharedMaterials, outputFolder, skeletonOption.defaultRigidAnimShader, skeletonOption.defaultSkinAnimShader);
                    var meshFilter = childObjInNewPrefab.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = mesh;
                    //增加一个标记标识使用哪个shader
                    var rendererTag = childObjInNewPrefab.AddComponent<RendererTag>();
                    rendererTag.shaderType = shaderType;
                    childObjInNewPrefab.transform.localPosition = Vector3.zero;
                    childObjInNewPrefab.transform.localScale = Vector3.one;
                    childObjInNewPrefab.transform.localRotation = Quaternion.identity;

                    mBones.AddBindPose(r.bones, r.sharedMesh.bindposes, false);
                }
                else
                {
                    var oldRenderer = mRenderers[i] as MeshRenderer;
                    var oldFilter = oldRenderer.GetComponent<MeshFilter>();
                    Debug.Assert(oldFilter != null && oldFilter.sharedMesh.isReadable, "Mesh must be readable!");
                    var idx = GetBoneIndex(oldRenderer.transform, mShaderDrivenCustomBones);
                    Debug.Assert(idx >= 0, "invalid custom bone!");
                    if (idx >= 0)
                    {
                        mesh = CreateOrGetMeshAsset(oldFilter.sharedMesh, idx, outputFolder);

                        GameObject childObjInNewPrefab = Utils.FindGameObject(newPrefab, mRenderers[i].name);
                        var newRenderer = childObjInNewPrefab.GetComponent<MeshRenderer>();
                        var newFilter = childObjInNewPrefab.GetComponent<MeshFilter>();
                        var shaderType = SetMaterials(newRenderer, mRenderers[i].sharedMaterials, outputFolder, skeletonOption.defaultRigidAnimShader, skeletonOption.defaultSkinAnimShader);
                        newFilter.sharedMesh = mesh;
                        //增加一个标记标识使用哪个shader
                        var rendererTag = childObjInNewPrefab.AddComponent<RendererTag>();
                        rendererTag.shaderType = shaderType;
                    }
                }
            }

            for (int i = 0; i < stateInfos.Count; ++i)
            {
                var skinRenderers = GetSkinnedMeshRenderers(mRenderers);
                SampleAnimation(skinRenderers, prefab.transform, stateInfos[i], skeletonOption.sampleFPS);
            }

            FixLODGroup(newPrefab);
            //reparent skinned mesh renderers
            var rendererTags = newPrefab.GetComponentsInChildren<RendererTag>(true);
            for (int i = 0; i < rendererTags.Length; ++i)
            {
                if (rendererTags[i].shaderType == RendererShaderType.SkinShader)
                {
                    rendererTags[i].gameObject.transform.SetParent(newPrefab.transform, false);
                }
            }

            if (skeletonOption.useRGBA32Texture)
            {
                CreateMaterialAssets(newPrefab, outputFolder, stateInfos, parameters, skeletonOption.blendType, skeletonOption.useAnimationBlending, skeletonOption.useSRPBatcher, animationControllerGuid, originalPrefabGuid, skeletonOption.useSeparateCPUDrivenBoneTransformData);
            }
            else
            {
                CreateMaterialAssetsForRGBAHalfTexture(newPrefab, outputFolder, stateInfos, parameters, skeletonOption.blendType, skeletonOption.useAnimationBlending, skeletonOption.useSRPBatcher, animationControllerGuid, originalPrefabGuid, skeletonOption.useSeparateCPUDrivenBoneTransformData);
            }

            //注意,需要把newPrefab的transform重置!因为transform信息都烘培到贴图里了!
            newPrefab.transform.position = Vector3.zero;
            newPrefab.transform.rotation = Quaternion.identity;
            newPrefab.transform.localScale = Vector3.one;

            if (skeletonOption.deleteBipBones)
            {
                DeleteBipGameObjects(newPrefab);
            }

            if (skeletonOption.generatePrefab)
            {
                SavePrefab(newPrefab, prefab, outputFolder);
                Object.DestroyImmediate(newPrefab);
                return null;
            }

            return newPrefab;
        }

        List<SkinnedMeshRenderer> GetSkinnedMeshRenderers(List<Renderer> renderers)
        {
            List<SkinnedMeshRenderer> sr = new List<SkinnedMeshRenderer>();
            for (int i = 0; i < renderers.Count; ++i)
            {
                if (renderers[i].GetType() == typeof(SkinnedMeshRenderer))
                {
                    sr.Add(renderers[i] as SkinnedMeshRenderer);
                }
            }
            return sr;
        }

        //删除null的custom bone
        Transform[] RemoveInvalidBones(Transform[] bones)
        {
            List<Transform> validBones = new List<Transform>();
            for (int i = 0; i < bones.Length; ++i)
            {
                if (bones[i] != null)
                {
                    validBones.Add(bones[i]);
                }
            }
            return validBones.ToArray();
        }

        //清理新的prefab不需要的数据
        void FixPrefab(GameObject newPrefab, string rootBoneName)
        {
            mLODGroupManager = new LODGroupManager(newPrefab);

            //删除所有skinned mesh renderer
            var renderers = newPrefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            for (int i = 0; i < renderers.Length; ++i)
            {
                var obj = renderers[i].gameObject;
                var shadowCastingMode = renderers[i].shadowCastingMode;
                var receiveShadow = renderers[i].receiveShadows;
                var lightProbeUsage = renderers[i].lightProbeUsage;
                var reflectionProbeUsage = renderers[i].reflectionProbeUsage;
                var allowOcclusionWhenDynamic = renderers[i].allowOcclusionWhenDynamic;
                var motionVectorGenerationMode = renderers[i].motionVectorGenerationMode;
                Object.DestroyImmediate(renderers[i]);
                var newRenderer = obj.AddComponent<MeshRenderer>();
                newRenderer.shadowCastingMode = shadowCastingMode;
                newRenderer.receiveShadows = receiveShadow;
                newRenderer.lightProbeUsage = lightProbeUsage;
                newRenderer.reflectionProbeUsage = reflectionProbeUsage;
                newRenderer.allowOcclusionWhenDynamic = allowOcclusionWhenDynamic;
                newRenderer.motionVectorGenerationMode = motionVectorGenerationMode;
            }

            //删除根的animator
            var animators = newPrefab.GetComponentsInChildren<Animator>(true);
            DeleteAnimatorOfHiestHierarchy(animators);

            //将customBones移到根节点
            for (int i = 0; i < mShaderDrivenCustomBones.Count; ++i)
            {
                var customBoneObjInNewPrefab = Utils.FindGameObject(newPrefab, mShaderDrivenCustomBones[i].name);
                if (customBoneObjInNewPrefab != null)
                {
                    customBoneObjInNewPrefab.transform.SetParent(newPrefab.transform);
                    //因为会使用烘培后的动画数据驱动,这里transform要reset
                    customBoneObjInNewPrefab.transform.localPosition = Vector3.zero;
                    customBoneObjInNewPrefab.transform.localRotation = Quaternion.identity;
                    customBoneObjInNewPrefab.transform.localScale = Vector3.one;
                }
            }

            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                var customBoneObjInNewPrefab = Utils.FindGameObject(newPrefab, mCPUDrivenCustomBones[i].name);
                if (customBoneObjInNewPrefab != null)
                {
                    customBoneObjInNewPrefab.transform.SetParent(newPrefab.transform);
                    //因为会使用烘培后的动画数据驱动,这里transform要reset
                    customBoneObjInNewPrefab.transform.localPosition = Vector3.zero;
                    customBoneObjInNewPrefab.transform.localRotation = Quaternion.identity;
                    customBoneObjInNewPrefab.transform.localScale = Vector3.one;
                }
            }

            //删除skinned mesh renderer使用的骨骼,注意可能会有其他骨骼不会被删除,需要手动删除
            GameObject rootBone = Utils.FindGameObject(newPrefab, rootBoneName);
            DeleteBoneGameObject(rootBone);
        }

        void DeleteAnimatorOfHiestHierarchy(Animator[] animators)
        {
            if (animators.Length > 0)
            {
                GameObject.DestroyImmediate(animators[0]);
            }
        }

        void DeleteBoneGameObject(GameObject obj)
        {
            var bones = mBones.bones;
            for (int i = 0; i < bones.Count; ++i)
            {
                if (obj != null)
                {
                    var boneObj = Utils.FindGameObject(obj, bones[i].transform.name);
                    if (boneObj != null)
                    {
                        GameObject.DestroyImmediate(boneObj);
                    }
                }
            }
        }

        void FixLODGroup(GameObject newPrefab)
        {
            bool lodError = false;
            Debug.Log($"FixLODGroup: {newPrefab.name}");
            int n = mLODGroupManager.lodGroups.Length;
            if (n > 0)
            {
                var lodGroups = newPrefab.GetComponentsInChildren<LODGroup>(true);
                for (int i = 0; i < n; ++i)
                {
                    var oldLODs = mLODGroupManager.lodGroups[i].lods;
                    var newLODs = new LOD[oldLODs.Length];
                    for (int k = 0; k < oldLODs.Length; ++k)
                    {
                        var oldRenderers = oldLODs[k].renderers;
                        var newRenderers = new Renderer[oldRenderers.Length];
                        for (int j = 0; j < oldRenderers.Length; ++j)
                        {
                            var obj = Utils.FindGameObject(newPrefab, oldRenderers[j]);
                            if (obj != null)
                            {
                                newRenderers[j] = obj.GetComponent<Renderer>();
                            }
                            else
                            {
                                lodError = true;
                            }
                        }
                        newLODs[k] = new LOD(oldLODs[k].transitionHeight, newRenderers);
                    }
                    lodGroups[i].SetLODs(newLODs);
                }
            }

            if (lodError)
            {
                string msg = $"{newPrefab.name} has lod group error!";
                ErrorMsg += msg;
                Debug.LogError(msg);
            }
        }

        RendererShaderType SetMaterials(MeshRenderer renderer, Material[] materials, string outputFolder, Shader defaultRigidAnimShader, Shader defaultSkinAnimShader)
        {
            RendererShaderType type;
            //判断这个renderer是否是属于挂在自定义骨骼下的
            bool isDescendant = Utils.IsDescendant(renderer.transform, mShaderDrivenCustomBones);
            if (isDescendant)
            {
                int nMtls = materials.Length;
                Material[] newMtls = new Material[nMtls];
                for (int i = 0; i < nMtls; ++i)
                {
                    Material newMtl;
                    mRigidRendererMaterials.TryGetValue(materials[i].GetInstanceID(), out newMtl);

                    if (newMtl == null)
                    {
                        var shader = mMaterialManager.GetTargetShader(materials[i]);
                        if (shader == null)
                        {
                            shader = defaultRigidAnimShader;
                        }
                        newMtl = new Material(shader);
                        newMtl.name = materials[i].name;
                        CopyProperties(newMtl, materials[i]);
                        mRigidRendererMaterials.Add(materials[i].GetInstanceID(), newMtl);
                    }
                    newMtls[i] = newMtl;
                }
                renderer.sharedMaterials = newMtls;
                type = RendererShaderType.RigidShader;
            }
            else
            {
                int nMtls = materials.Length;
                Material[] newMtls = new Material[nMtls];
                for (int i = 0; i < nMtls; ++i)
                {
                    Material newMtl;
                    mSkinRendererMaterials.TryGetValue(materials[i].GetInstanceID(), out newMtl);

                    if (newMtl == null)
                    {
                        var shader = mMaterialManager.GetTargetShader(materials[i]);
                        if (shader == null)
                        {
                            shader = defaultSkinAnimShader;
                        }
                        newMtl = new Material(shader);
                        newMtl.name = materials[i].name;
                        CopyProperties(newMtl, materials[i]);
                        mSkinRendererMaterials.Add(materials[i].GetInstanceID(), newMtl);
                    }
                    newMtls[i] = newMtl;
                }
                renderer.sharedMaterials = newMtls;
                type = RendererShaderType.SkinShader;
            }
            return type;
        }

        void CopyProperties(Material tgt, Material src)
        {
            var srcShader = src.shader;
            int srcShaderPropertyCount = ShaderUtil.GetPropertyCount(srcShader);
            for (int i = 0; i < srcShaderPropertyCount; ++i)
            {
                var srcPropName = ShaderUtil.GetPropertyName(srcShader, i);
                if (tgt.HasProperty(srcPropName))
                {
                    var srcPropType = ShaderUtil.GetPropertyType(srcShader, i);
                    switch (srcPropType)
                    {
                        case ShaderUtil.ShaderPropertyType.Color:
                            tgt.SetColor(srcPropName, src.GetColor(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.Float:
                        case ShaderUtil.ShaderPropertyType.Range:
                            tgt.SetFloat(srcPropName, src.GetFloat(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.TexEnv:
                            tgt.SetTexture(srcPropName, src.GetTexture(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.Vector:
                            tgt.SetVector(srcPropName, src.GetVector(srcPropName));
                            break;
                    }
                }
            }
        }

        void EncodeFloatRG(float v, ref float r, ref float g)
        {
            g = v * 255.0f % 1.0f;
            r = v - g / 255.0f;
        }

        int GetFloatExponentBits(float v)
        {
            int integralPart = (int)v;
            int bits = 0;

            while (integralPart > 0)
            {
                ++bits;
                integralPart >>= 1;
            }

            return bits;
        }

        void CompressMatrixColumn(ref Color col)
        {
            var maxComponent = Mathf.Max(Mathf.Abs(col.r), Mathf.Abs(col.g));
            maxComponent = Mathf.Max(Mathf.Abs(col.b), maxComponent);

            var sharedExponent = GetFloatExponentBits(maxComponent);
            for (int i = 0; i < 3; ++i)
            {
                col[i] /= Mathf.Pow(2, sharedExponent);
                col[i] = (col[i] + 1.0f) / 2.0f;
            }

            // encode shared exponent into alpha channel
            col[3] = sharedExponent / 255.0f;
        }

        void CompressRotation(ref Color col)
        {
            //rotation的值永远在-1到1之间
            int sharedExponent = 0;
            for (int i = 0; i < 4; ++i)
            {
                col[i] /= Mathf.Pow(2, sharedExponent);
                col[i] = (col[i] + 1.0f) / 2.0f;
            }
        }

        void EncodeMatrixesInRGBA32(Color[] matrixes, Color[] encodedMatrixes, AnimationBlendType blendType, int destIndex = 0)
        {
            // Matrixes were encoded in column major fasion, where each
            // column stored in a Color struct
            int nextRotationCounter = 4;
            for (int mi = 0; mi < matrixes.Length; mi++)
            {
                if (blendType == AnimationBlendType.Slerp && mi == nextRotationCounter)
                {
                    //rotation要使用特定的压缩算法
                    CompressRotation(ref matrixes[mi]);
                    nextRotationCounter += 6;
                }
                else
                {
                    CompressMatrixColumn(ref matrixes[mi]);
                }
                var outIndex = destIndex + mi * TRNASFORM_COLUMN_SIZE;

                EncodeFloatRG(matrixes[mi].r, ref encodedMatrixes[outIndex].r, ref encodedMatrixes[outIndex].g);
                EncodeFloatRG(matrixes[mi].g, ref encodedMatrixes[outIndex].b, ref encodedMatrixes[outIndex].a);
                EncodeFloatRG(matrixes[mi].b, ref encodedMatrixes[outIndex + 1].r, ref encodedMatrixes[outIndex + 1].g);
                EncodeFloatRG(matrixes[mi].a, ref encodedMatrixes[outIndex + 1].b, ref encodedMatrixes[outIndex + 1].a);
            }
        }

        void ResaveTextureToAsset(Texture2D texture, string assetPath)
        {
            var rawData = ImageConversion.EncodeToTGA(texture);
            System.IO.File.WriteAllBytes(assetPath, rawData);
            AssetDatabase.ImportAsset(assetPath);
        }

        Texture2D SaveAndReimportTextureUsingSpecificSettings(Texture2D texture, string assetPath)
        {
            ResaveTextureToAsset(texture, assetPath);
            Object.DestroyImmediate(texture);

            TextureImporter textureImporter = (TextureImporter)TextureImporter.GetAtPath(assetPath);
            textureImporter.filterMode = FilterMode.Point;
            textureImporter.mipmapEnabled = false;
            textureImporter.sRGBTexture = false;
            textureImporter.isReadable = false;
            textureImporter.wrapMode = TextureWrapMode.Clamp;

            var textureCompressionSettings = textureImporter.GetDefaultPlatformTextureSettings();
            textureCompressionSettings.overridden = true;
            textureCompressionSettings.format = TextureImporterFormat.RGBA32;
            textureImporter.SetPlatformTextureSettings(textureCompressionSettings);

            textureImporter.SaveAndReimport();

            return (Texture2D)AssetDatabase.LoadAssetAtPath(assetPath, typeof(Texture2D));
        }

        Texture2D EncodeAnimationsIntoTexture(Color[] skinnedAnimation, Color[] rigidBodyAnimation, string assetPath, AnimationBlendType blendType)
        {
            var totalLength = skinnedAnimation.Length + rigidBodyAnimation.Length;
            Vector2Int textureSize = Utils.CalculateSmallestPOTTextureSize(totalLength * TRNASFORM_COLUMN_SIZE);

            Color[] encodedMatrixes = new Color[textureSize.x * textureSize.y];
            EncodeMatrixesInRGBA32(skinnedAnimation, encodedMatrixes, blendType);
            EncodeMatrixesInRGBA32(rigidBodyAnimation, encodedMatrixes, blendType, skinnedAnimation.Length * TRNASFORM_COLUMN_SIZE);

            Texture2D animationTexture = new Texture2D(textureSize.x, textureSize.y, TextureFormat.RGBA32, false);
            animationTexture.SetPixels(encodedMatrixes);

            animationTexture = SaveAndReimportTextureUsingSpecificSettings(animationTexture, assetPath);
            return animationTexture;
        }

        //创建资源
        void CreateMaterialAssets(GameObject newPrefab, string outputFolder, List<StateInfo> stateInfos, List<ParameterInfo> parameters, AnimationBlendType blendType, bool useAnimationBlending, bool useSRPBatcher, string animationControllerGuid, string originalPrefabGuid, bool separateCPUDrivenBoneTransformData)
        {
            List<int> animationOffset;
            List<int> customBoneAnimationOffset;
            List<int> cpuDrivenCustomBoneAnimationOffset;

            var texturePath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_ANIM_TEXTURE_NAME}.tga");
            //骨骼的动画数据
            var skinnedAnimationTextureData = CreateAnimationTextureData(false, blendType, out animationOffset);
            //骨骼挂接点的动画数据,非skin mesh renderer模型不能使用skin shader来驱动动画
            var customAnimationTextureData = CreateAnimationTextureData(true, blendType, out customBoneAnimationOffset);
            var animationTexture = EncodeAnimationsIntoTexture(skinnedAnimationTextureData, customAnimationTextureData, texturePath, blendType);

            //cpu 驱动的骨骼动画
            string[] cpuDrivenCustomBoneNames;
            Vector3[] cpuDrivenCustomBoneTranslations;
            Vector3[] cpuDrivenCustomBoneScalings;
            Quaternion[] cpuDrivenCustomBoneRotations;
            CreateCPUDrivenCustomBonesAnimationData(out cpuDrivenCustomBoneAnimationOffset, out cpuDrivenCustomBoneNames, out cpuDrivenCustomBoneTranslations, out cpuDrivenCustomBoneRotations, out cpuDrivenCustomBoneScalings);

            //创建动画数据
            var customAnimator = newPrefab.GetComponent<AnimatorWrapper>();
            if (customAnimator == null)
            {
                customAnimator = newPrefab.AddComponent<AnimatorWrapper>();
            }
            customAnimator.InitInEditor(true, false, newPrefab);
            BakedAnimationData bakedAnimationData = ScriptableObject.CreateInstance<BakedAnimationData>();
            bakedAnimationData.animationControllerAssetGuid = animationControllerGuid;
            bakedAnimationData.originalPrefabGuid = originalPrefabGuid;
            bakedAnimationData.blendType = blendType;
            bakedAnimationData.useAnimationBlending = useAnimationBlending;
            bakedAnimationData.prefabName = newPrefab.name;

            if (separateCPUDrivenBoneTransformData)
            {
                var cpuDrivenBoneTransformData = ScriptableObject.CreateInstance<BakedCPUDrivenBoneTransformData>();
                cpuDrivenBoneTransformData.boneNames = cpuDrivenCustomBoneNames;
                cpuDrivenBoneTransformData.rotations = cpuDrivenCustomBoneRotations;
                cpuDrivenBoneTransformData.translations = cpuDrivenCustomBoneTranslations;
                cpuDrivenBoneTransformData.scalings = cpuDrivenCustomBoneScalings;

                string path = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_CPU_DRIVEN_BONE_TRANSFORM_DATA_NAME}.asset");
                AssetDatabase.CreateAsset(cpuDrivenBoneTransformData, path);

                bakedAnimationData.cpuDrivenBoneTransformData = cpuDrivenBoneTransformData;
            }
            else
            {
                bakedAnimationData.cpuDrivenCustomBoneTranslations = cpuDrivenCustomBoneTranslations;
                bakedAnimationData.cpuDrivenCustomBoneRotations = cpuDrivenCustomBoneRotations;
                bakedAnimationData.cpuDrivenCustomBoneScalings = cpuDrivenCustomBoneScalings;
                bakedAnimationData.cpuDrivenCustomBoneNames = cpuDrivenCustomBoneNames;
            }
            bakedAnimationData.animationInfo = CreateAnimationInfo(animationOffset, customBoneAnimationOffset, cpuDrivenCustomBoneAnimationOffset);
            bakedAnimationData.animationStateInfo = CreateAnimationStateInfo(stateInfos, bakedAnimationData.animationInfo);
            bakedAnimationData.parameterInfo = CreateParameterInfo(parameters);
            bakedAnimationData.useRGBA32Texture = true;
            bakedAnimationData.useSRPBatcher = useSRPBatcher;
            string bakedDataPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_ANIM_DATA_NAME}.asset");
            AssetDatabase.CreateAsset(bakedAnimationData, bakedDataPath);
            AssetDatabase.Refresh();
            customAnimator.SetAnimationData(bakedAnimationData, bakedDataPath);

            //保存材质
            foreach (var p in mSkinRendererMaterials)
            {
                var mtl = p.Value;
                mtl.SetFloat("_BoneCount", mBones.boneCount);
                mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, animationTexture);
                mtl.SetVector(MapCoreDef.BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME, new Vector4(animationTexture.width, animationTexture.height, skinnedAnimationTextureData.Length * TRNASFORM_COLUMN_SIZE, 0));
                mtl.enableInstancing = true;
                string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mtl.name}.mat");
                AssetDatabase.CreateAsset(mtl, mtlPath);
            }
            foreach (var p in mRigidRendererMaterials)
            {
                var mtl = p.Value;
                mtl.SetFloat("_BoneCount", (float)mShaderDrivenCustomBones.Count);
                mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, animationTexture);
                mtl.SetVector(MapCoreDef.BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME, new Vector4(animationTexture.width, animationTexture.height, skinnedAnimationTextureData.Length * TRNASFORM_COLUMN_SIZE, 0));
                mtl.enableInstancing = true;
                string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mtl.name}.mat");
                AssetDatabase.CreateAsset(mtl, mtlPath);
            }
        }

        //创建资源
        void CreateMaterialAssetsForRGBAHalfTexture(GameObject newPrefab, string outputFolder, List<StateInfo> stateInfos, List<ParameterInfo> parameters, AnimationBlendType blendType, bool useAnimationBlending, bool useSRPBatcher, string animationControllerGuid, string originalPrefabGuid, bool separateCPUDrivenBoneTransformData)
        {
            List<int> animationOffset;
            List<int> customBoneAnimationOffset;
            List<int> cpuDrivenCustomBoneAnimationOffset;

            var texturePath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_ANIM_TEXTURE_NAME}.asset");
            //骨骼的动画数据
            var skinnedAnimationTextureData = CreateDynamicAnimationTextureData(false, blendType, out animationOffset);
            //骨骼挂接点的动画数据,非skin mesh renderer模型不能使用skin shader来驱动动画
            var customAnimationData = CreateDynamicAnimationTextureData(true, blendType, out customBoneAnimationOffset);

            Texture2D animationTexture = CreateRGBAHalfTexture(texturePath, skinnedAnimationTextureData, customAnimationData);
            //cpu 驱动的骨骼动画
            string[] cpuDrivenCustomBoneNames;
            Vector3[] cpuDrivenCustomBoneTranslations;
            Vector3[] cpuDrivenCustomBoneScalings;
            Quaternion[] cpuDrivenCustomBoneRotations;
            CreateCPUDrivenCustomBonesAnimationData(out cpuDrivenCustomBoneAnimationOffset, out cpuDrivenCustomBoneNames, out cpuDrivenCustomBoneTranslations, out cpuDrivenCustomBoneRotations, out cpuDrivenCustomBoneScalings);

            //创建动画数据
            var customAnimator = newPrefab.GetComponent<AnimatorWrapper>();
            if (customAnimator == null)
            {
                customAnimator = newPrefab.AddComponent<AnimatorWrapper>();
            }
            customAnimator.InitInEditor(true, false, newPrefab);
            BakedAnimationData animTextureData = ScriptableObject.CreateInstance<BakedAnimationData>();
            animTextureData.animationControllerAssetGuid = animationControllerGuid;
            animTextureData.originalPrefabGuid = originalPrefabGuid;
            animTextureData.blendType = blendType;
            animTextureData.useAnimationBlending = useAnimationBlending;
            animTextureData.prefabName = newPrefab.name;

            if (separateCPUDrivenBoneTransformData)
            {
                var cpuDrivenBoneTransformData = ScriptableObject.CreateInstance<BakedCPUDrivenBoneTransformData>();
                cpuDrivenBoneTransformData.boneNames = cpuDrivenCustomBoneNames;
                cpuDrivenBoneTransformData.rotations = cpuDrivenCustomBoneRotations;
                cpuDrivenBoneTransformData.translations = cpuDrivenCustomBoneTranslations;
                cpuDrivenBoneTransformData.scalings = cpuDrivenCustomBoneScalings;

                string path = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_CPU_DRIVEN_BONE_TRANSFORM_DATA_NAME}.asset");
                AssetDatabase.CreateAsset(cpuDrivenBoneTransformData, path);

                animTextureData.cpuDrivenBoneTransformData = cpuDrivenBoneTransformData;
            }
            else
            {
                animTextureData.cpuDrivenCustomBoneTranslations = cpuDrivenCustomBoneTranslations;
                animTextureData.cpuDrivenCustomBoneRotations = cpuDrivenCustomBoneRotations;
                animTextureData.cpuDrivenCustomBoneScalings = cpuDrivenCustomBoneScalings;
                animTextureData.cpuDrivenCustomBoneNames = cpuDrivenCustomBoneNames;
            }

            animTextureData.animationInfo = CreateAnimationInfo(animationOffset, customBoneAnimationOffset, cpuDrivenCustomBoneAnimationOffset);
            animTextureData.animationStateInfo = CreateAnimationStateInfo(stateInfos, animTextureData.animationInfo);
            animTextureData.parameterInfo = CreateParameterInfo(parameters);
            animTextureData.useRGBA32Texture = false;
            animTextureData.useSRPBatcher = useSRPBatcher;
            string bakedDataPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_ANIM_DATA_NAME}.asset");
            AssetDatabase.CreateAsset(animTextureData, bakedDataPath);
            AssetDatabase.Refresh();
            customAnimator.SetAnimationData(animTextureData, bakedDataPath);

            //保存材质
            foreach (var p in mSkinRendererMaterials)
            {
                var mtl = p.Value;
                mtl.SetFloat("_BoneCount", mBones.boneCount);
                mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, animationTexture);
                mtl.SetVector(MapCoreDef.BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME, new Vector4(animationTexture.width, animationTexture.height, skinnedAnimationTextureData.Length, 0));
                mtl.enableInstancing = true;
                string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mtl.name}.mat");
                AssetDatabase.CreateAsset(mtl, mtlPath);
            }
            foreach (var p in mRigidRendererMaterials)
            {
                var mtl = p.Value;
                mtl.SetFloat("_BoneCount", (float)mShaderDrivenCustomBones.Count);
                mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, animationTexture);
                mtl.SetVector(MapCoreDef.BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME, new Vector4(animationTexture.width, animationTexture.height, skinnedAnimationTextureData.Length, 0));
                mtl.enableInstancing = true;
                string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mtl.name}.mat");
                AssetDatabase.CreateAsset(mtl, mtlPath);
            }
        }

        Texture2D CreateRGBAHalfTexture(string texturePath, Color[] skinnedAnimationTextureData, Color[] customAnimationData)
        {
            int totalLength = skinnedAnimationTextureData.Length + customAnimationData.Length;

            Vector2Int textureSize = Utils.CalculateSmallestPOTTextureSize(totalLength);
            var texture = new Texture2D(textureSize.x, textureSize.y, TextureFormat.RGBAHalf, false);

            Color[] textureData = new Color[textureSize.x * textureSize.y];
            //use array copy is much faster!
            System.Array.Copy(skinnedAnimationTextureData, textureData, skinnedAnimationTextureData.Length);
            System.Array.Copy(customAnimationData, 0, textureData, skinnedAnimationTextureData.Length, customAnimationData.Length);
            
            texture.SetPixels(textureData);
            texture.Apply(false, true);
            texture.filterMode = FilterMode.Point;

            AssetDatabase.CreateAsset(texture, texturePath);
            return texture;
        }
        //创建一个非skin的mesh
        Mesh CreateOrGetSkinMeshAsset(Mesh skinnedMesh, Transform[] skinRendererBones, string outputFolder)
        {
            Mesh mesh;
            mSkinMeshies.TryGetValue(skinnedMesh.GetInstanceID(), out mesh);
            if (mesh == null)
            {
                Debug.Assert(skinnedMesh.uv3.Length == 0);
                Debug.Assert(skinnedMesh.uv4.Length == 0);
                int n = skinnedMesh.vertexCount;
                var boneWeights = skinnedMesh.boneWeights;
                mesh = new Mesh();
                mesh.vertices = skinnedMesh.vertices;
                mesh.triangles = skinnedMesh.triangles;
                mesh.normals = skinnedMesh.normals;
                mesh.uv = skinnedMesh.uv;
                mesh.colors = skinnedMesh.colors;

                var newWeightData = new List<Vector4>(n);
                var boneIndices = new List<Vector4>(n);
                for (int i = 0; i < n; ++i)
                {
                    //转换成global bone index
                    int globalBoneIndex0 = mBones.GetBoneIndex(skinRendererBones[boneWeights[i].boneIndex0]);
                    int globalBoneIndex1 = mBones.GetBoneIndex(skinRendererBones[boneWeights[i].boneIndex1]);
                    int globalBoneIndex2 = mBones.GetBoneIndex(skinRendererBones[boneWeights[i].boneIndex2]);
                    int globalBoneIndex3 = mBones.GetBoneIndex(skinRendererBones[boneWeights[i].boneIndex3]);

                    newWeightData.Add(new Color(boneWeights[i].weight0, boneWeights[i].weight1, boneWeights[i].weight2, boneWeights[i].weight3));
                    boneIndices.Add(new Vector4(globalBoneIndex0, globalBoneIndex1, globalBoneIndex2, globalBoneIndex3));
                }
                mesh.SetUVs(2, boneIndices);
                mesh.SetUVs(3, newWeightData);

                string meshPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{skinnedMesh.name}.asset");
                meshPath = meshPath.Replace(":", "_");
                AssetDatabase.CreateAsset(mesh, meshPath);
                mSkinMeshies[skinnedMesh.GetInstanceID()] = mesh;
            }

            return mesh;
        }

        Mesh CreateOrGetMeshAsset(Mesh m, int customBoneIndex, string outputFolder)
        {
            Mesh newMesh;
            mRigidMeshies.TryGetValue(m.GetInstanceID(), out newMesh);
            if (newMesh == null)
            {
                int n = m.vertexCount;
                var boneWeights = m.boneWeights;
                newMesh = new Mesh();
                newMesh.vertices = m.vertices;
                newMesh.triangles = m.triangles;
                newMesh.normals = m.normals;
                newMesh.colors = m.colors;
                var originalUVs = m.uv;
                //设置新的uv,把骨骼index也传进去
                List<Vector4> newUV = new List<Vector4>();
                for (int i = 0; i < originalUVs.Length; ++i)
                {
                    newUV.Add(new Vector4(originalUVs[i].x, originalUVs[i].y, 0, customBoneIndex));
                }
                newMesh.SetUVs(0, newUV);

                string meshPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{m.name}.asset");
                AssetDatabase.CreateAsset(newMesh, meshPath);
                mRigidMeshies[m.GetInstanceID()] = newMesh;
            }

            return newMesh;
        }

        void SampleAnimation(List<SkinnedMeshRenderer> renderers, Transform rootGameObject, StateInfo state, float sampleFPS)
        {
            var clip = state.clip;

            for (int i = 0; i < mAnimationData.Count; ++i)
            {
                if (mAnimationData[i].clip.GetInstanceID() == state.clip.GetInstanceID())
                {
                    return;
                }
            }
            float frameMultiplier = sampleFPS / clip.frameRate;
            int totalFrame = Mathf.FloorToInt(clip.length * clip.frameRate * frameMultiplier);
            float timeStep = 0;
            if (totalFrame > 1)
            {
                timeStep = clip.length / (totalFrame - 1);
            }

            var frames = new AnimationFrames(totalFrame);

            //要从有animator的game object开始
            var animatorObj = rootGameObject.GetComponentInChildren<Animator>(true).gameObject;
            var transforms = animatorObj.GetComponentsInChildren<Transform>(true);

            var boneNames = GetCPUDrivenCustomBoneNames(transforms);
            frames.SetCPUDrivenCustomBoneNames(boneNames);

            Matrix4x4 rootTransform = animatorObj.transform.localToWorldMatrix;
            for (int i = 0; i < totalFrame; ++i)
            {
                //获取第i帧的transform信息
                clip.SampleAnimation(animatorObj, i * timeStep);

                //获取这帧自定义节点的信息
                Matrix4x4[] customBoneTransforms = GetCustomBoneTransformsAtFrame(transforms);
                frames.AddCustomBoneFrameTransforms(customBoneTransforms);

                //获取cpu驱动的骨骼信息
                Vector3[] cpuDrivenCustomBoneTranslations;
                Vector3[] cpuDrivenCustomBoneScalings;
                Quaternion[] cpuDrivenCustomBoneRotations;
                GetCPUDrivenCustomBoneTransformsAtFrame(transforms, out cpuDrivenCustomBoneTranslations, out cpuDrivenCustomBoneRotations, out cpuDrivenCustomBoneScalings);
                frames.AddCPUDriveCustomBoneFrameTransforms(cpuDrivenCustomBoneTranslations, cpuDrivenCustomBoneRotations, cpuDrivenCustomBoneScalings);

                //保存第i帧的骨骼动画信息
                Matrix4x4[] frameBoneData = new Matrix4x4[mBones.boneCount];
                //根据名称将transform赋值到bone中
                for (int k = 0; k < renderers.Count; ++k)
                {
                    Matrix4x4[] boneTransforms = GetBoneTransformsAtFrame(transforms, renderers[k]);
                    for (int b = 0; b < renderers[k].bones.Length; ++b)
                    {
                        var globalBoneIndex = mBones.GetBoneIndex(renderers[k].bones[b]);
                        frameBoneData[globalBoneIndex] = boneTransforms[b];
                    }
                }
                frames.AddBoneFrameTransforms(frameBoneData);
            }

            Debug.Assert(!string.IsNullOrEmpty(state.name));
            AnimationData data = new AnimationData(frames, state.name, clip.length, clip.frameRate, frameMultiplier, clip.isLooping, clip);
            mAnimationData.Add(data);
        }

        //把属于这个renderer的第n帧的所有骨骼动画信息筛选出来
        Matrix4x4[] GetCustomBoneTransformsAtFrame(Transform[] allTransforms)
        {
            var customBoneFrameTransforms = new Matrix4x4[mShaderDrivenCustomBones.Count];
            for (int i = 0; i < mShaderDrivenCustomBones.Count; ++i)
            {
                //看看这个transform是否是自定义的节点
                var customBoneIdx = GetBoneIndex(mShaderDrivenCustomBones[i], allTransforms);
                if (customBoneIdx >= 0)
                {
                    customBoneFrameTransforms[i] = allTransforms[customBoneIdx].localToWorldMatrix;
                }
            }
            return customBoneFrameTransforms;
        }

        string[] GetCPUDrivenCustomBoneNames(Transform[] allTransforms)
        {
            List<string> names = new List<string>();
            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                //看看这个transform是否是自定义的节点
                var customBoneIdx = GetBoneIndex(mCPUDrivenCustomBones[i], allTransforms);
                if (customBoneIdx >= 0)
                {
                    names.Add(allTransforms[customBoneIdx].name);
                }
            }
            return names.ToArray();
        }

        //把属于这个renderer的第n帧的所有骨骼动画信息筛选出来
        void GetCPUDrivenCustomBoneTransformsAtFrame(Transform[] allTransforms, out Vector3[] translations, out Quaternion[] rotations, out Vector3[] scalings)
        {
            translations = new Vector3[mCPUDrivenCustomBones.Count];
            rotations = new Quaternion[mCPUDrivenCustomBones.Count];
            scalings = new Vector3[mCPUDrivenCustomBones.Count];
            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                //看看这个transform是否是自定义的节点
                var customBoneIdx = GetBoneIndex(mCPUDrivenCustomBones[i], allTransforms);
                if (customBoneIdx >= 0)
                {
                    scalings[i] = allTransforms[customBoneIdx].lossyScale;
                    rotations[i] = allTransforms[customBoneIdx].rotation;
                    translations[i] = allTransforms[customBoneIdx].position;
                }
            }
        }

        //把属于这个renderer的第n帧的所有骨骼动画信息筛选出来
        Matrix4x4[] GetBoneTransformsAtFrame(Transform[] allTransforms, SkinnedMeshRenderer renderer)
        {
            var bones = renderer.bones;
            Matrix4x4[] frameBoneTransforms = new Matrix4x4[bones.Length];
            for (int i = 0; i < allTransforms.Length; ++i)
            {
                var idx = GetBoneIndex(allTransforms[i], bones);
                if (idx >= 0)
                {
                    frameBoneTransforms[idx] = allTransforms[i].localToWorldMatrix;
                }
            }
            return frameBoneTransforms;
        }

        int GetBoneIndex(Transform t, IEnumerable<Transform> bones)
        {
            int idx = 0;
            foreach (var b in bones)
            {
                //todo 是否需要用name来判断?
                if (b.name == t.name)
                {
                    return idx;
                }
                ++idx;
            }
            return -1;
        }

        //customBones:创建骨骼挂接点的动画数据
        //animationOffset: 返回每个动画数据在贴图中的起点偏移
        //返回一张包含所有动画骨骼transform数据的Color数组
        public Color[] CreateAnimationTextureData(bool customBones, AnimationBlendType blendType, out List<int> animationOffset)
        {
            animationOffset = new List<int>();
            int maxTextureSize = 2048;
            int idx = 0;
            List<Color> transformData = new List<Color>();
            //将所有动画的数据打包到一张贴图中
            for (int a = 0; a < mAnimationData.Count; ++a)
            {
                animationOffset.Add(idx);
                var frames = mAnimationData[a].frames;
                var transforms = frames.boneTransforms;
                if (customBones)
                {
                    transforms = frames.customBoneTransforms;
                }
                int nFrames = transforms.Count;
                for (int i = 0; i < nFrames; ++i)
                {
                    if (idx > maxTextureSize * maxTextureSize)
                    {
                        Debug.Assert(false, "Can't create texture!");
                        return null;
                    }
                    int nBones = transforms[i].Length;
                    for (int k = 0; k < nBones; ++k)
                    {
                        if (blendType == AnimationBlendType.Fast)
                        {
                            //第i帧的第k个骨骼
                            Matrix4x4 boneTransform = transforms[i][k];
                            if (!customBones)
                            {
                                boneTransform = transforms[i][k] * mBones.GetBindPose(k);
                            }
                            var c0 = boneTransform.GetColumn(0);
                            var c1 = boneTransform.GetColumn(1);
                            var c2 = boneTransform.GetColumn(2);
                            var c3 = boneTransform.GetColumn(3);
                            var col0 = new Color(c0.x, c0.y, c0.z, c0.w);
                            var col1 = new Color(c1.x, c1.y, c1.z, c1.w);
                            var col2 = new Color(c2.x, c2.y, c2.z, c2.w);
                            var col3 = new Color(c3.x, c3.y, c3.z, c3.w);
                            transformData.Add(col0);
                            transformData.Add(col1);
                            transformData.Add(col2);
                            transformData.Add(col3);
                            idx += TRNASFORM_BLOCK_SIZE;
                        }
                        else if (blendType == AnimationBlendType.Slerp)
                        {
                            Matrix4x4 bindPose = Matrix4x4.identity;
                            if (!customBones)
                            {
                                bindPose = mBones.GetBindPose(k);
                            }
                            var c0 = bindPose.GetColumn(0);
                            var c1 = bindPose.GetColumn(1);
                            var c2 = bindPose.GetColumn(2);
                            var c3 = bindPose.GetColumn(3);
                            var col0 = new Color(c0.x, c0.y, c0.z, c0.w);
                            var col1 = new Color(c1.x, c1.y, c1.z, c1.w);
                            var col2 = new Color(c2.x, c2.y, c2.z, c2.w);
                            var col3 = new Color(c3.x, c3.y, c3.z, c3.w);
                            transformData.Add(col0);
                            transformData.Add(col1);
                            transformData.Add(col2);
                            transformData.Add(col3);
                            Quaternion rotation = transforms[i][k].rotation;
                            Vector4 translation = transforms[i][k].GetColumn(3);
                            transformData.Add(new Color(rotation.x, rotation.y, rotation.z, rotation.w));
                            transformData.Add(new Color(translation.x, translation.y, translation.z, translation.w));
                            idx += SLERP_TRNASFORM_BLOCK_SIZE;
                        }
                        else
                        {
                            Debug.Assert(false);
                        }
                    }
                }
            }

            return transformData.ToArray();
        }

        //customBones:创建骨骼挂接点的动画数据
        //animationOffset: 返回每个动画数据在贴图中的起点偏移
        //返回一张包含所有动画骨骼transform数据的Color数组
        public Color[] CreateDynamicAnimationTextureData(bool customBones, AnimationBlendType blendType, out List<int> animationOffset)
        {
            animationOffset = new List<int>();
            int maxTextureSize = 2048;
            int idx = 0;
            List<Color> transformData = new List<Color>();
            //将所有动画的数据打包到一张贴图中
            for (int a = 0; a < mAnimationData.Count; ++a)
            {
                animationOffset.Add(idx);
                var frames = mAnimationData[a].frames;
                var transforms = frames.boneTransforms;
                if (customBones)
                {
                    transforms = frames.customBoneTransforms;
                }
                int nFrames = transforms.Count;
                for (int i = 0; i < nFrames; ++i)
                {
                    if (idx > maxTextureSize * maxTextureSize)
                    {
                        Debug.Assert(false, "Can't create texture!");
                        return null;
                    }
                    int nBones = transforms[i].Length;
                    for (int k = 0; k < nBones; ++k)
                    {
                        if (blendType == AnimationBlendType.Fast)
                        {
                            var boneTransform = transforms[i][k];
                            if (!customBones)
                            {
                                boneTransform = transforms[i][k] * mBones.GetBindPose(k);
                            }
                            var r0 = boneTransform.GetRow(0);
                            var r1 = boneTransform.GetRow(1);
                            var r2 = boneTransform.GetRow(2);
                            var r3 = boneTransform.GetRow(3);
                            var row0 = new Color(r0.x, r0.y, r0.z, r0.w);
                            var row1 = new Color(r1.x, r1.y, r1.z, r1.w);
                            var row2 = new Color(r2.x, r2.y, r2.z, r2.w);
                            var row3 = new Color(r3.x, r3.y, r3.z, r3.w);
                            transformData.Add(row0);
                            transformData.Add(row1);
                            transformData.Add(row2);
                            transformData.Add(row3);
                            idx += 4;
                        }
                        else if (blendType == AnimationBlendType.Slerp)
                        {
                            Matrix4x4 bindPose = Matrix4x4.identity;
                            if (!customBones)
                            {
                                bindPose = mBones.GetBindPose(k);
                            }
                            var r0 = bindPose.GetRow(0);
                            var r1 = bindPose.GetRow(1);
                            var r2 = bindPose.GetRow(2);
                            var r3 = bindPose.GetRow(3);
                            var row0 = new Color(r0.x, r0.y, r0.z, r0.w);
                            var row1 = new Color(r1.x, r1.y, r1.z, r1.w);
                            var row2 = new Color(r2.x, r2.y, r2.z, r2.w);
                            var row3 = new Color(r3.x, r3.y, r3.z, r3.w);
                            transformData.Add(row0);
                            transformData.Add(row1);
                            transformData.Add(row2);
                            transformData.Add(row3);
                            Quaternion rotation = transforms[i][k].rotation;
                            Vector4 translation = transforms[i][k].GetColumn(3);
                            transformData.Add(new Color(rotation.x, rotation.y, rotation.z, rotation.w));
                            transformData.Add(new Color(translation.x, translation.y, translation.z, translation.w));
                            idx += 6;
                        }
                        else
                        {
                            Debug.Assert(false);
                        }
                    }
                }
            }

            return transformData.ToArray();
        }

        public void CreateCPUDrivenCustomBonesAnimationData(out List<int> animationOffset, out string[] cpuDrivenCustomBoneNames, out Vector3[] translations, out Quaternion[] rotations, out Vector3[] scalings)
        {
            animationOffset = new List<int>();
            cpuDrivenCustomBoneNames = null;
            int idx = 0;
            List<Vector3> translationData = new List<Vector3>();
            List<Vector3> scalingData = new List<Vector3>();
            List<Quaternion> rotationData = new List<Quaternion>();
            //将所有动画的数据打包到一个数组中
            for (int a = 0; a < mAnimationData.Count; ++a)
            {
                animationOffset.Add(idx);
                var frames = mAnimationData[a].frames;
                var frameTranslations = frames.cpuDrivenCustomBoneTranslations;
                var frameScalings = frames.cpuDrivenCustomBoneScalings;
                var frameRotations = frames.cpuDrivenCustomBoneRotations;
                int nFrames = frameTranslations.Count;
                Debug.Assert(frameTranslations.Count == frameScalings.Count && frameTranslations.Count == frameRotations.Count);
                for (int i = 0; i < nFrames; ++i)
                {
                    translationData.AddRange(frameTranslations[i]);
                    scalingData.AddRange(frameScalings[i]);
                    rotationData.AddRange(frameRotations[i]);

                    idx += frameTranslations[i].Length;
                }

                if (cpuDrivenCustomBoneNames == null)
                {
                    cpuDrivenCustomBoneNames = frames.cpuDrivenCustomBoneNames;
                }
            }

            rotations = rotationData.ToArray();

            //check if translation is valid
            bool validTranslation = false;
            for (int i = 0; i < translationData.Count; ++i)
            {
                if (translationData[i] != Vector3.zero)
                {
                    validTranslation = true;
                    break;
                }
            }
            //check if scaling is valid
            bool validScaling = false;
            for (int i = 0; i < scalingData.Count; ++i)
            {
                if (scalingData[i] != Vector3.one)
                {
                    validScaling = true;
                    break;
                }
            }

            if (validScaling)
            {
                scalings = scalingData.ToArray();
            }
            else
            {
                scalings = null;
            }
            if (validTranslation)
            {
                translations = translationData.ToArray();
            }
            else
            {
                translations = null;
            }
        }

        //创建动画速度参数信息
        AnimationSpeedInfo CreateSpeedInfo(StateInfo stateInfo)
        {
            AnimationSpeedInfo speedInfo = new AnimationSpeedInfo();
            speedInfo.speed = stateInfo.speed;
            speedInfo.speedParameter = stateInfo.speedParameter;
            speedInfo.speedParameterActive = stateInfo.speedParameterActive;
            return speedInfo;
        }

        //创建动画cycle offset参数信息
        AnimationCycleOffsetInfo CreateCycleOffsetInfo(StateInfo stateInfo)
        {
            AnimationCycleOffsetInfo info = new AnimationCycleOffsetInfo();
            info.cycleOffset = stateInfo.cycleOffset;
            info.cycleOffsetParameter = stateInfo.cycleOffsetParameter;
            info.cycleOffsetParameterActive = stateInfo.cycleOffsetParameterActive;
            return info;
        }

        //创建动画信息
        AnimationInfo[] CreateAnimationInfo(List<int> animationStartOffset, List<int> rigidAnimationStartOffset, List<int> cpuDrivenAnimationStartOffset)
        {
            Debug.Assert(animationStartOffset.Count == mAnimationData.Count);

            AnimationInfo[] info = new AnimationInfo[mAnimationData.Count];
            for (int i = 0; i < mAnimationData.Count; ++i)
            {
                string clipGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(mAnimationData[i].clip));
                int sampledFrameCount = Mathf.FloorToInt(mAnimationData[i].lengthInSeconds * mAnimationData[i].framerate * mAnimationData[i].frameMultiplier);
                info[i] = new AnimationInfo(sampledFrameCount, mAnimationData[i].lengthInSeconds, mAnimationData[i].framerate, mAnimationData[i].frameMultiplier, mAnimationData[i].loop, animationStartOffset[i], rigidAnimationStartOffset[i], cpuDrivenAnimationStartOffset[i], clipGuid, mAnimationData[i].clip.name);
            }
            return info;
        }

        int GetAnimationInfoIndex(string clipGuid, AnimationInfo[] infos)
        {
            for (int i = 0; i < infos.Length; ++i)
            {
                if (infos[i].clipGuid == clipGuid)
                {
                    return i;
                }
            }
            Debug.Assert(false);
            return -1;
        }

        AnimationStateInfo[] CreateAnimationStateInfo(List<StateInfo> stateInfos, AnimationInfo[] animInfos)
        {
            AnimationStateInfo[] info = new AnimationStateInfo[stateInfos.Count];
            for (int i = 0; i < stateInfos.Count; ++i)
            {
                var speedInfo = CreateSpeedInfo(stateInfos[i]);
                var cycleOffsetInfo = CreateCycleOffsetInfo(stateInfos[i]);
                string clipGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(stateInfos[i].clip));
                int animInfoIndex = GetAnimationInfoIndex(clipGuid, animInfos);
                info[i] = new AnimationStateInfo(stateInfos[i].name, animInfoIndex, stateInfos[i].transitions, stateInfos[i].events, speedInfo, cycleOffsetInfo);
            }
            return info;
        }

        AnimationParameterInfo[] CreateParameterInfo(List<ParameterInfo> parameters)
        {
            AnimationParameterInfo[] info = new AnimationParameterInfo[parameters.Count];
            for (int i = 0; i < info.Length; ++i)
            {
                info[i] = new AnimationParameterInfo();
                info[i].defaultBool = parameters[i].defaultBool;
                info[i].defaultInt = parameters[i].defaultInt;
                info[i].defaultFloat = parameters[i].defaultFloat;
                info[i].name = parameters[i].name;
                info[i].nameHash = parameters[i].nameHash;
                info[i].type = parameters[i].type;
            }
            return info;
        }

        void SavePrefab(GameObject newPrefab, GameObject oldPrefab, string outputFolder)
        {
            if (newPrefab == null || string.IsNullOrEmpty(outputFolder))
            {
                Debug.Assert(false);
                return;
            }

            //fix prefab instance
            FixPrefabInstance(newPrefab, oldPrefab);

            //save prefab
            string prefabPath = Utils.ConvertToUnityAssetsPath(outputFolder + "/" + newPrefab.name + ".prefab");
            PrefabUtility.SaveAsPrefabAsset(newPrefab, prefabPath);
        }

        void FixPrefabInstance(GameObject newPrefab, GameObject oldPrefab)
        {
            var cpuAnimationTags = newPrefab.GetComponentsInChildren<CPUAnimationTag>(true);
            for (int i = cpuAnimationTags.Length - 1; i >= 0; --i)
            {
                FixPrefabInstance(cpuAnimationTags[i].gameObject, cpuAnimationTags[i].transform.parent);
            }
        }

        void FixPrefabInstance(GameObject gameObject, Transform parent)
        {
            GameObject oldPrefabGameObject = FindOldPrefabGameObject(gameObject.GetInstanceID());
            Debug.Assert(oldPrefabGameObject != null);
            var newPrefabGameObject = PrefabUtility.GetCorrespondingObjectFromSource(oldPrefabGameObject);
            if (newPrefabGameObject != null)
            {
                //删除普通game object,改成prefab instance
                var newChildPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(newPrefabGameObject);

                newChildPrefabInstance.name = gameObject.name;
                bool hasCPUAnimationTag = gameObject.GetComponent<CPUAnimationTag>() != null;
                newChildPrefabInstance.transform.parent = parent;
                if (newChildPrefabInstance.transform.parent != parent)
                {
                    //没有设置parent成功,不使用prefab instance
                    //GameObject.DestroyImmediate(newChildPrefabInstance);
                    Debug.LogError("Set Parent failed, but you can still use this prefab!");
                }
                else
                {
                    if (hasCPUAnimationTag)
                    {
                        bool attachedToAnyBone = IsGameObjectAttachedToAnyBone(gameObject);
                        if (attachedToAnyBone)
                        {
                            newChildPrefabInstance.transform.localPosition = Vector3.zero;
                            newChildPrefabInstance.transform.localScale = Vector3.one;
                            newChildPrefabInstance.transform.localRotation = Quaternion.identity;
                        }
                        else
                        {
                            newChildPrefabInstance.transform.localPosition = oldPrefabGameObject.transform.localPosition;
                            newChildPrefabInstance.transform.localScale = oldPrefabGameObject.transform.localScale;
                            newChildPrefabInstance.transform.localRotation = oldPrefabGameObject.transform.localRotation;
                        }
                    }
                    else
                    {
                        newChildPrefabInstance.transform.localPosition = oldPrefabGameObject.transform.localPosition;
                        newChildPrefabInstance.transform.localScale = oldPrefabGameObject.transform.localScale;
                        newChildPrefabInstance.transform.localRotation = oldPrefabGameObject.transform.localRotation;
                    }
                    GameObject.DestroyImmediate(gameObject);
                }
            }
            else
            {
                var transform = gameObject.transform;
                int childCount = transform.childCount;
                for (int i = childCount - 1; i >= 0; --i)
                {
                    FixPrefabInstance(transform.GetChild(i).gameObject, transform);
                }
            }
        }

        GameObject FindOldPrefabGameObject(int newGameObjectInstanceID)
        {
            GameObject result;
            mNewGameObjectToOldGameObjectInstanceIDMap.TryGetValue(newGameObjectInstanceID, out result);
            return result;
        }

        StateInfo GetStateInfo(string stateName, List<StateInfo> stateInfos)
        {
            for (int i = 0; i < stateInfos.Count; ++i)
            {
                if (stateName == stateInfos[i].name)
                {
                    return stateInfos[i];
                }
            }
            Debug.Assert(false);
            return null;
        }

        void CreateIDMap(GameObject newPrefab, GameObject oldPrefab, Dictionary<int, GameObject> newGameObjectToOldGameObjectInstanceIDMap)
        {
            newGameObjectToOldGameObjectInstanceIDMap.Add(newPrefab.GetInstanceID(), oldPrefab);
            int childCount = newPrefab.transform.childCount;
            for (int i = 0; i < childCount; ++i)
            {
                CreateIDMap(newPrefab.transform.GetChild(i).gameObject, oldPrefab.transform.GetChild(i).gameObject, newGameObjectToOldGameObjectInstanceIDMap);
            }
        }

        bool IsGameObjectAttachedToAnyBone(GameObject gameObject)
        {
            var correspondingGameObjectInOldPrefab = FindOldPrefabGameObject(gameObject.GetInstanceID());
            var rootBone = mBones.GetRootBone();
            var t = correspondingGameObjectInOldPrefab.transform;
            while (t != null)
            {
                if (t == rootBone)
                {
                    return true;
                }
                t = t.parent;
            }
            return false;
        }

        bool CanDeleteBipObject(Transform transform)
        {
            bool isBip = transform.gameObject.name.StartsWith("bip", true, null);
            if (!isBip)
            {
                return false;
            }

            bool hasCpuAnimationTag = (transform.GetComponent<CPUAnimationTag>() != null || IsAncestorHasCPUAnimationTag(transform));
            if (hasCpuAnimationTag)
            {
                return false;
            }

            return true;
        }

        void DeleteBipGameObjects(GameObject newPrefab)
        {
            var transform = newPrefab.transform;
            int n = transform.childCount;
            for (int i = n - 1; i >= 0; --i)
            {
                var child = transform.GetChild(i);
                bool canDelete = CanDeleteBipObject(child);
                if (canDelete)
                {
                    GameObject.DestroyImmediate(child.gameObject);
                }
                else
                {
                    DeleteBipGameObjects(child.gameObject);
                }
            }
        }

        public override List<Renderer> renderers { get { return mRenderers; } }
        public override BakeMaterialManager materialManager { get { return mMaterialManager; } }
        public Transform rootBone { get { return mBones.GetRootBone(); } set { mBones.SetRootBone(value); } }

        //管理一个prefab下的所有骨骼,不包括自定义骨骼
        BoneManager mBones = new BoneManager();
        //在prefab中发现的自定义骨骼
        List<Transform> mShaderDrivenCustomBones;
        List<Transform> mCPUDrivenCustomBones;
        //需要处理的renderer
        List<Renderer> mRenderers;
        List<AnimationData> mAnimationData = new List<AnimationData>();
        Dictionary<int, Mesh> mSkinMeshies = new Dictionary<int, Mesh>();
        Dictionary<int, Mesh> mRigidMeshies = new Dictionary<int, Mesh>();
        //skin mesh renderer使用的材质
        Dictionary<int, Material> mSkinRendererMaterials = new Dictionary<int, Material>();
        //custom bone的子节点mesh renderer使用的材质
        Dictionary<int, Material> mRigidRendererMaterials = new Dictionary<int, Material>();
        //将旧的prefab的shader转换成新的shader
        BakeMaterialManager mMaterialManager = new BakeMaterialManager();
        LODGroupManager mLODGroupManager;
        //从新的game object instanceID 找到老的game object instance id
        Dictionary<int, GameObject> mNewGameObjectToOldGameObjectInstanceIDMap = new Dictionary<int, GameObject>();
        //使用静态绑定的贴图
        //动画贴图中，每个变换矩阵的大小，单位是像素
        const int TRNASFORM_BLOCK_SIZE = TRNASFORM_COLUMN_SIZE * 4;
        const int SLERP_TRNASFORM_BLOCK_SIZE = TRNASFORM_COLUMN_SIZE * 6;
        //动画贴图中，每个变换矩阵列的大小，单位是像素
        const int TRNASFORM_COLUMN_SIZE = 2;
    }
}
#endif