%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Altar_plane
  m_Shader: {fileID: 4800000, guid: 12f865a19bb1cd941822f3f923be6f6d, type: 3}
  m_ShaderKeywords: DAYNIGHTTOGGLE_ON ETC1_EXTERNAL_ALPHA
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightMap:
        m_Texture: {fileID: 2800000, guid: 13b12630e16788d49bd204762c14b22c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 10d437627951aba4ca5af65f7338884c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutLine:
        m_Texture: {fileID: 2800000, guid: 9a761b7f699ea0f4d8b2ff8d09874cb2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - DayNightToggle: 1
    - PixelSnap: 0
    - _BUILDINGOUTLINE: 0
    - _BumpScale: 1
    - _Cutoff: 0.2
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlitterSpeed: 5
    - _GlitterStrength: 0.236
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _LightMapLocalIntensity: 1
    - _MaskDayNightInfluence: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineMipLevel: 0
    - _OutlineReferenceTexWidth: 256
    - _OutlineWidth: 0.87
    - _Parallax: 0.02
    - _STRAIGHT_COLOR_INPUT: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _ThresholdEnd: 0.25
    - _UVSec: 0
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MaskColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutLineColor: {r: 0.35686275, g: 0.9882353, b: 0.17254902, a: 1}
