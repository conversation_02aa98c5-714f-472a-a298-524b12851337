﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace TFW.Map {
    //加载队列中使用的任务的基类,不同类型的模型使用不同类型的加载任务
    public abstract class ModelLoadingTask {
        public ModelLoadingTask() { }

        //taskID其实是地图对象的id
        //priority决定了任务的加载优先级,例如地表需要被优先加载,前景物体可以稍后加载
        public ModelLoadingTask(Map map, int taskID, Action<ModelBase, bool> loadFinishedCallback, float priority) {
#if UNITY_EDITOR
            UnityEngine.Debug.Assert(loadFinishedCallback != null);
#endif
            mLoadFinishedCallback = loadFinishedCallback;
            mTaskID = taskID;
            mPriority = priority;
            mMap = map;
        }

        public virtual void OnDestroy() { }

        //加载一个任务,由加载队列管理器调用
        public void Load() {
            var objectData = mMap.FindObject(mTaskID);
            if (objectData != null) {
                var model = LoadImpl();
                bool suc = false;
                if (model != null) {
                    suc = true;
                }
                //加载完模型后的回调
                mLoadFinishedCallback(model, suc);
            }
            else {
                //无效的任务,因为地图对象已经被删除
#if UNITY_EDITOR
                UnityEngine.Debug.LogWarning(string.Format("object {0} is destroyed", mTaskID));
#endif
            }
        }
        //load的实现,由各种任务自己实现
        protected abstract ModelBase LoadImpl();

        public int id { get { return mTaskID; } }
        public float priority { get { return mPriority; } }
        //加载完任务后的回调
        protected Action<ModelBase, bool> mLoadFinishedCallback;
        protected int mTaskID;
        //priority低的task优先加载
        protected float mPriority = 0;
        protected Map mMap;
    }

    //加载任务的管理器
    public class ModelLoadingTaskManager {
        //设置一个一帧可以使用的最大加载时间,超过这个时间剩下的任务就不在当前帧执行了
        public void SetLoadingTimeThreshold(long timeInMS) {
            if (timeInMS <= 1) {
                timeInMS = 1;
            }
            mLoadTimeMSThresholdForOneFrame = timeInMS;
        }
        //加载一个任务,任务根据优先级排序
        public bool AddTask(ModelLoadingTask task) {
            if (ContainsTask(task.id) == false) {
                if (mTasks.Count == 0) {
                    var node = mNodePool.Require();
                    node.Value = task;
                    mTasks.AddLast(node);
                    mContainedTasks.Add(task.id, node);
                }
                else {
                    for (var cur = mTasks.First; ; cur = cur.Next) {
                        var curTask = cur.Value;
                        if (task.priority <= curTask.priority) {
                            var node = mNodePool.Require();
                            node.Value = task;
                            mTasks.AddBefore(cur, node);
                            mContainedTasks.Add(task.id, node);
                            break;
                        }

                        if (cur == mTasks.Last) {
                            var node = mNodePool.Require();
                            node.Value = task;
                            mTasks.AddLast(node);
                            mContainedTasks.Add(task.id, node);
                            break;
                        }
                    }
                }
                return true;
            }
            else {
                task.OnDestroy();
                return false;
            }
        }

        //取消一个任务
        public void CancelTask(int taskID) {
            LinkedListNode<ModelLoadingTask> node;
            bool found = mContainedTasks.TryGetValue(taskID, out node);
            if (found)
            {
                node.Value.OnDestroy();
                mNodePool.Release(node);
                mTasks.Remove(node);
                mContainedTasks.Remove(taskID);
            }
        }

        //是否任务队列中已经包含了任务
        public bool ContainsTask(int taskID) {
            return mContainedTasks.ContainsKey(taskID);
        }

        //运行加载队列
        public void Update() {
            int loadCount = 0;
            double totalElapsed = 0;
            while (mTasks.Count > 0) {
                mStopWatch.Start();

                var task = mTasks.First;
                task.Value.Load();
                mNodePool.Release(task);
                mContainedTasks.Remove(task.Value.id);
                mTasks.RemoveFirst();

                ++loadCount;

                mStopWatch.Stop();
                mStopWatch.Reset();

                totalElapsed += mStopWatch.ElapsedTicks / (double)Stopwatch.Frequency;
                if (totalElapsed >= mLoadTimeMSThresholdForOneFrame && loadCount >= mMinimumLoadObjectCountOneFrame) {
                    //超过最大消耗时间了,这帧不再进行加载任务
                    break;
                }
            }
        }

        //每秒可以消耗的加载时间,自己定义
        double mLoadTimeMSThresholdForOneFrame = 0.002;
        //每帧最少加载模型的数量
        int mMinimumLoadObjectCountOneFrame = 1;
        Stopwatch mStopWatch = new Stopwatch();
        Dictionary<int, LinkedListNode<ModelLoadingTask>> mContainedTasks = new Dictionary<int, LinkedListNode<ModelLoadingTask>>();
        //需要被加载的任务
        LinkedList<ModelLoadingTask> mTasks = new LinkedList<ModelLoadingTask>();
        static ObjectPool<LinkedListNode<ModelLoadingTask>> mNodePool = new ObjectPool<LinkedListNode<ModelLoadingTask>>(1000, () => new LinkedListNode<ModelLoadingTask>(null));
    }
}
