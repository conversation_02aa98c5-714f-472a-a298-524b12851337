﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    public partial class GridRegionEditorUI : UnityEditor.Editor
    {
        void DrawGridTemplates()
        {
            var editor = mLogic.editor;
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add"))
            {
                AddTemplate();
            }
            if (GUILayout.Button("Remove"))
            {
                if (EditorUtility.DisplayDialog("Warning", "Are you sure delete this? this action can't be redone!", "Yes", "No"))
                {
                    RemoveTemplate();
                }
            }
            if (GUILayout.But<PERSON>("Change Type"))
            {
                ChangeTemplateType();
            }
            EditorGUILayout.EndHorizontal();

            var templates = editor.templates;
            int selectedIndex = -1;
            bool selectionChange = false;
            for (int i = 0; i < templates.Count; ++i)
            {
                selectionChange = DrawTemplate(templates[i], i);
                if (selectionChange)
                {
                    selectedIndex = i;
                }
            }
            if (selectedIndex >= 0)
            {
                mLogic.selectedIndex = selectedIndex;
            }
            EditorGUILayout.EndVertical();
        }

        bool DrawTemplate(GridTemplate template, int i)
        {
            EditorGUIUtility.labelWidth = 40;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal();
            bool nowSelected = mLogic.selectedIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            template.name = EditorGUILayout.TextField(template.name);
            EditorGUILayout.IntField("Type", template.type);
            var newColor = EditorGUILayout.ColorField("", template.color);
            if (newColor != template.color)
            {
                template.color = newColor;
                template.templateObject.GetComponent<Renderer>().sharedMaterial.color = newColor;
            }

            if (GUILayout.Button("Change Color"))
            {
                mLogic.editor.RefreshTexture();
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.labelWidth = 0;
            return selectionChange;
        }

        void AddTemplate()
        {
            var templates = mLogic.editor.templates;
            int type = 1;
            if (templates.Count > 0)
            {
                type = templates[templates.Count - 1].type + 1;
            }
            var dlg = EditorUtils.CreateInputDialog("Add Region Brush");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "Region"),
                    new InputDialog.StringItem("Type", "", type.ToString()),
                };
            dlg.Show(items, OnClickAdd);
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            string name = (parameters[0] as InputDialog.StringItem).text;
            string typeStr = (parameters[1] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(name))
            {
                return false;
            }
            int type;
            bool suc = Utils.ParseInt(typeStr, out type);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }

            if (type <= 0)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0", "OK");
                return false;
            }

            var editor = mLogic.editor;
            var temp = editor.FindTemplate(type);
            if (temp != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {type} already existed!", "OK");
                return false;
            }

            var template = editor.AddTemplate(name, type, Color.white);
            mLogic.selectedIndex = editor.templateCount - 1;

            return true;
        }

        void RemoveTemplate()
        {
            if (mLogic.selectedIndex >= 0)
            {
                var editor = mLogic.editor;
                var template = editor.templates[mLogic.selectedIndex];
                var objects = editor.grids;
                int rows = objects.GetLength(0);
                int cols = objects.GetLength(1);
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        if (objects[i, j] == template.type)
                        {
                            editor.SetGridData(j, i, 0);
                        }
                    }
                }

                editor.RemoveTemplate(mLogic.selectedIndex);
                mLogic.selectedIndex = editor.templateCount - 1;

                editor.RefreshTexture();
            }
        }

        void ChangeTemplateType()
        {
            if (mLogic.selectedIndex >= 0)
            {
                var templates = mLogic.editor.templates;
                var dlg = EditorUtils.CreateInputDialog("Change Region Type");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Type", "", templates[mLogic.selectedIndex].type.ToString()),
                };
                dlg.Show(items, OnClickChangeTemplateType);
            }
        }

        bool OnClickChangeTemplateType(List<InputDialog.Item> parameters)
        {
            int newType;
            bool suc = Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newType);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }
            if (newType <= 0)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0", "OK");
                return false;
            }

            var editor = mLogic.editor;
            if (editor.FindTemplate(newType) != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {newType} is already used!", "OK");
                return false;
            }

            var template = editor.templates[mLogic.selectedIndex];
            var objects = editor.grids;
            int rows = objects.GetLength(0);
            int cols = objects.GetLength(1);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (objects[i, j] == template.type)
                    {
                        objects[i, j] = newType;
                    }
                }
            }
            template.type = newType;

            return true;
        }
    }
}

#endif