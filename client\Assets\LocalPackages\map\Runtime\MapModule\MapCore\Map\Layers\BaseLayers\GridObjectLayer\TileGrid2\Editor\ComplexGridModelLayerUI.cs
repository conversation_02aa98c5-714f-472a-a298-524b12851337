﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(ComplexGridModelLayerLogic))]
    public partial class ComplexGridModelLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as ComplexGridModelLayerLogic;

            mMultipleSelection = new SelectDecorationObjectsInRange(mLogic);

            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = mLogic.layer.GetPrefabManager();
            prefabManager.selectPrefabEvent += OnSelectPrefab;

            mLogic.InitTags();
            mLogic.prefab = prefabManager.selectedPrefab;

            mLogic.UpdateGizmoVisibilityState();

            //更新物体的坐标
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;
            layer.UpdateTransformChangedObjects();

            //show panel
            mPanel = ScriptableObject.CreateInstance(typeof(ComplexGridObjectLayerToolPanel)) as ComplexGridObjectLayerToolPanel;
            mPanel.titleContent = new GUIContent("Decoration Tool");
            mPanel.minSize = new Vector2(200, 100);
            mPanel.maxSize = new Vector2(300, 1000);
            var position = mPanel.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            mPanel.position = position;
            mPanel.Show(mLogic);

            mShowBounds = new GameObject().AddComponent<DrawBounds>();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var prefabManager = mLogic.layer.GetPrefabManager();
                prefabManager.selectPrefabEvent -= OnSelectPrefab;

                mLogic.SetLayerBoundsVisible(false);
                mLogic.SetGridVisible(false);
                HideIndicator();

                if (!mLogic.layerData.objectPlacementSetting.useMapLargeTile)
                {
                    mPanel.Close();
                }
                Object.DestroyImmediate(mPanel);
            }

            GameObject.DestroyImmediate(mShowBounds.gameObject);
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();

            var currentEvent = Event.current;
            CheckOperationType(currentEvent);

            var setting = mLogic.layerData.objectPlacementSetting;

            if (currentEvent.type == EventType.KeyDown)
            {
                Vector3 offset = Vector3.zero;
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    offset.z += setting.translationStep;
                }
                if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    offset.z -= setting.translationStep;
                }
                if (currentEvent.keyCode == KeyCode.LeftArrow)
                {
                    offset.x -= setting.translationStep;
                }
                if (currentEvent.keyCode == KeyCode.RightArrow)
                {
                    offset.x += setting.translationStep;
                }

                if (offset != Vector3.zero)
                {
                    mLogic.layer.MoveLocalViewport(offset);
                }

                float deltaScale = 0;
                if (currentEvent.keyCode == KeyCode.Equals)
                {
                    deltaScale += setting.scaleStep;
                }
                else if (currentEvent.keyCode == KeyCode.Minus)
                {
                    deltaScale -= setting.scaleStep;
                }

                if (deltaScale != 0)
                {
                    mLogic.layer.ScaleLocalViewport(deltaScale);
                }
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            bool addControl = false;

            if (mLogic.operationType == ComplexGridModelOperationType.kCreateObject)
            {
                if (mLogic.rotationSetting.Update())
                {
                    Repaint();
                }

                UpdateIndicator(pos);

                mPanel.DrawScene(pos);

                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        mPanel.AddObject(mLogic.selectedLOD, screenPos);
                    }
                    Repaint();
                }

                addControl = true;
            }
            else if (mLogic.operationType == ComplexGridModelOperationType.kRemoveObject)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                {
                    if (currentEvent.button == 0)
                    {
                        RemoveObject(mLogic.selectedLOD, screenPos);
                    }
                    Repaint();
                }

                addControl = true;
            }
            else if (mLogic.operationType == ComplexGridModelOperationType.kSelectObject)
            {
                if (setting.showObjectBounds)
                {
                    addControl = true;
                    if (currentEvent.type == EventType.MouseDown && currentEvent.alt == false)
                    {
                        if (currentEvent.button == 0)
                        {
                            ShowObjectBounds(pos);
                        }
                        Repaint();
                    }
                }
            }
            else if (mLogic.operationType == ComplexGridModelOperationType.kSelectObjectInRange)
            {
                addControl = true;
                mMultipleSelection.DrawSceneGUI(currentEvent);
            }

            var realBounds = mLogic.layerData.realLayerBounds;

            var center = new Vector3(realBounds.x + realBounds.width * 0.5f, 0, realBounds.y + realBounds.height * 0.5f);
            var size = new Vector3(realBounds.width, 0, realBounds.height);
            Handles.color = Color.magenta;
            Handles.DrawWireCube(center, size);

            if (addControl)
            {
                HandleUtility.AddDefaultControl(0);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                var layerData = mLogic.layerData;
                var setting = layerData.objectPlacementSetting;
                mLogic.DrawGizmoUI();
                EditorGUILayout.BeginHorizontal();
                if (mLogic.operationType == ComplexGridModelOperationType.kSelectObject)
                {
                    if (!layerData.objectPlacementSetting.useMapLargeTile)
                    {
                        setting.showObjectBounds = EditorGUILayout.ToggleLeft(new GUIContent("Show Object Bounds When Select Object", "在选择模式下选中物体时是否显示其包围框"), setting.showObjectBounds);
                    }
                }
                bool oldVisibleState = layerData.showGridViewer;
                bool visible = EditorGUILayout.ToggleLeft(new GUIContent("Show Grid", "是否显示运行时分块"), oldVisibleState);
                if (visible != oldVisibleState)
                {
                    layerData.showGridViewer = visible;
                }
                EditorGUILayout.EndHorizontal();

                layerData.enableObjectMaterialChange = EditorGUILayout.ToggleLeft(new GUIContent("Enable Change Object Material", "是否允许允许时替换装饰物材质"), layerData.enableObjectMaterialChange);

                layerData.enableCullIntersectedObjects = EditorGUILayout.ToggleLeft(new GUIContent("Enable Cull Intersected Objects", "是否允许裁剪相互穿插的装饰物"), layerData.enableCullIntersectedObjects);

                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;

                if (GUILayout.Button(new GUIContent("Remove All Objects", "删除该layer所有物体")))
                {
                    if (EditorUtility.DisplayDialog("Warning", "This action can't be undone, are you sure?", "Yes", "No"))
                    {
                        layer.RemoveAllObjects();
                    }
                }

                if (!setting.useMapLargeTile)
                {
                    if (GUILayout.Button("Remove Objects In Current LOD"))
                    {
                        if (EditorUtility.DisplayDialog("Warning", "This action can't be undone, are you sure?", "Yes", "No"))
                        {
                            if (mLogic.selectedLOD >= 0)
                            {
                                List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
                                layer.layerData.GetObjectsOfLOD(objects, mLogic.selectedLOD);
                                for (int i = 0; i < objects.Count; ++i)
                                {
                                    layer.RemoveObject(objects[i].GetEntityID());
                                }
                            }
                        }
                    }

                    if (GUILayout.Button("Copy Objects To Other LOD"))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Copy Objects To Other LOD");
                        int curLOD = mLogic.selectedLOD;
                        int totalLODs = layer.lodCount;
                        int nextLOD = Mathf.Clamp(mLogic.selectedLOD + 1, 0, totalLODs - 1);
                        var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Target LOD", "复制到的目标lod", nextLOD.ToString()),
                    };
                        dlg.Show(items, OnClickCopyToOtherLOD);

                    }
                }

                if (GUILayout.Button(new GUIContent("Export Runtime Data", "导出游戏运行时数据,点击Export Map按钮也可以导出该layer运行时数据,如果只修改了该layer数据,可以点击此按钮刷新")))
                {
                    layer.ExportData();
                }

                DrawRealSize();

                if (mLODs == null || layer.lodCount != mLODs.Length)
                {
                    CreateLODNames(layer.lodCount);
                }

                if (!setting.useMapLargeTile)
                {
                    mLogic.DrawObjectTagUI();
                    int newSelectedLOD = EditorGUILayout.Popup("LOD", mLogic.selectedLOD, mLODs);
                    if (mLogic.selectedLOD != newSelectedLOD)
                    {
                        SetActiveLOD(newSelectedLOD);
                    }
                }

                var newOperation = (ComplexGridModelOperationType)EditorGUILayout.EnumPopup(new GUIContent("Operation", "当前操作"), mLogic.operationType);
                if (mLogic.operationType == ComplexGridModelOperationType.kSelectObjectInRange)
                {
                    mMultipleSelection.DrawInspectorGUI();
                }

                if (!setting.useMapLargeTile)
                {
                    mLogic.viewportSettingFoldState = EditorGUILayout.Foldout(mLogic.viewportSettingFoldState, "Viewport Movement Setting");
                    EditorGUILayout.BeginVertical("GroupBox");
                    setting.translationStep = EditorGUILayout.FloatField("Delta Distance", setting.translationStep);
                    setting.scaleStep = EditorGUILayout.FloatField("Delta Scale", setting.scaleStep);
                    EditorGUILayout.EndVertical();
                }

                if (mLogic.operationType == ComplexGridModelOperationType.kCreateObject)
                {
                    var prefabManager = mLogic.layer.GetPrefabManager();
                    bool useMapLargeTile = mLogic.layer.layerData.objectPlacementSetting.useMapLargeTile;
                    prefabManager.Draw(PrefabGroupDisplayFlag.CanRemovePrefab);
                }
                if (newOperation != mLogic.operationType)
                {
                    SetOperation(newOperation);
                }

                var layerID = mLogic.layerID;
                
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, LODConditionChangeCallback, OnLODCountChanged);

                EditorGUILayout.BeginVertical("GroupBox");
                if (!mLogic.layerData.objectPlacementSetting.useMapLargeTile)
                {
                    EditorGUILayout.TextArea("Accelerator Keys:\n 'Left' 'Right' 'Up' 'Down' to move viewport.\n '+' '-' to change viewport size!.");
                    if (GUILayout.Button("Calculate Object Count In Current LOD"))
                    {
                        List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
                        layerData.GetObjectsOfLOD(objects, mLogic.selectedLOD);
                        EditorUtility.DisplayDialog("", $"Object Count In Current LOD {objects.Count}", "OK");
                    }
                    EditorGUILayout.LabelField("Total Object Count(All LODs)", layerData.objectCount.ToString());
                }
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void RemoveObject(int lod, Vector2 screenPos)
        {
            var layerID = mLogic.layerID;
            var map = SLGMakerEditor.GetMap();
            var layer = map.GetMapLayerByID(layerID) as EditorComplexGridModelLayer;
            var worldPos = map.FromScreenToWorldPosition(screenPos);
            var obj = layer.FindObjectAtPosition(lod, worldPos);
            if (obj != null)
            {
                var act = new ActionRemoveComplexGridModel(obj.GetEntityID(), layerID, lod);
                ActionManager.instance.PushAction(act);
            }
        }

        string GetAssetGUID(GameObject prefab)
        {
            var assetPath = AssetDatabase.GetAssetPath(prefab);
            var guid = AssetDatabase.AssetPathToGUID(assetPath);
            return guid;
        }

        void ShowIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(mLogic.prefab, null);
            if (mLogic.prefab != null)
            {
                indicator.SetActive(true);
            }
            else
            {
                indicator.SetActive(false);
            }
        }

        void UpdateIndicator(Vector3 pos)
        {
            ShowIndicator();

            var setting = mLogic.layerData.objectPlacementSetting;

            if (setting.alignByGrid)
            {
                pos = mLogic.CalculatePosAlignedToGrid(pos);
            }

            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPosition(pos);
            indicator.SetRotation(mLogic.rotationSetting.rotation);
        }

        void HideIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(null, null);
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mLogic.prefab = prefab;
            mLogic.selectedPrefabGUID = GetAssetGUID(prefab);
        }

        void CheckOperationType(Event currentEvent)
        {
            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 1)
            {
                if (mLogic.operationType == ComplexGridModelOperationType.kCreateObject)
                {
                    mLogic.operationType = ComplexGridModelOperationType.kSelectObject;
                    HideIndicator();
                }
                else if (mLogic.operationType == ComplexGridModelOperationType.kSelectObject)
                {
                    mLogic.operationType = ComplexGridModelOperationType.kCreateObject;
                }
                Repaint();
                SceneView.RepaintAll();
            }
        }

        void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            mLogic.layer.OnLODCountChanged(oldLODCount, newLODCount);
            Repaint();
            SetActiveLOD(0);
        }

        //是否能chang lod
        bool LODConditionChangeCallback(int newLOD)
        {
            if (newLOD < mLogic.layer.lodCount)
            {
                if (EditorUtility.DisplayDialog("Warning", "Are you sure to change LOD? higher LOD objects will be deleted!", "Yes", "No"))
                {
                    return true;
                }
                return false;
            }
            return true;
        }

        void CreateLODNames(int n)
        {
            mLODs = new string[n];
            for (int i = 0; i < n; ++i)
            {
                mLODs[i] = i.ToString();
            }
        }

        void SetActiveLOD(int newLOD)
        {
            mLogic.selectedLOD = newLOD;
            mLogic.layer.ShowObjectsOfLOD(newLOD);
            mLogic.layer.layerData.currentLOD = newLOD;
        }

        bool OnClickCopyToOtherLOD(List<InputDialog.Item> parameters)
        {
            string newLODStr = (parameters[0] as InputDialog.StringItem).text;
            int newLOD;
            bool suc = Utils.ParseInt(newLODStr, out newLOD);
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;
            int totalLODs = layer.lodCount;
            if (newLOD != mLogic.selectedLOD && newLOD >= 0 && newLOD < totalLODs)
            {
                List<IComplexGridModelData> newLODObjects = new List<IComplexGridModelData>();
                layer.layerData.GetObjectsOfLOD(newLODObjects, newLOD);
                for (int i = 0; i < newLODObjects.Count; ++i)
                {
                    layer.RemoveObject(newLODObjects[i].GetEntityID());
                }

                List<IComplexGridModelData> oldLODObjects = new List<IComplexGridModelData>();
                layer.layerData.GetObjectsOfLOD(oldLODObjects, mLogic.selectedLOD);
                for (int i = 0; i < oldLODObjects.Count; ++i)
                {
                    ComplexGridModelData data = new ComplexGridModelData(Map.currentMap.nextCustomObjectID, Map.currentMap, 0, oldLODObjects[i].GetPosition(), oldLODObjects[i].GetRotation(), oldLODObjects[i].GetScale(), oldLODObjects[i].GetModelTemplate(), oldLODObjects[i].occupiedGridCount, (byte)newLOD, oldLODObjects[i].objectTag, false);
                    layer.AddObject(newLOD, data);
                    //something wrong about lod
                }

                return true;
            }

            return false;
        }

        void SetOperation(ComplexGridModelOperationType op)
        {
            mLogic.operationType = op;
            mPanel.Repaint();
        }

        void ShowObjectBounds(Vector3 pos)
        {
            List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
            mLogic.layer.GetAllObjects(objects);
            var p2 = new Vector2(pos.x, pos.z);
            for (int i = 0; i < objects.Count; ++i)
            {
                if (objects[i].GetBounds().Contains(p2))
                {
                    mShowBounds.bounds = Utils.RectToBounds(objects[i].GetBounds());
                    break;
                }
            }
        }

        void DrawRealSize()
        {
            mShowRealSize = EditorGUILayout.Foldout(mShowRealSize, new GUIContent("Layer Real Size", "设置layer的实际大小,因为layer的大小是根据摆放的模型来决定,如果不设置则使用创建时的layer大小"));
            var realBounds = mLogic.layerData.realLayerBounds;
            if (mShowRealSize)
            {
                EditorGUI.BeginChangeCheck();
                var newX = EditorGUILayout.FloatField("X", realBounds.x);
                var newZ = EditorGUILayout.FloatField("Z", realBounds.y);
                var newWidth = EditorGUILayout.FloatField("Width", realBounds.width);
                var newHeight = EditorGUILayout.FloatField("Height", realBounds.height);
                bool changed = EditorGUI.EndChangeCheck();
                if (changed)
                {
                    mLogic.layerData.realLayerBounds = new Rect(newX, newZ, newWidth, newHeight);
                    SceneView.RepaintAll();
                }
            }
        }

        ComplexGridModelLayerLogic mLogic;
        string[] mLODs;

        ComplexGridObjectLayerToolPanel mPanel;
        DrawBounds mShowBounds;

        bool mShowRealSize = true;

        SelectDecorationObjectsInRange mMultipleSelection;
    }
}


#endif