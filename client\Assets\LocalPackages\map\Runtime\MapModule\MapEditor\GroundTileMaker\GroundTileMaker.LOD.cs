﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        public class GroundLODMaterialSetting
        {
            public GroundLODMaterialSetting(string materialPath, List<MaskTextureSetting> maskTextureSetting)
            {
                this.materialPath = materialPath;
                this.maskTextureSetting = maskTextureSetting;
            }

            public string materialPath;
            public List<MaskTextureSetting> maskTextureSetting;
        }
    }
}


#endif