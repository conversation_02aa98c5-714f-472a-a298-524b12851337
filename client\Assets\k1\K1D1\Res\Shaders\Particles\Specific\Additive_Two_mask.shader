// Shader created with Shader Forge v1.40 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.40;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,cpap:True,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:False,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:34262,y:32680,varname:node_4795,prsc:2|emission-7878-OUT;n:type:ShaderForge.SFN_Tex2d,id:6450,x:32424,y:32390,ptovrint:False,ptlb:Texture,ptin:_Texture,varname:_Texture,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-745-OUT;n:type:ShaderForge.SFN_Color,id:5977,x:32424,y:32579,ptovrint:False,ptlb:Diffuse_Color,ptin:_Diffuse_Color,varname:_Diffuse_Color,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Multiply,id:5724,x:33243,y:32605,varname:node_5724,prsc:2|A-9809-OUT,B-5977-RGB,C-6450-A,D-9624-RGB,E-9624-A;n:type:ShaderForge.SFN_VertexColor,id:9624,x:32292,y:32810,varname:node_9624,prsc:2;n:type:ShaderForge.SFN_Multiply,id:7878,x:33963,y:32811,varname:node_7878,prsc:2|A-5112-OUT,B-1552-OUT,C-7930-OUT;n:type:ShaderForge.SFN_Desaturate,id:5003,x:32867,y:32389,varname:node_5003,prsc:2|COL-6450-RGB,DES-4324-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4324,x:32632,y:32476,ptovrint:False,ptlb:Desaturate,ptin:_Desaturate,varname:_Desaturate,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Power,id:9809,x:33075,y:32410,varname:node_9809,prsc:2|VAL-5003-OUT,EXP-6010-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6010,x:32888,y:32572,ptovrint:False,ptlb:Power,ptin:_Power,varname:_Power,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_TexCoord,id:7515,x:31643,y:32823,varname:node_7515,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_TexCoord,id:275,x:31925,y:32351,varname:node_275,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:9726,x:31830,y:32595,varname:node_9726,prsc:2|A-9846-OUT,B-241-OUT;n:type:ShaderForge.SFN_Add,id:745,x:32206,y:32368,varname:node_745,prsc:2|A-275-UVOUT,B-9724-OUT;n:type:ShaderForge.SFN_ValueProperty,id:5459,x:31439,y:32540,ptovrint:False,ptlb:U_speed,ptin:_U_speed,varname:_U_speed,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:3333,x:31390,y:32716,ptovrint:False,ptlb:V_speed,ptin:_V_speed,varname:_V_speed,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:9846,x:31616,y:32540,varname:node_9846,prsc:2|A-5459-OUT,B-7058-T;n:type:ShaderForge.SFN_Time,id:7058,x:31274,y:32587,varname:node_7058,prsc:2;n:type:ShaderForge.SFN_Multiply,id:241,x:31616,y:32682,varname:node_241,prsc:2|A-7058-T,B-3333-OUT;n:type:ShaderForge.SFN_Append,id:9886,x:31858,y:32843,varname:node_9886,prsc:2|A-7515-U,B-7515-V;n:type:ShaderForge.SFN_SwitchProperty,id:9724,x:32045,y:32595,ptovrint:False,ptlb:Custom_UV_MinTex_Switch,ptin:_Custom_UV_MinTex_Switch,varname:_Custom_UV_MinTex_Switch,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-9726-OUT,B-9886-OUT;n:type:ShaderForge.SFN_Multiply,id:5112,x:33505,y:32617,varname:node_5112,prsc:2|A-5724-OUT,B-4118-OUT;n:type:ShaderForge.SFN_Tex2d,id:2738,x:33085,y:32799,ptovrint:False,ptlb:Mask,ptin:_Mask,varname:_Mask,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-7271-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:4118,x:33287,y:32841,ptovrint:False,ptlb:Mask_Switch,ptin:_Mask_Switch,varname:_Mask_Switch,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-2738-RGB,B-2738-A;n:type:ShaderForge.SFN_Fresnel,id:1552,x:33389,y:33024,varname:node_1552,prsc:2|EXP-9715-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9715,x:33085,y:33175,ptovrint:False,ptlb:Fresnel,ptin:_Fresnel,varname:_Fresnel,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Tex2d,id:4102,x:32205,y:33294,ptovrint:False,ptlb:Dissolution_Tex,ptin:_Dissolution_Tex,varname:_Dissolution_Tex,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_ValueProperty,id:8211,x:32062,y:33616,ptovrint:False,ptlb:Dissolution_soft,ptin:_Dissolution_soft,varname:_Dissolution_soft,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Multiply,id:9071,x:32813,y:33416,varname:node_9071,prsc:2|A-4102-R,B-8211-OUT,C-9647-RGB;n:type:ShaderForge.SFN_TexCoord,id:9795,x:31975,y:33800,varname:node_9795,prsc:2,uv:1,uaff:True;n:type:ShaderForge.SFN_Vector1,id:8766,x:32178,y:33742,varname:node_8766,prsc:2,v1:-1.5;n:type:ShaderForge.SFN_Lerp,id:8363,x:32858,y:33646,varname:node_8363,prsc:2|A-8211-OUT,B-8766-OUT,T-2620-OUT;n:type:ShaderForge.SFN_Subtract,id:5968,x:33131,y:33557,varname:node_5968,prsc:2|A-9071-OUT,B-8363-OUT;n:type:ShaderForge.SFN_Clamp01,id:6812,x:33357,y:33516,varname:node_6812,prsc:2|IN-5968-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:7930,x:33707,y:33354,ptovrint:False,ptlb:Dissolution(X),ptin:_DissolutionX,varname:_DissolutionX,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-4602-OUT,B-6812-OUT;n:type:ShaderForge.SFN_Vector1,id:4602,x:33200,y:33312,varname:node_4602,prsc:2,v1:1;n:type:ShaderForge.SFN_TexCoord,id:842,x:32111,y:32939,varname:node_842,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:8194,x:32007,y:33262,varname:node_8194,prsc:2|A-7596-OUT,B-2383-OUT;n:type:ShaderForge.SFN_Add,id:7271,x:32619,y:33080,varname:node_7271,prsc:2|A-842-UVOUT,B-8194-OUT;n:type:ShaderForge.SFN_ValueProperty,id:5333,x:31616,y:33207,ptovrint:False,ptlb:U_speed_Mask,ptin:_U_speed_Mask,varname:_U_speed_Mask,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:3659,x:31578,y:33473,ptovrint:False,ptlb:V_speed_Mask,ptin:_V_speed_Mask,varname:_V_speed_Mask,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:7596,x:31793,y:33207,varname:node_7596,prsc:2|A-5333-OUT,B-7985-T;n:type:ShaderForge.SFN_Time,id:7985,x:31451,y:33254,varname:node_7985,prsc:2;n:type:ShaderForge.SFN_Multiply,id:2383,x:31793,y:33349,varname:node_2383,prsc:2|A-7985-T,B-3659-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:2620,x:32513,y:33802,ptovrint:False,ptlb:Diss_CutUV_Switch(X),ptin:_Diss_CutUV_SwitchX,varname:_Diss_CutUV_SwitchX,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-9624-A,B-9795-U;n:type:ShaderForge.SFN_Color,id:9647,x:32546,y:33523,ptovrint:False,ptlb:Dissolution_Color,ptin:_Dissolution_Color,varname:_Dissolution_Color,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;proporder:4324-5977-6450-9724-5459-3333-6010-4118-2738-5333-3659-4102-9647-8211-7930-2620-9715;pass:END;sub:END;*/

Shader "Core/ZTest/Additive_Two_mask" {
    Properties {
        _Desaturate ("Desaturate", Float ) = 0
        [HDR]_Diffuse_Color ("Diffuse_Color", Color) = (0.5,0.5,0.5,1)
        _Texture ("Texture", 2D) = "white" {}
        [MaterialToggle] _Custom_UV_MinTex_Switch ("Custom_UV_MinTex_Switch", Float ) = 0
        _U_speed ("U_speed", Float ) = 0
        _V_speed ("V_speed", Float ) = 0
        _Power ("Power", Float ) = 1
        [MaterialToggle] _Mask_Switch ("Mask_Switch", Float ) = 0
        _Mask ("Mask", 2D) = "white" {}
        _U_speed_Mask ("U_speed_Mask", Float ) = 0
        _V_speed_Mask ("V_speed_Mask", Float ) = 0
        _Dissolution_Tex ("Dissolution_Tex", 2D) = "white" {}
        [HDR]_Dissolution_Color ("Dissolution_Color", Color) = (0.5,0.5,0.5,1)
        _Dissolution_soft ("Dissolution_soft", Float ) = 1
        [MaterialToggle] _DissolutionX ("Dissolution(X)", Float ) = 1
        [MaterialToggle] _Diss_CutUV_SwitchX ("Diss_CutUV_Switch(X)", Float ) = 0
        _Fresnel ("Fresnel", Float ) = 0
        _Noise_qiangdu ("Noise_qiangdu", Float ) = 0
        _Noise ("Noise", 2D) = "white" {}
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend One One
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma target 3.0
            uniform sampler2D _Texture; uniform float4 _Texture_ST;
            uniform sampler2D _Mask; uniform float4 _Mask_ST;
            uniform sampler2D _Dissolution_Tex; uniform float4 _Dissolution_Tex_ST;
            UNITY_INSTANCING_BUFFER_START( Props )
                UNITY_DEFINE_INSTANCED_PROP( float4, _Diffuse_Color)
                UNITY_DEFINE_INSTANCED_PROP( float, _Desaturate)
                UNITY_DEFINE_INSTANCED_PROP( float, _Power)
                UNITY_DEFINE_INSTANCED_PROP( float, _U_speed)
                UNITY_DEFINE_INSTANCED_PROP( float, _V_speed)
                UNITY_DEFINE_INSTANCED_PROP( fixed, _Custom_UV_MinTex_Switch)
                UNITY_DEFINE_INSTANCED_PROP( fixed, _Mask_Switch)
                UNITY_DEFINE_INSTANCED_PROP( float, _Fresnel)
                UNITY_DEFINE_INSTANCED_PROP( float, _Dissolution_soft)
                UNITY_DEFINE_INSTANCED_PROP( fixed, _DissolutionX)
                UNITY_DEFINE_INSTANCED_PROP( float, _U_speed_Mask)
                UNITY_DEFINE_INSTANCED_PROP( float, _V_speed_Mask)
                UNITY_DEFINE_INSTANCED_PROP( fixed, _Diss_CutUV_SwitchX)
                UNITY_DEFINE_INSTANCED_PROP( float4, _Dissolution_Color)
            UNITY_INSTANCING_BUFFER_END( Props )
            struct VertexInput {
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 texcoord0 : TEXCOORD0;
                float4 texcoord1 : TEXCOORD1;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                float2 uv0 : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float4 posWorld : TEXCOORD2;
                float3 normalDir : TEXCOORD3;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                UNITY_SETUP_INSTANCE_ID( v );
                UNITY_TRANSFER_INSTANCE_ID( v, o );
                o.uv0 = v.texcoord0;
                o.uv1 = v.texcoord1;
                o.vertexColor = v.vertexColor;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                UNITY_SETUP_INSTANCE_ID( i );
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                i.normalDir = normalize(i.normalDir);
                i.normalDir *= faceSign;
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
////// Lighting:
////// Emissive:
                float _U_speed_var = UNITY_ACCESS_INSTANCED_PROP( Props, _U_speed );
                float4 node_7058 = _Time;
                float _V_speed_var = UNITY_ACCESS_INSTANCED_PROP( Props, _V_speed );
                float2 _Custom_UV_MinTex_Switch_var = lerp( float2((_U_speed_var*node_7058.g),(node_7058.g*_V_speed_var)), float2(i.uv1.r,i.uv1.g), UNITY_ACCESS_INSTANCED_PROP( Props, _Custom_UV_MinTex_Switch ) );
                float2 node_745 = (i.uv0+_Custom_UV_MinTex_Switch_var);
                float4 _Texture_var = tex2D(_Texture,TRANSFORM_TEX(node_745, _Texture));
                float _Desaturate_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Desaturate );
                float _Power_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Power );
                float4 _Diffuse_Color_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Diffuse_Color );
                float _U_speed_Mask_var = UNITY_ACCESS_INSTANCED_PROP( Props, _U_speed_Mask );
                float4 node_7985 = _Time;
                float _V_speed_Mask_var = UNITY_ACCESS_INSTANCED_PROP( Props, _V_speed_Mask );
                float2 node_7271 = (i.uv0+float2((_U_speed_Mask_var*node_7985.g),(node_7985.g*_V_speed_Mask_var)));
                float4 _Mask_var = tex2D(_Mask,TRANSFORM_TEX(node_7271, _Mask));
                float3 _Mask_Switch_var = lerp( _Mask_var.rgb, _Mask_var.a, UNITY_ACCESS_INSTANCED_PROP( Props, _Mask_Switch ) );
                float _Fresnel_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Fresnel );
                float4 _Dissolution_Tex_var = tex2D(_Dissolution_Tex,TRANSFORM_TEX(i.uv0, _Dissolution_Tex));
                float _Dissolution_soft_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Dissolution_soft );
                float4 _Dissolution_Color_var = UNITY_ACCESS_INSTANCED_PROP( Props, _Dissolution_Color );
                float _Diss_CutUV_SwitchX_var = lerp( i.vertexColor.a, i.uv1.r, UNITY_ACCESS_INSTANCED_PROP( Props, _Diss_CutUV_SwitchX ) );
                float3 _DissolutionX_var = lerp( 1.0, saturate(((_Dissolution_Tex_var.r*_Dissolution_soft_var*_Dissolution_Color_var.rgb)-lerp(_Dissolution_soft_var,(-1.5),_Diss_CutUV_SwitchX_var))), UNITY_ACCESS_INSTANCED_PROP( Props, _DissolutionX ) );
                float3 emissive = (((pow(lerp(_Texture_var.rgb,dot(_Texture_var.rgb,float3(0.3,0.59,0.11)),_Desaturate_var),_Power_var)*_Diffuse_Color_var.rgb*_Texture_var.a*i.vertexColor.rgb*i.vertexColor.a)*_Mask_Switch_var)*pow(1.0-max(0,dot(normalDirection, viewDirection)),_Fresnel_var)*_DissolutionX_var);
                float3 finalColor = emissive;
                return fixed4(finalColor,1);
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
