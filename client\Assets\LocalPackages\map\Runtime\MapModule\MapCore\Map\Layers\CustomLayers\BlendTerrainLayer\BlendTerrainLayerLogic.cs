﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public enum TerrainOperationType
    {
        Create,
        Remove,
        Select,
        Fill,
        Stamp,
        PaintHeight,
    }

    [ExecuteInEditMode]
    [Black]
    public class BlendTerrainLayerLogic : MapLayerLogic
    {
        protected override MoveAxis moveAxis { get { return MoveAxis.Y; } }

        void Start()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                prefabManager.addPrefabEvent += OnAddPrefab;
                prefabManager.setPrefabEvent += OnSetPrefab;
                prefabManager.removePrefabEvent += OnRemovePrefab;
                prefabManager.setPrefabFixedIndexEvent += OnSetPrefabFixedIndex;
                prefabManager.setPrefabSubGroupPrefabFixedIndexEvent += OnSetPrefabSubGroupFixedPrefabIndex;
                prefabManager.changeSubGroupPrefabCountEvent += OnChangeSubGroupPrefabCount;
                prefabManager.setSubGroupPrefabEvent += OnEditSubGroupPrefab;

                mBrushManager = new BrushManager();
#if UNITY_EDITOR_WIN
                var f = new FixTerrainEdgeNormal();
                f.Fix(layer);
#endif
            }
        }

        void OnDestroy()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                prefabManager.addPrefabEvent -= OnAddPrefab;
                prefabManager.setPrefabEvent -= OnSetPrefab;
                prefabManager.removePrefabEvent -= OnRemovePrefab;
                prefabManager.setPrefabFixedIndexEvent -= OnSetPrefabFixedIndex;
                prefabManager.setPrefabSubGroupPrefabFixedIndexEvent -= OnSetPrefabSubGroupFixedPrefabIndex;
                prefabManager.setSubGroupPrefabEvent -= OnEditSubGroupPrefab;
                prefabManager.changeSubGroupPrefabCountEvent -= OnChangeSubGroupPrefabCount;

                mBrushManager.OnDestroy();
            }
        }

        public BlendTerrainLayerData layerData
        {
            get
            {
                return Map.currentMap.FindObject(layerID) as BlendTerrainLayerData;
            }
        }

        public BlendTerrainLayer layer
        {
            get
            {
                return Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
            }
        }

        void OnSetPrefab(int groupIndex, int index, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, index, 0, prefabPath);
        }

        void OnChangeSubGroupPrefabCount(int groupIndex, int prefabIndex, int count)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetSubGroupPrefabCountByID(group.groupID, prefabIndex, count);
        }

        void OnEditSubGroupPrefab(int groupIndex, int index, int subTypeIndex, string prefabPath)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, index, subTypeIndex, prefabPath);
        }

        void OnAddPrefab(int groupIndex, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var idx = group.count - 1;
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, idx, 0, prefabPath);
        }

        void OnRemovePrefab(int groupIndex, int index, string prefabPath)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetGroupPrefabByID(group.groupID, index, 0, "");
        }

        //设置group使用的prefab
        void OnSetPrefabFixedIndex(int groupIndex, int index)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetPrefabFixedIndexByID(group.groupID, index);
        }

        void OnSetPrefabSubGroupFixedPrefabIndex(int groupIndex, int prefabIndex, int subgroupPrefabIndex)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var terrainPrefabManager = Map.currentMap.data.terrainPrefabManager;
            terrainPrefabManager.SetPrefabSubGroupPrefabFixedIndexByID(group.groupID, prefabIndex, subgroupPrefabIndex);
        }

        public TerrainOperationType operationType { set; get; }
        public bool paintOneTile 
        {
            set { mPaintOneTile = value; }
            get
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                int prefabGroupIndex = prefabManager.selectedGroupIndex;
                var group = prefabManager.GetGroupByIndex(prefabGroupIndex);
                if (group != null && !group.addPrefabSet)
                {
                    return true;
                }
                return mPaintOneTile;
            }
        }
        public int brushSize { set; get; } = 1;
        public bool eraseFill { set; get; }
        public bool rectangleFill { set; get; } = true;
        public bool randomPattern { set; get; } = false;
        public bool[] drawMasks { get { return mDrawMasks; } }
        public bool showRenderTextureOption { set; get; } = false;
        public bool showAtlasUI { set; get; } = false;
        public bool showMaskUI { set; get; } = false;
        public BrushManager brushManager { get { return mBrushManager; } }

        BrushManager mBrushManager;

        bool[] mDrawMasks = new bool[4]
        {
            true, true, true, true,
        };
        bool mPaintOneTile = false;
    }
}


#endif