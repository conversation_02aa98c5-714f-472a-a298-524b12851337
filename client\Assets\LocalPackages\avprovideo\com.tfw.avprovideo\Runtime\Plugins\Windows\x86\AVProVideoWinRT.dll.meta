fileFormatVersion: 2
guid: 38dfb99d7bab76e4488fc5b3f3cb0dd3
PluginImporter:
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  isOverridable: 0
  platformData:
    data:
      first:
        '': Any
      second:
        enabled: 0
        settings:
          Exclude Editor: 0
          Exclude Linux: 0
          Exclude Linux64: 1
          Exclude LinuxUniversal: 1
          Exclude OSXIntel: 0
          Exclude OSXIntel64: 1
          Exclude OSXUniversal: 1
          Exclude Win: 0
          Exclude Win64: 1
    data:
      first:
        '': Editor
      second:
        enabled: 0
        settings:
          CPU: x86
          OS: Windows
    data:
      first:
        Android: Android
      second:
        enabled: 0
        settings:
          CPU: AnyCPU
    data:
      first:
        Any: 
      second:
        enabled: 0
        settings: {}
    data:
      first:
        Editor: Editor
      second:
        enabled: 1
        settings:
          CPU: x86
          DefaultValueInitialized: true
          OS: Windows
    data:
      first:
        Facebook: Win
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        Facebook: Win64
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: Linux
      second:
        enabled: 1
        settings:
          CPU: x86
    data:
      first:
        Standalone: Linux64
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: LinuxUniversal
      second:
        enabled: 0
        settings:
          CPU: x86
    data:
      first:
        Standalone: OSXIntel
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        Standalone: OSXIntel64
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: OSXUniversal
      second:
        enabled: 0
        settings:
          CPU: x86
    data:
      first:
        Standalone: Win
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        Standalone: Win64
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        iPhone: iOS
      second:
        enabled: 0
        settings:
          CompileFlags: 
          FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
