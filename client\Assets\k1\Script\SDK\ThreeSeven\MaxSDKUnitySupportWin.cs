
using UnityEngine;
using System.Collections.Generic;
//using System.Windows.Forms;
using Newtonsoft.Json;


/**
 *  unity调用ios中的方法
 */
namespace maxsdk
{

    //#if UNITY_STANDALONE_WIN && !UNITY_EDITOR

    public class MaxSDKUnitySupportWin : MaxSDKUnitySupportBase
    {

        public override int GetSDKType()
        {

            return MaxSDKType.MAX_SDK_UNKNOWN;
        }

        public override void SetListener(MaxSDKListener listener)
        {
            Debug.Log("gameObject is " + listener.gameObject.name);
            if (listener == null)
            {
                Debug.LogError("set SQSDKListener error, listener is null");
                return;
            }
            string gameObjectName = listener.gameObject.name;

            //TODO 
        }

        public override void Init(MaxSDKInitInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void Login(MaxSDKLoginInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void Logout(MaxSDKLogoutInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void Pay(MaxSDKPayInfo info)
        {
            throw new System.NotImplementedException();
        }


        public override void Share(MaxSDKShareInfo info)
        {

            throw new System.NotImplementedException();
            //TODO 
        }

        /**
        * 扩展接口（同步），支持返回值
        */
        public override string DispatchSync(MaxSDKDispatchInfo dispatchInfo)
        {
            //TODO
            return "";
        }


        /**
         * 扩展接口（异步），带统一回调方法  OnDispatchResult(MaxSDKDispatchBean bean)
         */
        public override void DispatchASync(MaxSDKDispatchInfo dispatchInfo)
        {
            //TODO
        }

        public override void ReportRoleInfo(MaxSDKRoleInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void SwitchAccount(MaxSDKSwitchAccountInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void ReportEvent(MaxSDKReportEventInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void ExitGame(MaxSDKExitInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override void OpenActionExt(MaxSDKActionInfo info)
        {
            throw new System.NotImplementedException();
        }

        public override bool IsActionSupported(int type)
        {
            throw new System.NotImplementedException();
        }



        /*************  按需扩展，override 复写父类的方法  ************/

    }
    //#endif
}
