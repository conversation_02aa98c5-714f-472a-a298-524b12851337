﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionSetBlendTerrainLayerOneTile : EditorAction
    {
        public ActionSetBlendTerrainLayerOneTile(int layerID, int prefabGroupIndex, Vector2Int coordinate, int tileIndex, int tileType, int subTypeIndex)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as BlendTerrainLayer;

            mCoord = coordinate;
            var tile = layer.GetTile(coordinate.x, coordinate.y);
            if (tile != null)
            {
                mOldSubTypeIndex = tile.subTypeIndex;
                mOldTileIndex = tile.index;
                mOldTileType = tile.type;
            }
            mNewSubTypeIndex = subTypeIndex;
            mNewTileIndex = tileIndex;
            mNewTileType = tileType;
            mLayerID = layerID;
            mPrefabGroupIndex = prefabGroupIndex;
            mDescription = string.Format("set {0} layer's tiles", layer.name);
        }

        public override bool Do()
        {
            return SetTile(mNewTileIndex, mNewTileType, mNewSubTypeIndex);
        }

        public override bool Undo()
        {
            return SetTile(mOldTileIndex, mOldTileType, mOldSubTypeIndex);
        }

        bool SetTile(int tileIndex, int tileType, int subTypeIndex)
        {
            var map = Map.currentMap;
            var layer = map.GetMapLayerByID(mLayerID) as BlendTerrainLayer;
            if (layer != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorTerrainPrefabManager;
                if (mPrefabGroupIndex >= 0)
                {
                    var group = prefabManager.GetGroupByIndex(mPrefabGroupIndex);
                    if (group != null)
                    {
                        layer.SetTile(mCoord.x, mCoord.y, tileIndex, tileType, subTypeIndex);

                        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                        return true;
                    }
                }
            }
            return false;
        }

        public override string description
        {
            get
            {
                return mDescription;
            }
        }

        Vector2Int mCoord;
        int mLayerID;
        int mPrefabGroupIndex;
        int mOldTileIndex = 0;
        int mNewTileIndex = 0;
        int mOldTileType = 0;
        int mNewTileType = 0;
        int mOldSubTypeIndex = 0;
        int mNewSubTypeIndex = 0;
        string mDescription;
    }
}

#endif