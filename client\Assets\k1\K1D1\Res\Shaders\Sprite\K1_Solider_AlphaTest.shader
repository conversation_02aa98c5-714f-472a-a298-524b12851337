﻿// Upgrade NOTE: upgraded instancing buffer 'MyProperties' to new syntax.

Shader "K1/Solider/Alpha Test"
{
    Properties
    {
		_MainTex("Texture", 2D) = "white" {}
		_Color("Main Color", Color) = (1,1,1,1)
		_Intensity("Intensity", Range(0.1,5)) = 1
		_Cutoff("Alpha cutoff", Range(0,1)) = 0.5
		
		[Toggle] DayNightToggle("Day Night Toggle", Float) = 0
		_DayNightIntensity("Day Night Local Intensity", Range(0, 1)) = 0.5
    }
    SubShader
    {
        Cull Off ZWrite On ZTest On

		Tags
		{
			"Queue" = "AlphaTest+450" "IgnoreProjector" = "True"
		}

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
            #include "Assets/k1/Res/Shader/CG/DayNightSystemShaderHelper.cginc"
			#pragma multi_compile_instancing
			#pragma multi_compile _ DAYNIGHTTOGGLE_GLOBAL_ON
			#pragma multi_compile_local _ DAYNIGHTTOGGLE_ON

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : COLOR;
				UNITY_VERTEX_OUTPUT_STEREO
            };

			sampler2D _MainTex;
			half4 _Color;
			half _Intensity;
			half _Cutoff;
			half _DayNightIntensity;

            v2f vert (appdata v)
            {
                v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);

                col.rgb = col.rgb * _Color.rgb * _Intensity;
                col = col * i.color;
                
                #ifdef DAYNIGHTTOGGLE_GLOBAL_ON
            		#ifdef DAYNIGHTTOGGLE_ON
                        col.rgb = lerp(col.rgb, ApplyDayNightLut(col.rgb), _DayNightIntensity);
			        #endif
			    #endif
                
                return col;
            }
            ENDCG
        }
    }
}
