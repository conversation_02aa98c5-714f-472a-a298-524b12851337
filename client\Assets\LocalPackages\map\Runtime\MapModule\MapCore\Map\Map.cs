﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */


using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    /// <summary>
    /// 管理世界地图的逻辑,地图上的接口都从这走
    /// </summary>
    public partial class Map
    {
        public Map(string name, MapManager.MapSetting mapSetting, bool isEditorMap = false,
            MapEventListener listener = null)
        {
            mMapEventListener = listener;
            mName = name;
            mMapSetting = mapSetting;
            currentMap = this;
            mIsEditorMap = isEditorMap;
            enableLOD = true;
            mFrameActionManager = new FrameActionManager(isEditorMap);

            if (isEditorMap)
            {
                Shader.SetGlobalFloat("_AlphaMultiplier", 1);
            }
        }

        /// <summary>
        /// 加载地图
        /// </summary>
        public void Load(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter, Rect viewport)
        {
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -1");
            //Debug.Assert(mView == null);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -2");
            //Debug.Assert(editorData != null);
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -3");
            this.viewport = viewport;
            //UnityEngine.Debug.Log($"[{nameof(MapCameraMgr)}] Init MapCamera MapMgrLayer LoadMap step14 -4");
            originalViewport = viewport;
            mView = new MapView(this, viewCenter, camera, editorData.backgroundSetting, mIsEditorMap);
            LoadMapData(editorData, camera, viewCenter, mView.root);
            mView.PostInitialize();

            var origin = Vector3.zero;
            if (mMapSetting != null)
            {
                origin = mMapSetting.origin;
            }

            mCullManager.Init(this, origin, "map cull manager", 80, editorData.map.width, editorData.map.height, 0,
                false, false);
        }

        /// <summary>
        /// 异步加载地图
        /// </summary>
        public IEnumerator LoadAsync(config.SLGMakerData editorData, MapCameras camera, Vector3 viewCenter,
            Rect viewport)
        {
            Debug.Assert(mView == null);
            Debug.Assert(editorData != null);
            this.viewport = viewport;
            originalViewport = viewport;
            mView = new MapView(this, viewCenter, camera, editorData.backgroundSetting, mIsEditorMap);
            yield return LoadMapDataAsync(editorData, camera, viewCenter, mView.root);
            mView.PostInitialize();

            var mapOrigin = Vector3.zero;
            if (mMapSetting != null)
            {
                mapOrigin = mMapSetting.origin;
            }

            mCullManager.Init(this, mapOrigin, "map cull manager", 80, editorData.map.width, editorData.map.height, 0,
                false, false);
        }

        //清理地图数据
        public void Unload()
        {
            mDetailSprites?.OnDestroy();
            mDetailSprites = null;

            for (int i = mMapLayers.Count - 1; i >= 0; --i)
            {
                mMapLayers[i].Unload();
            }

            Debug.Assert(mMapLayers.Count == 0);
            if (mData != null)
            {
                mData.OnDestroy();
            }

            if (mView != null)
            {
                mView.OnDestroy();
            }

            mObjectManager.OnDestroy();
            mObjectManager = null;

            mCullManager.OnDestroy();
            mCullManager = null;

            mMapLayers = null;
            currentMap = null;

            CrossfadeModel.Clear();

            mFakePrefabManager.OnDestroy();
            mFakePrefabManager = null;

            //Resources.UnloadUnusedAssets();
        }

        public async void LoadDetailSprites(string spritePath, string spawnPointPath, string scaleConfigPath)
        {
            Debug.Assert(mDetailSprites == null);
            mDetailSprites = new DetailSprites();

            var keepScaleConfig0 = await MapModuleResourceMgr.LoadResourceAsync<KeepScaleConfig>(scaleConfigPath, null);
            {
                mDetailSprites.Init(spritePath, spawnPointPath, keepScaleConfig0, this);

                MapModuleResourceMgr.UnloadAsset(scaleConfigPath);
            } 
        }

        protected virtual void OnUnload()
        {
        }

        public int GetMapLayerCount()
        {
            return mMapLayers.Count;
        }

        public int GetMapLayerIndex(MapLayerBase layer)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] == layer)
                {
                    return i;
                }
            }

            return -1;
        }

        public void BroadcastMessage(GameMessage msg)
        {
            mMsgQueue.BroadcastGameMessage(msg);
        }

        public void AddObject(BaseObject obj)
        {
            mObjectManager.AddObject(obj);
        }

        public void RemoveObject(BaseObject obj)
        {
            mObjectManager.RemoveObject(obj);
        }

        public void RemoveObject(int id)
        {
            mObjectManager.RemoveObject(id);
        }

        public BaseObject FindObject(int objectID)
        {
            return mObjectManager.FindObject(objectID);
        }

        public void DestroyObject(int objectID)
        {
            mObjectManager.DestroyObject(objectID);
        }

        public void DestroyObject(BaseObject obj)
        {
            mObjectManager.DestroyObject(obj);
        }

        public MapObjectView GetMapObjectView(int id)
        {
            return view.GetMapObjectView(id);
        }

        public Vector2 CalculateViewSize(Vector3 viewCenter)
        {
            var rect = GetViewportRect(camera.firstCamera, camera.fieldOfView, camera.transform.position);
            var rectCenter = rect.center;
            var dx = rectCenter.x - viewCenter.x;
            var dy = rectCenter.y - viewCenter.z;

            float viewWidth = rect.width + Mathf.Abs(dx) * 2;
            float viewHeight = rect.height + Mathf.Abs(dy) * 2;
            return new Vector2(viewWidth, viewHeight);
        }

        public float CalculateCameraZoom(float cameraHeight)
        {
            return mData.lodManager.GetZoom(cameraHeight);
        }

        public float CalculateCameraHeightFromZoom(float zoom)
        {
            return mData.lodManager.GetCameraHeight(zoom);
        }

        //tileModelTemplate: 表示是否是一个大块的prefab
        public ModelTemplate GetOrCreateModelTemplate(int objectDataID, string prefabPath, bool tileModelTemplate,
            bool preload = false)
        {
            return data.GetOrCreateModelTemplate(objectDataID, prefabPath, tileModelTemplate, preload, this);
        }

        public void GetOrCreateModelTemplateAsync(int objectDataID, string prefabPath, bool tileModelTemplate, bool preload = false, System.Action<ModelTemplate> action = default)
        {
            data.GetOrCreateModelTemplateAsync(objectDataID, prefabPath, tileModelTemplate, preload, this, action);
        }

        

        public ModelTemplate FindModelTemplate(string prefabPath)
        {
            return data.modelTemplateManager.FindModelTemplate(prefabPath);
        }

        public ModelTemplate GetEntityModelTemplate(int objectDataID)
        {
            return data.GetEntityModelTemplate(objectDataID);
        }

        public bool AddMapLayer(MapLayerBase layer)
        {
            Debug.Assert(layer != null);
            var idx = mMapLayers.IndexOf(layer);
            if (idx == -1)
            {
                mMapLayers.Add(layer);

                RecordLayerHandle(layer);

                if (eventListener != null)
                {
                    eventListener.OnAddMapLayer(layer);
                }

                return true;
            }

            return false;
        }

        public bool RemoveMapLayerByIndex(int index)
        {
            if (index >= 0 && index < mMapLayers.Count)
            {
                EraseLayerHandle(mMapLayers[index]);
                mMapLayers[index].OnDestroy();
                mMapLayers.RemoveAt(index);
                return true;
            }

            return false;
        }

        public bool RemoveMapLayerByID(int id)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i].id == id)
                {
                    return RemoveMapLayerByIndex(i);
                }
            }

            Debug.Assert(false, "layer not found!");
            return false;
        }

        //预加载地图中使用到的prefab
        public void PreloadUsedPrefabs(bool instantiate)
        {
            //var startTime = Time.realtimeSinceStartup;
#if true
            HashSet<string> preloadedChildrenModelTemplates = new HashSet<string>();
            foreach (var p in mData.modelTemplates)
            {
                var temp = p.Value;
                if (temp.preload)
                {
                    int lodCount = temp.lodCount;
                    if (lodCount > 0)
                    {
                        for (int k = 0; k < lodCount; ++k)
                        {
                            var path = temp.GetLODPrefabPath(k);
                            //Debug.Log("preload prefab: " + path);
                            var prefab = MapModuleResourceMgr.LoadPrefab(path);
                            if (prefab != null)
                            {
                                if (instantiate)
                                {
                                    prefab = GameObject.Instantiate<GameObject>(prefab);
                                    mView.reusableGameObjectPool.Release(path, prefab, this);
                                }

                                //preload children prefabs
                                if (temp.isTileModelTemplate)
                                {
                                    List<ModelTemplate> childrenModelTemplates = temp.GetChildrenModelTemplates(k);
                                    for (int c = 0; c < childrenModelTemplates.Count; ++c)
                                    {
                                        var childPrefabPath = childrenModelTemplates[c].GetLODPrefabPath(0);
                                        //Debug.Log("preload prefab: " + childPrefabPath);
                                        if (preloadedChildrenModelTemplates.Contains(childPrefabPath) == false)
                                        {
                                            preloadedChildrenModelTemplates.Add(childPrefabPath);
                                            var childPrefab = MapModuleResourceMgr.LoadPrefab(childPrefabPath);
                                            if (childPrefab != null && instantiate)
                                            {
                                                childPrefab = GameObject.Instantiate<GameObject>(childPrefab);
                                                mView.reusableGameObjectPool.Release(childPrefabPath, childPrefab,
                                                    this);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        var path = temp.GetLODPrefabPath(0);
                        //Debug.Log("preload prefab: " + path);
                        var prefab = MapModuleResourceMgr.LoadPrefab(path);
                        if (prefab != null && instantiate)
                        {
                            prefab = GameObject.Instantiate<GameObject>(prefab);
                            mView.reusableGameObjectPool.Release(path, prefab, this);
                        }
                    }
                }
            }
#else
            //temp code, preload obstacles
#endif
            //var delta = Time.realtimeSinceStartup - startTime;
            //Debug.LogError("preload prefabs time: " + delta.ToString());
        }

        public int GetObjectLayerID(int objectID)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i].Contains(objectID))
                {
                    return mMapLayers[i].id;
                }
            }

            return 0;
        }

        public Vector2 GetCameraMoveHeightRange()
        {
            var setting = MapCameraMgr.cameraSetting;
            if (setting == null)
            {
                return Vector2.zero;
            }

            return new Vector2(setting.cameraMinHeight, setting.cameraMaxHeight);
        }

        public float GetZoom()
        {
            return mData.zoom;
        }

        public void SetZoom(float zoom)
        {
            mData.zoom = zoom;

            if (ZoomChangeEvent != null)
            {
                ZoomChangeEvent(zoom);
            }
        }

        public bool HasCachedObject(string prefabPath)
        {
            return view.reusableGameObjectPool.HasCache(prefabPath);
        }

        public GameMessageQueue messageQueue
        {
            get { return mMsgQueue; }
        }

        public int nextCustomObjectID
        {
            get { return mData.nextCustomObjectID; }
            set { mData.nextCustomObjectID = value; }
        }

        public MapData data
        {
            get { return mData; }
            set { mData = value; }
        }

        public MapView view
        {
            get { return mView; }
            set { mView = value; }
        }

        public ModelLoadingTaskManager loadingTaskManager
        {
            get { return mLoadingTaskManager; }
        }

        public MapEventListener eventListener
        {
            get { return mMapEventListener; }
        }

        public static Map currentMap { set; get; }
        public bool inAction { set; get; }
        public bool enableLOD { set; get; }

        public bool isEditorMode
        {
            get { return mIsEditorMap; }
        }

        //触发地图层LOD切换的事件
        public void OnMapLayerLODChanged(string mapLayerName, int currentLOD)
        {
            if (MapLayerLODChangeEvent != null)
            {
                MapLayerLODChangeEvent(mapLayerName, currentLOD);
            }
        }

        public void OnMapGameObjectLoaded(MapLayerData layerData, int objectID, GameObject gameObject)
        {
            if (MapObjectLoadEvent != null)
            {
                MapObjectLoadEvent(layerData, objectID, gameObject);
            }
        }

        public void OnMapGameObjectUnloaded(MapLayerData layerData, int objectID, GameObject gameObject)
        {
            if (MapObjectUnloadEvent != null)
            {
                MapObjectUnloadEvent(layerData, objectID, gameObject);
            }
        }

        //replace prefabs for a specified model template
        public void ReplaceModelTemplatePrefab(string oldPrefabPath, string newPrefabPath)
        {
            if (oldPrefabPath != newPrefabPath)
            {
                mData.ReplaceModelTemplatePrefab(oldPrefabPath, newPrefabPath);
                for (int i = 0; i < mMapLayers.Count; ++i)
                {
                    var layerView = mMapLayers[i].GetLayerView();
                    layerView.ReloadVisibleViews();
                }

                //在替换后再清理cache,因为reusable model在替换时会将老的game object放入pool
                mView.ClearCachedGameObjects(oldPrefabPath);
            }
        }

        public bool HasAction(int queueIndex, long key)
        {
            return mFrameActionManager.HasAction(queueIndex, key);
        }

        public FrameAction FindAction(int queueIndex, long key)
        {
            return mFrameActionManager.FindAction(queueIndex, key);
        }

        public LinkedListNode<FrameAction> FindActionPos(int queueIndex, long key)
        {
            return mFrameActionManager.FindActionPos(queueIndex, key);
        }

        public void AddFrameAction(int queueIndex, FrameAction action, bool addFirst)
        {
            mFrameActionManager.AddAction(queueIndex, action, addFirst);
        }

        public void PromoteFrameAction(int queueIndex, LinkedListNode<FrameAction> pos)
        {
            mFrameActionManager.PromoteAction(queueIndex, pos);
        }

        public void InsertFrameAction(int queueIndex, LinkedListNode<FrameAction> pos, FrameAction action)
        {
            mFrameActionManager.InsertFrameAction(queueIndex, pos, action);
        }

        public void AddFrameActions(int queueIndex, List<FrameAction> actions, bool addFirst)
        {
            mFrameActionManager.AddActions(queueIndex, actions, addFirst);
        }

        public int AddFrameActionQueue(string debugName)
        {
            return mFrameActionManager.AddQueue(debugName);
        }

        public bool RemoveFrameAction(int queueIndex, long key)
        {
            return mFrameActionManager.RemoveAction(queueIndex, key);
        }

        public bool RemoveFrameActionsOfType(int queueIndex, FrameActionType type)
        {
            return mFrameActionManager.RemoveActionsOfType(queueIndex, type);
        }

        public long RegisterCullObject(float radius, float centerX, float centerZ,
            System.Action<long, object, bool> visibilityChangeCallback, object customParam, out bool isVisible)
        {
            return mCullManager.RegisterCircle(radius, centerX, centerZ, visibilityChangeCallback, customParam,
                out isVisible);
        }

        public void UnregisterCullObject(long id)
        {
            mCullManager.UnregisterCullObject(id);
        }

        public bool IsCullObjectVisible(float radius, float centerX, float centerZ)
        {
            return mCullManager.IsCircleVisible(radius, centerX, centerZ);
        }

        public void SetRegionState(int regionID, bool enable)
        {
            Nav.NavigationManager.SetRegionState(regionID, enable);
        }

        public bool GetRegionState(int regionID)
        {
            return Nav.NavigationManager.GetRegionState(regionID);
        }

        public int GetNavMeshRegionID(Vector3 pos)
        {
            return Nav.NavigationManager.GetNavMeshRegionID(pos);
        }

        public List<int> GetNavMeshRegionIDs(Vector3 center, float radius)
        {
            return Nav.NavigationManager.GetNavMeshRegionIDs(center, radius);
        }

        public void ShowNavMeshRegions(bool show)
        {
            Nav.NavigationManager.ShowNavMeshRegions(show);
        }

        void RecordLayerHandle(MapLayerBase layer)
        {
            if (layer.GetType() == typeof(BlendTerrainLayer))
            {
                mBlendTerrainLayer = layer as BlendTerrainLayer;
            }
            else if (layer.GetType() == typeof(VaryingTileSizeTerrainLayer))
            {
                mVaryingTileSizeTerrainLayer = layer as VaryingTileSizeTerrainLayer;
            }
            else if (layer.GetType() == typeof(SimpleBlendTerrainLayer))
            {
                mSimpleBlendTerrainLayer = layer as SimpleBlendTerrainLayer;
            }
            else if (layer.GetType() == typeof(TileBlockTerrainLayer))
            {
                mTileBlockTerrainLayer = layer as TileBlockTerrainLayer;
            }
            else if (layer.GetType() == typeof(SplitFogLayer))
            {
                mSplitFogLayer = layer as SplitFogLayer;
            }
            else if (layer.GetType() == typeof(GridModelLayer))
            {
                mFrontLayer = layer as GridModelLayer;
            }
            else if (layer.GetType() == typeof(GridModelLayer2))
            {
                mDecorationLayer = layer as GridModelLayer2;
            }
            else if (layer.GetType() == typeof(RuntimeDecorationBorderLayer))
            {
                mDecorationBorderLayer = layer as RuntimeDecorationBorderLayer;
            }
            else if (layer.GetType() == typeof(RiverLayer))
            {
                mRiverLayer = layer as RiverLayer;
            }
            else if (layer.GetType() == typeof(CircleBorderLayer))
            {
                mCircleBorderLayer = layer as CircleBorderLayer;
            }
            else if (layer.GetType() == typeof(SplineLayer))
            {
                mSplineLayer = layer as SplineLayer;
            }
            else if (layer.GetType() == typeof(RuntimeRegionLayer))
            {
                mRegionLayer = layer as RuntimeRegionLayer;
            }
            else if (layer.GetType() == typeof(RuntimeRegionColorLayer))
            {
                mRegionColorLayer = layer as RuntimeRegionColorLayer;
            }
            else if (layer.GetType() == typeof(LODLayer))
            {
                mLODLayer = layer as LODLayer;
            }
            else if (layer.GetType() == typeof(DynamicObjectLayer))
            {
                mDynamicObjectLayer = layer as DynamicObjectLayer;
            }
            else if (layer.GetType() == typeof(CityTerritoryLayer))
            {
                mCityTerritoryLayer = layer as CityTerritoryLayer;
            }
            else
            {
#if UNITY_EDITOR
                Debug.Log($"Record Unknown Layer {layer.name}");
#endif
            }
        }

        void EraseLayerHandle(MapLayerBase layer)
        {
            if (layer.GetType() == typeof(BlendTerrainLayer))
            {
                mBlendTerrainLayer = null;
            }
            else if (layer.GetType() == typeof(VaryingTileSizeTerrainLayer))
            {
                mVaryingTileSizeTerrainLayer = null;
            }
            else if (layer.GetType() == typeof(SimpleBlendTerrainLayer))
            {
                mSimpleBlendTerrainLayer = null;
            }
            else if (layer.GetType() == typeof(TileBlockTerrainLayer))
            {
                mTileBlockTerrainLayer = null;
            }
            else if (layer.GetType() == typeof(SplitFogLayer))
            {
                mSplitFogLayer = null;
            }
            else if (layer.GetType() == typeof(GridModelLayer))
            {
                mFrontLayer = null;
            }
            else if (layer.GetType() == typeof(GridModelLayer2))
            {
                mDecorationLayer = null;
            }
            else if (layer.GetType() == typeof(RuntimeDecorationBorderLayer))
            {
                mDecorationBorderLayer = null;
            }
            else if (layer.GetType() == typeof(RiverLayer))
            {
                mRiverLayer = null;
            }
            else if (layer.GetType() == typeof(CircleBorderLayer))
            {
                mCircleBorderLayer = null;
            }
            else if (layer.GetType() == typeof(SplineLayer))
            {
                mSplineLayer = null;
            }
            else if (layer.GetType() == typeof(RuntimeRegionLayer))
            {
                mRegionLayer = null;
            }
            else if (layer.GetType() == typeof(RuntimeRegionColorLayer))
            {
                mRegionColorLayer = null;
            }
            else if (layer.GetType() == typeof(LODLayer))
            {
                mLODLayer = null;
            }
            else if (layer.GetType() == typeof(DynamicObjectLayer))
            {
                mDynamicObjectLayer = null;
            }
            else if (layer.GetType() == typeof(CityTerritoryLayer))
            {
                mCityTerritoryLayer = null;
            }
            else
            {
                Debug.Log($"Erase Unknown Layer {layer.name}");
            }
        }

        public void SetSize(float newWidth, float newHeight)
        {
            mData.Resize(newWidth, newHeight);
        }

        public TransformTable transformTable
        {
            get { return mTransformTable; }
        }

        public FrameActionManager frameActionManager
        {
            get { return mFrameActionManager; }
        }

        public Vector3 center
        {
            get { return new Vector3(mapWidth * 0.5f, 0, mapHeight * 0.5f); }
        }

        public string dataFolder
        {
            get { return mDataFolder; }
            set { mDataFolder = Utils.ConvertToUnityAssetsPath(value); }
        }

        public FakePrefabManager fakePrefabManager
        {
            get { return mFakePrefabManager; }
        }

        public DetailSprites detailSprites
        {
            get { return mDetailSprites; }
        }

        public BlendTerrainLayer blendTerrainLayer
        {
            get { return mBlendTerrainLayer; }
        }

        public VaryingTileSizeTerrainLayer varyingTileSizeTerrainLayer
        {
            get { return mVaryingTileSizeTerrainLayer; }
        }

        public SimpleBlendTerrainLayer simpleBlendTerrainLayer
        {
            get { return mSimpleBlendTerrainLayer; }
        }

        public TileBlockTerrainLayer tileBlockTerrainLayer
        {
            get { return mTileBlockTerrainLayer; }
        }

        public IBlendTerrainLayer activeBlendTerrainLayer
        {
            get
            {
                if (mTileBlockTerrainLayer != null)
                {
                    return mTileBlockTerrainLayer;
                }

                if (mSimpleBlendTerrainLayer != null)
                {
                    return mSimpleBlendTerrainLayer;
                }

                return mBlendTerrainLayer;
            }
        }

        public SplitFogLayer splitFogLayer
        {
            get { return mSplitFogLayer; }
        }

        public GridModelLayer frontLayer
        {
            get { return mFrontLayer; }
        }

        public GridModelLayer2 decorationLayer
        {
            get { return mDecorationLayer; }
        }

        public RuntimeDecorationBorderLayer decorationBorderLayer
        {
            get { return mDecorationBorderLayer; }
        }

        public RiverLayer riverLayer
        {
            get { return mRiverLayer; }
        }

        public SplineLayer splineLayer
        {
            get { return mSplineLayer; }
        }

        public RuntimeRegionLayer regionLayer
        {
            get { return mRegionLayer; }
        }

        public RuntimeRegionColorLayer regionColorLayer
        {
            get { return mRegionColorLayer; }
        }

        public CityTerritoryLayer cityTerritoryLayer
        {
            get { return mCityTerritoryLayer; }
        }

        public CircleBorderLayer circleBorderLayer
        {
            get { return mCircleBorderLayer; }
        }

        public LODLayer lodLayer
        {
            get { return mLODLayer; }
        }

        public DynamicObjectLayer dynamicObjectLayer
        {
            get { return mDynamicObjectLayer; }
        }

        public string name
        {
            get { return mName; }
        }

        public Vector3 origin
        {
            get { return mMapSetting.origin; }
        }

        public MapManager.MapSetting mapSetting
        {
            get { return mMapSetting; }
        }

        public float groundTileSize
        {
            get { return mData.groundTileSize; }
        }

        public float frontTileSize
        {
            get { return mData.frontTileSize; }
        }

        public event System.Action<float> ZoomChangeEvent;

        public event System.Action<string, int> MapLayerLODChangeEvent;

        //地图上有物体被加载出来了
        //string: MapLayerName
        //int: object id
        public event System.Action<MapLayerData, int, GameObject> MapObjectLoadEvent;

        //地图上某个物体被卸载了
        public event System.Action<MapLayerData, int, GameObject> MapObjectUnloadEvent;

        //地图的数据
        protected MapData mData;

        //地图的视图
        protected MapView mView;

        //地图层
        List<MapLayerBase> mMapLayers = new List<MapLayerBase>();

        //地图事件监听器
        MapEventListener mMapEventListener;

        //地图内部使用的消息队列
        GameMessageQueue mMsgQueue = new GameMessageQueue();

        //管理地图物体的对象
        MapObjectManager mObjectManager = new MapObjectManager();

        //注册物体的可见性事件
        CullManager mCullManager = new CullManager();

        //地图物体加载队列
        ModelLoadingTaskManager mLoadingTaskManager = new ModelLoadingTaskManager();
        FrameActionManager mFrameActionManager;
        bool mIsEditorMap;

        protected string mDataFolder = "";

        //如果viewport被扩展后,返回扩展的viewport
        public Rect viewport;

        //相机视野的viewport
        public Rect originalViewport;

        //当相机视角切换到水平后,需要改变视野的计算方式
        ViewportUpdateSetting mViewportUpdateSetting = new ViewportUpdateSetting();
        TransformTable mTransformTable;
        FakePrefabManager mFakePrefabManager = new FakePrefabManager();
        DetailSprites mDetailSprites;
        BlendTerrainLayer mBlendTerrainLayer;
        SimpleBlendTerrainLayer mSimpleBlendTerrainLayer;
        VaryingTileSizeTerrainLayer mVaryingTileSizeTerrainLayer;
        TileBlockTerrainLayer mTileBlockTerrainLayer;
        SplitFogLayer mSplitFogLayer;
        DynamicObjectLayer mDynamicObjectLayer;
        CityTerritoryLayer mCityTerritoryLayer;
        GridModelLayer mFrontLayer;
        GridModelLayer2 mDecorationLayer;
        RuntimeDecorationBorderLayer mDecorationBorderLayer;
        RiverLayer mRiverLayer;
        SplineLayer mSplineLayer;
        RuntimeRegionLayer mRegionLayer;
        RuntimeRegionColorLayer mRegionColorLayer;
        CircleBorderLayer mCircleBorderLayer;
        LODLayer mLODLayer;
        string mName;
        MapManager.MapSetting mMapSetting;
    }
}
