// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

using System;

namespace AmplifyShaderEditor
{
	[Serializable]
	[NodeAttributes( "Abs", "Math Operators", "Absolute value of scalars and vectors" )]
	public sealed class AbsOpNode : SingleInputOp
	{
		protected override void CommonInit( int uniqueId )
		{
			base.CommonInit( uniqueId );
			m_opName = "abs";
			m_previewShaderGUID = "cd6d6dfa3df214a479f68a490e177db6";
			m_inputPorts[ 0 ].CreatePortRestrictions(	WirePortDataType.OBJECT,
														WirePortDataType.FLOAT, 
														WirePortDataType.FLOAT2,
														WirePortDataType.FLOAT3,
														WirePortDataType.FLOAT4,
														WirePortDataType.COLOR,
														WirePortDataType.INT);
		}
	}
}
