﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [ExecuteInEditMode]
    [Black]
    public class NavMeshLayerLogic : MapLayerLogic
    {
        public float radius = 0;
        public float minimumAngle = 100.0f;
        public float maximumArea = 180000f;
        public NavigationCreateMode mode = (Map.currentMap as EditorMap).navMeshMode;
        List<KeyValuePair<MapLayerBase, bool>> mValidMapLayers = new List<KeyValuePair<MapLayerBase, bool>>();
        Dictionary<int, bool> mLayerSelectionState = new Dictionary<int, bool>();

        public List<KeyValuePair<MapLayerBase, bool>> validMapLayers { get { return mValidMapLayers; } }

        public NavMeshLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as NavMeshLayer;
                return layer;
            }
        }

        public void BeginAdd()
        {
            mValidMapLayers.Clear();
        }

        public void AddValidMapLayer(MapLayerBase layer)
        {
            bool selected = IsSelected(layer.id);
            mValidMapLayers.Add(new KeyValuePair<MapLayerBase, bool>(layer, selected));
        }

        bool IsSelected(int layerID)
        {
            bool selected;
            mLayerSelectionState.TryGetValue(layerID, out selected);
            return selected;
        }

        public void SetSelection(int layerID, bool selection)
        {
            mLayerSelectionState[layerID] = selection;
        }

        public static void ExportNavMesh(NavMeshLayer layer, string folder)
        {
            if (string.IsNullOrEmpty(folder))
            {
                return;
            }

            var navMeshCount = layer.layerData.navMeshCount;
            if (navMeshCount > 0)
            {
                for (int i = 0; i < navMeshCount; ++i)
                {
                    var meshDatas = layer.layerData.navMeshDatas;

                    var triangleTypeSettings = Utils.CreateTriangleTypeSettings(MapCoreDef.MAP_MAX_REGION_TYPE_ID, meshDatas[0].triangleTypes, meshDatas[0].triangleStates);

                    var path = JSONExporter.ExportNavMesh("nav_mesh", folder, meshDatas[0].vertices, meshDatas[0].indices, meshDatas[0].triangleTypes, triangleTypeSettings);
                    if (!string.IsNullOrEmpty(path))
                    {
                        var pathName = Path.ChangeExtension(path, "obj");
                        OBJExporter.Export(pathName, meshDatas[0].vertices, null, null, meshDatas[0].indices);
                    }

                    ExportRuntimeNavMeshData1(meshDatas[0].vertices, meshDatas[0].indices, meshDatas[0].triangleTypes, triangleTypeSettings);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "No NavMesh", "OK");
            }
        }


        static void ExportRuntimeNavMeshData1(Vector3[] vertices, int[] indices, ushort[] triangleTypes, Nav.TriangleTypeSetting[] typeSettings)
        {
            string path = MapCoreDef.GetNavMeshDataFilePath(SLGMakerEditor.instance.exportFolder);
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.NavMeshVersion.majorVersion);
            writer.Write(VersionSetting.NavMeshVersion.minorVersion);

            writer.Write(Map.currentMap.mapWidth);
            writer.Write(Map.currentMap.mapHeight);

            int indexCount = indices.Length;
            writer.Write(indexCount);
            for (int i = 0; i < indexCount; ++i)
            {
                writer.Write(indices[i]);
            }

            int vertexCount = vertices.Length;
            writer.Write(vertexCount);
            for (int i = 0; i < vertexCount; ++i)
            {
                writer.Write(Utils.F2I(vertices[i].x));
                writer.Write(Utils.F2I(vertices[i].z));
            }

            //triangle types
            if (triangleTypes == null)
            {
                triangleTypes = new ushort[indices.Length / 3];
            }
            for (int i = 0; i < triangleTypes.Length; ++i)
            {
                writer.Write(triangleTypes[i]);
            }

            //type settings
            int typeCount = 0;
            if (typeSettings != null)
            {
                typeCount = typeSettings.Length;
            }
            writer.Write(typeCount);
            for (int i = 0; i < typeCount; ++i)
            {
                bool valid = typeSettings[i] != null;
                writer.Write(valid);
                if (valid)
                {
                    writer.Write(typeSettings[i].typeID);
                    writer.Write(typeSettings[i].walkable);
                }
            }

            //export regions
            ExportRegions(writer, indices, triangleTypes);

            var data = stream.ToArray();
            File.WriteAllBytes(path, data);

            writer.Close();
        }

        //注意,导出的区域三角形都是按区域导出的,不应该出现不同区域三角形穿插的情况
        static void ExportRegions(BinaryWriter writer, int[] triangleIndices, ushort[] triangleTypes)
        {
            List<int> regionEndIndex;
            List<int> regionIDs;
            Utils.CreateRegions(triangleIndices, triangleTypes, out regionEndIndex, out regionIDs);

            int regionCount = regionEndIndex.Count;
            writer.Write(regionCount);
            for (int i = 0; i < regionCount; ++i)
            {
                writer.Write(regionEndIndex[i]);
                writer.Write(regionIDs[i]);
            }
        }
    }
}


#endif