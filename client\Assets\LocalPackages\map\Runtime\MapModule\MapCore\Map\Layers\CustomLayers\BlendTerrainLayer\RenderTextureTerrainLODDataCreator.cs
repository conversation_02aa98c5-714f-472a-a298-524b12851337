﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
using System.IO;
#endif

namespace TFW.Map
{
    [System.Flags]
    public enum TextureMapLayerMask
    {
        None = 0,
        FrontLayer = 1,
        DecorationLayer = 2,
        RiverLayer = 4,
        DecorationBorderLayer = 8,
    }

    //地表每一级生成lod的设置
    public class TerrainRenderTextureLODSetting
    {
        public TerrainRenderTextureLODSetting(float height, int s, Material mtl)
        {
            cameraHeight = height;
            blockSize = s;
            material = mtl;
            realBlockSize = 0;
        }

        //实际使用的180mtile的数量
        public int realBlockSize { set; get; }

        //相机高度
        public float cameraHeight;
        //相对于上一级的块大小
        public int blockSize;
        //使用哪个材质
        public Material material;
    }

    //lod中每个tile的数据
    public class TerrainLODTileData
    {
        public TerrainLODTileData(Vector3 pos, int index)
        {
            position = pos;
            prefabIndex = index;
        }

        //这个tile使用的prefab的index
        public int prefabIndex;
        //中心点坐标
        public Vector3 position;
    }

    //生成地表lod
    public class RenderTextureTerrainLODDataCreator
    {
        /*
        * var setting = new TerrainRenderTextureLODSetting[] 
        * {
        *      new TerrainRenderTextureLODSetting(300, 5, 5),
        *      //block大小是相对于上一层级的,这里2的意思是2x5
        *      new TerrainRenderTextureLODSetting(1000, 2, 10),
        * }
        * CreateLODDatas(setting)
        * 
        */
        /*
         * layerData:地表层
         * assetFolder:生成文件的目录
         * lods:地表lod设置
         */
        public void CreateLODDatas(IBlendTerrainLayer layerData, TerrainRenderTextureLODSetting[] lods, string assetFolder, Camera camera, int textureSize, TextureMapLayerMask layerMask, Shader defaultShader, string texturePropertyName, bool generateMeshOBJ, bool wrongLODOrientation)
        {
#if UNITY_EDITOR
            if (!layerData.supportGeneratingLOD)
            {
                EditorUtility.DisplayDialog("Erorr", "Can't generate lod for this terrain layer type!", "OK");
                return;
            }
#endif
            Shader.SetGlobalFloat("_ToneMappingMin", 0);
            Shader.SetGlobalFloat("_ToneMappingMax", 1);
            Shader.SetGlobalColor("_GlobalDayNightColor", new Color(1, 1, 1, 1));

            //计算每一层lod实际使用的tile数量
            for (int i = 0; i < lods.Length; ++i)
            {
                if (i > 0)
                {
                    lods[i].realBlockSize = lods[i].blockSize * lods[i - 1].realBlockSize;
                }
                else
                {
                    lods[i].realBlockSize = lods[i].blockSize;
                }
            }

            //检测layer是否满足生成的条件
            bool isValid = CheckValidation(layerData, lods);
            if (!isValid)
            {
#if UNITY_EDITOR
                EditorUtility.DisplayDialog("Error", "Invalid terrain layer!", "OK");
#endif
                return;
            }

#if UNITY_EDITOR
            if (!Directory.Exists(assetFolder))
            {
                Directory.CreateDirectory(assetFolder);
            }
#endif
            mLODSettings = lods;
            Debug.Assert(mOtherLODTiles == null);
            mOtherLODTiles = new List<TerrainLODTileData[,]>();

            for (int i = 0; i < lods.Length; ++i)
            {
                //生成额外的lod数据
                CreateOtherDataAtHeight(layerData, lods[i].cameraHeight, i, lods[i].blockSize, assetFolder, camera, lods, textureSize, lods[i].material, layerMask, defaultShader, texturePropertyName, generateMeshOBJ);
            }

            //保存生成的数据
            SaveNewVersion(assetFolder, wrongLODOrientation);
        }

        //这里需要考虑layer的tile数量是否满足lod生成设置的参数,例如是否可以整除等
        bool CheckValidation(IBlendTerrainLayer layerData, TerrainRenderTextureLODSetting[] lodSettings)
        {
            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            if (rows != cols)
            {
                return false;
            }
            //for (int i = 0; i < lodSettings.Length; ++i)
            //{
            //    if (rows % lodSettings[i].realBlockSize != 0)
            //    {
            //        return false;
            //    }
            //}

            return true;
        }

        /*生成lod数据
         * lod: lod设置的索引
         */
        void CreateOtherDataAtHeight(IBlendTerrainLayer layer, float cameraHeight, int lod, int blockSize, string assetFolder, Camera camera, TerrainRenderTextureLODSetting[] lodSettings, int textureSize, Material mtl, TextureMapLayerMask layerMask, Shader defaultShader, string texturePropertyName, bool generateMeshOBJ)
        {
#if UNITY_EDITOR
            //该层实际的block大小
            int realBlockSize = lodSettings[lod].realBlockSize;
            //计算这一层lod需要多少个tile
            int horizontalTileCountInThisLOD = layer.horizontalTileCount / realBlockSize;
            int verticalTileCountInThisLOD = layer.verticalTileCount / realBlockSize;

            var lodTiles = new TerrainLODTileData[horizontalTileCountInThisLOD, verticalTileCountInThisLOD];
            mOtherLODTiles.Add(lodTiles);

            Vector3 blockPos;
            int idx = 0;
            for (int i = 0; i < verticalTileCountInThisLOD; ++i)
            {
                for (int j = 0; j < horizontalTileCountInThisLOD; ++j)
                {
                    EditorUtility.DisplayProgressBar($"Baking LOD {lod}...", $"Processing tile {j}_{i}", 1f * (idx + 1) / (horizontalTileCountInThisLOD * verticalTileCountInThisLOD));

                    int objectID = Map.currentMap.nextCustomObjectID;
                    //生成这一层这个lod tile需要的model template
                    var modelTemplateIndex = CreateModelTemplate(layer, cameraHeight, objectID, lod - 1, j, i, blockSize, assetFolder, camera, lodSettings, textureSize, mtl, layerMask, defaultShader, texturePropertyName, generateMeshOBJ, out blockPos);
                    if (modelTemplateIndex >= 0)
                    {
                        TerrainLODTileData tileData = null;
                        if (lod == 0)
                        {
                            var pos = CalculateLODTilePosition(layer, j, i, realBlockSize);
                            tileData = new TerrainLODTileData(pos, modelTemplateIndex);
                        }
                        else
                        {
                            tileData = new TerrainLODTileData(blockPos, modelTemplateIndex);
                        }
                        lodTiles[i, j] = tileData;
                    }

                    ++idx;
                }
            }

            EditorUtility.ClearProgressBar();
#endif
        }

        //创建在lod下x,y lod tile使用的model template
        //blockPos: tile的坐标
        int CreateModelTemplate(IBlendTerrainLayer layer, float cameraHeight, int objectDataID, int lastLOD, int x, int y, int blockSize, string assetFolder, Camera camera, TerrainRenderTextureLODSetting[] lodSettings, int textureSize, Material mtl, TextureMapLayerMask layerMask, Shader defaultShader, string texturePropertyName, bool generateMeshOBJ, out Vector3 blockPos)
        {
            Debug.Assert(defaultShader != null);
            blockPos = Vector3.zero;
#if UNITY_EDITOR
            Bounds bounds = new Bounds();
            string tilePrefix = string.Format("LOD_{0}_Tile_{1}_{2}", lastLOD + 1, x, y);
            string prefabPath = "";
            RenderObjectToTexture rt = new RenderObjectToTexture(camera, null);
            //create combined tiles
            int realBlockSize = lodSettings[lastLOD + 1].realBlockSize;
            var root = new GameObject(string.Format("LOD {0} Tile ({1},{2})", lastLOD + 1, x, y));
            if (lastLOD < 0)
            {
                //terrain layer
                //使用地表的实际tile数据
                //根据相机高度得到地表当前的lod
                int layerLOD = layer.GetLODFromCameraHeight(cameraHeight);
                var simpleLayer = Map.currentMap.simpleBlendTerrainLayer;
                if (simpleLayer == null)
                {
                    OptimizedMeshCombiner combiner = new OptimizedMeshCombiner(0.001f);
                    for (int i = 0; i < blockSize; ++i)
                    {
                        for (int j = 0; j < blockSize; ++j)
                        {
                            int gx = j + x * blockSize;
                            int gy = i + y * blockSize;

                            string tilePrefabPath = layer.GetTilePrefabPath(gx, gy, layerLOD);
                            if (tilePrefabPath != null)
                            {
                                var pos = layer.FromCoordinateToWorldPosition(j, i);
                                var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, pos, Vector3.one, Quaternion.identity);
                                tileObject.name = tilePrefabPath;
                                tileObject.transform.parent = root.transform;
                            }
                        }
                    }
                }
                else
                {
                    var simpleLayerData = simpleLayer.layerData as SimpleBlendTerrainLayerData;
                    var tiles = simpleLayerData.tiles;
                    var prefabPaths = simpleLayerData.tilePrefabPaths;
                    int cols = simpleLayer.horizontalTileCount;
                    OptimizedMeshCombiner combiner = new OptimizedMeshCombiner(0.001f);
                    for (int i = 0; i < blockSize; ++i)
                    {
                        for (int j = 0; j < blockSize; ++j)
                        {
                            int gx = j + x * blockSize;
                            int gy = i + y * blockSize;

                            var idx = gy * cols + gx;
                            string tilePrefabPath = prefabPaths[tiles[idx]];
                            if (tilePrefabPath != null)
                            {
                                var pos = layer.FromCoordinateToWorldPosition(j, i);
                                var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, pos, Vector3.one, Quaternion.identity);
                                tileObject.name = tilePrefabPath;
                                tileObject.transform.parent = root.transform;

                                if (generateMeshOBJ)
                                {
                                    var filter = tileObject.GetComponentInChildren<MeshFilter>();
                                    if (filter != null)
                                    {
                                        var tileMesh = filter.sharedMesh;
                                        combiner.AddMesh(tileMesh.vertices, tileMesh.triangles, tileObject.transform.localToWorldMatrix);
                                    }
                                }
                            }
                        }
                    }

                    if (generateMeshOBJ)
                    {
                        Vector3[] combinedVertices;
                        int[] combinedIndices;
                        combiner.Combine(true, out combinedVertices, out combinedIndices);
                        float boundsWidth = blockSize * layer.layerData.tileWidth;
                        float boundsHeight = blockSize * layer.layerData.tileHeight;
                        Vector2[] uvs = new Vector2[combinedVertices.Length];

                        float scale = layer.tileWidth * realBlockSize;
                        bool convertToRightHandedSystem = true;
                        for (int k = 0; k < uvs.Length; ++k)
                        {
                            float u = combinedVertices[k].x / boundsWidth;
                            float v = combinedVertices[k].z / boundsHeight;
                            uvs[k] = new Vector2(u, v);
                            //obj use right handed system, convert to left handed system for unity
                            combinedVertices[k] -= new Vector3(scale * 0.5f, 0, scale * 0.5f);
                            if (convertToRightHandedSystem)
                            {
                                combinedVertices[k].x = -combinedVertices[k].x;
                            }
                        }

                        if (convertToRightHandedSystem)
                        {
                            int triangleCount = combinedIndices.Length / 3;
                            for (int i = 0; i < triangleCount; ++i)
                            {
                                int t = combinedIndices[i * 3];
                                combinedIndices[i * 3] = combinedIndices[i * 3 + 2];
                                combinedIndices[i * 3 + 2] = t;
                            }
                        }

                        string meshPath = string.Format("{0}/{1}.obj", assetFolder, tilePrefix);
                        OBJExporter.Export(meshPath, combinedVertices, uvs, null, combinedIndices);

#if false
                    //temp code
                    BigMeshViewer bigMesh = new BigMeshViewer();
                    bigMesh.Create(null, meshPath, combinedVertices, combinedIndices, false, Color.yellow);
#endif
                    }
                }

                bool calculateBounds = true;

                if (layerMask.HasFlag(TextureMapLayerMask.FrontLayer))
                {
                    calculateBounds = true;
                    AddFrontLayerTiles(x, y, blockSize, cameraHeight, root);
                }

                if (layerMask.HasFlag(TextureMapLayerMask.RiverLayer))
                {
                    calculateBounds = true;
                    AddRiverLayerTiles(x, y, blockSize, cameraHeight, root, layer);
                }

                if (layerMask.HasFlag(TextureMapLayerMask.DecorationLayer))
                {
                    calculateBounds = true;
                    AddDecorationLayerTiles(x, y, blockSize, cameraHeight, root, layer);
                }

                if (layerMask.HasFlag(TextureMapLayerMask.DecorationBorderLayer))
                {
                    calculateBounds = true;
                    AddDecorationBorderLayerTiles(x, y, blockSize, cameraHeight, root, layer);
                }

                if (calculateBounds)
                {
                    bounds = CalculateBounds(blockSize, layer.layerData);
                }
            }
            else
            {
                //使用上一级的lod生成数据来生成新的lod的tile
                var tiles = mOtherLODTiles[lastLOD];
                //remember to add layer offset!
                var blockStartPos = new Vector3(layer.tileWidth * realBlockSize * x, 0, layer.tileHeight * realBlockSize * y) + layer.layerOffset;
                blockPos = blockStartPos + new Vector3(layer.tileWidth * realBlockSize * 0.5f, 0, layer.tileHeight * realBlockSize * 0.5f);
                for (int i = 0; i < blockSize; ++i)
                {
                    for (int j = 0; j < blockSize; ++j)
                    {
                        int gx = j + x * blockSize;
                        int gy = i + y * blockSize;
                        var tile = tiles[gy, gx];
                        if (tile != null)
                        {
                            string tilePrefabPath = mUsedPrefabs[tile.prefabIndex];

                            var tilePos = blockStartPos + new Vector3(layer.tileWidth * lodSettings[lastLOD].realBlockSize * j, 0, layer.tileHeight * lodSettings[lastLOD].realBlockSize * i);
                            var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, tilePos, Vector3.one, Quaternion.identity);
                            tileObject.name = tilePrefabPath;
                            tileObject.transform.parent = root.transform;
                        }
                    }
                }
            }

            int n = root.transform.childCount;
            //if (n > 0)
            {
                //render block tiles into texture
                rt.textureSize = textureSize;
                rt.Render(root, false, cameraHeight, null, false, false, bounds);
                string texturePath = string.Format("{0}/{1}.tga", assetFolder, tilePrefix);
                //save texture
                rt.SaveTexture(rt.renderTexture, texturePath, false);
                AssetDatabase.Refresh();

                for (int i = n - 1; i >= 0; --i)
                {
                    var child = root.transform.GetChild(i).gameObject;
                    Map.currentMap.view.reusableGameObjectPool.Release(child.name, child, Map.currentMap);
                }

                float scale = layer.tileWidth * realBlockSize;
                var planePrefab = GameObject.CreatePrimitive(PrimitiveType.Quad);

                //create quad mesh asset
                string meshPath = string.Format("{0}/{1}.asset", assetFolder, tilePrefix);
                var mesh = CreateQuadMeshAsset(meshPath, scale);
                if (generateMeshOBJ)
                {
                    string objPath = string.Format("{0}/{1}.obj", assetFolder, tilePrefix);
                    //flip uv
                    var uv = mesh.uv;
                    for (int i = 0; i < uv.Length; ++i)
                    {
                        uv[i].x = 1 - uv[i].x;
                    }
                    OBJExporter.Export(objPath, mesh.vertices, uv, null, mesh.triangles);
                }
                var parent = new GameObject("lod tile root");
                planePrefab.transform.parent = parent.transform;
                var collider = planePrefab.GetComponent<Collider>();
                GameObject.DestroyImmediate(collider);
                var filter = planePrefab.GetComponent<MeshFilter>();
                filter.sharedMesh = mesh;
                var meshRenderer = planePrefab.GetComponent<MeshRenderer>();
                bool createMtl = false;
                if (mtl == null)
                {
                    createMtl = true;
                    mtl = new Material(defaultShader);
                }
                //create material assete
                if (createMtl)
                {
                    string mtlPath = string.Format("{0}/{1}.mat", assetFolder, tilePrefix);
                    AssetDatabase.CreateAsset(mtl, mtlPath);
                }

                var texture = MapModuleResourceMgr.LoadTexture(texturePath);
                texture.wrapMode = TextureWrapMode.Clamp;
                mtl.SetTexture(texturePropertyName, texture);
                meshRenderer.sharedMaterial = mtl;

                bool success;
                prefabPath = string.Format("{0}/{1}.prefab", assetFolder, tilePrefix);
                //create prefab asset
                PrefabUtility.SaveAsPrefabAsset(parent, prefabPath, out success);
                Debug.Assert(success, "Create prefab failed!");

                GameObject.DestroyImmediate(parent);

                mUsedPrefabs.Add(prefabPath);

                Debug.Log("Create Prefab " + prefabPath);
            }

            GameObject.DestroyImmediate(root);
            rt.OnDestroy(true);

            return n == 0 ? -1 : mUsedPrefabs.Count - 1;
#else
            return 0;
#endif
        }

        Bounds CalculateBounds(int blockSize, MapLayerData layerData)
        {
            var bounds = new Bounds();
            float boundsWidth = blockSize * layerData.tileWidth;
            float boundsHeight = blockSize * layerData.tileHeight;
            var boundsMin = layerData.layerOffset;
            var boundsMax = boundsMin + new Vector3(boundsWidth, 0, boundsHeight);
            bounds.SetMinMax(boundsMin, boundsMax);
            return bounds;
        }

        void AddFrontLayerTiles(int x, int y, int blockSize, float cameraHeight, GameObject root)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as GridModelLayer;
            if (layer != null)
            {
                var layerData = layer.layerData;
                int layerLOD = layerData.GetLODFromCameraHeight(cameraHeight);
                for (int i = 0; i < blockSize; ++i)
                {
                    for (int j = 0; j < blockSize; ++j)
                    {
                        int gx = j + x * blockSize;
                        int gy = i + y * blockSize;
                        var tile = layerData.GetBigTileData(gx, gy);
                        if (tile != null)
                        {
                            var pos = layerData.FromCoordinateToWorldPositionCenter(j, i);
                            string tilePrefabPath = tile.objectsOfEachLOD[layerLOD].originalPrefabPath;
                            var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, pos, Vector3.one, Quaternion.identity);
                            tileObject.name = tilePrefabPath;
                            tileObject.transform.parent = root.transform;
                        }
                    }
                }
            }
        }

        void AddRiverLayerTiles(int x, int y, int blockSize, float cameraHeight, GameObject root, IBlendTerrainLayer terrainLayerData)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_RUNTIME_RIVER) as RiverLayer;
            if (layer != null)
            {
                var layerData1 = layer.layerData;
                int layerLOD = layerData1.GetLODFromCameraHeight(cameraHeight);
                List<IMapObjectData> riversInBounds = new List<IMapObjectData>();
                var localBounds = CalculateBounds(blockSize, terrainLayerData.layerData);
                var offset = new Vector3(x * blockSize * terrainLayerData.tileWidth, 0, y * blockSize * terrainLayerData.tileHeight);
                Bounds worldBounds = new Bounds();
                worldBounds.SetMinMax(localBounds.min + offset, localBounds.max + offset);
                layerData1.GetObjectInBounds(worldBounds, riversInBounds);
                for (int i = 0; i < riversInBounds.Count; ++i)
                {
                    //convert to pos in tile
                    var posInTile = riversInBounds[i].GetPosition() - offset;
                    string tilePrefabPath = riversInBounds[i].GetAssetPath(layerLOD);
                    var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, posInTile, riversInBounds[i].GetScale(), riversInBounds[i].GetRotation());
                    tileObject.name = tilePrefabPath;
                    tileObject.transform.parent = root.transform;
                }
            }
        }

        void AddDecorationLayerTiles(int x, int y, int blockSize, float cameraHeight, GameObject root, IBlendTerrainLayer terrainLayerData)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as GridModelLayer2;
            if (layer != null)
            {
                var layerData1 = layer.layerData;
                int layerLOD = layerData1.GetLODFromCameraHeight(cameraHeight);

                var localBounds = CalculateBounds(blockSize, terrainLayerData.layerData);
                var combineTileWidth = blockSize * terrainLayerData.tileWidth;
                var combineTileHeight = blockSize * terrainLayerData.tileHeight;
                var offset = new Vector3(x * combineTileWidth, 0, y * combineTileHeight);
                Bounds worldBounds = new Bounds();
                worldBounds.SetMinMax(localBounds.min + offset, localBounds.max + offset);

                var min = worldBounds.min;
                var max = worldBounds.max;
                var minCoord = layerData1.FromWorldPositionToCoordinate(min);
                var maxCoord = layerData1.FromWorldPositionToCoordinate(max);

                HashSet<int> addedObjects = new HashSet<int>();
                for (int yy = minCoord.y; yy <= maxCoord.y; ++yy)
                {
                    for (int xx = minCoord.x; xx <= maxCoord.x; ++xx)
                    {
                        if (xx >= 0 && xx < layerData1.horizontalTileCount && yy >= 0 && yy < layerData1.verticalTileCount)
                        {
                            var tileData = layerData1.GetBigTileData(xx, yy);
                            if (tileData != null)
                            {
                                //convert to pos in tile
                                var lodObjects = tileData.objectsOfEachLOD[layerLOD];
                                for (int k = 0; k < lodObjects.Count; ++k)
                                {
                                    if (!addedObjects.Contains(lodObjects[k].viewID))
                                    {
                                        addedObjects.Add(lodObjects[k].viewID);
                                        var objWorldPos = lodObjects[k].GetPosition(layerData1.prefabInfos) + new Vector3(xx * layerData1.tileWidth, 0, yy * layerData1.tileHeight);
                                        var posInTile = objWorldPos - offset;
                                        var rotation = lodObjects[k].GetRotation(layerData1.prefabInfos);
                                        var scale = lodObjects[k].GetScale(layerData1.prefabInfos);
                                        string tilePrefabPath = layerData1.prefabInfos[lodObjects[k].prefabInitInfoIndex].prefabPathForEachCustomType[0];
                                        var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, posInTile, scale, rotation);
                                        tileObject.name = tilePrefabPath;
                                        tileObject.transform.SetParent(root.transform, false);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        void AddDecorationBorderLayerTiles(int x, int y, int blockSize, float cameraHeight, GameObject root, IBlendTerrainLayer terrainLayerData)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER) as RuntimeDecorationBorderLayer;
            if (layer != null)
            {
                var layerData1 = layer.layerData;
                int layerLOD = layerData1.GetLODFromCameraHeight(cameraHeight);

                var localBounds = CalculateBounds(blockSize, terrainLayerData.layerData);
                var combineTileWidth = blockSize * terrainLayerData.tileWidth;
                var combineTileHeight = blockSize * terrainLayerData.tileHeight;
                var offset = new Vector3(x * combineTileWidth, 0, y * combineTileHeight);
                Bounds worldBounds = new Bounds();
                worldBounds.SetMinMax(localBounds.min + offset, localBounds.max + offset);

                var min = worldBounds.min;
                var max = worldBounds.max;
                var minCoord = layerData1.FromWorldPositionToCoordinate(min);
                var maxCoord = layerData1.FromWorldPositionToCoordinate(max);

                HashSet<int> addedObjects = new HashSet<int>();
                for (int yy = minCoord.y; yy <= maxCoord.y; ++yy)
                {
                    for (int xx = minCoord.x; xx <= maxCoord.x; ++xx)
                    {
                        if (xx >= 0 && xx < layerData1.horizontalTileCount && yy >= 0 && yy < layerData1.verticalTileCount)
                        {
                            var tileData = layerData1.GetBigTileData(xx, yy);
                            if (tileData != null)
                            {
                                //convert to pos in tile
                                var lodObjects = tileData.objectsOfEachLOD[layerLOD];
                                for (int k = 0; k < lodObjects.Count; ++k)
                                {
                                    if (!addedObjects.Contains(lodObjects[k].viewID))
                                    {
                                        addedObjects.Add(lodObjects[k].viewID);
                                        var objWorldPos = lodObjects[k].GetPosition(layerData1.prefabInfos) + new Vector3(xx * layerData1.tileWidth, 0, yy * layerData1.tileHeight);
                                        var posInTile = objWorldPos - offset;
                                        var rotation = lodObjects[k].GetRotation(layerData1.prefabInfos);
                                        var scale = lodObjects[k].GetScale(layerData1.prefabInfos);
                                        string tilePrefabPath = layerData1.prefabInfos[lodObjects[k].prefabInitInfoIndex].prefabPathForEachCustomType[0];
                                        var tileObject = Map.currentMap.view.reusableGameObjectPool.Require(tilePrefabPath, posInTile, scale, rotation);
                                        tileObject.name = tilePrefabPath;
                                        tileObject.transform.SetParent(root.transform, false);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Vector3 CalculateLODTilePosition(IBlendTerrainLayer layer, int x, int y, int size)
        {
            var startPos = layer.FromCoordinateToWorldPosition(x * size, y * size);
            var endPos = layer.FromCoordinateToWorldPosition(x * size + size, y * size + size);
            return (startPos + endPos) * 0.5f;
        }

        void Save(string path)
        {
#if UNITY_EDITOR
            if (mOtherLODTiles != null)
            {
                var dataPath = path + "/" + "terrain_lod_data.bytes";
                using MemoryStream stream = new MemoryStream();
                using BinaryWriter writer = new BinaryWriter(stream);

                //save prefabs
                writer.Write(mUsedPrefabs.Count);
                for (int i = 0; i < mUsedPrefabs.Count; ++i)
                {
                    Utils.WriteString(writer, mUsedPrefabs[i]);
                }

                //save lod tile data
                writer.Write(mOtherLODTiles.Count);
                for (int i = 0; i < mOtherLODTiles.Count; ++i)
                {
                    int rows = mOtherLODTiles[i].GetLength(0);
                    int cols = mOtherLODTiles[i].GetLength(1);
                    writer.Write(rows);
                    writer.Write(cols);
                    for (int r = 0; r < rows; ++r)
                    {
                        for (int c = 0; c < cols; ++c)
                        {
                            var tileData = mOtherLODTiles[i][r, c];
                            if (tileData != null)
                            {
                                Utils.WriteVector3(writer, tileData.position);
                                writer.Write(tileData.prefabIndex);
                            }
                            else
                            {
                                Utils.WriteVector3(writer, Vector3.zero);
                                writer.Write(0);
                            }
                        }
                    }
                }

                //save lod settings
                writer.Write(mLODSettings.Length);
                for (int i = 0; i < mLODSettings.Length; ++i)
                {
                    writer.Write(mLODSettings[i].cameraHeight);
                    writer.Write(mLODSettings[i].blockSize);
                    writer.Write(mLODSettings[i].realBlockSize);
                }

                var data = stream.ToArray();
                File.WriteAllBytes(dataPath, data);

                writer.Close();

                AssetDatabase.SaveAssets();

                AssetDatabase.Refresh();
            }
#endif
        }

        void SaveNewVersion(string path, bool wrongLODOrientation)
        {
#if UNITY_EDITOR
            if (mOtherLODTiles != null)
            {
                var dataPath = path + "/" + "terrain_lod_data_ex.bytes";
                using MemoryStream stream = new MemoryStream();
                using BinaryWriter writer = new BinaryWriter(stream);

                writer.Write(VersionSetting.TerrainLODDataVersion);

                writer.Write(wrongLODOrientation);

                //save prefabs
                writer.Write(mUsedPrefabs.Count);
                for (int i = 0; i < mUsedPrefabs.Count; ++i)
                {
                    Utils.WriteString(writer, mUsedPrefabs[i]);
                }

                //save lod tile data
                writer.Write(mOtherLODTiles.Count);
                for (int i = 0; i < mOtherLODTiles.Count; ++i)
                {
                    int rows = mOtherLODTiles[i].GetLength(0);
                    int cols = mOtherLODTiles[i].GetLength(1);
                    writer.Write(rows);
                    writer.Write(cols);
                    for (int r = 0; r < rows; ++r)
                    {
                        for (int c = 0; c < cols; ++c)
                        {
                            var tileData = mOtherLODTiles[i][r, c];
                            if (tileData != null)
                            {
                                Utils.WriteVector3(writer, tileData.position);
                                writer.Write(tileData.prefabIndex);
                            }
                            else
                            {
                                Utils.WriteVector3(writer, Vector3.zero);
                                writer.Write(-1);
                            }
                        }
                    }
                }

                //save lod settings
                writer.Write(mLODSettings.Length);
                for (int i = 0; i < mLODSettings.Length; ++i)
                {
                    writer.Write(mLODSettings[i].cameraHeight);
                    writer.Write(mLODSettings[i].blockSize);
                    writer.Write(mLODSettings[i].realBlockSize);
                }

                var data = stream.ToArray();
                File.WriteAllBytes(dataPath, data);

                writer.Close();

                AssetDatabase.SaveAssets();

                AssetDatabase.Refresh();
            }
#endif
        }

        Mesh CreateQuadMeshAsset(string assetPath, float size)
        {
            float hs = size * 0.5f;
            Mesh mesh = new Mesh();
            mesh.vertices = new Vector3[4]
            {
                new Vector3(-hs, 0, -hs),
                new Vector3(-hs, 0, hs),
                new Vector3(hs, 0, hs),
                new Vector3(hs, 0, -hs),
            };
            mesh.uv = new Vector2[4]
            {
                new Vector2(0, 0),
                new Vector2(0, 1),
                new Vector2(1, 1),
                new Vector2(1, 0),
            };


            mesh.triangles = new int[6]
            {
                0,1,2,0,2,3,
            };

#if UNITY_EDITOR
            AssetDatabase.CreateAsset(mesh, assetPath);
#endif
            return mesh;
        }

        List<string> mUsedPrefabs = new List<string>();
        List<TerrainLODTileData[,]> mOtherLODTiles;
        TerrainRenderTextureLODSetting[] mLODSettings;
    }
}
