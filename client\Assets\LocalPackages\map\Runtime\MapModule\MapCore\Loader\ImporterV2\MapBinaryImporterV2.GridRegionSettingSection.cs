﻿ 



 
 


using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        void LoadGridRegionSetting(BinaryReader reader)
        {
            reader.BaseStream.Position = GetSectionDataStartPosition(MapDataSectionType.GridRegionSetting);
            int version = reader.ReadInt32();

            //--------------------version 1 start--------------------------
            bool hasRegionData = reader.ReadBoolean();
            if (hasRegionData)
            {
                mEditorData.gridRegionSetting = new config.GridRegionSetting();
                mEditorData.gridRegionSetting.gridWidth = reader.ReadSingle();
                mEditorData.gridRegionSetting.gridHeight = reader.ReadSingle();
                mEditorData.gridRegionSetting.horizontalGridCount = reader.ReadInt32();
                mEditorData.gridRegionSetting.verticalGridCount = reader.ReadInt32();
                int shortCount = reader.ReadInt32();
                short[] gridData = new short[shortCount];
                for (int i = 0; i < shortCount; ++i)
                {
                    gridData[i] = reader.ReadInt16();
                }

                //convert to byte data
                byte[] byteGridData = ConvertToByteArray(gridData);
                mEditorData.gridRegionSetting.gridData = byteGridData;

                int n = reader.ReadInt32();
                mEditorData.gridRegionSetting.templates = new config.GridRegionTemplate[n];
                for (int i = 0; i < n; ++i)
                {
                    var color = Utils.ReadColor32(reader);
                    byte type = reader.ReadByte();
                    mEditorData.gridRegionSetting.templates[i] = new config.GridRegionTemplate(color, type);
                }
            }
            //-----------------version 1 end------------------------
        }

        byte[] ConvertToByteArray(short[] gridData)
        {
            int n = gridData.Length * 2;
            byte[] byteData = new byte[n];
            for (int i = 0; i < n; ++i)
            {
                int idx = i / 2;
                int m = i % 2;
                short val = gridData[i / 2];
                byteData[i] = GetByte(val, m);
            }

            return byteData;
        }

        byte GetByte(short val, int m)
        {
            if (m == 0)
            {
                return (byte)val;
            }
            return (byte)((val & 0xff00) >> 8);
        }
    }
}
