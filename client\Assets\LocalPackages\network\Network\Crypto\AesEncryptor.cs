﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;

namespace Crypto
{
    public class AesEncryptor
    {
        AesManaged m_AesManager;
        ICryptoTransform m_AesEncryptor;
        public AesEncryptor(byte[] key, byte[] iv, bool isUseKeyV1)
        {
            m_AesManager = new AesManaged { Mode = CipherMode.ECB, Padding = PaddingMode.None };
            m_AesEncryptor = new CounterModeCryptoTransform(m_AesManager, key, iv, isUseKeyV1);
        }

        public AesEncryptor(string str, string Key, out string result)
        {
            byte[] keyArray = Encoding.UTF8.GetBytes(Key);
            byte[] toEncryptArray = Encoding.UTF8.GetBytes(str);
            RijndaelManaged rDel = new RijndaelManaged();
            rDel.Key = keyArray;
            rDel.Mode = CipherMode.ECB;
            rDel.Padding = PaddingMode.PKCS7;
            ICryptoTransform cTransform = rDel.CreateEncryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            result = BitConverter.ToString(resultArray, 0).Replace("-", string.Empty).ToLower();
        }

        public byte[] Encrypt(byte[] data, int offset, int len)
        {
            return m_AesEncryptor.TransformFinalBlock(data, offset, len);
        }
    }
}