﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System.IO;

namespace TFW.Map {
    /*编辑器导出的地图数据的版本分为major和minor,同一个major版本都使用一个importer,
    根据minor版本来判断不同版本的数据,不同major版本使用不同的importer
    importer的任务是生成导入数据,然后MapCreator根据这个数据生成地图
    */
    public abstract class MapBinaryImporter {
        public MapBinaryImporter(int version) {
            mVersion = version;
        }
        public int version {
            get {
                return mVersion;
            }
        }

        public abstract config.SLGMakerData Load(int minorVersion, string mapName, BinaryReader reader);

        int mVersion;
    }
}
