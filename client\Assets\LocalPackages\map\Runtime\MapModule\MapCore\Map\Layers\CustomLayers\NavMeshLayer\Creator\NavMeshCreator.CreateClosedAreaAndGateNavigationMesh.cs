﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static partial class NavMeshCreator
    {
        static void CreateClosedAreaAndGateNavMesh(PrefabOutlineType type, Vector3 min, Vector3 max, float agentRadius, float minimumAngle, float maximumArea, bool oceanAreaEnabled, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            meshVertices = null;
            meshIndices = null;
            triangleStates = null;
            triangleTypes = null;
            var map = Map.currentMap;
            var collisionLayer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (collisionLayer != null)
            {
                //先创建普通的导航网格,special region(Gate)也当作障碍物
                var meshBlocks = Utils.CreateNavMesh(LayerTypeMask.kGridModelLayer | LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kModelLayer | LayerTypeMask.kCollisionLayer, 1, 1, type, false, NavigationCreateMode.CreateLandNavigationMesh, true, false, true, removeSameHoles, agentRadius, minimumAngle, maximumArea);

#if false
                    //temp code
                    BigMeshViewer viewer1 = new BigMeshViewer();
                    viewer1.Create(null, $"navmesh1", meshBlocks[0].vertices, meshBlocks[0].indices, false, Color.yellow);
#endif

                var combineMeshies = new List<MeshItem>();

                //gate regions
                Vector3[] specialRegionVertices;
                int[] specialRegionIndices;
                List<ushort> triangleTypeList;
                List<bool> triangleStateList;
                CreateSpecialRegionNavMesh(type, agentRadius, false, out specialRegionVertices, out specialRegionIndices, out triangleTypeList, out triangleStateList);

#if false
                    //temp code
                    BigMeshViewer viewer2 = new BigMeshViewer();
                    viewer2.Create(null, $"navmesh2", specialRegionVertices, specialRegionIndices, false, Color.red);
#endif
                combineMeshies.Add(new MeshItem(specialRegionVertices, specialRegionIndices));

                //closed regions
                List<ClosedAreaDetector.Area> closedRegions = collisionLayer.regionDetector.CalculateRegions(meshBlocks[0].vertices, meshBlocks[0].indices);
                for (int i = 0; i < closedRegions.Count; ++i)
                {
                    combineMeshies.Add(new MeshItem(closedRegions[i].meshVertices, closedRegions[i].meshIndices));
                    int triangleCount = closedRegions[i].meshIndices.Length / 3;
                    for (int t = 0; t < triangleCount; ++t)
                    {
                        triangleTypeList.Add((ushort)closedRegions[i].type);
                        triangleStateList.Add(true);
                    }

#if false
                        //temp code
                        BigMeshViewer viewer = new BigMeshViewer();
                        viewer.Create(null, $"closedRegion {i}", closedRegions[i].meshVertices, closedRegions[i].meshIndices, false, Color.blue);
#endif
                }

                CombineMesh(combineMeshies, out meshVertices, out meshIndices);
                if (meshVertices == null || meshVertices.Length == 0)
                {
                    meshVertices = meshBlocks[0].vertices;
                    meshIndices = meshBlocks[0].indices;
                    triangleTypes = null;
                    triangleStates = null;
                }
                else
                {
                    triangleTypes = triangleTypeList.ToArray();
                    triangleStates = triangleStateList.ToArray();
                }
            }
        }
    }
}


#endif