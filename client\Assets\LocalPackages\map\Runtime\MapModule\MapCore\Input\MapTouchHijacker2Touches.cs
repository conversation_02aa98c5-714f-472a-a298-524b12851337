﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    //模拟2根手指touch,一根是鼠标左键,另外一根是虚拟touch
    public class MapTouchHijacker2Touches : IMapTouchHijacker
    {
        public enum VirtualTouchMoveType
        {
            Static,
            Dynamic,
        }

        public int GetTouchCount(IMapTouchManager touchManager)
        {
            if (touchManager.touchCount == 1)
            {
                return 2;
            }
            return touchManager.touchCount;
        }

        public IMapTouch GetTouch(IMapTouchManager touchManager, int index)
        {
            if (touchManager.touchCount == 1)
            {
                if (index == 0)
                {
                    return touchManager.GetTouch(0);
                }
                return mVirtualTouch;
            }
            return touchManager.GetTouch(index);
        }

        public void Update(IMapTouchManager touchManager)
        {
            if (touchManager.touchCount == 1)
            {
                var touch = touchManager.GetTouch(0);
                if (mVirtualTouch == null)
                {
                    mVirtualTouch = new VirtualTouch();
                }
                if (touch.state == MapTouchState.Touch)
                {
                    mVirtualTouch.position = mVirtualTouchStartPos;
                }
                else if (touch.state == MapTouchState.Touching)
                {
                    if (mMoveType == VirtualTouchMoveType.Dynamic)
                    {
                        var touchDeltaPos = touch.position - touch.lastPosition;
                        if (touchDeltaPos.x != 0 || touchDeltaPos.y != 0)
                        {
                            var dir = touch.position - mVirtualTouch.position;
                            dir.Normalize();
                            var proj = Vector2.Dot(dir, touchDeltaPos) * dir;
                            mVirtualTouch.position += proj;
                        }
                    }
                }
                mVirtualTouch.state = touch.state;
                mVirtualTouch.alive = true;
            }
            else
            {
                mVirtualTouch = null;
            }
        }

        public VirtualTouchMoveType moveType
        {
            get {
                return mMoveType;
            }
            set
            {
                mMoveType = value;
            }
        }

        public Vector2 virtualTouchStartPos { set { mVirtualTouchStartPos = value; } get { return mVirtualTouchStartPos; } }

        VirtualTouchMoveType mMoveType = VirtualTouchMoveType.Dynamic;
        VirtualTouch mVirtualTouch;
        Vector2 mVirtualTouchStartPos;
    }
}
