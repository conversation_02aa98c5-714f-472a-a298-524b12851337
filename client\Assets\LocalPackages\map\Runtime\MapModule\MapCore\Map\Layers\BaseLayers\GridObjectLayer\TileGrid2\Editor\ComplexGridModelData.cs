﻿ 



 
 

using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public interface IComplexGridModelData : IMapObjectData
    {
        void CalculateBounds();
        void ShowGrid();
        void HideGrid();
        short modifiedTransformDataIndex { get; set; }
        string gameObjectTag { get; set; }
        string objectTag { get; set; }
        ushort occupiedGridCount { get; set; }
        ushort activeGridCount { get; set; }
        byte lod { get; }
        bool useRenderTextureModel { get; set; }
        bool hasPrefabOutline { get; }
    }

    public class ComplexGridModelData : ModelData, IComplexGridModelData
    {
        public ComplexGridModelData(int id, Map map, int flag, Vector3 position, Quaternion rotation, Vector3 scale, ModelTemplate modelTemplate, ushort occupiedGridCount, byte lod, string objectTag, bool useRenderTextureModel)
            : base(id, map, flag, position, rotation, scale, modelTemplate, true)
        {
#if UNITY_EDITOR
            Debug.Assert(occupiedGridCount < ushort.MaxValue, "Occupied grid count is out of range!");
#endif
            mActiveGridCount = 0;
            mOccupiedGridCount = occupiedGridCount;
            mLOD = lod;
            mObjectTag = objectTag;
            mUseRenderTextureModel = useRenderTextureModel;
        }

        public void ShowGrid()
        {
            ++mActiveGridCount;
            if (mActiveGridCount > mOccupiedGridCount)
            {
                Debug.Assert(false, "grid out of range");
            }
        }

        public void HideGrid()
        {
            --mActiveGridCount;
            if (mActiveGridCount == ushort.MaxValue)
            {
                Debug.Assert(false);
            }
        }

        //修改prefab的rotation和scaling后这些修改值存储的索引
        public short modifiedTransformDataIndex { get { return mModifiedTransformDataIndex; } set { mModifiedTransformDataIndex = value; } }
        public ushort occupiedGridCount { get { return mOccupiedGridCount; } set { mOccupiedGridCount = value; } }
        public ushort activeGridCount { get { return mActiveGridCount; } set { mActiveGridCount = value; } }
        public bool useRenderTextureModel { get { return mUseRenderTextureModel; } set { mUseRenderTextureModel = value; } }
        public byte lod { get { return mLOD; } }
        public bool hasPrefabOutline { get { return false; } }
        public string objectTag { get { return mObjectTag; } set { mObjectTag = value; } }
        public string gameObjectTag { 
            get
            {
#if UNITY_EDITOR
                string childPrefabPath = GetAssetPath(0);
                var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(childPrefabPath);
                return childPrefab.tag;
#else
                return "";
#endif
            }
            set { }
        }

        ushort mActiveGridCount = 0;
        ushort mOccupiedGridCount = 0;
        short mModifiedTransformDataIndex = -1;
        string mObjectTag;
        byte mLOD = 0;
        bool mUseRenderTextureModel = false;
    }
}
