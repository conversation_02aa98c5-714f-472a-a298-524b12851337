// Amplify Shader Editor - Visual Shader Editing Tool
// Copyright (c) Amplify Creations, Lda <<EMAIL>>

using System;

namespace AmplifyShaderEditor
{
	[Serializable]
	[NodeAttributes( "Tanh", "Trigonometry Operators", "Hyperbolic tangent of scalars and vectors" )]
	public sealed class TanhOpNode : SingleInputOp
	{
		protected override void CommonInit( int uniqueId )
		{
			base.CommonInit( uniqueId );
			m_opName = "tanh";
			m_previewShaderGUID = "52f78d3a1c66d1c489cd63b0a9300b38";
			m_inputPorts[ 0 ].CreatePortRestrictions( WirePortDataType.OBJECT,
														WirePortDataType.FLOAT,
														WirePortDataType.FLOAT2,
														WirePortDataType.FLOAT3,
														WirePortDataType.FLOAT4,
														WirePortDataType.COLOR,
														WirePortDataType.INT );
		}
	}
}
