﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        float GetAlpha()
        {
            float height = MapCameraMgr.currentCameraHeight;
            float alpha = 1 - Mathf.Clamp01((height - mAlpha1Height) / (mAlpha0Height - mAlpha1Height));
            return alpha;
        }

        void UpdateFading()
        {
            if (mCrossfading)
            {
                if (mSpriteMaterial != null)
                {
                    float alpha = GetAlpha();
                    mSpriteMaterial.SetFloat("_SpriteAlpha", alpha);
                }
            }
        }

        public void UpdateScale()
        {
            if (mUpdateScale)
            {
                if (mScaleUpdater != null)
                {
                    float scaleFactor = mScaleUpdater.UpdateObjectScaleAtHeight();
                    if (scaleFactor != 0)
                    {
                        mScaleFactorAtCameraHeight = scaleFactor;
                        for (int i = 0; i < mSprites.Count; ++i)
                        {
                            var sprite = mSprites[i];
                            var group = mSpriteGroups[sprite.groupIndex];
                            sprite.obj.transform.localScale = Vector3.one * mScaleFactorAtCameraHeight * group.spriteBaseScale[sprite.type];
                        }
                    }
                }
            }
        }
    }
}
