%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: 2lerp
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18800\n1985;30;1743;939;933.399;370.3858;1;True;False\nNode;AmplifyShaderEditor.LerpOp;14;54.51489,-161.6079;Inherit;True;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.LerpOp;8;337.515,55.39209;Inherit;True;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;1;-582.8999,-230.2;Inherit;False;0;4;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;2;-592.8999,-108.2;Inherit;False;1;4;1;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;3;-592.8999,68.79999;Inherit;False;2;4;2;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;15;-632.2629,214.8747;Inherit;False;Enum;0;3;False;1;0;INT;0;False;1;INT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;10;-188.8547,240.3341;Inherit;False;2;0;INT;0;False;1;INT;1;False;1;INT;0\nNode;AmplifyShaderEditor.SaturateNode;17;-146.399,1.614197;Inherit;False;1;0;INT;0;False;1;INT;0\nNode;AmplifyShaderEditor.SaturateNode;18;38.60095,228.6142;Inherit;False;1;0;INT;0;False;1;INT;0\nNode;AmplifyShaderEditor.FunctionOutput;16;748.7664,41.68552;Inherit;False;True;-1;Output;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nWireConnection;14;0;1;0\nWireConnection;14;1;2;0\nWireConnection;14;2;17;0\nWireConnection;8;0;14;0\nWireConnection;8;1;3;0\nWireConnection;8;2;18;0\nWireConnection;10;0;15;0\nWireConnection;17;0;15;0\nWireConnection;18;0;10;0\nWireConnection;16;0;8;0\nASEEND*/\n//CHKSM=9ADE03FDF8408F36F5B87FC47C724838A611F450"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
