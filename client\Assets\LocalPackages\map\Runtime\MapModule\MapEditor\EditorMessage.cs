﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map {
    [Black]
    public class GMSetTile : GameMessageTemplate<GMSetTile> {
        public GMSetTile(int x, int y, int layerID, TileData tileData) {
            this.x = x;
            this.y = y;
            this.layerID = layerID;
            this.tileData = tileData;
        }
        public int x;
        public int y;
        public int layerID;
        public TileData tileData;
    }

    public class GMClearTile : GameMessageTemplate<GMClearTile> {
        public GMClearTile(int x, int y, int layerID) {
            this.x = x;
            this.y = y;
            this.layerID = layerID;
        }
        public int x;
        public int y;
        public int layerID;
    }

    [Black]
    public class GMTileActiveStateChange : GameMessageTemplate<GMTileActiveStateChange> {
        public GMTileActiveStateChange() { }

        public GMTileActiveStateChange(int minX, int minY, int maxX, int maxY, int layerID, bool active) {
            this.minX = minX;
            this.minY = minY;
            this.maxX = maxX;
            this.maxY = maxY;
            this.layerID = layerID;
            this.active = active;
        }
        public int minX;
        public int minY;
        public int maxX;
        public int maxY;
        public int layerID;
        public bool active;
    }
}

#endif