%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Atlas_Map_2
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 0
      compressionQuality: 0
      maxTextureSize: 0
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: Android
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: 50
      m_TextureCompression: 0
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 1
      m_IgnorePlatformSupport: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 0
    packingSettings:
      serializedVersion: 2
      padding: 2
      blockOffset: 2
      allowAlphaSplitting: 0
      enableRotation: 0
      enableTightPacking: 0
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: a7df4d231c4bdb64688d3d053b059bc6, type: 3}
    - {fileID: 2800000, guid: 070290d986ae3044bb2e4256fb69eec1, type: 3}
    - {fileID: 2800000, guid: 833f3e8b95d99494d8b6b27b02c6c604, type: 3}
    - {fileID: 2800000, guid: 9e764f456eb8fa04cb629533e06a82fd, type: 3}
    - {fileID: 2800000, guid: a820291731b71ab4f90888259d37d6bf, type: 3}
    - {fileID: 2800000, guid: bba4c258868d4e94fa21b0a6623975b1, type: 3}
    - {fileID: 2800000, guid: 02792bfc289118f4c90bb91509c5b70c, type: 3}
    - {fileID: 2800000, guid: 64aece202b267cc4d9a778e367c127a3, type: 3}
    - {fileID: 2800000, guid: 18b70d81f58c722408f9d35335d73c38, type: 3}
    bindAsDefault: 1
    isAtlasV2: 0
    cachedData: {fileID: 0}
    packedSpriteRenderDataKeys: []
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_RenderDataMap: {}
  m_Tag: Atlas_Map_2
  m_IsVariant: 0
