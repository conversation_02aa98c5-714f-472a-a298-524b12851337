﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class CurveConfigUI
    {
        public CurveConfigUI(CurveConfig config, System.Action onSetDirty, string timeName, string valueName, string foldName)
        {
            mConfig = config;
            if (mConfig.curve == null)
            {
                mConfig.curve = new AnimationCurve();
            }

            mOnSetDirty = onSetDirty;
            mTimeName = timeName;
            mValueName = valueName;
            mFoldName = foldName;

            CreateKeyframes(mConfig.curve);
        }

        public void OnDestroy()
        {
            ApplyCurve();
        }

        public void OnInspectorGUI()
        {
            DrawCameraFOVSetting();
        }

        void DrawCameraFOVSetting()
        {
            mCurveFoldState = EditorGUILayout.Foldout(mCurveFoldState, mFoldName);

            if (mCurveFoldState)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                if (mEditingKeyframes == false)
                {
                    var newCurve = EditorGUILayout.CurveField("Curve", mConfig.curve);
                    RefreshKeyframes(mConfig.curve);
                }

                var curve = mConfig.curve;
                bool editingState = EditorGUILayout.ToggleLeft("Edit", mEditingKeyframes);
                if (editingState)
                {
                    int nodeCount = curve.length;
                    int newNodeCount = EditorGUILayout.IntField("Count", nodeCount);
                    newNodeCount = Mathf.Clamp(newNodeCount, 0, 50);
                    if (newNodeCount != nodeCount)
                    {
                        ChangeNodeCount(newNodeCount);
                    }

                    nodeCount = curve.length;

                    for (int i = 0; i < nodeCount; ++i)
                    {
                        var keyframe = curve[i];
                        EditorGUILayout.BeginHorizontal();
                        mKeyframes[i].time = EditorGUILayout.FloatField(mTimeName, mKeyframes[i].time);
                        mKeyframes[i].value = EditorGUILayout.FloatField(mValueName, mKeyframes[i].value);
                        EditorGUILayout.EndHorizontal();
                    }
                }
                else
                {
                    int nodeCount = curve.length;
                    EditorGUILayout.LabelField("Count", nodeCount.ToString());

                    for (int i = 0; i < nodeCount; ++i)
                    {
                        var keyframe = curve[i];
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField(mTimeName, mKeyframes[i].time.ToString());
                        EditorGUILayout.LabelField(mValueName, mKeyframes[i].value.ToString());
                        EditorGUILayout.EndHorizontal();
                    }
                }

                if (editingState != mEditingKeyframes)
                {
                    if (!editingState)
                    {
                        ApplyCurve();
                    }
                    mEditingKeyframes = editingState;
                }

                if (GUILayout.Button("Apply"))
                {
                    ApplyCurve();
                }

                EditorGUILayout.EndVertical();
            }
        }

        void ApplyCurve()
        {
            int n = mKeyframes.Length;
            Keyframe[] keys = new Keyframe[n];
            for (int i = 0; i < n; ++i)
            {
                keys[i] = mKeyframes[i];
            }

            mConfig.curve.keys = keys;

            if (mOnSetDirty != null)
            {
                mOnSetDirty();
            }
        }

        void ChangeNodeCount(int newCount)
        {
            mConfig.SetNodeCount(newCount);

            CreateKeyframes(mConfig.curve);

            if (mOnSetDirty != null)
            {
                mOnSetDirty();
            }
        }

        void CreateKeyframes(AnimationCurve curve)
        {
            int n = curve.length;
            mKeyframes = new Keyframe[n];
            for (int i = 0; i < n; ++i)
            {
                mKeyframes[i] = curve[i];
            }
        }

        void RefreshKeyframes(AnimationCurve curve)
        {
            if (curve.length != mKeyframes.Length)
            {
                CreateKeyframes(curve);
            }
            else
            {
                for (int i = 0; i < curve.length; ++i)
                {
                    mKeyframes[i] = curve[i];
                }
            }
        }

        bool mCurveFoldState = true;
        bool mEditingKeyframes = false;
        Keyframe[] mKeyframes;
        CurveConfig mConfig;
        System.Action mOnSetDirty;
        string mTimeName;
        string mValueName;
        string mFoldName;
    }
}

#endif