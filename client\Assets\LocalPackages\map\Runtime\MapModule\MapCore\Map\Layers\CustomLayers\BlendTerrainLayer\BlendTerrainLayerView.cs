﻿ 



 
 



/*
 * created by wzw at 2019.10.30
 */

using UnityEngine;
using System.Collections.Generic;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //针对地表优化的视图!
    public class BlendTerrainLayerView : MapLayerView
    {
        public BlendTerrainLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = Map.currentMap.view.reusableGameObjectPool;
            CreateGameObjects();
            mRuntimeHeightMeshManager = new TerrainHeightMeshManager(layerData.map);
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            Clear();
        }

        public void Clear()
        {
            for (int i = 0; i < mTerrainTiles.Count; ++i)
            {
                var list = mTerrainTiles[i];
                int n1 = list.GetLength(0);
                int n2 = list.GetLength(1);
                for (int k = 0; k < n1; ++k)
                {
                    for (int j = 0; j < n2; ++j)
                    {
                        Utils.DestroyObject(list[k, j]);
                        list[k, j] = null;
                    }
                }
            }

#if UNITY_EDITOR_WIN
            foreach (var p in mHeightMeshies)
            {
                p.Value.OnDestroy();
            }
            mHeightMeshies.Clear();
#endif
        }

        void CreateGameObjects()
        {
            var terrainLayerData = layerData as RenderTextureBlendTerrainLayerData;
            var otherLODTiles = terrainLayerData.otherLODTiles;
            int rows = terrainLayerData.verticalTileCount;
            int cols = terrainLayerData.horizontalTileCount;
            int nDefaultLODs = terrainLayerData.defaultLODCount;
            int nLODs = terrainLayerData.lodConfig.lodConfigs.Length;
            mTerrainTiles = new List<GameObject[,]>(nLODs);
            for (int i = 0; i < nDefaultLODs; ++i)
            {
                mTerrainTiles.Add(new GameObject[rows, cols]);
            }

            if (otherLODTiles != null)
            {
                for (int i = nDefaultLODs; i < nLODs; ++i)
                {
                    int r = otherLODTiles[i - nDefaultLODs].GetLength(0);
                    int c = otherLODTiles[i - nDefaultLODs].GetLength(1);
                    mTerrainTiles.Add(new GameObject[r, c]);
                }
            }
        }

        public void PreprocessHeightMeshies(string[] usedPrefabPaths)
        {
            if (usedPrefabPaths != null)
            {
                for (int i = 0; i < usedPrefabPaths.Length; ++i)
                {
                    var prefab = MapModuleResourceMgr.LoadPrefab(usedPrefabPaths[i]);
                    var filter = prefab.GetComponentInChildren<MeshFilter>();
                    if (filter != null)
                    {
                        var mesh = filter.sharedMesh;
                        if (mesh != null)
                        {
                            mRuntimeHeightMeshManager.SetOriginalMesh(usedPrefabPaths[i], mesh);
                        }
                    }
                }
            }
        }

        public void OnObjectActiveStateChange(IMapObjectData tileData, int x, int y, int lod)
        {
            //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load OnObjectActiveStateChange");
            if (tileData.IsObjActive())
            {
               // UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load OnObjectActiveStateChange 01");
                ShowObject(tileData, x, y, lod);
            }
            else
            {
               // UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load OnObjectActiveStateChange 02");
                HideObject(tileData, x, y, lod);
            }
        }

        //显示地图对象的模型
        public void ShowObject(IMapObjectData data, int x, int y, int lod)
        {
            //UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load ShowObject {lod},{mTerrainTiles.Count}");

            if (lod >= mTerrainTiles.Count)
            {
                lod = mTerrainTiles.Count - 1;
            }

#if UNITY_EDITOR
            if (mTerrainTiles[lod][y, x] != null)
            {
                Debug.Assert(false, string.Format("Show object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            var tile = data as BlendTerrainTileData;
            mObjectTileX = x;
            mObjectTileY = y;
            string prefabPath = data.GetAssetPath(lod);

            //var obj = mObjectPool.Require(prefabPath);
            GameObject obj= new GameObject(prefabPath);
            obj.transform.SetParent(root.transform, false);
            mTerrainTiles[lod][y, x] = obj;
            obj.transform.localPosition = data.GetPosition();
            obj.transform.rotation = data.GetRotation();

            mObjectPool.Require(prefabPath, (mObj) =>
            {
                if (obj != null && obj.activeSelf)
                {
                    mObj.transform.SetParent(obj.transform, false);
                    mObj.transform.localPosition = Vector3.zero;

                    // UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load ShowObject {obj.transform.localPosition.x},{obj.transform.localPosition.y},{obj.transform.localPosition.z}");
                    // UnityEngine.Debug.Log($"[{nameof(ModelTemplateManager)}] OnUpdateMapSceneLoadDone GetOrCreateModelTemplate Load ShowObject {obj.transform.rotation.x},{obj.transform.rotation.y},{obj.transform.rotation.z}");

#if UNITY_EDITOR
            if (Map.currentMap.isEditorMode)
            {
                if (mObj.GetComponent<DisableKeyboardDelete>() == null)
                {
                    mObj.AddComponent<DisableKeyboardDelete>();
                }

                var renderer = mObj.GetComponentInChildren<MeshRenderer>();
                if (renderer != null && renderer.sharedMaterial != null)
                {
                    renderer.sharedMaterial.SetColor("_GlobalDayNightColor", new Color(1, 1, 1, 1));
                    renderer.sharedMaterial.SetFloat("_ToneMappingMin", 0);
                    renderer.sharedMaterial.SetFloat("_ToneMappingMax", 1);
                }
            }
#endif

                    if (Map.currentMap.isEditorMode && MapModule.supportGroundHeightChange)
                    {
                        UpdateMesh(x, y);
                    }
                    else
                    {
                        if (lod == 0)
                        {
                            if (tile != null && tile.hasHeightData)
                            {
                                var filter = mObj.GetComponentInChildren<MeshFilter>();
                                filter.sharedMesh = mRuntimeHeightMeshManager.GetHeightMesh(mObjectTileX, mObjectTileY);
                            }
                        }
                    }
                }
                else
                {
                    mObjectPool.Release(prefabPath, mObj, Map.currentMap);
                    //Utils.DestroyObject(mObj);
                    //mObjectPool.Release(prefabPath);
                }
            }); 
           
        }

        //隐藏地图对象的模型
        public void HideObject(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mTerrainTiles.Count)
            {
                lod = mTerrainTiles.Count - 1;
            }
#if UNITY_EDITOR
            if (mTerrainTiles[lod][y, x] == null)
            {
                Debug.Assert(false, string.Format("Hide object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            var tile = data as BlendTerrainTileData;
            bool hasHeight = false;
            if (tile != null)
            {
                hasHeight = tile.hasHeightData;
            }
            GameObject obj = mTerrainTiles[lod][y, x];
            string prefabPath = data.GetAssetPath(lod);

            if (obj != null && obj.transform.childCount > 0)
            {
                var prefabObj = obj.transform.GetChild(0);
                if (hasHeight)
                {
                    var filter = prefabObj.GetComponentInChildren<MeshFilter>();
                    filter.sharedMesh = mRuntimeHeightMeshManager.GetOriginalMesh(prefabPath); 
                }

                mObjectPool.Release(prefabPath, prefabObj.gameObject, layerData.map);
            }

            if (obj != null)
            {
                Utils.DestroyObject(obj);
            }

            mTerrainTiles[lod][y, x] = null;
        }

        public Mesh GetTerrainTileMesh(int tileX, int tileY)
        {
            var obj = mTerrainTiles[0][tileY, tileX];
            var filter = obj.GetComponentInChildren<MeshFilter>();
            if (filter != null)
            {
                return filter.sharedMesh;
            }
            return null;
        }

        public void GetTerrainTileMeshAndGameObject(int tileX, int tileY, out Mesh mesh, out GameObject gameObject)
        {
            mesh = null;
            gameObject = null;
            var obj = mTerrainTiles[0][tileY, tileX];
            var filters = obj.GetComponentsInChildren<MeshFilter>();
            if (filters != null)
            {
                for (int i = 0; i < filters.Length; ++i)
                {
                    if (filters[i].tag != MapCoreDef.MAP_IGNORE_MESH_TAG) {
                        mesh = filters[i].sharedMesh;
                        gameObject = obj;
                    }
                }
            }
        }

        public void UpdateMesh(int tileX, int tileY)
        {
#if UNITY_EDITOR_WIN
            var layerData = mLayerData as BlendTerrainLayerData;
            var tile = layerData.GetTile(tileX, tileY);
            if (tile == null || !tile.meshDirty)
            {
                return;
            }

            tile.meshDirty = false;
            var obj = mTerrainTiles[0][tileY, tileX];
            var filter = obj.GetComponentInChildren<MeshFilter>();
            var filterMesh = filter.sharedMesh;
            var key = new Vector2Int(tileX, tileY);
            TerrainHeightData oldData;
            bool found = mHeightMeshies.TryGetValue(key, out oldData);
            TerrainHeightData newData = CreateTerrainMesh(tileX, tileY, oldData);
            if (newData != null)
            {
                if (newData != oldData)
                {
                    oldData?.OnDestroy();
                }
                mHeightMeshies[key] = newData;
                filter.sharedMesh = newData.mesh;
            }
#endif
        }
#if UNITY_EDITOR_WIN
        Vector2 InterpolateUV(Vector2[] cornerUVs, float rx, float ry)
        {
            Vector2 lBottom = Vector2.Lerp(cornerUVs[0], cornerUVs[1], rx);
            Vector2 lTop = Vector2.Lerp(cornerUVs[2], cornerUVs[3], rx);
            Vector2 final = Vector2.Lerp(lBottom, lTop, ry);
            return final;
        }

        //如果tile有高度,必须保证prefab没有transform旋转,只能旋转uv
        public TerrainHeightData CreateTerrainMesh(int tileX, int tileY, TerrainHeightData oldData)
        {
            var layerData = mLayerData as BlendTerrainLayerData;
            var tile = layerData.GetTile(tileX, tileY);
            if (tile == null)
            {
                return null;
            }
            if (tile.uvs == null)
            {
                return null;
            }
            float[] heights = tile.heights;
            int resolution = tile.resolution;
            if (heights == null)
            {
                heights = new float[4];
                resolution = 1;
            }
            int newVertexCount = (resolution + 1) * (resolution + 1);
            Mesh mesh = null;
            TerrainHeightData newData;
            if (oldData == null || newVertexCount != oldData.mesh.vertexCount)
            {
                newData = new TerrainHeightData();
                mesh = new Mesh();
                mesh.MarkDynamic();
                newData.mesh = mesh;
            }
            else
            {
                newData = oldData;
                mesh = oldData.mesh;
            }
            float gridSize = layerData.tileWidth / resolution;
            var vertices = new Vector3[newVertexCount];
            var uvs = new Vector2[newVertexCount];
            var indices = new int[resolution * resolution * 6];
            int idx = 0;
            int triangleIdx = 0;
            for (int i = 0; i <= resolution; ++i)
            {
                for (int j = 0; j <= resolution; ++j)
                {
                    float x = j;
                    float z = i;
                    vertices[idx] = new Vector3(x * gridSize, heights[idx], z * gridSize);
                    float rx = (float)j / resolution;
                    float ry = (float)i / resolution;
                    uvs[idx] = InterpolateUV(tile.uvs, rx, ry);
                    ++idx;

                    if (i != resolution && j != resolution)
                    {
                        int v0 = i * (resolution + 1) + j;
                        int v1 = v0 + (resolution + 1);
                        int v2 = v1 + 1;
                        int v3 = v0 + 1;

                        indices[triangleIdx * 6] = v0;
                        indices[triangleIdx * 6 + 1] = v1;
                        indices[triangleIdx * 6 + 2] = v2;
                        indices[triangleIdx * 6 + 3] = v0;
                        indices[triangleIdx * 6 + 4] = v2;
                        indices[triangleIdx * 6 + 5] = v3;
                        ++triangleIdx;
                    }
                }
            }

            mesh.vertices = vertices;
            mesh.uv = uvs;
            mesh.triangles = indices;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();
            mesh.UploadMeshData(false);

            float heightRange = mesh.bounds.size.y;
            float[] normalizedHeights = (float[])heights.Clone();
            for (int i = 0; i < normalizedHeights.Length; ++i)
            {
                if (heightRange != 0)
                {
                    normalizedHeights[i] = heights[i] / heightRange;
                }
                else
                {
                    normalizedHeights[i] = 0;
                }
            }

            if (newData.collider == null)
            {
                if (tile.heights != null)
                {
                    Vector3 pos = layerData.FromCoordinateToWorldPosition(tileX, tileY);
                    newData.collider = new PhysxTerrainCollider();
                    newData.collider.Init(pos, resolution, layerData.tileWidth, layerData.tileHeight, normalizedHeights, heightRange);
                }
            }
            else
            {
                newData.collider.UpdateHeights(normalizedHeights, resolution, layerData.tileWidth, layerData.tileHeight, heightRange);
            }

            return newData;
        }
#endif

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        public GameObject GetTerrainTileLOD0GameObject(int x, int y)
        {
            return mTerrainTiles[0][y, x];
        }

#if UNITY_EDITOR_WIN
        public Mesh GetTerrainHeightMesh(int x, int y)
        {
            TerrainHeightData data;
            mHeightMeshies.TryGetValue(new Vector2Int(x, y), out data);
            if (data != null)
            {
                return data.mesh;
            }
            return null;
        }

        public void CreateTerrainDebugMesh(int x, int y)
        {
            TerrainHeightData data;
            mHeightMeshies.TryGetValue(new Vector2Int(x, y), out data);
            if (data != null)
            {
                var tile = (mLayerData as BlendTerrainLayerData).GetTile(x, y);
                data.collider.CreateDebugObject(x, y, tile.resolution, mLayerData.tileWidth, mLayerData.tileHeight);
            }
        }
#endif

        public void SetSize(int newHorizontalTileCount, int newVerticalTileCount)
        {
            if (!Map.currentMap.isEditorMode)
            {
                return;
            }
            Clear();
            mTerrainTiles = new List<GameObject[,]>(1);
            mTerrainTiles.Add(new GameObject[newVerticalTileCount, newHorizontalTileCount]);
        }

#if UNITY_EDITOR
        public CollisionSegment GetCollisionSegment(int x, int y)
        {
            var lod0Tiles = mTerrainTiles[0];
            int rows = lod0Tiles.GetLength(0);
            int cols = lod0Tiles.GetLength(1);
            if (x >= 0 && x < cols && y >= 0 && y < rows)
            {
                var obj = lod0Tiles[y, x];
                if (obj != null)
                {
                    return obj.GetComponentInChildren<CollisionSegment>(true);
                }
            }
            return null;
        }

        public List<CollisionSegmentInfo> GetCollisionSegments()
        {
            List<CollisionSegmentInfo> result = new List<CollisionSegmentInfo>();
            var lod0Tiles = mTerrainTiles[0];
            int rows = lod0Tiles.GetLength(0);
            int cols = lod0Tiles.GetLength(1);
            for (int r = 0; r < rows; ++r)
            {
                for (int c = 0; c < cols; ++c)
                {
                    var obj = lod0Tiles[r, c];
                    if (obj != null)
                    {
                        var segments = obj.GetComponentsInChildren<CollisionSegment>(true);
                        if (segments != null && segments.Length > 0)
                        {
                            if (segments.Length == 1 && segments[0].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle).Count > 0)
                            {
                                var info = new CollisionSegmentInfo(c, r, segments[0]);
                                result.Add(info);
                            }
                            else
                            {
                                Debug.LogError("Error, only 1 none empty collision segment is supported in one tile");
                            }
                        }
                    }
                }
            }
            return result;
        }

        public void ApplySegmentChangeToPrefab()
        {
            var lod0Tiles = mTerrainTiles[0];
            int rows = lod0Tiles.GetLength(0);
            int cols = lod0Tiles.GetLength(1);
            HashSet<int> processed = new HashSet<int>();
            for (int r = 0; r < rows; ++r)
            {
                for (int c = 0; c < cols; ++c)
                {
                    var obj = lod0Tiles[r, c];
                    if (obj != null)
                    {
                        var segment = obj.GetComponentInChildren<CollisionSegment>(true);
                        if (segment != null && segment.dirty)
                        {
                            var modelTemplate = (layerData as BlendTerrainLayerData).GetTile(c, r).GetModelTemplate();
                            if (processed.Contains(modelTemplate.id) == false)
                            {
                                segment.dirty = false;
                                processed.Add(modelTemplate.id);
                                var prefab = MapModuleResourceMgr.LoadPrefab(modelTemplate.GetLODPrefabPath(0));
                                var prefabSegment = prefab.GetComponent<CollisionSegment>();
                                if (prefabSegment != null)
                                {
                                    prefabSegment.SetOutlineData(segment.GetOutlineData(PrefabOutlineType.NavMeshObstacle), PrefabOutlineType.NavMeshObstacle);
                                }
                                else
                                {
                                    Debug.LogError($"{modelTemplate.GetLODPrefabPath(0)} no collision segment found");
                                }
                                PrefabUtility.SavePrefabAsset(prefab);
                                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                            }
                        }
                    }
                }
            }
        }
#endif

        List<GameObject[,]> mTerrainTiles;
#if UNITY_EDITOR_WIN
        Dictionary<Vector2Int, TerrainHeightData> mHeightMeshies = new Dictionary<Vector2Int, TerrainHeightData>();
#endif
        GameObjectPool mObjectPool;
        int mObjectTileX = 0;
        int mObjectTileY = 0;
        TerrainHeightMeshManager mRuntimeHeightMeshManager;
    };
}
