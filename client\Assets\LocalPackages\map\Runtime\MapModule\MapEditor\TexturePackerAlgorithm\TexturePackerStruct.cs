﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TexturePackerSetting
    {
        //如果预先知道输入贴图的大小(所有贴图相同大小),就传入非0数值
        public int inputTextureWidth;
        public int inputTextureHeight;
        public int atlasTextureMaxWidth;
        public int atlasTextureMaxHeight;
        public int borderSize = 0;
        public int padding = 0;
        public Color backgroundColor;
        public bool enableRotate = false;
        public bool onlyPackOneTexture = false;
        //如果是rgb贴图,是否将其alpha默认为1
        public bool rgbTextureAlphaIsOne = true;
    };

    public class TexturePackEntry
    {
        public string name;
        public int offsetX = 0;
        public int offsetY = 0;
        public int width = 0;
        public int height = 0;
        public Rect uv;
        public bool rotated = false;
    };

    public class TexturePackResult
    {
        public List<TextureAtlas> atlas;
        public List<string> missingTextures;

        //通过texutreID得到atlas entry的Index
        public void GetTexturePackEntryIndex(int textureInstanceID, out int atlasIndex, out int entryIndex)
        {
            entryIndex = -1;
            atlasIndex = -1;
            for (int i = 0; i < atlas.Count; ++i)
            {
                int index = atlas[i].GetTexturePackEntryIndex(textureInstanceID);
                if (index >= 0)
                {
                    entryIndex = index;
                    atlasIndex = i;
                    break;
                }
            }
        }
    };
}

#endif