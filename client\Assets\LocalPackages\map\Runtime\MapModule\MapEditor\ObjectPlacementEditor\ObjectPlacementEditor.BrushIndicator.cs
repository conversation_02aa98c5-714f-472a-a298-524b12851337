﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditorBrushIndicator
    {
        public void SetBrush(ObjectPlacementEditor.Brush brush, Transform parent)
        {
            Utils.DestroyObject(mRoot);
            mRoot = new GameObject("Custom Brush");
            mRoot.transform.parent = parent;
            for (int i = 0; i < brush.objects.Count; ++i)
            {
                var obj = AssetDatabase.LoadAssetAtPath<GameObject>(brush.objects[i].prefabPath);
                obj = GameObject.Instantiate<GameObject>(obj);
                obj.transform.parent = mRoot.transform;
                obj.transform.position = brush.objects[i].position;
                obj.transform.rotation = brush.objects[i].rotation;
                obj.transform.localScale = brush.objects[i].scale;
                obj.tag = brush.objects[i].tag;
                obj.layer = brush.objects[i].layer;
            }
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
        }

        public void ShowBrush()
        {
            if (mRoot != null)
            {
                mRoot.SetActive(true);
            }
        }

        public void HideBrush()
        {
            if (mRoot != null)
            {
                mRoot.SetActive(false);
            }
        }

        public void Update(Vector3 position, Quaternion rotation)
        {
            if (mRoot != null)
            {
                mRoot.transform.position = position;
                //mRoot.transform.rotation = rotation;
            }
        }

        GameObject mRoot;
    }
}

#endif