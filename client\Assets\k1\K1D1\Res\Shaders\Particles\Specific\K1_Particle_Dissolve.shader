// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "K1/Particle/Dissolve"
{
	Properties {
	_Amount ("Amount", Range (-0.08, 1)) = 0.5
	_StartAmount("StartAmount", float) = 0.1
	_Tile("Tile", float) = 1
	_DissColor ("DissColor", Color) = (1,1,1,1)
	_ColorAnimate ("ColorAnimate", vector) = (1,1,1,1)
	_MainTex ("Base (RGB) Gloss (A)", 2D) = "white" {}
	_RotateSpeed("RotateDegree",float) = 0
	_DissolveSrc ("DissolveSrc", 2D) = "white" {}
	_RotateSpeedDis ("DisRotateDegree",float) = 0
	_DissolveScale ("DissolveScale", Range (0, 1)) = 0.5
	_alpha("Alpha",Range(0,0.5)) = 0.5
	_alphaRange("AlphaRange",range(0.001,0.5)) = 0.001
	[Enum(UnityEngine.Rendering.BlendMode)] _BlendModeSrc ("Source Blend Mode", Float) = 1
    [Enum(UnityEngine.Rendering.BlendMode)] _BlendModeDst ("Destination Blend Mode", Float) = 1
	}
	Category {
		Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }
		//Blend SrcAlpha OneMinusSrcColor
		Blend [_BlendModeSrc] [_BlendModeDst]
		ColorMask RGB
		Cull Off 
		Lighting Off 
		ZWrite Off

		SubShader {
			Pass {
			
				CGPROGRAM
				#pragma target 3.0
				#pragma vertex vert
				#pragma fragment frag
				#pragma multi_compile_particles

				#include "UnityCG.cginc"

				sampler2D _MainTex;
				float4 _MainTex_ST;
				sampler2D _DissolveSrc;
				float4 _DissolveSrc_ST;
				half4 _DissColor;
				half _Amount;
				//static half3 Color = float3(1,1,1);
				half4 _ColorAnimate;
				half _Tile;
				half _StartAmount;
				float  _DissolveScale;
				float _RotateSpeed;
				float _RotateSpeedDis;
				float _alpha;
				float _alphaRange;

				struct appdata_t {
					float4 vertex : POSITION;
					fixed4 color : COLOR;
					float2 texcoord : TEXCOORD0;
				};

				struct v2f {
					float4 vertex : SV_POSITION;
					fixed4 color : COLOR;
					float2 texcoord : TEXCOORD0;
					float2 dissolveSrc : TEXCOORD1;
				};

				v2f vert (appdata_t v)
				{
					v2f o;
					o.vertex = UnityObjectToClipPos(v.vertex);
					o.color = v.color;
					o.texcoord = TRANSFORM_TEX(v.texcoord,_MainTex);
					o.dissolveSrc = TRANSFORM_TEX(v.texcoord,_DissolveSrc);
					return o;
				}

				fixed4 frag (v2f i) : SV_Target
				{
					//Rotate the UV
					float2 uvMain=i.texcoord.xy -float2(0.5,0.5);
					uvMain = float2(    uvMain.x*cos(_RotateSpeed/(180/3.1416) ) - uvMain.y*sin(_RotateSpeed/(180/3.1416)),
                           uvMain.x*sin(_RotateSpeed /(180/3.1416)) + uvMain.y*cos(_RotateSpeed/(180/3.1416)) );
                    uvMain += float2(0.5,0.5);
                    float2 uvDiss=i.dissolveSrc.xy -float2(0.5,0.5);
					uvDiss = float2(    uvDiss.x*cos(_RotateSpeedDis/(180/3.1416) ) - uvDiss.y*sin(_RotateSpeedDis/(180/3.1416)),
                           uvDiss.x*sin(_RotateSpeedDis /(180/3.1416)) + uvDiss.y*cos(_RotateSpeedDis/(180/3.1416)) );
                    uvDiss += float2(0.5,0.5);

					fixed4 tex = tex2D(_MainTex, uvMain);
					float4 ClipTex = tex2D (_DissolveSrc, uvDiss/_Tile);
					float ClipAmount = Luminance(ClipTex.rgb) - (_Amount+(1-i.color.r));
					float Clip = 0;
					float ani_color = saturate(1 / max((ClipAmount * 100),0.001) * _DissolveScale);
					float3 albedo1 = tex.rgb*1.9 ;
					float3 albedo2 = _DissColor + fixed4( ani_color,ani_color,ani_color,1) * _ColorAnimate;
					clip(ClipAmount+0.01);
					float scale_ = step(0,ClipAmount * 0.7 - _StartAmount);
					fixed4 col = fixed4(albedo1 * scale_ + albedo2 * (1 - scale_)*Luminance(tex.rgb),tex.a*i.color.a) ;
					float alpha = 1;
					if(ClipAmount<_alphaRange)
					{
						alpha = saturate(ClipAmount*_alpha*100);
					}
					col.a = alpha*tex.a;
					return col;
				}
				ENDCG 
			}
		}	
	}
}
