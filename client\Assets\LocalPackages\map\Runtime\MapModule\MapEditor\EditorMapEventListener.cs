﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class EditorMapEventListener : MapEventListener
    {
        public void OnAddMapObject(IMapObjectData objectData)
        {
            var view = Map.currentMap.GetMapObjectView(objectData.GetEntityID());
            if (view != null)
            {
                if (!(view.model is ModelPlaceholder))
                {
                    var objID = view.model.gameObject.AddComponent<ObjectSetting>();
                    objID.objectDataID = objectData.GetEntityID();
                    var template = Map.currentMap.GetEntityModelTemplate(objectData.GetEntityID());
                    if (template != null)
                    {
                        objID.assetPath = template.GetLODPrefabPath(0);
                    }
                }
            }
        }

        public void OnMapObjectLODChange(MapObjectView view)
        {
            if (view.model.gameObject.GetComponent<ObjectSetting>() == null)
            {
                var objID = view.model.gameObject.AddComponent<ObjectSetting>();
                objID.objectDataID = view.objectDataID;
                var objectData = Map.currentMap.FindObject(view.objectDataID) as IMapObjectData;
                if (objectData != null)
                {
                    var modelData = objectData as IMapObjectData;
                    var template = Map.currentMap.GetEntityModelTemplate(objectData.GetEntityID());
                    objID.assetPath = template.GetLODPrefabPath(0);
                }
            }
        }

        public void OnAddMapLayer(MapLayerBase layer)
        {
            MapLayerLogic layerLogic = null;
            if (layer.GetType() == typeof(EditorGridModelLayer))
            {
                layerLogic = layer.gameObject.AddComponent<GridModelLayerLogic>();
                var gridModelLayer = layer as EditorGridModelLayer;
                layerLogic.Initialize(gridModelLayer.layerData, gridModelLayer.layerView);
            }
            else if (layer.GetType() == typeof(RuinLayer))
            {
                layerLogic = layer.gameObject.AddComponent<RuinLayerLogic>();
                var ruinLayer = layer as RuinLayer;
                layerLogic.Initialize(ruinLayer.layerData, ruinLayer.layerView);
            }
            else if (layer.GetType() == typeof(CircleBorderLayer))
            {
                layerLogic = layer.gameObject.AddComponent<CircleBorderLayerLogic>();
                var borderLayer = layer as CircleBorderLayer;
                layerLogic.Initialize(borderLayer.layerData, borderLayer.layerView);
            }
            else if (layer.GetType() == typeof(RailwayLayer))
            {
                layerLogic = layer.gameObject.AddComponent<RailwayLayerLogic>();
                var railwayLayer = layer as RailwayLayer;
                layerLogic.Initialize(railwayLayer.layerData, railwayLayer.layerView);
            }
            else if (layer.GetType() == typeof(ModelLayer))
            {
                layerLogic = layer.gameObject.AddComponent<ModelLayerLogic>();
                var modelLayer = layer as ModelLayer;
                layerLogic.Initialize(modelLayer.layerData, modelLayer.layerView);
            }
            else if (layer.GetType() == typeof(SpriteTileLayer))
            {
                layerLogic = layer.gameObject.AddComponent<SpriteTileLayerLogic>();
                var spriteTileLayer = layer as SpriteTileLayer;
                layerLogic.Initialize(spriteTileLayer.layerData, spriteTileLayer.layerView);
            }
            else if (layer.GetType() == typeof(NavMeshLayer))
            {
                layerLogic = layer.gameObject.AddComponent<NavMeshLayerLogic>();
                var navMeshLayer = layer as NavMeshLayer;
                layerLogic.Initialize(navMeshLayer.layerData, navMeshLayer.layerView);
            }
            else if (layer.GetType() == typeof(NPCRegionLayer))
            {
                layerLogic = layer.gameObject.AddComponent<NPCRegionLayerLogic>();
                var npcLayer = layer as NPCRegionLayer;
                layerLogic.Initialize(npcLayer.layerData, npcLayer.layerView);
            }
            else if (layer.GetType() == typeof(BlendTerrainLayer))
            {
                layerLogic = layer.gameObject.AddComponent<BlendTerrainLayerLogic>();
                var terrainLayer = layer as BlendTerrainLayer;
                layerLogic.Initialize(terrainLayer.layerData, terrainLayer.layerView);
            }
            else if (layer.GetType() == typeof(SplitFogLayer))
            {
                layerLogic = layer.gameObject.AddComponent<SplitFogLayerLogic>();
                var fogLayer = layer as SplitFogLayer;
                layerLogic.Initialize(fogLayer.layerData, fogLayer.layerView);
            }
            else if (layer.GetType() == typeof(EntitySpawnLayer))
            {
                layerLogic = layer.gameObject.AddComponent<EntitySpawnLayerLogic>();
                var entityLayer = layer as EntitySpawnLayer;
                layerLogic.Initialize(entityLayer.layerData, entityLayer.layerView);
            }
            else if (layer.GetType() == typeof(LODLayer))
            {
                layerLogic = layer.gameObject.AddComponent<LODLayerLogic>();
                var lodLayer = layer as LODLayer;
                layerLogic.Initialize(lodLayer.GetLayerData(), lodLayer.layerView);
            }
            else if (layer.GetType() == typeof(MapCollisionLayer))
            {
                layerLogic = layer.gameObject.AddComponent<MapCollisionLayerLogic>();
                var collisionLayer = layer as MapCollisionLayer;
                layerLogic.Initialize(collisionLayer.GetLayerData(), collisionLayer.layerView);
            }
            else if (layer.GetType() == typeof(PolygonRiverLayer))
            {
                layerLogic = layer.gameObject.AddComponent<PolygonRiverLayerLogic>();
                var riverLayer = layer as PolygonRiverLayer;
                layerLogic.Initialize(riverLayer.GetLayerData(), riverLayer.layerView);
            }
            else if (layer.GetType() == typeof(RegionColorLayer))
            {
                layerLogic = layer.gameObject.AddComponent<RegionColorLayerLogic>();
                var regionLayer = layer as RegionColorLayer;
                layerLogic.Initialize(regionLayer.GetLayerData(), regionLayer.layerView);
            }
            else if (layer.GetType() == typeof(CameraColliderLayer))
            {
                layerLogic = layer.gameObject.AddComponent<CameraColliderLayerLogic>();
                var colliderLayer = layer as CameraColliderLayer;
                layerLogic.Initialize(colliderLayer.GetLayerData(), colliderLayer.layerView);
            }
            else if (layer.GetType() == typeof(EditorComplexGridModelLayer))
            {
                layerLogic = layer.gameObject.AddComponent<ComplexGridModelLayerLogic>();
                var gridLayer = layer as EditorComplexGridModelLayer;
                layerLogic.Initialize(gridLayer.GetLayerData(), gridLayer.layerView);
            }
            else if (layer.GetType() == typeof(EditorTerritoryLayer))
            {
                layerLogic = layer.gameObject.AddComponent<EditorTerritoryLayerLogic>();
                var gridLayer = layer as EditorTerritoryLayer;
                layerLogic.Initialize(gridLayer);
            }
            else if (layer.GetType() == typeof(RegionLayer))
            {
                layerLogic = layer.gameObject.AddComponent<RegionLayerLogic>();
                var regionLayer = layer as RegionLayer;
                layerLogic.Initialize(regionLayer);
            }
            else if (layer.GetType() == typeof(BuildingGridLayer))
            {
                layerLogic = layer.gameObject.AddComponent<BuildingGridLayerLogic>();
                var gridLayer = layer as BuildingGridLayer;
                layerLogic.Initialize(gridLayer);
            }
            else if (layer.GetType() == typeof(DecorationBorderLayer))
            {
                layerLogic = layer.gameObject.AddComponent<ComplexGridModelLayerLogic>();
                var gridLayer = layer as DecorationBorderLayer;
                layerLogic.Initialize(gridLayer);
            }
            else if (layer.GetType() == typeof(VaryingTileSizeTerrainLayer))
            {
                layerLogic = layer.gameObject.AddComponent<VaryingTileSizeTerrainLayerLogic>();
                var gridLayer = layer as VaryingTileSizeTerrainLayer;
                layerLogic.Initialize(gridLayer.GetLayerData(), gridLayer.layerView);
            }
            else
            {
                if (!MapPlugin.IsPluginLayer(layer.name))
                {
                    Debug.Assert(false, $"unknown map layer {layer.name}");
                }
            }
        }
    }
}

#endif