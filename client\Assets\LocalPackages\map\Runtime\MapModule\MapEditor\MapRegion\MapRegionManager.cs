﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

namespace TFW.Map
{
    //管理地图每个区域的障碍物
    public class MapRegionManager
    {
        public void CreateRegions(float startX, float startZ, float mapWidth, float mapHeight, float regionWidth, float regionHeight, float obstacleRadius, PrefabOutlineType type, CheckMapCollisionOperation operation, bool generateNPCSpawnPointsInBorderLine, bool calculateArea)
        {
            CreateRegionsMultithread(startX, startZ, mapWidth, mapHeight, regionWidth, regionHeight, obstacleRadius, type, operation, generateNPCSpawnPointsInBorderLine, calculateArea);
        }

        List<List<Vector3>> GetBorderLinePolygons(float borderLineShrinkRadius)
        {
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            //获取特殊区域
            List<MapCollisionData> borderLine = new List<MapCollisionData>();
            if (collisionLayer != null)
            {
                collisionLayer.GetCollisionsOfType(borderLine, CollisionAttribute.BorderLine);
            }

            //求正方形区域与特殊区域的交集
            var specialRegionPolygons = Utils.CreatePolygonsFromObstacles(borderLine, PrefabOutlineType.NavMeshObstacle, borderLineShrinkRadius);
            return specialRegionPolygons;
        }

        Task<List<MapRegion>> CreateTask(int minRegionIndex, int maxRegionIndex, float startX, float startZ, List<List<Vector3>> borderLinePolygons, float obstacleRadius, bool calculateArea)
        {
            //将障碍物分区域划分
            var task = Task<List<MapRegion>>.Run(() =>
            {
                List<MapRegion> regions = new List<MapRegion>();
                for (int k = minRegionIndex; k <= maxRegionIndex; ++k)
                {
                    int i = k / mHorizontalRegionCount;
                    int j = k % mHorizontalRegionCount;
                    var min = new Vector2(j * mRegionSize.x + startX, i * mRegionSize.y + startZ);
                    var max = min + mRegionSize;
                    //StopWatchWrapper w = new StopWatchWrapper();
                    //w.Start();
                    var obstacleTriangles = mObstacleTree.GetIntersectedTriangles(min.x, min.y, max.x, max.y);
                    //var time0 = w.Stop();
                    //UnityEngine.Debug.LogError($"time0: {time0}");
                    //w.Start();
                    var region = new MapRegion(k, min, mRegionSize, obstacleTriangles, vertices, borderLinePolygons, obstacleRadius, calculateArea);
                    //var time1 = w.Stop();
                    //UnityEngine.Debug.LogError($"time1: {time1}");
                    regions.Add(region);
                }
                return regions;
            });
            return task;
        }

        public void CreateRegionsMultithread(float startX, float startZ, float mapWidth, float mapHeight, float regionWidth, float regionHeight, float obstacleRadius, PrefabOutlineType type, CheckMapCollisionOperation operation, bool generateNPCSpawnPointsInBorderLine, bool calculateArea)
        {
            mStartOffset = new Vector2(startX, startZ);
            mMapSize = new Vector2(mapWidth, mapHeight);
            mRegionSize = new Vector2(regionWidth, regionHeight);
            if (obstacleRadius < 0)
            {
                obstacleRadius = 0;
            }
            mObstacleRadius = obstacleRadius;

            mHorizontalRegionCount = Mathf.CeilToInt(mMapSize.x / mRegionSize.x);
            mVerticalRegionCount = Mathf.CeilToInt(mMapSize.y / mRegionSize.y);

            var watch = new StopWatchWrapper();
            watch.Start();
            //创建地图上所有障碍物的集合
            CreateObstacleQuadTree(type, operation);
            double time = watch.Stop($"CreateObstacleQuadTree, region count: {mVerticalRegionCount * mHorizontalRegionCount}");
            //UnityEngine.Debug.Log("CreateObstacleQuadTree: " + time);

            watch.Start();
            int regionCount = mVerticalRegionCount * mHorizontalRegionCount;
            mRegions = new MapRegion[regionCount];
            //Task<MapRegion>[] tasks = new Task<MapRegion>[regionCount];
            //int taskIdx = 0;

            List<List<Vector3>> borderLinePolygons = null;
            if (generateNPCSpawnPointsInBorderLine)
            {
                borderLinePolygons = GetBorderLinePolygons(obstacleRadius);
            }

            int threadCount = System.Environment.ProcessorCount;
            int regionCountPerThread = Mathf.CeilToInt(regionCount / (float)threadCount);

            List<Task<List<MapRegion>>> tasks = new List<Task<List<MapRegion>>>();
            for (int i = 0; i < threadCount; ++i)
            {
                int min = i * regionCountPerThread;
                int max = Mathf.Min(min + regionCountPerThread - 1, regionCount - 1);
                var task = CreateTask(min, max, startX, startZ, borderLinePolygons, obstacleRadius, calculateArea);
                tasks.Add(task);
            }

            Task.WaitAll(tasks.ToArray());
            int offset = 0;
            for (int i = 0; i < tasks.Count; ++i)
            {
                var regions = tasks[i].Result;
                for (int k = 0; k < regions.Count; ++k) {
                    mRegions[offset] = regions[k];
                    ++offset;
                }
            }

            UnityEngine.Debug.Assert(offset == regionCount);
            time = watch.Stop("Create Regions");
            UnityEngine.Debug.Log("Create Regions Multithread: " + time);
        }

        //decorationObjectsAsObstacle:是否将装饰物也算作障碍物
        void CreateObstacleQuadTree(PrefabOutlineType type, CheckMapCollisionOperation operation)
        {
            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start();
            var map = Map.currentMap;

            var layerMask = LayerTypeMask.kGridModelLayer | LayerTypeMask.kRailwayLayer | LayerTypeMask.kModelLayer | LayerTypeMask.kCollisionLayer | LayerTypeMask.kComplexGridModelLayer;
            var obstacles = Utils.CreateObstacles(layerMask, false, type, operation, false, false, true);

            List<IObstacle> obs = new List<IObstacle>();
            for (int i = 0; i < obstacles.Count; ++i)
            {
                if (obstacles[i].IsSimplePolygon(type))
                {
                    obs.Add(obstacles[i]);
                }
            }

            var mapDataGenerationRange = Map.currentMap.data.mapDataGenerationRange;
            //todo,这里没有同时考虑mapDataGenerationRange和mStartOffset+mMapSize
            //Rect intersection;
            //Utils.GetRectIntersection(mapDataGenerationRange, new Rect(mStartOffset, mMapSize), out intersection);

            Vector3[] vertices;
            int[] indices;
            ushort[] triangleTypes;
            bool[] triangleStates;
            var editorMap = Map.currentMap as EditorMap;
            NavMeshCreator.CreateNavMesh(type, mapDataGenerationRange.min, mapDataGenerationRange.max, obs, mObstacleRadius, 100, 180000, false, NavigationCreateMode.CreateLandObstacles, true, 1.0f, editorMap.removeSameHoles, out vertices, out indices, out triangleTypes, out triangleStates);
            if (vertices != null)
            {
                mObstacleTree.Create("map region manager", mStartOffset, mMapSize, vertices, indices, 0, indices != null ? indices.Length - 1 : 0, 20, false, false, false);
            }

#if false
            //temp code
            BigMeshViewer viewer = new BigMeshViewer();
            viewer.Create(null, "CreateObstacleQuadTree", vertices, indices, false, Color.yellow);
#endif
            stopWatch.Stop();

            long elapsed = stopWatch.ElapsedMilliseconds;

            UnityEngine.Debug.Log("Creat Spawn points NavMesh cost " + elapsed + " ms");
        }

        public Vector3[] vertices { get { return mObstacleTree.vertices; } }
        public MapRegion[] regions { get { return mRegions; } }

        Vector2 mRegionSize;
        Vector2 mMapSize;
        Vector2 mStartOffset;
        int mHorizontalRegionCount;
        int mVerticalRegionCount;
        float mObstacleRadius;
        TriangleQuadTree mObstacleTree = new TriangleQuadTree();
        MapRegion[] mRegions;
    }
}

#endif