﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map {
    public delegate void OnKeyPressed(KeyCode keyCode, bool ctrl, bool shift);

    [Black]
    public static class KeyboardEventListener {
        public static event OnKeyPressed keyPressedEvent;

        static KeyboardEventListener() {
            BindKeys();
        }

        static void BindKeys() {
            System.Reflection.FieldInfo info = typeof(EditorApplication).GetField("globalEventHandler", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);
            EditorApplication.CallbackFunction value = (EditorApplication.CallbackFunction)info.GetValue(null);
            value += OnKeyPressed;
            info.SetValue(null, value);
        }

        static void OnKeyPressed() { 
            if (Event.current.type == EventType.KeyDown) {
                if (!IsUtilityKeys(Event.current)) {
                    if (mKeyIsDown == false) {
                        mKeyIsDown = true;
                        if (keyPressedEvent != null) {
                            var e = Event.current;
                            keyPressedEvent(Event.current.keyCode, Event.current.control, Event.current.shift);
                        }
                    }
                }
            }
            else if (Event.current.type == EventType.KeyUp) {
                if (!IsUtilityKeys(Event.current)) {
                    mKeyIsDown = false;
                }
            }

        }

        static bool IsUtilityKeys(Event e) {
            if (!e.control && !e.shift && !e.alt) {
                return false;
            }
            return true;
        }

        static bool mKeyIsDown = false;
    }
}

#endif