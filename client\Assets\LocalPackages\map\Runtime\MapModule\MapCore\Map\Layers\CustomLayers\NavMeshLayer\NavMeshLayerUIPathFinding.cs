﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class NavMeshLayerUI : UnityEditor.Editor
    {
        void ResetPathFinding()
        {
            mStartPoint = Vector3.zero;
            mEndPoint = Vector3.zero;
            mState = PlacePointState.WaitingForStartPos;
            mPaths = null;
            Repaint();
            SceneView.RepaintAll();
        }

        void SetStartPos(float x, float z)
        {
            mStartPoint = new Vector3(x, 0, z);
            mState = PlacePointState.WaitingForEndPos;
            Repaint();
            SceneView.RepaintAll();
        }

        void SetEndPos(float x, float z)
        {
            mEndPoint = new Vector3(x, 0, z);
            mState = PlacePointState.ReadyForPathFinding;
            Repaint();
            SceneView.RepaintAll();
        }

        void FindPath()
        {
            if (mState == PlacePointState.ReadyForPathFinding)
            {
                var path = mLogic.layer.FindPath(Utils.ToVector2(mStartPoint), Utils.ToVector2(mEndPoint));
                if (path != null && path.Count > 0)
                {
                    mPaths = new Vector3[path.Count];
                    for (int i = 0; i < path.Count; ++i)
                    {
                        mPaths[i] = Utils.ToVector3(path[i]);
                    }
                }
                else
                {
                    mPaths = null;
                }
                Repaint();
                SceneView.RepaintAll();
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select Start And End Point first!", "OK");
            }
        }

        void DrawPathInfo()
        {
            if (mPaths != null)
            {
                Handles.color = Color.white;
                Handles.DrawPolyLine(mPaths);
            }

            if (mState == PlacePointState.WaitingForEndPos)
            {
                Handles.color = Color.green;
                Handles.SphereHandleCap(0, mStartPoint, Quaternion.identity, mDisplayRadius, EventType.Repaint);
            }
            else if (mState == PlacePointState.ReadyForPathFinding)
            {
                Handles.color = Color.green;
                Handles.SphereHandleCap(0, mStartPoint, Quaternion.identity, mDisplayRadius, EventType.Repaint);
                Handles.color = Color.red;
                Handles.SphereHandleCap(0, mEndPoint, Quaternion.identity, mDisplayRadius, EventType.Repaint);
            }
        }

        void DrawPathFindingUI()
        {
            EditorGUILayout.BeginHorizontal("GroupBox");
            mDisplayRadius = EditorGUILayout.FloatField("Display Radius", mDisplayRadius);
            if (mDisplayRadius <= 0)
            {
                mDisplayRadius = 1.0f;
            }
            if (GUILayout.Button("Find Path"))
            {
                FindPath();
            }
            EditorGUILayout.EndHorizontal();
            mDebugPathFinding = EditorGUILayout.ToggleLeft("Debug", mDebugPathFinding);

            if (mDebugPathFinding)
            {
                DrawPathFindingDebugUI();
            }
        }

        void DrawPathFindingDebugUI()
        {
            EditorGUILayout.BeginVertical("GroupBox");

            //triangles
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Show All Triangles"))
            {
                mLogic.layer.navigationDebugger?.ShowAllTriangles();
            }
            if (GUILayout.Button("Hide All Triangles"))
            {
                mLogic.layer.navigationDebugger?.HideAllTriangles();
            }
            EditorGUILayout.EndHorizontal();

            mHighlightTriangle = EditorGUILayout.ToggleLeft("Highlight Triangle", mHighlightTriangle);

            //convexes
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Show All Convexes"))
            {
                mLogic.layer.navigationDebugger?.ShowAllConvexs();
            }
            if (GUILayout.Button("Hide All Convexes"))
            {
                mLogic.layer.navigationDebugger?.HideAllConvexs();
            }
            EditorGUILayout.EndHorizontal();

            mHighlightConvex = EditorGUILayout.ToggleLeft("Highlight Convex", mHighlightConvex);

            EditorGUILayout.EndVertical();
        }

        PlacePointState mState = PlacePointState.WaitingForStartPos;
        Vector3 mStartPoint;
        Vector3 mEndPoint;
        Vector3[] mPaths;
        float mDisplayRadius = 10.0f;
        bool mDebugPathFinding = true;
        bool mHighlightTriangle = false;
        bool mHighlightConvex = false;
    }
}

#endif