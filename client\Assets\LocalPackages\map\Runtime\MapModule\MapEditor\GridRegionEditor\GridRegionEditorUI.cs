﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    public enum GridRegionOperation
    {
        Create,
        Clear,
    }

    [CustomEditor(typeof(GridRegionEditorLogic))]
    public partial class GridRegionEditorUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as GridRegionEditorLogic;
        }

        void OnDisable()
        {
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                if (mLogic.selectedIndex == -1 && mLogic.editor.templateCount > 0)
                {
                    mLogic.selectedIndex = 0;
                }

                //EditorGUILayout.BeginHorizontal("GroupBox");
                //if (GUILayout.Button(mSaveIcon))
                //{
                //    ExportPatch(mLogic.editor);
                //}
                //if (GUILayout.Button(mLoadIcon))
                //{
                //    LoadPatch(mLogic.editor);
                //}
                //EditorGUILayout.EndHorizontal();

                mLogic.operation = (GridRegionOperation)EditorGUILayout.EnumPopup("Operation", mLogic.operation);

                mLogic.brushSize = EditorGUILayout.IntField("Brush Size", mLogic.brushSize);
                mLogic.brushSize = Mathf.Clamp(mLogic.brushSize, 1, 200);

                var editor = mLogic.editor;
                bool tileActive = EditorGUILayout.ToggleLeft("Show Tiles", editor.active);
                if (tileActive != editor.active)
                {
                    editor.active = tileActive;
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Clear All Grids"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure? this operation can't be undone!", "Yes", "No"))
                    {
                        ClearAllGrids();
                    }
                }
                if (GUILayout.Button("Export"))
                {
                    editor.Export();
                }
#if false
                //只有c5用
                if (GUILayout.Button("Load Gates"))
                {
                    LoadGate();
                }
#endif
                EditorGUILayout.EndHorizontal();

                if (mLogic.operation == GridRegionOperation.Create)
                {
                    DrawGridTemplates();
                }

                EditorGUILayout.LabelField("Grid Width", "10");
                EditorGUILayout.LabelField("Grid Height", "10");
                EditorGUILayout.TextArea("Accelerator Keys:\n 'Left Mouse Button' to paint.\n 'Ctrl + Left Mouse Button' to erase.\n 'Up Arrow' and 'Down Arrow' to change brush size.");
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);
            var editor = mLogic.editor;

            if (mLeftButtonDown)
            {
                if (mLogic.operation == GridRegionOperation.Create)
                {
                    if (mLogic.selectedIndex >= 0)
                    {
                        int type = 0;
                        if (!currentEvent.control)
                        {
                            type = editor.templates[mLogic.selectedIndex].type;
                        }
                        editor.SetGrid(pos, mLogic.brushSize, type);
                    }
                }
                else
                {
                    editor.SetGrid(pos, mLogic.brushSize, 0);
                }
            }

            if (currentEvent.type == EventType.KeyDown)
            {
                if (currentEvent.keyCode == KeyCode.UpArrow)
                {
                    mLogic.brushSize = mLogic.brushSize + 1;
                    Repaint();
                }
                else if (currentEvent.keyCode == KeyCode.DownArrow)
                {
                    mLogic.brushSize = mLogic.brushSize - 1;
                    Repaint();
                }
            }

            var coord = editor.FromPositionToCoordinate(pos);
            DrawBrush(coord);

            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void DrawBrush(Vector2Int centerCoord)
        {
            int brushSize = mLogic.brushSize;
            int startX = centerCoord.x - brushSize / 2;
            int startY = centerCoord.y - brushSize / 2;
            int endX = startX + brushSize;
            int endY = startY + brushSize;
            var startPos = mLogic.editor.FromCoordinateToPosition(startX, startY);
            var endPos = mLogic.editor.FromCoordinateToPosition(endX, endY);

            Handles.color = Color.white;
            Handles.DrawWireCube((startPos + endPos) * 0.5f, endPos - startPos);
        }

        void ClearAllGrids()
        {
            var editor = mLogic.editor;
            int horizontalGridCount = editor.horizontalGridCount;
            int verticalGridCount = editor.verticalGridCount;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    editor.SetGridData(j, i, 0);
                }
            }

            editor.RefreshTexture();
        }

        GridRegionEditorLogic mLogic;
        bool mLeftButtonDown = false;
    }
}

#endif