﻿using Common;
using cspb;
using DeepUI;
using Logic;
using TFW.UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 主界面简单按钮
    /// </summary>
    public partial class UIMain2
    {

        #region 属性字段信息

        

        /// <summary>
        /// 收藏按钮对象
        /// </summary> 
        private GameObject favoriteObj;

        /// <summary>
        /// 拉高大地图相机按钮对象
        /// </summary>
        //[PopupField("content/MapEnlarge/composeButton")]
        //private GameObject drawHightMapObj;

        /// <summary>
        /// 主动塔防出兵按钮
        /// </summary>
        //[PopupField("content/NewIllustrated1")]
        //private GameObject NewIllustrated1Obj;

        /// <summary>
        /// 拉低大地图相机按钮对象
        /// </summary>
        //[PopupField("content/MapNarrow/composeButton")]
        //private GameObject drawDownMapObj;

        ///// <summary>
        ///// 搜索按钮对象
        ///// </summary>
        //[PopupField("content/Root/Btn/BtnOutside/ButtonOther/Buttom")]
        //private GameObject searchObj;

        [PopupField("content/LeftNewBtns/search")]
        private GameObject btnSearch;

        [PopupField("content/UpperRoot/SideRoot/Left_Top/power/Image/btnRank")]
        private GameObject btn_Rank;

        [PopupField("content/UpperRoot/SideRoot/Left_Top/power/Image/btnRank/icon")]
        private TFWImage rank_icon;

        [PopupField("content/UpperRoot/SideRoot/Left_Top/Grid/QuestionnaireBtn")]
        private GameObject btn_question;//问券按钮

        [PopupField("content/UpperRoot/SideRoot/Left_Top/Grid/QuestionnaireBtn/Icon/RedDot")]
        private GameObject btn_question_redPoint;

        #endregion


        #region 初始化

        /// <summary>
        /// 初始化按钮
        /// </summary>
        private void InitButton()
        {
            AddButtonListener();
            //EventMgr.RegisterEvent(TEventType.ShowRestartLevel, (isOpen) =>
            //{
            //    UIHelper.SafeSetActive(NewIllustrated1Obj, (bool)isOpen[0]);
            //}, this);

            RefreshQuestionbtn();

            SDKManager.instance.InfoGameButtonRefreshCallBack= RefreshQuestionbtn;
        }

     
        private void RefreshQuestionbtn()
        {
            btn_question.SetActive(false);
            foreach (var item in SDKManager.instance.InfoGameButtons)
            {
                if (item.gameButtonId == 1)
                {
                    btn_question.SetActive(true);
                    btn_question_redPoint.SetActive(item.showRedPoint);

                    UIBase.AddRemoveListener(TFW.EventTriggerType.Click, btn_question, (x, y) =>
                    {
                        SDKManager.instance.SDKOpenLink(item.url);
                    });
                }
            }
            
        }

        /// <summary>
        /// 反向初始化按钮
        /// </summary>
        private void UnInitButton()
        {
            RemoveButtonListener();
            EventMgr.UnregisterEvent(TEventType.ShowRestartLevel, this);
        }

        /// <summary>
        /// 添加按钮监听事件
        /// </summary>
        private void AddButtonListener()
        {
            //点击收藏按钮
            BindClickListener(btn_Rank, OnClickRankBtn);
            //BindClickListener(NewIllustrated1Obj, OnClickRestart);
            //UIHelper.SafeSetActive(NewIllustrated1Obj, false);
            ////点击搜索按钮
            //BindClickListener(searchObj, OnClickSearchBtn);

            BindClickListener(btnSearch, OnClickSearchEnemyBtn);
        }

        private void OnClickRankBtn(GameObject arg0, PointerEventData arg1)
        {
            PopupManager.I.ShowPanel<UIPlayerInfo_K1>("UIPlayerInfo_K1");
        }

        private void OnClickSearchEnemyBtn(GameObject arg0, PointerEventData arg1)
        {
            PopupManager.I.ShowPanel<UISearch>();
        }

        //[PopupEvent(TEventType.RankListAck)]
        //private void UpdateSelfRank(object[] objs)
        //{
        //    var rankItem = RankNewMgr.I.GetSelfRank(RankType.RankTypePower);
        //    if (rankItem != null)
        //    {
        //        if (rankItem.place < 10)
        //        {
        //            UITools.SetImageBySpriteName(rank_icon, $"UI_Main_Rank_Img_{rankItem.place}");
        //        }
        //        else
        //        {
        //            UITools.SetImageBySpriteName(rank_icon, $"UI_Main_Rank_Img_10");
        //        }
        //    }
        //    else
        //    {
        //        UITools.SetImageBySpriteName(rank_icon, $"UI_Main_Rank_Img_10");
        //    }

        //}

        /// <summary>
        /// 移除按钮监听事件
        /// </summary>
        private void RemoveButtonListener()
        {
            //移除收藏绑定事件
            RemoveClickListener(favoriteObj);
            ////移除搜索绑定事件
            //RemoveClickListener(searchObj);
        }

        #endregion

        #region 事件监听

        

        /// <summary>
        /// 点击收藏夹
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickFavoritesBtn(GameObject arg0, PointerEventData arg1)
        {
            UIFavoritesPanel.Show();
        }

        private async void OnClickRestart(GameObject arg0, PointerEventData arg1)
        {
            //await GameLevelManager.I.StartLevel();
            //UIHelper.SafeSetActive(NewIllustrated1Obj, false);
        }


        /// <summary>
        /// 点击搜索按钮对象
        /// </summary>
        /// <param name="arg0"></param>
        /// <param name="arg1"></param>
        private void OnClickSearchBtn(GameObject arg0, PointerEventData arg1)
        {
            PopupManager.I.ShowPanel<UICoordSearch>("UiInputSearch");
        }
        #endregion

    }
}
