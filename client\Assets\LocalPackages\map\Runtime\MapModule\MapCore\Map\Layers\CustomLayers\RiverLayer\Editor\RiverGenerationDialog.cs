﻿ 



 
 



//created by wzw at 2019/11/27

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RiverGenerationParameter
    {
        public string mtlPath;
        public int textureSize;
    }
    //河流生成的界面
    [Black]
    public class RiverGenerationDialog : EditorWindow
    {
        public void Show(System.Func<RiverGenerationParameter, bool> OnClickOK, Material defaultMaterial)
        {
            mOnClickOK = OnClickOK;
            mMaterial = defaultMaterial;
        }

        void OnGUI()
        {
            EditorGUILayout.BeginVertical();
            mMaterial = EditorGUILayout.ObjectField("River Material", mMaterial, typeof(Material), false) as Material;
            mTextureSize = EditorGUILayout.IntField("River Texture Size", mTextureSize);
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("OK"))
            {
                if (mOnClickOK != null)
                {
                    var param = CreateParameter();
                    if (param != null)
                    {
                        if (mOnClickOK(param))
                        {
                            Close();
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Invalid parameter!", "OK");
                    }
                }
                else
                {
                    Close();
                }
            }
        }

        RiverGenerationParameter CreateParameter()
        {
            if (mMaterial == null)
            {
                return null;
            }
            if (mTextureSize <= 0 || !Utils.IsPOT(mTextureSize))
            {
                return null;
            }

            var param = new RiverGenerationParameter();
            param.mtlPath = AssetDatabase.GetAssetPath(mMaterial);
            param.textureSize = mTextureSize;
            return param;
        }

        Material mMaterial;
        int mTextureSize = 512;
        System.Func<RiverGenerationParameter, bool> mOnClickOK;
    }
}

#endif