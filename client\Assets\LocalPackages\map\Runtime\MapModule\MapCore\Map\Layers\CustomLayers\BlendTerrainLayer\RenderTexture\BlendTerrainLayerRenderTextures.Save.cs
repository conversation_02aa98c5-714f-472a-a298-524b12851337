﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using UnityEditor;

namespace TFW.Map
{
    public partial class BlendTerrainLayerRenderTextures
    {
        public void Save(string projectFolder)
        {
            if (mBigTileRenderTextures != null)
            {
                string textureFolder = $"{projectFolder}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME}";
                FileUtil.DeleteFileOrDirectory(textureFolder);
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                if (!Directory.Exists(textureFolder))
                {
                    Directory.CreateDirectory(textureFolder);
                }

                //save meta data
                using MemoryStream stream = new MemoryStream();
                using BinaryWriter writer = new BinaryWriter(stream);

                writer.Write(VersionSetting.RenderTextureFileVersion);
                writer.Write(mBigTileCount);

                string filePath = $"{projectFolder}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_FOLDER_NAME}/{MapCoreDef.BLEND_TERRAIN_LAYER_RENDER_TEXTURE_DATA_NAME}";
                var data = stream.ToArray();
                File.WriteAllBytes(filePath, data);
                writer.Close();

                //save textures
                Texture2D tex = new Texture2D(mTextureSize, mTextureSize, TextureFormat.RGBA32, false);
                for (int i = 0; i < mBigTileRenderTextures.Length; ++i)
                {
                    RenderTexture.active = mBigTileRenderTextures[i];
                    tex.ReadPixels(new Rect(0, 0, tex.width, tex.height), 0, 0);
                    tex.Apply();

                    string texturePath = $"{textureFolder}/render_texture_{i}.png";
                    var bytes = tex.EncodeToPNG();
                    File.WriteAllBytes(texturePath, bytes);
                }
            }
        }
    }
}


#endif