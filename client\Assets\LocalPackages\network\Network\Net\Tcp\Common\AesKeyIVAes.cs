﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using Net.Common;
using Net.Tcp.Client;
using System;
using System.Net.Sockets;
using UnityEngine;

namespace Net.Tcp.Common
{
    class AesKeyIVAes : IIOCP
    {
        enum ReadState
        {
            Head = 0,
            Body = 1,
        }
        public AesKeyIVAes(TCPClient.SocketArgs args, Action<TCPClient.SocketArgs,byte[]> conn, Action<TCPClient.SocketArgs> close, byte[] kiv)
        {
            if(AesKeyIV.USE_KEY_V1)
            {
                m_KeyIV = new byte[AesKeyIV.KeyIVLen_V0];
                Array.Copy(kiv, 1, m_KeyIV, 0, AesKeyIV.KeyIVLen_V0);
            }
            else
            {
                m_KeyIV = kiv;
            }

            
            m_HandleConnected = conn;
            m_HandleClose = close;
            m_SocketArgs = args;
            Socket = m_SocketArgs.m_Socket;
            m_ReadState = ReadState.Head;
            IOCPConn.Start(this);
        }
        Action<TCPClient.SocketArgs,byte[]> m_HandleConnected;
        Action<TCPClient.SocketArgs> m_HandleClose;
        byte[] m_KeyIV;
        TCPClient.SocketArgs m_SocketArgs;
        ReadState m_ReadState;

        public Socket Socket { get; private set; }

        public void IOCPInitialize(SocketAsyncEventArgs e)
        {
            e.SetBuffer(0, 4);
        }
        public bool IOCPReceived(SocketAsyncEventArgs e)
        {
            if (e.UserToken != this) return false;
            var count = e.Count - e.BytesTransferred;
            if (m_ReadState == ReadState.Head)
            {
                if (count == 0)
                {
                    //解包大小
                    var len = NetHelper.ToInt32(e.Buffer, 0);
                    if (len != e.Buffer.Length)
                    {
                        BufferManager.Free(e);
                        var buffer = new byte[len];
                        e.SetBuffer(buffer, 0, len);
                    }
                    else
                    {
                        e.SetBuffer(0, len);
                    }
                    m_ReadState = ReadState.Body;
                }
                else
                {
                    var offset = e.BytesTransferred + e.Offset;
                    e.SetBuffer(offset, count);
                }
            }
            else
            {
                if (count == 0)
                {
                    //解Aes Key IV
                    var decrypt = new AesDecryptor(m_KeyIV, m_KeyIV, AesKeyIV.USE_KEY_V1);
                    var bytes = decrypt.Decrypt(e.Buffer);
                    if (!AesKeyIV.Check(bytes))
                    {
                        Debug.LogError(string.Format("Aes Key IV len error {0}", bytes.Length));
                        IOCPConn.Close(e);
                        return false;
                    }
                    else
                    {
                        m_HandleConnected?.Invoke(m_SocketArgs, bytes);
                        return false;
                    }
                }
                else
                {
                    var offset = e.BytesTransferred + e.Offset;
                    e.SetBuffer(offset, count);
                }
            }
            return true;
        }

        public void IOCPClose()
        {
            Socket = null;
            m_HandleClose?.Invoke(m_SocketArgs);
        }
    }
}

#endif