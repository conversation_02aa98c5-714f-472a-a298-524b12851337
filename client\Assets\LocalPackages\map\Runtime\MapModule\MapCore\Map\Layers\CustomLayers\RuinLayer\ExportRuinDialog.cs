﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ExportRuinDialog : EditorWindow
    {
        public void Init(int layerID, int startID, float startX, float startZ, float bigGridWidth, float bigGridHeight, int horizontalBigGridCount, int verticalBigGridCount)
        {
            mLayerID = layerID;
            mStartID = startID;
            mStartX = startX;
            mStartZ = startZ;
            mBigGridWidth = bigGridWidth;
            mBigGridHeight = bigGridHeight;
            mHorizontalBigGridCount = horizontalBigGridCount;
            mVerticalBigGridCount = verticalBigGridCount;
        }

        void OnGUI()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RuinLayer;
            var ruinSettings = layer.ruinObjectTypes;

            if (mRuinCheckStates == null)
            {
                mRuinCheckStates = new bool[ruinSettings.Count];
            }

            for (int i = 0; i < ruinSettings.Count; ++i)
            {
                DrawRuinSetting(i, ruinSettings[i]);
            }

            EditorGUILayout.BeginVertical("GroupBox");
            bool selectAll = EditorGUILayout.ToggleLeft("Select All", mSelectAll);
            layer.exportAsGridData = EditorGUILayout.ToggleLeft("Export As Grid Data", layer.exportAsGridData);
            layer.exportInHierarchyOrder = EditorGUILayout.ToggleLeft(new GUIContent("Export In Hierarchy Order", "是否按照物体在Hierarchy界面中的顺序导出"), layer.exportInHierarchyOrder);
            EditorGUILayout.EndHorizontal();
            if (mSelectAll != selectAll)
            {
                mSelectAll = selectAll;
                for (int i = 0; i < mRuinCheckStates.Length; ++i)
                {
                    mRuinCheckStates[i] = selectAll;
                }
            }
            mStartID = EditorGUILayout.IntField("Start ID", mStartID);

            if (GUILayout.Button("Export"))
            {
                List<RuinSetting> exportRuinSettings = GetExportedRuinSettings();
                if (exportRuinSettings == null)
                {
                    EditorUtility.DisplayDialog("Error", "Can't export!", "OK");
                }
                else
                {
                    string ruinDataFilePath = SLGMakerEditor.instance.exportFolder + "/ruin_data.bytes";
                    //for test only
                    layer.ExportRuinDataForClient(exportRuinSettings, ruinDataFilePath);

                    string filePath = EditorUtility.SaveFilePanel("Select file", "", "ruin_data", "tsv");
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        if (layer.exportAsGridData)
                        {
                            layer.ExportGridData(exportRuinSettings, filePath, mStartID, mStartX, mStartZ, mBigGridWidth, mBigGridHeight, mHorizontalBigGridCount, mVerticalBigGridCount);
                        }
                        else
                        {
                            layer.ExportData(exportRuinSettings, filePath, mStartID);
                        }
                    }
                }
            }
        }

        List<RuinSetting> GetExportedRuinSettings()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RuinLayer;
            var ruinSettings = layer.ruinObjectTypes;
            if (!layer.shareProperties)
            {
                return ruinSettings;
            }

            List<RuinSetting> exportedRuinSettings = new List<RuinSetting>();

            for (int i = 0; i < mRuinCheckStates.Length; ++i)
            {
                if (mRuinCheckStates[i])
                {
                    exportedRuinSettings.Add(ruinSettings[i]);
                }
            }

            if (exportedRuinSettings.Count == 0)
            {
                return null;
            }

            var props = exportedRuinSettings[0].properties;
            for (int i = 1; i < exportedRuinSettings.Count; ++i)
            {
                if (!props.EqualTypeWith(exportedRuinSettings[i].properties))
                {
                    return null;
                }
            }

            return exportedRuinSettings;
        }

        void DrawRuinSetting(int i, RuinSetting ruinSetting)
        {
            mRuinCheckStates[i] = EditorGUILayout.ToggleLeft(ruinSetting.name, mRuinCheckStates[i]);
        }

        bool[] mRuinCheckStates;
        int mStartID;
        bool mSelectAll;
        //k1格式
        float mBigGridWidth;
        float mBigGridHeight;
        float mStartX;
        float mStartZ;
        int mHorizontalBigGridCount;
        int mVerticalBigGridCount;
        int mLayerID;
    }
}

#endif