﻿ 



 
 

﻿using System.Diagnostics;

namespace TFW.Map
{
    public class StopWatchWrapper
    {
        public StopWatchWrapper()
        {
            mWatch = new Stopwatch();
        }

        public void Start()
        {
            mWatch.Start();
        }

        public double Stop(string text)
        {
            mWatch.Stop();
            double elapsedTime = mWatch.ElapsedTicks / (double)Stopwatch.Frequency;
            mWatch.Reset();

            if (!string.IsNullOrEmpty(text))
            {
                //UnityEngine.Debug.Log(text + " elapsed time: " + elapsedTime);
            }
            return elapsedTime;
        }

        public double Stop()
        {
            mWatch.Stop();
            double elapsedSeconds = mWatch.ElapsedTicks / (double)Stopwatch.Frequency;
            mWatch.Reset();
            return elapsedSeconds;
        }

        Stopwatch mWatch;
    }
}