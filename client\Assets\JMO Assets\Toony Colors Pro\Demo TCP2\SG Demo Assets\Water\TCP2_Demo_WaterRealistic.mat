%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_WaterRealistic
  m_Shader: {fileID: 4800000, guid: b7c970a370a5f4041adb0953b37bb7ff, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 2800000, guid: c9de4a6ca27ab52479c42e95c4ec1194, type: 3}
        m_Scale: {x: 8, y: 4}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _FoamTex
      second:
        m_Texture: {fileID: 2800000, guid: 869319dead08c3b4a9c074af06b585f4, type: 3}
        m_Scale: {x: 4, y: 4}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Mask1
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DepthAlpha
      second: 1.5
    - first:
        name: _DepthDistance
      second: 0.6
    - first:
        name: _DepthMinAlpha
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _DstBlendTCP2
      second: 10
    - first:
        name: _FoamSmooth
      second: 0.078
    - first:
        name: _FoamSpread
      second: 2
    - first:
        name: _FoamStrength
      second: 0.8
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _NormalDepthInfluence
      second: 0.142
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.641
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _ReflRoughness
      second: 0
    - first:
        name: _ReflStrength
      second: 1
    - first:
        name: _RimMax
      second: 1
    - first:
        name: _RimMin
      second: 0
    - first:
        name: _Shininess
      second: 10
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _SrcBlendTCP2
      second: 5
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _UVWaveAmplitude
      second: 0.01
    - first:
        name: _UVWaveFrequency
      second: 0.5
    - first:
        name: _UVWaveSpeed
      second: 1.5
    - first:
        name: _WaveFrequency
      second: 2
    - first:
        name: _WaveHeight
      second: 0.2
    - first:
        name: _WaveSpeed
      second: 1
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    m_Colors:
    - first:
        name: _BumpSpeed
      second: {r: 0.2, g: 0.2, b: -0.2, a: -0.2}
    - first:
        name: _Color
      second: {r: 0.20891002, g: 0.45795533, b: 0.50735295, a: 0.8627451}
    - first:
        name: _DepthColor
      second: {r: 0.066733986, g: 0.29797322, b: 0.547, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _FoamColor
      second: {r: 0.9, g: 0.9, b: 0.9, a: 0.922}
    - first:
        name: _FoamSpeed
      second: {r: 1, g: 1, b: -1, a: -1}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _RimColor
      second: {r: 0.30147058, g: 0.2948205, b: 0.2948205, a: 1}
    - first:
        name: _SColor
      second: {r: 0.598128, g: 0.73157454, b: 0.816, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.1297578, g: 0.14705884, b: 0.14013842, a: 1}
