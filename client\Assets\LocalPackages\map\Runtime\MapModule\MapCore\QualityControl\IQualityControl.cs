﻿ 



 
 



namespace TFW.Map
{
    public interface IQualityControl
    {
        int GetCurrentQuality();
    }

    //用于根据质量等级来控制特殊装饰物的加载
    public static class QualityControl
    {
        public static void SetQualityControlImpl(IQualityControl impl)
        {
            mImpl = impl;
        }

        public static int GetCurrentQuality()
        {
            if (mImpl == null)
            {
                return -1;
            }
            return mImpl.GetCurrentQuality();
        }

        static IQualityControl mImpl;
    }
}