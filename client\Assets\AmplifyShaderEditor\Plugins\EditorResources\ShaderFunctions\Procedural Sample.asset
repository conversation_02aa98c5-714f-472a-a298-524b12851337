%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Procedural Sample
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18701\n339;100;906;513;-1593.459;91.35236;1;True;False\nNode;AmplifyShaderEditor.CommentaryNode;142;-1295.165,-358.8215;Inherit;False;2404.07;914.0666;;23;293;24;27;28;144;4;10;2;6;7;135;137;136;134;139;140;141;138;23;25;22;26;12;2D;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;160;-1287.924,722.2014;Inherit;False;2400.167;913.1149;;30;185;190;189;171;176;181;179;187;188;186;165;161;164;163;162;182;172;191;173;175;180;178;177;174;170;169;168;167;166;294;2D
    Array;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;198;-1266.038,4663.606;Inherit;False;2381.579;2608.276;;94;289;254;253;291;290;292;285;271;277;257;256;288;287;286;250;281;264;263;266;278;283;284;282;274;273;272;279;260;259;237;249;235;218;251;248;246;258;214;247;242;280;275;276;270;269;267;265;262;261;255;252;245;244;243;241;240;239;238;236;234;233;232;231;230;229;228;227;226;225;224;223;222;221;220;219;217;216;215;213;212;211;210;209;208;207;206;205;204;203;202;201;200;199;296;Triplanar
    2D Array;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;297;1268.411,-354.576;Inherit;False;1484.525;902.526;;15;302;303;304;305;301;300;299;298;34;0;35;33;32;157;31;Outputs;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;146;-1281.138,1820.621;Inherit;False;2395.235;2604.375;;83;57;130;48;84;67;58;133;132;131;93;56;91;129;43;46;89;47;55;44;194;87;96;110;109;101;105;103;116;100;115;90;119;118;92;120;126;127;128;86;63;42;125;123;88;62;112;60;65;95;54;45;108;70;121;69;79;106;94;97;99;122;192;111;113;53;83;107;98;193;59;149;148;147;114;152;150;102;155;156;117;85;41;295;Triplanar
    2D;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;143;-2805.996,-359.0244;Inherit;False;1399.204;991.5428;;28;104;309;76;36;68;145;82;184;5;73;159;315;77;75;308;80;306;74;71;50;72;81;78;183;64;307;158;61;Inputs;1,1,1,1;0;0\nNode;AmplifyShaderEditor.SamplerNode;170;-365.7677,1082.383;Inherit;True;Property;_TextureSample13;Texture
    Sample 1;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;85;-18.17893,3443.745;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;161;-992.5226,1018.242;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;45;-64.76524,2521.532;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CustomExpressionNode;83;-970.9571,3339.666;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.WireNode;102;-46.64538,4274.229;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;113;-1224.541,3090.587;Inherit;False;64;Triplanar_UV1;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;47;-674.6476,2026.501;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;121;72.30684,4059.421;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.CustomExpressionNode;108;165.3301,2727.335;Inherit;False;half3
    weights = max( abs( WorldNormal.xyz ), 0.000001 )@$weights /= ( weights.x + weights.y
    + weights.z ).xxx@$W0 = weights.x@$W1 = weights.y@$W2 = weights.z@$return@;7;False;4;True;WorldNormal;FLOAT3;0,0,0;In;;Inherit;False;True;W0;FLOAT;0;Out;;Inherit;False;True;W1;FLOAT;0;Out;;Inherit;False;True;W2;FLOAT;0;Out;;Inherit;False;Triplanar
    Weights;True;False;0;5;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;4;FLOAT;0;FLOAT;3;FLOAT;4;FLOAT;5\nNode;AmplifyShaderEditor.WireNode;141;-687.2004,-56.97699;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;70;702.0071,3020.752;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;194;-172.265,3509.147;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;26;303.8399,385.2554;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CustomExpressionNode;2;-967.7908,239.7245;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.WireNode;152;-187.3821,4345.959;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;169;-365.385,1286.126;Inherit;True;Property;_TextureSample12;Texture
    Sample 2;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;43;-377.2667,2361.99;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;119;-519.4247,3719.87;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;163;-740.3662,1072.4;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;25;303.8398,354.8411;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;139;-671.59,-112.4807;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;130;-951.8373,1870.621;Inherit;False;68;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;114;-21.95006,4281.605;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;100;-369.6875,2860.79;Inherit;True;Property;_TextureSample7;Texture
    Sample 7;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;56;-374.4885,1954.325;Inherit;True;Property;_TextureSample4;Texture
    Sample 4;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;89;-372.4656,3268.457;Inherit;True;Property;_TextureSample6;Texture
    Sample 6;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;46;-674.6478,2058.139;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;87;-539.7791,2910.117;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;84;-669.847,2964.604;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;164;-736.9875,1046.247;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;131;-507.7252,1950.13;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;91;-372.8483,3064.712;Inherit;True;Property;_TextureSample5;Texture
    Sample 5;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;69;520.7668,2902.641;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;7;-372.6252,205.1037;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;156;-195.8623,4301.043;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;95;275.6625,3046.13;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;136;-568.3672,-163.3666;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;173;-362.6067,878.461;Inherit;True;Property;_TextureSample14;Texture
    Sample 0;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;150;-190.2717,4322.456;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;202;-180.7623,7144.028;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;112;-540.5168,3756.782;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;94;77.9698,3211.89;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;99;65.72094,2930.447;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;186;-641.9001,979.174;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;10;-960.4671,-5.020669;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;162;-755.9764,1096.683;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;60;16.91692,2162.801;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;122;269.9997,3893.663;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;138;-999.7629,-162.4468;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;147;-183.8212,2565.917;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;303;1604.811,-34.21301;Inherit;False;Planar
    2D Array;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;133;-559.291,1999.009;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;200;-3.078922,6286.73;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;107;-961.0446,3904.996;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;90;-949.1356,2796.05;Inherit;False;68;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;140;-671.5901,-81.25983;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;53;-969.106,2453.156;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;106;524.8408,3222.355;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;137;-600.6234,-132.8083;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;192;-180.2166,3486.774;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;116;-948.1158,3727.082;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;176;67.66106,1501.591;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;111;-570.3969,3770.843;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionSubtitle;304;1618.694,95.19456;Inherit;False;Triplanar
    2D;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;134;-1003.098,-253.8223;Inherit;False;68;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;44;-1231.138,2184.123;Inherit;False;50;Triplanar_UV0;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;63;222.213,2136.949;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;115;-688.1614,3828.846;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;128;-373.2032,4115.121;Inherit;True;Property;_TextureSample11;Texture
    Sample 11;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;120;-670.5846,3811.27;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;294;865.0776,1047.969;Inherit;False;Output_2DArray;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;104;-2531.787,313.7767;Inherit;False;Tiling;2;5;False;1;0;FLOAT2;1,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;149;-183.7648,2612.974;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;172;-948.1591,1126.679;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;178;487.1229,1080.427;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;42;24.52043,2302.707;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;4;-369.847,-202.5619;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;168;-547.5455,876.9119;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.GetLocalVarNode;174;-1236.06,1180.329;Inherit;False;145;Input_UV;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;190;-158.0187,1528.828;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;123;64.70333,3919.515;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;188;-659.7223,1049.705;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;118;-670.5844,3779.631;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;191;-158.0863,1551.41;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;86;-960.3069,3058.33;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;148;-185.7914,2588.942;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;28;689.8213,-14.83985;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;175;692.4185,1054.575;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;157;1885.965,-13.69264;Inherit;False;Sample
    Mode;False;0;4;-1;Planar 2D;Planar 2D Array;Triplanar 2D;Triplanar 2D Array;Object;-1;9;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;132;-522.834,1983.267;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;59;-45.21275,2528.454;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;293;865.8185,-18.28572;Inherit;False;Output_2D;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;103;-949.8732,3642.714;Inherit;False;68;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;177;494.7263,1220.333;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSubtitle;305;1583.404,244.2609;Inherit;False;Triplanar
    2D Array;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;24;492.1291,150.9177;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;65;21.47899,2033.54;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;180;491.6849,951.1669;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DdyOpNode;182;-946.6234,1204.227;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSubtitle;302;1639.862,-181.2332;Inherit;False;Planar
    2D;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;166;-561.127,917.6563;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;193;-184.1203,3463.355;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;300;1336.352,91.59142;Inherit;False;295;Output_Triplanar;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;48;-952.1794,1973.951;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.DdyOpNode;92;-958.7713,3135.878;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;208;-168.7211,5408.902;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;298;1358.352,-187.2293;Inherit;False;293;Output_2D;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;155;-67.66827,4256.529;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;93;-669.8467,2932.966;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;171;-942.7695,1359.865;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.WireNode;88;-518.6871,2873.205;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;101;-947.3781,2880.416;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;199;-18.26203,6268.457;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;189;-159.3294,1507.557;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;79;518.7319,3049.262;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;125;69.26546,3790.255;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.CustomExpressionNode;117;-976.4214,4183.103;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.SamplerNode;6;-373.0079,1.359925;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;126;-373.5859,3911.377;Inherit;True;Property;_TextureSample9;Texture
    Sample 9;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;181;61.0726,1469.656;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CustomExpressionNode;201;-961.3214,7026.089;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.WireNode;167;-593.3832,948.2145;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SamplerNode;127;-370.425,3707.456;Inherit;True;Property;_TextureSample10;Texture
    Sample 10;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DdyOpNode;12;-958.9313,72.52722;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;165;-1044.845,828.8899;Inherit;False;159;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;41;-33.36201,3425.471;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;187;-645.2793,997.7559;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;299;1331.738,-38.71012;Inherit;False;294;Output_2DArray;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;129;-362.8989,2887.883;Inherit;False;68;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.DdyOpNode;58;-963.5724,2229.414;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WorldNormalVector;62;-31.00317,2723.179;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;54;-92.78294,2504.121;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdxOpNode;57;-965.1083,2151.866;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;251;-733.1257,6786.954;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;216;-954.0061,5296.142;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;240;39.62043,5145.692;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DdxOpNode;242;-927.6881,5993.915;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;279;-998.8414,4745.661;Inherit;False;159;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;224;539.9408,6065.341;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;261;-157.165,6352.133;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;105;-377.6494,2158.247;Inherit;True;Property;_TextureSample8;Texture
    Sample 8;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;232;290.7627,5889.115;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;227;87.40696,6902.406;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;213;-33.87604,6253.656;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;231;-77.68291,5347.106;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;284;-605.3763,4924.135;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;239;84.36546,6633.24;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DdxOpNode;214;-933.4313,6855.596;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;205;-175.1717,7165.441;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;245;-358.4859,6754.362;Inherit;True;Property;_TextureSample17;Texture
    Sample 9;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;288;-603.3726,5839.669;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;277;-693.7852,5897.679;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;260;-494.6287,5761.116;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;218;-533.2599,6622.33;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;257;-711.3621,5915.255;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.FunctionInput;183;-2522.189,101.5914;Inherit;False;Index;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;212;-169.0203,6306.341;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;243;-358.1031,6958.106;Inherit;True;Property;_TextureSample15;Texture
    Sample 11;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;233;36.57905,4876.525;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WorldNormalVector;236;-15.90314,5566.165;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;271;-676.2661,5873.548;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;234;32.01691,5005.787;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;185;-1005.769,928.8007;Inherit;False;184;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdyOpNode;275;-915.9375,5175.009;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;238;79.80344,6762.5;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;254;-1037.831,6514.394;Inherit;False;159;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;97;70.36629,3071.982;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;203;-52.56817,7099.515;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;230;-49.66521,5364.518;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;207;-6.85004,7124.591;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;259;-524.509,5775.177;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;109;-687.4238,2982.181;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;229;180.4301,5570.32;Inherit;False;half3
    weights = max( abs( WorldNormal.xyz ), 0.000001 )@$weights /= ( weights.x + weights.y
    + weights.z ).xxx@$W0 = weights.x@$W1 = weights.y@$W2 = weights.z@$return@;7;False;4;True;WorldNormal;FLOAT3;0,0,0;In;;Inherit;False;True;W0;FLOAT;0;Out;;Inherit;False;True;W1;FLOAT;0;Out;;Inherit;False;True;W2;FLOAT;0;Out;;Inherit;False;Triplanar
    Weights;True;False;0;5;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;4;FLOAT;0;FLOAT;3;FLOAT;4;FLOAT;5\nNode;AmplifyShaderEditor.DdyOpNode;247;-926.1526,6071.462;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;27;305.3607,417.1903;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;264;-677.0543,4964.566;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;241;237.3129,4979.935;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DdyOpNode;258;-931.8958,6933.145;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;255;-362.5493,5001.232;Inherit;True;Property;_TextureSample19;Texture
    Sample 8;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;246;-715.549,6769.378;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;237;-473.5366,5724.204;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SamplerNode;252;-354.5874,5703.775;Inherit;True;Property;_TextureSample18;Texture
    Sample 7;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;248;-715.5488,6737.739;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;223;93.0698,6054.875;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;281;-964.0038,4829.952;Inherit;False;184;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;250;-1022.183,5639.036;Inherit;False;159;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;228;717.1071,5863.737;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;204;-31.54535,7117.215;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;296;870.2289,5857.978;Inherit;False;Output_TriplanarArray;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;273;-475.6802,4856.304;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;22;489.0877,-118.2482;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;287;-589.3491,5819.636;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;144;-1257.727,32.94259;Inherit;False;145;Input_UV;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;158;-2590.266,-3.661018;Inherit;False;Texture
    Array;12;1;False;1;0;SAMPLER2DARRAY;0;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;274;-512.1373,4872.046;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.GetLocalVarNode;253;-984.0741,6702.991;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;256;-972.3212,5842.516;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;272;-460.5714,4823.167;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;290;-615.3917,6651.026;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdyOpNode;110;-959.5091,3982.545;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;67;-1225.278,3937.253;Inherit;False;71;Triplanar_UV2;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;265;-357.3655,6111.442;Inherit;True;Property;_TextureSample20;Texture
    Sample 6;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;283;-597.3629,4908.108;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;291;-613.3883,6679.072;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;179;44.18006,1435.864;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;292;-619.3984,6699.106;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;217;-1206.556,6060.53;Inherit;False;64;Triplanar_UV1;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;276;-1230.178,6903.238;Inherit;False;71;Triplanar_UV2;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;220;285.0999,6736.648;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;269;-357.7482,5907.697;Inherit;True;Property;_TextureSample22;Texture
    Sample 5;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;222;85.46629,5914.968;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;219;-165.1165,6329.76;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;226;535.8668,5745.627;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;266;-694.5734,4988.696;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;221;80.82102,5773.433;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;289;-998.0596,6606.927;Inherit;False;184;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;267;-362.1667,5204.976;Inherit;True;Property;_TextureSample21;Texture
    Sample 3;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;74;-2575.694,186.7092;Inherit;False;SS;13;6;False;1;0;SAMPLERSTATE;0;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;225;533.8319,5892.248;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.DdxOpNode;280;-917.4734,5097.461;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;235;-503.3798,6608.269;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;55;-692.2244,2075.716;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;98;-48.9761,3410.671;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;210;-168.6647,5455.96;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;308;-2149.554,321.281;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;211;-30.11273,5371.439;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;263;-699.6365,5013.781;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;244;-355.325,6550.441;Inherit;True;Property;_TextureSample16;Texture
    Sample 10;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;286;-589.3489,5787.583;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;209;-170.6913,5431.928;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;206;-172.282,7188.944;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;96;-569.6593,2924.178;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;278;-946.5848,4931.528;Inherit;False;76;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;135;-554.7857,-204.111;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;36;-2384.665,-215.9967;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;249;-482.2875,6571.357;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;145;-1629.496,-192.1966;Inherit;False;Input_UV;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;68;-1637.527,-288.2665;Inherit;False;Input_Texture;-1;True;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;270;-359.3885,4797.311;Inherit;True;Property;_TextureSample23;Texture
    Sample 4;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Derivative;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;309;-2384.424,-81.31772;Inherit;False;0;-1;2;3;2;SAMPLER2DARRAY;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;23;484.5256,11.0122;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;262;-1204.496,5159.837;Inherit;False;50;Triplanar_UV0;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.CustomExpressionNode;215;-955.8572,6182.651;Inherit;False;float2
    vertex1, vertex2, vertex3@$$// Scaling of the input$float2 uv = UV * 3.464@ //
    2 * sqrt (3)$$// Skew input space into simplex triangle grid$const float2x2 gridToSkewedGrid
    = float2x2( 1.0, 0.0, -0.57735027, 1.15470054 )@$float2 skewedCoord = mul( gridToSkewedGrid,
    uv )@$$// Compute local triangle vertex IDs and local barycentric coordinates$int2
    baseId = int2( floor( skewedCoord ) )@$float3 temp = float3( frac( skewedCoord
    ), 0 )@$temp.z = 1.0 - temp.x - temp.y@$if ( temp.z > 0.0 )${$\tW1 = temp.z@$\tW2
    = temp.y@$\tW3 = temp.x@$\tvertex1 = baseId@$\tvertex2 = baseId + int2( 0, 1 )@$\tvertex3
    = baseId + int2( 1, 0 )@$}$else${$\tW1 = -temp.z@$\tW2 = 1.0 - temp.y@$\tW3 =
    1.0 - temp.x@$\tvertex1 = baseId + int2( 1, 1 )@$\tvertex2 = baseId + int2( 1,
    0 )@$\tvertex3 = baseId + int2( 0, 1 )@$}$$UV1 = UV + frac( sin( mul( float2x2(
    127.1, 311.7, 269.5, 183.3 ), vertex1 ) ) * 43758.5453 )@$UV2 = UV + frac( sin(
    mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex2 ) ) * 43758.5453 )@$UV3 =
    UV + frac( sin( mul( float2x2( 127.1, 311.7, 269.5, 183.3 ), vertex3 ) ) * 43758.5453
    )@$return@;7;False;7;True;UV;FLOAT2;0,0;In;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Float;False;True;W1;FLOAT;0;Out;;Float;False;True;W2;FLOAT;0;Out;;Float;False;True;W3;FLOAT;0;Out;;Float;False;Stochastic
    Tiling;False;False;0;8;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;7;FLOAT;0;FLOAT2;3;FLOAT2;4;FLOAT2;5;FLOAT;6;FLOAT;7;FLOAT;8\nNode;AmplifyShaderEditor.FunctionInput;5;-1772.503,-191.8328;Inherit;False;UV;2;3;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;315;-2116.706,-191.1189;Inherit;False;Option;False;0;4;-1;2D;2D
    Array;Triplanar;Triplanar Array;Instance;157;9;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;159;-1638.465,-4.046509;Inherit;False;Input_TextureArray;-1;True;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.FunctionInput;82;-2562.134,-290.8169;Inherit;False;Texture;9;0;False;1;0;SAMPLER2D;0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;282;-591.3529,4882.065;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;301;1327.809,238.6714;Inherit;False;296;Output_TriplanarArray;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;184;-1632.055,101.7441;Inherit;False;Input_Index;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;61;-2258.47,364.7153;Inherit;False;FLOAT2;2;1;2;3;1;0;FLOAT3;0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;306;-2196.85,294.618;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;307;-2138.884,347.3101;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;77;-1994.028,329.8235;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;73;-2257.888,435.678;Inherit;False;FLOAT2;2;0;2;3;1;0;FLOAT3;0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;285;-984.0376,5741.479;Inherit;False;184;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;31;2294.798,132.5219;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RegisterLocalVarNode;76;-1632.419,184.9388;Inherit;False;Input_SS;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;295;873.6226,3015.605;Inherit;False;Output_Triplanar;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;81;-2760.234,421.0934;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;72;-1993.533,507.2687;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;64;-1632.479,418.3403;Inherit;False;Triplanar_UV1;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;78;-1994.079,417.5941;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;80;-2574.644,415.9991;Inherit;False;World
    Position;3;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;75;-2257.687,506.5578;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT3;0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;50;-1630.848,341.5698;Inherit;False;Triplanar_UV0;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;71;-1630.916,494.2191;Inherit;False;Triplanar_UV2;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;33;2573.17,133.5936;Inherit;False;False;-1;G;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;2573.008,-6.675806;Inherit;False;True;-1;RGBA;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;34;2576.37,208.7937;Inherit;False;False;-1;B;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;35;2577.97,287.1936;Inherit;False;False;-1;A;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;32;2573.17,63.19369;Inherit;False;False;-1;R;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;170;0;166;0\nWireConnection;170;1;171;4\nWireConnection;170;3;172;0\nWireConnection;170;4;182;0\nWireConnection;170;6;187;0\nWireConnection;170;7;163;0\nWireConnection;85;0;194;0\nWireConnection;45;0;148;0\nWireConnection;83;1;113;0\nWireConnection;102;0;150;0\nWireConnection;47;0;48;0\nWireConnection;121;0;128;0\nWireConnection;121;1;114;0\nWireConnection;108;1;62;0\nWireConnection;141;0;138;0\nWireConnection;70;0;69;0\nWireConnection;70;1;79;0\nWireConnection;70;2;106;0\nWireConnection;194;0;83;8\nWireConnection;26;0;2;7\nWireConnection;2;1;144;0\nWireConnection;152;0;117;8\nWireConnection;169;0;167;0\nWireConnection;169;1;171;5\nWireConnection;169;3;172;0\nWireConnection;169;4;182;0\nWireConnection;169;6;188;0\nWireConnection;169;7;162;0\nWireConnection;43;0;133;0\nWireConnection;43;1;53;5\nWireConnection;43;3;57;0\nWireConnection;43;4;58;0\nWireConnection;43;7;55;0\nWireConnection;119;0;103;0\nWireConnection;163;0;161;0\nWireConnection;25;0;2;6\nWireConnection;139;0;138;0\nWireConnection;114;0;152;0\nWireConnection;100;0;88;0\nWireConnection;100;1;83;3\nWireConnection;100;3;86;0\nWireConnection;100;4;92;0\nWireConnection;100;7;93;0\nWireConnection;56;0;131;0\nWireConnection;56;1;53;3\nWireConnection;56;3;57;0\nWireConnection;56;4;58;0\nWireConnection;56;7;47;0\nWireConnection;89;0;96;0\nWireConnection;89;1;83;5\nWireConnection;89;3;86;0\nWireConnection;89;4;92;0\nWireConnection;89;7;109;0\nWireConnection;46;0;48;0\nWireConnection;87;0;90;0\nWireConnection;84;0;101;0\nWireConnection;164;0;161;0\nWireConnection;131;0;130;0\nWireConnection;91;0;87;0\nWireConnection;91;1;83;4\nWireConnection;91;3;86;0\nWireConnection;91;4;92;0\nWireConnection;91;7;84;0\nWireConnection;69;0;63;0\nWireConnection;69;1;108;3\nWireConnection;7;0;137;0\nWireConnection;7;1;2;5\nWireConnection;7;3;10;0\nWireConnection;7;4;12;0\nWireConnection;7;7;141;0\nWireConnection;156;0;117;6\nWireConnection;95;0;99;0\nWireConnection;95;1;97;0\nWireConnection;95;2;94;0\nWireConnection;136;0;134;0\nWireConnection;173;0;168;0\nWireConnection;173;1;171;3\nWireConnection;173;3;172;0\nWireConnection;173;4;182;0\nWireConnection;173;6;186;0\nWireConnection;173;7;164;0\nWireConnection;150;0;117;7\nWireConnection;202;0;201;6\nWireConnection;112;0;103;0\nWireConnection;94;0;89;0\nWireConnection;94;1;85;0\nWireConnection;99;0;100;0\nWireConnection;99;1;98;0\nWireConnection;186;0;185;0\nWireConnection;10;0;144;0\nWireConnection;162;0;161;0\nWireConnection;60;0;105;0\nWireConnection;60;1;45;0\nWireConnection;122;0;125;0\nWireConnection;122;1;123;0\nWireConnection;122;2;121;0\nWireConnection;147;0;53;6\nWireConnection;303;0;299;0\nWireConnection;133;0;130;0\nWireConnection;200;0;261;0\nWireConnection;107;0;67;0\nWireConnection;140;0;138;0\nWireConnection;53;1;44;0\nWireConnection;106;0;108;5\nWireConnection;106;1;122;0\nWireConnection;137;0;134;0\nWireConnection;192;0;83;7\nWireConnection;176;0;191;0\nWireConnection;111;0;103;0\nWireConnection;304;0;300;0\nWireConnection;63;0;65;0\nWireConnection;63;1;60;0\nWireConnection;63;2;42;0\nWireConnection;115;0;116;0\nWireConnection;128;0;111;0\nWireConnection;128;1;117;5\nWireConnection;128;3;107;0\nWireConnection;128;4;110;0\nWireConnection;128;7;115;0\nWireConnection;120;0;116;0\nWireConnection;294;0;175;0\nWireConnection;149;0;53;8\nWireConnection;172;0;174;0\nWireConnection;178;0;170;0\nWireConnection;178;1;181;0\nWireConnection;42;0;43;0\nWireConnection;42;1;59;0\nWireConnection;4;0;135;0\nWireConnection;4;1;2;3\nWireConnection;4;3;10;0\nWireConnection;4;4;12;0\nWireConnection;4;7;139;0\nWireConnection;168;0;165;0\nWireConnection;190;0;171;7\nWireConnection;123;0;126;0\nWireConnection;123;1;102;0\nWireConnection;188;0;185;0\nWireConnection;118;0;116;0\nWireConnection;191;0;171;8\nWireConnection;86;0;113;0\nWireConnection;148;0;53;7\nWireConnection;28;0;22;0\nWireConnection;28;1;23;0\nWireConnection;28;2;24;0\nWireConnection;175;0;180;0\nWireConnection;175;1;178;0\nWireConnection;175;2;177;0\nWireConnection;157;0;302;0\nWireConnection;157;1;303;0\nWireConnection;157;2;304;0\nWireConnection;157;3;305;0\nWireConnection;132;0;130;0\nWireConnection;59;0;149;0\nWireConnection;293;0;28;0\nWireConnection;177;0;169;0\nWireConnection;177;1;176;0\nWireConnection;305;0;301;0\nWireConnection;24;0;7;0\nWireConnection;24;1;27;0\nWireConnection;65;0;56;0\nWireConnection;65;1;54;0\nWireConnection;180;0;173;0\nWireConnection;180;1;179;0\nWireConnection;182;0;174;0\nWireConnection;302;0;298;0\nWireConnection;166;0;165;0\nWireConnection;193;0;83;6\nWireConnection;92;0;113;0\nWireConnection;208;0;216;6\nWireConnection;155;0;156;0\nWireConnection;93;0;101;0\nWireConnection;171;1;174;0\nWireConnection;88;0;90;0\nWireConnection;199;0;219;0\nWireConnection;189;0;171;6\nWireConnection;79;0;108;4\nWireConnection;79;1;95;0\nWireConnection;125;0;127;0\nWireConnection;125;1;155;0\nWireConnection;117;1;67;0\nWireConnection;6;0;136;0\nWireConnection;6;1;2;4\nWireConnection;6;3;10;0\nWireConnection;6;4;12;0\nWireConnection;6;7;140;0\nWireConnection;126;0;112;0\nWireConnection;126;1;117;4\nWireConnection;126;3;107;0\nWireConnection;126;4;110;0\nWireConnection;126;7;120;0\nWireConnection;181;0;190;0\nWireConnection;201;1;276;0\nWireConnection;167;0;165;0\nWireConnection;127;0;119;0\nWireConnection;127;1;117;3\nWireConnection;127;3;107;0\nWireConnection;127;4;110;0\nWireConnection;127;7;118;0\nWireConnection;12;0;144;0\nWireConnection;41;0;192;0\nWireConnection;187;0;185;0\nWireConnection;58;0;44;0\nWireConnection;54;0;147;0\nWireConnection;57;0;44;0\nWireConnection;251;0;253;0\nWireConnection;216;1;262;0\nWireConnection;240;0;267;0\nWireConnection;240;1;211;0\nWireConnection;242;0;217;0\nWireConnection;224;0;229;5\nWireConnection;224;1;220;0\nWireConnection;261;0;215;8\nWireConnection;105;0;132;0\nWireConnection;105;1;53;4\nWireConnection;105;3;57;0\nWireConnection;105;4;58;0\nWireConnection;105;7;46;0\nWireConnection;232;0;221;0\nWireConnection;232;1;222;0\nWireConnection;232;2;223;0\nWireConnection;227;0;243;0\nWireConnection;227;1;207;0\nWireConnection;213;0;212;0\nWireConnection;231;0;208;0\nWireConnection;284;0;281;0\nWireConnection;239;0;244;0\nWireConnection;239;1;203;0\nWireConnection;214;0;276;0\nWireConnection;205;0;201;7\nWireConnection;245;0;235;0\nWireConnection;245;1;201;4\nWireConnection;245;3;214;0\nWireConnection;245;4;258;0\nWireConnection;245;6;291;0\nWireConnection;245;7;246;0\nWireConnection;288;0;285;0\nWireConnection;277;0;256;0\nWireConnection;260;0;250;0\nWireConnection;218;0;254;0\nWireConnection;257;0;256;0\nWireConnection;212;0;215;6\nWireConnection;243;0;218;0\nWireConnection;243;1;201;5\nWireConnection;243;3;214;0\nWireConnection;243;4;258;0\nWireConnection;243;6;292;0\nWireConnection;243;7;251;0\nWireConnection;233;0;270;0\nWireConnection;233;1;231;0\nWireConnection;271;0;256;0\nWireConnection;234;0;255;0\nWireConnection;234;1;230;0\nWireConnection;275;0;262;0\nWireConnection;238;0;245;0\nWireConnection;238;1;204;0\nWireConnection;97;0;91;0\nWireConnection;97;1;41;0\nWireConnection;203;0;202;0\nWireConnection;230;0;209;0\nWireConnection;207;0;206;0\nWireConnection;259;0;250;0\nWireConnection;109;0;101;0\nWireConnection;229;1;236;0\nWireConnection;247;0;217;0\nWireConnection;27;0;2;8\nWireConnection;264;0;278;0\nWireConnection;241;0;233;0\nWireConnection;241;1;234;0\nWireConnection;241;2;240;0\nWireConnection;258;0;276;0\nWireConnection;255;0;273;0\nWireConnection;255;1;216;4\nWireConnection;255;3;280;0\nWireConnection;255;4;275;0\nWireConnection;255;6;283;0\nWireConnection;255;7;266;0\nWireConnection;246;0;253;0\nWireConnection;237;0;250;0\nWireConnection;252;0;237;0\nWireConnection;252;1;215;3\nWireConnection;252;3;242;0\nWireConnection;252;4;247;0\nWireConnection;252;6;286;0\nWireConnection;252;7;271;0\nWireConnection;248;0;253;0\nWireConnection;223;0;265;0\nWireConnection;223;1;200;0\nWireConnection;228;0;226;0\nWireConnection;228;1;225;0\nWireConnection;228;2;224;0\nWireConnection;204;0;205;0\nWireConnection;296;0;228;0\nWireConnection;273;0;279;0\nWireConnection;22;0;4;0\nWireConnection;22;1;25;0\nWireConnection;287;0;285;0\nWireConnection;274;0;279;0\nWireConnection;272;0;279;0\nWireConnection;290;0;289;0\nWireConnection;110;0;67;0\nWireConnection;265;0;259;0\nWireConnection;265;1;215;5\nWireConnection;265;3;242;0\nWireConnection;265;4;247;0\nWireConnection;265;6;288;0\nWireConnection;265;7;257;0\nWireConnection;283;0;281;0\nWireConnection;291;0;289;0\nWireConnection;179;0;189;0\nWireConnection;292;0;289;0\nWireConnection;220;0;239;0\nWireConnection;220;1;238;0\nWireConnection;220;2;227;0\nWireConnection;269;0;260;0\nWireConnection;269;1;215;4\nWireConnection;269;3;242;0\nWireConnection;269;4;247;0\nWireConnection;269;6;287;0\nWireConnection;269;7;277;0\nWireConnection;222;0;269;0\nWireConnection;222;1;199;0\nWireConnection;219;0;215;7\nWireConnection;226;0;241;0\nWireConnection;226;1;229;3\nWireConnection;266;0;278;0\nWireConnection;221;0;252;0\nWireConnection;221;1;213;0\nWireConnection;267;0;274;0\nWireConnection;267;1;216;5\nWireConnection;267;3;280;0\nWireConnection;267;4;275;0\nWireConnection;267;6;284;0\nWireConnection;267;7;263;0\nWireConnection;225;0;229;4\nWireConnection;225;1;232;0\nWireConnection;280;0;262;0\nWireConnection;235;0;254;0\nWireConnection;55;0;48;0\nWireConnection;98;0;193;0\nWireConnection;210;0;216;8\nWireConnection;308;0;104;0\nWireConnection;211;0;210;0\nWireConnection;263;0;278;0\nWireConnection;244;0;249;0\nWireConnection;244;1;201;3\nWireConnection;244;3;214;0\nWireConnection;244;4;258;0\nWireConnection;244;6;290;0\nWireConnection;244;7;248;0\nWireConnection;286;0;285;0\nWireConnection;209;0;216;7\nWireConnection;206;0;201;8\nWireConnection;96;0;90;0\nWireConnection;135;0;134;0\nWireConnection;36;2;82;0\nWireConnection;249;0;254;0\nWireConnection;145;0;5;0\nWireConnection;68;0;82;0\nWireConnection;270;0;272;0\nWireConnection;270;1;216;3\nWireConnection;270;3;280;0\nWireConnection;270;4;275;0\nWireConnection;270;6;282;0\nWireConnection;270;7;264;0\nWireConnection;309;2;158;0\nWireConnection;23;0;6;0\nWireConnection;23;1;26;0\nWireConnection;215;1;217;0\nWireConnection;5;0;315;0\nWireConnection;315;0;36;0\nWireConnection;315;1;309;0\nWireConnection;315;2;36;0\nWireConnection;315;3;309;0\nWireConnection;159;0;158;0\nWireConnection;282;0;281;0\nWireConnection;184;0;183;0\nWireConnection;61;0;80;0\nWireConnection;306;0;104;0\nWireConnection;307;0;104;0\nWireConnection;77;0;306;0\nWireConnection;77;1;61;0\nWireConnection;73;0;80;0\nWireConnection;31;0;157;0\nWireConnection;76;0;74;0\nWireConnection;295;0;70;0\nWireConnection;72;0;307;0\nWireConnection;72;1;75;0\nWireConnection;64;0;78;0\nWireConnection;78;0;308;0\nWireConnection;78;1;73;0\nWireConnection;80;0;81;0\nWireConnection;75;0;80;0\nWireConnection;50;0;77;0\nWireConnection;71;0;72;0\nWireConnection;33;0;31;1\nWireConnection;0;0;157;0\nWireConnection;34;0;31;2\nWireConnection;35;0;31;3\nWireConnection;32;0;31;0\nASEEND*/\n//CHKSM=DC3AB08472A21A32907C6A155EB8627E743376BD"
  m_functionName: 
  m_description: Based on "Procedural Stochastic Textures by Tiling and Blending"
    by Thomas Deliot and Eric Heitz
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 12
  m_customNodeCategory: Rust
  m_previewPosition: 0
  m_hidden: 0
