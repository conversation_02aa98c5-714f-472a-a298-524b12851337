﻿ 



 
 



/*
 * created by wzw at 2019.11.6
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //游戏运行时使用
    public sealed class RuntimeDecorationBorderLayerView : MapLayerView
    {
        class TileObjectView
        {
            public GameObject gameObject;
            public int activeGridCount = 0;
        }

        public RuntimeDecorationBorderLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = Map.currentMap.view.reusableGameObjectPool;
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var obj in mTileViews)
            {
                Utils.DestroyObject(obj.Value.gameObject);
            }
            mTileViews = null;
        }

        public void OnObjectActiveStateChange(TileObjectData2 data)
        {
            if (data.IsObjActive())
            {
                ShowObject(data);
            }
            else
            {
                HideObject(data);
            }
        }

        public void OnObjectScaleChange(TileObjectData2 data)
        {
            TileObjectView view;
            mTileViews.TryGetValue(data.viewID, out view);
            view.gameObject.transform.localScale = data.GetScale();
        }

        //显示地图对象的模型
        void ShowObject(TileObjectData2 data)
        {
            if (data is TileObjectData2)
            {
                TileObjectView view;
                bool found = mTileViews.TryGetValue(data.viewID, out view);
                if (found)
                {
                    view.activeGridCount += 1;
                }
                else
                {
                    var obj = mObjectPool.Require(data.GetAssetPath());

                    view = mTileObjectViewPool.Require();
                    view.activeGridCount = 1;
                    view.gameObject = obj;
                    obj.SetActive(true);
                    mTileViews[data.viewID] = view;

                    var transform = obj.transform;
                    transform.localPosition = data.GetPosition();
                    transform.localRotation = (data.objectType == TileObjectType.NoneScaleDecorationObject || data.objectType == TileObjectType.ScaleDecorationObject) && MapObjectUtils.NeedRotation ? data.GetRotation() * Quaternion.Euler(0, 180f, 0) : data.GetRotation();
                    transform.localScale = data.GetScale();
                    transform.SetParent(root.transform, false);
                }
            }
        }

        //隐藏地图对象的模型
        void HideObject(TileObjectData2 data)
        {
            if (data is TileObjectData2)
            {
                var id = data.viewID;
                TileObjectView view;
                bool found = mTileViews.TryGetValue(id, out view);
#if UNITY_EDITOR
                Debug.Assert(found);
#endif
                view.activeGridCount -= 1;
                if (view.activeGridCount == 0)
                {
                    mTileObjectViewPool.Release(view);
                    mTileViews.Remove(id);
                    mObjectPool.Release(data.GetAssetPath(), view.gameObject, mLayerData.map);
                }
#if UNITY_EDITOR
                else if (view.activeGridCount < 0)
                {
                    Debug.Assert(false);
                }
#endif
            }
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        //该层中所有地图对象的视图, key是view id
        Dictionary<int, TileObjectView> mTileViews = new Dictionary<int, TileObjectView>();
        ObjectPool<TileObjectView> mTileObjectViewPool = new ObjectPool<TileObjectView>(3000, () => { return new TileObjectView(); });
        GameObjectPool mObjectPool;
    };
}