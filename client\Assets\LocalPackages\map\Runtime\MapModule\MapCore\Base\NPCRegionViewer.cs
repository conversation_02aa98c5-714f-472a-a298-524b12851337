﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public static class NPCRegionViewer
    {
        public static void OnDestroy()
        {
            Utils.DestroyObject(mRoot);
            mRoot = null;
            if (mRegionMaterials != null)
            {
                foreach (var mtl in mRegionMaterials)
                {
                    GameObject.Destroy(mtl.Value);
                }
                mRegionMaterials = null;
            }
            if (mMesh != null)
            {
                GameObject.Destroy(mMesh);
                mMesh = null;
            }
        }

        public static void Create(List<int> regionLevel, int regionMaxLevel, float regionWidth, float regionHeight, Color lighterColor, Color darkerColor, bool showText)
        {
            var map = Map.currentMap;
            if (map == null)
            {
#if UNITY_EDITOR
                EditorUtility.DisplayDialog("Error", "Only works in game!", "OK");
#endif
                return;
            }

            OnDestroy();

            mRoot = new GameObject("NPC region viewer");
            mRegionMaterials = new Dictionary<int, Material>();

            int k = 0;
            int xRegionCount = Mathf.CeilToInt(map.mapWidth / regionWidth);
            int yRegionCount = Mathf.CeilToInt(map.mapHeight / regionHeight);
            var regionSize = new Vector2(regionWidth, regionHeight);
            for (int i = 0; i < yRegionCount; ++i)
            {
                for (int j = 0; j < xRegionCount; ++j)
                {
                    var pos = new Vector3((j + 0.5f) * regionWidth, 0, (i + 0.5f) * regionHeight);
                    var obj = CreateGameObject(j, i, pos, regionSize, regionLevel[k], regionMaxLevel, lighterColor, darkerColor, showText);
                    ++k;
                }
            }
        }

        static GameObject CreateGameObject(int x, int y, Vector3 pos, Vector2 regionSize, int regionLevel, int regionMaxLevel, Color lighterColor, Color darkerColor, bool showText)
        {
            var obj = GameObject.CreatePrimitive(PrimitiveType.Plane);
            obj.name = string.Format("Region_{0}_{1}", x, y);
            var planePos = pos;
            obj.transform.position = new Vector3(planePos.x, 15.0f, planePos.z - 15.0f);// 高过山体
            obj.transform.localScale = new Vector3(regionSize.x, 1.0f, regionSize.y);
            obj.transform.SetParent(mRoot.transform);
            var mtl = CreateMaterial(regionLevel, regionMaxLevel, lighterColor, darkerColor);
            obj.GetComponent<MeshRenderer>().sharedMaterial = mtl;
            if (mMesh == null)
            {
                mMesh = CreateMesh();
            }
            obj.GetComponent<MeshFilter>().sharedMesh = mMesh;

            //            if (showText)
            //            {
            //                var textObj = new GameObject("Text");
            //                Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
            //                var renderer = textObj.AddComponent<MeshRenderer>();
            //                renderer.sharedMaterial = ArialFont.material;
            //                var textMesh = textObj.AddComponent<TextMesh>();
            //                textMesh.text = regionLevel.ToString();
            //                textMesh.fontSize = 32;
            //                var textPos = obj.transform.position;
            //                textObj.transform.position = new Vector3(textPos.x, 20.0f, textPos.z);
            //                textObj.transform.localScale = Vector3.one * 10;
            //                textObj.transform.rotation = Quaternion.Euler(45, 0, 0);
            //                textObj.transform.SetParent(mRoot.transform);
            //            }

            return obj;
        }

        static Material CreateMaterial(int level, int maxLevel, Color lighterColor, Color darkerColor)
        {
            Material mtl = null;
            mRegionMaterials.TryGetValue(level, out mtl);
            if (mtl == null)
            {
                mtl = new Material(Shader.Find("SLGMaker/ColorTransparent"));
                float t = Mathf.Clamp01(level / (float)maxLevel);
                var color = Color.Lerp(lighterColor, darkerColor, t);
                color.a = 0.65f;
                mtl.color = color;

                mRegionMaterials[level] = mtl;
            }
            return mtl;
        }

        static Mesh CreateMesh()
        {
            var mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(-0.5f, 0, -0.5f),
                new Vector3(-0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, 0.5f),
                new Vector3(0.5f, 0, -0.5f),
            };
            mesh.triangles = new int[]
            {
                0, 1, 2,0,2,3,
            };

            return mesh;
        }

        public static void SetVisible(bool visible)
        {
            if (mRoot != null)
            {
                mRoot.SetActive(visible);
            }
        }

        public static bool visible
        {
            get
            {
                if (mRoot != null)
                {
                    return mRoot.activeSelf;
                }
                return false;
            }
        }

        static Mesh mMesh;
        static GameObject mRoot;
        static Dictionary<int, Material> mRegionMaterials;
    }
}