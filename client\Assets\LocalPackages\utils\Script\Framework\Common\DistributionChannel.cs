﻿using UnityEngine;

namespace DistributionChannel
{
    /// <summary>
    /// 分发渠道判断
    /// </summary>
    public class DistributionChannel : MonoBehaviour
    {
        /// <summary>
        /// 是否是国服
        /// </summary>
        public static bool isChinaServer =>
#if USE_37SDK
            true;
#else
            false;
#endif

        /// <summary>
        /// 是否是微信
        /// </summary>
        public static bool isWechat =>
#if UNITY_WEBGL
            true;
#else
            false;
#endif

        public static bool isAndroid
        {
            get
            {
#if UNITY_ANDROID
                return true;
#elif UNITY_WEBGL
                throw new System.NotImplementedException("调用微信SDK API判断平台，WX.GetSystemInfoSync().Platform");
#else    
                return false;           
#endif
            }
        }

        public static bool isIOS
        {
            get
            {
#if UNITY_IOS
                return true;
#elif UNITY_WEBGL
                throw new System.NotImplementedException("调用微信SDK API判断平台，WX.GetSystemInfoSync().Platform");
#else    
                return false;           
#endif
            }
        }
    }
}