﻿using Common;
using Game.Config;
using Logic;
using System;
using System.Collections.Generic;
using DeepUI;
using TFW;
using TFW.Localization;
using TFW.UI;
using UnityEngine;
using Game.Data;
using K1;
using UI.Mail;
using Cysharp.Threading.Tasks;

namespace UI.Alliance
{
    public class UIAllianceMainData : UIData
    {

        public AllianceTabType openAlliancePage = AllianceTabType.Info;

        /// <summary>
        /// 返回的时候看返回到哪里
        /// </summary>
        public MainMenuType CurrentTabKey;
    }

    public enum AllianceTabType 
    {
        none,
        Info,
        Member,
        Setting
    }

    /// <summary>
    /// 联盟主界面
    /// </summary>
    [Popup("Alliance/New/UIAllianceMain", true,true)]
    public class UIAllianceMain : BasePopup
    { 
        #region

        TFWTabGroup tabGroup;

        TFWTab tab1;
        RedWidget tab1Red;
        TFWTab tab2;
        RedWidget tab2Red;
        TFWTab tab3;
        RedWidget tab3Red;

        /// <summary>
        /// 联盟信息
        /// </summary>
        GameObject AlliancePage;
        /// <summary>
        /// 联盟成员
        /// </summary>
        GameObject MemberPage;

        /// <summary>
        /// 联盟设置
        /// </summary>
        GameObject settingPage;

        #region 联盟公告
        /// <summary>
        /// 翻译组件
        /// </summary>
        TextTranslate translateText;
        /// <summary>
        /// 联盟公告标题
        /// </summary>
        TFWText allianceNoiceTitle;
        /// <summary>
        /// 联盟公告发布时间
        /// </summary>
        TFWText allianceNoiceTime;
        
        RectTransform allianceNoiceDescParRT;
        /// <summary>
        /// 联盟公告 正文
        /// </summary>
        TFWText allianceNoiceDesc;
        /// <summary>
        /// 联盟公告 编辑按钮
        /// </summary>
        GameObject editBtnObj;
        /// <summary>
        /// 联盟公告 邮件按钮
        /// </summary>
        GameObject mailBtnObj;

        bool f1 = false;
        bool f2 = false;
        bool f3 = false;

        #endregion

        #endregion

        UIAllianceMainData pageData;

        
        AllianceTabType curSelectPage = AllianceTabType.none;

        /// <summary>
        /// 联盟成员
        /// </summary>
        AllianceMemberWidget menberWidget;

        /// <summary>
        /// 联盟上方信息
        /// </summary>
        AllianceInfoWidget infoWidget;

        TranslateWidget translateWidget;
        
        Animator noiceAnimator;

        StarLevelInfoWidget starLevelInfoWidget;
        GameObject starLevelObj;

        Dictionary<int, AllianceClickItemWidget> btnsDic = new Dictionary<int, AllianceClickItemWidget>();


        #region  聊天

        RectTransform _AnnouncementRoot;
        RectTransform _Btns;
        //GameObject chatBtn;
        //GameObject outChatRoot;

        #endregion

        #region

        GameObject kvkMedalRoot;
        GameObject kvkMedalItem;
        Transform kvkMedalPar;

        #endregion


        protected override void OnInit()
        {
            base.OnInit();
        
            UIBase.AddRemoveListener(TFW.EventTriggerType.Click, GetChild("Root/BtnClose"), (x,y) =>
            {
                OnClickClose();
            });

            tabGroup = GetComponent<TFWTabGroup>("Root/Tab");
            tab1 = GetComponent<TFWTab>("Root/Tab/tab1");
            tab2 = GetComponent<TFWTab>("Root/Tab/tab2");
            tab3 = GetComponent<TFWTab>("Root/Tab/tab3");
            tab1Red = new RedWidget(GetChild("Root/Tab/tab1/Red"));
            tab2Red = new RedWidget(GetChild("Root/Tab/tab2/Red"));
            tab2Red.SetData(0);
            tab3Red = new RedWidget(GetChild("Root/Tab/tab3/Red"));

            RedPointMgr.I.CreateRedPoint(RedPointType.RedPointUnionMain, tab1Red, GetUnionMainRedCount);
            RedPointMgr.I.CreateRedPoint(RedPointType.RedPointUnionSetting, tab3Red, GetUnionSettingRedCount);

            noiceAnimator = GetComponent<Animator>("Root/AlliancePage");
            noiceAnimator.enabled = true;
            
            tab1?.AddTabClickEvent((x, y) => 
            {
                curSelectPage = AllianceTabType.Info;
                RefushTabPage();
            });
            tab2?.AddTabClickEvent((x, y) => 
            {
                curSelectPage = AllianceTabType.Member;
                RefushTabPage();
            });
            tab3?.AddTabClickEvent((x, y) => 
            {
                curSelectPage = AllianceTabType.Setting;
                RefushTabPage();
            });

            AlliancePage = GetChild("Root/AlliancePage");
            settingPage = GetChild("Root/SettingPage");
            MemberPage = GetChild("Root/MemberPage");
            //chatBtn = GetChild("Root/AlliancePage/ChatBtn");
            //outChatRoot = GetChild("Root/AlliancePage/Chat");
            //outChatRoot.SetActive(false);//逸民说不显示聊天
            _AnnouncementRoot = GetComponent<RectTransform>("Root/AlliancePage/Announcement");
            _Btns = GetComponent<RectTransform>("Root/AlliancePage/Btns");

            starLevelObj = GetChild("Root/KvkLegionStarLevel");
            ///初始化顶部元素
            InitTop();
            InitInfoPageDownBtns();

            tab1?.UpdateTabText(LocalizationMgr.Get("Alliance_title_1"), LocalizationMgr.Get("Alliance_title_1"));
            tab2?.UpdateTabText(LocalizationMgr.Get("Alliance_title_2"), LocalizationMgr.Get("Alliance_title_2"));
            tab3?.UpdateTabText(LocalizationMgr.Get("Alliance_title_3"), LocalizationMgr.Get("Alliance_title_3"));

            starLevelInfoWidget?.SetData(new StarLevelInfoWidgetData()
            {
                data = LAllianceMgr.I.GetUnionInfo(),
                isUnion = true
            }) ;

            rawBtnSize = _Btns.sizeDelta;
            rawBtnPos = _Btns.localPosition;

            kvkMedalRoot = GetChild("Root/KvkLegionStarLevel");
            kvkMedalItem = GetChild("Root/KvkLegionStarLevel/Icon1");
            kvkMedalPar = GetChild("Root/KvkLegionStarLevel/IconList").transform;
            
            // 增加熔岩洞穴初始化
            //LLavaCave.I.ReqLavaCave();
        }

        /// <summary>
        /// 获取联盟设置红点
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private int GetUnionSettingRedCount()
        {
            return 0;//TODO服务器还没做
        }

        /// <summary>
        /// 获取联盟主界面红点
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private int GetUnionMainRedCount()
        {
            return LGameRedPoint.I.GetRedCount(RedPointType.RedPointUnionGift, RedPointType.RedPointUnionHelp,
                RedPointType.RedPointUnionTech, RedPointType.RedPointUnionLand, RedPointType.RedPointUnionShop,
                RedPointType.RedPointUnionMobilize, RedPointType.RedPointUnionAchieve);
        }

        protected override void OnDispose()
        {
            base.OnDispose();

            if (btnsDic != null)
            {
                foreach (var item in btnsDic)
                {
                    item.Value?.Destroy();
                }
                btnsDic.Clear();
            }
            translateWidget?.Destroy();

            RedPointMgr.I.RemoveRedPoint(RedPointType.RedPointUnionMain, true);
            RedPointMgr.I.RemoveRedPoint(RedPointType.RedPointUnionSetting, true);

            menberWidget?.Destroy();
        }

        protected internal override void OnOpenComplete()
        {
            base.OnOpenComplete(); OnShown();
        }

        protected internal override void OnShowComplete()
        {
            base.OnShowComplete(); OnShown();
        }
        
        protected   void OnShown()
        {
           
            var flag = LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateDesc);
            editBtnObj?.SetActive(flag);
            mailBtnObj?.SetActive(flag);
            var unionData = LAllianceMgr.I.GetUnionInfo();
            starLevelObj?.SetActive(unionData != null && unionData.medals != null && unionData.medals.Count > 0);

           
            pageData = Data as UIAllianceMainData;
            if (pageData != null)
            {
                curSelectPage = pageData.openAlliancePage;

                GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FirstOpenAlliance, 0);

            }
            else
            {
                if (curSelectPage == AllianceTabType.none)
                    curSelectPage = AllianceTabType.Info;
            }
            if(tab1)
                tab1.isOn = (curSelectPage == AllianceTabType.Info);
            if(tab2)
                tab2.isOn = (curSelectPage == AllianceTabType.Member);
            if(tab3)
                tab3.isOn = (curSelectPage == AllianceTabType.Setting);
            ///更新联盟公告
            UpDataNoiceData();
            RefushTabPage();

            CheckRed().Forget();
            FrameUpdateMgr.RegisterPerSecondFunListUpdate(this, (time) =>
            {
                CheckRed().Forget();
            });

            EventMgr.RegisterEvent(TEventType.AllianceInfoDetail, OnAllianceInfoDetail, this);
            EventMgr.RegisterEvent(TEventType.AllianceDisband, OnAllianceDisband, this);
            EventMgr.RegisterEvent(TEventType.AllianceExiting, OnAllianceExiting, this);
            //EventMgr.RegisterEvent(TEventType.OnSwitchMainUI, OnOnSwitchMainUI, this);

            ///主动申请联盟礼物信息 为了红点
            GameData.I.AllianceGiftData?.RefreshUnionGiftInfo();

            RefushChatShow();
            //ShowMedal();

            UIMainSwitchMgr.I.onChange += RefushChatShow;
        }

        #region  聊天

        Vector2 rawBtnSize;
        Vector2 rawBtnPos;

        void RefushChatShow()
        {

            return;
            //if (chatBtn && outChatRoot)
            //{
            //    bool isUseNew = UIMainSwitchMgr.I.useNewUI;
            //    //var flag = GameData.I.LevelData.CanToDoByGameLevel(MetaConfig.UnlockChatBtn);
            //    //if (flag)
            //    {
            //        chatBtn.SetActive(!isUseNew);
            //        outChatRoot.SetActive(isUseNew);
            //    }
            //    //else 
            //    //{
            //    //    chatBtn.SetActive(false);
            //    //    outChatRoot.SetActive(false);
            //    //}
            //    if (_Btns)
            //    {
            //        if (isUseNew)
            //        {
            //            float offSize = 60f;
            //            //_AnnouncementRoot.sizeDelta = new Vector2(_AnnouncementRoot.sizeDelta.x, _AnnouncementRoot.sizeDelta.y - offSize);
            //            _Btns.sizeDelta = new Vector2(rawBtnSize.x, rawBtnSize.y - offSize / 2);
            //            _Btns.localPosition = new Vector3(rawBtnPos.x, rawBtnPos.y, _Btns.localPosition.z - offSize - 10);
            //        }
            //        else
            //        {
            //            _Btns.localPosition = rawBtnPos;
            //            _Btns.sizeDelta = rawBtnSize;
            //        }
            //    }
            //}
        }

        #endregion

        void ShowMedal() 
        {
            var flag = LSeason.I.IsInCrossSeason();
            if (!flag) 
            {
                kvkMedalRoot.SetActive(false);
                return;
            }
            kvkMedalRoot.SetActive(true);
            var dic = LAllianceMain.I.GetMedals();
            int count = kvkMedalPar.childCount;
            for (int i = count; i < dic.Count; i++)
            {
                var obj =  GameObject.Instantiate(kvkMedalItem, kvkMedalPar);
                if (obj)
                    obj.SetActive(true);
            }
        }


        public void Pop()
        {
           
            SceneManager.I.IsNeedUImainClickCheckTime = false;
            //打开其他界面
            if (WorldSwitchMgr.I.ShowWorldType == WorldTypeEnum.CITY)
            {
                //需要切回菜单
                //UIMain.I?.TrySwitch(MainMenuConst.TRAIN);
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.CITY);
            }
            else
            {
                //需要切回菜单
                //UIMain.I?.TrySwitch(MainMenuConst.WORLD);
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
            }
        }

        protected internal override void OnCloseComplete()
        {
            base.OnCloseComplete();
        
            menberWidget?.Destroy();
            infoWidget?.Destroy();
            FrameUpdateMgr.UnregisterPerSecondFunListUpdate(this);
            EventMgr.UnregisterEvent(this);
            if(tab1)
                tab1.isOn = true;
            curSelectPage = AllianceTabType.Info;
            timerID = -1;
            UIMainSwitchMgr.I.onChange -= RefushChatShow;
            
        }
         

        void InitTop()
        {
            translateText = GetComponent<TextTranslate>("Root/AlliancePage/Announcement/ScrollView/Viewport/Content/Text");
            allianceNoiceTitle = GetComponent<TFWText>("Root/AlliancePage/Announcement/Title");
            allianceNoiceTime = GetComponent<TFWText>("Root/AlliancePage/Announcement/Time");
            allianceNoiceDescParRT = GetComponent<RectTransform>("Root/AlliancePage/Announcement/ScrollView/Viewport/Content");
            allianceNoiceDesc = GetComponent<TFWText>("Root/AlliancePage/Announcement/ScrollView/Viewport/Content/Text");
            editBtnObj = GetChild("Root/AlliancePage/Announcement/EditBtn");
            mailBtnObj = GetChild("Root/AlliancePage/Announcement/MailBtn");

            UIBase.AddListener(TFW.EventTriggerType.Click, mailBtnObj, (x, y) =>
            {
                OnClickMailBtn();
            });
            UIBase.AddListener(TFW.EventTriggerType.Click, editBtnObj, (x, y) =>
            {
                OnClickEditBtn();
            });
            translateWidget = new TranslateWidget(translateText, GetChild("Root/AlliancePage/Announcement/TranslateBtn"));
            //if (K1D2Config.IsSq())
            //{
            //    translateWidget.SetRootVisible(false);
            //}
            infoWidget = new AllianceInfoWidget(GetChild("Root/AllianceInfoMine"));
            starLevelInfoWidget = new StarLevelInfoWidget(GetChild("Root/KvkLegionStarLevel"));
        }
        void InitInfoPageDownBtns() 
        {
            ///主界面
            btnsDic.Add((int)AllianceItemEnum.war, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/War"), AllianceItemEnum.war));
            btnsDic.Add((int)AllianceItemEnum.gift, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Gift"), AllianceItemEnum.gift));
            btnsDic.Add((int)AllianceItemEnum.challenge, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Challenge"), AllianceItemEnum.challenge));
            btnsDic.Add((int)AllianceItemEnum.science, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Tech"), AllianceItemEnum.science));
            btnsDic.Add((int)AllianceItemEnum.land, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Area"), AllianceItemEnum.land));
            btnsDic.Add((int)AllianceItemEnum.shop, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Store"), AllianceItemEnum.shop));
            btnsDic.Add((int)AllianceItemEnum.mainRank, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Rank"), AllianceItemEnum.mainRank));
            btnsDic.Add((int)AllianceItemEnum.achievement, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/Goal"), AllianceItemEnum.achievement));
            btnsDic.Add((int)AllianceItemEnum.LavaCave, new AllianceClickItemWidget(GetChild("Root/AlliancePage/Btns/LavalCave"), AllianceItemEnum.LavaCave));

            ///设置界面
            btnsDic.Add((int)AllianceItemEnum.messgae, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Message"), AllianceItemEnum.messgae));
            btnsDic.Add((int)AllianceItemEnum.setting, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Set"), AllianceItemEnum.setting));
            btnsDic.Add((int)AllianceItemEnum.log, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Note"), AllianceItemEnum.log));
            btnsDic.Add((int)AllianceItemEnum.rank, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Lederboard"), AllianceItemEnum.rank));
            btnsDic.Add((int)AllianceItemEnum.allianceList, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/List"), AllianceItemEnum.allianceList));
            btnsDic.Add((int)AllianceItemEnum.moveCity, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/MoveCity"), AllianceItemEnum.moveCity));
            btnsDic.Add((int)AllianceItemEnum.publicRecruit, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Recruitment"), AllianceItemEnum.publicRecruit));
            btnsDic.Add((int)AllianceItemEnum.quit, new AllianceClickItemWidget(GetChild("Root/SettingPage/ScrollView/ViewPort/Content/Exit"), AllianceItemEnum.quit));
        }


        void SetDownAnnounceDesc() 
        {
            if(allianceNoiceDescParRT)
                allianceNoiceDescParRT.anchoredPosition = Vector2.zero;
        }


        async UniTask CheckRed()
        {
            if (btnsDic == null)
                return;

            if (LGameRedPoint.I.OpenClientRed)
            {
                f1 = false;
                f3 = false;
                foreach (var item in btnsDic)
                {
                    if (item.Value == null)
                        continue;

                    var t = await item.Value.RefushRed();
                    if (t != AllianceItemEnum.none)
                    {
                        if (t < AllianceItemEnum.messgae)
                        {
                            f1 = true;
                        }
                        else
                        {
                            f3 = true;
                        }
                    }
                }
                tab1Red?.SetData(f1 ? 1 : 0, false);
                tab3Red?.SetData(f3 ? 1 : 0, false);
            }
        }

        int timerID = -1;

        void RefushTabPage()
        {
            //allianceNoiceDesc.text = string.Empty;
            AlliancePage?.SetActive(curSelectPage == AllianceTabType.Info);
            settingPage?.SetActive(curSelectPage == AllianceTabType.Setting);
            MemberPage?.SetActive(curSelectPage == AllianceTabType.Member);
            SetDownAnnounceDesc();
            UpdateAllianceData();
            if (btnsDic != null)
            {
                foreach (var item in btnsDic)
                {
                    item.Value?.CheckShow();
                }
            }

            if (curSelectPage == AllianceTabType.Member)
            {
                if (menberWidget == null) 
                {
                    menberWidget = new AllianceMemberWidget(MemberPage);
                }
                menberWidget.SetData(new AllianceMenberWidgetData()
                {
                    isCanShowTips = true,
                    allianceInfo = LAllianceMgr.I.GetUnionInfo(),
                    root = GameObject
                });
            }
            //if(timerID != -1) 
            //{
            //    NTimer.Destroy(timerID);
            //}
            //timerID = NTimer.CountDown(1.2f, () =>
            //{
            //    //SetDownAnnounceDesc();
            //});
        }

        /// <summary>
        ///  更新联盟信息
        /// </summary>
        void UpdateAllianceData()
        {
            var data = LAllianceMgr.I.GetUnionInfo();
            infoWidget?.SetData(new AllianceInfoWidgetData()
            {
                data = data,
                clickInfoBtn = OnClickInfo,
                isShowInfoBtn = curSelectPage == AllianceTabType.Member
            });
        }

        /// <summary>
        /// 更新联盟公告信息
        /// </summary>
        void UpDataNoiceData()
        {
            if(noiceAnimator)
                noiceAnimator.enabled = true;
            if (allianceNoiceDesc && allianceNoiceTitle && allianceNoiceTime)
            {
                var announcementStr = LAllianceMgr.I.GetUnionDesc();
                var time = LAllianceMgr.I.GetUnionModifyTime();
                var info = LAllianceMgr.I.GetUnionInfo();
                string leaderName = "";
                if (info != null)
                {
                    if (info.LeaderHead != null)
                    {
                        //leaderName = string.Format("[color=#FFD675FF>[{0}]</color> <color=#66E8FF>[{1}]</color>: ", info.NickName, info.LeaderHead.name);
                        //leaderName = $"[<color=#FFD675FF>{info.NickName}</color>] {info.LeaderHead.name}";
                        leaderName = info.LeaderHead.name;
                    }
                }
                if (allianceNoiceDesc)
                {
                    if (string.IsNullOrEmpty(announcementStr))
                    {
                        allianceNoiceDesc.isUseSrcText = false;
                        allianceNoiceDesc.text = LocalizationMgr.Get("Alliance_pre_note");
                        allianceNoiceTitle.text = leaderName;
                    }
                    else
                    {
                        allianceNoiceDesc.isUseSrcText = false;
                        allianceNoiceDesc.text = announcementStr;
                        allianceNoiceTitle.text = LAllianceMgr.I.GetUnionModifyName();
                    }
                }

                //公告文本缓存，阿语必须缓存源文本，反则会出现翻译问题

                //if(!K1D2Config.IsSq())
                {
                    translateWidget?.SetData(announcementStr);
                }
                if (allianceNoiceTime)
                {
                    ///容错旧版本数据
                    if (time == 0)
                    {
                        allianceNoiceTitle.text = leaderName;
                        allianceNoiceTime.text = "";
                        return;
                    }
                    System.DateTime startTime = new System.DateTime(1970, 1, 1, 0, 0, 0, 0);
                    System.DateTime date = startTime.AddTicks(time * 10000);
                    date = date.ToLocalTime();
                    allianceNoiceTime.text = $"{LocalizationMgr.Format("LC_MENU_time_date_format", date.Year, date.Month, date.Day)} {date.Hour:D2}:{date.Minute:D2}";
                }
            }
            //NTimer.CountDown(1.2f, () =>
            // {
            //     SetDownAnnounceDesc();
            // });
        }

        #region

        private void OnPreSecondUpdate(float obj)
        {
          
        }


        void OnAllianceInfoDetail(object [] arr) 
        {
            UpdateAllianceData();
            UpDataNoiceData();
        }

        /// <summary>
        /// 解散联盟
        /// </summary>
        void OnAllianceDisband(object[] arr)
        {
            if (arr == null)
                return;
            if((bool)arr[0])
                OnClickClose();
        }

        void OnAllianceExiting(object[] arr)
        {
            OnClickClose();
        }

        int closeTimerID = -1;

        void OnOnSwitchMainUI(object [] arr) 
        {
            return;
            //if (arr == null)
            //    return;
            //MainMenuType type = (MainMenuType)arr[0];
            //if (type != MainMenuType.ALLIANCE)
            //{
            //    if (closeTimerID != -1)
            //        NTimer.Destroy(closeTimerID);
            //    closeTimerID = NTimer.CountDown(SceneManager1.I.ChangeSceneDuration, () =>
            //     {
            //         CloseSelf();
            //     });
                
            //}
        }

        #endregion

        #region 点击事件


        void OnClickClose() 
        {
            if(tab1)
                tab1.isOn = true;
            curSelectPage = AllianceTabType.Info;
             
            Pop();
            //if (pageData != null)
            //{
            //    if (pageData.CurrentTabKey == MainMenuType.ALLIANCE || pageData.CurrentTabKey == MainMenuType.NONE)
            //    {
            //        if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE && GameData.I.MainData.CurrMenuType != MainMenuType.NONE)
            //            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)GameData.I.MainData.CurrMenuType);
            //        else
            //            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.TRAIN);
            //    }
            //    else
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)pageData.CurrentTabKey);
            //}
            //else
            //{
            //    if (GameData.I.MainData.CurrMenuType != MainMenuType.ALLIANCE && GameData.I.MainData.CurrMenuType != MainMenuType.NONE)
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)GameData.I.MainData.CurrMenuType);
            //    else
            //        EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.TRAIN);
            //}

        }

        void OnClickEditBtn()
        {
            var info = LAllianceMgr.I.GetUnionInfo();
            if (info != null)
            {
                var desc = info?.Desc;
                if (string.IsNullOrEmpty(desc))
                {
                    var canUpdateDesc = LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateDesc);
                    if (!canUpdateDesc)
                    {
                        //没有权限才显示 
                        //Alliance_Notice_Default_Desc  盟主没有发布通知  The Alliance leader made no notice
                        desc = LocalizationMgr.Get("Alliance_Notice_Default_Desc");
                    }
                    else
                    {
                        //否则需要显示点击输入
                    }
                }

                PopupManager.I.ShowDialog<UIAllianceAnnouncement>(
                    new UIAllianceAnnouncementData()
                    {
                        announcement = string.IsNullOrEmpty(desc) ? LocalizationMgr.Get("Alliance_pre_note") : desc,
                    });
            }
        }

        void OnClickMailBtn()
        {
            var classInfo = LAllianceMgr.I.GetMemberClassInfo(LPlayer.I.PlayerID);
            if (classInfo != null && classInfo.Class >= (int)AllianceClassEnum.Class4)
            {
                PopupManager.I.ShowPanel<UIWriteUnionMail>(new UIWriteUnionMail.UIWriteUnionMailData()
                {
                    Receivers = new List<UI.Mail.Receiver>
                    {
                        new UI.Mail.Receiver { id = LPlayer.I.PlayerID, name = LPlayer.I.PlayerName }
                    }
                });
            }
            else
            {
                // R4及以上成员才可以发送联盟邮件
                FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("ERRCODE_unionmailnotpermit"));
            }
        }

        void OnClickInfo()
        {
            if (curSelectPage == AllianceTabType.Member)
            {
                PopupManager.I.ShowPanel<UIAlliancePermission>();
            }
            else
            {
                PopupManager.I.ShowPanel<UIAlliancePermission>();
                ///不需要显示一般规则信息界面了
                //UIActivityRuleData rule = new UIActivityRuleData();
                //rule.isKey = false;
                //rule.title = LocalizationMgr.Get("LevelRank_Text02");
                //DeepUI.PopupManager.I.ShowDialog<UIActivityRules>(rule);
            }
        }

        #endregion

    }

}
