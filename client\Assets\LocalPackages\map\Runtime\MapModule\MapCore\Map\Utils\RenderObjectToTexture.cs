﻿// ��Ȩ����[�ɶ����������Ƽ��ɷ����޹�˾]
// ���ݡ�������Ϣʹ�����֤��������;
// ���Ƿ�����ɣ�����������ʹ�ô��ļ���
// ������������λ�û�ȡ���֤���������ӵ�ַ��
 
// �������÷���Ҫ�������ͬ�⣬��������Ϣ����ʹ�����֤Ҫ��ʹ�ã��������κ���ʾ��ʾ�ı�֤��������
// �йع���Ȩ�޵��ض����ԣ���������֤������


using UnityEngine;

namespace TFW.Map
{
    public class RenderObjectToTexture
    {
        public RenderObjectToTexture(Camera camera, GameObject parent)
        {
            mRoot = new GameObject("RenderObjectToTexture");
            if (parent != null)
            {
                mRoot.transform.parent = parent.transform;
            }
            Utils.HideGameObject(mRoot);

            mDestroyCamera = false;
            if (camera == null)
            {
                var obj = new GameObject("Camera");
                obj.transform.parent = mRoot.transform;
                camera = obj.AddComponent<Camera>();
                mDestroyCamera = true;
            }

            mCamera = camera;
            mCamera.cullingMask = 1 << LayerMask.NameToLayer("RenderToTexture");
            mCamera.orthographic = true;
            mCamera.clearFlags = CameraClearFlags.Color;
            mCamera.backgroundColor = new Color(0, 0, 0, 0);
            mCamera.nearClipPlane = 0.1f;
            mCamera.farClipPlane = 100000f;
            mCamera.enabled = false;
            mCamera.depth = 1;

            CreateReplacementCamera();
        }

        void CreateReplacementCamera()
        {
            var cameraObj = new GameObject("Replacement Camera");
            cameraObj.transform.parent = mRoot.transform;
            mReplacementCamera = cameraObj.AddComponent<Camera>();
        }

        public void OnDestroy(bool isEditor)
        {
            if (mRenderTexture)
            {
                if (isEditor)
                {
                    Object.DestroyImmediate(mRenderTexture);
                }
                else
                {
                    if (mCamera)
                    {
                        mCamera.targetTexture = null;
                    }
                    Object.Destroy(mRenderTexture);
                }
                mRenderTexture = null;
            }

            Utils.DestroyObject(mRoot);

            if (mDestroyCamera)
            {
                if (mCamera != null)
                {
                    Utils.DestroyObject(mCamera.gameObject);
                }
            }
            mCamera = null;
            mReplacementCamera = null;
            mLight = null;
        }

        //overrideBounds:ǿ��ʹ��overrideBounds��Ϊ��Ұ�ķ�Χ
        public void Render(GameObject obj, bool renderFront, float cameraExtraDistance, RenderTexture customRenderTexture, bool hasLight, bool useReplacementCamera, Bounds overrideBounds)
        {
            if (hasLight)
            {
                CreateLight();
                mLight.enabled = true;
            }

            mCamera.transform.gameObject.SetActive(true);
            mCamera.enabled = true;

            if (overrideBounds.extents == Vector3.zero)
            {
                overrideBounds = GameObjectBoundsCalculator.CalculateBounds(obj, false);
            }
            var objectSize = overrideBounds.size;

            float cameraDistance = 0;
            float objectWidth = 0;
            float objectHeight = 0;
            if (renderFront)
            {
                objectWidth = objectSize.x;
                objectHeight = objectSize.y;
                cameraDistance = cameraExtraDistance;
            }
            else
            {
                objectWidth = objectSize.x;
                objectHeight = objectSize.z;
                cameraDistance = cameraExtraDistance;
            }

            RenderTexture texture = customRenderTexture;

            if (customRenderTexture == null)
            {
                SetRenderTextureSize(textureSize, textureSize);
                texture = mRenderTexture;
            }

            texture.wrapMode = TextureWrapMode.Clamp;

            SetCameraPosition(obj, overrideBounds.center, objectWidth, objectHeight, cameraDistance, renderFront);

            //set object layer to render layer
            int renderLayer = LayerMask.NameToLayer("RenderToTexture");
            LayerHelper.BeginSetLayerRecursively(obj, renderLayer);

            mCamera.targetTexture = texture;
            mCamera.Render();

            if (useReplacementCamera)
            {
                //fix cutoff objects
                mReplacementCamera.CopyFrom(mCamera);
                mReplacementCamera.clearFlags = CameraClearFlags.Nothing;
                var replacementShader = Shader.Find("SLGMaker/ReplacementShader");
                Debug.Assert(replacementShader != null, "Invalid replacement shader!");
                mReplacementCamera.RenderWithShader(replacementShader, "RenderType");
            }

            LayerHelper.RestoreLayerRecursively(obj);

            mCamera.enabled = false;
            mCamera.transform.gameObject.SetActive(false);
            if (hasLight)
            {
                mLight.enabled = false;
            }
        }

        void CreateLight()
        {
            if (mLight == null)
            {
                var lightObj = new GameObject("Light");
                mLight = lightObj.AddComponent<UnityEngine.Light>();
                lightObj.transform.parent = mRoot.transform;
                mLight.enabled = false;
                mLight.cullingMask = 1 << LayerMask.NameToLayer("RenderToTexture");
                mLight.type = LightType.Directional;
                mLight.color = new Color(233 / 255.0f, 212 / 255.0f, 166 / 255.0f, 1.0f);
                lightObj.transform.position = new Vector3(424.6f, 10.6f, 63.9f);
                lightObj.transform.rotation = Quaternion.Euler(66.703f, -56.319f, 16.689f);
            }
        }

        void SetRenderTextureSize(int width, int height)
        {
            while (true)
            {
                if (mRenderTexture == null)
                {
                    //���ͼƬ���ɴ�С��ֵ��⴦��
                    if (width <= 0)
                        width = 256;
                    if (height <= 0)
                        height = 256;

                    mRenderTexture = new RenderTexture(width, height, 24, RenderTextureFormat.Default, RenderTextureReadWrite.Default);
                }

                if (mRenderTexture.width != width || mRenderTexture.height != height)
                {
                    if (mCamera)
                    {
                        mCamera.targetTexture = null;
                    }
                    GameObject.Destroy(mRenderTexture);
                    mRenderTexture = null;
                }

                if (mRenderTexture)
                {
                    break;
                }
            }
        }

        void SetCameraPosition(GameObject obj, Vector3 objCenter, float objectWidth, float objectHeight, float cameraDistance, bool renderFront)
        {
            mCamera.orthographicSize = objectHeight * 0.5f;
            mCamera.aspect = objectWidth / (float)objectHeight;

            if (renderFront)
            {
                mCamera.transform.position = objCenter + Vector3.forward * cameraDistance;
                mCamera.transform.LookAt(objCenter, Vector3.up);
            }
            else
            {
                mCamera.transform.position = objCenter + Vector3.up * cameraDistance;
                mCamera.transform.LookAt(objCenter, Vector3.forward);
            }
        }

        public void SaveTexture(RenderTexture renderTexture, string path, bool hasAlpha)
        {
            byte[] bytes = ToTexture2D(renderTexture, hasAlpha).EncodeToTGA();
            System.IO.File.WriteAllBytes(path, bytes);
        }

        Texture2D ToTexture2D(RenderTexture renderTexture, bool hasAlpha)
        {
            TextureFormat format = TextureFormat.RGB24;
            if (hasAlpha)
            {
                format = TextureFormat.ARGB32;
            }
            Texture2D tex = new Texture2D(renderTexture.width, renderTexture.height, format, false, false);
            tex.wrapMode = TextureWrapMode.Clamp;
            RenderTexture.active = renderTexture;
            tex.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
            tex.Apply();
            RenderTexture.active = null;
            return tex;
        }

        public RenderTexture renderTexture { get { return mRenderTexture; } }
        public int textureSize { set { mTextureSize = value; } get { return mTextureSize; } }

        Camera mCamera;
        Camera mReplacementCamera;
        GameObject mRoot;
        UnityEngine.Light mLight;
        int mTextureSize = 1024;
        RenderTexture mRenderTexture;
        bool mDestroyCamera = false;
    }
}
