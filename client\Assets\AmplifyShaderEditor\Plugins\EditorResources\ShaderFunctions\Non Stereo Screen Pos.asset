%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Non Stereo Screen Pos
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17005\n1931;362;1056;538;1167.983;206.851;1.3;True;False\nNode;AmplifyShaderEditor.FunctionInput;23;-544,0;Inherit;False;Screen
    Pos;4;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;21;-752,0;Inherit;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;22;-384,0;Inherit;False;#if
    UNITY_SINGLE_PASS_STEREO$float4 scaleOffset = unity_StereoScaleOffset[ unity_StereoEyeIndex
    ]@$UV.xy = (UV.xy - scaleOffset.zw) / scaleOffset.xy@$#endif$return UV@;2;False;1;True;UV;FLOAT2;0,0;In;;Float;False;UnStereo;False;False;0;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-224,0;Inherit;False;True;UV;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nWireConnection;23;0;21;0\nWireConnection;22;0;23;0\nWireConnection;0;0;22;0\nASEEND*/\n//CHKSM=C807B44314507262BB266549414C48D41B38178A"
  m_functionName: 
  m_description: Transforms a Screen Position values from VR Stereo to non Stereo
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
