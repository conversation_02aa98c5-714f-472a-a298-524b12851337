﻿









using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using ICSharp.SharpZipLib.Zip;
using UnityEditor;
using UnityEngine;

namespace TFW.SDK.Editor
{
    public partial class SDKConfigBuilder
    {
        [MenuItem("Framework/SDK/ConfigBuilder/ALL/Build", false, 2000)]
        public static void BuildSDKConfigAll()
        {
            try
            {
                CallAllMethodWithAttribute(typeof(SDKConfigBuilderAttribute));
            }
            catch (Exception e)
            {
                Debug.LogError("Build SDKConfig All Exception: " + e);
                throw;
            }
        }

        #region Const
        const string SDK_CONFIG_PATH = "SDKConfig/";
        const string SDK_PATH = "SDK/";
        #endregion

        #region Zip
        static bool UnZip(string zipFile, string targetPath, string password = null)
        {
            try
            {
                if (!Directory.Exists(targetPath))
                {
                    Directory.CreateDirectory(targetPath);
                }

                using (ZipInputStream zis = new ZipInputStream(File.OpenRead(zipFile)))
                {
                    if (!string.IsNullOrEmpty(string.Empty))
                    {
                        zis.Password = Md5(password);
                    }

                    ZipEntry entry;
                    while ((entry = zis.GetNextEntry()) != null)
                    {
                        string file = entry.Name.Replace('\\', '/');
                        if (file.Contains("/"))
                        {
                            string dirs = targetPath + "/" + Path.GetDirectoryName(file);
                            if (!Directory.Exists(dirs))
                            {
                                Directory.CreateDirectory(dirs);
                            }
                        }

                        if (entry.IsFile)
                        {
                            FileStream fs = new FileStream(targetPath + "/" + file, FileMode.OpenOrCreate,
                                FileAccess.ReadWrite);

                            int size = 1024 * 64;
                            byte[] buffer = new byte[size];

                            while (size > 0)
                            {
                                size = zis.Read(buffer, 0, buffer.Length);
                                if (size > 0)
                                {
                                    fs.Write(buffer, 0, size);
                                }
                            }

                            fs.Close();
                            fs.Dispose();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError("SDKConfigBuilder UnZip: " + zipFile + " to: " + targetPath + " Exception: " + e.Message);
                return false;
            }

            return true;
        }

        static string Md5(string data)
        {
            var res = "";
            MD5 md = MD5.Create();
            byte[] s = md.ComputeHash(Encoding.Default.GetBytes(data));

            for (int i = 0; i < s.Length; i++)
            {
                res = res + s[i].ToString("X");
            }

            return res;
        }
        #endregion

        #region Utils
        static List<string> paths = new List<string>();
        static List<string> files = new List<string>();

        static void Recursive(string path, bool filterMeta = true)
        {
            string[] names = Directory.GetFiles(path);
            string[] dirs = Directory.GetDirectories(path);

            foreach (string filename in names)
            {
                string ext = Path.GetExtension(filename);
                if (filterMeta && ext.Equals(".meta")) continue;
                if (filterMeta && ext.Equals(".DS_Store")) continue;
                files.Add(filename.Replace('\\', '/'));
            }

            foreach (string dir in dirs)
            {
                Recursive(dir, filterMeta);
                paths.Add(dir.Replace('\\', '/'));
            }
        }

        public static void ClearPath(string path)
        {
            files.Clear();
            paths.Clear();
            Recursive(path, false);

            foreach (string filename in files)
            {
                File.Delete(filename);
            }

            foreach (string dir in paths)
            {
                Directory.Delete(dir);
            }
        }

        public static void RemoveDir(string path)
        {
            try
            {
                if (Directory.Exists(path))
                {
                    ClearPath(path);

                    Directory.Delete(path);
                }
            }
            catch (Exception e)
            {
                Debug.LogError("RemoveDir Exception: " + e);
            }
        }

        static void RemoveSDKDefaultConfig(string sdk)
        {
            string cfgPath = SDK_CONFIG_PATH + "/" + sdk;
            RemoveDir(Path.Combine(Application.dataPath, cfgPath + "/default"));

            if (File.Exists(cfgPath + "/default.meta"))
            {
                File.Delete(cfgPath + "/default.meta");
            }

            if (File.Exists(cfgPath + "/Readme.txt"))
            {
                File.Delete(cfgPath + "/Readme.txt");
            }

            if (File.Exists(cfgPath + "/Readme.txt.meta"))
            {
                File.Delete(cfgPath + "/Readme.txt.meta");
            }
        }

        public static void BuildSDKDefaultConfig(string sdk, string password = null)
        {
            RemoveSDKDefaultConfig(sdk);

            string cfgPath = Path.Combine(Application.dataPath, SDK_PATH + "/" + sdk + "/Editor/" + sdk + ".zip");
            if (File.Exists(cfgPath))
            {
                UnZip(cfgPath, Path.Combine(Application.dataPath, SDK_CONFIG_PATH), password);
            }
        }
        #endregion

        #region Attribute

        static void CallAllMethodWithAttribute(Type attributeType, params object[] args)
        {
            List<Type> types = new List<Type>()
            {
                typeof(SDKConfigBuilder)
            };

            foreach (var method in (
                from type in types
                from method in type.GetMethods(BindingFlags.Static | BindingFlags.Public)
                where method.IsDefined(attributeType, false)
                select method))
            {
                method.Invoke(null, args);
            }
        }
        #endregion
    }

    public class SDKConfigBuilderAttribute : Attribute
    {
    };
}