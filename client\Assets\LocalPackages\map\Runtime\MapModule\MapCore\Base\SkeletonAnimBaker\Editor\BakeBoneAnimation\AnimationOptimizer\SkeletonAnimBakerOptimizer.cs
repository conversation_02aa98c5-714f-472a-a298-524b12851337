﻿#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    //将使用相同animation controller和bone hierarchy的prefab共享烘培动画数据
    public static partial class SkeletonAnimBakerOptimizer
    {
        static void Reset()
        {
            mOriginalTextures.Clear();
            mRemovedTextures.Clear();
        }

        public static void OptimizePrefabs(string folderPath)
        {
            Reset();
            //先收集优化前信息
            CollectOriginalStats(folderPath);

            //生成所有的bake info
            List<AnimBakeInfo> allBakeInfos = new List<AnimBakeInfo>();
            var entries = Directory.EnumerateFileSystemEntries(folderPath, "*.asset", SearchOption.AllDirectories);
            foreach (var path in entries)
            {
                string name = Utils.GetPathName(path.Replace('\\', '/'), false);
                if (name == MapCoreDef.BAKED_ANIM_DATA_NAME)
                {
                    var bakeInfo = new AnimBakeInfo();
                    bool success = bakeInfo.Process(path);
                    if (success)
                    {
                        allBakeInfos.Add(bakeInfo);
                    }
                }
            }

            //归类相同的bake info
            List<List<AnimBakeInfo>> sameBakeInfoGroups = new List<List<AnimBakeInfo>>();
            while (allBakeInfos.Count > 0)
            {
                List<AnimBakeInfo> group = new List<AnimBakeInfo>();
                group.Add(allBakeInfos[0]);
                for (int i = allBakeInfos.Count - 1; i >= 1; --i)
                {
                    if (allBakeInfos[i].CanShareAnimation(allBakeInfos[0]))
                    {
                        group.Add(allBakeInfos[i]);
                        allBakeInfos.RemoveAt(i);
                    }
                }
                allBakeInfos.RemoveAt(0);
                sameBakeInfoGroups.Add(group);
            }

            //共享group中bake info
            for (int i = 0; i < sameBakeInfoGroups.Count; ++i)
            {
                EditorUtility.DisplayProgressBar("Optimizing Prefab Groups", $"{sameBakeInfoGroups[i][0].originalPrefab.name}", (float)i / sameBakeInfoGroups.Count);
                ShareAnimationDataInGroup(sameBakeInfoGroups[i]);
            }
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            EditorUtility.ClearProgressBar();
        }

        static string GetBakedAnimationDataPath(string folderPath)
        {
            return $"{folderPath}/{MapCoreDef.BAKED_ANIM_DATA_NAME}.asset";
        }

        static string GetAnimationTexturePath(string folderPath)
        {
            string tgaPath = $"{folderPath}/{MapCoreDef.BAKED_ANIM_TEXTURE_NAME}.tga";
            if (File.Exists(tgaPath))
            {
                return tgaPath;
            }
            string pngPath = $"{folderPath}/{MapCoreDef.BAKED_ANIM_TEXTURE_NAME}.png";
            if (File.Exists(pngPath))
            {
                return pngPath;
            }
            string assetPath = $"{folderPath}/{MapCoreDef.BAKED_ANIM_TEXTURE_NAME}.asset";
            if (File.Exists(assetPath))
            {
                return assetPath;
            }
            return null;
        }

        static List<Material> FindMaterialsInFolder(string folderPath)
        {
            List<Material> mtls = new List<Material>();
            var files = Directory.EnumerateFiles(folderPath);
            foreach (var path in files)
            {
                if (path.EndsWith(".mat"))
                {
                    var mtl = AssetDatabase.LoadAssetAtPath<Material>(path);
                    if (mtl != null)
                    {
                        mtls.Add(mtl);
                    }
                }
            }
            return mtls;
        }

        static void ShareAnimationDataInGroup(List<AnimBakeInfo> group)
        {
            if (group.Count > 1)
            {
                string masterBakeFolderPath = Utils.GetFolderPath(AssetDatabase.GetAssetPath(group[0].bakedAnimationData));
                string animTexturePath = GetAnimationTexturePath(masterBakeFolderPath);
                Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(animTexturePath);
                string bakedAnimationDataPath = GetBakedAnimationDataPath(masterBakeFolderPath);
                BakedAnimationData animationData = AssetDatabase.LoadAssetAtPath<BakedAnimationData>(bakedAnimationDataPath);
                Debug.Assert(texture != null);
                Debug.Assert(animationData != null);
                for (int i = 1; i < group.Count; ++i)
                {
                    string slaveFolderPath = Utils.GetFolderPath(AssetDatabase.GetAssetPath(group[i].bakedAnimationData));
                    //删除slave动画贴图,使用master的贴图
                    string slaveAnimationTexturePath = GetAnimationTexturePath(slaveFolderPath);
                    if (slaveAnimationTexturePath != null) {
                        Texture2D slaveTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(slaveAnimationTexturePath);

                        int memorySize;
                        if (slaveTexture.format == TextureFormat.RGBA32)
                        {
                            memorySize = slaveTexture.width * slaveTexture.height * 4;
                        }
                        else
                        {
                            memorySize = slaveTexture.width * slaveTexture.height * 4 * 2;
                        }
                        mRemovedTextures.Add(new TextureStats(memorySize, slaveAnimationTexturePath));
                        FileUtil.DeleteFileOrDirectory(slaveAnimationTexturePath);
                    }
                    //设置材质贴图
                    List<Material> materials = FindMaterialsInFolder(slaveFolderPath);
                    foreach (var mtl in materials)
                    {
                        mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, texture);

                        EditorUtility.SetDirty(mtl);
                    }

                    //删除slave的cpu driven bone transform data
                    string slaveCPUDrivenBoneTransformDataPath = string.Format($"{slaveFolderPath}/{MapCoreDef.BAKED_CPU_DRIVEN_BONE_TRANSFORM_DATA_NAME}.asset");
                    if (File.Exists(slaveCPUDrivenBoneTransformDataPath))
                    {
                        FileUtil.DeleteFileOrDirectory(slaveCPUDrivenBoneTransformDataPath);
                    }
                    //设置共享的cpu driven bone transform data
                    string slaveBakedAnimationDataPath = GetBakedAnimationDataPath(slaveFolderPath);
                    BakedAnimationData slaveAnimationData = AssetDatabase.LoadAssetAtPath<BakedAnimationData>(slaveBakedAnimationDataPath);
                    slaveAnimationData.cpuDrivenBoneTransformData = animationData.cpuDrivenBoneTransformData;
                    EditorUtility.SetDirty(slaveAnimationData);
                }
            }
        }

        public static List<TextureStats> removedTextureStats { get { return mRemovedTextures; } }
        public static List<TextureStats> originalTextureStats { get { return mOriginalTextures; } }
    }
}

#endif