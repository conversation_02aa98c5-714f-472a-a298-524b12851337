﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        //左手坐标系的left turn，用左手坐标系是因为unity是左手坐标系
        bool IsLeftTurnLH(Vector3 a, Vector3 b)
        {
            float d = Vector3.Cross(a, b).y;
            return d <= 0;
        }

        int Mod(int a, int n)
        {
            if (a < 0)
            {
                return a + n;
            }
            else if (a >= n)
            {
                return a - n;
            }
            return a;
        }
    }
}


#endif