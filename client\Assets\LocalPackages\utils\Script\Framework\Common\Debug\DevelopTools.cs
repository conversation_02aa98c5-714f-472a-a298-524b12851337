using System;
using System.Collections;
using System.Collections.Generic;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using UnityEngine;

namespace TFW
{
    public class DTButtonItem
    {
        public string Description;
        public object Value;
        public Func<object> valueFunc;
        public Action<DTButtonItem> Callback;
    }

    /// <summary>
    /// 开发者工具类
    /// </summary>
    public class DevelopTools : MonoBehaviour
    {
        // 统计1s内的平均帧率
        private int m_LastFrameCount;

        private float m_LastCalcTime;

        private const float m_RecalculateDelta = 1;

        private float m_Fps;

        private string m_Ip = "";

        private bool m_SettingQuality;

        private bool m_SettingShadow;

        private bool m_SettingEnv;

        private bool m_SettingLocalization;

        private bool m_ShowDetail;

        private bool m_ShowLocalization;

        private static GUIStyle m_NormalFont;

        private GUIStyle m_QualityFont;

        private GUIStyle m_GreenFont;

        private GUIStyle m_YellowFont;

        private GUIStyle m_RedFont;

        private static List<DTButtonItem> m_ButtonItem = new List<DTButtonItem>();

        //private static bool m_DefalutOpenFTE = true;

        //public bool isOpenFTE = false;

        private void Awake()
        {
            m_NormalFont = new GUIStyle
            {
                normal = { textColor = Color.white },
                fontSize = 30,
                padding = new RectOffset(500, 0, 0, 0)
            };

            m_QualityFont = new GUIStyle(m_NormalFont)
            {
                padding = new RectOffset(500 + 110, 0, 0, 0)
            };

            m_GreenFont = new GUIStyle
            {
                normal = { textColor = Color.green },
                fontSize = 30,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(500, 0, 0, 0)
            };

            m_YellowFont = new GUIStyle
            {
                normal = { textColor = Color.yellow },
                fontSize = 30,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(500, 0, 0, 0)
            };

            m_RedFont = new GUIStyle
            {
                normal = { textColor = Color.red },
                fontSize = 30,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(500, 0, 0, 0)
            };

//#if USE_REPORTER || UNITY_EDITOR
//            DevelopTools.AddButtonItem<DTButtonItem>(new DTButtonItem()
//            {
//                Description = "Enable FTE",
//                Value = PlayerPrefs.GetInt("EnableFTE", m_DefalutOpenFTE ? 1 : 0) == 1,
//                Callback = (item) =>
//                {
//                    bool v = (bool)item.Value;
//                    item.Value = !v;
//                    PlayerPrefs.SetInt("EnableFTE", (bool)item.Value ? 1 : 0);
//                }
//            });
//#endif
            SetCustomParam();
        }

        /// <summary>
        /// 设置自定义参数
        /// </summary>
        private void SetCustomParam()
        {
            //FTE
            //PlayerPrefs.SetInt("EnableFTE", m_DefalutOpenFTE ? 1 : 0);
            //
        }

        private void Start()
        {
            //Application.targetFrameRate = GameQuality.LockMaxFPS_Low;
            StartCoroutine(Init());
        }

        private void Update()
        {
            // 帧率
            var t = Time.realtimeSinceStartup - m_LastCalcTime;
            if (t <= m_RecalculateDelta)
            {
                return;
            }

            m_Fps = (Time.frameCount - m_LastFrameCount) / t;
            m_LastFrameCount = Time.frameCount;
            m_LastCalcTime = Time.realtimeSinceStartup;
        }

        // 协程
        private IEnumerator Init()
        {
            yield return new WaitForEndOfFrame();
            m_Ip = GetIpAddress();
        }

        private GUIStyle GetFpsStyle(float fps)
        {
            return fps >= 45 ? m_GreenFont : fps > 25 ? m_YellowFont : m_RedFont;
        }

        public static string Server { get; set; }

        public DevelopTools()
        {
            m_Fps = 0;
            m_LastFrameCount = 0;
            m_LastCalcTime = 0;
        }

        enum LocalizationType
        {
            enable = 0,
            disable = 1
        }

        LightShadows m_LightShadows;
    
        LocalizationType m_LocalizationType;

        private void OnGUI()
        {
            // 字体风格
            var fs = m_NormalFont;

            // FPS
            if (GUILayout.Button($"Fps: {m_Fps:F1}", GetFpsStyle(m_Fps)))
                m_ShowDetail = !m_ShowDetail;

            if (!m_ShowDetail)
                return;

            // 版本
            GUILayout.Space(20);
            GUILayout.Label($"Version: {Application.version:F1},Server: {Server}", fs);

            //// Debug
            //GUILayout.Space(20);
            //GUILayout.Label($"IP: {m_Ip}:{Logger.LogManager.Port}", fs);

            // Camera
            //GUILayout.Space(20);
            //if (Map.Map.currentMap != null)
            //    GUILayout.Label($"Height: {MapCameraMgr.lastCameraHeight} , Fov: {MapMgr.MapCamera?.fieldOfView}", fs);

            // screen resolutions
            GUILayout.Space(20);
            GUILayout.Label($"Screen resolution: {Screen.width} * {Screen.height}", fs);

            //// LOD
            //if (MapMgr.IsLoaded())
            //{
            //    GUILayout.Space(20);
            //    GUILayout.Label($"LOD: {MapMgr.CurrentLod}", fs);
            //}

            //fs.fixedHeight = 50;
            //GUILayout.Space(20);
            //if (GUILayout.Button($"Quality: {(GameQuality.RenderQuality)GameQuality.Quality}", fs))
            //    m_SettingQuality = !m_SettingQuality;

            //if (m_SettingQuality)
            //{
            //    for (var i = 0; i <= 5; ++i)
            //    {
            //        var idx = i;
            //        GUILayout.Space(20);
            //        if (GUILayout.Button("" + (GameQuality.RenderQuality)idx, m_QualityFont))
            //            GameQuality.SetQuality(idx);
            //    }
            //}

            GUILayout.Space(20);
            if (GUILayout.Button($"LightShadows: {m_LightShadows}", fs))
            {
                m_SettingShadow = !m_SettingShadow;
                var light = GameObject.Find("Directional Light");
                if (light != null)
                {
                    var lgt = light.GetComponent<UnityEngine.Light>();
                    m_LightShadows = lgt.shadows;
                }
            }

            if (m_SettingShadow)
            {
                for (var i = 0; i <= 2; ++i)
                {
                    var idx = i;
                    GUILayout.Space(20);
                    if (GUILayout.Button("" + (LightShadows)idx, m_QualityFont))
                    {
                        m_LightShadows = (LightShadows)idx;
                        var light = GameObject.Find("Directional Light");
                        if (light != null)
                        {
                            var lgt = light.GetComponent<UnityEngine.Light>();
                            lgt.shadows = m_LightShadows;
                        }
                    }
                }
            }

            //GUILayout.Space(20);
            //if (GUILayout.Button($"Env: {m_EnvType}", fs))
            //{
            //    m_SettingEnv = !m_SettingEnv;
            //    m_EnvType = (EnvironmentType)PlayerPrefs.GetInt(Entrance.EnvironmentPrefKey, (int)EnvironmentType.Beta);
            //}

            //if (m_SettingEnv)
            //{
            //    for (var i = (int)EnvironmentType.Beta; i <= (int)EnvironmentType.Gold; ++i)
            //    {
            //        var idx = i;
            //        GUILayout.Space(20);
            //        if (GUILayout.Button("" + (EnvironmentType)idx, m_QualityFont))
            //        {
            //            m_EnvType = (EnvironmentType)idx;
            //            PlayerPrefs.SetInt(Entrance.EnvironmentPrefKey, idx);
            //            D.Warning?.Log($"Setting Environment to {(EnvironmentType)PlayerPrefs.GetInt(Entrance.EnvironmentPrefKey)}");
            //        }
            //    }
            //}

            //GUILayout.Space(20);
            //if (GUILayout.Button($"Localization:{m_LocalizationType}", fs))
            //{
            //    m_SettingLocalization = !m_SettingLocalization;
            //    m_LocalizationType = (LocalizationType)PlayerPrefs.GetInt("ShowLCKey", 0);
            //}
            //if (m_SettingLocalization)
            //{
            //    GUILayout.Space(20);
            //    if (GUILayout.Button("" + (LocalizationType)0, m_QualityFont))
            //    {
            //        LocalizationMgr.ShowLCKey = false;
            //        m_LocalizationType = (LocalizationType)0;
            //        PlayerPrefs.SetInt("ShowLCKey", 0);
            //    }
            //    GUILayout.Space(20);
            //    if (GUILayout.Button("" + (LocalizationType)1, m_QualityFont))
            //    {
            //        LocalizationMgr.ShowLCKey = true;
            //        m_LocalizationType = (LocalizationType)1;
            //        PlayerPrefs.SetInt("ShowLCKey", 1);
            //    }
            //}

            // Custom Button
            ProcessButtonItem();
        }

        public static void AddButtonItem<T>(DTButtonItem item)
        {
            if (item != null)
            {
                m_ButtonItem.Add(item);
            }
        }

        public static void ProcessButtonItem()
        {
            foreach (var item in m_ButtonItem)
            {
                GUILayout.Space(20);
                var value = item.valueFunc != null ? item.valueFunc.Invoke() : item.Value;
                if (GUILayout.Button($"{item.Description}: {value}", m_NormalFont))
                {
                    item.Callback?.Invoke(item);
                }
            }
        }


        private string GetIpAddress()
        {
            var ips = string.Empty;
#if UNITY_IOS || UNITY_EDITOR_OSX
            var adapters = NetworkInterface.GetAllNetworkInterfaces();
            foreach (var adapter in adapters)
            {
                if (adapter.Supports(NetworkInterfaceComponent.IPv4))
                {
                    UnicastIPAddressInformationCollection uniCast = adapter.GetIPProperties().UnicastAddresses;
                    if (uniCast.Count > 0)
                    {
                        foreach (UnicastIPAddressInformation uni in uniCast)
                        {
                            //得到IPv4的地址。 AddressFamily.InterNetwork指的是IPv4
                            if (uni.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                var ip = uni.Address.ToString();
                                if (ip.StartsWith("172.") || ip.StartsWith("192."))
                                {
                                    return ip;
                                }
                                ips += " " + ip;
                            }
                        }
                    }
                }
            }
#else
            var hostName = Dns.GetHostName();

            var ipHostEntry = Dns.GetHostEntry(hostName);
            if (ipHostEntry != null && ipHostEntry.AddressList.Length > 0)
            {
                foreach (var addr in ipHostEntry.AddressList)
                {
                    if (addr.AddressFamily == AddressFamily.InterNetwork)
                    {
                        var ip = addr.ToString();
                        if (ip.StartsWith("172.") || ip.StartsWith("192."))
                        {
                            return ip;
                        }

                        ips += " " + ip;
                    }
                }
            }
#endif
            return ips;
        }
    }
}
