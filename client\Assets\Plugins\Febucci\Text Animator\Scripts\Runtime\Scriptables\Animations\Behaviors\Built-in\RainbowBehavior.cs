﻿using System;
using Febucci.UI.Core;
using UnityEngine;

namespace Febucci.UI.Effects
{
    [UnityEngine.Scripting.Preserve]
    [CreateAssetMenu(menuName = "Text Animator/Animations/Behaviors/Rainbow", fileName = "Rainbow Behavior")]
    [EffectInfo("rainb", EffectCategory.Behaviors)]
    public sealed class RainbowBehavior : BehaviorScriptableBase
    {
        public float baseFrequency = 0.5f;
        public float baseWaveSize = 0.08f;

        // 定义两个颜色，在这两个颜色之间渐变
        public Color startColor = Color.blue;
        public Color endColor = Color.red;

        float frequency;
        float waveSize;
        public override void SetModifier(ModifierInfo modifier)
        {
            switch (modifier.name)
            {
                //frequency
                case "f": frequency = baseFrequency * modifier.value; break;
                //wave size
                case "s": waveSize = baseWaveSize * modifier.value; break;

                // 添加颜色修改器
                case "c1":
                    try
                    {
                        // 将浮点数转换为整数，然后转换为十六进制字符串
                        int colorInt = (int)modifier.value;
                        string hexColor = "#" + colorInt.ToString("X6");

                        if (ColorUtility.TryParseHtmlString(hexColor, out Color color1))
                        {
                            startColor = color1;

                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"Error parsing color: {e.Message}");
                    }
                    break;
                case "c2":
                    try
                    {
                        int colorInt = (int)modifier.value;
                        string hexColor = "#" + colorInt.ToString("X6");


                        if (ColorUtility.TryParseHtmlString(hexColor, out Color color2))
                        {
                            endColor = color2;

                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"Error parsing color: {e.Message}");
                    }
                    break;
            }
        }

        public override void ResetContext(TAnimCore animator)
        {
            frequency = baseFrequency;
            waveSize = baseWaveSize;
        }

        Color32 temp;
        public override void ApplyEffectTo(ref Core.CharacterData character, TAnimCore animator)
        {
            for (byte i = 0; i < TextUtilities.verticesPerChar; i++)
            {
                // 使用PingPong在0-1之间循环，用于在两个颜色之间插值
                float t = Mathf.PingPong(animator.time.timeSinceStart * frequency + character.index * waveSize, 1);

                // 在两个颜色之间进行线性插值
                temp = Color.Lerp(startColor, endColor, t);

                // 保留原始alpha值
                temp.a = character.current.colors[i].a;

                // 应用颜色
                character.current.colors[i] = temp;
            }
        }
    }
}


