﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerData : MapLayerData
    {
        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(Map.currentMap.viewport, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            bool lodChanged = false;
            if (!Mathf.Approximately(newZoom, mLastZoom))
            {
                mLastZoom = newZoom;
                lodChanged = SetZoom(newZoom);
            }

            if (mLastViewport != newViewport)
            {
                if (lodChanged)
                {
                    UpdateLOD();
                }
                UpdateLOD0Content(newViewport);
            }

            return lodChanged;
        }

        void UpdateLOD0Content(Rect newViewport)
        {
            if (mCurrentLOD == 0)
            {
                var oldViewRect = GetViewRect(mLastViewport);
                var newViewRect = GetViewRect(newViewport);

                //hide invisible rect fogs
                for (int i = mVisibleRectFogs.Count - 1; i >= 0; --i)
                {
                    if (!mVisibleRectFogs[i].Intersect(newViewport.xMin, newViewport.yMin, newViewport.xMax, newViewport.yMax))
                    {
                        RemoveVisibleFog(i);
                    }
                }

                mOldRect.Set(oldViewRect.minX, oldViewRect.minY, oldViewRect.maxX, oldViewRect.maxY);
                mNewRect.Set(newViewRect.minX, newViewRect.minY, newViewRect.maxX, newViewRect.maxY);
                mRectDiffCalculator.Calculate(mOldRect, mNewRect, mVisibleRects, mInvisibleRects);
                for (int i = 0; i < mVisibleRects.Count; ++i)
                {
                    var xMin = mVisibleRects[i].xMin;
                    var yMin = mVisibleRects[i].yMin;
                    var xMax = mVisibleRects[i].xMax;
                    var yMax = mVisibleRects[i].yMax;
                    for (int y = yMin; y <= yMax; ++y)
                    {
                        for (int x = xMin; x <= xMax; ++x)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                SetObjectVisibility(x, y, true);
                            }
                        }
                    }
                }

                for (int i = 0; i < mInvisibleRects.Count; ++i)
                {
                    var xMin = mInvisibleRects[i].xMin;
                    var yMin = mInvisibleRects[i].yMin;
                    var xMax = mInvisibleRects[i].xMax;
                    var yMax = mInvisibleRects[i].yMax;
                    for (int y = yMin; y <= yMax; ++y)
                    {
                        for (int x = xMin; x <= xMax; ++x)
                        {
                            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                            {
                                SetObjectVisibility(x, y, false);
                            }
                        }
                    }
                }

                mLastViewport = newViewport;
            }
        }

        void RemoveVisibleFog(int i)
        {
            mFogVisibilityChangeCallback(mVisibleRectFogs[i], false);
            mVisibleRectFogs[i].isIntersectedWithViewport = false;
            mVisibleRectFogs.RemoveAt(i);
        }

        void AddVisibleFog(FogRect fog)
        {
            if (mCurrentLOD == 0)
            {
                fog.isIntersectedWithViewport = true;
                mVisibleRectFogs.Add(fog);
                mFogVisibilityChangeCallback(fog, true);
            }
        }

        void SetObjectVisibility(int x, int y, bool visible)
        {
            int idx = y * mCols + x;
            if (mTileTypes[idx] > 0 && mTileTypes[idx] < 16)
            {
                mTileVisibilityChangeCallback(x, y, visible);
            }
            else if (mTileTypes[idx] >= 16)
            {
                if (visible)
                {
                    var fog = GetRectFog(mTileTypes[idx]);
                    if (!fog.isIntersectedWithViewport)
                    {
                        AddVisibleFog(fog);
                    }
                }
            }
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public bool isSwitchingLOD { get { return mIsSwitchingLOD; } }

        Rect mLastViewport;
        float mLastZoom;
        bool mIsSwitchingLOD = false;
        RectDifference mRectDiffCalculator = new RectDifference();
        RectI mOldRect = new RectI();
        RectI mNewRect = new RectI();
        List<RectInt> mVisibleRects = new List<RectInt>(9);
        List<RectInt> mInvisibleRects = new List<RectInt>(9);
        List<FogRect> mVisibleRectFogs = new List<FogRect>();

        System.Action<int, int, bool> mTileVisibilityChangeCallback;
        System.Action<FogRect, bool> mFogVisibilityChangeCallback;
    }
}
