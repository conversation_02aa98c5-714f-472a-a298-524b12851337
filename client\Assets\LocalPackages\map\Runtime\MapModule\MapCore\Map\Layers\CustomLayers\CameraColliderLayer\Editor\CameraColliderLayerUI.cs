﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [CustomEditor(typeof(CameraColliderLayerLogic))]
    public partial class CameraColliderLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as CameraColliderLayerLogic;

            mLogic.UpdateGizmoVisibilityState();

            var layerData = mLogic.layer.GetLayerData();

            mLogic.layer.ShowOutline(mLogic.layer.displayType);

            mEditor = new CameraColliderEditor(mLogic.layer, Repaint, RepaintScene);
            var editVertex = new EditCameraColliderVertex(mEditor);
            var createCameraCollider = new CreateCameraColliderOutline(mEditor);
            var moveCameraCollider = new MoveCameraCollider(mEditor);
            mEditor.AddTool(editVertex);
            mEditor.AddTool(createCameraCollider);
            mEditor.AddTool(moveCameraCollider);
            mEditor.SetActiveTool(mLogic.operation);

            var handlers = mLogic.layer.GetEventHandlers();
            handlers.onOutlineChanged = OnOutlineChanged;
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);

                mEditor.OnDestroy();

                var indicator = SLGMakerEditor.instance.brushIndicator;
                indicator.SetActive(false);
                indicator.SetTexture(null);
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                var layerData = mLogic.layer.GetLayerData();

                var operation = (CameraColliderEditorToolType)EditorGUILayout.EnumPopup(new GUIContent("Operation", "当前操作"), mLogic.operation);
                if (operation != mLogic.operation)
                {
                    mLogic.operation = operation;
                    mEditor.SetActiveTool(operation);
                    mLogic.layer.ShowOutline(PrefabOutlineType.NavMeshObstacle);
                }

                mEditor.DrawGUI();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button(new GUIContent("Create Top Outline", "创建顶层边框")))
                {
                    if (mEditor.selectedObjectID != 0)
                    {
                        var dlg = EditorUtils.PopupDialog<CameraColliderCreatorDialog>("Create Camera Collider");
                        dlg.Show(OnCreateCollider);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Select a collider first!", "OK");
                    }
                }
                if (GUILayout.Button(new GUIContent("Clear Top Outline", "清除顶层边框")))
                {
                    var collider = mEditor.layer.GetCameraCollider(mEditor.selectedObjectID);
                    if (collider != null && collider.topOutline != null)
                    {
                        var action = new ActionClearCameraColliderTopOutline(mEditor.layer.id, mEditor.selectedObjectID);
                        ActionManager.instance.PushAction(action);
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button(new GUIContent("Create Collider", "创建collider mesh")))
                {
                    var collider = mEditor.layer.GetCameraCollider(mEditor.selectedObjectID);
                    if (collider != null)
                    {
                        if (collider.topOutline != null)
                        {
                            var act = new ActionCreateColliderMesh(mEditor.layer.id, collider.id);
                            ActionManager.instance.PushAction(act);
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Create Top Outline first!", "OK");
                        }
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Select a collider first!", "OK");
                    }
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Camera Collider Count", mLogic.layer.objectCount.ToString());
                EditorGUILayout.LabelField("Width", mLogic.layer.GetTotalWidth().ToString());
                EditorGUILayout.LabelField("Height", mLogic.layer.GetTotalHeight().ToString());
                EditorGUILayout.EndVertical();
            }
        }

        bool OnCreateCollider(float height)
        {
            var act = new ActionCreateCameraColliderTopOutline(mEditor.layer.id, mEditor.selectedObjectID, height);
            ActionManager.instance.PushAction(act);
            return true;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            mEditor.Update(currentEvent);

            if (Event.current.button == 1 && Event.current.type == EventType.MouseDown)
            {
                var tool = mEditor.GetActiveTool();
                mEditor.DrawMenu();
            }

            mEditor.DrawScene();

            mLogic.layer.UpdateColor(mLogic.layer.displayType);

            DoRepaint();
        }

        void SetOutlineType(PrefabOutlineType type)
        {
            mLogic.layer.displayType = type;
            mLogic.layer.ShowOutline(type);
        }

        void OnOutlineChanged(int dataID, PrefabOutlineType type)
        {
            RepaintScene();
            mLogic.layer.ShowOutline(mLogic.layer.displayType);
        }

        void RepaintScene()
        {
            mSceneDirty = true;
        }

        void DoRepaint()
        {
            if (mSceneDirty)
            {
                mSceneDirty = false;
                SceneView.RepaintAll();
            }
        }

        CameraColliderLayerLogic mLogic;
        bool mSceneDirty = false;
        CameraColliderEditor mEditor;
    }
}

#endif