﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class CircleBorderLayerData : QuadTreeObjectLayerData
    {
        public CircleBorderLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, int combineBorderLOD) : base(header, config, map, null, null)
        {
            this.combineBorderLOD = combineBorderLOD;
        }

        public int combineBorderLOD { set; get; }
    }
}
