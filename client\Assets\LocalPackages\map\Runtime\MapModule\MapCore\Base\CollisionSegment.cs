﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    [System.Serializable]
    public class SegmentData
    {
        public SegmentData()
        {
        }

        public SegmentData(List<Vector3> vertices)
        {
            outline.AddRange(vertices);
        }

        public OutlineData Clone()
        {
            OutlineData d = new OutlineData(outline);
            return d;
        }

        public List<Vector3> GetCopy()
        {
            List<Vector3> copy = new List<Vector3>(outline.Count);
            copy.AddRange(outline);
            return copy;
        }

        public void Clear()
        {
            outline.Clear();
        }

        public Vector3 GetVertexPos(int index)
        {
            if (index >= 0 && index < outline.Count)
            {
                return outline[index];
            }
            return Vector3.zero;
        }

        public void SetVertexPos(int index, Vector3 pos)
        {
            if (index >= 0 && index < outline.Count)
            {
                outline[index] = pos;
            }
        }

        public int GetVertexIndex(Vector3 pos)
        {
            for (int i = 0; i < outline.Count; ++i)
            {
                if (pos == outline[i])
                {
                    return i;
                }
            }
            return -1;
        }

        //局部坐标系下的顶点
        public List<Vector3> outline = new List<Vector3>();
    }

    public class CollisionSegmentInfo
    {
        public CollisionSegmentInfo(int x, int y, CollisionSegment segment)
        {
            this.x = x;
            this.y = y;
            this.segment = segment;
        }

        public int x;
        public int y;
        public CollisionSegment segment;
    }

    //障碍物的边框
    [ExecuteInEditMode]
    [SelectionBaseAttribute]
    public class CollisionSegment : MonoBehaviour
    {
#if UNITY_EDITOR
        void OnEnable()
        {
            for (int i = 0; i < 2; ++i)
            {
                Reverse(i);
            }
        }
#endif

        public void Init()
        {
            currentOutlineType = PrefabOutlineType.NavMeshObstacle;
            for (int i = 0; i < 2; ++i)
            {
                if (mOutlineData[i] == null)
                {
                    mOutlineData[i] = new SegmentData();
                }
            }
        }

        public void Reverse(PrefabOutlineType outlineType)
        {
            Reverse((int)outlineType);
        }

        void Reverse(int idx)
        {
            if (mOutlineData[idx] != null && !Utils.IsPolygonCW(mOutlineData[idx].outline))
            {
                Utils.ReverseList(mOutlineData[idx].outline);
            }
        }

        //检测outline的vertex是否在有效的范围内
        public void IsInValidRange(float minX, float maxX, float minZ, float maxZ, out bool validNavMeshObstacle, out bool validObjectplacementObstacle)
        {
            validNavMeshObstacle = true;
            validObjectplacementObstacle = true;
            for (int i = 0; i < mOutlineData.Length; ++i)
            {
                if (mOutlineData[i] != null)
                {
                    int n = mOutlineData[i].outline.Count;
                    var worldSpaceOutlines = GetWorldSpaceOutlineVertices((PrefabOutlineType)i);
                    for (int j = 0; j < n; ++j)
                    {
                        var p = worldSpaceOutlines[j];
                        if (Utils.LTE(p.x, minX, 0.001f) || Utils.GTE(p.x, maxX, 0.001f) ||
                            Utils.LTE(p.z, minZ, 0.001f) || Utils.GTE(p.z, maxZ, 0.001f))
                        {
                            if (i == 0)
                            {
                                validNavMeshObstacle = false;
                            }
                            else if (i == 1)
                            {
                                validObjectplacementObstacle = false;
                            }
                            else
                            {
                                Debug.Assert(false, "Unknown obstacle type!");
                            }
                            break;
                        }
                    }
                }
            }
        }

        public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
        {
            return mOutlineData[(int)type].outline;
        }

        public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
        {
            var outlineVertices = GetOutlineVertices(type);
            int n = outlineVertices.Count;
            Vector3[] worldSpaceOutlines = new Vector3[n];
            for (int i = 0; i < n; ++i)
            {
                worldSpaceOutlines[i] = TransformToWorldPosition(outlineVertices[i]);
            }
            return worldSpaceOutlines;
        }

        public Vector3 TransformToWorldPosition(Vector3 localPoint)
        {
            var worldPos = transform.position;
            var worldScale = transform.lossyScale;
            var worldRotationEuler = transform.rotation.eulerAngles;
            var rotY = Quaternion.Euler(0, worldRotationEuler.y, 0);
            var p1 = new Vector3(worldScale.x * localPoint.x, 0, worldScale.z * localPoint.z);
            var pos = rotY * p1 + new Vector3(worldPos.x, 0, worldPos.z);
            pos.y = 0;
            return pos;
        }

        public Vector3 TransformToLocalPosition(Vector3 worldPos)
        {
            var objWorldPos = transform.position;
            var objWorldScale = transform.lossyScale;
            var worldRotationEuler = transform.rotation.eulerAngles;
            var rotY = Quaternion.Euler(0, worldRotationEuler.y, 0);
            var localPos = new Vector3(worldPos.x - objWorldPos.x, 0, worldPos.z - objWorldPos.z);
            var invRot = Quaternion.Inverse(rotY);
            localPos = invRot * localPos;
            localPos = new Vector3(localPos.x / objWorldScale.x, 0, localPos.z / objWorldScale.z);
            return localPos;
        }

        public bool IsExtendable()
        {
            return true;
        }

        public void ClampToBorder()
        {
            if (hasRange)
            {
                for (int idx = 0; idx < 2; ++idx)
                {
                    var expandedVertices = new List<Vector3>();
                    expandedVertices.AddRange(mOutlineData[idx].outline);
                    List<Vector3> finalVertices = new List<Vector3>();
                    for (int i = 0; i < expandedVertices.Count; ++i)
                    {
                        if (hasRange)
                        {
                            var pos = ClampPosition(expandedVertices[i]);
                            finalVertices.Add(pos);
                        }
                        else
                        {
                            finalVertices.Add(expandedVertices[i]);
                        }
                    }

                    mOutlineData[idx].outline = finalVertices;
                }
            }
        }

        public void Expand(PrefabOutlineType type, float radius)
        {
#if UNITY_EDITOR
            if (radius > 0)
            {
                int idx = (int)type;
                var expandedVertices = PolygonAlgorithm.ExpandPolygon(radius, mOutlineData[idx].outline)[0];
                List<Vector3> finalVertices = new List<Vector3>();
                for (int i = 0; i < expandedVertices.Count; ++i)
                {
                    if (hasRange)
                    {
                        var pos = ClampPosition(expandedVertices[i]);
                        finalVertices.Add(pos);
                    }
                    else
                    {
                        finalVertices.Add(expandedVertices[i]);
                    }
                }

                mOutlineData[idx].outline = finalVertices;
            }
#endif
        }

        //将顶点限制在某个范围内
        public Vector3 ClampPosition(Vector3 originalLocalPos)
        {
            var worldPos = transform.TransformPoint(originalLocalPos);

            Transform root = Utils.GetRootTransform(transform);
            var localPosInRoot = root.worldToLocalMatrix.MultiplyPoint(worldPos);

            if (hasRange)
            {
                localPosInRoot.x = Mathf.Clamp(localPosInRoot.x, minX, maxX);
                localPosInRoot.z = Mathf.Clamp(localPosInRoot.z, minZ, maxZ);
                worldPos = root.localToWorldMatrix.MultiplyPoint(localPosInRoot);
            }

            var localPos = transform.worldToLocalMatrix.MultiplyPoint(worldPos);

            return localPos;
        }

        //radius: 边框扩大一圈的半径
        public void CopyFromTo(PrefabOutlineType fromType, PrefabOutlineType toType, float radius)
        {
#if UNITY_EDITOR
            var fromList = mOutlineData[(int)fromType].outline;
            if (radius > 0)
            {
                fromList = PolygonAlgorithm.ExpandPolygon(radius, fromList)[0];
            }
            int toIndex = (int)toType;
            if (mOutlineData[toIndex] == null)
            {
                mOutlineData[toIndex] = new SegmentData();
            }
            mOutlineData[toIndex].outline = new List<Vector3>();

            for (int i = 0; i < fromList.Count; ++i)
            {
                if (hasRange)
                {
                    var pos = ClampPosition(fromList[i]);
                    mOutlineData[toIndex].outline.Add(pos);
                }
                else
                {
                    mOutlineData[toIndex].outline.Add(fromList[i]);
                }
            }
#endif
        }

        public void SetOutlineData(SegmentData data, PrefabOutlineType type)
        {
            mOutlineData[(int)type] = data;
        }

        public SegmentData GetOutlineData(PrefabOutlineType type)
        {
            return mOutlineData[(int)type];
        }

        public Vector3 offset { set; get; }
        public bool dirty { set { mDirty = value; } get { return mDirty; } }

        [SerializeField]
        [HideInInspector]
        SegmentData[] mOutlineData = new SegmentData[2];
        public PrefabOutlineType currentOutlineType { set; get; }
        public bool hasRange = true;
        public float minX = 0;
        public float maxX = 180;
        public float minZ = 0;
        public float maxZ = 180;
        bool mDirty = false;
    }
}