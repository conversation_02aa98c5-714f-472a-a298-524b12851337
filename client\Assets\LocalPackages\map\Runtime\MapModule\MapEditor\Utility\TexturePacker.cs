﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine.UI;

namespace TFW.Map
{
    public class TexturePackerItem
    {
        public TexturePackerItem(string uniqueID, Texture2D texture, bool treatAlphaAsZero)
        {
            this.uniqueID = uniqueID;
            this.texture = texture;
            this.treatAlphaAsZero = treatAlphaAsZero;
        }

        public string uniqueID;
        public Texture2D texture;
        public bool treatAlphaAsZero;
    }

    //将使用不同mask贴图的地表替换成同一个材质,将mask贴图打包成图集,从而可以batch渲染
    public class TexturePacker
    {
        public class PackInfo
        {
            public PackInfo(int startIdx, int count, Texture2D packedTexture, Vector2[][] uvs)
            {
                this.startIdx = startIdx;
                this.count = count;
                this.packedTexture = packedTexture;
                this.uvs = uvs;
            }

            public int startIdx { get; private set; }
            public int count { get; private set; }
            public Texture2D packedTexture { get; private set; }
            public Vector2[][] uvs { get; private set; }
        }

        public bool Pack(TexturePackerStrategyType type, System.Func<List<PackInfo>, List<TexturePackerItem>, bool> onPackTextureSucceed, List<Texture2D> textures, List<bool> treatAlphaAsZero = null, bool sortInput = false, int maxTextureSize = 2048, int border = 4, bool rgbTextureAlphaIsOne = true)
        {
            List<TexturePackerItem> items = new List<TexturePackerItem>();
            for (int i = 0; i < textures.Count; ++i)
            {
                string assetPath = AssetDatabase.GetAssetPath(textures[i]);
                if (string.IsNullOrEmpty(assetPath))
                {
                    Debug.LogError($"Pack texture failed! Invalid texture path for texture {i}");
                    return false;
                }
                items.Add(new TexturePackerItem(assetPath, textures[i], treatAlphaAsZero == null ? false : treatAlphaAsZero[i]));
            }

            return Pack(type, onPackTextureSucceed, items, sortInput, maxTextureSize, border, rgbTextureAlphaIsOne);
        }

        public bool Pack(TexturePackerStrategyType type, System.Func<List<PackInfo>, List<TexturePackerItem>, bool> onPackTextureSucceed, List<TexturePackerItem> textures, bool sortInput = false, int maxTextureSize = 2048, int border = 4, bool rgbTextureAlphaIsOne = true)
        {
            if (textures == null || textures.Count == 0)
            {
                return false;
            }

            mItems = textures;

            //为了绕开texture的readable设置,创建一些临时贴图
            List<TexturePackerItem> tempTextures = CreateTempTextures(textures);
            //将所有mask贴图打包成一张或多张图集
            List<PackInfo> packInfos = null;
            if (type == TexturePackerStrategyType.EqualRects)
            {
                packInfos = PackAllTexturesWithSameSize(tempTextures, border, sortInput, maxTextureSize, rgbTextureAlphaIsOne);
            }
            else
            {
                packInfos = PackAllTextures(type, tempTextures, border, sortInput, maxTextureSize, rgbTextureAlphaIsOne);
            }

            if (packInfos == null)
            {
                return false;
            }

            mPackInfo = packInfos;

            if (onPackTextureSucceed != null)
            {
                bool destroyPackTexture = onPackTextureSucceed(packInfos, textures);
                if (destroyPackTexture)
                {
                    for (int i = 0; i < packInfos.Count; ++i)
                    {
                        Object.DestroyImmediate(packInfos[i].packedTexture);
                    }
                }
            }

            //删除临时贴图
            for (int i = 0; i < tempTextures.Count; ++i)
            {
                Object.DestroyImmediate(tempTextures[i].texture);
            }

            AssetDatabase.Refresh();

            return true;
        }

        public Vector2[] GetUV(string uniqueID, out int textureAtlasIndex, out int indexInAtlas)
        {
            textureAtlasIndex = 0;
            int index = -1;
            indexInAtlas = -1;
            for (int i = 0; i < mItems.Count; ++i)
            {
                if (mItems[i].uniqueID == uniqueID)
                {
                    index = i;
                    break;
                }
            }

            if (index < 0)
            {
                Debug.LogError($"uniqueID {uniqueID} not found in atlas!");
                return null;
            }
            for (int i = 0; i < mPackInfo.Count; ++i)
            {
                if (index >= mPackInfo[i].startIdx && index < mPackInfo[i].startIdx + mPackInfo[i].count)
                {
                    textureAtlasIndex = i;
                    indexInAtlas = index - mPackInfo[i].startIdx;
                    var list = mPackInfo[i];
                    Vector2[] uvs = list.uvs[indexInAtlas];
                    return uvs;
                }
            }

            Debug.Assert(false);
            return null;
        }

        public int GetAtlasTextureIndex(string uniqueID, List<TexturePackerItem> items, List<PackInfo> textureAtlas)
        {
            int index = -1;
            for (int i = 0; i < items.Count; ++i)
            {
                if (items[i].uniqueID == uniqueID)
                {
                    index = i;
                    break;
                }
            }

            Debug.Assert(index >= 0);
            for (int i = 0; i < textureAtlas.Count; ++i)
            {
                if (index >= textureAtlas[i].startIdx && index < textureAtlas[i].startIdx + textureAtlas[i].count)
                {
                    return i;
                }
            }

            Debug.Assert(false);
            return -1;
        }

        List<TexturePackerItem> CreateTempTextures(List<TexturePackerItem> textures)
        {
            List<TexturePackerItem> tempTextures = new List<TexturePackerItem>(textures.Count);
            for (int i = 0; i < textures.Count; ++i)
            {
                var texPath = AssetDatabase.GetAssetPath(textures[i].texture);
                Debug.Assert(!string.IsNullOrEmpty(texPath));
                var tempTexture = EditorUtils.CreateTexture(texPath, false);
                tempTexture.name = textures[i].texture.name;
                tempTextures.Add(new TexturePackerItem(textures[i].uniqueID, tempTexture, textures[i].treatAlphaAsZero));
            }
            return tempTextures;
        }

        Texture2D ExpandTexture(Texture2D texture, int border)
        {
            Debug.Assert(texture.isReadable);
            int oldWidth = texture.width;
            int oldHeight = texture.height;
            int newWidth = texture.width + border * 2;
            int newHeight = texture.height + border * 2;
            var expandedTexture = new Texture2D(newWidth, newHeight, texture.format, false);
            Color[] oldTextureData = texture.GetPixels();
            Color[] expandedTextureData = new Color[newWidth * newHeight];
            if (border > 0)
            {
                //left top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = 0; x < border; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[0];
                    }
                }
                //right top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = oldWidth + border; x < newWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[oldWidth - 1];
                    }
                }
                //left bottom
                for (int y = border + oldHeight; y < newHeight; ++y)
                {
                    for (int x = 0; x < border; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        int srcIdx = (oldHeight - 1) * oldWidth;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }
                //right bottom
                for (int y = border + oldHeight; y < newHeight; ++y)
                {
                    for (int x = border + oldWidth; x < newWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        expandedTextureData[dstIdx] = oldTextureData[oldTextureData.Length - 1];
                    }
                }

                //region 0, top
                for (int y = 0; y < border; ++y)
                {
                    for (int x = border; x < border + oldWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = x - border;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 2, bottom
                for (int y = oldHeight + border; y < newHeight; ++y)
                {
                    for (int x = border; x < border + oldWidth; ++x)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (oldHeight - 1) * oldWidth + x - border;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 1, left
                for (int x = 0; x < border; ++x)
                {
                    for (int y = border; y < border + oldHeight; ++y)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (y - border) * oldWidth;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }

                //region 3, right
                for (int x = oldWidth + border; x < newWidth; ++x)
                {
                    for (int y = border; y < border + oldHeight; ++y)
                    {
                        int dstIdx = y * newWidth + x;
                        var srcIdx = (y - border) * oldWidth + oldWidth - 1;
                        expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                    }
                }
            }

            //middle
            for (int y = border; y < border + oldHeight; ++y)
            {
                for (int x = border; x < border + oldWidth; ++x)
                {
                    int dstIdx = y * newWidth + x;
                    var srcIdx = (y - border) * oldWidth + x - border;
                    expandedTextureData[dstIdx] = oldTextureData[srcIdx];
                }
            }

            expandedTexture.SetPixels(expandedTextureData);
            expandedTexture.Apply();
            return expandedTexture;
        }

        List<PackInfo> PackAllTextures(TexturePackerStrategyType type, List<TexturePackerItem> maskTextures, int border, bool sortInput, int maxTextureSize, bool rgbTextureAlphaIsOne)
        {
            MyTexturePacker myPacker = new MyTexturePacker(type);
            List<PackInfo> ret = new List<PackInfo>();
            int offset = 0;
            while (offset < maskTextures.Count)
            {
                int maxPackedCount = 0;
                //先测试最大能pack多少个texture
                for (int j = offset; j < maskTextures.Count; ++j)
                {
                    var triedTextures = new TexturePackerItem[j - offset + 1];
                    for (int k = 0; k < triedTextures.Length; ++k)
                    {
                        triedTextures[k] = maskTextures[offset + k];
                    }
                    TexturePackResult result1;
                    bool packSuccess = myPacker.CanPackInOneTexture(maxTextureSize, border, 0, triedTextures, sortInput, out result1);

                    if (!packSuccess)
                    {
                        maxPackedCount = triedTextures.Length - 1;
                        break;
                    }
                }
                if (maxPackedCount == 0)
                {
                    maxPackedCount = maskTextures.Count - offset;
                }

                if (maxPackedCount == 0)
                {
                    //不能pack,贴图超过2048了
                    return null;
                }

                var textures = new TexturePackerItem[maxPackedCount];
                for (int k = 0; k < textures.Length; ++k)
                {
                    textures[k] = maskTextures[k + offset];
                }

                Vector2[][] uvs = null;

                TexturePackResult result;
                bool suc = myPacker.Pack(maxTextureSize, border, 0, rgbTextureAlphaIsOne, textures, sortInput, out result);
                Debug.Assert(result.atlas.Count == 1);
                int entryCount = result.atlas[0].GetEntryCount();
                var packedMaskTexture = result.atlas[0].GetTexture();
                Rect[] rects = new Rect[entryCount];
                for (int k = 0; k < entryCount; ++k)
                {
                    var entry = result.atlas[0].GetEntry(k);
                    rects[k] = entry.uv;
                }
                uvs = new Vector2[rects.Length][];
                for (int i = 0; i < rects.Length; ++i)
                {
                    uvs[i] = new Vector2[4];
                    uvs[i][0] = new Vector2(rects[i].xMin, rects[i].yMin);
                    uvs[i][1] = new Vector2(rects[i].xMin, rects[i].yMax);
                    uvs[i][2] = new Vector2(rects[i].xMax, rects[i].yMax);
                    uvs[i][3] = new Vector2(rects[i].xMax, rects[i].yMin);
                }

                PackInfo pi = new PackInfo(offset, maxPackedCount, packedMaskTexture, uvs);
                ret.Add(pi);

                offset += maxPackedCount;
            }

            return ret;
        }

        List<PackInfo> PackAllTexturesWithSameSize(List<TexturePackerItem> maskTextures, int border, bool sortInput, int maxTextureSize, bool rgbTextureAlphaIsOne)
        { 
            MyTexturePacker myPacker = new MyTexturePacker(TexturePackerStrategyType.EqualRects);
            List<PackInfo> ret = new List<PackInfo>();
            int offset = 0;
            while (offset < maskTextures.Count)
            {
                int maxPackedCount = myPacker.GetMaxPackCountInOneTexture(maskTextures[0].texture.width, border, 0, maxTextureSize);
                maxPackedCount = Mathf.Clamp(maxPackedCount, 0, maskTextures.Count - offset);

                var textures = new TexturePackerItem[maxPackedCount];
                for (int k = 0; k < textures.Length; ++k)
                {
                    textures[k] = maskTextures[k + offset];
                }

                Vector2[][] uvs = null;

                TexturePackResult result;
                bool suc = myPacker.Pack(maxTextureSize, border, 0, rgbTextureAlphaIsOne, textures, sortInput, out result);
                Debug.Assert(result.atlas.Count == 1);
                int entryCount = result.atlas[0].GetEntryCount();
                var packedMaskTexture = result.atlas[0].GetTexture();
                Rect[] rects = new Rect[entryCount];
                for (int k = 0; k < entryCount; ++k)
                {
                    var entry = result.atlas[0].GetEntry(k);
                    rects[k] = entry.uv;
                }
                uvs = new Vector2[rects.Length][];
                for (int i = 0; i < rects.Length; ++i)
                {
                    uvs[i] = new Vector2[4];
                    uvs[i][0] = new Vector2(rects[i].xMin, rects[i].yMin);
                    uvs[i][1] = new Vector2(rects[i].xMin, rects[i].yMax);
                    uvs[i][2] = new Vector2(rects[i].xMax, rects[i].yMax);
                    uvs[i][3] = new Vector2(rects[i].xMax, rects[i].yMin);
                }

                PackInfo pi = new PackInfo(offset, maxPackedCount, packedMaskTexture, uvs);
                ret.Add(pi);

                offset += maxPackedCount;
            }

            return ret;
        }

        List<TexturePackerItem> mItems;
        List<PackInfo> mPackInfo;
    }
}

#endif