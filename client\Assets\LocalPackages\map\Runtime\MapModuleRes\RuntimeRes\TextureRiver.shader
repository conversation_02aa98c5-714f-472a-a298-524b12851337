﻿Shader "SLGMaker/TextureRiver"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		//不能改变_Alpha这个名字!
		_Alpha("Alpha", 2D) = "white" {}
	}
		SubShader
		{
			Tags { "RenderType" = "Transparent" "Queue" = "Transparent"}
			LOD 100

			Pass
			{
				ZWrite Off
				Blend SrcAlpha OneMinusSrcAlpha

				CGPROGRAM
	#pragma vertex vert
	#pragma fragment frag

	# include "UnityCG.cginc"

				struct appdata
	{
		float4 vertex : POSITION;
					float2 uv : TEXCOORD0;
				};

	struct v2f
	{
		float2 uv : TEXCOORD0;
					float4 vertex : SV_POSITION;
				};

	sampler2D _MainTex;
	sampler2D _Alpha;
	float4 _MainTex_ST;

	v2f vert(appdata v)
	{
		v2f o;
		o.vertex = UnityObjectToClipPos(v.vertex);
		o.uv = TRANSFORM_TEX(v.uv, _MainTex);
		return o;
	}

	fixed4 frag(v2f i) : SV_Target
				{
		fixed4 col = tex2D(_MainTex, i.uv);
	fixed4 alpha = tex2D(_Alpha, i.uv);
	col.a = alpha.a;
	return col;
	}
	ENDCG
	}
		}
}
