using cspb;
using UnityEngine;

namespace UI
{
    public class TroopState: MonoBehaviour
    {
        public GameObject StatusGather,
            StatusPlunder,
            StatusFight,
            StatusReturn,
            StatusRun,
            StatusCamp,
            StatusRallyFight,
            StatusRallyGather,
            StatusRallyReinforce;


        public void Hide()
        {
            gameObject.SetActive(false);
        }

        public void Show(bool show,LineupState state)
        {
            HideAll();
            gameObject.SetActive(show);
            if (!show)
                return;
            
            switch (state)
            {
                case LineupState.LineupStateErr:
                    Hide();
                    break;
                case LineupState.LineupStateDefender:
                    Hide();
                    break;
                case LineupState.LineupStateOutCity:
                    StatusRun.SetActive(true);
                    break;
                case LineupState.LineupStateCamp:
                    StatusCamp.SetActive(true);
                    break;
                case LineupState.LineupStateRally:
                    StatusPlunder.SetActive(true);
                    break;
                case LineupState.LineupStateReinforce:
                    StatusRallyReinforce.SetActive(true);
                    break;
                case LineupState.LineupStateGathering:
                    StatusGather.SetActive(true);
                    break;
            }
        }

        private void SetData()
        {
            
        }

        void HideAll()
        {
            StatusGather.SetActive(false);
            StatusPlunder.SetActive(false);
            StatusFight.SetActive(false);
            StatusReturn.SetActive(false);
            StatusRun.SetActive(false);
            StatusCamp.SetActive(false);
            StatusRallyFight.SetActive(false);
            StatusRallyGather.SetActive(false);
            StatusRallyReinforce.SetActive(false);
        }
    }
}