﻿using Cysharp.Threading.Tasks;
using DeepUI;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace UI
{
    public class PopupPreloadUI:MonoBehaviour
    { 
#if UNITY_EDITOR
        [ValueDropdown(nameof(_availablePopupTypes))]
#endif
        [SerializeField]
        string[] _preloadPopupNames;


        private bool _preloaded = false;
        private bool _preloadPending = false;

        static IEnumerable<Type> GetAllTypesDerivedFrom(Type baseType)
        {
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                foreach (var type in assembly.GetTypes().Where(t => t.IsSubclassOf(baseType)))
                {
                    yield return type;
                }
            }
        }

        private async UniTask PreloadAsync()
        {
            if (this._preloadPending)
                return;

            this._preloadPending = true;

            if (!this._preloaded)
            {

                foreach (var popupName in _preloadPopupNames)
                {
                    var t = Type.GetType(popupName);
                    await PopupManager.I.PreloadPopupAsync(t);
                }

                this._preloaded=true;
            }

            this._preloadPending = false;
        }

        private void Awake()
        {
            this._preloaded = false;
            this._preloadPending = false;
        }

        private void OnEnable()
        {
            this.PreloadAsync().Forget();
        }

#if UNITY_EDITOR
        private IEnumerable _availablePopupTypes
        {
            get
            {
                var ret = new ValueDropdownList<string>();
                foreach (var popupType in GetAllTypesDerivedFrom(typeof(BasePopup)))
                {
                    ret.Add(popupType.FullName, popupType.AssemblyQualifiedName);
                }
                return ret;
            }
        }
#endif
    }
}
