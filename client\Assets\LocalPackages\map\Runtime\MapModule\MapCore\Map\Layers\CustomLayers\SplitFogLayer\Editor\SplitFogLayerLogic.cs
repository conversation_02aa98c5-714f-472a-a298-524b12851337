﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public enum SplitFogOperationType
    {
        Create,
        Remove,
        Select,
    }

    [ExecuteInEditMode]
    public class SplitFogLayerLogic : MapLayerLogic
    {
        protected override MoveAxis moveAxis { get { return MoveAxis.All; } }

        void Start()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                prefabManager.addPrefabEvent += OnAddPrefab;
                prefabManager.setPrefabEvent += OnSetPrefab;
                prefabManager.removePrefabEvent += OnRemovePrefab;
                prefabManager.setPrefabFixedIndexEvent += OnSetPrefabFixedIndex;
                prefabManager.setPrefabSubGroupPrefabFixedIndexEvent += OnSetPrefabSubGroupFixedPrefabIndex;
                prefabManager.changeSubGroupPrefabCountEvent += OnChangeSubGroupPrefabCount;
                prefabManager.setSubGroupPrefabEvent += OnEditSubGroupPrefab;
            }
        }

        void OnDestroy()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                prefabManager.addPrefabEvent -= OnAddPrefab;
                prefabManager.setPrefabEvent -= OnSetPrefab;
                prefabManager.removePrefabEvent -= OnRemovePrefab;
                prefabManager.setPrefabFixedIndexEvent -= OnSetPrefabFixedIndex;
                prefabManager.setPrefabSubGroupPrefabFixedIndexEvent -= OnSetPrefabSubGroupFixedPrefabIndex;
                prefabManager.setSubGroupPrefabEvent -= OnEditSubGroupPrefab;
                prefabManager.changeSubGroupPrefabCountEvent -= OnChangeSubGroupPrefabCount;
            }
        }

        public SplitFogLayerData layerData
        {
            get
            {
                return Map.currentMap.FindObject(layerID) as SplitFogLayerData;
            }
        }

        public SplitFogLayer layer
        {
            get
            {
                return Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as SplitFogLayer;
            }
        }

        void OnSetPrefab(int groupIndex, int index, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetGroupPrefabByID(group.groupID, index, 0, prefabPath);
        }

        void OnChangeSubGroupPrefabCount(int groupIndex, int prefabIndex, int count)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetSubGroupPrefabCountByID(group.groupID, prefabIndex, count);
        }

        void OnEditSubGroupPrefab(int groupIndex, int index, int subTypeIndex, string prefabPath)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetGroupPrefabByID(group.groupID, index, subTypeIndex, prefabPath);
        }

        void OnAddPrefab(int groupIndex, GameObject prefab)
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var idx = group.count - 1;
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetGroupPrefabByID(group.groupID, idx, 0, prefabPath);

            layerData.SetPrefabPath(idx, prefabPath);
        }

        void OnRemovePrefab(int groupIndex, int index, string prefabPath)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetGroupPrefabByID(group.groupID, index, 0, "");
        }

        //设置group使用的prefab
        void OnSetPrefabFixedIndex(int groupIndex, int index)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetPrefabFixedIndexByID(group.groupID, index);
        }

        void OnSetPrefabSubGroupFixedPrefabIndex(int groupIndex, int prefabIndex, int subgroupPrefabIndex)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            var group = prefabManager.GetGroupByIndex(groupIndex);
            var splitFogPrefabManager = Map.currentMap.data.splitFogPrefabManager;
            splitFogPrefabManager.SetPrefabSubGroupPrefabFixedIndexByID(group.groupID, prefabIndex, subgroupPrefabIndex);
        }

        public SplitFogOperationType operationType { set; get; }
    }
}


#endif