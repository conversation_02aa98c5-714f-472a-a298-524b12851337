﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Net.Common
{
    class CPack : IPack
    {
        void IPack.Encode(byte[] arr, Stream output, int offset, int len, AesEncryptor encryptor, int sidx, byte cccflag)
        {
            byte flag = (byte)(sidx & 0x1F);
            int crc32 = 0;
            cccflag = NetHelper.CCC_Crypto;
            if (((cccflag & NetHelper.CCC_Compress) == NetHelper.CCC_Compress))
            {
                if (len >= 1024)
                {
                    arr = ZLib.Zip(arr, offset, len);
                    offset = 0; len = arr.Length;
                    flag |= NetHelper.CCC_Compress;
                }
            }
            if (((cccflag & NetHelper.CCC_Crypto) == NetHelper.CCC_Crypto))
            {
                bool aesed = (new Random().Next() % 10) < 3;
                if (aesed)
                {
                    arr = encryptor.Encrypt(arr, offset, len);
                    offset = 0; len = arr.Length;
                    flag |= NetHelper.CCC_Crypto;
                }
            }
            if (((cccflag & NetHelper.CCC_Crc) == NetHelper.CCC_Crc))
            {
                bool crced = (new Random().Next() % 10) < 3;
                if (crced)
                {
                    crc32 = Crc.Crc32(arr, offset, len);
                    flag |= NetHelper.CCC_Crc;
                }
            }

            int alllen = len + 1 + 4;
            var balllen = NetHelper.ToBytes(alllen);
            var m2 = NetHelper.ToBytes(crc32);

            output.Write(balllen, 0, NetHelper.PackHeadSize); //allLen
            output.WriteByte(flag);//flag
            output.Write(m2, 0, 4);//crc
            output.Write(arr, offset, len);
        }

        void IPack.Decode(byte[] buffer, Stream output, int offset, int size, AesDecryptor decryptor, int ridx)
        {
            byte flag = buffer[offset];
            bool ziped = ((flag & 0x80) == 0x80);
            bool aesed = ((flag & 0x40) == 0x40);
            bool crced = ((flag & 0x20) == 0x20);
            int idx = flag & 0x1F;
            if (ridx == idx)
            {
                int crc32 = NetHelper.ToInt32(buffer, offset + 1);
                int ncrc32 = 0;
                if (crced)
                {
                    ncrc32 = Crc.Crc32(buffer, offset + 1 + 4, size - 1 - 4);
                }
                if (ncrc32 == crc32)
                {
                    byte[] data;
                    if (aesed && ziped)
                    {
                        data = decryptor.Decrypt(buffer, offset + 1 + 4, size - 1 - 4);
                        data = ZLib.UnZip(data);
                    }
                    else if (aesed)
                    {
                        data = decryptor.Decrypt(buffer, offset + 1 + 4, size - 1 - 4);
                    }
                    else if (ziped)
                    {
                        data = ZLib.UnZip(buffer, offset + 1 + 4, size - 1 - 4);
                    }
                    else
                    {
                        data = new byte[size - 1 - 4];
                        Buffer.BlockCopy(buffer, offset + 1 + 4, data, 0, data.Length);
                    }

                    if (data != null)
                    {
                        output.Write(data, 0, data.Length);
                    }
                    else
                    {
                        throw new Exception("Recv Decode data null");
                    }
                }
                else
                {
                    throw new Exception("Recv error crc32 " + crc32 + "   ncrc32" + ncrc32);
                }
            }
            else
            {
                throw new Exception("Recv error idx " + idx + "   lidx" + ridx);
            }
        }
    }
}
#endif