﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class PolygonRiverLayer : MapLayerBase
    {
        public void ImportMaskTextureDataFromExportedAssets()
        {
            string riverAssetFolderPath = MapCoreDef.GetRiverAssetsFolderPath(SLGMakerEditor.instance.exportFolder);
            string manifestFilePath = MapCoreDef.GetRiverPrefabManifestFilePath(riverAssetFolderPath);
            var stream = MapModuleResourceMgr.LoadTextStream(manifestFilePath, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                int riverPrefabCount = reader.ReadInt32();
                for (int i = 0; i < riverPrefabCount; ++i)
                {
                    int riverID = reader.ReadInt32();
                    int sectionIndex = reader.ReadInt32();
                    string sectionPrefabPath = Utils.ReadString(reader);

                    Texture2D texture = GetPrefabRiverMaskTexture(sectionPrefabPath);
                    if (texture != null) 
                    {
                        if (texture.isReadable)
                        {
                            mLayerData.SetRiverSectionTextureData(riverID, sectionIndex, texture);
                            mLayerView.UpdateTexture(riverID);
                        }
                        else
                        {
                            Debug.LogError($"Can't import texture because {texture.name} is not readable");
                        }
                    }
                    else
                    {
                        Debug.LogError($"Texture of Prefab {sectionPrefabPath} not found!");
                    }
                }

                reader.Close();
            }
        }

        Texture2D GetPrefabRiverMaskTexture(string prefabPath)
        {
            var prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);
            if (prefab != null)
            {
                var renderer = prefab.GetComponent<MeshRenderer>();
                if (renderer != null)
                {
                    var mtl = renderer.sharedMaterial;
                    if (mtl != null)
                    {
                        return mtl.GetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME) as Texture2D;
                    }
                }
            }
            return null;
        }
    }
}


#endif