﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        enum RectangleFillCursorState
        {
            Idle,
            SelectMin,
        }

        void DrawRectangleFillToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.alt)
            {
                return;
            }

            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mRectangleFillState == RectangleFillCursorState.Idle)
                {
                    mRectangleFillState = RectangleFillCursorState.SelectMin;
                    mMinCursorPos = worldPos;
                    mMaxCursorPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseDrag && e.button == 0)
            {
                if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                {
                    mMaxCursorPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseUp && e.button == 0)
            {
                if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                {
                    if (e.control)
                    {
                        RemoveObjectInRectangle();
                    }
                    else
                    {
                        FillObject();
                    }
                    mRectangleFillState = RectangleFillCursorState.Idle;
                }
            }

            if (mRectangleFillState != RectangleFillCursorState.Idle)
            {
                Vector3 min = Vector3.Min(mMinCursorPos, mMaxCursorPos);
                Vector3 max = Vector3.Max(mMinCursorPos, mMaxCursorPos);
                Handles.DrawWireCube((min + max) * 0.5f, max - min);
            }
        }

        void RemoveObjectInRectangle()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            var rootTransform = stage.prefabContentsRoot.transform;
            int n = rootTransform.childCount;
            Vector3 min = Vector3.Min(mMinCursorPos, mMaxCursorPos);
            Vector3 max = Vector3.Max(mMinCursorPos, mMaxCursorPos);
            if (mRemoveObjectOfSeletedPrefab)
            {
                var selectedPrefab = mPrefabManager.selectedPrefab;
                for (int i = n - 1; i >= 0; --i)
                {
                    var childTransform = rootTransform.GetChild(i);
                    var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childTransform.gameObject);
                    if (selectedPrefab == childPrefab)
                    {
                        var pos = childTransform.position;
                        if (pos.x >= min.x && pos.x <= max.x &&
                            pos.z >= min.z && pos.z <= max.z)
                        {
                            Undo.DestroyObjectImmediate(childTransform.gameObject);
                        }
                    }
                }
            }
            else
            {
                for (int i = n - 1; i >= 0; --i)
                {
                    var childTransform = rootTransform.GetChild(i);
                    var pos = childTransform.position;
                    if (pos.x >= min.x && pos.x <= max.x &&
                        pos.z >= min.z && pos.z <= max.z)
                    {
                        Undo.DestroyObjectImmediate(childTransform.gameObject);
                    }
                }
            }
        }

        void FillObject()
        {
            if (mPrefabManager.selectedPrefab != null)
            {
                var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
                Vector3 min = Vector3.Min(mMinCursorPos, mMaxCursorPos);
                Vector3 max = Vector3.Max(mMinCursorPos, mMaxCursorPos);

                List<Vector3> positions = null;
                if (mFillEdge)
                {
                    positions = GenerateRectEdgePoints(min, max);
                }
                else
                {
                    positions = CalculateObjectPositions(mFillCount, min, max);
                }
                for (int i = 0; i < positions.Count; ++i)
                {
                    var prefab = mPrefabManager.selectedPrefab;
                    CreateObject(positions[i], prefab, stage.prefabContentsRoot.transform);
                }
            }
        }

        void RegenerateObjectsInRectangle()
        {
            Undo.PerformUndo();
            FillObject();
        }

        bool IsValid(Vector3 candidate, List<Vector3> validPositions, float minDistance)
        {
            minDistance *= minDistance;
            int n = validPositions.Count;
            for (int i = 0; i < n; ++i)
            {
                if ((candidate-validPositions[i]).sqrMagnitude <= minDistance)
                {
                    return false;
                }
            }
            return true;
        }

        bool IsValid(Vector3 candidate, List<Vector2> validPositions, float minDistance)
        {
            Vector2 candidate2 = Utils.ToVector2(candidate);
            minDistance *= minDistance;
            int n = validPositions.Count;
            for (int i = 0; i < n; ++i)
            {
                if ((candidate2 - validPositions[i]).sqrMagnitude <= minDistance)
                {
                    return false;
                }
            }
            return true;
        }

        List<Vector3> CalculateObjectPositions(int n, Vector3 min, Vector3 max)
        {
            List<Vector3> validPositions = new List<Vector3>();
            int tryCount = 500;
            Vector3 candidate = Vector3.zero;
            for (int i = 0; i < n; ++i)
            {
                for (int k = 0; k < tryCount; ++k)
                {
                    float x = UnityEngine.Random.Range(min.x, max.x);
                    float z = UnityEngine.Random.Range(min.z, max.z);
                    candidate = new Vector3(x, 0, z);
                    
                    if (IsValid(candidate, validPositions, mMinDistance))
                    {
                        validPositions.Add(candidate);
                        break;
                    }
                }
            }

            return validPositions;
        }

        float[] CalculateRectangleChanceTable(List<Vector3> rectVertices)
        {
            float[] chanceTable = new float[rectVertices.Count];
            float totalLength = 0;
            for (int i = 0; i < chanceTable.Length; ++i)
            {
                var dir = rectVertices[(i + 1) % rectVertices.Count] - rectVertices[i];
                totalLength += dir.magnitude;
                chanceTable[i] = totalLength;
            }

            for (int i = 0; i < chanceTable.Length; ++i)
            {
                chanceTable[i] /= totalLength;
            }
            return chanceTable;
        }

        List<Vector3> GenerateRectEdgePoints(Vector3 min, Vector3 max)
        {
            int tryCount = 500;
            List<Vector3> points = new List<Vector3>();

            if (min != max)
            {
                List<Vector3> rectVertices = new List<Vector3>()
                {
                    min,
                    new Vector3(min.x, 0, max.z),
                    max,
                    new Vector3(max.x, 0, min.z),
                };

                var chanceTable = CalculateRectangleChanceTable(rectVertices);

                for (int i = 0; i < mFillCount; ++i)
                {
                    for (int t = 0; t < tryCount; ++t)
                    {
                        float chance = Random.Range(0.0f, 1.0f);
                        int vertexIndex = -1;
                        for (int c = 0; c < chanceTable.Length; ++c)
                        {
                            if (chance <= chanceTable[c])
                            {
                                vertexIndex = c;
                                break;
                            }
                        }
                        Debug.Assert(vertexIndex >= 0);

                        var dir = rectVertices[(vertexIndex + 1) % rectVertices.Count] - rectVertices[vertexIndex];
                        float distance = UnityEngine.Random.Range(0.0f, 1.0f);
                        var point = rectVertices[vertexIndex] + distance * dir;
                        dir.Normalize();
                        var perp = new Vector3(dir.z, 0, -dir.x);
                        float halfEdgeSize = mEdgeSize * 0.5f;
                        float perpDistance = UnityEngine.Random.Range(-halfEdgeSize, halfEdgeSize);
                        point += perp * perpDistance;

                        if (IsValid(point, points, 0))
                        {
                            points.Add(point);
                            break;
                        }
                    }
                }
            }

            return points;
        }

        RectangleFillCursorState mRectangleFillState;
        Vector3 mMinCursorPos;
        Vector3 mMaxCursorPos;
        //每平方米放多少个物体
        int mFillCount = 1;
        float mMinDistance;
    }
}
#endif