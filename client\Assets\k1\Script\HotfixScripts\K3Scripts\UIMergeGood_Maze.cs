﻿using Cfg;
using DeepUI;
using DG.Tweening;
using Game.Data;
using Logic;
using Render;
using Spine.Unity;
using System;
using System.Collections;
using System.Collections.Generic;
using K3.Scripts;
using TFW;
using TFW.UI;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Sequence = DG.Tweening.Sequence;
using Common;

namespace K3
{
    [Serializable]
    public class UIMergeGoodData_Maze
    {
        public int id;
        /// <summary>
        /// 0 是道具 1 是英雄
        /// </summary>
        public int goodType;
        public bool locked;
        public long creatTamp;
        public bool max;
        public long boxCdTamp;
        /// <summary>
        /// 使用次数 或者 英雄能量值
        /// </summary>
        public int BoxTimes;
        public bool Immovable;
        /// <summary>
        /// 来源与Buff的BoxTimes
        /// </summary>
        public int BoxTimes_Buff;

        public UIMergeGoodData_Maze()
        {
        }

        public UIMergeGoodData_Maze(ItemInfo info)
        {
            id = info.id;
            locked = false;
            goodType = info.Type < 0 ? 1 : 0;
            Immovable = info.Immovable;
        }


        public UIMergeGoodData_Maze(ItemInfo info, bool locked, long boxCdTamp, ItemInfoBoxData infoBoxData, int BoxTimes, int boxtimes_buff)
        {
            id = info.id;
            this.locked = locked;
            goodType = info.Type < 0 ? 1 : 0;
            this.boxCdTamp = boxCdTamp;
            if (BoxTimes == 0 && boxCdTamp == 0 && infoBoxData != null)
            {
                this.BoxTimes = infoBoxData.BoxTimes;
            }
            else
            {
                this.BoxTimes = BoxTimes;
            }
            this.BoxTimes_Buff = boxtimes_buff;
            Immovable = info.Immovable;
        }

        public void LevelUp(ItemInfo info)
        {
            id = info.id;
            if (info.ItemBox?.BoxID > 0)
            {
                BoxTimes = info.ItemBox.BoxTimes;
            }

            Immovable = false;
        }

        public void UnLocked()
        {
            locked = false;
        }

        public void RestBox(ItemInfo info)
        {
            if (BoxTimes <= 0)
            {
                BoxTimes = info.ItemBox.BoxTimes;
            }
        }



        //public void GetBuffTime(out double totalSec, out double lastTime)
        //{
        //    totalSec = 0;
        //    lastTime = 0;

        //    if (goodType == 1)
        //    {
        //        var skillCfg = Cfg.C.CD2Skill.I(Cfg.C.CD2Hero.I(id)?.ActiveSkill ?? 0);
        //        if (skillCfg != null)
        //        {

        //            foreach (var item in skillCfg.Effect3)
        //            {
        //                if (item.C > 0)
        //                {
        //                    lastTime = item.C - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.C;

        //                    return;
        //                }
        //            }



        //            foreach (var item in skillCfg.Effect5)
        //            {
        //                if (item.C > 0)
        //                {
        //                    lastTime = item.C - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.C;

        //                    return;
        //                }
        //            }

        //            foreach (var item in skillCfg.Effect6)
        //            {
        //                if (item.E > 0)
        //                {
        //                    lastTime = item.E - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.E;

        //                    return;
        //                }
        //            }

        //        }
        //    }
        //}
    }


    public class UIMergeGood_Maze : MonoBehaviour, IBeginDragHandler, IDragHandler, IEndDragHandler, ICanvasRaycastFilter,
        IPointerClickHandler
    {
        public UIMergeGoodData_Maze mData;
        public UIMergeGrid_Maze CurGrid
        {
            get
            {
                uIMerge.mGridDic.TryGetValue(mPoint.ServerIndex, out var curGrid);
                return curGrid;
            }
        }

        private Transform MoveGoodRoot;
        private bool isRaycastLocationValid = true; //默认射线不能穿透物品
        private Camera canvasCamera;
        public ItemInfo Info;
        public Point mPoint;
        public TFWImage icon, icon_collect, goodImage, icon_Immovable;
        private UIMerge_Maze uIMerge;
        private Sequence DoTweenSeq;
        private Animator ani;
        private TFWImage Raycastimage;
        private UIMergeGood_Maze curTouchGood;
        private GameObject lockedObj;
        private GameObject boxEffect, type_scene, type_city, type_task, type_max, type_collect, typeRoot, type_bg, hero_quality, cityBg;
        public bool locked;
        private TimeSpan disposInterval;
        private TimeSpan curDur;
        private SkeletonGraphic spineAni_unlock;
        private TFWImage boxCdPointer;
        private GameObject itemboxCDObj;
        private GameObject maxEnery;

        private bool inited = false;
        private bool canClick;

        public bool CanClick { get { return canClick; } }

        public Vector2 startPos, EndPos, dir_drag;
        private int collectpointCount = 32;
        private float radius = 30f;
        private float radius_min = 30f;
        private float radius_max = 80f;
        private float dir_rang;
        private float dir_rang_max = 30f;
        private float dir_rang_min = 10f;
        private float maxTimer = 1500; //毫秒
        public EventSystem _mEventSystem;
        public GraphicRaycaster gra;
        private DateTime starttime;
        private double dur;
        double dur_max = 0.15f;//--秒
        private Vector3 sellPos;
        private List<Vector2> pointList;
        private List<Vector2> curPointList = new List<Vector2>();
        private Dictionary<UIMergeGood_Maze, int> dictionary_good = new Dictionary<UIMergeGood_Maze, int>();
        private Dictionary<UIMergeGrid_Maze, int> dictionary_grid = new Dictionary<UIMergeGrid_Maze, int>();
        private UIGrid heroTypeGrid;
        private TFWSlider heroEnergySlider;
        private ParticleSystem heroEnergyParticleSystem;

        private GameObject heroBuffObj;
        private TFWSlider heroBuffSlider;
        private TFWImage heroBuffIcon;

        private TFWText buffAddNumText;

        private GameObject mGa;

        public void Init(UIMergeGrid_Maze grid, ItemInfo info, Transform MoveGoodRoot, UIMerge_Maze merge,
            bool locked = false, long creatTamp = 0, bool showTween = true, float delayTime = 0.2f, long boxDdTamp = 0, int boxtims = 0, int boxtimes_buff = 0)
        {
            uIMerge = merge;
            mGa = gameObject;
            this.Info = info;
            canClick = false;
            mData = new UIMergeGoodData_Maze(info, locked, boxDdTamp, info.ItemBox, boxtims, boxtimes_buff);
            this.locked = locked;
            this.MoveGoodRoot = MoveGoodRoot;

            GoodInGrid(grid.Point);

            if (!inited)
            {
                dir_rang_max = dir_rang_max * ((float)Screen.width / 1080f);
                dir_rang_min = dir_rang_min * ((float)Screen.width / 1080f);
                radius_min = radius_min * ((float)Screen.width / 1080f);
                radius_max = radius_max * ((float)Screen.width / 1080f);
                radius = radius * ((float)Screen.width / 1080f);
                pointList = GetPoint(radius, collectpointCount);

                _mEventSystem = GameObject.FindObjectOfType<EventSystem>();
                gra = merge.canvas.GetComponent<GraphicRaycaster>();
                ani = transform.GetComponent<Animator>();
                Raycastimage = transform.GetComponent<TFWImage>();
                canvasCamera = GameObject.Find("UICamera").GetComponent<Camera>();
                icon = transform.Find("icon").GetComponent<TFWImage>();

                icon_Immovable = transform.Find("icon/Immovable").GetComponent<TFWImage>();
                goodImage = transform.GetComponent<TFWImage>();
                icon_collect = transform.Find("icon/collect").GetComponent<TFWImage>();
                spineAni_unlock = transform.Find("locked/spine_qipao").GetComponent<SkeletonGraphic>();
                lockedObj = transform.Find("locked").gameObject;
                boxEffect = transform.Find("tishi").gameObject;
                type_scene = transform.Find("typeRoot/sceneIcon").gameObject;
                heroTypeGrid = transform.Find("heroTypeGrid").GetComponent<UIGrid>();
                heroEnergySlider = transform.Find("heroEnergy").GetComponent<TFWSlider>();
                heroEnergyParticleSystem = transform.Find("heroEnergy/effect").GetComponent<ParticleSystem>();
                maxEnery = transform.Find("heroEnergy/maxEnery").gameObject;
                type_task = transform.Find("typeRoot/taskIcon").gameObject;
                type_max = transform.Find("typeRoot/maxIcon").gameObject;
                type_collect = transform.Find("typeRoot/collectIcon").gameObject;
                typeRoot = transform.Find("typeRoot").gameObject;
                type_bg = transform.Find("bg").gameObject;
                hero_quality = transform.Find("heroBg").gameObject;
                boxCdPointer = transform.Find("typeRoot/collectionCD/BOXCD/CDpointer").GetComponent<TFWImage>();
                itemboxCDObj = transform.Find("typeRoot/collectionCD").gameObject;

                heroBuffObj = transform.Find("buffIcon").gameObject;
                heroBuffIcon = transform.Find("buffIcon/icon").GetComponent<TFWImage>();
                heroBuffSlider = transform.Find("buffIcon/slider").GetComponent<TFWSlider>();

                buffAddNumText = transform.Find("buffAddNum").GetComponent<TFWText>();
            }

            UITools.SetCommonItemIcon(icon, info.Icon, false);
            UITools.SetCommonItemIcon(icon_Immovable, info.Icon, false);
            UITools.SetCommonItemIcon(icon_collect, info.Icon, false);

            if (info.Type < 0)
            {


            }
            else
            {
                heroTypeGrid.Clear();
                hero_quality.SetActive(false);

                heroBuffObj.SetActive(false);
                ShowHeroEffect(false);
                heroEnergySlider.gameObject.SetActive(false);

                icon.rectTransform.sizeDelta = new Vector2(146, 146);
            }

            ShowItemBox();

            var icon_light = transform.Find("icon_light");//补丁 ~
            if (icon_light != null)
            {
                Destroy(icon_light.gameObject);
            }

            typeRoot.SetActive(false);
            lockedObj.SetActive(locked);
            ani.SetBool("locked", locked);

            if (spineAni_unlock != null && spineAni_unlock.AnimationState != null && spineAni_unlock.SkeletonData.Animations.Items.Length > 1)
            {
                spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[1],
                    true);
            }

            if (locked)
            {
                disposInterval = new TimeSpan(0, 0, CSVConfig.GetItemRetentionTime());
                mData.creatTamp = creatTamp;
                if (mData.creatTamp == 0)
                {
                    mData.creatTamp = K3PlayerMgr.I.GetLocalTimestamp();
                }
            }
            else if (mData.Immovable)
            {

            }

            goodImage.raycastTarget = false;

            if (showTween)
            {
                canClick = false;
                StartCoroutine(ShowTween(delayTime));
            }
            else
            {
                transform.SetParent(CurGrid.goodRoot);
                transform.localPosition = Vector3.zero;
                transform.localScale = Vector3.one;
                canClick = true;
                goodImage.raycastTarget = true;
                typeRoot.SetActive(true);
            }

            if (Info.Type > 0 && CSVItem.GetMaxLevelByTypeAndCode_Maze(mData.id) <= Info.Level)
            {
                if (Info.ItemBox?.BoxID > 0)
                {
                    mData.max = false;
                }
                else
                {
                    mData.max = true;
                }
                type_max.SetActive(mData.max);
            }
            else
            {
                type_max.SetActive(false);
                mData.max = false;
            }


            icon_collect.gameObject.SetActive(info.Type != 1 && info.Type > 0);
            icon_Immovable.gameObject.SetActive(mData.Immovable && info.Type > 0);

            if (Info.Type > 0 && K3PlayerMgr.I.AreaData.NoPoint(mPoint) && !mData.Immovable)
            {
                //K3PlayerMgr.I.UnlockedId_Maze(mData.id);
            }

            inited = true;
            if (showTween)
            {
                LMaze.I.SaveGood(mPoint.ServerIndex, Info.id, mPoint.x, mPoint.y);
            }
            else
            {

            }
        }
        public int Index;
        void Update()
        {
            Index = mPoint.ServerIndex;

            if (inited)
            {
                if (locked)
                {
                    curDur = disposInterval - K3PlayerMgr.I.GetDuration(mData.creatTamp);
                    if (curDur <= TimeSpan.Zero)
                    {
                        DisPos();
                        uIMerge.ShowChekBox(false);
                    }
                }

                ShowItemBoxCD();

                ShowHeroBuffInfo();

                if (mData.BoxTimes_Buff > 0)
                {
                    buffAddNumText.text = $"+{mData.BoxTimes_Buff}";
                }
                else
                    buffAddNumText.text = "";
            }
        }

        private void OnEnable()
        {

        }




        private void OnDisable()
        {
            Common.EventMgr.UnregisterEvent(this);
        }

        public string GetDeleteTime()
        {
            return curDur.ToString(@"mm\:ss");
        }
        public double GetSecondTime()
        {
            return curDur.TotalSeconds;
        }

        IEnumerator ShowTween(float delaytime)
        {
            transform.localScale = Vector3.zero;
            yield return new WaitForSeconds(delaytime);
            transform.localScale = Vector3.one;
            DoTweenSeq = DOTween.Sequence();
            Vector3[] path = new Vector3[3];
            path[0] = transform.position; //起始点
            path[2] = CurGrid.goodRoot.position; //终点 
            DoTweenSeq.Insert(0, transform.DOScale(1f, 0.3f));
            // DoTweenSeq.Insert(.4f,transform.DOScale(1f,0.1f));
            //DoTweenSeq.Append(transform.DOPath(bezierPath, 0.6f).SetEase(Ease.OutCubic)); //InOutSine
            DoTweenSeq.Insert(0, DOJump(transform, path[2], 1.2f, 1, 0.5f, false).SetDelay(0.2f)); //InOutSine
            ani.SetTrigger("creat_move");
            DoTweenSeq.OnComplete(() =>
            {
                uIMerge.ShowCreatEffect(transform);
                transform.SetParent(CurGrid.goodRoot);
                transform.localPosition = Vector3.zero;
                canClick = true;
                goodImage.raycastTarget = true;
                typeRoot.SetActive(true);
            });
        }

        public static Sequence DOJump(Transform target, Vector3 endValue, float jumpPower, int numJumps, float duration, bool snapping = false)
        {
            if (numJumps < 1) numJumps = 1;
            float startPosY = target.position.y; // Temporary fix for OnStart not being called when using Goto instead of GotoWithCallbacks
            float offsetY = -1;
            bool offsetYSet = false;

            // Separate Y Tween so we can elaborate elapsedPercentage on that instead of on the Sequence
            // (in case users add a delay or other elements to the Sequence)
            Sequence s = DOTween.Sequence();
            Tween yTween = DOTween.To(() => target.position, x => target.position = x, new Vector3(0, jumpPower, 0), duration / (numJumps * 2))
                .SetOptions(AxisConstraint.Y, snapping).SetEase(Ease.OutQuad).SetRelative()
                .SetLoops(numJumps * 2, LoopType.Yoyo)
                .OnStart(() => startPosY = target.position.y); // FIXME not called if you only use Goto (and not GotoWithCallbacks)
            s.Append(DOTween.To(() => target.position, x => target.position = x, new Vector3(endValue.x, 0, 0), duration)
                    .SetOptions(AxisConstraint.X, snapping).SetEase(Ease.Linear)
                ).Join(DOTween.To(() => target.position, x => target.position = x, new Vector3(0, 0, endValue.z), duration)
                    .SetOptions(AxisConstraint.Z, snapping).SetEase(Ease.Linear)
                ).Join(yTween)
                .SetTarget(target).SetEase(DOTween.defaultEaseType);
            yTween.OnUpdate(() =>
            {
                if (!offsetYSet)
                {
                    offsetYSet = true;
                    offsetY = endValue.y - startPosY;
                }
                Vector3 pos = target.position;
                pos.y += DOVirtual.EasedValue(0, offsetY, yTween.ElapsedPercentage(), Ease.OutQuad);
                target.position = pos;
            });
            return s;
        }


        public void AutoMerge(UIMergeGood_Maze toGood, Action callBack)
        {

            canClick = false;
            Raycastimage.raycastTarget = false;
            toGood.canClick = false;
            toGood.Raycastimage.raycastTarget = false;

            transform.SetParent(MoveGoodRoot);

            var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
            if (uimerge != null)
            {
                GameObject jump_effect = Instantiate(uimerge.GameObject.transform.Find("Root/Ui_TuoWei1").gameObject, this.transform);
                jump_effect.transform.localPosition = Vector3.zero;


                if (!icon.gameObject.GetComponent<Canvas>())
                {
                    icon.gameObject.AddComponent<Canvas>();
                }
                icon.gameObject.GetComponent<Canvas>().overrideSorting = true;
                icon.gameObject.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.MainUI;
                icon.gameObject.GetComponent<Canvas>().sortingOrder = 100;

                TFWImage icon_light = GameObject.Instantiate(icon, this.transform);
                icon_light.name = "icon_light";
                icon_light.transform.localPosition = Vector3.zero;
                icon_light.transform.localScale = icon.transform.localScale;
                icon_light.transform.SetAsFirstSibling();
                icon_light.gameObject.AddComponent<OutLight>();
                icon_light.GetComponent<OutLight>().effectColor = new Color(1, 1, 0.85f, 0.9f);
                icon_light.GetComponent<OutLight>().effectDistance = new Vector2(10, 10);
                Shader shader = Shader.Find("GUI/Text Shader");
                Material material = new Material(shader);
                icon_light.material = material;
                icon_light.gameObject.GetComponent<Canvas>().overrideSorting = true;
                icon_light.gameObject.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.MainUI;
                icon_light.gameObject.GetComponent<Canvas>().sortingOrder = 80;


                //icon.transform.SetParent(icon_light.transform);

                DOJump(transform, toGood.transform.position, 1.2f, 1, 0.3f).OnComplete(() =>
                {
                    try
                    {
                        if (jump_effect.gameObject != null)
                        {
                            jump_effect.transform.SetParent(this.transform.parent);
                            Destroy(jump_effect.gameObject, 3);
                        }
                        if (icon.gameObject.GetComponent<Canvas>())
                        {
                            Destroy(icon.gameObject.GetComponent<Canvas>());
                        }
                        if (icon_light)
                        {
                            Destroy(icon_light);
                        }
                        toGood.canClick = true;
                        toGood.Raycastimage.raycastTarget = true;
                        toGood.CurGrid.Compound();

                        uIMerge.ShowMergeEffect(toGood.transform);

                        Raycastimage.raycastTarget = true;
                        canClick = true;

                        DisPos(true);

                        NTimer.CountDown(0.16f, callBack);
                    }
                    catch (Exception ex)
                    {
                        //Debug.LogError(ex.ToString());
                    }
                });

            }
        }


        private float pointCount = 30f;

        //获取二阶贝塞尔曲线路径数组
        private Vector3[] Bezier2Path(Vector3 startPos, Vector3 controlPos, Vector3 endPos)
        {
            Vector3[] path = new Vector3[(int)pointCount];
            for (int i = 1; i <= pointCount; i++)
            {
                float t = i / pointCount;
                path[i - 1] = Bezier2(startPos, controlPos, endPos, t);
            }

            return path;
        }

        // 2阶贝塞尔曲线
        public static Vector3 Bezier2(Vector3 startPos, Vector3 controlPos, Vector3 endPos, float t)
        {
            return (1 - t) * (1 - t) * startPos + 2 * t * (1 - t) * controlPos + t * t * endPos;
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            if (!canClick)
            {
                return;
            }


            // if (mData.Immovable)
            // {
            //     return;
            // }
            //鼠标点击A对象，按下鼠标时A对象响应此事件
            if (!uIMerge.CheckDoubleClick(this))
            {
                ani.SetTrigger("touch");
                uIMerge.ShowChekBox(true, this);
                uIMerge.OnUpdateBubbleState();
                uIMerge.isCancelState = false;
                GameAudio.PlayAudio(5);
            }
            ClickBoxItem();
#if UNITY_EDITOR
            // uIMerge.pointRoot.position = transform.position;
#endif
        }

        private List<Vector2> GetPoint(float radius = 180, int count = 8)
        {
            List<Vector2> pointList = new List<Vector2>();

            var radians = (Math.PI / 180) * (360.0 / count); //弧度
            for (int i = 0; i < count; i++)
            {
                double x = radius * Math.Sin(radians * i);
                double y = radius * Math.Cos(radians * i);
                pointList.Add(new Vector2((float)x, (float)y));
            }
            return pointList;
        }

        public void OnBeginDrag(PointerEventData eventData) //开始拖拽
        {

            if (mData.Immovable)
            {
                return;
            }


            canClick = false;
            transform.DOKill();
            isRaycastLocationValid =
                false; //ui穿透：置为可以穿透  【拖拽物体移动的时候鼠标下是有物体一直跟随遮挡的，如果不穿透就获取不到放置位置（OnEndDrag中判断是空格子，物体，还是无效位置）】
            ani.SetBool("move", true);
            ani.SetTrigger("moveTi");
            uIMerge.ShowChekBox(true, this);
            transform.SetParent(MoveGoodRoot);
            Raycastimage.raycastTarget = false;

            uIMerge.ShowMask0();
            startPos = Input.mousePosition;
            starttime = DateTime.Now;
            pointList.Clear();
            pointList = GetPoint(radius, collectpointCount);

        }


        public void OnDrag(PointerEventData eventData) //拖拽过程中
        {

            if (mData.Immovable)
            {
                return;
            }




            uIMerge.ShowMask0();
            transform.position = canvasCamera.ScreenToWorldPoint(Input.mousePosition); //鼠标左键按住拖拽的时候，物体跟着鼠标移动
            transform.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y, 0);
#if UNITY_EDITOR
            // uIMerge.pointRoot.position = transform.position;
            // uIMerge.pointRoot_guanxing.position = transform.position;
#endif
            curPointList.Clear();
            dictionary_good.Clear();
            for (int i = 0; i < pointList.Count; i++)
            {
                curPointList.Add((Vector2)Input.mousePosition + pointList[i]);
            }


            for (int i = 0; i < curPointList.Count; i++)
            {
                List<RaycastResult> list = GraphicRaycaster(curPointList[i]);
                for (int j = 0; j < list.Count; j++)
                {
                    if (list[j].gameObject.tag.Equals("UIMergeGood"))
                    {
                        UIMergeGood_Maze good = list[j].gameObject.GetComponent<UIMergeGood_Maze>();
                        if (good != null)
                        {
                            if (dictionary_good.ContainsKey(good))
                            {
                                dictionary_good[good]++;
                            }
                            else
                            {
                                dictionary_good.Add(good, 1);
                            }
                        }
                    }
                }
            }

            List<KeyValuePair<UIMergeGood_Maze, int>> list_dic = new List<KeyValuePair<UIMergeGood_Maze, int>>(dictionary_good);

            list_dic.Sort(delegate (KeyValuePair<UIMergeGood_Maze, int> s1, KeyValuePair<UIMergeGood_Maze, int> s2)
            {
                return s2.Value.CompareTo(s1.Value);
            });
            dictionary_good.Clear();

            foreach (KeyValuePair<UIMergeGood_Maze, int> pair in list_dic)
            {
                if (pair.Key.GetData().id == mData.id)
                {
                    dictionary_good.Add(pair.Key, pair.Value);
                }
            }

            if (dictionary_good.Count > 0)
            {
                list_dic = new List<KeyValuePair<UIMergeGood_Maze, int>>(dictionary_good);
                UIMergeGood_Maze curGood = list_dic[0].Key;
                if (K3PlayerMgr.I.PlayerData.guiding > 0 && curGood.mData.Immovable)
                {
                    ShowExitSame();
                }
                else
                {
                    curGood.ShowEnterSame();
                }

            }
            else
            {
                ShowExitSame();
            }
        }


        public void OnEndDrag(PointerEventData eventData) //
        {

            if (mData.Immovable)
            {
                return;
            }


            canClick = true;

            ShowExitSame();
            curPointList.Clear();
            dictionary_good.Clear();
            dictionary_grid.Clear();
            uIMerge.isCancelState = false;

            dur = (DateTime.Now - starttime).TotalSeconds;

            dir_drag = (Vector2)Input.mousePosition - startPos;

            float dir = dir_drag.magnitude * (Screen.width / 1080);

            //---距离远偏移大
            dir_rang = dir_rang_max * (dir / 1080);
            radius = radius_max * (dir / 1080);
            //---时间短偏移大


            dir_rang = dir_rang < dir_rang_min ? dir_rang_min : dir_rang;
            dir_rang = dir_rang > dir_rang_max ? dir_rang_max : dir_rang;
            radius = radius < radius_min ? radius_min : radius;
            radius = radius > radius_max ? radius_max : radius;
            pointList.Clear();
            pointList = GetPoint(radius, collectpointCount);


            EndPos = ((Vector2)Input.mousePosition - startPos).normalized * dir_rang + (Vector2)Input.mousePosition;

            for (int i = 0; i < pointList.Count; i++)
            {
                curPointList.Add((Vector2)EndPos + pointList[i]);
            }

            for (int i = 0; i < curPointList.Count; i++)
            {
                List<RaycastResult> list = GraphicRaycaster(curPointList[i]);
                for (int j = 0; j < list.Count; j++)
                {
                    if (list[j].gameObject.tag.Equals("UIMergeGood"))
                    {
                        UIMergeGood_Maze good = list[j].gameObject.GetComponent<UIMergeGood_Maze>();
                        if (good != null)
                        {
                            if (dictionary_good.ContainsKey(good))
                            {
                                dictionary_good[good]++;
                            }
                            else
                            {
                                dictionary_good.Add(good, 1);
                            }
                        }
                    }
                    else if (list[j].gameObject.tag.Equals("UIMergeGrid"))
                    {
                        UIMergeGrid_Maze grid = list[j].gameObject.GetComponent<UIMergeGrid_Maze>();
                        if (grid != null)
                        {
                            if (dictionary_grid.ContainsKey(grid))
                            {
                                dictionary_grid[grid]++;
                            }
                            else
                            {
                                dictionary_grid.Add(grid, 1);
                            }
                        }
                    }
                }
            }

            List<KeyValuePair<UIMergeGood_Maze, int>> list_dic = new List<KeyValuePair<UIMergeGood_Maze, int>>(dictionary_good);

            list_dic.Sort(delegate (KeyValuePair<UIMergeGood_Maze, int> s1, KeyValuePair<UIMergeGood_Maze, int> s2)
            {
                return s2.Value.CompareTo(s1.Value);
            });
            dictionary_good.Clear();


            foreach (KeyValuePair<UIMergeGood_Maze, int> pair in list_dic)
            {
                if (pair.Key.Info.id == Info.id)
                {
                    dictionary_good.Add(pair.Key, pair.Value);
                }
            }

            if (dictionary_good.Count > 0)
            {
                list_dic = new List<KeyValuePair<UIMergeGood_Maze, int>>(dictionary_good);
                UIMergeGood_Maze toGood = list_dic[0].Key;

                int MaxLevel = CSVItem.GetMaxLevelByTypeAndCode_Maze(mData.id);
                //Debug.LogError("MaxLevel:" + MaxLevel);
                //Debug.LogError("目标Level:" + toGood.Info.Level);
                //Debug.LogError("自己Level:" + Info.Level);
                //--判断 当前good是否能合成 (是否已解锁,是否是最大等级)
                if (toGood == this)
                {
                    ReSet();
                }
                else if (MaxLevel <= Info.Level)
                {
                    ReSet();
                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_MaxLevel"));
                }
                else
                {
                    if (toGood.mData.Immovable)
                    {
                        ReSet();
                    }
                    else
                    {
                        Action mergeFunc = new Action(() =>
                        {
                            canClick = false;
                            //--合成
                            transform.DOMove(toGood.transform.position, 0.05f).OnComplete(() =>
                        {
                            toGood.Compound();
                            canClick = true;
                            goodImage.raycastTarget = true;
                            DisPos(true);



                        });
                            ani.SetTrigger("disPos");
                            GameAudio.PlayAudio(9);
                        });

                        mergeFunc.Invoke();
                        NiceVibrationsCtrl.Haptic_MergeItems();
                    }
                }
            }
            else
            {
                List<KeyValuePair<UIMergeGrid_Maze, int>> list_dic_grid =
                    new List<KeyValuePair<UIMergeGrid_Maze, int>>(dictionary_grid);

                list_dic_grid.Sort(delegate (KeyValuePair<UIMergeGrid_Maze, int> s1, KeyValuePair<UIMergeGrid_Maze, int> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                dictionary_grid.Clear();
                foreach (KeyValuePair<UIMergeGrid_Maze, int> pair in list_dic_grid)
                {
                    dictionary_grid.Add(pair.Key, pair.Value);
                }


                if (dictionary_grid.Count > 0) //创建引导及合并引导不可交换~
                {
                    list_dic_grid = new List<KeyValuePair<UIMergeGrid_Maze, int>>(dictionary_grid);
                    UIMergeGrid_Maze toGrid = list_dic_grid[0].Key;

                    //--(判断交换逻辑)
                    if (toGrid.Good != null)
                    {
                        if (toGrid.Good.mData.Immovable)
                        {
                            ReSet();
                        }
                        else
                        {
                            //互换

                            LMaze.I.SaveGood(toGrid.Point.ServerIndex, Info.id, toGrid.Point.x, toGrid.Point.y);
                            LMaze.I.SaveGood(mPoint.ServerIndex, toGrid.Good.Info.id, mPoint.x, mPoint.y);

                            var toPoint = toGrid.Point;

                            toGrid.Good.SetGrid(mPoint);

                            SetGrid(toPoint);

                        }
                        uIMerge.ShowChekBox(true, this);

                    }
                    else
                    {
                        //移动
                        GridOutGood();
                        LMaze.I.RemoveGood(mPoint.ServerIndex);
                        SetGrid(toGrid.Point);
                        uIMerge.ShowChekBox(true, this);
                        LMaze.I.SaveGood(mPoint.ServerIndex, Info.id, mPoint.x, mPoint.y);
                    }
                }
                else
                {
                    ReSet();
                }
            }


            Raycastimage.raycastTarget = true;
            isRaycastLocationValid = true; //ui事件穿透：置为不能穿透
            uIMerge.OnUpdateBubbleState();

            uIMerge.HideMask0();

        }

        /// <summary>
        /// 当前道具  解锁相册、城堡升级、完成任务所需Y个（总和）， 当前棋盘有X个  x-2< y 就弹窗提示
        /// </summary>
        /// <returns></returns>
        public bool MergeWindowTip(out int curX, out int needY)
        {
            curX = K3PlayerMgr.I.GetItemCount(Info.id);
            needY = K3PlayerMgr.I.needItemCount(Info.id);

            Debug.Log($"当前道具：{curX}  所需量:{needY}");
            return curX - 2 < needY && needY > 0;
        }


        private List<RaycastResult> GraphicRaycaster(Vector2 pos)
        {
            var mPointerEventData = new PointerEventData(_mEventSystem);
            mPointerEventData.position = pos;
            List<RaycastResult> results = new List<RaycastResult>();

            gra.Raycast(mPointerEventData, results);
            return results;
        }

        public bool IsRaycastLocationValid(Vector2 sp, Camera eventCamera) //UI事件穿透：如置为false即可以穿透，被图片覆盖的按钮可以被点击到
        {
            return isRaycastLocationValid;
        }

        public void DisPos(bool immediately = false)
        {

            canClick = false;

            locked = false;

            GridOutGood();

            ani.SetTrigger("disPos");

            if (immediately)
            {
                mGa.SetActive(false);

                uIMerge.MergeGoodPool.Release(this);
            }
            else
            {
                //Debug.LogError("变小！！！");
                gameObject.transform.DOScale(0.2f, 0.2f);

                CoroutineMgr.StartCoroutine(DelayDisPos());
            }
        }

        public void InPool()
        {

            canClick = false;

            locked = false;

            ani.SetTrigger("disPos");

            mGa.SetActive(false);

            uIMerge.MergeGoodPool.Release(this);

        }


        private void GridOutGood()
        {
            uIMerge.mGoodDic.Remove(CurGrid.Point.ServerIndex);
            LMaze.I.RemoveGood(mPoint.ServerIndex);
            CurGrid.UpdateData();
        }

        IEnumerator DelayDisPos()
        {
            yield return new WaitForSeconds(0.2f);
            if (mGa != null)
            {
                mGa.SetActive(false);
                uIMerge.MergeGoodPool.Release(this);
            }
        }

        public void Collect()
        {
            //TODO 生成对应资源
            if (Info.Rewards?.Count > 0 && Info.Rewards[0].IsK3VM) //金币
            {
                if (Config.ConfigID.Recover_Energy == Info.Rewards[0].RewardId)
                {
                    if (!K3PlayerMgr.I.CanAddEnergy(Info.Rewards[0].RewardVal))
                    {
                        //体力超出拥有量 Tip 
                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("EnergyOverLimitTips"));
                        return;
                    }
                }

                //Logic.CSPlayer.I.k3ToServerData.useItemsToServer.Add(Info.id);

                //--使用
                K3PlayerMgr.I.AddMoney(Info.Rewards[0].RewardId, Info.Rewards[0].RewardVal, "useitem");
                uIMerge.ShowChekBox(false);
                K3PlayerMgr.I.ShowFlyEffect(Info.Rewards[0].RewardId, transform.position, Info.Rewards[0].RewardVal);

                //EventMgr.FireEvent(TEventType.UIMain_FlyVmItemEffect_Pos, GameCurrencyEnum.CURRENCY_GOLD, (int)item.value, pos);

                DisPos();


            }

        }


        public async void ClickBoxItem()
        {
            if (locked || mData.Immovable)
                return;
            //生成道具
            if (Info.ItemBox?.BoxID > 0)
            {
                if (CurBoxUseCd() > 0)
                {

                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_BoxInCD"));
                    return;
                }

                if (!K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, Info.ItemBox.CostEnery))
                {
                    // PopupManager.I.ShowDialog<UIHyposthenia>();

                    var showDataList = await UIComplementPop.GetEnergyShowData();
                    PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Energy, datas = showDataList, needData = new CommonItem.CommonItemData() { Id = 11171012, Typ = AssetType.Recover, Val = 1 } });

                    return;
                }

                if (BoxCanUse() && K3PlayerMgr.I.CheckGoodFull_Maze(Info.ItemBox.BoxRewardTimes))
                {
                    if (K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, Info.ItemBox.CostEnery))
                    {
                        if (uIMerge.CreatGood(this))
                        {
                            K3PlayerMgr.I.UseMoney(MoneyType.Energy, Info.ItemBox.CostEnery, "createitem");
                        }

                        if (mData.BoxTimes <= 0 && CurBoxUseCd() <= 0)
                        {
                            if (Info.ItemBox?.BoxUseCd == 0)
                            {
                                DisPos();
                                uIMerge.ShowChekBox(false);
                            }
                            else
                            {
                                //变为不可使用--等待CD
                                EnterItemBoxCD();
                                uIMerge.ShowChekBox(true, this);
                            }
                        }
                        else
                        {
                            uIMerge.ShowChekBox(true, this);
                        }


                        return;
                    }
                }

                return;
            }

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            if (K3.UIMerge.douclickCopyGood)
            {
                uIMerge.CopyGood(this);
            }
#endif
        }





        public void Sell()
        {

            //TODO 生成对应资源
            uIMerge.ShowChekBox(true, this, true);
            DisPos();
            uIMerge.isCancelState = true;
        }
        public void CancelSell()
        {
            if (uIMerge.CreatGood(Info, transform, false, CurGrid, "CancelSell") != null)
            {

            }
            uIMerge.ShowChekBox(false);
            uIMerge.isCancelState = false;
        }
        public bool Unlock(bool useMoney)
        {
            if (locked)
            {
                if (useMoney)
                {
                    if (K3PlayerMgr.I.UseMoney(MoneyType.Diamond, Info.UnlockCost, "unlockitem"))
                    {
                        mData.creatTamp = 0;
                        mData.UnLocked();
                        lockedObj.SetActive(false);
                        ani.SetBool("locked", false);
                        ani.SetTrigger("unlock");
                        spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[0],
                            false);
                        locked = false;
                        CurGrid.Unlock();
                        ShowItemBox();
                        try
                        {
                            GameAudio.PlayAudio(13);
                        }
                        catch
                        {
                            return true;
                        }

                        return true;
                    }
                    else
                    {
                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ui_diamond_tilte_not_enough"));
                        return false;
                    }
                }
                else
                {
                    mData.creatTamp = 0;
                    mData.locked = false;
                    lockedObj.SetActive(false);
                    ani.SetBool("locked", false);
                    ani.SetTrigger("unlock");
                    spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[0],
                        false);
                    locked = false;
                    CurGrid.Unlock();
                    try
                    {
                        GameAudio.PlayAudio(13);
                    }
                    catch
                    {
                        return true;
                    }

                    return true;
                }
            }

            return false;
        }

        public void SetGrid(Point toPoint)
        {
            //mPoint.ServerIndex = toPoint.ServerIndex;
            GoodInGrid(toPoint);

            transform.SetParent(CurGrid.goodRoot);

            canClick = false;

            transform.DOLocalMove(Vector3.zero, 0.3f).SetEase(Ease.OutSine).OnComplete(() =>
            {
                canClick = true;
                goodImage.raycastTarget = true;
            });
        }


        private void GoodInGrid(Point toPoint)
        {
            //mPoint.ServerIndex = toPoint.ServerIndex;
            mPoint = toPoint;

            uIMerge.mGoodDic[toPoint.ServerIndex] = this;

            if (uIMerge.mGridDic.TryGetValue(toPoint.ServerIndex, out var grid))
            {
                grid.UpdateData();

                grid.Point = toPoint;
            }

        }

        public void LevelUp()
        {
            int fromItemID = Info.id;

            Info = CSVItem.GetItemInfoByTypeAndCodeAndLevel_Maze(Info.Type, Info.Code, Info.Level + 1);

            int toItemID = Info.id;



            mData.LevelUp(Info);

            if (!mData.Immovable && Info.Type > 0)
            {
                //K3PlayerMgr.I.UnlockedId_Maze(Info.id);
            }
            icon_Immovable.gameObject.SetActive(mData.Immovable && Info.Type > 0);

            //K3PlayerMgr.I.PlayerData.mergeData_Maze.AddMergeCount();




            //--通过配置显示目标 
            // MgrResK3.Instance.LoadSprite(icon,"K3/Atlas/Type"+goodData.Type+"Code"+goodData.Code+"/" +goodData.Icon);

            ShowItemBox();
            float scaleTime = 0.12f;
            //K3PlayerMgr.I.AreaData.AddItem(Info.id);
            var seq = DOTween.Sequence();
            seq.Append(transform.DOScale(0, scaleTime));
            seq.InsertCallback(scaleTime, ChangeImage);
            seq.Append(transform.DOScale(1, scaleTime));
            seq.Play();

            LMaze.I.SaveGood(mPoint.ServerIndex, Info.id, mPoint.x, mPoint.y);
            //Invoke(nameof(ChangeImage), 0.15f);

            if (CSVItem.GetMaxLevelByTypeAndCode_Maze(mData.id) <= Info.Level)
            {
                if (Info.ItemBox?.BoxID > 0)
                {
                    mData.max = false;
                }
                else
                {
                    mData.max = true;
                }
                type_max.SetActive(mData.max);
            }


        }

        private void ShowItemBox()
        {
            if (BoxCanUse())
            {
                type_collect.SetActive(Info.ItemBox?.BoxID > 0);
                boxEffect.SetActive(Info.ItemBox?.BoxID > 0);
                itemboxCDObj.SetActive(false);
                ani.SetBool("itembox", true);
            }
            else
            {
                type_collect.SetActive(false);
                boxEffect.SetActive(false);
                itemboxCDObj.SetActive(true);
                ani.SetBool("itembox", false);
            }
            if (Info.ItemBox?.BoxID > 0)
            {
                ShowItemBoxCD();
            }
            else
            {
                itemboxCDObj.SetActive(false);
                type_collect.SetActive(false);
                boxEffect.SetActive(false);
            }
        }
        private void EnterItemBoxCD()
        {
            mData.boxCdTamp = K3PlayerMgr.I.GetLocalTimestamp() + Info.ItemBox.BoxUseCd * 1000;//终点时间
            ShowItemBox();
            ShowItemBoxCD();
            CurGrid.UpdateData();
        }

        public int GetCurRestCDCost()
        {
            double curcd = CurBoxUseCd();
            int cost = (int)Math.Ceiling(Info.ItemBox.GemToReset * curcd / Info.ItemBox.BoxUseCd);
            return cost;
        }

        public double CurBoxUseCd()
        {
            if (mData.boxCdTamp == 0)
            {
                return 0;
            }

            var curDd = TimeSpan.FromMilliseconds(Mathf.Max(0, mData.boxCdTamp - GameTime.Time)).TotalSeconds;
            if (curDd <= 0)
            {
                curDd = 0;
            }
            return curDd;
        }

        public bool BoxCanUse()
        {
            return Info.ItemBox?.BoxID > 0 &&
                   (Info.ItemBox.BoxUseCd == 0 || (Info.ItemBox.BoxUseCd != 0 && CurBoxUseCd() <= 0));
        }
        void ShowItemBoxCD()
        {
            if (Info?.ItemBox?.BoxID > 0)
            {
                //--显示CD
                if (BoxCanUse())
                {
                    HidBoxCD();
                    type_collect.SetActive(true);
                    boxEffect.SetActive(true);
                }
                else
                {
                    itemboxCDObj.SetActive(true);
                    boxCdPointer.transform.localEulerAngles = new Vector3(0, 0, -360 * ((float)(Info.ItemBox.BoxUseCd - CurBoxUseCd()) / Info.ItemBox.BoxUseCd));
                    HidBoxCD();
                    type_collect.SetActive(false);
                    boxEffect.SetActive(false);
                }

            }
        }

        private void ShowHeroBuffInfo()
        {
            if (mData.goodType == 1)
            {
                //mData.GetBuffTime(out var totalSec, out var lastTime);
                int lastTime=0, totalSec = 0;
                heroBuffObj.SetActive(lastTime > 0);
                ShowHeroEffect(lastTime > 0);
                if (totalSec > 0)
                {
                    heroBuffSlider.value = (float)(lastTime / totalSec);
                }

                if (lastTime > 0)
                {
                    UITools.SetImageBySpriteName(heroBuffIcon, "skill_img_buff_1");//后续改成对应三种Buff 图
                }
            }
        }

        GameObject hero_effect;

        public void ShowHeroEffect(bool show)
        {
            if (show)
            {
                if (hero_effect == null)
                {
                    var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
                    if (uimerge.GameObject.transform.Find("Root/Hecheng_ui"))
                    {
                        hero_effect = Instantiate(uimerge.GameObject.transform.Find("Root/Hecheng_ui").gameObject, this.transform);
                        hero_effect.transform.localPosition = Vector3.zero;
                    }
                }
            }
            else
            {
                if (hero_effect != null)
                {
                    Destroy(hero_effect.gameObject);
                    hero_effect = null;
                }
            }
        }


        void HidBoxCD()
        {
            if (CurBoxUseCd() <= 0)
            {
                if (itemboxCDObj.activeSelf)
                {
                    itemboxCDObj.SetActive(false);
                }
                if (mData.BoxTimes <= 0)
                {
                    mData.RestBox(Info);
                    uIMerge.ShowChekBox(true, this);
                    ShowItemBox();
                    CurGrid.UpdateData();
                }
            }
        }

        void ChangeImage()
        {
            //Debug.LogError("======================================ChangeImageIcon");
            UITools.SetCommonItemIcon(icon, Info.Icon);
            UITools.SetCommonItemIcon(icon_Immovable, Info.Icon);
            UITools.SetCommonItemIcon(icon_collect, Info.Icon);
            K3PlayerMgr.I.UnlockedId(Info.id);
        }

        public void Compound()
        {
            CurGrid.Compound();
            uIMerge.ShowChekBox(true, this);
            uIMerge.CheckMazeTask();
            // uIMerge.ShowTopInfo(this);
        }

        //public bool CanLevelUpByCityTech(bool tip = true)
        //{
        //    var maxLevel = Logic.LSkill.I.GetMergeLevel();

        //    if (Info.Level < maxLevel)
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        if (tip)
        //        {
        //            bool Unlock = false;
        //            var UIMain2 = SceneManager1.I.FindScene(typeof(UIMain2));
        //            if (UIMain2 == null)
        //            {

        //            }
        //            else
        //            {

        //                GameObject targetGa = UIMain2.GameObject.transform.Find("content/MenuRoot").gameObject;
        //                if (targetGa == null)
        //                {

        //                }
        //                else
        //                {
        //                    if (targetGa.gameObject.activeSelf)
        //                    {

        //                        Unlock = true;
        //                    }

        //                }
        //            }

        //            if (Unlock)
        //            {
        //                PopupManager.I.ShowDialog<UIMainCityNeedUpgrade>();
        //            }
        //            else
        //            {
        //                FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ERRCODE_unionapplylevel"));
        //            }

        //        }
        //        return false;
        //    }
        //}

        void ReSet()
        {
            transform.SetParent(CurGrid.goodRoot);
            // transform.localPosition = Vector3.zero;
            uIMerge.ShowChekBox(true, this);
            // canClick = false;
            transform.DOLocalMove(Vector3.zero, 0.1f); //.OnComplete(() => { canClick = true; });
            // uIMerge.ShowTopInfo(this);
        }

        public void ShowEnterSame()
        {
            // ani.SetBool("enter", true);
            // ani.SetBool("exit", false);
            // ani.SetTrigger("enterTi");
            uIMerge.ShowSameBox(true, transform);
        }

        public void ShowExitSame()
        {
            // ani.SetBool("enter", false);
            // ani.SetBool("exit", true);
            // ani.SetTrigger("exitTi");
            uIMerge.ShowSameBox(false, null);
        }

        public ItemInfo GetInfo()
        {
            return Info;
        }

        public UIMergeGoodData_Maze GetData()
        {
            return mData;
        }

        public bool GetLocked()
        {
            return locked;
        }

        public void DoubleClick()
        {
            if (!locked && !mData.Immovable)
            {
                Collect();
            }
        }

        public void ShowGuideMerge()
        {
            ani.SetTrigger("guideMerge");
        }

        public void HidGuideMerge()
        {
            ani.SetTrigger("idel");
        }

        void ChangeType()
        {
        }

        public void ShowTaskUseType(bool show)
        {
            if (type_task == null)
            {
                type_task = transform.Find("typeRoot/taskIcon").gameObject;
            }

            if (type_scene.activeSelf)
            {
                type_task.SetActive(false);
                return;
            }

            type_task.SetActive(show);
        }

        public void ShowPhotoUseType(bool show)
        {
            if (type_scene == null)
            {
                type_scene = transform.Find("typeRoot/sceneIcon").gameObject;
            }
            if (type_bg == null)
            {
                type_bg = transform.Find("bg").gameObject;
            }
            type_scene.SetActive(show);
            type_bg.SetActive(show || Info.Type < 0);
        }


        public void ShowCityUseType(bool show)
        {
            if (type_city == null)
            {
                type_city = transform.Find("typeRoot/cityIcon").gameObject;
            }

            if (cityBg == null)
            {
                cityBg = transform.Find("cityBg").gameObject;
            }

            cityBg.SetActive(show);
            type_city.SetActive(show);

            //if (show && LPlayer.I.GetMainCityLevel() == 2 && K3GuidMgr.I.cityLevelUpGuiding == false)
            //{
            //    if (GameData.I.SkillData.MTechs.TryGetValue(1, out var techData))
            //    {
            //        var upCost = PlayerAssetsMgr.I.HaveAssets(techData.UpgradeCost);
            //        if (string.IsNullOrEmpty(upCost))
            //        {


            //            var guidData = new UIGuidData();
            //            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMerge", UIItem = "Root/btnBack", slide = true });
            //            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMain2", UIItem = "content/MenuRoot/Castle" });
            //            guidData.guidItems.Add(new UIGuidItem() { UIName = "UIMainCity", UIItem = "Root/Building/Castle1" });
            //            guidData.guidItems.Add(new UIGuidItem() { UIName = "UICityLevelUp", UIItem = "Root/mBtnRoot/Button" });

            //            if (UIGuid.StartGuid(guidData))
            //            {
            //                K3GuidMgr.I.cityLevelUpGuiding = true;
            //            }
            //        }
            //    }
            //}
        }


    }
}
