﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class EditorTerritorySubLayerCreationDialog : EditorWindow
    {
        void OnEnable()
        {
            mLayerWidth = Map.currentMap.mapWidth;
            mLayerHeight = Map.currentMap.mapHeight;
        }

        void OnGUI()
        {
            GUILayout.BeginHorizontal();
            mLayerWidth = EditorGUILayout.FloatField("Layer Width", mLayerWidth);
            mLayerHeight = EditorGUILayout.FloatField("Layer Height", mLayerHeight);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            mTileWidth = EditorGUILayout.FloatField("Tile Width", mTileWidth);
            mTileHeight = EditorGUILayout.FloatField("Tile Height", mTileHeight);
            GUILayout.EndHorizontal();

            mExportFileName = EditorGUILayout.TextField("Export File Name", mExportFileName);

            if (GUILayout.Button("Create"))
            {
                bool valid = CheckParameter();
                if (valid)
                {
                    var map = Map.currentMap as EditorMap;
                    var layer = map.GetMapLayer<EditorTerritoryLayer>();
                    var subLayer = new EditorTerritorySubLayer();
                    int cols = Mathf.CeilToInt(mLayerWidth / mTileWidth);
                    int rows = Mathf.CeilToInt(mLayerHeight / mTileHeight);
                    string name = layer.GetUniqueSubLayerName("new layer");
                    var data = new config.EditorTerritorySubLayerData(name, false, mTileWidth, mTileHeight, cols, rows, 1, null, null, null, mExportFileName, null, new config.TerritorySharedEdgeInfo[0], null, 0, 0, null);
                    subLayer.Load(data, layer.gameObject.transform);
                    layer.AddSubLayer(subLayer);
                    mCallback();
                    Close();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid Parameter", "OK");
                }
            }
        }

        public void Show(System.Action callback)
        {
            mCallback = callback;
            Show();
        }

        bool CheckParameter()
        {
            if (mLayerWidth <= 0 || mLayerHeight <= 0 ||
                mTileWidth <= 0 || mTileHeight <= 0)
            {
                return false;
            }

            if (string.IsNullOrEmpty(mExportFileName))
            {
                return false;
            }
            return true;
        }

        float mLayerWidth;
        float mLayerHeight;
        float mTileWidth = MapModule.defaultGroundTileSize;
        float mTileHeight = MapModule.defaultGroundTileSize;
        string mExportFileName;
        System.Action mCallback;
    }
}

#endif