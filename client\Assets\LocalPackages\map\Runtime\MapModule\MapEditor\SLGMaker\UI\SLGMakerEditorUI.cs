﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using UnityEditor.SceneManagement;

namespace TFW.Map
{
    [CustomEditor(typeof(SLGMakerEditor))]
    public class SLGMakerEditorUI : UnityEditor.Editor
    {
        [MenuItem("Map Editor/Help #h")]
        public static void OpenHelpPage()
        {
            Application.OpenURL("https://wiki..com/display/SLGFramework/Unity+Map+Editor+Tutorials");
        }

        [MenuItem("Map Editor/Utils/k2/创建暗影区域prefab,生成的文件在Assets文件夹的AllShadowGrounds_lod0.prefab")]
        public static void CreateAllShadowAreaPrefab()
        {
            string filePath = EditorUtility.OpenFilePanel("Select Config File", "", "tsv");
            if (!string.IsNullOrEmpty(filePath))
            {
                var dlg = EditorUtils.CreateInputDialog("输入地图的大小");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Map Width", "", "7200"),
                    new InputDialog.StringItem("Map Height", "", "7200"),
                };
                dlg.Show(items, (List<InputDialog.Item> parameters) => {
                    var widthStr = (parameters[0] as InputDialog.StringItem).text;
                    var heightStr = (parameters[1] as InputDialog.StringItem).text;
                    int.TryParse(widthStr, out int width);
                    int.TryParse(heightStr, out int height);

                    CreateShadowAreaPrefab.Create(filePath, "Assets", width, height);
                    return true;
                });
            }
        }

        [MenuItem("Map Editor/Utils/k2/生成圣地装饰物prefab,生成的文件在Assets文件夹的AllHolyLandDecorations_lod0.prefab")]
        public static void CreateAllHoleLandDecorationPrefabs()
        {
            string filePath = EditorUtility.OpenFilePanel("选择村庄配置表", "", "tsv");
            if (!string.IsNullOrEmpty(filePath))
            {
                var dlg = EditorUtils.CreateInputDialog("输入地图的大小");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Map Width", "", "0"),
                    new InputDialog.StringItem("Map Height", "", "0"),
                };
                dlg.Show(items, (List<InputDialog.Item> parameters) => {
                    var widthStr = (parameters[0] as InputDialog.StringItem).text;
                    var heightStr = (parameters[1] as InputDialog.StringItem).text;
                    int.TryParse(widthStr, out int width);
                    int.TryParse(heightStr, out int height);

                    CreateHolyLandDecorationPrefabs.Create(filePath, "Assets", width, height);
                    return true;
                });
            }
        }

        [MenuItem("Map Editor/Utils/Set Alpha Multiplier")]
        public static void SetAlphaMultiplier()
        {
            var dlg = EditorUtils.CreateInputDialog("Set Alpha Multiplier");

            var items = new List<InputDialog.Item>
            {
                new InputDialog.StringItem("Alpha", "", "1"),
            };
            dlg.Show(items, (List<InputDialog.Item> parameters) => {
                string sizeStr = (parameters[0] as InputDialog.StringItem).text;
                bool suc = Utils.ParseFloat(sizeStr, out float value);
                if (!suc)
                {
                    EditorUtility.DisplayDialog("Error", "invalid value!", "OK");
                    return false;
                }

                Shader.SetGlobalFloat("_AlphaMultiplier", value);

                return true;
            });
        }

        [MenuItem("Map Editor/Utils/Draw NavMesh Or Obstacle")]
        public static void DrawNavMeshOrObstacle()
        {
            var filePath = EditorUtility.OpenFilePanel("Open File", "", "json");
            if (!string.IsNullOrEmpty(filePath))
            {
                NavMeshViewer.Create(filePath);
            }
        }

        [MenuItem("Map Editor/Utils/Show Selected Object World Position")]
        public static void ShowSelectedObjectWorldPosition()
        {
            var obj = Selection.activeGameObject;
            if (obj != null)
            {
                EditorUtility.DisplayDialog("", $"World Position {obj.transform.position}", "OK");
            }
        }

        [MenuItem("Map Editor/Utils/Resize Ground Tile")]
        public static void ResizeGroundTileMesh()
        {
            var dlg = EditorUtils.CreateInputDialog("Resize Ground Tile");

            var items = new List<InputDialog.Item>
            {
                new InputDialog.StringItem("New Size", "", "0"),
            };
            dlg.Show(items, (List<InputDialog.Item> parameters) => {
                string sizeStr = (parameters[0] as InputDialog.StringItem).text;
                bool suc = Utils.ParseInt(sizeStr, out int size);
                if (!suc || size <= 0)
                {
                    EditorUtility.DisplayDialog("Error", "invalid size!", "OK");
                    return false;
                }

                ResizeGroundTile.Resize(size);

                return true;
            });
        }

        [MenuItem("Map Editor/Action/Open Map Editor Action Window #o")]
        static void OpenActionWindow()
        {
            var window = EditorWindow.GetWindow<ActionWindow>("Actions");
            EditorUtils.CenterWindow(window);
            window.Show();
        }

        [MenuItem("Map Editor/Action/Undo Map Editor Action #z")]
        static void UndoAction()
        {
            if (ActionManager.instance != null)
            {
                ActionManager.instance.Undo();
            }
        }

        [MenuItem("Map Editor/Action/Redo Map Editor Action #r")]
        static void RedoAction()
        {
            if (ActionManager.instance != null)
            {
                ActionManager.instance.Redo();
            }
        }

        [MenuItem("Map Editor/Utils/Copy GameObject Transform #q")]
        static void CopyTransform()
        {
            var obj = Selection.activeGameObject;
            if (obj != null)
            {
                EditorConfig.copyPosition = obj.transform.position;
                EditorConfig.copyRotation = obj.transform.rotation;
                EditorConfig.copyScale = obj.transform.localScale;
            }
        }

        [MenuItem("Map Editor/Utils/Paste GameObject Transform #w")]
        static void PasteTransform()
        {
            var obj = Selection.activeGameObject;
            if (obj != null)
            {
                obj.transform.position = EditorConfig.copyPosition;
                obj.transform.rotation = EditorConfig.copyRotation;
                obj.transform.localScale = EditorConfig.copyScale;
            }
        }

        [MenuItem("Map Editor/Utils/Adjust Prefab Scale #p")]
        static void AdjustScale()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            if (stage == null)
            {
                return;
            }
            var assetPath = stage.prefabAssetPath;
            if (string.IsNullOrEmpty(assetPath))
            {
                EditorUtility.DisplayDialog("Error", "Must be attached to a prefab!", "OK");
                return;
            }

            var inputDialog = EditorUtils.CreateInputDialog("Adjust scale");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Multiplier", "", "1"),
                };
            inputDialog.Show(items, (List<InputDialog.Item> parameters) =>
            {
                float scaleMultiplier;
                Utils.ParseFloat((parameters[0] as InputDialog.StringItem).text, out scaleMultiplier);
                if (scaleMultiplier <= 0)
                {
                    return false;
                }
                var prefab = stage.prefabContentsRoot;
                var objs = Selection.gameObjects;
                for (int i = 0; i < objs.Length; ++i)
                {
                    objs[i].transform.localScale *= scaleMultiplier;
                }

                PrefabUtility.SaveAsPrefabAsset(prefab, assetPath);
                return true;
            });
        }

        [MenuItem("Map Editor/Utils/Create PrefabOutline From Selected Objects")]
        static void CreatePrefabOutline()
        {
            PrefabOutlineCreator.Create();
        }

        [MenuItem("Map Editor/Utils/Create Decoration Prefab From Selected Objects")]
        static void CreateDecorationLayerPrefab()
        {
            var dlg = EditorUtils.CreateInputDialog("Create Prefab");
            var items = new List<InputDialog.Item> {
                    new InputDialog.BoolItem("Ignore inactive game objects", "", false),
                    new InputDialog.PathItem("Folder Path", "", ""),
            };

            dlg.Show(items, (List<InputDialog.Item> parameters) => {
                bool ignoreInactive = (parameters[0] as InputDialog.BoolItem).value;
                string folderPath = (parameters[1] as InputDialog.PathItem).text;
                if (string.IsNullOrEmpty(folderPath))
                {
                    return false;
                }
                string filePath = $"{folderPath}/decoration_prefab_lod0.prefab";
                DecorationPrefabCreator.Create(ignoreInactive, filePath);
                return true;
            });
        }

        [MenuItem("Map Editor/Utils/Show Selected Prefab Count #x")]
        static void ShowSelectedPrefabCount()
        {
            var objs = Selection.gameObjects;
            EditorUtility.DisplayDialog("", $"Selected Object Count {objs.Length}", "OK");
        }

        [MenuItem("Map Editor/Utils/Create Map List Setting From Map List File")]
        static void CreateMapListSettingFromMapList()
        {
            var mapListFile = Selection.activeObject as TextAsset;
            if (mapListFile != null)
            {
                string filePath = AssetDatabase.GetAssetPath(mapListFile);
                MapListSettingUI.CreateMapListSettingFromMapListFile(filePath);
            }
        }

        [MenuItem("Map Editor/Action/Save Map #s")]
        public static void SaveMap()
        {
            SLGMakerEditor.instance?.SaveMap();
        }

        [MenuItem("Map Editor/Utils/Change Ground Tile Mesh")]
        public static void ChangeGroundTileMesh()
        {
            //将地表的mesh替换为同名的obj或fbx文件
            var obj = Selection.activeObject;
            if (obj is DefaultAsset) {
                var folderPath = AssetDatabase.GetAssetPath(obj);
                ReplaceGroundMeshInFolder.Replace(folderPath);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a folder!", "OK");
            }
        }

        void OnEnable()
        {
            mEditor = target as SLGMakerEditor;
            EditorApplication.playModeStateChanged -= OnPlayModeChange;
            EditorApplication.playModeStateChanged += OnPlayModeChange;
        }

        public override void OnInspectorGUI()
        {
            if (EditorApplication.isPlaying == false)
            {
                EditorGUILayout.BeginHorizontal();
                {
                    EditorGUIUtility.labelWidth = 100;
                    EditorGUILayout.TextField(new GUIContent("Project Folder", "地图工程路径"), mEditor.projectFolder);
                    EditorGUIUtility.labelWidth = 0;
                    if (GUILayout.Button("Select", GUILayout.MaxWidth(60)))
                    {
                        SelectProjectFolder(true);
                        Repaint();
                    }
                    if (GUILayout.Button("Open", GUILayout.MaxWidth(60)))
                    {
                        EditorUtils.OpenFolder(mEditor.projectFolder);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                {
                    EditorGUIUtility.labelWidth = 100;
                    EditorGUILayout.TextField(new GUIContent("Export Folder", "运行时游戏地图数据路径"), mEditor.exportFolder);
                    EditorGUIUtility.labelWidth = 0;
                    if (GUILayout.Button("Select", GUILayout.MaxWidth(60)))
                    {
                        SelectExportFolder();
                        Repaint();
                    }
                    if (GUILayout.Button("Open", GUILayout.MaxWidth(60)))
                    {
                        EditorUtils.OpenFolder(mEditor.exportFolder);
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button("Create New Map"))
                    {
                        if (mEditor.projectFolder.Length > 0 && Directory.Exists(mEditor.projectFolder))
                        {
                            if (!IsDirectoryEmpty(mEditor.projectFolder))
                            {
                                EditorUtility.DisplayDialog("Error", "Project folder is not empty", "OK");
                            }
                            else
                            {
                                if (Map.currentMap != null)
                                {
                                    EditorUtility.DisplayDialog("Error", "Map is already created!", "OK");
                                }
                                else
                                {
                                    var window = EditorWindow.GetWindow<CreateMapWindow>();
                                    EditorUtils.CenterWindow(window, 600, 300, 1000, 400);
                                    window.OnClickCreateMap += CreateNewMap;
                                }
                            }
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Select a project folder", "OK");
                        }
                    }

                    if (GUILayout.Button(new GUIContent("Reset Map", "重置地图编辑器")))
                    {
                        mEditor.Reset();
                        var scene = EditorSceneManager.GetActiveScene();
                        var rootObjects = scene.GetRootGameObjects();
                        if (rootObjects != null)
                        {
                            for (int i = 0; i < rootObjects.Length; ++i)
                            {
                                if (rootObjects[i].name != "SLGMakerMapEditor")
                                {
                                    GameObject.DestroyImmediate(rootObjects[i]);
                                }
                            }
                        }
                    }

                    if (GUILayout.Button(new GUIContent("Clear Project Folder", "清空地图工程文件夹")))
                    {
                        if (EditorUtility.DisplayDialog("Clear Project Folder", "Map data file will be removed! Are you sure ?", "Yes", "No"))
                        {
                            mEditor.ClearProjectFolder(mEditor.projectFolder);
                        }
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button(new GUIContent("Save Map", "保存地图工程数据")))
                    {
                        if (mEditor.projectFolder.Length == 0)
                        {
                            SelectProjectFolder(true);
                        }
                        if (mEditor.projectFolder.Length > 0)
                        {
                            mEditor.SaveMap(mEditor.projectFolder);
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("Error", "Select a project folder", "OK");
                        }
                    }

                    if (GUILayout.Button(new GUIContent("Load Map", "加载地图工程数据")))
                    {
                        if (mEditor.projectFolder.Length == 0 || !Directory.Exists(mEditor.projectFolder))
                        {
                            SelectProjectFolder(false);
                        }
                        if (mEditor.projectFolder.Length > 0 && Directory.Exists(mEditor.projectFolder))
                        {
                            mEditor.LoadMap(mEditor.projectFolder);
                        }
                    }

                    if (GUILayout.Button(new GUIContent("Export Map", "导出游戏运行时所需地图数据,只导出现有数据,不会生成数据")))
                    {
                        try
                        {
                            if (mEditor.exportFolder.Length == 0)
                            {
                                SelectExportFolder();
                            }
                            if (mEditor.exportFolder.Length > 0)
                            {
                                EditorUtils.ShowProgressiveBar(true, "Exporting...", "Exporting Map Data...", 0.5f);
                                mEditor.ExportMap(mEditor.exportFolder, true);
                                EditorUtility.ClearProgressBar();
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("Error", "Select an export folder", "OK");
                            }
                        }
                        catch
                        {
                            EditorUtility.ClearProgressBar();
                        }
                    }

                    if (GUILayout.Button(new GUIContent("Update Map", "生成所有地图数据然后导出,这一步可能会比较耗时,但是可一键导出地图编辑器中所有游戏运行时所需数据!")))
                    {
                        var dialog = EditorUtils.CreateDialog<UpdateMapDialog>("Update Map Setting", false);
                        dialog.Show(() => {
                            mEditor.UpdateMap(true);
                        }, mEditor.updateMapConfig);
                    }
                }
                EditorGUILayout.EndHorizontal();

                var map = Map.currentMap as EditorMap;
                if (map != null)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    map.saveRiverMaterials = EditorGUILayout.ToggleLeft(new GUIContent("Save River Materials","是否保存polygon river layer生成的河流材质(河流创建时可以使用同一个材质,但是每条河流可以在河流编辑器中修改参数,这些修改过参数的材质会保存到单独的材质文件中),如果没使用polygon river layer,则可忽略."), map.saveRiverMaterials);
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.TextField(new GUIContent("River Material Save Folder", "河流材质保存的文件夹"), map.riverMaterialsFolder);
                    if (GUILayout.Button("Select"))
                    {
                        map.riverMaterialsFolder = EditorUtility.OpenFolderPanel("Select River Material Folder", "Assets", "");
                        map.riverMaterialsFolder = Utils.ConvertToUnityAssetsPath(map.riverMaterialsFolder);
                    }
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.EndVertical();
                }
            }

            mCameraEditor.DrawInspector();

            Repaint();
        }

        void OnPlayModeChange(PlayModeStateChange playMode)
        {
            if (playMode == PlayModeStateChange.ExitingEditMode)
            {
                if (mEditor != null)
                {
                    mEditor.Reset();
                }
            }
        }

        void SelectProjectFolder(bool checkEmpty)
        {
            var folderPath = EditorUtility.OpenFolderPanel("Select Project Folder", "Map", "");
            if (folderPath != null)
            {
                if (!checkEmpty || IsDirectoryEmpty(folderPath))
                {
                    mEditor.projectFolder = folderPath;
                }
                else
                {
                    if (folderPath.Length > 0)
                    {
                        EditorUtility.DisplayDialog("Error", "Project folder is not empty", "OK");
                    }
                }
            }
        }

        void SelectExportFolder()
        {
            var folderPath = EditorUtility.OpenFolderPanel("Select Export Folder", mEditor.projectFolder, "");
            if (folderPath != null && folderPath.Length > 0)
            {
                mEditor.exportFolder = folderPath;
            }
        }

        public bool IsDirectoryEmpty(string path)
        {
            if (path.Length > 0)
            {
                string[] dirs = System.IO.Directory.GetDirectories(path);
                string[] files = System.IO.Directory.GetFiles(path);
                return dirs.Length == 0 && files.Length == 0;
            }
            return false;
        }

        void CreateNewMap(float mapWidth, float mapHeight, GameMapType type)
        {
            SceneView.lastActiveSceneView.rotation = Quaternion.Euler(45, 0, 0);
            var map = mEditor.CreateNewMap(type, mapWidth, mapHeight) as EditorMap;
            if (map == null)
            {
                return;
            }
            if (type == GameMapType.P2)
            {
                //load lod config
                var editorMapData = map.data as EditorMapData;
                editorMapData.mapLODSetting.LoadConfig(MapModule.intuitiveZoomConfigFilePath);

                var data = Map.currentMap.data as EditorMapData;
                int nLODs = editorMapData.lodManager.lodCount;
                if (nLODs > 1)
                {
                    //data.cameraMinHeight = editorMapData.lodManager.GetLOD(0).cameraHeight;
                    //data.cameraMaxHeight = editorMapData.lodManager.GetLOD(nLODs - 1).cameraHeight;
                }

                //create a new map for p2
                var frontLayer = map.CreateEditorGridModelLayer(MapCoreDef.MAP_LAYER_NODE_FRONT, mapWidth, mapHeight, MapModule.defaultFrontTileSize, MapModule.defaultFrontTileSize, GridType.Rectangle);
                frontLayer.asyncLoading = false;

                var terrainLayer = map.CreateBlendTerrainLayer(MapCoreDef.MAP_LAYER_NODE_GROUND, mapWidth, mapHeight, MapModule.defaultGroundTileSize, MapModule.defaultGroundTileSize);

                var railLayer = map.CreateRailwayLayer(MapCoreDef.MAP_LAYER_NODE_RAILWAY, mapWidth, mapHeight, 5, mapWidth * 0.5f, 50, 10, new Vector3(mapWidth * 0.5f, 0, mapWidth * 0.5f));
                railLayer.asyncLoading = false;

                var navMeshLayer = map.CreateNavMeshLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH, mapWidth, mapHeight);

                var npcRegionLayer = map.CreateNPCRegionLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize);
                npcRegionLayer.active = false;
                //npcRegionLayer.AddRandomRegionSpriteTemplate();
                //bool suc = npcRegionLayer.FillRandomTiles();
                //Debug.Assert(suc);

                var strategy = new GenerateSpawnPointsRandomly(MapCoreDef.MAP_NPC_DENSITY, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX, 4, 4);
                var entitySpawnLayer = map.CreateEntitySpawnLayer(MapCoreDef.MAP_LAYER_NODE_ENTITY_SPAWN, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize, new Vector2(MapCoreDef.MAP_NPC_MOVE_RANGE_MIN, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX), MapCoreDef.MAP_NPC_WAYPOINT_COUNT, strategy);

                var lodLayer = map.CreateLODLayer(MapCoreDef.MAP_LAYER_NODE_LOD, mapWidth, mapHeight);
                lodLayer.GetLayerData().lodConfig.SetLODCount(editorMapData.lodManager.lodCount);

                float lodBufferDistance = 10.0f;
                for (int i = 0; i < nLODs; ++i)
                {
                    float cameraHeightInZoom = Map.currentMap.CalculateCameraHeightFromZoom(i);
                    float cameraHeightInZoomPlus1 = Map.currentMap.CalculateCameraHeightFromZoom(i + 1);
                    float delta = cameraHeightInZoomPlus1 - cameraHeightInZoom;
                    float bufferDistance = Mathf.Min(lodBufferDistance, delta / 3.0f);
                    float zoom = Map.currentMap.CalculateCameraZoom(cameraHeightInZoom + bufferDistance);
                    float bufferZoom = zoom - i;
                    lodLayer.GetLayerData().lodConfig.lodConfigs[i].changeZoomThreshold = bufferZoom;
                }

                var collisionLayer = map.CreateCollisionLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION, mapWidth, mapHeight);
                EditorUtils.AddInverseCircleCollsion(collisionLayer, 20, mapWidth * 0.5f, 5);

                var circleBorderLayer = map.CreateCircleBorderLayer(MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER, mapWidth, mapHeight);
            }
            else if (type == GameMapType.C5)
            {
                //load lod config
                var editorMapData = map.data as EditorMapData;
                editorMapData.mapLODSetting.LoadConfig(MapModule.intuitiveZoomConfigFilePath);

                var data = Map.currentMap.data as EditorMapData;
                int nLODs = editorMapData.lodManager.lodCount;
                if (nLODs > 1)
                {
                    //data.cameraMinHeight = editorMapData.lodManager.GetLOD(0).cameraHeight;
                    //data.cameraMaxHeight = editorMapData.lodManager.GetLOD(nLODs - 1).cameraHeight;
                }

                //create a new map for c5
                var frontLayer = map.CreateEditorGridModelLayer(MapCoreDef.MAP_LAYER_NODE_FRONT, mapWidth, mapHeight, MapModule.defaultFrontTileSize, MapModule.defaultFrontTileSize, GridType.Rectangle);
                frontLayer.asyncLoading = false;

                var terrainLayer = map.CreateBlendTerrainLayer(MapCoreDef.MAP_LAYER_NODE_GROUND, mapWidth, mapHeight, MapModule.defaultGroundTileSize, MapModule.defaultGroundTileSize);

                var navMeshLayer = map.CreateNavMeshLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH, mapWidth, mapHeight);

                //temp code
                var npcRegionLayer = map.CreateNPCRegionLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize);
                npcRegionLayer.active = false;

                var strategy = new GenerateSpawnPointsInGridLayout(new Vector3(4.5f, 0, 3.75f), new Vector2(9, 7.5f), 4.5f, 1.5f, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX);
                var entitySpawnLayer = map.CreateEntitySpawnLayer(MapCoreDef.MAP_LAYER_NODE_ENTITY_SPAWN, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize, new Vector2(MapCoreDef.MAP_NPC_MOVE_RANGE_MIN, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX), MapCoreDef.MAP_NPC_WAYPOINT_COUNT, strategy);
            }
            else if (type == GameMapType.K2)
            {
                //load lod config
                var editorMapData = map.data as EditorMapData;
                editorMapData.mapLODSetting.LoadConfig(MapModule.intuitiveZoomConfigFilePath);

                //create a new map for k2
                var frontLayer = map.CreateEditorGridModelLayer(MapCoreDef.MAP_LAYER_NODE_FRONT, mapWidth, mapHeight, MapModule.defaultFrontTileSize, MapModule.defaultFrontTileSize, GridType.Rectangle);
                frontLayer.asyncLoading = false;

                map.CreateBlendTerrainLayer(MapCoreDef.MAP_LAYER_NODE_GROUND, mapWidth, mapHeight, MapModule.defaultGroundTileSize, MapModule.defaultGroundTileSize);

                map.CreateNavMeshLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH, mapWidth, mapHeight);

                var npcRegionLayer = map.CreateNPCRegionLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize);
                npcRegionLayer.active = false;

                var strategy = new GenerateSpawnPointsRandomly(MapCoreDef.MAP_NPC_DENSITY, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX, 4, 4);
                map.CreateEntitySpawnLayer(MapCoreDef.MAP_LAYER_NODE_ENTITY_SPAWN, mapWidth, mapHeight, MapModule.defaultNPCRegionSize, MapModule.defaultNPCRegionSize, new Vector2(MapCoreDef.MAP_NPC_MOVE_RANGE_MIN, MapCoreDef.MAP_NPC_MOVE_RANGE_MAX), MapCoreDef.MAP_NPC_WAYPOINT_COUNT, strategy);

                var lodLayer = map.CreateLODLayer(MapCoreDef.MAP_LAYER_NODE_LOD, mapWidth, mapHeight);
                lodLayer.GetLayerData().lodConfig.SetLODCount(editorMapData.lodManager.lodCount);

                int nLODs = editorMapData.lodManager.lodCount;
                float lodBufferDistance = 0;
                for (int i = 0; i < nLODs; ++i)
                {
                    float cameraHeightInZoom = Map.currentMap.CalculateCameraHeightFromZoom(i);
                    float cameraHeightInZoomPlus1 = Map.currentMap.CalculateCameraHeightFromZoom(i + 1);
                    float delta = cameraHeightInZoomPlus1 - cameraHeightInZoom;
                    float bufferDistance = Mathf.Min(lodBufferDistance, delta / 3.0f);
                    float zoom = Map.currentMap.CalculateCameraZoom(cameraHeightInZoom + bufferDistance);
                    float bufferZoom = zoom - i;
                    lodLayer.GetLayerData().lodConfig.lodConfigs[i].changeZoomThreshold = bufferZoom;
                }

                map.CreateCollisionLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION, mapWidth, mapHeight);
            }
            mEditor.SaveMap(mEditor.projectFolder);
        }

        [MenuItem("Map Editor/Create Terrain LOD Data")]
        public static void CreateTerrainLODData()
        {
            if (Map.currentMap == null || Map.currentMap.isEditorMode)
            {
                EditorUtility.DisplayDialog("Error", "Can only be used in play mode!", "OK");
                return;
            }

            if (Map.currentMap.varyingTileSizeTerrainLayer != null)
            {
                var dlg = EditorUtils.PopupDialog<VaryingTileSizeTerrainLayerLODCreatorDialog>("Create Terrain LOD Data");
                dlg.minSize = new Vector2(500, 500);
                dlg.maxSize = new Vector2(500, 1000);
                dlg.Show();
            }
            else if (Map.currentMap.activeBlendTerrainLayer != null)
            {
                var dlg = EditorUtils.PopupDialog<BlendTerrainLayerLODCreatorDialog>("Create Terrain LOD Data");
                dlg.minSize = new Vector2(500, 500);
                dlg.maxSize = new Vector2(500, 1000);
                dlg.Show();
            }
        }

        [MenuItem("Map Editor/Utils/Show Used Map Resources")]
        static void ShowUsedMapResources()
        {
            var dlg = EditorWindow.GetWindow<MapResourceWindow>("Show Map Used Resources");
            dlg.minSize = new Vector2(200, 100);
            dlg.maxSize = new Vector2(900, 800);
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            dlg.Show();
        }

        //将多个prefab合并成一个
        //[MenuItem("Map Editor/Combine Prefab")]
        //static void CombinePrefab()
        //{
        //    var dlg = EditorUtils.PopupDialog<CombinePrefabWindow>("Combine Prefabs");
        //    dlg.Show();
        //}

        //[MenuItem("Map Editor/Combine Terrain")]
        //static void CombineTerrain()
        //{
        //    var dlg = EditorUtils.PopupDialog<CombineTerrainPrefabWindow>("Combine Terrain Prefabs");
        //    dlg.Show();
        //}

        [MenuItem("Map Editor/Utils/Combine River")]
        static void Open()
        {
            var dlg = EditorWindow.GetWindow<CombineRiverPrefabWindow>("Combine River Prefab Materials");
            dlg.minSize = new Vector2(500, 300);
            dlg.maxSize = new Vector2(900, 300);
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            dlg.Show();
        }

        [MenuItem("Map Editor/Utils/Create TGA")]
        static void CreateTGA()
        {
            var dlg = EditorWindow.GetWindow<CreateTGAWindow>("Create TGA");
            dlg.minSize = new Vector2(500, 300);
            dlg.maxSize = new Vector2(900, 300);
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
            dlg.Show();
        }

        [MenuItem("Map Editor/Utils/Resize Decoration Prefab")]
        static void ResizeDecorationPrefab()
        {
            var obj = Selection.activeGameObject;
            if (obj != null && PrefabUtility.GetPrefabAssetType(obj) == PrefabAssetType.Regular || PrefabUtility.GetPrefabAssetType(obj) == PrefabAssetType.Variant)
            {
                var dlg = EditorUtils.CreateInputDialog("Move And Resize Prefab");

                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Size", "", "8100"),
                    new InputDialog.EnumItem("Alignment", "", ResizeAlignment.Move),
                    };
                dlg.Show(items, OnClickMoveAndResizePrefab);
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a prefab first!", "OK");
            }
        }

        static bool OnClickMoveAndResizePrefab(List<InputDialog.Item> parameters)
        {
            var mapconfigFilePath = Utils.TryToGetValidConfigFilePath();
            var config = MapConfig.CreateFromFile(mapconfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (config != null)
            {
                string newSizeStr = (parameters[0] as InputDialog.StringItem).text;
                System.Enum alignment = (parameters[1] as InputDialog.EnumItem).value;
                int newSize;
                Utils.ParseInt(newSizeStr, out newSize);
                if (newSize > 0)
                {
                    var obj = Selection.activeGameObject;
                    if (obj != null)
                    {
                        //ResizePrefab.Process(obj, newSize, alignment);
                        return true;
                    }
                }
            }

            return false;
        }

        [MenuItem("Map Editor/Utils/Rotate Mesh UV/Rotate")]
        static void RotateGroundTileMeshUV()
        {
            var mapconfigFilePath = Utils.TryToGetValidConfigFilePath();
            var config = MapConfig.CreateFromFile(mapconfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (config != null)
            {
                RotateMeshUV.RotateAll(config.rotatedUVMeshPath);
            }
        }

        [MenuItem("Map Editor/Utils/Rotate Mesh UV/Recover")]
        static void RecoverGroundTileMeshUV()
        {
            RotateMeshUV.RecoverAll();
        }

        [MenuItem("Map Editor/Bake Skeleton Animation/Open Bake Window")]
        public static void OpenBakeWindow()
        {
            if (!EditorWindow.HasOpenInstances<BakeSkeletonAnimationWindow>())
            {
                var dlg = EditorUtils.PopupDialog<BakeSkeletonAnimationWindow>("Bake Skeleton Animation Prefabs");
                dlg.Show();
            }
        }

        [MenuItem("Map Editor/Bake Skeleton Animation/Optimize Baked Prefabs")]
        static void OptimizeBakedPrefabs()
        {
            var dlg = EditorUtils.PopupDialog<SkeletonAnimBakerOptimizerSettingWindow>("Optimize Baked Prefabs", false);
            dlg.Show();
        }

        [MenuItem("Map Editor/Ground Tile Maker")]
        static void MakeGroundTiles()
        {
            GroundTileMaker.ResetScene();
        }

        [MenuItem("Map Editor/Utils/Create Plane Border")]
        static void CreatePlaneBorder()
        {
            var dlg = EditorWindow.GetWindow<PlaneBorderMakerWindow>("Create Plane Border");
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
        }

        [MenuItem("Map Editor/Utils/Toggle Map Editor Toolbar Visibility")]
        static void ToggleMapEditorToolbarVisibility()
        {
            MapEditorToolbar.showToolbar = !MapEditorToolbar.showToolbar;
        }

        [MenuItem("Map Editor/Object Placement Editor")]
        static void OpenObjectPlacementEditor()
        {
            var dlg = EditorWindow.GetWindow<ObjectPlacementEditor>("Object Placement Editor");
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
        }

        [MenuItem("Map Editor/Map Plugin Manager")]
        static void CreateMapPlugin()
        {
            var dlg = EditorWindow.GetWindow<MapPluginManagerEditor>("Map Plugin Manager");
            var position = dlg.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            dlg.position = position;
        }

        [MenuItem("Map Editor/Start")]
        public static void OpenScene()
        {
            if (!string.IsNullOrEmpty(Utils.TryToGetValidConfigFilePath()))
            {
                StartEditorDialog.Run();
            }
            else
            {
                var dlg = EditorUtils.PopupDialog<StartEditorDialog>("Start Map Editor");
                dlg.minSize = new Vector2(500, 100);
                dlg.maxSize = new Vector2(500, 100);
                dlg.Show();
            }
        }

        SLGMakerEditor mEditor;
        CameraEditor mCameraEditor = new CameraEditor();
    }
}

#endif