﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

 
    /// <summary>
    /// Loading前期的一些固定多语言
    /// 写死一批多语言，只在Loading期间使用，且不会修改
    /// </summary>
    public class Loading_Language
    {

        /// <summary>
        /// 写死的多语言缓存
        /// </summary>
        public static Dictionary<string, Dictionary<string, string>> i18nLocaition;

        /// <summary>
        /// 当前语言
        /// </summary>
        public static string CurrentLanguage;

        /// <summary>
        /// 语种本地存储playerprefs Key
        /// </summary>
        private static readonly string LL_PLAYERPREFS_KEY = "LOCALIZATION_LANGUAGE_PLAYER_SETTING";

        /// <summary>
        /// 设备识别语言
        /// </summary>
        public static Dictionary<int, string> MapLanuage = new Dictionary<int, string>()
        {
            {(int)SystemLanguage.Chinese, "cn"},
            {(int)SystemLanguage.ChineseSimplified, "cn"},
            {(int)SystemLanguage.ChineseTraditional, "zh"},
            {(int)SystemLanguage.English, "en"},
            {(int)SystemLanguage.Arabic, "ar"},
            {(int)SystemLanguage.German, "de"},
            {(int)SystemLanguage.French, "fr"},
            {(int)SystemLanguage.Russian, "ru"},
            {(int)SystemLanguage.Thai, "th"},
            {(int)SystemLanguage.Turkish, "tr"},
            {(int)SystemLanguage.Spanish, "es"},
            {(int)SystemLanguage.Indonesian, "id"},
            {(int)SystemLanguage.Polish, "pl"},
            {(int)SystemLanguage.Vietnamese, "vi"},
            {(int)SystemLanguage.Japanese, "ja"},
            {(int)SystemLanguage.Korean, "kr"},
            {(int)SystemLanguage.Ukrainian, "uk"},
            {(int)SystemLanguage.Italian, "it"},
        };

        public static readonly List<string> Languages = new List<string>()
        {
            "cn", //简体中文
            "en",
            "zh", //繁体中文
            "fr", //法语
            "de", //德语
            "ru", //俄语
            "id", //印尼语
            "ms", //马来语
            "pl", //波兰语
            "th", //泰语
            "vi", //越南语
            "tr", //土耳其语
            "ja", //日本语
            "kr", //韩语
            "ar", //阿拉伯语
            "pt", //葡萄牙语
            "es", //西班牙语
            "gr", //希臘
            "it", //意大利
            "nl", //荷兰语
            "se", //瑞典
            "uk", //乌克兰   24个
        };

        /// <summary>
        /// 初始化
        /// </summary>
        public static void Init()
        {
            string LLDefaultLanage = "en";
            var curLanage = (int)Application.systemLanguage;
            if (MapLanuage.ContainsKey(curLanage))
            {
                LLDefaultLanage = MapLanuage[curLanage];
            }
             

            // 读取本地设置语种
            CurrentLanguage = PlayerPrefs.GetString(LL_PLAYERPREFS_KEY, LLDefaultLanage);

            i18nLocaition = new Dictionary<string, Dictionary<string, string>>();

            i18nLocaition.Add("loading_desc_01", new Dictionary<string, string>());
            
            //key value直接从多语言表粘过来, 注意以前的有些国服中文和国服中文转换前会没有， 一共是24个语言
            i18nLocaition.Add("net_detection_title", new Dictionary<string, string>());
            string[] net_detection_title = new string[]
            { "网络助手" , "Network Assistant", "網路助手", "Assistant Réseau",
                "Netzwerk - Assistent", "Сетевой Помощник", "ASISTEN JARINGAN",
                "Penolong Rangkaian", "Asystent Sieciowy", "ผู้ช่วยเครือข่าย", "Trợ Lý Mạng",  "Ağ Yardımcısı",
                "ネットワークアシスタント", "네트워크 도우미", "مساعد الشبكة", "Asistente de Red",
                "Assistente de Rede",  "Βοηθός Δικτύου", "Assistente di Rete", "Netwerk Assistent",
                "Nätverksassistent", "网络助手" , "网络助手" , "Сітевий Помічник"
            };

            i18nLocaition.Add("net_detection", new Dictionary<string, string>());
            string[] net_detection = new string[]
            { "网络检测中，请稍后...",	"Network testing in progress, please wait...",	
            "網路檢測中，請稍後...",	"Test du réseau en cours, veuillez patienter...",	
            "Netzwerktest wird durchgeführt, bitte warten...",	"Тестирование сети в процессе, пожалуйста, подождите...",
            "Pengecekan jaringan sedang berlangsung, mohon tunggu sebentar...",	"Ujian rangkaian sedang berlangsung, sila tunggu...",
            "Trwa testowanie sieci, proszę czekać...",	"การตรวจสอบเครือข่าย โปรดรอสักครู่...",	"Đang kiểm tra mạng, vui lòng đợi...",
            "Ağ testi devam ediyor, lütfen bekleyin...",	"ネットワーク検出中、お待ちください..." 	,
            "네트워크 검사 중입니다, 잠시만 기다려 주세요...",	"جارٍ اختبار الشبكة، يُرجى الانتظار...",
            "Prueba de red en curso, por favor espera...",	"Teste de rede em andamento, por favor aguarde...",
            "Δοκιμή δικτύου σε εξέλιξη, παρακαλώ περιμένετε...",	"Test della rete in corso, attendi...",
            "Netwerktest in voortgang, even geduld alstublieft...",	"Nätverkstest pågår, vänligen vänta..."	,
            "网络检测中，请稍后...","网络检测中，请稍后...",
            "Тестування мережі в процесі, будь ласка, зачекайте..."
            };

            i18nLocaition.Add("Update_Dynamic_Btn_Quit", new Dictionary<string, string>());
            string[] Update_Dynamic_Btn_Quit = new string[]
            { "退出", "QUIT", "退出", "QUITTER", "VERLASSEN", "ВЫЙТИ", "KELUAR", "KELUAR", "QUIT",
               "ออกจาก", "Rời Khỏi", "ÇIK", "退出", "탈퇴", "خروج ", "SALIR", "SAIR", "ΚΛΕΙΣΙΜΟ", 
                "ESENTATO", "STOP", "AVSLUTA", "退出", "退出", "ВИЙТИ"
            };

            i18nLocaition.Add("Loading_cantload_tips", new Dictionary<string, string>());
            string[] Loading_cantload_tips = new string[]
            { "抱歉，加载游戏资源出错了，需要再次尝试。",	"Sorry, there was an error loading the game resources. Please try again.",
            "抱歉，載入遊戲資源出錯了，需要再次嘗試。",	"Une erreur s'est produite lors du chargement des ressources du jeu. Veuillez réessayer.",	
            "Entschuldigung, es ist ein Fehler beim Landen der Spielressourcen aufgetreten. Bitte versuche es erneut."	,
            "Извините, при загрузке ресурсов игры произошла ошибка. Пожалуйста, повторите попытку."	,
            "Maaf, terjadi kesalahan saat memuat sumber daya game, silakan mencobanya kembali."	,
            "Terdapat sumber loading kesilapan, perlu cuba lagi.",	"Sorry, there was an error loading the game resources. Please try again.",
            "ขออภัย ขณะดาวน์โหลดทรัพยากรเกมเกิดข้อผิดพลาด โปรดลองใหม่อีกครั้ง",	"Rất tiếc, đã xảy ra lỗi khi tải tài nguyên trò chơi, cần phải thử lại.",
            "Üzgünüz, oyun kaynakları yüklenirken bir hata oluştu. Lütfen tekrar dene."	,
            "ゲーム資源のロードでエラーが発生しました。再度お試しください。",	"죄송합니다. 게임 리소스 다운로드 중 오류가 발생했습니다. 다시 시도해야 합니다.",
            "	عذراً، حدث خطأ أثناء تحميل موارد اللعبة. يُرجى إعادة المحاولة.",	"Se produjo un error al cargar los recursos del juego. Vuelve a intentarlo.	",
            "Desculpe, ocorreu um erro ao carregar os recursos do jogo. Tente novamente.",	"Λυπάμαι, υπήρξε σφάλμα κατά τη φόρτωση των πόρων του παιχνιδιού. Παρακαλώ δοκιμάστε ξανά.",
            "Spiacenti, si è verificato un errore durante il caricamento delle risorse del gioco. Per favore riprova.",
            "Sorry, er is een fout opgetreden bij het laden van de spelbronnen. Probeer het alstublieft opnieuw.",
            "Tyvärr uppstod ett fel vid laddning av spelresurserna. Vänligen försök igen.", "抱歉，加载游戏资源出错了，需要再次尝试。",
            "抱歉，加载游戏资源出错了，需要再次尝试",	"Вибачте, при завантаженні ресурсів гри виникла помилка. Будь ласка, спробуйте ще раз."
            };

            i18nLocaition.Add("MENU_ok_cap", new Dictionary<string, string>());
            string[] MENU_ok_cap = new string[]
            {
                "好的",    "OK",  "好的",  "OK" , "OK"  ,"ОК" , "OKE", "OK",    "OK",  "โอเค", 
                "OK", "TAMAM",  "わかりました", "좋아!" ,"موافق ",  "OK" ,
                "OK" , "Εντάξει", "OK" , "OK", "OK" , "好的" ,"好的", "ОК"
            };

            i18nLocaition.Add("PLAYER_settings_tips", new Dictionary<string, string>());
            string[] PLAYER_settings_tips = new string[]
            { "提示" ,   "NOTE" ,   "提示",  "NOTE",    "ANMERKUNG" ,  "ПРИМЕЧАНИЯ"  ,"TIPS"  ,  "NOTIS"  , "NOTE" ,  "แจ้งเตือน",
            "Nhắc Nhở" ,   "NOT", "ヒント" ,"안내" , "ملاحظة ", "NOTA" ,  "OBSERVAÇÃO"  ,"ΣΗΜΕΙΩΣΗ"  ,  "NOTA",    "OPMERKING",
            "NOTERA",  "提示" , "提示",  "ПРИМІТКИ"
            };

            i18nLocaition.Add("Context_1", new Dictionary<string, string>());
            string[] Context_1 = new string[]
            { "当前版本过低，请升级到最新版本。",	"Version is out of date. Please upgrade to the latest version."  ,
            "當前版本過低，請升級到最新版本。",	"Votre version n'est pas à jour. Veuillez installer la dernière version."	,
            "Version ist veraltet. Bitte auf die neueste Version aktualisieren.	", "Версия устарела. Пожалуйста, обновите до последней версии."	,
            "Versi game terlalu rendah, silakan update ke versi terbaru.",	"Sila naik taraf kepada versi terkini untuk teruskan.",	
            "Version is out of date. Please upgrade to the latest version.",	"เวอร์ชันปัจจุบันต่ำเกินไป โปรดอัปเกรดเป็นเวอร์ชันล่าสุด"	,
            "Phiên bản hiện tại quá thấp, vui lòng update lên phiên bản mới nhất.",	"Sürüm güncel değil. Lütfen son sürüme yükselt.",
            "現在のバージョンが古すぎます。最新バージョンにアップデートしてください。"	, "현재 버전이 너무 낮습니다. 최신 버전으로 업그레이드해 주세요.",
            "الإصدار قديم. يرجى الترقية إلى أحدث إصدار.",	"Versión desactualizada. Actualiza a la última versión."	,
            "A versão está desatualizada. Faça upgrade para a versão mais recente.",	"Η έκδοση είναι παλιά. Παρακαλώ αναβαθμίστε στην τελευταία έκδοση."	,
            "La versione è obsoleta. Si prega di aggiornare all'ultima versione.", "De versie is verouderd. Update alstublieft naar de nieuwste versie.",
            "Versionen är föråldrad. Vänligen uppgradera till den senaste versionen.", "当前版本过低，请升级到最新版本。"	,
            "当前版本过低，请升级到最新版本。",	"Версія застаріла. Будь ласка, оновіть до останньої версії."
            };

            i18nLocaition.Add("Update_Dynamic_Title", new Dictionary<string, string>());
            string[] Update_Dynamic_Title = new string[]
            { "更新提示",  "Update Notification", "更新提示" ,   "Notification de mise à jour", "Update-Hinweis" , "Уведомление об обновлении",
            "Tips Update", "Pemberitahuan Kemaskinian",   "Update Notification" ,"แจ้งเตือนอัปเดต" ,"Nhắc Nhở Update" ,"Güncelleme Bildirimi" ,   
            "更新通知",    "업데이트 안내" , "إشعار التحديث " , "Notificación de actualización" ,  "Notificação de atualização" ,
            "Ειδοποίηση Ενημέρωσης" ,  "Notifica di aggiornamento",   "Bijwerkingsmelding",  "Uppdateringsmeddelande",  "更新提示",
            "更新提示",    "Повідомлення про оновлення"
            };

            i18nLocaition.Add("Update_Dynamic_Desc", new Dictionary<string, string>());
            string[] Update_Dynamic_Desc = new string[]
            {  "目前游戏版本需要更新，本次更新大小约{0}M，请按更新按钮即可立即开始下载。",
                "Your game needs to be updated to the latest version. The update is around {0}MB. Tap the 'Update' button to begin the download.",
                "目前遊戲版本需要更新，本次更新大小約{0}M，請按更新按鈕即可立即開始下載。",
                "La nouvelle version du jeu doit être installée. Taille approximative de la mise à jour : {0}Mo.Appuyez sur le bouton 'Mise à jour' pour lancer le téléchargement.",
                "Dein Spiel muss auf die neueste Version aktualisiert werden.Das Update ist zirka {0}MB groß. Tippe auf die Schaltfläche „Update“, um den Download zu starten.",
                "Обновите игру до последней версии.Размер обновления составляет около {0}МБ.Нажмите кнопку «Обновить», чтобы начать загрузку.",
                "Versi game harus diupdate, update kali ini membutuhkan sekitar {0}MB.Silakan ketuk tombol UPDATE untuk mulai mengunduh.",
                "Versi semasa permainan ini perlu dikemas kini.Saiz kemaskinian ialah {0}M.Sila tekan butang Kemas Kini untuk mula memuat turun versi baharu dengan serta merta.",
                "Your game needs to be updated to the latest version. The update is around {0} MB. Tap the 'Update' button to begin the download.",
                "เวอร์ชันเกมปัจจุบันต้องการอัปเดต จนาดอัปเดตครั้งนี้ประมาณ {0}M กรุณาแตะปุ่มอัปเดตเพื่อเริ่มดาวน์โหลดทันที",
                "Hiện tại phiên bản game cần update, độ lớn update lần này khoảng {0}MB, vui lòng nhấn nút Update để bắt đầu tải ngay."    ,
                "Oyununun son sürüme güncellenmesi gerekiyor.Güncelleme yaklaşık {0}MB boyutunda. İndirmeye başlamak için 'Güncelle' düğmesine dokun." ,
                "バージョンアップをデートする必要があり、今回のアップデートは約{0}Mです。アップデートボタンをタップしてダウンロード開始。"   ,
                "게임 버전을 업데이트해야 합니다. 약 {0}분이 소요되며, 업데이트 버튼 터치 즉시 다운로드가 시작됩니다" ,
                ".يتعين تحديث لعبتك إلى آخر إصدار. حجم التحديث حوالي {0}ميغابايت.انقر فوق زر تحديث لبدء التنزيل.	",
                "Necesitas actualizar el juego a su última versión. Esta actualización pesa unos {0}MB.Toca el botón 'Actualizar' para iniciar la descarga.",
                "Seu jogo precisa ser atualizado para a versão mais recente. A atualização tem em torno de {0}MB.Toque no botão para começar a baixar.",
                "Το παιχνίδι σας πρέπει να ενημερωθεί στην τελευταία έκδοση.Η ενημέρωση είναι περίπου {0}MB.Πατήστε το κουμπί 'Ενημέρωση' για να ξεκινήσει η λήψη.",
                "Il tuo gioco deve essere aggiornato all'ultima versione. L'aggiornamento è di circa {0}MB.Tocca il pulsante \"Aggiorna\" per iniziare il download."   ,
                "Je spel moet worden bijgewerkt naar de nieuwste versie.De update is ongeveer {0} MB. Tik op de 'Bijwerken' knop om de download te starten.",
                "Ditt spel behöver uppdateras till den senaste versionen. Uppdateringen är ungefär {0}MB.Tryck på 'Uppdatera' - knappen för att starta nedladdningen." ,
                "目前游戏版本需要更新，本次更新大小约{0}M，请按更新按钮即可立即开始下载。",
                "目前游戏版本需要更新，本次更新大小约{0}M，请按更新按钮即可立即开始下载。",
                "Оновіть гру до останньої версії.Розмір оновлення складає близько {0}МБ.Натисніть кнопку «Оновити», щоб розпочати завантаження."
            };

            i18nLocaition.Add("Update_Dynamic_Desc1", new Dictionary<string, string>());
            string[] Update_Dynamic_Desc1 = new string[] 
            {
                "目前游戏版本需要更新，请按更新按钮即可立即开始下载。",
            "The current game version needs to be updated, please press the update button to start downloading immediately.",
            "目前遊戲版本需要更新，請按更新按鈕即可立即開始下載。",
            "La version actuelle du jeu doit être mise à jour, veuillez appuyer sur le bouton de mise à jour pour commencer le téléchargement immédiatement.",
            "Die aktuelle Spielversion muss aktualisiert werden, bitte drücke die Update-Schaltfläche, um sofort mit dem Herunterladen zu beginnen.",
            "Текущая версия игры требует обновления, пожалуйста, нажмите кнопку обновления, чтобы начать загрузку немедленно.",
            "Versi game saat ini memerlukan update, silakan tekan tombol update untuk segera memulai unduhan.",
            "Versi semasa permainan ini perlu dikemas kini, sila tekan butang kemas kini untuk mula memuat turun dengan serta merta.",
            "Aktualna wersja gry wymaga aktualizacji, proszę nacisnąć przycisk aktualizacji, aby natychmiast rozpocząć pobieranie.",
            "เวอร์ชันเกมปัจจุบันต้องการอัปเดต โปรดกดปุ่มอัปเดตเพื่อเริ่มดาวน์โหลดทันที",
            "Phiên bản game hiện tại cần được cập nhật, vui lòng nhấn nút cập nhật để bắt đầu tải xuống ngay lập tức.",
            "Mevcut oyun sürümü güncellenmeli, lütfen indirmeye hemen başlamak için güncelleme düğmesine basın.",
            "現在のゲームバージョンを更新する必要があります。更新ボタンを押すとすぐにダウンロードが開始されます。",
            "현재 게임 버전을 업데이트해야 합니다. 업데이트 버튼을 누르면 즉시 다운로드가 시작됩니다.",
            "يتعين تحديث إصدار اللعبة الحالي، يرجى الضغط على زر التحديث لبدء التنزيل فورًا.",
            "目前遊戲版本需要更新，請按更新按鈕即可立即開始下載。",
            "目前遊戲版本需要更新，請按更新按鈕即可立即開始下載。",
            "La versión actual del juego necesita ser actualizada, por favor presiona el botón de actualización para comenzar la descarga inmediatamente.",
                "A versão atual do jogo precisa ser atualizada, por favor pressione o botão de atualizar para começar a baixar imediatamente.",
                "Η τρέχουσα έκδοση του παιχνιδιού πρέπει να ενημερωθεί, παρακαλώ πατήστε το κουμπί ενημέρωσης για να ξεκινήσετε τη λήψη αμέσως.",
                "La versione attuale del gioco deve essere aggiornata, si prega di premere il pulsante di aggiornamento per iniziare immediatamente il download.",
                "De huidige spelversie moet worden bijgewerkt, druk alstublieft op de bijwerken knop om direct met downloaden te beginnen.",
                "Den nuvarande spelversionen behöver uppdateras, vänligen tryck på uppdateringsknappen för att starta nedladdningen omedelbart.",
                "Поточна версія гри вимагає оновлення, будь ласка, натисніть кнопку оновлення, щоб розпочати завантаження негайно.","","","","","","","","","","","","","","",
                	  	                        	        			
            };

            i18nLocaition.Add("System_Update_Notice_Button", new Dictionary<string, string>());
            string[] System_Update_Notice_Button = new string[]
            { "更新" ,   "UPDATE",  "更新",  "METTRE À JOUR",   "UPDATE" , "ОБНОВИТЬ" ,   "UPDATE",  "KEMAS KINI" , "UPDATE",  "อัปเดต" , "Update" ,
            "GÜNCELLE",    "更新"  ,"업데이트" ,   "تحديث " , "ACTUALIZAR" , "ATUALIZAR" , "ΕΝΗΜΕΡΩΣΗ",   "AGGIORNAMENTO"  , "BIJWERKEN"   ,
            "UPPDATERA" ,  "更新",  "更新" , "ОБНОВИТИ"
            };

            i18nLocaition.Add("MENU_server_disconnected_title", new Dictionary<string, string>());
            string[] MENU_server_disconnected_title = new string[]
            { "网络连接断开" ,   "NETWORK DISCONNECTED",    "網路連接斷開" , "RÉSEAU DÉCONNECTÉ",   "NETZWERK GETRENNT" ,  "ПОДКЛЮЧЕНИЕ К СЕТИ ПОТЕРЯНО",
            "KONEKSI JARINGAN TERPUTUS" ,  "RANGKAIAN TERPUTUS SAMBUNGAN"  ,  "NETWORK DISCONNECTED" ,   "ถูกตัดการเชื่อมต่อจากอินเทอร์เน็ต",   "Kết nối mạng đã ngắt",
            "AĞ BAĞLANTISI KESİLDİ" ,  "ネットワークへの接続遮断"  ,  "네트워크 연결 끊김" , "تم فصل الشبكة ", "RED DESCONECTADA" ,   "REDE DESCONECTADA",
            "ΤΟ ΔΙΚΤΥΟ ΑΠΟΣΥΝΔΕΘΗΚΕ"  ,"RETE DISCONNESSA" ,   "NETWERK VERBROKEN" ,  "NÄTVERKET ÄR URKOPPLAT",  "网络连接断开",
            "网络连接断开"  ,"ПІДКЛЮЧЕННЯ ДО МЕРЕЖІ ВТРАЧЕНО"
            };

            i18nLocaition.Add("loading_desc_03", new Dictionary<string, string>());
            string[] loading_desc_03 = new string[]
            {"更新中...{0}", "Updating... {0}","更新中...{0}","Mise à jour... {0}","Aktualisierung läuft... {0}",
                "Обновление... {0}","Sedang UPDATE...{0}","Mengemas kini... {0}","Aktualizowanie... {0}","อัปเดต...{0}",
                "Đang cập nhật...{0}","Güncelleniyor... {0}","更新中...{0}","업데이트 중...{0}","جار التحديث... {0}",
                "Actualizando... {0}","Atualizando... {0}","Ενημερώνεται... {0}","Aggiornamento in corso... {0}",
                "Bijwerken... {0}","Uppdaterar... {0}", "更新中...{0}","更新中...{0}","Оновлення... {0}"

                };

            i18nLocaition.Add("ERRCODE_ServerErr", new Dictionary<string, string>());
            string[] ERRCODE_ServerErr = new string[]
            { "无法连接？<color=#56CD25>联系我们</color>。", 
               "Connection Issues? <color=#56CD25> Contact Us</color>."  ,
            "無法連接？<color=#56CD25>聯繫我們</color>。",
            "VOUS N'ARRIVEZ PAS À VOUS CONNECTER? <color=#56CD25>CONTACTEZ-NOUS</color>." ,
            "KEINE VERBINDUNG? <color=#56CD25>KONTAKTIERE UNS</color>." ,
            "НЕ ПОЛУЧАЕТСЯ ВОЙТИ? <color=#56CD25>СВЯЖИТЕСЬ С НАМИ!</color>." ,
            "KONEKSI BERMASALAH? <color=#56CD25>HUBUNGI KAMI</color>." ,
            "TIDAK DAPAT MENYAMBUNG? SILA <color=#56CD25>HUBUNGI KAMI</color>." ,
            "Connection Issues? <color=#56CD25> Contact Us</color>." ,
            "เชื่อมต่อไม่สำเร็จ <color=#56CD25>ติดต่อเรา</color>" ,
            "Không thể kết nối? <color=#56CD25>Liên hệ chúng tôi</color>." ,
            "BAĞLANAMIYOR MUSUN? <color=#56CD25>BİZE ULAŞ</color>." ,
            "接続できませんか？<color=#56CD25>お問い合わせください</color>。" ,
            "연결할 수 없나요?<color=#56CD25>우리에게 연락해주세요</color>." ,
            "هل توجد مشاكل في الاتصال؟ <color=#56CD25> تواصل معنا</color>." ,
            "¿Problemas de conexión? <color=#56CD25>Comunícate con nosotros</color>." ,
            "Problemas de conexão? <color=#56CD25> Entre em contato conosco</color>." ,
            "Προβλήματα σύνδεσης; <color=#56CD25> Επικοινωνήστε μαζί μας</color>." ,
            "Problemi di connessione? <color=#56CD25> Contattaci</color>." ,
            "Verbindingsproblemen? <color=#56CD25> Neem Contact Met Ons Op</color>." ,
            "Problem med anslutningen? <color=#56CD25> Kontakta oss</color>." ,
            "无法连接？<color=#56CD25>联系我们</color>。" ,
            "无法连接？<color=#56CD25>联系我们</color>。" ,
            "Проблеми з підключенням? <color=#56CD25> Зв'яжіться з нами</color>."
            };


            for (int i = 0; i < Languages.Count; i++)
            {
                i18nLocaition["loading_desc_01"].Add(Languages[i], ". . .");
                i18nLocaition["net_detection_title"].Add(Languages[i], net_detection_title[i]);
                i18nLocaition["net_detection"].Add(Languages[i], net_detection[i]);
                i18nLocaition["Update_Dynamic_Btn_Quit"].Add(Languages[i], Update_Dynamic_Btn_Quit[i]);
                i18nLocaition["Loading_cantload_tips"].Add(Languages[i], Loading_cantload_tips[i]);
                i18nLocaition["MENU_ok_cap"].Add(Languages[i], MENU_ok_cap[i]);
                i18nLocaition["PLAYER_settings_tips"].Add(Languages[i], PLAYER_settings_tips[i]);
                i18nLocaition["Context_1"].Add(Languages[i], Context_1[i]);
                i18nLocaition["Update_Dynamic_Title"].Add(Languages[i], Update_Dynamic_Title[i]);
                i18nLocaition["Update_Dynamic_Desc1"].Add(Languages[i], Update_Dynamic_Desc1[i]);
                i18nLocaition["Update_Dynamic_Desc"].Add(Languages[i], Update_Dynamic_Desc[i]);
                i18nLocaition["System_Update_Notice_Button"].Add(Languages[i], System_Update_Notice_Button[i]);
                i18nLocaition["MENU_server_disconnected_title"].Add(Languages[i], MENU_server_disconnected_title[i]);
                i18nLocaition["loading_desc_03"].Add(Languages[i], loading_desc_03[i]);
                i18nLocaition["ERRCODE_ServerErr"].Add(Languages[i], ERRCODE_ServerErr[i]);
            }


        }

        /// <summary>
        /// 获取loading语言
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string GetLoadingI18N(string key)
        {
            var result = string.Empty;
            if (i18nLocaition == null)
                return result;

            if (i18nLocaition.TryGetValue(key, out var dic))
                result = dic[CurrentLanguage];

            return result;
        }
    }
 
