﻿ 



 
 


//#define USE_CULL

using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public sealed class RuntimeDecorationBorderLayerData : MapLayerData
    {
        public RuntimeDecorationBorderLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, Rect realLayerBounds, bool[] tileIsNotEmpty, bool asyncLoading)
            : base(header, config, map)
        {
            mLastViewport = new Rect(-10000, -10000, 0, 0);
            mTileObjectIDs = new List<int[]>[mRows * mCols];
            mBigTileDatas = new ComplexGridBigTileData2[mRows * mCols];

            mRealLayerBounds = realLayerBounds;

#if UNITY_EDITOR
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
#endif
            Debug.LogError(map.dataFolder);
            string dataPath = MapCoreDef.GetMapBorderBigTileDataPath(map.dataFolder);
            if (MapModuleResourceMgr.Exists(dataPath))
            {
                LoadAllTiles(dataPath, asyncLoading);
            }
            else
            {
                Debug.Assert(false);
            }
#if UNITY_EDITOR
            var time = w.Stop();
            Debug.Log("load tile cost: " + time.ToString());
#endif

            mDontUpdateTileBigObjectCulling = new bool[mRows, mCols];
            LoadPrefabInfos();

#if UNITY_EDITOR && TFW_MAP_DEBUG
            mGridViewer = new GridViewer("Tile Grid Object Layer 2 Grids", 0, 0, header.cols, header.rows, header.tileWidth * header.cols, header.tileHeight * header.rows, header.tileWidth, header.tileHeight, new Color(0, 1, 0, 0.2f), new Color(1, 0, 0, 0.2f), true, 0.3f, map.root, false);
            mGridViewer.SetVisible(false);
#endif
        }

        #region hide
        //清理地图数据
        public override void OnDestroy()
        {
            foreach (var obj in mObjects)
            {
                map.DestroyObject(obj.Value.GetEntityID());
            }
            mObjects = null;

            foreach (var p in mTileChildren)
            {
                var list = p.Value;
                for (int i = 0; i < list.Count; ++i)
                {
                    mPool.Release(list[i]);
                }
            }

            mPool.OnDestroy();
            mPool = null;
            mTileChildren = null;
        }

        //get一个地图对象的数据
        public TileObjectData2 GetObjectData(int objectID)
        {
            TileObjectData2 val;
            mObjects.TryGetValue(objectID, out val);
            return val;
        }
        #endregion

        public void SetObjectActiveOnly(TileObjectData2 data, bool active, int lod)
        {
            bool change = SetObjectActiveFromAction(data, active, lod);

            if (change)
            {
                if (active)
                {
                    mVisibleTileChildren[data.GetEntityID()] = data as TileObjectData2;
                }
                else
                {
                    mVisibleTileChildren.Remove(data.GetEntityID());
                }
            }
        }

        public void SetObjectScaleChangeCallback(System.Action<TileObjectData2> onObjectScaleChangeCallback)
        {
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
        }

        public void SetObjectActiveStateChangeCallback(System.Action<TileObjectData2> onObjectActiveStateChangeCallback)
        {
            mOnActiveStateChangeCallback = onObjectActiveStateChangeCallback;
        }

        public bool SetObjectActiveFromAction(TileObjectData2 objectData, bool active, int lod)
        {
            bool changed = objectData.SetObjActive(active);
            if (changed)
            {
                mOnActiveStateChangeCallback(objectData);
            }
            return changed;
        }

        //设置big tile的可见性
        public void SetObjectActiveTop(bool active, bool changeLODInCurrentFrame, float updateZoom, int x, int y)
        {
            int lastLOD = mCurrentLOD;
            if (mIsLODChanged)
            {
                lastLOD = mLastLOD;
            }

            if (active)
            {
                SetObjectActiveFromAction(lastLOD, mCurrentLOD, true, x, y);

#if false
                var addAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, true, FrameActionType.ShowFrontTile2, false, changeLODInCurrentFrame, x, y);
                parentAction.AddChildAction(addAction);
#region hide
                //将立即执行的action独立成一个单独的action
                if (mIsLODChanged)
                {
                    if (changeLODInCurrentFrame)
                    {
                        var addNowAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, true, FrameActionType.ShowFrontTile2, true, true, x, y);
                        while (true)
                        {
                            bool finished = addNowAction.Do();
                            if (finished)
                            {
                                addNowAction.OnDestroy();
                                break;
                            }
                        }
                    }
                }
#endregion
#endif
            }
            else
            {
                SetObjectActiveFromAction(lastLOD, mCurrentLOD, false, x, y);
                //                var action = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, false, FrameActionType.HideFrontTile2, false, changeLODInCurrentFrame, x, y);
                //                parentAction.AddChildAction(action);
                //#region hide
                //                //将立即执行的action独立成一个单独的action
                //                if (mIsLODChanged)
                //                {
                //                    if (changeLODInCurrentFrame)
                //                    {
                //                        var removeNowAction = FrameActionSetFrontTileVisibility2.Require(this, lastLOD, mCurrentLOD, false, FrameActionType.HideFrontTile2, true, true, x, y);
                //                        while (true)
                //                        {
                //                            bool finished = removeNowAction.Do();
                //                            if (finished)
                //                            {
                //                                removeNowAction.OnDestroy();
                //                                break;
                //                            }
                //                        }
                //                    }
                //                }
                //#endregion
            }
        }

        //给frame action使用
        //instant: 是否立即在当前帧切换lod
        public void SetObjectActiveFromAction(int oldLOD, int newLOD, bool active, int x, int y)
        {
            int idx = y * mCols + x;

            if (active)
            {
                AddChildren(oldLOD, newLOD, x, y, mBigTileDatas[idx]);
            }
            else
            {
                RemoveChildren(oldLOD, newLOD, x, y, mBigTileDatas[idx]);
            }
        }

#region hide
        int GetCurrentLOD()
        {
            if (mCurrentLOD < 0)
            {
                return 0;
            }
            return mCurrentLOD;
        }

        /*
         *  2 3
         *  0 1
         */
        int GetSubRegionIndex(float x, float z)
        {
            float centerX = tileWidth * 0.5f;
            float centerZ = tileHeight * 0.5f;
            if (x < centerX && z < centerZ)
            {
                return 0;
            }
            if (x >= centerX && z < centerZ)
            {
                return 1;
            }
            if (x < centerX && z >= centerZ)
            {
                return 2;
            }
            if (x >= centerX && z >= centerZ)
            {
                return 3;
            }
#if UNITY_EDITOR
            Debug.Log("invalid subregion");
#endif
            return 0;
        }

        //返回lodlevel下,第tileIndex个tile下的第index个物体的id
        public int GetObjectID(int lodLevel, ComplexGridBigTileData2 tileData, int tileIndex, int index)
        {
            if (lodLevel < 0)
            {
                lodLevel = 0;
            }

            List<int[]> lodIDs = mTileObjectIDs[tileIndex];
            if (lodIDs == null)
            {
                lodIDs = new List<int[]>();
                mTileObjectIDs[tileIndex] = lodIDs;
                int nLODs = tileData.objectsOfEachLOD.Count;
                
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabs = tileData.objectsOfEachLOD[lod];
                    int n = childPrefabs.Count;
                    int[] ids = new int[n];
                    lodIDs.Add(ids);
                    for (int i = 0; i < n; ++i)
                    {
                        ids[i] = map.nextCustomObjectID;
                    }
                }
            }

            if (lodLevel >= lodIDs.Count || index >= lodIDs[lodLevel].Length)
            {
                return 0;
            }
            return lodIDs[lodLevel][index];
        }

        public Vector2Int GetObjectCoordinate(TileObjectData2 data)
        {
            var pos = data.GetPosition();
            return FromWorldPositionToCoordinate(pos);
        }

        public bool IsInViewRange(TileObjectData2 data, Rect viewport)
        {
            var coord = GetObjectCoordinate(data);
            var rect = GetViewRect(viewport);
            return rect.Contains(new Vector2Int(coord.x, coord.y));
        }

        //地图对象是否在视野中
        public bool IsInViewRange(TileObjectData2 data)
        {
            return IsInViewRange(data, map.viewport);
        }

        public override void RefreshObjectsInViewport()
        {
            Debug.Assert(false, "can't be here");
        }

        public RectInt GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new RectInt(startCoord.x, startCoord.y, endCoord.x - startCoord.x, endCoord.y - startCoord.y);
        }
#endregion

        public override bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (map.enableLOD)
            {
                lodChanged = base.SetZoom(zoom);
            }

            return lodChanged;
        }

        public bool SetZoomFromAction(float oldZoom, Rect oldViewport, float newZoom, Rect newViewport)
        {
            bool lodChanged = base.SetZoom(newZoom);

            if (lodChanged)
            {
                var oldRect = GetViewRect(oldViewport);
                var oldMin = oldRect.min;
                var oldMax = oldRect.max;
                var oldMinX = oldMin.x;
                var oldMinY = oldMin.y;
                var oldMaxX = oldMax.x;
                var oldMaxY = oldMax.y;

                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                        {
                            int idx = i * mCols + j;
                            if (mBigTileDatas[idx] != null)
                            {
                                mIsLODChanged = true;
                                SetObjectActiveTop(false, true, oldZoom, j, i);
                                mIsLODChanged = false;
                            }
                        }
                    }
                }

                var newRect = GetViewRect(newViewport);
                var newMin = newRect.min;
                var newMax = newRect.max;
                var newMinX = newMin.x;
                var newMinY = newMin.y;
                var newMaxX = newMax.x;
                var newMaxY = newMax.y;

                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                        {
                            int idx = i * mCols + j;
                            if (mBigTileDatas[idx] != null)
                            {
                                mIsLODChanged = true;
                                SetObjectActiveTop(true, true, newZoom, j, i);
                                mIsLODChanged = false;
                            }
                        }
                    }
                }
            }

            mLastCameraZoom = newZoom;

            return lodChanged;
        }

        void RemoveChildren(int currentLOD, int nextLOD, int tileX, int tileY, ComplexGridBigTileData2 tileData)
        {
            var tileIndex = tileY * mCols + tileX;

            if (mTileChildren != null)
            {
                //取消删除任务
                List<TileObjectData2> objectsInThisTile;
                bool found = mTileChildren.TryGetValue(tileIndex, out objectsInThisTile);
                if (found)
                {
                    for (int i = objectsInThisTile.Count - 1; i >= 0; --i)
                    {
                        var tileObject = objectsInThisTile[i];
                        if (tileObject.lod == currentLOD)
                        {
                            SetObjectActiveOnly(tileObject, false, tileObject.lod);
                            mPool.Release(tileObject);
                            RemoveTileObject(tileObject);
                        }

                        //if (instant)
                        //{
                        //    if (objectsInThisTile[i].objectType == TileObjectType.BigObject && objectsInThisTile[i].lod == currentLOD)
                        //    {
                        //        var action = FrameActionRemoveTileObject2.Require(null, this, objectsInThisTile[i]);
                        //        mInstantActions.Add(action);
                        //    }
                        //}
                        //else
                        //{
                        //    if (!hasInstantAction || objectsInThisTile[i].objectType != TileObjectType.BigObject)
                        //    {
                        //        if (objectsInThisTile[i].lod == currentLOD)
                        //        {
                        //            FrameActionRemoveTileObject2.Require(parentAction, this, objectsInThisTile[i]);
                        //        }
                        //    }
                        //}
                    }

                    //for (int i = 0; i < mInstantActions.Count; ++i)
                    //{
                    //    //++InstantActionCounter.removeCount;
                    //    mInstantActions[i].Do();
                    //    mInstantActions[i].OnDestroy();
                    //}
                    //mInstantActions.Clear();
                }
            }

            //删除这个tile的update action
            //if (instant == false)
            //{
            //    var key = FrameActionUpdateTileObjects2.MakeActionKey(tileIndex, currentLOD, FrameActionType.UpdateTileObjects2);
            //    bool removed = map.RemoveFrameAction(mUpdateActionQueueIndex, key);
            //}
        }

        //管理Tile的子节点
        void AddChildren(int lastLOD, int newLOD, int tileX, int tileY, ComplexGridBigTileData2 bigTileData)
        {
            var childrenModelTemplates = bigTileData.objectsOfEachLOD[newLOD];
            int n = childrenModelTemplates.Count;

            var tilePos = FromCoordinateToWorldPosition(tileX, tileY);
            int tileIndex = tileY * mCols + tileX;
            for (int i = 0; i < n; ++i)
            {
                var objectID = GetObjectID(newLOD, bigTileData, tileIndex, i);
                //必须是没有加载的物体才加载
                if (mActiveTileChildren.ContainsKey(objectID) == false)
                {
                    var prefabInfo = mPrefabInfos[childrenModelTemplates[i].prefabInitInfoIndex];
                    //检查quality
                    
                        AddTileObject(tilePos, childrenModelTemplates[i], prefabInfo.prefabPathForEachCustomType[0], objectID, i, newLOD);
                        //if (instantAction)
                        //{
                        //    if (childrenModelTemplates[i].objectType == TileObjectType.BigObject)
                        //    {
                        //        //++InstantActionCounter.addCount;
                        //        //如果这个tile object和上一个lod的tile object是相同位置并且是标记成了立即切换的object的,则直接显示,避免闪烁问题
                        //        action.Do();
                        //        action.OnDestroy();
                        //    }
                        //}
                        //else
                        //{
                        //    if (!hasInstantAction || childrenModelTemplates[i].objectType != TileObjectType.BigObject)
                        //    {
                        //        //在后续帧切换lod
                        //        mSortedActions.Add(action);
                        //    }
                        //}
                    
                }
            }

            //temp code
            //if (instantCount != 0)
            //{
            //    Debug.Log("instant action count: " + instantCount);
            //}
            //parentAction.AddChildActions(mSortedActions);
            //mSortedActions.Clear();

            //if (instantAction == false && n > 0)
            //{
            //    //增加更新新object的命令
            //    if (!mDontUpdateTileBigObjectCulling[tileY, tileX])
            //    {
            //        var updateAction = FrameActionUpdateTileObjects2.Require(this, bigTileData, newLOD, tileX, tileY);
            //        map.AddFrameAction(mUpdateActionQueueIndex, updateAction, false);
            //    }
            //}
        }

        void AddTileObject(Vector3 bigTilePos, BigTileChildPrefabData2 prefabData, string prefabPath, int objectID, int idx, int lod)
        {
            //var viewport = map.viewport;

            var coord = FromWorldPositionToCoordinate(bigTilePos);
            int tileIndex = coord.y * horizontalTileCount + coord.x;
            //var bigTileData = GetBigTileData(coord.x, coord.y);

            var offset = new Vector2(bigTilePos.x, bigTilePos.z);

            var bounds = prefabData.GetLocalBoundsInPrefab(mPrefabInfos);
            var rectMin = bounds.min + offset;
            var rectMax = bounds.max + offset;

            var pos = prefabData.GetPosition(mPrefabInfos) + bigTilePos;

            //bool alwaysVisible = prefabData.objectType == TileObjectType.AlwaysVisible;

            bool isVisible = true;
            //if (alwaysVisible)
            //{
            //    isVisible = true;
            //}
            //else
            //{
            //    if (rectMin.x > viewport.xMax || rectMin.y > viewport.yMax ||
            //        viewport.xMin > rectMax.x || viewport.yMin > rectMax.y)
            //    {
            //        isVisible = false;
            //    }
            //}

            var worldBounds = new Rect();
            worldBounds.Set(rectMin.x, rectMin.y, rectMax.x - rectMin.x, rectMax.y - rectMin.y);
            var prefabInfo = prefabInfos[prefabData.prefabInitInfoIndex];

            var tile = mPool.Require(objectID, map, prefabData, prefabPath, worldBounds, pos, prefabData.GetScale(mPrefabInfos), prefabData.GetRotation(mPrefabInfos), idx, tileIndex, lod, 0, prefabInfo, mOnObjectScaleChangeCallback, false, null);

            SetObjectActiveOnly(tile, isVisible, lod);
            AddTileObject(tile);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            if (!mInited)
            {
                return false;
            }

            bool zoomChanged = !Mathf.Approximately(newZoom, mLastCameraZoom);
            bool viewportChanged = (newViewport != mLastViewport);
            if (zoomChanged || viewportChanged)
            {
                //var action = FrameActionUpdateFrontLayerViewport2.Require(this, mActionTimeStamp, mLastViewport, mLastCameraZoom, newViewport, newZoom);
                bool lodChanged = false;
                //先根据高度更新lod
                if (zoomChanged)
                {
                    lodChanged = SetZoomFromAction(mLastCameraZoom, mLastViewport, newZoom, newViewport);
                }

                if (viewportChanged)
                {
                    UpdateViewportFromAction(lodChanged, mLastViewport, newViewport);
                }
            }

            return false;
        }

        public void UpdateViewportFromAction(bool lodChanged, Rect oldViewport, Rect newViewport)
        {
            if (!lodChanged)
            {
                var oldViewRect = GetViewRect(oldViewport);
                var newViewRect = GetViewRect(newViewport);

                UpdateViewRect(oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;
        }

        bool UpdateViewRect(RectInt oldViewRect, RectInt newViewRect)
        {
            if (!oldViewRect.Equals(newViewRect))
            {
                var oldMin = oldViewRect.min;
                var oldMax = oldViewRect.max;
                var newMin = newViewRect.min;
                var newMax = newViewRect.max;
                int oldMinX = oldMin.x;
                int oldMinY = oldMin.y;
                int oldMaxX = oldMax.x;
                int oldMaxY = oldMax.y;
                int newMinX = newMin.x;
                int newMinY = newMin.y;
                int newMaxX = newMax.x;
                int newMaxY = newMax.y;

                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        if (!(i >= newMinY && i <= newMaxY &&
                            j >= newMinX && j <= newMaxX))
                        {
                            //物体不在新的视野中,隐藏它
                            if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                            {
                                int idx = i * mCols + j;
                                if (mBigTileDatas[idx] != null)
                                {
                                    SetObjectActiveTop(false, false, mCurrentZoom, j, i);
                                }
                            }
                        }
                    }
                }

                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        if (!(i >= oldMinY && i <= oldMaxY &&
                            j >= oldMinX && j <= oldMaxX))
                        {
                            //物体不在旧的视野中,显示它
                            if (i >= 0 && i < mRows && j >= 0 && j < mCols)
                            {
                                int idx = i * mCols + j;
                                if (mBigTileDatas[idx] != null)
                                {
                                    SetObjectActiveTop(true, false, mCurrentZoom, j, i);
                                }
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        }

        public void GetObjectsInBounds(Bounds bounds, List<ComplexGridBigTileData2> objects)
        {
            var min = bounds.min;
            var max = bounds.max;
            var minCoord = FromWorldPositionToCoordinate(min);
            var maxCoord = FromWorldPositionToCoordinate(max);

            for (int y = minCoord.y; y <= maxCoord.y; ++y)
            {
                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                {
                    var tileData = GetBigTileData(x, y);
                    if (tileData != null)
                    {
                        objects.Add(tileData);
                    }
                }
            }
        }

        public override bool isGameLayer => true;

        public void AddTileObject(TileObjectData2 obj)
        {
            List<TileObjectData2> objectsInThisTile;
            mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile);
#if UNITY_EDITOR
            Debug.Assert(objectsInThisTile.Contains(obj) == false);
#endif
            objectsInThisTile.Add(obj);
            mActiveTileChildren.Add(obj.id, obj);
        }

        public void RemoveTileObject(TileObjectData2 obj)
        {
            List<TileObjectData2> objectsInThisTile;
            mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile);
            bool suc = objectsInThisTile.Remove(obj);
#if UNITY_EDITOR
            Debug.Assert(suc);
#endif
            bool removeSuc = mActiveTileChildren.Remove(obj.id);
#if UNITY_EDITOR
            Debug.Assert(removeSuc);
#endif
        }

        public bool HasTileObject(int id)
        {
            return mActiveTileChildren.ContainsKey(id);
        }

        public TileObjectData2 GetTileObject(int id)
        {
            TileObjectData2 obj;
            mActiveTileChildren.TryGetValue(id, out obj);
            return obj;
        }

        public override bool Contains(int objectID)
        {
            Debug.Assert(false);
            return true;
            //return mObjects.ContainsKey(objectID);
        }

        //x,y对应的地块是否是视野边界地块
        public bool IsBoundsTile(int x, int y)
        {
            var viewRect = GetViewRect(map.viewport);
            int xMin = viewRect.x;
            int xMax = xMin + viewRect.width;
            int yMin = viewRect.y;
            int yMax = yMin + viewRect.height;
            if (x > xMin && x < xMax &&
                y > yMin && y < yMax)
            {
                return false;
            }
            return true;
        }

        void LoadPrefabInfos()
        {
            string path = MapCoreDef.GetDecorationBorderLayerHeaderFilePath(map.dataFolder);
            var stream = MapModuleResourceMgr.LoadTextStream(path, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);
                var version = Utils.ReadVersion(reader);
                int nPrefabs = reader.ReadInt32();
                mPrefabInfos = new List<PrefabInitInfo2>(nPrefabs);
                for (int i = 0; i < nPrefabs; ++i)
                {
                    List<string> prefabPathsForAllType = null;
                    if (version.minorVersion >= 4)
                    {
                        prefabPathsForAllType = Utils.ReadStringList(reader);
                    }
                    else
                    {
                        string prefabPath = Utils.ReadString(reader);
                        prefabPathsForAllType = new List<string>() { prefabPath };
                    }
                    float boundsMinX = reader.ReadSingle();
                    float boundsMinZ = reader.ReadSingle();
                    float boundsWidth = reader.ReadSingle();
                    float boundsHeight = reader.ReadSingle();
                    var rotation = Utils.ReadQuaternion(reader);
                    var scale = Utils.ReadVector3(reader);
                    float height = reader.ReadSingle();
                    mPrefabInfos.Add(new PrefabInitInfo2(prefabPathsForAllType, boundsMinX, boundsMinZ, boundsWidth, boundsHeight, rotation, scale, height));
                }
                if (version.minorVersion >= 2)
                {
                    for (int i = 0; i < nPrefabs; ++i)
                    {
                        mPrefabInfos[i].maxVisibleQuality = reader.ReadInt16();
                    }
                }

                if (version.minorVersion >= 3)
                {
                    for (int i = 0; i < mRows; ++i)
                    {
                        for (int j = 0; j < mCols; ++j)
                        {
                            mDontUpdateTileBigObjectCulling[i, j] = reader.ReadBoolean();
                        }
                    }
                }
                reader.Close();
            }
            else
            {
                Debug.LogError($"invalid header {path}");
            }
          
        }

        void LoadTask(Stream stream)
        {
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int majorVersion = reader.ReadInt32();
                int minorVersion = reader.ReadInt32();

                int tileCount = reader.ReadInt32();

                for (int i = 0; i < tileCount; ++i)
                {
                    int tileX = reader.ReadInt32();
                    int tileY = reader.ReadInt32();

                    int idx = tileY * mCols + tileX;
                    mBigTileDatas[idx] = new ComplexGridBigTileData2();

                    int nLODs = reader.ReadInt32();
                    for (int lod = 0; lod < nLODs; ++lod)
                    {
                        int nObjectsInThisLOD = reader.ReadInt32();
                        var objects = new List<BigTileChildPrefabData2>(nObjectsInThisLOD);
                        mBigTileDatas[idx].objectsOfEachLOD.Add(objects);
                        for (int k = 0; k < nObjectsInThisLOD; ++k)
                        {
                            BigTileChildPrefabData2 childPrefabData = new BigTileChildPrefabData2();
                            childPrefabData.x = reader.ReadSingle();
                            childPrefabData.z = reader.ReadSingle();
                            childPrefabData.prefabInitInfoIndex = reader.ReadInt16();
                            childPrefabData.objectType = (TileObjectType)reader.ReadByte();
                            childPrefabData.viewID = reader.ReadInt32();
                            objects.Add(childPrefabData);
                        }
                    }

                    mTileChildren[idx] = new List<TileObjectData2>(mBigTileDatas[idx].objectsOfEachLOD[0].Count);
                }

                mInited = true;
                reader.Close();
            }
        }

        void DoLoadAllTiles(Stream stream, bool asyncLoading)
        {
            if (asyncLoading)
            {
                var task = new LoadingTask(()=> {
                    LoadTask(stream);
                });
                LoadingTaskManager.AddTask(task);
            }
            else
            {
                LoadTask(stream);
            }
        }

        void LoadAllTiles(string dataPath, bool asyncLoading)
        {
            if (asyncLoading)
            {
                MapModuleResourceMgr.LoadTextStreamAsync(dataPath, (str, stream)=> {
                    DoLoadAllTiles(stream, asyncLoading);
                });
            }
            else
            {
                var stream = MapModuleResourceMgr.LoadTextStream(dataPath, true);
                DoLoadAllTiles(stream, asyncLoading);
            }
            
        }

        public ComplexGridBigTileData2 GetBigTileData(int x, int y)
        {
            return mBigTileDatas[y * horizontalTileCount + x];
        }

        public override Vector3 FromCoordinateToWorldPosition(int x, int y)
        {
            return new Vector3(x * mTileWidth + mLayerOrigin.x + mRealLayerBounds.x, 0, y * mTileHeight + mLayerOrigin.z + mRealLayerBounds.y);
        }

        public override Vector3 FromCoordinateToWorldPositionCenter(int x, int y)
        {
            return new Vector3((x + 0.5f) * mTileWidth + mLayerOrigin.x + mRealLayerBounds.x, 0, (y + 0.5f) * mTileHeight + mLayerOrigin.z + mRealLayerBounds.y);
        }

        public override Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            int x = Mathf.FloorToInt((position.x - mLayerOrigin.x - mRealLayerBounds.x) / tileWidth);
            int y = Mathf.FloorToInt((position.z - mLayerOrigin.z - mRealLayerBounds.y) / tileHeight);
            return new Vector2Int(x, y);
        }

        public bool isLoading { set; get; }
        public int objectCount { get { return mObjects.Count; } }
        public List<PrefabInitInfo2> prefabInfos { get { return mPrefabInfos; } }

        Rect mLastViewport;
        float mLastCameraZoom;

        //按tile来索引
        Dictionary<int, List<TileObjectData2>> mTileChildren = new Dictionary<int, List<TileObjectData2>>(3000);
        Dictionary<int, TileObjectData2> mActiveTileChildren = new Dictionary<int, TileObjectData2>(2000);
        Dictionary<int, TileObjectData2> mVisibleTileChildren = new Dictionary<int, TileObjectData2>(1000);
        
        bool mIsLODChanged = false;
        bool mInited = false;

        //在每个tile中,每个lod中每个child object的id
        List<int[]>[] mTileObjectIDs;

        ObjectPool<List<int>> mIDPool = new ObjectPool<List<int>>(1000, () => new List<int>(5));
        Dictionary<int, TileObjectData2> mObjects = new Dictionary<int, TileObjectData2>(1000);

        System.Action<TileObjectData2> mOnActiveStateChangeCallback;
        System.Action<TileObjectData2> mOnObjectScaleChangeCallback;

        ComplexGridBigTileData2[] mBigTileDatas;
        List<PrefabInitInfo2> mPrefabInfos;
        Rect mRealLayerBounds;
        TileObjectDataPool2 mPool = new TileObjectDataPool2();

        bool[,] mDontUpdateTileBigObjectCulling;

#if UNITY_EDITOR && TFW_MAP_DEBUG
        GridViewer mGridViewer;
#endif
    };
}
