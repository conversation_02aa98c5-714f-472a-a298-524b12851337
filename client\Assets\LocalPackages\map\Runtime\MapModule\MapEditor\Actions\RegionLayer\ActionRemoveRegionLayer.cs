﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public class ActionRemoveRegionLayer : EditorAction
    {
        public ActionRemoveRegionLayer(int layerID)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as RegionLayer;
            mDisplayVertexRadius = layer.displayVertexRadius;
            mLayerID = layerID;
            mLayerWidth = layer.GetTotalWidth();
            mLayerHeight = layer.GetTotalHeight();

            List<IMapObjectData> regions = new List<IMapObjectData>();
            layer.GetAllObjects(regions);
            for (int i = 0; i < regions.Count; ++i)
            {
                var action = new ActionRemoveRegion(layerID, regions[i].GetEntityID());
                mRemoveActions.Add(action);
            }
        }

        public override bool Do()
        {
            bool suc = true;
            for (int i = mRemoveActions.Count - 1; i >= 0; --i)
            {
                suc &= mRemoveActions[i].Do();
            }

            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RegionLayer;
            int index = Map.currentMap.GetMapLayerIndex(layer);
            Map.currentMap.RemoveMapLayerByIndex(index);
            return suc;
        }

        public override bool Undo()
        {
            var map = Map.currentMap as EditorMap;
            var layer = map.CreateRegionLayer(MapCoreDef.MAP_LAYER_NODE_REGION, mLayerWidth, mLayerHeight, mLayerID, mDisplayVertexRadius);
            Map.currentMap.AddMapLayer(layer);

            bool suc = true;
            for (int i = 0; i < mRemoveActions.Count; ++i)
            {
                suc &= mRemoveActions[i].Undo();
            }

            Selection.activeObject = layer.layerView.root;
            return suc;
        }

        List<ActionRemoveRegion> mRemoveActions = new List<ActionRemoveRegion>();
        int mLayerID;
        float mLayerWidth;
        float mLayerHeight;
        float mDisplayVertexRadius;
    }
}

#endif