﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class FOVControl : MonoBehaviour
    {
        public void UpdateFOV()
        {
            if (config != null)
            {
                var map = Map.currentMap;
                if (map != null)
                {
                    var camera = map.camera;
                    var cameraHeight = camera.transform.position.y;
                    if (!Mathf.Approximately(mLastCameraHeight, cameraHeight))
                    {
                        mLastCameraHeight = cameraHeight;
                        float fov = config.GetFOV(cameraHeight);
                        camera.fieldOfView = fov;
                    }
                }
            }
        }

        public float GetFOV(float cameraHeight)
        {
            if (config != null)
            {
                return config.GetFOV(cameraHeight);
            }

            Debug.Assert(false, "Invalid fov config!");
            return 15;
        }

        float mLastCameraHeight;
        public FOVConfig config;
    }
}
