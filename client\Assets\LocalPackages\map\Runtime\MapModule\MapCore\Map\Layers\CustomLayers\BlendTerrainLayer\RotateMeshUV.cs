﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    //需要旋转的地表tile挂这个脚本来旋转uv,流程是美术还是在transform上做旋转,然后在这个transform上挂这个脚本,指定上对应的旋转,统一处理后transform变为
    //identity,然后mesh的uv被旋转成对应值
    class RotateMeshUV : MonoBehaviour
    {
        public TileUVRotation uvRotation;
        //用于还原tranform的prefab
        [SerializeField]
        Vector3 originalPosition;
        [SerializeField]
        Quaternion originalRotation;
        [SerializeField]
        string originalMeshGUID;

        static List<RotateMeshUV> FindScripts()
        {
            List<RotateMeshUV> scripts = new List<RotateMeshUV>();
            string[] guids = AssetDatabase.FindAssets("t:Prefab");

            foreach (string guid in guids)
            {
                string myObjectPath = AssetDatabase.GUIDToAssetPath(guid);
                Object[] myObjs = AssetDatabase.LoadAllAssetsAtPath(myObjectPath);

                foreach (Object thisObject in myObjs)
                {
                    var type = thisObject.GetType();
                    if (type == typeof(RotateMeshUV))
                    {
                        scripts.Add(thisObject as RotateMeshUV);
                    }
                }
            }

            return scripts;
        }

        //旋转所有带标记的mesh
        public static void RotateAll(string outputFolder)
        {
            if (string.IsNullOrEmpty(outputFolder))
            {
                return;
            }    

            if (!Directory.Exists(outputFolder))
            {
                Directory.CreateDirectory(outputFolder);
            }

            var scripts = FindScripts();
            for (int i = 0; i < scripts.Count; ++i)
            {
                DoRotate(scripts[i], outputFolder);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        public static void RecoverAll()
        {
            var scripts = FindScripts();
            for (int i = 0; i < scripts.Count; ++i)
            {
                DoRecover(scripts[i]);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        static void DoRecover(RotateMeshUV rotate)
        {
            var gameObject = rotate.gameObject;
            var rootGameObject = Utils.GetRootTransform(gameObject.transform).gameObject;
            string prefabPath = AssetDatabase.GetAssetPath(rootGameObject);
            if (rotate.uvRotation != TileUVRotation.None && !string.IsNullOrEmpty(prefabPath))
            {
                //only process prefabs
                var meshFilter = gameObject.GetComponent<MeshFilter>();
                if (meshFilter == null || meshFilter.sharedMesh == null)
                {
                    Debug.Assert(false, $"{gameObject.name}'s mesh filter is not found!");
                    return;
                }

                string meshPath = AssetDatabase.GUIDToAssetPath(rotate.originalMeshGUID);
                var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                if (mesh != null)
                {
                    meshFilter.sharedMesh = mesh;
                    gameObject.transform.rotation = rotate.originalRotation;
                    gameObject.transform.localScale = Vector3.one;
                    gameObject.transform.position = rotate.originalPosition;
                    rotate.uvRotation = TileUVRotation.None;
                    rotate.originalPosition = Vector3.zero;
                    rotate.originalRotation = Quaternion.identity;
                    rotate.originalMeshGUID = "";
                    PrefabUtility.SavePrefabAsset(rootGameObject);
                }
            }
        }

        static void DoRotate(RotateMeshUV rotate, string outputFolder)
        {
            var gameObject = rotate.gameObject;
            float yRotation = gameObject.transform.rotation.eulerAngles.y;
            if (yRotation == 0)
            {
                rotate.uvRotation = TileUVRotation.None;
            }
            else if (Mathf.Approximately(yRotation, 90))
            {
                rotate.uvRotation = TileUVRotation.Rotate90;
            }
            else if (Mathf.Approximately(yRotation, 180))
            {
                rotate.uvRotation = TileUVRotation.Rotate180;
            }
            else if (Mathf.Approximately(yRotation, 270))
            {
                rotate.uvRotation = TileUVRotation.Rotate270;
            }
            else
            {
                Debug.Assert(false, "Not support");
                return;
            }
            var rootGameObject = Utils.GetRootTransform(gameObject.transform).gameObject;
            string prefabPath = AssetDatabase.GetAssetPath(rootGameObject);
            if (yRotation != 0 && !string.IsNullOrEmpty(prefabPath))
            {
                //only process prefabs
                var meshFilter = gameObject.GetComponent<MeshFilter>();
                if (meshFilter == null || meshFilter.sharedMesh == null)
                {
                    Debug.Assert(false, $"{gameObject.name}'s mesh filter is not found!");
                    return;
                }

                string prefabName = Utils.GetPathName(prefabPath, false);
                string meshPath = $"{outputFolder}/{prefabName}.asset";
                rotate.originalMeshGUID = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(meshFilter.sharedMesh));
                rotate.originalPosition = gameObject.transform.position;
                rotate.originalRotation = gameObject.transform.rotation;
                var mesh = CreateRotatedMesh(yRotation, meshFilter.sharedMesh, meshPath);
                meshFilter.sharedMesh = mesh;
                //reset transform
                gameObject.transform.rotation = Quaternion.identity;
                gameObject.transform.localScale = Vector3.one;
                gameObject.transform.position = Vector3.zero;

                PrefabUtility.SavePrefabAsset(rootGameObject);
            }
        }

        static Mesh CreateRotatedMesh(float yRotation, Mesh originalMesh, string outputMeshPath)
        {
            Quaternion q = Quaternion.Euler(0, -yRotation, 0);
            var newMesh = Object.Instantiate<Mesh>(originalMesh);
            //only rotate uv0 now!
            var uvs = newMesh.uv;
            Vector2 uvCenter = Utils.CalculateCentroid(uvs);
            for (int i = 0; i < uvs.Length; ++i)
            {
                //swap x y
                Vector3 uv3 = new Vector3(uvs[i].x - uvCenter.x, 0, uvs[i].y - uvCenter.y);
                Vector3 rotatedUV3 = q * uv3;
                uvs[i].Set(rotatedUV3.x + uvCenter.x, rotatedUV3.z + uvCenter.y);
            }
            newMesh.SetUVs(0, uvs);

            AssetDatabase.CreateAsset(newMesh, outputMeshPath);
            return newMesh;
        }
    }
}


#endif