﻿ 



 
 


#if UNITY_EDITOR

using System.IO;
using UnityEditor;

namespace TFW.Map
{
    public partial class RegionLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.RegionLayerEditorDataVersion);

            SaveLayerData(writer);
            
            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayerData(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(displayVertexRadius);

            string defaultRegionMaterialGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(layerData.defaultRegionMaterial));
            Utils.WriteString(writer, defaultRegionMaterialGuid);

            writer.Write(layerData.borderMinX);
            writer.Write(layerData.borderMinZ);
            writer.Write(layerData.borderMaxX);
            writer.Write(layerData.borderMaxZ);
            Utils.WriteVector3Array(writer, layerData.borderMeshVertices);
            Utils.WriteIntArray(writer, layerData.borderMeshIndices);
            string borderMaterialGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(layerData.borderMaterial));
            Utils.WriteString(writer, borderMaterialGuid);

            writer.Write(layerData.generateBorderLine);
            string borderLineMaterialGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(layerData.borderLineMaterial));
            Utils.WriteString(writer, borderLineMaterialGuid);
            writer.Write(layerData.borderLineWidth);
            writer.Write(layerData.showBorderLineMesh);
            writer.Write(layerData.showRegionMesh);

            int objectCount = mLayerData.objectCount;
            writer.Write(objectCount);
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var obj = p.Value as RegionData;
                Utils.WriteVector3Array(writer, obj.outlines[0].outline.ToArray());
                Utils.WriteColor(writer, obj.innerColor);
                Utils.WriteColor(writer, obj.outerColor);
                Utils.WriteVector3Array(writer, obj.meshVertices);
                Utils.WriteIntArray(writer, obj.meshIndices);
                Utils.WriteColorArray(writer, obj.vertexColors);
                writer.Write((int)obj.type);
                writer.Write(obj.number);
                string materialGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(obj.material));
                Utils.WriteString(writer, materialGuid);
            }
        }
    }
}
#endif