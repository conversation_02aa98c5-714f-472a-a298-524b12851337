﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    class DrawRegion : MonoBehaviour
    {
        public void Init(Vector3 center, Vector3 size, Color color)
        {
            mCenter = center;
            mSize = size;
            mColor = color;
        }

        void OnDrawGizmos()
        {
            Gizmos.color = mColor;
            Gizmos.DrawWireCube(mCenter, mSize);
        }

        Color mColor;
        Vector3 mCenter;
        Vector3 mSize;
    }

    class TileLayerDebugger : MonoBehaviour
    {
        private void OnDestroy()
        {
            for (int i = 0; i < mGameObjects.Count; ++i)
            {
                Utils.DestroyObject(mGameObjects[i]);
            }
            mGameObjects = null;
        }

        public void Create(TileGridObjectLayerData layerData)
        {
            if (mLayerData == null)
            {
                mLayerData = layerData;
                int rows = mLayerData.verticalTileCount;
                int cols = mLayerData.horizontalTileCount;

                int k = 0;
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        var obj = CreateGameObject(k, j, i);
                        mGameObjects.Add(obj);
                        ++k;
                    }
                }
            }
        }

        GameObject CreateGameObject(int idx, int x, int y)
        {
            var obj = new GameObject(string.Format("{0}, ({1},{2})", idx.ToString(), x, y));
            var dr = obj.AddComponent<DrawRegion>();
            var pos = mLayerData.FromCoordinateToWorldPositionCenter(x, y);
            var size = new Vector3(mLayerData.tileWidth, 10, mLayerData.tileHeight);
            Color color = new Color(163 / 255.0f, 73 / 255.0f, 164 / 255.0f, 1.0f);
            if (idx % 2 == 0)
            {
                color = new Color(34 / 255.0f, 177 / 255.0f, 76 / 255.0f, 1.0f);
            }
            dr.Init(pos, size, color);
            Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
            var renderer = obj.AddComponent<MeshRenderer>();
            renderer.sharedMaterial = ArialFont.material;
            var textMesh = obj.AddComponent<TextMesh>();
            textMesh.text = idx.ToString();
            textMesh.fontSize = 32;
            obj.transform.localScale = Vector3.one * 10;
            obj.transform.rotation = Quaternion.Euler(45, 0, 0);
            obj.transform.position = new Vector3(pos.x, 20, pos.z);
            obj.transform.SetParent(transform);

            return obj;
        }


        List<GameObject> mGameObjects = new List<GameObject>();
        TileGridObjectLayerData mLayerData;
    }
}