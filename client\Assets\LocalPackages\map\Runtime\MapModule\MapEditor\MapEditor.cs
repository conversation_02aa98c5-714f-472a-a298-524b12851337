﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public class MapEditor : MonoBehaviour
    {
        void Start()
        {
            instance = this;
        }

        void OnDestroy()
        {
            instance = null;
        }

        void OnEnable()
        {
            instance = this;

            EditorApplication.update -= UpdateInEditMode;
            EditorApplication.update += UpdateInEditMode;
        }

        void Update()
        {
            //fix transform
            if (transform.position != Vector3.zero)
            {
                transform.position = Vector3.zero;
            }
            if (transform.rotation != Quaternion.identity)
            {
                transform.rotation = Quaternion.identity;
            }
            if (transform.localScale != Vector3.one)
            {
                transform.localScale = Vector3.one;
            }

            int newChildCount = gameObject.transform.childCount;
            if (mChildCount == 0)
            {
                mChildCount = newChildCount;
            }
            if (mChildCount != newChildCount)
            {
                DetectGameObjectDestroy();
                mChildCount = newChildCount;
            }
        }

        public ModelTemplate CreateModelTemplate(string prefabPath, GameObject prefabObject, bool isTileModelTemplate)
        {
            ModelTemplate temp = Map.currentMap.FindModelTemplate(prefabPath);
            if (temp == null)
            {
                var bounds = GameObjectBoundsCalculator.CalculateRect(prefabObject);

                temp = Map.currentMap.AddModelTemplate(prefabPath, Map.currentMap, bounds, isTileModelTemplate, new List<List<ModelTemplate>>(), new List<List<ChildPrefabTransform>>(), null);
            }
            return temp;
        }

        void UpdateInEditMode()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                map.Update();

                var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
                if (layer != null)
                {
                    layer.UpdateDestroyedObjects();
                }

                var ruinLayers = Map.currentMap.GetMapLayers<RuinLayer>();
                foreach (var ruinLayer in ruinLayers)
                {
                    ruinLayer.UpdateDestroyedObjects();
                }
            }
        }

        public Vector2 GetLoadSize(float fov, float aspect, float camRotateX, float cameraHeight, Vector2 scale)
        {
            float halfFovDeg = fov * 0.5f;

            // top
            float topRad = (90 - camRotateX + halfFovDeg) * Mathf.Deg2Rad;
            float top = Mathf.Tan(topRad) * cameraHeight;

            // bottom
            float bottomRad = (90 - camRotateX - halfFovDeg) * Mathf.Deg2Rad;
            float bottom = Mathf.Tan(bottomRad) * cameraHeight;

            // left & right
            float halfWidth = (top - bottom) * aspect * 0.5f;
            float left = -halfWidth;
            float right = +halfWidth;

            var rect = Rect.MinMaxRect(left, bottom, right, top);
            return new Vector2(rect.width * scale.x, rect.height * scale.y);
        }

        void DetectGameObjectDestroy()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                int nLayers = map.GetMapLayerCount();
                int childCount = transform.childCount;
                for (int i = nLayers - 1; i >= 0; --i)
                {
                    var mapLayer = map.GetMapLayerByIndex(i);
                    bool found = false;
                    for (int j = 0; j < childCount; ++j)
                    {
                        if (mapLayer.name == transform.GetChild(j).name)
                        {
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        var layer = Map.currentMap.GetMapLayerByIndex(i);
                        if (layer is MapCollisionLayer)
                        {
                            var action = new ActionRemoveMapCollisionLayer(layer.id);
                            ActionManager.instance.PushAction(action);
                        }
                        else if (layer is PolygonRiverLayer)
                        {
                            var action = new ActionRemovePolygonRiverLayer(layer.id);
                            ActionManager.instance.PushAction(action);
                        }
                        else if (layer is CameraColliderLayer)
                        {
                            var action = new ActionRemoveCameraColliderLayer(layer.id);
                            ActionManager.instance.PushAction(action);
                        }
                        else
                        {
                            Map.currentMap.RemoveMapLayerByIndex(i);
                        }
                    }
                }
            }
        }

        public static MapEditor instance;

        //用来检测是否有game object被删除
        int mChildCount = 0;
    }
}

#endif