%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2895394121960471
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5406474680001501290}
  - component: {fileID: 8516523705085182090}
  m_Layer: 0
  m_Name: ObstacleOutline 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5406474680001501290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2895394121960471}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8516523705085182090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2895394121960471}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -88.5574, y: 0, z: -73.54005}
    - {x: -44.037235, y: 0, z: -54.429176}
    - {x: -35.81702, y: 0, z: -60.53381}
    - {x: -39.127426, y: 0, z: -68.09072}
    - {x: -56.17128, y: 0, z: -84.20099}
    - {x: -72.93567, y: 0, z: -90}
    - {x: -87.56485, y: 0, z: -83.300316}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -69.4735, y: 0, z: -90}
    - {x: -78.5771, y: 0, z: -90}
    - {x: -79.003, y: 0, z: -90}
    - {x: -85.8228, y: 0, z: -87.144}
    - {x: -86.1244, y: 0, z: -86.9581}
    - {x: -88.4836, y: 0, z: -85.0883}
    - {x: -88.9471, y: 0, z: -84.4723}
    - {x: -89.937, y: 0, z: -82.0604}
    - {x: -89.9904, y: 0, z: -81.9073}
    - {x: -90, y: 0, z: -80.7062}
    - {x: -90, y: 0, z: -80.4463}
    - {x: -90, y: 0, z: -75.1003}
    - {x: -90, y: 0, z: -74.7401}
    - {x: -90, y: 0, z: -73.1549}
    - {x: -89.8912, y: 0, z: -71.9597}
    - {x: -87.5157, y: 0, z: -70.7212}
    - {x: -87.4264, y: 0, z: -70.6783}
    - {x: -59.5297, y: 0, z: -58.3996}
    - {x: -59.5167, y: 0, z: -58.394}
    - {x: -50.3255, y: 0, z: -54.4522}
    - {x: -50.1972, y: 0, z: -54.404}
    - {x: -46.2184, y: 0, z: -53.1126}
    - {x: -44.5469, y: 0, z: -52.5701}
    - {x: -43.5712, y: 0, z: -52.5785}
    - {x: -38.8425, y: 0, z: -54.2034}
    - {x: -38.1776, y: 0, z: -54.6559}
    - {x: -33.2109, y: 0, z: -60.4722}
    - {x: -33.0557, y: 0, z: -62.4107}
    - {x: -37.3719, y: 0, z: -69.4988}
    - {x: -37.6182, y: 0, z: -69.8048}
    - {x: -54.8421, y: 0, z: -86.253}
    - {x: -55.2545, y: 0, z: -86.5331}
    - {x: -57.3065, y: 0, z: -87.4762}
    - {x: -57.3737, y: 0, z: -87.5052}
    - {x: -59.9031, y: 0, z: -88.5214}
    - {x: -59.9568, y: 0, z: -88.5418}
    - {x: -64.9936, y: 0, z: -90}
    - {x: -65.0137, y: 0, z: -90}
    - {x: -68.771, y: 0, z: -90}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
