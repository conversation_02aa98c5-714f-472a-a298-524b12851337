﻿using Common;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Config;
using K3;
using Logic;
using Public;
using System.Collections;
using System.Collections.Generic;
using TFW.UI;
using UI;
using UnityEngine;

public class MergeGridSort : MonoBehaviour
{

    public TFWImage cdTime, cdTime2;
    public TFWText cdDesc;

    // Start is called before the first frame update
    void Start()
    {
        gameObject.SetActive(UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeSort));

        EventMgr.RegisterEvent(TEventType.OnUnlockConditionChanged, (objs) => 
        {
            CheckUnlock();
        }, this);

        UIBase.AddRemoveListener(TFW.EventTriggerType.Click, gameObject, (x, y) =>
        {
            SortMerge();
        });
    }

    private void CheckUnlock()
    {
        var unlock= UnlockMgr.I.CheckUnlock(UnlockFuncEnum.MergeSort);
        
        gameObject.SetActive(unlock);

        if (K3PlayerMgr.I.PlayerData!=null && !K3PlayerMgr.I.PlayerData.UnlockMenus.Contains((int)K3UnlockData.UnlockType.SortMerge) && unlock)
        { 
            CSPlayer.I.AddUnlockMenus((int)K3UnlockData.UnlockType.SortMerge); 
            
            D.Info?.Log($"UnlockMenu :解锁SortMerge");
        }
    }

    private void OnDestroy()
    {
        EventMgr.UnregisterEvent(this);
    }

    public void SortMerge()
    {
        if (K3PlayerMgr.I.PlayerData.MergeSortTime > GameTime.Time)
        { 
            return;
        }
         
        //TODO 棋盘执行排序
        var uiMerge= PopupManager.I.FindPopup<UIMerge>();
        if (uiMerge != null)
        {
            UniTask.Void(async () =>
            {
               var success= await uiMerge.SortMerge();
                if (success)
                {
                    K3PlayerMgr.I.PlayerData.MergeSortTime = GameTime.Time + (int)MetaConfig.Maze_sort_out_cd * 1000;
                    K3PlayerMgr.I.SavePlayerDataToServer();
                }
            });
             
        }
    }

    // Update is called once per frame
    void LateUpdate()
    {
        if (K3PlayerMgr.I.PlayerData.MergeSortTime > GameTime.Time)
        {
            cdTime.gameObject.SetActive(true);
            float process = Mathf.Max(0, Mathf.Min(1, (K3PlayerMgr.I.PlayerData.MergeSortTime - GameTime.Time) * 1f / (MetaConfig.Maze_sort_out_cd * 1000)));
            cdTime.fillAmount = process;
            cdTime2.fillAmount = process;
            //cdDesc.text = UIStringUtils.CommonFormatTime(K3PlayerMgr.I.PlayerData.MergeSortTime - GameTime.Time);
        }
        else
        {
            cdTime.gameObject.SetActive(false);
            //cdDesc.text = "Function_7".ToLocal();
        }
    }
}
