﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public class PolygonRiverSectionModel
    {
        public void OnDestroy()
        {
            DestroyMesh();
        }

        void GetMeshData(PolygonRiverSectionData data, out Vector3[] meshVertices, out int[] meshIndices)
        {
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (collisionLayer != null)
            {
                List<List<Vector3>> clipperOutlines = new List<List<Vector3>>();
                collisionLayer.GetCollisionOutlinesOfType(clipperOutlines, CollisionAttribute.RiverClipper, PrefabOutlineType.NavMeshObstacle);
                if (clipperOutlines.Count > 0)
                {
                    List<List<Vector3>> noneHoles = new List<List<Vector3>>();
                    List<List<Vector3>> holes = new List<List<Vector3>>();
                    PolygonAlgorithm.GetDifferencePolygons(data.outlineRaw, clipperOutlines, out noneHoles, out holes, ClipperLib.PolyFillType.pftEvenOdd, ClipperLib.PolyFillType.pftNonZero);
                    Triangulator.TriangulatePolygons(noneHoles, holes, false, 0, 0, null, out meshVertices, out meshIndices);
                }
                else
                {
                    var blendTerrainLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_GROUND) as BlendTerrainLayer;
                    if (blendTerrainLayer != null)
                    {
                        var polygonRiverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
                        AutoClipRiverPartHiddenByGroundTile.Clip(data, polygonRiverLayer.layerData.expandingTileCount, out meshVertices, out meshIndices);
                    }
                    else
                    {
                        //不裁剪
                        Triangulator.TriangulatePolygon(data.outlineRaw, out meshVertices, out meshIndices);
                    }
                }
            }
            else
            {
                //不裁剪
                Triangulator.TriangulatePolygon(data.outlineRaw, out meshVertices, out meshIndices);
            }
        }

        public void GenerateMesh(PolygonRiverSectionData data, RiverGenerationParameter param, GameObject parentObject, Bounds riverBounds)
        {
            DestroyMesh();

            Vector3[] meshVertices;
            int[] meshIndices;
            GetMeshData(data, out meshVertices, out meshIndices);
            
            mRiverMeshObject = new GameObject("River");
            mRiverMeshObject.transform.parent = parentObject.transform;
            var filter = mRiverMeshObject.AddComponent<MeshFilter>();
            mRiverMesh = new Mesh();
            mRiverMesh.SetVertices(meshVertices);
            mRiverMesh.triangles = meshIndices;
            mRiverMesh.RecalculateNormals();
            //uv0是section的bounds 创建的uv
            mRiverMesh.uv = GenerateUVs(meshVertices, mRiverMesh.bounds);
            //uv1是河流的bounds 创建的uv
            mRiverMesh.uv2 = GenerateUVs(meshVertices, riverBounds);
            mRiverMaterial = AssetDatabase.LoadAssetAtPath<Material>(param.mtlPath);
            mRiverMaterial = Object.Instantiate<Material>(mRiverMaterial);
#if false
            if (!mRiverMaterial.HasProperty(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME))
            {
                EditorUtility.DisplayDialog("Warning", string.Format("Material has no {0} shader property! can't use river texture in this material!", MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME), "OK");
            }
#endif
            mRiverMaterial.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, data.texture);
            mRiverMeshObject.AddComponent<MeshRenderer>().sharedMaterial = mRiverMaterial;
            filter.sharedMesh = mRiverMesh;
        }

        public void RecreateMesh(PolygonRiverSectionData data, Bounds riverBounds)
        {
            Vector3[] meshVertices;
            int[] meshIndices;

            GetMeshData(data, out meshVertices, out meshIndices);

            if (mRiverMeshObject.GetComponent<MeshFilter>() == null)
            {
                mRiverMeshObject.AddComponent<MeshFilter>();
            }
            mRiverMesh.Clear();
            mRiverMesh.SetVertices(meshVertices);
            mRiverMesh.triangles = meshIndices;
            mRiverMesh.RecalculateNormals();
            //uv0是section的bounds 创建的uv
            mRiverMesh.uv = GenerateUVs(meshVertices, mRiverMesh.bounds);
            //uv1是河流的bounds 创建的uv
            mRiverMesh.uv2 = GenerateUVs(meshVertices, riverBounds);
            mRiverMesh.UploadMeshData(false);
        }

        Vector2[] GenerateUVs(Vector3[] vertices, Bounds bounds)
        {
            var min = bounds.min;
            var size = bounds.size;
            Vector2[] uvs = new Vector2[vertices.Length];
            for (int i = 0; i < vertices.Length; ++i)
            {
                float u = (vertices[i].x - min.x) / size.x;
                float v = (vertices[i].z - min.z) / size.z;
                uvs[i] = new Vector2(u, v);
            }
            return uvs;
        }

        void DestroyMesh()
        {
            if (mRiverMeshObject != null)
            {
                GameObject.DestroyImmediate(mRiverMeshObject);
                mRiverMeshObject = null;
            }

            if (mRiverMesh != null)
            {
                Object.DestroyImmediate(mRiverMesh);
                mRiverMesh = null;
            }

            if (mRiverMaterial != null)
            {
                Object.DestroyImmediate(mRiverMaterial);
                mRiverMaterial = null;
            }
        }

        public void Move(Vector3 offset)
        {
            if (mRiverMesh != null)
            {
                mRiverMesh.GetVertices(mVerticesCache);
                for (int i = 0; i < mVerticesCache.Count; ++i)
                {
                    mVerticesCache[i] += offset;
                }
                mRiverMesh.SetVertices(mVerticesCache);
                mRiverMesh.UploadMeshData(false);
                mRiverMesh.RecalculateBounds();
                mVerticesCache.Clear();
            }
        }

        public void SetShaderLOD(int lod)
        {
            if (mRiverMaterial != null)
            {
                //设置了这个值就不会受quality的影响了
                mRiverMaterial.shader.maximumLOD = lod;
            }
        }

        public void SetTexture(Texture2D texture)
        {
            if (mRiverMaterial != null)
            {
                mRiverMaterial.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, texture);
            }
        }

        public void ChangeMaterial(Material newMtl)
        {
            Texture2D oldTexture = null;

            if (mRiverMaterial != null)
            {
                oldTexture = mRiverMaterial.GetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME) as Texture2D;
                Utils.DestroyObject(mRiverMaterial);
                mRiverMaterial = null;
            }

            if (newMtl != null)
            {
                mRiverMaterial = Object.Instantiate<Material>(newMtl);
#if false
                if (!mRiverMaterial.HasProperty(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME))
                {
                    EditorUtility.DisplayDialog("Warning", string.Format("Material has no {0} shader property! can't use river texture in this material!", MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME), "OK");
                }
#endif
                mRiverMaterial.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, oldTexture);
                if (mRiverMeshObject != null)
                {
                    mRiverMeshObject.AddComponent<MeshRenderer>().sharedMaterial = mRiverMaterial;
                }
            }
        }

        public GameObject riverGameObject { get { return mRiverMeshObject; } }
        public Material riverMaterial { get { return mRiverMaterial; } }

        GameObject mRiverMeshObject;
        Mesh mRiverMesh;
        Material mRiverMaterial;
        List<Vector3> mVerticesCache = new List<Vector3>();
    }

    public class PolygonRiverModel : PolygonObjectModel
    {
        public PolygonRiverModel(PolygonObjectData data) : base(data, MapCoreDef.POLYGON_RIVER_MODEL_NAME)
        {
            var river = data as PolygonRiverData;
            var param = new RiverGenerationParameter();
            param.mtlPath = river.materialPath;
            param.textureSize = river.textureSize;
            if (!string.IsNullOrEmpty(param.mtlPath))
            {
                GenerateMesh(river, param);
            }
            var layer = Map.currentMap.GetMapLayerOfType(typeof(PolygonRiverLayer)) as PolygonRiverLayer;
            for (int i = 0; i < river.splitters.Count; ++i)
            {
                GenerateSplitter(-1, river.splitters[i], layer.displayVertexRadius, true);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ClearSections();
            ClearSplitters();
        }

        void ClearSections()
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].OnDestroy();
            }
            mSections.Clear();
        }

        void ClearSplitters()
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                mSplitters[i].OnDestroy();
            }
            mSplitters.Clear();
        }

        public void ShowSplitter(bool show)
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                mSplitters[i].SetActive(show);
            }
        }

        public void DeleteSplitter(int idx)
        {
            if (idx >= 0 && idx < mSplitters.Count)
            {
                mSplitters[idx].OnDestroy();
                mSplitters.RemoveAt(idx);
            }
        }

        public void SetSplitterColor(int index, Color color)
        {
            if (index >= 0 && index < mSplitters.Count)
            {
                mSplitters[index].SetLinkColor(color);
            }
        }

        public void GenerateMesh(PolygonRiverData data, RiverGenerationParameter param)
        {
            ClearSections();
            for (int i = 0; i < data.sections.Count; ++i)
            {
                var sectionModel = new PolygonRiverSectionModel();
                sectionModel.GenerateMesh(data.sections[i], param, mGameObject, Utils.RectToBounds(data.GetBounds()));
                mSections.Add(sectionModel);
            }
        }

        public void RecreateMesh(PolygonRiverData data)
        {
            int n = data.sections.Count;
            if (mSections.Count != n)
            {
                Debug.LogWarning($"Recreate Mesh {data.id} failed! section count not match! you have to regenerate river");
                return;
            }
            for (int i = 0; i < n; ++i)
            {
                mSections[i].RecreateMesh(data.sections[i], Utils.RectToBounds(data.GetBounds()));
            }
        }

        public override void UpdateVertex(PrefabOutlineType type, int index, PolygonObjectData river)
        {
            base.UpdateVertex(type, index, river);
            //update splitter vertex pos
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                var splitter = (river as PolygonRiverData).GetSplitter(i);
                mSplitters[i].SetPosition(true, splitter.startVertexPosition);
                mSplitters[i].SetPosition(false, splitter.endVertexPosition);
            }
        }

        public GameObject GetRiverMesh(int section)
        {
            if (section >= 0 && section < mSections.Count)
            {
                return mSections[section].riverGameObject;
            }
            Debug.Assert(false);
            return null;
        }

        public void GenerateSplitter(int idx, PolygonRiverSplitterData data, float radius, bool visible)
        {
            var splitter = new PolygonRiverSplitterModel(data.startVertexPosition, data.endVertexPosition, radius, mGameObject, true);
            splitter.SetActive(visible);
            if (idx < 0)
            {
                idx = mSplitters.Count;
            }
            mSplitters.Insert(idx, splitter);
        }

        public override void SetVertexRadius(float radius)
        {
            base.SetVertexRadius(radius);

            for (int i = 0; i < mSplitters.Count; ++i)
            {
                mSplitters[i].SetSize(true, radius);
                mSplitters[i].SetSize(false, radius);
            }
        }

        public override void Update(PolygonObjectData data)
        {
            var riverData = data as PolygonRiverData;
            base.Update(data);
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                mSplitters[i].Update(riverData.GetSplitter(i));
            }
        }

        public void Move(PolygonObjectData data, Vector3 offset)
        {
            var riverData = data as PolygonRiverData;
            base.Update(data);
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                mSplitters[i].Update(riverData.GetSplitter(i));
            }
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].Move(offset);
            }
        }

        public void SetRiverMaterialProperties(int section, Material materialProperties)
        {
            if (section >= 0 && section < mSections.Count)
            {
                Utils.CopyRiverMaterial(materialProperties, mSections[section].riverMaterial);
            }
        }

        public Material GetRiverMaterial(int section)
        {
            if (section >= 0 && section < mSections.Count)
            {
                return mSections[section].riverMaterial;
            }
            return null;
        }

        public void ChangeMaterial(Material newMtl)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].ChangeMaterial(newMtl);
            }
        }

        public void UpdateTexture(int riverID)
        {
            var riverData = Map.currentMap.FindObject(riverID) as PolygonRiverData;
            if (mSections.Count == riverData.sections.Count)
            {
                for (int i = 0; i < mSections.Count; ++i)
                {
                    mSections[i].SetTexture(riverData.sections[i].texture);
                }
            }
        }

        public void SetShaderLOD(int lod)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                mSections[i].SetShaderLOD(lod);
            }
        }

        List<PolygonRiverSectionModel> mSections = new List<PolygonRiverSectionModel>();
        List<PolygonRiverSplitterModel> mSplitters = new List<PolygonRiverSplitterModel>();
    }
}
#endif