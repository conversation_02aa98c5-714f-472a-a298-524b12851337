﻿using Cfg.C;
using cspb;
using Public;
using TFW.UI;
using UnityEngine;

namespace UI.Mail
{
    /// <summary>
    /// 邮件 侦查邮件  好像没了
    /// </summary>
    public class MailBodyScoutPlayer : MailBodyBase
    {
        protected override async void SetPage()
        {
            var root = UIHelper.GetChild(obj, "cont");

            MailContentScoutPlayer player = null;/// md.unionCoordsFav;

            if (player == null)
                return;
            
            var avatarCfg = await CPlayerAvatar.GetConfigAsync(player.avatarCfgID);
            //var avatarCfg = CPlayerAvatar.I(11330001);//for test 玩家头像，假数据
            if (avatarCfg != null)
            {
                UITools.SetImageBySpriteName(UIHelper.GetComponent<TFWImage>(root, "playerhead/frame"), avatarCfg.DisplayKey);
            }

           // D.Warning?.Assert(player != null && player.playerName != null, "[MailFormater] SetScoutPlayerInfo playerName is nil");
            UIHelper.SetText(root, "detail/playername", player.playerName);

           // D.Warning?.Assert(player != null && player.pos != null, "[MailFormater] SetScoutPlayerInfo pos is nil");
            var posObj = UIHelper.GetChild(root, "detail/coordinate");

            // 保护一下
            var pos = new Vector3(0, 0, 0);
            if (player != null && player.pos != null)
            {
                pos.x = (int)(player.pos.X / 1000);
                pos.z = (int)(player.pos.Z / 1000);
            }

            UIHelper.SetText(posObj, "", string.Format("(X:{0} Y:{1})", (int)(pos.x + 0.5f), (int)(pos.z + 0.5f)));
            UIHelper.SetText(root, "detail/power/powernum", player.power.ToString());
            UIHelper.SetText(root, "playerhead/LvTxt", player.intllLvl.ToString());
            //for test 玩家战力和等级，假数据
            //UIHelper.SetText(root, "detail/power/powernum","5000");
            //UIHelper.SetText(root, "playerhead/LvTxt", "50");

            var eventTrigger = posObj.GetComponent<TFW.EventTriggerListener>();
            eventTrigger.AddListener("onClick", (go, args) =>
            {
                ClosePage();
                Logic.LViewportJump.JumpWithMeter(pos, null);
            });
        }
    }

}