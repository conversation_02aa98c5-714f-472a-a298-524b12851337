﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class EntitySpawnLayerView : MapLayerView
    {
        GameObject mPlaneObject;
        Mesh mMesh;
        Material mMaterial;
        Texture2D mGridTexture;

        void DestroyTexturePlane()
        {
            Object.DestroyImmediate(mMesh);
            Object.DestroyImmediate(mMaterial);
            Object.DestroyImmediate(mGridTexture);
            Utils.DestroyObject(mPlaneObject);
        }

        void CreateTexturePlane(float width, float height)
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var npcLayerData = layerData as EntitySpawnLayerData;
                int horizontalGridCount = layerData.horizontalTileCount;
                int verticalGridCount = layerData.verticalTileCount;
                if (mPlaneObject == null && horizontalGridCount > 0)
                {
                    //create region texture and materials
                    mGridTexture = new Texture2D(horizontalGridCount, verticalGridCount, TextureFormat.RGBA32, false, false);
                    Color32[] gridColors = new Color32[horizontalGridCount * verticalGridCount];
                    var grids = npcLayerData.regionBrushIDs;
                    for (int i = 0; i < verticalGridCount; ++i)
                    {
                        for (int j = 0; j < horizontalGridCount; ++j)
                        {
                            var brushID = grids[i, j];
                            var brush = npcLayerData.FindBrush(brushID);
                            if (brush != null)
                            {
                                gridColors[i * horizontalGridCount + j] = brush.color;
                            }
                        }
                    }

                    mGridTexture.filterMode = FilterMode.Point;
                    mGridTexture.SetPixels32(gridColors);
                    mGridTexture.Apply();

                    //create plane
                    mPlaneObject = new GameObject("region plane");
                    mPlaneObject.SetActive(true);
                    //Utils.HideGameObject(mPlaneObject);
                    mPlaneObject.transform.parent = root.transform;
                    var meshRenderer = mPlaneObject.AddComponent<MeshRenderer>();
                    var meshFilter = mPlaneObject.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = CreateMesh(width, height);
                    meshRenderer.sharedMaterial = CreateMaterial();
                    meshRenderer.sharedMaterial.SetTexture("_MainTex", mGridTexture);
                }
            }
        }

        Mesh CreateMesh(float mapWidth, float mapHeight)
        {
            if (mMesh == null)
            {
                mMesh = new Mesh();
                mMesh.vertices = new Vector3[]{
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mapHeight),
                    new Vector3(mapWidth, 0, mapHeight),
                    new Vector3(mapWidth, 0, 0),
                };
                mMesh.uv = new Vector2[] {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mMesh;
        }

        Material CreateMaterial()
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/DiffuseTransparent"));
            }
            return mMaterial;
        }

        public void OnSetPixels(int x, int y, int width, int height, Color32[] pixels)
        {
            mGridTexture.SetPixels32(x, y, width, height, pixels);
            mGridTexture.Apply();
        }

        public void RefreshTexture()
        {
            var npcLayerData = layerData as EntitySpawnLayerData;
            int verticalGridCount = npcLayerData.verticalTileCount;
            int horizontalGridCount = npcLayerData.horizontalTileCount;
            Color32[] colors = new Color32[verticalGridCount * horizontalGridCount];
            Color32 black = new Color32(0, 0, 0, 0);
            int idx = 0;
            var grids = npcLayerData.regionBrushIDs;
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    var brushID = grids[i, j];
                    var brush = npcLayerData.FindBrush(brushID);
                    if (brush != null)
                    {
                        colors[idx] = brush.color;
                    }
                    else
                    {
                        colors[idx] = black;
                    }
                    ++idx;
                }
            }
            OnSetPixels(0, 0, horizontalGridCount, verticalGridCount, colors);
        }
    }
}


#endif