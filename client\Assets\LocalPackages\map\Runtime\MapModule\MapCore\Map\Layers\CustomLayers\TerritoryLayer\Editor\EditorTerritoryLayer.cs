﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;
using System.Text;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public class EditorTerritory
    {
        public EditorTerritory(int id, string name, Color color, PropertyDatas properties)
        {
            mColor = color;
            if (properties == null)
            {
                properties = new PropertyDatas(null);
            }
            mProperties = properties;
            this.id = id;
            this.name = name;
        }

        public void OnDestroy()
        {
            if (mBuildings != null)
            {
                for (int i = 0; i < mBuildings.Count; ++i)
                {
                    mBuildings[i].OnDestroy(true);
                }
            }
        }

        public void AddBuilding(EditorTerritoryBuilding building)
        {
            mBuildings.Add(building);
        }

        public bool RemoveBuilding(EditorTerritoryBuilding building, bool destroyGameObject)
        {
            for(int i = 0; i < mBuildings.Count; ++i)
            {
                if (building == mBuildings[i])
                {
                    building.OnDestroy(destroyGameObject);
                    mBuildings.RemoveAt(i);
                    return true;
                }
            }
            return false;
        }

        public void SetRadius(float radius)
        {
            foreach (var building in mBuildings)
            {
                building.gameObject.transform.localScale = Vector3.one * radius;
            }
        }

        public void SetColor(Color color)
        {
            mColor = color;
            for (int i = 0; i < mBuildings.Count; ++i)
            {
                mBuildings[i].SetColor(color);
            }
        }

        public Color GetColor()
        {
            return mColor;
        }

        public List<EditorTerritoryBuilding> buildings { get { return mBuildings; } }
        public PropertyDatas properties { get { return mProperties; } }

        public int id;
        public string name;
        Color mColor;
        PropertyDatas mProperties;
        List<EditorTerritoryBuilding> mBuildings = new List<EditorTerritoryBuilding>();
    }

    public class CurveRegionMeshGenerationLODParam
    {
        public CurveRegionMeshGenerationLODParam(float segmentLengthRatio, float minTangentLength, float maxTangentLength, float pointDeltaDistance, int maxPointCountInOneSegment, bool moreRectangular, float lineWidth, float textureAspectRatio, float gridErrorThreshold, Material edgeMaterial, Material regionMaterial, bool useVertexColorForRegionMesh, bool combineMesh, bool mergeEdge, float edgeHeight, bool shareEdge)
        {
            this.segmentLengthRatio = segmentLengthRatio;
            this.minTangentLength = minTangentLength;
            this.maxTangentLength = maxTangentLength;
            this.pointDeltaDistance = pointDeltaDistance;
            this.maxPointCountInOneSegment = maxPointCountInOneSegment;
            this.moreRectangular = moreRectangular;
            this.lineWidth = lineWidth;
            this.textureAspectRatio = textureAspectRatio;
            this.edgeMaterial = edgeMaterial;
            this.regionMaterial = regionMaterial;
            this.useVertexColorForRegionMesh = useVertexColorForRegionMesh;
            this.gridErrorThreshold = gridErrorThreshold;
            this.combineMesh = combineMesh;
            this.mergeEdge = mergeEdge;
            this.edgeHeight = edgeHeight;
            this.shareEdge = shareEdge;
        }

        public float segmentLengthRatio = 0.3f;
        public float minTangentLength = 10.0f;
        public float maxTangentLength = 30.0f;
        public float pointDeltaDistance = 10.0f;
        public int maxPointCountInOneSegment = 10;
        public bool moreRectangular = false;
        public float lineWidth = 30;
        public float textureAspectRatio = 2.0f;
        public float gridErrorThreshold;
        public Material edgeMaterial;
        public Material regionMaterial;
        public bool useVertexColorForRegionMesh;
        public bool combineMesh;
        public bool mergeEdge;
        public bool shareEdge;
        public float edgeHeight;
    }

    public class CurveRegionMeshGenerationParam
    {
        public CurveRegionMeshGenerationParam(float vertexDisplayRadius, float segmentLengthRatioRandomRange, float tangentRotationRandomRange, List<CurveRegionMeshGenerationLODParam> lodParams)
        {
            this.vertexDisplayRadius = vertexDisplayRadius;
            this.segmentLengthRatioRandomRange = segmentLengthRatioRandomRange;
            this.tangentRotationRandomRange = tangentRotationRandomRange;
            this.lodParams = lodParams;
        }

        public float vertexDisplayRadius = 2.5f;        
        public float segmentLengthRatioRandomRange = 0.1f;
        public float tangentRotationRandomRange = 20.0f;
        public List<CurveRegionMeshGenerationLODParam> lodParams;
    }

    public class EditorTerritoryMeshGenerationParam
    {
        public EditorTerritoryMeshGenerationParam(int cornerSegment, float borderSizeRatio, float uvScale, bool curveCorner, string territoryMaterialGuid)
        {
            this.cornerSegment = cornerSegment;
            this.borderSizeRatio = borderSizeRatio;
            this.uvScale = uvScale;
            this.curveCorner = curveCorner;

            territoryMeshMaterial = Utils.GetAssetFromGuid<Material>(territoryMaterialGuid);

            if (territoryMeshMaterial == null)
            {
                territoryMeshMaterial = AssetDatabase.LoadAssetAtPath<Material>(MapModule.defaultTerritoryMaterialPath);
            }
        }

        public int cornerSegment;
        public float borderSizeRatio;
        public float uvScale;
        public bool curveCorner;
        public Material territoryMeshMaterial;
    }

    public partial class EditorTerritoryLayer : MapLayerBase
    {
        public EditorTerritoryLayer(Map map) : base(map)
        {
        }

        public override void OnDestroy()
        {
            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                mSubLayers[i].OnDestroy();
            }
            mSubLayers = null;
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.EditorTerritoryLayerData;
            if (sourceLayer == null)
            {
                return;
            }

            //读取LOD配置信息
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                mLayerLODConfig = new MapLayerLODConfig(map, lods);
            }

            mRoot = new GameObject(MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY);
            mRoot.transform.SetParent(map.view.root.transform, true);
            mRoot.transform.position = sourceLayer.origin;
            mRoot.SetActive(layerData.active);

            int subLayerCount = sourceLayer.subLayers.Length;
            for (int i = 0; i < subLayerCount; ++i)
            {
                var subLayer = new EditorTerritorySubLayer();
                subLayer.Load(sourceLayer.subLayers[i], mRoot.transform);
                mSubLayers.Add(subLayer);
                if (i == 0)
                {
                    subLayer.active = true;
                }
                else
                {
                    subLayer.active = false;
                }
            }
            mLayerID = sourceLayer.id;

            map.AddMapLayer(this);
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public void SetGrid(int layer, Vector3 pos, int brushSize, int type)
        {
            mSubLayers[layer].SetGrid(pos, brushSize, type);
        }

        public void SetGridData(int layer, int x, int y, int type)
        {
            mSubLayers[layer].SetGridData(x, y, type);
        }

        public int GetGridData(int layer, int x, int y)
        {
            return mSubLayers[layer].GetGridData(x, y);
        }

        public int GetGridData(int layer, Vector3 pos)
        {
            return mSubLayers[layer].GetGridData(pos);
        }

        public EditorTerritory FindTerritory(int layer, int type)
        {
            return mSubLayers[layer].FindTerritory(type);
        }

        public void AddBuilding(int layer, Vector3 pos, int type)
        {
            mSubLayers[layer].AddBuilding(pos, type);
        }

        public EditorTerritory AddTerritory(int layer, string name, int type, Color color, PropertyDatas properties, Vector3[] buildingPositions)
        {
            return mSubLayers[layer].AddTerritory(name, type, color, properties, buildingPositions);
        }

        public void RemoveTerritory(int layer, int index)
        {
            mSubLayers[layer].RemoveTerritory(index);
        }

        EditorTerritory GetTerritory(int layer, int type)
        {
            return mSubLayers[layer].GetTerritory(type);        
        }

        public void RemoveTerritoryBuilding(EditorTerritoryBuilding building, bool destroyGameObject)
        {
            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                mSubLayers[i].RemoveTerritoryBuilding(building, destroyGameObject);
            }
        }

        public Vector3 FromCoordinateToPositionCenter(int layer, int x, int y)
        {
            return mSubLayers[layer].FromCoordinateToPositionCenter(x, y);
        }

        public Vector3 FromCoordinateToPosition(int layer, int x, int y)
        {
            return mSubLayers[layer].FromCoordinateToPosition(x, y);
        }

        public Vector2Int FromPositionToCoordinate(int layer, Vector3 pos)
        {
            return mSubLayers[layer].FromPositionToCoordinate(pos);
        }

        public Vector3 GetTerritoryCenter(int layer, int id)
        {
            return mSubLayers[layer].GetTerritoryCenter(id);
        }

        public Rect GetTerritoryWorldBounds(int layer, int id)
        {
            return mSubLayers[layer].GetTerritoryWorldBounds(id);
        }

        public bool HasRegionData(int layer)
        {
            return mSubLayers[layer].HasRegionData();
        }

        public List<Vector2Int> GetTerritoryCoordinates(int layer, int id)
        {
            return mSubLayers[layer].GetTerritoryCoordinates(id);
        }

        string ConvertToStringList(List<Vector2Int> coordinates)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("[");
            for (int i = 0; i < coordinates.Count; ++i)
            {
                int v = coordinates[i].x * 10000 + coordinates[i].y;
                builder.Append(v);
                if (i != coordinates.Count - 1)
                {
                    builder.Append(",");
                }
            }
            builder.Append("]");
            return builder.ToString();
        }

        public EditorTerritoryMeshGenerationParam GetMeshGenerationParam(int layer, int lod)
        {
            return mSubLayers[layer].GetMeshGenerationParam(lod);
        }

        public void OnLODCountChanged(int oldLODCount, int newLODCount)
        {
            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                mSubLayers[i].OnLODCountChanged(oldLODCount, newLODCount);
            }
        }

        public short GetTerritoryIndex(int layer, int id)
        {
            return mSubLayers[layer].GetTerritoryIndex(id);
        }

        public void SetActive(int layer, bool active)
        {
            mSubLayers[layer].active = active;
        }

        public bool IsActive(int layer)
        {
            return mSubLayers[layer].active;
        }

        public List<EditorTerritory> GetTerritories(int layer)
        {
            return mSubLayers[layer].territories;
        }

        public int GetTerritoryCount(int layer)
        {
            return mSubLayers[layer].territoryCount;
        }

        public int[,] GetGrids(int layer)
        {
            return mSubLayers[layer].grids;
        }

        public void Export(string folder)
        {
            for (int i = 0; i < mSubLayers.Count; ++i)
            {
                mSubLayers[i].Export(folder);
            }
        }

        public void Export(int layer, string folder)
        {
            mSubLayers[layer].Export(folder);
        }

        public void Import(int layer, string filePath)
        {
            mSubLayers[layer].Import(filePath);
        }
        
        public override bool Contains(int objectID)
        {
            return false;
        }
        public override int GetCurrentLOD() { return 0; }
        public override MapLayerData GetLayerData() { return null; }
        public override MapLayerView GetLayerView() { return null; }
        //加载完地图时刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport() { }
        public override bool UpdateViewport(Rect newViewport, float newCameraZoom) { return false; }
        //显示地图层中所有的对象,而不考虑视野的范围.给编辑器使用
        //返回地图层的总宽度
        public override float GetTotalWidth() { Debug.Assert(false, "not implemented"); return 0; }
        //返回地图层的总高度
        public override float GetTotalHeight() { Debug.Assert(false, "not implemented"); return 0; }
        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset) { return false; }

        //地图层的名称
        public override string name { get { return MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY; } set { } }
        //地图层的格子类型
        public override GridType gridType { get { return GridType.Rectangle; } }
        //x方向上格子的数量
        public override int horizontalTileCount { get { Debug.Assert(false, "not implemented"); return 0; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { Debug.Assert(false, "not implemented"); return 0; } }
        //格子的宽
        public override float tileWidth { get { Debug.Assert(false, "not implemented"); return 0; } }
        //格子的高
        public override float tileHeight { get { Debug.Assert(false, "not implemented"); return 0; } }
        //地图层的偏移值
        public override Vector3 layerOffset { get { return new Vector3(0, mRoot.transform.position.y, 0); } }
        public override int lodCount { get { return mLayerLODConfig.lodConfigs.Length; } }
        public override bool active { get { return mRoot.activeSelf; } set { mRoot.SetActive(value); } }
        public override GameObject gameObject => mRoot;
        public override int id => mLayerID;

        public float GetDisplayRadius(int layer)
        {
            return mSubLayers[layer].displayRadius;
        }

        public void SetDisplayRadius(int layer, float radius)
        {
            mSubLayers[layer].displayRadius = radius;
        }

        public List<EditorTerritoryMeshGenerationParam> GetMeshGenerationParams(int layer)
        {
            return mSubLayers[layer].meshGenerationParams;
        }

        public CurveRegionMeshGenerationParam GetCurveRegionMeshGenerationParam(int layer)
        {
            return mSubLayers[layer].curveRegionMeshGenerationParam;
        }

        public void ShowGrid(int layer, bool show)
        {
            mSubLayers[layer].showGrid = show;
        }
        
        public bool IsGridVisible(int layer)
        {
            return mSubLayers[layer].showGrid;
        }

        public void AddSubLayer(EditorTerritorySubLayer subLayer)
        {
            mSubLayers.Add(subLayer);
        }

        public void RemoveSubLayer(int idx)
        {
            if (idx >= 0 && idx < mSubLayers.Count)
            {
                mSubLayers[idx].OnDestroy();
                mSubLayers.RemoveAt(idx);
            }
        }

        public EditorTerritorySubLayer GetSubLayer(int idx) { 
            if (idx >= 0 && idx < mSubLayers.Count)
            {
                return mSubLayers[idx];
            }
            return null;
        }

        public void CreateOutline(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.CreateOutline(lod, false);
            }
        }

        public void GenerateOutlineAssets(int idx, string folder, int lod, bool generateAssets, float layerWidth, float layerHeight, int horizontalTileCount, int verticalTileCount)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.GenerateOutlineAssets(folder, idx, lod, false, generateAssets, layerWidth, layerHeight, horizontalTileCount, verticalTileCount);
            }
        }

        public void HideLineAndMesh(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.HideLineAndMesh(lod);
            }
        }

        public void HideLine(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.HideLine(lod);
            }
        }

        public void HideMesh(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.HideMesh(lod);
            }
        }

        public void HideRegionMesh(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.HideRegionMesh(lod);
            }
        }

        public void ShowMesh(int idx, int lod)
        {
            var layer = GetSubLayer(idx);
            if (layer != null)
            {
                layer.ShowMesh(lod);
            }
        }

        public string GetUniqueSubLayerName(string name)
        {
            int k = 0;
            string originalName = name;
            while (true)
            {
                bool found = false;
                for (int i = 0; i < mSubLayers.Count; ++i)
                {
                    if (mSubLayers[i].name == name)
                    {
                        name = $"{originalName}{k}";
                        found = true;
                        ++k;
                        break;
                    }
                }
                if (!found)
                {
                    break;
                }
            }
            return name;
        }

        public MapLayerLODConfig lodConfig { get { return mLayerLODConfig; } }
        public List<EditorTerritorySubLayer> subLayers { get { return mSubLayers; } }

        MapLayerLODConfig mLayerLODConfig;
        int mLayerID;
        GameObject mRoot;
        List<EditorTerritorySubLayer> mSubLayers = new List<EditorTerritorySubLayer>();
    }
}

#endif