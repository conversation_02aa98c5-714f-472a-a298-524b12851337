﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map.Nav
{
    public partial class NavigationDebugger
    {
        class ConvexObject
        {
            public GameObject obj = null;
            public int index = 0;
        }

        public void HighlightConvex(Geo.Coord pos, bool toggleWalkable)
        {
            var obj = GetConvexObject(pos);
            if (obj != null && (obj != mHighlightedConvex || toggleWalkable))
            {
                if (mHighlightedConvex != null)
                {
                    DisableConvexHighlighting();
                }
                EnableConvexHighlighting(obj, toggleWalkable);
            }
        }

        public void ShowConvex(Geo.Coord pos)
        {
            var index = GetConvexIndex(pos);
            for (int i = 0; i < mConvexs.Count; ++i)
            {
                if (mConvexs[i].index == index)
                {
                    mConvexs[i].obj.SetActive(true);
                }
            }
        }

        public void HideAllConvexs()
        {
            DisableConvexHighlighting();
            for (int i = 0; i < mConvexs.Count; ++i)
            {
                mConvexs[i].obj.SetActive(false);
            }
        }

        public void ShowAllConvexs()
        {
            DisableConvexHighlighting();
            for (int i = 0; i < mConvexs.Count; ++i)
            {
                mConvexs[i].obj.SetActive(true);
            }
        }

        int GetConvexIndex(Geo.Coord pos)
        {
            //var convexes = mNavMgr.mesh.convexes;
            //for (int i = 0; i < convexes.Length; ++i)
            //{
            //    if (convexes[i].IsCoordInside(pos))
            //    {
            //        return convexes[i].index;
            //    }
            //}
            return -1;
        }

        ConvexObject GetConvexObject(Geo.Coord pos)
        {
            var idx = GetConvexIndex(pos);
            if (idx >= 0)
            {
                for (int i = 0; i < mConvexs.Count; ++i)
                {
                    if (mConvexs[i].index == idx)
                    {
                        return mConvexs[i];
                    }
                }
            }
            return null;
        }

        void DestroyConvexs()
        {
            for (int i = 0; i < mConvexs.Count; ++i)
            {
                var mesh = mConvexs[i].obj.GetComponent<MeshFilter>().sharedMesh;
                var mtl = mConvexs[i].obj.GetComponent<MeshRenderer>().sharedMaterial;
                GameObject.DestroyImmediate(mesh);
                GameObject.DestroyImmediate(mtl);
                GameObject.DestroyImmediate(mConvexs[i].obj);
            }
            mConvexs = null;
        }

        void CreateConvexs(GameObject root)
        {
            //var mesh = mNavMgr.mesh;
            //var convexes = mesh.convexes;
            //var convexMap = new Dictionary<int, Geo.Convex>();

            //foreach (var convex in convexes)
            //{
            //    convexMap[convex.index] = convex;
            //}

            //foreach (var p in convexMap)
            //{
            //    mConvexs.Add(CreateConvexObject(p.Value, root));
            //}
        }

        //ConvexObject CreateConvexObject(Geo.Convex t, GameObject root)
        //{
        //    string name;
        //    Vector3 center;
        //    var mesh = CreateConvexMesh(t, out center, out name);
        //    var obj = new GameObject(name);
        //    obj.SetActive(false);
        //    obj.transform.parent = root.transform;
        //    var filter = obj.AddComponent<MeshFilter>();
        //    var renderer = obj.AddComponent<MeshRenderer>();

        //    filter.sharedMesh = mesh;
        //    renderer.sharedMaterial = CreateConvexMaterial();
        //    ConvexObject tri = new ConvexObject();
        //    tri.index = t.index;
        //    tri.obj = obj;
        //    center.y = 1.0f;
        //    obj.transform.position = center;

        //    Font ArialFont = (Font)Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
        //    var textObj = new GameObject();
        //    var textObjRenderer = textObj.AddComponent<MeshRenderer>();
        //    textObjRenderer.sharedMaterial = ArialFont.material;
        //    var textMesh = textObj.AddComponent<TextMesh>();
        //    textMesh.text = $"{t.index}";
        //    textMesh.fontSize = 32;
        //    textObj.transform.parent = obj.transform;
        //    textObj.transform.position = obj.transform.position;
        //    textObj.transform.rotation = Quaternion.Euler(70, 0, 0);
        //    textObj.transform.localScale = Vector3.one * 10.0f;
        //    textObj.SetActive(false);
        //    return tri;
        //}

        //UnityEngine.Mesh CreateConvexMesh(Geo.Convex t, out Vector3 center, out string name)
        //{
        //    UnityEngine.Mesh m = new UnityEngine.Mesh();
        //    int n = t.vertices.Count;
        //    List<Vector3> vertices = new List<Vector3>(t.vertices.Count);
        //    for (int i = 0; i < n; ++i)
        //    {
        //        vertices.Add(Geo.GeoUtils.CoordToVector3(t.vertices[i].Coord));
        //    };

        //    name = $"convex {t.index},";
        //    for (int i = 0; i < n; ++i)
        //    {
        //        name += $"v{i}: {vertices[i].ToString()}";
        //    }

        //    center = Vector3.zero;
        //    for (int i = 0; i < n; ++i)
        //    {
        //        center += vertices[i];
        //    }
        //    center /= n;
        //    for (int i = 0; i < n; ++i)
        //    {
        //        vertices[i] -= center;
        //    }

        //    List<Vector3> splitedVertices;
        //    List<int> splitedIndices;
        //    TFW.Map.Triangulator.TriangulatePolygon(vertices, out splitedVertices, out splitedIndices);
        //    m.SetVertices(splitedVertices);
        //    m.triangles = splitedIndices.ToArray();
        //    return m;
        //}

        Material CreateConvexMaterial()
        {
            var mtl = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            var color = Color.yellow;
            color.a = 0.3f;
            mtl.color = mDefaultColor;
            mtl.renderQueue = 4999;
            return mtl;
        }

        void EnableConvexHighlighting(ConvexObject obj, bool toggleWalkable)
        {
#if false
            var convexs = mNavMgr.mesh.convexes;
            if (toggleWalkable)
            {
                //mNavMgr.mesh.SetConvexWalkable(obj.index, !convexs[obj.index].IsWalkable());
            }
            //Debug.Log($"Enable Highlight {obj.index}");
            Debug.Assert(mHighlightedConvex == null);
            mHighlightedConvex = obj;
            var mtl = obj.obj.GetComponent<MeshRenderer>().sharedMaterial;
            //if (!convexs[obj.index].IsWalkable())
            //{
            //    mtl.color = mObstacleColor;
            //}
            //else
            //{
            mtl.color = mHighlightColor;
            //}
            mHighlightedConvex.obj.SetActive(true);
#endif
        }

        public void DisableConvexHighlighting()
        {
#if false
            if (mHighlightedConvex != null)
            {
                //Debug.Log($"Disable Highlight {mHighlightedConvex.index}");
                var convexs = mNavMgr.mesh.convexes;
                var mtl = mHighlightedConvex.obj.GetComponent<MeshRenderer>().sharedMaterial;
                //if (!convexs[mHighlightedConvex.index].IsWalkable())
                //{
                //    mtl.color = mObstacleColor;
                //}
                //else
                //{
                mtl.color = mDefaultColor;
                //}
                mHighlightedConvex = null;
            }
#endif
        }

        List<ConvexObject> mConvexs = new List<ConvexObject>();
        ConvexObject mHighlightedConvex = null;
        Color32 mDefaultColor = new Color32(0, 0, 255, 200);
        Color32 mHighlightColor = new Color32(255, 0, 0, 200);
        Color32 mObstacleColor = new Color32(0, 0, 0, 200);
    }
}

#endif