%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Lava
  m_Shader: {fileID: 4800000, guid: 8bc4e62ea9d964448a2c2e539d65b77a, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 4}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _FoamTex
      second:
        m_Texture: {fileID: 2800000, guid: 869319dead08c3b4a9c074af06b585f4, type: 3}
        m_Scale: {x: 3, y: 3}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 929f2fb0e4fa033409d770aa87bea9b9, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex2
      second:
        m_Texture: {fileID: 2800000, guid: 929f2fb0e4fa033409d770aa87bea9b9, type: 3}
        m_Scale: {x: 3, y: 3}
        m_Offset: {x: 0.5, y: 0.5}
    - first:
        name: _Mask1
      second:
        m_Texture: {fileID: 2800000, guid: 532c9e2e0905df744860e5e5c36ada26, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Mask2
      second:
        m_Texture: {fileID: 2800000, guid: 929f2fb0e4fa033409d770aa87bea9b9, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DepthAlpha
      second: 1.54
    - first:
        name: _DepthDistance
      second: 1.61
    - first:
        name: _DepthMinAlpha
      second: 0.4
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _DstBlendTCP2
      second: 10
    - first:
        name: _EmisPulseMin
      second: 0
    - first:
        name: _EmisPulseSpeed
      second: 2
    - first:
        name: _FoamSmooth
      second: 0.296
    - first:
        name: _FoamSpread
      second: 2
    - first:
        name: _FoamStrength
      second: 1
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _NormalDepthInfluence
      second: 0.142
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.641
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _ReflRoughness
      second: 0
    - first:
        name: _ReflStrength
      second: 1
    - first:
        name: _RimMax
      second: 1
    - first:
        name: _RimMin
      second: 0
    - first:
        name: _Shininess
      second: 10
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecSmooth
      second: 0.05
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _SrcBlendTCP2
      second: 5
    - first:
        name: _UVScrolling2X
      second: 0.12
    - first:
        name: _UVScrolling2Y
      second: 0.2
    - first:
        name: _UVScrollingX
      second: 0.06
    - first:
        name: _UVScrollingY
      second: 0.17
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _UVWaveAmplitude
      second: 0.05
    - first:
        name: _UVWaveAmplitude2
      second: 0.03
    - first:
        name: _UVWaveFrequency
      second: 0.3
    - first:
        name: _UVWaveFrequency2
      second: 0.1
    - first:
        name: _UVWaveSpeed
      second: 0.5
    - first:
        name: _UVWaveSpeed2
      second: 0.8
    - first:
        name: _WaveFrequency
      second: 4
    - first:
        name: _WaveHeight
      second: 0.05
    - first:
        name: _WaveSpeed
      second: 1
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    m_Colors:
    - first:
        name: _BumpSpeed
      second: {r: 0.2, g: 0.2, b: -0.2, a: -0.2}
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _DepthColor
      second: {r: 0.63529414, g: 0.09411766, b: 0, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _FoamColor
      second: {r: 1, g: 0.68275857, b: 0, a: 1}
    - first:
        name: _FoamColor2
      second: {r: 1, g: 0.93103445, b: 0, a: 1}
    - first:
        name: _FoamSpeed
      second: {r: 0.5, g: 0.5, b: -0.5, a: -0.5}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _MaskedColor
      second: {r: 1, g: 0, b: 0, a: 1}
    - first:
        name: _RimColor
      second: {r: 1, g: 0.6827586, b: 0, a: 1}
    - first:
        name: _SColor
      second: {r: 0.44852942, g: 0, b: 0, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.1297578, g: 0.14705884, b: 0.14013842, a: 1}
