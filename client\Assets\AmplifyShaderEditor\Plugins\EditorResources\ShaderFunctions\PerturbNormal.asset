%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: PerturbNormal
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=15103\n344;92;1056;673;575.1386;503.1177;1.033186;True;False\nNode;AmplifyShaderEditor.BreakToComponentsNode;27;760.8596,55.21472;Float;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;24;567.4818,-78.97424;Float;False;2;2;0;FLOAT3x3;0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;31;80,-272;Float;False;CotangentFrame;-1;;7;62ce0f00f1417804bb4f2b38501ba0d0;0;3;4;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT2;0,0;False;1;FLOAT3x3;5\nNode;AmplifyShaderEditor.FunctionInput;6;-160,48;Float;False;Normal;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;13;-176,-400;Float;False;True;1;0;FLOAT3;0,0,0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NormalizeNode;8;64,112;Float;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;12;-160,-256;Float;False;World;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;11;-176,-96;Float;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;26;256,48;Float;False;Normalize;True;0;2;-1;In
    0;In 1;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;29;1040,80;Float;False;False;Y;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;28;1040,0;Float;False;False;X;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;9;1040,-80;Float;False;True;XYZ;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;30;1040,160;Float;False;False;Z;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;27;0;24;0\nWireConnection;24;0;31;5\nWireConnection;24;1;26;0\nWireConnection;31;4;13;0\nWireConnection;31;2;12;0\nWireConnection;31;1;11;0\nWireConnection;8;0;6;0\nWireConnection;26;0;6;0\nWireConnection;26;1;8;0\nWireConnection;29;0;27;1\nWireConnection;28;0;27;0\nWireConnection;9;0;24;0\nWireConnection;30;0;27;2\nASEEND*/\n//CHKSM=82B74CD48F60956E292CC6A0881ADA96C894E5CD"
  m_functionName: 
  m_description: 'Perturbing normals without Precomputed Tangents

    http://www.thetenthplanet.de/archives/1180'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
