﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class Optimized<PERSON>esh<PERSON><PERSON>iner
    {
        class MeshData
        {
            public MeshData(Vector3[] vertices, int[] indices, Matrix4x4 transform)
            {
                this.vertices = vertices;
                this.indices = indices;
                this.transform = transform;
            }

            public MeshData(Vector3[] vertices, int[] indices)
            {
                this.vertices = vertices;
                this.indices = indices;
            }
            public Vector3[] vertices;
            public int[] indices;
            public Matrix4x4 transform;
        }

        public OptimizedMeshCombiner(float distanceError)
        {
            mVertexIndexMap = new SortedDictionary<Vector3, int>(new CompareVector3(distanceError));
        }

        public void AddMesh(Vector3[] vertices, int[] indices, Matrix4x4 transform)
        {
            mMeshies.Add(new MeshData(vertices, indices, transform));
        }

        public void AddMesh(Vector3[] vertices, int[] indices)
        {
            mMeshies.Add(new MeshData(vertices, indices));
        }

        public void Combine(bool useTransform, out Vector3[] vertices, out int[] indices)
        {
            int n = mMeshies.Count;
            int totalIndexCount = GetTotalIndexCount();
            indices = new int[totalIndexCount];
            int offset = 0;
            for (int i = 0; i < n; ++i)
            {
                if (mMeshies[i].indices != null)
                {
                    var mi = mMeshies[i].indices;
                    var mv = mMeshies[i].vertices;
                    if (useTransform)
                    {
                        var transform = mMeshies[i].transform;
                        for (int j = 0; j < mi.Length; ++j)
                        {
                            var pos = transform.MultiplyPoint(mv[mi[j]]);
                            var combinedIndex = GetVertexIndex(pos);
                            indices[offset] = combinedIndex;
                            ++offset;
                        }
                    }
                    else
                    {
                        for (int j = 0; j < mi.Length; ++j)
                        {
                            var combinedIndex = GetVertexIndex(mv[mi[j]]);
                            indices[offset] = combinedIndex;
                            ++offset;
                        }
                    }
                }
            }

            vertices = mCombinedVertices.ToArray();
        }

        int GetVertexIndex(Vector3 pos)
        {
            int index;
            bool found = mVertexIndexMap.TryGetValue(pos, out index);
            if (found)
            {
                return index;
            }

            mCombinedVertices.Add(pos);
            index = mCombinedVertices.Count - 1;
            mVertexIndexMap.Add(pos, index);
            return index;
        }

        int GetTotalIndexCount()
        {
            int n = 0;
            for (int i = 0; i < mMeshies.Count; ++i)
            {
                if (mMeshies[i].indices != null)
                {
                    n += mMeshies[i].indices.Length;
                }
            }
            return n;
        }

        List<Vector3> mCombinedVertices = new List<Vector3>();
        List<MeshData> mMeshies = new List<MeshData>();
        SortedDictionary<Vector3, int> mVertexIndexMap;
    }
}