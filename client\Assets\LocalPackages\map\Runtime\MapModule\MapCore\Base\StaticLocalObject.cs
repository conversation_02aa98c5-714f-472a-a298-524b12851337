﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    class StaticLocalObject : MonoBehaviour
    {
        void Start()
        {
            mInitPosition = transform.localPosition;
            mInitRotation = transform.localRotation;
            mInitScale = transform.localScale;
        }

        void Update()
        {
            if (mInitPosition != transform.localPosition)
            {
                transform.localPosition = mInitPosition;
            }
            if (mInitScale != transform.localScale)
            {
                transform.localScale = mInitScale;
            }
            if (mInitRotation != transform.localRotation)
            {
                transform.localRotation = mInitRotation;
            }
        }

        Vector3 mInitPosition;
        Quaternion mInitRotation;
        Vector3 mInitScale;
    }
}
