// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'
// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

// Upgrade NOTE: replaced '_Object2World' with '_Object2World'

Shader "K1/Particle/Distortion/Additive" {
	Properties {
		[HDR]_TintColor ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
		_MainTex ("Main Texture", 2D) = "black" {}
	}

	Category {

		Tags { "Queue"="Transparent"  "IgnoreProjector"="True"  "RenderType"="Transparent" }
		Blend SrcAlpha OneMinusSrcAlpha
		Cull Off 
		Lighting Off 
		ZWrite Off 
		Fog { Mode Off}

		SubShader {
		Pass {
				Blend SrcAlpha One
				
				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				#pragma fragmentoption ARB_precision_hint_fastest
				#include "UnityCG.cginc"

				struct appdata_t {
					float4 vertex : POSITION;
					float2 texcoord: TEXCOORD0;
					half4 color : COLOR;
				};

				struct v2f {
					float4 vertex : POSITION;
					float2 uvmain : TEXCOORD0;
					half4 color : COLOR;
				};

				sampler2D _MainTex;
				float4 _MainTex_ST;
				float4 _TintColor;

				v2f vert (appdata_t v)
				{
					v2f o;
					o.vertex = UnityObjectToClipPos(v.vertex);
					o.color = v.color;
					o.uvmain.rg = TRANSFORM_TEX( v.texcoord, _MainTex);
					return o;
				}

				half4 frag( v2f i ) : COLOR
				{
					half4 tex = tex2D( _MainTex, i.uvmain.rg);
					return tex * _TintColor * 5 * i.color.a;
				}
				ENDCG
			}
		}
	}
}
