﻿ 



 
 

using System;
using UnityEngine;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        SpriteObject RequireSprite(float x, float z, int type, int gx, int gy, int groupIndex, float height)
        {
            var sprite = mSpritePool.Require();
            var path = mSpriteGroups[groupIndex].sprites[type];
            sprite.obj = mObjectPool.Require(path);
            sprite.obj.transform.position = new Vector3(x, height, z);
            sprite.type = type;
            sprite.gx = gx;
            sprite.gy = gy;
            sprite.groupIndex = groupIndex;

            mSprites.Add(sprite);

            return sprite;
        }

        void ReleaseSprite(SpriteObject sprite)
        {
            var path = mSpriteGroups[sprite.groupIndex].sprites[sprite.type];
            mObjectPool.Release(path, sprite.obj, mMap);
            mSpritePool.Release(sprite);
            int x = sprite.gx / mHorizontalGridCount;
            int y = sprite.gy / mHorizontalGridCount;
            int sx = sprite.gx % mHorizontalGridCount;
            int sy = sprite.gy % mHorizontalGridCount;
            bool suc = mSubGridObjects[y, x].objs[sy, sx].Remove(sprite);
            Debug.Assert(suc);
            mSprites.Remove(sprite);
            sprite.gx = -1;
            sprite.gy = -1;
        }

        void ReleaseAllSprites()
        {
            for (int i = mSprites.Count - 1; i >= 0; --i)
            {
                ReleaseSprite(mSprites[i]);
            }
        }

        void CreateSpriteGameObject(string prefabPath,Action<GameObject> callBack)
        {
            MapModuleResourceMgr.LoadGameObjectAsync(prefabPath, null, (_path, obj) => {
                if (obj != null)
                {
                    var spriteRenderer = obj.GetComponentInChildren<Renderer>();
                    if (spriteRenderer != null)
                    {
                        spriteRenderer.sharedMaterial = mSpriteMaterial;
                    }
                    callBack?.Invoke(obj);
                }
            }); 
        }

        ObjectPool<SpriteObject> mSpritePool = new ObjectPool<SpriteObject>(1000, () => new SpriteObject());
        GameObjectPool mObjectPool;
        Map mMap;
    }
}
