﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class RegionColorLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.RegionColorLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.showRegionInGame);

            int layerCount = mLayerData.layerCount;
            writer.Write(layerCount);
            for (int s = 0; s < layerCount; ++s)
            {
                var layer = mLayerData.GetLayer(s);
                Utils.WriteString(writer, layer.name);
                writer.Write(layer.tileWidth);
                writer.Write(layer.tileHeight);
                writer.Write(layer.horizontalTileCount);
                writer.Write(layer.verticalTileCount);

                var gridIDs = layer.gridIDs;
                for (int i = 0; i < layer.verticalTileCount; ++i)
                {
                    for (int j = 0; j < layer.horizontalTileCount; ++j)
                    {
                        writer.Write(gridIDs[i, j]);
                    }
                }
                var brushes = layer.grids;
                writer.Write(brushes.Count);
                for (int i = 0; i < brushes.Count; ++i)
                {
                    writer.Write(brushes[i].id);
                    var region = brushes[i] as RegionColorData;
                    Utils.WriteColor(writer, region.color);
                }
            }
        }
    }
}

#endif