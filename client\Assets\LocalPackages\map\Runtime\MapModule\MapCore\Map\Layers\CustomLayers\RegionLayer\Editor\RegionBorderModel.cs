﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionBorderModel
    {
        public RegionBorderModel()
        {
            mGameObject = new GameObject();
            mGameObject.AddComponent<MeshRenderer>();
            mGameObject.AddComponent<MeshFilter>();
            Utils.HideGameObject(mGameObject);
        }

        public void OnDestroy()
        {
            Utils.DestroyObject(mGameObject);
            mGameObject = null;
        }

        public void UpdateMesh(Mesh mesh, Material mtl)
        {
            var filter = mGameObject.GetComponent<MeshFilter>();
            Utils.DestroyObject(filter.sharedMesh);
            filter.sharedMesh = mesh;

            var renderer = mGameObject.GetComponent<MeshRenderer>();
            renderer.sharedMaterial = mtl;
        }

        public void SetVisible(bool visible)
        {
            mGameObject.SetActive(visible);
        }

        public Mesh GetMesh()
        {
            var filter = mGameObject.GetComponent<MeshFilter>();
            return filter.sharedMesh;
        }

        GameObject mGameObject;
    }
}

#endif