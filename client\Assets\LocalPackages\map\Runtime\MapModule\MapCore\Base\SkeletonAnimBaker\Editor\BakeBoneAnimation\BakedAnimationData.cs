﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    //烘培的动画数据
    public class BakedAnimationData : BakedAnimationDataBase
    {
        public Vector3[] cpuDrivenCustomBoneTranslations;
        public Quaternion[] cpuDrivenCustomBoneRotations;
        public Vector3[] cpuDrivenCustomBoneScalings;
        public string[] cpuDrivenCustomBoneNames;
        //是否将cpu driven bone的信息独立出去,放到单独的文件中,方便多个prefab共享
        public BakedCPUDrivenBoneTransformData cpuDrivenBoneTransformData;

        public override string[] GetCPUDrivenCustomBoneNames()
        {
            if (cpuDrivenBoneTransformData != null)
            {
                return cpuDrivenBoneTransformData.boneNames;
            }
            return cpuDrivenCustomBoneNames;
        }

        public Vector3[] GetCPUDrivenCustomBoneTranslations()
        {
            if (cpuDrivenBoneTransformData != null)
            {
                return cpuDrivenBoneTransformData.translations;
            }
            return cpuDrivenCustomBoneTranslations;
        }

        public Quaternion[] GetCPUDrivenCustomBoneRotations()
        {
            if (cpuDrivenBoneTransformData != null)
            {
                return cpuDrivenBoneTransformData.rotations;
            }
            return cpuDrivenCustomBoneRotations;
        }

        public Vector3[] GetCPUDrivenCustomBoneScalings()
        {
            if (cpuDrivenBoneTransformData != null)
            {
                return cpuDrivenBoneTransformData.scalings;
            }
            return cpuDrivenCustomBoneScalings;
        }
    }
}
