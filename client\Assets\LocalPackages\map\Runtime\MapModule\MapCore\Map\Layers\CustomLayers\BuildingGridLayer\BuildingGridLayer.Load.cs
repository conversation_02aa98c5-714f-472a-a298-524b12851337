﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class BuildingGridLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, Map.currentMap.nextCustomObjectID, version);

            reader.Close();

            var layer = new BuildingGridLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.MapLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            Vector3 gridStartPos = Utils.ReadVector3(reader);

            int layerCount = reader.ReadInt32();
            config.TextureGridLayer[] layers = new config.TextureGridLayer[layerCount];
            for (int s = 0; s < layerCount; ++s)
            {
                var name = Utils.ReadString(reader);
                var tileWidth = reader.ReadSingle();
                var tileHeight = reader.ReadSingle();
                var horizontalTileCount = reader.ReadInt32();
                var verticalTileCount = reader.ReadInt32();
                var grids = new List<config.TextureGridData>();
                var gridIDs = new int[verticalTileCount, horizontalTileCount];
                for (int i = 0; i < verticalTileCount; ++i)
                {
                    for (int j = 0; j < horizontalTileCount; ++j)
                    {
                        gridIDs[i, j] = reader.ReadInt32();
                    }
                }
                int nRegions = reader.ReadInt32();
                for (int i = 0; i < nRegions; ++i)
                {
                    var id = reader.ReadInt32();
                    var color = Utils.ReadColor(reader);
                    bool walkable = reader.ReadBoolean();
                    var region = new config.BuildingGridData(id, color, walkable);
                    grids.Add(region);
                }

                layers[s] = new config.TextureGridLayer(name, horizontalTileCount, verticalTileCount, tileWidth, tileHeight, gridIDs, grids);
            }

            var layerData = new config.BuildingGridLayerData(layerID, layerName, layerOffset, layers, gridStartPos);
            layerData.active = active;
            return layerData;
        }
    }
}

#endif