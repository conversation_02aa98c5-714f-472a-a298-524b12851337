﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map {
    public class SpriteTemplateUI : EditorWindow {
        public void Show(int spriteTemplateID, bool showDefault, System.Action<string> onSpriteTemplateNameChange) {
            mSpriteTemplateID = spriteTemplateID;
            mShowDefault = showDefault;
            mOnSpriteTemplateNameChange = onSpriteTemplateNameChange;
        }

        void OnGUI() {
            var spriteTemplate = Map.currentMap.FindObject(mSpriteTemplateID) as SpriteTemplate;
            if (spriteTemplate != null) {
                var newName = EditorGUILayout.TextField("Name", spriteTemplate.name);
                if (newName != spriteTemplate.name) {
                    if (mOnSpriteTemplateNameChange != null) {
                        mOnSpriteTemplateNameChange(newName);
                    }
                }
                var newColor = EditorGUILayout.ColorField("Color", spriteTemplate.color);
                if (newColor != spriteTemplate.color) {
                    var act = new ActionChangeSpriteTemplateColor(mSpriteTemplateID, newColor);
                    ActionManager.instance.PushAction(act, false);
                }

                if (mShowDefault) {
                    spriteTemplate.isDefault = EditorGUILayout.Toggle("Default Tile", spriteTemplate.isDefault);
                }

                EditorGUILayout.BeginHorizontal();

                if (spriteTemplate.propertySetID != 0) {
                    var ps = Map.currentMap.FindObject(spriteTemplate.propertySetID) as PropertySet;
                    mPropertyName = ps.name;
                }

                EditorGUILayout.TextField("Property Set", mPropertyName);

                if (GUILayout.Button("Edit Property")) {
                    var propertyEditor = EditorWindow.GetWindow<PropertyEditor>("Property Editor");
                    EditorUtils.CenterWindow(propertyEditor);
                    propertyEditor.Show(spriteTemplate.id, spriteTemplate.propertySetID, (int propertySetID) => { spriteTemplate.propertySetID = propertySetID; });
                }

                if (GUILayout.Button("Reset")) {
                    spriteTemplate.propertySetID = 0;
                    mPropertyName = "";
                }

                EditorGUILayout.EndHorizontal();
            }
        }

        int mSpriteTemplateID;
        string mPropertyName = "";
        System.Action<string> mOnSpriteTemplateNameChange;
        bool mShowDefault;
    }
}

#endif