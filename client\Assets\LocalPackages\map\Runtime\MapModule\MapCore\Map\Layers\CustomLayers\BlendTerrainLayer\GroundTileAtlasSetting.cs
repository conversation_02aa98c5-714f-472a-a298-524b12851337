﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [System.Serializable]
    public class SpecialTextureAtlasSetting
    {
        public string texturePath = "";
        public string tag = "";
    }

    [CreateAssetMenu(fileName = "ground_tile_atlas_setting", menuName = "Assets/GroundTileAtlasSetting")]
    public class GroundTileAtlasSetting : ScriptableObject
    {
        public string atlasTexturePropertyName = "_MainTex";
        public int borderSize = 4;
        public bool rgbTextureAlphaIsOne = false;
        public bool useTileBlock = true;
        public int minimumTileCountInRectangle = 4;
        public Material material;
        public UnityEditor.DefaultAsset[] groundTileFolders = new UnityEditor.DefaultAsset[0];
        //folder的tag
        public string[] tags = new string[0];
        //是否将rgba贴图的alpha当成0
        public bool[] treatAlphaAsZeros = new bool[0];
        //针对特殊贴图设置tag,如果贴图tag和文件夹tag冲突,以贴图tag为准
        public SpecialTextureAtlasSetting[] specialTextureAtlasSettings = new SpecialTextureAtlasSetting[0];
    }
}

#endif