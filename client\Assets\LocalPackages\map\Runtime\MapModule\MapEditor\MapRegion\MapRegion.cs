﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MapRegion
    {
        //borderLineObstacles: 是否只在特殊区域内生成刷新点,当不为空时,先要求该区域与特殊区域的交集的,然后从交集里减去障碍物三角形
        public MapRegion(int index, Vector2 startPos, Vector2 regionSize, List<Vector3Int> obstacleTriangles, Vector3[] obstacleTriangleVertices, List<List<Vector3>> borderLinePolygons, float borderLineShrinkRadius, bool calculateArea)
        {
            mIndex = index;
            mRegionSize = regionSize;
            mStartPosition = startPos;

            CalculateValidPolygon(obstacleTriangles, obstacleTriangleVertices, borderLinePolygons, borderLineShrinkRadius, calculateArea);
        }

        void CalculateValidPolygon(List<Vector3Int> obstacleTrianglesIntersectedWithThisRegion, Vector3[] triangleVertices, List<List<Vector3>> borderLinePolygons, float borderLineShrinkRadius, bool calculateArea)
        {
            //StopWatchWrapper watch = new StopWatchWrapper();
            //watch.Start();

            //Debug.Assert(mValidPolygons.Count == 0);
            var min = mStartPosition;
            var max = mStartPosition + mRegionSize;

            List<List<Vector3>> regionEmptyPolygon = new List<List<Vector3>>()
            {
                new List<Vector3>() {
                new Vector3(min.x, 0, min.y),
                new Vector3(max.x, 0, min.y),
                new Vector3(max.x, 0, max.y),
                new Vector3(min.x, 0, max.y),
                }
            };


#if true
            if (borderLinePolygons != null)
            {
                //var w = new StopWatchWrapper();
                //w.Start();

                regionEmptyPolygon = PolygonAlgorithm.GetPolygonIntersections(min.x, min.y, max.x, max.y, borderLinePolygons, out _);
                //var specialTime = w.Stop();
                //Debug.Log($"special time: {specialTime}");
            }
#endif

#if true
            //var w1 = new StopWatchWrapper();
            //w1.Start();
            PolygonAlgorithm.GetDifferencePolygons(regionEmptyPolygon, obstacleTrianglesIntersectedWithThisRegion, triangleVertices, out mNoneHolePolygons, out mHolePolygons);
            //var diffTime = w1.Stop();
            //Debug.Log($"diff time: {diffTime}");

            for (int i = mNoneHolePolygons.Count - 1; i >= 0; --i)
            {
                mNoneHolePolygons[i] = Utils.RemoveDuplicated1(mNoneHolePolygons[i], 0.1f);
                if (mNoneHolePolygons[i].Count < 3)
                {
                    mNoneHolePolygons.RemoveAt(i);
                }
            }
            for (int i = mHolePolygons.Count - 1; i >= 0; --i)
            {
                mHolePolygons[i] = Utils.RemoveDuplicated1(mHolePolygons[i], 0.1f);
                if (mHolePolygons[i].Count < 3)
                {
                    mHolePolygons.RemoveAt(i);
                }
            }
#endif

#if true
            if (calculateArea)
            {
                //w1.Start();
                GetPolygons(mNoneHolePolygons, mHolePolygons);
                //diffTime = w1.Stop();
                //Debug.Log($"get polygons: {diffTime}");
            }
#endif

            //double elapsedTime = watch.Stop("CalculateValidPolygon");
            //Debug.Log("CalculateValidPolygon: " + elapsedTime);
        }

        void GetPolygons(List<List<Vector3>> noneHoles, List<List<Vector3>> holes)
        {
            mValidArea = 0;
            for (int i = 0; i < noneHoles.Count; ++i)
            {
                //int nPoints = noneHoles[i].Count;
                //Vector3[] polygon = new Vector3[nPoints];
                //for (int k = 0; k < nPoints; ++k)
                //{
                    //polygon[k] = noneHoles[i][k];
                //}

                //mValidPolygons.Add(noneHoles[i]);
                mValidArea += EditorUtils.CalculatePolygonArea(noneHoles[i]);
            }

            for (int i = 0; i < holes.Count; ++i)
            {
                //int nPoints = holes[i].Count;
                //Vector3[] polygon = new Vector3[nPoints];
                //for (int k = 0; k < nPoints; ++k)
                //{
                //    polygon[k] = new Vector3(holes[i][k].x, holes[i][k].y, holes[i][k].z);
                //}

                //mHoles.Add(holes[i]);
                mValidArea -= EditorUtils.CalculatePolygonArea(holes[i]);
            }

            //temp code
#if false
            for (int i = 0; i < mValidPolygons.Count; ++i)
            {
                var obj = new GameObject("valid polygon " + i.ToString());
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(mValidPolygons[i]);
            }

            for (int i = 0; i < mHoles.Count; ++i)
            {
                var obj = new GameObject("hole polygon " + i.ToString());
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(mHoles[i]);
            }
#endif
        }

        public bool IsInside(Vector3 pos)
        {
            var maxPos = endPosition;
            if (pos.x >= mStartPosition.x && pos.x <= maxPos.x &&
                pos.z >= mStartPosition.y && pos.z <= maxPos.y)
            {
                return true;
            }
            return false;
        }

        public bool IsInEmptySpace(Vector3 pos, float moveRange)
        {
            //是否在去掉了npcMoveRange的地图区域内
            if (IsInWorld(pos, moveRange))
            {
                for (int p = 0; p < mNoneHolePolygons.Count; ++p)
                {
                    if (EditorUtils.PointInPolygon2D(pos, mNoneHolePolygons[p]))
                    {
                        if (!IsInHoles(pos))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public bool IsInWorld(Vector3 pos, float npcMoveRange)
        {
            if (npcMoveRange <= 0)
            {
                return true;
            }

            var mapDataGenerationRange = Map.currentMap.data.mapDataGenerationRange;
            var min = mapDataGenerationRange.min;
            var max = mapDataGenerationRange.max;
            float minX = npcMoveRange + min.x;
            float maxX = max.x - npcMoveRange;
            float minZ = npcMoveRange + min.z;
            float maxZ = max.z - npcMoveRange;
            if (pos.x >= minX && pos.x <= maxX &&
                pos.z >= minZ && pos.z <= maxZ)
            {
                return true;
            }

            return false;
        }

        bool IsInHoles(Vector3 pos)
        {
            for (int i = 0; i < mHolePolygons.Count; ++i)
            {
                if (EditorUtils.PointInPolygon2D(pos, mHolePolygons[i]))
                {
                    return true;
                }
            }
            return false;
        }

        public Vector2 startPosition { get { return mStartPosition; } }
        public Vector2 endPosition { get { return mStartPosition + mRegionSize; } }

        public float GetValidArea()
        {
            return mValidArea;
        }

        public float GetRegionArea()
        {
            return mRegionSize.x * mRegionSize.y;
        }

        //返回与圆的包含关系,-1表示在圆外,0表示与圆相交,1表示在圆内
        public int CheckCollisionWithCircle(Vector2 center, float radius)
        {
            var corners = new List<Vector2>()
            {
                mStartPosition,
                new Vector2(mStartPosition.x + mRegionSize.x, mStartPosition.y),
                new Vector2(mStartPosition.x + mRegionSize.x, mStartPosition.y + mRegionSize.y),
                new Vector2(mStartPosition.x, mStartPosition.y + mRegionSize.y),
            };

            int insideCount = 0;
            int outsideCount = 0;
            for (int i = 0; i < corners.Count; ++i)
            {
                var d = (corners[i] - center).sqrMagnitude;
                if (d > radius * radius)
                {
                    ++outsideCount;
                }
                else
                {
                    ++insideCount;
                }
            }
            if (outsideCount == 0)
            {
                return 1;
            }
            if (insideCount == 0)
            {
                return -1;
            }
            return 0;
        }

        public Vector2 size { get { return mRegionSize; } }
        public int index { get { return mIndex; } }
        public List<List<Vector3>> noneHolePolygons { get { return mNoneHolePolygons; } }
        public List<List<Vector3>> holePolygons { get { return mHolePolygons; } }

        Vector2 mRegionSize;
        Vector2 mStartPosition;
        List<List<Vector3>> mNoneHolePolygons;
        List<List<Vector3>> mHolePolygons;
        float mValidArea = 0;
        int mIndex;
    }
}

#endif