﻿Shader "SLGMaker/RegionCombine"
{
    Properties
    {
        _Mask("Mask Texture", 2D) = "white" {}
        _Edge("Edge Texture", 2D) = "white" {}
        _MaskTextureSize("Texture Size", Vector) = (0, 0, 0, 0)
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // make fog work
            #pragma multi_compile_fog

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float4 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _Mask;
            sampler2D _Edge;
            float4 _MaskTextureSize;

            //获取某个像素的uv坐标
            float2 GetUV(float pixelOffset, float2 textureSize) {
                int offset = round(pixelOffset);
                int textureWidth = round(textureSize.x);
                int x = offset % textureWidth;
                int y = offset / textureWidth;
                float u = x / textureSize.x + 0.5 / textureSize.x;
                float v = y / textureSize.y + 0.5 / textureSize.y;
                return float2(u, v);
            }

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                float2 maskUV = GetUV(i.uv.z, _MaskTextureSize.xy);
                fixed4 col = tex2D(_Mask, maskUV);
                if (i.uv.w < 0.5) {
                    fixed4 edgeColor = tex2D(_Edge, float2(i.uv.x, i.uv.y + col.r));
                    return edgeColor;
                }
                return col;
            }
            ENDCG
        }
    }
}
