﻿using Cfg.C;
using Common;
using cspb;
using DeepUI;
using Game.Config;
using Game.Data;
using Game.Utils;
using TFW;
using TFW.Localization;
using TFW.UI;
using Logic;
using Public;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using HeroData = cspb.HeroData;
using Cysharp.Threading.Tasks;

namespace UI
{
    public class UINewTroopSelectData : UIData
    {
        public int curLineUpIndex;
        public MarchArgs marchArgs;
    }

    [Popup("UINewTroopSelectNew", true, true)]
    public class UINewTroopSelect : BasePopupLayer
    {

        [PopupField("Root/Close")]
        private GameObject btnClose;

        // [PopupField("Root/Scroll View/Viewport/Content/Image")]
        // private UINewTroopSelectSoldierItem soldierItem;
        
        [PopupField("Root/Center/LineUp/NumberPickerSlider")]
        private NumberPickerSlider soldierSlider;
        
        // [PopupField("Root/Top/Tab/Tabs/item/Select")]
        // private GameObject lineupSelectObj;

        [PopupField("Root/Center/LineUp/HeroItem")]
        private GameObject heroItem1;

        [PopupField("Root/Center/LineUp/HeroItem1")]
        private GameObject heroItem2;

        [PopupField("Root/Center/LineUp/HeroItem2")]
        private GameObject heroItem3;

        [PopupField("Root/Center/LineUp/InfoBtn")]
        private GameObject InfoBtn;

        [PopupField("Root/Center/LineUp/HeroItem/Item/Delete")]
        private GameObject heroItem1PutOff;

        [PopupField("Root/Center/LineUp/HeroItem1/Item/Delete")]
        private GameObject heroItem2PutOff;

        [PopupField("Root/Center/LineUp/HeroItem2/Item/Delete")]
        private GameObject heroItem3PutOff;

        [PopupField("Root/Center/LineUp/Btn1")]
        private GameObject Btn1;

        [PopupField("Root/Center/LineUp/Btn2")]
        private GameObject Btn2;

        [PopupField("Root/Center/LineUp/Btn3")]
        private GameObject Btn3;

        [PopupField("Root/Top/Tab/Tabs")]
        private UIGrid tabsList;

        [PopupField("Root/Top/Tab/TroopCount")]
        private TFWText troopCount;

        [PopupField("Root/Top/Tab/Tabs/itemAdd")]
        private GameObject addQueueBtn;

        [PopupField("Root/Center/LineUp/power/Text")]
        private TFWText troopPower;
        [PopupField("Root/Center/LineUp/Soldier/Text")]
        private TFWText troopSoldierCount;

        [PopupField("Root/Center/AttackContext/Text")]
        private TFWText powerInfo;

        [PopupField("Root/Center/CostContext/cost/Text")]
        private TFWText costText;

        [PopupField("Root/Center/CostContext/cost")]
        private GameObject costObj;

        [PopupField("Root/Center/btns/YellowBtn3/CostBG/Cost")]
        private TFWText timeText;
        [PopupField("Root/Center/btns/YellowBtn3/CostBG")]
        private GameObject CostBG;

        [PopupField("Root/Center/btns/BlueBtn")]
        private GameObject btnAutoSoldier;

        [PopupField("Root/Center/btns/YellowBtn3")]
        private GameObject btnGo;

        [PopupField("Root/Center/btns/YellowBtn3/BG")]
        private GameObject btnGoImg;
        [PopupField("Root/Center/btns/YellowBtn3/BG_grey")]
        private GameObject btnGoImg_grey;

        [PopupField("Root/Center/btns/YellowBtn3/Text")]
        private TFWText btnTxt;

        [PopupField("Root/Mask")]
        private GameObject Mask;
        
        [PopupField("Root/Center/GatherContext/Item_Time/Des")]
        private TFWText Item_Time;
         
        [PopupField("Root/Center/GatherContext/Item_weight/Des")]
        private TFWText Item_weight;
        [PopupField("Root/Center/GatherContext")]
        private GameObject GatherContext;
        [PopupField("Root/Center/AttackContext")]
        private GameObject AttackContext;
        [PopupField("Root/Center/CostContext")]
        private GameObject CostContext;

        [PopupField("Root/Arrow")]
        private Transform ArrowTr;

        [PopupField("Root")]
        private Animator anim;
        
        private int curLineUpIndex = 0;

        private cspb.Lineup mLineup;

        private MarchArgs m_MarchArgs;
        private float m_MarchDistance;

        private Dictionary<int, int> SelSoldierDic;

        private int curSelectSoldierCount = 0;

        protected override void OnInit()
        {
            base.OnInit();

            BindClickListener(btnClose, (x, y) =>
            {
                Close();
                EventMgr.FireEvent(TEventType.RefreshHeroData);
            });

            BindClickListener(InfoBtn, (x, y) =>
            {
                PopupManager.I.ShowPanel<UIPlayerInfo_K1>("UIPlayerInfo_K1");
            });

            BindClickListener(btnAutoSoldier, (x, y) => { AutoBattleSoldier().Forget(); });

            BindClickListener(addQueueBtn, (x, y) =>
            {
                HeroUtils.ShowAddQueue();
            });



            BindClickListener(heroItem1PutOff, (x, y) => { HeroPutOn(0);});

            BindClickListener(heroItem2PutOff, (x, y) => { HeroPutOn(1);});

            BindClickListener(heroItem3PutOff, (x, y) => { HeroPutOn(2);});

            BindClickListener(Btn1, (x, y) =>
            {
                if (!IsMask)
                {
                    PopupManager.I.ShowPanel<UIHeroListWindow>(new UIHeroListWindowData()
                    {
                        LineUpId = curLineUpIndex,
                        mHelpHeroIndex = 1,
                    });
                }

            });

            BindClickListener(Btn2, (x, y) =>
            {
                if (!IsMask)
                {
                    //bool pass_tesh = false;
                    //if (heroItem2.transform.Find("Item").gameObject.activeSelf)
                    //{
                    //    pass_tesh = true;
                    //}
                    //else
                    //{
                    //    if (heroItem2.transform.Find("Empty").gameObject.activeSelf)
                    //    {
                    //        if (heroItem2.transform.Find("Empty/Lock").GetComponent<TFWImage>().enabled)
                    //        {
                    //            FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Challenge_Level_Tips", MetaConfig.LieutenantInterfaceDisplayRules[0]));

                    //            return;
                    //        }
                    //        if (heroItem2.transform.Find("Empty/Add").GetComponent<TFWImage>().enabled)
                    //        {

                    //            pass_tesh = false;
                    //        }
                    //    }

                    //}


                    //if (!GameData.I.LineUpData.CanUpDeputyHero(1) && !pass_tesh)
                    //{
                    //    if (Cfg.C.CD2CityBuilding.I((int)MainCityItem.CityType.College * 1000).ReBuilded(true))
                    //    {
                    //        // PopupManager.I.ShowLayer<UITech>();
                    //        PopupManager.I.ShowLayer<UITechView>(new UITechViewJumpData()
                    //        {
                    //            TechType = 4000 + 1,
                    //        });
                    //        // PopupManager.I.ShowPanel<UITechDetail>(new UITechDetailData() { Type = 4000 + curPos });

                    //        var guidData = new UIGuidData();
                    //        guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UITechView", UIItem = "Root/mBottom/mBtnUpgrade", slide = true, closeDelay = 3 });
                    //        UIGuid.StartGuid(guidData, true);
                    //    }
                    //}
                    //else
                    //{
                    var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
                    if (mainHero == null)
                        return;
                    
                    PopupManager.I.ShowPanel<UIHeroListWindow>(new UIHeroListWindowData()
                    {
                        LineUpId = curLineUpIndex,
                        Pos = 1,

                    });
                    //}

                }
            });

            BindClickListener(Btn3, (x, y) =>
            {
                if (!IsMask)
                {
                    //bool pass_tesh = false;
                    //if (heroItem3.transform.Find("Item").gameObject.activeSelf)
                    //{
                    //    pass_tesh = true;
                    //}
                    //else
                    //{
                    //    if (heroItem3.transform.Find("Empty").gameObject.activeSelf)
                    //    {
                    //        if (heroItem3.transform.Find("Empty/Lock").GetComponent<TFWImage>().enabled)
                    //        {
                    //            FloatTips.I.FloatMsg(LocalizationMgr.Format("Alliance_Challenge_Level_Tips", MetaConfig.LieutenantInterfaceDisplayRules[1]));
                    //            return;
                    //        }
                    //        if (heroItem3.transform.Find("Empty/Add").GetComponent<TFWImage>().enabled)
                    //        {
                    //            pass_tesh = false;
                    //        }
                    //    }

                    //}

                    //if (!GameData.I.LineUpData.CanUpDeputyHero(2) && !pass_tesh)
                    //{
                    //    if (Cfg.C.CD2CityBuilding.I((int)MainCityItem.CityType.College * 1000).ReBuilded(true))
                    //    {
                    //        // PopupManager.I.ShowLayer<UITech>();
                    //        PopupManager.I.ShowLayer<UITechView>(new UITechViewJumpData()
                    //        {
                    //            TechType = 4000 + 2,
                    //        });
                    //        // PopupManager.I.ShowPanel<UITechDetail>(new UITechDetailData() { Type = 4000 + curPos });

                    //        var guidData = new UIGuidData();
                    //        guidData.guidItems.Add(new UIGuidItem() { UIName = "UI.UITechView", UIItem = "Root/mBottom/mBtnUpgrade", slide = true, closeDelay = 3 });
                    //        UIGuid.StartGuid(guidData, true);
                    //    }
                    //}
                    //else
                    //{
                    var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
                    if (mainHero == null)
                        return;
                    
                    PopupManager.I.ShowPanel<UIHeroListWindow>(new UIHeroListWindowData()
                    {
                        LineUpId = curLineUpIndex,
                        Pos = 2,

                    });
                    //}
                }
            });


            BindClickListener(btnGo, async (x, y) =>
            {
                if (SelSoldierDic == null)
                    SelSoldierDic = new Dictionary<int, int>();
                else
                    SelSoldierDic.Clear();

                // if (soldierItem != null)
                // { 
                //     if (soldierItem.slider.value > 0)
                //     { 
                //         var soldierCfg = Cfg.C.CSoldier.I(1 * 10000 + 1);
                //
                //         if (soldierCfg != null)
                //         {
                //             SelSoldierDic.Add(1 * 10000 + 1, soldierItem.slider.value);
                //         } 
                //     }
                // }

                if (soldierSlider != null)
                {
                    if (soldierSlider.value > 0)
                    {
                        var soldierCfg = Cfg.C.CSoldier.I(1 * 10000 + 1);

                        if (soldierCfg != null)
                        {
                            SelSoldierDic.Add(1 * 10000 + 1, soldierSlider.value);
                        } 
                    }
                    else
                    {
                        FloatTips.I.FloatMsg("March_select_tips_01");
                        return;
                    }
                }

                if (SelSoldierDic.Count > 0)
                {

                    int cost = 0;
                    if (m_MarchArgs != null)
                    {
                        cost = m_MarchArgs.targetId != 0 ? await MarchUtils.GetActionValue(m_MarchArgs.targetId, m_MarchArgs.act) : 0;
                    }

                    var powers=await PlayerAssetsMgr.I.GetShowAction();

                    var currentPhyPower =(long) powers[0];
                    var maxPhyPower=(long) powers[1];

                    if (currentPhyPower >= cost)
                    {
                        UICreateTroop.Start(m_MarchDistance, m_MarchArgs.act, m_MarchArgs.targetId, SelSoldierDic.Values.ToList()[0], DefaultCreateTroopCallback);
                    }
                    else
                    {
                        PhysicalPowerMgr.I.ShowAddPowerDialog();
                    }
                }
                else
                {
                    FloatTips.I.FloatMsg(LocalizationMgr.Get("TroopsBattle"));
                }
            });
        }

        #region 数据刷新显示

        /// <summary>
        /// 当界面第一次打开时调用
        /// </summary>
        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();

            OnShowStart();
        }

        /// <summary>
        /// 界面每次显示时调用
        /// </summary>
        protected internal override async void OnShowStart()
        {
            base.OnShowStart();
            curSelectSoldierCount = 0;
            MessageMgr.RegisterMsg<DeputyHeroAck>(this, OnDeputyHeroAck);
            // MessageMgr.RegisterMsg<LineupBuildRemoveAck>(this, OnLineupBuildRemoveAck);
            // btnTxt.text = LocalizationMgr.Get("MAP_attack_cap");

            m_MarchArgs = (Data as UINewTroopSelectData)?.marchArgs;
            curLineUpIndex = (Data as UINewTroopSelectData)?.curLineUpIndex ?? 0;

            if (null != m_MarchArgs)
            {
                // if (m_MarchArgs.act == MarchAct.MarchActGather)
                // {
                //     btnTxt.text = LocalizationMgr.Get("Gather_Btn");
                // }
                // else
                // {
                //     btnTxt.text = LocalizationMgr.Get("MAP_attack_cap");
                // }

                GatherContext.SetActive(m_MarchArgs.act == MarchAct.MarchActGather);
                AttackContext.SetActive(!(m_MarchArgs.act == MarchAct.MarchActGather || m_MarchArgs.act == MarchAct.MarchActRally));
                CostContext.SetActive(m_MarchArgs.act != MarchAct.MarchActGather);

            }

            var entity = LMapEntityManager.I.GetEntityInfo(LPlayer.I.MainCityID);
            if (entity != null && m_MarchArgs != null)
            {
                var targetPosition = m_MarchArgs.targetPosition;
                var startPosition = entity.CurrentPosition;
                if (m_MarchArgs?.targetId != 0)
                {
                    m_MarchDistance =await MarchUtils.GetMarchLineLength(LPlayer.I.MainCityID, m_MarchArgs.targetId);
                }
                else
                {
                    m_MarchDistance =await MarchUtils.GetMarchLineLength(startPosition, targetPosition,
                        await LMapEntityManager.I.GetOccupyRadius(LPlayer.I.MainCityID) * -1);
                }
            }
            
            InitTabs();

            ChangeTabClick(curLineUpIndex);
            
            RefreshLineUpIndex();

            //引导
            WaitForAnimation().Forget();
          
        }

        public async UniTaskVoid WaitForAnimation() {
            
            AnimatorClipInfo[] clipInfo = anim.GetCurrentAnimatorClipInfo(0);
            float animLength = clipInfo[0].clip.length;
            await UniTask.Delay(TimeSpan.FromSeconds(animLength)); 
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            if (PlayerPrefs.GetInt($"{LPlayer.I.PlayerID}_firsttroopselect", 0) == 0 && curLineUpIndex == 0 && mainHero == null)//
            {
                D.Debug.Log("show TroopSelect Guid");
                PlayerPrefs.SetInt($"{LPlayer.I.PlayerID}_firsttroopselect", 1);
                UIGuidData guidData = new UIGuidData();
                guidData.guidItems.Add(new UIGuidItem() { UIName = "UINewTroopSelect", UIItem = $"Root/Center/btns/BlueBtn", forceGuid = true, slide = true, delayFinger = 0.5f });
                UIGuid.StartGuid(guidData);
            }
            else
            {
                //
                if (!IsMask)
                {
                    //点开界面后给当前部队一键上阵
                    AutoBattleSoldier().Forget();
                }
            }
            
        }
        
        // private void OnLineupBuildRemoveAck(LineupBuildRemoveAck obj)
        // {
        //     RefreshLineUpIndex();
        // }

        private void OnDeputyHeroAck(DeputyHeroAck obj)
        {
            RefreshLineUpIndex();
            RefreshTabRed();
        }

        #endregion

        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();
            curSelectSoldierCount = 0;
            MessageMgr.UnregisterMsg(this);
            //关闭界面要下阵所有城内的部队
            //RemoveAll();
        }

        private void InitTabs()
        {
            List<int> _lineupLst = GameData.I.LineUpData.lockedList;

            tabsList.Clear();
            if (_lineupLst?.Count > 0)
            {
                for (int i = 0; i < _lineupLst.Count; i++)
                {
                    var tabItem = tabsList.AddItem<TroopTabItem>();
                    tabItem.InitData(i);
                }


                addQueueBtn.transform.SetAsLastSibling();
                addQueueBtn.SetActive(MetaConfig.Player_max_march_count > GameData.I.LineUpData.lockedList.Count);
                ArrowTr.gameObject.SetActive(true);

                // RefreshLineUpCount();
            }
            else
            {
                ArrowTr.gameObject.SetActive(false);
                troopCount.text = string.Empty;
            }
            
        }

        void RefreshLineUpCount()
        {
            var count =  GameData.I.LineUpData.lockedList.Count;
            var curCount = 0;

            for (int i = 0; i < GameData.I.LineUpData.lockedList.Count; i++)
            {
                var index = GameData.I.LineUpData.lockedList[i];
                GameData.I.LineUpData.LineUpDataDic.TryGetValue(index, out var curLineup);
                var mainHero = GameData.I.LineUpData.GetLineUpHero(curLineup, 0);
                if (mainHero != null)
                {
                    curCount++;
                }
            }

            troopCount.text = $"{curCount}/{count}";
        }

        [PopupEvent(TEventType.ClickTroopTab)]
        private void RefreshByTab(object[] obj)
        {
            if (obj.Length > 0)
            {
                var toLineUpIndex = (int)obj[0];
                if (curLineUpIndex == toLineUpIndex)
                {
                    return;
                }

                curSelectSoldierCount = 0;

                if (GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[toLineUpIndex], out var mLineup))
                {

                    curLineUpIndex = toLineUpIndex;
                    RefreshLineUpIndex();
                    //return;

                }

                ChangeTabClick(curLineUpIndex);
            }
        }

        [PopupEvent(TEventType.RefreshLineData)]
        private void RefreshLineData(object[] obj)
        {
            InitTabs();
            ChangeTabClick(curLineUpIndex);
            RefreshLineUpIndex();
            RefreshTabRed();
        }

        [PopupEvent(TEventType.DeputyHeroChange)]
        private void RefreshDeputyHeroChange(object[] objs)
        {
            InitTabs();
            ChangeTabClick(curLineUpIndex);
            RefreshLineUpIndex();
            RefreshTabRed();
        }

        [PopupEvent(TEventType.LineupBuildRemoveAck)]
        private void RefreshLineupBuildRemoveAck(object[] objs)
        {
            RefreshLineUpIndex();
            RefreshSlider();
            RefreshTabRed();
        }

        private void ChangeTabClick(int tabIndex)
        {
            if (tabIndex >= 0 && tabsList.MList.Count > tabIndex)
            {
                curLineUpIndex = tabIndex;
                tabsList.MList[tabIndex].GetComponent<TroopTabItem>().SelectedItem(tabIndex);

                //tabsList.MList[tabIndex].GetComponent<TroopTabItem>().OnClickTab(null, null);
            }

            RefreshTabRed();
            RefreshSlider();
        }

        private void RefreshTabRed()
        {
            foreach (var item in tabsList.MList)
            {
                item.GetComponent<TroopTabItem>().RefreshRedPoint();
            }
        }

        private void RefreshLineUpIndex()
        {
            if (GameData.I.LineUpData.lockedList?.Count > 0)
            {
                if (curLineUpIndex >= 0)
                {
                    mLineup = null;

                    if (GameData.I.LineUpData.lockedList.Count > curLineUpIndex)
                    {
                        if (GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex], out mLineup))
                        {
                            if (mLineup.state != (int)LineupState.LineupStateDefender && mLineup.heroes.Count > 0)
                            {
                                // return;
                            }
                        }
                    } 
                }
                
                RefreshHeroUI();
                ArrowTr.transform.position = new Vector3(ArrowTr.transform.position.x, tabsList.MList[curLineUpIndex].transform.position.y, ArrowTr.transform.position.z);
                RefreshLineUpCount();
            }
        }

        private async UniTask RefreshHeroUI()
        {
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);

            RefreshHero(mainHero, leftHero, rightHero);

            //var targetInfo = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
            //var cost = m_MarchArgs.targetId != 0 ? MarchUtils.GetActionValue(m_MarchArgs.targetId, m_MarchArgs.act) : 0;

            btnAutoSoldier.SetActive(mainHero==null);
            btnGo.SetActive(mainHero!=null);
            timeText.gameObject.SetActive(mainHero!=null);
            CostBG.SetActive(mainHero!=null);

            if (mainHero != null)
            {
               await RefreshBaseInfo(mainHero);

                if (m_MarchArgs == null)
                {
                    costObj.SetActive(false);
                    //btnAutoSoldier.SetActive(false);
                    btnGo.SetActive(false);
                    timeText.gameObject.SetActive(false);
                    CostBG.SetActive(false);
                }

                RefreshSoldierCount();

                //AutoBattleSoldier();//TODO 判断当前队列是否有缓存

                RefreshPower();

                // RefreshTroopSoliderCount();
            }
            else
            {
                costObj.SetActive(false);
                //btnAutoSoldier.SetActive(false);
                btnGo.SetActive(false);
                timeText.gameObject.SetActive(false);
                CostBG.SetActive(false);
                powerInfo.text = String.Empty;
                troopSoldierCount.text = "0/0";
            }
            IsMask = false;
            GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex], out mLineup);
            if (mLineup.state > 1)
            {
                IsMask = true;
            }
            // Mask.gameObject.SetActive(IsMask);
        }

        private async UniTask RefreshSoldierCount()
        {
            //如果没有切过队列 curSelectSoldierCount = -1  这个时候要重新计算携带士兵数
            //如果是在编辑当前队列则 curSelectSoldierCount不大于最大可携带士兵数时不刷新这个数据
            
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            // var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            // var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);
            // int maxtroopSoldier = 0;
            // maxtroopSoldier += mainHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            // maxtroopSoldier += leftHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            // maxtroopSoldier += rightHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            //当前带兵上限,剩余士兵数量  那个小用哪个
            // int soldierType = mainHero.HeroCfg.SoldiersType;
            // var addValueByProp = Game.Data.GameData.I.PlayerData.PropData.GetPropSpeedUp(GameConfig.PROP_Hero_Capcity_Solider);

            float maxSoldier = GetLineUpMaxSoldier();//Mathf.Min(LogicCreateSoldier.I.IdleSoldier, maxtroopSoldier);//mainHero.HeroAttribute.LevelUpSoldierCapacity + (int)addValueByProp);
            double lineUpWeight = 0;
            float recomondSoldier = 0;
            if (curSelectSoldierCount == 0)
            {
                if (m_MarchArgs != null && (LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId)?.type == MapUnitType.MapUnitNpcTroop || m_MarchArgs.act == MarchAct.MarchActGather))
                {
                    if (m_MarchArgs.act == MarchAct.MarchActGather)
                    {
                        var data = LGatherInfo.I.GetGatherableNpc(m_MarchArgs.targetId);
                        if (data != null)
                        {
                            double bilv = 0;

                            bilv +=await Game.Data.HeroGameData.I.GetHeroProp(mainHero.HeroId, GameConfig.Hero_Add_Load);


                            if (LPlayer.I.IsPlayerInUnion())
                            {
                                bilv += await Game.Data.AllianceGameData.I.AlliancePropGameData.GetPropSpeedUp(GameConfig.Alliance_Add_Load);
                            }

                            var initReserveCount = LGatherInfo.I.GetGatherRssRemain(m_MarchArgs.targetId);

                            var maxSolderCollect = (initReserveCount / MetaConfig.MapGoldWeightParam) / (MetaConfig.SoldierWeightParam * (1 + bilv));
                            
                            recomondSoldier = Mathf.Min(maxSoldier, (float)maxSolderCollect);
                        }
                        else
                        {
                            var entity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
                            if (entity != null
                                && entity.CentralCity != null
                                && await entity.CentralCity.GetBuildingCfg() != null
                                && (await entity.CentralCity.GetBuildingCfg()).Yield != null
                                )
                            {
                                if ((await entity.CentralCity.GetBuildingCfg()).Yield.Count > 0)
                                {
                                    recomondSoldier = maxSoldier;
                                }
                            }
                        }
                    }
                    else
                    {
                        float maxLineUpNum = LogicCreateSoldier.I.IdleSoldier / (GameData.I.LineUpData.lockedList?.Count ?? 2);
                        recomondSoldier = Mathf.Min(maxLineUpNum, maxSoldier);
                    }
                }
                else
                {
                    recomondSoldier = maxSoldier;
                }

                curSelectSoldierCount = Mathf.CeilToInt(recomondSoldier);
            }
            else
            {
                recomondSoldier = curSelectSoldierCount;
            }
                
            soldierSlider.SetRange(0, (int)maxSoldier ,curSelectSoldierCount,1);
            soldierSlider.SetTipSetting(false);
            // soldierSlider.SetValueWithoutNotify((int)curSelectSoldierCount,false);
            soldierSlider.onValueChanged.RemoveAllListeners();
            soldierSlider.onValueChanged.AddListener((t) =>
            {
                SliderChangeValue(soldierSlider,t);
            });
            RefreshTroopSoliderCount((int)curSelectSoldierCount);
            RefreshSoldierWeight((int)curSelectSoldierCount);
            RefreshPower();
        }

        bool IsMask;

        private void SliderChangeValue(NumberPickerSlider slider, int value)
        {
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);

            int maxNum = 0;// (int)LogicCreateSoldier.I.IdleSoldier;

            if (mainHero != null)
            {
               
                var maxSoldier = GetLineUpMaxSoldier();
                maxNum = (int)maxSoldier;
            }
            
            //后续受科技属性加成 TODO
            if (value > maxNum)
            {
                value = maxNum;
                slider.SetValueWithoutNotify(maxNum,false);
            }
            curSelectSoldierCount = value;
            RefreshPower().Forget();
            RefreshTroopSoliderCount(value);
            RefreshSoldierWeight(value);
        }

        float GetLineUpMaxSoldier()
        { 
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            //var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            //var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);
            int maxtroopSoldier = 0;
            maxtroopSoldier += mainHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            //maxtroopSoldier += leftHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            //maxtroopSoldier += rightHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;

            var addValueByProp = Game.Data.GameData.I.PlayerData.PropData.GetPropSpeedUp(GameConfig.PROP_Hero_Capcity_Solider);
            maxtroopSoldier += (int)addValueByProp;

            float maxSoldier = Mathf.Min(LogicCreateSoldier.I.IdleSoldier, maxtroopSoldier);
            return maxSoldier;
        }

        private void RefreshSlider()
        {
            var mainHeros = GameData.I.LineUpData.GetLineUpHeros(mLineup);
            if (mainHeros.Count <= 0)
            {
                if (soldierSlider != null)
                {
                    soldierSlider.SetValueWithoutNotify(0,false);
                    RefreshPower();
                }
            }
        }

        private async UniTask RefreshPower()
        {
            troopPower.text = UIStringUtils.FormatIntegerByLanguage( await GetTroopBattlePower());

            if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActAttack)
            {
                var objs =await GetPowerCompStrAndColor();
                powerInfo.text = objs.Item2;
                //powerInfo.color = compColor;
                powerInfo.gameObject.SetActive(true);
                powerInfo.enabled = objs.Item1;
            }
            else
            {
                powerInfo.enabled = false;
                powerInfo.text = String.Empty;
                powerInfo.gameObject.SetActive(false);
                //m_ActionPointText.text = string.Empty;
            }

        }

        private async UniTask RefreshSoldierWeight(int count)
        {
            //负重显示当前兵力 最大负重
            if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActGather)
            {
                double soldierCount = count;
                var heros = GameData.I.LineUpData.GetLineUpHeros(mLineup);
              
                        // soldierCount += soldierSlider.value;
                  
                //如果是采集，显示的是负重

                var maxLoad = soldierCount * MetaConfig.SoldierWeightParam;
                double bilv = 0;
                if (heros.Count > 0)
                {
                    bilv +=await Game.Data.HeroGameData.I.GetHeroProp(heros[0].HeroId, GameConfig.Hero_Add_Load);
                }

                if (LPlayer.I.IsPlayerInUnion())
                {
                    bilv += await Game.Data.AllianceGameData.I.AlliancePropGameData.GetPropSpeedUp(GameConfig.Alliance_Add_Load);
                }

                maxLoad = maxLoad + maxLoad * bilv;

                var data = LGatherInfo.I.GetGatherableNpc(m_MarchArgs.targetId);
                if (data != null)
                {
                    //金币采集加速百分比
                    //var gatherBuffSpeedUp = GameData.I.PlayerData.PropData.GetPropSpeedUp(GameConfig.GatherGoldBuffSpeedUpId); //LPlayer.I.GetBuffSpeedUpDic(GameConfig.GatherGoldBuffSpeedUpId);

                    //Debug.LogErrorFormat("data.Remain={0}", data.Remain, data.Total,data.spee);
                    var initReserveCount = LGatherInfo.I.GetGatherRssRemain(m_MarchArgs.targetId);
                    int result = (int)Math.Ceiling(Math.Min(maxLoad * MetaConfig.MapGoldWeightParam, initReserveCount) / (await data.GoldSpeed(heros.Select(a => a.HeroId).ToArray()) * 1000f));
                    var speed = await data.GoldSpeed(heros.Select(a => a.HeroId).ToArray());

                    //result = Math.Min(load, initReserveCount) / (basicGatherSpd * 1000);

                    //采集时间
                    //_collectionTimeText.text = SCommon.Seconds2HMS((int)((int)(m_SelectedLineUpItem.PowerMax * MetaConfig.GatherWeightCoeff) / (data.Yield[0].Val * 1000)));1000
                    //采集公式修改 min(负重,剩余金币)/金币产出速度=实际采集时间
                    // timeText.text = $"{UIHelper.GetFormatTime(result * 1000)}";
                    Item_Time.text = $"{UIHelper.GetFormatTime(result * 1000)} ({UIStringUtils.ExchangeValue(speed * 1000 * 3600f, 2)}/h)";
                    Item_weight.text = UIStringUtils.FormatIntegerByLanguage((long)maxLoad);
                }
                else
                {
                    var entity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
                    if (entity != null
                        && entity.CentralCity != null
                        && await entity.CentralCity.GetBuildingCfg() != null
                        && (await entity.CentralCity.GetBuildingCfg()).Yield != null
                        )
                    {
                        if ((await entity.CentralCity.GetBuildingCfg()).Yield.Count > 0)
                        {
                            //产出速度
                            //var produce = entity.CentralCity.BuildingCfg.Yield[0].Val;
                            var produce = await LGatherable.GetGatherSpeed(await entity.CentralCity.GetBaseProduce(), entity.CurrentPosition, heros.Select(a => a.HeroId).ToArray()); //entity.CentralCity.Produce;
                            var maxCount = maxLoad * MetaConfig.GatherWeightCoeff * MetaConfig.MapGoldWeightParam;
                            var time = maxCount / produce;
                            //有采集的最大时间上限
                            if (time > (await entity.CentralCity.GetBuildingCfg()).MaxGatherTimes)
                                time = (await entity.CentralCity.GetBuildingCfg()).MaxGatherTimes;
                            //_collectionTimeText.text = SCommon.Seconds2HMS((int)(time));
                            Item_Time.text = $"{UIHelper.GetFormatTime((int)(time))} ({UIStringUtils.ExchangeValue(produce * 1000 * 3600f, 2)}/h)"; ;
                            Item_weight.text = UIStringUtils.FormatIntegerByLanguage((long)maxCount);
                        }
                    }
                }


            }
        }

        private void RefreshTroopSoliderCount(int soldierCount)
        {
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);
            int maxtroopSoldier = 0;
            maxtroopSoldier += mainHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            maxtroopSoldier += leftHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            maxtroopSoldier += rightHero?.HeroAttribute.LevelUpSoldierCapacity ?? 0;
            
            // D.Debug.Log($"soldierCount: {soldierCount}");
            troopSoldierCount.text = $"{UIStringUtils.FormatIntegerByLanguage(soldierCount)}/{UIStringUtils.FormatIntegerByLanguage(maxtroopSoldier)}";
            
            btnGoImg.SetActive(soldierCount > 0);
            btnGoImg_grey.SetActive(soldierCount <= 0);
        }


        private async UniTask AutoBattleSoldier()
        {
            GameData.I.LineUpData.LineUpDataDic.TryGetValue(GameData.I.LineUpData.lockedList[curLineUpIndex], out mLineup);
            if (mLineup.state > 1)
                return;
            
            if (GameData.I.LineUpData.lockedList?.Count > 0)
            {
                int curLineup = GameData.I.LineUpData.lockedList[curLineUpIndex];

                var heros = HeroGameData.I.GetOutBattleHeros(curLineUpIndex);
                if (heros.Count <= 0)
                {
                    return;
                }
                heros.Sort((h1, h2) => (int)(h2.Power() - h1.Power()));

                var mainHeros = GameData.I.LineUpData.GetLineUpHeros(mLineup);

                //带兵逻辑 当前部队  剩余士兵数量 -> 最大带兵数量
                if (mainHeros.Count > 0)
                {
                    RefreshSoldierCount();
                    // var addValueByProp = Game.Data.GameData.I.PlayerData.PropData.GetPropSpeedUp(GameConfig.PROP_Hero_Capcity_Solider);
                    //
                    // float maxSoldier = Mathf.Min(LogicCreateSoldier.I.IdleSoldier, mainHeros[0].HeroAttribute.LevelUpSoldierCapacity + (int)addValueByProp);
                    // float recomondSoldier = 0;
                    // if (m_MarchArgs != null && (LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId)?.type == MapUnitType.MapUnitNpcTroop || m_MarchArgs.act == MarchAct.MarchActGather))
                    // {
                    //     if (m_MarchArgs.act == MarchAct.MarchActGather)
                    //     {
                    //         var data = LGatherInfo.I.GetGatherableNpc(m_MarchArgs.targetId);
                    //         if (data != null)
                    //         {
                    //             double bilv = 0;
                    //
                    //             bilv += await Game.Data.HeroGameData.I.GetHeroProp(mainHeros[0].HeroId, GameConfig.Hero_Add_Load);
                    //
                    //             if (LPlayer.I.IsPlayerInUnion())
                    //             {
                    //                 bilv += await Game.Data.AllianceGameData.I.AlliancePropGameData.GetPropSpeedUp(GameConfig.Alliance_Add_Load);
                    //             }
                    //
                    //
                    //             var initReserveCount = LGatherInfo.I.GetGatherRssRemain(m_MarchArgs.targetId);
                    //
                    //             var maxSolderCollect = (initReserveCount / MetaConfig.MapGoldWeightParam) / (MetaConfig.SoldierWeightParam * (1 + bilv));
                    //
                    //             recomondSoldier = Mathf.Min(maxSoldier, (float)maxSolderCollect);
                    //         }
                    //         else
                    //         {
                    //             var entity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
                    //             if (entity != null
                    //                 && entity.CentralCity != null
                    //                 && (await entity.CentralCity.GetBuildingCfg()) != null
                    //                 && (await entity.CentralCity.GetBuildingCfg()).Yield != null
                    //                 )
                    //             {
                    //                 if ((await entity.CentralCity.GetBuildingCfg()).Yield.Count > 0)
                    //                 {
                    //                     recomondSoldier = maxSoldier;
                    //                 }
                    //             }
                    //         }
                    //     }
                    //     else
                    //     {
                    //         float maxLineUpNum = LogicCreateSoldier.I.IdleSoldier / (GameData.I.LineUpData.lockedList?.Count ?? 2);
                    //         recomondSoldier = Mathf.Min(maxLineUpNum, mainHeros[0].HeroAttribute.LevelUpSoldierCapacity + (int)addValueByProp);
                    //     }
                    // }
                    // else
                    // {
                    //     recomondSoldier = maxSoldier;
                    // }
                    //
                    //
                    // if (null != soldierSlider)
                    // {
                    //     soldierSlider.SetValueWithoutNotify((int)recomondSoldier, false);
                    // }
                }
                else
                {
                    RefreshSlider();
                }

                //上阵最大战力的闲置英雄 

                ChangeHero(mainHeros, heros, curLineup);
            }
        }


        private void HeroPutOn(int index)
        {
            if (IsMask)
                return;
            
            if (index == 0) 
            {
                //主将下阵  副将一起下阵
                int curLineup = GameData.I.LineUpData.lockedList[curLineUpIndex];
                MessageMgr.Send(new LineupBuildRemoveReq()
                {
                    index = curLineup
                });
                
            }
            else
            {
                //副将 
                var curHero = GameData.I.LineUpData.GetLineUpHero(mLineup, index);
                MessageMgr.Send(new DeputyHeroReq()
                {
                    heroId = curHero.HeroId,
                    deputyHeroPos = index,
                    isPutOn = 0,
                    lineupId = mLineup.id
                });
            }
        }

        private void ChangeHero(List<Game.Data.HeroData> curHeros, List<Game.Data.HeroData> toHeros, int curLineup)
        {
            //上阵英雄
            LineupBuildHeroReq req = new LineupBuildHeroReq();

            bool changed = false;
            for (int i = 0; i < 3; i++)
            {
                if (toHeros.Count > i)
                {
                    req.heroIDs.Add(toHeros[i].HeroId);


                    if (curHeros.Count <= i)
                    {
                        changed = true;
                        continue;
                    }

                    if (curHeros[i].HeroCfg.Id != toHeros[i].HeroCfg.Id)
                    {
                        changed = true;
                        continue;
                    }
                }
            }

            if (changed)
            {
                req.index = curLineup;
                MessageMgr.Send(req);
            }
        }


        private async UniTask RefreshBaseInfo(Game.Data.HeroData mainHero)
        {
            // RefreshPower();
            // RefreshTroopSoliderCount();
            powerInfo.text = String.Empty;
            EntityInfo targetInfo = null;
            if (m_MarchArgs != null)
            {
                targetInfo = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
            }
            int cost = 0;
            if (m_MarchArgs != null)
            {
                cost = m_MarchArgs.targetId != 0 ? await MarchUtils.GetActionValue(m_MarchArgs.targetId, m_MarchArgs.act) : 0;
            }
            if (targetInfo == null || targetInfo.PlayerCity == null)
            {
                // 行动力消耗
                costObj.SetActive(true);
            }
            else
            {
                costObj.SetActive(false);
            }

            if (m_MarchArgs == null)
            {
                costObj.SetActive(false);
                //btnAutoSoldier.gameObject.SetActive(false);
                btnGo.gameObject.SetActive(false);
                timeText.gameObject.SetActive(false);
                CostBG.SetActive(false);
            }


            var prop =await HeroGameData.I.GetHeroProp(mainHero.HeroId, GameConfig.TroopCostStrength);
            cost = Mathf.CeilToInt(cost * (1 - (float)prop));

            costText.text = (cost).ToString();

            // var heroId = selectedLineUpItem.HeroId;
            //var heros = GameData.I.LineUpData.GetLineUpHeros(mLineup);
            //if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActGather)
            //{

            //    //如果是采集，显示的是负重
            //    GameData.I.LineUpData.GetMarchPrepared(mLineup.id, (ack) =>
            //    {
            //        var data = LGatherInfo.I.GetGatherableNpc(m_MarchArgs.targetId);
            //        if (data != null)
            //        {
            //            //金币采集加速百分比
            //            //var gatherBuffSpeedUp = GameData.I.PlayerData.PropData.GetPropSpeedUp(GameConfig.GatherGoldBuffSpeedUpId); //LPlayer.I.GetBuffSpeedUpDic(GameConfig.GatherGoldBuffSpeedUpId);

            //            //Debug.LogErrorFormat("data.Remain={0}", data.Remain, data.Total,data.spee);
            //            var initReserveCount = LGatherInfo.I.GetGatherRssRemain(m_MarchArgs.targetId);
            //            int result = (int)Math.Ceiling(Math.Min(ack.maxLoad, initReserveCount) / (data.GoldSpeed(heros.Select(a => a.HeroId).ToArray()) * 1000f));

            //            var speed = data.GoldSpeed(heros.Select(a => a.HeroId).ToArray());

            //            //result = Math.Min(load, initReserveCount) / (basicGatherSpd * 1000);

            //            //采集时间
            //            //_collectionTimeText.text = SCommon.Seconds2HMS((int)((int)(m_SelectedLineUpItem.PowerMax * MetaConfig.GatherWeightCoeff) / (data.Yield[0].Val * 1000)));
            //            //采集公式修改 min(负重,剩余金币)/金币产出速度=实际采集时间
            //            timeText.text = $"{UIHelper.GetFormatTime(result * 1000)} ({UIStringUtils.ExchangeValue(speed * 1000 * 3600f, 2)}/h)";
            //        }
            //        else
            //        {
            //            var entity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
            //            if (entity != null
            //                && entity.CentralCity != null
            //                && entity.CentralCity.BuildingCfg != null
            //                && entity.CentralCity.BuildingCfg.Yield != null
            //                )
            //            {
            //                if (entity.CentralCity.BuildingCfg.Yield.Count > 0)
            //                {
            //                    //产出速度
            //                    //var produce = entity.CentralCity.BuildingCfg.Yield[0].Val;
            //                    var produce = LGatherable.GetGatherSpeed(entity.CentralCity.BaseProduce, entity.Position, heros.Select(a => a.HeroId).ToArray()); //entity.CentralCity.Produce;
            //                    var maxCount = ack.maxLoad * MetaConfig.GatherWeightCoeff;
            //                    var time = maxCount / produce;
            //                    //有采集的最大时间上限
            //                    if (time > entity.CentralCity.BuildingCfg.MaxGatherTimes)
            //                        time = entity.CentralCity.BuildingCfg.MaxGatherTimes;
            //                    //_collectionTimeText.text = SCommon.Seconds2HMS((int)(time));
            //                    timeText.text = $"{UIHelper.GetFormatTime((int)(time))} ({UIStringUtils.ExchangeValue(produce * 1000 * 3600f, 2)}/h)"; ;
            //                }
            //            }
            //        }
            //    });

            //}


            float marchLength = 0f;
            int needTime = 0;
            //if (m_SelectedTroopItem != null && m_SelectedTroopItem.troopId != 0)
            //{
            //    if (m_MarchArgs.targetId != 0)
            //    {
            //        marchLength = MarchUtils.GetMarchLineLength(m_SelectedTroopItem.troopId, m_MarchArgs.targetId);
            //    }
            //    else
            //    {
            //        var entity = LMapEntityManager.I.GetEntityInfo(m_SelectedTroopItem.troopId);
            //        if (entity != null)
            //        {
            //            marchLength = MarchUtils.GetMarchLineLength(entity.CurrentPosition,
            //            m_MarchArgs.targetPosition);
            //        }
            //    }
            //    if (lineUpData != null)
            //    {
            //        needTime = MarchUtils.GetMarchTime(m_MarchAct == MarchAct.MarchActRally, lineUpData.heroes[0].heroID, marchLength);
            //    }

            //    if (needTime < 1)
            //        needTime = 1;
            //    //_timeCostText.text = SCommon.Seconds2HMS(needTime);
            //    _timeCostTextYe.text = SCommon.Seconds2HMS(needTime);
            //    _timeCostTextRed.text = SCommon.Seconds2HMS(needTime);
            //    _timeCostTextGr.text = SCommon.Seconds2HMS(needTime);
            //}


            if (mLineup != null && m_MarchArgs != null)
            {
                // 时间消耗
                needTime = await MarchUtils.GetMarchTime(m_MarchArgs.act == MarchAct.MarchActRally, mLineup.heroes[0].heroID, m_MarchDistance);
            }
            if (needTime < 1)
                needTime = 1;
            //_timeCostText.text = SCommon.Seconds2HMS(needTime);
            timeText.text = SCommon.Seconds2HMS(needTime);


            if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActJoinRally)
            {
                var data = GameData.I.RallyData.GetRallyInfo(this.m_MarchArgs.targetId);
                if (data != null)
                {
                    //加入集结显示 集结到达发起者的时间
                    if (!data.GetIsPlayerSelf())
                    {
                        var start = LMapEntityManager.I.GetEntityInfo(LPlayer.I.MainCityID);
                        if (start != null)
                        {
                            //MapUnitRallyTroop
                            var marchDistance =await MarchUtils.GetMarchLineLength(start.CurrentPosition, data.GetRallyPosition());

                            if (mLineup.heroes != null && mLineup.heroes.Count > 0)
                            {
                                var joinRallyNeedTime =await MarchUtils.GetMarchTime(false, mLineup.heroes[0].heroID, marchDistance);
                                //Debug.LogFormat($"totalSpeedUp={totalSpeedUp},needTime={needTime1},needTime1={needTime2}");
                                var time = SCommon.Seconds2HMS(joinRallyNeedTime);
                                //_timeCostText.text = time;
                                timeText.text = time;
                            }
                        }
                    }
                }


                //发起集结显示集结等待时间
                //_timeCostText.text = SCommon.Seconds2HMS(m_MarchArgs.rallyTime / 1000);
                if (m_MarchArgs != null && m_MarchArgs.rallyRemeingTime > 0)
                {
                    if (needTime > (m_MarchArgs.rallyRemeingTime / 1000))
                    {
                        FloatTips.I.FloatMsg(LocalizationMgr.Get("Rally_Time_Not_Enough"));
                    }
                }
            }

            if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActAttack)
            {
                var objs =await GetPowerCompStrAndColor();
                powerInfo.text = objs.Item2;
                //powerInfo.color = compColor;
                powerInfo.gameObject.SetActive(true);
                powerInfo.enabled = objs.Item1;
            }
            else
            {
                powerInfo.enabled = false;
                powerInfo.text = String.Empty;
                powerInfo.gameObject.SetActive(false);
                //m_ActionPointText.text = string.Empty;
            }

            //部队只有在城内时才能恢复
            //if (selectedLineUpItem.LineUpState == (int)LineupState.LineupStateDefender)
            //{
            //    UpdateRemainTime();
            //}
            //else
            //{
            //    if (m_Timer != null)
            //    {
            //        //需要停止 否则SetRemainTime 会在1秒后刷新 导致数据显示问题
            //        m_Timer.Stop();
            //    }
            //    foreach (var troopItem in troopItemDic)
            //    {
            //        if (troopItem.Value != null && selectedLineUpItem.Id == troopItem.Value.lineUpId)
            //        {
            //            var entity = LMapEntityManager.I.GetEntityInfo(troopItem.Value.troopId);
            //            if (entity != null)
            //            {
            //                _powerRecoverTime.text = string.Empty;
            //                _powerRecoverTime.gameObject.SetActive(false);
            //                var val = entity.PlayerTroop.combatPower * 1.0f / selectedLineUpItem.MaxPower;
            //                _powerSlider.value = val;

            //                //部队不在城内 当前战力读取实体里的
            //                _powerText.text = UIStringUtils.ExchangeValue(entity.PlayerTroop.combatPower, 2);

            //                if (entity.PlayerTroop.combatPower < selectedLineUpItem.MaxPower)
            //                {
            //                    powerDisplays.Clear();
            //                    //powerDisplays.Add(string.Format("{0}{1}", (((float)entity.PlayerTroop.combatPower / selectedLineUpItem.MaxPower) * 100).ToString("f1"), "%"));
            //                    powerDisplays.Add(UIStringUtils.FormatPercentByLanguage((float)entity.PlayerTroop.combatPower / selectedLineUpItem.MaxPower));
            //                    var speed = (selectedLineUpItem.MaxPower - entity.PlayerTroop.combatPower) / (0.001f * selectedLineUpItem.RecoverTime);
            //                    _powerDisplay.RefreshList(powerDisplays, false);
            //                }
            //                else
            //                {
            //                    _powerDisplay.Kill();
            //                    //_powerSliderText.text = string.Format("{0}{1}", (val * 100).ToString("f1"), "%");
            //                    _powerSliderText.text = UIStringUtils.FormatPercentByLanguage(val);
            //                }
            //            }
            //            break;
            //        }
            //    }
            //}
        }

        private async UniTask< long> GetTroopBattlePower()
        {
            var mainHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 0);
            var leftHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 1);
            var rightHero = GameData.I.LineUpData.GetLineUpHero(mLineup, 2);

            if (mainHero == null)
            {
                return 0;
            }

            var power = 0d;
            var heroPower = 0d;
            var buildingPower = LPlayer.I.GetPower(PowerConst.DRAGON_POWER).val;
            var soldierCount = 0d;

            heroPower += mainHero.Power();
            heroPower += leftHero?.Power() ?? 0;
            heroPower += rightHero?.Power() ?? 0;


            if (null != soldierSlider)
            {
                
                    soldierCount += soldierSlider.value;
                 
            }

            power = (heroPower + buildingPower) / MetaConfig.MapTroopHeroPowerParam * soldierCount;

            var bilv = GameData.I.PlayerData.PropData.GetPropSpeedUp(Game.Config.GameConfig.PROP_POWER);
            var powUp = await AllianceGameData.I.AlliancePropGameData.GetPropSpeedUp(Game.Config.GameConfig.AlliancePowerUp);
            
            var tower = power * (1 + bilv+ powUp);

            //D.Error?.Log($"英雄战力：{heroPower} 雕像战力:{buildingPower} 士兵数：{soldierCount}" +
            //    $"战力加成(11507)：{bilv} 联盟属性加成(30029):{powUp}  " +
            //    $"总值是：{tower}");

            return (long)tower;
        }

        /// <summary>
        /// 获取战力对比以及相应颜色显示
        /// </summary>
        /// <param name="str"></param>
        /// <param name="color"></param>
        /// <returns></returns>
        private async UniTask<(bool, string, Color, int)> GetPowerCompStrAndColor()
        {
            long myPower =await GetTroopBattlePower();
            long enemyPower = GetTargetPower();
            return await UIHelper.GetPowerCompStrAndColor(myPower, enemyPower);
        }

        /// <summary>
        /// 获取目标战力
        /// </summary>
        /// <returns></returns>
        private long GetTargetPower()
        {
            long targetPower = -1;
            if (m_MarchArgs != null && m_MarchArgs.act == MarchAct.MarchActAttack)
            {
                var entity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
                if (entity == null)
                {
                    Debug.LogWarningFormat($"UISelectTroop  targetId : {m_MarchArgs.targetId} not find!");
                }
                else
                {
                    if (entity.type == MapUnitType.MapUnitNpcTroop)
                    {
                        var powerConfig = CNpcTroopPowerExtension.GetNpcTroopPower(entity.NpcTroop.npcTroopID, entity.NpcTroop.GetLevel());
                        //Debug.LogErrorFormat(_level+"    +++++++++++++++++***********   "+ powerConfig.Level);
                        if (powerConfig != null)
                        {
                            targetPower = (long)(powerConfig.Power / MetaConfig.MapTroopHeroPowerParam * powerConfig.SoldierNum);
                        }
                        else
                        {
                            targetPower = entity.NpcTroop.combat;
                        }
                        //Debug.LogFormat("Id={0},BeginNum={1}", powerConfig.Id, powerConfig.BeginNum);
                    }
                    else if (entity.type == MapUnitType.MapUnitWorldBoss)
                    {
                        targetPower = entity.WorldBoss.combat;
                    }
                    else
                    {
                        Debug.LogFormat($"UISelectTroop  targetId : {m_MarchArgs.targetId} type {entity.type} need imp!");
                    }
                }
            }

            return targetPower;
        }


        private void RefreshHero(Game.Data.HeroData mainHero, Game.Data.HeroData leftHero, Game.Data.HeroData rightHero)
        {



            if (mainHero != null)
            {
                UIHeroNewUtils.SetTroopSpineData(heroItem1, mainHero, -1, -1);
            }
            else
            {
                UIHeroNewUtils.SetData(heroItem1, HeroEmptyEnum.Add);
            }

            //Transform red2 = heroItem2.transform.Find("Empty/Add/Red").transform;
            //red2.transform.localScale = Vector3.zero;

            if (leftHero != null)
            {
                UIHeroNewUtils.SetTroopSpineData(heroItem2, leftHero, -1, -1);
            }
            else
            {
                //if (LPlayer.I.GetMainCityLevel() >= MetaConfig.LieutenantInterfaceDisplayRules[0])
                //{
                D.Warning.Log("RefreshHero heroItem2");
                UIHeroNewUtils.SetData(heroItem2, mainHero == null? HeroEmptyEnum.None :HeroEmptyEnum.Add);
                //if (GameData.I.LineUpData.CanUpDeputyHero(1))
                //{
                //    red2.transform.localScale = Vector3.one;
                //}

                //}
                //else
                //{
                //    UIHeroNewUtils.SetData(heroItem2, HeroEmptyEnum.Lock);
                //}
            }

            //Transform red3 = heroItem3.transform.Find("Empty/Add/Red").transform;
            //red3.transform.localScale = Vector3.zero;


            if (rightHero != null)
            {
                UIHeroNewUtils.SetTroopSpineData(heroItem3, rightHero, -1, -1);
            }
            else
            {
                //if (LPlayer.I.GetMainCityLevel() >= MetaConfig.LieutenantInterfaceDisplayRules[1])
                //{
                UIHeroNewUtils.SetData(heroItem3, mainHero == null? HeroEmptyEnum.None :HeroEmptyEnum.Add);
                //if (GameData.I.LineUpData.CanUpDeputyHero(2))
                //{
                //    red3.transform.localScale = Vector3.one;
                //}

                //}
                //else
                //{
                //    UIHeroNewUtils.SetData(heroItem3, HeroEmptyEnum.Lock);
                //}
            }
        }

        //private bool IsHero2Unlock()
        //{
        //    return LPlayer.I.GetMainCityLevel() >= MetaConfig.LieutenantInterfaceDisplayRules[0];
        //}

        //private bool IsHero3Unlock()
        //{
        //    return LPlayer.I.GetMainCityLevel() >= MetaConfig.LieutenantInterfaceDisplayRules[1];
        //}



        public int LineUpIndex
        {
            get
            {
                if (GameData.I.LineUpData.lockedList == null
                    || GameData.I.LineUpData.lockedList.Count <= curLineUpIndex)
                    return 0;

                return GameData.I.LineUpData.lockedList[curLineUpIndex];
            }
        }




        private void DefaultCreateTroopCallback(int soldiers, List<int> heroes, int killTimes = 0)
        {
            if (m_MarchArgs == null)
            {
                Debug.LogWarningFormat("m_MarchArgs is null");
                return;
            }
            var targetPosition = Vector3.zero;
            if (m_MarchArgs.act == cspb.MarchAct.MarchActMove)
            {
                targetPosition = m_MarchArgs.targetPosition;
                //Debug.LogFormat("向服务器发送行军的消息++++9999999999   " + targetPosition + "  " + heroes.Count + "  " + soldiers.Count);
                LMapEntityPlayerTroop.RequestMove(LineUpIndex, targetPosition, soldiers, heroes);
                //GameAudio.PlayAudio(AudioID.LaunchMarch);

                Close();

                return;
            }

            Debug.LogFormat("DefaultCreateTroopCallback m_MarchArgs.act ={0} targetId={1}", m_MarchArgs.act, m_MarchArgs.targetId);
            if (m_MarchArgs.act == cspb.MarchAct.MarchActJoinRally)
            {
                if (mLineup != null)
                {
                    LMapEntityPlayerTroop.RequestJoinRally(mLineup, m_MarchArgs.targetId, 0, LineUpIndex, soldiers);
                }

                //加入集结不需要获取目标实体 TODO

                Close();

                return;
            }

            var targetEntity = LMapEntityManager.I.GetEntityInfo(m_MarchArgs.targetId);
            if (targetEntity != null)
            {
                //Debug.LogError("========================m_MarchArgs.act:::" + m_MarchArgs.act);
                switch (m_MarchArgs.act)
                {
                    case cspb.MarchAct.MarchActAttack:
                        targetPosition = targetEntity.CurrentPosition;
                        LMapEntityPlayerTroop.RequestNewTroop(LineUpIndex, m_MarchArgs.targetId, m_MarchArgs.act, soldiers,
                            heroes,
                            targetPosition,
                            m_MarchArgs.isCampAfterHunt);
                        break;
                    case cspb.MarchAct.MarchActRally:
                        LMapEntityPlayerTroop.RequestNewTroop(LineUpIndex, m_MarchArgs.targetId, m_MarchArgs.act, soldiers,
                        heroes,
                        targetPosition,
                        m_MarchArgs.rallyTime);
                        break;
                    case cspb.MarchAct.MarchActReinforce:
                    case cspb.MarchAct.MarchActGather:
                    case cspb.MarchAct.MarchActOccupy:
                    case cspb.MarchAct.MarchActCaravan:
                        LMapEntityPlayerTroop.RequestNewTroop(LineUpIndex, m_MarchArgs.targetId, m_MarchArgs.act, soldiers, heroes, targetPosition);
                        break;
                }
            }

            Close();

        }

    }
}