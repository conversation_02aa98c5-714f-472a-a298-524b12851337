﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using System.Collections.Generic;
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    //记录prefab与section之间的引用关系，从而可以从生成的prefab导入mask贴图数据到编辑器中
    public static class PolygonRiverAssetManifestGenerator
    {
        //返回每个river使用的prefab路径
        public static void Generate(string folderPath, PolygonRiverLayer layer)
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                EditorUtility.DisplayDialog("Error", "invalid river asset folder", "OK");
                return;
            }
            Dictionary<int, string> riverID = new Dictionary<int, string>();
            int lodCount = layer.layerData.lodConfig.lodConfigs.Length;
            if (lodCount == 0)
            {
                EditorUtility.DisplayDialog("Error", "Invalid river layer lod count", "OK");
                return;
            }

            if (Directory.Exists(folderPath))
            {
                List<IMapObjectData> allPolygonRivers = new List<IMapObjectData>();
                layer.GetAllObjects(allPolygonRivers);

                List<config.RiverPrefab> riverPrefabs = new List<config.RiverPrefab>();
                for (int i = 0; i < allPolygonRivers.Count; ++i)
                {
                    GetRiverPrefabs(folderPath, i, allPolygonRivers[i] as PolygonRiverData, layer, riverPrefabs);
                }

                SaveRiverPrefabInfosToFile(folderPath, riverPrefabs);

                AssetDatabase.Refresh();
            }
        }

        static void GetRiverPrefabs(string folder, int idx, PolygonRiverData river, PolygonRiverLayer layer, List<config.RiverPrefab> riverPrefabInfos)
        {
            var sections = river.sections;
            for (int s = 0; s < sections.Count; ++s)
            {
                string sectionPrefabPath = PolygonRiverAssetGenerator.GetAssetPath(folder, "", idx, s, 0, "prefab");

                bool useRenderTextureAtLOD = UseRenderTextureAtLOD(layer, 0);
                if (!useRenderTextureAtLOD)
                {
                    riverPrefabInfos.Add(new config.RiverPrefab(river.id, s, sectionPrefabPath));
                }
            }
        }

        //是否在lod上使用render texture
        static bool UseRenderTextureAtLOD(PolygonRiverLayer layer, int lod)
        {
            var config = layer.layerData.lodConfig.lodConfigs[lod];
            return config.useRenderTexture;
        }

        static void SaveRiverPrefabInfosToFile(string folder, List<config.RiverPrefab> riverPrefabs)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.RiverPrefabManifestFileVersion);

            int n = riverPrefabs.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(riverPrefabs[i].riverID);
                writer.Write(riverPrefabs[i].sectionIndex);
                Utils.WriteString(writer, riverPrefabs[i].prefabPath);
            }

            var data = stream.ToArray();
            string path = MapCoreDef.GetRiverPrefabManifestFilePath(folder);
            File.WriteAllBytes(path, data);
            writer.Close();
        }
    }
}

#endif