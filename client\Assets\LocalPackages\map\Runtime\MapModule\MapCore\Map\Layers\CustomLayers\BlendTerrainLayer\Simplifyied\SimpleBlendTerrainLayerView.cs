﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class SimpleBlendTerrainLayerView : MapLayerView
    {
        public SimpleBlendTerrainLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = layerData.map.view.reusableGameObjectPool;
            CreateLOD1GameObjects();
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var p in mLOD0TerrainTiles)
            {
                Utils.DestroyObject(p.Value);
            }
            mLOD0TerrainTiles = null;

            for (int i = 0; i < mLOD1TerrainTiles.Count; ++i)
            {
                var list = mLOD1TerrainTiles[i];
                if (list != null)
                {
                    int n1 = list.GetLength(0);
                    int n2 = list.GetLength(1);
                    for (int k = 0; k < n1; ++k)
                    {
                        for (int j = 0; j < n2; ++j)
                        {
                            Utils.DestroyObject(list[k, j]);
                        }
                    }
                }
            }
            mLOD1TerrainTiles = null;
        }

        void CreateLOD1GameObjects()
        {
            var map = layerData.map;
            var terrainLayerData = layerData as SimpleBlendTerrainLayerData;
            var otherLODTiles = terrainLayerData.otherLODTiles;
            int nDefaultLODs = terrainLayerData.defaultLODCount;
            int nLODs = terrainLayerData.lodConfig.lodConfigs.Length;
            mLOD1TerrainTiles = new List<GameObject[,]>(nLODs);
            //placeholder for lod0
            mLOD1TerrainTiles.Add(null);

            mCachedVisibilityChangedGameObjects = new List<GameObject>[terrainLayerData.tilePrefabPaths.Length];
            for (int i = 0; i < mCachedVisibilityChangedGameObjects.Length; ++i)
            {
                //todo,根据max tile count来计算list的大小
                mCachedVisibilityChangedGameObjects[i] = new List<GameObject>();
            }

            if (otherLODTiles != null)
            {
                for (int i = nDefaultLODs; i < nLODs; ++i)
                {
                    int r = otherLODTiles[i - nDefaultLODs].GetLength(0);
                    int c = otherLODTiles[i - nDefaultLODs].GetLength(1);
                    mLOD1TerrainTiles.Add(new GameObject[r, c]);
                }
            }
        }

        public void OnShowLOD0Tile(int x, int y)
        {
            int key = Utils.MakeInt32Key(x, y);
#if UNITY_EDITOR
            GameObject obj = null;
            mLOD0TerrainTiles.TryGetValue(key, out obj);
            if (!Object.ReferenceEquals(obj, null))
            {
                Debug.Assert(false, string.Format("Show object at lod 0 {0}_{1} failed!", x, y));
            }
#endif
            var layerData = mLayerData as SimpleBlendTerrainLayerData;
            int tileType;
            string prefabPath = layerData.GetTilePrefabPath(x, y, out tileType);
#if UNITY_EDITOR
            Debug.Assert(!string.IsNullOrEmpty(prefabPath));
#endif

            var newObj = GetFromCache(tileType);
            if (Object.ReferenceEquals(newObj, null))
            {
                newObj = mObjectPool.Require(prefabPath);
            }

#if UNITY_EDITOR
            Utils.HideGameObject(newObj);
#endif
            newObj.transform.localPosition = new Vector3(x * layerData.tileWidth, 0, y * layerData.tileHeight) + layerData.layerOffset;
            newObj.transform.SetParent(root.transform, false);
            mLOD0TerrainTiles.Add(key, newObj);
        }

        public void OnHideLOD0Tile(int x, int y)
        {
            int key = Utils.MakeInt32Key(x, y);
            GameObject obj;
            mLOD0TerrainTiles.TryGetValue(key, out obj);
#if UNITY_EDITOR
            if (Object.ReferenceEquals(obj, null))
            {
                Debug.Assert(false, string.Format("Hide object at lod 0 {0}_{1} failed!", x, y));
            }
#endif
            var layerData = mLayerData as SimpleBlendTerrainLayerData;
            int tileType;
            string prefabPath = layerData.GetTilePrefabPath(x, y, out tileType);
            mLOD0TerrainTiles.Remove(key);
            AddToCache(tileType, obj);
        }

        GameObject GetFromCache(int tileType)
        {
            var list = mCachedVisibilityChangedGameObjects[tileType];
            if (list.Count > 0)
            {
                var obj = list[list.Count - 1];
                list.RemoveAt(list.Count - 1);
                return obj;
            }
            return null;
        }

        void AddToCache(int tileType, GameObject obj)
        {
            mCachedVisibilityChangedGameObjects[tileType].Add(obj);
        }

        void RemoveCache()
        {
            var layerData = mLayerData as SimpleBlendTerrainLayerData;
            string[] tilePrefabPaths = layerData.tilePrefabPaths;
            for (int i = 0; i < mCachedVisibilityChangedGameObjects.Length; ++i)
            {
                string prefabPath = tilePrefabPaths[i];
                var list = mCachedVisibilityChangedGameObjects[i];
                int n = list.Count;
                for (int k = 0; k < n; ++k)
                {
                    mObjectPool.Release(prefabPath, list[k], layerData.map);
                }
                list.Clear();
            }
        }

        public void OnFinishUpdateViewport()
        {
            RemoveCache();
        }

        //显示地图对象的模型
        public void OnShowLOD1Object(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mLOD1TerrainTiles.Count)
            {
                lod = mLOD1TerrainTiles.Count - 1;
            }

#if UNITY_EDITOR
            if (mLOD1TerrainTiles[lod][y, x] != null)
            {
                Debug.Assert(false, string.Format("Show object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            var obj = mObjectPool.Require(data.GetAssetPath(lod));
            Utils.HideGameObject(obj);
            obj.transform.localPosition = data.GetPosition();

            obj.transform.SetParent(root.transform, false);
            mLOD1TerrainTiles[lod][y, x] = obj;
        }

        //隐藏地图对象的模型
        public void OnHideLOD1Object(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mLOD1TerrainTiles.Count)
            {
                lod = mLOD1TerrainTiles.Count - 1;
            }
#if UNITY_EDITOR
            if (mLOD1TerrainTiles[lod][y, x] == null)
            {
                Debug.Assert(false, string.Format("Hide object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            mObjectPool.Release(data.GetAssetPath(lod), mLOD1TerrainTiles[lod][y, x], layerData.map);
            mLOD1TerrainTiles[lod][y, x] = null;
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        List<GameObject[,]> mLOD1TerrainTiles;
        Dictionary<int, GameObject> mLOD0TerrainTiles = new Dictionary<int, GameObject>();
        GameObjectPool mObjectPool;

        List<GameObject>[] mCachedVisibilityChangedGameObjects;
    };
}