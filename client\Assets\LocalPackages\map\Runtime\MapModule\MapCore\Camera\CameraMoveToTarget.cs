﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机移动并缩放到目标点
    public class CameraMoveToTarget : ZoomActionBase
    {
        enum State
        {
            Idle,
            PrepareMoving,
            Moving,
        }

        public CameraMoveToTarget(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
            mState = State.Idle;

            Init();
        }

        public void StartMoving(Vector3 targetPos, float moveDuration, float zoomDuration, System.Action onCameraReachTarget, bool clampBorder, bool forceMoving, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            DoStartMoving(targetPos, moveDuration, zoomDuration, null, onCameraReachTarget, clampBorder, forceMoving, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        public void StartMovingByCurve(Vector3 targetPos, float moveDuration, float zoomDuration, AnimationCurve curve, System.Action onCameraReachTarget, bool clampBorder, bool forceMoving, bool triggerCameraReachTargetCallbackWhenBeingInterrupted = false)
        {
            DoStartMoving(targetPos, moveDuration, zoomDuration, curve, onCameraReachTarget, clampBorder, forceMoving, triggerCameraReachTargetCallbackWhenBeingInterrupted);
        }

        void DoStartMoving(Vector3 targetPos, float moveDuration, float zoomDuration, AnimationCurve curve, System.Action onCameraReachTarget, bool clampBorder, bool forceMoving, bool triggerCameraReachTargetCallbackWhenBeingInterrupted)
        {
            if (MapCameraMgr.isMovingToTarget)
            {
                MapCameraMgr.EnableCameraDrag(true);
                MapCameraMgr.EnableCameraZoom(true);
                MapCameraMgr.clampBorder = mOriginalClampBorderValue;

                if(mCameraMovingReentranceCallback != null)
                {
                    mCameraMovingReentranceCallback();
                }
            }

            if (enabled == false || forceMoving)
            {
                if (MapCameraMgr.isMovingToTarget && mTriggerCameraReachTargetCallbackWhenBeingInterrupted)
                {
                    if (mCameraReachTarget != null)
                    {
                        mCameraReachTarget();
                    }
                }

                mTriggerCameraReachTargetCallbackWhenBeingInterrupted = triggerCameraReachTargetCallbackWhenBeingInterrupted;

                //save state
                mMoveState.targetPos = targetPos;
                mMoveState.moveDuration = moveDuration;
                mMoveState.zoomDuration = zoomDuration;
                mMoveState.cameraReachTargetCallback = onCameraReachTarget;
                mMoveState.clampBorder = clampBorder;
                mMoveState.forceMoving = forceMoving;

                MapCameraMgr.isMovingToTarget = true;
                enabled = true;
                mState = State.PrepareMoving;
                mMoveDuration = moveDuration;
                mStartPos = MapCameraMgr.updatedCameraPosition;
                mEndPos = targetPos;
                mZoomDuration = zoomDuration;
                mCameraReachTarget = onCameraReachTarget;
                mClampBorder = clampBorder;
                mMoveCurve = curve;
                if (NeedZoomCamera(mStartPos, mEndPos))
                {
                    mMoveEndPos = CalculateMoveEndPos(mEndPos, MapCameraMgr.MapCamera.transform.forward, mStartPos.y);
                    mZoomDuration = zoomDuration;
                }
                else
                {
                    mMoveEndPos = mEndPos;
                    mZoomDuration = 0;
                }
            }
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            if (mState == State.PrepareMoving)
            {
                MapCameraMgr.InvokeStartMovingCallback();

                mState = State.Moving;

                Init();

                mOriginalClampBorderValue = MapCameraMgr.clampBorder;
                MapCameraMgr.clampBorder = mClampBorder;

                mElapsedTime = 0;
                mIsZooming = false;
            }
            else if (mState == State.Idle)
            {
                enabled = false;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            if (mStartPos == mEndPos)
            {
                isFinished = true;
                return mEndPos;
            }

            mElapsedTime += Time.deltaTime;

            if (mIsZooming)
            {
                bool finished = UpdateZooming();
                if (finished)
                {
                    isFinished = true;
                }

                return GetCameraPos();
            }
            else
            {
                //平移相机
                float t = 1.0f;
                if (!Mathf.Approximately(mMoveDuration, 0))
                {
                    t = Mathf.Clamp(mElapsedTime, 0, mMoveDuration) / mMoveDuration;
                    if (mMoveCurve != null)
                    {
                        t = Mathf.Clamp01(mMoveCurve.Evaluate(t));
                    }
                }

                float x = Mathf.SmoothStep(mStartPos.x, mMoveEndPos.x, t);
                float y = Mathf.SmoothStep(mStartPos.y, mMoveEndPos.y, t);
                float z = Mathf.SmoothStep(mStartPos.z, mMoveEndPos.z, t);

                if (mElapsedTime >= mMoveDuration)
                {
                    //看看是否需要zoom
                    if (NeedZoomCamera(mStartPos, mEndPos))
                    {
                        //下一帧开始缩放相机
                        mIsZooming = true;
                        mElapsedTime = 0;
                        t = 0;
                        mStartPos = mMoveEndPos;
                        mMoveEndPos = mEndPos;
                        mMoveDuration = mZoomDuration;

                        var cameraSetting = MapCameraMgr.cameraSetting;
                        float dxf = cameraSetting.GetCameraDXFFromHeight(mEndPos.y);
                        AutoZoomToVDF(dxf, mZoomDuration * 1000);
                    }
                    else
                    {
                        //不用zoom
                        isFinished = true;
                    }
                }

                return new Vector3(x, y, z);
            }
        }

        bool NeedZoomCamera(Vector3 startPos, Vector3 targetPos)
        {
            if (mZoomDuration == 0)
            {
                return false;
            }

            var deltaY = targetPos.y - startPos.y;
            if (Mathf.Approximately(deltaY, 0))
            {
                return false;
            }

            return true;
        }

        Vector3 CalculateMoveEndPos(Vector3 endPos, Vector3 cameraDir, float startY)
        {
            float t = (startY - endPos.y) / -cameraDir.y;
            return endPos - t * cameraDir;
        }

        public void Finish(bool restoreState)
        {
            MapCameraMgr.isMovingToTarget = false;

            MapCameraMgr.clampBorder = mOriginalClampBorderValue;

            mState = State.Idle;

            if (!restoreState)
            {
                if (mCameraReachTarget != null)
                {
                    mCameraReachTarget();
                }
            }
        }

        public override void OnFinishImpl()
        {
            Finish(false);
        }

        class MoveState
        {
            public Vector3 targetPos;
            public float moveDuration;
            public float zoomDuration;
            public System.Action cameraReachTargetCallback;
            public bool clampBorder;
            public bool forceMoving;
        }

        public void SaveState(bool keepOriginalDuration)
        {
            if (!keepOriginalDuration)
            {
                mMoveState.zoomDuration = Mathf.Max(0, mMoveState.zoomDuration - mElapsedTime);
            }
        }

        //从保存的状态继续移动
        public void RestoreState(bool changeAngle)
        {
            isFinished = true;
            Finish(true);

            if (changeAngle)
            {
                //temp code,重新计算target pos,按90到任意度计算
                mMoveState.targetPos = Map.currentMap.CalculateCameraPositionFromLookAtPosition(mMoveState.targetPos.x, mMoveState.targetPos.z,
                    mMoveState.targetPos.y);
            }

            StartMoving(mMoveState.targetPos, 0, mMoveState.zoomDuration, mMoveState.cameraReachTargetCallback, mMoveState.clampBorder, true);
        }

        public System.Action reentranceCallback { get { return mCameraMovingReentranceCallback; } set { mCameraMovingReentranceCallback = value; } }

        //相机的起始位置
        Vector3 mStartPos;

        //相机最终的目标位置
        Vector3 mEndPos;

        //相机平移的目标位置
        Vector3 mMoveEndPos;
        float mElapsedTime;
        float mMoveDuration;
        float mZoomDuration;
        bool mOriginalClampBorderValue;
        bool mIsZooming = false;
        System.Action mCameraReachTarget;
        State mState;
        MoveState mMoveState = new MoveState();
        AnimationCurve mMoveCurve;
        bool mClampBorder;
        bool mTriggerCameraReachTargetCallbackWhenBeingInterrupted;
        //相机如果在moving to target过程中又被触发移动到新目标后调用
        System.Action mCameraMovingReentranceCallback;
    }
}