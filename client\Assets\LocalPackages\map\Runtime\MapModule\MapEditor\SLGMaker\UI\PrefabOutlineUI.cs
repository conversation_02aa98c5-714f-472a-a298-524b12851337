﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(PrefabOutline))]
    class PrefabOutlineUI : UnityEditor.Editor
    {
        private void OnEnable()
        {
            mPrefabOutline = target as PrefabOutline;
            mPrefabOutline.Init();

            CreateDisplayOutlineVertices();
        }

        public override void OnInspectorGUI()
        {
            EditorGUI.BeginChangeCheck();

            base.OnInspectorGUI();

            var newType = (PrefabOutlineType)EditorGUILayout.EnumPopup("Outline Type", mPrefabOutline.currentOutlineType);
            if (mPrefabOutline.currentOutlineType != newType)
            {
                ChangeOutlineType(newType);
            }

            if (GUILayout.Button("Copy To Other Outline"))
            {
                CopyToOtherOutline(mPrefabOutline.currentOutlineType);
            }

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.But<PERSON>("Expand Outline"))
            {
                ExpandPrefabOutline();
            }

            if (GUILayout.Button("Reset Outline"))
            {
                mPrefabOutline.ResetOutline(mPrefabOutline.gameObject, mPrefabOutline.currentOutlineType, false);
                CreateDisplayOutlineVertices();
                EditorUtility.SetDirty(target);
            }
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Clamp To Border"))
            {
                mPrefabOutline.ClampToBorder();
                CreateDisplayOutlineVertices();
                EditorUtility.SetDirty(target);
            }

            EditorGUILayout.BeginVertical("GroupBox");
            bool edit = GUILayout.Toggle(mEditingOutline, "Edit Outline");
            if (edit != mEditingOutline)
            {
                SetEditMode(edit);
            }
            mVertexRadius = EditorGUILayout.FloatField("Vertex Radius", mVertexRadius);
            
            EditorGUILayout.LabelField("Add Vertex: Ctrl + Mouse Left Button");
            EditorGUILayout.LabelField("Remove Vertex: Ctrl + Shift + Mouse Left Button");
            EditorGUILayout.LabelField("Move Outline: Shift + Mouse Left Button");
            EditorGUILayout.EndVertical();
            bool changed = EditorGUI.EndChangeCheck();
            if (changed)
            {
                SceneView.RepaintAll();
            }
        }

        void ChangeOutlineType(PrefabOutlineType type)
        {
            mPrefabOutline.currentOutlineType = type;
            mMovingVertex = false;
            CreateDisplayOutlineVertices();
            SetEditMode(false);
        }

        void CopyToOtherOutline(PrefabOutlineType currentType)
        {
            var dlg = EditorUtils.CreateInputDialog("Copy To Other Outline");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Expand Size", "", "3"),
                };
            dlg.Show(items, (List<InputDialog.Item> values)=> {
                var radiusText = (values[0] as InputDialog.StringItem).text;
                bool suc = float.TryParse(radiusText, out float size);
                if (suc)
                {
                    if (currentType == PrefabOutlineType.NavMeshObstacle)
                    {
                        mPrefabOutline.CopyFromTo(PrefabOutlineType.NavMeshObstacle, PrefabOutlineType.ObjectPlacementObstacle, size);
                    }
                    else
                    {
                        mPrefabOutline.CopyFromTo(PrefabOutlineType.ObjectPlacementObstacle, PrefabOutlineType.NavMeshObstacle, size);
                    }
                    return true;
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Invalid size", "OK");
                    return false;
                }
            });
            EditorUtility.SetDirty(target);
        }

        void OnSceneGUI()
        {
            DrawObstacleEditor();
            EditOutline();
        }

        void DrawObstacleEditor()
        {
            if (mWorldSpaceOutlineVertices == null)
            {
                return;
            }

            bool isSimple = PolygonUtils.IsSimplePolygon(mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType));
            mPrefabOutline.SetSimplePolygon(mPrefabOutline.currentOutlineType, isSimple);

            for (int i = 0; i < mWorldSpaceOutlineVertices.Length; ++i)
            {
                mWorldSpaceOutlineVertices[i] = mPrefabOutline.TransformToWorldPosition(mLocalSpaceOutlineVertices[i]);
            }

            if (isSimple)
            {
                if (mEditingOutline)
                {
                    Handles.color = Color.green;
                }
                else
                {
                    Handles.color = Color.white;
                }
            }
            else
            {
                Handles.color = Color.red;
            }
            Handles.DrawPolyLine(mWorldSpaceOutlineVertices);

            for (int i = 0; i < mWorldSpaceOutlineVertices.Length; ++i)
            {
                Handles.SphereHandleCap(0, mWorldSpaceOutlineVertices[i], Quaternion.identity, mVertexRadius, EventType.Repaint);
            }
        }

        void EditOutline()
        {
            if (mEditingOutline)
            {
                var currentEvent = Event.current;
                var camera = SceneView.GetAllSceneCameras()[0];
                var worldPos = EditorUtils.FromEditorScreenToWorldPosition(currentEvent.mousePosition, camera, mPrefabOutline.gameObject.transform.position.y);

                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
                {
                    if (currentEvent.alt == false)
                    {
                        mLeftButtonDown = true;
                    }
                    var localPos = mPrefabOutline.TransformToLocalPosition(worldPos);
                    if (currentEvent.control == false)
                    {
                        mSelectedVertex = PickVertex(localPos, mVertexRadius);
                    }
                    else
                    {
                        if (currentEvent.shift)
                        {
                            mSelectedVertex = PickVertex(localPos, mVertexRadius);
                            RemoveVertex(mSelectedVertex);
                            CreateDisplayOutlineVertices();
                        }
                        else
                        {
                            AddVertex(localPos);
                            CreateDisplayOutlineVertices();
                        }
                    }
                }

                if (currentEvent.type == EventType.MouseDrag && currentEvent.button == 0)
                {
                    mMouseMover.Update(worldPos);
                    if (currentEvent.shift && mSelectedVertex >= 0)
                    {
                        MoveOutline(mMouseMover.GetDelta());
                    }
                }

                if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
                {
                    mLeftButtonDown = false;
                    mMovingVertex = false;
                    EditorUtility.SetDirty(target);
                    mMouseMover.Reset();
                }

                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 1)
                {
                    //pick vertex
                    var localPos = mPrefabOutline.TransformToLocalPosition(worldPos);
                    int vertexIndex = PickVertex(localPos, mVertexRadius);
                    if (vertexIndex >= 0)
                    {
                        var vertices = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
                        Debug.Log($"vertex {vertexIndex}: {vertices[vertexIndex].ToString()}");
                        SceneView.RepaintAll();
                    }
                }

                if (mLeftButtonDown)
                {
                    if (mSelectedVertex >= 0)
                    {
                        if (mMovingVertex == false)
                        {
                            mMovingVertex = true;
                        }

                        var localPosInRoot = mPrefabOutline.TransformToLocalPosition(worldPos);

                        if (mPrefabOutline.hasRange)
                        {
                            localPosInRoot.x = Mathf.Clamp(localPosInRoot.x, mPrefabOutline.minX, mPrefabOutline.maxX);
                            localPosInRoot.z = Mathf.Clamp(localPosInRoot.z, mPrefabOutline.minZ, mPrefabOutline.maxZ);
                            worldPos = mPrefabOutline.TransformToWorldPosition(localPosInRoot);
                        }

                        var localPos = mPrefabOutline.TransformToLocalPosition(worldPos);

                        MoveVertex(mSelectedVertex, localPos);
                        SceneView.RepaintAll();
                    }
                }

                HandleUtility.AddDefaultControl(0);
            }
        }

        void CreateDisplayOutlineVertices()
        {
            var outlineVertices = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            int n = outlineVertices.Count;
            if (n > 0)
            {
                mLocalSpaceOutlineVertices = new Vector3[n + 1];
                for (int i = 0; i < n; ++i)
                {
                    mLocalSpaceOutlineVertices[i] = outlineVertices[i];
                }
                mLocalSpaceOutlineVertices[n] = outlineVertices[0];
                mWorldSpaceOutlineVertices = new Vector3[mLocalSpaceOutlineVertices.Length];

                SceneView.RepaintAll();
            }
            mSelectedVertex = -1;
        }

        void SetEditMode(bool edit)
        {
            mEditingOutline = edit;
            mSelectedVertex = -1;
            mLeftButtonDown = false;
            SceneView.RepaintAll();
        }

        public int PickVertex(Vector3 localPos, float radius)
        {
            //convert to local space radius
            radius = radius / mPrefabOutline.gameObject.transform.localScale.x;
            float r2 = radius * radius;
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            for (int i = 0; i < outline.Count; ++i)
            {
                var d = outline[i] - localPos;
                if (d.sqrMagnitude <= r2)
                {
                    return i;
                }
            }
            return -1;
        }

        void MoveOutline(Vector3 offset)
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            for (int i = 0; i < outline.Count; ++i)
            {
                outline[i] = outline[i] + offset;
                mLocalSpaceOutlineVertices[i] = outline[i];
                if (i == 0)
                {
                    mLocalSpaceOutlineVertices[mLocalSpaceOutlineVertices.Length - 1] = outline[i];
                }
            }
        }

        public void MoveVertex(int index, Vector3 localPos)
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            if (index >= 0 && index < outline.Count)
            {
                outline[index] = localPos;
                mLocalSpaceOutlineVertices[index] = localPos;
                if (index == 0)
                {
                    mLocalSpaceOutlineVertices[mLocalSpaceOutlineVertices.Length - 1] = localPos;
                }
            }
        }

        public void AddVertex(Vector3 localPos)
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            var idx = Utils.FindNearestEdgeDistance(localPos, outline);
            InsertVertex(idx, localPos);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            outline.Insert(index, localPos);
            CreateLoopVertices();
        }

        public void RemoveVertex(int index)
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            if (outline.Count > 3 && index >= 0 && index < outline.Count)
            {
                outline.RemoveAt(index);
                CreateLoopVertices();
            }
        }

        void CreateLoopVertices()
        {
            var outline = mPrefabOutline.GetOutlineVertices(mPrefabOutline.currentOutlineType);
            mLocalSpaceOutlineVertices = new Vector3[outline.Count + 1];
            for (int i = 0; i < outline.Count; ++i)
            {
                mLocalSpaceOutlineVertices[i] = outline[i];
            }
            mLocalSpaceOutlineVertices[outline.Count] = outline[0];
        }

        void ExpandPrefabOutline()
        {
            var inputDialog = EditorUtils.CreateInputDialog("Expand Prefab Outline");

            var items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Radius", "", "1"),
                    };
            inputDialog.Show(items, OnExpandOutline);
        }

        bool OnExpandOutline(List<InputDialog.Item> param)
        {
            var radiusStr = (param[0] as InputDialog.StringItem).text;
            if (!string.IsNullOrEmpty(radiusStr))
            {
                float radius;
                bool suc = Utils.ParseFloat(radiusStr, out radius);

                if (suc)
                {
                    mPrefabOutline.Expand(mPrefabOutline.currentOutlineType, radius);
                    CreateDisplayOutlineVertices();
                    SceneView.RepaintAll();
                    EditorUtility.SetDirty(target);
                    return true;
                }
            }
            return false;
        }

        PrefabOutline mPrefabOutline;
        Vector3[] mWorldSpaceOutlineVertices;
        Vector3[] mLocalSpaceOutlineVertices;
        bool mEditingOutline = false;
        bool mLeftButtonDown = false;
        bool mMovingVertex = false;
        float mVertexRadius = 0.5f;
        int mSelectedVertex = -1;
        MouseMover mMouseMover = new MouseMover();
    }
}
#endif