﻿ 



 
 

#if UNITY_EDITOR_WIN

using UnityEngine;

namespace TFW.Map
{
    public class FixTerrainEdgeNormal
    {
        public void Fix(BlendTerrainLayer layer)
        {
            int horizontalTileCount = layer.horizontalTileCount;
            int verticalTileCount = layer.verticalTileCount;

            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    FixTile(j, i, layer);
                }
            }
        }

        void FixTile(int x, int y, BlendTerrainLayer layer)
        {   
            var tile = layer.GetTile(x, y);
            if (tile != null)
            {
                var tileHeights = tile.heights;
                if (tileHeights == null)
                {
                    return;
                }

                var layerView = layer.layerView as BlendTerrainLayerView;

                var curTileMesh = layerView.GetTerrainHeightMesh(x, y);
                var curTileNormals = curTileMesh.normals;
                int r1 = tile.resolution + 1;
                for (int i = 0; i < mNeighbourOffset.Length; ++i)
                {
                    int nx = mNeighbourOffset[i].x + x;
                    int ny = mNeighbourOffset[i].y + y;
                    var neighbourTile = layer.GetTile(nx, ny);
                    if (neighbourTile != null && neighbourTile.heights != null && neighbourTile.resolution == tile.resolution)
                    {
                        var neighbourTileMesh = layerView.GetTerrainHeightMesh(nx, ny);
                        var neighbourTileNormal = neighbourTileMesh.normals;
                        
                        if (i == 0)
                        {
                            //fix left edge normals
                            for (int v = 0; v < r1; ++v)
                            {
                                int neighbourVertexIdx = v * r1 + r1 - 1;
                                int curTileVertexIdx = v * r1;
                                Vector3 averageNormal = curTileNormals[curTileVertexIdx] + neighbourTileNormal[neighbourVertexIdx];
                                averageNormal.Normalize();
                                curTileNormals[curTileVertexIdx] = averageNormal;
                                neighbourTileNormal[neighbourVertexIdx] = averageNormal;
                            }
                        }
                        else if (i == 1)
                        {
                            //fix top edge normals
                            for (int h = 0; h < r1; ++h)
                            {
                                int neighbourVertexIdx = h;
                                int curTileVertexIdx = (r1 - 1) * r1 + h;
                                Vector3 averageNormal = curTileNormals[curTileVertexIdx] + neighbourTileNormal[neighbourVertexIdx];
                                averageNormal.Normalize();
                                curTileNormals[curTileVertexIdx] = averageNormal;
                                neighbourTileNormal[neighbourVertexIdx] = averageNormal;
                            }
                        }
                        else if (i == 2)
                        {
                            //fix right edge normals
                            for (int v = 0; v < r1; ++v)
                            {
                                int neighbourVertexIdx = v * r1;
                                int curTileVertexIdx = v * r1 + r1 - 1;
                                Vector3 averageNormal = curTileNormals[curTileVertexIdx] + neighbourTileNormal[neighbourVertexIdx];
                                averageNormal.Normalize();
                                curTileNormals[curTileVertexIdx] = averageNormal;
                                neighbourTileNormal[neighbourVertexIdx] = averageNormal;
                            }
                        }
                        else if (i == 3)
                        {
                            //fix bottom edge normals
                            for (int h = 0; h < r1; ++h)
                            {
                                int neighbourVertexIdx = (r1 - 1) * r1 + h;
                                int curTileVertexIdx = h;
                                    
                                Vector3 averageNormal = curTileNormals[curTileVertexIdx] + neighbourTileNormal[neighbourVertexIdx];
                                averageNormal.Normalize();
                                curTileNormals[curTileVertexIdx] = averageNormal;
                                neighbourTileNormal[neighbourVertexIdx] = averageNormal;
                            }
                        }
                        else
                        {
                            Debug.Assert(false, "todo");
                        }

                        neighbourTileMesh.normals = neighbourTileNormal;
                    }
                }
                curTileMesh.normals = curTileNormals;
                curTileMesh.UploadMeshData(false);
            }
        }

        Vector2Int[] mNeighbourOffset = new Vector2Int[4] {
            new Vector2Int(-1, 0),
            new Vector2Int(0, 1),
            new Vector2Int(1, 0),
            new Vector2Int(0, -1),
        };
    }
}


#endif