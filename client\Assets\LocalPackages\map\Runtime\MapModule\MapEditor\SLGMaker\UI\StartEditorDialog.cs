﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;
using System.IO;

namespace TFW.Map
{
    class StartEditorDialog : EditorWindow
    {
        void OnEnable()
        {
            mMapConfigFilePath = EditorPrefs.GetString("map config");
            if (!File.Exists(mMapConfigFilePath))
            {
                mMapConfigFilePath = Utils.TryToGetValidConfigFilePath();
                if (!string.IsNullOrEmpty(mMapConfigFilePath))
                {
                    EditorPrefs.SetString("map config", mMapConfigFilePath);
                }
            }
        }

        void OnGUI()
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.TextField("Map Config File", mMapConfigFilePath);
#if false
            if (GUILayout.Button("Select"))
            {
                mMapConfigFilePath = EditorUtility.OpenFilePanelWithFilters("Map Config File", "Assets", new string[] { "bytes", "bytes" });
                if (!string.IsNullOrEmpty(mMapConfigFilePath))
                {
                    mMapConfigFilePath = Utils.ConvertToUnityAssetsPath(mMapConfigFilePath);
                    EditorPrefs.SetString("map config", mMapConfigFilePath);
                }
            }
#endif
            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Start Editor"))
            {
                if (string.IsNullOrEmpty(mMapConfigFilePath))
                {
                    EditorUtility.DisplayDialog("Error", "Invalid map config file", "OK");
                }
                else
                {
                    Run();
                    Close();
                }
            }
        }

        public static void Run()
        {
            var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            scene.name = "SLGMaker";
            var gameObject = new GameObject("SLGMakerMapEditor");
            gameObject.AddComponent<DisableKeyboardDelete>();
            gameObject.tag = "EditorOnly";
            gameObject.AddComponent<SLGMakerEditor>();
            Selection.activeGameObject = gameObject;
        }

        string mMapConfigFilePath;
    }
}

#endif