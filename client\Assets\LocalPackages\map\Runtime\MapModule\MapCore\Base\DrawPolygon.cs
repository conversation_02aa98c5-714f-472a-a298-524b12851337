﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
using ClipperLib;
#endif

namespace TFW.Map
{
    public class DrawPolygon : MonoBehaviour
    {
        void OnDrawGizmos()
        {
#if UNITY_EDITOR
            if (polygon != null)
            {
                Color oldColor = Handles.color;
                Handles.color = color;
                Handles.DrawPolyLine(polygon);

                if (drawVertex)
                {
                    Handles.color = Color.red;
                    Handles.SphereHandleCap(0, polygon[0], Quaternion.identity, radius, EventType.Repaint);

                    Handles.color = Color.green;
                    Handles.SphereHandleCap(0, polygon[polygon.Length - 2], Quaternion.identity, radius, EventType.Repaint);

                    for (int i = 1; i < polygon.Length - 2; ++i)
                    {
                        Handles.color = Color.white;
                        Handles.SphereHandleCap(0, polygon[i], Quaternion.identity, radius, EventType.Repaint);
                    }
                }

                Handles.color = oldColor;
            }

            if (radius != 0)
            {
                Color oldColor = Handles.color;
                Handles.color = color;
                Handles.DrawWireDisc(center, Vector3.up, radius);
                Handles.color = oldColor;
            }
#endif
        }

#if UNITY_EDITOR
        public void SetVertices(List<IntPoint> vertices)
        {
            polygon = new Vector3[vertices.Count + 1];
            for (int i = 0; i < vertices.Count; ++i)
            {
                polygon[i] = new Vector3((float)Utils.DownScale(vertices[i].X), 0, (float)Utils.DownScale(vertices[i].Y));
            }
            //loop
            polygon[vertices.Count] = polygon[0];
        }
#endif

        public void SetVertices(List<Vector2> vertices)
        {
            polygon = new Vector3[vertices.Count + 1];
            for (int i = 0; i < vertices.Count; ++i)
            {
                polygon[i] = new Vector3(vertices[i].x, 0, vertices[i].y);
            }
            //loop
            polygon[vertices.Count] = polygon[0];
        }

        public void SetVertices(List<Vector3> vertices)
        {
            polygon = new Vector3[vertices.Count + 1];
            for (int i = 0; i < vertices.Count; ++i)
            {
                polygon[i] = vertices[i];
            }
            //loop
            polygon[vertices.Count] = polygon[0];
        }

        public void SetVertices(Vector3[] vertices)
        {
            polygon = new Vector3[vertices.Length + 1];
            for (int i = 0; i < vertices.Length; ++i)
            {
                polygon[i] = vertices[i];
            }
            //loop
            polygon[vertices.Length] = polygon[0];
        }

        public void SetCircle(Vector3 center, float radius)
        {
            this.center = center;
            this.radius = radius;
        }

        public bool drawVertex = true;
        public float radius = 0;
        public Vector3 center;
        public Color color = Color.white;
        Vector3[] polygon;
    }
}
