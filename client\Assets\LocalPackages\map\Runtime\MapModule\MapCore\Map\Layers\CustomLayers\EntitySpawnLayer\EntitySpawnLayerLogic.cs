﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.4.17
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [ExecuteInEditMode]
    [Black]
    public class EntitySpawnLayerLogic : MapLayerLogic
    {
        public enum Option
        {
            SetBrush,
            ClearBrush,
            SetVisibleRegion,
            SetInvisibleRegion,
            EditWaypoint,
        }

        public Option selectedOption = 0;
        public int brushSize = 1;
        public int selectedIndex = -1;

        public EntitySpawnLayer layer
        {
            get
            {
                var layer = Map.currentMap.GetMapLayerByID(layerID) as EntitySpawnLayer;
                return layer;
            }
        }
    }
}


#endif