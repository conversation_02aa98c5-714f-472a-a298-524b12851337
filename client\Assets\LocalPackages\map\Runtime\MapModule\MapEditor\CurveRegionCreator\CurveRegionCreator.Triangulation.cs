﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CurveRegionCreator
    {
        public void TriangulateTerritories(string folder, int lod, bool displayProgressBar, bool generateAssets)
        {
            for (int i = 0; i < mTerritories.Count; ++i)
            {
                TriangulateTerritory(mTerritories[i], folder, lod, generateAssets);
                if (displayProgressBar)
                {
                    bool cancel = EditorUtility.DisplayCancelableProgressBar("Generating Region Data", $"Triangulating Territory {i + 1}/{mTerritories.Count}", 0.5f + (float)(i + 1) / mTerritories.Count);
                    if (cancel)
                    {
                        break;
                    }
                }
            }   
        }

        void CalculateTerritoryAllSharedEdgeSplineVertices(Territory t)
        {
            for (int i = 0; i < t.sharedEdges.Count; ++i)
            {
                CalculateSharedEdgeSplineVertices(t.sharedEdges[i], t.regionID);
            }
        }

        void CalculateSharedEdgeSplineVertices(SharedEdgeWithNeighbourTerritroy sharedEdge, int regionID)
        {
            List<Vector3> vertices = new List<Vector3>();
            for (int i = 0; i < sharedEdge.controlPoints.Count - 1; ++i)
            {
                var s = sharedEdge.controlPoints[i].position;
                var e = sharedEdge.controlPoints[i + 1].position;
                var evaluatedPoints = FindPoints(s, e);
                foreach (var p in evaluatedPoints)
                {
                    if (!vertices.Contains(p.pos))
                    {
                        vertices.Add(p.pos);
                    }
                }
            }
            sharedEdge.evaluatedVertices = vertices;

            //EditorUtils.CreateDrawLineStrip($"shared edge {regionID}", vertices, mInput.settings.vertexDisplayRadius);
        }

        void TriangulateTerritory(Territory territory, string folder, int lod, bool generateAssets)
        {
            float yOffset = 1.0f;
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            //计算一个territory所有shared edge的顶点
            CalculateTerritoryAllSharedEdgeSplineVertices(territory);
            double time = w.Stop();
            Debug.Log($"CalculateTerritoryAllSharedEdgeSplineVertices elapsed time: {time} seconds");

            //生成inner outline和edge mesh
            territory.innerOutline = CreateInnerOutlineMesh(territory, territory.outline, folder, lod, generateAssets);
#if false
            var innerOutlineObj = new GameObject($"new Inner Outline");
            var dp = innerOutlineObj.AddComponent<DrawPolygon>();
            dp.radius = mInput.settings.vertexDisplayRadius;
            dp.SetVertices(territory.innerOutline);
            innerOutlineObj.transform.SetParent(mRoot.transform, true);
            territory.SetGameObject(Territory.ObjectType.InnerOutline, innerOutlineObj);
#endif

            w.Start();
            if (!string.IsNullOrEmpty(folder))
            {
                //内部区域mesh
                //先保证innerOutline没有self intersection
                //var validInnerOutline = RemoveSelfIntersection(territory.innerOutline);
                //var dpd = EditorUtils.CreateDrawPolygon("remove intersection", validInnerOutline, 1);

                Triangulator.TriangulatePolygon(territory.outline, out Vector3[] meshVertices, out int[] meshIndices);
                var mesh = new Mesh();
                mesh.SetVertices(meshVertices);
                if (mInput.settings.useVertexColorForRegionMesh)
                {
                    mesh.SetColors(CreateVertexColor(territory.color, meshVertices.Length));
                }
                mesh.SetIndices(meshIndices, MeshTopology.Triangles, 0);
                var obj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                obj.transform.position = new Vector3(0, yOffset, 0);
                obj.name = $"territory mesh {territory.regionID}";
                obj.transform.SetParent(mRoot.transform, true);
                var filter = obj.GetComponent<MeshFilter>();
                filter.sharedMesh = mesh;
                obj.transform.SetParent(mRoot.transform, true);
                var renderer = obj.GetComponent<MeshRenderer>();
                var mtl = mInput.settings.regionMaterial;
                if (mtl == null)
                {
                    mtl = new Material(Shader.Find("Unlit/Color"));
                }
                else
                {
                    mtl = Object.Instantiate(mtl);
                }
                mtl.color = territory.color;
                renderer.sharedMaterial = mtl;
                territory.SetGameObject(Territory.ObjectType.Region, obj);

                if (generateAssets && mInput.settings.combineMesh == false)
                {
                    string path = $"{folder}/region_mesh_{territory.regionID}_lod{lod}.asset";
                    var localMesh = CreateLocalMesh(mesh, territory.regionID);
                    AssetDatabase.CreateAsset(localMesh, path);
                    string name = $"region_{territory.regionID}_lod{lod}";
                    var regionObj = new GameObject(name);
                    regionObj.transform.position = new Vector3(0, 0, 0);
                    var objRenderer = regionObj.AddComponent<MeshRenderer>();
                    objRenderer.sharedMaterial = mInput.settings.regionMaterial;
                    var regionFilter = regionObj.AddComponent<MeshFilter>();
                    regionFilter.sharedMesh = localMesh;
                    territory.prefabPath = $"{folder}/{name}.prefab";
                    PrefabUtility.SaveAsPrefabAssetAndConnect(regionObj, territory.prefabPath, InteractionMode.AutomatedAction);
                    Utils.DestroyObject(regionObj);
                }
            }

            time = w.Stop();
            Debug.Log($"create region mesh elapsed time: {time} seconds");
        }

        List<Color> CreateVertexColor(Color color, int n)
        {
            List<Color> colors = new List<Color>(n);
            for (int i = 0; i < n; ++i)
            {
                colors.Add(color);
            }
            return colors;
        }

        List<Vector3> CreateInnerOutlineMesh(Territory territory, List<Vector3> outline, string folder, int lod, bool generateAssets)
        {
            StopWatchWrapper w = new StopWatchWrapper();

            w.Start();
            float yOffset = 1.0f;
            List<Vector3> newInnerOutline = new List<Vector3>();

            bool isLoop = true;
            List<Vector3> meshVertices = new List<Vector3>();
            List<int> meshIndices = new List<int>();

            Vector3 lastPerp = Vector3.zero;
            if (isLoop)
            {
                var dir = outline[0] - outline[outline.Count - 1];
                dir.Normalize();
                lastPerp = new Vector3(-dir.z, 0, dir.x);
            }
            int nPoints = outline.Count;
            for (int s = 0; s < nPoints; ++s)
            {
                Vector3 cur, next;
                Vector3 dir;
                if (!isLoop && s == nPoints - 1)
                {
                    lastPerp = Vector3.zero;
                    cur = outline[s];
                    next = outline[s - 1];
                    dir = cur - next;
                }
                else
                {
                    cur = outline[s];
                    next = outline[(s + 1) % nPoints];
                    dir = next - cur;
                }
                dir.Normalize();
                var curPerp = new Vector3(-dir.z, 0, dir.x);
                var perp = curPerp + lastPerp;
                perp.Normalize();
                float width = mInput.settings.lineWidth;
                float cosTheta = Vector3.Dot(perp, curPerp);
                float realWidth = width / cosTheta;
                lastPerp = curPerp;
                Vector3 v0 = cur;
                Vector3 v1 = cur + perp * realWidth;
                newInnerOutline.Add(v1);
                if (mInput.settings.mergeEdge)
                {
                    v0.y = mInput.settings.edgeHeight;
                    v1.y = mInput.settings.edgeHeight;
                }
                meshVertices.Add(v0);
                meshVertices.Add(v1);
            }
            
            int segmentCount = nPoints - 1;
            if (isLoop)
            {
                segmentCount += 1;
                //for uv, we have to make duplicated vertices
                meshVertices.Add(meshVertices[0]);
                meshVertices.Add(meshVertices[1]);
            }
            for (int i = 0; i < segmentCount; ++i)
            {
                int offset = i * 2;
                meshIndices.Add((0 + offset) % meshVertices.Count);
                meshIndices.Add((1 + offset) % meshVertices.Count);
                meshIndices.Add((2 + offset) % meshVertices.Count);
                meshIndices.Add((1 + offset) % meshVertices.Count);
                meshIndices.Add((3 + offset) % meshVertices.Count);
                meshIndices.Add((2 + offset) % meshVertices.Count);
            }
            double time = w.Stop();
            Debug.Log($"create inner outline elapsed time: {time} seconds");

            w.Start();
            
            if (territory.sharedEdges.Count > 1)
            {
                //根据shared edge生成断开的edge mesh
                for (int i = 0; i < territory.sharedEdges.Count; ++i)
                {
                    var sharedEdge = territory.sharedEdges[i];

                    int startVertexIndex = territory.outline.IndexOf(sharedEdge.evaluatedVertices[0]);
                    Debug.Assert(startVertexIndex >= 0);
                    int vertexCount = sharedEdge.evaluatedVertices.Count;
                    //calculate edge vertex
                    var edgeVertices = CalculateEdgeVertices(startVertexIndex, vertexCount, meshVertices, outline.Count);
                    //calculate edge indices
                    List<int> edgeIndices = new List<int>();
                    for (int s = 0; s < vertexCount - 1; ++s)
                    {
                        int offset = s * 2;
                        edgeIndices.Add((0 + offset) % edgeVertices.Count);
                        edgeIndices.Add((1 + offset) % edgeVertices.Count);
                        edgeIndices.Add((2 + offset) % edgeVertices.Count);
                        edgeIndices.Add((1 + offset) % edgeVertices.Count);
                        edgeIndices.Add((3 + offset) % edgeVertices.Count);
                        edgeIndices.Add((2 + offset) % edgeVertices.Count);
                    }

                    string name = $"edge_{sharedEdge.selfRegionID}_{sharedEdge.neighbourRegionID}_lod{lod}";

                    var edgeMesh = new Mesh();
                    edgeMesh.SetVertices(edgeVertices);
                    var uvs = CreateSplineMeshUVInRange(startVertexIndex, vertexCount, outline);
                    edgeMesh.SetUVs(0, uvs);
                    edgeMesh.SetIndices(edgeIndices, MeshTopology.Triangles, 0);
                    var edgeObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                    edgeObj.transform.position = new Vector3(0, yOffset, 0);
                    edgeObj.name = name;
                    var edgeFilter = edgeObj.GetComponent<MeshFilter>();
                    edgeFilter.sharedMesh = edgeMesh;
                    var edgeRenderer = edgeObj.GetComponent<MeshRenderer>();
                    edgeRenderer.sharedMaterial = mInput.settings.edgeMaterial;
                    edgeObj.transform.SetParent(mRoot.transform, true);
                    sharedEdge.gameObject = edgeObj;

                    
                    sharedEdge.prefabPath = $"{folder}/{name}.prefab";
                    sharedEdge.meshPath = $"{folder}/edge_mesh_{sharedEdge.selfRegionID}_{sharedEdge.neighbourRegionID}_lod{lod}.asset";
                    if (!string.IsNullOrEmpty(folder) && generateAssets && mInput.settings.combineMesh == false && (mInput.settings.mergeEdge == false || !mInput.settings.shareEdge))
                    {
                        //create edge mesh
                        var localMesh = CreateLocalMesh(edgeMesh, territory.regionID);
                        AssetDatabase.CreateAsset(localMesh, sharedEdge.meshPath);

                        var obj = new GameObject(name);
                        obj.transform.position = new Vector3(0, 0, 0);
                        var renderer = obj.AddComponent<MeshRenderer>();
                        var filter = obj.AddComponent<MeshFilter>();
                        filter.sharedMesh = localMesh;
                        renderer.sharedMaterial = mInput.settings.edgeMaterial;
                        PrefabUtility.SaveAsPrefabAssetAndConnect(obj, sharedEdge.prefabPath, InteractionMode.AutomatedAction);
                        Utils.DestroyObject(obj);   
                    }
                    EdgeAssetInfo edgeInfo = new EdgeAssetInfo(sharedEdge.selfRegionID, sharedEdge.neighbourRegionID, sharedEdge.prefabPath, mInput.settings.edgeMaterial);
                    mEdgeAssetsInfo.Add(edgeInfo);
                }
            }
            else
            {
                string name = $"edge_{territory.regionID}_0_lod{lod}";

                var edgeMesh = new Mesh();
                edgeMesh.SetVertices(meshVertices);
                var uvs = CreateSplineMeshUV(meshVertices, outline);
                edgeMesh.SetUVs(0, uvs);
                edgeMesh.SetIndices(meshIndices, MeshTopology.Triangles, 0);
                var edgeObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                edgeObj.transform.position = new Vector3(0, yOffset, 0);
                edgeObj.name = name;
                var edgeFilter = edgeObj.GetComponent<MeshFilter>();
                edgeFilter.sharedMesh = edgeMesh;
                var edgeRenderer = edgeObj.GetComponent<MeshRenderer>();
                edgeRenderer.sharedMaterial = mInput.settings.edgeMaterial;
                edgeObj.transform.SetParent(mRoot.transform, true);
                var sharedEdge = territory.sharedEdges[0];
                sharedEdge.gameObject = edgeObj;

                sharedEdge.prefabPath = $"{folder}/{name}.prefab";
                sharedEdge.meshPath = $"{folder}/edge_mesh_{territory.regionID}_0_lod{lod}.asset";
                if (!string.IsNullOrEmpty(folder) && 
                    generateAssets && 
                    mInput.settings.combineMesh == false &&
                    (mInput.settings.mergeEdge == false || !mInput.settings.shareEdge))
                {
                    //create mesh
                    var localMesh = CreateLocalMesh(edgeMesh, territory.regionID);
                    AssetDatabase.CreateAsset(localMesh, sharedEdge.meshPath);
                    
                    var obj = new GameObject(name);
                    obj.transform.position = new Vector3(0, 0, 0);
                    var renderer = obj.AddComponent<MeshRenderer>();
                    renderer.sharedMaterial = mInput.settings.edgeMaterial;
                    var filter = obj.AddComponent<MeshFilter>();
                    filter.sharedMesh = localMesh;
                    PrefabUtility.SaveAsPrefabAssetAndConnect(obj, sharedEdge.prefabPath, InteractionMode.AutomatedAction);
                    Utils.DestroyObject(obj);
                }

                EdgeAssetInfo edgeInfo = new EdgeAssetInfo(territory.regionID, 0, sharedEdge.prefabPath, mInput.settings.edgeMaterial);
                mEdgeAssetsInfo.Add(edgeInfo);
            }

            time = w.Stop();
            Debug.Log($"create edge mesh elapsed time: {time} seconds");

            return newInnerOutline;
        }

        List<Vector2> CreateSplineMeshUV(List<Vector3> vertices, List<Vector3> outline)
        {
            List<Vector2> uv = new List<Vector2>();
            float len = 0;
            int virtualControlPointCount = vertices.Count / 2;
            float ratio = mInput.settings.textureAspectRatio;
            bool isLoop = true;
            for (int i = 0; i < virtualControlPointCount; ++i)
            {
                float r = (len / mInput.settings.lineWidth) / ratio;
                if (isLoop && i == virtualControlPointCount - 1)
                {
                    //将最后一段设置成整数,保证能与第一段相接,但是最后一段的贴图可能会扭曲
                    r = Mathf.CeilToInt(r);
                }

                uv.Add(new Vector2(r, 0));
                uv.Add(new Vector2(r, 1));

                if (i != virtualControlPointCount - 1)
                {
                    var d = outline[(i + 1) % outline.Count] - outline[i];
                    len += d.magnitude;
                }
            }
            return uv;
        }

        List<Vector3> CalculateEdgeVertices(int startVertexIndex, int length, List<Vector3> meshVertices, int outlinePointCount)
        {
            List<Vector3> vertices = new List<Vector3>();
            for (int i = 0; i < length; ++i)
            {
                int pointIdx = Mod(startVertexIndex + i, outlinePointCount);
                vertices.Add(meshVertices[pointIdx * 2]);
                vertices.Add(meshVertices[pointIdx * 2 + 1]);
            }
            return vertices;
        }

        List<Vector2> CreateSplineMeshUVInRange(int startIndex, int length, List<Vector3> outline)
        {
            List<Vector2> uv = new List<Vector2>();
            float len = 0;
            float ratio = mInput.settings.textureAspectRatio;
            for (int i = 0; i < length; ++i)
            {
                float r = (len / mInput.settings.lineWidth) / ratio;
                if (i == length - 1)
                {
                    //截断成正数，保证贴图完整
                    r = Mathf.Ceil(r);
                }
                uv.Add(new Vector2(r, 0));
                uv.Add(new Vector2(r, 1));

                int idx = i + startIndex;
                var d = outline[(idx + 1) % outline.Count] - outline[idx % outline.Count];
                len += d.magnitude;
            }
            return uv;
        }

        Mesh CreateLocalMesh(Mesh mesh, int territoryID)
        {
            var offset = mInput.getTerritoryCenterFunc(territoryID);
            Mesh localMesh = Mesh.Instantiate(mesh);
            var vertices = localMesh.vertices;
            for (int i = 0; i < vertices.Length; ++i)
            {
                vertices[i] -= offset;
            }
            localMesh.vertices = vertices;
            localMesh.RecalculateBounds();
            return localMesh;
        }
    }
}


#endif