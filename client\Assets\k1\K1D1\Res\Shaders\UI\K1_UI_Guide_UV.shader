﻿//新手引导的 线UV滚动
Shader "K1/UI/Guide_UV"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		_ScrollX("X Speed", Float) = 1.0
		_ScrollY("Y Speed", Float) = 0.0
	}
	
	SubShader
	{
		Tags{ "RenderType" = "Transparent"  "Queue" = "Transparent"}
		Blend SrcAlpha OneMinusSrcAlpha // 混合的参数
		ZWrite Off
		LOD 100


		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_fog

			#include "UnityCG.cginc"
			
			float _ScrollX;
			float _ScrollY;

			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
			};

			struct v2f
			{
				float2 uv : TEXCOORD0;
				UNITY_FOG_COORDS(1)
				float4 vertex : SV_POSITION;
			};

			sampler2D _MainTex;
			float4 _MainTex_ST;

			v2f vert(appdata v)
			{
				v2f o;
				o.vertex = UnityObjectToClipPos(v.vertex);
				o.uv = TRANSFORM_TEX(v.uv, _MainTex) + frac(float2(_ScrollX * _Time.y, _ScrollY * _Time.y));


				// UNITY_TRANSFER_FOG(o,o.vertex);
				return o;
			}

			fixed4 frag(v2f i) : SV_Target
			{
				fixed4 col = tex2D(_MainTex, i.uv);

				// UNITY_APPLY_FOG(i.fogCoord, col);
				return col;
			}
			ENDCG
		}
	}
}