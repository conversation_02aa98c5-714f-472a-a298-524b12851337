﻿ 



 
 


using System.Collections.Generic;
using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    // PathAlgorithm 寻路算法
    public interface PathAlgorithm
    {
        // 入参：网格，矩阵，开始顶点编号和目标顶点编号，返回到终点的顶点逆序列表
        int[] FindPath(Mesh mesh, Matrix mat, int curID, int dstID, TriangleTypeSetting[] typeSetting);
    };

    public delegate uint Heuristic(Mesh mesh, Matrix matrix, int curID, int dstID);

    public static class PathFunc
    {
        // EdgeHeuristicFunc 边类型节点，启发函数
        public static uint EdgeHeuristicFunc(Mesh mesh, Matrix martrix, int curID, int dstID)
        {
            var dstCoord = mesh.edges[curID].WtCoord;
            var edgeCoord = mesh.edges[dstID].WtCoord;
            return (uint)GeoUtils.CalDstCoordToCoord(edgeCoord, dstCoord);
        }
    }

    // AStar A*算法
    public class AStar : PathAlgorithm
    {
        protected Heuristic hFunc;
        protected uint[] dist;
        //保存前一个路径
        protected int[] prev;
        //是否已访问过
        protected bool[] visited;
        protected List<int> cleanIndexs = new List<int>();
        protected NodeHeap nodeheap;

        // NewAStar 新建
        public AStar(int nCnt)
        {
            hFunc = PathFunc.EdgeHeuristicFunc;

            dist = new uint[nCnt];
            prev = new int[nCnt];
            visited = new bool[nCnt];
            nodeheap = new NodeHeap(visited.Length);
            cleanIndexs = new List<int>();
            for (int i = 0; i < nCnt; i++)
            {
                dist[i] = Const.LargerNumber;
            }
            // 前置节点
            for (int i = 0; i < nCnt; i++)
            {
                prev[i] = Const.StubIndex;
            }
            // 已经访问过的集合
            for (int i = 0; i < nCnt; i++)
            {
                visited[i] = false;
            }
        }

        protected void Clean()
        {
            foreach (var clean in cleanIndexs)
            {
                dist[clean] = Const.LargerNumber;
                prev[clean] = Const.StubIndex;
                visited[clean] = false;
            }
            cleanIndexs.Clear();
        }

        // FindPath 寻路
        // A*算法
        // 参考链接：https://en.wikipedia.org/wiki/A*_search_algorithm
        public virtual int[] FindPath(Mesh mesh, Matrix matrix, int srcID, int dstID, TriangleTypeSetting[] typeSetting)
        {
            Clean();
            var nodes = matrix.nodes;
            // 出发点加入堆中
            dist[srcID] = 0;

            cleanIndexs.Add(srcID);

            nodeheap.DecreaseKey(srcID, 0, -1);
            while (nodeheap.Len() > 0)
            {
                // 按照优先级弹出节点
                var u = heap.HeapUtils.Pop(nodeheap);
                if (u.ID == dstID)
                {
                    break;
                }
                if (visited[u.ID])
                {
                    continue;
                }
                // 到达当前节点的最短距离
                var distU = dist[u.ID];
                // 标记已经访问过
                visited[u.ID] = true;

                cleanIndexs.Add(u.ID);

                var node = nodes[u.ID];
                // 查看u的所有邻接节点
                foreach (var v in node)
                {
                    if (visited[v.id])
                    {
                        continue;

                    }
                    var alt = distU + v.weight;

                    if (alt < dist[v.id])
                    {
                        //距离更近,更新
                        dist[v.id] = alt;
                        prev[v.id] = u.ID;
                        cleanIndexs.Add(v.id);
                        var priority = alt + hFunc(mesh, matrix, v.id, dstID);
                        nodeheap.DecreaseKey(v.id, priority, -1);
                    }
                }
            }

            nodeheap.Reset();
            return prev;
        }
    }

    // VisibleAStar 可视化A*算法
    public class VisibleAStar : AStar
    {
        public VisibleAStar(int nodeCnt) : base(nodeCnt)
        {
        }

        // FindPath 可见性优化启发函数的A*算法
        public override int[] FindPath(Mesh mesh, Matrix matrix, int srcIdx, int dstIdx, TriangleTypeSetting[] typeSetting)
        {
            Clean();
            // 出发点加入堆中
            dist[srcIdx] = 0;
            cleanIndexs.Add(srcIdx);
            nodeheap.DecreaseKey(srcIdx, 0, -1);
            bool found = false;
            // 获取目标点坐标
            var dstCoord = mesh.edges[dstIdx].WtCoord;
            while (nodeheap.Len() > 0)
            {
                // 按照优先级弹出节点
                var u = heap.HeapUtils.Pop(nodeheap);
                var edge = mesh.edges[u.ID];
                if (typeSetting != null)
                {
                    bool walkable = edge.IsWalkable(typeSetting, mesh.triangles);
                    if (!walkable)
                    {
                        continue;
                    }
                }

                // 如果当前节点为目标节点，则终止查找
                if (u.ID == dstIdx)
                {
                    found = true;
                    break;
                }
                if (visited[u.ID])
                {
                    continue;
                }
                // 到达当前节点的最短距离
                var distU = dist[u.ID];
                // 标记已经访问
                visited[u.ID] = true;

                cleanIndexs.Add(u.ID);
                // 更新权重
                var curEdgeKey = u.ID;
                var curCoord = mesh.edges[curEdgeKey].WtCoord;
                var nodes = matrix.nodes[u.ID];
                // 查看u的所有邻接节点
                foreach (var v in nodes)
                {
                    if (visited[v.id])
                    {
                        continue;
                    }
                    var visEdgeKey = v.id;
                    var q1 = mesh.edges[visEdgeKey].Vertice0.Coord;
                    var q2 = mesh.edges[visEdgeKey].Vertice1.Coord;
                    // 判断是否和下一条边相交
                    // 是则求出交点作为下一条边的权重点，否则选取距离终点最近的顶点作为权重点
                    bool exist;
                    var coord = GeoUtils.GetCrossCoord(curCoord, dstCoord, q1, q2, out exist);

                    if (exist)
                    {
                        mesh.edges[visEdgeKey].WtCoord = coord;
                    }
                    else
                    {
                        // 下条边的权重点为顶点到终点距离最短的点
                        if (GeoUtils.Smaller(GeoUtils.CalDstCoordToCoord(q1, dstCoord), GeoUtils.CalDstCoordToCoord(q2, dstCoord)))
                        {
                            mesh.edges[visEdgeKey].WtCoord = q1;
                        }
                        else
                        {
                            mesh.edges[visEdgeKey].WtCoord = q2;
                        }
                    }
                    var edgeCoord = mesh.edges[visEdgeKey].WtCoord;
                    // 计算权重
                    uint weight = (uint)GeoUtils.CalDstCoordToCoord(curCoord, edgeCoord);

                    //var alt = distU + v.weight;
                    var alt = distU + weight;
                    if (alt < dist[v.id])
                    {
                        dist[v.id] = alt;
                        prev[v.id] = u.ID;
                        cleanIndexs.Add(v.id);
                        var priority = alt + hFunc(mesh, matrix, v.id, dstIdx);
                        nodeheap.DecreaseKey(v.id, priority, -1);
                    }
                }
            }

            nodeheap.Reset();

            if (!found)
            {
                return null;
            }
            return prev;
        }
    }
}
