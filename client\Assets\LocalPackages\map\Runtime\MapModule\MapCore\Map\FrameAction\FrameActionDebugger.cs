﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class FrameActionDebugger : MonoBehaviour
    {
        public event System.Action RefreshEvent;

#if false
        void OnGUI()
        {
            if (Map.currentMap != null)
            {
                var frameActionManager = Map.currentMap.frameActionManager;
                var queue = frameActionManager.FindActionQueue(MapCoreDef.MAP_FRONT_TILE_QUEUE);
                if (queue != null)
                {
                    string flagText =
 string.Format("queue: {0}, flag: {1}, paused: {2}", queue.debugName, queue.flag.ToString(), queue.paused);
                    GUILayout.BeginVertical();
                    GUILayout.Label(flagText);
                    GUILayout.EndVertical();
                }
            }
        }
#endif

        public void Refresh()
        {
            if (RefreshEvent != null)
            {
                RefreshEvent();
            }
        }

        public bool[] queueFoldState;
    }
}