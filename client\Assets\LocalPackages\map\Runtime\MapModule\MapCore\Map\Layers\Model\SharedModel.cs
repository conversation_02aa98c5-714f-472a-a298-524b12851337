﻿ 



 
 



using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //使用对象池并且多个data可以共享同一个模型
    public class SharedModel : ModelBase
    {
        public void Init(Map map, string prefabPath, Vector3 position, Vector3 scale, Quaternion rotation, System.Action<SharedModel> OnRefCountBeZero)
        {
            mMap = map;
            mPrefabPath = prefabPath;
            //从对象池中拿出一个模型
            mGameObject = mMap.view.reusableGameObjectPool.Require(mPrefabPath, position, scale, rotation);
            mOnRefCountBeZero = OnRefCountBeZero;
            mRefCount = 0;

            Utils.HideGameObject(mGameObject);
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
            if (mGameObject != null)
            {
                mMap.view.reusableGameObjectPool.Release(mPrefabPath, mGameObject, mMap);
            }
        }

        public override void Release()
        {
            DecRef();
        }

        public static SharedModel Require(Map map, string prefabPath, Vector3 position, Vector3 scale, Quaternion rotation, System.Action<SharedModel> onRefCountBeZero)
        {
            var model = mPool.Require();
            model.Init(map, prefabPath, position, scale, rotation, onRefCountBeZero);
            return model;
        }

        public void IncRef()
        {
            ++mRefCount;
        }

        public void DecRef()
        {
            --mRefCount;
            if (mRefCount == 0)
            {
                if (mOnRefCountBeZero != null)
                {
                    mOnRefCountBeZero(this);
                }
                ReleaseToPool(this);
            }
            else if (mRefCount < 0)
            {
                Debug.Assert(false, "can't be here!");
            }
        }

        static void ReleaseToPool(SharedModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        public string prefabPath { get { return mPrefabPath; } }

        Map mMap;
        string mPrefabPath;
        int mRefCount = 0;
        System.Action<SharedModel> mOnRefCountBeZero;
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<SharedModel> mPool = new ObjectPool<SharedModel>(20, () => new SharedModel());
    }
}
