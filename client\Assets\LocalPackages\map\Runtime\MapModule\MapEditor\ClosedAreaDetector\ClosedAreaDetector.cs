﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class AreaIndicator
    {
        public Vector3 position;
        public int type;
    }

    //检测封闭区域的三角形
    public static class ClosedAreaDetector
    {
        public class Area
        {
            public int[] meshIndices;
            public Vector3[] meshVertices;
            public int type;
        }

        class Triangle
        {
            public bool visited = false;
            public int index;
            public int v0;
            public int v1;
            public int v2;
            public int[] neighbourTriangles = new int[3];
        }

        class Edge
        {
            public int triangle0 = -1;
            public int triangle1 = -1;
        }

        static void DrawTriangleIndices(Vector3[] meshVertices, int[] meshIndices)
        {
            int t = meshIndices.Length / 3;
            for (int i = 0; i < t; ++i)
            {
                var v0 = meshIndices[i * 3];
                var v1 = meshIndices[i * 3 + 1];
                var v2 = meshIndices[i * 3 + 2];

                var p0 = meshVertices[v0];
                var p1 = meshVertices[v1];
                var p2 = meshVertices[v2];
                var center = (p0 + p1 + p2) / 3;

                var text = Utils.CreateTextGameObject(i.ToString(), i.ToString(), Color.red);
                text.transform.position = center;
                text.transform.localScale = Vector3.one * 20;
            }
        }

        //indicator必须丢在不同的区域里
        public static List<Area> Process(List<AreaIndicator> indicators, Vector3[] meshVertices, int[] meshIndices)
        {
            Clear();

#if false
            //draw triangle indices
            DrawTriangleIndices(meshVertices, meshIndices);
#endif

            List<Area> areas = new List<Area>();

            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            BuildTriangles(meshIndices);
            var time = w.Stop();
            Debug.Log($"BuildTriangles cost {time} s");

            for (int i = 0; i < indicators.Count; ++i)
            {
                int triangleIndex = FindTriangleIndex(Utils.ToVector2(indicators[i].position), meshVertices);
                if (triangleIndex < 0)
                {
                    Debug.LogError("invalid triangle");
                }
                else
                {
                    var triangleList = CreateAreaByFloodFill(triangleIndex);
                    int triangleCount = triangleList.Count;
                    int[] indices = new int[triangleCount * 3];
                    List<Vector3> vertices = new List<Vector3>();
                    Dictionary<Vector3, int> posIndex = new Dictionary<Vector3, int>();

                    for (int t = 0; t < triangleCount; ++t)
                    {
                        int v0 = triangleList[t].v0;
                        int v1 = triangleList[t].v1;
                        int v2 = triangleList[t].v2;

                        var p0 = meshVertices[v0];
                        var p1 = meshVertices[v1];
                        var p2 = meshVertices[v2];

                        v0 = AddVertex(p0, vertices, posIndex);
                        v1 = AddVertex(p1, vertices, posIndex);
                        v2 = AddVertex(p2, vertices, posIndex);
                        indices[t * 3] = v0;
                        indices[t * 3 + 1] = v1;
                        indices[t * 3 + 2] = v2;
                    }

                    var area = new Area();
                    area.type = indicators[i].type;
                    area.meshIndices = indices;
                    area.meshVertices = vertices.ToArray();

#if false
                    BigMeshViewer viewer = new BigMeshViewer();
                    viewer.Create(null, $"closed area {i}", vertices.ToArray(), indices, false, Color.yellow);
#endif

                    areas.Add(area);
                }
            }

            return areas;
        }

        static int AddVertex(Vector3 v, List<Vector3> vertices, Dictionary<Vector3, int> posIndex)
        {
            bool found = posIndex.TryGetValue(v, out int index);
            if (found)
            {
                return index;
            }

            vertices.Add(v);
            index = vertices.Count - 1;
            posIndex[v] = index;
            return index;
        }

        static void BuildTriangles(int[] meshIndices)
        {
            int nTriangles = meshIndices.Length / 3;

            //build edges
            for (int t = 0; t < nTriangles; ++t)
            {
                int v0 = meshIndices[t * 3];
                int v1 = meshIndices[t * 3 + 1];
                int v2 = meshIndices[t * 3 + 2];

                CheckEdge(v0, v1, t);
                CheckEdge(v1, v2, t);
                CheckEdge(v2, v0, t);
            }

            //build triangles
            for (int t = 0; t < nTriangles; ++t)
            {
                int v0 = meshIndices[t * 3];
                int v1 = meshIndices[t * 3 + 1];
                int v2 = meshIndices[t * 3 + 2];

                Edge edge0 = FindEdgeSwap(v0, v1);
                Edge edge1 = FindEdgeSwap(v1, v2);
                Edge edge2 = FindEdgeSwap(v2, v0);
                Triangle triangle = new Triangle();
                mTriangles.Add(triangle);
                triangle.v0 = v0;
                triangle.v1 = v1;
                triangle.v2 = v2;
                triangle.index = t;
                if (edge0.triangle0 == t)
                {
                    triangle.neighbourTriangles[0] = edge0.triangle1;
                }
                else
                {
                    triangle.neighbourTriangles[0] = edge0.triangle0;
                }
                if (edge1.triangle0 == t)
                {
                    triangle.neighbourTriangles[1] = edge1.triangle1;
                }
                else
                {
                    triangle.neighbourTriangles[1] = edge1.triangle0;
                }
                if (edge2.triangle0 == t)
                {
                    triangle.neighbourTriangles[2] = edge2.triangle1;
                }
                else
                {
                    triangle.neighbourTriangles[2] = edge2.triangle0;
                }
            }
        }

        static void CheckEdge(int v0, int v1, int triangleIndex)
        {
            Edge edge = FindEdgeSwap(v0, v1);

            if (edge == null)
            {
                AddEdge(v0, v1, triangleIndex);
            }
            else
            {
                Debug.Assert(edge.triangle0 >= 0);
                Debug.Assert(edge.triangle1 == -1);
                edge.triangle1 = triangleIndex;
            }
        }

        static Edge FindEdgeSwap(int v0, int v1)
        {
            long key = Utils.MakeInt64Key(v0, v1);
            Edge edge;
            mEdges.TryGetValue(key, out edge);
            if (edge == null)
            {
                key = Utils.MakeInt64Key(v1, v0);
                mEdges.TryGetValue(key, out edge);
            }
            return edge;
        }

        static void AddEdge(int v0, int v1, int triangleIndex)
        {
            var edge = new Edge();
            edge.triangle0 = triangleIndex;
            var key = Utils.MakeInt64Key(v0, v1);
            mEdges.Add(key, edge);
        }

        static int FindTriangleIndex(Vector2 pos, Vector3[] meshVertices)
        {
            int nTriangles = mTriangles.Count;
            for (int t = 0; t < nTriangles; ++t)
            {
                int v0 = mTriangles[t].v0;
                int v1 = mTriangles[t].v1;
                int v2 = mTriangles[t].v2;
                if (Utils.PointInTriangle(pos, Utils.ToVector2(meshVertices[v0]), Utils.ToVector2(meshVertices[v1]), Utils.ToVector2(meshVertices[v2])))
                {
                    return t;
                }
            }

            return -1;
        }

        static List<Triangle> CreateAreaByFloodFill(int startTriangleIndex)
        {
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            List<Triangle> result = new List<Triangle>();

            List<Triangle> pendingTriangles = new List<Triangle>();
            pendingTriangles.Add(mTriangles[startTriangleIndex]);
            mTriangles[startTriangleIndex].visited = true;

            while (pendingTriangles.Count > 0)
            {
                int idx = pendingTriangles.Count - 1;
                var curTriangle = pendingTriangles[idx];
                pendingTriangles.RemoveAt(idx);

                result.Add(curTriangle);

                int nNeighbours = curTriangle.neighbourTriangles.Length;
                for (int i = 0; i < nNeighbours; ++i)
                {
                    if (curTriangle.neighbourTriangles[i] >= 0)
                    {
                        var neighbourTriangle = mTriangles[curTriangle.neighbourTriangles[i]];
                        if (neighbourTriangle.visited == false)
                        {
                            neighbourTriangle.visited = true;
#if false
                            //temp code
                            for (int k = 0; k < pendingTriangles.Count; ++k)
                            {
                                if (pendingTriangles[k].v0 == neighbourTriangle.v0 &&
                                    pendingTriangles[k].v1 == neighbourTriangle.v1 &&
                                    pendingTriangles[k].v2 == neighbourTriangle.v2)
                                {
                                    int a = 1;
                                }
                            }
#endif
                            pendingTriangles.Add(neighbourTriangle);
                        }
                    }
                }
            }

            var time = w.Stop();
            Debug.Log($"create flood fill cost {time} s");

            return result;
        }

        static void Clear()
        {
            mEdges.Clear();
            mTriangles.Clear();
        }

        static Dictionary<long, Edge> mEdges = new Dictionary<long, Edge>();
        static List<Triangle> mTriangles = new List<Triangle>();
    }
}

#endif