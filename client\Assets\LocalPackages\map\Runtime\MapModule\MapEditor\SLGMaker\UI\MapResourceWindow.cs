﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;

namespace TFW.Map
{
    public class MapResourceWindow : EditorWindow
    {
        enum DisplayModelTemplateType
        {
            //编辑器使用的model template,但不包含子model template
            All,
            //运行时使用的model template,但不包含子model template
            Exported,
            //运行时使用的model template,包含子model template
            Detailed,
        }

        class ReferenceInfo
        {
            public ReferenceInfo(int n, string name)
            {
                layerName = name;
                usedCount = n;
            }

            public int usedCount;
            public string layerName;
        }

        void OnGUI()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                if (GUILayout.Button("Validate Prefabs"))
                {
                    CheckValidation();
                }
                mType = (DisplayModelTemplateType)EditorGUILayout.EnumPopup("Type", mType);

                if (mType == DisplayModelTemplateType.All)
                {
                    var modelTemplates = map.data.modelTemplateManager.modelTemplates;
                    ShowModelTemplate(modelTemplates);
                }
                else if (mType == DisplayModelTemplateType.Exported)
                {
                    Dictionary<int, ModelTemplate> usedModelTemplateList = new Dictionary<int, ModelTemplate>();
                    var usedTemplates = map.data.GetUsedModelTemplates();
                    for (int i = 0; i < usedTemplates.Count; ++i)
                    {
                        usedModelTemplateList[usedTemplates[i].id] = usedTemplates[i];
                    }
                    ShowModelTemplate(usedModelTemplateList);
                }
                else if (mType == DisplayModelTemplateType.Detailed)
                {
                    var usedTemplates = CollectAllModelTemplates();
                    ShowModelTemplate(usedTemplates);
                }
            }
        }

        void ShowModelTemplate(Dictionary<int, ModelTemplate> modelTemplates)
        {
            EditorGUILayout.IntField("Count", modelTemplates.Count);
            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);
            foreach (var p in modelTemplates)
            {
                var temp = p.Value;
                EditorGUILayout.BeginHorizontal();
                string path = temp.GetLODPrefabPath(0);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab != null)
                {
                    temp.preload = GUILayout.Toggle(temp.preload, "Preload", GUILayout.Width(60));

                    EditorGUILayout.IntField(temp.id, GUILayout.Width(40));
                    if (GUILayout.Button("!", GUILayout.Width(20)))
                    {
                        ShowReferenceInfo(temp.id);
                    }
                    EditorGUILayout.ObjectField("Prefab", prefab, typeof(GameObject), false, null);
                }
                else
                {
                    EditorGUILayout.LabelField("Invalid Prefab", path);
                }
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndScrollView();
        }

        Dictionary<int, ModelTemplate> CollectAllModelTemplates()
        {
            Dictionary<int, ModelTemplate> usedAllModelTemplateList = new Dictionary<int, ModelTemplate>();
            var usedTemplates = Map.currentMap.data.GetUsedModelTemplates();
            for (int i = 0; i < usedTemplates.Count; ++i)
            {
                int nLODs = usedTemplates[i].lodCount;
                if (nLODs > 0)
                {
                    for (int k = 0; k < nLODs; ++k)
                    {
                        var childModelTemplates = usedTemplates[i].GetChildrenModelTemplates(k);
                        foreach (var temp in childModelTemplates)
                        {
                            if (!usedAllModelTemplateList.ContainsKey(temp.id))
                            {
                                usedAllModelTemplateList.Add(temp.id, temp);
                            }
                        }
                    }
                }
                else
                {
                    if (!usedAllModelTemplateList.ContainsKey(usedTemplates[i].id))
                    {
                        usedAllModelTemplateList.Add(usedTemplates[i].id, usedTemplates[i]);
                    }
                }
            }

            return usedAllModelTemplateList;
        }

        void ShowReferenceInfo(int modelTemplateID)
        {
            mReferencedIDs.Clear();
            mReferencedInfo.Clear();
            var map = Map.currentMap;
            map.data.modelTemplateManager.FindReference(modelTemplateID, mReferencedIDs);
            int n = map.GetMapLayerCount();
            for (int k = 0; k < n; ++k)
            {
                var layer = map.GetMapLayerByIndex(k);
                for (int v = 0; v < mReferencedIDs.Count; ++v)
                {
                    if (layer.Contains(mReferencedIDs[v]))
                    {
                        AddReferenceInfo(layer.name);
                    }
                }
            }

            string referenceInfo = BuildingReferenceDescription();
            EditorUtility.DisplayDialog("Reference Info", referenceInfo, "OK");
        }

        void AddReferenceInfo(string layerName)
        {
            bool found = false;
            for (int i = 0; i < mReferencedInfo.Count; ++i)
            {
                if (mReferencedInfo[i].layerName == layerName)
                {
                    ++mReferencedInfo[i].usedCount;
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                mReferencedInfo.Add(new ReferenceInfo(1, layerName));
            }
        }

        string BuildingReferenceDescription()
        {
            if (mReferencedInfo.Count == 0)
            {
                return "No Reference!";
            }

            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < mReferencedInfo.Count; ++i)
            {
                builder.AppendFormat("Layer {0}, Count {1}", mReferencedInfo[i].layerName, mReferencedInfo[i].usedCount);
            }
            return builder.ToString();
        }

        void CheckValidation()
        {
            var usedTemplates = Map.currentMap.data.GetUsedModelTemplates();
            for (int i = 0; i < usedTemplates.Count; ++i)
            {
                if (usedTemplates[i].isTileModelTemplate)
                {
                    var prefabPath = usedTemplates[i].GetLODPrefabPath(0);
                    int nLods = usedTemplates[i].lodCount;
                    for (int k = 0; k < nLods; ++k)
                    {
                        string lodPrefabPath = usedTemplates[i].GetLODPrefabPath(k);
                        var prefab = MapModuleResourceMgr.LoadPrefab(lodPrefabPath);
                        if (prefab != null)
                        {
                            if (prefab.transform.position != Vector3.zero ||
                                prefab.transform.rotation != Quaternion.identity ||
                                prefab.transform.localScale != Vector3.one)
                            {
                                Debug.Log($"invalid prefab {lodPrefabPath} transform is not identity!");
                            }
                        }

                        var childPrefabs = usedTemplates[i].GetChildPrefabTransform(k);
                        foreach (var p in childPrefabs)
                        {
                            bool found = FindTemplateWithLOD0(usedTemplates, p.path);
                            if (found)
                            {
                                Debug.Log($"invalid prefab {p.path} in {lodPrefabPath}");
                            }
                        }
                    }
                }
            }
        }

        bool FindTemplateWithLOD0(List<ModelTemplate> usedTemplates, string path)
        {
            for (int i = 0; i < usedTemplates.Count; ++i)
            {
                if (usedTemplates[i].isTileModelTemplate)
                {
                    int nLods = usedTemplates[i].lodCount;
                    for (int k = 0; k < nLods; ++k)
                    {
                        if (usedTemplates[i].GetLODPrefabPath(k) == path)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        Vector2 mScrollPos;
        DisplayModelTemplateType mType = DisplayModelTemplateType.All;
        List<int> mReferencedIDs = new List<int>();
        List<ReferenceInfo> mReferencedInfo = new List<ReferenceInfo>();
    }
}


#endif