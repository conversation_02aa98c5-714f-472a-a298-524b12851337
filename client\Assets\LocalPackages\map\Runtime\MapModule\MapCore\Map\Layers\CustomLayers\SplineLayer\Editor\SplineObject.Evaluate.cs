﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace TFW.Map
{
    public partial class SplineObject
    {
        void Evaluate()
        {
            mEvaluatedPoints.Clear();
            for (int i = 0; i < mControlPoints.Count - 1; ++i)
            {
                EvaluateSegment(mControlPoints[i], mControlPoints[i + 1], i + 1);
            }

            if (isLoop && mControlPoints.Count > 2)
            {
                EvaluateSegment(mControlPoints[mControlPoints.Count - 1], mControlPoints[0], 0);
                //remove last point which is same as first point
                mEvaluatedPoints.RemoveAt(mEvaluatedPoints.Count - 1);
            }
        }

        void EvaluateSegment(ControlPoint start, ControlPoint end, int controlPointIndex)
        {
            float lengthSoFar = 0;
            List<Vector3> pointsInSegment = new List<Vector3>();
            for (int i = 0; i < end.pointCountInSegment; ++i)
            {
                float t = (float)i / (end.pointCountInSegment - 1);
                var pos = SplineUtils.Bezier(start.pos, start.tangents[1], end.tangents[0], end.pos, t);
                pointsInSegment.Add(pos);
                if (pointsInSegment.Count >= 2)
                {
                    lengthSoFar += (pos - pointsInSegment[pointsInSegment.Count - 2]).magnitude;
                }

                if (mEvaluatedPoints.Count == 0 || mEvaluatedPoints[mEvaluatedPoints.Count - 1].pos != pos)
                {
                    //不添加重复的point
                    bool isBreakPoint = (controlPointIndex != mControlPoints.Count - 1 && i == end.pointCountInSegment - 1);
                    mEvaluatedPoints.Add(new EvaluatePoint(pos, controlPointIndex, lengthSoFar, isBreakPoint));
                }
            }

            end.curveLength = lengthSoFar;
        }

        public void CreateSplineMesh()
        {
            if (mRiverData.isRiverObject)
            {
                CreateSplineComplexMesh();
            }
            else
            {
                Evaluate();

                int nPoints = mEvaluatedPoints.Count;
                if (nPoints > 1)
                {
                    if (mMesh == null)
                    {
                        mMesh = new Mesh();
                    }
                    mMesh.Clear();

                    List<Vector3> meshVertices = new List<Vector3>();
                    List<int> meshIndices = new List<int>();

                    Vector3 lastPerp = Vector3.zero;
                    if (isLoop)
                    {
                        var dir = mEvaluatedPoints[0].pos - mEvaluatedPoints[nPoints - 1].pos;
                        dir.Normalize();
                        lastPerp = new Vector3(-dir.z, 0, dir.x);
                    }
                    for (int s = 0; s < nPoints; ++s)
                    {
                        Vector3 cur, next;
                        Vector3 dir;
                        if (!isLoop && s == nPoints - 1)
                        {
                            lastPerp = Vector3.zero;
                            cur = mEvaluatedPoints[s].pos;
                            next = mEvaluatedPoints[s - 1].pos;
                            dir = cur - next;
                        }
                        else
                        {
                            cur = mEvaluatedPoints[s].pos;
                            next = mEvaluatedPoints[(s + 1) % nPoints].pos;
                            dir = next - cur;
                        }
                        dir.Normalize();
                        var curPerp = new Vector3(-dir.z, 0, dir.x);
                        var perp = curPerp + lastPerp;
                        perp.Normalize();
                        lastPerp = curPerp;
                        Vector3 v0 = cur;

                        int curControlPointIndex = mEvaluatedPoints[s].controlPointIndex;
                        int lastControlPointIndex = Mathf.Max(curControlPointIndex - 1, 1);
                        float curveLength = mControlPoints[curControlPointIndex].curveLength;
                        float lengthSoFar = mEvaluatedPoints[s].lengthFromStartControlPoint;
                        float p = lengthSoFar / curveLength;
                        Debug.Assert(p <= 1.0f);
                        float lastControlPointWidth = mControlPoints[lastControlPointIndex].width;
                        float curControlPointWidth = mControlPoints[curControlPointIndex].width;

                        float width = Mathf.Lerp(lastControlPointWidth, curControlPointWidth, p);
                        Vector3 v1 = cur + perp * width;
                        meshVertices.Add(v0);
                        meshVertices.Add(v1);
                    }

                    int segmentCount = nPoints - 1;
                    if (isLoop)
                    {
                        segmentCount += 1;
                        //for uv, we have do make duplicated vertices
                        meshVertices.Add(meshVertices[0]);
                        meshVertices.Add(meshVertices[1]);
                    }
                    for (int i = 0; i < segmentCount; ++i)
                    {
                        int offset = i * 2;
                        meshIndices.Add((0 + offset) % meshVertices.Count);
                        meshIndices.Add((1 + offset) % meshVertices.Count);
                        meshIndices.Add((2 + offset) % meshVertices.Count);
                        meshIndices.Add((1 + offset) % meshVertices.Count);
                        meshIndices.Add((3 + offset) % meshVertices.Count);
                        meshIndices.Add((2 + offset) % meshVertices.Count);
                    }

                    mMeshVertices = meshVertices;
                    mMeshIndices = meshIndices;
                    var uvs = CreateSplineMeshUV(mMeshVertices);
                    var colors = CreateMeshVertexColor(mMeshVertices);

                    Vector2[] uv2s = null;
#if false
                InjectVertexForTiling(mMeshVertices, mMeshIndices, uvs, colors, out uv2s);
#endif

                    mMesh.vertices = mMeshVertices.ToArray();
                    mMesh.triangles = mMeshIndices.ToArray();
                    mMesh.uv = uvs.ToArray();
                    mMesh.colors = colors.ToArray();
                    if (uv2s != null)
                    {
                        mMesh.uv2 = uv2s;
                    }

                    CreateMeshGameObject();
                }
            }
        }

        public void CreateSplineComplexMesh()
        {
            Debug.Assert(mRiverData.stripeCount >= 2, $"stripe count: {mRiverData.stripeCount}");

            Evaluate();

            int nPoints = mEvaluatedPoints.Count;
            if (nPoints > 1)
            {
                if (mMesh == null)
                {
                    mMesh = new Mesh();
                }
                mMesh.Clear();

                List<Vector3> meshVertices = new List<Vector3>();
                List<int> meshIndices = new List<int>();

                List<Vector3> innerOutline = new List<Vector3>();
                Vector3 lastPerp = Vector3.zero;
                if (isLoop)
                {
                    var dir = mEvaluatedPoints[0].pos - mEvaluatedPoints[nPoints - 1].pos;
                    dir.Normalize();
                    lastPerp = new Vector3(-dir.z, 0, dir.x);
                }
                for (int s = 0; s < nPoints; ++s)
                {
                    Vector3 cur, next;
                    Vector3 dir;
                    if (!isLoop && s == nPoints - 1)
                    {
                        lastPerp = Vector3.zero;
                        cur = mEvaluatedPoints[s].pos;
                        next = mEvaluatedPoints[s - 1].pos;
                        dir = cur - next;
                    }
                    else
                    {
                        cur = mEvaluatedPoints[s].pos;
                        next = mEvaluatedPoints[(s + 1) % nPoints].pos;
                        dir = next - cur;
                    }
                    dir.Normalize();
                    var curPerp = new Vector3(-dir.z, 0, dir.x);
                    var perp = curPerp + lastPerp;
                    perp.Normalize();
                    lastPerp = curPerp;
                    Vector3 v0 = cur;

#if false
                    int curControlPointIndex = mEvaluatedPoints[s].controlPointIndex;
                    int lastControlPointIndex = Mathf.Max(curControlPointIndex - 1, 1);
                    float curveLength = mControlPoints[curControlPointIndex].curveLength;
                    float lengthSoFar = mEvaluatedPoints[s].lengthFromStartControlPoint;
                    float p = lengthSoFar / curveLength;
                    Debug.Assert(p <= 1.0f);
                    float lastControlPointWidth = mControlPoints[lastControlPointIndex].width;
                    float curControlPointWidth = mControlPoints[curControlPointIndex].width;
                    float width = Mathf.Lerp(lastControlPointWidth, curControlPointWidth, p);
#endif
                    float width = mRiverData.stripeWidth[0];
                    
                    Vector3 v1 = cur + perp * width;
                    meshVertices.Add(v0);
                    meshVertices.Add(v1);

                    innerOutline.Add(v1);
                }

                int segmentCount = nPoints - 1;
                if (isLoop)
                {
                    segmentCount += 1;
                    //for uv, we have do make duplicated vertices
                    meshVertices.Add(meshVertices[0]);
                    meshVertices.Add(meshVertices[1]);
                }
                for (int i = 0; i < segmentCount; ++i)
                {
                    int offset = i * 2;
                    meshIndices.Add((0 + offset) % meshVertices.Count);
                    meshIndices.Add((1 + offset) % meshVertices.Count);
                    meshIndices.Add((2 + offset) % meshVertices.Count);
                    meshIndices.Add((1 + offset) % meshVertices.Count);
                    meshIndices.Add((3 + offset) % meshVertices.Count);
                    meshIndices.Add((2 + offset) % meshVertices.Count);
                }

                //for (int i = 0; i < mStripeGameObjects.Count; ++i)
                //{
                //    if (mStripeGameObjects[i] == null)
                //    {
                //        mStripeGameObjects[i] = new GameObject();
                //        mStripeGameObjects[i].AddComponent<DrawPolygon>();
                //    }
                //}

                List<List<Vector3>> allStripeVertices = new List<List<Vector3>>();
                allStripeVertices.Add(innerOutline);
                OptimizedMeshCombiner combiner = new OptimizedMeshCombiner(0.01f);
                combiner.AddMesh(meshVertices.ToArray(), meshIndices.ToArray());
                for (int i = 1; i < mRiverData.stripeCount; ++i)
                {
                    CreateStripeVertices(innerOutline, mRiverData.stripeWidth[i], out var stripeVertices);
                    stripeVertices = Utils.RemoveSelfIntersection(stripeVertices, out _);

                    allStripeVertices.Add(stripeVertices);

                    CreateStripeMeshVertices(innerOutline, stripeVertices, out var vertices, out var indices);

                    combiner.AddMesh(vertices, indices);

                    //var dp = mStripGameObjects[i].GetComponent<DrawPolygon>();
                    //dp.SetVertices(stripeVertices);
                    //dp.radius = r;
                    innerOutline = stripeVertices;
                }

                //bottom mesh
                Triangulator.TriangulatePolygon(allStripeVertices[allStripeVertices.Count - 1], out var bottomMeshVertices, out var bottomMeshIndices);
                combiner.AddMesh(bottomMeshVertices, bottomMeshIndices);

                combiner.Combine(false, out var combinedVertices, out var combinedIndices);
                for (int i = 0; i < combinedVertices.Length; ++i)
                {
                    for (int j = 0; j < allStripeVertices.Count; ++j)
                    {
                        if (Utils.Contains(combinedVertices[i], allStripeVertices[j], 0.01f))
                        {
                            combinedVertices[i] = new Vector3(combinedVertices[i].x, mRiverData.stripeDepth[j], combinedVertices[i].z);
                        }
                    }
                    //Debug.Assert(contains);
                }
                
                mMeshVertices = new List<Vector3>();
                mMeshIndices = new List<int>();

                mMeshVertices.AddRange(combinedVertices);
                mMeshIndices.AddRange(combinedIndices);

                var bounds = Utils.CreateBounds(mMeshVertices);
                List<Vector2> uvs = new List<Vector2>();
                float minX = bounds.min.x;
                float minZ = bounds.min.z;
                float boundsWidth = bounds.size.x;
                float boundsHeight = bounds.size.z;
                for (int i = 0; i < mMeshVertices.Count; ++i)
                {
                    float u = (mMeshVertices[i].x - minX) / boundsWidth;
                    float v = (mMeshVertices[i].z - minZ) / boundsHeight;
                    uvs.Add(new Vector2(u, v));
                }

                mMesh.vertices = mMeshVertices.ToArray();
                mMesh.uv = uvs.ToArray();
                mMesh.triangles = mMeshIndices.ToArray();
                mMesh.RecalculateBounds();

                CreateMeshGameObject();

                //stencil mask
                Triangulator.TriangulatePolygon(allStripeVertices[0], out var maskVertices, out var maskIndices);
                for (var i = 0; i < maskVertices.Length; ++i)
                {
                    maskVertices[i] = new Vector3(maskVertices[i].x, mRiverData.stripeDepth[0], maskVertices[i].z);
                }
                var mesh = new Mesh();
                mesh.vertices = maskVertices;
                mesh.triangles = maskIndices;
                mesh.RecalculateBounds();
                CreateStencilMaskObject(mesh);

                //water
                var center = bounds.center;
                CreateWaterMesh(center, allStripeVertices[1], out var waterVertices, out var waterIndices, out var uv);
                var waterMesh = new Mesh();
                for (var i = 0; i < waterVertices.Length; ++i)
                {
                    waterVertices[i] = new Vector3(waterVertices[i].x, mRiverData.stripeDepth[1],  waterVertices[i].z);
                }
                waterMesh.vertices = waterVertices;
                waterMesh.uv = uv;
                waterMesh.triangles = waterIndices;
                waterMesh.RecalculateBounds();
                CreateRiverObject(waterMesh);
            }
        }

        void CreateWaterMesh(Vector3 center, List<Vector3> waterOutline, out Vector3[] waterVertices, out int[] waterIndices, out Vector2[] uv)
        {
            if (!Utils.IsPolygonCW(waterOutline))
            {
                Utils.ReverseList(waterOutline);
            }

            List<int> indices = new List<int>();
            waterVertices = new Vector3[waterOutline.Count + 1];
            uv = new Vector2[waterVertices.Length];
            uv[0] = Vector2.zero;
            waterVertices[0] = center;
            float distance = 0;
            for (int i = 1; i <= waterOutline.Count; ++i)
            {
                if (i > 1)
                {
                    distance = Vector2.Distance(waterOutline[i - 1], waterOutline[i - 2]);
                }
                waterVertices[i] = waterOutline[i - 1];
                uv[i] = new Vector2(distance, 1);
            }
            int triangleCount = waterOutline.Count - 1;
            for (int i = 1; i <= triangleCount; ++i)
            {
                indices.Add(0);
                indices.Add(i);
                indices.Add(i + 1);
            }
            waterIndices = indices.ToArray();
        }

        void CreateStripeMeshVertices(List<Vector3> outer, List<Vector3> inner, out Vector3[] vertices, out int[] indices)
        {
            PolygonAlgorithm.GetDifferencePolygon(outer, inner, out var noneHoles, out var holes);
            Triangulator.TriangulatePolygons(noneHoles, holes, null, out vertices, out indices);
        }

        void CreateStripeVertices(List<Vector3> outline, float fixedWidth, out List<Vector3> stripeVertices)
        {
            stripeVertices = new List<Vector3>();
            int nPoints = outline.Count;
            if (nPoints > 1)
            {
                Vector3 lastPerp = Vector3.zero;
                if (isLoop)
                {
                    var dir = outline[0] - outline[nPoints - 1];
                    dir.Normalize();
                    lastPerp = new Vector3(-dir.z, 0, dir.x);
                }
                for (int s = 0; s < nPoints; ++s)
                {
                    Vector3 cur, next;
                    Vector3 dir;
                    if (!isLoop && s == nPoints - 1)
                    {
                        lastPerp = Vector3.zero;
                        cur = outline[s];
                        next = outline[s - 1];
                        dir = cur - next;
                    }
                    else
                    {
                        cur = outline[s];
                        next = outline[(s + 1) % nPoints];
                        dir = next - cur;
                    }
                    dir.Normalize();
                    var curPerp = new Vector3(-dir.z, 0, dir.x);
                    var perp = curPerp + lastPerp;
                    perp.Normalize();
                    lastPerp = curPerp;
                    Vector3 v1 = cur + perp * fixedWidth;
                    stripeVertices.Add(v1);
                }

                if (isLoop)
                {
                    //for uv, we have do make duplicated vertices
                    stripeVertices.Add(stripeVertices[0]);
                }
            }
        }

        List<Vector2> CreateSplineMeshUV(List<Vector3> vertices)
        {
            List<Vector2> uv = new List<Vector2>();
            float len = 0;
            int virtualControlPointCount = vertices.Count / 2;
            float ratio = GetRatio();
            float width = GetMaxWidth();
            for (int i = 0; i < virtualControlPointCount; ++i)
            {
                float r = (len / width) / ratio;
                if (isLoop && i == virtualControlPointCount - 1)
                {
                    //将最后一段设置成整数,保证能与第一段相接,但是最后一段的贴图可能会扭曲
                    r = Mathf.CeilToInt(r);
                }

                //int tilingIndex = mControlPoints[mEvaluatedPoints[i].controlPointIndex].tileIndex;
                //temp code
                //int tileCount = 4;
                //float startV = tilingIndex * (1.0f / tileCount);
                //float endV = startV + (1.0f / tileCount);
                uv.Add(new Vector2(r, 0));
                uv.Add(new Vector2(r, 1));
                //uv2[i * 2] = new Vector2(r, startV);
                //uv2[i * 2 + 1] = new Vector2(r, endV);

                if (i != virtualControlPointCount - 1)
                {
                    var d = mEvaluatedPoints[(i + 1) % mEvaluatedPoints.Count].pos - mEvaluatedPoints[i].pos;
                    len += d.magnitude;
                }
            }
            //mMesh.uv = uv;
            //mMesh.uv2 = uv2;
            return uv;
        }

        bool IsBreakPoint(Vector3 pos)
        {
            for (int i = 0; i < mEvaluatedPoints.Count; ++i)
            {
                if (mEvaluatedPoints[i].pos == pos)
                {
                    if (mEvaluatedPoints[i].isBreakPoint)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        void InjectVertexForTiling(List<Vector3> vertices, List<int> indices, List<Vector2> uvs, List<Color> colors, out Vector2[] uv2s)
        {
            uv2s = null;
            if (isLoop)
            {
                uv2s = uvs.ToArray();
                return;
            }

            int startIndex = 0;
            int insertedPointCount = 0;
            while (true)
            {
                bool foundBreakPoint = false;
                for (int i = startIndex; i < vertices.Count; ++i)
                {
                    if (IsBreakPoint(vertices[i]))
                    {
                        int offset = i % 2 == 0 ? 1 : -1;

                        foundBreakPoint = true;
                        Vector3 clonedVertex0Pos = vertices[i];
                        Vector3 clonedVertex1Pos = vertices[i + offset];

                        AddIndicesFromSegment((i - insertedPointCount) / 2, 2, indices);

                        Vector2 clonedVertex0UV = uvs[i];
                        Vector2 clonedVertex1UV = uvs[i + offset];

                        Color clonedVertexColor0 = colors[i];
                        Color clonedVertexColor1 = colors[i + offset];
                        clonedVertexColor0.a = 0;
                        clonedVertexColor1.a = 0;
                        colors[i] = new Color(colors[i].r, colors[i].g, colors[i].b, 0);
                        colors[i + offset] = new Color(colors[i + offset].r, colors[i + offset].g, colors[i + offset].b, 0);

                        vertices.Insert(i + offset + 1, clonedVertex0Pos);
                        vertices.Insert(i + offset + 2, clonedVertex1Pos);
                        uvs.Insert(i + offset + 1, clonedVertex0UV);
                        uvs.Insert(i + offset + 2, clonedVertex1UV);
                        colors.Insert(i + offset + 1, clonedVertexColor0);
                        colors.Insert(i + offset + 2, clonedVertexColor1);

                        if (offset == 1)
                        {
                            startIndex = i + 3;
                        }
                        else
                        {
                            startIndex = i + 4;
                        }

                        insertedPointCount += 2;

                        break;
                    }
                }

                if (!foundBreakPoint)
                {
                    //处理uv2
                    int startVertexIndex = 0;
                    if (insertedPointCount != 0)
                    {
                        uv2s = new Vector2[uvs.Count];

                        for (int k = 0; k < mEvaluatedPoints.Count; ++k)
                        {
                            if (mEvaluatedPoints[k].isBreakPoint || k == mEvaluatedPoints.Count - 1)
                            {
                                int endVertexIndex = k * 6;
                                var controlPoint = mControlPoints[mEvaluatedPoints[k].controlPointIndex];
                                int tilingIndex = controlPoint.tileIndex;
                                float startV = tilingIndex * (1.0f / mTileCount);
                                float endV = startV + (1.0f / mTileCount);
                                for (int v = startVertexIndex; v < endVertexIndex; ++v)
                                {
                                    var idx = indices[v];
                                    uv2s[idx] = uvs[idx];
                                    if (idx % 2 == 0)
                                    {
                                        uv2s[idx].y = startV;
                                    }
                                    else
                                    {
                                        uv2s[idx].y = endV;
                                    }
                                }
                                startVertexIndex = endVertexIndex;
                            }
                        }
                    }
                    return;
                }
            }
        }

        void AddIndicesFromSegment(int segmentIndex, int addedNumber, List<int> indices)
        {
            int idx = segmentIndex * 6;
            for (int i = idx; i < indices.Count; ++i)
            {
                indices[i] += addedNumber;
            }
        }
    }
}

#endif