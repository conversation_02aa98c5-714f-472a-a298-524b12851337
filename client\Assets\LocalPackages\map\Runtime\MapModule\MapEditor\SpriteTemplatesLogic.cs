﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [ExecuteInEditMode]
    [Black]
    public class SpriteTemplatesLogic : MonoBehaviour {
        private void Start() {
            mInstance = this;
        }

        public static SpriteTemplatesLogic instance { get { return mInstance; } }

        static SpriteTemplatesLogic mInstance;
    }
}

#endif