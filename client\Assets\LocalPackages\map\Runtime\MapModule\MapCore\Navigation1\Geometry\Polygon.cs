﻿ 



 
 


using System.Collections.Generic;

namespace TFW.Map.Geo
{

    // 多边形接口
    public interface Polygon
    {
        bool IsCoordInside(Coord p);
        Vector[] GetVectors();
        void ToRect(out int minX, out int minZ, out int maxX, out int maxZ);
        int GetIndex();
        int[] GetEdgeIDs();
        Coord[] GetEdgeMidCoords();
        List<Vertice> GetVertices();
        ushort GetCustomType();
    }
}