﻿ 



 
 



#if UNITY_EDITOR
/*
 * created by wzw at 2019.3.5
 */


using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //创建navmesh
    [Black]
    public static partial class NavMeshCreator
    {
        //walkableArea: 是否是创建可行走的区域,如果是false,则创建所有obstacle的并集
        //triangleTypes: 每个三角形的类型
        //triangleState: 是否可通过
        public static void CreateNavMesh(PrefabOutlineType type, Vector3 min, Vector3 max, List<IObstacle> obstacles, float agentRadius, float minimumAngle, float maximumArea, bool useDelaunay, NavigationCreateMode createMode, bool oceanAreaEnabled, float scaleFactor, bool removeSameHoles, out Vector3[] meshVertices, out int[] meshIndices, out ushort[] triangleTypes, out bool[] triangleStates)
        {
            meshVertices = null;
            meshIndices = null;
            triangleTypes = null;
            //默认都可通过
            triangleStates = null;

            min *= scaleFactor;
            max *= scaleFactor;

            if (createMode == NavigationCreateMode.CreateLandNavigationMesh)
            {
                CreateLandNavigationMesh(type, min, max, obstacles, agentRadius, minimumAngle, maximumArea, useDelaunay, scaleFactor, removeSameHoles, out meshVertices, out meshIndices);
            }
            else if (createMode == NavigationCreateMode.CreateIslandAndOceanNavigationMesh)
            {
                CreateIslandAndOceanNavigationMesh(type, min, max, agentRadius, oceanAreaEnabled, removeSameHoles, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
            else if (createMode == NavigationCreateMode.CreateIslandNavigationMesh)
            {
                CreateIslandNavigationMesh(type, min, max, obstacles, agentRadius, minimumAngle, maximumArea, scaleFactor, useDelaunay, oceanAreaEnabled, removeSameHoles, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
            else if (createMode == NavigationCreateMode.CreateIslandClosedAreaNavigationMesh)
            {
                CreateIslandGateNavMesh(PrefabOutlineType.NavMeshObstacle, min, max, agentRadius, minimumAngle, maximumArea, oceanAreaEnabled, useDelaunay, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
            else if (createMode == NavigationCreateMode.CreateLandObstacles)
            {
                CreateLandObstacles(type, min, max, obstacles, agentRadius, minimumAngle, maximumArea, useDelaunay, scaleFactor, out meshVertices, out meshIndices);
            }
            else if (createMode == NavigationCreateMode.CreateIslandAndOceanObstacles)
            {
                CreateIslandAndOceanObstacles(type, min, max, obstacles, agentRadius, minimumAngle, maximumArea, useDelaunay, oceanAreaEnabled, scaleFactor, removeSameHoles, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
            else if (createMode == NavigationCreateMode.CreateClosedAreaAndGateNavMesh)
            {
                CreateClosedAreaAndGateNavMesh(type, min, max, agentRadius, minimumAngle, maximumArea, oceanAreaEnabled, removeSameHoles, out meshVertices, out meshIndices, out triangleTypes, out triangleStates);
            }
            else
            {
                Debug.Assert(false, $"unknown mode {createMode.ToString()}");
            }

#if false
            //temp code, show walkable polygons
            for (int i = 0; i < noneHoles.Count; ++i)
            {
                var obj = new GameObject("walkablePolygons " + i.ToString());
                var dp = obj.AddComponent<DrawPolygon>();
                dp.SetVertices(noneHoles[i]);
            }
#endif
        }

        //ignoreZeroIDRegion:是否忽略area type为0的special region
        static void CreateSpecialRegionNavMesh(PrefabOutlineType type, float agentRadius, bool ignoreZeroIDRegion, out Vector3[] vertices, out int[] indices, out List<ushort> triangleTypes, out List<bool> triangleStates)
        {
            var collisionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            //获取特殊区域
            List<MapCollisionData> specialRegions = new List<MapCollisionData>();
            if (collisionLayer != null)
            {
                collisionLayer.GetCollisionsOfType(specialRegions, CollisionAttribute.SpecialRegion);
            }
            if (ignoreZeroIDRegion)
            {
                //去掉area type为0的special regions
                for (int i = specialRegions.Count - 1; i >= 0; --i)
                {
                    if (specialRegions[i].type == 0)
                    {
                        specialRegions.RemoveAt(i);
                    }
                }
            }

            //id高的被id低的当成障碍物处理
            SortedList<int, List<MapCollisionData>> sortedRegionTypes = new SortedList<int, List<MapCollisionData>>();
            for (int i = 0; i < specialRegions.Count; ++i)
            {
                List<MapCollisionData> collisionList;
                sortedRegionTypes.TryGetValue(specialRegions[i].type, out collisionList);
                if (collisionList == null)
                {
                    collisionList = new List<MapCollisionData>();
                    sortedRegionTypes[specialRegions[i].type] = collisionList;
                }
                collisionList.Add(specialRegions[i]);
            }

            Dictionary<Vector3, int> posIndex = new Dictionary<Vector3, int>();
            List<Vector3> combinedSpecialRegionVertices = new List<Vector3>();
            List<int> combinedSpecialRegionIndices = new List<int>();
            List<ushort> combinedTriangleTypes = new List<ushort>();
            List<bool> combinedTriangleStates = new List<bool>();
            //合并special regions
            foreach (var p in sortedRegionTypes)
            {
                List<List<Vector3>> combinedPolygonHoles;
                var regionsOfType = p.Value;
                var combinedPolygons = PolygonAlgorithm.GetCombinedObstaclePolygons(type, Utils.ToObstacleList(regionsOfType), 0, regionsOfType.Count - 1, 0, 1, out combinedPolygonHoles);
                var obstacles = Utils.CreateObstacles(LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kCollisionLayer | LayerTypeMask.kGridModelLayer, false, type, CheckMapCollisionOperation.kGenerateMapObstacles, false, false, true);
                //add higher region type as obstacles
                foreach (var p1 in sortedRegionTypes)
                {
                    if (p1.Key > p.Key)
                    {
                        var specialRegionCollisions = p1.Value;
                        obstacles.AddRange(specialRegionCollisions);
                    }
                }
                List<List<Vector3>> obstacleHoles;
                var obstaclePolygons = PolygonAlgorithm.GetCombinedObstaclePolygons(type, obstacles, 0, obstacles.Count - 1, agentRadius, 1, out obstacleHoles);

                List<List<Vector3>> resultPolygons;
                List<List<Vector3>> holes;
                PolygonAlgorithm.GetDifferencePolygons(combinedPolygons, obstaclePolygons, out resultPolygons, out holes);

                Vector3[] verticesForThisRegionType;
                int[] indicesForThisRegionType;
                Triangulator.TriangulatePolygons(resultPolygons, holes, false, 0, 0, null, out verticesForThisRegionType, out indicesForThisRegionType);

                if (indicesForThisRegionType != null)
                {
                    int nTriangles = indicesForThisRegionType.Length / 3;
                    for (int t = 0; t < nTriangles; ++t)
                    {
                        combinedTriangleTypes.Add((ushort)p.Key);
                        //注意, 相同type的区域必须三角形的enable/disable状态相同!所以这里只取第一个区域的状态代表所有同类型三角形的状态
                        combinedTriangleStates.Add(!regionsOfType[0].HasAttribute(CollisionAttribute.Disabled));
                    }

                    //合并到上一次生成的导航网格中
                    for (int i = 0; i < indicesForThisRegionType.Length; ++i)
                    {
                        var v = verticesForThisRegionType[indicesForThisRegionType[i]];
                        //int idx = Utils.FindVertex(combinedSpecialRegionVertices, v, 0.01f);
                        bool found = posIndex.TryGetValue(v, out int idx);
                        if (!found)
                        {
                            idx = combinedSpecialRegionVertices.Count;
                            posIndex.Add(v, idx);
                            combinedSpecialRegionVertices.Add(v);
                        }
                        combinedSpecialRegionIndices.Add(idx);
                    }
                }
            }

            vertices = combinedSpecialRegionVertices.ToArray();
            indices = combinedSpecialRegionIndices.ToArray();
            triangleTypes = combinedTriangleTypes;
            triangleStates = combinedTriangleStates;
        }

        static void CreateInvalidSpaceObstacleMesh(Vector3 min, Vector3 max, out Vector3[] invalidSpaceMeshVertices, out int[] invalidSpaceMeshIndices)
        {
            float mapWidth = Map.currentMap.mapWidth;
            float mapHeight = Map.currentMap.mapHeight;

            List<Vector3> mapDataGenerationRange = new List<Vector3>()
            {
                new Vector3(min.x, 0, min.z),
                new Vector3(max.x, 0, min.z),
                new Vector3(max.x, 0, max.z),
                new Vector3(min.x, 0, max.z),
            };

            List<Vector3> mapRange = new List<Vector3>()
            {
                new Vector3(0, 0, 0),
                new Vector3(mapWidth, 0, 0),
                new Vector3(mapWidth, 0, mapHeight),
                new Vector3(0, 0, mapHeight),
            };

            List<List<Vector3>> noneHoles;
            List<List<Vector3>> holes;
            PolygonAlgorithm.GetDifferencePolygons(mapRange, new List<List<Vector3>>() { mapDataGenerationRange}, out noneHoles, out holes);

            Triangulator.TriangulatePolygons(noneHoles, holes, false, 0, 0, null, out invalidSpaceMeshVertices, out invalidSpaceMeshIndices);
        }

        static void CreateWaterRegionNavMesh(PrefabOutlineType type, Vector3 min, Vector3 max, float agentRadius, List<ushort> triangleTypes, List<bool> triangleStates, bool oceanAreaWalkable, bool removeSameHoles, out Vector3[] vertices, out int[] indices)
        {
            var obstacles = Utils.CreateObstacles(LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kGridModelLayer | LayerTypeMask.kCollisionLayer, false, type, CheckMapCollisionOperation.kGenerateWaterNavMesh, false, false, true);
            List<List<Vector3>> waterPolygons;
            List<List<Vector3>> obstaclesPolygons;
            PolygonAlgorithm.GetDifferencePolygons(type, min.x, min.z, max.x, max.z, obstacles, agentRadius, 1, removeSameHoles, out waterPolygons, out obstaclesPolygons);

            Triangulator.TriangulatePolygons(waterPolygons, obstaclesPolygons, false, 0, 0, null, out vertices, out indices);
            if (indices != null)
            {
                int nTriangles = indices.Length / 3;
                for (int t = 0; t < nTriangles; ++t)
                {
                    //MAP_MAX_REGION_TYPE_ID用来表示海面的三角形类型
                    triangleTypes.Add(MapCoreDef.MAP_MAX_REGION_TYPE_ID);
                    triangleStates.Add(oceanAreaWalkable);
                }
            }
        }

        public class MeshItem
        {
            public MeshItem(Vector3[] vertices, int[] indices)
            {
                this.vertices = vertices;
                this.indices = indices;
            }
            
            public Vector3[] vertices;
            public int[] indices;
        }

        public static void CombineMesh(List<MeshItem> meshies, out Vector3[] meshVertices, out int[] meshIndices)
        {
            OptimizedMeshCombiner combiner = new OptimizedMeshCombiner(0.0001f);
            for (int i = 0; i < meshies.Count; ++i)
            {
                combiner.AddMesh(meshies[i].vertices, meshies[i].indices, Matrix4x4.identity);
            }
            combiner.Combine(true, out meshVertices, out meshIndices);
        }        
    }
}

#endif