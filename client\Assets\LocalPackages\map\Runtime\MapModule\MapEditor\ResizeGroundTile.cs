﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    //修改ground tile的大小
    public static class ResizeGroundTile
    {
        public static void Resize(int newSize)
        {
            float oldSize = 0;
            
            foreach (var obj in Selection.objects)
            {
                oldSize = 0;
                var folder = obj as DefaultAsset;
                if (folder != null)
                {
                    List<Mesh> meshies = new List<Mesh>();
                    var folderPath = AssetDatabase.GetAssetPath(folder);
                    foreach (var filePath in Directory.EnumerateFiles(folderPath))
                    {
                        var unityAssetPath = Utils.ConvertToUnityAssetsPath(filePath);
                        if (oldSize == 0)
                        {
                            if (unityAssetPath.EndsWith(".prefab"))
                            {
                                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(unityAssetPath);
                                oldSize = GameObjectBoundsCalculator.CalculateBounds(prefab, false).size.x;
                            }
                        }

                        if (unityAssetPath.EndsWith(".asset"))
                        {
                            var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(unityAssetPath);
                            if (mesh != null)
                            {
                                meshies.Add(mesh);
                            }
                        }
                    }

                    //resize mesh
                    for (int i = 0; i < meshies.Count; ++i)
                    {
                        ResizeMesh(meshies[i], oldSize, newSize);
                    }
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }
        }

        static void ResizeMesh(Mesh mesh, float oldSize, float newSize)
        {
            var vertices = mesh.vertices;
            float scale = newSize / oldSize;
            for (int i = 0; i < vertices.Length; ++i)
            {
                vertices[i] *= scale;
            }
            mesh.vertices = vertices;
            mesh.RecalculateBounds();
            mesh.RecalculateNormals();
            mesh.RecalculateTangents();
            EditorUtility.SetDirty(mesh);
        }
    }
}

#endif