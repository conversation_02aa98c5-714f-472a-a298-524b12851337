﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [CustomEditor(typeof(SpecialAreaConfig))]
    public class SpecialAreaConfigUI : Editor
    {
        public override void OnInspectorGUI()
        {
            var config = target as SpecialAreaConfig;
            EditorGUI.BeginChangeCheck();

            config.id = EditorGUILayout.IntField("ID", config.id);

            config.shape = (SpecialAreaShape)EditorGUILayout.EnumPopup("Shape", config.shape);
            if (config.shape == SpecialAreaShape.Rectangle)
            {
                config.width = EditorGUILayout.FloatField("Width", config.width);
                config.height = EditorGUILayout.FloatField("Height", config.height);
            }
            else if (config.shape == SpecialAreaShape.Circle)
            {
                float newRadius = EditorGUILayout.FloatField("Radius", config.width * 0.5f);
                config.width = newRadius * 0.5f;
            }
            bool changed = EditorGUI.EndChangeCheck();
            if (changed)
            {
                EditorUtility.SetDirty(target);
            }
        }
    }
}


#endif