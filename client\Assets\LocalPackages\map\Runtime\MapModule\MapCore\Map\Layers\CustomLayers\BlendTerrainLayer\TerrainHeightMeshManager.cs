﻿ 



 
 

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class TerrainHeightMeshManager
    {
        public TerrainHeightMeshManager(Map map)
        {
            mMap = map;
        }

        public Mesh GetHeightMesh(int tileX, int tileY)
        {
            var mesh = MapModuleResourceMgr.LoadResource<Mesh>(MapCoreDef.GetTerrainMeshPath(mMap.dataFolder, tileX, tileY));
            return mesh;
        }

        public void SetOriginalMesh(string prefabPath, Mesh mesh)
        {
            mTileOriginalMesh[prefabPath] = mesh;
        }

        public Mesh GetOriginalMesh(string prefabPath)
        {
            Mesh result;
            mTileOriginalMesh.TryGetValue(prefabPath, out result);
            return result;
        }

        Dictionary<string, Mesh> mTileOriginalMesh = new Dictionary<string, Mesh>();
        Map mMap;
    }
}
