﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RuinLayer : ModelLayer, IOverlapObjectAtPosition
    {
        public void GenerateAllRandomPointsInSpecialRegion(RandomWrapper randomGenerator)
        {
            RemoveAllObjects();
            bool sameGridSize = CheckIfSameGridSize(ruinObjectTypes);

            MapRegionManager regionManager = null;
            if (sameGridSize)
            {
                regionManager = new MapRegionManager();
                var setting = ruinObjectTypes[0];
                regionManager.CreateRegions(setting.startX, setting.startZ, setting.bigGridWidth * setting.horizontalBigGridCount, setting.bigGridHeight * setting.verticalBigGridCount, setting.bigGridWidth, setting.bigGridHeight, setting.colliderRadius, PrefabOutlineType.ObjectPlacementObstacle, CheckMapCollisionOperation.kGenerateRuinPoints, false, true);
            }

            for (int i = 0; i < ruinObjectTypes.Count; ++i)
            {
                GenerateRandomPointsInSpecialRegion(ruinObjectTypes[i].name, regionManager, randomGenerator);
            }
        }

        public void GenerateRandomPointsInSpecialRegion(string objectType, MapRegionManager regionManager, RandomWrapper randomGenerator)
        {
            List<Vector3> positions = GenerateRandomPositionsInSpecialRegions(objectType, regionManager, true, randomGenerator);
            if (positions.Count > 0)
            {
                for (int i = 0; i < positions.Count; ++i)
                {
                    AddObject(objectType, positions[i], null, false, false);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "invalid parameter!", "OK");
            }
        }

        public List<Vector3> GenerateRandomPositionsInSpecialRegions(string objectType, MapRegionManager regionManager, bool hasMinDeltaDistance, RandomWrapper randomGenerator)
        {
            var setting = GetRuinSetting(objectType);
            int ruinSettingIndex = GetRuinSettingIndex(objectType);
            if (setting != null)
            {
                float mapWidth = Map.currentMap.mapWidth;
                float mapHeight = Map.currentMap.mapHeight;
                if (setting.bigGridWidth > 0 && setting.bigGridWidth > 0 && setting.startX >= 0 && setting.startX <= mapWidth && setting.startZ >= 0 && setting.startZ <= mapHeight && setting.horizontalBigGridCount > 0 && setting.verticalBigGridCount > 0 && setting.pointCount > 0)
                {
                    List<Vector2> allPoints = new List<Vector2>();

                    if (regionManager == null)
                    {
                        regionManager = new MapRegionManager();
                        regionManager.CreateRegions(setting.startX, setting.startZ, setting.bigGridWidth * setting.horizontalBigGridCount, setting.bigGridHeight * setting.verticalBigGridCount, setting.bigGridWidth, setting.bigGridHeight, setting.colliderRadius, PrefabOutlineType.ObjectPlacementObstacle, CheckMapCollisionOperation.kGenerateRuinPoints, false, true);
                    }

                    var layer = this;
                    var invalidCircles = layer.GetInvalidCircles(objectType);
                    var regions = regionManager.regions;
                    for (int r = 0; r < regions.Length; ++r)
                    {
                        var region = regions[r];

                        int x = r % setting.horizontalBigGridCount;
                        int y = r / setting.horizontalBigGridCount;
                        int distanceX = Mathf.Abs(x - setting.horizontalBigGridCount / 2);
                        int distanceY = Mathf.Abs(y - setting.verticalBigGridCount / 2);
                        int minDistance = Mathf.Max(distanceX, distanceY);
                        int cirlceIndex = setting.horizontalBigGridCount / 2 - minDistance + 1;
                        if (!invalidCircles.Contains(cirlceIndex))
                        {
                            List<Vector2> points = null;
                            if (hasMinDeltaDistance)
                            {
                                points = GeneratePointsWithDeltaDistance(region, setting, ruinSettingIndex, randomGenerator, allPoints);
                            }
                            else
                            {
                                points = GeneratePointsWithoutDeltaDistance(region, setting, randomGenerator);
                            }
                            allPoints.AddRange(points);
                        }
                    }

                    return Utils.ConvertToVector3List(allPoints);
                }
            }
            return new List<Vector3>();
        }

        //生成没最小间隔的点
        List<Vector2> GeneratePointsWithoutDeltaDistance(MapRegion region, RuinSetting setting, RandomWrapper randomGenerator)
        {
            MapRegionPointGenerator gen = new MapRegionPointGenerator(new Rect(0, 0, Map.currentMap.mapWidth, Map.currentMap.mapHeight));
            gen.Create(region.noneHolePolygons, region.holePolygons, setting.pointCount, 0, 0, randomGenerator, 0, (List<Vector2> generatedPoints, Vector3 pos) =>
            {
                return FindObjectAtExactSamePosition(pos) != null;
            }, 0, null);

            return gen.generatedPoints;
        }

        class RuinPoint
        {
            public RuinPoint(Vector3 pos, float radius)
            {
                this.pos = pos;
                this.radius = radius;
            }

            public Vector3 pos;
            public float radius;
        }

        List<RuinPoint> GetPointsOfHigherPriority(int endIndex)
        {
            List<RuinPoint> points = new List<RuinPoint>();

            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                var ruinData = objects[i] as RuinData;
                int index = GetRuinSettingIndex(ruinData.objectType);
                if (index < endIndex)
                {
                    points.Add(new RuinPoint(ruinData.GetPosition(), mRuinObjectTypes[index].colliderRadius));
                }
            }

            return points;
        }

        //生成含最小间隔的点
        List<Vector2> GeneratePointsWithDeltaDistance(MapRegion region, RuinSetting setting, int curSettingIndex, RandomWrapper randomGenerator, List<Vector2> allCurrentTypePoints)
        {
            var gen = new GeneratePointsInGridWithDeltaDistance();
            List<RuinPoint> higherPriorityPoints = GetPointsOfHigherPriority(curSettingIndex);
            return gen.Generate(region, setting.startX, setting.startZ, setting.colliderRadius, setting.pointCount, randomGenerator, 
                (Vector3 pos)=> {
                int n = higherPriorityPoints.Count;
                for (int i = 0; i < n; ++i)
                {
                    var delta = pos - higherPriorityPoints[i].pos;
                        float r2 = setting.colliderRadius + higherPriorityPoints[i].radius;
                        r2 *= r2;
                    if (delta.sqrMagnitude <= r2)
                    {
                        return false;
                    }
                }
                return true;
            }, true, allCurrentTypePoints);
        }

        bool CheckIfSameGridSize(List<RuinSetting> ruinTypes)
        {
            if (ruinTypes.Count == 0)
            {
                return false;
            }

            float width = ruinTypes[0].bigGridWidth;
            float height = ruinTypes[0].bigGridHeight;
            int horizontalBigGridCount = ruinTypes[0].horizontalBigGridCount;
            int verticalBigGridCount = ruinTypes[0].verticalBigGridCount;
            float startX = ruinTypes[0].startX;
            float startZ = ruinTypes[0].startZ;
            for (int i = 1; i < ruinTypes.Count; ++i)
            {
                if (!Mathf.Approximately(width, ruinTypes[i].bigGridWidth) ||
                    !Mathf.Approximately(height, ruinTypes[i].bigGridHeight) ||
                    !Mathf.Approximately(startX, ruinTypes[i].startX) ||
                    !Mathf.Approximately(startZ, ruinTypes[i].startZ) || 
                    horizontalBigGridCount != ruinTypes[i].horizontalBigGridCount ||
                    verticalBigGridCount != ruinTypes[i].verticalBigGridCount)
                {
                    return false;
                }
            }
            return true;
        }
    }
}


#endif