﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class PolygonRiverMover
    {
        public PolygonRiverMover(int layerID, int dataID, Vector3 pos)
        {
            Debug.Assert(mAction == null);
            mAction = new ActionMovePolygonRiver(layerID, dataID, pos);
        }

        public void Stop(Vector3 pos)
        {
            mAction.SetEndPosition(pos);
            if (mAction.moved)
            {
                ActionManager.instance.PushAction(mAction, true, false);
            }
            mAction = null;
        }

        ActionMovePolygonRiver mAction;
    }
}


#endif