﻿//#define USE_RGBA32_TEXTURE

#define TEXTURE_SKIN_APP_DATA \
			float4 uv3 : TEXCOORD2;	\
			float4 uv4 : TEXCOORD3;	\
			UNITY_VERTEX_INPUT_INSTANCE_ID

sampler2D_half _AnimationData;
float4 _AnimTextureSize;
float _BoneCount;

//获取某个像素的uv坐标
float4 GetUV(float pixelOffset, float2 textureSize) {
	float u = (fmod(pixelOffset, textureSize.x)) / textureSize.x;
	float v = (floor(pixelOffset / textureSize.x)) / textureSize.y;
	return float4(u, v, 0, 0);
}

#ifdef USE_RGBA32_TEXTURE
half4 DecodeMatrixColumn(half4 c0, half4 c1)
{
	half4 col;

	col.x = DecodeFloatRG(c0.xy);
	col.y = DecodeFloatRG(c0.zw);
	col.z = DecodeFloatRG(c1.xy);
	col.w = DecodeFloatRG(c1.zw);

	col.xyz = (col.xyz * 2 - 1) * pow(2, col.w * 255);
	return half4(col.xyz, 0);
}

//获取某帧某个骨骼的transform
half4x4 LoadBoneTransform(float animStartOffset, float frame, float boneIndex) {
	//一根骨骼transform占8个像素

	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 8.0;
	float4 uv00 = GetUV(pixelOffset, _AnimTextureSize.xy);
	float4 uv01 = GetUV(pixelOffset + 1, _AnimTextureSize.xy);
	float4 uv10 = GetUV(pixelOffset + 2, _AnimTextureSize.xy);
	float4 uv11 = GetUV(pixelOffset + 3, _AnimTextureSize.xy);
	float4 uv20 = GetUV(pixelOffset + 4, _AnimTextureSize.xy);
	float4 uv21 = GetUV(pixelOffset + 5, _AnimTextureSize.xy);
	float4 uv30 = GetUV(pixelOffset + 6, _AnimTextureSize.xy);
	float4 uv31 = GetUV(pixelOffset + 7, _AnimTextureSize.xy);

	half4 col00 = tex2Dlod(_AnimationData, uv00);
	half4 col01 = tex2Dlod(_AnimationData, uv01);
	half4 col0 = DecodeMatrixColumn(col00, col01);

	half4 col10 = tex2Dlod(_AnimationData, uv10);
	half4 col11 = tex2Dlod(_AnimationData, uv11);
	half4 col1 = DecodeMatrixColumn(col10, col11);

	half4 col20 = tex2Dlod(_AnimationData, uv20);
	half4 col21 = tex2Dlod(_AnimationData, uv21);
	half4 col2 = DecodeMatrixColumn(col20, col21);

	half4 col30 = tex2Dlod(_AnimationData, uv30);
	half4 col31 = tex2Dlod(_AnimationData, uv31);
	half4 col3 = DecodeMatrixColumn(col30, col31);
	col3.w = 1;

	half4x4 m;
	m._11_21_31_41 = col0;
	m._12_22_32_42 = col1;
	m._13_23_33_43 = col2;
	m._14_24_34_44 = col3;

	return m;
}
#else
//获取某帧某个骨骼的transform
half4x4 LoadBoneTransform(float animStartOffset, float frame, float boneIndex) {
	//一根骨骼transform占4个像素
	float pixelOffset = animStartOffset + (frame * _BoneCount + boneIndex) * 4.0;
	float4 uv0 = GetUV(pixelOffset, _AnimTextureSize.xy);
	float4 uv1 = GetUV(pixelOffset + 1, _AnimTextureSize.xy);
	float4 uv2 = GetUV(pixelOffset + 2, _AnimTextureSize.xy);
	float4 uv3 = GetUV(pixelOffset + 3, _AnimTextureSize.xy);
	half4 row0 = tex2Dlod(_AnimationData, uv0);
	half4 row1 = tex2Dlod(_AnimationData, uv1);
	half4 row2 = tex2Dlod(_AnimationData, uv2);
	half4 row3 = tex2Dlod(_AnimationData, uv3);
	return half4x4(row0, row1, row2, row3);
}
#endif

//用4根骨骼skin
half3 CustomSkinImpl(float3 localPos, float4 indices, float4 weights) {
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform0 = LoadBoneTransform(startOffset, frame, indices.x);
	half4x4 boneTransform1 = LoadBoneTransform(startOffset, frame, indices.y);
	half4x4 boneTransform2 = LoadBoneTransform(startOffset, frame, indices.z);
	half4x4 boneTransform3 = LoadBoneTransform(startOffset, frame, indices.w);
	half4 localPos4 = half4(localPos, 1.0);
	return
		mul(boneTransform0, localPos4).xyz * weights.x +
		mul(boneTransform1, localPos4).xyz * weights.y +
		mul(boneTransform2, localPos4).xyz * weights.z +
		mul(boneTransform3, localPos4).xyz * weights.w;
}

half3 CustomSkinWithBlendingImpl(float3 localPos, float4 indices, float4 weights) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform0 = LoadBoneTransform(startOffset, frame, indices.x);
	half4x4 boneTransform1 = LoadBoneTransform(startOffset, frame, indices.y);
	half4x4 boneTransform2 = LoadBoneTransform(startOffset, frame, indices.z);
	half4x4 boneTransform3 = LoadBoneTransform(startOffset, frame, indices.w);
	half4 localPos4 = half4(localPos, 1.0);
	half3 curStatePos =
		mul(boneTransform0, localPos4).xyz * weights.x +
		mul(boneTransform1, localPos4).xyz * weights.y +
		mul(boneTransform2, localPos4).xyz * weights.z +
		mul(boneTransform3, localPos4).xyz * weights.w;

	float nextStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float nextStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);
	half4x4 nextStateBoneTransform0 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.x);
	half4x4 nextStateBoneTransform1 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.y);
	half4x4 nextStateBoneTransform2 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.z);
	half4x4 nextStateBoneTransform3 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.w);

	half3 nextStatePos =
		mul(nextStateBoneTransform0, localPos4).xyz * weights.x +
		mul(nextStateBoneTransform1, localPos4).xyz * weights.y +
		mul(nextStateBoneTransform2, localPos4).xyz * weights.z +
		mul(nextStateBoneTransform3, localPos4).xyz * weights.w;

	return curStatePos * (1 - blendFactor) + nextStatePos * blendFactor;
}

//用4根骨骼skin
half3 CustomSkinWithNormalImpl(float3 localPos, inout float3 localNormal, float4 indices, float4 weights) {
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform0 = LoadBoneTransform(startOffset, frame, indices.x);
	half4x4 boneTransform1 = LoadBoneTransform(startOffset, frame, indices.y);
	half4x4 boneTransform2 = LoadBoneTransform(startOffset, frame, indices.z);
	half4x4 boneTransform3 = LoadBoneTransform(startOffset, frame, indices.w);

	half4 localNormal4 = half4(localNormal, 0.0);
	localNormal = mul(boneTransform0, localNormal4).xyz * weights.x +
		mul(boneTransform1, localNormal4).xyz * weights.y +
		mul(boneTransform2, localNormal4).xyz * weights.z +
		mul(boneTransform3, localNormal4).xyz * weights.w;

	localNormal = normalize(localNormal);

	half4 localPos4 = half4(localPos, 1.0);
	return
		mul(boneTransform0, localPos4).xyz * weights.x +
		mul(boneTransform1, localPos4).xyz * weights.y +
		mul(boneTransform2, localPos4).xyz * weights.z +
		mul(boneTransform3, localPos4).xyz * weights.w;
}

//只用2根骨骼skin
half3 CustomSkin2BoneImpl(float3 localPos, float4 indices, float4 weights) {
	//获取transform
	float curStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float curStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	half4 localPos4 = half4(localPos, 1.0);

	half4x4 curStateBoneTransform0 = LoadBoneTransform(curStateStartOffset, curStateFrame, indices.x);
	half4x4 curStateBoneTransform1 = LoadBoneTransform(curStateStartOffset, curStateFrame, indices.y);
	return
		mul(curStateBoneTransform0, localPos4).xyz * weights.x +
		mul(curStateBoneTransform1, localPos4).xyz * weights.y;
}

half3 CustomSkin2BoneWithBlendingImpl(float3 localPos, float4 indices, float4 weights) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);
	//获取transform
	float curStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float curStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);

	float nextStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float nextStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);

	half4 localPos4 = half4(localPos, 1.0);

	half4x4 curStateBoneTransform0 = LoadBoneTransform(curStateStartOffset, curStateFrame, indices.x);
	half4x4 curStateBoneTransform1 = LoadBoneTransform(curStateStartOffset, curStateFrame, indices.y);
	half3 curStatePos =
		mul(curStateBoneTransform0, localPos4).xyz * weights.x +
		mul(curStateBoneTransform1, localPos4).xyz * weights.y;

	half4x4 nextStateBoneTransform0 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.x);
	half4x4 nextStateBoneTransform1 = LoadBoneTransform(nextStateStartOffset, nextStateFrame, indices.y);
	half3 nextStatePos =
		mul(nextStateBoneTransform0, localPos4).xyz * weights.x +
		mul(nextStateBoneTransform1, localPos4).xyz * weights.y;

	return curStatePos * (1 - blendFactor) + nextStatePos * blendFactor;
}

//只用2根骨骼skin,带法线
half3 CustomSkin2BoneWithNormalImpl(float3 localPos, inout float3 localNormal, float4 indices, float4 weights) {
	//获取transform
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform0 = LoadBoneTransform(startOffset, frame, indices.x);
	half4x4 boneTransform1 = LoadBoneTransform(startOffset, frame, indices.y);

	half4 localNormal4 = half4(localNormal, 0.0);
	localNormal = mul(boneTransform0, localNormal4).xyz * weights.x +
		mul(boneTransform1, localNormal4).xyz * weights.y;
	localNormal = normalize(localNormal);

	half4 localPos4 = half4(localPos, 1.0);
	return
		mul(boneTransform0, localPos4).xyz * weights.x +
		mul(boneTransform1, localPos4).xyz * weights.y;
}

//刚体动画
half3 RigidTransformImpl(half3 localPos, float boneIndex) {
	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform = LoadBoneTransform(startOffset + _AnimTextureSize.z, frame, boneIndex);
	return mul(boneTransform, half4(localPos, 1.0)).xyz;
}

//刚体动画
half3 RigidTransformWithBlendingImpl(half3 localPos, float boneIndex) {
	float blendFactor = UNITY_ACCESS_INSTANCED_PROP(Props, _BlendParams);

	float frame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.z);
	float startOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.y);
	half4x4 boneTransform = LoadBoneTransform(startOffset + _AnimTextureSize.z, frame, boneIndex);
	half3 curStatePos = mul(boneTransform, half4(localPos, 1.0)).xyz;

	float nextStateFrame = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.x);
	float nextStateStartOffset = UNITY_ACCESS_INSTANCED_PROP(Props, _AnimationParams.w);
	half4x4 nextStateBoneTransform = LoadBoneTransform(nextStateStartOffset + _AnimTextureSize.z, nextStateFrame, boneIndex);
	half3 nextStatePos = mul(nextStateBoneTransform, half4(localPos, 1.0)).xyz;

	return curStatePos * (1 - blendFactor) + nextStatePos * blendFactor;
}