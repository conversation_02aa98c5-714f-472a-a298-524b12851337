﻿ 



 
 


#if UNITY_EDITOR

using System.IO;
using UnityEngine;

namespace TFW.Map
{
    public partial class RegionLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new RegionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.RegionLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string name = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            float layerWidth = reader.ReadSingle();
            float layerHeight = reader.ReadSingle();
            float displayVertexRadius = reader.ReadSingle();

            string defaultRegionMaterialGuid = Utils.ReadString(reader);

            float borderMinX = reader.ReadSingle();
            float borderMinZ = reader.ReadSingle();
            float borderMaxX = reader.ReadSingle();
            float borderMaxZ = reader.ReadSingle();
            Vector3[] borderMeshVertices = Utils.ReadVector3Array(reader);
            int[] borderMeshIndices = Utils.ReadIntArray(reader);
            string borderMaterialGuid = Utils.ReadString(reader);

            bool generateBorderLine = reader.ReadBoolean();
            string borderLineMaterialGuid = Utils.ReadString(reader);
            float borderLineWidth = reader.ReadSingle();
            bool showBorderLineMesh = reader.ReadBoolean();
            bool showRegionMesh = reader.ReadBoolean();

            int objectCount = reader.ReadInt32();

            config.RegionData[] regions = new config.RegionData[objectCount];
            for (int i = 0; i < objectCount; ++i)
            {
                var regionData = new config.RegionData();
                regions[i] = regionData;
                regionData.outline = Utils.ReadVector3List(reader);
                regionData.innerColor = Utils.ReadColor(reader);
                regionData.outerColor = Utils.ReadColor(reader);
                regionData.meshVertices = Utils.ReadVector3Array(reader);
                regionData.meshIndices = Utils.ReadIntArray(reader);
                regionData.vertexColors = Utils.ReadColorArray(reader);
                regionData.type = (RegionType)reader.ReadInt32();
                regionData.number = reader.ReadInt32();
                regionData.materialGuid = Utils.ReadString(reader);
                regionData.SetID(AllocateID());
            }

            var layer = new config.RegionLayerData(layerID, name, layerOffset, layerWidth, layerHeight, regions, displayVertexRadius, defaultRegionMaterialGuid, borderMinX, borderMinZ, borderMaxX, borderMaxZ, borderMeshVertices, borderMeshIndices, borderMaterialGuid, generateBorderLine, borderLineMaterialGuid, borderLineWidth, showBorderLineMesh, showRegionMesh);
            layer.active = active;
            return layer;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif