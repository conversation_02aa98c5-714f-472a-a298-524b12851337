﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveGridModelLayer(BinaryWriter writer, EditorGridModelLayer layer)
        {
            BeginSection(MapDataSectionType.Grid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, writer);

            writer.Write(VersionSetting.GridModelLayerStructVersion);

            //-----------------version 1 start---------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            int cols = layer.horizontalTileCount;
            int rows = layer.verticalTileCount;

            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layer.layerData.tileWidth);
            writer.Write(layer.layerData.tileHeight);
            writer.Write((int)layer.layerData.gridType);

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var data = layer.GetObjectData(j, i) as ModelData;
                    bool hasTile = data != null;
                    writer.Write(hasTile);
                    if (hasTile)
                    {
                        SaveGridModelDataV1(writer, data, layer);
                    }
                }
            }
            //save map layer lod config
            SaveGridModelLayerLODConfigV1(writer, layer.layerData);
            //------------------version 1 end--------------------------
            //-----------------version 2 start------------------------------
            SaveGridModelLayerLODConfigV2(writer, layer.layerData);
            //-----------------version 2 end------------------------------
        }

        void SaveGridModelDataV1(BinaryWriter writer, ModelData data, EditorGridModelLayer layer)
        {
            bool isDefaultRotation = (data.GetRotation() == Quaternion.identity);
            bool isDefaultScale = (data.GetScale() == Vector3.one);

            mIDExport.Export(writer, data.id);
            Utils.WriteVector3(writer, data.GetPosition());
            writer.Write(isDefaultRotation);
            if (isDefaultRotation == false)
            {
                Utils.WriteQuaternion(writer, data.GetRotation());
            }
            writer.Write(isDefaultScale);
            if (isDefaultScale == false)
            {
                Utils.WriteVector3(writer, data.GetScale());
            }

            var coord = (layer.layerData as SparseGridObjectLayerData).GetObjectCoordinate(data);
            var pos = layer.layerData.FromCoordinateToWorldPositionCenter(coord.x, coord.y);
            bool isDefaultPosition = pos == data.GetPosition();
            writer.Write(isDefaultPosition);
            mIDExport.Export(writer, data.GetModelTemplateID());
        }

        void SaveGridModelLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SaveGridModelLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif