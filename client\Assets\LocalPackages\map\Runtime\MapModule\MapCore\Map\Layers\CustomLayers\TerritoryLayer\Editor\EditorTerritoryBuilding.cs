﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class EditorTerritoryBuilding
    {
        public EditorTerritoryBuilding(int id, GameObject root, Vector3 pos, Color color, float displayRadius)
        {
            mGameObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            mGameObject.name = "City " + id.ToString();
            mGameObject.transform.position = pos;
            mGameObject.transform.localScale = Vector3.one * displayRadius;
            mGameObject.transform.SetParent(root.transform);
            var logic = mGameObject.AddComponent<EditorTerritoryBuildingLogic>();
            logic.SetBuilding(this);
            var renderer = mGameObject.GetComponent<MeshRenderer>();
            mMaterial = new Material(Shader.Find("SLGMaker/Color"));
            mMaterial.SetColor("_Color", color);
            mID = id;
            renderer.sharedMaterial = mMaterial;
        }

        public void OnDestroy(bool destroyGameObject)
        {
            mIsDestroying = true;
            if (destroyGameObject)
            {
                Utils.DestroyObject(mGameObject);
            }
            Utils.DestroyObject(mMaterial);
        }

        public void SetColor(Color color)
        {
            var renderer = mGameObject.GetComponent<MeshRenderer>();
            renderer.sharedMaterial.SetColor("_Color", color);
        }

        public GameObject gameObject { get { return mGameObject; } }
        public int id { get { return mID; } }
        public bool isDestroying { get { return mIsDestroying; } }

        int mID;
        GameObject mGameObject;
        Material mMaterial;
        bool mIsDestroying = false;
    }
}

#endif