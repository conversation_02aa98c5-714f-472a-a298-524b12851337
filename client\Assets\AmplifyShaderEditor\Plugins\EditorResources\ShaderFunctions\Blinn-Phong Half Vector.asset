%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Blinn-Phong Half Vector
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=13705\n487;506;979;512;774.2847;195.955;1;False;False\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;2;-640,96;Float;False;1;0;FLOAT;0.0;False;4;FLOAT3;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1;-592,-64;Float;False;World;0;4;FLOAT3;FLOAT;FLOAT;FLOAT\nNode;AmplifyShaderEditor.SimpleAddOpNode;3;-336,0;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0.0,0,0;False;1;FLOAT3\nNode;AmplifyShaderEditor.NormalizeNode;4;-176,0;Float;False;1;0;FLOAT3;0,0,0,0;False;1;FLOAT3\nNode;AmplifyShaderEditor.FunctionOutput;0;0,0;Float;False;True;XYZ;0;1;0;FLOAT3;0,0,0;False;0\nWireConnection;3;0;1;0\nWireConnection;3;1;2;0\nWireConnection;4;0;3;0\nWireConnection;0;0;4;0\nASEEND*/\n//CHKSM=CDF922191A60D2395104E4F539CAFDF78289C070"
  m_functionName: 
  m_description: Calculates the halfway vector between view direction and light direction
    in world space.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_nodeCategory: 11
  m_customNodeCategory: Custom Lighting
