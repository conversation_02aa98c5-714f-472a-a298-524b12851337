﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //相机在某个高度范围内使transform从一个点移动到另外一个点
    public class LODMoveToTarget
    {
        public LODMoveToTarget(Transform transform, Transform cityRootTransform)
        {
            mTransform = transform;
            mCityRootTransform = cityRootTransform;
        }

        public void SetTransform(Transform transform)
        {
            mTransform = transform;
        }

        public void SetMoveInfo(Vector3 worldStartPos, Vector3 localEndPos, Vector2 cameraHeightRange)
        {
            mWorldStartPosition = worldStartPos;
            mLocalEndPosition = localEndPos;
            mCameraMinHeight = cameraHeightRange.x;
            mCameraMaxHeight = cameraHeightRange.y;
        }

        public void SetStartPosition(Vector3 worldStartPos)
        {
            mWorldStartPosition = worldStartPos;
        }

        public void Update()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                float cameraHeight = map.camera.transform.position.y;
                float t = (cameraHeight - mCameraMinHeight) / (mCameraMaxHeight - mCameraMinHeight);
                t = Mathf.Clamp01(t);
                var worldEndPos = mCityRootTransform.TransformPoint(mLocalEndPosition);
                mTransform.position = Vector3.Lerp(mWorldStartPosition, worldEndPos, t);
            }
        }

        public Vector3 worldEndPosition { get { return mCityRootTransform.TransformPoint(mLocalEndPosition); } }

        Vector3 mLocalEndPosition;
        Vector3 mWorldStartPosition;
        float mCameraMinHeight = 20;
        float mCameraMaxHeight = 70;
        Transform mTransform;
        Transform mCityRootTransform;
    }
}
