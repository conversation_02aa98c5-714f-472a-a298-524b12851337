﻿using System.Collections.Generic;

namespace Common.Pool
{
    /// <summary>
    /// List对象池(不会自动检测数据，需手动释放清理数据)
    /// </summary>
    public static class ListPool<T>
    {

        #region 属性和字段

        /// <summary>
        /// 对象池数据存储
        /// </summary>
        private static readonly ObjectPool<List<T>> _listPool = new ObjectPool<List<T>>(null, Clear);

        #endregion

        #region 对象池逻辑处理

        /// <summary>
        /// 数据清理
        /// </summary>
        /// <param name="l"></param>
        private static void Clear(List<T> l)
        {
            if(l != null)
                l.Clear();
        }

        /// <summary>
        /// 数据获取
        /// </summary>
        /// <returns></returns>
        public static List<T> Get()
        {
            return _listPool.Get();
        }

        /// <summary>
        /// 数据回收
        /// </summary>
        /// <param name="toRelease"></param>
        public static void Release(List<T> toRelease)
        {
            _listPool.Release(toRelease);
        }

        #endregion

    }
}
