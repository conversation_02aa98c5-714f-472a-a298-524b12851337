﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class EditCameraColliderVertex : CameraColliderEditorTool
    {
        public EditCameraColliderVertex(CameraColliderEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onMoveVertex = OnMoveVertex;
            handlers.onInsertVertex = OnInsertVertex;
            handlers.onRemoveVertex = OnRemoveVertex;
        }

        public override void OnDestroy()
        {
        }

        public override void OnEnabled()
        {
        }
        public override void OnDisabled()
        {
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.button == 0 && e.type == EventType.MouseUp)
            {
                mLeftButtonDown = false;
                if (mVertexMover != null)
                {
                    mVertexMover.Stop(pos);
                    mVertexMover = null;
                }
                mMover.Reset();
            }

            if (e.type == EventType.MouseDown && e.button == 0 && e.alt == false)
            {
                if (e.control == false)
                {
                    mEditor.Pick(screenPos);
                }
                else
                {
                    if (e.shift)
                    {
                        mEditor.Pick(screenPos);
                        RemoveVertex();
                    }
                    else
                    {
                        AddVertex(pos);
                    }
                }
            }

            if (mLeftButtonDown && e.control == false && e.shift == false)
            {
                MoveVertex(pos);
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void DrawScene()
        {
        }

        public override void DrawGUI()
        {
            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "顶点显示大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }

            EditorGUILayout.LabelField("Add Vertex: Ctrl + Mouse Left Button");
            EditorGUILayout.LabelField("Remove Vertex: Ctrl + Shift + Mouse Left Button");
        }

        void OnMoveVertex(int dataID, int vertexIndex)
        {
            mEditor.RepaintScene();
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void MoveVertex(Vector3 pos)
        {
            if (mEditor.selectedObjectID != 0 && mEditor.selectedVertexIndex >= 0)
            {
                UnityEngine.Debug.Assert(mEditor.selectedVertexIndex >= 0);

                if (mVertexMover == null)
                {
                    mVertexMover = new CameraColliderVertexMover(mEditor.layer.id, mEditor.selectedObjectID, mEditor.selectedVertexIndex, mEditor.layer.displayType);
                }

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as CameraColliderData;
                var oldPos = collisionData.GetVertexPos(mEditor.selectedVertexIndex);
                var newPos = mMover.GetDelta() + oldPos;

                mEditor.layer.SetVertexPosition(mEditor.selectedObjectID, mEditor.selectedVertexIndex, newPos);

                mEditor.RepaintScene();
            }
        }

        void AddVertex(Vector3 localPos)
        {
            if (mEditor.selectedObjectID == 0)
            {
                return;
            }
            var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as CameraColliderData;
            if (collisionData.topOutline == null)
            {
                if (IsBottomOutlineVertexSelected())
                {
                    var idx = Utils.FindNearestEdgeDistance(localPos, collisionData.GetOutlineVertices(mEditor.layer.displayType));
                    InsertVertex(idx, localPos);
                }
            }
        }

        void OnInsertVertex(int dataID, int index)
        {
            mEditor.SetSelection(Map.currentMap.FindObject(dataID) as CameraColliderData, index);
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void InsertVertex(int index, Vector3 localPos)
        {
            var action = new ActionAddCameraColliderVertex(mEditor.layer.id, mEditor.selectedObjectID, index, localPos, mEditor.layer.displayType);
            ActionManager.instance.PushAction(action);
        }

        bool IsBottomOutlineVertexSelected()
        {
            var idx = mEditor.selectedVertexIndex;
            var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as CameraColliderData;
            if (collisionData != null && (idx >= 0 && idx < collisionData.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle).Count))
            {
                return true;
            }
            return false;
        }

        void OnRemoveVertex(int dataID, int index)
        {
            mEditor.ClearVertexSelection();
            mEditor.RepaintGUI();
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
        }

        void RemoveVertex()
        {
            if (mEditor.selectedObjectID == 0)
            {
                return;
            }
            var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as CameraColliderData;
            if (collisionData.topOutline == null)
            {
                if (IsBottomOutlineVertexSelected())
                {
                    var vertices = collisionData.GetOutlineVertices(mEditor.layer.displayType);
                    if (vertices.Count > 3)
                    {
                        var action = new ActionRemoveCameraColliderVertex(mEditor.layer.id, mEditor.selectedObjectID, mEditor.selectedVertexIndex, mEditor.layer.displayType);
                        ActionManager.instance.PushAction(action);
                    }
                }
            }
        }

        public override CameraColliderEditorToolType type { get { return CameraColliderEditorToolType.EditVertex; } }

        bool mLeftButtonDown = false;
        CameraColliderVertexMover mVertexMover;
        MouseMover mMover = new MouseMover();
    }
}

#endif