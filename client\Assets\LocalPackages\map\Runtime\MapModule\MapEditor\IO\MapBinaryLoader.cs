﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using System.IO;

namespace TFW.Map {
    [Black]
    public abstract class MapBinaryLoader {
        public MapBinaryLoader(int majorVersion) {
            mMajorVersion = majorVersion;
        }

        public abstract config.EditorMapData Load(int minorVersion, string mapName, Map map, BinaryReader reader);

        public int majorVersion { get { return mMajorVersion; } }

        int mMajorVersion;
    }
}


#endif