﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public enum ModelOperationType {
        kCreateObject,
        kSelectObject,
        kRemoveObject,
    }

    [ExecuteInEditMode]
    [Black]
    public class ModelLayerLogic : MapLayerLogic {
        public string selectedPrefabGUID { set; get; }
        public ModelOperationType operationType { set; get; }
        public PrefabRotationSetting rotationSetting { get { return mRotationSetting; } }

        public QuadTreeObjectLayerData layerData {
            get {
                var layerData = Map.currentMap.FindObject(layerID) as QuadTreeObjectLayerData;
                return layerData;
            }
        }

        void OnDestroy() {
            //base.OnDestroy();
            mRotationSetting.OnDestroy();
        }

        PrefabRotationSetting mRotationSetting = new PrefabRotationSetting();
    }
}

#endif