﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.11.19
 */

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class RenderTextureObjectIndicator
    {
        public void SetPrefab(GameObject prefab)
        {
            if (prefab != mPrefab)
            {
                mPrefab = prefab;
                if (mPrefabInstance != null)
                {
                    GameObject.DestroyImmediate(mPrefabInstance);
                    mPrefabInstance = null;
                }

                if (prefab != null)
                {
                    mPrefabInstance = CreateGameObject();
                    Utils.HideGameObject(mPrefabInstance);
                    mPrefabInstance.name = "Prefab Indicator";
                    mPrefabInstance.SetActive(false);
                }
            }
        }

        public void OnDestroy()
        {
            if (mPrefabInstance != null)
            {
                GameObject.DestroyImmediate(mPrefabInstance);
                mPrefabInstance = null;
            }

            if (mRenderTexture != null)
            {
                GameObject.DestroyImmediate(mRenderTexture);
                mRenderTexture = null;
            }

            if (mRenderObjectToTexture != null)
            {
                mRenderObjectToTexture.OnDestroy(true);
                mRenderObjectToTexture = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.position = pos;
            }
        }

        public void SetRotation(Quaternion rot)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.rotation = rot;
            }
        }

        public void SetActive(bool active)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.SetActive(active);
            }
        }

        GameObject CreateGameObject()
        {
            var prefabPath = AssetDatabase.GetAssetPath(mPrefab);
            Debug.Assert(!string.IsNullOrEmpty(prefabPath));
            var prefab = Map.currentMap.view.reusableGameObjectPool.Require(prefabPath, Vector3.zero, Vector3.one, Quaternion.identity);

            if (mRenderTexture == null)
            {
                mRenderTexture = new RenderTexture(256, 256, 16, RenderTextureFormat.Default, RenderTextureReadWrite.Default);
                mRenderObjectToTexture = new RenderObjectToTexture(null, null);
            }
            mRenderObjectToTexture.Render(prefab, false, 40, mRenderTexture, true, false, new Bounds());

            var objectBounds = GameObjectBoundsCalculator.CalculateBounds(prefab, false);

            var obj = new GameObject("RenderTextureGameObject");
            obj.transform.parent = Map.currentMap.root.transform;
            var mesh = CreateMesh(objectBounds.min, objectBounds.max);
            var meshFilter = obj.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = mesh;
            var renderer = obj.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Transparent"));
            mtl.mainTexture = mRenderTexture;
            renderer.sharedMaterial = mtl;

            Map.currentMap.view.reusableGameObjectPool.Release(prefabPath, prefab, Map.currentMap);

            return obj;
        }

        Mesh CreateMesh(Vector3 min, Vector3 max)
        {
            var mesh = new Mesh();
            var vertices = new Vector3[4]
            {
                new Vector3(min.x, 0, min.z),
                new Vector3(min.x, 0, max.z),
                new Vector3(max.x, 0, max.z),
                new Vector3(max.x, 0, min.z),
            };
            var indices = new int[6]
            {
                0, 1, 2, 0, 2, 3
            };
            var uvs = new Vector2[4]
            {
                Vector2.zero,
                new Vector2(0, 1),
                Vector2.one,
                new Vector2(1, 0),
            };
            mesh.vertices = vertices;
            mesh.triangles = indices;
            mesh.uv = uvs;
            mesh.RecalculateBounds();
            mesh.RecalculateNormals();
            return mesh;
        }

        RenderTexture mRenderTexture;
        RenderObjectToTexture mRenderObjectToTexture;
        GameObject mPrefab;
        GameObject mPrefabInstance;
    }
}

#endif