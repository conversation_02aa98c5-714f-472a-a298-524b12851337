﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [System.Flags]
    public enum DirtyMask
    {
        FrontLayerObstacle = 1,
        RailwayObstacle = 2,
        NavMesh = 4,
        NPCRegionConfig = 8,
        NPCSpawnPoints = 16,
        <PERSON>lisionLayer = 32,
        RuinConfig = 64,
        PolygonRiverLayer = 128,
        ComplexGridModelLayer = 256,

        All = -1,
    }

    public static class EditorConfig
    {
        public static void Reset()
        {
            dirtyFlag = DirtyMask.All;
        }

        public static Quaternion copyRotation = Quaternion.identity;
        public static Vector3 copyPosition = Vector3.zero;
        public static Vector3 copyScale = Vector3.one;
        public static DirtyMask dirtyFlag = DirtyMask.All;
    }
}

#endif