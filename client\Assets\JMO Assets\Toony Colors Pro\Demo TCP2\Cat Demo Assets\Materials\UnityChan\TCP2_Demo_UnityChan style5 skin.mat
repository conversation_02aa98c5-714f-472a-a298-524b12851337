%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_UnityChan style5 skin
  m_Shader: {fileID: 4800000, guid: d9b5104efdb2d6743b329b68d42640dd, type: 3}
  m_ShaderKeywords: TCP2_ZSMOOTH_ON _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BlendTex1
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _BlendTex2
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 2800000, guid: 0acd5974827369042b71efa22a9a1947, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 88e58b4cc42d7304fb1dc40653de8e97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _STexture
      second:
        m_Texture: {fileID: 2800000, guid: 45ac4363e3b257c418e91f9f201bbf13, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _SketchTex
      second:
        m_Texture: {fileID: 2800000, guid: a0d03d906b6264d4c8c02b0ffd7bc8c2, type: 3}
        m_Scale: {x: 10, y: 10}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 0.5
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _EnableConstSizeOutline
      second: 0
    - first:
        name: _EnableTexturedOutline
      second: 0
    - first:
        name: _EnableZSmooth
      second: 1
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Offset1
      second: 0
    - first:
        name: _Offset2
      second: 0
    - first:
        name: _Outline
      second: 0.2
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.1
    - first:
        name: _RampSmoothOtherLights
      second: 1
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RampThresholdOtherLights
      second: 0.65
    - first:
        name: _RimMax
      second: 0.9
    - first:
        name: _RimMin
      second: 0.6
    - first:
        name: _Shadow_HSV_H
      second: 0
    - first:
        name: _Shadow_HSV_S
      second: 0
    - first:
        name: _Shadow_HSV_V
      second: 0
    - first:
        name: _Shininess
      second: 1.04
    - first:
        name: _SketchSpeed
      second: 5
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _TexLod
      second: 5
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _WrapFactor
      second: 0.5
    - first:
        name: _ZSmooth
      second: 1.5
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    - first:
        name: __outline_gui_dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 0.8627451, b: 0.8627451, a: 1}
    - first:
        name: _DiffTint
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _OutlineColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _RimColor
      second: {r: 0, g: 0, b: 0, a: 0.7058824}
    - first:
        name: _SColor
      second: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    - first:
        name: _SketchColor
      second: {r: 1, g: 0.7882353, b: 0.6313726, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - first:
        name: _VColorBlendOffset
      second: {r: 0, g: 0, b: 0, a: 0}
    - first:
        name: _VColorBlendSmooth
      second: {r: 0.25, g: 0.25, b: 0.25, a: 0.25}
