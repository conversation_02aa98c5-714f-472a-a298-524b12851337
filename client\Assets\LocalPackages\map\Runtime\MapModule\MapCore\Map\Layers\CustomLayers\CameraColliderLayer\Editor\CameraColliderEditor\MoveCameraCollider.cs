﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class MoveCameraCollider : CameraColliderEditorTool
    {
        public MoveCameraCollider(CameraColliderEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onMoveCollision = OnMoveCollision;
            handlers.onRotateCollision = OnRotateCollision;
        }

        public override void OnDestroy()
        {
        }

        public override void OnDisabled()
        {
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.type == EventType.MouseUp)
            {
                if (e.button == 0)
                {
                    mLeftButtonDown = false;

                    if (mCollisionMover != null)
                    {
                        mCollisionMover.Stop(pos);
                        mCollisionMover = null;
                    }
                }

                mPickWhenMovingCollision = true;
                mMover.Reset();
            }

            if (mLeftButtonDown)
            {
                if (mPickWhenMovingCollision == true)
                {
                    mPickWhenMovingCollision = false;
                    mEditor.Pick(screenPos);
                }
                MoveCollision(pos);
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void OnEnabled()
        {
        }

        void OnMoveCollision(int dataID)
        {
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
            mEditor.RepaintScene();
        }

        void MoveCollision(Vector3 pos)
        {
            if (mEditor.selectedObjectID != 0)
            {
                if (mCollisionMover == null)
                {
                    mCollisionMover = new CameraColliderMover(mEditor.layer.id, mEditor.selectedObjectID, pos);
                }

                UnityEngine.Debug.Assert(mEditor.selectedVertexIndex >= 0);

                mMover.Update(pos);

                var collisionData = Map.currentMap.FindObject(mEditor.selectedObjectID) as CameraColliderData;
                var offset = mMover.GetDelta();

                mEditor.layer.MoveObject(PrefabOutlineType.NavMeshObstacle, mEditor.selectedObjectID, offset);

                mEditor.RepaintScene();
            }
        }

        public override void DrawScene()
        {
        }

        public override void DrawGUI()
        {
            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "显示顶点大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }
            if (mEditor.selectedObjectID != 0)
            {
                EditorGUILayout.BeginHorizontal();
                mRotateAngle = EditorGUILayout.FloatField(new GUIContent("Angle", "旋转角度"), mRotateAngle);
                if (GUILayout.Button(new GUIContent("Rotate", "旋转")))
                {
                    if (mEditor.selectedObjectID != 0)
                    {
                        var action = new ActionRotateCameraCollider(mEditor.layer.id, mEditor.selectedObjectID, mRotateAngle);
                        ActionManager.instance.PushAction(action);
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        void OnRotateCollision(int dataID)
        {
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
            mEditor.RepaintScene();
        }

        public override CameraColliderEditorToolType type { get { return CameraColliderEditorToolType.MoveAndRotate; } }

        bool mLeftButtonDown = false;
        bool mPickWhenMovingCollision = true;
        float mRotateAngle = 0;
        MouseMover mMover = new MouseMover();
        CameraColliderMover mCollisionMover;
    }
}


#endif