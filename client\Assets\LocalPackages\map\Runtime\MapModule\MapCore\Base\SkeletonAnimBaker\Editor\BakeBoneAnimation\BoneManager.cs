﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理烘培动画时的骨骼信息
    public class BoneManager
    {
        public class BoneEntry
        {
            public BoneEntry(Transform t)
            {
                transform = t;
            }

            public BoneEntry(Transform t, Matrix4x4 bindpose)
            {
                transform = t;
                mBindPose = bindpose;
            }

            public bool SetBindPose(Matrix4x4 pose, bool check)
            {
                if (check && mBindPose != pose)
                    return false;

                mBindPose = pose;
                return true;
            }

            public Matrix4x4 bindpose { get { return mBindPose; } }

            public Transform transform;
            Matrix4x4 mBindPose;
        }

        public void AddBone(Transform bone)
        {
            var oldBone = GetBone(bone.name);
            if (oldBone == null)
            {
                mBones.Add(new BoneEntry(bone));
            }
        }

        public void AddBone(Transform bone, Matrix4x4 bindpose)
        {
            var oldBone = GetBone(bone.name);
            if (oldBone == null)
            {
                mBones.Add(new BoneEntry(bone, bindpose));
            }
        }

        public bool AddBindPose(Transform[] bones, Matrix4x4[] bineposes, bool check)
        {
            for (int i = 0; i < bones.Length; ++i)
            {
                var bone = GetBone(bones[i].name);
                bool suc = bone.SetBindPose(bineposes[i], check);
                if (!suc)
                {
                    return false;
                }
            }
            return true;
        }

        public Transform GetBoneTransform(string name)
        {
            var bone = GetBone(name);
            return bone?.transform;
        }

        public int GetBoneIndex(Transform t)
        {
            for (int i = 0; i < mBones.Count; ++i)
            {
                if (mBones[i].transform.name == t.name)
                {
                    return i;
                }
            }
            return -1;
        }

        public Matrix4x4 GetBindPose(int idx)
        {
            return mBones[idx].bindpose;
        }

        public Matrix4x4 GetBindPose(string name)
        {
            var bone = GetBone(name);
            if (bone != null)
            {
                return bone.bindpose;
            }

            Debug.Assert(false);
            return Matrix4x4.identity;
        }

        BoneEntry GetBone(string name)
        {
            for (int i = 0; i < mBones.Count; ++i)
            {
                if (mBones[i].transform.name == name)
                {
                    return mBones[i];
                }
            }
            return null;
        }

        public string GetRootBoneName()
        {
            var rootBone = GetRootBone();
            return rootBone.name;
        }

        public Transform GetRootBone()
        {
            if (mRootBone == null)
            {
                mRootBone = CalculateRootBone();
            }
            return mRootBone;
        }

        public void SetRootBone(Transform rootBone)
        {
            mRootBone = rootBone;
        }

        Transform CalculateRootBone()
        {
            List<Transform> rootBones = new List<Transform>();
            for (int i = 0; i < mBones.Count; ++i)
            {
                BoneEntry testBone = mBones[i];
                bool isRoot = true;
                for (int k = 0; k < mBones.Count; ++k)
                {
                    if (k != i && Utils.IsChild(mBones[k].transform.gameObject, testBone.transform.gameObject))
                    {
                        isRoot = false;
                        break;
                    }
                }
                if (isRoot)
                {
                    rootBones.Add(mBones[i].transform);
                }
            }

            if (rootBones.Count == 1)
            {
                return rootBones[0];
            }
            return rootBones[0].parent;
        }

        public int boneCount { get { return mBones.Count; } }
        public List<BoneEntry> bones { get { return mBones; } }

        List<BoneEntry> mBones = new List<BoneEntry>();
        //root bone可以是mBones以外的bone
        Transform mRootBone;
    }
}
