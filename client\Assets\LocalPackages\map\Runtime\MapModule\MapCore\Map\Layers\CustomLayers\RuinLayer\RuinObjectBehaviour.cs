﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [SelectionBase]
    [ExecuteInEditMode]
    class RuinObjectBehaviour : MonoBehaviour
    {
        bool mProcessStartMessage = true;
        public void Init(int objectID, int layerID)
        {
            mObjectID = objectID;
            mLayerID = layerID;
            Reset();
            mProcessStartMessage = false;
        }

        void Start()
        {
            if (mProcessStartMessage)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RuinLayer;
                if (layer.selectedObjectTypeIndex >= 0) {
                    var objectType = layer.ruinObjectTypes[layer.selectedObjectTypeIndex].name;
                    layer.AddObject(objectType, gameObject.transform.position, null, false, true);
                    GameObject.DestroyImmediate(gameObject);
                }
            }
        }

        void OnDestroy()
        {
            var map = Map.currentMap;
            if (map != null)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLayerID) as RuinLayer;
                if (layer != null)
                {
                    layer.AddDestroyedObject(objectID);
                }
            }
        }

        public bool IsTransformChanged()
        {
            return
                mInitPosition != gameObject.transform.localPosition ||
                mInitRotation != gameObject.transform.localRotation;
        }

        public void Reset()
        {
            mInitPosition = gameObject.transform.localPosition;
            mInitRotation = gameObject.transform.localRotation;
        }

        public int objectID { get { return mObjectID; } }

        Vector3 mInitPosition;
        Quaternion mInitRotation;
        int mObjectID;
        int mLayerID;
    }
}

#endif