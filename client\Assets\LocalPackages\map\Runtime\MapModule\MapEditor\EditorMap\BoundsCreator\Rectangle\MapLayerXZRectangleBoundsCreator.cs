﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class MapLayerXZRectangleBoundsCreator : MapLayerBoundsCreator
    {
        public override GameObject CreateLayerBounds(Color color)
        {
            float totalWidth = mLayer.GetLayerWidthInMeter(0);
            float totalHeight = mLayer.GetLayerHeightInMeter(0);
            return CreateBounds(totalWidth, totalHeight, color, false);
        }

        protected virtual Mesh CreateBoundsMesh(float width, float height, bool offset)
        {
            var mesh = new Mesh();
            Vector3[] positions = new Vector3[4];
            int[] indices = new int[8]{
            0,1,1,2,2,3,3,0,
            };
            float ox = 0;
            float oz = 0;
            if (offset)
            {
                ox = -width * 0.5f;
                oz = -height * 0.5f;
            }
            positions[0] = new Vector3(ox, 0, oz);
            positions[1] = new Vector3(ox + width, 0, oz);
            positions[2] = new Vector3(ox + width, 0, oz + height);
            positions[3] = new Vector3(ox, 0, oz + height);

            mesh.vertices = positions;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            mesh.RecalculateBounds();

            return mesh;
        }

        GameObject CreateBounds(float width, float height, Color color, bool offset)
        {
            var bounds = new GameObject("XZSquareBounds");
            Utils.HideGameObject(bounds);
            var meshFilter = bounds.AddComponent<MeshFilter>();
            var meshRenderer = bounds.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Color"));
            mtl.color = color;
            meshRenderer.sharedMaterial = mtl;
            meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            var mesh = CreateBoundsMesh(1, 1, offset);
            meshFilter.sharedMesh = mesh;
            bounds.SetActive(true);
            bounds.transform.localScale = new Vector3(width, 1, height);
            return bounds;
        }
    };
}

#endif