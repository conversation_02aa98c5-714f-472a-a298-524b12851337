﻿Shader "SLGMaker/SealineShader"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		_DistortUV("DistortUV", 2D) = "white" {}
		_Mask("Mask", 2D) = "white" {}
		_Color("Color", Color) = (1,1,1,1)
		_DistortionFactor("Distortion Factor", Vector) = (0.03, 0.04, 0, 0)
		_WaveOffset("Wave Offset", Vector) = (0.05, 0, 0, 0)
	}
		SubShader
		{
			Tags { "RenderType" = "Transparent" }
			LOD 100

			Pass
			{
			Blend SrcAlpha OneMinusSrcAlpha
			Cull Off
			ZWrite Off

				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag

			#include "UnityCG.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
				float2 uv2 : TEXCOORD1;
				float4 color    : COLOR;
			};

			struct v2f
			{
				float2 uv : TEXCOORD0;
				float2 uv2 : TEXCOORD1;
				float3 worldPos:TEXCOORD2;
				float4 color    : COLOR;
				float4 vertex : SV_POSITION;
			};

			sampler2D _MainTex;
			sampler2D _DistortUV;
			sampler2D _Mask;
			float4 _MainTex_ST;
			fixed4 _Color;
			float4 _DistortionFactor;
			float4 _WaveOffset;

			v2f vert(appdata v)
			{
				v2f o;
				o.vertex = UnityObjectToClipPos(v.vertex);
				o.uv = TRANSFORM_TEX(v.uv, _MainTex);
				o.uv2 = v.uv2;
				o.color = v.color;
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				UNITY_TRANSFER_FOG(o,o.vertex);
				return o;
			}

			//float rand(float3 co)
			//{
			//	return frac(sin(dot(co.xyz, float3(12.9898, 78.233, 45.5432))) * 43758.5453);
			//}

			fixed4 frag(v2f i) : SV_Target
			{
				float2 uv = i.uv;
				//float r = rand(i.worldPos);
				//uv.y += r;
				float4 distortion = tex2D(_DistortUV, uv + _Time.x);
				float2 distortedUV = i.uv + float2(distortion.x*_DistortionFactor.x, distortion.y * _DistortionFactor.y);

				//distortedUV.y += cos(sin(i.worldPos.x + _Time.y) + _Time.y) * distortStrength;
				float delta = sin(i.worldPos.x + _Time.y);
				distortedUV.y += delta * _WaveOffset.x;
				//distortedUV.x += sin(i.worldPos.x + _Time.y) * 0.02;
				fixed4 col = tex2D(_MainTex, distortedUV);
				//fixed4 mask = tex2D(_Mask, i.uv2);
				//float s = 1;
				//float alphaChange = 1 - lerp(0, 0.5, saturate((delta * s + 1)));
				col.a = col.r * pow(i.color.a, 5) /** alphaChange*/ /** mask.g*/;
				col.rgb += _Color.rgb;
			return col;
		}
		ENDCG
	}
		}
}
