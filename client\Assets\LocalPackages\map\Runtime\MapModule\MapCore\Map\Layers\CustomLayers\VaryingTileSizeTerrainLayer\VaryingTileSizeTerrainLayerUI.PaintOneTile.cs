﻿#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public partial class VaryingTileSizeTerrainLayerUI : UnityEditor.Editor
    {
        void SetOneTile(bool clearTile)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorVaryingTileSizeTerrainPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                if (group.selectedIndex >= 0)
                {
                    var item = group.GetItem(group.selectedIndex);
                    if (clearTile)
                    {
                        mLogic.layer.SetTile(mPickedTile.x, mPickedTile.y, 0, 0, 0);
                    }
                    else
                    {
                        mLogic.layer.SetTile(mPickedTile.x, mPickedTile.y, group.selectedIndex, group.groupID, item.id);
                    }
                }
            }
        }

    }
}


#endif