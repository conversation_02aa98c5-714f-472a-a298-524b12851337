fileFormatVersion: 2
guid: 4ae0efe5bf8f6c964dfa46a9e95459a4
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: CityMovingArea
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 4
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 1
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 9
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 256
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 50
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPad
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: 32
    textureCompression: 0
    compressionQuality: 100
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 256
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 128
    resizeAlgorithm: 0
    textureFormat: 51
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: CityMovingArea
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 512
        height: 512
      alignment: 9
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -254, y: -53}
        - {x: -256, y: -41}
        - {x: -256, y: 40}
        - {x: -253, y: 58}
        - {x: -249, y: 74}
        - {x: -243, y: 91}
        - {x: -239, y: 105}
        - {x: -231, y: 119}
        - {x: -226, y: 129}
        - {x: -213, y: 149}
        - {x: -202, y: 164}
        - {x: -186, y: 182}
        - {x: -177, y: 191}
        - {x: -149, y: 213}
        - {x: -134, y: 223}
        - {x: -111, y: 235}
        - {x: -96, y: 242}
        - {x: -81, y: 247}
        - {x: -74, y: 249}
        - {x: -66, y: 251}
        - {x: -57, y: 253}
        - {x: -41, y: 256}
        - {x: 40, y: 256}
        - {x: 66, y: 251}
        - {x: 74, y: 249}
        - {x: 81, y: 247}
        - {x: 102, y: 240}
        - {x: 111, y: 235}
        - {x: 132, y: 224}
        - {x: 149, y: 213}
        - {x: 166, y: 200}
        - {x: 176, y: 192}
        - {x: 192, y: 175}
        - {x: 200, y: 166}
        - {x: 213, y: 149}
        - {x: 226, y: 128}
        - {x: 235, y: 112}
        - {x: 241, y: 96}
        - {x: 249, y: 74}
        - {x: 253, y: 57}
        - {x: 255, y: 47}
        - {x: 256, y: 40}
        - {x: 256, y: -41}
        - {x: 253, y: -58}
        - {x: 247, y: -81}
        - {x: 235, y: -111}
        - {x: 226, y: -128}
        - {x: 219, y: -139}
        - {x: 213, y: -148}
        - {x: 202, y: -164}
        - {x: 192, y: -174}
        - {x: 186, y: -181}
        - {x: 176, y: -191}
        - {x: 149, y: -213}
        - {x: 132, y: -224}
        - {x: 111, y: -235}
        - {x: 81, y: -247}
        - {x: 74, y: -249}
        - {x: 52, y: -254}
        - {x: 40, y: -256}
        - {x: -40, y: -256}
        - {x: -47, y: -255}
        - {x: -66, y: -251}
        - {x: -81, y: -247}
        - {x: -93, y: -242}
        - {x: -102, y: -240}
        - {x: -111, y: -235}
        - {x: -113, y: -233}
        - {x: -134, y: -223}
        - {x: -149, y: -213}
        - {x: -165, y: -201}
        - {x: -186, y: -181}
        - {x: -195, y: -171}
        - {x: -213, y: -149}
        - {x: -219, y: -140}
        - {x: -226, y: -129}
        - {x: -238, y: -105}
        - {x: -247, y: -81}
        - {x: -251, y: -67}
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    outline:
    - - {x: -254, y: -53}
      - {x: -256, y: -41}
      - {x: -256, y: 40}
      - {x: -253, y: 58}
      - {x: -249, y: 74}
      - {x: -243, y: 91}
      - {x: -239, y: 105}
      - {x: -231, y: 119}
      - {x: -226, y: 129}
      - {x: -213, y: 149}
      - {x: -202, y: 164}
      - {x: -186, y: 182}
      - {x: -177, y: 191}
      - {x: -149, y: 213}
      - {x: -134, y: 223}
      - {x: -111, y: 235}
      - {x: -96, y: 242}
      - {x: -81, y: 247}
      - {x: -74, y: 249}
      - {x: -66, y: 251}
      - {x: -57, y: 253}
      - {x: -41, y: 256}
      - {x: 40, y: 256}
      - {x: 66, y: 251}
      - {x: 74, y: 249}
      - {x: 81, y: 247}
      - {x: 102, y: 240}
      - {x: 111, y: 235}
      - {x: 132, y: 224}
      - {x: 149, y: 213}
      - {x: 166, y: 200}
      - {x: 176, y: 192}
      - {x: 192, y: 175}
      - {x: 200, y: 166}
      - {x: 213, y: 149}
      - {x: 226, y: 128}
      - {x: 235, y: 112}
      - {x: 241, y: 96}
      - {x: 249, y: 74}
      - {x: 253, y: 57}
      - {x: 255, y: 47}
      - {x: 256, y: 40}
      - {x: 256, y: -41}
      - {x: 253, y: -58}
      - {x: 247, y: -81}
      - {x: 235, y: -111}
      - {x: 226, y: -128}
      - {x: 219, y: -139}
      - {x: 213, y: -148}
      - {x: 202, y: -164}
      - {x: 192, y: -174}
      - {x: 186, y: -181}
      - {x: 176, y: -191}
      - {x: 149, y: -213}
      - {x: 132, y: -224}
      - {x: 111, y: -235}
      - {x: 81, y: -247}
      - {x: 74, y: -249}
      - {x: 52, y: -254}
      - {x: 40, y: -256}
      - {x: -40, y: -256}
      - {x: -47, y: -255}
      - {x: -66, y: -251}
      - {x: -81, y: -247}
      - {x: -93, y: -242}
      - {x: -102, y: -240}
      - {x: -111, y: -235}
      - {x: -113, y: -233}
      - {x: -134, y: -223}
      - {x: -149, y: -213}
      - {x: -165, y: -201}
      - {x: -186, y: -181}
      - {x: -195, y: -171}
      - {x: -213, y: -149}
      - {x: -219, y: -140}
      - {x: -226, y: -129}
      - {x: -238, y: -105}
      - {x: -247, y: -81}
      - {x: -251, y: -67}
    physicsShape: []
    bones: []
    spriteID: ec503053dd6e7df46be4468632c36daa
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      CityMovingArea: 21300000
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
