﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    //创建camera collider 的mesh
    public class CreateCameraColliderMesh
    {
        public static void CreateMesh(CameraColliderData data, out Vector3[] meshVertices, out int[] meshIndices)
        {
            meshVertices = null;
            meshIndices = null;
            var outline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            if (data.topOutline == null)
            {
                return;
            }
            bool isCWPolygon = Utils.IsPolygonCW(outline);
            var outline2 = data.topOutline.outline;

            var mesh = new Mesh();
            int n = outline.Count;
            meshVertices = new Vector3[n * 2];
            for (int i = 0; i < n; ++i)
            {
                meshVertices[i] = outline[i];
            }
            for (int i = 0; i < n; ++i)
            {
                meshVertices[i + n] = outline2[i];
            }
            var center = Utils.CalculateCentroid(meshVertices);
            List<int> indices = new List<int>(n * 6);
            for (int i = 0; i < n; ++i)
            {
                int a = i;
                int b = (i + 1) % n;
                int c = (a + n) % (n * 2);
                int d = (b + n) % (n * 2);
                //根据polygon的winding order来生成三角形
                if (isCWPolygon)
                {
                    indices.Add(a);
                    indices.Add(d);
                    indices.Add(c);
                }
                else
                {
                    indices.Add(a);
                    indices.Add(c);
                    indices.Add(d);
                }

                if (isCWPolygon)
                {
                    indices.Add(a);
                    indices.Add(b);
                    indices.Add(d);
                }
                else
                {
                    indices.Add(a);
                    indices.Add(d);
                    indices.Add(b);
                }
            }

            Vector3[] polygonVertices;
            int[] polygonIndices;
            Triangulator.TriangulatePolygon(outline2, out polygonVertices, out polygonIndices);
            for (int i = 0; i < polygonVertices.Length; ++i)
            {
                polygonVertices[i] = new Vector3(polygonVertices[i].x, data.height, polygonVertices[i].z);
            }
            for (int i = 0; i < polygonIndices.Length; ++i)
            {
                var pos = polygonVertices[polygonIndices[i]];
                var realIndex = GetIndex(meshVertices, pos);
                polygonIndices[i] = realIndex;
            }

            indices.AddRange(polygonIndices);
            meshIndices = indices.ToArray();
        }

        static int GetIndex(Vector3[] vertices, Vector3 pos)
        {
            for (int i = 0; i < vertices.Length; ++i)
            {
                if (Utils.Approximately(vertices[i].x, pos.x) &&
                    Utils.Approximately(vertices[i].y, pos.y) &&
                    Utils.Approximately(vertices[i].z, pos.z))
                {
                    return i;
                }
            }
            Debug.Assert(false);
            return -1;
        }
    }
}

#endif