﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public class CameraScroll2D : ZoomActionBase2D
    {
        public CameraScroll2D(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mCurrentPos = currentCameraPos;
            int touchCount = MapTouchManager.touchCount;
            if (mOn && touchCount == 1)
            {
                var touch = MapTouchManager.GetTouch(0);

                Vector2 center = (Vector2)touch.position;
                int centerX = (int)center.x;
                int centerY = (int)center.y;

                if (touch.scrollDelta.y != 0)
                {
                    enabled = true;

                    float delta = Mathf.Sign(touch.scrollDelta.y);

                    var cursorPos = Input.mousePosition;
                    ZoomAt(cursorPos.x, cursorPos.y, -delta * 100);
                }
            }
            else
            {
                Reset();
                isFinished = true;
            }
        }

        public override Vector3 GetTargetPosition()
        {
            return GetCameraPos();
        }

        public override void OnFinishImpl()
        {
        }

        public bool on
        {
            get
            {
                return mOn;
            }
            set
            {
                mOn = value;
                if (value == false)
                {
                    isFinished = true;
                }
            }
        }
        
        bool mOn = true;
        Vector3 mCurrentPos;
    }
}
