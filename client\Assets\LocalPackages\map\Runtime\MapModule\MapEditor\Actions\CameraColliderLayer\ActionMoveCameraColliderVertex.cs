﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionMoveCameraColliderVertex : EditorAction
    {
        public ActionMoveCameraColliderVertex(int layerID, int dataID, int vertexIdx, PrefabOutlineType outlineType)
        {
            mDataID = dataID;
            mLayerID = layerID;
            mVertexIndex = vertexIdx;
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            mStartPosition = layer.GetVertexPos(mDataID, mVertexIndex);
            mEndPosition = mStartPosition;
            mOutlineType = outlineType;
        }

        public void SetEndPosition(Vector3 pos)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            mEndPosition = layer.AdjustVertexPos(mDataID, mVertexIndex, pos);
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetVertexPosition(mDataID, mVertexIndex, mEndPosition);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.SetVertexPosition(mDataID, mVertexIndex, mStartPosition);
            return true;
        }

        public bool isMoved { get { return mStartPosition != mEndPosition; } }

        public override string description
        {
            get
            {
                return string.Format("{0}, idx: {1}, start: {2}, end: {3}", GetType().Name, mVertexIndex, mStartPosition.ToString(), mEndPosition.ToString());
            }
        }

        Vector3 mEndPosition;
        Vector3 mStartPosition;
        int mDataID;
        int mLayerID;
        int mVertexIndex;
        PrefabOutlineType mOutlineType;
    }
}

#endif
