﻿using System.Collections.Generic;
using UnityEngine;

 
 
public class BuildingUpgrade
{
    public long buildingID;//建筑ID
    public int locationID;//建筑位置ID
    public int level;//建筑等级
}
public class BeatMonster
{
    public int monsterType;//怪物类型 1 普通 2 特殊
    public long monsterID;//怪物ID
    public string locationID;//怪物坐标
    public int level;//怪物等级
    public int battleType;//战斗类型 1 普通战斗 2 集结战斗
    public int result;//打怪结果 0 失败 1 成功
    public int isFirst;//是否首次 0 非首次 1 首次
}
public class BuildingUpgradeEvent1
{
    public long goldNum;//金币数量
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮

}
public class BuildingUpgradeEvent2
{
    public long goldNum;//金币数量
    public int tabID;//页签ID
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent3
{
    public long goldNum;//金币数量
    public long buildID;//建筑ID
    public long upgradeTime;//升级所需时间
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent4
{
    public long goldNum;//金币数量
    public long upgradeTime;//升级剩余时间
    public long itemID;//道具ID
    public int itemNum;//道具数量
    public long buildID;//建筑ID
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent5
{
    public long goldNum;//金币数量
    public long upgradeTime;//升级剩余时间
    public long costGold;//消耗金币
    public long buildID;//建筑ID
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent6
{
    public long goldNum;//金币数量
    public long upgradeTime;//升级剩余时间
    public long playerVIPLevel;//玩家VIP等级
    public long buildID;//建筑ID
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent7
{
    public long goldNum;//金币数量
    public long buildID;//建筑ID
    public long upgradeTime;//升级剩余时间
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class BuildingUpgradeEvent8
{
    public long goldNum;//金币数量
    public long buildID;//建筑ID
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮

}
public class HeroEvent1
{
    public int heroID;//英雄ID
}
public class HeroEvent2
{
    public int heroID;//英雄ID
    public int itemID;//道具ID
}
public class HeroEvent3
{
    public int heroID;//英雄ID
    public long costBadgeNum;//消耗徽章数量
}
public class HeroEvent4
{
    public int heroID;//英雄ID
    public int currentHeroDebrisNum;//当前英雄碎片数量
}
public class GetResourceEvent
{
    public int resourceID;//收获资源ID
    public int frontResource;//收获前资源数量
    public int currentResource;//收获资源数量
    public int behindResource;//收获后资源数量
}
public class TrainSoldier1
{
    public int trainSoldierID;//训练士兵ID
    public int trainNum;//训练数量
}
public class TrainSoldie2
{
    public int trainSoldierID;//训练士兵ID
    public int trainNum;//训练数量
    public int costGold;//消耗金币
}
public class TrainSoldie3
{
    public long useSpeedUpItemID;//使用加速道具ID
    public long beforeSpeedUpTime;//加速前剩余时间
    public long afterSpeedUpTime;//加速前剩余时间
    public long useSpeedUpItemNum;//加速道具数量
}
public class JoinAllianceEvent
{
    public long goldNum;//金币余额    
}
public class TaskEvent1
{
    public int taskID;//任务ID
}
public class TaskEvent2
{
    public int taskID;//任务ID
    public int taskProgress;//任务进度
}
public class TaskEvent3
{
    public int chapterID;//章节id
}
public class TaskEvent4
{
    public long taskId;//任务ID
    public int taskSequenceID;//任务顺序ID
    public long startTime;//任务开始时间戳
    public long endTime;//任务结束时间戳
    public long costTime;//完成任务所耗时长
}
public class PurchaseEvent
{
    public int GiftName;//礼包名称
}
public class VipGiftBagEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class NormalGiftBagEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class SubscriptionEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class MonthCardWeekCardChargeEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class GrowUpChargeEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class FirstActivityChargeEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}
public class BattlePassBuyBadgeEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
    public int battlePassType;//徽章类型
    public int battlePassLevel;//battlePass等级
}
public class ContinuousChargeEvent
{
    public long giftId;//礼包id
    public long goldNum;//当前金币数
    public long payTime;//购买时间
}   
public class ScoutCampStartEvent
{
    public string targetPos;//目标领地坐标
    public int targetLevel;//目标等级
    public long targetPlayerID;//目标领领主ID 
    public long marchTime;//行军时间
    public long costFood;//消耗粮食

}
public class ScoutCampArriveEvent
{
    public string targetPos;//目标领地坐标
    public int targetLevel;//目标等级
    public long targetPlayerID;//目标领领主ID s
}
public class BeforeResource
{
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class GetResource
{
    public long wood;//木
    public long stone;//石
    public long iron;//铁
    public long food;//粮
}
public class GameserverConnectFailEvent
{
    public string ErrorCode;//错误信息
    public string loginState;//登录状态
}
public class GameserverConnectFailEvent1
{
    public string ErrorMassage;//错误信息   
    public string loginState;//登录状态 
}
public class GameRetryCountEvent
{
    public int RetryCount;//重试次数
    public string loginState;//登陆状态
}
public class TutorialSkip
{
    public string id;//跳过的id
}
public class LoginState
{
    public string loginState;//登录状态
}
public class GetbundleManifestFail
{
    public int RetryCount;//重试次数
    public string ErrorMassage;//错误信息   
    public string loginState;//登录状态 
}

public class GetbundleManifestSuccess
{
    public string AssetUrl;// 
    public string loginState;//登录状态 
}

public class StartLuaCore
{
    public string loginState;//登录状态 
}
public class StartLua
{
    public string loginState;//登录状态 
}
public class StartLuaSuccess
{
    public string loginState;//登录状态 
}
public class StartLuaFail
{
    public string loginState;//登录状态 
}
public class RequestOderId
{
    public string TalkId;//唯一码
    public int GiftId;//礼包id
    public int GiftType;//礼包类型
}
public class RequestOderIdSuccess
{
    public string TalkId;//唯一码
    public string Oderid;//订单号
    public int GiftId;//礼包id
    public int GiftType;//礼包类型
}
public class RequestOderIdFail
{
    public string TalkId;//唯一码
    public int GiftId;//礼包id
    public int GiftType;//礼包类型
    public string ErrorMsg;//错误信息
}
public class StartPaySuccess
{
    public string TalkId;//唯一码
    public string Oderid;//订单号
    public int GiftId;//礼包id
    public int GiftType;//礼包类型
}
public class StartPayFail
{
    public string TalkId;//唯一码
    public string Oderid;//订单号
    public int GiftId;//礼包id
    public int GiftType;//礼包类型
    public string ErrorMsg;//错误信息
    public string StateCode;//错误码
    public string GoogleErrorCode;//Google错误码
}
public class CmdAndTime
{
    public string cmd;
    public string time;
    public int status;//接收状态 发送时候为0
}