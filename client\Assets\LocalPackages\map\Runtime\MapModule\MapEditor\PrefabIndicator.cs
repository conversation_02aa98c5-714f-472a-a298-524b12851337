﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;


namespace TFW.Map
{
    public class PrefabIndicator
    {
        public PrefabIndicator(bool hideObject)
        {
            mHideGameObject = hideObject;
        }

        public void SetPrefab(GameObject prefab, Transform parent)
        {
            if (prefab != mPrefab)
            {
                mPrefab = prefab;
                if (mPrefabInstance != null)
                {
                    GameObject.DestroyImmediate(mPrefabInstance);
                    mPrefabInstance = null;
                }

                if (prefab != null)
                {
                    mPrefabInstance = GameObject.Instantiate<GameObject>(prefab);
                    if (mHideGameObject)
                    {
                        Utils.HideGameObject(mPrefabInstance);
                    }
                    mPrefabInstance.name = MapCoreDef.MAP_PREFAB_INDICATOR_NAME;
                    mPrefabInstance.transform.parent = parent;
                    mPrefabInstance.SetActive(false);
                    mBounds = GameObjectBoundsCalculator.CalculateRect(mPrefabInstance);
                }
            }
        }

        public void OnDestroy()
        {
            if (mPrefabInstance != null)
            {
                GameObject.DestroyImmediate(mPrefabInstance);
                mPrefabInstance = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.position = pos;
            }
        }

        public void SetRotation(Quaternion rot)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.rotation = rot;
            }
        }

        public void SetYRotation(float yRotation)
        {
            if (mPrefabInstance != null)
            {
                var originalRotation = mPrefabInstance.transform.rotation.eulerAngles;
                mPrefabInstance.transform.rotation = Quaternion.Euler(originalRotation.x, yRotation, originalRotation.z);
            }
        }

        public void SetActive(bool active)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.SetActive(active);
            }
        }

        public GameObject prefabInstance { get { return mPrefabInstance; } }
        public GameObject prefab { get { return mPrefab; } }
        public Rect bounds { get { return mBounds; } }

        GameObject mPrefab;
        GameObject mPrefabInstance;
        bool mHideGameObject;
        Rect mBounds;
    }
}

#endif