﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;
using System.Text;


namespace TFW.Map
{
    public partial class CameraDrag : CameraAction
    {
        public enum CameraState
        {
            Invalid,
            Dragging,
            MovingByInertia,
            SlowMovement,
            BouncingBack,
            //only works in editor
            Rotating,
        }

        class TouchItem
        {
            public float movedDistance;
            public float timeStamp;
            public Vector2 screenPos;
        }

        public CameraDrag(CameraActionType type, float inertiaDuration, float endingDuration) :
            base(type)
        {
            mSlowTime = endingDuration;
            mInertiaDuration = inertiaDuration;
            moveAxis = CameraMoveAxis.Horizontal;
            ownAxis = CameraMoveAxis.Horizontal;
            on = true;
        }

        public void TurnOn(bool on)
        {
            this.on = on;
            if (!on)
            {
                OnFinish();
            }
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mTargetPosition = currentCameraPos;
            mDragMoveOffset = Vector3.zero;
            int touchCount = MapTouchManager.touchCount;
            
           
            if (on && touchCount == 1 && mCameraState != CameraState.BouncingBack)
            {
                var touch = MapTouchManager.GetTouch(0);

                bool rightButton = false;
#if UNITY_EDITOR || UNITY_STANDALONE
                //unity remote
                //if (touch.fingerId != 0)
                //{
                //    return;
                //}

                if (touch.fingerId == 1)
                {
                    //dragging
                    rightButton = true;
                }
#endif

                var camera = Map.currentMap.camera;
                Vector3 lastTouchWorldPosition = Utils.FromScreenToWorldPosition(touch.lastPosition, camera.firstCamera);
                Vector3 touchWorldPosition = Utils.FromScreenToWorldPosition(touch.position, camera.firstCamera);

                var delta = touchWorldPosition - lastTouchWorldPosition;
                float distance = delta.magnitude;
                //强制刷新高度（不然会由于精度问题，导致相机高度抖动的情况）
                delta.y = 0;
#if UNITY_EDITOR
                Vector2 dir = touch.position - touch.lastPosition;
                float movedDistance0 = dir.magnitude;
                mDragMoveScreenOffset = Utils.CalculateWorldDistance(movedDistance0);
                mRotateDragDir = dir.x > 0 ? 1 : -1;
#endif
                mDragMoveOffset = -delta;

                if (rightButton)
                {
                    OnTouchRightButton(touch);
                }
                else
                {
                    if (touch.state == MapTouchState.Touch)
                    {
                        mTouchStartWorldPos = touchWorldPosition;

                        mCameraState = CameraState.Dragging;
                        mInertiaVelocity = Vector3.zero;

                        mCanDrag = !touch.blockUIDrag;

                        ClearTouchies();

                        if (rightButton)
                        {
                            mCameraState = CameraState.Rotating;
                        }
                    }
                    else if (touch.state == MapTouchState.Touching)
                    {
                        mCameraState = CameraState.Dragging;
                        var touchDelta = touch.position - touch.lastPosition;
                        if (Mathf.Abs(touchDelta.x) > 5 || Mathf.Abs(touchDelta.y) > 5)
                        {
                            mDragDir = mTouchStartWorldPos - touchWorldPosition;
                            mDragDir.Normalize();
                            var item = mTouchItemPool.Require();
                            item.movedDistance = distance;
                            item.timeStamp = Time.time;
                            item.screenPos = touch.position;
                            mTouchies.Add(item);
                        }
                        if (mCanDrag)
                        {
                            enabled = true;
                        }
                        else
                        {
                            OnFinish();
                        }

                        if (mBounceAreaRange != 0)
                        {
                            MapCameraMgr.bounceRange = mBounceAreaRange;
                        }

                        if (rightButton)
                        {
                            mCameraState = CameraState.Rotating;
                        }
                    }
                    else if (touch.state == MapTouchState.Release)
                    {
                        bool bounce = CheckBouncing(currentCameraPos);
                        if (!bounce)
                        {
                            float timeInterval = 0.1f;
                            float curTime = Time.time;
                            while (mTouchies.Count > 0)
                            {
                                if (mTouchies[0].timeStamp >= curTime - timeInterval)
                                {
                                    break;
                                }
                                var item = mTouchies[0];
                                mTouchItemPool.Release(item);
                                mTouchies.RemoveAt(0);
                            }

                            float movedDistance = 0;
                            float totalTime = 0;
                            if (mTouchies.Count > 0)
                            {
                                for (int i = 0; i < mTouchies.Count; ++i)
                                {
                                    movedDistance += mTouchies[i].movedDistance;
                                }
                                totalTime = curTime - mTouchies[0].timeStamp;
                            }

                            //释放touch时
                            if (totalTime > 0 && mTouchies.Count > 1)
                            {
                                float inertialSpeed = movedDistance / totalTime;

                                mInertiaVelocity = mDragDir * inertialSpeed;
                                mInertiaSpeed = mInertiaVelocity.magnitude;
                                mInertiaVelocity.Normalize();
                                mInertiaElapsedTime = 0;
                                mCameraState = CameraState.MovingByInertia;
                                mSlowSpeed = mInertiaSpeed * 0.01f;
                            }
                            else
                            {
                                OnFinish();
                            }
                        }
                    }
                }
            }
            else if (touchCount == 2)
            {
                OnFinish();
            }
        }

        public override Vector3 GetTargetPosition()
        {
            Vector3 cameraTargetPos = mTargetPosition;
            if (on)
            {
                if (mCameraState != CameraState.Rotating)
                {
                    MapCameraMgr.isDragging = true;
                }
                else
                {
                    MapCameraMgr.isDragging = false;
                }
                if (mCameraState == CameraState.Dragging)
                {
                    //拖动相机中
                    cameraTargetPos = MapCameraMgr.updatedCameraPosition + mDragMoveOffset;
                    //UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos 01:{cameraTargetPos.x},{cameraTargetPos.z}");
                    if (mDragMoveOffset != Vector3.zero)
                    {
                        MapCameraMgr.UpdateRotateCenter();
                    }
                }
                else if (mCameraState == CameraState.MovingByInertia)
                {
                    //惯性移动
                    cameraTargetPos = MovingByInertia();
                    MapCameraMgr.UpdateRotateCenter();
                    //UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos 02:{cameraTargetPos.x},{cameraTargetPos.z}");
                }
                else if (mCameraState == CameraState.SlowMovement)
                {
                    cameraTargetPos = SlowMovement();
                    MapCameraMgr.UpdateRotateCenter();
                   // UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos 03:{cameraTargetPos.x},{cameraTargetPos.z}");
                }
                else if (mCameraState == CameraState.BouncingBack)
                {
                    cameraTargetPos = Bounce();
                    MapCameraMgr.UpdateRotateCenter();
                }
                else if (mCameraState == CameraState.Rotating)
                {
                    cameraTargetPos = OnRotate(cameraTargetPos, mRotateDragDir, mDragMoveScreenOffset);
                    //UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos 04:{cameraTargetPos.x},{cameraTargetPos.z}");
                }
            }
            if (mEnableClampRange)
            {
                cameraTargetPos.x = Mathf.Clamp(cameraTargetPos.x, mRange.xMin, mRange.xMax);
                cameraTargetPos.z = Mathf.Clamp(cameraTargetPos.z, mRange.yMin, mRange.yMax);
            }


            //UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos:{cameraTargetPos.x},{cameraTargetPos.z}");

            return cameraTargetPos;
        }

        Vector3 MovingByInertia()
        {
            mInertiaElapsedTime += Time.deltaTime;
            bool finished = false;
            if (mInertiaElapsedTime >= mInertiaDuration)
            {
                mInertiaElapsedTime = mInertiaDuration;
                finished = true;
            }

            float t = Mathf.Clamp01(mInertiaElapsedTime / mInertiaDuration);
            t = Utils.EaseOutExpo(0, 1, t);
            Vector3 velocity = new Vector3();
            float speed = Mathf.Lerp(mInertiaSpeed, mSlowSpeed, t);
            velocity = mInertiaVelocity * speed;
            var cameraTargetPos = MapCameraMgr.updatedCameraPosition + velocity * Time.deltaTime;

            if (finished)
            {
                mCameraState = CameraState.SlowMovement;
                mInertiaElapsedTime = 0;
            }

            return cameraTargetPos;
        }

        Vector3 SlowMovement()
        {
            mInertiaElapsedTime += Time.deltaTime;
            bool finished = false;
            if (mInertiaElapsedTime >= mSlowTime)
            {
                mInertiaElapsedTime = mSlowTime;
                finished = true;
            }

            float t = Mathf.Clamp01(mInertiaElapsedTime / mSlowTime);
            float speed = Utils.EaseOutQuad(mSlowSpeed, 0, t);
            Vector3 velocity = mInertiaVelocity * speed;

            var cameraTargetPos = MapCameraMgr.updatedCameraPosition + velocity * Time.deltaTime;
            //UnityEngine.Debug.Log($"[{nameof(CameraDrag)}] cameraTargetPos 03-0:{cameraTargetPos.x},{cameraTargetPos.z},{MapCameraMgr.updatedCameraPosition.x},{MapCameraMgr.updatedCameraPosition.z},{velocity.x},{velocity.z},{Time.deltaTime} ");
            if (finished)
            {
                OnFinish();
            }
            return cameraTargetPos;
        }

        public override void OnFinishImpl()
        {
            enabled = false;
            MapCameraMgr.isDragging = false;
            mInertiaElapsedTime = 0;
            mInertiaVelocity = Vector3.zero;
            isFinished = true;
            mDragDir = Vector3.zero;
            mDragMoveOffset = Vector3.zero;
            mRotateDragDir = 0;

            mCameraState = CameraState.Invalid;
        }

        void ClearTouchies()
        {
            for (int i = 0; i < mTouchies.Count; ++i)
            {
                mTouchItemPool.Release(mTouchies[i]);
            }
            mTouchies.Clear();
        }

        public void SetRange(Rect range)
        {
            mRange = range;
            if (range.width > 0 && range.height > 0)
            {
                mEnableClampRange = true;
            }
            else
            {
                mEnableClampRange = false;
            }
        }

        bool CheckBouncing(Vector3 cameraPos)
        {
            bool shouldBounce = false;
            var map = Map.currentMap;
            var camera = map.camera;
            var viewCenter = map.GetViewCenterFromCamera(camera.transform.forward, cameraPos);
            var minPos = map.origin;
            var maxPos = minPos + new Vector3(map.mapWidth, 0, map.mapHeight);
            if ((viewCenter.x < minPos.x || viewCenter.z < minPos.z || viewCenter.x > maxPos.x || viewCenter.z > maxPos.z) && MapCameraMgr.bounceRange != 0)
            {
                shouldBounce = true;
                mCameraState = CameraState.BouncingBack;
                mBounceStartPos = cameraPos;
                mBounceSpeed = Mathf.Max(mBounceMinSpeed, (mBounceEndPos - mBounceStartPos).magnitude / mBounceDuration);
                Vector3 intersection = Utils.ClampPoint(viewCenter, minPos, maxPos);
                mBounceEndPos = map.CalculateCameraPositionFromLookAtPosition(intersection.x, intersection.z, cameraPos.y);

                MapCameraMgr.EnableCameraZoom(false);
            }
            else
            {
                MapCameraMgr.bounceRange = 0;
            }

            return shouldBounce;
        }

        Vector3 Bounce()
        {
            if (mBounceStartPos == mBounceEndPos)
            {
                mCameraState = CameraState.Invalid;
                MapCameraMgr.bounceRange = 0;
                MapCameraMgr.EnableCameraZoom(true);
            }
            mBounceStartPos = Vector3.MoveTowards(mBounceStartPos, mBounceEndPos, mBounceSpeed * Time.deltaTime);
            return mBounceStartPos;
        }

        public Rect GetRange()
        {
            return mRange;
        }

        public override string GetDebugMessage()
        {
            StringBuilder builder = new StringBuilder();
            builder.AppendLine($"enabled: {enabled}");
            builder.AppendLine($"on: {on}");
            builder.AppendLine($"state: {mCameraState}");
            builder.AppendLine($"clamp range: {mEnableClampRange}");
            builder.AppendLine($"blocked by UI: {!mCanDrag}");
            return builder.ToString();
        }

        public bool on
        {
            set
            {
                mOn = value;
                if (!mOn)
                {
                    isFinished = true;
                }
            }
            get
            {
                return mOn;
            }
        }

        public float bounceAreaRange
        {
            get { return mBounceAreaRange; }
            set { mBounceAreaRange = value; }
        }

        public float bounceDuration
        {
            get { return mBounceDuration; }
            set { mBounceDuration = value; }
        }

        public float bounceMinimumSpeed
        {
            get { return mBounceMinSpeed; }
            set { mBounceMinSpeed = value; }
        }

        public CameraState state { get { return mCameraState; } }

        CameraState mCameraState = CameraState.Invalid;
        float mInertiaDuration;
        float mInertiaElapsedTime;
        float mSlowTime;
        float mSlowSpeed;
        float mInertiaSpeed;
        Vector3 mInertiaVelocity;
        Vector3 mTargetPosition;
        Vector3 mTouchStartWorldPos;
        Vector3 mDragMoveOffset;
        bool mCanDrag = false;
        bool mEnableClampRange = false;
        bool mOn = true;
        //将相机xz移动范围限制在mRange里
        Rect mRange;

        Vector3 mDragDir;
        Vector3 mBounceStartPos;
        Vector3 mBounceEndPos;
        float mBounceDuration = 0.3f;
        float mBounceSpeed;
        float mBounceMinSpeed = 50.0f;
        float mBounceAreaRange = 0;
        List<TouchItem> mTouchies = new List<TouchItem>(100);
        ObjectPool<TouchItem> mTouchItemPool = new ObjectPool<TouchItem>(100, () => { return new TouchItem(); });
    }
}
