﻿ 



 
 



/*
 * created by wzw at 2019/12/4
 */

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public class CameraColliderLayerEventHandlers
    {
        public System.Action<int> onDeleteCollision;
        public System.Action<int> onAddCollision;
        public System.Action<int> onMoveCollision;
        public System.Action<int> onRotateCollision;
        public System.Action<int, int> onMoveVertex;
        public System.Action<int, int> onInsertVertex;
        public System.Action<int, int> onRemoveVertex;
        public System.Action<int, PrefabOutlineType> onOutlineChanged;
    }

    public partial class CameraColliderLayer : MapLayerBase
    {
        public CameraColliderLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.CameraColliderLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }

            mLayerData = new CameraColliderLayerData(header, config, map);
            mLayerView = new CameraColliderLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);

            mLayerData.isLoading = true;
            if (sourceLayer.colliders != null)
            {
                int n = sourceLayer.colliders.Length;
                for (int i = 0; i < n; ++i)
                {
                    var model = sourceLayer.colliders[i] as config.CameraColliderData;
                    var bottomVertices = Utils.ConvertToVector3List(model.bottomOutline);
                    var topVertices = Utils.ConvertToVector3List(model.topOutline);
                    for (int t = 0; t < topVertices.Count; ++t)
                    {
                        topVertices[t] = new Vector3(topVertices[t].x, model.height, topVertices[t].z);
                    }
                    var collisionData = new CameraColliderData(model.id, map, bottomVertices, topVertices, model.radius, model.height, model.vertices, model.indices);
                    mLayerData.AddObjectData(collisionData);
                }
            }
            mLayerData.isLoading = false;

            this.displayType = PrefabOutlineType.NavMeshObstacle;
            this.displayVertexRadius = sourceLayer.displayVertexRadius;

            map.AddMapLayer(this);
        }

        public void SetEventHandlers(CameraColliderLayerEventHandlers eventHandlers)
        {
            mEventHandlers = eventHandlers;
        }

        public CameraColliderLayerEventHandlers GetEventHandlers()
        {
            return mEventHandlers;
        }

        public void InsertCollisionVertex(PrefabOutlineType type, int dataID, int index, Vector3 vertex)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.InsertVertex(type, index, vertex);
                mLayerView.InsertVertex(type, objectData, index, vertex);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onInsertVertex != null)
                    {
                        mEventHandlers.onInsertVertex(dataID, index);
                    }
                }
            }
        }

        public void RemoveCollsionVertex(PrefabOutlineType type, int dataID, int index)
        {
            var riverData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (riverData != null)
            {
                var pos = riverData.GetVertexPos(type, index);
                riverData.RemoveVertex(type, index);
                mLayerView.RemoveVertex(type, riverData, index);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onRemoveVertex != null)
                    {
                        mEventHandlers.onRemoveVertex(dataID, index);
                    }
                }
            }
        }

        public void SetVertexPosition(int dataID, int vertexIndex, Vector3 pos)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.SetVertexPosition(vertexIndex, pos);
                mLayerView.UpdateVertex(dataID, vertexIndex, pos);
            }

            if (mEventHandlers != null)
            {
                if (mEventHandlers.onMoveVertex != null)
                {
                    mEventHandlers.onMoveVertex(dataID, vertexIndex);
                }
            }
        }

        public void RotateObject(int dataID, float degree)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.Rotate(degree);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onRotateCollision != null)
                    {
                        mEventHandlers.onRotateCollision(dataID);
                    }
                }
            }
        }

        public void MoveObject(PrefabOutlineType type, int dataID, Vector3 offset)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.Move(type, offset);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onMoveCollision != null)
                    {
                        mEventHandlers.onMoveCollision(dataID);
                    }
                }
            }
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            var removedObjectIDs = mLayerData.Resize(newWidth, newHeight);
            if (removedObjectIDs != null && removedObjectIDs.Count > 0)
            {
                for (int i = 0; i < removedObjectIDs.Count; ++i)
                {
                    mLayerView.RemoveObjectView(removedObjectIDs[i]);
                }
                return true;
            }
            return false;
        }

        public void ClearOutline(PrefabOutlineType type, int dataID)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.ClearOutline(type);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }
        }

        public void SetOutline(PrefabOutlineType type, int dataID, List<Vector3> vertices)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.SetOutline(type, vertices);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }
        }

        public void ClearAllOutlines(PrefabOutlineType type)
        {
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var collision = allObjects[i] as CameraColliderData;
                collision.ClearOutline(type);
                mLayerView.UpdateView(collision.id);
            }
        }

        public void ExpandOutline(PrefabOutlineType type, int dataID, float radius)
        {
            var objectData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (objectData != null)
            {
                objectData.ExpandOutline(type, radius);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }
        }

        public void SetVertexDisplayRadius(float radius)
        {
            displayVertexRadius = radius;
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var obj = allObjects[i] as CameraColliderData;
                obj.displayRadius = radius;
            }

            var allViews = mLayerView.allViews;
            foreach (var p in allViews)
            {
                var view = (p.Value as CameraColliderView);
                view.SetVertexDisplayRadius(radius);
            }
        }

        public void SetSelected(int dataID, bool selected)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (collisionData != null)
            {
                collisionData.isSelected = selected;
            }
        }

        public void SetIntersectedWithObstacles(int dataID, bool hitObstacle)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (collisionData != null)
            {
                collisionData.hitObstacles = hitObstacle;
            }
        }

        public void UpdateColor(PrefabOutlineType type)
        {
            var allObjects = mLayerData.objects;
            foreach (var obj in allObjects)
            {
                var collisionData = obj.Value as CameraColliderData;
                if (collisionData.hitObstacles)
                {
                    SetDisplayColor(type, collisionData.id, Color.magenta);
                }
                else if (!collisionData.IsSimplePolygon(type))
                {
                    SetDisplayColor(type, collisionData.id, Color.red);
                }
                else if (collisionData.isSelected)
                {
                    SetDisplayColor(type, collisionData.id, Color.green);
                }
                else
                {
                    SetDisplayColor(type, collisionData.id, new Color(1, 0.5f, 0, 1.0f));
                }
            }
        }

        void SetDisplayColor(PrefabOutlineType type, int dataID, Color color)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                var collisionView = (view as CameraColliderView);
                collisionView.SetColor(type, color);
            }
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);

                    if (mEventHandlers != null)
                    {
                        if (mEventHandlers.onAddCollision != null)
                        {
                            mEventHandlers.onAddCollision(objectID);
                        }
                    }

                    return true;
                }
            }
            return false;
        }

        public int GetObjectIDByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    return p.Value.objectDataID;
                }
            }
            return 0;
        }

        public void RemoveObjectByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    RemoveObject(p.Value.objectDataID);
                    break;
                }
            }
        }

        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onDeleteCollision != null)
                    {
                        mEventHandlers.onDeleteCollision(objectDataID);
                    }
                }

                mLayerView.RemoveObjectView(objectDataID);
            }
            return success;
        }

        public void RemoveAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }
        }

        public void ShowObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, 0);
            }
        }

        public void HideObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, false, 0);
            }
        }

        public GameObject GetObjectGameObject(int dataID)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();

            ShowOutline(PrefabOutlineType.NavMeshObstacle);
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var collider = p.Value as CameraColliderData;
                if (collider.HasMeshData())
                {
                    CreateCollider(p.Value.GetEntityID());
                }
            }
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        public void Traverse(System.Func<IMapObjectData, bool> visitFunc)
        {
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                if (visitFunc(p.Value))
                {
                    break;
                }
            }
        }

        public bool IntersectWithPolygon(PrefabOutlineType type, List<Vector3> polygon)
        {
            return mLayerData.IntersectWithPolygon(type, polygon);
        }

        //for debug
        public void CheckCollision(PrefabOutlineType type, System.Action<PolygonObjectData, PolygonObjectData> onCollide)
        {
            mLayerData.CheckCollision(type, onCollide);
        }

        public void HideOutline()
        {
            mLayerView.HideOutline();
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            mLayerView.ShowOutline(type);
        }

        public List<Vector3> GetOutline(int dataID, PrefabOutlineType type)
        {
            var river = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (river != null)
            {
                return river.GetOutlineVertices(type);
            }
            return null;
        }

        public CameraColliderData GetCameraCollider(int dataID)
        {
            return mLayerData.GetObjectData(dataID) as CameraColliderData;
        }

        public int GetVertexIndex(PrefabOutlineType type, int dataID, Vector3 pos)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                return data.GetVertexIndex(type, pos);
            }
            return -1;
        }

        public Vector3 GetVertexPos(int dataID, int vertexIndex)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                return data.GetVertexPos(vertexIndex);
            }
            return Vector3.zero;
        }

        public void SetTopOutline(int dataID, List<Vector3> outline, float height)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                data.SetTopOutline(outline, height);
                var view = mLayerView.GetObjectView(dataID) as CameraColliderView;
                view.CreateTopOutline(data);
            }
        }

        public void CreateTopOutline(int dataID, float height)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                data.CreateTopOutline(height);
                var view = mLayerView.GetObjectView(dataID) as CameraColliderView;
                view.CreateTopOutline(data);
            }
        }

        public void CreateCollider(int dataID)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                data.CreateCollider();
                var view = mLayerView.GetObjectView(dataID) as CameraColliderView;
                view.CreateCollider(data);
            }
        }

        public void SetCollider(int dataID, Vector3[] vertices, int[] indices)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                data.SetColliderMeshData(vertices, indices);
                var view = mLayerView.GetObjectView(dataID) as CameraColliderView;
                view.CreateCollider(data);
            }
        }

        public Vector3 AdjustVertexPos(int dataID, int index, Vector3 pos)
        {
            var data = mLayerData.GetObjectData(dataID) as CameraColliderData;
            if (data != null)
            {
                return data.AdjustVertexPos(index, pos);
            }
            return pos;
        }

        //获取所有collider合并后的mesh
        public void GetCombinedMesh(out Vector3[] vertices, out int[] indices)
        {
            MeshCombiner combiner = new MeshCombiner();
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                var collider = p.Value as CameraColliderData;
                combiner.AddMesh(collider.vertices, collider.indices, Matrix4x4.identity);
            }
            combiner.Combine(0.001f, out vertices, out indices);
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public CameraColliderLayerData layerData { get { return mLayerData; } }
        public CameraColliderLayerView layerView { get { return mLayerView; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public PrefabOutlineType displayType { set; get; }
        public float displayVertexRadius { get; internal set; }
        public override int lodCount => mLayerData.lodCount;
        protected CameraColliderLayerData mLayerData;
        protected CameraColliderLayerView mLayerView;
        CameraColliderLayerEventHandlers mEventHandlers;
    }
}

#endif