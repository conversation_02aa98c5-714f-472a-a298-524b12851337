%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1629798371629279392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8221337415985204030}
  - component: {fileID: 2716750727511987802}
  m_Layer: 0
  m_Name: ObstacleOutline
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8221337415985204030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629798371629279392}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2716750727511987802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1629798371629279392}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -41.49638, y: 0, z: -33.612026}
    - {x: -20.431332, y: 0, z: -19.0458}
    - {x: 15.61297, y: 0, z: -3.0605927}
    - {x: 45.792355, y: 0, z: -4.973768}
    - {x: 50.985867, y: 0, z: -13.668079}
    - {x: 44.856445, y: 0, z: -19.84024}
    - {x: 30.164988, y: 0, z: -18.51589}
    - {x: -31.421703, y: 0, z: -48.21972}
    - {x: -37.64002, y: 0, z: -46.4913}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -27.9392, y: 0, z: -49.0039}
    - {x: -27.7648, y: 0, z: -48.9508}
    - {x: 45.7962, y: 0, z: -21.5524}
    - {x: 46.2079, y: 0, z: -21.3201}
    - {x: 52.7387, y: 0, z: -16.1349}
    - {x: 53.2629, y: 0, z: -14.2621}
    - {x: 50.6115, y: 0, z: -7.088}
    - {x: 50.1438, y: 0, z: -6.4336}
    - {x: 46.2998, y: 0, z: -3.3012}
    - {x: 45.4735, y: 0, z: -2.9632}
    - {x: 27.7284, y: 0, z: -1.3096}
    - {x: 27.6465, y: 0, z: -1.3043}
    - {x: 20.354, y: 0, z: -1.0256}
    - {x: 20.0776, y: 0, z: -1.0405}
    - {x: 13.0752, y: 0, z: -2.0716}
    - {x: 12.7953, y: 0, z: -2.1408}
    - {x: -5.3886, y: 0, z: -8.5372}
    - {x: -5.55, y: 0, z: -8.6048}
    - {x: -22.1476, y: 0, z: -16.7223}
    - {x: -22.3697, y: 0, z: -16.8557}
    - {x: -34.1062, y: 0, z: -25.3678}
    - {x: -34.1202, y: 0, z: -25.378}
    - {x: -34.9883, y: 0, z: -26.0231}
    - {x: -35.0634, y: 0, z: -26.0826}
    - {x: -42.5467, y: 0, z: -32.423}
    - {x: -43.0635, y: 0, z: -33.3033}
    - {x: -43.3696, y: 0, z: -34.8625}
    - {x: -43.3289, y: 0, z: -35.6099}
    - {x: -40.0246, y: 0, z: -46.196}
    - {x: -39.3511, y: 0, z: -47.058}
    - {x: -34.385, y: 0, z: -50.0816}
    - {x: -33.2114, y: 0, z: -50.2662}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
