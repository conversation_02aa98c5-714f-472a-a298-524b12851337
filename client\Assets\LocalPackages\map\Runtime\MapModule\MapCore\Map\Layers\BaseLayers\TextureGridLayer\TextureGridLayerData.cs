﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TextureGridData
    {
        public TextureGridData(int id, Color color)
        {
            this.id = id;
            this.color = color;
        }

        public int id;
        public Color color = Color.white;
    }

    //npc刷新区域的地图层
    public class TextureGridLayerData : MapLayerData
    {
        public class Layer
        {
            public Layer(string name, int horizontalTileCount, int verticalTileCount, float tileWidth, float tileHeight, int[,] gridIDs, List<TextureGridData> grids, Vector3 gridStartPos)
            {
                this.name = name;
                this.horizontalTileCount = horizontalTileCount;
                this.verticalTileCount = verticalTileCount;
                this.tileWidth = tileWidth;
                this.tileHeight = tileHeight;
                this.gridIDs = gridIDs;
                this.grids = grids;
                this.gridStartPos = gridStartPos;
            }

            public virtual void OnDestroy()
            {
            }

            public Vector2Int FromWorldPositionToCoordinate1(Vector3 worldPos)
            {
                var localPos = worldPos - gridStartPos;
                return new Vector2Int(Mathf.FloorToInt(localPos.x / tileWidth), Mathf.FloorToInt(localPos.z / tileHeight));
            }

            public Vector3 FromCoordinateToWorldPosition1(int x, int y)
            {
                return new Vector3(x * tileWidth, 0, y * tileHeight) + gridStartPos;
            }

            public Vector3 FromCoordinateToWorldPositionCenter1(int x, int y)
            {
                return new Vector3((x + 0.5f) * tileWidth, 0, (y + 0.5f) * tileHeight) + gridStartPos;
            }

            public string name;
            public int[,] gridIDs;
            public List<TextureGridData> grids;
            public int horizontalTileCount;
            public int verticalTileCount;
            public float tileWidth;
            public float tileHeight;
            public Vector3 gridStartPos;
        }

        public TextureGridLayerData(MapLayerDataHeader header, Map map, List<Layer> layers) : base(header, null, map)
        {
            mLayers = layers;
        }

        public override void OnDestroy()
        {
            for (int i = 0; i < mLayers.Count; ++i)
            {
                mLayers[i].OnDestroy();
            }
        }

        int GetGrid1(int layerIdx, float x, float y)
        {
            var layer = mLayers[layerIdx];

            int col = Mathf.FloorToInt((x - layer.gridStartPos.x) / layer.tileWidth);
            int row = Mathf.FloorToInt((y -layer.gridStartPos.z) / layer.tileHeight);
            if (row >= 0 && row < layer.verticalTileCount && col >= 0 && col < layer.horizontalTileCount)
            {
                return row * layer.horizontalTileCount + col;
            }
            return -1;
        }

        public Vector2Int GetGridIndex(int layerIdx, Vector3 worldPos)
        {
            int idx = GetGrid1(layerIdx, worldPos.x, worldPos.z);
            var layer = mLayers[layerIdx];
            int rows = idx / layer.horizontalTileCount;
            int cols = idx % layer.horizontalTileCount;
            return new Vector2Int(cols, rows);
        }

        public Vector3 GetGridPosition(int layerIdx, int gridIdx)
        {
            var layer = mLayers[layerIdx];
            int rows = gridIdx / layer.horizontalTileCount;
            int cols = gridIdx % layer.horizontalTileCount;
            return new Vector3((cols + 0.5f) * layer.tileWidth, 0, (rows + 0.5f) * layer.tileHeight);
        }

        public Vector3[] GetGridRect(int layerIdx, int gridIdx)
        {
            var layer = mLayers[layerIdx];
            int rows = gridIdx / layer.horizontalTileCount;
            int cols = gridIdx % layer.horizontalTileCount;
            return new Vector3[]
            {
                new Vector3(cols * layer.tileWidth, 0, rows * layer.tileHeight),
                new Vector3((cols + 1) * layer.tileWidth, 0, rows * layer.tileHeight),
                new Vector3((cols + 1) * layer.tileWidth, 0, (rows + 1) * layer.tileHeight),
                new Vector3(cols * layer.tileWidth, 0, (rows + 1) * layer.tileHeight)
            };
        }

        public TextureGridData FindGrid(int layerIdx, int id)
        {
            var grids = mLayers[layerIdx].grids;
            for (int i = 0; i < grids.Count; ++i)
            {
                if (grids[i].id == id)
                {
                    return grids[i];
                }
            }
            return null;
        }

        public TextureGridData GetGrid(int layerIdx, int x, int y)
        {
            var layer = mLayers[layerIdx];
            return FindGrid(layerIdx, layer.gridIDs[y, x]);
        }

        public void AddGrid(int layerIdx, TextureGridData grid)
        {
            var layer = mLayers[layerIdx];
            layer.grids.Add(grid);
        }

        public void RemoveGrid(int layerIdx, int index)
        {
            var grids = mLayers[layerIdx].grids;
            if (index >= 0 && index < grids.Count)
            {
                grids.RemoveAt(index);
            }
        }

        public Vector2Int FromWorldPositionToCoordinate1(int layerIdx, Vector3 worldPos)
        {
            var layer = mLayers[layerIdx];
            return layer.FromWorldPositionToCoordinate1(worldPos);
        }

        public Vector3 FromCoordinateToWorldPosition1(int layerIdx, int x, int y)
        {
            var layer = mLayers[layerIdx];
            return layer.FromCoordinateToWorldPosition1(x, y);
        }

        public Vector3 FromCoordinateToWorldPositionCenter1(int layerIdx, int x, int y)
        {
            var layer = mLayers[layerIdx];
            return layer.FromCoordinateToWorldPositionCenter1(x, y);
        }

        public void SetGrid1(int layerIdx, Vector3 pos, int brushSize, int gridID)
        {
            var layer = mLayers[layerIdx];
            Vector2Int coord = layer.FromWorldPositionToCoordinate1(pos);
            int startX = coord.x - brushSize / 2;
            int startY = coord.y - brushSize / 2;
            int endX = startX + brushSize - 1;
            int endY = startY + brushSize - 1;

            int rows = layer.verticalTileCount;
            int cols = layer.horizontalTileCount;
            if (endX < 0 || endY < 0 || startX >= cols || startY >= rows)
            {
                return;
            }

            startX = Mathf.Clamp(startX, 0, cols - 1);
            startY = Mathf.Clamp(startY, 0, rows - 1);
            endX = Mathf.Clamp(endX, 0, cols - 1);
            endY = Mathf.Clamp(endY, 0, rows - 1);

            int width = endX - startX + 1;
            int height = endY - startY + 1;
            var pixels = mColorArrayPool.Rent(width * height);
            int idx = 0;
            var template = FindGrid(layerIdx, gridID);
            Color32 color = new Color32(0, 0, 0, 0);
            if (template != null)
            {
                color = template.color;
            }
            for (int i = startY; i <= endY; ++i)
            {
                for (int j = startX; j <= endX; ++j)
                {
                    layer.gridIDs[i, j] = gridID;
                    pixels[idx] = color;
                    ++idx;
                }
            }
            mOnSetPixels(layerIdx, startX, startY, width, height, pixels);
            mColorArrayPool.Return(pixels);
        }

        public void SetGridData(int layerIdx, int x, int y, int type)
        {
            var layer = mLayers[layerIdx];
            if (x >= 0 && x < layer.horizontalTileCount && y >= 0 && y < layer.verticalTileCount)
            {
                if (layer.gridIDs[y, x] != type)
                {
                    layer.gridIDs[y, x] = type;
                }
            }
        }

        public override bool isGameLayer => false;

        public int[,] GetGridIDs(int layerIdx)
        {
            return mLayers[layerIdx].gridIDs;
        }

        public List<TextureGridData> GetGrids(int layerIdx)
        {
            return mLayers[layerIdx].grids;
        }

        public Layer GetLayer(int layerIdx)
        {
            if (layerIdx >= 0 && layerIdx < mLayers.Count)
            {
                return mLayers[layerIdx];
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom) { return false; }
        public override void RefreshObjectsInViewport() { }
        public override bool Contains(int objectID) { return false; }

        public void SetOnPixelsChangeCallback(System.Action<int, int, int, int, int, Color32[]> onSetPixels)
        {
            mOnSetPixels = onSetPixels;
        }

        public void SetOnAddLayerCallback(System.Action<Layer> callback)
        {
            mOnAddLayer = callback;
        }

        public void SetOnRemoveLayerCallback(System.Action<int> callback)
        {
            mOnRemoveLayer = callback;
        }

        public bool HasLayer(string name)
        {
            for (int i = 0; i < mLayers.Count; ++i)
            {
                if (mLayers[i].name == name)
                {
                    return true;
                }
            }
            return false;
        }

        public int GetLayerIndex(Layer layer)
        {
            for (int i = 0; i < mLayers.Count; ++i)
            {
                if (mLayers[i] == layer)
                {
                    return i;
                }
            }
            return -1;
        }

        public void AddLayer(Layer layer)
        {
            mLayers.Add(layer);
            if (mOnAddLayer != null)
            {
                mOnAddLayer(layer);
            }
        }

        public virtual bool RemoveLayer(int idx)
        {
            if (idx >= 0 && idx < mLayers.Count)
            {
                if (mOnRemoveLayer != null)
                {
                    mOnRemoveLayer(idx);
                }

                mLayers.RemoveAt(idx);
                return true;
            }
            return false;
        }

        public bool RemoveLayer(Layer layer)
        {
            int index = GetLayerIndex(layer);
            if (index >= 0)
            {
                return RemoveLayer(index);
            }
            else
            {
                Debug.Assert(false, "sub layer not found!");
            }

            return false;
        }

        public int layerCount { get { return mLayers.Count; } }

        List<Layer> mLayers = new List<Layer>();
        ArrayPool<Color32> mColorArrayPool = ArrayPool<Color32>.Create();
        System.Action<int, int, int, int, int, Color32[]> mOnSetPixels;
        System.Action<Layer> mOnAddLayer;
        System.Action<int> mOnRemoveLayer;
    }
}