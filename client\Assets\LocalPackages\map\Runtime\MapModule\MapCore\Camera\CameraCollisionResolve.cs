﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //相机碰撞行为
    public class CameraCollisionResolve : CameraAction
    {
        class Trigger
        {
            public Trigger(float length, Vector3 direction)
            {
                mLength = length;
                mDirection = direction;
            }

            public float GetColliderSlope(Vector3 newCameraPos, int layerMask)
            {
                RaycastHit hitInfo;
                var endPos = newCameraPos + mLength * mDirection;
                bool hit = Physics.Linecast(newCameraPos, endPos, out hitInfo, layerMask);
                if (hit)
                {
                    var xy = new Vector2(hitInfo.normal.x, hitInfo.normal.z);
                    if (!Mathf.Approximately(hitInfo.normal.y, 0))
                    {
                        // 1/slope
                        float invSlope = xy.magnitude / Mathf.Abs(hitInfo.normal.y);
                        return invSlope;
                    }
                    else
                    {
                        return -1;
                    }
                }

                return 0;
            }

            float mLength;
            Vector3 mDirection;
        }

        public CameraCollisionResolve(CameraActionType type, float colliderRadius, float triggerLength) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            mLayerMask = 1 << LayerMask.NameToLayer(MapCoreDef.CAMERA_COLLIDER_LAYER);
            mColliderRadius = colliderRadius;
            mTriggers = new List<Trigger>()
            {
                new Trigger(triggerLength, Vector3.forward),
                new Trigger(triggerLength, Vector3.back),
                new Trigger(triggerLength, Vector3.left),
                new Trigger(triggerLength, Vector3.right),
                new Trigger(triggerLength, (Vector3.forward + Vector3.left).normalized),
                new Trigger(triggerLength, (Vector3.forward + Vector3.right).normalized),
                new Trigger(triggerLength, (Vector3.back + Vector3.left).normalized),
                new Trigger(triggerLength, (Vector3.back + Vector3.right).normalized),
            };
        }

        public void EnableCollisionCheck(bool enabled)
        {
            mCheckCollision = enabled;
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            return mCurrentCameraPos;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            if (!mCheckCollision || MapCameraMgr.isMovingToTarget)
            {
                enabled = false;
                mIsTouching = false;
                mCurrentCameraPos = currentCameraPos;
                return;
            }

            float maxCheckHeight = Map.currentMap.data.maxCameraColliderHeight;
            if (currentCameraPos.y > maxCheckHeight)
            {
                mCurrentCameraPos = currentCameraPos;
                enabled = false;
                mIsTouching = false;
                return;
            }

            var oldCameraPos = MapCameraMgr.MapCameraRoot.transform.position;
            var delta = currentCameraPos - oldCameraPos;
            if (delta == Vector3.zero)
            {
                enabled = false;
                mCurrentCameraPos = currentCameraPos;
                return;
            }

            if (mIsTouching && delta.y < 0)
            {
                //when touching collider, we can't zoom camera
                enabled = true;
                mCurrentCameraPos = oldCameraPos;
                return;
            }

            //P.B("Camera Collision Update");
            int n = Physics.OverlapSphereNonAlloc(currentCameraPos, mColliderRadius, mResults, mLayerMask);
            if (n > 0)
            {
                mIsTouching = true;
                var testCenter = currentCameraPos - Vector3.up * mColliderRadius * 0.5f;
                float minHeight = MapCameraMgr.cameraSetting.cameraMinHeight;
                if (testCenter.y < minHeight)
                {
                    testCenter.y = minHeight;
                }
                for (int i = 0; i < mTriggers.Count; ++i)
                {
                    float slope = mTriggers[i].GetColliderSlope(testCenter, mLayerMask);
                    if (slope != 0)
                    {
                        mTouchingSlope = slope;
                        break;
                    }
                }
            }
            else
            {
                mIsTouching = false;
                mTouchingSlope = 0;
            }

            if (mIsTouching)
            {
                enabled = true;
                if (mTouchingSlope > 0)
                {
                    Vector2 delta2 = new Vector2(delta.x, delta.z);
                    float dy = mTouchingSlope * delta2.magnitude;
                    //限制最大的升高距离
                    dy = Mathf.Clamp(dy, 0, 10);
                    currentCameraPos.y = dy + oldCameraPos.y;
                }
                else if (mTouchingSlope < 0)
                {
                    var dis = (currentCameraPos - oldCameraPos).magnitude;
                    currentCameraPos.y = dis + oldCameraPos.y;
                    currentCameraPos.x = oldCameraPos.x;
                    currentCameraPos.z = oldCameraPos.z;
                }
            }
            mCurrentCameraPos = currentCameraPos;

            //P.E();
        }

        Vector3 mCurrentCameraPos;
        List<Trigger> mTriggers;
        float mColliderRadius;
        float mTouchingSlope = 0;
        int mLayerMask;
        bool mIsTouching = false;
        bool mCheckCollision = true;
        Collider[] mResults = new Collider[10];
    }
}