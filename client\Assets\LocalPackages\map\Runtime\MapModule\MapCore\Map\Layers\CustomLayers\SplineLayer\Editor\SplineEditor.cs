﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public partial class SplineEditor : MonoBehaviour
    {
        public enum Mode
        {
            Create,
            EditControlPoint,
            //EditMesh,
            Move,
        }
        public EditorMapLayerLODSetting setting = new EditorMapLayerLODSetting();
        public float splineControlPointRadius = 1f;
        public bool isLoop = false;
        public bool inside = true;
        public bool isCreatingRiver = true;
        public bool generateAssetsInWorldSpace = false;
        public string splineMaterialPath = "";
        public string waterMaterialPath = "";
        public string stencilMaskMaterialPath = "";
        public Mode mode = Mode.Create;
        SplineObjectManager mSplineObjectManager;
        public SplineObjectManager splineObjectManager { get { return mSplineObjectManager; } }

        void Start()
        {
            mSplineObjectManager = (Map.currentMap.data as EditorMapData).splineObjectManager;
        }
    }
}

#endif