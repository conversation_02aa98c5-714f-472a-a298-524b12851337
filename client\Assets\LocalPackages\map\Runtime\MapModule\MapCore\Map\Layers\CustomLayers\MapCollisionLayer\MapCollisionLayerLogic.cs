﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.8.5
 */

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum MapCollisionOperation
    {
        CreateCollision,
        EditCollisionVertex,
        MoveCollision,
        CutCollision,
    }

    [ExecuteInEditMode]
    [Black]
    public class MapCollisionLayerLogic : MapLayerLogic
    {
        public MapCollisionOperation operation = MapCollisionOperation.EditCollisionVertex;
        public float obstacleExpandRadius = 6.0f;
        public float snapDistance = 5.0f;
        public bool showCursor = false;
        public float cursorRadius = 5.0f;

        public MapCollisionLayer layer
        {
            get
            {
                if (Map.currentMap != null && Map.currentMap.root != null)
                {
                    var layer = Map.currentMap.GetMapLayerByID(layerID) as MapCollisionLayer;
                    return layer;
                }
                return null;
            }
        }

        private void OnEnable()
        {
            if (layer != null)
            {
                (layer.layerView as PolygonObjectLayerView).shadowRoot.SetActive(true);
            }
        }

        private void OnDisable()
        {
            if (layer != null)
            {
                var shadowRoot = (layer.layerView as PolygonObjectLayerView).shadowRoot;
                if (shadowRoot != null)
                {
                    shadowRoot.SetActive(false);
                }
            }
        }

        protected override void OnUpdate()
        {
            if (mRemovedCollisionsGameObjectID.Count > 0)
            {
                var collisionLayer = this.layer;
                mExistedObjectIDs.Clear();
                mAllObjects.Clear();
                collisionLayer.GetAllObjects(mAllObjects);

                var shadowRootTransform = (layer.layerView as PolygonObjectLayerView).shadowRoot.transform;
                int n = shadowRootTransform.childCount;
                for (int k = n - 1; k >= 0; --k)
                {
                    var child = shadowRootTransform.GetChild(k).gameObject;

                    var objectID = collisionLayer.GetObjectIDByGameObjectID(child.GetInstanceID());
                    if (objectID != 0)
                    {
                        mExistedObjectIDs.Add(objectID);
                    }
                }

                //find difference
                for (int j = 0; j < mAllObjects.Count; ++j)
                {
                    bool found = false;
                    for (int i = 0; i < mExistedObjectIDs.Count; ++i)
                    {
                        if (mAllObjects[j].GetEntityID() == mExistedObjectIDs[i])
                        {
                            found = true;
                            break;
                        }
                    }
                    if (!found)
                    {
                        collisionLayer.RemoveObject(mAllObjects[j].GetEntityID());
                    }
                }

                mRemovedCollisionsGameObjectID.Clear();
            }
        }

        public void OnCollisionModelRemoved(int gameObjectID)
        {
            mRemovedCollisionsGameObjectID.Add(gameObjectID);
        }

        List<int> mRemovedCollisionsGameObjectID = new List<int>();
        List<int> mExistedObjectIDs = new List<int>();
        List<IMapObjectData> mAllObjects = new List<IMapObjectData>();
    }
}
#endif