﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public abstract class MapLayerHexagonalGridCreator : MapLayerGridCreator
    {
        public override GameObject CreateGrid(Color color)
        {
            int rows = mLayer.verticalTileCount;
            int cols = mLayer.horizontalTileCount;
            var grid = new GameObject("Hexagonal Grid");
            grid.transform.position = mLayer.layerOffset;
            var meshFilter = grid.AddComponent<MeshFilter>();
            var meshRenderer = grid.AddComponent<MeshRenderer>();
            var mtl = new Material(Shader.Find("SLGMaker/Color"));
            mtl.color = color;
            meshRenderer.sharedMaterial = mtl;
            meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            var mesh = CreateGridMesh(rows, cols);
            meshFilter.sharedMesh = mesh;
            grid.SetActive(true);
            return grid;
        }

        protected abstract Vector3 CreateCorner(Vector3 center, float size, int index);

        void SetPositions(int positionOffset, Vector3 center, float size, Vector3[] positions)
        {
            for (int i = 0; i < 6; ++i)
            {
                positions[positionOffset + i] = CreateCorner(center, size, i);
            }
        }

        void SetIndices(int indexOffset, int positionOffset, int[] indices)
        {
            for (int i = 0; i < 6; ++i)
            {
                indices[indexOffset + i * 2] = i + positionOffset;
                indices[indexOffset + i * 2 + 1] = (i + 1) % 6 + positionOffset;
            }
        }

        Mesh CreateGridMesh(int rows, int cols)
        {
            var mesh = new Mesh();
            mesh.indexFormat = UnityEngine.Rendering.IndexFormat.UInt32;
            int vertexCount = rows * cols * 6;
            int idxCount = rows * cols * 12;
            var positions = new Vector3[vertexCount];
            var indices = new int[idxCount];

            int positionOffset = 0;
            int indexOffset = 0;
            float tileHeight = mLayer.tileHeight;
            float size = tileHeight * 0.5f;

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var center = mLayer.FromCoordinateToWorldPosition(j, i);
                    SetPositions(positionOffset, center, size, positions);
                    SetIndices(indexOffset, positionOffset, indices);
                    positionOffset += 6;
                    indexOffset += 12;
                }
            }

            mesh.vertices = positions;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            return mesh;
        }
    }
}

#endif