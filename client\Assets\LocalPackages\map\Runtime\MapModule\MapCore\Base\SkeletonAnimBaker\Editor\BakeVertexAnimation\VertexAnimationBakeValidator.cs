﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //验证烘培的数据是否正确
    public class VertexAnimationBakeValidator : MonoBehaviour
    {
        class RendererItem
        {
            public Texture2D texture;
            public Mesh mesh;
            public int meshIndex = -1;
        }

        void Start()
        {
            mRenderers = new List<RendererItem>();
            mAnimationData = gameObject.GetComponent<AnimatorWrapper>().animationData as BakedVertexAnimationData;
            var rendererTags = gameObject.GetComponentsInChildren<RendererTag>(true);
            foreach (var tag in rendererTags)
            {
                var item = new RendererItem();
                item.texture = tag.GetComponent<MeshRenderer>().sharedMaterial.GetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME) as Texture2D;
                item.mesh = tag.GetComponent<MeshFilter>().sharedMesh;
                item.meshIndex = tag.meshIndex;
                mRenderers.Add(item);
            }
            PlayAnimation("idle");
        }

        public void PlayAnimation(string name)
        {
            mCurrentAnimationIndex = mAnimationData.GetAnimationIndex(name);
            mCurrentTime = 0;
            mAnimationLength = mAnimationData.animationInfo[mCurrentAnimationIndex].lengthInSeconds;
        }

        void Update()
        {
            for (int i = 0; i < mRenderers.Count; ++i)
            {
                SampleAnimation(mRenderers[i].meshIndex);
            }
            mCurrentTime += Time.deltaTime;
            if (mCurrentTime > mAnimationLength)
            {
                mCurrentTime -= mAnimationLength;
            }
        }

        void SampleAnimation(int meshIndex)
        {
            var texture = mRenderers[meshIndex].texture;
            var mesh = mRenderers[meshIndex].mesh;
            int vertexCount = mesh.vertexCount;
            float v = mCurrentTime / mAnimationLength * mAnimationData.animationInfo[mCurrentAnimationIndex].bakedVertexAnimationLengthRatio;
            //Debug.LogError($"mCurrentTime: {mCurrentTime}, mAnimationLength: {mAnimationLength}");
            mSampledVertexPositions.Clear();
            List<Vector3> uvs = new List<Vector3>();
            mesh.GetUVs(0, uvs);
            for (int i = 0; i < vertexCount; ++i) {
                float u = (float)uvs[i].z / (vertexCount - 1);
                float gu = u * (texture.width - 1);
                float gv = v * (texture.height - 1);
                int minX = Mathf.FloorToInt(gu);
                int minY = Mathf.FloorToInt(gv) + mAnimationData.GetVertexAnimationOffsetForRenderer(meshIndex, mCurrentAnimationIndex);
                int maxX = Mathf.CeilToInt(gu);
                int maxY = Mathf.CeilToInt(gv);
                var c0 = texture.GetPixel(minX, minY);
                var c1 = texture.GetPixel(maxX, minY);
                var c2 = texture.GetPixel(minX, maxY);
                var c3 = texture.GetPixel(maxX, maxY);
                var v0 = Color.Lerp(c0, c1, gu - minX);
                var v1 = Color.Lerp(c2, c3, gu - minX);
                var result = Color.Lerp(v0, v1, gv - minY);
                var pos =  new Vector3(result.r, result.g, result.b);
                mSampledVertexPositions.Add(pos);
            }

            mesh.SetVertices(mSampledVertexPositions);
        }

        List<RendererItem> mRenderers;
        float mCurrentTime = 0;
        float mAnimationLength = 0;
        int mCurrentAnimationIndex = -1;
        List<Vector3> mSampledVertexPositions = new List<Vector3>();
        BakedVertexAnimationData mAnimationData;
    }
}


#endif