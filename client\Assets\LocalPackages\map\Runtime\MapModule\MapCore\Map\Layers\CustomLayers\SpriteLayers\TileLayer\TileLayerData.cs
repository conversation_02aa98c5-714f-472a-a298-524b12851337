﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class TileLayerData : MapLayerData
    {
        public TileLayerData(MapLayerDataHeader header, Map map, TileData[] tileDatas)
            : base(header, null, map)
        {
            int n = mRows * mCols;
            if (tileDatas == null)
            {
                tileDatas = new TileData[n];
            }
            Debug.Assert(tileDatas.Length == n);
            mTiles = tileDatas;

            mLastViewport = new Rect(-100000, -100000, 0, 0);
        }

        public override void OnDestroy()
        {
            int n = mTiles.Length;
            for (int i = 0; i < n; ++i)
            {
                if (mTiles[i] != null)
                {
                    mTiles[i].OnDestroy();
                }
            }
            mTiles = null;
        }

        public TileData GetTile(int x, int y)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                return mTiles[y * mCols + x];
            }
            return null;
        }

        public bool SetTile(int x, int y, TileData data)
        {
            bool success = false;
            if (SetTileInternal(x, y, data))
            {
                Map.currentMap.BroadcastMessage(new GMSetTile(x, y, id, data));
                success = true;
            }
            return success;
        }

        public void ClearTile(int x, int y)
        {
            var data = GetTile(x, y);
            if (data != null)
            {
                Map.currentMap.BroadcastMessage(new GMClearTile(x, y, id));
                data.OnDestroy();
                mTiles[y * mCols + x] = null;
            }
        }

        public void ClearAllTiles()
        {
            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    ClearTile(j, i);
                }
            }
        }

        bool SetTileInternal(int x, int y, TileData data)
        {
            if (x >= 0 && x < mCols && y >= 0 && y < mRows)
            {
                mTiles[y * mCols + x] = data;
                return true;
            }
            return false;
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            if (mLastViewport != newViewport)
            {
                var oldViewRect = GetViewRect(mLastViewport);
                var newViewRect = GetViewRect(newViewport);
                UpdateViewRect(oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;

            return false;
        }

        bool UpdateViewRect(Rect2D oldViewRect, Rect2D newViewRect)
        {
            var map = Map.currentMap;
            if (oldViewRect != newViewRect)
            {
                for (int i = oldViewRect.minY; i <= oldViewRect.maxY; ++i)
                {
                    for (int j = oldViewRect.minX; j <= oldViewRect.maxX; ++j)
                    {
                        if (!newViewRect.Contains(j, i))
                        {
                            //物体不在新的视野中,隐藏它
                            SetActive(j, i, false);
                        }
                    }
                }

                for (int i = newViewRect.minY; i <= newViewRect.maxY; ++i)
                {
                    for (int j = newViewRect.minX; j <= newViewRect.maxX; ++j)
                    {
                        if (!oldViewRect.Contains(j, i))
                        {
                            //物体不在旧的视野中,显示它
                            SetActive(j, i, true);
                        }
                    }
                }

                return true;
            }

            return false;
        }

        void SetActive(int x, int y, bool active)
        {
            var objectData = GetTile(x, y);
            if (objectData != null)
            {
                mActiveStateChangeEvent.minX = x;
                mActiveStateChangeEvent.minY = y;
                mActiveStateChangeEvent.maxX = x;
                mActiveStateChangeEvent.maxY = y;
                mActiveStateChangeEvent.active = active;
                mActiveStateChangeEvent.layerID = id;
                Map.currentMap.BroadcastMessage(mActiveStateChangeEvent);
            }
        }

        public override void RefreshObjectsInViewport()
        {
            UpdateViewport(Map.currentMap.viewport, 0);
        }

        public override bool Contains(int objectID)
        {
            return false;
        }

        public Rect2D GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new Rect2D(startCoord.x, startCoord.y, endCoord.x, endCoord.y);
        }

        public bool IsInViewRange(int x, int y, Rect viewport)
        {
            var viewRect = GetViewRect(viewport);
            return viewRect.Contains(x, y);
        }

        public override bool isGameLayer => false;

        protected TileData[] mTiles;
        Rect mLastViewport;
        GMTileActiveStateChange mActiveStateChangeEvent = new GMTileActiveStateChange();
    };
}

#endif