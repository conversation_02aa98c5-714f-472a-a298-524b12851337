﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class MapPluginManagerEditor : EditorWindow
    {
        DefaultAsset mScriptGenerateFolder;
        Vector2 mScrollPos;
        bool mShowPlugins = true;

        public void LoadFromFile()
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            MapModule.InitEditor(mapConfig);
            MapPlugin.LoadPluginListEditor(mapConfig.pluginList);
        }

        public void SaveToFile()
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            MapModule.InitEditor(mapConfig);

            var pluginLayerInfos = MapPlugin.pluginLayerInfos;
            for (int i = 0; i < pluginLayerInfos.Count; ++i)
            {
                string errorMsg = Validate(pluginLayerInfos[i], false);
                if (!string.IsNullOrEmpty(errorMsg))
                {
                    EditorUtility.DisplayDialog("Error", errorMsg, "OK");
                    return;
                }
            }

            MapPlugin.SavePluginList(mapConfig.pluginList);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        private void OnGUI()
        {
            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Load Plugin Configs"))
            {
                LoadFromFile();
                Repaint();
            }
            if (GUILayout.Button("Save Plugin Configs"))
            {
                SaveToFile();
            }
            EditorGUILayout.EndHorizontal();
            if (GUILayout.Button("Add Plugin Config"))
            {
                var pluginInfo = new MapPluginLayerInfo();
                pluginInfo.assemblyName = "Assembly-CSharp";
                pluginInfo.editorSaveFileName = "layer_plugin_test.config";
                pluginInfo.runtimeSaveFileName = "layer_plugin_test_data.bytes";
                pluginInfo.layerClassName = "LayerPluginTest";
                pluginInfo.runtimeLayerClassName = "LayerPluginTest";
                pluginInfo.proxyLayerClassName = "MapLayerPluginTest";
                pluginInfo.layerTitle = "Plugin Layer Test";
                pluginInfo.layerTooltip = "This is a plugin layer test";
                pluginInfo.namespaceName = "TFW.Map";
                MapPlugin.RegisterPluginLayerInfo(pluginInfo);
            }

            EditorGUIUtility.labelWidth = 185;
            mScriptGenerateFolder = EditorGUILayout.ObjectField($"Script Files Generating Folder", mScriptGenerateFolder, typeof(DefaultAsset), false, null) as DefaultAsset;
            EditorGUIUtility.labelWidth = 0;

            mShowPlugins = EditorGUILayout.Foldout(mShowPlugins, "Plugin Layers");
            if (mShowPlugins)
            {
                var pluginLayerInfo = MapPlugin.pluginLayerInfos;
                for (int i = 0; i < pluginLayerInfo.Count; ++i)
                {
                    DrawPluginLayer(pluginLayerInfo[i]);
                }
            }

            EditorGUILayout.EndScrollView();
        }

        void DrawPluginLayer(MapPluginLayerInfo layerInfo)
        {
            EditorGUILayout.BeginVertical("GroupBox");
            layerInfo.layerClassName = EditorGUILayout.TextField("Layer Class Name", layerInfo.layerClassName);
            layerInfo.runtimeLayerClassName = EditorGUILayout.TextField("Runtime Layer Class Name", layerInfo.runtimeLayerClassName);
            layerInfo.proxyLayerClassName = EditorGUILayout.TextField("Proxy Layer Class Name", layerInfo.proxyLayerClassName);
            layerInfo.assemblyName = EditorGUILayout.TextField("Assembly Name", layerInfo.assemblyName);
            layerInfo.namespaceName = EditorGUILayout.TextField("Namespace Name", layerInfo.namespaceName);
            layerInfo.layerTitle = EditorGUILayout.TextField("Layer Title", layerInfo.layerTitle);
            layerInfo.layerTooltip = EditorGUILayout.TextField("Tooltip", layerInfo.layerTooltip);
            layerInfo.editorSaveFileName = EditorGUILayout.TextField("Editor Save File Name", layerInfo.editorSaveFileName);
            layerInfo.runtimeSaveFileName = EditorGUILayout.TextField("Runtime Save File Name", layerInfo.runtimeSaveFileName);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Generate Template C# Scripts"))
            {
                string errorMsg = Validate(layerInfo, true);
                if (string.IsNullOrEmpty(errorMsg))
                {
                    if (EditorUtility.DisplayDialog("Warning", "生成新脚本会覆盖旧的文件!确定继续?", "Yes", "No"))
                    {
                        string folder = AssetDatabase.GetAssetPath(mScriptGenerateFolder);
                        MapPluginCreator.CreateCSharpScripts(folder, layerInfo);
                        SaveToFile();
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", errorMsg, "OK");
                }
            }
            if (GUILayout.Button("Remove Plugin"))
            {
                if (EditorUtility.DisplayDialog("Warning", "确定删除该插件?", "Yes", "No"))
                {
                    MapPlugin.UnregisterPluginLayerInfo(layerInfo);
                    SaveToFile();
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndHorizontal();
        }

        string Validate(MapPluginLayerInfo layerInfo, bool validateOutputFolder)
        {
            if (string.IsNullOrEmpty(layerInfo.layerClassName))
            {
                return "Invalid layer class name";
            }

            if (string.IsNullOrEmpty(layerInfo.proxyLayerClassName))
            {
                return "Invalid proxy layer class name";
            }

            if (validateOutputFolder && mScriptGenerateFolder == null)
            {
                return "Invalid output folder";
            }

            return MapPlugin.CheckDuplication(layerInfo);
        }
    }
}

#endif