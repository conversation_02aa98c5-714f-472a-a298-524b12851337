﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class SelectDecorationObjectsInRange
    {
        enum RectangleFillCursorState
        {
            Idle,
            SelectMin,
        }

        public SelectDecorationObjectsInRange(ComplexGridModelLayerLogic logic)
        {
            mLogic = logic;
        }

        public void DrawInspectorGUI()
        {
            if (mLogic.operationType == ComplexGridModelOperationType.kSelectObjectInRange)
            {
                var setting = mLogic.layerData.objectPlacementSetting;
                setting.onlySelectCurrentTagObjects = EditorGUILayout.ToggleLeft("Only Select Current Tag Objects", setting.onlySelectCurrentTagObjects);
            }
        }

        public void DrawSceneGUI(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
            bool dirty = false;
            var setting = mLogic.layerData.objectPlacementSetting;

            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mRectangleFillState == RectangleFillCursorState.Idle)
                {
                    mRectangleFillState = RectangleFillCursorState.SelectMin;
                    mMinPos = worldPos;
                    mMaxPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseDrag && e.button == 0)
            {
                dirty = true;
                if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                {
                    mMaxPos = worldPos;
                }
            }
            else if (e.type == EventType.MouseUp && e.button == 0)
            {
                if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                {
                    var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
                    List<IComplexGridModelData> objects = new List<IComplexGridModelData>();
                    Vector3 min = Vector3.Min(mMinPos, mMaxPos);
                    Vector3 max = Vector3.Max(mMinPos, mMaxPos);
                    string tag = mLogic.selectedObjectTag;
                    if (setting.onlySelectCurrentTagObjects && !string.IsNullOrEmpty(tag))
                    {
                        layer.GetObjectsInRangeWithTag(mLogic.selectedLOD, tag, min, max, objects);
                    }
                    else
                    {
                        layer.GetObjectsInRange(mLogic.selectedLOD, min, max, objects);
                    }
                    GameObject[] gameObjects = new GameObject[objects.Count];
                    for (int i = 0; i < objects.Count; ++i)
                    {
                        gameObjects[i] = layer.layerView.GetGameObject(objects[i].GetEntityID());
                        Debug.Assert(gameObjects[i] != null);
                    }
                    Selection.objects = gameObjects;
                    
                    mRectangleFillState = RectangleFillCursorState.Idle;
                }
            }

            if (mRectangleFillState != RectangleFillCursorState.Idle)
            {
                Vector3 min = Vector3.Min(mMinPos, mMaxPos);
                Vector3 max = Vector3.Max(mMinPos, mMaxPos);
                Handles.color = Color.green;
                Handles.DrawWireCube((min + max) * 0.5f, max - min);
            }

            if (dirty)
            {
                SceneView.RepaintAll();
            }
        }

        RectangleFillCursorState mRectangleFillState = RectangleFillCursorState.Idle;
        Vector3 mMinPos;
        Vector3 mMaxPos;
        ComplexGridModelLayerLogic mLogic;
    }
}
#endif