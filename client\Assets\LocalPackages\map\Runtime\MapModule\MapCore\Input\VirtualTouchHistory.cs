﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class VirtualTouchHistory : IMapTouchHistory
    {
        public VirtualTouchHistory(float minDistance)
        {
            mMinDistance = minDistance;
        }

        public bool AddPoint(Vector2 pt)
        {
            if (mStartPosition == Vector2.zero)
            {
                mStartPosition = pt;
                //D.Log("Set Start Position: " + pt.ToString());
            }

            if (mPrevious == Vector2.zero)
            {
                mPrevious = pt;
                mCurrent = pt;
                return true;
            }
            else
            {
                if ((pt - mPrevious).sqrMagnitude > mMinDistance * mMinDistance)
                {
                    mPrevious = mCurrent;
                    mCurrent = pt;
                    return true;
                }
                else
                {
                    mPrevious = pt;
                    mCurrent = pt;
                }
            }

            return false;
        }

        public void SetCurPos(Vector2 pt)
        {
            mCurrent = pt;
        }

        public Vector2 GetDelta(Vector2 alongDir)
        {
            var d = mCurrent - mPrevious;
            if (alongDir == Vector2.zero)
            {
                return d;
            }
            alongDir.Normalize();
            return Vector2.Dot(d, alongDir) * alongDir;
        }

        public void Clear()
        {
            mPrevious = Vector2.zero;
            mCurrent = Vector2.zero;
            mStartPosition = Vector2.zero;
        }

        public Vector2 previous { get { return mPrevious; } }
        public Vector2 current { get { return mCurrent; } }
        public Vector2 startPosition { get { return mStartPosition; } }
        public bool isMoved { get { return mCurrent != mPrevious; } }

        Vector2 mPrevious = Vector2.zero;
        Vector2 mCurrent;
        Vector2 mStartPosition = Vector2.zero;
        float mMinDistance = 1.0f;
    }
}