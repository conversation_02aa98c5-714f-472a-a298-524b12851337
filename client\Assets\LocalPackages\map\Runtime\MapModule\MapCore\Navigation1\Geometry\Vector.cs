﻿ 



 
 

namespace TFW.Map.Geo
{
    // 从坐标原点指向点所在位置的矢量称为位置矢量，简称位矢。
    // https://zh.wikipedia.org/wiki/%E4%BD%8D%E7%BD%AE%E5%90%91%E9%87%8F
    public struct Vector
    {
        int X;
        int Z;

        public Vector(Coord start, Coord end)
        {
            X = end.X - start.X;
            Z = end.Z - start.Z;
        }

        public Vector(Coord p)
        {
            X = p.X;
            Z = p.Z;
        }

        public Vector(int X, int Z)
        {
            this.X = X;
            this.Z = Z;
        }

        public double Dot(Vector vec)
        {
            return (long)X * (long)vec.X + (long)Z * (long)vec.Z;
        }

        // result > 0 vec在v左侧; reslut <0 vec在v右; result.Y = 0 共线
        public long Cross(Vector vec)
        {
            return (long)X * (long)vec.Z - (long)Z * (long)vec.X;
        }

        public double LengthSquared()
        {
            return (long)X * (long)X + (long)Z * (long)Z;
        }

        public double Length()
        {
            return System.Math.Sqrt(LengthSquared());
        }

        public Vector Trunc(double ratio)
        {
            return new Vector(
                (int)(ratio * (double)X),
                (int)(ratio * (double)Z));
        }

        public Coord ToCoord(Coord start)
        {
            return new Coord(
                start.X + X,
                start.Z + Z);
        }

        // 向量按照角度旋转获得新的向量，左手坐标系，旋转的时候默认向左边旋转
        // 对于任意两个不同点A和B，A绕B旋转θ角度后的坐标为：
        // (Δx*cosθ- Δy * sinθ+ xB, Δy*cosθ + Δx * sinθ+ yB )
        // 注：xB、yB为B点坐标。
        // https://blog.csdn.net/u013445530/article/details/44904017
        public Vector Rotate(double angle)
        {
            double x0 = X;
            double z0 = Z;

            var cos = System.Math.Cos(angle);
            var sin = System.Math.Sin(angle);

            var x = x0 * cos - z0 * sin;

            var z = x0 * sin + z0 * cos;

            return new Vector((int)x, (int)z);
        }

        // 获取点到向量的距离
        public double CalCoordDst(Coord start, Coord target)
        {
            var vec = new Vector(start, target);
            var angle = GetAngle(vec);
            return vec.Length() * System.Math.Sin(angle);
        }

        // 获取俩个向量的角度
        public double GetAngle(Vector vec)
        {
            var a = Dot(vec);
            var b = Length() * vec.Length();

            var t = a / b;
            var angle = System.Math.Acos(t);
            if (double.IsNaN(angle))
            {
                if (t > 0)
                {
                    return 0;
                }
                return System.Math.PI;
            }

            return angle;
        }

        // Ratio表示newVec和Vec长度的比值
        // 返回newVec终点坐标
        public Coord CalCoordByRatio(Coord startCoord, Coord endCoord, double ratio)
        {
            var vec = new Vector(startCoord, endCoord);
            return vec.Trunc(ratio).ToCoord(startCoord);
        }
    }
}

