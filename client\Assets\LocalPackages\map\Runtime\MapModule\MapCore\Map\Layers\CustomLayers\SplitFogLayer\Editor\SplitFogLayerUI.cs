﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(SplitFogLayerLogic))]
    public partial class SplitFogLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as SplitFogLayerLogic;
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            prefabManager.selectPrefabEvent += OnSelectPrefab;

            mPrefab = prefabManager.selectedPrefab;

            mLogic.UpdateGizmoVisibilityState();

            mIndicator = new TileIndicator(false);
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                prefabManager.selectPrefabEvent -= OnSelectPrefab;
                mLogic.SetGridVisible(false);
                mLogic.SetLayerBoundsVisible(false);
                HideIndicator();

                mIndicator.OnDestroy();
                mIndicator = null;
            }
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;
            if (mLogic.operationType == SplitFogOperationType.Create || mLogic.operationType == SplitFogOperationType.Remove)
            {
                if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
                {
                    mLeftButtonDown = true;
                }

                if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
                {
                    mLeftButtonDown = false;
                    mLastSubCoord = new Vector3Int(-1, -1, -1);
                }

                var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);

                var curSubCoord = PickTiles(screenPos);

                UpdateIndicatorState();

                if (mLeftButtonDown)
                {
                    if (curSubCoord != mLastSubCoord)
                    {
                        mLastSubCoord = curSubCoord;
                        SetTiles(mLogic.operationType == SplitFogOperationType.Remove);
                    }
                }

                HandleUtility.AddDefaultControl(0);
            }
            else
            {
                HideIndicator();
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();

                if (mLogic.operationType == SplitFogOperationType.Create)
                {
                    var operationType = (SplitFogOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                    if (operationType != mLogic.operationType)
                    {
                        SetOperation(operationType);
                    }

                    DrawTilingTile();

                    //draw height
                    float height = EditorGUILayout.FloatField("Height", mLogic.layerData.fogHeight);
                    if (!Mathf.Approximately(height, mLogic.layerData.fogHeight))
                    {
                        mLogic.layerData.fogHeight = height;
                    }

                    //draw lod prefab
                    GameObject lod1Prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mLogic.layerData.fogLOD1PrefabPath);
                    lod1Prefab = EditorGUILayout.ObjectField("LOD 1 Prefab", lod1Prefab, typeof(GameObject), false, null) as GameObject;
                    if (lod1Prefab != null && PrefabUtility.GetPrefabAssetType(lod1Prefab) == PrefabAssetType.Regular)
                    {
                        mLogic.layerData.fogLOD1PrefabPath = AssetDatabase.GetAssetPath(lod1Prefab);
                    }

                    //draw selection material
                    Material selectionMaterial = AssetDatabase.LoadAssetAtPath<Material>(mLogic.layerData.selectionMaterialPath);
                    selectionMaterial = EditorGUILayout.ObjectField("Selection Material", selectionMaterial, typeof(Material), false, null) as Material;
                    if (selectionMaterial != null)
                    {
                        mLogic.layerData.selectionMaterialPath = AssetDatabase.GetAssetPath(selectionMaterial);
                    }

                    //draw mask texture property name
                    mLogic.layerData.fogMaskTexPropertyName = EditorGUILayout.TextField("Mask Texture Property Name", mLogic.layerData.fogMaskTexPropertyName);

                    var editorMapData = Map.currentMap.data as EditorMapData;
                    var prefabManager = editorMapData.editorSplitFogPrefabManager;
                    prefabManager.Draw(PrefabGroupDisplayFlag.ShowColor | PrefabGroupDisplayFlag.ShowDecoration);
                }
                else
                {
                    mLogic.operationType = (SplitFogOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operationType);
                }

                var layerData = mLogic.layerData;
                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, LODDisplayFlag.SpecialBufferSetting, null, null);

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Tile Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Tile Height", layerData.tileHeight.ToString());
                EditorGUILayout.LabelField("X Tile Count", layerData.horizontalTileCount.ToString());
                EditorGUILayout.LabelField("Z Tile Count", layerData.verticalTileCount.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        void CalculateTileDatas(out int[] oldTileDatas, out int[] newTileDatas)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as SplitFogLayer;
            oldTileDatas = new int[4];
            newTileDatas = new int[4];
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            int prefabGroupIndex = prefabManager.selectedGroupIndex;

            for (int i = 0; i < mPickedTiles.Length; ++i)
            {
                var tile = layer.GetTile(mPickedTiles[i].x, mPickedTiles[i].y);
                if (tile != 0)
                {
                    oldTileDatas[i] = tile;
                }
                else
                {
                    oldTileDatas[i] = 0;
                }
            }

            //try push tile
            if (prefabGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabGroupIndex);
                if (group != null && group.isValid)
                {
                    //不能使用的tile index,避免4个tile中有相同的tile index
                    for (int i = 0; i < 4; ++i)
                    {
                        int tileIndex;
                        bool valid = layer.GetPushTileResult(mPickedTiles[i].x, mPickedTiles[i].y, mTileIndex[i], out tileIndex);
                        if (valid)
                        {
                            newTileDatas[i] = tileIndex;
                        }
                        else
                        {
                            newTileDatas[i] = 0;
                        }
                    }
                }
            }
        }

        void SetTiles(bool clearTile)
        {
            if (clearTile)
            {
                var act = new ActionClearSplitFogLayerTiles(mLogic.layerID, mPickedTiles);
                ActionManager.instance.PushAction(act);
            }
            else
            {
                var editorMapData = Map.currentMap.data as EditorMapData;
                var prefabManager = editorMapData.editorSplitFogPrefabManager;
                if (prefabManager.selectedGroupIndex >= 0)
                {
                    var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                    if (group.isValid)
                    {
                        int[] oldTileDatas;
                        int[] newTileDatas;
                        CalculateTileDatas(out oldTileDatas, out newTileDatas);
                        var act = new ActionSetSplitFogLayerTiles(mLogic.layerID, prefabManager.selectedGroupIndex, mPickedTiles, oldTileDatas, newTileDatas);

                        ActionManager.instance.PushAction(act);
                    }
                }
            }
        }

        void DrawTilingTile()
        {
            if (GUILayout.Button("Tiling Prefab"))
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as SplitFogLayer;
                TilePrefab(layer);
            }
        }

        //创建这个tile的多边形
        List<Vector3> CreateTilePolygon(int x, int y)
        {
            float minX = mLogic.layerData.tileWidth * x;
            float maxX = mLogic.layerData.tileWidth * (x + 1);
            float minZ = mLogic.layerData.tileHeight * y;
            float maxZ = mLogic.layerData.tileHeight * (y + 1);

            var polygon = new List<Vector3>()
            {
            new Vector3(minX, 0, minZ),
            new Vector3(maxX, 0, minZ),
            new Vector3(maxX, 0, maxZ),
            new Vector3(minX, 0, maxZ),
            };

            return polygon;
        }

        List<Vector2Int> GetValidTiles()
        {
            List<Vector2Int> tiles = new List<Vector2Int>();

            int rows = mLogic.layerData.verticalTileCount;
            int cols = mLogic.layerData.horizontalTileCount;

            var center = new Vector3(Map.currentMap.mapWidth * 0.5f, 0, Map.currentMap.mapHeight * 0.5f);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    tiles.Add(new Vector2Int(j, i));
                }
            }

            return tiles;
        }

        void TilePrefab(SplitFogLayer layer)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorSplitFogPrefabManager;
            if (prefabManager.selectedGroupIndex >= 0)
            {
                var validTiles = GetValidTiles();
                if (validTiles.Count > 0)
                {
                    var group = prefabManager.GetGroupByIndex(prefabManager.selectedGroupIndex);
                    //获取所有有效的full prefab
                    var validPrefabIndices = group.GetValidPrefabIndices();
                    if (validPrefabIndices.Count > 0)
                    {
                        var act = new ActionTilingSplitFogLayer(layer.id, prefabManager.selectedGroupIndex, validTiles, 15);
                        ActionManager.instance.PushAction(act);
                        Repaint();
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Error", "Can't tile prefab!", "OK");
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Can't tile prefab!", "OK");
                }
            }
        }

        void ShowIndicator()
        {
            mIndicator.SetActive(true);
            mIndicator.SetColor(new Color(0, 1, 0, 0.5f));
        }

        void UpdateIndicatorState()
        {
            Vector3 indicatorPos = Vector3.zero;
            for (int i = 0; i < 4; ++i)
            {
                var pos = mLogic.layerData.FromCoordinateToWorldPositionCenter(mPickedTiles[i].x, mPickedTiles[i].y);
                indicatorPos += pos;
            }

            UpdateIndicator(indicatorPos / 4);
        }

        void UpdateIndicator(Vector3 pos)
        {
            if (mLogic.operationType == SplitFogOperationType.Create || mLogic.operationType == SplitFogOperationType.Remove)
            {
                ShowIndicator();
            }
            else
            {
                HideIndicator();
            }

            pos.y += 0.5f;
            mIndicator.SetPosition(pos);
            mIndicator.SetScale(mLogic.layerData.tileWidth);
        }

        void HideIndicator()
        {
            mIndicator.SetActive(false);
        }

        void SetPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        void OnSelectPrefab(GameObject prefab)
        {
            mPrefab = prefab;
        }

        Vector3Int PickTiles(Vector2 screenPos)
        {
            Vector3Int curSubCoord = Vector3Int.zero;
            var layerData = mLogic.layerData;
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);
            var coord = layerData.FromWorldPositionToCoordinate(worldPos);
            curSubCoord.x = coord.x;
            curSubCoord.y = coord.y;
            var tileStartPos = layerData.FromCoordinateToWorldPosition(coord.x, coord.y);
            var rx = (worldPos.x - tileStartPos.x) / layerData.tileWidth;
            var rz = (worldPos.z - tileStartPos.z) / layerData.tileHeight;
            if (rx <= 0.5)
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y);
                    curSubCoord.z = 0;
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x - 1, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x - 1, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x, coord.y + 1);
                    curSubCoord.z = 3;
                }
            }
            else
            {
                if (rz <= 0.5)
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y - 1);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y - 1);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y);
                    curSubCoord.z = 2;
                }
                else
                {
                    mPickedTiles[0] = new Vector2Int(coord.x, coord.y);
                    mPickedTiles[1] = new Vector2Int(coord.x + 1, coord.y);
                    mPickedTiles[2] = new Vector2Int(coord.x, coord.y + 1);
                    mPickedTiles[3] = new Vector2Int(coord.x + 1, coord.y + 1);
                    curSubCoord.z = 4;
                }
            }
            return curSubCoord;
        }

        bool IsValidTile(int index)
        {
            var layerData = mLogic.layerData;
            var tile = mPickedTiles[index];
            return tile.x >= 0 && tile.x < layerData.horizontalTileCount &&
                tile.y >= 0 && tile.y < layerData.verticalTileCount;
        }

        void SetOperation(SplitFogOperationType operationType)
        {
            mLogic.operationType = operationType;
            mLastCoord = new Vector2Int(-1, -1);
        }

        GameObject mPrefab;
        TileIndicator mIndicator;

        bool mLeftButtonDown = false;
        Vector2Int mLastCoord = new Vector2Int(-1, -1);

        /* 3 4
         * 1 2
         */
        //当前鼠标拾取的4个tile
        Vector2Int[] mPickedTiles = new Vector2Int[4];
        static int[] mTileIndex = new int[] { 1, 2, 4, 8 };
        SplitFogLayerLogic mLogic;
        Vector3Int mLastSubCoord;
    }
}

#endif