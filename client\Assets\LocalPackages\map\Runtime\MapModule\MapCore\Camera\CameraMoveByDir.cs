﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //方向自动移动相机
    public class CameraMoveByDir : CameraAction
    {
        public Vector3 dir;

        public CameraMoveByDir(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            return mCurrentCameraPos;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mCurrentCameraPos = currentCameraPos + dir.normalized * 5f * Time.deltaTime;
        }

        Vector3 mCurrentCameraPos;
    }
}
