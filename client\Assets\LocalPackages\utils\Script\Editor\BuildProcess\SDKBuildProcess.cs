﻿








//#define UNITY_IPHONE
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Callbacks;
#if UNITY_IPHONE
using UnityEditor.iOS.Xcode;
#endif
using UnityEngine;

namespace TFW.SDK.Editor
{
    public partial class SDKBuildProcess
    {
        public static bool BuildFaild = false;
        public static string BuildError = string.Empty;

        public const string SDK_CONFIG_PATH = "SDKConfig/";
        public const string SDK_PATH = "SDK/";
        public const string SDK_CONFIG_USED = "autogen";

        public static void SDKPreprocess(BuildTarget target)
        {
            try
            {
                CallAllMethodWithAttribute(typeof(SDKPreprocessAttribute), target);
            }
            catch (Exception e)
            {
                throw new Exception("SDKPreprocess Exception: " + e.Message);
            }

            AssetDatabase.Refresh();
        }

        public static void SDKPostprocess(BuildTarget target)
        {
            try
            {
                CallAllMethodWithAttribute(typeof(SDKPostprocessAttribute), target);
            }
            catch (Exception e)
            {
                throw new Exception("SDKPostprocess Exception: " + e.Message);
            }

            AssetDatabase.Refresh();
        }

        public static void PreprocessBuild(BuildTarget target)
        {
            try
            {
                if (BuildTarget.Android == target)
                {
                    RemoveDir(Path.Combine(Application.dataPath, SDK_CONFIG_PATH + "Plugins/Android"));
                }

                CallAllMethodWithAttribute(typeof(SDKOnPreProcessAttribute), target);
            }
            catch (Exception e)
            {
                throw new Exception("PreprocessBuild Exception: " + e.Message);
            }

            AssetDatabase.Refresh();
        }

        public static void PreprocessBuildForceResolve(BuildTarget target)
        {
            // #if UNITY_ANDROID && USE_TGSBASIC

            //             if (BuildTarget.Android == target)
            //             {
            //                 //Google.VersionHandler.VerboseLoggingEnabled = true;
            //                 //Google.VersionHandler.UpdateVersionedAssets(forceUpdate: true);
            //                 //Google.VersionHandler.Enabled = true;
            //                 //AssetDatabase.Refresh();

            //                 GooglePlayServices.PlayServicesResolver.MenuForceResolve();
            //                 AssetDatabase.Refresh();
            //             }

            // #endif
        }

        #region Utils
        static List<string> paths = new List<string>();
        static List<string> files = new List<string>();

        static void Recursive(string path, bool filterMeta = true)
        {
            string[] names = Directory.GetFiles(path);
            string[] dirs = Directory.GetDirectories(path);

            foreach (string filename in names)
            {
                string ext = Path.GetExtension(filename);
                if (filterMeta && ext.Equals(".meta")) continue;
                if (filterMeta && ext.Equals(".DS_Store")) continue;
                files.Add(filename.Replace('\\', '/'));
            }

            foreach (string dir in dirs)
            {
                Recursive(dir, filterMeta);
                paths.Add(dir.Replace('\\', '/'));
            }
        }

        public static void ClearPath(string path)
        {
            files.Clear();
            paths.Clear();
            Recursive(path, false);

            foreach (string filename in files)
            {
                File.Delete(filename);
            }

            foreach (string dir in paths)
            {
                Directory.Delete(dir);
            }
        }

        public static void RemoveDir(string path)
        {
            //Debug.Log("RemoveDir path: " + path);

            try
            {
                if (Directory.Exists(path))
                {
                    ClearPath(path);

                    Directory.Delete(path);
                }
            }
            catch (Exception e)
            {
                Debug.LogError("RemoveDir Exception: " + e);
            }
        }

        public static void CopyDir(string sourcePath, string targetPath, bool cleanTargetPath = true, bool filterMeta = false)
        {
            if (cleanTargetPath)
            {
                if (Directory.Exists(targetPath))
                {
                    ClearPath(targetPath);
                }
            }

            files.Clear();
            paths.Clear();
            Recursive(sourcePath, filterMeta);

            string t = "";
            string d = "";
            foreach (string f in files)
            {
                t = targetPath + f.Substring(sourcePath.Length);

                d = Path.GetDirectoryName(t);
                if (!Directory.Exists(d))
                {
                    Directory.CreateDirectory(d);
                }

                File.Copy(f, t, true);
            }
        }

        public static void CopyDirectoryAndDeleteSourceDir(string sourceDir, string targetDir)
        {
            if (Directory.Exists(sourceDir))
            {
                if (Directory.Exists(targetDir))
                {
                    RemoveDir(targetDir);
                }

                if (!Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                CopyDir(sourceDir, targetDir);
                RemoveDir(sourceDir);
            }
        }

        public static string CheckPath(string sdk, string path)
        {
            string cfg_path = SDK_CONFIG_PATH;
            string bundleId = PlayerSettings.applicationIdentifier;
            string sourcePath = Path.Combine(Application.dataPath, cfg_path + sdk + "/" + bundleId + "/" + path);
            UnityEngine.Debug.Log("CheckPath 1 sourcePath: " + sourcePath);
            if (File.Exists(sourcePath) || Directory.Exists(sourcePath))
            {
                return sourcePath;
            }

            sourcePath = Path.Combine(Application.dataPath, cfg_path + sdk + "/default/" + path);
            UnityEngine.Debug.Log("CheckPath 2 sourcePath: " + sourcePath);
            if (File.Exists(sourcePath) || Directory.Exists(sourcePath))
            {
                return sourcePath;
            }

            UnityEngine.Debug.Log("CheckPath 3 sourcePath Not Exists!");
            return string.Empty;
        }

        public static void RemoveSDKDir(string sdk)
        {
            RemoveDir(Path.Combine(Application.dataPath, SDK_CONFIG_PATH + "/" + sdk));
            RemoveDir(Path.Combine(Application.dataPath, SDK_PATH + "/" + sdk));
        }

        public static string RemoveAutoConfigDir(string sdk, List<string> removeFirst = null)
        {
            string cfg_path = SDK_CONFIG_PATH;
            string autoDir = Path.Combine(Application.dataPath, cfg_path + "/" + sdk + "/" + SDK_CONFIG_USED);

            if (removeFirst != null)
            {
                for (int i = 0; i < removeFirst.Count; i++)
                {
                    string f = autoDir + "/" + removeFirst[i];
                    if (File.Exists(f))
                    {
                        File.Delete(f);
                    }
                }
            }

            RemoveDir(autoDir);

            return autoDir;
        }

        public static string EnsureAutoConfigDir(string sdk)
        {
            string cfg_path = SDK_CONFIG_PATH;
            string autoDir = Path.Combine(Application.dataPath, cfg_path + "/" + sdk + "/" + SDK_CONFIG_USED);

            if (!Directory.Exists(autoDir))
            {
                Directory.CreateDirectory(autoDir);
            }

            return autoDir;
        }

#if UNITY_IPHONE
        // add xcode lib
        public static void AddLibToXcodeProject(PBXProject inst, string targetGuid, string lib)
        {
            string fileGuid = inst.AddFile("usr/lib/" + lib, "Frameworks/" + lib, PBXSourceTree.Sdk);
            inst.AddFileToBuild(targetGuid, fileGuid);
        }
#endif
        #endregion

        #region Attribute

        static List<Type> GetAllTypes()
        {
            List<Type> allTypes = new List<Type>();
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            for (int i = 0; i < assemblies.Length; i++)
            {
                try
                {
                    allTypes.AddRange(assemblies[i].GetTypes());
                }
                catch (Exception)
                {
                }
            }

            return allTypes;
        }

        static void CallAllMethodWithAttribute(Type attributeType, params object[] args)
        {
            List<Type> types = GetAllTypes();

            foreach (var method in (
                from type in types
                from method in type.GetMethods(BindingFlags.Static | BindingFlags.Public)
                where method.IsDefined(attributeType, false)
                select method))
            {
                method.Invoke(null, args);
            }
        }
        #endregion
    }

    public class SDKBuildProcessAttribute : Attribute
    {
    };

    public class SDKPreprocessAttribute : Attribute
    {
    };

    public class SDKPostprocessAttribute : Attribute
    {
    };

    public class SDKOnPreProcessAttribute : Attribute
    {
    };
}