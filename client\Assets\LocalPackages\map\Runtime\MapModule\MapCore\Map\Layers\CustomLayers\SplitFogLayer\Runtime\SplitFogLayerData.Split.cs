﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerData : MapLayerData
    {
        //根据tile数组初始化分割fog
        public void InitSplit(int[] tileTypes)
        {
#if UNITY_EDITOR
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
#endif

            int leftBottomCornerMask = 8;
            int idx = 0;
            for (int i = 0; i < mRows; ++i)
            {
                for (int j = 0; j < mCols; ++j)
                {
                    if (tileTypes[idx] >= 0 && tileTypes[idx] < 16 &&
                        (tileTypes[idx] & leftBottomCornerMask) != leftBottomCornerMask)
                    {
                        var pos = FromCoordinateToWorldPosition(j, i);
                        PopTiles(pos);
                    }
                    ++idx;
                }
            }

#if UNITY_EDITOR
            //temp code
            var time = w.Stop();
            Debug.Log($"split fog init time: {time}");
#endif
        }

        void RecordSplitRect(int rectID, int x, int y)
        {
            FogRect r = new FogRect(rectID, x * mTileWidth, y * mTileHeight, x * mTileWidth + mTileWidth, y * mTileHeight + mTileHeight);
            bool found = false;
            for (int i = 0; i < mRecordedRects.Count; ++i)
            {
                if (rectID == mRecordedRects[i].id)
                {
                    mRecordedRects[i].Add(r);
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                mRecordedRects.Add(r);
            }
        }

        void SplitRecordedRects()
        {
            FogRect fog;
            for (int i = 0; i < mRecordedRects.Count; ++i)
            {
                mRectFogs.TryGetValue(mRecordedRects[i].id, out fog);
#if UNITY_EDITOR
                Debug.Assert(fog.isMerged == false);
#endif
                fog.Subtract(mRecordedRects[i], mSubtractResults);
                for (int k = 0; k < mSubtractResults.Count; ++k)
                {
                    mSubtractResults[k].id = mNextMeshID;
                    ++mNextMeshID;
#if UNITY_EDITOR
                    AssertIsFogRectTile(mSubtractResults[k]);
#endif
                    mCreatedRects.Add(mSubtractResults[k]);
                }
                mSubtractResults.Clear();
                OnRemoveRect(fog);
            }

            for (int i = 0; i < mCreatedRects.Count; ++i)
            {
                if (mCreatedRects[i].isMerged == false)
                {
                    MergeRect(mCreatedRects[i]);
                }
            }

            mCreatedRects.Clear();
            mRecordedRects.Clear();
        }

        void GetFogRectCoordinateRange(FogRect r, out int minX, out int minY, out int maxX, out int maxY)
        {
            minX = Mathf.FloorToInt(r.minX / mTileWidth);
            minY = Mathf.FloorToInt(r.minY / mTileHeight);
            maxX = minX + Mathf.FloorToInt(r.width / mTileWidth) - 1;
            maxY = minY + Mathf.FloorToInt(r.height / mTileHeight) - 1;
        }

        void MergeRect(FogRect r)
        {
#if true
            //merge bottom
            while (true)
            {
                int minX, minY, maxX, maxY;
                GetFogRectCoordinateRange(r, out minX, out minY, out maxX, out maxY);

                int minY1 = minY - 1;
                if (minY1 < 0)
                {
                    break;
                }
                int tileType = GetTileType(minX, minY1);
                if (tileType >= 16)
                {
                    var fog = GetRectFog(tileType);
                    if (!object.ReferenceEquals(fog, null))
                    {
                        if (Mathf.Approximately(fog.width, r.width) && Mathf.Approximately(fog.maxY, r.minY) &&
                            Mathf.Approximately(fog.minX, r.minX))
                        {
#if UNITY_EDITOR
                            AssertIsFogRectTile(fog);
                            AssertIsFogRectTile(r);
                            DebugFogRectTile(fog);
                            FogRect original = new FogRect(r.id, r.minX, r.minY, r.maxX, r.maxY);
#endif
                            //merge
                            r.Add(fog);
#if UNITY_EDITOR
                            AssertIsFogRectTile(r);
#endif
                            AssignRectTileTypes(r);
                            fog.isMerged = true;
                            OnRemoveRect(fog);
                        }
                        else
                        {
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }

            //merge top
            while (true)
            {
                int minX, minY, maxX, maxY;
                GetFogRectCoordinateRange(r, out minX, out minY, out maxX, out maxY);

                int maxY1 = maxY + 1;
                if (maxY1 >= mRows)
                {
                    break;
                }
                int tileType = GetTileType(maxX, maxY1);
                if (tileType >= 16)
                {
                    var fog = GetRectFog(tileType);
                    if (!object.ReferenceEquals(fog, null))
                    {
                        if (Mathf.Approximately(fog.width, r.width) && Mathf.Approximately(fog.minY, r.maxY) &&
                            Mathf.Approximately(fog.minX, r.minX))
                        {
#if UNITY_EDITOR
                            AssertIsFogRectTile(fog);
                            AssertIsFogRectTile(r);
                            DebugFogRectTile(fog);
                            FogRect original = new FogRect(r.id, r.minX, r.minY, r.maxX, r.maxY);
#endif
                            //merge
                            r.Add(fog);
#if UNITY_EDITOR
                            AssertIsFogRectTile(r);
#endif
                            AssignRectTileTypes(r);
                            fog.isMerged = true;
                            OnRemoveRect(fog);
                        }
                        else
                        {
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
#endif
            OnCreateRect(r);
        }

        //给rect的占地范围赋值
        void AssignRectTileTypes(FogRect r)
        {
            int minX, minY, maxX, maxY;
            GetFogRectCoordinateRange(r, out minX, out minY, out maxX, out maxY);
            int idx;
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; ++j)
                {
                    idx = i * mCols + j;
#if UNITY_EDITOR
                    //temp code
                    if (mTileTypes[idx] > 0 && mTileTypes[idx] < 16)
                    {
                        Debug.Assert(false);
                    }
#endif
                    mTileTypes[idx] = r.id;
                }
            }
        }

#if UNITY_EDITOR
        void DebugFogRectTile(FogRect r)
        {
            int minX, minY, maxX, maxY;
            GetFogRectCoordinateRange(r, out minX, out minY, out maxX, out maxY);
            int v = GetTileType(minX, minY);
            int idx;
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; j = j + 1)
                {
                    idx = i * mCols + j;
                    if (mTileTypes[idx] != v)
                    {
                        Debug.Assert(false, $"invalid fog tile rect {r.id} at x={j},y={i}");
                        return;
                    }
                }
            }
        }

        void AssertIsFogRectTile(FogRect r)
        {
            int minX, minY, maxX, maxY;
            GetFogRectCoordinateRange(r, out minX, out minY, out maxX, out maxY);

            int idx;
            for (int i = minY; i <= maxY; ++i)
            {
                for (int j = minX; j <= maxX; j = j + 1)
                {
                    idx = i * mCols + j;
                    if (mTileTypes[idx] > 0 && mTileTypes[idx] <= 15)
                    {
                        Debug.Assert(false, $"invalid fog tile rect {r.id} at x={j},y={i}");
                        return;
                    }
                }
            }
        }
#endif

        void OnCreateRect(FogRect newRect)
        {
#if UNITY_EDITOR
            if (mRectFogs.ContainsKey(newRect.id))
            {
                Debug.Assert(false);
            }
#endif
            //设置mTileTypes数组
            AssignRectTileTypes(newRect);

            mRectFogs.Add(newRect.id, newRect);

            var viewport = GetViewportRect();
            bool isVisible = newRect.Intersect(viewport.xMin, viewport.yMin, viewport.xMax, viewport.yMax);
            if (isVisible)
            {
                if (mCurrentLOD == 0)
                {
                    newRect.isIntersectedWithViewport = true;
                    mVisibleRectFogs.Add(newRect);
                    //通知view创建新rect
                    mCreateRectCallback(newRect);
                }
            }
        }

        void OnRemoveRect(FogRect fog)
        {
            //通知view删除rect
            mRemoveRectCallback(fog);

            //从mRectFogs中删除fogData
            mRectFogs.Remove(fog.id);
            for (int i = 0; i < mVisibleRectFogs.Count; ++i)
            {
                if (mVisibleRectFogs[i].id == fog.id)
                {
                    mVisibleRectFogs.RemoveAt(i);
                    break;
                }
            }
        }

        public FogRect GetRectFog(int id)
        {
            FogRect fog;
            mRectFogs.TryGetValue(id, out fog);
            return fog;
        }

        List<FogRect> mRecordedRects = new List<FogRect>();
        List<FogRect> mSubtractResults = new List<FogRect>();
        List<FogRect> mCreatedRects = new List<FogRect>();
        Dictionary<int, FogRect> mRectFogs = new Dictionary<int, FogRect>();
        System.Action<FogRect> mCreateRectCallback;
        System.Action<FogRect> mRemoveRectCallback;

        int mNextMeshID = 16;
    }
}
