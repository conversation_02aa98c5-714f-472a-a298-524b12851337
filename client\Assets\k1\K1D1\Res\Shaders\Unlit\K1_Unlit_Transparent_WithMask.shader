// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

// Unlit alpha-blended shader.
// - no lighting
// - no lightmap support
// - no per-material color

Shader "K1/Unlit/K1_Unlit_Transparent_WithMask" {
Properties {
    _MainTex ("Base (RGB) Trans (A)", 2D) = "white" {}
    _AreaTex("Area Tex", 2D) = "white" {}
    _AlphaFactor("Alpha",Range(0,10)) = 5
    _RedTeamColor("Red Team Color",Color) = (1,1,1,1)
    _BlueTeamColor("Blue Team Color",Color) = (1,1,1,1)
}

SubShader {
    Tags {"Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"}
    LOD 100

    ZWrite Off
    Blend SrcAlpha OneMinusSrcAlpha

    Pass {
        CGPROGRAM

            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                float2 texcoord : TEXCOORD0;
                float2 originalTexcoord:TEXCOORD1;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _AreaTex;
            float4 _AreaTex_ST;
            float _AlphaFactor;
            uniform float _NeedDisplaySafeArea[13];
            uniform float _SafeAreaTeam[13];
            float4 _RedTeamColor;
            float4 _BlueTeamColor;

            v2f vert (appdata_t v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
                o.originalTexcoord = v.texcoord;
                
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.texcoord);
                fixed4 areaCol = tex2D(_AreaTex, i.originalTexcoord);
                int areaIdx = floor(areaCol.r * 255.0 / 10.0 + 0.5) - 1;
                int areaDisplayState = _NeedDisplaySafeArea[areaIdx];
                int areaTeam = _SafeAreaTeam[areaIdx];
                col.a *= step(0.5,areaDisplayState) * step(0.00000001, areaCol.r) * _AlphaFactor;
                col.rgb = lerp(_RedTeamColor.rgb, _BlueTeamColor.rgb, step(0.5, areaTeam));
                return col;
            }
        ENDCG
    }
}

}
