﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class ActionExpandMapCollisionOutline : EditorAction
    {
        public ActionExpandMapCollisionOutline(int layerID, int dataID, PrefabOutlineType type, float radius)
        {
            mLayerID = layerID;
            mDataID = dataID;
            mOutlineType = type;
            mRadius = radius;
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            mOutlineVertices = data.GetOutlineVerticesCopy(type);
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            layer.ExpandOutline(mOutlineType, mDataID, mRadius);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            layer.SetOutline(mOutlineType, mDataID, mOutlineVertices);
            return true;
        }

        int mLayerID;
        int mDataID;
        PrefabOutlineType mOutlineType;
        float mRadius;
        List<Vector3> mOutlineVertices;
    }
}

#endif