﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    public enum TileOperationType {
        kCreateTile,
        kSelectTile,
        kRemoveTile,
    }

    [ExecuteInEditMode]
    [Black]
    public class SpriteTileLayerLogic : MapLayerLogic {
        public int selectedSpriteTemplateID { set; get; }
        public TileOperationType operationType { set; get; }

        public int GetSpriteTemplateIndex() {
            var editorMapData = Map.currentMap.data as EditorMapData;
            return editorMapData.GetSpriteTemplateIndex(selectedSpriteTemplateID);
        }

        public SpriteTileLayerData layerData {
            get {
                var layerData = Map.currentMap.FindObject(layerID) as SpriteTileLayerData;
                return layerData;
            }
        }
    }
}

#endif