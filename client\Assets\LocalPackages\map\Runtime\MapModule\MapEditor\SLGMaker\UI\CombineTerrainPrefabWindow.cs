﻿ 



 
 

﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Text;

namespace TFW.Map
{
    public class CombineTerrainPrefabWindow : EditorWindow
    {
        private void OnEnable()
        {
        }

        void OnGUI()
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Terrain Prefab Count", mPrefabCount.ToString());
            if (GUILayout.Button("Change"))
            {
                var dlg = EditorUtils.CreateInputDialog("Set Terrain Prefab Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Prefab Count", "", "32"),
                };
                dlg.Show(items, OnClickChangeCount);
            }
            EditorGUILayout.EndHorizontal();

            if (mPrefabCount > 0)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.TextField("Material Output Folder", mMaterialOutputFolder);
                if (GUILayout.Button("Select"))
                {
                    mMaterialOutputFolder = EditorUtility.SaveFolderPanel("Select material output folder", "", "");
                }
                EditorGUILayout.EndHorizontal();
            }

            for (int i = 0; i < mPrefabCount; ++i)
            {
                var prefab = EditorGUILayout.ObjectField(mPrefabNames[i], mPrefabs[i], typeof(GameObject), true, null) as GameObject;
                if (prefab != null && PrefabUtility.GetPrefabAssetType(prefab) == PrefabAssetType.Regular)
                {
                    mPrefabs[i] = prefab;
                }
            }

            if (mPrefabCount > 0)
            {
                if (!string.IsNullOrEmpty(mMaterialOutputFolder))
                {
                    if (GUILayout.Button("Combine"))
                    {
                        List<GameObject> validPrefabs = new List<GameObject>();
                        for (int k = 0; k < mPrefabs.Length; ++k)
                        {
                            if (mPrefabs[k] != null)
                            {
                                validPrefabs.Add(mPrefabs[k]);
                            }
                        }
                        TerrainCombiner.Combine(validPrefabs, mMaterialOutputFolder);
                    }
                }
            }
        }

        bool OnClickChangeCount(List<InputDialog.Item> param)
        {
            int prefabCount;
            Utils.ParseInt((param[0] as InputDialog.StringItem).text, out prefabCount);
            if (prefabCount <= 0)
            {
                return false;
            }

            if (mPrefabNames == null || mPrefabs == null || prefabCount != mPrefabs.Length)
            {
                GameObject[] newPrefabs = new GameObject[prefabCount];
                int n = Mathf.Min(prefabCount, mPrefabCount);
                for (int i = 0; i < n; ++i)
                {
                    newPrefabs[i] = mPrefabs[i];
                }

                mPrefabs = newPrefabs;
                mPrefabCount = prefabCount;

                mPrefabNames = new string[prefabCount];
                for (int k = 0; k < prefabCount; ++k)
                {
                    mPrefabNames[k] = $"Prefab {k}";
                }
            }

            return true;
        }

        int mPrefabCount = 0;
        GameObject[] mPrefabs;
        string[] mPrefabNames;
        string mMaterialOutputFolder;
    }
}


#endif