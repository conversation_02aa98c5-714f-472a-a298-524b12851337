﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionSetCollisionAreaType : EditorAction
    {
        public ActionSetCollisionAreaType(int layerID, MapCollisionData data, int type)
        {
            mLayerID = layerID;
            mOldType = data.type;
            mNewType = type;
            mCollisionDataID = data.id;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.SetCollisionAreaType(mCollisionDataID, mNewType);
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as MapCollisionLayer;
            if (layer == null)
            {
                return false;
            }

            return layer.SetCollisionAreaType(mCollisionDataID, mOldType);
        }

        int mCollisionDataID;
        int mLayerID;
        int mOldType;
        int mNewType;
    }
}


#endif