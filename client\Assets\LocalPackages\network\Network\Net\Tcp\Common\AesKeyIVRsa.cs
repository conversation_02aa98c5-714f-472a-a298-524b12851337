﻿#if !UNITY_WEBGL
using Crypto;
using Helper;
using Net.Common;
using System.Net.Sockets;
using UnityEngine;

namespace Net.Tcp.Common
{
    class AesKeyIVRsa : IIOCP
    {
        enum ReadState
        {
            Head = 0,
            Body = 1,
        }
        public AesKeyIVRsa(Socket sock, IHandlerServer handler, IServer server, string rsa, byte cccflag)
        {
            m_Handler = handler;
            m_Server = server;
            m_RsaKey = rsa;
            m_CCCFlag = cccflag;
            Socket = sock;
            m_ReadState = ReadState.Head;
            IOCPConn.Start(this);
        }

        IHandlerServer m_Handler;
        IServer m_Server;
        string m_RsaKey;
        byte m_CCCFlag;
        ReadState m_ReadState;

        public Socket Socket { get; private set; }

        public void IOCPInitialize(SocketAsyncEventArgs e)
        {
            e.SetBuffer(0, 4);
        }
        public bool IOCPReceived(SocketAsyncEventArgs e)
        {
            if (e.UserToken != this) return false;
            var count = e.Count - e.BytesTransferred;
            if (m_ReadState == ReadState.Head)
            {
                if (count == 0)
                {
                    //解包大小
                    var len = NetHelper.ToInt32(e.<PERSON>uffer, 0);
                    if (len != e.Buffer.Length)
                    {
                        BufferManager.Free(e);
                        var buffer = new byte[len];
                        e.SetBuffer(buffer, 0, len);
                    }
                    else
                    {
                        e.SetBuffer(0, len);
                    }
                    m_ReadState = ReadState.Body;
                }
                else
                {
                    var offset = e.BytesTransferred + e.Offset;
                    e.SetBuffer(offset, count);
                }
            }
            else
            {
                if (count == 0)
                {
                    //解Aes Key IV
                    byte[] bytes;
                    if (!Rsa.Decrypt(m_RsaKey, e.Buffer, out bytes))
                    {
                        Debug.LogError($"Rsa Decrypt Error {m_RsaKey}");
                        IOCPConn.Close(e);
                        return false;
                    }
                    if (!AesKeyIV.Check(bytes))
                    {
                        Debug.LogError($"Aes Key IV len error {bytes.Length}");
                        IOCPConn.Close(e);
                        return false;
                    }
                    else
                    {
                        var kiv = AesKeyIV.GenKeyIV();
                        AesKeyIV.SendAesKeyIVAes(Socket, bytes, kiv);
                        m_Handler.HandleAcceptConnected((h, c) => new TCPConnection(Socket, h, m_Server.Pack, c, kiv, m_CCCFlag));
                        return false;
                    }
                }
                else
                {
                    var offset = e.BytesTransferred + e.Offset;
                    e.SetBuffer(offset, count);
                }
            }
            return true;
        }
        public void IOCPClose()
        {
            IOCPConn.Close(Socket);
            Socket = null;
        }
    }
}

#endif