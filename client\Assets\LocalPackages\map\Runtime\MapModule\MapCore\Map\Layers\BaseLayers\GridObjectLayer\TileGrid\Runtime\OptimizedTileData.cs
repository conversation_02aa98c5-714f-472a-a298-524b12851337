﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //grid model layer不再使用model template,而是使用更优化的数据结构
    //子prefab的transform信息
    public class ChildPrefabTransform2
    {
        public float x;
        public float z;
        //prefab init info表的索引,大部分装饰物都使用的同一个prefab,所以把共享信息集中保存
        public short prefabInitInfoIndex;
        public TileObjectType objectType;

        public Vector3 GetPosition(PrefabInitInfo1[] table)
        {
            return new Vector3(x, table[prefabInitInfoIndex].y, z);
        }

        public Vector3 GetScale(PrefabInitInfo1[] table)
        {
            return table[prefabInitInfoIndex].scale;
        }

        public Quaternion GetRotation(PrefabInitInfo1[] table)
        {
            return table[prefabInitInfoIndex].rotation;
        }

        public Rect GetLocalBoundsInPrefab(PrefabInitInfo1[] table)
        {
            var objPos = GetPosition(table);
            return new Rect(
                objPos.x + table[prefabInitInfoIndex].boundsMinX,
                objPos.z + table[prefabInitInfoIndex].boundsMinZ,
                table[prefabInitInfoIndex].boundsWidth,
                table[prefabInitInfoIndex].boundsHeight
                );
        }
    }

    //这个数据是可以被多个tile共享的
    public class OptimizedTileData
    {
        public class LOD
        {
            public LOD(string prefabPath, ChildPrefabTransform2[] children)
            {
                this.originalPrefabPath = prefabPath;
                this.childPrefabs = children;
            }

            //可能是fake prefab也可能是180m unity prefab, only used in unity editor
            public string originalPrefabPath;
            public ChildPrefabTransform2[] childPrefabs;
        }

        public OptimizedTileData(int id)
        {
            this.id = id;
        }

        public int id { get; private set; }
        //每级lod下每个物体信息
        public List<LOD> objectsOfEachLOD = new List<LOD>();
    }

    public class ObstacleDisplayInfo
    {
        public OptimizedTileData tileData;
        public Vector3 position;
    }
}
