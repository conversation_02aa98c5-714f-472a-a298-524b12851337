﻿//混合扭曲流动效果
Shader "K1/Particle/K1_Particle_Magic_Line" 
{

    Properties 
    {
       
        _TintColor ("TintColor", Color) = (0.5,0.5,0.5,0.5)
        _MainTex1 ("MainTex1", 2D) = "white" {}
        _MainTex_Uspeed1 ("MainTex Uspeed1", Float ) = 0
        _MainTex_Vspeed1 ("MainTex Vspeed1", Float ) = 0
        _RotateSpeed1("RotateTex1",float) = 0

        _MainTex2("MainTex2", 2D) = "white" {}
        _MainTex_Uspeed2("MainTex Uspeed2", Float) = 0
        _MainTex_Vspeed2("MainTex Vspeed2", Float) = 0
        _RotateSpeed2("RotateTex2",float) = 0

        _LightSpeed("LightSpeed", Range(1,10)) = 5
        _MainTex3("MainTex3", 2D) = "white" {}
        _MainTex_Uspeed3("MainTex Uspeed3", Float) = 0
        _MainTex_Vspeed3("MainTex Vspeed3", Float) = 0
        _RotateSpeed3("RotateTex3",float) = 0

        _MainTex4("MainTex4", 2D) = "white" {}
        _MainTex_Uspeed4("MainTex Uspeed4", Float) = 0
        _MainTex_Vspeed4("MainTex Vspeed4", Float) = 0
        _RotateSpeed4("RotateTex4",float) = 0


        _LightingBeamColor("LightingBeamColor", Color) = (0.5,0.5,0.5,0.5)
        _MainTexLightingBeam("MainTexLightingBeam", 2D) = "white" {}
        


        [Header(Blend Model)]
        [Enum(UnityEngine.Rendering.BlendMode)] _SrcBlend ("SrcBlend Mode", Float) = 5
        [Enum(UnityEngine.Rendering.BlendMode)] _DstBlend ("DstBlend Mode", Float) = 10
        [Enum(Off,0,On,1)] _ZWrite ("ZWrite", Float) = 0   //特效用不到
        [Enum(UnityEngine.Rendering.CompareFunction)] _ZTest ("ZTest", Float) = 2
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", float) = 2
        
    }

    SubShader
    {
        Tags { "Queue"="Transparent+500" "IgnoreProjector"="True" "RenderType"="Transparent" "PreviewType"="Plane" }

        Blend [_SrcBlend] [_DstBlend]
        ZWrite[_ZWrite]
        ZTest[_ZTest]
        Cull[_Cull]
        Lighting Off

        Pass 
        {

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #include "UnityCG.cginc"

            float4 _TintColor;
            sampler2D _MainTex1;
            float4 _MainTex1_ST;
            float _RotateSpeed1;
            float _MainTex_Uspeed1;
            float _MainTex_Vspeed1;

            sampler2D _MainTex2;
            float4 _MainTex2_ST;
            float _RotateSpeed2;
            float _MainTex_Uspeed2;
            float _MainTex_Vspeed2;

            sampler2D _MainTex3;
            float4 _MainTex3_ST;
            float _RotateSpeed3;
            float _MainTex_Uspeed3;
            float _MainTex_Vspeed3;
            float _LightSpeed;
            sampler2D _MainTex4;
            float4 _MainTex4_ST;
            float _RotateSpeed4;
            float _MainTex_Uspeed4;
            float _MainTex_Vspeed4;

            float4 _LightingBeamColor;
            sampler2D _MainTexLightingBeam;
            float4 _MainTexLightingBeam_ST;

            struct appdata_t 
            {
                float4 vertex : POSITION;
                fixed4 color : COLOR;
                
                float2 texcoord : TEXCOORD0;
                float2 texcoord2 : TEXCOORD1;
               
            };

            struct v2f 
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
               
                float4 texcoord : TEXCOORD0;
                float4 texcoord2 : TEXCOORD1;
                float4 texcoord3 : TEXCOORD2;
                float4 texcoord4 : TEXCOORD3;
                float4 lightingBeamColor : TEXCOORD4;
            };

            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.color = v.color * _TintColor;
                o.lightingBeamColor = v.color * _LightingBeamColor;
                o.texcoord.xy = TRANSFORM_TEX(v.texcoord, _MainTex1) + float2(_Time.y * _MainTex_Uspeed1, _Time.y * _MainTex_Vspeed1);
                o.texcoord2.xy = TRANSFORM_TEX(v.texcoord, _MainTex2) + float2(_Time.y * _MainTex_Uspeed2, _Time.y * _MainTex_Vspeed2);
                o.texcoord3.xy = TRANSFORM_TEX(v.texcoord, _MainTex3);
                o.texcoord3.y += floor(_Time.y * _LightSpeed) % 4 * 0.25;
                o.texcoord4.xy = TRANSFORM_TEX(v.texcoord, _MainTex4);
                o.texcoord4.y += floor(_Time.y * _LightSpeed) % 5 * 0.2;
                return o;
            }

            float4 frag (v2f i) : SV_Target
            {


                float2 uvMain1 = i.texcoord.xy - float2(0.5,0.5);
                uvMain1 = float2(uvMain1.x*cos(_RotateSpeed1/(180/3.1416)) - uvMain1.y*sin(_RotateSpeed1/(180/3.1416)),uvMain1.x*sin(_RotateSpeed1 /(180/3.1416)) + uvMain1.y*cos(_RotateSpeed1/(180/3.1416)) );
                uvMain1 += float2(0.5,0.5);
                float2 uvMain2 = i.texcoord.xy - float2(0.5,0.5);
                uvMain2 = float2(uvMain2.x*cos(_RotateSpeed2/(180/3.1416)) - uvMain2.y*sin(_RotateSpeed2/(180/3.1416)),uvMain2.x*sin(_RotateSpeed2 /(180/3.1416)) + uvMain2.y*cos(_RotateSpeed2/(180/3.1416)) );
                uvMain2 += float2(0.5,0.5);
                float2 uvMain3 = i.texcoord3.xy - float2(0.5, 0.5);
                uvMain3 = float2(uvMain3.x * cos(_RotateSpeed3 / (180 / 3.1416)) - uvMain3.y * sin(_RotateSpeed3 / (180 / 3.1416)), uvMain3.x * sin(_RotateSpeed3 / (180 / 3.1416)) + uvMain3.y * cos(_RotateSpeed3 / (180 / 3.1416)));
                uvMain3 += float2(0.5, 0.5);
                
                float2 uvMain4 = i.texcoord4.xy - float2(0.5, 0.5);
                uvMain4 = float2(uvMain4.x * cos(_RotateSpeed4 / (180 / 3.1416)) - uvMain4.y * sin(_RotateSpeed4 / (180 / 3.1416)), uvMain4.x * sin(_RotateSpeed4 / (180 / 3.1416)) + uvMain4.y * cos(_RotateSpeed4 / (180 / 3.1416)));
                uvMain4 += float2(0.5, 0.5);

                float4 col = i.color;
                float4 lightingBeamCol = i.lightingBeamColor * tex2D(_MainTexLightingBeam, i.texcoord);

                col.rgb *= tex2D(_MainTex1, uvMain1);
                col.rgb *= tex2D(_MainTex2, uvMain2);
                col.rgb += tex2D(_MainTex3, uvMain3);
                col.rgb += tex2D(_MainTex4, uvMain4);
                col.rgb += lightingBeamCol;
                //#endif
                return col;
            }
            ENDCG
        }
    }

}
