
using UnityEngine;
using System.Collections.Generic;
using Newtonsoft.Json;


/**
 *  unity调用平台base类
 *  
 *  具体平台，可以通过继承该base类，按需 override 复写
 */
namespace maxsdk
{

    public abstract class MaxSDKUnitySupportBase
    {

        public abstract int GetSDKType();

        public abstract void SetListener(MaxSDKListener listener);

        public abstract void Init(MaxSDKInitInfo info);

        public abstract void Login(MaxSDKLoginInfo info);

        public abstract void Logout(MaxSDKLogoutInfo info);

        public abstract void Pay(MaxSDKPayInfo info);

        public abstract void ReportRoleInfo(MaxSDKRoleInfo info);

        public abstract void SwitchAccount(MaxSDKSwitchAccountInfo info);

        public abstract void ReportEvent(MaxSDKReportEventInfo info); //埋点事件上报


        public abstract void OpenActionExt(MaxSDKActionInfo info);

        public abstract void Share(MaxSDKShareInfo info);

        public abstract string DispatchSync(MaxSDKDispatchInfo info);

        public abstract void DispatchASync(MaxSDKDispatchInfo info);

        public abstract bool IsActionSupported(int type);

        /* --------------------------- 我是一条华丽的分割线 ----------------------------- */


        public virtual string GetAppConfig()
        {
            return "";
        }

        public virtual void ExitGame(MaxSDKExitInfo info)
        {

        }


        public virtual string DispatchSync(string methodName, MaxSDKBaseInfo info)
        {
            return "";
        }

        public virtual void DispatchASync(string methodName, MaxSDKBaseInfo info)
        {

        }

    }
}

