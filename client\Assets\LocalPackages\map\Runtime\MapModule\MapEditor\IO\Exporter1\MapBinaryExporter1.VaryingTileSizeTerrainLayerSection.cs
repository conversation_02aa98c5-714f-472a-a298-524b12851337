﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        bool SaveVaryingTileSizeTerrainLayer(BinaryWriter writer, VaryingTileSizeTerrainLayer layer)
        {
            BeginSection(MapDataSectionType.VaryingTileSizeTerrainLayer, writer);
            //版本号
            writer.Write(VersionSetting.VaryingTileSizeTerrainLayerStructVersion);

            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return false;
            }

            //-----------------version 1 start------------------------------
            int horizontalTileCount = layer.layerData.horizontalTileCount;
            int verticalTileCount = layer.layerData.verticalTileCount;
            float tileWidth = layer.layerData.tileWidth;
            float tileHeight = layer.layerData.tileHeight;
            var terrainLayerData = layer.layerData as VaryingTileSizeTerrainLayerData;
            VaryingTileSizeTerrainTileData[] tiles = terrainLayerData.tiles;

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            writer.Write(verticalTileCount);
            writer.Write(horizontalTileCount);
            writer.Write(tileWidth);
            writer.Write(tileHeight);
            writer.Write(terrainLayerData.useGeneratedLOD);
            Utils.WriteVector3(writer, layer.layerView.root.transform.position);

            //save big tiles
            var layerData = layer.layerData as VaryingTileSizeTerrainLayerData;
            var bigTiles = layerData.bigTiles;
            int bigTileCount = bigTiles.Count;
            writer.Write(bigTileCount);
            for (int i = 0; i < bigTileCount; ++i)
            {
                writer.Write(bigTiles[i].x);
                writer.Write(bigTiles[i].y);
                writer.Write(bigTiles[i].width);
                writer.Write(bigTiles[i].height);
            }

            for (int i = 0; i < verticalTileCount; ++i)
            {
                for (int j = 0; j < horizontalTileCount; ++j)
                {
                    int idx = i * horizontalTileCount + j;
                    var tile = tiles[idx];
                    bool hasTile = tile != null;
                    writer.Write(hasTile);
                    if (hasTile)
                    {
                        SaveTerrainTileDataV1(writer, tile, bigTiles);
                    }
                }
            }

            //save map layer lod config
            SaveTerrainLayerLayerLODConfigV1(writer, layer.layerData);
            //-----------------version 1 end------------------------------

            return true;
        }

        void SaveTerrainLayerLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write(c.useRenderTexture);
                writer.Write((int)c.flag);
                writer.Write(c.terrainLODTileCount);
            }
        }

        void SaveTerrainTileDataV1(BinaryWriter writer, VaryingTileSizeTerrainTileData tileData, List<VaryingTileSizeTerrainLayerData.BigTileData> bigTiles)
        {
            mIDExport.Export(writer, tileData.id);
            mIDExport.Export(writer, tileData.GetModelTemplateID());
            writer.Write(tileData.type);
            writer.Write(tileData.index);

            int bigTileIndex = -1;
            if (tileData.bigTile != null)
            {
                bigTileIndex = bigTiles.IndexOf(tileData.bigTile);
            }
            writer.Write(bigTileIndex);
        }
    }
}

#endif