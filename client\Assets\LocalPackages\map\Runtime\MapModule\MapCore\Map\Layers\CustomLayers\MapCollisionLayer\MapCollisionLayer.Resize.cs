﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapCollisionLayer : MapLayerBase
    {
        public void MoveAndResize(float newLayerWidth, float newLayerHeight, ResizeAlignment alignment)
        {
            float oldLayerWidth = mLayerData.GetLayerWidthInMeter(0);
            float oldLayerHeight = mLayerData.GetLayerHeightInMeter(0);
            float dx = newLayerWidth - oldLayerWidth;
            float dz = newLayerHeight - oldLayerHeight;
            if (Mathf.Approximately(dx, 0) && Mathf.Approximately(dz, 0))
            {
                return;
            }

            float offsetX = 0;
            float offsetZ = 0;
            if (alignment == ResizeAlignment.Move)
            {
                offsetX = dx * 0.5f;
                offsetZ = dz * 0.5f;
            }

            List<MapCollisionData> allCollisions = new List<MapCollisionData>();
            mLayerData.GetAllCollisions(allCollisions);
            var offset = new Vector3(offsetX, 0, offsetZ);
            for (int i = 0; i < allCollisions.Count; ++i)
            {
                MoveObject(PrefabOutlineType.NavMeshObstacle, allCollisions[i].id, offset);
                MoveObject(PrefabOutlineType.ObjectPlacementObstacle, allCollisions[i].id, offset);
            }

            mLayerData.SetSize(newLayerWidth, newLayerHeight);
        }
    }
}


#endif