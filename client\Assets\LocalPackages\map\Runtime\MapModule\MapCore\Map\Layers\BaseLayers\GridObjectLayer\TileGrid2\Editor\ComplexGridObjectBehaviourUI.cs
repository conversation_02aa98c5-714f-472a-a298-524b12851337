﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [CustomEditor(typeof(ComplexGridObjectBehaviour))]
    [CanEditMultipleObjects]
    public partial class ComplexGridObjectBehaviourUI : UnityEditor.Editor
    {
        private void OnEnable()
        {
            if (Map.currentMap != null)
            {
                var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as ComplexGridModelLayer;
                if (layer != null)
                {
                    string[] tagNames = layer.GetObjectTags();
                    var behaviour = target as ComplexGridObjectBehaviour;
                    for (int i = 0; i < tagNames.Length; ++i)
                    {
                        if (behaviour.objectTag == tagNames[i])
                        {
                            mObjectTagIndex = i;
                            break;
                        }
                    }

                    if (mObjectTagIndex < 0)
                    {
                        behaviour.objectTag = "";
                    }

                    mObjectTagNames = tagNames;
                }
            }
        }

        public override void OnInspectorGUI()
        {
            if (mObjectTagNames != null)
            {
                int newIndex = EditorGUILayout.Popup("Object Tags", mObjectTagIndex, mObjectTagNames);
                if (newIndex != mObjectTagIndex)
                {
                    mObjectTagIndex = newIndex;
                    for (int i = 0; i < targets.Length; ++i)
                    {
                        var behaviour = targets[i] as ComplexGridObjectBehaviour;
                        behaviour.objectTag = mObjectTagNames[mObjectTagIndex];
                    }
                }
                
                if (GUILayout.Button("Change To 2D"))
                {
                    ChangeModel(true);
                }

                if (GUILayout.Button("Change To 3D"))
                {
                    ChangeModel(false);
                }
            }
        }

        void ChangeModel(bool useRenderTextureModel)
        {
            var layer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as ComplexGridModelLayer;
            for (int i = 0; i < targets.Length; ++i)
            {
                var behaviour = targets[i] as ComplexGridObjectBehaviour;
                layer.ChangeObjectModel(behaviour.objectID, useRenderTextureModel);
            }
        }

        string[] mObjectTagNames;
        int mObjectTagIndex = -1;
    }
}


#endif