﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.IO;

namespace TFW.Map
{
    //修改一个prefab可以连同修改其他prefab
    public partial class ObjectPlacementEditor : EditorWindow
    {
        class ChildPrefabInfo
        {
            public ChildPrefabInfo(string path, string tag, int layer, Vector3 position, Vector3 scaling, Quaternion rotation)
            {
                this.path = path;
                this.tag = tag;
                this.layer = layer;
                this.position = position;
                this.scaling = scaling;
                this.rotation = rotation;
            }

            public string path;
            public string tag;
            public int layer;
            public Vector3 scaling;
            public Quaternion rotation;
            public Vector3 position;
        }

        class LODInfo
        {
            public LODInfo(int lod, string prefabPath, List<ChildPrefabInfo> childPrefabs)
            {
                mLOD = lod;
                mPrefabPath = prefabPath;
                mChildrenPrefab = childPrefabs;
            }

            public bool FindAtPosition(Vector3 position)
            {
                int n = mChildrenPrefab.Count;
                for (int i = 0; i < n; ++i)
                {
                    if (mChildrenPrefab[i].position == position)
                    {
                        return true;
                    }
                }
                return false;
            }

            public void Add(List<ChildPrefabInfo> objects)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabPath);
                var prefabInstance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                for (int i = 0; i < objects.Count; ++i)
                {
                    var childPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(objects[i].path);
                    Debug.Assert(childPrefab != null);
                    var childPrefabInstance = PrefabUtility.InstantiatePrefab(childPrefab) as GameObject;
                    childPrefabInstance.transform.position = objects[i].position;
                    childPrefabInstance.transform.rotation = objects[i].rotation;
                    childPrefabInstance.transform.localScale = objects[i].scaling;
                    childPrefabInstance.tag = objects[i].tag;
                    childPrefabInstance.layer = objects[i].layer;
                    childPrefabInstance.transform.SetParent(prefabInstance.transform, true);
                }
                PrefabUtility.SaveAsPrefabAsset(prefabInstance, mPrefabPath);
                Object.DestroyImmediate(prefabInstance);

                mChildrenPrefab.AddRange(objects);
            }

            public void Remove(List<ChildPrefabInfo> objects)
            {
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(mPrefabPath);
                for (int i = 0; i < objects.Count; ++i)
                {
                    int index = childrenPrefab.IndexOf(objects[i]);
                    Debug.Assert(index >= 0);
                    Object.DestroyImmediate(prefab.transform.GetChild(index).gameObject, true);
                    mChildrenPrefab.RemoveAt(index);
                }
                EditorUtility.SetDirty(prefab);
                PrefabUtility.SavePrefabAsset(prefab);
            }

            public List<ChildPrefabInfo> childrenPrefab { get { return mChildrenPrefab; } }
            public int lod { get { return mLOD; } }

            int mLOD;
            string mPrefabPath;
            List<ChildPrefabInfo> mChildrenPrefab;
        }

        void OnPrefabStageClosing(UnityEditor.SceneManagement.PrefabStage stage)
        {
            mLODInfo = new List<LODInfo>();
            Debug.Log("OnPrefabStageClosing");
        }

        void OnPrefabStageOpened(UnityEditor.SceneManagement.PrefabStage stage)
        {
            var prefabPath = stage.prefabAssetPath;

            CheckExistedLODs(prefabPath);

            Debug.Log($"OnPrefabStageOpened, LOD: {mLODInfo.Count}");
        }

        //检测这个prefab有哪些lod,只有编辑器模式下才会运行
        void CheckExistedLODs(string prefabPath)
        {
            //注意,只有以lod后缀结尾的文件路径才判断是否自动切换lod
            var idx = prefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (idx == -1)
            {
                return;
            }
            var lodSubstring = prefabPath.Substring(idx);
            lodSubstring = Utils.GetPathName(lodSubstring, false);
            Regex rx = new Regex(@"_lod\d+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            if (rx.Match(lodSubstring).Length != lodSubstring.Length)
            {
                return;
            }

            var lodStr = lodSubstring.Remove(0, 4);
            int.TryParse(lodStr, out mCurrentLOD);

            var ext = Utils.GetExtension(prefabPath);

            mLODInfo = new List<LODInfo>();
            List<int> existedLODs = new List<int>();
            var prefixIdx = prefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (prefixIdx != -1)
            {
                var prefixString = prefabPath.Substring(0, prefixIdx + MapCoreDef.MAP_PREFAB_LOD_PREFIX.Length);

                int nLods = 100;
                for (int i = 0; i < nLods; ++i)
                {
                    var lodPrefabPath = prefixString + i.ToString() + "." + ext;
                    if (File.Exists(lodPrefabPath))
                    {
                        existedLODs.Add(i);
                    }
                }

                if (existedLODs.Count > 0)
                {
                    for (int i = 0; i < existedLODs.Count; ++i)
                    {
                        mLODInfo.Add(CalculateLODInfo(i, ext, prefixString, existedLODs));
                    }
                }
            }
        }

        LODInfo CalculateLODInfo(int lodLevel, string ext, string prefabPathPrefix, List<int> existedLODs)
        {
            int lodIdx = 0;
            for (int i = 0; i < existedLODs.Count; ++i)
            {
                if (existedLODs[i] <= lodLevel)
                {
                    lodIdx = i;
                }
            }

            var prefabPath = string.Format("{0}{1}.{2}", prefabPathPrefix, existedLODs[lodIdx], ext);
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            var transform = prefab.transform;
            int n = transform.childCount;
            List<ChildPrefabInfo> childrenPrefab = new List<ChildPrefabInfo>(n);
            for (int i = 0; i < n; ++i)
            {
                var child = transform.GetChild(i).gameObject;
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(child);
                string assetPath = AssetDatabase.GetAssetPath(childPrefab);
                var prefabInfo = new ChildPrefabInfo(assetPath, child.tag, child.layer, child.transform.position, child.transform.localScale, child.transform.rotation);
                childrenPrefab.Add(prefabInfo);
            }

            LODInfo info = new LODInfo(lodIdx, prefabPath, childrenPrefab);
            return info;
        }

        //将某一个lod的prefab修改应用到其他lod,删除从当前lod到最大lod的物体,添加从lod0到当前lod的物体
        void ApplyModificationToOtherLODs()
        {
            var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
            if (stage == null)
            {
                return;
            }

            if (mLODInfo.Count == 0)
            {
                var prefabPath = stage.prefabAssetPath;
                CheckExistedLODs(prefabPath);
            }

            var prefab = stage.prefabContentsRoot;
            for (int i = 0; i < mCurrentLOD; ++i)
            {
                AddDifference(mLODInfo[i], prefab);
            }
            for (int i = mCurrentLOD + 1; i < mLODInfo.Count; ++i)
            {
                SubtractDifference(mLODInfo[i], prefab);
            }

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        void AddDifference(LODInfo lowerLOD, GameObject prefab)
        {
            var transform = prefab.transform;
            int n = transform.childCount;
            List<ChildPrefabInfo> addedPrefabInfo = new List<ChildPrefabInfo>();
            for (int i = 0; i < n; ++i)
            {
                var child = transform.GetChild(i).gameObject;
                var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(child);
                if (childPrefab != null)
                {
                    if (lowerLOD.FindAtPosition(child.transform.position) == false)
                    {
                        string path = AssetDatabase.GetAssetPath(childPrefab);
                        var position = child.transform.position;
                        var rotation = child.transform.rotation;
                        var scaling = child.transform.localScale;
                        var tag = child.tag;
                        var layer = child.layer;
                        ChildPrefabInfo prefabInfo = new ChildPrefabInfo(path, tag, layer, position, scaling, rotation);
                        addedPrefabInfo.Add(prefabInfo);
                    }
                }
            }

            lowerLOD.Add(addedPrefabInfo);
        }

        void SubtractDifference(LODInfo higherLOD, GameObject currentLODPrefab)
        {
            List<ChildPrefabInfo> removedPrefabInfo = new List<ChildPrefabInfo>();

            var higherLODChildrenPrefab = higherLOD.childrenPrefab;
            int n = higherLODChildrenPrefab.Count;
            for (int i = 0; i < n; ++i)
            {
                if (FindAtPosition(currentLODPrefab, higherLODChildrenPrefab[i].position) == false)
                {
                    removedPrefabInfo.Add(higherLODChildrenPrefab[i]);
                }   
            }

            higherLOD.Remove(removedPrefabInfo);
        }

        bool FindAtPosition(GameObject currentLODPrefab, Vector3 position)
        {
            var transform = currentLODPrefab.transform;
            int n = transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                var child = transform.GetChild(i);
                if (child.position == position)
                {
                    return true;
                }
            }
            return false;
        }

        List<LODInfo> mLODInfo = new List<LODInfo>();
        int mCurrentLOD;
    }
}
#endif