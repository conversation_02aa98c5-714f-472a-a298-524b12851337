﻿ 



 
 

using System.Collections.Generic;

namespace TFW.Map.Geo
{
    public static class GeoUtils
    {
        // 计算叉积
        public static int CrossProduct(Vertice p1, Vertice p2, Vertice p3)
        {
            var ax = p2.Coord.X - p1.Coord.X;
            var ay = p2.Coord.Z - p1.Coord.Z;
            var bx = p3.Coord.X - p2.Coord.X;
            var by = p3.Coord.Z - p2.Coord.Z;
            var cp = ax * by - ay * bx;
            if (cp > 0)
            {
                return 1;
            }
            else if (cp < 0)
            {
                return -1;
            }
            else
            {
                return 0;
            }
        }

        // 截断边为一个单位向量
        public static Coord TruncEdge(Coord start, Coord end, float length)
        {
            // 生成拐点数组
            var vec0 = new Vector(start, end);
            // 单位向量
            vec0 = vec0.Trunc(length / vec0.Length());

            return vec0.ToCoord(start);
        }

        // 查看是否为凸多边形
        public static bool IsConvex(List<Vertice> vertices)
        {
            var numPoints = vertices.Count;

            var negativeFlag = false;
            var positiveFlag = false;
            for (int i = 0; i < numPoints; i++)
            {
                var curvec = new Vector(vertices[i].Coord, vertices[(i + 1) % numPoints].Coord);

                var vec2next = new Vector(vertices[i].Coord, vertices[(i + 2) % numPoints].Coord);
                if (curvec.Cross(vec2next) > 0)
                {
                    positiveFlag = true;
                }
                else
                {
                    negativeFlag = true;
                }
            }
            return positiveFlag != negativeFlag;
        }


        // 获得圆圈周围的点
        // startCoord: 圆上一点
        // centerCoord: 圆心
        // endCoord: 圆外点
        public static Coord[] GetCoordsAround(Coord startCoord, Coord endCoord, Coord centerCoord)
        {
            var centerVector = new Vector(centerCoord, startCoord);

            var endVector = new Vector(centerCoord, endCoord);

            // 向量夹角
            var angle = centerVector.GetAngle(endVector);
            var radius = CalDstCoordToCoord(startCoord, centerCoord);
            // 切线角度
            var cutAngle = GetCutOffCoordAngle(endCoord, centerCoord, radius);

            angle -= cutAngle;
            // 叉积确定方向
            if (centerVector.Cross(endVector) > 0)
            {
                angle = -angle;
            }
            var coords = GetArcCoords(startCoord, centerCoord, angle);

            return coords;
        }

        // 获取圆周围均匀分布的点
        // circleCoord: 圆周上的点
        // centerCoord: 圆心
        // n: 均匀分配点的个数
        public static Coord[] GetCoordsAround2(Coord circleCoord, Coord centerCoord, int n)
        {
            return GetCoordsAround(circleCoord, centerCoord, n, 2 * UnityEngine.Mathf.PI);
        }

        // ound 获取圆上的startCoord出发, angle角度内的点, 包括startCoord
        // n: 采样数
        public static Coord[] GetCoordsAround(Coord startCoord, Coord centerCoord, int n, double angle)
        {
            if (n < 2)
            {
                return null;
            }

            var ret = new Coord[n];

            ret[0] = startCoord;

            var vec = new Vector(centerCoord, startCoord);

            var ave = angle / (double)(n - 1);

            angle = 0;
            for (int i = 1; i < n; i++)
            {
                angle += ave;
                var v = vec.Rotate(angle);
                ret[i] = v.ToCoord(centerCoord);
            }

            return ret;
        }

        // 获取圆弧上的点, 圆弧从startCoord点出发, angle弧度
        // angle: 角度, >0 为顺时针方向, <0 为逆时针方向
        public static Coord[] GetArcCoords(Coord startCoord, Coord centerCoord, double angle)
        {
            // pi 34个点
            var n = UnityEngine.Mathf.Max((int)(System.Math.Abs(angle) * 10), 2);
            return GetCoordsAround(startCoord, centerCoord, n, -angle);
        }

        // 求圆外一点（endCoord）的到圆(center)的切点的角度
        public static double GetCutOffCoordAngle(Coord endCoord, Coord centerCoord, double radius)
        {
            // 令终点为e, 圆心为c, 切点为 p
            // ep ⊥ cp
            // cos∠ecp = ep / cp
            var dst = CalDstCoordToCoord(endCoord, centerCoord);

            var a = System.Math.Acos(radius / dst);

            if (double.IsNaN(a))
            {
                return 0;
            }

            return a;
        }

        // 计算中心点
        public static Coord CalMidCoord(Coord p1, Coord p2)
        {
            return new Coord(
                (p1.X + p2.X) / 2,
                (p1.Z + p2.Z) / 2);
        }

        // 计算点和点之间的距离
        public static double CalDstCoordToCoord(Coord coord1, Coord coord2)
        {
            long dx = coord1.X - coord2.X;
            long dz = coord1.Z - coord2.Z;
            return System.Math.Sqrt(dx * dx + dz * dz);
        }

        // 计算点和点之间的距离,不开根号
        public static double CalDstCoordToCoordWithoutSqrt(Coord coord1, Coord coord2)
        {
            long dx = coord1.X - coord2.X;
            long dz = coord1.Z - coord2.Z;
            return dx * dx + dz * dz;
        }

        public static void Copy(List<Edge> dst, List<Edge> src)
        {
            for (int i = 0; i < src.Count; ++i)
            {
                dst.Add(src[i]);
            }
        }

        public static void Copy(Edge[] dst, Edge[] src)
        {
            int n = UnityEngine.Mathf.Min(dst.Length, src.Length);
            for (int i = 0; i < n; ++i)
            {
                dst[i] = src[i];
            }
        }


        // 求线段P1P2与Q1Q2的交点。
        // https://stackoverflow.com/questions/563198/how-do-you-detect-where-two-line-segments-intersect/565282#
        public static Coord GetCrossCoord(Coord p0, Coord p1, Coord q0, Coord q1, out bool exist)
        {
            var v1 = new Vector(p0, p1);

            var v2 = new Vector(q0, q1);
            exist = false;
            // 共线
            if (v1.Cross(v2) == 0)
            {
                return new Coord(0, 0);
            }

            if (GeoUtils.IsRectCross(p0, p1, q0, q1))
            {
                if (IsLineSegmentCross(p0, p1, q0, q1))
                {
                    // 求交点
                    var s1X = (double)(p1.X - p0.X);

                    var s1Z = (double)(p1.Z - p0.Z);

                    var s2X = (double)(q1.X - q0.X);

                    var s2Z = (double)(q1.Z - q0.Z);


                    var t = (s2X * (double)(p0.Z - q0.Z) - s2Z * (double)(p0.X - q0.X)) / (-s2X * s1Z + s1X * s2Z);

                    exist = true;
                    return new Coord(p0.X + (int)(t * s1X), p0.Z + (int)(t * s1Z));
                }
            }
            return new Coord(0, 0);
        }

        // 排斥判断
        public static bool IsRectCross(Coord p0, Coord p1, Coord q0, Coord q1)
        {
            var ret = UnityEngine.Mathf.Min(p0.X, p1.X) <= UnityEngine.Mathf.Max(q0.X, q1.X) &&
                UnityEngine.Mathf.Min(q0.X, q1.X) <= UnityEngine.Mathf.Max(p0.X, p1.X) &&
                UnityEngine.Mathf.Min(p0.Z, p1.Z) <= UnityEngine.Mathf.Max(q0.Z, q1.Z) &&
                UnityEngine.Mathf.Min(q0.Z, q1.Z) <= UnityEngine.Mathf.Max(p0.Z, p1.Z);

            return ret;
        }

        // 叉乘算法,
        // 积=p3p1 X p3p2
        public static long Cross(Coord p1, Coord p2, Coord p3)
        {
            var s = (long)(p1.X - p3.X) * (long)((p2.Z - p3.Z)) - (long)(p2.X - p3.X) * (long)(p1.Z - p3.Z);
            return s;
        }

        // 跨立判断
        public static bool IsLineSegmentCross(Coord p0, Coord p1, Coord q0, Coord q1)
        {
            // q0q1 X q0p0
            var b1 = Cross(q1, p0, q0);
            // q0q1 X q0p1
            var b2 = Cross(q1, p1, q0);

            // 叉积等于0，说明其中一点与另外的线段共线
            if (b1 == 0 || b2 == 0)
            {
                return true;
            }

            // p0p1 X p0q0
            var a1 = Cross(p1, q0, p0);
            // p0p1 X p0q1
            var a2 = Cross(p1, q1, p0);

            if (a1 == 0 || a2 == 0)
            {
                return true;
            }

            return ((b1 < 0) != (b2 < 0)) && ((a1 < 0) != (a2 < 0));
        }

        public static bool Smaller(double a, double b)
        {
            if (b - a >= UnityEngine.Mathf.Epsilon)
            {
                return true;
            }

            return false;
        }

        // 根据出发点和连接到的边，生成左右两条向量，求叉积确定位置关系
        public static void GenLeftAndRightVector(Coord curCoord, Edge edge, int edgeIdx, out Nav.Vector1 leftVector, out Nav.Vector1 rightVector)
        {
            leftVector = new Nav.Vector1(curCoord, edge, edgeIdx, 0);
            rightVector = new Nav.Vector1(curCoord, edge, edgeIdx, 1);

            // 计算leftVector和rightVector的实际位置关系
            // leftVector在rightVector的左边，叉积大于0，如果小于0，则在右边，说明两者需要交换位置关系
            if (leftVector.Vec.Cross(rightVector.Vec) > 0)
            {
                var temp = leftVector;
                leftVector = rightVector;
                rightVector = temp;
            }
        }

        public static UnityEngine.Vector3 CoordToVector3(Coord c)
        {
            return new UnityEngine.Vector3(TFW.Map.Utils.I2F(c.X), 0, TFW.Map.Utils.I2F(c.Z));
        }

        public static Coord Vector3ToCoord(UnityEngine.Vector3 p)
        {
            return new Coord(TFW.Map.Utils.F2I(p.x), TFW.Map.Utils.F2I(p.z));
        }

        public static Coord Vector2ToCoord(UnityEngine.Vector2 p)
        {
            return new Coord(TFW.Map.Utils.F2I(p.x), TFW.Map.Utils.F2I(p.y));
        }
    }
}
