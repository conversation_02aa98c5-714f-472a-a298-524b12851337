#if USE_ADDRESSABLES
using System;
using System.IO;
using System.Linq;

namespace CISystem
{
    public static class AddressablesJenkins
    {
        public static void BuildAddressablesContent()
        {
            var result = AddressablesHelper.BuildAddressablesContent();

            bool success = string.IsNullOrEmpty(result.Error);

            if (!success)
            {
                UnityEngine.Debug.LogError("Addressables build error encountered: " + result.Error);
                JenkinsDingDingHelper.NotifyMarkDown($"资源打包失败:Error {result.Error}");
            }
            else
            {
                var sizeSum = 0L;
                foreach (var filePath in result.FileRegistry.GetFilePaths())
                {
                    if (File.Exists(filePath))
                    {
                        var fileSize = new FileInfo(filePath).Length;
                        UnityEngine.Debug.Log($"{fileSize} B: {filePath}");
                        sizeSum += fileSize;
                    }
                }

                var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
                var argsDic = argsParser.Options;
                argsDic.TryGetValue("-versionCode", out var versionCode);
                var msgPre = string.IsNullOrEmpty(versionCode) ? string.Empty : $"{versionCode}版本";

                var msg = $"## {msgPre}资源打包完成 \n\n 用时: {result.Duration} 秒 \n\n 资源数量：{result.LocationCount} \n\n 资源大小：{sizeSum / 1024f / 1024f} MB";
                UnityEngine.Debug.Log(msg);
                JenkinsDingDingHelper.NotifyMarkDown($"{msg}");
            }
        }

        public static void UpdateAPreviousBuild()
        {
            try
            {
                var (result, modifiedEntries, contentUpdateGroupName) = AddressablesHelper.UpdateAPreviousBuild();
                
                bool success = string.IsNullOrEmpty(result.Error);

                if (!success)
                {
                    UnityEngine.Debug.LogError("Addressables build error encountered: " + result.Error);
                    JenkinsDingDingHelper.NotifyMarkDown($"热更打包失败: {result.Error}");
                }
                else
                {
                    var sizeSum = 0L;
                    var updateContents = result.AssetBundleBuildResults
                        .Where(x => x.SourceAssetGroup.Name == contentUpdateGroupName)
                        .ToList();
                    var bundleFileNames = string.Empty;
                    foreach (var r in updateContents)
                    {
                        var filePath = r.FilePath;
                        if (File.Exists(filePath))
                        {
                            var fileSize = new FileInfo(filePath).Length;
                            Console.WriteLine($"{fileSize} B: {filePath}");
                            sizeSum += fileSize;
                        }
                        else
                        {
                            UnityEngine.Debug.LogError($"BuildResult 路径不存在 {filePath}");
                        }
                        var fileName = Path.GetFileName(filePath);
                        bundleFileNames += $"{fileName}\n\n";
                    }

                    var changedAssets = string.Empty;
                    foreach (var entry in modifiedEntries)
                    {
                        changedAssets += $"{entry.AssetPath}\n\n";
                    }

                    var argsParser = new CommandLineParser(Environment.GetCommandLineArgs());
                    var argsDic = argsParser.Options;
                    argsDic.TryGetValue("-versionCode", out var versionCode);
                    var msgPre = string.IsNullOrEmpty(versionCode) ? string.Empty : $"{versionCode}版本";

                    var msg = $"## {msgPre}热更打包完成 \n\n 用时: {result.Duration} 秒 \n\n 资源数量：{updateContents.Count} \n\n 资源大小：{sizeSum / 1024f / 1024f} MB";
                    
                    var changedAssetsMsg = string.IsNullOrEmpty(changedAssets) ? "无" : changedAssets;
                    msg += $"\n\n变更的Assets: {changedAssetsMsg}";
                    
                    var bundleFileNamesMsg = string.IsNullOrEmpty(bundleFileNames) ? "无" : bundleFileNames;
                    msg += $"\n\n生成的Bundles: {bundleFileNamesMsg}";
                    
                    Console.WriteLine(msg);
                    JenkinsDingDingHelper.NotifyMarkDown($"{msg}");
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError("Addressables build error encountered: " + e);
                JenkinsDingDingHelper.NotifyMarkDown($"热更打包失败: {e}");
            }
        }
    }
}
#endif