﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.IO;

namespace TFW.Map
{
    public partial class RegionLayer : MapLayerBase
    {
        public string GetAssetPathLOD0(string exportFolder, int number)
        {
            string assetFolder = $"{exportFolder}/{MapCoreDef.REGION_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            return $"{assetFolder}/region_{number}_lod0.prefab";
        }

        public string GetBorderLinePrefabPathLOD0(string exportFolder, int number)
        {
            string assetFolder = $"{exportFolder}/{MapCoreDef.REGION_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            return $"{assetFolder}/region_border_{number}_lod0.prefab";
        }

        public string GetBorderAssetPath(string exportFolder)
        {
            string assetFolder = $"{exportFolder}/{MapCoreDef.REGION_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            return $"{assetFolder}/border_lod0.prefab";
        }

        public void GenerateAssets(string folder)
        {
            if (string.IsNullOrEmpty(folder))
            {
                Debug.LogError("Invalid export folder!");
                return;
            }

            string assetFolder = $"{folder}/{MapCoreDef.REGION_LAYER_RUNTIME_ASSETS_FOLDER_NAME}";
            FileUtil.DeleteFileOrDirectory(assetFolder);
            Directory.CreateDirectory(assetFolder);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            GenerateLODAssets(assetFolder);
        }

        void GenerateLODAssets(string assetFolder)
        {
            var regions = mLayerData.objects;
            int idx = 0;
            foreach (var p in regions)
            {
                if (regions.Count > 0)
                {
                    EditorUtility.DisplayProgressBar("Generating...", "Generating Region Assets...", (float)idx / (regions.Count - 1));
                }
                ++idx;
                var region = p.Value as RegionData;
                if (region.meshVertices != null && region.meshVertices.Length > 0)
                {
                    var mesh = Object.Instantiate<Mesh>(GetRegionMesh(region.id));

                    string prefix = $"{assetFolder}/region_{region.number}_lod0.";

                    AssetDatabase.CreateAsset(mesh, prefix + "asset");

                    Material mtl = null;
                    if (region.material != null)
                    {
                        mtl = Object.Instantiate<Material>(region.material);
                        AssetDatabase.CreateAsset(mtl, prefix + "mat");
                    }

                    GameObject obj = new GameObject("region " + region.number);
                    var renderer = obj.AddComponent<MeshRenderer>();
                    var filter = obj.AddComponent<MeshFilter>();
                    filter.sharedMesh = mesh;
                    renderer.sharedMaterial = mtl;

                    PrefabUtility.SaveAsPrefabAsset(obj, prefix + "prefab");
                    GameObject.DestroyImmediate(obj);
                }

                //generate border line assets
                var borderLineMesh = mLayerView.GetRegionBorderLineMesh(region.id);
                if (borderLineMesh != null)
                {
                    var mesh = Object.Instantiate<Mesh>(borderLineMesh);

                    string prefix = $"{assetFolder}/region_border_{region.number}_lod0.";

                    AssetDatabase.CreateAsset(mesh, prefix + "asset");

                    Material mtl = null;
                    if (mLayerData.borderLineMaterial != null)
                    {
                        mtl = Object.Instantiate<Material>(mLayerData.borderLineMaterial);
                        AssetDatabase.CreateAsset(mtl, prefix + "mat");
                    }

                    GameObject obj = new GameObject("region border" + region.number);
                    var renderer = obj.AddComponent<MeshRenderer>();
                    var filter = obj.AddComponent<MeshFilter>();
                    filter.sharedMesh = mesh;
                    renderer.sharedMaterial = mtl;

                    PrefabUtility.SaveAsPrefabAsset(obj, prefix + "prefab");
                    GameObject.DestroyImmediate(obj);
                }
            }
            EditorUtility.ClearProgressBar();

            //generate border assets
            var borderVertices = mLayerData.borderMeshVertices;
            if (borderVertices.Length > 0)
            {
                var mesh = Object.Instantiate<Mesh>(mLayerView.borderMesh);

                string prefix = $"{assetFolder}/border_lod0.";

                AssetDatabase.CreateAsset(mesh, prefix + "asset");

                Material mtl = null;
                if (mLayerData.borderMaterial != null)
                {
                    mtl = Object.Instantiate<Material>(mLayerData.borderMaterial);
                    AssetDatabase.CreateAsset(mtl, prefix + "mat");


                    GameObject obj = new GameObject("border");
                    var renderer = obj.AddComponent<MeshRenderer>();
                    var filter = obj.AddComponent<MeshFilter>();
                    filter.sharedMesh = mesh;
                    renderer.sharedMaterial = mtl;

                    PrefabUtility.SaveAsPrefabAsset(obj, prefix + "prefab");
                    GameObject.DestroyImmediate(obj);
                }
            }

            Mesh GetRegionMesh(int id)
            {
                var view = mLayerView.GetObjectView(id) as RegionView;
                if (view == null)
                {
                    return null;
                }
                return view.GetMesh();
            }
        }
    }
}

#endif