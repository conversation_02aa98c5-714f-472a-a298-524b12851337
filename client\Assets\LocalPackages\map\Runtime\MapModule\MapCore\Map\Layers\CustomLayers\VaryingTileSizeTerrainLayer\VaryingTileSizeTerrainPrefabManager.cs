﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class VaryingTileSizeTerrainPrefabGroup
    {
        public class Item
        {
            public string prefabPath;
            public Vector2Int size = Vector2Int.one;
        }

        public VaryingTileSizeTerrainPrefabGroup(int groupID)
        {
            mGroupID = groupID;
        }

        public void SetPrefabPath(int index, string path)
        {
            if (index >= 0 && index < mPrefabs.Length)
            {
                if (mPrefabs[index] == null)
                {
                    mPrefabs[index] = new Item();
                }
                mPrefabs[index].prefabPath = path;
            }
        }

        public string GetPrefabPath(int index)
        {
            if (mPrefabs[index] == null)
            {
                return null;
            }
            return mPrefabs[index].prefabPath;
        }

        public void SetPrefabSize(int index, Vector2Int size)
        {
            if (index >= 0 && index < mPrefabs.Length)
            {
                if (mPrefabs[index] == null)
                {
                    mPrefabs[index] = new Item();
                }
                mPrefabs[index].size = size;
            }
        }

        public Vector2Int GetPrefabSize(int index)
        {
            if (mPrefabs[index] == null)
            {
                return Vector2Int.one;
            }
            return mPrefabs[index].size;
        }

        Item[] mPrefabs = new Item[1024];
        int mGroupID;

        public Item[] terrainPrefabs { get { return mPrefabs; } }
        public int prefabCount { get { return mPrefabs.Length; } }
        public int groupID { get { return mGroupID; } }
    }

    public class VaryingTileSizeTerrainPrefabManager
    {
        public void SetGroupPrefabByID(int groupID, int index, string prefabPath)
        {
            var group = GetOrCreateGroup(groupID);
            group.SetPrefabPath(index, prefabPath);
        }

        public void SetGroupPrefabSizeByID(int groupID, int index, Vector2Int size)
        {
            var group = GetOrCreateGroup(groupID);
            group.SetPrefabSize(index, size);
        }

        public string GetPrefabByID(int groupID, int index)
        {
            var group = GetOrCreateGroup(groupID);
            return group.GetPrefabPath(index);
        }

        public Vector2Int GetPrefabSizeByID(int groupID, int index)
        {
            var group = GetOrCreateGroup(groupID);
            return group.GetPrefabSize(index);
        }

        public VaryingTileSizeTerrainPrefabGroup GetGroupByIndex(int groupIndex)
        {
            return mGroups[groupIndex];
        }

        public VaryingTileSizeTerrainPrefabGroup GetOrCreateGroup(int groupID)
        {
            Debug.Assert(groupID >= 0);
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].groupID == groupID)
                {
                    return mGroups[i];
                }
            }

            var group = new VaryingTileSizeTerrainPrefabGroup(groupID);
            mGroups.Add(group);
            return group;
        }

        public int groupCount { get { return mGroups.Count; } }

        List<VaryingTileSizeTerrainPrefabGroup> mGroups = new List<VaryingTileSizeTerrainPrefabGroup>();
    }
}
