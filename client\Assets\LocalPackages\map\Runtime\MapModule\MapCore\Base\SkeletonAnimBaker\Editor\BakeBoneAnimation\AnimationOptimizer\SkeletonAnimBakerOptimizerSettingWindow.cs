﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public class SkeletonAnimBakerOptimizerSettingWindow : EditorWindow
    {
        void OnEnable()
        {
            mStyle = new GUIStyle();
            mStyle.normal.textColor = Color.green;
        }

        void OnGUI()
        {
            mScrollPos = EditorGUILayout.BeginScrollView(mScrollPos);
            EditorStyles.textField.wordWrap = true;
            EditorGUILayout.TextArea("选择烘培prefab所在的目录,将使用相同状态机和相同骨骼节点的prefab共享同一个烘培动画,减少动画贴图数量");
            EditorStyles.textField.wordWrap = false;

            mFolder = EditorGUILayout.ObjectField($"Bake Folder", mFolder, typeof(DefaultAsset), false, null) as DefaultAsset;
            if (GUILayout.Button("Optimize"))
            {
                if (mFolder != null)
                {
                    string folderPath = AssetDatabase.GetAssetPath(mFolder);
                    SkeletonAnimBakerOptimizer.OptimizePrefabs(folderPath);
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Select bake folder!", "OK");
                }
            }

            var originalStats = SkeletonAnimBakerOptimizer.originalTextureStats;
            if (originalStats.Count > 0)
            {
                int totalSize = 0;
                EditorGUILayout.LabelField("Original Texture Info");
                EditorGUI.indentLevel++;
                for (int i = 0; i < originalStats.Count; ++i)
                {
                    totalSize += originalStats[i].textureMemorySize;
                    EditorGUILayout.LabelField($"{originalStats[i].texturePath} use {originalStats[i].textureMemorySize} bytes", mStyle);
                }
                EditorGUI.indentLevel--;

                EditorGUILayout.LabelField("Removed Texture Info");
                EditorGUI.indentLevel++;
                var removedStats = SkeletonAnimBakerOptimizer.removedTextureStats;
                int totalFreeupSize = 0;
                for (int i = 0; i < removedStats.Count; ++i)
                {
                    totalFreeupSize += removedStats[i].textureMemorySize;
                    EditorGUILayout.LabelField($"remove {removedStats[i].texturePath} frees up {removedStats[i].textureMemorySize} bytes", mStyle);
                }
                EditorGUI.indentLevel--;
                EditorGUILayout.LabelField($"Total Memory Usage: {totalSize} bytes, Freed Memory Size: {totalFreeupSize} bytes. ratio is {totalFreeupSize / (float)totalSize * 100.0f}%");
            }

            EditorGUILayout.EndScrollView();
        }

        DefaultAsset mFolder;
        Vector2 mScrollPos;
        GUIStyle mStyle;
    }
}


#endif