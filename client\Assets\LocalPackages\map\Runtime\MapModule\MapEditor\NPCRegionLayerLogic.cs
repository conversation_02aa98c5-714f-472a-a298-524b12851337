﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.8
 */

using UnityEngine;

namespace TFW.Map
{
    [ExecuteInEditMode]
    [Black]
    public class NPCRegionLayerLogic : MapLayerLogic
    {
        public GridRegionOperation operation = GridRegionOperation.Create;
        public bool showBrush = true;

        public NPCRegionLayer layer { get { return Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION) as NPCRegionLayer; } }
    }
}

#endif