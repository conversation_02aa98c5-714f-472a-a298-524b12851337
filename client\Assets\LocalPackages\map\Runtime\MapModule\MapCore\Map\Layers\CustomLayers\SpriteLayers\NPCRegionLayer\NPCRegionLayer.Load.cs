﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class NPCRegionLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID(), version.minorVersion);

            reader.Close();

            var layer = new NPCRegionLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.NPCRegionLayerData LoadLayerData(BinaryReader reader, int layerID, int minorVersion)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int nRows = reader.ReadInt32();
            int nCols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            int gridType = reader.ReadInt32();

            int nLayers = reader.ReadInt32();

            List<config.NPCRegionLayerData.Layer> layerDatas = new List<config.NPCRegionLayerData.Layer>();
            for (int k = 0; k < nLayers; ++k)
            {
                var layerData = new config.NPCRegionLayerData.Layer();
                layerDatas.Add(layerData);
                layerData.name = Utils.ReadString(reader);
                int overridenWidth = nCols;
                int overridenHeight = nRows;
                if (minorVersion >= 2) {
                    overridenWidth = reader.ReadInt32();
                    overridenHeight = reader.ReadInt32();
                }
                bool export = true;
                if (minorVersion >= 4)
                {
                    export = reader.ReadBoolean();
                }

                layerData.tiles = new int[overridenWidth * overridenHeight];
                layerData.overridenWidth = overridenWidth;
                layerData.overridenHeight = overridenHeight;
                layerData.overridenTileWidth = tileWidth * nCols / overridenWidth;
                layerData.overridenTileHeight = tileHeight * nRows / overridenHeight;
                layerData.export = export;
                for (int i = 0; i < overridenHeight; ++i)
                {
                    for (int j = 0; j < overridenWidth; ++j)
                    {
                        var spriteTemplateID = reader.ReadInt32();
                        int idx = i * overridenWidth + j;
                        layerData.tiles[idx] = spriteTemplateID;
                    }
                }

                layerData.regionTemplates = new config.NPCRegionTemplate[0];

                int nTemplates = reader.ReadInt32();
                layerData.regionTemplates = new config.NPCRegionTemplate[nTemplates];
                for (int i = 0; i < nTemplates; ++i)
                {
                    int type = reader.ReadInt32();
                    int level = reader.ReadInt32();
                    int tileType = 0;
                    if (minorVersion >= 3)
                    {
                        tileType = reader.ReadInt32();
                    }
                    Color32 color = Utils.ReadColor32(reader);
                    string templateName = Utils.ReadString(reader);
                    layerData.regionTemplates[i] = new config.NPCRegionTemplate(templateName, type, level, color, tileType);
                }
            }

            var layer = new config.NPCRegionLayerData(layerID, layerName, layerOffset, null, nRows, nCols, tileWidth, tileHeight, layerDatas);
            layer.active = active;
            return layer;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif