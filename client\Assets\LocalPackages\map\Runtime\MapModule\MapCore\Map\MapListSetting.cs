﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [System.Serializable]
    public class MapInfo
    {
        public int mapID;
        public string mapName = "main";
        public DefaultAsset mapDataFolder;
        public Vector3 mapOrigin = Vector3.zero;
        public bool loadAtStartup = true;
        public bool enableUpdateCameraFarClipPlane = true;
        public bool enableUpdateCameraNearClipPlane = false;
        public bool clampBorder = true;
        public bool isMainMap = false;
        public bool isPlaceholderMap = false;
        public TextAsset defaultCameraSetting;
        public CameraClipPlaneSetting nearClipPlaneSetting;
        public CameraClipPlaneSetting farClipPlaneSetting;
        public CameraZoomCurveSetting cameraZoomCurveSetting;
    }

    //生成map list文件
    [CreateAssetMenu(fileName = "map_list_setting", menuName = "Assets/MapListSetting")]
    public class MapListSetting : ScriptableObject
    {
        public MapInfo[] maps = new MapInfo[0];
        public string activeMapName;

        public void SetMapCount(int newCount)
        {
            if (maps.Length != newCount)
            {
                MapInfo[] newMaps = new MapInfo[newCount];
                int minCount = Mathf.Min(newCount, maps.Length);
                for (int i = 0; i < minCount; ++i)
                {
                    newMaps[i] = maps[i];
                }

                for (int i = minCount; i < newCount; ++i)
                {
                    newMaps[i] = new MapInfo();
                }

                maps = newMaps;
            }
        }

        public bool HasInvalidIDs()
        {
            bool allZero = true;
            for (int i = 0; i < maps.Length; ++i)
            {
                if (maps[i].mapID != 0)
                {
                    allZero = false;
                    break;
                }
            }

            if (allZero && maps.Length > 0)
            {
                return true;
            }

            return false;
        }

        public void ResetIDs()
        {
            for (int i = 0; i < maps.Length; ++i)
            {
                maps[i].mapID = i + 1;
            }
        }
    }
}

#endif