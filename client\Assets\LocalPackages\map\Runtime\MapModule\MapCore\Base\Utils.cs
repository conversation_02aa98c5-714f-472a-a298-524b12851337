﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using System.Diagnostics;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Text;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public class NavMeshBlock
    {
        public Vector3[] vertices;
        public int[] indices;
        public ushort[] triangleTypes;
        public bool[] triangleStates;
    }

    class CompareVector3 : IComparer<Vector3>
    {
        public CompareVector3(float distanceError)
        {
            mDistanceError = distanceError;
        }

        public int Compare(Vector3 a, Vector3 b)
        {
            if (Utils.Approximately(a.x, b.x, mDistanceError))
            {
                return Utils.CompareFloat(a.z, b.z, mDistanceError);
            }

            return Utils.CompareFloat(a.x, b.x, mDistanceError);
        }

        float mDistanceError;
    }

    //障碍物的代理,可以有多个代理对应一个障碍物
    class ObstacleProxy : IObstacle
    {
        public ObstacleProxy(IObstacle obstacle, Vector3 offset)
        {
            this.offset = offset;
            this.obstacle = obstacle;
        }

        public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
        {
            return obstacle.GetOutlineVertices(type);
        }

        public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
        {
            return obstacle.GetWorldSpaceOutlineVertices(type);
        }

        public bool IsSimplePolygon(PrefabOutlineType type)
        {
            return obstacle.IsSimplePolygon(type);
        }

        public GameObject gameObject
        {
            get { return obstacle.gameObject; }
        }

        public Rect GetOutlineBounds(PrefabOutlineType type)
        {
            return obstacle.GetOutlineBounds(type);
        }

        public Vector3 offset { set; get; }
        public IObstacle obstacle { set; get; }

        public bool IsExtendable()
        {
            return true;
        }
    }

    public static class Utils
    {
        public static bool Contains<T>(List<T> objects, T obj) where T : UnityEngine.Object
        {
            for (int i = 0; i < objects.Count; ++i)
            {
                if (objects[i].GetInstanceID() == obj.GetInstanceID())
                {
                    return true;
                }
            }

            return false;
        }

        public static string GetFolderPath(string path)
        {
            int idx = path.LastIndexOf("/", StringComparison.Ordinal);
            if (idx != -1)
            {
                path = path.Substring(0, idx);
            }

            return path;
        }

        public static string GetNameWithLOD(string path, bool includeExtension)
        {
            var extension = GetExtension(path);
            var fullName = GetPathName(path, false);
            var idx = fullName.LastIndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX, StringComparison.Ordinal);
            if (idx >= 0)
            {
                fullName = fullName.Substring(0, idx);
            }

            if (includeExtension)
            {
                return fullName + "." + extension;
            }

            return fullName;
        }

        public static string GetExtension(string path)
        {
            var idx = path.IndexOf(".");
            if (idx != -1)
            {
                return path.Substring(idx + 1);
            }

            return "";
        }

        public static string GetPathName(string path, bool includeExtension)
        {
            int idx = path.LastIndexOf("/", StringComparison.Ordinal);
            if (idx != -1)
            {
                path = path.Substring(idx + 1);
            }

            if (includeExtension == false)
            {
                idx = path.IndexOf(".");
                if (idx != -1)
                {
                    path = path.Substring(0, idx);
                }
            }

            return path;
        }

        public static string RemoveExtension(string path)
        {
            var idx = path.IndexOf(".");
            if (idx != -1)
            {
                path = path.Substring(0, idx);
            }

            return path;
        }

        public static PropertyDatas ReadProperties(BinaryReader reader)
        {
            PropertyDatas props = new PropertyDatas(null);
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var propType = (PropertyType) reader.ReadInt32();
                var propName = Utils.ReadString(reader);
                switch (propType)
                {
                    case PropertyType.kPropertyBool:
                    {
                        var p = new PropertyData<bool>(propName, propType, reader.ReadBoolean());
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyColor:
                    {
                        var p = new PropertyData<Color>(propName, propType, Utils.ReadColor(reader));
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyFloat:
                    {
                        var p = new PropertyData<float>(propName, propType, reader.ReadSingle());
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyInt:
                    {
                        var p = new PropertyData<int>(propName, propType, reader.ReadInt32());
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyIntArray:
                    {
                        var value = ReadIntArray(reader);
                        var p = new PropertyData<int[]>(propName, propType, value);
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyString:
                    {
                        var p = new PropertyData<string>(propName, propType, Utils.ReadString(reader));
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyVector2:
                    {
                        var p = new PropertyData<Vector2>(propName, propType, Utils.ReadVector2(reader));
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyVector3:
                    {
                        var p = new PropertyData<Vector3>(propName, propType, Utils.ReadVector3(reader));
                        props.AddProperty(p);
                        break;
                    }
                    case PropertyType.kPropertyVector4:
                    {
                        var p = new PropertyData<Vector4>(propName, propType, Utils.ReadVector4(reader));
                        props.AddProperty(p);
                        break;
                    }
                    default:
                        UnityEngine.Debug.Assert(false, "todo");
                        break;
                }
            }

            return props;
        }

        public static string ReadString(BinaryReader reader)
        {
            var length = reader.ReadInt32();
            if (length > 0)
            {
                var bytes = reader.ReadBytes(length);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }

            return "";
        }

        public static Version ReadVersion(BinaryReader reader)
        {
            int majorVersion = reader.ReadInt32();
            int minorVersion = reader.ReadInt32();
            return new Version(majorVersion, minorVersion);
        }

        public static void WriteVersion(BinaryWriter writer, Version version)
        {
            writer.Write(version.majorVersion);
            writer.Write(version.minorVersion);
        }

        public static void WriteProperties(BinaryWriter writer, PropertyDatas properties)
        {
            int n = properties.GetPropertyCount();
            writer.Write(n);

            for (int i = 0; i < n; ++i)
            {
                var prop = properties.GetProperty(i);
                writer.Write((int) prop.type);
                Utils.WriteString(writer, prop.name);
                switch (prop.type)
                {
                    case PropertyType.kPropertyBool:
                        writer.Write((prop as PropertyData<bool>).value);
                        break;
                    case PropertyType.kPropertyColor:
                        Utils.WriteColor(writer, (prop as PropertyData<Color>).value);
                        break;
                    case PropertyType.kPropertyFloat:
                        writer.Write((prop as PropertyData<float>).value);
                        break;
                    case PropertyType.kPropertyInt:
                        writer.Write((prop as PropertyData<int>).value);
                        break;
                    case PropertyType.kPropertyIntArray:
                        WriteIntArray(writer, (prop as PropertyData<int[]>).value);
                        break;
                    case PropertyType.kPropertyString:
                        Utils.WriteString(writer, (prop as PropertyData<string>).value);
                        break;
                    case PropertyType.kPropertyVector2:
                        Utils.WriteVector2(writer, (prop as PropertyData<Vector2>).value);
                        break;
                    case PropertyType.kPropertyVector3:
                        Utils.WriteVector3(writer, (prop as PropertyData<Vector3>).value);
                        break;
                    case PropertyType.kPropertyVector4:
                        Utils.WriteVector4(writer, (prop as PropertyData<Vector4>).value);
                        break;
                    default:
                        UnityEngine.Debug.Assert(false, "todo");
                        break;
                }
            }
        }

        public static void WriteString(BinaryWriter writer, string val)
        {
            int length = 0;
            if (val != null)
            {
                length = val.Length;
            }

            writer.Write(length);
            if (length > 0)
            {
                byte[] bytes = System.Text.Encoding.UTF8.GetBytes(val);
                writer.Write(bytes);
            }
        }

        public static void WriteBoolArray(BinaryWriter writer, bool[] vals)
        {
            int length = 0;
            if (vals != null)
            {
                length = vals.Length;
            }

            writer.Write(length);
            for (int i = 0; i < length; ++i)
            {
                writer.Write(vals[i]);
            }
        }

        public static void WriteUInt16Array(BinaryWriter writer, ushort[] vals)
        {
            int length = 0;
            if (vals != null)
            {
                length = vals.Length;
            }

            writer.Write(length);
            for (int i = 0; i < length; ++i)
            {
                writer.Write(vals[i]);
            }
        }

        public static void WriteStringList(BinaryWriter writer, List<string> vals)
        {
            int length = 0;
            if (vals != null)
            {
                length = vals.Count;
            }

            writer.Write(length);
            for (int i = 0; i < length; ++i)
            {
                WriteString(writer, vals[i]);
            }
        }

        public static void WriteStringArray(BinaryWriter writer, string[] vals)
        {
            int length = 0;
            if (vals != null)
            {
                length = vals.Length;
            }

            writer.Write(length);
            for (int i = 0; i < length; ++i)
            {
                WriteString(writer, vals[i]);
            }
        }

        public static string[] ReadStringArray(BinaryReader reader)
        {
            int length = reader.ReadInt32();
            string[] ret = new string[length];
            for (int i = 0; i < length; ++i)
            {
                ret[i] = ReadString(reader);
            }

            return ret;
        }

        public static List<string> ReadStringList(BinaryReader reader)
        {
            int count = reader.ReadInt32();
            List<string> ret = new List<string>(count);
            for (int i = 0; i < count; ++i)
            {
                ret.Add(ReadString(reader));
            }
            return ret;
        }

        public static void WriteRect(BinaryWriter writer, Rect r)
        {
            Utils.WriteVector2(writer, r.min);
            Utils.WriteVector2(writer, r.max);
        }

        public static void WriteRectInt(BinaryWriter writer, RectInt r)
        {
            Utils.WriteVector2Int(writer, r.min);
            Utils.WriteVector2Int(writer, r.max);
        }

        public static Rect ReadRect(BinaryReader reader)
        {
            var min = Utils.ReadVector2(reader);
            var max = Utils.ReadVector2(reader);
            return new Rect(min.x, min.y, max.x - min.x, max.y - min.y);
        }

        public static RectInt ReadRectInt(BinaryReader reader)
        {
            var min = Utils.ReadVector2Int(reader);
            var max = Utils.ReadVector2Int(reader);
            var r = new RectInt();
            r.SetMinMax(min, max);
            return r;
        }

        public static Bounds ReadBounds(BinaryReader reader)
        {
            var min = ReadVector3(reader);
            var max = ReadVector3(reader);
            return CreateBounds(min, max);
        }

        public static void WriteBounds(BinaryWriter writer, Bounds bounds)
        {
            WriteVector3(writer, bounds.min);
            WriteVector3(writer, bounds.max);
        }

        public static void WriteFloatArray(BinaryWriter writer, float[] data)
        {
            int n = 0;
            if (data != null)
            {
                n = data.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(data[i]);
            }
        }

        public static float[] ReadFloatArray(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            if (n == 0)
            {
                return null;
            }

            float[] ret = new float[n];
            for (int i = 0; i < n; ++i)
            {
                ret[i] = reader.ReadSingle();
            }

            return ret;
        }

        public static List<float> ReadFloatList(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            if (n == 0)
            {
                return null;
            }

            List<float> ret = new List<float>(n);
            for (int i = 0; i < n; ++i)
            {
                ret.Add(reader.ReadSingle());
            }

            return ret;
        }

        public static Vector2 ReadVector2(BinaryReader reader)
        {
            return new Vector2(reader.ReadSingle(), reader.ReadSingle());
        }

        public static void WriteVector2(BinaryWriter writer, Vector2 val)
        {
            writer.Write(val.x);
            writer.Write(val.y);
        }

        public static void WriteVector2Int(BinaryWriter writer, Vector2Int val)
        {
            writer.Write(val.x);
            writer.Write(val.y);
        }

        public static Vector2Int ReadVector2Int(BinaryReader reader)
        {
            return new Vector2Int(reader.ReadInt32(), reader.ReadInt32());
        }

        public static Color[] ReadColorArray(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new Color[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = Utils.ReadColor(reader);
            }

            return val;
        }

        public static ushort[] ReadUInt16Array(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new ushort[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = reader.ReadUInt16();
            }

            return val;
        }

        public static bool[] ReadBoolArray(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new bool[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = reader.ReadBoolean();
            }

            return val;
        }

        public static Color32[] ReadColor32Array(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new Color32[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = Utils.ReadColor32(reader);
            }

            return val;
        }

        public static Vector3[] ReadVector3Array(BinaryReader reader)
        {
            int n = reader.ReadInt32();

            var val = new Vector3[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = Utils.ReadVector3(reader);
            }

            return val;
        }

        public static Vector2[] ReadVector2Array(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            ;
            var val = new Vector2[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = Utils.ReadVector2(reader);
            }

            return val;
        }

        public static Quaternion[] ReadQuaternionArray(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new Quaternion[n];
            for (int i = 0; i < n; ++i)
            {
                val[i] = Utils.ReadQuaternion(reader);
            }

            return val;
        }

        public static List<Vector3> ReadVector3List(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            var val = new List<Vector3>(n);
            for (int i = 0; i < n; ++i)
            {
                val.Add(Utils.ReadVector3(reader));
            }

            return val;
        }

        public static void WriteColorArray(BinaryWriter writer, Color[] colors)
        {
            int n = 0;
            if (colors != null)
            {
                n = colors.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteColor(writer, colors[i]);
            }
        }

        public static void WriteColor32Array(BinaryWriter writer, Color32[] colors)
        {
            int n = 0;
            if (colors != null)
            {
                n = colors.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteColor32(writer, colors[i]);
            }
        }

        public static void WriteVector3List(BinaryWriter writer, List<Vector3> vals)
        {
            int n = 0;
            if (vals != null)
            {
                n = vals.Count;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteVector3(writer, vals[i]);
            }
        }

        public static void WriteVector3Array(BinaryWriter writer, Vector3[] vals)
        {
            int n = 0;
            if (vals != null)
            {
                n = vals.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteVector3(writer, vals[i]);
            }
        }

        public static void WriteVector2Array(BinaryWriter writer, Vector2[] vals)
        {
            int n = 0;
            if (vals != null)
            {
                n = vals.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteVector2(writer, vals[i]);
            }
        }

        public static void WriteQuaternionArray(BinaryWriter writer, Quaternion[] rotations)
        {
            int n = 0;
            if (rotations != null)
            {
                n = rotations.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                WriteQuaternion(writer, rotations[i]);
            }
        }

        public static void WriteIntArray(BinaryWriter writer, int[] vals)
        {
            int n = 0;
            if (vals != null)
            {
                n = vals.Length;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(vals[i]);
            }
        }

        public static void WriteIntList(BinaryWriter writer, List<int> vals)
        {
            int n = 0;
            if (vals != null)
            {
                n = vals.Count;
            }

            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                writer.Write(vals[i]);
            }
        }

        public static void WriteColor(BinaryWriter writer, Color color)
        {
            writer.Write(color.r);
            writer.Write(color.g);
            writer.Write(color.b);
            writer.Write(color.a);
        }

        public static void WriteColor32(BinaryWriter writer, Color32 color)
        {
            writer.Write(color.r);
            writer.Write(color.g);
            writer.Write(color.b);
            writer.Write(color.a);
        }

        public static int[] ReadIntArray(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            int[] arr = new int[n];
            for (int i = 0; i < n; ++i)
            {
                arr[i] = reader.ReadInt32();
            }

            return arr;
        }

        public static List<int> ReadIntList(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            List<int> arr = new List<int>(n);
            for (int i = 0; i < n; ++i)
            {
                arr.Add(reader.ReadInt32());
            }

            return arr;
        }

        public static Vector3 ReadVector3(BinaryReader reader)
        {
            return new Vector3(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
        }

        public static void WriteVector3(BinaryWriter writer, Vector3 val)
        {
            writer.Write(val.x);
            writer.Write(val.y);
            writer.Write(val.z);
        }

        public static Vector4 ReadVector4(BinaryReader reader)
        {
            return new Vector4(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
        }

        public static void WriteVector4(BinaryWriter writer, Vector4 val)
        {
            writer.Write(val.x);
            writer.Write(val.y);
            writer.Write(val.z);
            writer.Write(val.w);
        }

        public static Quaternion ReadQuaternion(BinaryReader reader)
        {
            return new Quaternion(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
        }

        public static void WriteQuaternion(BinaryWriter writer, Quaternion val)
        {
            writer.Write(val.x);
            writer.Write(val.y);
            writer.Write(val.z);
            writer.Write(val.w);
        }

        public static Color32 ReadColor32(BinaryReader reader)
        {
            var r = reader.ReadByte();
            var g = reader.ReadByte();
            var b = reader.ReadByte();
            var a = reader.ReadByte();
            return new Color32((byte) r, (byte) g, (byte) b, (byte) a);
        }

        public static Color ReadColor(BinaryReader reader)
        {
            return new Color(reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle(), reader.ReadSingle());
        }

        public static void WriteAndJump(BinaryWriter writer, long jumpToPos, int data)
        {
            var curPos = writer.BaseStream.Position;
            writer.BaseStream.Seek(jumpToPos, SeekOrigin.Begin);
            writer.Write(data);
            writer.BaseStream.Position = curPos;
        }

        public static void WriteAndJump(BinaryWriter writer, long jumpToPos, long data)
        {
            var curPos = writer.BaseStream.Position;
            writer.BaseStream.Seek(jumpToPos, SeekOrigin.Begin);
            writer.Write(data);
            writer.BaseStream.Position = curPos;
        }

        public static int PeekID(BinaryReader reader)
        {
            var stream = reader.BaseStream;
            var currentPointer = stream.Seek(0, SeekOrigin.Current);
            var id = reader.ReadInt32();
            stream.Seek(currentPointer, SeekOrigin.Begin);
            return id;
        }

        public static void SetLayerRecursively(GameObject go, int layerNumber)
        {
            if (go == null) return;
            foreach (Transform trans in go.GetComponentsInChildren<Transform>(true))
            {
                trans.gameObject.layer = layerNumber;
            }
        }

        public static Bounds TransformBounds(Bounds bounds, Matrix4x4 mat)
        {
            var right = new Vector3(mat.m00, mat.m10, mat.m20);
            var up = new Vector3(mat.m01, mat.m11, mat.m21);
            var forward = new Vector3(mat.m02, mat.m12, mat.m22);
            var translation = new Vector3(mat.m03, mat.m13, mat.m23);

            var boundsMin = bounds.min;
            var boundsMax = bounds.max;

            var xa = right * boundsMin.x;
            var xb = right * boundsMax.x;

            var ya = up * boundsMin.y;
            var yb = up * boundsMax.y;

            var za = forward * boundsMin.z;
            var zb = forward * boundsMax.z;

            var min = Vector3.Min(xa, xb) + Vector3.Min(ya, yb) + Vector3.Min(za, zb) + translation;
            var max = Vector3.Max(xa, xb) + Vector3.Max(ya, yb) + Vector3.Max(za, zb) + translation;

            var ret = new Bounds();
            ret.SetMinMax(min, max);
            return ret;
        }

        static Rect TransformRect(Rect bounds, Matrix4x4 mat)
        {
            var right = new Vector3(mat.m00, mat.m10, mat.m20);
            var up = new Vector3(mat.m01, mat.m11, mat.m21);
            var forward = new Vector3(mat.m02, mat.m12, mat.m22);
            var translation = new Vector3(mat.m03, mat.m13, mat.m23);

            var min = bounds.min;
            var max = bounds.max;
            var boundsMin = new Vector3(min.x, 0, min.y);
            var boundsMax = new Vector3(max.x, 0, max.y);

            var xa = right * boundsMin.x;
            var xb = right * boundsMax.x;

            var ya = up * boundsMin.y;
            var yb = up * boundsMax.y;

            var za = forward * boundsMin.z;
            var zb = forward * boundsMax.z;

            var min3 = Vector3.Min(xa, xb) + Vector3.Min(ya, yb) + Vector3.Min(za, zb) + translation;
            var max3 = Vector3.Max(xa, xb) + Vector3.Max(ya, yb) + Vector3.Max(za, zb) + translation;

            return new Rect(min3.x, min3.z, max3.x - min3.x, max3.z - min3.z);
        }

        public static Bounds TransformBounds(Bounds bounds, Transform transform)
        {
            var mat = transform.localToWorldMatrix;
            return TransformBounds(bounds, mat);
        }

        public static Bounds TransformBounds(Bounds bounds, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            Matrix4x4 transform = Matrix4x4.TRS(position, rotation, scale);
            return TransformBounds(bounds, transform);
        }

        public static Rect TransformRect(Rect rect, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            Matrix4x4 transform = Matrix4x4.TRS(position, rotation, scale);
            return TransformRect(rect, transform);
        }

        public static Rect TransformRect(Rect r, Transform transform)
        {
            var mat = transform.localToWorldMatrix;
            return TransformRect(r, mat);
        }

        //if b is inside a
        public static bool FullContains(Rect a, Rect b)
        {
            var aMin = a.min;
            var aMax = a.max;
            var bMin = b.min;
            var bMax = b.max;

            if (bMin.x >= aMin.x && bMin.y >= aMin.y &&
                bMax.x <= aMax.x && bMax.y <= aMax.y)
            {
                return true;
            }

            return false;
        }

        public static Bounds CreateBounds(Vector3[] vertices)
        {
            var bounds = new Bounds();
            for (int i = 0; i < vertices.Length; ++i)
            {
                bounds.Encapsulate(vertices[i]);
            }

            return bounds;
        }

        public static Bounds CreateBounds(Vector3 min, Vector3 max)
        {
            float minX = Mathf.Min(min.x, max.x);
            float minY = Mathf.Min(min.y, max.y);
            float minZ = Mathf.Min(min.z, max.z);
            float maxX = Mathf.Max(min.x, max.x);
            float maxY = Mathf.Max(min.y, max.y);
            float maxZ = Mathf.Max(min.z, max.z);
            var newMin = new Vector3(minX, minY, minZ);
            var newMax = new Vector3(maxX, maxY, maxZ);
            var bounds = new Bounds();
            bounds.SetMinMax(newMin, newMax);
            return bounds;
        }

        public static Vector3 FromViewportToWorldPosition(Vector3 viewportPos, Camera camera)
        {
            if (camera.orthographic == false)
            {
                var ray = camera.ViewportPointToRay(viewportPos);
                if (Mathf.Approximately(ray.direction.y, 0))
                {
                    return Vector3.zero;
                }

                float t = -ray.origin.y / ray.direction.y;
                var intersection = ray.origin + ray.direction * t;
                return intersection;
            }
            else
            {
                return camera.ViewportToWorldPoint(viewportPos);
            }
        }

        public static Vector3 FromScreenToWorldPosition(Vector3 screenPos, Camera camera, float planeHeight = 0)
        {
            if (camera == null)
            {
                return Vector3.zero;
            }

            var ray = camera.ScreenPointToRay(screenPos);
            if (Mathf.Approximately(ray.direction.y, 0))
            {
                return Vector3.zero;
            }

            float t = (planeHeight - ray.origin.y) / ray.direction.y;
            var intersection = ray.origin + ray.direction * t;
            return intersection;
        }

        public static Vector3 FromScreenToWorldPosition2D(Vector3 screenPos, Camera camera, float orthographicSize)
        {
            if (camera == null)
            {
                return Vector3.zero;
            }

            float oldOrthographicSize = camera.orthographicSize;
            camera.orthographicSize = orthographicSize;
            var pos = camera.ScreenToWorldPoint(screenPos);
            camera.orthographicSize = oldOrthographicSize;
            return pos;
        }

        public static MapLayerCoordinateConverter GetMapLayerCoordinateConverter(GridType shape)
        {
            switch (shape)
            {
                case GridType.Hexagonal:
                    return new MapLayerXZHexagonalCoordinateConverter();
                case GridType.Rectangle:
                    return new MapLayerXZRectangleCoordinateConverter();
                default:
                    UnityEngine.Debug.Assert(false, "Unknown grid type");
                    break;
            }

            return null;
        }

        //将角度转换到[-180,180]的范围
        public static float ClampAngleTo180(float angle)
        {
            while(angle >= 360)
            {
                angle -= 360.0f;
            }
            while(angle < 0)
            {
                angle += 360.0f;
            }
            float ret = angle;
            if (angle > 180)
            {
                ret = angle - 360;
            }
            return ret;
        }

        public static float MapTo(float a, float oldMin, float oldMax, float newMin, float newMax)
        {
            UnityEngine.Debug.Assert(oldMax > oldMin && newMax > newMin);
            float percent = Mathf.Clamp01((a - oldMin) / (oldMax - oldMin));
            return newMin + percent * (newMax - newMin);
        }

        public static Vector2Int ClampCoordinate(Vector2Int coord, int rows, int cols)
        {
            return new Vector2Int(Mathf.Clamp(coord.x, 0, cols - 1), Mathf.Clamp(coord.y, 0, rows - 1));
        }

        public static bool IsNotDefaultRotationOrScale(Quaternion rotation, Vector3 scale)
        {
            return rotation != Quaternion.identity || scale != Vector3.one;
        }

        public static void DestroyObject(UnityEngine.Object obj)
        {
#if UNITY_EDITOR
            if (!EditorApplication.isPlaying)
            {
                if (obj != null)
                {
                    UnityEngine.Object.DestroyImmediate(obj);
                }

                return;
            }
#endif
            UnityEngine.Object.Destroy(obj);
        }

        public static Vector2 GetViewportSizeFromScreen(Camera camera)
        {
            var p1 = Utils.FromScreenToWorldPosition(Vector2.zero, camera);
            var p2 = Utils.FromScreenToWorldPosition(new Vector2(camera.pixelWidth, camera.pixelHeight), camera);
            var p3 = Utils.FromScreenToWorldPosition(new Vector2(camera.pixelWidth, 0), camera);
            var p4 = Utils.FromScreenToWorldPosition(new Vector2(0, camera.pixelHeight), camera);
            Bounds bounds = new Bounds();
            bounds.SetMinMax(p1, p1);
            bounds.Encapsulate(p2);
            bounds.Encapsulate(p3);
            bounds.Encapsulate(p4);
            var size = bounds.size;
            float max = Mathf.Max(size.x, size.z);
            return new Vector2(max, max);
        }

        public static float EaseInQuad(float s, float e, float t)
        {
            return (e - s) * t * t + s;
        }

        public static float EaseOutQuart(float s, float e, float t)
        {
            float c = e - s;
            t--;
            return -c * (t * t * t * t - 1) + s;
        }

        public static float EaseOutCubic(float s, float e, float t)
        {
            float c = e - s;
            t--;
            return c * (t * t * t + 1) + s;
        }

        public static float EaseInCubic(float s, float e, float t)
        {
            float c = e - s;
            return c * t * t * t + s;
        }

        public static float EaseInQuart(float s, float e, float t)
        {
            float c = e - s;
            return c * t * t * t * t + s;
        }

        public static float EaseOutQuad(float s, float e, float t)
        {
            float c = e - s;
            return -c * t * (t - 2) + s;
        }

        public static float EaseOutExpo(float s, float e, float t)
        {
            float c = e - s;
            return c * (-Mathf.Pow(2.0f, -10.0f * t) + 1.0f) + s;
        }

        public static float EaseInQuint(float s, float e, float t)
        {
            float c = e - s;
            return c * t * t * t * t * t + s;
        }

        public static Vector3 GetDirectionVectorFromScreenPoint(Camera camera, float fov, Vector2 screenPoint)
        {
            float halfWidth = camera.pixelWidth * 0.5f;
            float halfHeight = camera.pixelHeight * 0.5f;
            float halfViewportHeight = camera.nearClipPlane * Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad);
            float halfViewportWidth = camera.aspect * halfViewportHeight;
            float rx = (screenPoint.x - halfWidth) / halfWidth;
            float ry = (screenPoint.y - halfHeight) / halfHeight;
            float x = rx * halfViewportWidth;
            float y = ry * halfViewportHeight;
            float z = camera.nearClipPlane;
            var cameraSpaceDir = new Vector3(x, y, z);
            cameraSpaceDir.Normalize();

            var rot = camera.transform.rotation;
            var worldSpaceDir = rot * cameraSpaceDir;
            return worldSpaceDir;
        }

        public static bool GE(float a, float b, float esp = 0.0001f)
        {
            if (Utils.Approximately(a, b, esp))
            {
                return true;
            }

            if (a - b >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LE(float a, float b, float esp = 0.0001f)
        {
            if (Utils.Approximately(a, b, esp))
            {
                return true;
            }

            if (b - a >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LT(float a, float b, float esp = 0.0001f)
        {
            if (a <= b - esp)
            {
                return true;
            }

            return false;
        }

        public static bool GT(float a, float b, float esp = 0.0001f)
        {
            if (a >= b + esp)
            {
                return true;
            }

            return false;
        }

        public static bool GTE(float a, float b, float esp)
        {
            if (a - b >= esp)
            {
                return true;
            }

            return false;
        }

        public static bool LTE(float a, float b, float esp)
        {
            if (b - a >= esp)
            {
                return true;
            }

            return false;
        }

        public static int CompareFloat(float a, float b, float esp)
        {
            if (Approximately(a, b, esp))
            {
                return 0;
            }

            if (LT(a, b, esp))
            {
                return -1;
            }

            return 1;
        }

        public static bool RectEmpty(Rect r)
        {
            if (Mathf.Approximately(r.width, 0))
            {
                return true;
            }

            if (Mathf.Approximately(r.height, 0))
            {
                return true;
            }

            return false;
        }

        static bool IsInBlock(Vector3 pos, int x, int y, float blockSize)
        {
            float minX = x * blockSize;
            float minZ = y * blockSize;
            float maxX = minX + blockSize;
            float maxZ = minZ + blockSize;

            if (pos.x > minX && pos.x < maxX &&
                pos.z > minZ && pos.z < maxZ)
            {
                return true;
            }

            return false;
        }

        class WorldSpacePrefabOutline : IObstacle
        {
            public bool IsSimplePolygon(PrefabOutlineType type)
            {
                return true;
            }
            //是否可扩充,有些障碍物不能扩充,不然会有问题.例如圆形地图的边界
            public bool IsExtendable()
            {
                return true;
            }

            public List<Vector3> GetOutlineVertices(PrefabOutlineType type)
            {
                List<Vector3> vertices = new List<Vector3>();
                vertices.AddRange(mWorldSpaceVertices[(int)type]);
                return vertices;
            }

            public void SetWorldSpaceOutlineVertices(PrefabOutlineType type, Vector3[] vertices)
            {
                mWorldSpaceVertices[(int)type] = vertices;
            }

            public Vector3[] GetWorldSpaceOutlineVertices(PrefabOutlineType type)
            {
                return mWorldSpaceVertices[(int)type];
            }

            public GameObject gameObject { get { return null; } }
            public Rect GetOutlineBounds(PrefabOutlineType type)
            {
                UnityEngine.Debug.Assert(false, "todo");
                return new Rect(0, 0, 0, 0);
            }
            public Vector3 offset { set { } get { return Vector3.zero; } }

            Vector3[][] mWorldSpaceVertices = new Vector3[2][];
        }

#if UNITY_EDITOR
        public static List<IObstacle> GetGridModelLayerObstacles(bool useLayerOffset,
            HashSet<PrefabOutline> allPrefabOutlines)
        {
            List<IObstacle> obstacles = new List<IObstacle>();
            var map = Map.currentMap;
            var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_FRONT) as EditorGridModelLayer;
            if (layer != null)
            {
                int rows = layer.verticalTileCount;
                int cols = layer.horizontalTileCount;

                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        var tileData = layer.GetObjectData(j, i);
                        if (tileData != null)
                        {
                            //如果game object是2d模式,则需要转换成3d模式才能获取prefaboutline
                            bool isTextureModel = tileData.HasFlag((int)ObjectFlag.kUseRenderTextureModel);
                            if (isTextureModel)
                            {
                                layer.SwitchModel(j, i, false);
                            }

                            var gameObject = layer.GetObjectGameObject(tileData.GetEntityID());
                            if (gameObject != null)
                            {
                                var prefabOutlines = gameObject.GetComponentsInChildren<PrefabOutline>();
                                int n = 0;
                                if (prefabOutlines != null)
                                {
                                    n = prefabOutlines.Length;
                                }

                                for (int p = 0; p < n; ++p)
                                {
                                    var obstacle = new WorldSpacePrefabOutline();
                                    obstacle.SetWorldSpaceOutlineVertices(PrefabOutlineType.NavMeshObstacle, prefabOutlines[p].GetWorldSpaceOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                                    obstacle.SetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle, prefabOutlines[p].GetWorldSpaceOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle));

                                    allPrefabOutlines.Add(prefabOutlines[p]);

                                    var proxy = new ObstacleProxy(obstacle, Vector3.zero);
                                    obstacles.Add(proxy);
                                }
                            }
                            else
                            {
                                UnityEngine.Debug.LogError("Game Object还未加载,请将Viewport设置为全地图再尝试!");
                            }

                            if (isTextureModel)
                            {
                                layer.SwitchModel(j, i, true);
                            }
                        }
                    }
                }
            }

            return obstacles;
        }

        public static List<IObstacle> GetComplexGridModelLayerObstacles(HashSet<PrefabOutline> allPrefabOutlines)
        {
            List<IObstacle> obstacles = new List<IObstacle>();
            var map = Map.currentMap;
            var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_DECORATION) as EditorComplexGridModelLayer;
            HashSet<int> processedObjects = new HashSet<int>();
            if (layer != null)
            {
                int rows = layer.verticalTileCount;
                int cols = layer.horizontalTileCount;

                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        var objects = layer.GetObjectsInGrid(0, j, i);
                        for (int o = 0; o < objects.Count; ++o)
                        {
                            int objectID = objects[o].GetEntityID();
                            if (!processedObjects.Contains(objectID))
                            {
                                bool useRenderTexture = objects[o].useRenderTextureModel;
                                if (useRenderTexture)
                                {
                                    layer.ChangeObjectModel(objectID, false);
                                }

                                processedObjects.Add(objectID);
                                var gameObject = layer.GetGameObject(objectID);
                                if (gameObject != null)
                                {
                                    var prefabOutlines = gameObject.GetComponentsInChildren<PrefabOutline>();
                                    int n = 0;
                                    if (prefabOutlines != null)
                                    {
                                        n = prefabOutlines.Length;
                                    }

                                    for (int p = 0; p < n; ++p)
                                    {
                                        allPrefabOutlines.Add(prefabOutlines[p]);
                                        //var pos = prefabOutlines[p].transform.position;
                                        var proxy = new ObstacleProxy(prefabOutlines[p], Vector3.zero);
                                        obstacles.Add(proxy);
                                    }
                                }
                                else
                                {
                                    UnityEngine.Debug.LogError("Game Object还未加载,请将Viewport设置为全地图再尝试!");
                                }

                                if (useRenderTexture)
                                {
                                    layer.ChangeObjectModel(objectID, true);
                                }
                            }
                        }
                    }
                }
            }

            return obstacles;
        }
#endif

        static List<IObstacle> GetModelLayerObstacles(string name)
        {
            List<IObstacle> obstacles = new List<IObstacle>();
            var map = Map.currentMap;
            var layer = map.GetMapLayer(name) as ModelLayer;
            if (layer != null)
            {
                List<IMapObjectData> objects = new List<IMapObjectData>();
                layer.GetAllObjects(objects);
                for (int i = 0; i < objects.Count; ++i)
                {
                    //var modelTemplate = objects[i].GetModelTemplate();
                    //var prefabOutlines = modelTemplate.GetPrefabOutlines();
                    var gameObject = layer.GetObjectGameObject(objects[i].GetEntityID());
                    if (gameObject != null)
                    {
                        var prefabOutlines = gameObject.GetComponentsInChildren<PrefabOutline>();

                        int n = 0;
                        if (prefabOutlines != null)
                        {
                            n = prefabOutlines.Length;
                        }

                        for (int p = 0; p < n; ++p)
                        {
                            var pos = objects[i].GetPosition();
                            var proxy = new ObstacleProxy(prefabOutlines[p], pos);
                            obstacles.Add(proxy);
                        }
                    }
                    else
                    {
                        UnityEngine.Debug.LogError("Game Object还未加载,请将Viewport设置为全地图再尝试!");
                    }
                }
            }

            return obstacles;
        }

        static List<IObstacle> GetRailwayLayerObstacles()
        {
            List<IObstacle> obstacles = new List<IObstacle>();
            var map = Map.currentMap;
            var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_RAILWAY) as RailwayLayer;
            if (layer != null)
            {
                var outlines = layer.GetColliderOutlines();
                for (int i = 0; i < outlines.Count; ++i)
                {
                    var proxy = new ObstacleProxy(outlines[i], Vector3.zero);
                    obstacles.Add(proxy);
                }
            }

            return obstacles;
        }

        static List<IObstacle> GetCollisionLayerObstacles(PrefabOutlineType outlineType,
            CheckMapCollisionOperation operation, bool treatSpecialRegionAsObstacle,
            bool ignoreSpecialRegionOfAreaType0)
        {
            List<IObstacle> obstacles = new List<IObstacle>();
#if UNITY_EDITOR
            var map = Map.currentMap;
            var layer = map.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_COLLISION) as MapCollisionLayer;
            if (layer != null)
            {
                List<IMapObjectData> objects = new List<IMapObjectData>();
                layer.GetAllObjects(objects);
                for (int i = 0; i < objects.Count; ++i)
                {
                    var collisionData = objects[i] as MapCollisionData;
                    var attribute = collisionData.attribute;
                    //不考虑这种类型的collision
                    if (attribute.HasFlag(CollisionAttribute.TestCollisionWhenPlaceDecorationObject) ||
                        attribute.HasFlag(CollisionAttribute.CameraLookAtArea) ||
                        attribute.HasFlag(CollisionAttribute.IsFrontLayerObjectClipper))
                    {
                        continue;
                    }

                    if (operation != CheckMapCollisionOperation.kGenerateRuinPoints &&
                        attribute.HasFlag(CollisionAttribute.RuinPointClipper))
                    {
                        continue;
                    }

                    switch (operation)
                    {
                        case CheckMapCollisionOperation.kCalculateValidArea:
                            if (!attribute.HasFlag(CollisionAttribute.CanPlaceNPCSpawnPoint) &&
                                !attribute.HasFlag(CollisionAttribute.SpecialRegion))
                            {
                                if (!attribute.HasFlag(CollisionAttribute.Walkable))
                                {
                                    //只包含不能放npc随机点的区域
                                    obstacles.Add(objects[i] as IObstacle);
                                }
                            }

                            break;
                        case CheckMapCollisionOperation.kCreateNPCSpawnPoints:
                            if (!attribute.HasFlag(CollisionAttribute.CanPlaceNPCSpawnPoint))
                            {
                                //只包含不能放npc随机点的区域
                                obstacles.Add(objects[i] as IObstacle);
                            }

                            break;
                        case CheckMapCollisionOperation.kGenerateMapObstacles:
                        {
                            if (!attribute.HasFlag(CollisionAttribute.CanPlaceNPCSpawnPoint) &&
                                !attribute.HasFlag(CollisionAttribute.SpecialRegion))
                            {
                                //只包含不能放npc随机点的区域
                                if (!attribute.HasFlag(CollisionAttribute.Walkable))
                                {
                                    obstacles.Add(objects[i] as IObstacle);
                                }
                            }
                        }
                            break;
                        case CheckMapCollisionOperation.kGenerateNavMesh:
                        {
                            if (treatSpecialRegionAsObstacle && attribute.HasFlag(CollisionAttribute.SpecialRegion))
                            {
                                if (ignoreSpecialRegionOfAreaType0)
                                {
                                    //忽略area type为0的special region
                                    var collsionData = objects[i] as MapCollisionData;
                                    if (collsionData.type != 0)
                                    {
                                        obstacles.Add(objects[i] as IObstacle);
                                    }
                                }
                                else
                                {
                                    obstacles.Add(objects[i] as IObstacle);
                                }
                            }
                            else
                            {
                                if (
                                    !attribute.HasFlag(CollisionAttribute.Walkable) &&
                                    !attribute.HasFlag(CollisionAttribute.CanNotTeleportOnly) &&
                                    !attribute.HasFlag(CollisionAttribute.SpecialRegion)
                                )
                                {
                                    //只包含不能放npc随机点的区域
                                    obstacles.Add(objects[i] as IObstacle);
                                }
                            }
                        }
                            break;
                        case CheckMapCollisionOperation.kGenerateWaterNavMesh:
                        {
                            //在计算海水区域时,所有collision都算障碍物
                            obstacles.Add(objects[i] as IObstacle);
                        }
                            break;
                        case CheckMapCollisionOperation.kGenerateRuinPoints:
                        {
                            if ((!attribute.HasFlag(CollisionAttribute.CanPlaceRuinPoint) &&
                                 !attribute.HasFlag(CollisionAttribute.SpecialRegion)) ||
                                attribute.HasFlag(CollisionAttribute.RuinPointClipper))
                            {
                                //只包含不能放npc随机点的区域
                                obstacles.Add(objects[i] as IObstacle);
                            }
                        }
                            break;
                    }
                }
            }
#endif
            return obstacles;
        }

        public static List<IObstacle> CreateObstacles(LayerTypeMask typeMask, bool useLayerOffset,
            PrefabOutlineType type, CheckMapCollisionOperation operation, bool treatSpecialRegionAsObstacle,
            bool ignoreSpecialRegionsWithAreaType0, bool findOtherLayerPrefabOutlines)
        {
#if UNITY_EDITOR
            //从前景层中找到各个地块使用的障碍物数据
            List<IObstacle> obstacles = new List<IObstacle>();

            if (MapModule.usePrefabOutline)
            {
                HashSet<PrefabOutline> prefabOutlines = new HashSet<PrefabOutline>();
                if (typeMask.HasFlag(LayerTypeMask.kGridModelLayer))
                {
                    var obs = GetGridModelLayerObstacles(useLayerOffset, prefabOutlines);
                    obstacles.AddRange(obs);
                }

                if (typeMask.HasFlag(LayerTypeMask.kComplexGridModelLayer))
                {
                    var obs = GetComplexGridModelLayerObstacles(prefabOutlines);
                    obstacles.AddRange(obs);
                }

                if (findOtherLayerPrefabOutlines)
                {
                    var allPrefabOutlines = UnityEngine.Object.FindObjectsOfType<PrefabOutline>();
                    for (int p = 0; p < allPrefabOutlines.Length; ++p)
                    {
                        if (prefabOutlines.Contains(allPrefabOutlines[p]) == false)
                        {
                            var proxy = new ObstacleProxy(allPrefabOutlines[p], Vector3.zero);
                            obstacles.Add(proxy);
                        }
                    }
                }
            }

            if (typeMask.HasFlag(LayerTypeMask.kModelLayer))
            {
                var modelLayerObstacles = GetModelLayerObstacles(MapCoreDef.MAP_LAYER_NODE_DYNAMIC);
                obstacles.AddRange(modelLayerObstacles);
            }

            if (typeMask.HasFlag(LayerTypeMask.kCollisionLayer))
            {
                var obs = GetCollisionLayerObstacles(type, operation, treatSpecialRegionAsObstacle,
                    ignoreSpecialRegionsWithAreaType0);
                obstacles.AddRange(obs);
            }

            return obstacles;
#else
            return null;
#endif
        }

        public static NavMeshBlock[] CreateNavMesh(LayerTypeMask typeMask,
            int xTileCount, int yTileCount, PrefabOutlineType type, bool useDelaunay, NavigationCreateMode createMode,
            bool treatSpecialRegionAsObstacle, bool oceanAreaWalkable, bool findOtherLayerPrefabOutlines, bool removeSameHoles,
            float radius = 0, float minimumAngle = 100, float maximumArea = 180000)
        {
            xTileCount = 1;
            yTileCount = 1;
#if UNITY_EDITOR
            //先加载所有的模型
            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start();
            var map = Map.currentMap;

            //创建地图上所有的障碍物数据
            CheckMapCollisionOperation operation;
            bool walkable = false;
            if (createMode == NavigationCreateMode.CreateIslandAndOceanNavigationMesh ||
                createMode == NavigationCreateMode.CreateIslandNavigationMesh ||
                createMode == NavigationCreateMode.CreateLandNavigationMesh ||
                createMode == NavigationCreateMode.CreateClosedAreaAndGateNavMesh)
            {
                operation = CheckMapCollisionOperation.kGenerateNavMesh;
                walkable = true;
            }
            else
            {
                operation = CheckMapCollisionOperation.kGenerateMapObstacles;
            }

            var obstacles = CreateObstacles(typeMask, !walkable, type, operation, treatSpecialRegionAsObstacle, false,
                findOtherLayerPrefabOutlines);

            NavMeshBlock[] blockMeshies = new NavMeshBlock[1];
            List<IObstacle> obs = new List<IObstacle>();
            for (int i = 0; i < obstacles.Count; ++i)
            {
                if (obstacles[i].IsSimplePolygon(type))
                {
                    obs.Add(obstacles[i]);
                }
            }

            blockMeshies[0] = new NavMeshBlock();
            var mapDataGenerationRange = Map.currentMap.data.mapDataGenerationRange;
            NavMeshCreator.CreateNavMesh(type, mapDataGenerationRange.min,
                mapDataGenerationRange.max, obs, radius, minimumAngle,
                maximumArea, useDelaunay, createMode, oceanAreaWalkable, 1f, removeSameHoles, out blockMeshies[0].vertices,
                out blockMeshies[0].indices, out blockMeshies[0].triangleTypes, out blockMeshies[0].triangleStates);

            stopWatch.Stop();

            long elapsed = stopWatch.ElapsedMilliseconds;

            UnityEngine.Debug.Log("Creat NavMesh cost " + elapsed + " ms");
            return blockMeshies;
#else
            return null;
#endif
        }

        public static bool TriangleCircleIntersection2D(float centerX, float centerY, float radius, Vector2[] vertices)
        {
#if false
            float radiusSquared = radius * radius;

            Vector2 vertex = vertices[vertices.Length - 1];
            Vector2 center = new Vector2(centerX, centerY);

            float nearestDistance = Single.MaxValue;
            bool nearestIsInside = false;
            int nearestVertex = -1;
            bool lastIsInside = false;

            for (int i = 0; i < vertices.Length; i++)
            {
                Vector2 nextVertex = vertices[i];

                Vector2 axis = center - vertex;

                float distance = axis.sqrMagnitude - radiusSquared;

                if (distance <= 0)
                {
                    return true;
                }

                bool isInside = false;

                Vector2 edge = nextVertex - vertex;

                float edgeLengthSquared = edge.sqrMagnitude;

                if (edgeLengthSquared != 0)
                {
                    float dot = Vector3.Dot(edge, axis);

                    if (dot >= 0 && dot <= edgeLengthSquared)
                    {
                        Vector2 projection = vertex + (dot / edgeLengthSquared) * edge;

                        axis = projection - center;

                        if (axis.sqrMagnitude <= radiusSquared)
                        {
                            return true;
                        }
                        else
                        {
                            if (edge.x > 0)
                            {
                                if (axis.y > 0)
                                {
                                    return false;
                                }
                            }
                            else if (edge.x < 0)
                            {
                                if (axis.y < 0)
                                {
                                    return false;
                                }
                            }
                            else if (edge.y > 0)
                            {
                                if (axis.x < 0)
                                {
                                    return false;
                                }
                            }
                            else
                            {
                                if (axis.x > 0)
                                {
                                    return false;
                                }
                            }

                            isInside = true;
                        }
                    }
                }

                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestIsInside = isInside || lastIsInside;
                    nearestVertex = i;
                }

                vertex = nextVertex;
                lastIsInside = isInside;
            }

            if (nearestVertex == 0)
            {
                return nearestIsInside || lastIsInside;
            }
            else
            {
                return nearestIsInside;
            }
#else
            int insideEdge = 0;
            var center = new Vector2(centerX, centerY);
            for (int i = 0; i < vertices.Length; ++i)
            {
                var centerToVertex = center - vertices[i];
                float centerToVertexDistance = centerToVertex.magnitude;
                if (centerToVertexDistance <= radius)
                {
                    return true;
                }

                var edgeDir = vertices[(i + 1) % vertices.Length] - vertices[i];
                float edgeDirDistance = edgeDir.magnitude;
                //normalize edgeDir
                edgeDir /= edgeDirDistance;
                var projDistance = Vector2.Dot(centerToVertex, edgeDir);
                if (projDistance >= 0 && projDistance <= edgeDirDistance)
                {
                    //normalize centerToVertex
                    centerToVertex /= centerToVertexDistance;

                    float perpDistance =
                        Mathf.Sqrt(centerToVertexDistance * centerToVertexDistance - projDistance * projDistance);
                    if (perpDistance <= radius)
                    {
                        return true;
                    }

                    //lies inside edge region, check if center is inside or outside of polygon
                    float y = centerToVertex.y * edgeDir.x - edgeDir.y * centerToVertex.x;
                    if (y < 0)
                    {
                        //center is outside of polygon
                        return false;
                    }
                    else
                    {
                        ++insideEdge;
                    }
                }
                else
                {
                    float y = centerToVertex.y * edgeDir.x - edgeDir.y * centerToVertex.x;
                    if (y >= 0)
                    {
                        ++insideEdge;
                    }
                }
            }

            if (insideEdge == 3)
            {
                return true;
            }

            return false;
#endif
        }

        public static bool PointInRect2D(Vector2 p, Vector2 rectMin, Vector2 rectMax)
        {
            return p.x >= rectMin.x && p.x <= rectMax.x &&
                   p.y >= rectMin.y && p.y <= rectMax.y;
        }

        public static bool PointInTriangle(Vector2 p, Vector2 a, Vector2 b, Vector2 c)
        {
            Vector2 v0 = b - a, v1 = c - a, v2 = p - a;
            float d00 = Vector2.Dot(v0, v0);
            float d01 = Vector2.Dot(v0, v1);
            float d11 = Vector2.Dot(v1, v1);
            float d20 = Vector2.Dot(v2, v0);
            float d21 = Vector2.Dot(v2, v1);
            float denom = d00 * d11 - d01 * d01;
            var v = (d11 * d20 - d01 * d21) / denom;
            var w = (d00 * d21 - d01 * d20) / denom;
            var u = 1.0f - v - w;

            return (u >= 0) && (v >= 0) && (u + v < 1);
        }


        public static bool TriangleRectangleIntersection2D(
            float rectMinX, float rectMinY, float rectMaxX, float rectMaxY,
            float v0X, float v0Y, float v1X, float v1Y, float v2X, float v2Y)
        {
            var rectMin = new Vector2(rectMinX, rectMinY);
            var rectMax = new Vector2(rectMaxX, rectMaxY);
            var v0 = new Vector2(v0X, v0Y);
            var v1 = new Vector2(v1X, v1Y);
            var v2 = new Vector2(v2X, v2Y);

            if (PointInTriangle(rectMin, v0, v1, v2) || PointInTriangle(rectMax, v0, v1, v2))
            {
                return true;
            }

            if (PointInRect2D(v0, rectMin, rectMax) &&
                PointInRect2D(v1, rectMin, rectMax) &&
                PointInRect2D(v2, rectMin, rectMax)
               )
            {
                return true;
            }

            if (LineRectIntersectionTest(v0, v1, rectMin, rectMax))
            {
                return true;
            }

            if (LineRectIntersectionTest(v1, v2, rectMin, rectMax))
            {
                return true;
            }

            if (LineRectIntersectionTest(v2, v0, rectMin, rectMax))
            {
                return true;
            }

            return false;
        }

        // LINE/RECTANGLE
        public static bool LineRectIntersectionTest(Vector2 lineStart, Vector2 lineEnd, Vector2 rectMin,
            Vector2 rectMax)
        {
            float x1 = lineStart.x;
            float y1 = lineStart.y;
            float x2 = lineEnd.x;
            float y2 = lineEnd.y;
            float rx = rectMin.x;
            float ry = rectMin.y;
            float rw = rectMax.x - rectMin.x;
            float rh = rectMax.y - rectMin.y;
            // check if the line has hit any of the rectangle's sides
            // uses the Line/Line function below
            bool left = LineLine(x1, y1, x2, y2, rx, ry, rx, ry + rh);
            bool right = LineLine(x1, y1, x2, y2, rx + rw, ry, rx + rw, ry + rh);
            bool top = LineLine(x1, y1, x2, y2, rx, ry, rx + rw, ry);
            bool bottom = LineLine(x1, y1, x2, y2, rx, ry + rh, rx + rw, ry + rh);

            // if ANY of the above are true, the line
            // has hit the rectangle
            if (left || right || top || bottom)
            {
                return true;
            }

            return false;
        }


        // LINE/LINE
        static bool LineLine(float x1, float y1, float x2, float y2, float x3, float y3, float x4, float y4)
        {
            // calculate the direction of the lines
            float uA = ((x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)) /
                       ((y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1));
            float uB = ((x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)) /
                       ((y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1));

            // if uA and uB are between 0-1, lines are colliding
            if (uA >= 0 && uA <= 1 && uB >= 0 && uB <= 1)
            {
                return true;
            }

            return false;
        }

        public static bool GetLineRectIntersection(Vector2 lineStart, Vector2 lineEnd, Vector2 rectMin, Vector2 rectMax,
            out Vector2 point)
        {
            float maxTIn = float.MinValue;
            float minTOut = float.MaxValue;
            var d = lineEnd - lineStart;
            for (int i = 0; i < 2; ++i)
            {
                if (!Mathf.Approximately(d[i], 0))
                {
                    float tmin = (rectMin[i] - lineStart[i]) / d[i];
                    float tmax = (rectMax[i] - lineStart[i]) / d[i];

                    if (d[i] > 0)
                    {
                        //min plane is input, max plane is output
                        if (tmin > maxTIn)
                        {
                            maxTIn = tmin;
                        }

                        if (tmax < minTOut)
                        {
                            minTOut = tmax;
                        }
                    }
                    else
                    {
                        //max plane is input, min plane is output
                        if (tmin < minTOut)
                        {
                            minTOut = tmin;
                        }

                        if (tmax > maxTIn)
                        {
                            maxTIn = tmax;
                        }
                    }
                }
            }

            point = lineStart + maxTIn * d;
            if (maxTIn > minTOut)
            {
                return false;
            }

            if (maxTIn >= 0 && maxTIn <= 1)
            {
                return true;
            }

            return false;
        }

        public static bool IsCircleCircleOverlap(float x1, float z1, float r1, float x2, float z2, float r2)
        {
            float dr = r1 - r2;
            dr *= dr;
            float dx = x1 - x2;
            float dz = z1 - z2;
            dx *= dx;
            dz *= dz;
            return (dx + dz) <= dr;
        }

        //判断矩形是否和圆相交
        public static bool IsRectCircleIntersected(float rectMinX, float rectMinY, float rectMaxX, float rectMaxY,
            float circleX, float circleY, float circleRadius)
        {
            if (circleX >= rectMinX && circleX <= rectMaxX &&
                circleY >= rectMinY && circleY <= rectMaxY)
            {
                return true;
            }

            float dis = 0;
            float d = 0;
            if (circleX >= rectMaxX)
            {
                d = circleX - rectMaxX;
            }
            else if (circleX <= rectMinX)
            {
                d = rectMinX - circleX;
            }

            dis += d * d;

            if (circleY >= rectMaxY)
            {
                d = circleY - rectMaxY;
            }
            else if (circleY <= rectMinY)
            {
                d = rectMinY - circleY;
            }

            dis += d * d;

            if (dis <= circleRadius * circleRadius)
            {
                return true;
            }

            return false;
        }

        //计算矩形到圆的距离
        public static float GetRectToCircleDistance(Vector2 rectCenter, Vector2 rectSize, Vector2 circleCenter,
            float circleRadius)
        {
            Vector2 rectMax = rectCenter + rectSize * 0.5f;
            Vector2 rectMin = rectCenter - rectSize * 0.5f;

            if (circleCenter.x >= rectMin.x && circleCenter.x <= rectMax.x &&
                circleCenter.y >= rectMin.y && circleCenter.y <= rectMax.y)
            {
                return 0;
            }

            float dis = 0;
            for (int i = 0; i < 2; ++i)
            {
                float d = 0;
                if (circleCenter[i] >= rectMax[i])
                {
                    d = circleCenter[i] - rectMax[i];
                }
                else if (circleCenter[i] <= rectMin[i])
                {
                    d = rectMin[i] - circleCenter[i];
                }

                dis += d * d;
            }

            return Mathf.Sqrt(dis);
        }

        public static float GetAngleBetween(Vector2 dir0, Vector2 dir1)
        {
            dir0.Normalize();
            dir1.Normalize();
            float dot = Vector2.Dot(dir0, dir1);
            if (dot >= 1.0f)
            {
                return 0;
            }
            else if (dot <= -1.0f)
            {
                return 180.0f;
            }

            return Mathf.Acos(dot) * Mathf.Rad2Deg;
        }

        public static Bounds RectToBounds(Rect r)
        {
            var center = r.center;
            var size = r.size;
            return new Bounds(new Vector3(center.x, 0, center.y), new Vector3(size.x, 0, size.y));
        }

        public static Rect BoundsToRect(Bounds b)
        {
            var min = b.min;
            var size = b.size;
            return new Rect(new Vector2(min.x, min.z), new Vector2(size.x, size.z));
        }

        public static bool RectOverlap(Rect a, Rect b)
        {
            var amin = a.min;
            var amax = a.max;
            var bmin = b.min;
            var bmax = b.max;

            if (amin.x > bmax.x || amin.y > bmax.y ||
                bmin.x > amax.x || bmin.y > amax.y)
            {
                return false;
            }

            return true;
        }

        public static bool RectOverlap(float minXA, float minYA, float maxXA, float maxYA, float minXB, float minYB,
            float maxXB, float maxYB)
        {
            if (minXA > maxXB || minYA > maxYB ||
                minXB > maxXA || minYB > maxYA)
            {
                return false;
            }

            return true;
        }

        public static bool RectIntOverlap(RectInt a, RectInt b)
        {
            var amin = a.min;
            var amax = a.max;
            var bmin = b.min;
            var bmax = b.max;

            if (amin.x > bmax.x || amin.y > bmax.y ||
                bmin.x > amax.x || bmin.y > amax.y)
            {
                return false;
            }

            return true;
        }

        public static float HeightToViewDistance(float xRot, float height)
        {
            return (float) ((double) height / Math.Sin(xRot * 0.0174532924));
        }

        public static Vector3 Abs(Vector3 v)
        {
            return new Vector3(Mathf.Abs(v.x), Mathf.Abs(v.y), Mathf.Abs(v.z));
        }

        public static int MakeInt32Key(int a, int b)
        {
            return a | (b << 16);
        }

        public static long MakeInt64Key(int a, int b)
        {
            long id64 = (long) a;
            long type64 = (long) b;
            id64 <<= 32;
            return type64 | id64;
        }

        public static Transform GetRootTransform(Transform t)
        {
            Transform root = t;
            while (root.parent != null)
            {
                root = root.parent;
            }

            return root;
        }

        public static List<Vector3> ToVector3List(Vector3[] vertices)
        {
            if (vertices == null)
            {
                return null;
            }

            List<Vector3> v3 = new List<Vector3>();
            v3.AddRange(vertices);

            return v3;
        }

        public static List<int> ToIntList(int[] indices)
        {
            if (indices == null)
            {
                return null;
            }

            List<int> v3 = new List<int>();
            v3.AddRange(indices);

            return v3;
        }

        public static List<Vector3> ConvertToVector3List(List<Vector2> vertices)
        {
            if (vertices == null)
            {
                return null;
            }

            List<Vector3> v3 = new List<Vector3>();
            for (int i = 0; i < vertices.Count; ++i)
            {
                v3.Add(new Vector3(vertices[i].x, 0, vertices[i].y));
            }

            return v3;
        }

        public static List<Vector2> ConvertToVector2List(List<Vector3> vertices)
        {
            if (vertices == null)
            {
                return null;
            }

            List<Vector2> v2 = new List<Vector2>();
            for (int i = 0; i < vertices.Count; ++i)
            {
                v2.Add(new Vector2(vertices[i].x, vertices[i].z));
            }

            return v2;
        }

        //只求垂直距离,没考虑point与s和e的距离
        //inBetween:point的投影是否在s和e之间
        public static float GetPointToSegmentDistance(Vector3 point, Vector3 s, Vector3 e, out bool inBetween)
        {
            var d = (e - s).normalized;
            var ps = point - s;
            var proj = Vector3.Dot(ps, d) * d;
            var perp = ps - proj;

            var pe = point - e;
            pe.Normalize();
            ps.Normalize();
            var d1 = Vector3.Dot(ps, d);
            var d2 = Vector3.Dot(pe, -d);
            inBetween = (d1 >= 0 && d2 >= 0);
            return perp.magnitude;
        }

        public static int FindNearestEdgeDistance(Vector3 localPos, List<Vector3> polygonVertices)
        {
            int edgeCount = polygonVertices.Count - 1;
            if (edgeCount <= 0)
            {
                return 0;
            }

            float minDistance = float.MaxValue;
            int minEdgeIndex = -1;
            bool inBetween;
            for (int i = 0; i < edgeCount; ++i)
            {
                var s = polygonVertices[i];
                var e = polygonVertices[(i + 1) % edgeCount];
                float perpDistance = GetPointToSegmentDistance(localPos, s, e, out inBetween);
                if (inBetween)
                {
                    if (perpDistance < minDistance)
                    {
                        minEdgeIndex = i;
                        minDistance = perpDistance;
                    }
                }
            }

            //check end point
            float disStart = Vector3.Distance(localPos, polygonVertices[0]);
            float disEnd = Vector3.Distance(localPos, polygonVertices[polygonVertices.Count - 1]);
            if (disStart < disEnd)
            {
                if (disStart < minDistance)
                {
                    return 0;
                }
            }
            else
            {
                if (disEnd < minDistance)
                {
                    return polygonVertices.Count;
                }
            }

            return (minEdgeIndex + 1) % edgeCount;
        }

        public static int FindNearestEdgeDistance(Vector3 localPos, Vector3[] polygonVertices, bool loop)
        {
            int edgeCount = polygonVertices.Length;
            float minDistance = float.MaxValue;
            int minEdgeIndex = -1;
            if (!loop)
            {
                edgeCount -= 1;
            }

            bool betweenFirstSegment = false;
            bool betweenLastSegment = false;
            for (int i = 0; i < edgeCount; ++i)
            {
                var s = polygonVertices[i];
                var e = polygonVertices[(i + 1) % polygonVertices.Length];
                bool inBetween;
                float perpDistance = GetPointToSegmentDistance(localPos, s, e, out inBetween);
                if (i == 0)
                {
                    betweenFirstSegment = inBetween;
                }
                else if (i == edgeCount - 1)
                {
                    betweenLastSegment = inBetween;
                }

                bool considerInBetween = (i > 0 && i < edgeCount - 1);
                if (perpDistance < minDistance)
                {
                    if (!considerInBetween || (considerInBetween && inBetween))
                    {
                        minEdgeIndex = i;
                        minDistance = perpDistance;
                    }
                }
            }

            if (loop)
            {
                return (minEdgeIndex + 1) % polygonVertices.Length;
            }
            else
            {
                if (minEdgeIndex == 0 && !betweenFirstSegment)
                {
                    return 0;
                }
                else if (minEdgeIndex == edgeCount - 1 && !betweenLastSegment)
                {
                    return polygonVertices.Length;
                }

                return minEdgeIndex + 1;
            }
        }

        public static bool ParseFloat(string text, out float val)
        {
            val = 0;
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            bool suc = float.TryParse(text, out val);
            if (!suc)
            {
                return false;
            }

            return true;
        }

        public static bool ParseInt(string text, out int val)
        {
            val = 0;
            if (string.IsNullOrEmpty(text))
            {
                return false;
            }

            bool suc = int.TryParse(text, out val);
            if (!suc)
            {
                return false;
            }

            return true;
        }

        public static bool SegmentSegmentIntersectionTest(Vector2 aStart, Vector2 aEnd, Vector2 bStart, Vector2 bEnd,
            out Vector2 intersectionPoint)
        {
            intersectionPoint = Vector2.zero;
            Vector2 da = aEnd - aStart;
            Vector2 db = bEnd - bStart;
            float delta = da.y * db.x - da.x * db.y;
            if (Mathf.Approximately(delta, 0))
            {
                return false;
            }

            Vector2 k = bStart - aStart;
            float ta = (k.y * db.x - k.x * db.y) / delta;
            float tb = (k.y * da.x - k.x * da.y) / delta;
            if (ta >= 0 && ta < 1.0f && tb >= 0 && tb < 1.0f)
            {
                intersectionPoint = aStart + ta * da;
                return true;
            }

            return false;
        }

        public static string RemovePrefix(string fullPath, string prefix)
        {
            int idx = fullPath.IndexOf(prefix);
            if (idx == 0)
            {
                return fullPath.Substring(prefix.Length + 1);
            }

            UnityEngine.Debug.Assert(false);
            return null;
        }

        public static string ConvertToUnityAssetsPath(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return path;
            }

            path = path.Replace('\\', '/');

            var idx = path.IndexOf("Assets/");
            if (idx >= 0)
            {
                return path.Substring(idx);
            }

            if (path.Length > 0)
            {
                UnityEngine.Debug.Assert(false, "Invalid unity resource path!");
                return "";
            }

            return path;
        }

        public static Vector2 ToVector2(Vector3 v)
        {
            return new Vector2(v.x, v.z);
        }

        public static Vector3 ToVector3(Vector2 v)
        {
            return new Vector3(v.x, 0, v.y);
        }

        public static Bounds CreateBounds(List<Vector3> polygon)
        {
            Vector3 min = Vector3.one * 10000000;
            Vector3 max = Vector3.one * -10000000;
            for (int i = 0; i < polygon.Count; ++i)
            {
                for (int k = 0; k < 3; ++k)
                {
                    if (polygon[i][k] < min[k])
                    {
                        min[k] = polygon[i][k];
                    }

                    if (polygon[i][k] > max[k])
                    {
                        max[k] = polygon[i][k];
                    }
                }
            }

            var bounds = new Bounds();
            bounds.SetMinMax(min, max);
            return bounds;
        }

        public static bool IsPolygonFullInsideOfPolygon(List<Vector3> innerRegion, List<Vector3> outerRegion)
        {
#if UNITY_EDITOR
            for (int i = 0; i < innerRegion.Count; ++i)
            {
                if (!EditorUtils.PointInPolygon2D(innerRegion[i], outerRegion))
                {
                    return false;
                }
            }
#endif
            return true;
        }

        //detect if polygon is full outside of circle
        public static bool IsConvexHullFullOutsideOfCircle(List<Vector3> polygon, Vector3 center, float radius)
        {
            for (int i = 0; i < polygon.Count; ++i)
            {
                var d = (polygon[i] - center).magnitude;
                if (d < radius)
                {
                    return false;
                }
            }

            return true;
        }

        public static bool IsConvexHullFullInsideOfCircle(List<Vector3> polygon, Vector3 center, float radius)
        {
            for (int i = 0; i < polygon.Count; ++i)
            {
                var d = (polygon[i] - center).magnitude;
                if (d > radius)
                {
                    return false;
                }
            }

            return true;
        }

        //这个接口会分配内存,用stream接口替换
        //public static byte[] GetBytes(string dataPath, bool unloadTextAsset = true)
        //{
        //    if (!MapModuleResourceMgr.Exists(dataPath))
        //    {
        //        return null;
        //    }

        //    return MapModuleResourceMgr.LoadTextBytes(dataPath, unloadTextAsset);
        //}

        public static GameObject CreateTextGameObject(string name, string text, Color color, int fontSize = 32,
            float xRot = 70, int renderQueue = 3100)
        {
            UnityEngine.Font ArialFont = (UnityEngine.Font) Resources.GetBuiltinResource(typeof(Font), "Arial.ttf");
            var textObj = new GameObject();
            var renderer = textObj.AddComponent<MeshRenderer>();
            renderer.sharedMaterial = ArialFont.material;
            renderer.sharedMaterial.renderQueue = renderQueue;
            var textMesh = textObj.AddComponent<TextMesh>();
            textMesh.text = text;
            textMesh.fontSize = fontSize;
            textMesh.color = color;
            textObj.transform.rotation = Quaternion.Euler(xRot, 0, 0);
            textObj.name = name;

            return textObj;
        }

        public static Color[] ConvertToColorArray(Color32[] pixels)
        {
            if (pixels == null)
            {
                return null;
            }

            Color[] result = new Color[pixels.Length];
            for (int i = 0; i < pixels.Length; ++i)
            {
                result[i] = pixels[i];
            }

            return result;
        }

        public static Color32[] ConvertToColor32Array(Color[] pixels)
        {
            if (pixels == null)
            {
                return null;
            }

            Color32[] result = new Color32[pixels.Length];
            for (int i = 0; i < pixels.Length; ++i)
            {
                result[i] = pixels[i];
            }

            return result;
        }

        public static Color32 ConvertToColor32(string list)
        {
            var red = list.Substring(0, 2);
            var green = list.Substring(2, 2);
            var blue = list.Substring(4, 2);

            int r, g, b;

            CultureInfo provider;

            // If currency symbol is allowed, use en-US culture.
            provider = CultureInfo.InvariantCulture;

            bool ok = int.TryParse(red, NumberStyles.HexNumber, provider, out r);
            ok |= int.TryParse(green, NumberStyles.HexNumber, provider, out g);
            ok |= int.TryParse(blue, NumberStyles.HexNumber, provider, out b);

            return new Color32((byte) r, (byte) g, (byte) b, 255);
        }

        public static List<int> Substract(List<int> a, List<int> b)
        {
            List<int> difference = new List<int>();
            HashSet<int> hb = new HashSet<int>();
            for (int i = 0; i < b.Count; ++i)
            {
                hb.Add(b[i]);
            }

            for (int i = 0; i < a.Count; ++i)
            {
                if (!hb.Contains(a[i]))
                {
                    difference.Add(a[i]);
                }
            }

            return difference;
        }

        public static bool Approximately(float a, float b, float esp = 0.0001f)
        {
            if (Mathf.Abs(a - b) <= esp)
            {
                return true;
            }

            return false;
        }

        public static bool Approximately(Vector3 a, Vector3 b, float esp = 0.0001f)
        {
            return Approximately(a.x, b.x, esp) &&
                   Approximately(a.y, b.y, esp) &&
                   Approximately(a.z, b.z, esp);
        }

        public static bool Approximately(Vector2 a, Vector2 b, float esp = 0.0001f)
        {
            return Approximately(a.x, b.x, esp) &&
                   Approximately(a.y, b.y, esp);
        }

        public static Rect CalculateRect(IEnumerable<Vector3> vertices)
        {
            float minX = float.MaxValue;
            float minZ = float.MaxValue;
            float maxX = float.MinValue;
            float maxZ = float.MinValue;
            foreach (var local in vertices)
            {
                if (local.x < minX)
                {
                    minX = local.x;
                }

                if (local.z < minZ)
                {
                    minZ = local.z;
                }

                if (local.x > maxX)
                {
                    maxX = local.x;
                }

                if (local.z > maxZ)
                {
                    maxZ = local.z;
                }
            }

            var bounds = new Rect(minX, minZ, maxX - minX, maxZ - minZ);
            return bounds;
        }

        public static ModelLODGroupManager CreateLODGroupManager(TFW.Map.config.MapLayerData layerData, Map map)
        {
            ModelLODGroupManager manager = new ModelLODGroupManager();
            var data = layerData.lodGroupManager;
            if (data != null)
            {
                int nGroups = data.groups.Length;
                for (int i = 0; i < nGroups; ++i)
                {
                    ModelLODGroup group = new ModelLODGroup(data.groups[i].id, map);
                    group.lod = data.groups[i].lod;
                    group.combineModels = data.groups[i].combineModels;
                    group.leaderObjectID = data.groups[i].leaderObjectID;
                    manager.AddGroup(group);
                }
            }

            return manager;
        }

        public static float CalculateViewportWidth(float fov, Map map)
        {
            if (null == map)
            {
                return 1.0f;
            }

            var camera = map.camera.firstCamera;
            float height = Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad) * camera.nearClipPlane * 2.0f;
            float width = height * camera.aspect;
            return width;
        }

        //在编辑器模式下隐藏gameObject
        public static void HideGameObject(GameObject gameObject)
        {
#if UNITY_EDITOR
            if (gameObject != null)
            {
                if (!EditorApplication.isPlaying)
                {
                    gameObject.hideFlags = HideFlags.HideInHierarchy;
                }
            }
#endif
        }

        public static int F2I(float v)
        {
            return (int) (v * 1000);
        }

        public static float I2F(int v)
        {
            return (float) v / 1000.0f;
        }

        public static bool IsPOT(int v)
        {
            if (v <= 0)
            {
                return false;
            }

            return (v & (v - 1)) == 0;
        }

        public static Vector3 CalculateCentroid(GameObject[] objects)
        {
            Vector3 centroid = Vector3.zero;
            for (int i = 0; i < objects.Length; ++i)
            {
                centroid += objects[i].transform.position;
            }

            if (objects.Length > 0)
            {
                centroid /= objects.Length;
            }

            return centroid;
        }

        public static Vector2 CalculateCentroid(Vector2[] vertices)
        {
            Vector2 centroid = Vector2.zero;
            for (int i = 0; i < vertices.Length; ++i)
            {
                centroid += vertices[i];
            }

            if (vertices.Length > 0)
            {
                centroid /= vertices.Length;
            }

            return centroid;
        }

        public static Vector3 CalculateCentroid(Vector3[] vertices)
        {
            Vector3 centroid = Vector3.zero;
            for (int i = 0; i < vertices.Length; ++i)
            {
                centroid += vertices[i];
            }

            if (vertices.Length > 0)
            {
                centroid /= vertices.Length;
            }

            return centroid;
        }

        public static Vector3 CalculateCentroid(List<Vector3> vertices)
        {
            Vector3 centroid = Vector3.zero;
            for (int i = 0; i < vertices.Count; ++i)
            {
                centroid += vertices[i];
            }

            if (vertices.Count > 0)
            {
                centroid /= vertices.Count;
            }

            return centroid;
        }

        public static List<Vector3> CalculateCopy(List<Vector3> val)
        {
            if (val == null)
            {
                return null;
            }

            var copy = new List<Vector3>(val.Count);
            copy.AddRange(val);
            return copy;
        }

        public static void CopyRiverMaterial(Material src, Material target)
        {
            if (src != null && target != null)
            {
                var riverTexture = target.GetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME);
                //copy all materials except the river texture!
                target.CopyPropertiesFromMaterial(src);
                target.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, riverTexture);
            }
        }

        public static GameObject FindGameObject(GameObject obj, string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return null;
            }

            if (obj.name == name)
            {
                return obj;
            }

            int n = obj.transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                var childObj = FindGameObject(obj.transform.GetChild(i).gameObject, name);
                if (childObj != null)
                {
                    return childObj;
                }
            }

            return null;
        }

        public static bool IsChild(GameObject root, GameObject child)
        {
            if (root == child)
            {
                return true;
            }

            int n = root.transform.childCount;
            for (int i = 0; i < n; ++i)
            {
                bool isChild = IsChild(root.transform.GetChild(i).gameObject, child);
                if (isChild)
                {
                    return true;
                }
            }

            return false;
        }

        //判断t是否是属于任意node的子节点,根据名字判断
        public static bool IsDescendant(Transform t, List<Transform> nodes)
        {
            for (int i = 0; i < nodes.Count; ++i)
            {
                var obj = FindGameObject(nodes[i].gameObject, t.name);
                if (obj != null)
                {
                    return true;
                }
            }

            return false;
        }

        public static string GetHierarchyPath(Transform t)
        {
            string fullPath = "";
            while (t != null)
            {
                if (fullPath.Length == 0)
                {
                    fullPath = t.name;
                }
                else
                {
                    fullPath = t.name + "/" + fullPath;
                }

                t = t.parent;
            }

            return fullPath;
        }

        public static bool SameHierarchy(Transform a, Transform b)
        {
            if (a.name != b.name)
            {
                return false;
            }

            if (a.childCount != b.childCount)
            {
                return false;
            }

            int n = a.childCount;
            for (int i = 0; i < n; ++i)
            {
                bool sameHierarchy = SameHierarchy(a.GetChild(i), b.GetChild(i));
                if (!sameHierarchy)
                {
                    return false;
                }
            }

            return true;
        }

        public static int GetPrefabNameLOD(string prefabPath)
        {
            var idx = prefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (idx == -1)
            {
                return -1;
            }

            var lodSubstring = prefabPath.Substring(idx);
            lodSubstring = Utils.GetPathName(lodSubstring, false);
            Regex rx = new Regex(@"_lod\d+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            if (rx.Match(lodSubstring).Length == lodSubstring.Length)
            {
                var path = GetPathName(prefabPath, false);
                idx = path.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
                var lodPos = path.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX, idx);
                var lodStr = path.Substring(lodPos + MapCoreDef.MAP_PREFAB_LOD_PREFIX.Length);
                int lod;
                Utils.ParseInt(lodStr, out lod);
                return lod;
            }

            return -1;
        }

        //如果outline有自相交的情况,删除无效的顶点
        public static List<Vector3> RemoveSelfIntersection(List<Vector3> outline, out List<Vector3> removedVertices)
        {
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            List<Vector3> part1 = new List<Vector3>();
            List<Vector3> part2 = new List<Vector3>();
            List<Vector3> originalOutline = new List<Vector3>();
            originalOutline.AddRange(outline);
            removedVertices = new List<Vector3>();
            while (true)
            {
                bool foundIntersection = false;
                //find intersection edges
                int n = outline.Count;
                for (int i = 0; i < n; ++i)
                {
                    for (int j = 0; j < n; ++j)
                    {
                        if (j != i)
                        {
                            Vector2 s1 = Utils.ToVector2(outline[i]);
                            Vector2 e1 = Utils.ToVector2(outline[(i + 1) % n]);
                            Vector2 s2 = Utils.ToVector2(outline[j]);
                            Vector2 e2 = Utils.ToVector2(outline[(j + 1) % n]);
                            if (s1 != e2 && s2 != e1 && s1 != s2 && e1 != e2)
                            {
                                if (Utils.SegmentSegmentIntersectionTest(s1, e1, s2, e2, out Vector2 p))
                                {
                                    foundIntersection = true;
                                    part1.Clear();
                                    for (int k = 0; k <= i; ++k)
                                    {
                                        part1.Add(outline[k]);
                                    }
                                    part1.Add(Utils.ToVector3(p));
                                    for (int k = j + 1; k < n; ++k)
                                    {
                                        part1.Add(outline[k]);
                                    }

                                    part2.Clear();
                                    part2.Add(Utils.ToVector3(p));
                                    for (int k = i + 1; k <= j; ++k)
                                    {
                                        part2.Add(outline[k]);
                                    }
#if false
                                    float area1 = EditorUtils.CalculatePolygonArea(part1);
                                    float area2 = EditorUtils.CalculatePolygonArea(part2);
                                    if (area1 < area2)
                                    {
                                        outline = part2;
                                    }
                                    else {
                                        outline = part1;
                                    }
#else
                                    if (part1.Count < part2.Count)
                                    {
                                        outline.Clear();
                                        outline.AddRange(part2);
                                    }
                                    else
                                    {
                                        outline.Clear();
                                        outline.AddRange(part1);
                                    }
#endif
                                    break;
                                }
                            }
                        }
                    }
                    if (foundIntersection)
                    {
                        break;
                    }
                }
                if (!foundIntersection)
                {
                    break;
                }
            }

            double time = w.Stop();
            UnityEngine.Debug.Log($"Remove Intersection cost {time} seconds");

            removedVertices = originalOutline.Except(outline).ToList();

            return outline;
        }

        public static string GetModelTemplatePath(int modelTemplateID, Map map)
        {
            var temp = map.FindObject(modelTemplateID) as ModelTemplate;
            if (temp == null)
            {
                return null;
            }

            return temp.GetLODPrefabPath(0);
        }


        //from here https://stackoverflow.com/questions/1165647/how-to-determine-if-a-list-of-polygon-points-are-in-clockwise-order
        public static bool IsPolygonCW(List<Vector3> closedPolygon)
        {
            float sum = 0;
            for (int i = 0; i < closedPolygon.Count; ++i)
            {
                int next = (i + 1) % closedPolygon.Count;
                sum += (closedPolygon[next].x - closedPolygon[i].x) / (closedPolygon[next].z + closedPolygon[i].z);
            }

            return sum < 0;
        }

        public static void ReverseList(List<GameObject> objects)
        {
            int n = objects.Count / 2;
            for (int i = 0; i < n; ++i)
            {
                GameObject temp = objects[i];
                int k = objects.Count - 1 - i;
                objects[i] = objects[k];
                objects[k] = temp;
            }
        }

        public static void ReverseList(List<Vector3> polygon)
        {
            int n = polygon.Count / 2;
            for (int i = 0; i < n; ++i)
            {
                Vector3 temp = polygon[i];
                int k = polygon.Count - 1 - i;
                polygon[i] = polygon[k];
                polygon[k] = temp;
            }
        }

        public static void ReverseList<T>(List<T> list)
        {
            int n = list.Count / 2;
            for (int i = 0; i < n; ++i)
            {
                var temp = list[i];
                int k = list.Count - 1 - i;
                list[i] = list[k];
                list[k] = temp;
            }
        }

        public static List<Vector3> GetReverseList(List<Vector3> polygon)
        {
            List<Vector3> list = new List<Vector3>();
            for (int i = polygon.Count - 1; i >= 0; --i)
            {
                list.Add(polygon[i]);
            }

            return list;
        }

        public static List<T> GetReverseList<T>(List<T> polygon)
        {
            List<T> list = new List<T>();
            for (int i = polygon.Count - 1; i >= 0; --i)
            {
                list.Add(polygon[i]);
            }

            return list;
        }

        public static void ClampToRange(List<Vector3> polygon, float minX, float minZ, float maxX, float maxZ)
        {
            if (polygon == null)
            {
                return;
            }

            int n = polygon.Count;
            for (int i = 0; i < n; ++i)
            {
                polygon[i] = new Vector3(Mathf.Clamp(polygon[i].x, minX, maxX), polygon[i].y,
                    Mathf.Clamp(polygon[i].z, minZ, maxZ));
            }
        }

        public static Color[] Color32ToColorArray(Color32[] input)
        {
            Color[] result = new Color[input.Length];
            for (int i = 0; i < result.Length; ++i)
            {
                result[i] = input[i];
            }

            return result;
        }

        public static Color32[] ColorToColor32Array(Color[] input)
        {
            Color32[] result = new Color32[input.Length];
            for (int i = 0; i < result.Length; ++i)
            {
                result[i] = input[i];
            }

            return result;
        }

        public static bool IsFolderEmpty(string folder)
        {
            var entries = Directory.GetFileSystemEntries(folder);
            if (entries == null || entries.Length == 0)
            {
                return true;
            }

            return false;
        }

        public static Texture2D ResizeTexture(Texture2D texture2D, int targetX, int targetY)
        {
            //添加图片生成大小数值检测处理
            if (targetX <= 0)
                targetX = 256;
            if (targetY <= 0)
                targetY = 256;

            RenderTexture rt = new RenderTexture(targetX, targetY, 24);
            RenderTexture.active = rt;
            Graphics.Blit(texture2D, rt);
            Texture2D result = new Texture2D(targetX, targetY);
            result.ReadPixels(new Rect(0, 0, targetX, targetY), 0, 0);
            result.Apply();
            RenderTexture.active = null;
            UnityEngine.Object.DestroyImmediate(rt);
            return result;
        }

        public static List<List<Vector3>> CreatePolygonsFromObstacles(IEnumerable<IObstacle> obstacles,
            PrefabOutlineType type, float borderLineShrinkRadius)
        {
            List<List<Vector3>> result = new List<List<Vector3>>();

            foreach (var obstacle in obstacles)
            {
                var polygon = obstacle.GetWorldSpaceOutlineVertices(type);
                var polygonList = new List<Vector3>();
                polygonList.AddRange(polygon);

                if (borderLineShrinkRadius != 0)
                {
                    result.AddRange(PolygonAlgorithm.ExpandPolygon(-borderLineShrinkRadius, polygonList));
                }
                else
                {
                    result.Add(polygonList);
                }
            }

            return result;
        }

        public static bool IsTransformIdentity(GameObject gameObject)
        {
            return gameObject.transform.position == Vector3.zero &&
                   gameObject.transform.lossyScale == Vector3.one &&
                   gameObject.transform.rotation == Quaternion.identity;
        }

        //计算满足totalPixelsCount的最小POT贴图大小
        public static Vector2Int CalculateSmallestPOTTextureSize(int totalPixelsCount)
        {
            int s = Mathf.CeilToInt(Mathf.Sqrt(totalPixelsCount));
            if (Mathf.NextPowerOfTwo(s) == s)
            {
                return new Vector2Int(s, s);
            }

            s = Mathf.NextPowerOfTwo(s);
            int textureWidth = s;
            while (s != 0)
            {
                if (textureWidth * s < totalPixelsCount)
                {
                    return new Vector2Int(textureWidth, s * 2);
                }

                s /= 2;
            }

            UnityEngine.Debug.Assert(false, "invalid size");
            return Vector2Int.zero;
        }

        public static List<IObstacle> ToObstacleList<T>(List<T> list) where T : IObstacle
        {
            List<IObstacle> ret = new List<IObstacle>(list.Count);
            for (int i = 0; i < list.Count; ++i)
            {
                ret.Add(list[i]);
            }

            return ret;
        }

        public static Nav.TriangleTypeSetting[] CreateTriangleTypeSettings(int maxTypeCount, ushort[] triangleTypes,
            bool[] triangleStates)
        {
            if (triangleTypes == null || triangleTypes.Length == 0)
            {
                return null;
            }

            Nav.TriangleTypeSetting[] typeSettings = new Nav.TriangleTypeSetting[maxTypeCount + 1];
            HashSet<ushort> uniqueTriangleTypes = new HashSet<ushort>();
            for (int i = 0; i < triangleTypes.Length; ++i)
            {
                if (uniqueTriangleTypes.Contains(triangleTypes[i]) == false)
                {
                    uniqueTriangleTypes.Add(triangleTypes[i]);
                    typeSettings[triangleTypes[i]] = new Nav.TriangleTypeSetting(triangleTypes[i],
                        triangleStates == null ? false : triangleStates[i]);
                }
            }

            return typeSettings;
        }

        public static string FormatColorRGBStr(Color32 color)
        {
            string r = Convert.ToString(color.r, 16);
            string g = Convert.ToString(color.g, 16);
            string b = Convert.ToString(color.b, 16);
            if (r.Length == 1)
            {
                r = "0" + r;
            }

            if (g.Length == 1)
            {
                g = "0" + g;
            }

            if (b.Length == 1)
            {
                b = "0" + b;
            }

            string s = string.Format("{0}{1}{2}", r, g, b);
            return s;
        }

        public static int FindVertex(List<Vector3> vertices, Vector3 v, float esp)
        {
            for (int i = 0; i < vertices.Count; ++i)
            {
                var o = vertices[i];
                if (Approximately(o.x, v.x, esp) &&
                    Approximately(o.y, v.y, esp) &&
                    Approximately(o.z, v.z, esp))
                {
                    return i;
                }
            }

            return -1;
        }

        public static int FindVertexXZ(List<Vector3> vertices, Vector3 v, float esp)
        {
            for (int i = 0; i < vertices.Count; ++i)
            {
                var o = vertices[i];
                if (Approximately(o.x, v.x, esp) &&
                    Approximately(o.z, v.z, esp))
                {
                    return i;
                }
            }

            return -1;
        }

        public static int FindOrAddVertex(List<Vector3> vertices, Vector3 v, float esp, out bool added)
        {
            for (int i = 0; i < vertices.Count; ++i)
            {
                var o = vertices[i];
                if (Approximately(o.x, v.x, esp) &&
                    Approximately(o.y, v.y, esp) &&
                    Approximately(o.z, v.z, esp))
                {
                    added = false;
                    return i;
                }
            }

            added = true;
            vertices.Add(v);
            return vertices.Count - 1;
        }

        public static string[] GetTexturePropertyNames(Material material)
        {
            List<string> validPropertyNames = new List<string>();
            if (material != null)
            {
                var names = material.GetTexturePropertyNames();
                for (int i = 0; i < names.Length; ++i)
                {
                    if (material.HasProperty(names[i]))
                    {
                        validPropertyNames.Add(names[i]);
                    }
                }
            }

            return validPropertyNames.ToArray();
        }

        public static void ClearFolderContent(string dir)
        {
#if UNITY_EDITOR
            if (Directory.Exists(dir))
            {
                var enumerator = Directory.EnumerateFileSystemEntries(dir);
                foreach (var e in enumerator)
                {
                    string validPath = e.Replace('\\', '/');
                    bool suc = FileUtil.DeleteFileOrDirectory(validPath);
                    UnityEngine.Debug.Assert(suc);
                }
            }

            AssetDatabase.Refresh();
#endif
        }

        public static string TryToGetValidConfigFilePath()
        {
#if UNITY_EDITOR
            string[] filePaths = System.IO.Directory.GetFiles("Assets", "mapconfig.bytes", SearchOption.AllDirectories);
            if (filePaths != null && filePaths.Length > 0)
            {
                return filePaths[0].Replace('\\', '/');
            }
#endif
            return "";
        }

        public static bool GetRectIntersection(Rect a, Rect b, out Rect intersection)
        {
            if (a.Overlaps(b))
            {
                float minX = Mathf.Max(a.min.x, b.min.x);
                float minY = Mathf.Max(a.min.y, b.min.y);
                float maxX = Mathf.Min(a.max.x, b.max.x);
                float maxY = Mathf.Min(a.max.y, b.max.y);
                intersection = new Rect(minX, minY, maxX - minX, maxY - minY);
                return true;
            }

            intersection = new Rect();
            return false;
        }

        public static Vector3 CalculateBaryCentricCoord(float x0, float z0, float x1, float z1, float x2, float z2,
            float px, float pz)
        {
            float pax = px - x0;
            float paz = pz - z0;
            float bax = x1 - x0;
            float baz = z1 - z0;
            float cax = x2 - x0;
            float caz = z2 - z0;
            float delta = bax * caz - baz * cax;
            if (delta == 0)
            {
                return Vector3.zero;
            }

            float t1 = (pax * caz - paz * cax) / delta;
            float t2 = (bax * paz - baz * pax) / delta;

            return new Vector3(1 - t1 - t2, t1, t2);
        }

        public static string GetAssetGuid(UnityEngine.Object assetObject)
        {
#if UNITY_EDITOR
            return AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(assetObject));
#else
            return "";
#endif
        }

        public static T GetAssetFromGuid<T>(string guid) where T : UnityEngine.Object
        {
#if UNITY_EDITOR
            var path = AssetDatabase.GUIDToAssetPath(guid);
            return AssetDatabase.LoadAssetAtPath<T>(path);
#else
            return null;
#endif
        }

        public static string ConvertToServerTypeName(PropertyType propType)
        {
            switch (propType)
            {
                case PropertyType.kPropertyBool:
                case PropertyType.kPropertyInt:
                    return "INT";
                case PropertyType.kPropertyIntArray:
                    return "ARR_INT";
                case PropertyType.kPropertyFloat:
                    return "FLT";
                case PropertyType.kPropertyString:
                    return "STR";
                case PropertyType.kPropertyVector2:
                case PropertyType.kPropertyVector3:
                case PropertyType.kPropertyVector4:
                case PropertyType.kPropertyColor:
                    return "MAP";
                default:
                    UnityEngine.Debug.Assert(false);
                    return "";
            }
        }

        //获取屏幕空间矩形对应的xz屏幕的矩形
        public static Rect GetXZPlaneRect(Rect screenRect, Camera camera)
        {
            var plane = new Plane(new Vector3(0, 1, 0), 0);
            var ray0 = camera.ScreenPointToRay(screenRect.min);
            var ray1 = camera.ScreenPointToRay(new Vector2(screenRect.xMin, screenRect.yMax));
            var ray2 = camera.ScreenPointToRay(new Vector2(screenRect.xMax, screenRect.yMax));
            var ray3 = camera.ScreenPointToRay(new Vector2(screenRect.xMax, screenRect.yMin));

            float d0, d1, d2, d3;
            bool suc0 = plane.Raycast(ray0, out d0);
            bool suc1 = plane.Raycast(ray1, out d1);
            bool suc2 = plane.Raycast(ray2, out d2);
            bool suc3 = plane.Raycast(ray3, out d3);

            if (!suc0 || !suc1 || !suc2 || !suc3)
            {
                return Rect.zero;
            }

            Vector3 p0 = ray0.GetPoint(d0);
            Vector3 p1 = ray0.GetPoint(d1);
            Vector3 p2 = ray0.GetPoint(d2);
            Vector3 p3 = ray0.GetPoint(d3);

            float minX = Mathf.Min(p0.x, p1.x, p2.x, p3.x);
            float maxX = Mathf.Max(p0.x, p1.x, p2.x, p3.x);
            float minY = Mathf.Min(p0.y, p1.y, p2.y, p3.y);
            float maxY = Mathf.Max(p0.y, p1.y, p2.y, p3.y);

            return new Rect(minX, minY, maxX - minX, maxY - minY);
        }

        public static bool IsAlphaFormat(TextureFormat format)
        {
#if UNITY_EDITOR
            return
                format == TextureFormat.ARGB4444 ||
                format == TextureFormat.RGBA32 ||
                format == TextureFormat.ARGB32 ||
                format == TextureFormat.RGBA4444 ||
                format == TextureFormat.BGRA32 ||
                format == TextureFormat.RGBAHalf ||
                format == TextureFormat.RGBAFloat ||
                format == TextureFormat.PVRTC_RGBA2 ||
                format == TextureFormat.PVRTC_RGBA4 ||
                format == TextureFormat.ETC2_RGBA1 ||
                format == TextureFormat.ETC2_RGBA8 ||
                format == TextureFormat.ASTC_8x8 ||
                format == TextureFormat.ASTC_10x10 ||
                format == TextureFormat.ASTC_12x12 ||
                format == TextureFormat.ASTC_4x4 ||
                format == TextureFormat.ASTC_5x5 ||
                format == TextureFormat.ASTC_6x6 ||
                format == TextureFormat.ASTC_8x8 ||
                format == TextureFormat.ASTC_10x10 ||
                format == TextureFormat.ASTC_12x12;
#else
            return false;
#endif
        }

        public static Rect AddRect(Rect a, Rect b)
        {
            float x = Mathf.Min(a.x, b.x);
            float y = Mathf.Min(a.y, b.y);
            float maxX = Mathf.Max(a.max.x, b.max.x);
            float maxY = Mathf.Max(a.max.y, b.max.y);
            return new Rect(x, y, maxX - x, maxY - y);
        }

        public static Rect ExpandRect(Rect r, float size)
        {
            float minX = r.xMin - size * 0.5f;
            float maxX = r.xMax + size * 0.5f;
            float minY = r.yMin - size * 0.5f;
            float maxY = r.yMax + size * 0.5f;

            return new Rect(minX, minY, maxX - minX, maxY - minY);
        }

        public static Vector3 RotateY(Vector3 dir, float angle)
        {
            Quaternion q = Quaternion.Euler(0, angle, 0);
            return q * dir;
        }

        public static float Angle(Vector3 dirA, Vector3 dirB)
        {
            float angle = Vector3.Angle(dirA, dirB);
            var p = Vector3.Cross(dirA, dirB);
            if (p.y > 0)
            {
                return angle;
            }

            return -angle;
        }

        public static string IntArrayToStringList(int[] values)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("[");
            for (int i = 0; i < values.Length; ++i)
            {
                builder.Append(values[i].ToString());
                if (i != values.Length - 1)
                {
                    builder.Append(",");
                }
            }

            builder.Append("]");
            return builder.ToString();
        }

        public static int Mod(int x, int y)
        {
            if (x < 0)
            {
                return x + y;
            }

            return x % y;
        }


        //直接一对一转换
        public static short[] ConvertToShortGrids(int[,] grids)
        {
            int rows = grids.GetLength(0);
            int cols = grids.GetLength(1);
            short[] data = new short[rows * cols];
            int idx = 0;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    data[idx] = (short) grids[i, j];
                    ++idx;
                }
            }

            return data;
        }

        public static byte[] ConvertToByteGrids(int[,] grids)
        {
            int rows = grids.GetLength(0);
            int cols = grids.GetLength(1);
            byte[] data = new byte[rows * cols];
            int idx = 0;
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    data[idx] = (byte) grids[i, j];
                    ++idx;
                }
            }

            return data;
        }

        public static Vector3 ClampPoint(Vector3 p, Vector3 min, Vector3 max)
        {
            return new Vector3(Mathf.Clamp(p.x, min.x, max.x),
                Mathf.Clamp(p.y, min.y, max.y), Mathf.Clamp(p.z, min.z, max.z));
        }

        public static bool IsHitRectangle(Vector3 pos, Vector3 center, float radius)
        {
            float minX = center.x - radius;
            float maxX = center.x + radius;
            float minZ = center.z - radius;
            float maxZ = center.z + radius;
            return pos.x >= minX && pos.x <= maxX && pos.z >= minZ && pos.z <= maxZ;
        }

        public static List<T> GetRange<T>(List<T> list, int startIndex, int count)
        {
            UnityEngine.Debug.Assert(startIndex + count <= list.Count);

            List<T> range = new List<T>();
            for (int i = 0; i < count; ++i)
            {
                range.Add(list[i + startIndex]);
            }

            return range;
        }

        public static void Shuffle<T>(List<T> values)
        {
            for (int i = 0; i < values.Count; ++i)
            {
                var swapIdx = UnityEngine.Random.Range(i, values.Count);
                var temp = values[swapIdx];
                values[swapIdx] = values[i];
                values[i] = temp;
            }
        }

        public static void Shuffle<T>(T[] values)
        {
            for (int i = 0; i < values.Length; ++i)
            {
                var swapIdx = UnityEngine.Random.Range(i, values.Length);
                var temp = values[swapIdx];
                values[swapIdx] = values[i];
                values[i] = temp;
            }
        }

        //regionEndIndex: 每个region的triangle索引区间
        //regionIDs:索引区间内triangle的region id
        public static void CreateRegions(int[] triangleIndices, ushort[] triangleTypes, out List<int> regionEndIndex,
            out List<int> regionIDs)
        {
            regionEndIndex = new List<int>();
            regionIDs = new List<int>();
            if (triangleTypes != null && triangleTypes.Length > 0)
            {
                ushort curRegionID = triangleTypes[0];
                regionIDs.Add(curRegionID);
                for (int i = 1; i < triangleTypes.Length; ++i)
                {
                    if (curRegionID != triangleTypes[i])
                    {
                        regionEndIndex.Add(i - 1);
                        curRegionID = triangleTypes[i];
                        regionIDs.Add(curRegionID);
                    }
                }

                regionEndIndex.Add(triangleTypes.Length - 1);
            }
            else
            {
                regionIDs.Add(0);
                regionEndIndex.Add(triangleIndices.Length / 3 - 1);
            }
        }

        static float gScaleFactor = 10000;

        public static int UpScale(float v)
        {
            return (int) (v * gScaleFactor);
        }

        public static double DownScale(long v)
        {
            return v / gScaleFactor;
        }

        public static Color RandomColor()
        {
            return UnityEngine.Random.ColorHSV();
        }

        static int CompareFloatAscending(float a, float b)
        {
            if (a < b)
            {
                return -1;
            }
            else if (a > b)
            {
                return 1;
            }

            return 0;
        }

        public static bool Contains(Vector3 p, List<Vector3> points, float epsilon)
        {
            for (int i = 0; i < points.Count; ++i)
            {
                if (Utils.Approximately(p, points[i], epsilon))
                {
                    return true;
                }
            }

            return false;
        }

        public static float CalculateWorldDistance(float screenDistance)
        {
            float distance = Mathf.Clamp01(screenDistance / Screen.width) * Map.currentMap.viewport.width;
            return distance;
        }

        public static bool IsCurveEmpty(AnimationCurve curve)
        {
            return curve == null || curve.length == 0;
        }

        public static List<Vector3> RemoveDuplicated1(List<Vector3> points, float epsilon)
        {
            List<Vector3> result = new List<Vector3>();
            foreach (var p in points)
            {
                if (Contains(p, result, epsilon) == false)
                {
                    result.Add(p);
                }
            }

            return result;
        }

#if false
		//有bug
        public static void RemoveDuplicated(List<Vector3> points)
        {
            points.Sort((Vector3 a, Vector3 b) =>
            {
                if (!Mathf.Approximately(a.x, b.x))
                {
                    return CompareFloatAscending(a.x, b.x);
                }

                if (!Mathf.Approximately(a.y, b.y))
                {
                    return CompareFloatAscending(a.y, b.y);
                }

                return CompareFloatAscending(a.z, b.z);
            });

            for (int i = points.Count - 1; i > 0;)
            {
                for (int j = i - 1; j >= 0; --j)
                {
                    if (points[j] != points[i])
                    {
                        points.RemoveRange(j + 1, i - j - 1);
                        i = j;
                        break;
                    }
                    else if (j == 0)
                    {
                        points.RemoveRange(1, i - j);
                        i = 0;
                    }
                }
            }
        }
#endif

        public static bool ListEqual(List<Vector3> a, List<Vector3> b, float esp)
        {
            if (a.Count != b.Count)
            {
                return false;
            }

            for (int i = 0; i < a.Count; ++i)
            {
                if (!Utils.Approximately(a[i], b[i], esp))
                {
                    return false;
                }
            }

            return true;
        }
    }

    /// <summary>
    /// 由于协程函数无法接受ref参数，故此类目前专用来协程函数之间的传值
    /// 其他地方不要滥用
    /// </summary>
    public class Ref<T>
    {
        public T value;

        public static implicit operator T(Ref<T> src) => src.value;

        public static implicit operator Ref<T>(T src) => new Ref<T> { value = src };
    }

    public static class Extensions
    {
        /// <summary>
        /// Get the array slice between the two indexes.
        /// ... Inclusive for start index, exclusive for end index.
        /// </summary>
        public static T[] Slice<T>(this T[] source, int start, int end)
        {
            // Handles negative ends.
            if (end < 0)
            {
                end = source.Length;
            }

            int len = end - start;

            // Return new array.
            T[] res = new T[len];
            for (int i = 0; i < len; i++)
            {
                res[i] = source[i + start];
            }

            return res;
        }
    }
}