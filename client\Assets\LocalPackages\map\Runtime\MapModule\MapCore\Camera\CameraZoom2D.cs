﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public class CameraZoom2D : ZoomActionBase2D
    {
        public CameraZoom2D(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
            on = true;

            Init();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mTargetPosition = currentCameraPos;

            int touchCount = MapTouchManager.touchCount;
            if (on && touchCount == 2)
            {
                IMapTouch touch = MapTouchManager.GetTouch(0);
                IMapTouch touch2 = MapTouchManager.GetTouch(1);
                float touchDelta = Vector2.Distance(touch.position, touch2.position);

                if (mTouch0PressTime == 0)
                {
                    mTouch0PressTime = Time.time;
                    mTouch1PressTime = Time.time;
                }

                var dir0 = (touch.position - touch.lastPosition).normalized;
                var dir1 = (touch2.position - touch2.lastPosition).normalized;
                bool isDir0Zero = dir0 == Vector2.zero;
                bool isDir1Zero = dir1 == Vector2.zero;

                if ((touch.state == MapTouchState.Touch && isDir0Zero) ||
                    (touch2.state == MapTouchState.Touch && isDir1Zero))
                {
                    return;
                }

                if ((isDir0Zero || isDir1Zero) && isDir0Zero != isDir1Zero)
                {
                    if (!mIsTwoTouchZooming)
                    {
                        InitZooming(touchDelta, touch, touch2);
                    }
                    OnZoom(touch, touch2);
                }
                else
                {
                    if (!mIsTwoTouchZooming)
                    {
                        InitZooming(touchDelta, touch, touch2);
                    }

                    OnZoom(touch, touch2);
                }
            }
            else if (mIsTwoTouchZooming)
            {
                mIsTwoTouchZooming = false;
                isFinished = true;
            }
        }

        void InitZooming(float touchDelta, IMapTouch touch0, IMapTouch touch1)
        {
            //初始化zoom的一些值
            mIsTwoTouchZooming = true;
            mLastTwoTouchDist = touchDelta;
            mFirstTwoTouchDist = touchDelta;
            var camera = Map.currentMap.camera.firstCamera;
            //mFirstTwoTouchOrthoSize = camera.orthographicSize;
            mFirstTwoTouchOrthoSize = MapCameraMgr.updatedOrthographicSize;
            var center = (touch0.position + touch1.position) * 0.5f;

            mZoomCenterStart = Utils.FromScreenToWorldPosition2D(center, camera, mFirstTwoTouchOrthoSize);

            Reset();
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            return mTargetPosition;
        }

        //相机双指缩放
        void OnZoom(IMapTouch touch0, IMapTouch touch1)
        {
            float touchDelta = Vector2.Distance(touch0.position, touch1.position);
            float f = mLastTwoTouchDist - touchDelta;

            if (((Mathf.Abs(f) > 1f) && ((touch0.state == MapTouchState.Touching) || (touch1.state == MapTouchState.Touching))) && (mLastTwoTouchDist > 0f && mFirstTwoTouchDist > 0))
            {
                float orthoSize = mFirstTwoTouchDist / mLastTwoTouchDist * mFirstTwoTouchOrthoSize;

                Vector2 center = (touch0.position + touch1.position) * 0.5f;
                enabled = true;

                var camera = Map.currentMap.camera.firstCamera;
                //camera.orthographicSize = orthoSize;
                MapCameraMgr.updatedOrthographicSize = orthoSize;
                //var centerWorldPos =  camera.ScreenToWorldPoint(center);
                var centerWorldPos = Utils.FromScreenToWorldPosition2D(center, camera, orthoSize);

                var delta = centerWorldPos - mZoomCenterStart;
                mTargetPosition -= delta;
                
                mLastTwoTouchDist = touchDelta;
            }
        }

        public override void OnCameraSettingChange(MapCameraSetting newSetting)
        {
            Reset();
        }

        public bool on
        {
            set
            {
                mOn = value;
                if (!mOn)
                {
                    isFinished = true;
                }
            }
            get
            {
                return mOn;
            }
        }

        Vector3 mTargetPosition;

        float mFirstTwoTouchDist = 0f;
        float mFirstTwoTouchOrthoSize;
        bool mIsTwoTouchZooming = false;
        float mLastTwoTouchDist = 0f;
        bool mOn = true;

        float mTouch0PressTime;
        float mTouch1PressTime;
        Vector3 mZoomCenterStart;
    }
}
