<!DOCTYPE html>
<!--[if IE 6]>
<html id="ie6" lang="en-US">
<![endif]-->
<!--[if IE 7]>
<html id="ie7" lang="en-US">
<![endif]-->
<!--[if IE 8]>
<html id="ie8" lang="en-US">
<![endif]-->
<!--[if !(IE 6) | !(IE 7) | !(IE 8)  ]><!-->
<html lang="en-US">
<!--<![endif]-->
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width" />
<title>Unity3d Log Viewer | </title>
<link rel="profile" href="../../../gmpg.org/xfn/11/index.htm" />
<link rel="stylesheet" type="text/css" media="all" href="style.css" />
<link rel="pingback" href="../../xmlrpc.php.htm" />
<!--[if lt IE 9]>
<script src="../../wp-content/themes/twentyeleven/js/html5.js" type="text/javascript"></script>
<![endif]-->
<link rel="alternate" type="application/rss+xml" title=" &raquo; Feed" href="../../feed/index.htm" />
<link rel="alternate" type="application/rss+xml" title=" &raquo; Comments Feed" href="../../comments/feed/index.htm" />
<link rel="alternate" type="application/rss+xml" title=" &raquo; Unity3d Log Viewer Comments Feed" href="feed/index.htm" />
<script type='text/javascript' src='../../wp-includes/js/comment-reply.min.js-ver=3.8.4.htm'></script>
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="../../xmlrpc.php-rsd.htm" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="../../wp-includes/wlwmanifest.xml.htm" /> 
<link rel='prev' title='Contact Us' href='../../contact-us/index.htm' />
<link rel='next' title='Products' href='../index.htm' />
<link rel='canonical' href='index.htm' />
<link rel='shortlink' href='../../-p=47.htm' />
</head>

<body class="page page-id-47 page-child parent-pageid-52 page-template-default single-author singular two-column right-sidebar">
<div id="page" class="hfeed"> <!-- #branding -->


	<div id="main">
		<div id="primary">
			<div id="content" role="main">

				
					
<article id="post-47" class="post-47 page type-page status-publish hentry">
	<header class="entry-header">
	  <h1 class="entry-title"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Unity3d Logs Viewer</h1>
	</header>
	<!-- .entry-header -->

	<div class="entry-content">
<p style="text-align: left;">         To use Reporter just create reporter from menu (Reporter-&gt;Create) at first scene your game start . then set the  &#8221; Scrip execution order &#8221; in (Edit -&gt; Project Settings ) of Reporter.cs to be the highest. Congratulations you have finished setup of Log Viewer , no coding is required, now you can view all logs warnings errors and exception which was showing in unity3d editor console directly on game screen even after you release it to mobile , pc or any other platform, all what you have to do is to make a circle gesture using your mouse (click and drag) or your finger (touch and drag) on the screen to show all these logs, you will see something like this logs .</p>
<p style="text-align: left;"><a href="screenShot11.png"><img class="alignnone size-full wp-image-106" alt="screenShot1" src="screenShot11.png" width="1904" height="986" /></a></p>
<p>As you see most of functionality that are available for you in editor console are available to you in your game even if you release the game to any platform.<br />
You can view clear collapse logs, also clear the logs once new scene is loaded , view fps and finally hide the logs to go back to your game, you can use scroller on the right for fast scrolling or scroll directly on log window which give you slow and big scroller so you can scroll normally even on small mobiles.</p>
<p>This asset include script for testing. just drop TestReporter script on any game object, this script will do 1000 logs + 1000 warnings + 1000 errors, you can adjust the Log Test Count to check the functionality of this asset, also note you can adjust it to huge number to check how this asset perform for stress loggings.</p>
<p>Let us talk now about reporter functionality, down you see the image of tool bar</p>
<p><a href="screenShot3.png"><img class="alignnone size-full wp-image-73" alt="screenShot3" src="screenShot3.png" width="1451" height="95" /></a></p>
<p>This bar is scrollable so you can view all button on small screen.</p>
<p><a href="clear.png"><img class="alignnone size-full wp-image-74" alt="clear" src="clear.png" width="98" height="96" /></a>   Button  to clear all logs.</p>
<p><a href="collapse.png"><img class="alignnone size-full wp-image-77" alt="collapse" src="collapse.png" width="99" height="96" /></a>   Toggle button  to collapse logs.</p>
<p><a href="clearOnNewScene.png"><img class="alignnone size-full wp-image-75" alt="clearOnNewScene" src="clearOnNewScene.png" width="97" height="96" /></a>   Toggle button  to clear Logs if new scene is loaded.</p>
<p><a href="time.png"><img class="alignnone size-full wp-image-85" alt="time" src="time.png" width="94" height="96" /></a>   Toggle button to show or hide time beside each logs , time represent time since application start,  the number on the button show the current time.</p>
<p><a href="scene.png"><img class="alignnone size-full wp-image-83" alt="scene" src="scene.png" width="97" height="96" /></a>   Toggle button to show or hide the scene name of each log, so you know which scene this log happened. the name on this button show the current scene name.</p>
<p><a href="GCMem.png"><img class="alignnone size-full wp-image-80" alt="GCMem" src="GCMem.png" width="92" height="96" /></a>   Toggle button to show or hide GC memory of each log, so you know what is GC memory of application when this log happen, it could be useful in monitoring application memory. the number on button show the current GC memory.</p>
<p><a href="fps.png"><img class="alignnone size-full wp-image-79" alt="fps" src="fps.png" width="97" height="96" /></a>   Toggle button to show or hide frame rate of each log, so you can know what is the frame rate when this log happened.</p>
<p><a href="info.png"><img class="alignnone size-full wp-image-81" alt="info" src="info.png" width="92" height="96" /></a></p>
<p>Button to show system information. contains many useful information about system and application. like from which pc this build is done and in which date. this is very useful during game testing to confirm this is up to date build from proper pc.</p>
<p>note also information window are scrollable so you can view all information in small screen mobiles.</p>
<p><a href="screenShot21.png"><img class="alignnone size-full wp-image-107" alt="screenShot2" src="screenShot21.png" width="1904" height="989" /></a></p>
<p>&nbsp;</p>
<p><a href="log.png"><img class="alignnone size-full wp-image-82" alt="log" src="log.png" width="190" height="96" /></a>   Toggle button to show or hide normal logs. number on button show normal logs count.</p>
<p><a href="warning.png"><img class="alignnone size-full wp-image-86" alt="warning" src="warning.png" width="190" height="96" /></a>   Toggle button to show or hide warning logs. number on button show warning logs count.</p>
<p><a href="error.png"><img class="alignnone size-full wp-image-78" alt="error" src="error.png" width="193" height="96" /></a>   Toggle button to show or hide error logs. number on button show error logs count.</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>Thanks for using Reporter and Hope you find this asset useful .</p>
<p>if you have any Issue please visit</p>
<p><a href="https://github.com/aliessmael/Unity-Logs-Viewer">https://github.com/aliessmael/Unity-Logs-Viewer</a></p>
			</div><!-- .entry-content -->
	<footer class="entry-meta">
			</footer><!-- .entry-meta -->
</article><!-- #post-47 -->			  <!-- #comments -->

				
			</div><!-- #content -->
		</div><!-- #primary -->


	</div><!-- #main -->

	<footer id="colophon" role="contentinfo"> </footer><!-- #colophon -->
</div><!-- #page -->


</body>
</html>