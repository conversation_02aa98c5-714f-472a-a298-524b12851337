%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Enzo_Armor_MAT
  m_Shader: {fileID: -6465566751694194690, guid: df5bb027d94a6c44bb32b3c31ec1303f,
    type: 3}
  m_ValidKeywords:
  - TCP2_REFLECTIONS_FRESNEL
  - TCP2_RIM_LIGHTING_LIGHTMASK
  - TCP2_SHADOW_LIGHT_COLOR
  - TCP2_UV_NORMALS_FULL
  - _NORMALMAP
  m_InvalidKeywords:
  - TCP2_DISABLE_WRAPPED_LIGHT
  - TCP2_STYLIZED_FRESNEL
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 268b76deadf86c7488e92399a63b1161, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: fdb7c18559e217547a754481200f2c7b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 268b76deadf86c7488e92399a63b1161, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowBaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DirectIntensityOutline: 1
    - _DstBlend: 0
    - _EmissionChannel: 4
    - _EnableOutline: 1
    - _FresnelMax: 1.5
    - _FresnelMin: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _IndirectIntensity: 1
    - _IndirectIntensityOutline: 0
    - _MatCapMaskChannel: 0
    - _MatCapType: 0
    - _Metallic: 0
    - _Mode: 0
    - _NormalsSource: 0
    - _NormalsUVType: 0
    - _OcclusionChannel: 0
    - _OcclusionStrength: 1
    - _Offset1: 0
    - _Offset2: 0
    - _Outline: 4.23
    - _OutlineLightingType: 0
    - _OutlineLightingTypeURP: 0
    - _OutlineMaxWidth: 1
    - _OutlineMinWidth: 1
    - _OutlinePixelSizeType: 0
    - _OutlineTextureLOD: 5
    - _OutlineTextureType: 0
    - _OutlineWidth: 5
    - _Parallax: 0.02
    - _RampBands: 4
    - _RampBandsSmoothing: 0.1
    - _RampOffset: 0
    - _RampScale: 1
    - _RampSmooth: 0.512
    - _RampSmoothAdd: 0.2
    - _RampSmoothing: 0.1
    - _RampThreshold: 0.8
    - _RampType: 0
    - _ReceiveShadowsOff: 1
    - _ReflectionMapType: 0
    - _ReflectionSmoothness: 0.5
    - _RenderingMode: 0
    - _RimMax: 0.561
    - _RimMin: 0.228
    - _RimStrength: 1.07
    - _ShadowColorLightAtten: 1
    - _SingleIndirectColor: 0
    - _SmoothnessTextureChannel: 0
    - _SpecBlend: 1
    - _SpecSmooth: 1
    - _SpecularHighlights: 1
    - _SpecularMapType: 0
    - _SpecularRoughness: 0.5
    - _SpecularToonSize: 0.25
    - _SpecularToonSmoothness: 0.05
    - _SpecularType: 0
    - _SrcBlend: 1
    - _TCP2_DISABLE_WRAPPED_LIGHT: 1
    - _TCP2_OUTLINE_CONST_SIZE: 0
    - _TCP2_OUTLINE_TEXTURED: 0
    - _TCP2_RAMPTEXT: 0
    - _TCP2_SPEC_TOON: 0
    - _TCP2_STYLIZED_FRESNEL: 1
    - _TCP2_ZSMOOTH_ON: 0
    - _TexLod: 5
    - _UVSec: 0
    - _UseAlphaTest: 0
    - _UseEmission: 0
    - _UseFresnelReflections: 1
    - _UseMatCap: 0
    - _UseMatCapMask: 0
    - _UseMobileMode: 0
    - _UseNormalMap: 1
    - _UseOcclusion: 0
    - _UseOutline: 1
    - _UseReflections: 0
    - _UseRim: 0
    - _UseRimLightMask: 1
    - _UseShadowTexture: 0
    - _UseSpecular: 0
    - _ZSmooth: -0.5
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.8301887, g: 0.8301887, b: 0.8301887, a: 1}
    - _Color: {r: 0.4056604, g: 0.4056604, b: 0.4056604, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _ReflectionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.6}
    - _SColor: {r: 0.25, g: 0.25, b: 0.25, a: 1}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SpecularColor: {r: 0.75, g: 0.75, b: 0.75, a: 1}
  m_BuildTextureStacks: []
