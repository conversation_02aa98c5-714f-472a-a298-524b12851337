﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    public abstract class ZoomActionBase2D : CameraAction
    {
        public ZoomActionBase2D(CameraActionType updateOrder) : base(updateOrder)
        {
        }

        public Vector3 GetCameraPos()
        {
            return mCameraPos;
        }

        public void AutoZoomToOrthoSize(float targetOrthoSize, float duration)
        {
            var camera = Map.currentMap.camera.firstCamera;
            SetCameraTargetOrthoSize(camera, targetOrthoSize, duration);
        }

        public float GetCurrentCameraOrthoSize()
        {
            //var camera = Map.currentMap.camera;
            //return camera.orthographicSize;
            return MapCameraMgr.updatedOrthographicSize;
        }

        public void Init()
        {
            mIsAutoZooming = false;
            mZoomStartTime = 0;
            mZoomEndTime = 0;
            mZoomStartOrthoSize = 0;
            mZoomEndOrthoSize = 0;
            mCameraPos = MapCameraMgr.updatedCameraPosition;
        }

        //zoomIn: deltaDistance < 0
        //zoomOut: deltaDistance > 0
        protected void ZoomAt(float zoomPivotX, float zoomPivotY, float deltaDistance)
        {
            if (!mIsAutoZooming)
            {
                var camera = Map.currentMap.camera.firstCamera;

                float rx = zoomPivotX / camera.pixelWidth;
                float ry = zoomPivotY / camera.pixelHeight;

                var center = camera.pixelRect.center;

                //float cameraOrthoSize = camera.orthographicSize;
                float cameraOrthoSize = MapCameraMgr.updatedOrthographicSize;
                float minSize = MapCameraMgr.minOrthoSize;
                float maxSize = MapCameraMgr.maxOrthoSize;

                float newOrthoSize = cameraOrthoSize + deltaDistance;
                newOrthoSize = Mathf.Clamp(newOrthoSize, minSize, maxSize);
                deltaDistance = newOrthoSize - cameraOrthoSize;

                float newHeight = camera.pixelHeight + deltaDistance;
                float newWidth = camera.pixelWidth + deltaDistance * camera.aspect;

                float newX = center.x - newWidth * 0.5f + rx * newWidth;
                float newY = center.y - newHeight * 0.5f + ry * newHeight;

                float dx = zoomPivotX - newX;
                float dy = zoomPivotY - newY;

                //camera.orthographicSize += (newHeight - camera.pixelHeight) * 0.5f;
                MapCameraMgr.updatedOrthographicSize += (newHeight - camera.pixelHeight) * 0.5f;

                mCameraPos += new Vector3(dx, 0, dy);

                //MapCameraMgr.SetDirty();
            }
        }

        public void Reset()
        {
            mCameraPos = MapCameraMgr.updatedCameraPosition;
            mZoomStartOrthoSize = GetCurrentCameraOrthoSize();
        }

        public void SetCameraTargetOrthoSize(Camera camera, float orthoSize, float interpolateTime)
        {
            float size = GetCurrentCameraOrthoSize();
            if (!mIsAutoZooming)
            {
                mIsAutoZooming = true;
                mZoomStartTime = (int)(Time.realtimeSinceStartup * 1000f);
                mZoomEndTime = mZoomStartTime + ((int)interpolateTime);
                mZoomStartOrthoSize = GetCurrentCameraOrthoSize();
                mZoomEndOrthoSize = orthoSize;
            }
        }

        public bool UpdateZooming()
        {
            if (mIsAutoZooming)
            {
                //var camera = Map.currentMap.camera.firstCamera;
                float currentTime = Time.realtimeSinceStartup * 1000f;
                if (currentTime >= mZoomEndTime)
                {
                    mIsAutoZooming = false;
                    MapCameraMgr.updatedOrthographicSize = mZoomEndOrthoSize;

                    return true;
                }
                else
                {
                    float zoomDeltaOrthoSize = (float)(mZoomEndOrthoSize - mZoomStartOrthoSize);
                    float f = (currentTime - mZoomStartTime) / ((float)(mZoomEndTime - mZoomStartTime));

                    float f1 = 0.7f;
                    float f2 = 1f - f1;
                    if (f < f1)
                    {
                        f /= f1;
                        f *= f;
                        f *= f1;
                    }
                    else
                    {
                        f = (f - f1) / f2;
                        f = (Mathf.Sqrt(f) * f2) + f1;
                    }

                    float size = mZoomStartOrthoSize + zoomDeltaOrthoSize * f;
                    MapCameraMgr.updatedOrthographicSize = size;
                    //Map.currentMap.camera.orthographicSize = size;
                }
            }
            return false;
        }

        bool mIsAutoZooming;
        int mZoomStartTime;
        int mZoomEndTime;
        float mZoomStartOrthoSize;
        float mZoomEndOrthoSize;
        
        Vector3 mCameraPos = Vector3.zero;
    }
}