#if USE_WXSDK
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using UnityEditor;
using WeChatWASM;

namespace CISystem.WeChat
{
    public static class WeChatHelper
    {
        public static void BuildWeChat(string appId, string cdn, string localExportPath, bool developmentBuild = false)
        {
            WXConvertCore.Init();
            var config = WXConvertCore.config;
            if (config != null)
            {
                config.ProjectConf.Appid = appId;
                config.ProjectConf.CDN = cdn;
                config.ProjectConf.DST = localExportPath;
                config.CompileOptions.DevelopBuild = developmentBuild;

                UnityEngine.Debug.Log($"Minigame Config Appid {appId}");
                UnityEngine.Debug.Log($"Minigame Config CDN {cdn}");
                UnityEngine.Debug.Log($"Minigame Config ExportPath {localExportPath}");
                UnityEngine.Debug.Log($"Minigame Config DevelopBuild {developmentBuild}");

                EditorUtility.SetDirty(config);
                AssetDatabase.SaveAssets();

                WXConvertCore.DoExport(true);
            }
            else
            {
                UnityEngine.Debug.LogError($"WXConvertCore.config not found");
            }
        }

        public static void CopyBuildArtifactToSVN(string localExportPath, string svnLocalFullPath, string versionFolderName, string assetsFolderName = "Assets", string streamingAssetsFolderName = "StreamingAssets")
        {
            var svnDir = new DirectoryInfo(svnLocalFullPath);
            var localExportDir = new DirectoryInfo(Path.Combine(localExportPath, "webgl"));
            if (svnDir.Exists && localExportDir.Exists)
            {
                var versionFolderPath = Path.Combine(svnLocalFullPath, versionFolderName);
                var destinationDir = Directory.CreateDirectory(versionFolderPath);

                //copy txt
                foreach (FileInfo file in localExportDir.GetFiles("*.txt"))
                {
                    string targetFilePath = Path.Combine(destinationDir.FullName, file.Name);
                    file.CopyTo(targetFilePath, false);
                }

                //copy Assets folder
                var assetsSourceDir = Path.Combine(localExportDir.FullName, assetsFolderName);
                var assetsDestinationDir = Path.Combine(destinationDir.FullName, assetsFolderName);

                // Get information about the source directory
                var assetsSource = new DirectoryInfo(assetsSourceDir);
                var assetsDest = new DirectoryInfo(assetsDestinationDir);

                // Check if the source directory exists
                if (assetsSource.Exists && assetsDest.Exists)
                {
                    CopyDirectory(assetsSourceDir, assetsDestinationDir);
                }

                //copy StreamingAssets folder
                var streamingAssetsSourceDir = Path.Combine(localExportDir.FullName, streamingAssetsFolderName);
                var streamingAssetsDestinationDir = Path.Combine(destinationDir.FullName, streamingAssetsFolderName);
                CopyDirectory(streamingAssetsSourceDir, streamingAssetsDestinationDir);

                UnityEngine.Debug.Log($"Copy from {localExportPath} to {destinationDir} finished");
            }
            else
            {
                UnityEngine.Debug.LogError($"{svnLocalFullPath} or {localExportPath} not exist");
            }
        }

        public static void PushSvnToRemote(string svnLocalFullPath, string svnAddPath, string commitMsg)
        {
            var svn = "svn.exe";

            var svnUpdate = new ProcessStartInfo
            {
                WorkingDirectory = svnLocalFullPath,
                FileName = svn,
                Arguments = "update"
            };

            Process.Start(svnUpdate).WaitForExit();
            UnityEngine.Debug.Log($"svn update succeed");

            svnUpdate.Arguments = $"add {svnAddPath} --force";
            Process.Start(svnUpdate).WaitForExit();
            UnityEngine.Debug.Log($"svn add succeed");

            svnUpdate.Arguments = $"commit -m \"{commitMsg}\"";
            Process.Start(svnUpdate).WaitForExit();
            UnityEngine.Debug.Log($"svn commit succeed");
        }

        static void CopyDirectory(string sourceDir, string destinationDir, bool recursive = true)
        {
            // Get information about the source directory
            var dir = new DirectoryInfo(sourceDir);

            // Check if the source directory exists
            if (!dir.Exists)
                throw new DirectoryNotFoundException($"Source directory not found: {dir.FullName}");

            // Cache directories before we start copying
            DirectoryInfo[] dirs = dir.GetDirectories();

            // Create the destination directory
            Directory.CreateDirectory(destinationDir);

            // Get the files in the source directory and copy to the destination directory
            foreach (FileInfo file in dir.GetFiles())
            {
                string targetFilePath = Path.Combine(destinationDir, file.Name);
                file.CopyTo(targetFilePath, false);
            }

            // If recursive and copying subdirectories, recursively call this method
            if (recursive)
            {
                foreach (DirectoryInfo subDir in dirs)
                {
                    string newDestinationDir = Path.Combine(destinationDir, subDir.Name);
                    CopyDirectory(subDir.FullName, newDestinationDir, true);
                }
            }
        }

        public static string CompressProjectToZip(string localExportPath, string versionCode, string zipDir)
        {
            var suffix = string.IsNullOrEmpty(versionCode) ? string.Empty : versionCode;
            var archiveFileName = $"minigame-project_v{suffix}.zip";
            ZipFile.CreateFromDirectory(Path.Combine(localExportPath, "minigame"), Path.Combine(zipDir, archiveFileName));
            return archiveFileName;
        }

        public static void GeneratePreviewQRCode(string appid, string projectPath, string privateKeyPath, string qrCodeOutput)
        {
            var installProcInfo = new ProcessStartInfo
            {
                WorkingDirectory = Path.GetFullPath("Packages/com.guoxiaochuan.ci-system/Editor/WeChat/Tools~/miniprogram-ci"),
                FileName = "npm",
                Arguments = $"install"
            };
            
            Process.Start(installProcInfo)?.WaitForExit();
            
            var procInfo = new ProcessStartInfo
            {
                WorkingDirectory = Path.GetFullPath("Packages/com.guoxiaochuan.ci-system/Editor/WeChat/Tools~/miniprogram-ci"),
                FileName = "node",
                Arguments = $"index.js {appid} {projectPath} {privateKeyPath} {qrCodeOutput}"
            };

            Process.Start(procInfo)?.WaitForExit();
            UnityEngine.Debug.Log($"Generate Preview QRCode Finished");
        }

#if DEBUG_TEST

        [MenuItem("微信小游戏CI测试/CopyBuildArtifactToSVN")]
        public static void CopyBuildArtifactToSVNTest()
        {
            CopyBuildArtifactToSVN("D:/work/k1d12", "D:/work/K1D12_CDN/k1d12", "citestver");
        }

        [MenuItem("微信小游戏CI测试/PushSvnToRemote")]
        public static void PushSvnToRemoteTest()
        {
            PushSvnToRemote("D:/work/K1D12_CDN/k1d12", "citestver");
        }

        [MenuItem("微信小游戏CI测试/CompressProjectToZipTest")]
        public static void CompressProjectToZipTest()
        {
            ZipFile.CreateFromDirectory("D:/work/k1d12/minigame", "D:/work/k1d12/minigame-project.zip");
        }

        [MenuItem("微信小游戏CI测试/HttpServerTest")]
        public static void StartHttpServerTest()
        {
            var svnUpdate = new ProcessStartInfo();

            svnUpdate.WorkingDirectory = "D:/work/k1d12";
            svnUpdate.FileName = "http-server";

            Process.Start(svnUpdate);
        }
#endif
    }
}
#endif