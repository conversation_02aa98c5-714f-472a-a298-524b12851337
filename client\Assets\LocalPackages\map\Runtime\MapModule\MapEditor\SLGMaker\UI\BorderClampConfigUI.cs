﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;

namespace TFW.Map
{
    [CustomEditor(typeof(BorderClampConfig))]
    public class BorderClampConfigUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mConfig = target as BorderClampConfig;
            mCurveUI = new CurveConfigUI(mConfig, OnSetDirty, "Height", "Edge Size", "Border Clamp Range Setting");
        }

        void OnDisable()
        {
            mCurveUI.OnDestroy();
        }

        public override void OnInspectorGUI()
        {
            mCurveUI.OnInspectorGUI();
            float bottomMinOffset = EditorGUILayout.FloatField("Viewport Bottom Minimum Offset", mConfig.bottomMinOffset);
            float bottomMaxOffset = EditorGUILayout.FloatField("Viewport Bottom Maximum Offset", mConfig.bottomMaxOffset);
            float topMinOffset = EditorGUILayout.FloatField("Viewport Top Minimum Offset", mConfig.topMinOffset);
            float topMaxOffset = EditorGUILayout.FloatField("Viewport Top Maximum Offset", mConfig.topMaxOffset);
            float viewZOffsetMin = EditorGUILayout.FloatField("Viewport Minimum Z Offset ", mConfig.viewZOffsetMin);
            float viewZOffsetMax = EditorGUILayout.FloatField("Viewport Maximum Z Offset", mConfig.viewZOffsetMax);

            if (!Mathf.Approximately(bottomMinOffset, mConfig.bottomMinOffset))
            {
                mConfig.bottomMinOffset = bottomMinOffset;
                OnSetDirty();
            }

            if (!Mathf.Approximately(bottomMaxOffset, mConfig.bottomMaxOffset))
            {
                mConfig.bottomMaxOffset = bottomMaxOffset;
                OnSetDirty();
            }

            if (!Mathf.Approximately(topMinOffset, mConfig.topMinOffset))
            {
                mConfig.topMinOffset = topMinOffset;
                OnSetDirty();
            }

            if (!Mathf.Approximately(topMaxOffset, mConfig.topMaxOffset))
            {
                mConfig.topMaxOffset = topMaxOffset;
                OnSetDirty();
            }

            if (!Mathf.Approximately(viewZOffsetMin, mConfig.viewZOffsetMin))
            {
                mConfig.viewZOffsetMin = viewZOffsetMin;
                OnSetDirty();
            }

            if (!Mathf.Approximately(viewZOffsetMax, mConfig.viewZOffsetMax))
            {
                mConfig.viewZOffsetMax = viewZOffsetMax;
                OnSetDirty();
            }
        }

        void OnSetDirty()
        {
            if (target != null)
            {
                UnityEditor.EditorUtility.SetDirty(target);
                UnityEditor.EditorUtility.SetDirty(this);
                if (EditorApplication.isPlaying == false)
                {
                    EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                }
            }
        }

        BorderClampConfig mConfig;
        CurveConfigUI mCurveUI;
    }
}

#endif