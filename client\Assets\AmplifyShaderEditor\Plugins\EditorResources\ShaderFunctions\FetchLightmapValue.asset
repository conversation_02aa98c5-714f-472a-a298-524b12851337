%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: FetchLightmapValue
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=16205\n234;92;1295;716;-169.6277;375.1745;1.3;True;False\nNode;AmplifyShaderEditor.SwizzleNode;9;-188.546,133.5814;Float;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;10;368,-112;Float;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector4Node;8;-500.423,19.56814;Float;False;Global;unity_LightmapST;unity_LightmapST;2;0;Fetch;True;0;0;False;0;0,0,0,0;1,1,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;5;-28.547,-122.4186;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;4;-188.546,21.5814;Float;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StaticSwitch;13;981.1006,204.6251;Float;False;Property;_Keyword0;Keyword
    0;1;0;Fetch;True;0;0;False;0;0;0;0;False;UNITY_LIGHTMAP_FULL_HDR;Toggle;2;Key0;Key1;9;1;FLOAT4;0,0,0,0;False;0;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;5;FLOAT4;0,0,0,0;False;6;FLOAT4;0,0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;3;224,-32;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;7;654.0723,-337.4277;Float;True;Property;_TextureSample0;Texture
    Sample 0;0;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;2;-528,-128;Float;False;1;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.StaticSwitch;15;602.9202,151.7659;Float;False;Property;_Keyword2;Keyword
    2;2;0;Fetch;True;0;0;False;0;0;0;0;False;UNITY_LIGHTMAP_RGBM_ENCODING;Toggle;2;Key0;Key1;9;1;FLOAT4;0,0,0,0;False;0;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;5;FLOAT4;0,0,0,0;False;6;FLOAT4;0,0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.Vector4Node;17;329.9202,94.76587;Float;False;Constant;_Vector2;Vector
    2;3;0;Create;True;0;0;False;0;2,2.2,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;1;314.5027,-433.4464;Float;True;Global;unity_Lightmap;unity_Lightmap;0;0;Create;True;0;0;False;0;None;None;False;white;Auto;Texture2D;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.Vector4Node;18;322.9202,307.7659;Float;False;Constant;_Vector3;Vector
    3;3;0;Create;True;0;0;False;0;34.49324,2.2,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DecodeLightmapHlpNode;6;1313.549,-194.924;Float;False;2;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector4Node;16;754.9202,321.7659;Float;False;Constant;_Vector1;Vector
    1;3;0;Create;True;0;0;False;0;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;11;735.7104,-21.51056;Float;False;return
    SAMPLE_TEXTURE2D( unity_Lightmap, samplerunity_Lightmap, UV )@;4;False;1;True;UV;FLOAT2;0,0;In;;Float;SampleLightmapHD;True;False;0;1;0;FLOAT2;0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;12;1027.71,-224.5106;Float;False;3;0;COLOR;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1545.852,-190.3676;Float;False;True;Output;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;9;0;8;0\nWireConnection;10;0;3;0\nWireConnection;5;0;2;0\nWireConnection;5;1;4;0\nWireConnection;4;0;8;0\nWireConnection;13;1;15;0\nWireConnection;13;0;16;0\nWireConnection;3;0;5;0\nWireConnection;3;1;9;0\nWireConnection;7;0;1;0\nWireConnection;7;1;10;0\nWireConnection;15;1;17;0\nWireConnection;15;0;18;0\nWireConnection;6;0;12;0\nWireConnection;6;1;13;0\nWireConnection;11;0;10;0\nWireConnection;12;0;7;0\nWireConnection;12;1;11;0\nWireConnection;12;2;11;0\nWireConnection;0;0;6;0\nASEEND*/\n//CHKSM=F6FA52D482B850462FBD099367992FDE64BDD469"
  m_functionName: 
  m_description: 'Fetches the value from the lightmap. Multiply the fetched value
    by the final fragment color. '
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_customNodeCategory: 
  m_previewPosition: 0
