﻿
/**
 *  XXXBean，代表返回参数规范类名
 */
namespace maxsdk
{

    /**
	 * 通用返回参数，封装对象 MaxSDKBaseBean
	 */
    public class MaxSDKBaseBean
    {
        public string extData;  //扩展字段，json格式
    }


    // 失败信息
    public class MaxSDKFailBean : MaxSDKBaseBean
    {
        /*
		 * 错误码
		 */
        public string code;

        /*
		 * 错误提示
		 */
        public string msg;
    }


    //init初始化接口，返回bean
    public class MaxSDKInitBean : MaxSDKBaseBean
    {
        //todo 待补充
    }


    // 用户信息，登录回调中使用
    public class MaxSDKLoginBean : MaxSDKBaseBean
    {
      
        public string token;    //用户的token
        public string puid;     //渠道侧的用户ID
        public string uid;      //SDK侧的用户ID
        public string uname;    //SDK侧用户帐号
    }


    // 支付信息，支付回调中使用
    public class MaxSDKPayBean : MaxSDKBaseBean
    {
        /*
		 *  平台订单号
		 */
        public string platformOrderID;

    }

    //退出游戏，成功bean
    public class MaxSDKExitBean : MaxSDKBaseBean
    {
        //TODO 暂无
    }

    //用户行为扩展接口
    public class MaxSDKActionBean : MaxSDKBaseBean
    {
        public int type;         //行为类型
    }

    public class MaxSDKActionFailBean : MaxSDKFailBean
    {
        public int type;         //行为类型
    }


    //分享成功 bean
    public class MaxSDKShareBean : MaxSDKBaseBean
    {
        //TODO 暂无
    }

    //扩展方法接口调用成功 bean
    public class MaxSDKDispatchBean : MaxSDKBaseBean
    {
        public string apiName = "";

        public int statusCode;

        public string returnData = "";
    }


    public class MaxSDKAppConfigBean
    {
        /** 主游戏 id */
        public string gameId = "";

        /** 联运商 id */
        public string pId = "";

        /** 子游戏 id */
        public string cGameId = "";

        /** 渠道号 */
        public string refer = "";
    }


}
