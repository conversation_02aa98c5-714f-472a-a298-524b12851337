﻿ 



 
 



using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    //用键盘来控制相机
    public class CameraKeyboardMovement : CameraAction
    {
        public CameraKeyboardMovement(CameraActionType type) : base(type)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            MapCameraMgr.UpdateRotateCenter();
            return mCurrentCameraPos;
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            bool pressed = false;
            Vector3 dir = Vector3.zero;
            if (Input.GetKeyDown(KeyCode.A))
            {
                pressed = true;
                dir.x = -1;
            }
            if (Input.GetKeyDown(KeyCode.D))
            {
                pressed = true;
                dir.x = 1;
            }
            if (Input.GetKeyDown(KeyCode.W))
            {
                pressed = true;
                dir.z = 1;
            }
            if (Input.GetKeyDown(KeyCode.S))
            {
                pressed = true;
                dir.z = -1;
            }

            if (Input.GetKeyDown(KeyCode.X))
            {
                pressed = true;
                dir.z = -1;
                dir.x = -1;
            }

            if (pressed)
            {
                enabled = true;
                mCurrentCameraPos = currentCameraPos + dir.normalized * 100.0f;
            }
            else
            {
                enabled = false;
                mCurrentCameraPos = currentCameraPos;
            }
        }

        Vector3 mCurrentCameraPos;
    }
}
