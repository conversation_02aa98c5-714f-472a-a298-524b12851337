﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class ActionRotateCameraCollider : EditorAction
    {
        public ActionRotateCameraCollider(int layerID, int dataID, float degree)
        {
            mDataID = dataID;
            mLayerID = layerID;
            mDegree = degree;
        }

        public override bool Do()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RotateObject(mDataID, mDegree);
            return true;
        }

        public override bool Undo()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as CameraColliderLayer;
            if (layer == null)
            {
                return false;
            }
            layer.RotateObject(mDataID, -mDegree);
            return true;
        }

        float mDegree;
        int mDataID;
        int mLayerID;
    }
}

#endif