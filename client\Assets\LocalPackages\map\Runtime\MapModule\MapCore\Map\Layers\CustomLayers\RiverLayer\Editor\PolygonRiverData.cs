﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/11/25

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [Black]
    public class PolygonRiverData : PolygonObjectData
    {
        public PolygonRiverData(int id, Map map, List<Vector3> outline, float displayRadius, int textureSize, string riverMaterialPath, List<PolygonRiverSectionData> sections, List<PolygonRiverSplitterData> splitters, int hideLOD, float height, bool generateRiverMaterial) : base(id, map, new OutlineData[2] { new OutlineData(outline), null }, displayRadius, true, false)
        {
            mHideLOD = hideLOD;
            mTextureSize = textureSize;
            mSections = sections;
            mMaterialPath = riverMaterialPath;
            mHeight = height;
            mGenerateRiverMaterial = generateRiverMaterial;
            if (splitters == null)
            {
                splitters = new List<PolygonRiverSplitterData>();
            }
            mSplitters = splitters;

            if (sections == null || sections.Count == 0)
            {
                CreateSections();
            }
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            ClearSections();
        }

        void RemoveSection(PolygonRiverSectionData section)
        {
            mSections.Remove(section);
            section.OnDestroy();
        }

        void ClearSections()
        {
            for (int i = mSections.Count - 1; i >= 0; --i)
            {
                mSections[i].OnDestroy();
            }
            mSections.Clear();
            mTextureSize = 0;
        }

        void CreateSections()
        {
            mSections = new List<PolygonRiverSectionData>();
            var section0 = new PolygonRiverSectionData(map.nextCustomObjectID, mOutlines[0].outline, null, 0);
            mSections.Add(section0);

            for (int i = 0; i < mSplitters.Count; ++i)
            {
                //找到split是要分割哪个section
                var section = FindSection(mSplitters[i]);
                //分割这个section为2个新的section
                SplitSection(section, mSplitters[i]);
            }
        }

        PolygonRiverSectionData FindSection(PolygonRiverSplitterData splitter)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                if (mSections[i].GetSplitterIndex(splitter.startVertexPosition, splitter.endVertexPosition) >= 0)
                {
                    return mSections[i];
                }
            }
            return null;
        }

        void SplitSection(PolygonRiverSectionData section, PolygonRiverSplitterData splitter)
        {
            var startIndex = section.GetVertexIndex(splitter.startVertexPosition);
            var endIndex = section.GetVertexIndex(splitter.endVertexPosition);
            Debug.Assert(startIndex >= 0 && endIndex >= 0);

            int vertexCount = section.GetVertexCount();
            int section1EndIndex = endIndex;
            var minIdx = Mathf.Min(startIndex, endIndex);
            var maxIdx = Mathf.Max(startIndex, endIndex);
            //section 1
            List<Vector3> newSection1Outline = new List<Vector3>();
            for (int i = minIdx; i <= maxIdx; ++i)
            {
                newSection1Outline.Add(section.GetVertex(i));
            }
            //section 2
            List<Vector3> newSection2Outline = new List<Vector3>();
            for (int i = minIdx + vertexCount; i >= maxIdx; --i)
            {
                var idx = i % vertexCount;
                newSection2Outline.Add(section.GetVertex(idx));
            }
            var newSection1 = new PolygonRiverSectionData(map.nextCustomObjectID, newSection1Outline, null, mTextureSize);
            var newSection2 = new PolygonRiverSectionData(map.nextCustomObjectID, newSection2Outline, null, mTextureSize);
            //delete old section
            RemoveSection(section);
            //add new sections
            mSections.Add(newSection1);
            mSections.Add(newSection2);
        }

        //移动河流
        public override void Move(PrefabOutlineType type, Vector3 offset)
        {
            if (offset != Vector3.zero)
            {
                base.Move(type, offset);
                for (int i = 0; i < mSplitters.Count; ++i)
                {
                    mSplitters[i].Move(offset);
                }
                for (int i = 0; i < mSections.Count; ++i)
                {
                    mSections[i].Move(offset);
                }
            }
        }

        public void SetTextureSize(int size, List<PolygonRiverSectionData> sections)
        {
            if (sections == null)
            {
                ClearSections();
                mTextureSize = size;
                CreateSections();
                for (int i = 0; i < mSections.Count; ++i)
                {
                    mSections[i].SetTextureData(null, size);
                }
            }
            else
            {
                mTextureSize = size;
                mSections = sections;
            }
        }

        public int GetSectionIndexByID(int sectionID)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                if (mSections[i].id == sectionID)
                {
                    return i;
                }
            }
            return -1;
        }

        public PolygonRiverSectionData GetSection(int idx)
        {
            if (idx >= 0 && idx < mSections.Count)
            {
                return mSections[idx];
            }
            return null;
        }

        public int GetSplitterCount()
        {
            return mSplitters.Count;
        }

        public PolygonRiverSplitterData GetSplitter(int idx)
        {
            if (idx >= 0 && idx < mSplitters.Count)
            {
                return mSplitters[idx];
            }
            return null;
        }

        public void DeleteSplitter(int idx)
        {
            mSplitters.RemoveAt(idx);
        }

        public bool ShowSplitter(bool show)
        {
            if (mShowSplitter != show)
            {
                mShowSplitter = show;
                return true;
            }
            return false;
        }

        public bool IsSplitterVisible()
        {
            return mShowSplitter;
        }

        public void AddSplitter(PolygonRiverSplitterData splitter)
        {
            mSplitters.Add(splitter);
        }

        public void InsertSplitter(int idx, PolygonRiverSplitterData splitter)
        {
            mSplitters.Insert(idx, splitter);
        }

        public bool IsIntersectedWithSplitter(Vector3 startPos, Vector3 endPos)
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                if (startPos == mSplitters[i].startVertexPosition || startPos == mSplitters[i].endVertexPosition ||
                    endPos == mSplitters[i].startVertexPosition || endPos == mSplitters[i].endVertexPosition)
                {
                    continue;
                }
                if (PolygonUtils.SegmentSegmentIntersection(startPos, endPos, mSplitters[i].startVertexPosition, mSplitters[i].endVertexPosition))
                {
                    return true;
                }
            }
            return false;
        }

        public bool ContainsSplitter(Vector3 startPos, Vector3 endPos)
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                //对称的算一条splitter
                if ((mSplitters[i].startVertexPosition == startPos && mSplitters[i].endVertexPosition == endPos) ||
                    (mSplitters[i].startVertexPosition == endPos && mSplitters[i].endVertexPosition == startPos))
                {
                    return true;
                }
            }
            return false;
        }

        public override void SetVertexPosition(PrefabOutlineType type, int idx, Vector3 pos)
        {
            var oldPos = base.GetVertexPos(type, idx);
            base.SetVertexPosition(type, idx, pos);
            //move section vertex
            for (int i = 0; i < sections.Count; ++i)
            {
                var si = mSections[i].GetVertexIndex(oldPos);
                if (si >= 0)
                {
                    mSections[i].SetVertex(si, pos);
                }
            }
            //move splitter vertex
            for (int i = 0; i < splitters.Count; ++i)
            {
                if (mSplitters[i].startVertexPosition == oldPos)
                {
                    mSplitters[i].startVertexPosition = pos;
                }
                if (mSplitters[i].endVertexPosition == oldPos)
                {
                    mSplitters[i].endVertexPosition = pos;
                }
            }
        }

        public List<PolygonRiverSplitterData> GetUsedSplitters(Vector3 pos)
        {
            List<PolygonRiverSplitterData> splitters = null;
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                if (mSplitters[i].startVertexPosition == pos || mSplitters[i].endVertexPosition == pos)
                {
                    if (splitters == null)
                    {
                        splitters = new List<PolygonRiverSplitterData>();
                    }
                    splitters.Add(mSplitters[i]);
                }
            }
            return splitters;
        }

        public int GetSplitterIndex(PolygonRiverSplitterData splitter)
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                if (mSplitters[i] == splitter)
                {
                    return i;
                }
            }
            return -1;
        }

        public int GetSplitterIndex(Vector3 startPos, Vector3 endPos)
        {
            for (int i = 0; i < mSplitters.Count; ++i)
            {
                if (mSplitters[i].startVertexPosition == startPos && mSplitters[i].endVertexPosition == endPos)
                {
                    return i;
                }
            }
            return -1;
        }

        public string GetTextureName(int riverID, int sectionID)
        {
            string textureName = $"{MapCoreDef.RIVER_MASK_PREFIX}{riverID}_section_{sectionID}.tga";
            return textureName;
        }

        public void FlushMaskTexture(string textureFolder, bool refresh, bool forceFlush, List<string> usedTextureNames)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                string textureName = GetTextureName(id, mSections[i].id);
                string texturePath = $"{textureFolder}/{textureName}";

                if (mSections[i].textureDataDirty || !File.Exists(texturePath) || forceFlush)
                {
                    mSections[i].textureDataDirty = false;

                    var bytes = mSections[i].texture.EncodeToTGA();
                    File.WriteAllBytes(texturePath, bytes);
                }

                if (usedTextureNames != null && !usedTextureNames.Contains(textureName))
                {
                    usedTextureNames.Add(textureName);
                }
            }

            if (refresh)
            {
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }
        }

        public void RefreshMaskTexture(string textureFolder)
        {
            for (int i = 0; i < mSections.Count; ++i)
            {
                var section = mSections[i];
                string textureName = GetTextureName(id, section.id);
                string texturePath = $"{textureFolder}/{textureName}";

                if (File.Exists(texturePath))
                {
                    texturePath = Utils.ConvertToUnityAssetsPath(texturePath);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                    if (texture != null && 
                        texture.width == section.texture.width &&
                        texture.height == section.texture.height)
                    {
                        section.SetTextureData(texture.GetPixels(), texture.width);
                    }
                }
                
            }
        }

        public void ChangeMaterial(Material newMtl)
        {
            mMaterialPath = AssetDatabase.GetAssetPath(newMtl);
        }

        public override Vector3 GetPosition() { return new Vector3(0, mHeight, 0); }

        public List<PolygonRiverSectionData> sections { get { return mSections; } }
        public List<PolygonRiverSplitterData> splitters { get { return mSplitters; } }
        public int textureSize { get { return mTextureSize; } }
        public bool generateRiverMaterial { get { return mGenerateRiverMaterial; } set { mGenerateRiverMaterial = value; } }
        public string materialPath { set { mMaterialPath = value; } get { return mMaterialPath; } }
        public int hideLOD { get { return mHideLOD; } set { mHideLOD = value; } }
        public float height { get { return mHeight; } set { mHeight = value; } }

        //河流的各个分段数据
        List<PolygonRiverSectionData> mSections;
        List<PolygonRiverSplitterData> mSplitters;
        int mTextureSize;
        string mMaterialPath;
        bool mGenerateRiverMaterial;
        bool mShowSplitter = true;
        //hide when lod is this
        int mHideLOD = -1;
        float mHeight = 0;
    }
}
#endif