﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class GridRegionEditor
    {
        public void Save(string dataPath)
        {
            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.GridRegionEditorDataVersion);

            SaveGridRegionEditor(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveGridRegionEditor(BinaryWriter writer)
        {
            writer.Write(gridWidth);
            writer.Write(gridHeight);
            writer.Write(horizontalGridCount);
            writer.Write(verticalGridCount);
            writer.Write(active);
            for (int i = 0; i < verticalGridCount; ++i)
            {
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    writer.Write(grids[i, j]);
                }
            }
            writer.Write(templates.Count);
            for (int i = 0; i < templates.Count; ++i)
            {
                Utils.WriteString(writer, templates[i].name);
                Utils.WriteColor(writer, templates[i].color);
                writer.Write(templates[i].type);
            }
        }
    }
}

#endif