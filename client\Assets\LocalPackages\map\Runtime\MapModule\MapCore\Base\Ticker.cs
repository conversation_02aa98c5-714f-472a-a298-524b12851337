﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class Ticker
    {
        public void Start(float duration)
        {
            mTimer = 0;
            mDuration = duration;
        }

        public void SetDuration(float duration)
        {
            mTimer = 0;
            mDuration = duration;
        }

        public void Restart()
        {
            mTimer = 0;
        }

        public bool Update(float dt)
        {
            bool finished = false;
            mTimer += dt;
            if (mTimer >= mDuration)
            {
                mTimer = mDuration;
                finished = true;
            }
            return finished;
        }

        public float percentage { 
            get {
                if (mDuration == 0)
                {
                    return 0;
                }
                return mTimer / mDuration; 
            }
        }

        public float leftTime { get { return Mathf.Max(0, mDuration - mTimer); } }
        public float duration { get { return mDuration; } }

        float mTimer;
        float mDuration;
    }
}
