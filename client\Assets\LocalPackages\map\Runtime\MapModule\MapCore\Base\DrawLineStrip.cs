﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public class DrawLineStrip : MonoBehaviour
    {
        void OnDrawGizmos()
        {
#if UNITY_EDITOR
            if (lines != null)
            {
                Color oldColor = Handles.color;
                Handles.color = color;
                Handles.DrawLines(lines);

                if (drawVertex)
                {
                    Handles.color = Color.red;
                    Handles.SphereHandleCap(0, lines[0], Quaternion.identity, radius, EventType.Repaint);

                    Handles.color = Color.green;
                    Handles.SphereHandleCap(0, lines[lines.Length - 1], Quaternion.identity, radius, EventType.Repaint);

                    for (int i = 1; i < lines.Length - 1; ++i)
                    {
                        Handles.color = Color.white;
                        Handles.SphereHandleCap(0, lines[i], Quaternion.identity, radius, EventType.Repaint);
                    }
                }

                Handles.color = oldColor;
            }
#endif
        }

        public void SetVertices(List<Vector2> vertices)
        {
            int segment = vertices.Count - 1;
            int vertexCount = segment * 2;
            lines = new Vector3[vertexCount];
            for (int i = 0; i < segment; ++i)
            {
                lines[i * 2] = new Vector3(vertices[i].x, 0, vertices[i].y);
                lines[i * 2 + 1] = new Vector3(vertices[i + 1].x, 0, vertices[i + 1].y);
            }
        }

        public void SetVertices(List<Vector3> vertices)
        {
            int segment = vertices.Count - 1;
            int vertexCount = segment * 2;
            lines = new Vector3[vertexCount];
            for (int i = 0; i < segment; ++i)
            {
                lines[i * 2] = vertices[i];
                lines[i * 2 + 1] = vertices[i + 1];
            }
        }

        public float radius = 1.0f;
        public bool drawVertex = true;
        public Color color = Color.white;
        Vector3[] lines;
    }
}
