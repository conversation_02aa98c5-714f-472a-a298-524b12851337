﻿ 



 
 


/*
 * created by wzw at 2019.5.31
 */

using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    //管理一个大块地表物体的数据层,将Tile中所有第一层子节点打散管理,分帧加载物体
    public sealed class TileGridObjectLayerData : MapLayerData
    {
        public TileGridObjectLayerData(MapLayerDataHeader header, MapLayerLODConfig config, Map map, KeepScaleConfig[] scaleConfig, bool asyncLoading)
            : base(header, config, map)
        {
            mLastViewport = new Rect(-10000 + map.origin.x, -10000 + map.origin.z, 0, 0);
            mScaleUpdater = new KeepScaleUpdater(scaleConfig);
            
            LoadTiles(asyncLoading);
        }

        #region hide
        //清理地图数据
        public override void OnDestroy()
        {
            var tmpTileObjList = mTileObjectDataListPool.Require();

            {
                tmpTileObjList.Clear();

                tmpTileObjList.AddRange(mObjects.Values);
                mObjects.Clear();
                mObjects = null;

                foreach (var tileData in tmpTileObjList)
                {
                    Map.currentMap.DestroyObject(tileData.GetEntityID());
                }
            }

            {
                tmpTileObjList.Clear();

                foreach (var tileChildren in mTileChildren.Values)
                {
                    tmpTileObjList.AddRange(tileChildren);
                    mTileObjectDataListPool.Release(tileChildren);
                }
                mTileChildren.Clear();
                mTileChildren = null;

                foreach (var tileData in tmpTileObjList)
                {
                    tileData.OnDestroy();
                    mPool.Release(tileData);
                }
            }

            mTileObjectDataListPool.Release(tmpTileObjList);
            mBigTileDatas = null;
            mPool.OnDestroy();
            mPool = null;
        }

        //get一个地图对象的数据
        public TileObjectData GetTileObjectData(int objectID)
        {
            TileObjectData val;
            mObjects.TryGetValue(objectID, out val);
            return val;
        }

        //返回big tile的数据
        public OptimizedTileData GetBigTileData(int x, int y)
        {
            if (x >= 0 && x < mCols &&
                y >= 0 && y < mRows)
            {
                int idx = y * mCols + x;
                return mBigTileDatas[idx];
            }
            return null;
        }

        public OptimizedTileData GetBigTileData(int tileIndex)
        {
            if (tileIndex >= 0 && tileIndex < mBigTileDatas.Length)
            {
                return mBigTileDatas[tileIndex];
            }
            return null;
        }

        void AddObjectDataInternal(OptimizedTileData data, int x, int y)
        {
            if (x >= 0 && x < mCols &&
                y >= 0 && y < mRows)
            {
                int idx = y * mCols + x;
                mBigTileDatas[idx] = data;
            }
        }

        void RemoveObjectDataInternal(OptimizedTileData data, int x, int y)
        {
            if (x >= 0 && x < mCols &&
                y >= 0 && y < mRows)
            {
                int idx = y * mCols + x;
                mBigTileDatas[idx] = null;
            }
        }
        #endregion

        public void SetObjectActiveOnly(TileObjectData data, bool active, int lod)
        {
            bool change = SetObjectActiveFromAction(data, active, lod);

            if (change)
            {
                if (active)
                {
                    mVisibleTileChildren[data.GetEntityID()] = data as TileObjectData;
                }
                else
                {
                    mVisibleTileChildren.Remove(data.GetEntityID());
                }
            }
        }

        public void SetObjectScaleChangeCallback(System.Action<TileObjectData> onObjectScaleChangeCallback)
        {
            mOnObjectScaleChangeCallback = onObjectScaleChangeCallback;
        }

        public void SetObjectActiveStateChangeCallback(System.Action<TileObjectData, int> onObjectActiveStateChangeCallback)
        {
            mOnActiveStateChangeCallback = onObjectActiveStateChangeCallback;
        }

        public bool SetObjectActiveFromAction(TileObjectData objectData, bool active, int lod)
        {
            bool changed = objectData.SetObjActive(active);
            if (changed)
            {
                mOnActiveStateChangeCallback(objectData, lod);
            }
            return changed;
        }

        //设置big tile的可见性
        public void SetObjectActiveTop(FrameAction parentAction, OptimizedTileData data, bool active, bool changeLODInCurrentFrame, float updateZoom, int x, int y)
        {
            //每个tile有个最大的update zoom,超过了就忽略!
            //float dissapearZoom = 10;
            //if (updateZoom > dissapearZoom)
            //{
            //    return;
            //}

            int lastLOD = mCurrentLOD;
            if (mIsLODChanged)
            {
                lastLOD = mLastLOD;
            }
            if (active)
            {
                var addAction = FrameActionSetFrontTileVisibility.Require(this, lastLOD, mCurrentLOD, data, true, FrameActionType.ShowFrontTile, false, changeLODInCurrentFrame, x, y);
                parentAction.AddChildAction(addAction);
                #region hide
                //将立即执行的action独立成一个单独的action
                if (mIsLODChanged)
                {
                    if (changeLODInCurrentFrame)
                    {
                        var addNowAction = FrameActionSetFrontTileVisibility.Require(this, lastLOD, mCurrentLOD, data, true, FrameActionType.ShowFrontTile, true, true, x, y);
                        while (true)
                        {
                            bool finished = addNowAction.Do();
                            if (finished)
                            {
                                addNowAction.OnDestroy();
                                break;
                            }
                        }
                    }
                }
                #endregion
            }
            else
            {
                var action = FrameActionSetFrontTileVisibility.Require(this, lastLOD, mCurrentLOD, data, false, FrameActionType.HideFrontTile, false, changeLODInCurrentFrame, x, y);
                parentAction.AddChildAction(action);
                #region hide
                //将立即执行的action独立成一个单独的action
                if (mIsLODChanged)
                {
                    if (changeLODInCurrentFrame)
                    {
                        var removeNowAction = FrameActionSetFrontTileVisibility.Require(this, lastLOD, mCurrentLOD, data, false, FrameActionType.HideFrontTile, true, true, x, y);
                        while (true)
                        {
                            bool finished = removeNowAction.Do();
                            if (finished)
                            {
                                removeNowAction.OnDestroy();
                                break;
                            }
                        }
                    }
                }
                #endregion
            }
        }

        //给frame action使用
        //instant: 是否立即在当前帧切换lod
        public void SetObjectActiveFromAction(FrameAction parentAction, int oldLOD, int newLOD, bool active, bool instant, bool hasInstantAction, int x, int y)
        {
            int idx = y * mCols + x;

            if (active)
            {
                AddChildren(parentAction, oldLOD, newLOD, instant, hasInstantAction, x, y);
            }
            else
            {
                RemoveChildren(parentAction, oldLOD, newLOD, instant, hasInstantAction, x, y);
            }
        }

        #region hide
        int GetCurrentLOD()
        {
            if (mCurrentLOD < 0)
            {
                return 0;
            }
            return mCurrentLOD;
        }

        /*
         *  2 3
         *  0 1
         */
        int GetSubRegionIndex(float x, float z)
        {
            float centerX = tileWidth * 0.5f;
            float centerZ = tileHeight * 0.5f;
            if (x < centerX && z < centerZ)
            {
                return 0;
            }
            if (x >= centerX && z < centerZ)
            {
                return 1;
            }
            if (x < centerX && z >= centerZ)
            {
                return 2;
            }
            if (x >= centerX && z >= centerZ)
            {
                return 3;
            }
#if UNITY_EDITOR
            Debug.Log("invalid subregion");
#endif
            return 0;
        }

        //返回lodlevel下,第tileIndex个tile下的第index个物体的id
        public int GetObjectID(int lodLevel, OptimizedTileData tileData, int tileIndex, int index)
        {
            if (lodLevel < 0)
            {
                lodLevel = 0;
            }

            List<int[]> lodIDs = mTileObjectIDs[tileIndex];
            if (lodIDs == null)
            {
                lodIDs = new List<int[]>();
                mTileObjectIDs[tileIndex] = lodIDs;
                int nLODs = tileData.objectsOfEachLOD.Count;
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    var childPrefabs = tileData.objectsOfEachLOD[lod].childPrefabs;
                    int n = childPrefabs.Length;
                    int[] ids = new int[n];
                    lodIDs.Add(ids);
                    for (int i = 0; i < n; ++i)
                    {
                        ids[i] = map.nextCustomObjectID;
                    }
                }
            }

            if (lodLevel >= lodIDs.Count || index >= lodIDs[lodLevel].Length)
            {
                return 0;
            }
            return lodIDs[lodLevel][index];
        }

        public override void RefreshObjectsInViewport()
        {
            Debug.Assert(false, "can't be here");
        }

        public RectInt GetViewRect(Rect viewport)
        {
            var startCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMin, 0, viewport.yMin));
            var endCoord = FromWorldPositionToCoordinate(new Vector3(viewport.xMax, 0, viewport.yMax));

            return new RectInt(startCoord.x, startCoord.y, endCoord.x - startCoord.x, endCoord.y - startCoord.y);
        }
        #endregion

        public override bool SetZoom(float zoom)
        {
            bool lodChanged = false;
            if (map.enableLOD)
            {
                lodChanged = base.SetZoom(zoom);
            }

            return lodChanged;
        }

        public bool SetZoomFromAction(FrameAction parentAction, float oldZoom, Rect oldViewport, float newZoom, Rect newViewport)
        {
            bool lodChanged = base.SetZoom(newZoom);

            if (lodChanged)
            {
                var oldRect = GetViewRect(oldViewport);
                var oldMin = oldRect.min;
                var oldMax = oldRect.max;
                var oldMinX = oldMin.x;
                var oldMinY = oldMin.y;
                var oldMaxX = oldMax.x;
                var oldMaxY = oldMax.y;

                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        var objectData = GetBigTileData(j, i);
                        if (objectData != null)
                        {
                            mIsLODChanged = true;
                            SetObjectActiveTop(parentAction, objectData, false, true, oldZoom, j, i);
                            mIsLODChanged = false;
                        }
                    }
                }

                var newRect = GetViewRect(newViewport);
                var newMin = newRect.min;
                var newMax = newRect.max;
                var newMinX = newMin.x;
                var newMinY = newMin.y;
                var newMaxX = newMax.x;
                var newMaxY = newMax.y;

                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        var objectData = GetBigTileData(j, i);
                        if (objectData != null)
                        {
                            mIsLODChanged = true;
                            SetObjectActiveTop(parentAction, objectData, true, true, newZoom, j, i);
                            mIsLODChanged = false;
                        }
                    }
                }
            }

            //if (InstantActionCounter.addCount != 0 || InstantActionCounter.removeCount != 0)
            //{
            //    InstantActionCounter.Log();
            //}
            mLastCameraZoom = newZoom;

            return lodChanged;
        }

        public new Vector2Int FromWorldPositionToCoordinate(Vector3 position)
        {
            float dx = position.x - mLayerOrigin.x;
            float dz = position.z - mLayerOrigin.z;
            return new Vector2Int((int)(dx / tileWidth), (int)(dz / tileHeight));
        }

        void RemoveChildren(FrameAction parentAction, int currentLOD, int nextLOD, bool instant, bool hasInstantAction, int x, int y)
        {
            var tileIndex = y * mCols + x;

            if (mTileChildren != null)
            {
                //取消删除任务
                List<TileObjectData> objectsInThisTile;
                bool found = mTileChildren.TryGetValue(tileIndex, out objectsInThisTile);
                if (found)
                {
                    for (int i = 0; i < objectsInThisTile.Count; ++i)
                    {
                        if (instant)
                        {
                            if (MapCoreDef.IsInstantSwitchObjectType(objectsInThisTile[i].objectType) && objectsInThisTile[i].lod == currentLOD)
                            {
                                var action = FrameActionRemoveTileObject.Require(null, this, objectsInThisTile[i]);
                                mInstantActions.Add(action);
                            }
                        }
                        else
                        {
                            if (!hasInstantAction || !MapCoreDef.IsInstantSwitchObjectType(objectsInThisTile[i].objectType))
                            {
                                if (objectsInThisTile[i].lod == currentLOD)
                                {
                                    FrameActionRemoveTileObject.Require(parentAction, this, objectsInThisTile[i]);
                                }
                            }
                        }
                    }

                    for (int i = 0; i < mInstantActions.Count; ++i)
                    {
                        //++InstantActionCounter.removeCount;
                        mInstantActions[i].Do();
                        mInstantActions[i].OnDestroy();
                    }
                    mInstantActions.Clear();
                }
            }

            //删除这个tile的update action
            if (instant == false)
            {
                var key = FrameActionUpdateTileObjects.MakeActionKey(tileIndex, currentLOD, FrameActionType.UpdateTileObjects);
                bool removed = map.RemoveFrameAction(mUpdateActionQueueIndex, key);
            }
        }

        //管理Tile的子节点
        void AddChildren(FrameAction parentAction, int lastLOD, int newLOD, bool instantAction, bool hasInstantAction, int x, int y)
        {
            var tileData = GetBigTileData(x, y);
            if (newLOD < tileData.objectsOfEachLOD.Count)
            {
                var childrenModelTemplates = tileData.objectsOfEachLOD[newLOD].childPrefabs;
                int n = childrenModelTemplates.Length;
                var tileIndex = y * mCols + x;
                var tilePos = FromCoordinateToWorldPosition(x, y);
                int currentQuality = QualityControl.GetCurrentQuality();

                for (int i = 0; i < n; ++i)
                {
                    var objectID = GetObjectID(newLOD, tileData, tileIndex, i);
                    //必须是没有加载的物体才加载
                    if (mActiveTileChildren.ContainsKey(objectID) == false)
                    {
                        var prefabInfo = mPrefabInfos[childrenModelTemplates[i].prefabInitInfoIndex];
                        if (prefabInfo.maxVisibleQuality >= currentQuality) {
                            var action = FrameActionAddTileObject.Require(this, tilePos, childrenModelTemplates[i], prefabInfo.prefabPath, objectID, i, newLOD, mOnObjectScaleChangeCallback);
                            if (instantAction)
                            {
                                if (MapCoreDef.IsInstantSwitchObjectType(childrenModelTemplates[i].objectType))
                                {
                                    //++InstantActionCounter.addCount;
                                    //如果这个tile object和上一个lod的tile object是相同位置并且是标记成了立即切换的object的,则直接显示,避免闪烁问题
                                    action.Do();
                                    action.OnDestroy();
                                }
                            }
                            else
                            {
                                if (!hasInstantAction || !MapCoreDef.IsInstantSwitchObjectType(childrenModelTemplates[i].objectType))
                                {
                                    //在后续帧切换lod
                                    mSortedActions.Add(action);
                                }
                            }
                        }
                    }
                }

                //temp code
                //if (instantCount != 0)
                //{
                //    Debug.Log("instant action count: " + instantCount);
                //}
                parentAction.AddChildActions(mSortedActions);
                mSortedActions.Clear();

                if (instantAction == false && n > 0)
                {
                    //增加更新新object的命令
                    if (!mDontUpdateTileBigObjectCulling[y, x])
                    {
                        var updateAction = FrameActionUpdateTileObjects.Require(this, tileData, newLOD, x, y);
                        map.AddFrameAction(mUpdateActionQueueIndex, updateAction, false);
                    }
                }
            }
        }

        public void UpdateCulling()
        {
            mTileObjectCullManager.UpdateViewport(map.viewport);
        }

        public override bool UpdateViewport(Rect newViewport, float newZoom)
        {
            if (!mInited)
            {
                return false;
            }

            var key = FrameActionUpdateFrontLayerViewport.MakeActionKey(id, mActionTimeStamp, FrameActionType.UpdateFrontLayerViewport);
            bool removed = map.RemoveFrameActionsOfType(mActionQueueIndex, FrameActionType.UpdateFrontLayerViewport);

            ++mActionTimeStamp;
            if (mActionTimeStamp > 10000)
            {
                mActionTimeStamp = 10000;
            }

            bool zoomChanged = !Mathf.Approximately(newZoom, mLastCameraZoom);
            bool viewportChanged = (newViewport != mLastViewport);
            if (zoomChanged || viewportChanged)
            {
                var action = FrameActionUpdateFrontLayerViewport.Require(this, mActionTimeStamp, mLastViewport, mLastCameraZoom, newViewport, newZoom);
                map.AddFrameAction(mActionQueueIndex, action, false);
            }

            return false;
        }

        public void UpdateViewportFromAction(FrameAction parentAction, bool lodChanged, Rect oldViewport, Rect newViewport)
        {
            if (!lodChanged)
            {
                var oldViewRect = GetViewRect(oldViewport);
                var newViewRect = GetViewRect(newViewport);

                UpdateViewRect(parentAction, oldViewRect, newViewRect);
            }

            mLastViewport = newViewport;
        }

        public void GetTilesInViewport(List<ObstacleDisplayInfo> tiles)
        {
            var rect = GetViewRect(map.viewport);
            var min = rect.min;
            var max = rect.max;
            for (int i = min.y; i <= max.y; ++i)
            {
                for (int j = min.x; j <= max.x; ++j)
                {
                    //物体不在新的视野中,隐藏它
                    var objectData = GetBigTileData(j, i);
                    if (objectData != null)
                    {
                        tiles.Add(new ObstacleDisplayInfo() { tileData = objectData, position = FromCoordinateToWorldPositionCenter(j, i) });
                    }
                }
            }
        }

        bool UpdateViewRect(FrameAction parentAction, RectInt oldViewRect, RectInt newViewRect)
        {
            if (!oldViewRect.Equals(newViewRect))
            {
                var oldMin = oldViewRect.min;
                var oldMax = oldViewRect.max;
                var newMin = newViewRect.min;
                var newMax = newViewRect.max;
                int oldMinX = oldMin.x;
                int oldMinY = oldMin.y;
                int oldMaxX = oldMax.x;
                int oldMaxY = oldMax.y;
                int newMinX = newMin.x;
                int newMinY = newMin.y;
                int newMaxX = newMax.x;
                int newMaxY = newMax.y;

                for (int i = oldMinY; i <= oldMaxY; ++i)
                {
                    for (int j = oldMinX; j <= oldMaxX; ++j)
                    {
                        if (!(i >= newMinY && i <= newMaxY &&
                            j >= newMinX && j <= newMaxX))
                        {
                            //物体不在新的视野中,隐藏它
                            var objectData = GetBigTileData(j, i);
                            if (objectData != null)
                            {
                                SetObjectActiveTop(parentAction, objectData, false, false, mCurrentZoom, j, i);
                            }
                        }
                    }
                }

                for (int i = newMinY; i <= newMaxY; ++i)
                {
                    for (int j = newMinX; j <= newMaxX; ++j)
                    {
                        if (!(i >= oldMinY && i <= oldMaxY &&
                            j >= oldMinX && j <= oldMaxX))
                        {
                            //物体不在旧的视野中,显示它
                            var objectData = GetBigTileData(j, i);
                            if (objectData != null)
                            {
                                SetObjectActiveTop(parentAction, objectData, true, false, mCurrentZoom, j, i);
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        }

        #region show npc
        public void UpdateObjectScaleAtHeight()
        {
            if (mHasScaleObject)
            {
                float scaleFactor = mScaleUpdater.UpdateObjectScaleAtHeight();
                if (scaleFactor != 0)
                {
                    mScaleFactorAtCameraHeight = scaleFactor;
                    foreach (var obj in mVisibleTileChildren)
                    {
                        var tileObject = obj.Value;
                        if (tileObject.objectType == TileObjectType.ScaleDecorationObject)
                        {
                            tileObject.SetScale(mScaleFactorAtCameraHeight * obj.Value.baseScale);
                        }
                    }
                }
            }
        }

        public override bool isGameLayer => true;

        public void OnShowNPC(long npcID, Vector3 center, float radius)
        {
            if (mInited)
            {
                //temp code, todo, 地图lod确定后优化,在某个lod之上就不判断npc与装饰物是否重叠了
                var action = FrameActionCheckDecorationObjectOverlap.Require(this, npcID, center, radius);
                map.AddFrameAction(mOverlapActionQueueIndex, action, false);
            }
            else
            {
                Debug.LogError("layer not inited");
            }
        }

        //释放npcID占领的坑位
        public void OnHideNPC(long npcID)
        {
            if (mInited)
            {
                List<int> takenIDs;
                mNPCTakenIDs.TryGetValue(npcID, out takenIDs);
                if (takenIDs != null)
                {
                    for (int i = 0; i < takenIDs.Count; ++i)
                    {
                        RemoveInvaderID(takenIDs[i], npcID);
                    }
                    takenIDs.Clear();
                    mIDPool.Release(takenIDs);
                    mNPCTakenIDs.Remove(npcID);
                }
            }
            else
            {
                Debug.LogError("layer not inited");
            }
        }

        void RemoveInvaderID(int tileObjectID, long npcID)
        {
            List<long> invaders;
            mTileObjectsInvaderIDs.TryGetValue(tileObjectID, out invaders);
            if (invaders != null)
            {
                bool suc = invaders.Remove(npcID);
#if UNITY_EDITOR
                Debug.Assert(suc);
#endif
            }
        }

        public void HideDecorationObject(long npcID, Vector3 center, float radius)
        {
            var rect = new Rect(center.x - radius, center.z - radius, radius * 2, radius * 2);
            var range = GetViewRect(rect);
            int minX = range.xMin;
            int minY = range.yMin;
            int maxX = range.xMax;
            int maxY = range.yMax;

            for (int y = minY; y <= maxY; ++y)
            {
                for (int x = minX; x <= maxX; ++x)
                {
                    var tile = GetBigTileData(x, y);
                    if (tile != null)
                    {
                        Vector3 tilePos = FromCoordinateToWorldPosition(x, y);
                        var localCenter = center - tilePos;

                        var tileIndex = y * mCols + x;
                        mIntersectedTileObjects.Clear();

                        Rect localBounds = new Rect(new Vector2(localCenter.x - radius, localCenter.z - radius), new Vector2(radius * 2, radius * 2));
                        int nLODs = mBigTileDatas[tileIndex].objectsOfEachLOD.Count;
                        for (int lod = 0; lod < nLODs; ++lod)
                        {
                            var objectsInThisLOD = mBigTileDatas[tileIndex].objectsOfEachLOD[lod].childPrefabs;
                            for (int k = 0; k < objectsInThisLOD.Length; ++k)
                            {
                                //不检测big object
                                if (!MapCoreDef.IsBigObject(objectsInThisLOD[k].objectType))
                                {
                                    Rect localBoundsInPrefab = objectsInThisLOD[k].GetLocalBoundsInPrefab(mPrefabInfos);
                                    if (localBoundsInPrefab.Overlaps(localBounds))
                                    {
                                        mIntersectedTileObjects.Add(new TileObjectInfo2(lod, k, 0));
                                    }
                                }
                            }
                        }

                        foreach (var p in mIntersectedTileObjects)
                        {
                            var tileObjectID = GetObjectID(p.lod, mBigTileDatas[tileIndex], tileIndex, p.index);
                            TakeID(npcID, tileObjectID);

                            //隐藏目前可见的与npc重叠的装饰物
                            HideVisibleObject(tileObjectID);
                        }
                    }
                }
            }
        }

        //tileObjectID对应的坑位是否被占领
        public bool IsIDTaken(int tileObjectID, TileObjectType objectType)
        {
            if (objectType != TileObjectType.ScaleDecorationObject &&
                objectType != TileObjectType.NoneScaleDecorationObject)
            {
                return false;
            }

            List<long> invaderIDs;
            bool suc = mTileObjectsInvaderIDs.TryGetValue(tileObjectID, out invaderIDs);
            if (!suc)
            {
                return false;
            }
            return invaderIDs.Count > 0;
        }

        //占领id对应的坑位
        public void TakeID(long npcID, int id)
        {
            List<int> takenIDs = null;
            mNPCTakenIDs.TryGetValue(npcID, out takenIDs);
            if (takenIDs == null)
            {
                takenIDs = mIDPool.Require();
                mNPCTakenIDs[npcID] = takenIDs;
            }
            takenIDs.Add(id);

            List<long> invaderIDs;
            mTileObjectsInvaderIDs.TryGetValue(id, out invaderIDs);
            if (invaderIDs == null)
            {
                invaderIDs = new List<long>();
                mTileObjectsInvaderIDs[id] = invaderIDs;
            }
            invaderIDs.Add(npcID);
        }

        //隐藏装饰物
        void HideVisibleObject(int tileObjectID)
        {
            var tileObjectData = map.FindObject(tileObjectID) as TileObjectData;
            if (tileObjectData != null)
            {
                if (tileObjectData.objectType == TileObjectType.NoneScaleDecorationObject ||
                    tileObjectData.objectType == TileObjectType.ScaleDecorationObject)
                {
                    SetObjectActiveOnly(tileObjectData, false, currentLOD);
                }
            }
        }

        public void AddTileObject(TileObjectData obj)
        {
            List<TileObjectData> objectsInThisTile;

            if (!mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile))
            {
                mTileChildren[obj.tileIndex] = objectsInThisTile = mTileObjectDataListPool.Require();
            }

#if UNITY_EDITOR
            Debug.Assert(objectsInThisTile.Contains(obj) == false);
#endif
            objectsInThisTile.Add(obj);
            mActiveTileChildren.Add(obj.id, obj);

            //现在只管理小件物体的视野,太大的物体例如山不适合用这种方式管理
            
            if (obj.useCullManager)
            {
                var pos = obj.GetPosition();
                bool isVisible;
                var bounds = obj.GetBounds();
                float width = bounds.width;
                float height = bounds.height;
                float x = bounds.x;
                float z = bounds.y;
                obj.cullObjectID = mTileObjectCullManager.RegisterRectangle(width * 0.5f, height * 0.5f, x + width * 0.5f, z + height * 0.5f, mOnTileObjectVisibilityChangeCallback, obj, out isVisible);
            }
        }

        public void RemoveTileObject(TileObjectData obj)
        {
            List<TileObjectData> objectsInThisTile;
            mTileChildren.TryGetValue(obj.tileIndex, out objectsInThisTile);
            bool suc = objectsInThisTile != null && objectsInThisTile.Remove(obj);
            
#if UNITY_EDITOR
            Debug.Assert(suc);
#endif
            bool removeSuc = mActiveTileChildren.Remove(obj.id);
#if UNITY_EDITOR
            Debug.Assert(removeSuc);
#endif
            if (obj.useCullManager)
            {
                suc = mTileObjectCullManager.UnregisterCullObject(obj.cullObjectID);
#if UNITY_EDITOR
                Debug.Assert(suc);
#endif
            }

            // 如果TileObject列表已为空，则将其回收到对象池
            if (objectsInThisTile != null && objectsInThisTile.Count == 0)
            {
                mTileChildren.Remove(obj.tileIndex);
                mTileObjectDataListPool.Release(objectsInThisTile);
            }
        }

        void OnTileObjectVisibilityChange(long id, object tileObj, bool visible)
        {
            var tileObject = tileObj as TileObjectData;
            bool isTaken = IsIDTaken(tileObject.GetEntityID(), tileObject.objectType);
            if (isTaken == false)
            {
                bool isActive = tileObject.IsObjActive();
                if (visible != isActive)
                {
                    //隐藏视野外的物体,显示视野内的物体
                    SetObjectActiveOnly(tileObject, visible, tileObject.lod);

                    if (visible && isActive == false)
                    {
                        //第一次显示,设置装饰物的初始scale
                        if (tileObject.objectType == TileObjectType.ScaleDecorationObject)
                        {
                            tileObject.SetScale(objectScale * tileObject.baseScale);
                        }
                    }
                }
            }
            else
            {
                SetObjectActiveOnly(tileObject, false, tileObject.lod);
            }
        }

        public bool HasTileObject(int id)
        {
            return mActiveTileChildren.ContainsKey(id);
        }

        public TileObjectData GetTileObject(int id)
        {
            TileObjectData obj;
            mActiveTileChildren.TryGetValue(id, out obj);
            return obj;
        }

        public override bool Contains(int objectID)
        {
            Debug.Assert(false);
            return true;
            //return mObjects.ContainsKey(objectID);
        }

        //x,y对应的地块是否是视野边界地块
        public bool IsBoundsTile(int x, int y)
        {
            var viewRect = GetViewRect(map.viewport);
            int xMin = viewRect.x;
            int xMax = xMin + viewRect.width;
            int yMin = viewRect.y;
            int yMax = yMin + viewRect.height;
            if (x > xMin && x < xMax &&
                y > yMin && y < yMax)
            {
                return false;
            }
            return true;
        }
        #endregion

        void LoadTileTask(Stream stream)
        {
            InitLayer();

            mBigTileDatas = new OptimizedTileData[mRows * mCols];
            using BinaryReader reader = new BinaryReader(stream);

            int version = reader.ReadInt32();

            int nPrefabs = reader.ReadInt32();
            mPrefabInfos = new PrefabInitInfo1[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                string prefabPath = Utils.ReadString(reader);
                float boundsMinX = reader.ReadSingle();
                float boundsMinZ = reader.ReadSingle();
                float boundsWidth = reader.ReadSingle();
                float boundsHeight = reader.ReadSingle();
                var rotation = Utils.ReadQuaternion(reader);
                var scale = Utils.ReadVector3(reader);
                float height = reader.ReadSingle();

                mPrefabInfos[i] = new PrefabInitInfo1(prefabPath, boundsMinX, boundsMinZ, boundsWidth, boundsHeight, rotation, scale, height);
            }

            int idOffset = map.nextCustomObjectID;
            //load tiles
            List<OptimizedTileData> allUniqueTiles = new List<OptimizedTileData>();
            int nTiles = reader.ReadInt32();
            for (int i = 0; i < nTiles; ++i)
            {
                //read id
                int id = reader.ReadInt32();
                id += idOffset;

                var tileData = new OptimizedTileData(id);
                int nLODs = reader.ReadInt32();
                for (int lod = 0; lod < nLODs; ++lod)
                {
                    //read prefab path
                    string originalPrefabPath = Utils.ReadString(reader);

                    int nObjectsInThisLOD = reader.ReadInt32();
                    var objects = new ChildPrefabTransform2[nObjectsInThisLOD];
                    tileData.objectsOfEachLOD.Add(new OptimizedTileData.LOD(originalPrefabPath, objects));
                    for (int k = 0; k < nObjectsInThisLOD; ++k)
                    {
                        ChildPrefabTransform2 childPrefabData = new ChildPrefabTransform2();
                        childPrefabData.x = reader.ReadSingle();
                        childPrefabData.z = reader.ReadSingle();

                        childPrefabData.prefabInitInfoIndex = reader.ReadInt16();
                        childPrefabData.objectType = (TileObjectType)reader.ReadByte();
                        objects[k] = childPrefabData;
                    }
                }

                allUniqueTiles.Add(tileData);
            }

            int idx = 0;
            for (int i = 0; i < mRows; ++i)
            {
                for (int j = 0; j < mCols; ++j)
                {
                    int usedTileIndex = reader.ReadInt32();
                    if (usedTileIndex >= 0)
                    {
                        mBigTileDatas[idx] = allUniqueTiles[usedTileIndex];
                        //mTileChildren[idx] = new List<TileObjectData>(mBigTileDatas[idx].objectsOfEachLOD[0].childPrefabs.Length);
                    }
                    ++idx;
                }
            }

            //-------------------------version 2 start-------------------------------
            if (version >= 2)
            {
                for (int i = 0; i < nPrefabs; ++i)
                {
                    mPrefabInfos[i].maxVisibleQuality = reader.ReadInt16();
                }
            }
            //-------------------------version 2 end-------------------------------

            //-------------------------version 3 start-------------------------------
            if (version >= 3)
            {
                for (int i = 0; i < mRows; ++i)
                {
                    for (int j = 0; j < mCols; ++j)
                    {
                        mDontUpdateTileBigObjectCulling[i, j] = reader.ReadBoolean();
                    }
                }
            }
            //-------------------------version 3 end-------------------------------
            reader.Close();

            mInited = true;
        }

        void DoLoadAllTiles(Stream stream, bool async)
        {
            if (stream != null)
            {
                var task = new LoadingTask(() =>
                {
                    LoadTileTask(stream);
                });
                LoadingTaskManager.AddTask(task);
            }
            else
            {
                LoadTileTask(stream);
            }
        }

        void InitLayer()
        {
            mTileObjectIDs = new List<int[]>[mRows * mCols];

            mTileObjectCullManager.Init(map, map.origin, "tile object layer cull manager", MapCoreDef.MAP_DECORATION_LAYER_CULL_GRID_SIZE, GetLayerWidthInMeter(), GetLayerHeightInMeter(), 2, false, false);

            mOnTileObjectVisibilityChangeCallback = OnTileObjectVisibilityChange;

            mActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_FRONT_TILE_QUEUE);
            mUpdateActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_UPDATE_TILE_OBJECT_QUEUE);
            mOverlapActionQueueIndex = map.AddFrameActionQueue(MapCoreDef.MAP_FRONT_TILE_OVERLAPE_CHECK_QUEUE);

            mDontUpdateTileBigObjectCulling = new bool[mRows, mCols];

            var updateAction = FrameActionUpdateTileObjectCull.Require(this);
            map.AddFrameAction(mUpdateActionQueueIndex, updateAction, false);
        }

        async UniTask LoadTiles(bool asyncLoading)
        {
            string path = MapCoreDef.GetOptimizedFrontTileDataPath(map.dataFolder);
            if (asyncLoading)
            {
                using var stream = await MapModuleResourceMgr.LoadTextStreamAsync(path);
                DoLoadAllTiles(stream, asyncLoading);
            }
            else
            {
                using var stream = await MapModuleResourceMgr.LoadTextStreamAsync(path);
                DoLoadAllTiles(stream, asyncLoading);

            }
        }

        //大的tile数据
        internal OptimizedTileData[] gridObjects { get { return mBigTileDatas; } }
        //动态缩放的物体在当前相机高度下的缩放值
        public float objectScale { get { return mScaleFactorAtCameraHeight; } }
        //是否有需要动态缩放的物体
        public bool hasScaleObject { set { mHasScaleObject = value; } get { return mHasScaleObject; } }
        public bool isLoading { set; get; }
        public int objectCount { get { return mObjects.Count; } }
        public PrefabInitInfo1[] prefabInitInfo { get { return mPrefabInfos; } }
        public TileObjectDataPool pool { get { return mPool; } }
        Rect mLastViewport;
        float mLastCameraZoom;

        KeepScaleConfig mScaleConfig;

        //更新装饰物的scale
        float mScaleFactorAtCameraHeight = 1.0f;
        KeepScaleUpdater mScaleUpdater;

        //npc占领的所有tile object id
        Dictionary<long, List<int>> mNPCTakenIDs = new Dictionary<long, List<int>>();
        //每个tile object被哪些npc占领了
        Dictionary<int, List<long>> mTileObjectsInvaderIDs = new Dictionary<int, List<long>>();
        List<TileObjectInfo2> mIntersectedTileObjects = new List<TileObjectInfo2>();
        List<FrameAction> mSortedActions = new List<FrameAction>();
        List<FrameAction> mInstantActions = new List<FrameAction>();

        //按tile来索引
        Dictionary<int, List<TileObjectData>> mTileChildren = new Dictionary<int, List<TileObjectData>>(1600);
        Dictionary<int, TileObjectData> mActiveTileChildren = new Dictionary<int, TileObjectData>(2000);
        Dictionary<int, TileObjectData> mVisibleTileChildren = new Dictionary<int, TileObjectData>(1000);
        int mActionQueueIndex;
        int mOverlapActionQueueIndex;
        int mUpdateActionQueueIndex;
        //这一层是否有需要缩放的物体
        bool mHasScaleObject = false;
        bool mIsLODChanged = false;
        bool mInited = false;

        static int mActionTimeStamp;
        static ObjectPool<List<TileObjectData>> mTileObjectDataListPool = new ObjectPool<List<TileObjectData>>(10, () => new List<TileObjectData>(), l => l.Clear());

        //在每个tile中,每个lod中每个child object的id
        List<int[]>[] mTileObjectIDs;

        ObjectPool<List<int>> mIDPool = new ObjectPool<List<int>>(1000, () => new List<int>(5), id => id.Clear());
        CullManager mTileObjectCullManager = new CullManager();
        Dictionary<int, TileObjectData> mObjects = new Dictionary<int, TileObjectData>(1000);

        OptimizedTileData[] mBigTileDatas;
        PrefabInitInfo1[] mPrefabInfos;

        TileObjectDataPool mPool = new TileObjectDataPool();

        bool[,] mDontUpdateTileBigObjectCulling;

        System.Action<TileObjectData, int> mOnActiveStateChangeCallback;
        System.Action<TileObjectData> mOnObjectScaleChangeCallback;
        System.Action<long, object, bool> mOnTileObjectVisibilityChangeCallback;
    };
}
