﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using System.Collections.Generic;
using UnityEngine;
using System.IO;

namespace TFW.Map
{
    //生成游戏运行时需要的河流资源
    public static class PolygonRiverMaterialSaver
    {
        public static void PrepareSaveRiverMaterial(string riverMaterialFolder)
        {
            if (!string.IsNullOrEmpty(riverMaterialFolder))
            {
                FileUtil.DeleteFileOrDirectory(riverMaterialFolder);
                Directory.CreateDirectory(riverMaterialFolder);
            }
        }
        //返回每个river使用的prefab路径
        public static void SaveRiverMaterials(PolygonRiverLayer riverLayer, PolygonRiverData river, string riverMaterialFolder)
        {
            if (Directory.Exists(riverMaterialFolder))
            {
                int lodCount = riverLayer.layerData.lodConfig.lodConfigs.Length;
                CreateMaterial(riverMaterialFolder, lodCount, river, riverLayer);
                AssetDatabase.Refresh();
            }
        }

        public static void LoadRiverMaterials(PolygonRiverLayer riverLayer, PolygonRiverData river, string riverMaterialFolder)
        {
            if (Directory.Exists(riverMaterialFolder))
            {
                int lodCount = riverLayer.layerData.lodConfig.lodConfigs.Length;
                for (int i = 0; i < lodCount; ++i)
                {
                    var sections = river.sections;
                    for (int s = 0; s < sections.Count; ++s)
                    {
                        //copy all materials except the river texture!
                        var material = riverLayer.GetRiverMaterial(river.id, s);
                        string mtlPath = PolygonRiverLayer.GetAssetName(riverMaterialFolder, "river_material", river.id, s, i, "mat");
                        var savedMaterial = AssetDatabase.LoadAssetAtPath<Material>(mtlPath);
                        Utils.CopyRiverMaterial(savedMaterial, material);
                    }
                }
            }
        }

        static void CreateMaterial(string folder, int lodCount, PolygonRiverData river, PolygonRiverLayer layer)
        {
            for (int i = 0; i < lodCount; ++i)
            {
                bool hide = layer.layerData.lodConfig.lodConfigs[i].hideObject;
                if (hide == false)
                {
                    var sections = river.sections;
                    for (int s = 0; s < sections.Count; ++s)
                    {
                        var material = layer.GetRiverMaterial(river.id, s);
                        var savedMaterial = Object.Instantiate<Material>(material);
                        string mtlPath = PolygonRiverLayer.GetAssetName(folder, "river_material", river.id, s, i, "mat");
                        AssetDatabase.CreateAsset(savedMaterial, mtlPath);
                    }
                }
            }
        }
    }
}

#endif