﻿using Common;
using cspb;
using DeepUI;
using Game.Sprite.Fight;
using Logic;
using UI.Utils;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI
{

    /// <summary>
    /// 头像
    /// </summary>
    public partial class UIMain2
    {

        #region 属性字段信息

        /// <summary>
        /// 头像对象
        /// </summary>
        [PopupField("content/UpperRoot/SideRoot/Left_Top/Head_k1")]
        private SelfHeadIconCompent headObj;
        [PopupField("content/UpperRoot/SideRoot/Left_Top/Head_k1/RedDot")]
        private GameObject headRedRoot;

        private RedWidget headRedWidget;
        ///// <summary>
        ///// 战力背景对象
        ///// </summary>
        //[PopupField("content/UpperRoot/SideRoot/Left_Top/Head/headbg")]
        //private GameObject powerBgObj;

        ///// <summary>
        ///// 战力对象
        ///// </summary>
        //[PopupField("content/UpperRoot/SideRoot/Left_Top/Head/Power")]
        //private GameObject powerObj;

        /// <summary>
        /// 头像Widget信息
        /// </summary>
        //private MainHeadWidget headWidget;
    
        /// <summary>
        /// 头像框红点
        /// </summary>
        private bool _hasAvatarRed = false;
        /// <summary>
        /// 皮肤红点
        /// </summary>
        private bool _hasSkinRed = false;
        #endregion

        #region 初始化

        /// <summary>
        /// 初始化头像
        /// </summary>
        private void InitHead()
        {
            headObj.SelfIconWidget.AddListener(TFW.EventTriggerType.Click, OnClickHead);
            headRedWidget = new RedWidget(headRedRoot);
            EventMgr.RegisterEvent(TEventType.AvatarFrameRedCount, OnUpdateAvatarFrameRedPoint, this);
            EventMgr.RegisterEvent(TEventType.GetAllSkinAck, SetSkinRed, this);
            InitAvatarFrameRedPoint(Game.Data.GameData.I.headFrameGameData.GetRedPoint());
            EventMgr.RegisterEvent(TEventType.RefreshSkinRedNew, SetSkinRed, this);

            //headWidget = new MainHeadWidget(headObj);
            //headWidget.SetClickHead(OnClickHead);
        }

        public void InitAvatarFrameRedPoint(int count)
        {           
            _hasAvatarRed = count > 0;
            count = _hasAvatarRed || _hasSkinRed ? 1 : 0;
            headRedWidget?.SetData(count, false);
        }

        public void OnUpdateAvatarFrameRedPoint(object[] obj)
        {
            var avatarObj = obj !=null && obj.Length>0 ? obj[0] : 0;
            var avatarCount = (int)avatarObj;
            _hasAvatarRed = avatarCount > 0;
            avatarCount = _hasAvatarRed || _hasSkinRed ? 1 : 0;
            headRedWidget?.SetData(avatarCount, false);
        }
        
        private void SetSkinRed(object[] obj)
        {
            var count = ShopSkinMgr.I.GetSkinRedCountNew();
            _hasSkinRed = count > 0;
            count =  _hasAvatarRed || _hasSkinRed ? 1 : 0;
            headRedWidget?.SetData(count, false);
        }

        void RefushTaskRed() 
        {
            ///请求红点信息
            //CpeTaskMgr.I.RequestCpeTaskInfo();
        }

        /// <summary>
        /// 反向初始化头像
        /// </summary>
        private void UnInitHead()
        {
            //if(headWidget != null)
            //{
            //    headWidget.Destroy();
            //    headWidget = null;
            //}
        }

        #endregion


        #region 数据刷新

        /// <summary>
        /// 刷新头像信息数据
        /// </summary>
        public async void UpdateHeadData()
        {
            //if(headWidget != null)
            //{
            //    var power = LPlayer.I.TotalPower;
            //    //var powerRect = powerObj.GetComponent<RectTransform>();
            //    //if (power == 0)
            //    //{
            //    //    //powerBgObj.SetActive(false);
            //    //    //powerObj.SetActive(false);
            //    //    if(powerRect)
            //    //        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(powerRect);
            //    //}
            //    //else
            //    //{
            //    //    //powerBgObj.SetActive(true);
            //    //    //powerObj.SetActive(true);
            //    //    if (powerRect)
            //    //        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(powerRect);
            //    //}
            //    UnifyPlayerHead hand = new UnifyPlayerHead()
            //    {
            //        avatarCfgID = LPlayer.I.AvatarCfgID,
            //        avatarFrrame = LPlayer.I.avatarFrameId,
            //        customHead = LPlayer.I.CustomPlayerHead,
            //        useCustomHead = LPlayer.I.UseCustomHeadIcon,
            //    };
                
            //    object[] param = await PlayerAssetsMgr.I.GetShowAction();
            //    long currentPhyPower = (long)param[0];
            //    long maxPhyPower = (long)param[1];

            //    headWidget.SetData(
            //        hand, 
            //        FightManager.I.TrainLevel.ToString(), 
            //        UIStringUtils.ExchangeValue(power,2),
            //        (float)currentPhyPower / maxPhyPower);
            //}
        }

        /// <summary>
        /// 刷新头像活动状态
        /// </summary>
        private void UpdateHeadActive()
        {
            //if(headWidget != null)
            //{
            //    headWidget.UpdateBtnOpenState(MainFunctionOpenUtils.PlayerHeadOpenState);
            //    //headWidget.SetRootVisible(MainFunctionOpenUtils.PlayerHeadOpenState);
            //}
        }

        #endregion


        #region 事件监听

        /// <summary>
        /// 点击头像
        /// </summary>
        private void OnClickHead(GameObject ga, PointerEventData point)
        {
            var ui = PopupManager.I.FindPopup<UI.Alliance.UIAllianceMain>();
            if (ui != null)
                return;
            
            //if (!GuideMgr.I.IsGuideDone)
            //    return;

            PopupManager.I.ShowLayer<UIPlayerMain>();
            EventMgr.FireEvent(TEventType.HideTriggerGuide);

            //UpdateShowLeftBtnsInfo();
        }
         

        #endregion


    }
}
