﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ComplexGridModelLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            var version = GetVersion();
            Utils.WriteVersion(writer, version);

            var prefabManager = GetPrefabManager();
            PrepareSaving();
            SaveSetting(writer);
            SavePrefabManager(writer, prefabManager);
            SaveComplexGridModelLayer(writer);
            SavePathMapper(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        public virtual PrefabManager GetPrefabManager()
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            return editorMapData.complexGridModelLayerPrefabManager;
        }

        protected virtual Version GetVersion()
        {
            return VersionSetting.ComplexGridModelLayerEditorDataVersion;
        }

        void SaveSetting(BinaryWriter writer)
        {
            mPathMapperPosition = writer.BaseStream.Position;
            long pathMapperOffsetPlaceholder = 0;
            writer.Write(pathMapperOffsetPlaceholder);
        }

        void SaveComplexGridModelLayer(BinaryWriter writer)
        {
            int cols = horizontalTileCount;
            int rows = verticalTileCount;

            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(rows);
            writer.Write(cols);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(layerData.exportData);
            Utils.WriteVector3(writer, localViewCenter);
            Utils.WriteVector2(writer, localViewSize);
            Utils.WriteRect(writer, layerData.realLayerBounds);
            writer.Write(layerData.enableObjectMaterialChange);
            writer.Write(layerData.enableCullIntersectedObjects);

            SaveObjectPlacementSetting(writer, layerData.objectPlacementSetting);

            int nLODs = lodCount;
            writer.Write(nLODs);
            List<IComplexGridModelData> objectsInLOD = new List<IComplexGridModelData>();
            for (int i = 0; i < nLODs; ++i)
            {
                objectsInLOD.Clear();
                layerData.GetObjectsOfLOD(objectsInLOD, i);

                int count = objectsInLOD.Count;
                writer.Write(count);
                for (int k = 0; k < count; ++k)
                {
                    SaveComplexGridModelData(writer, objectsInLOD[k]);
                }
            }
            //save map layer lod config
            SaveMapLayerLODConfig(writer, layerData);
            var tags = layerData.objectTags;
            int n = tags.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, tags[i].name);
                writer.Write(tags[i].visible);
            }
        }

        void SaveObjectPlacementSetting(BinaryWriter writer, DecorationObjectPlacementSetting placementSetting)
        {
            writer.Write(placementSetting.useMapLargeTile);
            writer.Write(placementSetting.showObjectBounds);
            writer.Write(placementSetting.translationStep);
            writer.Write(placementSetting.scaleStep);
            writer.Write(placementSetting.isGroup);
            writer.Write(placementSetting.considerObstacle);
            writer.Write(placementSetting.addLOD);
            writer.Write(placementSetting.alignByGrid);
            writer.Write(placementSetting.alignGridSize);
            writer.Write(placementSetting.onlySelectCurrentTagObjects);        
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                writer.Write((int)0);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }

        void SavePrefabManager(BinaryWriter writer, PrefabManager prefabManager)
        {
            int nGroups = prefabManager.groupCount;
            writer.Write(nGroups);
            for (int i = 0; i < nGroups; ++i)
            {
                var group = prefabManager.GetGroupByIndex(i);
                SavePrefabGroup(writer, group);
            }
        }

        void SavePrefabGroup(BinaryWriter writer, PrefabGroup group)
        {
            Utils.WriteString(writer, group.name);
            Utils.WriteColor32(writer, group.color);
            int n = group.count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteString(writer, mPathMapper.Map(group.GetPrefabPath(i)));
                //save subgroup prefab paths
                var subgroupPrefabs = group.GetSubGroupPrefabPaths(i);
                int subGroupPrefabCount = subgroupPrefabs == null ? 0 : subgroupPrefabs.Length;
                writer.Write(subGroupPrefabCount);
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    Utils.WriteString(writer, mPathMapper.Map(subgroupPrefabs[k]));
                }
            }
        }

        void SaveComplexGridModelData(BinaryWriter writer, IComplexGridModelData data)
        {
            Utils.WriteVector3(writer, data.GetPosition());
            Utils.WriteQuaternion(writer, data.GetRotation());
            Utils.WriteVector3(writer, data.GetScale());
            short prefabPathIndex = GetPrefabPathIndex(data.GetModelTemplate());
            writer.Write(prefabPathIndex);
            writer.Write(data.occupiedGridCount);
            Utils.WriteString(writer, data.objectTag);
            writer.Write(data.useRenderTextureModel);
        }

        short GetPrefabPathIndex(ModelTemplate modelTemplate)
        {
            short index;
            bool found = mModelTemplateIDToPrefabPathIndex.TryGetValue(modelTemplate.id, out index);
            if (!found)
            {
                mPrefabPathStringTable.Add(mPathMapper.Map(modelTemplate.GetLODPrefabPath(0)));
                index = (short)(mPrefabPathStringTable.Count - 1);
                mModelTemplateIDToPrefabPathIndex[modelTemplate.id] = index;
            }
            return index;
        }

        void SavePathMapper(BinaryWriter writer)
        {
            long position = writer.BaseStream.Position;
            //跳转到offset placeholder的地方,写下PathMapper数据的地址
            writer.BaseStream.Position = mPathMapperPosition;
            writer.Write(position);
            writer.BaseStream.Position = position;

            int n = mPathMapper.pathToGuid.Count;
            writer.Write(n);

            foreach (var p in mPathMapper.pathToGuid)
            {
                Utils.WriteString(writer, p.Key);
                Utils.WriteString(writer, p.Value);
            }

            //save string table
            int pathCount = mPrefabPathStringTable.Count;
            writer.Write(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                Utils.WriteString(writer, mPrefabPathStringTable[i]);
            }
        }

        void PrepareSaving()
        {
            mPathMapper = new PathMapper();
            mModelTemplateIDToPrefabPathIndex = new Dictionary<int, short>();
            mPrefabPathStringTable = new List<string>();
        }

        PathMapper mPathMapper;
        long mPathMapperPosition;
        Dictionary<int, short> mModelTemplateIDToPrefabPathIndex;
        List<string> mPrefabPathStringTable;
    }
}

#endif