﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    //地图对象的视图使用的模型的基类,负责管理game object
    public abstract class ModelBase {
        protected void SetGameObjectLayer(int layer) {
            if (mGameObject != null && layer != 0 && mGameObject.layer != layer) {
                Utils.SetLayerRecursively(mGameObject, layer);
            }
        }

        //model release时调用
        public abstract void Release();

        //model销毁时调用
        protected virtual void OnDestroy() {
            if (mGameObject) {
                Utils.DestroyObject(mGameObject);
                mGameObject = null;
            }
        }
        public GameObject gameObject { get { return mGameObject; } }
        public Transform transform {
            get {
                if (mGameObject) {
                    return mGameObject.transform;
                }
                return null;
            }
        }

        public virtual Vector3 position { get { return transform.position; } }
        public virtual Quaternion rotation { get { return transform.rotation; } }
        public virtual Vector3 scaling { get { return transform.localScale; } }

        public virtual bool active {
            get {
                if (mGameObject) {
                    return mGameObject.activeSelf;
                }
                return false;
            }
            set {
                if (mGameObject) {
                    mGameObject.SetActive(value);
                }
            }
        }

        protected GameObject mGameObject;
    }
}
