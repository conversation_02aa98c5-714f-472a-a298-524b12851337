﻿ 



 
 



/*
 * created by wzw at 2019.10.14
 */

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    //在lod切换时使用crossfade的model
    public class CrossfadeModel : ModelBase
    {
        public void Init(Map map, string currentLODPrefabPath, string newLODPrefabPath, int curLOD, int newLOD, float lowerHeight, float upperHeight, bool keepOldLOD,
            Vector3 position, Vector3 scale, Quaternion rotation)
        {
            Debug.Assert(upperHeight > lowerHeight);
            Debug.Assert(mNewLODRenderers.Count == 0);
            Debug.Assert(mCurrentLODRenderers.Count == 0);

            mKeepOldLOD = keepOldLOD;
            mOldHidden = false;
            mMap = map;

            mGameObject = mMap.view.reusableGameObjectPool.Require(MapCoreDef.DUMMY_OBJECT_NAME, position, scale, rotation);

            if (curLOD != newLOD)
            {
                mNewLODPrefabPath = newLODPrefabPath;
                mNewLOD = newLOD;
                mNewLODGameObject = mMap.view.reusableGameObjectPool.Require(mNewLODPrefabPath, Vector3.zero, Vector3.one, Quaternion.identity);
                mNewLODGameObject.GetComponentsInChildren<Renderer>(mNewLODRenderers);
                Utils.HideGameObject(mNewLODGameObject);
                mNewLODGameObject.transform.SetParent(mGameObject.transform, false);

                mActiveModels.Add(this);

                mHideLastLOD = mNewLODGameObject.GetComponentInChildren<HideLastLOD>();
            }
            else
            {
                mNewLODPrefabPath = "";
                mNewLODGameObject = null;
                mNewLOD = mCurrentLOD;
            }
            mLowerHeight = lowerHeight;
            mUpperHeight = upperHeight;
            mCurrentLOD = curLOD;
            if (mKeepOldLOD)
            {
                mCurrentLODPrefabPath = currentLODPrefabPath;
                mCurrentLODGameObject = mMap.view.reusableGameObjectPool.Require(mCurrentLODPrefabPath, Vector3.zero, Vector3.one, Quaternion.identity);
                Utils.HideGameObject(mCurrentLODGameObject);
                mCurrentLODGameObject.GetComponentsInChildren<Renderer>(mCurrentLODRenderers);
                mCurrentLODGameObject.transform.SetParent(mGameObject.transform, false);
            }
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
            var map = mMap;
            if (mCurrentLODGameObject != null)
            {
                map.view.reusableGameObjectPool.Release(mCurrentLODPrefabPath, mCurrentLODGameObject, mMap);
                mCurrentLODGameObject = null;
            }
            if (mNewLODGameObject != null)
            {
                map.view.reusableGameObjectPool.Release(mNewLODPrefabPath, mNewLODGameObject, mMap);
                mNewLODGameObject = null;
            }

            mCurrentLODRenderers.Clear();
            mNewLODRenderers.Clear();

            mMap.view.reusableGameObjectPool.Release(MapCoreDef.DUMMY_OBJECT_NAME, mGameObject, mMap);
            mGameObject = null;
        }


        //释放回对象池中
        public override void Release()
        {
            ReleaseToPool(this);
            if (mNewLOD != mCurrentLOD)
            {
                mActiveModels.Remove(this);
            }
        }

        void Update(float cameraHeight)
        {
            if (mNewLOD != mCurrentLOD)
            {
                float t = (cameraHeight - mLowerHeight) / (mUpperHeight - mLowerHeight);
                if (t >= 1)
                {
                    if (!mOldHidden)
                    {
                        mOldHidden = true;
                        if (mHideLastLOD != null)
                        {
                            mHideLastLOD.gameObject.SetActive(false);
                        }
                    }
                }
                else
                {
                    if (mOldHidden)
                    {
                        mOldHidden = false;
                        if (mHideLastLOD != null)
                        {
                            mHideLastLOD.gameObject.SetActive(true);
                        }
                    }
                }

                t = Mathf.Clamp01(t);

                for (int i = 0; i < mCurrentLODRenderers.Count; ++i)
                {
                    var mtls = mCurrentLODRenderers[i].sharedMaterials;
                    for (int k = 0; k < mtls.Length; ++k)
                    {
                        mtls[k].SetFloat("_Alpha", 1 - t);
                    }
                }
                for (int i = 0; i < mNewLODRenderers.Count; ++i)
                {
                    var mtls = mNewLODRenderers[i].sharedMaterials;
                    for (int k = 0; k < mtls.Length; ++k) { 
                        mtls[k].SetFloat("_Alpha", t);
                    }
                }
            }
        }

        public static CrossfadeModel Require(Map map, int modelTemplateID, int curLOD, int newLOD, float lowerHeight, float upperHeight, bool keepOldLOD, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            if (curLOD < 0)
            {
                curLOD = 0;
            }
            var modelTemplate = map.FindObject(modelTemplateID) as ModelTemplate;
            var curLODPrefabPath = modelTemplate.GetLODPrefabPath(curLOD);
            var newLODPrefabPath = modelTemplate.GetLODPrefabPath(newLOD);
            var model = mPool.Require();
            model.Init(map, curLODPrefabPath, newLODPrefabPath, curLOD, newLOD, lowerHeight, upperHeight, keepOldLOD, position, scale, rotation);
            return model;
        }

        static void ReleaseToPool(CrossfadeModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        public static void UpdateAll(float cameraHeight)
        {
            if (!Map.currentMap.isEditorMode)
            {
                if (!Utils.Approximately(cameraHeight, mLastUpdateCameraHeight))
                {
                    mLastUpdateCameraHeight = cameraHeight;
                    for (int i = 0; i < mActiveModels.Count; ++i)
                    {
                        mActiveModels[i].Update(cameraHeight);
                    }
                }
            }
        }

        public static void Clear()
        {
            mActiveModels.Clear();
        }

        Map mMap;
        int mNewLOD;
        int mCurrentLOD;
        string mCurrentLODPrefabPath;
        string mNewLODPrefabPath;
        float mLowerHeight;
        float mUpperHeight;
        bool mKeepOldLOD;
        bool mOldHidden;
        GameObject mNewLODGameObject;
        GameObject mCurrentLODGameObject;
        HideLastLOD mHideLastLOD;
        List<Renderer> mCurrentLODRenderers = new List<Renderer>();
        List<Renderer> mNewLODRenderers = new List<Renderer>();
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<CrossfadeModel> mPool = new ObjectPool<CrossfadeModel>(1000, () => new CrossfadeModel());
        //需要更新的model
        static List<CrossfadeModel> mActiveModels = new List<CrossfadeModel>();
        static float mLastUpdateCameraHeight;
    }
}
