﻿using DeepUI;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFW.Localization;
using TFW;
using TFW.UI;
using UnityEngine;
using Cfg.C;
using Common;
using cspb;
using Render;
using Cysharp.Threading.Tasks;

namespace UI
{


    [Popup("StargazingPlatform/UIStargazingPlatformDetailSpecial", true,true)]
    public class UIStargazingPlatformDetailSpecial:BasePopup
    {
        
        /// <summary>
        /// 情报任务详情关闭
        /// </summary>
        [PopupField("Root/TaskDetails/BtnClose")]
        private GameObject TaskDetails_BtnClose;

        /// <summary>
        /// 任务标题
        /// </summary>
        [PopupField("Root/TaskDetails/TaskTitle")]
        private TFWText TaskTitle;
        

        /// <summary>
        /// 奖励列表
        /// </summary>
        [PopupField("Root/TaskDetails/RewardParent")]
        private GameObject RewardParent;

        /// <summary>
        /// 任务描述
        /// </summary>
        [PopupField("Root/TaskDetails/TaskRewardDes")]
        private TFWText TaskRewardDes;

        /// <summary>
        /// 奖励Item
        /// </summary>
        [PopupField("Root/TaskDetails/RewardParent/RewardItem")]
        private GameObject RewardItem;

        /// <summary>
        /// 任务前往
        /// </summary>
        [PopupField("Root/TaskDetails/BtnGo")]
        private GameObject BtnGo;

        /// <summary>
        /// 任务前往文本
        /// </summary>
        [PopupField("Root/TaskDetails/BtnGo/TxtGo")]
        private TFWText TxtGo;

        /// <summary>
        /// 领取奖励
        /// </summary>
        [PopupField("Root/TaskDetails/BtnGet")]
        private GameObject BtnGet;

        /// <summary>
        /// 领取奖励文本
        /// </summary>
        [PopupField("Root/TaskDetails/BtnGet/TxtGet")]
        private TFWText TxtGet;


        protected internal override bool PopBackEnabled => true;

        protected override void OnInit()
        {
            base.OnInit();

            BindClickListener(TaskDetails_BtnClose, (x, y) =>
            {
               Close();
            });

            BindClickListener(BtnGo, (x, y) =>
            {
                BtnClickGo();
            });

            // 手动清理
            RemoveClickListener(BtnGet);
            BindClickListener(BtnGet, (x, y) =>
            {
                BtnClickGet();
            });
        }


        protected internal override void OnOpenComplete()
        {
            base.OnOpenComplete();

            OnShow();
        }

        protected internal override void OnShowComplete()
        {
            base.OnShowComplete();

            OnShow();
        }


        private void OnShow()
        {
            var mData= Data as UIStargazingPlatformDetailData;
            if ((mData!=null))
            {
                ShowTaskDetails(mData.mID);
            }
        }

        Mission NowChooseMission;
        Cfg.G.CProphecy NowChooseProphecy;
        Cfg.G.CProphecy recieveProhecy;

        private List<ItemNewWidget> rewardWidgetList;

        void BtnClickGo()
        {
            var going = LMapEntityPlayerTroop.CheckSelfTroopTarget(NowChooseMission.UnitId, NowChooseMission.Id);

            if (going)
                return;

            //Debug.LogError("--------------------------地图单位ID：" + NowChooseMission.UnitId);
            SearchIntelligenceTargetReq req = new SearchIntelligenceTargetReq();
            req.id = NowChooseMission.Id;
            MessageMgr.Send(req);
        }

        void BtnClickGet()
        {
            recieveProhecy = Cfg.C.CProphecy.RawDict().FirstOrDefault(t => t.Value.Id == NowChooseMission.CfgId).Value;

            //Debug.LogError("--------------------------领取奖励："+ NowChooseMission.Id);
            ReceiveIntelligencePrizeReq req = new ReceiveIntelligencePrizeReq();
            req.id = NowChooseMission.Id;
            MessageMgr.Send(req);
        }

       

        [PopupEvent(TEventType.SearchIntelligenceTargetAck)]
        private void OnSearchIntelligenceTargetAck(object[] args)
        {
            var ack = args[0] as SearchIntelligenceTargetAck;
            
            BtnClose();
            
            var pos = new Vector3(ack.mission.PosX / 1000, ack.mission.PosZ / 1000);

            NowChooseMission.UnitId = ack.mission.UnitId;//更新目标ID

            var entityInfo = LMapEntityManager.I.GetEntityInfo(ack.mission.UnitId);
            if (entityInfo != null)
            {
                pos = new Vector3(entityInfo.position.position.X / 1000, entityInfo.position.position.Z / 1000);
            }
            else
            {
                //Debug.LogError("未找到目标实例:" + ack.mission.UnitId);
            }

            DeepUI.PopupManager.I.ClearAllPopup();  //WndMgr.HideAll(); 

            EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
            RMap.JumpIntoTargetPosition(pos.x, pos.y, null, true);
            NTimer.Timer tickTimer = null;
            NTimer.TickNoPool(ref tickTimer, 10, 0.1f, () =>
            {
                var entityInfo = LMapEntityManager.I.GetEntityInfo(ack.mission.UnitId);
                if (entityInfo != null)
                {
                    tickTimer?.Stop();

                    // var Position = LMapEntityManager.I.GetEntityInfo(entityInfo.ID)?.CurrentPosition ?? Vector3.zero;
                    // MapCameraMgr.MoveCameraToTarget(Position.x, Position.z - MapConst.PopupZOffset, "city_world", 0.3f,
                    //      0.5f, () =>
                    //      {
                    if (NowChooseProphecy.ProphecType == 1)
                    {
                        WndMgr.Show<UINpcTroopPopup>(new UIPopMovingBaseData
                        {
                            EntityId = entityInfo.ID,
                        });
                    }
                    else if (NowChooseProphecy.ProphecType == 2)
                    {
                        PopupManager.I.ShowPanel<UIMaze>(new HistoricMovingPopupData()
                        {
                            EntityId = entityInfo.ID,
                        });
                    }
                    else if (NowChooseProphecy.ProphecType == 3)
                    {
                        PopupManager.I.ShowPanel<UICaravan>(new HistoricMovingPopupData()
                        {
                            EntityId = entityInfo.ID,
                        });
                    }else if (NowChooseProphecy.ProphecType == 4)
                    {
                        WndMgr.Show<UINpcTroopPopup>(new UIPopMovingBaseData
                        {
                            EntityId = entityInfo.ID,
                        });
                    }

                    if (GuidManage.isRunStationGuid)
                    {
                        var UIMain2 = SceneManager.I.FindScene(typeof(UIMain2)) as UIMain2;
                        UIMain2.HideGuideEffect(() =>
                        {
                            CommonLoadCtrl.I.Destroy();
                            // 关闭引导标识
                            GuidManage.isRunStationGuid = false;
                            GuidManage.TriggerGuid(GuidManage.GuidTriggerType.IntelligenceStatic, 0);
                        });
                    }
                    // });
                }

            });

        }


        void BtnClose()
        {
            LIntelligence.I.SetFirstInLIntelligence(false);

            PopupManager.I.ClosePopup<UIStargazingPlatform>();
            PopupManager.I.ClosePopup<UIStargazingPlatformDetail>();
            PopupManager.I.ClosePopup<UIStargazingPlatformDetailSpecial>();

        }
        


        void ShowTaskDetails(int id)
        {
            if (LIntelligence.I.DicMission.ContainsKey(id))
            {
                NowChooseMission = LIntelligence.I.DicMission[id];

                NowChooseProphecy = CProphecy.RawDict().FirstOrDefault(t => t.Value.Id == NowChooseMission.CfgId).Value;

                if (NowChooseProphecy != null)
                {
                    ShowRewardList();

                    string str_lvl = LocalizationMgr.Get("ProphecyInterface_5");
                    str_lvl = string.Format(str_lvl, NowChooseProphecy.ProphecyLevel);
                    TaskTitle.text = $"{LocalizationMgr.Get(NowChooseProphecy.ProphecyName)} {str_lvl}";
                    TxtGet.text = LocalizationMgr.Get("Ui_task3");
                    TaskRewardDes.text = LocalizationMgr.Get("ProphecyInterface_10");
                    var going = LMapEntityPlayerTroop.CheckSelfTroopTarget(NowChooseMission.UnitId, NowChooseMission.Id);
                    BtnGo.gameObject.GetComponent<TFWImage>().enabled = !going;
                    if (going)
                    {
                        TxtGo.text = LocalizationMgr.Get("chronicle_txt_13");
                    }
                    else
                    {
                        TxtGo.text = LocalizationMgr.Get("ProphecyInterface_11");
                    }


                    if (NowChooseMission.State == 0)
                    {
                        BtnGo.gameObject.SetActive(true);
                        BtnGet.gameObject.SetActive(false);
                    }
                    else if (NowChooseMission.State == 1)
                    {
                        BtnGo.gameObject.SetActive(false);
                        BtnGet.gameObject.SetActive(true);
                    }
                    else if (NowChooseMission.State == 2)
                    {
                        BtnGo.gameObject.SetActive(false);
                        BtnGet.gameObject.SetActive(false);
                    } 
                }
            }
        }

        private float m_TaskDetailY = 80;
        private float m_TaskDetailY2 = -133;

        

        List<GameObject> List_Reward = new List<GameObject>();

        async UniTaskVoid ShowRewardList()
        {
            foreach (var item in List_Reward)
            {
                if (item != null)
                {
                    GameObject.Destroy(item);
                }
            }

            List_Reward.Clear();

            var rewards = await VersionRewardMgr.I.GetDisplayRewards(NowChooseProphecy.RewardId);
            foreach (var reward in rewards)
            {
                GameObject item = GameObject.Instantiate(RewardItem, RewardParent.transform);
                item.SetActive(true);
                ItemNewWidget widget = new ItemNewWidget(item);
                widget.SetData(reward);
                List_Reward.Add(item);
            }

        }


    }
}
