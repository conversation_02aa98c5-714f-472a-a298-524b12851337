﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //按格子排列来生成npc刷新点
    public class GenerateSpawnPointsInGridLayout : ISpawnPointGenerationStrategy
    {
        //startOffset:第一个点的位置
        //pointDeltaDistance:两个点之间的间距
        //xOffset:偶数排的点的x的偏移
        //randomRange:点的坐标的浮动范围
        //maxMoveRange:npc最大的移动范围
        public GenerateSpawnPointsInGridLayout(Vector3 startOffset, Vector2 pointDeltaDistance, float xOffset, float randomRange, float maxMoveRange)
        {
            mStartOffset = startOffset;
            mPointDeltaDistance = pointDeltaDistance;
            mXOffset = xOffset;
            mRandomRange = randomRange;
            mMaxMoveRange = maxMoveRange;
        }

        public List<Vector2> Generate(MapRegion region, float mapRadius, RandomWrapper r, TriangleNet.TrianglePool pool)
        {
            List<Vector2> spawnPoints = new List<Vector2>();

            var regionSize = region.size;
            int horizontalPointCount = Mathf.CeilToInt(regionSize.x / mPointDeltaDistance.x);
            int verticalPointCount = Mathf.CeilToInt(regionSize.y / mPointDeltaDistance.y);

            var regionStartPos = new Vector3(region.startPosition.x, 0, region.startPosition.y);
            for (int i = 0; i < verticalPointCount; ++i)
            {
                for (int j = 0; j < horizontalPointCount; ++j)
                {
                    float xOffset = (i % 2 == 0) ? 0 : mXOffset;
                    var pos = regionStartPos + mStartOffset + new Vector3(j * mPointDeltaDistance.x + xOffset, 0, i * mPointDeltaDistance.y);
                    if (region.IsInEmptySpace(pos, mMaxMoveRange))
                    {
                        if (region.IsInside(pos))
                        {
                            int maxTryCount = 5;
                            bool foundValidPoint = false;
                            for (int t = 0; t < maxTryCount; ++t)
                            {
                                var randomOffset = r.insideUnitCircle * mRandomRange;
                                var offsetPos = pos + new Vector3(randomOffset.x, 0, randomOffset.y);
                                if (region.IsInEmptySpace(offsetPos, mMaxMoveRange))
                                {
                                    spawnPoints.Add(new Vector2(offsetPos.x, offsetPos.z));
                                    foundValidPoint = true;
                                    break;
                                }
                            }

                            if (foundValidPoint == false)
                            {
                                //多次随机偏移后仍然未找到有效的点,直接使用未偏移的点
                                spawnPoints.Add(new Vector2(pos.x, pos.z));
                            }
                        }
                    }
                }
            }

            return spawnPoints;
        }

        public SpawnPointGenerateStrategyType strategy { get { return SpawnPointGenerateStrategyType.Grid; } }
        public Vector3 startOffset { get { return mStartOffset; } set { mStartOffset = value; } }
        public Vector2 pointDeltaDistance { get { return mPointDeltaDistance; } set { mPointDeltaDistance = value; } }
        public float xOffset { get { return mXOffset; } set { mXOffset = value; } }
        public float randomRange { get { return mRandomRange; } set { mRandomRange = value; } }

        Vector3 mStartOffset;
        Vector2 mPointDeltaDistance;
        float mXOffset;
        float mRandomRange;
        float mMaxMoveRange;
    }
}

#endif