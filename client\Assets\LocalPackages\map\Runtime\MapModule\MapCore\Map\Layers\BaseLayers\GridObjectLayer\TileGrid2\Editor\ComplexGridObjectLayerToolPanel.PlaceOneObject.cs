﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    public partial class ComplexGridObjectLayerToolPanel : EditorWindow
    {
        void DrawPlaceOneObjectGUI()
        {
            var setting = mLogic.layerData.objectPlacementSetting;

            setting.isGroup = EditorGUILayout.ToggleLeft("Is Group Object", setting.isGroup);
            if (!setting.isGroup)
            {
                mLogic.rotationSetting.Draw();
            }
            setting.considerObstacle = EditorGUILayout.ToggleLeft("Consider Obstacle", setting.considerObstacle);
            setting.addLOD = EditorGUILayout.ToggleLeft("Add LOD", setting.addLOD);
            setting.alignByGrid = EditorGUILayout.ToggleLeft("Align By Grid", setting.alignByGrid);
            if (setting.alignByGrid)
            {
                setting.alignGridSize = EditorGUILayout.FloatField("Grid Size", setting.alignGridSize);
                setting.alignGridSize = Mathf.Clamp(setting.alignGridSize, 0.1f, Map.currentMap.mapWidth);
            }   
        }

        ModelTemplate GetModelTemplate()
        {
            var assetPath = AssetDatabase.GetAssetPath(mLogic.prefab);
            if (assetPath.Length == 0)
            {
                return null;
            }
            return MapEditor.instance.CreateModelTemplate(assetPath, mLogic.prefab, false);
        }

        public void PlaceOneObject(int lod, Vector3 worldPos)
        {
            var setting = mLogic.layerData.objectPlacementSetting;

            if (setting.alignByGrid)
            {
                worldPos = mLogic.CalculatePosAlignedToGrid(worldPos);
            }

            var modelTemplate = GetModelTemplate();
            if (modelTemplate == null)
            {
                return;
            }
            var prefab = MapModuleResourceMgr.LoadPrefab(modelTemplate.GetLODPrefabPath(0));
            bool isReallyGroupObject = setting.isGroup;
            if (setting.isGroup)
            {
                if (prefab.transform.childCount <= 1)
                {
                    isReallyGroupObject = false;
                }
            }

            if (!isReallyGroupObject)
            {
                if (modelTemplate != null)
                {
                    var map = SLGMakerEditor.GetMap();
                    var layer = map.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;
                    var obj = layer.FindObjectAtExactSamePosition(lod, worldPos);
                    if (obj == null && layer.layerData.IsValidPosition(worldPos))
                    {
                        bool isOverlapWithObstacle = false;
                        var rot = mLogic.rotationSetting.rotation;
                        if (setting.considerObstacle)
                        {
                            isOverlapWithObstacle = mLogic.layer.CheckObstacle(modelTemplate.GetLODPrefabPath(0), worldPos, rot, Vector3.one);
                        }

                        if (!isOverlapWithObstacle)
                        {
                            float boundsWidth = modelTemplate.bounds.width;
                            float boundsHeight = modelTemplate.bounds.height;
                            float minX = worldPos.x - boundsWidth * 0.5f;
                            float minZ = worldPos.z - boundsHeight * 0.5f;
                            float maxX = worldPos.x + boundsWidth * 0.5f;
                            float maxZ = worldPos.z + boundsHeight * 0.5f;
                            int occupiedGridCount = layer.CalculateOccupiedGridCount(minX, minZ, maxX, maxZ);
                            var act = new ActionAddComplexGridModel(mLogic.layerID, map.nextCustomObjectID, modelTemplate.id, worldPos, rot, Vector3.one, occupiedGridCount, lod, modelTemplate.GetLODPrefabPath(0), mLogic.GetObjectTag(), false);
                            ActionManager.instance.PushAction(act);
                            mLogic.rotationSetting.StepToNextRandomRotation();
                        }
                    }
                }
            }
            else
            {
                PlaceOneGroupObject(lod, 0, worldPos);
            }
        }

        void PlaceOneGroupObject(int addToLOD, int prefabLOD, Vector3 worldPos)
        {
            var modelTemplate = GetModelTemplate();
            if (modelTemplate != null)
            {
                var setting = mLogic.layerData.objectPlacementSetting;

                bool addOtherLOD = CheckCanAddOtherLODs(modelTemplate);

                CompoundAction actions = new CompoundAction("Place One Group Objects");
                var map = SLGMakerEditor.GetMap();
                var layer = map.GetMapLayerByID(mLogic.layerID) as EditorComplexGridModelLayer;
                while (true)
                {
                    var groupPrefab = MapModuleResourceMgr.LoadPrefab(modelTemplate.GetLODPrefabPath(prefabLOD));
                    if (groupPrefab != null)
                    {
                        int nChildren = groupPrefab.transform.childCount;
                        for (int i = 0; i < nChildren; ++i)
                        {
                            var childTransform = groupPrefab.transform.GetChild(i);
                            var childPrefabInstance = childTransform.gameObject;

                            //获取prefab instance使用的prefab
                            var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);

                            var childPrefabAssetPath = AssetDatabase.GetAssetPath(childPrefab);
                            if (string.IsNullOrEmpty(childPrefabAssetPath))
                            {
                                Debug.LogError($"{modelTemplate.GetLODPrefabPath(0)} invalid child prefab {i}");
                            }
                            else
                            {
                                var objWorldPos = childTransform.position + worldPos;
                                var obj = layer.FindObjectAtExactSamePosition(addToLOD, objWorldPos);
                                if (obj == null && layer.layerData.IsValidPosition(objWorldPos))
                                {
                                    bool isOverlapWithObstacle = false;
                                    if (setting.considerObstacle)
                                    {
                                        isOverlapWithObstacle = mLogic.layer.CheckObstacle(childPrefabAssetPath, objWorldPos, childTransform.rotation, childTransform.localScale);
                                    }

                                    if (!isOverlapWithObstacle)
                                    {
                                        int objectID = map.nextCustomObjectID;
                                        var childModelTemplate = map.GetOrCreateModelTemplate(objectID, childPrefabAssetPath, false, false);

                                        //calculate bounds
                                        var childBounds = GameObjectBoundsCalculator.CalculateRect(childTransform.gameObject, false);
                                        var min = childBounds.min;
                                        var max = childBounds.max;

                                        childBounds.Set(min.x + worldPos.x, min.y + worldPos.z, max.x - min.x, max.y - min.y);

                                        float boundsWidth = childBounds.width;
                                        float boundsHeight = childBounds.height;
                                        float minX = objWorldPos.x - boundsWidth * 0.5f;
                                        float minZ = objWorldPos.z - boundsHeight * 0.5f;
                                        float maxX = objWorldPos.x + boundsWidth * 0.5f;
                                        float maxZ = objWorldPos.z + boundsHeight * 0.5f;
                                        int occupiedGridCount = layer.CalculateOccupiedGridCount(minX, minZ, maxX, maxZ);
                                        var act = new ActionAddComplexGridModel(mLogic.layerID, objectID, childModelTemplate.id, objWorldPos, childTransform.rotation, childTransform.localScale, occupiedGridCount, addToLOD, childModelTemplate.GetLODPrefabPath(0), mLogic.GetObjectTag(), false);
                                        actions.Add(act);

                                        //temp code
#if false
                                var boundsObj = new GameObject(childPrefabAssetPath + "_bounds");
                                var dp = boundsObj.AddComponent<DrawBounds>();
                                dp.bounds = Utils.RectToBounds(childBounds);
                                dp.worldSpace = true;
#endif
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        break;
                    }

                    if (addOtherLOD)
                    {
                        ++prefabLOD;
                        ++addToLOD;
                        if (addToLOD >= layer.lodCount || prefabLOD >= modelTemplate.lodCount)
                        {
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }

                if (!actions.IsEmpty())
                {
                    ActionManager.instance.PushAction(actions);
                }
            }
        }

        bool CheckCanAddOtherLODs(ModelTemplate modelTemplate)
        {
            if (mLogic.layerData.objectPlacementSetting.addLOD && modelTemplate.lodCount > 1)
            {
                var prefabLOD = modelTemplate.GetLODPrefabPath(0);
                var lod = Utils.GetPrefabNameLOD(prefabLOD);
                if (lod == 0 && mLogic.selectedLOD == 0)
                {
                    return true;
                }
            }
            return false;
        }
    }
}

#endif