﻿ 



 
 



#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Diagnostics;

namespace TFW.Map
{
    public enum SpawnPointGenerateStrategyType
    {
        //随机分布
        Random,
        //按格子分布
        Grid,
        //一个region生成一个点
        OnePointInRegion,
    }

    public partial class EntitySpawnLayer : MapLayerBase
    {
        public EntitySpawnLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }

            if (mLayerData != null)
            {
                mLayerData.OnDestroy();
                Map.currentMap.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }
        //加载地图的数据
        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool async)
        {
            var sourceLayer = layerData as config.EntitySpawnLayerData;
            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, sourceLayer.horizontalRegionCount, sourceLayer.verticalRegionCount, sourceLayer.regionSize.x, sourceLayer.regionSize.y, GridType.Rectangle, sourceLayer.origin);

            List<EntitySpawnRegion> regionData = new List<EntitySpawnRegion>();
            if (sourceLayer.regions != null)
            {
                int nRegions = sourceLayer.regions.Count;
                for (int i = 0; i < nRegions; ++i)
                {
                    int x = i % sourceLayer.horizontalRegionCount;
                    int y = i / sourceLayer.horizontalRegionCount;
                    var region = new EntitySpawnRegion(new Vector2Int(x, y), sourceLayer.regions[i].spawnPoints, sourceLayer.regions[i].visible, sourceLayer.regions[i].seed);
                    regionData.Add(region);
                }
            }

            mLayerData = new EntitySpawnLayerData(header, Map.currentMap, sourceLayer.regionSize, sourceLayer.npcMoveRange, sourceLayer.npcWaypoints, regionData, sourceLayer.strategy, sourceLayer.regionBrushIDs, sourceLayer.regionBrushes, sourceLayer.spawnPointReferenceNPCReginLayerName);
            mLayerView = new EntitySpawnLayerView(mLayerData);
            mLayerView.active = layerData.active;
            mLayerData.SetOnPixelsChangeCallback(mLayerView.OnSetPixels);

            Map.currentMap.AddMapLayer(this);
        }
        //卸载地图层
        public override void Unload()
        {
            Map.currentMap.RemoveMapLayerByID(id);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            int w = Mathf.CeilToInt(newWidth / mLayerData.tileWidth);
            int h = Mathf.CeilToInt(newHeight / mLayerData.tileHeight);
            mLayerData.Resize(w, h, useLayerOffset);
            return true;
        }

        public void GenerateSpawnPoints(bool generateVisibleRegions, float mapRadius)
        {
            Stopwatch watch = new Stopwatch();
            watch.Start();
            mLayerData.GenerateSpawnPoints(generateVisibleRegions, mapRadius);
            watch.Stop();
            UnityEngine.Debug.Log("Generate spawn points cost " + watch.ElapsedMilliseconds + " ms");
        }

        public float GetRegionEmptyArea(int regionIndex)
        {
            return mLayerData.GetRegionEmptyArea(regionIndex);
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            return false;
        }

        public void SetStrategy(ISpawnPointGenerationStrategy strategy)
        {
            mLayerData.SetStrategy(strategy);
        }

        public void Export(string folder, bool sortRegion)
        {
            if (string.IsNullOrEmpty(folder))
            {
                return;
            }

            var filePath = folder + "/map_npc_refresh_point.json";
            ExportSpawnPoints(filePath, sortRegion);

            var movePointPath = folder + "/map_npc_moving_point.tsv";
            ExportMoveInfo(movePointPath);
        }

        //table https://docs.google.com/spreadsheets/d/1VPzhSw186irtFopO3TEZ7udqVgJ35KpIcAD6aJgR794/edit#gid=0
        void ExportSpawnPoints(string path, bool sortRegion)
        {
            StopWatchWrapper w = new StopWatchWrapper();
            w.Start();
            List<object> zones = new List<object>();

            var wayPoints = layerData.entityMoveInfo.waypoints;
            float mapWidth = layerData.GetLayerWidthInMeter();
            float mapHeight = layerData.GetLayerHeightInMeter();

            int startZoneID = 13131001;
            int nRegions = layerData.regions.Count;
            int endZoneID = startZoneID + nRegions - 1;
            List<EntitySpawnRegion> regions = null;
            //if (MapModule.projectName == "k2")
            //{
            //    regions = SortSpiralOrder(layerData.regions, layerData.verticalTileCount, layerData.horizontalTileCount);
            //}
            //else
            {
                if (sortRegion)
                {
                    regions = layerData.GetSortedRegions();
                }
                else
                {
                    regions = layerData.regions;
                }
            }

            var npcRegionLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NPC_REGION) as NPCRegionLayer;
            int layerIndex = -1;
            if (npcRegionLayer != null)
            {
                layerIndex = npcRegionLayer.layerData.GetLayerIndex(mLayerData.spawnPointTypeReferenceNPCRegionLayerName);
            }
            var cityTerritoryLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY) as EditorTerritoryLayer;

            for (int i = 0; i < nRegions; ++i)
            {
                Dictionary<string, object> zone = new Dictionary<string, object>();
                List<int[]> coordinates = new List<int[]>();
                //points type是npc region layer中的type值
                List<int> pointsType = new List<int>();
                //每个刷新点属于哪个city territory
                //List<int> pointsCityTerritoryID = new List<int>();
                
                var spawnPoints = regions[i].spawnPoints;
                for (int p = 0; p < spawnPoints.Count; ++p)
                {
                    var coord = new int[2] { (int)(spawnPoints[p].x * 1000), (int)(spawnPoints[p].y * 1000) };
                    coordinates.Add(coord);

                    int pointType = 0;
                    if (layerIndex >= 0)
                    {
                        pointType = npcRegionLayer.layerData.GetLayerTileTypeByPosition(layerIndex, spawnPoints[p].x, spawnPoints[p].y);
                    }
                    pointsType.Add(pointType);
                    //if (cityTerritoryLayer != null)
                    //{
                    //    int territoryID = cityTerritoryLayer.GetGridData(new Vector3(spawnPoints[p].x, 0, spawnPoints[p].y));
                    //    pointsCityTerritoryID.Add(territoryID);
                    //}
#if false
                    //temp code,检查是否有npc移动点出地图边界
                    for (int k = 0; k < wayPoints.Count; ++k)
                    {
                        var x = spawnPoints[p].x + wayPoints[k].x;
                        var z = spawnPoints[p].y + wayPoints[k].y;
                        if (x <= 0 || x >= mapWidth ||
                            z <= 0 || z >= mapHeight)
                        {
                            int a = 1;
                        }
                    }
#endif
                }
                zone["zone"] = GetZoneID(startZoneID, endZoneID, regions[i].coord);
                zone["points"] = coordinates;
                if (layerIndex >= 0)
                {
                    zone["points_type"] = pointsType;
                }
                //if (cityTerritoryLayer != null)
                //{
                //    zone["points_city_territory_id"] = pointsCityTerritoryID;
                //}

                zones.Add(zone);
            }

            var str = JSONParser.Serialize(zones);
            File.WriteAllText(path, str);

            var t = w.Stop();
            UnityEngine.Debug.Log($"Export spawn points cost: {t}");
        }

        int GetZoneID(int startZoneID, int endZoneID, Vector2Int zoneCoord)
        {
            var idx = zoneCoord.y * mLayerData.horizontalTileCount + zoneCoord.x;
            return startZoneID + idx;
        }

        void ExportMoveInfo(string path)
        {
            StringBuilder builder = new StringBuilder();

            string[] headers = null;
            if (MapModule.useNewConfigFormat)
            {
                headers = new string[] {
                "S_INT_id",
                "S_ARR_Coord_moving_point",
            };
            }
            else
            {
                headers = new string[] {
                "S_INT_id",
                "S_ARR_moving_point",
            };
            }

            for (int i = 0; i < headers.Length; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Length - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            var moveInfo = layerData.entityMoveInfo;
            var moveInfoStr = GetMoveInfoStr(moveInfo);

            builder.AppendFormat("{0}\t", 13221001);
            //move points                    
            builder.AppendFormat("{0}\n", moveInfoStr);

            var str = builder.ToString();
            File.WriteAllText(path, str);
        }

        string FormatVector2(Vector2 v)
        {
            int x = Mathf.FloorToInt(v.x * 1000);
            int z = Mathf.FloorToInt(v.y * 1000);
            return "{\"x\":" + x + ",\"z\":" + z + "}";
        }

        string GetMoveInfoStr(EntityMoveInfo info)
        {
            StringBuilder build = new StringBuilder();
            build.Append("[");
            int n = info.waypoints.Count;
            for (int i = 0; i < n; ++i)
            {
                build.Append(FormatVector2(info.waypoints[i]));
                if (i != n - 1)
                {
                    build.Append(",");
                }
            }
            build.Append("]");
            return build.ToString();
        }

        EntitySpawnRegion GetRegion(List<EntitySpawnRegion> regions, int x, int y, int horizontalRegionCount)
        {
            return regions[y * horizontalRegionCount + x];
        }

        List<EntitySpawnRegion> SortSpiralOrder(List<EntitySpawnRegion> unsortedRegions, int rows, int cols)
        {
            UnityEngine.Debug.Assert(rows == cols);
            SortedDictionary<int, EntitySpawnRegion> sortedRegions = new SortedDictionary<int, EntitySpawnRegion>();

            int priority = 0;
            int loopCount = Mathf.CeilToInt(rows / 2.0f);
            for (int k = 0; k < loopCount; ++k)
            {
                //bottom
                for (int x = k; x < rows - k; ++x)
                {
                    int xx = x;
                    int yy = k;
                    sortedRegions.Add(priority, GetRegion(unsortedRegions, xx, yy, rows));
                    //Debug.Log($"region {xx}_{yy}, priority {priority}");
                    ++priority;
                }

                //right
                for (int y = k + 1; y < rows - k; ++y)
                {
                    int xx = rows - 1 - k;
                    int yy = y;
                    sortedRegions.Add(priority, GetRegion(unsortedRegions, xx, yy, rows));
                    //Debug.Log($"region {xx}_{yy}, priority {priority}");
                    ++priority;
                }

                //top
                for (int x = rows - 1 - k - 1; x >= k; --x)
                {
                    int xx = x;
                    int yy = rows - 1 - k;
                    sortedRegions.Add(priority, GetRegion(unsortedRegions, xx, yy, rows));
                    //Debug.Log($"region {xx}_{yy}, priority {priority}");
                    ++priority;
                }

                //left
                for (int y = rows - 1 - k - 1; y > k; --y)
                {
                    int xx = k;
                    int yy = y;
                    sortedRegions.Add(priority, GetRegion(unsortedRegions, xx, yy, rows));
                    //Debug.Log($"region {xx}_{yy}, priority {priority}");
                    ++priority;
                }
            }

            List<EntitySpawnRegion> results = new List<EntitySpawnRegion>(sortedRegions.Count);
            foreach (var p in sortedRegions)
            {
                results.Add(p.Value);
            }
            return results;
        }

        public int horizontalRegionCount { get { return mLayerData.horizontalTileCount; } }
        public int verticalRegionCount { get { return mLayerData.verticalTileCount; } }
        //地图层的id
        public override int id { get { return mLayerData.id; } }
        //地图层的root gameobject
        public override GameObject gameObject { get { return mLayerView.root; } }
        public override bool Contains(int objectID) { return false; }
        public override int GetCurrentLOD() { return 0; }
        public override void RefreshObjectsInViewport()
        {
        }
        public override Vector3 layerOffset => mLayerData.layerOffset;

        //返回地图层的总宽度
        public override float GetTotalWidth() { return mLayerData.GetLayerWidthInMeter(); }
        //返回地图层的总高度
        public override float GetTotalHeight() { return mLayerData.GetLayerHeightInMeter(); }
        //地图层的名称
        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public bool isCreated { get { return mLayerData.isCreated; } }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        public EntitySpawnLayerData layerData { get { return mLayerData; } }
        public EntitySpawnLayerView layerView { get { return mLayerView; } }

        EntitySpawnLayerData mLayerData;
        EntitySpawnLayerView mLayerView;
    }
}
#endif