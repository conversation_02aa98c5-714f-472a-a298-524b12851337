﻿ 



 
 


using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryImporterV2
    {
        config.MapLayerData LoadVaryingTileSizeTerrainLayerData(BinaryReader reader)
        {
            long pos = GetSectionDataStartPosition(MapDataSectionType.VaryingTileSizeTerrainLayer);
            if (pos < 0)
            {
                return null;
            }
            reader.BaseStream.Position = pos;

            int version = reader.ReadInt32();

            bool useLayer = reader.ReadBoolean();
            if (!useLayer)
            {
                return null;
            }

            //-------------------version 1 start------------------------------
            var layerID = reader.ReadInt32();
            var name = Utils.ReadString(reader);
            var offset = Utils.ReadVector3(reader);

            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();
            bool useGeneratedLOD = reader.ReadBoolean();
            var layerPosition = Utils.ReadVector3(reader);

            int bigTileCount = reader.ReadInt32();
            var bigTiles = new config.VaryingTileSizeTerrainLayerBigTileData[bigTileCount];
            for (int i = 0; i < bigTileCount; ++i)
            {
                bigTiles[i] = new config.VaryingTileSizeTerrainLayerBigTileData();
                bigTiles[i].x = reader.ReadInt32();
                bigTiles[i].y = reader.ReadInt32();
                bigTiles[i].width = reader.ReadInt32();
                bigTiles[i].height = reader.ReadInt32();
            }

            var objects = new config.VaryingTileSizeTerrainTileData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var idx = i * cols + j;
                    objects[idx] = LoadVaryingTileSizeTerrainTileDataV1(reader, layerID);
                }
            }

            var config = LoadVaryingTileSizeTerrainLayerLODConfigV1(reader);
            //-------------------version 1 end-----------------------
           
            var layer = new config.VaryingTileSizeTerrainLayerData(layerID, name, offset, config, rows, cols, tileWidth, tileHeight, objects, bigTiles, useGeneratedLOD, layerPosition);
            return layer;
        }

        config.VaryingTileSizeTerrainTileData LoadVaryingTileSizeTerrainTileDataV1(BinaryReader reader, int layerID)
        {
            bool hasTile = reader.ReadBoolean();
            if (!hasTile)
            {
                return null;
            }

            var tileID = reader.ReadInt32();
            var templateID = reader.ReadInt32();
            var type = reader.ReadInt32();
            var index = reader.ReadInt32();
            int bigTileIndex = reader.ReadInt32();

            //tileID is not used in runtime
            var tileData = new config.VaryingTileSizeTerrainTileData(tileID, layerID, type, index, 0, templateID, bigTileIndex);
            return tileData;
        }

        config.MapLayerLODConfig LoadVaryingTileSizeTerrainLayerLODConfigV1(BinaryReader reader)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                bool hideObject = reader.ReadBoolean();
                int shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                var flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                var terrainLODTileCount = reader.ReadInt32();
                config.lodConfigs[i] = new config.MapLayerLODConfigItem("", zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, terrainLODTileCount);
            }
            return config;
        }
    }
}
