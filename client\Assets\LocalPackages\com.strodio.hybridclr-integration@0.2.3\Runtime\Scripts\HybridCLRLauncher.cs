﻿using Cysharp.Threading.Tasks;
using K3;
using Logic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ThinkingAnalytics;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.UI;


namespace HybridCLRIntegration
{
    public class HybridCLRLauncher : MonoBehaviour
    {
        private const int MaxRetryCount = 3;
        private const int RetryDelayMilliseconds = 3000; // 3 seconds

        [SerializeField]
        private LauncherConfig _config;

        [SerializeField]
        private Slider _progress;

        [SerializeField]
        private Text _progressTitle;

        private AssemblyLoader _assemblyLoader;
        private int _assembly0OrMetadata1;

        private bool OpenLog;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSplashScreen)]
        private static void BootStrap()
        {
            Addressables.InitializeAsync();
        }

        async UniTaskVoid Start()
        {

            if (SDKManager.instance != null)
            {
                //初始化SDKManager
            }

#if INTERNAL_ENTRANCE || DEFAULT_ENV_BETA || UNITY_EDITOR 
            OpenLog = true;
#else
        OpenLog = false; 
#endif

#if USE_LOG
     OpenLog=true;
#endif
            GameObject.Find("Reporter").SetActive(OpenLog);

            Application.targetFrameRate = 60;
             
            if (PlayerPrefs.GetInt("launchgame_new_firsttime", 0) == 0)
            {
                K3GameEvent.I.BiLogLoginFunnel("custom_loss", "launchgame_new_firsttime");
                PlayerPrefs.SetInt("launchgame_new_firsttime", 1);
                PlayerPrefs.Save();
            }


            K3GameEvent.I.BiLogLoginFunnel("custom_loss", "launch_game");
            K3GameEvent.I.BiLogLoginFunnel("custom_loss", "logo_display");

#if USE_SDK
        InitTa();//数数SDK初始化
#endif

            Loading_Res.CurLoadingProcess = LogoLoadingProcessEnum.GameQualityMgr;
             
            GameQualityMgr.I.Init();

            GameQualityMgr.I.DetectQuality(true);

            K3GameEvent.I.TaLog(new LoginEvent() { EventKey = "hotfix_start" });

            await Addressables.InitializeAsync();
              
#if !UNITY_EDITOR
            await StartAsync();
#endif
            K3GameEvent.I.TaLog(new LoginEvent() { EventKey = "hotfix_end" });

            Loading_Res.CurLoadingProcess = LogoLoadingProcessEnum.AddressablesInit;
            
            await Addressables.LoadSceneAsync(this._config.targetLaunchScene);
        }

        #region TA



        public void InitTa()
        {
#if USE_SDK
            GameObject.Find("SDKManager").AddComponent(typeof(ThinkingAnalyticsAPI));
            
#if UNITY_EDITOR || INTERNAL_ENTRANCE
        ThinkingAnalyticsAPI.TAMode mode = ThinkingAnalyticsAPI.TAMode.DEBUG_ONLY;
#else
        ThinkingAnalyticsAPI.TAMode mode = ThinkingAnalyticsAPI.TAMode.NORMAL;
#endif

        ThinkingAnalyticsAPI.TATimeZone timeZone = ThinkingAnalyticsAPI.TATimeZone.Local;
        ThinkingAnalyticsAPI.Token token = new ThinkingAnalyticsAPI.Token(K3GameEvent.taAppId, K3GameEvent.taServerUrl, mode, timeZone);
        ThinkingAnalyticsAPI.StartThinkingAnalytics(token);
        ThinkingAnalyticsAPI.EnableLog(true);
        ThinkingAnalyticsAPI.EnableAutoTrack(AUTO_TRACK_EVENTS.ALL);

#endif



        }
        #endregion



        private async UniTask StartAsync()
        {
            this._assemblyLoader = new AssemblyLoader(this._config);
            this._assembly0OrMetadata1 = 0;
            var (isOk, assemblies) =
                await this._assemblyLoader.LoadAssembliesAsyncWithRetry(MaxRetryCount, RetryDelayMilliseconds);

            if (!isOk)
            {
                Debug.LogError("Failed to load assemblies.");
                await UniTask.Delay(TimeSpan.FromMilliseconds(RetryDelayMilliseconds));
                _ = this.StartAsync();
                return;
            }

            this._assembly0OrMetadata1 = 1;
            await this._assemblyLoader.LoadMetadataForAOTAssemblyAsyncWithRetry(MaxRetryCount, RetryDelayMilliseconds);

            var methods = GetRuntimeInitializeMethods(assemblies);
            foreach (var method in methods.SelectMany(x => x.Value))
            {
                method.Invoke(null, null);
            }

            await Addressables.LoadContentCatalogAsync($"{Addressables.RuntimePath}/catalog.json"); 
        }

        private void Update()
        {
            if (this._assemblyLoader == null || !this._assemblyLoader.loadStatus.HasValue) return;
            if (this._progressTitle == null && this._progress == null) return;

            var loadStatus = this._assemblyLoader.loadStatus.Value;
            var suffixStr = this._assembly0OrMetadata1 == 0 ? "Assemblies" : "Metadata";

            var titleStr = string.Empty;
            var progressValue = 0f;

            switch (loadStatus.loadPhase)
            {
                case LoadPhase.Loading:
                    var sizeSuffix = loadStatus.totalBytes > 0 ? $"({BytesToMegabytes(loadStatus.downloadedBytes)}MB/{BytesToMegabytes(loadStatus.totalBytes)}MB)" : string.Empty;
                    titleStr = loadStatus.isDownloadDone
                        ? $"Loading {suffixStr}..."
                        : $"Downloading {suffixStr}...{sizeSuffix}";
                    progressValue = loadStatus.isDownloadDone
                        ? loadStatus.percentComplete
                        : loadStatus.downloadedPercent;
                    break;
                case LoadPhase.WaitingRetry:
                    titleStr = $"Failed to load {suffixStr} and waiting for retry...";
                    break;
                case LoadPhase.Failed:
                    titleStr = $"Failed to load {suffixStr}, will retry...";
                    break;
            }

            this._progressTitle.text = titleStr;
            this._progress.value = progressValue;
        }

        private static Dictionary<RuntimeInitializeLoadType, List<MethodInfo>> GetRuntimeInitializeMethods(
            IEnumerable<Assembly> assemblies)
        {
            var result = assemblies
                .SelectMany(assembly => assembly.GetTypes())
                .SelectMany(type => type.GetMethods(BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public))
                .SelectMany(method => method.GetCustomAttributes<RuntimeInitializeOnLoadMethodAttribute>(false),
                    (method, attribute) => new { method, attribute })
                .GroupBy(x => x.attribute.loadType)
                .ToDictionary(g => g.Key, g => g.Select(x => x.method).ToList());

            var sortedLoadTypes = new List<RuntimeInitializeLoadType>
            {
                RuntimeInitializeLoadType.SubsystemRegistration,
                RuntimeInitializeLoadType.AfterAssembliesLoaded,
                RuntimeInitializeLoadType.BeforeSplashScreen,
                RuntimeInitializeLoadType.BeforeSceneLoad,
                RuntimeInitializeLoadType.AfterSceneLoad
            };

            return result.OrderBy(kv => sortedLoadTypes.IndexOf(kv.Key)).ToDictionary(kv => kv.Key, kv => kv.Value);
        }

        private static double BytesToMegabytes(long bytes)
        {
            const double bytesPerMegabyte = 1024 * 1024;
            double megabytes = bytes / bytesPerMegabyte;
            return Math.Round(megabytes, 2);
        }
    }

}