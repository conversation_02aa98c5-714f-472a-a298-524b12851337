﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class CircleBorderLayerView : ModelLayerView
    {
        public CircleBorderLayerView(CircleBorderLayerData layerData, bool asyncLoading)
        : base(layerData, asyncLoading)
        {
            //todo 注意,必须把lod缓冲设置为0
            if (!Map.currentMap.isEditorMode)
            {
                float zoom = layerData.lodConfig.GetLODChangeZoom(layerData.combineBorderLOD, false);
                mCrossfadeLowerHeight = Map.currentMap.CalculateCameraHeightFromZoom(zoom);
                //temp code
                mCrossfadeUpperHeight = mCrossfadeLowerHeight + 400;
            }
            else
            {
                mCrossfadeUpperHeight = 0;
            }
        }

        public override void SetZoom(float zoom, bool lodChanged)
        {
            foreach (var pair in mViews)
            {
                var view = pair.Value as CircleBorderView;
                view.SetZoom(zoom, lodChanged);
            }
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new CircleBorderView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public float crossFadeLowerHeight { get { return mCrossfadeLowerHeight; } }
        public float crossFadeUpperHeight { get { return mCrossfadeUpperHeight; } }

        float mCrossfadeLowerHeight;
        float mCrossfadeUpperHeight;
    }
}
