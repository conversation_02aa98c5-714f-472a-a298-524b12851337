﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveGridRegionSetting(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.GridRegionSetting, writer);

            writer.Write(VersionSetting.GridRegionSettingStructVersion);

            //-------------------version 1 start------------------------------
            var gridRegionEditor = (Map.currentMap.data as EditorMapData).gridRegionEditor;
            //检测到底是否使用了region编辑
            bool hasRegionData = gridRegionEditor.HasRegionData();
            writer.Write(hasRegionData);
            if (hasRegionData)
            {
                var grids = gridRegionEditor.grids;
                writer.Write(gridRegionEditor.gridWidth);
                writer.Write(gridRegionEditor.gridHeight);
                writer.Write(gridRegionEditor.horizontalGridCount);
                writer.Write(gridRegionEditor.verticalGridCount);

                byte[] gridData = Utils.ConvertToByteGrids(grids);
                int shortCount = gridData.Length / 2;
                writer.Write(shortCount);
                for (int i = 0; i < gridData.Length; ++i)
                {
                    writer.Write(gridData[i]);
                }

                //save grid templates
                var templates = gridRegionEditor.templates;
                int n = templates.Count;
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    Utils.WriteColor32(writer, templates[i].color);
                    writer.Write((byte)templates[i].type);
                }
            }
            //-------------------version 1 end------------------------------
        }

    }
}

#endif