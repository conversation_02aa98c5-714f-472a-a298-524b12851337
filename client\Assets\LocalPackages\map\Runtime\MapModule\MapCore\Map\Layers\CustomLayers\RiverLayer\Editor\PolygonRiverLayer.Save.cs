﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class PolygonRiverLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.PolygonRiverLayerEditorDataVersion);

            var editorMapData = Map.currentMap.data as EditorMapData;
            SaveLayerData(writer);

            FlushMaskTextures();

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayerData(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(displayVertexRadius);
            writer.Write(bakedTextureSize);
            writer.Write(onlyGenerateLOD0Assets);
            writer.Write(layerData.useUV2);
            writer.Write(layerData.generateOBJ);
            Utils.WriteIntList(writer, mGroundTileTypeClipExceptions);
            writer.Write(layerData.expandingTileCount);
            Utils.WriteString(writer, layerData.riverMaskTextureSaveFolderPath);

            var allObjects = layerData.objects;
            int n = allObjects.Count;
            writer.Write(n);

            //local id exporter
            IDExporter idExporter = new IDExporter();

            var map = Map.currentMap as EditorMap;
            PolygonRiverMaterialSaver.PrepareSaveRiverMaterial(map.riverMaterialsFolder);
            foreach (var p in allObjects)
            {
                var objData = p.Value;
                var riverID = p.Key;
                idExporter.Export(writer, riverID);
                SavePolygonRiverData(writer, objData as PolygonRiverData);
            }

            //save river prefab infos
            Dictionary<int, string> riverPrefabInfo = riverPrefabInfos;
            writer.Write(riverPrefabInfo.Count);
            foreach (var pair in riverPrefabInfo)
            {
                //保存每一段河流的prefab信息
                int sectionID = pair.Key;
                string prefabPath = pair.Value;
                int sectionIndex;
                PolygonRiverData river = GetRiverFromSectionID(sectionID, out sectionIndex);
                //河流id
                idExporter.Export(writer, river.id);
                //section index
                writer.Write(sectionIndex);
                //prefab路径
                Utils.WriteString(writer, prefabPath);
            }

            //save map layer lod config
            SaveMapLayerLODConfig(writer, GetLayerData());
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                writer.Write((int)0);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }

        static void SavePolygonRiverData(BinaryWriter writer, PolygonRiverData data)
        {
            var map = Map.currentMap as EditorMap;
            if (map.saveRiverMaterials)
            {
                var riverLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER) as PolygonRiverLayer;
                PolygonRiverMaterialSaver.SaveRiverMaterials(riverLayer, data, map.riverMaterialsFolder);
            }

            //save outline
            var bottomOutline = data.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
            int n = bottomOutline.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var pos = bottomOutline[i];
                Utils.WriteVector2(writer, new Vector2(pos.x, pos.z));
            }

            //save sections
            var sections = data.sections;
            int nSections = sections.Count;
            writer.Write(nSections);
            for (int i = 0; i < nSections; ++i)
            {
                var si = sections[i];
                //save texture
                var textureData = Utils.ColorToColor32Array(si.textureData);
                Utils.WriteColor32Array(writer, textureData);
                //save outline
                Utils.WriteVector3List(writer, si.outlineRaw);
            }

            //save splitters
            int nSplitters = data.splitters.Count;
            writer.Write(nSplitters);
            for (int i = 0; i < nSplitters; ++i)
            {
                Utils.WriteVector3(writer, data.splitters[i].startVertexPosition);
                Utils.WriteVector3(writer, data.splitters[i].endVertexPosition);
            }

            var radius = data.displayRadius;
            writer.Write(radius);
            writer.Write(data.textureSize);
            //save guid
            var guid = AssetDatabase.AssetPathToGUID(data.materialPath);
            Utils.WriteString(writer, guid);
            writer.Write(data.hideLOD);

            writer.Write(data.height);
            writer.Write(data.generateRiverMaterial);
        }
    }
}

#endif