﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map
{
    //占位符模型,当模型未加载完毕的时候使用
    public class ModelPlaceholder : ModelBase
    {
        public void Init(Vector3 position, Vector3 scale, Quaternion rotation)
        {
            mPosition = position;
            mRotation = rotation;
            mScaling = scale;
        }

        public override void Release()
        {
            ReleaseToPool(this);
        }

        public static ModelPlaceholder Require(Vector3 position, Vector3 scale, Quaternion rotation)
        {
            var model = mPool.Require();
            model.Init(position, scale, rotation);
            return model;
        }

        static void ReleaseToPool(ModelPlaceholder model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        public override Vector3 position { get { return mPosition; } }
        public override Quaternion rotation { get { return mRotation; } }
        public override Vector3 scaling { get { return mScaling; } }

        Vector3 mPosition;
        Quaternion mRotation;
        Vector3 mScaling;

        static ObjectPool<ModelPlaceholder> mPool = new ObjectPool<ModelPlaceholder>(1000, () => new ModelPlaceholder());
    }
}
