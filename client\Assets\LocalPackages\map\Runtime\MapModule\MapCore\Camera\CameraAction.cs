﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [System.Flags]
    public enum CameraMoveAxis
    {
        //不移动
        None = 0,
        //xz方向移动
        Horizontal = 1,
        //y方向移动
        Vertical = 2,
        //xyz移动
        All = Horizontal | Vertical,
    }

    //用于排序相机
    public class ComapreCameraAction : IComparer<CameraAction>
    {
        public int Compare(CameraAction a, CameraAction b)
        {
            return a.updateOrder - b.updateOrder;
        }
    }

    //相机的移动行为
    public abstract class CameraAction
    {
        public CameraAction(CameraActionType type)
        {
            enabled = false;
            affectViewCenter = true;
            moveAxis = CameraMoveAxis.All;
            updateOrder = MapCameraMgr.GetActionUpdateOrder(type);
            actionType = type;
            ownAxis = CameraMoveAxis.All;
            isFinished = true;
            ignoreAxis = false;
        }

        //被删除时调用
        public virtual void OnDestroy() { }
        //切换camera setting时调用
        public virtual void OnCameraSettingChange(MapCameraSetting newSetting) { }
        //相机更新,是否enable都会更新
        public void Update(Vector3 currentCameraPos, System.Action<CameraAction, int> updateCallback)
        {
            if (updateCallback != null)
            {
                updateCallback(this, MapTouchManager.touchCount);
            }

            UpdateImpl(currentCameraPos);
        }

        protected abstract void UpdateImpl(Vector3 currentCameraPos);
        //是否enable都会调用
        public virtual void PostUpdate() {
        }
        //相机行为结束时调用
        public void OnFinish()
        {
            if (mFinishedCallback != null)
            {
                mFinishedCallback();
            }
            OnFinishImpl();
        }
        //被其他camera action打断时调用
        public virtual void OnInterrupted(CameraActionType type)
        {
            if (mInterruptedCallback != null)
            {
                mInterruptedCallback(type);
            }
        }

        public abstract void OnFinishImpl();
        public virtual string GetDebugMessage() { return ""; }
        //计算当前帧相机的位置
        public abstract Vector3 GetTargetPosition();
        //是否影响地图物体的刷新,true表示地图的视野框会改变
        public bool affectViewCenter { set; get; }
        //相机可以移动的轴
        public CameraMoveAxis moveAxis { get; protected set; }
        //camera action独占的轴,轴的拥有者才能移动
        public CameraMoveAxis ownAxis { set; get; }
        //是否忽略相机轴,如果忽略,则不管axis被谁拥有,这个action都会执行
        public bool ignoreAxis { set; get; }
        //相机行为执行顺序,高的覆盖低的
        public CameraActionType actionType { get; set; }
        public int updateOrder { get; set; }
        //一帧内调用的顺序
        public int callOrder { get; set; }
        public bool enabled
        {
            set
            {
                mEnabled = value;
                if (mEnabled)
                {
                    isFinished = false;
                }
            }
            get
            {
                return mEnabled;
            }
        }
        //是否action结束
        public bool isFinished
        {
            set
            {
                mIsFinished = value;
                if (mIsFinished)
                {
                    mEnabled = false;
                }
                else
                {
                    mEnabled = true;
                }
            }
            get
            {
                return mIsFinished;
            }
        }

        //被其他camerea action打断时调用的callback
        public System.Action<CameraActionType> interrupedCallback { set { mInterruptedCallback = value; } get { return mInterruptedCallback; } }
        //相机移动完成时调用的callback
        public System.Action finishedCallback { set { mFinishedCallback = value; } get { return mFinishedCallback; } }
        //是否block更新顺序更高的camera action
        public bool blockOtherActions { get { return mBlockOtherActions; } set { mBlockOtherActions = value; } }

        bool mEnabled = false;
        bool mIsFinished = false;
        bool mBlockOtherActions = false;
        System.Action<CameraActionType> mInterruptedCallback;
        System.Action mFinishedCallback;
    }
}