﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CreateRiver : RiverEditorTool
    {
        public CreateRiver(RiverEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onAddCollision = OnAddRiver;
        }

        public override void OnDestroy()
        {
        }

        public override void OnDisabled()
        {
            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                Material mtl = mEditor.layer.defaultRiverMaterial;
                if (mtl == null)
                {
                    //EditorUtility.DisplayDialog("Error", "select river material!", "OK");
                }
                else
                {
                    mLeftButtonDown = true;
                }
            }

            if (e.button == 1 && e.type == EventType.MouseUp)
            {
                //press right key
                mCanCreate = true;
                AddRiver();
                mEditor.RepaintGUI();
            }

            if (e.button == 0 && e.type == EventType.MouseUp)
            {
                mCanCreate = true;
                mLeftButtonDown = false;
            }

            if (mLeftButtonDown && mCanCreate)
            {
                mCanCreate = false;

                mAddedVertices.Add(pos);
                if (mAddedVertices.Count == 1)
                {
                    mAddedVertices.Add(pos);
                }
                mDisplayedVertices = mAddedVertices.ToArray();
                mEditor.RepaintScene();
            }

            if (e.type == EventType.MouseMove)
            {
                if (mAddedVertices.Count > 0)
                {
                    mAddedVertices[mAddedVertices.Count - 1] = pos;
                    mDisplayedVertices[mDisplayedVertices.Length - 1] = pos;
                }

                mEditor.RepaintScene();
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void OnEnabled()
        {
        }

        void OnAddRiver(int dataID)
        {
            var data = Map.currentMap.FindObject(dataID) as PolygonRiverData;
            mEditor.SetSelection(data, 0);
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
            mEditor.RepaintGUI();
        }

        public bool AddRiver()
        {
            bool created = mAddedVertices.Count > 3;
            if (created)
            {
                Material mtl = mEditor.layer.defaultRiverMaterial;
                if (mtl != null)
                {
                    var mtlPath = AssetDatabase.GetAssetPath(mtl);
                    if (!string.IsNullOrEmpty(mtlPath) && mtlPath.EndsWith(".mat"))
                    {
                        mAddedVertices.RemoveAt(mAddedVertices.Count - 1);
                        var dataID = Map.currentMap.nextCustomObjectID;
                        var action = new ActionAddPolygonRiver(mEditor.layer.id, dataID, mAddedVertices, AssetDatabase.GetAssetPath(mtl), mEditor.logic.riverHeight, MapModule.generateRiverMaterial) ;
                        ActionManager.instance.PushAction(action);
                    }
                }
                else
                {
                    //Debug.LogError("select river material!");
                    created = false;
                }
            }

            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];

            return created;
        }

        public override void DrawScene()
        {
            Handles.DrawPolyLine(mDisplayedVertices);
        }

        protected override void DrawGUIImpl()
        {
            int shaderLOD = EditorGUILayout.IntField(new GUIContent("Shader LOD", "设置当前river shader的maximum lod值"), mEditor.layer.GetShaderLOD());
            mEditor.layer.SetShaderLOD(shaderLOD);

            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "河流顶点的显示大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }
        }

        public override void DrawMenu() {
            if (mAddedVertices.Count == 0)
            {
                var e = Event.current;
                var camera = Map.currentMap.camera;
                var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, camera.firstCamera);
                var pos = Map.currentMap.FromScreenToWorldPosition(screenPos);

                GenericMenu menu = new GenericMenu();
                menu.AddItem(new GUIContent("Create Rectangle River"), false, AddRectangleRiver, pos);
                menu.ShowAsContext();
            }
        }

        List<Vector3> CreateRectangleVertices(Vector3 center, float width, float height)
        {
            float minX = center.x - width * 0.5f;
            float maxX = center.x + width * 0.5f;
            float minZ = center.z - height * 0.5f;
            float maxZ = center.z + height * 0.5f;
            List<Vector3> vertices = new List<Vector3>()
            {
                new Vector3(minX, 0, minZ),
                new Vector3(minX, 0, maxZ),
                new Vector3(maxX, 0, maxZ),
                new Vector3(maxX, 0, minZ),
            };

            return vertices;
        }

        void AddRectangleRiver(object pos)
        {
            Material mtl = mEditor.layer.defaultRiverMaterial;
            if (mtl == null)
            {
                EditorUtility.DisplayDialog("Error", "Select a material", "OK");
                return;
            }

            System.Func<List<InputDialog.Item>, bool> onClickAdd = (List<InputDialog.Item> texts) =>
            {
                string xStr = (texts[0] as InputDialog.StringItem).text;
                string zStr = (texts[1] as InputDialog.StringItem).text;
                string widthStr = (texts[2] as InputDialog.StringItem).text;
                string heightStr = (texts[3] as InputDialog.StringItem).text;
                float width;
                bool suc = Utils.ParseFloat(widthStr, out width);
                if (!suc)
                {
                    return false;
                }
                float height;
                suc = Utils.ParseFloat(heightStr, out height);
                if (!suc)
                {
                    return false;
                }
                if (width <= 0 || height <= 0)
                {
                    return false;
                }

                float x;
                suc = Utils.ParseFloat(xStr, out x);
                if (!suc)
                {
                    return false;
                }
                float z;
                suc = Utils.ParseFloat(zStr, out z);
                if (!suc)
                {
                    return false;
                }

                var vertices = CreateRectangleVertices(new Vector3(x, 0, z), width, height);
                var action = new ActionAddPolygonRiver(mEditor.layer.id, Map.currentMap.nextCustomObjectID, vertices, AssetDatabase.GetAssetPath(mtl), mEditor.logic.riverHeight, MapModule.generateRiverMaterial);
                ActionManager.instance.PushAction(action);

                return true;
            };

            Vector3 pos3 = (Vector3)pos;
            var window = EditorWindow.GetWindow<InputDialog>("Input Rectangle Parameters");
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("X", "", pos3.x.ToString()),
                new InputDialog.StringItem("Z", "", pos3.z.ToString()),
                new InputDialog.StringItem("Width", "", "45"),
                new InputDialog.StringItem("Height", "", "45"),
                };
            window.Show(items, onClickAdd);
            window.minSize = new Vector2(200, 100);
            window.maxSize = new Vector2(300, 100);
            var position = window.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            window.position = position;
        }

        public override RiverEditorToolType type { get { return RiverEditorToolType.Create; } }

        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
        bool mCanCreate = true;
        bool mLeftButtonDown = false;
    }
}


#endif