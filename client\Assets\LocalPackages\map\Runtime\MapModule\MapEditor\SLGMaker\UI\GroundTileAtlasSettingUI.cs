﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    [CustomEditor(typeof(GroundTileAtlasSetting))]
    public partial class GroundTileAtlasSettingUI : Editor
    {
        public override void OnInspectorGUI()
        {
            var obj = target as GroundTileAtlasSetting;
            mDirty = false;

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Create"))
            {
                PackUsedTileTextures();
            }
            if (GUILayout.Button("Select Output Folder"))
            {
                SelectOutputFolder();
            }
            EditorGUILayout.EndHorizontal();

            var newMtl = EditorGUILayout.ObjectField("Atlas Material", obj.material, typeof(Material), false, null) as Material;
            if (newMtl != obj.material)
            {
                obj.material = newMtl;
                mDirty = true;
            }

            var atlasTexturePropertyName = EditorGUILayout.TextField("Atlas Texture Property Name", obj.atlasTexturePropertyName);
            if (atlasTexturePropertyName != obj.atlasTexturePropertyName)
            {
                obj.atlasTexturePropertyName = atlasTexturePropertyName;
                mDirty = true;
            }
            var borderSize = Mathf.Max(0, EditorGUILayout.IntField("Border Size", obj.borderSize));
            if (borderSize != obj.borderSize)
            {
                obj.borderSize = borderSize;
                mDirty = true;
            }
            var rgbTextureAlphaIsOne = EditorGUILayout.Toggle("RGB Texture Alpha Is One", obj.rgbTextureAlphaIsOne);
            if (rgbTextureAlphaIsOne != obj.rgbTextureAlphaIsOne)
            {
                obj.rgbTextureAlphaIsOne = rgbTextureAlphaIsOne;
                mDirty = true;
            }
            var useTileBlock = EditorGUILayout.Toggle("Use Tile Block", obj.useTileBlock);
            if (useTileBlock != obj.useTileBlock)
            {
                obj.useTileBlock = useTileBlock;
                mDirty = true;
            }
            int minimumTileCountInRectangle = EditorGUILayout.IntField("Minimum Tile Count In Rectangle", obj.minimumTileCountInRectangle);
            if (minimumTileCountInRectangle != obj.minimumTileCountInRectangle)
            {
                obj.minimumTileCountInRectangle = minimumTileCountInRectangle;
                mDirty = true;
            }
            EditorGUILayout.BeginHorizontal();
            Mathf.Max(0, EditorGUILayout.IntField("Ground Tile Folders", obj.groundTileFolders.Length));
            if (GUILayout.Button("Change Count"))
            {
                var dlg = EditorUtils.CreateInputDialog("Change Folder Count");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Count", "", obj.groundTileFolders.Length.ToString()),
                };
                dlg.Show(items, OnClickChangeFolderCount);
            }
            EditorGUILayout.EndHorizontal();

            if (obj.tags.Length != obj.groundTileFolders.Length)
            {
                obj.tags = new string[obj.groundTileFolders.Length];
                mDirty = true;
            }

            if (obj.treatAlphaAsZeros.Length != obj.groundTileFolders.Length)
            {
                obj.treatAlphaAsZeros = new bool[obj.groundTileFolders.Length];
                mDirty = true;
            }

            int n = obj.groundTileFolders.Length;
            for (int i = 0; i < n; ++i)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUIUtility.labelWidth = 80;
                var treatAlphaAsZero = EditorGUILayout.Toggle("Zero Alpha", obj.treatAlphaAsZeros[i], GUILayout.MaxWidth(120));
                if (treatAlphaAsZero != obj.treatAlphaAsZeros[i])
                {
                    obj.treatAlphaAsZeros[i] = treatAlphaAsZero;
                    mDirty = true;
                }
                EditorGUIUtility.labelWidth = 70;
                var folder = EditorGUILayout.ObjectField($"Folder {i}", obj.groundTileFolders[i], typeof(DefaultAsset), false, null) as DefaultAsset;
                if (folder != obj.groundTileFolders[i])
                {
                    obj.groundTileFolders[i] = folder;
                    mDirty = true;
                }
                EditorGUIUtility.labelWidth = 40;
                var tag = EditorGUILayout.TextField("Tag", obj.tags[i], GUILayout.MaxWidth(120));
                if (tag != obj.tags[i])
                {
                    obj.tags[i] = tag;
                    mDirty = true;
                }
                EditorGUILayout.EndHorizontal();
                EditorGUIUtility.labelWidth = 0;
            }

            //draw special texture atlas settings
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUILayout.TextArea("可以对文件夹内单张贴图单独设置一个tag,如果贴图tag和文件夹tag不一致,优先使用贴图的tag打图集.\n相同tag的贴图打包到相同的贴图内(如果一张贴图能容纳下)");
            int newCount = Mathf.Max(0, EditorGUILayout.IntField("Special Textures Count", obj.specialTextureAtlasSettings.Length));
            if (newCount != obj.specialTextureAtlasSettings.Length)
            {
                SetSpecialAtlasTextureCount(newCount);
                mDirty = true;
            }
            for (int i = 0; i < newCount; ++i)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUIUtility.labelWidth = 100;
                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(obj.specialTextureAtlasSettings[i].texturePath);
                var newTexture = EditorGUILayout.ObjectField($"Texture {i}", texture, typeof(Texture2D), false, null) as Texture2D;
                if (newTexture != texture)
                {
                    obj.specialTextureAtlasSettings[i].texturePath = AssetDatabase.GetAssetPath(newTexture);
                    mDirty = true;
                }
                EditorGUIUtility.labelWidth = 60;
                var tag = EditorGUILayout.TextField("Tag", obj.specialTextureAtlasSettings[i].tag, GUILayout.MaxWidth(150));
                if (tag != obj.specialTextureAtlasSettings[i].tag)
                {
                    obj.specialTextureAtlasSettings[i].tag = tag;
                    mDirty = true;
                }
                EditorGUILayout.EndHorizontal();
                EditorGUIUtility.labelWidth = 0;
            }
            EditorGUILayout.EndVertical();

            if (mDirty)
            {
                EditorUtility.SetDirty(obj);
            }
        }

        bool OnClickChangeFolderCount(List<InputDialog.Item> parameters)
        {
            int n;
            int.TryParse((parameters[0] as InputDialog.StringItem).text, out n);
            if (n < 0)
            {
                return false;
            }
            SetFolderCount(n);
            return true;
        }

        void SetFolderCount(int n)
        {
            var obj = target as GroundTileAtlasSetting;
            int k = Mathf.Min(n, obj.groundTileFolders.Length);
            var groundTileFolders = new DefaultAsset[n];
            var tags = new string[n];
            var treatAlphaAsZeros = new bool[n];

            for (int i = 0; i < k; ++i)
            {
                groundTileFolders[i] = obj.groundTileFolders[i];
                tags[i] = obj.tags[i];
                treatAlphaAsZeros[i] = obj.treatAlphaAsZeros[i];
            }
            obj.groundTileFolders = groundTileFolders;
            obj.tags = tags;
            obj.treatAlphaAsZeros = treatAlphaAsZeros;
            mDirty = true;
        }

        void SetSpecialAtlasTextureCount(int n)
        {
            var obj = target as GroundTileAtlasSetting;
            int k = Mathf.Min(n, obj.specialTextureAtlasSettings.Length);
            var settings = new SpecialTextureAtlasSetting[n];

            for (int i = 0; i < k; ++i)
            {
                settings[i] = obj.specialTextureAtlasSettings[i];
            }
            for (int i = k; i < n; ++i)
            {
                settings[i] = new SpecialTextureAtlasSetting();
            }
            obj.specialTextureAtlasSettings = settings;
        }

        bool mDirty;
    }
}

#endif