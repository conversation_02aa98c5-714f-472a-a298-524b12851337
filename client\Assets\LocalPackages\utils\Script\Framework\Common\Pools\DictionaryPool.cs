﻿using System.Collections.Generic;

namespace Common.Pool
{

    /// <summary>
    /// 字典类型缓存池(不会自动检测数据，需手动释放清理数据)
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="U"></typeparam>
    public static class DictionaryPool<T, U>
    {

        #region 属性和字段

        /// <summary>
        /// 对象池数据存储
        /// </summary>
        private static readonly ObjectPool<Dictionary<T, U>> _listPool = new ObjectPool<Dictionary<T, U>>(null, Clear);

        #endregion

        #region 对象池逻辑处理

        /// <summary>
        /// 数据清理
        /// </summary>
        /// <param name="l"></param>
        private static void Clear(Dictionary<T, U> l)
        {
            if(l != null)
                l.Clear();
        }

        /// <summary>
        /// 数据获取
        /// </summary>
        /// <returns></returns>
        public static Dictionary<T, U> Get()
        {
            return _listPool.Get();
        }

        /// <summary>
        /// 数据回收
        /// </summary>
        /// <param name="toRelease"></param>
        public static void Release(Dictionary<T, U> toRelease)
        {
            _listPool.Release(toRelease);
        }


        #endregion

    }
}
