﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class NavMeshLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.NavMeshLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            bool visible = layerView.IsNavMeshVisible();
            writer.Write(visible);
            writer.Write(layerData.tileWidth);
            writer.Write(layerData.tileHeight);
            writer.Write(layerData.oceanAreaWalkable);

            int blockCount = layerData.navMeshCount;
            writer.Write(blockCount);

            for (int b = 0; b < blockCount; ++b)
            {
                var mesh = layerData.navMeshDatas[b];
                Vector3[] vertices = null;
                int[] triangles = null;
                if (mesh != null)
                {
                    vertices = mesh.vertices;
                    triangles = mesh.indices;
                }

                int vertexCount = vertices != null ? vertices.Length : 0;
                int indexCount = triangles != null ? triangles.Length : 0;
                writer.Write(vertexCount);
                writer.Write(indexCount);
                if (vertexCount > 0)
                {
                    for (int i = 0; i < vertexCount; ++i)
                    {
                        Utils.WriteVector3(writer, vertices[i]);
                    }
                }

                if (indexCount > 0)
                {
                    for (int i = 0; i < indexCount; ++i)
                    {
                        writer.Write(triangles[i]);
                    }
                }

                //save triangle types and states
                Utils.WriteUInt16Array(writer, mesh.triangleTypes);
                Utils.WriteBoolArray(writer, mesh.triangleStates);
            }
        }
    }
}

#endif