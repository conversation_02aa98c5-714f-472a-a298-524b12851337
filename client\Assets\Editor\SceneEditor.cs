﻿using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace K1
{

    public class SceneEditor
    {
        [UnityEditor.Callbacks.DidReloadScripts]
        static void TestDidReloadScripts()
        {
            //enableScene = EditorPrefs.GetBool("EanbleSceneGUI");
            SceneView.duringSceneGui += OnSceneGUI;
        }

        private static void OnSceneGUI(SceneView obj)
        {
            Handles.BeginGUI();
            var rect = obj.position;// new Rect(obj.maxSize.x, obj.maxSize.y,100,100);
                                    //rect.position = obj.maxSize;
                                    //D.Error?.Log($"{rect}, {obj.maxSize},{ obj.minSize}");
                                    //GUILayout.BeginArea(rect); // 规定显示区域为屏幕大小
            rect.position = Vector2.zero;
            rect.width = 100; rect.height = 20;
            for (int i = 0; i < EditorBuildSettings.scenes.Length; i++)
            {
                var item = EditorBuildSettings.scenes[i];
                var idx = item.path.LastIndexOf("/");
                string name = item.path.Substring(idx + 1).Replace(".unity", "");
                if (GUI.Button(rect, name))
                {
                    bool isDirty = false;

                    for (int s = 0; s < SceneManager.sceneCount; s++)
                    {
                        if (SceneManager.GetSceneAt(s).isDirty)
                        {
                            isDirty = true;
                            break;
                        }
                    }

                    if (isDirty)
                    {
                        //Selection.activeObject = AssetDatabase.LoadAssetAtPath(item.path, typeof(Object));
                        int b = EditorUtility.DisplayDialogComplex("打开场景", $"确定要打开场景{name}吗, \n请提前保存上一个场景!", "打开(保存场景)", "取消", "打开(不保存)");

                        switch (b)
                        {
                            //打开(保存场景)
                            case 0:
                                EditorSceneManager.SaveOpenScenes();
                                EditorSceneManager.OpenScene(item.path);
                                break;

                            //打开(不保存)
                            case 2:
                                EditorSceneManager.OpenScene(item.path);
                                break;

                            //取消
                            default:
                                break;
                        }
                    }
                    else
                    {
                        //Selection.activeObject = AssetDatabase.LoadAssetAtPath(item.path, typeof(Object));
                        EditorSceneManager.OpenScene(item.path);
                    }
                }
                rect.x += 100;
                rect.width = 30;
                if (GUI.Button(rect, "->"))
                {
                    Debug.Log(item.path);
                    //var guid = AssetDatabase.AssetPathToGUID(item.path);
                    var obj1 = AssetDatabase.LoadAssetAtPath<Object>(item.path);
                    if (obj1 != null)
                    {
                        EditorUtility.FocusProjectWindow();
                        EditorGUIUtility.PingObject(obj1);
                    }
                    //EditorWindow.FocusWindowIfItsOpen<SceneView>();
                }
                //还原位置
                rect.x -= 100;
                rect.width = 100;

                rect.y += 20;
            }

            //GUILayout.EndArea();
            Handles.EndGUI();
        }
    }
}