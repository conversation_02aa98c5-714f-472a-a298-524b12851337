﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerView : MapLayerView
    {
        void CreateLODPlane()
        {
            if (!Map.currentMap.isEditorMode)
            {
                Debug.Assert(mFogPlane == null);
                var layerData = mLayerData as SplitFogLayerData;
                if (!string.IsNullOrEmpty(layerData.fogLOD1PrefabPath))
                {
                    mFogPlane = MapModuleResourceMgr.LoadGameObject(layerData.fogLOD1PrefabPath);
                    var renderer = mFogPlane.GetComponent<MeshRenderer>();
                    int width = layerData.horizontalTileCount * 2;
                    int height = layerData.verticalTileCount * 2;
                    mMaskTexture = new Texture2D(width, height, TextureFormat.Alpha8, false);
                    mMaskTexture.SetPixels32(layerData.maskPixels);
                    mMaskTexture.Apply();
                    renderer.sharedMaterial.SetTexture(MapCoreDef.SPLIT_FOG_MASK_TEXTURE_NAME, mMaskTexture);
                    mFogPlane.SetActive(false);
                    float layerWidth = layerData.GetLayerWidthInMeter();
                    float layerHeight = layerData.GetLayerHeightInMeter();
                    var origin = layerData.layerOffset;
                    mFogPlane.transform.position = new Vector3(origin.x + layerWidth * 0.5f, layerData.fogHeight, origin.z + layerHeight * 0.5f);
                }
            }
        }

        public void OnMaskChange(Color32[] maskPixels)
        {
            mMaskTexture.SetPixels32(maskPixels);
            mMaskTexture.Apply();
        }

        public void OnLODChange()
        {
            if (mFogPlane != null)
            {
                if (layerData.currentLOD > 0)
                {
                    mFogPlane.SetActive(true);
                }
                else
                {
                    mFogPlane.SetActive(false);
                }
            }
        }

        GameObject mFogPlane;
        Texture2D mMaskTexture;
    }
}