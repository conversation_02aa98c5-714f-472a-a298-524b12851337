﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CreateCameraColliderOutline : CameraColliderEditorTool
    {
        public CreateCameraColliderOutline(CameraColliderEditor editor) : base(editor)
        {
            var handlers = editor.layer.GetEventHandlers();
            handlers.onAddCollision = OnAddCameraCollider;
        }

        public override void OnDestroy()
        {
        }

        public override void OnDisabled()
        {
            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];
        }

        public override void Update(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var pos = map.FromScreenToWorldPosition(screenPos);

            if (e.button == 0 && e.type == EventType.MouseDown && e.alt == false)
            {
                mLeftButtonDown = true;
            }

            if (e.button == 1 && e.type == EventType.MouseDown)
            {
                //press right key
                mCanCreate = true;
                bool created = AddCameraCollider();
                mEditor.RepaintGUI();
            }

            if (e.button == 0 && e.type == EventType.MouseUp)
            {
                mCanCreate = true;
                mLeftButtonDown = false;
            }

            if (mLeftButtonDown && mCanCreate)
            {
                mCanCreate = false;

                mAddedVertices.Add(pos);
                if (mAddedVertices.Count == 1)
                {
                    mAddedVertices.Add(pos);
                }
                mDisplayedVertices = mAddedVertices.ToArray();
                mEditor.RepaintScene();
            }

            if (e.type == EventType.MouseMove)
            {
                if (mAddedVertices.Count > 0)
                {
                    mAddedVertices[mAddedVertices.Count - 1] = pos;
                    mDisplayedVertices[mDisplayedVertices.Length - 1] = pos;
                }

                mEditor.RepaintScene();
            }

            HandleUtility.AddDefaultControl(0);
        }

        public override void OnEnabled()
        {
        }

        void OnAddCameraCollider(int dataID)
        {
            var data = Map.currentMap.FindObject(dataID) as CameraColliderData;
            mEditor.SetSelection(data, 0);
            mEditor.layer.ShowOutline(mEditor.layer.displayType);
            mEditor.RepaintGUI();
        }

        public bool AddCameraCollider()
        {
            bool created = mAddedVertices.Count > 3;
            if (created)
            {
                mAddedVertices.RemoveAt(mAddedVertices.Count - 1);

                var dataID = Map.currentMap.nextCustomObjectID;
                var action = new ActionAddCameraCollider(mEditor.layer.id, dataID, mAddedVertices);
                ActionManager.instance.PushAction(action);
            }

            mAddedVertices.Clear();
            mDisplayedVertices = new Vector3[0];

            return created;
        }

        public override void DrawScene()
        {
            Handles.DrawPolyLine(mDisplayedVertices);
        }

        public override void DrawGUI()
        {
            float radius = EditorGUILayout.FloatField(new GUIContent("Vertex Radius", "顶点显示大小"), mEditor.layer.displayVertexRadius);
            if (radius != mEditor.layer.displayVertexRadius)
            {
                mEditor.layer.SetVertexDisplayRadius(radius);
            }
        }

        public override void DrawMenu() { }

        public override CameraColliderEditorToolType type { get { return CameraColliderEditorToolType.CreateOutline; } }

        List<Vector3> mAddedVertices = new List<Vector3>();
        Vector3[] mDisplayedVertices = new Vector3[0];
        bool mCanCreate = true;
        bool mLeftButtonDown = false;
    }
}


#endif