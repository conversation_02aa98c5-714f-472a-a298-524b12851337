﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    //烘培顶点动画到贴图
    public partial class VertexAnimationBaker : AnimationBaker
    {
        public class BakeVertexOption : BakeOption
        {
            public int sampleFrameInterval;
            public Shader defaultShader;
        }

        void Clear()
        {
            mAllSkinningMeshesAnimationData = new List<MeshAllAnimationsData>();
            mSkinMeshies = new Dictionary<int, Mesh>();
            mSkinRendererMaterials = new Dictionary<int, Material>();
            mCPUAnimationDataForAllAnimations = new List<CPUAnimationData>();
            mNewMaterialToRendererIndex = new Dictionary<Material, int>();
        }

        //prefab是带有animator的root game object
        //将prefab中的Animator删除,将骨骼节点删除,将SkinnedMeshRenderer替换为MeshRenderer+MeshFilter,其他gameobject保持不变
        //generatePrefab:是否生成prefab文件
        //如果shader为null就使用bakeShader
        //useSRPBatcher:如果为true,会给每个动画模型使用独立的材质,注意要修改shader代码
        public override GameObject Bake(BakeOption option)
        {
            BakeVertexOption op = option as BakeVertexOption;
            ErrorMsg = "";
            GameObject prefab = op.prefab;
            string outputFolder = op.outputFolder;
#if UNITY_EDITOR
            Debug.Log($"Baking {prefab.name}");
#endif
            Clear();
            Debug.Assert(mRenderers != null);

            string originalPrefabGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(prefab));

            //process animation transitions
            List<ParameterInfo> parameters;
            string animationControllerGuid;
            var stateInfos = GetAnimationStateInfo(prefab, out parameters, out animationControllerGuid);

            if (op.clearOutputFolder)
            {
                Utils.ClearFolderContent(outputFolder);
            }
            if (string.IsNullOrEmpty(outputFolder))
            {
                outputFolder = Utils.GetFolderPath(AssetDatabase.GetAssetPath(prefab));
            }
            if (!Directory.Exists(outputFolder))
            {
                Directory.CreateDirectory(outputFolder);
                AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            }

            var newPrefab = GameObject.Instantiate<GameObject>(prefab);
            CreateIDMap(newPrefab, prefab, mNewGameObjectToOldGameObjectInstanceIDMap);
            newPrefab.name = prefab.name;
            string rootBoneName = mBones.GetRootBoneName();
            //删除新prefab中不要的数据
            FixPrefab(newPrefab, rootBoneName);

            for (int i = 0; i < mRenderers.Count; ++i)
            {
                //创建一些不带有skin信息的mesh,将skinmesh的skin信息存储到其他通道中
                Mesh mesh = null;
                if (mRenderers[i].GetType() == typeof(SkinnedMeshRenderer))
                {
                    var r = mRenderers[i] as SkinnedMeshRenderer;
                    mesh = CreateOrGetSkinMeshAsset(r.sharedMesh, outputFolder);
                    GameObject childObjInNewPrefab = Utils.FindGameObject(newPrefab, mRenderers[i].name);
                    Debug.Assert(childObjInNewPrefab.GetComponent<SkinnedMeshRenderer>() == null);
                    //给新的game object添加mesh renderer和mesh filter
                    var renderer = childObjInNewPrefab.GetComponent<MeshRenderer>();
                    SetMaterials(i, renderer, mRenderers[i].sharedMaterials, op.defaultShader);
                    var meshFilter = childObjInNewPrefab.AddComponent<MeshFilter>();
                    meshFilter.sharedMesh = mesh;

                    //增加一个标记标识使用哪个shader
                    var rendererTag = childObjInNewPrefab.AddComponent<RendererTag>();
                    rendererTag.shaderType = RendererShaderType.SkinShader;
                    rendererTag.meshIndex = i;
                    childObjInNewPrefab.transform.localPosition = Vector3.zero;
                    childObjInNewPrefab.transform.localScale = Vector3.one;
                    childObjInNewPrefab.transform.localRotation = Quaternion.identity;

                    mBones.AddBindPose(r.bones, r.sharedMesh.bindposes, false);
                }
            }

            var skinRenderers = GetSkinnedMeshRenderers(mRenderers);
            int allAnimationsSampledFrameCount = 0;
            for (int r = 0; r < skinRenderers.Count; ++r)
            {
                MeshAllAnimationsData animData = SampleAllAnimationsForSkinningMeshRenderer(skinRenderers[r], prefab.transform, stateInfos, op.sampleFrameInterval, out allAnimationsSampledFrameCount);
                mAllSkinningMeshesAnimationData.Add(animData);
            }

            FixLODGroup(newPrefab);
            //reparent skinned mesh renderers
            var rendererTags = newPrefab.GetComponentsInChildren<RendererTag>(true);
            for (int i = 0; i < rendererTags.Length; ++i)
            {
                if (rendererTags[i].shaderType == RendererShaderType.SkinShader)
                {
                    rendererTags[i].gameObject.transform.SetParent(newPrefab.transform, false);
                }
            }

            CreateAssets(newPrefab, outputFolder, stateInfos, parameters, op.blendType, op.useAnimationBlending, op.useSRPBatcher, animationControllerGuid, originalPrefabGuid, allAnimationsSampledFrameCount);

            //注意,需要把newPrefab的transform重置!因为transform信息都烘培到贴图里了!
            newPrefab.transform.position = Vector3.zero;
            newPrefab.transform.rotation = Quaternion.identity;
            newPrefab.transform.localScale = Vector3.one;

            if (op.deleteBipBones)
            {
                DeleteBipGameObjects(newPrefab);
            }

            if (op.generatePrefab)
            {
                SavePrefab(newPrefab, prefab, outputFolder);
                Object.DestroyImmediate(newPrefab);
                return null;
            }

            return newPrefab;
        }

        List<SkinnedMeshRenderer> GetSkinnedMeshRenderers(List<Renderer> renderers)
        {
            List<SkinnedMeshRenderer> sr = new List<SkinnedMeshRenderer>();
            for (int i = 0; i < renderers.Count; ++i)
            {
                if (renderers[i].GetType() == typeof(SkinnedMeshRenderer))
                {
                    sr.Add(renderers[i] as SkinnedMeshRenderer);
                }
            }
            return sr;
        }

        //清理新的prefab不需要的数据
        void FixPrefab(GameObject newPrefab, string rootBoneName)
        {
            mLODGroupManager = new LODGroupManager(newPrefab);

            //删除所有skinned mesh renderer
            var renderers = newPrefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            for (int i = 0; i < renderers.Length; ++i)
            {
                var obj = renderers[i].gameObject;
                var shadowCastingMode = renderers[i].shadowCastingMode;
                var receiveShadow = renderers[i].receiveShadows;
                var lightProbeUsage = renderers[i].lightProbeUsage;
                var reflectionProbeUsage = renderers[i].reflectionProbeUsage;
                var allowOcclusionWhenDynamic = renderers[i].allowOcclusionWhenDynamic;
                var motionVectorGenerationMode = renderers[i].motionVectorGenerationMode;
                Object.DestroyImmediate(renderers[i]);
                var newRenderer = obj.AddComponent<MeshRenderer>();
                newRenderer.shadowCastingMode = shadowCastingMode;
                newRenderer.receiveShadows = receiveShadow;
                newRenderer.lightProbeUsage = lightProbeUsage;
                newRenderer.reflectionProbeUsage = reflectionProbeUsage;
                newRenderer.allowOcclusionWhenDynamic = allowOcclusionWhenDynamic;
                newRenderer.motionVectorGenerationMode = motionVectorGenerationMode;
            }

            //删除根的animator
            var animators = newPrefab.GetComponentsInChildren<Animator>(true);
            DeleteAnimatorOfHiestHierarchy(animators);

            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                var customBoneObjInNewPrefab = Utils.FindGameObject(newPrefab, mCPUDrivenCustomBones[i].name);
                if (customBoneObjInNewPrefab != null)
                {
                    customBoneObjInNewPrefab.transform.SetParent(newPrefab.transform);
                    //因为会使用烘培后的动画数据驱动,这里transform要reset
                    customBoneObjInNewPrefab.transform.localPosition = Vector3.zero;
                    customBoneObjInNewPrefab.transform.localRotation = Quaternion.identity;
                    customBoneObjInNewPrefab.transform.localScale = Vector3.one;
                }
            }

            //删除skinned mesh renderer使用的骨骼,注意可能会有其他骨骼不会被删除,需要手动删除
            GameObject rootBone = Utils.FindGameObject(newPrefab, rootBoneName);
            DeleteBoneGameObject(rootBone);
        }

        void DeleteAnimatorOfHiestHierarchy(Animator[] animators)
        {
            if (animators.Length > 0)
            {
                GameObject.DestroyImmediate(animators[0]);
            }
        }

        void DeleteBoneGameObject(GameObject obj)
        {
            var bones = mBones.bones;
            for (int i = 0; i < bones.Count; ++i)
            {
                if (obj != null)
                {
                    var boneObj = Utils.FindGameObject(obj, bones[i].transform.name);
                    if (boneObj != null)
                    {
                        GameObject.DestroyImmediate(boneObj);
                    }
                }
            }
        }

        void FixLODGroup(GameObject newPrefab)
        {
            bool lodError = false;
            Debug.Log($"FixLODGroup: {newPrefab.name}");
            int n = mLODGroupManager.lodGroups.Length;
            if (n > 0)
            {
                var lodGroups = newPrefab.GetComponentsInChildren<LODGroup>(true);
                for (int i = 0; i < n; ++i)
                {
                    var oldLODs = mLODGroupManager.lodGroups[i].lods;
                    var newLODs = new LOD[oldLODs.Length];
                    for (int k = 0; k < oldLODs.Length; ++k)
                    {
                        var oldRenderers = oldLODs[k].renderers;
                        var newRenderers = new Renderer[oldRenderers.Length];
                        for (int j = 0; j < oldRenderers.Length; ++j)
                        {
                            var obj = Utils.FindGameObject(newPrefab, oldRenderers[j]);
                            if (obj != null)
                            {
                                newRenderers[j] = obj.GetComponent<Renderer>();
                            }
                            else
                            {
                                lodError = true;
                            }
                        }
                        newLODs[k] = new LOD(oldLODs[k].transitionHeight, newRenderers);
                    }
                    lodGroups[i].SetLODs(newLODs);
                }
            }

            if (lodError)
            {
                string msg = $"{newPrefab.name} has lod group error!";
                ErrorMsg += msg;
                Debug.LogError(msg);
            }
        }

        //将原material instance id映射到新创建的material
        //materials是原prefab中的material
        void SetMaterials(int rendererIndex, MeshRenderer renderer, Material[] materials, Shader defaultShader)
        {
            int nMtls = materials.Length;
            Material[] newMtls = new Material[nMtls];
            for (int i = 0; i < nMtls; ++i)
            {
                Material newMtl;
                mSkinRendererMaterials.TryGetValue(materials[i].GetInstanceID(), out newMtl);
                if (newMtl == null)
                {
                    var shader = mMaterialManager.GetTargetShader(materials[i]);
                    if (shader == null)
                    {
                        shader = defaultShader;
                    }
                    newMtl = new Material(shader);
                    newMtl.name = materials[i].name;
                    CopyProperties(newMtl, materials[i]);
                    mSkinRendererMaterials.Add(materials[i].GetInstanceID(), newMtl);
                    mNewMaterialToRendererIndex[newMtl] = rendererIndex;
                }
                newMtls[i] = newMtl;
            }
            renderer.sharedMaterials = newMtls;
        }

        void CopyProperties(Material tgt, Material src)
        {
            var srcShader = src.shader;
            int srcShaderPropertyCount = ShaderUtil.GetPropertyCount(srcShader);
            for (int i = 0; i < srcShaderPropertyCount; ++i)
            {
                var srcPropName = ShaderUtil.GetPropertyName(srcShader, i);
                if (tgt.HasProperty(srcPropName))
                {
                    var srcPropType = ShaderUtil.GetPropertyType(srcShader, i);
                    switch (srcPropType)
                    {
                        case ShaderUtil.ShaderPropertyType.Color:
                            tgt.SetColor(srcPropName, src.GetColor(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.Float:
                        case ShaderUtil.ShaderPropertyType.Range:
                            tgt.SetFloat(srcPropName, src.GetFloat(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.TexEnv:
                            tgt.SetTexture(srcPropName, src.GetTexture(srcPropName));
                            break;
                        case ShaderUtil.ShaderPropertyType.Vector:
                            tgt.SetVector(srcPropName, src.GetVector(srcPropName));
                            break;
                    }
                }
            }
        }

        //创建资源
        void CreateAssets(GameObject newPrefab, string outputFolder, List<StateInfo> stateInfos, List<ParameterInfo> parameters, AnimationBlendType blendType, bool useAnimationBlending, bool useSRPBatcher, string animationControllerGuid, string originalPrefabGuid, int allAnimationsSampledFrameCount)
        {
            var animatorWrapper = newPrefab.GetComponent<AnimatorWrapper>();
            if (animatorWrapper == null)
            {
                animatorWrapper = newPrefab.AddComponent<AnimatorWrapper>();
            }
            animatorWrapper.InitInEditor(true, true, newPrefab);

            BakedVertexAnimationData animTextureData = ScriptableObject.CreateInstance<BakedVertexAnimationData>();
            animTextureData.animationControllerAssetGuid = animationControllerGuid;
            animTextureData.originalPrefabGuid = originalPrefabGuid;
            animTextureData.blendType = blendType;
            animTextureData.useAnimationBlending = useAnimationBlending;
            animTextureData.prefabName = newPrefab.name;

            List<AnimationTextureDataForOneMesh> allTextureDatas = CreateAnimationTextureDataForAllSkinMeshRenderers();
            List<Texture2D> bakedTextures = new List<Texture2D>();
            for (int i = 0; i < allTextureDatas.Count; ++i)
            {
                AnimationTextureDataForOneMesh textureData = allTextureDatas[i];
                var texturePath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mRenderers[i].name}_texture.asset");
                Texture2D texture = CreateRGBAHalfTexture(texturePath, textureData.textureWidth, textureData.textureHeight, textureData.data.ToArray());
                bakedTextures.Add(texture);
            }

            List<List<int>> animationStartOffsetsForAllMeshes = new List<List<int>>();
            List<List<float>> animationStartOffsetRatioForAllMeshes = new List<List<float>>();
            int nAnimations = stateInfos.Count;
            for (int i = 0; i < nAnimations; ++i)
            {
                List<int> animationOffsetForOneMesh = new List<int>();
                List<float> animationOffsetRatioForOneMesh = new List<float>();
                animationStartOffsetsForAllMeshes.Add(animationOffsetForOneMesh);
                animationStartOffsetRatioForAllMeshes.Add(animationOffsetRatioForOneMesh);
                for (int t = 0; t < allTextureDatas.Count; ++t)
                {
                    animationOffsetForOneMesh.Add(allTextureDatas[t].animationsOffset[i]);
                    animationOffsetRatioForOneMesh.Add(allTextureDatas[t].animationsOffset[i] / (float)allAnimationsSampledFrameCount);
                }
            }

            animTextureData.totalFrameCount = allAnimationsSampledFrameCount;

            //cpu 驱动的骨骼动画
            Vector3[] combinedCpuDrivenCustomBoneTranslations;
            Vector3[] combinedCpuDrivenCustomBoneScalings;
            Quaternion[] combinedCpuDrivenCustomBoneRotations;
            List<int> cpuDrivenCustomBoneAnimationOffset;
            CombineCPUDrivenCustomBonesAnimationData(out cpuDrivenCustomBoneAnimationOffset, out combinedCpuDrivenCustomBoneTranslations, out combinedCpuDrivenCustomBoneRotations, out combinedCpuDrivenCustomBoneScalings);

            animTextureData.cpuDrivenCustomBoneTranslations = combinedCpuDrivenCustomBoneTranslations;
            animTextureData.cpuDrivenCustomBoneRotations = combinedCpuDrivenCustomBoneRotations;
            animTextureData.cpuDrivenCustomBoneScalings = combinedCpuDrivenCustomBoneScalings;
            MeshAllAnimationsData meshAllAnimationData = mAllSkinningMeshesAnimationData[0];
            animTextureData.cpuDrivenCustomBoneNames = meshAllAnimationData.cpuDrivenCustomBoneNames;

            //每个mesh的动画基础信息都相同,所以取第一个即可
            animTextureData.animationInfo = CreateAnimationInfo(mAllSkinningMeshesAnimationData[0], animationStartOffsetsForAllMeshes, animationStartOffsetRatioForAllMeshes, cpuDrivenCustomBoneAnimationOffset);
            animTextureData.animationStateInfo = CreateAnimationStateInfo(stateInfos, animTextureData.animationInfo);
            animTextureData.parameterInfo = CreateParameterInfo(parameters);
            animTextureData.useRGBA32Texture = false;
            animTextureData.useSRPBatcher = useSRPBatcher;

            string bakedDataPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{MapCoreDef.BAKED_ANIM_DATA_NAME}.asset");
            AssetDatabase.CreateAsset(animTextureData, bakedDataPath);
            AssetDatabase.Refresh();

            animatorWrapper.SetAnimationData(animTextureData, bakedDataPath);

            //保存材质
            foreach (var p in mSkinRendererMaterials)
            {
                var mtl = p.Value;
                mNewMaterialToRendererIndex.TryGetValue(mtl, out int rendererIndex);

                Texture2D texture = bakedTextures[rendererIndex];
                mtl.SetTexture(MapCoreDef.BAKED_ANIMATION_TEXTURE_PROPERTY_NAME, texture);
                mtl.SetVector(MapCoreDef.BAKED_ANIMATION_TEXTURE_SIZE_PROPERTY_NAME, new Vector4(texture.width, texture.height, 0, 0));
                mtl.enableInstancing = true;
                string mtlPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{mtl.name}.mat");
                AssetDatabase.CreateAsset(mtl, mtlPath);
            }
        }

        Texture2D CreateRGBAHalfTexture(string texturePath, int textureWidth, int textureHeight, Color[] skinnedAnimationTextureData)
        {
            var texture = new Texture2D(textureWidth, textureHeight, TextureFormat.RGBAHalf, false);

            texture.SetPixels(skinnedAnimationTextureData);
            texture.Apply(false, true);
            texture.filterMode = FilterMode.Bilinear;

            AssetDatabase.CreateAsset(texture, texturePath);
            return texture;
        }

        //创建一个非skin的mesh
        Mesh CreateOrGetSkinMeshAsset(Mesh skinnedMesh, string outputFolder)
        {
            Mesh mesh;
            mSkinMeshies.TryGetValue(skinnedMesh.GetInstanceID(), out mesh);
            if (mesh == null)
            {
                int n = skinnedMesh.vertexCount;
                mesh = new Mesh();
                mesh.vertices = skinnedMesh.vertices;
                mesh.triangles = skinnedMesh.triangles;
                mesh.normals = skinnedMesh.normals;
                //将顶点序号放到uv.z中
                List<Vector3> uv = new List<Vector3>();
                var originalUV = skinnedMesh.uv;
                for (int i = 0; i < originalUV.Length; ++i)
                {
                    uv.Add(new Vector3(originalUV[i].x, originalUV[i].y, (float)i));
                }
                mesh.SetUVs(0, uv);
                mesh.colors = skinnedMesh.colors;

                string meshPath = Utils.ConvertToUnityAssetsPath($"{outputFolder}/{skinnedMesh.name}_mesh.asset");
                meshPath = meshPath.Replace(":", "_");
                AssetDatabase.CreateAsset(mesh, meshPath);
                mSkinMeshies[skinnedMesh.GetInstanceID()] = mesh;
            }

            return mesh;
        }

        MeshAllAnimationsData SampleAllAnimationsForSkinningMeshRenderer(SkinnedMeshRenderer renderer, Transform rootGameObject, List<StateInfo> allStates, int sampleFrameInterval, out int allAnimationsSampledFrameCount)
        {
            //每个renderer都需要一张贴图,因为顶点数不同,贴图width = 顶点数,height = frame * animation count
            MeshAllAnimationsData meshAllAnimationsData = new MeshAllAnimationsData(renderer);
            bool processCPUAnimation = mCPUAnimationDataForAllAnimations.Count == 0;
            //每个动画采样数据占所有动画采样长度的比例
            allAnimationsSampledFrameCount = 0;
            for (int s = 0; s < allStates.Count; ++s)
            {
                StateInfo state = allStates[s];
                AnimationClip clip = state.clip;
                Debug.Assert(!string.IsNullOrEmpty(state.name));
                int totalFrame = Mathf.FloorToInt(clip.length * clip.frameRate);
                //计算出需要sample多少帧动画数据到贴图中
                int totalSampledFrameCount = Mathf.Min(totalFrame, Mathf.CeilToInt(totalFrame / sampleFrameInterval) + 1);
                allAnimationsSampledFrameCount += totalSampledFrameCount;
                float timeStep = 0;
                if (totalSampledFrameCount > 1)
                {
                    timeStep = clip.length / (totalSampledFrameCount - 1);
                }

                //要从有animator的game object开始
                var animatorObj = rootGameObject.GetComponentInChildren<Animator>(true).gameObject;
                var transforms = animatorObj.GetComponentsInChildren<Transform>(true);

                var boneNames = GetCPUDrivenCustomBoneNames(transforms);
                meshAllAnimationsData.SetCPUDrivenCustomBoneNames(boneNames);

                MeshOneAnimationData meshAnimData = new MeshOneAnimationData(totalSampledFrameCount, state.name, clip.length, clip.frameRate, clip.isLooping, clip);
                meshAllAnimationsData.AddMeshAnimationData(meshAnimData);

                CPUAnimationData cpuAnimationData = null;
                if (processCPUAnimation)
                {
                    cpuAnimationData = new CPUAnimationData();
                    mCPUAnimationDataForAllAnimations.Add(cpuAnimationData);
                }

                for (int i = 0; i < totalSampledFrameCount; ++i)
                {
                    //获取第i帧的transform信息
                    float time = Mathf.Min(i * timeStep, clip.length);
                    clip.SampleAnimation(animatorObj, time);

                    if (mSnapMesh == null)
                    {
                        mSnapMesh = new Mesh();
                    }
                    mSnapMesh.Clear();
                    //得到该帧的烘培顶点
                    renderer.BakeMesh(mSnapMesh);
                    meshAnimData.AddBakedVertexPositionsInOneFrame(mSnapMesh.vertices);

                    if (processCPUAnimation)
                    {
                        //获取cpu驱动的骨骼信息
                        Vector3[] cpuDrivenCustomBoneTranslations;
                        Vector3[] cpuDrivenCustomBoneScalings;
                        Quaternion[] cpuDrivenCustomBoneRotations;
                        GetCPUDrivenCustomBoneTransformsInOneFrame(transforms, out cpuDrivenCustomBoneTranslations, out cpuDrivenCustomBoneRotations, out cpuDrivenCustomBoneScalings);

                        cpuAnimationData.AddCPUDriveCustomBoneTransformsInOneFrame(cpuDrivenCustomBoneTranslations, cpuDrivenCustomBoneRotations, cpuDrivenCustomBoneScalings);
                    }
                }
            }

            for (int i = 0; i < meshAllAnimationsData.meshAllAnimationsData.Count; ++i)
            {
                MeshOneAnimationData animData = meshAllAnimationsData.meshAllAnimationsData[i];
                animData.lengthRatio = animData.bakedVertexPositionsInAllFrames.Count / (float)allAnimationsSampledFrameCount;
            }

            return meshAllAnimationsData;
        }

        string[] GetCPUDrivenCustomBoneNames(Transform[] allTransforms)
        {
            List<string> names = new List<string>();
            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                //看看这个transform是否是自定义的节点
                var customBoneIdx = GetBoneIndex(mCPUDrivenCustomBones[i], allTransforms);
                if (customBoneIdx >= 0)
                {
                    names.Add(allTransforms[customBoneIdx].name);
                }
            }
            return names.ToArray();
        }

        void GetCPUDrivenCustomBoneTransformsInOneFrame(Transform[] allTransforms, out Vector3[] translations, out Quaternion[] rotations, out Vector3[] scalings)
        {
            translations = new Vector3[mCPUDrivenCustomBones.Count];
            rotations = new Quaternion[mCPUDrivenCustomBones.Count];
            scalings = new Vector3[mCPUDrivenCustomBones.Count];
            for (int i = 0; i < mCPUDrivenCustomBones.Count; ++i)
            {
                //看看这个transform是否是自定义的节点
                var customBoneIdx = GetBoneIndex(mCPUDrivenCustomBones[i], allTransforms);
                if (customBoneIdx >= 0)
                {
                    scalings[i] = allTransforms[customBoneIdx].lossyScale;
                    rotations[i] = allTransforms[customBoneIdx].rotation;
                    translations[i] = allTransforms[customBoneIdx].position;
                }
            }
        }

        int GetBoneIndex(Transform t, IEnumerable<Transform> bones)
        {
            int idx = 0;
            foreach (var b in bones)
            {
                //todo 是否需要用name来判断?
                if (b.name == t.name)
                {
                    return idx;
                }
                ++idx;
            }
            return -1;
        }

        public class AnimationTextureDataForOneMesh
        {
            public string name;
            public List<Color> data;
            public int textureWidth;
            public int textureHeight;
            public List<int> animationsOffset = new List<int>();
        }

        public List<AnimationTextureDataForOneMesh> CreateAnimationTextureDataForAllSkinMeshRenderers()
        {
            List<AnimationTextureDataForOneMesh> textureDataForAllMeshies = new List<AnimationTextureDataForOneMesh>();
            int maxTextureSize = 2048;
            //将所有动画的数据打包到一张贴图中
            foreach (MeshAllAnimationsData allDataForOneMesh in mAllSkinningMeshesAnimationData)
            {
                AnimationTextureDataForOneMesh textureDataForOneMesh = new AnimationTextureDataForOneMesh();
                textureDataForAllMeshies.Add(textureDataForOneMesh);
                textureDataForOneMesh.textureWidth = allDataForOneMesh.GetMeshVertexCount();
                textureDataForOneMesh.textureHeight = allDataForOneMesh.GetAllAnimationsTotalFrameCount();
                if (textureDataForOneMesh.textureWidth > maxTextureSize ||
                    textureDataForOneMesh.textureHeight > maxTextureSize)
                {
                    Debug.Assert(false, $"Can't create texture! Invalid texture size {textureDataForOneMesh.textureWidth} {textureDataForOneMesh.textureHeight}");
                    return null;
                }

                int offset = 0;
                textureDataForOneMesh.data = new List<Color>();
                foreach (MeshOneAnimationData oneAnimationData in allDataForOneMesh.meshAllAnimationsData)
                {
                    //遍历每个烘培动画数据,组合成贴图数据
                    textureDataForOneMesh.animationsOffset.Add(offset);
                    int nFrames = oneAnimationData.bakedVertexPositionsInAllFrames.Count;
                    List<Vector3[]> vertexPositionsInAllFrames = oneAnimationData.bakedVertexPositionsInAllFrames;
                    for (int i = 0; i < nFrames; ++i)
                    {
                        var vertexPositionsInOneFrame = vertexPositionsInAllFrames[i];
                        int vertexCount = vertexPositionsInOneFrame.Length;
                        for (int k = 0; k < vertexCount; ++k)
                        {
                            float x = vertexPositionsInOneFrame[k].x;
                            float y = vertexPositionsInOneFrame[k].y;
                            float z = vertexPositionsInOneFrame[k].z;
                            //padding
                            float w = 0;
                            textureDataForOneMesh.data.Add(new Color(x, y, z, w));
                        }
                    }
                    //注意这个offset只是所有frame的offset
                    offset += nFrames;
                }
            }

            return textureDataForAllMeshies;
        }

        //将所有cpu动画的数据打包到对应的数组中
        void CombineCPUDrivenCustomBonesAnimationData(out List<int> animationOffset, out Vector3[] translations, out Quaternion[] rotations, out Vector3[] scalings)
        {
            animationOffset = new List<int>();
            int idx = 0;
            List<Vector3> translationData = new List<Vector3>();
            List<Vector3> scalingData = new List<Vector3>();
            List<Quaternion> rotationData = new List<Quaternion>();
            foreach(var data in mCPUAnimationDataForAllAnimations)
            {
                animationOffset.Add(idx);
                var frameTranslations = data.cpuDrivenCustomBoneTranslationsInAllFrames;
                var frameScalings = data.cpuDrivenCustomBoneScalingsInAllFrames;
                var frameRotations = data.cpuDrivenCustomBoneRotationsInAllFrames;
                int nFrames = frameTranslations.Count;
                Debug.Assert(frameTranslations.Count == frameScalings.Count && frameTranslations.Count == frameRotations.Count);
                for (int i = 0; i < nFrames; ++i)
                {
                    translationData.AddRange(frameTranslations[i]);
                    scalingData.AddRange(frameScalings[i]);
                    rotationData.AddRange(frameRotations[i]);

                    idx += frameTranslations[i].Length;
                }
            }

            rotations = rotationData.ToArray();

            //check if translation is valid
            bool validTranslation = false;
            for (int i = 0; i < translationData.Count; ++i)
            {
                if (translationData[i] != Vector3.zero)
                {
                    validTranslation = true;
                    break;
                }
            }
            //check if scaling is valid
            bool validScaling = false;
            for (int i = 0; i < scalingData.Count; ++i)
            {
                if (scalingData[i] != Vector3.one)
                {
                    validScaling = true;
                    break;
                }
            }

            if (validScaling)
            {
                scalings = scalingData.ToArray();
            }
            else
            {
                scalings = null;
            }
            if (validTranslation)
            {
                translations = translationData.ToArray();
            }
            else
            {
                translations = null;
            }
        }

        //创建动画速度参数信息
        AnimationSpeedInfo CreateSpeedInfo(StateInfo stateInfo)
        {
            AnimationSpeedInfo speedInfo = new AnimationSpeedInfo();
            speedInfo.speed = stateInfo.speed;
            speedInfo.speedParameter = stateInfo.speedParameter;
            speedInfo.speedParameterActive = stateInfo.speedParameterActive;
            return speedInfo;
        }

        //创建动画cycle offset参数信息
        AnimationCycleOffsetInfo CreateCycleOffsetInfo(StateInfo stateInfo)
        {
            AnimationCycleOffsetInfo info = new AnimationCycleOffsetInfo();
            info.cycleOffset = stateInfo.cycleOffset;
            info.cycleOffsetParameter = stateInfo.cycleOffsetParameter;
            info.cycleOffsetParameterActive = stateInfo.cycleOffsetParameterActive;
            return info;
        }

        //创建动画信息
        AnimationInfo[] CreateAnimationInfo(MeshAllAnimationsData animationData, List<List<int>> animationStartOffsets, List<List<float>> animationStartOffsetRatio, List<int> cpuDrivenAnimationStartOffsets)
        {
            int animationCount = animationData.meshAllAnimationsData.Count;
            AnimationInfo[] info = new AnimationInfo[animationCount];
            for (int i = 0; i < animationCount; ++i)
            {
                var data = animationData.meshAllAnimationsData[i];
                string clipGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(data.clip));
                int sampledFrameCount = data.bakedVertexPositionsInAllFrames.Count;
                info[i] = new AnimationInfo(sampledFrameCount, data.lengthInSeconds, data.framerate, 1, data.loop, 0, 0, 0, clipGuid, data.clip.name);
                info[i].cpuDrivenAnimStartOffset = cpuDrivenAnimationStartOffsets[i];
                info[i].bakedVertexAnimationStartOffsetsForEachMesh = animationStartOffsets[i].ToArray();
                info[i].bakedVertexAnimationStartOffsetRatioForEachMesh = animationStartOffsetRatio[i].ToArray();
                info[i].bakedVertexAnimationLengthRatio = data.lengthRatio;
            }
            return info;
        }

        int GetAnimationInfoIndex(string clipGuid, AnimationInfo[] infos)
        {
            for (int i = 0; i < infos.Length; ++i)
            {
                if (infos[i].clipGuid == clipGuid)
                {
                    return i;
                }
            }
            Debug.Assert(false);
            return -1;
        }

        AnimationStateInfo[] CreateAnimationStateInfo(List<StateInfo> stateInfos, AnimationInfo[] animInfos)
        {
            AnimationStateInfo[] info = new AnimationStateInfo[stateInfos.Count];
            for (int i = 0; i < stateInfos.Count; ++i)
            {
                var speedInfo = CreateSpeedInfo(stateInfos[i]);
                var cycleOffsetInfo = CreateCycleOffsetInfo(stateInfos[i]);
                string clipGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(stateInfos[i].clip));
                int animInfoIndex = GetAnimationInfoIndex(clipGuid, animInfos);
                info[i] = new AnimationStateInfo(stateInfos[i].name, animInfoIndex, stateInfos[i].transitions, stateInfos[i].events, speedInfo, cycleOffsetInfo);
            }
            return info;
        }

        AnimationParameterInfo[] CreateParameterInfo(List<ParameterInfo> parameters)
        {
            AnimationParameterInfo[] info = new AnimationParameterInfo[parameters.Count];
            for (int i = 0; i < info.Length; ++i)
            {
                info[i] = new AnimationParameterInfo();
                info[i].defaultBool = parameters[i].defaultBool;
                info[i].defaultInt = parameters[i].defaultInt;
                info[i].defaultFloat = parameters[i].defaultFloat;
                info[i].name = parameters[i].name;
                info[i].nameHash = parameters[i].nameHash;
                info[i].type = parameters[i].type;
            }
            return info;
        }

        void SavePrefab(GameObject newPrefab, GameObject oldPrefab, string outputFolder)
        {
            if (newPrefab == null || string.IsNullOrEmpty(outputFolder))
            {
                Debug.Assert(false);
                return;
            }

            //fix prefab instance
            FixPrefabInstance(newPrefab, oldPrefab);

            //temp code
            //newPrefab.AddComponent<VertexAnimationBakeValidator>();

            //save prefab
            string prefabPath = Utils.ConvertToUnityAssetsPath(outputFolder + "/" + newPrefab.name + ".prefab");
            PrefabUtility.SaveAsPrefabAsset(newPrefab, prefabPath);
        }

        void FixPrefabInstance(GameObject newPrefab, GameObject oldPrefab)
        {
            var cpuAnimationTags = newPrefab.GetComponentsInChildren<CPUAnimationTag>(true);
            for (int i = cpuAnimationTags.Length - 1; i >= 0; --i)
            {
                FixPrefabInstance(cpuAnimationTags[i].gameObject, cpuAnimationTags[i].transform.parent);
            }
        }

        void FixPrefabInstance(GameObject gameObject, Transform parent)
        {
            GameObject oldPrefabGameObject = FindOldPrefabGameObject(gameObject.GetInstanceID());
            Debug.Assert(oldPrefabGameObject != null);
            var newPrefabGameObject = PrefabUtility.GetCorrespondingObjectFromSource(oldPrefabGameObject);
            if (newPrefabGameObject != null)
            {
                //删除普通game object,改成prefab instance
                var newChildPrefabInstance = (GameObject)PrefabUtility.InstantiatePrefab(newPrefabGameObject);

                newChildPrefabInstance.name = gameObject.name;
                bool hasCPUAnimationTag = gameObject.GetComponent<CPUAnimationTag>() != null;
                newChildPrefabInstance.transform.parent = parent;
                if (newChildPrefabInstance.transform.parent != parent)
                {
                    //没有设置parent成功,不使用prefab instance
                    //GameObject.DestroyImmediate(newChildPrefabInstance);
                    Debug.LogError("Set Parent failed, but you can still use this prefab!");
                }
                else
                {
                    if (hasCPUAnimationTag)
                    {
                        bool attachedToAnyBone = IsGameObjectAttachedToAnyBone(gameObject);
                        if (attachedToAnyBone)
                        {
                            newChildPrefabInstance.transform.localPosition = Vector3.zero;
                            newChildPrefabInstance.transform.localScale = Vector3.one;
                            newChildPrefabInstance.transform.localRotation = Quaternion.identity;
                        }
                        else
                        {
                            newChildPrefabInstance.transform.localPosition = oldPrefabGameObject.transform.localPosition;
                            newChildPrefabInstance.transform.localScale = oldPrefabGameObject.transform.localScale;
                            newChildPrefabInstance.transform.localRotation = oldPrefabGameObject.transform.localRotation;
                        }
                    }
                    else
                    {
                        newChildPrefabInstance.transform.localPosition = oldPrefabGameObject.transform.localPosition;
                        newChildPrefabInstance.transform.localScale = oldPrefabGameObject.transform.localScale;
                        newChildPrefabInstance.transform.localRotation = oldPrefabGameObject.transform.localRotation;
                    }
                    GameObject.DestroyImmediate(gameObject);
                }
            }
            else
            {
                var transform = gameObject.transform;
                int childCount = transform.childCount;
                for (int i = childCount - 1; i >= 0; --i)
                {
                    FixPrefabInstance(transform.GetChild(i).gameObject, transform);
                }
            }
        }

        GameObject FindOldPrefabGameObject(int newGameObjectInstanceID)
        {
            GameObject result;
            mNewGameObjectToOldGameObjectInstanceIDMap.TryGetValue(newGameObjectInstanceID, out result);
            return result;
        }

        StateInfo GetStateInfo(string stateName, List<StateInfo> stateInfos)
        {
            for (int i = 0; i < stateInfos.Count; ++i)
            {
                if (stateName == stateInfos[i].name)
                {
                    return stateInfos[i];
                }
            }
            Debug.Assert(false);
            return null;
        }

        void CreateIDMap(GameObject newPrefab, GameObject oldPrefab, Dictionary<int, GameObject> newGameObjectToOldGameObjectInstanceIDMap)
        {
            newGameObjectToOldGameObjectInstanceIDMap.Add(newPrefab.GetInstanceID(), oldPrefab);
            int childCount = newPrefab.transform.childCount;
            for (int i = 0; i < childCount; ++i)
            {
                CreateIDMap(newPrefab.transform.GetChild(i).gameObject, oldPrefab.transform.GetChild(i).gameObject, newGameObjectToOldGameObjectInstanceIDMap);
            }
        }

        bool IsGameObjectAttachedToAnyBone(GameObject gameObject)
        {
            var correspondingGameObjectInOldPrefab = FindOldPrefabGameObject(gameObject.GetInstanceID());
            var rootBone = mBones.GetRootBone();
            var t = correspondingGameObjectInOldPrefab.transform;
            while (t != null)
            {
                if (t == rootBone)
                {
                    return true;
                }
                t = t.parent;
            }
            return false;
        }

        bool CanDeleteBipObject(Transform transform)
        {
            bool isBip = transform.gameObject.name.StartsWith("bip", true, null);
            if (!isBip)
            {
                return false;
            }

            bool hasCpuAnimationTag = (transform.GetComponent<CPUAnimationTag>() != null || IsAncestorHasCPUAnimationTag(transform));
            if (hasCpuAnimationTag)
            {
                return false;
            }

            return true;
        }

        void DeleteBipGameObjects(GameObject newPrefab)
        {
            var transform = newPrefab.transform;
            int n = transform.childCount;
            for (int i = n - 1; i >= 0; --i)
            {
                var child = transform.GetChild(i);
                bool canDelete = CanDeleteBipObject(child);
                if (canDelete)
                {
                    GameObject.DestroyImmediate(child.gameObject);
                }
                else
                {
                    DeleteBipGameObjects(child.gameObject);
                }
            }
        }

        public override List<Renderer> renderers { get { return mRenderers; } }
        public override BakeMaterialManager materialManager { get { return mMaterialManager; } }
        public Transform rootBone { get { return mBones.GetRootBone(); } set { mBones.SetRootBone(value); } }

        //管理一个prefab下的所有骨骼,不包括自定义骨骼
        BoneManager mBones = new BoneManager();
        List<Transform> mCPUDrivenCustomBones;
        //需要处理的renderer
        List<Renderer> mRenderers;
        List<MeshAllAnimationsData> mAllSkinningMeshesAnimationData = new List<MeshAllAnimationsData>();
        List<CPUAnimationData> mCPUAnimationDataForAllAnimations = new List<CPUAnimationData>();
        Dictionary<int, Mesh> mSkinMeshies = new Dictionary<int, Mesh>();
        //skin mesh renderer使用的新创建的材质
        Dictionary<int, Material> mSkinRendererMaterials = new Dictionary<int, Material>();
        //从新创建的material找到其对应的renderer在mRenderers中的索引
        Dictionary<Material, int> mNewMaterialToRendererIndex = new Dictionary<Material, int>();
        //将旧的prefab的shader转换成新的shader
        BakeMaterialManager mMaterialManager = new BakeMaterialManager();
        LODGroupManager mLODGroupManager;
        //从新的game object instanceID 找到老的game object instance id
        Dictionary<int, GameObject> mNewGameObjectToOldGameObjectInstanceIDMap = new Dictionary<int, GameObject>();
        Mesh mSnapMesh;
    }
}
#endif