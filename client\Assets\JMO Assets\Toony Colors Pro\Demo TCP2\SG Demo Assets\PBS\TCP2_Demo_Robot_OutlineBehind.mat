%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Robot_OutlineBehind
  m_Shader: {fileID: 4800000, guid: 0d325ef349acc794da37e3dd556b7a99, type: 3}
  m_ShaderKeywords: OUTLINES TCP2_DISABLE_WRAPPED_LIGHT TCP2_SPEC_TOON TCP2_STYLIZED_FRESNEL
    TCP2_TANGENT_AS_NORMALS _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 90f5c2a59e018304bb00979205857f25, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _SketchTex
      second:
        m_Texture: {fileID: 2800000, guid: a0d03d906b6264d4c8c02b0ffd7bc8c2, type: 3}
        m_Scale: {x: 10, y: 10}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ThresholdTex
      second:
        m_Texture: {fileID: 2800000, guid: ebbb2bec16a72fe48b8463e62d47d076, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _DstBlendOutline
      second: 10
    - first:
        name: _EnableConstSizeOutline
      second: 0
    - first:
        name: _EnableTexturedOutline
      second: 0
    - first:
        name: _EnableZSmooth
      second: 0
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.85
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _HSV_H
      second: 180
    - first:
        name: _HSV_S
      second: 0
    - first:
        name: _HSV_V
      second: 0
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Offset1
      second: 0
    - first:
        name: _Offset2
      second: 0
    - first:
        name: _Outline
      second: 1.5
    - first:
        name: _Parallax
      second: 0.02
    - first:
        name: _RampSmooth
      second: 0.1
    - first:
        name: _RampSmoothAdd
      second: 0.1
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RimMax
      second: 0.8
    - first:
        name: _RimMin
      second: 0.6
    - first:
        name: _RimStrength
      second: 0.25
    - first:
        name: _Shininess
      second: 2
    - first:
        name: _SketchHalftoneMax
      second: 1
    - first:
        name: _SketchHalftoneMin
      second: 0
    - first:
        name: _SketchSpeed
      second: 6
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecBlend
      second: 0.842
    - first:
        name: _SpecSmooth
      second: 1
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _SrcBlendOutline
      second: 5
    - first:
        name: _StencilRef
      second: 1
    - first:
        name: _TCP2_DISABLE_WRAPPED_LIGHT
      second: 1
    - first:
        name: _TCP2_OUTLINE_CONST_SIZE
      second: 0
    - first:
        name: _TCP2_OUTLINE_TEXTURED
      second: 0
    - first:
        name: _TCP2_RAMPTEXT
      second: 0
    - first:
        name: _TCP2_SPEC_TOON
      second: 1
    - first:
        name: _TCP2_STYLIZED_FRESNEL
      second: 1
    - first:
        name: _TCP2_ZSMOOTH_ON
      second: 0
    - first:
        name: _TexLod
      second: 5
    - first:
        name: _ThresholdScale
      second: 10
    - first:
        name: _ThresholdStrength
      second: 1
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _ZSmooth
      second: -0.5
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __dummy__
      second: 0
    - first:
        name: __outline_gui_dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    - first:
        name: _DiffTint
      second: {r: 0.2205882, g: 0.09584185, b: 0, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _HColor
      second: {r: 0.9117647, g: 0.9117647, b: 0.9117647, a: 1}
    - first:
        name: _OutlineColor
      second: {r: 0, g: 0, b: 0, a: 0.459}
    - first:
        name: _RimColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _SColor
      second: {r: 0.27930364, g: 0.4422383, b: 0.60294116, a: 1}
    - first:
        name: _SketchColor
      second: {r: 0.5588235, g: 0.5588235, b: 0.5588235, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.05644801, g: 0.07766688, b: 0.12800002, a: 1}
