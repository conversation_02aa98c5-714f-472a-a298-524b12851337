﻿







//#define COROUTINEMGR_DEBUG       //打开这个宏会使CoroutineMgr输出更多调试信息，可以用来帮助Debug和性能优化。

/// <summary>
/// CoroutineMgr.cs
/// Moved by wangxiangwei 2015-3-19
/// Native协程的实现
/// 本class作为组件，挂接到GameRoot对象身上
///
/// 和系统的协程区别是：
/// 1. 这个协程有catch报错，而系统没有，可以防止手机上闪退
/// 2. 本协程调度可以不依赖于具体的gameobject（因为gameobject析构或者隐藏后，其挂载的协程就停止运行了）
/// </summary>

using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace TFW
{
    public class CoroutineMgr : MonoBehaviour
    {
        #region 成员变量

        // 协程错误回调的委托
        public delegate void CoroutineErrorCallback();
         

        static CoroutineMgr _mInstance;
        // 单件
        private static CoroutineMgr mInstance
        {
            get
            {
                if (_mInstance == null)
                {
                    _mInstance=new GameObject("CoroutineMgr").AddComponent<CoroutineMgr>();
                }

                return _mInstance;
            }
        }

        // 当前运行中的所有协程列表
        private static Dictionary<string, CoroutineProxy> mCoroutionMap = new Dictionary<string, CoroutineProxy>();

        // 当前所有的延迟调用信息
        private static Queue<DelayCallInfo> mDelayCallInfos_Automatic = new Queue<DelayCallInfo>();
        private static List<DelayCallInfo> mDelayCallInfos_Frame = new List<DelayCallInfo>();
        private static List<DelayCallInfo> mDelayCallInfos_Time = new List<DelayCallInfo>();

        // 待添加的所有延迟调用信息。这些信息将在LateUpdate的时候被添加到m_DelayCallInfos中。
        private static List<DelayCallInfo> mToAddDelayCallInfos = new List<DelayCallInfo>();
        // 延迟调用信息对象池。
        private static Stack<DelayCallInfo> mDelayCallInfoPool = new Stack<DelayCallInfo>();

        // 单帧内执行的延迟调用的最小数量。只有延迟类型为Automatic的回调才会受该参数影响。
        public int minDelayCallCount = 5;

        // 单帧内执行的延迟调用的最大数量。只有延迟类型为Automatic的回调才会受该参数影响。
        public int maxDelayCallCount = 50;

        // 单帧内执行的延迟调用的最大处理时间。（单位：毫秒）
        public int maxDelayCallProcTime = 25;

        // 单帧内执行的延迟调用的阈值，超过这个阈值将加快处理
        public int delayCallCountThreshold = 25;

        // 单帧内执行的延迟调用的加快处理比率，每增加N个延迟调用，最小处理个数增1
        public int delayCallFastProcRatio = 25;

#if COROUTINEMGR_DEBUG
        // 协程数量，可通过GameRoot对象在编辑器中看到协程个数
        public int DebugCoroutineCount = 0;

        // Debug调试检查功能运行周期（秒）
        public float DebugCheckInterval = 15f;

        // 判定为长时运行的Coroutine的阈值时间（秒）
        public float DebugLongRunningCoroutineThreshold = 30f;
#endif

        #endregion

        #region MonoBehaviour生命周期


        private void Awake()
        {
            DontDestroyOnLoad(gameObject);

            D.Info?.Log("[CoroutineMgr] 协程管理器 CoroutineMgr 组件.");
        }

        /// <summary>
        /// 每帧更新
        /// </summary>
        private void Update()
        {
            // 每帧处理延迟调用
            ProcessDelayCalls(Time.deltaTime);

#if COROUTINEMGR_DEBUG
            // 这句只能写到对象实例身上，所以每帧更新即可
            DebugCoroutineCount = mCoroutionMap.Count;

            Debug_CheckCoroutines();
#endif
        }

        private void LateUpdate()
        {
            // 每次LateUpdate时将待添加列表中的延迟调用信息添加到正式列表中。
            if (mToAddDelayCallInfos.Count > 0)
            {
                foreach (var info in mToAddDelayCallInfos)
                {
                    var delayType = info.delayType;

                    switch (info.delayType)
                    {
                        case DelayCallType.Automatic:
                            mDelayCallInfos_Automatic.Enqueue(info);
                            break;

                        case DelayCallType.Frame:
                            mDelayCallInfos_Frame.Add(info);
                            break;

                        case DelayCallType.Time:
                            mDelayCallInfos_Time.Add(info);
                            break;

                        default:
                            Debug.LogError("CoroutineMgr: Invalid delay call type: " + delayType + ". Ignoring.");
                            ReleaseDelayCallInfo(info);
                            break;
                    }
                }

                mToAddDelayCallInfos.Clear();
            }
        }
        #endregion

#if COROUTINEMGR_DEBUG
        // 上次运行Debug检查功能的时间
        private float m_Debug_LastCheckTimer;
        // 当前活跃Couroutine数量
        private int m_Debug_ActiveCoroutineCount;
        // 当前Couroutine数量
        private int m_Debug_TotalCoroutineCount;
        // 峰值Couroutine数量
        private int m_Debug_PeakCoroutineCount;
        // 输出Debug信息用的StringBuilder
        private StringBuilder m_Debug_TimerCheckStringBuilder = new StringBuilder();

        /// <summary>
        /// Debug方法：检查所有
        /// </summary>
        private void Debug_CheckCoroutines()
        {
            var deltaTime = Time.deltaTime;

            m_Debug_ActiveCoroutineCount = 0;
            m_Debug_TotalCoroutineCount = 0;

            // 更新每个Coroutine的调试信息
            foreach (var coroutine in mCoroutionMap.Values)
            {
                coroutine.m_Debug_TotalExistTime += deltaTime;
                m_Debug_TotalCoroutineCount++;  // 更新Coroutine总数信息

                if (!coroutine.isFinished && coroutine.isRunning)
                {
                    coroutine.m_Debug_TotalRunningTime += deltaTime;
                    m_Debug_ActiveCoroutineCount++;  // 更新活跃Coroutine总数信息
                }

                if (m_Debug_PeakCoroutineCount < m_Debug_TotalCoroutineCount)
                {
                    m_Debug_PeakCoroutineCount = m_Debug_TotalCoroutineCount;  // 更新峰值Coroutine总数信息
                }
            }

            // 每隔DebugCheckInterval检查一次Coroutine运行时间
            m_Debug_LastCheckTimer += deltaTime;
            if (m_Debug_LastCheckTimer < DebugCheckInterval)
            {
                return;
            }
            m_Debug_LastCheckTimer = 0f;

            int longRunningCoroutineCount = 0;

            m_Debug_TimerCheckStringBuilder.Clear();
            m_Debug_TimerCheckStringBuilder.AppendFormat("[CoroutineMgr] Checking Coroutines. ActiveCoroutineCount={0}, TotalCoroutineCount={1}, PeakCoroutineCount={2}\n",
                                                          m_Debug_ActiveCoroutineCount, m_Debug_TotalCoroutineCount, m_Debug_PeakCoroutineCount);

            // 检查每个Coroutine
            foreach (var coroutine in mCoroutionMap.Values)
            {
                // 检查长时运行的Timer
                if (coroutine.m_Debug_TotalExistTime > DebugLongRunningCoroutineThreshold)
                {
                    longRunningCoroutineCount++;

                    m_Debug_TimerCheckStringBuilder.AppendFormat("LongRunningCoroutine({0}s, {1}s total)：Name='{2}', Owner='{3}', Func='{4}'\n",
                                                                 coroutine.m_Debug_TotalRunningTime,
                                                                 coroutine.m_Debug_TotalExistTime,
                                                                 coroutine.mName,
                                                                 coroutine.mOwner?.name ?? "(null)",
                                                                 coroutine.mIEnumerator?.ToString() ?? "(null)");
                }
            }

            if (m_Debug_TimerCheckStringBuilder.Length > 0)
            {
                Debug.LogWarning(m_Debug_TimerCheckStringBuilder.ToString());
            }
        }
#endif

        /// <summary>
        /// 安全协程步进，这里主要是增加了try catch，以及处理从YieldObject继承的我们自己的协程类
        /// </summary>
        /// <param name="cp"></param>
        /// <returns></returns>
        private static IEnumerator SafeCoroutine(CoroutineProxy cp)
        {
            while (true)
            {
                if (!cp.isFinished && cp.isRunning)
                {
#if false // GL2提倡直接crash
                try
                {
                    object yieldObj = cp.mIEnumerator.Current;
                    if (yieldObj != null && yieldObj is IYieldObject)
                    {
                        // 我们自己的协程类
                        if ((yieldObj as IYieldObject).IsDone())
                            cp.isFinished = !cp.mIEnumerator.MoveNext();
                    } else
                    {
                        // 步进
                        cp.isFinished = !cp.mIEnumerator.MoveNext();
                    }
                } catch (Exception e)
                {
                    cp.isFinished = true;
                    Debug.LogError(e.ToString());
                    try
                    {
                        if (cp.mErrorCb != null)
                            cp.mErrorCb();
                    } catch (Exception eb)
                    {
                        Debug.LogError(eb.ToString());
                    }
                }
#else
                    object yieldObj = cp.mIEnumerator.Current;
                    if (yieldObj != null && yieldObj is IYieldObject)
                    {
                        // 我们自己的协程类
                        if ((yieldObj as IYieldObject).IsDone())
                            cp.isFinished = !cp.mIEnumerator.MoveNext();
                    }
                    else
                    {
                        // 步进
                        cp.isFinished = !cp.mIEnumerator.MoveNext();
                    }
#endif
                }
                if (cp.isFinished)
                {
                    cp.Finish();
                    yield break;
                }
                else
                {
                    yield return cp.mIEnumerator.Current;
                }
            }
        }

        #region 延迟调用相关功能
        /// <summary>
        /// 延迟调用快捷接口。
        /// 这个接口会至少延迟1帧调用传入的回调。
        /// 回调的具体时机由CoroutineMgr根据每帧要调用的回调数量决定。
        /// </summary>
        /// <param name="action"></param>
        public static void DelayCall(Action action)
        {
            if (action == null) { throw new ArgumentNullException(nameof(action)); }

            var delayCallInfo = GetDelayCallInfo();

            delayCallInfo.delayType = DelayCallType.Automatic;
            delayCallInfo.callback = action;

            mToAddDelayCallInfos.Add(delayCallInfo);
        }

        /// <summary>
        /// 延迟调用快捷接口。
        /// 这个接口会至少延迟1帧调用传入的回调。
        /// 回调的具体时机由CoroutineMgr根据每帧的负载情况决定。
        /// </summary>
        /// <param name="action"></param>
        public static void DelayCall<T>(Action<T> action, T args)
        {
            if (action == null) { throw new ArgumentNullException(nameof(action)); }

            var delayCallInfo = GetDelayCallInfo();

            delayCallInfo.delayType = DelayCallType.Automatic;
            delayCallInfo.callbackWithArgs = action;
            delayCallInfo.args[0] = args;

            mToAddDelayCallInfos.Add(delayCallInfo);
        }

        /// <summary>
        /// 延迟调用快捷接口。
        /// 这个接口会至少延迟1帧调用传入的回调。
        /// 回调的具体时机由CoroutineMgr根据每帧的负载情况决定。
        /// </summary>
        /// <param name="action"></param>
        public static void DelayCall<T, U>(Action<T, U> action, T args, U args1)
        {
            if (action == null) { throw new ArgumentNullException(nameof(action)); }

            var delayCallInfo = GetDelayCallInfo();

            delayCallInfo.delayType = DelayCallType.Automatic;
            delayCallInfo.callbackWithArgs = action;
            delayCallInfo.args[0] = args;
            delayCallInfo.args[1] = args1;

            mToAddDelayCallInfos.Add(delayCallInfo);
        }

        /// <summary>
        /// 延迟N帧调用快捷接口。
        /// </summary>
        /// <param name="frame">要延迟的帧数，最小值1，传入小于1的值时将延迟1帧。</param>
        /// <param name="action"></param>
        public static void DelayFrameCall(int frame, Action action)
        {
            if (action == null) { throw new ArgumentNullException(nameof(action)); }

            var delayCallInfo = GetDelayCallInfo();

            delayCallInfo.delayType = DelayCallType.Frame;
            delayCallInfo.delayFrame = frame;
            delayCallInfo.callback = action;

            mToAddDelayCallInfos.Add(delayCallInfo);
        }

        /// <summary>
        /// 延迟调用快捷接口
        /// </summary>
        /// <param name="delay"></param>
        /// <param name="action"></param>
        public static void DelayTimeCall(float delay, Action action)
        {
            if (action == null) { throw new ArgumentNullException(nameof(action)); }

            var delayCallInfo = GetDelayCallInfo();

            delayCallInfo.delayType = DelayCallType.Time;
            delayCallInfo.delayTime = delay;
            delayCallInfo.callback = action;

            mToAddDelayCallInfos.Add(delayCallInfo);
        }

        /// <summary>
        /// 处理所有延迟调用
        /// </summary>
        private void ProcessDelayCalls(float deltaTime)
        {
            var delayCallInfos_Frame = mDelayCallInfos_Frame;

            if (delayCallInfos_Frame.Count > 0)
            {
                for (int i = delayCallInfos_Frame.Count - 1; i >= 0; i--)
                {
                    var info = delayCallInfos_Frame[i];

                    if (--info.delayFrame <= 0)
                    {
                        try
                        {
                            info.callback?.Invoke();

                            if (info.callbackWithArgs != null)
                            {
                                info.callbackWithArgs.Method.Invoke(info.callbackWithArgs.Target, info.args);
                            }
                        }
                        catch (Exception e)
                        {
                            Debug.LogException(e);
                        }

                        delayCallInfos_Frame.RemoveAt(i);
                        ReleaseDelayCallInfo(info);
                    }
                }
            }

            var delayCallInfos_Time = mDelayCallInfos_Time;

            if (delayCallInfos_Time.Count > 0)
            {
                for (int i = delayCallInfos_Time.Count - 1; i >= 0; i--)
                {
                    var info = delayCallInfos_Time[i];

                    if ((info.delayTime -= deltaTime) <= 0)
                    {
                        try
                        {
                            info.callback?.Invoke();

                            if (info.callbackWithArgs != null)
                            {
                                info.callbackWithArgs.Method.Invoke(info.callbackWithArgs.Target, info.args);
                            }
                        }
                        catch (Exception e)
                        {
                            Debug.LogException(e);
                        }

                        delayCallInfos_Time.RemoveAt(i);
                        ReleaseDelayCallInfo(info);
                    }
                }
            }

            var delayCallInfos_Automatic = mDelayCallInfos_Automatic;

            if (delayCallInfos_Automatic.Count > 0)
            {
                var tick = PerformanceMgr.realtimeTickTime;
                var currentCallCount = 0;

                var tmpMinDelayCallCount = minDelayCallCount;

                if (delayCallInfos_Automatic.Count > delayCallCountThreshold)
                {
                    tmpMinDelayCallCount += 1 + (delayCallInfos_Automatic.Count - delayCallCountThreshold) / delayCallFastProcRatio;
                }

                while (delayCallInfos_Automatic.Count > 0)
                {
                    var info = delayCallInfos_Automatic.Dequeue();

                    currentCallCount++;

                    try
                    {
                        info.callback?.Invoke();

                        if (info.callbackWithArgs != null)
                        {
                            info.callbackWithArgs.Method.Invoke(info.callbackWithArgs.Target, info.args);
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogException(e);
                    }

                    ReleaseDelayCallInfo(info);

                    if (tick + maxDelayCallProcTime < PerformanceMgr.realtimeTickTime)  // 限制最大处理时间
                    {
                        break;
                    }
                    else if (currentCallCount > maxDelayCallCount)  // 限制最大处理数量
                    {
                        break;
                    }
                    else if ((currentCallCount > minDelayCallCount && PerformanceMgr.realtimeFrameTimeBudget <= 0))  // 限制动态处理数量
                    {
                        break;
                    }

                    //UnityEngine.Debug.LogWarning("CoroutineMgr: currentCallCount = " + currentCallCount);
                }
            }
        }

        /// <summary>
        /// 获取延迟调用信息的实例
        /// </summary>
        /// <returns></returns>
        private static DelayCallInfo GetDelayCallInfo()
        {
            if (mDelayCallInfoPool.Count > 0)
            {
                return mDelayCallInfoPool.Pop();
            }

            return new DelayCallInfo();
        }

        /// <summary>
        /// 归还延迟调用信息的实例
        /// </summary>
        /// <param name="info"></param>
        private static void ReleaseDelayCallInfo(DelayCallInfo info)
        {
            info.Reset();
            mDelayCallInfoPool.Push(info);
        }
        #endregion

        /// <summary>
        /// 查找指定名字的协程是否存在
        /// </summary>
        /// <returns><c>true</c> if has coroutine the specified name; otherwise, <c>false</c>.</returns>
        /// <param name="name">Name.</param>
        public static bool HasCoroutine(string name)
        {
            return mCoroutionMap.ContainsKey(name);
        }

        private static int endfix = 0;

        public static string GetUniqueName(string prefix)
        {
            return string.Format("{0}_{1}_{2}", prefix, endfix++, DateTime.Now.Ticks);
        }
        /// <summary>
        /// 开启一个协程
        /// </summary>
        /// <returns>协程对象.</returns>
        /// <param name="i">迭代目标.</param>
        /// <param name="name">协程名，便于stop操作的时候识别，默认会生成唯一字符.</param>
        /// <param name="owner">协程挂接对象，默认为GameRootObject.</param>
        /// <param name="errcb">报错回调，当协程异常的时候，catch后执行本回调.</param>
        public static Coroutine StartCoroutine(IEnumerator i, string name = null, UnityEngine.Object owner = null, CoroutineErrorCallback errcb = null)
        { 
            // 协程数量上限，一般来说是当逻辑出现问题的时候才会溢出
            if (mCoroutionMap.Count > 1000)
            {
                Debug.LogWarningFormat("Coroutine数量太多!");
            }

            // 没有名字，自动取一个
            if (string.IsNullOrEmpty(name))
                name = GetUniqueName("AnonymousCoroutine");
            //if (mCoroutionMap.ContainsKey(name))
            //{
            //    Debug.LogWarningFormat("{0}, 失败!", name);
            //    StopCoroutine(name);
            //}

            //// 创建协程对象，安全执行
            //CoroutineProxy cp = new CoroutineProxy(name, i, owner, errcb);
            //mCoroutionMap.Add(name, cp);
            //IEnumerator si = SafeCoroutine(cp);
            //return (mInstance as MonoBehaviour).StartCoroutine(si);

            // 创建协程对象，安全执行
            CoroutineProxy cp = null;

            if (mCoroutionMap.TryGetValue(name, out cp))
            {
                Debug.LogWarningFormat("{0}, 失败!", name);
                StopCoroutine(cp);
            }

            // 创建协程对象，安全执行
            cp = new CoroutineProxy(name, i, owner, errcb);
            mCoroutionMap.Add(name, cp);
            IEnumerator si = SafeCoroutine(cp);
            return (mInstance as MonoBehaviour).StartCoroutine(si);
        }

        /// <summary>
        /// 停止一个协程
        /// </summary>
        /// <param name="name">Name.</param>
        public static new void StopCoroutine(string name)
        {
            CoroutineProxy cp;
            if (mCoroutionMap.TryGetValue(name, out cp))
            {
                cp.isFinished = true;
                mCoroutionMap.Remove(name);

#if COROUTINEMGR_DEBUG
                Debug.Log($"协程Stop{name}");
#endif

            }
        }

        /// <summary>
        /// 停止一个协程
        /// </summary>
        /// <param name="name">Name.</param>
        private static void StopCoroutine(CoroutineProxy cp)
        {
            if (cp == null || mCoroutionMap == null)
                return;

            cp.isFinished = true;
            mCoroutionMap.Remove(cp.mName);

            //CoroutineProxy cp;
            //if (mCoroutionMap.TryGetValue(name, out cp))
            //{
            //    cp.isFinished = true;
            //    mCoroutionMap.Remove(name);
            //}
        }

        /// <summary>
        /// 停止所有协程.
        /// </summary>
        public static new void StopAllCoroutines()
        {
            foreach (var cp in mCoroutionMap.Values)
                cp.isFinished = true;
            mCoroutionMap.Clear();
        }

        /// <summary>
        /// 挂起协程
        /// </summary>
        /// <param name="name">Name.</param>
        public static void SuspendCoroutine(string name)
        {
            CoroutineProxy cp;
            if (mCoroutionMap.TryGetValue(name, out cp))
            {
                cp.isRunning = false;
            }
        }

        /// <summary>
        /// 继续运行挂起的协程
        /// </summary>
        /// <param name="name">Name.</param>
        public static void ResumeCoroutine(string name)
        {
            CoroutineProxy cp;
            if (mCoroutionMap.TryGetValue(name, out cp))
            {
                cp.isRunning = true;
            }
        }

        #region 内部类

        // 协程委托内部类，封装部分native操作
        private class CoroutineProxy
        {
            public string mName;
            public IEnumerator mIEnumerator;
            public UnityEngine.Object mOwner;
            public CoroutineErrorCallback mErrorCb;

#if COROUTINEMGR_DEBUG
            // 调试参数：总的执行时间
            public float m_Debug_TotalRunningTime;
            // 调试参数：总的存在时间（存在于mCoroutineMap中的总时间）
            public float m_Debug_TotalExistTime;
            // 调试参数：关联的Coroutine实例
            public Coroutine m_Debug_Coroutine;
#endif

            // 构造函数
            public CoroutineProxy(string name, IEnumerator i, UnityEngine.Object owner, CoroutineErrorCallback errcb)
            {
                mName = name;
                mIEnumerator = i;
                mOwner = owner != null ? owner : mInstance.gameObject;
                mErrorCb = errcb;
            }

            // 是否结束
            bool mIsFinished = false;
            public bool isFinished
            {
                get
                {
                    if (mIsFinished)
                        return true;
                    return mOwner == null;
                }
                set
                {
                    mIsFinished = value;
                }
            }

            public void Finish()
            {
                isFinished = true;
                mCoroutionMap.Remove(mName);
            }

            // 是否执行中
            bool mIsRunning = true;
            public bool isRunning { get { return mIsRunning; } set { mIsRunning = value; } }
        }

        // 延迟调用信息类
        private class DelayCallInfo
        {
            // 延迟类型
            public DelayCallType delayType;
            // 要延迟的帧数
            public int delayFrame;
            // 要延迟的时间（秒）
            public float delayTime;
            // 回调
            public Action callback;
            // 带参数的回调
            public Delegate callbackWithArgs;
            // 回调参数
            public object[] args = new object[2];

            public void Reset()
            {
                delayType = DelayCallType.Automatic;
                delayFrame = 0;
                delayTime = 0f;
                callback = null;
                callbackWithArgs = null;
                args[0] = null;
                args[1] = null;
            }
        }

        // 延迟回调类型
        private enum DelayCallType
        {
            // 自动。CoroutineMgr将根据当前负载决定执行时机
            Automatic,
            // 按帧延迟
            Frame,
            // 按时间延迟
            Time,
        }

        #endregion
    }

    /// <summary>
    /// I yield object.
    /// </summary>
    public interface IYieldObject
    {
        bool IsDone();
    }
}