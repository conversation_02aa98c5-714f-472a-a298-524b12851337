%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Four Splats First Pass Terrain
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18506\n437;92;820;529;1391.052;-2467.193;1.770204;True;False\nNode;AmplifyShaderEditor.CommentaryNode;209;-339.216,268.535;Inherit;False;408.144;170.0303;;2;46;47;Old
    Smoothness;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;170;296.9723,2913.144;Inherit;False;827.0509;423.9417;;10;149;158;157;156;155;151;153;152;150;154;Mask
    Smoothness;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;159;252.6284,2169.96;Inherit;False;917.8029;677.2048;;10;140;139;138;130;113;112;114;137;111;141;Compute
    Masks;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;129;-954.9393,2741.565;Inherit;False;1210.908;2784.683;;21;124;123;122;128;127;125;126;132;133;134;135;131;136;121;103;104;105;106;188;189;191;Mask
    Variables;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;182;303.4217,3942.71;Inherit;False;812.6509;423.9421;;10;177;176;181;180;175;174;173;172;179;178;Mask
    Occlusion;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;171;303.88,3430.155;Inherit;False;807.0508;423.9419;;10;165;164;169;168;163;162;161;160;166;167;Mask
    Metallic;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;68;-84.15448,1030.644;Inherit;False;1134.33;398.6418;Comment;8;17;77;80;75;78;16;76;81;Tangents;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;199;416.7253,4483.475;Inherit;False;1281.122;462.335;;9;201;198;197;218;200;192;195;196;193;Occlusion;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;69;97.99105,251.4516;Inherit;False;1299.523;553.9363;;9;217;220;212;213;216;45;58;215;214;Smoothness;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;67;-23.4915,-70.91965;Inherit;False;686.1986;184.0211;;3;27;19;62;Alpha;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;64;-1838.406,612.7104;Inherit;False;1582.024;883.2601;;14;11;73;71;72;10;1;70;14;12;8;2;61;84;85;Normal;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;95;-70.55766,-610.683;Inherit;False;758.3281;346.0931;;3;99;100;98;Holes;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;63;-2707.89,-60.62326;Inherit;False;1931.073;500.6417;Comment;10;24;20;5;74;22;59;23;26;25;21;Control;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;66;-75.88382,1489.471;Inherit;False;1596.247;581.4266;;13;54;56;57;48;49;50;203;53;219;202;55;204;51;Metallic;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;65;-1778.84,-1982.8;Inherit;False;1604.401;1763.963;;27;4;6;7;35;32;36;60;28;42;33;29;3;30;41;9;40;38;31;39;34;37;44;43;205;206;207;210;Albedo;1,1,1,1;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;33;-1437.993,-1929.072;Inherit;False;FLOAT4;4;0;FLOAT;1;False;1;FLOAT;1;False;2;FLOAT;1;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;151;349.7676,3046.995;Inherit;False;139;mask1;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SamplerNode;11;-1568,1280;Inherit;True;Property;_Normal3;Normal3;9;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DynamicAppendNode;168;731.5413,3553.301;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;162;358.0726,3738.697;Inherit;False;141;mask3;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;31;-1704.334,-987.735;Float;False;Property;_Smoothness2;Smoothness2;13;1;[HideInInspector];Create;True;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;43;-1591.952,-656.6342;Float;False;Constant;_Float4;Float
    4;13;0;Create;True;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TangentVertexDataNode;77;-72.23888,1072.639;Inherit;False;0;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;113;305.6313,2556.965;Inherit;False;0;6;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;72;-1808,1072;Inherit;False;0;6;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;195;441.4373,4605.778;Inherit;False;191;defaultOcclusion;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;165;529.8993,3566.496;Inherit;False;FLOAT;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;10;-1568,1072;Inherit;True;Property;_Normal2;Normal2;8;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;152;346.9723,3130.847;Inherit;False;140;mask2;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;48;-25.88388,1619.345;Float;False;Property;_Metallic0;Metallic0;14;2;[HideInInspector];[Gamma];Create;True;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;78;152.2551,1153.509;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;156;522.9917,3129.485;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;1;-1568,864;Inherit;True;Property;_Normal1;Normal1;7;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;169;886.1307,3557.652;Inherit;False;maskMetallic;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionSwitch;85;-614.1091,1096.369;Inherit;False;Draw
    SRP Per Pixel Normals;True;0;2;-1;In 0;In 1;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;59;-1356.444,20.82132;Inherit;False;Custom
    Control;4;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;191;-119.3642,3742.854;Inherit;False;defaultOcclusion;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;23;-1712,16;Inherit;False;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;5;-2698.008,-13.10038;Inherit;True;Property;_Control;Control;1;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;100;514.496,-539.1801;Inherit;False;#ifdef
    _ALPHATEST_ON$\tclip(Hole == 0.0f ? -1 : 1)@$#endif;1;True;1;True;Hole;FLOAT;0;In;;Inherit;False;ClipHoles;False;False;0;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;12;-898.4255,806.7103;Inherit;False;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-1208.126,-1416.042;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;35;-1204.12,-1877.288;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;27;26.5085,-20.91957;Inherit;False;22;SplatWeight;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;167;529.8993,3726.496;Inherit;False;FLOAT;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;28;-629.0479,-660.6064;Float;False;MixDiffuse;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;76;346.3839,1191.393;Float;False;v.ase_tangent.xyz
    = cross ( v.ase_normal, float3( 0, 0, 1 ) )@$v.ase_tangent.w = -1@;1;True;0;CalculateTangentsSRP;True;False;0;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;49;-24.21591,1708.193;Float;False;Property;_Metallic1;Metallic1;17;2;[HideInInspector];[Gamma];Create;True;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;190;-287.0641,3742.854;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;150;353.9599,2963.144;Inherit;False;138;mask0;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;62;288.6909,-19.04097;Inherit;False;Custom
    Alpha;1;6;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;32;-1705.907,-528.7023;Float;False;Property;_Smoothness3;Smoothness3;10;1;[HideInInspector];Create;True;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;166;529.8993,3646.496;Inherit;False;FLOAT;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;158;724.6343,3036.289;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;80;-45.98957,1349.331;Float;False;Constant;_Float5;Float
    5;17;0;Create;True;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;215;389.3123,504.1568;Inherit;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;196;440.4074,4680.303;Inherit;False;181;maskOcclusion;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;54;680.1399,1554.148;Inherit;False;26;SplatControl;1;0;OBJECT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;46;-289.2161,318.535;Inherit;False;28;MixDiffuse;1;0;OBJECT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;74;-2041.122,125.4553;Float;False;#if
    !defined(SHADER_API_MOBILE) && defined(TERRAIN_SPLAT_ADDPASS)$\tclip(SplatWeight
    == 0.0f ? -1 : 1)@$#endif;1;True;1;True;SplatWeight;FLOAT;0;In;;Float;False;SplatClip;False;False;0;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;60;-369.0532,-738.7761;Inherit;False;Custom
    Albedo;4;1;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;210;-428.2156,-1804.76;Inherit;False;defaultSmoothness;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;57;1177.389,1568.532;Inherit;False;Custom
    Metallic;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;192;664.4073,4664.303;Inherit;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TexturePropertyNode;104;-649.9539,2840.603;Inherit;True;Property;_Mask1;_Mask1;21;1;[HideInInspector];Create;True;0;0;True;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;103;-867.7384,2839.526;Inherit;True;Property;_Mask0;_Mask0;20;1;[HideInInspector];Create;True;0;0;True;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;106;-219.8126,2839.176;Inherit;True;Property;_Mask3;_Mask3;22;1;[HideInInspector];Create;True;0;0;True;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;105;-434.9858,2836.898;Inherit;True;Property;_Mask2;_Mask2;19;1;[HideInInspector];Create;True;0;0;True;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;212;101.0529,324.0095;Inherit;False;210;defaultSmoothness;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;213;128.0529,504.0095;Inherit;False;149;maskSmoothness;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;217;414.0565,655.2891;Inherit;False;26;SplatControl;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;220;570.5651,410.0537;Inherit;False;4;0;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DotProductOpNode;216;817.0565,513.2891;Inherit;False;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;218;867.7135,4622.045;Inherit;False;4;0;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;111;302.6284,2306.024;Inherit;False;0;4;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;6;-1575.096,-907.3033;Inherit;True;Property;_Splat2;Splat2;3;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;155;522.9917,3049.485;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;99;252.4595,-530.1924;Inherit;False;holeClipValue;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;39;-1432.712,-1076.035;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SamplerNode;4;-1571.735,-1779.668;Inherit;True;Property;_Splat0;Splat0;5;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;161;353.88,3647.859;Inherit;False;140;mask2;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;36;-1399.088,-1520.375;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;164;529.8993,3486.496;Inherit;False;FLOAT;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;50;-19.21585,1806.193;Float;False;Property;_Metallic2;Metallic2;15;2;[HideInInspector];[Gamma];Create;True;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;197;1089.656,4723.663;Inherit;False;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;163;356.6753,3564.007;Inherit;False;139;mask1;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;214;154.0529,668.0095;Inherit;False;136;layerHasMask;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;154;522.9917,2969.485;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;9;-835.2949,-654.9649;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;173;353.4217,4160.415;Inherit;False;140;mask2;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;193;440.4074,4760.303;Inherit;False;136;layerHasMask;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SummedBlendNode;8;-1106.425,902.7103;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;160;360.8676,3480.155;Inherit;False;138;mask0;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;81;-66.2998,1212.924;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;30;-1677.401,-1423.224;Float;False;Property;_Smoothness1;Smoothness1;11;1;[HideInInspector];Create;True;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;2;-1570.425,662.7104;Inherit;True;Property;_Normal0;Normal0;6;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.StaticSwitch;84;-1024.719,1119.15;Inherit;False;Property;_EnablePerpixelNormals;Enable
    Per-pixel Normals;18;0;Create;True;0;0;False;0;False;0;0;0;True;_TERRAIN_INSTANCED_PERPIXEL_NORMAL;Toggle;2;Key0;Key1;Create;True;False;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;34;-1585.608,-1932.8;Float;False;Constant;_Float1;Float
    1;13;0;Create;True;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;137;327.5631,2219.96;Inherit;False;136;layerHasMask;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;112;305.6313,2427.848;Inherit;False;0;3;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;176;529.441,3999.052;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;174;357.6143,4251.252;Inherit;False;141;mask3;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;139;929.6311,2444.965;Inherit;False;mask1;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;180;731.0833,4065.856;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionInput;61;-637.6442,821.6487;Inherit;False;Custom
    Normal;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;16;395.9061,1093.737;Float;False;v.tangent.xyz
    = cross ( v.normal, float3( 0, 0, 1 ) )@$v.tangent.w = -1@;1;True;0;CalculateTangentsStandard;True;False;0;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;20;-2392.622,167.361;Inherit;False;2;0;COLOR;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;141;940.2234,2675.739;Inherit;False;mask3;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;188;-485.4102,3647.58;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;42;-1414.952,-615.6342;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;75;647.9054,1106.737;Inherit;False;4;0;FLOAT;0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;102;791.2579,-704.1357;Inherit;False;Activate
    Holes;True;1;2;-1;In 0;In 1;Object;-1;9;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;40;-1595.099,-1079.418;Float;False;Constant;_Float3;Float
    3;13;0;Create;True;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;51;-8.215824,1904.193;Float;False;Property;_Metallic3;Metallic3;16;2;[HideInInspector];[Gamma];Create;True;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;140;944.0311,2564.296;Inherit;False;mask2;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;44;-1252.967,-522.7264;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;177;529.441,4079.052;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;172;360.4093,3992.71;Inherit;False;138;mask0;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;179;529.441,4239.051;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;70;-1808,688;Inherit;False;0;4;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;114;305.6313,2684.965;Inherit;False;0;7;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;204;418.8666,1933.974;Inherit;False;136;layerHasMask;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;133;-846.5417,5243.348;Inherit;False;Global;_LayerHasMask2;_LayerHasMask2;23;0;Create;True;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;135;-622.5417,5160.348;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;29;-1728.84,-1854.911;Float;False;Property;_Smoothness0;Smoothness0;12;1;[HideInInspector];Create;True;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;98;-48.01836,-552.3624;Inherit;True;Property;_TerrainHolesTexture;_TerrainHolesTexture;0;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;21;-2689.97,184.6759;Float;False;Constant;_Vector0;Vector
    0;9;0;Create;True;0;0;False;0;False;1,1,1,1;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;22;-2261.105,164.0728;Float;False;SplatWeight;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-1260.513,-932.1429;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;189;-460.6728,3882.633;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;26;-1568,16;Float;False;SplatControl;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RangedFloatNode;132;-848.5417,5153.348;Inherit;False;Global;_LayerHasMask1;_LayerHasMask1;23;0;Create;True;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;203;411.0664,1824.774;Inherit;False;169;maskMetallic;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;138;945.6311,2284.965;Inherit;False;mask0;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;47;-82.6719,320.1653;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;3;-1584.171,-1329.939;Inherit;True;Property;_Splat1;Splat1;4;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;157;522.9917,3209.485;Inherit;False;FLOAT;3;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;101;112.4228,-666.2932;Inherit;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;175;356.217,4076.562;Inherit;False;139;mask1;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;131;-861.5417,5073.348;Inherit;False;Global;_LayerHasMask0;_LayerHasMask0;23;0;Create;True;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;24;-1836.506,177.3755;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;153;351.1649,3221.686;Inherit;False;141;mask3;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;73;-1808,1312;Inherit;False;0;7;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;58;931.6936,507.8923;Inherit;False;Custom
    Smoothness;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;149;879.2237,3040.641;Inherit;False;maskSmoothness;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;71;-1808,880;Inherit;False;0;3;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;201;1243.743,4725.404;Inherit;False;Custom
    Occlusion;1;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;198;884.6371,4816.325;Inherit;False;26;SplatControl;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;181;885.6727,4070.208;Inherit;False;maskOcclusion;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SwizzleNode;178;529.441,4159.051;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;25;-2144,256;Float;False;Constant;_Float0;Float
    0;9;0;Create;True;0;0;False;0;False;0.001;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;205;-794.3522,-1895.012;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.LerpOp;202;657.944,1768.564;Inherit;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.DynamicAppendNode;55;297.546,1713.904;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.Vector4Node;126;-912,4112;Inherit;False;Global;_MaskMapRemapScale1;_MaskMapRemapScale1;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;134;-859.5417,5329.349;Inherit;False;Global;_LayerHasMask3;_LayerHasMask3;23;0;Create;True;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;219;829.7055,1679.817;Inherit;False;4;0;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;130;625.6312,2412.965;Inherit;False;masks0
    = 0.5h@$masks1 = 0.5h@$masks2 = 0.5h@$masks3 = 0.5h@$#ifdef _MASKMAP$masks0 =
    lerp(masks0, SAMPLE_TEXTURE2D(_Mask0, sampler_Mask0, uvMask0), hasMask.x)@$masks1
    = lerp(masks1, SAMPLE_TEXTURE2D(_Mask1, sampler_Mask0, uvMask1), hasMask.y)@$masks2
    = lerp(masks2, SAMPLE_TEXTURE2D(_Mask2, sampler_Mask0, uvMask2), hasMask.z)@$masks3
    = lerp(masks3, SAMPLE_TEXTURE2D(_Mask3, sampler_Mask0, uvMask3), hasMask.w)@$#endif$masks0
    *= _MaskMapRemapScale0.rgba@$masks0 += _MaskMapRemapOffset0.rgba@$masks1 *= _MaskMapRemapScale1.rgba@$masks1
    += _MaskMapRemapOffset1.rgba@$masks2 *= _MaskMapRemapScale2.rgba@$masks2 += _MaskMapRemapOffset2.rgba@$masks3
    *= _MaskMapRemapScale3.rgba@$masks3 += _MaskMapRemapOffset3.rgba@;7;False;9;True;masks0;FLOAT4;0,0,0,0;Out;;Inherit;False;True;masks1;FLOAT4;0,0,0,0;Out;;Inherit;False;True;masks2;FLOAT4;0,0,0,0;Out;;Inherit;False;True;masks3;FLOAT4;0,0,0,0;Out;;Inherit;False;False;hasMask;FLOAT4;0,0,0,0;In;;Inherit;False;False;uvMask0;FLOAT2;0,0;In;;Inherit;False;False;uvMask1;FLOAT2;0,0;In;;Inherit;False;False;uvMask2;FLOAT2;0,0;In;;Inherit;False;True;uvMask3;FLOAT2;0,0;In;;Inherit;False;ComputeMasks;True;False;0;10;0;FLOAT;0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;5;FLOAT4;0,0,0,0;False;6;FLOAT2;0,0;False;7;FLOAT2;0,0;False;8;FLOAT2;0,0;False;9;FLOAT2;0,0;False;5;FLOAT;0;FLOAT4;2;FLOAT4;3;FLOAT4;4;FLOAT4;5\nNode;AmplifyShaderEditor.DynamicAppendNode;206;-800,-1728;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SamplerNode;7;-1563.881,-422.9568;Inherit;True;Property;_Splat3;Splat3;2;1;[HideInInspector];Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;136;-415.5418,5176.348;Inherit;False;layerHasMask;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.Vector4Node;125;-912,3904;Inherit;False;Global;_MaskMapRemapScale0;_MaskMapRemapScale0;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;122;-930.8565,3265.366;Inherit;False;Global;_MaskMapRemapOffset1;_MaskMapRemapOffset1;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;123;-935.1711,3467.736;Inherit;False;Global;_MaskMapRemapOffset2;_MaskMapRemapOffset2;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;207;-595.396,-1831.095;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;37;-1553.258,-1504.617;Float;False;Constant;_Float2;Float
    2;13;0;Create;True;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;53;1011.853,1573.078;Inherit;False;2;0;COLOR;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;127;-912,4304;Inherit;False;Global;_MaskMapRemapScale2;_MaskMapRemapScale2;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;128;-912,4496;Inherit;False;Global;_MaskMapRemapScale3;_MaskMapRemapScale3;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;121;-941.6769,3065.909;Inherit;False;Global;_MaskMapRemapOffset0;_MaskMapRemapOffset0;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;124;-934.2458,3669.353;Inherit;False;Global;_MaskMapRemapOffset3;_MaskMapRemapOffset3;23;0;Create;True;0;0;True;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionOutput;14;-417.8965,823.1106;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;56;1366.832,1559.547;Inherit;False;False;-1;Metallic;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;45;1145.995,514.5017;Inherit;False;False;-1;Smoothness;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;19;493.604,-31.32466;Inherit;False;False;-1;Alpha;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1098.414,-685.4164;Inherit;False;True;-1;Albedo;0;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;200;1474.611,4755.851;Inherit;False;False;-1;Occlusion;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;17;887.9049,1074.737;Inherit;False;False;-1;Tangents;6;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;33;0;34;0\nWireConnection;33;1;34;0\nWireConnection;33;2;34;0\nWireConnection;33;3;29;0\nWireConnection;11;1;73;0\nWireConnection;168;0;164;0\nWireConnection;168;1;165;0\nWireConnection;168;2;166;0\nWireConnection;168;3;167;0\nWireConnection;165;0;163;0\nWireConnection;10;1;72;0\nWireConnection;78;0;77;0\nWireConnection;78;1;81;0\nWireConnection;78;2;80;0\nWireConnection;156;0;152;0\nWireConnection;1;1;71;0\nWireConnection;169;0;168;0\nWireConnection;85;0;61;0\nWireConnection;85;1;84;0\nWireConnection;59;0;26;0\nWireConnection;191;0;190;0\nWireConnection;23;0;5;0\nWireConnection;23;1;24;0\nWireConnection;100;0;101;0\nWireConnection;100;1;99;0\nWireConnection;12;0;8;0\nWireConnection;38;0;36;0\nWireConnection;38;1;3;0\nWireConnection;35;0;33;0\nWireConnection;35;1;4;0\nWireConnection;167;0;162;0\nWireConnection;28;0;9;0\nWireConnection;76;0;78;0\nWireConnection;190;0;188;0\nWireConnection;190;1;189;0\nWireConnection;62;0;27;0\nWireConnection;166;0;161;0\nWireConnection;158;0;154;0\nWireConnection;158;1;155;0\nWireConnection;158;2;156;0\nWireConnection;158;3;157;0\nWireConnection;215;0;212;0\nWireConnection;215;1;213;0\nWireConnection;215;2;214;0\nWireConnection;74;0;22;0\nWireConnection;74;1;22;0\nWireConnection;60;0;28;0\nWireConnection;210;0;207;0\nWireConnection;57;0;53;0\nWireConnection;192;0;195;0\nWireConnection;192;1;196;0\nWireConnection;192;2;193;0\nWireConnection;220;0;212;0\nWireConnection;220;3;212;0\nWireConnection;220;1;215;0\nWireConnection;220;2;215;0\nWireConnection;216;0;220;0\nWireConnection;216;1;217;0\nWireConnection;218;0;195;0\nWireConnection;218;3;195;0\nWireConnection;218;1;192;0\nWireConnection;218;2;192;0\nWireConnection;155;0;151;0\nWireConnection;99;0;98;1\nWireConnection;39;0;40;0\nWireConnection;39;1;40;0\nWireConnection;39;2;40;0\nWireConnection;39;3;31;0\nWireConnection;36;0;37;0\nWireConnection;36;1;37;0\nWireConnection;36;2;37;0\nWireConnection;36;3;30;0\nWireConnection;164;0;160;0\nWireConnection;197;0;218;0\nWireConnection;197;1;198;0\nWireConnection;154;0;150;0\nWireConnection;9;0;59;0\nWireConnection;9;1;35;0\nWireConnection;9;2;38;0\nWireConnection;9;3;41;0\nWireConnection;9;4;44;0\nWireConnection;8;0;59;0\nWireConnection;8;1;2;0\nWireConnection;8;2;1;0\nWireConnection;8;3;10;0\nWireConnection;8;4;11;0\nWireConnection;2;1;70;0\nWireConnection;84;1;61;0\nWireConnection;84;0;61;0\nWireConnection;176;0;172;0\nWireConnection;139;0;130;3\nWireConnection;180;0;176;0\nWireConnection;180;1;177;0\nWireConnection;180;2;178;0\nWireConnection;180;3;179;0\nWireConnection;61;0;12;0\nWireConnection;20;0;5;0\nWireConnection;20;1;21;0\nWireConnection;141;0;130;5\nWireConnection;188;0;121;2\nWireConnection;188;1;122;2\nWireConnection;188;2;123;2\nWireConnection;188;3;124;2\nWireConnection;42;0;43;0\nWireConnection;42;1;43;0\nWireConnection;42;2;43;0\nWireConnection;42;3;32;0\nWireConnection;75;0;16;0\nWireConnection;75;3;76;0\nWireConnection;75;1;76;0\nWireConnection;75;2;76;0\nWireConnection;102;0;60;0\nWireConnection;102;1;100;0\nWireConnection;140;0;130;4\nWireConnection;44;0;42;0\nWireConnection;44;1;7;0\nWireConnection;177;0;175;0\nWireConnection;179;0;174;0\nWireConnection;135;0;131;0\nWireConnection;135;1;132;0\nWireConnection;135;2;133;0\nWireConnection;135;3;134;0\nWireConnection;22;0;20;0\nWireConnection;41;0;39;0\nWireConnection;41;1;6;0\nWireConnection;189;0;125;2\nWireConnection;189;1;126;2\nWireConnection;189;2;127;2\nWireConnection;189;3;128;2\nWireConnection;26;0;23;0\nWireConnection;138;0;130;2\nWireConnection;47;0;46;0\nWireConnection;157;0;153;0\nWireConnection;101;0;60;0\nWireConnection;24;0;74;0\nWireConnection;24;1;25;0\nWireConnection;58;0;216;0\nWireConnection;149;0;158;0\nWireConnection;201;0;197;0\nWireConnection;181;0;180;0\nWireConnection;178;0;173;0\nWireConnection;205;0;29;0\nWireConnection;205;1;30;0\nWireConnection;205;2;31;0\nWireConnection;205;3;32;0\nWireConnection;202;0;55;0\nWireConnection;202;1;203;0\nWireConnection;202;2;204;0\nWireConnection;55;0;48;0\nWireConnection;55;1;49;0\nWireConnection;55;2;50;0\nWireConnection;55;3;51;0\nWireConnection;219;0;55;0\nWireConnection;219;3;55;0\nWireConnection;219;1;202;0\nWireConnection;219;2;202;0\nWireConnection;130;5;137;0\nWireConnection;130;6;111;0\nWireConnection;130;7;112;0\nWireConnection;130;8;113;0\nWireConnection;130;9;114;0\nWireConnection;206;0;4;4\nWireConnection;206;1;3;4\nWireConnection;206;2;6;4\nWireConnection;206;3;7;4\nWireConnection;136;0;135;0\nWireConnection;207;0;205;0\nWireConnection;207;1;206;0\nWireConnection;53;0;54;0\nWireConnection;53;1;219;0\nWireConnection;14;0;85;0\nWireConnection;56;0;57;0\nWireConnection;45;0;58;0\nWireConnection;19;0;62;0\nWireConnection;0;0;102;0\nWireConnection;200;0;201;0\nWireConnection;17;0;75;0\nASEEND*/\n//CHKSM=B5129B21BBF493FDBEDCD9047474F2BEBF168912"
  m_functionName: 
  m_description: Creates a Standard Terrain shader. On main shader the tag SplatCount
    must be created with a value of 4.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives:
    - {fileID: 0}
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 2
      LineValue: multi_compile_local __ _ALPHATEST_ON
      GUIDToggle: 0
      GUIDValue: 
      Origin: 2
    - LineType: 2
      LineValue: shader_feature_local _MASKMAP
      GUIDToggle: 0
      GUIDValue: 
      Origin: 2
  m_nodeCategory: 9
  m_customNodeCategory: Terrain
  m_previewPosition: 0
  m_hidden: 0
