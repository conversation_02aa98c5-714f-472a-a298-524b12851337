﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class CityWithResourceLODManager : CityLODManager
    {
        public override void AddCity(IBuildingElement city)
        {
            mCities.Add(city);
            mAllObjects.Add(city);

            if (!(city is IResourceNoLOD))
            {
                //把所有静态物体都放到最后,这样检测碰撞的时候可以简单点
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    if (mAllObjects[i] is IResourceNoLOD)
                    {
                        Swap(mAllObjects, i, mAllObjects.Count - 1);
                        break;
                    }
                }
            }

            OnAddCity(city);
        }

        void Swap(List<IBuildingElement> buildings, int a, int b)
        {
            IBuildingElement temp = buildings[a];
            buildings[a] = buildings[b];
            buildings[b] = temp;
        }

        //判断建筑之间的重叠关系
        protected override void CheckCityCollision()
        {
            float cameraHeight = MapCameraMgr.currentCameraHeight;
            if (cameraHeight >= mCityOpenCameraHeight)
            {
                //超过最大高度直接显示主建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    if (!mAllObjects[i].isMainBuilding)
                    {
                        mAllObjects[i].SetVisible(false);
                    }
                    else
                    {
                        mAllObjects[i].SetVisible(true);
                    }
                }
            }
            else if (Utils.LE(cameraHeight, mCityCollapseCameraHeight))
            {
                //超过最小高度直接显示所有建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    mAllObjects[i].SetVisible(true);
                }
            }
            else
#if true
            {
                //根据建筑的碰撞关系来显示建筑
                bool useCollisionList = true;
                float lastCameraHeight = MapCameraMgr.lastCameraHeight;
                bool cameraRising = cameraHeight > lastCameraHeight;

                if (mStaticObjectStates.Length != mAllObjects.Count)
                {
                    mStaticObjectStates = new byte[mAllObjects.Count];
                }
                else
                {
                    for (int i = 0; i < mAllObjects.Count; ++i)
                    {
                        mStaticObjectStates[i] = 0;
                    }
                }

                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    if (mAllObjects[i] is IResourceNoLOD)
                    {
                        break;
                    }
                    mStaticObjectStates[i] = 1;
                    var list = mAllObjects[i].GetCollisionList();
                    if (list == null)
                    {
                        useCollisionList = false;
                        list = mAllObjects;
                    }

                    bool isHitObj = false;
                    for (int j = 0; j < list.Count; ++j)
                    {
                        if (mAllObjects[i] != list[j])
                        {
                            if (IsObjectHit(mAllObjects[i], list[j]))
                            {
                                mStaticObjectStates[j] = 1;
                                if (list[j].isVisible)
                                {
                                    //隐藏priority大的建筑
                                    if (mAllObjects[i].GetPriority() < list[j].GetPriority())
                                    {
                                        if (mAllObjects[i].isVisible)
                                        {

                                            list[j].SetVisible(false);
                                        }
                                    }
                                    else
                                    {
                                        isHitObj = true;
                                        mAllObjects[i].SetVisible(false);
                                    }

                                    //record collisions
                                    if (useCollisionList == false)
                                    {
                                        mAllObjects[i].AddCollision(list[j]);
                                    }
                                }
                            }
                        }
                    }

                    if (isHitObj == false)
                    {
                        if (mAllObjects[i].isVisible == false)
                        {
                            if (!cameraRising)
                            {
                                mAllObjects[i].SetVisible(true);
                            }
                        }
                    }
                }

                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    if (mStaticObjectStates[i] == 0)
                    {
                        mAllObjects[i].SetVisible(true);
                    }
                }
            }
#else
            {
                //Debug.Log(gameObject.name + " Check collision at camera height: " + cameraHeight.ToString());
                //根据建筑的碰撞关系来显示建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    bool isHitObj = false;
                    var list = mAllObjects[i].GetPotentialCollidableBuildingList();
                    for (int j = 0; j < list.Count; ++j)
                    {
                        if (mAllObjects[i] != list[j])
                        {
                            if (IsObjectHit(mAllObjects[i], list[j]))
                            {
                                if (list[j].isVisible)
                                {
                                    //隐藏priority大的建筑
                                    if (mAllObjects[i].GetPriority() < list[j].GetPriority())
                                    {
                                        if (mAllObjects[i].isVisible)
                                        {
                                            list[j].SetVisible(false);
                                        }
                                    }
                                    else
                                    {
                                        isHitObj = true;
                                        mAllObjects[i].SetVisible(false);
                                    }
                                }
                            }
                        }
                    }

                    if (isHitObj == false)
                    {
                        if (mAllObjects[i].isVisible == false)
                        {
                            mAllObjects[i].SetVisible(true);
                        }
                    }
                }
            }
#endif
        }

        protected override bool IsObjectHit(IBuildingElement a, IBuildingElement b)
        {
            if (a is IBuilding && b is IBuilding)
            {
                var buildingA = a as IBuilding;
                var buildingB = b as IBuilding;
                return IsHit(buildingA, buildingA.scaledColliderRadius, buildingB, buildingB.scaledColliderRadius);
            }
            else if (a is IBuilding && b is IResourceNoLOD)
            {
                var buildingA = a as IBuilding;
                var buildingB = b as IResourceNoLOD;
                return IsHit(buildingA, buildingA.scaledColliderRadius, buildingB, buildingB.scaledColliderRadius);
            }
            else
            {
                CityWall wall;
                IBuilding city;
                if (a.GetType() == typeof(CityWall))
                {
                    wall = a as CityWall;
                    city = b as IBuilding;
                }
                else
                {
                    wall = b as CityWall;
                    city = a as IBuilding;
                }

                if (city == null)
                {
                    return false;
                }
                else if (city.isMainBuilding)
                {
                    if (!mHideWallWhenMainBuildingAtFinalPosition)
                    {
                        bool hit = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                        return hit;
                    }

                    if (city.isAtFinalPosition)
                    {
						//主建筑移动到城中心时才判断和城墙的碰撞
                        bool hit = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                        return hit;
                    }
                    return false;
                }

                bool hitWall = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                return hitWall;
            }
        }


        //使用世界坐标的圆来计算,这样可能会有建筑视觉上的重叠,但是碰撞检测效率高
        bool IsHit(IBuilding city0, float r0, IResourceNoLOD city1, float r1)
        {
            if (r0 == 0 || r1 == 0)
            {
                //半径为0表示不参与碰撞
                return true;
            }

            float r = r0 + r1;
            r *= r;
            var pos = city0.colliderCenter - city1.colliderCenter;
            if (pos.sqrMagnitude < r)
            {
                return true;
            }

            return false;
        }

        byte[] mStaticObjectStates = new byte[0];
    }
}
