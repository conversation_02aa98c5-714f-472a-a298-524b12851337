﻿ 



 
 



using System;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理某个resource的lod实现,脚本挂接到一个建筑上
    public class ResourceBuildingLODControl : KeepScaleBySize, IBuilding
    {
        public Action<bool> visibleChange;

        public void Init(BuildingLODManager manager)
        {
            if (manager != null)
            {
                mLODManager = manager;
                if (mRenderers.Count == 0)
                {
                    gameObject.GetComponentsInChildren<Renderer>(mRenderers);
                    for (int i = 0; i < mRenderers.Count; ++i)
                    {
                        //默认所有renderer都不显示
                        mRenderers[i].enabled = false;
                    }
                }

                if (mCollider == null)
                {
                    mCollider = gameObject.GetComponentInChildren<Collider>();
                }

                mLODManager.AddCity(this);

                mInitViewportWidth = Utils.CalculateViewportWidth(config.cameraFovWhenScaleIsOne, Map.currentMap);

                //在物体显示时根据相机的高度设置它初始的缩放值
                SetInitScale();

                UpdateCollider();

                mMaxColliderRadius = GetScaleAtHeight(GetScaleConfig().maximumCameraHeight, true);
                mMaxScale = mMaxColliderRadius;
                mMinScale = GetScaleAtHeight(GetScaleConfig().minimumCameraHeight, true);

                mCollisionManager.ClearCollisionList();

                PassUpperHeightLimitEvent += ReachHigher;
                PassLowerHeightLimitEvent += ReachLower;
            }
        }

        public void UnInit()
        {
            SetLODManager(null);
            PassUpperHeightLimitEvent -= ReachHigher;
            PassLowerHeightLimitEvent -= ReachLower;
        }

        void ReachHigher(float cameraHeight, bool upperScale)
        {
            mCollisionManager.ReachHigherHeight();
        }

        void ReachLower(float cameraHeight, bool upperScale)
        {
            mCollisionManager.ReachLowerHeight();
        }

        //根据相机的高度来更新建筑的缩放或位置
        public void UpdateCity()
        {
            bool updated = UpdateScaleImpl();
            UpdateCollider();
        }

        void UpdateCollider()
        {
            var t = transform;
            mColliderCenter = t.position + colliderOffset;
            mScaledColliderRadius = colliderRadius * t.lossyScale.x;
        }

        void OnDestroy()
        {
            if (mLODManager != null)
            {
                mLODManager.RemoveCity(this);
            }
        }

        //设置主城的移动信息
        //startPos:在相机高度为cameraHeightRange.x时主城的位置
        //endPos:在相机高度为cameraHeightRange.y时主城的位置
        //cameraHeightRange:主城移动期间相机的高度范围
        public void SetMoveInfo(Vector3 startPos, Vector3 endPos, Vector2 cameraHeightRange,
            Transform cityRootTransform)
        {
            Debug.Assert(mLODManager != null);
            if (mMoveToTarget == null)
            {
                mMoveToTarget = new LODMoveToTarget(transform, cityRootTransform);
            }

            mMoveToTarget.SetMoveInfo(startPos, endPos, cameraHeightRange);
            mUseDeltaScale = true;
            mMoveEndPosition = mMoveToTarget.worldEndPosition;
        }

        public void SetCityStartPosition(Vector3 startPos)
        {
            Debug.Assert(mLODManager != null);
            if (mMoveToTarget != null)
            {
                mMoveToTarget.SetStartPosition(startPos);
            }
        }

        //显示或隐藏建筑
        public void SetVisible(bool visible)
        {
            if (mIsVisible != visible)
            {
                Debug.Assert(mLODManager != null);
                mIsVisible = visible;
                for (int i = 0; i < mRenderers.Count; ++i)
                {
                    mRenderers[i].enabled = visible;
                }

                if (!System.Object.ReferenceEquals(visibleChange, null))
                {
                    visibleChange.Invoke(visible);
                }
            }
        }

        public void SetLODManager(BuildingLODManager lodManager)
        {
            mLODManager = lodManager;
        }

        public bool isMainBuilding
        {
            get { return mMoveToTarget != null; }
        }

        public bool isAtFinalPosition
        {
            get { return transform.position == mMoveEndPosition; }
        }

        public Vector3 colliderCenter
        {
            get { return mColliderCenter; }
        }

        void OnDrawGizmos()
        {
#if true
            if (mIsVisible)
            {
                Gizmos.DrawWireSphere(colliderCenter, scaledColliderRadius);

                var collider = gameObject.GetComponentInChildren<Collider>();
                if (collider != null)
                {
                    Gizmos.color = Color.red;
                    var boxCollider = collider as BoxCollider;
                    if (boxCollider != null)
                    {
                        Gizmos.matrix = collider.transform.localToWorldMatrix;
                        Gizmos.DrawWireCube(boxCollider.center, boxCollider.size);
                    }
                }
            }
#endif
        }

        public int GetPriority()
        {
            return hidePriority;
        }

        //建筑物未缩放时的半径,如果使用半径来做建筑的重叠判定时使用
        public float colliderRadius;

        //碰撞框的中心点偏移
        public Vector3 colliderOffset;

        //随建筑物缩放后的半径
        public float scaledColliderRadius
        {
            get { return mScaledColliderRadius; }
        }

        //建筑物是否可见
        public bool isVisible
        {
            get { return mIsVisible; }
        }

        //使用collider来判定建筑物重叠时使用
        public Collider objectCollider
        {
            get { return mCollider; }
        }

        public BuildingLODManager lodManager
        {
            get { return mLODManager; }
        }

        float GetScaleAtHeight(float cameraHeight, bool calculate)
        {
            var config = GetScaleConfig();
            if (cameraHeight >= config.maximumCameraHeight && mMaxScale != 0)
            {
                return mMaxScale;
            }
            else if (cameraHeight <= config.minimumCameraHeight && mMinScale != 0)
            {
                return mMinScale;
            }

            float fov, newWidth;
            if (calculate)
            {
                fov = Map.currentMap.GetCameraFOVAtHeight(cameraHeight);
                newWidth = Utils.CalculateViewportWidth(fov, Map.currentMap);
            }
            else
            {
                fov = BuildingLODManagerUpdateDataCache.fovAtCurrentCameraHeight;
                newWidth = BuildingLODManagerUpdateDataCache.viewportWidthAtCurrentCameraHeight;
            }

            float ratio = newWidth / mInitViewportWidth;

            float scaleFactor = mPrefabInitScale / config.cameraHeightWhenScaleIsBaseScale;
            float t = (cameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp(t, 0, 1.0f);

            float deltaScale = 1.0f;
            if (mUseDeltaScale)
            {
                deltaScale = 1 + maxDeltaScale * t;
            }
            return scaleFactor * cameraHeight * ratio * deltaScale;
        }

        protected override void SetScaleAtHeight(float cameraHeight)
        {
            float scale = GetScaleAtHeight(cameraHeight, false);
            transform.localScale = Vector3.one * scale;
        }

        protected override KeepScaleConfig GetScaleConfig()
        {
            return config;
        }

        public void ClearPotentialCollidableList()
        {
            mPotentialCollidableBuildings.Clear();
        }

        public void AddPotentialCollidableBuilding(IBuildingElement building)
        {
#if DEBUG
            Debug.Assert(mPotentialCollidableBuildings.Contains(building) == false);
#endif
            mPotentialCollidableBuildings.Add(building);
        }

        public List<IBuildingElement> GetPotentialCollidableBuildingList()
        {
            return mPotentialCollidableBuildings;
        }

        public void AddCollision(IBuildingElement building)
        {
            mCollisionManager.AddCollision(building);
        }

        public List<IBuildingElement> GetCollisionList()
        {
            return mCollisionManager.GetCollisionList();
        }

        public float maxColliderRadius { get { return mMaxColliderRadius; } }
        public int id { get { return gameObject.GetInstanceID(); } }

        //在建筑物重叠时hidePriority小的隐藏hidePriority大的建筑
        public int hidePriority;

        BuildingLODManager mLODManager;

        //建筑物子节点中所有的renderer
        List<Renderer> mRenderers = new List<Renderer>();
        Collider mCollider;

        bool mIsVisible = false;

        Vector3 mColliderCenter;
        float mScaledColliderRadius;

        //用于优化
        float mMaxScale;
        float mMinScale;

        float mInitViewportWidth;
        Vector3 mMoveEndPosition;

        float mMaxColliderRadius;
        List<IBuildingElement> mPotentialCollidableBuildings = new List<IBuildingElement>();

        //将建筑物在相机缩放时移动到目标点
        LODMoveToTarget mMoveToTarget;

        BuildingLODControlCollisionManager mCollisionManager = new BuildingLODControlCollisionManager(true);
    }
}