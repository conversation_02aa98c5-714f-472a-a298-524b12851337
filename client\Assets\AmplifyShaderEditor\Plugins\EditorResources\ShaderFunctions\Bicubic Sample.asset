%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Bicubic Sample
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18600\n0;537;1729;822;-467.7644;465.2727;1.320468;True;False\nNode;AmplifyShaderEditor.CommentaryNode;201;-1043.748,1734.264;Inherit;False;2226.074;1089.492;;25;205;187;192;188;204;203;202;176;196;195;194;193;191;190;189;186;185;184;183;182;181;180;179;178;177;Fetch
    2D;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;130;-1074.157,-571.3032;Inherit;False;2248.869;991.9108;;28;126;131;40;4;44;45;120;124;122;121;119;118;2;123;125;127;66;128;43;6;7;46;68;67;129;167;168;169;2D;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;71;1388.118,-555.2822;Inherit;False;1546.347;970.757;;15;84;82;85;83;86;72;80;76;74;88;81;79;73;87;78;Outputs;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;89;-2721.481,-555.8615;Inherit;False;1378.169;964.4158;;17;197;199;94;97;198;105;96;200;99;102;98;92;91;93;101;103;100;Inputs;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;132;-1059.981,593.8146;Inherit;False;2248.869;991.9108;;33;157;156;155;154;153;152;151;150;149;148;147;146;145;144;142;141;140;139;138;137;136;135;134;133;158;159;160;161;162;163;164;165;166;2D
    Array;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;206;-1034.245,2999.804;Inherit;False;2216.924;1095.592;;32;231;230;229;228;227;226;225;224;223;222;221;220;219;218;217;216;215;214;213;212;211;210;209;208;207;232;233;234;235;236;237;238;Fetch
    2D Array;1,1,1,1;0;0\nNode;AmplifyShaderEditor.WireNode;218;-269.7177,3218.184;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;217;-272.4329,3188.314;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;191;-311.0844,1978.569;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SwizzleNode;214;-540.3856,3483.624;Inherit;False;FLOAT2;1;2;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;213;-540.3856,3663.624;Inherit;False;FLOAT2;1;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;219;-287.368,3242.624;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.GetLocalVarNode;227;-924.5254,3528.712;Inherit;False;197;Input_FetchOffsets;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;189;-296.1492,1924.26;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;233;-190.7924,3352.336;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;127;-95.72092,292.4236;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;226;517.788,3744.942;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;234;-201.4768,3473.426;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;180;-110.8541,2465.857;Inherit;True;Property;_TextureSample11;Texture
    Sample 11;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;181;318.6297,2397.643;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.LerpOp;224;728.2203,3446.359;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.LerpOp;176;704.5042,2182.305;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;207;-90.20921,3317.818;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;177;-113.9256,2053.764;Inherit;True;Property;_TextureSample8;Texture
    Sample 8;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;158;-740.9282,796.8607;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SwizzleNode;216;-542.3856,3574.624;Inherit;False;FLOAT2;0;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.CustomExpressionNode;145;-657.9156,1104.576;Inherit;False;{$
    UV = UV * TexelSize.zw - 0.5@$    float2 f = frac( UV )@$    UV -= f@$$    float4
    xn = float4( 1.0, 2.0, 3.0, 4.0 ) - f.xxxx@$    float4 yn = float4( 1.0, 2.0,
    3.0, 4.0 ) - f.yyyy@$$    float4 xs = xn * xn * xn@$    float4 ys = yn * yn *
    yn@$$    float3 xv = float3( xs.x, xs.y - 4.0 * xs.x, xs.z - 4.0 * xs.y + 6.0
    * xs.x )@$    float3 yv = float3( ys.x, ys.y - 4.0 * ys.x, ys.z - 4.0 * ys.y +
    6.0 * ys.x )@$    float4 xc = float4( xv.xyz, 6.0 - xv.x - xv.y - xv.z )@$ float4
    yc = float4( yv.xyz, 6.0 - yv.x - yv.y - yv.z )@$$    float4 c = float4( UV.x
    - 0.5, UV.x + 1.5, UV.y - 0.5, UV.y + 1.5 )@$    float4 s = float4( xc.x + xc.y,
    xc.z + xc.w, yc.x + yc.y, yc.z + yc.w )@$$    float4 off = ( c + float4( xc.y,
    xc.w, yc.y, yc.w ) / s ) * TexelSize.xyxy@$$    UV0 = off.xz@$    UV1 = off.yz@$
    \   UV2 = off.xw@$    UV3 = off.yw@$    W0 = s.x / ( s.x + s.y )@$ W1 = s.z /
    ( s.z + s.w )@$};7;False;8;True;UV;FLOAT2;0,0;In;;Float;False;True;TexelSize;FLOAT4;0,0,0,0;In;;Inherit;False;True;UV0;FLOAT2;0,0;Out;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Inherit;False;True;W0;FLOAT;0;Out;;Inherit;False;True;W1;FLOAT;0;Out;;Inherit;False;Bicubic
    Prepare;False;False;0;9;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT;0;False;8;FLOAT;0;False;7;FLOAT;0;FLOAT2;4;FLOAT2;5;FLOAT2;6;FLOAT2;7;FLOAT;8;FLOAT;9\nNode;AmplifyShaderEditor.WireNode;190;-293.4339,1954.13;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;179;-113.5429,2257.507;Inherit;True;Property;_TextureSample10;Texture
    Sample 10;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;105;-1643.842,35.19871;Inherit;False;Input_SS;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.LerpOp;45;351.319,-373.6947;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;203;494.0718,2480.887;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;220;-370.9889,3317.928;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.LerpOp;211;342.3458,3661.698;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;209;-89.82652,3521.562;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;193;-415.6305,2008.959;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;159;-334.8512,736.2561;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SamplerNode;208;-85.47648,3112.596;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.BreakToComponentsNode;230;-19.01893,3942.506;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.LerpOp;182;314.7018,1978.976;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SwizzleNode;185;-570.4064,2125.433;Inherit;False;FLOAT2;0;2;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;161;-309.4034,811.0089;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.SwizzleNode;186;-566.1018,2310.57;Inherit;False;FLOAT2;0;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;202;878.0947,2176.097;Inherit;False;Output_Fetch2D;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionInput;198;-2580.608,143.3581;Inherit;False;Offsets;4;4;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;187;-42.73524,2678.451;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionInput;199;-2581.965,245.189;Inherit;False;Weights;2;5;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSubtitle;88;1783.674,101.3291;Inherit;False;Fetch
    2D Array (2/2);1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSubtitle;76;1794.327,-169.9491;Inherit;False;2D
    Array;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;74;2490.12,0.7858257;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionSubtitle;80;1803.251,-313.9693;Inherit;False;2D;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;87;1442.031,99.73958;Inherit;False;225;Output_Fetch2DArray;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;78;1496.474,-317.2461;Inherit;False;131;Output_2D;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;225;901.8109,3440.151;Inherit;False;Output_Fetch2DArray;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;160;-326.8987,769.6562;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.GetLocalVarNode;231;-377.444,3934.225;Inherit;False;200;Input_FetchWeights;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;235;-357.6844,3222.431;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;81;1787.2,-40.47982;Inherit;False;Fetch
    2D (2/2);1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.CustomExpressionNode;2;-672.0914,-60.54211;Inherit;False;{$
    UV = UV * TexelSize.zw - 0.5@$    float2 f = frac( UV )@$    UV -= f@$$    float4
    xn = float4( 1.0, 2.0, 3.0, 4.0 ) - f.xxxx@$    float4 yn = float4( 1.0, 2.0,
    3.0, 4.0 ) - f.yyyy@$$    float4 xs = xn * xn * xn@$    float4 ys = yn * yn *
    yn@$$    float3 xv = float3( xs.x, xs.y - 4.0 * xs.x, xs.z - 4.0 * xs.y + 6.0
    * xs.x )@$    float3 yv = float3( ys.x, ys.y - 4.0 * ys.x, ys.z - 4.0 * ys.y +
    6.0 * ys.x )@$    float4 xc = float4( xv.xyz, 6.0 - xv.x - xv.y - xv.z )@$ float4
    yc = float4( yv.xyz, 6.0 - yv.x - yv.y - yv.z )@$$    float4 c = float4( UV.x
    - 0.5, UV.x + 1.5, UV.y - 0.5, UV.y + 1.5 )@$    float4 s = float4( xc.x + xc.y,
    xc.z + xc.w, yc.x + yc.y, yc.z + yc.w )@$$    float4 off = ( c + float4( xc.y,
    xc.w, yc.y, yc.w ) / s ) * TexelSize.xyxy@$$    UV0 = off.xz@$    UV1 = off.yz@$
    \   UV2 = off.xw@$    UV3 = off.yw@$    W0 = s.x / ( s.x + s.y )@$ W1 = s.z /
    ( s.z + s.w )@$};7;False;8;True;UV;FLOAT2;0,0;In;;Float;False;True;TexelSize;FLOAT4;0,0,0,0;In;;Inherit;False;True;UV0;FLOAT2;0,0;Out;;Float;False;True;UV1;FLOAT2;0,0;Out;;Float;False;True;UV2;FLOAT2;0,0;Out;;Float;False;True;UV3;FLOAT2;0,0;Out;;Inherit;False;True;W0;FLOAT;0;Out;;Inherit;False;True;W1;FLOAT;0;Out;;Inherit;False;Bicubic
    Prepare;False;False;0;9;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT2;0,0;False;6;FLOAT2;0,0;False;7;FLOAT;0;False;8;FLOAT;0;False;7;FLOAT;0;FLOAT2;4;FLOAT2;5;FLOAT2;6;FLOAT2;7;FLOAT;8;FLOAT;9\nNode;AmplifyShaderEditor.SwizzleNode;183;-564.1018,2399.57;Inherit;False;FLOAT2;1;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;118;-955.8895,-151.2794;Inherit;False;100;Input_UV;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;215;-546.6902,3389.487;Inherit;False;FLOAT2;0;2;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;196;-399.3375,2086.351;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;195;-396.6223,2064.626;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;238;-353.1872,3276.79;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;204;-948.2417,2264.657;Inherit;False;197;Input_FetchOffsets;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;194;-402.0527,2038.829;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;184;-564.1018,2219.57;Inherit;False;FLOAT2;1;2;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;212;338.418,3243.03;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;197;-1637.331,141.5877;Inherit;False;Input_FetchOffsets;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;128;248.1051,292.4236;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;192;-647.9,1997.286;Inherit;False;105;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;188;-663.9133,1852.121;Inherit;False;101;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.SamplerNode;6;-77.30836,-298.9066;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;94;-2587.118,36.96911;Inherit;False;SS;13;6;False;1;0;SAMPLERSTATE;0;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;156;956.144,991.3506;Inherit;False;Output_2DArray;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;178;-109.1929,1848.542;Inherit;True;Property;_TextureSample9;Texture
    Sample 9;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;97;-1783.926,-341.5729;Inherit;False;UV;2;3;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;139;-60.06113,1278.305;Inherit;True;Property;_TextureSample6;Texture
    Sample 6;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;152;365.4948,791.4232;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.TexelSizeNode;155;-968.2141,1150.107;Inherit;False;-1;1;0;SAMPLER2DARRAY;;False;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;91;-2601.69,-153.4011;Inherit;False;Texture
    Array;12;1;False;1;0;SAMPLER2DARRAY;0;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;68;-88.63429,348.3121;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;92;-2128.13,-340.859;Inherit;False;Option;False;0;4;-1;2D;2D
    Array;Triplanar;Triplanar Array;Instance;72;9;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;157;-993.7668,956.1034;Inherit;False;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;142;-81.54513,1457.542;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;162;-670.3486,817.4661;Inherit;False;98;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;151;-406.4995,963.3865;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;163;-384.1563,834.8665;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;79;1470.797,-174.3436;Inherit;False;156;Output_2DArray;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.LerpOp;44;750.9617,-170.366;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;119;-652.9467,-253.246;Inherit;False;105;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;134;-77.88247,1485.869;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;210;-87.13773,3729.912;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;124;-967.2612,-508.0312;Inherit;False;101;Input_Texture;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;148;-406.0995,985.9865;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;140;262.2807,1457.542;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;167;-339.9795,-424.4737;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionInput;93;-2533.613,-48.14872;Inherit;False;Index;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;138;-63.13266,866.2112;Inherit;True;Property;_TextureSample5;Texture
    Sample 5;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;103;-2395.848,-231.0578;Inherit;False;0;-1;2;3;2;SAMPLER2DARRAY;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;101;-1648.95,-438.0066;Inherit;False;Input_Texture;-1;True;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;146;-941.7139,1013.838;Inherit;False;100;Input_UV;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;136;369.4226,1210.091;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;100;-1640.919,-341.9367;Inherit;False;Input_UV;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;46;355.2469,44.97323;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;144;-404.5566,1029.196;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;154;-58.39999,660.9893;Inherit;True;Property;_TextureSample7;Texture
    Sample 7;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;43;-74.23686,113.1873;Inherit;True;Property;_TextureSample4;Texture
    Sample 4;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexelSizeNode;40;-982.3898,-15.01096;Inherit;False;-1;1;0;SAMPLER2D;;False;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;137;-62.74997,1069.955;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2DArray;8;0;SAMPLER2DARRAY;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;166;-387.3372,888.9426;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;149;-403.2565,1011.686;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;237;-354.9358,3257.045;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;141;239.3885,1414.408;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;66;225.2129,249.2902;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;99;-2573.558,-440.557;Inherit;False;Texture;9;0;False;1;0;SAMPLER2D;0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;169;-326.6317,-354.7689;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;126;-1003.882,-231.9052;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;131;941.968,-173.7672;Inherit;False;Output_2D;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;96;-1649.888,-153.7866;Inherit;False;Input_TextureArray;-1;True;1;0;SAMPLER2DARRAY;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;164;-379.3847,853.952;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;229;-572.9658,3296.807;Inherit;False;105;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;7;-76.92567,-95.16291;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;232;-581.6864,3211.935;Inherit;False;98;Input_Index;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;129;299.7782,302.3605;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;133;313.9539,1467.479;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;4;-72.57567,-504.1286;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;0;0;Create;True;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;123;-399.6465,-182.0461;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;165;-384.1563,873.0378;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;120;-392.0465,-249.4461;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.LerpOp;153;765.1377,994.7519;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;150;-921.8599,664.8158;Inherit;False;96;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;125;-792.2485,-371.2797;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;121;-391.6465,-226.8461;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;102;-2396.089,-365.7368;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;223;-372.0595,3381.453;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;236;-352.8128,3242.612;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;221;-370.7801,3337.677;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;228;-617.197,3114.176;Inherit;False;96;Input_TextureArray;1;0;OBJECT;;False;1;SAMPLER2DARRAY;0\nNode;AmplifyShaderEditor.WireNode;168;-329.5981,-387.3966;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;67;-92.05823,320.7514;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;200;-1638.688,242.1192;Inherit;False;Input_FetchWeights;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;72;2095.89,-139.1704;Inherit;False;Sample
    Mode;False;0;4;-1;2D;2D Array;Fetch 2D;Fetch 2D Array;Object;-1;9;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;222;-367.2213,3359.789;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;205;-401.1604,2671.893;Inherit;False;200;Input_FetchWeights;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;147;-661.0379,957.996;Inherit;False;105;Input_SS;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;98;-1643.479,-47.99601;Inherit;False;Input_Index;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;73;1469.538,-43.06931;Inherit;False;202;Output_Fetch2D;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;122;-398.3464,-201.1461;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;135;-74.45852,1513.43;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;85;2768.492,1.857527;Inherit;False;False;-1;G;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;82;2771.692,77.05763;Inherit;False;True;-1;B;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;83;2773.292,155.4575;Inherit;False;False;-1;A;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;84;2768.492,-68.54239;Inherit;False;False;-1;R;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;86;2768.33,-138.4119;Inherit;False;False;-1;RGBA;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nWireConnection;218;0;228;0\nWireConnection;217;0;228;0\nWireConnection;191;0;188;0\nWireConnection;214;0;227;0\nWireConnection;213;0;227;0\nWireConnection;219;0;228;0\nWireConnection;189;0;188;0\nWireConnection;233;0;215;0\nWireConnection;127;0;2;8\nWireConnection;226;0;230;1\nWireConnection;234;0;214;0\nWireConnection;180;0;191;0\nWireConnection;180;1;183;0\nWireConnection;180;7;196;0\nWireConnection;181;0;180;0\nWireConnection;181;1;179;0\nWireConnection;181;2;187;0\nWireConnection;224;0;211;0\nWireConnection;224;1;212;0\nWireConnection;224;2;226;0\nWireConnection;176;0;181;0\nWireConnection;176;1;182;0\nWireConnection;176;2;203;0\nWireConnection;207;0;217;0\nWireConnection;207;1;234;0\nWireConnection;207;6;236;0\nWireConnection;207;7;221;0\nWireConnection;177;0;189;0\nWireConnection;177;1;184;0\nWireConnection;177;7;194;0\nWireConnection;158;0;150;0\nWireConnection;216;0;227;0\nWireConnection;145;1;146;0\nWireConnection;145;2;155;0\nWireConnection;190;0;188;0\nWireConnection;179;0;190;0\nWireConnection;179;1;186;0\nWireConnection;179;7;195;0\nWireConnection;105;0;94;0\nWireConnection;45;0;6;0\nWireConnection;45;1;4;0\nWireConnection;45;2;66;0\nWireConnection;203;0;187;1\nWireConnection;220;0;229;0\nWireConnection;211;0;210;0\nWireConnection;211;1;209;0\nWireConnection;211;2;230;0\nWireConnection;209;0;218;0\nWireConnection;209;1;216;0\nWireConnection;209;6;237;0\nWireConnection;209;7;222;0\nWireConnection;193;0;192;0\nWireConnection;159;0;150;0\nWireConnection;208;0;228;0\nWireConnection;208;1;233;0\nWireConnection;208;6;235;0\nWireConnection;208;7;220;0\nWireConnection;230;0;231;0\nWireConnection;182;0;177;0\nWireConnection;182;1;178;0\nWireConnection;182;2;187;0\nWireConnection;185;0;204;0\nWireConnection;161;0;150;0\nWireConnection;186;0;204;0\nWireConnection;202;0;176;0\nWireConnection;187;0;205;0\nWireConnection;88;0;87;0\nWireConnection;76;0;79;0\nWireConnection;74;0;72;0\nWireConnection;80;0;78;0\nWireConnection;225;0;224;0\nWireConnection;160;0;150;0\nWireConnection;235;0;232;0\nWireConnection;81;0;73;0\nWireConnection;2;1;118;0\nWireConnection;2;2;40;0\nWireConnection;183;0;204;0\nWireConnection;215;0;227;0\nWireConnection;196;0;192;0\nWireConnection;195;0;192;0\nWireConnection;238;0;232;0\nWireConnection;194;0;192;0\nWireConnection;184;0;204;0\nWireConnection;212;0;207;0\nWireConnection;212;1;208;0\nWireConnection;212;2;230;0\nWireConnection;197;0;198;0\nWireConnection;128;0;67;0\nWireConnection;6;0;167;0\nWireConnection;6;1;2;5\nWireConnection;6;7;121;0\nWireConnection;156;0;153;0\nWireConnection;178;0;188;0\nWireConnection;178;1;185;0\nWireConnection;178;7;193;0\nWireConnection;97;0;92;0\nWireConnection;139;0;161;0\nWireConnection;139;1;145;7\nWireConnection;139;6;166;0\nWireConnection;139;7;144;0\nWireConnection;152;0;138;0\nWireConnection;152;1;154;0\nWireConnection;152;2;141;0\nWireConnection;155;0;157;0\nWireConnection;68;0;2;9\nWireConnection;92;0;102;0\nWireConnection;92;1;103;0\nWireConnection;92;2;102;0\nWireConnection;92;3;103;0\nWireConnection;157;0;158;0\nWireConnection;142;0;145;8\nWireConnection;151;0;147;0\nWireConnection;163;0;162;0\nWireConnection;44;0;46;0\nWireConnection;44;1;45;0\nWireConnection;44;2;129;0\nWireConnection;134;0;145;8\nWireConnection;210;0;219;0\nWireConnection;210;1;213;0\nWireConnection;210;6;238;0\nWireConnection;210;7;223;0\nWireConnection;148;0;147;0\nWireConnection;140;0;134;0\nWireConnection;167;0;124;0\nWireConnection;138;0;159;0\nWireConnection;138;1;145;5\nWireConnection;138;6;164;0\nWireConnection;138;7;148;0\nWireConnection;103;2;91;0\nWireConnection;101;0;99;0\nWireConnection;136;0;139;0\nWireConnection;136;1;137;0\nWireConnection;136;2;140;0\nWireConnection;100;0;97;0\nWireConnection;46;0;43;0\nWireConnection;46;1;7;0\nWireConnection;46;2;128;0\nWireConnection;144;0;147;0\nWireConnection;154;0;150;0\nWireConnection;154;1;145;4\nWireConnection;154;6;163;0\nWireConnection;154;7;151;0\nWireConnection;43;0;169;0\nWireConnection;43;1;2;7\nWireConnection;43;7;123;0\nWireConnection;40;0;126;0\nWireConnection;137;0;160;0\nWireConnection;137;1;145;6\nWireConnection;137;6;165;0\nWireConnection;137;7;149;0\nWireConnection;166;0;162;0\nWireConnection;149;0;147;0\nWireConnection;237;0;232;0\nWireConnection;141;0;142;0\nWireConnection;66;0;127;0\nWireConnection;169;0;124;0\nWireConnection;126;0;125;0\nWireConnection;131;0;44;0\nWireConnection;96;0;91;0\nWireConnection;164;0;162;0\nWireConnection;7;0;168;0\nWireConnection;7;1;2;6\nWireConnection;7;7;122;0\nWireConnection;129;0;68;0\nWireConnection;133;0;135;0\nWireConnection;4;0;124;0\nWireConnection;4;1;2;4\nWireConnection;4;7;120;0\nWireConnection;123;0;119;0\nWireConnection;165;0;162;0\nWireConnection;120;0;119;0\nWireConnection;153;0;136;0\nWireConnection;153;1;152;0\nWireConnection;153;2;133;0\nWireConnection;125;0;124;0\nWireConnection;121;0;119;0\nWireConnection;102;2;99;0\nWireConnection;223;0;229;0\nWireConnection;236;0;232;0\nWireConnection;221;0;229;0\nWireConnection;168;0;124;0\nWireConnection;67;0;2;8\nWireConnection;200;0;199;0\nWireConnection;72;0;80;0\nWireConnection;72;1;76;0\nWireConnection;72;2;81;0\nWireConnection;72;3;88;0\nWireConnection;222;0;229;0\nWireConnection;98;0;93;0\nWireConnection;122;0;119;0\nWireConnection;135;0;145;9\nWireConnection;85;0;74;1\nWireConnection;82;0;74;2\nWireConnection;83;0;74;3\nWireConnection;84;0;74;0\nWireConnection;86;0;72;0\nASEEND*/\n//CHKSM=92DC859C84122553D9A506AB24FD12161823607A"
  m_functionName: 
  m_description: 'Fast 4-Tap bicubic texture sampling. Smoother than regular texture
    sampling at a slightly higher performance cost.


    IMPORTANT

    Fetch modes are faster sampling paths for multiple textures that share the same
    size and UVs. For these modes, the "Bicubic Precompute" node must be used to feed
    the necessary data.'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 12
  m_customNodeCategory: Rust
  m_previewPosition: 0
  m_hidden: 0
