﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Diagnostics;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class RegionLayerUI : UnityEditor.Editor
    {
        //计算一个区域内刨除障碍物后的剩余区域
        void CalculateRegionValidArea(RegionData region, bool excludeObstacles)
        {
            if (region == null)
            {
                return;
            }

            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start();

            if (excludeObstacles)
            {
                var obstacles = Utils.CreateObstacles(LayerTypeMask.kGridModelLayer | LayerTypeMask.kComplexGridModelLayer | LayerTypeMask.kRailwayLayer | LayerTypeMask.kModelLayer | LayerTypeMask.kCollisionLayer, false, PrefabOutlineType.NavMeshObstacle, CheckMapCollisionOperation.kCalculateValidArea, false, false, true);

                List<IObstacle> obs = new List<IObstacle>();
                for (int i = 0; i < obstacles.Count; ++i)
                {
                    if (obstacles[i].IsSimplePolygon(PrefabOutlineType.NavMeshObstacle))
                    {
                        obs.Add(obstacles[i]);
                    }
                }

                List<List<Vector3>> holes = null;
                List<List<Vector3>> obstaclePolygons = PolygonAlgorithm.GetCombinedObstaclePolygons(PrefabOutlineType.NavMeshObstacle, obstacles, 0, obstacles.Count - 1, 0, 1.0f, out holes);
                if (obstaclePolygons.Count > 0)
                {
                    var regionOutline = region.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                    List<List<Vector3>> validRegionAreaNoneHoles;
                    List<List<Vector3>> validRegionAreaHoles;
                    PolygonAlgorithm.GetDifferencePolygons(regionOutline, obstaclePolygons, out validRegionAreaNoneHoles, out validRegionAreaHoles);

                    Vector3[] meshVertices;
                    int[] meshIndices;
                    Triangulator.TriangulatePolygons(validRegionAreaNoneHoles, validRegionAreaHoles, true, 100, 180000, null, out meshVertices, out meshIndices);

                    Color[] colors = new Color[meshVertices.Length];
                    for (int i = 0; i < colors.Length; ++i)
                    {
                        colors[i] = region.color;
                    }
                    mLogic.layer.SetMesh(region.id, meshVertices, meshIndices, colors);
#if false
                    //temp code
                    BigMeshViewer viewer = new BigMeshViewer();
                    viewer.Create(null, "CalculateRegionValidArea", meshVertices, meshIndices, false, Color.yellow);
#endif
                }
            }
            else
            {
                Vector3[] meshVertices;
                int[] meshIndices;
                var regionOutline = region.GetOutlineVertices(PrefabOutlineType.NavMeshObstacle);
                Triangulator.TriangulatePolygons(new List<List<Vector3>>() { regionOutline }, null, true, 100, 180000, null, out meshVertices, out meshIndices);

                Color[] colors = new Color[meshVertices.Length];
                for (int i = 0; i < colors.Length; ++i)
                {
                    colors[i] = region.color;
                }
                mLogic.layer.SetMesh(region.id, meshVertices, meshIndices, colors);
            }
            stopWatch.Stop();

            long elapsed = stopWatch.ElapsedMilliseconds;

            UnityEngine.Debug.Log("Creat Spawn points NavMesh cost " + elapsed + " ms");
        }
    }
}

#endif