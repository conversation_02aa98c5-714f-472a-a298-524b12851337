﻿using System.Collections.Generic;

namespace TileMatching
{
    /// <summary>
    /// 图块类型
    /// </summary>
    public enum TileType
    {
        Red = 1,
        Yellow,
        Blue,
        Green,
        Purple,
    }

    public struct TileTypeComparer : IEqualityComparer<TileType>
    {
        public bool Equals(TileType x, TileType y)
        {
            return x == y;
        }

        public int GetHashCode(TileType obj)
        {
            // you need to do some thinking here,
            return (int)obj;
        }
    }
}