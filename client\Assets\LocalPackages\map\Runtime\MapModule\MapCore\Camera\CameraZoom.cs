﻿ 



 
 



using System.Collections;
using UnityEngine;

namespace TFW.Map
{
    //相机双指缩放
    public class CameraZoom : ZoomActionBase
    {
        public CameraZoom(CameraActionType updateOrder) : base(updateOrder)
        {
            moveAxis = CameraMoveAxis.All;
            ownAxis = CameraMoveAxis.All;

            Init();
        }

        public override void OnFinishImpl()
        {
        }

        public override Vector3 GetTargetPosition()
        {
            return GetCameraPos();
        }

        void OnTouchZoomed(float centerX, float centerY, float scrollRate)
        {
            enabled = true;
            TouchZoomed(centerX, centerY, scrollRate);
            MapCameraMgr.ResetAutoUpdateTargetHeight();
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            if (MapTouchManager.touchCount == 2)
            {
                IMapTouch touch = MapTouchManager.GetTouch(0);
                IMapTouch touch2 = MapTouchManager.GetTouch(1);
                Vector2 center = (Vector2)((touch.position + touch2.position) * 0.5f);
                int centerX = (int)center.x;
                int centerY = (int)center.y;
                float touchDelta = Vector2.Distance(touch.position, touch2.position);
                if (!mIsTwoTouchZooming)
                {
                    mIsTwoTouchZooming = true;
                    mLastTowTouchDist = touchDelta;
                    mFirstTwoTouchDist = touchDelta;
                    Reset();
                }
                else
                {
                    float f = mLastTowTouchDist - touchDelta;
                    if (((Mathf.Abs(f) > 1f) && ((touch.state == MapTouchState.Touching) || (touch2.state == MapTouchState.Touching))) && (mLastTowTouchDist > 0f))
                    {
                        OnTouchZoomed(centerX, centerY, mFirstTwoTouchDist / mLastTowTouchDist);
                        mLastTowTouchDist = touchDelta;
                    }
                }
            }
            else if (mIsTwoTouchZooming)
            {
                mIsTwoTouchZooming = false;
                isFinished = true;
            }
        }

        float mFirstTwoTouchDist = 0f;
        bool mIsTwoTouchZooming = false;
        float mLastTowTouchDist = 0f;
    }
}