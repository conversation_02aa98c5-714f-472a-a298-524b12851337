﻿ 



 
 



/*
 * created by wzw at 2019.12.9
 */

using UnityEngine;
using System;
using System.Collections.Generic;

namespace TFW.Map
{
    //目前地图上所有使用prefab的对象都可以使用这种视图
    public class SplineView : ModelView
    {
        public SplineView(IMapObjectData data, MapLayerView layerView)
            : base(data, layerView)
        {
        }

        protected override void OnModelReady(ModelBase model)
        {
            base.OnModelReady(model);

            //set shader lod
#if false
            var layerData = layerView.layerData;
            if (layerData.lodConfig != null)
            {
                var shaderLOD = layerData.lodConfig.lodConfigs[layerData.currentLOD].shaderLOD;
                if (shaderLOD != 0)
                {
                    var renderer = model.gameObject.GetComponent<MeshRenderer>();
                    if (renderer != null)
                    {
                        renderer.sharedMaterial.shader.maximumLOD = shaderLOD;
                    }
                }
            }
#endif

            CheckVisibility(model);
        }

        protected override int CalculateLOD()
        {
            return layerView.layerData.currentLOD;
        }

        protected override void OnLODChanged()
        {
            bool isHide = CheckVisibility(model);
            if (!isHide)
            {
                base.OnLODChanged();
            }
        }

        bool CheckVisibility(ModelBase model)
        {
            var layerData = layerView.layerData;
            var spline = layerData.map.FindObject(objectDataID) as ModelData;

            bool visible = true;
            //if (riverData.hideLOD != -1)
            //{
            //    if (riverData.hideLOD == layerData.currentLOD)
            //    {
            //        visible = false;
            //    }
            //}

            bool hideObject = layerData.lodConfig.lodConfigs[layerData.currentLOD].hideObject;
            if (hideObject)
            {
                visible = false;
            }

            model.active = visible;

            return visible == false;
        }
    }
}
