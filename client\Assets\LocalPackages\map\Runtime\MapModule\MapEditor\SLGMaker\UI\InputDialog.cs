﻿ 



 
 



#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map {
    public class InputDialog : EditorWindow {
        public class Item
        {
            public Item(string n, string tooltip)
            {
                name = n;
                this.tooltip = tooltip;
            }

            public string name;
            public string tooltip;
        }

        public class StringItem : Item
        {
            public StringItem(string name, string tooltip, string text) : base(name, tooltip)
            {
                this.text = text;
            }

            public string text;
        }

        public class BoolItem : Item
        {
            public BoolItem(string name, string tooltip, bool val) : base(name, tooltip)
            {
                value = val;
            }

            public bool value;
        }

        public class EnumItem : Item
        {
            public EnumItem(string name, string tooltip, System.Enum val) : base(name, tooltip)
            {
                value = val;
            }

            public System.Enum value;
        }

        public class PathItem : Item
        {
            public PathItem(string name, string tooltip, string text) : base(name, tooltip)
            {
                this.text = text;
            }

            public string text;
        }

        public class ColorItem : Item
        {
            public ColorItem(string name, string tooltip, Color color) : base(name, tooltip)
            {
                this.color = color;
            }

            public Color color;
        }

        public void Show(List<Item> items, System.Func<List<Item>, bool> OnClickOK) {
            mItems = items;
            mOnClickOK = OnClickOK;
        }

        void OnGUI() {
            EditorGUILayout.BeginVertical();
            for (int i = 0; i < mItems.Count; ++i)
            {
                if (mItems[i] is StringItem)
                {
                    StringItem item = mItems[i] as StringItem;
                    item.text = EditorGUILayout.TextField(new GUIContent(mItems[i].name, mItems[i].tooltip), item.text);
                }
                else if (mItems[i] is PathItem)
                {
                    PathItem item = mItems[i] as PathItem;
                    EditorGUILayout.BeginHorizontal();
                    item.text = EditorGUILayout.TextField(new GUIContent(mItems[i].name, mItems[i].tooltip), item.text);
                    if (GUILayout.Button("..."))
                    {
                        item.text = EditorUtility.OpenFolderPanel("Select save folder", "", "");
                    }
                    EditorGUILayout.EndHorizontal();
                }
                else if (mItems[i] is BoolItem)
                {
                    BoolItem item = mItems[i] as BoolItem;
                    item.value = EditorGUILayout.Toggle(new GUIContent(mItems[i].name, mItems[i].tooltip), item.value);
                }
                else if (mItems[i] is ColorItem)
                {
                    var item = mItems[i] as ColorItem;
                    item.color = EditorGUILayout.ColorField(new GUIContent(mItems[i].name, mItems[i].tooltip), item.color);
                }
                else if (mItems[i] is EnumItem)
                {
                    EnumItem item = mItems[i] as EnumItem;
                    item.value = EditorGUILayout.EnumPopup(new GUIContent(mItems[i].name, mItems[i].tooltip), item.value);
                }
                else
                {
                    Debug.Assert(false, "todo");
                }
            }
            EditorGUILayout.EndVertical();

            if (GUILayout.Button("OK")) {
                if (mOnClickOK != null) {
                    if (mOnClickOK(mItems)) {
                        Close();
                    }
                }
                else {
                    Close();
                }
            }
        }

        List<Item> mItems = new List<Item>();
        System.Func<List<Item>, bool> mOnClickOK;
    }
}

#endif