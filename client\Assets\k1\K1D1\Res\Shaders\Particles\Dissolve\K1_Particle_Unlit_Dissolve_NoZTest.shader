﻿Shader "K1/Particle/Unlit/Dissolve_NoZTest"
{
	Properties
	{
		_MainTex("Texture", 2D) = "white" {}
		_NoiseTex("Noise", 2D) = "white"{}
		_RampTex("Ramp", 2D) = "black"{}
		_Emission("Emission", Range(0, 2)) = 1
		_BorderWidth("BorderWidth", Range(0, 1)) = 0
	}
		SubShader
		{
			Tags { "Queue" = "Transparent" "RenderType" = "Opaque" }
			Zwrite off
			ZTest off
			LOD 100

			Pass
			{
				Blend SrcAlpha OneMinusSrcAlpha
				CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag

				#include "UnityCG.cginc"

				struct appdata
				{
					float4 vertex : POSITION;
					float4 uv : TEXCOORD0;
					fixed4 color : COLOR;
				};

				struct v2f
				{
					float4 uv : TEXCOORD0;
					float4 vertex : SV_POSITION;
					fixed4 color : COLOR;
				};

				sampler2D _MainTex;
				float4 _MainTex_ST;

				sampler2D _NoiseTex;
				float4 _NoiseTex_ST;

				sampler2D _RampTex;
				float _Emission;
				float _BorderWidth;

				v2f vert(appdata v)
				{
					v2f o;
					o.vertex = UnityObjectToClipPos(v.vertex);
					o.uv.xy = TRANSFORM_TEX(v.uv, _MainTex);
					o.uv.zw = TRANSFORM_TEX(v.uv, _NoiseTex);
					o.uv.zw = o.uv.zw / 4 + v.uv.zw;
					o.color = v.color;
					o.color.a = 1 - o.color.a;
					return o;
				}

				fixed4 frag(v2f i) : SV_Target
				{
					fixed Noise = tex2D(_NoiseTex, i.uv.zw).r;
					fixed dissolve = Noise - (i.color.a * 2 - 1) - 0.5;
					clip(dissolve);

					fixed4 col = tex2D(_MainTex,i.uv.xy);
					col.rgb *= i.color;

					half2 rampuv = half2((dissolve - _BorderWidth) / _BorderWidth,0.5);
					fixed4 rampcol = tex2D(_RampTex, rampuv) * _Emission;
					rampcol.a = col.a;
					col = lerp(col, rampcol,step(dissolve,_BorderWidth));

					return col;
				}
				ENDCG
			}
		}
}
