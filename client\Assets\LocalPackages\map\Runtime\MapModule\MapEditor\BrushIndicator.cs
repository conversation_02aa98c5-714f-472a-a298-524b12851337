﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.11.26
 */

using UnityEngine;


namespace TFW.Map
{
    [Black]
    public class BrushIndicator
    {
        public void SetTexture(Texture2D texture)
        {
            if (texture != mTexture)
            {
                mTexture = texture;
                if (mPrefabInstance != null)
                {
                    GameObject.DestroyImmediate(mPrefabInstance);
                    mPrefabInstance = null;
                }

                if (texture != null)
                {
                    mPrefabInstance = CreateGameObject(texture);
                    Utils.HideGameObject(mPrefabInstance);
                    mPrefabInstance.name = "Brush Indicator";
                    mPrefabInstance.SetActive(false);
                }
            }

            if (mMaterial != null)
            {
                mMaterial.mainTexture = texture;
            }
        }

        public void OnDestroy()
        {
            if (mPrefabInstance != null)
            {
                GameObject.DestroyImmediate(mPrefabInstance);
                mPrefabInstance = null;
            }

            if (mQuadMesh != null)
            {
                Object.DestroyImmediate(mQuadMesh);
                mQuadMesh = null;
            }

            if (mMaterial != null)
            {
                Object.DestroyImmediate(mMaterial);
                mMaterial = null;
            }
        }

        public void SetPosition(Vector3 pos)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.position = pos;
            }
        }

        public void SetRotation(Quaternion rot)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.rotation = rot;
            }
        }

        public void SetSize(float size)
        {
            if (mPrefabInstance != null)
            {
                mPrefabInstance.transform.localScale = Vector3.one * size;
            }
        }

        public void SetActive(bool active)
        {
            if (mPrefabInstance != null)
            {
                if (mPrefabInstance.activeSelf != active)
                {
                    mPrefabInstance.SetActive(active);
                }
            }
        }

        GameObject CreateGameObject(Texture2D texture)
        {
            var obj = new GameObject();
            var filter = obj.AddComponent<MeshFilter>();
            var meshRenderer = obj.AddComponent<MeshRenderer>();
            var mtl = CreateMaterial(texture);
            var mesh = CreateQuadMesh();
            filter.sharedMesh = mesh;
            meshRenderer.sharedMaterial = mtl;
            return obj;
        }

        Material CreateMaterial(Texture2D texture)
        {
            if (mMaterial == null)
            {
                mMaterial = new Material(Shader.Find("SLGMaker/Brush"));
                mMaterial.renderQueue = 4000;
                mMaterial.SetFloat("_FadeFactor", 0.3f);
            }
            mMaterial.mainTexture = texture;
            return mMaterial;
        }

        Mesh CreateQuadMesh()
        {
            if (mQuadMesh == null)
            {
                mQuadMesh = new Mesh();
                mQuadMesh.vertices = new Vector3[4]{
                    new Vector3(-0.5f, 0, -0.5f),
                    new Vector3(-0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, 0.5f),
                    new Vector3(0.5f, 0, -0.5f),
                };
                mQuadMesh.uv = new Vector2[]
                {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
                };
                mQuadMesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            }
            return mQuadMesh;
        }

        Texture2D mTexture;
        GameObject mPrefabInstance;
        Mesh mQuadMesh;
        Material mMaterial;
    }
}

#endif