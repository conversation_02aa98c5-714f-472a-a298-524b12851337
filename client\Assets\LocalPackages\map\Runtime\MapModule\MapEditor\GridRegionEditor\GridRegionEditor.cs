﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public class GridTemplate
    {
        public GridTemplate(int type, string name, Color color)
        {
            this.color = color;
            this.type = type;
            this.name = name;
        }
        public Color color;
        public int type;
        public string name;
        public GameObject templateObject;
    }

    public partial class GridRegionEditor
    {
        public GridRegionEditor(int horizontalGridCount, int verticalGridCount, float gridWidth, float gridHeight, List<GridTemplate> templates, int[,] grids, bool showGrid)
        {
            mGrids = new int[verticalGridCount, horizontalGridCount];
            mGridWidth = gridWidth;
            mGridHeight = gridHeight;
            mHorizontalGridCount = horizontalGridCount;
            mVerticalGridCount = verticalGridCount;
            mTileRoot = new GameObject("Grid Tile Root");
            mTileRoot.transform.SetParent(SLGMakerEditor.instance.gameObject.transform);
            Utils.HideGameObject(mTileRoot);

            for (int i = 0; i < templates.Count; ++i)
            {
                AddTemplate(templates[i].name, templates[i].type, templates[i].color);
            }

            if (grids != null)
            {
                for (int i = 0; i < verticalGridCount; ++i)
                {
                    for (int j = 0; j < horizontalGridCount; ++j)
                    {
                        mGrids[i, j] = grids[i, j];
                    }
                }
            }

            CreateTexturePlane(horizontalGridCount * gridWidth, verticalGridCount * gridHeight);

            active = showGrid;
        }

        public void OnDestroy()
        {
            for (int i = 0; i < mTemplates.Count; ++i)
            {
                Utils.DestroyObject(mTemplates[i].templateObject);
            }
            mTemplates = null;
            mGrids = null;

            DestroyTexturePlane();
        }

        public void SetGrid(Vector3 pos, int brushSize, int type)
        {
            Vector2Int coord = FromPositionToCoordinate(pos);
            int startX = coord.x - brushSize / 2;
            int startY = coord.y - brushSize / 2;
            int endX = startX + brushSize - 1;
            int endY = startY + brushSize - 1;

            if (endX < 0 || endY < 0 || startX >= mHorizontalGridCount || startY >= mVerticalGridCount)
            {
                return;
            }

            startX = Mathf.Clamp(startX, 0, mHorizontalGridCount - 1);
            startY = Mathf.Clamp(startY, 0, mVerticalGridCount - 1);
            endX = Mathf.Clamp(endX, 0, mHorizontalGridCount - 1);
            endY = Mathf.Clamp(endY, 0, mVerticalGridCount - 1);

            int width = endX - startX + 1;
            int height = endY - startY + 1;
            var pixels = mColorArrayPool.Rent(width * height);
            int idx = 0;
            var template = GetGridTemplate(type);
            Color32 color = new Color32(0, 0, 0, 0);
            if (template != null)
            {
                color = template.color;
            }
            for (int i = startY; i <= endY; ++i)
            {
                for (int j = startX; j <= endX; ++j)
                {
                    mGrids[i, j] = type;
                    pixels[idx] = color;
                    ++idx;
                }
            }
            SetPixels(startX, startY, width, height, pixels);
            mColorArrayPool.Return(pixels);
        }

        public void SetGridData(int x, int y, int type)
        {
            if (x >= 0 && x < mHorizontalGridCount && y >= 0 && y < mVerticalGridCount)
            {
                if (mGrids[y, x] != type)
                {
                    mGrids[y, x] = type;
                }
            }
        }

        public GridTemplate FindTemplate(int type)
        {
            for (int i = 0; i < mTemplates.Count; ++i)
            {
                if (mTemplates[i].type == type)
                {
                    return mTemplates[i];
                }
            }
            return null;
        }

        public GridTemplate AddTemplate(string name, int type, Color color)
        {
            var template = new GridTemplate(type, name, color);
            var gameObject = new GameObject("grid template");
            gameObject.transform.parent = SLGMakerEditor.instance.transform;
            var meshFilter = gameObject.AddComponent<MeshFilter>();
            var mesh = new Mesh();
            mesh.vertices = new Vector3[]
            {
                new Vector3(-mGridWidth * 0.5f, 0, -mGridHeight * 0.5f),
                new Vector3(-mGridWidth * 0.5f, 0, mGridHeight * 0.5f),
                new Vector3(mGridWidth * 0.5f, 0, mGridHeight * 0.5f),
                new Vector3(mGridWidth * 0.5f, 0, -mGridHeight * 0.5f),
        };
            mesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };

            meshFilter.sharedMesh = mesh;
            var renderer = gameObject.AddComponent<MeshRenderer>();
            renderer.sharedMaterial = new Material(Shader.Find("SLGMaker/ColorTransparent"));
            renderer.sharedMaterial.color = color;
            gameObject.SetActive(false);
            Utils.HideGameObject(gameObject);

            template.templateObject = gameObject;
            mTemplates.Add(template);

            return template;
        }

        public void RemoveTemplate(int index)
        {
            if (index >= 0 && index < mTemplates.Count)
            {
                Utils.DestroyObject(mTemplates[index].templateObject);
                mTemplates.RemoveAt(index);
            }
        }

        GridTemplate GetGridTemplate(int type)
        {
            for (int i = 0; i < mTemplates.Count; ++i)
            {
                if (mTemplates[i].type == type)
                {
                    return mTemplates[i];
                }
            }
            return null;
        }

        public Vector3 FromCoordinateToPositionCenter(int x, int y)
        {
            return new Vector3(x * mGridWidth + mGridWidth * 0.5f, 0, y * mGridHeight + mGridHeight * 0.5f);
        }

        public Vector3 FromCoordinateToPosition(int x, int y)
        {
            return new Vector3(x * mGridWidth, 0, y * mGridHeight);
        }

        public Vector2Int FromPositionToCoordinate(Vector3 pos)
        {
            return new Vector2Int(Mathf.FloorToInt(pos.x / mGridWidth), Mathf.FloorToInt(pos.z / mGridHeight));
        }

        public bool HasRegionData()
        {
            for (int i = 0; i < mVerticalGridCount; ++i)
            {
                for (int j = 0; j < mHorizontalGridCount; ++j)
                {
                    if (mGrids[i, j] != 0)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public void Export()
        {
            bool hasRegionData = HasRegionData();
            if (!hasRegionData)
            {
                return;
            }

            string folder = SLGMakerEditor.instance.projectFolder;
            string filePath = EditorUtility.SaveFilePanel("Select export folder", folder, "area", "json");
            if (string.IsNullOrEmpty(filePath))
            {
                return;
            }

            List<object> zones = new List<object>();
            Dictionary<string, object> zone = new Dictionary<string, object>();
            zone["Width"] = gridWidth;
            zone["Height"] = gridHeight;
            zone["ID"] = 1;

            var grid = grids;
            int horizontalGridCount = this.horizontalGridCount;
            int verticalGridCount = this.verticalGridCount;
            List<object> rows = new List<object>();
            for (int i = 0; i < verticalGridCount; ++i)
            {
                List<object> cols = new List<object>();
                for (int j = 0; j < horizontalGridCount; ++j)
                {
                    cols.Add(grid[j, i]);
                }
                rows.Add(cols);
            }

            zone["Areas"] = rows;
            zones.Add(zone);

            var str = JSONParser.Serialize(zones);
            File.WriteAllText(filePath, str);
        }

        public void Clear()
        {
            mTemplates.Clear();
        }

        public bool active
        {
            get
            {
                return mTileRoot.activeSelf;
            }

            set
            {
                mTileRoot.SetActive(value);
            }
        }

        public List<GridTemplate> templates { get { return mTemplates; } }
        public int templateCount { get { return mTemplates.Count; } }
        public int[,] grids { get { return mGrids; } }
        public float gridWidth { get { return mGridWidth; } }
        public float gridHeight { get { return mGridHeight; } }
        public int horizontalGridCount { get { return mHorizontalGridCount; } }
        public int verticalGridCount { get { return mVerticalGridCount; } }

        List<GridTemplate> mTemplates = new List<GridTemplate>();
        int[,] mGrids;
        float mGridWidth;
        float mGridHeight;
        int mHorizontalGridCount;
        int mVerticalGridCount;
        GameObject mTileRoot;
        ArrayPool<Color32> mColorArrayPool = ArrayPool<Color32>.Create();
    }
}

#endif