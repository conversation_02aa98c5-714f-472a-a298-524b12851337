﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;
using System;

/// <summary>
/// 播放抛物线类动画
/// </summary>
public class PlayParabolaAnimation : MonoBehaviour
{

    [Header("持续时间")]
    public float Duration = 10; //持续时间

    [Header("x偏移数字是否随机")]
    public bool IsXRandom = false;

    [Header("偏移x")]
    public float OffsetX = 0;

    [Header("X偏移随机最大值")]
    public float MaxOffsetX = 0;

    [Header("X偏移随机最小值")]
    public float MinOffsetX = 0;

    [Header("y偏移数字是否随机")]
    public bool IsYRandom = false;

    [Header("偏移y")]
    public float OffsetY = 0;

    [Header("Y偏移随机最大值")]
    public float MaxOffsetY = 0;

    [Header("Y偏移随机最小值")]
    public float MinOffsetY = 0;

    [Header("z偏移数字是否随机")]
    public bool IsZRandom = false;

    [Header("偏移z")]
    public float OffsetZ = 0;

    [Header("Z偏移随机最大值")]
    public float MaxOffsetZ = 0;

    [Header("Z偏移随机最小值")]
    public float MinOffsetZ = 0;

    [Header("延迟时间")]
    public float delayed = 0f;

    [Header("缓和曲线")]
    public AnimationCurve easeCurve = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));

    [Header("缩放曲线")]
    public AnimationCurve scaleCurve = new AnimationCurve(new Keyframe(0, 1), new Keyframe(1, 1));

    [Header("旋转曲线X")]
    public AnimationCurve rotationCurveX = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 0));

    [Header("旋转曲线Y")]
    public AnimationCurve rotationCurveY = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 0));

    [Header("旋转曲线Z")]
    public AnimationCurve rotationCurveZ = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 0));

    [Header("初始缩放")]
    public Vector3 InitLocalScale = Vector3.one;

    [Header("粒子特效的父节点")]
    public Transform ParticlesParent;

    [Header("自动销毁")]
    public bool IsDestory = true;

    [Header("是否跟随路线旋转")]
    public bool IsRotateWithPath = false;

    [Header("初始旋转角度")]
    public float InitRotate;
	

    private ParticleSystem[] m_Particles;

    private const int m_PointSize = 20; //点的数量
    private float m_CurrTime = 0; //当前运行时刻
    private float m_Ratio = 0; //当前完成的百分比

    private float _oriOffsetX = 0.0f; // 原始的偏移X轴值

    // -- 以下变量为了适配目标点可能会在移动过程中变化的情况
    private Action _onComplete = null; // 移动完成的事件
    private int _serialId = 0; // serial的ID
    private bool _isNeedMoveTarget = false; // 是否要移动到目标点
    private Transform _targetTransform = null; // 目标的transform
    private const float MoveTargetSpeed = 5f; // 向目标点移动的速度
    // -- 以上变量为了适配目标点可能会在移动过程中变化的情况
    private bool StartFly = false;

    private Action<GameObject> _onStart = null; // 移动完成的事件
    System.Random rd = new System.Random();
    private void Awake()
    {
        if (ParticlesParent != null)
        {
            m_Particles = ParticlesParent.GetComponentsInChildren<ParticleSystem>();
        }
        _oriOffsetX = OffsetX;
    }

    public Transform end;

    void Start()
    {
        //BeginPlay(transform.position, end.position, end, false, null);
    }

    private void OnDestroy()
    {
        //m_SkeletonAnimation = null;
        easeCurve = null;
        scaleCurve = null;
        rotationCurveX = null;
        rotationCurveY = null;
        rotationCurveZ = null;
        ParticlesParent = null;

        if (m_Particles != null)
        {
            int len = m_Particles.Length;
            for (int i = 0; i < len; i++)
            {
                m_Particles[i] = null;
            }
            m_Particles = null;
        }
        _onComplete = null;
        _targetTransform = null;
        _onStart = null;
        rd = null;
    }

    /// <summary>
    /// 设置持续时间
    /// </summary>
    /// <param name="time"></param>
    public void SetDuration(float time)
    {
        Duration = time;
    }

    /// <summary>
    /// 设置偏移量
    /// </summary>
    /// <param name="offset"></param>
    public void SetOffset(Vector3 offset)
    {
        OffsetX = offset.x;
        OffsetY = offset.y;
        OffsetZ = offset.z;
    }

    /// <summary>
    /// 设置粒子的缩放
    /// </summary>
    public void SetParticlesLocalScale()
    {
        if (m_Particles != null)
        {
            int len = m_Particles.Length;
            for (int i = 0; i < len; i++)
            {
                m_Particles[i].transform.localScale = transform.localScale;
            }
        }
    }



    /// <summary>
    /// 开始播放
    /// </summary>
    /// <param name="beginPos"></param>
    /// <param name="endPos"></param>
    /// <param name="targetTrans"></param>
    /// <param name="serialId"></param>
    /// <param name="isOffset"></param>
    /// <param name="onComplete"></param>
    public void BeginPlay(Vector3 beginPos, Vector3 endPos, Transform targetTrans, bool isOffset, Action onComplete, Action<GameObject> onStart = null)
    {
        StopAllCoroutines();

        if (!isOffset) // 如果没有设置偏移，则按照下面的规则
        {
            if (IsXRandom)
            {
                OffsetX = MinOffsetX + (MaxOffsetX - MinOffsetX) * (float)(rd.Next(1, 1000)) / 1000f;
            }
            else
            {
                if (endPos.x < beginPos.x) // 目标点在起始点左侧的时候，抛物线向右偏移
                {
                    OffsetX = _oriOffsetX;
                }
                else // 目标点在起始点右侧的时候，抛物线向左偏移
                {
                    OffsetX = -_oriOffsetX;
                }
            }

            if (IsYRandom)
            {
                OffsetY = MinOffsetY + (MaxOffsetY - MinOffsetY) * (float)(rd.Next(1, 1000)) / 1000f;
            }

            if (IsZRandom)
            {
                OffsetZ = MinOffsetZ + (MaxOffsetZ - MinOffsetZ) * (float)(rd.Next(1, 1000)) / 1000f;
            }
        }

        _onComplete = onComplete;
        _onStart = onStart;
        if (delayed > 0)
            StartFly = false;
        else
            StartFly = true;

        if (this.gameObject.activeInHierarchy)
        {
            //oroutine couldn't be started because the the game object 'xxxx' is inactive!
            StartCoroutine(WaitDelay());
            //Debug.Log("位置信息: "+"起点："+ beginPos+ "终点：" + endPos);
            StartCoroutine(PlayAction(beginPos, endPos, targetTrans));
        }
    }


    public IEnumerator BeginPlay(Vector3 beginPos, Vector3 endPos, Transform targetTrans, bool isOffset, Action onComplete, float delay)
    {
        StartFly = false;
        StopAllCoroutines();

        gameObject.SetActive(false);

        yield return new WaitForSeconds(delay);

        gameObject.SetActive(true);

        if (!isOffset) // 如果没有设置偏移，则按照下面的规则
        {
            if (IsXRandom)
            {
                OffsetX = MinOffsetX + (MaxOffsetX - MinOffsetX) * (float)(rd.Next(1, 1000)) / 1000f;
            }
            else
            {
                if (endPos.x < beginPos.x) // 目标点在起始点左侧的时候，抛物线向右偏移
                {
                    OffsetX = _oriOffsetX;
                }
                else // 目标点在起始点右侧的时候，抛物线向左偏移
                {
                    OffsetX = -_oriOffsetX;
                }
            }

            if (IsYRandom)
            {
                OffsetY = MinOffsetY + (MaxOffsetY - MinOffsetY) * (float)(rd.Next(1, 1000)) / 1000f;
            }

            if (IsZRandom)
            {
                OffsetZ = MinOffsetZ + (MaxOffsetZ - MinOffsetZ) * (float)(rd.Next(1, 1000)) / 1000f;
            }
        }

        _onComplete = onComplete;
        _onStart = null;
        StartFly = true;
        yield return PlayAction(beginPos, endPos, targetTrans);
    }



    public void AdvanceStart()
    {
        if (!StartFly)
        {
            StartFly = true;
        }
    }

    private IEnumerator WaitDelay()
    {
        if (delayed > 0)
        {
            yield return new WaitForSeconds(delayed);
            StartFly = true;
        }
        else
            yield return null;
    }
	
    private IEnumerator PlayAction(Vector3 beginPos, Vector3 endPos, Transform targetTrans)
    {
        yield return new WaitUntil(() =>
            {
                return StartFly;
            });

        if (_onStart != null)
        {
            _onStart(gameObject);
        }
        // Debug.LogWarning("beginPos" + beginPos);
        //先获取两个点的中心点
        Vector3 middlePos = (endPos - beginPos) * 0.5f;

		//float x = Mathf.Abs(endPos.x - beginPos.x); //两点距离
		//middlePos = new Vector3(middlePos.x, middlePos.y + x, middlePos.z);
        middlePos += new Vector3(OffsetX, OffsetY, OffsetZ);

		Bezierd bezier = new Bezierd(beginPos, middlePos, Vector3.zero, endPos);

        Vector3 PreFramePos = beginPos;

        Vector3[] resultList = new Vector3[m_PointSize];

        for (int index = 1; index <= m_PointSize; index++)
        {
			
            resultList[index - 1] = bezier.GetPointAtTime((float)index / (float)m_PointSize);
			//Debug.Log(resultList[index - 1]);
        }

        m_Ratio = 0;
        m_CurrTime = 0;

        //transform.localScale = InitLocalScale;
        SetParticlesLocalScale();		
        transform.DOPath(resultList, Duration, PathType.Linear) //.SetEase(easeCurve).OnUpdate(
   //         () =>
   //         {
			//	SetParticlesLocalScale();

			//	//移动中回调
			//	m_CurrTime += Time.deltaTime;
			//	m_Ratio = m_CurrTime / Duration;
			//	//transform.localScale = new Vector3(scaleCurve.Evaluate(m_Ratio), scaleCurve.Evaluate(m_Ratio), scaleCurve.Evaluate(m_Ratio));

			//	//transform.localRotation = Quaternion.Euler(rotationCurveX.Evaluate(m_Ratio) * 180f, rotationCurveY.Evaluate(m_Ratio) * 180f, rotationCurveZ.Evaluate(m_Ratio) * 180f);

			//	//if (IsRotateWithPath)
			//	//{
			//	//	transform.rotation = Quaternion.Euler(transform.rotation.eulerAngles.x, transform.rotation.eulerAngles.y, InitRotate + GetRotateAngleOfZ(PreFramePos, transform.position));
			//	//	PreFramePos = transform.position;
			//	//}


			//})
        .OnComplete(() =>
            {
                // 判断目标点物体是否在动画过程中移动了，如果移动了，则运动的物体在到达目标点以后，再移动到目标物体的实际位置上去。
                _isNeedMoveTarget = false;
                if (targetTrans != null)
                {
                    _targetTransform = targetTrans;
                    float dis = Vector3.Distance(transform.position, targetTrans.position);
                    // Debug.LogError("PlayParabolaAnimation.PlayAction dis = " + dis);

                    if (dis > 0.001f)
                    {
                        _isNeedMoveTarget = true;
                    }
                    else
                    {
                        transform.position = targetTrans.position;
                    }
                }

                if (!_isNeedMoveTarget)
                {
                    OnMoveFinish();
                }
            });
    }

    public float GetRotateAngleOfZ(Vector3 begin, Vector3 end)
    {
        float detalX = end.x - begin.x;
        float detalY = end.y - begin.y;
        if (detalY == 0)
            return 0;

        if (detalX == 0)
        {
            if (detalY > 0)
                return -90;
            else
                return 90;
        }
        else if (detalX > 0)
        {
            if (detalY > 0)
                return (float)-(180 - Math.Atan(detalY / detalX) / Math.PI * 180);
            else
                return (float)(180 - Math.Atan(-detalY / detalX) / Math.PI * 180);
        }
        else
        {
            if (detalY > 0)
                return (float)-(Math.Atan(detalY / -detalX) / Math.PI * 180);
            else
                return (float)(Math.Atan(-detalY / -detalX) / Math.PI * 180);
        }
    }

    /// <summary>
    /// 更新函数
    /// </summary>
    public void Update()
    {
        if (_isNeedMoveTarget)
        {
            transform.position = Vector3.MoveTowards(transform.position, _targetTransform.position, MoveTargetSpeed * Time.deltaTime);
            float dis = Vector3.Distance(transform.position, _targetTransform.position);
            if (dis <= 0.001f)
            {
                _isNeedMoveTarget = false;
                OnMoveFinish();
            }
        }
	}

    /// <summary>
    /// 动画移动完成事件
    /// </summary>
    private void OnMoveFinish()
    {
        // Debug.LogError("PlayParabolaAnimation.OnMoveFinish ...");
        //移动完成回调
        if (_onComplete != null)
        {
            _onComplete();
        }
    }
}