﻿Shader "K1/Solider/WithColor_Alpha Test"
{
    Properties
    {
		[PerRendererData]_MainTex("Texture", 2D) = "white" {}
        _Color("Tint Color", Color) = (1,1,1,1)
        _Cutoff("Alpha cutoff", Range(0,1)) = 0.5
        _lerpPercent("Color Lerp Value",Range(0,1)) = 0.5
        [Toggle(_MASK_ON)] _BlackKey("Mask", Float) = 0
    }
    SubShader
    {
        Cull Off ZWrite On ZTest On

		Tags
		{
			"Queue" = "AlphaTest+450" "IgnoreProjector" = "True"
		}

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
			#pragma multi_compile_instancing
             #pragma multi_compile _ _MASK_ON
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : COLOR;
				UNITY_VERTEX_OUTPUT_STEREO
            };

			sampler2D _MainTex;
			half4 _Color;
			half _Cutoff;
            half _lerpPercent;
            v2f vert (appdata v)
            {
                v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
				
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
#ifdef _MASK_ON
				return fixed4(lerp(1 - (1 - _Color.rgb) * (1 - col.rgb),fixed3(0,0,0), _lerpPercent), col.a);
#else
                return fixed4(1 - (1 - _Color.rgb) * (1 - col.rgb), col.a);
#endif
            }
            ENDCG
        }
    }
}
