﻿ 



 
 

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    class RegionColorData : TextureGridData
    {
        public RegionColorData(int id, Color color) : base(id, color)
        {
        }
    }

    public class RegionColorLayerData : TextureGridLayerData
    {
        public RegionColorLayerData(MapLayerDataHeader header, Map map, List<Layer> layers, bool showRegionInGame) : base(header, map, layers)
        {
            mShowRegionInGame = showRegionInGame;
        }

        public bool showRegionInGame { get { return mShowRegionInGame; } set { mShowRegionInGame = value; } }

        bool mShowRegionInGame;
    }
}


#endif