﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    [CustomEditor(typeof(MapEditor))]
    public class MapEditorUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mDetailSpritesEditor = new DetailSpritesEditor();
        }

        void OnLayerSelected(object layerType)
        {
            var t = layerType as string;
            if (t == MapCoreDef.MAP_LAYER_NODE_FRONT)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_FRONT);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<GridModelLayerSettingWindow>("Create Front Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_FRONT);
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_DYNAMIC)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_DYNAMIC);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<ModelLayerSettingWindow>("Create Dynamic Object Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_DYNAMIC, (string name, float width, float height) => Map.currentMap.CreateModelLayer(name, width, height));
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_RAILWAY)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_RAILWAY);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<RailwayLayerSettingWindow>("Create Railway Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_RAILWAY);
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_DECORATION)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_DECORATION);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<DecorationLayerSettingWindow>("Create Decoration Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_DECORATION);
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<DecorationBorderLayerSettingWindow>("Create Decoration Border Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER);
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_BUILDING_GRID)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_BUILDING_GRID);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<BuildingGridLayerSettingWindow>("Create Building Grid Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_BUILDING_GRID);
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_GROUND)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_GROUND);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<BlendTerrainLayerSettingWindow>("Create Ground Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<VaryingTileSizeTerrainLayerSettingWindow>("Create Varying Tile Size Ground Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_SPLIT_FOG)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_SPLIT_FOG);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<SplitFogLayerSettingWindow>("Create Split Fog Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER)
            {
                bool exist = CheckLayerExistence(MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER);
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<ModelLayerSettingWindow>("Create Circle Border Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER, (string name, float width, float height) => Map.currentMap.CreateCircleBorderLayer(name, width, height));
                }
            }
            else if (t == typeof(NavMeshLayer).Name)
            {
                bool exist = CheckLayerExistence(typeof(NavMeshLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<NavMeshLayerSettingWindow>("Create NavMesh Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == typeof(NPCRegionLayer).Name)
            {
                bool exist = CheckLayerExistence(typeof(NPCRegionLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<NPCRegionLayerSettingWindow>("Create NPC Region Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == typeof(EntitySpawnLayer).Name)
            {
                bool exist = CheckLayerExistence(typeof(EntitySpawnLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<EntitySpawnLayerSettingWindow>("Create NPC Spawn Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == typeof(LODLayer).Name)
            {
                bool exist = CheckLayerExistence(typeof(LODLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<LODLayerSettingWindow>("Create LOD Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_COLLISION)
            {
                bool exist = CheckLayerExistence(typeof(MapCollisionLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<MapCollisionLayerSettingWindow>("Create Collision Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_REGION)
            {
                bool exist = CheckLayerExistence(typeof(RegionLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<RegionLayerSettingWindow>("Create Region Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_REGION_COLOR)
            {
                bool exist = CheckLayerExistence(typeof(RegionColorLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<RegionColorLayerSettingWindow>("Create Region Color Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER)
            {
                bool exist = CheckLayerExistence(typeof(PolygonRiverLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<PolygonRiverLayerSettingWindow>("Create Polygon River Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER)
            {
                bool exist = CheckLayerExistence(typeof(CameraColliderLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<CameraColliderLayerSettingWindow>("Create Camera Collider Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY)
            {
                bool exist = CheckLayerExistence(typeof(EditorTerritoryLayer));
                if (!exist)
                {
                    var window = EditorWindow.GetWindow<EditorTerritoryLayerSettingWindow>("Territory Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show();
                }
            }
            else if (t == MapCoreDef.MAP_LAYER_NODE_RUIN)
            {
                //bool exist = CheckLayerExistence(typeof(RuinLayer));
                //if (!exist)
                {
                    var window = EditorWindow.GetWindow<RuinLayerSettingWindow>("Create Ruin Layer");
                    EditorUtils.CenterWindow(window, 500, 300, 500, 300);
                    window.Show(MapCoreDef.MAP_LAYER_NODE_RUIN);
                }
            }
            else
            {
                bool suc = ProcessPluginLayer(t);
                if (!suc)
                {
                    if (!MapPlugin.IsPluginLayer(t))
                    {
                        Debug.Assert(false, "todo");
                    }
                }
            }
        }

        bool ProcessPluginLayer(string layerType)
        {
            if (Map.currentMap.GetMapLayer(layerType) == null)
            {
                Type settingWindowType = MapPlugin.GetPluginLayerSettingWindowType(layerType);
                if (settingWindowType != null)
                {
                    var window = EditorWindow.GetWindow(settingWindowType, false, $"Creating {layerType} layer") as MapPluginLayerSettingWindow;
                    EditorUtils.CenterWindow(window);
                    window.Show(layerType);
                }
                else
                {
                    var layer = MapPlugin.CreatePluginLayer(layerType, Map.currentMap);
                    layer.CreateGameObject(layerType);
                    var type = MapPlugin.GetPluginLayerLogicType(layerType);
                    if (type != null)
                    {
                        layer.gameObject.AddComponent(type);
                    }
                    Map.currentMap.AddMapLayer(layer);
                }
                return true;
            }
            EditorUtility.DisplayDialog("Error", $"layer {layerType} has been already created!", "OK");
            return false;
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap() as EditorMap;
            if (map != null)
            {
                EditorGUILayout.BeginVertical("GroupBox");

                if (GUILayout.Button(new GUIContent("Create Map Layer", "创建地图层")))
                {
                    GenericMenu menu = new GenericMenu();

                    AddMenuItem(menu, "Ground Layer", "地表层", MapCoreDef.MAP_LAYER_NODE_GROUND);
                    AddMenuItem(menu, "Varying Tile Size Ground Layer", "地表tile大小不同的地表层", MapCoreDef.MAP_LAYER_NODE_VARYING_TILE_SIZE_GROUND);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "Front Layer", "使用大块prefab拼接的装饰物层", MapCoreDef.MAP_LAYER_NODE_FRONT);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "Decoration Layer", "整个地图使用一个prefab来表示的装饰物层", MapCoreDef.MAP_LAYER_NODE_DECORATION);
                    menu.AddSeparator("");

                    AddMenuItem(menu, "Decoration Border Layer", "用来摆放地图边界的装饰物,这一层的物体不是分帧加载，所以只适用于大而少的物体", MapCoreDef.MAP_LAYER_NODE_DECORATION_BORDER);
                    menu.AddSeparator("");

                    if (MapModule.displayRailwayLayer)
                    {
                        AddMenuItem(menu, "Railway Layer", "铁轨层", MapCoreDef.MAP_LAYER_NODE_RAILWAY);
                        menu.AddSeparator("");
                    }

                    AddMenuItem(menu, "NavMesh Layer", "导航层,注意导航数据是通过collision层或prefab outline来生成", typeof(NavMeshLayer).Name);
                    menu.AddSeparator("");

                    AddMenuItem(menu, "NPC Region Layer", "npc 区域层,用来将地图分为多个格子区域", typeof(NPCRegionLayer).Name);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "NPC Spawn Layer", "NPC刷新点层", typeof(EntitySpawnLayer).Name);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "LOD Layer", "LOD设置层,用来在游戏中触发lod改变的事件", typeof(LODLayer).Name);
                    menu.AddSeparator("");
                    if (MapModule.displayCircleBorderLayer)
                    {
                        AddMenuItem(menu, "Map Circle Border Layer", "P2地图边界的的圆形山脉层", MapCoreDef.MAP_LAYER_NODE_CIRCLE_BORDER);
                        menu.AddSeparator("");
                    }
                    AddMenuItem(menu, "Collision Layer", "碰撞数据层", MapCoreDef.MAP_LAYER_NODE_COLLISION);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "Ruin Layer", "手动放置一些服务器使用的数据点", MapCoreDef.MAP_LAYER_NODE_RUIN);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "River Layer", "在编辑器中编辑多边形河流", MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "Camera Collider Layer", "创建相机的碰撞体", MapCoreDef.MAP_LAYER_NODE_CAMERA_COLLIDER);
                    menu.AddSeparator("");
                    AddMenuItem(menu, "Split Fog Layer", "基于tile和区域划分的迷雾层", MapCoreDef.MAP_LAYER_NODE_SPLIT_FOG);
                    menu.AddSeparator("");

                    AddMenuItem(menu, "City Territory Layer", "绘制固定的城市占地区域", MapCoreDef.MAP_LAYER_NODE_CITY_TERRITORY);
                    menu.AddSeparator("");

                    AddMenuItem(menu, "Region Layer", "多边形区域", MapCoreDef.MAP_LAYER_NODE_REGION);
                    menu.AddSeparator("");

                    AddMenuItem(menu, "Region Color Layer", "设置区域颜色", MapCoreDef.MAP_LAYER_NODE_REGION_COLOR);
                    menu.AddSeparator("");

                    AddCustomLayers(menu);

                    menu.ShowAsContext();
                }

                if (MapModule.enableResizeMap)
                {
                    if (GUILayout.Button(new GUIContent("Resize Map", "修改地图大小")))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Change Map Size");
                        var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Size", "", "10000"),
                    };
                        dlg.Show(items, OnClickChangeMapSize);
                    }
                }

                EditorGUILayout.LabelField("Map Version", Map.currentMap.data.version.ToString());
                EditorGUILayout.LabelField("Layer Count", map.GetMapLayerCount().ToString());
                float newBorderHeight = EditorGUILayout.FloatField(new GUIContent("Map Border Height", "地图的边界山脉的高度,主要是用于p2的圆形边界,根据此高度可以计算出相机在边界的碰撞行为"), map.data.borderHeight);
                if (newBorderHeight < 5)
                {
                    newBorderHeight = 5.0f;
                }
                SetBorderHeight(newBorderHeight);

                float backExtendedSize = EditorGUILayout.FloatField(new GUIContent("Viewport Extended Size", "游戏内视野在z轴的大小扩展, 因为游戏内物体的裁剪是根据game object的xz平面的bounds来判断,所以一些很高的山会遇到即使在视野内也会被裁剪的情况,这时候就需要扩大z轴的裁剪框"), map.data.backExtendedSize);
                backExtendedSize = Mathf.Max(0, backExtendedSize);
                map.data.backExtendedSize = backExtendedSize;

                map.data.farClipOffset = EditorGUILayout.FloatField(new GUIContent("Camera Far Clip Plane Offset", ""), map.data.farClipOffset);

                map.data.globalObstacleManager.gridSize = EditorGUILayout.FloatField(new GUIContent("Obstacle Grid Size", "如果该值不为0,则会生成一份将整个地图划分成该值指定的格子大小,然后导出每个格子是否和任何障碍物相交的数据"), map.data.globalObstacleManager.gridSize);
                map.data.maxCameraColliderHeight = EditorGUILayout.FloatField(new GUIContent("Max Camera Collider Height", "游戏中相机碰撞最高有效高度,超过这个高度就忽略相机碰撞"), map.data.maxCameraColliderHeight);

                EditorGUILayout.BeginHorizontal();
                map.data.isCircleMap = EditorGUILayout.ToggleLeft(new GUIContent("Circle Map", "是否是圆形地图,圆形地图在生成刷新点等设置时会限制在圆形内"), map.data.isCircleMap);
                map.data.globalObstacleManager.IsGlobalObstaclesVisible = EditorGUILayout.ToggleLeft(new GUIContent("Show Global Obstacles", "是否显示地图障碍物数据"), map.data.globalObstacleManager.IsGlobalObstaclesVisible);
                EditorGUILayout.EndHorizontal();

                var editorMap = map as EditorMap;
                editorMap.navMeshRegionVisible = EditorGUILayout.ToggleLeft(new GUIContent("Show NavMesh Regions", "显示分区域的navmesh, 整个地图的navmesh可以分成各个带有唯一id的区域,这些区域可以通过代码控制开关,关闭的区域是不能寻路的"), editorMap.navMeshRegionVisible);

                map.generateNPCSpawnPointsInBorderLine = EditorGUILayout.ToggleLeft(new GUIContent("Generate NPC Spawn Points In Border Line Only", "是否只在勾选了Border Line的collision区域中生成npc刷新点"), map.generateNPCSpawnPointsInBorderLine);

                map.removeSameHoles = EditorGUILayout.ToggleLeft(new GUIContent("Remove Same Holes", "合并多边形时是否删除hole"), map.removeSameHoles);

                int navmeshModeIndex = mNavMeshModes.IndexOf(map.navMeshMode);
                int newNavMeshModeIndex = EditorGUILayout.Popup(new GUIContent("NavMesh Mode", "导航网格生成类型, 不同类型的地图需要不同的导航网格生成方式,例如海岛地图,封闭的关卡地图等"), navmeshModeIndex, mNavMeshModeNames);
                map.navMeshMode = mNavMeshModes[newNavMeshModeIndex];
                int obstacleModeIndex = mObstacleModes.IndexOf(map.globalObstacleMode);
                int newObstacleModeIndex = EditorGUILayout.Popup(new GUIContent("Global Obstacle Mode", "障碍物生成的类型"), obstacleModeIndex, mObstacleModeNames);
                map.globalObstacleMode = mObstacleModes[newObstacleModeIndex];

                map.data.frontTileSize = EditorGUILayout.FloatField(new GUIContent("Front Tile Size", "Front Layer的tile 大小"), map.data.frontTileSize);
                map.data.groundTileSize = EditorGUILayout.FloatField(new GUIContent("Ground Tile Size", "Ground Layer的tile 大小"), map.data.groundTileSize);

                var mapData = editorMap.data as EditorMapData;
                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(mapData.backgroundTexturePath);
                var newTexture = EditorGUILayout.ObjectField(new GUIContent("Background Texture", "可以设置一张背景图作为地图的参考"), texture, typeof(Texture2D), false, null) as Texture2D;
                if (newTexture != texture)
                {
                    mapData.backgroundTexturePath = AssetDatabase.GetAssetPath(newTexture);
                    Map.currentMap.view.SetBackgroundTexture(newTexture);
                }

                DrawMapDataGenerationRangeSetting();
                DrawGlobalGridSetting();

                EditorGUILayout.EndVertical();

                DrawMapLODManager();

                DrawLoadRange();

                mDetailSpritesEditor.Draw();
            }
        }

        bool OnClickChangeMapSize(List<InputDialog.Item> parameters)
        {
            var sizeStr = (parameters[0] as InputDialog.StringItem).text;
            int size;
            Utils.ParseInt(sizeStr, out size);
            if (size > 0)
            {
                Map.currentMap.SetSize(size, size);
                return true;
            }
            return false;
        }

        void AddCustomLayers(GenericMenu menu)
        {
            var pluginLayerInfos = MapPlugin.pluginLayerInfos;
            for (int i = 0; i < pluginLayerInfos.Count; ++i)
            {
                string title = string.IsNullOrEmpty(pluginLayerInfos[i].layerTitle) ? pluginLayerInfos[i].layerClassName : pluginLayerInfos[i].layerTitle;
                AddMenuItem(menu, $"Plugin Layers/{title}", pluginLayerInfos[i].layerTooltip, pluginLayerInfos[i].layerClassName);
            }
        }

        void DrawMapLODManager()
        {
            var mapData = Map.currentMap.data as EditorMapData;
            mapData.mapLODSetting.Draw(Map.currentMap.data.lodManager);
#if false
            if (GUILayout.Button("Export TSV"))
            {
                ExportMapLODSettingTSV(Map.currentMap.data.lodManager);
            }
#endif
        }

        //using this config https://docs.google.com/spreadsheets/d/1UFNWfaq-xa3HBi9ILaZSQ0ipJ5tzrSp2AaBbCUH5Ibs/edit#gid=0
        void ExportMapLODSettingTSV(MapLODManager lodManager)
        {
            var filePath = EditorUtility.SaveFilePanel("Select file", "", "1320_intuitive_zoom", "tsv");
            if (filePath != null && filePath.Length > 0)
            {
                StringBuilder builder = new StringBuilder();

                string[] headers = new string[] {
                "A_INT_id",
                "A_ARR_unit",
                "C_FLT_zoom",
                "C_FLT_height_ge",
                "A_INT_width",
                "A_INT_height",
                };

                for (int i = 0; i < headers.Length; ++i)
                {
                    builder.Append(headers[i]);
                    if (i != headers.Length - 1)
                    {
                        builder.Append("\t");
                    }
                }

                builder.AppendLine();

                int startID = 13201001;

                int nLODs = lodManager.lodCount;
                for (int i = 0; i < nLODs; ++i)
                {
                    var lod = lodManager.GetLOD(i);
                    //id
                    builder.AppendFormat("{0}\t", startID++);
                    string unitsStr = "[";
                    int nUnits = lod.displayingUnits.Count;
                    for (int k = 0; k < nUnits; ++k)
                    {
                        unitsStr += string.Format("\"{0}\"", lod.displayingUnits[k]);
                        if (k != nUnits - 1)
                        {
                            unitsStr += ",";
                        }
                    }
                    unitsStr += "]";
                    builder.AppendFormat("{0}\t", unitsStr);
                    //zoom
                    builder.AppendFormat("{0}\t", i);
                    //camera height
                    builder.AppendFormat("{0}\t", lod.cameraHeight);
                    //viewport width
                    builder.AppendFormat("{0}\t", lod.viewWidth);
                    //viewport height
                    builder.AppendFormat("{0}\n", lod.viewHeight);
                }

                var str = builder.ToString();
                File.WriteAllText(filePath, str);
            }
        }

        public void OnSceneGUI()
        {
            DrawMapBounds();

            Handles.color = Color.white;
            if (mShowTiles)
            {
                if (mPropertyTiles != null)
                {
                    foreach (var p in mPropertyTiles)
                    {
                        var coord = p.Key;
                        var center = new Vector3(coord.x, 0, coord.y);
                        Handles.DrawWireCube(center, Vector3.one);
                    }
                }
            }
        }

        void DrawMapBounds()
        {
            var min = new Vector3(0, 0, 0);
            var max = new Vector3(Map.currentMap.mapWidth, 0, Map.currentMap.mapHeight);
            mMapBounds.SetMinMax(min, max);

            Handles.color = new Color32(255, 70, 70, 255);
            Handles.DrawWireCube(mMapBounds.center, mMapBounds.size);
        }

        void AddMenuItem(GenericMenu menu, string menuPath, string tooltip, string layerType)
        {
            menu.AddItem(new GUIContent(menuPath, tooltip), false, OnLayerSelected, layerType);
        }

        bool CheckLayerExistence(string layerName)
        {
            var layer = Map.currentMap.GetMapLayer(layerName);
            if (layer == null)
            {
                return false;
            }
            EditorUtility.DisplayDialog("Error", string.Format("Map Layer {0} is already created", layerName), "OK");
            return true;
        }

        bool CheckLayerExistence(System.Type type)
        {
            var map = Map.currentMap;
            int nLayers = map.GetMapLayerCount();
            for (int i = 0; i < nLayers; ++i)
            {
                if (map.GetMapLayerByIndex(i).GetType() == type)
                {
                    EditorUtility.DisplayDialog("Error", string.Format("Map Layer {0} is already created", type.Name), "OK");
                    return true;
                }
            }
            return false;
        }

        void DrawLoadRange()
        {
            if (Map.currentMap != null)
            {
                var data = (Map.currentMap.data as EditorMapData).loadRangeData;
                mFoldLoadRange = EditorGUILayout.Foldout(mFoldLoadRange, new GUIContent("Load Range Calculator", "相机在游戏中xz平面视野大小计算器,输入以下的参数就可以计算出相机能在xz平面看到的视野大小"));
                if (mFoldLoadRange)
                {
                    EditorGUILayout.BeginVertical("GroupBox");
                    data.cameraFov = EditorGUILayout.FloatField(new GUIContent("FOV", "相机的fov"), data.cameraFov);
                    data.resolution = EditorUtils.Vector2Field("Screen Width", "运行时屏幕宽", "Screen Height", "运行时屏幕高", data.resolution);
                    data.cameraRotationX = EditorGUILayout.FloatField(new GUIContent("Camera Rotation X", "相机的x轴旋转"), data.cameraRotationX);
                    data.cameraHeight = EditorGUILayout.FloatField(new GUIContent("Camera height", "相机的高度"), data.cameraHeight);
                    data.loadRangeScale = EditorGUILayout.Vector2Field(new GUIContent("Scale", "缩放因子, 最终计算出的大小会乘以这个缩放因子"), data.loadRangeScale);
                    var loadSize = MapEditor.instance.GetLoadSize(data.cameraFov, data.resolution.x / data.resolution.y, data.cameraRotationX, data.cameraHeight, data.loadRangeScale);
                    EditorGUILayout.FloatField(new GUIContent("Load Range Width", "计算出的视野宽"), loadSize.x);
                    EditorGUILayout.FloatField(new GUIContent("Load Range Height", "计算出的视野高"), loadSize.y);
                    EditorGUILayout.EndVertical();
                }
            }
        }

        void SetBorderHeight(float borderHeight)
        {
            Map.currentMap.data.borderHeight = borderHeight;
            EditorConfig.dirtyFlag |= DirtyMask.NPCSpawnPoints;
        }

        [MenuItem("Framework/Nav/Draw Path", false, 364)]
        static void DrawPathCommand()
        {
            var window = EditorWindow.GetWindow<TFW.Map.PathViewerWindow>("Draw Path");
            EditorUtils.CenterWindow(window, 500, 300, 500, 300);
            window.Show();
        }

        [MenuItem("Framework/Nav/Draw Vector", false, 365)]
        static void DrawVectorCommand()
        {
            var window = EditorWindow.GetWindow<TFW.Map.VectorViewerWindow>("Draw Vector");
            EditorUtils.CenterWindow(window, 500, 300, 500, 300);
            window.Show();
        }

        [MenuItem("Framework/NPC Region/Create", false, 366)]
        static void CreateNPCRegion()
        {
            bool suc = TSVReader.Load($"{MapModule.configResDirectory}map_npc_refresh_zone.tsv");
            if (suc)
            {
                var rows = TSVReader.rows;
                int column = TSVReader.GetColumnIndex("lvl");
                if (column >= 0)
                {
                    List<int> levels = new List<int>();
                    for (int i = 0; i < rows.Count; ++i)
                    {
                        bool s;
                        var level = TSVReader.GetInt(i, column, out s);
                        levels.Add((int)level);
                    }
                    //temp code, remove 180 later
                    NPCRegionViewer.Create(levels, 8, 180, 180, new Color32(204, 249, 204, 255), new Color32(17, 123, 17, 255), true);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Can't find map_npc_refresh_zone.tsv!", "OK");
            }
        }

        [MenuItem("Framework/NPC Region/Display", false, 366)]
        static void DisplayNPCRegion()
        {
            NPCRegionViewer.SetVisible(!NPCRegionViewer.visible);
        }

        [MenuItem("Framework/Region/Display", false, 400)]
        static void DisplayRegion()
        {
            if (Map.currentMap.data.gridRegionSetting != null)
            {
                Map.currentMap.data.gridRegionSetting.ShowRegions();
            }
        }

        [MenuItem("Framework/Rail/Create Prefab", false, 367)]
        static void CreateRailPrefab()
        {
            var window = EditorWindow.GetWindow<RailPrefabCreateWindow>("Create Rail Prefab");
            EditorUtils.CenterWindow(window, 500, 300, 500, 300);
            window.Show();
        }

        void DrawGlobalGridSetting()
        {
            mFoldMapGrid = EditorGUILayout.Foldout(mFoldMapGrid, new GUIContent("Map Grid", "地图辅助线设置,只在编辑器中显示"));
            if (mFoldMapGrid)
            {
                EditorGUILayout.BeginVertical("GroupBox");
                var grid = (Map.currentMap as EditorMap).grid;
                EditorGUILayout.BeginHorizontal();
                bool isActive = EditorGUILayout.ToggleLeft(new GUIContent("Show","是否显示辅助线"), grid.isActive);
#if false
                bool showGridIndex = EditorGUILayout.ToggleLeft("Show Grid Index", grid.showGridIndex);
                if (showGridIndex != grid.showGridIndex)
                {
                    grid.ShowGridIndex(showGridIndex);
                }
#endif
                EditorGUILayout.EndHorizontal();
                grid.SetActive(isActive);
                EditorGUILayout.FloatField(new GUIContent("Total Width", "总宽度"), grid.totalWidth);
                EditorGUILayout.FloatField(new GUIContent("Total Height", "总高度"), grid.totalHeight);
                EditorGUILayout.FloatField(new GUIContent("Grid Width", "辅助线格子的宽度"), grid.gridWidth);
                EditorGUILayout.FloatField(new GUIContent("Grid Height", "辅助线格子的高度"), grid.gridHeight);

                if (GUILayout.Button(new GUIContent("Change Map Grid Size", "修改辅助线格子大小")))
                {
                    var dlg = EditorUtils.CreateInputDialog("Change Map Grid Setting");
                    var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Total Width", "", grid.totalWidth.ToString()),
                    new InputDialog.StringItem("Total Height", "", grid.totalHeight.ToString()),
                    new InputDialog.StringItem("Grid Width", "", grid.gridWidth.ToString()),
                    new InputDialog.StringItem("Grid Height", "", grid.gridHeight.ToString()),
                    };
                    dlg.Show(items, OnClickChangeMapGridSetting);
                }
                EditorGUILayout.EndVertical();
            }
        }

        void DrawMapDataGenerationRangeSetting()
        {
            mFoldMapDataGenerationRange = EditorGUILayout.Foldout(mFoldMapDataGenerationRange, new GUIContent("Map Data Generation Range", "只在该范围内生成地图导航,障碍物,npc刷新点数据"));
            if (mFoldMapDataGenerationRange)
            {
                var range = Map.currentMap.data.mapDataGenerationRange;
                var min = range.min;
                EditorGUILayout.BeginVertical("GroupBox");
                float x = EditorGUILayout.FloatField(new GUIContent("X", "起点x值"), min.x);
                float z = EditorGUILayout.FloatField(new GUIContent("Z", "起点z值"), min.z);
                float width = EditorGUILayout.FloatField(new GUIContent("Width", "区域宽度"), range.size.x);
                float height = EditorGUILayout.FloatField(new GUIContent("Height", "区域高度"), range.size.z);
                range.SetMinMax(new Vector3(x, 0, z), new Vector3(x + width, 0, z + height));
                Map.currentMap.data.mapDataGenerationRange = range;
                EditorGUILayout.EndVertical();
            }
        }

        static bool OnClickChangeMapGridSetting(List<InputDialog.Item> parameters)
        {
            var map = Map.currentMap;
            if (map != null)
            {
                float totalWidth;
                float totalHeight;
                float gridWidth;
                float gridHeight;
                bool suc = Utils.ParseFloat((parameters[0] as InputDialog.StringItem).text, out totalWidth);
                suc &= Utils.ParseFloat((parameters[1] as InputDialog.StringItem).text, out totalHeight);
                suc &= Utils.ParseFloat((parameters[2] as InputDialog.StringItem).text, out gridWidth);
                suc &= Utils.ParseFloat((parameters[3] as InputDialog.StringItem).text, out gridHeight);
                if (!suc)
                {
                    return false;
                }

                if (totalWidth > 0 && totalHeight > 0 && gridWidth <= totalWidth && gridHeight <= totalHeight && gridWidth > 0 && gridHeight > 0)
                {
                    var grid = (Map.currentMap as EditorMap).grid;
                    grid.Create(grid.isActive, totalWidth, totalHeight, gridWidth, gridHeight, grid.color);
                    return true;
                }

                return false;
            }

            return true;
        }

        public static void CreateObstacles(string path)
        {
            GridModelLayerLogic.CreatePrefabObstacles(path);
            CreateAndExportGlobalObstacles(path);
        }

        static void CreateAndExportGlobalObstacles(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                return;
            }

            //StopWatchWrapper w = new StopWatchWrapper();
            //w.Start();
            var globalObstacleManager = Map.currentMap.data.globalObstacleManager;
            var meshBlocks = globalObstacleManager.CreateObstacles((Map.currentMap as EditorMap).globalObstacleMode);
            if (globalObstacleManager.gridSize > 0)
            {
                globalObstacleManager.GenerateGridObstacleData(globalObstacleManager.gridSize);
            }
            Mesh mesh = globalObstacleManager.obstacleMesh;
            var triangleTypeSettings = Utils.CreateTriangleTypeSettings(MapCoreDef.MAP_MAX_REGION_TYPE_ID, meshBlocks[0].triangleTypes, meshBlocks[0].triangleStates);
            var vertices = mesh == null ? null : mesh.vertices;
            var indices = mesh == null ? null : mesh.triangles;
            //var t = w.Stop();
            //Debug.LogError($"CreateAndExportGlobalObstacles cost: {t}");

            //w.Start();
            JSONExporter.ExportNavMesh("global_obstacles", folderPath, vertices, indices, meshBlocks[0].triangleTypes, triangleTypeSettings);
            //t = w.Stop();
            //Debug.LogError($"ExportNavMesh cost: {t}");
        }

        bool mFoldMapGrid = true;
        bool mFoldMapDataGenerationRange = true;
        bool mShowTiles = false;
        bool mFoldLoadRange = true;
        Dictionary<Vector2Int, int> mPropertyTiles = null;
        Bounds mMapBounds = new Bounds();
        DetailSpritesEditor mDetailSpritesEditor;

        string[] mObstacleModeNames = new string[] { "Island And Ocean", "Land" };
        string[] mNavMeshModeNames = new string[] {
        "Land",
        //海岛类型的navmesh,将navmesh分为两类,海岛区域和海洋区域,海洋区域初始化为不可行走.但是可以动态调整为可以行走.海岛区域和陆地类型navmesh一样
        "Island And Ocean",
        //海岛类型的navmesh,但是只创建海岛的navmesh,忽略海面
        "Island",
        //创建关卡地图的导航网格,分为closedAreaDetector生成的区域和关卡通道的区域
        "Closed Area And Gate",
        "Island Closed Area And Gate"
        };
        List<NavigationCreateMode> mNavMeshModes = new List<NavigationCreateMode>
        {
        NavigationCreateMode.CreateLandNavigationMesh,
        NavigationCreateMode.CreateIslandAndOceanNavigationMesh,
        NavigationCreateMode.CreateIslandNavigationMesh,
        NavigationCreateMode.CreateClosedAreaAndGateNavMesh,
        NavigationCreateMode.CreateIslandClosedAreaNavigationMesh,
        };
        List<NavigationCreateMode> mObstacleModes = new List<NavigationCreateMode>
        {
        NavigationCreateMode.CreateIslandAndOceanObstacles,
        NavigationCreateMode.CreateLandObstacles,
        };
    }
}

#endif