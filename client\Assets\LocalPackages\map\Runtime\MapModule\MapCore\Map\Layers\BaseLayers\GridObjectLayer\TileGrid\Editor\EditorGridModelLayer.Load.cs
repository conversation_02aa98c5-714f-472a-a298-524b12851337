﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class EditorGridModelLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);
            PrepareLoading();
            LoadSetting(reader);
            LoadPrefabManager(reader);
            var layerData = LoadLayerData(reader, AllocateID(), version);

            reader.Close();

            var layer = new EditorGridModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static void LoadSetting(BinaryReader reader)
        {
            long pathMapperDataOffset = reader.ReadInt64();
            long curPos = reader.BaseStream.Position;
            reader.BaseStream.Position = pathMapperDataOffset;
            LoadPathMapper(reader);
            reader.BaseStream.Position = curPos;
        }

        static config.GridModelLayerData LoadLayerData(BinaryReader reader, int layerID, Version version)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            int rows = reader.ReadInt32();
            int cols = reader.ReadInt32();
            float tileWidth = reader.ReadSingle();
            float tileHeight = reader.ReadSingle();

            var objects = new config.GridMapObjectData[rows * cols];

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    bool hasTile = reader.ReadBoolean();
                    if (hasTile)
                    {
                        var idx = i * cols + j;
                        objects[idx] = LoadGridModelData(reader);
                    }
                }
            }

            var config = LoadMapLayerLODConfig(reader, version);

            var layer = new config.GridModelLayerData(layerID, layerName, layerOffset, config, rows, cols, tileWidth, tileHeight, GridType.Rectangle, objects);
            layer.active = active;
            return layer;
        }

        static config.GridModelData LoadGridModelData(BinaryReader reader)
        {
            var gridModelData = new config.GridModelData();
            gridModelData.flag = reader.ReadInt32();
            gridModelData.position = Utils.ReadVector3(reader);
            var prefabPathIndex = reader.ReadInt16();
            gridModelData.SetID(AllocateID());
            string prefabPath = mLoadPrefabPathStringTable[prefabPathIndex];
            var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(gridModelData.id, prefabPath, true, false);
            if (modelTemplate == null)
            {
                gridModelData.modelTemplateID = 0;
            }
            else
            {
                gridModelData.modelTemplateID = modelTemplate.id;
            }
            return gridModelData;
        }

        static config.MapLayerLODConfig LoadMapLayerLODConfig(BinaryReader reader, Version version)
        {
            var config = new config.MapLayerLODConfig();
            int n = reader.ReadInt32();
            config.lodConfigs = new config.MapLayerLODConfigItem[n];
            for (int i = 0; i < n; ++i)
            {
                float zoom = reader.ReadSingle();
                float threshold = reader.ReadSingle();
                var hideObject = reader.ReadBoolean();
                var shaderLOD = reader.ReadInt32();
                var useRenderTexture = reader.ReadBoolean();
                MapLayerLODConfigFlag flag = MapLayerLODConfigFlag.None;
                if (version.minorVersion >= 2)
                {
                    flag = (MapLayerLODConfigFlag)reader.ReadInt32();
                }
                string name = "";
                if (version.minorVersion >= 3)
                {
                    name = Utils.ReadString(reader);
                }
                else
                {
                    name = Map.currentMap.data.lodManager.ConvertZoomToName(zoom);
                }
                config.lodConfigs[i] = new config.MapLayerLODConfigItem(name, zoom, threshold, hideObject, shaderLOD, useRenderTexture, flag, 0);
            }
            return config;
        }

        static void LoadPrefabManager(BinaryReader reader)
        {
            var prefabManagerData = LoadPrefabManagerData(reader);
            var prefabManager = (Map.currentMap.data as EditorMapData).gridModelLayerPrefabManager;
            EditorMap.CreatePrefabManager(prefabManager, prefabManagerData, false, true, false);
        }

        static config.PrefabManager LoadPrefabManagerData(BinaryReader reader)
        {
            var prefabManager = new config.PrefabManager();
            int nGroups = reader.ReadInt32();
            prefabManager.groups = new config.PrefabGroup[nGroups];
            for (int i = 0; i < nGroups; ++i)
            {
                prefabManager.groups[i] = LoadPrefabGroup(reader);
            }
            return prefabManager;
        }

        static config.PrefabGroup LoadPrefabGroup(BinaryReader reader)
        {
            config.PrefabGroup group = new config.PrefabGroup();
            group.name = Utils.ReadString(reader);
            group.color = Utils.ReadColor32(reader);

            int nPrefabs = reader.ReadInt32();
            group.prefabPaths = new config.PrefabSubGroup[nPrefabs];
            for (int i = 0; i < nPrefabs; ++i)
            {
                group.prefabPaths[i] = new config.PrefabSubGroup();
                string prefabPath = Utils.ReadString(reader);
                prefabPath = mLoadPathMapper.Unmap(prefabPath);
                group.prefabPaths[i].prefabPath = prefabPath;
                //load subgroup prefabs

                int subGroupPrefabCount = reader.ReadInt32();
                group.prefabPaths[i].subGroupPrefabPaths = new string[subGroupPrefabCount];
                for (int k = 0; k < subGroupPrefabCount; ++k)
                {
                    string subgroupPrefabPath = Utils.ReadString(reader);
                    group.prefabPaths[i].subGroupPrefabPaths[k] = mLoadPathMapper.Unmap(subgroupPrefabPath);
                }
            }
            return group;
        }

        static void LoadPathMapper(BinaryReader reader)
        {
            int n = reader.ReadInt32();
            for (int i = 0; i < n; ++i)
            {
                var path = Utils.ReadString(reader);
                var guid = Utils.ReadString(reader);

                mLoadPathMapper.pathToGuid[path] = guid;
            }

            int pathCount = reader.ReadInt32();
            mLoadPrefabPathStringTable = new List<string>(pathCount);
            for (int i = 0; i < pathCount; ++i)
            {
                mLoadPrefabPathStringTable.Add(mLoadPathMapper.Unmap(Utils.ReadString(reader)));
            }
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }

        static void PrepareLoading()
        {
            mLoadPrefabPathStringTable = new List<string>();
            mLoadPathMapper = new PathMapper();
        }

        static PathMapper mLoadPathMapper = new PathMapper();
        static List<string> mLoadPrefabPathStringTable;
    }
}

#endif