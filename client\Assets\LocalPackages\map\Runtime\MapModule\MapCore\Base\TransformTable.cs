﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public class TransformTable
    {
        public TransformTable(Vector3[] scalings, Quaternion[] rotations)
        {
            mScalings = scalings;
            mRotations = rotations;
        }

        public Vector3 GetScale(int index)
        {
            return mScalings[index];
        }

        public Quaternion GetRotation(int index)
        {
            return mRotations[index];
        }

        public Vector3[] scalings { get { return mScalings; } }
        public Quaternion[] rotations { get { return mRotations; } }

        Vector3[] mScalings;
        Quaternion[] mRotations;
    }
}
