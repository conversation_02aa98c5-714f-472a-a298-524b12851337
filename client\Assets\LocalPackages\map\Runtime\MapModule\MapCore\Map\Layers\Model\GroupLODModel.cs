﻿ 



 
 

﻿using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    //在某个LOD将同一组的prefab替换成一个prefab,其他的prefab隐藏
    public class GroupLODModel : ModelBase
    {
        public void Init(Map map, string prefabPath, int lod, ModelLODGroup group, bool isLeader, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            mMap = map;
            mPrefabPath = prefabPath;
            mIsGroupLeader = isLeader;
            if (group != null && group.combineModels && group.lod == lod)
            {
                if (isLeader)
                {
                    mGameObject = mMap.view.reusableGameObjectPool.Require(mPrefabPath, position, scale, rotation);
                    Utils.HideGameObject(mGameObject);
                }
                else
                {
                    mGameObject = null;
                }
            }
            else
            {
                mGameObject = mMap.view.reusableGameObjectPool.Require(mPrefabPath, position, scale, rotation);
                Utils.HideGameObject(mGameObject);
            }

            if (System.Object.ReferenceEquals(mGameObject, null))
            {
                mPosition = position;
                mRotation = rotation;
                mScaling = scaling;
            }
        }

        //销毁时将模型返回对象池中
        protected override void OnDestroy()
        {
            if (mGameObject != null)
            {
                //在编辑器中不再允许手动删除game object了,必须使用编辑器提供的方法来删除game object
                mMap.view.reusableGameObjectPool.Release(mPrefabPath, mGameObject, mMap);
                mGameObject = null;
            }
        }

        public override void Release()
        {
            ReleaseToPool(this);
        }

        public static GroupLODModel Require(Map map, int modelTemplateID, int lod, ModelLODGroup group, bool isLeader, Vector3 position, Vector3 scale, Quaternion rotation)
        {
            var modelTemplate = map.FindObject(modelTemplateID) as ModelTemplate;
            var newPrefabPath = modelTemplate.GetLODPrefabPath(lod);
            var model = mPool.Require();
            model.Init(map, newPrefabPath, lod, group, isLeader, position, scale, rotation);
            return model;
        }

        static void ReleaseToPool(GroupLODModel model)
        {
            model.OnDestroy();
            mPool.Release(model);
        }

        public override Vector3 position {
            get {
                if (mGameObject != null)
                {
                    return mGameObject.transform.position;
                }
                return mPosition;
            }
        }
        public override Quaternion rotation {
            get {
                if (mGameObject != null)
                {
                    return mGameObject.transform.rotation;
                }
                return mRotation;
            }
        }
        public override Vector3 scaling {
            get {
                if (mGameObject != null)
                {
                    return mGameObject.transform.localScale;
                }
                return mScaling;
            }
        }

        Map mMap;
        string mPrefabPath;
        //这个pool在地图销毁时可以不用清除,因为pool中的物体不持有game object
        static ObjectPool<GroupLODModel> mPool = new ObjectPool<GroupLODModel>(1000, ()=>new GroupLODModel());

        //只有leader会显示,其他隐藏
        bool mIsGroupLeader;
        Vector3 mPosition;
        Vector3 mScaling;
        Quaternion mRotation;
    }
}
