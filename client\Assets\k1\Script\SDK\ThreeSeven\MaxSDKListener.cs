﻿
using UnityEngine;
using Newtonsoft.Json;

namespace maxsdk
{
    public abstract class MaxSDKListener : MonoBehaviour
    {
        /* --------- 初始化回调 ----------- */

        public void CallInitSuccess(string json)
        {
            MaxSDKInitBean initBean = JsonConvert.DeserializeObject<MaxSDKInitBean>(json);
            OnInitSuccess(initBean);
        }

        public void CallInitFail(string json)
        {
            OnInitFail(JsonToErrorInfo(json));
        }

        public abstract void OnInitSuccess(MaxSDKInitBean initBean);

        public abstract void OnInitFail(MaxSDKFailBean bean);

        /* --------- 登录回调 ----------- */

        public void CallLoginSuccess(string json)
        {
            MaxSDKLoginBean userInfo = JsonConvert.DeserializeObject<MaxSDKLoginBean>(json);
            OnLoginSuccess(userInfo);
        }

        public void CallLoginFail(string json)
        {
            OnLoginFail(JsonToErrorInfo(json));
        }

        public abstract void OnLoginSuccess(MaxSDKLoginBean bean);

        public abstract void OnLoginFail(MaxSDKFailBean bean);

        /* --------- 切换账号回调 ----------- */

        public void CallChangeAccountSuccess(string json)
        {
            MaxSDKLoginBean userInfo = JsonConvert.DeserializeObject<MaxSDKLoginBean>(json);
            OnChangeAccountSuccess(userInfo);
        }

        public void CallChangeAccountFail(string json)
        {
            OnChangeAccountFail(JsonToErrorInfo(json));
        }

        public abstract void OnChangeAccountSuccess(MaxSDKLoginBean bean);

        public abstract void OnChangeAccountFail(MaxSDKFailBean bean);


        /* --------- 支付回调 ----------- */

        public void CallPaySuccess(string json)
        {
            MaxSDKPayBean result = JsonConvert.DeserializeObject<MaxSDKPayBean>(json);
            OnPaySuccess(result);
        }

        public void CallPayFail(string json)
        {
            OnPayFail(JsonToErrorInfo(json));
        }

        public abstract void OnPaySuccess(MaxSDKPayBean bean);

        public abstract void OnPayFail(MaxSDKFailBean bean);

        /* --------- 分享回调 ----------- */

        public void CallShareSuccess(string json)
        {
            MaxSDKShareBean shareBean = JsonConvert.DeserializeObject<MaxSDKShareBean>(json);

            OnShareSuccess(shareBean);
        }

        public void CallShareFail(string json)
        {
            OnShareFail(JsonToErrorInfo(json));
        }

        public void CallShareCancel(string json)
        {
            OnShareCancel();
        }

        public abstract void OnShareSuccess(MaxSDKShareBean shareBean);

        public abstract void OnShareFail(MaxSDKFailBean bean);

        public abstract void OnShareCancel();



        /* --------- 注销登录回调【国内 SDK 独有回调】 ----------- */

        public void CallLogoutSuccess(string json)
        {
            MaxSDKLoginBean logoutBean = JsonConvert.DeserializeObject<MaxSDKLoginBean>(json);
            OnLogoutSuccess(logoutBean);
        }

        public void CallLogoutFail(string json)
        {
            OnLogoutFail(JsonToErrorInfo(json));
        }

        public abstract void OnLogoutSuccess(MaxSDKLoginBean logoutBean);

        public abstract void OnLogoutFail(MaxSDKFailBean bean);


        /* --------- 退出游戏回调 ----------- */

        public void CallExitSuccess(string json)
        {
            MaxSDKExitBean exitBean = JsonConvert.DeserializeObject<MaxSDKExitBean>(json);
            OnExitSuccess(exitBean);
        }

        public abstract void OnExitSuccess(MaxSDKExitBean exitBean);


        /* --------- 扩展行为接口方法回调 ----------- */

        public void CallActionSuccess(string json)
        {
            MaxSDKActionBean actionBean = JsonConvert.DeserializeObject<MaxSDKActionBean>(json);
            OnActionSuccess(actionBean);
        }

        public void CallActionFail(string json)
        {

            MaxSDKActionFailBean actionFailBean = JsonConvert.DeserializeObject<MaxSDKActionFailBean>(json); //反序列化
            OnActionFail(actionFailBean);
        }

        public abstract void OnActionSuccess(MaxSDKActionBean actionBean);

        public abstract void OnActionFail(MaxSDKActionFailBean bean);


        /* --------- 扩展方法接口，OnDispatchResult 回调 ----------- */

        public void CallDispatchResult(string json)
        {

            MaxSDKDispatchBean dispatchBean = JsonConvert.DeserializeObject<MaxSDKDispatchBean>(json); //反序列化
            OnDispatchResult(dispatchBean);
        }

        public abstract void OnDispatchResult(MaxSDKDispatchBean dispatchBean);


        /* --------- 我是一条华丽的分割线 ----------- */

        private MaxSDKFailBean JsonToErrorInfo(string json)
        {
            var data = SimpleJSON.JSONNode.Parse(json);
            MaxSDKFailBean errorInfo = JsonConvert.DeserializeObject<MaxSDKFailBean>(json);
            return errorInfo;
        }

    }
}


