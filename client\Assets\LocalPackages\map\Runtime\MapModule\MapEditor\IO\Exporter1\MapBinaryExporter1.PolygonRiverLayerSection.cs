﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SavePolygonRiverLayer(BinaryWriter writer, PolygonRiverLayer layer)
        {
            BeginSection(MapDataSectionType.PolygonRiverLayer, writer);

            writer.Write(VersionSetting.PolygonRiverLayerStructVersion);

            //-----------------version 1 start---------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);

            List<IMapObjectData> polygonRivers = new List<IMapObjectData>();
            layer.GetAllObjects(polygonRivers);
            writer.Write(layer.GetLayerData().tileWidth);
            writer.Write(layer.GetLayerData().tileHeight);
            int n = polygonRivers.Count;
            var countPos = writer.BaseStream.Position;
            writer.Write(n);
            int realObjectCount = 0;

            var rivePrefabInfos = layer.riverPrefabInfos;
            for (int i = 0; i < n; ++i)
            {
                var river = polygonRivers[i] as PolygonRiverData;
                var sections = river.sections;
                for (int s = 0; s < sections.Count; ++s)
                {
                    ++realObjectCount;
                    //export every section of river
                    var objectID = Map.currentMap.nextCustomObjectID;
                    var bounds = sections[s].bounds;
                    var position = Utils.ToVector3(bounds.center);
                    position.y = river.height;
                    var prefabPath = rivePrefabInfos[sections[s].id];
                    var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(sections[s].id, prefabPath, false, false);

                    mIDExport.Export(writer, objectID);
                    Utils.WriteVector3(writer, position);
                    mIDExport.Export(writer, modelTemplate.id);
                    writer.Write(river.hideLOD);
                }
            }
            Utils.WriteAndJump(writer, countPos, realObjectCount);
            //save map layer lod config
            SavePolygonRiverLayerLODConfigV1(writer, layer.GetLayerData());
            //-----------------version 1 end----------------------
            //-----------------version 2 start------------------------------
            SavePolygonRiverLayerLODConfigV2(writer, layer.GetLayerData());
            //-----------------version 2 end------------------------------

            //export river prefab manifest
            string riverAssetFolderPath = MapCoreDef.GetRiverAssetsFolderPath(SLGMakerEditor.instance.exportFolder);
            PolygonRiverAssetManifestGenerator.Generate(riverAssetFolderPath, layer);
        }

        void SavePolygonRiverLayerLODConfigV1(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
            }
        }

        void SavePolygonRiverLayerLODConfigV2(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                writer.Write(c.useRenderTexture);
            }
        }
    }
}

#endif