﻿ 



 
 

#if UNITY_EDITOR

using System.IO;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void Save<PERSON><PERSON><PERSON>(BinaryWriter writer)
        {
            BeginSection(MapDataSectionType.Camera, writer);

            writer.Write(VersionSetting.CameraStructVersion);

            //-------------------version 1 start------------------------------
            var map = Map.currentMap;
            var camera = map.camera;

            var cameraPos = camera.transform.position;
            Utils.WriteVector3(writer, cameraPos);
            Utils.WriteQuaternion(writer, camera.transform.rotation);
            writer.Write(camera.orthographic);
            writer.Write(camera.orthographicSize);
            writer.Write(camera.fieldOfView);
            //-------------------version 1 end------------------------------
        }
    }
}

#endif