﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace TFW.Map
{
    [CustomEditor(typeof(RailwayLayerLogic))]
    public class RailwayLayerUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mLogic = target as RailwayLayerLogic;

            mLogic.UpdateGizmoVisibilityState();
        }

        void OnDisable()
        {
            if (Map.currentMap != null)
            {
                mLogic.SetLayerBoundsVisible(false);
                mLogic.SetGridVisible(false);
                HidePrefabIndicator();
            }
        }

        public override void OnInspectorGUI()
        {
            var map = SLGMakerEditor.GetMap();
            if (map != null)
            {
                mLogic.DrawGizmoUI();
                mLogic.operation = (RailwayLayerOperationType)EditorGUILayout.EnumPopup("Operation", mLogic.operation);

                mLogic.currentObjectType = (RailObjectType)EditorGUILayout.EnumPopup("Object Type", mLogic.currentObjectType);

                var layer = map.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
                var layerID = mLogic.layerID;
                var layerData = map.FindObject(layerID) as RailwayLayerData;
                int railCount = layer.railLayerData.railCount;
                EditorGUILayout.LabelField("Rail Count", railCount.ToString());

                EditorGUILayout.BeginVertical();
                mLogic.currentRailIndex = EditorGUILayout.IntField("Rail Index", mLogic.currentRailIndex);
                mLogic.currentRailIndex = Mathf.Clamp(mLogic.currentRailIndex, 0, railCount - 1);

                EditorGUILayout.BeginHorizontal();
                mLogic.prefab = EditorGUILayout.ObjectField("Prefab", mLogic.prefab, typeof(GameObject), false, null) as GameObject;
                if (mLogic.currentObjectType == RailObjectType.Rail)
                {
                    if (GUILayout.Button("Tiling Rails"))
                    {
                        TilingRails();
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
                if (mLogic.currentObjectType == RailObjectType.Rail)
                {
                    EditorGUILayout.LabelField("Rail Length", layer.railLayerData.railTotalLength.ToString());
                    EditorGUILayout.LabelField("Rail Width", layer.railLayerData.railWidth.ToString());
                    EditorGUILayout.LabelField("Rail Prefab Length", layer.railLayerData.railPrefabLength.ToString());
                    EditorGUILayout.LabelField("Railway Center", layer.railLayerData.railwayCenter.ToString());

                    if (GUILayout.Button("Create Rails"))
                    {
                        var dlg = EditorUtils.CreateInputDialog("Rail Parameters");

                        var items = new List<InputDialog.Item> {
                            new InputDialog.StringItem("Rail Count", "", "5"),
                            new InputDialog.StringItem("Rail Full Length", "", (layerData.GetLayerWidthInMeter() * 0.5f).ToString()),
                            new InputDialog.StringItem("Rail Prefab Length", "", "50"),
                            new InputDialog.StringItem("Rail Width", "", "10"),
                        };
                        dlg.Show(items, OnClickCreateRails);
                    }
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Remove Rails"))
                {
                    layer.RemoveObjectsOfType(RailObjectType.Rail);
                }
                if (GUILayout.Button("Remove Tunnels"))
                {
                    layer.RemoveObjectsOfType(RailObjectType.Tunnel);
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button("Export Data"))
                {
                    string filepath = EditorUtility.SaveFilePanel("Select file", "", "tunnel_data", "tsv");
                    if (!string.IsNullOrEmpty(filepath))
                    {
                        ExportData(filepath);
                    }
                }

                layer.showColliders = EditorGUILayout.ToggleLeft("Show Colliders", layer.showColliders);

                mLogic.setting.Draw("Map Layer LOD Setting", layerData.lodConfig, 0, null, null);

                //temp code
                if (GUILayout.Button("Create LOD Group"))
                {
                    CreateTestGroups();
                }

                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.LabelField("Object Count", layerData.objectCount.ToString());
                EditorGUILayout.LabelField("Width", layerData.tileWidth.ToString());
                EditorGUILayout.LabelField("Height", layerData.tileHeight.ToString());
                EditorGUILayout.EndVertical();
            }
        }

        bool OnClickCreateRails(List<InputDialog.Item> parameters)
        {
            string railCountStr = (parameters[0] as InputDialog.StringItem).text;
            int count;
            bool suc = Utils.ParseInt(railCountStr, out count);
            count = Mathf.Clamp(count, 1, 100);

            string totalLengthStr = (parameters[1] as InputDialog.StringItem).text;
            float totalLength;
            suc = Utils.ParseFloat(totalLengthStr, out totalLength);
            if (!suc || totalLength < 1)
            {
                return false;
            }

            string prefabLengthStr = (parameters[2] as InputDialog.StringItem).text;
            float prefabLength;
            suc = Utils.ParseFloat(prefabLengthStr, out prefabLength);
            if (!suc || prefabLength <= 0)
            {
                return false;
            }

            string widthStr = (parameters[3] as InputDialog.StringItem).text;
            float width;
            suc = Utils.ParseFloat(widthStr, out width);
            if (!suc || width <= 0)
            {
                return false;
            }

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;

            layer.GenerateRailColliders(width, Map.currentMap.center, totalLength, count, prefabLength);
            return true;
        }

        float ClampAngle(float angle)
        {
            while (angle > 360)
            {
                angle -= 360;
            }
            return angle;
        }

        void OnSceneGUI()
        {
            var map = SLGMakerEditor.GetMap();
            var currentEvent = Event.current;

            bool clicked = false;
            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && currentEvent.alt == false)
            {
                //mLeftButtonDown = true;
                clicked = true;
            }

            if (currentEvent.type == EventType.MouseUp && currentEvent.button == 0)
            {
                //mLeftButtonDown = false;
            }

            var screenPos = EditorUtils.ConvertScreenPosition(currentEvent.mousePosition, map.camera.firstCamera);
            var worldPos = map.FromScreenToWorldPosition(screenPos);

            UpdateIndicator(worldPos);

            if (clicked)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
                var railLayerData = layer.railLayerData;
                var delta = worldPos - railLayerData.railwayCenter;
                var rot = Quaternion.FromToRotation(Vector3.right, delta.normalized);
                float angle = rot.eulerAngles.y;
                float railAngle = ClampAngle(railLayerData.GetRailAngle(mLogic.currentRailIndex));
                float deltaAngle = Mathf.Abs(angle - railAngle);
                if (deltaAngle <= 10)
                {
                    if (mLogic.currentObjectType == RailObjectType.Rail)
                    {
                        int railSegmentIdx = railLayerData.GetRailSegmentIndex(worldPos, railLayerData.railPrefabLength);
                        if (mLogic.operation == RailwayLayerOperationType.kCreateObject)
                        {
                            SetRail(railSegmentIdx, true);
                        }
                        else if (mLogic.operation == RailwayLayerOperationType.kRemoveObject)
                        {
                            SetRail(railSegmentIdx, false);
                        }
                    }
                    else if (mLogic.currentObjectType == RailObjectType.Tunnel)
                    {
                        CreateTunnel(worldPos);
                    }
                    else
                    {
                        Debug.Assert(false, "todo");
                    }
                }
            }

            HandleUtility.AddDefaultControl(0);
        }

        void TilingRails()
        {
            if (mLogic.prefab != null)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
                var railLayerData = layer.railLayerData;
                int railCount = Mathf.CeilToInt(railLayerData.railTotalLength / railLayerData.railPrefabLength);
                for (int i = 0; i < railCount; ++i)
                {
                    SetRail(i, true);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a rail prefab first!", "OK");
            }
        }

        void SetRail(int railSegmentIdx, bool add)
        {
            string railPrefabPath = AssetDatabase.GetAssetPath(mLogic.prefab);
            if (string.IsNullOrEmpty(railPrefabPath))
            {
                return;
            }

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
            var railLayerData = layer.railLayerData;
            Vector3 pos;
            Quaternion rot;
            railLayerData.GetRotationAndPosition(mLogic.currentRailIndex, railSegmentIdx, railLayerData.railPrefabLength, out pos, out rot);

            //remove original rail
            var objects = layer.layerData.objects;
            foreach (var p in objects)
            {
                if (p.Value.GetPosition() == pos && p.Value.GetRotation() == rot)
                {
                    layer.RemoveObject(p.Key);
                    break;
                }
            }

            if (add)
            {
                var id = Map.currentMap.nextCustomObjectID;
                var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(id, railPrefabPath, false, false);

                bool isGroupLeader = false;
                if (railSegmentIdx == 0)
                {
                    isGroupLeader = true;
                }
                var segmentIndex = railLayerData.CalculateRailSegmentIndex(pos + rot * Vector3.right * 1);
                var rail = new RailObjectData(id, Map.currentMap, (int)ObjectFlag.kAddToObjectList, pos, rot, Vector3.one, modelTemplate, RailObjectType.Rail, isGroupLeader, 0, mLogic.currentRailIndex, segmentIndex);
                layer.AddObject(rail);
            }
        }

        void UpdateIndicator(Vector3 worldPos)
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
            var railLayerData = layer.railLayerData;
            Vector3 pos;
            Quaternion rot;

            railLayerData.GetRotationAndPosition(mLogic.currentRailIndex, worldPos, railLayerData.railPrefabLength, out pos, out rot);
            if (mLogic.currentObjectType == RailObjectType.Tunnel)
            {
                var dir = pos - railLayerData.railwayCenter;
                dir.Normalize();
                var projPos = Vector3.Project(worldPos - railLayerData.railwayCenter, dir);
                pos = railLayerData.railwayCenter + projPos;
            }

            if (mLogic.operation == RailwayLayerOperationType.kCreateObject)
            {
                ShowPrefabIndicator();
            }
            else
            {
                HidePrefabIndicator();
            }

            var indicator = SLGMakerEditor.instance.prefabIndicator;
            pos.y += 1.0f;
            indicator.SetPosition(pos);
            indicator.SetRotation(rot);
        }

        void ShowPrefabIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(mLogic.prefab, null);
            if (mLogic.prefab != null)
            {
                indicator.SetActive(true);
                //indicator.SetPosition(new Vector3(1000000, 0, 0));
            }
            else
            {
                indicator.SetActive(false);
            }
        }

        void HidePrefabIndicator()
        {
            var indicator = SLGMakerEditor.instance.prefabIndicator;
            indicator.SetPrefab(null, null);
        }

        void CreateTunnel(Vector3 worldPos)
        {
            if (mLogic.prefab != null)
            {
                var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
                var railLayerData = layer.railLayerData;
                var prefabPath = AssetDatabase.GetAssetPath(mLogic.prefab);
                float angle = railLayerData.GetRailAngle(mLogic.currentRailIndex);
                var rot = Quaternion.Euler(0, angle, 0);
                var railDir = rot * Vector3.right;
                var projPos = Vector3.Project(worldPos - railLayerData.railwayCenter, railDir);
                var pos = railLayerData.railwayCenter + projPos;

                List<IMapObjectData> tunnels = layer.GetObjectsOfType(RailObjectType.Tunnel);
                for (int i = 0; i < tunnels.Count; ++i)
                {
                    if (tunnels[i].GetPosition() == pos)
                    {
                        layer.RemoveObject(tunnels[i].GetEntityID());
                        break;
                    }
                }

                var id = Map.currentMap.nextCustomObjectID;
                var modelTemplate = Map.currentMap.GetOrCreateModelTemplate(id, prefabPath, false, false);
                int segmentIndex = railLayerData.CalculateRailSegmentIndex(pos + rot * Vector3.right * 1);
                var tunnel = new RailObjectData(id, Map.currentMap, 0, pos, rot, Vector3.one, modelTemplate, RailObjectType.Tunnel, false, 0, mLogic.currentRailIndex, segmentIndex);
                layer.AddObject(tunnel);
            }
        }

        void ExportData(string filepath)
        {
            StringBuilder builder = new StringBuilder();

            string[] headers = new string[] {
                "S_MAP_position",
            };

            for (int i = 0; i < headers.Length; ++i)
            {
                builder.Append(headers[i]);
                if (i != headers.Length - 1)
                {
                    builder.Append("\t");
                }
            }

            builder.AppendLine();

            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
            var allTunnels = layer.GetObjectsOfType(RailObjectType.Tunnel);

            for (int i = 0; i < allTunnels.Count; ++i)
            {
                var tunnel = allTunnels[i] as RailObjectData;
                var pos = tunnel.GetPosition();

                var s = string.Format("\"x\":{0},\"z\":{1}", Utils.F2I(pos.x), Utils.F2I(pos.z));
                s = "{" + s + "}\n";
                builder.Append(s);
            }

            var str = builder.ToString();
            File.WriteAllText(filepath, str);
        }

        void CreateTestGroups()
        {
            var layer = Map.currentMap.GetMapLayerByID(mLogic.layerID) as RailwayLayer;
            var railLayerData = layer.railLayerData;

            for (int i = 0; i < railLayerData.railCount; ++i)
            {
                var group = new ModelLODGroup(Map.currentMap.nextCustomObjectID, Map.currentMap);
                group.combineModels = true;
                group.lod = 2;

                layer.layerData.AddModelLODGroup(group);

                var objects = layer.GetRailObjects(i);
                var leader = objects[0] as RailObjectData;
                //temp code
                var pos = leader.GetPosition();
                var rot = leader.GetRotation();
                for (int k = 0; k < objects.Length; ++k)
                {
                    objects[k].SetModelLODGroupID(group.id);
                }
                leader.isGroupLeader = true;
                group.leaderObjectID = leader.id;
            }
        }

        RailwayLayerLogic mLogic;
        //bool mLeftButtonDown = false;

    }
}

#endif