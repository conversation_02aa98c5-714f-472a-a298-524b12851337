<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sirenix.OdinInspector.Attributes</name>
    </assembly>
    <members>
        <member name="T:Sirenix.OdinInspector.AssetListAttribute">
            <summary>
            <para>AssetLists is used on lists and arrays and single elements of unity types, and replaces the default list drawer with a list of all possible assets with the specified filter.</para>
            <para>Use this to both filter and include or exclude assets from a list or an array, without navigating the project window.</para>
            </summary>
            <remarks>
            <para>Asset lists works on all asset types such as materials, scriptable objects, prefabs, custom components, audio, textures etc, and does also show inherited types.</para>
            </remarks>
            <example>
            <para>The following example will display an asset list of all prefabs located in the project window.</para>
            <code>
            public class AssetListExamples : MonoBehaviour
            {
                [InfoBox("The AssetList attribute work on both lists of UnityEngine.Object types and UnityEngine.Object types, but have different behaviour.")]
                [AssetList]
                [InlineEditor(InlineEditorModes.LargePreview)]
                public GameObject Prefab;
            
                [AssetList]
                public List&lt;PlaceableObject&gt; PlaceableObjects;
            
                [AssetList(Path = "Plugins/Sirenix/")]
                [InlineEditor(InlineEditorModes.LargePreview)]
                public UnityEngine.Object Object;
            
                [AssetList(AutoPopulate = true)]
                public List&lt;PlaceableObject&gt; PlaceableObjectsAutoPopulated;
            
                [AssetList(LayerNames = "MyLayerName")]
                public GameObject[] AllPrefabsWithLayerName;
            
                [AssetList(AssetNamePrefix = "Rock")]
                public List&lt;GameObject&gt; PrefabsStartingWithRock;
            
                [AssetList(Path = "/Plugins/Sirenix/")]
                public List&lt;GameObject&gt; AllPrefabsLocatedInFolder;
            
                [AssetList(Tags = "MyTagA, MyTabB", Path = "/Plugins/Sirenix/")]
                public List&lt;GameObject&gt; GameObjectsWithTag;
            
                [AssetList(Path = "/Plugins/Sirenix/")]
                public List&lt;Material&gt; AllMaterialsInSirenix;
            
                [AssetList(Path = "/Plugins/Sirenix/")]
                public List&lt;ScriptableObject&gt; AllScriptableObjects;
            
                [InfoBox("Use a method as a custom filter for the asset list.")]
                [AssetList(CustomFilterMethod = "HasRigidbodyComponent")]
                public List&lt;GameObject&gt; MyRigidbodyPrefabs;
            
                private bool HasRigidbodyComponent(GameObject obj)
                {
                    return obj.GetComponent&lt;Rigidbody&gt;() != null;
                }
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.AutoPopulate">
            <summary>
            <para>If <c>true</c>, all assets found and displayed by the asset list, will automatically be added to the list when inspected.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.Tags">
            <summary>
            <para>Comma separated list of tags to filter the asset list.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.LayerNames">
            <summary>
            <para>Filter the asset list to only include assets with a specified layer.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.AssetNamePrefix">
            <summary>
            <para>Filter the asset list to only include assets which name begins with.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.Path">
            <summary>
            <para>Filter the asset list to only include assets which is located at the specified path.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetListAttribute.CustomFilterMethod">
            <summary>
            <para>Filter the asset list to only include assets for which the given filter method returns true.</para>
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.AssetListAttribute.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.AssetListAttribute"/> class.</para>
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.AssetSelectorAttribute">
            <summary>
            The AssetSelector attribute can be used on all Unity types and will prepend a small button next to the object field that when clicked,
            will present the user with a dropdown of assets to select from which can be customized from the attribute.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.IsUniqueList">
            <summary>
            True by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.DrawDropdownForListElements">
            <summary>
            True by default. If the ValueDropdown attribute is applied to a list, then disabling this,
            will render all child elements normally without using the ValueDropdown. The ValueDropdown will
            still show up when you click the add button on the list drawer, unless <see cref="F:Sirenix.OdinInspector.AssetSelectorAttribute.DisableListAddButtonBehaviour"/> is true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.DisableListAddButtonBehaviour">
            <summary>
            False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.ExcludeExistingValuesInList">
            <summary>
            If the ValueDropdown attribute is applied to a list, and <see cref="F:Sirenix.OdinInspector.AssetSelectorAttribute.IsUniqueList"/> is set to true, then enabling this,
            will exclude existing values, instead of rendering a checkbox indicating whether the item is already included or not.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.ExpandAllMenuItems">
            <summary>
            If the dropdown renders a tree-view, then setting this to true will ensure everything is expanded by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.FlattenTreeView">
            <summary>
            By default, the dropdown will create a tree view.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.DropdownWidth">
            <summary>
            Gets or sets the width of the dropdown. Default is zero.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.DropdownHeight">
            <summary>
            Gets or sets the height of the dropdown. Default is zero.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.DropdownTitle">
            <summary>
            Gets or sets the title for the dropdown. Null by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.SearchInFolders">
            <summary>
            Specify which folders to search in. Specifying no folders will make it search in your entire project.
            Use the <see cref="P:Sirenix.OdinInspector.AssetSelectorAttribute.Paths"/> property for a more clean way of populating this array through attributes.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AssetSelectorAttribute.Filter">
            <summary>
            The filters we should use when calling AssetDatabase.FindAssets.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.AssetSelectorAttribute.Paths">
            <summary>
            <para>
            Specify which folders to search in. Specifying no folders will make it search in your entire project.
            You can decalir multiple paths using '|' as the seperator.
            Example: <code>[AssetList(Paths = "Assets/Textures|Assets/Other/Textures")]</code>
            </para>
            <para>
            This property is simply a more clean way of populating the <see cref="F:Sirenix.OdinInspector.AssetSelectorAttribute.SearchInFolders"/> array. 
            </para>
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.AssetsOnlyAttribute">
            <summary>
            <para>AssetsOnly is used on object properties, and restricts the property to project assets, and not scene objects.</para>
            <para>Use this when you want to ensure an object is from the project, and not from the scene.</para>
            </summary>
            <example>
            <para>The following example shows a component with a game object property, that must be a prefab from the project, and not a scene object.</para>
            <code>
            public MyComponent : MonoBehaviour
            {
            	[AssetsOnly]
            	public GameObject MyPrefab;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.SceneObjectsOnlyAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.BoxGroupAttribute">
            <summary>
            <para>BoxGroup is used on any property and organizes the property in a boxed group.</para>
            <para>Use this to cleanly organize relevant values together in the inspector.</para>
            </summary>
            <example>
            <para>The following example shows how BoxGroup is used to organize properties together into a box.</para>
            <code>
            public class BoxGroupExamples : MonoBehaviour
            {
                // Box with a centered title.
                [BoxGroup("Centered Title", centerLabel: true)]
                public int A;
            
                [BoxGroup("Centered Title", centerLabel: true)]
                public int B;
            
                [BoxGroup("Centered Title", centerLabel: true)]
                public int C;
            
                // Box with a title.
                [BoxGroup("Left Oriented Title")]
                public int D;
            
                [BoxGroup("Left Oriented Title")]
                public int E;
            
                // Box with a title recieved from a field.
                [BoxGroup("$DynamicTitle1"), LabelText("Dynamic Title")]
                public string DynamicTitle1 = "Dynamic box title";
            
                [BoxGroup("$DynamicTitle1")]
                public int F;
            
                // Box with a title recieved from a property.
                [BoxGroup("$DynamicTitle2")]
                public int G;
            
                [BoxGroup("$DynamicTitle2")]
                public int H;
            
                // Box without a title.
                [InfoBox("You can also hide the label of a box group.")]
                [BoxGroup("NoTitle", false)]
                public int I;
            
                [BoxGroup("NoTitle")]
                public int J;
            
                [BoxGroup("NoTitle")]
                public int K;
            
            #if UNITY_EDITOR
                public string DynamicTitle2
                {
                    get { return UnityEditor.PlayerSettings.productName; }
                }
            #endif
            
                [BoxGroup("Boxed Struct"), HideLabel]
                public SomeStruct BoxedStruct;
            
                public SomeStruct DefaultStruct;
            
                [Serializable]
                public struct SomeStruct
                {
                    public int One;
                    public int Two;
                    public int Three;
                }
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.FoldoutGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HorizontalGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.BoxGroupAttribute.ShowLabel">
            <summary>
            If <c>true</c> a label for the group will be drawn on top.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.BoxGroupAttribute.CenterLabel">
            <summary>
            If <c>true</c> the header label will be places in the center of the group header. Otherwise it will be in left side.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.BoxGroupAttribute.LabelText">
            <summary>
            If non-null, this is used instead of the group's name as the title label.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.BoxGroupAttribute.#ctor(System.String,System.Boolean,System.Boolean,System.Single)">
            <summary>
            Adds the property to the specified box group.
            </summary>
            <param name="group">The box group.</param>
            <param name="showLabel">If <c>true</c> a label will be drawn for the group.</param>
            <param name="centerLabel">If set to <c>true</c> the header label will be centered.</param>
            <param name="order">The order of the group in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.BoxGroupAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/> class. Use the other constructor overloads in order to show a header-label on the box group.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.BoxGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines the box group with another group.
            </summary>
            <param name="other">The other group.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ButtonAttribute">
            <summary>
            <para>Buttons are used on functions, and allows for clickable buttons in the inspector.</para>
            </summary>
            <example>
            <para>The following example shows a component that has an initialize method, that can be called from the inspector.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[Button]
            	private void Init()
            	{
            		// ...
            	}
            }
            </code>
            </example>
            <example>
            <para>The following example show how a Button could be used to test a function.</para>
            <code>
            public class MyBot : MonoBehaviour
            {
            	[Button]
            	private void Jump()
            	{
            		// ...
            	}
            }
            </code>
            </example>
            <example>
            <para>The following example show how a Button can named differently than the function it's been attached to.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[Button("Function")]
            	private void MyFunction()
            	{
            		// ...
            	}
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.InlineButtonAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.Name">
            <summary>
            Use this to override the label on the button.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.Style">
            <summary>
            The style in which to draw the button.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.Expanded">
            <summary>
            If the button contains parameters, you can disable the foldout it creates by setting this to true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.DisplayParameters">
            <summary>
            <para>Whether to display the button method's parameters (if any) as values in the inspector. True by default.</para>
            <para>If this is set to false, the button method will instead be invoked through an ActionResolver or ValueResolver (based on whether it returns a value), giving access to contextual named parameter values like "InspectorProperty property" that can be passed to the button method.</para>
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.DirtyOnClick">
            <summary>
            Whether the containing object or scene (if there is one) should be marked dirty when the button is clicked. True by default. Note that if this is false, undo for any changes caused by the button click is also disabled, as registering undo events also causes dirtying.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonAttribute.ButtonHeight">
            <summary>
            Gets the height of the button. If it's zero or below then use default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonAttribute.Icon">
            <summary>
            The icon to be displayed inside the button.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonAttribute.IconAlignment">
            <summary>
            The alignment of the icon that is displayed inside the button.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonAttribute.ButtonAlignment">
            <summary>
            The alignment of the button represented by a range from 0 to 1 where 0 is the left edge of the available space and 1 is the right edge.
            ButtonAlignment only has an effect when Stretch is set to false.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonAttribute.Stretch">
            <summary>
            Whether the button should stretch to fill all of the available space. Default value is true.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonAttribute.DrawResult">
            <summary>
            If the button has a return type, set this to false to not draw the result. Default value is true.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.ButtonSizes)">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
            <param name="size">The size of the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.Int32)">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
            <param name="buttonSize">The size of the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String,Sirenix.OdinInspector.ButtonSizes)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
            <param name="buttonSize">Size of the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String,System.Int32)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
            <param name="buttonSize">Size of the button in pixels.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.Int32,Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
            <param name="buttonSize">The size of the button.</param>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.ButtonSizes,Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector named after the method.
            </summary>
            <param name="size">The size of the button.</param>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String,Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String,Sirenix.OdinInspector.ButtonSizes,Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
            <param name="buttonSize">Size of the button.</param>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(System.String,System.Int32,Sirenix.OdinInspector.ButtonStyle)">
            <summary>
            Creates a button in the inspector with a custom name.
            </summary>
            <param name="name">Custom name for the button.</param>
            <param name="buttonSize">Size of the button in pixels.</param>
            <param name="parameterBtnStyle">Button style for methods with parameters.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment)">
            <summary>
            Creates a button in the inspector with a custom icon.
            </summary>
            <param name="icon">The icon to be displayed inside the button.</param>
            <param name="iconAlignment">The alignment of the icon that is displayed inside the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.SdfIconType)">
            <summary>
            Creates a button in the inspector with a custom icon.
            </summary>
            <param name="icon">The icon to be displayed inside the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonAttribute.#ctor(Sirenix.OdinInspector.SdfIconType,System.String)">
            <summary>
            Creates a button in the inspector with a custom icon.
            </summary>
            <param name="icon">The icon to be displayed inside the button.</param>
            <param name="name">Custom name for the button.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ButtonGroupAttribute">
             <summary>
             <para>ButtonGroup is used on any instance function, and adds buttons to the inspector organized into horizontal groups.</para>
             <para>Use this to organize multiple button in a tidy horizontal group.</para>
             </summary>
             <example>
             <para>The following example shows how ButtonGroup is used to organize two buttons into one group.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[ButtonGroup("MyGroup")]
            		private void A()
            		{
            			// ..
            		}
            
            		[ButtonGroup("MyGroup")]
            		private void B()
            		{
            			// ..
            		}
            	}
             </code>
             </example>
             <example>
             <para>The following example shows how ButtonGroup can be used to create multiple groups of buttons.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[ButtonGroup("First")]
            		private void A()
            		{ }
            
            		[ButtonGroup("First")]
            		private void B()
            		{ }
            
            		[ButtonGroup("")]
            		private void One()
            		{ }
            
            		[ButtonGroup("")]
            		private void Two()
            		{ }
            
            		[ButtonGroup("")]
            		private void Three()
            		{ }
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.ButtonAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.InlineButtonAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.FoldoutGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.HorizontalGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonGroupAttribute.ButtonHeight">
            <summary>
            Gets the height of the button. If it's zero or below then use default.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonGroupAttribute.IconAlignment">
            <summary>
            The alignment of the icon that is displayed inside the button.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonGroupAttribute.ButtonAlignment">
            <summary>
            The alignment of the button represented by a range from 0 to 1 where 0 is the left edge of the available space and 1 is the right edge.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ButtonGroupAttribute.Stretch">
            <summary>
            Whether the button should stretch to fill all of the available space. Default value is true.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ButtonGroupAttribute.#ctor(System.String,System.Single)">
            <summary>
            Organizes the button into the specified button group.
            </summary>
            <param name="group">The group to organize the button into.</param>
            <param name="order">The order of the group in the inspector..</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ButtonStyle">
            <summary>
            Button style for methods with parameters.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonStyle.CompactBox">
            <summary>
            Draws a foldout box around the parameters of the method with the button on the box header itself.
            This is the default style of a method with parameters.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonStyle.FoldoutButton">
            <summary>
            Draws a button with a foldout to expose the parameters of the method.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonStyle.Box">
            <summary>
            Draws a foldout box around the parameters of the method with the button at the bottom of the box.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ChildGameObjectsOnlyAttribute">
            <summary>
            The ChildGameObjectsOnly attribute can be used on Components and GameObject fields and will prepend a small button next to the object-field that
            will search through all child gameobjects for assignable objects and present them in a dropdown for the user to choose from.
            </summary>
            <seealso cref="T:System.Attribute" />
        </member>
        <member name="T:Sirenix.OdinInspector.ColorPaletteAttribute">
            <summary>
            <para>ColorPalette is used on any Color property, and allows for choosing colors from different definable palettes.</para>
            <para>Use this to allow the user to choose from a set of predefined color options.</para>
            </summary>
            <remarks>
            <para>See and edit the color palettes in Tools > Odin > Inspector > Preferences > Drawers > Color Palettes.</para>
            <note type="note">The color property is not tied to the color palette, and can be edited. Therefore the color will also not update if the ColorPalette is edited.</note>
            </remarks>
            <example>
            <para>The following example shows how ColorPalette is applied to a property. The user can freely choose between all available ColorPalettes.</para>
            <code>
            public class ColorPaletteExamples : MonoBehaviour
            {
                [ColorPalette]
                public Color ColorOptions;
            
                [ColorPalette("Underwater")]
                public Color UnderwaterColor;
            
                [ColorPalette("Fall"), HideLabel]
                public Color WideColorPalette;
            
                [ColorPalette("My Palette")]
                public Color MyColor;
            
                [ColorPalette("Clovers")]
                public Color[] ColorArray;
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.ColorPaletteAttribute.PaletteName">
            <summary>
            Gets the name of the palette.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ColorPaletteAttribute.ShowAlpha">
            <summary>
            Indicates if the color palette should show alpha values or not.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ColorPaletteAttribute.#ctor">
            <summary>
            Adds a color palette options to a Color property.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ColorPaletteAttribute.#ctor(System.String)">
            <summary>
            Adds color options to a Color property from a specific palette.
            </summary>
            <param name="paletteName">Name of the palette.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.CustomContextMenuAttribute">
             <summary>
             <para>CustomContextMenu is used on any property, and adds a custom options to the context menu for the property.</para>
             <para>Use this for when you want to add custom actions to the context menu of a property.</para>
             </summary>
             <remarks>
             <note type="note">CustomContextMenu currently does not support static functions.</note>
             </remarks>
             <example>
             <para>The following example shows how CustomContextMenu is used to add a custom option to a property.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[CustomContextMenu("My custom option", "MyAction")]
            		public Vector3 MyVector;
            
            		private void MyAction()
            		{
            			MyVector = Random.onUnitSphere;
            		}
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.DisableContextMenuAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.CustomContextMenuAttribute.MenuItem">
            <summary>
            The name of the menu item.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.CustomContextMenuAttribute.MethodName">
            <summary>
            The name of the callback method. Obsolete; use the Action member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.CustomContextMenuAttribute.Action">
            <summary>
            A resolved string defining the action to take when the context menu is clicked.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.CustomContextMenuAttribute.#ctor(System.String,System.String)">
            <summary>
            Adds a custom option to the context menu of the property.
            </summary>
            <param name="menuItem">The name of the menu item.</param>
            <param name="action">A resolved string defining the action to take when the context menu is clicked.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.CustomValueDrawerAttribute">
             <summary>
             Instead of making a new attribute, and a new drawer, for a one-time thing, you can with this attribute, make a method that acts as a custom property drawer.
             These drawers will out of the box have support for undo/redo and multi-selection.
             </summary>
             <example>
             Usage:
             <code>
             public class CustomDrawerExamples : MonoBehaviour
             {
                 public float From = 2, To = 7;
            
                 [CustomValueDrawer("MyStaticCustomDrawerStatic")]
                 public float CustomDrawerStatic;
            
                 [CustomValueDrawer("MyStaticCustomDrawerInstance")]
                 public float CustomDrawerInstance;
            
                 [CustomValueDrawer("MyStaticCustomDrawerArray")]
                 public float[] CustomDrawerArray;
            
             #if UNITY_EDITOR
            
                 private static float MyStaticCustomDrawerStatic(float value, GUIContent label)
                 {
                     return EditorGUILayout.Slider(value, 0f, 10f);
                 }
            
                 private float MyStaticCustomDrawerInstance(float value, GUIContent label)
                 {
                     return EditorGUILayout.Slider(value, this.From, this.To);
                 }
            
                 private float MyStaticCustomDrawerArray(float value, GUIContent label)
                 {
                     return EditorGUILayout.Slider(value, this.From, this.To);
                 }
            
             #endif
             }
             </code>
             </example>
        </member>
        <member name="P:Sirenix.OdinInspector.CustomValueDrawerAttribute.MethodName">
            <summary>
            Name of the custom drawer method. Obsolete; use the Action member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.CustomValueDrawerAttribute.Action">
            <summary>
            A resolved string that defines the custom drawer action to take, such as an expression or method invocation.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.CustomValueDrawerAttribute.#ctor(System.String)">
            <summary>
            Instead of making a new attribute, and a new drawer, for a one-time thing, you can with this attribute, make a method that acts as a custom property drawer.
            These drawers will out of the box have support for undo/redo and multi-selection.
            </summary>
            <param name="action">A resolved string that defines the custom drawer action to take, such as an expression or method invocation.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.DelayedPropertyAttribute">
            <summary>
            Delays applying changes to properties while they still being edited in the inspector.
            Similar to Unity's built-in Delayed attribute, but this attribute can also be applied to properties.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DetailedInfoBoxAttribute">
            <summary>
            <para>DetailedInfoBox is used on any property, and displays a message box that can be expanded to show more details.</para>
            <para>Use this to convey a message to a user, and give them the option to see more details.</para>
            </summary>
            <example>
            <para>The following example shows how DetailedInfoBox is used on a field.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[DetailedInfoBox("This is a message", "Here is some more details about that message")]
            	public int MyInt;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.InfoBoxAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.DetailedInfoBoxAttribute.Message">
            <summary>
            The message for the message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DetailedInfoBoxAttribute.Details">
            <summary>
            The hideable details of the message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DetailedInfoBoxAttribute.InfoMessageType">
            <summary>
            Type of the message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DetailedInfoBoxAttribute.VisibleIf">
            <summary>
            Optional name of a member to hide or show the message box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.DetailedInfoBoxAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.InfoMessageType,System.String)">
            <summary>
            Displays a message box with hideable details.
            </summary>
            <param name="message">The message for the message box.</param>
            <param name="details">The hideable details of the message box.</param>
            <param name="infoMessageType">Type of the message box.</param>
            <param name="visibleIf">Optional name of a member to hide or show the message box.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.DictionaryDrawerSettings">
            <summary>
            Customize the behavior for dictionaries in the inspector.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDrawerSettings.KeyLabel">
            <summary>
            Specify an alternative key label for the dictionary drawer.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDrawerSettings.ValueLabel">
            <summary>
            Specify an alternative value label for the dictionary drawer.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDrawerSettings.DisplayMode">
            <summary>
            Specify how the dictionary should draw its items.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDrawerSettings.IsReadOnly">
            <summary>
            Gets or sets a value indicating whether this instance is read only.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDrawerSettings.KeyColumnWidth">
            <summary>
            Gets or sets a value indicating the default key column width of the dictionary.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableContextMenuAttribute">
            <summary>
            <para>DisableContextMenu is used on any property and disables the context menu for that property.</para>
            <para>Use this if you do not want the context menu to be available for a property.</para>
            </summary>
            <example>
            <para>The following example shows how DisableContextMenu is used on a property.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[DisableContextMenu]
            	public Vector3 MyVector;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.CustomContextMenuAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.DisableContextMenuAttribute.DisableForMember">
            <summary>
            Whether to disable the context menu for the member itself.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisableContextMenuAttribute.DisableForCollectionElements">
            <summary>
            Whether to disable the context menu for collection elements.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.DisableContextMenuAttribute.#ctor(System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.DisableContextMenuAttribute" /> class.
            </summary>
            <param name="disableForMember">Whether to disable the context menu for the member itself.</param>
            <param name="disableCollectionElements">Whether to also disable the context menu of collection elements.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableIfAttribute">
             <summary>
             <para>DisableIf is used on any property, and can disable or enable the property in the inspector.</para>
             <para>Use this to disable properties when they are irrelevant.</para>
             </summary>
             <example>
             <para>The following example shows how a property can be disabled by the state of a field.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		public bool DisableProperty;
            
            		[DisableIf("DisableProperty")]
            		public int MyInt;
            		
            	    public SomeEnum SomeEnumField;
            		
            		[DisableIf("SomeEnumField", SomeEnum.SomeEnumMember)]
            		public string SomeString;
             }
             </code>
             </example>
             <example>
             <para>The following examples show how a property can be disabled by a function.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[EnableIf("MyDisableFunction")]
            		public int MyInt;
            
            		private bool MyDisableFunction()
            		{
            			// ...
            		}
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.DisableIfAttribute.MemberName">
            <summary>
            The name of a bool member field, property or method. Obsolete; use the Condition member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisableIfAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisableIfAttribute.Value">
            <summary>
            The optional condition value.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.DisableIfAttribute.#ctor(System.String)">
            <summary>
            Disables a property in the inspector, based on the value of a resolved string.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisableIfAttribute.#ctor(System.String,System.Object)">
            <summary>
            Disables a property in the inspector, if the resolved string evaluates to the specified value.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="optionalValue">Value to check against.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInAttribute">
            <summary>
            Disables a member based on which type of a prefab and instance it is in. 
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInEditorModeAttribute">
            <summary>
            <para>DisableInEditorMode is used on any property, and disables the property when not in play mode.</para>
            <para>Use this when you only want a property to be editable when in play mode.</para>
            </summary>
            <example>
            <para>The following example shows how DisableInEditorMode is used to disable a property when in the editor.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[DisableInEditorMode]
            	public int MyInt;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.DisableInPlayModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInInlineEditorsAttribute">
            <summary>
            Disables a property if it is drawn within an <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute"/>.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInNonPrefabsAttribute">
            <summary>
            Disables a property if it is drawn from a non-prefab asset or instance.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInPlayModeAttribute">
            <summary>
            <para>DisableInPlayMode is used on any property, and disables the property when in play mode.</para>
            <para>Use this to prevent users from editing a property when in play mode.</para>
            </summary>
            <example>
            <para>The following example shows how DisableInPlayMode is used to disable a property when in play mode.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[DisableInPlayMode]
            	public int MyInt;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.HideInPlayModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableInEditorModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HideInEditorModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInPrefabAssetsAttribute">
            <summary>
            Disables a property if it is drawn from a prefab asset.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInPrefabInstancesAttribute">
            <summary>
            Disables a property if it is drawn from a prefab instance.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisableInPrefabsAttribute">
            <summary>
            Disables a property if it is drawn from a prefab asset or a prefab instance.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DisallowModificationsInAttribute">
            <summary>
            DisallowModificationsIn disables / grays out members, preventing modifications from being made and enables validation,
            providing error messages in case a modification was made prior to introducing the attribute.
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.DisableInAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HideInAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.RequiredAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.DisplayAsStringAttribute">
             <summary>
             <para>DisplayAsString is used on any property, and displays a string in the inspector as text.</para>
             <para>Use this for when you want to show a string in the inspector, but not allow for any editing.</para>
             </summary>
             <remarks>
             <para>DisplayAsString uses the property's ToString method to display the property as a string.</para>
             </remarks>
             <example>
             <para>The following example shows how DisplayAsString is used to display a string property as text in the inspector.</para>
             <code>
             public class MyComponent : MonoBehaviour
                {
                    [DisplayAsString]
                    public string MyInt = 5;
            
                    // You can combine with <see cref="T:Sirenix.OdinInspector.HideLabelAttribute"/> to display a message in the inspector.
                    [DisplayAsString, HideLabel]
                    public string MyMessage = "This string will be displayed as text in the inspector";
            
                    [DisplayAsString(false)]
                    public string InlineMessage = "This string is very long, but has been configured to not overflow.";
                }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.TitleAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.MultiLinePropertyAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.DisplayAsStringAttribute.Overflow">
            <summary>
            If <c>true</c>, the string will overflow past the drawn space and be clipped when there's not enough space for the text.
            If <c>false</c> the string will expand to multiple lines, if there's not enough space when drawn.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisplayAsStringAttribute.Alignment">
            <summary>
            How the string should be aligned.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisplayAsStringAttribute.FontSize">
            <summary>
            The size of the font.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisplayAsStringAttribute.EnableRichText">
            <summary>
            If <c>true</c> the string will support rich text.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DisplayAsStringAttribute.Format">
            <summary>
            String for formatting the value. Type must implement the <c>IFormattable</c> interface.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(UnityEngine.TextAlignment)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="alignment">How the string should be aligned.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Int32)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="fontSize">The size of the font.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,UnityEngine.TextAlignment)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="alignment">How the string should be aligned.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,System.Int32)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="fontSize">The size of the font.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Int32,UnityEngine.TextAlignment)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="fontSize">The size of the font.</param>
            <param name="alignment">How the string should be aligned.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,System.Int32,UnityEngine.TextAlignment)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="fontSize">The size of the font.</param>
            <param name="alignment">How the string should be aligned.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(UnityEngine.TextAlignment,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="alignment">How the string should be aligned.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Int32,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="fontSize">The size of the font.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,UnityEngine.TextAlignment,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="alignment">How the string should be aligned.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,System.Int32,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="fontSize">The size of the font.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Int32,UnityEngine.TextAlignment,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="fontSize">The size of the font.</param>
            <param name="alignment">How the string should be aligned.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.DisplayAsStringAttribute.#ctor(System.Boolean,System.Int32,UnityEngine.TextAlignment,System.Boolean)">
            <summary>
            Displays the property as a string in the inspector.
            </summary>
            <param name="overflow">Value indicating if the string should overflow past the available space, or expand to multiple lines when there's not enough horizontal space.</param>
            <param name="fontSize">The size of the font.</param>
            <param name="alignment">How the string should be aligned.</param>
            <param name="enableRichText">If <c>true</c> the string will support rich text.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.DoNotDrawAsReferenceAttribute">
            <summary>
            Indicates that the member should not be drawn as a value reference, if it becomes a reference to another value in the tree. Beware, and use with care! This may lead to infinite draw loops!
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DontApplyToListElementsAttribute">
            <summary>
            <para>DontApplyToListElements is used on other attributes, and indicates that those attributes should be applied only to the list, and not to the elements of the list.</para>
            <para>Use this on attributes that should only work on a list or array property as a whole, and not on each element of the list.</para>
            </summary>
            <example>
            <para>The following example shows how DontApplyToListElements is used on <see cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>.</para>
            <code>
            [DontApplyToListElements]
            [AttributeUsage(AttributeTargets.All, AllowMultiple = true, Inherited = true)]
            public sealed class VisibleIfAttribute : Attribute
            {
                public string MemberName { get; private set; }
            
                public VisibleIfAttribute(string memberName)
                {
                    this.MemberName = memberName;
                }
            }
            </code>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.DontValidateAttribute">
            <summary>
            Tells the validation system that this member should not be validated. It will not show validation messages in the inspector, and it will not be scanned by the project validator.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DrawWithUnityAttribute">
            <summary>
            <para>DrawWithUnity can be applied to a field or property to make Odin draw it using Unity's old drawing system. Use it if you want to selectively disable Odin drawing for a particular member.</para>
            </summary>
            <remarks>
            <para>Note that this attribute does not mean "disable Odin completely for this property"; it is visual only in nature, and in fact represents an Odin drawer which calls into Unity's old property drawing system. As Odin is still ultimately responsible for arranging the drawing of the property, and since other attributes exist with a higher priority than this attribute, and it is not guaranteed that Unity will draw the property if another attribute is present to override this one.</para>
            </remarks>
        </member>
        <member name="T:Sirenix.OdinInspector.DrawWithVisualElementsAttribute">
            <summary>
            Force Odin to draw this value as an IMGUI-embedded UI Toolkit Visual Element.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.EnableGUIAttribute">
            <summary>
            <para>An attribute that enables GUI.</para>
            </summary>
            <example>
            <code>
            public class InlineEditorExamples : MonoBehaviour
            {
                [EnableGUI]
                public string SomeReadonlyProperty { get { return "My GUI is usually disabled." } }
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.ReadOnlyAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.EnableIfAttribute">
             <summary>
             <para>EnableIf is used on any property, and can enable or disable the property in the inspector.</para>
             <para>Use this to enable properties when they are relevant.</para>
             </summary>
             <example>
             <para>The following example shows how a property can be enabled by the state of a field.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		public bool EnableProperty;
            
            		[EnableIf("EnableProperty")]
            		public int MyInt;
            		
            	    public SomeEnum SomeEnumField;
            		
            		[EnableIf("SomeEnumField", SomeEnum.SomeEnumMember)]
            		public string SomeString;
             }
             </code>
             </example>
             <example>
             <para>The following examples show how a property can be enabled by a function.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[EnableIf("MyEnableFunction")]
            		public int MyInt;
            
            		private bool MyEnableFunction()
            		{
            			// ...
            		}
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.HideIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.DisableInEditorModeAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.DisableInPlayModeAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.EnableIfAttribute.MemberName">
            <summary>
            The name of a bool member field, property or method. Obsolete; use the Condition member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.EnableIfAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.EnableIfAttribute.Value">
            <summary>
            The optional condition value.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.EnableIfAttribute.#ctor(System.String)">
            <summary>
            Enables a property in the inspector, based on the value of a resolved string.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.EnableIfAttribute.#ctor(System.String,System.Object)">
            <summary>
            Enables a property in the inspector, if the resolved string evaluates to the specified value.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="optionalValue">Value to check against.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.EnableInAttribute">
            <summary>
            Enables a member based on which type of a prefab and instance it is. 
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.EnumPagingAttribute">
            <summary>
            <para>Draws an enum selector in the inspector with next and previous buttons to let you cycle through the available values for the enum property.</para>
            </summary>
            <example>
            <code>
            public enum MyEnum
            {
                One,
                Two,
                Three,
            }
            
            public class MyMonoBehaviour : MonoBehaviour
            {
                [EnumPaging]
                public MyEnum Value;
            }
            </code>
            </example>
            <seealso cref="T:System.Attribute" />
        </member>
        <member name="T:Sirenix.OdinInspector.EnumToggleButtonsAttribute">
             <summary>
             <para>Draws an enum in a horizontal button group instead of a dropdown.</para>
             </summary>
             <example>
             <code>
             public class MyComponent : MonoBehvaiour
             {
                 [EnumToggleButtons]
                 public MyBitmaskEnum MyBitmaskEnum;
            
                 [EnumToggleButtons]
                 public MyEnum MyEnum;
             }
            
             [Flags]
             public enum MyBitmaskEnum
             {
                 A = 1 &lt;&lt; 1, // 1
                 B = 1 &lt;&lt; 2, // 2
                 C = 1 &lt;&lt; 3, // 4
                 ALL = A | B | C
             }
            
             public enum MyEnum
             {
                 A,
                 B,
                 C
             }
             </code>
             </example>
             <seealso cref="T:System.Attribute" />
        </member>
        <member name="T:Sirenix.OdinInspector.FilePathAttribute">
             <summary>
             <para>FilePath is used on string properties, and provides an interface for file paths.</para>
             </summary>
             <example>
             <para>The following example demonstrates how FilePath is used.</para>
             <code>
            	public class FilePathExamples : MonoBehaviour
            	{
            		// By default, FilePath provides a path relative to the Unity project.
            		[FilePath]
            		public string UnityProjectPath;
            
            		// It is possible to provide custom parent path. Parent paths can be relative to the Unity project, or absolute.
            		[FilePath(ParentFolder = "Assets/Plugins/Sirenix")]
            		public string RelativeToParentPath;
            
            		// Using parent path, FilePath can also provide a path relative to a resources folder.
            		[FilePath(ParentFolder = "Assets/Resources")]
            		public string ResourcePath;
            
            		// Provide a comma seperated list of allowed extensions. Dots are optional.
            		[FilePath(Extensions = "cs")]
            		public string ScriptFiles;
            
            		// By setting AbsolutePath to true, the FilePath will provide an absolute path instead.
            		[FilePath(AbsolutePath = true)]
            		[BoxGroup("Conditions")]
            		public string AbsolutePath;
            
            		// FilePath can also be configured to show an error, if the provided path is invalid.
            		[FilePath(RequireValidPath = true)]
            		public string ValidPath;
            
            		// By default, FilePath will enforce the use of forward slashes. It can also be configured to use backslashes instead.
            		[FilePath(UseBackslashes = true)]
            		public string Backslashes;
            
            		// FilePath also supports member references with the $ symbol.
            		[FilePath(ParentFolder = "$DynamicParent", Extensions = "$DynamicExtensions")]
            		public string DynamicFilePath;
            
            		public string DynamicParent = "Assets/Plugin/Sirenix";
            
            		public string DynamicExtensions = "cs, unity, jpg";
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.FolderPathAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.DisplayAsStringAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.AbsolutePath">
            <summary>
            If <c>true</c> the FilePath will provide an absolute path, instead of a relative one.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.Extensions">
            <summary>
            Comma separated list of allowed file extensions. Dots are optional.
            Supports member referencing with $.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.ParentFolder">
            <summary>
            ParentFolder provides an override for where the path is relative to. ParentFolder can be relative to the Unity project, or an absolute path.
            Supports member referencing with $.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.RequireValidPath">
            <summary>
            If <c>true</c> an error will be displayed for invalid, or missing paths.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.RequireExistingPath">
            <summary>
            If <c>true</c> an error will be displayed for non-existing paths.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.UseBackslashes">
            <summary>
            By default FilePath enforces forward slashes. Set UseBackslashes to <c>true</c> if you want backslashes instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FilePathAttribute.IncludeFileExtension">
            <summary>
            If <c>true</c> the file path will include the file's extension.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.FilePathAttribute.ReadOnly">
            <summary>
            Gets or sets a value indicating whether the path should be read only.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.FolderPathAttribute">
            <summary>
            <para>FolderPath is used on string properties, and provides an interface for directory paths.</para>
            </summary>
            <example>
            <para>The following example demonstrates how FolderPath is used.</para>
            <code>
            public class FolderPathExamples : MonoBehaviour
            {
            	// By default, FolderPath provides a path relative to the Unity project.
            	[FolderPath]
            	public string UnityProjectPath;
            
            	// It is possible to provide custom parent patn. ParentFolder paths can be relative to the Unity project, or absolute.
            	[FolderPath(ParentFolder = "Assets/Plugins/Sirenix")]
            	public string RelativeToParentPath;
            
            	// Using ParentFolder, FolderPath can also provide a path relative to a resources folder.
            	[FolderPath(ParentFolder = "Assets/Resources")]
            	public string ResourcePath;
            
            	// By setting AbsolutePath to true, the FolderPath will provide an absolute path instead.
            	[FolderPath(AbsolutePath = true)]
            	public string AbsolutePath;
            
            	// FolderPath can also be configured to show an error, if the provided path is invalid.
            	[FolderPath(RequireValidPath = true)]
            	public string ValidPath;
            
            	// By default, FolderPath will enforce the use of forward slashes. It can also be configured to use backslashes instead.
            	[FolderPath(UseBackslashes = true)]
            	public string Backslashes;
            
            	// FolderPath also supports member references with the $ symbol.
            	[FolderPath(ParentFolder = "$DynamicParent")]
            	public string DynamicFolderPath;
            
            	public string DynamicParent = "Assets/Plugins/Sirenix";
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.FilePathAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisplayAsStringAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.FolderPathAttribute.AbsolutePath">
            <summary>
            If <c>true</c> the FolderPath will provide an absolute path, instead of a relative one.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FolderPathAttribute.ParentFolder">
            <summary>
            ParentFolder provides an override for where the path is relative to. ParentFolder can be relative to the Unity project, or an absolute path.
            Supports member referencing with $.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FolderPathAttribute.RequireValidPath">
            <summary>
            If <c>true</c> an error will be displayed for invalid, or missing paths.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FolderPathAttribute.RequireExistingPath">
            <summary>
            If <c>true</c> an error will be displayed for non-existing paths.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.FolderPathAttribute.UseBackslashes">
            <summary>
            By default FolderPath enforces forward slashes. Set UseBackslashes to <c>true</c> if you want backslashes instead.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.FoldoutGroupAttribute">
             <summary>
             <para>FoldoutGroup is used on any property, and organizes properties into a foldout.</para>
             <para>Use this to organize properties, and to allow the user to hide properties that are not relevant for them at the moment.</para>
             </summary>
             <example>
             <para>The following example shows how FoldoutGroup is used to organize properties into a foldout.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		[FoldoutGroup("MyGroup")]
            		public int A;
            
            		[FoldoutGroup("MyGroup")]
            		public int B;
            
            		[FoldoutGroup("MyGroup")]
            		public int C;
            	}
             </code>
             </example>
             <example>
             <para>The following example shows how properties can be organizes into multiple foldouts.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		[FoldoutGroup("First")]
            		public int A;
            
            		[FoldoutGroup("First")]
            		public int B;
            
            		[FoldoutGroup("Second")]
            		public int C;
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.PropertyGroupAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.FoldoutGroupAttribute.Expanded">
            <summary>
            Gets a value indicating whether or not the foldout should be expanded by default.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.FoldoutGroupAttribute.HasDefinedExpanded">
            <summary>
            Gets a value indicating whether or not the Expanded property has been set.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.FoldoutGroupAttribute.#ctor(System.String,System.Single)">
            <summary>
            Adds the property to the specified foldout group.
            </summary>
            <param name="groupName">Name of the foldout group.</param>
            <param name="order">The order of the group in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.FoldoutGroupAttribute.#ctor(System.String,System.Boolean,System.Single)">
            <summary>
            Adds the property to the specified foldout group.
            </summary>
            <param name="groupName">Name of the foldout group.</param>
            <param name="expanded">Whether or not the foldout should be expanded by default.</param>
            <param name="order">The order of the group in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.FoldoutGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines the foldout property with another.
            </summary>
            <param name="other">The group to combine with.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.GUIColorAttribute">
            <summary>
            <para>GUIColor is used on any property and changes the GUI color used to draw the property.</para>
            </summary>
            <example>
            <para>The following example shows how GUIColor is used on a properties to create a rainbow effect.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[GUIColor(1f, 0f, 0f)]
            	public int A;
            
            	[GUIColor(1f, 0.5f, 0f, 0.2f)]
            	public int B;
            
            	[GUIColor("GetColor")]
            	public int C;
            	
            	private Color GetColor() { return this.A == 0 ? Color.red : Color.white; }
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.GUIColorAttribute.Color">
            <summary>
            The GUI color of the property.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.GUIColorAttribute.GetColor">
            <summary>Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow.</summary>
        </member>
        <member name="M:Sirenix.OdinInspector.GUIColorAttribute.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Sets the GUI color for the property.
            </summary>
            <param name="r">The red channel.</param>
            <param name="g">The green channel.</param>
            <param name="b">The blue channel.</param>
            <param name="a">The alpha channel.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.GUIColorAttribute.#ctor(System.String)">
            <summary>
            Sets the GUI color for the property.
            </summary>
            <param name="getColor">Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor").</param>
        </member>
        <member name="T:Sirenix.OdinInspector.HideDuplicateReferenceBoxAttribute">
            <summary>
            Indicates that Odin should hide the reference box, if this property would otherwise be drawn as a reference to another property, due to duplicate reference values being encountered.
            Note that if the value is referencing itself recursively, then the reference box will be drawn regardless of this attribute in all recursive draw calls.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideIfAttribute">
             <summary>
             <para>HideIf is used on any property and can hide the property in the inspector.</para>
             <para>Use this to hide irrelevant properties based on the current state of the object.</para>
             </summary>
             <example>
             <para>This example shows a component with fields hidden by the state of another field.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		public bool HideProperties;
            
            		[HideIf("HideProperties")]
            		public int MyInt;
            
            		[HideIf("HideProperties", false)]
            		public string MyString;
            
            	    public SomeEnum SomeEnumField;
            
            		[HideIf("SomeEnumField", SomeEnum.SomeEnumMember)]
            		public string SomeString;
             }
             </code>
             </example>
             <example>
             <para>This example shows a component with a field that is hidden when the game object is inactive.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[HideIf("MyVisibleFunction")]
            		public int MyHideableField;
            
            		private bool MyVisibleFunction()
            		{
            			return !this.gameObject.activeInHierarchy;
            		}
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.HideIfAttribute.MemberName">
            <summary>
            The name of a bool member field, property or method. Obsolete; use the Condition member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HideIfAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HideIfAttribute.Value">
            <summary>
            The optional condition value.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HideIfAttribute.Animate">
            <summary>
            Whether or not to slide the property in and out when the state changes.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.HideIfAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Hides a property in the inspector, based on the value of a resolved string.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="animate">Whether or not to slide the property in and out when the state changes.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.HideIfAttribute.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Hides a property in the inspector, if the resolved string evaluates to the specified value.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="optionalValue">Value to check against.</param>
            <param name="animate">Whether or not to slide the property in and out when the state changes.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.HideIfGroupAttribute">
            <summary>
            <p>HideIfGroup allows for showing or hiding a group of properties based on a condition.</p>
            <p>The attribute is a group attribute and can therefore be combined with other group attributes, and even be used to show or hide entire groups.</p>
            <p>Note that in the vast majority of cases where you simply want to be able to control the visibility of a single group, it is better to use the VisibleIf parameter that *all* group attributes have.</p>
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HideIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ShowIfGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
            <seealso cref="T:UnityEngine.HideInInspector"/>
        </member>
        <member name="P:Sirenix.OdinInspector.HideIfGroupAttribute.Animate">
            <summary>
            Whether or not to visually animate group visibility changes.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HideIfGroupAttribute.Value">
            <summary>
            The optional member value.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.HideIfGroupAttribute.MemberName">
            <summary>
            Name of member to use when to hide the group. Defaults to the name of the group, by can be overriden by setting this property.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.HideIfGroupAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.HideIfGroupAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Makes a group that can be shown or hidden based on a condition.
            </summary>
            <param name="path">The group path.</param>
            <param name="animate">If <c>true</c> then a fade animation will be played when the group is hidden or shown.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.HideIfGroupAttribute.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Makes a group that can be shown or hidden based on a condition.
            </summary>
            <param name="path">The group path.</param>
            <param name="value">The value the member should equal for the property to shown.</param>
            <param name="animate">If <c>true</c> then a fade animation will be played when the group is hidden or shown.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.HideIfGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines HideIfGroup attributes.
            </summary>
            <param name="other">Another ShowIfGroup attribute.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInAttribute">
            <summary>
            Hides a member based on which type of a prefab and instance it is in. 
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInEditorModeAttribute">
            <summary>
            <para>HideInEditorMode is used on any property, and hides the property when not in play mode.</para>
            <para>Use this when you only want a property to only be visible play mode.</para>
            </summary>
            <example>
            <para>The following example shows how HideInEditorMode is used to hide a property when in the editor.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[HideInEditorMode]
            	public int MyInt;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.HideInPlayModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableInPlayModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInInlineEditorsAttribute">
            <summary>
            Hides a property if it is drawn within an <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute"/>.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInNonPrefabsAttribute">
            <summary>
            Hides a property if it is drawn from a non prefab instance or asset.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInPlayModeAttribute">
            <summary>
            <para>HideInPlayMode is used on any property, and hides the property when not in editor mode.</para>
            <para>Use this when you only want a property to only be visible the editor.</para>
            </summary>
            <example>
            <para>The following example shows how HideInPlayMode is used to hide a property when in play mode.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[HideInPlayMode]
            	public int MyInt;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.HideInEditorModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableInPlayModeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInPrefabAssetsAttribute">
            <summary>
            Hides a property if it is drawn from a prefab asset.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInPrefabInstancesAttribute">
            <summary>
            Hides a property if it is drawn from a prefab instance.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInPrefabsAttribute">
            <summary>
            Hides a property if it is drawn from a prefab instance or a prefab asset.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideInTablesAttribute">
            <summary>
            The HideInTables attribute is used to prevent members from showing up as columns in tables drawn using the <see cref="T:Sirenix.OdinInspector.TableListAttribute"/>.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.HideLabelAttribute">
            <summary>
            <para>HideLabel is used on any property, and hides the label in the inspector.</para>
            <para>Use this to hide the label of properties in the inspector.</para>
            </summary>
            <example>
            <para>The following example show how HideLabel is used to hide the label of a game object property.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[HideLabel]
            	public GameObject MyGameObjectWithoutLabel;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.LabelTextAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.HideMonoScriptAttribute">
            <summary>
            Apply HideMonoScript to your class to prevent the Script property from being shown in the inspector.
            <remarks>
            <para>This attribute has the same effect on a single type that the global configuration option "Show Mono Script In Editor" in "Preferences -> Odin Inspector -> General -> Drawers" has globally when disabled.</para>
            </remarks>
            </summary>
            <example>
            <para>The following example shows how to use this attribute.</para>
            <code>
            [HideMonoScript]
            public class MyComponent : MonoBehaviour
            {
                // The Script property will not be shown for this component in the inspector
            }
            </code>
            </example>
            <seealso cref="T:System.Attribute" />
        </member>
        <member name="T:Sirenix.OdinInspector.HideNetworkBehaviourFieldsAttribute">
            <summary>
            Apply HideNetworkBehaviourFields to your class to prevent the special "Network Channel" and "Network Send Interval" properties from being shown in the inspector for a NetworkBehaviour.
            This attribute has no effect on classes that are not derived from NetworkBehaviour.
            </summary>
            <example>
            <para>The following example shows how to use this attribute.</para>
            <code>
            [HideNetworkBehaviourFields]
            public class MyComponent : NetworkBehaviour
            {
                // The "Network Channel" and "Network Send Interval" properties will not be shown for this component in the inspector
            }
            </code>
            </example>
            <seealso cref="T:System.Attribute" />
        </member>
        <member name="T:Sirenix.OdinInspector.HideReferenceObjectPickerAttribute">
             <summary>
             Hides the polymorphic object-picker shown above the properties of non-Unity serialized reference types.
             </summary>
             <remarks>
             When the object picker is hidden, you can right click and set the instance to null, in order to set a new value.
             If you don't want this behavior, you can use <see cref="!:DisableContextMenu"/> attribute to ensure people can't change the value.
             </remarks>
             <seealso cref="!:Sirenix.Serialization.OdinSerializeAttribute"/>
             <example>
             <code>
             public class MyComponent : SerializedMonoBehaviour
             {
                 [Header("Hidden Object Pickers")]
                 [Indent]
                 [HideReferenceObjectPicker]
                 public MyCustomReferenceType OdinSerializedProperty1;
            
                 [Indent]
                 [HideReferenceObjectPicker]
                 public MyCustomReferenceType OdinSerializedProperty2;
            
                 [Indent]
                 [Header("Shown Object Pickers")]
                 public MyCustomReferenceType OdinSerializedProperty3;
            
                 [Indent]
                 public MyCustomReferenceType OdinSerializedProperty4;
            
                 public class MyCustomReferenceType
                 {
                     public int A;
                     public int B;
                     public int C;
                 }
             }
             </code>
             </example>
        </member>
        <member name="T:Sirenix.OdinInspector.HorizontalGroupAttribute">
            <summary>
            <para>HorizontalGroup is used group multiple properties horizontally in the inspector.</para>
            <para>The width can either be specified as percentage or pixels.</para>
            <para>All values between 0 and 1 will be treated as a percentage.</para>
            <para>If the width is 0 the column will be automatically sized.</para>
            <para>Margin-left and right can only be specified in pixels.</para>
            </summary>
            <example>
            <para>The following example shows how three properties have been grouped together horizontally.</para>
            <code>
            // The width can either be specified as percentage or pixels.
            // All values between 0 and 1 will be treated as a percentage.
            // If the width is 0 the column will be automatically sized.
            // Margin-left and right can only be specified in pixels.
            
            public class HorizontalGroupAttributeExamples : MonoBehaviour
            {
                [HorizontalGroup]
                public int A;
            
                [HideLabel, LabelWidth (150)]
                [HorizontalGroup(150)]
                public LayerMask B;
            
                // LabelWidth can be helpfull when dealing with HorizontalGroups.
                [HorizontalGroup("Group 1"), LabelWidth(15)]
                public int C;
            
                [HorizontalGroup("Group 1"), LabelWidth(15)]
                public int D;
            
                [HorizontalGroup("Group 1"), LabelWidth(15)]
                public int E;
            
                // Having multiple properties in a column can be achived using multiple groups. Checkout the "Combining Group Attributes" example.
                [HorizontalGroup("Split", 0.5f, PaddingRight = 15)]
                [BoxGroup("Split/Left"), LabelWidth(15)]
                public int L;
            
                [BoxGroup("Split/Right"), LabelWidth(15)]
                public int M;
            
                [BoxGroup("Split/Left"), LabelWidth(15)]
                public int N;
            
                [BoxGroup("Split/Right"), LabelWidth(15)]
                public int O;
            
                // Horizontal Group also has supprot for: Title, MarginLeft, MarginRight, PaddingLeft, PaddingRight, MinWidth and MaxWidth.
                [HorizontalGroup("MyButton", MarginLeft = 0.25f, MarginRight = 0.25f)]
                public void SomeButton()
                {
            
                }
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.VerticalGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.Width">
            <summary>
            The width. Values between 0 and 1 will be treated as percentage, 0 = auto, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.MarginLeft">
            <summary>
            The margin left. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.MarginRight">
            <summary>
            The margin right. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.PaddingLeft">
            <summary>
            The padding left. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.PaddingRight">
            <summary>
            The padding right. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.MinWidth">
            <summary>
            The minimum Width. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.MaxWidth">
            <summary>
            The maximum Width. Values between 0 and 1 will be treated as percentage, 0 = ignore, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.Gap">
            <summary>
            The width between each column. Values between 0 and 1 will be treated as percentage, otherwise pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.Title">
            <summary>
            Adds a title above the horizontal group.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.DisableAutomaticLabelWidth">
            <summary>
            Fallback to using the default label width, whatever that might be.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.HorizontalGroupAttribute.LabelWidth">
            <summary>
            The label width, 0 = auto.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.HorizontalGroupAttribute.#ctor(System.String,System.Single,System.Int32,System.Int32,System.Single)">
            <summary>
            Organizes the property in a horizontal group.
            </summary>
            <param name="group">The group for the property.</param>
            <param name="width">The width of the property. Values between 0 and 1 are interpolated as a percentage, otherwise pixels.</param>
            <param name="marginLeft">The left margin in pixels.</param>
            <param name="marginRight">The right margin in pixels.</param>
            <param name="order">The order of the group in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.HorizontalGroupAttribute.#ctor(System.Single,System.Int32,System.Int32,System.Single)">
            <summary>
            Organizes the property in a horizontal group.
            </summary>
            <param name="width">The width of the property. Values between 0 and 1 are interpolated as a percentage, otherwise pixels.</param>
            <param name="marginLeft">The left margin in pixels.</param>
            <param name="marginRight">The right margin in pixels.</param>
            <param name="order">The order of the group in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.HorizontalGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Merges the values of two group attributes together.
            </summary>
            <param name="other">The other group to combine with.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.IndentAttribute">
            <summary>
            <para>Indent is used on any property and moves the property's label to the right.</para>
            <para>Use this to clearly organize properties in the inspector.</para>
            </summary>
            <example>
            <para>The following example shows how a property is indented by Indent.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[Indent]
            	public int IndentedInt;
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.IndentAttribute.IndentLevel">
            <summary>
            Indicates how much a property should be indented.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.IndentAttribute.#ctor(System.Int32)">
            <summary>
            Indents a property in the inspector.
            </summary>
            <param name="indentLevel">How much a property should be indented.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.InfoBoxAttribute">
             <summary>
             <para>InfoBox is used on any property, and display a text box above the property in the inspector.</para>
             <para>Use this to add comments or warn about the use of different properties.</para>
             </summary>
             <example>
             <para>The following example shows different info box types.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[InfoBox("This is an int property")]
            		public int MyInt;
            
            		[InfoBox("This info box is a warning", InfoMessageType.Warning)]
            		public float MyFloat;
            
            		[InfoBox("This info box is an error", InfoMessageType.Error)]
            		public object MyObject;
            
             	[InfoBox("This info box is just a box", InfoMessageType.None)]
            		public Vector3 MyVector;
            	}
             </code>
             </example>
             <example>
             <para>The following example how info boxes can be hidden by fields and properties.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[InfoBox("This info box is hidden by an instance field.", "InstanceShowInfoBoxField")]
            		public int MyInt;
            		public bool InstanceShowInfoBoxField;
            
            		[InfoBox("This info box is hideable by a static field.", "StaticShowInfoBoxField")]
            		public float MyFloat;
            		public static bool StaticShowInfoBoxField;
            
            		[InfoBox("This info box is hidden by an instance property.", "InstanceShowInfoBoxProperty")]
            		public int MyOtherInt;
             	public bool InstanceShowInfoBoxProperty { get; set; }
            
            		[InfoBox("This info box is hideable by a static property.", "StaticShowInfoBoxProperty")]
            		public float MyOtherFloat;
            		public static bool StaticShowInfoBoxProperty { get; set; }
             }
             </code>
             </example>
             <example>
             <para>The following example shows how info boxes can be hidden by functions.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[InfoBox("This info box is hidden by an instance function.", "InstanceShowFunction")]
            		public int MyInt;
            		public bool InstanceShowFunction()
            		{
            			return this.MyInt == 0;
            		}
            
            		[InfoBox("This info box is hidden by a static function.", "StaticShowFunction")]
            		public short MyShort;
            		public bool StaticShowFunction()
            		{
            			return true;
            		}
            
            		// You can also specify a function with the same type of parameter.
            		// Use this to specify the same function, for multiple different properties.
            		[InfoBox("This info box is hidden by an instance function with a parameter.", "InstanceShowParameterFunction")]
            		public GameObject MyGameObject;
            		public bool InstanceShowParameterFunction(GameObject property)
            		{
            			return property != null;
            		}
            
            		[InfoBox("This info box is hidden by a static function with a parameter.", "StaticShowParameterFunction")]
            		public Vector3 MyVector;
            		public bool StaticShowParameterFunction(Vector3 property)
            		{
            			return property.magnitude == 0f;
            		}
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.RequiredAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ValidateInputAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoBoxAttribute.Message">
            <summary>
            The message to display in the info box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoBoxAttribute.InfoMessageType">
            <summary>
            The type of the message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoBoxAttribute.VisibleIf">
            <summary>
            Optional member field, property or function to show and hide the info box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoBoxAttribute.GUIAlwaysEnabled">
            <summary>
            When <c>true</c> the InfoBox will ignore the GUI.enable flag and always draw as enabled.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.InfoBoxAttribute.Icon">
            <summary>
            The icon to be displayed next to the message.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoBoxAttribute.IconColor">
            <summary>Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow.</summary>
        </member>
        <member name="M:Sirenix.OdinInspector.InfoBoxAttribute.#ctor(System.String,Sirenix.OdinInspector.InfoMessageType,System.String)">
            <summary>
            Displays an info box above the property.
            </summary>
            <param name="message">The message for the message box. Supports referencing a member string field, property or method by using $.</param>
            <param name="infoMessageType">The type of the message box.</param>
            <param name="visibleIfMemberName">Name of member bool to show or hide the message box.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.InfoBoxAttribute.#ctor(System.String,System.String)">
            <summary>
            Displays an info box above the property.
            </summary>
            <param name="message">The message for the message box. Supports referencing a member string field, property or method by using $.</param>
            <param name="visibleIfMemberName">Name of member bool to show or hide the message box.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.InfoBoxAttribute.#ctor(System.String,Sirenix.OdinInspector.SdfIconType,System.String)">
            <summary>
            Displays an info box above the property.
            </summary>
            <param name="message">The message for the message box. Supports referencing a member string field, property or method by using $.</param>
            <param name="icon">The icon to be displayed next to the message.</param>
            <param name="visibleIfMemberName">Name of member bool to show or hide the message box.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.InlineButtonAttribute">
             <summary>
             <para>The inline button adds a button to the end of a property.</para>
             </summary>
             <remarks>
             <note type="note">Due to a bug, multiple inline buttons are currently not supported.</note>
             </remarks>
             <example>
             <para>The following examples demonstrates how InlineButton can be used.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		// Adds a button to the end of the A property.
            		[InlineButton("MyFunction")]
            		public int A;
            
            		// This is example demonstrates how you can change the label of the button.
            		// InlineButton also supports refering to string members with $.
            		[InlineButton("MyFunction", "Button")]
            		public int B;
            
             	private void MyFunction()
            		{
            			// ...
            		}
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.ButtonAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.InlineButtonAttribute.MemberMethod">
            <summary>
            Name of member method to call when the button is clicked. Obsolete; use the Action member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineButtonAttribute.Action">
            <summary>
            A resolved string that defines the action to perform when the button is clicked, such as an expression or method invocation.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineButtonAttribute.Label">
            <summary>
            Optional label of the button.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineButtonAttribute.ShowIf">
            <summary>
            Optional resolved string that specifies a condition for whether to show the inline button or not.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineButtonAttribute.ButtonColor">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineButtonAttribute.TextColor">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.InlineButtonAttribute.#ctor(System.String,System.String)">
            <summary>
            Draws a button to the right of the property.
            </summary>
            <param name="action">A resolved string that defines the action to perform when the button is clicked, such as an expression or method invocation.</param>
            <param name="label">Optional label of the button.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.InlineButtonAttribute.#ctor(System.String,Sirenix.OdinInspector.SdfIconType,System.String)">
            <summary>
            Draws a button to the right of the property.
            </summary>
            <param name="action">A resolved string that defines the action to perform when the button is clicked, such as an expression or method invocation.</param>
            <param name="icon">The icon to be shown inside the button.</param>
            <param name="label">Optional label of the button.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.InlineEditorAttribute">
             <summary>
             <para>InlineAttribute is used on any property or field with a type that inherits from UnityEngine.Object. This includes components and assets etc.</para>
             </summary>
             <example>
             <code>
             public class InlineEditorExamples : MonoBehaviour
             {
                 [DisableInInlineEditors]
                 public Vector3 DisabledInInlineEditors;
            
                 [HideInInlineEditors]
                 public Vector3 HiddenInInlineEditors;
            
                 [InlineEditor]
                 public Transform InlineComponent;
            
                 [InlineEditor(InlineEditorModes.FullEditor)]
                 public Material FullInlineEditor;
            
                 [InlineEditor(InlineEditorModes.GUIAndHeader)]
                 public Material InlineMaterial;
            
                 [InlineEditor(InlineEditorModes.SmallPreview)]
                 public Material[] InlineMaterialList;
            
                 [InlineEditor(InlineEditorModes.LargePreview)]
                 public GameObject InlineObjectPreview;
            
                 [InlineEditor(InlineEditorModes.LargePreview)]
                 public Mesh InlineMeshPreview;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.DisableInInlineEditorsAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.HideInInlineEditorsAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.InlineEditorAttribute.Expanded">
            <summary>
            If true, the inline editor will start expanded.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.DrawHeader">
            <summary>
            Draw the header editor header inline.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.DrawGUI">
            <summary>
            Draw editor GUI inline.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.DrawPreview">
            <summary>
            Draw editor preview inline.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.MaxHeight">
            <summary>
            Maximum height of the inline editor. If the inline editor exceeds the specified height, a scrollbar will appear.
            Values less or equals to zero will let the InlineEditor expand to its full size.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.PreviewWidth">
            <summary>
            The size of the editor preview if drawn together with GUI.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.PreviewHeight">
            <summary>
            The size of the editor preview if drawn alone.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.IncrementInlineEditorDrawerDepth">
            <summary>
            If false, this will prevent the InlineEditor attribute from incrementing the InlineEditorAttributeDrawer.CurrentInlineEditorDrawDepth.
            This is helpful in cases where you want to draw the entire editor, and disregard attributes
            such as [<see cref="T:Sirenix.OdinInspector.HideInInlineEditorsAttribute"/>] and [<see cref="T:Sirenix.OdinInspector.DisableInInlineEditorsAttribute"/>].
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.ObjectFieldMode">
            <summary>
            How the InlineEditor attribute drawer should draw the object field.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.DisableGUIForVCSLockedAssets">
            <summary>
            Whether to set GUI.enabled = false when drawing an editor for an asset that is locked by source control. Defaults to true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorAttribute.PreviewAlignment">
            <summary>
            Where to draw the preview.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.InlineEditorAttribute.#ctor(Sirenix.OdinInspector.InlineEditorModes,Sirenix.OdinInspector.InlineEditorObjectFieldModes)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute" /> class.
            </summary>
            <param name="inlineEditorMode">The inline editor mode.</param>
            <param name="objectFieldMode">How the object field should be drawn.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.InlineEditorAttribute.#ctor(Sirenix.OdinInspector.InlineEditorObjectFieldModes)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute"/> class.
            </summary>
            <param name="objectFieldMode">How the object field should be drawn.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.InlinePropertyAttribute">
             <summary>
             The Inline Property is used to place the contents of a type next to the label, instead of being rendered in a foldout.
             </summary>
             <example>
             <code>
             public class InlinePropertyExamples : MonoBehaviour
             {
                 public Vector3 Vector3;
            
                 public Vector3Int Vector3Int;
            
                 [InlineProperty(LabelWidth = 12)]  // It can be placed on classes as well as members
                 public Vector2Int Vector2Int;
            
             }
            
             [Serializable]
             [InlineProperty(LabelWidth = 12)] // It can be placed on classes as well as members
             public struct Vector3Int
             {
                 [HorizontalGroup]
                 public int X;
            
                 [HorizontalGroup]
                 public int Y;
            
                 [HorizontalGroup]
                 public int Z;
             }
            
             [Serializable]
             public struct Vector2Int
             {
                 [HorizontalGroup]
                 public int X;
            
                 [HorizontalGroup]
                 public int Y;
             }
             </code>
             </example>
             <seealso cref="T:System.Attribute" />
        </member>
        <member name="F:Sirenix.OdinInspector.InlinePropertyAttribute.LabelWidth">
            <summary>
            Specify a label width for all child properties.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.LabelTextAttribute">
             <summary>
             <para>LabelText is used to change the labels of properties.</para>
             <para>Use this if you want a different label than the name of the property.</para>
             </summary>
             <example>
             <para>The following example shows how LabelText is applied to a few property fields.</para>
             <code>
             public MyComponent : MonoBehaviour
             {
            		[LabelText("1")]
            		public int MyInt1;
            
            		[LabelText("2")]
            		public int MyInt2;
            
            		[LabelText("3")]
            		public int MyInt3;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.TitleAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.LabelTextAttribute.Text">
            <summary>
            The new text of the label.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.LabelTextAttribute.NicifyText">
            <summary>
            Whether the label text should be nicified before it is displayed, IE, "m_someField" becomes "Some Field".
            If the label text is resolved via a member reference, an expression, or the like, then the evaluated result
            of that member reference or expression will be nicified.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.LabelTextAttribute.Icon">
            <summary>
            The icon to be displayed.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.LabelTextAttribute.IconColor">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelTextAttribute.#ctor(System.String)">
            <summary>
            Give a property a custom label.
            </summary>
            <param name="text">The new text of the label.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelTextAttribute.#ctor(Sirenix.OdinInspector.SdfIconType)">
            <summary>
            Give a property a custom icon.
            </summary>
            <param name="icon">The icon to be shown next to the property.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelTextAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Give a property a custom label.
            </summary>
            <param name="text">The new text of the label.</param>
            <param name="nicifyText">Whether to nicify the label text.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelTextAttribute.#ctor(System.String,Sirenix.OdinInspector.SdfIconType)">
            <summary>
            Give a property a custom label with a custom icon.
            </summary>
            <param name="text">The new text of the label.</param>
            <param name="icon">The icon to be displayed.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelTextAttribute.#ctor(System.String,System.Boolean,Sirenix.OdinInspector.SdfIconType)">
            <summary>
            Give a property a custom label with a custom icon.
            </summary>
            <param name="text">The new text of the label.</param>
            <param name="nicifyText">Whether to nicify the label text.</param>
            <param name="icon">The icon to be displayed.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.LabelWidthAttribute">
            <summary>
            <para>LabelWidth is used to change the width of labels for properties.</para>
            </summary>
            <example>
            <para>The following example shows how LabelText is applied to a few property fields.</para>
            <code>
            public MyComponent : MonoBehaviour
            {
            	[LabelWidth("3")]
            	public int MyInt3;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.TitleAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.LabelWidthAttribute.Width">
            <summary>
            The new text of the label.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.LabelWidthAttribute.#ctor(System.Single)">
            <summary>
            Give a property a custom label.
            </summary>
            <param name="width">The width of the label.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ListDrawerSettingsAttribute">
             <summary>
             Customize the behavior for lists and arrays in the inspector.
             </summary>
             <example>
             <para>This example shows how you can add your own custom add button to a list.</para>
             <code>
             [ListDrawerSettings(HideAddButton = true, OnTitleBarGUI = "DrawTitleBarGUI")]
             public List&lt;MyType&gt; SomeList;
            
             #if UNITY_EDITOR
             private void DrawTitleBarGUI()
             {
                 if (SirenixEditorGUI.ToolbarButton(EditorIcons.Plus))
                 {
                     this.SomeList.Add(new MyType());
                 }
             }
             #endif
             </code>
             </example>
             <remarks>
             This attribute is scheduled for refactoring.
             </remarks>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.HideAddButton">
            <summary>
            If true, the add button will not be rendered in the title toolbar. You can use OnTitleBarGUI to implement your own add button.
            </summary>
            <value>
              <c>true</c> if [hide add button]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.HideRemoveButton">
            <summary>
            If true, the remove button  will not be rendered on list items. You can use OnBeginListElementGUI and OnEndListElementGUI to implement your own remove button.
            </summary>
            <value>
                <c>true</c> if [hide remove button]; otherwise, <c>false</c>.    
            </value>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ListElementLabelName">
            <summary>
            Specify the name of a member inside each list element which defines the label being drawn for each list element.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.CustomAddFunction">
            <summary>
            Override the default behaviour for adding objects to the list. 
            If the referenced member returns the list type element, it will be called once per selected object.
            If the referenced method returns void, it will only be called once regardless of how many objects are selected.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.OnBeginListElementGUI">
            <summary>
            Calls a method before each list element. The member referenced must have a return type of void, and an index parameter of type int which represents the element index being drawn.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.OnEndListElementGUI">
            <summary>
            Calls a method after each list element. The member referenced must have a return type of void, and an index parameter of type int which represents the element index being drawn.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.AlwaysAddDefaultValue">
            <summary>
            If true, object/type pickers will never be shown when the list add button is clicked, and default(T) will always be added instantly instead, where T is the element type of the list.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.AddCopiesLastElement">
            <summary>
            Whether adding a new element should copy the last element. False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ElementColor">
            <summary> A resolved string with "int index" and "Color defaultColor" parameters that lets you control the color of individual elements. Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowPaging">
            <summary>
            Override the default setting specified in the Advanced Odin Preferences window and explicitly tell whether paging should be enabled or not.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.DraggableItems">
            <summary>
            Override the default setting specified in the Advanced Odin Preferences window and explicitly tell whether items should be draggable or not.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.NumberOfItemsPerPage">
            <summary>
            Override the default setting specified in the Advanced Odin Preferences window and explicitly tells how many items each page should contain.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.IsReadOnly">
            <summary>
            Mark a list as read-only. This removes all editing capabilities from the list such as Add, Drag and delete,
            but without disabling GUI for each element drawn as otherwise would be the case if the <see cref="T:Sirenix.OdinInspector.ReadOnlyAttribute"/> was used.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowItemCount">
            <summary>
            Override the default setting specified in the Advanced Odin Preferences window and explicitly tell whether or not item count should be shown.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowFoldout">
            <summary>
            Whether to show a foldout for the collection or not. If this is set to false, the collection will *always* be expanded.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.Expanded">
            <summary>
            <para>Whether to show a foldout for the collection or not. If this is set to false, the collection will *always* be expanded.</para>
            <para>
            This documentation used to wrongly state that this value would override the default setting specified in the Advanced Odin Preferences 
            window and explicitly tell whether or not the list should be expanded or collapsed by default. This value *would* do that, but it would
            also simultaneously act as ShowFoldout, leading to weird and unintuitive behaviour.
            </para>
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.DefaultExpandedState">
            <summary>
            Override the default setting specified in the Odin Preferences window and explicitly tell whether or not the list should be expanded or collapsed by default.
            Note that this will override the persisted expand state, as this is set *every time* the collection drawer is initialized.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowIndexLabels">
            <summary>
            If true, a label is drawn for each element which shows the index of the element.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.OnTitleBarGUI">
            <summary>
            Use this to inject custom GUI into the title-bar of the list.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.PagingHasValue">
            <summary>
            Whether the Paging property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowItemCountHasValue">
            <summary>
            Whether the ShowItemCount property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.NumberOfItemsPerPageHasValue">
            <summary>
            Whether the NumberOfItemsPerPage property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.DraggableHasValue">
            <summary>
            Whether the Draggable property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.IsReadOnlyHasValue">
            <summary>
            Whether the IsReadOnly property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.ShowIndexLabelsHasValue">
            <summary>
            Whether the ShowIndexLabels property is set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ListDrawerSettingsAttribute.DefaultExpandedStateHasValue">
            <summary>
            Whether the DefaultExpandedState property is set.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.MaxValueAttribute">
            <summary>
            <para>MaxValue is used on primitive fields. It caps value of the field to a maximum value.</para>
            <para>Use this to define a maximum value for the field.</para>
            </summary>
            <remarks>
            <note type="note">Note that this attribute only works in the editor! Values changed from scripting will not be capped at a maximum.</note>
            </remarks>
            <example>
            <para>The following example shows a component where a speed value must be less than or equal to 200.</para>
            <code>
            public class Car : MonoBehaviour
            {
            	// The speed of the car must be less than or equal to 200.
            	[MaxValue(200)]
            	public float Speed;
            }
            </code>
            </example>
            <example>
            <para>The following example shows how MaxValue can be combined with <see cref="T:Sirenix.OdinInspector.MinValueAttribute"/>.</para>
            <code>
            public class Health : MonoBehaviour
            {
            	// The speed value must be between 0 and 200.
            	[MinValue(0), MaxValue(200)]
            	public float Speed;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.MinValueAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.MaxValueAttribute.MaxValue">
            <summary>
            The maximum value for the property.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MaxValueAttribute.Expression">
            <summary>
            The string with which to resolve a maximum value. This could be a field, property or method name, or an expression.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.MaxValueAttribute.#ctor(System.Double)">
            <summary>
            Sets a maximum value for the property in the inspector.
            </summary>
            <param name="maxValue">The max value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MaxValueAttribute.#ctor(System.String)">
            <summary>
            Sets a maximum value for the property in the inspector.
            </summary>
            <param name="expression">The string with which to resolve a maximum value. This could be a field, property or method name, or an expression.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.MinMaxSliderAttribute">
            <summary>
            <para>Draw a special slider the user can use to specify a range between a min and a max value.</para>
            <para>Uses a Vector2 where x is min and y is max.</para>
            </summary>
            <example>
            <para>The following example shows how MinMaxSlider is used.</para>
            <code>
            public class Player : MonoBehaviour
            {
            	[MinMaxSlider(4, 5)]
            	public Vector2 SpawnRadius;
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.MinValue">
            <summary>
            The hardcoded min value for the slider.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.MaxValue">
            <summary>
            The hardcoded max value for the slider.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.MinMaxSliderAttribute.MinMember">
            <summary>
            The name of a field, property or method to get the min value from. Obsolete; use MinValueGetter instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.MinValueGetter">
            <summary>
            A resolved string that should evaluate to a float value, which is used as the min bounds.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.MinMaxSliderAttribute.MaxMember">
            <summary>
            The name of a field, property or method to get the max value from. Obsolete; use MaxValueGetter instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.MaxValueGetter">
            <summary>
            A resolved string that should evaluate to a float value, which is used as the max bounds.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.MinMaxSliderAttribute.MinMaxMember">
            <summary>
            The name of a Vector2 field, property or method to get the min max values from. Obsolete; use MinMaxValueGetter instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.MinMaxValueGetter">
            <summary>
            A resolved string that should evaluate to a Vector2 value, which is used as the min/max bounds. If this is non-null, it overrides the behaviour of the MinValue, MinValueGetter, MaxValue and MaxValueGetter members.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinMaxSliderAttribute.ShowFields">
            <summary>
            Draw float fields for min and max value.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.MinMaxSliderAttribute.#ctor(System.Single,System.Single,System.Boolean)">
            <summary>
            Draws a min-max slider in the inspector. X will be set to min, and Y will be set to max.
            </summary>
            <param name="minValue">The min value.</param>
            <param name="maxValue">The max value.</param>
            <param name="showFields">If <c>true</c> number fields will drawn next to the MinMaxSlider.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MinMaxSliderAttribute.#ctor(System.String,System.Single,System.Boolean)">
            <summary>
            Draws a min-max slider in the inspector. X will be set to min, and Y will be set to max.
            </summary>
            <param name="minValueGetter">A resolved string that should evaluate to a float value, which is used as the min bounds.</param>
            <param name="maxValue">The max value.</param>
            <param name="showFields">If <c>true</c> number fields will drawn next to the MinMaxSlider.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MinMaxSliderAttribute.#ctor(System.Single,System.String,System.Boolean)">
            <summary>
            Draws a min-max slider in the inspector. X will be set to min, and Y will be set to max.
            </summary>
            <param name="minValue">The min value.</param>
            <param name="maxValueGetter">A resolved string that should evaluate to a float value, which is used as the max bounds.</param>
            <param name="showFields">If <c>true</c> number fields will drawn next to the MinMaxSlider.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MinMaxSliderAttribute.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Draws a min-max slider in the inspector. X will be set to min, and Y will be set to max.
            </summary>
            <param name="minValueGetter">A resolved string that should evaluate to a float value, which is used as the min bounds.</param>
            <param name="maxValueGetter">A resolved string that should evaluate to a float value, which is used as the max bounds.</param>
            <param name="showFields">If <c>true</c> number fields will drawn next to the MinMaxSlider.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MinMaxSliderAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Draws a min-max slider in the inspector. X will be set to min, and Y will be set to max.
            </summary>
            <param name="minMaxValueGetter">A resolved string that should evaluate to a Vector2 value, which is used as the min/max bounds. If this is non-null, it overrides the behaviour of the MinValue, MinValueGetter, MaxValue and MaxValueGetter members.</param>
            <param name="showFields">If <c>true</c> number fields will drawn next to the MinMaxSlider.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.MinValueAttribute">
            <summary>
            <para>MinValue is used on primitive fields. It caps value of the field to a minimum value.</para>
            <para>Use this to define a minimum value for the field.</para>
            </summary>
            <remarks>
            <note type="note">Note that this attribute only works in the editor! Values changed from scripting will not be capped at a minimum.</note>
            </remarks>
            <example>
            <para>The following example shows a player component that must have at least 1 life.</para>
            <code>
            public class Player : MonoBehaviour
            {
            	// The life value must be set to at least 1.
            	[MinValue(1)]
            	public int Life;
            }
            </code>
            </example>
            <example>
            <para>The following example shows how MinValue can be combined with <see cref="T:Sirenix.OdinInspector.MaxValueAttribute"/></para>
            <code>
            public class Health : MonoBehaviour
            {
            	// The health value must be between 0 and 100.
            	[MinValue(0), MaxValue(100)]
            	public float Health;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.MaxValueAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.MinValueAttribute.MinValue">
            <summary>
            The minimum value for the property.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.MinValueAttribute.Expression">
            <summary>
            The string with which to resolve a minimum value. This could be a field, property or method name, or an expression.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.MinValueAttribute.#ctor(System.Double)">
            <summary>
            Sets a minimum value for the property in the inspector.
            </summary>
            <param name="minValue">The minimum value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.MinValueAttribute.#ctor(System.String)">
            <summary>
            Sets a minimum value for the property in the inspector.
            </summary>
            <param name="expression">The string with which to resolve a minimum value. This could be a field, property or method name, or an expression.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.MultiLinePropertyAttribute">
            <summary>
            <para>MultiLineProperty is used on any string property.</para>
            <para>Use this to allow users to edit strings in a multi line textbox.</para>
            </summary>
            <remarks>
            <para>MultiLineProperty is similar to Unity's <see cref="T:UnityEngine.MultilineAttribute"/> but can be applied to both fields and properties.</para>
            </remarks>
            <example>
            <para>The following example shows how MultiLineProperty is applied to properties.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[MultiLineProperty]
            	public string MyString;
            
            	[ShowInInspector, MultiLineProperty(10)]
            	public string PropertyString;
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.MultiLinePropertyAttribute.Lines">
            <summary>
            The number of lines for the text box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.MultiLinePropertyAttribute.#ctor(System.Int32)">
            <summary>
            Makes a multiline textbox for editing strings.
            </summary>
            <param name="lines">The number of lines for the text box.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.OnCollectionChangedAttribute">
            <summary>
            <para>
               OnCollectionChanged can be put on collections, and provides an event callback when the collection is about to be changed through the inspector, 
               and when the collection has been changed through the inspector. Additionally, it provides a CollectionChangeInfo struct containing information
               about the exact changes made to the collection. This attribute works for all collections with a collection resolver, amongst them arrays, lists,
               dictionaries, hashsets, stacks and linked lists.
            </para>
            </summary>
            <remarks>
            <note type="note">Note that this attribute only works in the editor! Collections changed by script will not trigger change events!</note>
            </remarks>
            <example>
            <para>The following example shows how OnCollectionChanged can be used to get callbacks when a collection is being changed.</para>
            <code>
            [OnCollectionChanged("Before", "After")]
            public List&lt;string&gt; list;
            
            public void Before(CollectionChangeInfo info)
            {
                if (info.ChangeType == CollectionChangeType.Add || info.ChangeType == CollectionChangeType.Insert)
                {
                    Debug.Log("Adding to the list!");
                }
                else if (info.ChangeType == CollectionChangeType.RemoveIndex || info.ChangeType == CollectionChangeType.RemoveValue)
                {
                    Debug.Log("Removing from the list!");
                }
            }
            
            public void After(CollectionChangeInfo info)
            {
                if (info.ChangeType == CollectionChangeType.Add || info.ChangeType == CollectionChangeType.Insert)
                {
                    Debug.Log("Finished adding to the list!");
                }
                else if (info.ChangeType == CollectionChangeType.RemoveIndex || info.ChangeType == CollectionChangeType.RemoveValue)
                {
                    Debug.Log("Finished removing from the list!");
                }
            }
            </code>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.OnInspectorDisposeAttribute">
            <summary>
            <para>The OnInspectorDispose attribute takes in an action string as an argument (typically the name of a method to be invoked, or an expression to be executed), and executes that action when the property's drawers are disposed in the inspector.</para>
            <para>Disposing will happen at least once, when the inspector changes selection or the property tree is collected by the garbage collector, but may also happen several times before that, most often when the type of a polymorphic property changes and it refreshes its drawer setup and recreates all its children, disposing of the old setup and children.</para>
            </summary>
            <example>
            <para>The following example demonstrates how OnInspectorDispose works.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
                [OnInspectorDispose(@"@UnityEngine.Debug.Log(""Dispose event invoked!"")")]
                [ShowInInspector, InfoBox("When you change the type of this field, or set it to null, the former property setup is disposed. The property setup will also be disposed when you deselect this example."), DisplayAsString]
                public BaseClass PolymorphicField;
                
                public abstract class BaseClass { public override string ToString() { return this.GetType().Name; } }
                public class A : BaseClass { }
                public class B : BaseClass { }
                public class C : BaseClass { }
            }
            </code>
            </example>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorDisposeAttribute.#ctor">
            <summary>
            This constructor should be used when the attribute is placed directly on a method.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorDisposeAttribute.#ctor(System.String)">
            <summary>
            This constructor should be used when the attribute is placed on a non-method member.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.OnInspectorGUIAttribute">
             <summary>
             <para>OnInspectorGUI is used on any property, and will call the specified function whenever the inspector code is running.</para>
             <para>Use this to create custom inspector GUI for an object.</para>
             </summary>
             <example>
             <para></para>
             <code>
             public MyComponent : MonoBehaviour
             {
            		[OnInspectorGUI]
            		private void MyInspectorGUI()
            		{
            			GUILayout.Label("Label drawn from callback");
            		}
             }
             </code>
             </example>
             <example>
            	<para>The following example shows how a callback can be set before another property.</para>
             <code>
             public MyComponent : MonoBehaviour
             {
            		[OnInspectorGUI("MyInspectorGUI", false)]
            		public int MyField;
            
            		private void MyInspectorGUI()
            		{
            			GUILayout.Label("Label before My Field property");
            		}
             }
             </code>
             </example>
             <example>
            	<para>The following example shows how callbacks can be added both before and after a property.</para>
             <code>
             public MyComponent : MonoBehaviour
             {
            		[OnInspectorGUI("GUIBefore", "GUIAfter")]
            		public int MyField;
            
            		private void GUIBefore()
            		{
            			GUILayout.Label("Label before My Field property");
            		}
            
            		private void GUIAfter()
            		{
            			GUILayout.Label("Label after My Field property");
            		}
             }
             </code>
             </example>
        </member>
        <member name="F:Sirenix.OdinInspector.OnInspectorGUIAttribute.Prepend">
            <summary>
            The resolved action string that defines the action to be invoked before the property is drawn, if any.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnInspectorGUIAttribute.Append">
            <summary>
            The resolved action string that defines the action to be invoked after the property is drawn, if any.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnInspectorGUIAttribute.PrependMethodName">
            <summary>
            The name of the method to be called before the property is drawn, if any. Obsolete; use the Prepend member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnInspectorGUIAttribute.AppendMethodName">
            <summary>
            The name of the method to be called after the property is drawn, if any. Obsolete; use the Append member instead.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorGUIAttribute.#ctor">
            <summary>
            Calls a function decorated with this attribute, when the inspector is being drawn.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorGUIAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Adds callbacks to the specified action when the property is being drawn.
            </summary>
            <param name="action">The resolved action string that defines the action to be invoked.</param>
            <param name="append">If <c>true</c> the method will be called after the property has been drawn. Otherwise the method will be called before.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorGUIAttribute.#ctor(System.String,System.String)">
            <summary>
            Adds callbacks to the specified actions when the property is being drawn.
            </summary>
            <param name="prepend">The resolved action string that defines the action to be invoked before the property is drawn, if any.</param>
            <param name="append">The resolved action string that defines the action to be invoked after the property is drawn, if any.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.OnInspectorInitAttribute">
            <summary>
            <para>The OnInspectorInit attribute takes in an action string as an argument (typically the name of a method to be invoked, or an expression to be executed), and executes that action when the property's drawers are initialized in the inspector.</para>
            <para>Initialization will happen at least once during the first drawn frame of any given property, but may also happen several times later, most often when the type of a polymorphic property changes and it refreshes its drawer setup and recreates all its children.</para>
            </summary>
            <example>
            <para>The following example demonstrates how OnInspectorInit works.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
                // Display current time for reference.
                [ShowInInspector, DisplayAsString, PropertyOrder(-1)]
                public string CurrentTime { get { GUIHelper.RequestRepaint(); return DateTime.Now.ToString(); } }
                
                // OnInspectorInit executes the first time this string is about to be drawn in the inspector.
                // It will execute again when the example is reselected.
                [OnInspectorInit("@TimeWhenExampleWasOpened = DateTime.Now.ToString()")]
                public string TimeWhenExampleWasOpened;
                
                // OnInspectorInit will not execute before the property is actually "resolved" in the inspector.
                // Remember, Odin's property system is lazily evaluated, and so a property does not actually exist
                // and is not initialized before something is actually asking for it.
                // 
                // Therefore, this OnInspectorInit attribute won't execute until the foldout is expanded.
                [FoldoutGroup("Delayed Initialization", Expanded = false, HideWhenChildrenAreInvisible = false)]
                [OnInspectorInit("@TimeFoldoutWasOpened = DateTime.Now.ToString()")]
                public string TimeFoldoutWasOpened;
            }
            </code>
            </example>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorInitAttribute.#ctor">
            <summary>
            This constructor should be used when the attribute is placed directly on a method.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.OnInspectorInitAttribute.#ctor(System.String)">
            <summary>
            This constructor should be used when the attribute is placed on a non-method member.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.OnStateUpdateAttribute">
             <summary>
             <para>
                OnStateUpdate provides an event callback when the property's state should be updated, when the StateUpdaters run on the property instance.
                This generally happens at least once per frame, and the callback will be invoked even when the property is not visible. This can be used to
                approximate custom StateUpdaters like [ShowIf] without needing to make entire attributes and StateUpdaters for one-off cases.
             </para>
             </summary>
             <example>
             <para>The following example shows how OnStateUpdate can be used to control the visible state of a property.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[OnStateUpdate("@$property.State.Visible = ToggleMyInt")]
            		public int MyInt;
            
            		public bool ToggleMyInt;
             }
             </code>
             </example>
             <example>
             <para>The following example shows how OnStateUpdate can be used to control the expanded state of a list.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[OnStateUpdate("@$property.State.Expanded = ExpandList")]
            		public List&lt;string&gt; list;
            		
            		public bool ExpandList;
             }
             </code>
             <para>The following example shows how OnStateUpdate can be used to control the state of another property.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		public List&gt;string&lt; list;
            		
            		[OnStateUpdate("@#(list).State.Expanded = $value")]
            		public bool ExpandList;
             }
             </code>
             </example>
        </member>
        <member name="T:Sirenix.OdinInspector.OnValueChangedAttribute">
             <summary>
             <para>
                OnValueChanged works on properties and fields, and calls the specified function 
                whenever the value has been changed via the inspector.
             </para>
             </summary>
             <remarks>
             <note type="note">Note that this attribute only works in the editor! Properties changed by script will not call the function.</note>
             </remarks>
             <example>
             <para>The following example shows how OnValueChanged is used to provide a callback for a property.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[OnValueChanged("MyCallback")]
            		public int MyInt;
            
            		private void MyCallback()
            		{
            			// ..
            		}
             }
             </code>
             </example>
             <example>
             <para>The following example show how OnValueChanged can be used to get a component from a prefab property.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[OnValueChanged("OnPrefabChange")]
            		public GameObject MyPrefab;
            
            		// RigidBody component of MyPrefab.
            		[SerializeField, HideInInspector]
            		private RigidBody myPrefabRigidbody;
            
            		private void OnPrefabChange()
            		{
            			if(MyPrefab != null)
            			{
            				myPrefabRigidbody = MyPrefab.GetComponent&lt;Rigidbody&gt;();
            			}
            			else
            			{
            				myPrefabRigidbody = null;
            			}
            		}
             }
             </code>
             </example>
        </member>
        <member name="P:Sirenix.OdinInspector.OnValueChangedAttribute.MethodName">
            <summary>
            Name of callback member function. Obsolete; use the Action member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnValueChangedAttribute.Action">
            <summary>
            A resolved string that defines the action to perform when the value is changed, such as an expression or method invocation.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnValueChangedAttribute.IncludeChildren">
            <summary>
            Whether to perform the action when a child value of the property is changed.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnValueChangedAttribute.InvokeOnUndoRedo">
            <summary>
            Whether to perform the action when an undo or redo event occurs via UnityEditor.Undo.undoRedoPerformed. True by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.OnValueChangedAttribute.InvokeOnInitialize">
            <summary>
            Whether to perform the action when the property is initialized. This will generally happen when the property is first viewed/queried (IE when the inspector is first opened, or when its containing foldout is first expanded, etc), and whenever its type or a parent type changes, or it is otherwise forced to rebuild.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.OnValueChangedAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Adds a callback for when the property's value is changed.
            </summary>
            <param name="action">A resolved string that defines the action to perform when the value is changed, such as an expression or method invocation.</param>
            <param name="includeChildren">Whether to perform the action when a child value of the property is changed.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.OptionalAttribute">
            <summary>
            Overrides the 'Reference Required by Default' rule to allow for null values.
            Has no effect if the rule is disabled.
            
            This attribute does not do anything unless you have Odin Validator and the 'Reference Required by Default' rule is enabled.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.NonDefaultConstructorPreference">
            <summary>
            Specifies how non-default constructors are handled.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.NonDefaultConstructorPreference.Exclude">
            <summary>
            Excludes types with non default constructors from the Selector.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.NonDefaultConstructorPreference.ConstructIdeal">
            <summary>
            Attempts to find the most straightforward constructor to call, prioritizing default values.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.NonDefaultConstructorPreference.PreferUninitialized">
            <summary>
            Uses <see cref="M:System.Runtime.Serialization.FormatterServices.GetUninitializedObject(System.Type)"/> if no default constructor is found.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.NonDefaultConstructorPreference.LogWarning">
            <summary>
            Logs a warning instead of constructing the object, indicating that an attempt was made to construct an object without a default constructor.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PolymorphicDrawerSettingsAttribute.ShowBaseType">
            <summary>
            Determines whether the base type should be displayed in the drawer.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PolymorphicDrawerSettingsAttribute.ReadOnlyIfNotNullReference">
            <summary>
            Indicates if the drawer should be read-only once a value is assigned.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PolymorphicDrawerSettingsAttribute.NonDefaultConstructorPreference">
            <summary>
            Specifies how non-default constructors are handled.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PolymorphicDrawerSettingsAttribute.CreateInstanceFunction">
            <summary>
            Specifies a custom function for creating an instance of the selected <see cref="T:System.Type"/>.
            </summary>
            <remarks>Does not get called for <see cref="T:UnityEngine.Object">UnityEngine.Object</see> types.</remarks>
            <example>
            <para>
            The resolver expects any method that takes a single parameter of <see cref="T:System.Type"/>, where the parameter is named 'type', and which returns an <see cref="T:System.Object"/>.
            </para>
            
            <para>Implementation example: <c>public object Method(Type type)</c>.</para>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.PreviewFieldAttribute">
             <summary>
             <para>
             Draws a square ObjectField which renders a preview for UnityEngine.Object types.
             This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
             If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
             </para>
             <para>
             These object fields can also be selectively enabled and customized globally from the Odin preferences window.
             </para>
             </summary>
             <example>
             <para>The following example shows how PreviewField is applied to a few property fields.</para>
             <code>
             public MyComponent : MonoBehaviour
             {
            		[PreviewField]
            		public UnityEngine.Object SomeObject;
            
            		[PreviewField]
            		public Texture SomeTexture;
            
            		[HorizontalGroup, HideLabel, PreviewField(30)]
            		public Material A, B, C, D, F;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.TitleAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.PreviewFieldAttribute.Height">
            <summary>
            The height of the object field
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PreviewFieldAttribute.FilterMode">
            <summary>
            The FilterMode to be used for the preview.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PreviewFieldAttribute.Alignment">
            <summary>
            Left aligned.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PreviewFieldAttribute.AlignmentHasValue">
            <summary>
            Whether an alignment value is specified.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PreviewFieldAttribute.PreviewGetter">
            <summary>
            A resolved value that should resolve to the desired preview texture.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.Single)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="height">The height of the preview field.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.String,UnityEngine.FilterMode)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="previewGetter">A resolved value that should resolve to the desired preview texture.</param>
            <param name="filterMode">The filter mode to be used for the preview texture.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.String,System.Single,UnityEngine.FilterMode)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="previewGetter">A resolved value that should resolve to the desired preview texture.</param>
            <param name="height">The height of the preview field.</param>
            <param name="filterMode">The filter mode to be used for the preview texture.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.Single,Sirenix.OdinInspector.ObjectFieldAlignment)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="height">The height of the preview field.</param>
            <param name="alignment">The alignment of the preview field.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.String,Sirenix.OdinInspector.ObjectFieldAlignment,UnityEngine.FilterMode)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="previewGetter">A resolved value that should resolve to the desired preview texture.</param>
            <param name="alignment">The alignment of the preview field.</param>
            <param name="filterMode">The filter mode to be used for the preview texture.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(System.String,System.Single,Sirenix.OdinInspector.ObjectFieldAlignment,UnityEngine.FilterMode)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="previewGetter">A resolved value that should resolve to the desired preview texture.</param>
            <param name="height">The height of the preview field.</param>
            <param name="alignment">The alignment of the preview field.</param>
            <param name="filterMode">The filter mode to be used for the preview texture.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PreviewFieldAttribute.#ctor(Sirenix.OdinInspector.ObjectFieldAlignment)">
            <summary>
            Draws a square object field which renders a preview for UnityEngine.Object type objects.
            </summary>
            <param name="alignment">The alignment of the preview field.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ProgressBarAttribute">
             <summary>
             <para>Draws a horizontal progress bar based on the value of the property.</para>
             <para>Use it for displaying a meter to indicate how full an inventory is, or to make a visual indication of a health bar.</para>
             </summary>
             <example>
             <para>The following example shows how ProgressBar can be used.</para>
             <code>
             public class ProgressBarExample : MonoBehaviour
             {
            		// Default progress bar.
            		[ProgressBar(0, 100)]
            		public int ProgressBar;
            
            		// Health bar.
            		[ProgressBar(0, 100, ColorMember = "GetHealthBarColor")]
            		public float HealthBar = 50;
            
            		private Color GetHealthBarColor(float value)
            		{
            			// Blends between red, and yellow color for when the health is below 30,
            			// and blends between yellow and green color for when the health is above 30.
            			return Color.Lerp(Color.Lerp(
            				Color.red, Color.yellow, MathUtilities.LinearStep(0f, 30f, value)),
            				Color.green, MathUtilities.LinearStep(0f, 100f, value));
            		}
            
            		// Stacked health bar.
            		// The ProgressBar attribute is placed on property, without a set method, so it can't be edited directly.
            		// So instead we have this Range attribute on a float to change the value.
            		[Range(0, 300)]
            		public float StackedHealth;
            
            		[ProgressBar(0, 100, ColorMember = "GetStackedHealthColor", BackgroundColorMember = "GetStackHealthBackgroundColor")]
            		private float StackedHealthProgressBar
            		{
            			// Loops the stacked health value between 0, and 100.
            			get { return this.StackedHealth - 100 * (int)((this.StackedHealth - 1) / 100); }
            		}
            
            		private Color GetStackedHealthColor()
            		{
            			return
            				this.StackedHealth > 200 ? Color.cyan :
            				this.StackedHealth > 100 ? Color.green :
            				Color.red;
            		}
            
            		private Color GetStackHealthBackgroundColor()
            		{
            			return
            				this.StackedHealth > 200 ? Color.green :
            				this.StackedHealth > 100 ? Color.red :
            				new Color(0.16f, 0.16f, 0.16f, 1f);
            		}
            
            		// Custom color and height.
            		[ProgressBar(-100, 100, r: 1, g: 1, b: 1, Height = 30)]
            		public short BigProgressBar = 50;
            		
                 // You can also reference members by name to dynamically assign the min and max progress bar values.
                 [ProgressBar("DynamicMin", "DynamicMax")]
                 public float DynamicProgressBar;
                 
                 public float DynamicMin, DynamicMax;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.HideLabelAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.PropertyRangeAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.MinMaxSliderAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.Min">
            <summary>
            The minimum value.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.Max">
            <summary>
            The maximum value.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.MinMember">
            <summary>
            The name of a field, property or method to get the min values from. Obsolete; use the MinGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.MinGetter">
            <summary>
            A resolved string that should evaluate to a float value, and will be used as the min bounds.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.MaxMember">
            <summary>
            The name of a field, property or method to get the max values from. Obsolete; use the MaxGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.MaxGetter">
            <summary>
            A resolved string that should evaluate to a float value, and will be used as the max bounds.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.R">
            <summary>
            The red channel of the color of the progress bar.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.G">
            <summary>
            The green channel of the color of the progress bar.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.B">
            <summary>
            The blue channel of the color of the progress bar.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.Height">
            <summary>
            The height of the progress bar in pixels. Defaults to 12 pixels.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.ColorMember">
            <summary>
            Optional reference to a Color field, property or method, to dynamically change the color of the progress bar. Obsolete; use the ColorGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.ColorGetter">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.BackgroundColorMember">
            <summary>
            Optional reference to a Color field, property or method, to dynamically change the background color of the progress bar.
            Default background color is (0.16, 0.16, 0.16, 1).
            Obsolete; use the BackgroundColorGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.BackgroundColorGetter">
            <summary> Optional resolved string that should evaluate to a Color value, to dynamically change the background color of the progress bar. Default background color is (0.16, 0.16, 0.16, 1). It supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.Segmented">
            <summary>
            If <c>true</c> then the progress bar will be drawn in tiles.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.CustomValueStringMember">
            <summary>
            References a member by name to get a custom value label string from. Obsolete; use the CustomValueStringGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ProgressBarAttribute.CustomValueStringGetter">
            <summary>
            A resolved string to get a custom value label string from.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ProgressBarAttribute.#ctor(System.Double,System.Double,System.Single,System.Single,System.Single)">
            <summary>
            Draws a progress bar for the value.
            </summary>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
            <param name="r">The red channel of the color of the progress bar.</param>
            <param name="g">The green channel of the color of the progress bar.</param>
            <param name="b">The blue channel of the color of the progress bar.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ProgressBarAttribute.#ctor(System.String,System.Double,System.Single,System.Single,System.Single)">
            <summary>
            Draws a progress bar for the value.
            </summary>
            <param name="minGetter">A resolved string that should evaluate to a float value, and will be used as the min bounds.</param>
            <param name="max">The maximum value.</param>
            <param name="r">The red channel of the color of the progress bar.</param>
            <param name="g">The green channel of the color of the progress bar.</param>
            <param name="b">The blue channel of the color of the progress bar.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ProgressBarAttribute.#ctor(System.Double,System.String,System.Single,System.Single,System.Single)">
            <summary>
            Draws a progress bar for the value.
            </summary>
            <param name="min">The minimum value.</param>
            <param name="maxGetter">A resolved string that should evaluate to a float value, and will be used as the max bounds.</param>
            <param name="r">The red channel of the color of the progress bar.</param>
            <param name="g">The green channel of the color of the progress bar.</param>
            <param name="b">The blue channel of the color of the progress bar.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ProgressBarAttribute.#ctor(System.String,System.String,System.Single,System.Single,System.Single)">
            <summary>
            Draws a progress bar for the value.
            </summary>
            <param name="minGetter">A resolved string that should evaluate to a float value, and will be used as the min bounds.</param>
            <param name="maxGetter">A resolved string that should evaluate to a float value, and will be used as the max bounds.</param>
            <param name="r">The red channel of the color of the progress bar.</param>
            <param name="g">The green channel of the color of the progress bar.</param>
            <param name="b">The blue channel of the color of the progress bar.</param>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.DrawValueLabel">
            <summary>
            If <c>true</c> then there will be drawn a value label on top of the progress bar.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.DrawValueLabelHasValue">
            <summary>
            Gets a value indicating if the user has set a custom DrawValueLabel value.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.ValueLabelAlignment">
            <summary>
            The alignment of the value label on top of the progress bar. Defaults to center.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ProgressBarAttribute.ValueLabelAlignmentHasValue">
            <summary>
            Gets a value indicating if the user has set a custom ValueLabelAlignment value.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.PropertyGroupAttribute">
             <summary>
             <para>Attribute to derive from if you wish to create a new property group type, such as box groups or tab groups.</para>
             <note type="note">Note that this attribute has special behaviour for "combining" several attributes into one, as one group,
             may be declared across attributes in several members, completely out of order. See <see cref="M:Sirenix.OdinInspector.PropertyGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)"/>.</note>
             </summary>
             <remarks>
             <para>All group attributes for a group with the same name (and of the same attribute type) are combined into a single representative group attribute using the <see cref="M:Sirenix.OdinInspector.PropertyGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)"/> method, which is called by the <see cref="M:Sirenix.OdinInspector.PropertyGroupAttribute.Combine(Sirenix.OdinInspector.PropertyGroupAttribute)"/> method.</para>
             <para>This behaviour is a little unusual, but it is important that you understand it when creating groups with many custom parameters that may have to be combined.</para>
             </remarks>
             <example>
             <para>This example shows how <see cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/> could be implemented.</para>
             <code>
             [AttributeUsage(AttributeTargets.All, AllowMultiple = false, Inherited = true)]
             public class BoxGroupAttribute : PropertyGroupAttribute
             {
                 public string Label { get; private set; }
                 public bool ShowLabel { get; private set; }
                 public bool CenterLabel { get; private set; }
            
                 public BoxGroupAttribute(string group, bool showLabel = true, bool centerLabel = false, float order = 0)
                     : base(group, order)
                 {
                     this.Label = group;
                     this.ShowLabel = showLabel;
                     this.CenterLabel = centerLabel;
                 }
            
                 protected override void CombineValuesWith(PropertyGroupAttribute other)
                 {
                     // The given attribute parameter is *guaranteed* to be of type BoxGroupAttribute.
                     var attr = other as BoxGroupAttribute;
            
                     // If this attribute has no label, we the other group's label, thus preserving the label across combines.
                     if (this.Label == null)
                     {
                         this.Label = attr.Label;
                     }
            
                     // Combine ShowLabel and CenterLabel parameters.
                     this.ShowLabel |= attr.ShowLabel;
                     this.CenterLabel |= attr.CenterLabel;
                 }
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.FoldoutGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.GroupID">
            <summary>
            The ID used to grouping properties together.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.GroupName">
            <summary>
            The name of the group. This is the last part of the group ID if there is a path, otherwise it is just the group ID.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.Order">
            <summary>
            The order of the group.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.HideWhenChildrenAreInvisible">
            <summary>
            Whether to hide the group by default when all its children are not visible. True by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.VisibleIf">
            <summary>
            If not null, this resolved string controls the group's visibility. Note that if <see cref="F:Sirenix.OdinInspector.PropertyGroupAttribute.HideWhenChildrenAreInvisible"/> is true, there must be *both* a visible child *and* this condition must be true, before the group is shown.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyGroupAttribute.AnimateVisibility">
            <summary>
            Whether to animate the visibility changes of this group or make the visual transition instantly. True by default.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyGroupAttribute.#ctor(System.String,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.PropertyGroupAttribute"/> class.
            </summary>
            <param name="groupId">The group identifier.</param>
            <param name="order">The group order.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyGroupAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.PropertyGroupAttribute"/> class.
            </summary>
            <param name="groupId">The group identifier.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyGroupAttribute.Combine(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            <para>Combines this attribute with another attribute of the same type. 
            This method invokes the virtual <see cref="M:Sirenix.OdinInspector.PropertyGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)"/> method to invoke custom combine logic.</para>
            <para>All group attributes are combined to one attribute used by a single OdinGroupDrawer.</para> 
            <para>Example: <code>protected override void CombineValuesWith(PropertyGroupAttribute other) { this.Title = this.Title ?? (other as MyGroupAttribute).Title; }</code></para> 
            </summary>
            <param name="other">The attribute to combine with.</param>
            <returns>The instance that the method was invoked on.</returns>
            <exception cref="T:System.ArgumentNullException">The argument 'other' was null.</exception>
            <exception cref="T:System.ArgumentException">
            Attributes to combine are not of the same type.
            or
            PropertyGroupAttributes to combine must have the same group id.
            </exception>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
             <summary>
             <para>Override this method to add custom combine logic to your group attribute. This method determines how your group's parameters combine when spread across multiple attribute declarations in the same class.</para>
             <para>Remember, in .NET, member order is not guaranteed, so you never know which order your attributes will be combined in.</para>
             </summary>
             <param name="other">The attribute to combine with. This parameter is guaranteed to be of the correct attribute type.</param>
             <example>
             <para>This example shows how <see cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/> attributes are combined.</para>
             <code>
             protected override void CombineValuesWith(PropertyGroupAttribute other)
             {
                 // The given attribute parameter is *guaranteed* to be of type BoxGroupAttribute.
                 var attr = other as BoxGroupAttribute;
            
                 // If this attribute has no label, we the other group's label, thus preserving the label across combines.
                 if (this.Label == null)
                 {
                     this.Label = attr.Label;
                 }
            
                 // Combine ShowLabel and CenterLabel parameters.
                 this.ShowLabel |= attr.ShowLabel;
                 this.CenterLabel |= attr.CenterLabel;
             }
             </code>
             </example>
        </member>
        <member name="T:Sirenix.OdinInspector.PropertyOrderAttribute">
            <summary>
            <para>PropertyOrder is used on any property, and allows for ordering of properties.</para>
            <para>Use this to define in which order your properties are shown.</para>
            </summary>
            <remarks>
            <para>Lower order values will be drawn before higher values.</para>
            <note type="note">There is unfortunately no way of ensuring that properties are in the same order, as they appear in your class. PropertyOrder overcomes this.</note>
            </remarks>
            <example>
            <para>The following example shows how PropertyOrder is used to order properties in the inspector.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[PropertyOrder(1)]
            	public int MySecondProperty;
            
            	[PropertyOrder(-1)]
            	public int MyFirstProperty;
            }
            </code>
            </example>    
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyOrderAttribute.Order">
            <summary>
            The order for the property.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyOrderAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.PropertyOrderAttribute"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyOrderAttribute.#ctor(System.Single)">
            <summary>
            Defines a custom order for the property.
            </summary>
            <param name="order">The order for the property.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.PropertyRangeAttribute">
            <summary>
            <para>PropertyRange attribute creates a slider control to set the value of a property to between the specified range.</para>
            <para>This is equivalent to Unity's Range attribute, but this attribute can be applied to both fields and property.</para>
            </summary>
            <example>The following example demonstrates how PropertyRange is used.</example>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[PropertyRange(0, 100)]
            	public int MyInt;
            	
            	[PropertyRange(-100, 100)]
            	public float MyFloat;
            	
            	[PropertyRange(-100, -50)]
            	public decimal MyDouble;
            	
                // This attribute also supports dynamically referencing members by name to assign the min and max values for the range field.
                [PropertyRange("DynamicMin", "DynamicMax"]
                public float MyDynamicValue;
                
                public float DynamicMin, DynamicMax;
            }
            </code>
            <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertySpaceAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertyTooltipAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertyOrderAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyRangeAttribute.Min">
            <summary>
            The minimum value.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyRangeAttribute.Max">
            <summary>
            The maximum value.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PropertyRangeAttribute.MinMember">
            <summary>
            The name of a field, property or method to get the min value from. Obsolete; use the MinGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyRangeAttribute.MinGetter">
            <summary>
            A resolved string that should evaluate to a float value, and will be used as the min bounds.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.PropertyRangeAttribute.MaxMember">
            <summary>
            The name of a field, property or method to get the max value from. Obsolete; use the MaxGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyRangeAttribute.MaxGetter">
            <summary>
            A resolved string that should evaluate to a float value, and will be used as the max bounds.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyRangeAttribute.#ctor(System.Double,System.Double)">
            <summary>
            Creates a slider control to set the value of the property to between the specified range..
            </summary>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyRangeAttribute.#ctor(System.String,System.Double)">
            <summary>
            Creates a slider control to set the value of the property to between the specified range..
            </summary>
            <param name="minGetter">A resolved string that should evaluate to a float value, and will be used as the min bounds.</param>
            <param name="max">The maximum value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyRangeAttribute.#ctor(System.Double,System.String)">
            <summary>
            Creates a slider control to set the value of the property to between the specified range..
            </summary>
            <param name="min">The minimum value.</param>
            <param name="maxGetter">A resolved string that should evaluate to a float value, and will be used as the max bounds.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyRangeAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a slider control to set the value of the property to between the specified range..
            </summary>
            <param name="minGetter">A resolved string that should evaluate to a float value, and will be used as the min bounds.</param>
            <param name="maxGetter">A resolved string that should evaluate to a float value, and will be used as the max bounds.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.PropertySpaceAttribute">
            <summary>
            <para>The PropertySpace attribute have the same function as Unity's existing Space attribute, but can be applied anywhere as opposed to just fields.</para>
            </summary>
            <example>
            <para>The following example demonstrates the usage of the PropertySpace attribute.</para>
            <code>
            [PropertySpace] // Defaults to a space of 8 pixels just like Unity's Space attribute.
            public int MyField;
            
            [ShowInInspector, PropertySpace(16)]
            public int MyProperty { get; set; }
            
            [ShowInInspector, PropertySpace(16, 16)]
            public int MyProperty { get; set; }
            
            [Button, PropertySpace(32)]
            public void MyMethod()
            {
                ...
            }
            
            [PropertySpace(-8)] // A negative space can also be remove existing space between properties.
            public int MovedUp;
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertyRangeAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertyTooltipAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.PropertyOrderAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertySpaceAttribute.SpaceBefore">
            <summary>
            The space between properties in pixels.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertySpaceAttribute.SpaceAfter">
            <summary>
            The space between properties in pixels.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertySpaceAttribute.#ctor">
            <summary>
            Adds a space of 8 pixels between properties.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertySpaceAttribute.#ctor(System.Single)">
            <summary>
            Adds a space between properties.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertySpaceAttribute.#ctor(System.Single,System.Single)">
            <summary>
            Adds a space between properties.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.PropertyTooltipAttribute">
             <summary>
             <para>PropertyTooltip is used on any property, and creates tooltips for when hovering the property in the inspector.</para>
             <para>Use this to explain the purpose, or how to use a property.</para>
             </summary>
             <remarks>
             <para>This is similar to Unity's <see cref="T:UnityEngine.TooltipAttribute"/> but can be applied to both fields and properties.</para>
             </remarks>
             <example>
             <para>The following example shows how PropertyTooltip is applied to various properties.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[PropertyTooltip("This is an int property.")]
            		public int MyField;
            
            		[ShowInInspector, PropertyTooltip("This is another int property.")]
            		public int MyProperty { get; set; }
            	}
             </code>
             </example>
             <seealso cref="T:UnityEngine.TooltipAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.PropertySpaceAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.PropertyRangeAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.PropertyOrderAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.PropertyTooltipAttribute.Tooltip">
            <summary>
            The message shown in the tooltip.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.PropertyTooltipAttribute.#ctor(System.String)">
            <summary>
            Adds a tooltip to the property in the inspector.
            </summary>
            <param name="tooltip">The message shown in the tooltip.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ReadOnlyAttribute">
             <summary>
             <para>ReadOnly is used on any property, and prevents the property from being changed in the inspector.</para>
             <para>Use this for when you want to see the value of a property in the inspector, but don't want it to be changed.</para>
             </summary>
             <remarks>
             <note type="note">This attribute only affects the inspector! Values can still be changed by script.</note>
             </remarks>
             <example>
             <para>The following example shows how a field can be displayed in the editor, but not be editable.</para>
             <code>
             public class Health : MonoBehaviour
             {
            		public int MaxHealth;
            
            		[ReadOnly]
            		public int CurrentHealth;
             }
             </code>
             </example>
             <example>
             <para>ReadOnly can also be combined with <see cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>.</para>
             <code>
             public class Health : MonoBehaviour
             {
            		public int MaxHealth;
            
            		[ShowInInspector, ReadOnly]
            		private int currentHealth;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.RequiredAttribute">
             <summary>
             <para>Required is used on any object property, and draws a message in the inspector if the property is missing.</para>
             <para>Use this to clearly mark fields as necessary to the object.</para>
             </summary>
             <example>
             <para>The following example shows different uses of the Required attribute.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[Required]
            		public GameObject MyPrefab;
            
            		[Required(InfoMessageType.Warning)]
            		public Texture2D MyTexture;
            
            		[Required("MyMesh is nessessary for this component.")]
            		public Mesh MyMesh;
            
            		[Required("MyTransform might be important.", InfoMessageType.Info)]
            		public Transform MyTransform;
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.InfoBoxAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ValidateInputAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredAttribute.ErrorMessage">
            <summary>
            The message of the info box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredAttribute.MessageType">
            <summary>
            The type of the info box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredAttribute.#ctor">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredAttribute.#ctor(System.String,Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredAttribute.#ctor(System.String)">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredAttribute.#ctor(Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.RequiredInAttribute">
            <summary>
            Makes a member required based on which type of a prefab and instance it is in. 
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.ErrorMessage">
            <summary>
            The message of the info box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.MessageType">
            <summary>
            The type of the info box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.#ctor">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.#ctor(System.String,Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.#ctor(System.String)">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabAssetsAttribute.#ctor(Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.ErrorMessage">
            <summary>
            The message of the info box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.MessageType">
            <summary>
            The type of the info box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.#ctor">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.#ctor(System.String,Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.#ctor(System.String)">
            <summary>
            Adds an error box to the inspector, if the property is missing.
            </summary>
            <param name="errorMessage">The message to display in the error box.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredInPrefabInstancesAttribute.#ctor(Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Adds an info box to the inspector, if the property is missing.
            </summary>
            <param name="messageType">The type of info box to draw.</param>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Sirenix.OdinInspector.RequiredListLengthAttribute" -->
        <member name="P:Sirenix.OdinInspector.RequiredListLengthAttribute.MinLength">
            <summary>
            The minimum length of the collection. If not set, there is no minimum length restriction.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.RequiredListLengthAttribute.MaxLength">
            <summary>
            The maximum length of the collection. If not set, there is no maximum length restriction.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredListLengthAttribute.MinLengthGetter">
            <summary>
            A C# expression for getting the minimum length of the collection, for example "@this.otherList.Count".
            If set, MinLength will be the fallback in case nothing in case MinLengthGetter returns null.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.RequiredListLengthAttribute.MaxLengthGetter">
            <summary>
            A C# expression for getting the maximum length of the collection, for example "@this.otherList.Count".
            If set, MaxLength will be the fallback in case nothing in case MaxLengthGetter returns null.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.Int32)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="fixedLength">The minimum and maximum length of the collection.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="minLength">The minimum length of the collection.</param>
            <param name="maxLength">The maximum length of the collection.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="minLength">The minimum length of the collection.</param>
            <param name="maxLengthGetter">A C# expression for getting the maximum length of the collection, for example "@this.otherList.Count". If set, MaxLength will be the fallback in case nothing in case MaxLengthGetter returns null.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.String)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="fixedLengthGetter">The minimum and maximum length of the collection.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.String,System.String)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="minLengthGetter">A C# expression for getting the minimum length of the collection, for example "@this.otherList.Count". If set, MinLength will be the fallback in case nothing in case MinLengthGetter returns null.</param>
            <param name="maxLengthGetter">A C# expression for getting the maximum length of the collection, for example "@this.otherList.Count". If set, MaxLength will be the fallback in case nothing in case MaxLengthGetter returns null.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.RequiredListLengthAttribute.#ctor(System.String,System.Int32)">
            <summary>
            Limits the collection to be contain the specified number of elements.
            </summary>
            <param name="minLengthGetter">A C# expression for getting the minimum length of the collection, for example "@this.otherList.Count". If set, MinLength will be the fallback in case nothing in case MinLengthGetter returns null.</param>
            <param name="maxLength">The maximum length of the collection.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ResponsiveButtonGroupAttribute">
            <summary>
            Groups buttons into a group that will position and resize the buttons based on the amount of available layout space.
            </summary>
            <example>
            <code>
            [ResponsiveButtonGroup]
            public void Foo() { }
            
            [ResponsiveButtonGroup]
            public void Bar() { }
            
            [ResponsiveButtonGroup]
            public void Baz() { }
            </code>
            </example>
            <example>
            <code>
            [ResponsiveButtonGroup(UniformLayout = true)]
            public void Foo() { }
            
            [ResponsiveButtonGroup]
            public void Bar() { }
            
            [ResponsiveButtonGroup]
            public void Baz() { }
            </code>
            </example>
            <example>
            <code>
            [ResponsiveButtonGroupAttribute(UniformLayout = true, DefaultButtonSize = ButtonSizes.Large)]
            public void Foo() { }
            
            [GUIColor(0, 1, 0))]
            [Button(ButtonSizes.Large)]
            [ResponsiveButtonGroup]
            public void Bar() { }
            
            [ResponsiveButtonGroup]
            public void Baz() { }
            </code>
            </example>
            <example>
            <code>
            [TabGroup("SomeTabGroup", "SomeTab")]
            [ResponsiveButtonGroup("SomeTabGroup/SomeTab/SomeBtnGroup")]
            public void Foo() { }
            
            [ResponsiveButtonGroup("SomeTabGroup/SomeTab/SomeBtnGroup")]
            public void Bar() { }
            
            [ResponsiveButtonGroup("SomeTabGroup/SomeTab/SomeBtnGroup")]
            public void Baz() { }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.ResponsiveButtonGroupAttribute.DefaultButtonSize">
            <summary>
            The default size of the button.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ResponsiveButtonGroupAttribute.UniformLayout">
            <summary>
            If <c>true</c> then the widths of a line of buttons will be the same.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ResponsiveButtonGroupAttribute.#ctor(System.String)">
            <summary>
            Draws a button that will be placed in a group that will respond to the horizontal space available to the group.
            </summary>
            <param name="group">The name of the group to place the button in.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ResponsiveButtonGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Merges the values of this group with another ResponsiveButtonGroupAttribute.
            </summary>
            <param name="other">The attribute to combine with.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.SceneObjectsOnlyAttribute">
            <summary>
            <para>SceneObjectsOnly is used on object properties, and restricts the property to scene objects, and not project assets.</para>
            <para>Use this when you want to ensure an object is a scene object, and not from a project asset.</para>
            </summary>
            <example>
            <para>The following example shows a component with a game object property, that must be from a scene, and not a prefab asset.</para>
            <code>
            public MyComponent : MonoBehaviour
            {
            	[SceneObjectsOnly]
            	public GameObject MyPrefab;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.AssetsOnlyAttribute"/>
        </member>
        <member name="T:Sirenix.OdinInspector.SearchableAttribute">
            <summary>
            Adds a search filter that can search the children of the field or type on
            which it is applied. Note that this does not currently work when directly
            applied to dictionaries, though a search field "above" the dictionary will
            still search the dictionary's properties if it is searching recursively.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.SearchableAttribute.FuzzySearch">
            <summary>
            Whether to use fuzzy string matching for the search.
            Default value: true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.SearchableAttribute.FilterOptions">
            <summary>
            The options for which things to use to filter the search.
            Default value: All.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.SearchableAttribute.Recursive">
            <summary>
            Whether to search recursively, or only search the top level properties.
            Default value: true.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowDrawerChainAttribute">
            <summary>
            <para>
            ShowDrawerChain lists all prepend, append and value drawers being used in the inspector.
            This is great in situations where you want to debug, and want to know which drawers might be involved in drawing the property.
            </para>
            <para>Your own custom drawers are highlighted with a green label.</para>
            <para>Drawers, that have not been called during the draw chain, will be greyed out in the inspector to make it clear which drawers have had an effect on the properties.</para>
            </summary>
            <example>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[ShowDrawerChain]
            	public int IndentedInt;
            }
            </code>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowIfAttribute">
             <summary>
             <para>ShowIf is used on any property and can hide the property in the inspector.</para>
             <para>Use this to hide irrelevant properties based on the current state of the object.</para>
             </summary>
             <example>
             <para>This example shows a component with fields hidden by the state of another field.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		public bool ShowProperties;
            
            		[ShowIf("showProperties")]
            		public int MyInt;
            
            		[ShowIf("showProperties", false)]
            		public string MyString;
            		
            	    public SomeEnum SomeEnumField;
            		
            		[ShowIf("SomeEnumField", SomeEnum.SomeEnumMember)]
            		public string SomeString;
             }
             </code>
             </example>
             <example>
             <para>This example shows a component with a field that is hidden when the game object is inactive.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[ShowIf("MyVisibleFunction")]
            		public int MyHideableField;
            
            		private bool MyVisibleFunction()
            		{
            			return this.gameObject.activeInHierarchy;
            		}
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.EnableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.DisableIfAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.HideIfAttribute"/>
        </member>
        <member name="P:Sirenix.OdinInspector.ShowIfAttribute.MemberName">
            <summary>
            The name of a bool member field, property or method. Obsolete; use the Condition member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ShowIfAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ShowIfAttribute.Value">
            <summary>
            The optional condition value.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ShowIfAttribute.Animate">
            <summary>
            Whether or not to slide the property in and out when the state changes.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ShowIfAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Shows a property in the inspector, based on the value of a resolved string.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="animate">Whether or not to slide the property in and out when the state changes.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ShowIfAttribute.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Shows a property in the inspector, if the resolved string evaluates to the specified value.
            </summary>
            <param name="condition">A resolved string that defines the condition to check the value of, such as a member name or an expression.</param>
            <param name="optionalValue">Value to check against.</param>
            <param name="animate">Whether or not to slide the property in and out when the state changes.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowIfGroupAttribute">
            <summary>
            <p>ShowIfGroup allows for showing or hiding a group of properties based on a condition.</p>
            <p>The attribute is a group attribute and can therefore be combined with other group attributes, and even be used to show or hide entire groups.</p>
            <p>Note that in the vast majority of cases where you simply want to be able to control the visibility of a single group, it is better to use the VisibleIf parameter that *all* group attributes have.</p>
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.ShowIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HideIfAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.HideIfGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ShowInInspectorAttribute"/>
            <seealso cref="T:UnityEngine.HideInInspector"/>
        </member>
        <member name="P:Sirenix.OdinInspector.ShowIfGroupAttribute.Animate">
            <summary>
            Whether or not to visually animate group visibility changes. Alias for AnimateVisibility.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ShowIfGroupAttribute.Value">
            <summary>
            The optional member value.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ShowIfGroupAttribute.MemberName">
            <summary>
            Name of member to use when to hide the group. Defaults to the name of the group, by can be overriden by setting this property.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ShowIfGroupAttribute.Condition">
            <summary>
            A resolved string that defines the condition to check the value of, such as a member name or an expression.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ShowIfGroupAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Makes a group that can be shown or hidden based on a condition.
            </summary>
            <param name="path">The group path.</param>
            <param name="animate">If <c>true</c> then a fade animation will be played when the group is hidden or shown.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ShowIfGroupAttribute.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Makes a group that can be shown or hidden based on a condition.
            </summary>
            <param name="path">The group path.</param>
            <param name="value">The value the member should equal for the property to shown.</param>
            <param name="animate">If <c>true</c> then a fade animation will be played when the group is hidden or shown.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ShowIfGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines ShowIfGroup attributes.
            </summary>
            <param name="other">Another ShowIfGroup attribute.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowInAttribute">
            <summary>
            Shows a member based on which type of a prefab and instance it is in. 
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowInInlineEditorsAttribute">
            <summary>
            Only shows a property if it is drawn within an <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute"/>.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowInInspectorAttribute">
             <summary>
             <para>ShowInInspector is used on any member, and shows the value in the inspector. Note that the value being shown due to this attribute DOES NOT mean that the value is being serialized.</para>
             </summary>
             <remarks>
             <para>This can for example be combined with <see cref="T:Sirenix.OdinInspector.ReadOnlyAttribute"/> to allow for live debugging of values.</para>
             <note type="note"></note>
             </remarks>
             <example>
             <para>The following example shows how ShowInInspector is used to show properties in the inspector, that otherwise wouldn't.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[ShowInInspector]
            		private int myField;
            
            		[ShowInInspector]
            		public int MyProperty { get; set; }
            	}
             </code>
             </example>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowOdinSerializedPropertiesInInspectorAttribute">
            <summary>
            Marks a type as being specially serialized. Odin uses this attribute to check whether it should include non-Unity-serialized members in the inspector.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ShowPropertyResolverAttribute">
            <summary>
            <para>
            ShowPropertyResolver shows the property resolver responsible for bringing the member into the property tree.
            This is useful in situations where you want to debug why a particular member that is normally not shown in the inspector suddenly is.
            </para>
            </summary>
            <example>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[ShowPropertyResolver]
            	public int IndentedInt;
            }
            </code>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.SuffixLabelAttribute">
             <summary>
             <para>The SuffixLabel attribute draws a label at the end of a property.</para>
             <para>Use this for conveying intend about a property. Is the distance measured in meters, kilometers, or in light years?.
             Is the angle measured in degrees or radians?
             Using SuffixLabel, you can place a neat label at the end of a property, to clearly show how the the property is used.</para>
             </summary>
             <example>
             <para>The following example demonstrates how SuffixLabel is used.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		// The SuffixLabel attribute draws a label at the end of a property.
            		// It's useful for conveying intend about a property.
            		// Fx, this field is supposed to have a prefab assigned.
            		[SuffixLabel("Prefab")]
            		public GameObject GameObject;
            
            		// Using the Overlay property, the suffix label will be drawn on top of the property instead of behind it.
            		// Use this for a neat inline look.
            		[SuffixLabel("ms", Overlay = true)]
            		public float Speed;
            
            		[SuffixLabel("radians", Overlay = true)]
            		public float Angle;
            
            		// The SuffixLabel attribute also supports string member references by using $.
            		[SuffixLabel("$Suffix", Overlay = true)]
            		public string Suffix = "Dynamic suffix label";
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.LabelTextAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.HideLabelAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.InlineButtonAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.LabelWidthAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.SuffixLabelAttribute.Label">
            <summary>
            The label displayed at the end of the property.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.SuffixLabelAttribute.Overlay">
            <summary>
            If <c>true</c> the suffix label will be drawn on top of the property, instead of after.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.SuffixLabelAttribute.Icon">
            <summary>
            The icon to be displayed.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.SuffixLabelAttribute.IconColor">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.SuffixLabelAttribute.#ctor(System.String,System.Boolean)">
            <summary>
            Draws a label at the end of the property.
            </summary>
            <param name="label">The text of the label.</param>
            <param name="overlay">If <c>true</c> the suffix label will be drawn on top of the property, instead of after.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.SuffixLabelAttribute.#ctor(System.String,Sirenix.OdinInspector.SdfIconType,System.Boolean)">
            <summary>
            Draws a label at the end of the property.
            </summary>
            <param name="label">The text of the label.</param>
            <param name="icon">The icon to be displayed.</param>
            <param name="overlay">If <c>true</c> the suffix label will be drawn on top of the property, instead of after.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.SuffixLabelAttribute.#ctor(Sirenix.OdinInspector.SdfIconType)">
            <summary>
            Draws a label at the end of the property.
            </summary>
            <param name="icon">The icon to be displayed.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.SuppressInvalidAttributeErrorAttribute">
             <summary>
             <para>SuppressInvalidAttributeError is used on members to suppress the inspector error message you get when applying an attribute to a value that it's not supposed to work on.</para>
             <para>This can be very useful for applying attributes to generic parameter values, when it only applies to some of the possible types that the value might become.</para>
             </summary>
             <example>
             <para>The following example shows a case where the attribute might be useful.</para>
             <code>
             public class NamedValue&lt;T&gt;
             {
                 public string Name;
            
                 // The Range attribute will be applied if T is compatible with it, but if T is not compatible, an error will not be shown.
            		[SuppressInvalidAttributeError, Range(0, 10)]
            		public T Value;
             }
             </code>
             </example>
        </member>
        <member name="T:Sirenix.OdinInspector.TabGroupAttribute">
             <summary>
             <para>TabGroup is used on any property, and organizes properties into different tabs.</para>
             <para>Use this to organize different value to make a clean and easy to use inspector.</para>
             </summary>
             <remarks>
             <para>Use groups to create multiple tab groups, each with multiple tabs and even sub tabs.</para>
             </remarks>
             <example>
             <para>The following example shows how to create a tab group with two tabs.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		[TabGroup("First")]
            		public int MyFirstInt;
            
            		[TabGroup("First")]
            		public int AnotherInt;
            
            		[TabGroup("Second")]
            		public int MySecondInt;
            	}
             </code>
             </example>
             <example>
             <para>The following example shows how multiple groups of tabs can be created.</para>
             <code>
            	public class MyComponent : MonoBehaviour
            	{
            		[TabGroup("A", "FirstGroup")]
            		public int FirstGroupA;
            
            		[TabGroup("B", "FirstGroup")]
            		public int FirstGroupB;
            
            		// The second tab group has been configured to have constant height across all tabs.
            		[TabGroup("A", "SecondGroup", true)]
            		public int SecondgroupA;
            
            		[TabGroup("B", "SecondGroup")]
            		public int SecondGroupB;
            
            		[TabGroup("B", "SecondGroup")]
            		public int AnotherInt;
            	}
             </code>
             </example>
             <example>
             <para>This example demonstrates how multiple tabs groups can be combined to create tabs in tabs.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
                 [TabGroup("ParentGroup", "First Tab")]
                 public int A;
                 
                 [TabGroup("ParentGroup", "Second Tab")]
                 public int B;
                 
                 // Specify 'First Tab' as a group, and another child group to the 'First Tab' group.
                 [TabGroup("ParentGroup/First Tab/InnerGroup", "Inside First Tab A")]
                 public int C;
                 
                 [TabGroup("ParentGroup/First Tab/InnerGroup", "Inside First Tab B")]
                 public int D;
                 
                 [TabGroup("ParentGroup/Second Tab/InnerGroup", "Inside Second Tab")]
                 public int E;
             }
             </code>
             </example>
             <seealso cref="!:TabListAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.DEFAULT_NAME">
            <summary>
            The default tab group name which is used when the single-parameter constructor is called.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.TabName">
            <summary>
            Name of the tab.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.UseFixedHeight">
            <summary>
            Should this tab be the same height as the rest of the tab group.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.Paddingless">
            <summary>
            If true, the content of each page will not be contained in any box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.HideTabGroupIfTabGroupOnlyHasOneTab">
            <summary>
            If true, the tab group will be hidden if it only contains one tab.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.TextColor">
            <summary> Supports a variety of color formats, including named colors (e.g. "red", "orange", "green", "blue"), hex codes (e.g. "#FF0000" and "#FF0000FF"), and RGBA (e.g. "RGBA(1,1,1,1)") or RGB (e.g. "RGB(1,1,1)"), including Odin attribute expressions (e.g "@this.MyColor"). Here are the available named colors: black, blue, clear, cyan, gray, green, grey, magenta, orange, purple, red, transparent, transparentBlack, transparentWhite, white, yellow, lightblue, lightcyan, lightgray, lightgreen, lightgrey, lightmagenta, lightorange, lightpurple, lightred, lightyellow, darkblue, darkcyan, darkgray, darkgreen, darkgrey, darkmagenta, darkorange, darkpurple, darkred, darkyellow. </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.TabLayouting">
            <summary>
            Specify how tabs should be layouted.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TabGroupAttribute.#ctor(System.String,System.Boolean,System.Single)">
            <summary>
            Organizes the property into the specified tab in the default group.
            Default group name is '_DefaultTabGroup'
            </summary>
            <param name="tab">The tab.</param>
            <param name="useFixedHeight">if set to <c>true</c> [use fixed height].</param>
            <param name="order">The order.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.TabGroupAttribute.#ctor(System.String,System.String,System.Boolean,System.Single)">
            <summary>
            Organizes the property into the specified tab in the specified group.
            </summary>
            <param name="group">The group to attach the tab to.</param>
            <param name="tab">The name of the tab.</param>
            <param name="useFixedHeight">Set to true to have a constant height across the entire tab group.</param>
            <param name="order">The order of the group.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.TabGroupAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.SdfIconType,System.Boolean,System.Single)">
            <summary>
            Organizes the property into the specified tab in the specified group.
            </summary>
            <param name="group">The group to attach the tab to.</param>
            <param name="tab">The name of the tab.</param>
            <param name="useFixedHeight">Set to true to have a constant height across the entire tab group.</param>
            <param name="order">The order of the group.</param>
        </member>
        <member name="F:Sirenix.OdinInspector.TabGroupAttribute.Tabs">
            <summary>
            Name of all tabs in this group.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.TableColumnWidthAttribute">
            <summary>
            The TableColumnWidth attribute is used to further customize the width of a column in tables drawn using the <see cref="T:Sirenix.OdinInspector.TableListAttribute"/>.
            </summary>
            <example>
            <code>
            [TableList]
            public List&lt;SomeType&gt; TableList = new List&lt;SomeType&gt;();
            
            [Serializable]
            public class SomeType
            {
                [LabelWidth(30)]
                [TableColumnWidth(130, false)]
                [VerticalGroup("Combined")]
                public string A;
            
                [LabelWidth(30)]
                [VerticalGroup("Combined")]
                public string B;
            
                [Multiline(2), Space(3)]
                public string fields;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.TableListAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TableColumnWidthAttribute.Width">
            <summary>
            The width of the column.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableColumnWidthAttribute.Resizable">
            <summary>
            Whether the column should be resizable. True by default.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TableColumnWidthAttribute.#ctor(System.Int32,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.TableColumnWidthAttribute"/> class.
            </summary>
            <param name="width">The width of the column in pixels.</param>
            <param name="resizable">If <c>true</c> then the column can be resized in the inspector.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.TableListAttribute">
            <summary>
            Renders lists and arrays in the inspector as tables.
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.TableColumnWidthAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.TableColumnWidthAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.NumberOfItemsPerPage">
            <summary>
            If ShowPaging is enabled, this will override the default setting specified in the Odin Preferences window.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.IsReadOnly">
            <summary>
            Mark the table as read-only. This removes all editing capabilities from the list such as Add and delete,
            but without disabling GUI for each element drawn as otherwise would be the case if the <see cref="T:Sirenix.OdinInspector.ReadOnlyAttribute"/> was used.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.DefaultMinColumnWidth">
            <summary>
            The default minimum column width - 40 by default. This can be overwriten by individual columns using the <see cref="T:Sirenix.OdinInspector.TableColumnWidthAttribute"/>.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.ShowIndexLabels">
            <summary>
            If true, a label is drawn for each element which shows the index of the element.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.DrawScrollView">
            <summary>
            Whether to draw all rows in a scroll-view.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.MinScrollViewHeight">
            <summary>
            The number of pixels before a scroll view appears. 350 by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.MaxScrollViewHeight">
            <summary>
            The number of pixels before a scroll view appears. 0 by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.AlwaysExpanded">
            <summary>
            If true, expanding and collapsing the table from the table title-bar is no longer an option.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.HideToolbar">
            <summary>
            Whether to hide the toolbar containing the add button and pagin etc.s
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableListAttribute.CellPadding">
            <summary>
            The cell padding.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TableListAttribute.ShowPaging">
            <summary>
            Whether paging buttons should be added to the title bar. The default value of this, can be customized from the Odin Preferences window.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TableListAttribute.ShowPagingHasValue">
            <summary>
            Whether the ShowPaging property has been set.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TableListAttribute.ScrollViewHeight">
            <summary>
            Sets the Min and Max ScrollViewHeight.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.TableMatrixAttribute">
             <summary>
             The TableMatrix attribute is used to further specify how Odin should draw two-dimensional arrays.
             </summary>
             <example>
             <code>
             // Inheriting from SerializedMonoBehaviour is only needed if you want Odin to serialize the multi-dimensional arrays for you.
             // If you prefer doing that yourself, you can still make Odin show them in the inspector using the ShowInInspector attribute.
             public class TableMatrixExamples : SerializedMonoBehaviour
             {
                 [InfoBox("Right-click and drag column and row labels in order to modify the tables."), PropertyOrder(-10), OnInspectorGUI]
                 private void ShowMessageAtOP() { }
            
                 [BoxGroup("Two Dimensional array without the TableMatrix attribute.")]
                 public bool[,] BooleanTable = new bool[15, 6];
            
                 [BoxGroup("ReadOnly table")]
                 [TableMatrix(IsReadOnly = true)]
                 public int[,] ReadOnlyTable = new int[5, 5];
            
                 [BoxGroup("Labled table")]
                 [TableMatrix(HorizontalTitle = "X axis", VerticalTitle = "Y axis")]
                 public GameObject[,] LabledTable = new GameObject[15, 10];
            
                 [BoxGroup("Enum table")]
                 [TableMatrix(HorizontalTitle = "X axis")]
                 public InfoMessageType[,] EnumTable = new InfoMessageType[4,4];
            
                 [BoxGroup("Custom table")]
                 [TableMatrix(DrawElementMethod = "DrawColoredEnumElement", ResizableColumns = false)]
                 public bool[,] CustomCellDrawing = new bool[30,30];
            
                 #if UNITY_EDITOR
            
                     private static bool DrawColoredEnumElement(Rect rect, bool value)
                     {
                         if (Event.current.type == EventType.MouseDown &#38;&#38; rect.Contains(Event.current.mousePosition))
                         {
                             value = !value;
                             GUI.changed = true;
                             Event.current.Use();
                         }
            
                         UnityEditor.EditorGUI.DrawRect(rect.Padding(1), value ? new Color(0.1f, 0.8f, 0.2f) : new Color(0, 0, 0, 0.5f));
            
                         return value;
                     }
            
                 #endif
             }
             </code>
             </example>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.IsReadOnly">
            <summary>
            If true, inserting, removing and dragging columns and rows will become unavailable. But the cells themselves will remain modifiable.
            If you want to disable everything, you can use the <see cref="!:ReadOnly"/> attribute.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.ResizableColumns">
            <summary>
            Whether or not columns are resizable.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.VerticalTitle">
            <summary>
            The vertical title label.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.HorizontalTitle">
            <summary>
            The horizontal title label.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.DrawElementMethod">
            <summary>
            Override how Odin draws each cell.                  <para />
            [TableMatrix(DrawElementMethod='DrawMyElement')]    <para />
            public MyType[,] myArray;                           <para />
            private static MyType DrawElement(Rect rect, MyType value) { return GUI.DrawMyType(rect, value); }
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.RowHeight">
            <summary>
            The height for all rows. 0 = default row height.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.SquareCells">
            <summary>
            If true, the height of each row will be the same as the width of the first cell.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.HideColumnIndices">
            <summary>
            If true, no column indices drawn.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.HideRowIndices">
            <summary>
            If true, no row indices drawn.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.RespectIndentLevel">
            <summary>
            Whether the drawn table should respect the current GUI indent level.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.Transpose">
            <summary>
            If true, tables are drawn with rows/columns reversed (C# initialization order).
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TableMatrixAttribute.Labels">
            <summary>
            A resolved string that should evaluate to a tuple (string, LabelDirection) which will be used as the label for the rows and columns of the table.
            </summary>
            <code>
            [TableMatrix(SquareCells = true, Labels = "GetLabel")]
            public int[,] ChessBoard = new int[8, 8];
             
            private (string, LabelDirection) GetLabel(int[,] array, TableAxis axis, int index)
            {
                var chessFileLetters = "ABCDEFGH";
             
                switch (axis)
                {
                    case TableAxis.Y:
                        return ((array.GetLength(1) - index).ToString(), LabelDirection.LeftToRight);
                    case TableAxis.X:
                        return (chessFileLetters[index].ToString(), LabelDirection.TopToBottom);
                    default:
                        return (index.ToString(), LabelDirection.LeftToRight);
                }
            }
            </code>
        </member>
        <member name="T:Sirenix.OdinInspector.TitleAttribute">
            <summary>
            <para>Title is used to make a bold header above a property.</para>
            </summary>
            <example>
            The following example shows how Title is used on different properties.
            <code>
            public class TitleExamples : MonoBehaviour
            {
                [Title("Titles and Headers")]
                [InfoBox(
                    "The Title attribute has the same purpose as Unity's Header attribute," +
                    "but it also supports properties, and methods." +
                    "\n\nTitle also offers more features such as subtitles, options for horizontal underline, bold text and text alignment." +
                    "\n\nBoth attributes, with Odin, supports either static strings, or refering to members strings by adding a $ in front.")]
                public string MyTitle = "My Dynamic Title";
                public string MySubtitle = "My Dynamic Subtitle";
            
                [Title("Static title")]
                public int C;
                public int D;
            
                [Title("Static title", "Static subtitle")]
                public int E;
                public int F;
            
                [Title("$MyTitle", "$MySubtitle")]
                public int G;
                public int H;
            
                [Title("Non bold title", "$MySubtitle", bold: false)]
                public int I;
                public int J;
            
                [Title("Non bold title", "With no line seperator", horizontalLine: false, bold: false)]
                public int K;
                public int L;
            
                [Title("$MyTitle", "$MySubtitle", TitleAlignments.Right)]
                public int M;
                public int N;
            
                [Title("$MyTitle", "$MySubtitle", TitleAlignments.Centered)]
                public int O;
                public int P;
            
                [Title("$Combined", titleAlignment: TitleAlignments.Centered)]
                public int Q;
                public int R;
            
                [ShowInInspector]
                [Title("Title on a Property")]
                public int S { get; set; }
            
                [Title("Title on a Method")]
                [Button]
                public void DoNothing()
                { }
            
                public string Combined { get { return this.MyTitle + " - " + this.MySubtitle; } }
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.ButtonAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.LabelTextAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAttribute.Title">
            <summary>
            The title displayed above the property in the inspector.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAttribute.Subtitle">
            <summary>
            Optional subtitle.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAttribute.Bold">
            <summary>
            If <c>true</c> the title will be displayed with a bold font.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAttribute.HorizontalLine">
            <summary>
            Gets a value indicating whether or not to draw a horizontal line below the title.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAttribute.TitleAlignment">
            <summary>
            Title alignment.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TitleAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.TitleAlignments,System.Boolean,System.Boolean)">
            <summary>
            Creates a title above any property in the inspector.
            </summary>
            <param name="title">The title displayed above the property in the inspector.</param>
            <param name="subtitle">Optional subtitle</param>
            <param name="titleAlignment">Title alignment</param>
            <param name="horizontalLine">Horizontal line</param>
            <param name="bold">If <c>true</c> the title will be drawn with a bold font.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.TitleGroupAttribute">
            <summary>
            Groups properties vertically together with a title, an optional subtitle, and an optional horizontal line. 
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleGroupAttribute.Subtitle">
            <summary>
            Optional subtitle.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleGroupAttribute.Alignment">
            <summary>
            Title alignment.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleGroupAttribute.HorizontalLine">
            <summary>
            Gets a value indicating whether or not to draw a horizontal line below the title.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleGroupAttribute.BoldTitle">
            <summary>
            If <c>true</c> the title will be displayed with a bold font.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleGroupAttribute.Indent">
            <summary>
            Gets a value indicating whether or not to indent all group members.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TitleGroupAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.TitleAlignments,System.Boolean,System.Boolean,System.Boolean,System.Single)">
            <summary>
            Groups properties vertically together with a title, an optional subtitle, and an optional horizontal line. 
            </summary>
            <param name="title">The title-</param>
            <param name="subtitle">Optional subtitle.</param>
            <param name="alignment">The text alignment.</param>
            <param name="horizontalLine">Horizontal line.</param>
            <param name="boldTitle">Bold text.</param>
            <param name="indent">Whether or not to indent all group members.</param>
            <param name="order">The group order.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.TitleGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines TitleGroup attributes.
            </summary>
            <param name="other">The other group attribute to combine with.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ToggleAttribute">
             <summary>
             <para>Toggle is used on any field or property, and allows to enable or disable the property in the inspector.</para>
             <para>Use this to create a property that can be turned off or on.</para>
             </summary>
             <remarks>
             <note type="note">Toggle does current not support any static members for toggling.</note>
             </remarks>
             <example>
             <para>The following example shows how Toggle is used to create a toggleable property.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		[Toggle("Enabled")]
            		public MyToggleable MyToggler = new MyToggleable();
            	}
            
            	public class MyToggleable
            	{
            		public bool Enabled;
            
            		public int MyValue;
            	}
             </code>
             </example>
             <seealso cref="!:ToggleListAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ToggleAttribute.ToggleMemberName">
            <summary>
            Name of any bool field or property to enable or disable the object.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ToggleAttribute.CollapseOthersOnExpand">
            <summary>
            If true, all other open toggle groups will collapse once another one opens.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ToggleAttribute.#ctor(System.String)">
            <summary>
            Create a togglable property in the inspector.
            </summary>
            <param name="toggleMemberName">Name of any bool field or property to enable or disable the object.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ToggleGroupAttribute">
             <summary>
             <para>ToggleGroup is used on any field, and create a toggleable group of options.</para>
             <para>Use this to create options that can be enabled or disabled.</para>
             </summary>
             <remarks>
             <para>The <see cref="P:Sirenix.OdinInspector.ToggleGroupAttribute.ToggleMemberName"/> functions as the ID for the ToggleGroup, and therefore all members of a toggle group must specify the same toggle member.</para>
             <note note="Note">This attribute does not support static members!</note>
             </remarks>
             <example>
             <para>The following example shows how ToggleGroup is used to create two separate toggleable groups.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		// This attribute has a title specified for the group. The title only needs to be applied to a single attribute for a group.
            		[ToggleGroup("FirstToggle", order: -1, groupTitle: "First")]
            		public bool FirstToggle;
            
            		[ToggleGroup("FirstToggle")]
            		public int MyInt;
            
            		// This group specifies a member string as the title of the group. A property or a function can also be used.
            		[ToggleGroup("SecondToggle", titleStringMemberName: "SecondGroupTitle")]
            		public bool SecondToggle { get; set; }
            
            		[ToggleGroup("SecondToggle")]
            		public float MyFloat;
            
            		[HideInInspector]
            		public string SecondGroupTitle = "Second";
            	}
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.ToggleAttribute"/>
             <seealso cref="!:ToggleListAttribute"/>"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ToggleGroupAttribute.ToggleGroupTitle">
            <summary>
            Title of the toggle group in the inspector.
            If <c>null</c> <see cref="P:Sirenix.OdinInspector.ToggleGroupAttribute.ToggleMemberName"/> will be used instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ToggleGroupAttribute.CollapseOthersOnExpand">
            <summary>
            If true, all other open toggle groups will collapse once another one opens.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ToggleGroupAttribute.#ctor(System.String,System.Single,System.String)">
            <summary>
            Creates a ToggleGroup. See <see cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>.
            </summary>
            <param name="toggleMemberName">Name of any bool field or property to enable or disable the ToggleGroup.</param>
            <param name="order">The order of the group.</param>
            <param name="groupTitle">Use this to name the group differently than toggleMemberName.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ToggleGroupAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a ToggleGroup. See <see cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>.
            </summary>
            <param name="toggleMemberName">Name of any bool field or property to enable or disable the ToggleGroup.</param>
            <param name="groupTitle">Use this to name the group differently than toggleMemberName.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ToggleGroupAttribute.#ctor(System.String,System.Single,System.String,System.String)">
            <summary>
            Obsolete constructor overload.
            </summary>
            <param name="toggleMemberName">Obsolete overload.</param>
            <param name="order">Obsolete overload.</param>
            <param name="groupTitle">Obsolete overload.</param>
            <param name="titleStringMemberName">Obsolete overload.</param>
        </member>
        <member name="P:Sirenix.OdinInspector.ToggleGroupAttribute.ToggleMemberName">
            <summary>
            Name of any bool field, property or function to enable or disable the ToggleGroup.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ToggleGroupAttribute.TitleStringMemberName">
            <summary>
            Name of any string field, property or function, to title the toggle group in the inspector.
            If <c>null</c> <see cref="F:Sirenix.OdinInspector.ToggleGroupAttribute.ToggleGroupTitle"/> will be used instead.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ToggleGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines the ToggleGroup with another ToggleGroup.
            </summary>
            <param name="other">Another ToggleGroup.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ToggleLeftAttribute">
            <summary>
            <para>Draws the checkbox before the label instead of after.</para>
            </summary>
            <remarks>ToggleLeftAttribute can be used an all fields and properties of type boolean</remarks>
            <example>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[ToggleLeft]
            	public bool MyBoolean;
            }
            </code>
            </example>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeDrawerSettingsAttribute.BaseType">
            <summary>
            Specifies whether a base type should be used instead of all types.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeDrawerSettingsAttribute.Filter">
            <summary>
            Filters the result.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TypeFilterAttribute.MemberName">
            <summary>
            Name of any field, property or method member that implements IList. E.g. arrays or Lists. Obsolete; use the FilterGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeFilterAttribute.FilterGetter">
            <summary>
            A resolved string that should evaluate to a value that is assignable to IList; e.g, arrays and lists are compatible.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeFilterAttribute.DropdownTitle">
            <summary>
            Gets or sets the title for the dropdown. Null by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeFilterAttribute.DrawValueNormally">
            <summary>
            If true, the value will be drawn normally after the type selector dropdown has been drawn. False by default.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TypeFilterAttribute.#ctor(System.String)">
            <summary>
            Creates a dropdown menu for a property.
            </summary>
            <param name="filterGetter">A resolved string that should evaluate to a value that is assignable to IList; e.g, arrays and lists are compatible.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.TypeInfoBoxAttribute">
            <summary>
            <para>The TypeInfoBox attribute adds an info box to the very top of a type in the inspector.</para>
            <para>Use this to add an info box to the top of a class in the inspector, without having to use neither the PropertyOrder nor the OnInspectorGUI attribute.</para>
            </summary>
            <example>
            <para>The following example demonstrates the use of the TypeInfoBox attribute.</para>
            <code>
            [TypeInfoBox("This is my component and it is mine.")]
            public class MyComponent : MonoBehaviour
            {
                // Class implementation.
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.InfoBoxAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.DetailedInfoBoxAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeInfoBoxAttribute.Message">
            <summary>
            The message to display in the info box.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.TypeInfoBoxAttribute.#ctor(System.String)">
            <summary>
            Draws an info box at the top of a type in the inspector.
            </summary>
            <param name="message">The message to display in the info box.</param>
        </member>
        <member name="P:Sirenix.OdinInspector.TypeSelectorSettingsAttribute.ShowNoneItem">
            <summary> Specifies if the '&lt;none&gt;' item is shown. </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TypeSelectorSettingsAttribute.ShowCategories">
            <summary> Specifies if categories are shown. </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.TypeSelectorSettingsAttribute.PreferNamespaces">
            <summary>
            Specifies if namespaces are preferred over assembly category names for category names.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeSelectorSettingsAttribute.FilterTypesFunction">
            <summary>
            Function for filtering types displayed in the Type Selector.
            </summary>
            <example>
            <para>
            The resolver expects any method that takes a single parameter of <see cref="T:System.Type"/>, with the parameter name 'type', and which returns a <see cref="T:System.Boolean"/> indicating whether the <see cref="T:System.Type"/> is included or not;
            </para>
            
            <para>Implementation example: <c>public bool SomeFilterMethod(Type type)</c>.</para>
            </example>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Sirenix.OdinInspector.UnitAttribute" -->
        <member name="F:Sirenix.OdinInspector.UnitAttribute.Base">
            <summary>
            The unit of underlying value.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.UnitAttribute.Display">
            <summary>
            The unit displayed in the number field.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.UnitAttribute.BaseName">
            <summary>
            Name of the underlying unit.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.UnitAttribute.DisplayName">
            <summary>
            Name of the unit displayed in the number field.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.UnitAttribute.DisplayAsString">
            <summary>
            If <c>true</c> the number field is drawn as read-only text.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.UnitAttribute.ForceDisplayUnit">
            <summary>
            If <c>true</c> disables the option to change display unit with the right-click context menu.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(Sirenix.OdinInspector.Units)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="unit">The unit of underlying value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(System.String)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="unit">The name of the underlying value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(Sirenix.OdinInspector.Units,Sirenix.OdinInspector.Units)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="base">The unit of underlying value.</param>
            <param name="display">The unit to display the value as in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(Sirenix.OdinInspector.Units,System.String)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="base">The unit of underlying value.</param>
            <param name="display">The unit to display the value as in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(System.String,Sirenix.OdinInspector.Units)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="base">The unit of underlying value.</param>
            <param name="display">The unit to display the value as in the inspector.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.UnitAttribute.#ctor(System.String,System.String)">
            <summary>
            Displays the number as a unit field.
            </summary>
            <param name="base">The unit of underlying value.</param>
            <param name="display">The unit to display the value as in the inspector.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.Units">
            <summary>
            Units for use with <see cref="T:Sirenix.OdinInspector.UnitAttribute"/> and <see cref="!:Sirenix.Utilities.Editor.UnitNumberUtility"/>.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ValidateInputAttribute">
             <summary>
             <para>ValidateInput is used on any property, and allows to validate input from inspector.</para>
             <para>Use this to enforce correct values.</para>
             </summary>
             <remarks>
             <note type="note">ValidateInput refuses invalid values.</note>
             <note type="note">ValidateInput only works in the editor. Values changed through scripting will not be validated.</note>
             </remarks>
             <example>
             <para>The following examples shows how a speed value can be forced to be above 0.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[ValidateInput("ValidateInput")]
            		public float Speed;
            
            		// Specify custom output message and message type.
            		[ValidateInput("ValidateInput", "Health must be more than 0!", InfoMessageType.Warning)]
            		public float Health;
            
            		private bool ValidateInput(float property)
            		{
            			return property > 0f;
            		}
             }
             </code>
             </example>
             <example>
             <para>The following example shows how a static function could also be used.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[ValidateInput("StaticValidateFunction")]
            		public int MyInt;
            
            		private static bool StaticValidateFunction(int property)
            		{
            			return property != 0;
            		}
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.InfoBoxAttribute"/>
             <seealso cref="T:Sirenix.OdinInspector.RequiredAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ValidateInputAttribute.DefaultMessage">
            <summary>
            Default message for invalid values.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.ValidateInputAttribute.MemberName">
            <summary>
            OBSOLETE; use the Condition member instead.
            A resolved string that should evaluate to a boolean value, and which should validate the input. Note that in expressions, the $value named parameter, and in methods, a parameter named value, can be used to get the validated value instead of referring to the value by its containing member. This makes it easier to reuse validation strings.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValidateInputAttribute.Condition">
            <summary>
            A resolved string that should evaluate to a boolean value, and which should validate the input. Note that in expressions, the $value named parameter, and in methods, a parameter named value, can be used to get the validated value instead of referring to the value by its containing member. This makes it easier to reuse validation strings.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValidateInputAttribute.MessageType">
            <summary>
            The type of the message.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValidateInputAttribute.IncludeChildren">
            <summary>
            Whether to also trigger validation when changes to child values happen. This is true by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValidateInputAttribute.ContinuousValidationCheck">
            <summary>
            If true, the validation method will not only be executed when the User has changed the value. It'll run once every frame in the inspector.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValidateInputAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.InfoMessageType)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.ValidateInputAttribute"/> class.
            </summary>
            <param name="condition">A resolved string that should evaluate to a boolean value, and which should validate the input. Note that in expressions, the $value named parameter, and in methods, a parameter named value, can be used to get the validated value instead of referring to the value by its containing member. This makes it easier to reuse validation strings.</param>
            <param name="defaultMessage">Default message for invalid values.</param>
            <param name="messageType">Type of the message.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ValidateInputAttribute.#ctor(System.String,System.String,Sirenix.OdinInspector.InfoMessageType,System.Boolean)">
            <summary>
            Obsolete. Rejecting invalid input is no longer supported. Use the other constructors instead.
            </summary>
            <param name="condition">Obsolete overload.</param>
            <param name="message">Obsolete overload.</param>
            <param name="messageType">Obsolete overload.</param>
            <param name="rejectedInvalidInput">Obsolete overload.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ValueDropdownAttribute">
             <summary>
             <para>ValueDropdown is used on any property and creates a dropdown with configurable options.</para>
             <para>Use this to give the user a specific set of options to select from.</para>
             </summary>
             <remarks>
             <note type="note">Due to a bug in Unity, enums will sometimes not work correctly. The last example shows how this can be fixed.</note>
             </remarks>
             <example>
             <para>The following example shows a how the ValueDropdown can be used on an int property.</para>
             <code>
             public class MyComponent : MonoBehaviour
            	{
            		[ValueDropdown("myValues")]
            		public int MyInt;
            
            		// The selectable values for the dropdown.
            		private int[] myValues = { 1, 2, 3 };
            	}
             </code>
             </example>
             <example>
             <para>The following example shows how ValueDropdownList can be used for objects, that do not implement a usable ToString.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		[ValueDropdown("myVectorValues")]
            		public Vector3 MyVector;
            
            		// The selectable values for the dropdown, with custom names.
            		private ValueDropdownList&lt;Vector3&gt; myVectorValues = new ValueDropdownList&lt;Vector3&gt;()
            		{
            			{"Forward",	Vector3.forward	},
            			{"Back",	Vector3.back	},
            			{"Up",		Vector3.up		},
            			{"Down",	Vector3.down	},
            			{"Right",	Vector3.right	},
            			{"Left",	Vector3.left	},
            		};
             }
             </code>
             </example>
            	<example>
            	<para>The following example shows how the ValueDropdown can on any member that implements IList.</para>
            	<code>
             public class MyComponent : MonoBehaviour
             {
            		// Member field of type float[].
            		private float[] valuesField;
            
            		[ValueDropdown("valuesField")]
            		public float MyFloat;
            
            		// Member property of type List&lt;thing&gt;.
            		private List&lt;string&gt; ValuesProperty { get; set; }
            
            		[ValueDropdown("ValuesProperty")]
            		public string MyString;
            
            		// Member function that returns an object of type IList.
            		private IList&lt;ValueDropdownItem&lt;int&gt;&gt; ValuesFunction()
            		{
            			return new ValueDropdownList&lt;int&gt;
            			{
            				{ "The first option",	1 },
            				{ "The second option",	2 },
            				{ "The third option",	3 },
            			};
            		}
            
            		[ValueDropdown("ValuesFunction")]
            		public int MyInt;
             }
             </code>
             </example>
             <example>
             <para>Due to a bug in Unity, enums member arrays will in some cases appear as empty. This example shows how you can get around that.</para>
             <code>
             public class MyComponent : MonoBehaviour
             {
            		// Make the field static.
            		private static MyEnum[] MyStaticEnumArray = MyEnum[] { ... };
            
            		// Force Unity to serialize the field, and hide the property from the inspector.
            		[SerializeField, HideInInspector]
            		private MyEnum MySerializedEnumArray = MyEnum[] { ... };
             }
             </code>
             </example>
             <seealso cref="T:Sirenix.OdinInspector.ValueDropdownList`1"/>
        </member>
        <member name="P:Sirenix.OdinInspector.ValueDropdownAttribute.MemberName">
            <summary>
            Name of any field, property or method member that implements IList. E.g. arrays or Lists. Obsolete; use the ValuesGetter member instead.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.ValuesGetter">
            <summary>
            A resolved string that should evaluate to a value that is assignable to IList; e.g, arrays and lists are compatible.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.NumberOfItemsBeforeEnablingSearch">
            <summary>
            The number of items before enabling search. Default is 10.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.IsUniqueList">
            <summary>
            False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DrawDropdownForListElements">
            <summary>
            True by default. If the ValueDropdown attribute is applied to a list, then disabling this,
            will render all child elements normally without using the ValueDropdown. The ValueDropdown will
            still show up when you click the add button on the list drawer, unless <see cref="F:Sirenix.OdinInspector.ValueDropdownAttribute.DisableListAddButtonBehaviour"/> is true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DisableListAddButtonBehaviour">
            <summary>
            False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.ExcludeExistingValuesInList">
            <summary>
            If the ValueDropdown attribute is applied to a list, and <see cref="F:Sirenix.OdinInspector.ValueDropdownAttribute.IsUniqueList"/> is set to true, then enabling this,
            will exclude existing values, instead of rendering a checkbox indicating whether the item is already included or not.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.ExpandAllMenuItems">
            <summary>
            If the dropdown renders a tree-view, then setting this to true will ensure everything is expanded by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.AppendNextDrawer">
            <summary>
            If true, instead of replacing the drawer with a wide dropdown-field, the dropdown button will be a little button, drawn next to the other drawer.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DisableGUIInAppendedDrawer">
            <summary>
            Disables the the GUI for the appended drawer. False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DoubleClickToConfirm">
            <summary>
            By default, a single click selects and confirms the selection.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.FlattenTreeView">
            <summary>
            By default, the dropdown will create a tree view.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DropdownWidth">
            <summary>
            Gets or sets the width of the dropdown. Default is zero.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DropdownHeight">
            <summary>
            Gets or sets the height of the dropdown. Default is zero.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.DropdownTitle">
            <summary>
            Gets or sets the title for the dropdown. Null by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.SortDropdownItems">
            <summary>
            False by default.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.HideChildProperties">
            <summary>
            Whether to draw all child properties in a foldout.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.CopyValues">
            <summary>
            Whether values selected by the value dropdown should be copies of the original or references (in the case of reference types). Defaults to true.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownAttribute.OnlyChangeValueOnConfirm">
            <summary>
            If this is set to true, the actual property value will *only* be changed *once*, when the selection in the dropdown is fully confirmed.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownAttribute.#ctor(System.String)">
            <summary>
            Creates a dropdown menu for a property.
            </summary>
            <param name="valuesGetter">A resolved string that should evaluate to a value that is assignable to IList; e.g, arrays and lists are compatible.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.IValueDropdownItem">
            <summary>
            
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.IValueDropdownItem.GetText">
            <summary>
            Gets the label for the dropdown item.
            </summary>
            <returns>The label text for the item.</returns>
        </member>
        <member name="M:Sirenix.OdinInspector.IValueDropdownItem.GetValue">
            <summary>
            Gets the value of the dropdown item.
            </summary>
            <returns>The value for the item.</returns>
        </member>
        <member name="T:Sirenix.OdinInspector.ValueDropdownList`1">
            <summary>
            Use this with <see cref="T:Sirenix.OdinInspector.ValueDropdownAttribute"/> to specify custom names for values.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownList`1.Add(System.String,`0)">
            <summary>
            Adds the specified value with a custom name.
            </summary>
            <param name="text">The name of the item.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownList`1.Add(`0)">
            <summary>
            Adds the specified value.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.ValueDropdownItem">
            <summary>
            
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownItem.Text">
            <summary>
            The name of the item.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownItem.Value">
            <summary>
            The value of the item.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.ValueDropdownItem`1" /> class.
            </summary>
            <param name="text">The text to display for the dropdown item.</param>
            <param name="value">The value for the dropdown item.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem.ToString">
            <summary>
            The name of this item.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem.Sirenix#OdinInspector#IValueDropdownItem#GetText">
            <summary>
            Gets the text.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem.Sirenix#OdinInspector#IValueDropdownItem#GetValue">
            <summary>
            Gets the value.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ValueDropdownItem`1">
            <summary>
            
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownItem`1.Text">
            <summary>
            The name of the item.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ValueDropdownItem`1.Value">
            <summary>
            The value of the item.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem`1.#ctor(System.String,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.OdinInspector.ValueDropdownItem`1" /> class.
            </summary>
            <param name="text">The text to display for the dropdown item.</param>
            <param name="value">The value for the dropdown item.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem`1.Sirenix#OdinInspector#IValueDropdownItem#GetText">
            <summary>
            Gets the text.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem`1.Sirenix#OdinInspector#IValueDropdownItem#GetValue">
            <summary>
            Gets the value.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.ValueDropdownItem`1.ToString">
            <summary>
            The name of this item.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.VerticalGroupAttribute">
            <summary>
            <para>VerticalGroup is used to gather properties together in a vertical group in the inspector.</para>
            <para>This doesn't do much in and of itself, but in combination with other groups, such as <see cref="T:Sirenix.OdinInspector.HorizontalGroupAttribute"/> it can be very useful.</para>
            </summary>
            <example>
            <para>The following example demonstrates how VerticalGroup can be used in conjunction with <see cref="T:Sirenix.OdinInspector.HorizontalGroupAttribute"/></para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[HorizontalGroup("Split")]
            	[VerticalGroup("Split/Left")]
            	public Vector3 Vector;
            	
            	[VerticalGroup("Split/Left")]
            	public GameObject First;
            	
            	[VerticalGroup("Split/Left")]
            	public GameObject Second;
            	
            	[VerticalGroup("Split/Right", PaddingTop = 18f)]
            	public int A;
            	
            	[VerticalGroup("Split/Right")]
            	public int B;
            }
            </code>
            </example>
            <seealso cref="T:Sirenix.OdinInspector.HorizontalGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.BoxGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.TabGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ToggleGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.ButtonGroupAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.VerticalGroupAttribute.PaddingTop">
            <summary>
            Space in pixels at the top of the group.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.VerticalGroupAttribute.PaddingBottom">
            <summary>
            Space in pixels at the bottom of the group.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.VerticalGroupAttribute.#ctor(System.String,System.Single)">
            <summary>
            Groups properties vertically.
            </summary>
            <param name="groupId">The group ID.</param>
            <param name="order">The group order.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.VerticalGroupAttribute.#ctor(System.Single)">
            <summary>
            <para>Groups properties vertically.</para>
            <para>GroupId: _DefaultVerticalGroup</para>
            </summary>
            <param name="order">The group order.</param>
        </member>
        <member name="M:Sirenix.OdinInspector.VerticalGroupAttribute.CombineValuesWith(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Combines properties that have been group vertically.
            </summary>
            <param name="other">The group attribute to combine with.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.WrapAttribute">
            <summary>
            <para>Wrap is used on most primitive property, and allows for wrapping the value when it goes out of the defined range.</para>
            <para>Use this when you want a value that goes around in circle, like for example an angle.</para>
            </summary>
            <remarks>
            <note type="note">Currently unsigned primitives are not supported.</note>
            </remarks>
            <example>
            <para>The following example show how Wrap is used on a property.</para>
            <code>
            public class MyComponent : MonoBehaviour
            {
            	[Wrap(-100, 100)]
            	public float MyFloat;
            }
            </code>
            </example>
            <seealso cref="!:AngleWrapAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.WrapAttribute.Min">
            <summary>
            The lowest value for the property.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.WrapAttribute.Max">
            <summary>
            The highest value for the property.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.WrapAttribute.#ctor(System.Double,System.Double)">
            <summary>
            Wraps the value of the property round when the values goes out of range.
            </summary>
            <param name="min">The lowest value for the property.</param>
            <param name="max">The highest value for the property.</param>
        </member>
        <member name="T:Sirenix.OdinInspector.AttributeTargetFlags">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.AttributeTargetFlags.Default">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ButtonSizes">
            <summary>
            Various built-in button sizes.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonSizes.Small">
            <summary>
            Small button size, fits well with properties in the inspector.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonSizes.Medium">
            <summary>
            A larger button.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonSizes.Large">
            <summary>
            A very large button. 
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ButtonSizes.Gigantic">
            <summary>
            A gigantic button. Twice as big as Large 
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.DictionaryDisplayOptions">
            <summary>
            Various display modes for the dictionary to draw its items.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDisplayOptions.OneLine">
            <summary>
            Draws all dictionary items in two columns. The left column contains all key values, the right column displays all values.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDisplayOptions.Foldout">
            <summary>
            Draws each dictionary item in a box with the key in the header and the value inside the box.
            Whether or not the box is expanded or collapsed by default, is determined by the
            "Expand Foldout By Default" setting found in the preferences window "Tools > Odin > Inspector > Preferences > Drawers > Settings".
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDisplayOptions.CollapsedFoldout">
            <summary>
            Draws each dictionary item in a collapsed foldout with the key in the header and the value inside the box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.DictionaryDisplayOptions.ExpandedFoldout">
            <summary>
            Draws each dictionary item in an expanded foldout with the key in the header and the value inside the box.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.IncludeMyAttributesAttribute">
            <summary>
            When this attribute is added is added to another attribute, then attributes from that attribute
            will also be added to the property in the attribute processing step.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.InfoMessageType">
            <summary>
            Type of info message box. This enum matches Unity's MessageType enum which could not be used since it is located in the UnityEditor assembly.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoMessageType.None">
            <summary>
            Generic message box with no type.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoMessageType.Info">
            <summary>
            Information message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoMessageType.Warning">
            <summary>
            Warning message box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InfoMessageType.Error">
            <summary>
            Error message box.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.InlineEditorModes">
            <summary>
            Editor modes for <see cref="T:Sirenix.OdinInspector.InlineEditorAttribute" />
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.InlineEditorAttribute" />
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.GUIOnly">
            <summary>
            Draws only the editor GUI
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.GUIAndHeader">
            <summary>
            Draws the editor GUI and the editor header.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.GUIAndPreview">
            <summary>
            Draws the editor GUI to the left, and a small editor preview to the right.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.SmallPreview">
            <summary>
            Draws a small editor preview without any GUI.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.LargePreview">
            <summary>
            Draws a large editor preview without any GUI.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorModes.FullEditor">
            <summary>
            Draws the editor header and GUI to the left, and a small editor preview to the right.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.InlineEditorObjectFieldModes">
            <summary>
            How the InlineEditor attribute drawer should draw the object field.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorObjectFieldModes.Boxed">
            <summary>
            Draws the object field in a box.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorObjectFieldModes.Foldout">
            <summary>
            Draws the object field with a foldout.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorObjectFieldModes.Hidden">
            <summary>
            Hides the object field unless it's null.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.InlineEditorObjectFieldModes.CompletelyHidden">
            <summary>
            Hidden the object field also when the object is null.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.ISearchFilterable">
            <summary>
            Implement this interface to create custom matching
            logic for search filtering in the inspector.
            </summary>
            <example>
            <para>The following example shows how you might do this:</para>
            <code>
            public class MyCustomClass : ISearchFilterable
            {
                public bool SearchEnabled;
                public string MyStr;
                
                public bool IsMatch(string searchString)
                {
                    if (SearchEnabled)
                    {
                        return MyStr.Contains(searchString);
                    }
                    
                    return false;
                }
            }
            </code>
            </example>
        </member>
        <member name="T:Sirenix.OdinInspector.ISelfValidator">
            <summary>
            Any type implementing this interface will be considered to be validating itself using the implemented logic, as if a custom validator had been written for it.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.Internal.ISubGroupProviderAttribute">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.Internal.ISubGroupProviderAttribute.GetSubGroupAttributes">
            <summary>
            Not yet documented.
            </summary>
            <returns>Not yet documented.</returns>
        </member>
        <member name="M:Sirenix.OdinInspector.Internal.ISubGroupProviderAttribute.RepathMemberAttribute(Sirenix.OdinInspector.PropertyGroupAttribute)">
            <summary>
            Not yet documented.
            </summary>
            <param name="attr">Not yet documented.</param>
            <returns>Not yet documented.</returns>
        </member>
        <member name="T:Sirenix.OdinInspector.ObjectFieldAlignment">
            <summary>
            How the square object field should be aligned.
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.PreviewFieldAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.ObjectFieldAlignment.Left">
            <summary>
            Left aligned.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ObjectFieldAlignment.Center">
            <summary>
            Aligned to the center.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.ObjectFieldAlignment.Right">
            <summary>
            Right aligned.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.PrefabKind">
            <summary>
            The prefab kind returned by <see cref="!:Sirenix.OdinInspector.Editor.OdinPrefabUtility.GetPrefabKind"/> 
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.None">
            <summary>
            None. 
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.InstanceInScene">
            <summary>
            Instances of prefabs in scenes.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.InstanceInPrefab">
            <summary>
            Instances of prefabs nested inside other prefabs.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.Regular">
            <summary>
            Regular prefab assets.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.Variant">
            <summary>
            Prefab variant assets.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.NonPrefabInstance">
            <summary>
            Non-prefab component or gameobject instances in scenes.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.PrefabInstance">
            <summary>
            Instances of regular prefabs, and prefab variants in scenes or nested in other prefabs.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.PrefabAsset">
            <summary>
            Prefab assets and prefab variant assets.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.PrefabInstanceAndNonPrefabInstance">
            <summary>
            Prefab Instances, as well as non-prefab instances.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.PrefabKind.All">
            <summary>
            All kinds
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.SearchFilterOptions">
            <summary>
            Options for filtering search.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.TitleAlignments">
            <summary>
            Title alignment enum used by various attributes.
            </summary>
            <seealso cref="T:Sirenix.OdinInspector.TitleGroupAttribute"/>
            <seealso cref="T:Sirenix.OdinInspector.TitleAttribute"/>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAlignments.Left">
            <summary>
            Title and subtitle left aligned.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAlignments.Centered">
            <summary>
            Title and subtitle centered aligned.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAlignments.Right">
            <summary>
            Title and subtitle right aligned.
            </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TitleAlignments.Split">
            <summary>
            Title on the left, subtitle on the right.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.TypeInclusionFilter">
            <summary> Specifies the types to include based on certain criteria. </summary>
        </member>
        <member name="F:Sirenix.OdinInspector.TypeInclusionFilter.IncludeConcreteTypes">
            <summary> Represents types that are not interfaces, abstracts, or generics. </summary>
        </member>
    </members>
</doc>
