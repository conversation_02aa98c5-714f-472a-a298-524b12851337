﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    [Black]
    public class SpriteTileLayerView : TileLayerView
    {
        SpriteTileLayerView(MapLayerData layerData, Vector2 spriteAnchor)
            : base(layerData)
        {

            var map = Map.currentMap;
            var spriteTileLayerData = layerData as SpriteTileLayerData;
            mTileViewPool = new SpritePool(layerData, spriteAnchor);
            mTileViewPool.root.transform.SetParent(root.transform, true);
        }

        public static SpriteTileLayerView Create(MapLayerData layerData, Vector2 spriteAnchor)
        {
            var view = new SpriteTileLayerView(layerData, spriteAnchor);
            return view;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            mTileViewPool.OnDestroy();
            mTileViewPool = null;
        }

        public override void SetZoom(float zoom, bool lodChanged)
        {
        }

        public void ChangeMaterialColor(Color32 oldColor, Color32 newColor)
        {
            if (mTileViewPool is SpritePool)
            {
                var pool = mTileViewPool as SpritePool;
                pool.ChangeMaterialColor(oldColor, newColor);
            }
        }

        protected override TileView CreateTileView(TileData data) {
            var spriteData = data as SpriteTileData;

            bool skipColorSprite = true;
#if UNITY_EDITOR
            if (!EditorApplication.isPlaying)
            {
                //只在编辑器模式下绘制color tile
                skipColorSprite = false;
            }
#endif

            if (skipColorSprite)
            {
                return new DummySpriteTileView(this);
            }

            var spriteTileLayer = layerData as SpriteTileLayerData;
            GameObject gameObject = null;
            gameObject = mTileViewPool.Require(spriteData.spriteTemplateID);

            gameObject.transform.SetParent(root.transform, true);
            gameObject.SetActive(false);
            var tileView = new SpriteTileView(this, gameObject);
            return tileView;
        }

        protected override void OnReleaseTileView(int x, int y, TileView view)
        {
            if (view is DummySpriteTileView)
            {
            }
            else
            {
                var tileView = view as SpriteTileView;
                var layerData = mLayerData as SpriteTileLayerData;
                var tileData = layerData.GetTile(x, y) as SpriteTileData;
                mTileViewPool.Release(tileData.spriteTemplateID, tileView.gameObject);
            }
        }

        protected override void ShowTileInternal(TileView view, int x, int y, TileData data)
        {
            var spriteTileView = view as SpriteTileView;
            var spriteTileData = data as SpriteTileData;
            var gameObject = spriteTileView.gameObject;

            if (gameObject != null)
            {
                //temp code
                int layerIndex = 0;
                if (spriteTileData != null && spriteTileData.spriteTemplateID != 0)
                {

                    string name = string.Format("tiles_{0}_{1}", x, y);
                    gameObject.name = name;

                    var position = mLayerData.FromCoordinateToWorldPositionCenter(x, y);
                    position.y = 0.2f;
                    gameObject.transform.position = position;
                    gameObject.SetActive(true);
                    var spriteRenderer = gameObject.GetComponent<SpriteRenderer>();
                    if (spriteRenderer)
                    {
                        spriteRenderer.sortingOrder = layerIndex;
                    }
                    else
                    {
                        var meshRenderer = gameObject.GetComponent<MeshRenderer>();
                        meshRenderer.sortingOrder = layerIndex;
                    }
                }
                else
                {
                    gameObject.SetActive(false);
                }
            }
        }

        public override void ReloadVisibleViews()
        {
        }

        ISpritePool mTileViewPool;
    }
}

#endif