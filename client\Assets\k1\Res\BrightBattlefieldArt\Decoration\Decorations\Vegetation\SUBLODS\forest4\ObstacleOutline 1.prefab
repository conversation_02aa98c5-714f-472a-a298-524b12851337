%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &9076287043318694807
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1820846823286421677}
  - component: {fileID: 744892160466315487}
  m_Layer: 0
  m_Name: ObstacleOutline 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1820846823286421677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9076287043318694807}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &744892160466315487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9076287043318694807}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: 8.587603, y: 0, z: -41.64999}
    - {x: 16.127611, y: 0, z: -28.936295}
    - {x: 52.933266, y: 0, z: -22.177961}
    - {x: 67.58623, y: 0, z: -0.63718414}
    - {x: 90, y: 0, z: -5.350443}
    - {x: 90, y: 0, z: -14.389948}
    - {x: 57.867233, y: 0, z: -43.627365}
    - {x: 17.919724, y: 0, z: -50.965656}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: 19.7151, y: 0, z: -52.5848}
    - {x: 19.189, y: 0, z: -52.5842}
    - {x: 18.573, y: 0, z: -52.4745}
    - {x: 18.2432, y: 0, z: -52.3758}
    - {x: 15.9779, y: 0, z: -51.4035}
    - {x: 15.7739, y: 0, z: -51.2968}
    - {x: 14.0325, y: 0, z: -50.2079}
    - {x: 13.8057, y: 0, z: -50.0339}
    - {x: 12.3525, y: 0, z: -48.6822}
    - {x: 12.3472, y: 0, z: -48.6772}
    - {x: 12.0703, y: 0, z: -48.4171}
    - {x: 11.98, y: 0, z: -48.3247}
    - {x: 8.0895, y: 0, z: -43.9831}
    - {x: 7.6934, y: 0, z: -42.8741}
    - {x: 7.8508, y: 0, z: -39.3463}
    - {x: 8.049, y: 0, z: -38.6605}
    - {x: 8.7733, y: 0, z: -37.3903}
    - {x: 8.7749, y: 0, z: -37.3876}
    - {x: 13.4721, y: 0, z: -29.1906}
    - {x: 13.515, y: 0, z: -29.1203}
    - {x: 14.1518, y: 0, z: -28.1382}
    - {x: 14.7064, y: 0, z: -27.6264}
    - {x: 66.291, y: 0, z: 0.2225}
    - {x: 66.9576, y: 0, z: 0.4039}
    - {x: 73.9089, y: 0, z: 0.6575}
    - {x: 74.2638, y: 0, z: 0.6283}
    - {x: 86.7837, y: 0, z: -1.9183}
    - {x: 87.4479, y: 0, z: -2.2349}
    - {x: 90, y: 0, z: -4.6532}
    - {x: 90, y: 0, z: -4.9026}
    - {x: 90, y: 0, z: -8.6591}
    - {x: 90, y: 0, z: -9.3431}
    - {x: 90, y: 0, z: -12.1209}
    - {x: 90, y: 0, z: -13.3602}
    - {x: 90, y: 0, z: -16.0781}
    - {x: 90, y: 0, z: -16.1145}
    - {x: 87.4862, y: 0, z: -20.878}
    - {x: 87.3403, y: 0, z: -21.0238}
    - {x: 67.814, y: 0, z: -38.0231}
    - {x: 67.8094, y: 0, z: -38.0271}
    - {x: 59.2314, y: 0, z: -45.4337}
    - {x: 58.497, y: 0, z: -45.7821}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
