﻿ 



 
 

using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;

namespace TFW.Map
{
    public enum GlobalAnimationBlendingState
    {
        //不采用全局设置.每个prefab使用其烘培时的设置
        Off,
        //所有动画都启用插值混合
        EnableAnimationBlending,
        //所有动画都不启用插值混合
        DisableAnimationBlending,
    }

    public class AnimatorWrapper : MonoBehaviour
    {
        //debug id
        //public int debugID;

        AnimatorBase mAnimator;
        public bool useCustomAnimator = false;
        public bool useVertexAnimator = false;
        [SerializeField]
        BakedAnimationDataBase mAnimationData;
        [SerializeField]
        string mAnimationDataPath;
        static GlobalAnimationBlendingState mGlobalAnimationBlendingState = GlobalAnimationBlendingState.Off;

        void Awake()
        {
            if (useCustomAnimator)
            {
                if (useVertexAnimator)
                {
                    mAnimator = new CustomVertexAnimator(gameObject);
                }
                else
                {
                    mAnimator = new CustomAnimator(gameObject);
                }
            }
            else
            {
                mAnimator = new UnityAnimator(gameObject);
            }
            mAnimator?.OnEnable();
        }

        void OnDisable()
        {
            mAnimator?.OnDisable();
        }

        void OnDestroy()
        {
            mAnimator?.OnDestroy();
        }

        public void InitInEditor(bool useCustomAnimator, bool useVertexAnimator, GameObject gameObject)
        {
            this.useVertexAnimator = useVertexAnimator;
            if (this.useCustomAnimator != useCustomAnimator)
            {
                this.useCustomAnimator = useCustomAnimator;
                if (useCustomAnimator)
                {
                    if (useVertexAnimator)
                    {
                        mAnimator = new CustomVertexAnimator(gameObject);
                    }
                    else
                    {
                        mAnimator = new CustomAnimator(gameObject);
                    }
                }
                else
                {
                    mAnimator = new UnityAnimator(gameObject);
                }
            }
        }

        public void InitInGame()
        {
            mAnimator = new UnityAnimator(gameObject);
            mAnimator.OnEnable();
        }

        public void RegisterAnimationEventCallback(System.Action<string, string> stringEventCallback, System.Action<string, int> intEventCallback, System.Action<string, float> floatEventCallback)
        {
            mAnimator?.RegisterAnimationEventCallback(stringEventCallback, intEventCallback, floatEventCallback);
        }

        public void UnregisterAnimationEventCallback()
        {
            mAnimator?.UnregisterAnimationEventCallback();
        }
        public void UnregisterAnimationIntEventCallback()
        {
            mAnimator?.UnregisterAnimationIntEventCallback();
        }
        public void UnregisterAnimationFloatEventCallback()
        {
            mAnimator?.UnregisterAnimationFloatEventCallback();
        }
        public void UnregisterAnimationStringEventCallback()
        {
            mAnimator?.UnregisterAnimationStringEventCallback();
        }

        void Update()
        {
            bool blendStateChanged = mAnimator.Update(mGlobalAnimationBlendingState);
            if (useCustomAnimator && blendStateChanged)
            {
                ApplyAnimationBlendingStateChange(mAnimator.enableAnimationBlending);
            }
        }

        public void SetAnimationData(BakedAnimationDataBase data, string path)
        {
            mAnimationData = data;
            mAnimationDataPath = path;
        }

        //是否开启全局animation blending,如果off,则使用烘培时的设置
        public static void SetGlobalAnimationBlendingState(GlobalAnimationBlendingState state)
        {
            mGlobalAnimationBlendingState = state;
        }

        //当状态改变时启用keyword
        void ApplyAnimationBlendingStateChange(bool enable)
        {
            var tags = GetComponentsInChildren<RendererTag>(true);
            for (int i = 0; i < tags.Length; ++i)
            {
                var renderer = tags[i].GetComponent<MeshRenderer>();
                var sharedMaterials = renderer.sharedMaterials;
                for (int k = 0; k < sharedMaterials.Length; ++k)
                {
                    if (enable)
                    {
                        sharedMaterials[k].EnableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                    }
                    else
                    {
                        sharedMaterials[k].DisableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                    }
                }
            }
        }

        public string[] GetAnimationNames() { return mAnimator.GetAnimationNames(); }
        public bool IsPlayingAnimation(string animStateName) { return mAnimator.IsPlayingAnimation(animStateName); }
        public float GetCurrentNormalizedTime() { return mAnimator.GetCurrentNormalizedTime(); }
        public string GetCurrentStateName() { return mAnimator.GetCurrentStateName(); }
        public string GetNextStateName() { return mAnimator.GetNextStateName(); }

        public AnimatorBase animator { get { return mAnimator; } }
        public string animationDataPath { get { return mAnimationDataPath; } }
        public BakedAnimationDataBase animationData { get { return mAnimationData; } }

        public bool stabilizeFeet { get { return mAnimator.stabilizeFeet; } set { mAnimator.stabilizeFeet = value; } }
        public Quaternion bodyRotation { get { return mAnimator.bodyRotation; } set { mAnimator.bodyRotation = value; } }
        public Vector3 bodyPosition { get { return mAnimator.bodyPosition; } set { mAnimator.bodyPosition = value; } }
        public float gravityWeight { get { return mAnimator.gravityWeight; } }
        public bool hasTransformHierarchy { get { return mAnimator.hasTransformHierarchy; } }
        public AnimatorUpdateMode updateMode { get { return mAnimator.updateMode; } set { mAnimator.updateMode = value; } }
        public bool applyRootMotion { get { return mAnimator.applyRootMotion; } set { mAnimator.applyRootMotion = value; } }
        public Quaternion rootRotation { get { return mAnimator.rootRotation; } set { mAnimator.rootRotation = value; } }
        public Vector3 rootPosition { get { return mAnimator.rootPosition; } set { mAnimator.rootPosition = value; } }
        public Vector3 angularVelocity { get { return mAnimator.angularVelocity; } }
        public Vector3 velocity { get { return mAnimator.velocity; } }
        public Quaternion deltaRotation { get { return mAnimator.deltaRotation; } }
        public Vector3 deltaPosition { get { return mAnimator.deltaPosition; } }
        public bool isInitialized { get { return mAnimator.isInitialized; } }
        public float humanScale { get { return mAnimator.humanScale; } }
        public bool hasRootMotion { get { return mAnimator.hasRootMotion; } }
        public bool isHuman { get { return mAnimator.isHuman; } }
        public int layerCount { get { return mAnimator.layerCount; } }
        public bool isOptimizable { get { return mAnimator.isOptimizable; } }
        public AnimationParameterInfo[] parameters
        {
            get
            {
                return mAnimator.parameters;
            }
        }
        public float feetPivotActive { get { return mAnimator.feetPivotActive; } set { mAnimator.feetPivotActive = value; } }
        public bool logWarnings { get { return mAnimator.logWarnings; } set { mAnimator.logWarnings = value; } }
        public float rightFeetBottomHeight { get { return mAnimator.rightFeetBottomHeight; } }
        public float leftFeetBottomHeight { get { return mAnimator.leftFeetBottomHeight; } }
        public bool layersAffectMassCenter { get { return mAnimator.layersAffectMassCenter; } set { mAnimator.layersAffectMassCenter = value; } }
        public PlayableGraph playableGraph { get { return mAnimator.playableGraph; } }
        public Avatar avatar { get { return mAnimator.avatar; } set { mAnimator.avatar = value; } }
        public bool hasBoundPlayables { get { return mAnimator.hasBoundPlayables; } }
        public RuntimeAnimatorController runtimeAnimatorController { get { return mAnimator.runtimeAnimatorController; } set { mAnimator.runtimeAnimatorController = value; } }
        public AnimatorRecorderMode recorderMode { get { return mAnimator.recorderMode; } }
        public float recorderStopTime { get { return mAnimator.recorderStopTime; } set { mAnimator.recorderStopTime = value; } }
        public float recorderStartTime { get { return mAnimator.recorderStartTime; } set { mAnimator.recorderStartTime = value; } }
        public float playbackTime { get { return mAnimator.playbackTime; } set { mAnimator.playbackTime = value; } }
        public AnimatorCullingMode cullingMode { get { return mAnimator.cullingMode; } set { mAnimator.cullingMode = value; } }
        public Quaternion targetRotation { get { return mAnimator.targetRotation; } }
        public Vector3 targetPosition { get { return mAnimator.targetPosition; } }
        public float speed { get { return mAnimator.speed; } set { mAnimator.speed = value; } }
        public bool isMatchingTarget { get { return mAnimator.isMatchingTarget; } }
        public Vector3 pivotPosition { get { return mAnimator.pivotPosition; } }
        public float pivotWeight { get { return mAnimator.pivotWeight; } }
        public int parameterCount { get { return mAnimator.parameterCount; } }
        public bool fireEvents { get { return mAnimator.fireEvents; } set { mAnimator.fireEvents = value; } }
        public bool keepAnimatorControllerStateOnDisable { get { return mAnimator.keepAnimatorControllerStateOnDisable; } set { mAnimator.keepAnimatorControllerStateOnDisable = value; } }
        public InterruptionType interruptionType { get { return mAnimator.interruptionType; } set { mAnimator.interruptionType = value; } }
        public void ApplyBuiltinRootMotion()
        {
            mAnimator.ApplyBuiltinRootMotion();
        }
        public void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer, normalizedTimeOffset, normalizedTransitionTime);
        }
        public void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer, normalizedTimeOffset);
        }
        public void CrossFade(string stateName, float normalizedTransitionDuration)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration);
        }
        public void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer, normalizedTimeOffset, normalizedTransitionTime);
        }
        public void CrossFade(string stateName, float normalizedTransitionDuration, int layer)
        {
            mAnimator.CrossFade(stateName, normalizedTransitionDuration, layer);
        }
        public void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer);
        }
        public void CrossFade(int stateHashName, float normalizedTransitionDuration)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration);
        }
        public void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset)
        {
            mAnimator.CrossFade(stateHashName, normalizedTransitionDuration, layer, normalizedTimeOffset);
        }
        public void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer, fixedTimeOffset);
        }
        public void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer, fixedTimeOffset, normalizedTransitionTime);
        }
        public void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration);
        }
        public void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer);
        }
        public void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset)
        {
            mAnimator.CrossFadeInFixedTime(stateHashName, fixedTransitionDuration, layer, fixedTimeOffset);
        }
        public void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer, fixedTimeOffset, normalizedTransitionTime);
        }
        public void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration, layer);
        }
        public void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration)
        {
            mAnimator.CrossFadeInFixedTime(stateName, fixedTransitionDuration);
        }
        public AnimatorTransitionInfo GetAnimatorTransitionInfo(int layerIndex)
        {
            return mAnimator.GetAnimatorTransitionInfo(layerIndex);
        }
        public T GetBehaviour<T>() where T : StateMachineBehaviour
        {
            return mAnimator.GetBehaviour<T>();
        }
        public StateMachineBehaviour[] GetBehaviours(int fullPathHash, int layerIndex)
        {
            return mAnimator.GetBehaviours(fullPathHash, layerIndex);
        }
        public T[] GetBehaviours<T>() where T : StateMachineBehaviour
        {
            return mAnimator.GetBehaviours<T>();
        }
        public Transform GetBoneTransform(HumanBodyBones humanBoneId)
        {
            return mAnimator.GetBoneTransform(humanBoneId);
        }
        public bool GetBool(string name)
        {
            return mAnimator.GetBool(name);
        }
        public bool GetBool(int id)
        {
            return mAnimator.GetBool(id);
        }
        public AnimatorClipInfo[] GetCurrentAnimatorClipInfo(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorClipInfo(layerIndex);
        }
        public void GetCurrentAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips)
        {
            mAnimator.GetCurrentAnimatorClipInfo(layerIndex, clips);
        }
        public int GetCurrentAnimatorClipInfoCount(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorClipInfoCount(layerIndex);
        }
        public AnimatorStateInfo GetCurrentAnimatorStateInfo(int layerIndex)
        {
            return mAnimator.GetCurrentAnimatorStateInfo(layerIndex);
        }
        public float GetFloat(int id)
        {
            return mAnimator.GetFloat(id);
        }
        public float GetFloat(string name)
        {
            return mAnimator.GetFloat(name);
        }
        public Vector3 GetIKHintPosition(AvatarIKHint hint)
        {
            return mAnimator.GetIKHintPosition(hint);
        }
        public float GetIKHintPositionWeight(AvatarIKHint hint)
        {
            return mAnimator.GetIKHintPositionWeight(hint);
        }
        public Vector3 GetIKPosition(AvatarIKGoal goal)
        {
            return mAnimator.GetIKPosition(goal);
        }
        public float GetIKPositionWeight(AvatarIKGoal goal)
        {
            return mAnimator.GetIKPositionWeight(goal);
        }
        public Quaternion GetIKRotation(AvatarIKGoal goal)
        {
            return mAnimator.GetIKRotation(goal);
        }
        public float GetIKRotationWeight(AvatarIKGoal goal)
        {
            return mAnimator.GetIKRotationWeight(goal);
        }
        public int GetInteger(string name)
        {
            return mAnimator.GetInteger(name);
        }
        public int GetInteger(int id)
        {
            return mAnimator.GetInteger(id);
        }
        public int GetLayerIndex(string layerName)
        {
            return mAnimator.GetLayerIndex(layerName);
        }
        public string GetLayerName(int layerIndex)
        {
            return mAnimator.GetLayerName(layerIndex);
        }
        public float GetLayerWeight(int layerIndex)
        {
            return mAnimator.GetLayerWeight(layerIndex);
        }
        public AnimatorClipInfo[] GetNextAnimatorClipInfo(int layerIndex)
        {
            return mAnimator.GetNextAnimatorClipInfo(layerIndex);
        }
        public void GetNextAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips)
        {
            mAnimator.GetNextAnimatorClipInfo(layerIndex, clips);
        }
        public int GetNextAnimatorClipInfoCount(int layerIndex)
        {
            return mAnimator.GetNextAnimatorClipInfoCount(layerIndex);
        }
        public AnimatorStateInfo GetNextAnimatorStateInfo(int layerIndex)
        {
            return mAnimator.GetNextAnimatorStateInfo(layerIndex);
        }
        public AnimationParameterInfo GetParameter(int index)
        {
            return mAnimator.GetParameter(index);
        }
        public bool HasState(int layerIndex, int stateID)
        {
            return mAnimator.HasState(layerIndex, stateID);
        }
        public void InterruptMatchTarget()
        {
            mAnimator.InterruptMatchTarget();
        }
        public void InterruptMatchTarget(bool completeMatch)
        {
            mAnimator.InterruptMatchTarget(completeMatch);
        }
        public bool IsInTransition(int layerIndex)
        {
            return mAnimator.IsInTransition(layerIndex);
        }
        public bool IsParameterControlledByCurve(int id)
        {
            return mAnimator.IsParameterControlledByCurve(id);
        }
        public bool IsParameterControlledByCurve(string name)
        {
            return mAnimator.IsParameterControlledByCurve(name);
        }
        public void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime)
        {
            mAnimator.MatchTarget(matchPosition, matchRotation, targetBodyPart, weightMask, startNormalizedTime);
        }
        public void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime, float targetNormalizedTime)
        {
            mAnimator.MatchTarget(matchPosition, matchRotation, targetBodyPart, weightMask, startNormalizedTime, targetNormalizedTime);
        }
        public void Play(string stateName, int layer)
        {
            mAnimator.Play(stateName, layer);
        }
        public void Play(int stateNameHash, int layer, float normalizedTime)
        {
            mAnimator.Play(stateNameHash, layer, normalizedTime);
        }
        public void Play(string stateName, int layer, float normalizedTime)
        {
            mAnimator.Play(stateName, layer, normalizedTime);
        }
        public void Play(string stateName)
        {
            mAnimator.Play(stateName);
        }
        public void Play(int stateNameHash)
        {
            mAnimator.Play(stateNameHash);
        }
        public void Play(int stateNameHash, int layer)
        {
            mAnimator.Play(stateNameHash, layer);
        }
        public void PlayInFixedTime(int stateNameHash, int layer, float fixedTime)
        {
            mAnimator.PlayInFixedTime(stateNameHash, layer, fixedTime);
        }
        public void PlayInFixedTime(string stateName, int layer, float fixedTime)
        {
            mAnimator.PlayInFixedTime(stateName, layer, fixedTime);
        }
        public void PlayInFixedTime(int stateNameHash)
        {
            mAnimator.PlayInFixedTime(stateNameHash);
        }
        public void PlayInFixedTime(int stateNameHash, int layer)
        {
            mAnimator.PlayInFixedTime(stateNameHash, layer);
        }
        public void PlayInFixedTime(string stateName, int layer)
        {
            mAnimator.PlayInFixedTime(stateName, layer);
        }
        public void PlayInFixedTime(string stateName)
        {
            mAnimator.PlayInFixedTime(stateName);
        }
        public void Rebind()
        {
            mAnimator.Rebind();
        }
        public void ResetTrigger(string name)
        {
            mAnimator.ResetTrigger(name);
        }
        public void ResetTrigger(int id)
        {
            mAnimator.ResetTrigger(id);
        }
        public void SetBoneLocalRotation(HumanBodyBones humanBoneId, Quaternion rotation)
        {
            mAnimator.SetBoneLocalRotation(humanBoneId, rotation);
        }
        public void SetBool(string name, bool value)
        {
            mAnimator.SetBool(name, value);
        }
        public void SetBool(int id, bool value)
        {
            mAnimator.SetBool(id, value);
        }
        public void SetFloat(int id, float value)
        {
            mAnimator.SetFloat(id, value);
        }
        public void SetFloat(string name, float value, float dampTime, float deltaTime)
        {
            mAnimator.SetFloat(name, value, dampTime, deltaTime);
        }
        public void SetFloat(string name, float value)
        {
            mAnimator.SetFloat(name, value);
        }
        public void SetFloat(int id, float value, float dampTime, float deltaTime)
        {
            mAnimator.SetFloat(id, value, dampTime, deltaTime);
        }
        public void SetIKHintPosition(AvatarIKHint hint, Vector3 hintPosition)
        {
            mAnimator.SetIKHintPosition(hint, hintPosition);
        }
        public void SetIKHintPositionWeight(AvatarIKHint hint, float value)
        {
            mAnimator.SetIKHintPositionWeight(hint, value);
        }
        public void SetIKPosition(AvatarIKGoal goal, Vector3 goalPosition)
        {
            mAnimator.SetIKPosition(goal, goalPosition);
        }
        public void SetIKPositionWeight(AvatarIKGoal goal, float value)
        {
            mAnimator.SetIKPositionWeight(goal, value);
        }
        public void SetIKRotation(AvatarIKGoal goal, Quaternion goalRotation)
        {
            mAnimator.SetIKRotation(goal, goalRotation);
        }
        public void SetIKRotationWeight(AvatarIKGoal goal, float value)
        {
            mAnimator.SetIKRotationWeight(goal, value);
        }
        public void SetInteger(int id, int value)
        {
            mAnimator.SetInteger(id, value);
        }
        public void SetInteger(string name, int value)
        {
            mAnimator.SetInteger(name, value);
        }
        public void SetLayerWeight(int layerIndex, float weight)
        {
            mAnimator.SetLayerWeight(layerIndex, weight);
        }
        public void SetLookAtPosition(Vector3 lookAtPosition)
        {
            mAnimator.SetLookAtPosition(lookAtPosition);
        }
        public void SetLookAtWeight(float weight)
        {
            mAnimator.SetLookAtWeight(weight);
        }
        public void SetLookAtWeight(float weight, float bodyWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight);
        }
        public void SetLookAtWeight(float weight, float bodyWeight, float headWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight);
        }
        public void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight, eyesWeight);
        }
        public void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight, float clampWeight)
        {
            mAnimator.SetLookAtWeight(weight, bodyWeight, headWeight, eyesWeight, clampWeight);
        }
        public void SetTarget(AvatarTarget targetIndex, float targetNormalizedTime)
        {
            mAnimator.SetTarget(targetIndex, targetNormalizedTime);
        }
        public void SetTrigger(int id)
        {
            mAnimator.SetTrigger(id);
        }
        public void SetTrigger(string name)
        {
            mAnimator.SetTrigger(name);
        }
        public void StartPlayback()
        {
            mAnimator.StartPlayback();
        }
        public void StartRecording(int frameCount)
        {
            mAnimator.StartRecording(frameCount);
        }
        public void StopPlayback()
        {
            mAnimator.StopPlayback();
        }
        public void StopRecording()
        {
            mAnimator.StopRecording();
        }
        public void UpdateAnimator(float deltaTime)
        {
            mAnimator.UpdateAnimator(deltaTime);
        }
        public void WriteDefaultValues()
        {
            mAnimator.WriteDefaultValues();
        }
    }
}
