﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public enum MapTouchState
    {
        //手指第一次接触到屏幕
        Touch,
        //手指正在接触屏幕
        Touching,
        //手指离开屏幕
        Release,
    }

    public interface IMapTouch
    {
        //当前touch的位置
        Vector2 position { get; }
        Vector2 lastPosition { get; }
        //touch第一次接触屏幕时的位置
        Vector2 touchPosition { get; }
        MapTouchState state { get; }
        IMapTouchHistory history { get; }
        bool isMoved { get; }
        bool alive { get; set; }
        int fingerId { get; }
        Vector2 scrollDelta { get; }

        //是否屏蔽ui点击
        bool blockUIClick { get; set; }
        //是否屏蔽ui拖动
        bool blockUIDrag { get; set; }

        void Reset();
    }

    public interface IMapTouchHistory
    {
        bool AddPoint(Vector2 pt);
        void SetCurPos(Vector2 pt);
        Vector2 GetDelta(Vector2 alongDir);
        void Clear();

        Vector2 previous { get; }
        Vector2 current { get; }
        Vector2 startPosition { get; }
        bool isMoved { get; }
    }

    public interface IMapTouchManager
    {
        int touchCount { get; }
        int deviceTouchCount { get; }
        IMapTouch GetTouch(int index);
        void Update();
    }

    public interface IMapTouchHijacker
    {
        int GetTouchCount(IMapTouchManager touchManager);
        IMapTouch GetTouch(IMapTouchManager touchManager, int index);
        void Update(IMapTouchManager touchManager);
    }

    class DummyTouchManagerImpl : IMapTouchManager
    {
        public int touchCount { get { return 0; } }
        public int deviceTouchCount { get { return 0; } }
        public IMapTouch GetTouch(int index) { return null; }
        public void Update() { }
    }

    public static class MapTouchManager
    {
        public static void SetTouchManagerImpl(IMapTouchManager impl)
        {
            mTouchManagerImpl = impl;
        }

        public static void SetHijacker(IMapTouchHijacker hijacker)
        {
            mHijacker = hijacker;
        }

        public static IMapTouchHijacker GetHighjacker()
        {
            return mHijacker;
        }

        public static IMapTouch GetTouch(int index) 
        { 
            if (mHijacker != null)
            {
                return mHijacker.GetTouch(mTouchManagerImpl, index);
            }
            return mTouchManagerImpl.GetTouch(index); 
        }
        public static int touchCount 
        { 
            get 
            {
                if (mHijacker != null)
                {
                    return mHijacker.GetTouchCount(mTouchManagerImpl);
                }
                return mTouchManagerImpl.touchCount; 
            }
        }

        //unity接口返回的touch count
        public static int deviceTouchCount
        {
            get
            {
                return mTouchManagerImpl.deviceTouchCount;
            }
        }

        public static void Update()
        {
            if (mHijacker != null)
            {
                mHijacker.Update(mTouchManagerImpl);
            }
        }

        static IMapTouchManager mTouchManagerImpl;
        static IMapTouchHijacker mHijacker;
    }
}
