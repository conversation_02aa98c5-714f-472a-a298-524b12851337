%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1722176227783950271
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4137343535880652896}
  - component: {fileID: 6746196684376490223}
  m_Layer: 0
  m_Name: ObstacleOutline 3
  m_TagString: ignore
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4137343535880652896
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1722176227783950271}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6746196684376490223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1722176227783950271}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 39e3c02bf9f223d4799d2aeeb886be95, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mOutlineData:
  - outline:
    - {x: -57.24794, y: 0, z: -17.876694}
    - {x: -51.745075, y: 0, z: -8.718639}
    - {x: -35.539433, y: 0, z: -5.969021}
    - {x: -32.12883, y: 0, z: -11.621101}
    - {x: -38.74081, y: 0, z: -19.081451}
    - {x: -48.34624, y: 0, z: -23.453278}
    isSimplePolygon: 1
    isConvex: 1
  - outline:
    - {x: -48.0796, y: 0, z: -25.4065}
    - {x: -48.8502, y: 0, z: -25.385}
    - {x: -51.3534, y: 0, z: -24.6558}
    - {x: -51.5066, y: 0, z: -24.602}
    - {x: -52.6621, y: 0, z: -24.1248}
    - {x: -52.8969, y: 0, z: -24.0026}
    - {x: -57.4286, y: 0, z: -21.1109}
    - {x: -57.7069, y: 0, z: -20.8821}
    - {x: -58.8123, y: 0, z: -19.7259}
    - {x: -59.2234, y: 0, z: -18.3241}
    - {x: -58.6963, y: 0, z: -15.6182}
    - {x: -58.6267, y: 0, z: -15.3734}
    - {x: -57.929, y: 0, z: -13.5303}
    - {x: -57.791, y: 0, z: -13.255}
    - {x: -55.8133, y: 0, z: -10.1489}
    - {x: -55.6813, y: 0, z: -9.9719}
    - {x: -53.161, y: 0, z: -7.064}
    - {x: -52.4987, y: 0, z: -6.6184}
    - {x: -51.2484, y: 0, z: -6.1935}
    - {x: -50.987, y: 0, z: -6.1301}
    - {x: -37.9133, y: 0, z: -4.1758}
    - {x: -37.6488, y: 0, z: -4.1599}
    - {x: -34.7982, y: 0, z: -4.2402}
    - {x: -33.7106, y: 0, z: -4.726}
    - {x: -32.4482, y: 0, z: -6.0577}
    - {x: -32.3615, y: 0, z: -6.1576}
    - {x: -31.0652, y: 0, z: -7.7921}
    - {x: -30.7607, y: 0, z: -8.4626}
    - {x: -30.3699, y: 0, z: -10.5856}
    - {x: -30.3472, y: 0, z: -10.7778}
    - {x: -30.3016, y: 0, z: -11.638}
    - {x: -30.3035, y: 0, z: -11.8278}
    - {x: -30.4273, y: 0, z: -13.5097}
    - {x: -30.907, y: 0, z: -14.522}
    - {x: -37.7258, y: 0, z: -20.9908}
    - {x: -38.0895, y: 0, z: -21.2457}
    - {x: -44.8137, y: 0, z: -24.6071}
    - {x: -45.1471, y: 0, z: -24.7271}
    isSimplePolygon: 1
    isConvex: 1
  saveMe: 0
  radius: 1
  hasRange: 1
  minX: -90
  maxX: 90
  minZ: -90
  maxZ: 90
