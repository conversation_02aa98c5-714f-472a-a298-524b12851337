﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/11/25

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class PolygonRiverLayerView : PolygonObjectLayerView
    {
        public PolygonRiverLayerView(MapLayerData layerData, bool asyncLoading) : base(layerData, asyncLoading)
        {
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new PolygonRiverView(data, this);
            view.CreateModel(data, layerData.currentLOD);
            return view;
        }

        public void ShowSplitter(int dataID, bool show)
        {
            var view = GetObjectView(dataID) as PolygonRiverView;
            if (view != null)
            {
                view.ShowSplitter(show);
            }
        }

        public void Move(int objectDataID, Vector3 offset)
        {
            var view = GetObjectView(objectDataID) as PolygonRiverView;
            if (view != null)
            {
                view.Move(offset);
            }
        }

        public void SetShaderLOD(int lod)
        {
            foreach (var p in mViews)
            {
                var riverView = p.Value as PolygonRiverView;
                riverView.SetShaderLOD(lod);
            }
        }

        public void UpdateTexture(int riverID)
        {
            var riverView = GetObjectView(riverID) as PolygonRiverView;
            if (riverView != null)
            {
                riverView.UpdateTexture();
            }
        }
    }
}


#endif