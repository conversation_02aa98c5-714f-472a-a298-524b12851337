﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;
using System.IO;
using Cysharp.Threading.Tasks;

namespace TFW.Map
{
    public class CameraClampInRegion
    {
        public void SetRegion(List<Vector3d> region)
        {
            mRegionVertices = region;
#if false
            var obj = new GameObject("camera clamp in region");
            var dp = obj.AddComponent<DrawPolygon>();
            List<Vector3> r = new List<Vector3>();
            for (int i = 0; i < region.Count; ++i)
            {
                r.Add(new Vector3((float)region[i].x, (float)region[i].y, (float)region[i].z));
            }
            dp.SetVertices(r);
#endif
        }

        public static CameraClampInRegion Load(string dataPath)
        {
            CameraClampInRegion clamp = null;
            var stream = MapModuleResourceMgr.LoadTextStream(dataPath, true);
            if (stream != null)
            {
                using BinaryReader reader = new BinaryReader(stream);

                int version = reader.ReadInt32();

                int n = reader.ReadInt32();
                var regionVertices = new List<Vector3d>(n);
                for (int i = 0; i < n; ++i)
                {
                    var v = Utils.ReadVector3(reader);
                    regionVertices.Add(new Vector3d(v.x, v.y, v.z));
                }

                clamp = new CameraClampInRegion();
                clamp.SetRegion(regionVertices);

                reader.Close();
            }

            return clamp;
        }

        public static async UniTask<CameraClampInRegion> LoadAsync(string dataPath)
        {
            CameraClampInRegion clamp = null;
            using var stream = await MapModuleResourceMgr.LoadTextStreamAsync(dataPath);
            {
                if (stream != null)
                {
                    using BinaryReader reader = new BinaryReader(stream);

                    int version = reader.ReadInt32();

                    int n = reader.ReadInt32();
                    var regionVertices = new List<Vector3d>(n);
                    for (int i = 0; i < n; ++i)
                    {
                        var v = Utils.ReadVector3(reader);
                        regionVertices.Add(new Vector3d(v.x, v.y, v.z));
                    }

                    clamp = new CameraClampInRegion();
                    clamp.SetRegion(regionVertices);

                    reader.Close();
                }
                //MapModuleResourceMgr.UnloadText(path);  
            }

            return clamp;
        }

        public bool ClampAndSlide(Vector3 oldPos, Vector3 newPos, out Vector3 clampedPos)
        {
            Vector3d op = new Vector3d(oldPos.x, oldPos.y, oldPos.z);
            Vector3d np = new Vector3d(newPos.x, newPos.y, newPos.z); ;
            clampedPos = newPos;
            int n = mRegionVertices.Count;
            if (n == 0)
            {
                return false;
            }
            //for (int i = 0; i < mCheckState.Length; ++i)
            //{
            //    mCheckState[i] = false;
            //}
            bool hitOnce = false;

            for (int i = 0; i < n; ++i)
            {
                //if (mCheckState[i] == false)
                {
                    Vector3d start = mRegionVertices[i];
                    Vector3d end = mRegionVertices[(i + 1) % n];

                    Vector3d hitPoint;
                    SlideInfo si;
                    bool intersected = GetSlideIntersection(op, np, start, end, out hitPoint, out si);
                    if (intersected)
                    {
                        hitOnce = true;
                        op = si.start;
                        np = si.end;
                        clampedPos = new Vector3((float)np.x, (float)np.y, (float)np.z);
                        //mCheckState[i] = true;
                        break;
                    }
                }
            }

            if (!hitOnce)
            {
                return false;
            }
            return true;

        }

        class SlideInfo
        {
            public Vector3d start;
            public Vector3d end;
        }

        bool GetSlideIntersection(Vector3d oldPos, Vector3d newPos, Vector3d edgeStart, Vector3d edgeEnd, out Vector3d hitPoint, out SlideInfo si)
        {
            si = null;
            hitPoint = new Vector3d(0, 0, 0);
            Vector3d d0 = newPos - oldPos;
            Vector3d d1 = edgeEnd - edgeStart;
            Vector3d k = oldPos - edgeStart;
            Vector3d d1Normal = new Vector3d(d1.z, 0, -d1.x);
            d1Normal.Normalize();
            double det = -d1.x * d0.z + d1.z * d0.x;
            if (det == 0)
            {
                return false;
            }

            double t1 = (-k.x * d0.z + k.z * d0.x) / det;
            double t0 = (d1.x * k.z - d1.z * k.x) / det;
            double esp = 0.1f;
            if (MathDouble.GE(t1, 0, esp) && MathDouble.LE(t1, 1, esp) &&
                MathDouble.GE(t0, 0, esp) && MathDouble.LE(t0, 1, esp))
            {
                //Debug.Log($"{t0}, {t1}");
                hitPoint = oldPos + d0 * t0;
                if (MathDouble.Approximately(t0, 0, esp))
                {
                    double d = Vector3d.Dot(d0.GetNormalized(), d1Normal);
                    if (d >= 0)
                    {
                        return false;
                    }
                }
                var slide = newPos - hitPoint;
                d1.Normalize();
                var p = Vector3d.Project(slide, d1);
                si = new SlideInfo();
                si.start = hitPoint;
                si.end = Clamp(hitPoint + p, edgeStart, edgeEnd);
                return true;
            }

            return false;
        }

        Vector3d Clamp(Vector3d p, Vector3d start, Vector3d end)
        {
            Vector3d ps = p - start;
            Vector3d pe = p - end;
            double d1 = ps.GetMagnitude();
            double d2 = pe.GetMagnitude();
            ps.Normalize();
            pe.Normalize();
            double d = Vector3d.Dot(ps, pe);
            if (!MathDouble.Approximately(d, -1.0, 0.000001f))
            {
                if (d1 < d2)
                {
                    return start;
                }
                return end;
            }
            return p;
        }

        List<Vector3d> mRegionVertices;
    }
}

