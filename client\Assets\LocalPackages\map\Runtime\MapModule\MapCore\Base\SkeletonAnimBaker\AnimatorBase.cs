﻿ 



 
 

using UnityEngine;
using UnityEngine.Playables;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class AnimatorBase
    {
        protected GameObject mGameObject;
        public GameObject gameObject { get { return mGameObject; } }

        public AnimatorBase(GameObject gameObject)
        {
            mGameObject = gameObject;
        }

        public abstract void OnEnable();
        public abstract void OnDisable();
        public abstract void OnDestroy();

        public abstract void RegisterAnimationEventCallback(System.Action<string, string> stringEventCallback, System.Action<string, int> intEventCallback, System.Action<string, float> floatEventCallback);
        public abstract void UnregisterAnimationEventCallback();
        public abstract void UnregisterAnimationIntEventCallback();
        public abstract void UnregisterAnimationFloatEventCallback();
        public abstract void UnregisterAnimationStringEventCallback();

        public abstract bool enableAnimationBlending { get; }
        public abstract bool stabilizeFeet { get; set; }
        public abstract Quaternion bodyRotation { get; set; }
        public abstract Vector3 bodyPosition { get; set; }
        public abstract float gravityWeight { get; }
        public abstract bool hasTransformHierarchy { get; }
        public abstract AnimatorUpdateMode updateMode { get; set; }
        public abstract bool applyRootMotion { get; set; }
        public abstract Quaternion rootRotation { get; set; }
        public abstract Vector3 rootPosition { get; set; }
        public abstract Vector3 angularVelocity { get; }
        public abstract Vector3 velocity { get; }
        public abstract Quaternion deltaRotation { get; }
        public abstract Vector3 deltaPosition { get; }
        public abstract bool isInitialized { get; }
        public abstract float humanScale { get; }
        public abstract bool hasRootMotion { get; }
        public abstract bool isHuman { get; }
        public abstract int layerCount { get; }
        public abstract bool isOptimizable { get; }
        public abstract AnimationParameterInfo[] parameters { get; }
        public abstract float feetPivotActive { get; set; }
        public abstract bool logWarnings { get; set; }
        public abstract float rightFeetBottomHeight { get; }
        public abstract float leftFeetBottomHeight { get; }
        public abstract bool layersAffectMassCenter { get; set; }
        public abstract PlayableGraph playableGraph { get; }
        public abstract Avatar avatar { get; set; }
        public abstract bool hasBoundPlayables { get; }
        public abstract RuntimeAnimatorController runtimeAnimatorController { get; set; }
        public abstract AnimatorRecorderMode recorderMode { get; }
        public abstract float recorderStopTime { get; set; }
        public abstract float recorderStartTime { get; set; }
        public abstract float playbackTime { get; set; }
        public abstract AnimatorCullingMode cullingMode { get; set; }
        public abstract Quaternion targetRotation { get; }
        public abstract Vector3 targetPosition { get; }
        public abstract float speed { get; set; }
        public abstract bool isMatchingTarget { get; }
        public abstract Vector3 pivotPosition { get; }
        public abstract float pivotWeight { get; }
        public abstract int parameterCount { get; }
        public abstract bool fireEvents { get; set; }
        public abstract InterruptionType interruptionType { get; set; }
        public abstract bool keepAnimatorControllerStateOnDisable { get; set; }
        public abstract void ApplyBuiltinRootMotion();
        public abstract void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime);
        public abstract void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset);
        public abstract void CrossFade(string stateName, float normalizedTransitionDuration);
        public abstract void CrossFade(string stateName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset, float normalizedTransitionTime);
        public abstract void CrossFade(string stateName, float normalizedTransitionDuration, int layer);
        public abstract void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer);
        public abstract void CrossFade(int stateHashName, float normalizedTransitionDuration);
        public abstract void CrossFade(int stateHashName, float normalizedTransitionDuration, int layer, float normalizedTimeOffset);
        public abstract void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset);
        public abstract void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime);
        public abstract void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration);
        public abstract void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer);
        public abstract void CrossFadeInFixedTime(int stateHashName, float fixedTransitionDuration, int layer, float fixedTimeOffset);
        public abstract void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer, float fixedTimeOffset, float normalizedTransitionTime);
        public abstract void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration, int layer);
        public abstract void CrossFadeInFixedTime(string stateName, float fixedTransitionDuration);
        public abstract AnimatorTransitionInfo GetAnimatorTransitionInfo(int layerIndex);
        public abstract T GetBehaviour<T>() where T : StateMachineBehaviour;
        public abstract StateMachineBehaviour[] GetBehaviours(int fullPathHash, int layerIndex);
        public abstract T[] GetBehaviours<T>() where T : StateMachineBehaviour;
        public abstract Transform GetBoneTransform(HumanBodyBones humanBoneId);
        public abstract bool GetBool(string name);
        public abstract bool GetBool(int id);
        public abstract AnimatorClipInfo[] GetCurrentAnimatorClipInfo(int layerIndex);
        public abstract void GetCurrentAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips);
        public abstract int GetCurrentAnimatorClipInfoCount(int layerIndex);
        public abstract AnimatorStateInfo GetCurrentAnimatorStateInfo(int layerIndex);
        public abstract float GetFloat(int id);
        public abstract float GetFloat(string name);
        public abstract Vector3 GetIKHintPosition(AvatarIKHint hint);
        public abstract float GetIKHintPositionWeight(AvatarIKHint hint);
        public abstract Vector3 GetIKPosition(AvatarIKGoal goal);
        public abstract float GetIKPositionWeight(AvatarIKGoal goal);
        public abstract Quaternion GetIKRotation(AvatarIKGoal goal);
        public abstract float GetIKRotationWeight(AvatarIKGoal goal);
        public abstract int GetInteger(string name);
        public abstract int GetInteger(int id);
        public abstract int GetLayerIndex(string layerName);
        public abstract string GetLayerName(int layerIndex);
        public abstract float GetLayerWeight(int layerIndex);
        public abstract AnimatorClipInfo[] GetNextAnimatorClipInfo(int layerIndex);
        public abstract void GetNextAnimatorClipInfo(int layerIndex, List<AnimatorClipInfo> clips);
        public abstract int GetNextAnimatorClipInfoCount(int layerIndex);
        public abstract AnimatorStateInfo GetNextAnimatorStateInfo(int layerIndex);
        public abstract AnimationParameterInfo GetParameter(int index);
        public abstract bool HasState(int layerIndex, int stateID);
        public abstract void InterruptMatchTarget();
        public abstract void InterruptMatchTarget(bool completeMatch);
        public abstract bool IsInTransition(int layerIndex);
        public abstract bool IsParameterControlledByCurve(int id);
        public abstract bool IsParameterControlledByCurve(string name);
        public abstract void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime);
        public abstract void MatchTarget(Vector3 matchPosition, Quaternion matchRotation, AvatarTarget targetBodyPart, MatchTargetWeightMask weightMask, float startNormalizedTime, float targetNormalizedTime);
        public abstract void Play(string stateName, int layer);
        public abstract void Play(int stateNameHash, int layer, float normalizedTime);
        public abstract void Play(string stateName, int layer, float normalizedTime);
        public abstract void Play(string stateName);
        public abstract void Play(int stateNameHash);
        public abstract void Play(int stateNameHash, int layer);
        public abstract void PlayInFixedTime(int stateNameHash, int layer, float fixedTime);
        public abstract void PlayInFixedTime(string stateName, int layer, float fixedTime);
        public abstract void PlayInFixedTime(int stateNameHash);
        public abstract void PlayInFixedTime(int stateNameHash, int layer);
        public abstract void PlayInFixedTime(string stateName, int layer);
        public abstract void PlayInFixedTime(string stateName);
        public abstract void Rebind();
        public abstract void ResetTrigger(string name);
        public abstract void ResetTrigger(int id);
        public abstract void SetBoneLocalRotation(HumanBodyBones humanBoneId, Quaternion rotation);
        public abstract void SetBool(string name, bool value);
        public abstract void SetBool(int id, bool value);
        public abstract void SetFloat(int id, float value);
        public abstract void SetFloat(string name, float value, float dampTime, float deltaTime);
        public abstract void SetFloat(string name, float value);
        public abstract void SetFloat(int id, float value, float dampTime, float deltaTime);
        public abstract void SetIKHintPosition(AvatarIKHint hint, Vector3 hintPosition);
        public abstract void SetIKHintPositionWeight(AvatarIKHint hint, float value);
        public abstract void SetIKPosition(AvatarIKGoal goal, Vector3 goalPosition);
        public abstract void SetIKPositionWeight(AvatarIKGoal goal, float value);
        public abstract void SetIKRotation(AvatarIKGoal goal, Quaternion goalRotation);
        public abstract void SetIKRotationWeight(AvatarIKGoal goal, float value);
        public abstract void SetInteger(int id, int value);
        public abstract void SetInteger(string name, int value);
        public abstract void SetLayerWeight(int layerIndex, float weight);
        public abstract void SetLookAtPosition(Vector3 lookAtPosition);
        public abstract void SetLookAtWeight(float weight);
        public abstract void SetLookAtWeight(float weight, float bodyWeight);
        public abstract void SetLookAtWeight(float weight, float bodyWeight, float headWeight);
        public abstract void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight);
        public abstract void SetLookAtWeight(float weight, float bodyWeight, float headWeight, float eyesWeight, float clampWeight);
        public abstract void SetTarget(AvatarTarget targetIndex, float targetNormalizedTime);
        public abstract void SetTrigger(int id);
        public abstract void SetTrigger(string name);
        public abstract void StartPlayback();
        public abstract void StartRecording(int frameCount);
        public abstract void StopPlayback();
        public abstract void StopRecording();
        public abstract void UpdateAnimator(float deltaTime);
        public abstract void WriteDefaultValues();
        public abstract bool Update(GlobalAnimationBlendingState state);
        public abstract string[] GetAnimationNames();
        public abstract bool IsPlayingAnimation(string animStateName);
        public abstract float GetCurrentNormalizedTime();
        public abstract string GetCurrentStateName();
        public abstract string GetNextStateName();
    }
}
