using Cfg.Cm;
using Common;
using cspb;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using TFW;
using TFW.UI;
using Logic;
using Public;
using Render;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cfg.G;
using UI;
using UnityEngine;
using Random = UnityEngine.Random;
using Cfg;
using K3.Scripts;
using TFW.Localization;
using TMPro;
using System.Net.Sockets;

namespace K3
{
    [Serializable]
    public class UIMergeData
    {
        //public List<int> boxidQueue;
        //public int curBoxId;

        public Dictionary<int, int> BoxClickCount;

        public DateTime mergeTime;
        public int mergeCount;

        /// <summary>
        /// 当天合成次数
        /// </summary>
        public int DayMergeCount = 0;


        // public Dictionary<Point,UIMergeGridData> GridsDataDIc;

        public UIMergeData()
        {
            BoxClickCount = new Dictionary<int, int>();
            //GridsData = new List<UIMergeGridData>();
            //boxidQueue = new List<int>();
            //AreaData = new UIMergeAreaData();
            //curBoxId = -1;
        }

        public void AddClickCount(int box)
        {
            if (BoxClickCount.ContainsKey(box))
            {
                BoxClickCount[box] = BoxClickCount[box] + 1;
            }
            else
            {
                BoxClickCount.Add(box, 1);
            }
        }

        public void AddMergeCount()
        {
            if (mergeCount < int.MaxValue)
            {
                mergeCount++;

                var timeD = DateTime.UtcNow - mergeTime;

                if (timeD.TotalDays > 1 || DateTime.UtcNow.Day != mergeTime.Day)
                {
                    DayMergeCount = 1;
                }
                else
                {
                    DayMergeCount++;
                }

                mergeTime = DateTime.UtcNow;

                if (mergeCount > 500)
                {
                    return;
                }

                if (mergeCount % 50 == 0)
                {
                    K3.K3GameEvent.I.BiLog<MergeEvent>(new MergeEvent() { EventKey = $"merge_{mergeCount}" });
                }

                if (mergeCount == 1)
                {
                    K3.K3GameEvent.I.BiLog<MergeEvent>(new MergeEvent() { EventKey = $"merge_{mergeCount}" });
                }
            }
        }

        //public void UpdateCXCurBoxId()
        //{
        //    if (K3PlayerMgr.I.BoxidQueue.Count > 0)
        //    {
        //        curBoxId = K3PlayerMgr.I.BoxidQueue[0];
        //        K3PlayerMgr.I.BoxidQueue.RemoveAt(0);
        //    }
        //    else
        //    {
        //        curBoxId = -1;
        //    }
        //}

        /// <summary>
        /// 获取当前在棋盘的数目
        /// </summary>
        /// <returns></returns>
        public int CurItemNum()
        {
            int itemNum = 0;

            foreach (var item in K3PlayerMgr.I.GridsData)
            {
                if (item.goodData != null)
                {
                    if (K3PlayerMgr.I.AreaData.NoPoint(new Vector2(item.point.x, item.point.y)))
                    {
                        itemNum++;
                    }
                }
            }

            return itemNum;
        }
    }

    public class UIMergeAreaData
    {
        /// <summary>
        /// 已经解锁的区域
        /// </summary>
        public List<int> AreaedData { get; } = new List<int>();

        private List<int> areaingData = new List<int>();

        /// <summary>
        /// 包含已经解锁及可解锁未解锁的区域
        /// </summary>
        public List<int> AreaingData
        {
            get
            {
                areaingData.Clear();

                var areas = Cfg.C.CCSArea.RawList();

                foreach (var item in areas)
                {
                    if (item.CanUnlock)
                    {
                        areaingData.Add(item.Id);
                    }
                }

                return areaingData;
            }
        }

        public int TodoUnlock
        {
            get
            {
                int todoId = 1;


                foreach (var item in AreaingData)
                {
                    if (item >= todoId)
                    {
                        todoId = item + 1;
                    }
                }

                return todoId;
            }
        }

        /// <summary>
        /// 可解锁的区域
        /// </summary>
        public List<int> AreaCanData
        {
            get
            {
                List<int> list = new List<int>();
                foreach (var item in AreaingData)
                {
                    if (!AreaedData.Contains(item))
                    {
                        list.Add(item);
                    }
                }

                return list;
            }
        }

        public static Dictionary<int, AreaPostion> AreaPostionDic = new Dictionary<int, AreaPostion>();

        public bool NoPoint(Point point)
        {
            return NoPoint(new Vector2(point.x, point.y));
        }

        public bool NoPoint(Vector2 point)
        {
            //if (Logic.LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Open_Fog))
            {
                var areas = Cfg.C.CCSArea.RawList();
                foreach (var item in areas)
                {
                    if (AreaedData.Contains(item.Id))
                    {
                        continue;
                    }

                    if (!AreaPostionDic.TryGetValue(item.Id, out AreaPostion areaPostion))
                    {
                        areaPostion = new AreaPostion(item.AreaPosition);
                        AreaPostionDic.Add(item.Id, areaPostion);
                    }

                    if (new Rect(new Vector2(areaPostion.x, areaPostion.y),
                            new Vector2(areaPostion.width, areaPostion.height)).Contains(point))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        public int GetAreaIDByGoodPoint(Vector2 point)
        {
            //if (Logic.LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Open_Fog))
            {
                var areas = Cfg.C.CCSArea.RawList();
                foreach (var item in areas)
                {
                    if (AreaedData.Contains(item.Id))
                    {
                        continue;
                    }

                    if (!AreaPostionDic.TryGetValue(item.Id, out AreaPostion areaPostion))
                    {
                        areaPostion = new AreaPostion(item.AreaPosition);
                        AreaPostionDic.Add(item.Id, areaPostion);
                    }

                    if (new Rect(new Vector2(areaPostion.x, areaPostion.y),
                            new Vector2(areaPostion.width, areaPostion.height)).Contains(point))
                    {
                        return item.Id;
                    }
                }
            }
            return -1;
        }
    }

    public class AreaPostion
    {
        public AreaPostion(CYXWidthHeight_iiii cYXWidthHeight_Iiii)
        {
            this.x = cYXWidthHeight_Iiii.X;
            this.y = cYXWidthHeight_Iiii.Y;
            this.width = cYXWidthHeight_Iiii.Width;
            this.height = cYXWidthHeight_Iiii.Height;
        }

        public int x;
        public int y;
        public int width;
        public int height;
    }

    [Serializable]
    public struct Point
    {
        public int x;
        public int y;

        public int ServerIndex
        {
            get { return y * CSPlayer.Grid_X + x; }
        }

        public Point(int serverIndex)
        {
            this.x = serverIndex % CSPlayer.Grid_X;
            this.y = serverIndex / CSPlayer.Grid_X;
        }

        public Point(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public bool Compare(Point point)
        {
            if (x == point.x && y == point.y)
            {
                return true;
            }

            return false;
        }

        public override string ToString()
        {
            return $"x:{x} y:{y}";
        }
    }

    [Popup("UIMerge", true, true)]
    public partial class UIMerge : BasePopupLayer
    {
        //[PopupField("Root/mageSpace/bottomBtns/topBtns/GoodButtonBG/redPoint/Text")]
        //public TFWText redPointGood;

        //[PopupField("Root/btn_questionnaire")]
        //public GameObject btn_questionnaire;


        //[PopupField("Root/mageSpace/bottomBtns")]
        //private GameObject bottomBtnsObj;


        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot")]
        public Transform MoveGoodRoot;


        [PopupField("Root/mageSpace/FirstCharge3day")]
        private GameObject FirstChargeObj;

        private BaseBtnWidget FirstChargeWidget;

        [PopupField("Root/mageSpace/FirstCharge3day/Red")]
        private GameObject redPointObj;

        [PopupField("Root/mageSpace/FirstCharge3day/Red/BG/Text")]
        private TFWText redPointText;

        //[PopupField("Root/Mask0")]
        //private Transform Mask0;

        //[PopupField("Root/InfoPanel/photoTaskUI")]
        //public UIMergeTaskTopInfo taskTopInfo;

        //[PopupField("Root/mageSpace/clickRoot/toplayer/CreatGoodButton/Box/Box0")]
        //private Animator BoxAni;

        //{
        //    get
        //    {
        //        var UIMain2 = SceneManager1.I.FindScene(typeof(UIMain2)) as UIMain2;

        //        if (UIMain2!=null && UIMain2.BattleBtn != null)
        //        {
        //            if (UIMain2.BattleBtn.activeSelf)
        //            {
        //                return UIMain2.BoxAnimator_lock;
        //            }
        //            else
        //                return UIMain2.BoxAnimator;
        //        }

        //        return null;
        //    }
        //}

        [PopupField("Root/mageSpace")] private RectTransform mageSpaceObj;


        [PopupField("Root/mageSpace/clickRoot/checkBox")]
        private Animator checkBoxAni;

        [PopupField("Root/mageSpace/clickRoot/checkBox/iconType")]
        private TFWImage checkGoodIconType;

        [PopupField("Root/mageSpace/clickRoot/checkBox/iconLv")]
        private TFWText checkGoodLvText;

        [PopupField("Root/mageSpace/EffectRoot/sameEffect")]
        private GameObject sameEffect;

        [PopupField("Root/mageSpace/EffectRoot/sameEffect_enter")]
        private GameObject sameEffect_enter;

        [PopupField("Root/mageSpace/EffectRoot/compoundEffect")]
        private GameObject compoundEffect;

        [PopupField("Root/mageSpace/EffectRoot/creatEffect")]
        private GameObject creatEffect;

        [PopupField("Root/mageSpace/EffectRoot/sameEffect_exit")]
        private GameObject sameEffect_exit;

        [PopupField("Root/mageSpace/EffectRoot/mergeEffect")]
        private GameObject mergeEffect;

        [PopupField("Root/mageSpace/EffectRoot/AccidentEffect1")]
        private GameObject accidentEffect1;

        [PopupField("Root/mageSpace/EffectRoot/AccidentEffect2")]
        private GameObject accidentEffect2;

        [PopupField("Root/mageSpace/EffectRoot/AccidentEffect3")]
        private GameObject accidentEffect3;

        [PopupField("Root/mageSpace/EffectRoot/UIMergeSkillIcon")]
        private GameObject UIMergeSkillIconGO;

        //[PopupField("Root/Top")]
        //private GameObject TopMoney;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/GuideFinger")]
        private GameObject GuideFingerObj;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/CreateGuid")]
        private GameObject CreateGuidMask;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/MergerGuid")]
        private GameObject MergerGuidMask;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/GuideFinger/hand")]
        private GameObject GuideFingerHand;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/GuideFinger/Icon")]
        private RectTransform guidTextObj;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/GuideFinger/Icon/guidText")]
        private TFWText guidText;

        [PopupField("Root/mageSpace/clickRoot/toplayer/MoveGoodRoot/AlphaImage")]
        private TFWImage m_AlphaImage;


        //[PopupField("Root/bottomBtns/topBtns/goodempty")]
        //private GameObject goodempty;


        [PopupField("Root/center")] private Transform centerRoot;


        [PopupField("Root/mageSpace/FirstCharge3day/Mask")]
        private GameObject flytoMaskObj;

        [PopupField("Root/mageSpace/FirstCharge3day/flyMask")]
        private GameObject flyMaskObj;

        // [PopupField("Root/mageSpace/EffectRoot/mergeEffect")]
        //private GameObject lockedEffect;

        private UIGrid gridRoot;
        private UIMergeGood goodPre;
        //private UIGrid mGridAniRoot;

        public UIMergeGrid[] mGridDic;
        public UIMergeGood[] mGoodDic;

        //private Dictionary<int, UIMergeGoodLuckEffect>
        //    mLuckEffectDic = new Dictionary<int, UIMergeGoodLuckEffect>(); // [grid.Point.ServerIndex, effect]

        public UIMergeData mData;

        //public List<UIMergeGood> ;
        public ObjectPool<GameObject> MergeGoodPool;

        private List<GameObject> creatEffectList = new List<GameObject>();
        private List<UIMergeGrid> guideGridList = new List<UIMergeGrid>();
        private ItemInfo curBox;
        public UIMergeGood curGood;

        private List<GameObject> mSkillIconEffectList = new List<GameObject>();

        //-------------TopInfo------------------
        //[PopupField("Root/InfoPanel/name")] private TFWText nameText;

        [PopupField("Root/InfoPanel/description")]
        private TFWText descriptionText;

        //[PopupField("Root/InfoPanel/level")] private TFWText levelText;

        //[PopupField("Root/InfoPanel/sellButton/price")]
        //private TFWText sellPriceText;

        [PopupField("Root/InfoPanel/collectButton/price")]
        private TFWText collectPriceText;

        [PopupField("Root/InfoPanel/unlockButton/price")]
        private TFWText unlockPriceText;

        [PopupField("Root/InfoPanel/infoButton")]
        private GameObject btnInfo;

        [PopupField("Root/InfoPanel/boxInfoButton")]
        private GameObject BOXbtnInfo;

        [PopupField("Root/InfoPanel/sellButton")]
        public GameObject btnSell;

        [PopupField("Root/InfoPanel/collectButton")]
        private GameObject btnCollect;

        [PopupField("Root/InfoPanel/unlockButton")]
        private GameObject btnUnlock;

        [PopupField("Root/InfoPanel/cancelSellButton")]
        private GameObject btnCancelSell;

        [PopupField("Root/InfoPanel/HeroItemInfo/ChangeBtn")]
        private GameObject btnChange;

        [PopupField("Root/InfoPanel/HeroItemInfo/buffTime")]
        private TFWText heroBuffTimeText;

        [PopupField("Root/InfoPanel/cancelTitle")]
        private TFWText cancelTitle;

        //[PopupField("Root/InfoPanel/IconBg/Icon")]
        //private TFWImage icon;

        [PopupField("Root/InfoPanel/collectButton/Icon")]
        private TFWImage collectTypeIcon;

        [PopupField("Root/InfoPanel/description_null")]
        private TFWText description_null;

        //[PopupField("Root/InfoPanel/IconBg")]
        //private GameObject IconObj;

        //[PopupField("Root/InfoPanel/Bg")]
        //private GameObject levelBg;

        [PopupField("Root/InfoPanel/unlockButton/deletetimer")]
        private TFWText deletetimerText;

        [PopupField("Root/mageSpace/clickRoot/toplayer/AreaRoot")]
        private Transform areaParent;

        [PopupField("Root/mageSpace/clickRoot/toplayer/AreaRoot/prefab")]
        private GameObject areaPrefab;

        [PopupField("Root/InfoPanel/BoxItemCreatGoodButton")]
        private GameObject btnBoxItemCreatGoodButton;

        [PopupField("Root/InfoPanel/BoxItemRestCD/btnAD_BoxItem")]
        private GameObject btnAD_BoxItem;

        [PopupField("Root/InfoPanel/BoxItemRestCD/btnFree")]
        private GameObject btnFree_BoxItem;

        [PopupField("Root/InfoPanel/BoxItemRestCD/btnCoin_BoxItem")]
        private GameObject btnCoin_BoxItem;

        [PopupField("Root/InfoPanel/BoxItemRestCD")]
        private GameObject BoxItemCDObj;

        [PopupField("Root/InfoPanel/IconBg/BOXCD")]
        private GameObject BoxItemCDOIcon;

        [PopupField("Root/InfoPanel/BoxItemCreatGoodButton/price")]
        private TFWText BoxItemCreatGoodPriceText;

        [PopupField("Root/InfoPanel/BoxItemRestCD/btnCoin_BoxItem/price")]
        private TFWText BoxItemRestCDPriceText;

        [PopupField("Root/InfoPanel/BoxItemRestCD/CD")]
        private TFWText BoxItemRestCDText;

        [PopupField("Root/InfoPanel/HeroItemInfo")]
        private GameObject HeroItemInfoGa;

        [PopupField("Root/InfoPanel/HeroItemInfo/DesText")]
        private TFWText HeroItemInfoDesText;

        [PopupField("Root/InfoPanel/HeroItemInfo/SkillIcon")]
        private TFWImage skillIcon;

        #region 专属箱子信息显示

        [PopupField("Root/InfoPanel/SpecialBoxInfo")]
        private GameObject specialBoxInfo;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/GoIntoBtn")]
        private GameObject goIntoBtn;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/ChangeBtn")]
        private GameObject changeBtn;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/HeroInfo")]
        private GameObject heroHeadBtn;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/HeroInfo/Hero")]
        private TFWImage heroQuality;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/HeroInfo/Hero/Icon")]
        private TFWImage heroIcon;

        [PopupField("Root/InfoPanel/SpecialBoxInfo/HeroInfo/Empty")]
        private TFWImage heroEmpty;

        #endregion 专属箱子信息显示

        private ItemInfo gooddata;
        //----

        private System.DateTime lastTime;
        private float doubleDuration = 0.5f;
        private float showMergeGuodeinterval = 5f;
        private float showCreatGoodGuideinterval = 10f;

        private bool boxOpen = false;
        private bool CreatGood_click = false;

        private float lastShowMergeGuidetimer;
        private float lastShowCreatGoodGuidetimer;


        //[PopupField("BG")]
        //public GameObject BG;

        //-----本地化无变化文字
        [PopupField("Root/InfoPanel/infoButton/Text")]
        private TFWText infobuttonText;

        [PopupField("Root/InfoPanel/sellButton/title")]
        private TFWText sellText;

        [PopupField("Root/InfoPanel/unlockButton/title")]
        private TFWText unlockText;

        [PopupField("Root/InfoPanel/collectButton/title")]
        private TFWText collectText;

        [PopupField("Root/InfoPanel/BoxItemCreatGoodButton/title")]
        private TFWText BoxItemCreatGoodButtonTitleText;

        [PopupField("Root/InfoPanel/BoxItemRestCD/title")]
        private TFWText boxItemCDtitleText;

        //-----气泡解锁
        [PopupField("Root/UIMergeBubbleInfo")] private GameObject bubblePanel;

        [PopupField("Root/UIMergeBubbleInfo/Root/Content/btns/btnAD")]
        private GameObject btnUnLockAD;

        [PopupField("Root/UIMergeBubbleInfo/Root/Content/timeIcon/tiemText")]
        private TFWText timeText;

        [PopupField("Root/UIMergeBubbleInfo/Root/Content/btns/btnFree")]
        private GameObject btnUnlockFree;

        [PopupField("Root/UIMergeBubbleInfo/Root/Content/btns/btnCoin")]
        private GameObject btnUnlockCoin;

        [PopupField("Root/UIMergeBubbleInfo/Root/Content/btns/btnCoin/text")]
        private TFWText priceText;

        [PopupField("Root/btnBack")]
        public GameObject btnBack;



        [PopupField("Root/flyIcon")] public GameObject btnBackIcon;

        //[PopupField("Root/Task")]
        //public GameObject Task;

        /// <summary>
        /// 倒计时
        /// </summary>
        private NTimer.Timer timer; //timer_update

        private Dictionary<int, UIMergeAreaComp> AreaDic;

        public bool isCancelState = false;

        //private TaskWidget m_TaskWidget;

        #region 初始化

        /// <summary>
        /// 解锁逻辑
        /// </summary>
        //private void UnlockView()
        //{
        //    //bool flag = UnlockMgr.I.CheckUnlock(UnlockFuncEnum.ChatperFunc) && LSwitchMgr.I.IsFunctionOpen(SwitchConfig.ChapterQuest);
        //    // UnityEngine.Debug.Log($"LogDebug = {flag}");
        //    //Task.SetActive(flag);
        //}
        protected override void OnInit()
        {
            //BindClickListener(btn_questionnaire, (x, y) =>
            //{
            //    SDKManager.instance.SDKOpenLink(@"https://gevents.endomainname.com/survey/index.html?aid=cswj1");
            //});

            BindClickListener(btnBack, (x, y) =>
            {
                // GameAudio.PlayAudio(5);                
                if (MergeGuiding) //
                    return;

                if (StateMerge)
                {
                    //UIHelper.RemoveComponent<BasePopupLayerUpdate>(centerRoot.gameObject);
                    Close();
                }
                else
                {
                    EventMgr.FireEvent(TEventType.K3UIMerge_ToMerge);
                }
            });

            BindClickListener(btnHeroAdd, (x, y) =>
            {
                // GameAudio.PlayAudio(5);                
                if (StateMerge)
                {
                    EventMgr.FireEvent(TEventType.K3UIMerge_ToHero);
                }
                else
                {
                    //一键上阵
                    CSPlayer.I.SendMergeHeroBatchSetReq();

                    EventMgr.FireEvent(TEventType.K3UIMerge_ToMerge);
                }

                /*PlayerPrefs.SetInt("UIMerge_btnHeroAddRed" + LPlayer.I.PlayerID, 1);
                PlayerPrefs.Save();
                btnHeroAddRed.SetActive(false);*/
            });

            BindClickListener(btnQuickDeploy, (x, y) =>
            {
                //一键上阵
                CSPlayer.I.SendMergeHeroBatchSetReq();
                EventMgr.FireEvent(TEventType.K3UIMerge_ToMerge);
            });

            // 初始化遮罩点击事件监听器
            BindClickListener(mMask, (x, y) =>
            {
                EventMgr.FireEvent(TEventType.K3UIMerge_ToMerge);
            });

            //替换Good按钮特效

            //GameObject.Destroy(btnBox.transform.Find("PowerupGlow6").gameObject);

            //GameObject effect = ResourceMgr.LoadInstance("Assets/K1D7/Res/Effects/Prefabs/ui_effect_tf_01.prefab");
            //effect.SetParent(btnBox.transform);
            //effect.transform.localPosition = Vector3.zero;
            //effect.transform.localScale = Vector3.one * 1.2f;
            /*
            ParticleSystem[] ps = effect.gameObject.GetComponentsInChildren<ParticleSystem>();
            foreach (var item in ps)
            {
                ParticleSystemRenderer renderer = item.GetComponent<ParticleSystemRenderer>();
                renderer.sortingLayerID = CustomSortingLayer.Layer;
                renderer.sortingOrder = 1;
                if (item.gameObject.name == "1")
                {
                    renderer.sortingOrder = 3;
                }
            }
            */
            //BindClickListener(btnBox, (x, y) =>
            //{
            //    if (MergeGuiding)
            //        return;
            //    //-- 生成对应的Good,列表为空时隐藏
            //    OpenCurBox();
            //});

            BindClickListener(btnCollect, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                collectGood();
            });
            BindClickListener(btnSell, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                SellGood();
            });
            BindClickListener(btnCancelSell, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                CancelSellGood();
            });
            BindClickListener(btnInfo, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                ShowGoodInfo();
            });

            //BindClickListener(icon.gameObject, (x, y) =>
            //{
            //    if (MergeGuiding)
            //        return;

            //    if (curSpecialBox?.mBox != null)
            //    {
            //        PopupManager.I.ShowPanel<UIMergeBoxItemInfoPanel>(curSpecialBox.mBox);
            //    }
            //    else
            //    {
            //        ShowGoodInfo();
            //    }
            //});

            //BindClickListener(btnUnlock, (x, y) =>
            //{
            //    if (MergeGuiding)
            //        return;

            //    UnlockGoodAD();
            //});
            BindClickListener(btnUnLockAD, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                UnlockGoodAD();
            });

            BindClickListener(btnUnlockFree, (x, y) =>
            {
                if (MergeGuiding)
                    return;

                if (K3PlayerMgr.I.PlayerData.freeAdUnlockTimes == 0)
                {
                    K3PlayerMgr.I.PlayerData.freeAdUnlockTimes++;
                    UnlockGood();
                }
                else
                {
                    UnlockGoodAD();
                }
            });

            BindClickListener(btnUnlockCoin, (x, y) =>
            {
                if (MergeGuiding)
                    return;

                UnlockGoodCoin();
            });

            BindClickListener(btnBoxItemCreatGoodButton, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                BoxItemCreatGood();
            });

            BindClickListener(btnAD_BoxItem, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                RestBoxItemByAD();
            });

            BindClickListener(btnFree_BoxItem, (x, y) =>
            {
                if (MergeGuiding)
                    return;

                K3PlayerMgr.I.PlayerData.freeResetBoxCD += 1;
                RestBoxItemCD();
            });

            BindClickListener(btnCoin_BoxItem, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                RestBoxItemByMoney();
            });
            BindClickListener(btnChange, (x, y) =>
            {
                if (MergeGuiding)
                    return;
                ChangeHero();
            });

            //int time = CSVGuide.GetGuideSec();

            if (MergeGoodPool == null)
            {
                MergeGoodPool = new ObjectPool<GameObject>();
            }

            //showMergeGuodeinterval = CSVGuide.GetGuideSec();
            //SetGoodData();

            AreaDic = new Dictionary<int, UIMergeAreaComp>();
            InitAreaData();

            goodPre = UIHelper.GetAddComponent<UIMergeGood>(GetChild("Root/mageSpace/Good"));
            goodPre.gameObject.SetActive(false);

            mGridDic = new UIMergeGrid[56];
            mGoodDic = new UIMergeGood[56];

            gridRoot = GetComponent<UIGrid>("Root/mageSpace/clickRoot/GridRoot");

            gridRoot.Clear();

            //D.Warning?.Log($"【棋盘】棋盘Grid 初始化！");
            for (int i = 0; i < CSPlayer.Grid_Y; i++)
            {
                for (int j = 0; j < CSPlayer.Grid_X; j++)
                {
                    UIMergeGrid grid = gridRoot.AddItem<UIMergeGrid>();
                    grid.Init(this, j, i);
                    grid.gameObject.name = grid.Point.ServerIndex.ToString();
                    mGridDic[grid.Point.ServerIndex] = grid;
                }
            }

            ShowTopInfo();

            sameEffect.SetActive(false);
            compoundEffect.SetActive(false);

            infobuttonText.text = TFW.Localization.LocalizationMgr.Get("Ui_item_info");
            sellText.text = TFW.Localization.LocalizationMgr.Get("Item_click1");
            unlockText.text = TFW.Localization.LocalizationMgr.Get("Item_click3");
            collectText.text = TFW.Localization.LocalizationMgr.Get("ui_idle_btn_collect");
            BoxItemCreatGoodButtonTitleText.text = TFW.Localization.LocalizationMgr.Get("ui_idle_btn_collect");
            boxItemCDtitleText.text = TFW.Localization.LocalizationMgr.Get("Ui_BoxCd_refresh_title");

            //showMergeGuodeinterval = time == -1 ? float.MaxValue : time;

            //Mask0.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Panel;

            InitAuto();
        }

        #endregion

        /// <summary>
        /// 初始化迁服信息
        /// </summary>
        private void InitFirstCharge()
        {
            FirstChargeWidget = new BaseBtnWidget(FirstChargeObj);
            FirstChargeWidget.SetBtnClickCallBack(OnClickFirstChargeBtn);
            //redPointObj = 
            //刷新迁服按钮活动状态
            UpdateFirstChargeActive(null);
        }


        [PopupField("Root/mageSpace/FlyAnimRoot")]
        private GameObject firstRechargeAniObj;

        [PopupEvent(TEventType.TestUseHeroEnd)]
        private void TestHeroEnd(object[] objs)
        {
            firstRechargeAniObj.gameObject.SetActive(true);
            firstRechargeAniObj.GetComponentInChildren<Animation>().Play("UI_UIMerge_FirstCharge3day_Fly", () =>
            {
                firstRechargeAniObj.SetActive(false);
                RefreshBtnActive();
                SetHeroAddRedState();

            });

        }


        private void UpdatePerSecondFirstRecharge(float dt)
        {
            if (timeText != null && timeText.gameObject.activeInHierarchy)
            {
                DateTime nextDateTime = DateTimeOffset.FromUnixTimeMilliseconds(GameTime.Time + 86400000).UtcDateTime;
                // 获取下一天的零点时间
                long nextZeroPoint = ((DateTimeOffset)nextDateTime.Date).ToUnixTimeMilliseconds();
                var diffTime = nextZeroPoint - GameTime.Time;
                timeText.text = UIStringUtils.MSToFormateTime(diffTime);
            }
        }

        private void OnClickFirstChargeBtn(object[] obj)
        {
            //显示首充界面
            FirstChargeNewUI.Layer.OpenAdaptively();
            AutoOpenMgr.I.SetClickFirstChargeBtn();
        }

        /// <summary>
        /// 反向初始化迁服信息
        /// </summary>
        private void UnInitFirstCharge()
        {
            if (FirstChargeWidget != null)
            {
                FirstChargeWidget.Destroy();
                FirstChargeWidget = null;
            }

            FrameUpdateMgr.UnregisterPerSecondFunListUpdate(this);
        }

        /// <summary>
        /// 刷新三日购红点显示信息数据
        /// </summary>
        /// <param name="objs"></param>
        private void UpdateFirstChargeRedPoint()
        {
            if (redPointObj != null)
            {
                int num;

                {
                    num = FirstChargeNewMgr.I.GetTotalRedDot();
                }

                redPointObj.SetActive(num > 0);
                redPointText.text = num.ToString();
            }
        }

        /// <summary>
        /// 刷新首充3日按钮活动状态
        /// </summary>
        [PopupEvent(TEventType.FirstChargeNtfDataSuccess)]
        private void UpdateFirstChargeActive(object[] obj)
        {
            //  
            RefreshBtnActive();
        }


        [PopupEvent(TEventType.FlyFirstChargeBtn)]
        private void FlyFirstChargeBtn(object[] objs)
        {
            Debug.Log("--------1");
            if (objs != null && objs.Length > 0)
            {
                var startPoint = objs[0] as Transform;

                flyMaskObj.transform.position = startPoint.position;
                flyMaskObj.SetActive(true);

                flyMaskObj.transform.DOLocalMove(flytoMaskObj.transform.localPosition, 1f).SetEase(Ease.InBack)
                    .OnComplete(() =>
                    {
                        flytoMaskObj.SetActive(true);
                        flyMaskObj.SetActive(false);
                        flytoMaskObj.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f);
                    });
            }
        }


        private void RefreshBtnActive()
        {
            UpdateFirstChargeRedPoint();

            bool isOpen = AutoOpenMgr.I.CanShowFirstRecharge();
            FirstChargeWidget?.UpdateBtnOpenState(isOpen && !MergeTaskMgr.I.CanTestHero());
            if (timeText != null)
                timeText.gameObject.SetActive(false);
        }


        [PopupEvent(TEventType.OnUnlockConditionChanged)]
        private void UpdateK3TaskDataRefresh(object[] objs)
        {
            RefreshBtnActive();
        }


        //[PopupEvent(TEventType.K3TaskComplete_UIMerge)]
        //private void OnNewTaskFinished(object[] args)
        //{
        //    // 判断按钮状态
        //    //btnBack.SetActive(UI.Utils.MainFunctionOpenUtils.OpenMenuBtnsState);
        //    TaskCompleteToDo();

        //    //mageSpaceObj.DOAnchorPos(new Vector2(0, -3920), 0.36f); 
        //}


        //private void OnClickHead()
        //{
        //    PopupManager.I.ShowLayer<UIPlayerMain>();
        //    EventMgr.FireEvent(TEventType.HideTriggerGuide);
        //}

        private void UnlockGoodAD()
        {
            //var adEvent = new ADEvent();
            //adEvent.EventKey = "ADEvent_Start";
            //adEvent.Properties.Add("AdResource", "UnlockItem");
            //adEvent.Properties.Add("itemid", curGood.Info.id);
            //K3GameEvent.I.TaLog(adEvent);

            //SDKManager.instance.ShowAd("unlockItem", (success) =>
            // {
            //     switch (success)
            //     {
            //         case SDKManager.AdResult.VIP:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_vip";
            //             //adEvent.Properties.Add("AdResource", "UnlockItem");
            //             //adEvent.Properties.Add("itemid", curGood.Info.id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             UnlockGood();
            //             break;

            //         case SDKManager.AdResult.Ok:
            //             FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("System_ADShop_Erro_3"));

            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_Success";
            //             //adEvent.Properties.Add("AdResource", "UnlockItem");
            //             //adEvent.Properties.Add("itemid", curGood.Info.id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             UnlockGood();
            //             break;

            //         case SDKManager.AdResult.CloseVideo:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_closevideo";
            //             //adEvent.Properties.Add("AdResource", "UnlockItem");
            //             //adEvent.Properties.Add("itemid", curGood.Info.id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             break;

            //         default:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_loadError";
            //             //adEvent.Properties.Add("AdResource", "UnlockItem");
            //             //adEvent.Properties.Add("itemid", curGood.Info.id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("System_ADShop_Erro_2"));
            //             break;
            //     }
            // });
        }

        private void UnlockGoodCoin()
        {
            UnlockGood(true);
        }

        //private void ShowPhoto()
        //{
        //    //PopupManager.I.ClearAllPopup();

        //    //--展示photo界面
        //    //WndMgr.Show<UIMergePhoto>();

        //    //CloudEffectCtrl.I.PlayBack();
        //}


        #region 数据刷新显示

        protected internal override void OnOpenStart()
        {
            base.OnOpenStart();
            /// 监听以下解锁通知逻辑
            //UnlockMgr.I.OnUnlockConditionUpdate += UnlockView;

            //m_TaskWidget = new TaskWidget(Task);            

            //m_TaskWidget.SetData();
            OnShowStart();
        }

        protected internal override void OnShowStart()
        {
            base.OnShowStart();

            //GSGamePlay.TALog("mergeshow");

            //UIHelper.GetAddComponent<BasePopupLayerUpdate>(centerRoot.gameObject).Init(this);

            if (K3PlayerMgr.I.PlayerData == null)
            {
                NTimer.CountDown(0.1f, () => { Close(); });
                return;
            }


            StateMerge = true;

            ShowData();

            InitSpecialBox();

            RefreshHero(null);


            InitFirstCharge();
            //btnBack.SetActive(UI.Utils.MainFunctionOpenUtils.OpenMenuBtnsState);
            //mageSpaceObj.anchoredPosition = new Vector2(0, -920);


            checkBoxAni.gameObject.SetActive(false);
            //if (TopMoney.GetComponent<TopMoneyCtr>() == null)
            //{
            //    TopMoney.AddComponent<TopMoneyCtr>().Init();
            //}
            //else
            //{
            //    TopMoney.GetComponent<TopMoneyCtr>().Init();
            //}

            bool showMask = mData.mergeCount <= 0;
            m_AlphaImage.gameObject.SetActive(false);

            CoroutineMgr.StartCoroutine(ShowGuid());
            UpdateEnable = true;
            //timer_update = NTimer.CountDownNoPool(1f, () => { UpdateEnable = true; });

            //K3PlayerMgr.I.CheckCityUse(mGridDic);

            //EventMgr.FireEvent(TEventType.K3GridDataRefresh);

            //taskTopInfo.CheckTaskUse();

            //K3PlayerMgr.I.CurUIMerge = this;

            //UnlockView();

            LSkill.I.TryShowSpecialBoxItemUpgradePanel();

            //btnHeroAdd.GetComponentInChildren<TextMeshProUGUI>().text = "Hero_List_Pick".ToLocal();

            //#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            //            btn_questionnaire.SetActive(true);
            //#else
            //        btn_questionnaire.SetActive(false);
            //#endif

            //OnBtnBackRedRefrsh();
            SetHeroAddRedState();
        }

        private void ShowData()
        {
            //InitDataAuto();

            mData = K3PlayerMgr.I.PlayerData.mergeData;

            //SetGoodData();

            RefreshArea();


            //D.Warning?.Log($"【棋盘】棋盘Grid Good ShowData！");
            foreach (var grid in mGridDic)
            {
                var uIMergeGridData = K3.K3PlayerMgr.I.GridsData[grid.Point.ServerIndex];
                if (uIMergeGridData.isFull)
                {
                    if (mGoodDic[grid.Point.ServerIndex])
                    {
                        var info = CSVItem.GetItemInfoById(uIMergeGridData.goodData.id);

                        info.Immovable = uIMergeGridData.goodData.Immovable;

                        mGoodDic[grid.Point.ServerIndex].Init(grid, info, MoveGoodRoot, this, uIMergeGridData.goodData.locked, uIMergeGridData.goodData.creatTamp, false, 0.2f, uIMergeGridData.goodData.boxCdTamp, uIMergeGridData.goodData.BoxTimes,
                            uIMergeGridData.goodData.BoxTimes_Buff);
                    }
                    else
                    {
                        CreatGood(uIMergeGridData.goodData.id, uIMergeGridData.goodData.goodType, grid,
                            uIMergeGridData.goodData.locked,
                            uIMergeGridData.goodData.creatTamp, uIMergeGridData.goodData.boxCdTamp,
                            uIMergeGridData.goodData.BoxTimes, uIMergeGridData.goodData.Immovable,
                            uIMergeGridData.goodData.BoxTimes_Buff);
                    }
                }
                else
                {
                    if (mGoodDic[grid.Point.ServerIndex])
                        mGoodDic[grid.Point.ServerIndex].DisPos(true);
                }
            }

            ShowTopInfo();
            sameEffect.SetActive(false);
            compoundEffect.SetActive(false);

            //RefreshPhotoCountText();

            //EventMgr.RegisterEvent(TEventType.K3GridDataRefresh, (objs) =>
            //{
            //    //autoMergeChanging = true;
            //    //RefreshTaskEffect();
            //}, this);

            //EventMgr.RegisterEvent(TEventType.MainCitySkillLevelUp, (objs) =>
            //{
            //    RefreshCityLvUnlockItems();
            //}, this);

            //RefreshCityLvUnlockItems();
        }

        private void RefreshCityLvUnlockItems()
        {
            //int minLv= LSkill.I.GetMainCityBoxLevel();

            //var mStep= CSPlayer.I.k3ToServerData.AddObjUnolckStep();

            //foreach (var item in mGoodDic)
            //{
            //    if (item.Value.mData.Immovable && item.Value.Info.Level<minLv)
            //    {
            //        item.Value.UnlockImmovable();
            //        mStep.assets.Add(item.Value.mPoint.ServerIndex,K3PlayerMgr.I.TOID(item.Value.mData));
            //    }
            //}

            //if (mStep.assets.Count > 0)
            //{
            //    mStep.StepRecordEnd();
            //}
        }

        protected internal override void OnCloseStart()
        {
            base.OnCloseStart();

            timer?.Stop();
            //timer_update?.Stop();

            UnInitFirstCharge();

            //Using_HeroSkill = false;
            //IsAutoMerging = false;

            CSPlayer.I.SyncMerge(false);
            //UnlockMgr.I.OnUnlockConditionUpdate -= UnlockView;
            //m_TaskWidget.UnInitTask();
            EventMgr.FireEvent(TEventType.RefreshAssetAck);
        }

        private void InitAreaData()
        {
            var areaList = Cfg.C.CCSArea.RawList();
            foreach (var item in areaList)
            {
                if (K3PlayerMgr.I.AreaData.AreaedData.Contains(item.Id) == true)
                {
                    continue;
                }

                //if (!Logic.LSwitchMgr.I.IsFunctionOpen(SwitchConfig.Open_Fog))
                //{
                //    continue;
                //}

                //
                var areaComp = GameObject.Instantiate(areaPrefab, areaParent);
                areaComp.SetActive(true);
                var mergeAreaComp = areaComp.GetAddCom<UIMergeAreaComp>();
                mergeAreaComp.InitArea(item.Id, K3PlayerMgr.I.AreaData);

                AreaDic.Add(item.Id, mergeAreaComp);

                int areaWidth = 142;
                int areaHight = 142;

                AreaPostion areaPostion = new AreaPostion(item.AreaPosition);
                int width = areaPostion.width * areaWidth;
                int height = areaPostion.height * areaHight;
                //145*N
                areaComp.GetComponent<RectTransform>().anchoredPosition = new Vector2(
                    width * 0.5f + areaPostion.x * areaWidth, height * -0.5f - areaPostion.y * areaHight);
                areaComp.GetComponent<RectTransform>().sizeDelta = new Vector2(width, height);
            }
        }

        private void RefreshArea()
        {
            List<int> removeAreas = new List<int>();
            foreach (var item in AreaDic)
            {
                if (K3PlayerMgr.I.AreaData.AreaedData.Contains(item.Key))
                {
                    removeAreas.Add(item.Key);
                }
            }

            foreach (var item in removeAreas)
            {
                if (AreaDic.ContainsKey(item))
                {
                    if (AreaDic[item] != null && AreaDic[item].gameObject != null)
                    {
                        UnityEngine.Object.Destroy(AreaDic[item].gameObject);
                    }

                    AreaDic.Remove(item);
                }
            }
        }

        private bool m_IsGuideFinish;

        private IEnumerator ShowGuid()
        {
            StopWaitShowGuideMerge();
            while (!m_IsGuideFinish)
            {
                lastShowMergeGuidetimer = Time.time;
                m_IsGuideFinish = GuidManage.FirstGuideHasBeenCompleted();
                yield return new WaitForSeconds(0.2f);
            }

            yield return new WaitForSeconds(0.2f);
            //UnityEngine.Debug.Log($"LogDebug - m_IsGuideFinish = {lastShowMergeGuidetimer}");
            /*bool showMask = mData.mergeCount <= 0 && m_IsGuideFinish;
            if (showMask)
            {
                m_AlphaImage.DOFade(0, 1.2f).OnComplete(() =>
                {
                    m_AlphaImage.gameObject.SetActive(false);

                });
            }*/

            CheckGuid(true);
        }

        public bool MergeGuiding
        {
            get { return MergerGuidMask.activeInHierarchy == true; }
        }

        public bool CanDrag()
        {
            if (autoMergeing)
            {
                Dictionary<int, List<UIMergeGrid>> gridDic = new Dictionary<int, List<UIMergeGrid>>();

                foreach (var grid in mGridDic)
                {
                    if (grid.GetData() != null
                        && grid.GetData().isFull
                        && grid.NoInArea()
                        && grid.Good != null
                        && grid.Good.mData.goodType == 0
                        && !grid.GetData().goodData.locked
                        && !grid.GetData().goodData.max
                        && !grid.GetData().goodData.Immovable
                        && !grid.Good.autoMergeing
                        && grid.Good.CanClick)
                    //&& grid.Value.Good.CanLevelUpByCityTech(false))
                    {

                        //var k3ItemCfg = Cfg.C.CK3Item.I(grid.Value.Good.mData.id);

                        //if (autoMergeingGM ||  mergeAutoType.Contains(new Vector2Int(k3ItemCfg.Type, k3ItemCfg.Code)))

                        //{
                        int id = grid.GetData().goodData.id;
                        if (!gridDic.ContainsKey(id))
                        {
                            gridDic.Add(id, new List<UIMergeGrid>());
                        }

                        if (!gridDic[id].Contains(grid) && !grid.Good.AutoMerge())
                        {
                            gridDic[id].Add(grid);
                        }
                        //}
                    }
                }

                var keyList = gridDic.Keys.OrderBy(a => Cfg.C.CK3Item.I(a)?.Level ?? 0).ToList();
                List<int> mergeCode = new List<int>();


                foreach (var key in keyList)
                {
                    if (gridDic.TryGetValue(key, out var item))
                    {
                        if (item.Count > 1)
                        {

                            return false;

                        }
                    }
                }
            }

            return true;
        }

        public bool CreateGuiding
        {
            get { return K3PlayerMgr.I.PlayerData.guiding == 6 || K3PlayerMgr.I.PlayerData.guiding == 7; }
        }

        private int curGuiding;
        private NTimer.Timer guidTimer;

        private int lastItemCount;

        private List<int> m_AudioIds = new List<int>();

        public void CheckGuid(bool forceFind = true)
        {
            if (!GuidManage.FirstGuideHasBeenCompleted())
                return;

            if (GameObject == null || !GameObject.activeSelf)
                return;

            if (mData == null)
                return;

            GuideFingerObj?.SetActive(false);

            List<UIMergeGrid> startGrids;
            if (mData.mergeCount <= 1)
            {
                if (m_AudioIds.Count == 0 && mData.mergeCount == 0)
                {
                    var cfg = Cfg.C.CAudioList.I(201001);
                    if (cfg != null)
                    {
                        int audioId = AudioManager.Instance.Play((AudioChannelType)cfg.Scene, cfg.Asset, cfg.Time,
                            cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
                        m_AudioIds.Add(audioId);
                    }
                }

                if (m_AudioIds.Count == 1 && mData.mergeCount == 1)
                {
                    AudioManager.Instance.Stop(m_AudioIds[0]);
                    var cfg = Cfg.C.CAudioList.I(201002);
                    if (cfg != null)
                    {
                        int audioId = AudioManager.Instance.Play((AudioChannelType)cfg.Scene, cfg.Asset, cfg.Time,
                            cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
                        m_AudioIds.Add(audioId);
                    }
                }

                startGrids = GetGuideMergeGrid(102001).OrderByDescending(grid => grid.transform.localPosition.y)
                    .ThenBy(grid => grid.transform.localPosition.x).ToList();
                if (startGrids.Count >= 2)
                {
                    if (curGuiding <= 2 || lastItemCount != startGrids.Count || forceFind)
                    {
                        lastItemCount = startGrids.Count;
                        K3PlayerMgr.I.PlayerData.guiding = mData.mergeCount + 1;
                        curGuiding = K3PlayerMgr.I.PlayerData.guiding;

                        K3PlayerMgr.I.SavePlayerDataToServer();
                        GuideFingerObj?.SetActive(true);
                        DOTween.Kill(GuideFingerHand.transform);
                        guidTimer?.Stop();

                        GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
                        GuideFingerHand.transform.position = startGrids[0].transform.position;
                        GuideFingerHand.gameObject.SetActive(false);
                        guidTimer = NTimer.CountDownNoPool(0.4f, () =>
                        {
                            GuideFingerHand.gameObject.SetActive(true);
                            GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
                        });

                        guidTextObj.gameObject.SetActive(true);
                        guidTextObj.anchoredPosition = new Vector2(0, 800);
                        guidText.text =
                            TFW.Localization.LocalizationMgr.Get(mData.mergeCount == 0
                                ? "dialog_desc_01"
                                : "dialog_desc_02");
                        MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
                            Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y),
                            MergerGuidMask.transform.position.z);
                        MergerGuidMask.SetActive(true);

                        SetPos(0);
                    }
                }

                return;
            }
            else if (mData.mergeCount == 2)
            {
                if (m_AudioIds.Count == 2)
                {
                    AudioManager.Instance.Stop(m_AudioIds[1]);
                    var cfg = Cfg.C.CAudioList.I(201003);
                    if (cfg != null)
                    {
                        int audioId = AudioManager.Instance.Play((AudioChannelType)cfg.Scene, cfg.Asset, cfg.Time,
                            cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
                        m_AudioIds.Add(audioId);
                    }
                }

                startGrids = GetGuideMergeGrid(102002);
                if (startGrids.Count >= 2)
                {
                    if (curGuiding <= 3 || lastItemCount != startGrids.Count || forceFind)
                    {
                        lastItemCount = startGrids.Count;
                        K3PlayerMgr.I.PlayerData.guiding = mData.mergeCount + 1;
                        curGuiding = K3PlayerMgr.I.PlayerData.guiding;

                        K3PlayerMgr.I.SavePlayerDataToServer();
                        GuideFingerObj?.SetActive(true);
                        DOTween.Kill(GuideFingerHand.transform);
                        guidTimer?.Stop();

                        GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
                        GuideFingerHand.transform.position = startGrids[0].transform.position;
                        GuideFingerHand.gameObject.SetActive(false);
                        guidTimer = NTimer.CountDownNoPool(0.4f, () =>
                        {
                            GuideFingerHand.gameObject.SetActive(true);
                            GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
                        });

                        guidTextObj.gameObject.SetActive(true);
                        guidTextObj.anchoredPosition = new Vector2(0, 800);
                        guidText.text = TFW.Localization.LocalizationMgr.Get("dialog_desc_03");
                        MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
                            Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y),
                            MergerGuidMask.transform.position.z);
                        MergerGuidMask.SetActive(true);

                        SetPos(0);
                    }
                }

                return;
            }
            else if (mData.mergeCount == 3)
            {
                if (m_AudioIds.Count == 3)
                {
                    AudioManager.Instance.Stop(m_AudioIds[2]);
                    var cfg = Cfg.C.CAudioList.I(201004);
                    if (cfg != null)
                    {
                        int audioId = AudioManager.Instance.Play((AudioChannelType)cfg.Scene, cfg.Asset, cfg.Time,
                            cfg.VolScale, 1, cfg.Priority, (AudioPlayMode)cfg.Typ, cfg.Mode == 1, null);
                        m_AudioIds.Add(audioId);
                    }
                }

                startGrids = GetGuideMergeGrid(102003, Immovable: true);
                startGrids.AddRange(GetGuideMergeGrid(102003));
                startGrids = startGrids.OrderByDescending(grid => grid.transform.localPosition.y).ToList();
                if (startGrids.Count >= 2)
                {
                    if (curGuiding <= 4 || lastItemCount != startGrids.Count || forceFind)
                    {
                        lastItemCount = startGrids.Count;
                        K3PlayerMgr.I.PlayerData.guiding = mData.mergeCount + 1;
                        curGuiding = K3PlayerMgr.I.PlayerData.guiding;

                        K3PlayerMgr.I.SavePlayerDataToServer();
                        GuideFingerObj?.SetActive(true);
                        DOTween.Kill(GuideFingerHand.transform);
                        guidTimer?.Stop();

                        GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
                        GuideFingerHand.transform.position = startGrids[0].transform.position;
                        GuideFingerHand.gameObject.SetActive(false);
                        guidTimer = NTimer.CountDownNoPool(0.4f, () =>
                        {
                            GuideFingerHand.gameObject.SetActive(true);
                            GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
                        });

                        guidTextObj.gameObject.SetActive(true);
                        guidTextObj.anchoredPosition = new Vector2(0, 800);
                        guidText.text = TFW.Localization.LocalizationMgr.Get("dialog_desc_04");
                        MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
                            Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y),
                            MergerGuidMask.transform.position.z);
                        MergerGuidMask.SetActive(true);

                        SetPos(0);
                    }
                }

                return;
            }
            else if (mData.mergeCount == 4)
            {
                startGrids = GetGuideMergeGrid(102004, true);
                if (startGrids.Count == 1)
                {
                    if (m_AudioIds.Count >= 4)
                    {
                        AudioManager.Instance.Stop(m_AudioIds[3]);
                    }

                    GuideFingerObj.SetActive(false);
                    CreateGuidMask.SetActive(false);
                    MergerGuidMask.SetActive(false);
                }

                // return;
            }

            if (K3PlayerMgr.I.PlayerData.guiding != 0)
            {
                K3PlayerMgr.I.PlayerData.guiding = 0;
                K3PlayerMgr.I.SavePlayerDataToServer();
            }
        }

        public void ShowLuckyEffect(int gridServerIdx, SpecialBoxData.Enum_LuckyType luckyType)
        {
            //if (mLuckEffectDic.ContainsKey(gridServerIdx))
            //{
            //    mLuckEffectDic[gridServerIdx].ShowEffect(luckyType);
            //}
            //暂时移除 之前写法比较耗
        }

        //public void CheckGuid1(bool forceFind = true)
        //{
        //    if (mData == null)
        //        return;

        //    GSGamePlay.TALog("guidingcheck", mData.BoxClickCount.ToString());

        //    if (mData.BoxClickCount < 2) //执行强引导
        //    {
        //        if (mData.BoxClickCount == 0)
        //        {
        //            var startGrids = GetGuideMergeGrid(10201);
        //            if (startGrids.Count >= 4)
        //            {
        //                if (curGuiding != 1 || lastItemCount != startGrids.Count || forceFind)
        //                {
        //                    lastItemCount = startGrids.Count;
        //                    K3PlayerMgr.I.PlayerData.guiding = 1;
        //                    curGuiding = K3PlayerMgr.I.PlayerData.guiding;

        //                    K3PlayerMgr.I.SavePlayerDataToServer();

        //                    GuideFingerObj.SetActive(true);
        //                    DOTween.Kill(GuideFingerHand.transform);
        //                    guidTimer?.Stop();

        //                    GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //                    GuideFingerHand.transform.position = startGrids[0].transform.position;
        //                    GuideFingerHand.gameObject.SetActive(false);
        //                    guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //                      {
        //                          GuideFingerHand.gameObject.SetActive(true);
        //                          GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //                      });

        //                    guidTextObj.gameObject.SetActive(true);
        //                    guidTextObj.anchoredPosition = new Vector2(0, 800);
        //                    guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue1");
        //                    MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //                    MergerGuidMask.SetActive(true);

        //                    SetPos(0);

        //                    K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_1_start" });
        //                    K3.K3GameEvent.I.TimeStart("guide");
        //                }
        //            }
        //            else if (startGrids.Count >= 2)
        //            {
        //                if (curGuiding != 2 || lastItemCount != startGrids.Count || forceFind)
        //                {
        //                    lastItemCount = startGrids.Count;
        //                    K3PlayerMgr.I.PlayerData.guiding = 2;
        //                    curGuiding = K3PlayerMgr.I.PlayerData.guiding;

        //                    K3PlayerMgr.I.SavePlayerDataToServer();

        //                    GuideFingerObj.SetActive(true);
        //                    DOTween.Kill(GuideFingerHand.transform);
        //                    guidTimer?.Stop();

        //                    GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //                    GuideFingerHand.transform.position = startGrids[0].transform.position;

        //                    GuideFingerHand.gameObject.SetActive(false);
        //                    guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //                    {
        //                        GuideFingerHand.gameObject.SetActive(true);
        //                        GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //                    });
        //                    guidTextObj.gameObject.SetActive(true);
        //                    guidTextObj.anchoredPosition = new Vector2(0, 800);
        //                    guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue2");
        //                    MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //                    MergerGuidMask.SetActive(true);

        //                    SetPos(0);

        //                    K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_1" });
        //                    K3.K3GameEvent.I.TimeEnd("guide", "1");

        //                    K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_2_start" });
        //                    K3.K3GameEvent.I.TimeStart("guide");
        //                }
        //            }
        //            else
        //            {
        //                startGrids = GetGuideMergeGrid(10202);
        //                if (startGrids.Count >= 2)
        //                {
        //                    if (curGuiding != 3 || lastItemCount != startGrids.Count || forceFind)
        //                    {
        //                        lastItemCount = startGrids.Count;
        //                        K3PlayerMgr.I.PlayerData.guiding = 3;
        //                        curGuiding = K3PlayerMgr.I.PlayerData.guiding;

        //                        K3PlayerMgr.I.SavePlayerDataToServer();

        //                        GuideFingerObj.SetActive(true);
        //                        DOTween.Kill(GuideFingerHand.transform);
        //                        guidTimer?.Stop();

        //                        GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //                        GuideFingerHand.transform.position = startGrids[0].transform.position;
        //                        GuideFingerHand.gameObject.SetActive(false);
        //                        guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //                        {
        //                            GuideFingerHand.gameObject.SetActive(true);
        //                            GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //                        });

        //                        guidTextObj.gameObject.SetActive(true);
        //                        guidTextObj.anchoredPosition = new Vector2(0, 800);
        //                        guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue6");
        //                        MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //                        MergerGuidMask.SetActive(true);

        //                        SetPos(0);

        //                        K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_2" });
        //                        K3.K3GameEvent.I.TimeEnd("guide", "2");

        //                        K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_3_start" });
        //                        K3.K3GameEvent.I.TimeStart("guide");
        //                    }
        //                }
        //                else
        //                {
        //                    // GuidCreatGood(4);
        //                }
        //            }
        //        }
        //        else
        //        {
        //            // GuidCreatGood(5);
        //        }
        //    }
        //    else
        //    {
        //        if (CSPlayer.I.UnlockItems.Contains(10205))
        //            MergeGreyItemGuide(forceFind);
        //        //if (K3PlayerMgr.I.OpenMenu(K3UnlockData.UnlockType.Shop) && CSPlayer.I.CurPhotoAlbum == 1 && CSPlayer.I.PhotoIdList.Count == 1)//尚未购买 首次免费道具
        //        //{
        //        //    if (!K3PlayerMgr.I.ShopData.FirstBought)
        //        //    {
        //        //        //商店有免费的道具可购买
        //        //        K3GuidMgr.I.ShopGuid();
        //        //    }
        //        //    else
        //        //    {
        //        //        if (!CSPlayer.I.UnlockItems.Contains(10802))
        //        //        {
        //        //            var startGrids = GetGuideMergeGrid(10801);
        //        //            if (startGrids.Count > 1)
        //        //            {
        //        //                K3GuidMgr.I.shopGuiding = true;
        //        //            }
        //        //        }
        //        //    }
        //        //}

        //        //if (K3GuidMgr.I.shopGuiding)
        //        //{
        //        //    var startGrids = GetGuideMergeGrid(10801);
        //        //    if (startGrids.Count == 2)
        //        //    {
        //        //        if (curGuiding != 30 || lastItemCount != startGrids.Count || forceFind)
        //        //        {
        //        //            curGuiding = 30;
        //        //            lastItemCount = startGrids.Count;
        //        //            GuideFingerObj.SetActive(true);
        //        //            DOTween.Kill(GuideFingerHand.transform);
        //        //            guidTimer?.Stop();

        //        //            CreateGuidMask.SetActive(false);

        //        //            GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //        //            GuideFingerHand.transform.position = startGrids[0].transform.position;
        //        //            GuideFingerHand.gameObject.SetActive(false);
        //        //            guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //        //            {
        //        //                GuideFingerHand.gameObject.SetActive(true);
        //        //                GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //        //            });

        //        //            guidTextObj.gameObject.SetActive(false);
        //        //            guidTextObj.anchoredPosition = new Vector2(0, 800);
        //        //            guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue1");
        //        //            MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //        //            MergerGuidMask.SetActive(true);

        //        //            SetPos(startGrids[1].Point.y - startGrids[0].Point.y);
        //        //        }
        //        //    }
        //        //    else
        //        //    {
        //        //        MergerGuidMask.SetActive(false);
        //        //    }

        //        //    var start2Grids = GetGuideMergeGrid(10802);
        //        //    if (startGrids.Count > 0)
        //        //    {
        //        //        StopCreatGoodGuide(false);
        //        //        K3GuidMgr.I.shopGuiding = false;
        //        //    }

        //        //    return;
        //        //}

        //        if (K3PlayerMgr.I.PlayerData.guiding > 0)
        //        {
        //            var startGrids = GetGuideMergeGrid(10203, true);
        //            if (startGrids.Count >= 2)
        //            {
        //                if (curGuiding != 6 || lastItemCount != startGrids.Count || forceFind)
        //                {
        //                    lastItemCount = startGrids.Count;
        //                    K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_5" });

        //                    K3GameEvent.I.TimeEnd("guide", "5");

        //                    K3PlayerMgr.I.PlayerData.guiding = 6;

        //                    K3PlayerMgr.I.SavePlayerDataToServer();

        //                    curGuiding = K3PlayerMgr.I.PlayerData.guiding;
        //                    //5_1
        //                    GuideFingerObj.SetActive(true);
        //                    DOTween.Kill(GuideFingerHand.transform);
        //                    guidTimer?.Stop();

        //                    CreateGuidMask.SetActive(false);

        //                    GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //                    GuideFingerHand.transform.position = startGrids[0].transform.position;
        //                    GuideFingerHand.gameObject.SetActive(false);
        //                    guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //                    {
        //                        GuideFingerHand.gameObject.SetActive(true);
        //                        GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //                    });

        //                    guidTextObj.gameObject.SetActive(false);
        //                    guidTextObj.anchoredPosition = new Vector2(0, 800);
        //                    guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue1");
        //                    MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //                    MergerGuidMask.SetActive(true);

        //                    SetPos(startGrids[1].Point.y - startGrids[0].Point.y);

        //                    K3GameEvent.I.BiLog(new GuidEvent() { EventKey = $"guide_5_1_start" });
        //                    K3GameEvent.I.TimeStart($"guide");
        //                }

        //                return;
        //            }
        //            else
        //            {
        //                startGrids = GetGuideMergeGrid(10204, true);
        //                if (startGrids.Count >= 2)
        //                {
        //                    if (curGuiding != 7 || lastItemCount != startGrids.Count || forceFind)
        //                    {
        //                        lastItemCount = startGrids.Count;
        //                        K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_5_1" });
        //                        K3GameEvent.I.TimeEnd($"guide", "5_1");

        //                        K3PlayerMgr.I.PlayerData.guiding = 7;
        //                        curGuiding = K3PlayerMgr.I.PlayerData.guiding;

        //                        K3PlayerMgr.I.SavePlayerDataToServer();
        //                        //5_2
        //                        GuideFingerObj.SetActive(true);
        //                        DOTween.Kill(GuideFingerHand.transform);
        //                        guidTimer?.Stop();
        //                        CreateGuidMask.SetActive(false);

        //                        GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //                        GuideFingerHand.transform.position = startGrids[0].transform.position;
        //                        GuideFingerHand.gameObject.SetActive(false);
        //                        guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //                        {
        //                            GuideFingerHand.gameObject.SetActive(true);
        //                            GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //                        });

        //                        guidTextObj.gameObject.SetActive(false);
        //                        guidTextObj.anchoredPosition = new Vector2(0, 800);
        //                        guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue1");
        //                        MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x, Math.Max(startGrids[0].transform.position.y, startGrids[1].transform.position.y), MergerGuidMask.transform.position.z);
        //                        MergerGuidMask.SetActive(true);

        //                        SetPos(startGrids[1].Point.y - startGrids[0].Point.y);

        //                        K3GameEvent.I.BiLog(new GuidEvent() { EventKey = $"guide_5_2_start" });
        //                        K3GameEvent.I.TimeStart($"guide");
        //                    }

        //                    return;
        //                }
        //                else
        //                {
        //                    startGrids = GetGuideMergeGrid(10205, true);
        //                    if (startGrids.Count == 1)
        //                    {
        //                        K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_5_2" });
        //                        K3GameEvent.I.TimeEnd($"guide", "5_2");

        //                        GuideFingerObj.SetActive(false);
        //                        CreateGuidMask.SetActive(false);
        //                        MergerGuidMask.SetActive(false);
        //                    }
        //                    else
        //                    {
        //                        return;
        //                    }
        //                }
        //            }
        //        }

        //        if (K3PlayerMgr.I.PlayerData.guiding != 0)
        //        {
        //            K3PlayerMgr.I.PlayerData.guiding = 0;
        //            K3PlayerMgr.I.SavePlayerDataToServer();
        //        }
        //    }
        //}

        public void SetPos(int chaju)
        {
            if (chaju == 0)
            {
                //MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75);
                //MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -75);
            }
            else
            {
                if (chaju > 0)
                {
                    //MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75);
                    //MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -75 - 150 * chaju);
                }
                else
                {
                    //MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75 - 150 * chaju);
                    //MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -75);
                }
            }

            MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75);
            MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition =
                new Vector2(0, -1800);
        }

        #endregion 数据刷新显示

        #region 数据刷帧处理

        private bool openToSyncing = false;

        protected internal override void OnUpdate(float deltaTime)
        {
            base.OnUpdate(deltaTime);


            if (GameObject == null || mData == null)
                return;

            //Update_Skill();

            UpdatePerSecondFirstRecharge(Time.deltaTime);

            K3PlayerMgr.I.UpdateEnergy();

            UpdateAuto();


            AutoMergeDOing();

            if (K3PlayerMgr.I.PlayerData.guiding ==
                0) //相册未激活不显示~~&& Logic.CSPlayer.I.PhotoIdList.Count > 0 //mData?.clickCount >= 2 &&
            {
                if (!UIGuid.HaveWakeGuid())//!PopupManager.I.AnyPopup &&
                {
                    // UnityEngine.Debug.Log($"LogDebug - m_IsGuideFinish = {m_IsGuideFinish}, {Time.time - lastShowMergeGuidetimer}");
                    if (m_IsGuideFinish && (Time.time - lastShowMergeGuidetimer) >= showMergeGuodeinterval)
                    {
                        ShowGuideMerge();
                    }
                }
                else
                {
                    StopCreatGoodGuide(false);
                }
            }

            if (btnUnlock.activeSelf)
            {
                if (curGood != null)
                {
                    deletetimerText.text = curGood.GetDeleteTime();
                }
            }

            if (curSpecialBox != null)
            {
                //if (curGood.mData.goodType == 1)
                //{
                //    curGood.mData.GetBuffTime(out var totalTime, out var lastTime);
                //    if (lastTime > 0)
                //    {
                //        TimeSpan cdTimer = new TimeSpan(0, 0, (int)lastTime);
                //        heroBuffTimeText.text = cdTimer.ToString(@"h\:mm\:ss");
                //    }
                //    else
                //    {
                //        heroBuffTimeText.text = "";
                //    }
                //}
                //else
                //{
                //if (curSpecialBox.CurBoxUseCd()>0)
                //{
                //    TimeSpan cdTimer = TimeSpan.FromSeconds(curSpecialBox.CurBoxUseCd());
                //    BoxItemRestCDText.text = cdTimer.ToString(@"h\:mm\:ss");
                //}
                //}
            }

            //if (PopupManager.I.AnyPopup_NOLayer)
            //{
            //    if (openToSyncing)
            //    {
            //        openToSyncing = false;

            //        CSPlayer.I.SyncMerge();
            //    }
            //}
            //else if (K3PlayerMgr.I.PlayerData.guiding == 0)//mData.clickCount >= 2 &&
            //{
            //    openToSyncing = CSPlayer.I.k3ToServerData.HaveDataSync;

            //    //if (CSPlayer.I.CurPhotoAlbum > 1 || CSPlayer.I.PhotoIdList.Count > 0)
            //    //{
            //    //    //AutoMergeDOing();
            //    //}
            //}

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            if (Input.GetKeyDown(KeyCode.C))
            {
                if (clickTime > Time.time)
                {
                    //2S
                    if (curGood)
                    {
                        CopyGood(curGood);
                    }
                }
                else
                    clickTime = Time.time + 2f;
            }

            if (Input.GetKeyDown(KeyCode.Space))
            {
                if (clickTime > Time.time)
                {
                    //2S
                    CurTaskAutoTODO();
                }
                else
                    clickTime = Time.time + 2f;
            }
#endif

            //if (Input.touchCount > 0)
            //{
            //    var touch0 = Input.GetTouch(0);
            //    if (touch0.phase == TouchPhase.Ended)
            //    {
            //        DoTouch(touch0.position);
            //    }
            //}
            //else
            //{
            //    if (Input.GetMouseButtonUp(0))
            //    {
            //        DoTouch(Input.mousePosition);
            //    }
            //}
        }

        //private void DoTouch(Vector2 touchPos)
        //{
        //    var uiEventSystem = EventSystem.current;
        //    if (uiEventSystem != null)
        //    {
        //        var uiPointerEventData = new PointerEventData(uiEventSystem);
        //        uiPointerEventData.position = touchPos;
        //        var uiRaycastResultCache = new List<RaycastResult>();
        //        try
        //        {
        //            uiEventSystem.RaycastAll(uiPointerEventData, uiRaycastResultCache);
        //        }
        //        catch (System.Exception ex)
        //        {
        //        }

        //        if (uiRaycastResultCache.Count > 0)
        //        {
        //            if (uiRaycastResultCache[0].gameObject == btnCreatGood)
        //            {
        //                if (MergeGuiding)
        //                    return;

        //                CreatGood();
        //                StopWaitShowGuideMerge();
        //            }
        //            else
        //            {
        //                UnityEngine.Debug.LogWarning($"未点击CreateGood  {Time.time} :: {uiRaycastResultCache[0].gameObject.name}");
        //            }
        //        }
        //        else
        //        {
        //            UnityEngine.Debug.LogWarning($"RaycastResultCaches Is Null  {Time.time}");
        //        }
        //    }
        //}

        public bool CheckMergeGrey(int id)
        {
            if (MergeGreyGridItem?.Length == 3)
            {
                if (MergeGreyItemGuiding_1 && id != MergeGreyGridItem[1])
                    return true;

                if (MergeGreyItemGuiding && id != MergeGreyGridItem[2])
                    return true;
            }

            return false;
        }

        public int[] MergeGreyGridItem;
        public bool MergeGreyItemGuiding_1 = false;
        public bool MergeGreyItemGuiding = false;

        //private void MergeGreyItemGuide(bool forceFind)
        //{
        //    if (!K3GuidMgr.I.CheckGuidToDo(K3GuidMgr.K3GuidType.MergeGreyItem2))//CSPlayer.I.PhotoIdList.Count == 0 ||
        //    {
        //        MergeGreyItemGuiding = false;
        //        MergeGreyItemGuiding_1 = false;
        //        return;
        //    }

        //    if (MergeGreyGridItem == null)
        //    {
        //        MergeGreyGridItem = MetaConfig.GreyItemGuideMerge;
        //    }

        //    if (MergeGreyGridItem.Length != 3)
        //    {
        //        //配置有误，默认三个灰色道具进行引导
        //        return;
        //    }

        //    var startGrids_10303 = GetGuideMergeGrid(MergeGreyGridItem[0]);
        //    if (startGrids_10303.Count >= 1)
        //    {
        //        MergeGreyItemGuiding = false;
        //        MergeGreyItemGuiding_1 = false;
        //        K3GuidMgr.I.SetGuidToDo(K3GuidMgr.K3GuidType.MergeGreyItem2);
        //        StopCreatGoodGuide();
        //        //ShowCreatGoodGuide();
        //    }
        //    var startGrids1 = GetGuideMergeGrid(MergeGreyGridItem[1]);
        //    if (startGrids1.Count >= 1)
        //    {
        //        MergeGreyItemGuiding = false;
        //        K3GuidMgr.I.SetGuidToDo(K3GuidMgr.K3GuidType.MergeGreyItem1);
        //        var startGrids2 = GetGuideMergeGrid(MergeGreyGridItem[1], false, true);
        //        if (K3GuidMgr.I.CheckGuidToDo(K3GuidMgr.K3GuidType.MergeGreyItem2) && startGrids2.Count > 0 && (!MergeGreyItemGuiding_1 || forceFind))
        //        {
        //            MergeGreyItemGuiding_1 = true;
        //            curGuiding = K3PlayerMgr.I.PlayerData.guiding;
        //            GuideFingerObj.SetActive(true);
        //            DOTween.Kill(GuideFingerHand.transform);
        //            guidTimer?.Stop();

        //            GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //            GuideFingerHand.transform.position = startGrids1[0].transform.position;
        //            GuideFingerHand.gameObject.SetActive(false);
        //            guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //            {
        //                GuideFingerHand.gameObject.SetActive(true);
        //                GuideFingerHand.transform.DOMove(startGrids2[0].transform.position, 1f).SetLoops(-1);
        //            });
        //            guidTextObj.gameObject.SetActive(true);
        //            guidTextObj.anchoredPosition = new Vector2(0, 800);
        //            guidText.text = TFW.Localization.LocalizationMgr.Get("Ui_Guide_GreyMerge_txt");
        //            MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
        //               Mathf.Max(startGrids1[0].transform.position.y, startGrids2[0].transform.position.y), MergerGuidMask.transform.position.z);
        //            MergerGuidMask.SetActive(true);

        //            SetPos(0);

        //            MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75);
        //            MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -75);
        //            K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_MergeGreyItem_2_start" });
        //            K3GameEvent.I.TimeStart("guide");
        //        }
        //    }

        //    var startGrids = GetGuideMergeGrid(MergeGreyGridItem[2]);
        //    if (startGrids.Count >= 2)
        //    {
        //        if (K3GuidMgr.I.CheckGuidToDo(K3GuidMgr.K3GuidType.MergeGreyItem1) && (!MergeGreyItemGuiding || forceFind))
        //        {
        //            MergeGreyItemGuiding = true;
        //            curGuiding = K3PlayerMgr.I.PlayerData.guiding;
        //            GuideFingerObj.SetActive(true);
        //            DOTween.Kill(GuideFingerHand.transform);
        //            guidTimer?.Stop();

        //            GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //            GuideFingerHand.transform.position = startGrids[0].transform.position;
        //            GuideFingerHand.gameObject.SetActive(false);
        //            guidTimer = NTimer.CountDownNoPool(0.4f, () =>
        //            {
        //                GuideFingerHand.gameObject.SetActive(true);
        //                GuideFingerHand.transform.DOMove(startGrids[1].transform.position, 1f).SetLoops(-1);
        //            });
        //            guidTextObj.gameObject.SetActive(false);
        //            // guidTextObj.gameObject.SetActive(true);
        //            // guidTextObj.anchoredPosition = new Vector2(0, 800);
        //            // guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue1");

        //            var grid = startGrids[1].transform.position.y > startGrids[0].transform.position.y
        //                ? startGrids[1]
        //                : startGrids[0];
        //            MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
        //                grid.transform.position.y, MergerGuidMask.transform.position.z);
        //            MergerGuidMask.SetActive(true);

        //            SetPos(0);

        //            K3GameEvent.I.BiLog(new GuidEvent() { EventKey = "guide_MergeGreyItem_1_start" });
        //            K3GameEvent.I.TimeStart("guide");
        //        }
        //    }
        //}

        //private void SellItemGuid()
        //{
        //    //if (GetFreeGrids(1).Count <= 0)
        //    //{
        //    //    if (K3GuidMgr.I.CheckGuidToDO(K3GuidMgr.K3GuidType.SellGood))
        //    //    {
        //    //        var toList = mGoodDic.Values.Where(a => CanGuidSell(a) && K3PlayerMgr.I.AreaData.NoPoint(new Vector2(a.mPoint.x, a.mPoint.y)) && a.CanClick && !a.locked && !a.mData.Immovable).OrderBy(a => a.Info.Level).ToList();

        //    //        if (toList.Count > 0)
        //    //        {
        //    //            var guidData = new UIGuidData();

        //    //            if (curGood != null && CanGuidSell(curGood) && curGood.Info.Level == toList[0].Info.Level)
        //    //            {
        //    //            }
        //    //            else
        //    //            {
        //    //                guidData.guidItems.Add(new UIGuidItem() // //找到一个低等级的 道具
        //    //                {
        //    //                    UIName = "UIMerge",
        //    //                    slide = true,
        //    //                    UIItemGa = toList[0].gameObject,
        //    //                });
        //    //            }

        //    //            guidData.guidItems.Add(new UIGuidItem()
        //    //            {
        //    //                UIName = "UIMerge",
        //    //                slide = true,
        //    //                UIItemGa = btnSell,
        //    //            });

        //    //            UIGuid.StartGuid(guidData);
        //    //        }
        //    //    }
        //    //}
        //}

        private bool CanGuidSell(UIMergeGood good)
        {
            return good.Info.Code < 6 && good.Info.Type == 1 && good.Info.Price != -1;
        }

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
        private float clickTime;

        public static bool douclickCopyGood = false;

        public void CopyGood(UIMergeGood mergeGood)
        {
            var list = CSVItem.GetItemInfoById(mergeGood.GetInfo().id);

            List<UIMergeGrid> grids = GetFreeGrids(1);

            if (grids.Count > 0)
            {
                UIMergeGrid gride = grids[0];
                UIMergeGood good = GetGood();
                good.transform.position = mergeGood.transform.position;
                good.Init(gride, list, MoveGoodRoot, this, false, 0, true, 0);

                var mStep = CSPlayer.I.k3ToServerData.AddGMMergeStep();
                mStep.assets.Add(good.mPoint.ServerIndex, K3PlayerMgr.I.TOID(good.mData));

                mStep.StepRecordEnd();
            }
        }

#endif

        #endregion 数据刷帧处理

        //[PopupEvent(TEventType.MainCitySkillLevelUp)]
        //private void MainCitySkillLevelUpRefresh(object[] pars)
        //{
        //    K3PlayerMgr.I.CheckCityUse(mGridDic);
        //}

        //[PopupEvent(TEventType.PhotoChange)]
        //private void PhotoChangeRefresh(object[] pars)
        //{
        //    RefreshLockMenu(null);
        //    K3PlayerMgr.I.CheckPhotoUse(mGridDic);
        //}

        //[PopupEvent(TEventType.K3ClickBox)]
        //private void ClickBoxRefresh(object[] pars)
        //{
        //    if (MergeGuiding)
        //        return;

        //    CreatGood();
        //    StopWaitShowGuideMerge();
        //}

        //[PopupEvent(TEventType.K3GridDataRefresh)]
        //public void PhotoTaskRefresh(object[] pars)
        //{
        //    RefreshTaskEffect();

        //    //K3PlayerMgr.I.CheckHaveUnlockPhoto(true);
        //}

        //[PopupEvent(TEventType.K3TaskDataRefresh)]
        //public void PhotoTaskDataRefresh(object[] pars)
        //{
        //    RefreshTaskEffect();
        //}

        //private void RefreshTaskEffect()
        //{
        //    //taskTopInfo.RefreshData();
        //    //var canReward = curPhotoTask?.CanUpdate(out var goReward) ?? false;

        //    //manzu_effect.SetActive(canReward);

        //    //int taskRedPointNum = 0;

        //    //foreach (var item in K3PlayerMgr.I.TaskData.TaskItemIDs)
        //    //{
        //    //    if (item.state == 0)
        //    //    {
        //    //        if (string.IsNullOrEmpty(PlayerAssetsMgr.I.HaveAssets(Cfg.C.CMergeTask.I(item.id)?.TaskTarget)))
        //    //        {
        //    //            taskRedPointNum++;
        //    //        }
        //    //    }
        //    //}

        //    //taskRedPoint.SetActive(taskRedPointNum > 0);
        //    //taskRedPointText.text = taskRedPointNum.ToString();
        //}

        public void GridDataRefresh(cspb.MergeChangeNtf mergeAssetNtf)
        {
            //D.Warning?.Log($"【棋盘】棋盘刷新！！ MergeChangeNtf");
            if (mergeAssetNtf?.assets.Count > 0)
            {
                foreach (var asset in mergeAssetNtf.assets)
                {
                    //新增或者消耗
                    var grid = mGridDic[asset.Key];

                    {
                        int id = 0;
                        long ts = 0;

                        if (asset.Value != 0)
                        {
                            K3PlayerMgr.I.GetID(asset.Value, out id, out ts);
                        }

                        if (id == 0)
                        {
                            //Debug.LogError($"棋盘即将同步清除： {grid.Point.ServerIndex}");

                            if (grid.GetData().isFull)
                            {
                                //Debug.LogError($"棋盘同步清除成功：{grid.Point.ServerIndex} ==>>> {grid.Good.Info.id}");

                                grid.Good.DisPos(true);
                            }
                        }
                        else if (id > 0)
                        {
                            UIMergeGoodData goodData = new UIMergeGoodData();
                            goodData.id = id;

                            if (Cfg.C.CK3Item.I(id).Type < 5 && ts > 0)
                            {
                                goodData.locked = true;
                                goodData.creatTamp = ts;
                            }

                            if (!grid.GetData().isFull || grid.Good == null)
                            {
                                //Debug.LogError($"创建新：{goodData.id}");

                                CreatGood(goodData.id, goodData.goodType, grid, goodData.locked,
                                    goodData.creatTamp, goodData.boxCdTamp, goodData.BoxTimes, goodData.Immovable,
                                    goodData.BoxTimes_Buff);
                            }
                            else if (grid.GetData().goodData.id != goodData.id)
                            {
                                //Debug.LogError($"替换新：{goodData.id}");
                                grid.Good.DisPos(true);

                                CreatGood(goodData.id, goodData.goodType, grid, goodData.locked,
                                    goodData.creatTamp, goodData.boxCdTamp, goodData.BoxTimes, goodData.Immovable,
                                    goodData.BoxTimes_Buff);
                            }
                            else if (grid.GetData().goodData.id == id)
                            {
                                if (grid.Good.Info.Type < 5 && ts > 0)
                                {
                                    grid.Good.GetData().locked = true;
                                    grid.Good.GetData().creatTamp = ts;
                                }

                                grid.UpdateData();
                            }
                        }
                        else
                        {
                            UIMergeGoodData goodData = new UIMergeGoodData();
                            goodData.id = Mathf.Abs(id);
                            goodData.Immovable = true;

                            if (Cfg.C.CK3Item.I(goodData.id).Type < 5 && ts > 0)
                            {
                                goodData.locked = true;
                                goodData.creatTamp = ts;
                            }

                            if (!grid.GetData().isFull || grid.Good == null)
                            {
                                //Debug.LogError($"创建新：{goodData.id}");

                                CreatGood(goodData.id, goodData.goodType, grid, goodData.locked,
                                    goodData.creatTamp, goodData.boxCdTamp, goodData.BoxTimes, goodData.Immovable,
                                    goodData.BoxTimes_Buff);
                            }
                            else if (grid.GetData().goodData.id != goodData.id)
                            {
                                //Debug.LogError($"替换新：{goodData.id}");
                                grid.Good.DisPos(true);

                                CreatGood(goodData.id, goodData.goodType, grid, goodData.locked,
                                    goodData.creatTamp, goodData.boxCdTamp, goodData.BoxTimes, goodData.Immovable,
                                    goodData.BoxTimes_Buff);
                            }
                            else if (grid.GetData().goodData.id == goodData.id)
                            {
                                if (grid.Good.Info.Type < 5 && ts > 0)
                                {
                                    grid.Good.GetData().locked = true;
                                    grid.Good.GetData().creatTamp = ts;
                                }

                                grid.UpdateData();
                            }
                        }
                    }
                }

                ShowChekBox(false);

                EventMgr.FireEvent(TEventType.RefreshAssetAck); //K3 道具刷新 也是资源

                //EventMgr.FireEvent(TEventType.K3GridDataRefresh);
            }
        }

        [PopupEvent(TEventType.K3BoxNtf)]
        private void UpdateBox(object[] objs)
        {
            //RefreshRedPointNum();

            if (!StateMerge)
            {
                EventMgr.FireEvent(TEventType.K3UIMerge_SelectHeroType, selectMergeType);
            }

        }


        public UIMergeGood CreatGood(ItemInfo info, Transform targteTr, bool locked, UIMergeGrid grid = null,
            string reson = "")
        {
            List<UIMergeGrid> grids = GetFreeGrids(1);
            if (grids.Count > 0)
            {
                UIMergeGrid gride = grids[0];
                if (grid != null)
                {
                    gride = grid;
                }

                UIMergeGood good = GetGood();
                good.transform.position = targteTr.position;
                good.Init(gride, info, MoveGoodRoot, this, locked, 0, true, 0);

                if (!string.IsNullOrEmpty(reson))
                {
                    //var createEvent = new CreateEvent() { EventKey = "createevent" };
                    //createEvent.Properties.Add("from", reson);
                    //createEvent.Properties.Add("id", good.Info.id);
                    //K3GameEvent.I.TaLog(createEvent);

                    //CSPlayer.I.k3ToServerData.AddCreateEvent(reson, good.Info.id);
                }

                return good;
            }

            return null;
        }

        //private void CreatGood()
        //{
        //    if (mData == null)
        //    {
        //        return;
        //    }

        //    if (mData.clickCount >= 2 && !CSPlayer.I.UnlockItems.Contains(10205))
        //        return; //引导中不可生成道具

        //    UpdateBubblePanelActive();
        //    int boxCost = CSPlayer.I.BaseBox.BoxCost;
        //    if (!K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, boxCost))
        //    {
        //        PopupManager.I.ShowDialog<UIHyposthenia>();
        //        //if (BoxAni != null)
        //        //    BoxAni.SetTrigger("shake");
        //        return;
        //    }

        //    var ItemInfos = CSVConfig.GetOpenBox(mData.clickCount);

        //    bool openOpenBoxCfg = ItemInfos.Count > 0;//走的OpenBox 常量道具

        //    if (ItemInfos.Count == 0)
        //    {
        //        ItemInfos = CSPlayer.I.BaseBox.GetItems();
        //    }

        //    if (ItemInfos.Count > 0)
        //    {
        //        int index = 0;
        //        if (!K3PlayerMgr.I.CheckGoodFull(ItemInfos.Count, false))
        //        {
        //            //if (BoxAni != null)
        //            //    BoxAni.SetTrigger("shake");
        //            FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_BoardFull"));
        //            return;
        //        }

        //        List<UIMergeGrid> grids = GetFreeGrids(ItemInfos.Count);
        //        if (grids.Count > 0)
        //        {
        //            K3PlayerMgr.I.UseMoney(MoneyType.Energy, boxCost, "createitem");
        //            var boxStep = CSPlayer.I.k3ToServerData.AddBoxOpenStep();
        //            for (int i = 0; i < grids.Count; i++)
        //            {
        //                UIMergeGrid gride = grids[i];
        //                UIMergeGood good = GetGood();

        //                GameAudio.PlayAudio(6);
        //                good.transform.position = btnCreatGood.transform.position;
        //                good.Init(gride, ItemInfos[i], MoveGoodRoot, this, false, 0, true, i * 0.15f);
        //                if (isCancelState)//出售撤回状态
        //                {
        //                    ShowChekBox(false);
        //                }

        //                boxStep.assets.Add(good.mPoint.ServerIndex, K3PlayerMgr.I.TOID(good.mData));

        //                if (openOpenBoxCfg)
        //                {
        //                    boxStep.guideId = K3PlayerMgr.I.PlayerData.GetMergeSyncGuidParam();
        //                }
        //                //var createEvent = new CreateEvent() { EventKey = "createevent" };
        //                //createEvent.Properties.Add("from", "box");
        //                //createEvent.Properties.Add("id", good.Info.id);
        //                //K3GameEvent.I.TaLog(createEvent);

        //                //CSPlayer.I.k3ToServerData.AddCreateEvent("box", good.Info.id);
        //            }

        //            boxStep.StepRecordEnd();
        //            //UnityEngine.Debug.LogWarning($"CreateGood!");

        //            boxOpen = true;
        //            if (BoxAni != null)
        //                BoxAni.SetBool("boxOpen", boxOpen);

        //            boxOpen = false;

        //            mData.AddClickCount();
        //            //K3PlayerMgr.I.SaveDataLocal();
        //            CoroutineMgr.StopCoroutine("boxOpen");

        //            closeTime = Time.time + grids.Count * 0.15f + 0.25f;

        //            CoroutineMgr.StartCoroutine(CloseBox(), "boxOpen");
        //        }
        //        else
        //        {
        //            if (BoxAni != null)
        //                BoxAni.SetTrigger("shake");
        //            FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_BoardFull"));
        //        }
        //    }

        //    CheckGuid();

        //    //K3.K3PlayerMgr.I.SaveDataLocal();
        //}

        private float closeTime;

        //private IEnumerator CloseBox()
        //{
        //    while (closeTime > Time.time)
        //    {
        //        yield return null;
        //    }

        //    //if (BoxAni != null)
        //    //{
        //    //    BoxAni.SetBool("boxOpen", false);
        //    //}
        //}

        private void CreatGood(int id, int itemType, UIMergeGrid grid, bool locked, long creatTamp, long boxCdTamp,
            int boxtimes, bool Immovable, int boxtimes_buff)
        {
            ItemInfo info;

            info = CSVItem.GetItemInfoById(id);

            info.Immovable = Immovable;
            UIMergeGood good = GetGood();
            good.Init(grid, info, MoveGoodRoot, this, locked, creatTamp, false, 0.2f, boxCdTamp, boxtimes,
                boxtimes_buff);
        }

        public bool CreatGood(UIMergeGood mergeGood)
        {
            if (mergeGood.mData.BoxTimes <= 0)
            {
                return false;
            }

            var list = CSVItem.GetItemInfosByItemBox(mergeGood.GetInfo().ItemBox.BoxID, mergeGood.mData.id);
            if (list?.Count > 0 == true)
            {
                List<UIMergeGrid> grids = GetFreeGrids(list.Count);
                for (int i = 0; i < grids.Count; i++)
                {
                    if (grids[i] != null)
                    {
                        UIMergeGrid grid = grids[i];
                        UIMergeGood good = GetGood();
                        good.transform.position = mergeGood.transform.position;
                        good.Init(grid, list[i], MoveGoodRoot, this, false, 0, true, i * 0.15f);
                        //var createEvent = new CreateEvent() { EventKey = "createevent" };
                        //createEvent.Properties.Add("from", "item");
                        //createEvent.Properties.Add("id", good.Info.id);
                        //K3GameEvent.I.TaLog(createEvent);

                        //CSPlayer.I.k3ToServerData.AddCreateEvent("item", good.Info.id);
                    }
                }
            }

            if (mergeGood.mData.BoxTimes_Buff > 0)
            {
                mergeGood.mData.BoxTimes_Buff--;
            }
            else if (mergeGood.mData.BoxTimes > 0)
            {
                mergeGood.mData.BoxTimes--;
            }

            //K3PlayerMgr.I.SaveDataLocal();
            return true;
            // return mergeGood.mData.BoxTimes == 0;
        }

        public bool CreatGood(UIMergeSpecialBox boxData, out MStep mStep)
        {
            mStep = null;


            //ShowTopInfo(boxData);

            if (K3PlayerMgr.I.CheckGoodFull(boxData.mBox.BoxCfg.Num))
            {
                if (K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, boxData.mBox.BoxCfg.EnergyCost))
                {
                    if (boxData.mBox.curCapacity <= 0)
                    {
                        return false;
                    }

                    var list = boxData.mBox.GetItems(out SpecialBoxData.Enum_LuckyType luckyType);
                    if (list?.Count > 0 == true)
                    {
                        var createdGoods = new List<UIMergeGood>();
                        var createdGoodGrids = new List<UIMergeGrid>();
                        var mergeBeActiveSkill_AddEnergy = false;
                        CCSSkillForPassive addEnergySkill = null;
                        var mergeBeActiveSkill_AddGood = false;
                        CCSSkillForPassive addGoodSkill = null;

                        mStep = CSPlayer.I.k3ToServerData.AddBoxOpenStep();
                        <EMAIL>(boxData.mBox.CfgID - 2);

                        List<UIMergeGrid> grids = GetFreeGrids(list.Count);
                        for (int i = 0; i < grids.Count; i++)
                        {
                            if (grids[i] != null)
                            {
                                UIMergeGrid grid = grids[i];
                                UIMergeGood good = GetGood();
                                good.transform.position = boxData.transform.position;

                                //首次创建士兵时按照指定规则进行生成

                                if (list[i].Code > 20)
                                {
                                    var list_str = MetaConfig.InitialRewardSoldiers;
                                    int max = list_str.Length;
                                    int CreatSoliderIndex =
                                        PlayerPrefs.GetInt("CreatSoliderIndex_" + LPlayer.I.PlayerID, 0);
                                    if (CreatSoliderIndex < max)
                                    {
                                        list[i] = new ItemInfo(Cfg.C.CK3Item.RawDict()[list_str[CreatSoliderIndex]]);
                                        mStep.guideId = 5000 + CreatSoliderIndex;
                                        CreatSoliderIndex++;
                                        PlayerPrefs.SetInt("CreatSoliderIndex_" + LPlayer.I.PlayerID,
                                            CreatSoliderIndex);
                                    }
                                }

                                NiceVibrationsCtrl.Haptic_CreateItems(i * 0.15f);

                                good.Init(grid, list[i], MoveGoodRoot, this, false, 0, true, i * 0.15f,
                                    luckyType: luckyType);
                                createdGoods.Add(good);
                                createdGoodGrids.Add(grid);

                                mStep.assets.Add(good.mPoint.ServerIndex, K3PlayerMgr.I.TOID(good.mData));
                            }
                        }

                        if (boxData.mBox.curCapacity > 0 && !boxData.mBox.TestHeroCfgID) //试用期间不进行容量扣减
                        {
                            boxData.mBox.curCapacity = boxData.mBox.curCapacity - 1;
                        }

                        //if (boxData.mBox.heroCfgIDs.Count > 0 && boxData.mBox.heroCfgIDs[0] > 0)
                        //{
                        //    boxData.mBox.curHeroPower += boxData.mBox.BoxCfg.Energy;
                        //}

                        int mPriveBoxIndex = 0;


                        var mHero = boxData.mBox.GetHeroData();

                        if (mHero.HeroMergeSkillDatas.Count > 0)
                        {
                            foreach (var item in mHero.HeroMergeSkillDatas)
                            {
                                if (item.unlockStar > mHero.Star ||
                                    item.mType != Game.Data.HeroData.HeroSkillData.SkillType.MergePassive)
                                    continue;

                                var skillCfg = Cfg.C.CCSSkillForPassive.I(item.id);
                                if (skillCfg?.Type == 2) //增加能量
                                {
                                    int randomValue = Random.Range(0, 100);
                                    if (randomValue <= skillCfg.Probability)
                                    {
                                        <EMAIL>(2);

                                        //boxData.mBox.curHeroPower += skillCfg.Param;

                                        mergeBeActiveSkill_AddEnergy = true;
                                        addEnergySkill = skillCfg;
                                    }
                                }
                                else if (skillCfg.Type == 1) //额外获得道具
                                {
                                    int randomValue = Random.Range(0, 100);
                                    if (randomValue <= skillCfg.Probability)
                                    {
                                        <EMAIL>(1);
                                        mergeBeActiveSkill_AddGood = true;
                                        addGoodSkill = skillCfg;

                                        //后续需要判断是否放进临时仓库 现在是触发到棋盘里面
                                        grids = GetFreeGrids(skillCfg.Param);
                                        for (int j = 0; j < grids.Count; j++)
                                        {
                                            if (grids[j] != null && list.Count > 0)
                                            {
                                                UIMergeGrid grid = grids[j];
                                                UIMergeGood good = GetGood();
                                                good.transform.position = boxData.transform.position;
                                                good.Init(grid, list[0], MoveGoodRoot, this, false, 0,
                                                    true); //i * 0.15f

                                                createdGoods.Add(good);
                                                createdGoodGrids.Add(grid);

                                                mStep.assets.Add(good.mPoint.ServerIndex,
                                                    K3PlayerMgr.I.TOID(good.mData));
                                                ShowTopInfo(good);
                                            }
                                        }

                                        //if (grids.Count < skillCfg.Param)
                                        //{
                                        //    //多余道具飞入临时仓库
                                        //    if (IsPrivateOpen())
                                        //    {
                                        //        for (int z = grids.Count; z < skillCfg.Param; z++)
                                        //        {
                                        //            if (CSPlayer.I.mergeStoreData.TmpPrivateItems.Count <
                                        //                CSPlayer.I.mergeStoreData.TmpCapacityMax)
                                        //            {
                                        //                UIMergeGood good = GetGood();
                                        //                good.transform.position = boxData.transform.position;
                                        //                good.InitToMergeStore(list[0], MoveGoodRoot, this, false, 0,
                                        //                    true, z * 0.15f); //飞入仓库的道具
                                        //                good.ShowMergeBeActiveSkillAni();

                                        //                // createdGoods.Add(good);

                                        //                mPriveBoxIndex--;

                                        //                mStep.assets.Add(mPriveBoxIndex,
                                        //                    K3PlayerMgr.I.TOID(good.mData));

                                        //                CSPlayer.I.mergeStoreData.TmpPrivateItems.Add(list[0].id);

                                        //                CSPlayer.I.mergeStoreData.SetTmpRedPoint(true);
                                        //            }
                                        //        }
                                        //    }
                                        //}
                                    }
                                }
                            }
                        }


                        if (createdGoods.Count > 0)
                        {
                            if (mergeBeActiveSkill_AddGood)
                            {
                                for (int i = 0; i < createdGoods.Count && i < createdGoodGrids.Count; i++)
                                {
                                    var good = createdGoods[i];
                                    var grid = createdGoodGrids[i];

                                    good.ShowMergeBeActiveSkillAni();
                                    ShowSkillIconEffect(grid.transform, addGoodSkill.Icon);
                                }
                            }
                            else if (mergeBeActiveSkill_AddEnergy)
                            {
                            }

                            boxData.ShowMergeBeActiveSkillAni(mergeBeActiveSkill_AddGood, addGoodSkill,
                                mergeBeActiveSkill_AddEnergy, addEnergySkill);
                        }
                    }

                    //if (boxData.mBox.curHeroPower > boxData.mBox.MaxPower)
                    //{
                    //    boxData.mBox.curHeroPower = boxData.mBox.MaxPower;
                    //}

                    if (luckyType != SpecialBoxData.Enum_LuckyType.None)
                    {
                        //特效播放 Lucky~~
                        boxData.PlayLuckyEffect(luckyType);
                    }

                    return true;
                }
            }

            return false;
        }

        public List<UIMergeGrid> GetFreeGrids(int count)
        {
            List<UIMergeGrid> grids = new List<UIMergeGrid>();
            List<UIMergeGrid> freeGridList = new List<UIMergeGrid>();

            foreach (var item in mGridDic)
            {
                if (item.CanInItem())
                {
                    freeGridList.Add(item);
                }
            }

            for (int i = count - 1; i >= 0; i--)
            {
                if (freeGridList.Count > 0)
                {
                    int gridindex = Random.Range(0, freeGridList.Count);
                    UIMergeGrid grid = freeGridList[gridindex];
                    grids.Add(grid);
                    freeGridList.Remove(grid);
                }
            }

            return grids;
        }

        public void ShowChekBox(bool show, UIMergeGood good = null, bool isCancelSell = false)
        {
            //var draging = good != null ? good.Draging : false;
            //D.Error?.Log($"ShowChekBox:{show} draging:{draging}");
            if (show)
            {
                if (good == null || good.mData.id == CfgConst.K3MergeStoreID)
                {
                    if (checkBoxAni.gameObject.activeSelf)
                    {
                        checkBoxAni.gameObject.SetActive(false);
                    }
                }
                else
                {
                    if (!checkBoxAni.gameObject.activeSelf)
                    {
                        checkBoxAni.gameObject.SetActive(true);
                    }

                    checkBoxAni.transform.position = good.CurGrid.transform.position;
                    checkBoxAni.SetTrigger("show");

                    if (good.Info.ItemSource.Count > 0)
                    {
                        UITools.SetImageBySpriteName(checkGoodIconType,
                            UITools.GetAttributeDisplayKey(good.Info.ItemSource[0] - 10));
                        checkGoodIconType.gameObject.SetActive(!good.Draging);
                    }
                    else
                    {
                        checkGoodIconType.gameObject.SetActive(false);
                    }

                    if (good.Draging)
                    {
                        checkGoodLvText.text = "";
                    }
                    else
                    {
                        checkGoodLvText.text = LocalizationMgr.Format(LocalizationMgr.Get("Skill_Info_Level"),
                            good.Info.Level.ToString());
                    }

                    //if(curGood!=good)
                    //    NiceVibrationsCtrl.Haptic_SelectItems();
                }
            }
            else
            {
                if (checkBoxAni.gameObject.activeSelf)
                {
                    checkBoxAni.gameObject.SetActive(false);
                }
            }

            ShowTopInfo(good, isCancelSell);
        }

        //public void ShowChekBox(bool show, UIMergeSpecialBox good)
        //{
        //    //特殊箱子点击时不显选中标识
        //    //if (show)
        //    //{
        //    //    if (good == null /*|| good.mData.Immovable*/)
        //    //    {
        //    //        if (checkBoxAni.gameObject.activeSelf)
        //    //        {
        //    //            checkBoxAni.gameObject.SetActive(false);
        //    //        }
        //    //    }
        //    //    else
        //    //    {
        //    //        if (!checkBoxAni.gameObject.activeSelf)
        //    //        {
        //    //            checkBoxAni.gameObject.SetActive(true);
        //    //        }

        //    //        checkBoxAni.transform.position = good.transform.position;
        //    //        checkBoxAni.SetTrigger("show");
        //    //    }
        //    //}
        //    //else
        //    //{
        //    if (checkBoxAni.gameObject.activeSelf)
        //    {
        //        checkBoxAni.gameObject.SetActive(false);
        //    }

        //    //}
        //    if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.HeroForMergeBox)) // good.mBox.CfgID != 6
        //    {
        //        specialBoxInfo.gameObject.SetActive(true);
        //    }
        //    else
        //    {
        //        specialBoxInfo.gameObject.SetActive(false);
        //    }

        //    //ShowTopInfo(good);
        //    UpdateSpecialBoxInfo(null);
        //}

        [PopupEvent(Common.TEventType.K3MergeBoxHeroRefresh)]
        private void OnK3MergeBoxHeroRefresh(System.Object[] objs)
        {
            UpdateSpecialBoxInfo(objs);
            SetHeroAddRedState();
            //GameAudio.PlayAudio(AudioConst.POWER_UP);
        }

        public void UpdateSpecialBoxInfo(System.Object[] objs)
        {
            if (curSpecialBox != null)
            {
                var mHero = HeroGameData.I.GetHeroByCfgId(curSpecialBox.mBox.heroCfgIDs[0]);
                if (mHero != null)
                {
                    heroQuality.gameObject.SetActive(true);
                    heroEmpty.gameObject.SetActive(false);
                    UITools.SetCommonItemIcon(heroIcon, mHero.HeroCfg.Icon1, false);
                    //var qualityDisplayKey = UITools.GetBagQuelity(mHero.HeroCfg.HeroType);
                    UITools.SetQualityIcon(heroQuality, mHero.HeroCfg.HeroType);
                    goIntoBtn.gameObject.SetActive(false);
                    changeBtn.gameObject.SetActive(true);
                }
                else
                {
                    heroEmpty.gameObject.SetActive(true);
                    heroQuality.gameObject.SetActive(false);
                    goIntoBtn.gameObject.SetActive(true);
                    changeBtn.gameObject.SetActive(false);
                }
            }

            //RefreshBoxSelect();
        }

        //private void RefreshBoxSelect()
        //{
        //    box1?.SetSelect(false);
        //    //box2?.SetSelect(false);
        //    //box3?.SetSelect(false);
        //    //box4?.SetSelect(false);
        //    //box5?.SetSelect(false);

        //    curSpecialBox?.SetSelect(true);
        //}

        public void ShowSameBox(bool show, Transform target)
        {
            if (show)
            {
                if (!sameEffect.activeSelf)
                {
                    sameEffect.SetActive(true);
                }

                sameEffect.transform.position = target.position;
            }
            else
            {
                if (sameEffect.activeSelf)
                {
                    sameEffect.SetActive(false);
                }
            }
        }

        public void ShowCompoundEffect(Transform target, Action act)
        {
            compoundEffect.SetActive(true);
            compoundEffect.transform.position = target.position;
            compoundEffect.transform.DOScale(Vector3.one, 0.3f).OnComplete(() =>
            {
                act?.Invoke();
                compoundEffect.SetActive(false);
            });
        }

        public void ShowMergeEffect(Transform target)
        {
            if (!mergeEffect.activeSelf)
                mergeEffect.SetActive(true);

            mergeEffect.transform.position = target.position;
            mergeEffect.GetComponentInChildren<ParticleSystem>().Play();
            mergeEffect.transform.DOKill();
            mergeEffect.transform.DOScale(Vector3.one, 2).OnComplete<Tween>(() => { mergeEffect.SetActive(false); }
            );
        }

        public void ShowAccidentEffect(Transform target, bool isShow = true,
            SpecialBoxData.Enum_LuckyType luckyType = SpecialBoxData.Enum_LuckyType.Lucky3)
        {
            var effectGo = accidentEffect3;

            switch (luckyType)
            {
                case SpecialBoxData.Enum_LuckyType.Lucky1:
                    {
                        effectGo = accidentEffect1;
                        break;
                    }
                case SpecialBoxData.Enum_LuckyType.Lucky2:
                    {
                        effectGo = accidentEffect2;
                        break;
                    }
                case SpecialBoxData.Enum_LuckyType.Lucky3:
                    {
                        effectGo = accidentEffect3;
                        break;
                    }
            }

            effectGo.SetActive(isShow);
            if (isShow)
            {
                effectGo.transform.position = target.position;
                effectGo.GetComponentInChildren<ParticleSystem>().Play();
                Transform transform0 = target.transform;
                NTimer.CountDown(0.15f, () =>
                {
                    GameObject effect = ResourceMgr.LoadInstance("Assets/K1D7/Res/Effects/Luck.prefab");
                    effect.SetParent(transform0);
                    effect.transform.localPosition = new Vector3(0, 150, 0);
                    effect.transform.localScale = Vector3.one;
                    effect.gameObject.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Layer;
                    effect.gameObject.GetComponent<Canvas>().sortingOrder = 100;
                    GameObject.Destroy(effect, 1);
                });
            }
            else
            {
            }
        }

        public UIMergeGood GetGood()
        {
            UIMergeGood good = null;

            if (MergeGoodPool.Count > 0)
            {
                good = MergeGoodPool.Get().GetAddCom<UIMergeGood>();
            }

            if (good == null)
            {
                good = GameObject.Instantiate(goodPre, MoveGoodRoot);
            }
            else
            {
                good.transform.SetParent(MoveGoodRoot);
            }

            good.gameObject.SetActive(true);

            return good;
        }

        //private int photoTaskIndex = 0;

        //public PhotoInfo curPhotoTask = null;
        //public void ShowPhotoTaskInfo()
        //{
        //    //taskTopInfo.RefreshData();
        //    //if (K3PlayerMgr.I.OpenMenu(K3UnlockData.UnlockType.Photo))
        //    //{
        //    //    photoTaskObj.SetActive(true);
        //    //    description_null.text = TFW.Localization.LocalizationMgr.Get("Ui_board_txt");
        //    //    int choose = CSPlayer.I.GetPhotoMax();
        //    //    if (K3PlayerMgr.I.IsPhotoEnd(choose) && K3PlayerMgr.I.PhotoMax())
        //    //    {
        //    //        //photoTaskObj.SetActive(false);
        //    //        return;
        //    //    }
        //    //    photoTaskGrid.Clear();
        //    //    photoTaskCity.SetActive(false);
        //    //    int CurUnclaimedBoxIndex = K3PlayerMgr.I.CurUnclaimedBox();
        //    //    UnlcokBoxObj.SetActive(CurUnclaimedBoxIndex > -1);
        //    //    gridBox.SetActive(CurUnclaimedBoxIndex > -1);
        //    //    if (CurUnclaimedBoxIndex > -1)
        //    //    {
        //    //        photoUnlockText.text = TFW.Localization.LocalizationMgr.Format("Ui_GotoOpenPhotoBox", CurUnclaimedBoxIndex);
        //    //        BindClickListener(UnlcokBoxObj, (x, y) =>
        //    //        {
        //    //            ShowPhoto();
        //    //        });
        //    //        return;
        //    //    }

        //    //    if (K3PlayerMgr.I.PhotoMax(choose))
        //    //    {
        //    //        choose++;
        //    //    }
        //    //    var photoInfos = CSVPhoto.GetCK3PhotoAlbumByAlbumToActive(choose);

        //    //    if (photoInfos.Count > photoTaskIndex)
        //    //    {
        //    //        curPhotoTask = photoInfos[photoTaskIndex];
        //    //    }
        //    //    else
        //    //    {
        //    //        photoTaskIndex = 0;
        //    //        curPhotoTask = photoInfos[0];
        //    //    }
        //    //    photoUnlockText.text = TFW.Localization.LocalizationMgr.Format("PhotoChapterTxT", curPhotoTask.Album, curPhotoTask.Sequence);

        //    //    photoTaskCity.SetActive(curPhotoTask.UnlockLevel > 1);
        //    //    photoTaskCitytip.SetActive(!UI.Utils.MainFunctionOpenUtils.CityOpenState);
        //    //    photoTaskCityOkObj.SetActive(curPhotoTask.UnlockLevel <= Logic.LPlayer.I.GetMainCityLevel());
        //    //    photoTaskCityCount.transform.parent.gameObject.SetActive(curPhotoTask.UnlockLevel > Logic.LPlayer.I.GetMainCityLevel());

        //    //    if (curPhotoTask.UnlockLevel <= Logic.LPlayer.I.GetMainCityLevel())
        //    //    {
        //    //        photoTaskCityCount.text = $"<color=#00ff00>{Logic.LPlayer.I.GetMainCityLevel()}</color><color=#BC773F>/{curPhotoTask.UnlockLevel}</color>";
        //    //    }
        //    //    else
        //    //    {
        //    //        photoTaskCityCount.text = $"<color=#E65A5A>{Logic.LPlayer.I.GetMainCityLevel()}</color><color=#BC773F>/{curPhotoTask.UnlockLevel}</color>";
        //    //    }

        //    //    UIBase.AddRemoveListener(TFW.EventTriggerType.Click, photoTaskCitytip, (x, y) =>
        //    //    {
        //    //        DeepUI.PopupManager.I.ShowDialog<UIMergeCityUnockInfo>();
        //    //    });

        //    //    foreach (var item in curPhotoTask.CostItemInfo)
        //    //    {
        //    //        photoTaskGrid.AddItem<UIMergeMainPhotoGridItem>().InitItem(item);
        //    //    }

        //    //    RefreshTaskEffect();
        //    //    //photoTaskName.text = TFW.Localization.LocalizationMgr.Get(curPhotoTask.Des);
        //    //    // btnPhotoTaskNext.SetActive(photoInfos.Count > 1);

        //    //}
        //    //else
        //    //{
        //    //    //photoTaskObj.SetActive(false);
        //    //    //description_null.text = TFW.Localization.LocalizationMgr.Get("Ui_board_txt");
        //    //}
        //}

        public void CurTaskAutoTODO()
        {
            EventMgr.FireEvent(TEventType.K3TaskAutoToDO);
        }


        public void ShowTopInfo(UIMergeGood good = null, bool isCancelSell = false)
        {

            curGood = good;
            curSpecialBox = null;
            lastTime = DateTime.Now;

            specialBoxInfo.gameObject.SetActive(false);

            //RefreshBoxSelect();

            UpdateBubblePanelActive();
            cancelTitle.gameObject.SetActive(false);
            HeroItemInfoGa.SetActive(false);

            //ShowPhotoTaskInfo();

            if (good == null)
            {
                btnCancelSell.SetActive(false);
                btnSell.SetActive(false);
                btnUnlock.SetActive(false);
                btnCollect.SetActive(false);
                btnInfo.SetActive(false);
                BOXbtnInfo.SetActive(false);
                //IconObj.SetActive(false);
                //levelText.gameObject.SetActive(false);
                //levelBg.SetActive(false);
                //nameText.gameObject.SetActive(false);
                descriptionText.gameObject.SetActive(false);
                BoxItemCDObj.SetActive(false);
                description_null.gameObject.SetActive(false);
                btnBoxItemCreatGoodButton.gameObject.SetActive(false);
                specialBoxInfo.gameObject.SetActive(false);
                //TODO ~~
                //photoTaskIndex = 0;
            }
            else
            {
                gooddata = curGood.GetInfo();
                //if (curGood.GetInfo().Type > 0)
                //{
                //    UITools.SetCommonItemIcon(icon, curGood.GetInfo().Icon);
                //}
                //else
                //{
                //    //选中英雄
                //    UITools.SetCommonItemIcon(icon, Cfg.C.CD2Hero.I(curGood.GetInfo().id).Icon1, true);
                //    //UITools.SetImage(icon, Cfg.C.CD2Hero.I(curGood.GetInfo().id).Icon1, "NewItem", false);
                //    HeroItemInfoGa.SetActive(true);

                //    var skillCfg = Cfg.C.CD2Skill.I(Cfg.C.CD2Hero.I(curGood.GetInfo().id).ActiveSkill);
                //    HeroItemInfoDesText.text = TFW.Localization.LocalizationMgr.Get(skillCfg.Name);

                //    UITools.SetCommonSKillIcon(skillIcon, skillCfg.Icon.ToString());

                //    UIBase.AddRemoveListener(TFW.EventTriggerType.Click, skillIcon.gameObject, (x, y) =>
                //      {
                //          PopupManager.I.ShowDialog<UIMergeHeroInfo>(new UIMergeHeroInfoData()
                //          {
                //              serverIndex = curGood.mPoint.ServerIndex,
                //              heroID = curGood.Info.id,
                //              heroCurEnergy = curGood.mData.BoxTimes
                //          });
                //      });
                //}

                if (isCancelSell)
                {
                    btnInfo.SetActive(false && gooddata.Code <= 20);
                    BOXbtnInfo.SetActive(false);
                    //IconObj.SetActive(true);
                    descriptionText.gameObject.SetActive(false);
                    btnCancelSell.SetActive(true);
                    // cancelTitle.gameObject.SetActive(true); // 新版界面用不到这个
                    //levelText.gameObject.SetActive(false);
                    description_null.gameObject.SetActive(false);
                    //nameText.gameObject.SetActive(false);
                    btnSell.SetActive(false);
                    //levelBg.SetActive(false);
                    descriptionText.text = TFW.Localization.LocalizationMgr.Get("Ui_item_revert_txt");
                    return;
                }


                btnInfo.SetActive(false && gooddata.Code <= 20);
                BOXbtnInfo.SetActive(false);
                //IconObj.SetActive(true);
                //levelText.gameObject.SetActive(true);
                //levelBg.SetActive(true);
                //nameText.gameObject.SetActive(true);
                btnCancelSell.SetActive(false);
                description_null.gameObject.SetActive(false);
                //levelText.text = gooddata.Level.ToString();
                //nameText.text = gooddata.Name;
                if (curGood.GetInfo().Type < 0)
                {
                    descriptionText.gameObject.SetActive(false);
                }
                else
                {
                    descriptionText.gameObject.SetActive(false);
                    descriptionText.text = gooddata.Des;
                }

                if (good.GetLocked())
                {
                    btnUnlock.SetActive(false);
                    btnSell.SetActive(false);
                    btnCollect.SetActive(false);
                    BoxItemCDObj.SetActive(false);
                    btnBoxItemCreatGoodButton.gameObject.SetActive(false);
                    deletetimerText.text = "00:00";
                    unlockPriceText.text = good.GetInfo().UnlockCost.ToString();
                    BoxItemCDOIcon.SetActive(false);
                }
                else
                {
                    btnUnlock.SetActive(false);
                    if (curGood.Info.Type == 2 || curGood.Info.Type == 3 || curGood.Info.Type == 4)
                    {
                        btnSell.SetActive(false);
                        btnCollect.SetActive(true && !curGood.mData.Immovable && !curGood.mData.locked);
                        btnBoxItemCreatGoodButton.gameObject.SetActive(false);
                        BoxItemCDObj.SetActive(false);
                        BoxItemCDOIcon.SetActive(false);
                        collectPriceText.text = UIStringUtils.FormatIntUnitByLanguage(gooddata.Rewards[0].RewardVal);
                        UITools.SetImageBySpriteName(collectTypeIcon,
                            ((MoneyType)gooddata.Rewards[0].RewardId).ToString());
                    }
                    else
                    {
                        if (good.Info.ItemBox?.BoxID > 0)
                        {
                            btnSell.SetActive(false);
                            btnCollect.SetActive(false);
                            btnInfo.SetActive(false);
                            if (good.BoxCanUse())
                            {
                                BoxItemCDObj.SetActive(false);
                                BoxItemCDOIcon.SetActive(false);
                                btnBoxItemCreatGoodButton.gameObject.SetActive(false);
                                BoxItemCreatGoodPriceText.text =
                                    UIStringUtils.FormatIntUnitByLanguage(good.Info.ItemBox.CostEnery);
                            }
                            else
                            {
                                BoxItemCDObj.SetActive(true);
                                BoxItemCDOIcon.SetActive(true);
                                btnBoxItemCreatGoodButton.gameObject.SetActive(false);

                                btnCoin_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD > 0);
                                btnFree_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD == 0);
                                btnAD_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD >
                                                        0); // && SDKManager.instance.RewardADLoaded

                                if (K3PlayerMgr.I.PlayerData.freeResetBoxCD == 0)
                                {
                                    ////免费使用重置盒子引导
                                    //var guidData = new UIGuidData();

                                    //guidData.guidItems.Add(new UIGuidItem()
                                    //{
                                    //    UIName = "UIMerge",
                                    //    slide = true,
                                    //    UIItemGa = btnFree_BoxItem,
                                    //    doubleClick = false,
                                    //    figherOffset = new Vector3(50, 0, 0),
                                    //    forceGuid = true //改成 强引导

                                    //});

                                    //UIGuid.StartGuid(guidData);

                                    //GuidManage.TriggerGuid(GuidManage.GuidTriggerType.FreeResetBoxCD, 0);
                                }
                            }
                        }
                        else
                        {
                            BoxItemCDObj.SetActive(false);
                            BoxItemCDOIcon.SetActive(false);
                            if (good.mData.Immovable)
                            {
                                btnSell.SetActive(false);
                            }
                            else
                            {
                                btnSell.SetActive(gooddata.Price > 0 && UnlockMgr.I.CheckUnlock(UnlockFuncEnum.Sale));
                            }

                            btnCollect.gameObject.SetActive(false);
                            //sellPriceText.text = UIStringUtils.FormatIntUnitByLanguage(gooddata.Price);
                            btnBoxItemCreatGoodButton.gameObject.SetActive(false);
                        }
                    }
                }

                //仓库
                if (curGood.Info.id == Cfg.CfgConst.K3MergeStoreID)
                {
                    btnInfo.SetActive(false);
                    //levelBg.SetActive(false);
                    //levelText.gameObject.SetActive(false);
                }

                //nameText.GetComponent<RectTransform>().DOLocalMoveX(levelBg.activeSelf ? 26 : -60, 0);
            }
        }

        public UIMergeSpecialBox curSpecialBox;

        //public void ShowTopInfo(UIMergeSpecialBox boxData)
        //{
        //    return;

        //    //ShowAutoMergeToPinfo();
        //    //curSpecialBox = boxData;
        //    //curGood = null;
        //    //lastTime = DateTime.Now;

        //    ////RefreshBoxSelect();

        //    //UpdateBubblePanelActive();
        //    //cancelTitle.gameObject.SetActive(false);
        //    //HeroItemInfoGa.SetActive(false);

        //    //ShowPhotoTaskInfo();

        //    ////UITools.SetCommonItemIcon(icon, boxData.mBox.BoxCfg.BoxIcon);

        //    //btnInfo.SetActive(false);
        //    ////IconObj.SetActive(true);
        //    ////levelText.gameObject.SetActive(true);
        //    ////levelBg.SetActive(false);
        //    ////nameText.gameObject.SetActive(true);
        //    //btnCancelSell.SetActive(false);
        //    //descriptionText.gameObject.SetActive(true);
        //    //description_null.gameObject.SetActive(false);
        //    ////levelText.text = "";
        //    ////nameText.text = TFW.Localization.LocalizationMgr.Get(boxData.mBox.BoxCfg.BoxName);
        //    //descriptionText.text = TFW.Localization.LocalizationMgr.Get(boxData.mBox.BoxCfg.Des);
        //    //btnUnlock.SetActive(false);
        //    //btnSell.SetActive(false);
        //    //btnCollect.SetActive(false);
        //    ////var id =int.Parse( Cfg.C.CD2Config.I(10722).Val.ToString());
        //    ////BOXbtnInfo.SetActive(CSPlayer.I.AllPhotoIdList.Contains(id));
        //    //BOXbtnInfo.SetActive(true);
        //    ////nameText.GetComponent<RectTransform>().DOLocalMoveX(levelBg.activeSelf ? 26 : -60, 0);

        //    //BindClickListener(BOXbtnInfo, (x, y) =>
        //    //{
        //    //    //PopupManager.I.ShowPanel<UIMergeBoxPanel>(new UIMergeBoxPanelData() { specialBoxData = boxData.mBox });
        //    //    PopupManager.I.ShowPanel<UIMergeBoxItemInfoPanel>(boxData.mBox);
        //    //});
        //    //BindClickListener(goIntoBtn, (x, y) =>
        //    //{
        //    //    // PopupManager.I.ShowPanel<UIMergeBoxPanel>(new UIMergeBoxPanelData() { specialBoxData = boxData.mBox });
        //    //    PopupManager.I.ShowPanel<UIMergeSpecialBoxSetHeroView>(boxData.mBox.CityType);
        //    //});
        //    //BindClickListener(changeBtn, (x, y) =>
        //    //{
        //    //    // PopupManager.I.ShowPanel<UIMergeBoxPanel>(new UIMergeBoxPanelData() { specialBoxData = boxData.mBox });
        //    //    PopupManager.I.ShowPanel<UIMergeSpecialBoxSetHeroView>(boxData.mBox.CityType);
        //    //});
        //    //BindClickListener(heroHeadBtn, (x, y) =>
        //    //{
        //    //    // PopupManager.I.ShowPanel<UIMergeBoxPanel>(new UIMergeBoxPanelData() { specialBoxData = boxData.mBox });
        //    //    PopupManager.I.ShowPanel<UIMergeSpecialBoxSetHeroView>(boxData.mBox.CityType);
        //    //});
        //    //if (boxData.CurBoxUseCd() <= 0)
        //    //{
        //    //    BoxItemCDObj.SetActive(false);
        //    //    BoxItemCDOIcon.SetActive(false);
        //    //    btnBoxItemCreatGoodButton.gameObject.SetActive(false);
        //    //    //BoxItemCreatGoodPriceText.text = UIStringUtils.FormatIntUnitByLanguage(good.Info.ItemBox.CostEnery);
        //    //}
        //    //else
        //    //{
        //    //    //BoxItemCDObj.SetActive(true);
        //    //    BoxItemCDOIcon.SetActive(true);
        //    //    btnBoxItemCreatGoodButton.gameObject.SetActive(false);

        //    //    btnCoin_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD > 0);
        //    //    btnFree_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD == 0);
        //    //    btnAD_BoxItem.SetActive(K3PlayerMgr.I.PlayerData.freeResetBoxCD > 0 && SDKManager.instance.RewardADLoaded);

        //    //    if (K3PlayerMgr.I.PlayerData.freeResetBoxCD == 0)
        //    //    {
        //    //        //免费使用重置盒子引导
        //    //        //var guidData = new UIGuidData();

        //    //        //guidData.guidItems.Add(new UIGuidItem()
        //    //        //{
        //    //        //    UIName = "UIMerge",
        //    //        //    slide = true,
        //    //        //    UIItemGa = btnFree_BoxItem,
        //    //        //    doubleClick = false,
        //    //        //    figherOffset = new Vector3(50, 0, 0),
        //    //        //    forceGuid = true //改成 强引导

        //    //        //});

        //    //        //UIGuid.StartGuid(guidData);
        //    //    }
        //    //}
        //}

        public void OnUpdateBubbleState()
        {
            if (curGood != null && curGood.GetLocked() && !curGood.mData.Immovable)
            {
                btnUnlock.SetActive(false);
                btnSell.SetActive(false);
                btnCollect.SetActive(false);
                bubblePanel.SetActive(true);
                UpdateBubblePos();
                timeText.text =
                    TFW.Localization.LocalizationMgr.Format("Ui_item_unlock_time", (int)curGood.GetSecondTime());
                btnUnlockFree.SetActive(K3PlayerMgr.I.PlayerData.freeAdUnlockTimes == 0);
                btnUnLockAD.SetActive(K3PlayerMgr.I.PlayerData.freeAdUnlockTimes >
                                      0); //&& SDKManager.instance.RewardADLoaded
                btnUnlockCoin.SetActive(K3PlayerMgr.I.PlayerData.freeAdUnlockTimes > 0);

                priceText.text = curGood.GetInfo().UnlockCost.ToString();
                if (K3PlayerMgr.I.PlayerData.diamond >= curGood.GetInfo().UnlockCost)
                {
                    priceText.color = Color.white;
                }
                else
                {
                    priceText.color = new Color(255 / 255f, 42 / 255f, 42 / 255f);
                }

                NTimer.TickNoPool(ref timer, NTimer.INF, 1f, () =>
                {
                    if (curGood != null)
                    {
                        timeText.text =
                            TFW.Localization.LocalizationMgr.Format("Ui_item_unlock_time",
                                (int)curGood.GetSecondTime());
                        if ((int)curGood.GetSecondTime() <= 0)
                        {
                            timer.Stop();
                        }
                    }
                    else
                    {
                        timer.Stop();
                    }
                });
            }
        }

        private void UpdateBubblePos()
        {
            var width = Screen.width / 6;
            var height = Screen.height / 2;
            var screen = clickRoot.worldCamera.WorldToScreenPoint(curGood.transform.position);
            if (height > screen.y) //显示在bubble上面
            {
                bubblePanel.transform.position = curGood.transform.position + new Vector3(0, 1.6f, 0);
            }
            else //下面
            {
                bubblePanel.transform.position = curGood.transform.position + new Vector3(0, -1.6f, 0);
            }

            if (width * 4 < screen.x) //左边
            {
                bubblePanel.transform.position = curGood.transform.position + new Vector3(-2f, 0.5f, 0);
            }
            else if (width * 2 > screen.x) //右边
            {
                bubblePanel.transform.position = curGood.transform.position + new Vector3(2f, 0.5f, 0);
            }
        }

        private void SellGood()
        {
            if (curGood != null)
            {
                //if (CSPlayer.I.CurPhotoAlbum == 1 && CSPlayer.I.PhotoIdList.Count == 0) //相册物件未解锁，不可出售！
                //    return;

                curGood.Sell();
                //ShowChekBox(false);
            }
        }

        private void CancelSellGood()
        {
            if (curGood != null)
            {
                curGood.CancelSell();
                //ShowChekBox(false);
            }
        }

        private void collectGood()
        {
            if (curGood != null && !curGood.mData.Immovable && !curGood.mData.locked)
            {
                curGood.Collect();
                ShowChekBox(false);
            }
        }

        private void UnlockGood(bool useMoney = false)
        {
            if (curGood != null)
            {
                if (curGood.Unlock(useMoney))
                {
                    GameAudio.PlayAudio(13);
                    ShowTopInfo(curGood);
                }
            }

            UpdateBubblePanelActive();
        }

        private void RestBoxItemCD(bool useMoney = false)
        {
            if (curSpecialBox != null)
            {
                if (curSpecialBox.ResetItemBoxCD(useMoney))
                {
                    GameAudio.PlayAudio(13);
                    //ShowTopInfo(curSpecialBox);
                }
            }
        }

        private void BoxItemCreatGood()
        {
            if (curGood != null)
            {
                curGood.Collect();
            }
        }

        private void RestBoxItemByAD()
        {
            //var adEvent = new ADEvent();
            //adEvent.EventKey = "ADEvent_Start";
            //adEvent.Properties.Add("AdResource", "ResetBox");
            //adEvent.Properties.Add("itemid", curSpecialBox.mBox.BoxCfg.Id);
            //K3GameEvent.I.TaLog(adEvent);

            //SDKManager.instance.ShowAd("resetBox", (success) =>
            // {
            //     switch (success)
            //     {
            //         case SDKManager.AdResult.VIP:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_vip";
            //             //adEvent.Properties.Add("AdResource", "ResetBox");
            //             //adEvent.Properties.Add("itemid", curSpecialBox.mBox.BoxCfg.Id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             RestBoxItemCD();
            //             break;

            //         case SDKManager.AdResult.Ok:
            //             FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("System_ADShop_Erro_3"));
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_Success";
            //             //adEvent.Properties.Add("AdResource", "ResetBox");
            //             //adEvent.Properties.Add("itemid", curSpecialBox.mBox.BoxCfg.Id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             RestBoxItemCD();
            //             break;

            //         case SDKManager.AdResult.CloseVideo:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_closevideo";
            //             //adEvent.Properties.Add("AdResource", "ResetBox");
            //             //adEvent.Properties.Add("itemid", curSpecialBox.mBox.BoxCfg.Id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             break;

            //         default:
            //             //adEvent = new ADEvent();
            //             //adEvent.EventKey = "ADEvent_loadError";
            //             //adEvent.Properties.Add("AdResource", "ResetBox");
            //             //adEvent.Properties.Add("itemid", curSpecialBox.mBox.BoxCfg.Id);
            //             //K3GameEvent.I.TaLog(adEvent);

            //             FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("System_ADShop_Erro_2"));
            //             break;
            //     }
            // });
        }

        private void RestBoxItemByMoney()
        {
            RestBoxItemCD(true);
        }

        private void ChangeHero()
        {
            PopupManager.I.ClosePopup<UIMerge>();
            Common.EventMgr.FireEvent(Common.TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.HERO);
        }

        private void ShowGoodInfo()
        {
            if (curGood != null && curGood.Info.Code <= 20 && curGood.Info.id != Cfg.CfgConst.K3MergeStoreID)
            {
                if (curGood.Info.Type > 0)
                {
                    PopupManager.I.ShowDialog<UIMergeGoodInfo>(new UIMergeGoodInfoData(curGood.Info));
                }
                //else
                //{
                //    PopupManager.I.ShowDialog<UIMergeHeroInfo>(new UIMergeHeroInfoData()
                //    {
                //        serverIndex = curGood.mPoint.ServerIndex,
                //        heroID = curGood.Info.id,
                //        heroCurEnergy = curGood.mData.BoxTimes
                //    });
                //}
            }
        }

        //private IEnumerator DisPosMergeEffect(GameObject obj)
        //{
        //    yield return new WaitForSeconds(2f);
        //    obj.SetActive(false);
        //}

        public bool CheckDoubleClick(UIMergeGood good)
        {
            if (curGood != null && curGood == good)
            {
                if ((DateTime.Now - lastTime).Seconds < doubleDuration)
                {
                    good.DoubleClick();
                    return true;
                }
            }

            return false;
        }

        public GameObject GetLockedEffect()
        {
            GameObject lockedEffect = null;

            return lockedEffect;
        }

        public void ShowCreatEffect(Transform targt)
        {
            GameObject effect = null;

            for (int i = 0; i < creatEffectList.Count; i++)
            {
                if (!creatEffectList[i].activeSelf)
                {
                    effect = creatEffectList[i];
                    break;
                }
            }

            if (effect == null)
            {
                effect = GameObject.Instantiate(creatEffect, creatEffect.transform.parent);
                creatEffectList.Add(effect);
            }

            effect.SetActive(true);
            effect.transform.position = targt.position;

            NTimer.CountDown(0.8f, () => effect.SetActive(false));
        }

        public void ShowSkillIconEffect(Transform targt, string skillIconName)
        {
            GameObject effect = null;

            for (int i = 0; i < mSkillIconEffectList.Count; i++)
            {
                if (!mSkillIconEffectList[i].activeSelf)
                {
                    effect = mSkillIconEffectList[i];
                    break;
                }
            }

            if (effect == null)
            {
                effect = GameObject.Instantiate(UIMergeSkillIconGO, UIMergeSkillIconGO.transform.parent);
                mSkillIconEffectList.Add(effect);
            }

            effect.GetComponent<UIMergeSkillIcon>()?.SetSkillImg(skillIconName);
            effect.SetActive(true);
            effect.transform.position = targt.position;
        }

        //public void CollectTask(List<ItemInfo> infos)
        //{
        //    for (int i = 0; i < infos.Count; i++)
        //    {
        //        int count = infos[i].TaskCount;
        //        int id = infos[i].id;

        //        foreach (var item in mGridDic)
        //        {
        //            if (count > 0 && item.Good != null && item.Good.GetInfo().id == id)
        //            {
        //                item.Good.DisPos();
        //                count--;
        //            }
        //        }
        //    }

        //    ShowChekBox(false);
        //}

        public void ClearGrids()
        {
            List<int> ListClear = new List<int>();
            foreach (var item in mGoodDic)
            {
                if (item != null && item.Info.id != Cfg.CfgConst.K3MergeStoreID)
                {
                    if (item.Info?.ItemBox?.BoxID > 0)
                    {
                    }
                    else if (item.mData.goodType == 1)
                    {
                    }
                    else
                    {
                        ListClear.Add(item.mPoint.ServerIndex);
                    }
                }
            }

            var mStep = CSPlayer.I.k3ToServerData.AddGMMergeStep();

            for (int i = 0; i < ListClear.Count; i++)
            {
                mStep.assets.Add(ListClear[i], 0);

                mGoodDic[ListClear[i]].DisPos(true);
            }

            mStep.StepRecordEnd();
            //mGoodDic.Clear();
        }

        public void ClearMist()
        {
            foreach (var item in areaParent.GetComponentsInChildren<UIMergeAreaComp>())
            {
                GameObject.Destroy(item.gameObject);
            }

            var areaList = Cfg.C.CCSArea.RawList();
            foreach (var item in areaList)
            {
                if (!K3PlayerMgr.I.AreaData.AreaedData.Contains(item.Id))
                {
                    K3PlayerMgr.I.AreaData.AreaedData.Add(item.Id);
                }
            }
        }


        //public static void TaskCompleteToDo()
        //{
        //    foreach (var item in K3PlayerMgr.I.GridsData)
        //    {
        //        if (item.isFull)
        //        {
        //            if (item.goodData.id != Cfg.CfgConst.K3MergeStoreID && !item.goodData.Immovable)
        //            {
        //                item.goodData = null;
        //            }
        //        }
        //    }

        //    //List<int> ListClear = new List<int>();
        //    //foreach (var item in mGoodDic)
        //    //{
        //    //    if (item.Value != null && item.Value.Info.id != Cfg.CfgConst.K3MergeStoreID && item.Value.Info.id != Cfg.CfgConst.K3MergeSoldierStoreID)
        //    //    {
        //    //        if (item.Value.Info?.ItemBox?.BoxID > 0)
        //    //        {
        //    //        }
        //    //        else if (item.Value?.mData.goodType == 1)
        //    //        {
        //    //        }
        //    //        else if (item.Value?.mData.Immovable==true)
        //    //        {

        //    //        }
        //    //        else
        //    //        {
        //    //            ListClear.Add(item.Key);
        //    //        }
        //    //    }
        //    //}
        //    //var mStep = CSPlayer.I.k3ToServerData.AddGMMergeStep();

        //    //for (int i = 0; i < ListClear.Count; i++)
        //    //{
        //    //    mStep.assets.Add(ListClear[i], 0);

        //    //    mGoodDic[ListClear[i]].DisPos();
        //    //}
        //    //mStep.StepRecordEnd();
        //}


        //public void USInPhoto(List<Reward> infos)
        //{
        //    foreach (var item in infos)
        //    {
        //        int needNum = item.RewardVal;

        //        foreach (var good in mGoodDic)
        //        {
        //            if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.mData.id == item.RewardId)
        //            {
        //                good.Value.DisPos();
        //                needNum--;
        //            }

        //            if (needNum <= 0)
        //            {
        //                break;
        //            }
        //        }
        //    }

        //    ShowChekBox(false);

        //    RefreshPhotoCountText();
        //}

        //private List<UIMergeGood> goodList = new List<UIMergeGood>();

        //public List<UIMergeGood> GetAllGood()
        //{
        //    goodList.Clear();

        //    foreach (var item in mGoodDic)
        //    {
        //        if (!item.Value.GetLocked() && !item.Value.mData.Immovable && item.Value.CurGrid.NoInArea())
        //        {
        //            goodList.Add(item.Value);
        //        }
        //    }

        //    return goodList;
        //}

        //public void RefreshPhotoCountText()
        //{
        //    //PhotoCountText.text = K3PlayerMgr.I.GetPhotoUse();
        //    //int curPhotoAlbumID = Logic.CSPlayer.I.GetPhotoChoose();

        //    //ResourceMgr.LoadDynamicRawImage(PhotoIcon, $"Assets/K3/Res/Photos/Textures/{Cfg.C.CK3PhotoAlbum.I(curPhotoAlbumID).Icon}.png", true);
        //}

        //public void UpdateData()
        //{
        //    //K3.K3PlayerMgr.I.GridsData[data.point.ServerIndex] = data;

        //    //int redCountBefore = ActiveCount();

        //    //RefreshPhotoCountText();

        //    //redPoint_Task.SetActive(K3PlayerMgr.I.CheckFinishTask(GetAllGood(),lockTask.activeSelf));
        //    //redPoint_Photo.SetActive(K3PlayerMgr.I.CheckHaveUnlockPhoto(false).Count > 0);
        //    //redPoint_Good.SetActive(K3PlayerMgr.I.BoxidQueue.Count > 1);
        //    //RefreshRedPointNum();
        //    //CheckAtlasRedPoint();

        //    //taskTopInfo.CheckTaskUse();

        //    //K3PlayerMgr.I.CheckCityUse(mGridDic);


        //    //K3PlayerMgr.I.SaveDataLocal();
        //    //int redCountAfter = ActiveCount();
        //    //if (redCountAfter != redCountBefore)
        //    //{
        //    //    RedSyn();
        //    //}
        //}

        //private int ActiveCount()
        //{
        //    int count = 0;
        //    //if (redPoint_Task.activeInHierarchy)
        //    //{
        //    //    count++;
        //    //}
        //    //if (redPoint_Photo.activeInHierarchy)
        //    //{
        //    //    count++;
        //    //}
        //    //if (redPoint_Atlas.activeInHierarchy)
        //    //{
        //    //    count++;
        //    //}
        //    //if (redPoint_Shop.activeInHierarchy)
        //    //{
        //    //    count++;
        //    //}
        //    //if (redPoint_Good.activeInHierarchy)
        //    //{
        //    //    count++;
        //    //}
        //    return count;
        //}

        //public void RedSyn()
        //{
        //    //redPoint_Task.GetComponent<Animator>().SetTrigger("startJump");
        //    //redPoint_Photo.GetComponent<Animator>().SetTrigger("startJump");
        //    //redPoint_Atlas.GetComponent<Animator>().SetTrigger("startJump");
        //    //redPoint_Shop.GetComponent<Animator>().SetTrigger("startJump");
        //    //redPoint_Good.GetComponent<Animator>().SetTrigger("startJump");
        //}

        //public void RefreshRedPointNum()
        //{
        //    //if (K3PlayerMgr.I.CheckHaveUnlockPhoto(false).Count > 1)
        //    //{
        //    //    redPointPhoto.text = K3PlayerMgr.I.CheckHaveUnlockPhoto(false).Count.ToString();
        //    //    redPointPhoto.gameObject.SetActive(true);
        //    //}
        //    //else
        //    //{
        //    //    redPointPhoto.gameObject.SetActive(false);
        //    //}

        //    //if (K3PlayerMgr.I.GetAtlasRedPointNum() > 1)
        //    //{
        //    //    redPointAtlas.text = K3PlayerMgr.I.GetAtlasRedPointNum().ToString();
        //    //    redPointAtlas.gameObject.SetActive(true);
        //    //}
        //    //else
        //    //{
        //    //    redPointAtlas.gameObject.SetActive(false);
        //    //}
        //    if (K3PlayerMgr.I.BoxidQueue.Count >= 2)
        //    {
        //        redPointGood.text = K3PlayerMgr.I.BoxidQueue.Count.ToString();
        //    }
        //}

        public void HideMergeGuide()
        {
            if (guideGridList == null)
                return;

            for (int i = 0; i < guideGridList.Count; i++)
            {
                guideGridList[i].HidGuideMerge();
            }

            guideGridList.Clear();
        }

        private List<UIMergeGrid> GetGuideMergeGrid()
        {
            List<UIMergeGrid> guideGridList = new List<UIMergeGrid>();
            Dictionary<int, List<UIMergeGrid>> gridDic = new Dictionary<int, List<UIMergeGrid>>();

            foreach (var grid in mGridDic)
            {
                if (grid.GetData().isFull && grid.NoInArea() && !grid.GetData().goodData.locked &&
                    !grid.GetData().goodData.max)
                {
                    int id = grid.GetData().goodData.id;
                    if (!gridDic.ContainsKey(id))
                    {
                        gridDic.Add(id, new List<UIMergeGrid>());
                    }

                    if (!gridDic[id].Contains(grid))
                    {
                        gridDic[id].Add(grid);
                    }
                }
            }

            List<List<UIMergeGrid>> all = new List<List<UIMergeGrid>>();
            foreach (var item in gridDic)
            {
                int ImmovableCount = 0;
                List<UIMergeGrid> ImmovableUIMergeGrid = new List<UIMergeGrid>();
                for (int i = 0; i < item.Value.Count; i++)
                {
                    if (item.Value[i].GetData().goodData.Immovable)
                    {
                        ImmovableCount++;
                        if (ImmovableCount > 1)
                        {
                            ImmovableUIMergeGrid.Add(item.Value[i]);
                        }
                    }
                }

                for (int i = 0; i < ImmovableUIMergeGrid.Count; i++)
                {
                    if (item.Value.Contains(ImmovableUIMergeGrid[i]))
                    {
                        item.Value.Remove(ImmovableUIMergeGrid[i]);
                    }
                }

                if (item.Value.Count >= 2)
                {
                    all.Add(item.Value);
                }
            }

            if (all.Count > 0)
            {
                List<UIMergeGrid> list = all[Random.Range(0, all.Count)];

                List<int> index = new List<int>();
                for (int i = 0; i < 2; i++)
                {
                    UIMergeGrid grid = list[Random.Range(0, list.Count)];
                    guideGridList.Add(grid);
                    list.Remove(grid);
                }
            }

            return guideGridList;
        }

        private List<UIMergeGrid> GetGuideMergeGrid(int cardID, bool goodClick = false, bool Immovable = false)
        {
            List<UIMergeGrid> guideGridList = new List<UIMergeGrid>();
            Dictionary<int, List<UIMergeGrid>> gridDic = new Dictionary<int, List<UIMergeGrid>>();

            if (mGridDic != null)
            {
                foreach (var grid in mGridDic)
                {
                    if (grid.Good != null && grid.Good.mData.id == cardID && !grid.Good.locked &&
                        grid.Good.mData.Immovable == Immovable)
                    {
                        if (goodClick)
                        {
                            if (grid.Good.CanClick)
                            {
                                guideGridList.Add(grid);
                            }
                        }
                        else
                        {
                            guideGridList.Add(grid);
                        }
                    }
                }
            }

            guideGridList.Sort((a, b) =>
            {
                if (a == null)
                {
                    return 1;
                }
                else if (b == null)
                {
                    return -1;
                }
                else
                {
                    return a.Point.x - b.Point.x;
                }
            });

            return guideGridList;
        }

        public void ShowGuideMerge()
        {
            guideGridList.Clear();
            guideGridList = GetGuideMergeGrid();

            if (guideGridList.Count <= 0)
            {
                if (!CreatGood_click)
                {
                    lastShowCreatGoodGuidetimer = Time.time;
                    CreatGood_click = true;
                }
            }
            else
            {
                CreatGood_click = false;
                lastShowMergeGuidetimer = Time.time;
                for (int i = 0; i < guideGridList.Count && guideGridList.Count > 1; i++)
                {
                    var target = guideGridList[guideGridList.Count - 1 - i];
                    guideGridList[i].ShowGuideMerge(target.Point.x, target.Point.y);
                }
            }
        }

        public void StopCreatGoodGuide(bool checkGuid = true)
        {
            //if (mergeSoliderGuid > 0 && mergeSoliderGuid < 100)
            //{
            //    return;
            //}

            if (checkGuid)
            {
                CheckGuid(false);
            }

            if (K3PlayerMgr.I.PlayerData.guiding == 0 && !MergeGreyItemGuiding &&
                !MergeGreyItemGuiding_1) //执行强引导中~ //mData?.clickCount >= 2 &&
            {
                GuideFingerObj.SetActive(false);
                CreateGuidMask.SetActive(false);
                MergerGuidMask.SetActive(false);

                CreatGood_click = false;
                lastShowCreatGoodGuidetimer = Time.time;
            }
        }

        public static void CreateGoodGuid()
        {
            var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
            if (uimerge == null)
            {
                PopupManager.I.ShowLayer<UIMerge>();
            }

            var guidData = new UIGuidData();

            guidData.guidItems.Add(new UIGuidItem()
            {
                UIName = "K3.UIMerge",
                slide = true,
                UIItem = "Root/mageSpace/clickRoot/toplayer/CreatGoodButton",
                //doubleClick = false,
                //forceGuid = false
            });

            UIGuid.StartGuid(guidData);
        }

        /// <summary>
        /// 新道具盒子
        /// </summary>
        public static void CreateGoodNewGuid(int sourceValue)
        {
            string _indexName = null;

            var uimerge = DeepUI.PopupManager.I.FindPopup<UIMerge>();
            if (uimerge == null)
            {
                PopupManager.I.ShowLayer<UIMerge>();
            }

            var guidData = new UIGuidData();

            bool _OpenState = false;
            foreach (var item in Logic.CSPlayer.I.SpecialBoxs)
            {
                if (item.BoxCfg.Id + 9 == sourceValue)
                {
                    _OpenState = item.Unlock;
                }
            }

            //TODO:检测移动到外方，箱子未解锁直接不显示 
            if (_OpenState)
            {
                _indexName = "Root/bottomBtns/boxes/SpecialBox" + sourceValue;
            }
            else
            {
                _indexName = "Root/mageSpace/clickRoot/toplayer/CreatGoodButton";
            }

            guidData.guidItems.Add(new UIGuidItem()
            {
                UIName = "K3.UIMerge",
                slide = true,
                UIItem = _indexName,
                //doubleClick = false,
                //forceGuid = false
            });

            UIGuid.StartGuid(guidData);
        }

        //public void ShowCreatGoodGuide()
        //{
        //    if (UIGuid.HaveWakeGuid())
        //        return;

        //    CreatGood_click = false;
        //    GuideFingerObj.SetActive(true);
        //    DOTween.Kill(GuideFingerHand.transform);
        //    GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //    GuideFingerHand.transform.position = centerRoot.position;

        //    GuideFingerHand.gameObject.SetActive(true);
        //    GuideFingerHand.transform.DOMove(btnCreatGood.transform.position, 1.2f).OnComplete(() =>
        //    {
        //        GuideFingerHand.GetComponentInChildren<Animation>().enabled = true;
        //    });

        //    guidTextObj.gameObject.SetActive(false);
        //    CreateGuidMask.SetActive(false);
        //    MergerGuidMask.SetActive(false);

        //    guidTextObj.anchoredPosition = new Vector2(0, 800);
        //    guidText.text = TFW.Localization.LocalizationMgr.Get("dialogue5");

        //    K3PlayerMgr.I.PlayerData.AddCreateGuid();
        //    //K3PlayerMgr.I.SaveDataLocal();
        //}

        //public bool GetCreatGoodForBoxItem(ItemInfo itemInfo)
        //{
        //    int code = itemInfo.Code;

        //    List<int> idlist = CSVItem.GetBoxInfoItemIdByCode(code);
        //    UIMergeGood goodTo = null;
        //    List<UIMergeGood> goods = new List<UIMergeGood>();
        //    foreach (var good in mGoodDic)
        //    {
        //        if (idlist.Contains(good.Value.mData.id) &&
        //            K3PlayerMgr.I.AreaData.NoPoint(new Vector2(good.Value.mPoint.x, good.Value.mPoint.y)) &&
        //            !good.Value.locked && !good.Value.mData.Immovable)
        //        {
        //            goods.Add(good.Value);
        //        }
        //    }

        //    return goods.Count > 0;
        //}

        public void ShowCreatGoodForBoxItemGuide(ItemInfo itemInfo)
        {
            int code = itemInfo.Code;

            List<int> idlist = CSVItem.GetBoxInfoItemIdByCode(code);
            UIMergeGood goodTo = null;
            List<UIMergeGood> goods = new List<UIMergeGood>();
            foreach (var good in mGoodDic)
            {
                if (good != null && idlist.Contains(good.mData.id) && !good.locked && !good.mData.Immovable)
                {
                    goods.Add(good);
                }
            }

            if (goods.Count > 0)
            {
                for (int i = 0; i < goods.Count; i++)
                {
                    if (K3PlayerMgr.I.AreaData.NoPoint(new Vector2(goods[i].mPoint.x, goods[i].mPoint.y)))
                    {
                        goodTo = goods[i];
                        break;
                    }
                }

                if (goodTo != null)
                {
                    var guidData = new UIGuidData();

                    guidData.guidItems.Add(new UIGuidItem()
                    {
                        UIName = "UIMerge",
                        slide = true,
                        //UIItemGa = goodTo.gameObject,
                        //doubleClick = false,
                        //forceGuid = false
                    });

                    UIGuid.StartGuid(guidData);
                    return;
                }
                else
                {
                    //不再引导点击云直接点箱子

                    //--引导点击云
                    goodTo = goods[0];
                    int areaId =
                        K3PlayerMgr.I.AreaData.GetAreaIDByGoodPoint(new Vector2(goodTo.mPoint.x, goodTo.mPoint.y));

                    List<UIMergeAreaComp> AreaComps = areaParent.GetComponentsInChildren<UIMergeAreaComp>().ToList();

                    GameObject guideTo = null;
                    for (int i = 0; i < AreaComps.Count; i++)
                    {
                        if (AreaComps[i].CheckId(areaId) &&
                            (areaId == 1001 || K3PlayerMgr.I.AreaData.AreaedData.Contains(areaId - 1))) //前置云彩解锁才可
                        {
                            guideTo = AreaComps[i].gameObject;
                            break;
                        }
                    }

                    if (guideTo != null)
                    {
                        var guidData = new UIGuidData();

                        guidData.guidItems.Add(new UIGuidItem()
                        {
                            UIName = "UIMerge",
                            slide = true,
                            //UIItemGa = guideTo.gameObject,
                            //doubleClick = false,
                            //forceGuid = false
                        });

                        UIGuid.StartGuid(guidData);
                        return;
                    }
                    //else
                    //{
                    //    if (idlist.Contains(curBox.id))
                    //    {
                    //        var guidData = new UIGuidData();
                    //        guidData.guidItems.Add(new UIGuidItem()
                    //        {
                    //            UIName = "UIMerge",
                    //            UIItem = "Root/bottom/GoodButtonBG/GoodButton",
                    //            slide = true,
                    //            forceGuid = false
                    //        });
                    //        UIGuid.StartGuid(guidData);
                    //        return;
                    //    }

                    //}
                }
            }

            //ShowCreatGoodGuide();
        }


        public void StopWaitShowGuideMerge()
        {
            lastShowMergeGuidetimer = Time.time;
            StopCreatGoodGuide();
        }

        //public void AddGoods(List<ItemInfo> infoList, List<ItemInfo> boxInfoList, string reson)
        //{
        //    BoxAni.SetTrigger("show");
        //    if (boxInfoList.Count > 0)
        //    {
        //        GameAudio.PlayAudio(15);
        //    }

        //    int index = 0;
        //    List<UIMergeGrid> grids = GetFreeGrids(infoList.Count);
        //    for (int i = 0; i < boxInfoList.Count; i++)
        //    {
        //        mData.boxidQueue.Add(boxInfoList[i].id);
        //    }
        //    for (int i = 0; i < grids.Count; i++)
        //    {
        //        if (grids[i] != null)
        //        {
        //            UIMergeGrid gride = grids[i];
        //            UIMergeGood good = GetGood();
        //            good.transform.position = btnCreatGood.transform.position;
        //            good.Init(gride, infoList[i], MoveGoodRoot, this, false, 0, false, i * 0.15f);

        //            var createEvent = new CreateEvent() { EventKey = "createevent" };
        //            createEvent.Properties.Add("from", reson);
        //            createEvent.Properties.Add("id", good.Info.id);
        //            K3GameEvent.I.TaLog(createEvent);
        //        }
        //    }

        //    if (mData.boxidQueue.Count > 0 && curBox == null)
        //    {
        //        UpdateBox();
        //    }

        //    CheckGuid();
        //}
        //public void CheckAtlasRedPoint()
        //{
        //    redPoint_Atlas.SetActive(K3PlayerMgr.I.GetAtlasRedPoint());
        //}

        private void UpdateBubblePanelActive()
        {
            if (bubblePanel.activeInHierarchy)
            {
                bubblePanel.SetActive(false);
            }
        }

        //public void GMAddGood(int count, int id)
        //{
        //    List<UIMergeGrid> grids = GetFreeGrids(count);
        //    ItemInfo info = CSVItem.GetItemInfoById(id);
        //    for (int i = 0; i < grids.Count; i++)
        //    {
        //        if (grids[i] != null)
        //        {
        //            UIMergeGrid gride = grids[i];
        //            UIMergeGood good = GetGood();
        //            good.transform.position = btnBox.transform.position;

        //            GameAudio.PlayAudio(6);

        //            good.Init(gride, info, MoveGoodRoot, this, false, 0, true, i * 0.15f);
        //        }
        //    }
        //}

        public void SetBubblePanelState(bool isShow)
        {
            bubblePanel.SetActive(isShow);
        }

        //public void GuideTOBoxItem(int goodID, string desText)
        //{
        //    UIMergeGood goodTo = null;

        //    foreach (var good in mGoodDic)
        //    {
        //        if (good.Value.mData.id == goodID && K3PlayerMgr.I.AreaData.NoPoint(new Point(good.Key)))
        //        {
        //            goodTo = good.Value;
        //        }
        //    }

        //    if (goodTo != null)
        //    {
        //        var guidData = new UIGuidData();

        //        guidData.guidItems.Add(new UIGuidItem()
        //        {
        //            UIName = "UIMerge",
        //            slide = true,
        //            UIItemGa = goodTo.gameObject,
        //            d7Desc = TFW.Localization.LocalizationMgr.Format(desText),
        //            d7ObjY = 0,
        //            doubleClick = false,
        //            forceGuid = true
        //        });

        //        UIGuid.StartGuid(guidData, true);
        //    }
        //}

        public void AreaUnlockRefreshItem()
        {
            foreach (var item in mGoodDic)
            {
                if (item != null && K3PlayerMgr.I.AreaData.NoPoint(item.mPoint) && !item.mData.Immovable &&
                    item.Info.Type > 0)
                {
                    K3PlayerMgr.I.UnlockedId(item.mData.id);
                }
            }
        }

        public void RefreshCurBoxItemPrice(UIMergeSpecialBox good)
        {
            if (curSpecialBox != null && curSpecialBox == good)
            {
                BoxItemRestCDPriceText.text = good.GetCurRestCDCost().ToString();

                TimeSpan cdTimer = TimeSpan.FromSeconds(curSpecialBox.CurBoxUseCd());
                BoxItemRestCDText.text = cdTimer.ToString(@"h\:mm\:ss");
            }
        }

        //public bool IsOpenSoliderHouse()
        //{
        //    bool isOpen = false;
        //    foreach (var good in mGoodDic)
        //    {
        //        if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID)
        //        {
        //            isOpen = true;
        //            break;
        //        }
        //    }
        //    return isOpen;
        //}

        //public Vector2Int GetSoliderCanMerge()
        //{
        //    Vector2Int vector2 = Vector2Int.zero;
        //    Dictionary<int, List<int>> dic = new Dictionary<int, List<int>>();
        //    foreach (var good in mGoodDic)
        //    {
        //        if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.Info.Code > 20 &&
        //            good.Value.Info.Level < CSVItem.GetMaxLevelByTypeAndCode(good.Value.Info.id))
        //        {
        //            if (!dic.ContainsKey(good.Value.Info.id))
        //            {
        //                dic.Add(good.Value.Info.id, new List<int>());
        //            }

        //            dic[good.Value.Info.id].Add(good.Value.mPoint.ServerIndex);
        //        }
        //    }

        //    var _t = dic.Where(t => t.Value.Count >= 2).FirstOrDefault().Value;
        //    if (_t != null)
        //    {
        //        vector2 = new Vector2Int(_t[0], _t[1]);
        //    }

        //    return vector2;
        //}

        //public Vector2Int DragSoliderToHouse()
        //{
        //    Vector2Int vector2 = Vector2Int.zero;
        //    int index = 0;
        //    foreach (var good in mGoodDic)
        //    {
        //        if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID)
        //        {
        //            index = good.Value.mPoint.ServerIndex;
        //            break;
        //        }
        //    }
        //    vector2 = new Vector2Int(BasicX, index);
        //    return vector2;
        //}

        //public Vector3 GetPos(int index)
        //{
        //    return mGoodDic[index].transform.position;
        //}

        //public int mergeSoliderGuid = 0;

        //public void BeginGuid1(Vector2Int pos, int index)
        //{
        //    BasicVector = pos;
        //    GuideFingerObj.SetActive(true);
        //    DOTween.Kill(GuideFingerHand.transform);
        //    guidTimer?.Stop();
        //    Vector3 pos1 = GetPos(pos.x);
        //    Vector3 pos2 = GetPos(pos.y);
        //    GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //    GuideFingerHand.transform.position = pos1;
        //    GuideFingerHand.gameObject.SetActive(false);
        //    guidTimer = NTimer.CountDownNoPool(0.0f, () =>
        //    {
        //        GuideFingerHand.gameObject.SetActive(true);
        //        GuideFingerHand.transform.DOMove(pos2, 1f).SetLoops(-1);
        //    });

        //    guidTextObj.gameObject.SetActive(false);
        //    guidTextObj.anchoredPosition = new Vector2(0, 800);

        //    MergerGuidMask.transform.position = new Vector3(MergerGuidMask.transform.position.x,
        //        Math.Max(pos1.y, pos2.y), MergerGuidMask.transform.position.z);
        //    MergerGuidMask.SetActive(true);
        //    MergerGuidMask.transform.Find("top").GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 75);
        //    MergerGuidMask.transform.Find("bottom").GetComponent<RectTransform>().anchoredPosition =
        //        new Vector2(0, -75);

        //    mergeSoliderGuid = index;
        //}

        //private Vector2Int BasicVector;
        //private int BasicX;


        // 视频所用
        //public void FlyMergeItem(GameObject target, UIMergeGood good, Action callBack)
        //{
        //    int item_id = good.Info.id;

        //    //UITools.SetCommonItemIcon(icon, Info.Icon);
        //    TFWImage Icon = new GameObject("Icon").AddComponent<TFWImage>();

        //    UITools.SetCommonItemIcon(Icon, CSVItem.GetItemInfoById(item_id).Icon.ToString());
        //    Icon.transform.SetParent(MoveGoodRoot.transform);
        //    Icon.transform.position = good.icon.transform.position;
        //    Icon.transform.localScale = Vector3.one;
        //    Icon.rectTransform.sizeDelta = new Vector2(146, 146);
        //    good.gameObject.SetActive(false);


        //    GameObject jump_effect =
        //        GameObject.Instantiate(GameObject.transform.Find("Root/Ui_TuoWei1").gameObject, Icon.transform);
        //    jump_effect.transform.localPosition = Vector3.zero;

        //    var DoTweenSeq = DOTween.Sequence();
        //    Vector3[] path = new Vector3[3];
        //    path[0] = Icon.transform.position; //起始点
        //    path[2] = Icon.transform.position + new Vector3(0.07f, -0.25f); //终点 
        //    DoTweenSeq.Insert(0, Icon.transform.DOScale(1.1f, 0.3f));

        //    DoTweenSeq.Insert(0, DOJump(Icon.transform, path[2], 0.2f, 1, 0.35f, false)); //InOutSine

        //    DoTweenSeq.OnComplete(() =>
        //    {
        //        var DoTweenSeq1 = DOTween.Sequence();
        //        Vector3[] path1 = new Vector3[3];
        //        path1[0] = Icon.transform.position; //起始点
        //        path1[2] = target.transform.position; //终点 
        //        float dis = Vector2.Distance(Icon.transform.position, target.transform.position);
        //        float time = dis / 8f;

        //        DoTweenSeq1.Insert(0, Icon.transform.DOScale(0.5f, time));


        //        DoTweenSeq1.Insert(0,
        //            DOJump(Icon.transform, path1[2], 0.5f, 1, time, false).SetEase(Ease.Linear)); //InOutSine
        //        DoTweenSeq1.OnComplete(() =>
        //        {
        //            Icon.DOFade(0, 0.1f);
        //            GameObject.Destroy(Icon.gameObject, 0.25f);

        //            callBack?.Invoke();
        //        });
        //    });
        //}


        //private GameObject SevereEffect;

        //private void MergeSolider_Update()
        //{
        //    bool isSevere = false;
        //    foreach (var item in CSPlayer.I.mergeSoldierStoreData.GetDisplaySoldiers())
        //    {
        //        foreach (var item1 in item.displaySoldiers)
        //        {
        //            if (item1.y == 2)
        //            {
        //                isSevere = true;
        //                break;
        //            }
        //        }
        //    }

        //    foreach (var good in mGoodDic)
        //    {
        //        if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID)
        //        {
        //            if (isSevere)
        //            {
        //                if (SevereEffect == null)
        //                {
        //                    SevereEffect = GameObject.Instantiate(Transform.transform.Find("Root/severe").gameObject, good.Value.transform);
        //                    SevereEffect.transform.localPosition = Vector3.zero;
        //                    SevereEffect.transform.localScale = Vector3.one * 0.85f;
        //                    SevereEffect.GetComponent<TFWImage>().DOKill();
        //                    SevereEffect.GetComponent<TFWImage>().color = new Color(1, 0, 0, 0.2f);
        //                    SevereEffect.GetComponent<TFWImage>().DOColor(new Color(1, 0, 0, 0.65f), 0.5f).SetLoops(-1, LoopType.Yoyo);
        //                }
        //            }
        //            else
        //            {
        //                if (SevereEffect != null)
        //                {
        //                    GameObject.Destroy(SevereEffect.gameObject);
        //                    SevereEffect = null;
        //                }
        //            }
        //            break;
        //        }
        //    }

        //    //if (autoMergeingGM)
        //    //{
        //    //    return;
        //    //}

        //    if (mergeSoliderGuid == 0)
        //    {
        //        if (!PopupManager.I.AnyPopup_NOLayer && !UIGuid.HaveWakeGuid())
        //        {
        //            if (IsOpenSoliderHouse())
        //            {
        //                Vector2Int vector2 = GetSoliderCanMerge();
        //                if (vector2.x != 0 && vector2.y != 0)
        //                {
        //                    mergeSoliderGuid = 5;

        //                    NTimer.CountDown(0.0f, () =>
        //                    {
        //                        if (GuidManage.TriggerGuid(GuidManage.GuidTriggerType.MergeSolider, 0))
        //                        {
        //                        }
        //                        else
        //                        {
        //                        }
        //                    });
        //                }
        //            }
        //        }
        //    }
        //    else if (mergeSoliderGuid == 1)
        //    {
        //        if (mGoodDic.ContainsKey(BasicVector.x) && mGoodDic.ContainsKey(BasicVector.y))
        //        {
        //        }
        //        else
        //        {
        //            GuideFingerObj.SetActive(false);
        //            CreateGuidMask.SetActive(false);
        //            MergerGuidMask.SetActive(false);
        //            if (mGoodDic.ContainsKey(BasicVector.x))
        //            {
        //                BasicX = BasicVector.x;
        //            }
        //            else
        //            {
        //                BasicX = BasicVector.y;
        //            }
        //            mergeSoliderGuid = 2;

        //            WndMgr.Show<UIEpisodeGuid>(new UIEpisodeGuidItemData
        //            {
        //                EpisodeCfgID = 200001,
        //                guidItems = new List<int>() { 20000103, 20000104 }
        //            });
        //        }
        //    }
        //    else if (mergeSoliderGuid == 3)
        //    {
        //        if (!mGoodDic.ContainsKey(BasicX))
        //        {
        //            GuideFingerObj.SetActive(false);
        //            CreateGuidMask.SetActive(false);
        //            MergerGuidMask.SetActive(false);
        //            mergeSoliderGuid = 4;

        //            CreatGood_click = false;
        //            GuideFingerObj.SetActive(true);
        //            DOTween.Kill(GuideFingerHand.transform);
        //            GuideFingerHand.GetComponentInChildren<Animation>().enabled = false;
        //            int index = 0;
        //            foreach (var good in mGoodDic)
        //            {
        //                if (!good.Value.locked && !good.Value.mData.Immovable && good.Value.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID)
        //                {
        //                    index = good.Value.mPoint.ServerIndex;
        //                    break;
        //                }
        //            }
        //            GuideFingerHand.transform.position = centerRoot.position;

        //            GuideFingerHand.gameObject.SetActive(true);
        //            GuideFingerHand.transform.DOMove(GetPos(index), 1.2f).OnComplete(() =>
        //            {
        //                GuideFingerHand.GetComponentInChildren<Animation>().enabled = true;
        //            });

        //            CreateGuidMask.SetActive(false);
        //            MergerGuidMask.SetActive(false);
        //        }
        //    }
        //    else if (mergeSoliderGuid == 4)
        //    {
        //        var ui = PopupManager.I.FindPopup<UIMergeSoldierStorehouse>();
        //        if (ui != null)
        //        {
        //            GuideFingerObj.SetActive(false);
        //            CreateGuidMask.SetActive(false);
        //            MergerGuidMask.SetActive(false);
        //            mergeSoliderGuid = 1000;
        //            /*
        //                WndMgr.Show<UIEpisodeGuid>(new UIEpisodeGuidItemData
        //                {
        //                    EpisodeCfgID = 200002,
        //                    guidItems = new List<int>() { 20000201, 20000202, 20000203, 20000204, 20000205, 20000206, 20000207 }
        //                });
        //            */
        //            GuidManage.TriggerGuid(GuidManage.GuidTriggerType.GoInSoliderHouse, 0);
        //        }
        //    }
        //}

        //[PopupEvent(TEventType.QuestChangeNtf)]
        //private void OnBtnBackRedRefrsh(System.Object[] objs = null)
        //{
        //    // 每次刷新列表都取最新的任务数据
        //    //var activate = ChapterTaskMgr.I.GetCurrentChapterList().FirstOrDefault(x=>x.state == QuestState.QuestStateFinish);
        //    //btnBackRedDot.SetActive(activate != null);
        //}


        protected internal override bool Pop()
        {
            if (!StateMerge)
            {
                //一键上阵
                CSPlayer.I.SendMergeHeroBatchSetReq();

                EventMgr.FireEvent(TEventType.K3UIMerge_ToMerge);

                return true;
            }

            return base.Pop();
        }
    }
}