﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using UnityEditor;

namespace TFW.Map
{
    public static class MapPluginCreator
    {
        public static void CreateCSharpScripts(string folder, MapPluginLayerInfo layerInfo)
        {
            CreateLayerClass(folder, layerInfo);
            CreateLayerSettingWindow(folder, layerInfo);
            CreateLayerLogic(folder, layerInfo);
            CreateLayerEditor(folder, layerInfo);
            CreateProxyLayer(folder, layerInfo);

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        static void CreateLayerClass(string folder, MapPluginLayerInfo layerInfo)
        {
            string code = @"
using UnityEngine;
using TFW.Map;

$NamespaceBegin$
    
    /*
    layer的具体实现
    */
    public class $LayerName$ : MapPluginLayerBase
    {
        public $LayerName$(Map map) :base(map) { }

        //删除地图层
        public override void OnDestroy()
        {
        }
        
        //地图编辑器中刷新初始视野中的对象时使用
        public override void RefreshObjectsInViewport()
        {
        }

        //游戏运行时更新视野中的物体
        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = UpdateLODStatus(newCameraZoom);
            //********************************** custom code begin **********************************


            //********************************** custom code end   **********************************
            return lodChanged;
        }

        //加载地图运行时数据
        public override void LoadGameData()
        {
            CreateGameObject(""$LayerName$"");
            string runtimeFilePath = map.dataFolder + ""/"" + pluginLayerInfo.runtimeSaveFileName;
            //load runtime data from file at runtimeFilePath
            //********************************** custom code begin **********************************


            //********************************** custom code end   **********************************
            map.AddMapLayer(this);
        }

        //导出游戏数据接口
        public override void SaveGameData(string mapRuntimDataFolder) 
        { 
            string runtimeFilePath = mapRuntimDataFolder + ""/"" + pluginLayerInfo.runtimeSaveFileName;
            //save runtime data to file at runtimeFilePath
            //********************************** custom code begin **********************************


            //********************************** custom code end   **********************************
        }

        //保存地图编辑器数据接口
        public override void SaveEditorData(string mapEditorDataFolder) 
        {
            string editorFilePath = mapEditorDataFolder + ""/"" + pluginLayerInfo.editorSaveFileName;
            //save editor data to file at editorFilePath
            //********************************** custom code begin **********************************


            //********************************** custom code end   **********************************
        }

        //读取地图编辑器数据接口,返回是否读取成功
        public override bool LoadEditorData(string mapEditorDataFolder)
        {
            //需要手动修改loading是否成功能的标记
            string editorFilePath = mapEditorDataFolder + ""/"" + pluginLayerInfo.editorSaveFileName;
            if (!MapModuleResourceMgr.Exists(editorFilePath)){
                return false;
            }
            
            CreateGameObject(""$LayerName$"");
#if UNITY_EDITOR
            var logic = gameObject.AddComponent<$LayerLogic$>();
            logic.layerID = id;
#endif
            //********************************** custom code begin **********************************


            //********************************** custom code end   **********************************

            return true; 
        }

        //x方向上格子的数量
        public override int horizontalTileCount { get { return 1; } }
        //z方向上格子的数量
        public override int verticalTileCount { get { return 1; } }
        //格子的宽
        public override float tileWidth { get { return 7200; } }
        //格子的高
        public override float tileHeight { get { return 7200; } }
        public override int lodCount { get { return 1; } }
    }
$NamespaceEnd$
";

            TemplateParser parser = new TemplateParser();
            parser.SetInput(code);
            if (string.IsNullOrEmpty(layerInfo.namespaceName))
            {
                parser.SetReplacement("NamespaceBegin", "");
                parser.SetReplacement("NamespaceEnd", "");
            }
            else
            {
                parser.SetReplacement("NamespaceBegin", $"namespace {layerInfo.namespaceName}" + " {");
                parser.SetReplacement("NamespaceEnd", "}");
            }
            parser.SetReplacement("LayerName", layerInfo.layerClassName);
            parser.SetReplacement("LayerLogic", $"{layerInfo.layerClassName}Logic");
            code = parser.Generate();
            string filePath = $"{folder}/{layerInfo.layerClassName}.cs";
            File.WriteAllText(filePath, code);
        }

        static void CreateLayerSettingWindow(string folder, MapPluginLayerInfo layerInfo)
        {
            string code = @"
#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using TFW.Map;

$NamespaceBegin$
    /*
    在编辑器中Create Layer时弹出的参数设置面板
    */
    public class $LayerName$SettingWindow : MapPluginLayerSettingWindow
    {
        void OnGUI()
        {
            if (GUILayout.Button(""Create""))
            {
                var layer = MapPlugin.CreatePluginLayer(mLayerName, Map.currentMap);
                layer.CreateGameObject(mLayerName);
                var logic = layer.gameObject.AddComponent<$LayerName$Logic>();
                logic.layerID = layer.id;
                Map.currentMap.AddMapLayer(layer);
                if (layer != null)
                {
                    Selection.activeObject = layer.gameObject;
                }

                //********************************** custom code begin **********************************


                //********************************** custom code end   **********************************

                Close();
            }
        }
    }
$NamespaceEnd$

#endif
";
            TemplateParser parser = new TemplateParser();
            parser.SetInput(code);
            if (string.IsNullOrEmpty(layerInfo.namespaceName))
            {
                parser.SetReplacement("NamespaceBegin", "");
                parser.SetReplacement("NamespaceEnd", "");
            }
            else
            {
                parser.SetReplacement("NamespaceBegin", $"namespace {layerInfo.namespaceName}" + " {");
                parser.SetReplacement("NamespaceEnd", "}");
            }
            parser.SetReplacement("LayerName", layerInfo.layerClassName);
            code = parser.Generate();
            string filePath = $"{folder}/{layerInfo.layerClassName}SettingWindow.cs";
            File.WriteAllText(filePath, code);
        }

        static void CreateLayerLogic(string folder, MapPluginLayerInfo layerInfo)
        {
            string code = @"
#if UNITY_EDITOR

using UnityEngine;
using TFW.Map;

$NamespaceBegin$
    /*
    在地图编辑器中挂这个脚本从而可以实现自定义editor
    */
    [ExecuteInEditMode]
    class $LayerName$Logic : MapLayerLogic
    {
    }
$NamespaceEnd$

#endif
";
            TemplateParser parser = new TemplateParser();
            parser.SetInput(code);
            if (string.IsNullOrEmpty(layerInfo.namespaceName))
            {
                parser.SetReplacement("NamespaceBegin", "");
                parser.SetReplacement("NamespaceEnd", "");
            }
            else
            {
                parser.SetReplacement("NamespaceBegin", $"namespace {layerInfo.namespaceName}" + " {");
                parser.SetReplacement("NamespaceEnd", "}");
            }
            parser.SetReplacement("LayerName", layerInfo.layerClassName);
            code = parser.Generate();
            string filePath = $"{folder}/{layerInfo.layerClassName}Logic.cs";
            File.WriteAllText(filePath, code);
        }

        static void CreateLayerEditor(string folder, MapPluginLayerInfo layerInfo)
        {
            string code = @"
#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using TFW.Map;

$NamespaceBegin$
    /*
    在地图编辑器中plugin layer的editor
    */
    [CustomEditor(typeof($LayerName$Logic))]
    public class $LayerName$Editor : UnityEditor.Editor
    {
        void OnEnable()
        {
        }

        void OnSceneGUI()
        {
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
        }
    }
$NamespaceEnd$

#endif
";
            TemplateParser parser = new TemplateParser();
            parser.SetInput(code);
            if (string.IsNullOrEmpty(layerInfo.namespaceName))
            {
                parser.SetReplacement("NamespaceBegin", "");
                parser.SetReplacement("NamespaceEnd", "");
            }
            else
            {
                parser.SetReplacement("NamespaceBegin", $"namespace {layerInfo.namespaceName}" + " {");
                parser.SetReplacement("NamespaceEnd", "}");
            }
            parser.SetReplacement("LayerName", layerInfo.layerClassName);
            code = parser.Generate();
            string filePath = $"{folder}/{layerInfo.layerClassName}Editor.cs";
            File.WriteAllText(filePath, code);
        }

        //创建layer的wrapper
        static void CreateProxyLayer(string folder, MapPluginLayerInfo layerInfo)
        {
            string code = @"

using UnityEngine;
using TFW.Map;

$NamespaceBegin$
    public class $ProxyLayer$ : MapLayerPlugin
    {
        $LayerName$ mLayerImpl;
        
        public $ProxyLayer$(string name, GameObject layerRootNode)
        {
            LayerName = name;
        }

        public override void OnCurrentMapChange()
        {
            mLayerImpl = Map.currentMap.GetMapLayer(""$LayerName$"") as $LayerName$;
        }

        /// <summary>
        /// 帧更新.
        /// </summary>
        protected override void UpdateImpl()
        {
            if (mLayerImpl != null)
            {
                var curZoom = MapMgr.GetZoom();
                mLayerImpl.UpdateViewport(MapMgr.viewport, curZoom);
            }
        }

        public override int GetCurrentLOD()
        {
            if (mLayerImpl != null)
            {
                return mLayerImpl.GetCurrentLOD();
            }
            return -1;
        }
    }
$NamespaceEnd$
";
            TemplateParser parser = new TemplateParser();
            parser.SetInput(code);
            if (string.IsNullOrEmpty(layerInfo.namespaceName))
            {
                parser.SetReplacement("NamespaceBegin", "");
                parser.SetReplacement("NamespaceEnd", "");
            }
            else
            {
                parser.SetReplacement("NamespaceBegin", $"namespace {layerInfo.namespaceName}" + " {");
                parser.SetReplacement("NamespaceEnd", "}");
            }
            parser.SetReplacement("ProxyLayer", layerInfo.proxyLayerClassName);
            parser.SetReplacement("LayerName", layerInfo.layerClassName);
            code = parser.Generate();
            string filePath = $"{folder}/{layerInfo.proxyLayerClassName}.cs";
            File.WriteAllText(filePath, code);
        }
    }
}


#endif