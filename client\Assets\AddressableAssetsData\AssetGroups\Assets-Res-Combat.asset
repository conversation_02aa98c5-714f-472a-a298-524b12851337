%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb281ee3bf0b054c82ac2347e9e782c, type: 3}
  m_Name: Assets-Res-Combat
  m_EditorClassIdentifier: 
  m_GroupName: Assets-Res-Combat
  m_Data:
    m_SerializedData: []
  m_GUID: a796ccb153057ef459c8dbf55bbbe494
  m_SerializeEntries:
  - m_GUID: 372df757fe9fe0840bb07dfb3b68e09d
    m_Address: Assets/Res/Combat/Formations/NpcTroopMarch.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 636a9e6697c313a4b851a7867ed19782
    m_Address: Assets/Res/Combat/Formations/PlayerTroopBattle.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 8bbc8334e43e1f74e9c8b542b796e077
    m_Address: Assets/Res/Combat/Formations/PlayerTroopMarch.asset
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 125c144b21f2bc248b31b7ce697eceaa
    m_Address: Assets/Res/Combat/Data/model_config.xml
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  m_ReadOnly: 0
  m_Settings: {fileID: 11400000, guid: 0136e5c1707ffcc42a01c054e7a28fbc, type: 2}
  m_SchemaSet:
    m_Schemas:
    - {fileID: 11400000, guid: 0444432eea7b78042ace9e56558c8ead, type: 2}
    - {fileID: 11400000, guid: 7f8ba53e92ae4804f9bb7fde99860c8b, type: 2}
