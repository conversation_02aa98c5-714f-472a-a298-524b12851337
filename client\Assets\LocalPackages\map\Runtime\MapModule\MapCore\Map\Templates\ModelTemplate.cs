﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;
using System.Text.RegularExpressions;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public struct LODIndex
    {
        public LODIndex(int lod, int index)
        {
            this.lod = lod;
            this.index = index;
        }
        public int lod;
        public int index;
    }

    public class ChildPrefabTransform
    {
        //子prefab的transform信息
#if UNITY_EDITOR
        //宏以内的只在编辑器模式下使用
        public string path;
        public string tag;
        public Vector3 editorScaling;
        public Quaternion editorRotation;
        public short prefabInitInfoIndex = -1;
#endif
        //0到tile width的范围
        public Vector3 position;
        public Rect localBoundsInPrefab;
        public short scaleIndex = -1;
        public short rotationIndex = -1;
        public TileObjectType objectType;

        public Quaternion GetRotation(TransformTable table)
        {
            return table.GetRotation(rotationIndex);
        }

        public Vector3 GetScaling(TransformTable table)
        {
            return table.GetScale(scaleIndex);
        }
    }

    public class ModelTemplateLODInfo
    {
        public List<int> existedLODs;
        public List<string> lODPrefabPaths;
    }

    //地图对象的引用的模型的配置参数
    public class ModelTemplate : BaseObject
    {
        public ModelTemplate(int id, Map map, string prefabPath, Rect bounds, bool tileModelTemplate, List<List<ModelTemplate>> childrenModelTemplates, List<List<ChildPrefabTransform>> childPrefabTransforms, ModelTemplateLODInfo lodInfo, bool preload) : base(id, map)
        {
            mPrefabPath = prefabPath;
            mPreload = preload;
            mBounds = bounds;
            mTileModelTemplate = tileModelTemplate;
            mChildrenModelTemplates = childrenModelTemplates;
            mChildrenPrefabInfo = childPrefabTransforms;
#if UNITY_EDITOR
            if (mChildrenPrefabInfo != null)
            {
                if (mMap.isEditorMode)
                {
                    for (int lod = 0; lod < mChildrenPrefabInfo.Count; ++lod)
                    {
                        for (int i = 0; i < mChildrenPrefabInfo[lod].Count; ++i)
                        {
                            mChildrenPrefabInfo[lod][i].path = mChildrenModelTemplates[lod][i].GetLODPrefabPath(0);
                        }
                    }
                }
            }
#endif

            if (mMap.isEditorMode || lodInfo == null)
            {
                CheckExistedLODs();
            }
            else
            {
                if (lodInfo.existedLODs.Count == 0)
                {
                    mExistedLODs = null;
                    mLODPrefabPaths = null;
                }
                else
                {
                    mExistedLODs = lodInfo.existedLODs;
                    mLODPrefabPaths = lodInfo.lODPrefabPaths;
                }

            }

            CalculateChildObjectInfo();
        }

        public override void OnDestroy() { }

        public void Recreate()
        {
            mPrefabPath = GetLODPrefabPath(0);
            mChildrenPrefabInfo.Clear();
            CalculateChildObjectInfo();
            CheckExistedLODs();
        }

        public Rect bounds { get { return mBounds; } set { mBounds = value; } }
        //是否有lodLevel对应的prefab
        public bool ExistLOD(int lodLevel)
        {
            if (mExistedLODs == null)
            {
                return false;
            }
            for (int i = 0; i < mExistedLODs.Count; ++i)
            {
                if (mExistedLODs[i] == lodLevel)
                {
                    return true;
                }
            }
            return false;
        }

        //找到最近的比lod小的lod等级
        public int GetNearestLODSmallerThan(int lod)
        {
            if (mExistedLODs != null)
            {
                for (int i = mExistedLODs.Count - 1; i >= 0; --i)
                {
                    if (mExistedLODs[i] < lod)
                    {
                        return mExistedLODs[i];
                    }
                }
            }
            return -1;
        }

        //warning, we don't recalculate bounds!
        public void ReplacePrefab(string newPrefabPath)
        {
            Debug.Assert(isTileModelTemplate == false);
            mPrefabPath = newPrefabPath;
            CheckExistedLODs();
        }

#if UNITY_EDITOR
        //返回block row col对应的model template prefabPath
        public string GetLODPrefabPathOfBlock(int lodLevel, int row, int col)
        {
            Debug.Assert(lodLevel >= 0);
            var prefabPath = $"{mPrefabPathPrefix}_block{row}_{col}{MapCoreDef.MAP_PREFAB_LOD_PREFIX}{lodLevel}.bytes";
            return prefabPath;
        }
#endif

        public string GetLODPrefabPath(int lodLevel)
        {
            if (lodLevel < 0 || mExistedLODs == null)
            {
                return mPrefabPath;
            }

            if (lodLevel >= mLODPrefabPaths.Count)
            {
                lodLevel = mLODPrefabPaths.Count - 1;
            }
            return mLODPrefabPaths[lodLevel];
        }

        string CalculateLODPrefabPath(int lodLevel, string ext)
        {
            if (lodLevel < 0 || mExistedLODs == null)
            {
                return mPrefabPath;
            }

            int lodIdx = 0;
            for (int i = 0; i < mExistedLODs.Count; ++i)
            {
                if (mExistedLODs[i] <= lodLevel)
                {
                    lodIdx = i;
                }
            }

            var prefabPath = string.Format("{0}{1}.{2}", mPrefabPath, mExistedLODs[lodIdx], ext);
            return prefabPath;
        }

        //检测这个prefab有哪些lod,只有编辑器模式下才会运行
        void CheckExistedLODs()
        {
            //注意,只有以lod后缀结尾的文件路径才判断是否自动切换lod
            var idx = mPrefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (idx == -1)
            {
                return;
            }
            var lodSubstring = mPrefabPath.Substring(idx);
            lodSubstring = Utils.GetPathName(lodSubstring, false);
            Regex rx = new Regex(@"_lod\d+", RegexOptions.Compiled | RegexOptions.IgnoreCase);
            if (rx.Match(lodSubstring).Length != lodSubstring.Length)
            {
                return;
            }

            var ext = Utils.GetExtension(mPrefabPath);

            mExistedLODs = null;
            var prefixIdx = mPrefabPath.IndexOf(MapCoreDef.MAP_PREFAB_LOD_PREFIX);
            if (prefixIdx != -1)
            {
                mPrefabPathPrefix = mPrefabPath.Substring(0, prefixIdx);
                var prefixString = mPrefabPath.Substring(0, prefixIdx + MapCoreDef.MAP_PREFAB_LOD_PREFIX.Length);

                int nLods = 10;
                for (int i = 0; i < nLods; ++i)
                {
                    var lodPrefabPath = prefixString + i.ToString() + "." + ext;
                    if (MapModuleResourceMgr.Exists(lodPrefabPath))
                    {
                        if (mExistedLODs == null)
                        {
                            mExistedLODs = new List<int>();
                        }
                        mExistedLODs.Add(i);
                    }
                }

                if (mExistedLODs != null)
                {
                    mPrefabPath = prefixString;

                    //create path names for each lod!
                    mLODPrefabPaths = new List<string>(mExistedLODs.Count);
                    for (int i = 0; i < mExistedLODs.Count; ++i)
                    {
                        mLODPrefabPaths.Add(CalculateLODPrefabPath(i, ext));
                    }
                }
            }
        }

        public int lodCount
        {
            get
            {
                if (mExistedLODs == null)
                {
                    return 0;
                }
                return mExistedLODs.Count;
            }
        }

        //返回这个模型所有子节点的障碍物边界
        public PrefabOutline[] GetPrefabOutlines()
        {
#if UNITY_EDITOR
            string prefabPath = GetLODPrefabPath(0);
            var prefab = MapModuleResourceMgr.LoadPrefab(prefabPath);
            if (prefab != null)
            {
                return prefab.GetComponentsInChildren<PrefabOutline>();
            }
            else
            {
                Debug.Assert(false, string.Format("prefab {0} not found", prefabPath));
            }
#endif
            return null;
        }

        //返回lod的子model template的起始index
        public int GetChildModelTemplateStartIndex(int lod)
        {
            int index = 0;
            for (int i = 0; i < lod; ++i)
            {
                index += mChildrenModelTemplates[i].Count;
            }
            return index;
        }

        public int GetChildModelTemplateCount()
        {
            int n = 0;
            for (int i = 0; i < mChildrenModelTemplates.Count; ++i)
            {
                n += mChildrenModelTemplates[i].Count;
            }
            return n;
        }

        //only used int editor
        public void SetBlockSize(int width, int height, float tileSize)
        {
            int oldWidth = mBlockWidth;
            int oldHeight = mBlockHeight;
            if (oldWidth != width || oldHeight != height)
            {
                mBlockWidth = width;
                mBlockHeight = height;
                bool ok = CreateBlockPrefabs(tileSize);
                if (!ok)
                {
                    mBlockWidth = oldWidth;
                    mBlockHeight = oldHeight;
                }
            }
        }

        //检查block prefab是否已经创建,并且检查数量是否和当前block数一致
        public void CheckBlockPrefabs(float tileSize)
        {
#if UNITY_EDITOR
            ModelTemplateBlockPrefabCreator.CheckBlockPrefabs(this, tileSize);
#endif
        }

        public bool CreateBlockPrefabs(float tileSize)
        {
#if UNITY_EDITOR
            if (string.IsNullOrEmpty(SLGMakerEditor.instance.exportFolder))
            {
                EditorUtility.DisplayDialog("Error", "Set export folder first!", "OK");
                return false;
            }
            ModelTemplateBlockPrefabCreator.DeleteBlockPrefabs(mPrefabPathPrefix);
            ModelTemplateBlockPrefabCreator.CreateBlockPrefabs(this, tileSize);
            return true;
#else
            return false;
#endif
        }

        public List<ModelTemplate> GetChildrenModelTemplates(int lod)
        {
            if (lod >= 0 && lod < mChildrenModelTemplates.Count)
            {
                return mChildrenModelTemplates[lod];
            }

            //没有对应的lod就返回空,当作空lod
            return mEmptyList;
        }

        public List<ChildPrefabTransform> GetChildPrefabTransform(int lod)
        {
            if (lod >= 0 && lod < mChildrenPrefabInfo.Count)
            {
                return mChildrenPrefabInfo[lod];
            }

            //没有对应的lod就返回空,当作空lod
            return mEmptyPrefabList;
        }

        //计算tile model template子节点的包围框
        public void CalculateChildObjectInfo()
        {
#if UNITY_EDITOR
            if (isTileModelTemplate)
            {
                if (mChildrenPrefabInfo.Count == 0)
                {
                    mChildrenPrefabInfo = new List<List<ChildPrefabTransform>>();

                    for (int lod = 0; lod < lodCount; ++lod)
                    {
                        List<ChildPrefabTransform> prefabInfo = new List<ChildPrefabTransform>();
                        mChildrenPrefabInfo.Add(prefabInfo);
                        //保存子prefeb的信息
                        string lodPrefabPath = GetLODPrefabPath(lod);
                        if (MapModule.useFakePrefab &&
                            lodPrefabPath.EndsWith(MapCoreDef.FAKE_PREFAB_EXT) &&
                            MapCoreDef.IsModelTemplateGenerated(lodPrefabPath))
                        {
                            var prefab = mMap.fakePrefabManager.LoadPrefab(lodPrefabPath);
                            if (prefab != null)
                            {
                                var offset = new Vector2(mMap.frontTileSize * 0.5f, mMap.frontTileSize * 0.5f);

                                var transform = prefab.gameObject.transform;
                                int childPrefabCount = transform.childCount;
                                for (int i = 0; i < childPrefabCount; ++i)
                                {
                                    var childPrefab = transform.GetChild(i).gameObject;
                                    var fakePrefabInfo = prefab.childPrefabInfos[i];

                                    //if (childPrefab.CompareTag(MapCoreDef.IGNORED_OBJECT_TAG))
                                    //{
                                    //    continue;
                                    //}
                                    //var childBounds = GameObjectBoundsCalculator.CalculateRect(childTransform.gameObject, false);
                                    var childBounds = fakePrefabInfo.bounds;
                                    var min = childBounds.min;
                                    var max = childBounds.max;

                                    var childPos = childPrefab.transform.position + new Vector3(offset.x, 0, offset.y);
                                    childBounds.Set(min.x + offset.x, min.y + offset.y, max.x - min.x, max.y - min.y);

                                    if (childPrefab.CompareTag(MapCoreDef.CAMERA_COLLIDER_TAG))
                                    {
                                        //collider must be always visible when tile is visible
                                        childBounds = new Rect(0, 0, mMap.frontTileSize, mMap.frontTileSize);
                                    }

                                    var childPrefabTransform = new ChildPrefabTransform();
                                    childPrefabTransform.path = fakePrefabInfo.prefabPath;
                                    childPrefabTransform.localBoundsInPrefab = childBounds;
                                    childPrefabTransform.tag = childPrefab.tag;
                                    childPrefabTransform.objectType = MapCoreDef.GetTileObjectType(childPrefab.transform.tag);
                                    childPrefabTransform.position = childPos;
                                    childPrefabTransform.editorScaling = childPrefab.transform.localScale;
                                    childPrefabTransform.editorRotation = childPrefab.transform.localRotation;
                                    prefabInfo.Add(childPrefabTransform);
                                }
                            }
                        }
                        else
                        {
                            var tilePrefab = AssetDatabase.LoadAssetAtPath<GameObject>(lodPrefabPath);
                            if (tilePrefab != null)
                            {
                                int n = tilePrefab.transform.childCount;

                                var modelTemplateObject = tilePrefab;
                                var offset = new Vector2(mMap.frontTileSize * 0.5f, mMap.frontTileSize * 0.5f);

                                for (int i = 0; i < n; ++i)
                                {
                                    var childTransform = tilePrefab.transform.GetChild(i);
                                    var childPrefabInstance = childTransform.gameObject;

                                    //获取prefab instance使用的prefab
                                    var childPrefab = PrefabUtility.GetCorrespondingObjectFromSource(childPrefabInstance);

                                    var childPrefabAssetPath = AssetDatabase.GetAssetPath(childPrefab);
                                    if (string.IsNullOrEmpty(childPrefabAssetPath))
                                    {
                                        Debug.LogError($"invalid child prefab {i} of {lodPrefabPath}");
                                    }

                                    var childGameObject = modelTemplateObject.transform.GetChild(i).gameObject;
                                    //if (childGameObject.CompareTag(MapCoreDef.IGNORED_OBJECT_TAG))
                                    //{
                                    //    continue;
                                    //}
                                    var childBounds = GameObjectBoundsCalculator.CalculateRect(childTransform.gameObject, false);
                                    var min = childBounds.min;
                                    var max = childBounds.max;

                                    var childPos = childTransform.transform.position + new Vector3(offset.x, 0, offset.y);
                                    childBounds.Set(min.x + offset.x, min.y + offset.y, max.x - min.x, max.y - min.y);

                                    if (childGameObject.CompareTag(MapCoreDef.CAMERA_COLLIDER_TAG))
                                    {
                                        //collider must be always visible when tile is visible
                                        childBounds = new Rect(0, 0, mMap.frontTileSize, mMap.frontTileSize);
                                    }

                                    var childPrefabTransform = new ChildPrefabTransform();
                                    childPrefabTransform.path = childPrefabAssetPath;
                                    childPrefabTransform.localBoundsInPrefab = childBounds;
                                    childPrefabTransform.tag = childTransform.tag;
                                    childPrefabTransform.objectType = MapCoreDef.GetTileObjectType(childTransform.tag);
                                    childPrefabTransform.position = childPos;
                                    childPrefabTransform.editorScaling = childTransform.transform.localScale;
                                    childPrefabTransform.editorRotation = childTransform.transform.rotation;
                                    prefabInfo.Add(childPrefabTransform);
                                }
                            }
                        }
                    }
                }
            }
#endif
        }

        public bool isTileModelTemplate
        {
            get
            {
                return mTileModelTemplate;
            }
            set
            {
                mPrefabPath = GetLODPrefabPath(0);
                mChildrenPrefabInfo.Clear();
                mTileModelTemplate = value;
                CalculateChildObjectInfo();
                CheckExistedLODs();
            }
        }
        public List<List<ChildPrefabTransform>> childrenPrefabInfo { get { return mChildrenPrefabInfo; } }
        public bool generated { get { return MapCoreDef.IsModelTemplateGenerated(this); } }
        public bool preload { set { mPreload = value; } get { return mPreload; } }
        public bool isValid
        {
            get
            {
#if UNITY_EDITOR
                return AssetDatabase.LoadAssetAtPath<GameObject>(GetLODPrefabPath(0)) != null;
#else
                return true;
#endif
            }
        }

        protected string mPrefabPathPrefix;
        //prefab的路径,如果prefab有lod,则是lod的前缀路径
        protected string mPrefabPath;
        //prefab的bounding box,用于四叉树管理
        protected Rect mBounds;
        //prefab有效的lod等级
        protected List<int> mExistedLODs;
        //地块上的子prefab物体信息
        protected List<List<ModelTemplate>> mChildrenModelTemplates = new List<List<ModelTemplate>>();
        protected List<List<ChildPrefabTransform>> mChildrenPrefabInfo = new List<List<ChildPrefabTransform>>();
        bool mTileModelTemplate = false;
        bool mPreload = false;
        //Block大小,如果block不是1x1,例如2x3,则会根据原始prefab文件生成2x3个子model template,设置到对应的2x3个tile上
        int mBlockWidth = 1;
        int mBlockHeight = 1;

        public List<int> existedLODs { get { return mExistedLODs; } }
        public List<string> lodPrefabPaths { get { return mLODPrefabPaths; } }
        public int blockWidth { get { return mBlockWidth; } }
        public int blockHeight { get { return mBlockHeight; } }
        public string prefabPathPrefix { get { return mPrefabPathPrefix; } }

        //这个model template所有lod的路径名,用来减少gc
        List<string> mLODPrefabPaths;

        static List<ModelTemplate> mEmptyList = new List<ModelTemplate>();
        static List<ChildPrefabTransform> mEmptyPrefabList = new List<ChildPrefabTransform>();
    };
}
