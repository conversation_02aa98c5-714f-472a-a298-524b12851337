﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //获取鼠标的移动信息
    public class MouseMover
    {
        public void Update(Vector3 pos)
        {
            if (mLastPosition == Vector3.zero)
            {
                mLastPosition = pos;
            }
            else
            {
                mLastPosition = mCurrentPosition;
            }
            mCurrentPosition = pos;
        }

        public Vector3 GetDelta()
        {
            if (mLastPosition == Vector3.zero)
            {
                return Vector3.zero;
            }
            return mCurrentPosition - mLastPosition;
        }

        public void Reset()
        {
            mLastPosition = Vector3.zero;
            mCurrentPosition = Vector3.zero;
        }

        Vector3 mLastPosition;
        Vector3 mCurrentPosition;
    }
}
