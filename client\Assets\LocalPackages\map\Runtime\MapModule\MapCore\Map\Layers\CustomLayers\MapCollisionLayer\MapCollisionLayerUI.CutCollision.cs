﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class MapCollisionLayerUI : UnityEditor.Editor
    {
        void OnCutCollisionSceneGUI(Event currentEvent, Vector3 worldPos)
        {
            var layer = mLogic.layer;
            if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0)
            {
                if (currentEvent.control == false)
                {
                    if (mStartVertexIndex == -1)
                    {
                        PickVertex(worldPos, out mStartVertexObjectID, out mStartVertexIndex);
                    }
                    else
                    {
                        int objectID;
                        int vertexIndex;
                        PickVertex(worldPos, out objectID, out vertexIndex);
                        if (vertexIndex != -1 && vertexIndex != mStartVertexIndex && objectID == mStartVertexObjectID)
                        {
                            bool ok = Split(layer.displayType, mStartVertexObjectID, mStartVertexIndex, vertexIndex);
                            if (ok)
                            {
                                mLogic.operation = MapCollisionOperation.EditCollisionVertex;
                            }
                        }
                        mStartVertexIndex = -1;
                        mStartVertexObjectID = 0;
                    }
                    SceneView.RepaintAll();
                }
            }

            if (mStartVertexIndex != -1)
            {
                var collision = Map.currentMap.FindObject(mStartVertexObjectID) as MapCollisionData;
                Handles.DrawWireDisc(collision.GetVertexPos(layer.displayType, mStartVertexIndex), Vector3.up, layer.displayVertexRadius * 1.5f);
            }

            HandleUtility.AddDefaultControl(0);
        }

        bool GetSplitVertices(List<Vector3> vertices, int startVertexIndex, int endVertexIndex, out List<Vector3> leftVertices, out List<Vector3> rightVertices)
        {
            leftVertices = new List<Vector3>();
            rightVertices = new List<Vector3>();

            int minVertex = Mathf.Min(startVertexIndex, endVertexIndex);
            int maxVertex = Mathf.Max(startVertexIndex, endVertexIndex);

            int min = minVertex;
            int max = maxVertex;
            while (min != max)
            {
                leftVertices.Add(vertices[min]);
                min = (min + 1) % vertices.Count;
            }
            leftVertices.Add(vertices[max]);

            min = minVertex;
            max = maxVertex;
            while (max != min)
            {
                rightVertices.Add(vertices[max]);
                max = (max + 1) % vertices.Count;
            }
            rightVertices.Add(vertices[min]);

            return leftVertices.Count >= 3 && rightVertices.Count >= 3;
        }

        bool Split(PrefabOutlineType outlineType, int collisionID, int startVertexIndex, int endVertexIndex)
        {
            var collision = Map.currentMap.FindObject(collisionID) as MapCollisionData;
            List<Vector3> leftVertices;
            List<Vector3> rightVertices;
            bool ok = GetSplitVertices(collision.GetOutlineVerticesCopy(outlineType), startVertexIndex, endVertexIndex, out leftVertices, out rightVertices);
            if (!ok)
            {
                return false;
            }

            var actions = new CompoundAction("Split collision");

            //remove old collision
            var removeAction = new ActionRemoveMapCollision(mLogic.layerID, collisionID);
            actions.Add(removeAction);

            //create 2 new collision
            var createLeftAction = new ActionAddMapCollision(mLogic.layerID, Map.currentMap.nextCustomObjectID, leftVertices, true, collision.attribute, collision.type, 0);
            var createRightAction = new ActionAddMapCollision(mLogic.layerID, Map.currentMap.nextCustomObjectID, rightVertices, true, collision.attribute, collision.type, 0);

            actions.Add(createLeftAction);
            actions.Add(createRightAction);

            ActionManager.instance.PushAction(actions);

            return true;
        }

        int mStartVertexIndex = -1;
        int mStartVertexObjectID = 0;
    }
}


#endif