﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class ObjectPlacementEditor : EditorWindow
    {
        void DrawPolygonFillToolSceneGUI(Event e, Vector3 worldPos)
        {
            if (e.alt)
            {
                return;
            }

            if (e.type == EventType.MouseDown && e.button == 0)
            {
                if (mStartSetPoint)
                {
                    mPolygonVertices.Clear();
                    mStartSetPoint = false;
                }
                if (mPolygonVertices.Count == 0)
                {
                    mPolygonVertices.Add(worldPos);
                }
                mPolygonVertices.Add(worldPos);
                mDiplayPolygonVertices = mPolygonVertices.ToArray();
            }
            else if (e.type == EventType.MouseMove && e.button == 0)
            {
                if (mPolygonVertices.Count > 0 && mDiplayPolygonVertices != null && mDiplayPolygonVertices.Length == mPolygonVertices.Count)
                {
                    mPolygonVertices[mPolygonVertices.Count - 1] = worldPos;
                    mDiplayPolygonVertices[mPolygonVertices.Count - 1] = worldPos;
                }
            }
            else if (e.type == EventType.MouseUp && e.button == 1)
            {
                if (mPolygonVertices.Count > 3)
                {
                    mPolygonVertices.RemoveAt(mPolygonVertices.Count - 1);
                    FillPolygon();
                }
                else
                {
                    mPolygonVertices.Clear();
                }
                mDiplayPolygonVertices = null;
                mStartSetPoint = true;
            }

            if (mPolygonVertices.Count > 0 && mDiplayPolygonVertices != null)
            {
                Handles.DrawPolyLine(mDiplayPolygonVertices);
            }
        }

        void RegenerateObjectsInPolygon()
        {
            if (mPolygonVertices.Count > 0)
            {
                Undo.PerformUndo();
                FillPolygon();
            }
        }

        void FillPolygon()
        {
            if (mPrefabManager.selectedPrefab != null)
            {
                var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();

                List<Vector2> positions = null;
                if (mFillEdge)
                {
                    positions = GenerateEdgePoints();
                }
                else
                {
                    MapRegionPointGenerator gen = new MapRegionPointGenerator(new Rect(-10000f, -10000f, 20000f, 20000f));
                    gen.Create(new List<List<Vector3>>() { mPolygonVertices }, new List<List<Vector3>>(), mFillCount, 0, 0, new RandomWrapper((int)Time.time), 0, (List<Vector2> generatedPoints, Vector3 pos) =>
                    {
                        return !IsValid(pos, generatedPoints, mMinDistance);
                    }, 0, null);
                    positions = gen.generatedPoints;
                }

                for (int i = 0; i < positions.Count; ++i)
                {
                    var prefab = mPrefabManager.selectedPrefab;
                    CreateObject(Utils.ToVector3(positions[i]), prefab, stage.prefabContentsRoot.transform);
                }
            }
        }

        float[] CalculateChanceTable()
        {
            float[] chanceTable = new float[mPolygonVertices.Count];
            float totalLength = 0;
            for (int i = 0; i < chanceTable.Length; ++i)
            {
                var dir = mPolygonVertices[(i + 1) % mPolygonVertices.Count] - mPolygonVertices[i];
                totalLength += dir.magnitude;
                chanceTable[i] = totalLength;
            }

            for (int i = 0; i < chanceTable.Length; ++i)
            {
                chanceTable[i] /= totalLength;
            }
            return chanceTable;
        }

        List<Vector2> GenerateEdgePoints()
        {
            int tryCount = 500;
            List<Vector3> points = new List<Vector3>();
            int n = mPolygonVertices.Count;

            float[] chanceTable = CalculateChanceTable();

            for (int i = 0; i < mFillCount; ++i)
            {
                for (int t = 0; t < tryCount; ++t)
                {
                    float chance = Random.Range(0.0f, 1.0f);
                    int vertexIndex = -1;
                    for (int c = 0; c < chanceTable.Length; ++c)
                    {
                        if (chance <= chanceTable[c])
                        {
                            vertexIndex = c;
                            break;
                        }
                    }
                    Debug.Assert(vertexIndex >= 0);
                    
                    var dir = mPolygonVertices[(vertexIndex + 1) % n] - mPolygonVertices[vertexIndex];
                    float distance = Random.Range(0.0f, 1.0f);
                    var point = mPolygonVertices[vertexIndex] + distance * dir;
                    dir.Normalize();
                    var perp = new Vector3(dir.z, 0, -dir.x);
                    float halfEdgeSize = mEdgeSize * 0.5f;
                    float perpDistance = Random.Range(-halfEdgeSize, halfEdgeSize);
                    point += perp * perpDistance;

                    if (IsValid(point, points, mMinDistance))
                    {
                        points.Add(point);
                        break;
                    }
                }
            }

            return Utils.ConvertToVector2List(points);
        }

        List<Vector3> mPolygonVertices = new List<Vector3>();
        bool mStartSetPoint = true;
        Vector3[] mDiplayPolygonVertices;
    }
}


#endif