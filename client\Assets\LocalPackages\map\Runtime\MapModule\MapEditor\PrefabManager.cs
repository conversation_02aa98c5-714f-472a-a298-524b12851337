﻿ 



 
 



#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    [System.Flags]
    public enum PrefabGroupDisplayFlag
    {
        None = 0,
        ShowRegularTiles = 1,
        IsGridModelLayer = 2,
        ShowDecoration = 4,
        ShowColor = 8,
        ShowAddFolderWithLODConstrain = 16,
        AddOnlyOnePrefab = 32,
        ShowAddFolderWithoutLODConstrain = 64,
        CanRemovePrefab = 128,
        ShowGridSize = 256,
    }

    public delegate void OnSelectPrefab(GameObject prefab);
    public delegate void OnAddPrefab(int groupIndex, GameObject prefab);
    public delegate void OnSetPrefab(int groupIndex, int index, GameObject prefab);
    public delegate void OnSetSubGroupPrefab(int groupIndex, int index, int subTypeIndex, string prefabPath);
    public delegate void OnRemovePrefab(int groupIndex, int index, string prefabPath);
    public delegate void OnFillRandomPrefab(List<GameObject> prefabs, TileFillMode mode);
    public delegate void OnSetPrefabFixedIndex(int groupIndex, int fixedIndex);
    public delegate void OnSetPrefabSubGroupPrefabFixedIndex(int groupIndex, int prefabIndex, int subgroupPrefabFixedIndex);
    public delegate void OnChangeSubGroupPrefabCount(int groupIndex, int prefabIndex, int count);
    public delegate void OnChangeSelectedPrefabGroup(int oldGroupIndex, int newGroupIndex);
    public delegate void OnPrefabGroupGridSizeChange(int groupIndex, float gridSize);

    public class PrefabManager
    {
        public event OnSelectPrefab selectPrefabEvent;
        public event OnAddPrefab addPrefabEvent;
        public event OnSetPrefab setPrefabEvent;
        public event OnSetSubGroupPrefab setSubGroupPrefabEvent;
        public event OnRemovePrefab removePrefabEvent;
        public event OnFillRandomPrefab fillRandomPrefabEvent;
        public event OnSetPrefabFixedIndex setPrefabFixedIndexEvent;
        public event OnSetPrefabSubGroupPrefabFixedIndex setPrefabSubGroupPrefabFixedIndexEvent;
        public event OnChangeSubGroupPrefabCount changeSubGroupPrefabCountEvent;
        public event OnChangeSelectedPrefabGroup changeSelectedPrefabGroupEvent;
        public event OnPrefabGroupGridSizeChange changePrefabGroupGridSizeEvent;

        List<PrefabGroup> mGroups = new List<PrefabGroup>();
        string[] mGroupNames = new string[0];
        int mSelectedGroup = -1;
        int mNextGroupID = -1;
        bool mAddPrefabSet;
        bool mAllowDuplication;
        bool mCheckTransformIdentity;
        bool mEditSubgroup;
        bool mOnlyOnePrefabGroup;
        System.Action<PrefabGroup.Item, int, int> mCustomEditFunc;
        System.Func<object> mCreateCustomParameterFunc;

        public PrefabManager(bool allowDuplication, bool addPrefabSet, bool editSubgroup, bool onlyOnePrefabGroup, bool checkTransformIdentity, System.Action<PrefabGroup.Item, int, int> customEditFunc, System.Func<object> createCustomParameterFunc)
        {
            mAllowDuplication = allowDuplication;
            mAddPrefabSet = addPrefabSet;
            mEditSubgroup = editSubgroup;
            mOnlyOnePrefabGroup = onlyOnePrefabGroup;
            mCheckTransformIdentity = checkTransformIdentity;
            mCustomEditFunc = customEditFunc;
            mCreateCustomParameterFunc = createCustomParameterFunc;
        }

        public void Clear()
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                mGroups[i].OnDestroy();
            }
            mGroups.Clear();
            mGroupNames = new string[0];
            mSelectedGroup = -1;
        }

        public void Iterate(System.Func<PrefabGroup.Item, bool> func)
        {
            foreach (var g in mGroups)
            {
                int n = g.count;
                for (int i = 0; i < n; ++i)
                {
                    var item = g.GetItem(i);
                    bool quit = func(item);
                    if (quit)
                    {
                        return;
                    }
                }
            }
        }

        public PrefabGroup FindGroup(Color32 color)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].color.r == color.r &&
                    mGroups[i].color.g == color.g &&
                    mGroups[i].color.b == color.b)
                {
                    return mGroups[i];
                }
            }
            return null;
        }

        public PrefabGroup AddGroup(int groupID, string name, Color32 color, float gridSize)
        {
            return AddGroup(groupID, name, color, mAddPrefabSet, mAllowDuplication, gridSize);
        }

        public PrefabGroup AddGroup(int groupID, string name, Color32 color, bool addPrefabSet, bool allowDuplication, float gridSize)
        {
            if (FindGroup(name) == null)
            {
                if (groupID < 0)
                {
                    groupID = ++mNextGroupID;
                }
                var group = new PrefabGroup(groupID, allowDuplication, this, name, gridSize, addPrefabSet, mEditSubgroup, new string[0], OnSelectPrefab, OnAddPrefab, OnRemovePrefab, OnFillRandomTiles, OnSetPrefabFixedIndex, OnSetPrefab, OnSetSubGroupPrefab, OnSetPrefabSubGroupPrefabFixedIndex, OnChangeSubGroupPrefabCount, OnChangePrefabGroupGridSize);
                group.color = color;
                mGroups.Add(group);
                CreateNames();
                SetSelection(mGroups.Count - 1);
                return group;
            }
            return null;
        }

        public void RemoveGroup(int index)
        {
            if (index >= 0 && index < mGroups.Count)
            {
                mGroups[index].OnDestroy();
                mGroups.RemoveAt(index);
                CreateNames();

                if (index >= mGroups.Count)
                {
                    --index;
                }
                int newSelected = index;
                if (mGroups.Count == 0)
                {
                    newSelected = -1;
                }
                SetSelection(newSelected);
            }
        }

        void RenameGroup(int index)
        {
            var inputDialog = EditorWindow.GetWindow<InputDialog>("Input Group Name");
            inputDialog.minSize = new Vector2(200, 100);
            inputDialog.maxSize = new Vector2(300, 100);
            var position = inputDialog.position;
            position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
            inputDialog.position = position;

            var group = mGroups[index];
            var items = new List<InputDialog.Item> {
                new InputDialog.StringItem("Group Name", "", group.name),
            };
            inputDialog.Show(items, OnRenameGroup);
        }

        bool OnRenameGroup(List<InputDialog.Item> groupName)
        {
            var selectedGroup = GetSelectedGroup();
            if (selectedGroup != null)
            {
                selectedGroup.name = (groupName[0] as InputDialog.StringItem).text;
                CreateNames();
            }

            return true;
        }

        public PrefabGroup GetGroupByIndex(int index)
        {
            if (index >= 0 && index < mGroups.Count)
            {
                return mGroups[index];
            }
            return null;
        }

        public PrefabGroup GetGroupByID(int id)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].groupID == id)
                {
                    return mGroups[i];
                }
            }
            return null;
        }

        public int GetGroupIndex(PrefabGroup group)
        {
            return mGroups.IndexOf(group);
        }

        PrefabGroup GetSelectedGroup()
        {
            if (mSelectedGroup >= 0 && mSelectedGroup < mGroups.Count)
            {
                return mGroups[mSelectedGroup];
            }
            return null;
        }

        PrefabGroup FindGroup(string name)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                if (mGroups[i].name == name)
                {
                    return mGroups[i];
                }
            }
            return null;
        }

        public string GetRandomPrefabInCurrentGroup()
        {
            var group = GetSelectedGroup();
            if (group != null)
            {
                return group.GetRandomPrefabPath();
            }
            return "";
        }

        public void RemoveInvalidPrefabs()
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                mGroups[i].RemoveInvalidPrefabs();
            }
        }

        public void Draw(PrefabGroupDisplayFlag displayFlags)
        {
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Prefab Groups");
            var selectedGroup = EditorGUILayout.Popup(mSelectedGroup, names);
            EditorGUILayout.EndHorizontal();
            if (selectedGroup != mSelectedGroup)
            {
                SetSelection(selectedGroup);
            }
            if (GUILayout.Button("Add"))
            {
                if (mGroups.Count == 0 || mOnlyOnePrefabGroup == false)
                {
                    var window = EditorWindow.GetWindow<InputDialog>("Input group name");
                    List<InputDialog.Item> items = null;
                    if (displayFlags.HasFlag(PrefabGroupDisplayFlag.ShowRegularTiles))
                    {
                        items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Group Name", "group名称", "New Group"),
                        new InputDialog.BoolItem("Use Regular Tiles", "是否使用有规则的1-15号tile,不勾选则使用无规则的tile", mAddPrefabSet),
                        };
                    }
                    else {
                        items = new List<InputDialog.Item> {
                        new InputDialog.StringItem("Group Name", "group名称", "New Group"),
                        };
                    }
                    window.Show(items, OnClickAdd);
                    window.minSize = new Vector2(200, 100);
                    window.maxSize = new Vector2(300, 100);
                    var position = window.position;
                    position.center = new Rect(0f, 0f, Screen.currentResolution.width, Screen.currentResolution.height).center;
                    window.position = position;
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Can only add 1 group", "OK");
                }
            }
            if (GUILayout.Button("Remove"))
            {
                if (EditorUtility.DisplayDialog("Remove Prefab Group", "Are you sure ?", "Yes", "No"))
                {
                    RemoveGroup(mSelectedGroup);
                }
            }
            if (GUILayout.Button("Rename"))
            {
                RenameGroup(mSelectedGroup);
            }
            EditorGUILayout.EndHorizontal();

            if (mSelectedGroup >= 0)
            {
                mGroups[mSelectedGroup].Draw(displayFlags);
            }
            EditorGUILayout.EndVertical();
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            string text = (parameters[0] as InputDialog.StringItem).text;
            for (int i = 0; i < mGroupNames.Length; ++i)
            {
                if (text == mGroupNames[i])
                {
                    return false;
                }
            }

            bool addPrefabSet = false;
            if (parameters.Count > 1)
            {
                addPrefabSet = (parameters[1] as InputDialog.BoolItem).value;
            }
            AddGroup(-1, text, Color.white, addPrefabSet, mAllowDuplication, 0);
            return true;
        }

        void OnSelectPrefab(GameObject prefab)
        {
            if (selectPrefabEvent != null)
            {
                selectPrefabEvent(prefab);
            }
        }

        void OnAddPrefab(int groupIndex, GameObject prefab)
        {
            if (addPrefabEvent != null)
            {
                addPrefabEvent(groupIndex, prefab);
            }
        }

        void OnSetPrefab(int groupIndex, int index, GameObject prefab)
        {
            if (setPrefabEvent != null)
            {
                setPrefabEvent(groupIndex, index, prefab);
            }
        }

        void OnSetSubGroupPrefab(int groupIndex, int index, int subTypeIndex, string prefabPath)
        {
            if (setSubGroupPrefabEvent != null)
            {
                setSubGroupPrefabEvent(groupIndex, index, subTypeIndex, prefabPath);
            }
        }

        void OnFillRandomTiles(List<GameObject> prefabs, TileFillMode mode)
        {
            if (fillRandomPrefabEvent != null)
            {
                fillRandomPrefabEvent(prefabs, mode);
            }
        }

        void OnSetPrefabFixedIndex(int groupIndex, int fixedIndex)
        {
            if (setPrefabFixedIndexEvent != null)
            {
                setPrefabFixedIndexEvent(groupIndex, fixedIndex);
            }
        }

        void OnSetPrefabSubGroupPrefabFixedIndex(int groupIndex, int prefabIndex, int subgroupPrefabFixedIndex)
        {
            if (setPrefabSubGroupPrefabFixedIndexEvent != null)
            {
                setPrefabSubGroupPrefabFixedIndexEvent(groupIndex, prefabIndex, subgroupPrefabFixedIndex);
            }
        }

        void OnChangeSubGroupPrefabCount(int groupIndex, int prefabIndex, int count)
        {
            if (changeSubGroupPrefabCountEvent != null)
            {
                changeSubGroupPrefabCountEvent(groupIndex, prefabIndex, count);
            }
        }

        void OnChangePrefabGroupGridSize(int groupIndex, float gridSize)
        {
            if (changePrefabGroupGridSizeEvent != null)
            {
                changePrefabGroupGridSizeEvent(groupIndex, gridSize);
            }
        }

        void OnRemovePrefab(int groupIndex, int index, string prefabPath)
        {
            if (removePrefabEvent != null)
            {
                removePrefabEvent(groupIndex, index, prefabPath);
            }
        }

        void SetSelection(int index)
        {
            int old = mSelectedGroup;
            var selectedGroup = GetSelectedGroup();
            if (selectedGroup != null)
            {
                selectedGroup.OnDeactivate();
            }
            mSelectedGroup = index;
            selectedGroup = GetSelectedGroup();
            if (selectedGroup != null)
            {
                selectedGroup.OnActivate();
            }

            if (changeSelectedPrefabGroupEvent != null)
            {
                changeSelectedPrefabGroupEvent(old, index);
            }
        }

        void CreateNames()
        {
            if (mGroupNames.Length != mGroups.Count)
            {
                mGroupNames = new string[mGroups.Count];
            }

            for (int i = 0; i < mGroups.Count; ++i)
            {
                mGroupNames[i] = mGroups[i].name;
            }
        }

        public string FindPrefabPath(string prefabName)
        {
            for (int i = 0; i < mGroups.Count; ++i)
            {
                string path = mGroups[i].FindPrefabPath(prefabName);
                if (!string.IsNullOrEmpty(path))
                {
                    return path;
                }
            }
            return null;
        }

        public string[] names { get { return mGroupNames; } }
        public GameObject selectedPrefab
        {
            get
            {
                var group = GetSelectedGroup();
                if (group != null)
                {
                    return group.GetSelectedPrefab();
                }
                return null;
            }
        }
        public object selectedCustomParameter
        {
            get
            {
                var group = GetSelectedGroup();
                if (group != null)
                {
                    return group.GetSelectedCustomParameter();
                }
                return null;
            }
        }
        public int nextItemID { 
            get
            {
                int maxID = 0;
                for (int i = 0; i < mGroups.Count; ++i)
                {
                    var groupMaxID = mGroups[i].GetMaxItemID();
                    if (groupMaxID > maxID)
                    {
                        maxID = groupMaxID;
                    }
                }
                return maxID + 1;
            } 
        }
        public int nextGroupID { get { return mNextGroupID; } set { mNextGroupID = value; } }
        public int groupCount { get { return mGroups.Count; } }
        public int selectedGroupIndex { get { return mSelectedGroup; } }
        public bool checkTransformIdentity { get { return mCheckTransformIdentity; } }
        public PrefabGroup selectedGroup
        {
            get
            {
                if (mSelectedGroup >= 0 && mSelectedGroup < mGroups.Count)
                {
                    return mGroups[mSelectedGroup];
                }
                return null;
            }
        }
        public System.Action<PrefabGroup.Item, int, int> customEditFunc { get { return mCustomEditFunc; } set { mCustomEditFunc = value; } }
        public System.Func<object> createCustomParameterFunc { get { return mCreateCustomParameterFunc; } set { mCreateCustomParameterFunc = value; } }
    }
}


#endif