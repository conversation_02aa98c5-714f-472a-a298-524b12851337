%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Flow
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity Asset
    Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=17500\n368;498;1477;895;2968.752;895.1555;2.383106;True;False\nNode;AmplifyShaderEditor.CommentaryNode;47;-1168,448;Inherit;False;527;247;Linear
    Blend;3;32;30;31;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.FunctionInput;18;-1712,80;Inherit;False;Flow
    Direction;2;2;False;1;0;FLOAT2;0,1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;41;-1472,80;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.NegateNode;46;-1872,80;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;42;-1728,176;Float;False;Constant;_Rescalevectors;Rescale
    vectors;3;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleTimeNode;22;-1792,416;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;27;-1440,416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;28;-1584,528;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;24;-1984,416;Inherit;False;Flow
    Speed;1;4;False;1;0;FLOAT;0.2;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;26;-1776,624;Float;False;Constant;_Float0;Float
    0;1;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;17;-1488,272;Inherit;False;Flow
    Strength;2;3;False;1;0;FLOAT2;1,1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;12;-896,224;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;14;-1184,256;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;2;-1696,-160;Inherit;False;UVs;2;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexColorNode;45;-2288,80;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ComponentMaskNode;44;-2096,80;Inherit;False;True;True;False;False;1;0;COLOR;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;43;-1920,-160;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleDivideOpNode;37;-1472,-48;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;49;-544,384;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;3;0;Create;True;0;0;False;0;-1;None;None;True;0;False;bump;Auto;True;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;8;-544,192;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;1;0;Create;True;0;0;False;0;-1;e28dc97a9541e3642a48c0e3886688c5;e28dc97a9541e3642a48c0e3886688c5;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;9;144,112;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;48;-544,-64;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;2;0;Create;True;0;0;False;0;-1;None;None;True;0;False;bump;Auto;True;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;30;-1120,496;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;32;-800,560;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;7;-544,-256;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;1;0;Create;True;0;0;False;0;-1;e28dc97a9541e3642a48c0e3886688c5;e28dc97a9541e3642a48c0e3886688c5;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.AbsOpNode;31;-944,496;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ComponentMaskNode;4;-1200,-16;Inherit;False;True;True;True;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;13;-1184,80;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;38;-1808,-16;Float;False;Property;_Size;Size;1;0;Create;True;0;0;False;0;1;0;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FractNode;29;-1440,528;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;11;-896,-16;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;52;0,528;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;5;-1008,-256;Inherit;False;Tex;9;0;False;1;0;SAMPLER2D;0,0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.TexturePropertyNode;6;-1248,-256;Float;True;Property;_Tex;Tex;0;0;Create;True;0;0;False;0;e28dc97a9541e3642a48c0e3886688c5;e28dc97a9541e3642a48c0e3886688c5;False;white;Auto;Texture2D;-1;0;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.FunctionSwitch;50;-160,16;Inherit;False;Is
    Normal;True;1;2;-1;In 0;In 1;Object;-1;9;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;51;-160,192;Inherit;False;Is
    Normal;True;1;2;-1;In 0;In 1;Instance;50;9;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;320,112;Inherit;True;True;-1;Output;0;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;18;0;46;0\nWireConnection;41;0;18;0\nWireConnection;41;1;42;0\nWireConnection;46;0;44;0\nWireConnection;22;0;24;0\nWireConnection;27;0;22;0\nWireConnection;28;0;22;0\nWireConnection;28;1;26;0\nWireConnection;12;0;4;0\nWireConnection;12;1;14;0\nWireConnection;14;0;41;0\nWireConnection;14;1;17;0\nWireConnection;14;2;29;0\nWireConnection;2;0;43;0\nWireConnection;44;0;45;0\nWireConnection;37;0;2;0\nWireConnection;37;1;38;0\nWireConnection;49;0;5;0\nWireConnection;49;1;12;0\nWireConnection;8;0;5;0\nWireConnection;8;1;12;0\nWireConnection;9;0;50;0\nWireConnection;9;1;51;0\nWireConnection;9;2;52;0\nWireConnection;48;0;5;0\nWireConnection;48;1;11;0\nWireConnection;30;0;27;0\nWireConnection;30;1;26;0\nWireConnection;32;0;31;0\nWireConnection;32;1;26;0\nWireConnection;7;0;5;0\nWireConnection;7;1;11;0\nWireConnection;31;0;30;0\nWireConnection;4;0;37;0\nWireConnection;13;0;41;0\nWireConnection;13;1;17;0\nWireConnection;13;2;27;0\nWireConnection;29;0;28;0\nWireConnection;11;0;4;0\nWireConnection;11;1;13;0\nWireConnection;52;0;32;0\nWireConnection;5;0;6;0\nWireConnection;50;0;7;0\nWireConnection;50;1;48;0\nWireConnection;51;0;8;0\nWireConnection;51;1;49;0\nWireConnection;0;0;9;0\nASEEND*/\n//CHKSM=E084CD7EC3DACA44AFB695EB557ABBE5C34C5A63"
  m_functionName: 
  m_description: Creates a flow effect which can be given from a flow map.
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 9
  m_customNodeCategory: My noise category
  m_previewPosition: 0
  m_hidden: 0
