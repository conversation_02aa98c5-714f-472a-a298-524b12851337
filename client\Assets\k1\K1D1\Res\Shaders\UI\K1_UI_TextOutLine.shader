﻿Shader "K1/UI/TextOutLine"
{
    Properties
    {
		_MainTex("Main Texture", 2D) = "white"{}
		_OutLineColor("Out Line Color", Color) = (1, 1, 1, 0)
		_OutLineWidth("Out Line Pixel", Range(0.0, 20.0)) = 1.0
    }
    SubShader
    {
		Tags
		{
			"Queue"="Transparent"
			"IgnoreProjector"="true"
			"RenderType"="Transparent"
		}

		Cull Off ZWrite Off ZTest Always Lighting Off
		blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
				float4 tangent : TANGENT;
				fixed4 color : COLOR;
            };

            struct v2f
            {
				float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
				float4 bounding : TEXCOORD1;
				fixed4 color : COLOR;
            };

			sampler2D _MainTex;
			half4 _MainTex_TexelSize;
			fixed4 _OutLineColor;
			half _OutLineWidth;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
				o.color = v.color;
				o.bounding = half4(v.tangent.xy - floor(_OutLineWidth) / _MainTex_TexelSize.zw, v.tangent.zw + floor(_OutLineWidth) / _MainTex_TexelSize.zw);
				o.bounding = v.tangent;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
				fixed2 fOutLine = floor(_OutLineWidth) / _MainTex_TexelSize.zw;
				fixed4 outlineLeft = tex2D(_MainTex, i.uv - half2(fOutLine.x, 0.0));
				outlineLeft.a = step(i.bounding.x, i.uv.x - fOutLine.x) * outlineLeft.a;

				fixed4 outlineRight = tex2D(_MainTex, i.uv + half2(fOutLine.x, 0.0));
				outlineRight.a = step(i.uv.x + fOutLine.x, i.bounding.z) * outlineRight.a;

				fixed4 outlineUpper = tex2D(_MainTex, i.uv + half2(0.0, fOutLine.y));
				outlineUpper.a = step(i.uv.y + fOutLine.y, i.bounding.w) * outlineUpper.a;

				fixed4 outlineButtom = tex2D(_MainTex, i.uv - half2(0.0, fOutLine.y));
				outlineButtom.a = step(i.bounding.y, i.uv.y - fOutLine.y) * outlineButtom.a;

				fixed4 origin = (tex2D(_MainTex, i.uv) + fixed4(i.color.rgb, 0.0));
				fixed4 outline = outlineLeft + outlineRight + outlineUpper + outlineButtom;
				fixed4 outlineRes = (1.0 - origin.a) * (outline + _OutLineColor);
                return outlineRes + fixed4(origin.rgb * origin.a, origin.a);
            }
            ENDCG
        }
    }
}
