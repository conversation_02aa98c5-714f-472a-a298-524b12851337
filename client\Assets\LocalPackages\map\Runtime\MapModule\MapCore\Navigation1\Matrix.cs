﻿ 



 
 


using System.Collections.Generic;
using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    // Matrix 邻接表
    public class Matrix
    {
        public struct Node
        {
            public Node(int id, uint weight)
            {
                this.id = id;
                this.weight = weight;
            }
            public int id;      // edge id
            public uint weight; // 边与边的距离(初始时是边的中心点之间的距离)
        }

        // 下标为每个adjacent edge ID，值表示与其相互连接的adjacent edge id
        List<Node>[] mNodes;

        // CreateMatrix 创建矩阵
        public void CreateMatrix(Mesh mesh)
        {
            CreateEdgeMatrix(mesh);
            //mesh.CreateTriangleSearchGrid();
        }

        // 创建基于边的矩阵
        void CreateEdgeMatrix(Mesh mesh)
        {
            int adjEdgeCount = mesh.edges.Length;
            mNodes = new List<Node>[adjEdgeCount];
            for (int i = 0; i < adjEdgeCount; ++i)
            {
                mNodes[i] = new List<Node>();
            }
            // +2是为了给开始点和结束点留下位置
            int triangleCount = mesh.triangles.Length;
            for (int t = 0; t < triangleCount; t++)
            {
                var triangle = mesh.triangles[t];
                // 取得三角形的边
                // 取得每条边中心点
                var midCoords = triangle.GetEdgeMidCoords();
                // 遍历边，将符合条件的边加入到矩阵中
                for (int i = 0; i < 3; i++)
                {
                    // 判断三角形内的边是否为邻接边
                    var edgeID = triangle.GetEdgeID(i);
                    // 不存在或者不是邻接边则不加入矩阵中
                    if (!mesh.IsAdjacencyEdge(edgeID))
                    {
                        continue;
                    }
                    // 三角形的另外两条边
                    var others = new int[] { (i + 1) % 3, (i + 2) % 3 };
                    foreach (var other in others)
                    {
                        var otherID = triangle.GetEdgeID(other);
                        // 判断边是否为邻接边，如果是则加入矩阵中
                        if (mesh.IsAdjacencyEdge(otherID))
                        {
                            var wt = new Node(otherID, (uint)GeoUtils.CalDstCoordToCoord(midCoords[i], midCoords[other]));
                            mNodes[edgeID].Add(wt);
                        }
                    }
                }
            }
        }

        // 加入新的节点时，调整矩阵
        public void AdjustEdgeMartix(Mesh mesh, int polygonID, int newEdgeID, Coord newEdgeWtCoord)
        {
            Triangle polygon = mesh.triangles[polygonID];
            // 遍历边，加入到newEdgeIDx的邻接表里
            for (int i = 0; i < 3; ++i)
            {
                // 只加入邻接边
                var edgeID = polygon.GetEdgeID(i);
                if (mesh.IsAdjacencyEdge(edgeID))
                {
                    var edge = mesh.edges[edgeID];
                    var edgeMidCoord = GeoUtils.CalMidCoord(edge.Vertice0.Coord, edge.Vertice1.Coord);

                    var wt = new Node(edgeID, (uint)GeoUtils.CalDstCoordToCoord(newEdgeWtCoord, edgeMidCoord));
                    mNodes[newEdgeID].Add(wt);
                }
            }

            // 遍历边，将三角形边之间的邻接关系移除，同时将当前节点的邻接关系加入
            for (int i = 0; i < 3; i++)
            {
                var edgeID = polygon.GetEdgeID(i);
                // 不存在或者不是邻接边则不加入矩阵中
                if (!mesh.IsAdjacencyEdge(edgeID))
                {
                    continue;
                }
                var connectedEdges = mNodes[edgeID];

                // 判断其他两条边是否为邻接边
                var otherIDs = new int[2];
                for (int j = 1; j <= 2; j++)
                {
                    // 因为ID从0开始递增，所以使用-1初始化，表示没有ID
                    otherIDs[j - 1] = -1;

                    var otherIndex = polygon.GetEdgeID((i + j) % 3);

                    if (mesh.IsAdjacencyEdge(otherIndex))
                    {
                        otherIDs[j - 1] = otherIndex;
                    }
                }
                // 移除
                for (int k = 0; k < connectedEdges.Count;)
                {
                    if (connectedEdges[k].id == otherIDs[0] || connectedEdges[k].id == otherIDs[1])
                    {
                        connectedEdges.RemoveAt(k);
                    }
                    else
                    {
                        k++;
                    }
                }
                var edge = mesh.edges[edgeID];

                var edgeMidCoord = GeoUtils.CalMidCoord(edge.Vertice0.Coord, edge.Vertice1.Coord);
                // 加上到当前节点的权重
                connectedEdges.Add(new Node(newEdgeID, (uint)GeoUtils.CalDstCoordToCoord(newEdgeWtCoord, edgeMidCoord)));
                mNodes[edgeID] = connectedEdges;
            }
        }

        // 撤回边权修改
        public void RevertEdgeMatrix(Mesh mesh, int polygonID)
        {
            var polygon = mesh.triangles[polygonID];

            // 遍历边，将邻接边的权重加入，同时去除起点和终点
            for (int i = 0; i < 3; i++)
            {
                var edgeID = polygon.GetEdgeID(i);
                // 不存在或者不是邻接边则不加入矩阵中
                if (!mesh.IsAdjacencyEdge(edgeID))
                {
                    continue;
                }
                var edge = mesh.edges[edgeID];
                var mid = edge.CalMidCoord();
                var wts = mNodes[edgeID];
                // 去除起点和终点
                for (int j = 0; j < wts.Count;)
                {
                    if (wts[j].id == Const.StartEdgeID || wts[j].id == Const.EndEdgeID)
                    {
                        wts.RemoveAt(j);
                    }
                    else
                    {
                        j++;
                    }
                }

                // 判断其他两条边是否为邻接边
                var otherIndexes = new List<int>();
                for (int j = 1; j <= 2; j++)
                {
                    var otherIndex = polygon.GetEdgeID((i + j) % 3);
                    if (mesh.IsAdjacencyEdge(otherIndex))
                    {
                        otherIndexes.Add(otherIndex);
                    }
                }

                // 将邻接边加入
                foreach (var otherIndex in otherIndexes)
                {
                    var otherEdge = mesh.edges[otherIndex];
                    if (otherEdge != null)
                    {
                        var otherMid = otherEdge.CalMidCoord();
                        // 权重为两边中点之间的距离
                        var dis = GeoUtils.CalDstCoordToCoord(mid, otherMid);

                        var wt = new Node(otherIndex, (uint)dis);
                        wts.Add(wt);
                    }
                }

                mNodes[edgeID] = wts;
            }
            mNodes[Const.StartEdgeID] = new List<Node>();
            mNodes[Const.EndEdgeID] = new List<Node>();
        }

        public List<Node>[] nodes { get { return mNodes; } }
    };
}