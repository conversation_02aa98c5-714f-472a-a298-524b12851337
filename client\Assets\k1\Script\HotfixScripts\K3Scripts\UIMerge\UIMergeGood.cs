﻿using Cfg;
using Common;
using cspb;
using DeepUI;
using DG.Tweening;
using Game.Config;
using Game.Data;
using TFW;
using TFW.Localization;
using TFW.UI;
using Logic;
using Render;
using Spine.Unity;
using System;
using System.Collections;
using System.Collections.Generic;
using K3.Scripts;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Sequence = DG.Tweening.Sequence;
using System.Linq;
using Public;

namespace K3
{
    [Serializable]
    public class UIMergeGoodData
    {
        public int id;
        /// <summary>
        /// 0 是道具 1 是英雄
        /// </summary>
        public int goodType;
        public bool locked;
        public long creatTamp;
        public bool max;
        public long boxCdTamp;
        /// <summary>
        /// 使用次数 或者 英雄能量值
        /// </summary>
        public int BoxTimes;
        public bool Immovable;
        /// <summary>
        /// 来源与Buff的BoxTimes
        /// </summary>
        public int BoxTimes_Buff;

        public int InfoCode;

        /// <summary>
        /// 标记：不被技能选中（用于避免技能和自动合成冲突）
        /// </summary>
        public bool UnChooseBySkill;
        public UIMergeGoodData()
        {
        }

        public UIMergeGoodData(ItemInfo info)
        {
            id = info.id;
            locked = false;
            goodType = info.Type < 0 ? 1 : 0;
            Immovable = info.Immovable;
        }

        public UIMergeGoodData(ItemInfo info, bool locked, long boxCdTamp, ItemInfoBoxData infoBoxData, int BoxTimes, int boxtimes_buff)
        {
            id = info.id;
            InfoCode = info.Code;
            this.locked = locked;
            goodType = info.Type < 0 ? 1 : 0;
            this.boxCdTamp = boxCdTamp;
            if (BoxTimes == 0 && boxCdTamp == 0 && infoBoxData != null)
            {
                this.BoxTimes = infoBoxData.BoxTimes;
            }
            else
            {
                this.BoxTimes = BoxTimes;
            }
            this.BoxTimes_Buff = boxtimes_buff;
            Immovable = info.Immovable;
        }

        public void LevelUp(ItemInfo info)
        {
            id = info.id;
            if (info.ItemBox?.BoxID > 0)
            {
                BoxTimes = info.ItemBox.BoxTimes;
            }

            Immovable = false;
        }

        public void UnLocked()
        {
            locked = false;
        }

        public void RestBox(ItemInfo info)
        {
            if (BoxTimes <= 0)
            {
                BoxTimes = info.ItemBox.BoxTimes;
            }
        }



        //public void GetBuffTime(out double totalSec, out double lastTime)
        //{
        //    totalSec = 0;
        //    lastTime = 0;

        //    if (goodType == 1)
        //    {
        //        var skillCfg = Cfg.C.CD2Skill.I(Cfg.C.CD2Hero.I(id)?.ActiveSkill ?? 0);
        //        if (skillCfg != null)
        //        {

        //            foreach (var item in skillCfg.Effect3)
        //            {
        //                if (item.C > 0)
        //                {
        //                    lastTime = item.C - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.C;

        //                    return;
        //                }
        //            }



        //            foreach (var item in skillCfg.Effect5)
        //            {
        //                if (item.C > 0)
        //                {
        //                    lastTime = item.C - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.C;

        //                    return;
        //                }
        //            }

        //            foreach (var item in skillCfg.Effect6)
        //            {
        //                if (item.E > 0)
        //                {
        //                    lastTime = item.E - K3PlayerMgr.I.GetDuration(boxCdTamp).TotalSeconds;
        //                    if (lastTime < 0)
        //                        lastTime = 0;

        //                    totalSec = item.E;

        //                    return;
        //                }
        //            }

        //        }
        //    }
        //}
    }


    public class UIMergeGood : MonoBehaviour, IBeginDragHandler, IDragHandler, IEndDragHandler,
        IPointerClickHandler, IDropHandler // ICanvasRaycastFilter,
    {
        public UIMergeGoodData mData;
        public UIMergeGrid CurGrid
        {
            get
            {
                return uIMerge.mGridDic[mPoint.ServerIndex];
            }
        }

        private Transform MoveGoodRoot;
        private bool isRaycastLocationValid = true; //默认射线不能穿透物品
        private Camera canvasCamera;
        public ItemInfo Info;
        public Point mPoint;
        public TFWImage icon, icon_collect, goodImage, icon_ImmovableGrayIcon, icon_Immovable1, icon_Immovable2;
        private UIMerge uIMerge;
        private Sequence DoTweenSeq;
        private Animator ani;
        private TFWImage Raycastimage;

        // DOTween动画管理相关
        private Tween currentMoveTween;
        private Tween currentScaleTween;

        private GameObject lockedObj;
        private GameObject type_scene, type_max, type_collect, typeRoot, type_bg;
        public bool locked;
        private TimeSpan disposInterval;
        private TimeSpan curDur;
        private SkeletonGraphic spineAni_unlock;
        private TFWImage boxCdPointer;
        private GameObject itemboxCDObj;



        private bool inited = false;
        private bool canClick;
        private SpecialBoxData.Enum_LuckyType mLuckyType;
        private TFWText lvText;


        /// <summary>
        /// 是否正在自动合成
        /// </summary>
        [HideInInspector]
        public bool autoMergeing;

        /// <summary>
        /// 合成引导参数 同步使用
        /// </summary>
        [HideInInspector]
        public int mergeGuidParam;

        public bool CanClick { get { return canClick; } }

        public Vector2 startPos, EndPos, dir_drag;
        private int collectpointCount = 32;
        private float radius = 30f;
        private float radius_min = 30f;
        private float radius_max = 80f;
        private float dir_rang;
        private float dir_rang_max = 30f;
        private float dir_rang_min = 10f;
        private float maxTimer = 1500; //毫秒
        public EventSystem _mEventSystem;
        public GraphicRaycaster gra;
        private DateTime starttime;
        private double dur;
        double dur_max = 0.15f;//--秒
        private Vector3 sellPos;
        private List<Vector2> pointList;
        private List<Vector2> curPointList = new List<Vector2>();
        private Dictionary<UIMergeGood, int> dictionary_good = new Dictionary<UIMergeGood, int>();
        private Dictionary<UIMergeGrid, int> dictionary_grid = new Dictionary<UIMergeGrid, int>();



        private GameObject mGa;

        private Sequence mMergeAniSeq = null;

        /// <summary>
        /// 清理所有DOTween动画，防止动画冲突和位置偏移
        /// </summary>
        private void KillAllTweens()
        {
            // 清理Transform上的所有动画
            transform.DOKill();

            // 清理icon上的动画
            if (icon != null && icon.gameObject != null)
            {
                icon.gameObject.transform.DOKill();
            }

            // 清理Sequence动画
            if (DoTweenSeq != null)
            {
                DoTweenSeq.Kill();
                DoTweenSeq = null;
            }

            if (mMergeAniSeq != null)
            {
                mMergeAniSeq.Kill();
                mMergeAniSeq = null;
            }

            // 清理单独的Tween引用
            if (currentMoveTween != null)
            {
                currentMoveTween.Kill();
                currentMoveTween = null;
            }

            if (currentScaleTween != null)
            {
                currentScaleTween.Kill();
                currentScaleTween = null;
            }
        }

        /// <summary>
        /// 对象销毁时清理所有动画，防止内存泄漏
        /// </summary>
        private void OnDestroy()
        {
            KillAllTweens();
        }



        private void OnDisable()
        {
            autoMergeing = false;
        }

        public void Init(UIMergeGrid grid, ItemInfo info, Transform MoveGoodRoot, UIMerge merge,
            bool locked = false, long creatTamp = 0, bool showTween = true, float delayTime = 0.2f, long boxDdTamp = 0, int boxtims = 0, int boxtimes_buff = 0, SpecialBoxData.Enum_LuckyType luckyType = SpecialBoxData.Enum_LuckyType.None)
        {
            mergeGuidParam = 0;
            uIMerge = merge;
            mGa = gameObject;
            this.Info = info;
            this.mLuckyType = luckyType;
            canClick = false;
            mData = new UIMergeGoodData(info, locked, boxDdTamp, info.ItemBox, boxtims, boxtimes_buff);
            this.locked = locked;
            this.MoveGoodRoot = MoveGoodRoot;


            GoodInGrid(grid.Point);

            ShowPhotoUseType(false);


            if (!inited)
            {
                dir_rang_max = dir_rang_max * ((float)Screen.width / 1080f);
                dir_rang_min = dir_rang_min * ((float)Screen.width / 1080f);
                radius_min = radius_min * ((float)Screen.width / 1080f);
                radius_max = radius_max * ((float)Screen.width / 1080f);
                radius = radius * ((float)Screen.width / 1080f);
                pointList = GetPoint(radius, collectpointCount);

                _mEventSystem = GameObject.FindObjectOfType<EventSystem>();
                gra = merge.clickRoot.GetComponent<GraphicRaycaster>();
                ani = transform.GetComponent<Animator>();
                Raycastimage = transform.GetComponent<TFWImage>();
                canvasCamera = GameObject.Find("UICamera").GetComponent<Camera>();
                icon = transform.Find("icon").GetComponent<TFWImage>();


                icon_ImmovableGrayIcon = transform.Find("icon/Immovable").GetComponent<TFWImage>();
                icon_Immovable1 = transform.Find("Immovable1").GetComponent<TFWImage>();
                icon_Immovable2 = transform.Find("icon/Immovable/Immovable2").GetComponent<TFWImage>();
                goodImage = transform.GetComponent<TFWImage>();
                icon_collect = transform.Find("icon/collect").GetComponent<TFWImage>();
                spineAni_unlock = transform.Find("locked/spine_qipao").GetComponent<SkeletonGraphic>();
                lockedObj = transform.Find("locked").gameObject;
                //boxEffect = transform.Find("tishi").gameObject;
                type_scene = transform.Find("typeRoot/sceneIcon").gameObject;

                type_max = transform.Find("typeRoot/maxIcon").gameObject;
                type_collect = transform.Find("typeRoot/collectIcon").gameObject;
                typeRoot = transform.Find("typeRoot").gameObject;
                type_bg = transform.Find("bg").gameObject;

                boxCdPointer = transform.Find("typeRoot/collectionCD/BOXCD/CDpointer").GetComponent<TFWImage>();
                itemboxCDObj = transform.Find("typeRoot/collectionCD").gameObject;


                lvText = transform.Find("iconLv").GetComponent<TFWText>();
                lvText.gameObject.SetActive(false);

            }
            lvText.text = LocalizationMgr.Format(LocalizationMgr.Get("Skill_Info_Level"), info.Level.ToString());

            //Debug.LogError("============================InitImage====================");
            UITools.SetCommonItemIcon(icon, info.Icon, false);
            UITools.SetCommonItemIcon(icon_ImmovableGrayIcon, info.Icon, false);
            UITools.SetCommonItemIcon(icon_collect, info.Icon, false);

            ShowItemBox();

            var icon_light = transform.Find("icon_light");//补丁 ~
            if (icon_light != null)
            {
                Destroy(icon_light.gameObject);
            }

            typeRoot.SetActive(false);
            lockedObj.SetActive(locked);
            ani.SetBool("locked", locked);

            //if (spineAni_unlock != null && spineAni_unlock.AnimationState != null && spineAni_unlock.SkeletonData.Animations.Items.Length > 1)
            //{
            //    spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[1],
            //        true);
            //}

            if (locked)
            {
                disposInterval = new TimeSpan(0, 0, CSVConfig.GetItemRetentionTime());
                mData.creatTamp = creatTamp;
                if (mData.creatTamp == 0)
                {
                    mData.creatTamp = K3PlayerMgr.I.GetLocalTimestamp();
                }
            }
            else if (mData.Immovable)
            {

            }

            goodImage.raycastTarget = false;

            transform.Find("Eff_UI_tsdj_xz").gameObject.SetActive(false);

            if (showTween)
            {
                canClick = false;
                CoroutineMgr.StartCoroutine(ShowTween(delayTime));
            }
            else
            {
                transform.SetParent(CurGrid.goodRoot);
                transform.localPosition = Vector3.zero;
                transform.localScale = Vector3.one;
                canClick = true;
                goodImage.raycastTarget = true;
                typeRoot.SetActive(true);

                ShowRareItemEffect();
            }

            if (Info.Type > 0 && CSVItem.GetMaxLevelByTypeAndCode(mData.id) <= Info.Level)
            {
                if (Info.ItemBox?.BoxID > 0 || Info.id == Cfg.CfgConst.K3MergeStoreID)
                {
                    mData.max = false;
                }
                else
                {
                    mData.max = true;
                }
                type_max.SetActive(mData.max);
            }
            else
            {
                type_max.SetActive(false);
                mData.max = false;
            }


            icon_collect.gameObject.SetActive(info.Type != 1 && info.Type > 0 && info.id != Cfg.CfgConst.K3MergeStoreID);

            icon_ImmovableGrayIcon.gameObject.SetActive(mData.Immovable && info.Type > 0);
            icon_Immovable1.gameObject.SetActive(mData.Immovable && info.Type > 0);
            icon_Immovable2.gameObject.SetActive(mData.Immovable && info.Type > 0);


            if (Info.Type > 0 && K3PlayerMgr.I.AreaData.NoPoint(mPoint) && !mData.Immovable)
            {
                K3PlayerMgr.I.UnlockedId(mData.id);
            }

            inited = true;
        }


        //public void InitToMergeStore(ItemInfo info, Transform MoveGoodRoot, UIMerge merge,
        // bool locked = false, long creatTamp = 0, bool showTween = true, float delayTime = 0.2f, long boxDdTamp = 0, int boxtims = 0, int boxtimes_buff = 0)
        //{
        //    mergeGuidParam = 0;
        //    uIMerge = merge;
        //    mGa = gameObject;
        //    this.Info = info;
        //    canClick = false;
        //    mData = new UIMergeGoodData(info, locked, boxDdTamp, info.ItemBox, boxtims, boxtimes_buff);

        //    foreach (var item in K3PlayerMgr.I.GridsData)
        //    {
        //        if (item.goodData != null && item.goodData.id == Cfg.CfgConst.K3MergeStoreID)
        //        {
        //            mPoint = item.point;
        //            break;
        //        }
        //    }


        //    this.locked = locked;
        //    this.MoveGoodRoot = MoveGoodRoot;


        //    if (!inited)
        //    {
        //        dir_rang_max = dir_rang_max * ((float)Screen.width / 1080f);
        //        dir_rang_min = dir_rang_min * ((float)Screen.width / 1080f);
        //        radius_min = radius_min * ((float)Screen.width / 1080f);
        //        radius_max = radius_max * ((float)Screen.width / 1080f);
        //        radius = radius * ((float)Screen.width / 1080f);
        //        pointList = GetPoint(radius, collectpointCount);

        //        _mEventSystem = GameObject.FindObjectOfType<EventSystem>();
        //        gra = merge.clickRoot.GetComponent<GraphicRaycaster>();
        //        ani = transform.GetComponent<Animator>();
        //        Raycastimage = transform.GetComponent<TFWImage>();
        //        canvasCamera = GameObject.Find("UICamera").GetComponent<Camera>();
        //        icon = transform.Find("icon").GetComponent<TFWImage>();

        //        //icon_ImmovableGrayIcon = transform.Find("icon/Immovable").GetComponent<TFWImage>();
        //        //icon_Immovable1 = transform.Find("icon/Immovable").GetComponent<TFWImage>();
        //        //icon_Immovable2 = transform.Find("icon/Immovable").GetComponent<TFWImage>();

        //        icon_ImmovableGrayIcon = transform.Find("icon/Immovable").GetComponent<TFWImage>();
        //        icon_Immovable1 = transform.Find("Immovable1").GetComponent<TFWImage>();
        //        icon_Immovable2 = transform.Find("icon/Immovable/Immovable2").GetComponent<TFWImage>();

        //        goodImage = transform.GetComponent<TFWImage>();
        //        icon_collect = transform.Find("icon/collect").GetComponent<TFWImage>();
        //        spineAni_unlock = transform.Find("locked/spine_qipao").GetComponent<SkeletonGraphic>();
        //        lockedObj = transform.Find("locked").gameObject;

        //        type_scene = transform.Find("typeRoot/sceneIcon").gameObject;


        //        type_max = transform.Find("typeRoot/maxIcon").gameObject;
        //        type_collect = transform.Find("typeRoot/collectIcon").gameObject;
        //        typeRoot = transform.Find("typeRoot").gameObject;
        //        type_bg = transform.Find("bg").gameObject;

        //        boxCdPointer = transform.Find("typeRoot/collectionCD/BOXCD/CDpointer").GetComponent<TFWImage>();
        //        itemboxCDObj = transform.Find("typeRoot/collectionCD").gameObject;

        //        lvText = transform.Find("iconLv").GetComponent<TFWText>();
        //        lvText.gameObject.SetActive(false);
        //    }

        //    lvText.text =LocalizationMgr.Format(LocalizationMgr.Get("Skill_Info_Level"), info.Level.ToString());

        //    UITools.SetCommonItemIcon(icon, info.Icon, false);
        //    UITools.SetCommonItemIcon(icon_ImmovableGrayIcon, info.Icon, false);
        //    UITools.SetCommonItemIcon(icon_collect, info.Icon, false);

        //    //icon.rectTransform.sizeDelta = new Vector2(146, 146);

        //    ShowItemBox();

        //    var icon_light = transform.Find("icon_light");//补丁 ~
        //    if (icon_light != null)
        //    {
        //        Destroy(icon_light.gameObject);
        //    }

        //    typeRoot.SetActive(false);
        //    lockedObj.SetActive(locked);
        //    ani.SetBool("locked", locked);

        //    //if (spineAni_unlock != null && spineAni_unlock.AnimationState != null && spineAni_unlock.SkeletonData.Animations.Items.Length > 1)
        //    //{
        //    //    spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[1],
        //    //        true);
        //    //}

        //    if (locked)
        //    {
        //        disposInterval = new TimeSpan(0, 0, CSVConfig.GetItemRetentionTime());
        //        mData.creatTamp = creatTamp;
        //        if (mData.creatTamp == 0)
        //        {
        //            mData.creatTamp = K3PlayerMgr.I.GetLocalTimestamp();
        //        }
        //    }
        //    else if (mData.Immovable)
        //    {

        //    }

        //    goodImage.raycastTarget = false;

        //    if (showTween)
        //    {
        //        canClick = false;
        //        StartCoroutine(ShowTweenMerge(delayTime));
        //    }
        //    else
        //    {
        //        transform.SetParent(CurGrid.goodRoot);
        //        transform.localPosition = Vector3.zero;
        //        transform.localScale = Vector3.one;
        //        canClick = true;
        //        goodImage.raycastTarget = true;
        //        typeRoot.SetActive(true);
        //    }

        //    if (Info.Type > 0 && CSVItem.GetMaxLevelByTypeAndCode(mData.id) <= Info.Level)
        //    {
        //        if (Info.ItemBox?.BoxID > 0 || Info.id == Cfg.CfgConst.K3MergeStoreID)
        //        {
        //            mData.max = false;
        //        }
        //        else
        //        {
        //            mData.max = true;
        //        }
        //        type_max.SetActive(mData.max);
        //    }
        //    else
        //    {
        //        type_max.SetActive(false);
        //        mData.max = false;
        //    }


        //    icon_collect.gameObject.SetActive(info.Type != 1 && info.Type > 0 && info.id != Cfg.CfgConst.K3MergeStoreID);
        //    icon_ImmovableGrayIcon.gameObject.SetActive(mData.Immovable && info.Type > 0);
        //    icon_Immovable1.gameObject.SetActive(mData.Immovable && info.Type > 0);
        //    icon_Immovable2.gameObject.SetActive(mData.Immovable && info.Type > 0);

        //    if (Info.Type > 0 && K3PlayerMgr.I.AreaData.NoPoint(mPoint) && !mData.Immovable)
        //    {
        //        K3PlayerMgr.I.UnlockedId(mData.id);
        //    }

        //    inited = true;
        //}

        void Update()
        {


            if (inited)
            {
                if (locked)
                {
                    curDur = disposInterval - K3PlayerMgr.I.GetDuration(mData.creatTamp);
                    if (curDur <= TimeSpan.Zero)
                    {

                        DisPos();

                        CSPlayer.I.k3ToServerData.AddUnlockItemDisStep(mPoint.ServerIndex);


                    }
                }

            }
        }



        public string GetDeleteTime()
        {
            return curDur.ToString(@"mm\:ss");
        }
        public double GetSecondTime()
        {
            return curDur.TotalSeconds;
        }

        IEnumerator ShowTween(float delaytime)
        {
            lvText.gameObject.SetActive(true);
            transform.localScale = Vector3.zero;
            yield return new WaitForSeconds(delaytime);
            transform.localScale = Vector3.one;
            // 清理之前的动画序列
            if (DoTweenSeq != null)
            {
                DoTweenSeq.Kill();
            }

            DoTweenSeq = DOTween.Sequence();
            Vector3[] path = new Vector3[3];
            path[0] = transform.position; //起始点
            path[2] = CurGrid.goodRoot.position; //终点
            DoTweenSeq.Insert(0, transform.DOScale(1f, 0.3f));
            // DoTweenSeq.Insert(.4f,transform.DOScale(1f,0.1f));
            //DoTweenSeq.Append(transform.DOPath(bezierPath, 0.6f).SetEase(Ease.OutCubic)); //InOutSine
            DoTweenSeq.Insert(0, DOJump(transform, path[2], 1.2f, 1, 0.5f, false).SetDelay(0.2f)); //InOutSine
            DoTweenSeq.InsertCallback(0, () =>
            {
                if (mLuckyType != SpecialBoxData.Enum_LuckyType.None)
                {
                    // uIMerge.ShowAccidentEffect(transform, luckyType: mLuckyType);
                    uIMerge.ShowLuckyEffect(CurGrid.Point.ServerIndex, mLuckyType);
                }
            });
            ani.SetTrigger("creat_move");
            DoTweenSeq.OnComplete(() =>
            {
                uIMerge.ShowCreatEffect(transform);
                transform.SetParent(CurGrid.goodRoot);
                transform.localPosition = Vector3.zero;
                canClick = true;
                goodImage.raycastTarget = true;
                typeRoot.SetActive(true);
                uIMerge.CheckGuid();

                ShowRareItemEffect();
                NTimer.CountDown(0.5f, () =>
                {
                    if (lvText != null)
                        lvText.gameObject.SetActive(false);
                });

            });
        }


        IEnumerator ShowTweenMerge(float delaytime)
        {
            transform.localScale = Vector3.zero;
            yield return new WaitForSeconds(delaytime);
            transform.localScale = Vector3.one;
            // 清理之前的动画序列
            if (DoTweenSeq != null)
            {
                DoTweenSeq.Kill();
            }

            DoTweenSeq = DOTween.Sequence();
            Vector3[] path = new Vector3[3];
            path[0] = transform.position; //起始点
            path[2] = CurGrid.goodRoot.position; //终点
            DoTweenSeq.Insert(0, transform.DOScale(1f, 0.3f));
            // DoTweenSeq.Insert(.4f,transform.DOScale(1f,0.1f));
            //DoTweenSeq.Append(transform.DOPath(bezierPath, 0.6f).SetEase(Ease.OutCubic)); //InOutSine
            DoTweenSeq.Insert(0, DOJump(transform, path[2], 1.2f, 1, 0.5f, false).SetDelay(0.2f)); //InOutSine
            ani.SetTrigger("creat_move");
            DoTweenSeq.OnComplete(() =>
            {
                canClick = false;

                locked = false;

                ani.SetTrigger("disPos");

                // 清理之前的动画，防止冲突
                KillAllTweens();

                currentScaleTween = transform.DOScale(0.2f, 0.2f).OnComplete(() =>
                {
                    currentScaleTween = null; // 清理引用
                });

                DelayDisPos();
            });
        }

        public static Sequence DOJump(Transform target, Vector3 endValue, float jumpPower, int numJumps, float duration, bool snapping = false)
        {
            if (numJumps < 1) numJumps = 1;
            float startPosY = target.position.y; // Temporary fix for OnStart not being called when using Goto instead of GotoWithCallbacks
            float offsetY = -1;
            bool offsetYSet = false;

            // Separate Y Tween so we can elaborate elapsedPercentage on that instead of on the Sequence
            // (in case users add a delay or other elements to the Sequence)
            Sequence s = DOTween.Sequence();
            Tween yTween = DOTween.To(() => target.position, x => target.position = x, new Vector3(0, jumpPower, 0), duration / (numJumps * 2))
                .SetOptions(AxisConstraint.Y, snapping).SetEase(Ease.OutQuad).SetRelative()
                .SetLoops(numJumps * 2, LoopType.Yoyo)
                .OnStart(() => startPosY = target.position.y); // FIXME not called if you only use Goto (and not GotoWithCallbacks)
            s.Append(DOTween.To(() => target.position, x => target.position = x, new Vector3(endValue.x, 0, 0), duration)
                    .SetOptions(AxisConstraint.X, snapping).SetEase(Ease.Linear)
                ).Join(DOTween.To(() => target.position, x => target.position = x, new Vector3(0, 0, endValue.z), duration)
                    .SetOptions(AxisConstraint.Z, snapping).SetEase(Ease.Linear)
                ).Join(yTween)
                .SetTarget(target).SetEase(DOTween.defaultEaseType);
            yTween.OnUpdate(() =>
            {
                if (!offsetYSet)
                {
                    offsetYSet = true;
                    offsetY = endValue.y - startPosY;
                }
                Vector3 pos = target.position;
                pos.y += DOVirtual.EasedValue(0, offsetY, yTween.ElapsedPercentage(), Ease.OutQuad);
                target.position = pos;
            });
            return s;
        }


        public void AutoMerge(UIMergeGood toGood, Action callBack = null)
        {
            this.autoMergeing = true;
            toGood.autoMergeing = true;

            canClick = false;
            Raycastimage.raycastTarget = false;
            toGood.canClick = false;
            toGood.Raycastimage.raycastTarget = false;

            transform.SetParent(MoveGoodRoot);

            if (uIMerge != null)
            {
                //UIMerge.IsAutoMerging = true;
                GameObject jump_effect = Instantiate(uIMerge.GameObject.transform.Find("Root/Ui_TuoWei1").gameObject, this.transform);
                jump_effect.transform.localPosition = Vector3.zero;


                if (!icon.gameObject.GetComponent<Canvas>())
                {
                    icon.gameObject.AddComponent<Canvas>();
                }
                icon.gameObject.GetComponent<Canvas>().overrideSorting = true;
                icon.gameObject.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Layer;
                icon.gameObject.GetComponent<Canvas>().sortingOrder = 100;

                TFWImage icon_light = GameObject.Instantiate(icon, this.transform);
                icon_light.name = "icon_light";
                icon_light.transform.localPosition = Vector3.zero;
                icon_light.transform.localScale = icon.transform.localScale;
                icon_light.transform.SetAsFirstSibling();
                icon_light.gameObject.AddComponent<OutLight>();
                icon_light.GetComponent<OutLight>().effectColor = new Color(1, 1, 0.85f, 0.9f);
                icon_light.GetComponent<OutLight>().effectDistance = new Vector2(10, 10);
                Shader shader = Shader.Find("GUI/Text Shader");
                Material material = new Material(shader);
                icon_light.material = material;
                icon_light.gameObject.GetComponent<Canvas>().overrideSorting = true;
                icon_light.gameObject.GetComponent<Canvas>().sortingLayerID = CustomSortingLayer.Layer;
                icon_light.gameObject.GetComponent<Canvas>().sortingOrder = 80;


                this.mData.UnChooseBySkill = true;
                toGood.mData.UnChooseBySkill = true;

                DOJump(transform, toGood.transform.position, 1.2f, 1, 0.3f).OnComplete(() =>
                {
                    try
                    {
                        if (jump_effect.gameObject != null)
                        {
                            jump_effect.transform.SetParent(this.transform.parent);
                            Public.UIHelper.SafeDestroy(jump_effect.gameObject, 3);
                        }
                        if (icon.gameObject.GetComponent<Canvas>())
                        {
                            Destroy(icon.gameObject.GetComponent<Canvas>());
                        }
                        if (icon_light)
                        {
                            Destroy(icon_light);
                        }


                        var dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                        dragStep.src = mPoint.ServerIndex;
                        dragStep.tar = toGood.mPoint.ServerIndex;

                        toGood.canClick = true;
                        toGood.Raycastimage.raycastTarget = true;
                        toGood.CurGrid.Compound(out var exGood);

                        this.mData.UnChooseBySkill = false;
                        toGood.mData.UnChooseBySkill = false;

                        toGood.autoMergeing = false;

                        if (exGood != null)
                        {
                            dragStep.assets.Add(exGood.mPoint.ServerIndex, K3PlayerMgr.I.TOID(exGood.mData));

                            if (exGood.mergeGuidParam != 0)
                            {
                                dragStep.guideId = exGood.mergeGuidParam;
                            }
                        }
                        dragStep.actionInfo = "Compound";


                        uIMerge.ShowMergeEffect(toGood.transform);
                        if (uIMerge.curGood == this) //自动合成 刷新 当前选中的道具信息
                        {
                            uIMerge.ShowTopInfo();
                        }
                        else if (uIMerge.curGood == toGood)
                        {
                            uIMerge.ShowTopInfo(toGood);
                        }


                        Raycastimage.raycastTarget = true;
                        canClick = true;

                        DisPos(true);

                        //NTimer.CountDown(0.16f, callBack);

                        dragStep.StepRecordEnd();

                        callBack?.Invoke();
                        //UIMerge.IsAutoMerging = false;
                    }
                    catch (Exception ex)
                    {
                        callBack?.Invoke();
                        Debug.LogError(ex.ToString());

                        DisPos(true);
                    }
                });

            }
        }


        private float pointCount = 30f;

        //获取二阶贝塞尔曲线路径数组
        private Vector3[] Bezier2Path(Vector3 startPos, Vector3 controlPos, Vector3 endPos)
        {
            Vector3[] path = new Vector3[(int)pointCount];
            for (int i = 1; i <= pointCount; i++)
            {
                float t = i / pointCount;
                path[i - 1] = Bezier2(startPos, controlPos, endPos, t);
            }

            return path;
        }

        // 2阶贝塞尔曲线
        public static Vector3 Bezier2(Vector3 startPos, Vector3 controlPos, Vector3 endPos, float t)
        {
            return (1 - t) * (1 - t) * startPos + 2 * t * (1 - t) * controlPos + t * t * endPos;
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            if (!canClick)
            {
                return;
            }

            if (uIMerge.CheckMergeGrey(mData.id))
            {
                return;
            }


            // if (mData.Immovable)
            // {
            //     return;
            // }
            //鼠标点击A对象，按下鼠标时A对象响应此事件
            if (!uIMerge.CheckDoubleClick(this))
            {
                ani.SetTrigger("touch");
                uIMerge.HideMergeGuide();
                uIMerge.ShowChekBox(true, this);
                uIMerge.StopWaitShowGuideMerge();
                uIMerge.OnUpdateBubbleState();
                uIMerge.isCancelState = false;
                GameAudio.PlayAudio(5);
            }
            ClickBoxItem();
#if UNITY_EDITOR
            // uIMerge.pointRoot.position = transform.position;
#endif
        }

        private List<Vector2> GetPoint(float radius = 180, int count = 8)
        {
            List<Vector2> pointList = new List<Vector2>();

            var radians = (Math.PI / 180) * (360.0 / count); //弧度
            for (int i = 0; i < count; i++)
            {
                double x = radius * Math.Sin(radians * i);
                double y = radius * Math.Cos(radians * i);
                pointList.Add(new Vector2((float)x, (float)y));
            }
            return pointList;
        }


        public bool Draging = false;
        public void OnBeginDrag(PointerEventData eventData) //开始拖拽
        {
            if (mData.Immovable)
            {
                return;
            }
            if (uIMerge.CheckMergeGrey(mData.id))
            {
                return;
            }

            if (!uIMerge.CanDrag())
            {
                FloatTips.I.FloatMsg("auto_merge_tips_01".ToLocal());
                return; //自动合成中
            }

            NiceVibrationsCtrl.Haptic_DragItems();

            canClick = false;

            // 完整清理所有DOTween动画，防止位置偏移
            KillAllTweens();

            Draging = true;
            //isRaycastLocationValid =
            //    false; //ui穿透：置为可以穿透  【拖拽物体移动的时候鼠标下是有物体一直跟随遮挡的，如果不穿透就获取不到放置位置（OnEndDrag中判断是空格子，物体，还是无效位置）】
            ani.SetBool("move", true);
            ani.SetTrigger("moveTi");
            uIMerge.ShowChekBox(false);
            transform.SetParent(MoveGoodRoot);
            Raycastimage.raycastTarget = false;
            uIMerge.HideMergeGuide();
            uIMerge.StopWaitShowGuideMerge();
            //uIMerge.ShowMask0();
            startPos = Input.mousePosition;
            starttime = DateTime.Now;
            pointList.Clear();
            pointList = GetPoint(radius, collectpointCount);
#if UNITY_EDITOR
            // uIMerge.pointRoot.position = transform.position;
            // uIMerge.pointRoot_guanxing.position = transform.position;
            // uIMerge.pointRoot_guanxing.SetAsLastSibling();
            // uIMerge.pointRoot.SetAsLastSibling();
            // radius = radius_min;
            // for (int i = 0; i < uIMerge.pointRoot_guanxing.childCount; i++)
            // {
            //     uIMerge.pointRoot_guanxing.GetChild(i).localPosition = new Vector3(pointList[i].x, pointList[i].y, 0);
            // }
#endif
        }






        public void OnDrag(PointerEventData eventData) //拖拽过程中
        {
            //if (mData.Immovable)
            //{
            //    return;
            //}
            //if (uIMerge.CheckMergeGrey(mData.id))
            //{
            //    return;
            //}

            if (!Draging)
                return;

            //if (!uIMerge.CanDrag())
            //    return; //自动合成中 


            uIMerge.StopWaitShowGuideMerge();
            //uIMerge.ShowMask0();
            uIMerge.ShowAccidentEffect(transform, false);
            transform.position = canvasCamera.ScreenToWorldPoint(Input.mousePosition); //鼠标左键按住拖拽的时候，物体跟着鼠标移动
            transform.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y, 0);
#if UNITY_EDITOR
            // uIMerge.pointRoot.position = transform.position;
            // uIMerge.pointRoot_guanxing.position = transform.position;
#endif
            curPointList.Clear();
            dictionary_good.Clear();
            for (int i = 0; i < pointList.Count; i++)
            {
                curPointList.Add((Vector2)Input.mousePosition + pointList[i]);
            }


            for (int i = 0; i < curPointList.Count; i++)
            {
                List<RaycastResult> list = GraphicRaycaster(curPointList[i]);
                for (int j = 0; j < list.Count; j++)
                {
                    if (list[j].gameObject.tag.Equals("UIMergeGood"))
                    {
                        UIMergeGood good = list[j].gameObject.GetComponent<UIMergeGood>();
                        if (good != null && K3PlayerMgr.I.AreaData.NoPoint(new Vector2(good.mPoint.x, good.mPoint.y)))
                        {
                            if (dictionary_good.ContainsKey(good))
                            {
                                dictionary_good[good]++;
                            }
                            else
                            {
                                dictionary_good.Add(good, 1);
                            }
                        }
                    }
                }
            }

            List<KeyValuePair<UIMergeGood, int>> list_dic = new List<KeyValuePair<UIMergeGood, int>>(dictionary_good);

            list_dic.Sort(delegate (KeyValuePair<UIMergeGood, int> s1, KeyValuePair<UIMergeGood, int> s2)
            {
                return s2.Value.CompareTo(s1.Value);
            });
            dictionary_good.Clear();

            foreach (KeyValuePair<UIMergeGood, int> pair in list_dic)
            {
                if (pair.Key.GetData().id == mData.id)
                {
                    dictionary_good.Add(pair.Key, pair.Value);
                }
            }

            if (dictionary_good.Count > 0)
            {
                list_dic = new List<KeyValuePair<UIMergeGood, int>>(dictionary_good);
                UIMergeGood curGood = list_dic[0].Key;
                if (K3PlayerMgr.I.PlayerData.guiding > 0 && curGood.mData.Immovable)
                {
                    ShowExitSame();
                }
                else
                {
                    curGood.ShowEnterSame();
                }

            }
            else
            {
                ShowExitSame();
            }
        }

        public void OnDrop(PointerEventData eventData)
        {
            OnEndDrag(eventData);
        }


        public void OnEndDrag(PointerEventData eventData) //
        {
            //if (mData.Immovable)
            //{
            //    return;
            //}
            //if (uIMerge.CheckMergeGrey(mData.id))
            //{
            //    return;
            //}

            //if (!uIMerge.CanDrag())
            //    return; //自动合成中

            if (!Draging)
                return;


            Draging = false;
            canClick = true;

            ShowExitSame();
            curPointList.Clear();
            dictionary_good.Clear();
            dictionary_grid.Clear();
            uIMerge.isCancelState = false;

            dur = (DateTime.Now - starttime).TotalSeconds;

            dir_drag = (Vector2)Input.mousePosition - startPos;

            float dir = dir_drag.magnitude * (Screen.width / 1080);

            //---距离远偏移大
            dir_rang = dir_rang_max * (dir / 1080);
            radius = radius_max * (dir / 1080);
            //---时间短偏移大
            // if (dur > dur_max)
            // {
            //     dir_rang = dir_rang_min;
            //     radius = radius_min;
            // }
            // else
            // {
            //     dur = (dur_max - dur) / dur_max;
            //     dir_rang = (float)(dir_rang_max * dur);
            //     radius = (float)(radius_max * dur);
            // }

            dir_rang = dir_rang < dir_rang_min ? dir_rang_min : dir_rang;
            dir_rang = dir_rang > dir_rang_max ? dir_rang_max : dir_rang;
            radius = radius < radius_min ? radius_min : radius;
            radius = radius > radius_max ? radius_max : radius;
            pointList.Clear();
            pointList = GetPoint(radius, collectpointCount);
#if UNITY_EDITOR
            // for (int i = 0; i < uIMerge.pointRoot_guanxing.childCount; i++)
            // {
            //     uIMerge.pointRoot_guanxing.GetChild(i).localPosition = new Vector3(pointList[i].x, pointList[i].y, 0);
            // }
#endif

            EndPos = ((Vector2)Input.mousePosition - startPos).normalized * dir_rang + (Vector2)Input.mousePosition;
#if UNITY_EDITOR
            // uIMerge.pointRoot_guanxing.position = canvasCamera.ScreenToWorldPoint(new Vector3(EndPos.x,EndPos.y,0)); 
            // uIMerge.pointRoot.position = canvasCamera.ScreenToWorldPoint(Input.mousePosition); 
            // uIMerge.pointRoot.localPosition = new Vector3(uIMerge.pointRoot.localPosition.x, uIMerge.pointRoot.localPosition.y, 0);
            // uIMerge.pointRoot_guanxing.localPosition = new Vector3(uIMerge.pointRoot_guanxing.localPosition.x, uIMerge.pointRoot_guanxing.localPosition.y, 0);
#endif
            for (int i = 0; i < pointList.Count; i++)
            {
                curPointList.Add((Vector2)EndPos + pointList[i]);
            }

            for (int i = 0; i < curPointList.Count; i++)
            {
                List<RaycastResult> list = GraphicRaycaster(curPointList[i]);
                for (int j = 0; j < list.Count; j++)
                {
                    if (list[j].gameObject.tag.Equals("UIMergeGood"))
                    {
                        UIMergeGood good = list[j].gameObject.GetComponent<UIMergeGood>();
                        if (good != null && K3PlayerMgr.I.AreaData.NoPoint(new Vector2(good.mPoint.x, good.mPoint.y)) && !good.mPoint.Equals(mPoint))
                        {
                            if (dictionary_good.ContainsKey(good))
                            {
                                dictionary_good[good]++;
                            }
                            else
                            {
                                dictionary_good.Add(good, 1);
                            }
                        }
                    }
                    else if (list[j].gameObject.tag.Equals("UIMergeGrid"))
                    {
                        UIMergeGrid grid = list[j].gameObject.GetComponent<UIMergeGrid>();
                        if (grid != null && K3PlayerMgr.I.AreaData.NoPoint(new Vector2(grid.Point.x, grid.Point.y)) && grid.Point.ServerIndex >= 0 && !grid.Point.Equals(mPoint))
                        {
                            if (dictionary_grid.ContainsKey(grid))
                            {
                                dictionary_grid[grid]++;
                            }
                            else
                            {
                                dictionary_grid.Add(grid, 1);
                            }
                        }
                    }
                }
            }

            List<KeyValuePair<UIMergeGood, int>> list_dic = new List<KeyValuePair<UIMergeGood, int>>(dictionary_good);

            list_dic.Sort(delegate (KeyValuePair<UIMergeGood, int> s1, KeyValuePair<UIMergeGood, int> s2)
            {
                return s2.Value.CompareTo(s1.Value);
            });
            dictionary_good.Clear();


            foreach (KeyValuePair<UIMergeGood, int> pair in list_dic)
            {
                if (pair.Key.GetData().id == mData.id)
                {
                    dictionary_good.Add(pair.Key, pair.Value);
                }
            }

            if (dictionary_good.Count > 0)
            {
                list_dic = new List<KeyValuePair<UIMergeGood, int>>(dictionary_good);
                UIMergeGood toGood = list_dic[0].Key;
                //--判断 当前good是否能合成 (是否已解锁,是否是最大等级)
                if (toGood == this)
                {
                    ReSet();
                }
                else if (uIMerge.MergeGreyItemGuiding_1 && uIMerge.MergeGreyGridItem.Length == 3)
                {
                    if (toGood.mData.id == uIMerge.MergeGreyGridItem[1] && toGood.mData.Immovable)
                    {
                        canClick = false;
                        //--合成
                        // 清理之前的动画，防止冲突
                        KillAllTweens();

                        currentMoveTween = transform.DOMove(toGood.transform.position, 0.05f).OnComplete(() =>
                        {
                            currentMoveTween = null; // 清理引用
                            var dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                            dragStep.src = mPoint.ServerIndex;
                            dragStep.tar = toGood.mPoint.ServerIndex;

                            DisPos(true);
                            toGood.Compound(out var exGood);
                            canClick = true;
                            goodImage.raycastTarget = true;

                            uIMerge.CheckGuid();
                            uIMerge.ShowChekBox(true, toGood);

                            if (exGood != null)
                            {
                                dragStep.assets.Add(exGood.mPoint.ServerIndex, K3PlayerMgr.I.TOID(exGood.mData));

                                if (exGood.mergeGuidParam != 0)
                                {
                                    dragStep.guideId = exGood.mergeGuidParam;
                                }
                            }
                            dragStep.actionInfo = "Compound";
                            dragStep.StepRecordEnd();
                            //K3PlayerMgr.I.SaveDataLocal();
                        });
                        ani.SetTrigger("disPos");
                        GameAudio.PlayAudio(9);
                    }
                    else
                    {
                        ReSet();
                    }
                }
                else if (toGood.GetLocked() || locked || uIMerge.CreateGuiding)//创建引导中~·
                {
                    if (K3PlayerMgr.I.PlayerData.guiding > 0 && toGood.mData.Immovable)
                    {
                        Debug.Log($"guiding: {K3PlayerMgr.I.PlayerData.guiding}");
                        ReSet();
                    }
                    else
                    {
                        //--回到原位
                        var toPoint = toGood.mPoint;

                        MStep dragStep = null;
                        if (mPoint.ServerIndex != toPoint.ServerIndex)
                        {
                            dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                            dragStep.src = mPoint.ServerIndex;
                            dragStep.tar = toGood.mPoint.ServerIndex;

                            dragStep.actionInfo = "replace";
                        }

                        toGood.SetGrid(mPoint, false);
                        SetGrid(toPoint, false);

                        uIMerge.ShowChekBox(true, this);
                        uIMerge.CheckGuid();
                        uIMerge.ShowTopInfo(this);

                        dragStep?.StepRecordEnd();
                    }
                }
                else if (CSVItem.GetMaxLevelByTypeAndCode(mData.id) <= Info.Level)
                {
                    ReSet();
                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_MaxLevel"));
                }
                else
                {
                    if (!CanLevelUpByCityTech())
                    {
                        ReSet();
                    }
                    else if (K3PlayerMgr.I.PlayerData.guiding > 0 && K3PlayerMgr.I.PlayerData.guiding != 4 && toGood.mData.Immovable)
                    {
                        Debug.Log($"guiding: {K3PlayerMgr.I.PlayerData.guiding}");
                        ReSet();
                    }
                    else
                    {
                        if (MergeWindowTip(toGood))//out var curCount, out var needCount
                        {
                            var title = LocalizationMgr.GetUIString("Ui_Confirm_merge&mergeskill_title");
                            var content = LocalizationMgr.GetUIString("Ui_Confirm_merge&mergeskill");
                            UITools.OpenConfirmPop(() =>
                            {
                                CompundToGood(toGood);
                            }, ReSet, title, content, true, UIMsgBoxInitData.OpenSecondEnum.MergeGoodMergeHelpTip);
                        }
                        else
                        {
                            CompundToGood(toGood);
                        }

                    }
                }
            }
            else
            {
                List<KeyValuePair<UIMergeGrid, int>> list_dic_grid =
                    new List<KeyValuePair<UIMergeGrid, int>>(dictionary_grid);

                list_dic_grid.Sort(delegate (KeyValuePair<UIMergeGrid, int> s1, KeyValuePair<UIMergeGrid, int> s2)
                {
                    return s2.Value.CompareTo(s1.Value);
                });
                dictionary_grid.Clear();
                foreach (KeyValuePair<UIMergeGrid, int> pair in list_dic_grid)
                {
                    if (pair.Key.CanChangeItem())
                    {
                        dictionary_grid.Add(pair.Key, pair.Value);
                    }
                }

                if (dictionary_grid.Count > 0 && !uIMerge.CreateGuiding && !uIMerge.MergeGuiding) //创建引导及合并引导不可交换~
                {
                    list_dic_grid = new List<KeyValuePair<UIMergeGrid, int>>(dictionary_grid);
                    UIMergeGrid toGrid = list_dic_grid[0].Key;

                    //--(判断交换逻辑)
                    if (toGrid.Good != null)
                    {
                        if (toGrid.Good.mData.Immovable)
                        {
                            ReSet();
                        }
                        else
                        {

                            if (toGrid.Good.mData.id == Cfg.CfgConst.K3MergeStoreID && mData.id != Cfg.CfgConst.K3MergeStoreID && Info.Code <= 20)
                            {
                                if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.UnlockWarehouse, true))
                                {

                                    if (mData.locked)
                                    {
                                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Tips_WarehouseUnlockedItem"));
                                        ReSet();
                                    }
                                    else
                                    {
                                        if (CSPlayer.I.mergeStoreData.CurPrivateItems.Count >= CSPlayer.I.mergeStoreData.CurPrivateCapacity)
                                        {
                                            FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Tips_PersonalWarehouseFull"));
                                            ReSet();
                                        }
                                        else
                                        {
                                            var dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                                            dragStep.src = mPoint.ServerIndex;
                                            dragStep.tar = toGrid.Point.ServerIndex;
                                            dragStep.actionInfo = "inPrivate";


                                            CSPlayer.I.mergeStoreData.CurPrivateItems.Add(mData.id);
                                            CSPlayer.I.mergeStoreData.SetCurRedPoint(true);

                                            toGrid.Good.GetComponent<Animator>().SetTrigger("touch");
                                            DisPos();

                                            dragStep.StepRecordEnd();
                                        }
                                    }

                                }
                                else
                                {
                                    ReSet();
                                }
                            }
                            //else if (toGrid.Good.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID && mData.id != Cfg.CfgConst.K3MergeStoreID && mData.id != Cfg.CfgConst.K3MergeSoldierStoreID && Info.Code > 20)
                            //{
                            //    if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.GroundsOpenLv, true))
                            //    {
                            //        if (mData.locked)
                            //        {
                            //            ReSet();
                            //        }
                            //        else
                            //        {
                            //            if (!CSPlayer.I.mergeSoldierStoreData.CanInStore())
                            //            {
                            //                FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Tips_SoldierStorehouseFull"));
                            //                ReSet();
                            //            }
                            //            else
                            //            {
                            //                toGrid.Good.GetComponent<Animator>().SetTrigger("touch");
                            //                DisPos();

                            //                CSPlayer.I.mergeSoldierStoreData.InStore(mPoint.ServerIndex, mData.id);
                            //            }
                            //        }

                            //    }
                            //    else
                            //    {
                            //        ReSet();
                            //    }
                            //}
                            else
                            {
                                MStep dragStep = null;
                                if (mPoint.ServerIndex != toGrid.Point.ServerIndex)
                                {
                                    dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                                    dragStep.src = mPoint.ServerIndex;
                                    dragStep.tar = toGrid.Point.ServerIndex;

                                    dragStep.actionInfo = "replace";

                                }

                                var toPoint = toGrid.Point;
                                toGrid.Good.SetGrid(mPoint, false);
                                SetGrid(toPoint, false);

                                dragStep?.StepRecordEnd();
                            }
                        }
                        uIMerge.ShowChekBox(true, this);
                        uIMerge.CheckGuid();
                        uIMerge.ShowTopInfo(this);
                    }
                    else
                    {
                        MStep dragStep = null;
                        if (mPoint.ServerIndex != toGrid.Point.ServerIndex)
                        {
                            dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                            dragStep.src = mPoint.ServerIndex;
                            dragStep.tar = toGrid.Point.ServerIndex;

                            dragStep.actionInfo = "replace";

                        }

                        NiceVibrationsCtrl.Haptic_DragItems();

                        GridOutGood();

                        SetGrid(toGrid.Point);
                        uIMerge.ShowChekBox(true, this);
                        uIMerge.CheckGuid();
                        uIMerge.ShowTopInfo(this);

                        dragStep?.StepRecordEnd();
                    }
                }

                else
                {
                    //var uimerge = DeepUI.PopupManager.I.FindPopup<K3.UIMerge>();
                    //if (uimerge.mergeSoliderGuid > 0 && uimerge.mergeSoliderGuid < 100)
                    //{
                    //    list_dic_grid = new List<KeyValuePair<UIMergeGrid, int>>(dictionary_grid);
                    //    if (list_dic_grid.Count > 0)
                    //    {
                    //        UIMergeGrid toGrid = list_dic_grid[0].Key;

                    //        if (toGrid.Good.mData.id == Cfg.CfgConst.K3MergeSoldierStoreID && mData.id != Cfg.CfgConst.K3MergeStoreID && mData.id != Cfg.CfgConst.K3MergeSoldierStoreID && Info.Code > 20)
                    //        {
                    //            if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.GroundsOpenLv, true))
                    //            {
                    //                if (mData.locked)
                    //                {
                    //                    ReSet();
                    //                }
                    //                else
                    //                {
                    //                    if (!CSPlayer.I.mergeSoldierStoreData.CanInStore())
                    //                    {
                    //                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Tips_SoldierStorehouseFull"));
                    //                        ReSet();
                    //                    }
                    //                    else
                    //                    {
                    //                        toGrid.Good.GetComponent<Animator>().SetTrigger("touch");
                    //                        DisPos();

                    //                        CSPlayer.I.mergeSoldierStoreData.InStore(mPoint.ServerIndex, mData.id);
                    //                    }
                    //                }

                    //            }
                    //            else
                    //            {
                    //                ReSet();
                    //            }
                    //        }
                    //    }
                    //}
                    //else
                    //{
                    ReSet();
                    //}
                }
            }

            Raycastimage.raycastTarget = true;
            //isRaycastLocationValid = true; //ui事件穿透：置为不能穿透
            uIMerge.OnUpdateBubbleState();
            uIMerge.HideMergeGuide();
            //uIMerge.HideMask0();
            uIMerge.StopWaitShowGuideMerge();
        }

        private void CompundToGood(UIMergeGood toGood)
        {
            canClick = false;

            if (!uIMerge.autoMergeing)
            {
                NiceVibrationsCtrl.Haptic_MergeItems();
            }

            //--合成
            // 清理之前的动画，防止冲突
            KillAllTweens();

            currentMoveTween = transform.DOMove(toGood.transform.position, 0.05f).OnComplete(() =>
            {
                currentMoveTween = null; // 清理引用
                var dragStep = CSPlayer.I.k3ToServerData.AddDragMergeStep();
                dragStep.src = mPoint.ServerIndex;
                dragStep.tar = toGood.mPoint.ServerIndex;

                DisPos(true);
                toGood.Compound(out var exGood);
                canClick = true;
                goodImage.raycastTarget = true;

                uIMerge.CheckGuid();
                //uIMerge.ShowChekBox(true, toGood);


                //K3PlayerMgr.I.SaveDataLocal();
                if (exGood != null)
                {
                    dragStep.assets.Add(exGood.mPoint.ServerIndex, K3PlayerMgr.I.TOID(exGood.mData));

                    if (exGood.mergeGuidParam != 0)
                    {
                        dragStep.guideId = exGood.mergeGuidParam;
                    }
                }
                dragStep.actionInfo = "Compound";
                dragStep.StepRecordEnd();
            });
            ani.SetTrigger("disPos");
            GameAudio.PlayAudio(9);
        }

        /// <summary>
        /// 当前道具  解锁相册、城堡升级、完成任务所需Y个（总和）， 当前棋盘有X个  x-2< y 就弹窗提示
        /// </summary>
        /// <returns></returns>
        public bool MergeWindowTip(out int curX, out int needY)
        {
            curX = K3PlayerMgr.I.GetItemCount(Info.id);
            needY = K3PlayerMgr.I.needItemCount(Info.id);

            return curX - 2 < needY && needY > 0;
        }

        public bool MergeWindowTip(UIMergeGood toGood)
        {
            return showTaskUse || toGood.showTaskUse;
        }

        public bool AutoMerge()
        {
            return showTaskUse;
        }

        private List<RaycastResult> GraphicRaycaster(Vector2 pos)
        {
            var mPointerEventData = new PointerEventData(_mEventSystem);
            mPointerEventData.position = pos;
            List<RaycastResult> results = new List<RaycastResult>();

            gra.Raycast(mPointerEventData, results);
            return results;
        }

        //public bool IsRaycastLocationValid(Vector2 sp, Camera eventCamera) //UI事件穿透：如置为false即可以穿透，被图片覆盖的按钮可以被点击到
        //{
        //    return isRaycastLocationValid;
        //}

        public void DisPos(bool immediately = false)
        {
            Destroy(this);

            canClick = false;

            locked = false;

            GridOutGood();

            if (gameObject == null)
                return;

            uIMerge.ShowChekBox(false);

            if (immediately)
            {
                //if (uIMerge != null && uIMerge.MergeGoodPool != null)
                //{

                //    ReleaseObject();
                //}
                //else
                {
                    Destroy(gameObject);
                }
            }
            else
            {
                // 清理之前的动画，防止冲突
                KillAllTweens();

                currentScaleTween = gameObject.transform.DOScale(0.2f, 0.2f).OnComplete(() =>
                {
                    currentScaleTween = null; // 清理引用
                });

                DelayDisPos();
            }
        }

        //public void InPool()
        //{
        //    Destroy(this);

        //    canClick = false;

        //    locked = false;

        //    ani.SetTrigger("disPos");

        //    ReleaseObject();
        //}

        private void ReleaseObject()
        {
            //D.Error?.Log($"Release Good");
            gameObject.SetActive(false);
            uIMerge.MergeGoodPool.Release(gameObject);
        }


        public void GridOutGood()
        {
            uIMerge.mGoodDic[CurGrid.Point.ServerIndex] = null;
            CurGrid.UpdateData();
        }

        void DelayDisPos()
        {
            if (gameObject != null)
            {
                NTimer.CountDown(0.2f, () =>
                {
                    //if (uIMerge != null && uIMerge.MergeGoodPool != null)
                    //{
                    //    ReleaseObject();
                    //}
                    //else
                    //{
                    Destroy(gameObject);
                    //}
                });
            }
        }

        public void Collect()
        {
            //TODO 生成对应资源
            if (Info.Rewards?.Count > 0 && Info.Rewards[0].IsK3VM) //金币
            {
                if (Config.ConfigID.Recover_Energy == Info.Rewards[0].RewardId)
                {
                    if (!K3PlayerMgr.I.CanAddEnergy(Info.Rewards[0].RewardVal))
                    {
                        //体力超出拥有量 Tip 
                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("EnergyOverLimitTips"));
                        return;
                    }
                }

                //Logic.CSPlayer.I.k3ToServerData.useItemsToServer.Add(Info.id);

                //--使用
                K3PlayerMgr.I.AddMoney(Info.Rewards[0].RewardId, Info.Rewards[0].RewardVal, "useitem");

                K3PlayerMgr.I.ShowFlyEffect(Info.Rewards[0].RewardId, transform, Info.Rewards[0].RewardVal);

                DisPos();

                CSPlayer.I.k3ToServerData.AddUseItemStep(mPoint.ServerIndex);
                //K3PlayerMgr.I.SaveDataLocal();
            }

        }


        public async void ClickBoxItem()
        {
            if (locked || mData.Immovable)
                return;
            //生成道具
            if (Info.ItemBox?.BoxID > 0)
            {
                if (CurBoxUseCd() > 0)
                {

                    FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("Ui_tips_BoxInCD"));
                    return;
                }

                if (!K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, Info.ItemBox.CostEnery))
                {
                    // PopupManager.I.ShowDialog<UIHyposthenia>();

                    var showDataList = await UIComplementPop.GetEnergyShowData();
                    PopupManager.I.ShowPanel<UIComplementPop>(new UIComplementPopData() { popShowEnum = UIComplementPop.UIComplementPopEnum.Energy, datas = showDataList, needData = new CommonItem.CommonItemData() { Id = 11171012, Typ = AssetType.Recover, Val = 1 } });

                    return;
                }

                if (BoxCanUse() && K3PlayerMgr.I.CheckGoodFull(Info.ItemBox.BoxRewardTimes))
                {
                    if (K3PlayerMgr.I.CheckFullMoney(MoneyType.Energy, Info.ItemBox.CostEnery))
                    {
                        if (uIMerge.CreatGood(this))
                        {
                            K3PlayerMgr.I.UseMoney(MoneyType.Energy, Info.ItemBox.CostEnery, "createitem");
                        }

                        if (mData.BoxTimes <= 0 && CurBoxUseCd() <= 0)
                        {
                            if (Info.ItemBox?.BoxUseCd == 0)
                            {
                                DisPos();
                            }
                            else
                            {
                                //变为不可使用--等待CD
                                EnterItemBoxCD();
                                uIMerge.ShowChekBox(true, this);
                            }
                        }
                        else
                        {
                            uIMerge.ShowChekBox(true, this);
                        }

                        // K3.K3PlayerMgr.I.SaveDataLocal();

                        return;
                    }
                }

                return;
            }

            //if (Info.Type == -1 && mData.BoxTimes >= Cfg.C.CD2Hero.I(Info.id).MaxEnergy)
            //{
            //    var hero = Game.Data.HeroGameData.I.GetHeroByCfgId(Info.id);
            //    Game.Data.HeroData.HeroSkillData curSkillData = null;


            //    foreach (var item in hero?.HeroSkillDatas)
            //    {
            //        if (item.Value.Active)
            //        {
            //            curSkillData = item.Value;
            //            break;
            //        }
            //    }


            //    //DoSKill(curSkillData?.id ?? hero.SkillId, Info.id, mPoint.ServerIndex);

            //    return;
            //}


            if (mData.id == Cfg.CfgConst.K3MergeStoreID)
            {
                if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.UnlockWarehouse, true))
                {
                    PopupManager.I.ShowPanel<UIMergeStorehouse>();
                }
            }

            //if (mData.id == Cfg.CfgConst.K3MergeSoldierStoreID)
            //{
            //    if (LSkill.I.CanToDoByCityLevel_ConfigID((int)MetaConfig.GroundsOpenLv, true))
            //    {
            //        if (Cfg.C.CD2CityBuilding.I((int)MainCityItem.CityType.Barracks * 1000)?.ReBuilded(true) == true)
            //        {
            //            PopupManager.I.ShowPanel<UIMergeSoldierStorehouse>();
            //        }

            //    }
            //}

#if UNITY_EDITOR || INTERNAL_ENTRANCE || DEFAULT_ENV_BETA
            if (K3.UIMerge.douclickCopyGood)
            {
                uIMerge.CopyGood(this);
            }
#endif
        }


        //public static void DoSKill(int skillID, int heroID, int pointSeverIndex)
        //{
        //    Dictionary<int, int> effect2FindItems = null;
        //    var skillCfg = Cfg.C.CD2Skill.I(skillID);
        //    UIMergeSkillCompleteData skillCompleyeData = new UIMergeSkillCompleteData();

        //    if (skillCfg != null)
        //    {

        //        Action skillUp = new Action(() =>
        //        {


        //        });

        //        if (skillCfg.Effect1.Count > 0)
        //        {
        //            foreach (var item in skillCfg.Effect1)
        //            {
        //                skillCompleyeData.Rewards.Add(new CommonItem.ItemData(item));
        //            }
        //        }

        //        if (skillCfg.Effect2.Count > 0)
        //        {

        //            effect2FindItems = UIMergeHeroInfo.GetEffect2Items(skillID, out var rewards);
        //            foreach (var item in rewards)
        //            {
        //                skillCompleyeData.Rewards.Add(new CommonItem.ItemData() { Id = item.Key, Typ = "merge", Val = item.Value });
        //            }

        //            if (skillCompleyeData.Rewards.Count == 0)
        //            {
        //                FloatTips.I.FloatMsg(LocalizationMgr.Get("Ui_txt_NomergeToUpgrade"));

        //                return;
        //            }


        //            foreach (var item in effect2FindItems)
        //            {
        //                if (UIMergeHeroInfo.SkillUpWindowTip(item.Key, item.Value))
        //                {
        //                    var title = LocalizationMgr.Get("Ui_Confirm_merge&mergeskill_title");
        //                    var content = LocalizationMgr.Get("Ui_Confirm_merge&mergeskill");
        //                    UITools.OpenConfirmPop(skillUp, null, title, content, false);
        //                    return;
        //                }
        //            }

        //        }

        //        //if (skillCfg.Effect3.Count > 0)
        //        //{
        //        //    //自动合成
        //        //    var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");
        //        //    var content = LocalizationMgr.Get("HeroSkill_AutomaticSynthesisTips");

        //        //    UITools.OpenConfirmPop(skillUp, null, title, content, true, UIMsgBoxInitData.OpenSecondEnum.HeroUseSkill);
        //        //    return;
        //        //}


        //        if (skillCfg.Effect7.Count > 0)
        //        {
        //            List<UIMergeGoodData> boxes = new List<UIMergeGoodData>();

        //            int buffAddNum = 0;

        //            foreach (var item in skillCfg.Effect7)
        //            {
        //                foreach (var good in K3.K3PlayerMgr.I.GridsData)
        //                {
        //                    if (good.Value.goodData.goodType == 0 && good.Value.goodData.id == item.A)
        //                    {
        //                        boxes.Add(good.Value.goodData);

        //                        buffAddNum = item.B;
        //                    }
        //                }
        //            }


        //            if (boxes.Count == 0)
        //            {
        //                FloatTips.I.FloatMsg(LocalizationMgr.Get("HeroSkill_NoBoxTips"));
        //                return;
        //            }

        //            foreach (var item in boxes)
        //            {
        //                if (item.BoxTimes == 0)
        //                {
        //                    FloatTips.I.FloatMsg(LocalizationMgr.Get("HeroSkill_CDTips"));
        //                    return;
        //                }
        //            }

        //            foreach (var item in boxes)
        //            {
        //                if (item.BoxTimes_Buff == buffAddNum)
        //                {
        //                    FloatTips.I.FloatMsg(LocalizationMgr.Get("HeroSkill_MaximumTimesTips"));
        //                    return;
        //                }
        //            }

        //            if (boxes[0].BoxTimes_Buff > 0)
        //            {
        //                ////7.	箱子次数型：[{a,b,c}]增加a（BoxId)箱子的最大使用次数b次,箱子数量为c
        //                var title = LocalizationMgr.Get("Buying_Confirm_Tips_Title");
        //                var content = LocalizationMgr.Format("HeroSkill_UpperLimitTips", buffAddNum - boxes[0].BoxTimes_Buff);

        //                UITools.OpenConfirmPop(skillUp, null, title, content, false);
        //            }
        //            else
        //            {
        //                skillUp();
        //            }
        //            return;
        //        }

        //        skillUp();
        //    }
        //}


        public void Sell()
        {

            //TODO 生成对应资源
            //Logic.CSPlayer.I.k3ToServerData.sellItemsToServer.Add(Info.id);


            K3PlayerMgr.I.AddMoney(MoneyType.Gold, Info.Price);
            K3PlayerMgr.I.ShowFlyEffect(MoneyType.Gold, transform, Info.Price, 0.4f);

            DisPos();

            CSPlayer.I.k3ToServerData.AddSellItemStep(mPoint.ServerIndex);

            uIMerge.isCancelState = true;

            var itemSellEvent = new ItemSellEvent();

            itemSellEvent.EventKey = "ItemSellEvent";

            itemSellEvent.Properties.Add("id", Info.id);
            itemSellEvent.Properties.Add("addenergy", Info.Price);
            //K3GameEvent.I.BiLog(itemSellEvent);

            K3GameEvent.I.TaLog(itemSellEvent);

            //K3PlayerMgr.I.SaveDataLocal();
        }
        public void CancelSell()
        {
            if (uIMerge.CreatGood(Info, transform, false, CurGrid, "CancelSell") != null)
            {
                CSPlayer.I.k3ToServerData.CancelSellItemStep(mPoint.ServerIndex);
                //Logic.CSPlayer.I.k3ToServerData.sellItemsToServer.Remove(Info.id);
                K3PlayerMgr.I.UseMoney(MoneyType.Star, Info.Price, "CancelSell");
            }
            uIMerge.ShowChekBox(false);
            uIMerge.isCancelState = false;
        }
        public bool Unlock(bool useMoney)
        {
            if (locked)
            {
                if (useMoney)
                {
                    if (K3PlayerMgr.I.UseMoney(MoneyType.Diamond, Info.UnlockCost, "unlockitem"))
                    {
                        var unlockItemStep = CSPlayer.I.k3ToServerData.AddUnlockItemStep();
                        unlockItemStep.src = mPoint.ServerIndex;
                        <EMAIL>(0);


                        mData.creatTamp = 0;
                        mData.UnLocked();
                        lockedObj.SetActive(false);
                        ani.SetBool("locked", false);
                        ani.SetTrigger("unlock");
                        //spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[0],
                        //    false);
                        locked = false;
                        CurGrid.Unlock();
                        ShowItemBox();

                        unlockItemStep.StepRecordEnd();

                        try
                        {
                            GameAudio.PlayAudio(13);
                        }
                        catch
                        {
                            return true;
                        }

                        return true;
                    }
                    else
                    {
                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ui_diamond_tilte_not_enough"));
                        return false;
                    }
                }
                else
                {

                    var unlockItemStep = CSPlayer.I.k3ToServerData.AddUnlockItemStep();
                    unlockItemStep.src = mPoint.ServerIndex;
                    <EMAIL>(1);



                    mData.creatTamp = 0;
                    mData.locked = false;
                    lockedObj.SetActive(false);
                    ani.SetBool("locked", false);
                    ani.SetTrigger("unlock");
                    //spineAni_unlock.AnimationState.SetAnimation(0, spineAni_unlock.SkeletonData.Animations.Items[0],
                    //    false);
                    locked = false;
                    CurGrid.Unlock();

                    unlockItemStep.StepRecordEnd();

                    try
                    {
                        GameAudio.PlayAudio(13);
                    }
                    catch
                    {
                        return true;
                    }

                    return true;
                }
            }

            return false;
        }

        public void SetGrid(Point toPoint, bool dispose = true)
        {
            GoodInGrid(toPoint, dispose);

            transform.SetParent(CurGrid.goodRoot);

            canClick = false;

            // 清理之前的动画，防止冲突
            KillAllTweens();

            // 安全地开始新的移动动画
            currentMoveTween = transform.DOLocalMove(Vector3.zero, 0.3f).SetEase(Ease.OutSine).OnComplete(() =>
            {
                canClick = true;
                goodImage.raycastTarget = true;
                currentMoveTween = null; // 清理引用
            });
        }


        private void GoodInGrid(Point toPoint, bool dispose = true)
        {
            mPoint = toPoint;

            if (dispose && uIMerge.mGoodDic[toPoint.ServerIndex] != null && uIMerge.mGoodDic[toPoint.ServerIndex] != this)
                uIMerge.mGoodDic[toPoint.ServerIndex].DisPos(true);

            uIMerge.mGoodDic[toPoint.ServerIndex] = this;

            uIMerge.mGridDic[toPoint.ServerIndex].UpdateData();
        }

        public UIMergeGood LevelUp(int level = 1, int code = 0)
        {
            UIMergeGood good = null;

            int fromItemID = Info.id;

            Info = CSVItem.GetItemInfoByTypeAndCodeAndLevel(Info.Type, code == 0 ? Info.Code : code, Info.Level + level);

            int toItemID = Info.id;

            //Logic.CSPlayer.I.k3ToServerData.AddMergeEvent(fromItemID, toItemID);

            mData.LevelUp(Info);


            //if (Info.Code > 20)
            //{
            //    soldierLv.text = Info.Level.ToString();
            //    int soldierType = Info.Code % 20;
            //    UITools.SetCommonItemIcon(soldierIcon, LEvolution.I.GetEvolutionData(soldierType)?.ConfigData.BattleIcon);
            //}

            //if (Logic.CSPlayer.I.k3ToServerData.mergeNum.ContainsKey(Info.id))
            //{
            //    Logic.CSPlayer.I.k3ToServerData.mergeNum[Info.id] = Logic.CSPlayer.I.k3ToServerData.mergeNum[Info.id]+1;
            //}
            //else
            //{
            //    Logic.CSPlayer.I.k3ToServerData.mergeNum.Add(Info.id, 1);
            //}

            //if (!Logic.CSPlayer.I.UnlockItems.Contains(Info.id))
            //{
            //    var itemToGuid = MetaConfig.Guide_item_use;
            //    if (itemToGuid.Length % 2 == 0)
            //    {
            //        int total = itemToGuid.Length / 2;

            //        for (int i = 0; i < total; i++)
            //        {
            //            if (Info.id == itemToGuid[i])
            //            {
            //                good = uIMerge.CreatGood(
            //                    new ItemInfo(Cfg.C.CK3Item.I(itemToGuid[i * 2 + 1])), transform, true);
            //                if (good != null)
            //                {

            //                    if (K3PlayerMgr.I.PlayerData.freeAdUnlockTimes == 0)
            //                    {
            //                        //第一次解锁泡泡引导
            //                        K3GuidMgr.I.TriggerGuid(K3GuidMgr.K3GuidType.UnlockItem, 0, good.gameObject);
            //                    }

            //                    //var guidData = new UIGuidData();
            //                    //guidData.guidItems.Add(new UIGuidItem()
            //                    //{
            //                    //    UIName = "UIMerge",
            //                    //    UIItem = "",
            //                    //    slide = true,
            //                    //    UIItemGa = good.gameObject,
            //                    //    doubleClick = true,
            //                    //    d7Desc = TFW.Localization.LocalizationMgr.Get("dialogue3"),
            //                    //    delayFinger = 1f,
            //                    //    guidEventID = "12",
            //                    //    d7ObjY = 0
            //                    //});

            //                    //UIGuid.StartGuid(guidData); //体力引导 暂时关闭


            //                    good.mergeGuidParam = K3PlayerMgr.I.PlayerData.GetMergeSyncGuidParam();
            //                }
            //            }
            //        }
            //    }
            //}

            if (!mData.Immovable && Info.Type > 0)
            {
                K3PlayerMgr.I.UnlockedId(Info.id);
            }
            icon_ImmovableGrayIcon.gameObject.SetActive(mData.Immovable && Info.Type > 0);
            icon_Immovable1.gameObject.SetActive(mData.Immovable && Info.Type > 0);
            icon_Immovable2.gameObject.SetActive(mData.Immovable && Info.Type > 0);


            K3PlayerMgr.I.PlayerData.mergeData.AddMergeCount();

            //uIMerge.taskTopInfo.CheckTaskUse();
            //K3PlayerMgr.I.CheckCityUse(uIMerge.mGridDic);
            //EventMgr.FireEvent(TEventType.K3GridDataRefresh);

            //--通过配置显示目标 
            // MgrResK3.Instance.LoadSprite(icon,"K3/Atlas/Type"+goodData.Type+"Code"+goodData.Code+"/" +goodData.Icon);

            ShowItemBox();

            float scaleTime = 0.12f;

            //K3PlayerMgr.I.AreaData.AddItem(Info.id);
            // 清理之前的动画，防止冲突
            KillAllTweens();

            var seq = DOTween.Sequence();
            seq.Append(transform.DOScale(0, scaleTime));
            seq.InsertCallback(scaleTime, ChangeImage);
            seq.Append(transform.DOScale(1, scaleTime));
            seq.OnComplete(() =>
            {
                // 序列完成后清理引用
                seq = null;
            });
            seq.Play();
            //Invoke(nameof(ChangeImage), 0.15f);

            if (CSVItem.GetMaxLevelByTypeAndCode(mData.id) <= Info.Level)
            {
                if (Info.ItemBox?.BoxID > 0 || Info.id == Cfg.CfgConst.K3MergeStoreID)
                {
                    mData.max = false;
                }
                else
                {
                    mData.max = true;
                }
                type_max.SetActive(mData.max);
            }

            //Common.EventMgr.FireEvent(Common.TEventType.K3MergeItemLevelUp, Info);

            return good;
        }

        private void ShowItemBox()
        {
            if (BoxCanUse())
            {
                type_collect.SetActive(Info.ItemBox?.BoxID > 0);
                //boxEffect.SetActive(Info.ItemBox?.BoxID > 0);
                itemboxCDObj.SetActive(false);
                ani.SetBool("itembox", true);
            }
            else
            {
                type_collect.SetActive(false);
                //boxEffect.SetActive(false);
                itemboxCDObj.SetActive(true);
                ani.SetBool("itembox", false);
            }
            //if (Info.ItemBox?.BoxID > 0)
            //{
            //    ShowItemBoxCD();
            //}
            //else
            //{
            itemboxCDObj.SetActive(false);
            type_collect.SetActive(false);
            //boxEffect.SetActive(false);
            //}
        }
        private void EnterItemBoxCD()
        {
            mData.boxCdTamp = K3PlayerMgr.I.GetLocalTimestamp() + Info.ItemBox.BoxUseCd * 1000;//终点时间
            ShowItemBox();
            //ShowItemBoxCD();
            CurGrid.UpdateData();
        }

        //public bool ResetItemBoxCD(bool useMoney = false)
        //{
        //    if (useMoney)
        //    {
        //        int cost = GetCurRestCDCost();
        //        if (K3PlayerMgr.I.CheckFullMoney(MoneyType.Diamond, cost))
        //        {
        //            cost = GetCurRestCDCost();
        //            if (K3PlayerMgr.I.UseMoney(MoneyType.Diamond, cost, "RestitemCD"))
        //            {
        //                mData.boxCdTamp = 0;
        //                ShowItemBoxCD();
        //            }
        //            else
        //            {
        //                FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ui_diamond_tilte_not_enough"));
        //            }
        //            return true;
        //        }
        //        else
        //        {
        //            FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ui_diamond_tilte_not_enough"));
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        mData.boxCdTamp = 0;
        //        ShowItemBoxCD();
        //        return true;
        //    }
        //}

        public int GetCurRestCDCost()
        {
            double curcd = CurBoxUseCd();
            int cost = (int)Math.Ceiling(Info.ItemBox.GemToReset * curcd / Info.ItemBox.BoxUseCd);
            return cost;
        }

        public double CurBoxUseCd()
        {
            if (mData.boxCdTamp == 0)
            {
                return 0;
            }

            var curDd = TimeSpan.FromMilliseconds(Mathf.Max(0, mData.boxCdTamp - GameTime.Time)).TotalSeconds;
            if (curDd <= 0)
            {
                curDd = 0;
            }
            return curDd;
        }

        public bool BoxCanUse()
        {
            return Info.ItemBox?.BoxID > 0 &&
                   (Info.ItemBox.BoxUseCd == 0 || (Info.ItemBox.BoxUseCd != 0 && CurBoxUseCd() <= 0));
        }


        void ChangeImage()
        {
            //Debug.LogError("============================ChangeImage====================");
            UITools.SetCommonItemIcon(icon, Info.Icon);
            UITools.SetCommonItemIcon(icon_ImmovableGrayIcon, Info.Icon);
            UITools.SetCommonItemIcon(icon_collect, Info.Icon);
        }


        public void UnlockImmovable()
        {
            icon_ImmovableGrayIcon.gameObject.SetActive(false);
            icon_Immovable1.gameObject.SetActive(false);
            icon_Immovable2.gameObject.SetActive(false);
            Info.Immovable = false;
            mData.Immovable = false;

            CurGrid.UpdateData();
        }

        public void Compound(out UIMergeGood exGood)
        {
            CurGrid.Compound(out exGood);
            uIMerge.ShowCompoundEffect(transform, () => { uIMerge.ShowChekBox(true, this); });
            // uIMerge.ShowTopInfo(this);
        }

        public bool CanLevelUpByCityTech(bool tip = true)
        {
            var maxLevel = 0;

            //foreach (var item in Info.ItemSource)
            {
                if (GameData.I.SkillData.MTechs.TryGetValue(1, out var data))
                {
                    maxLevel += data.MergeLv;
                }
            }

            if (Info.Level < maxLevel)
            {
                return true;
            }
            else
            {
                if (tip)
                {
                    bool Unlock = false;
                    var UIMain2 = SceneManager.I.FindScene(typeof(UIMain2));
                    if (UIMain2 == null)
                    {

                    }
                    else
                    {

                        GameObject targetGa = UIMain2.GameObject.transform.Find("content/MenuRoot").gameObject;
                        if (targetGa == null)
                        {

                        }
                        else
                        {
                            if (targetGa.gameObject.activeSelf)
                            {

                                Unlock = true;
                            }

                        }
                    }

                    if (Unlock)
                    {
                        FloatTips.I.FloatMsg("City_LV_Not_match".ToLocal());
                        //PopupManager.I.ShowDialog<UIMainCityNeedUpgrade>();
                    }
                    else
                    {
                        FloatTips.I.FloatMsg(TFW.Localization.LocalizationMgr.Get("ERRCODE_unionapplylevel"));
                    }

                }
                return false;
            }
        }

        void ReSet()
        {
            transform.SetParent(CurGrid.goodRoot);
            // transform.localPosition = Vector3.zero;
            uIMerge.ShowChekBox(true, this);
            // canClick = false;

            // 清理之前的动画，防止冲突
            KillAllTweens();

            // 安全地开始重置动画
            currentMoveTween = transform.DOLocalMove(Vector3.zero, 0.1f).OnComplete(() =>
            {
                currentMoveTween = null; // 清理引用
            });
            // uIMerge.ShowTopInfo(this);
        }

        public void ShowEnterSame()
        {
            // ani.SetBool("enter", true);
            // ani.SetBool("exit", false);
            // ani.SetTrigger("enterTi");
            uIMerge.ShowSameBox(true, transform);
        }

        public void ShowExitSame()
        {
            // ani.SetBool("enter", false);
            // ani.SetBool("exit", true);
            // ani.SetTrigger("exitTi");
            uIMerge.ShowSameBox(false, null);
        }

        public ItemInfo GetInfo()
        {
            return Info;
        }

        public UIMergeGoodData GetData()
        {
            return mData;
        }

        public bool GetLocked()
        {
            return locked;
        }

        public void DoubleClick()
        {
            if (!locked && !mData.Immovable)
            {
                Collect();
            }
        }

        public void ShowGuideMerge(int targetPosX = -1, int targetPosY = -1)
        {
            // ani.SetTrigger("guideMerge");

            ShowGuideMergeAni(targetPosX, targetPosY);
        }

        private void ShowGuideMergeAni(int targetPosX = -1, int targetPosY = -1)
        {
            // 完整清理所有动画，防止冲突
            KillAllTweens();

            ani.enabled = false;
            mMergeAniSeq = DOTween.Sequence();

            var aniTime1 = 0.12f;
            mMergeAniSeq.AppendCallback(() =>
            {
                icon.gameObject.transform.DOScale(new Vector3(0.9f, 0.9f, 1), aniTime1);
            });
            mMergeAniSeq.AppendInterval(aniTime1);

            var aniTime2 = 0.5f;
            mMergeAniSeq.AppendCallback(() =>
            {
                icon.gameObject.transform.DOScale(new Vector3(1.15f, 1.15f, 1), aniTime2);
                // 像目标点移动

                var x = targetPosX - this.CurGrid.Point.x;
                var y = -(targetPosY - this.CurGrid.Point.y);
                var ratio = 20 / Math.Sqrt(x * x + y * y);

                icon.gameObject.transform.DOLocalMove(new Vector3((float)ratio * x, (float)ratio * y, 1), aniTime2);
            });
            mMergeAniSeq.AppendInterval(aniTime2).SetDelay(0.33f);

            var aniTime3 = 0.25f;
            mMergeAniSeq.AppendCallback(() =>
            {
                //icon.gameObject.transform.DOScale(new Vector3(0.95f, 0.95f, 1), aniTime3);
                // 回来
                var x = targetPosX - this.CurGrid.Point.x;
                var y = -(targetPosY - this.CurGrid.Point.y);
                var ratio = 8 / Math.Sqrt(x * x + y * y);

                icon.gameObject.transform.DOLocalMove(new Vector3((float)ratio * x, (float)ratio * y, 1), aniTime2);
            });
            mMergeAniSeq.AppendInterval(aniTime3).SetDelay(0.25f);

            var aniTime4 = 0.25f;
            mMergeAniSeq.AppendCallback(() =>
            {
                //icon.gameObject.transform.DOScale(new Vector3(1.05f, 1.05f, 1.05f), aniTime4);
                //// 像目标点移动

                var x = targetPosX - this.CurGrid.Point.x;
                var y = -(targetPosY - this.CurGrid.Point.y);
                var ratio = 20 / Math.Sqrt(x * x + y * y);

                icon.gameObject.transform.DOLocalMove(new Vector3((float)ratio * x, (float)ratio * y, 1), aniTime2);
            });
            mMergeAniSeq.AppendInterval(aniTime4);

            var aniTime5 = 0.5f;
            mMergeAniSeq.AppendCallback(() =>
            {
                icon.gameObject.transform.DOScale(Vector3.one, aniTime5);
                // 回来

                icon.gameObject.transform.DOLocalMove(new Vector3(0, 0, 1), aniTime2);
            });
            mMergeAniSeq.AppendInterval(aniTime5);

            var aniTime6 = 0.5f;
            mMergeAniSeq.AppendInterval(aniTime6);

            mMergeAniSeq.AppendCallback(() =>
            {
                ani.enabled = true;
            });
        }

        public void HidGuideMerge()
        {
            ani.SetTrigger("idel");
        }




        public bool showTaskUse;

        public void ShowPhotoUseType(bool show)
        {
            if (type_scene == null)
            {
                type_scene = transform.Find("typeRoot/sceneIcon").gameObject;
            }
            if (type_bg == null)
            {
                type_bg = transform.Find("bg").gameObject;
            }

            showTaskUse = show && !mData.locked && !mData.Immovable;

            type_scene.SetActive(showTaskUse);

            type_bg.SetActive(showTaskUse);
        }



        public void ShowMergeBeActiveSkillAni()
        {
            transform.Find("Eff_UI_DJ_fly").gameObject.SetActive(true);

            NTimer.CountDown(0.5f, () =>
            {
                transform.Find("Eff_UI_DJ_fly").gameObject.SetActive(false);
            });
        }

        public void ShowRareItemEffect()
        {
            var isRareItem = CSVItem.IsRareItem(mData.InfoCode);
            var isGray = mData.Immovable;

            transform.Find("Eff_UI_tsdj_xz").gameObject.SetActive(false && isRareItem && !isGray);
        }
    }
}
