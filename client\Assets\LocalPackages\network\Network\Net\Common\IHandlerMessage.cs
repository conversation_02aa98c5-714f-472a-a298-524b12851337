﻿#if !UNITY_WEBGL
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Net.Common
{
    public interface IHandlerMessage
    {
        void HandleInitialize(IConnection conn);
        void HandleConnected(bool v);
        /// <summary>
        /// 消息数据处理接口。
        /// 注：传入的byte[]是只读的，并且使用者不可以缓存它的引用。
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="offset"></param>
        /// <param name="len"></param>
        void Handle(byte[] message, int offset, int len);
        void HandleDisconnected();
        void HandleClose();
    }
}
#endif