﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class NPCRegionLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.NPCRegionLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            int nCols = horizontalTileCount;
            int nRows = verticalTileCount;

            writer.Write(nRows);
            writer.Write(nCols);
            writer.Write(mLayerData.tileWidth);
            writer.Write(mLayerData.tileHeight);
            writer.Write((int)mLayerData.gridType);

            var layers = layerData.layers;
            int nLayers = layers.Count;
            writer.Write(nLayers);
            for (int k = 0; k < nLayers; ++k)
            {
                var layer = layerData.GetLayer(k);
                Utils.WriteString(writer, layerData.GetLayerName(k));
                writer.Write(layer.overridenWidth);
                writer.Write(layer.overridenHeight);
                writer.Write(layer.export);
                for (int i = 0; i < layer.overridenHeight; ++i)
                {
                    for (int j = 0; j < layer.overridenWidth; ++j)
                    {
                        int tileType = layerData.GetTileBandID(k, j, i);
                        writer.Write(tileType);
                    }
                }

                //save npc region templates
                var templates = layerData.GetTemplates(k);
                writer.Write(templates.Count);
                for (int i = 0; i < templates.Count; ++i)
                {
                    writer.Write(templates[i].bandID);
                    writer.Write(templates[i].level);
                    writer.Write(templates[i].tileType);
                    Utils.WriteColor32(writer, templates[i].color);
                    Utils.WriteString(writer, templates[i].name);
                }
            }
        }
    }
}

#endif