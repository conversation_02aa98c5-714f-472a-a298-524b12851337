﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class TileBlockTerrainLayerView : MapLayerView
    {
        public TileBlockTerrainLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = layerData.map.view.reusableGameObjectPool;
            CreateLOD1GameObjects();
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            foreach (var p in mLOD0TerrainTiles)
            {
                Utils.DestroyObject(p.Value);
            }
            mLOD0TerrainTiles = null;

            foreach (var p in mLOD0TerrainTileBlockMeshies)
            {
                Utils.DestroyObject(p.Value);
            }
            mLOD0TerrainTileBlockMeshies = null;

            foreach (var p in mLOD0TerrainTileBlockGameObjects)
            {
                Utils.DestroyObject(p.Value);
            }
            mLOD0TerrainTileBlockGameObjects = null;

            for (int i = 0; i < mTileBlockGameObjectPool.Count; ++i)
            {
                Utils.DestroyObject(mTileBlockGameObjectPool[i]);
            }
            mTileBlockGameObjectPool = null;

            for (int i = 0; i < mLOD1TerrainTiles.Count; ++i)
            {
                var list = mLOD1TerrainTiles[i];
                if (list != null)
                {
                    int n1 = list.GetLength(0);
                    int n2 = list.GetLength(1);
                    for (int k = 0; k < n1; ++k)
                    {
                        for (int j = 0; j < n2; ++j)
                        {
                            Utils.DestroyObject(list[k, j]);
                        }
                    }
                }
            }
            mLOD1TerrainTiles = null;
        }

        void CreateLOD1GameObjects()
        {
            var map = layerData.map;
            var terrainLayerData = layerData as TileBlockTerrainLayerData;
            var otherLODTiles = terrainLayerData.otherLODTiles;
            int nDefaultLODs = terrainLayerData.defaultLODCount;
            int nLODs = terrainLayerData.lodConfig.lodConfigs.Length;
            mLOD1TerrainTiles = new List<GameObject[,]>(nLODs);
            //placeholder for lod0
            mLOD1TerrainTiles.Add(null);

            mCachedVisibilityChangedGameObjects = new List<GameObject>[terrainLayerData.tilePrefabPaths.Length];
            for (int i = 0; i < mCachedVisibilityChangedGameObjects.Length; ++i)
            {
                //todo,根据max tile count来计算list的大小
                mCachedVisibilityChangedGameObjects[i] = new List<GameObject>();
            }

            if (otherLODTiles != null)
            {
                for (int i = nDefaultLODs; i < nLODs; ++i)
                {
                    int r = otherLODTiles[i - nDefaultLODs].GetLength(0);
                    int c = otherLODTiles[i - nDefaultLODs].GetLength(1);
                    mLOD1TerrainTiles.Add(new GameObject[r, c]);
                }
            }
        }

        public void OnShowLOD0Tile(int x, int y)
        {
            int key = Utils.MakeInt32Key(x, y);
#if UNITY_EDITOR
            GameObject obj = null;
            mLOD0TerrainTiles.TryGetValue(key, out obj);
            if (!Object.ReferenceEquals(obj, null))
            {
                Debug.Assert(false, string.Format("Show object at lod 0 {0}_{1} failed!", x, y));
            }
#endif
            var layerData = mLayerData as TileBlockTerrainLayerData;
            int tileType;
            string prefabPath = layerData.GetTilePrefabPath(x, y, out tileType);
#if UNITY_EDITOR
            Debug.Assert(!string.IsNullOrEmpty(prefabPath));
#endif

            var newObj = GetFromCache(tileType);
            if (Object.ReferenceEquals(newObj, null))
            {
                newObj = mObjectPool.Require(prefabPath);
            }

#if UNITY_EDITOR
            Utils.HideGameObject(newObj);
#endif
            newObj.transform.position = new Vector3(x * layerData.tileWidth, 0, y * layerData.tileHeight);
            newObj.transform.SetParent(root.transform, false);
            mLOD0TerrainTiles.Add(key, newObj);
        }

        public void OnShowLOD0TileBlock(int tileType)
        {
            GameObject obj = null;
            bool found = mLOD0TerrainTileBlockGameObjects.TryGetValue(tileType, out obj);
            if (!found)
            {
                obj = CreateTileBlockObject(tileType);
                mLOD0TerrainTileBlockGameObjects.Add(tileType, obj);
            }
#if UNITY_EDITOR
            else
            {
                Debug.Assert(false);
            }
#endif
            obj.SetActive(true);
#if UNITY_EDITOR
            Utils.HideGameObject(obj);
#endif
            obj.transform.SetParent(root.transform, false);
        }

        public void OnHideLOD0TileBlock(int tileType)
        {
            GameObject obj;
            mLOD0TerrainTileBlockGameObjects.TryGetValue(tileType, out obj);
#if UNITY_EDITOR
            if (Object.ReferenceEquals(obj, null))
            {
                Debug.Assert(false, "Hide tile block failed!");
            }
#endif
            ReleaseGameObject(obj);
            mLOD0TerrainTileBlockGameObjects.Remove(tileType);
        }

        public void OnHideLOD0Tile(int x, int y)
        {
            int key = Utils.MakeInt32Key(x, y);
            GameObject obj;
            mLOD0TerrainTiles.TryGetValue(key, out obj);
#if UNITY_EDITOR
            if (Object.ReferenceEquals(obj, null))
            {
                Debug.Assert(false, string.Format("Hide object at lod 0 {0}_{1} failed!", x, y));
            }
#endif
            var layerData = mLayerData as TileBlockTerrainLayerData;
            int tileType;
            string prefabPath = layerData.GetTilePrefabPath(x, y, out tileType);
            mLOD0TerrainTiles.Remove(key);
            AddToCache(tileType, obj);
        }

        GameObject GetFromCache(int tileType)
        {
            var list = mCachedVisibilityChangedGameObjects[tileType];
            if (list.Count > 0)
            {
                var obj = list[list.Count - 1];
                list.RemoveAt(list.Count - 1);
                return obj;
            }
            return null;
        }

        void AddToCache(int tileType, GameObject obj)
        {
            mCachedVisibilityChangedGameObjects[tileType].Add(obj);
        }

        void RemoveCache()
        {
            var layerData = mLayerData as TileBlockTerrainLayerData;
            string[] tilePrefabPaths = layerData.tilePrefabPaths;
            for (int i = 0; i < mCachedVisibilityChangedGameObjects.Length; ++i)
            {
                string prefabPath = tilePrefabPaths[i];
                var list = mCachedVisibilityChangedGameObjects[i];
                int n = list.Count;
                for (int k = 0; k < n; ++k)
                {
                    mObjectPool.Release(prefabPath, list[k], layerData.map);
                }
                list.Clear();
            }
        }

        public void OnFinishUpdateViewport()
        {
            RemoveCache();
        }

        //显示地图对象的模型
        public void OnShowLOD1Object(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mLOD1TerrainTiles.Count)
            {
                lod = mLOD1TerrainTiles.Count - 1;
            }

#if UNITY_EDITOR
            if (mLOD1TerrainTiles[lod][y, x] != null)
            {
                Debug.Assert(false, string.Format("Show object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            var obj = mObjectPool.Require(data.GetAssetPath(lod));
            Utils.HideGameObject(obj);
            obj.transform.position = data.GetPosition();

            obj.transform.SetParent(root.transform, false);
            mLOD1TerrainTiles[lod][y, x] = obj;
        }

        //隐藏地图对象的模型
        public void OnHideLOD1Object(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mLOD1TerrainTiles.Count)
            {
                lod = mLOD1TerrainTiles.Count - 1;
            }
#if UNITY_EDITOR
            if (mLOD1TerrainTiles[lod][y, x] == null)
            {
                Debug.Assert(false, string.Format("Hide object at lod {0} {1}_{2} failed!", lod, x, y));
            }
#endif
            mObjectPool.Release(data.GetAssetPath(lod), mLOD1TerrainTiles[lod][y, x], layerData.map);
            mLOD1TerrainTiles[lod][y, x] = null;
        }

        Mesh CreateMesh(int tileType)
        {
            var layerData = mLayerData as TileBlockTerrainLayerData;
            var tileBlock = layerData.GetTileBlock(tileType);
            var mesh = new Mesh();
            Vector2[] uv0 = new Vector2[4]
            {
                new Vector2(0, 0),
                new Vector2(0, tileBlock.height),
                new Vector2(tileBlock.width, tileBlock.height),
                new Vector2(tileBlock.width, 0),
            };

            Vector2[] uvInAtlas;
            bool found = layerData.singleTileTypeAtlasUVMappings.TryGetValue(tileBlock.singleTileType, out uvInAtlas);
#if UNITY_EDITOR
            Debug.Assert(found);
#endif

            float minU = Mathf.Min(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
            float minV = Mathf.Min(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);
            float maxU = Mathf.Max(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
            float maxV = Mathf.Max(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);

            Vector4[] uv2 = new Vector4[4]
            {
                new Vector4(minU, minV, maxU, maxV),
                new Vector4(minU, minV, maxU, maxV),
                new Vector4(minU, minV, maxU, maxV),
                new Vector4(minU, minV, maxU, maxV),
            };

            Vector3 minPos = mLayerData.FromCoordinateToWorldPosition(tileBlock.minX, tileBlock.minY);
            Vector3 maxPos = mLayerData.FromCoordinateToWorldPosition(tileBlock.minX + tileBlock.width, tileBlock.minY + tileBlock.height);
            mesh.vertices = new Vector3[]
            {
                Vector3.zero,
                new Vector3(0, 0, maxPos.z - minPos.z),
                new Vector3(maxPos.x - minPos.x, 0, maxPos.z - minPos.z),
                new Vector3(maxPos.x - minPos.x, 0, 0),
            };
            mesh.uv = uv0;
            mesh.SetUVs(1, uv2);
            mesh.triangles = new int[] { 0, 1, 2, 0, 2, 3 };
            mesh.RecalculateBounds();
            mesh.RecalculateNormals();

            mesh.UploadMeshData(true);

            return mesh;
        }

        GameObject CreateTileBlockObject(int tileType)
        {
            Mesh mesh;
            bool found = mLOD0TerrainTileBlockMeshies.TryGetValue(tileType, out mesh);
            if (!found)
            {
                mesh = CreateMesh(tileType);
                mLOD0TerrainTileBlockMeshies.Add(tileType, mesh);
            }

            var layerData = mLayerData as TileBlockTerrainLayerData;
            var tileBlock = layerData.GetTileBlock(tileType);
            Vector3 minPos = mLayerData.FromCoordinateToWorldPosition(tileBlock.minX, tileBlock.minY);
            var obj = RequireGameObject(mesh, minPos, tileBlock.singleTileType);
            return obj;
        }

        GameObject RequireGameObject(Mesh mesh, Vector3 position, int singleTileType)
        {
            GameObject obj = null;
            MeshFilter filter = null;
            if (mTileBlockGameObjectPool.Count > 0)
            {
                obj = mTileBlockGameObjectPool[mTileBlockGameObjectPool.Count - 1];
                mTileBlockGameObjectPool.RemoveAt(mTileBlockGameObjectPool.Count - 1);
                filter = obj.GetComponent<MeshFilter>();
            }
            else
            {
                obj = new GameObject();
                
                var renderer = obj.AddComponent<MeshRenderer>();
                //需要把shader替换成使用combine tile的shader
                renderer.sharedMaterial = (layerData as TileBlockTerrainLayerData).GetMaterialForCombinedTiles(singleTileType);
                filter = obj.AddComponent<MeshFilter>();
            }
            obj.transform.position = position;
            filter.sharedMesh = mesh;

            obj.SetActive(true);
            return obj;
        }

        void ReleaseGameObject(GameObject obj)
        {
            mTileBlockGameObjectPool.Add(obj);
            obj.SetActive(false);
        }

        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        List<GameObject[,]> mLOD1TerrainTiles;
        Dictionary<int, GameObject> mLOD0TerrainTiles = new Dictionary<int, GameObject>();
        Dictionary<int, GameObject> mLOD0TerrainTileBlockGameObjects = new Dictionary<int, GameObject>();
        Dictionary<int, Mesh> mLOD0TerrainTileBlockMeshies = new Dictionary<int, Mesh>();
        GameObjectPool mObjectPool;
        List<GameObject> mTileBlockGameObjectPool = new List<GameObject>();

        List<GameObject>[] mCachedVisibilityChangedGameObjects;
    };
}