﻿
 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.IO;

namespace TFW.Map
{
    public partial class LODLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.LODLayerEditorDataVersion);

            SaveLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);
            writer.Write(GetLayerData().tileWidth);
            writer.Write(GetLayerData().tileHeight);

            //save map layer lod config
            SaveMapLayerLODConfig(writer, GetLayerData());
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                writer.Write((int)0);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }
    }
}

#endif