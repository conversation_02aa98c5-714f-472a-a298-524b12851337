﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class EntitySpawnLayerUI : UnityEditor.Editor
    {
        void DrawBrushes()
        {
            mShowBrushTemplate = EditorGUILayout.Foldout(mShowBrushTemplate, "Brush Setting");

            if (mShowBrushTemplate)
            {
                var layer = mLogic.layer.layerData;
                EditorGUILayout.BeginVertical("GroupBox");
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add"))
                {
                    AddTemplate();
                }
                if (GUILayout.Button("Remove"))
                {
                    if (EditorUtility.DisplayDialog("Warning", "Are you sure delete this? this action can't be redone!", "Yes", "No"))
                    {
                        RemoveTemplate();
                    }
                }
                if (GUILayout.Button("Change Type"))
                {
                    ChangeTemplateType();
                }
                EditorGUILayout.EndHorizontal();

                var templates = layer.regionBrushes;
                int selectedIndex = -1;
                bool selectionChange = false;
                for (int i = 0; i < templates.Count; ++i)
                {
                    selectionChange = DrawTemplate(templates[i], i);
                    if (selectionChange)
                    {
                        selectedIndex = i;
                    }
                }
                if (selectedIndex >= 0)
                {
                    mLogic.selectedIndex = selectedIndex;
                }
                EditorGUILayout.EndVertical();
            }
        }

        bool DrawTemplate(EntitySpawnRegionBrush template, int i)
        {
            EditorGUIUtility.labelWidth = 40;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal();
            bool nowSelected = mLogic.selectedIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            EditorGUIUtility.fieldWidth = 100;
            EditorGUILayout.IntField("ID", template.id);
            EditorGUIUtility.fieldWidth = 0;
            EditorGUIUtility.fieldWidth = 30;
            template.priority = EditorGUILayout.IntField("Priority", template.priority);
            EditorGUIUtility.fieldWidth = 0;
            EditorGUIUtility.fieldWidth = 70;
            template.canGenerateSpawnPoints = EditorGUILayout.ToggleLeft("Generate Points", template.canGenerateSpawnPoints);
            EditorGUIUtility.fieldWidth = 0;
            EditorGUIUtility.fieldWidth = 30;
            var newColor = EditorGUILayout.ColorField("", template.color);
            EditorGUIUtility.fieldWidth = 0;
            if (newColor != template.color)
            {
                template.color = newColor;
            }

            if (GUILayout.Button("Change Color"))
            {
                mLogic.layer.layerView.RefreshTexture();
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.labelWidth = 0;
            return selectionChange;
        }

        void AddTemplate()
        {
            var templates = mLogic.layer.layerData.regionBrushes;
            int type = 1;
            if (templates.Count > 0)
            {
                type = templates[templates.Count - 1].id + 1;
            }
            var dlg = EditorUtils.CreateInputDialog("Add Region Brush");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Type", "", type.ToString()),
                    new InputDialog.StringItem("Priority", "优先级高的tile导出顺序在前", "1"),
                };
            dlg.Show(items, OnClickAdd);
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            string typeStr = (parameters[0] as InputDialog.StringItem).text;
            string priorityStr = (parameters[1] as InputDialog.StringItem).text;
            int type;
            bool suc = Utils.ParseInt(typeStr, out type);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }

            int priority;
            suc = Utils.ParseInt(priorityStr, out priority);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid priority!", "OK");
                return false;
            }

            if (type <= 0)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0", "OK");
                return false;
            }

            var editor = mLogic.layer.layerData;
            var temp = editor.FindBrush(type);
            if (temp != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {type} already existed!", "OK");
                return false;
            }

            var template = editor.AddBrush(type, Color.white, priority);
            mLogic.selectedIndex = editor.regionBrushes.Count - 1;

            return true;
        }

        void RemoveTemplate()
        {
            if (mLogic.selectedIndex >= 0)
            {
                var editor = mLogic.layer.layerData;
                var template = editor.regionBrushes[mLogic.selectedIndex];
                var objects = editor.regionBrushIDs;
                int rows = objects.GetLength(0);
                int cols = objects.GetLength(1);
                for (int i = 0; i < rows; ++i)
                {
                    for (int j = 0; j < cols; ++j)
                    {
                        if (objects[i, j] == template.id)
                        {
                            objects[i, j] = 0;
                        }
                    }
                }

                editor.RemoveBrush(mLogic.selectedIndex);
                mLogic.selectedIndex = editor.regionBrushes.Count - 1;

                mLogic.layer.layerView.RefreshTexture();
            }
        }

        void ChangeTemplateType()
        {
            if (mLogic.selectedIndex >= 0)
            {
                var templates = mLogic.layer.layerData.regionBrushes;
                var dlg = EditorUtils.CreateInputDialog("Change Region Type");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Type", "", templates[mLogic.selectedIndex].id.ToString()),
                };
                dlg.Show(items, OnClickChangeTemplateType);
            }
        }

        bool OnClickChangeTemplateType(List<InputDialog.Item> parameters)
        {
            int newType;
            bool suc = Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newType);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }
            if (newType <= 0 || newType >= byte.MaxValue)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0 and < byte.MaxValue", "OK");
                return false;
            }

            var editor = mLogic.layer.layerData;
            if (editor.FindBrush(newType) != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {newType} is already used!", "OK");
                return false;
            }

            var template = editor.regionBrushes[mLogic.selectedIndex];
            var objects = editor.regionBrushIDs;
            int rows = objects.GetLength(0);
            int cols = objects.GetLength(1);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (objects[i, j] == template.id)
                    {
                        objects[i, j] = newType;
                    }
                }
            }
            template.id = newType;

            return true;
        }
    }
}
#endif