﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理建筑的lod实现的基类
    public abstract class BuildingLODManager : MonoBehaviour
    {
        #region Public Method
        //设置城市信息
        //mainBuilding: 主建筑的game object
        //mainBuildingStartPos: 主建筑的起始世界坐标
        //mainBuildingEndPos: 主建筑的结束世界坐标
        //cityRadius: 城市的半径,作为主城的范围,对于非主城的建筑,可以设置为0
        public void SetCityInfo(GameObject mainBuilding, Vector3 mainBuildingStartPos, Vector3 mainBuildingEndPos, float cityRadius, float notUsed)
        {
            mCityRadius = cityRadius;
            mainBuilding.transform.position = mainBuildingStartPos;
            mMainBuilding = mainBuilding;
            var cityLOD = mainBuilding.GetComponent<IBuilding>();
            var cameraRange = cityLOD.GetCameraHeightRange();
            mCityOpenCameraHeight = cameraRange.y;
            mCityCollapseCameraHeight = cameraRange.x;
            var maxCameraHeightWhenCityIsInCenter = cityLOD.GetMaxCameraHeightWhenCityIsInCenter();
            cameraRange.y = maxCameraHeightWhenCityIsInCenter;

            if (mainBuilding)
            {
                cityLOD.SetMoveInfo(mainBuildingStartPos, mainBuildingEndPos, cameraRange, transform);
                cityLOD.AddPassLowerHeightLimitEvent(OnDownScaleChange);
                cityLOD.AddPassUpperHeightLimitEvent(OnUpScaleChange);
                //主建筑默认显示
                cityLOD.SetVisible(true);
            }

            CreateCollidableList();

            mInit = true;
        }

        //设置相机出城后高度提高的参数
        public void SetCameraRisingParameter(Vector2 xzDistanceAndHeightRatio, Vector2 risingSpeed)
        {
            mXZDistanceAndHeightRatioRange = xzDistanceAndHeightRatio;
            mCameraRisingSpeedRange = risingSpeed;
        }

        //更换城市显示信息
        //mainBuilding: 主建筑的game object
        public void ReSetCityInfo(GameObject mainBuilding)
        {
            mMainBuilding = mainBuilding;
            var cityLOD = mainBuilding.GetComponent<IBuilding>();
            cityLOD.AddPassLowerHeightLimitEvent(OnDownScaleChange);
            cityLOD.AddPassUpperHeightLimitEvent(OnUpScaleChange);
            //主建筑默认显示
            cityLOD.SetVisible(true);
        }

        public virtual void Cleanup()
        {
            if (mMainBuilding != null)
            {
                //uninit all buildings
                var buildings = GetComponentsInChildren<BuildingLODControl>();
                foreach (var building in buildings)
                {
                    building.Uninit();
                }

                var cityLOD = mMainBuilding.GetComponent<IBuilding>();
                cityLOD.RemovePassLowerHeightLimitEvent(OnDownScaleChange);
                cityLOD.RemovePassUpperHeightLimitEvent(OnUpScaleChange);
                mMainBuilding = null;
                mInit = false;
                mIsInside = false;
                mDirty = false;
                mLastCheckZoom = 0;
                mCities.Clear();
                mAllObjects.Clear();
            }
        }

        //当主城的主建筑位置改变时调用
        public void SetCityStartPosition(Vector3 worldStartPos)
        {
            var lodControl = mMainBuilding.GetComponent<BuildingLODControl>();
            if (lodControl != null)
            {
                lodControl.SetCityStartPosition(worldStartPos);
            }
        }

        //当相机高度进入或离开城市的细节模式时调用
        public void SetDownScaleEvent(System.Action<bool> handler)
        {
            mDownScaleEvent -= handler;
            mDownScaleEvent += handler;
        }

        //当主城变成一个图标时调用
        public void SetUpScaleEvent(System.Action<bool> handler)
        {
            mUpScaleEvent -= handler;
            mUpScaleEvent += handler;
        }

        //摄像机高度刷新时调用
        public void AddDoUpdateEvent(System.Action handler)
        {
            mDoUpdateEvent += handler;
        }

        public void RemoveDoUpdateEvent(System.Action handler)
        {
            mDoUpdateEvent -= handler;
        }

        #endregion

        protected BuildingLODManager()
        {
            mHideWallWhenMainBuildingAtFinalPosition = MapModule.hideWallWhenMainBuildingAtFinalPosition;
        }

        void OnDisable()
        {
            //当主城隐藏时,从视野列表内删除
            OnDisableImpl();
            mLastCheckZoom = 0;
            mIsInside = false;
        }

        void Start()
        {
            mDirty = true;
        }

        void OnDestroy()
        {
            mDownScaleEvent = null;
            mUpScaleEvent = null;
            //当主城销毁时,从视野列表内删除
            OnDestroyImpl();
        }

        void OnEnable()
        {
            OnEnableImpl();
        }

        protected abstract void OnEnableImpl();
        protected abstract void OnDisableImpl();
        protected abstract void OnDestroyImpl();

        //当相机高度低于主城缩放的最低点时调用
        //cameraRising:相机的高度是上升还是下降
        protected virtual void OnDownScaleChange(float cameraHeight, bool cameraRising) { }

        //当相机高度高于主城最高点时调用
        //cameraRising:相机的高度是上升还是下降
        protected virtual void OnUpScaleChange(float cameraHeight, bool cameraRising)
        {
        }

        public void AddFadeObject(LODFade fade)
        {
            mFadeObjects.Add(fade);
        }

        public void RemoveFadeObject(LODFade fade)
        {
            mFadeObjects.Remove(fade);
        }

        public virtual void AddCity(IBuildingElement city)
        {
            mCities.Add(city);
            mAllObjects.Add(city);

            OnAddCity(city);
        }

        protected abstract void OnAddCity(IBuildingElement city);

        public void RemoveCity(IBuildingElement city)
        {
            mCities.Remove(city);
            mAllObjects.Remove(city);
        }

        //判断主城是否在视野内
        bool IsInViewport(float cityRadius, Rect viewport)
        {
            var cityPos = transform.position;
            //主城使用圆判断,视野使用矩形
            return Utils.IsRectCircleIntersected(viewport.xMin, viewport.yMin, viewport.xMax, viewport.yMax, cityPos.x, cityPos.z, cityRadius);
        }

        //更新主城lod逻辑
        public void UpdateCity(bool forceUpdate, Rect viewport)
        {
            mUpdatedInThisFrame = false;
            if (mInit == false)
                return;

            if (mDirty)
            {
                mDirty = false;
                DoUpdateCity();
                return;
            }

            if (MapModule.useNewCameraHeightAutoUpdateAlgorithm)
            {
                UpdateCamera();
            }

            bool isInRealViewport = IsInViewport(mRealCityRadius, viewport);
            UpdateCityEnterLeaveViewport(isInRealViewport);

            if (BuildingLODManagerUpdateDataCache.isValidUpperHeight || forceUpdate)
            {
                //修改在视野内的主城列表
                bool isInUpdateViewport = IsInViewport(mCityRadius, viewport);
                UpdateCitiesInViewport(isInUpdateViewport);

                UpdateCityState();

                //玩家或在视野内才更新主城
                if (BuildingLODManagerUpdateDataCache.isValidLowerHeight && (mIsPlayerCity || isInUpdateViewport))
                {
                    float zoom = Map.currentMap.GetZoom();
                    
                    //在相机高度变化或者需要强行更新时才更新主城的lod
                    if (!Utils.Approximately(mLastCheckZoom, zoom))
                    {
                        mLastCheckZoom = zoom;
                        DoUpdateCity();
                    }
                }
            }

            //更新半透明物体
            int n = mFadeObjects.Count;
            if (n > 0 && !Utils.Approximately(MapCameraMgr.lastCameraHeight, MapCameraMgr.currentCameraHeight))
            {
                for (int i = 0; i < n; ++i)
                {
                    mFadeObjects[i].UpdateAlpha();
                }
            }
        }

        protected void DoUpdateCity()
        {
            if (mUpdatedInThisFrame)
            {
                return;
            }
            mUpdatedInThisFrame = true;
            //更新建筑物
            int cityCount = mCities.Count;
            for (int i = cityCount - 1; i >= 0; --i)
            {
                mCities[i].UpdateCity();
            }
            //检测主城内物体的可见性
            UpdateCityVisibility();

            mDoUpdateEvent?.Invoke();
        }

        //更新相机的当前高度和最低高度
        void UpdateCamera()
        {
            CameraAutoUpdateState state;
            float minCameraHeight = 0;
            var viewCenter = Map.currentMap.viewCenter;
            var delta = viewCenter - transform.position;
            float distanceSqr = delta.sqrMagnitude;

            float worldCameraRadius = MapCameraMgr.cameraSetting.cameraRisingTransitionSize + mCityRadius;

            if (distanceSqr <= mCityRadius * mCityRadius)
            {
                state = CameraAutoUpdateState.InCityRange;
                minCameraHeight = MapCameraMgr.cameraSetting.cameraMinHeight;
            }
            else if (distanceSqr >= worldCameraRadius * worldCameraRadius)
            {
                state = CameraAutoUpdateState.InWorld;
                minCameraHeight = MapCameraMgr.cameraSetting.cameraWorldMapMinHeight;
            }
            else
            {
                state = CameraAutoUpdateState.InTransitionRange;
                distanceSqr = Mathf.Sqrt(distanceSqr);
                float t = Mathf.Clamp01((distanceSqr - mCityRadius) / (worldCameraRadius - mCityRadius));
                minCameraHeight = Mathf.Lerp(MapCameraMgr.cameraSetting.cameraMinHeight, MapCameraMgr.cameraSetting.cameraWorldMapMinHeight, t);
            }

            MapCameraMgr.SetAutoUpdateTargetHeight(minCameraHeight, state);
        }

        //判断城市是出城还是进城的状态
        protected virtual void UpdateCityState()
        {
            var viewCenter = Map.currentMap.viewCenter;
            var delta = viewCenter - transform.position;
            bool isInside = true;
            if (delta.sqrMagnitude > mCityRadius * mCityRadius)
            {
                isInside = false;
            }

            if (mIsInside != isInside)
            {
                if (isInside)
                {
                    EnterCity();
                }
                else
                {
                    LeaveCity();
                }
                mIsInside = isInside;
            }
        }

        protected abstract void EnterCity();
        protected abstract void LeaveCity();

        void OnDrawGizmos()
        {
#if true
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(transform.position, mCityRadius);

            if (Map.currentMap != null)
            {
                var camera = Map.currentMap.camera;
                if (camera != null)
                {
                    var renderArea = Map.currentMap.GetViewportRect();
                    var center3 = renderArea.center;
                    var size3 = renderArea.size;
                    Gizmos.color = Color.magenta;
                    Gizmos.DrawWireCube(new Vector3(center3.x, 0, center3.y), new Vector3(size3.x, 1, size3.y));
                }
            }
#endif
        }

        //判断主城内建筑是否可见
        void UpdateCityVisibility()
        {
            if (mOnlyShowMainCity)
            {
                //只显示主建筑
                for (int i = 0; i < mCities.Count; ++i)
                {
                    if (mCities[i].isMainBuilding)
                    {
                        mCities[i].SetVisible(true);
                    }
                    else
                    {
                        mCities[i].SetVisible(false);
                    }
                }
            }
            else
            {
                //根据建筑的碰撞关系来显示建筑
                CheckCityCollision();
            }
        }

        //判断建筑之间的重叠关系
        protected virtual void CheckCityCollision()
        {
            var cameraHeight = MapCameraMgr.currentCameraHeight;
            if (cameraHeight >= mCityOpenCameraHeight)
            {
                //超过最大高度直接显示主建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    if (!mAllObjects[i].isMainBuilding)
                    {
                        mAllObjects[i].SetVisible(false);
                    }
                    else
                    {
                        mAllObjects[i].SetVisible(true);
                    }
                }
            }
            else if (Utils.LE(cameraHeight, mCityCollapseCameraHeight))
            {
                //超过最小高度直接显示所有建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    mAllObjects[i].SetVisible(true);
                }
            }
            else
#if true
            {
                //根据建筑的碰撞关系来显示建筑
                bool useCollisionList = true;
                float lastCameraHeight = MapCameraMgr.lastCameraHeight;
                bool cameraRising = cameraHeight > lastCameraHeight;

                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    var list = mAllObjects[i].GetCollisionList();
                    if (list == null)
                    {
                        useCollisionList = false;
                        list = mAllObjects;
                    }

                    bool isHitObj = false;
                    for (int j = 0; j < list.Count; ++j)
                    {
                        if (mAllObjects[i] != list[j])
                        {
                            if (IsObjectHit(mAllObjects[i], list[j]))
                            {
                                if (list[j].isVisible)
                                {
                                    //隐藏priority大的建筑
                                    if (mAllObjects[i].GetPriority() < list[j].GetPriority())
                                    {
                                        if (mAllObjects[i].isVisible)
                                        {
                                            list[j].SetVisible(false);
                                        }
                                    }
                                    else
                                    {
                                        isHitObj = true;
                                        mAllObjects[i].SetVisible(false);
                                    }

                                    //record collisions
                                    if (useCollisionList == false)
                                    {
                                        mAllObjects[i].AddCollision(list[j]);
                                    }
                                }
                            }
                        }
                    }

                    if (isHitObj == false)
                    {
                        if (mAllObjects[i].isVisible == false)
                        {
                            if (!cameraRising)
                            {
                                mAllObjects[i].SetVisible(true);
                            }
                        }
                    }
                }
            }
#else
            {
                //Debug.Log(gameObject.name + " Check collision at camera height: " + cameraHeight.ToString());
                //根据建筑的碰撞关系来显示建筑
                for (int i = 0; i < mAllObjects.Count; ++i)
                {
                    bool isHitObj = false;
                    var list = mAllObjects[i].GetPotentialCollidableBuildingList();
                    for (int j = 0; j < list.Count; ++j)
                    {
                        if (mAllObjects[i] != list[j])
                        {
                            if (IsObjectHit(mAllObjects[i], list[j]))
                            {
                                if (list[j].isVisible)
                                {
                                    //隐藏priority大的建筑
                                    if (mAllObjects[i].GetPriority() < list[j].GetPriority())
                                    {
                                        if (mAllObjects[i].isVisible)
                                        {
                                            list[j].SetVisible(false);
                                        }
                                    }
                                    else
                                    {
                                        isHitObj = true;
                                        mAllObjects[i].SetVisible(false);
                                    }
                                }
                            }
                        }
                    }

                    if (isHitObj == false)
                    {
                        if (mAllObjects[i].isVisible == false)
                        {
                            mAllObjects[i].SetVisible(true);
                        }
                    }
                }
            }
#endif
        }

        bool IsObjectHitWithMaxRadius(IBuildingElement a, IBuildingElement b)
        {
            if (a is IBuilding && b is IBuilding)
            {
                var buildingA = a as IBuilding;
                var buildingB = b as IBuilding;
                if (buildingA.isMainBuilding || buildingB.isMainBuilding)
                {
                    //默认主城永远能碰到其他建筑
                    return true;
                }
                return IsHit(buildingA, buildingA.maxColliderRadius, buildingB, buildingB.maxColliderRadius);
            }
            else
            {
                CityWall wall;
                IBuilding city;
                if (a.GetType() == typeof(CityWall))
                {
                    wall = a as CityWall;
                    city = b as IBuilding;
                }
                else
                {
                    wall = b as CityWall;
                    city = a as IBuilding;
                }

                if (city.isMainBuilding)
                {
                    return true;
                }

                bool hitWall = wall.IsCollideWithCircle(city.colliderCenter, city.maxColliderRadius);
                return hitWall;
            }
        }

        protected virtual bool IsObjectHit(IBuildingElement a, IBuildingElement b)
        {
            if (a is IBuilding && b is IBuilding)
            {
                var buildingA = a as IBuilding;
                var buildingB = b as IBuilding;
                return IsHit(buildingA, buildingA.scaledColliderRadius, buildingB, buildingB.scaledColliderRadius);
            }
            else
            {
                CityWall wall;
                IBuilding city;
                if (a.GetType() == typeof(CityWall))
                {
                    wall = a as CityWall;
                    city = b as IBuilding;
                }
                else
                {
                    wall = b as CityWall;
                    city = a as IBuilding;
                }

                if (city == null)
                {
                    return false;
                }
                else if (city.isMainBuilding)
                {
                    if (!mHideWallWhenMainBuildingAtFinalPosition)
                    {
                        bool hit = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                        return hit;
                    }

                    if (city.isAtFinalPosition)
                    {
                        //主建筑移动到城中心时才判断和城墙的碰撞
                        bool hit = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                        return hit;
                    }
                    return false;
                }

                bool hitWall = wall.IsCollideWithCircle(city.colliderCenter, city.scaledColliderRadius);
                return hitWall;
            }
        }

#if true
        //使用世界坐标的圆来计算,这样可能会有建筑视觉上的重叠,但是碰撞检测效率高
        protected bool IsHit(IBuilding city0, float r0, IBuilding city1, float r1)
        {
            if (r0 == 0 || r1 == 0)
            {
                //半径为0表示不参与碰撞
                return false;
            }

            float r = r0 + r1;
            r *= r;
            var pos = city0.colliderCenter - city1.colliderCenter;
            if (pos.sqrMagnitude <= r)
            {
                return true;
            }

            return false;
        }
#else
        #region obb test
        //使用collider的屏幕obb投影来检测,效率低但是很准确
        bool IsHit(BuildingLODControl city0, BuildingLODControl city1)
        {
            var collider0 = city0.objectCollider as BoxCollider;
            var collider1 = city1.objectCollider as BoxCollider;
            var obb0 = GetOBB2D(collider0);
            var obb1 = GetOBB2D(collider1);

            if (OBBHit2D(obb0, obb1))
            {
                return true;
            }

            return false;
        }

        //计算box collider在屏幕上的2d投影obb
        Vector2[] GetOBB2D(BoxCollider collider)
        {
            //todo,计算世界坐标下的obb可以不用每帧计算,只在game object位置改变后重新计算
            var center = collider.center;
            var size = collider.size;

            var lt = center - size * 0.5f;
            var rb = center + size * 0.5f;
            lt.z = rb.z;
            var lb = new Vector3(lt.x, rb.y, lt.z);
            var rt = new Vector3(rb.x, lt.y, lt.z);

            var wslt = collider.gameObject.transform.TransformPoint(lt);
            var wslb = collider.gameObject.transform.TransformPoint(lb);
            var wsrb = collider.gameObject.transform.TransformPoint(rb);
            var wsrt = collider.gameObject.transform.TransformPoint(rt);

            if (Map.currentMap != null)
            {
                var camera = Map.currentMap.camera;

                bool useCameraSpace = true;
                if (useCameraSpace)
                {
                    var worldToLocalMatrix = camera.transform.worldToLocalMatrix;
                    Vector2 cslt = worldToLocalMatrix.MultiplyPoint(wslt);
                    Vector2 cslb = worldToLocalMatrix.MultiplyPoint(wslb);
                    Vector2 csrb = worldToLocalMatrix.MultiplyPoint(wsrb);
                    Vector2 csrt = worldToLocalMatrix.MultiplyPoint(wsrt);
                    return new Vector2[] { cslt, cslb, csrb, csrt };
                }
                else
                {
                    Vector2 sslt = camera.WorldToScreenPoint(wslt);
                    Vector2 sslb = camera.WorldToScreenPoint(wslb);
                    Vector2 ssrb = camera.WorldToScreenPoint(wsrb);
                    Vector2 ssrt = camera.WorldToScreenPoint(wsrt);
                    return new Vector2[] { sslt, sslb, ssrb, ssrt };
                }
            }
            else
            {
                return null;
            }
        }

        //在屏幕空间中测试obb是否相交,使用SAT算法
        bool OBBHit2D(Vector2[] obb0, Vector2[] obb1)
        {
            if (obb0 != null && obb1 != null)
            {
                //check obb0 axis
                for (int i = 0; i < obb0.Length; ++i)
                {
                    int next = (i + 1) % obb0.Length;
                    Vector2 n = obb0[next] - obb0[i];
                    n.Normalize();

                    bool intersect = IsObbIntersectedOnAxis(obb0, obb1, obb0[i], n);
                    if (!intersect)
                    {
                        return false;
                    }
                }

                //check obb1 axis
                for (int i = 0; i < obb1.Length; ++i)
                {
                    int next = (i + 1) % obb1.Length;
                    Vector2 n = obb1[next] - obb1[i];
                    n.Normalize();

                    bool intersect = IsObbIntersectedOnAxis(obb0, obb1, obb1[i], n);
                    if (!intersect)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        //计算obb在某个轴上的投影范围
        Vector2 GetProjectRange(Vector2[] obb0, Vector2 axisOrigin, Vector2 axisDir)
        {
            float obb0MinProjDistance = float.MaxValue;
            float obb0MaxProjDistance = float.MinValue;
            for (int i = 0; i < obb0.Length; ++i)
            {
                var dir = obb0[i] - axisOrigin;
                var projPos = Vector2.Dot(dir, axisDir);
                if (projPos < obb0MinProjDistance)
                {
                    obb0MinProjDistance = projPos;
                }
                if (projPos > obb0MaxProjDistance)
                {
                    obb0MaxProjDistance = projPos;
                }
            }

            Debug.Assert(obb0MaxProjDistance >= obb0MinProjDistance);
            return new Vector2(obb0MinProjDistance, obb0MaxProjDistance);
        }

        //判断2个obb在某个轴上的投影是否相交
        bool IsObbIntersectedOnAxis(Vector2[] obb0, Vector2[] obb1, Vector2 axisOrigin, Vector2 axisDir)
        {
            var range0 = GetProjectRange(obb0, axisOrigin, axisDir);
            var range1 = GetProjectRange(obb1, axisOrigin, axisDir);
            if (range0.x > range1.y || range1.x > range0.y)
            {
                return false;
            }
            return true;
        }
        #endregion
#endif

        //设置成玩家主城
        public void SetAsPlayerCity(bool isPlayerCity)
        {
            mIsPlayerCity = isPlayerCity;
        }

        public void CreateCollidableList()
        {
            int n = mAllObjects.Count;
            for (int i = 0; i < n; ++i)
            {
                CreatePotentialCollidableBuildingList(mAllObjects[i]);
            }
        }
        //创建building可能碰撞到的建筑的列表,用于优化碰撞检测,只与当前存在的建筑做检测
        void CreatePotentialCollidableBuildingList(IBuildingElement building)
        {
            building.ClearPotentialCollidableList();
            for (int i = 0; i < mAllObjects.Count; ++i)
            {
                if (building.id != mAllObjects[i].id)
                {
                    if (IsObjectHitWithMaxRadius(building, mAllObjects[i]))
                    {
                        building.AddPotentialCollidableBuilding(mAllObjects[i]);
                    }
                }
            }
        }

        protected void InvokeDownScaleEvent(bool cameraRising)
        {
            if (mDownScaleEvent != null)
            {
                mDownScaleEvent(cameraRising);
            }
        }

        protected void InvokeUpScaleEvent(bool cameraRising)
        {
            if (mUpScaleEvent != null)
            {
                mUpScaleEvent(cameraRising);
            }
        }

        protected void SetInside(bool inside)
        {
            mIsInside = inside;
        }

        public void SetHideWallWhenMainBuildingAtFinalPosition(bool atFinalPosition)
        {
            mHideWallWhenMainBuildingAtFinalPosition = atFinalPosition;
        }

        //更新视野内可见的其他玩家主城的列表
        protected virtual void UpdateCitiesInViewport(bool isInViewport) { }
        protected virtual void UpdateCityEnterLeaveViewport(bool isInViewport) { }

        public Vector3 position { get { return gameObject.transform.position; } }
        public float cityRadius => mCityRadius;

        //主城中所有的建筑的lod控制
        protected List<IBuildingElement> mCities = new List<IBuildingElement>();
        protected List<IBuildingElement> mAllObjects = new List<IBuildingElement>();
        List<LODFade> mFadeObjects = new List<LODFade>();
        //实现主城相机高度的改变
        float mLastCheckZoom = 0;
        //是否是玩家的主城,玩家的主城一直可见
        protected bool mIsPlayerCity = false;
        //是否只显示主城
        protected bool mOnlyShowMainCity = false;
        //判断相机是否能下降到主城最低高度或自动升高
        protected float mCityRadius;
        //判断城市是否出入视野
        float mRealCityRadius = 3.0f;
        protected static Vector2 mXZDistanceAndHeightRatioRange = new Vector2(2, 2);
        protected static Vector2 mCameraRisingSpeedRange = new Vector2(20, 20);
        protected bool mDirty = true;
        //是否在城市主建筑移动到城中心才判断城墙和建筑的碰撞
        protected bool mHideWallWhenMainBuildingAtFinalPosition = true;
        //大地图上完全显示一个主城的相机高度
        static protected float mCityOpenCameraHeight;
        //主城建筑物开始收缩时相机的高度
        static protected float mCityCollapseCameraHeight;
        // 是否设置了初始化参数
        bool mInit = false;
        bool mIsInside = false;
        bool mUpdatedInThisFrame = false;

        GameObject mMainBuilding;

        protected event System.Action<bool> mDownScaleEvent;
        protected event System.Action<bool> mUpScaleEvent;
        protected event System.Action mDoUpdateEvent;
    }
}
