﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class CustomAnimator : CustomAnimatorBase
    {
        static int _AnimationParamsID = Shader.PropertyToID(MapCoreDef.BAKED_ANIMATION_PARAMETERS_PROPERTY_NAME);
        static int _BlendParamsID = Shader.PropertyToID(MapCoreDef.BAKED_ANIMATION_BLEND_PARAMETERS_PROPERTY_NAME);

        public class RendererEntry
        {
            public RendererEntry(RendererShaderType shaderType, MeshRenderer renderer)
            {
                this.shaderType = shaderType;
                this.renderer = renderer;
            }
            public RendererShaderType shaderType;
            public MeshRenderer renderer;
            public Material[] sharedMaterials;
        }

        MaterialPropertyBlock mProps;
        List<RendererEntry> mRenderers = new List<RendererEntry>();
        List<Transform> mCPUDrivenBones = new List<Transform>();
        AnimationBlendType mBlendType;

        public CustomAnimator(GameObject obj) : base(obj)
        {
        }

        public override void OnEnable()
        {
            if (mStateMachine == null)
            {
                mWrapper = mGameObject.GetComponent<AnimatorWrapper>();
                mBlendType = mWrapper.animationData.blendType;

                var tags = mGameObject.GetComponentsInChildren<RendererTag>(true);
                for (int i = 0; i < tags.Length; ++i)
                {
                    var meshRenderer = tags[i].GetComponent<MeshRenderer>();
                    var renderEntry = new RendererEntry(tags[i].shaderType, meshRenderer);
                    mRenderers.Add(renderEntry);

                    var mtls = meshRenderer.sharedMaterials;
                    for (int k = 0; k < mtls.Length; ++k)
                    {
                        if (mWrapper.animationData.useAnimationBlending)
                        {
                            mtls[k].EnableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                        }
                        else
                        {
                            mtls[k].DisableKeyword(MapCoreDef.ENABLE_SKIN_ANIM_BLENDING);
                        }

                        if (mBlendType == AnimationBlendType.Slerp)
                        {
                            mtls[k].EnableKeyword(MapCoreDef.USE_SLERP_BLENDING);
                        }
                        else
                        {
                            mtls[k].DisableKeyword(MapCoreDef.USE_SLERP_BLENDING);
                        }

                        if (mWrapper.animationData.useSRPBatcher)
                        {
                            meshRenderer.materials[k] = mtls[k];
                        }
                    }

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        renderEntry.sharedMaterials = meshRenderer.sharedMaterials;
                    }
                }

                mProps = new MaterialPropertyBlock();
                mStateMachine = new CustomAnimationStateMachine(mWrapper.animationData.animationInfo, mWrapper.animationData.animationStateInfo, mWrapper.animationData.parameterInfo, mWrapper.animationData.useAnimationBlending/*, mWrapper.debugID*/);
#if UNITY_EDITOR
                mStateMachine.SetAnimatorName(mGameObject.name);
#endif
                //init cpu bones
                var names = mWrapper.animationData.GetCPUDrivenCustomBoneNames();
                if (names != null)
                {
                    for (int i = 0; i < names.Length; ++i)
                    {
                        var transform = mWrapper.transform.Find(names[i]);
                        Debug.Assert(transform != null, $"CPU bone {names[i]} not found!");
                        mCPUDrivenBones.Add(transform);
                    }
                }
            }
        }

        public override void OnDisable()
        {
        }

        public override void OnDestroy()
        {
            if (mWrapper.animationData.useSRPBatcher)
            {
                var tags = mGameObject.GetComponentsInChildren<RendererTag>(true);
                for (int i = 0; i < tags.Length; ++i)
                {
                    var meshRenderer = tags[i].GetComponent<MeshRenderer>();
                    var mtls = meshRenderer.sharedMaterials;
                    for (int m = 0; m < mtls.Length; ++m)
                    {
                        Utils.DestroyObject(mtls[m]);
                    }
                }
            }
        }

        public override bool Update(GlobalAnimationBlendingState state)
        {
            if (mStateMachine == null)
            {
                return false;
            }
            bool blendStateChanged = mStateMachine.Update(state);

            if (mStateMachine.enableAnimationBlendingNow)
            {
                UpdateAnimationBlending();
            }
            else
            {
                UpdateAnimation();
            }
            return blendStateChanged;
        }

        void UpdateAnimationBlending()
        {
            var curState = mStateMachine.currentState;
            var animationData = mWrapper.animationData as BakedAnimationData;
            bool isVisible = false;

            var nextState = mStateMachine.nextState;
            bool isInTransition = mStateMachine.IsInTransitionDuration;
            var blendParams = mStateMachine.normalizedDurationRatio;
            float curStateFrame = (int) (isInTransition ? mStateMachine.currentStateFrameInTransitionDuration : curState.currentFrame);
            float nextStateFrame = (int) (isInTransition ? mStateMachine.nextStateFrameInTransitionDuration : 0);

            for (int i = 0; i < mRenderers.Count; ++i)
            {
                var renderer = mRenderers[i].renderer;
                if (renderer.isVisible)
                {
                    isVisible = true;
                    int curAnimOffset = animationData.GetAnimationOffset(curState.animationIndex, mRenderers[i].shaderType);
                    int nextAnimOffset = isInTransition ? animationData.GetAnimationOffset(nextState.animationIndex, mRenderers[i].shaderType) : 0;
                    var animationParams = new Vector4(nextStateFrame, curAnimOffset, curStateFrame, nextAnimOffset);

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        var sharedMaterials = mRenderers[i].sharedMaterials;
                        for (int m = 0; m < sharedMaterials.Length; ++m)
                        {
                            sharedMaterials[m].SetVector(_AnimationParamsID, animationParams);
                            sharedMaterials[m].SetFloat(_BlendParamsID, blendParams);
                        }
                    }
                    else
                    {
                        renderer.GetPropertyBlock(mProps);
                        mProps.SetVector(_AnimationParamsID, animationParams);
                        mProps.SetFloat(_BlendParamsID, blendParams);
                        renderer.SetPropertyBlock(mProps);
                    }
                }
            }

            //update cpu driven bone animations
            var cpuDrivenBoneNames = animationData.GetCPUDrivenCustomBoneNames();
            if (isVisible && cpuDrivenBoneNames != null)
            {
                int cpuDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(curState.animationIndex);
                int nBones = cpuDrivenBoneNames.Length;
                if (isInTransition)
                {
                    int nextStateCPUDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(nextState.animationIndex);
                    for (int i = 0; i < nBones; ++i)
                    {
                        var boneTransform = mCPUDrivenBones[i].transform;
                        int curStateAnimDataOffset = cpuDrivenAnimOffset + (int) curState.currentFrame * nBones + i;
                        int nextStateAnimDataOffset = nextStateCPUDrivenAnimOffset + (int) nextState.currentFrame * nBones + i;
                        var translations = animationData.GetCPUDrivenCustomBoneTranslations();
                        if (translations.Length > 0)
                        {
                            boneTransform.localPosition = Vector3.Lerp(translations[curStateAnimDataOffset], translations[nextStateAnimDataOffset], blendParams);
                        }

                        var scalings = animationData.GetCPUDrivenCustomBoneScalings();
                        if (scalings.Length > 0)
                        {
                            boneTransform.localScale = Vector3.Lerp(scalings[curStateAnimDataOffset], scalings[nextStateAnimDataOffset], blendParams);
                        }

                        var rotations = animationData.GetCPUDrivenCustomBoneRotations();
                        boneTransform.localRotation = Quaternion.Slerp(rotations[curStateAnimDataOffset], rotations[nextStateAnimDataOffset], blendParams);
                    }
                }
                else
                {
                    var translations = animationData.GetCPUDrivenCustomBoneTranslations();
                    var rotations = animationData.GetCPUDrivenCustomBoneRotations();
                    var scalings = animationData.GetCPUDrivenCustomBoneScalings();
                    for (int i = 0; i < nBones; ++i)
                    {
                        int offset = cpuDrivenAnimOffset + (int) curState.currentFrame * nBones + i;
                        var boneTransform = mCPUDrivenBones[i].transform;

                        boneTransform.localPosition = translations[offset];
                        boneTransform.localRotation = rotations[offset];
                        if (scalings.Length > 0)
                        {
                            boneTransform.localScale = scalings[offset];
                        }
                    }
                }
            }
        }

        void UpdateAnimation()
        {
            var curState = mStateMachine.currentState;
            var animationData = mWrapper.animationData as BakedAnimationData;
            bool isVisible = false;
            float curStateFrame = (int)curState.currentFrame;

            for (int i = 0; i < mRenderers.Count; ++i)
            {
                var renderer = mRenderers[i].renderer;
                if (renderer.isVisible)
                {
                    isVisible = true;
                    int curAnimOffset = animationData.GetAnimationOffset(curState.animationIndex, mRenderers[i].shaderType);
                    var animationParams = new Vector4(0, curAnimOffset, curStateFrame, 0);

                    if (mWrapper.animationData.useSRPBatcher)
                    {
                        var mtls = mRenderers[i].sharedMaterials;
                        for (int m = 0; m < mtls.Length; ++m)
                        {
                            mtls[m].SetVector(_AnimationParamsID, animationParams);
                        }
                    }
                    else
                    {
                        renderer.GetPropertyBlock(mProps);
                        mProps.SetVector(_AnimationParamsID, animationParams);
                        renderer.SetPropertyBlock(mProps);
                    }
                }
            }

            //update cpu driven bone animations
            var cpuDrivenBoneNames = animationData.GetCPUDrivenCustomBoneNames();
            if (isVisible && cpuDrivenBoneNames != null)
            {
                int cpuDrivenAnimOffset = animationData.GetCPUDrivenAnimationOffset(curState.animationIndex);
                int nBones = cpuDrivenBoneNames.Length;

                var rotations = animationData.GetCPUDrivenCustomBoneRotations();
                var scalings = animationData.GetCPUDrivenCustomBoneScalings();
                var translations = animationData.GetCPUDrivenCustomBoneTranslations();
                for (int i = 0; i < nBones; ++i)
                {
                    int offset = cpuDrivenAnimOffset + (int)curState.currentFrame * nBones + i;
                    var boneTransform = mCPUDrivenBones[i].transform;
                    boneTransform.localPosition = translations[offset];
                    boneTransform.localRotation = rotations[offset];
                    if (scalings.Length > 0)
                    {
                        boneTransform.localScale = scalings[offset];
                    }
                }
            }
        }
    }
}
