The TexturePacker Importer script (located in Assets/codeandweb.com/Editor)
extends your Unity editor to read sprite atlas data created with TexturePacker.

It automatically detects changed or newly added spritesheets written by TexturePacker
and (re)imports them as native Unity 2D spritesheets, so that their sprites can directly
be used in the editor. In your TexturePacker project you have to select the data format
"Unity - Texture2D sprite sheet".

TexturePacker Importer with Unity 2021.2 (or newer) requires the "Sprite 2D" package,
please make sure that it is part of your Unity project. You can install it using
Unity's package manager.

Visit our tutorial page for more information:
https://www.codeandweb.com/texturepacker/unity
