﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    //管理一个资源建筑的lod实现
    public class ResourceBuildingLODManager : BuildingLODManager
    {
        protected override void OnEnableImpl()
        {
            mIsPlayerCity = false;
            mAllCities.Add(gameObject.GetInstanceID(), this);
        }

        protected override void OnDisableImpl()
        {
            mAllCities.Remove(gameObject.GetInstanceID());
        }

        protected override void OnDestroyImpl()
        {
            mAllCities.Remove(gameObject.GetInstanceID());
        }

        protected override void EnterCity()
        {
        }

        protected override void LeaveCity()
        {
        }

        protected override void UpdateCityState()
        {
        }

        protected override void UpdateCityEnterLeaveViewport(bool isInViewport)
        {
            if (mIsInViewport != isInViewport)
            {
                mIsInViewport = isInViewport;
                if (mIsInViewport)
                {
                    mDirty = true;
                }
            }
        }

        //更新所有主城,手动控制update时机
        public static void UpdateAllCities(bool forceUpdate, Rect viewport)
        {
            foreach (var p in mAllCities)
            {
                p.Value.UpdateCity(forceUpdate, viewport);
            }
        }

        public static void InitGameObjectLOD(GameObject resourceBuilding, float cityRadius = 6.0f)
        {
            GameObject mainBuilding = null;
            Vector3 startPos = Vector3.zero;
            Vector3 endPos = resourceBuilding.transform.position;
            var lodManager = resourceBuilding.GetComponent<ResourceBuildingLODManager>();
            int n = resourceBuilding.transform.childCount;
            //遍历资源建筑根节点下的子节点,设置它们的LODManager
            for (int i = 0; i < n; ++i)
            {
                var buildingObject = resourceBuilding.transform.GetChild(i).gameObject;
                var buildingLODs = buildingObject.GetComponentsInChildren<ResourceBuildingLODControl>();
                foreach (var buildingLOD in buildingLODs)
                {
                    buildingLOD.Init(lodManager);
                }

                var fade = buildingObject.GetComponentInChildren<LODFade>();
                if (fade != null)
                {
                    fade.Init(lodManager);
                }

                //找到主建筑,必须要有一个叫mainBuilding的节点作为主建筑
                if (buildingObject.name == "mainBuilding")
                {
                    startPos = buildingObject.transform.position;
                    mainBuilding = buildingObject;
                }
            }

            if (mainBuilding == null)
            {
                Debug.LogError(
                    "resource spot's main building not found!, there must be a game object named mainBuilding in prefab!");
            }

            if (lodManager != null)
            {
                //设置lod manager的信息
                lodManager.SetCityInfo(mainBuilding, startPos, Vector3.zero, cityRadius, 0);
            }
        }

        //清理lod数据
        public static void UninitGameObjectLOD(GameObject resourceBuilding)
        {
            var lodManager = resourceBuilding.GetComponent<ResourceBuildingLODManager>();
            lodManager?.Cleanup();                         //判断之前editor下报空
        }

        protected override void OnAddCity(IBuildingElement city)
        {
            float cameraHeight = MapCameraMgr.currentCameraHeight;
            if (!city.isMainBuilding && cameraHeight >= mCityOpenCameraHeight)
            {
                city.SetVisible(false);
            }
        }

        bool mIsInViewport = false;

        static Dictionary<int, ResourceBuildingLODManager> mAllCities =
            new Dictionary<int, ResourceBuildingLODManager>();
    }
}