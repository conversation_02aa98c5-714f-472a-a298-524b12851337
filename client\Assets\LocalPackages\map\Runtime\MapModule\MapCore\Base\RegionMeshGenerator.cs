﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public static class RegionMeshGenerator
    {
        //生成矩形区域mesh
        public static GameObject GenerateRectangle(string name, Vector3 center, float width, float height, Material mtl, bool curveCorner = true, float borderSize = 1.0f, int cornerSegment = 8, float uvScale = 10.0f)
        {
            List<Vector3> polygon = new List<Vector3>()
            {
                new Vector3(- width * 0.5f, 0, - height * 0.5f),
                new Vector3(- width * 0.5f, 0, + height * 0.5f),
                new Vector3(+ width * 0.5f, 0, + height * 0.5f),
                new Vector3(+ width * 0.5f, 0, - height * 0.5f),
            };

            if (curveCorner)
            {
                polygon = ConvertToCurveOutline(polygon, borderSize, cornerSegment);
            }
            var mesh = CreateTerritoryMesh(polygon, borderSize, uvScale);

            GameObject obj = new GameObject(name);
            obj.transform.position = center;
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            filter.sharedMesh = mesh;
            renderer.sharedMaterial = mtl;

            return obj;
        }

        //生成多边形区域mesh
        public static GameObject GeneratePolygon(string name, List<Vector3> polygon, Material mtl, bool curveCorner = true, float borderSize = 1.0f, int cornerSegment = 8, float uvScale = 10.0f)
        {
            if (curveCorner)
            {
                polygon = ConvertToCurveOutline(polygon, borderSize, cornerSegment);
            }
            var mesh = CreateTerritoryMesh(polygon, borderSize, uvScale);

            GameObject obj = new GameObject(name);
            
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            filter.sharedMesh = mesh;
            renderer.sharedMaterial = mtl;

            return obj;
        }

        static bool IsLeftTurn(Vector3 a, Vector3 b)
        {
            return Vector3.Cross(a, b).y < 0;
        }

        static List<Vector3> ConvertToCurveOutline(List<Vector3> outline, float borderSize, int cornerSegment)
        {
            List<Vector3> curveOutline = new List<Vector3>();
            int n = outline.Count;
            for (int i = 0; i < n; ++i)
            {
                Vector3 cur = outline[i];
                Vector3 prev = outline[Utils.Mod(i - 1, n)];
                Vector3 next = outline[Utils.Mod(i + 1, n)];
                Vector3 curToPrev = (cur - prev).normalized;
                Vector3 nextToCur = (next - cur).normalized;

                bool isLeftTurn = IsLeftTurn(curToPrev, nextToCur);

                //outer curve
                Vector3 startPosOnPrev = prev + curToPrev * borderSize;
                Vector3 endPosOnPrev = cur - curToPrev * borderSize;
                Vector3 startPosOnNext = cur + nextToCur * borderSize;
                curveOutline.Add(startPosOnPrev);
                curveOutline.Add(endPosOnPrev);
                //generate corner vertices
                Vector3 perp;
                if (isLeftTurn)
                {
                    perp = new Vector3(-curToPrev.z, 0, curToPrev.x);
                }
                else
                {
                    perp = new Vector3(curToPrev.z, 0, -curToPrev.x);
                }
                Vector3 cornerSphereCenter = endPosOnPrev + perp * borderSize;
                int quadrant = CalculateQuadrant(endPosOnPrev, startPosOnNext, cornerSphereCenter);
                List<Vector3> cornerVertices = GenerateCorner(borderSize, cornerSphereCenter, quadrant, isLeftTurn, cornerSegment);
                curveOutline.AddRange(cornerVertices);
            }

            return curveOutline;
        }

        static Mesh CreateTerritoryMesh(List<Vector3> outline, float borderSize, float uvScale)
        {
            Mesh mesh = new Mesh();
            List<Vector3> meshVertices = new List<Vector3>();
            List<Vector2> meshUVs = new List<Vector2>();
            List<int> meshIndices = new List<int>();
            int n = outline.Count;
            float length = 0;

            Vector3 sideVertexPos0 = Vector3.zero;

            for (int i = 0; i < n; ++i)
            {
                Vector3 cur = outline[i];
                Vector3 prev = outline[Utils.Mod(i - 1, n)];
                Vector3 next = outline[Utils.Mod(i + 1, n)];
                Vector3 curToPrev = (cur - prev).normalized;
                Vector3 nextToCur = (next - cur).normalized;
                Vector3 offsetDir = ((nextToCur + curToPrev) * 0.5f).normalized;
                Vector3 perpDir = new Vector3(offsetDir.z, 0, -offsetDir.x);
                Vector3 sideVertex = cur + perpDir * borderSize;
                meshVertices.Add(cur);
                meshVertices.Add(sideVertex);

                if (i == 0)
                {
                    sideVertexPos0 = sideVertex;
                }

                meshUVs.Add(new Vector2(0, length / uvScale));
                meshUVs.Add(new Vector2(1, length / uvScale));

                length += (next - cur).magnitude;

                if (i == n - 1)
                {
                    //由于uv连续的关系,需要在最后追加一组复制的顶点
                    meshVertices.Add(outline[0]);
                    meshVertices.Add(sideVertexPos0);
                    meshUVs.Add(new Vector2(0, length / uvScale));
                    meshUVs.Add(new Vector2(1, length / uvScale));
                }

                int v0 = (i * 2);
                int v1 = (i * 2 + 1);
                int v2 = (i * 2 + 2);
                int v3 = (i * 2 + 3);
                meshIndices.Add(v0);
                meshIndices.Add(v2);
                meshIndices.Add(v1);
                meshIndices.Add(v2);
                meshIndices.Add(v3);
                meshIndices.Add(v1);
            }

            mesh.SetVertices(meshVertices);
            mesh.SetUVs(0, meshUVs);
            mesh.SetIndices(meshIndices, MeshTopology.Triangles, 0);

            return mesh;
        }

        /*
         * quadrant
         * 2 3
         * 1 0
         */
        static int CalculateQuadrant(Vector3 cornerVertexA, Vector3 cornerVertexB, Vector3 cornerSphereCenter)
        {
            if (cornerVertexA.x > cornerSphereCenter.x && cornerVertexB.z < cornerSphereCenter.z ||
                cornerVertexB.x > cornerSphereCenter.x && cornerVertexA.z < cornerSphereCenter.z)
            {
                return 0;
            }

            if (cornerVertexA.x < cornerSphereCenter.x && cornerVertexB.z < cornerSphereCenter.z ||
                cornerVertexB.x < cornerSphereCenter.x && cornerVertexA.z < cornerSphereCenter.z)
            {
                return 1;
            }

            if (cornerVertexA.x < cornerSphereCenter.x && cornerVertexB.z > cornerSphereCenter.z ||
                cornerVertexB.x < cornerSphereCenter.x && cornerVertexA.z > cornerSphereCenter.z)
            {
                return 2;
            }

            if (cornerVertexA.x > cornerSphereCenter.x && cornerVertexB.z > cornerSphereCenter.z ||
                cornerVertexB.x > cornerSphereCenter.x && cornerVertexA.z > cornerSphereCenter.z)
            {
                return 3;
            }

            Debug.Assert(false);
            return 0;
        }

        static List<Vector3> GenerateCorner(float radius, Vector3 center, int quadrant, bool isLeftTurn, int cornerSegment)
        {
            List<Vector3> cornerVertices = new List<Vector3>();
            float deltaAngle = 90.0f / (cornerSegment - 1);
            float startAngle = quadrant * 90.0f + 90;
            int start = 1;
            int end = cornerSegment - 1;
            int delta = 1;
            if (isLeftTurn)
            {
                start = cornerSegment - 1;
                end = 1;
                delta = -1;
            }
            for (int i = start; i != end; i += delta)
            {
                float angle = startAngle + i * deltaAngle;
                float x = Mathf.Sin(angle * Mathf.Deg2Rad) * radius;
                float z = Mathf.Cos(angle * Mathf.Deg2Rad) * radius;
                x += center.x;
                z += center.z;
                cornerVertices.Add(new Vector3(x, 0, z));
            }

            return cornerVertices;
        }

#if false
        class BorderEdge
        {
            public Vector3 start;
            public Vector3 end;
        }

        static List<Vector3> GetOutlinePolygon(int id, List<Vector2Int> coords)
        {
            mStartPosToEdge.Clear();
            //忽略hole
            for (int i = 0; i < coords.Count; ++i)
            {
                int x = coords[i].x;
                int y = coords[i].y;
                int left = GetGridData(x - 1, y);
                if (left != id)
                {
                    AddBorderEdge(x, y, x, y + 1);
                }
                int top = GetGridData(x, y + 1);
                if (top != id)
                {
                    AddBorderEdge(x, y + 1, x + 1, y + 1);
                }
                int right = GetGridData(x + 1, y);
                if (right != id)
                {
                    AddBorderEdge(x + 1, y + 1, x + 1, y);
                }
                int bottom = GetGridData(x, y - 1);
                if (bottom != id)
                {
                    AddBorderEdge(x + 1, y, x, y);
                }
            }

            return ConnectEdges();
        }

        static void AddBorderEdge(int startX, int startY, int endX, int endY)
        {
            var startPos = FromCoordinateToPosition(startX, startY);
            if (!mStartPosToEdge.ContainsKey(startPos))
            {
                var endPos = FromCoordinateToPosition(endX, endY);
                var borderEdge = new BorderEdge { start = startPos, end = endPos };
                mStartPosToEdge.Add(startPos, borderEdge);
            }
        }

        static List<Vector3> ConnectEdges()
        {
            List<Vector3> outline = new List<Vector3>();
            int nEdges = mStartPosToEdge.Count;
            Debug.Assert(nEdges > 0);
            //get first edge
            BorderEdge firstEdge = null;
            foreach (var p in mStartPosToEdge)
            {
                firstEdge = p.Value;
                break;
            }

            outline.Add(firstEdge.start);

            for (int i = 1; i < nEdges; ++i)
            {
                BorderEdge nextEdge;
                mStartPosToEdge.TryGetValue(firstEdge.end, out nextEdge);
                Debug.Assert(nextEdge != null);

                if (!SameDirection(firstEdge, nextEdge))
                {
                    outline.Add(nextEdge.start);
                }

                firstEdge = nextEdge;
            }

            return outline;
        }

        static bool SameDirection(BorderEdge a, BorderEdge b)
        {
            var dirA = a.end - a.start;
            var dirB = b.end - b.start;
            dirA.Normalize();
            dirB.Normalize();
            float dot = Vector3.Dot(dirA, dirB);
            if (Mathf.Approximately(dot, 1) || Mathf.Approximately(dot, -1))
            {
                return true;
            }
            return false;
        }

        static Dictionary<Vector3, BorderEdge> mStartPosToEdge = new Dictionary<Vector3, BorderEdge>();
#endif
    }
}
