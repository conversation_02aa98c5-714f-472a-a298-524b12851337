﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class BuildingGridData : TextureGridData
    {
        public BuildingGridData(int id, Color color, bool walkable) : base(id, color)
        {
            this.walkable = walkable;
        }

        public bool walkable;
    }

    public class BuildingGridLayerData : TextureGridLayerData
    {
        public BuildingGridLayerData(MapLayerDataHeader header, Map map, List<Layer> layers) :base(header, map, layers)
        {
        }
    }
}


#endif