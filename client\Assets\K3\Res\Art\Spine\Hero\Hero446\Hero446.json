{"skeleton": {"hash": "p9h8b7l8Tas", "spine": "4.2.33", "x": -344.46, "y": -2.43, "width": 613, "height": 1781.84, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "ALL", "parent": "root", "length": 100, "x": 300.43, "y": 1194.42}, {"name": "ALL2", "parent": "ALL", "x": -271.35, "y": -23.99, "icon": "arrows"}, {"name": "body", "parent": "ALL2", "length": 86.56, "rotation": 98, "x": -2.53, "y": 9.35}, {"name": "body2", "parent": "body", "length": 232.52, "rotation": 91.98, "x": 86.56, "inherit": "onlyTranslation"}, {"name": "neck", "parent": "body2", "length": 81.49, "rotation": 83.72, "x": 232.52, "inherit": "onlyTranslation"}, {"name": "head", "parent": "neck", "length": 208, "rotation": 1.48, "x": 81.49}, {"name": "tun", "parent": "ALL2", "length": 125.22, "rotation": -76.92, "x": 0.07, "y": -9.24}, {"name": "leg_L2", "parent": "tun", "rotation": -2.15, "x": 108.96, "y": 106.37}, {"name": "leg_R2", "parent": "tun", "rotation": -2.15, "x": 87.71, "y": -92.99}, {"name": "leg_L3", "parent": "leg_L2", "length": 450.44, "rotation": -17.63, "x": 57.41, "y": 13.4}, {"name": "leg_L4", "parent": "leg_L3", "length": 400, "rotation": 2.22, "x": 450.44, "inherit": "noScale"}, {"name": "foot_L2", "parent": "root", "length": 137.69, "rotation": 89.48, "x": 96.72, "y": 39.83, "inherit": "noScale"}, {"name": "foot_R2", "parent": "root", "length": 154.87, "rotation": 81.38, "x": -199.4, "y": 30.92, "inherit": "noScale"}, {"name": "leg_R3", "parent": "leg_R2", "length": 422.04, "rotation": -19.73, "x": 57.74, "y": -6.34}, {"name": "leg_R4", "parent": "leg_R3", "length": 395.72, "rotation": -1.98, "x": 422.04, "inherit": "noScale"}, {"name": "sh_L", "parent": "body2", "x": 179.87, "y": -140.61, "inherit": "noScale"}, {"name": "sh_R", "parent": "body2", "x": 221.68, "y": 158.58, "inherit": "noScale"}, {"name": "arm_R2", "parent": "sh_R", "length": 240.04, "rotation": 139.02, "x": -3.86, "y": 3.84}, {"name": "arm_R3", "parent": "arm_R2", "length": 189.25, "rotation": 113.96, "x": 240.04, "inherit": "noScale"}, {"name": "arm_L2", "parent": "sh_L", "length": 226.19, "rotation": -170.03, "x": -5.78, "y": -5.52}, {"name": "arm_L3", "parent": "arm_L2", "length": 184, "rotation": -95.39, "x": 226.19, "inherit": "noScale"}, {"name": "hand_L", "parent": "arm_L3", "length": 130, "rotation": -154.07, "x": 184, "inherit": "onlyTranslation"}, {"name": "hand_R", "parent": "hand_L", "length": 61, "rotation": -73.09, "x": 92.84, "y": -39.48}, {"name": "bone24", "parent": "sh_L", "length": 254.75, "rotation": -164.59, "x": -5.15, "y": -5.21}, {"name": "bone25", "parent": "bone24", "length": 211.68, "rotation": -107.52, "x": 254.75, "color": "abe323ff"}, {"name": "arm_L", "parent": "body", "rotation": -98, "x": 23.37, "y": 4.78, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_L1", "parent": "root", "x": 201.35, "y": 1223.25, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone26", "parent": "sh_R", "length": 279.66, "rotation": 133.57, "x": -3.63, "y": 4.16}, {"name": "bone27", "parent": "bone26", "length": 230.28, "rotation": 125.23, "x": 279.66, "color": "abe323ff"}, {"name": "arm_R", "parent": "hand_R", "rotation": -132.84, "x": 61.03, "y": -0.07, "color": "ff3f00ff", "icon": "ik"}, {"name": "arm_R1", "parent": "root", "x": -306.4, "y": 1291.03, "color": "ff3f00ff", "icon": "ik"}, {"name": "RU_L", "parent": "body2", "x": 94.52, "y": -83.42}, {"name": "RU_L2", "parent": "RU_L", "x": -13.1, "y": -17.18}, {"name": "RU_L3", "parent": "RU_L2", "x": -10.43, "y": -8.98}, {"name": "RU_R", "parent": "body2", "x": 104.34, "y": 83.19}, {"name": "RU_R2", "parent": "RU_R", "x": -15.78, "y": 19.71}, {"name": "RU_R3", "parent": "RU_R2", "x": -10.58, "y": 11.72}, {"name": "bone34", "parent": "leg_L2", "length": 852.18, "rotation": -16.58, "x": 57.41, "y": 13.38}, {"name": "leg_L", "parent": "foot_L2", "rotation": -89.48, "x": 139.34, "y": 0.23, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_L1", "parent": "bone34", "rotation": 95.65, "x": 450.36, "y": -8.21, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone35", "parent": "leg_R2", "length": 820.57, "rotation": -20.68, "x": 57.74, "y": -6.32}, {"name": "leg_R", "parent": "foot_R2", "rotation": -81.38, "x": 161.88, "y": 0.25, "color": "ff3f00ff", "icon": "ik"}, {"name": "leg_R1", "parent": "bone35", "rotation": 99.75, "x": 421.98, "y": 7.05, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone36", "parent": "head", "length": 12.96, "rotation": 67.81, "x": 79.68, "y": -61.93}, {"name": "bone37", "parent": "bone36", "length": 17.54, "rotation": 48.02, "x": 12.96}, {"name": "bone38", "parent": "bone37", "length": 15.34, "rotation": -1.86, "x": 17.54}, {"name": "bone39", "parent": "head", "length": 14.37, "rotation": -60.14, "x": 76.26, "y": 49.16}, {"name": "bone40", "parent": "bone39", "length": 18.56, "rotation": -46.99, "x": 14.37}, {"name": "bone41", "parent": "bone40", "length": 15.43, "rotation": -11.98, "x": 18.56}, {"name": "bone42", "parent": "head", "x": 61.29, "y": -39.39}, {"name": "bone43", "parent": "head", "x": 60.75, "y": 26.77}, {"name": "hair", "parent": "head", "x": 131.52, "y": -6.96}, {"name": "hair_L", "parent": "hair", "length": 39.82, "rotation": -158, "x": 17.04, "y": -33.08}, {"name": "hair_L2", "parent": "hair_L", "length": 45.51, "rotation": 19.07, "x": 39.82, "color": "abe323ff"}, {"name": "hair_L3", "parent": "hair_L2", "length": 46.99, "rotation": 12.5, "x": 45.51, "color": "abe323ff"}, {"name": "hair_L4", "parent": "hair_L3", "length": 43.04, "rotation": -15.58, "x": 46.99, "color": "abe323ff"}, {"name": "hair_L5", "parent": "hair_L4", "length": 28.23, "rotation": -24.97, "x": 43.04, "color": "abe323ff"}, {"name": "hair_L6", "parent": "hair_L5", "length": 24.24, "rotation": -28.73, "x": 28.23, "color": "abe323ff"}, {"name": "hair_R", "parent": "hair", "length": 38.04, "rotation": 153.92, "x": 18.08, "y": 31.41}, {"name": "hair_R2", "parent": "hair_R", "length": 42.04, "rotation": 1.64, "x": 38.04, "color": "abe323ff"}, {"name": "hair_R3", "parent": "hair_R2", "length": 40.8, "rotation": -23.14, "x": 42.04, "color": "abe323ff"}, {"name": "hair_R4", "parent": "hair_R3", "length": 41.54, "rotation": -0.2, "x": 40.8, "color": "abe323ff"}, {"name": "hair_R5", "parent": "hair_R4", "length": 31.44, "rotation": 31.23, "x": 41.54, "color": "abe323ff"}, {"name": "hair_R6", "parent": "hair_R5", "length": 19.52, "rotation": 53.67, "x": 31.44, "color": "abe323ff"}, {"name": "hair_R7", "parent": "hair_R6", "length": 18.56, "rotation": 53.52, "x": 19.52, "color": "abe323ff"}, {"name": "hair_RR", "parent": "hair", "length": 52.55, "rotation": 144.41, "x": 17.14, "y": 36.17}, {"name": "hair_RR2", "parent": "hair_RR", "length": 40.38, "rotation": 16.9, "x": 52.55, "color": "abe323ff"}, {"name": "hair_RR3", "parent": "hair_RR2", "length": 43.24, "rotation": 14.02, "x": 40.38, "color": "abe323ff"}, {"name": "hair_RR4", "parent": "hair_RR3", "length": 39.85, "rotation": 21.93, "x": 43.24, "color": "abe323ff"}, {"name": "hair_RR5", "parent": "hair_RR4", "length": 32.27, "rotation": 22.14, "x": 39.85, "color": "abe323ff"}, {"name": "hair_LL", "parent": "hair", "length": 47.86, "rotation": -147.24, "x": 16.8, "y": -39.52}, {"name": "hair_LL2", "parent": "hair_LL", "length": 46.15, "rotation": -8.06, "x": 47.86, "color": "abe323ff"}, {"name": "hair_LL3", "parent": "hair_LL2", "length": 42.75, "rotation": -15.89, "x": 46.15, "color": "abe323ff"}, {"name": "hair_LL4", "parent": "hair_LL3", "length": 41.42, "rotation": -20.81, "x": 42.75, "color": "abe323ff"}, {"name": "hair_LL5", "parent": "hair_LL4", "length": 35.99, "rotation": -26.52, "x": 41.42, "color": "abe323ff"}, {"name": "headround3", "parent": "head", "x": 428.56, "y": -4.09, "color": "abe323ff", "icon": "arrowLeftRight"}, {"name": "headround", "parent": "headround3", "y": -57.41, "color": "abe323ff", "icon": "arrowUpDown"}, {"name": "headround2", "parent": "head", "x": 378.42, "y": -61.5, "icon": "warning"}, {"name": "bodyround", "parent": "body2", "x": 163.31, "y": -326.84, "color": "abe323ff", "icon": "arrowsB"}, {"name": "bodyround2", "parent": "body2", "x": 108.17, "y": -326.84, "icon": "warning"}, {"name": "tunround", "parent": "ALL2", "x": 353.25, "y": -63.39, "color": "abe323ff", "icon": "arrowsB"}, {"name": "tunround2", "parent": "ALL2", "x": 353.25, "y": -119.15, "icon": "warning"}, {"name": "foot_R", "parent": "root", "length": 65.62, "rotation": 26.4, "x": -258.46, "y": 1.47}, {"name": "foot_L", "parent": "root", "length": 40.61, "rotation": 94.18, "x": 99.46, "y": -0.39}], "slots": [{"name": "arm", "bone": "root", "attachment": "arm"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "arm2", "bone": "root", "attachment": "arm"}, {"name": "eyewhite_R", "bone": "root", "attachment": "eyewhite_R"}, {"name": "eyewhite_L", "bone": "root", "attachment": "eyewhite_L"}, {"name": "eye_R", "bone": "root", "attachment": "eye_R"}, {"name": "eye_L", "bone": "root", "attachment": "eye_L"}, {"name": "head", "bone": "root", "attachment": "head"}, {"name": "head2", "bone": "root", "attachment": "head"}, {"name": "eye_RR", "bone": "root", "attachment": "eye_RR"}, {"name": "eye_LL", "bone": "root", "attachment": "eye_LL"}, {"name": "glass", "bone": "root", "attachment": "glass"}, {"name": "hair_L", "bone": "root", "attachment": "hair_L"}, {"name": "hair_R", "bone": "root", "attachment": "hair_R"}, {"name": "hair_F", "bone": "root", "attachment": "hair_F"}], "ik": [{"name": "arm_L", "bones": ["bone24", "bone25"], "target": "arm_L", "bendPositive": false}, {"name": "arm_L1", "order": 2, "bones": ["arm_L2"], "target": "arm_L1", "compress": true, "stretch": true}, {"name": "arm_L2", "order": 3, "bones": ["arm_L3"], "target": "arm_L", "compress": true, "stretch": true}, {"name": "arm_R", "order": 4, "bones": ["bone26", "bone27"], "target": "arm_R"}, {"name": "arm_R1", "order": 6, "bones": ["arm_R2"], "target": "arm_R1", "compress": true, "stretch": true}, {"name": "arm_R2", "order": 7, "bones": ["arm_R3"], "target": "arm_R", "compress": true, "stretch": true}, {"name": "leg_L", "order": 9, "bones": ["bone34"], "target": "leg_L", "compress": true, "stretch": true}, {"name": "leg_L1", "order": 10, "bones": ["leg_L3"], "target": "leg_L1", "compress": true, "stretch": true}, {"name": "leg_L2", "order": 11, "bones": ["leg_L4"], "target": "leg_L", "compress": true, "stretch": true}, {"name": "leg_R", "order": 13, "bones": ["bone35"], "target": "leg_R", "compress": true, "stretch": true}, {"name": "leg_R1", "order": 14, "bones": ["leg_R3"], "target": "leg_R1", "compress": true, "stretch": true}, {"name": "leg_R2", "order": 15, "bones": ["leg_R4"], "target": "leg_R", "compress": true, "stretch": true}], "transform": [{"name": "arm_L3", "order": 1, "bones": ["arm_L1"], "target": "bone25", "rotation": -179.94, "x": 28.91, "y": -21.2, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "arm_R3", "order": 5, "bones": ["arm_R1"], "target": "bone27", "rotation": 8.95, "x": 42.1, "y": 20.05, "mixRotate": 0.8, "mixX": 0.8, "mixScaleX": 0.8, "mixShearY": 0.8}, {"name": "bodyround", "order": 16, "bones": ["bodyround2"], "target": "bodyround", "x": -55.15, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround", "order": 17, "bones": ["headround2"], "target": "headround", "x": -50.14, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround1", "order": 18, "bones": ["hair"], "target": "headround", "x": -297.04, "y": 54.54, "mixRotate": 0, "mixX": 0.035, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround2", "order": 19, "bones": ["bone36"], "target": "headround", "rotation": 67.81, "x": -348.88, "y": -0.43, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "headround3", "order": 20, "bones": ["bone39"], "target": "headround", "rotation": -60.14, "x": -352.29, "y": 110.66, "mixRotate": 0, "mixX": 0.042, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround", "order": 21, "bones": ["tunround2"], "target": "tunround", "y": -55.76, "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround1", "order": 8, "bones": ["leg_L2"], "target": "tunround", "rotation": -79.07, "x": -224.91, "y": -27.9, "mixRotate": 0, "mixX": 0.008, "mixScaleX": 0, "mixShearY": 0}, {"name": "tunround2", "order": 12, "bones": ["leg_R2"], "target": "tunround", "rotation": -79.07, "x": -423.9, "y": -52.32, "mixRotate": 0, "mixX": -0.006, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "hair_L2", "order": 26, "bone": "hair_L2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L3", "order": 27, "bone": "hair_L3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L4", "order": 28, "bone": "hair_L4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L5", "order": 29, "bone": "hair_L5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_L6", "order": 30, "bone": "hair_L6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL2", "order": 22, "bone": "hair_LL2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL3", "order": 23, "bone": "hair_LL3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL4", "order": 24, "bone": "hair_LL4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_LL5", "order": 25, "bone": "hair_LL5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R2", "order": 35, "bone": "hair_R2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R3", "order": 36, "bone": "hair_R3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R4", "order": 37, "bone": "hair_R4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R5", "order": 38, "bone": "hair_R5", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R6", "order": 39, "bone": "hair_R6", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_R7", "order": 40, "bone": "hair_R7", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR2", "order": 31, "bone": "hair_RR2", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR3", "order": 32, "bone": "hair_RR3", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR4", "order": 33, "bone": "hair_RR4", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "hair_RR5", "order": 34, "bone": "hair_RR5", "rotate": 1, "inertia": 0.5, "damping": 0.85}], "skins": [{"name": "default", "attachments": {"arm": {"arm": {"type": "mesh", "uvs": [0.32688, 0.00282, 0.35772, 0.02554, 0.40028, 0.07201, 0.41089, 0.16051, 0.40512, 0.22547, 0.39263, 0.27695, 0.35058, 0.33209, 0.3161, 0.39224, 0.28921, 0.44054, 0.25222, 0.48547, 0.22096, 0.50936, 0.19628, 0.5123, 0.19138, 0.52938, 0.20499, 0.54286, 0.26676, 0.58759, 0.3467, 0.62121, 0.37166, 0.62812, 0.39417, 0.63564, 0.4193, 0.66381, 0.47604, 0.72776, 0.56223, 0.7243, 0.5985, 0.73637, 0.62946, 0.72417, 0.66111, 0.72389, 0.69276, 0.7236, 0.73044, 0.71376, 0.82199, 0.67092, 0.84273, 0.65843, 0.83277, 0.63747, 0.82251, 0.57301, 0.80944, 0.50799, 0.7953, 0.43743, 0.78287, 0.37635, 0.77103, 0.30776, 0.76463, 0.24247, 0.78415, 0.1814, 0.81885, 0.10617, 0.87046, 0.09112, 0.90148, 0.12238, 0.90943, 0.16844, 0.92429, 0.1984, 0.93792, 0.23697, 0.92751, 0.306, 0.94149, 0.36043, 0.93467, 0.39926, 0.95845, 0.46276, 0.969, 0.52099, 0.97685, 0.57571, 0.99196, 0.62901, 0.99949, 0.72053, 0.9885, 0.78924, 0.96195, 0.85434, 0.91301, 0.8702, 0.71854, 0.85956, 0.68064, 0.85584, 0.65529, 0.8555, 0.63414, 0.85688, 0.62001, 0.8746, 0.59679, 0.90418, 0.51499, 0.98346, 0.49142, 1, 0.47638, 1, 0.40882, 0.95828, 0.39109, 0.9154, 0.4012, 0.85457, 0.37331, 0.78303, 0.3617, 0.75158, 0.34811, 0.73941, 0.31736, 0.73577, 0.12788, 0.7123, 0.08838, 0.69599, 0.04355, 0.66128, 0.01344, 0.64609, 0, 0.60588, 0.00402, 0.54351, 0.03243, 0.4507, 0.07799, 0.4074, 0.07824, 0.37562, 0.12334, 0.30369, 0.15114, 0.27419, 0.20212, 0.15592, 0.22947, 0.15351, 0.22926, 0.08669, 0.25797, 0.08966, 0.27907, 0.04622, 0.30017, 0.00279, 0.14417, 0.49342, 0.08441, 0.48827, 0.03901, 0.52514, 0.08674, 0.45546, 0.06437, 0.59279, 0.11421, 0.53462, 0.12955, 0.60423, 0.32081, 0.07529, 0.34073, 0.15539, 0.35457, 0.23796, 0.2965, 0.13114, 0.26943, 0.18474, 0.12245, 0.42225, 0.32103, 0.26236, 0.17391, 0.4718, 0.20659, 0.42303, 0.1582, 0.36469, 0.87269, 0.14519, 0.84338, 0.26164, 0.85447, 0.20607, 0.83234, 0.34279, 0.87687, 0.60884, 0.88588, 0.63989, 0.94483, 0.64258, 0.91409, 0.6821, 0.94034, 0.77378, 0.87625, 0.71043, 0.89358, 0.79603, 0.81202, 0.77016, 0.9334, 0.59441, 0.88783, 0.3244, 0.88808, 0.26383, 0.8854, 0.20897, 0.92296, 0.53252, 0.86777, 0.55243], "triangles": [6, 95, 5, 95, 94, 4, 4, 94, 3, 5, 95, 4, 34, 35, 104, 104, 35, 105, 106, 33, 104, 33, 34, 104, 32, 33, 106, 117, 104, 105, 118, 39, 40, 117, 118, 40, 117, 40, 41, 42, 117, 41, 116, 117, 42, 116, 104, 117, 43, 116, 42, 44, 116, 43, 31, 32, 106, 120, 30, 31, 119, 116, 44, 119, 44, 45, 119, 45, 46, 120, 116, 119, 120, 106, 116, 120, 31, 106, 29, 30, 120, 115, 119, 46, 115, 46, 47, 107, 120, 119, 107, 119, 115, 29, 120, 107, 28, 29, 107, 108, 107, 115, 27, 28, 107, 109, 115, 47, 109, 47, 48, 108, 115, 109, 108, 27, 107, 110, 108, 109, 27, 108, 110, 109, 48, 49, 49, 111, 110, 49, 110, 109, 116, 106, 104, 118, 117, 105, 112, 27, 110, 114, 25, 26, 26, 27, 112, 114, 26, 112, 112, 110, 111, 50, 111, 49, 113, 112, 111, 114, 112, 113, 51, 111, 50, 55, 22, 23, 54, 23, 24, 55, 23, 54, 56, 22, 55, 53, 24, 25, 53, 25, 114, 54, 24, 53, 52, 113, 111, 52, 111, 51, 114, 113, 52, 53, 114, 52, 21, 22, 56, 57, 21, 56, 58, 20, 21, 58, 21, 57, 64, 61, 62, 63, 64, 62, 58, 59, 19, 58, 19, 20, 59, 64, 19, 59, 61, 64, 60, 61, 59, 66, 17, 18, 65, 66, 18, 65, 18, 19, 64, 65, 19, 12, 92, 91, 90, 91, 92, 74, 90, 73, 72, 73, 90, 71, 72, 90, 70, 90, 92, 71, 90, 70, 69, 70, 92, 68, 14, 15, 69, 92, 13, 92, 12, 13, 69, 13, 14, 68, 69, 14, 67, 15, 16, 68, 15, 67, 17, 66, 67, 17, 67, 16, 83, 84, 96, 81, 82, 83, 97, 81, 83, 96, 97, 83, 94, 97, 96, 99, 97, 94, 6, 99, 95, 79, 97, 102, 78, 79, 102, 80, 97, 79, 81, 97, 80, 98, 77, 78, 97, 101, 102, 7, 99, 6, 97, 99, 101, 102, 98, 78, 76, 77, 98, 7, 101, 99, 8, 101, 7, 89, 76, 98, 75, 76, 89, 100, 102, 101, 98, 102, 100, 9, 101, 8, 87, 75, 89, 86, 98, 100, 89, 98, 86, 87, 89, 86, 10, 101, 9, 11, 100, 101, 10, 11, 101, 88, 75, 87, 12, 100, 11, 86, 100, 12, 91, 87, 86, 74, 75, 88, 90, 88, 87, 90, 87, 91, 12, 91, 86, 90, 74, 88, 99, 94, 95, 94, 2, 3, 96, 93, 94, 84, 85, 93, 93, 0, 1, 2, 93, 1, 93, 85, 0, 2, 94, 93, 96, 84, 93, 118, 103, 39, 103, 37, 38, 36, 37, 103, 105, 36, 103, 105, 103, 118, 103, 38, 39, 35, 36, 105], "vertices": [1, 17, 25.9, 2.65, 1, 1, 17, 16.47, -14.89, 1, 2, 17, -2.4, -38.89, 0.7919, 4, 219.29, 119.68, 0.2081, 2, 17, -36.93, -43.85, 0.36417, 4, 184.76, 114.73, 0.63583, 3, 17, -62, -39.64, 0.14951, 18, 15.38, 70.95, 0.04, 4, 159.68, 118.94, 0.81049, 3, 17, -81.71, -31.72, 0.05756, 18, 35.46, 77.9, 0.18286, 4, 139.97, 126.86, 0.75958, 3, 17, -102.25, -6.65, 0.00622, 18, 67.4, 72.44, 0.65664, 4, 119.43, 151.93, 0.33714, 3, 18, 98.1, 71.61, 0.90592, 19, 123.1, 100.61, 0.00325, 4, 96.8, 172.68, 0.09084, 2, 18, 122.47, 71.3, 0.98055, 19, 112.92, 78.47, 0.01945, 2, 18, 149.49, 65.63, 0.91917, 19, 96.76, 56.08, 0.08083, 2, 18, 168.09, 57.4, 0.8114, 19, 81.68, 42.43, 0.1886, 2, 18, 177.97, 47.01, 0.62292, 19, 68.17, 37.62, 0.37708, 2, 18, 184.9, 48.97, 0.37656, 19, 67.15, 30.49, 0.62344, 2, 18, 184.01, 58.39, 0.16521, 19, 76.12, 27.48, 0.83479, 1, 19, 115.16, 20, 1, 1, 19, 163.25, 19.4, 1, 2, 19, 177.9, 20.55, 0.88967, 23, 81.57, -11.44, 0.11033, 2, 19, 191.24, 21.12, 0.59063, 23, 70.57, -19.01, 0.40937, 2, 19, 208.14, 14.34, 0.11398, 23, 52.66, -22.25, 0.88602, 2, 23, 12.13, -29.47, 0.8783, 22, 68.17, -59.65, 0.1217, 3, 23, -20.82, -66.97, 0.16164, 22, 22.7, -39.03, 0.83253, 21, 218.37, -29.29, 0.00582, 3, 23, -38.54, -79.19, 0.03417, 22, 5.86, -25.64, 0.78338, 21, 198.04, -22.24, 0.18245, 3, 23, -47.26, -95.55, 0.00327, 22, -12.33, -22.05, 0.28473, 21, 179.69, -24.9, 0.71201, 2, 22, -28.86, -14.14, 0.01301, 21, 161.47, -22.92, 0.98699, 1, 21, 143.25, -20.94, 1, 1, 21, 121.14, -22.24, 1, 2, 21, 66.58, -32.7, 0.84588, 20, 187.38, -63.21, 0.15412, 2, 21, 54.09, -36.15, 0.58653, 20, 185.12, -50.45, 0.41347, 2, 21, 58.9, -44.89, 0.37887, 20, 175.97, -54.41, 0.62113, 2, 21, 61.94, -70.41, 0.1176, 20, 150.27, -55.04, 0.8824, 2, 21, 66.58, -96.34, 0.02176, 20, 124.02, -57.23, 0.97824, 3, 21, 71.59, -124.47, 0.00076, 20, 95.55, -59.57, 0.88781, 4, 69.68, -104, 0.11143, 2, 20, 70.87, -61.7, 0.70571, 4, 93.62, -97.62, 0.29429, 3, 16, -59.42, 48.92, 0.09815, 20, 43.41, -62.9, 0.26664, 4, 120.45, -91.69, 0.63521, 1, 4, 145.9, -88.87, 1, 2, 16, -10.69, 39.63, 0.33428, 4, 169.19, -100.98, 0.66572, 2, 16, 17.79, 18.54, 0.69429, 4, 197.66, -122.07, 0.30571, 1, 16, 22.6, -11.52, 1, 1, 16, 9.85, -29.05, 1, 2, 16, -8.17, -33.04, 0.55429, 20, 7.12, 26.69, 0.44571, 2, 16, -20.08, -41.23, 0.13714, 20, 20.27, 32.7, 0.86286, 2, 16, -35.31, -48.6, 0.02857, 20, 36.55, 37.32, 0.97143, 1, 20, 61.5, 25.88, 1, 1, 20, 83.84, 29.43, 1, 1, 20, 97.76, 22.44, 1, 1, 20, 124.72, 30.82, 1, 1, 20, 148.08, 32.12, 1, 1, 20, 169.79, 32.16, 1, 1, 20, 191.84, 36.44, 1, 2, 21, -33.33, -1.85, 0.14939, 20, 227.48, 33.36, 0.85061, 2, 21, -23.97, 23.9, 0.58241, 20, 252.25, 21.61, 0.41759, 2, 21, -5.81, 47.24, 0.89129, 20, 273.78, 1.34, 0.10871, 2, 21, 23.04, 50.12, 0.99171, 20, 273.93, -27.65, 0.00829, 2, 22, -35.74, 47.74, 1e-05, 21, 134.44, 33.18, 0.99999, 2, 22, -16.64, 36.85, 0.04826, 21, 156.08, 29.24, 0.95174, 2, 22, -3.5, 30.31, 0.23677, 21, 170.64, 27.43, 0.76323, 2, 22, 7.75, 25.44, 0.62598, 21, 182.87, 26.57, 0.37402, 2, 22, 18.12, 28.04, 0.87148, 21, 191.79, 32.46, 0.12852, 2, 22, 35.22, 32.48, 0.98979, 21, 206.45, 42.33, 0.01021, 1, 22, 91.27, 39.43, 1, 1, 22, 106.35, 39.24, 1, 1, 22, 114.19, 35.43, 1, 1, 22, 142.28, 3.76, 1, 2, 23, -7.81, 56.1, 0.11786, 22, 144.24, -15.69, 0.88214, 2, 23, 5.52, 35.76, 0.58265, 22, 128.66, -34.35, 0.41735, 3, 19, 194.41, -37.24, 0.07017, 23, 36.85, 28.73, 0.9103, 22, 131.04, -66.38, 0.01953, 3, 19, 184.75, -27.2, 0.36204, 23, 50.37, 25.36, 0.63706, 22, 131.75, -80.29, 0.00089, 2, 19, 175.93, -24.68, 0.68847, 23, 59.18, 27.92, 0.31153, 1, 19, 158.37, -27.94, 1, 1, 19, 50.05, -47.59, 1, 1, 19, 26.31, -47.41, 1, 2, 18, 278.54, 14.66, 0.11484, 19, -2.25, -41.13, 0.88516, 2, 18, 284.93, -2.6, 0.27596, 19, -20.61, -39.96, 0.72404, 2, 18, 277.7, -18.47, 0.43065, 19, -32.17, -26.91, 0.56935, 2, 18, 257.43, -31.88, 0.74718, 19, -36.2, -2.94, 0.25282, 1, 18, 219.09, -41.77, 1, 1, 18, 189.44, -31.83, 1, 1, 18, 179.76, -39.48, 1, 1, 18, 141.64, -36.75, 1, 1, 18, 122.61, -31.44, 1, 1, 18, 68.38, -37.38, 1, 1, 18, 57.68, -25.66, 1, 2, 17, -4.67, 60.26, 0.03143, 18, 37.61, -42.07, 0.96857, 2, 17, -6.39, 43.69, 0.14857, 18, 28.04, -28.43, 0.85143, 2, 17, 10.03, 30.9, 0.39275, 18, 7.26, -29.54, 0.60725, 1, 17, 26.45, 18.1, 1, 2, 18, 191.26, 18.95, 0.74518, 19, 37.13, 36.88, 0.25482, 1, 18, 211.48, -9.2, 1, 2, 18, 239.14, -20.63, 0.91894, 19, -18.48, 9.2, 0.08106, 1, 18, 200.74, -16.16, 1, 2, 18, 250.3, 7.3, 0.16573, 19, 2.5, -12.34, 0.83427, 2, 18, 214.6, 15.53, 0.53922, 19, 24.53, 16.94, 0.46078, 1, 19, 40.11, -6.84, 1, 3, 17, -2.08, 7.13, 0.96499, 4, 219.6, 165.71, 0.02689, 79, 56.29, 492.55, 0.00812, 3, 17, -33.54, -3.32, 0.56554, 4, 188.15, 155.26, 0.42365, 79, 24.83, 482.1, 0.0108, 4, 17, -65.83, -10.22, 0.25246, 18, 37.57, 51.25, 0.16969, 4, 155.85, 148.36, 0.56772, 79, -7.46, 475.2, 0.01012, 3, 17, -23.25, 21.95, 0.42738, 18, 26.52, -0.96, 0.56324, 79, 35.12, 507.37, 0.00938, 2, 18, 52.54, -0.05, 0.99068, 79, 14.88, 523.75, 0.00932, 1, 18, 177.72, -8.2, 1, 3, 17, -74.62, 9.51, 0.0098, 18, 57.14, 42.12, 0.97991, 79, -16.25, 494.93, 0.01029, 2, 18, 173.9, 27.05, 0.80072, 19, 51.59, 49.44, 0.19928, 3, 18, 147.29, 29.85, 0.94122, 19, 64.95, 72.62, 0.05453, 79, -76.27, 563.31, 0.00425, 2, 18, 147.33, -6.17, 0.99516, 79, -52.68, 590.53, 0.00484, 3, 16, 1.58, -12.09, 0.90096, 4, 181.46, -152.7, 0.09379, 79, 18.14, 174.14, 0.00525, 4, 16, -42.99, 6.43, 0.13164, 20, 34.58, -18.21, 0.19587, 4, 136.89, -134.18, 0.65862, 79, -26.43, 192.66, 0.01388, 3, 16, -21.66, -0.73, 0.63078, 4, 158.21, -141.34, 0.35832, 79, -5.1, 185.5, 0.0109, 4, 16, -74.23, 13.91, 0.02313, 20, 64.06, -30.98, 0.49594, 4, 105.64, -126.7, 0.46829, 79, -57.67, 200.14, 0.01264, 2, 21, 32.26, -53.01, 0.17274, 20, 170.39, -27.13, 0.82726, 2, 21, 28.45, -40.44, 0.26859, 20, 183.25, -24.52, 0.73141, 1, 20, 191.34, 8.66, 1, 2, 21, 14.09, -22.31, 0.28234, 20, 202.66, -11.93, 0.71766, 2, 21, 3.05, 14.76, 0.83461, 20, 240.61, -4.43, 0.16539, 2, 21, 37.11, -13.89, 0.8109, 20, 208.88, -35.64, 0.1891, 1, 21, 30.93, 20.25, 1, 1, 21, 76.71, 4.89, 1, 1, 20, 171.68, 6.05, 1, 2, 20, 63.73, 1.93, 0.98637, 79, -51.65, 167.78, 0.01363, 3, 16, -44.73, -19.4, 0.07054, 20, 40.77, 6.93, 0.91704, 79, -28.17, 166.83, 0.01242, 3, 16, -23.4, -18.59, 0.48608, 20, 19.62, 9.82, 0.50304, 79, -6.84, 167.64, 0.01088, 2, 20, 146.94, 5.11, 0.99523, 79, -133.06, 150.25, 0.00477, 3, 21, 34.99, -75.35, 0.05531, 20, 147.89, -27.75, 0.93973, 79, -139.68, 182.45, 0.00496], "hull": 86, "edges": [0, 170, 4, 6, 6, 8, 8, 10, 26, 28, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 68, 70, 70, 72, 72, 74, 74, 76, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 118, 120, 120, 122, 122, 124, 124, 126, 136, 138, 138, 140, 144, 146, 148, 150, 152, 154, 160, 162, 162, 164, 126, 128, 116, 118, 114, 116, 110, 112, 112, 114, 106, 108, 108, 110, 42, 44, 44, 46, 46, 48, 54, 56, 60, 62, 66, 68, 62, 64, 64, 66, 76, 78, 78, 80, 80, 82, 0, 2, 2, 4, 166, 168, 168, 170, 164, 166, 158, 160, 154, 156, 156, 158, 150, 152, 146, 148, 140, 142, 142, 144, 128, 130, 130, 132, 132, 134, 134, 136, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 24, 172, 172, 174, 174, 176, 198, 202, 202, 200, 194, 204, 204, 196, 14, 16, 10, 12, 12, 14, 54, 216, 216, 218, 220, 222, 220, 54, 230, 238, 238, 232, 212, 240, 240, 214, 58, 60, 56, 58], "width": 579, "height": 388}}, "arm2": {"arm": {"type": "mesh", "uvs": [0.03901, 0.52514, 0.08441, 0.48827, 0.14417, 0.49342, 0.19138, 0.52938, 0.20499, 0.54286, 0.26676, 0.58759, 0.3467, 0.62121, 0.37166, 0.62812, 0.39417, 0.63564, 0.4193, 0.66381, 0.47604, 0.72776, 0.56223, 0.7243, 0.5985, 0.73637, 0.62946, 0.72417, 0.66111, 0.72389, 0.69276, 0.7236, 0.73044, 0.71376, 0.82199, 0.67092, 0.84273, 0.65843, 0.88588, 0.63989, 0.94483, 0.64258, 0.99949, 0.72053, 0.9885, 0.78924, 0.96195, 0.85434, 0.91301, 0.8702, 0.71854, 0.85956, 0.68064, 0.85584, 0.65529, 0.8555, 0.63414, 0.85688, 0.62001, 0.8746, 0.59679, 0.90418, 0.51499, 0.98346, 0.49142, 1, 0.47638, 1, 0.40882, 0.95828, 0.39109, 0.9154, 0.4012, 0.85457, 0.37331, 0.78303, 0.3617, 0.75158, 0.34811, 0.73941, 0.31736, 0.73577, 0.12788, 0.7123, 0.08838, 0.69599, 0.04355, 0.66128, 0.01344, 0.64609, 0, 0.60588, 0.00402, 0.54351, 0.06437, 0.59279, 0.11421, 0.53462, 0.12955, 0.60423, 0.91409, 0.6821, 0.94034, 0.77378, 0.87625, 0.71043, 0.89358, 0.79603, 0.81202, 0.77016], "triangles": [50, 19, 20, 18, 19, 50, 21, 51, 50, 21, 50, 20, 52, 18, 50, 54, 16, 17, 17, 18, 52, 54, 17, 52, 52, 50, 51, 22, 51, 21, 53, 52, 51, 54, 52, 53, 23, 51, 22, 27, 13, 14, 26, 14, 15, 27, 14, 26, 28, 13, 27, 25, 15, 16, 25, 16, 54, 26, 15, 25, 24, 53, 51, 24, 51, 23, 54, 53, 24, 25, 54, 24, 12, 13, 28, 29, 12, 28, 30, 11, 12, 30, 12, 29, 36, 33, 34, 35, 36, 34, 30, 31, 10, 30, 10, 11, 31, 36, 10, 31, 33, 36, 32, 33, 31, 38, 8, 9, 37, 38, 9, 37, 9, 10, 36, 37, 10, 3, 49, 48, 47, 48, 49, 46, 47, 45, 44, 45, 47, 43, 44, 47, 42, 47, 49, 43, 47, 42, 41, 42, 49, 40, 5, 6, 41, 49, 4, 49, 3, 4, 41, 4, 5, 40, 41, 5, 39, 6, 7, 40, 6, 39, 8, 38, 39, 8, 39, 7, 48, 1, 2, 47, 0, 1, 47, 1, 48, 3, 48, 2, 47, 46, 0], "vertices": [2, 18, 239.14, -20.63, 0.91894, 19, -18.48, 9.2, 0.08106, 1, 18, 211.48, -9.2, 1, 2, 18, 191.26, 18.95, 0.74518, 19, 37.13, 36.88, 0.25482, 2, 18, 184.9, 48.97, 0.37656, 19, 67.15, 30.49, 0.62344, 2, 18, 184.01, 58.39, 0.16521, 19, 76.12, 27.48, 0.83479, 1, 19, 115.16, 20, 1, 1, 19, 163.25, 19.4, 1, 2, 19, 177.9, 20.55, 0.88967, 23, 81.57, -11.44, 0.11033, 2, 19, 191.24, 21.12, 0.59063, 23, 70.57, -19.01, 0.40937, 2, 19, 208.14, 14.34, 0.11398, 23, 52.66, -22.25, 0.88602, 2, 23, 12.13, -29.47, 0.8783, 22, 68.17, -59.65, 0.1217, 3, 23, -20.82, -66.97, 0.16164, 22, 22.7, -39.03, 0.83253, 21, 218.37, -29.29, 0.00582, 3, 23, -38.54, -79.19, 0.03417, 22, 5.86, -25.64, 0.78338, 21, 198.04, -22.24, 0.18245, 3, 23, -47.26, -95.55, 0.00327, 22, -12.33, -22.05, 0.28473, 21, 179.69, -24.9, 0.71201, 2, 22, -28.86, -14.14, 0.01301, 21, 161.47, -22.92, 0.98699, 1, 21, 143.25, -20.94, 1, 1, 21, 121.14, -22.24, 1, 2, 21, 66.58, -32.7, 0.84588, 20, 187.38, -63.21, 0.15412, 2, 21, 54.09, -36.15, 0.58653, 20, 185.12, -50.45, 0.41347, 2, 21, 28.45, -40.44, 0.26859, 20, 183.25, -24.52, 0.73141, 1, 20, 191.34, 8.66, 1, 2, 21, -33.33, -1.85, 0.14939, 20, 227.48, 33.36, 0.85061, 2, 21, -23.97, 23.9, 0.58241, 20, 252.25, 21.61, 0.41759, 2, 21, -5.81, 47.24, 0.89129, 20, 273.78, 1.34, 0.10871, 2, 21, 23.04, 50.12, 0.99171, 20, 273.93, -27.65, 0.00829, 2, 22, -35.74, 47.74, 1e-05, 21, 134.44, 33.18, 0.99999, 2, 22, -16.64, 36.85, 0.04826, 21, 156.08, 29.24, 0.95174, 2, 22, -3.5, 30.31, 0.23677, 21, 170.64, 27.43, 0.76323, 2, 22, 7.75, 25.44, 0.62598, 21, 182.87, 26.57, 0.37402, 2, 22, 18.12, 28.04, 0.87148, 21, 191.79, 32.46, 0.12852, 2, 22, 35.22, 32.48, 0.98979, 21, 206.45, 42.33, 0.01021, 1, 22, 91.27, 39.43, 1, 1, 22, 106.35, 39.24, 1, 1, 22, 114.19, 35.43, 1, 1, 22, 142.28, 3.76, 1, 2, 23, -7.81, 56.1, 0.11786, 22, 144.24, -15.69, 0.88214, 2, 23, 5.52, 35.76, 0.58265, 22, 128.66, -34.35, 0.41735, 3, 19, 194.41, -37.24, 0.07017, 23, 36.85, 28.73, 0.9103, 22, 131.04, -66.38, 0.01953, 3, 19, 184.75, -27.2, 0.36204, 23, 50.37, 25.36, 0.63706, 22, 131.75, -80.29, 0.00089, 2, 19, 175.93, -24.68, 0.68847, 23, 59.18, 27.92, 0.31153, 1, 19, 158.37, -27.94, 1, 1, 19, 50.05, -47.59, 1, 1, 19, 26.31, -47.41, 1, 2, 18, 278.54, 14.66, 0.11484, 19, -2.25, -41.13, 0.88516, 2, 18, 284.93, -2.6, 0.27596, 19, -20.61, -39.96, 0.72404, 2, 18, 277.7, -18.47, 0.43065, 19, -32.17, -26.91, 0.56935, 2, 18, 257.43, -31.88, 0.74718, 19, -36.2, -2.94, 0.25282, 2, 18, 250.3, 7.3, 0.16573, 19, 2.5, -12.34, 0.83427, 2, 18, 214.6, 15.53, 0.53922, 19, 24.53, 16.94, 0.46078, 1, 19, 40.11, -6.84, 1, 2, 21, 14.09, -22.31, 0.28234, 20, 202.66, -11.93, 0.71766, 2, 21, 3.05, 14.76, 0.83461, 20, 240.61, -4.43, 0.16539, 2, 21, 37.11, -13.89, 0.8109, 20, 208.88, -35.64, 0.1891, 1, 21, 30.93, 20.25, 1, 1, 21, 76.71, 4.89, 1], "hull": 47, "edges": [8, 10, 20, 22, 22, 24, 30, 32, 32, 34, 34, 36, 42, 44, 44, 46, 46, 48, 48, 50, 62, 64, 64, 66, 66, 68, 68, 70, 80, 82, 82, 84, 88, 90, 70, 72, 60, 62, 58, 60, 54, 56, 56, 58, 50, 52, 52, 54, 24, 26, 26, 28, 28, 30, 90, 92, 84, 86, 86, 88, 72, 74, 74, 76, 76, 78, 78, 80, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 6, 8, 6, 4, 4, 2, 2, 0, 36, 38, 38, 40, 100, 102, 100, 36, 40, 42, 0, 92], "width": 579, "height": 388}}, "body": {"body": {"type": "mesh", "uvs": [0.45679, 0.00492, 0.49612, 0.00059, 0.52894, 0.00108, 0.54914, 0.00561, 0.56415, 0.00212, 0.60377, 0.00304, 0.63956, 0.00754, 0.68005, 0.01853, 0.70596, 0.0292, 0.72663, 0.04417, 0.73877, 0.06121, 0.74977, 0.0774, 0.75982, 0.09434, 0.77123, 0.10733, 0.78491, 0.12109, 0.81677, 0.12871, 0.8451, 0.1388, 0.83926, 0.15313, 0.85548, 0.16901, 0.8272, 0.17911, 0.80779, 0.17961, 0.79643, 0.18361, 0.77805, 0.19552, 0.76565, 0.20927, 0.80422, 0.23069, 0.81474, 0.24627, 0.80692, 0.26197, 0.7837, 0.27496, 0.74334, 0.28384, 0.73721, 0.29225, 0.7377, 0.30552, 0.73589, 0.32116, 0.77397, 0.34504, 0.82397, 0.35842, 0.88338, 0.37862, 0.93079, 0.39997, 0.96572, 0.42362, 0.98842, 0.44837, 0.99689, 0.47171, 0.99999, 0.49802, 0.94909, 0.54977, 0.88238, 0.60767, 0.84732, 0.65102, 0.83511, 0.6685, 0.82678, 0.68636, 0.82941, 0.70095, 0.83463, 0.71535, 0.84379, 0.75538, 0.81611, 0.78653, 0.76464, 0.83964, 0.74274, 0.86583, 0.73365, 0.87669, 0.72871, 0.88472, 0.72878, 0.89461, 0.72885, 0.90511, 0.72893, 0.91561, 0.73523, 0.94142, 0.74114, 0.95764, 0.75279, 0.97625, 0.75998, 0.98774, 0.74789, 0.99952, 0.61958, 0.99994, 0.59542, 0.99548, 0.58562, 0.98484, 0.59275, 0.97034, 0.60102, 0.95466, 0.6011, 0.9403, 0.59706, 0.92083, 0.61269, 0.90482, 0.61562, 0.89771, 0.62748, 0.88941, 0.63582, 0.88207, 0.6394, 0.87416, 0.63939, 0.86316, 0.63936, 0.82272, 0.62161, 0.77726, 0.62261, 0.7528, 0.64798, 0.71061, 0.65064, 0.69878, 0.64188, 0.68279, 0.6357, 0.66681, 0.63752, 0.64851, 0.64193, 0.63021, 0.64594, 0.6044, 0.63087, 0.55141, 0.62788, 0.53451, 0.62458, 0.51803, 0.59892, 0.51799, 0.58878, 0.53329, 0.57421, 0.54879, 0.52339, 0.58976, 0.49282, 0.61422, 0.45567, 0.64899, 0.44361, 0.66434, 0.43312, 0.67836, 0.41642, 0.69176, 0.39522, 0.70477, 0.38506, 0.71348, 0.36768, 0.76422, 0.35178, 0.78587, 0.29144, 0.81884, 0.2322, 0.86758, 0.22334, 0.87756, 0.21792, 0.88828, 0.21954, 0.89767, 0.23921, 0.90452, 0.25939, 0.91636, 0.25995, 0.92825, 0.22626, 0.94769, 0.21882, 0.95494, 0.20831, 0.99124, 0.18888, 0.99166, 0.18856, 0.96229, 0.17779, 0.97245, 0.17903, 0.98708, 0.17627, 0.99511, 0.16956, 0.99975, 0.00217, 0.99945, 0, 0.99669, 0.01962, 0.98874, 0.04409, 0.98088, 0.06114, 0.97136, 0.06338, 0.96309, 0.0648, 0.95229, 0.06907, 0.93889, 0.08154, 0.9156, 0.08678, 0.90146, 0.09755, 0.88884, 0.10389, 0.87721, 0.10843, 0.86422, 0.1232, 0.78596, 0.13949, 0.74382, 0.19587, 0.70441, 0.21529, 0.69171, 0.22522, 0.6803, 0.23218, 0.66501, 0.24121, 0.6487, 0.24025, 0.63649, 0.21962, 0.57492, 0.22069, 0.52107, 0.23036, 0.49325, 0.24999, 0.46664, 0.27773, 0.44572, 0.28849, 0.42964, 0.30017, 0.40624, 0.31326, 0.38227, 0.33009, 0.36032, 0.34008, 0.33338, 0.31925, 0.31747, 0.29619, 0.30156, 0.27652, 0.28276, 0.23931, 0.27379, 0.21246, 0.2606, 0.19909, 0.24413, 0.20425, 0.22738, 0.2318, 0.20413, 0.21649, 0.1866, 0.19555, 0.1692, 0.17514, 0.15332, 0.1986, 0.13867, 0.21007, 0.12265, 0.23873, 0.11598, 0.27666, 0.1102, 0.30561, 0.09891, 0.3234, 0.08853, 0.34012, 0.07579, 0.35164, 0.06147, 0.35785, 0.04983, 0.37148, 0.03705, 0.39539, 0.02388, 0.42416, 0.01287, 0.20188, 0.15326, 0.22562, 0.15693, 0.26335, 0.15771, 0.33488, 0.15296, 0.79862, 0.17366, 0.72882, 0.16642, 0.56363, 0.02706, 0.55919, 0.03964, 0.5616, 0.01427, 0.42446, 0.14561, 0.43772, 0.13339, 0.43794, 0.12137, 0.43629, 0.0922, 0.437, 0.07757, 0.43736, 0.10783, 0.58526, 0.15087, 0.58878, 0.1365, 0.59684, 0.12573, 0.63914, 0.07884, 0.62418, 0.09552, 0.61017, 0.11203, 0.53796, 0.17296, 0.6314, 0.17319, 0.48301, 0.17227, 0.3888, 0.16949, 0.47398, 0.154, 0.47242, 0.13757, 0.47398, 0.12275, 0.47553, 0.10863, 0.47943, 0.09336, 0.4841, 0.07878, 0.58143, 0.07924, 0.57442, 0.09521, 0.56741, 0.10979, 0.55963, 0.12484, 0.55262, 0.13919, 0.54328, 0.15516, 0.37785, 0.18405, 0.44325, 0.18937, 0.51489, 0.21854, 0.49348, 0.2021, 0.52657, 0.2028, 0.56784, 0.19053, 0.63339, 0.18727, 0.71164, 0.19158, 0.292, 0.18863, 0.72465, 0.17854, 0.27925, 0.16958, 0.38938, 0.14188, 0.39972, 0.15484, 0.65359, 0.15045, 0.61512, 0.16188, 0.53209, 0.00996, 0.49017, 0.00965, 0.44927, 0.01735, 0.4208, 0.02966, 0.40476, 0.04197, 0.3913, 0.0572, 0.38405, 0.07468, 0.37111, 0.08976, 0.35724, 0.10724, 0.3248, 0.12441, 0.28067, 0.13405, 0.24303, 0.14466, 0.38886, 0.1281, 0.44013, 0.06167, 0.45959, 0.04701, 0.48166, 0.03409, 0.51865, 0.02753, 0.59977, 0.01267, 0.63611, 0.01692, 0.66661, 0.03139, 0.68348, 0.04682, 0.69062, 0.06244, 0.69539, 0.07854, 0.69474, 0.09455, 0.69798, 0.1123, 0.71875, 0.13004, 0.7473, 0.14586, 0.78948, 0.15937, 0.65905, 0.13064, 0.64349, 0.06199, 0.64155, 0.04521, 0.61299, 0.02939, 0.34468, 0.28731, 0.4319, 0.27937, 0.51581, 0.27364, 0.59847, 0.2789, 0.48768, 0.26278, 0.51911, 0.2462, 0.54268, 0.26185, 0.67075, 0.28637, 0.37717, 0.29879, 0.39464, 0.31531, 0.41171, 0.3353, 0.49163, 0.33501, 0.5863, 0.33245, 0.66641, 0.32733, 0.66854, 0.30673, 0.46496, 0.29531, 0.48216, 0.31477, 0.57426, 0.29185, 0.57757, 0.31193, 0.73594, 0.24909, 0.28828, 0.24873, 0.68975, 0.22705, 0.74379, 0.22729, 0.7759, 0.23866, 0.65996, 0.23849, 0.65903, 0.2553, 0.69789, 0.26718, 0.74737, 0.26611, 0.77407, 0.25487, 0.63218, 0.20821, 0.70562, 0.20691, 0.76547, 0.21584, 0.5897, 0.22484, 0.58418, 0.24946, 0.61939, 0.26849, 0.68256, 0.27822, 0.7481, 0.27617, 0.30532, 0.2036, 0.38657, 0.2072, 0.43275, 0.22555, 0.43843, 0.25008, 0.40863, 0.26939, 0.33717, 0.2785, 0.26131, 0.27433, 0.2762, 0.22712, 0.3283, 0.22777, 0.2419, 0.23873, 0.24117, 0.25593, 0.2743, 0.2667, 0.32438, 0.26739, 0.358, 0.25698, 0.36047, 0.23972, 0.24488, 0.21297, 0.29394, 0.5666, 0.28787, 0.63693, 0.27498, 0.65024, 0.26512, 0.66647, 0.25905, 0.68315, 0.2454, 0.69915, 0.30732, 0.70351, 0.32476, 0.69292, 0.34826, 0.67601, 0.36419, 0.65843, 0.37784, 0.6413, 0.19037, 0.74634, 0.16836, 0.83645, 0.27532, 0.7552, 0.44254, 0.57413, 0.37694, 0.56696, 0.30964, 0.5124, 0.41072, 0.51154, 0.51468, 0.51183, 0.60324, 0.50869, 0.62371, 0.50848, 0.71469, 0.50767, 0.7071, 0.55192, 0.69919, 0.63692, 0.69226, 0.65236, 0.68995, 0.67124, 0.69688, 0.68874, 0.70689, 0.70799, 0.68753, 0.75533, 0.70526, 0.84321, 0.7879, 0.757, 0.77161, 0.707, 0.77115, 0.68958, 0.77047, 0.6737, 0.7785, 0.65377, 0.78255, 0.63788, 0.86462, 0.55337, 0.50185, 0.52671, 0.47976, 0.54852, 0.39137, 0.53906, 0.29768, 0.53906, 0.71136, 0.52769, 0.87755, 0.52802, 0.88435, 0.50374, 0.80003, 0.506, 0.79595, 0.52781, 0.78408, 0.55282, 0.40429, 0.36198, 0.39372, 0.38503, 0.39687, 0.40915, 0.43164, 0.43449, 0.42532, 0.485, 0.43359, 0.4597, 0.48653, 0.3616, 0.48536, 0.3836, 0.49828, 0.40945, 0.52176, 0.4337, 0.65445, 0.42814, 0.64514, 0.40351, 0.62987, 0.37836, 0.6099, 0.35706, 0.70036, 0.35112, 0.73209, 0.36869, 0.7507, 0.39281, 0.77298, 0.41866, 0.78869, 0.44819, 0.7936, 0.47408, 0.66268, 0.4527, 0.66877, 0.48042, 0.53675, 0.45737, 0.53418, 0.48343, 0.34009, 0.45943, 0.31891, 0.48619, 0.36454, 0.43454, 0.81328, 0.3866, 0.86399, 0.41345, 0.8825, 0.44087, 0.88706, 0.47083, 0.67678, 0.9854, 0.67397, 0.9735, 0.67324, 0.95658, 0.67297, 0.94208], "triangles": [117, 119, 116, 119, 120, 116, 116, 120, 115, 117, 118, 119, 115, 120, 114, 121, 114, 120, 61, 386, 60, 386, 61, 63, 60, 386, 59, 61, 62, 63, 387, 386, 63, 386, 58, 59, 386, 387, 58, 316, 317, 94, 94, 317, 93, 316, 311, 317, 135, 136, 311, 311, 310, 317, 311, 136, 310, 93, 317, 92, 310, 309, 317, 317, 318, 92, 317, 309, 318, 309, 310, 137, 92, 318, 91, 309, 323, 318, 309, 308, 323, 310, 136, 137, 318, 322, 91, 318, 323, 322, 308, 309, 138, 309, 137, 138, 91, 322, 90, 322, 346, 90, 90, 346, 89, 308, 138, 348, 322, 323, 346, 308, 348, 323, 323, 347, 346, 323, 348, 347, 348, 138, 139, 346, 345, 89, 89, 345, 88, 346, 347, 345, 348, 324, 347, 347, 325, 345, 347, 324, 325, 348, 139, 324, 88, 345, 87, 87, 345, 326, 345, 325, 326, 139, 140, 324, 87, 326, 327, 324, 380, 325, 324, 140, 380, 325, 359, 326, 326, 378, 327, 326, 359, 378, 325, 380, 359, 376, 327, 378, 140, 141, 380, 380, 379, 359, 380, 141, 379, 359, 379, 360, 359, 360, 378, 360, 381, 358, 381, 360, 379, 378, 377, 376, 378, 360, 377, 141, 142, 379, 360, 364, 377, 360, 358, 364, 379, 142, 381, 142, 143, 381, 143, 144, 381, 381, 357, 358, 358, 363, 364, 127, 128, 103, 103, 128, 102, 128, 129, 102, 102, 129, 101, 129, 320, 101, 101, 320, 100, 129, 130, 320, 320, 321, 99, 319, 320, 130, 320, 99, 100, 319, 321, 320, 130, 131, 319, 99, 321, 98, 98, 321, 97, 321, 319, 314, 321, 314, 97, 314, 319, 313, 319, 132, 313, 319, 131, 132, 97, 314, 96, 314, 315, 96, 96, 315, 95, 132, 133, 313, 315, 314, 312, 314, 313, 312, 313, 133, 312, 315, 316, 95, 315, 312, 316, 95, 316, 94, 133, 134, 312, 312, 311, 316, 312, 134, 311, 134, 135, 311, 114, 121, 113, 109, 110, 111, 111, 112, 109, 121, 122, 113, 113, 122, 112, 122, 123, 112, 108, 109, 112, 112, 123, 124, 108, 112, 124, 108, 124, 125, 108, 125, 107, 107, 125, 106, 126, 127, 104, 127, 103, 104, 106, 125, 105, 126, 105, 125, 105, 126, 104, 387, 63, 64, 387, 64, 388, 388, 64, 65, 387, 57, 58, 387, 388, 57, 388, 56, 57, 65, 389, 388, 388, 389, 56, 65, 66, 389, 55, 389, 67, 389, 55, 56, 55, 68, 54, 68, 55, 67, 68, 69, 54, 389, 66, 67, 54, 70, 53, 54, 69, 70, 53, 71, 52, 53, 70, 71, 71, 72, 52, 52, 72, 51, 50, 51, 73, 51, 72, 73, 73, 337, 50, 50, 337, 49, 73, 74, 337, 49, 337, 48, 48, 337, 338, 336, 338, 337, 74, 75, 336, 337, 74, 336, 48, 338, 47, 75, 76, 336, 47, 338, 46, 46, 338, 339, 335, 339, 338, 338, 336, 335, 76, 77, 336, 336, 77, 335, 339, 45, 46, 77, 78, 335, 78, 334, 335, 335, 340, 339, 335, 334, 340, 339, 340, 45, 340, 44, 45, 78, 79, 334, 334, 341, 340, 340, 341, 44, 79, 333, 334, 334, 333, 341, 79, 80, 333, 44, 341, 43, 341, 342, 43, 341, 333, 342, 333, 332, 342, 333, 80, 332, 43, 342, 42, 80, 81, 332, 332, 331, 342, 342, 343, 42, 342, 331, 343, 332, 81, 331, 42, 343, 41, 81, 82, 331, 344, 41, 343, 82, 83, 331, 354, 343, 331, 344, 343, 354, 330, 354, 331, 331, 83, 330, 41, 344, 40, 83, 84, 330, 354, 353, 344, 344, 350, 40, 344, 353, 350, 330, 349, 354, 354, 349, 353, 349, 330, 85, 330, 84, 85, 40, 350, 39, 39, 350, 351, 85, 86, 349, 353, 352, 350, 350, 352, 351, 349, 329, 353, 353, 329, 352, 349, 86, 329, 87, 327, 86, 327, 328, 86, 86, 328, 329, 328, 327, 376, 328, 376, 329, 352, 329, 374, 329, 376, 374, 352, 374, 351, 39, 385, 38, 39, 351, 385, 351, 374, 385, 377, 375, 376, 376, 375, 374, 375, 373, 374, 374, 373, 385, 385, 37, 38, 373, 384, 385, 385, 384, 37, 375, 365, 373, 384, 36, 37, 365, 372, 373, 384, 372, 383, 384, 373, 372, 384, 383, 36, 383, 35, 36, 383, 34, 35, 381, 144, 357, 356, 144, 145, 144, 356, 357, 358, 357, 363, 357, 362, 363, 357, 356, 362, 145, 146, 356, 356, 355, 362, 356, 146, 355, 355, 146, 147, 366, 371, 372, 372, 382, 383, 372, 371, 382, 383, 382, 34, 367, 370, 371, 371, 370, 382, 382, 33, 34, 382, 370, 33, 370, 32, 33, 370, 369, 32, 369, 31, 32, 375, 377, 365, 377, 364, 365, 365, 364, 366, 364, 363, 366, 365, 366, 372, 363, 362, 367, 363, 367, 366, 368, 362, 361, 362, 368, 367, 366, 367, 371, 362, 355, 361, 367, 369, 370, 367, 368, 369, 361, 355, 265, 361, 267, 368, 368, 268, 369, 216, 156, 218, 218, 172, 173, 157, 172, 218, 218, 156, 157, 218, 173, 174, 157, 171, 172, 157, 158, 171, 172, 234, 173, 174, 173, 233, 172, 171, 234, 158, 159, 171, 171, 159, 234, 173, 234, 233, 233, 232, 174, 233, 234, 160, 234, 159, 160, 160, 161, 233, 161, 162, 233, 233, 162, 232, 215, 217, 22, 22, 217, 21, 21, 175, 20, 21, 217, 175, 20, 175, 19, 19, 175, 18, 217, 176, 175, 176, 250, 175, 175, 250, 18, 176, 221, 249, 250, 17, 18, 176, 249, 250, 250, 249, 17, 17, 249, 16, 16, 249, 15, 249, 14, 15, 249, 248, 14, 361, 266, 267, 265, 355, 147, 361, 265, 266, 368, 267, 268, 369, 268, 31, 147, 264, 265, 265, 271, 266, 265, 264, 271, 266, 273, 267, 266, 271, 273, 147, 148, 264, 267, 273, 268, 268, 269, 31, 268, 273, 269, 31, 269, 30, 148, 263, 264, 148, 149, 263, 264, 270, 271, 264, 263, 270, 273, 271, 272, 271, 270, 272, 273, 272, 269, 269, 29, 30, 269, 272, 262, 269, 262, 29, 262, 272, 258, 149, 255, 263, 149, 150, 255, 263, 256, 270, 263, 255, 256, 270, 257, 272, 270, 256, 257, 29, 262, 28, 272, 257, 258, 150, 297, 255, 255, 296, 256, 255, 297, 296, 262, 290, 28, 258, 289, 262, 262, 289, 290, 28, 291, 27, 28, 290, 291, 151, 298, 150, 150, 298, 297, 256, 259, 257, 256, 296, 259, 257, 261, 258, 258, 261, 289, 297, 298, 304, 298, 303, 304, 297, 305, 296, 297, 304, 305, 290, 281, 291, 290, 289, 281, 281, 282, 291, 27, 291, 26, 26, 291, 282, 302, 303, 152, 152, 303, 298, 298, 151, 152, 257, 259, 261, 296, 295, 259, 296, 305, 295, 261, 288, 289, 289, 280, 281, 289, 288, 280, 305, 304, 275, 282, 281, 274, 304, 303, 275, 303, 302, 275, 281, 280, 274, 282, 283, 26, 282, 274, 283, 259, 260, 261, 259, 295, 260, 26, 283, 25, 261, 260, 288, 152, 153, 302, 305, 306, 295, 305, 275, 306, 302, 301, 275, 302, 153, 301, 280, 279, 274, 280, 288, 279, 283, 278, 25, 283, 274, 278, 306, 294, 295, 295, 294, 260, 288, 287, 279, 288, 260, 287, 279, 276, 274, 274, 277, 278, 274, 276, 277, 301, 299, 275, 275, 300, 306, 275, 299, 300, 278, 24, 25, 294, 210, 260, 260, 210, 287, 153, 154, 301, 306, 300, 294, 301, 154, 299, 278, 277, 24, 279, 287, 276, 277, 286, 24, 286, 23, 24, 299, 292, 300, 300, 293, 294, 300, 292, 293, 154, 307, 299, 154, 155, 307, 286, 277, 285, 299, 307, 292, 287, 284, 276, 277, 276, 285, 276, 284, 285, 294, 211, 210, 211, 293, 209, 211, 294, 293, 210, 212, 287, 287, 212, 284, 284, 212, 213, 210, 211, 212, 286, 285, 23, 307, 155, 292, 22, 23, 215, 284, 214, 285, 284, 213, 214, 292, 208, 293, 293, 208, 209, 23, 285, 215, 285, 214, 215, 216, 155, 156, 155, 216, 292, 292, 216, 208, 213, 212, 192, 209, 194, 211, 212, 211, 192, 211, 194, 192, 215, 214, 217, 213, 193, 214, 213, 192, 193, 208, 195, 209, 209, 195, 194, 216, 218, 208, 214, 193, 217, 208, 218, 195, 217, 193, 176, 176, 193, 221, 193, 192, 222, 222, 192, 207, 207, 186, 222, 193, 222, 221, 192, 194, 207, 195, 220, 194, 194, 220, 196, 220, 180, 196, 194, 196, 207, 218, 174, 195, 195, 174, 220, 222, 186, 221, 174, 219, 220, 248, 249, 221, 248, 221, 251, 207, 206, 186, 206, 207, 197, 220, 219, 180, 180, 197, 196, 207, 196, 197, 219, 232, 235, 219, 174, 232, 186, 187, 221, 186, 206, 187, 221, 187, 251, 180, 181, 197, 180, 219, 181, 219, 235, 181, 206, 205, 187, 205, 206, 198, 206, 197, 198, 197, 181, 198, 187, 188, 251, 187, 205, 188, 181, 182, 198, 181, 235, 182, 188, 191, 251, 232, 231, 235, 235, 231, 185, 235, 185, 182, 188, 205, 204, 232, 162, 231, 205, 198, 199, 198, 182, 199, 251, 191, 247, 251, 247, 248, 247, 190, 246, 190, 247, 191, 14, 247, 13, 14, 248, 247, 185, 230, 183, 230, 185, 231, 191, 188, 204, 204, 205, 199, 231, 162, 163, 182, 185, 199, 247, 246, 12, 247, 12, 13, 11, 246, 245, 246, 11, 12, 204, 203, 191, 191, 203, 190, 199, 200, 204, 204, 200, 203, 199, 185, 200, 185, 183, 200, 163, 164, 231, 231, 164, 230, 203, 202, 190, 190, 189, 246, 190, 202, 189, 200, 201, 203, 203, 201, 202, 246, 189, 245, 245, 189, 244, 200, 183, 201, 183, 230, 184, 183, 184, 201, 184, 230, 229, 164, 165, 230, 230, 165, 229, 189, 202, 252, 189, 252, 244, 178, 252, 202, 252, 178, 253, 201, 178, 202, 184, 236, 201, 236, 237, 201, 201, 237, 178, 178, 238, 239, 238, 178, 237, 245, 10, 11, 245, 244, 10, 184, 229, 236, 165, 166, 229, 229, 228, 236, 229, 166, 228, 252, 243, 244, 244, 9, 10, 244, 243, 9, 252, 253, 243, 237, 236, 227, 166, 167, 228, 236, 228, 227, 228, 167, 227, 167, 168, 227, 227, 226, 237, 237, 226, 238, 253, 242, 243, 243, 8, 9, 243, 242, 8, 178, 254, 253, 253, 254, 242, 227, 168, 226, 178, 177, 254, 178, 239, 177, 168, 169, 226, 226, 225, 238, 238, 225, 239, 254, 241, 242, 242, 7, 8, 242, 241, 7, 226, 170, 225, 226, 169, 170, 177, 240, 254, 254, 240, 241, 225, 224, 239, 239, 224, 223, 239, 179, 177, 239, 223, 179, 223, 1, 2, 1, 223, 224, 177, 179, 240, 241, 6, 7, 225, 0, 224, 225, 170, 0, 241, 240, 6, 223, 3, 179, 179, 4, 240, 179, 3, 4, 240, 5, 6, 240, 4, 5, 223, 2, 3, 224, 0, 1], "vertices": [2, 6, 189.23, 50.71, 1, 9, -699.74, 157.81, 0, 2, 6, 198.66, 30.59, 1, 9, -703.37, 179.74, 0, 2, 6, 199.26, 13.18, 1, 9, -699.22, 196.65, 0, 2, 6, 192.1, 1.84, 1, 9, -689.25, 205.63, 0, 1, 6, 198.96, -5.57, 1, 1, 6, 199.09, -26.63, 1, 1, 6, 192.67, -46.2, 1, 1, 6, 174.93, -69.23, 1, 2, 6, 157.12, -84.5, 0.99978, 5, 240.75, -80.41, 0.00022, 2, 6, 131.45, -97.65, 0.99427, 5, 215.42, -94.22, 0.00573, 2, 6, 101.7, -106.6, 0.96365, 5, 185.91, -103.93, 0.03635, 3, 6, 73.43, -114.82, 0.87652, 5, 157.86, -112.88, 0.08786, 16, 192.66, 6.23, 0.03562, 3, 6, 43.78, -122.65, 0.69789, 5, 128.43, -121.48, 0.16195, 16, 162.3, 1.95, 0.14017, 3, 6, 21.21, -130.61, 0.53303, 5, 106.07, -130.02, 0.2218, 16, 138.94, -3.29, 0.24516, 3, 6, -2.65, -139.89, 0.32814, 5, 82.46, -139.91, 0.25134, 16, 114.16, -9.69, 0.42053, 3, 6, -14.78, -157.85, 0.15829, 5, 70.8, -158.18, 0.22518, 16, 99.99, -26.1, 0.61653, 3, 6, -31.45, -174.32, 0.05326, 5, 54.56, -175.07, 0.19031, 16, 81.5, -40.48, 0.75642, 3, 6, -57.17, -173.37, 0.05007, 5, 28.82, -174.79, 0.12457, 16, 56.07, -36.5, 0.82536, 3, 6, -84.66, -184.3, 0.02865, 5, 1.63, -186.43, 0.05407, 16, 27.48, -44.12, 0.91728, 1, 16, 10, -28.52, 1, 3, 4, 189.34, -158.82, 0.057, 16, 9.47, -18.21, 0.94057, 79, 26.03, 168.03, 0.00242, 3, 4, 182.42, -152.55, 0.09945, 16, 2.54, -11.94, 0.89506, 79, 19.1, 174.29, 0.00549, 4, 4, 161.54, -142.09, 0.34125, 16, -18.33, -1.47, 0.60073, 32, 67.01, -58.67, 0.04958, 79, -1.77, 184.76, 0.00845, 4, 4, 137.25, -134.67, 0.52781, 16, -42.62, 5.94, 0.26246, 32, 42.72, -51.25, 0.19757, 79, -26.06, 192.17, 0.01216, 4, 4, 98.39, -153.78, 0.75308, 16, -81.49, -13.17, 0.0408, 33, 16.97, -53.18, 0.19847, 79, -64.93, 173.06, 0.00765, 3, 4, 70.43, -158.39, 0.79481, 33, -10.99, -57.79, 0.1987, 79, -92.88, 168.45, 0.00648, 3, 4, 42.6, -153.28, 0.79534, 33, -38.83, -52.68, 0.19883, 79, -120.72, 173.56, 0.00583, 5, 4, 19.87, -140.18, 0.76666, 3, 91.61, -141.49, 0.04196, 33, -61.55, -39.58, 0.09983, 32, -74.65, -56.76, 0.08985, 79, -143.44, 186.66, 0.0017, 3, 4, 4.79, -118.26, 0.62682, 3, 78.91, -118.11, 0.17318, 32, -89.74, -34.84, 0.2, 3, 4, -10.09, -114.49, 0.60703, 3, 64.51, -112.8, 0.37831, 7, -72.97, 119.74, 0.01466, 4, 4, -33.74, -113.93, 0.32382, 3, 41.05, -109.76, 0.51424, 7, -49.87, 114.64, 0.10393, 8, -159.03, 2.31, 0.05801, 4, 4, -61.58, -112.01, 0.10188, 3, 13.57, -104.93, 0.5061, 7, -22.93, 107.4, 0.21622, 8, -131.83, -3.91, 0.17581, 4, 4, -104.82, -130.71, 0.00732, 3, -31.4, -118.99, 0.19757, 7, 23.11, 117.42, 0.27851, 8, -86.2, 7.83, 0.5166, 5, 4, -129.58, -156.37, 8e-05, 3, -58.71, -141.91, 0.06925, 7, 52.34, 137.83, 0.12951, 8, -57.75, 29.32, 0.79985, 10, -114.83, -19.71, 0.00131, 4, 6, -455.85, -230.27, 0, 3, -98.76, -168.07, 0.00687, 8, -16.42, 53.4, 0.81421, 10, -82.66, 15.76, 0.17892, 4, 5, -403.34, -271.12, 0, 8, 25.72, 70.86, 0.34619, 9, 39.49, 270.87, 0, 10, -47.71, 45.16, 0.65381, 4, 5, -443.24, -294.13, 0, 8, 70.65, 81.03, 0.04629, 9, 84.42, 281.04, 0, 10, -7.88, 68.47, 0.95371, 4, 6, -575.13, -296.14, 0, 5, -485.79, -310.91, 0, 9, 130.02, 284.49, 0, 10, 34.63, 85.56, 1, 4, 6, -616.23, -304.09, 0, 5, -526.67, -319.92, 0, 9, 171.74, 281, 0, 10, 75.53, 94.87, 1, 3, 6, -662.83, -309.65, 0, 5, -573.11, -326.68, 0, 10, 122.03, 101.98, 1, 2, 6, -757.04, -290.48, 0, 10, 217.04, 85.95, 1, 1, 10, 323.92, 62.88, 1, 3, 6, -941.44, -251.81, 0, 10, 403.03, 53.44, 0.96283, 11, -45.09, 55.23, 0.03717, 3, 6, -973.04, -247.97, 0, 10, 434.81, 50.65, 0.71985, 11, -13.59, 51.22, 0.28015, 3, 6, -1005.15, -246.23, 0, 10, 467.04, 49.98, 0.23591, 11, 18.43, 49.3, 0.76409, 3, 6, -1030.95, -249.8, 0, 10, 492.76, 54.4, 0.03241, 11, 44.19, 52.72, 0.96759, 2, 6, -1056.3, -254.69, 0, 11, 69.5, 57.48, 1, 2, 6, -1127.02, -265.5, 0, 11, 140.1, 67.89, 1, 2, 6, -1183.59, -255.52, 0, 11, 196.47, 57.59, 1, 2, 6, -1280.23, -236.25, 0, 11, 292.77, 37.79, 1, 2, 6, -1327.74, -228.59, 0, 11, 340.11, 29.86, 1, 2, 6, -1347.44, -225.41, 0, 11, 359.75, 26.57, 1, 3, 6, -1361.91, -223.99, 0, 11, 374.18, 25.08, 0.93676, 12, 163.43, -26.58, 0.06324, 3, 6, -1379.5, -225.5, 0, 11, 391.73, 26.49, 0.70677, 12, 145.78, -26.77, 0.29323, 3, 6, -1398.15, -227.11, 0, 11, 410.34, 27.99, 0.28246, 12, 127.06, -26.98, 0.71754, 3, 6, -1416.81, -228.71, 0, 11, 428.96, 29.49, 0.04627, 12, 108.34, -27.19, 0.95373, 2, 6, -1462.37, -235.88, 0, 12, 62.36, -30.94, 1, 3, 6, -1490.93, -241.42, 0, 12, 33.48, -34.34, 0.95625, 84, 70.87, -37.16, 0.04375, 3, 6, -1523.48, -250.35, 0, 12, 0.34, -40.81, 0.37318, 84, 37.31, -40.9, 0.62682, 3, 6, -1543.58, -255.86, 0, 12, -20.1, -44.8, 0.09602, 84, 16.61, -43.2, 0.90398, 2, 6, -1565.05, -251.23, 0, 84, -3.88, -35.28, 1, 2, 9, 1058.53, -93.98, 0, 84, 0.34, 32.6, 1, 3, 9, 1048.3, -105.05, 0, 12, -34.69, 42.29, 0.12869, 84, 9.21, 44.79, 0.87131, 3, 9, 1028.69, -106.55, 0, 12, -15.77, 47.65, 0.2949, 84, 28.51, 48.59, 0.7051, 3, 9, 1004.02, -97.93, 0, 12, 10.12, 44.1, 0.65928, 84, 54.02, 42.93, 0.34072, 3, 9, 977.41, -88.33, 0, 12, 38.11, 39.97, 0.97273, 84, 81.57, 36.52, 0.02727, 2, 9, 952.27, -83.43, 0, 12, 63.72, 40.16, 1, 3, 9, 917.79, -78.95, 0, 11, 443.66, -39.46, 0.00657, 12, 98.41, 42.61, 0.99343, 3, 9, 891.33, -65.4, 0, 11, 414.63, -33.43, 0.2016, 12, 127.03, 34.59, 0.7984, 3, 9, 879.18, -61.47, 0, 11, 401.9, -32.87, 0.45589, 12, 139.72, 33.15, 0.54411, 3, 9, 865.84, -52.49, 0, 11, 386.69, -27.76, 0.77453, 12, 154.57, 27, 0.22547, 3, 9, 853.82, -45.67, 0, 11, 373.32, -24.37, 0.97109, 12, 167.71, 22.69, 0.02891, 2, 9, 840.33, -41.13, 0, 11, 359.15, -23.59, 1, 2, 9, 821.07, -37.42, 0, 11, 339.64, -25.12, 1, 2, 9, 750.29, -23.76, 0, 11, 267.96, -30.76, 1, 2, 9, 668.9, -17.62, 0, 11, 188.07, -46.47, 1, 2, 9, 626.2, -8.83, 0, 11, 144.67, -49.34, 1, 3, 9, 554.88, 18.64, 0, 10, 521.14, -39.1, 0.00032, 11, 68.81, -41.8, 0.99968, 3, 9, 534.43, 24.03, 0, 10, 499.98, -40.16, 0.0223, 11, 47.72, -42.04, 0.9777, 4, 5, -921.34, -174.03, 0, 9, 505.57, 24.88, 0, 10, 472.15, -48.09, 0.2216, 11, 19.74, -48.9, 0.7784, 4, 5, -893.37, -167.66, 0, 9, 476.97, 27.06, 0, 10, 444.17, -54.67, 0.60423, 11, -8.34, -54.39, 0.39577, 4, 5, -860.82, -165.05, 0, 9, 445.11, 34.2, 0, 10, 411.57, -57.52, 0.92036, 11, -40.87, -55.97, 0.07964, 1, 10, 378.82, -59.01, 1, 1, 10, 332.76, -62.26, 1, 4, 7, 364.35, -39.73, 0.0029, 9, 274.45, 63.58, 0, 10, 239.65, -81.21, 0.97273, 14, 181.05, 138.96, 0.02437, 4, 7, 334.64, -34.46, 0.01923, 9, 244.57, 67.74, 0, 10, 209.84, -86.3, 0.85164, 14, 151.41, 132.79, 0.12913, 3, 7, 305.61, -29.5, 0.04906, 10, 180.78, -91.46, 0.61259, 14, 122.52, 126.58, 0.33835, 3, 7, 302.48, -42.74, 0.05112, 10, 182.32, -104.98, 0.38073, 14, 124.55, 113.12, 0.56815, 3, 7, 327.84, -54.15, 0.02294, 10, 210.1, -107.14, 0.16793, 14, 152.43, 111.98, 0.80913, 3, 7, 353.01, -67.93, 0.00598, 10, 238.52, -111.58, 0.05285, 14, 181.03, 108.57, 0.94117, 1, 14, 257.6, 93.12, 1, 1, 14, 303.35, 83.77, 1, 1, 14, 367.86, 73.79, 1, 2, 15, -28.3, 70.72, 0.16315, 14, 395.98, 71.66, 0.83685, 2, 15, -2.8, 69.93, 0.39867, 14, 421.62, 69.98, 0.60133, 2, 15, 22.23, 65.7, 0.66965, 14, 446.68, 64.89, 0.33035, 3, 5, -974.58, -48.37, 0, 15, 47.03, 59, 0.89699, 14, 471.41, 57.33, 0.10301, 3, 5, -990.59, -44.71, 0, 15, 63.22, 56.61, 0.97234, 14, 487.63, 54.38, 0.02766, 2, 9, 620.56, -145.35, 0, 15, 153.48, 64.47, 1, 2, 9, 656.86, -160.95, 0, 15, 192.83, 63.41, 1, 3, 6, -1264.24, 16.77, 0, 9, 708.51, -203.49, 0, 15, 256.31, 42.98, 1, 4, 6, -1353.47, 40.79, 0, 9, 787.89, -250.8, 0, 13, 209.49, -29.94, 0.00497, 15, 347.2, 28.38, 0.99503, 4, 6, -1371.59, 43.98, 0, 9, 804.47, -258.79, 0, 13, 191.19, -27.96, 0.07504, 15, 365.49, 27.09, 0.92496, 4, 6, -1390.87, 45.25, 0, 9, 822.68, -265.24, 0, 13, 171.87, -27.98, 0.41566, 15, 384.73, 27.84, 0.58434, 4, 6, -1407.48, 43, 0, 9, 839.28, -267.57, 0, 13, 155.45, -31.34, 0.86309, 15, 400.95, 31.81, 0.13691, 5, 6, -1418.79, 31.58, 0, 5, -1337.64, -5.11, 0, 9, 853.26, -259.65, 0, 13, 144.93, -43.48, 0.98255, 15, 410.97, 44.34, 0.01745, 4, 6, -1438.92, 19.16, 0, 5, -1357.44, -18.05, 0, 9, 876, -253.15, 0, 13, 125.67, -57.22, 1, 4, 6, -1460.02, 17.1, 0, 5, -1378.48, -20.66, 0, 9, 896.88, -256.88, 0, 13, 104.76, -60.69, 1, 4, 6, -1496.06, 31.99, 0, 5, -1414.89, -6.7, 0, 9, 927.53, -280.99, 0, 13, 67.8, -48.24, 1, 4, 6, -1509.28, 34.84, 0, 5, -1428.18, -4.2, 0, 9, 939.48, -287.31, 0, 13, 54.42, -46.28, 1, 1, 13, -10.39, -50.48, 1, 1, 13, -12.67, -40.4, 1, 4, 6, -1523.68, 49.73, 0, 5, -1442.96, 10.32, 0, 9, 949.3, -305.55, 0, 13, 39.06, -32.39, 1, 5, 6, -1542.19, 53.9, 0, 5, -1461.58, 14.01, 0, 9, 966, -314.58, 0, 13, 20.31, -29.46, 0.86, 83, 101.78, -0.16, 0.14, 5, 6, -1568.14, 51.07, 0, 5, -1487.44, 10.5, 0, 9, 991.74, -318.89, 0, 13, -5.39, -34.02, 0.50096, 83, 90.77, -23.83, 0.49904, 4, 6, -1582.52, 51.33, 0, 9, 1005.51, -323.04, 0, 13, -19.76, -34.72, 0.25368, 83, 83.09, -36, 0.74632, 1, 83, 76.23, -41.83, 1, 1, 83, -3, -1.91, 1, 1, 83, -1.84, 3.02, 1, 1, 83, 13.78, 11.1, 1, 2, 13, -5.18, 38.35, 0.04343, 83, 31.62, 17.88, 0.95657, 2, 13, 12.95, 31.96, 0.30313, 83, 47.26, 29.06, 0.69687, 2, 13, 27.7, 32.99, 0.5353, 83, 54.88, 41.74, 0.4647, 2, 13, 46.87, 35.14, 0.77702, 83, 64.12, 58.66, 0.22298, 2, 13, 70.82, 36.48, 0.9314, 83, 76.77, 79.05, 0.0686, 1, 13, 112.87, 36.17, 1, 2, 13, 138.22, 37.21, 0.95298, 15, 420.65, -36.05, 0.04702, 2, 13, 161.31, 34.94, 0.54857, 15, 397.57, -34.65, 0.45143, 2, 13, 182.32, 34.73, 0.03358, 15, 376.66, -35.22, 0.96642, 1, 15, 353.53, -37.18, 1, 2, 6, -1213.26, 110.52, 0, 15, 215.51, -55.58, 1, 3, 6, -1137.67, 108.2, 0, 9, 561.9, -257.19, 0, 15, 140.37, -61.14, 1, 4, 6, -1065.15, 84.29, 0, 5, -985.48, 56.72, 0, 9, 498.57, -214.53, 0, 15, 66.04, -44.92, 1, 5, 6, -1041.73, 75.93, 0, 5, -961.85, 48.97, 0, 9, 478.3, -200.12, 0, 15, 41.96, -39.04, 0.98583, 14, 462.94, -40.47, 0.01417, 5, 6, -1021.03, 72.38, 0, 5, -941.06, 45.96, 0, 9, 459.33, -191.1, 0, 15, 21.09, -37.67, 0.84888, 14, 441.97, -38.38, 0.15112, 5, 6, -993.55, 70.99, 0, 5, -913.56, 45.27, 0, 9, 433.26, -182.31, 0, 15, -6.28, -39.14, 0.37054, 14, 414.36, -38.9, 0.62946, 1, 14, 384.77, -38.62, 1, 1, 14, 363.27, -42.44, 1, 1, 14, 256.06, -70.03, 1, 3, 6, -738.31, 98.5, 0, 9, 180.12, -139.61, 0, 14, 160.76, -84.15, 1, 3, 6, -688.45, 97.54, 0, 9, 132.39, -125.17, 0.00045, 14, 110.77, -86.66, 0.99955, 3, 6, -640.31, 91.14, 0, 9, 87.78, -105.96, 0.03244, 14, 62.13, -83.63, 0.96756, 3, 6, -601.89, 79.61, 0, 9, 53.93, -84.44, 0.15816, 14, 22.86, -74.8, 0.84184, 4, 6, -572.85, 76.31, 0, 3, -144.95, 156.82, 0.00016, 9, 26.87, -73.4, 0.38022, 14, -6.44, -73.55, 0.61962, 4, 3, -104.49, 144.88, 0.01036, 7, 72.57, -151.88, 0.00385, 9, -12.93, -59.41, 0.7956, 14, -48.78, -73.81, 0.19019, 4, 3, -63.14, 132.06, 0.07757, 7, 32.52, -135.45, 0.08562, 9, -53.56, -44.5, 0.83051, 14, -92.22, -73.48, 0.0063, 4, 4, -123.93, 105.35, 0.00725, 3, -25.63, 117.77, 0.22169, 7, -3.58, -117.9, 0.18387, 9, -90.29, -28.31, 0.58719, 4, 4, -76.1, 98.4, 0.10421, 3, 21.21, 105.84, 0.53351, 7, -49.18, -101.87, 0.13718, 9, -136.46, -14, 0.22511, 4, 4, -47.37, 108.45, 0.2847, 3, 50.83, 112.82, 0.58398, 7, -79.31, -106.21, 0.05749, 9, -166.41, -19.46, 0.07384, 3, 4, -18.6, 119.68, 0.5204, 3, 80.62, 120.97, 0.46209, 7, -109.7, -111.69, 0.01751, 3, 4, 15.26, 128.95, 0.66279, 3, 115.27, 126.63, 0.13721, 35, -89.08, 45.75, 0.2, 5, 4, 31.93, 148.1, 0.77359, 3, 133.86, 143.94, 0.03483, 36, -56.63, 45.2, 0.09981, 35, -72.41, 64.91, 0.08983, 79, -131.39, 474.95, 0.00194, 3, 4, 55.93, 161.51, 0.79584, 36, -32.63, 58.61, 0.19896, 79, -107.38, 488.35, 0.0052, 3, 4, 85.52, 167.58, 0.7938, 36, -3.04, 64.68, 0.19845, 79, -77.8, 494.42, 0.00775, 4, 4, 115.26, 163.81, 0.75894, 17, -106.42, 5.24, 0.03499, 36, 26.7, 60.91, 0.19848, 79, -48.05, 490.65, 0.0076, 4, 4, 156.2, 147.79, 0.54793, 17, -65.48, -10.79, 0.24366, 35, 51.86, 64.59, 0.1979, 79, -7.11, 474.63, 0.01052, 4, 4, 187.71, 154.81, 0.41561, 17, -33.97, -3.76, 0.5548, 35, 83.37, 71.62, 0.0198, 79, 24.4, 481.66, 0.00979, 3, 4, 219.1, 164.84, 0.0129, 17, -2.59, 6.26, 0.98008, 79, 55.78, 491.68, 0.00702, 1, 17, 26.09, 16.09, 1, 3, 6, -59.85, 167.15, 0.01671, 5, 17.34, 165.54, 0.06839, 17, 51.77, 2.76, 0.9149, 3, 6, -30.87, 163.48, 0.0427, 5, 46.41, 162.62, 0.17588, 17, 80.11, -4.3, 0.78142, 3, 6, -17.75, 149.33, 0.12055, 5, 59.88, 148.82, 0.24063, 17, 91.47, -19.89, 0.63882, 3, 6, -5.8, 130.16, 0.27114, 5, 72.33, 129.97, 0.30975, 17, 101.07, -40.34, 0.41911, 3, 6, 15.53, 116.55, 0.51403, 5, 94, 116.92, 0.28924, 17, 120.65, -56.37, 0.19673, 3, 6, 34.77, 108.71, 0.71596, 5, 113.44, 109.57, 0.20968, 17, 138.83, -66.43, 0.07436, 2, 6, 58.14, 101.77, 0.89813, 5, 136.98, 103.24, 0.10187, 3, 6, 84.1, 97.83, 0.96375, 5, 163.03, 99.97, 0.03625, 9, -611.32, 83.97, 0, 3, 6, 105.05, 96.28, 0.98467, 5, 184.02, 98.96, 0.01533, 9, -631.06, 91.14, 0, 3, 6, 128.37, 90.98, 0.99741, 5, 207.47, 94.27, 0.00259, 9, -652.08, 102.55, 0, 2, 6, 152.82, 80.32, 1, 9, -672.71, 119.45, 0, 2, 6, 173.65, 66.76, 1, 9, -689.1, 138.14, 0, 2, 4, 247.39, 160.5, 0.00048, 17, 25.71, 1.92, 0.99952, 2, 4, 240.41, 148.15, 0.03135, 17, 18.73, -10.42, 0.96865, 2, 4, 238.34, 128.22, 0.15917, 17, 16.66, -30.36, 0.84083, 3, 5, -0.1, 90.97, 0.15056, 4, 245.48, 90.04, 0.36886, 17, 23.8, -68.54, 0.48058, 1, 16, 20.24, -13.71, 1, 2, 4, 214.29, -117.8, 0.32959, 16, 34.41, 22.81, 0.67041, 2, 6, 154.62, -9.01, 0.96495, 77, -273.94, 52.49, 0.03505, 2, 6, 132.08, -8.54, 0.96292, 77, -296.48, 52.96, 0.03708, 2, 6, 177.25, -6.04, 0.98108, 77, -251.31, 55.46, 0.01892, 2, 5, 18.13, 45.21, 0.63689, 4, 256.95, 42.13, 0.36311, 2, 5, 40.55, 40.6, 0.91279, 4, 278.47, 34.35, 0.08721, 3, 6, -18.52, 43.32, 0.25678, 5, 61.86, 42.83, 0.73701, 4, 299.88, 33.5, 0.00621, 3, 6, 33.25, 48.54, 0.90524, 5, 113.47, 49.38, 0.09476, 9, -549.01, 117.62, 0, 3, 6, 59.28, 50.34, 0.99621, 5, 139.45, 51.86, 0.00379, 9, -574.55, 122.95, 0, 2, 6, 5.51, 45.64, 0.62819, 5, 85.82, 45.77, 0.37181, 2, 5, 18.13, -40.54, 0.54692, 4, 244.63, -42.72, 0.45308, 2, 5, 43.79, -39.59, 0.89864, 4, 270.16, -45.47, 0.10136, 3, 6, -19.22, -41.25, 0.28793, 5, 63.34, -41.74, 0.7026, 4, 289.21, -50.4, 0.00946, 2, 6, 65.97, -56.61, 0.99602, 5, 148.91, -54.88, 0.00398, 2, 6, 35.66, -51.19, 0.91075, 5, 118.46, -50.25, 0.08925, 2, 6, 5.72, -46.25, 0.62257, 5, 88.4, -46.09, 0.37743, 2, 4, 206.13, -16.3, 0.97111, 79, 42.81, 310.54, 0.02889, 3, 4, 204, -65.78, 0.82078, 16, 24.13, 74.83, 0.15097, 79, 40.69, 261.06, 0.02824, 2, 4, 208.37, 12.76, 0.97066, 79, 45.06, 339.6, 0.02934, 3, 4, 215.04, 62.49, 0.82941, 17, -6.64, -96.08, 0.14134, 79, 51.73, 389.34, 0.02925, 3, 5, 6.13, 17.48, 0.39456, 4, 241.09, 16.42, 0.58495, 79, 77.77, 343.26, 0.02049, 3, 5, 35.16, 21.5, 0.91497, 4, 270.4, 16.23, 0.06449, 79, 107.09, 343.07, 0.02053, 3, 6, -19.37, 24.08, 0.06148, 5, 61.5, 23.57, 0.91822, 79, 133.45, 341.34, 0.0203, 4, 6, 5.78, 25.36, 0.67893, 5, 86.62, 25.5, 0.30065, 9, -516.29, 132.49, 0, 79, 158.58, 339.64, 0.02042, 4, 6, 33.1, 25.58, 0.97341, 5, 113.92, 26.43, 0.00624, 9, -542.64, 139.68, 0, 79, 185.73, 336.64, 0.02035, 3, 6, 59.21, 25.29, 0.97871, 9, -567.7, 147.04, 0, 79, 211.63, 333.26, 0.02129, 2, 6, 62.7, -26.19, 0.97954, 79, 209.02, 281.74, 0.02046, 2, 6, 34.02, -24.87, 0.97913, 79, 180.69, 286.43, 0.02087, 3, 6, 7.8, -23.34, 0.65736, 5, 89.89, -23.13, 0.32152, 79, 154.84, 291.04, 0.02112, 3, 6, -19.28, -21.47, 0.17386, 5, 62.77, -21.96, 0.80537, 79, 128.17, 296.1, 0.02078, 3, 5, 36.94, -21.06, 0.8993, 4, 266.04, -26.15, 0.08039, 79, 102.73, 300.69, 0.02031, 3, 5, 8.09, -19.25, 0.40782, 4, 237.76, -20.22, 0.57195, 79, 74.44, 306.63, 0.02023, 4, 4, 189.3, 69.19, 0.78389, 17, -32.38, -89.38, 0.09931, 35, 84.96, -14, 0.0768, 79, 25.99, 396.03, 0.04, 3, 4, 178.62, 34.87, 0.864, 35, 74.28, -48.32, 0.096, 79, 15.31, 361.72, 0.04, 4, 4, 125.34, -1.27, 0.7776, 32, 30.82, 82.15, 0.0864, 35, 21, -84.47, 0.096, 79, -37.97, 325.57, 0.04, 3, 4, 155.02, 9.06, 0.768, 35, 50.67, -74.14, 0.192, 79, -8.3, 335.9, 0.04, 3, 4, 153.17, -8.43, 0.768, 32, 58.65, 74.99, 0.192, 79, -10.14, 318.41, 0.04, 3, 4, 174.27, -31.04, 0.864, 32, 79.75, 52.37, 0.096, 79, 10.96, 295.8, 0.04, 4, 4, 178.88, -65.97, 0.81857, 16, -0.99, 74.64, 0.06463, 32, 84.36, 17.45, 0.0768, 79, 15.57, 260.88, 0.04, 4, 4, 169.77, -107.15, 0.46245, 16, -10.1, 33.46, 0.41218, 32, 75.25, -23.73, 0.09718, 79, 6.46, 219.69, 0.02818, 4, 4, 182.72, 114.94, 0.50009, 17, -38.97, -43.63, 0.37479, 35, 78.37, 31.75, 0.09721, 79, 19.4, 441.78, 0.02791, 3, 4, 192.77, -114.84, 0.34557, 16, 12.9, 25.77, 0.6367, 79, 29.46, 212, 0.01772, 3, 4, 216.9, 120.52, 0.26527, 17, -4.79, -38.05, 0.71702, 79, 53.58, 447.37, 0.01772, 3, 5, 22.71, 64.41, 0.59044, 4, 264.24, 60.48, 0.27546, 17, 42.56, -98.09, 0.1341, 4, 5, 0.33, 56.44, 0.25371, 4, 240.94, 55.8, 0.64083, 17, 19.26, -102.77, 0.09636, 79, 77.63, 382.65, 0.0091, 3, 5, 22.83, -76.45, 0.4729, 4, 244.13, -78.93, 0.35393, 16, 64.26, 61.68, 0.17317, 4, 5, 0.34, -58.41, 0.12862, 4, 224.46, -57.85, 0.81566, 16, 44.59, 82.76, 0.04453, 79, 61.15, 268.99, 0.01118, 3, 6, 183.61, 10.19, 0.97892, 9, -683.34, 195.29, 0, 77, -244.95, 71.69, 0.02108, 3, 6, 182.3, 32.38, 0.97796, 9, -688.1, 173.58, 0, 77, -246.26, 93.88, 0.02204, 3, 6, 166.82, 52.83, 0.97706, 9, -678.74, 149.7, 0, 77, -261.74, 114.33, 0.02294, 3, 6, 143.68, 66.04, 0.97684, 9, -660.05, 130.72, 0, 77, -284.87, 127.53, 0.02316, 4, 6, 121.1, 72.68, 0.97542, 5, 200.67, 75.78, 0.00269, 9, -640.12, 118.2, 0, 77, -307.46, 134.17, 0.0219, 4, 6, 93.44, 77.51, 0.96646, 5, 172.9, 79.9, 0.01418, 9, -614.8, 106.05, 0, 77, -335.12, 139.01, 0.01936, 4, 6, 62.06, 78.74, 0.93201, 5, 141.5, 80.32, 0.05106, 9, -584.93, 96.37, 0, 77, -366.5, 140.24, 0.01693, 3, 6, 34.7, 83.33, 0.8094, 5, 114.02, 84.19, 0.17445, 77, -393.86, 144.82, 0.01615, 4, 6, 3.03, 88.04, 0.53786, 5, 82.24, 88.09, 0.38442, 17, 104.87, -83.2, 0.06409, 77, -425.53, 149.54, 0.01363, 5, 6, -28.91, 102.62, 0.19591, 5, 49.93, 101.84, 0.44245, 4, 296.56, 93.62, 0.02662, 17, 74.87, -64.96, 0.32478, 77, -457.47, 164.12, 0.01024, 5, 6, -48.01, 124.49, 0.05169, 5, 30.28, 123.21, 0.29032, 4, 280.18, 117.58, 0.03523, 17, 58.49, -40.99, 0.61832, 77, -476.56, 185.99, 0.00445, 4, 6, -68.52, 142.79, 0.02217, 5, 9.3, 140.97, 0.10455, 4, 261.96, 138.17, 0.02569, 17, 40.28, -20.4, 0.84759, 5, 6, -32.63, 68.24, 0.15027, 5, 47.1, 67.37, 0.71952, 4, 288.81, 59.91, 0.06237, 17, 67.12, -98.67, 0.06067, 77, -461.19, 129.74, 0.00716, 3, 6, 87.66, 51.06, 0.98398, 9, -602.07, 129.95, 0, 77, -340.9, 112.56, 0.01602, 4, 6, 114.57, 42.96, 0.9798, 5, 194.91, 45.91, 6e-05, 9, -625.77, 145.03, 0, 77, -313.99, 104.46, 0.02014, 3, 6, 138.51, 33.23, 0.96833, 9, -646.18, 160.89, 0, 77, -290.05, 94.73, 0.03167, 3, 6, 151.8, 14.68, 0.96324, 9, -653.95, 182.35, 0, 77, -276.76, 76.17, 0.03676, 2, 6, 181.79, -25.96, 0.97797, 77, -246.77, 35.54, 0.02203, 2, 6, 175.85, -45.78, 0.98144, 77, -252.7, 15.72, 0.01856, 2, 6, 151.5, -64.05, 0.97181, 77, -277.06, -2.55, 0.02819, 3, 6, 124.83, -75.26, 0.96927, 5, 208.22, -72, 0.00408, 77, -303.73, -13.76, 0.02665, 3, 6, 97.38, -81.35, 0.95581, 5, 180.95, -78.81, 0.02019, 77, -331.18, -19.86, 0.02399, 3, 6, 68.99, -86.27, 0.93158, 5, 152.69, -84.46, 0.045, 77, -359.57, -24.77, 0.02342, 3, 6, 40.52, -88.31, 0.85818, 5, 124.28, -87.23, 0.12013, 77, -388.04, -26.81, 0.02169, 4, 6, 9.13, -92.67, 0.65001, 5, 93.01, -92.4, 0.27435, 16, 131.42, 35.81, 0.05783, 77, -419.43, -31.17, 0.01781, 5, 6, -21.48, -106.28, 0.35003, 5, 62.76, -106.8, 0.36768, 4, 279.29, -114.71, 0.01793, 16, 99.42, 25.9, 0.25258, 77, -450.04, -44.78, 0.01178, 5, 6, -48.32, -123.72, 0.09361, 5, 36.38, -124.93, 0.31535, 4, 250.58, -128.86, 0.04353, 16, 70.71, 11.75, 0.54199, 77, -476.88, -62.22, 0.00553, 4, 6, -70.45, -148.01, 0.03723, 5, 14.9, -149.78, 0.16875, 4, 225.75, -150.37, 0.0091, 16, 45.88, -9.76, 0.78491, 5, 6, -25.19, -74.84, 0.35577, 5, 58.24, -75.47, 0.5791, 4, 279.32, -83.05, 0.04621, 16, 99.44, 57.56, 0.0108, 77, -453.75, -13.34, 0.00813, 3, 6, 96.1, -56.4, 0.97023, 5, 179.02, -53.89, 0.00112, 77, -332.46, 5.1, 0.02865, 3, 6, 125.83, -52.87, 0.9683, 5, 208.65, -49.6, 0.00061, 77, -302.73, 8.63, 0.03109, 2, 6, 152.67, -35.43, 0.96573, 77, -275.89, 26.07, 0.03427, 4, 4, 5.91, 93.12, 0.56003, 3, 102.21, 91.99, 0.21479, 35, -98.43, 9.93, 0.19371, 79, -157.4, 419.96, 0.03147, 4, 4, 18.46, 46.44, 0.71945, 3, 109.8, 44.24, 0.04855, 35, -85.88, -36.76, 0.192, 79, -144.85, 373.28, 0.04, 2, 4, 27.13, 1.64, 0.96, 79, -136.19, 328.48, 0.04, 4, 4, 16.24, -41.82, 0.71559, 3, 98.33, -43.3, 0.05241, 32, -78.28, 41.6, 0.192, 79, -147.07, 285.02, 0.04, 3, 4, 46.99, 15.87, 0.768, 35, -57.35, -67.33, 0.192, 79, -116.32, 342.71, 0.04, 4, 4, 75.96, -1.8, 0.7776, 32, -18.56, 81.61, 0.0864, 35, -28.38, -85, 0.096, 79, -87.35, 325.04, 0.04, 3, 4, 47.65, -13.33, 0.768, 32, -46.88, 70.09, 0.192, 79, -115.67, 313.52, 0.04, 4, 4, 1.6, -79.65, 0.54836, 3, 79.8, -79.38, 0.22508, 32, -92.92, 3.77, 0.19336, 79, -161.71, 247.19, 0.0332, 4, 4, -15.15, 76.62, 0.49623, 3, 79.53, 77.79, 0.45151, 7, -104.8, -68.77, 0.01859, 79, -178.47, 403.46, 0.03367, 6, 4, -44.91, 68.39, 0.21195, 3, 49.08, 72.72, 0.65227, 7, -74.01, -66.42, 0.06502, 9, -162.6, 20.5, 0.03814, 79, -208.22, 395.23, 0.03087, 81, -434.62, 111.22, 0.00175, 6, 4, -80.83, 60.58, 0.04613, 3, 12.53, 68.72, 0.56353, 7, -37.26, -65.67, 0.18424, 9, -125.9, 22.62, 0.17174, 79, -244.14, 387.42, 0.02093, 81, -425.57, 75.59, 0.01343, 6, 4, -81.78, 18.23, 0.00248, 3, 7.15, 26.71, 0.7131, 7, -28.18, -24.29, 0.22541, 9, -118.38, 64.31, 0.0141, 79, -245.09, 345.07, 0.02851, 81, -383.22, 76.11, 0.0164, 5, 4, -78.95, -32.08, 0.00106, 3, 4.68, -23.61, 0.74962, 7, -21.26, 25.61, 0.204, 79, -242.27, 294.76, 0.02909, 81, -333.04, 80.67, 0.01623, 6, 4, -71.3, -74.82, 0.04639, 3, 7.8, -66.93, 0.55691, 7, -20.54, 69.03, 0.25444, 8, -128.01, -42.16, 0.10562, 79, -234.62, 252.02, 0.02157, 81, -290.58, 89.79, 0.01506, 6, 4, -34.64, -77.22, 0.27703, 3, 44.01, -73.16, 0.58093, 7, -56.06, 78.45, 0.08876, 8, -163.86, -34.09, 0.01732, 79, -197.95, 249.62, 0.03016, 81, -289.45, 126.52, 0.00581, 4, 4, -10.55, 29.91, 0.46146, 3, 79.21, 30.85, 0.49715, 7, -100.32, -22.04, 0.0014, 79, -173.86, 356.75, 0.04, 5, 4, -45.54, 21.99, 0.05732, 3, 43.58, 26.65, 0.8585, 7, -64.46, -21.01, 0.03914, 79, -208.86, 348.83, 0.03835, 81, -388.23, 112.19, 0.00669, 3, 4, -6.4, -28.2, 0.51485, 3, 77.24, -27.37, 0.44515, 79, -169.71, 298.64, 0.04, 5, 4, -42.22, -28.72, 0.0508, 3, 41.56, -24.13, 0.87773, 7, -57.96, 29.39, 0.02593, 79, -205.54, 298.12, 0.03913, 81, -337.67, 117.26, 0.00642, 3, 4, 66.84, -116.48, 0.69673, 34, -4.15, -6.89, 0.23224, 79, -96.48, 210.36, 0.07102, 3, 4, 75.7, 120.62, 0.69623, 37, -2.28, 6, 0.23208, 79, -87.62, 447.46, 0.0717, 3, 4, 106.97, -93.37, 0.744, 34, 35.98, 16.22, 0.186, 79, -56.35, 233.47, 0.07, 3, 4, 105.55, -121.98, 0.75254, 34, 34.56, -12.4, 0.18814, 79, -57.77, 204.86, 0.05932, 3, 4, 84.7, -138.28, 0.75893, 34, 13.71, -28.7, 0.18973, 79, -78.61, 188.56, 0.05134, 3, 4, 87.12, -76.88, 0.744, 34, 16.13, 32.7, 0.186, 79, -76.19, 249.96, 0.07, 3, 4, 57.18, -75.36, 0.744, 34, -13.81, 34.23, 0.186, 79, -106.13, 251.49, 0.07, 3, 4, 35.3, -95.21, 0.74816, 34, -35.69, 14.38, 0.18704, 79, -128.02, 231.63, 0.06481, 3, 4, 36.31, -121.48, 0.75645, 34, -34.68, -11.9, 0.18911, 79, -127.01, 205.36, 0.05444, 3, 4, 55.85, -136.32, 0.7591, 34, -15.14, -26.73, 0.18977, 79, -107.46, 190.52, 0.05113, 3, 4, 141.6, -64.04, 0.752, 33, 60.18, 36.56, 0.188, 79, -21.71, 262.8, 0.06, 4, 4, 142.56, -103.01, 0.6528, 16, -37.31, 37.6, 0.10785, 33, 61.14, -2.41, 0.19016, 79, -20.75, 223.83, 0.04918, 4, 4, 125.55, -134.17, 0.6646, 16, -54.32, 6.45, 0.11287, 33, 44.13, -33.56, 0.19437, 79, -37.76, 192.68, 0.02816, 3, 4, 112.74, -40.51, 0.752, 33, 31.32, 60.09, 0.188, 79, -50.57, 286.33, 0.06, 3, 4, 68.97, -36.07, 0.752, 33, -12.45, 64.53, 0.188, 79, -94.34, 290.77, 0.06, 3, 4, 34.41, -53.55, 0.752, 33, -47.01, 47.06, 0.188, 79, -128.9, 273.3, 0.06, 4, 4, 15.92, -86.41, 0.64632, 3, 93.33, -87.6, 0.11121, 33, -65.5, 14.19, 0.18938, 79, -147.39, 240.43, 0.05309, 4, 4, 18.37, -121.25, 0.68703, 3, 92.11, -122.51, 0.08168, 33, -63.05, -20.65, 0.19218, 79, -144.94, 205.59, 0.03911, 4, 4, 155.8, 108.81, 0.64443, 17, -65.89, -49.76, 0.11464, 36, 67.24, 5.91, 0.18977, 79, -7.52, 435.65, 0.05117, 3, 4, 147.88, 65.99, 0.752, 36, 59.32, -36.91, 0.188, 79, -15.43, 392.84, 0.06, 3, 4, 114.34, 42.67, 0.752, 36, 25.78, -60.24, 0.188, 79, -48.97, 369.51, 0.06, 3, 4, 70.53, 41.17, 0.752, 36, -18.03, -61.73, 0.188, 79, -92.78, 368.01, 0.06, 3, 4, 36.66, 58.14, 0.752, 36, -51.9, -44.76, 0.188, 79, -126.65, 384.99, 0.06, 4, 4, 21.73, 96.56, 0.63936, 3, 118.31, 93.74, 0.11829, 36, -66.82, -6.34, 0.18941, 79, -141.58, 423.4, 0.05294, 4, 4, 30.57, 136.48, 0.73012, 3, 131.28, 132.52, 0.0482, 36, -57.99, 33.58, 0.19458, 79, -132.75, 463.32, 0.02709, 3, 4, 114.41, 125.68, 0.74956, 37, 36.43, 11.07, 0.18739, 79, -48.9, 452.52, 0.06305, 3, 4, 112.3, 98.13, 0.744, 37, 34.33, -16.49, 0.186, 79, -51.01, 424.97, 0.07, 3, 4, 94.36, 144.57, 0.7553, 37, 16.39, 29.95, 0.18882, 79, -68.95, 471.41, 0.05588, 3, 4, 63.72, 146.01, 0.75888, 37, -14.26, 31.4, 0.18972, 79, -99.6, 472.86, 0.0514, 3, 4, 43.93, 129.13, 0.75464, 37, -34.05, 14.51, 0.18866, 79, -119.39, 455.97, 0.0567, 4, 4, 41.78, 102.65, 0.73689, 3, 138.88, 97.7, 0.01052, 37, -36.19, -11.97, 0.18685, 79, -121.53, 429.49, 0.06574, 3, 4, 59.7, 84.2, 0.744, 37, -18.27, -30.42, 0.186, 79, -103.61, 411.04, 0.07, 3, 4, 90.41, 81.83, 0.744, 37, 12.44, -32.79, 0.186, 79, -72.9, 408.67, 0.07, 4, 4, 140.21, 141.4, 0.67017, 17, -81.47, -17.17, 0.10273, 36, 51.65, 38.5, 0.19323, 79, -23.1, 468.24, 0.03387, 2, 14, 235.31, -33.37, 0.97866, 81, -487.99, -336.83, 0.02134, 2, 14, 360.17, -17.39, 0.9805, 81, -491.21, -462.22, 0.0195, 2, 14, 384.75, -20.51, 0.98127, 81, -498.04, -485.95, 0.01873, 6, 6, -994.67, 53.37, 0, 5, -914.23, 27.64, 0, 9, 439.11, -165.66, 0, 15, -7, -21.51, 0.34154, 14, 414.25, -21.25, 0.64024, 81, -503.27, -514.89, 0.01822, 6, 6, -1024.57, 54.09, 0, 5, -944.14, 27.58, 0, 9, 467.71, -174.46, 0, 15, 22.71, -19.11, 0.89648, 14, 444.24, -19.88, 0.08636, 81, -506.48, -544.63, 0.01717, 5, 6, -1053.61, 58.92, 0, 5, -973.29, 31.65, 0, 9, 494.35, -186.97, 0, 15, 51.98, -20.88, 0.98487, 81, -513.72, -573.16, 0.01513, 4, 5, -977.44, -1.81, 0, 15, 53.49, 12.81, 0.98539, 14, 476.33, 10.94, 0.00148, 81, -480.9, -580.94, 0.01314, 4, 5, -957.65, -8.94, 0, 15, 33.29, 18.36, 0.94989, 14, 456.18, 17.19, 0.0364, 81, -471.66, -562.05, 0.01371, 3, 15, 1.47, 24.96, 0.47524, 14, 424.38, 24.9, 0.50892, 81, -459.2, -531.91, 0.01585, 2, 14, 391.99, 28.45, 0.98118, 81, -450.76, -500.56, 0.01882, 2, 14, 360.59, 30.93, 0.98043, 81, -443.53, -470.01, 0.01957, 4, 6, -1139.91, 80.95, 0, 9, 571.44, -231.57, 0, 15, 139.76, -33.81, 0.98524, 81, -542.89, -657.31, 0.01476, 2, 5, -1221.1, 45.48, 0, 15, 299.17, -15.24, 1, 3, 9, 595.47, -190.36, 0, 15, 146.84, 13.38, 0.98572, 81, -497.86, -673.09, 0.01428, 2, 14, 236.54, 46.51, 0.97876, 81, -409.23, -350.25, 0.02124, 2, 14, 229.2, 10.2, 0.97645, 81, -444, -337.47, 0.02355, 3, 6, -718.96, 52.82, 0, 14, 138.19, -39.92, 0.98064, 81, -479.67, -240.19, 0.01936, 4, 7, 268.71, -137.3, 0.00258, 10, 182.54, -205.39, 0.01086, 14, 128.46, 12.79, 0.96365, 81, -426.1, -238.66, 0.02291, 4, 7, 281.68, -83.74, 0.03638, 10, 176.61, -150.6, 0.15041, 14, 120.51, 67.32, 0.79476, 81, -371, -239.17, 0.01845, 3, 7, 286.84, -36.75, 0.06925, 10, 165.54, -104.64, 0.43822, 14, 107.74, 112.85, 0.49253, 3, 7, 288.94, -26.1, 0.06872, 10, 163.9, -93.91, 0.53875, 14, 105.72, 123.52, 0.39253, 4, 7, 298.44, 21.19, 0.03718, 10, 156.83, -46.19, 0.82806, 14, 96.89, 170.95, 0.11405, 81, -264.99, -231.75, 0.02071, 2, 10, 235.85, -40.98, 0.9816, 81, -269.02, -310.66, 0.0184, 4, 5, -836.72, -195.28, 0, 9, 431.03, 70.21, 0, 10, 387.19, -27.47, 0.98009, 81, -273.21, -462.21, 0.01991, 5, 5, -864.49, -194.64, 0, 9, 457.37, 61.39, 0, 10, 415.03, -27.9, 0.95289, 11, -36.29, -26.51, 0.02819, 81, -276.88, -489.74, 0.01892, 5, 5, -898.07, -197.11, 0, 9, 490.17, 53.8, 0, 10, 448.66, -25.19, 0.49337, 11, -2.74, -25.1, 0.48983, 81, -278.1, -523.39, 0.01679, 4, 9, 521.51, 51.49, 0, 10, 479.29, -17.91, 0.04748, 11, 28.01, -19.01, 0.93671, 81, -274.43, -554.6, 0.01581, 3, 9, 556.22, 50.18, 0, 11, 61.73, -11.04, 0.9868, 81, -269.13, -588.92, 0.0132, 3, 9, 637.15, 24.1, 0, 11, 146.47, -14.68, 0.98881, 81, -279.39, -673.33, 0.01119, 2, 6, -1289.2, -205.42, 0, 11, 301.55, 6.91, 1, 3, 6, -1132.38, -236.22, 0, 11, 145.29, 38.58, 0.98896, 81, -226.19, -676.31, 0.01104, 3, 6, -1044.25, -220.17, 0, 11, 57.3, 23.02, 0.98691, 81, -234.82, -587.16, 0.01309, 4, 6, -1013.33, -217.33, 0, 10, 476.19, 21.37, 0.05085, 11, 26.44, 20.35, 0.93459, 81, -235.07, -556.1, 0.01456, 4, 6, -985.13, -214.6, 0, 10, 448.04, 17.7, 0.56352, 11, -1.7, 17.78, 0.41983, 81, -235.43, -527.78, 0.01665, 4, 6, -949.38, -215.87, 0, 10, 412.18, 17.78, 0.95542, 11, -37.36, 19.25, 0.02708, 81, -231.18, -492.25, 0.0175, 3, 6, -920.97, -215.65, 0, 10, 383.73, 16.61, 0.98121, 81, -229.03, -463.92, 0.01879, 2, 10, 228.65, 42.24, 0.97952, 81, -185.53, -313.23, 0.02048, 4, 7, 305.99, -96.37, 0.01509, 10, 203.82, -154.26, 0.08943, 14, 147.87, 64.66, 0.87758, 81, -377.8, -265.71, 0.0179, 2, 14, 188.23, 59.03, 0.97877, 81, -389.51, -304.59, 0.02123, 2, 14, 178.69, 10.16, 0.97603, 81, -436.35, -287.72, 0.02397, 2, 14, 186.31, -38.91, 0.97839, 81, -486.01, -287.72, 0.02161, 5, 7, 332.82, 11.39, 0.01341, 9, 241.03, 113.49, 0, 10, 192.57, -43.78, 0.89582, 14, 132.57, 174.66, 0.07053, 81, -266.76, -267.45, 0.02024, 2, 10, 182.86, 43.77, 0.98015, 81, -178.68, -268.04, 0.01985, 4, 6, -678.11, -249.42, 0, 5, -589.95, -266.87, 0, 10, 139.34, 42.3, 0.97992, 81, -175.07, -224.74, 0.02008, 5, 7, 305.79, 65.92, 0.0005, 9, 211.98, 166.96, 0, 10, 148.59, -1.62, 0.97468, 14, 87.01, 215.19, 0.00134, 81, -219.76, -228.78, 0.02348, 2, 10, 187.55, 0.77, 0.97586, 81, -221.93, -267.67, 0.02414, 2, 10, 232.66, -0.27, 0.97886, 81, -228.22, -312.25, 0.02114, 6, 4, -128.24, 66.15, 0.0026, 3, -34.03, 79.24, 0.19898, 7, 8.19, -80.27, 0.29284, 9, -79.94, 9.74, 0.47693, 79, -291.56, 392.99, 0.00554, 81, -429.51, 28.01, 0.02311, 5, 5, -407.98, 14.74, 0, 3, -73.95, 90.51, 0.05424, 7, 46.96, -95.03, 0.15174, 9, -40.65, -3.56, 0.76029, 81, -435.11, -13.09, 0.03373, 5, 6, -531.64, 22.13, 0, 3, -116.76, 94.85, 0.00025, 9, 1.89, -10.08, 0.90422, 14, -51.49, -22.37, 0.061, 81, -433.44, -56.09, 0.03453, 5, 6, -575.12, -0.01, 0, 7, 137.4, -95.41, 0.02439, 9, 49.75, -0.56, 0.08385, 14, -9.51, 2.75, 0.86039, 81, -415.01, -101.27, 0.03138, 4, 7, 224.37, -119.05, 0.01094, 10, 134.53, -203.22, 0.01568, 14, 80.33, 13.2, 0.94987, 81, -418.36, -191.34, 0.02351, 5, 5, -538.02, -20.82, 0, 7, 181.42, -104.57, 0.02093, 10, 89.12, -204.13, 0.00877, 14, 34.92, 10.64, 0.94485, 81, -413.98, -146.23, 0.02544, 5, 3, -39.43, 35.98, 0.09144, 7, 17.4, -37.65, 0.65283, 9, -72.34, 52.66, 0.21095, 79, -292.39, 349.4, 0.01588, 81, -385.92, 28.69, 0.0289, 6, 5, -400.14, -33.26, 0, 3, -78.19, 42.06, 0.01184, 7, 55.47, -47.14, 0.55435, 9, -33.94, 44.61, 0.38887, 79, -331.57, 351.38, 0.00566, 81, -386.54, -10.54, 0.03928, 5, 5, -445.2, -45.11, 0, 7, 101.9, -50.9, 0.47267, 9, 12.61, 42.59, 0.36165, 14, -59.21, 30.83, 0.12855, 81, -379.69, -56.62, 0.03713, 6, 5, -486.82, -62.2, 0, 7, 146.84, -48.56, 0.43005, 9, 57.42, 46.61, 0.07575, 10, 37.51, -163.13, 0.02881, 14, -18.23, 49.74, 0.43409, 81, -367.25, -99.86, 0.0313, 5, 7, 153.11, 22.18, 0.65608, 8, 47.27, -82.47, 0.06632, 10, 19.43, -94.44, 0.20149, 14, -38.84, 117.72, 0.04468, 81, -296.92, -89.96, 0.03142, 4, 7, 109.21, 27.32, 0.78677, 8, 3.21, -78.99, 0.14479, 10, -23.72, -104.46, 0.03115, 81, -301.85, -46.03, 0.03729, 6, 6, -466.62, -96.34, 0, 3, -79.61, -35.08, 0.0072, 7, 63.7, 29.58, 0.78105, 8, -42.34, -78.43, 0.16629, 79, -324.88, 274.51, 0.00661, 81, -309.95, -1.2, 0.03884, 5, 3, -40.52, -29.89, 0.05912, 7, 24.31, 27.86, 0.82478, 8, -81.65, -81.62, 0.07086, 79, -286.56, 283.78, 0.01688, 81, -320.53, 36.78, 0.02836, 6, 4, -114.32, -91.35, 0.00402, 3, -36.71, -78.84, 0.19621, 7, 24.85, 76.96, 0.41839, 8, -82.95, -32.54, 0.34668, 79, -277.63, 235.5, 0.0091, 81, -272.59, 47.37, 0.02559, 5, 3, -70.06, -91.13, 0.06465, 7, 59.16, 86.25, 0.31767, 8, -49.01, -21.97, 0.58233, 79, -309.51, 219.78, 0.00016, 81, -255.77, 16.06, 0.03518, 6, 6, -486.95, -162.31, 0, 3, -114.04, -94.91, 0.00525, 7, 103.3, 86.13, 0.19459, 8, -4.9, -20.44, 0.74029, 10, -49.24, -51.12, 0.02083, 81, -245.91, -26.97, 0.03903, 6, 7, 150.86, 87.2, 0.14449, 8, 42.59, -17.59, 0.27563, 9, 56.36, 182.42, 0, 10, -4.74, -34.02, 0.5467, 14, -65.24, 177.22, 0.00045, 81, -234.1, -73.06, 0.03274, 6, 7, 204.03, 83.39, 0.06335, 8, 95.86, -19.4, 0.00935, 9, 109.63, 180.61, 0, 10, 46.69, -19.61, 0.8904, 14, -14.3, 193.5, 0.00942, 81, -225.77, -125.7, 0.02749, 5, 7, 249.58, 75.48, 0.01802, 9, 155.45, 174.41, 0, 10, 92.34, -11.64, 0.9463, 14, 31.09, 203.12, 0.01163, 81, -223.17, -171.87, 0.02406, 5, 7, 196.74, 16.52, 0.37883, 8, 91.09, -86.5, 0.0116, 10, 62.5, -85, 0.42514, 14, 3.91, 128.72, 0.15882, 81, -292.56, -133.74, 0.0256, 4, 7, 245.61, 8.48, 0.16152, 10, 111.31, -76.03, 0.5893, 14, 52.44, 139.47, 0.22826, 81, -289.33, -183.16, 0.02092, 5, 5, -527.9, -74.71, 0, 7, 189.74, -50.38, 0.26728, 10, 78.59, -150.32, 0.1214, 14, 22.41, 64.04, 0.58571, 81, -359.3, -142.07, 0.02561, 4, 7, 234.7, -62.22, 0.12021, 10, 125, -146.25, 0.18787, 14, 68.71, 69.8, 0.67136, 81, -360.66, -188.54, 0.02055, 4, 6, -623.5, 44.63, 0, 9, 84.21, -56.63, 0.04415, 14, 42.05, -38.4, 0.93471, 81, -463.53, -145.75, 0.02114, 4, 6, -671.97, 51.82, 0, 9, 128.92, -76.7, 0.00096, 14, 91.08, -42.21, 0.98058, 81, -474.76, -193.45, 0.01847, 4, 6, -578.19, 35.42, 0, 9, 43.1, -35.49, 0.24621, 14, -3.96, -32.38, 0.72769, 81, -450.57, -101.37, 0.0261, 5, 6, -473.13, -194.43, 0, 3, -107.68, -129.3, 0.00655, 8, -9.5, 14.23, 0.92481, 10, -64.15, -19.48, 0.03678, 81, -212.74, -15.88, 0.03186, 5, 5, -431.11, -238.55, 0, 8, 42.61, 31.53, 0.19941, 9, 56.38, 231.55, 0, 10, -19.64, 12.8, 0.77431, 81, -185.87, -63.76, 0.02628, 4, 6, -566.5, -239.08, 0, 5, -478.63, -253.65, 0, 10, 27.88, 28.25, 0.97422, 81, -176.05, -112.65, 0.02578, 4, 6, -619.52, -245.95, 0, 5, -531.46, -261.89, 0, 10, 80.77, 36.88, 0.97593, 81, -173.64, -166.06, 0.02407, 2, 9, 1038.83, -59.3, 0, 84, 23.98, 0.47, 1, 3, 9, 1017.73, -56.74, 0, 12, 4.87, 1.01, 0.67885, 84, 45.25, 0.41, 0.32115, 2, 9, 988.03, -51.4, 0, 12, 35.03, 1.67, 1, 2, 9, 962.62, -46.64, 0, 12, 60.88, 2.04, 1], "hull": 171, "edges": [0, 340, 0, 2, 12, 14, 14, 16, 16, 18, 28, 30, 30, 32, 36, 38, 62, 64, 64, 66, 66, 68, 68, 70, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 94, 96, 96, 98, 118, 120, 120, 122, 122, 124, 124, 126, 136, 138, 150, 152, 152, 154, 154, 156, 172, 174, 180, 182, 194, 196, 196, 198, 198, 200, 206, 208, 212, 214, 214, 216, 216, 218, 232, 234, 234, 236, 256, 258, 258, 260, 260, 262, 266, 268, 272, 274, 274, 276, 276, 278, 278, 280, 284, 286, 286, 288, 292, 294, 300, 302, 306, 308, 316, 318, 318, 320, 324, 326, 326, 328, 328, 330, 334, 336, 336, 338, 338, 340, 330, 332, 332, 334, 2, 4, 4, 6, 6, 8, 320, 322, 322, 324, 316, 342, 342, 344, 344, 346, 346, 348, 314, 316, 310, 312, 312, 314, 302, 304, 304, 306, 298, 300, 308, 310, 294, 296, 296, 298, 280, 282, 282, 284, 288, 290, 290, 292, 58, 60, 60, 62, 56, 58, 52, 54, 54, 56, 48, 50, 50, 52, 46, 48, 38, 350, 350, 352, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 360, 362, 362, 364, 366, 368, 364, 370, 370, 366, 372, 374, 374, 376, 380, 378, 376, 382, 382, 380, 384, 386, 388, 390, 392, 394, 394, 396, 396, 398, 398, 400, 400, 402, 404, 406, 406, 408, 408, 410, 410, 412, 412, 414, 416, 418, 418, 422, 422, 420, 420, 424, 424, 426, 426, 428, 428, 430, 430, 46, 416, 432, 432, 310, 32, 34, 34, 36, 300, 510, 510, 512, 512, 518, 518, 520, 520, 522, 522, 516, 516, 524, 524, 56, 520, 420, 70, 72, 72, 74, 294, 530, 530, 532, 532, 534, 534, 536, 536, 62, 552, 554, 554, 556, 552, 558, 558, 560, 560, 562, 562, 564, 564, 566, 566, 556, 568, 570, 570, 572, 572, 48, 568, 574, 574, 576, 576, 578, 578, 580, 580, 582, 582, 52, 584, 586, 586, 588, 588, 590, 590, 592, 592, 594, 594, 596, 596, 304, 598, 600, 598, 602, 602, 604, 604, 606, 606, 608, 608, 610, 610, 612, 612, 600, 308, 614, 614, 584, 166, 168, 168, 170, 170, 172, 178, 180, 174, 176, 176, 178, 164, 166, 160, 162, 162, 164, 156, 158, 158, 160, 92, 94, 88, 90, 90, 92, 84, 86, 86, 88, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 268, 270, 270, 272, 262, 264, 264, 266, 208, 210, 210, 212, 204, 206, 250, 252, 252, 254, 254, 256, 202, 204, 200, 202, 248, 250, 242, 244, 244, 246, 246, 248, 236, 238, 238, 240, 240, 242, 230, 232, 218, 220, 220, 222, 226, 224, 224, 222, 226, 228, 228, 230, 134, 136, 138, 140, 140, 142, 142, 144, 102, 104, 104, 106, 106, 108, 108, 110, 148, 150, 144, 146, 146, 148, 98, 100, 100, 102, 616, 618, 618, 620, 620, 622, 622, 624, 624, 626, 628, 630, 630, 632, 632, 634, 634, 636, 626, 638, 638, 640, 640, 642, 642, 628, 636, 644, 278, 648, 648, 650, 650, 652, 652, 654, 654, 174, 172, 656, 656, 658, 660, 662, 662, 664, 664, 666, 666, 668, 668, 670, 670, 672, 672, 674, 674, 676, 676, 678, 678, 680, 680, 682, 682, 684, 684, 686, 686, 688, 702, 78, 658, 704, 704, 702, 710, 722, 722, 736, 736, 738, 114, 116, 116, 118, 126, 128, 128, 130, 110, 112, 112, 114, 130, 132, 132, 134, 44, 46, 44, 42, 42, 40, 40, 38], "width": 530, "height": 1783}}, "eyewhite_L": {"eyewhite_L": {"type": "mesh", "uvs": [0.06219, 0.34644, 0.04581, 0.77013, 0.50589, 0.94666, 0.90143, 0.78116, 0.93619, 0.19861, 0.48272, 0.06841], "triangles": [3, 5, 4, 2, 5, 3, 0, 5, 2, 1, 0, 2], "vertices": [2, 6, 63.18, -21.71, 0.96, 77, -365.38, 39.79, 0.04, 2, 6, 49.19, -22.16, 0.96, 77, -379.37, 39.34, 0.04, 2, 6, 45.08, -42.82, 0.96014, 77, -383.48, 18.68, 0.03986, 2, 6, 51.97, -59.71, 0.96783, 77, -376.59, 1.79, 0.03217, 2, 6, 71.26, -59.62, 0.96828, 77, -357.3, 1.87, 0.03172, 2, 6, 73.87, -39.38, 0.96, 77, -354.69, 22.12, 0.04], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 44, "height": 33}}, "eyewhite_R": {"eyewhite_R": {"type": "mesh", "uvs": [0.10482, 0.07704, 0.05438, 0.61892, 0.41792, 0.92295, 0.92075, 0.90864, 0.95144, 0.4687, 0.57845, 0.08599], "triangles": [4, 2, 5, 5, 1, 0, 3, 2, 4, 2, 1, 5], "vertices": [2, 6, 71.3, 47.71, 0.9681, 77, -357.26, 109.2, 0.0319, 2, 6, 53.27, 48.73, 0.96797, 77, -375.29, 110.22, 0.03203, 2, 6, 44.79, 29.77, 0.96106, 77, -383.77, 91.27, 0.03894, 2, 6, 47.36, 4.76, 0.96, 77, -381.2, 66.26, 0.04, 2, 6, 61.96, 4.44, 0.96, 77, -366.6, 65.94, 0.04, 2, 6, 72.98, 24.08, 0.96016, 77, -355.57, 85.58, 0.03984], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 50, "height": 33}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 50, -6.61, -7.38, 0.9624, 77, -373.88, 14.73, 0.0376, 2, 50, -7.78, 6.57, 0.96, 77, -375.05, 28.68, 0.04, 2, 50, 6.17, 7.74, 0.96, 77, -361.1, 29.85, 0.04, 2, 50, 7.34, -6.21, 0.9622, 77, -359.93, 15.9, 0.0378], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 14}}, "eye_LL": {"eye_LL": {"type": "mesh", "uvs": [0.84474, 0.27214, 0.99317, 0.4575, 0.85333, 0.58239, 0.79138, 0.7204, 0.693, 0.83931, 0.53437, 0.94285, 0.37724, 0.96153, 0.20324, 0.89891, 0.08641, 0.80289, 0.03136, 0.70687, 0.08566, 0.51183, 0.19446, 0.29446, 0.31891, 0.17291, 0.52394, 0.08944, 0.75366, 0.14584, 0.09951, 0.68958, 0.14997, 0.53184, 0.24079, 0.39479, 0.36567, 0.29136, 0.5246, 0.27843, 0.652, 0.36635, 0.75543, 0.5551, 0.66083, 0.69992, 0.53343, 0.74389, 0.37954, 0.75681, 0.26349, 0.74905, 0.17015, 0.72061], "triangles": [7, 25, 6, 6, 24, 5, 6, 25, 24, 24, 23, 5, 4, 23, 22, 4, 5, 23, 7, 8, 26, 8, 15, 26, 7, 26, 25, 4, 22, 3, 8, 9, 15, 19, 23, 24, 25, 16, 17, 18, 24, 17, 24, 25, 17, 24, 18, 19, 23, 20, 22, 23, 19, 20, 15, 16, 26, 25, 26, 16, 22, 21, 3, 3, 21, 2, 9, 10, 15, 22, 20, 21, 15, 10, 16, 21, 0, 2, 2, 0, 1, 20, 14, 21, 21, 14, 0, 17, 16, 11, 16, 10, 11, 17, 12, 18, 17, 11, 12, 14, 20, 13, 19, 18, 13, 18, 12, 13, 20, 19, 13], "vertices": [2, 6, 66.25, -58.48, 0.96744, 77, -362.3, 3.02, 0.03256, 2, 6, 63.07, -64.85, 0.96968, 77, -365.49, -3.36, 0.03032, 2, 6, 60.1, -59.35, 0.96713, 77, -368.46, 2.15, 0.03287, 2, 6, 57.14, -57.05, 0.96596, 77, -371.42, 4.45, 0.03404, 2, 6, 54.43, -53.23, 0.96422, 77, -374.13, 8.27, 0.03578, 2, 6, 51.82, -46.92, 0.96141, 77, -376.73, 14.58, 0.03859, 2, 6, 50.91, -40.53, 0.96, 77, -377.65, 20.97, 0.04, 2, 6, 51.57, -33.32, 0.96, 77, -376.99, 28.18, 0.04, 2, 6, 53.08, -28.38, 0.96, 77, -375.48, 33.11, 0.04, 2, 6, 54.8, -25.97, 0.96, 77, -373.75, 35.52, 0.04, 2, 6, 58.88, -27.87, 0.96, 77, -369.68, 33.63, 0.04, 2, 6, 63.58, -31.95, 0.96, 77, -364.98, 29.55, 0.04, 2, 6, 66.43, -36.83, 0.96, 77, -362.13, 24.67, 0.04, 2, 6, 68.8, -45.07, 0.96194, 77, -359.76, 16.43, 0.03806, 2, 6, 68.46, -54.55, 0.96597, 77, -360.1, 6.95, 0.03403, 2, 6, 55.38, -28.73, 0.96, 77, -373.18, 32.77, 0.04, 2, 6, 58.7, -30.53, 0.96, 77, -369.86, 30.97, 0.04, 2, 6, 61.74, -34.01, 0.96, 77, -366.82, 27.49, 0.04, 2, 6, 64.23, -38.94, 0.96, 77, -364.33, 22.56, 0.04, 2, 6, 65.03, -45.41, 0.96183, 77, -363.53, 16.09, 0.03817, 2, 6, 63.72, -50.76, 0.96395, 77, -364.84, 10.74, 0.03605, 2, 6, 60.31, -55.3, 0.96541, 77, -368.25, 6.19, 0.03459, 2, 6, 57.1, -51.68, 0.96367, 77, -371.46, 9.82, 0.03633, 2, 6, 55.79, -46.55, 0.96139, 77, -372.77, 14.95, 0.03861, 2, 6, 55, -40.28, 0.96, 77, -373.56, 21.21, 0.04, 2, 6, 54.76, -35.53, 0.96, 77, -373.8, 25.97, 0.04, 2, 6, 55.01, -31.67, 0.96, 77, -373.55, 29.83, 0.04], "hull": 15, "edges": [8, 10, 26, 28, 18, 20, 20, 22, 22, 24, 14, 16, 16, 18, 10, 12, 12, 14, 2, 4, 4, 6, 6, 8, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 30, 40, 42, 0, 28, 0, 2], "width": 41, "height": 20}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 51, -6.61, -7.36, 0.96, 77, -374.41, 80.91, 0.04, 2, 51, -7.78, 6.6, 0.96321, 77, -375.58, 94.86, 0.03679, 2, 51, 6.18, 7.77, 0.9636, 77, -361.63, 96.03, 0.0364, 2, 51, 7.35, -6.19, 0.96, 77, -360.46, 82.08, 0.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 14}}, "eye_RR": {"eye_RR": {"type": "mesh", "uvs": [0.23371, 0.09281, 0.42722, 0.07599, 0.59108, 0.18853, 0.7449, 0.3178, 0.87341, 0.53075, 0.9525, 0.77648, 0.95628, 0.9058, 0.88295, 0.94297, 0.74662, 0.96288, 0.57707, 0.95817, 0.38305, 0.90834, 0.23661, 0.78664, 0.13503, 0.59256, 0.1138, 0.40532, 0.04546, 0.29055, 0.04646, 0.10963, 0.18671, 0.47699, 0.29301, 0.33965, 0.43051, 0.30961, 0.56454, 0.33965, 0.71013, 0.4448, 0.83029, 0.60788, 0.89384, 0.79886, 0.80256, 0.79672, 0.67546, 0.79885, 0.51254, 0.78599, 0.36002, 0.74307, 0.2387, 0.64007], "triangles": [18, 1, 2, 17, 0, 1, 18, 17, 1, 19, 18, 2, 19, 2, 3, 0, 14, 15, 13, 0, 17, 13, 14, 0, 20, 19, 3, 16, 13, 17, 20, 3, 4, 12, 13, 16, 21, 20, 4, 27, 16, 17, 12, 16, 27, 26, 17, 18, 27, 17, 26, 22, 21, 4, 25, 18, 19, 25, 19, 20, 26, 18, 25, 11, 12, 27, 11, 27, 26, 21, 24, 20, 22, 23, 21, 24, 25, 20, 21, 23, 24, 4, 5, 22, 22, 5, 6, 10, 26, 25, 11, 26, 10, 7, 23, 22, 7, 22, 6, 9, 25, 24, 10, 25, 9, 8, 24, 23, 8, 23, 7, 9, 24, 8], "vertices": [2, 6, 67.57, 39.49, 0.96502, 77, -360.99, 100.99, 0.03498, 2, 6, 68.55, 32, 0.96255, 77, -360.01, 93.5, 0.03745, 2, 6, 66.73, 25.44, 0.96046, 77, -361.83, 86.93, 0.03954, 2, 6, 64.52, 19.23, 0.96, 77, -364.03, 80.73, 0.04, 2, 6, 60.49, 13.86, 0.96, 77, -368.07, 75.36, 0.04, 2, 6, 55.6, 10.36, 0.96, 77, -372.96, 71.86, 0.04, 2, 6, 52.91, 9.98, 0.96, 77, -375.65, 71.48, 0.04, 2, 6, 51.89, 12.77, 0.96, 77, -376.67, 74.27, 0.04, 2, 6, 51.03, 18.03, 0.96, 77, -377.53, 79.53, 0.04, 2, 6, 50.58, 24.63, 0.96, 77, -377.98, 86.13, 0.04, 2, 6, 50.99, 32.26, 0.96237, 77, -377.57, 93.75, 0.03763, 2, 6, 53.06, 38.16, 0.96438, 77, -375.5, 99.66, 0.03562, 2, 6, 56.79, 42.45, 0.96593, 77, -371.77, 103.95, 0.03407, 2, 6, 60.64, 43.6, 0.96641, 77, -367.92, 105.1, 0.03359, 2, 6, 62.81, 46.46, 0.96736, 77, -365.74, 107.96, 0.03264, 2, 6, 66.6, 46.74, 0.96741, 77, -361.96, 108.24, 0.03259, 2, 6, 59.37, 40.64, 0.96541, 77, -369.19, 102.14, 0.03459, 2, 6, 62.59, 36.75, 0.96419, 77, -365.97, 98.25, 0.03581, 2, 6, 63.67, 31.46, 0.96247, 77, -364.89, 92.96, 0.03753, 2, 6, 63.48, 26.2, 0.96075, 77, -365.08, 87.7, 0.03925, 2, 6, 61.75, 20.36, 0.96, 77, -366.81, 81.86, 0.04, 2, 6, 58.73, 15.4, 0.96, 77, -369.83, 76.9, 0.04, 2, 6, 54.94, 12.6, 0.96, 77, -373.62, 74.1, 0.04, 2, 6, 54.69, 16.15, 0.96, 77, -373.87, 77.65, 0.04, 2, 6, 54.23, 21.08, 0.96, 77, -374.33, 82.58, 0.04, 2, 6, 53.97, 27.44, 0.96096, 77, -374.59, 88.94, 0.03904, 2, 6, 54.37, 33.44, 0.96292, 77, -374.19, 94.94, 0.03708, 2, 6, 56.13, 38.34, 0.96457, 77, -372.43, 99.84, 0.03543], "hull": 16, "edges": [2, 4, 10, 12, 12, 14, 8, 10, 4, 6, 6, 8, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 18, 20, 14, 16, 16, 18, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 32, 2, 0, 30, 0], "width": 39, "height": 21}}, "glass": {"glass": {"type": "mesh", "uvs": [0.03204, 0.02674, 0.11896, 0.15264, 0.22018, 0.17468, 0.48048, 0.35181, 0.53344, 0.35623, 0.79733, 0.25802, 0.91385, 0.26008, 0.94985, 0.13188, 0.99276, 0.13279, 0.97453, 0.44122, 0.92764, 0.53615, 0.87718, 0.86742, 0.82397, 0.98166, 0.68677, 0.9867, 0.6157, 0.91996, 0.58043, 0.82356, 0.55037, 0.5623, 0.50545, 0.47423, 0.4648, 0.54131, 0.40872, 0.85317, 0.36421, 0.92496, 0.20594, 0.92288, 0.13938, 0.85887, 0.1001, 0.63355, 0.09067, 0.40305, 0.06023, 0.31474, 0.00711, 0.16068, 0.00816, 0.03292, 0.19326, 0.48353, 0.34519, 0.54945, 0.6647, 0.59555, 0.81897, 0.56737, 0.96277, 0.27105, 0.08116, 0.16172], "triangles": [33, 0, 1, 32, 7, 8, 6, 7, 32, 33, 26, 0, 26, 27, 0, 33, 25, 26, 1, 25, 33, 24, 25, 1, 9, 32, 8, 17, 3, 4, 28, 1, 2, 24, 1, 28, 9, 10, 6, 9, 6, 32, 3, 29, 2, 18, 3, 17, 29, 28, 2, 3, 18, 29, 30, 16, 4, 17, 4, 16, 31, 5, 6, 31, 6, 10, 5, 30, 4, 31, 30, 5, 23, 24, 28, 15, 16, 30, 19, 29, 18, 22, 23, 28, 11, 31, 10, 14, 15, 30, 21, 28, 29, 20, 21, 29, 22, 28, 21, 19, 20, 29, 12, 31, 11, 13, 30, 31, 13, 14, 30, 31, 12, 13], "vertices": [2, 6, 65.11, 63.78, 0.98884, 77, -363.45, 125.27, 0.01116, 2, 6, 59.78, 50.51, 0.95798, 77, -368.78, 112, 0.04202, 2, 6, 59.91, 35.59, 0.95706, 77, -368.65, 97.08, 0.04294, 2, 6, 54.1, -3.3, 0.955, 77, -374.46, 58.2, 0.045, 2, 6, 54.53, -11.08, 0.955, 77, -374.03, 50.42, 0.045, 2, 6, 62.76, -49.31, 0.95718, 77, -365.8, 12.19, 0.04282, 2, 6, 64.09, -66.39, 0.95825, 77, -364.47, -4.89, 0.04175, 2, 6, 71.04, -71.12, 0.99063, 77, -357.51, -9.62, 0.00937, 2, 6, 71.53, -77.41, 0.99903, 77, -357.03, -15.91, 0.00097, 2, 6, 55.63, -76.05, 0.95877, 77, -372.93, -14.55, 0.04123, 2, 6, 50.23, -69.59, 0.9583, 77, -378.33, -8.09, 0.0417, 2, 6, 32.77, -63.61, 0.95778, 77, -395.79, -2.11, 0.04222, 2, 6, 26.31, -56.3, 0.95727, 77, -402.25, 5.2, 0.04273, 2, 6, 24.37, -36.22, 0.95601, 77, -404.19, 25.28, 0.04399, 2, 6, 26.89, -25.53, 0.95536, 77, -401.67, 35.97, 0.04464, 2, 6, 31.35, -19.95, 0.95506, 77, -397.2, 41.55, 0.04494, 2, 6, 44.26, -14.43, 0.955, 77, -384.3, 47.06, 0.045, 2, 6, 48.19, -7.48, 0.955, 77, -380.37, 54.02, 0.045, 2, 6, 44.28, -1.81, 0.955, 77, -384.28, 59.69, 0.045, 2, 6, 27.74, 5.08, 0.95518, 77, -400.82, 66.57, 0.04482, 2, 6, 23.54, 11.29, 0.95554, 77, -405.01, 72.79, 0.04446, 2, 6, 21.71, 34.48, 0.95696, 77, -406.85, 95.98, 0.04304, 2, 6, 24.14, 44.51, 0.95759, 77, -404.42, 106, 0.04241, 2, 6, 35.11, 51.22, 0.95805, 77, -393.45, 112.72, 0.04195, 2, 6, 46.71, 53.58, 0.95815, 77, -381.85, 115.08, 0.04185, 2, 6, 50.82, 58.42, 0.95845, 77, -377.74, 119.92, 0.04155, 2, 6, 58, 66.86, 0.98898, 77, -370.56, 128.36, 0.01102, 2, 6, 64.51, 67.25, 0.98905, 77, -364.05, 128.75, 0.01095, 2, 6, 43.88, 38.21, 0.9572, 77, -384.68, 99.71, 0.0428, 2, 6, 42.39, 15.68, 0.95581, 77, -386.16, 77.17, 0.04419, 2, 6, 43.98, -31.32, 0.95587, 77, -384.58, 30.17, 0.04413, 2, 6, 47.3, -53.8, 0.9573, 77, -381.25, 7.7, 0.0427, 2, 6, 64.13, -73.6, 0.95884, 77, -364.43, -12.11, 0.04116, 2, 6, 58.86, 56.01, 0.95832, 77, -369.7, 117.5, 0.04168], "hull": 28, "edges": [0, 54, 0, 2, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 42, 44, 44, 46, 46, 48, 52, 54, 8, 10, 10, 12, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 26, 28, 22, 24, 24, 26, 4, 6, 2, 4, 56, 58, 60, 62, 48, 50, 50, 52, 66, 50, 64, 18], "width": 147, "height": 51}}, "hair_F": {"hair_F": {"type": "mesh", "uvs": [0.37328, 0.05487, 0.40591, 0.02235, 0.43476, 0.00286, 0.47144, 0.0036, 0.50621, 0.02416, 0.53315, 0.05878, 0.5589, 0.02795, 0.58978, 0.01714, 0.61819, 0.02006, 0.64373, 0.04518, 0.66681, 0.08133, 0.68624, 0.12588, 0.70336, 0.18424, 0.72024, 0.24077, 0.73824, 0.29563, 0.76635, 0.36005, 0.80682, 0.41506, 0.85778, 0.46856, 0.91484, 0.50457, 0.97595, 0.52738, 0.97715, 0.54811, 0.91631, 0.54817, 0.87836, 0.53344, 0.9136, 0.59879, 0.94816, 0.65401, 0.98692, 0.71669, 0.99036, 0.77592, 0.99428, 0.84356, 0.99506, 0.90613, 0.98157, 0.9574, 0.95438, 0.99866, 0.94432, 0.99199, 0.96129, 0.94498, 0.97275, 0.89732, 0.97615, 0.84082, 0.96382, 0.78632, 0.94489, 0.72901, 0.91564, 0.66903, 0.87982, 0.62356, 0.83565, 0.58564, 0.78593, 0.54434, 0.73848, 0.50122, 0.6992, 0.44722, 0.65992, 0.39656, 0.63823, 0.33794, 0.62638, 0.27799, 0.62398, 0.21857, 0.62048, 0.16064, 0.60585, 0.12836, 0.58524, 0.13031, 0.55793, 0.15415, 0.52433, 0.20112, 0.50872, 0.19999, 0.47765, 0.13473, 0.45125, 0.11104, 0.4358, 0.11654, 0.41698, 0.14883, 0.40672, 0.19439, 0.39845, 0.26013, 0.38519, 0.32496, 0.35645, 0.3897, 0.31235, 0.45173, 0.25942, 0.50691, 0.20555, 0.54206, 0.15156, 0.56545, 0.10969, 0.58733, 0.07783, 0.62541, 0.05605, 0.67091, 0.04443, 0.72734, 0.04155, 0.78855, 0.05882, 0.81834, 0.08679, 0.8316, 0.12021, 0.84002, 0.10689, 0.85751, 0.07778, 0.86134, 0.04879, 0.84441, 0.0244, 0.81716, 0.00382, 0.7912, 0.00572, 0.73819, 0.02252, 0.66891, 0.04647, 0.61, 0.08698, 0.55269, 0.12892, 0.50769, 0.16986, 0.46281, 0.20509, 0.40238, 0.23991, 0.33756, 0.27071, 0.26684, 0.30171, 0.19449, 0.32533, 0.14193, 0.34944, 0.09456, 0.52308, 0.15702], "triangles": [75, 70, 74, 74, 71, 73, 74, 70, 71, 73, 71, 72, 75, 76, 70, 76, 69, 70, 76, 77, 69, 77, 78, 69, 69, 78, 68, 78, 79, 68, 68, 79, 67, 79, 80, 67, 67, 80, 66, 65, 66, 81, 66, 80, 81, 64, 65, 82, 65, 81, 82, 64, 82, 83, 63, 64, 83, 63, 83, 62, 83, 84, 62, 62, 84, 61, 84, 85, 61, 61, 85, 60, 85, 86, 60, 60, 86, 59, 59, 86, 58, 58, 86, 87, 58, 87, 57, 57, 87, 88, 57, 88, 56, 56, 88, 89, 30, 32, 29, 30, 31, 32, 29, 32, 28, 28, 32, 33, 33, 27, 28, 33, 34, 27, 34, 26, 27, 34, 35, 26, 26, 35, 25, 35, 36, 25, 36, 24, 25, 36, 37, 24, 38, 23, 37, 37, 23, 24, 23, 38, 22, 38, 39, 22, 22, 39, 17, 22, 18, 21, 21, 19, 20, 21, 18, 19, 39, 40, 17, 40, 16, 17, 40, 41, 16, 22, 17, 18, 41, 15, 16, 41, 42, 15, 42, 43, 15, 43, 14, 15, 43, 44, 14, 44, 13, 14, 44, 45, 13, 13, 46, 12, 13, 45, 46, 12, 46, 47, 12, 47, 11, 47, 10, 11, 48, 9, 10, 48, 7, 8, 7, 48, 49, 49, 6, 7, 89, 0, 56, 56, 0, 55, 54, 55, 1, 55, 0, 1, 1, 2, 54, 53, 54, 4, 54, 3, 4, 54, 2, 3, 52, 53, 90, 5, 53, 4, 49, 5, 6, 51, 90, 50, 50, 90, 5, 49, 50, 5, 5, 90, 53, 52, 90, 51, 48, 8, 9, 47, 48, 10], "vertices": [3, 52, 19.65, 44.54, 0.68523, 59, 4.37, -12.48, 0.309, 78, -227.25, 99.09, 0.00577, 3, 52, 27.41, 35.63, 0.9206, 59, -6.53, -7.89, 0.07673, 78, -219.49, 90.18, 0.00268, 2, 52, 32.29, 27.59, 0.99956, 78, -214.61, 82.13, 0.00044, 1, 52, 33.03, 16.9, 1, 1, 52, 29.47, 6.42, 1, 1, 52, 22.71, -2.04, 1, 1, 52, 29.95, -8.98, 1, 1, 52, 33.01, -17.77, 1, 2, 52, 33.08, -26.09, 0.99997, 78, -213.82, 28.45, 3e-05, 2, 52, 28.32, -33.98, 0.99826, 78, -218.57, 20.57, 0.00174, 3, 52, 21.14, -41.34, 0.80425, 53, -0.71, 9.2, 0.19128, 78, -225.76, 13.2, 0.00447, 3, 52, 12.07, -47.8, 0.58181, 53, 10.12, 11.78, 0.41151, 78, -234.83, 6.75, 0.00668, 4, 52, -0.02, -53.83, 0.31634, 53, 23.58, 12.85, 0.66969, 54, -11.15, 17.45, 0.00558, 78, -246.91, 0.72, 0.00839, 4, 52, -11.71, -59.75, 0.05842, 53, 36.65, 13.96, 0.5279, 54, 1.57, 14.23, 0.40381, 78, -258.61, -5.21, 0.00986, 3, 53, 49.47, 15.49, 0.01846, 54, 14.19, 11.49, 0.96991, 78, -269.93, -11.43, 0.01163, 3, 54, 30.21, 9.91, 0.98445, 55, -12.79, 12.98, 0.00094, 78, -283.04, -20.77, 0.01461, 3, 54, 46.73, 12.44, 0.30576, 55, 3.89, 11.88, 0.67513, 78, -293.84, -33.53, 0.0191, 2, 55, 22.66, 13.03, 0.97523, 78, -304.06, -49.33, 0.02477, 2, 55, 40.3, 18.19, 0.96882, 78, -310.38, -66.58, 0.03118, 2, 55, 56.95, 26.26, 0.96177, 78, -313.78, -84.77, 0.03823, 2, 55, 60.15, 23.14, 0.9617, 78, -318.19, -85.49, 0.0383, 2, 55, 46.8, 11.42, 0.96884, 78, -319.69, -67.79, 0.03116, 2, 55, 36.38, 6.5, 0.97326, 78, -317.46, -56.48, 0.02674, 2, 56, 5.42, 4.33, 0.96935, 78, -330.6, -67.91, 0.03065, 3, 56, 20.88, 6.28, 0.96534, 58, -42.93, -27.7, 0, 78, -341.59, -78.96, 0.03466, 4, 56, 38.36, 8.37, 0.89564, 57, -7.77, 5.61, 0.06518, 58, -34.27, -12.38, 0, 78, -354.07, -91.36, 0.03918, 3, 56, 49.56, 2.24, 0.05311, 57, 4.97, 4.78, 0.90738, 78, -366.68, -93.42, 0.03951, 2, 57, 19.53, 3.84, 0.95981, 78, -381.07, -95.78, 0.04019, 3, 57, 32.88, 2.14, 0.05067, 58, 3.05, 4.11, 0.90849, 78, -394.46, -97.13, 0.04085, 3, 57, 43.22, -3.34, 6e-05, 58, 14.75, 4.28, 0.9606, 78, -405.77, -94.13, 0.03934, 2, 58, 25.85, -0.05, 0.9642, 78, -415.28, -86.96, 0.0358, 2, 58, 25.53, -3.31, 0.96529, 78, -414.09, -83.91, 0.03471, 2, 58, 14.33, -2.2, 0.96301, 78, -403.61, -88, 0.03699, 3, 57, 30.07, -4.04, 0.09746, 58, 3.56, -2.66, 0.86458, 78, -393.12, -90.48, 0.03796, 2, 57, 18.19, -1.32, 0.96277, 78, -380.93, -90.46, 0.03723, 2, 57, 6.08, -3.21, 0.96724, 78, -369.55, -85.89, 0.03276, 2, 56, 33.85, -3.35, 0.97099, 78, -357.73, -79.35, 0.02901, 2, 56, 18.39, -3.44, 0.97456, 78, -345.6, -69.76, 0.02544, 3, 55, 49.46, -7.79, 0.21997, 56, 4.48, -6.84, 0.76088, 78, -336.73, -58.52, 0.01916, 2, 55, 34.39, -10.16, 0.99007, 78, -329.68, -44.99, 0.00993, 3, 55, 17.62, -13.05, 0.99783, 77, -372.19, -29.78, 0.00085, 78, -322.05, -29.78, 0.00132, 3, 54, 49.86, -14.61, 0.40195, 55, 1.09, -15.21, 0.59565, 77, -364.11, -15.2, 0.0024, 3, 54, 33.72, -16.99, 0.96878, 55, -15.19, -14.03, 0.02805, 77, -353.5, -2.8, 0.00317, 3, 53, 63.43, -12.77, 0.00192, 54, 18.15, -19.79, 0.99371, 77, -343.6, 9.54, 0.00437, 3, 53, 49.52, -15.1, 0.12577, 54, 4.24, -17.44, 0.86959, 77, -331.57, 16.91, 0.00464, 4, 52, -21.98, -33.11, 0.13578, 53, 36.18, -14.59, 0.61312, 54, -8.2, -12.6, 0.24625, 77, -319.02, 21.43, 0.00486, 3, 52, -9.31, -31.35, 0.38984, 53, 23.77, -11.48, 0.60593, 77, -306.34, 23.2, 0.00422, 3, 52, 3.02, -29.29, 0.65512, 53, 11.57, -8.77, 0.34116, 77, -294.02, 25.26, 0.00372, 3, 52, 9.58, -24.45, 0.94612, 53, 3.68, -10.8, 0.0499, 77, -287.46, 30.09, 0.00398, 2, 52, 8.66, -18.49, 0.99623, 77, -288.38, 36.06, 0.00377, 2, 52, 2.88, -10.97, 0.99738, 77, -294.16, 43.57, 0.00262, 1, 52, -8, -2.04, 1, 1, 52, -8.14, 2.53, 1, 2, 52, 5.08, 12.74, 0.99816, 77, -291.95, 67.28, 0.00184, 2, 52, 9.52, 20.85, 0.99596, 77, -287.52, 75.39, 0.00404, 3, 52, 7.96, 25.24, 0.89045, 59, 6.38, 9.99, 0.10499, 77, -289.08, 79.79, 0.00456, 3, 52, 0.58, 30.14, 0.62098, 59, 15.16, 8.84, 0.37469, 77, -296.45, 84.68, 0.00434, 4, 52, -9.43, 32.31, 0.41056, 59, 25.1, 11.29, 0.56437, 60, -12.61, 11.66, 0.02056, 77, -306.46, 86.85, 0.00451, 4, 52, -23.71, 33.53, 0.12887, 59, 38.47, 16.47, 0.33881, 60, 0.9, 16.45, 0.52693, 77, -320.75, 88.07, 0.0054, 3, 59, 52.42, 20.3, 0.02555, 60, 14.96, 19.88, 0.96848, 77, -334.96, 90.77, 0.00597, 3, 60, 31.2, 19.36, 0.96832, 61, -17.57, 13.54, 0.02558, 77, -349.54, 97.97, 0.0061, 3, 60, 49.13, 14.63, 0.35098, 61, 0.77, 16.24, 0.64345, 77, -363.9, 109.69, 0.00557, 3, 61, 20.25, 16.2, 0.99365, 77, -377.02, 124.1, 0.00408, 78, -326.88, 124.1, 0.00227, 4, 61, 37.32, 12.58, 0.75877, 62, -3.52, 12.57, 0.23068, 77, -385.86, 139.14, 0.00193, 78, -335.72, 139.14, 0.00862, 4, 61, 52.88, 6.94, 0.0363, 62, 12.06, 6.98, 0.94091, 77, -392.19, 154.43, 0.00127, 78, -342.05, 154.43, 0.02153, 3, 62, 24.63, 3.29, 0.96864, 63, -12.75, 11.58, 7e-05, 78, -347.76, 166.22, 0.03129, 3, 62, 36.99, 4.14, 0.83156, 63, -1.74, 5.89, 0.13386, 78, -356.7, 174.81, 0.03458, 2, 63, 9.68, 3.53, 0.96343, 78, -366.98, 180.32, 0.03657, 3, 63, 22.22, 4.79, 0.92827, 64, -1.61, 10.26, 0.03483, 78, -379.35, 182.69, 0.0369, 3, 63, 34.78, 8.79, 0.01264, 64, 9.07, 2.51, 0.95127, 78, -392.53, 182.43, 0.03609, 3, 64, 17.17, 3.35, 0.56609, 65, 1.3, 3.88, 0.40026, 78, -398.49, 176.87, 0.03365, 2, 65, 9.66, 1.62, 0.96952, 78, -400.65, 168.49, 0.03048, 2, 65, 19.52, 0.53, 0.97547, 78, -401.64, 158.62, 0.02453, 3, 64, 31.79, 10.71, 1e-05, 65, 15.91, -3.51, 0.97215, 78, -405.71, 162.18, 0.02784, 2, 65, 7.5, -4.94, 0.96924, 78, -407.24, 170.58, 0.03076, 3, 64, 20.35, -2.12, 0.50724, 65, -1.21, -1.93, 0.45864, 78, -404.33, 179.32, 0.03413, 2, 64, 11.58, -5.01, 0.96281, 78, -399.08, 186.91, 0.03719, 4, 63, 39.32, -1.26, 0.0094, 64, 3.65, -7.1, 0.95064, 65, -15.14, 8.53, 8e-05, 78, -394.02, 193.36, 0.03988, 4, 62, 68.45, 10.6, 2e-05, 63, 28.51, -4.89, 0.85519, 64, -5.68, -0.54, 0.10399, 78, -382.62, 193.76, 0.0408, 3, 62, 55.5, 1.75, 0.00314, 63, 12.85, -5.75, 0.95656, 78, -367.37, 190.12, 0.04031, 3, 62, 42.25, -4.06, 0.54598, 63, -1.5, -3.84, 0.41575, 78, -354.16, 184.21, 0.03826, 2, 62, 25.37, -6.66, 0.96587, 78, -340.89, 173.45, 0.03413, 3, 61, 50.54, -6.93, 0.00136, 62, 9.76, -6.9, 0.96895, 78, -330.23, 162.05, 0.02969, 3, 61, 35.17, -7.27, 0.87985, 62, -5.6, -7.29, 0.09464, 78, -319.61, 150.95, 0.02551, 3, 60, 55.17, -17.88, 0.00178, 61, 19.1, -11.29, 0.97598, 78, -305.81, 141.78, 0.02224, 3, 60, 38.04, -15.81, 0.55151, 61, 2.53, -16.11, 0.42965, 78, -291.07, 132.81, 0.01883, 4, 59, 58.85, -14.8, 0.0025, 60, 20.38, -15.39, 0.97555, 61, -13.87, -22.67, 0.00597, 78, -275.17, 125.12, 0.01597, 4, 52, -12.01, 62.86, 0.01213, 59, 40.85, -15.01, 0.39598, 60, 2.38, -15.09, 0.57876, 78, -258.91, 117.4, 0.01313, 4, 52, -0.17, 56.93, 0.24724, 59, 27.62, -14.89, 0.70426, 60, -10.85, -14.59, 0.03768, 78, -247.07, 111.47, 0.01082, 3, 52, 10.56, 50.77, 0.47866, 59, 15.26, -14.08, 0.51299, 78, -236.34, 105.31, 0.00835, 1, 52, 1.42, -0.88, 1], "hull": 90, "edges": [58, 60, 60, 62, 74, 76, 98, 100, 100, 102, 154, 156, 102, 104, 104, 106, 106, 108, 118, 120, 112, 114, 94, 96, 96, 98, 90, 92, 92, 94, 108, 110, 110, 112, 114, 116, 116, 118, 120, 122, 122, 124, 86, 88, 88, 90, 82, 84, 84, 86, 80, 82, 76, 78, 78, 80, 70, 72, 72, 74, 66, 68, 68, 70, 62, 64, 64, 66, 54, 56, 56, 58, 52, 54, 50, 52, 50, 48, 48, 46, 46, 44, 44, 42, 38, 40, 42, 40, 36, 38, 32, 34, 34, 36, 28, 30, 30, 32, 26, 28, 24, 26, 16, 18, 18, 20, 14, 16, 12, 14, 6, 8, 8, 10, 10, 12, 2, 4, 4, 6, 2, 0, 0, 178, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 20, 22, 22, 24], "width": 292, "height": 215}}, "hair_L": {"hair_L": {"type": "mesh", "uvs": [0.21958, 0.01078, 0.39772, 0.04457, 0.52471, 0.09693, 0.66285, 0.17969, 0.79712, 0.28065, 0.91042, 0.38896, 0.98756, 0.52107, 1, 0.64148, 0.97324, 0.75111, 0.88842, 0.84403, 0.80183, 0.90922, 0.67792, 0.95603, 0.48867, 0.99606, 0.33722, 1, 0.43549, 0.91902, 0.54696, 0.8867, 0.63909, 0.81651, 0.69878, 0.72623, 0.70732, 0.6341, 0.66252, 0.53403, 0.5225, 0.42037, 0.39951, 0.31011, 0.31452, 0.21265, 0.2726, 0.14783, 0.20769, 0.09349, 0.12862, 0.06721, 0.01187, 0.0597, 0.01193, 0.01082, 0.11402, 0.00365], "triangles": [25, 28, 0, 26, 27, 28, 25, 26, 28, 24, 25, 0, 24, 0, 1, 23, 24, 1, 23, 1, 2, 22, 23, 2, 15, 16, 10, 11, 15, 10, 14, 15, 11, 12, 14, 11, 13, 14, 12, 8, 17, 7, 9, 17, 8, 16, 17, 9, 10, 16, 9, 19, 5, 6, 18, 19, 6, 18, 6, 7, 17, 18, 7, 20, 21, 4, 20, 4, 5, 19, 20, 5, 22, 2, 3, 21, 22, 3, 21, 3, 4], "vertices": [2, 52, 30.4, -29.38, 0.99985, 78, -216.5, 25.16, 0.00015, 3, 71, -3.97, 8.09, 0.02752, 52, 24.52, -44.18, 0.9635, 78, -222.38, 10.36, 0.00898, 3, 71, 10.5, 11.91, 0.32091, 52, 14.41, -55.22, 0.667, 78, -232.49, -0.68, 0.01209, 4, 71, 31.03, 13.52, 0.71884, 72, -18.56, 11.03, 0.00059, 52, -1.99, -67.69, 0.26151, 78, -248.89, -13.14, 0.01906, 3, 71, 54.8, 13.06, 0.14708, 72, 5.03, 13.91, 0.82878, 78, -269.11, -25.62, 0.02413, 2, 72, 29.51, 14.69, 0.97409, 78, -291.02, -36.55, 0.02591, 3, 72, 57.69, 11.04, 0.04239, 73, 8.07, 13.78, 0.9355, 78, -318.15, -45.02, 0.0221, 3, 73, 33.37, 13, 0.97024, 74, -13.39, 8.82, 0.01355, 78, -343.27, -48.13, 0.01621, 3, 73, 56.18, 9.26, 0.01114, 74, 9.26, 13.43, 0.98018, 78, -366.39, -47.92, 0.00868, 2, 74, 29.91, 12.57, 0.99336, 78, -386.4, -42.79, 0.00664, 3, 74, 45.01, 9.89, 0.43421, 75, -1.2, 10.45, 0.56158, 78, -400.62, -37.03, 0.00421, 1, 75, 12.75, 9.99, 1, 1, 75, 29.25, 4.74, 1, 1, 75, 38.16, -3.51, 1, 1, 75, 20.4, -9.46, 1, 2, 74, 46.38, -10.99, 0.05545, 75, 9.34, -7.62, 0.94455, 2, 74, 30.14, -8.2, 0.98309, 75, -6.44, -12.37, 0.01691, 2, 73, 49.43, -12.28, 0.04684, 74, 10.61, -9.1, 0.95316, 2, 73, 30.18, -10.24, 0.98717, 74, -8.11, -14.04, 0.01283, 2, 72, 51.4, -14.33, 0.14768, 73, 8.97, -12.35, 0.85232, 2, 72, 25.14, -16.74, 0.99737, 73, -15.63, -21.85, 0.00263, 3, 71, 45.34, -17.93, 0.50607, 72, 0.02, -18.11, 0.45434, 52, -31.04, -48.98, 0.03959, 3, 71, 24.08, -14.34, 0.53083, 72, -21.54, -17.53, 0.00023, 52, -11.21, -40.5, 0.46894, 2, 71, 10.48, -10.92, 0.2624, 52, 2.07, -36.02, 0.7376, 2, 71, -2.03, -10.15, 0.00651, 52, 13.01, -29.89, 0.99349, 1, 52, 17.98, -23.12, 1, 1, 52, 18.77, -13.68, 1, 1, 52, 29, -12.83, 1, 1, 52, 31.18, -20.84, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 8, 10, 10, 12, 16, 18, 22, 24, 26, 28, 28, 30, 30, 32, 36, 38, 52, 54, 54, 56, 48, 50, 50, 52, 44, 46, 46, 48, 42, 44, 4, 6, 6, 8, 38, 40, 40, 42, 12, 14, 14, 16, 32, 34, 34, 36, 24, 26, 18, 20, 20, 22], "width": 80, "height": 210}}, "hair_R": {"hair_R": {"type": "mesh", "uvs": [0.79015, 0, 0.9032, 0.01156, 0.99734, 0.05446, 0.94251, 0.1245, 0.84684, 0.10294, 0.78972, 0.09809, 0.71581, 0.11713, 0.61853, 0.16344, 0.50862, 0.22819, 0.42334, 0.29516, 0.3494, 0.36331, 0.27953, 0.45443, 0.24734, 0.5506, 0.24286, 0.64762, 0.24371, 0.72976, 0.27311, 0.81847, 0.31189, 0.89041, 0.3689, 0.9513, 0.44177, 0.99508, 0.02001, 1, 0.00769, 0.9863, 0.0848, 0.95661, 0.11026, 0.90481, 0.109, 0.83397, 0.02431, 0.74753, 0.00572, 0.65822, 0.02508, 0.57428, 0.06837, 0.46679, 0.14326, 0.35574, 0.22575, 0.26523, 0.32269, 0.18395, 0.43026, 0.1161, 0.54715, 0.05473, 0.67609, 0.01136], "triangles": [7, 32, 6, 32, 33, 6, 5, 0, 1, 3, 4, 2, 2, 4, 1, 4, 5, 1, 5, 33, 0, 6, 33, 5, 31, 32, 7, 19, 21, 18, 21, 17, 18, 21, 16, 17, 21, 22, 16, 19, 20, 21, 16, 22, 23, 16, 23, 15, 23, 14, 15, 23, 24, 14, 24, 25, 14, 25, 13, 14, 25, 26, 13, 13, 26, 12, 26, 27, 12, 12, 27, 11, 27, 28, 11, 11, 28, 10, 28, 29, 10, 10, 29, 9, 29, 30, 9, 9, 30, 8, 30, 31, 8, 8, 31, 7], "vertices": [1, 52, 31.1, 25.26, 1, 1, 52, 29.78, 13.35, 1, 1, 52, 22.04, 2.87, 1, 1, 52, 7.61, 7.39, 1, 1, 52, 11.07, 17.66, 1, 1, 52, 11.54, 23.66, 1, 3, 66, 5.15, 10.04, 0.07319, 52, 7.11, 31, 0.92636, 78, -239.79, 85.55, 0.00045, 3, 66, 18.76, 8.33, 0.34583, 52, -2.97, 40.31, 0.65042, 78, -249.87, 94.85, 0.00375, 3, 66, 36.03, 8.01, 0.68686, 52, -16.83, 50.62, 0.30566, 78, -263.73, 105.16, 0.00748, 3, 66, 51.98, 9.94, 0.3645, 67, 2.34, 9.67, 0.62512, 78, -277.81, 112.88, 0.01038, 2, 67, 17.91, 8.05, 0.98712, 78, -292.04, 119.4, 0.01288, 3, 67, 37.52, 8.64, 0.68952, 68, -0.68, 9.08, 0.29502, 78, -310.81, 125.12, 0.01546, 2, 68, 18.84, 8.94, 0.98345, 78, -330.25, 126.85, 0.01655, 3, 68, 38.06, 11.67, 0.66168, 69, -0.45, 12.76, 0.32159, 78, -349.63, 125.69, 0.01672, 3, 68, 54.25, 14.46, 0.00982, 69, 15.61, 9.3, 0.97331, 78, -365.99, 124.23, 0.01687, 3, 69, 33.6, 8.45, 0.87689, 70, -2.6, 10.19, 0.10724, 78, -383.42, 119.7, 0.01588, 3, 69, 48.52, 9.29, 0.00446, 70, 11.53, 5.33, 0.98098, 78, -397.42, 114.48, 0.01456, 2, 70, 24.92, 3.3, 0.98738, 78, -409.06, 107.55, 0.01262, 2, 70, 36.43, 4.56, 0.98985, 78, -417.15, 99.27, 0.01015, 2, 70, 12.33, -32.1, 0.99201, 78, -421.79, 142.9, 0.00799, 2, 70, 9.34, -31.6, 0.99188, 78, -419.17, 144.4, 0.00812, 3, 69, 56.35, -16.63, 0.00046, 70, 9.01, -21.63, 0.98661, 78, -412.59, 136.91, 0.01293, 3, 69, 46.8, -11.81, 0.11432, 70, 1.99, -13.56, 0.86948, 78, -402.04, 135.14, 0.01619, 3, 69, 32.94, -8.88, 0.92789, 70, -9.75, -5.62, 0.04836, 78, -387.93, 136.45, 0.02375, 2, 69, 14.16, -13.75, 0.968, 78, -371.44, 146.67, 0.032, 3, 68, 44.2, -12.31, 0.56255, 69, -3.7, -11.78, 0.40178, 78, -353.8, 150.09, 0.03566, 2, 68, 27.31, -13.08, 0.96544, 78, -336.9, 149.49, 0.03456, 3, 67, 48.53, -10.51, 0.0615, 68, 5.37, -12.17, 0.90814, 78, -315.11, 146.8, 0.03036, 2, 67, 25.06, -12.22, 0.97304, 78, -292.32, 140.89, 0.02696, 3, 66, 60.73, -9.6, 0.11558, 67, 5.04, -11.56, 0.86261, 78, -273.57, 133.86, 0.02181, 4, 66, 41.82, -12.45, 0.8413, 67, -13.89, -8.79, 0.00155, 52, -9.63, 70.63, 0.13997, 78, -256.53, 125.17, 0.01719, 3, 66, 24.23, -12.72, 0.50192, 52, 4.83, 60.61, 0.48719, 78, -242.07, 115.15, 0.01088, 3, 66, 7, -11.41, 0.16129, 52, 18.08, 49.52, 0.83457, 78, -228.82, 104.07, 0.00414, 2, 52, 27.84, 36.89, 0.9976, 78, -219.06, 91.43, 0.0024], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 18, 20, 20, 22, 22, 24, 28, 30, 34, 36, 44, 46, 46, 48, 62, 64, 64, 66, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 58, 60, 60, 62, 54, 56, 56, 58, 52, 54, 50, 52, 48, 50, 24, 26, 26, 28, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44], "width": 104, "height": 200}}, "head": {"head": {"type": "mesh", "uvs": [0.57879, 0.07227, 0.69827, 0.00267, 0.8128, 0.04156, 0.89667, 0.14616, 0.93493, 0.24028, 0.9436, 0.33898, 0.93519, 0.43266, 0.98495, 0.44385, 0.9944, 0.53735, 0.9128, 0.68501, 0.87417, 0.68333, 0.81819, 0.79312, 0.77851, 0.84032, 0.67173, 0.92422, 0.563, 0.99788, 0.43536, 0.99104, 0.31036, 0.90937, 0.19852, 0.82351, 0.15558, 0.76282, 0.12641, 0.6425, 0.07177, 0.6416, 0.01259, 0.52795, 0.00788, 0.44463, 0.03404, 0.39059, 0.06891, 0.38744, 0.10251, 0.41605, 0.10676, 0.33218, 0.14565, 0.22435, 0.21916, 0.1177, 0.34186, 0.00899, 0.48073, 0.00269, 0.55713, 0.13094, 0.59462, 0.13513, 0.68126, 0.10579, 0.79289, 0.11138, 0.46515, 0.08419, 0.36365, 0.0834, 0.64812, 0.50414, 0.66548, 0.4778, 0.69252, 0.45653, 0.72382, 0.44403, 0.76388, 0.43832, 0.80362, 0.44337, 0.87337, 0.47732, 0.66271, 0.49949, 0.67818, 0.47821, 0.70294, 0.46108, 0.73234, 0.45225, 0.76979, 0.4494, 0.80105, 0.45511, 0.82983, 0.46861, 0.84902, 0.48677, 0.84128, 0.45762, 0.67567, 0.50599, 0.70384, 0.5117, 0.73633, 0.51196, 0.77223, 0.51119, 0.80225, 0.50651, 0.83128, 0.49822, 0.42599, 0.48928, 0.41664, 0.4695, 0.39551, 0.45062, 0.36578, 0.43489, 0.33084, 0.42545, 0.29387, 0.42396, 0.26101, 0.42784, 0.23742, 0.44409, 0.24332, 0.46422, 0.27599, 0.48328, 0.311, 0.49002, 0.34812, 0.49031, 0.38207, 0.49047, 0.41096, 0.49054, 0.442, 0.48957, 0.42508, 0.46368, 0.40361, 0.44342, 0.37032, 0.42338, 0.33193, 0.41461, 0.29251, 0.41158, 0.25197, 0.41811, 0.21331, 0.43792, 0.66768, 0.51258, 0.7151, 0.52681, 0.76909, 0.52843, 0.82153, 0.52002, 0.86201, 0.49997, 0.42607, 0.49965, 0.37016, 0.50321, 0.31883, 0.50398, 0.26465, 0.49565, 0.22021, 0.46582, 0.4501, 0.42338, 0.28805, 0.34523, 0.24752, 0.34684, 0.1909, 0.37382, 0.19155, 0.36087, 0.23401, 0.32149, 0.27904, 0.308, 0.32022, 0.31394, 0.47652, 0.38511, 0.47778, 0.42004, 0.4, 0.34496, 0.37374, 0.37788, 0.328, 0.35913, 0.409, 0.39919, 0.36011, 0.32729, 0.43731, 0.36424, 0.6257, 0.3991, 0.79153, 0.34315, 0.82901, 0.33943, 0.89215, 0.38686, 0.86281, 0.35275, 0.88389, 0.39805, 0.84068, 0.3762, 0.80828, 0.37674, 0.64563, 0.43161, 0.6164, 0.42575, 0.72949, 0.40631, 0.70957, 0.37326, 0.667, 0.38671, 0.68883, 0.42002, 0.76762, 0.39046, 0.74832, 0.35767, 0.10272, 0.52187, 0.91898, 0.56901, 0.92594, 0.48318, 0.84558, 0.23235, 0.70836, 0.23282, 0.56353, 0.25175, 0.4272, 0.21073, 0.28477, 0.19246, 0.18125, 0.53588, 0.20193, 0.65343, 0.84969, 0.57879, 0.80868, 0.69662, 0.74743, 0.80879, 0.61228, 0.92293, 0.38568, 0.91248, 0.24635, 0.79189, 0.46038, 0.62142, 0.43078, 0.65792, 0.43165, 0.69369, 0.47517, 0.72216, 0.52305, 0.7207, 0.57354, 0.72873, 0.6162, 0.71121, 0.62229, 0.66814, 0.59705, 0.63164, 0.50598, 0.4796, 0.46942, 0.6548, 0.58868, 0.66502, 0.58433, 0.48398, 0.53035, 0.68509, 0.49533, 0.60411, 0.57735, 0.6094, 0.5112, 0.64547, 0.55212, 0.64766, 0.50675, 0.54113, 0.57562, 0.5445, 0.61487, 0.56224, 0.73385, 0.58982, 0.70831, 0.68239, 0.45983, 0.55058, 0.30491, 0.56338, 0.32004, 0.6627, 0.54568, 0.47234, 0.54097, 0.53783, 0.5376, 0.59937, 0.47108, 0.48493, 0.61446, 0.49479, 0.39558, 0.79216, 0.42562, 0.77842, 0.46662, 0.75933, 0.49593, 0.75817, 0.51733, 0.76894, 0.53964, 0.76117, 0.57188, 0.76141, 0.60258, 0.78299, 0.62813, 0.80693, 0.37692, 0.77669, 0.35295, 0.79026, 0.36314, 0.81237, 0.38891, 0.81036, 0.41288, 0.84504, 0.45322, 0.86795, 0.50836, 0.87587, 0.56648, 0.8747, 0.60824, 0.85358, 0.62982, 0.82493, 0.64, 0.79963, 0.64395, 0.82904, 0.65506, 0.81358, 0.63669, 0.81503, 0.61244, 0.8174, 0.54831, 0.81287, 0.51296, 0.81438, 0.4812, 0.80534, 0.44764, 0.80081, 0.40809, 0.80182, 0.38172, 0.7988, 0.58307, 0.81463, 0.45888, 0.83016, 0.51133, 0.84031, 0.56725, 0.83886, 0.43474, 0.89379, 0.50777, 0.88783, 0.57289, 0.90246, 0.16032, 0.46076], "triangles": [130, 96, 27, 130, 36, 129, 94, 25, 26, 26, 27, 96, 129, 36, 35, 36, 29, 30, 35, 30, 0, 31, 35, 0, 128, 32, 127, 128, 31, 32, 32, 33, 127, 126, 3, 4, 5, 111, 4, 6, 110, 5, 15, 205, 14, 137, 204, 15, 15, 204, 205, 204, 184, 205, 205, 184, 185, 185, 202, 186, 186, 203, 187, 186, 202, 203, 185, 184, 202, 184, 201, 202, 184, 183, 201, 182, 198, 183, 183, 197, 201, 183, 198, 197, 182, 199, 198, 199, 170, 198, 198, 171, 197, 198, 170, 171, 199, 179, 170, 170, 179, 171, 187, 188, 190, 203, 200, 187, 187, 193, 188, 187, 200, 193, 202, 194, 203, 202, 195, 194, 203, 194, 200, 194, 195, 174, 195, 196, 174, 174, 175, 194, 174, 173, 175, 196, 173, 174, 196, 172, 173, 13, 136, 135, 135, 190, 191, 135, 136, 190, 12, 135, 11, 191, 189, 135, 189, 161, 135, 135, 134, 11, 9, 124, 8, 11, 134, 10, 135, 161, 134, 173, 143, 175, 175, 143, 144, 144, 150, 145, 150, 147, 146, 143, 152, 144, 144, 152, 150, 143, 142, 152, 172, 142, 173, 173, 142, 143, 141, 140, 149, 138, 164, 180, 180, 164, 179, 179, 164, 141, 164, 140, 141, 163, 140, 164, 20, 123, 19, 18, 19, 132, 20, 21, 123, 25, 22, 23, 22, 123, 21, 207, 80, 90, 80, 94, 79, 66, 80, 79, 65, 79, 78, 131, 90, 89, 90, 66, 67, 68, 65, 64, 131, 89, 163, 89, 67, 68, 132, 163, 164, 139, 140, 163, 139, 163, 162, 140, 139, 149, 149, 155, 152, 155, 149, 153, 149, 139, 153, 153, 162, 157, 150, 156, 154, 156, 167, 154, 147, 154, 159, 158, 166, 151, 169, 151, 116, 158, 169, 159, 169, 115, 38, 160, 147, 159, 159, 169, 37, 159, 37, 81, 38, 37, 169, 44, 37, 38, 38, 115, 39, 45, 38, 39, 39, 120, 40, 159, 81, 82, 81, 44, 53, 81, 53, 54, 53, 45, 54, 45, 46, 54, 46, 40, 47, 46, 39, 40, 40, 117, 41, 47, 41, 48, 9, 10, 124, 10, 133, 124, 83, 84, 133, 133, 85, 124, 133, 84, 85, 124, 85, 125, 124, 125, 8, 125, 85, 43, 85, 58, 51, 85, 84, 58, 84, 57, 58, 56, 49, 57, 57, 50, 58, 57, 49, 50, 85, 51, 43, 58, 50, 51, 50, 52, 51, 51, 52, 43, 125, 43, 6, 50, 49, 52, 49, 42, 52, 52, 42, 113, 43, 52, 112, 49, 48, 42, 48, 41, 42, 134, 133, 10, 160, 83, 133, 83, 57, 84, 83, 56, 57, 82, 55, 83, 83, 55, 56, 56, 55, 47, 47, 48, 56, 56, 48, 49, 160, 82, 83, 82, 54, 55, 134, 160, 133, 159, 82, 160, 41, 121, 42, 82, 81, 54, 55, 54, 46, 47, 55, 46, 47, 40, 41, 53, 44, 45, 45, 39, 46, 37, 44, 81, 44, 38, 45, 167, 158, 154, 154, 158, 159, 167, 166, 158, 158, 151, 169, 146, 147, 160, 147, 150, 154, 167, 157, 166, 166, 165, 151, 155, 153, 167, 155, 167, 156, 153, 157, 167, 157, 148, 166, 166, 148, 165, 152, 156, 150, 152, 155, 156, 151, 165, 116, 139, 162, 153, 162, 168, 157, 162, 163, 87, 87, 163, 88, 163, 89, 88, 88, 89, 69, 88, 69, 70, 89, 68, 69, 68, 64, 69, 90, 67, 89, 67, 65, 68, 90, 80, 66, 67, 66, 65, 66, 79, 65, 19, 131, 132, 132, 131, 163, 131, 207, 90, 123, 207, 131, 80, 207, 94, 19, 123, 131, 123, 25, 207, 123, 22, 25, 207, 25, 94, 23, 24, 25, 18, 132, 138, 138, 132, 164, 172, 171, 141, 171, 179, 141, 172, 141, 142, 149, 152, 142, 149, 142, 141, 145, 150, 146, 161, 146, 160, 177, 145, 189, 177, 176, 145, 145, 176, 144, 176, 175, 144, 145, 146, 161, 189, 145, 161, 161, 160, 134, 125, 7, 8, 125, 6, 7, 6, 43, 112, 188, 192, 190, 190, 192, 191, 188, 193, 192, 193, 178, 192, 178, 193, 177, 192, 189, 191, 192, 178, 189, 193, 200, 177, 177, 200, 176, 200, 194, 176, 194, 175, 176, 178, 177, 189, 201, 196, 202, 201, 197, 196, 197, 172, 196, 197, 171, 172, 202, 196, 195, 137, 138, 181, 137, 183, 204, 183, 181, 182, 183, 137, 181, 181, 138, 180, 204, 183, 184, 181, 199, 182, 181, 180, 199, 180, 179, 199, 17, 138, 16, 138, 137, 16, 17, 18, 138, 206, 187, 136, 136, 187, 190, 206, 186, 187, 13, 135, 12, 14, 136, 13, 14, 206, 136, 14, 205, 206, 206, 205, 186, 205, 185, 186, 16, 137, 15, 126, 34, 3, 4, 111, 126, 127, 34, 126, 34, 2, 3, 34, 33, 2, 127, 33, 34, 33, 32, 0, 33, 1, 2, 33, 0, 1, 32, 31, 0, 129, 35, 31, 35, 36, 30, 28, 29, 36, 130, 28, 36, 27, 28, 130, 126, 108, 127, 168, 148, 157, 148, 100, 165, 87, 72, 86, 162, 73, 168, 168, 100, 148, 86, 73, 162, 69, 64, 63, 64, 65, 78, 78, 79, 93, 64, 78, 77, 75, 76, 104, 76, 77, 102, 129, 98, 130, 129, 31, 128, 128, 106, 129, 119, 128, 127, 78, 103, 77, 63, 77, 76, 62, 76, 75, 74, 75, 91, 61, 62, 75, 62, 63, 76, 62, 70, 63, 70, 62, 61, 61, 75, 74, 63, 64, 77, 60, 74, 59, 74, 91, 168, 60, 61, 74, 60, 71, 61, 71, 70, 61, 59, 72, 60, 86, 59, 73, 168, 73, 74, 72, 59, 86, 73, 59, 74, 72, 71, 60, 87, 71, 72, 70, 69, 63, 87, 70, 71, 88, 70, 87, 162, 87, 86, 168, 91, 100, 91, 99, 100, 106, 101, 129, 104, 101, 106, 99, 106, 128, 76, 102, 104, 102, 101, 104, 75, 104, 91, 91, 104, 99, 99, 104, 106, 116, 100, 99, 92, 97, 98, 78, 92, 103, 92, 98, 103, 102, 105, 101, 101, 105, 129, 105, 98, 129, 103, 98, 105, 98, 97, 130, 77, 103, 102, 103, 105, 102, 94, 93, 79, 78, 93, 92, 94, 95, 93, 94, 26, 95, 95, 96, 93, 95, 26, 96, 92, 93, 97, 93, 96, 97, 97, 96, 130, 99, 128, 107, 165, 100, 116, 169, 116, 115, 115, 120, 39, 40, 120, 117, 116, 107, 115, 115, 119, 120, 115, 107, 119, 107, 116, 99, 120, 118, 117, 120, 119, 118, 119, 107, 128, 127, 118, 119, 42, 121, 114, 41, 117, 121, 117, 122, 121, 117, 118, 122, 121, 108, 114, 121, 122, 108, 114, 108, 109, 118, 127, 122, 122, 127, 108, 109, 108, 126, 42, 114, 113, 52, 113, 112, 112, 110, 6, 112, 113, 110, 113, 111, 110, 110, 111, 5, 114, 109, 113, 113, 109, 111, 111, 109, 126], "vertices": [2, 6, 135.77, -7.04, 0.965, 77, -292.79, 54.46, 0.035, 2, 6, 150.69, -25.09, 0.965, 77, -277.87, 36.4, 0.035, 2, 6, 144.79, -44.09, 0.97, 77, -283.77, 17.4, 0.03, 2, 6, 125.91, -59.23, 0.985, 77, -302.65, 2.27, 0.015, 2, 6, 108.41, -66.88, 0.99, 77, -320.14, -5.38, 0.01, 2, 6, 89.65, -69.85, 0.99, 77, -338.91, -8.35, 0.01, 2, 6, 71.61, -70, 0.99, 77, -356.95, -8.51, 0.01, 3, 6, 70.14, -78.17, 0.97128, 77, -358.42, -16.67, 0.00981, 78, -308.28, -16.67, 0.01891, 3, 6, 52.38, -81.18, 0.96309, 77, -376.18, -19.68, 0.00973, 78, -326.04, -19.68, 0.02718, 3, 6, 23.03, -70.46, 0.97321, 77, -405.53, -8.96, 0.00983, 78, -355.39, -8.96, 0.01696, 2, 6, 22.83, -64.24, 0.99, 77, -405.73, -2.74, 0.01, 2, 6, 1.07, -57.02, 0.99, 77, -427.49, 4.48, 0.01, 2, 6, -8.5, -51.41, 0.98675, 77, -437.05, 10.09, 0.01325, 2, 6, -25.99, -35.62, 0.97414, 77, -454.54, 25.87, 0.02586, 2, 6, -41.54, -19.36, 0.965, 77, -470.1, 42.14, 0.035, 2, 6, -41.95, 1.23, 0.965, 77, -470.51, 62.73, 0.035, 2, 6, -28.01, 22.59, 0.97393, 77, -456.56, 84.09, 0.02607, 2, 6, -13.08, 41.91, 0.98471, 77, -441.64, 103.41, 0.01529, 2, 6, -2.05, 49.78, 0.99, 77, -430.61, 111.27, 0.01, 2, 6, 20.58, 56.39, 0.99, 77, -407.98, 117.88, 0.01, 3, 6, 20.02, 65.17, 0.97942, 77, -408.54, 126.67, 0.00989, 78, -358.4, 126.67, 0.01069, 3, 6, 40.96, 76.49, 0.96157, 77, -387.59, 137.98, 0.00971, 78, -337.45, 137.98, 0.02872, 3, 6, 56.84, 78.58, 0.95935, 77, -371.72, 140.08, 0.00969, 78, -321.58, 140.08, 0.03096, 3, 6, 67.53, 75.25, 0.96725, 77, -361.03, 136.75, 0.00977, 78, -310.89, 136.75, 0.02298, 3, 6, 68.61, 69.7, 0.98192, 77, -359.95, 131.2, 0.00992, 78, -309.81, 131.2, 0.00816, 2, 6, 63.58, 63.85, 0.99, 77, -364.98, 125.35, 0.01, 2, 6, 79.69, 64.52, 0.99, 77, -348.87, 126.02, 0.01, 2, 6, 100.84, 60.01, 0.99, 77, -327.72, 121.51, 0.01, 2, 6, 122.24, 49.93, 0.985, 77, -306.32, 111.43, 0.015, 2, 6, 144.69, 31.99, 0.97, 77, -283.87, 93.48, 0.03, 2, 6, 147.76, 9.81, 0.965, 77, -280.8, 71.3, 0.035, 2, 6, 124.25, -4.51, 0.965, 77, -304.31, 56.99, 0.035, 2, 6, 123.95, -10.59, 0.965, 77, -304.61, 50.91, 0.035, 2, 6, 130.73, -24.02, 0.965, 77, -297.83, 37.48, 0.035, 2, 6, 131.17, -42.02, 0.97, 77, -297.39, 19.48, 0.03, 2, 6, 131.96, 11, 0.965, 77, -296.6, 72.5, 0.035, 2, 6, 130.74, 27.3, 0.97, 77, -297.82, 88.79, 0.03, 2, 6, 54.07, -25.09, 0.96, 77, -374.49, 36.4, 0.04, 2, 6, 59.34, -27.46, 0.96, 77, -369.22, 34.04, 0.04, 2, 6, 63.78, -31.45, 0.96, 77, -364.78, 30.04, 0.04, 2, 6, 66.59, -36.27, 0.96, 77, -361.97, 25.22, 0.04, 2, 6, 68.22, -42.61, 0.96, 77, -360.34, 18.89, 0.04, 2, 6, 67.79, -49.07, 0.96301, 77, -360.77, 12.43, 0.03699, 2, 6, 62.23, -60.8, 0.9706, 77, -366.33, 0.69, 0.0294, 2, 6, 55.16, -27.36, 0.96, 77, -373.4, 34.14, 0.04, 2, 6, 59.44, -29.5, 0.96, 77, -369.12, 32, 0.04, 2, 6, 63.05, -33.2, 0.96, 77, -365.51, 28.3, 0.04, 2, 6, 65.13, -37.77, 0.96, 77, -363.43, 23.72, 0.04, 2, 6, 66.18, -43.74, 0.96, 77, -362.38, 17.76, 0.04, 2, 6, 65.51, -48.84, 0.96291, 77, -363.05, 12.65, 0.03709, 2, 6, 63.31, -53.68, 0.96609, 77, -365.24, 7.82, 0.03391, 2, 6, 60.1, -57.05, 0.96847, 77, -368.46, 4.45, 0.03153, 2, 6, 65.57, -55.34, 0.96696, 77, -362.99, 6.16, 0.03304, 2, 6, 54.09, -29.54, 0.96, 77, -374.47, 31.95, 0.04, 2, 6, 53.37, -34.15, 0.96, 77, -375.19, 27.34, 0.04, 2, 6, 53.76, -39.37, 0.96, 77, -374.8, 22.13, 0.04, 2, 6, 54.39, -45.12, 0.96123, 77, -374.17, 16.38, 0.03877, 2, 6, 55.69, -49.86, 0.96408, 77, -372.87, 11.64, 0.03592, 2, 6, 57.67, -54.38, 0.96687, 77, -370.89, 7.11, 0.03313, 2, 6, 53.93, 10.78, 0.96, 77, -374.63, 72.28, 0.04, 2, 6, 57.58, 12.6, 0.96, 77, -370.98, 74.1, 0.04, 2, 6, 60.91, 16.29, 0.96, 77, -367.65, 77.79, 0.04, 2, 6, 63.52, 21.32, 0.96, 77, -365.04, 82.81, 0.04, 2, 6, 64.86, 27.07, 0.96, 77, -363.7, 88.57, 0.04, 2, 6, 64.65, 33.03, 0.96318, 77, -363.91, 94.53, 0.03682, 2, 6, 63.46, 38.24, 0.9664, 77, -365.1, 99.73, 0.0336, 2, 6, 60.03, 41.76, 0.96857, 77, -368.52, 103.26, 0.03143, 2, 6, 56.26, 40.49, 0.96746, 77, -372.3, 101.99, 0.03254, 2, 6, 53.05, 34.94, 0.96393, 77, -375.5, 96.44, 0.03607, 2, 6, 52.24, 29.22, 0.96041, 77, -376.32, 90.72, 0.03959, 2, 6, 52.68, 23.26, 0.96, 77, -375.88, 84.76, 0.04, 2, 6, 53.11, 17.81, 0.96, 77, -375.45, 79.31, 0.04, 2, 6, 53.48, 13.17, 0.96, 77, -375.08, 74.67, 0.04, 2, 6, 54.08, 8.21, 0.96, 77, -374.47, 69.71, 0.04, 2, 6, 58.81, 11.34, 0.96, 77, -369.75, 72.84, 0.04, 2, 6, 62.4, 15.11, 0.96, 77, -366.16, 76.61, 0.04, 2, 6, 65.78, 20.77, 0.96, 77, -362.78, 82.27, 0.04, 2, 6, 66.95, 27.07, 0.96, 77, -361.61, 88.57, 0.04, 2, 6, 66.99, 33.44, 0.96339, 77, -361.56, 94.94, 0.03661, 2, 6, 65.2, 39.84, 0.96729, 77, -363.36, 101.34, 0.03271, 2, 6, 60.89, 45.73, 0.97101, 77, -367.67, 107.23, 0.02899, 2, 6, 52.72, -28.37, 0.96, 77, -375.84, 33.13, 0.04, 2, 6, 50.63, -36.2, 0.96, 77, -377.92, 25.29, 0.04, 2, 6, 51.05, -44.89, 0.96124, 77, -377.51, 16.61, 0.03876, 2, 6, 53.37, -53.17, 0.9663, 77, -375.19, 8.33, 0.0337, 2, 6, 57.75, -59.34, 0.96995, 77, -370.81, 2.15, 0.03005, 2, 6, 51.94, 10.6, 0.96, 77, -376.62, 72.1, 0.04, 2, 6, 50.51, 19.52, 0.96, 77, -378.05, 81.01, 0.04, 2, 6, 49.67, 27.74, 0.96, 77, -378.89, 89.24, 0.04, 2, 6, 50.53, 36.56, 0.9649, 77, -378.02, 98.06, 0.0351, 2, 6, 55.64, 44.17, 0.96965, 77, -372.91, 105.67, 0.03035, 3, 44, 59.88, 38.27, 0.00013, 47, 31.04, -28.66, 0.01513, 49, 15, -4.35, 0.98473, 4, 47, 13.76, -4.02, 0.36537, 48, 2.52, -3.19, 0.6258, 49, -15.02, -6.45, 0.00633, 78, -298.79, 96.72, 0.0025, 4, 47, 7.72, -1.53, 0.95505, 48, -3.41, -5.91, 0.03213, 49, -20.26, -10.34, 0.00409, 78, -299.64, 103.2, 0.00873, 5, 46, 78.86, -30.6, 5e-05, 47, -2.73, -2.36, 0.95554, 48, -9.94, -14.12, 0.00814, 49, -24.94, -19.73, 0.01527, 78, -305.57, 111.85, 0.021, 3, 47, -1.59, -0.16, 0.97592, 49, -26.24, -17.61, 0.00292, 78, -303.08, 111.95, 0.02116, 3, 45, 85.28, -40.03, 0.00076, 47, 7.81, 3.8, 0.98752, 78, -294.97, 105.77, 0.01172, 4, 45, 77.59, -39.84, 0.00334, 47, 15.48, 3.07, 0.49879, 48, -1.49, 2.9, 0.49369, 78, -291.79, 98.76, 0.00418, 3, 45, 71.81, -36.4, 0.00569, 48, 5.09, 4.32, 0.99386, 78, -292.37, 92.06, 0.00045, 3, 45, 53.23, -14.61, 0.00743, 48, 33.53, 1.04, 0.00336, 49, 14.43, 4.13, 0.98921, 3, 44, 56.2, 35.67, 0.00019, 47, 35.34, -29.97, 0.00468, 49, 18.34, -1.33, 0.99513, 3, 45, 61.96, -26.23, 0.00755, 48, 19.22, 3.59, 0.50244, 49, -0.09, 3.65, 0.49002, 3, 47, 23.6, -15.54, 0.0251, 48, 17.66, -3.85, 0.5159, 49, -0.07, -3.95, 0.459, 3, 47, 18.46, -9.16, 0.06824, 48, 9.49, -3.26, 0.90873, 49, -8.19, -5.07, 0.02304, 3, 47, 27.01, -21.65, 0.0199, 48, 24.46, -5.53, 0.02956, 49, 6.92, -4.18, 0.95054, 3, 45, 66.73, -31.7, 0.00716, 48, 12, 4.34, 0.98304, 49, -7.31, 2.88, 0.0098, 3, 45, 57.68, -20.62, 0.00759, 48, 26.18, 2.4, 0.0246, 49, 6.96, 3.93, 0.96781, 2, 45, 31.77, -3.49, 0.00901, 46, 14.34, -3.02, 0.99099, 5, 44, 17.88, -0.4, 0.05528, 45, 3, -3.93, 0.93715, 46, -14.41, -4.4, 0.00097, 48, 77.57, 27.45, 0.00496, 78, -291.62, 15.98, 0.00164, 4, 44, 12.83, -3.77, 0.68301, 45, -2.89, -2.43, 0.30753, 48, 82.91, 30.36, 0.00369, 78, -290.4, 10.02, 0.00577, 3, 44, -0.36, -0.27, 0.98103, 48, 95.74, 25.71, 1e-05, 78, -298.63, -0.87, 0.01896, 3, 44, 6.82, -3.96, 0.98602, 48, 88.91, 30.02, 0.00128, 78, -292.5, 4.39, 0.0127, 4, 44, -0.15, 2.25, 0.96104, 45, -7.1, 11.25, 0.01413, 46, -24.99, 10.44, 0.00749, 78, -300.88, 0.28, 0.01734, 4, 44, 7.95, 1.67, 0.94288, 45, -2.11, 4.84, 0.04649, 46, -19.8, 4.2, 0.00246, 78, -297.28, 7.56, 0.00818, 4, 44, 12.55, 4.13, 0.33177, 45, 2.8, 3.06, 0.66235, 46, -14.84, 2.58, 0.00205, 78, -297.82, 12.75, 0.00383, 3, 44, 31.11, 25.4, 0.00976, 46, 13.36, 3.93, 0.98997, 47, 58.88, -43.43, 0.00027, 3, 44, 35.81, 26.53, 0.00258, 46, 17.43, 1.32, 0.9971, 47, 55.09, -40.42, 0.00032, 3, 44, 21.28, 14.94, 0.03234, 45, 16.67, 3.81, 0.56071, 46, -0.99, 3.78, 0.40695, 2, 45, 17.39, -3.27, 0.56384, 46, -0.05, -3.27, 0.43616, 2, 45, 24.71, -3.32, 0.03285, 46, 7.27, -3.08, 0.96715, 3, 44, 25.92, 20.26, 0.01795, 45, 23.73, 3.91, 0.01845, 46, 6.06, 4.11, 0.9636, 4, 44, 17.19, 9.44, 0.05505, 45, 9.85, 3.17, 0.93719, 46, -7.79, 2.92, 0.00764, 78, -300.99, 19.06, 0.00012, 2, 45, 10.49, -3.82, 0.98577, 46, -6.92, -4.05, 0.01423, 2, 6, 43.34, 62.12, 0.99, 77, -385.22, 123.62, 0.01, 2, 6, 45.3, -69.59, 0.99, 77, -383.26, -8.09, 0.01, 2, 6, 61.82, -69.33, 0.99, 77, -366.74, -7.83, 0.01, 2, 6, 108.73, -52.41, 0.96835, 77, -319.83, 9.09, 0.03165, 2, 6, 106.79, -30.4, 0.96, 77, -321.77, 31.09, 0.04, 2, 6, 101.22, -7.47, 0.96, 77, -327.34, 54.02, 0.04, 2, 6, 107.24, 15.06, 0.96, 77, -321.32, 76.56, 0.04, 2, 6, 108.82, 38.2, 0.96947, 77, -319.74, 99.7, 0.03053, 2, 6, 41.72, 49.3, 0.96774, 77, -386.84, 110.8, 0.03226, 2, 6, 19.5, 44.1, 0.96761, 77, -409.05, 105.59, 0.03239, 2, 6, 42.5, -58.63, 0.9637, 77, -386.06, 2.87, 0.0363, 2, 6, 19.4, -53.94, 0.96419, 77, -409.15, 7.55, 0.03581, 2, 6, -2.88, -45.92, 0.96929, 77, -431.44, 15.58, 0.03071, 2, 6, -26.54, -26.06, 0.96452, 77, -455.1, 35.43, 0.03548, 2, 6, -27.59, 10.46, 0.96456, 77, -456.15, 71.96, 0.03544, 2, 6, -6.39, 34.75, 0.96966, 77, -434.95, 96.24, 0.03034, 2, 6, 29.11, 3.14, 0.96, 77, -399.45, 64.64, 0.04, 2, 6, 21.72, 7.31, 0.96, 77, -406.83, 68.81, 0.04, 2, 6, 14.89, 6.59, 0.96, 77, -413.67, 68.09, 0.04, 2, 6, 10.03, -0.85, 0.96, 77, -418.53, 60.65, 0.04, 2, 6, 10.95, -8.5, 0.96, 77, -417.6, 52.99, 0.04, 2, 6, 10.1, -16.73, 0.96, 77, -418.46, 44.76, 0.04, 2, 6, 14.02, -23.3, 0.96, 77, -414.54, 38.2, 0.04, 2, 6, 22.35, -23.58, 0.96, 77, -406.21, 37.92, 0.04, 2, 6, 28.99, -18.95, 0.96, 77, -399.57, 42.55, 0.04, 2, 6, 56.85, -1.9, 0.96, 77, -371.71, 59.6, 0.04, 2, 6, 22.84, 1.16, 0.955, 77, -405.72, 62.66, 0.045, 2, 6, 22.49, -18.14, 0.955, 77, -406.07, 43.36, 0.045, 2, 6, 57.07, -14.54, 0.96, 77, -371.49, 46.96, 0.04, 2, 6, 17.86, -9.1, 0.955, 77, -410.69, 52.39, 0.045, 2, 6, 32.89, -2.19, 0.957, 77, -395.67, 59.31, 0.043, 2, 6, 32.98, -15.43, 0.957, 77, -395.58, 46.07, 0.043, 2, 6, 25.19, -5.4, 0.951, 77, -403.37, 56.1, 0.049, 2, 6, 25.32, -11.99, 0.951, 77, -403.24, 49.5, 0.049, 2, 6, 45.09, -3.01, 0.959, 77, -383.47, 58.49, 0.041, 2, 6, 45.37, -14.11, 0.959, 77, -383.19, 47.39, 0.041, 2, 6, 42.51, -20.69, 0.96, 77, -386.05, 40.81, 0.04, 2, 6, 38.83, -40.22, 0.96, 77, -389.73, 21.27, 0.04, 2, 6, 20.78, -37.61, 0.96, 77, -407.78, 23.89, 0.04, 2, 6, 42.65, 4.37, 0.96, 77, -385.91, 65.87, 0.04, 2, 6, 38.12, 29.02, 0.96, 77, -390.44, 90.52, 0.04, 2, 6, 19.32, 25, 0.96, 77, -409.24, 86.5, 0.04, 2, 6, 58.78, -8.15, 0.959, 77, -369.78, 53.35, 0.041, 2, 6, 46.18, -8.44, 0.957, 77, -382.38, 53.05, 0.043, 2, 6, 34.36, -8.89, 0.954, 77, -394.2, 52.61, 0.046, 2, 6, 55.36, 3.62, 0.96, 77, -373.2, 65.12, 0.04, 2, 6, 55.41, -19.54, 0.96, 77, -373.15, 41.95, 0.04, 2, 6, -4.43, 10.8, 0.96, 77, -432.99, 72.3, 0.04, 2, 6, -1.4, 6.2, 0.96, 77, -429.96, 67.7, 0.04, 2, 6, 2.8, -0.07, 0.96, 77, -425.76, 61.43, 0.04, 2, 6, 3.42, -4.75, 0.96, 77, -425.14, 56.74, 0.04, 2, 6, 1.65, -8.36, 0.96, 77, -426.91, 53.14, 0.04, 2, 6, 3.43, -11.81, 0.96, 77, -425.13, 49.68, 0.04, 2, 6, 3.82, -16.99, 0.96, 77, -424.74, 44.51, 0.04, 2, 6, 0.11, -22.26, 0.96, 77, -428.45, 39.24, 0.04, 2, 6, -4.13, -26.75, 0.96, 77, -432.69, 34.75, 0.04, 2, 6, -1.73, 14.04, 0.96, 77, -430.28, 75.54, 0.04, 2, 6, -4.64, 17.67, 0.96, 77, -433.2, 79.17, 0.04, 2, 6, -8.74, 15.68, 0.96, 77, -437.3, 77.18, 0.04, 2, 6, -8.01, 11.58, 0.96, 77, -436.56, 73.08, 0.04, 2, 6, -14.32, 7.18, 0.96, 77, -442.88, 68.67, 0.04, 2, 6, -18.16, 0.34, 0.96, 77, -446.72, 61.83, 0.04, 2, 6, -18.93, -8.64, 0.96, 77, -447.49, 52.86, 0.04, 2, 6, -17.93, -17.94, 0.96, 77, -446.49, 43.55, 0.04, 2, 6, -13.32, -24.3, 0.96, 77, -441.88, 37.19, 0.04, 2, 6, -7.55, -27.3, 0.96, 77, -436.11, 34.19, 0.04, 2, 6, -2.57, -28.53, 0.96, 77, -431.13, 32.96, 0.04, 2, 6, -8.15, -29.64, 0.96, 77, -436.71, 31.86, 0.04, 2, 6, -5.04, -31.17, 0.96, 77, -433.6, 30.33, 0.04, 2, 6, -5.57, -28.25, 0.96, 77, -434.12, 33.25, 0.04, 2, 6, -6.34, -24.4, 0.96, 77, -434.9, 37.1, 0.04, 2, 6, -6.34, -14.04, 0.96, 77, -434.9, 47.46, 0.04, 2, 6, -7.11, -8.39, 0.96, 77, -435.66, 53.11, 0.04, 2, 6, -5.8, -3.15, 0.96, 77, -434.36, 58.35, 0.04, 2, 6, -5.39, 2.31, 0.96, 77, -433.95, 63.81, 0.04, 2, 6, -6.11, 8.64, 0.96, 77, -434.67, 70.14, 0.04, 2, 6, -5.89, 12.92, 0.96, 77, -434.45, 74.42, 0.04, 2, 6, -6.21, -19.64, 0.96, 77, -434.77, 41.86, 0.04, 2, 6, -10.85, 0.04, 0.96, 77, -439.41, 61.53, 0.04, 2, 6, -12.09, -8.54, 0.96, 77, -440.65, 52.96, 0.04, 2, 6, -11.06, -17.49, 0.96, 77, -439.62, 44.01, 0.04, 2, 6, -23.35, 2.89, 0.96, 77, -451.91, 64.39, 0.04, 2, 6, -21.23, -8.73, 0.96, 77, -449.79, 52.76, 0.04, 2, 6, -23.15, -19.42, 0.96, 77, -451.71, 42.08, 0.04, 2, 6, 55.81, 53.86, 0.97553, 77, -372.75, 115.36, 0.02447], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 16, 18, 28, 30, 30, 32, 32, 34, 40, 42, 42, 44, 44, 46, 58, 60, 60, 0, 62, 64, 64, 66, 66, 68, 68, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 34, 36, 36, 38, 38, 40, 24, 26, 26, 28, 46, 48, 48, 50, 50, 52, 52, 54, 62, 70, 70, 72, 54, 56, 72, 56, 56, 58, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 84, 104, 104, 86, 88, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 102, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 118, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 162, 164, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 176, 178, 178, 180, 184, 186, 186, 188, 190, 192, 192, 194, 194, 196, 198, 200, 200, 182, 184, 206, 206, 204, 182, 208, 208, 204, 196, 210, 210, 202, 198, 212, 212, 202, 216, 218, 218, 222, 222, 220, 224, 226, 226, 228, 230, 232, 232, 214, 214, 238, 238, 236, 230, 240, 240, 234, 228, 242, 242, 234, 216, 244, 244, 236, 220, 224, 50, 246, 246, 38, 20, 248, 12, 250, 250, 248, 262, 264, 266, 268, 268, 270, 270, 272, 274, 276, 276, 264, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 298, 304, 304, 300, 306, 298, 300, 308, 310, 312, 296, 314, 314, 306, 302, 316, 316, 308, 320, 322, 326, 328, 330, 332, 332, 334, 338, 318, 336, 324, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 376, 384, 386, 388, 390, 390, 392, 392, 394, 394, 396, 396, 398, 386, 400, 400, 388, 402, 404, 404, 406, 408, 410, 410, 412], "width": 161, "height": 192}}, "head2": {"head": {"type": "mesh", "uvs": [0.20193, 0.65343, 0.32004, 0.6627, 0.43078, 0.65792, 0.46942, 0.6548, 0.53035, 0.68509, 0.58868, 0.66502, 0.62229, 0.66814, 0.70831, 0.68239, 0.80868, 0.69662, 0.87417, 0.68333, 0.81819, 0.79312, 0.77851, 0.84032, 0.67173, 0.92422, 0.563, 0.99788, 0.43536, 0.99104, 0.31036, 0.90937, 0.19852, 0.82351, 0.15558, 0.76282, 0.12641, 0.6425, 0.74743, 0.80879, 0.61228, 0.92293, 0.38568, 0.91248, 0.24635, 0.79189, 0.43165, 0.69369, 0.47517, 0.72216, 0.52305, 0.7207, 0.57354, 0.72873, 0.6162, 0.71121, 0.39558, 0.79216, 0.42562, 0.77842, 0.46662, 0.75933, 0.49593, 0.75817, 0.51733, 0.76894, 0.53964, 0.76117, 0.57188, 0.76141, 0.60258, 0.78299, 0.62813, 0.80693, 0.37692, 0.77669, 0.35295, 0.79026, 0.36314, 0.81237, 0.38891, 0.81036, 0.41288, 0.84504, 0.45322, 0.86795, 0.50836, 0.87587, 0.56648, 0.8747, 0.60824, 0.85358, 0.62982, 0.82493, 0.64, 0.79963, 0.64395, 0.82904, 0.65506, 0.81358, 0.63669, 0.81503, 0.61244, 0.8174, 0.54831, 0.81287, 0.51296, 0.81438, 0.4812, 0.80534, 0.44764, 0.80081, 0.40809, 0.80182, 0.38172, 0.7988, 0.58307, 0.81463, 0.45888, 0.83016, 0.51133, 0.84031, 0.56725, 0.83886, 0.43474, 0.89379, 0.50777, 0.88783, 0.57289, 0.90246], "triangles": [23, 2, 3, 1, 2, 23, 27, 5, 6, 27, 6, 7, 3, 24, 23, 3, 4, 24, 25, 24, 4, 26, 4, 5, 26, 5, 27, 25, 4, 26, 31, 24, 25, 30, 23, 24, 30, 24, 31, 33, 25, 26, 31, 25, 33, 34, 33, 26, 17, 18, 0, 32, 31, 33, 37, 1, 23, 29, 37, 23, 30, 29, 23, 27, 34, 26, 35, 34, 27, 38, 1, 37, 22, 0, 1, 22, 1, 38, 17, 0, 22, 28, 37, 29, 10, 8, 9, 57, 37, 28, 38, 37, 57, 47, 27, 7, 35, 27, 47, 55, 29, 30, 56, 28, 29, 56, 29, 55, 57, 28, 56, 54, 30, 31, 54, 31, 32, 55, 30, 54, 36, 35, 47, 19, 7, 8, 19, 8, 10, 47, 7, 19, 40, 57, 56, 39, 38, 57, 39, 57, 40, 52, 33, 34, 58, 52, 34, 32, 33, 52, 53, 54, 32, 49, 47, 19, 52, 53, 32, 35, 58, 34, 51, 58, 35, 50, 36, 47, 50, 47, 49, 36, 51, 35, 51, 36, 50, 16, 17, 22, 46, 51, 50, 48, 50, 49, 46, 50, 48, 59, 55, 54, 61, 52, 58, 60, 54, 53, 59, 54, 60, 60, 53, 52, 60, 52, 61, 11, 19, 10, 41, 56, 55, 41, 55, 59, 40, 56, 41, 45, 58, 51, 45, 51, 46, 61, 58, 45, 42, 41, 59, 42, 59, 60, 44, 60, 61, 44, 61, 45, 43, 42, 60, 43, 60, 44, 63, 42, 43, 62, 41, 42, 62, 42, 63, 64, 44, 45, 45, 46, 48, 63, 43, 44, 64, 63, 44, 22, 21, 15, 39, 22, 38, 16, 22, 15, 41, 21, 39, 41, 39, 40, 21, 41, 62, 21, 22, 39, 20, 45, 48, 64, 45, 20, 19, 20, 48, 19, 48, 49, 12, 20, 19, 12, 19, 11, 14, 62, 63, 21, 62, 14, 15, 21, 14, 13, 63, 64, 13, 64, 20, 14, 63, 13, 13, 20, 12], "vertices": [2, 6, 19.5, 44.1, 0.96761, 77, -409.05, 105.59, 0.03239, 2, 6, 19.32, 25, 0.96, 77, -409.24, 86.5, 0.04, 2, 6, 21.72, 7.31, 0.96, 77, -406.83, 68.81, 0.04, 2, 6, 22.84, 1.16, 0.955, 77, -405.72, 62.66, 0.045, 2, 6, 17.86, -9.1, 0.955, 77, -410.69, 52.39, 0.045, 2, 6, 22.49, -18.14, 0.955, 77, -406.07, 43.36, 0.045, 2, 6, 22.35, -23.58, 0.96, 77, -406.21, 37.92, 0.04, 2, 6, 20.78, -37.61, 0.96, 77, -407.78, 23.89, 0.04, 2, 6, 19.4, -53.94, 0.96419, 77, -409.15, 7.55, 0.03581, 2, 6, 22.83, -64.24, 0.99, 77, -405.73, -2.74, 0.01, 2, 6, 1.07, -57.02, 0.99, 77, -427.49, 4.48, 0.01, 2, 6, -8.5, -51.41, 0.98675, 77, -437.05, 10.09, 0.01325, 2, 6, -25.99, -35.62, 0.97414, 77, -454.54, 25.87, 0.02586, 2, 6, -41.54, -19.36, 0.965, 77, -470.1, 42.14, 0.035, 2, 6, -41.95, 1.23, 0.965, 77, -470.51, 62.73, 0.035, 2, 6, -28.01, 22.59, 0.97393, 77, -456.56, 84.09, 0.02607, 2, 6, -13.08, 41.91, 0.98471, 77, -441.64, 103.41, 0.01529, 2, 6, -2.05, 49.78, 0.99, 77, -430.61, 111.27, 0.01, 2, 6, 20.58, 56.39, 0.99, 77, -407.98, 117.88, 0.01, 2, 6, -2.88, -45.92, 0.96929, 77, -431.44, 15.58, 0.03071, 2, 6, -26.54, -26.06, 0.96452, 77, -455.1, 35.43, 0.03548, 2, 6, -27.59, 10.46, 0.96456, 77, -456.15, 71.96, 0.03544, 2, 6, -6.39, 34.75, 0.96966, 77, -434.95, 96.24, 0.03034, 2, 6, 14.89, 6.59, 0.96, 77, -413.67, 68.09, 0.04, 2, 6, 10.03, -0.85, 0.96, 77, -418.53, 60.65, 0.04, 2, 6, 10.95, -8.5, 0.96, 77, -417.6, 52.99, 0.04, 2, 6, 10.1, -16.73, 0.96, 77, -418.46, 44.76, 0.04, 2, 6, 14.02, -23.3, 0.96, 77, -414.54, 38.2, 0.04, 2, 6, -4.43, 10.8, 0.96, 77, -432.99, 72.3, 0.04, 2, 6, -1.4, 6.2, 0.96, 77, -429.96, 67.7, 0.04, 2, 6, 2.8, -0.07, 0.96, 77, -425.76, 61.43, 0.04, 2, 6, 3.42, -4.75, 0.96, 77, -425.14, 56.74, 0.04, 2, 6, 1.65, -8.36, 0.96, 77, -426.91, 53.14, 0.04, 2, 6, 3.43, -11.81, 0.96, 77, -425.13, 49.68, 0.04, 2, 6, 3.82, -16.99, 0.96, 77, -424.74, 44.51, 0.04, 2, 6, 0.11, -22.26, 0.96, 77, -428.45, 39.24, 0.04, 2, 6, -4.13, -26.75, 0.96, 77, -432.69, 34.75, 0.04, 2, 6, -1.73, 14.04, 0.96, 77, -430.28, 75.54, 0.04, 2, 6, -4.64, 17.67, 0.96, 77, -433.2, 79.17, 0.04, 2, 6, -8.74, 15.68, 0.96, 77, -437.3, 77.18, 0.04, 2, 6, -8.01, 11.58, 0.96, 77, -436.56, 73.08, 0.04, 2, 6, -14.32, 7.18, 0.96, 77, -442.88, 68.67, 0.04, 2, 6, -18.16, 0.34, 0.96, 77, -446.72, 61.83, 0.04, 2, 6, -18.93, -8.64, 0.96, 77, -447.49, 52.86, 0.04, 2, 6, -17.93, -17.94, 0.96, 77, -446.49, 43.55, 0.04, 2, 6, -13.32, -24.3, 0.96, 77, -441.88, 37.19, 0.04, 2, 6, -7.55, -27.3, 0.96, 77, -436.11, 34.19, 0.04, 2, 6, -2.57, -28.53, 0.96, 77, -431.13, 32.96, 0.04, 2, 6, -8.15, -29.64, 0.96, 77, -436.71, 31.86, 0.04, 2, 6, -5.04, -31.17, 0.96, 77, -433.6, 30.33, 0.04, 2, 6, -5.57, -28.25, 0.96, 77, -434.12, 33.25, 0.04, 2, 6, -6.34, -24.4, 0.96, 77, -434.9, 37.1, 0.04, 2, 6, -6.34, -14.04, 0.96, 77, -434.9, 47.46, 0.04, 2, 6, -7.11, -8.39, 0.96, 77, -435.66, 53.11, 0.04, 2, 6, -5.8, -3.15, 0.96, 77, -434.36, 58.35, 0.04, 2, 6, -5.39, 2.31, 0.96, 77, -433.95, 63.81, 0.04, 2, 6, -6.11, 8.64, 0.96, 77, -434.67, 70.14, 0.04, 2, 6, -5.89, 12.92, 0.96, 77, -434.45, 74.42, 0.04, 2, 6, -6.21, -19.64, 0.96, 77, -434.77, 41.86, 0.04, 2, 6, -10.85, 0.04, 0.96, 77, -439.41, 61.53, 0.04, 2, 6, -12.09, -8.54, 0.96, 77, -440.65, 52.96, 0.04, 2, 6, -11.06, -17.49, 0.96, 77, -439.62, 44.01, 0.04, 2, 6, -23.35, 2.89, 0.96, 77, -451.91, 64.39, 0.04, 2, 6, -21.23, -8.73, 0.96, 77, -449.79, 52.76, 0.04, 2, 6, -23.15, -19.42, 0.96, 77, -451.71, 42.08, 0.04], "hull": 19, "edges": [26, 28, 28, 30, 30, 32, 18, 20, 20, 22, 32, 34, 34, 36, 22, 24, 24, 26, 16, 38, 38, 40, 42, 44, 44, 0, 4, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 12, 6, 8, 8, 10, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 102, 116, 116, 104, 118, 120, 120, 122, 124, 126, 126, 128, 36, 0, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 4, 6, 10, 12], "width": 161, "height": 192}}}}], "animations": {"idle": {"bones": {"ALL": {"translate": [{"x": 7.26, "curve": [0.444, 7.26, 0.889, -5.93, 0.444, 0, 0.889, 0]}, {"time": 1.3333, "x": -5.93, "curve": [1.778, -5.93, 2.222, 7.26, 1.778, 0, 2.222, 0]}, {"time": 2.6667, "x": 7.26, "curve": [3.111, 7.26, 3.556, -5.93, 3.111, 0, 3.556, 0]}, {"time": 4, "x": -5.93, "curve": [4.444, -5.93, 4.889, 7.26, 4.444, 0, 4.889, 0]}, {"time": 5.3333, "x": 7.26, "curve": [5.667, 7.26, 6, -24.11, 5.667, 0, 6, 0]}, {"time": 6.3333, "x": -24.11, "curve": [6.778, -24.11, 7.222, -8.43, 6.778, 0, 7.222, 0]}, {"time": 7.6667, "x": -8.43, "curve": [8.111, -8.43, 8.556, -24.11, 8.111, 0, 8.556, 0]}, {"time": 9, "x": -24.11, "curve": [9.444, -24.11, 9.889, 7.26, 9.444, 0, 9.889, 0]}, {"time": 10.3333, "x": 7.26, "curve": [10.778, 7.26, 11.222, -5.93, 10.778, 0, 11.222, 0]}, {"time": 11.6667, "x": -5.93, "curve": [12.111, -5.93, 12.556, 7.26, 12.111, 0, 12.556, 0]}, {"time": 13, "x": 7.26}]}, "ALL2": {"translate": [{"y": -13.4, "curve": [0.444, 0, 0.889, 0, 0.444, -13.4, 0.889, 14.83]}, {"time": 1.3333, "y": 14.83, "curve": [1.778, 0, 2.222, 0, 1.778, 14.83, 2.222, -13.4]}, {"time": 2.6667, "y": -13.4, "curve": [3.111, 0, 3.556, 0, 3.111, -13.4, 3.556, 14.83]}, {"time": 4, "y": 14.83, "curve": [4.444, 0, 4.889, 0, 4.444, 14.83, 4.889, -13.4]}, {"time": 5.3333, "y": -13.4, "curve": [5.667, 0, 6, 0, 5.667, -13.4, 6, 25.58]}, {"time": 6.3333, "y": 25.58, "curve": [6.778, 0, 7.222, 0, 6.778, 25.58, 7.222, 6.09]}, {"time": 7.6667, "y": 6.09, "curve": [8.111, 0, 8.556, 0, 8.111, 6.09, 8.556, 25.58]}, {"time": 9, "y": 25.58, "curve": [9.444, 0, 9.889, 0, 9.444, 25.58, 9.889, -13.4]}, {"time": 10.3333, "y": -13.4, "curve": [10.778, 0, 11.222, 0, 10.778, -13.4, 11.222, 14.83]}, {"time": 11.6667, "y": 14.83, "curve": [12.111, 0, 12.556, 0, 12.111, 14.83, 12.556, -13.4]}, {"time": 13, "y": -13.4}]}, "body": {"rotate": [{"value": 2.44, "curve": [0.444, 2.44, 0.889, -3.71]}, {"time": 1.3333, "value": -3.71, "curve": [1.778, -3.71, 2.222, 2.44]}, {"time": 2.6667, "value": 2.44, "curve": [3.111, 2.44, 3.556, -3.71]}, {"time": 4, "value": -3.71, "curve": [4.444, -3.71, 4.889, 2.44]}, {"time": 5.3333, "value": 2.44, "curve": [5.667, 2.44, 6, -7.33]}, {"time": 6.3333, "value": -7.33, "curve": [6.778, -7.33, 7.222, -2.45]}, {"time": 7.6667, "value": -2.45, "curve": [8.111, -2.45, 8.556, -7.33]}, {"time": 9, "value": -7.33, "curve": [9.444, -7.33, 9.889, 2.44]}, {"time": 10.3333, "value": 2.44, "curve": [10.778, 2.44, 11.222, -3.71]}, {"time": 11.6667, "value": -3.71, "curve": [12.111, -3.71, 12.556, 2.44]}, {"time": 13, "value": 2.44}], "translate": [{"y": -9.82, "curve": [0.057, 0, 0.112, 0, 0.057, -10.35, 0.112, -10.76]}, {"time": 0.1667, "y": -10.76, "curve": [0.611, 0, 1.056, 0, 0.611, -10.76, 1.056, 9.29]}, {"time": 1.5, "y": 9.29, "curve": [1.944, 0, 2.389, 0, 1.944, 9.29, 2.389, -10.76]}, {"time": 2.8333, "y": -10.76, "curve": [3.278, 0, 3.722, 0, 3.278, -10.76, 3.722, 9.29]}, {"time": 4.1667, "y": 9.29, "curve": [4.611, 0, 5.056, 0, 4.611, 9.29, 5.056, -10.76]}, {"time": 5.5, "y": -10.76, "curve": [5.833, 0, 6.167, 0, 5.833, -10.76, 6.167, 9.29]}, {"time": 6.5, "y": 9.29, "curve": [6.944, 0, 7.389, 0, 6.944, 9.29, 7.389, -0.73]}, {"time": 7.8333, "y": -0.73, "curve": [8.278, 0, 8.722, 0, 8.278, -0.73, 8.722, 9.29]}, {"time": 9.1667, "y": 9.29, "curve": [9.611, 0, 10.056, 0, 9.611, 9.29, 10.056, -10.76]}, {"time": 10.5, "y": -10.76, "curve": [10.944, 0, 11.389, 0, 10.944, -10.76, 11.389, 9.29]}, {"time": 11.8333, "y": 9.29, "curve": [12.223, 0, 12.613, 0, 12.223, 9.29, 12.613, -6.02]}, {"time": 13, "y": -9.82}], "scale": [{"y": 1.048, "curve": [0.057, 1, 0.112, 1, 0.057, 1.051, 0.112, 1.053]}, {"time": 0.1667, "y": 1.053, "curve": [0.611, 1, 1.056, 1, 0.611, 1.053, 1.056, 0.95]}, {"time": 1.5, "y": 0.95, "curve": [1.944, 1, 2.389, 1, 1.944, 0.95, 2.389, 1.053]}, {"time": 2.8333, "y": 1.053, "curve": [3.278, 1, 3.722, 1, 3.278, 1.053, 3.722, 0.95]}, {"time": 4.1667, "y": 0.95, "curve": [4.611, 1, 5.056, 1, 4.611, 0.95, 5.056, 1.053]}, {"time": 5.5, "y": 1.053, "curve": [5.833, 1, 6.167, 1, 5.833, 1.053, 6.167, 0.95]}, {"time": 6.5, "y": 0.95, "curve": [6.944, 1, 7.389, 1, 6.944, 0.95, 7.389, 1.001]}, {"time": 7.8333, "y": 1.001, "curve": [8.278, 1, 8.722, 1, 8.278, 1.001, 8.722, 0.95]}, {"time": 9.1667, "y": 0.95, "curve": [9.611, 1, 10.056, 1, 9.611, 0.95, 10.056, 1.053]}, {"time": 10.5, "y": 1.053, "curve": [10.944, 1, 11.389, 1, 10.944, 1.053, 11.389, 0.95]}, {"time": 11.8333, "y": 0.95, "curve": [12.223, 1, 12.613, 1, 12.223, 0.95, 12.613, 1.029]}, {"time": 13, "y": 1.048}]}, "body2": {"rotate": [{"value": -1.48, "curve": [0.444, -1.48, 0.889, 1.58]}, {"time": 1.3333, "value": 1.58, "curve": [1.778, 1.58, 2.222, -1.48]}, {"time": 2.6667, "value": -1.48, "curve": [3.111, -1.48, 3.556, 1.58]}, {"time": 4, "value": 1.58, "curve": [4.444, 1.58, 4.889, -1.48]}, {"time": 5.3333, "value": -1.48, "curve": [5.667, -1.48, 6, 0.51]}, {"time": 6.3333, "value": 0.51, "curve": [6.778, 0.51, 7.222, -0.48]}, {"time": 7.6667, "value": -0.48, "curve": [8.111, -0.48, 8.556, 0.51]}, {"time": 9, "value": 0.51, "curve": [9.444, 0.51, 9.889, -1.48]}, {"time": 10.3333, "value": -1.48, "curve": [10.778, -1.48, 11.222, 1.58]}, {"time": 11.6667, "value": 1.58, "curve": [12.111, 1.58, 12.556, -1.48]}, {"time": 13, "value": -1.48}], "translate": [{"x": -6.95, "curve": [0.114, -8.21, 0.224, -9.15, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -9.15, "curve": [0.778, -9.15, 1.222, 4.58, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 4.58, "curve": [2.111, 4.58, 2.556, -9.15, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -9.15, "curve": [3.444, -9.15, 3.889, 4.58, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 4.58, "curve": [4.778, 4.58, 5.222, -9.15, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -9.15, "curve": [6, -9.15, 6.333, 4.58, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 4.58, "curve": [7.111, 4.58, 7.556, -2.29, 7.111, 0, 7.556, 0]}, {"time": 8, "x": -2.29, "curve": [8.444, -2.29, 8.889, 4.58, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 4.58, "curve": [9.778, 4.58, 10.222, -9.15, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -9.15, "curve": [11.111, -9.15, 11.556, 4.58, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 4.58, "curve": [12.335, 4.58, 12.67, -3.11, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -6.95}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.114, 1.002, 0.224, 1, 0.114, 1.002, 0.224, 1]}, {"time": 0.3333, "curve": [0.778, 1, 1.222, 1.027, 0.778, 1, 1.222, 1.027]}, {"time": 1.6667, "x": 1.027, "y": 1.027, "curve": [2.111, 1.027, 2.556, 1, 2.111, 1.027, 2.556, 1]}, {"time": 3, "curve": [3.444, 1, 3.889, 1.027, 3.444, 1, 3.889, 1.027]}, {"time": 4.3333, "x": 1.027, "y": 1.027, "curve": [4.778, 1.027, 5.222, 1, 4.778, 1.027, 5.222, 1]}, {"time": 5.6667, "curve": [6, 1, 6.333, 1.027, 6, 1, 6.333, 1.027]}, {"time": 6.6667, "x": 1.027, "y": 1.027, "curve": [7.111, 1.027, 7.556, 1.014, 7.111, 1.027, 7.556, 1.014]}, {"time": 8, "x": 1.014, "y": 1.014, "curve": [8.444, 1.014, 8.889, 1.027, 8.444, 1.014, 8.889, 1.027]}, {"time": 9.3333, "x": 1.027, "y": 1.027, "curve": [9.778, 1.027, 10.222, 1, 9.778, 1.027, 10.222, 1]}, {"time": 10.6667, "curve": [11.111, 1, 11.556, 1.027, 11.111, 1, 11.556, 1.027]}, {"time": 12, "x": 1.027, "y": 1.027, "curve": [12.335, 1.027, 12.67, 1.012, 12.335, 1.027, 12.67, 1.012]}, {"time": 13, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.23, "curve": [0.114, -1.63, 0.224, -1.93]}, {"time": 0.3333, "value": -1.93, "curve": [0.778, -1.93, 1.222, 2.43]}, {"time": 1.6667, "value": 2.43, "curve": [2.111, 2.43, 2.556, -1.93]}, {"time": 3, "value": -1.93, "curve": [3.444, -1.93, 3.889, 2.43]}, {"time": 4.3333, "value": 2.43, "curve": [4.778, 2.43, 5.222, -1.93]}, {"time": 5.6667, "value": -1.93, "curve": [6, -1.93, 6.333, 5.14]}, {"time": 6.6667, "value": 5.14, "curve": [7.111, 5.14, 7.556, 3.1]}, {"time": 8, "value": 3.1, "curve": [8.444, 3.1, 8.889, 5.14]}, {"time": 9.3333, "value": 5.14, "curve": [9.778, 5.14, 10.222, -1.93]}, {"time": 10.6667, "value": -1.93, "curve": [11.111, -1.93, 11.556, 2.43]}, {"time": 12, "value": 2.43, "curve": [12.335, 2.43, 12.67, -0.01]}, {"time": 13, "value": -1.23}]}, "head": {"rotate": [{"value": -0.54, "curve": [0.168, -1.3, 0.334, -1.93]}, {"time": 0.5, "value": -1.93, "curve": [0.944, -1.93, 1.389, 2.43]}, {"time": 1.8333, "value": 2.43, "curve": [2.278, 2.43, 2.722, -1.93]}, {"time": 3.1667, "value": -1.93, "curve": [3.611, -1.93, 4.056, 2.43]}, {"time": 4.5, "value": 2.43, "curve": [4.944, 2.43, 5.389, -1.93]}, {"time": 5.8333, "value": -1.93, "curve": [6.167, -1.93, 6.5, 2.29]}, {"time": 6.8333, "value": 2.29, "curve": [7.278, 2.29, 7.722, 1.68]}, {"time": 8.1667, "value": 1.68, "curve": [8.611, 1.68, 9.056, 2.29]}, {"time": 9.5, "value": 2.29, "curve": [9.944, 2.29, 10.389, -1.93]}, {"time": 10.8333, "value": -1.93, "curve": [11.278, -1.93, 11.722, 2.43]}, {"time": 12.1667, "value": 2.43, "curve": [12.445, 2.43, 12.724, 0.73]}, {"time": 13, "value": -0.54}]}, "tun": {"rotate": [{"value": 2.44, "curve": [0.444, 2.44, 0.889, -3.71]}, {"time": 1.3333, "value": -3.71, "curve": [1.778, -3.71, 2.222, 2.44]}, {"time": 2.6667, "value": 2.44, "curve": [3.111, 2.44, 3.556, -3.71]}, {"time": 4, "value": -3.71, "curve": [4.444, -3.71, 4.889, 2.44]}, {"time": 5.3333, "value": 2.44, "curve": [5.667, 2.44, 6, -11.07]}, {"time": 6.3333, "value": -11.07, "curve": [6.778, -11.07, 7.222, -4.32]}, {"time": 7.6667, "value": -4.32, "curve": [8.111, -4.32, 8.556, -11.07]}, {"time": 9, "value": -11.07, "curve": [9.444, -11.07, 9.889, 2.44]}, {"time": 10.3333, "value": 2.44, "curve": [10.778, 2.44, 11.222, -3.71]}, {"time": 11.6667, "value": -3.71, "curve": [12.111, -3.71, 12.556, 2.44]}, {"time": 13, "value": 2.44}]}, "leg_L3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 5.56]}, {"time": 7.6667, "value": 5.56, "curve": [8.111, 5.56, 8.556, 0]}, {"time": 9}]}, "leg_L4": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, -0.01]}, {"time": 7.6667, "value": -0.01, "curve": [8.111, -0.01, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "foot_R2": {"rotate": [{"value": 1.86, "curve": [0.444, 1.86, 0.889, -2.29]}, {"time": 1.3333, "value": -2.29, "curve": [1.778, -2.29, 2.222, 1.86]}, {"time": 2.6667, "value": 1.86, "curve": [3.111, 1.86, 3.556, -2.29]}, {"time": 4, "value": -2.29, "curve": [4.444, -2.29, 4.889, 1.86]}, {"time": 5.3333, "value": 1.86, "curve": [5.667, 1.86, 6, -2.29]}, {"time": 6.3333, "value": -2.29, "curve": [6.778, -2.29, 7.222, 1.86]}, {"time": 7.6667, "value": 1.86, "curve": [8.111, 1.86, 8.556, -2.29]}, {"time": 9, "value": -2.29, "curve": [9.444, -2.29, 9.889, 1.86]}, {"time": 10.3333, "value": 1.86, "curve": [10.778, 1.86, 11.222, -2.29]}, {"time": 11.6667, "value": -2.29, "curve": [12.111, -2.29, 12.556, 1.86]}, {"time": 13, "value": 1.86}]}, "leg_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 3.32]}, {"time": 7.6667, "value": 3.32, "curve": [8.111, 3.32, 8.556, 0]}, {"time": 9}]}, "leg_R4": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 5.28]}, {"time": 7.6667, "value": 5.28, "curve": [8.111, 5.28, 8.556, 0]}, {"time": 9}]}, "sh_L": {"translate": [{"x": -3.23, "curve": [0.114, -4.29, 0.224, -5.08, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.08, "curve": [0.778, -5.08, 1.222, 6.48, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.48, "curve": [2.111, 6.48, 2.556, -5.08, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.08, "curve": [3.444, -5.08, 3.889, 6.48, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.48, "curve": [4.778, 6.48, 5.222, -5.08, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.08, "curve": [6, -5.08, 6.333, 6.48, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.48, "curve": [7.111, 6.48, 7.556, 0.7, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.7, "curve": [8.444, 0.7, 8.889, 6.48, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.48, "curve": [9.778, 6.48, 10.222, -5.08, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.08, "curve": [11.111, -5.08, 11.556, 6.48, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.48, "curve": [12.335, 6.48, 12.67, 0.01, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.23}]}, "sh_R": {"translate": [{"x": -3.23, "curve": [0.114, -4.29, 0.224, -5.08, 0.114, 0, 0.224, 0]}, {"time": 0.3333, "x": -5.08, "curve": [0.778, -5.08, 1.222, 6.48, 0.778, 0, 1.222, 0]}, {"time": 1.6667, "x": 6.48, "curve": [2.111, 6.48, 2.556, -5.08, 2.111, 0, 2.556, 0]}, {"time": 3, "x": -5.08, "curve": [3.444, -5.08, 3.889, 6.48, 3.444, 0, 3.889, 0]}, {"time": 4.3333, "x": 6.48, "curve": [4.778, 6.48, 5.222, -5.08, 4.778, 0, 5.222, 0]}, {"time": 5.6667, "x": -5.08, "curve": [6, -5.08, 6.333, 6.48, 6, 0, 6.333, 0]}, {"time": 6.6667, "x": 6.48, "curve": [7.111, 6.48, 7.556, 0.7, 7.111, 0, 7.556, 0]}, {"time": 8, "x": 0.7, "curve": [8.444, 0.7, 8.889, 6.48, 8.444, 0, 8.889, 0]}, {"time": 9.3333, "x": 6.48, "curve": [9.778, 6.48, 10.222, -5.08, 9.778, 0, 10.222, 0]}, {"time": 10.6667, "x": -5.08, "curve": [11.111, -5.08, 11.556, 6.48, 11.111, 0, 11.556, 0]}, {"time": 12, "x": 6.48, "curve": [12.335, 6.48, 12.67, 0.01, 12.335, 0, 12.67, 0]}, {"time": 13, "x": -3.23}]}, "arm_R2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0.98]}, {"time": 7.6667, "value": 0.98, "curve": [8.111, 0.98, 8.556, 0]}, {"time": 9}]}, "arm_R3": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -2.2]}, {"time": 7.6667, "value": -2.2, "curve": [8.111, -2.2, 8.556, 0]}, {"time": 9}]}, "arm_L2": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, -0.8]}, {"time": 7.6667, "value": -0.8, "curve": [8.111, -0.8, 8.556, 0]}, {"time": 9}]}, "arm_L3": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, 1.6]}, {"time": 7.6667, "value": 1.6, "curve": [8.111, 1.6, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "hand_L": {"rotate": [{"value": -0.89, "curve": [0.444, -0.89, 0.889, 0.98]}, {"time": 1.3333, "value": 0.98, "curve": [1.778, 0.98, 2.222, -0.89]}, {"time": 2.6667, "value": -0.89, "curve": [3.111, -0.89, 3.556, 0.98]}, {"time": 4, "value": 0.98, "curve": [4.444, 0.98, 4.889, -0.89]}, {"time": 5.3333, "value": -0.89, "curve": [5.667, -0.89, 6, 0.98]}, {"time": 6.3333, "value": 0.98, "curve": [6.778, 0.98, 7.222, 0.04]}, {"time": 7.6667, "value": 0.04, "curve": [8.111, 0.04, 8.556, 0.98]}, {"time": 9, "value": 0.98, "curve": [9.444, 0.98, 9.889, -0.89]}, {"time": 10.3333, "value": -0.89, "curve": [10.778, -0.89, 11.222, 0.98]}, {"time": 11.6667, "value": 0.98, "curve": [12.111, 0.98, 12.556, -0.89]}, {"time": 13, "value": -0.89}]}, "bone24": {"rotate": [{"value": -0.02, "curve": "stepped"}, {"time": 6.3333, "value": -0.02, "curve": [6.778, -0.02, 7.222, -0.92]}, {"time": 7.6667, "value": -0.92, "curve": [8.111, -0.92, 8.556, -0.02]}, {"time": 9, "value": -0.02}]}, "bone25": {"rotate": [{"value": 0.09, "curve": "stepped"}, {"time": 6.3333, "value": 0.09, "curve": [6.778, 0.09, 7.222, 2.04]}, {"time": 7.6667, "value": 2.04, "curve": [8.111, 2.04, 8.556, 0.09]}, {"time": 9, "value": 0.09}]}, "arm_L": {"translate": [{"x": -9.07, "curve": [0.168, -13.96, 0.334, -17.97, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -17.97, "curve": [0.944, -17.97, 1.389, 10.02, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 10.02, "curve": [2.278, 10.02, 2.722, -17.97, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -17.97, "curve": [3.611, -17.97, 4.056, 10.02, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 10.02, "curve": [4.944, 10.02, 5.389, -17.97, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -17.97, "curve": [6.167, -17.97, 6.5, 10.02, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 10.02, "curve": [7.278, 10.02, 7.722, -3.97, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -3.97, "curve": [8.611, -3.97, 9.056, 10.02, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 10.02, "curve": [9.944, 10.02, 10.389, -17.97, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -17.97, "curve": [11.278, -17.97, 11.722, 10.02, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 10.02, "curve": [12.445, 10.02, 12.724, -0.86, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -9.07}]}, "bone26": {"rotate": [{"value": 0.12, "curve": "stepped"}, {"time": 6.3333, "value": 0.12, "curve": [6.778, 0.12, 7.222, 0.39]}, {"time": 7.6667, "value": 0.39, "curve": [8.111, 0.39, 8.556, 0.12]}, {"time": 9, "value": 0.12}]}, "bone27": {"rotate": [{"value": 0.15, "curve": "stepped"}, {"time": 6.3333, "value": 0.15, "curve": [6.778, 0.15, 7.222, -1.59]}, {"time": 7.6667, "value": -1.59, "curve": [8.111, -1.59, 8.556, 0.15]}, {"time": 9, "value": 0.15}]}, "RU_L": {"translate": [{"x": -14.71, "curve": [0.168, -26.98, 0.334, -37.05, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -37.05, "curve": [0.944, -37.05, 1.389, 33.19, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 33.19, "curve": [2.278, 33.19, 2.722, -37.05, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -37.05, "curve": [3.611, -37.05, 4.056, 33.19, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 33.19, "curve": [4.944, 33.19, 5.389, -37.05, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -37.05, "curve": [6.167, -37.05, 6.5, 33.19, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 33.19, "curve": [7.278, 33.19, 7.722, -37.05, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -37.05, "curve": [8.611, -37.05, 9.056, 33.19, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 33.19, "curve": [9.944, 33.19, 10.389, -37.05, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -37.05, "curve": [11.278, -37.05, 11.722, 33.19, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 33.19, "curve": [12.445, 33.19, 12.724, 5.88, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -14.71}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.167, 1.029, 0.333, 0.928, 0.167, 0.979, 0.333, 1.072]}, {"time": 0.5, "x": 0.928, "y": 1.072, "curve": [0.722, 0.928, 0.944, 1.109, 0.722, 1.072, 0.944, 0.906]}, {"time": 1.1667, "x": 1.109, "y": 0.906, "curve": [1.389, 1.109, 1.611, 0.928, 1.389, 0.906, 1.611, 1.072]}, {"time": 1.8333, "x": 0.928, "y": 1.072, "curve": [2.056, 0.928, 2.278, 1.109, 2.056, 1.072, 2.278, 0.906]}, {"time": 2.5, "x": 1.109, "y": 0.906, "curve": [2.722, 1.109, 2.944, 0.928, 2.722, 0.906, 2.944, 1.072]}, {"time": 3.1667, "x": 0.928, "y": 1.072, "curve": [3.389, 0.928, 3.611, 1.109, 3.389, 1.072, 3.611, 0.906]}, {"time": 3.8333, "x": 1.109, "y": 0.906, "curve": [4.056, 1.109, 4.278, 0.928, 4.056, 0.906, 4.278, 1.072]}, {"time": 4.5, "x": 0.928, "y": 1.072, "curve": [4.722, 0.928, 4.944, 1.109, 4.722, 1.072, 4.944, 0.906]}, {"time": 5.1667, "x": 1.109, "y": 0.906, "curve": [5.389, 1.109, 5.611, 0.928, 5.389, 0.906, 5.611, 1.072]}, {"time": 5.8333, "x": 0.928, "y": 1.072, "curve": [6, 0.928, 6.167, 1.109, 6, 1.072, 6.167, 0.906]}, {"time": 6.3333, "x": 1.109, "y": 0.906, "curve": [6.5, 1.109, 6.667, 0.928, 6.5, 0.906, 6.667, 1.072]}, {"time": 6.8333, "x": 0.928, "y": 1.072, "curve": [7.056, 0.928, 7.278, 1.109, 7.056, 1.072, 7.278, 0.906]}, {"time": 7.5, "x": 1.109, "y": 0.906, "curve": [7.722, 1.109, 7.944, 0.928, 7.722, 0.906, 7.944, 1.072]}, {"time": 8.1667, "x": 0.928, "y": 1.072, "curve": [8.389, 0.928, 8.611, 1.109, 8.389, 1.072, 8.611, 0.906]}, {"time": 8.8333, "x": 1.109, "y": 0.906, "curve": [9.056, 1.109, 9.278, 0.928, 9.056, 0.906, 9.278, 1.072]}, {"time": 9.5, "x": 0.928, "y": 1.072, "curve": [9.722, 0.928, 9.944, 1.109, 9.722, 1.072, 9.944, 0.906]}, {"time": 10.1667, "x": 1.109, "y": 0.906, "curve": [10.389, 1.109, 10.611, 0.928, 10.389, 0.906, 10.611, 1.072]}, {"time": 10.8333, "x": 0.928, "y": 1.072, "curve": [11.056, 0.928, 11.278, 1.109, 11.056, 1.072, 11.278, 0.906]}, {"time": 11.5, "x": 1.109, "y": 0.906, "curve": [11.722, 1.109, 11.944, 0.928, 11.722, 0.906, 11.944, 1.072]}, {"time": 12.1667, "x": 0.928, "y": 1.072, "curve": [12.389, 0.928, 12.611, 1.109, 12.389, 1.072, 12.611, 0.906]}, {"time": 12.8333, "x": 1.109, "y": 0.906, "curve": [12.889, 1.109, 12.944, 1.097, 12.889, 0.906, 12.944, 0.917]}, {"time": 13, "x": 1.08, "y": 0.932}], "shear": [{"time": 1.8333, "curve": [2.278, 0, 2.722, 0, 2.278, 0, 2.722, -2.4]}, {"time": 3.1667, "y": -2.4, "curve": [3.611, 0, 4.056, 0, 3.611, -2.4, 4.056, 0]}, {"time": 4.5}]}, "RU_L2": {"translate": [{"x": 3.92, "curve": [0.225, -11.99, 0.446, -28.11, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -28.11, "curve": [1.111, -28.11, 1.556, 35.95, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 35.95, "curve": [2.444, 35.95, 2.889, -28.11, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -28.11, "curve": [3.778, -28.11, 4.222, 35.95, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 35.95, "curve": [5.111, 35.95, 5.556, -28.11, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -28.11, "curve": [6.333, -28.11, 6.667, 35.95, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 35.95, "curve": [7.444, 35.95, 7.889, -28.11, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -28.11, "curve": [8.778, -28.11, 9.222, 35.95, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 35.95, "curve": [10.111, 35.95, 10.556, -28.11, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -28.11, "curve": [11.444, -28.11, 11.889, 35.95, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 35.95, "curve": [12.557, 35.95, 12.781, 20.04, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 3.92}], "scale": [{"x": 1.109, "y": 0.906, "curve": [0.222, 1.109, 0.444, 0.928, 0.222, 0.906, 0.444, 1.072]}, {"time": 0.6667, "x": 0.928, "y": 1.072, "curve": [0.889, 0.928, 1.111, 1.109, 0.889, 1.072, 1.111, 0.906]}, {"time": 1.3333, "x": 1.109, "y": 0.906, "curve": [1.556, 1.109, 1.778, 0.928, 1.556, 0.906, 1.778, 1.072]}, {"time": 2, "x": 0.928, "y": 1.072, "curve": [2.222, 0.928, 2.444, 1.109, 2.222, 1.072, 2.444, 0.906]}, {"time": 2.6667, "x": 1.109, "y": 0.906, "curve": [2.889, 1.109, 3.111, 0.928, 2.889, 0.906, 3.111, 1.072]}, {"time": 3.3333, "x": 0.928, "y": 1.072, "curve": [3.556, 0.928, 3.778, 1.109, 3.556, 1.072, 3.778, 0.906]}, {"time": 4, "x": 1.109, "y": 0.906, "curve": [4.222, 1.109, 4.444, 0.928, 4.222, 0.906, 4.444, 1.072]}, {"time": 4.6667, "x": 0.928, "y": 1.072, "curve": [4.889, 0.928, 5.111, 1.109, 4.889, 1.072, 5.111, 0.906]}, {"time": 5.3333, "x": 1.109, "y": 0.906, "curve": [5.556, 1.109, 5.778, 0.928, 5.556, 0.906, 5.778, 1.072]}, {"time": 6, "x": 0.928, "y": 1.072, "curve": [6.167, 0.928, 6.333, 1.109, 6.167, 1.072, 6.333, 0.906]}, {"time": 6.5, "x": 1.109, "y": 0.906, "curve": [6.667, 1.109, 6.833, 0.928, 6.667, 0.906, 6.833, 1.072]}, {"time": 7, "x": 0.928, "y": 1.072, "curve": [7.222, 0.928, 7.444, 1.109, 7.222, 1.072, 7.444, 0.906]}, {"time": 7.6667, "x": 1.109, "y": 0.906, "curve": [7.889, 1.109, 8.111, 0.928, 7.889, 0.906, 8.111, 1.072]}, {"time": 8.3333, "x": 0.928, "y": 1.072, "curve": [8.556, 0.928, 8.778, 1.109, 8.556, 1.072, 8.778, 0.906]}, {"time": 9, "x": 1.109, "y": 0.906, "curve": [9.222, 1.109, 9.444, 0.928, 9.222, 0.906, 9.444, 1.072]}, {"time": 9.6667, "x": 0.928, "y": 1.072, "curve": [9.889, 0.928, 10.111, 1.109, 9.889, 1.072, 10.111, 0.906]}, {"time": 10.3333, "x": 1.109, "y": 0.906, "curve": [10.556, 1.109, 10.778, 0.928, 10.556, 0.906, 10.778, 1.072]}, {"time": 11, "x": 0.928, "y": 1.072, "curve": [11.222, 0.928, 11.444, 1.109, 11.222, 1.072, 11.444, 0.906]}, {"time": 11.6667, "x": 1.109, "y": 0.906, "curve": [11.889, 1.109, 12.111, 0.928, 11.889, 0.906, 12.111, 1.072]}, {"time": 12.3333, "x": 0.928, "y": 1.072, "curve": [12.556, 0.928, 12.778, 1.109, 12.556, 1.072, 12.778, 0.906]}, {"time": 13, "x": 1.109, "y": 0.906}], "shear": [{"time": 2, "curve": [2.444, 0, 2.889, 0, 2.444, 0, 2.889, -2.4]}, {"time": 3.3333, "y": -2.4, "curve": [3.778, 0, 4.222, 0, 3.778, -2.4, 4.222, 0]}, {"time": 4.6667}]}, "RU_L3": {"translate": [{"x": 9.38, "curve": [0.279, -5.42, 0.556, -25.11, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.11, "curve": [1.278, -25.11, 1.722, 25.47, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 25.47, "curve": [2.611, 25.47, 3.056, -25.11, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.11, "curve": [3.944, -25.11, 4.389, 25.47, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 25.47, "curve": [5.278, 25.47, 5.722, -25.11, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.11, "curve": [6.5, -25.11, 6.833, 25.47, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 25.47, "curve": [7.611, 25.47, 8.056, -25.11, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -25.11, "curve": [8.944, -25.11, 9.389, 25.47, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 25.47, "curve": [10.278, 25.47, 10.722, -25.11, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.11, "curve": [11.611, -25.11, 12.056, 25.47, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 25.47, "curve": [12.667, 25.47, 12.835, 18.33, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 9.38}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.056, 1.097, 0.111, 1.109, 0.056, 0.917, 0.111, 0.906]}, {"time": 0.1667, "x": 1.109, "y": 0.906, "curve": [0.389, 1.109, 0.611, 0.928, 0.389, 0.906, 0.611, 1.072]}, {"time": 0.8333, "x": 0.928, "y": 1.072, "curve": [1.056, 0.928, 1.278, 1.109, 1.056, 1.072, 1.278, 0.906]}, {"time": 1.5, "x": 1.109, "y": 0.906, "curve": [1.722, 1.109, 1.944, 0.928, 1.722, 0.906, 1.944, 1.072]}, {"time": 2.1667, "x": 0.928, "y": 1.072, "curve": [2.389, 0.928, 2.611, 1.109, 2.389, 1.072, 2.611, 0.906]}, {"time": 2.8333, "x": 1.109, "y": 0.906, "curve": [3.056, 1.109, 3.278, 0.928, 3.056, 0.906, 3.278, 1.072]}, {"time": 3.5, "x": 0.928, "y": 1.072, "curve": [3.722, 0.928, 3.944, 1.109, 3.722, 1.072, 3.944, 0.906]}, {"time": 4.1667, "x": 1.109, "y": 0.906, "curve": [4.389, 1.109, 4.611, 0.928, 4.389, 0.906, 4.611, 1.072]}, {"time": 4.8333, "x": 0.928, "y": 1.072, "curve": [5.056, 0.928, 5.278, 1.109, 5.056, 1.072, 5.278, 0.906]}, {"time": 5.5, "x": 1.109, "y": 0.906, "curve": [5.722, 1.109, 5.944, 0.928, 5.722, 0.906, 5.944, 1.072]}, {"time": 6.1667, "x": 0.928, "y": 1.072, "curve": [6.333, 0.928, 6.5, 1.109, 6.333, 1.072, 6.5, 0.906]}, {"time": 6.6667, "x": 1.109, "y": 0.906, "curve": [6.833, 1.109, 7, 0.928, 6.833, 0.906, 7, 1.072]}, {"time": 7.1667, "x": 0.928, "y": 1.072, "curve": [7.389, 0.928, 7.611, 1.109, 7.389, 1.072, 7.611, 0.906]}, {"time": 7.8333, "x": 1.109, "y": 0.906, "curve": [8.056, 1.109, 8.278, 0.928, 8.056, 0.906, 8.278, 1.072]}, {"time": 8.5, "x": 0.928, "y": 1.072, "curve": [8.722, 0.928, 8.944, 1.109, 8.722, 1.072, 8.944, 0.906]}, {"time": 9.1667, "x": 1.109, "y": 0.906, "curve": [9.389, 1.109, 9.611, 0.928, 9.389, 0.906, 9.611, 1.072]}, {"time": 9.8333, "x": 0.928, "y": 1.072, "curve": [10.056, 0.928, 10.278, 1.109, 10.056, 1.072, 10.278, 0.906]}, {"time": 10.5, "x": 1.109, "y": 0.906, "curve": [10.722, 1.109, 10.944, 0.928, 10.722, 0.906, 10.944, 1.072]}, {"time": 11.1667, "x": 0.928, "y": 1.072, "curve": [11.389, 0.928, 11.611, 1.109, 11.389, 1.072, 11.611, 0.906]}, {"time": 11.8333, "x": 1.109, "y": 0.906, "curve": [12.056, 1.109, 12.278, 0.928, 12.056, 0.906, 12.278, 1.072]}, {"time": 12.5, "x": 0.928, "y": 1.072, "curve": [12.667, 0.928, 12.833, 1.029, 12.667, 1.072, 12.833, 0.979]}, {"time": 13, "x": 1.08, "y": 0.932}], "shear": [{"time": 2.1667, "curve": [2.611, 0, 3.056, 0, 2.611, 0, 3.056, -2.4]}, {"time": 3.5, "y": -2.4, "curve": [3.944, 0, 4.389, 0, 3.944, -2.4, 4.389, 0]}, {"time": 4.8333}]}, "RU_R": {"translate": [{"x": -14.71, "curve": [0.168, -26.98, 0.334, -37.05, 0.168, 0, 0.334, 0]}, {"time": 0.5, "x": -37.05, "curve": [0.944, -37.05, 1.389, 33.19, 0.944, 0, 1.389, 0]}, {"time": 1.8333, "x": 33.19, "curve": [2.278, 33.19, 2.722, -37.05, 2.278, 0, 2.722, 0]}, {"time": 3.1667, "x": -37.05, "curve": [3.611, -37.05, 4.056, 33.19, 3.611, 0, 4.056, 0]}, {"time": 4.5, "x": 33.19, "curve": [4.944, 33.19, 5.389, -37.05, 4.944, 0, 5.389, 0]}, {"time": 5.8333, "x": -37.05, "curve": [6.167, -37.05, 6.5, 33.19, 6.167, 0, 6.5, 0]}, {"time": 6.8333, "x": 33.19, "curve": [7.278, 33.19, 7.722, -37.05, 7.278, 0, 7.722, 0]}, {"time": 8.1667, "x": -37.05, "curve": [8.611, -37.05, 9.056, 33.19, 8.611, 0, 9.056, 0]}, {"time": 9.5, "x": 33.19, "curve": [9.944, 33.19, 10.389, -37.05, 9.944, 0, 10.389, 0]}, {"time": 10.8333, "x": -37.05, "curve": [11.278, -37.05, 11.722, 33.19, 11.278, 0, 11.722, 0]}, {"time": 12.1667, "x": 33.19, "curve": [12.445, 33.19, 12.724, 5.88, 12.445, 0, 12.724, 0]}, {"time": 13, "x": -14.71}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.167, 1.029, 0.333, 0.928, 0.167, 0.979, 0.333, 1.072]}, {"time": 0.5, "x": 0.928, "y": 1.072, "curve": [0.722, 0.928, 0.944, 1.109, 0.722, 1.072, 0.944, 0.906]}, {"time": 1.1667, "x": 1.109, "y": 0.906, "curve": [1.389, 1.109, 1.611, 0.928, 1.389, 0.906, 1.611, 1.072]}, {"time": 1.8333, "x": 0.928, "y": 1.072, "curve": [2.056, 0.928, 2.278, 1.109, 2.056, 1.072, 2.278, 0.906]}, {"time": 2.5, "x": 1.109, "y": 0.906, "curve": [2.722, 1.109, 2.944, 0.928, 2.722, 0.906, 2.944, 1.072]}, {"time": 3.1667, "x": 0.928, "y": 1.072, "curve": [3.389, 0.928, 3.611, 1.109, 3.389, 1.072, 3.611, 0.906]}, {"time": 3.8333, "x": 1.109, "y": 0.906, "curve": [4.056, 1.109, 4.278, 0.928, 4.056, 0.906, 4.278, 1.072]}, {"time": 4.5, "x": 0.928, "y": 1.072, "curve": [4.722, 0.928, 4.944, 1.109, 4.722, 1.072, 4.944, 0.906]}, {"time": 5.1667, "x": 1.109, "y": 0.906, "curve": [5.389, 1.109, 5.611, 0.928, 5.389, 0.906, 5.611, 1.072]}, {"time": 5.8333, "x": 0.928, "y": 1.072, "curve": [6, 0.928, 6.167, 1.109, 6, 1.072, 6.167, 0.906]}, {"time": 6.3333, "x": 1.109, "y": 0.906, "curve": [6.5, 1.109, 6.667, 0.928, 6.5, 0.906, 6.667, 1.072]}, {"time": 6.8333, "x": 0.928, "y": 1.072, "curve": [7.056, 0.928, 7.278, 1.109, 7.056, 1.072, 7.278, 0.906]}, {"time": 7.5, "x": 1.109, "y": 0.906, "curve": [7.722, 1.109, 7.944, 0.928, 7.722, 0.906, 7.944, 1.072]}, {"time": 8.1667, "x": 0.928, "y": 1.072, "curve": [8.389, 0.928, 8.611, 1.109, 8.389, 1.072, 8.611, 0.906]}, {"time": 8.8333, "x": 1.109, "y": 0.906, "curve": [9.056, 1.109, 9.278, 0.928, 9.056, 0.906, 9.278, 1.072]}, {"time": 9.5, "x": 0.928, "y": 1.072, "curve": [9.722, 0.928, 9.944, 1.109, 9.722, 1.072, 9.944, 0.906]}, {"time": 10.1667, "x": 1.109, "y": 0.906, "curve": [10.389, 1.109, 10.611, 0.928, 10.389, 0.906, 10.611, 1.072]}, {"time": 10.8333, "x": 0.928, "y": 1.072, "curve": [11.056, 0.928, 11.278, 1.109, 11.056, 1.072, 11.278, 0.906]}, {"time": 11.5, "x": 1.109, "y": 0.906, "curve": [11.722, 1.109, 11.944, 0.928, 11.722, 0.906, 11.944, 1.072]}, {"time": 12.1667, "x": 0.928, "y": 1.072, "curve": [12.389, 0.928, 12.611, 1.109, 12.389, 1.072, 12.611, 0.906]}, {"time": 12.8333, "x": 1.109, "y": 0.906, "curve": [12.889, 1.109, 12.944, 1.097, 12.889, 0.906, 12.944, 0.917]}, {"time": 13, "x": 1.08, "y": 0.932}], "shear": [{"time": 1.8333, "curve": [2.278, 0, 2.722, 0, 2.278, 0, 2.722, -2.4]}, {"time": 3.1667, "y": -2.4, "curve": [3.611, 0, 4.056, 0, 3.611, -2.4, 4.056, 0]}, {"time": 4.5}]}, "RU_R2": {"translate": [{"x": 3.92, "curve": [0.225, -11.99, 0.446, -28.11, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -28.11, "curve": [1.111, -28.11, 1.556, 35.95, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 35.95, "curve": [2.444, 35.95, 2.889, -28.11, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -28.11, "curve": [3.778, -28.11, 4.222, 35.95, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 35.95, "curve": [5.111, 35.95, 5.556, -28.11, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -28.11, "curve": [6.333, -28.11, 6.667, 35.95, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 35.95, "curve": [7.444, 35.95, 7.889, -28.11, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": -28.11, "curve": [8.778, -28.11, 9.222, 35.95, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 35.95, "curve": [10.111, 35.95, 10.556, -28.11, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -28.11, "curve": [11.444, -28.11, 11.889, 35.95, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 35.95, "curve": [12.557, 35.95, 12.781, 20.04, 12.557, 0, 12.781, 0]}, {"time": 13, "x": 3.92}], "scale": [{"x": 1.109, "y": 0.906, "curve": [0.222, 1.109, 0.444, 0.928, 0.222, 0.906, 0.444, 1.072]}, {"time": 0.6667, "x": 0.928, "y": 1.072, "curve": [0.889, 0.928, 1.111, 1.109, 0.889, 1.072, 1.111, 0.906]}, {"time": 1.3333, "x": 1.109, "y": 0.906, "curve": [1.556, 1.109, 1.778, 0.928, 1.556, 0.906, 1.778, 1.072]}, {"time": 2, "x": 0.928, "y": 1.072, "curve": [2.222, 0.928, 2.444, 1.109, 2.222, 1.072, 2.444, 0.906]}, {"time": 2.6667, "x": 1.109, "y": 0.906, "curve": [2.889, 1.109, 3.111, 0.928, 2.889, 0.906, 3.111, 1.072]}, {"time": 3.3333, "x": 0.928, "y": 1.072, "curve": [3.556, 0.928, 3.778, 1.109, 3.556, 1.072, 3.778, 0.906]}, {"time": 4, "x": 1.109, "y": 0.906, "curve": [4.222, 1.109, 4.444, 0.928, 4.222, 0.906, 4.444, 1.072]}, {"time": 4.6667, "x": 0.928, "y": 1.072, "curve": [4.889, 0.928, 5.111, 1.109, 4.889, 1.072, 5.111, 0.906]}, {"time": 5.3333, "x": 1.109, "y": 0.906, "curve": [5.556, 1.109, 5.778, 0.928, 5.556, 0.906, 5.778, 1.072]}, {"time": 6, "x": 0.928, "y": 1.072, "curve": [6.167, 0.928, 6.333, 1.109, 6.167, 1.072, 6.333, 0.906]}, {"time": 6.5, "x": 1.109, "y": 0.906, "curve": [6.667, 1.109, 6.833, 0.928, 6.667, 0.906, 6.833, 1.072]}, {"time": 7, "x": 0.928, "y": 1.072, "curve": [7.222, 0.928, 7.444, 1.109, 7.222, 1.072, 7.444, 0.906]}, {"time": 7.6667, "x": 1.109, "y": 0.906, "curve": [7.889, 1.109, 8.111, 0.928, 7.889, 0.906, 8.111, 1.072]}, {"time": 8.3333, "x": 0.928, "y": 1.072, "curve": [8.556, 0.928, 8.778, 1.109, 8.556, 1.072, 8.778, 0.906]}, {"time": 9, "x": 1.109, "y": 0.906, "curve": [9.222, 1.109, 9.444, 0.928, 9.222, 0.906, 9.444, 1.072]}, {"time": 9.6667, "x": 0.928, "y": 1.072, "curve": [9.889, 0.928, 10.111, 1.109, 9.889, 1.072, 10.111, 0.906]}, {"time": 10.3333, "x": 1.109, "y": 0.906, "curve": [10.556, 1.109, 10.778, 0.928, 10.556, 0.906, 10.778, 1.072]}, {"time": 11, "x": 0.928, "y": 1.072, "curve": [11.222, 0.928, 11.444, 1.109, 11.222, 1.072, 11.444, 0.906]}, {"time": 11.6667, "x": 1.109, "y": 0.906, "curve": [11.889, 1.109, 12.111, 0.928, 11.889, 0.906, 12.111, 1.072]}, {"time": 12.3333, "x": 0.928, "y": 1.072, "curve": [12.556, 0.928, 12.778, 1.109, 12.556, 1.072, 12.778, 0.906]}, {"time": 13, "x": 1.109, "y": 0.906}], "shear": [{"time": 2, "curve": [2.444, 0, 2.889, 0, 2.444, 0, 2.889, -2.4]}, {"time": 3.3333, "y": -2.4, "curve": [3.778, 0, 4.222, 0, 3.778, -2.4, 4.222, 0]}, {"time": 4.6667}]}, "RU_R3": {"translate": [{"x": 9.38, "curve": [0.279, -5.42, 0.556, -25.11, 0.279, 0, 0.556, 0]}, {"time": 0.8333, "x": -25.11, "curve": [1.278, -25.11, 1.722, 25.47, 1.278, 0, 1.722, 0]}, {"time": 2.1667, "x": 25.47, "curve": [2.611, 25.47, 3.056, -25.11, 2.611, 0, 3.056, 0]}, {"time": 3.5, "x": -25.11, "curve": [3.944, -25.11, 4.389, 25.47, 3.944, 0, 4.389, 0]}, {"time": 4.8333, "x": 25.47, "curve": [5.278, 25.47, 5.722, -25.11, 5.278, 0, 5.722, 0]}, {"time": 6.1667, "x": -25.11, "curve": [6.5, -25.11, 6.833, 25.47, 6.5, 0, 6.833, 0]}, {"time": 7.1667, "x": 25.47, "curve": [7.611, 25.47, 8.056, -25.11, 7.611, 0, 8.056, 0]}, {"time": 8.5, "x": -25.11, "curve": [8.944, -25.11, 9.389, 25.47, 8.944, 0, 9.389, 0]}, {"time": 9.8333, "x": 25.47, "curve": [10.278, 25.47, 10.722, -25.11, 10.278, 0, 10.722, 0]}, {"time": 11.1667, "x": -25.11, "curve": [11.611, -25.11, 12.056, 25.47, 11.611, 0, 12.056, 0]}, {"time": 12.5, "x": 25.47, "curve": [12.667, 25.47, 12.835, 18.33, 12.667, 0, 12.835, 0]}, {"time": 13, "x": 9.38}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.056, 1.097, 0.111, 1.109, 0.056, 0.917, 0.111, 0.906]}, {"time": 0.1667, "x": 1.109, "y": 0.906, "curve": [0.389, 1.109, 0.611, 0.928, 0.389, 0.906, 0.611, 1.072]}, {"time": 0.8333, "x": 0.928, "y": 1.072, "curve": [1.056, 0.928, 1.278, 1.109, 1.056, 1.072, 1.278, 0.906]}, {"time": 1.5, "x": 1.109, "y": 0.906, "curve": [1.722, 1.109, 1.944, 0.928, 1.722, 0.906, 1.944, 1.072]}, {"time": 2.1667, "x": 0.928, "y": 1.072, "curve": [2.389, 0.928, 2.611, 1.109, 2.389, 1.072, 2.611, 0.906]}, {"time": 2.8333, "x": 1.109, "y": 0.906, "curve": [3.056, 1.109, 3.278, 0.928, 3.056, 0.906, 3.278, 1.072]}, {"time": 3.5, "x": 0.928, "y": 1.072, "curve": [3.722, 0.928, 3.944, 1.109, 3.722, 1.072, 3.944, 0.906]}, {"time": 4.1667, "x": 1.109, "y": 0.906, "curve": [4.389, 1.109, 4.611, 0.928, 4.389, 0.906, 4.611, 1.072]}, {"time": 4.8333, "x": 0.928, "y": 1.072, "curve": [5.056, 0.928, 5.278, 1.109, 5.056, 1.072, 5.278, 0.906]}, {"time": 5.5, "x": 1.109, "y": 0.906, "curve": [5.722, 1.109, 5.944, 0.928, 5.722, 0.906, 5.944, 1.072]}, {"time": 6.1667, "x": 0.928, "y": 1.072, "curve": [6.333, 0.928, 6.5, 1.109, 6.333, 1.072, 6.5, 0.906]}, {"time": 6.6667, "x": 1.109, "y": 0.906, "curve": [6.833, 1.109, 7, 0.928, 6.833, 0.906, 7, 1.072]}, {"time": 7.1667, "x": 0.928, "y": 1.072, "curve": [7.389, 0.928, 7.611, 1.109, 7.389, 1.072, 7.611, 0.906]}, {"time": 7.8333, "x": 1.109, "y": 0.906, "curve": [8.056, 1.109, 8.278, 0.928, 8.056, 0.906, 8.278, 1.072]}, {"time": 8.5, "x": 0.928, "y": 1.072, "curve": [8.722, 0.928, 8.944, 1.109, 8.722, 1.072, 8.944, 0.906]}, {"time": 9.1667, "x": 1.109, "y": 0.906, "curve": [9.389, 1.109, 9.611, 0.928, 9.389, 0.906, 9.611, 1.072]}, {"time": 9.8333, "x": 0.928, "y": 1.072, "curve": [10.056, 0.928, 10.278, 1.109, 10.056, 1.072, 10.278, 0.906]}, {"time": 10.5, "x": 1.109, "y": 0.906, "curve": [10.722, 1.109, 10.944, 0.928, 10.722, 0.906, 10.944, 1.072]}, {"time": 11.1667, "x": 0.928, "y": 1.072, "curve": [11.389, 0.928, 11.611, 1.109, 11.389, 1.072, 11.611, 0.906]}, {"time": 11.8333, "x": 1.109, "y": 0.906, "curve": [12.056, 1.109, 12.278, 0.928, 12.056, 0.906, 12.278, 1.072]}, {"time": 12.5, "x": 0.928, "y": 1.072, "curve": [12.667, 0.928, 12.833, 1.029, 12.667, 1.072, 12.833, 0.979]}, {"time": 13, "x": 1.08, "y": 0.932}], "shear": [{"time": 2.1667, "curve": [2.611, 0, 3.056, 0, 2.611, 0, 3.056, -2.4]}, {"time": 3.5, "y": -2.4, "curve": [3.944, 0, 4.389, 0, 3.944, -2.4, 4.389, 0]}, {"time": 4.8333}]}, "bone34": {"rotate": [{"value": -0.01, "curve": "stepped"}, {"time": 6.3333, "value": -0.01, "curve": [6.778, -0.01, 7.222, 5.56]}, {"time": 7.6667, "value": 5.56, "curve": [8.111, 5.56, 8.556, -0.01]}, {"time": 9, "value": -0.01}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 5.86]}, {"time": 7.6667, "value": 5.86, "curve": [8.111, 5.86, 8.556, 0]}, {"time": 9}]}, "leg_R1": {"translate": [{"y": -37.73, "curve": [0.444, 0, 0.889, 0, 0.444, -37.73, 0.889, 0]}, {"time": 1.3333, "curve": [1.778, 0, 2.222, 0, 1.778, 0, 2.222, -37.73]}, {"time": 2.6667, "y": -37.73, "curve": [3.111, 0, 3.556, 0, 3.111, -37.73, 3.556, 0]}, {"time": 4, "curve": [4.444, 0, 4.889, 0, 4.444, 0, 4.889, -37.73]}, {"time": 5.3333, "y": -37.73, "curve": [5.667, 0, 6, 0, 5.667, -37.73, 6, 0]}, {"time": 6.3333, "curve": [6.778, 0, 7.222, 0, 6.778, 0, 7.222, -18.87]}, {"time": 7.6667, "y": -18.87, "curve": [8.111, 0, 8.556, 0, 8.111, -18.87, 8.556, 0]}, {"time": 9, "curve": [9.444, 0, 9.889, 0, 9.444, 0, 9.889, -37.73]}, {"time": 10.3333, "y": -37.73, "curve": [10.778, 0, 11.222, 0, 10.778, -37.73, 11.222, 0]}, {"time": 11.6667, "curve": [12.111, 0, 12.556, 0, 12.111, 0, 12.556, -37.73]}, {"time": 13, "y": -37.73}]}, "bone36": {"translate": [{"curve": [0.445, 0, 5.955, 0, 0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, 1.31, 6.344, 0, 6.456, 0]}, {"time": 6.5667, "x": 1.31, "curve": "stepped"}, {"time": 9.5, "x": 1.31, "curve": [9.611, 1.31, 9.722, 0, 10.498, 0, 9.722, 0]}, {"time": 9.8333}]}, "bone37": {"rotate": [{"curve": [0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, -5.66]}, {"time": 6.5667, "value": -5.66, "curve": "stepped"}, {"time": 9.5, "value": -5.66, "curve": [9.611, -5.66, 9.722, 0]}, {"time": 9.8333}], "scale": [{"curve": [0.445, 1, 5.955, 1, 0.445, 1, 5.955, 1]}, {"time": 6.2333, "curve": [6.344, 1, 6.456, 0.965, 6.344, 1, 6.456, 1]}, {"time": 6.5667, "x": 0.965, "curve": "stepped"}, {"time": 9.5, "x": 0.965, "curve": [9.611, 0.965, 9.722, 1, 9.611, 1, 9.722, 1]}, {"time": 9.8333}]}, "bone38": {"rotate": [{"curve": [0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, -5.66]}, {"time": 6.5667, "value": -5.66, "curve": "stepped"}, {"time": 9.5, "value": -5.66, "curve": [9.611, -5.66, 9.722, 0]}, {"time": 9.8333}]}, "bone39": {"translate": [{"curve": [0.445, 0, 5.955, 0, 0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, 1.31, 6.344, 0, 6.456, 0]}, {"time": 6.5667, "x": 1.31, "curve": "stepped"}, {"time": 9.5, "x": 1.31, "curve": [9.611, 1.31, 9.722, 0, 10.498, 0, 9.722, 0]}, {"time": 9.8333}]}, "bone40": {"rotate": [{"curve": [0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, 4.4]}, {"time": 6.5667, "value": 4.4, "curve": "stepped"}, {"time": 9.5, "value": 4.4, "curve": [9.611, 4.4, 9.722, 0]}, {"time": 9.8333}], "scale": [{"curve": [0.445, 1, 5.955, 1, 0.445, 1, 5.955, 1]}, {"time": 6.2333, "curve": [6.344, 1, 6.456, 0.965, 6.344, 1, 6.456, 1]}, {"time": 6.5667, "x": 0.965, "curve": "stepped"}, {"time": 9.5, "x": 0.965, "curve": [9.611, 0.965, 9.722, 1, 9.611, 1, 9.722, 1]}, {"time": 9.8333}]}, "bone41": {"rotate": [{"curve": [0.445, 0, 5.955, 0]}, {"time": 6.2333, "curve": [6.344, 0, 6.456, 4.4]}, {"time": 6.5667, "value": 4.4, "curve": "stepped"}, {"time": 9.5, "value": 4.4, "curve": [9.611, 4.4, 9.722, 0]}, {"time": 9.8333}]}, "bone42": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.48, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -1.48, "curve": "stepped"}, {"time": 3.1667, "x": -1.48, "curve": [3.2, -1.48, 3.233, 0.64, 3.2, 0, 3.233, -1.4]}, {"time": 3.2667, "x": 0.64, "y": -1.4, "curve": "stepped"}, {"time": 3.7667, "x": 0.64, "y": -1.4, "curve": [3.878, 0.64, 3.833, 0, 3.878, -1.4, 3.833, 0]}, {"time": 3.8667, "curve": "stepped"}, {"time": 6.4, "curve": [6.433, 0, 6.467, -1.03, 6.433, 0, 6.467, 4.59]}, {"time": 6.5, "x": -1.03, "y": 4.59, "curve": "stepped"}, {"time": 7.1667, "x": -1.03, "y": 4.59, "curve": [7.2, -1.03, 7.233, -2.48, 7.2, 4.59, 7.233, 3.55]}, {"time": 7.2667, "x": -2.48, "y": 3.55, "curve": "stepped"}, {"time": 8.1667, "x": -2.48, "y": 3.55, "curve": [8.2, -2.48, 8.233, -0.6, 8.2, 3.55, 8.233, 5.41]}, {"time": 8.2667, "x": -0.6, "y": 5.41, "curve": "stepped"}, {"time": 8.6667, "x": -0.6, "y": 5.41, "curve": [8.7, -0.6, 8.733, -1.03, 8.7, 5.41, 8.733, 4.59]}, {"time": 8.7667, "x": -1.03, "y": 4.59, "curve": "stepped"}, {"time": 9.6667, "x": -1.03, "y": 4.59, "curve": [9.7, -1.03, 9.733, 0, 9.7, 4.59, 9.733, 0]}, {"time": 9.7667}]}, "bone43": {"translate": [{"time": 2, "curve": [2.033, 0, 2.067, -1.48, 2.033, 0, 2.067, 0]}, {"time": 2.1, "x": -1.48, "curve": "stepped"}, {"time": 3.1667, "x": -1.48, "curve": [3.2, -1.48, 3.233, 0.64, 3.2, 0, 3.233, -1.4]}, {"time": 3.2667, "x": 0.64, "y": -1.4, "curve": "stepped"}, {"time": 3.7667, "x": 0.64, "y": -1.4, "curve": [3.878, 0.64, 3.833, 0, 3.878, -1.4, 3.833, 0]}, {"time": 3.8667, "curve": "stepped"}, {"time": 6.4, "curve": [6.433, 0, 6.467, -1.03, 6.433, 0, 6.467, 4.59]}, {"time": 6.5, "x": -1.03, "y": 4.59, "curve": "stepped"}, {"time": 7.1667, "x": -1.03, "y": 4.59, "curve": [7.2, -1.03, 7.233, -2.48, 7.2, 4.59, 7.233, 3.55]}, {"time": 7.2667, "x": -2.48, "y": 3.55, "curve": "stepped"}, {"time": 8.1667, "x": -2.48, "y": 3.55, "curve": [8.2, -2.48, 8.233, -0.6, 8.2, 3.55, 8.233, 5.41]}, {"time": 8.2667, "x": -0.6, "y": 5.41, "curve": "stepped"}, {"time": 8.6667, "x": -0.6, "y": 5.41, "curve": [8.7, -0.6, 8.733, -1.03, 8.7, 5.41, 8.733, 4.59]}, {"time": 8.7667, "x": -1.03, "y": 4.59, "curve": "stepped"}, {"time": 9.6667, "x": -1.03, "y": 4.59, "curve": [9.7, -1.03, 9.733, 0, 9.7, 4.59, 9.733, 0]}, {"time": 9.7667}]}, "hair_L": {"rotate": [{"value": -1.69, "curve": [0.279, -0.04, 0.556, 2.14]}, {"time": 0.8333, "value": 2.14, "curve": [1.278, 2.14, 1.722, -3.47]}, {"time": 2.1667, "value": -3.47, "curve": [2.611, -3.47, 3.056, 2.14]}, {"time": 3.5, "value": 2.14, "curve": [3.944, 2.14, 4.389, -3.47]}, {"time": 4.8333, "value": -3.47, "curve": [5.278, -3.47, 5.722, 2.14]}, {"time": 6.1667, "value": 2.14, "curve": [6.5, 2.14, 6.833, -3.47]}, {"time": 7.1667, "value": -3.47, "curve": [7.611, -3.47, 8.056, 2.14]}, {"time": 8.5, "value": 2.14, "curve": [8.944, 2.14, 9.389, -3.47]}, {"time": 9.8333, "value": -3.47, "curve": [10.278, -3.47, 10.722, 2.14]}, {"time": 11.1667, "value": 2.14, "curve": [11.611, 2.14, 12.056, -3.47]}, {"time": 12.5, "value": -3.47, "curve": [12.667, -3.47, 12.835, -2.68]}, {"time": 13, "value": -1.69}]}, "hair_R": {"rotate": [{"value": -1.69, "curve": [0.279, -0.04, 0.556, 2.14]}, {"time": 0.8333, "value": 2.14, "curve": [1.278, 2.14, 1.722, -3.47]}, {"time": 2.1667, "value": -3.47, "curve": [2.611, -3.47, 3.056, 2.14]}, {"time": 3.5, "value": 2.14, "curve": [3.944, 2.14, 4.389, -3.47]}, {"time": 4.8333, "value": -3.47, "curve": [5.278, -3.47, 5.722, 2.14]}, {"time": 6.1667, "value": 2.14, "curve": [6.5, 2.14, 6.833, -3.47]}, {"time": 7.1667, "value": -3.47, "curve": [7.611, -3.47, 8.056, 2.14]}, {"time": 8.5, "value": 2.14, "curve": [8.944, 2.14, 9.389, -3.47]}, {"time": 9.8333, "value": -3.47, "curve": [10.278, -3.47, 10.722, 2.14]}, {"time": 11.1667, "value": 2.14, "curve": [11.611, 2.14, 12.056, -3.47]}, {"time": 12.5, "value": -3.47, "curve": [12.667, -3.47, 12.835, -2.68]}, {"time": 13, "value": -1.69}]}, "hair_RR": {"rotate": [{"value": -2.57, "curve": [0.336, -0.99, 0.668, 2.14]}, {"time": 1, "value": 2.14, "curve": [1.444, 2.14, 1.889, -3.47]}, {"time": 2.3333, "value": -3.47, "curve": [2.778, -3.47, 3.222, 2.14]}, {"time": 3.6667, "value": 2.14, "curve": [4.111, 2.14, 4.556, -3.47]}, {"time": 5, "value": -3.47, "curve": [5.444, -3.47, 5.889, 2.14]}, {"time": 6.3333, "value": 2.14, "curve": [6.667, 2.14, 7, -3.47]}, {"time": 7.3333, "value": -3.47, "curve": [7.778, -3.47, 8.222, 2.14]}, {"time": 8.6667, "value": 2.14, "curve": [9.111, 2.14, 9.556, -3.47]}, {"time": 10, "value": -3.47, "curve": [10.444, -3.47, 10.889, 2.14]}, {"time": 11.3333, "value": 2.14, "curve": [11.778, 2.14, 12.222, -3.47]}, {"time": 12.6667, "value": -3.47, "curve": [12.779, -3.47, 12.892, -3.11]}, {"time": 13, "value": -2.57}]}, "hair_LL": {"rotate": [{"value": -2.57, "curve": [0.336, -0.99, 0.668, 2.14]}, {"time": 1, "value": 2.14, "curve": [1.444, 2.14, 1.889, -3.47]}, {"time": 2.3333, "value": -3.47, "curve": [2.778, -3.47, 3.222, 2.14]}, {"time": 3.6667, "value": 2.14, "curve": [4.111, 2.14, 4.556, -3.47]}, {"time": 5, "value": -3.47, "curve": [5.444, -3.47, 5.889, 2.14]}, {"time": 6.3333, "value": 2.14, "curve": [6.667, 2.14, 7, -3.47]}, {"time": 7.3333, "value": -3.47, "curve": [7.778, -3.47, 8.222, 2.14]}, {"time": 8.6667, "value": 2.14, "curve": [9.111, 2.14, 9.556, -3.47]}, {"time": 10, "value": -3.47, "curve": [10.444, -3.47, 10.889, 2.14]}, {"time": 11.3333, "value": 2.14, "curve": [11.778, 2.14, 12.222, -3.47]}, {"time": 12.6667, "value": -3.47, "curve": [12.779, -3.47, 12.892, -3.11]}, {"time": 13, "value": -2.57}]}, "headround3": {"translate": [{"x": -49.3, "curve": [0.225, -111.41, 0.446, -174.36, 0.225, 0, 0.446, 0]}, {"time": 0.6667, "x": -174.36, "curve": [1.111, -174.36, 1.556, 75.76, 1.111, 0, 1.556, 0]}, {"time": 2, "x": 75.76, "curve": [2.444, 75.76, 2.889, -174.36, 2.444, 0, 2.889, 0]}, {"time": 3.3333, "x": -174.36, "curve": [3.778, -174.36, 4.222, 75.76, 3.778, 0, 4.222, 0]}, {"time": 4.6667, "x": 75.76, "curve": [5.111, 75.76, 5.556, -174.36, 5.111, 0, 5.556, 0]}, {"time": 6, "x": -174.36, "curve": [6.333, -174.36, 6.667, 75.76, 6.333, 0, 6.667, 0]}, {"time": 7, "x": 75.76, "curve": [7.444, 75.76, 7.889, 24.15, 7.444, 0, 7.889, 0]}, {"time": 8.3333, "x": 24.15, "curve": [8.778, 24.15, 9.222, 75.76, 8.778, 0, 9.222, 0]}, {"time": 9.6667, "x": 75.76, "curve": [10.111, 75.76, 10.556, -174.36, 10.111, 0, 10.556, 0]}, {"time": 11, "x": -174.36, "curve": [11.444, -174.36, 11.889, 75.76, 11.444, 0, 11.889, 0]}, {"time": 12.3333, "x": 75.76, "curve": [12.557, 75.76, 12.781, 13.64, 12.557, 0, 12.781, 0]}, {"time": 13, "x": -49.3}]}, "headround": {"translate": [{"y": 27.3, "curve": [0.279, 0, 0.556, 0, 0.279, -28.77, 0.556, -103.34]}, {"time": 0.8333, "y": -103.34, "curve": [1.278, 0, 1.722, 0, 1.278, -103.34, 1.722, 88.22]}, {"time": 2.1667, "y": 88.22, "curve": [2.611, 0, 3.056, 0, 2.611, 88.22, 3.056, -103.34]}, {"time": 3.5, "y": -103.34, "curve": [3.944, 0, 4.389, 0, 3.944, -103.34, 4.389, 88.22]}, {"time": 4.8333, "y": 88.22, "curve": [5.278, 0, 5.722, 0, 5.278, 88.22, 5.722, -103.16]}, {"time": 6.1667, "y": -103.34, "curve": [6.5, 0, 6.833, 0, 6.5, -103.48, 6.833, 430.14]}, {"time": 7.1667, "y": 430.16, "curve": [7.611, 0, 8.056, 0, 7.611, 430.18, 8.056, 327.62]}, {"time": 8.5, "y": 327.6, "curve": [8.944, 0, 9.389, 0, 8.944, 327.57, 9.389, 430.16]}, {"time": 9.8333, "y": 430.16, "curve": [10.278, 0, 10.722, 0, 10.278, 430.16, 10.722, -103.34]}, {"time": 11.1667, "y": -103.34, "curve": [11.611, 0, 12.056, 0, 11.611, -103.34, 12.056, 88.22]}, {"time": 12.5, "y": 88.22, "curve": [12.667, 0, 12.835, 0, 12.667, 88.22, 12.835, 61.19]}, {"time": 13, "y": 27.3}]}, "bodyround": {"translate": [{"y": 77.57, "curve": [0.168, 0, 0.334, 0, 0.168, 155.72, 0.334, 219.83]}, {"time": 0.5, "y": 219.83, "curve": [0.944, 0, 1.389, 0, 0.944, 219.83, 1.389, -227.55]}, {"time": 1.8333, "y": -227.55, "curve": [2.278, 0, 2.722, 0, 2.278, -227.55, 2.722, 219.83]}, {"time": 3.1667, "y": 219.83, "curve": [3.611, 0, 4.056, 0, 3.611, 219.83, 4.056, -227.55]}, {"time": 4.5, "y": -227.55, "curve": [4.944, 0, 5.389, 0, 4.944, -227.55, 5.389, 219.83]}, {"time": 5.8333, "y": 219.83, "curve": [6.167, 0, 6.5, 0, 6.167, 219.83, 6.5, -227.55]}, {"time": 6.8333, "y": -227.55, "curve": [7.278, 0, 7.722, 0, 7.278, -227.55, 7.722, -3.86]}, {"time": 8.1667, "y": -3.86, "curve": [8.611, 0, 9.056, 0, 8.611, -3.86, 9.056, -227.55]}, {"time": 9.5, "y": -227.55, "curve": [9.944, 0, 10.389, 0, 9.944, -227.55, 10.389, 219.83]}, {"time": 10.8333, "y": 219.83, "curve": [11.278, 0, 11.722, 0, 11.278, 219.83, 11.722, -227.55]}, {"time": 12.1667, "y": -227.55, "curve": [12.445, 0, 12.724, 0, 12.445, -227.55, 12.724, -53.62]}, {"time": 13, "y": 77.57}]}, "tunround": {"translate": [{"x": -215.24, "curve": [0.057, -228.28, 0.112, -238.21, 0.057, 0, 0.112, 0]}, {"time": 0.1667, "x": -238.21, "curve": [0.611, -238.21, 1.056, 250.59, 0.611, 0, 1.056, 0]}, {"time": 1.5, "x": 250.59, "curve": [1.944, 250.59, 2.389, -238.21, 1.944, 0, 2.389, 0]}, {"time": 2.8333, "x": -238.21, "curve": [3.278, -238.21, 3.722, 250.59, 3.278, 0, 3.722, 0]}, {"time": 4.1667, "x": 250.59, "curve": [4.611, 250.59, 5.056, -238.21, 4.611, 0, 5.056, 0]}, {"time": 5.5, "x": -238.21, "curve": [5.833, -238.21, 6.167, 250.59, 5.833, 0, 6.167, 0]}, {"time": 6.5, "x": 250.59, "curve": [6.944, 250.59, 7.389, 6.19, 6.944, 0, 7.389, 0]}, {"time": 7.8333, "x": 6.19, "curve": [8.278, 6.19, 8.722, 250.59, 8.278, 0, 8.722, 0]}, {"time": 9.1667, "x": 250.59, "curve": [9.611, 250.59, 10.056, -238.21, 9.611, 0, 10.056, 0]}, {"time": 10.5, "x": -238.21, "curve": [10.944, -238.21, 11.389, 250.59, 10.944, 0, 11.389, 0]}, {"time": 11.8333, "x": 250.59, "curve": [12.223, 250.59, 12.613, -122.58, 12.223, 0, 12.613, 0]}, {"time": 13, "x": -215.24}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"time": 1, "curve": [1.056, 0, 1.111, 1]}, {"time": 1.1667, "vertices": [-3.01477, -0.14106, -3.01404, -0.14103, -1.3418, -0.19048, -1.3418, -0.19047, -1.3418, -0.19048, -1.3418, -0.19047, -0.95093, -0.38319, -0.95093, -0.38313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.03625, 0.12373, -2.03625, 0.1238, -6.31165, -0.16975, -6.31152, -0.16945, -8.65283, -0.5407, -8.65247, -0.54041, -8.72363, -0.62045, -8.72119, -0.62033, -4.6438, 0.44241, -4.64307, 0.44257, 0, 0, 0, 0, -3.77283, -0.07677, -3.77075, -0.07658, -7.23694, -0.4085, -7.23572, -0.40828, -9.61597, -0.58868, -9.61426, -0.58836, -9.57214, -0.20677, -9.57214, -0.2066, -7.12451, -0.38194, -7.12415, -0.38165, -1.54443, -0.41953, -1.54443, -0.41946], "curve": [1.222, 0, 1.278, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 6.2333, "curve": [6.289, 0, 6.344, 1]}, {"time": 6.4, "vertices": [-3.01477, -0.14106, -3.01404, -0.14103, -1.3418, -0.19048, -1.3418, -0.19047, -1.3418, -0.19048, -1.3418, -0.19047, -0.95093, -0.38319, -0.95093, -0.38313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.03625, 0.12373, -2.03625, 0.1238, -6.31165, -0.16975, -6.31152, -0.16945, -8.65283, -0.5407, -8.65247, -0.54041, -8.72363, -0.62045, -8.72119, -0.62033, -4.6438, 0.44241, -4.64307, 0.44257, 0, 0, 0, 0, -3.77283, -0.07677, -3.77075, -0.07658, -7.23694, -0.4085, -7.23572, -0.40828, -9.61597, -0.58868, -9.61426, -0.58836, -9.57214, -0.20677, -9.57214, -0.2066, -7.12451, -0.38194, -7.12415, -0.38165, -1.54443, -0.41953, -1.54443, -0.41946], "curve": [6.456, 0, 6.511, 1]}, {"time": 6.5667, "curve": "stepped"}, {"time": 9.5, "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667, "vertices": [-3.01477, -0.14106, -3.01404, -0.14103, -1.3418, -0.19048, -1.3418, -0.19047, -1.3418, -0.19048, -1.3418, -0.19047, -0.95093, -0.38319, -0.95093, -0.38313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.03625, 0.12373, -2.03625, 0.1238, -6.31165, -0.16975, -6.31152, -0.16945, -8.65283, -0.5407, -8.65247, -0.54041, -8.72363, -0.62045, -8.72119, -0.62033, -4.6438, 0.44241, -4.64307, 0.44257, 0, 0, 0, 0, -3.77283, -0.07677, -3.77075, -0.07658, -7.23694, -0.4085, -7.23572, -0.40828, -9.61597, -0.58868, -9.61426, -0.58836, -9.57214, -0.20677, -9.57214, -0.2066, -7.12451, -0.38194, -7.12415, -0.38165, -1.54443, -0.41953, -1.54443, -0.41946], "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333}]}}, "eye_RR": {"eye_RR": {"deform": [{"time": 1, "curve": [1.056, 0, 1.111, 1]}, {"time": 1.1667, "vertices": [-4.70203, 0.29883, -4.70203, 0.29902, -9.11731, 0.70561, -9.11743, 0.70593, -9.2467, 0.60732, -9.24683, 0.60765, -7.64758, 0.3532, -7.64758, 0.35343, -3.46606, 0.08114, -3.46606, 0.08128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.54089, 0.25773, -1.54089, 0.25776, -1.88452, 0.1193, -1.88452, 0.11937, -1.88452, 0.1193, -1.88452, 0.11937, -3.14392, 0.3536, -3.14404, 0.35374, -1.59583, 0.19566, -1.59583, 0.19572, -7.02283, 0.68494, -7.02307, 0.6851, -9.89417, 0.62651, -9.89355, 0.62672, -9.71802, 0.63716, -9.71802, 0.63749, -7.89185, 0.51432, -7.89233, 0.51455, -4.33936, -0.03094, -4.33923, -0.03083], "curve": [1.222, 0, 1.278, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 6.2333, "curve": [6.289, 0, 6.344, 1]}, {"time": 6.4, "vertices": [-4.70203, 0.29883, -4.70203, 0.29902, -9.11731, 0.70561, -9.11743, 0.70593, -9.2467, 0.60732, -9.24683, 0.60765, -7.64758, 0.3532, -7.64758, 0.35343, -3.46606, 0.08114, -3.46606, 0.08128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.54089, 0.25773, -1.54089, 0.25776, -1.88452, 0.1193, -1.88452, 0.11937, -1.88452, 0.1193, -1.88452, 0.11937, -3.14392, 0.3536, -3.14404, 0.35374, -1.59583, 0.19566, -1.59583, 0.19572, -7.02283, 0.68494, -7.02307, 0.6851, -9.89417, 0.62651, -9.89355, 0.62672, -9.71802, 0.63716, -9.71802, 0.63749, -7.89185, 0.51432, -7.89233, 0.51455, -4.33936, -0.03094, -4.33923, -0.03083], "curve": [6.456, 0, 6.511, 1]}, {"time": 6.5667, "curve": "stepped"}, {"time": 9.5, "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667, "vertices": [-4.70203, 0.29883, -4.70203, 0.29902, -9.11731, 0.70561, -9.11743, 0.70593, -9.2467, 0.60732, -9.24683, 0.60765, -7.64758, 0.3532, -7.64758, 0.35343, -3.46606, 0.08114, -3.46606, 0.08128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.54089, 0.25773, -1.54089, 0.25776, -1.88452, 0.1193, -1.88452, 0.11937, -1.88452, 0.1193, -1.88452, 0.11937, -3.14392, 0.3536, -3.14404, 0.35374, -1.59583, 0.19566, -1.59583, 0.19572, -7.02283, 0.68494, -7.02307, 0.6851, -9.89417, 0.62651, -9.89355, 0.62672, -9.71802, 0.63716, -9.71802, 0.63749, -7.89185, 0.51432, -7.89233, 0.51455, -4.33936, -0.03094, -4.33923, -0.03083], "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333}]}}, "head": {"head": {"deform": [{"time": 1, "curve": [1.056, 0, 1.111, 1]}, {"time": 1.1667, "offset": 196, "vertices": [-5.39697, 0.22851, -5.39575, 0.22873, -9.68896, -0.02699, -9.68787, -0.02671, -11.86072, -0.22212, -11.85974, -0.22187, -12.36719, -0.49781, -12.36414, -0.49748, -10.07861, -0.53296, -10.07703, -0.53272, -5.8512, -0.6797, -5.85059, -0.67951, -0.67834, -0.19502, -0.67822, -0.19497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.29907, 0.04376, -4.29687, 0.0439, -7.91528, 0.20609, -7.91174, 0.20632, -11.0094, 0.63048, -11.0072, 0.631, -12.85229, 0.17941, -12.84937, 0.17968, -12.09351, 0.79438, -12.09106, 0.79482, -9.10339, 0.19611, -9.10461, 0.19625, -3.12695, 0.13138, -3.12744, 0.13145, -0.53247, -0.08641, -0.53247, -0.0864], "curve": [1.222, 0, 1.278, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 6.2333, "curve": [6.289, 0, 6.344, 1]}, {"time": 6.4, "offset": 196, "vertices": [-5.39697, 0.22851, -5.39575, 0.22873, -9.68896, -0.02699, -9.68787, -0.02671, -11.86072, -0.22212, -11.85974, -0.22187, -12.36719, -0.49781, -12.36414, -0.49748, -10.07861, -0.53296, -10.07703, -0.53272, -5.8512, -0.6797, -5.85059, -0.67951, -0.67834, -0.19502, -0.67822, -0.19497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.29907, 0.04376, -4.29687, 0.0439, -7.91528, 0.20609, -7.91174, 0.20632, -11.0094, 0.63048, -11.0072, 0.631, -12.85229, 0.17941, -12.84937, 0.17968, -12.09351, 0.79438, -12.09106, 0.79482, -9.10339, 0.19611, -9.10461, 0.19625, -3.12695, 0.13138, -3.12744, 0.13145, -0.53247, -0.08641, -0.53247, -0.0864], "curve": [6.456, 0, 6.511, 1]}, {"time": 6.5667, "curve": "stepped"}, {"time": 9.5, "curve": [9.556, 0, 9.611, 1]}, {"time": 9.6667, "offset": 196, "vertices": [-5.39697, 0.22851, -5.39575, 0.22873, -9.68896, -0.02699, -9.68787, -0.02671, -11.86072, -0.22212, -11.85974, -0.22187, -12.36719, -0.49781, -12.36414, -0.49748, -10.07861, -0.53296, -10.07703, -0.53272, -5.8512, -0.6797, -5.85059, -0.67951, -0.67834, -0.19502, -0.67822, -0.19497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.29907, 0.04376, -4.29687, 0.0439, -7.91528, 0.20609, -7.91174, 0.20632, -11.0094, 0.63048, -11.0072, 0.631, -12.85229, 0.17941, -12.84937, 0.17968, -12.09351, 0.79438, -12.09106, 0.79482, -9.10339, 0.19611, -9.10461, 0.19625, -3.12695, 0.13138, -3.12744, 0.13145, -0.53247, -0.08641, -0.53247, -0.0864], "curve": [9.722, 0, 9.778, 1]}, {"time": 9.8333}]}}}}}, "touch": {"bones": {"ALL": {"translate": [{"x": 7.26, "curve": [0.026, 7.18, 0.063, -24.11, 0.078, 0, 0.156, 0]}, {"time": 0.2333, "x": -24.11, "curve": [0.544, -24.11, 0.856, 7.26, 0.544, 0, 0.856, 0]}, {"time": 1.1667, "x": 7.26}]}, "ALL2": {"translate": [{"y": -13.4, "curve": [0.078, 0, 0.156, 0, 0.026, -13.32, 0.063, 19.92]}, {"time": 0.2333, "y": 19.92, "curve": [0.544, 0, 0.856, 0, 0.544, 19.92, 0.856, -13.4]}, {"time": 1.1667, "y": -13.4}]}, "body": {"rotate": [{"value": 2.44, "curve": [0.026, 2.41, 0.063, -8.98]}, {"time": 0.2333, "value": -8.98, "curve": [0.544, -8.98, 0.856, 2.44]}, {"time": 1.1667, "value": 2.44}], "translate": [{"y": -9.82, "curve": [0.078, 0, 0.156, 0, 0.026, -9.77, 0.063, 11.74]}, {"time": 0.2333, "y": 11.74, "curve": [0.544, 0, 0.856, 0, 0.544, 11.74, 0.856, -9.82]}, {"time": 1.1667, "y": -9.82}], "scale": [{"y": 1.048, "curve": [0.078, 1, 0.156, 1, 0.026, 1.048, 0.063, 0.95]}, {"time": 0.2333, "y": 0.95, "curve": [0.544, 1, 0.856, 1, 0.544, 0.95, 0.856, 1.048]}, {"time": 1.1667, "y": 1.048}]}, "body2": {"rotate": [{"value": -1.48, "curve": [0.026, -1.47, 0.063, 0.44]}, {"time": 0.2333, "value": 0.44, "curve": [0.544, 0.44, 0.856, -1.48]}, {"time": 1.1667, "value": -1.48}], "translate": [{"x": -6.95, "curve": [0.03, -6.92, 0.071, 10.31, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 10.31, "curve": [0.567, 10.31, 0.867, -6.95, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -6.95}], "scale": [{"x": 1.004, "y": 1.004, "curve": [0.03, 1.004, 0.071, 1.027, 0.03, 1.004, 0.071, 1.027]}, {"time": 0.2667, "x": 1.027, "y": 1.027, "curve": [0.567, 1.027, 0.867, 1.004, 0.567, 1.027, 0.867, 1.004]}, {"time": 1.1667, "x": 1.004, "y": 1.004}]}, "neck": {"rotate": [{"value": -1.23, "curve": [0.033, -1.22, 0.08, 0.21]}, {"time": 0.3, "value": 0.21, "curve": [0.589, 0.21, 0.878, -1.27]}, {"time": 1.1667, "value": -1.23}]}, "head": {"rotate": [{"value": -0.54, "curve": [0.037, -0.56, 0.089, -6.31]}, {"time": 0.3333, "value": -6.31, "curve": [0.611, -6.31, 0.889, -0.39]}, {"time": 1.1667, "value": -0.54}]}, "tun": {"rotate": [{"value": 2.44, "curve": [0.026, 2.4, 0.063, -11.07]}, {"time": 0.2333, "value": -11.07, "curve": [0.544, -11.07, 0.856, 2.44]}, {"time": 1.1667, "value": 2.44}]}, "leg_L3": {"rotate": [{}]}, "leg_L4": {"rotate": [{"value": -0.01}]}, "foot_R2": {"rotate": [{"value": 1.86, "curve": [0.026, 1.85, 0.063, -4.47]}, {"time": 0.2333, "value": -4.47, "curve": [0.544, -4.47, 0.856, 1.86]}, {"time": 1.1667, "value": 1.86}]}, "leg_R3": {"rotate": [{}]}, "leg_R4": {"rotate": [{}]}, "sh_L": {"translate": [{"x": -3.23, "curve": [0.03, -3.19, 0.071, 16.6, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 16.6, "curve": [0.567, 16.6, 0.867, -3.23, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.23}]}, "sh_R": {"translate": [{"x": -3.23, "curve": [0.03, -3.18, 0.071, 18.17, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 18.17, "curve": [0.567, 18.17, 0.867, -3.23, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -3.23}]}, "arm_R2": {"rotate": [{}]}, "arm_R3": {"rotate": [{}]}, "arm_L2": {"rotate": [{}]}, "arm_L3": {"rotate": [{"value": -0.01}]}, "hand_L": {"rotate": [{"value": -0.89, "curve": [0.037, -0.87, 0.089, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.611, 2.76, 0.889, -0.89]}, {"time": 1.1667, "value": -0.89}]}, "bone24": {"rotate": [{"value": -0.02}]}, "bone25": {"rotate": [{"value": 0.09}]}, "arm_L": {"translate": [{"x": -9.07, "curve": [0.037, -8.95, 0.089, 23.26, 0.037, -0.13, 0.089, -37.23]}, {"time": 0.3333, "x": 23.26, "y": -37.23, "curve": [0.611, 23.26, 0.889, -9.07, 0.611, -37.23, 0.889, 0]}, {"time": 1.1667, "x": -9.07}]}, "bone26": {"rotate": [{"value": 0.12}]}, "bone27": {"rotate": [{"value": 0.15}]}, "RU_L": {"translate": [{"x": -14.71, "curve": [0.073, -26.98, 0.161, -37.05, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -37.05, "curve": [0.427, -37.05, 0.607, 33.19, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 33.19, "curve": [0.921, 33.19, 1.046, 5.88, 0.921, 0, 1.046, 0]}, {"time": 1.1667, "x": -14.71}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.073, 1.029, 0.161, 0.928, 0.073, 0.979, 0.161, 1.072]}, {"time": 0.2333, "x": 0.928, "y": 1.072, "curve": [0.33, 0.928, 0.403, 1.109, 0.33, 1.072, 0.403, 0.906]}, {"time": 0.5, "x": 1.109, "y": 0.906, "curve": [0.597, 1.109, 0.703, 0.928, 0.597, 0.906, 0.703, 1.072]}, {"time": 0.8, "x": 0.928, "y": 1.072, "curve": [0.897, 0.928, 1.003, 1.109, 0.897, 1.072, 1.003, 0.906]}, {"time": 1.1, "x": 1.109, "y": 0.906, "curve": [1.125, 1.109, 1.143, 1.098, 1.125, 0.906, 1.143, 0.916]}, {"time": 1.1667, "x": 1.08, "y": 0.932}], "shear": [{"time": 0.8, "curve": [0.921, 0, 1.046, 0, 0.921, 0, 1.046, -0.93]}, {"time": 1.1667, "y": -1.64}]}, "RU_L2": {"translate": [{"x": 3.92, "curve": [0.098, -11.99, 0.204, -28.11, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -28.11, "curve": [0.493, -28.11, 0.673, 35.95, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 35.95, "curve": [0.964, 35.95, 1.071, 20.04, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 3.92}], "scale": [{"x": 1.109, "y": 0.906, "curve": [0.097, 1.109, 0.203, 0.928, 0.097, 0.906, 0.203, 1.072]}, {"time": 0.3, "x": 0.928, "y": 1.072, "curve": [0.397, 0.928, 0.47, 1.109, 0.397, 1.072, 0.47, 0.906]}, {"time": 0.5667, "x": 1.109, "y": 0.906, "curve": [0.663, 1.109, 0.77, 0.928, 0.663, 0.906, 0.77, 1.072]}, {"time": 0.8667, "x": 0.928, "y": 1.072, "curve": [0.963, 0.928, 1.07, 1.109, 0.963, 1.072, 1.07, 0.906]}, {"time": 1.1667, "x": 1.109, "y": 0.906}], "shear": [{"time": 0.8667, "curve": [0.964, 0, 1.071, 0, 0.964, 0, 1.071, -0.6]}, {"time": 1.1667, "y": -1.2}]}, "RU_L3": {"translate": [{"x": 9.38, "curve": [0.122, -5.42, 0.246, -25.11, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -25.11, "curve": [0.56, -25.11, 0.74, 25.47, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 25.47, "curve": [1.006, 25.47, 1.095, 18.33, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 9.38}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.024, 1.097, 0.042, 1.109, 0.024, 0.917, 0.042, 0.906]}, {"time": 0.0667, "x": 1.109, "y": 0.906, "curve": [0.163, 1.109, 0.27, 0.928, 0.163, 0.906, 0.27, 1.072]}, {"time": 0.3667, "x": 0.928, "y": 1.072, "curve": [0.463, 0.928, 0.57, 1.109, 0.463, 1.072, 0.57, 0.906]}, {"time": 0.6667, "x": 1.109, "y": 0.906, "curve": [0.763, 1.109, 0.837, 0.928, 0.763, 0.906, 0.837, 1.072]}, {"time": 0.9333, "x": 0.928, "y": 1.072, "curve": [1.006, 0.928, 1.095, 1.03, 1.006, 1.072, 1.095, 0.979]}, {"time": 1.1667, "x": 1.08, "y": 0.932}], "shear": [{"time": 0.9333, "curve": [1.006, 0, 1.095, 0, 1.006, 0, 1.095, -0.34]}, {"time": 1.1667, "y": -0.76}]}, "RU_R": {"translate": [{"x": -14.71, "curve": [0.073, -26.98, 0.161, -37.05, 0.073, 0, 0.161, 0]}, {"time": 0.2333, "x": -37.05, "curve": [0.427, -37.05, 0.607, 33.19, 0.427, 0, 0.607, 0]}, {"time": 0.8, "x": 33.19, "curve": [0.921, 33.19, 1.046, 5.88, 0.921, 0, 1.046, 0]}, {"time": 1.1667, "x": -14.71}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.073, 1.029, 0.161, 0.928, 0.073, 0.979, 0.161, 1.072]}, {"time": 0.2333, "x": 0.928, "y": 1.072, "curve": [0.33, 0.928, 0.403, 1.109, 0.33, 1.072, 0.403, 0.906]}, {"time": 0.5, "x": 1.109, "y": 0.906, "curve": [0.597, 1.109, 0.703, 0.928, 0.597, 0.906, 0.703, 1.072]}, {"time": 0.8, "x": 0.928, "y": 1.072, "curve": [0.897, 0.928, 1.003, 1.109, 0.897, 1.072, 1.003, 0.906]}, {"time": 1.1, "x": 1.109, "y": 0.906, "curve": [1.125, 1.109, 1.143, 1.098, 1.125, 0.906, 1.143, 0.916]}, {"time": 1.1667, "x": 1.08, "y": 0.932}], "shear": [{"time": 0.8, "curve": [0.921, 0, 1.046, 0, 0.921, 0, 1.046, -0.93]}, {"time": 1.1667, "y": -1.64}]}, "RU_R2": {"translate": [{"x": 3.92, "curve": [0.098, -11.99, 0.204, -28.11, 0.098, 0, 0.204, 0]}, {"time": 0.3, "x": -28.11, "curve": [0.493, -28.11, 0.673, 35.95, 0.493, 0, 0.673, 0]}, {"time": 0.8667, "x": 35.95, "curve": [0.964, 35.95, 1.071, 20.04, 0.964, 0, 1.071, 0]}, {"time": 1.1667, "x": 3.92}], "scale": [{"x": 1.109, "y": 0.906, "curve": [0.097, 1.109, 0.203, 0.928, 0.097, 0.906, 0.203, 1.072]}, {"time": 0.3, "x": 0.928, "y": 1.072, "curve": [0.397, 0.928, 0.47, 1.109, 0.397, 1.072, 0.47, 0.906]}, {"time": 0.5667, "x": 1.109, "y": 0.906, "curve": [0.663, 1.109, 0.77, 0.928, 0.663, 0.906, 0.77, 1.072]}, {"time": 0.8667, "x": 0.928, "y": 1.072, "curve": [0.963, 0.928, 1.07, 1.109, 0.963, 1.072, 1.07, 0.906]}, {"time": 1.1667, "x": 1.109, "y": 0.906}], "shear": [{"time": 0.8667, "curve": [0.964, 0, 1.071, 0, 0.964, 0, 1.071, -0.6]}, {"time": 1.1667, "y": -1.2}]}, "RU_R3": {"translate": [{"x": 9.38, "curve": [0.122, -5.42, 0.246, -25.11, 0.122, 0, 0.246, 0]}, {"time": 0.3667, "x": -25.11, "curve": [0.56, -25.11, 0.74, 25.47, 0.56, 0, 0.74, 0]}, {"time": 0.9333, "x": 25.47, "curve": [1.006, 25.47, 1.095, 18.33, 1.006, 0, 1.095, 0]}, {"time": 1.1667, "x": 9.38}], "scale": [{"x": 1.08, "y": 0.932, "curve": [0.024, 1.097, 0.042, 1.109, 0.024, 0.917, 0.042, 0.906]}, {"time": 0.0667, "x": 1.109, "y": 0.906, "curve": [0.163, 1.109, 0.27, 0.928, 0.163, 0.906, 0.27, 1.072]}, {"time": 0.3667, "x": 0.928, "y": 1.072, "curve": [0.463, 0.928, 0.57, 1.109, 0.463, 1.072, 0.57, 0.906]}, {"time": 0.6667, "x": 1.109, "y": 0.906, "curve": [0.763, 1.109, 0.837, 0.928, 0.763, 0.906, 0.837, 1.072]}, {"time": 0.9333, "x": 0.928, "y": 1.072, "curve": [1.006, 0.928, 1.095, 1.03, 1.006, 1.072, 1.095, 0.979]}, {"time": 1.1667, "x": 1.08, "y": 0.932}], "shear": [{"time": 0.9333, "curve": [1.006, 0, 1.095, 0, 1.006, 0, 1.095, -0.34]}, {"time": 1.1667, "y": -0.76}]}, "bone34": {"rotate": [{"value": -0.01}]}, "bone35": {"rotate": [{}]}, "leg_R1": {"translate": [{"y": -37.73, "curve": [0.026, 0.03, 0.063, 10.42, 0.026, -37.64, 0.063, 0]}, {"time": 0.2333, "x": 10.42, "curve": [0.544, 10.42, 0.856, 0, 0.544, 0, 0.856, -37.73]}, {"time": 1.1667, "y": -37.73}]}, "bone36": {"translate": [{"curve": [0.037, 0, 0.089, 1.41, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 1.41, "curve": [0.611, 1.41, 0.889, -0.03, 0.611, 0, 0.889, 0]}, {"time": 1.1667}]}, "bone37": {"rotate": [{"curve": [0.037, -0.03, 0.089, -10.38]}, {"time": 0.3333, "value": -10.38, "curve": [0.611, -10.38, 0.889, 0.25]}, {"time": 1.1667}], "scale": [{"curve": [0.037, 1, 0.089, 0.965, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.965, "curve": [0.611, 0.965, 0.889, 1.001, 0.611, 1, 0.889, 1]}, {"time": 1.1667}]}, "bone38": {"rotate": [{"curve": [0.037, -0.03, 0.089, -10.38]}, {"time": 0.3333, "value": -10.38, "curve": [0.611, -10.38, 0.889, 0.25]}, {"time": 1.1667}]}, "bone39": {"translate": [{"curve": [0.037, 0, 0.089, 1.41, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 1.41, "curve": [0.611, 1.41, 0.889, -0.03, 0.611, 0, 0.889, 0]}, {"time": 1.1667}]}, "bone40": {"rotate": [{"curve": [0.037, 0.02, 0.089, 6.21]}, {"time": 0.3333, "value": 6.21, "curve": [0.611, 6.21, 0.889, -0.14]}, {"time": 1.1667}], "scale": [{"curve": [0.037, 1, 0.089, 0.965, 0.111, 1, 0.222, 1]}, {"time": 0.3333, "x": 0.965, "curve": [0.611, 0.965, 0.889, 1.001, 0.611, 1, 0.889, 1]}, {"time": 1.1667}]}, "bone41": {"rotate": [{"curve": [0.037, 0.06, 0.089, 20.76]}, {"time": 0.3333, "value": 20.76, "curve": [0.611, 20.76, 0.889, -0.42]}, {"time": 1.1667}]}, "hair_L": {"rotate": [{"value": -1.69, "curve": [0.122, -0.04, 0.246, 2.14]}, {"time": 0.3667, "value": 2.14, "curve": [0.56, 2.14, 0.74, -3.47]}, {"time": 0.9333, "value": -3.47, "curve": [1.006, -3.47, 1.095, -2.68]}, {"time": 1.1667, "value": -1.69}]}, "hair_R": {"rotate": [{"value": -1.69, "curve": [0.122, -0.04, 0.246, 2.14]}, {"time": 0.3667, "value": 2.14, "curve": [0.56, 2.14, 0.74, -3.47]}, {"time": 0.9333, "value": -3.47, "curve": [1.006, -3.47, 1.095, -2.68]}, {"time": 1.1667, "value": -1.69}]}, "hair_RR": {"rotate": [{"value": -2.57, "curve": [0.146, -0.99, 0.289, 2.14]}, {"time": 0.4333, "value": 2.14, "curve": [0.627, 2.14, 0.807, -3.47]}, {"time": 1, "value": -3.47, "curve": [1.049, -3.47, 1.12, -3.11]}, {"time": 1.1667, "value": -2.57}]}, "hair_LL": {"rotate": [{"value": -2.57, "curve": [0.146, -0.99, 0.289, 2.14]}, {"time": 0.4333, "value": 2.14, "curve": [0.627, 2.14, 0.807, -3.47]}, {"time": 1, "value": -3.47, "curve": [1.049, -3.47, 1.12, -3.11]}, {"time": 1.1667, "value": -2.57}]}, "headround3": {"translate": [{"x": -49.3, "curve": [0.037, -48.06, 0.089, 299.22, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 299.22, "curve": [0.611, 299.22, 0.889, -49.3, 0.611, 0, 0.889, 0]}, {"time": 1.1667, "x": -49.3}]}, "headround": {"translate": [{"y": 27.3, "curve": [0.111, 0, 0.222, 0, 0.037, 26.22, 0.089, -384.96]}, {"time": 0.3333, "y": -384.96, "curve": [0.611, 0, 0.889, 0, 0.611, -384.96, 0.889, 27.3]}, {"time": 1.1667, "y": 27.3}]}, "bodyround": {"translate": [{"y": 77.57, "curve": [0.089, 0, 0.178, 0, 0.03, 76.39, 0.071, -334.19]}, {"time": 0.2667, "y": -334.19, "curve": [0.567, 0, 0.867, 0, 0.567, -334.19, 0.867, 77.57]}, {"time": 1.1667, "y": 77.57}]}, "tunround": {"translate": [{"x": -215.24, "curve": [0.03, -213.55, 0.071, 376.18, 0.089, 0, 0.178, 0]}, {"time": 0.2667, "x": 376.18, "curve": [0.567, 376.18, 0.867, -215.24, 0.567, 0, 0.867, 0]}, {"time": 1.1667, "x": -215.24}]}}, "attachments": {"default": {"eye_LL": {"eye_LL": {"deform": [{"curve": [0.035, 0, 0.089, 1]}, {"time": 0.3333, "vertices": [-1.0612, -0.04965, -1.06094, -0.04964, -0.47231, -0.06705, -0.47231, -0.06704, -0.47231, -0.06705, -0.47231, -0.06704, -0.33473, -0.13488, -0.33473, -0.13486, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.71676, 0.04355, -0.71676, 0.04358, -2.2217, -0.05975, -2.22166, -0.05965, -3.0458, -0.19032, -3.04567, -0.19022, -3.07072, -0.2184, -3.06986, -0.21836, -1.63462, 0.15573, -1.63436, 0.15579, 0, 0, 0, 0, -1.32804, -0.02702, -1.3273, -0.02695, -2.5474, -0.14379, -2.54697, -0.14371, -3.38482, -0.20722, -3.38422, -0.2071, -3.36939, -0.07278, -3.36939, -0.07272, -2.50783, -0.13444, -2.5077, -0.13434, -0.54364, -0.14768, -0.54364, -0.14765], "curve": [0.611, 0, 1.14, 1.78]}, {"time": 1.1667}]}}, "eye_RR": {"eye_RR": {"deform": [{"curve": [0.035, 0, 0.089, 1]}, {"time": 0.3333, "vertices": [-1.65511, 0.10519, -1.65511, 0.10525, -3.20929, 0.24838, -3.20934, 0.24849, -3.25484, 0.21377, -3.25488, 0.21389, -2.69195, 0.12433, -2.69195, 0.12441, -1.22005, 0.02856, -1.22005, 0.02861, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.54239, 0.09072, -0.54239, 0.09073, -0.66335, 0.04199, -0.66335, 0.04202, -0.66335, 0.04199, -0.66335, 0.04202, -1.10666, 0.12447, -1.1067, 0.12452, -0.56173, 0.06887, -0.56173, 0.0689, -2.47203, 0.2411, -2.47212, 0.24115, -3.48275, 0.22053, -3.48253, 0.22061, -3.42074, 0.22428, -3.42074, 0.2244, -2.77793, 0.18104, -2.7781, 0.18112, -1.52745, -0.01089, -1.52741, -0.01085], "curve": [0.611, 0, 1.14, 1.78]}, {"time": 1.1667}]}}, "head": {"head": {"deform": [{"curve": [0.035, 0, 0.089, 1]}, {"time": 0.3333, "offset": 196, "vertices": [-1.89973, 0.08043, -1.8993, 0.08051, -3.41052, -0.0095, -3.41013, -0.0094, -4.17497, -0.07819, -4.17463, -0.0781, -4.35325, -0.17523, -4.35218, -0.17511, -3.54767, -0.1876, -3.54711, -0.18752, -2.05962, -0.23926, -2.05941, -0.23919, -0.23878, -0.06865, -0.23873, -0.06863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.51327, 0.0154, -1.5125, 0.01545, -2.78618, 0.07254, -2.78493, 0.07263, -3.87531, 0.22193, -3.87454, 0.22211, -4.52401, 0.06315, -4.52298, 0.06325, -4.25691, 0.27962, -4.25605, 0.27978, -3.20439, 0.06903, -3.20482, 0.06908, -1.10069, 0.04625, -1.10086, 0.04627, -0.18743, -0.03042, -0.18743, -0.03041], "curve": [0.611, 0, 1.14, 1.78]}, {"time": 1.1667}]}}, "head2": {"head": {"deform": [{"curve": [0.035, 0, 0.089, 1]}, {"time": 0.3333, "offset": 112, "vertices": [-0.31885, 0.57279, -0.31958, 0.57265, -0.19287, -0.00182, -0.19287, -0.002, -0.83557, -0.00791, -0.83557, -0.00817, -0.4856, -0.0046, -0.4856, -0.00483, -0.31421, -0.00298, -0.31421, -0.00325, -0.48047, -0.00456, -0.48047, -0.00478, -1.40747, -0.01331, -1.40747, -0.01353, -0.88245, -0.00834, -0.88245, -0.00855, -0.30444, -0.43474, -0.30518, -0.43491, -0.005, 0.57575, -0.00574, 0.57576, -0.005, 0.57575, -0.00574, 0.57576, -0.005, 0.57575, -0.00574, 0.57576, 0.01904, 0.57599, 0.01831, 0.57584, 1.59863, 0.01511, 1.59863, 0.0149, 2.86389, 0.02705, 2.86389, 0.02682, 3.34949, 0.03164, 3.34949, 0.03133, 2.85815, 0.027, 2.85815, 0.02675, 1.53284, 0.01448, 1.53284, 0.01425, 0.02039, -0.43167, 0.01965, -0.43186, 0.00439, -0.43183, 0.00366, -0.43184, 0.00439, -0.43183, 0.00366, -0.43184, 0.00439, -0.43183, 0.00366, -0.43184, 0.00439, -0.43183, 0.00366, -0.43184, 0.40869, -0.42801, 0.40796, -0.42804, 1.23816, 0.01169, 1.23816, 0.01148, 1.70679, 0.01612, 1.70679, 0.01582, 1.06311, 0.01004, 1.06311, 0.00972, 0.3855, 0.00363, 0.3855, 0.00335, 0.39929, 0.57957, 0.39856, 0.57956, -0.005, 0.57575, -0.00574, 0.57576, 0.82654, 0.00781, 0.82654, 0.00757, 2.66345, 0.02515, 2.66345, 0.02485, 3.19263, 0.03014, 3.19263, 0.02986, 2.69873, 0.02548, 2.69873, 0.02525, 0.60645, 0.00573, 0.60645, 0.0057, 1.61731, 0.01527, 1.61731, 0.0152, 0.60645, 0.00573, 0.60645], "curve": [0.611, 0, 1.14, 1.78]}, {"time": 1.1667}]}}}}}}}