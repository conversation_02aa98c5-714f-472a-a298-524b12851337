﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

namespace TFW.Map {
    public static class GridObjectIntersectionTest {
        //左移视野时格子的相交性判断
        public static bool LeftCondition(int tileObjMinX, int tileObjMinY, int tileObjMaxX, int tileObjMaxY, int x, int y, int maxX, int maxY) {
            if (tileObjMaxX >= x && tileObjMaxX <= maxX && 
                !(tileObjMinY > maxY || y > tileObjMaxY)) {
                return true;
            }
            return false;
        }
        //右移视野时格子的相交性判断
        public static bool RightCondition(int tileObjMinX, int tileObjMinY, int tileObjMaxX, int tileObjMaxY, int x, int y, int maxX, int maxY) {
            if (tileObjMinX >= x && tileObjMinX <= maxX &&
                !(tileObjMinY > maxY || y > tileObjMaxY)) {
                return true;
            }
            return false;
        }
        //上移视野时格子的相交性判断
        public static bool TopCondition(int tileObjMinX, int tileObjMinY, int tileObjMaxX, int tileObjMaxY, int x, int y, int maxX, int maxY) {
            if (tileObjMaxY >= y && tileObjMaxY <= maxY &&
                !(tileObjMinX > maxX || x > tileObjMaxX)) {
                return true;
            }
            return false;
        }
        //下移视野时格子的相交性判断
        public static bool BottomCondition(int tileObjMinX, int tileObjMinY, int tileObjMaxX, int tileObjMaxY, int x, int y, int maxX, int maxY) {
            if (tileObjMinY >= y && tileObjMinY <= maxY &&
                !(tileObjMinX > maxX || x > tileObjMaxX)) {
                return true;
            }
            return false;
        }
        //任意的判断
        public static bool AnyCondition(int tileObjMinX, int tileObjMinY, int tileObjMaxX, int tileObjMaxY, int x, int y, int maxX, int maxY) {
            return true;
        }
    }
}
