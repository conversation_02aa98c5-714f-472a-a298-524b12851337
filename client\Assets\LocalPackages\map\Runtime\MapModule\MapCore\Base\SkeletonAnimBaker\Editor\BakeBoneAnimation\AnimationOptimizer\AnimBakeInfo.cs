﻿#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;

namespace TFW.Map
{
    //获取prefab的烘培信息,方便优化时比对prefab之间能否共享动画数据
    public class AnimBakeInfo
    {
        public bool Process(string animDataPath)
        {
            mBakedAnimationData = AssetDatabase.LoadAssetAtPath<BakedAnimationData>(animDataPath);
            if (mBakedAnimationData == null || string.IsNullOrEmpty(mBakedAnimationData.originalPrefabGuid))
            {
                return false;
            }

            //动态组装,无法优化
            string assetPath = AssetDatabase.GUIDToAssetPath(mBakedAnimationData.originalPrefabGuid);
            mOriginalPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            if (mOriginalPrefab == null)
            {
                return false;
            }

            var skinRenderers = mOriginalPrefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            AddBones(skinRenderers);

            return true;
        }

        public bool CanShareAnimation(AnimBakeInfo other)
        {
            if (other.mBakedAnimationData == null || mBakedAnimationData == null)
            {
                return false;
            }

            if (other.mOriginalPrefab == null || mOriginalPrefab == null)
            {
                return false;
            }

            if (other.mBakedAnimationData.animationControllerAssetGuid != mBakedAnimationData.animationControllerAssetGuid)
            {
                return false;
            }

            bool sameBoneHierarchy = SameBoneHierarchy(other.mBoneManager);
            if (!sameBoneHierarchy)
            {
                return false;
            }

            return true;
        }

        bool SameBoneHierarchy(BoneManager otherBoneManager)
        {
            if (otherBoneManager.boneCount != mBoneManager.boneCount)
            {
                return false;
            }

            var rootBone = mBoneManager.GetRootBone();
            var otherRootBone = otherBoneManager.GetRootBone();
            return Utils.SameHierarchy(rootBone, otherRootBone);
        }

        void AddBones(SkinnedMeshRenderer[] renderers)
        {
            if (renderers != null)
            {
                for (int i = 0; i < renderers.Length; ++i)
                {
                    var renderer = renderers[i] as SkinnedMeshRenderer;
                    var bones = renderer.bones;
                    for (int k = 0; k < bones.Length; ++k)
                    {
                        mBoneManager.AddBone(bones[k]);
                    }
                }
            }
        }

        public BakedAnimationData bakedAnimationData { get { return mBakedAnimationData; } }
        public GameObject originalPrefab { get { return mOriginalPrefab; } }

        BoneManager mBoneManager = new BoneManager();
        BakedAnimationData mBakedAnimationData;
        GameObject mOriginalPrefab;
    }
}


#endif