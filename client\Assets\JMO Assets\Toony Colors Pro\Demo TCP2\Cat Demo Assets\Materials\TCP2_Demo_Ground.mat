%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Ground
  m_Shader: {fileID: 4800000, guid: 7cf6e0bd6774e2a438d4701ceb5e6bae, type: 3}
  m_ShaderKeywords: _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BlendTex1
      second:
        m_Texture: {fileID: 2800000, guid: 5c5e55a9fbc4aa64a92b1555198f8bb8, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _BlendTex2
      second:
        m_Texture: {fileID: 2800000, guid: 379dbc64b9e961945bb85ccde59979cd, type: 3}
        m_Scale: {x: 0.75, y: 0.75}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 2800000, guid: de3d69e834491a34e9aceb298acdefa2, type: 3}
        m_Scale: {x: 6, y: 6}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailAlbedoMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailMask
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DetailNormalMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _EmissionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 2800000, guid: 31943228e69a5b54992b5322327313e7, type: 3}
        m_Scale: {x: 6, y: 6}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MetallicGlossMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _NoTileNoiseTex
      second:
        m_Texture: {fileID: 2800000, guid: c4210660884c17f4db192d9c5bf8de3b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _OcclusionMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _ParallaxMap
      second:
        m_Texture: {fileID: 2800000, guid: 45fa99a31bbf30d43ad28f4aa56c342d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _TexBlendMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _BlendContrast
      second: 1
    - first:
        name: _BumpScale
      second: 1
    - first:
        name: _Cutoff
      second: 0.5
    - first:
        name: _DetailNormalMapScale
      second: 1
    - first:
        name: _DstBlend
      second: 0
    - first:
        name: _GlossMapScale
      second: 1
    - first:
        name: _Glossiness
      second: 0.5
    - first:
        name: _GlossyReflections
      second: 1
    - first:
        name: _Metallic
      second: 0
    - first:
        name: _Mode
      second: 0
    - first:
        name: _OcclusionStrength
      second: 1
    - first:
        name: _Parallax
      second: 0.0312
    - first:
        name: _RampSmooth
      second: 0.5
    - first:
        name: _RampSmoothDir
      second: 0.5
    - first:
        name: _RampSmoothPoint
      second: 0.5
    - first:
        name: _RampSmoothSpot
      second: 0.15
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RampThresholdDir
      second: 0.5
    - first:
        name: _RampThresholdPoint
      second: 0.5
    - first:
        name: _RampThresholdSpot
      second: 0.5
    - first:
        name: _Shadow_HSV_H
      second: -10
    - first:
        name: _Shadow_HSV_S
      second: 0
    - first:
        name: _Shadow_HSV_V
      second: 0
    - first:
        name: _SmoothnessTextureChannel
      second: 0
    - first:
        name: _SpecularHighlights
      second: 1
    - first:
        name: _SrcBlend
      second: 1
    - first:
        name: _UVSec
      second: 0
    - first:
        name: _ZWrite
      second: 1
    - first:
        name: __BeginGroup_OtherLights
      second: 1
    - first:
        name: __EndGroup
      second: 0
    - first:
        name: __dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _EmissionColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _HColor
      second: {r: 0.9411765, g: 0.9411765, b: 0.9411765, a: 1}
    - first:
        name: _SColor
      second: {r: 0.63235295, g: 0.63235295, b: 0.63235295, a: 1}
    - first:
        name: _VColorBlendOffset
      second: {r: 1, g: 0.5, b: 0, a: 0}
    - first:
        name: _VColorBlendSmooth
      second: {r: 0.3, g: 0.65, b: 0.25, a: 0.25}
