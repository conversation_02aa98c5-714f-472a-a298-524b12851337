﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //根据相机高度改变fov
    public abstract class ZoomActionBase : CameraAction
    {
        public ZoomActionBase(CameraActionType updateOrder) : base(updateOrder)
        {
        }

        public Vector3 GetCameraPos()
        {
            return mCameraPos;
        }

        public void AutoZoomToVDF(float targetVDF, float duration)
        {
            SetCameraVDF(targetVDF, duration);
        }

        public float GetCurrentCameraVDF()
        {
            var camera = Map.currentMap.camera;
            return mCameraViewDistance * camera.fieldOfView;
        }

        public void Init()
        {
            mIsAutoZooming = false;
            mZoomStartTime = 0;
            mZoomEndTime = 0;
            mZoomStartVDF = 0;
            mZoomCurrentVDF = 0;
            mZoomEndVDF = 0;
            mCameraPos = MapCameraMgr.updatedCameraPosition;
            if (Map.currentMap != null)
            {
                mCameraViewDistance = (mCameraPos - Map.currentMap.viewCenter).magnitude;
            }
            else
            {
                mCameraViewDistance = 0;
            }
        }

        public void TouchZoomed(float centerX, float centerY, float scrollRate)
        {
            if (!mIsAutoZooming)
            {
                var camera = Map.currentMap.camera.firstCamera;
                Vector3 zoomTerrainPos = FromScreenToWorldPosition(new Vector3(centerX, centerY, 0), camera);

                float vdf = mZoomStartVDF * scrollRate;
                float dist = 0f;
                var cameraSetting = MapCameraMgr.cameraSetting;
                if (cameraSetting.GetDistFovByVDF(vdf, out dist, out _))
                {
                    mCameraViewDistance = dist;
                    var viewCenter = Map.currentMap.viewCenter;
                    CalculateCameraPositionFromViewPosition(camera, viewCenter);
                    Vector3 zoomCenter = FromScreenToWorldPosition(new Vector3(centerX, centerY, 0), camera);

                    Vector3 delta = zoomCenter - zoomTerrainPos;
                    float dx = viewCenter.x - delta.x;
                    float dz = viewCenter.z - delta.z;
                    CalculateCameraPositionFromViewPosition(camera, new Vector3(dx, 0f, dz));
                }
            }
        }

        public void TouchZoomedAccumulate(float centerX, float centerY, float deltaScrollRate)
        {
            if (!mIsAutoZooming)
            {
                var camera = Map.currentMap.camera.firstCamera;
                Vector3 zoomTerrainPos = FromScreenToWorldPosition(new Vector3(centerX, centerY, 0), camera);

                mZoomCurrentVDF += deltaScrollRate;
                float dist = 0f;
                var cameraSetting = MapCameraMgr.cameraSetting;
                if (cameraSetting.GetDistFovByVDF(mZoomCurrentVDF, out dist, out _))
                {
                    mCameraViewDistance = dist;
                    var viewCenter = Map.currentMap.viewCenter;
                    CalculateCameraPositionFromViewPosition(camera, viewCenter);
                    Vector3 zoomCenter = FromScreenToWorldPosition(new Vector3(centerX, centerY, 0), camera);

                    Vector3 delta = zoomCenter - zoomTerrainPos;
                    float dx = viewCenter.x - delta.x;
                    float dz = viewCenter.z - delta.z;
                    CalculateCameraPositionFromViewPosition(camera, new Vector3(dx, 0f, dz));
                }
            }
        }

        public void Reset()
        {
            mCameraPos = MapCameraMgr.updatedCameraPosition;
            mCameraViewDistance = (mCameraPos - Map.currentMap.viewCenter).magnitude;
            mZoomStartVDF = GetCurrentCameraVDF();
            mZoomCurrentVDF = mZoomStartVDF;
        }

        public void SetCameraVDF(float vdf, float interpolateTime)
        {
            SetCameraVDFCurve(vdf, interpolateTime);
        }

        public void SetCameraVDFCurve(float vdf, float interpolateTime)
        {
            if (!mIsAutoZooming)
            {
                mIsAutoZooming = true;
                mZoomStartTime = (int)(Time.realtimeSinceStartup * 1000f);
                mZoomEndTime = mZoomStartTime + ((int)interpolateTime);
                mZoomStartVDF = GetCurrentCameraVDF();
                mZoomEndVDF = vdf;
            }
                
            MapCameraMgr.ResetAutoUpdateTargetHeight();
        }

        public void CalculateCameraPositionFromViewPosition(Camera camera, Vector3 pos)
        {
            var viewCenter = Map.currentMap.viewCenter;
            if ((float.IsNaN(pos.x) || float.IsNaN(pos.y)) || float.IsNaN(pos.z))
            {
                pos = viewCenter;
            }
            mCameraPos = pos - camera.transform.forward * mCameraViewDistance;
        }

        public bool UpdateZooming()
        {
            if (mIsAutoZooming)
            {
                var camera = Map.currentMap.camera.firstCamera;
                float currentTime = Time.realtimeSinceStartup * 1000f;
                if (currentTime >= mZoomEndTime)
                {
                    mIsAutoZooming = false;
                    float fov = 0f;
                    float dist = 0f;
                    var cameraSetting = MapCameraMgr.cameraSetting;
                    if (cameraSetting.GetDistFovByVDF((float)mZoomEndVDF, out dist, out fov))
                    {
                        mCameraViewDistance = dist;
                        var viewCenter = Map.currentMap.viewCenter;
                        CalculateCameraPositionFromViewPosition(camera, viewCenter);
                    }

                    return true;
                }
                else
                {
                    float zoomDeltaVDF = mZoomEndVDF - mZoomStartVDF;
                    float f = (currentTime - mZoomStartTime) / (mZoomEndTime - mZoomStartTime);

                    float f1 = 0.7f;
                    float f2 = 1f - f1;
                    if (f < f1)
                    {
                        f /= f1;
                        f *= f;
                        f *= f1;
                    }
                    else
                    {
                        f = (f - f1) / f2;
                        f = (Mathf.Sqrt(f) * f2) + f1;
                    }

                    float vdf = ((float)mZoomStartVDF) + zoomDeltaVDF * f;
                    float fov = 0f;
                    float dist = 0f;
                    var cameraSetting = MapCameraMgr.cameraSetting;
                    if (cameraSetting.GetDistFovByVDF(vdf, out dist, out fov))
                    {
                        mCameraViewDistance = dist;
                        var viewCenter = Map.currentMap.viewCenter;

                        CalculateCameraPositionFromViewPosition(camera, new Vector3(viewCenter.x, 0f, viewCenter.z));
                    }
                }
            }
            return false;
        }

        public static Vector3 FromScreenToWorldPosition(Vector3 screenPos, Camera camera)
        {
            var ray = camera.ScreenPointToRay(screenPos);
            Vector3 intersection = Vector3.zero;
            if (!Mathf.Approximately(ray.direction.y, 0))
            {
                float t = -ray.origin.y / ray.direction.y;
                intersection = ray.origin + ray.direction * t;
            }
            return intersection;
        }

        float mZoomStartVDF;
        float mZoomCurrentVDF;
        float mZoomEndVDF;
        bool mIsAutoZooming;
        int mZoomStartTime;
        int mZoomEndTime;
        //相机forward与地表的距离
        float mCameraViewDistance;

        Vector3 mCameraPos = Vector3.zero;
    }
}