﻿ 



 
 



/*
 * created by wzw at 2019.11.27
 */

using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class RiverLayer : MapLayerBase
    {
        public RiverLayer(Map map) : base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                map.DestroyObject(mLayerData);
                mLayerData = null;
            }
        }

        public override void Unload()
        {
            map.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var sourceLayer = layerData as config.RiverLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + setting.origin);

            //读取LOD配置信息
            MapLayerLODConfig config = null;
            if (layerData.config != null)
            {
                int lodCount = layerData.config.lodConfigs.Length;
                MapLayerLODConfig.LOD[] lods = new MapLayerLODConfig.LOD[lodCount];
                for (int i = 0; i < lodCount; ++i)
                {
                    var srcLOD = layerData.config.lodConfigs[i];
                    lods[i] = new MapLayerLODConfig.LOD(srcLOD.name, srcLOD.changeZoom, srcLOD.changeZoomThreshold, srcLOD.hideObject, srcLOD.shaderLOD, srcLOD.useRenderTexture, srcLOD.flag, srcLOD.terrainLODTileCount);
                }
                config = new MapLayerLODConfig(map, lods);
            }
            mLayerData = new QuadTreeObjectLayerData(header, config, map, null, GetScaleConfig());
            mLayerView = new RiverLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);
            mLayerData.SetObjectScaleChangeCallback(mLayerView.OnObjectScaleChange);

            mLayerData.isLoading = true;
            if (sourceLayer.objects != null)
            {
                int n = sourceLayer.objects.Length;
                for (int i = 0; i < n; ++i)
                {
                    var model = sourceLayer.objects[i] as config.RiverData;
                    var modelTemplate = map.FindObject(model.modelTemplateID) as ModelTemplate;
                    if (modelTemplate == null)
                    {
                        Debug.Assert(false, $"model template{model.modelTemplateID} is not found!");
                    }
                    else
                    {
                        var modelData = new RiverData(model.id, map, 0, model.position + header.origin, model.rotation, model.scale, modelTemplate, true, model.hideLOD);
                        mLayerData.AddObjectData(modelData);
                    }
                }
            }
            mLayerData.isLoading = false;

            map.AddMapLayer(this);
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, objectData.GetAssetPath(), false, false);
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);
                    return true;
                }
            }
            return false;
        }

        public int AddObject(string prefabPath, Vector3 position, Quaternion rot, Vector3 scale, int objectID = 0)
        {
            if (objectID == 0)
            {
                objectID = map.nextCustomObjectID;
            }
            ModelTemplate modelTemplate = map.GetOrCreateModelTemplate(objectID, prefabPath, false, false);
            var data = new RiverData(objectID, map, 0, position, rot, scale, modelTemplate, true, -1);
            bool success = mLayerData.AddObjectData(data);
            if (success)
            {
                mLayerView.AddObjectView(data);
            }
            return data.id;
        }

        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                mLayerView.RemoveObjectView(objectDataID);
            }
            return success;
        }

        public void RemoveAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }
        }

        public void ShowObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, mLayerData.currentLOD);
            }
        }

        public void HideObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, false, mLayerData.currentLOD);
            }
        }

        public GameObject GetObjectGameObject(int dataID)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public IMapObjectData FindObjectAtPosition(Vector3 pos)
        {
            return mLayerData.FindObjectAtPosition(pos, 0);
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        //如果物体要缩放,就要返回非空的scale config
        public virtual KeepScaleConfig GetScaleConfig()
        {
            return null;
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            throw new System.NotImplementedException();
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public MapObjectLayerData layerData { get { return mLayerData; } }
        public RiverLayerView layerView { get { return mLayerView; } }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override int lodCount => mLayerData.lodCount;

        protected MapObjectLayerData mLayerData;
        protected RiverLayerView mLayerView;
    }
}
