﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */


using UnityEngine;

namespace TFW.Map {
    [Black]
    public class MapLayerXYRectangleBoundsCreator : MapLayerXZRectangleBoundsCreator {
        protected override Mesh CreateBoundsMesh(float width, float height, bool offset) {
            var mesh = new Mesh();
            Vector3[] positions = new Vector3[4];
            int[] indices = new int[8]{
            0,1,1,2,2,3,3,0,
            };
            float hw = width * 0.5f;
            float hh = height * 0.5f;
            positions[0] = new Vector3(-hw, -hh, 0);
            positions[1] = new Vector3(hw, -hh, 0);
            positions[2] = new Vector3(hw, hh, 0);
            positions[3] = new Vector3(-hw, hh, 0);

            mesh.vertices = positions;
            mesh.SetIndices(indices, MeshTopology.Lines, 0);
            mesh.RecalculateBounds();

            return mesh;
        }
    }
}

#endif