﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    public static class RiverCombiner
    {
        //注意,现在combine material是默认所有的水都使用相同的材质的,只是mask texture不同
        public static void Combine(string riverAssetFolder, bool combineMaterial, bool onlyCombineLOD0)
        {
            var dirIter = Directory.EnumerateFiles(riverAssetFolder);

            //获取该文件夹下所有的河流prefab
            List<GameObject> allRiverPrefabs = new List<GameObject>();
            foreach (var p in dirIter)
            {
                string ext = Utils.GetExtension(p);
                if (ext == "prefab")
                {
                    var path = Utils.ConvertToUnityAssetsPath(p);
                    var lod = Utils.GetPrefabNameLOD(path);
                    if (onlyCombineLOD0 && lod > 0)
                    {
                        continue;
                    }
                    var prefab = MapModuleResourceMgr.LoadPrefab(path);
                    if (prefab != null)
                    {
                        allRiverPrefabs.Add(prefab);
                    }
                }
            }
            //新资源的路径
            string newAssetFolder = riverAssetFolder + "/CombinedAssets";
            //需要删除的旧的贴图
            List<string> oldTextures = new List<string>();
            //需要删除的旧的材质
            List<string> oldMaterials = new List<string>();
            //唯一的mask texture列表
            List<Texture2D> uniqueMaskTextures = new List<Texture2D>();
            //某个texture被引用的prefab列表,key是uniqueMaskTextures的索引
            Dictionary<int, List<GameObject>> prefabUsedTextures = new Dictionary<int, List<GameObject>>();
            Dictionary<int, List<string>> textureIndexToOldTexturePathList = new Dictionary<int, List<string>>();
            //render texture河流的prefab列表
            List<GameObject> textureRiverPrefabs = new List<GameObject>();
            //遍历所有河流prefab
            for (int i = 0; i < allRiverPrefabs.Count; ++i)
            {
                var meshRenderer = allRiverPrefabs[i].GetComponent<MeshRenderer>();
                if (meshRenderer != null)
                {
                    var riverMaterial = meshRenderer.sharedMaterial;
                    if (riverMaterial.HasProperty(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME))
                    {
                        //非render texture的河流,判断是否有mask texture
                        var maskTexture = meshRenderer.sharedMaterial.GetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME);
                        if (maskTexture != null)
                        {
                            //材质原来的路径,原来的材质会被删除,新材质在新的文件夹下
                            string oldMtlPath = AssetDatabase.GetAssetPath(riverMaterial);
                            oldMaterials.Add(oldMtlPath);
                            //mask贴图原来的路径
                            string texturePath = AssetDatabase.GetAssetPath(maskTexture);
                            oldTextures.Add(texturePath);
                            //创建一个可以访问pixel的texture副本,用于对比mask数据是否一致
                            var readableMaskTexture = EditorUtils.CreateTexture(texturePath, false);
                            if (readableMaskTexture != null)
                            {
                                readableMaskTexture.name = Utils.GetPathName(texturePath, false);
                                //计算这个texture的index
                                int textureIndex = GetUniqueMaskTexture(readableMaskTexture, uniqueMaskTextures);
                                if (textureIndex == -1)
                                {
                                    uniqueMaskTextures.Add(readableMaskTexture);
                                    textureIndex = uniqueMaskTextures.Count - 1;
                                }
                                else
                                {
                                    Object.DestroyImmediate(readableMaskTexture);
                                }

                                //保存index与旧的mask贴图路径的映射关系
                                AddToList(textureIndexToOldTexturePathList, textureIndex, texturePath);

                                //将这个prefab添加到引用texture的列表中
                                List<GameObject> prefabList;
                                prefabUsedTextures.TryGetValue(textureIndex, out prefabList);
                                if (prefabList == null)
                                {
                                    prefabList = new List<GameObject>();
                                    prefabUsedTextures.Add(textureIndex, prefabList);
                                }
                                prefabList.Add(allRiverPrefabs[i]);
                            }
                        }
                    }
                    else if (riverMaterial.shader.name == "SLGMaker/TextureRiver")
                    {
                        //因为合并材质后贴图换成新的了,所以要修正渲染到贴图的水prefab的alpha贴图引用
                        var maskTexture = meshRenderer.sharedMaterial.GetTexture(MapCoreDef.RIVER_RENDER_TEXTURE_ALPHA_PROPERTY_NAME);
                        if (maskTexture != null)
                        {
                            string oldTexturePath = AssetDatabase.GetAssetPath(maskTexture);
                            if (string.IsNullOrEmpty(oldTexturePath))
                            {
                                Debug.LogError($"Alpha texture of {meshRenderer.gameObject.name} is not asset!");
                            }
                            else
                            {
                                textureRiverPrefabs.Add(allRiverPrefabs[i]);
                            }
                        }
                        else
                        {
                            Debug.LogError($"Alpha texture of {meshRenderer.gameObject.name} is not found!");
                        }
                    }
                }
            }

            //generate combined rivers
            //创建新的文件夹,存放新的贴图和材质
            if (!Directory.Exists(newAssetFolder))
            {
                Directory.CreateDirectory(newAssetFolder);
            }

            //旧的贴图路径到新的贴图的映射关系,用于将render texture的河流材质的alpha贴图替换成新的贴图
            Dictionary<string, Texture2D> texturePathMapping = new Dictionary<string, Texture2D>();
            //遍历非render texture河流的贴图引用列表
            foreach (var p in prefabUsedTextures)
            {
                //创建新mask贴图
                int textureIndex = p.Key;
                var newTexture = CreateTextureAsset(uniqueMaskTextures[textureIndex], newAssetFolder);
                List<string> useThisTextureIndexOldTextures;
                textureIndexToOldTexturePathList.TryGetValue(textureIndex, out useThisTextureIndexOldTextures);
                if (useThisTextureIndexOldTextures != null)
                {
                    for (int i = 0; i < useThisTextureIndexOldTextures.Count; ++i)
                    {
                        texturePathMapping[useThisTextureIndexOldTextures[i]] = newTexture;
                    }
                }

                Material mtl = null;
                var prefabList = p.Value;
                foreach (var prefab in prefabList)
                {
                    //创建新prefab,新的prefab引用新的material和新的mask贴图
                    string prefabPath = AssetDatabase.GetAssetPath(prefab);
                    var newPrefab = GameObject.Instantiate<GameObject>(prefab);
                    if (combineMaterial)
                    {
                        //是否创建新的材质,这里没有对比材质的其他参数
                        if (mtl == null)
                        {
                            var oldMtl = newPrefab.GetComponent<MeshRenderer>().sharedMaterial;
                            string mtlPath = newAssetFolder + "/" + oldMtl.name + ".mat";
                            mtl = Object.Instantiate<Material>(oldMtl);
                            mtl.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, newTexture);
                            AssetDatabase.CreateAsset(mtl, mtlPath);
                        }
                        //所有使用同样mask贴图的河流都使用同一个材质
                        newPrefab.GetComponent<MeshRenderer>().sharedMaterial = mtl;
                    }
                    else
                    {
                        //每个河流使用单独的材质
                        newPrefab.GetComponent<MeshRenderer>().sharedMaterial.SetTexture(MapCoreDef.RIVER_TEXTURE_ALPHA_PROPERTY_NAME, newTexture);
                    }

                    //保存prefab
                    PrefabUtility.SaveAsPrefabAsset(newPrefab, prefabPath);
                    Object.DestroyImmediate(newPrefab);
                }
            }

            //fix render texture river's alpha texture
            FixRenderTextureRiverAlphaTexture(textureRiverPrefabs, texturePathMapping);

            for (int i = 0; i < uniqueMaskTextures.Count; ++i)
            {
                Object.DestroyImmediate(uniqueMaskTextures[i]);
            }

            //删除旧的贴图
            for (int i = 0; i < oldTextures.Count; ++i)
            {
                FileUtil.DeleteFileOrDirectory(oldTextures[i]);
            }
            //删除旧的材质
            for (int i = 0; i < oldMaterials.Count; ++i)
            {
                FileUtil.DeleteFileOrDirectory(oldMaterials[i]);
            }
            AssetDatabase.Refresh();
        }

        static int GetUniqueMaskTexture(Texture2D maskTexture, List<Texture2D> uniqueTextures)
        {
            for (int i = 0; i < uniqueTextures.Count; ++i)
            {
                if (TextureContentEqual(maskTexture, uniqueTextures[i]))
                {
                    return i;
                }
            }

            return -1;
        }

        static bool TextureContentEqual(Texture2D a, Texture2D b)
        {
            if (a.format != b.format)
            {
                return false;
            }
            if (a.width != b.width || a.height != b.height)
            {
                return false;
            }

            var pa = a.GetPixels();
            var pb = b.GetPixels();

            int n = pa.Length;
            for (int i = 0; i < n; ++i)
            {
                if (pa[i] != pb[i])
                {
                    return false;
                }
            }

            return true;
        }

        static Texture2D CreateTextureAsset(Texture2D newTexture, string folder)
        {
            string texturePath = folder + "/" + newTexture.name + ".png";
            byte[] bytes = newTexture.EncodeToPNG();
            System.IO.File.WriteAllBytes(texturePath, bytes);
            AssetDatabase.Refresh();
            var unityTexturePath = Utils.ConvertToUnityAssetsPath(texturePath);
            newTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(unityTexturePath);
            return newTexture;
        }

        //将render texture河流材质的贴图替换
        static void FixRenderTextureRiverAlphaTexture(List<GameObject> textureRiverPrefabs, Dictionary<string, Texture2D> texturePathMapping)
        {
            for (int i = 0; i < textureRiverPrefabs.Count; ++i)
            {
                var mtl = textureRiverPrefabs[i].GetComponent<MeshRenderer>().sharedMaterial;
                var alphaTexture = mtl.GetTexture(MapCoreDef.RIVER_RENDER_TEXTURE_ALPHA_PROPERTY_NAME);
                var oldTexturePath = AssetDatabase.GetAssetPath(alphaTexture);
                Texture2D newTexture;
                texturePathMapping.TryGetValue(oldTexturePath, out newTexture);
                Debug.Assert(newTexture != null);
                mtl.SetTexture(MapCoreDef.RIVER_RENDER_TEXTURE_ALPHA_PROPERTY_NAME, newTexture);
                AssetDatabase.SaveAssets();
            }
        }

        static void AddToList(Dictionary<int, List<string>> textureIndexToOldTexturePathList, int textureIndex, string texturePath)
        {
            List<string> texturePaths;
            textureIndexToOldTexturePathList.TryGetValue(textureIndex, out texturePaths);
            if (texturePaths == null)
            {
                texturePaths = new List<string>();
                textureIndexToOldTexturePathList[textureIndex] = texturePaths;
            }
            texturePaths.Add(texturePath);
        }
    }
}

#endif