Shader "K1/Particle/Light/Trail"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" { }
    }
    
    SubShader
    {
        Tags { "IGNOREPROJECTOR" = "true" "QUEUE" = "Transparent+38" "RenderType" = "Transparent" }

        Pass
        {
            Tags { "IGNOREPROJECTOR" = "true" "QUEUE" = "Transparent+38" "RenderType" = "Transparent" }
            Blend SrcAlpha OneMinusSrcAlpha
            //ZClip Off
            //ZTest Off
            ZWrite Off
            //Cull Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : Color;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : Color;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            v2f vert (appdata v)
            {
                v2f o;
  
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
              return tex2D (_MainTex, i.uv) * i.color;
            }
            ENDCG
        }
    }
}