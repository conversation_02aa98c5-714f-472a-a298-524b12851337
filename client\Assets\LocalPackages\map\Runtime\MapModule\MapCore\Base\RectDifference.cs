﻿ 



 
 

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public class RectI
    {
        public void Clear()
        {
            xMin = int.MaxValue;
            xMax = int.MinValue;
            yMin = int.MaxValue;
            yMax = int.MinValue;
        }

        public void Set(int xMin, int yMin, int xMax, int yMax)
        {
            this.xMin = xMin;
            this.xMax = xMax;
            this.yMin = yMin;
            this.yMax = yMax;
        }

        public bool IsSame(RectI r)
        {
            return xMin == r.xMin && yMin == r.yMin && xMax == r.xMax && yMax == r.yMax;
        }

        public bool Contains(RectI r)
        {
            if (r.xMin >= xMin && r.yMin >= yMin &&
                r.xMax <= xMax && r.yMax <= yMax)
            {
                return true;
            }
            return false;
        }

        public void Enlarge(int x, int y, int width, int height)
        {
            xMin = Mathf.Min(xMin, x);
            yMin = Mathf.Min(yMin, y);
            xMax = Mathf.Max(xMax, x + width - 1);
            yMax = Mathf.Max(yMax, y + height - 1);
        }

        public RectI GetIntersection(RectI other)
        {
            int minX = Mathf.Max(xMin, other.xMin);
            int minY = Mathf.Max(yMin, other.yMin);
            int maxX = Mathf.Min(xMax, other.xMax);
            int maxY = Mathf.Min(yMax, other.yMax);
            var r = new RectI();
            r.Set(minX, minY, maxX, maxY);
            return r;
        }

        public override string ToString()
        {
            return $"{xMin}, {yMin}, {xMax}, {yMax}";
        }

        public bool IsEmpty()
        {
            return xMin > xMax || yMin > yMax;
        }

        public int xMin;
        public int yMin;
        public int xMax;
        public int yMax;
    }

    public class RectDifference
    {
        public RectDifference()
        {
            mHalfSpaces = new RectI[8];
            for (int i = 0; i < mHalfSpaces.Length; ++i)
            {
                mHalfSpaces[i] = new RectI();
            }
        }

        public void Calculate(RectI oldRect, RectI newRect, List<RectInt> visibleGrids, List<RectInt> invisibleGrids)
        {
            visibleGrids.Clear();
            invisibleGrids.Clear();
            //calculate invisible grids
            CalculateDifference(oldRect, newRect, invisibleGrids);
            CalculateDifference(newRect, oldRect, visibleGrids);
        }

        //计算a-b
        void CalculateDifference(RectI a, RectI b, List<RectInt> results)
        {
            CalculateHalfSpace(b, mHalfSpaces);
            for (int i = 0; i < mHalfSpaces.Length; ++i)
            {
                int minX = Mathf.Max(a.xMin, mHalfSpaces[i].xMin);
                int maxX = Mathf.Min(a.xMax, mHalfSpaces[i].xMax);
                if (minX <= maxX)
                {
                    int minY = Mathf.Max(a.yMin, mHalfSpaces[i].yMin);
                    int maxY = Mathf.Min(a.yMax, mHalfSpaces[i].yMax);
                    if (minY <= maxY)
                    {
                        results.Add(new RectInt(minX, minY, maxX - minX, maxY - minY));
                    }
                }
            }
        }


        /* 2 3 4 
         * 1   5
         * 0 7 6
         */
        void CalculateHalfSpace(RectI rect, RectI[] halfSpaces)
        {
            int minVal = -1000000;
            int maxVal = 1000000;
            halfSpaces[0].Set(minVal, minVal, rect.xMin - 1, rect.yMin - 1);
            halfSpaces[1].Set(minVal, rect.yMin, rect.xMin - 1, rect.yMax);
            halfSpaces[2].Set(minVal, rect.yMax + 1, rect.xMin - 1, maxVal);
            halfSpaces[3].Set(rect.xMin, rect.yMax + 1, rect.xMax, maxVal);
            halfSpaces[4].Set(rect.xMax + 1, rect.yMax + 1, maxVal, maxVal);
            halfSpaces[5].Set(rect.xMax + 1, rect.yMin, maxVal, rect.yMax);
            halfSpaces[6].Set(rect.xMax + 1, minVal, maxVal, rect.yMin - 1);
            halfSpaces[7].Set(rect.xMin, minVal, rect.xMax, rect.yMin - 1);
        }

        RectI[] mHalfSpaces;
    }
}