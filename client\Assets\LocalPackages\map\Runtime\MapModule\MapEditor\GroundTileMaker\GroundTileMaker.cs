﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    [ExecuteInEditMode]
    public partial class GroundTileMaker : MonoBehaviour
    {
        public enum PaintMode
        {
            Paint1_2_4_8_3_5_10_12_15,
            Paint6,
            Paint9,
            Paint7,
            Paint11,
            Paint13,
            Paint14,
            Paint_1_2_4_8,
            Paint_1_2_4_8_12_3,
            Paint_1_2_4_8_5_10,
            Paint_1_2_6_8,
            Pain<PERSON>_9_2_4_8,
            <PERSON><PERSON>_14_9_7,
            <PERSON><PERSON>_13_11_6,
            <PERSON><PERSON>_11_9_6,
            <PERSON><PERSON>_11_7_14_13,
            <PERSON>t_10_5,
            Pain<PERSON>_10_1,
            Pain<PERSON>_10_4,
            AllTiles,
        }

        public enum Operation
        {
            PaintTexture,
            PaintEdge,
        }

        public class MaskTexture
        {
            public string shaderPropertyName;
            public Texture2D texture;
            public Color[] textureData;
            public Color[] beforePaintingTextureData;
            public bool dirty = false;
            public RectI paintDirtyRange = new RectI();
        }

        public enum EdgeDirection
        {
            Up,
            Down,
            Left,
            Right,
        }

        //
        public enum AtlasUVChannel
        {
            UV0,
            UV1,
            UV2,
            UV3,
            UV4,
            UV5,
            UV6,
            UV7,
        }

        public class MaskTextureSetting
        {
            public string shaderPropertyName;
            public int resolution;
            public AtlasUVChannel uvChannel = AtlasUVChannel.UV1;
            public bool initChannelData;
            public bool normalizeColor;
        }

        void OnDestroy()
        {
            Uninit();
        }

        public void Init(string tilesetName, int tileCount, float tileSize, List<GroundLODMaterialSetting> lodMaterialSettings, string runtimeAssetOutputFolder, string editorAssetOutputFolder, bool createTiles, float fixedBrushRotationAngle, bool randomBrushRotation)
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }

            MapModuleResourceMgr.SetResourceManagerImpl(new EditorResourceMgr());
            mInstance = this;
            mLODMaterialSettings = lodMaterialSettings;
            mEditorAssetOutputFolder = editorAssetOutputFolder;
            mRuntimeAssetOutputFolder = runtimeAssetOutputFolder;
            mTileSetName = tilesetName;
            mTileSize = tileSize;
            mRandomBrushRotation = randomBrushRotation;
            mFixedBrushRotationAngle = fixedBrushRotationAngle;
            mBrushManager = new BrushManager();
            mColor32ArrayPool = ArrayPool<Color32>.Create();

            if (createTiles)
            {
                CreateTiles(tileCount);
            }
            CreateGrid(tileCount);
            CreateCamera();

            mActionManager = new ActionManager();
            mActionManager.finishRedoActionEvent += OnFinishRedoUndo;
            mActionManager.finishUndoActionEvent += OnFinishRedoUndo;
        }

        void OnFinishRedoUndo(EditorAction action)
        {
            RefreshMaskTextures(false);
        }

        public void Uninit()
        {
            if (mTiles != null)
            {
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    if (mTiles[i] != null)
                    {
                        mTiles[i].OnDestroy();
                    }
                }

                mLODMaterialSettings.Clear();

                int h = horizontalInstanceCount;
                int v = verticalInstanceCount;
                for (int i = 0; i < v; ++i)
                {
                    for (int j = 0; j < h; ++j)
                    {
                        GameObject.DestroyImmediate(mInstances[j, i]?.gameObject);
                    }
                }

                GameObject.DestroyImmediate(mGridObject);
                mGridObject = null;

                GameObject.DestroyImmediate(mCamera);
                mCamera = null;

                mTiles = null;
                mInstances = null;

                mActionManager.OnDestroy();
                mActionManager = null;

                mInstance = null;

                mColor32ArrayPool = null;
            }

            if (mBrushManager != null)
            {
                mBrushManager.OnDestroy();
            }
        }

        public void AddLOD(string materialPath, List<MaskTextureSetting> maskTextureSetting, bool copyFromLastLOD)
        {
            var lodMaterialSetting = new GroundLODMaterialSetting(materialPath, maskTextureSetting);
            mLODMaterialSettings.Add(lodMaterialSetting);

            for (int i = 0; i < mTiles.Length; ++i)
            {
                var variations = mTiles[i].variations;
                for (int v = 0; v < variations.Count; ++v)
                {
                    List<Color32[]> maskTextureDatas = null;
                    if (copyFromLastLOD)
                    {
                        int lastLOD = variations[v].lodCount - 1;
                        //必须保证贴图数量相同才拷贝上一级lod的数据到新的lod
                        if (lastLOD >= 0)
                        {
                            var lastLODMaskTextureSetting = mLODMaterialSettings[lastLOD].maskTextureSetting;
                            if (lastLODMaskTextureSetting.Count == maskTextureSetting.Count)
                            {
                                maskTextureDatas = new List<Color32[]>();
                                for (int k = 0; k < maskTextureSetting.Count; ++k)
                                {
                                    var lastLODMaskTexture = variations[v].GetLOD(lastLOD).maskTextures[k];
                                    //rescale texture
                                    if (lastLODMaskTextureSetting[k].resolution != maskTextureSetting[k].resolution)
                                    {
                                        var newTexture = Utils.ResizeTexture(lastLODMaskTexture.texture, maskTextureSetting[k].resolution, maskTextureSetting[k].resolution);

                                        maskTextureDatas.Add(newTexture.GetPixels32());
                                        Object.DestroyImmediate(newTexture);
                                    }
                                    else
                                    {
                                        var textureData = lastLODMaskTexture.textureData;
                                        maskTextureDatas.Add(Utils.ColorToColor32Array(textureData));
                                    }
                                }
                            }
                        }

                    }
                    var tileLOD = CreateGroundTileLODFromTextureData(i, mLODMaterialSettings.Count - 1, variations[v].name, maskTextureDatas);
                    variations[v].AddLOD(tileLOD);
                }
            }

            SetCurrentLOD(mLODMaterialSettings.Count - 1);

            ActionManager.instance.Clear();
        }

        public void RemoveLOD()
        {
            for (int i = 0; i < mTiles.Length; ++i)
            {
                mTiles[i].RemoveLOD();
            }

            mLODMaterialSettings.RemoveAt(mLODMaterialSettings.Count - 1);
            SetCurrentLOD(mLODMaterialSettings.Count - 1);

            ActionManager.instance.Clear();
        }

        //设置所有使用了这个tileindex的格子的variation
        public void SetVariation(int tileIndex, int variationIndex)
        {
            int v = verticalInstanceCount;
            int h = horizontalInstanceCount;
            for (int y = 0; y < v; ++y)
            {
                for (int x = 0; x < h; ++x)
                {
                    if (mInstances[x, y] != null && mInstances[x, y].tileIndex == tileIndex)
                    {
                        SetVariation(x, y, variationIndex);
                    }
                }
            }
        }

        //只设置某格的variation
        public void SetVariation(int tileX, int tileY, int variationIndex)
        {
            var instance = GetInstance(tileX, tileY);
            if (instance != null)
            {
                var tile = mTiles[instance.tileIndex];
                tile.SetCurrentVariation(variationIndex);
                Debug.Assert(tile.currentVariationIndex >= 0 && tile.currentVariationIndex < tile.variations.Count);
                ClearInstance(tileX, tileY, false);
                CreateInstance(tileX, tileY, instance.tileIndex, tile.writable);
            }
        }

        public void RemoveVariationByName(int tileIndex, string name)
        {
            var variations = mTiles[tileIndex].variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].name == name)
                {
                    RemoveVariation(tileIndex, i);
                    break;
                }
            }
        }

        public void RemoveVariationByID(int tileIndex, int instanceID)
        {
            var variations = mTiles[tileIndex].variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].instanceID == instanceID)
                {
                    RemoveVariation(tileIndex, i);
                    break;
                }
            }
        }

        public void RemoveVariation(int tileIndex, int variationIndex)
        {
            var variations = mTiles[tileIndex].variations;
            int n = variations.Count;
            if (n > 1)
            {
                if (variationIndex > 0)
                {
                    mTiles[tileIndex].RemoveVariation(variationIndex);

                    int h = horizontalInstanceCount;
                    int v = verticalInstanceCount;
                    for (int i = 0; i < v; ++i)
                    {
                        for (int j = 0; j < h; ++j)
                        {
                            if (mInstances[j, i] != null && mInstances[j, i].tileIndex == tileIndex)
                            {
                                ClearInstance(j, i, true);
                                SetVariation(j, i, mTiles[tileIndex].currentVariationIndex);
                            }
                        }
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Can't remove base tile", "OK");
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "There must be at least 1 variation", "OK");
            }
        }

        public int GetCurrentVariationIndex(int tileX, int tileY)
        {
            var instance = mInstances[tileX, tileY];
            if (instance == null)
            {
                return -1;
            }

            int tileIndex = instance.tileIndex;
            return mTiles[tileIndex].currentVariationIndex;
        }

        public void AddVariation(int tileIndex, string name, int instanceID, List<List<Color32[]>> maskTextureDatas)
        {
            DoAddVariation(tileIndex, name, instanceID, maskTextureDatas);
            int variationIndex = mTiles[tileIndex].variations.Count - 1;
            int h = horizontalInstanceCount;
            int v = verticalInstanceCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    if (mInstances[j, i] != null && mInstances[j, i].tileIndex == tileIndex)
                    {
                        SetVariation(j, i, variationIndex);
                    }
                }
            }
        }

        void DoAddVariation(int tileIndex, string name, int instanceID, List<List<Color32[]>> maskTextureDatas)
        {
            var tile = mTiles[tileIndex];
            //check if name is valid
            Debug.Assert(name != null);
            var variations = tile.variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].name == name)
                {
                    Debug.Assert(false, $"name {name} exists");
                    return;
                }
            }

            var variation = new GroundTileVariation(name, instanceID);
            int lodCount = mLODMaterialSettings.Count;
            for (int lod = 0; lod < lodCount; ++lod)
            {
                var tileLOD = CreateGroundTileLODFromTextureData(tileIndex, lod, name, maskTextureDatas == null ? null : maskTextureDatas[lod]);
                variation.AddLOD(tileLOD);
            }
            tile.variations.Add(variation);
        }

        GroundTileLOD CreateGroundTileLODFromTextureData(int tileIndex, int lod, string name, List<Color32[]> maskTextureDatas)
        {
            //create game object
            var gameObject = CreateGameObject(GetTileName(tileIndex, lod, name), lod);
            //create mask textures
            List<MaskTexture> textures = new List<MaskTexture>();
            var maskTextureSetting = mLODMaterialSettings[lod].maskTextureSetting;
            if (maskTextureDatas == null)
            {
                maskTextureDatas = new List<Color32[]>();
                for (int t = 0; t < maskTextureSetting.Count; ++t)
                {
                    int resolution = maskTextureSetting[t].resolution;
                    var pixels = new Color32[resolution * resolution];
                    if (maskTextureSetting[t].initChannelData)
                    {
                        for (int y = 0; y < resolution; ++y)
                        {
                            for (int x = 0; x < resolution; ++x)
                            {
                                int idx = y * resolution + x;
                                pixels[idx].r = 255;
                            }
                        }
                    }
                    maskTextureDatas.Add(pixels);
                }
            }

            textures = new List<MaskTexture>(maskTextureSetting.Count);
            for (int t = 0; t < maskTextureSetting.Count; ++t)
            {
                var maskTexture = new MaskTexture();
                textures.Add(maskTexture);
                int resolution = maskTextureSetting[t].resolution;
                maskTexture.shaderPropertyName = maskTextureSetting[t].shaderPropertyName;

                maskTexture.texture = new Texture2D(resolution, resolution, TextureFormat.RGBA32, false);
                maskTexture.texture.wrapMode = TextureWrapMode.Clamp;

                var colorArray = Utils.Color32ToColorArray(maskTextureDatas[t]);
                maskTexture.textureData = (Color[])colorArray.Clone();
                maskTexture.beforePaintingTextureData = (Color[])colorArray.Clone();
                maskTexture.texture.SetPixels32(maskTextureDatas[t]);
                maskTexture.texture.Apply();

                string propName = maskTextureSetting[t].shaderPropertyName;
                string texturePath = mEditorAssetOutputFolder + "/" + gameObject.name + "_" + propName + ".tga";
                byte[] bytes = maskTexture.texture.EncodeToTGA();
                System.IO.File.WriteAllBytes(texturePath, bytes);
                AssetDatabase.Refresh();
                var newTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                Object.DestroyImmediate(maskTexture.texture);
                maskTexture.texture = newTexture;
            }

            var tileLOD = new GroundTileLOD(gameObject, textures);
            return tileLOD;
        }

        GroundTileLOD CreateGroundTileLOD(int tileIndex, int lod, string name, List<MaskTexture> maskTextures)
        {
            //create game object
            var gameObject = CreateGameObject(GetTileName(tileIndex, lod, name), lod);
            var tileLOD = new GroundTileLOD(gameObject, maskTextures);
            return tileLOD;
        }

        void CreateTiles(int totalTileCount)
        {
            FileUtil.DeleteFileOrDirectory(mEditorAssetOutputFolder);
            AssetDatabase.Refresh();
            Directory.CreateDirectory(mEditorAssetOutputFolder);

            mInstances = new GroundTileInstance[10, 10];
            mTiles = new GroundTileTemplate[totalTileCount];
            for (int i = 0; i < mTiles.Length; ++i)
            {
                mTiles[i] = new GroundTileTemplate();
                AddVariation(i, "", ++mNextVariationInstanceID, null);
            }

            AssignEdgeID();

            if (totalTileCount != 16)
            {
                SetPaintMode(PaintMode.AllTiles);
            }
            else
            {
                SetPaintMode(PaintMode.Paint1_2_4_8_3_5_10_12_15);
            }
        }

        public void SetPaintMode(PaintMode mode)
        {
            ClearInstances();
            mPaintMode = mode;
            switch (mPaintMode)
            {
                case PaintMode.AllTiles:
                    {
                        int countInOneRow = Mathf.CeilToInt(Mathf.Sqrt(mTiles.Length));
                        int rowCount = Mathf.CeilToInt(mTiles.Length / (float)countInOneRow);
                        int tileIndex = 0;
                        for (int i = 0; i < rowCount; ++i)
                        {
                            for (int j = 0; j < countInOneRow; ++j)
                            {
                                CreateInstance(j, i, tileIndex++, true);
                                if (tileIndex >= mTiles.Length)
                                {
                                    break;
                                }
                            }
                        }
                        break;
                    }
                case PaintMode.Paint1_2_4_8_3_5_10_12_15:
                    CreateInstance(1, 1, 1, true);
                    CreateInstance(2, 1, 3, true);
                    CreateInstance(3, 1, 2, true);
                    CreateInstance(1, 2, 5, true);
                    CreateInstance(2, 2, 15, true);
                    CreateInstance(3, 2, 10, true);
                    CreateInstance(1, 3, 4, true);
                    CreateInstance(2, 3, 12, true);
                    CreateInstance(3, 3, 8, true);
                    break;
                case PaintMode.Paint6:
                    CreateInstance(1, 1, 1, false);
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(3, 1, 2, false);
                    CreateInstance(1, 2, 5, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(3, 2, 10, false);
                    CreateInstance(2, 3, 12, false);
                    CreateInstance(3, 3, 8, false);

                    CreateInstance(0, 4, 4, false);
                    CreateInstance(1, 4, 8, false);
                    CreateInstance(0, 3, 1, false);
                    CreateInstance(1, 3, 6, true);
                    break;
                case PaintMode.Paint9:
                    CreateInstance(1, 1, 1, false);
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(3, 1, 2, false);
                    CreateInstance(1, 2, 5, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(3, 2, 10, false);
                    CreateInstance(1, 3, 4, false);
                    CreateInstance(2, 3, 12, false);

                    CreateInstance(3, 3, 9, true);
                    CreateInstance(3, 4, 4, false);
                    CreateInstance(4, 4, 8, false);
                    CreateInstance(4, 3, 2, false);
                    break;

                case PaintMode.Paint7:
                    CreateInstance(1, 1, 1, false);
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(3, 1, 2, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(3, 2, 10, false);
                    CreateInstance(2, 3, 12, false);
                    CreateInstance(3, 3, 8, false);

                    CreateInstance(1, 2, 7, true);
                    CreateInstance(0, 2, 1, false);
                    CreateInstance(0, 3, 4, false);
                    CreateInstance(1, 3, 12, false);
                    break;

                case PaintMode.Paint13:
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(3, 1, 2, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(3, 2, 10, false);
                    CreateInstance(1, 3, 4, false);
                    CreateInstance(2, 3, 12, false);
                    CreateInstance(3, 3, 8, false);

                    CreateInstance(1, 2, 13, true);
                    CreateInstance(0, 1, 1, false);
                    CreateInstance(0, 2, 4, false);
                    CreateInstance(1, 1, 3, false);
                    break;

                case PaintMode.Paint11:
                    CreateInstance(1, 1, 1, false);
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(3, 1, 2, false);
                    CreateInstance(1, 2, 5, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(1, 3, 4, false);
                    CreateInstance(2, 3, 12, false);

                    CreateInstance(3, 2, 11, true);
                    CreateInstance(4, 2, 2, false);
                    CreateInstance(4, 3, 8, false);
                    CreateInstance(3, 3, 12, false);
                    break;

                case PaintMode.Paint14:
                    CreateInstance(1, 1, 1, false);
                    CreateInstance(2, 1, 3, false);
                    CreateInstance(1, 2, 5, false);
                    CreateInstance(2, 2, 15, false);
                    CreateInstance(1, 3, 4, false);
                    CreateInstance(2, 3, 12, false);
                    CreateInstance(3, 3, 8, false);

                    CreateInstance(3, 2, 14, true);
                    CreateInstance(4, 1, 2, false);
                    CreateInstance(4, 2, 8, false);
                    CreateInstance(3, 1, 3, false);
                    break;
                case PaintMode.Paint_1_2_4_8:
                    CreateInstance(1, 1, 1, true);
                    CreateInstance(2, 1, 2, true);
                    CreateInstance(1, 2, 4, true);
                    CreateInstance(2, 2, 8, true);
                    break;
                case PaintMode.Paint_1_2_4_8_12_3:
                    CreateInstance(1, 1, 1, true);
                    CreateInstance(2, 1, 3, true);
                    CreateInstance(3, 1, 2, true);
                    CreateInstance(1, 2, 4, true);
                    CreateInstance(2, 2, 12, true);
                    CreateInstance(3, 2, 8, true);
                    break;

                case PaintMode.Paint_1_2_4_8_5_10:
                    CreateInstance(1, 1, 1, true);
                    CreateInstance(1, 2, 5, true);
                    CreateInstance(1, 3, 4, true);
                    CreateInstance(2, 1, 2, true);
                    CreateInstance(2, 2, 10, true);
                    CreateInstance(2, 3, 8, true);
                    break;
                case PaintMode.Paint_1_2_6_8:
                    CreateInstance(0, 3, 1, true);
                    CreateInstance(0, 4, 4, true);
                    CreateInstance(1, 3, 6, true);
                    CreateInstance(1, 4, 8, true);
                    CreateInstance(1, 2, 1, true);
                    CreateInstance(2, 2, 2, true);
                    CreateInstance(2, 3, 8, true);
                    break;
                case PaintMode.Paint_9_2_4_8:
                    CreateInstance(4, 4, 8, true);
                    CreateInstance(3, 4, 4, true);
                    CreateInstance(4, 3, 2, true);
                    CreateInstance(3, 3, 9, true);
                    CreateInstance(3, 2, 2, true);
                    CreateInstance(2, 3, 4, true);
                    CreateInstance(2, 2, 1, true);
                    break;
                case PaintMode.Paint_14_9_7:
                    CreateInstance(1, 1, 7, true);
                    CreateInstance(0, 1, 9, true);
                    CreateInstance(0, 0, 14, true);
                    CreateInstance(1, 0, 9, true);
                    break;
                case PaintMode.Paint_13_11_6:
                    CreateInstance(1, 0, 13, true);
                    CreateInstance(0, 0, 6, true);
                    CreateInstance(1, 1, 6, true);
                    CreateInstance(0, 1, 11, true);
                    break;
                case PaintMode.Paint_11_9_6:
                    CreateInstance(0, 0, 6, true);
                    CreateInstance(0, 1, 11, true);
                    CreateInstance(1, 1, 6, true);
                    CreateInstance(1, 0, 9, true);
                    break;
                case PaintMode.Paint_11_7_14_13:
                    CreateInstance(0, 0, 14, true);
                    CreateInstance(1, 0, 13, true);
                    CreateInstance(0, 1, 11, true);
                    CreateInstance(1, 1, 7, true);
                    break;
                case PaintMode.Paint_10_5:
                    CreateInstance(0, 0, 10, true);
                    CreateInstance(1, 0, 5, true);
                    break;
                case PaintMode.Paint_10_1:
                    CreateInstance(0, 0, 10, true);
                    CreateInstance(1, 0, 1, true);
                    break;
                case PaintMode.Paint_10_4:
                    CreateInstance(0, 0, 10, true);
                    CreateInstance(1, 0, 4, true);
                    break;
                default:
                    Debug.Assert(false, "todo");
                    break;
            }
        }

        void ClearInstances()
        {
            int h = horizontalInstanceCount;
            int v = verticalInstanceCount;
            for (int i = 0; i < v; ++i)
            {
                for (int j = 0; j < h; ++j)
                {
                    if (mInstances[j, i] != null)
                    {
                        mInstances[j, i].OnDestroy();
                        mInstances[j, i] = null;
                    }
                }
            }
        }

        void ClearInstance(int tileX, int tileY, bool onlyDeleteGameObject)
        {
            var instance = mInstances[tileX, tileY];
            if (instance != null)
            {
                instance.OnDestroy();
                if (!onlyDeleteGameObject)
                {
                    mInstances[tileX, tileY] = null;
                }
            }
        }

        void CreateInstance(int gridX, int gridY, int tileIndex, bool writable)
        {
            Debug.Assert(mInstances[gridX, gridY] == null);
            var pos = new Vector3(gridX * mTileSize, 0, gridY * mTileSize);
            var obj = new GroundTileInstance(pos, tileIndex, mTiles[tileIndex].edgeIDs, mTileSize);
            int variationIndex = mTiles[tileIndex].currentVariationIndex;
            GroundTileVariation variation = mTiles[tileIndex].GetVariation(variationIndex);
            GroundTileLOD tileLOD = variation.GetLOD(mCurrentLOD);
            obj.gameObject = GameObject.Instantiate<GameObject>(tileLOD.gameObject);
            obj.gameObject.name = tileLOD.gameObject.name;
            obj.gameObject.SetActive(true);
            obj.gameObject.transform.position = pos;
            obj.gameObject.AddComponent<DisableKeyboardDelete>();
            //Utils.HideGameObject(obj.gameObject);
            SetMaskTexture(obj.gameObject, tileLOD.maskTextures);
            mInstances[gridX, gridY] = obj;
            mTiles[tileIndex].writable = writable;

            obj.tileIndexText.SetActive(mShowTileText);
            var edgeTexts = obj.edgeTexts;
            for (int i = 0; i < edgeTexts.Length; ++i)
            {
                if (edgeTexts[i] != null)
                {
                    edgeTexts[i].SetActive(mShowEdgeText);
                }
            }
        }

        GameObject CreateGameObject(string name, int lod)
        {
            var obj = new GameObject(name);
            obj.SetActive(false);
            obj.AddComponent<DisableKeyboardDelete>();
            Utils.HideGameObject(obj);
            var renderer = obj.AddComponent<MeshRenderer>();
            var filter = obj.AddComponent<MeshFilter>();
            string materialPath = mLODMaterialSettings[lod].materialPath;
            var mtl = MapModuleResourceMgr.LoadMaterial(materialPath);
            renderer.sharedMaterial = Object.Instantiate<Material>(mtl);
            filter.sharedMesh = GetMesh(name);
            return obj;
        }

        Mesh GetMesh(string name)
        {
            var mesh = new Mesh();
            mesh.name = name;
            mesh.vertices = new Vector3[]
            {
                    new Vector3(0, 0, 0),
                    new Vector3(0, 0, mTileSize),
                    new Vector3(mTileSize, 0, mTileSize),
                    new Vector3(mTileSize, 0, 0),
            };
            mesh.uv = new Vector2[]
            {
                    new Vector2(0, 0),
                    new Vector2(0, 1),
                    new Vector2(1, 1),
                    new Vector2(1, 0),
            };

            var indices = new int[]
            {
                    0, 1, 2,
                    0, 2, 3,
            };


            if (mExtraSubmeshMaterial != null)
            {
                mesh.subMeshCount = 2;
                mesh.SetTriangles(indices, 0);
                mesh.SetTriangles(indices, 1);
            }
            else
            {
                mesh.triangles = indices;
            }

            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        void SetMaskTexture(GameObject instance, List<MaskTexture> maskTextures)
        {
            for (int i = 0; i < maskTextures.Count; ++i)
            {
                instance.GetComponent<MeshRenderer>().sharedMaterial.SetTexture(maskTextures[i].shaderPropertyName, maskTextures[i].texture);
            }
        }

        void CreateGrid(int tileCount)
        {
            int countInOneRow = Mathf.CeilToInt(Mathf.Sqrt(tileCount));
            int rowCount = Mathf.CeilToInt(tileCount / (float)countInOneRow);
            var gridCreator = new MapLayerXZRectangleGridCreator(countInOneRow * mTileSize, rowCount * mTileSize, mTileSize, mTileSize);
            mGridObject = gridCreator.CreateGrid(Color.yellow);
            mGridObject.SetActive(true);
            Utils.HideGameObject(mGridObject);
            mGridObject.transform.position = new Vector3(0, 0.3f, 0);
        }

        void OnDrawGizmos()
        {
            DrawEdges();
            DrawPaintAreaGuideline();
        }

        public void CreateTextureAsset(string propertyName, string gameObjectName, MaskTexture maskTexture)
        {
            string texturePath = mEditorAssetOutputFolder + "/" + gameObjectName + "_" + propertyName + ".tga";
            byte[] bytes = maskTexture.texture.EncodeToTGA();
            System.IO.File.WriteAllBytes(texturePath, bytes);
            AssetDatabase.Refresh();
            var newTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
            Object.DestroyImmediate(maskTexture.texture);
            maskTexture.texture = newTexture;
        }

        public int GetTileIndex(int x, int y)
        {
            if (x >= 0 && x < horizontalInstanceCount && y >= 0 && y < verticalInstanceCount)
            {
                if (mInstances[x, y] != null)
                {
                    return mInstances[x, y].tileIndex;
                }
            }
            return -1;
        }

        public GroundTileInstance GetInstance(int x, int y)
        {
            if (x >= 0 && x < horizontalInstanceCount && y >= 0 && y < verticalInstanceCount)
            {
                return mInstances[x, y];
            }
            return null;
        }

        public GroundTileTemplate GetTile(int tileIndex)
        {
            if (tileIndex >= 0 && tileIndex < mTiles.Length)
            {
                return mTiles[tileIndex];
            }
            return null;
        }

        public void SetOperation(Operation operation)
        {
            mOperation = operation;
            if (mOperation == Operation.PaintEdge)
            {
                int textureResolution = mLODMaterialSettings[mCurrentLOD].maskTextureSetting[mMaskTextureIndex].resolution;
                mBrushSize = Mathf.Min(mBrushSize, textureResolution / 2);
            }
        }

        public float GetBrushSizeInWorldUnits()
        {
            if (mTiles != null)
            {
                int textureResolution = mLODMaterialSettings[mCurrentLOD].maskTextureSetting[mMaskTextureIndex].resolution;
                return ((float)mBrushSize / textureResolution) * mTileSize;
            }
            return 0;
        }

        public void CreateCamera()
        {
            Debug.Assert(mCamera == null);
            mCamera = new GameObject("Camera");
            var newCamera = mCamera.AddComponent<Camera>();
            newCamera.fieldOfView = 30;
            newCamera.clearFlags = CameraClearFlags.SolidColor;
            mCamera.transform.rotation = Quaternion.Euler(45, 0, 0);
        }

        public string GetTileName(int tileIndex, int lod, string name)
        {
            if (name.Length == 0)
            {
                return $"{mTileSetName}_{tileIndex.ToString("D2")}_lod{lod.ToString()}";
            }
            return $"{mTileSetName}_{tileIndex.ToString("D2")}_{name}_lod{lod.ToString()}";
        }

        public int GetVariationIndex(int tileIndex, int variationInstanceID)
        {
            var variations = mTiles[tileIndex].variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].instanceID == variationInstanceID)
                {
                    return i;
                }
            }
            return -1;
        }

        public int GetVariationInstanceID(int tileIndex, int variationIndex)
        {
            return mTiles[tileIndex].variations[variationIndex].instanceID;
        }

        public GroundTileMaker.GroundTileVariation GetVariation(int tileIndex, int variationInstanceID)
        {
            var variations = mTiles[tileIndex].variations;
            for (int i = 0; i < variations.Count; ++i)
            {
                if (variations[i].instanceID == variationInstanceID)
                {
                    return variations[i];
                }
            }
            return null;
        }

        public List<MaskTextureSetting> GetMaskTextureSetting(int lod)
        {
            return mLODMaterialSettings[lod].maskTextureSetting;
        }

        public string GetMaterialPath(int lod)
        {
            return mLODMaterialSettings[lod].materialPath;
        }

        public void ChangeMaterial(int lod, Material material)
        {
            string newMaterialPath = AssetDatabase.GetAssetPath(material);
            if (string.IsNullOrEmpty(newMaterialPath))
            {
                return;
            }
            mLODMaterialSettings[lod].materialPath = newMaterialPath;
            for (int i = 0; i < mTiles.Length; ++i)
            {
                mTiles[i].ChangeMaterial(lod, material);
            }

            ClearInstances();
            SetPaintMode(mPaintMode);
        }

        public void SetTileVariationLODDirty(int tileIndex, int lod, int variationInstanceID)
        {
            var variation = GetVariation(tileIndex, variationInstanceID);
            variation.GetLOD(lod).assetsDirty = true;
        }
        
        public void ReloadEditorTextureData(string textureName)
        {
            if (mTiles != null)
            {
                for (int i = 0; i < mTiles.Length; ++i)
                {
                    if (mTiles[i] != null)
                    {
                        mTiles[i].ReloadTextureData(textureName);
                    }
                }
            }
        }

        public static void ResetScene()
        {
            var scene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            scene.name = "Ground Tile Maker";
            var gameObject = new GameObject(mMakerName);
            gameObject.AddComponent<GroundTileMaker>();
        }

        public static void ClearSceneGameObjects()
        {
            var scene = EditorSceneManager.GetActiveScene();
            var rootObjects = scene.GetRootGameObjects();
            if (rootObjects != null)
            {
                for (int i = 0; i < rootObjects.Length; ++i)
                {
                    if (rootObjects[i].name != mMakerName)
                    {
                        GameObject.DestroyImmediate(rootObjects[i]);
                    }
                }
            }
        }

        public bool showGrid { set { mGridObject?.SetActive(value); } get { return mGridObject != null ? mGridObject.activeSelf : false; } }
        public float strength
        {
            get { return mStrength; }
            set
            {
                mStrength = Mathf.Clamp(value, 0.001f, 1.0f);
            }
        }
        public float strengthStep { get { return mStrengthStep; } set { mStrengthStep = value; } }
        public int brushSizeStep { get { return mBrushSizeStep; } set { mBrushSizeStep = value; } }
        public int brushSize
        {
            get { return mBrushSize; }
            set
            {
                mBrushSize = Mathf.Max(1, value);
                if (mOperation == Operation.PaintEdge)
                {
                    int textureResolution = mLODMaterialSettings[mCurrentLOD].maskTextureSetting[mMaskTextureIndex].resolution;
                    mBrushSize = Mathf.Min(mBrushSize, textureResolution / 2);
                }
            }
        }
        public int channel { get { return mChannel; } set { mChannel = value; } }
        public int maskTextureIndex { get { return mMaskTextureIndex; } set { mMaskTextureIndex = value; } }
        public BrushManager brushManager { get { return mBrushManager; } }
        public string runtimeAssetOutputFolder { get { return mRuntimeAssetOutputFolder; } }
        public string editorAssetOutputFolder { get { return mEditorAssetOutputFolder; } }
        public string tileSetName { get { return mTileSetName; } set { mTileSetName = value; } }
        public float tileSize { get { return mTileSize; } }
        public float edgeSize { get { return mEdgeWidth; } set { mEdgeWidth = value; } }
        public int horizontalInstanceCount { get { return mInstances.GetLength(1); } }
        public int verticalInstanceCount { get { return mInstances.GetLength(0); } }
        public Operation operation { get { return mOperation; } }
        public bool showEdge { get { return mShowEdge; } set { mShowEdge = value; } }
        public bool showEdgeText
        {
            get { return mShowEdgeText; }
            set
            {
                mShowEdgeText = value;
                int h = horizontalInstanceCount;
                int v = verticalInstanceCount;
                for (int i = 0; i < v; ++i)
                {
                    for (int j = 0; j < h; ++j)
                    {
                        if (mInstances[j, i] != null)
                        {
                            var edgeTexts = mInstances[j, i].edgeTexts;
                            for (int k = 0; k < edgeTexts.Length; ++k)
                            {
                                if (edgeTexts[k] != null)
                                {
                                    edgeTexts[k].SetActive(value);
                                }
                            }
                        }
                    }
                }
            }
        }
        public bool showTileText
        {
            get { return mShowTileText; }
            set
            {
                mShowTileText = value;
                int h = horizontalInstanceCount;
                int v = verticalInstanceCount;
                for (int i = 0; i < v; ++i)
                {
                    for (int j = 0; j < h; ++j)
                    {
                        if (mInstances[j, i] != null)
                        {
                            mInstances[j, i].tileIndexText.SetActive(value);
                        }
                    }
                }
            }
        }
        public int tileCount { get { return mTiles.Length; } }
        public bool shareMesh { get { return mShareMesh; } set { mShareMesh = value; } }
        public bool packTextures { get { return mPackTextures; } set { mPackTextures = value; } }
        public Shader atlasShader { get { return mAtlasShader; } set { mAtlasShader = value; } }
        public Material extraSubmeshMaterial { get { return mExtraSubmeshMaterial; } set { mExtraSubmeshMaterial = value; } }
        public bool useExtraSubmeshMaterialAsFirstMaterial { get { return mUseExtraSubmeshMaterialAsFirstMaterial; } set { mUseExtraSubmeshMaterialAsFirstMaterial = value; } }
        public int lodCount { get { return mLODMaterialSettings.Count; } }
        public bool showGuideline { get { return mShowPaintAreaGuideline; } set { mShowPaintAreaGuideline = value; } }
        public PaintMode paintMode { get { return mPaintMode; } }
        public bool inited { get { return mTiles != null; } }
        public bool useRandomBrushRotation { get { return mRandomBrushRotation; } set { mRandomBrushRotation = value; } }
        public bool useRotatedBrush { get { return useRandomBrushRotation || mFixedBrushRotationAngle != 0; } }
        public float fixedBrushRotation { get { return mFixedBrushRotationAngle; } set { mFixedBrushRotationAngle = value; } }
        public ArrayPool<Color32> color32ArrayPool { get { return mColor32ArrayPool; } }
        public int GetNextInstanceID()
        {
            return ++mNextVariationInstanceID;
        }
        public int currentLOD { get { return mCurrentLOD; } }
        public void SetCurrentLOD(int lod)
        {
            if (mCurrentLOD != lod)
            {
                ClearInstances();
                mCurrentLOD = lod;
                //update brushSize
                brushSize = brushSize;
                SetPaintMode(mPaintMode);
            }
        }

        PaintMode mPaintMode;
        string mTileSetName;
        float mTileSize;
        string mRuntimeAssetOutputFolder;
        string mEditorAssetOutputFolder;
        int mChannel = 1;
        float mStrength = 0.1f;
        //以像素为单位,可以精确控制
        int mBrushSize = 100;
        int mBrushSizeStep = 5;
        //brush rotation
        float mFixedBrushRotationAngle = 0;
        //whether use random brush rotation
        bool mRandomBrushRotation = true;
        float mStrengthStep = 0.01f;
        float mEdgeWidth = 10.0f;
        int mMaskTextureIndex;
        bool mShowEdge = false;
        bool mShowEdgeText = false;
        bool mShowTileText = false;
        bool mShowPaintAreaGuideline = true;
        //templates
        GroundTileTemplate[] mTiles;
        //grids
        GroundTileInstance[,] mInstances;
        BrushManager mBrushManager;
        List<GroundLODMaterialSetting> mLODMaterialSettings = new List<GroundLODMaterialSetting>();
        int mCurrentLOD = 0;
        GameObject mGridObject;
        Operation mOperation = Operation.PaintTexture;
        static GroundTileMaker mInstance;
        ActionManager mActionManager;
        ArrayPool<Color32> mColor32ArrayPool;
        GameObject mCamera;
        Shader mAtlasShader;
        bool mShareMesh = false;
        bool mPackTextures = false;
        bool mUseExtraSubmeshMaterialAsFirstMaterial = true;
        //可以创建2个sub mesh,每个submesh使用不同的材质,例如fog的地面阴影材质
        Material mExtraSubmeshMaterial;

        const string mMakerName = "GroundTileMaker";

        static int mNextVariationInstanceID = 0;
    }
}
#endif