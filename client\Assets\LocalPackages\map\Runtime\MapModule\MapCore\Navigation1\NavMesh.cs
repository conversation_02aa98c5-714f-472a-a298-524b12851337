﻿ 



 
 


using System.Collections.Generic;
using UnityEngine;
using TFW.Map.Geo;

namespace TFW.Map.Nav
{
    public static class Const
    {
        // 占位边
        public static int StubIndex = -1;

        // 起始边
        public static Vector2Int StartEdgeKey = IDBuilder.GenEdgeKey(int.MaxValue, int.MaxValue);

        // 结束边
        public static Vector2Int EndEdgeKey = IDBuilder.GenEdgeKey(int.MaxValue, int.MaxValue - 1);

        // 起始边序号
        public static int StartEdgeID = 0;

        // 结束边序号
        public static int EndEdgeID = 1;

        // 合成的凸多边形最大顶点数
        public const int MaxConvexVertexCount = 100;
        public const int LargerNumber = int.MaxValue;
    };

    public class NavMeshRegion
    {
        public NavMeshRegion(TriangleQuadTree tree, int regionID)
        {
            this.quadTree = tree;
            this.regionID = regionID;
        }

        public void OnDestroy()
        {
            quadTree.OnDestroy();
            quadTree = null;
        }

        public TriangleQuadTree quadTree;
        public int regionID;
    }

    // 导航网格数据结构
    public class Mesh
    {
        SearchGrid mPolygonGrid; // 存储凸多边形的网格矩阵
        IDBuilder mIDBuilder; // 获取递增唯一ID
        Edge[] mAdjacentEdges; // 只保存邻接边,前两个位置储存起始点和终点虚拟边
        Triangle[] mTriangles; // 三角形顺序索引位置
        Vertice[] mAllVertices; // 所有的顶点,保证只有一份
        public Nav.TriangleTypeSetting[] triangleSettings;
        public NavMeshRegion[] navMeshRegions;

        public static Vertice[] currentMeshVertices;

        public Mesh(int minX, int maxX, int width, int height, int searchGridSize)
        {
            mPolygonGrid = new SearchGrid(minX, maxX, width, height, searchGridSize);
        }

        public void OnDestroy()
        {
            if (navMeshRegions != null)
            {
                for (int i = 0; i < navMeshRegions.Length; ++i)
                {
                    if (navMeshRegions[i] != null)
                    {
                        navMeshRegions[i].OnDestroy();
                    }
                }

                navMeshRegions = null;
            }
        }

        public void Clear()
        {
            mIDBuilder = null;
        }

        public void ShowNavMeshRegions(bool show)
        {
#if UNITY_EDITOR
            if (Map.currentMap.isEditorMode)
            {
                for (int i = 0; i < navMeshRegions.Length; ++i)
                {
                    navMeshRegions[i].quadTree.visible = show;
                }
            }
#endif
        }

        // 返回三角形边的序号列表 兼容凸多边形
        public int[] GenEdgeIDs(Polygon t)
        {
            var vecs = t.GetVertices();
            var edgeKeys = new int[vecs.Count];
            for (int i = 0; i < vecs.Count; i++)
            {
                edgeKeys[i] = GenEdgeID(vecs[i].Index, vecs[(i + 1) % vecs.Count].Index);
            }

            return edgeKeys;
        }

        // 返回三角形边的key列表 兼容凸多边形
        Vector2Int[] GenEdgeKeys(Triangle t)
        {
            var edgeKeys = new Vector2Int[3];
            edgeKeys[0] = IDBuilder.GenEdgeKey(t.v0, t.v1);
            edgeKeys[1] = IDBuilder.GenEdgeKey(t.v1, t.v2);
            edgeKeys[2] = IDBuilder.GenEdgeKey(t.v2, t.v0);
            return edgeKeys;
        }

        // 生成边的序号
        int GenEdgeID(int i, int j)
        {
            var key = IDBuilder.GenEdgeKey(i, j);
            return mIDBuilder.GetIndexByKey(key);
        }

        // 初始化三角形数据结构
        public void Init(MeshCfg conf)
        {
            conf.GenTriangles(out mTriangles, out mAllVertices);
            currentMeshVertices = mAllVertices;

            triangleSettings = conf.DefaultTypeSettings;
            navMeshRegions = conf.NavMeshRegions;
            if (triangleSettings != null && triangleSettings.Length == 0)
            {
                triangleSettings = null;
            }

            int maxEdgeCount = mTriangles.Length * 3 + 2; //  边总数的最大值 三角形个数*3。2 用来存储两条虚拟的边

            mAdjacentEdges = new Edge[maxEdgeCount];
            //以下2条边是寻路的起点和终点的边
            mAdjacentEdges[0] = new Edge();
            mAdjacentEdges[1] = new Edge();
            var bufferEdges = new Edge[maxEdgeCount]; // 临时边切片
            var bufferIDBuilder = new IDBuilder(maxEdgeCount);
            // idbuilder 中加入两个值
            bufferIDBuilder.GetIndexByKey(Const.StartEdgeKey);
            bufferIDBuilder.GetIndexByKey(Const.EndEdgeKey);
            mIDBuilder = new IDBuilder(maxEdgeCount);
            mIDBuilder.GetIndexByKey(Const.StartEdgeKey);
            mIDBuilder.GetIndexByKey(Const.EndEdgeKey);

            Dictionary<int, Vector2Int[]> triangleEdgeKeys = new Dictionary<int, Vector2Int[]>(mTriangles.Length);

            int allEdgeCount = 0;
            int index = 0;
            foreach (var t in mTriangles)
            {
                //生成三条edge的key
                var edgeKeys = GenEdgeKeys(t);
                UnityEngine.Debug.Assert(edgeKeys.Length == 3);
                triangleEdgeKeys[t.Index] = edgeKeys;
                //t.SetEdgeKey(edgeKeys);

                //遍历边
                for (int i = 0; i < edgeKeys.Length; i++)
                {
                    var edgeIdx = bufferIDBuilder.GetIndexByKey(edgeKeys[i]);
                    var edge = bufferEdges[edgeIdx];
                    if (edge != null)
                    {
                        // 如果已经存储过当前边，则意味此为邻接边
                        // 为邻接边生成拐点数组
                        //edge.Inflect0.Coord = GeoUtils.TruncEdge(edge.Vertice0.Coord, edge.Vertice1.Coord);
                        //edge.Inflect1.Coord = GeoUtils.TruncEdge(edge.Vertice1.Coord, edge.Vertice0.Coord);
                        edge.Inflect0.Coord = GeoUtils.TruncEdge(edge.Vertice0.Coord, edge.Vertice1.Coord, 1000);
                        edge.Inflect1.Coord = GeoUtils.TruncEdge(edge.Vertice1.Coord, edge.Vertice0.Coord, 1000);
                        // 如果已储存过当前边 三角形为相邻三角形
                        edge.AddAdjacentTriangle(t);
                        if (edge.GetAdjacentTriangleCount() > 2)
                        {
                            UnityEngine.Debug.LogError("Should not have more than 2 triangle ");
                        }

                        // 确定为邻接边,获取新的序号
                        index = mIDBuilder.GetIndexByKey(edgeKeys[i]);

                        //只保存邻接边
                        mAdjacentEdges[index] = edge;
                    }
                    else
                    {
                        allEdgeCount++;

                        var newEdge = new Edge();
                        var j = (i + 1) % edgeKeys.Length;

                        newEdge.Vertice0 = mAllVertices[t.GetVertexIndex(i)];
                        newEdge.Vertice1 = mAllVertices[t.GetVertexIndex(j)];
                        // 边的权重点暂时默认为中点
                        newEdge.WtCoord = GeoUtils.CalMidCoord(newEdge.Vertice0.Coord, newEdge.Vertice1.Coord);
                        newEdge.AddAdjacentTriangle(t);
                        bufferEdges[edgeIdx] = newEdge; // 先存入临时切片中
                    }
                }
            }

            // 重新创建edges数组,减少内存浪费, +1因为个数为(index + 1)
            var edges = new Edge[index + 1];
            GeoUtils.Copy(edges, mAdjacentEdges);
            mAdjacentEdges = edges;

            // 存入格子结构, 将edgeKey换成唯一编号
            foreach (var t in mTriangles)
            {
                var edgeKeys = triangleEdgeKeys[t.Index];

                int e0 = mIDBuilder.GetIndexByKey(edgeKeys[0]);
                int e1 = mIDBuilder.GetIndexByKey(edgeKeys[1]);
                int e2 = mIDBuilder.GetIndexByKey(edgeKeys[2]);

                t.SetEdgeIDs(e0, e1, e2);
                mPolygonGrid.Insert(t);
            }
        }

        // 格子查询p落在了哪个多边形内
        public int GetPolygonIndexByCoord(Coord p, SearchGrid sg, TriangleTypeSetting[] typeSetting)
        {
            var tri = sg.RetrieveByCoord(p);
            if (tri.Count == 0)
            {
                return Const.StubIndex;
            }

            if (typeSetting != null && !typeSetting[tri[0].GetCustomType()].walkable)
            {
                return Const.StubIndex;
            }

            return tri[0].GetIndex();
        }

        // 根据序号获取三角形
        public Triangle GetTriangleByIndex(int id)
        {
            if (id < 0 || id > mTriangles.Length)
            {
                return null;
            }

            return mTriangles[id];
        }

        // 获取polygons所有的邻接边
        public List<Edge> GetEdges(List<Polygon> polygons)
        {
            List<Edge> edges = new List<Edge>();
            for (int i = 0; i < polygons.Count - 1; i++)
            {
                var curTri = polygons[i];

                var nextTri = polygons[i + 1];
                // 遍历两个三角形找到公共边
                foreach (var j in curTri.GetEdgeIDs())
                {
                    foreach (var k in nextTri.GetEdgeIDs())
                    {
                        if (j == k)
                        {
                            var edge = mAdjacentEdges[j];
                            edges.Add(edge);
                        }
                    }
                }
            }

            return edges;
        }

        //计算两个三角形重心的距离
        public double CalcDistance(int srcTriangleID, int dstTriangleID)
        {
            int i = srcTriangleID;
            int j = dstTriangleID;

            var x1 = (mAllVertices[mTriangles[i].v0].Coord.X + mAllVertices[mTriangles[i].v1].Coord.X +
                      mAllVertices[mTriangles[i].v2].Coord.X) / 3.0;
            var z1 = (mAllVertices[mTriangles[i].v0].Coord.Z + mAllVertices[mTriangles[i].v1].Coord.Z +
                      mAllVertices[mTriangles[i].v2].Coord.Z) / 3.0;
            var x2 = (mAllVertices[mTriangles[j].v0].Coord.X + mAllVertices[mTriangles[j].v1].Coord.X +
                      mAllVertices[mTriangles[j].v2].Coord.X) / 3.0;
            var z2 = (mAllVertices[mTriangles[j].v0].Coord.Z + mAllVertices[mTriangles[j].v1].Coord.Z +
                      mAllVertices[mTriangles[j].v2].Coord.Z) / 3.0;
            var weight = System.Math.Sqrt((double) ((x2 - x1) * (x2 - x1) + (z2 - z1) * (z2 - z1)));
            return weight;
        }

        // 生成虚拟边.起始和终点都是同一个点
        public void GenVirtualEdges(int edgeKey, Coord point)
        {
            var edge = new Edge();
            // 虚拟两个相同的顶点
            edge.Vertice0 = new Vertice(Const.StubIndex, point);
            edge.Vertice1 = new Vertice(Const.StubIndex, point);

            edge.Inflect0 = new Vertice(0, point);
            edge.Inflect1 = new Vertice(0, point);
            edge.WtCoord = point;

            mAdjacentEdges[edgeKey] = edge;
        }

        //生成每个三角形的邻接三角形的列表
        public List<int>[] GenAdjacentTriangleMatrix()
        {
            //生成每个三角形的邻接三角形列表
            int n = mTriangles.Length;
            var adjacentMat = new List<int>[n];
            for (int i = 0; i < n; ++i)
            {
                //一个三角形最多与三个三角形相邻
                adjacentMat[i] = new List<int>(3);
            }

            int edgeCount = mAdjacentEdges.Length;
            for (int i = 2; i < edgeCount; ++i)
            {
                var edge = mAdjacentEdges[i];
                var triangle0 = mTriangles[edge.AdjacentTriangle0];
                var triangle1 = mTriangles[edge.AdjacentTriangle1];
#if UNITY_EDITOR
                UnityEngine.Debug.Assert(edge != null && edge.GetAdjacentTriangleCount() == 2);
#endif
                adjacentMat[triangle0.Index].Add(triangle1.Index);
                adjacentMat[triangle1.Index].Add(triangle0.Index);

#if UNITY_EDITOR
                UnityEngine.Debug.Assert(adjacentMat[triangle0.Index].Count <= 3);
                UnityEngine.Debug.Assert(adjacentMat[triangle1.Index].Count <= 3);
#endif
            }

            return adjacentMat;
        }

        public void CreateTriangleSearchGrid()
        {
            var adjacentMat = GenAdjacentTriangleMatrix();

            var convexMap = new Dictionary<int, Triangle>();
            foreach (var convex in mTriangles)
            {
                convexMap[convex.Index] = convex;
            }

            mPolygonGrid.NewGrids();
            // 插入查找矩阵
            foreach (var convex in convexMap)
            {
                mPolygonGrid.Insert(convex.Value);
            }
        }

        // 判断序号为index的边是否为邻接边
        public bool IsAdjacencyEdge(int edgeID)
        {
            return edgeID < mAdjacentEdges.Length;
        }

        public SearchGrid polygonGrid
        {
            get { return mPolygonGrid; }
        }

        public IDBuilder idBuilder
        {
            get { return mIDBuilder; }
        }

        public Edge[] edges
        {
            get { return mAdjacentEdges; }
        }

        public Triangle[] triangles
        {
            get { return mTriangles; }
        }

        public Vertice[] vertices
        {
            get { return mAllVertices; }
        }
    }
}