﻿using System.Collections.Generic;
using System.Text;
using cspb;
using Common;
using Logic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EventTriggerType = TFW.EventTriggerType;
using LocalizationMgr = TFW.Localization.LocalizationMgr;
using Public;
using System;
using TFW.UI;
using TFW;
using DeepUI;
using System.Collections;
using Game.Data;
using Render;
using UI.Utils;
using Game.Config;
using Config;
using Cysharp.Threading.Tasks;
using Logic.Alliance.Achievement;
using K1;

namespace UI.Alliance
{
    /// <summary>
    /// 联盟主界面 点击Item
    /// </summary>
    public class AllianceClickItemWidget : UIWidgetBase
    {
        public Action<GameObject, PointerEventData> clickAction;
        RedWidget redWidget;
        public GameObject redObj;
        public TFWText redText;
        public TFWText itemName;
        public AllianceItemEnum selfType;
        public RedPointType redType;
        int redPointCount = 0;

        public AllianceClickItemWidget(GameObject obj) : base(obj)
        {
            itemName = GetComponent<TFWText>("Image/Text");

            if(itemName==null)
                itemName= GetComponent<TFWText>("Text");

            redObj = GetChild("RedDot");
            redText = GetComponent<TFWText>("RedDot/BG/Text");
            var eventTrigger = UIHelper.GetComponent<EventTriggerListenerWithoutDrag>(obj);
            if (eventTrigger)
            {
                eventTrigger.RemoveListener("onClick");
                eventTrigger.AddListener("onClick", (go, args) => { ClickEvent(go, args); });
            }
            else
            {
                UIBase.AddRemoveListener(TFW.EventTriggerType.Click, rootObj, ClickEvent);
            }
            redWidget = new RedWidget(redObj);
            redWidget.SetData(0);


        }

        public AllianceClickItemWidget(GameObject obj, AllianceItemEnum ActionEnum) : this(obj)
        {
            this.selfType = ActionEnum;
            redType = RedPointType.RedPointNone;
            CheckShow();
            SetData();

            if(!LGameRedPoint.I.OpenClientRed)
                RefushRedByServer();
        }

        public override void Destroy()
        {
            if (!LGameRedPoint.I.OpenClientRed)
                RedPointMgr.I.RemoveRedPoint(redType, true);
            base.Destroy();
        }

        public void CheckShow()
        {
            var selfData = LAllianceMgr.I.GetUnionMembersWithId(LPlayer.I.PlayerID);
            if (selfData == null)
                return;
            switch (selfType)
            {
                case AllianceItemEnum.moveCity:
                    SetRootVisible(selfData.Cls >= (int)AllianceClassEnum.Class5);
                    break;
                case AllianceItemEnum.publicRecruit:
                    SetRootVisible(selfData.Cls >= (int)AllianceClassEnum.Class4);
                    break;
            }

            CheckName();
        }

        void CheckName()
        {
            switch (selfType)
            {
                case AllianceItemEnum.quit:
                    if (LAllianceMgr.I.IsUnionLeader())
                        itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt4");
                    else
                        itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt3");
                    break;
            }
        }

        new void SetData()
        {
            switch (selfType)
            {
                case AllianceItemEnum.war:
                    itemName.text = LocalizationMgr.Get("Rally_Function_Entrance");
                    break;
                case AllianceItemEnum.gift:
                    itemName.text = LocalizationMgr.Get("Alliance_Btn_Cap_Gift");
                    EventMgr.RegisterEvent(TEventType.UnionGiftInfoAck, RefreshGiftRedPoint, this);
                    break;
                case AllianceItemEnum.science:
                    itemName.text = LocalizationMgr.Get("Alliance_Function_Btn_Tech");
                    EventMgr.RegisterEvent(TEventType.UnionTechNtf, OnUnionTechNtf, this);
                    break;
                case AllianceItemEnum.challenge:
                    itemName.text = LocalizationMgr.Get("Alliance_Function_Btn_Challenge");
                    EventMgr.RegisterEvent(TEventType.MilestoneRewardAck, RefreshMobilize, this);
                    EventMgr.RegisterEvent(TEventType.ChestChangeNtf, RefreshMobilize, this);
                    break;
                case AllianceItemEnum.land:
                    itemName.text = LocalizationMgr.Get("Alliance_Btn_Cap_Territory");
                    break;
                case AllianceItemEnum.shop:
                    itemName.text = LocalizationMgr.Get("Shop_label_01");
                    break;
                case AllianceItemEnum.mainRank:
                    itemName.text = LocalizationMgr.Get("Kw_main_icon04");
                    break;
                case AllianceItemEnum.achievement:
                    itemName.text = LocalizationMgr.Get("Alliance_Goal_title");
                    break;
                case AllianceItemEnum.messgae:
                    itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt1");
                    break;
                case AllianceItemEnum.setting:
                    itemName.text = LocalizationMgr.Get("Alliance_title_3");
                    break;
                case AllianceItemEnum.log:
                    itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt2");
                    break;
                case AllianceItemEnum.rank:
                    itemName.text = LocalizationMgr.Get("Kw_main_icon04");
                    break;
                case AllianceItemEnum.allianceList:
                    itemName.text = LocalizationMgr.Get("Hero_List_Btn");
                    break;
                case AllianceItemEnum.moveCity:
                    itemName.text = LocalizationMgr.Get("Alliance_invite_move");
                    break;
                case AllianceItemEnum.publicRecruit:
                    itemName.text = LocalizationMgr.Get("Union_Recruit_Btn_Cap");
                    break;
                case AllianceItemEnum.quit:
                    if (LAllianceMgr.I.IsUnionLeader())
                        itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt4");
                    else
                        itemName.text = LocalizationMgr.Get("Alliance_Setting_Bnt3");
                    break;
            }
        }

        public async UniTask<AllianceItemEnum> RefushRed()
        {
            if (!LGameRedPoint.I.OpenClientRed)
                return AllianceItemEnum.none;

            redPointCount = 0;
            switch (selfType)
            {
                case AllianceItemEnum.war:
                    var b = MainFunctionOpenUtils.IsOpenState((int)MetaConfig.UnlockUnionRally, true);//战争功能解锁
                    var w= LPlayer.I.GetMainCityLevel() >= (int)MetaConfig.NewUnlockWorldBtn;//大世界开放了
                    if(b&&w)
                    {
                        redPointCount = GameData.I.MainData.MassRedCount;
                        redPointCount += GameData.I.RallyData.IsNeedShowRallyRed ? 1 : 0;
                    }
                    break;
                case AllianceItemEnum.gift:
                    //RefreshGiftRedPoint(null);
                    redPointCount = await GameData.I.AllianceGiftData.TotalRedPoint();
                    redPointCount += AllianceGameData.I.AllianceHelp.GetHelpInfoCount();
                    break;
                case AllianceItemEnum.science:
                    OnUnionTechNtf(null);
                    break;
                case AllianceItemEnum.land:
                    redPointCount = (LAllianceAltar.I.CanCollect ? 1 : 0);
                    break;
                case AllianceItemEnum.challenge:
                    RefreshMobilize(null).Forget();
                    break;
                case AllianceItemEnum.mainRank:
                    redPointCount = 0;
                    break;
                case AllianceItemEnum.messgae:
                    if (LAllianceMgr.I.HavePermission(AlliancePermissionEnum.UpdateJoinApply))
                    {
                        var unionList = LAllianceMgr.I.GetUnionApplies();
                        redPointCount = unionList.Count;
                    }
                    ///留言
                    redPointCount += AllianceGameData.I.AlliancMsgBoardData.HaveSelfUnionNewMsgCount;
                    break;
                case AllianceItemEnum.achievement:
                     
                    //redPointCount = LAllianceNewAchievement.I.GetRedCount();
                    break;
                case AllianceItemEnum.shop:
                    redPointCount = await GameData.I.AllianceShopData.GetShopRedCount();
                    break;
            }
            RefushRedposintShow();
            if (redPointCount > 0)
                return selfType;
            return AllianceItemEnum.none;
        }

        public void RefushRedByServer()
        {
            redPointCount = 0;
            switch (selfType)
            {
                case AllianceItemEnum.war:
                case AllianceItemEnum.mainRank:
                case AllianceItemEnum.messgae:
                    break;
                case AllianceItemEnum.gift:
                    redType = RedPointType.RedPointUnionGiftAll;
                    RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceGiftRedCount, RedPointType.RedPointUnionMain);
                    break;
                case AllianceItemEnum.science:
                    redType = RedPointType.RedPointUnionTech;
                    RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceRedCount, RedPointType.RedPointUnionMain);
                    break;
                case AllianceItemEnum.land:
                    redType = RedPointType.RedPointUnionLand;
                    RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceRedCount, RedPointType.RedPointUnionMain);
                    break;
                case AllianceItemEnum.challenge:
                    redType = RedPointType.RedPointUnionMobilize;
                    RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceRedCount, RedPointType.RedPointUnionMain);
                    break;
                case AllianceItemEnum.achievement:
                    //redType = RedPointType.RedPointUnionAchieve;
                    //RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceRedCount, RedPointType.RedPointUnionMain);
                    break;
                case AllianceItemEnum.shop:
                    redType = RedPointType.RedPointUnionShop;
                    RedPointMgr.I.CreateRedPoint(redType, redWidget, GetAllianceRedCount, RedPointType.RedPointUnionMain);
                    break;
            }
        }

        private int GetAllianceRedCount()
        {
            return LGameRedPoint.I.GetRedCount(redType);
        }

        private int GetAllianceGiftRedCount()
        {
            return LGameRedPoint.I.GetRedCount(RedPointType.RedPointUnionGift, RedPointType.RedPointUnionHelp);
        }

        async void ClickEvent(GameObject arg0, PointerEventData arg1)
        {
            switch (selfType)
            {
                case AllianceItemEnum.war:
                    var b = MainFunctionOpenUtils.IsOpenState((int)MetaConfig.UnlockUnionRally, true);
                    if (!b)
                    {
                        return;
                    }
                    //if (LPlayer.I.GetMainCityLevel() < MetaConfig.NewUnlockWorldBtn)
                    //{
                    //    UITools.PopTips(LocalizationMgr.Format("DEMO_37", MetaConfig.NewUnlockWorldBtn));
                    //    return;
                    //    //FloatTips.I.FloatMsg(LocalizationMgr.Get("Kvk_Quest_Btn_Block_Tips"));
                    //}

                    PopupManager.I.ShowLayer<UIMilitaryAlertMain>();
                    break;
                case AllianceItemEnum.gift:
                    PopupManager.I.ShowLayer<UIAllianceHelpAndGift>(new UIAllianceGiftData() 
                    {
                         showTab = UIAllianceGiftData.ShowTab.gift
                    });
                    break;
                case AllianceItemEnum.challenge:
                    if (LSwitchMgr.I.IsFunctionOpen(SwitchConfig.UnionChallenge))
                    {
                        AllianceMobilizeMgr.I.OpenAllianceAction();
                    }
                    else
                    {
                        FloatTips.I.FloatOpeningMsg();
                    }
                    //
                   
                    break;
                case AllianceItemEnum.science:
                    AllianceShopMgr.I.OpenAllianceTech();
                    break;
                case AllianceItemEnum.land:
                    //只在本服大地图显示联盟建筑界面
                    if (LCrossServer.I.CurrSceneType == SceneType.SceneTypeKvk)
                    {
                        UI.FloatTips.I.FloatMsg(LocalizationMgr.Get("KVK_UI_Tips2"));
                    }
                    else
                    {
                        PopupManager.I.ShowPanel<UIAllianceWarTerritoryList>();
                    }
                    break;
                case AllianceItemEnum.shop:
                   
                    AllianceShopMgr.I.OpenAllianceShop();
                    break;
                case AllianceItemEnum.mainRank:
                    RankNewMgr.I.ReqRankList(RankType.RankTypeUnionWarPoint, 0, LPlayer.I.UnionID, () =>
                    {
                        PopupManager.I.ShowLayer<UIAllianceSkillRank>();
                    });
                    break;
                case AllianceItemEnum.achievement:
                    FloatTips.I.FloatOpeningMsg();
                    //PopupManager.I.ShowPanel<UIAllianceAchievementPage>(new AllianceAchievementPageData()
                    //{

                    //});
                    //if (LAllianceNewAchievement.I.data == null)
                    //    await LAllianceNewAchievement.I.Init();

                    //PopupManager.I.ShowPanel<UIAllianceNewAchievementPage>(new UIAllianceNewAchievementPageData()
                    //{

                    //});
                    break;
                case AllianceItemEnum.messgae:
                    PopupManager.I.ShowPanel<UIAllianceMessage>(new UIAllianceMessageData()
                    {
                        showType = ShowBoardType.all,
                        isCanEdit = true,
                        data = LAllianceMgr.I.GetUnionInfo(),
                        showTitle = LocalizationMgr.Get("Alliance_Setting_Bnt1")
                    });
                    break;
                case AllianceItemEnum.setting:
                    PopupManager.I.ShowLayer<UIAllianceCreateAndSet>(new UIAllianceCreateAndSetData
                    {
                        createAndSetEnum = AllianceCreateAndSetEnum.set
                    });
                    break;
                case AllianceItemEnum.log:
                    PopupManager.I.ShowPanel<UIAllianceLog>(new UIAllianceLogData()
                    {
                        openPage = LogTabType.member
                    });
                    break;
                case AllianceItemEnum.rank:
                    ///暂时打开贡献排行榜
                    RankNewMgr.I.ReqRankList(RankType.RankTypeUnionWarPoint, 0, LPlayer.I.UnionID, () =>
                    {
                        //显示联盟贡献排行榜
                        RankNewMgr.I.OpenRankAttr(RankType.RankTypeUnionWarPoint, LPlayer.I.UnionID);
                    });
                    break;
                case AllianceItemEnum.publicRecruit:
                    OnPublicRecruitClicl();
                    break;
                case AllianceItemEnum.allianceList:
                    PopupManager.I.ShowPanel<UIAllianceList_k1>(new UIAllianceList_k1Data()
                    {
                        Title = LocalizationMgr.Get("Hero_List_Btn"),
                        isUnionList = true
                    });
                    break;
                case AllianceItemEnum.moveCity:
                    OnMoveCityBtnClick();
                    break;
                case AllianceItemEnum.LavaCave:
                    FloatTips.I.FloatOpeningMsg();
                    //LLavaCave.I.ReqLavaCave();
                    break;
                case AllianceItemEnum.quit:
                    OnExitAllianceBtnClick();
                    break;
            }
        }

        public override void Clear()
        {
            base.Clear();
        }

        #region  红点

        async void OnUnionTechNtf(object[] arr)
        {
            redPointCount = await GameData.I.AllianceTechData.GetTechRedCount();
            RefushRedposintShow();
        }

        async UniTaskVoid RefreshGiftRedPoint(object[] arr)
        {
            redPointCount = await GameData.I.AllianceGiftData.TotalRedPoint();
            redPointCount += AllianceGameData.I.AllianceHelp.GetHelpInfoCount();
            RefushRedposintShow();
        }

        async UniTaskVoid RefreshMobilize(object[] arr)
        {
            if (AllianceMobilizeMgr.I.IsCanOpenMobilize(false))
            {
                redPointCount = await GameData.I.AllianceMobilizeData.GetMobilizeRedCount();
                RefushRedposintShow();
            }
        }

        void RefushRedposintShow()
        {
            redWidget.SetData(redPointCount, false);
        }

        #endregion


        #region  click


        void OnMoveCityBtnClick()
        {
            //WndMgr.Hide<UI.Alliance.UIAllianceMain>();

            if (GameData.I.MainData.CurrMenuType != MainMenuType.WORLD)
            {
                EventMgr.FireEvent(TEventType.UIMain_Update_ShowMenu, (int)MainMenuType.WORLD);
                NTimer.CountDownNoPool(0.5f, () =>
                {
                    CityTeleportManager.I.StartTeleport(RMap.ViewCenter,
                        TeleportTypeEnum.TeleportTypeInvite).Forget();//GameData.I.DragonWarData.SignData.IsDragonWaring ? TeleportTypeEnum.TeleportDragonWar : 
                });
            }
            else
            {
                CityTeleportManager.I.StartTeleport(RMap.ViewCenter,
                    TeleportTypeEnum.TeleportTypeInvite).Forget();//GameData.I.DragonWarData.SignData.IsDragonWaring ? TeleportTypeEnum.TeleportDragonWar : 
            }
        }

        /// <summary>
        /// 公开招募
        /// </summary>
        void OnPublicRecruitClicl()
        {
            if (!LAllianceMgr.I.HavePermission(AlliancePermissionEnum.Recruit))
            {
                UI.FloatTips.I.FloatMsg(LocalizationMgr.Get("UNION_no_jurisdiction"));
                return;
            }

            var classInfo = LAllianceMgr.I.GetMemberClassInfo(LPlayer.I.PlayerID);
            if (classInfo.Class >= (int)AllianceClassEnum.Class4)
            {
                var cost = (int)MetaConfig.UnionOpenRecruitGems;
                var myDiamond = PlayerAssetsMgr.I.GetVMCount(ConfigID.VM_Diamond);
                if (myDiamond < cost)//钻石不足
                {
                    ItemStoreMgr.I.OpenGetAssetPop(GameCurrencyEnum.CURRENCY_DIAMOND);
                    //FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("MENU_cd_not_enough_title"));
                    return;
                }
                var btn1data = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
                };

                var btn2data = new MsgBoxBtnParam()
                {
                    str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                    func = (o) =>
                    {
                        LAllianceMgr.I.ReqUnionRecruit();

                        //招募可能需要等级判断
                        //UIChatHelper.I.ShareRecruitToWorldChannel();
                    },
                };

                UIMsgBox.Push(EMsgBoxType.two_nc, LocalizationMgr.Get("Union_Recruit_Btn_Cap"),
                    LocalizationMgr.Format("Union_Recruit_PayCheck",
                        string.Format("<color=#5B28FF>{0}</color>", PublicFunc.FormatNumberThousandsInteger(cost))),
                    btn1data,
                    btn2data);
            }
            else
            {
                // R4及以上玩家才可以发布公开招募信息 
                FloatTips.I.FloatMsg(LocalizationMgr.GetUIString("ERRCODE_unionopenrecruitnotpermit"));
            }
        }
        void OnExitAllianceBtnClick()
        {
            var m_IsUnionLeader = LAllianceMgr.I.IsUnionLeader();
            if (m_IsUnionLeader)
            {
                ShowConfirmBoxBtn(LocalizationMgr.Get("LC_UNION_manage_dissolve"),
                    LocalizationMgr.Get("LC_UNION_affirm_dissolve"),
                    () => { LAllianceMgr.I.DisbandUnionReq(); });
            }
            else
            {
                string content = LocalizationMgr.Get("Alliance_Quit_Alarm");
                //if (LAllianceHelp.I.CheckSelfAllianceHelp())
                //{
                //    content = LocalizationMgr.Get("Alliance_Help_Text7");
                //}
                ShowConfirmBoxBtn(LocalizationMgr.Get("LC_UNION_manage_quit"), content,
                    () => { LAllianceMgr.I.QuitUnionReq(); });
            }
        }

        #endregion


        private void ShowConfirmBoxBtn(string title, string content, Action okCallBack)
        {
            var btn1data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_confirm_cap"),
                func = (arg) => { okCallBack?.Invoke(); },
            };
            var btn2data = new MsgBoxBtnParam()
            {
                str = LocalizationMgr.Get("LC_MENU_cancel_cap"),
            };
            UIMsgBox.Push(EMsgBoxType.two, title, content, btn1data, btn2data, ButtonColorGroup.RedBlue, ContentTextAnchor: TextAnchor.MiddleLeft);
        }
    }

    public enum AllianceItemEnum
    {
        /// <summary>
        /// 联盟战争
        /// </summary>
        war,
        /// <summary>
        /// 联盟礼物
        /// </summary>
        gift,
        /// <summary>
        /// 联盟挑战
        /// </summary>
        challenge,
        /// <summary>
        /// 联盟科技
        /// </summary>
        science,
        /// <summary>
        /// 联盟领地
        /// </summary>
        land,
        /// <summary>
        /// 联盟商店
        /// </summary>
        shop,
        /// <summary>
        /// 联盟排行
        /// </summary>
        mainRank,
        /// <summary>
        /// 联盟成就
        /// </summary>
        achievement,
        /// <summary>
        /// 熔岩地洞
        /// </summary>
        LavaCave,
        /// <summary>
        /// 联盟信息
        /// </summary>
        messgae,
        /// <summary>
        /// 联盟设置
        /// </summary>
        setting,
        /// <summary>
        /// 联盟日志
        /// </summary>
        log,
        /// <summary>
        /// 联盟排行榜
        /// </summary>
        rank,
        /// <summary>
        /// 联盟列表
        /// </summary>
        allianceList,
        /// <summary>
        /// 联盟迁城
        /// </summary>
        moveCity,
        /// <summary>
        /// 公开招募
        /// </summary>
        publicRecruit,
        /// <summary>
        /// 联盟退出
        /// </summary>
        quit,

        none,
    }
}