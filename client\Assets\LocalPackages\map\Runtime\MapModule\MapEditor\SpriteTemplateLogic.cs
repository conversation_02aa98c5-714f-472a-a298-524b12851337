﻿ 



 
 



#if UNITY_EDITOR

/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;

namespace TFW.Map {
    [ExecuteInEditMode]
    [Black]
    public class SpriteTemplateLogic : MonoBehaviour {
        public SpriteTemplate spriteTemplate {
            get {
                return Map.currentMap.FindObject(spriteTemplateID) as SpriteTemplate;
            }
        }

        public int spriteTemplateID;
    }
}

#endif