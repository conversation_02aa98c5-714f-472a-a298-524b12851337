﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SkeletonAnimationBaker
    {
        class AnimationData
        {
            public AnimationData(AnimationFrames frames, string stateName, float lengthInSeconds, float framerate, float frameMultiplier, bool loop, AnimationClip clip)
            {
                mClip = clip;
                mFrames = frames;
                mStateName = stateName;
                mLengthInSeconds = lengthInSeconds;
                mFramerate = framerate;
                mFrameMultiplier = frameMultiplier;
                mLoop = loop;
            }

            public AnimationFrames frames { get { return mFrames; } }
            public string stateName { get { return mStateName; } }
            public float lengthInSeconds { get { return mLengthInSeconds; } }
            public float framerate { get { return mFramerate; } }
            public float frameMultiplier { get { return mFrameMultiplier; } }
            public bool loop { get { return mLoop; } }
            public AnimationClip clip { get { return mClip; } }

            AnimationFrames mFrames;
            string mStateName;
            float mLengthInSeconds;
            float mFramerate;
            float mFrameMultiplier;
            AnimationClip mClip;
            bool mLoop;
        }

        //一个skin renderer使用的动画数据
        class AnimationFrames
        {
            public AnimationFrames(int totalFrame)
            {
                mBoneTransforms = new List<Matrix4x4[]>(totalFrame);
                mCustomBoneTransforms = new List<Matrix4x4[]>(totalFrame);
            }

            //添加某帧的所有骨骼的transform
            public void AddBoneFrameTransforms(Matrix4x4[] transformsAtFrame)
            {
                mBoneTransforms.Add(transformsAtFrame);
            }

            //添加某帧的自定义骨骼的transform
            public void AddCustomBoneFrameTransforms(Matrix4x4[] transformsAtFrame)
            {
                mCustomBoneTransforms.Add(transformsAtFrame);
            }

            public void AddCPUDriveCustomBoneFrameTransforms(Vector3[] translations, Quaternion[] rotations, Vector3[] scalings)
            {
                mCPUDrivenCustomBoneTranslations.Add(translations);
                mCPUDrivenCustomBoneRotations.Add(rotations);
                mCPUDrivenCustomBoneScalings.Add(scalings);
            }

            public void SetCPUDrivenCustomBoneNames(string[] names)
            {
                mCPUDrivenCustomBoneNames = names;
            }

            public List<Matrix4x4[]> boneTransforms { get { return mBoneTransforms; } }
            public List<Matrix4x4[]> customBoneTransforms { get { return mCustomBoneTransforms; } }
            public List<Vector3[]> cpuDrivenCustomBoneTranslations { get { return mCPUDrivenCustomBoneTranslations; } }
            public List<Vector3[]> cpuDrivenCustomBoneScalings { get { return mCPUDrivenCustomBoneScalings; } }
            public List<Quaternion[]> cpuDrivenCustomBoneRotations { get { return mCPUDrivenCustomBoneRotations; } }
            public string[] cpuDrivenCustomBoneNames { get { return mCPUDrivenCustomBoneNames; } }

            //骨骼的数据,用于shader中
            List<Matrix4x4[]> mBoneTransforms;
            //骨骼上挂接点的数据,用于shader中
            List<Matrix4x4[]> mCustomBoneTransforms;
            //骨骼上挂接点的数据,用于CPU驱动transform
            List<Vector3[]> mCPUDrivenCustomBoneTranslations = new List<Vector3[]>();
            List<Vector3[]> mCPUDrivenCustomBoneScalings = new List<Vector3[]>();
            List<Quaternion[]> mCPUDrivenCustomBoneRotations = new List<Quaternion[]>();
            string[] mCPUDrivenCustomBoneNames;
        }
    }
}
