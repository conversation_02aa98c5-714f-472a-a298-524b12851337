﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    //非逻辑的物体直接挂这个脚本
    class KeepScaleByUpdate : KeepScaleBySize
    {
        void OnEnable()
        {
            SetInitScale();
        }

        void OnDisable()
        {
            mLastCameraHeight = 0;
        }

        protected override KeepScaleConfig GetScaleConfig()
        {
            return config;
        }

        void Update()
        {
            if (!Utils.Approximately(mLastCameraHeight, MapCameraMgr.currentCameraHeight))
            {
                UpdateScaleImpl();
            }
        }
    }
}
