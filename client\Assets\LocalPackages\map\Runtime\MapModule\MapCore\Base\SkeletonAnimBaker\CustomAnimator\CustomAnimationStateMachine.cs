﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public class CustomAnimationStateMachine
    {
        public CustomAnimationStateMachine(AnimationInfo[] animations, AnimationStateInfo[] states, AnimationParameterInfo[] parameters, bool useAnimationBlendingWhenBaking/*, int debugID*/)
        {
            //mDebugID = debugID;
            mUseAnimationBlendingWhenBaking = useAnimationBlendingWhenBaking;
            CreateParameters(parameters);
            Debug.Assert(animations != null && animations.Length > 0);

            mStates = new CustomAnimationState[states.Length];
            for (int i = 0; i < states.Length; ++i)
            {
                int animIndex = states[i].animationIndex;
                var state = new CustomAnimationState(Animator.StringToHash(states[i].stateName), states[i], animations[animIndex], animIndex/*, mDebugID*/);
                mStates[i] = state;
            }
            for (int i = 0; i < states.Length; ++i)
            {
                mStates[i].PostInit(this);
            }

            ChangeAnimationBlendStateTransitionUpdater(useAnimationBlendingWhenBaking, mStates[0]);            
        }

        public void Play(string name, float normalizedNextStateFixedStartOffset, float normalizedTransitionDuration)
        {
            var state = GetState(name);
            if (state != null)
            {
                float transitionDuration = state.animInfo.lengthInSeconds * normalizedTransitionDuration;
                var curTime = currentState.currentTime;
                float nextStateStartOffset = normalizedNextStateFixedStartOffset * state.animInfo.lengthInSeconds;
                SetState(state, nextStateStartOffset, transitionDuration, curTime);
            }
            else
            {
                Debug.LogError($"animation state {name} not found!");
            }
        }

        public void Play(int stateHashName, float normalizedNextStateFixedStartOffset, float normalizedTransitionDuration)
        {
            var state = GetState(stateHashName);
            if (state != null)
            {
                float duration = state.animInfo.lengthInSeconds * normalizedTransitionDuration;
                var curTime = currentState.currentTime;
                float startOffset = normalizedNextStateFixedStartOffset * state.animInfo.lengthInSeconds;
                SetState(state, startOffset, duration, curTime);
            }
            else
            {
                Debug.LogError($"animation state {stateHashName} not found!");
            }
        }

        public void PlayInFixedTime(string name, float nextStateFixedStartOffset, float fixedTransitionDuration)
        {
            var state = GetState(name);
            if (state != null)
            {
                var curTime = currentState.currentTime;
                SetState(state, nextStateFixedStartOffset, fixedTransitionDuration, curTime);
            }
            else
            {
                Debug.LogError($"animation state {name} not found!");
            }
        }

        public void PlayInFixedTime(int stateHashName, float nextStateFixedStartOffset, float fixedTransitionDuration)
        {
            var state = GetState(stateHashName);
            if (state != null)
            {
                var curTime = currentState.currentTime;
                SetState(state, nextStateFixedStartOffset, fixedTransitionDuration, curTime);
            }
            else
            {
                Debug.LogError($"animation state {stateHashName} not found!");
            }
        }

        public void Play(string name)
        {
            var state = GetState(name);
            if (state != null)
            {
                SetState(state, 0, 0, 0);
            }
            else
            {
                Debug.LogError($"animation state {name} not found!");
            }
        }

        public void Play(int stateHashName)
        {
            var state = GetState(stateHashName);
            if (state != null)
            {
                SetState(state, 0, 0, 0);
            }
            else
            {
                Debug.LogError($"animation state {stateHashName} not found!");
            }
        }

        public void Pause()
        {
            currentState?.Pause();
        }

        public void Resume()
        {
            currentState?.Resume();
        }

        public bool Update(GlobalAnimationBlendingState state)
        {
            bool animationBlendingStateChanged = false;
            bool isAnimationBlendingEnabledNow = IsAnimationBlendingEnabledNow(state);
            if (isAnimationBlendingEnabledNow != mEnableAnimationBlendingNow)
            {
                ChangeAnimationBlendStateTransitionUpdater(isAnimationBlendingEnabledNow, currentState);
                animationBlendingStateChanged = true;
            }
            mStateTransitionUpdater.Update(mSpeed, mActiveParameters, this);
            return animationBlendingStateChanged;
        }

        void SetState(CustomAnimationState state, float nextStateStartOffset, float transitionDuration, float curStateTransitionStartTime)
        {
            //check if cycle offset exists
            float absoluteCycleOffset;
            bool hasCycleOffset = state.GetCycleOffset(mActiveParameters, out absoluteCycleOffset);
            if (hasCycleOffset)
            {
                nextStateStartOffset = absoluteCycleOffset;
            }

            mStateTransitionUpdater.SetState(state, nextStateStartOffset, transitionDuration, curStateTransitionStartTime, interruptionType);
        }

        public CustomAnimationState GetState(string stateName)
        {
            for (int i = 0; i < mStates.Length; ++i)
            {
                if (mStates[i].animStateInfo.stateName == stateName)
                {
                    return mStates[i];
                }
            }
            return null;
        }

        CustomAnimationState GetState(int stateHashName)
        {
            for (int i = 0; i < mStates.Length; ++i)
            {
                if (mStates[i].id == stateHashName)
                {
                    return mStates[i];
                }
            }
            return null;
        }

        bool CheckTransition()
        {
            float transitionDuration = 0;
            var nextState = currentState?.CheckTransition(mActiveParameters, out transitionDuration);
            if (nextState != null)
            {
                SetState(nextState, 0, transitionDuration, currentState.currentTime);
                return true;
            }
            return false;
        }

        public void SetBool(string name, bool val)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Bool)
            {
                if (param.defaultBool != val)
                {
                    param.defaultBool = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid bool parameter {name}");
#endif
            }
        }

        public void SetBool(int id, bool val)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Bool)
            {
                if (param.defaultBool != val)
                {
                    param.defaultBool = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid bool parameter {id}");
#endif
            }
        }

        public void SetTrigger(string name)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Trigger)
            {
                if (!param.defaultBool)
                {
                    param.defaultBool = true;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid trigger parameter {name}");
#endif
            }
        }

        public void SetTrigger(int id)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Trigger)
            {
                if (!param.defaultBool)
                {
                    param.defaultBool = true;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid trigger parameter {id}");
#endif
            }
        }

        public float GetFloat(string name)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Float)
            {
                return param.defaultFloat;
            }
#if UNITY_EDITOR
            Debug.Log($"float parameter {name} not found!");
#endif
            return 0;
        }

        public float GetFloat(int id)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Float)
            {
                return param.defaultFloat;
            }
#if UNITY_EDITOR
            Debug.Log($"float parameter {id} not found!");
#endif
            return 0;
        }

        public bool GetBool(string name)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Bool)
            {
                return param.defaultBool;
            }
#if UNITY_EDITOR
            Debug.Log($"bool parameter {name} not found!");
#endif
            return false;
        }

        public bool GetBool(int id)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Bool)
            {
                return param.defaultBool;
            }
#if UNITY_EDITOR
            Debug.Log($"bool parameter {id} not found!");
#endif
            return false;
        }

        public int GetInteger(string name)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Int)
            {
                return param.defaultInt;
            }
#if UNITY_EDITOR
            Debug.Log($"int parameter {name} not found!");
#endif
            return 0;
        }

        public int GetInteger(int id)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Int)
            {
                return param.defaultInt;
            }
#if UNITY_EDITOR
            Debug.Log($"int parameter {id} not found!");
#endif
            return 0;
        }

        public void ResetTrigger(string name)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Trigger)
            {
                param.defaultBool = false;
            }
            else
            {
#if UNITY_EDITOR
                Debug.Log($"trigger parameter {name} not found!");
#endif
            }
        }

        public void ResetTrigger(int id)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Trigger)
            {
                param.defaultBool = false;
            }
            else
            {
#if UNITY_EDITOR
                Debug.Log($"trigger parameter {id} not found!");
#endif
            }
        }

        public void SetFloat(string name, float val)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Float)
            {
                if (!Mathf.Approximately(param.defaultFloat, val))
                {
                    param.defaultFloat = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid float parameter {name}");
#endif
            }
        }

        public void SetFloat(int id, float val)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Float)
            {
                if (!Mathf.Approximately(param.defaultFloat, val))
                {
                    param.defaultFloat = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid float parameter {id}");
#endif
            }
        }

        public void SetInteger(string name, int val)
        {
            var param = GetActiveParameter(name);
            if (param != null && param.type == AnimatorControllerParameterType.Int)
            {
                if (param.defaultInt != val)
                {
                    param.defaultInt = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid int parameter {name}");
#endif
            }
        }

        public void SetInteger(int id, int val)
        {
            var param = GetActiveParameter(id);
            if (param != null && param.type == AnimatorControllerParameterType.Int)
            {
                if (param.defaultInt != val)
                {
                    param.defaultInt = val;
                    CheckTransition();
                }
            }
            else
            {
#if UNITY_EDITOR
                Debug.LogError($"{mAnimatorName} invalid int parameter {id}");
#endif
            }
        }

        void CreateParameters(AnimationParameterInfo[] parameters)
        {
            Debug.Assert(mActiveParameters == null);
            mActiveParameters = new AnimationParameterInfo[parameters.Length];
            for (int i = 0; i < parameters.Length; ++i)
            {
                mActiveParameters[i] = new AnimationParameterInfo();
                mActiveParameters[i].defaultBool = parameters[i].defaultBool;
                mActiveParameters[i].defaultFloat = parameters[i].defaultFloat;
                mActiveParameters[i].defaultInt = parameters[i].defaultInt;
                mActiveParameters[i].type = parameters[i].type;
                mActiveParameters[i].name = parameters[i].name;
                mActiveParameters[i].nameHash = parameters[i].nameHash;
            }
        }

        public bool HasState(int id)
        {
            for (int i = 0; i < mStates.Length; ++i)
            {
                if (mStates[i].id == id)
                {
                    return true;
                }
            }
            return false;
        }

        public AnimationParameterInfo GetParameter(int index)
        {
            if (index >= 0 && index < mActiveParameters.Length)
            {
                return mActiveParameters[index];
            }
            return null;
        }

        AnimationParameterInfo GetActiveParameter(string name)
        {
            for (int i = 0; i < mActiveParameters.Length; ++i)
            {
                if (mActiveParameters[i].name == name)
                {
                    return mActiveParameters[i];
                }
            }
            return null;
        }

        AnimationParameterInfo GetActiveParameter(int id)
        {
            for (int i = 0; i < mActiveParameters.Length; ++i)
            {
                if (mActiveParameters[i].nameHash == id)
                {
                    return mActiveParameters[i];
                }
            }
            return null;
        }

        public string[] GetAnimationNames()
        {
            string[] clipNames = new string[mStates.Length];
            for (int i = 0; i < clipNames.Length; ++i)
            {
                clipNames[i] = mStates[i].animInfo.clipName;
            }
            return clipNames;
        }

        public bool IsPlayingAnimation(string animStateName)
        {
            var currentState = mStateTransitionUpdater.currentState;
            var nextState = mStateTransitionUpdater.nextState;
            bool isPlayingAnimation = false;
            if (currentState != null)
            {
                isPlayingAnimation |= (currentState.animStateInfo.stateName == animStateName);
            }
            if (nextState != null)
            {
                isPlayingAnimation |= (nextState.animStateInfo.stateName == animStateName);
            }

            return isPlayingAnimation;
        }

        bool IsAnimationBlendingEnabledNow(GlobalAnimationBlendingState state)
        {
            if (state == GlobalAnimationBlendingState.Off)
            {
                return mUseAnimationBlendingWhenBaking;
            }
            else if (state == GlobalAnimationBlendingState.EnableAnimationBlending)
            {
                return true;
            }
            else if (state == GlobalAnimationBlendingState.DisableAnimationBlending)
            {
                return false;
            }

            Debug.Assert(false, "unknown state");
            return false;
        }

        void ChangeAnimationBlendStateTransitionUpdater(bool enableAnimationBlendingNow, CustomAnimationState initState)
        {
            mEnableAnimationBlendingNow = enableAnimationBlendingNow;
            if (enableAnimationBlendingNow)
            {
                mStateTransitionUpdater = new CustomAnimatorBlendStateTransitionUpdaterWithInterruption(initState);
            }
            else
            {
                mStateTransitionUpdater = new CustomAnimatorSimpleStateTransitionUpdater(initState);
            }
        }

        public void RegisterAnimationEventCallback(System.Action<string, string> stringEventCallback, System.Action<string, int> intEventCallback, System.Action<string, float> floatEventCallback)
        {
            mIntEventCallback = intEventCallback;
            mStringEventCallback = stringEventCallback;
            mFloatEventCallback = floatEventCallback;
        }

        public void UnregisterAnimationEventCallback()
        {
            mIntEventCallback = null;
            mStringEventCallback = null;
            mFloatEventCallback = null;
        }

        public void UnregisterAnimationIntEventCallback()
        {
            mIntEventCallback = null;
        }

        public void UnregisterAnimationFloatEventCallback()
        {
            mFloatEventCallback = null;
        }

        public void UnregisterAnimationStringEventCallback()
        {
            mStringEventCallback = null;
        }

        public void TriggerEventCallback(AnimationEventInfo eventInfo)
        {
            if (mIntEventCallback != null)
            {
                mIntEventCallback(eventInfo.functionName, eventInfo.intParameter);
            }
            if (mStringEventCallback != null)
            {
                mStringEventCallback(eventInfo.functionName, eventInfo.stringParameter);
            }
            if (mFloatEventCallback != null)
            {
                mFloatEventCallback(eventInfo.functionName, eventInfo.floatParameter);
            }
        }

#if UNITY_EDITOR
        public void SetAnimatorName(string animatorName)
        {
            mAnimatorName = animatorName;
        }
#endif

        public CustomAnimationState currentState { get { return mStateTransitionUpdater.currentState; } }
        public CustomAnimationState nextState { get { return mStateTransitionUpdater.nextState; } }
        public AnimationParameterInfo[] parameters { get { return mActiveParameters; } }
        public float speed { get { return mSpeed; } set { mSpeed = value; } }
        public CustomAnimationState[] states { get { return mStates; } }
        public bool IsInTransitionDuration { get { return mStateTransitionUpdater.IsInTransition; } }
        public float normalizedDurationRatio { get { return mStateTransitionUpdater.normalizedDurationRatio; } }
        public float currentStateFrameInTransitionDuration { get { return mStateTransitionUpdater.currentStateFrameInTransitionDuration; } }
        public float nextStateFrameInTransitionDuration { get { return mStateTransitionUpdater.nextStateFrameInTransitionDuration; } }
        public float nextStateTimeInTransitionDuration { get { return mStateTransitionUpdater.nextStateTimeInTransitionDuration; } }
        public bool enableAnimationBlendingNow { get { return mEnableAnimationBlendingNow; } }
        public InterruptionType interruptionType { get; set; } = InterruptionType.WaitUntilFinishTransition;

        CustomAnimationState[] mStates;
        AnimationParameterInfo[] mActiveParameters;
        AnimationInfo[] mInfo;
        float mSpeed = 1.0f;
        //这个是烘培时勾选的状态
        bool mUseAnimationBlendingWhenBaking;
        bool mEnableAnimationBlendingNow;
        ICustomAnimatorStateTransitionUpdater mStateTransitionUpdater;
        System.Action<string, int> mIntEventCallback;
        System.Action<string, string> mStringEventCallback;
        System.Action<string, float> mFloatEventCallback;
#if UNITY_EDITOR
        //用来调试
        string mAnimatorName;
        //int mDebugID;
#endif
    }
}
