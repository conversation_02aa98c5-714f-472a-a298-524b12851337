﻿ 



 
 

#if UNITY_EDITOR

//created by wzw at 2019/8/15

using UnityEngine;

namespace TFW.Map
{
    [Black]
    public class MapCollisionView : PolygonObjectView
    {
        public MapCollisionView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        //创建视图使用的模型
        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            Debug.Assert(data != null);

            ModelBase model = new PolygonObjectModel(data as PolygonObjectData, MapCoreDef.COLLISION_MODEL_NAME);
            var layerView = mLayerView as PolygonObjectLayerView;
            model.transform.SetParent(layerView.shadowRoot.transform, true);

            return model;
        }
    }
}


#endif