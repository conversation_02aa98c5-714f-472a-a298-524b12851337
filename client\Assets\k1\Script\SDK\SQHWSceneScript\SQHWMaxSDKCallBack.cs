﻿

using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using UnityEngine;
using maxsdk;
using maxsdkHWExt;


/**
 * 当前脚本为接口回调示例
 * 实现了MaxSDKListener中定义的回调方法
 */
namespace maxsdkSQHW
{
    public abstract class SQHWMaxSDKCallBack : MaxSDKHWListener
    {
        // 初始化成功回调，按需解析获取initData
        public override void OnInitSuccess(MaxSDKInitBean initBean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：初始化成功", ObjectToJson(initBean));
        }
        // 初始化失败回调
        public override void OnInitFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：初始化失败", ObjectToJson(bean));
        }


        // 登录成功回调
        public override void OnLoginSuccess(MaxSDKLoginBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：登录成功", ObjectToJson(bean));
        }
        // 登录失败回调
        public override void OnLoginFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：登录失败", ObjectToJson(bean));
        }


        // 登出成功回调
        public override void OnLogoutSuccess(MaxSDKLoginBean logoutBean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：账号登出成功", "");
        }
        // 登出失败回调
        public override void OnLogoutFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：账号登出失败", ObjectToJson(bean));
        }


        // 切换账号成功的回调
        public override void OnChangeAccountSuccess(MaxSDKLoginBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：切换账号成功", ObjectToJson(bean));

            var extData = maxsdk.SimpleJSON.JSONNode.Parse(bean.extData);
            string isLogout = extData["isLogout"].Value;
            if (isLogout.Equals("1"))
            {
                /**
                 * 1、东南亚账密模式下，点击用户中心切换账号，SDK会退出当前账户，然后触发当前回调，回调数据如下：
                 *      extData = { "isLogout": "1" }
                 *      研发需要在此处处理回到游戏开始的界面，然后调用自动登录接口，SDK会拉起登录界面让玩家继续选择新的登录方式；
                 */
                MaxSDK.GetInstance().AutoLogin();
            }
            else
            {
                /**
                 * 2、欧美无登录界面模式下，点击用户中心切换账号，SDK会退出当前账户，并弹窗让用户选择其它登录方式，用户完成选择后，触发当前回调，回调数据如下：
                 *      extData = { "isLogout": "0", "uid": "xxx", "其它账号相关信息"}，
                 *      研发可以在此处直接进入选服界面
                 *      当然也可以直接再调用一次自动登录接口，统一在登录回调里做后续处理
                 */
                MaxSDK.GetInstance().AutoLogin();
            }
        }
        // 切换账号失败
        public override void OnChangeAccountFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：切换账号失败", ObjectToJson(bean));
        }


        // 支付成功回调
        public override void OnPaySuccess(MaxSDKPayBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：支付成功", ObjectToJson(bean));
        }
        // 支付失败回调
        public override void OnPayFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：支付失败", ObjectToJson(bean));
        }


        // 分享成功回调
        public override void OnShareSuccess(MaxSDKShareBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：分享成功", ObjectToJson(bean));
            var extData = maxsdk.SimpleJSON.JSONNode.Parse(bean.extData);
            string isSimplyShare = extData["isSimplyShare"].Value;
            if (isSimplyShare != null && isSimplyShare.Equals("1"))
            { /*简单分享成功回调*/ }
            else
            { /*带奖励分享成功回调*/ }
        }
        // 分享失败回调
        public override void OnShareFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：分享失败", ObjectToJson(bean));
        }
        // 分享取消回调
        public override void OnShareCancel()
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：取消分享", "");
        }


        // 确认退出游戏回调
        public override void OnExitSuccess(MaxSDKExitBean exitBean)
        {
            ShowMessage("[MaxSDK Demo] Unity收到原生回调：确认退出游戏", "");
        }


        // 获取内购商品信息成功回调
        public override void OnGetInAppSkuDetailSuccess(Dictionary<string, MaxSDKHWSkuDetailBean> skuInfoDict)
        {
            ShowMessage("[MaxSDK Demo] 获取内购商品详情成功", ObjectToJson(skuInfoDict));
            foreach (var item in skuInfoDict)
            {
                Debug.Log("商品ID：" + item.Key + "，价格：" + item.Value.price);
            }
        }
        // 获取内购商品信息失败回调
        public override void OnGetInAppSkuDetailFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 获取内购商品详情失败：" , ObjectToJson(bean));
        }


        // 账号绑定成功回调
        public override void OnBindAccountSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 用户中心内账号绑定成功：", infoJson);
        }
        // 账号绑定失败回调
        public override void OnBindAccountFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 用户中心内账号绑定失败：", ObjectToJson(bean));
        }


        // 获取FB游戏内好友信息成功回调
        public override void OnGetFBInGameFriendsSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 获取FB游戏内好友信息成功：" , infoJson);
        }
        // 获取FB游戏内好友信息失败回调
        public override void OnGetFBInGameFriendsFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 获取FB游戏内好友信息失败：" , ObjectToJson(bean));
        }



        // 邀请FB好友进入游戏成功回调
        public override void OnInviteFBFriendsSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 邀请FB好友进入游戏成功：", infoJson);
        }
        // 邀请FB好友进入游戏失败回调
        public override void OnInviteFBFriendsFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 邀请FB好友进入游戏失败：", ObjectToJson(bean));
        }


        // 获取用户首次安装受邀信息成功回调
        public override void OnGetAdChannelDataSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 获取用户首次安装受邀信息成功：", infoJson);
        }
        // 获取用户首次安装受邀信息失败回调
        public override void OnGetAdChannelDataFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 获取用户首次安装受邀信息失败：", ObjectToJson(bean));
        }


        // SDK语言设置成功回调
        public override void OnSetSDKLanguageComplete(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] SDK语言设置成功：", infoJson);
        }


        // 游戏内按钮对接信息更新回调
        public override void OnInGameButtonInfoUpdate(List<MaxSDKHWInGameButtonBean> gameButtonBeans)
        {
            ShowMessage("[MaxSDK Demo] 游戏内按钮对接信息更新成功：", ObjectToJson(gameButtonBeans));
        }


        // 游客账户绑定第三方平台回调
        public override void OnBindPlatformSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 游客账户绑定第三方平台成功：", infoJson);
        }
        public override void OnBindPlatformFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 游客账户绑定第三方平台失败：", ObjectToJson(bean));
        }

        // 第三方登录回调
        public override void OnLoginPlatformSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 第三方登录成功：", infoJson);
        }
        public override void OnLoginPlatformFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 第三方登录失败：", ObjectToJson(bean));
        }

        // 获取Firebase推送Token回调
        public override void OnGetFCMTokenSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 获取Firebase推送Token成功：", infoJson);
        }
        public override void OnGetFCMTokenFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 获取Firebase推送Token失败：", ObjectToJson(bean));
        }

        // 游戏内切换icon回调
        public override void OnChangeAppIconSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 游戏内切换icon成功：", infoJson);
        }
        public override void OnChangeAppIconFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 游戏内切换icon失败：", ObjectToJson(bean));
        }

        // 带用户类型的登出接口回调（休闲益智游戏）
        public override void OnLogoutWithTypeSuccess(string infoJson)
        {
            ShowMessage("[MaxSDK Demo] 按用户类型登出成功：", infoJson);
        }
        public override void OnLogoutWithTypeFail(MaxSDKFailBean bean)
        {
            ShowMessage("[MaxSDK Demo] 按用户类型登出失败：", ObjectToJson(bean));
        }

    }

}

