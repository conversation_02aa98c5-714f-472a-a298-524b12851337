﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using UnityEditor.SceneManagement;

namespace TFW.Map
{
    [CustomEditor(typeof(FOVConfig))]
    public class FOVConfigUI : UnityEditor.Editor
    {
        void OnEnable()
        {
            mConfig = target as FOVConfig;
            mCurveUI = new CurveConfigUI(mConfig, OnSetDirty, "Height", "FOV", "Camera FOV Setting");
        }

        void OnDisable()
        {
            mCurveUI.OnDestroy();
        }

        public override void OnInspectorGUI()
        {
            mCurveUI.OnInspectorGUI();
        }

        void OnSetDirty()
        {
            UnityEditor.EditorUtility.SetDirty(target);
            UnityEditor.EditorUtility.SetDirty(this);
            if (EditorApplication.isPlaying == false)
            {
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }
        } 

        FOVConfig mConfig;
        CurveConfigUI mCurveUI;
    }
}

#endif