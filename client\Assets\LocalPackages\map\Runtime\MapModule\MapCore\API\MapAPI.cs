﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */

using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace TFW.Map
{
    public class ViewportUpdateSetting
    {
        public float cameraRotationThreshold = 25;
        public float cameraHeightThreshold = 10.0f;
        public float viewportWidth = 100;
        public float viewportHeight = 300;
    }

    //暴露给lua的地图接口
    public partial class Map
    {
        //创建一个使用格子管理对象的地图层
        public GridModelLayer CreateGridModelLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, GridType gridType, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var layerData = new config.GridModelLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, gridType, null);
            var layer = new GridModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            return layer;
        }

        public ComplexGridModelLayer CreateComplexGridModelLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, GridType gridType, DecorationObjectPlacementSetting objectPlacementSetting, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            var layerData = new config.ComplexGridModelLayerData(layerID, name, new Vector3(ox, 0, oz), new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, gridType, null, true, new Vector3(0, 0, 0), new Vector2(0, 0), new ObjectTagSetting[0], objectPlacementSetting, new Rect(ox, oz, layerWidth, layerHeight), true, false);
            var layer = new ComplexGridModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

            return layer;
        }

        public BlendTerrainLayer CreateBlendTerrainLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.BlendTerrainLayerData(layerID, name, offset, new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, null, true, false, "_MainTex", "", false, null, false, false, true, Vector3.zero);
            var layer = new BlendTerrainLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

#if UNITY_EDITOR
            Selection.activeObject = layer.layerView.root;
#endif

            return layer;
        }

        public VaryingTileSizeTerrainLayer CreateVaryingTileSizeTerrainLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.VaryingTileSizeTerrainLayerData(layerID, name, offset, new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, null, null, true, Vector3.zero);
            var layer = new VaryingTileSizeTerrainLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

#if UNITY_EDITOR
            Selection.activeObject = layer.layerView.root;
#endif
            return layer;
        }

        public SplitFogLayer CreateSplitFogLayer(string name, float layerWidth, float layerHeight, float tileWidth, float tileHeight, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - layerWidth) * 0.5f;
            var oz = (mapHeight - layerHeight) * 0.5f;
            int cols = Mathf.CeilToInt(layerWidth / tileWidth);
            int rows = Mathf.CeilToInt(layerHeight / tileHeight);
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.SplitFogLayerData(layerID, name, offset, new config.MapLayerLODConfig(), rows, cols, tileWidth, tileHeight, null, null, 0, "", "", "_MaskTex");
            var layer = new SplitFogLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

#if UNITY_EDITOR
            Selection.activeObject = layer.layerView.root;
#endif

            return layer;
        }

        //创建一个使用四叉树管理对象的地图层
        public ModelLayer CreateModelLayer(string name, float width, float height, bool addToMap = true, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - width) * 0.5f;
            var oz = (mapHeight - height) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.ModelLayerData(layerID, name, offset, new config.MapLayerLODConfig(), null, width, height, null);
            var layer = new ModelLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            if (addToMap)
            {
                AddMapLayer(layer);
#if UNITY_EDITOR
                Selection.activeObject = layer.layerView.root;
#endif
            }

            return layer;
        }

        //创建圆形的地图边界层
        public ModelLayer CreateCircleBorderLayer(string name, float width, float height, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - width) * 0.5f;
            var oz = (mapHeight - height) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.CircleBorderLayerData(layerID, name, offset, new config.MapLayerLODConfig(), width, height, null, 0);
            var layer = new CircleBorderLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

#if UNITY_EDITOR
            Selection.activeObject = layer.layerView.root;
#endif

            return layer;
        }

        //创建铁轨层
        public RailwayLayer CreateRailwayLayer(string name, float width, float height, int railCount, float railTotalLength, float railPrefabLength, float railWidth, Vector3 center, int layerID = 0)
        {
            if (layerID == 0)
            {
                layerID = Map.currentMap.nextCustomObjectID;
            }
            var ox = (mapWidth - width) * 0.5f;
            var oz = (mapHeight - height) * 0.5f;
            var offset = new Vector3(ox, 0, oz);
            var layerData = new config.RailwayLayerData(layerID, name, offset, new config.MapLayerLODConfig(), new config.ModelLODGroupManager(), width, height, null, railCount, railWidth, railTotalLength, center, railPrefabLength);
            var layer = new RailwayLayer(Map.currentMap);
            layer.Load(layerData, null, false);
            AddMapLayer(layer);

#if UNITY_EDITOR
            Selection.activeObject = layer.layerView.root;
#endif

            return layer;
        }

        //根据地图层id来找到地图层
        public MapLayerBase GetMapLayerByID(int layerID)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] != null && mMapLayers[i].id == layerID)
                {
                    return mMapLayers[i];
                }
            }
            return null;
        }

        //根据索引查找地图层
        public MapLayerBase GetMapLayerByIndex(int index)
        {
            if (index >= 0 && index < mMapLayers.Count)
            {
                return mMapLayers[index];
            }
            return null;
        }

        //根据名称查找地图层
        public MapLayerBase GetMapLayer(string name)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] != null && mMapLayers[i].name == name)
                {
                    return mMapLayers[i];
                }
            }
            return null;
        }

        public MapLayerBase GetMapLayerOfType(System.Type type)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] != null && mMapLayers[i].GetType() == type)
                {
                    return mMapLayers[i];
                }
            }
            return null;
        }

        public T GetMapLayer<T>() where T : MapLayerBase
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] != null && mMapLayers[i].GetType() == typeof(T))
                {
                    return mMapLayers[i] as T;
                }
            }
            return null;
        }

        public List<T> GetMapLayers<T>() where T : MapLayerBase
        {
            List<T> mapLayers = new List<T>();
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                if (mMapLayers[i] != null && mMapLayers[i].GetType() == typeof(T))
                {
                    mapLayers.Add(mMapLayers[i] as T);
                }
            }
            return mapLayers;
        }

        //刷新当前视野下可见的物体,主要是在初始化地图后显示当前范围中的前景物体,例如山,水等,地图上服务器生成的物体一般用不到这个函数
        public void RefreshObjectsInViewport()
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                mMapLayers[i].RefreshObjectsInViewport();
            }
        }

        //根据地图对象的id来返回其game object,这个id不是服务器定义的物体对象id,而是客户端本地管理的id,从map中获取的
        public GameObject GetMapObjectGameObject(int id)
        {
            var view = GetMapObjectView(id);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        void UpdateViewport()
        {
            if (isEditorMode == false)
            {
                var camera = mView.camera;
                var pos = camera.transform.position;
                if (!Utils.Approximately(mLastUpdateCameraPos, pos))
                {
                    mLastUpdateCameraPos = pos;
                    var cameraRotation = camera.transform.rotation.eulerAngles;
                    var cameraHeight = MapCameraMgr.currentCameraHeight;
                    if (cameraRotation.x < mViewportUpdateSetting.cameraRotationThreshold && cameraHeight < mViewportUpdateSetting.cameraHeightThreshold)
                    {
                        var pos2 = Utils.ToVector2(pos);
                        float width = mViewportUpdateSetting.viewportWidth;
                        float height = mViewportUpdateSetting.viewportHeight;
                        viewport = new Rect(pos2.x - width * 0.5f, pos2.y - 5, width, height);
                        originalViewport = viewport;
                    }
                    else
                    {
                        //把viewport往y方向扩大一圈,应对很高的山被裁剪的情况
                        viewport = GetViewportRect(camera.firstCamera, camera.fieldOfView, camera.transform.position);
                        originalViewport = viewport;
                        var minY = viewport.yMin - data.backExtendedSize;
                        var minX = viewport.xMin - data.backExtendedSize;
                        var maxX = viewport.xMax + data.backExtendedSize;
                        var maxY = viewport.yMax + data.backExtendedSize;
                        //由于KVK底图地表下沉，为避免地表衔接不及时Y方向扩大一些
                        if(currentMap.name.Equals("KVK"))
                        {
                            viewport = new Rect(minX, minY, maxX - minX, (maxY - minY) * 1.1f);
                        }
                        else
                        {
                            viewport = new Rect(minX, minY, maxX - minX, maxY - minY);
                        }
                    }
                }
            }
        }

        //更新地图,主要是删除物体和更新加载队列
        public void Update()
        {
            UpdateViewport();

            mLoadingTaskManager.Update();
            mObjectManager.Update();

            CrossfadeModel.UpdateAll(MapCameraMgr.currentCameraHeight);
            mCullManager.UpdateViewport(originalViewport);

#if UNITY_EDITOR
            if (view.viewCenter != null)
            {
                view.viewCenter.GetComponent<DrawBounds>().bounds = Utils.RectToBounds(originalViewport);
            }
#endif
        }

        public void LateUpdate()
        {
            mFrameActionManager.Update();
            mDetailSprites?.Update();
        }

        public void SetLookAtPosition(Vector3 newViewCenter)
        {
            var offset = newViewCenter - viewCenter;
            mData.MoveViewport(offset);
            mView.MoveViewport(offset, false);
        }

        //移动地图的视野,刷新地表的物体
        //offset: 针对上一帧移动的距离
        //moveCamera: 是否移动相机
        public void SetEditorLookAtPosition(Vector3 newViewCenter, Vector2 viewportSize)
        {
            var offset = newViewCenter - viewCenter;
            mData.MoveViewport(offset);
            mView.MoveViewport(offset, false);

            viewport = new Rect(newViewCenter.x - viewportSize.x * 0.5f, newViewCenter.z - viewportSize.y * 0.5f, viewportSize.x, viewportSize.y);
            originalViewport = viewport;
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                mMapLayers[i].UpdateViewport(viewport, 0);
            }
        }

        //从屏幕坐标转换到xz平面上的世界坐标
        public Vector3 FromScreenToWorldPosition(Vector2 screenPos, float planeHeight = 0)
        {
            return Utils.FromScreenToWorldPosition(screenPos, view.camera.firstCamera, planeHeight);
        }
        //从屏幕中心点转换到xz平面上的世界坐标
        public Vector3 FromScreenCenterToWorldPosition()
        {
            return FromScreenToWorldPosition(new Vector2(camera.firstCamera.pixelWidth * 0.5f, camera.firstCamera.pixelHeight * 0.5f));
        }

        //获取屏幕视野上对应的世界坐标下的包围框
        //widthExtension: x方向上的缩放系数
        //heightExtension: z方向上的缩放系数
        public Vector2 GetViewportSize(float widthExtension, float heightExtension)
        {
            var rect = GetViewportRect(camera.firstCamera, camera.fieldOfView, camera.transform.position);
            return new Vector2(rect.width * widthExtension, rect.height * heightExtension);
        }

        //获取某个高度下相机的视野大小
        public Vector2 CalculateViewportSizeFromHeight(float height)
        {
            float fov = GetCameraFOVAtHeight(height);
            var pos = camera.transform.position;
            pos.y = height;
            var rect = GetViewportRect(camera.firstCamera, fov, pos);
            return new Vector2(rect.width, rect.height);
        }

        public Rect GetViewportRect()
        {
            return GetViewportRect(camera.firstCamera, camera.fieldOfView, camera.transform.position);
        }

        public bool GetRayPlaneIntersection(ref Plane plane, Ray ray, ref Vector3 intersection)
        {
            float enter;
            if (!plane.Raycast(ray, out enter))
            {
                intersection = Vector3.zero;
                return false;
            }

            // 下面是获取t的公式
            // 注意，你需要先判断射线与平面是否平行，如果平面和射线平行，那么平面法线和射线方向的点积为0，即除数为0.
            //float t = (Vector3.Dot(normal, planePoint) - Vector3.Dot(normal, ray.origin)) / Vector3.Dot(normal, ray.direction.normalized);
            if (enter >= 0)
            {
                intersection = ray.origin + enter * ray.direction.normalized;
                return true;
            }
            else
            {
                intersection = Vector3.zero;
                return false;
            }
        }

        public Rect GetViewportRect(Camera camera, float fov, Vector3 cameraPos)
        {
#if false
            // 根据相机位置直接算出区域范围
            // 因为是0平面，可以直接简化计算
            float camRotateX = camera.transform.rotation.eulerAngles.x;
            float halfFovDeg = fov * 0.5f;

            // top
            float topRad = (90 - camRotateX + halfFovDeg) * Mathf.Deg2Rad;
            var offset = Mathf.Tan(topRad) * cameraPos.y;
            float top = offset + cameraPos.z;

            // bottom
            float bottomRad = (90 - camRotateX - halfFovDeg) * Mathf.Deg2Rad;
            float bottom = Mathf.Tan(bottomRad) * cameraPos.y + cameraPos.z;

            var horizontalFov = 0.5f * Camera.VerticalToHorizontalFieldOfView(fov, camera.aspect) * Mathf.Deg2Rad;
            var halfWidth = Mathf.Tan(horizontalFov) * new Vector3(0.0f, cameraPos.y, offset).magnitude;
            float left = cameraPos.x - halfWidth;
            float right = cameraPos.x + halfWidth;

            return Rect.MinMaxRect(left, bottom, right, top);
#else
            float curFOV = camera.fieldOfView;
            Vector3 curPos = camera.transform.position;

            camera.fieldOfView = fov;
            camera.transform.position = cameraPos;
            Ray rayBL = camera.ViewportPointToRay(new Vector3(0, 0, 1));     // bottom left
            Ray rayBR = camera.ViewportPointToRay(new Vector3(1, 0, 1));     // bottom right
            Ray rayTL = camera.ViewportPointToRay(new Vector3(0, 1, 1));     // top left
            Ray rayTR = camera.ViewportPointToRay(new Vector3(1, 1, 1));     // top right

            Plane plane = new Plane(Vector3.up, 0);

            Vector3 bl = Vector3.zero, br = Vector3.zero, tl = Vector3.zero, tr = Vector3.zero;
            GetRayPlaneIntersection(ref plane, rayBL, ref bl);
            GetRayPlaneIntersection(ref plane, rayBR, ref br);
            GetRayPlaneIntersection(ref plane, rayTR, ref tr);
            GetRayPlaneIntersection(ref plane, rayTL, ref tl);

            float width = 0;
            float height = 0;
            var rotation = camera.transform.rotation;
            Rect rect;
            if (rotation.y != 0)
            {
                float minX = Mathf.Min(bl.x, br.x, tr.x, tl.x);
                float minY = Mathf.Min(bl.z, br.z, tr.z, tl.z);
                float maxX = Mathf.Max(bl.x, br.x, tr.x, tl.x);
                float maxY = Mathf.Max(bl.z, br.z, tr.z, tl.z);

                width = maxX - minX;
                height = maxY - minY;
                rect = new Rect(minX, minY, width, height);
            }
            else
            {
                width = tr.x - tl.x;
                height = tr.z - br.z;
                rect = new Rect(tl.x, bl.z, width, height);
            }
            
            camera.fieldOfView = curFOV;
            camera.transform.position = curPos;

            return rect;
#endif
        }

        //从相机得到当前的view center
        public Vector3 GetViewCenterFromCamera(Vector3 forward, Vector3 cameraPos)
        {
            float t = -cameraPos.y / forward.y;
            var intersection = cameraPos + forward * t;
            return intersection;
        }

        public Vector3 CalculateCameraPositionFromLookAtPosition(float posX, float posZ, float cameraHeight, float posY = 0)
        {
            var dir = camera.transform.forward;
            float xRot = camera.transform.eulerAngles.x;
            float distance = cameraHeight / Mathf.Sin(xRot * Mathf.Deg2Rad);
            Vector3 cameraPos = new Vector3(posX, posY, posZ) - dir * distance;
            return cameraPos;
        }

        public Vector3 CalculateCameraPositionFromLookAtPositionWithRotation(float posX, float posZ, float cameraHeight, float xRot, float yRot)
        {
            var dir = Quaternion.Euler(xRot, yRot, 0) * Vector3.forward;
            float distance = cameraHeight / Mathf.Sin(xRot * Mathf.Deg2Rad);
            Vector3 cameraPos = new Vector3(posX, 0, posZ) - dir * distance;
            return cameraPos;
        }

        public void UninitNavigation()
        {
            Nav.NavigationManager.UninitNavigation();
        }

        //添加一个模型的模板,grid model layer和model layer内部的地图对象都使用model template来配置
        //prefabPath: prefab的路径
        //bounds: 模型的包围框大小,四叉树会根据模型的包围框大小来判断模型是否在视野中
        //id: 模板的id,为0的话会使用一个默认的id
        public ModelTemplate AddModelTemplate(string prefabPath, Map map, Rect bounds, bool isTileModelTemplate, List<List<ModelTemplate>> childrenModelTemplates, List<List<ChildPrefabTransform>> childPrefabTransforms, ModelTemplateLODInfo lodInfo, int id = 0, bool preload = false)
        {
            var temp = FindModelTemplate(prefabPath);
            if (string.IsNullOrEmpty(prefabPath) || temp == null)
            {
                if (id == 0)
                {
                    id = map.nextCustomObjectID;
                }
                temp = new ModelTemplate(id, map, prefabPath, bounds, isTileModelTemplate, childrenModelTemplates, childPrefabTransforms, lodInfo, preload);
                data.AddModelTemplate(temp);
                return temp;
            }
            return null;
        }

        public bool IsIntersectedWithObstacles(float centerX, float centerZ, float radius, bool multithread)
        {
            bool hit = data.localObstacleManager.IsIntersectedWithObstacles(centerX, centerZ, radius, multithread);
            if (!hit)
            {
                hit = data.globalObstacleManager.IsIntersectedWithObstacles(centerX, centerZ, radius);
            }
            return hit;
        }

        public float GetCameraFOVAtHeight(float cameraHeight)
        {
            return MapCameraMgr.cameraSetting.GetFOVAtHeight(cameraHeight);
        }

        public void FindPath(Vector3 start, Vector3 end, List<Vector3> path)
        {
            var localStart = start - mMapSetting.origin;
            var localEnd = end - mMapSetting.origin;
            Nav.NavigationManager.FindPath(localStart, localEnd, path, mMapSetting.origin);
        }

        public int GetRegionID(Vector3 pos)
        {
            if (data.gridRegionSetting != null)
            {
                return data.gridRegionSetting.GetRegionID(pos);
            }
            return 0;
        }

        public void GetIntersectedRegions(Vector3 center, float radius, List<int> intersectedRegionIDs)
        {
            data.gridRegionSetting?.GetIntersectedRegions(center, radius, intersectedRegionIDs);
        }

        public bool IsObstacleGrid(int x, int y)
        {
            return data.globalObstacleManager.IsCollidedWithObstacle(x, y);
        }

        public float GetTerrainHeightAtPos(float x, float z)
        {
            if (mData.useTerrainHeight)
            {
                if (mBlendTerrainLayer != null)
                {
                    return mBlendTerrainLayer.GetHeightAtPos(x, z);
                }
            }
            return 0;
        }

        public void SetActive(bool active)
        {
            for (int i = 0; i < mMapLayers.Count; ++i)
            {
                mMapLayers[i].active = active;
            }
            mData.SetActive(active);
        }

        //地图使用的相机
        public MapCameras camera { get { if (mView == null) return null; return mView.camera; } }
        //地图视野的中心点
        public Vector3 viewCenter 
        { 
            get 
            {
                if (mData == null)
                    return Vector3.zero;
                return mData.viewCenter; 
            } 
        }
        //地图的根game object
        public Transform root
        {
            get
            {
                if (mView.root == null)
                {
                    return null;
                }
                return mView.root.transform;
            }
        }
        //地图的x方向的长度
        public float mapWidth { get {
                if (mData == null)
                    return 7200;
                return mData.mapWidth; 
            } }
        //地图的z方向的长度
        public float mapHeight { get {
                if (mData == null)
                    return 7200;
                return mData.mapHeight; } }
        public virtual bool updateViewport { get { return true; } }
        Vector3 mLastUpdateCameraPos;
    }
}
