﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace TFW.Map
{
    //将所有用到的tile的贴图打到一张或几张atlas贴图上
    public partial class GroundTileAtlasSettingUI : Editor
    {
        public void PackUsedTileTextures()
        {
            var mapconfigFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(mapconfigFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            if (Application.isPlaying)
            {
                MapModule.Init(mapConfig);
            }
            else
            {
                MapModule.InitLoad(mapConfig);
            }

            var setting = target as GroundTileAtlasSetting;
            if (setting.material == null)
            {
                EditorUtility.DisplayDialog("Error", "Select material first!", "OK");
                return;
            }
            var folders = GetUsedTileFolders();
            mBorderSize = setting.borderSize;
            PackAllTilesToAtlas(folders, setting.atlasTexturePropertyName, setting.borderSize, setting.rgbTextureAlphaIsOne, setting.specialTextureAtlasSettings);
        }

        //获取所有使用了的tile prefab的目录,准备打包
        List<FolderEntry> GetUsedTileFolders()
        {
            var setting = target as GroundTileAtlasSetting;
            List<FolderEntry> folders = new List<FolderEntry>();
            //指定的目录
            int n = setting.groundTileFolders.Length;
            for (int i = 0; i < n; ++i)
            {
                string folderPath = AssetDatabase.GetAssetPath(setting.groundTileFolders[i]);
                if (Directory.Exists(folderPath))
                {
                    folders.Add(new FolderEntry { path = folderPath, tag = setting.tags[i], treatAlphaAsZero = setting.treatAlphaAsZeros[i] });
                }
            }
            return folders;
        }

        List<Texture2D> CreateTextureList(List<TextureSetting> textureSettings)
        {
            List<Texture2D> textures = new List<Texture2D>(textureSettings.Count);
            for (int i = 0; i < textureSettings.Count; ++i)
            {
                textures.Add(textureSettings[i].texture);
            }
            return textures;
        }

        List<bool> CreateTreatAlphaAsZeroList(List<TextureSetting> textureSettings)
        {
            List<bool> treatAlphaAsZero = new List<bool>(textureSettings.Count);
            for (int i = 0; i < textureSettings.Count; ++i)
            {
                treatAlphaAsZero.Add(textureSettings[i].treatAlphaAsZero);
            }
            return treatAlphaAsZero;
        }

        //将指定目录的所有tile prefab某个贴图打包到atlas
        void PackAllTilesToAtlas(List<FolderEntry> tilePrefabFolders, string atlasTexturePropertyName, int borderSize, bool rgbTextureAlphaIsOne, SpecialTextureAtlasSetting[] specialTextureSettings)
        {
            //find all textures in folders
            Dictionary<string, List<TextureSetting>> texturesSortedByTag = FindAllTexturesInFolder(tilePrefabFolders, atlasTexturePropertyName, specialTextureSettings);

            mPackers = new List<TexturePackerEntry>();
            mAtlasUVMappings = new Dictionary<string, Vector2[]>();
            bool packOK = true;
            foreach (var p in texturesSortedByTag)
            {
                mCurrentEntry = new TexturePackerEntry();
                mCurrentEntry.packer = new TexturePacker();
                var textures = CreateTextureList(p.Value);
                var treatAlphaAsZero = CreateTreatAlphaAsZeroList(p.Value);
                packOK &= mCurrentEntry.packer.Pack(TexturePackerStrategyType.EqualRects, OnPackTexturesSucceed, textures, treatAlphaAsZero, false, 2048, borderSize, rgbTextureAlphaIsOne);
                mPackers.Add(mCurrentEntry);
            }
            if (packOK)
            {
                OnPackFinish();
                ExportGroundTileUVAtlasInfo();
            }
            else
            {
                EditorUtility.DisplayDialog("Erorr", "Create Texture Atlas Failed!", "OK");
            }
        }

        bool IsSpecialTexture(string path, SpecialTextureAtlasSetting[] specialTextureSettings, out string specialTag)
        {
            specialTag = null;
            for (int i = 0; i < specialTextureSettings.Length; ++i)
            {
                if (specialTextureSettings[i].texturePath == path)
                {
                    specialTag = specialTextureSettings[i].tag;
                    return true;
                }
            }
            return false;
        }
        
        class TextureSetting
        {
            public Texture2D texture;
            public bool treatAlphaAsZero = false;
        }

        Dictionary<string, List<TextureSetting>> FindAllTexturesInFolder(List<FolderEntry> folders, string atlasTexturePropertyName, SpecialTextureAtlasSetting[] specialTextureSetting)
        {
            Dictionary<string, List<TextureSetting>> allTextures = new Dictionary<string, List<TextureSetting>>();

            for (int i = 0; i < folders.Count; ++i)
            {
                var enumerator = Directory.EnumerateFiles(folders[i].path);
                foreach (var path in enumerator)
                {
                    var validPath = path.Replace("\\", "/");
                    if (validPath.EndsWith("tga", true, null) && validPath.IndexOf("_00_") == -1)
                    {
                        string specialTag;
                        bool isSpecial = IsSpecialTexture(validPath, specialTextureSetting, out specialTag);
                        var tag = folders[i].tag;
                        bool treatAlphaAsZero = folders[i].treatAlphaAsZero;
                        if (string.IsNullOrEmpty(tag))
                        {
                            tag = "";
                        }
                        if (isSpecial)
                        {
                            tag = specialTag;
                        }
                        List<TextureSetting> texturesOfTag;
                        allTextures.TryGetValue(tag, out texturesOfTag);
                        if (texturesOfTag == null)
                        {
                            texturesOfTag = new List<TextureSetting>();
                            allTextures[tag] = texturesOfTag;
                        }

                        string nameWithoutExt = Utils.RemoveExtension(validPath);
                        if (nameWithoutExt.EndsWith(atlasTexturePropertyName, true, null))
                        {
                            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(validPath);
                            Debug.Assert(texture != null);
                            texturesOfTag.Add(new TextureSetting { texture = texture, treatAlphaAsZero = treatAlphaAsZero });
                        }
                    }
                }
            }

            return allTextures;
        }

        bool OnPackTexturesSucceed(List<TexturePacker.PackInfo> packInfos, List<TexturePackerItem> textures)
        {
            mCurrentEntry.packInfo = packInfos;
            mCurrentEntry.packItems = textures;
            return false;
        }

        void OnPackFinish()
        {
            var setting = target as GroundTileAtlasSetting;
            int globalIdx = 0;
            List<Material> atlasMaterials = new List<Material>();
            List<Texture2D> atlasTextures = new List<Texture2D>();

            for (int p = 0; p < mPackers.Count; ++p)
            {
                var entry = mPackers[p];
                if (globalIdx == 0)
                {
                    //create atlas texture
                    var atlasTexture0 = CreateAtlasTexture(entry.packInfo[0].packedTexture, 0);
                    setting.material.SetTexture(setting.atlasTexturePropertyName, atlasTexture0);
                    EditorUtility.SetDirty(atlasTexture0);
                    AssetDatabase.SaveAssets();
                    atlasTextures.Add(atlasTexture0);
                    atlasMaterials.Add(setting.material);

                    if (entry.packInfo.Count > 1)
                    {
                        for (int i = 1; i < entry.packInfo.Count; ++i)
                        {
                            var atlasTexture = CreateAtlasTexture(entry.packInfo[i].packedTexture, i + globalIdx);
                            var atlasMaterial = CreateAtlasMaterial(i + globalIdx, atlasTexture);
                            Debug.Assert(atlasMaterial != null);
                            atlasTextures.Add(atlasTexture);
                            atlasMaterials.Add(atlasMaterial);
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < entry.packInfo.Count; ++i)
                    {
                        var atlasTexture = CreateAtlasTexture(entry.packInfo[i].packedTexture, i + globalIdx);
                        var atlasMaterial = CreateAtlasMaterial(i + globalIdx, atlasTexture);
                        Debug.Assert(atlasMaterial != null);
                        atlasTextures.Add(atlasTexture);
                        atlasMaterials.Add(atlasMaterial);
                    }
                }

                for (int i = 0; i < entry.packItems.Count; ++i)
                {
                    EditorUtility.DisplayProgressBar("Creating Texture Atlas...",
                        $"Processing[{i + 1}/{entry.packItems.Count}]: {entry.packItems[i].texture.name}",
                        1f * (i + 1) / entry.packItems.Count);

                    string textureFilePath = AssetDatabase.GetAssetPath(entry.packItems[i].texture);
                    Debug.Assert(!string.IsNullOrEmpty(textureFilePath));
                    int textureAtlasIndex;
                    int indexInAtlas;
                    Vector2[] uvInAtlas = entry.packer.GetUV(textureFilePath, out textureAtlasIndex, out indexInAtlas);
                    
                    int globalTextureAtlasIndex = textureAtlasIndex + globalIdx;
                    string prefabPath = GetAssetPath(textureFilePath, setting.atlasTexturePropertyName, "prefab");
                    mAtlasUVMappings.Add(prefabPath, uvInAtlas);

                    //modify mesh uv
                    string meshPath = GetAssetPath(textureFilePath, setting.atlasTexturePropertyName, "asset");
                    var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                    if (setting.useTileBlock)
                    {
                        //mesh的uv还是保持0到1之间,通过生成一张uv transform贴图在shader中将0-1的uv映射成atlas中的uv
                        List<Vector2> uvExt = new List<Vector2>()
                        {
                            new Vector2(0, 0),
                            new Vector2(0, 1),
                            new Vector2(1, 1),
                            new Vector2(1, 0),
                        };

                        mesh.SetUVs(0, uvExt);

                        float minX = Mathf.Min(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
                        float minY = Mathf.Min(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);
                        float maxX = Mathf.Max(uvInAtlas[0].x, uvInAtlas[1].x, uvInAtlas[2].x, uvInAtlas[3].x);
                        float maxY = Mathf.Max(uvInAtlas[0].y, uvInAtlas[1].y, uvInAtlas[2].y, uvInAtlas[3].y);

                        List<Vector4> uv2 = new List<Vector4>(4);
                        for (int k = 0; k < 4; ++k)
                        {
                            uv2.Add(new Vector4(minX, minY, maxX, maxY));
                        }

                        mesh.SetUVs(1, uv2);
                    }
                    else
                    {
                        mesh.uv = uvInAtlas;
                    }
                    EditorUtility.SetDirty(mesh);

                    //modify material
                    atlasMaterials[globalTextureAtlasIndex].SetTexture(setting.atlasTexturePropertyName, atlasTextures[globalTextureAtlasIndex]);
                    EditorUtility.SetDirty(atlasMaterials[globalTextureAtlasIndex]);

                    //modify prefab
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    prefab.GetComponent<MeshRenderer>().sharedMaterial = atlasMaterials[globalTextureAtlasIndex];
                    PrefabUtility.SavePrefabAsset(prefab);
                }

                for (int i = 0; i < entry.packInfo.Count; ++i)
                {
                    Utils.DestroyObject(entry.packInfo[i].packedTexture);
                }
                 
                globalIdx += entry.packInfo.Count;

                EditorUtility.ClearProgressBar();
            }

            AssetDatabase.SaveAssets();
        }

        Material CreateAtlasMaterial(int index, Texture2D atlasTexture)
        {
            var setting = target as GroundTileAtlasSetting;
            var mtl = Object.Instantiate<Material>(setting.material);
            mtl.SetTexture(setting.atlasTexturePropertyName, atlasTexture);
            string assetPath = AssetDatabase.GetAssetPath(setting.material);
            string mtlPath = Utils.RemoveExtension(assetPath) + $"_{index}.mat";
            AssetDatabase.CreateAsset(mtl, mtlPath);
            return AssetDatabase.LoadAssetAtPath<Material>(mtlPath);
        }

        Texture2D CreateAtlasTexture(Texture2D runtimeAtlasTexture, int index)
        {
            string path = $"{MapModule.groundTileAtlasInfoDir}/all_ground_tile_atlas_{index}.tga";
            if (!Directory.Exists(MapModule.groundTileAtlasInfoDir))
            {
                Directory.CreateDirectory(MapModule.groundTileAtlasInfoDir);
            }

            var bytes = runtimeAtlasTexture.EncodeToTGA();
            File.WriteAllBytes(path, bytes);
            AssetDatabase.Refresh();
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
            return texture;
        }

        string GetAssetPath(string textureFilePath, string textureAtlasPropertyName, string ext)
        {
            int index = textureFilePath.IndexOf($"_{textureAtlasPropertyName}");
            var path = textureFilePath.Remove(index);
            path = path + $".{ext}";
            return path;
        }

        void SelectOutputFolder()
        {
            string configFilePath = Utils.TryToGetValidConfigFilePath();
            var mapConfig = MapConfig.CreateFromFile(configFilePath, new EditorResourceMgr(), new DummyTouchManagerImpl());
            EditorUtils.SelectFolder(mapConfig.groundTileAtlasInfoDir);
        }

        //生成tile prefab与atlas uv的对应信息
        void ExportGroundTileUVAtlasInfo()
        {
            string filePath = $"{MapModule.groundTileAtlasInfoDir}/{MapCoreDef.MAP_GROUND_TILE_ATLAS_INFO_FILE_NAME}";

            MemoryStream stream = new MemoryStream();
            BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.GroundTileAtlasInfoVersion);

            writer.Write(mAtlasUVMappings.Count);
            foreach (var p in mAtlasUVMappings)
            {
                string prefabFilePath = p.Key;
                Vector2[] uv = p.Value;
                Utils.WriteString(writer, prefabFilePath);
                Utils.WriteVector2Array(writer, uv);
            }

            var data = stream.ToArray();
            File.WriteAllBytes(filePath, data);
            writer.Close();

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        }

        class TexturePackerEntry
        {
            public TexturePacker packer;
            public List<TexturePacker.PackInfo> packInfo;
            public List<TexturePackerItem> packItems;
        }

        class FolderEntry
        {
            public string path;
            public string tag;
            public bool treatAlphaAsZero = false;
        }

        TexturePackerEntry mCurrentEntry;
        List<TexturePackerEntry> mPackers;
        Dictionary<string, Vector2[]> mAtlasUVMappings;
        int mBorderSize;
    }
}

#endif