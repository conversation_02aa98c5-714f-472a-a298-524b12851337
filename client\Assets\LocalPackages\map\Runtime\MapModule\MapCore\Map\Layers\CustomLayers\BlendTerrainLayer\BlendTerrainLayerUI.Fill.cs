﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class BlendTerrainLayerUI : Editor
    {
        enum RectangleFillCursorState
        {
            Idle,
            SelectMin,
        }

        void DrawFillInspector()
        {
            mLogic.eraseFill = EditorGUILayout.ToggleLeft(new GUIContent("Erase", "是否是删除tile操作"), mLogic.eraseFill);
            mLogic.rectangleFill = EditorGUILayout.ToggleLeft(new GUIContent("Rectangle", "填充矩形范围内的tile"), mLogic.rectangleFill);
            mLogic.randomPattern = EditorGUILayout.ToggleLeft(new GUIContent("Random Pattern", "随机填充还是填充15号地块"), mLogic.randomPattern);

            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            prefabManager.Draw(PrefabGroupDisplayFlag.ShowColor | PrefabGroupDisplayFlag.ShowDecoration);
        }

        void DrawFillToolSceneGUI(Event e)
        {
            var map = Map.currentMap;
            var screenPos = EditorUtils.ConvertScreenPosition(e.mousePosition, map.camera.firstCamera);
            var worldPos = Map.currentMap.FromScreenToWorldPosition(screenPos);

            if (mLogic.rectangleFill)
            {
                if (e.type == EventType.MouseDown && e.button == 0)
                {
                    if (mRectangleFillState == RectangleFillCursorState.Idle)
                    {
                        mRectangleFillState = RectangleFillCursorState.SelectMin;
                        mMinPos = worldPos;
                        mMaxPos = worldPos;
                    }
                }
                else if (e.type == EventType.MouseDrag && e.button == 0)
                {
                    if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                    {
                        mMaxPos = worldPos;
                    }
                }
                else if (e.type == EventType.MouseUp && e.button == 0)
                {
                    if (mRectangleFillState == RectangleFillCursorState.SelectMin)
                    {
                        SetTilesInRange(mLogic.randomPattern);
                        mRectangleFillState = RectangleFillCursorState.Idle;
                    }
                }

                if (mRectangleFillState != RectangleFillCursorState.Idle)
                {
                    Vector3 min = Vector3.Min(mMinPos, mMaxPos);
                    Vector3 max = Vector3.Max(mMinPos, mMaxPos);
                    Handles.DrawWireCube((min + max) * 0.5f, max - min);
                }
            }
            else
            {
                if (e.type == EventType.MouseDown && e.button == 0)
                {
                    if (mPolygonVertices.Count == 0)
                    {
                        mPolygonVertices.Add(worldPos);
                    }
                    mPolygonVertices.Add(worldPos);
                    mDiplayPolygonVertices = mPolygonVertices.ToArray();
                }
                else if (e.type == EventType.MouseMove && e.button == 0)
                {
                    if (mPolygonVertices.Count > 0)
                    {
                        mPolygonVertices[mPolygonVertices.Count - 1] = worldPos;
                        mDiplayPolygonVertices[mPolygonVertices.Count - 1] = worldPos;
                    }
                }
                else if (e.type == EventType.MouseUp && e.button == 1)
                {
                    if (mPolygonVertices.Count > 3)
                    {
                        mPolygonVertices.RemoveAt(mPolygonVertices.Count - 1);
                        FillPolygon(mLogic.randomPattern);
                    }
                    mPolygonVertices.Clear();
                    mDiplayPolygonVertices = null;
                }

                if (mPolygonVertices.Count > 0)
                {
                    Handles.DrawPolyLine(mDiplayPolygonVertices);
                }
            }
            
            SceneView.RepaintAll();
            HandleUtility.AddDefaultControl(0);
        }

        void SetTilesInRange(bool fillRandomPattern)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            int prefabGroupIndex = prefabManager.selectedGroupIndex;
            if (prefabGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabGroupIndex);
                if (group.isValid)
                {
                    Vector3 min = Vector3.Min(mMinPos, mMaxPos);
                    Vector3 max = Vector3.Max(mMinPos, mMaxPos);

                    var layer = mLogic.layer;
                    var layerData = layer.layerData;
                    var minCoord = layerData.FromWorldPositionToCoordinate(min);
                    var maxCoord = layerData.FromWorldPositionToCoordinate(max);

                    int minX = Mathf.Clamp(minCoord.x, 0, layerData.horizontalTileCount - 1);
                    int minY = Mathf.Clamp(minCoord.y, 0, layerData.verticalTileCount - 1);
                    int maxX = Mathf.Clamp(maxCoord.x, 0, layerData.horizontalTileCount - 1);
                    int maxY = Mathf.Clamp(maxCoord.y, 0, layerData.verticalTileCount - 1);
                    var act = new ActionChangeBlendTerrainLayerTiles(layer.id, Mathf.Max(0, minX - 1), Mathf.Max(0, minY - 1), maxX, maxY);
                    act.Begin();

                    for (int y = minCoord.y; y <= maxCoord.y; ++y)
                    {
                        for (int x = minCoord.x; x <= maxCoord.x; ++x)
                        {
                            var pos = layerData.FromCoordinateToWorldPosition(x, y);

                            if (mLogic.paintOneTile)
                            {
                                if (mLogic.eraseFill)
                                {
                                    layer.SetTile(x, y, 0, 0, 0);
                                }
                                else
                                {
                                    layer.PushTile(x, y, group.selectedIndex, group.groupID, 0, true, true, null, false);
                                }
                            }
                            else
                            {
                                if (fillRandomPattern && mLogic.eraseFill == false)
                                {
                                    if (Random.value < 0.5f)
                                    {
                                        layer.PushTile(pos, group.groupID);
                                    }
                                }
                                else
                                {
                                    if (mLogic.eraseFill)
                                    {
                                        layer.PopTile(pos);
                                    }
                                    else
                                    {
                                        layer.PushTile(pos, group.groupID);
                                    }
                                }
                            }
                        }
                    }

                    if (mLogic.paintOneTile == false)
                    {
                        if (fillRandomPattern && mLogic.eraseFill == false)
                        {
                            for (int y = minCoord.y; y <= maxCoord.y; ++y)
                            {
                                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                                {
                                    var tile = layer.GetTile(x, y);
                                    if (tile == null)
                                    {
                                        var pos = layerData.FromCoordinateToWorldPosition(x, y);
                                        layer.PushTile(pos, group.groupID);
                                    }
                                }
                            }
                        }
                    }

                    act.End();
                    ActionManager.instance.PushAction(act, true, false);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a prefab group", "OK");
            }
        }

        void FillPolygon(bool fillRandomPattern)
        {
            var editorMapData = Map.currentMap.data as EditorMapData;
            var prefabManager = editorMapData.editorTerrainPrefabManager;
            int prefabGroupIndex = prefabManager.selectedGroupIndex;
            if (prefabGroupIndex >= 0)
            {
                var group = prefabManager.GetGroupByIndex(prefabGroupIndex);
                if (group.isValid)
                {
                    var layer = mLogic.layer;
                    var layerData = layer.layerData;
                    var bounds = Utils.CreateBounds(mPolygonVertices);
                    var minCoord = layerData.FromWorldPositionToCoordinate(bounds.min);
                    var maxCoord = layerData.FromWorldPositionToCoordinate(bounds.max);

                    int minX = Mathf.Clamp(minCoord.x, 0, layerData.horizontalTileCount - 1);
                    int minY = Mathf.Clamp(minCoord.y, 0, layerData.verticalTileCount - 1);
                    int maxX = Mathf.Clamp(maxCoord.x, 0, layerData.horizontalTileCount - 1);
                    int maxY = Mathf.Clamp(maxCoord.y, 0, layerData.verticalTileCount - 1);
                    var act = new ActionChangeBlendTerrainLayerTiles(layer.id, minX, minY, maxX, maxY);
                    act.Begin();

                    Vector3[] polygon = mPolygonVertices.ToArray();

                    for (int y = minCoord.y; y <= maxCoord.y; ++y)
                    {
                        for (int x = minCoord.x; x <= maxCoord.x; ++x)
                        {
                            var pos = layerData.FromCoordinateToWorldPosition(x, y);
                            if (EditorUtils.PointInPolygon2D(pos, polygon))
                            {
                                if (mLogic.paintOneTile)
                                {
                                    if (mLogic.eraseFill)
                                    {
                                        layer.SetTile(x, y, 0, 0, 0);
                                    }
                                    else
                                    {
                                        layer.PushTile(x, y, group.selectedIndex, group.groupID, 0, true, true, null, false);
                                    }
                                }
                                else
                                {
                                    if (fillRandomPattern && mLogic.eraseFill == false)
                                    {
                                        if (Random.value < 0.5f)
                                        {
                                            layer.PushTile(pos, group.groupID);
                                        }
                                    }
                                    else
                                    {
                                        if (mLogic.eraseFill)
                                        {
                                            layer.PopTile(pos);
                                        }
                                        else
                                        {
                                            layer.PushTile(pos, group.groupID);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (mLogic.paintOneTile == false)
                    {
                        if (fillRandomPattern && mLogic.eraseFill == false)
                        {
                            for (int y = minCoord.y; y <= maxCoord.y; ++y)
                            {
                                for (int x = minCoord.x; x <= maxCoord.x; ++x)
                                {
                                    var tile = layer.GetTile(x, y);
                                    if (tile == null)
                                    {
                                        var pos = layerData.FromCoordinateToWorldPosition(x, y);
                                        if (EditorUtils.PointInPolygon2D(pos, polygon))
                                        {
                                            layer.PushTile(pos, group.groupID);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    act.End();
                    ActionManager.instance.PushAction(act, true, false);
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Select a prefab group", "OK");
            }
        }

        RectangleFillCursorState mRectangleFillState = RectangleFillCursorState.Idle;
        Vector3 mMinPos;
        Vector3 mMaxPos;
        List<Vector3> mPolygonVertices = new List<Vector3>();
        Vector3[] mDiplayPolygonVertices;
    }
}
#endif