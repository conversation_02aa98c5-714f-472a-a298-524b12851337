﻿ 



 
 

#if UNITY_EDITOR

using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class GroundTileMaker : MonoBehaviour
    {
        public void PaintEdge(Vector3 mouseCursorWorldPos, bool subtract, int maskTextureIndex)
        {
            if (mInstances == null)
            {
                return;
            }
            int rows = mInstances.GetLength(0);
            int cols = mInstances.GetLength(1);

            int h = horizontalInstanceCount;
            int v = verticalInstanceCount;

            var maskTextureSetting = mLODMaterialSettings[mCurrentLOD].maskTextureSetting;
            int textureResolution = maskTextureSetting[mMaskTextureIndex].resolution;
            int mousePixelX = Mathf.FloorToInt(mouseCursorWorldPos.x / (mTileSize * h) * (textureResolution * h - 1));
            int mousePixelY = Mathf.FloorToInt(mouseCursorWorldPos.z / (mTileSize * v) * (textureResolution * v - 1));
            int paintAreaPixelMinX = mousePixelX - mBrushSize / 2;
            int paintAreaPixelMinY = mousePixelY - mBrushSize / 2;
            int paintAreaPixelMaxX = paintAreaPixelMinX + mBrushSize - 1;
            int paintAreaPixelMaxY = paintAreaPixelMinY + mBrushSize - 1;

            float sign = 1.0f;
            if (subtract)
            {
                sign = -1.0f;
            }
            Vector3 tileSize = new Vector3(mTileSize, 0, mTileSize);
            var brush = mBrushManager.GetActiveBrush();
            bool normalizeColor = maskTextureSetting[maskTextureIndex].normalizeColor;

            bool rotateBrush = useRotatedBrush;

            int mipmap = brush.GetMipmap(mBrushSize, rotateBrush);

            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var instance = mInstances[j, i];
                    if (instance != null)
                    {
                        int tilePixelMinX = textureResolution * j;
                        int tilePixelMinY = textureResolution * i;
                        int tilePixelMaxX = tilePixelMinX + textureResolution - 1;
                        int tilePixelMaxY = tilePixelMinY + textureResolution - 1;

                        int overlappedPixelMinX = Mathf.Max(paintAreaPixelMinX, tilePixelMinX);
                        int overlappedPixelMinY = Mathf.Max(paintAreaPixelMinY, tilePixelMinY);
                        int overlappedPixelMaxX = Mathf.Min(paintAreaPixelMaxX, tilePixelMaxX);
                        int overlappedPixelMaxY = Mathf.Min(paintAreaPixelMaxY, tilePixelMaxY);

                        if (overlappedPixelMinX <= overlappedPixelMaxX && overlappedPixelMinY <= overlappedPixelMaxY)
                        {
                            var tile = mTiles[instance.tileIndex];
                            var variation = tile.GetCurrentVariation();
                            var tileLOD = variation.GetLOD(mCurrentLOD);
                            if (tile.writable)
                            {
                                var maskTexture = tileLOD.maskTextures[maskTextureIndex].texture;
                                var maskTextureData = tileLOD.maskTextures[maskTextureIndex].textureData;

                                int width = paintAreaPixelMaxX - paintAreaPixelMinX + 1;
                                int height = paintAreaPixelMaxY - paintAreaPixelMinY + 1;
                                int localPixelMinX = paintAreaPixelMinX - tilePixelMinX;
                                int localPixelMinY = paintAreaPixelMinY - tilePixelMinY;

                                for (int py = 0; py < height; ++py)
                                {
                                    for (int px = 0; px < width; ++px)
                                    {
                                        int dstIdx = py * width + px;
                                        float rx = px / (float)mBrushSize;
                                        float ry = py / (float)mBrushSize;
                                        Color brushVal = brush.SampleBilinear(mipmap, rx, ry, rotateBrush);
                                        Color val = brushVal * strength * sign;

                                        bool validHorizontal, validVertical;
                                        int x = WrapHorizontal(localPixelMinX + px, textureResolution, tile.edgeIDs, out validHorizontal);
                                        int y = WrapVertical(localPixelMinY + py, textureResolution, tile.edgeIDs, out validVertical);

                                        if (validHorizontal && validVertical)
                                        {
                                            var srcIdx = y * textureResolution + x;
                                            Color oldVal = maskTextureData[srcIdx];
                                            if (normalizeColor)
                                            {
                                                if (mChannel == 4)
                                                {
                                                    oldVal[0] = Mathf.Max(0, oldVal[0] + val[0]);
                                                    oldVal[1] = Mathf.Max(1, oldVal[1] + val[1]);
                                                    oldVal[2] = Mathf.Max(2, oldVal[2] + val[2]);
                                                }
                                                else if (mChannel == 5)
                                                {
                                                    oldVal[0] = Mathf.Max(0, oldVal[0] + val[0]);
                                                    oldVal[1] = Mathf.Max(1, oldVal[1] + val[1]);
                                                    oldVal[2] = Mathf.Max(2, oldVal[2] + val[2]);
                                                    oldVal[3] = Mathf.Max(2, oldVal[3] + val[3]);
                                                }
                                                else
                                                {
                                                    oldVal[mChannel] = Mathf.Max(0, oldVal[mChannel] + val.a);
                                                }
                                                float d = Mathf.Sqrt(oldVal.r * oldVal.r + oldVal.g * oldVal.g + oldVal.b * oldVal.b + oldVal.a * oldVal.a);
                                                if (d > 0)
                                                {
                                                    oldVal.r /= d;
                                                    oldVal.g /= d;
                                                    oldVal.b /= d;
                                                    oldVal.a /= d;
                                                }
                                                maskTexture.SetPixel(x, y, oldVal);
                                                maskTextureData[srcIdx] = oldVal;
                                            }
                                            else
                                            {
                                                if (mChannel == 4)
                                                {
                                                    oldVal[0] = Mathf.Clamp01(oldVal[0] + val[0]);
                                                    oldVal[1] = Mathf.Clamp01(oldVal[1] + val[1]);
                                                    oldVal[2] = Mathf.Clamp01(oldVal[2] + val[2]);
                                                }
                                                else if (mChannel == 5)
                                                {
                                                    oldVal[0] = Mathf.Clamp01(oldVal[0] + val[0]);
                                                    oldVal[1] = Mathf.Clamp01(oldVal[1] + val[1]);
                                                    oldVal[2] = Mathf.Clamp01(oldVal[2] + val[2]);
                                                    oldVal[3] = Mathf.Clamp01(oldVal[3] + val[3]);
                                                }
                                                else
                                                {
                                                    oldVal[mChannel] = Mathf.Clamp01(oldVal[mChannel] + val.a);
                                                }
                                                maskTexture.SetPixel(x, y, oldVal);
                                                maskTextureData[srcIdx] = oldVal;
                                            }
                                        }
                                    }
                                }

                                tileLOD.maskTextures[mMaskTextureIndex].dirty = true;
                                tileLOD.maskTextures[maskTextureIndex].paintDirtyRange.Enlarge(0, 0, textureResolution, textureResolution);
                                tileLOD.assetsDirty = true;
                                maskTexture.Apply();
                            }
                        }
                    }
                }
            }
        }

        int WrapHorizontal(int a, int n, EdgeID[] edgeIDs, out bool valid)
        {
            valid = true;
            if (a < 0)
            {
                if (edgeIDs[(int)EdgeDirection.Right] != edgeIDs[(int)EdgeDirection.Left])
                {
                    valid = false;
                }
                return a + n;
            }
            else if (a >= n)
            {
                if (edgeIDs[(int)EdgeDirection.Right] != edgeIDs[(int)EdgeDirection.Left])
                {
                    valid = false;
                }
                return a - n;
            }
            return a;
        }

        int WrapVertical(int a, int n, EdgeID[] edgeIDs, out bool valid)
        {
            valid = true;
            if (a < 0)
            {
                if (edgeIDs[(int)EdgeDirection.Down] != edgeIDs[(int)EdgeDirection.Up])
                {
                    valid = false;
                }
                return a + n;
            }
            else if (a >= n)
            {
                if (edgeIDs[(int)EdgeDirection.Down] != edgeIDs[(int)EdgeDirection.Up])
                {
                    valid = false;
                }
                return a - n;
            }
            return a;
        }
    }
}


#endif