﻿ 



 
 


#if UNITY_EDITOR

using UnityEditor;

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class EditorTerritoryLayerUI : UnityEditor.Editor
    {
        void DrawGridTemplates()
        {
            var layer = mLogic.layer;
            EditorGUILayout.BeginVertical("GroupBox");
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add"))
            {
                AddTemplate();
            }
            if (GUILayout.Button("Remove"))
            {
                if (EditorUtility.DisplayDialog("Warning", "Are you sure delete this? this action can't be redone!", "Yes", "No"))
                {
                    RemoveTemplate();
                }
            }
            if (GUILayout.Button("Change Type"))
            {
                ChangeTemplateType();
            }
            EditorGUILayout.EndHorizontal();

            var templates = layer.GetTerritories(mLogic.selectedSubLayerIndex);
            for (int i = 0; i < templates.Count; ++i)
            {
                bool selectionChange = DrawTemplate(templates[i], i);
                if (selectionChange)
                {
                    SetActiveTerritory(i);
                }
            }
            var subLayer = layer.GetSubLayer(mLogic.selectedSubLayerIndex);

            if (subLayer.selectedIndex == -1 && templates.Count > 0)
            {
                SetActiveTerritory(0);
            }

            EditorGUILayout.EndVertical();
        }

        void SetActiveTerritory(int idx)
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            subLayer.selectedIndex = idx;
            if (idx >= 0)
            {
                SetActiveProperty(mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex)[idx].properties);
            }
            else
            {
                SetActiveProperty(null);
            }
        }

        bool DrawTemplate(EditorTerritory template, int i)
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            EditorGUIUtility.labelWidth = 40;
            bool selectionChange = false;
            EditorGUILayout.BeginHorizontal();
            bool nowSelected = subLayer.selectedIndex == i;
            bool selected = EditorGUILayout.ToggleLeft("", nowSelected);
            if (!nowSelected && selected)
            {
                selectionChange = true;
            }
            template.name = EditorGUILayout.TextField(template.name);
            EditorGUIUtility.fieldWidth = 100;
            EditorGUILayout.IntField("ID", template.id);
            EditorGUIUtility.fieldWidth = 0;
            var newColor = EditorGUILayout.ColorField("", template.GetColor());
            if (newColor != template.GetColor())
            {
                template.SetColor(newColor);
            }

            if (GUILayout.Button("Change Color"))
            {
                mLogic.layer.RefreshTexture(mLogic.selectedSubLayerIndex);
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.labelWidth = 0;
            return selectionChange;
        }

        void AddTemplate()
        {
            var templates = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex);
            int type = 26010001;
            if (templates.Count > 0)
            {
                type = templates[templates.Count - 1].id + 1;
            }
            var dlg = EditorUtils.CreateInputDialog("Add Region Brush");
            var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("Name", "", "Region"),
                    new InputDialog.StringItem("Type", "", type.ToString()),
                };
            dlg.Show(items, OnClickAdd);
        }

        bool OnClickAdd(List<InputDialog.Item> parameters)
        {
            string name = (parameters[0] as InputDialog.StringItem).text;
            string typeStr = (parameters[1] as InputDialog.StringItem).text;
            if (string.IsNullOrEmpty(name))
            {
                return false;
            }
            int type;
            bool suc = Utils.ParseInt(typeStr, out type);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }

            if (type <= 0)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0", "OK");
                return false;
            }

            var editor = mLogic.layer;
            var temp = editor.FindTerritory(mLogic.selectedSubLayerIndex, type);
            if (temp != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {type} already existed!", "OK");
                return false;
            }
            
            var properties = new PropertyDatas(null);
            properties.AddProperty(new PropertyData<int>("citystar", PropertyType.kPropertyInt, 0));
            properties.AddProperty(new PropertyData<int>("SubordinateArea", PropertyType.kPropertyInt, 0));
            properties.AddProperty(new PropertyData<int[]>("refresh_types", PropertyType.kPropertyIntArray, new int[0]));

            editor.AddTerritory(mLogic.selectedSubLayerIndex, name, type, Color.white, properties, null);
            SetActiveTerritory(editor.GetTerritoryCount(mLogic.selectedSubLayerIndex) - 1);

            return true;
        }

        void RemoveTemplate()
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);

            if (subLayer.selectedIndex >= 0)
            {
                var editor = mLogic.layer;
                var template = editor.GetTerritories(mLogic.selectedSubLayerIndex)[subLayer.selectedIndex];
                RemoveGridOfType(mLogic.selectedSubLayerIndex, template.id);
                
                editor.RemoveTerritory(mLogic.selectedSubLayerIndex, subLayer.selectedIndex);
                SetActiveTerritory(editor.GetTerritoryCount(mLogic.selectedSubLayerIndex) - 1);

                editor.RefreshTexture(mLogic.selectedSubLayerIndex);
            }
        }

        void RemoveGridOfType(int subLayerIdx, int templateType)
        {
            var editor = mLogic.layer;
            var subLayer = editor.GetSubLayer(subLayerIdx);
            if (subLayer != null)
            {
                subLayer.RemoveGridOfType(templateType);
            }
        }

        void ChangeTemplateType()
        {
            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            if (subLayer.selectedIndex >= 0)
            {
                var templates = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex);
                var dlg = EditorUtils.CreateInputDialog("Change Region Type");
                var items = new List<InputDialog.Item> {
                    new InputDialog.StringItem("New Type", "", templates[subLayer.selectedIndex].id.ToString()),
                };
                dlg.Show(items, OnClickChangeTemplateType);
            }
        }

        bool OnClickChangeTemplateType(List<InputDialog.Item> parameters)
        {
            int newType;
            bool suc = Utils.ParseInt((parameters[0] as InputDialog.StringItem).text, out newType);
            if (!suc)
            {
                EditorUtility.DisplayDialog("Error", "invalid type!", "OK");
                return false;
            }
            if (newType <= 0)
            {
                EditorUtility.DisplayDialog("Error", "type must be > 0", "OK");
                return false;
            }

            var editor = mLogic.layer;
            if (editor.FindTerritory(mLogic.selectedSubLayerIndex, newType) != null)
            {
                EditorUtility.DisplayDialog("Error", $"type {newType} is already used!", "OK");
                return false;
            }

            var subLayer = mLogic.layer.GetSubLayer(mLogic.selectedSubLayerIndex);
            var template = editor.GetTerritories(mLogic.selectedSubLayerIndex)[subLayer.selectedIndex];
            var objects = editor.GetGrids(mLogic.selectedSubLayerIndex);
            int rows = objects.GetLength(0);
            int cols = objects.GetLength(1);
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    if (objects[i, j] == template.id)
                    {
                        objects[i, j] = newType;
                    }
                }
            }
            template.id = newType;

            return true;
        }

        void OnRenameProperty(string oldName, PropertyBase newProperty)
        {
            var territories = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex);
            for (int i = 0; i < territories.Count; ++i)
            {
                var template = territories[i];
                var prop = template.properties.FindProperty(oldName);
                if (prop != null)
                {
                    prop.name = newProperty.name;
                }
            }
        }

        void OnAddProperty(PropertyBase newProperty)
        {
            var territories = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex);
            for (int i = 0; i < territories.Count; ++i) {
                var template = territories[i];
                if (template.properties.FindProperty(newProperty.name) == null)
                {
                    var p = newProperty.Clone();
                    template.properties.AddProperty(p);
                }
            }
        }

        void OnRemoveProperty(PropertyBase removedProperty)
        {
            var territories = mLogic.layer.GetTerritories(mLogic.selectedSubLayerIndex);
            for (int i = 0; i < territories.Count; ++i)
            {
                var template = territories[i];
                var prop = template.properties.FindProperty(removedProperty.name);
                if (prop != null) {
                    template.properties.RemoveProperty(prop);
                }
            }
        }

        void SetActiveProperty(PropertyDatas properties)
        {
            mPropertyEditor.Show(properties, OnAddProperty, OnRemoveProperty, OnRenameProperty);
        }
    }
}

#endif