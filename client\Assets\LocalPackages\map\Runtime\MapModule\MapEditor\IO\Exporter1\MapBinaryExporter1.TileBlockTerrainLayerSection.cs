﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class MapBinaryExporter1
    {
        void SaveTileBlockTerrainLayer(BinaryWriter writer, BlendTerrainLayer layer)
        {
            BeginSection(MapDataSectionType.TileBlock<PERSON><PERSON>rain<PERSON>ay<PERSON>, writer);
            //版本号
            writer.Write(VersionSetting.TileBlockTerrainLayerStructVersion);

            //-----------------version 1 start------------------------------
            bool useLayer = layer != null;
            writer.Write(useLayer);
            if (!useLayer)
            {
                return;
            }

            var terrainLayerData = layer.layerData as BlendTerrainLayerData;
            int horizontalTileCount = layer.layerData.horizontalTileCount;
            int verticalTileCount = layer.layerData.verticalTileCount;
            float tileWidth = layer.layerData.tileWidth;
            float tileHeight = layer.layerData.tileHeight;
            ushort[] tiles;
            List<string> tilePrefabPaths;
            CalculateSingleTilesAndPrefabs(terrainLayerData, out tiles, out tilePrefabPaths);

            mIDExport.Export(writer, layer.id);
            Utils.WriteString(writer, ConvertToRuntimeLayerName(layer.name));
            Utils.WriteVector3(writer, layer.layerOffset);
            writer.Write(terrainLayerData.useGeneratedLOD);

            writer.Write(verticalTileCount);
            writer.Write(horizontalTileCount);
            writer.Write(tileWidth);
            writer.Write(tileHeight);

            //合并相同类型的15号tile
            var allConnectedTiles = layer.ProcessTiles();
            var atlasUVMapping = layer.atlasUVMapping;
            var prefabPathToCombinedTileMaterials = layer.prefabPathToCombinedTileMaterials;
            ConvertTiles(writer, layer, tilePrefabPaths.Count, tiles, tilePrefabPaths, allConnectedTiles, atlasUVMapping, prefabPathToCombinedTileMaterials);

            int n = tiles.Length;
            for (int i = 0; i < n; ++i)
            {
                writer.Write(tiles[i]);
            }
            Utils.WriteStringArray(writer, tilePrefabPaths.ToArray());

            //save map layer lod config
            SaveTileBlockTerrainLayerLayerLODConfig(writer, layer.layerData);
            //-----------------version 1 end------------------------------
        }

        //tile值为0表示空,所以tilePrefabPaths的第0项是null
        void CalculateSingleTilesAndPrefabs(BlendTerrainLayerData layerData, out ushort[] tiles, out List<string> tilePrefabPaths)
        {
            List<string> usedPrefabPaths = new List<string>();
            usedPrefabPaths.Add(null);

            int rows = layerData.verticalTileCount;
            int cols = layerData.horizontalTileCount;
            tiles = new ushort[rows * cols];
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    var tile = layerData.GetTile(j, i);
                    if (tile != null)
                    {
                        var prefabPath = tile.GetAssetPath(0);
                        int idx = usedPrefabPaths.IndexOf(prefabPath);
                        if (idx < 0)
                        {
                            idx = usedPrefabPaths.Count;
                            usedPrefabPaths.Add(prefabPath);
                        }
                        Debug.Assert(idx <= ushort.MaxValue && idx >= 1);
                        tiles[i * cols + j] = (ushort)idx;
                    }
                }
            }

            tilePrefabPaths = usedPrefabPaths;
        }

        void SaveTileBlockTerrainLayerLayerLODConfig(BinaryWriter writer, MapLayerData layerData)
        {
            var config = layerData.lodConfig;
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            writer.Write(n);
            var mapLODManager = Map.currentMap.data.lodManager;
            for (int i = 0; i < n; ++i)
            {
                var c = config.lodConfigs[i];
                float changeZoom = mapLODManager.ConvertNameToZoom(c.name);
                writer.Write(changeZoom);
                writer.Write(c.changeZoomThreshold);
                writer.Write(c.hideObject);
                writer.Write(c.shaderLOD);
                writer.Write((int)c.flag);
                writer.Write(c.terrainLODTileCount);
            }
        }

        void ConvertTiles(BinaryWriter writer, BlendTerrainLayer layer, int startOffset, ushort[] tiles, List<string> prefabPaths, List<BlendTerrainLayer.ConnectedTiles> allConnectedTiles, Dictionary<string, Vector2[]> atlasUVMapping, Dictionary<string, Material> prefabPathToCombinedTileMaterials)
        {
            ushort tileBlockType = (ushort)startOffset;
            int horizontalTileCount = layer.horizontalTileCount;
            List<TileBlockTerrainLayerData.TileBlock> allBlocks = new List<TileBlockTerrainLayerData.TileBlock>();
            for (int i = 0; i < allConnectedTiles.Count; ++i)
            {
                var connectedTiles = allConnectedTiles[i];
                int nRectangles = connectedTiles.rectangles.Count;
                int singleTileType = prefabPaths.IndexOf(connectedTiles.prefabPath);
                Debug.Assert(singleTileType > 0);
                for (int k = 0; k < nRectangles; ++k) {
                    int offsetX = connectedTiles.bounds.minX;
                    int offsetY = connectedTiles.bounds.minY;
                    int minX = connectedTiles.rectangles[k].minX + offsetX;
                    int minY = connectedTiles.rectangles[k].minY + offsetY;
                    int maxX = connectedTiles.rectangles[k].maxX + offsetX;
                    int maxY = connectedTiles.rectangles[k].maxY + offsetY;
                    for (int y = minY; y <= maxY; ++y)
                    {
                        for (int x = minX; x <= maxX; ++x)
                        {
                            int globalIdx = y * horizontalTileCount + x;
                            tiles[globalIdx] = tileBlockType;
                        }
                    }

                    TileBlockTerrainLayerData.TileBlock block = new TileBlockTerrainLayerData.TileBlock(minX, minY, maxX, maxY, singleTileType);
                    allBlocks.Add(block);

                    ++tileBlockType;
                }
            }

            Dictionary<int, Vector2[]> singleTileTypeAtlasUVMapping = new Dictionary<int, Vector2[]>();
            foreach (var p in atlasUVMapping)
            {
                int singleTileType = prefabPaths.IndexOf(p.Key);
                if (singleTileType > 0)
                {
                    singleTileTypeAtlasUVMapping.Add(singleTileType, p.Value);
                }
            }

            Dictionary<int, Material> singleTileTypeAtlasMaterialMapping = new Dictionary<int, Material>();
            foreach (var p in prefabPathToCombinedTileMaterials)
            {
                int singleTileType = prefabPaths.IndexOf(p.Key);
                if (singleTileType > 0)
                {
                    singleTileTypeAtlasMaterialMapping.Add(singleTileType, p.Value);
                }
            }

            //save data
            int nBlocks = allBlocks.Count;
            writer.Write(nBlocks);
            for (int i = 0; i < nBlocks; ++i)
            {
                writer.Write(allBlocks[i].minX);
                writer.Write(allBlocks[i].minY);
                writer.Write(allBlocks[i].width);
                writer.Write(allBlocks[i].height);
                writer.Write(allBlocks[i].singleTileType);
            }

            int n = singleTileTypeAtlasUVMapping.Count;
            writer.Write(n);
            foreach (var p in singleTileTypeAtlasUVMapping)
            {
                writer.Write(p.Key);
                Utils.WriteVector2Array(writer, p.Value);
            }

            n = singleTileTypeAtlasMaterialMapping.Count;
            writer.Write(n);
            foreach (var p in singleTileTypeAtlasMaterialMapping)
            {
                writer.Write(p.Key);
                string mtlPath = AssetDatabase.GetAssetPath(p.Value);
                Utils.WriteString(writer, mtlPath);
            }
        }
    }
}

#endif