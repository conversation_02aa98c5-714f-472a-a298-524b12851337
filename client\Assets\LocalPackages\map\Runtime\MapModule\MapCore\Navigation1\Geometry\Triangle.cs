﻿ 



 
 


using UnityEngine;

namespace TFW.Map.Geo
{
    public class Triangle
    {
        int mIndex;                                     // 三角形序号，唯一标示
        int mV0;
        int mV1;
        int mV2;
        // 三角形三边唯一序号，起服时生成
        int mEdgeID0;
        int mEdgeID1;
        int mEdgeID2;
        ushort mCustomType;                             //三角形的类型,每种类型可以有一个配置表,例如是否可通过等

        public Triangle(int idx, int v0, int v1, int v2, ushort customType)
        {
            mIndex = idx;
            mV0 = v0;
            mV1 = v1;
            mV2 = v2;
            mCustomType = customType;
        }

        public ushort GetCustomType()
        {
            return mCustomType;
        }

        // IsCoordInside 判断点是否在三角形内部
        // 三角形的向量是顺时针排列，所以当点p和三角形的向量之间求叉积，且全部小于0时，表示在同一侧，则点p在三角形内
        // 需要注意的是：共线的情况也认为在同一侧，所以需要加上=0的判断
        public bool IsCoordInside(Coord p)
        {
            var vertices = Nav.Mesh.currentMeshVertices;
            var pa = new Vector(p, vertices[mV0].Coord);
            var pb = new Vector(p, vertices[mV1].Coord);
            var pc = new Vector(p, vertices[mV2].Coord);


            var b1 = pa.Cross(pb) <= 0;
            var b2 = pb.Cross(pc) <= 0;

            if (b1 != b2)
            {
                return false;
            }

            var b3 = pc.Cross(pa) <= 0;
            return b2 == b3;
        }

        // GetIndex 获取序号
        public int GetIndex()
        {
            return Index;
        }

        // ToRect 获取三角形矩形边界
        public void ToRect(out int minX, out int minZ, out int maxX, out int maxZ)
        {
            minX = int.MaxValue;
            minZ = int.MaxValue;
            maxX = int.MinValue;
            maxZ = int.MinValue;

            var vertices = Nav.Mesh.currentMeshVertices;
            for (int i = 0; i < 3; ++i)
            {
                var coord = vertices[GetVertexIndex(i)].Coord;
                minX = UnityEngine.Mathf.Min(minX, coord.X);
                minZ = UnityEngine.Mathf.Min(minZ, coord.Z);
                maxX = UnityEngine.Mathf.Max(maxX, coord.X);
                maxZ = UnityEngine.Mathf.Max(maxZ, coord.Z);
            }
        }

        // GetEdgeIDs 返回三角形边的序号列表
        //public int[] GetEdgeIDs()
        //{
        //    return edgeIDs;
        //}

        // GetEdgeMidCoords 返回三角形边的中点列表
        public Coord[] GetEdgeMidCoords()
        {
            var coords = new Coord[3];
            var vertices = Nav.Mesh.currentMeshVertices;

            coords[0] = GeoUtils.CalMidCoord(vertices[mV0].Coord, vertices[mV1].Coord);
            coords[1] = GeoUtils.CalMidCoord(vertices[mV1].Coord, vertices[mV2].Coord);
            coords[2] = GeoUtils.CalMidCoord(vertices[mV2].Coord, vertices[mV0].Coord);
            return coords;
        }

        // GetVertices 获取点列表
        //public List<Vertice> GetVertices()
        //{
        //    return mVertices;
        //}

        // GetVectors 获取三角形向量组，逆时针排列
        public Vector[] GetVectors()
        {
            var vertices = Nav.Mesh.currentMeshVertices;
            var vecs = new Vector[3];
            vecs[0] = new Vector(vertices[mV0].Coord);
            vecs[1] = new Vector(vertices[mV2].Coord);
            vecs[2] = new Vector(vertices[mV1].Coord);
            return vecs;
        }

        public int GetVertexIndex(int i)
        {
            switch (i)
            {
                case 0:
                    return mV0;
                case 1:
                    return mV1;
                case 2:
                    return mV2;
            }
            Debug.Assert(false);
            return -1;
        }

        // GetNeighborEdgeNums 获取两个三角形邻接边的数量
        public int GetNeighborEdgeNums(Triangle t2)
        {
            var vertices = Nav.Mesh.currentMeshVertices;
            //var vertices2 = t2.Vertices;
            //int n = mVertices.Count;
            //int n2 = vertices2.Count;
            int cnt = 0;
            for (int i = 0; i < 3; ++i)
            {
                for (int j = 0; j < 3; ++j)
                {
                    //if (mVertices[i].Index == vertices2[j].Index)
                    if (GetVertexIndex(i) == t2.GetVertexIndex(j))
                    {
                        cnt++;
                    }
                }
            }
            return cnt;
        }

        public void SetEdgeIDs(int e0, int e1, int e2)
        {
            mEdgeID0 = e0;
            mEdgeID1 = e1;
            mEdgeID2 = e2;
        }

        public int GetEdgeID(int idx)
        {
            switch (idx)
            {
                case 0:
                    return mEdgeID0;
                case 1:
                    return mEdgeID1;
                case 2:
                    return mEdgeID2;
            }
            Debug.Assert(false);
            return -1;
        }

        public int Index { get { return mIndex; } }
        public int v0 { get { return mV0; } }
        public int v1 { get { return mV1; } }
        public int v2 { get { return mV2; } }
        public int e0 { get { return mEdgeID0; } }
        public int e1 { get { return mEdgeID1; } }
        public int e2 { get { return mEdgeID2; } }
    }
}