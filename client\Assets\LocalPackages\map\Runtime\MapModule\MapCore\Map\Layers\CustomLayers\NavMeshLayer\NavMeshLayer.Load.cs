﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class NavMeshLayer : MapLayerBase
    {
        public static void LoadEditorData(string dataPath)
        {
            if (!File.Exists(dataPath))
            {
                return;
            }
            var bytes = File.ReadAllBytes(dataPath);
            if (bytes == null)
            {
                return;
            }
            using MemoryStream stream = new MemoryStream(bytes);
            using BinaryReader reader = new BinaryReader(stream);

            var version = Utils.ReadVersion(reader);

            var layerData = LoadLayerData(reader, AllocateID());

            reader.Close();

            var layer = new NavMeshLayer(Map.currentMap);
            layer.Load(layerData, null, false);
        }

        static config.MapLayerData LoadLayerData(BinaryReader reader, int layerID)
        {
            string layerName = Utils.ReadString(reader);
            Vector3 layerOffset = Utils.ReadVector3(reader);
            bool active = reader.ReadBoolean();
            bool visible = reader.ReadBoolean();
            float width = reader.ReadSingle();
            float height = reader.ReadSingle();
            bool oceanAreaWalkable = reader.ReadBoolean();

            config.NavMeshData[] navMeshDatas = null;

            //后续版本nav mesh分成了多个小块
            int blockCount = reader.ReadInt32();
            navMeshDatas = new config.NavMeshData[blockCount];
            for (int b = 0; b < blockCount; ++b)
            {
                navMeshDatas[b] = new config.NavMeshData();
                int vertexCount = reader.ReadInt32();
                int indexCount = reader.ReadInt32();
                Vector3[] vertices = null;
                if (vertexCount > 0)
                {
                    vertices = new Vector3[vertexCount];
                    for (int i = 0; i < vertexCount; ++i)
                    {
                        vertices[i] = Utils.ReadVector3(reader);
                    }
                }

                int[] indices = null;
                if (indexCount > 0)
                {
                    indices = new int[indexCount];
                    for (int i = 0; i < indexCount; ++i)
                    {
                        indices[i] = reader.ReadInt32();
                    }
                }

                navMeshDatas[b].vertices = vertices;
                navMeshDatas[b].triangles = indices;
                navMeshDatas[b].triangleTypes = Utils.ReadUInt16Array(reader);
                navMeshDatas[b].triangleStates = Utils.ReadBoolArray(reader);
            }

            var layer = new config.NavMeshLayerData(layerID, layerName, layerOffset, null, null, visible, width, height, navMeshDatas, oceanAreaWalkable);
            layer.active = active;
            return layer;
        }

        static int AllocateID()
        {
            return Map.currentMap.nextCustomObjectID;
        }
    }
}

#endif