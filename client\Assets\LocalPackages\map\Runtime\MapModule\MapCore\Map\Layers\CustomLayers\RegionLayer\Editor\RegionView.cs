﻿ 



 
 

//created by wzw at 2020/2/17

#if UNITY_EDITOR

using UnityEngine;

namespace TFW.Map
{
    public class RegionView : PolygonObjectView
    {
        public RegionView(IMapObjectData data, MapLayerView layerView) : base(data, layerView)
        {
        }

        //创建视图使用的模型
        protected override ModelBase CreateModelInternal(IMapObjectData data, int newLOD)
        {
            Debug.Assert(mModel == null);
            Debug.Assert(data != null);

            var model = new RegionModel(data as RegionData);
            model.transform.SetParent(mLayerView.root.transform, true);

            var layerData = mLayerView.layerData as RegionLayerData;
            model.SetMeshVisible(layerData.showRegionMesh);
            model.SetBorderLineMeshVisible(layerData.showBorderLineMesh);

            return model;
        }

        public void SetMaterial(Material mtl)
        {
            var model = mModel as RegionModel;
            model.SetMaterial(mtl);
        }

        public void SetMeshVisible(bool visible)
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                model.SetMeshVisible(visible);
            }
        }

        public void SetBorderLineMeshVisible(bool visible)
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                model.SetBorderLineMeshVisible(visible);
            }
        }

        public void UpdateMesh()
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                var regionData = Map.currentMap.FindObject(objectDataID) as RegionData;
                model.UpdateMesh(regionData as RegionData);
            }
        }

        public void SetBorderLineMesh(Mesh mesh, Material mtl)
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                model.SetBorderLineMesh(mesh, mtl);
            }
        }
            
        public Mesh GetMesh()
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                return model.GetMesh();
            }
            return null;
        }

        public Mesh GetBorderLineMesh()
        {
            if (mModel != null)
            {
                var model = mModel as RegionModel;
                return model.GetBorderLineMesh();
            }
            return null;
        }
    }
}

#endif