﻿ 



 
 



namespace TFW.Map.Geo
{
    // 矩形对象
    public struct Rectangle
    {
        public int X;  // 左下角点
        public int Z;
        public int Width;
        public int Height;

        public Rectangle(int x, int z, int width, int height)
        {
            this.X = x;
            this.Z = z;
            this.Width = width;
            this.Height = height;
        }

        public Coord RandCoord()
        {
            return new Coord(X + UnityEngine.Random.Range(0, Width),
                Z + UnityEngine.Random.Range(0, Height));
        }

        // 获取4个顶点坐标,边界点按照逆时针排列
        Coord[] GetVerticeCoords()
        {
            var p = new Coord[4];
            p[0] = new Coord(X, Z);
            p[1] = new Coord(X + Width, Z);
            p[2] = new Coord(X + Width, Z + Height);
            p[3] = new Coord(X, Z + Height);
            return p;
        }

        // 获取4条线段，按照逆时针排列
        Vector[] GetVectors()
        {
            var coords = GetVerticeCoords();
            return new Vector[4]{
        new Vector(coords[0]),
        new Vector(coords[1]),
        new Vector(coords[2]),
        new Vector(coords[3])
        };
        }

        // 点是否在矩形内
        // 矩形的向量是逆时针排列，所以当点p和矩形的向量之间求叉积，且全部大于0时，表示在同一侧，则点p在矩形内
        // 需要注意的是：共线的情况也认为在同一侧，所以需要加上=0的判断
        bool IsCoordInside(Coord p)
        {
            var pts = GetVerticeCoords();

            var pa = new Vector(p, pts[0]);
            var pb = new Vector(p, pts[1]);
            var pc = new Vector(p, pts[2]);

            var b1 = pa.Cross(pb) >= 0;
            var b2 = pb.Cross(pc) >= 0;
            if (b1 != b2)
            {
                return false;
            }

            var pd = new Vector(p, pts[3]);
            var b3 = pc.Cross(pd) >= 0;
            if (b2 != b3)
            {
                return false;
            }

            var b4 = pd.Cross(pa) >= 0;
            return b3 == b4;
        }
    }
}
