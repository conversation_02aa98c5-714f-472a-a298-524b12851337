﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //当地块隐藏时去掉管理的其子节点
    public class FrameActionSetFrontTileVisibility2 : FrameAction
    {
        public static FrameActionSetFrontTileVisibility2 Require(TileGridObjectLayerData2 layerData, int oldLOD, int newLOD, bool visible, FrameActionType type, bool instant, bool changeLODInCurrentFrameForThisTile, int x, int y)
        {
            var act = mPool.Require();
            act.Init(layerData, oldLOD, newLOD, visible, type, instant, changeLODInCurrentFrameForThisTile, x, y);
            return act;
        }

        protected void Init(TileGridObjectLayerData2 layerData, int oldLOD, int newLOD, bool visible, FrameActionType type, bool instant, bool changeLODInCurrentFrameForThisTile, int x, int y)
        {
            InitAction();
            mOldLOD = oldLOD;
            mNewLOD = newLOD;
            mLayerData = layerData;
            mVisible = visible;
            mType = type;
            mInstant = instant;
            mChangeLODInCurrentFrameForThisTile = changeLODInCurrentFrameForThisTile;
            mTileX = x;
            mTileY = y;
            int idx = mTileY * mLayerData.horizontalTileCount + mTileX;
            if (visible)
            {
                mKey = MakeActionKey(idx, newLOD, type);
            }
            else
            {
                mKey = MakeActionKey(idx, oldLOD, type);
            }
        }

        protected override void DoImpl()
        {
            mLayerData.SetObjectActiveFromAction(this, mOldLOD, mNewLOD, mVisible, mInstant, mChangeLODInCurrentFrameForThisTile, mTileX, mTileY);
        }

        protected override void OnDestroyImpl()
        {
            mLayerData = null;
            mPool.Release(this);
        }

        public override long key { get { return mKey; } }
        public static long MakeActionKey(int id, int lod, FrameActionType type)
        {
            long id64 = (long)id;
            long lod64 = (long)lod;
            long type64 = (long)type;
            long typeAndLOD = (lod64 << 16) | type64;
            id64 <<= 32;
            return typeAndLOD | id64;
        }
        public bool changeLODInCurrentFrameForThisTile { get { return mChangeLODInCurrentFrameForThisTile; } }
        public override FrameActionType type => mType;
        public override string debugInfo
        {
            get
            {
                return string.Format("TileX: {0}, TileY: {1}, Old LOD: {2}, New LOD: {3}, Child: {3}", mTileX, mTileY, mOldLOD, mNewLOD, childActions.Count);
            }
        }
        public override string name
        {
            get
            {
                if (mVisible)
                {
                    return "Show Front Tile";
                }
                else
                {
                    return "Hide Front Tile";
                }
            }
        }

        protected TileGridObjectLayerData2 mLayerData;
        protected bool mVisible;
        protected long mKey;
        protected int mOldLOD;
        protected int mNewLOD;
        FrameActionType mType;
        bool mInstant;
        bool mChangeLODInCurrentFrameForThisTile;
        int mTileX;
        int mTileY;

        static ObjectPool<FrameActionSetFrontTileVisibility2> mPool = new ObjectPool<FrameActionSetFrontTileVisibility2>(3000, () => new FrameActionSetFrontTileVisibility2());
    }
}
