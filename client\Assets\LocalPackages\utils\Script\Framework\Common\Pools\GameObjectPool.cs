﻿ 
/*
 * created by zdt at 2021.05.19
 */

using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Common.Pool
{
    /// <summary>
    /// <para>GameObject对象池。</para>
    /// <para>这个对象池内部根据key值划分为多个子对象池。</para>
    /// <para>使用者可以通过key值获取不同类型的GameObject实例。</para>
    /// </summary>
    public sealed class GameObjectPool
    {
        private Dictionary<string, List<GameObject>> m_ObjectLists = new Dictionary<string, List<GameObject>>();
        private Func<string, GameObject> m_CreateFunc;
        private Action<string, GameObject> m_DestroyFunc;
        private Action<string, GameObject> m_ActionOnRequire;
        private Action<string, GameObject> m_ActionOnRelease;

        public GameObjectPool(Func<string, GameObject> createFunc = null,
                              Action<string, GameObject> destroyFunc = null,
                              Action<string, GameObject> actionOnRequire = null,
                              Action<string, GameObject> actionOnRelease = null)
        {
            m_CreateFunc = createFunc;
            m_DestroyFunc = destroyFunc;
            m_ActionOnRequire = actionOnRequire;
            m_ActionOnRelease = actionOnRelease;
        }

        /// <summary>
        /// 获取传入key值对应的GameObject实例
        /// </summary>
        /// <param name="key">key值，可以是prefab名称等</param>
        /// <param name="pos"></param>
        /// <param name="scale"></param>
        /// <param name="rot"></param>
        /// <returns></returns>
        public GameObject Require(string key, Vector3 pos, Vector3 scale, Quaternion rot)
        {
            GameObject model = Require(key);

            if (model != null)
            {
                model.transform.localPosition = pos;
                model.transform.localScale = scale;
                model.transform.rotation = rot;
                model.SetActive(true);
            }

            return model;
        }

        /// <summary>
        /// 获取传入key值对应的GameObject实例。
        /// 注：返回的GameObject实例不一定是显示或隐藏的，
        ///     需要在构造方法中指定actionOnRequire或actionOnRelease来自行设置。
        /// </summary>
        /// <param name="key">key值，可以是prefab名称等</param>
        /// <returns></returns>
        public GameObject Require(string key)
        {
            GameObject go = null;

            if (!m_ObjectLists.TryGetValue(key, out var objectList))
            {
                m_ObjectLists[key] = objectList = new List<GameObject>();
            }

            var objectListCount = objectList.Count;

            if (objectListCount > 0)
            {
                go = objectList[objectListCount - 1];
                objectList.RemoveAt(objectListCount - 1);
            }

            if (go == null)
            {
                if (m_CreateFunc != null)
                {
                    go = m_CreateFunc(key);
                }
                else
                {
                    go = new GameObject();
                }
            }

            m_ActionOnRequire?.Invoke(key, go);

            return go;
        }

        /// <summary>
        /// 归还传入的GameObject实例到传入的key值对应的对象池中
        /// </summary>
        /// <param name="key"></param>
        /// <param name="go"></param>
        public void Release(string key, GameObject go)
        {
            if (go == null) { return; }

            m_ActionOnRelease?.Invoke(key, go);
            go.SetActive(false);

            if (!m_ObjectLists.TryGetValue(key, out var objectList))
            {
                m_ObjectLists[key] = objectList = new List<GameObject>();
            }

            objectList.Add(go);
        }

        /// <summary>
        /// 清空指定的key值对应的对象池
        /// </summary>
        /// <param name="key"></param>
        public void Clear(string key)
        {
            if (m_ObjectLists.TryGetValue(key, out var objectList))
            {
                foreach (var go in objectList)
                {
                    if (m_DestroyFunc != null)
                    {
                        m_DestroyFunc.Invoke(key, go);
                    }
                    else
                    {
                        InternalDestroyObject(go);
                    }
                }
                m_ObjectLists.Remove(key);
            }
        }

        /// <summary>
        /// 清空所有的对象池
        /// </summary>
        public void Clear()
        {
            foreach (var kv in m_ObjectLists)
            {
                var key = kv.Key;
                var objectList = kv.Value;

                foreach (var go in objectList)
                {
                    if (m_DestroyFunc != null)
                    {
                        m_DestroyFunc.Invoke(key, go);
                    }
                    else
                    {
                        InternalDestroyObject(go);
                    }
                }

                objectList.Clear();
            }

            m_ObjectLists.Clear();
        }

        private static void InternalDestroyObject(UnityEngine.Object obj)
        {
#if UNITY_EDITOR
            if (!EditorApplication.isPlaying)
            {
                if (obj != null)
                {
                    UnityEngine.Object.DestroyImmediate(obj);
                }

                return;
            }
#endif
            UnityEngine.Object.Destroy(obj);
        }
    }
}