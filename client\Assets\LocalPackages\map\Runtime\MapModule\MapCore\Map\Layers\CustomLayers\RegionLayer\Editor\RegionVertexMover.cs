﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class RegionVertexMover
    {
        public RegionVertexMover(int layerID, List<RegionSelection> movedRegions, Vector3 pos, PrefabOutlineType outlineType)
        {
            for (int i = 0; i < movedRegions.Count; ++i)
            {
                var action = new ActionMoveRegionVertex(layerID, movedRegions[i].regionID, movedRegions[i].vertexIndex, pos, outlineType);
                mActions.Add(action);
            }
        }

        public void Stop(Vector3 pos)
        {
            for (int i = 0; i < mActions.Count; ++i)
            {
                mActions[i].SetEndPosition(pos);
            }
            if (mActions[0].isMoved)
            {
                var actions = new CompoundAction("move regions vertices");
                for (int i = 0; i < mActions.Count; ++i)
                {
                    actions.Add(mActions[i]);
                }
                ActionManager.instance.PushAction(actions, true, false);
            }
            mActions = null;
        }

        List<ActionMoveRegionVertex> mActions = new List<ActionMoveRegionVertex>();
    }
}


#endif