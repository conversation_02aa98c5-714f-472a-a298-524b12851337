fileFormatVersion: 2
guid: 47103a0dd0066fb4b8e31c75c49c2ee4
PluginImporter:
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  isOverridable: 0
  platformData:
    data:
      first:
        '': Any
      second:
        enabled: 0
        settings:
          Exclude Editor: 0
          Exclude Linux: 1
          Exclude Linux64: 0
          Exclude LinuxUniversal: 1
          Exclude OSXIntel: 1
          Exclude OSXIntel64: 0
          Exclude OSXUniversal: 1
          Exclude Win: 1
          Exclude Win64: 0
    data:
      first:
        '': Editor
      second:
        enabled: 0
        settings:
          CPU: x86_64
          OS: Windows
    data:
      first:
        Android: Android
      second:
        enabled: 0
        settings:
          CPU: AnyCPU
    data:
      first:
        Any: 
      second:
        enabled: 0
        settings: {}
    data:
      first:
        Editor: Editor
      second:
        enabled: 1
        settings:
          CPU: x86_64
          DefaultValueInitialized: true
          OS: Windows
    data:
      first:
        Facebook: Win
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Facebook: Win64
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        Standalone: Linux
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: Linux64
      second:
        enabled: 1
        settings:
          CPU: x86_64
    data:
      first:
        Standalone: LinuxUniversal
      second:
        enabled: 0
        settings:
          CPU: x86_64
    data:
      first:
        Standalone: OSXIntel
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: OSXIntel64
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        Standalone: OSXUniversal
      second:
        enabled: 0
        settings:
          CPU: x86_64
    data:
      first:
        Standalone: Win
      second:
        enabled: 0
        settings:
          CPU: None
    data:
      first:
        Standalone: Win64
      second:
        enabled: 1
        settings:
          CPU: AnyCPU
    data:
      first:
        iPhone: iOS
      second:
        enabled: 0
        settings:
          CompileFlags: 
          FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
