%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo_SG2 Hologram Torus
  m_Shader: {fileID: 4800000, guid: 77301915a8e11d64e9c9078eee018be5, type: 3}
  m_ShaderKeywords: TCP2_NONE _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlbedoDetail:
        m_Texture: {fileID: 0}
        m_Scale: {x: 2, y: 1}
        m_Offset: {x: -0.5, y: -0.5}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionTexture:
        m_Texture: {fileID: 2800000, guid: 90f5c2a59e018304bb00979205857f25, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineColorVertex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ScanlinesTex:
        m_Texture: {fileID: 2800000, guid: d3a84e60412339a45838ef70b6b87839, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Alpha: 0
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _MyFloat: 1
    - _NDVMaxFrag: 0.45
    - _NDVMinFrag: 0
    - _OcclusionStrength: 1
    - _OutlineWidth: 2
    - _Parallax: 0.02
    - _RampSmoothing: 0.1
    - _RampThreshold: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    - __dummy__: 0
    - __outline_gui_dummy__: 0
    m_Colors:
    - _Color: {r: 0.16191176, g: 0.54461896, b: 0.734, a: 1}
    - _Emission: {r: 0, g: 0.2307301, b: 0.36764705, a: 1}
    - _EmissionColor: {r: 0, g: 0.3184078, b: 0.50735295, a: 1}
    - _FakeLightDir: {r: 0.85, g: -0.27, b: -0.89, a: 0}
    - _HColor: {r: 0.9852941, g: 0.9852941, b: 0.9852941, a: 1}
    - _HologramColor: {r: 0, g: 1.3568628, b: 1.8039216, a: 1}
    - _MainLightDirection: {r: 0, g: 1, b: -1.72, a: 0}
    - _OutlineColorVertex: {r: 0, g: 0.5034485, b: 1, a: 1}
    - _SColor: {r: 0, g: 0, b: 0, a: 1}
    - _ScanlinesTex_SC: {r: 0, g: -0.02, b: 0, a: 0}
