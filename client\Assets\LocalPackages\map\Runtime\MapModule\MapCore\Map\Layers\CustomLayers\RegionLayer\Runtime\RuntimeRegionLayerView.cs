﻿ 



 
 


using UnityEngine;

namespace TFW.Map
{
    //地图对象的模型层
    public class RuntimeRegionLayerView : MapObjectLayerView
    {
        public RuntimeRegionLayerView(MapLayerData layerData, bool asyncLoading, string borderPrefabPath)
        : base(layerData, asyncLoading)
        {
            mBorderGameObject = MapModuleResourceMgr.LoadGameObject(borderPrefabPath);
            mBorderGameObject.transform.parent = root.transform;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            Utils.DestroyObject(mBorderGameObject);
        }

        //创建该地图层类型中对象的视图
        protected override MapObjectView CreateObjectView(IMapObjectData data)
        {
            var view = new RuntimeRegionView(data, this);
            view.CreateModel(data, layerData.currentLOD);

            return view;
        }

        GameObject mBorderGameObject;
    }
}
