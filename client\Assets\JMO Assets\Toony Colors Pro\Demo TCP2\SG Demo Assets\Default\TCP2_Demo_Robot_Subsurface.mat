%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: TCP2_Demo_Robot_Subsurface
  m_Shader: {fileID: 4800000, guid: 89d14b6e7418de84eb624cbf18d4de37, type: 3}
  m_ShaderKeywords: TCP2_DISABLE_WRAPPED_LIGHT TCP2_TANGENT_AS_NORMALS _EMISSION
  m_LightmapFlags: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
    - first:
        name: _BumpMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DissolveMap
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _DissolveRamp
      second:
        m_Texture: {fileID: 2800000, guid: 948e1b7327c293a4081ba3c18eaab19f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _MainTex
      second:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Mask1
      second:
        m_Texture: {fileID: 2800000, guid: 2fb9975c379e44e44b7abcf981468c32, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _Ramp
      second:
        m_Texture: {fileID: 2800000, guid: f7efee5cdbe39254082e87a9ddcf42e4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - first:
        name: _SketchTex
      second:
        m_Texture: {fileID: 2800000, guid: a0d03d906b6264d4c8c02b0ffd7bc8c2, type: 3}
        m_Scale: {x: 10, y: 10}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - first:
        name: _DissolveGradientWidth
      second: 0.2
    - first:
        name: _DissolveValue
      second: 0.5
    - first:
        name: _EnableConstSizeOutline
      second: 0
    - first:
        name: _EnableTexturedOutline
      second: 0
    - first:
        name: _EnableZSmooth
      second: 0
    - first:
        name: _HSV_H
      second: 180
    - first:
        name: _HSV_S
      second: 0
    - first:
        name: _HSV_V
      second: 0
    - first:
        name: _Offset1
      second: 0
    - first:
        name: _Offset2
      second: 0
    - first:
        name: _Outline
      second: 0.5
    - first:
        name: _RampSmooth
      second: 0.63
    - first:
        name: _RampThreshold
      second: 0.5
    - first:
        name: _RimMax
      second: 0.7
    - first:
        name: _RimMin
      second: 0.5
    - first:
        name: _SSDistortion
      second: 0.75
    - first:
        name: _SSPower
      second: 1.5
    - first:
        name: _SSScale
      second: 15
    - first:
        name: _Shininess
      second: 2
    - first:
        name: _SketchHalftoneMax
      second: 1
    - first:
        name: _SketchHalftoneMin
      second: 0
    - first:
        name: _SketchSpeed
      second: 6
    - first:
        name: _SpecSmooth
      second: 1
    - first:
        name: _TexLod
      second: 5
    - first:
        name: _ZSmooth
      second: -0.5
    - first:
        name: __dummy__
      second: 0
    - first:
        name: __outline_gui_dummy__
      second: 0
    m_Colors:
    - first:
        name: _Color
      second: {r: 0.6985294, g: 0.667712, b: 0.667712, a: 1}
    - first:
        name: _HColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _OutlineColor
      second: {r: 0.072306015, g: 0.122909546, b: 0.30900002, a: 1}
    - first:
        name: _RimColor
      second: {r: 0, g: 0, b: 0, a: 1}
    - first:
        name: _SColor
      second: {r: 0.14117648, g: 0.14117648, b: 0.14117648, a: 1}
    - first:
        name: _SSAmbColor
      second: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - first:
        name: _SSColor
      second: {r: 1, g: 1, b: 1, a: 1}
    - first:
        name: _SketchColor
      second: {r: 0.5588235, g: 0.5588235, b: 0.5588235, a: 1}
    - first:
        name: _SpecColor
      second: {r: 0.125, g: 0.125, b: 0.125, a: 1}
