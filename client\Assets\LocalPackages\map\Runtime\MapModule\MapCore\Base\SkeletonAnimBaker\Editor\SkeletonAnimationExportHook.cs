﻿ 



 
 

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public abstract class SkeletonAnimationExportHook : ScriptableObject
    {
        public abstract void Export(List<string> paths, List<string> bakedPrefabNames);
    }

    [CreateAssetMenu(fileName = "animation_export_hook_test", menuName = "Assets/Create Bake Animation Export Hook Test")]
    public class SkeletonAnimationExportHookTest : SkeletonAnimationExportHook
    {
        public override void Export(List<string> paths, List<string> bakedPrefabNames)
        {
            //do something here
        }
    }
}
