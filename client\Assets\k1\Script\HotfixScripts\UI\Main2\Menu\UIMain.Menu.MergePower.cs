﻿using System;
using System.Collections.Generic;
using Common;
using K3;
using Logic;
using Public;
using TFW.UI;
using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    public class UIMain_Menu_MergePower:MonoBehaviour
    {
        /// <summary>
        /// 体力进度条
        /// </summary>
        //private Slider powerSlider;
        /// <summary>
        /// 体力数字显示
        /// </summary>
        private TFWText powerNumText;

        /// <summary>
        /// 进度条图片
        /// </summary>
        private RectTransform powerFillNew;
        /// <summary>
        /// 默认宽度
        /// </summary>
        private float defualtWidth;
        /// <summary>
        /// size
        /// </summary>
        private Vector2 powerFillNewSizeDelta;
        private TextLoopDisplay powerText;
        private long lastTime;
        private long lastAction;

        private List<string> powerTxt = new List<string>() { "", "" };
        private string phyPowerStr = "";
        private string lastTimeStr = "";
        
        public void Awake()
        {
            //powerSlider = UIHelper.GetComponent<Slider>(gameObject, "Icon/mergeIcon/Slider");
            powerNumText = UIHelper.GetComponent<TFWText>(gameObject, "Icon/mergeIcon/Slider/TxtNum");
            powerFillNew = UIHelper.GetComponent<RectTransform>(gameObject, "Icon/mergeIcon/Slider/Fill Area/Fill");
            defualtWidth = powerFillNew.sizeDelta.x;
            powerFillNewSizeDelta = powerFillNew.sizeDelta;

           
        }

        public void Start()
        {
            EventMgr.RegisterEvent(TEventType.RefreshAssetAck, (objs) =>
            {
                UpdatePowerInfo();
            }, this);

            EventMgr.RegisterEvent(TEventType.RefreshHeroData, (objs) =>
            {
                UpdatePowerInfo();
            }, this);

            EventMgr.RegisterEvent(TEventType.ExChangeHeroEnd, (objs) => { UpdatePowerInfo(); }, this);
            K3PlayerMgr.I.ChangeEnergyTimer += ChangeEnergyTimer;
            UpdatePowerInfo();
        }

        public void OnDestroy()
        {
            EventMgr.UnregisterEvent(this);
            K3PlayerMgr.I.ChangeEnergyTimer -= ChangeEnergyTimer;
        }

        private void Update()
        {
            K3PlayerMgr.I.UpdateEnergy();
        }

        private void ChangeEnergyTimer(long targetTime)
        {
            UpdatePowerInfo();
        }

        /// <summary>
        /// 刷新体力信息数据
        /// </summary>
        public void UpdatePowerInfo()
        {
            int currentPhyPower = K3PlayerMgr.I.PlayerData?.energy ?? 0;
            int maxPhyPower = K3PlayerMgr.I.PlayerData?.maxEnergy ?? 1;
            var energy = (float)currentPhyPower / maxPhyPower;

            if (powerFillNew)
            {
                if (energy < 0)
                {
                    energy = 0f;
                }

                if (energy > 1f)
                {
                    energy = 1f;
                }

                //powerSlider.value = energy;
                //由于slider设置有几率出现空槽情况,改用图片宽度
                powerFillNewSizeDelta.x = energy * defualtWidth;
                powerFillNew.sizeDelta = powerFillNewSizeDelta;

                //if (powerSliderFill)
                //{
                //    Debug.LogFormat($"UpdatePowerInfo currentPhyPower={currentPhyPower}, maxPhyPower={maxPhyPower}, energy={energy}, anchorMax={powerSliderFill.anchorMax}, anchorMin={powerSliderFill.anchorMin}, sizeDelta={powerSliderFill.sizeDelta}, offsetMax={powerSliderFill.offsetMax},offsetMin={powerSliderFill.offsetMin}");
                //}

                if (powerText == null)
                    powerText = UIHelper.GetAddComponent<TextLoopDisplay>(powerNumText.gameObject);

                phyPowerStr = string.Format("{0}/{1}", currentPhyPower, maxPhyPower);

                if (currentPhyPower < maxPhyPower)
                {
                    // lastTime = PlayerAssetsMgr.I.RestoreActionTotalTime;
                    lastTime = LEnergyGauge.I.GetMergeRecoverNextTime(out int nextAddvalue);
                    lastTimeStr = UIHelper.GetFormatTime(lastTime - GameTime.Time,false, false, false);

                    powerTxt[0] = phyPowerStr;
                    powerTxt[1] = lastTimeStr;

                    if (!powerText.enabled)
                    {
                        powerText.enabled = true;
                    }

                    //powerText.RefreshList(new System.Collections.Generic.List<string>() { $"{currentPhyPower}/{maxPhyPower}", $"{UIHelper.GetFormatTime(lastTime)}" }, false);
                    powerText.RefreshList(powerTxt, false);
                }
                else
                {
                    if (powerText && powerText.enabled)
                    {
                        powerText.enabled = false;
                    }

                    powerNumText.text = phyPowerStr;
                    //powerText.RefreshList(new System.Collections.Generic.List<string>() { $"{currentPhyPower}/{maxPhyPower}" });
                }
            }
        }
    }
}