﻿ 



 
 


using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public sealed partial class TileGridObjectLayerData2 : MapLayerData
    {
        /*标记出这一层中一些特殊区域,做一些特殊操作,例如
         * 1.将这个区域内的物体都替换成另外一种
         * 2.隐藏这个区域代表的一个物体,例如区域的地板消失
        */
        public class SpecialArea
        {
            public int id;
            public float width;
            public float height;
            public Vector3 center;            
            public SpecialAreaShape shape;          //circle or rectangle
            public int state = 1;                   //处于哪种状态,0表示消失,区域内装饰物也还原成正常显示

            public float minX { get { return center.x - width * 0.5f; } }
            public float minZ { get { return center.z - height * 0.5f; } }
            public float maxX { get { return center.x + width * 0.5f; } }
            public float maxZ { get { return center.z + height * 0.5f; } }
            public float radius { get { return width * 0.5f; } }
        }

        public SpecialArea GetSpecialAreaByViewID(int viewID)
        {
            mViewIDToSpecialArea.TryGetValue(viewID, out SpecialArea specialArea);
            return specialArea;
        }

        //隐藏special area地板
        public void HideSpecialAreaObject(int areaID)
        {
            mSpecialAreas.TryGetValue(areaID, out var area);
            if (area != null && area.state > 0)
            {
                area.state = 0;
                RemoveRemovableObject(area.center);
            }
        }

        public void ShowSpecialAreaObject(int areaID)
        {
            mSpecialAreas.TryGetValue(areaID, out var area);
            if (area != null && area.state == 0)
            {
                area.state = 1;
                AddRemovableObject(area.center);
            }
        }

        //改变special area里的物体类型
        public void ChangeObjectsTypeInSpecialArea(int areaID, int state)
        {
            mSpecialAreas.TryGetValue(areaID, out var area);
            if (area != null)
            {
                area.state = state;
                ChangeObjectsGameObjectInRange(area.minX, area.minZ, area.maxX, area.maxZ, state);
            }
        }

        public void ChangeObjectsMaterialInSpecialArea(int areaID, int state)
        {
            mSpecialAreas.TryGetValue(areaID, out var area);
            if (area != null)
            {
                area.state = state;
                ChangeObjectsMaterialInRange(area.minX, area.minZ, area.maxX, area.maxZ, state);
            }
        }

        void ChangeObjectsMaterialInRange(float minX, float minZ, float maxX, float maxZ, int mtlType)
        {
            if (mtlType < 0)
            {
                Debug.Assert(false, "Invalid object type!");
                return;
            }
            GetObjectsInRange(new Vector3((minX + maxX) * 0.5f, 0, (minZ + maxZ) * 0.5f), maxX - minX, maxZ - minZ, mtlType);

            foreach (var p in mTemporaryObjects)
            {
                TileObjectData2 obj = p.Value;
                mOnObjectMaterialChange(p.Value, mtlType);
            }

            mTemporaryObjects.Clear();
        }

        void ChangeObjectsGameObjectInRange(float minX, float minZ, float maxX, float maxZ, int objectType)
        {
            if (objectType < 0)
            {
                Debug.Assert(false, "Invalid object type!");
                return;
            }
            GetObjectsInRange(new Vector3((minX + maxX) * 0.5f, 0, (minZ + maxZ) * 0.5f), maxX - minX, maxZ - minZ, objectType);

            foreach (var p in mTemporaryObjects)
            {
                TileObjectData2 obj = p.Value;
                string newPrefabPath = obj.prefabInfo.GetPrefabPath(objectType);
                if (!ReferenceEquals(obj.GetAssetPath(), newPrefabPath))
                {
                    obj.SetAssetPath(newPrefabPath);
                    mOnObjectTypeChange(p.Value);
                }
            }

            mTemporaryObjects.Clear();
        }

        void GetObjectsInRange(Vector3 center, float width, float height, int newObjectType)
        {
            float halfWidth = width * 0.5f;
            float halfHeight = height * 0.5f;

            var rect = new Rect(center.x - halfWidth, center.z - halfHeight, width, height);
            var range = GetViewRect(rect);
            int minX = range.xMin;
            int minY = range.yMin;
            int maxX = range.xMax;
            int maxY = range.yMax;

            mTemporaryObjects.Clear();

            for (int y = minY; y <= maxY; ++y)
            {
                for (int x = minX; x <= maxX; ++x)
                {
                    if (x >= 0 && x < mCols && y >= 0 && y < mRows)
                    {
                        var tileIndex = y * mCols + x;
                        mTileChildren.TryGetValue(tileIndex, out var tileChildren);
                        if (tileChildren != null)
                        {
                            foreach (var obj in tileChildren)
                            {
                                if (newObjectType < obj.prefabInfo.prefabPathForEachCustomType.Count)
                                {
                                    if (!IsObjectRemoved(obj.viewID))
                                    {
                                        var pos = obj.GetPosition();
                                        if (rect.Contains(new Vector2(pos.x, pos.z)))
                                        {
                                            mTemporaryObjects[obj.viewID] = obj;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public void SetObjectTypeChangeCallback(System.Action<TileObjectData2> onObjectTypeChange)
        {
            mOnObjectTypeChange = onObjectTypeChange;
        }

        public void SetObjectMaterialChangeCallback(System.Action<TileObjectData2, int> onObjectMaterialChange)
        {
            mOnObjectMaterialChange = onObjectMaterialChange;
        }

        public bool enableSpecialArea { get { return mSpecialAreas.Count > 0; } }    
        public bool enableObjectMaterialTypeChange { get { return mEnableObjectMaterialTypeChange; } }

        //从special area id到special area
        Dictionary<int, SpecialArea> mSpecialAreas = new Dictionary<int, SpecialArea>();
        //从view id索引special area
        Dictionary<int, SpecialArea> mViewIDToSpecialArea = new Dictionary<int, SpecialArea>();
        System.Action<TileObjectData2> mOnObjectTypeChange;
        System.Action<TileObjectData2, int> mOnObjectMaterialChange;
        bool mEnableObjectMaterialTypeChange = true;
    }
}
