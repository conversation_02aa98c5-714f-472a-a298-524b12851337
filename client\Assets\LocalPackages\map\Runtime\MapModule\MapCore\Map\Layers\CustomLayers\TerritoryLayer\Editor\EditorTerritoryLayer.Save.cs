﻿ 



 
 

#if UNITY_EDITOR

using System.IO;
using System.Collections.Generic;
using UnityEngine;

namespace TFW.Map
{
    public partial class EditorTerritoryLayer : MapLayerBase
    {
        public void Save(string dataPath)
        {
            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            Utils.WriteVersion(writer, VersionSetting.EditorTerritoryLayerEditorDataVersion);

            SaveTerritoryLayer(writer);

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        void SaveTerritoryLayer(BinaryWriter writer)
        {
            Utils.WriteString(writer, name);
            Utils.WriteVector3(writer, layerOffset);
            writer.Write(active);

            int subLayerCount = mSubLayers.Count;
            writer.Write(subLayerCount);
            for (int s = 0; s < subLayerCount; ++s)
            {
                var subLayer = mSubLayers[s];
                Utils.WriteString(writer, subLayer.name);
                writer.Write(subLayer.active);
                writer.Write(subLayer.verticalTileCount);
                writer.Write(subLayer.horizontalTileCount);
                writer.Write(subLayer.tileWidth);
                writer.Write(subLayer.tileHeight);
                writer.Write(subLayer.displayRadius);
                writer.Write(subLayer.meshGenerationParams.Count);
                var meshGenerationParams = subLayer.meshGenerationParams;
                for (int i = 0; i < meshGenerationParams.Count; ++i)
                {
                    writer.Write(meshGenerationParams[i].cornerSegment);
                    writer.Write(meshGenerationParams[i].borderSizeRatio);
                    writer.Write(meshGenerationParams[i].uvScale);
                    writer.Write(meshGenerationParams[i].curveCorner);
                    Utils.WriteString(writer, Utils.GetAssetGuid(meshGenerationParams[i].territoryMeshMaterial));
                }
                for (int i = 0; i < subLayer.verticalTileCount; ++i)
                {
                    for (int j = 0; j < subLayer.horizontalTileCount; ++j)
                    {
                        writer.Write(subLayer.grids[i, j]);
                    }
                }
                var territories = subLayer.territories;
                writer.Write(territories.Count);
                for (int i = 0; i < territories.Count; ++i)
                {
                    Utils.WriteString(writer, territories[i].name);
                    Utils.WriteColor(writer, territories[i].GetColor());
                    writer.Write(territories[i].id);
                    //save buildings
                    var buildings = territories[i].buildings;
                    writer.Write(buildings.Count);
                    for (int b = 0; b < buildings.Count; ++b)
                    {
                        Utils.WriteVector3(writer, buildings[b].gameObject.transform.position);
                    }

                    Utils.WriteProperties(writer, territories[i].properties);
                }

                //save curve region mesh param
                SaveCurveRegionMeshGenerationParam(writer, subLayer.curveRegionMeshGenerationParam);
                //save edge info
                var edgeAssetsInfo = subLayer.regionCreatorsForLODs[0].edgeAssetsInfo;
                writer.Write(edgeAssetsInfo.Count);
                for (int i = 0; i < edgeAssetsInfo.Count; ++i)
                {
                    var edge = edgeAssetsInfo[i];
                    writer.Write(edge.territoryID);
                    writer.Write(edge.neighbourTerritoyID);
                    Utils.WriteString(writer, edge.prefabPath);
                    Utils.WriteString(writer, Utils.GetAssetGuid(edge.material));
                }
                //save block info
                int blockCount = 0;
                List<CurveRegionCreator.Block> blockInfo = null;
                int lod1MaskTextureWidth = 0;
                int lod1MaskTextureHeight = 0;
                Color32[] lod1MaskTextureData = null;
                if (lodCount > 1)
                {
                    var creator = subLayer.regionCreatorsForLODs[1];
                    blockInfo = creator.blocks;
                    blockCount = blockInfo.Count;
                    lod1MaskTextureWidth = creator.lod1MaskTextureWidth;
                    lod1MaskTextureHeight = creator.lod1MaskTextureHeight;
                    lod1MaskTextureData = creator.lod1MaskTextureData;
                }

                //save mask texture data
                writer.Write(lod1MaskTextureWidth);
                writer.Write(lod1MaskTextureHeight);
                Utils.WriteColor32Array(writer, lod1MaskTextureData);

                writer.Write(blockCount);
                for (int i = 0; i < blockCount; ++i)
                {
                    var block = blockInfo[i];
                    Utils.WriteString(writer, block.prefabPath);
                    Utils.WriteBounds(writer, block.bounds);

                    int nRegions = block.regions.Count;
                    writer.Write(nRegions);
                    foreach (var region in block.regions)
                    {
                        writer.Write(region.territoryID);
                    }

                    int nEdges = block.edges.Count;
                    writer.Write(nEdges);
                    foreach (var edge in block.edges)
                    {
                        writer.Write(edge.territoryID);
                        writer.Write(edge.neighbourTerritoryID);
                    }
                }
                
                Utils.WriteString(writer, subLayer.exportFileName);
            }

            SaveMapLayerLODConfig(writer, mLayerLODConfig);
        }

        void SaveCurveRegionMeshGenerationParam(BinaryWriter writer, CurveRegionMeshGenerationParam param)
        {
            int n = param.lodParams.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                var lodParam = param.lodParams[i];
                writer.Write(lodParam.segmentLengthRatio);
                writer.Write(lodParam.minTangentLength);
                writer.Write(lodParam.maxTangentLength);
                writer.Write(lodParam.pointDeltaDistance);
                writer.Write(lodParam.maxPointCountInOneSegment);
                writer.Write(lodParam.moreRectangular);
                writer.Write(lodParam.lineWidth);
                writer.Write(lodParam.textureAspectRatio);
                writer.Write(lodParam.gridErrorThreshold);
                writer.Write(lodParam.useVertexColorForRegionMesh);
                writer.Write(lodParam.combineMesh);
                writer.Write(lodParam.mergeEdge);
                writer.Write(lodParam.edgeHeight);
                writer.Write(lodParam.shareEdge);
                Utils.WriteString(writer, Utils.GetAssetGuid(lodParam.edgeMaterial));
                Utils.WriteString(writer, Utils.GetAssetGuid(lodParam.regionMaterial));
            }

            writer.Write(param.vertexDisplayRadius);
            writer.Write(param.segmentLengthRatioRandomRange);
            writer.Write(param.tangentRotationRandomRange);
        }

        void SaveMapLayerLODConfig(BinaryWriter writer, MapLayerLODConfig config)
        {
            int n = 0;
            if (config != null)
            {
                n = config.lodConfigs.Length;
            }
            if (n == 0)
            {
                writer.Write(1);
                writer.Write(0);
                writer.Write(0);
                writer.Write(false);
                writer.Write(0);
                writer.Write(false);
                int flag = 0;
                writer.Write(flag);
                Utils.WriteString(writer, "");
            }
            else
            {
                writer.Write(n);
                for (int i = 0; i < n; ++i)
                {
                    var c = config.lodConfigs[i];
                    writer.Write(c.changeZoom);
                    writer.Write(c.changeZoomThreshold);
                    writer.Write(c.hideObject);
                    writer.Write(c.shaderLOD);
                    writer.Write(c.useRenderTexture);
                    writer.Write((int)c.flag);
                    Utils.WriteString(writer, c.name);
                }
            }
        }
    }
}

#endif