﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    [CreateAssetMenu(fileName = "border_clamp_config", menuName = "Assets/BorderClampConfig")]
    public class BorderClampConfig : CurveConfig
    {
        public BorderClampConfig()
        {
            mStartTime = 0;
            mStartValue = 15;
            mDeltaTime = 50;
            mDeltaValue = 5;
        }

        //相机在最低点时视野范围的最小偏移
        public float bottomMinOffset;
        //相机在最低点时视野范围的最大偏移
        public float bottomMaxOffset;
        //相机在最高点时视野范围的最小偏移
        public float topMinOffset;
        //相机在最高点时视野范围的最大偏移
        public float topMaxOffset;
        public float viewZOffsetMin;
        public float viewZOffsetMax;
    }
}
