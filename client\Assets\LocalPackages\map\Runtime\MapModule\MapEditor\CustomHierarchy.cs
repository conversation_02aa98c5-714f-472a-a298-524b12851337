﻿ 



 
 



//a hierarchy colorization tool to ease finding things in the Unity Hierarchy
//

#if UNITY_EDITOR

using UnityEngine;
using System.Collections;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace TFW.Map
{
    [InitializeOnLoad]
    [Black]
    static public class HierarchyCategories
    {
        static HierarchyCategories()
        {
            EditorApplication.hierarchyWindowItemOnGUI -= InspectHierarchyItem;
            EditorApplication.hierarchyWindowItemOnGUI += InspectHierarchyItem;
        }

        private static void InspectHierarchyItem(int instanceID, Rect selectionRect)
        {
            if (!EditorApplication.isPlaying)
            {
                var obj = EditorUtility.InstanceIDToObject(instanceID);
                if (obj is GameObject)
                {
                    var color = GetObjectColor(obj as GameObject);
                    GUI.backgroundColor = color;
                    GUI.Box(selectionRect, "");
                    GUI.backgroundColor = Color.white;
                }
            }
        }

        static Color GetObjectColor(GameObject obj)
        {
            if (obj.GetComponent<SLGMakerEditor>() != null)
            {
                return new Color32(255, 158, 24, 50);
            }
            else if (obj.GetComponent<MapEditor>() != null)
            {
                return new Color32(0, 255, 64, 50);
            }
            else if (obj.GetComponent<BlendTerrainLayerLogic>() != null)
            {
                return new Color32(0, 128, 255, 50);
            }
            else if (obj.GetComponent<SplitFogLayerLogic>() != null)
            {
                return new Color32(100, 100, 255, 50);
            }
            else if (obj.GetComponent<GridModelLayerLogic>() != null)
            {
                return new Color32(255, 128, 128, 50);
            }
            else if (obj.GetComponent<CircleBorderLayerLogic>() != null)
            {
                return new Color32(255, 0, 128, 50);
            }
            else if (obj.GetComponent<RuinLayerLogic>() != null)
            {
                return new Color32(20, 100, 180, 50);
            }
            else if (obj.GetComponent<ModelLayerLogic>() != null)
            {
                return new Color32(255, 0, 128, 50);
            }
            else if (obj.GetComponent<NPCRegionLayerLogic>() != null)
            {
                return new Color32(128, 255, 0, 50);
            }
            else if (obj.GetComponent<SpriteTileLayerLogic>() != null)
            {
                return new Color32(128, 128, 0, 50);
            }
            else if (obj.GetComponent<NavMeshLayerLogic>() != null)
            {
                return new Color32(255, 0, 0, 50);
            }
            else if (obj.GetComponent<EntitySpawnLayerLogic>() != null)
            {
                return new Color32(255, 201, 14, 50);
            }
            else if (obj.GetComponent<RailwayLayerLogic>() != null)
            {
                return new Color32(255, 128, 0, 50);
            }
            else if (obj.GetComponent<LODLayerLogic>() != null)
            {
                return new Color32(172, 0, 172, 50);
            }
            else if (obj.GetComponent<MapCollisionLayerLogic>() != null)
            {
                return new Color32(0, 162, 132, 50);
            }
            else if (obj.GetComponent<RegionLayerLogic>() != null)
            {
                return new Color32(200, 162, 132, 50);
            }
            else if (obj.GetComponent<PolygonRiverLayerLogic>() != null)
            {
                return new Color32(128, 128, 255, 50);
            }
            else if (obj.GetComponent<CameraColliderLayerLogic>() != null)
            {
                return new Color32(128, 0, 128, 50);
            }
            else if (obj.GetComponent<ComplexGridModelLayerLogic>() != null)
            {
                return new Color32(128, 255, 128, 50);
            }
            else if (obj.GetComponent<ObjectSetting>() != null)
            {
                return new Color32(255, 255, 0, 50);
            }
            return new Color32(0, 0, 0, 0);
        }
    }
}

#endif