﻿
using UnityEngine;

namespace RiverLog
{
    public static class LogHelper
    {
        /// <summary>
        /// 输出普通日志信息。
        /// </summary>
        /// <param name="message">要输出的消息。</param>
        /// <param name="prefix">消息前缀。</param>
        public static void Log(string message, string prefix = "")
        {
            Debug.Log($"[{prefix}] {message}");
        }

        /// <summary>
        /// 输出警告日志信息。
        /// </summary>
        /// <param name="message">要输出的消息。</param>
        /// <param name="prefix">消息前缀。</param>
        public static void LogWarning(string message, string prefix = "")
        {
            Debug.LogWarning($"[{prefix}] {message}");
        }

        /// <summary>
        /// 输出错误日志信息。
        /// </summary>
        /// <param name="message">要输出的消息。</param>
        /// <param name="prefix">消息前缀。</param>
        public static void LogError(string message, string prefix = "")
        {
            Debug.LogError($"[{prefix}] {message}");
        }
    }
}

