﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public class VaryingTileSizeTerrainLayerView : MapLayerView
    {
        class BigTileView
        {
            public int referenceCount = 0;
            public GameObject gameObject;
        }

        public VaryingTileSizeTerrainLayerView(MapLayerData layerData, bool asyncLoading)
            : base(layerData, asyncLoading)
        {
            mObjectPool = Map.currentMap.view.reusableGameObjectPool;
            CreateGameObjects();
        }

        //删除数据
        public override void OnDestroy()
        {
            base.OnDestroy();

            Clear();
        }

        public void Clear()
        {
            for (int i = 0; i < mTerrainTiles.Count; ++i)
            {
                var list = mTerrainTiles[i];
                int n1 = list.GetLength(0);
                int n2 = list.GetLength(1);
                for (int k = 0; k < n1; ++k)
                {
                    for (int j = 0; j < n2; ++j)
                    {
                        Utils.DestroyObject(list[k, j]);
                        list[k, j] = null;
                    }
                }
            }

            foreach (var p in mBigTileViews)
            {
                Utils.DestroyObject(p.Value.gameObject);
            }
            mBigTileViews.Clear();
        }

        void CreateGameObjects()
        {
            var terrainLayerData = layerData as RenderTextureVaryingTileSizeTerrainLayerData;
            var otherLODTiles = terrainLayerData.otherLODTiles;
            int rows = terrainLayerData.verticalTileCount;
            int cols = terrainLayerData.horizontalTileCount;
            int nDefaultLODs = terrainLayerData.defaultLODCount;
            int nLODs = terrainLayerData.lodConfig.lodConfigs.Length;
            mTerrainTiles = new List<GameObject[,]>(nLODs);
            for (int i = 0; i < nDefaultLODs; ++i)
            {
                mTerrainTiles.Add(new GameObject[rows, cols]);
            }

            if (otherLODTiles != null)
            {
                for (int i = nDefaultLODs; i < nLODs; ++i)
                {
                    int r = otherLODTiles[i - nDefaultLODs].GetLength(0);
                    int c = otherLODTiles[i - nDefaultLODs].GetLength(1);
                    mTerrainTiles.Add(new GameObject[r, c]);
                }
            }
        }

        public void OnObjectActiveStateChange(IMapObjectData tileData, int x, int y, int lod)
        {
            if (tileData.IsObjActive())
            {
                ShowObject(tileData, x, y, lod);
            }
            else
            {
                HideObject(tileData, x, y, lod);
            }
        }

        //显示地图对象的模型
        public void ShowObject(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mTerrainTiles.Count)
            {
                lod = mTerrainTiles.Count - 1;
            }
            var tile = data as VaryingTileSizeTerrainTileData;
            string prefabPath = data.GetAssetPath(lod);
            GameObject obj = null;
            if (tile != null && tile.bigTile != null)
            {
                int bigTileID = tile.bigTile.id;
                mBigTileViews.TryGetValue(bigTileID, out var bigTileView);
                if (bigTileView == null)
                {
                    bigTileView = new BigTileView();
                    mBigTileViews[bigTileID] = bigTileView;
                }
                if (bigTileView.referenceCount == 0)
                {
                    obj = mObjectPool.Require(prefabPath);
                    obj.transform.localPosition = layerData.FromCoordinateToWorldPosition(tile.bigTile.x, tile.bigTile.y);
                    obj.transform.SetParent(root.transform, false);
                    bigTileView.gameObject = obj;
                }
                ++bigTileView.referenceCount;
            }
            else
            {
#if UNITY_EDITOR
                if (mTerrainTiles[lod][y, x] != null && mTerrainTiles[lod][y, x].gameObject != null)
                {
                    Debug.Assert(false, string.Format("Show object at lod {0} {1}_{2} failed!", lod, x, y));
                }
#endif
                obj = mObjectPool.Require(prefabPath);
                obj.transform.localPosition = data.GetPosition();
                obj.transform.SetParent(root.transform, false);
                mTerrainTiles[lod][y, x] = obj;
            }

            if (obj != null)
            {
#if UNITY_EDITOR
                if (Map.currentMap.isEditorMode)
                {
                    if (obj.GetComponent<DisableKeyboardDelete>() == null)
                    {
                        obj.AddComponent<DisableKeyboardDelete>();
                    }

                    var renderer = obj.GetComponentInChildren<MeshRenderer>();
                    if (renderer != null && renderer.sharedMaterial != null)
                    {
                        renderer.sharedMaterial.SetColor("_GlobalDayNightColor", new Color(1, 1, 1, 1));
                        renderer.sharedMaterial.SetFloat("_ToneMappingMin", 0);
                        renderer.sharedMaterial.SetFloat("_ToneMappingMax", 1);
                    }
                }
#endif
            }
        }

        //隐藏地图对象的模型
        public void HideObject(IMapObjectData data, int x, int y, int lod)
        {
            if (lod >= mTerrainTiles.Count)
            {
                lod = mTerrainTiles.Count - 1;
            }

            var tile = data as VaryingTileSizeTerrainTileData;
            if (tile != null && tile.bigTile != null)
            {
                mBigTileViews.TryGetValue(tile.bigTile.id, out var bigTileView);
                --bigTileView.referenceCount;
                Debug.Assert(bigTileView.referenceCount >= 0);
                if (bigTileView.referenceCount == 0)
                {
                    string prefabPath = data.GetAssetPath(lod);
                    mObjectPool.Release(prefabPath, bigTileView.gameObject, layerData.map);
                    bigTileView.gameObject = null;
                }
            }
            else
            {
#if UNITY_EDITOR
                if (mTerrainTiles[lod][y, x] == null)
                {
                    Debug.Assert(false, string.Format("Hide object at lod {0} {1}_{2} failed!", lod, x, y));
                }
#endif
                GameObject obj = mTerrainTiles[lod][y, x];
                string prefabPath = data.GetAssetPath(lod);
                mObjectPool.Release(prefabPath, obj, layerData.map);
                mTerrainTiles[lod][y, x] = null;
            }
        }
       
        public override void SetZoom(float zoom, bool lodChanged) { }
        public override void ReloadVisibleViews() { }

        public void SetSize(int newHorizontalTileCount, int newVerticalTileCount)
        {
            if (!Map.currentMap.isEditorMode)
            {
                return;
            }
            Clear();
            mTerrainTiles = new List<GameObject[,]>(1);
            mTerrainTiles.Add(new GameObject[newVerticalTileCount, newHorizontalTileCount]);
        }

        public void GetTerrainTileMeshAndGameObject(int x, int y, out Mesh mesh, out GameObject gameObject)
        {
            mesh = null;
            gameObject = null;
            var varingTileSizeTerrainLayerData = layerData as VaryingTileSizeTerrainLayerData;
            var tileData = varingTileSizeTerrainLayerData.GetTile(x, y) as VaryingTileSizeTerrainTileData;
            if (tileData != null)
            {
                if (tileData.bigTile != null)
                {
                    gameObject = mBigTileViews[tileData.bigTile.id].gameObject;
                }
                else
                {
                    gameObject = mTerrainTiles[0][y, x];
                }

                var filters = gameObject.GetComponentsInChildren<MeshFilter>();
                if (filters != null)
                {
                    for (int i = 0; i < filters.Length; ++i)
                    {
                        if (filters[i].tag != MapCoreDef.MAP_IGNORE_MESH_TAG)
                        {
                            mesh = filters[i].sharedMesh;
                        }
                    }
                }
            }
        }

        List<GameObject[,]> mTerrainTiles;
        Dictionary<int, BigTileView> mBigTileViews = new Dictionary<int, BigTileView>();

        GameObjectPool mObjectPool;
    };
}