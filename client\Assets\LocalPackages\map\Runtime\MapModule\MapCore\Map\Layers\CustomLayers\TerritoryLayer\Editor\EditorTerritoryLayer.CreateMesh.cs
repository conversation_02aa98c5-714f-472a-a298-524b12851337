﻿#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class EditorTerritoryLayer : MapLayerBase
    {
        public void DestroyPreviewObjects(int layer)
        {
            mSubLayers[layer].DestroyPreviewObjects();
        }

        public void ClearPreview(int layer)
        {
            mSubLayers[layer].ClearPreview();
        }

        public void CreatePreview(int layer, int lod)
        {
            mSubLayers[layer].CreatePreview(lod);
        }
    }
}

#endif