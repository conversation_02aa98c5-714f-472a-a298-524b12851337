%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TCP2_Demo Robot PBS
  m_Shader: {fileID: 4800000, guid: 80412fe88398cd147be3aacebe2c3fe3, type: 3}
  m_ShaderKeywords: OUTLINES TCP2_COLORS_AS_NORMALS TCP2_DISABLE_WRAPPED_LIGHT TCP2_SPEC_TOON
    TCP2_STYLIZED_FRESNEL TCP2_UV_NORMALS_FULL _EMISSION _NORMALMAP
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Bump:
        m_Texture: {fileID: 2800000, guid: 94c473c9137744d7eabd829f1150da8d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: d13af914022d95848adfec9d2951e5ae, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cube:
        m_Texture: {fileID: 8900000, guid: 32cf25e1cd773ef43b3930a506e1b214, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 90f5c2a59e018304bb00979205857f25, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendOutline: 10
    - _EnableOutline: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.7
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _NormalsSource: 1
    - _NormalsUVType: 0
    - _OcclusionStrength: 1
    - _Offset1: 0
    - _Offset2: 0
    - _Outline: 0.4
    - _Parallax: 0.02
    - _RampSmooth: 0.15
    - _RampSmoothAdd: 0.5
    - _RampThreshold: 0.5
    - _RimMax: 0.9
    - _RimMin: 0.6
    - _RimPower: -1.2835822
    - _RimStrength: 0.3
    - _Shininess: 0.35
    - _SmoothnessTextureChannel: 0
    - _SpecBlend: 1
    - _SpecSmooth: 1
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendOutline: 5
    - _TCP2_DISABLE_WRAPPED_LIGHT: 1
    - _TCP2_OUTLINE_CONST_SIZE: 0
    - _TCP2_OUTLINE_TEXTURED: 0
    - _TCP2_RAMPTEXT: 0
    - _TCP2_SPEC_TOON: 1
    - _TCP2_STYLIZED_FRESNEL: 1
    - _TCP2_ZSMOOTH_ON: 0
    - _TexLod: 5
    - _UVSec: 0
    - _ZSmooth: -0.5
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.2509804, g: 0.2509804, b: 0.2509804, a: 0.5882353}
    - _ReflectColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.2, g: 0.6, b: 1, a: 0.5}
    - _RimDir: {r: 0, g: 0, b: 1, a: 0}
    - _SColor: {r: 0.23484801, g: 0.43726364, b: 0.716, a: 1}
    - _SpecColor: {r: 0.60294116, g: 0.60294116, b: 0.60294116, a: 1}
