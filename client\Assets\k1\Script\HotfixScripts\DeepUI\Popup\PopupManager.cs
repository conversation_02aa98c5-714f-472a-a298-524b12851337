﻿using Cysharp.Threading.Tasks;
using Game.Config;
using Game.Data;
using System;
using System.Collections.Generic;
using TFW;
using TFW.Map;
using THelper;
using UI;
using UnityEngine;

namespace DeepUI
{
    /// <summary>
    /// 弹出层管理器。用于弹出全屏、半屏面板或简单对话框，并对全部弹出UI进行统一控制（打包隐藏、直接清除、释放显示等）。
    /// 
    /// <br/>https://wiki..com/pages/viewpage.action?pageId=64121653
    /// <br/>https://wiki..com/pages/viewpage.action?pageId=64121655
    /// </summary>
    public class PopupManager : Ins<PopupManager>
    {
        private const string DEFAULT_PIPELINE = "DEFAULT-POPUP-PIPELINE";

        /// <summary>
        /// 打开界面前的回调
        /// </summary>
        public Func<string, bool> PopupBeforeShowAction;

        /// <summary>
        /// 关闭界面后的回调
        /// </summary>
        public Func<string, bool> PopupAfterCloseAction;

        public Dictionary<Type, BasePopup> PopupPool;
        
        /// <summary>
        /// 是否有任何弹板显示（或即将显示），包括全部三类
        /// </summary>
        public bool AnyPopup => layerController.AnyPopup;

        /// <summary>
        /// 是否有任何弹板显示（或即将显示），包括全部二类
        /// </summary>
        //public bool AnyPopup_NOLayer => panelController.AnyPopup || dialogController.AnyPopup;

        /// <summary>
        /// 是否有任何全屏弹板显示（或即将显示），仅包括全屏遮挡 Layer
        /// </summary>
        public bool AnyLayer => layerController.AnyPopup;
        
        //public bool AnyWidget => widgetController.AnyPopup;

        public GameObject deepUI;

        private PopupController layerController;
        //private PopupDialogController panelController;
        //private PopupDialogController dialogController;

        //小控件管道，独立于其他管道，永远保持该管道内只有一个小部件
        //private PopupWidgetController widgetController;

        /// <summary>
        /// 上一次打开界面的类型
        /// </summary>
        //private int lastUIType = CustomSortingLayer.MainUI;

        /// <summary>
        /// 临时遮罩，异步加载加载UI时防止点击操作
        /// </summary>
        public GameObject TemporaryMask;

        /// <summary>
        /// UI相机
        /// </summary>
        public UnityEngine.Camera UICamera;

        public CanvasGroup WaitingCanvasGroup;

        /// <summary>
        /// 初始化弹出层管理器
        /// </summary>
        /// <param name="deepUIRoot">弹出对象挂载根节点对象</param>
        /// <param name="defaultPipeline">默认弹出管道名，默认值取常量 PopupManager.DEFAULT_PIPELINE</param>
        public void Init(GameObject deepUIRoot, string defaultPipeline = DEFAULT_PIPELINE)
        {
            if (!deepUIRoot)
                return;

            deepUI = deepUIRoot;

            var deepUITransform = deepUI.transform;
            var layerUITransform = deepUITransform.Find("LayerUI");
            var panelUITransform = deepUITransform.Find("PanelUI");
            var dialogUITransform = deepUITransform.Find("DialogUI");
            var widgetUITransform = deepUITransform.Find("WidgetUI");
            deepUITransform.localPosition = Vector3.zero;


            WaitingCanvasGroup= deepUITransform.Find("DeepWaitingUI").GetComponent<CanvasGroup>();

            UICamera = GameObject.FindWithTag("UICamera").GetComponent<UnityEngine.Camera>();

            TemporaryMask = deepUITransform.Find("Mask").gameObject;
            if (TemporaryMask)
            {
                var c = TemporaryMask.GetComponent<Canvas>();
                c.renderMode = RenderMode.ScreenSpaceCamera;
                c.worldCamera = UICamera;
                c.sortingLayerID = SortingLayer.NameToID("PopupWidget");

                TemporaryMask.SetActive(false);
            }

            layerController = layerUITransform.gameObject.AddComponent<PopupLayerController>();
            //panelController = panelUITransform.gameObject.AddComponent<PopupDialogController>();
            //dialogController = dialogUITransform.gameObject.AddComponent<PopupDialogController>();
            //widgetController = widgetUITransform.gameObject.AddComponent<PopupWidgetController>();

            //panelController.DecorateInteractionMask(layerController);
            //dialogController.DecorateInteractionMask(panelController);
            //widgetController.DecorateInteractionMask(dialogController);

            layerController.Init(SortingLayer.NameToID("PopupLayer"), 120, defaultPipeline);
            //panelController.Init(SortingLayer.NameToID("PopupPanel"), 90, defaultPipeline);
            //dialogController.Init(SortingLayer.NameToID("PopupDialog"), 60, defaultPipeline);
            //widgetController.Init(SortingLayer.NameToID("PopupWidget"), 30, defaultPipeline);

            layerController.OnEmptyStateChange = UpdateMainCameraEnable;
            //layerController.popupEventListener = new IsolationHandler(panelController);

            UnityEngine.Object.DontDestroyOnLoad(deepUI);
        }
       
        /// <summary>
        /// 预先注册更多弹出管道，以便将各层级弹出面板进行可见空间隔离，即同一时间仅有一个弹出管道内的面板压栈会被显示出来。
        /// </summary>
        /// <param name="pipelines">需要预先注册的弹出管道集合</param>
        //public void RegistryPipelines(IEnumerable<string> pipelines)
        //{
        //    //layerController.RegistryPipelines(pipelines);
        //    //panelController.RegistryPipelines(pipelines);
        //    //dialogController.RegistryPipelines(pipelines);
        //    //widgetController.RegistryPipelines(pipelines);
        //}

        #region 打开界面方法

        public int GetMaxOrderNum(BasePopup popup)
        {
            return  layerController.GetMaxOrderNum(popup);
        }

        public void ShowLayer<T>(
           object data = null, string pipeline = null, Priority priority = Priority.High, Action<bool> onComplete = null)
           where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            //if (lastUIType == CustomSortingLayer.Dialog)
            //{
            //    ClearAllDialog((b) =>
            //    {
            //        if (b)
            //        {
                        
            //            layerController.ShowPopup<T>(data, pipeline, priority, (b1) =>
            //            {
            //                PopupManager.I.TemporaryMask.SetActive(false);
            //                onComplete?.Invoke(b1);
            //                if (b1)
            //                    lastUIType = CustomSortingLayer.Layer;
            //            });
            //        }
            //    });
            //}
            //else
            //{
                layerController.ShowPopup<T>(data, pipeline, priority, (b) =>
                {
                    //PopupManager.I.TemporaryMask.SetActive(false);
                    onComplete?.Invoke(b);
                    //if (b)
                    //    lastUIType = CustomSortingLayer.Layer;
                });
            //}
        }
         

        //public async UniTask<T> ShowLayerAsync<T>(object data = null, string pipeline = null, Priority priority = Priority.High)
        //    where T : BasePopup, new()
        //{
        //    var b = await layerController.ShowPopupAsync<T>(data, pipeline, priority);
        //    if (b)
        //        lastUIType = CustomSortingLayer.Layer;
        //    return layerController.FindPopup(typeof(T)) as T;
        //}

        public async UniTask<BasePopup> ShowLayerAsync(Type uitype,object data = null, string pipeline = null, Priority priority = Priority.High)
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync(uitype,data, pipeline, priority);
            //if (b)
            //{
            //    lastUIType = CustomSortingLayer.Layer;
            //}
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(uitype);
        }

        /// <summary>
        /// 显示新的非全屏面板。非全屏面板对象将显示在弹出层偏前端（相对接近摄像机屏幕，遮挡全屏面板层级）
        /// </summary>
        /// <param name="data">数据对象</param>
        /// <param name="pipeline">指定弹出管道，默认值 null 表明显示在当前管道即可</param>
        /// <param name="priority">显示优先级</param>
        /// <param name="onComplete">弹出完毕的回调，回传参数指明面板是否当即被显示</param>
        /// <typeparam name="T">面板类型定义</typeparam>
        public void ShowPanel<T>(
            object data = null, string pipeline = null, Priority priority = Priority.High, Action<bool> onComplete = null)
            where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            layerController.ShowPopup<T>(data, pipeline, priority, (b) =>
            {
                //PopupManager.I.TemporaryMask.SetActive(false);
                onComplete?.Invoke(b);
                //if (b)
                //    lastUIType = CustomSortingLayer.Layer;
            });
        }

        public async UniTask<T> ShowPanelAsync<T>(object data = null, string pipeline = null, Priority priority = Priority.High)
            where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync<T>(data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(typeof(T)) as T;
        }

        public async UniTask<BasePopup> ShowPanelAsync(Type uitype, object data = null, string pipeline = null, Priority priority = Priority.High)
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync(uitype, data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(uitype);
        }

        /// <summary>
        /// 显示新的简单对话框。对话框对象将显示在弹出层最前端（最接近摄像机屏幕，遮挡其他非全屏及全屏面板层级）
        /// </summary>
        /// <param name="data">数据对象</param>
        /// <param name="pipeline">指定弹出管道，默认值 null 表明显示在当前管道即可</param>
        /// <param name="priority">显示优先级</param>
        /// <param name="onComplete">弹出完毕的回调，回传参数指明面板是否当即被显示</param>
        /// <typeparam name="T">对话框类型定义</typeparam>
        public void ShowDialog<T>(
             object data = null, string pipeline = null, Priority priority = Priority.High, Action<bool> onComplete = null)
             where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            layerController.ShowPopup<T>(data, pipeline, priority, (b) =>
            {
                //PopupManager.I.TemporaryMask.SetActive(false);
                onComplete?.Invoke(b);
                //if (b)
                //    lastUIType = CustomSortingLayer.Layer;
            });
        }

        public async UniTask<T> ShowDialogAsync<T>(object data = null, string pipeline = null, Priority priority = Priority.High)
            where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync<T>(data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(typeof(T)) as T;
        }

        public async UniTask<BasePopup> ShowDialogAsync(Type uitype, object data = null, string pipeline = null, Priority priority = Priority.High)
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync(uitype, data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(uitype);
        }

        public void ShowWidget<T>(object data = null, string pipeline = null, Priority priority = Priority.High, Action<bool> onComplete = null)
          where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            layerController.ShowPopup<T>(data, pipeline, priority, (b) =>
            {
                //PopupManager.I.TemporaryMask.SetActive(false);
                onComplete?.Invoke(b);
                //if (b)
                //    lastUIType = CustomSortingLayer.Layer;
            });
        }


        public async UniTask<T> ShowWidgetAsync<T>(object data = null, string pipeline = null, Priority priority = Priority.High)
             where T : BasePopup, new()
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync<T>(data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(typeof(T)) as T;
        }

        public async UniTask<BasePopup> ShowWidgetAsync(Type uitype, object data = null, string pipeline = null, Priority priority = Priority.High)
        {
            //PopupManager.I.TemporaryMask.SetActive(true);
            var b = await layerController.ShowPopupAsync(uitype, data, pipeline, priority);
            //if (b)
            //    lastUIType = CustomSortingLayer.Layer;
            //PopupManager.I.TemporaryMask.SetActive(false);
            return layerController.FindPopup(uitype);
        }

        #endregion

        #region 处理整个管道方法, Clear方法 不允许功能模块调用，仅在Dispose时调用
        /// <summary>
        /// 清除当前弹出管道内的全部面板（包括全屏及非全屏面板、简单对话框等全部内容）
        /// </summary>
        /// <param name="onComplete">清除完毕后的回调，回传参数指明是否成功</param>
        private void ClearAllLayer(Action<bool> onComplete = null)
        {
            ClearAllPanel(_ => layerController.ClearAllPopup(onComplete));
        }

        /// <summary>
        /// 清除当前弹出管道内的非全屏面板及简单对话框等内容，全屏面板不受此影响
        /// </summary>
        /// <param name="onComplete">清除完毕后的回调，回传参数指明是否成功</param>
        private void ClearAllPanel(Action<bool> onComplete = null)
        {
            ClearAllDialog(_ => layerController.ClearAllPopup(onComplete));
        }

        /// <summary>
        /// 清除当前弹出管道内的简单对话框，非全屏面板及全屏面板不受此影响
        /// </summary>
        /// <param name="onComplete">清除完毕后的回调，回传参数指明是否成功</param>
        private void ClearAllDialog(Action<bool> onComplete = null)
        {
            layerController.ClearAllPopup(onComplete);
        }

        /// <summary>
        /// 清除当前弹出管道内的小部件，其他管道均不受影响
        /// </summary>
        /// <param name="onComplete"></param>
        //private void ClearAllWidget(Action<bool> onComplete = null)
        //{
        //    widgetController.ClearAllPopup(onComplete);
        //}

        /// <summary>
        /// 清除当前弹出管道内的全部面板（包括全屏及非全屏面板、简单对话框等全部内容），并将弹出管道切换至指定
        /// </summary>
        /// <param name="targetPipeline">切换到指定弹出管道</param>
        /// <param name="onComplete">清除完毕后的回调，回传参数指明是否成功</param>
        //public void ClearAllPopup(string targetPipeline, Action<bool> onComplete = null)
        //{
        //    dialogController.PackageAllPopup(targetPipeline, m =>
        //    {
        //        panelController.PackageAllPopup(targetPipeline, n => layerController.PackageAllPopup(targetPipeline, onComplete, true), true);
        //    }, true);
        //}

        /// <summary>
        /// 场景切换时使用，清理所有Layer、Panel、Dialog，其他时候切勿使用
        /// </summary>
        /// <param name="onComplete"></param>
        public void ClearAllPopup(Action<bool> onComplete = null)
        {
            ClearAllLayer(onComplete);
        }

        /// <summary>
        /// 打包隐藏当前弹出管道内的全部面板（包括全屏及非全屏面板、简单对话框等全部内容），并将弹出管道切换至指定。当任意时间弹出管道被切换回来后，可以通过 ReleaseAllPopup 将打包的面板再次释放显示，恢复到 PackageAllPopup 调用之前的显示状态。
        /// </summary>
        /// <param name="targetPipeline">切换到指定弹出管道</param>
        /// <param name="onComplete">打包完毕后的回调，回传参数指明是否成功</param>
        //public void PackageAllPopup(string targetPipeline, Action<bool> onComplete = null)
        //{
        //    dialogController.PackageAllPopup(targetPipeline, m =>
        //    {
        //        panelController.PackageAllPopup(targetPipeline, n => layerController.PackageAllPopup(targetPipeline, onComplete, true));
        //    });
        //}

        /// <summary>
        /// 通过 ClearAllPopup 或 PackageAllPopup 切换到新的弹出管道后，调用此方法将释放之前被打包隐藏的面板，恢复到对其执行 PackageAllPopup 调用之前的显示状态
        /// </summary>
        /// <param name="onComplete">释放完毕后的回调，回传参数指明是否成功</param>
        //public void ReleaseAllPopup(Action<bool> onComplete = null)
        //{
        //    dialogController.ReleaseAllPopup(m =>
        //    {
        //        layerController.ReleaseAllPopup(n => panelController.ReleaseAllPopup(onComplete));
        //    });
        //}

        #endregion

        /// <summary>
        /// 刷新弹窗堆栈里数据显示
        /// </summary>
        //public string UpdatePopupStackUIObject()
        //{
        //    var name = widgetController?.GetStackUIObjectName();
        //    if(string.IsNullOrEmpty(name))
        //    {
        //        name = dialogController?.GetStackUIObjectName();
        //        if (string.IsNullOrEmpty(name))
        //        {
        //            name = panelController?.GetStackUIObjectName();
        //            if (string.IsNullOrEmpty(name))
        //            {
        //                name = layerController?.GetStackUIObjectName();
        //            }
        //        }
        //    }

        //    return name;
        //}

        /// <summary>
        /// 释放当前弹出层最顶端显示（最接近摄像机屏幕）的面板，如同用户主动关闭改面板一样；
        /// 此接口可用于响应手机返回键触发。
        /// </summary>
        /// <returns>是否有面板被关闭</returns>
        public bool PopBack()
        {
            //if (widgetController)
            //    widgetController.ClearAllPopup();

            //if (dialogController)
            //{
            //    if (dialogController.PopBack() || panelController.PopBack())
            //    {
            //        return true;
            //    }

                if (layerController.PopBack())
                {

                    return true;
                }
            //}
               
            return false;
        }

        /// <summary>
        /// 关闭界面 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="checkEnable">是否检查当前界面打开状态，不显示的UI不关闭，默认生效</param>
        /// <returns></returns>
        public bool ClosePopup<T>(bool checkEnable = true) where T : BasePopup
        {
            //var uiComplementPop = FindPopup<T>();
            //if (uiComplementPop != null || !checkEnable)
            //{
                return  layerController.ClosePopup<T>();
            //}

            //return false;
        }

        /// <summary>
        /// 关闭一些面板
        /// </summary>
        /// <param name="uiList"></param>
        public void ClosePopupList(List<Type> uiList, Action onComplete = null)
        {
            if (uiList == null || uiList.Count == 0)
                return;

            var count = uiList.Count;
            for (int i = 0; i < count; i++)
            {
                //var ui = FindPopup(uiList[i]);
                //if (ui != null)
                ClosePopup(uiList[i]);
            }

            onComplete?.Invoke();
        }

        public bool ClosePopup(Type type)
        {
            //var popup = FindPopup(type);
            //if (popup == null)
            //    return false;

            return   layerController.ClosePopup(type);
        }

        /// <summary>
        /// 根据泛型清理某一个固定类型的上层数据信息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="onComplete"></param>
        /// <returns></returns>
        public bool ClearPanel<T>(Action<bool> onComplete = null) where T : BasePopup, new()
        {
            return layerController.ClosePopup<T>();
        }

        /// <summary>
        /// 注销所有管道数据
        /// </summary>
        /// <param name="onComplete"></param>
        public void DisposeAllPopup(Action onComplete)
        {
            ClearAllLayer(success =>
            {
                //dialogController.DisposeAllPopup();
                //panelController.DisposeAllPopup();
                layerController.DisposeAllPopup();
                onComplete?.Invoke();
            });

            //ClearAllWidget(success =>
            //{
            //    widgetController.DisposeAllPopup();
            //});

            //lastUIType = CustomSortingLayer.MainUI;

        }

        /// <summary>
        /// 销毁某个界面
        /// </summary>
        /// <typeparam name="T"></typeparam>
        internal void DisposePopup<T>() where T : BasePopup, new()
        {
            //dialogController.DisposePopup<T>();
            //panelController.DisposePopup<T>();
            layerController.DisposePopup<T>();
        }


        /// <summary>
        /// 查找正在显示状态的面板实例
        /// </summary>
        /// <typeparam name="T">面板实例类型</typeparam>
        /// <returns>面板实例，或者 null（面板未弹出或者未处于显示状态）</returns>
        public T FindPopup<T>() where T : BasePopup
        {
            return FindPopup(typeof(T)) as T;
        }

        /// <summary>
        /// 查找除了T类型之外当前正在打开的界面数量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public int FindPopupCountExceptType<T>() where T : BasePopup
        {
            var type = typeof(T);
            return FindPopupCountExceptType(type);
        }

        /// <summary>
        /// 查找除了T类型之外当前正在打开的界面数量（小部件管道不加入计算）
        /// </summary>
        /// <returns></returns>
        public int FindPopupCountExceptType(Type type)
        {
            //var dialogCount = dialogController.FindPopupCountExceptType(type);
            //var panelCount = panelController.FindPopupCountExceptType(type);
            var layerCount = layerController.FindPopupCountExceptType(type);

            return  layerCount;
        }

        /// <summary>
        /// 查找正在显示状态的面板实例
        /// </summary>
        /// <para name="type">面板实例类型</para>
        /// <returns>面板实例，或者 null（面板未弹出或者未处于显示状态）</returns>
        public BasePopup FindPopup(Type type)
        {
            //var popup = layerController.FindPopupFromPool(type); //Widget小部件界面可能同时存在多个，所以不能只用栈顶界面查找
            //if (popup != null && popup.Context != null)
            //{
            //    return popup;
            //}

            //popup = dialogController.FindPopup(type);
            //if (popup != null)
            //{
            //    return popup;
            //}

            //popup = panelController.FindPopup(type);
            //if (popup != null)
            //{
            //    return popup;
            //}

            return layerController.FindPopup(type);
        }

        public BasePopup FindAllPopup(Type type)
        {
            //var popup = widgetController.FindPopupFromPool(type);
            //if (popup != null)
            //{
            //    return popup;
            //}

            //popup = dialogController.FindPopupFromPool(type);
            //if (popup != null)
            //{
            //    return popup;
            //}

            //popup = panelController.FindPopupFromPool(type);
            //if (popup != null)
            //{
            //    return popup;
            //}

            var popup = layerController.FindPopupFromPool(type);
            if (popup != null)
            {
                return popup;
            }

            return SceneManager.I.FindScene(type);
        }


        //public void SetPanelMaskDisableing(bool disablePanelMask)
        //{
        //    if (disablePanelMask)
        //    {
        //        panelController.DisableMask();
        //    }
        //    else
        //    {
        //        panelController.EnabelMask();
        //    }
        //}


        /// <summary>
        /// 查找缓存池中的弹窗资源
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T FindPopupFromPool<T>() where T : BasePopup
        {
            //var popup = widgetController.FindPopupFromPool<T>();
            //if (popup != null)
            //{
            //    return popup;
            //}

            //popup = dialogController.FindPopupFromPool<T>();
            //if (popup != null)
            //{
            //    return popup;
            //}

            //popup = panelController.FindPopupFromPool<T>();
            //if (popup != null)
            //{
            //    return popup;
            //}

            return layerController.FindPopupFromPool<T>();
        }

        #region 兼容旧版本参数列表

        public void ShowLayer<T>(string _, PopupData data = null) where T : BasePopupLayer, new()
        {
            ShowLayer<T>(data);
        }

        public void ShowPanel<T>(string _, PopupData data = null) where T : BasePopup, new()
        {
            ShowPanel<T>(data);
        }

        #endregion

        /// <summary>
        /// 刷新主相机的显隐操作
        /// </summary>
        /// <param name="isEmpty">显示队列是否为空</param>
        private void UpdateMainCameraEnable(bool isEmpty)
        {
            switch (GameData.I.MainData.CurrMenuType)
            {
                case MainMenuType.CITY:
                    GameInstance.CityMainCameraEnable(isEmpty);
                    break;
                case MainMenuType.WORLD:
                    MapMgr.SetCameraEnable(isEmpty);
                    break;
            }

        }
        
        #region 预加载

        Dictionary<Type, AsyncLazy<BasePopup>> _pendingPreload = new Dictionary<Type, AsyncLazy<BasePopup>>();

        /// <summary>
        /// 预加载
        /// </summary>
        public async UniTask PreloadPopupAsync(Type classType)
        {
            var fullAssetPath = PopupController.GetPopupAssetPath(classType);
            await ResourceMgr.PreloadAsync(fullAssetPath);
        }
        
        /// <summary>
        /// 预加载
        /// </summary>
        public async UniTask PreloadPopupAsync<T>() where T : BasePopup, new()
        {
            await this.PreloadPopupAsync(typeof(T));
        }
        
        /// <summary>
        /// 加载
        /// </summary>
        public UniTask<BasePopup> LoadPopupAsync(Type classType, Transform parent)
        {
            return this.LoadPopupAsyncImpl(classType, parent);
        }

        public BasePopup LoadPopup(Type classType, Transform parent)
        {
            var fullAssetPath = PopupController.GetPopupAssetPath(classType);
             
            var obj =  ResourceMgr.LoadInstance(fullAssetPath, parent);
            if (obj != null)
                obj.SetActive(false);
         
            if (obj != null)
            {
                obj.SetActive(true);
                return BasePopup.Create(classType, obj);
            }
            else
            {
                D.Info?.Log($"LoadPopupFromResource Failure {classType} fullAssetPath: {fullAssetPath}");
            }

            return null;
        }

        /// <summary>
        /// 加载
        /// </summary>
        public UniTask<BasePopup> LoadPopupAsync<T>(Transform parent) where T : BasePopup, new()
        {
            return this.LoadPopupAsyncImpl(typeof(T), parent);
        }
        
        public void ReleasePopup(Type classType)
        {
            var fullAssetPath = PopupController.GetPopupAssetPath(classType);
            ResourceMgr.ReleaseInstance(fullAssetPath);
        }
        
        private async UniTask<BasePopup> LoadPopupAsyncImpl(Type classType, Transform parent)
        {
            if (this.PopupPool == null)
                return null;

            if (this.PopupPool.TryGetValue(classType, out var popup) && popup != null)
                return popup;

            if (!this._pendingPreload.TryGetValue(classType, out var lazyPopup))
            {
                lazyPopup = LoadPopupFromResourceAsync(classType, parent).ToAsyncLazy();
                this._pendingPreload[classType] = lazyPopup;
            }

            var p = await lazyPopup;

            if (this._pendingPreload.ContainsKey(classType))
            {
                this._pendingPreload.Remove(classType);
            }

            return p;
        }

         



        private static async UniTask<BasePopup> LoadPopupFromResourceAsync(Type classType, Transform parent)
        {
            var fullAssetPath = PopupController.GetPopupAssetPath(classType);
            
            var startTime = Time.time;
            var obj = await ResourceMgr.LoadInstanceAsync(fullAssetPath, parent);
            if (obj != null)
                obj.SetActive(false);
            var endTime = Time.time;
            var dur = endTime - startTime;
            await UniTask.Delay(TimeSpan.FromSeconds(dur * 1f));

            if (obj != null)
            { 
                obj.SetActive(true);
                return BasePopup.Create(classType, obj);
            }
            else
            {
                D.Info?.Log($"LoadPopupFromResource Failure {classType} fullAssetPath: {fullAssetPath}");
            }

            return null;
        }
        #endregion
    }

    /// <summary>
    /// 面板弹出优先级，决定了当前弹出面板的显示时机。
    /// </summary>
    public enum Priority
    {
        /// <summary>
        /// 低优先级，新的面板将在当前管道内的全部面板都被关闭后才会显示。
        /// </summary>
        Low,

        /// <summary>
        /// 普通优先级，新的面板将在当前显示面板被关闭后立即显示。
        /// </summary>
        Normal,

        /// <summary>
        /// 高优先级，新的面板将被立即显示，而当前显示面板将被压栈隐藏，直到新面板被关闭后才会恢复显示。
        /// </summary>
        High
    }


}
