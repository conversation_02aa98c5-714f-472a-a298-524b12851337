﻿ 



 
 


#if UNITY_EDITOR
using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class AnimationBaker
    {
        protected class LODInfo
        {
            public LODInfo(LOD lod)
            {
                transitionHeight = lod.screenRelativeTransitionHeight;
                renderers = new string[lod.renderers.Length];
                for (int i = 0; i < renderers.Length; ++i)
                {
                    if (lod.renderers[i] != null)
                    {
                        renderers[i] = lod.renderers[i].name;
                    }
                }
            }
            //每个lod下所有的renderer的名称
            public string[] renderers;
            public float transitionHeight;
        }

        protected class LODGroupInfo
        {
            public LODGroupInfo(LODGroup group)
            {
                gameObjectName = group.gameObject.name;
                var groupLODs = group.GetLODs();
                lods = new LODInfo[groupLODs.Length];
                for (int i = 0; i < lods.Length; ++i)
                {
                    lods[i] = new LODInfo(groupLODs[i]);
                }
            }
            //一个lod group下有多少个lod
            public LODInfo[] lods;
            public string gameObjectName;
        }

        protected class LODGroupManager
        {
            public LODGroupManager(GameObject prefab)
            {
                var lodGroupComponents = prefab.GetComponentsInChildren<LODGroup>(true);
                lodGroups = new LODGroupInfo[lodGroupComponents.Length];
                for (int i = 0; i < lodGroupComponents.Length; ++i)
                {
                    lodGroups[i] = new LODGroupInfo(lodGroupComponents[i]);
                }
            }

            public LODGroupInfo[] lodGroups;
        }

        protected class StateInfo
        {
            public string name = null;
            public AnimationClip clip = null;
            public float speed;
            public string speedParameter;
            public bool speedParameterActive;
            public float cycleOffset;
            public string cycleOffsetParameter;
            public bool cycleOffsetParameterActive;
            public AnimationTransitionInfo[] transitions = null;
            public AnimationEventInfo[] events = null;
        }

        protected class ParameterInfo
        {
            public string name = null;
            //     Returns the hash of the parameter based on its name.
            public int nameHash = 0;
            //     The type of the parameter.
            public AnimatorControllerParameterType type = AnimatorControllerParameterType.Float;
            //     The default float value for the parameter.
            public float defaultFloat = 0;
            //     The default int value for the parameter.
            public int defaultInt = 0;
            //     The default bool value for the parameter.
            public bool defaultBool = false;
        }
    }
}


#endif