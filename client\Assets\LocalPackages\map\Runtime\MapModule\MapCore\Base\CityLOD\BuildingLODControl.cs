﻿ 



 
 



using UnityEngine;
using System.Collections.Generic;
using System;

namespace TFW.Map
{
    //主城所有物体的接口
    public interface IBuildingElement
    {
        int id { get; }
        bool isVisible { get; }
        //是否是主城的主要建筑
        bool isMainBuilding { get; }
        //是否到达了最终位置
        bool isAtFinalPosition { get; }
        float upperHeight { get; }
        float lowerHeight { get; }
        void SetVisible(bool visible);
        int GetPriority();
        void UpdateCity();
        void ClearPotentialCollidableList();
        void AddPotentialCollidableBuilding(IBuildingElement building);
        void AddCollision(IBuildingElement building);
        List<IBuildingElement> GetCollisionList();
        List<IBuildingElement> GetPotentialCollidableBuildingList();
    }

    //主城建筑物的接口
    public interface IBuilding : IBuildingElement
    {
        float scaledColliderRadius { get; }
        float maxColliderRadius { get; }
        Vector3 colliderCenter { get; }
        Vector2 GetCameraHeightRange();
        float GetMaxCameraHeightWhenCityIsInCenter();
        void SetMoveInfo(Vector3 startPos, Vector3 endPos, Vector2 cameraHeightRange, Transform cityRootTransform);
        void AddPassUpperHeightLimitEvent(System.Action<float, bool> handler);
        void AddPassLowerHeightLimitEvent(System.Action<float, bool> handler);
        void RemovePassUpperHeightLimitEvent(System.Action<float, bool> handler);
        void RemovePassLowerHeightLimitEvent(System.Action<float, bool> handler);
    }

    //城墙的接口
    public interface ICityWall : IBuildingElement
    {
    }

    //管理某个city的lod实现,脚本挂接到一个建筑上
    public class BuildingLODControl : KeepScaleByGrid, IBuilding
    {
        public void Init(BuildingLODManager lodManager, float objectGridCount, float gridSize, GameObject rendererRoot = null)
        {
            if (mInited)
            {
                return;
            }

            if (rendererRoot == null)
            {
                rendererRoot = this.gameObject;
            }

            mInited = true;
            mLODManager = lodManager;
            mGridSize = gridSize;
            mIsVisible = true;
            if (objectGridCount != 0)
            {
                this.objectGridCount = objectGridCount;
            }
            mCurrentColliderRadius = colliderRadius;

            if (!System.Object.ReferenceEquals(mLODManager, null))
            {
                if (mRenderers.Count == 0)
                {
                    rendererRoot.GetComponentsInChildren<Renderer>(true, mRenderers);
                    for (int i = 0; i < mRenderers.Count; ++i)
                    {
                        mRenderers[i].enabled = true;
                    }
                }

                if (System.Object.ReferenceEquals(mCollider, null))
                {
                    mCollider = gameObject.GetComponentInChildren<Collider>();
                }

                UpdatePosition();

                mLODManager.AddCity(this);

                //在物体显示时根据相机的高度设置它初始的缩放值
                SetInitScale();

                UpdateCollider();

                //计算物体最大的碰撞半径
                mMaxColliderRadius = GetScaleAtHeight(GetScaleConfig().maximumCameraHeight);
            }
        }

        public void FilterRenderer(Renderer renderer)
        {
            mRenderers.Remove(renderer);
        }

        public void Uninit()
        {
            //Todo RBuilding.OnDestroyed 会重复调用Uninit 
            //Debug.Assert(mInited);
            if (mInited)
            {
                mInited = false;
                if (mLODManager != null)
                {
                    mLODManager.RemoveCity(this);
                    mLODManager = null;
                }

                mRenderers.Clear();
                mIsVisible = true;
                mCollider = null;

                mLastCameraHeight = 0;

                mMoveToTarget = null;
                ClearPotentialCollidableList();
                clearVisiableChangedEvent();
            }
            else
            {
                Debug.LogWarning("BuildingLODControl mInited is false");
            }
        }

        //根据相机的高度来更新建筑的缩放或位置
        public void UpdateCity()
        {
            UpdateScaleImpl();
            UpdatePosition();

            var config = GetScaleConfig();
            //update collider radius
            float cameraHeight = Map.currentMap.camera.transform.position.y;
            float t = (cameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp01(t);
            mCurrentColliderRadius = colliderRadius + t * increasedColliderRadius;

            UpdateCollider();
        }

        void UpdateCollider()
        {
            var t = transform;
            float localScale = t.lossyScale.x;
            mColliderCenter = t.position + colliderOffset * localScale;
            if (mCurrentColliderRadius == 0)
            {
                mScaledColliderRadius = colliderRadius;
            }
            else
            {
                mScaledColliderRadius = mCurrentColliderRadius * localScale / mPrefabInitScale;
            }
        }

        //根据相机的高度更新建筑的位置
        void UpdatePosition()
        {
            if (mMoveToTarget != null)
            {
                mMoveToTarget.Update();
            }
        }

        //设置主城的移动信息
        //startPos:在相机高度为cameraHeightRange.x时主城的位置
        //endPos:在相机高度为cameraHeightRange.y时主城的位置
        //cameraHeightRange:主城移动期间相机的高度范围
        public void SetMoveInfo(Vector3 startPos, Vector3 endPos, Vector2 cameraHeightRange, Transform cityRootTransform)
        {
            mMoveToTarget = new LODMoveToTarget(transform, cityRootTransform);
            mMoveToTarget.SetMoveInfo(startPos, endPos, cameraHeightRange);
            mMoveEndPosition = mMoveToTarget.worldEndPosition;
        }

        public void SetMoveInfo(LODMoveToTarget pMoveToTarget)
        {
            mMoveToTarget = pMoveToTarget;
            mMoveToTarget.SetTransform(transform);
            mMoveEndPosition = mMoveToTarget.worldEndPosition;
        }

        public void SetCityStartPosition(Vector3 startPos)
        {
            if (mMoveToTarget != null)
            {
                mMoveToTarget.SetStartPosition(startPos);
            }
        }

        //显示或隐藏建筑
        public void SetVisible(bool visible)
        {
            if (mIsVisible != visible)
            {
                mIsVisible = visible;
                for (int i = 0; i < mRenderers.Count; ++i)
                {
                    mRenderers[i].enabled = visible;
                }
                visiableChangedEvent?.Invoke(visible);
            }
        }

        void SetLODManager(BuildingLODManager lodManager)
        {
            mLODManager = lodManager;
        }

        protected override float GetGridSize()
        {
            return mGridSize;
        }

        public bool isMainBuilding { get { return mMoveToTarget != null; } }
        public bool isAtFinalPosition
        {
            get
            {
                return transform.position == mMoveEndPosition;
            }
        }
        public Vector3 colliderCenter { get { return mColliderCenter; } }

        void OnDrawGizmos()
        {
#if true
            if (mIsVisible)
            {
                Gizmos.DrawWireSphere(colliderCenter, scaledColliderRadius);

                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(transform.position, objectResourceRadius);

                var collider = gameObject.GetComponentInChildren<Collider>();
                if (collider != null)
                {
                    Gizmos.color = Color.red;
                    var boxCollider = collider as BoxCollider;
                    if (boxCollider != null)
                    {
                        Gizmos.matrix = collider.transform.localToWorldMatrix;
                        Gizmos.DrawWireCube(boxCollider.center, boxCollider.size);
                    }
                }
            }
#endif
        }

        public void ClearPotentialCollidableList()
        {
            mPotentialCollidableBuildings.Clear();
        }

        public void AddPotentialCollidableBuilding(IBuildingElement building)
        {
#if DEBUG
            Debug.Assert(mPotentialCollidableBuildings.Contains(building) == false);
#endif
            mPotentialCollidableBuildings.Add(building);
        }

        public List<IBuildingElement> GetPotentialCollidableBuildingList()
        {
            return mPotentialCollidableBuildings;
        }

        public int GetPriority()
        {
            return hidePriority;
        }
        //建筑物未缩放时的半径,如果使用半径来做建筑的重叠判定时使用
        public float colliderRadius;
        //增加的collider radius,因为当主城大厅填满整个主城时有可能大厅的collider碰不到城墙
        public float increasedColliderRadius = 0;
        //碰撞框的中心点偏移
        public Vector3 colliderOffset;
        //随建筑物缩放后的半径
        public float scaledColliderRadius
        {
            get
            {
                if (mScaledColliderRadius == 0)
                {
                    return mCurrentColliderRadius * transform.lossyScale.x / mPrefabInitScale;
                }
                return mScaledColliderRadius;
            }
        }

        /// <summary>
        /// 清除可视化变化事件
        /// </summary>
        public void clearVisiableChangedEvent()
        {
            if (visiableChangedEvent == null) return;
            Delegate[] dels = visiableChangedEvent.GetInvocationList();
            foreach (Delegate del in dels)
            {
                object delObj = del.GetType().GetProperty("Method").GetValue(del, null);
                string funcName = (string)delObj.GetType().GetProperty("Name").GetValue(delObj, null);////方法名
                Console.WriteLine(funcName);
                visiableChangedEvent -= del as OnVisibleChanged;
            }
        }

        public void AddVisiableChangedEvent(OnVisibleChanged pvisiableChangedEvent)
        {
            visiableChangedEvent -= pvisiableChangedEvent;
            visiableChangedEvent += pvisiableChangedEvent;
        }

        public void AddCollision(IBuildingElement building)
        {
        }

        public List<IBuildingElement> GetCollisionList()
        {
            return null;
        }

        protected override void GetObjectFinalScale()
        {
            var config = GetScaleConfig();
            if (mMinScaleFactor == 0)
            {
                mMinScaleFactor = GetScaleFactor(config.minimumCameraHeight);
            }
            if (mMaxScaleFactor == 0)
            {
                mMaxScaleFactor = GetScaleFactor(config.maximumCameraHeight);
            }
            mObjectMinScale = mPrefabInitScale * mMinScaleFactor;
            mObjectMaxScale = mPrefabInitScale * mMaxScaleFactor;
        }

        public Vector3 GetObjectMinScale()
        {
            return mObjectMinScale * Vector3.one;
        }

        public void SetGridSizeAndCount(float gridSize, float count)
        {
            if (!Mathf.Approximately(gridSize, mGridSize) || objectGridCount != count)
            {
                mGridSize = gridSize;
                objectGridCount = count;
                RecalculateObjectScale();
            }
        }

        public void SetGridSize(float gridSize)
        {
            if (!Mathf.Approximately(gridSize, mGridSize))
            {
                mGridSize = gridSize;
                RecalculateObjectScale();
            }
        }

        public void SetObjectGridCount(int count)
        {
            if (objectGridCount != count)
            {
                objectGridCount = count;
                RecalculateObjectScale();
            }
        }

        void RecalculateObjectScale()
        {
            mMinScaleFactor = 0;
            mMaxScaleFactor = 0;
            mPrefabInitScale = mPrefabInitScaleConst;
            if (objectResourceRadius != 0 && objectGridCount != 0)
            {
                float objectInGameRadius = objectGridCount * GetGridSize() * 0.5f;
                mPrefabInitScale = mPrefabInitScaleConst * (objectInGameRadius / objectResourceRadius);
            }
            GetObjectFinalScale();
            SetScaleAtHeight(MapCameraMgr.currentCameraHeight);
            UpdateCollider();
            mMaxColliderRadius = GetScaleAtHeight(GetScaleConfig().maximumCameraHeight);
        }

        //建筑物是否可见
        public bool isVisible { get { return mIsVisible; } }
        //使用collider来判定建筑物重叠时使用
        public Collider objectCollider { get { return mCollider; } }
        public BuildingLODManager lodManager { get { return mLODManager; } }
        public float maxColliderRadius { get { return mMaxColliderRadius; } }
        public int id { get { return gameObject.GetInstanceID(); } }

        public LODMoveToTarget MoveToTarget { get { return mMoveToTarget; } }

        //在建筑物重叠时hidePriority小的隐藏hidePriority大的建筑
        public int hidePriority;

        //在建筑物显示、隐藏时回调
        public delegate void OnVisibleChanged(bool visible);

        //建筑显示、隐藏事件
        public event OnVisibleChanged visiableChangedEvent;

        BuildingLODManager mLODManager;
        //建筑物子节点中所有的renderer
        List<Renderer> mRenderers = new List<Renderer>();
        Collider mCollider;
        float mCurrentColliderRadius;
        float mGridSize;
        bool mIsVisible = true;
        bool mInited = false;
        Vector3 mMoveEndPosition;

        Vector3 mColliderCenter;
        float mScaledColliderRadius;
        //最大可能的碰撞半径,可以利用这个半径进行碰撞检测优化
        float mMaxColliderRadius;

        static float mMinScaleFactor;
        static float mMaxScaleFactor;

        //潜在的可能碰撞的物体
        List<IBuildingElement> mPotentialCollidableBuildings = new List<IBuildingElement>();

        //将建筑物在相机缩放时移动到目标点
        LODMoveToTarget mMoveToTarget;
    }
}