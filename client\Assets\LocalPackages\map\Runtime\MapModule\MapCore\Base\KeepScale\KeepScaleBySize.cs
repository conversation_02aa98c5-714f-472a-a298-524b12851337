﻿ 



 
 



using UnityEngine;

namespace TFW.Map
{
    //按米来缩放prefab
    public class KeepScaleBySize : KeepScale
    {
        //物体增加的scale
        public float maxDeltaScale = 0;
        protected bool mUseDeltaScale = false;
        public float minExtraScale = 1.0f;
        public float maxExtraScale = 1;
        public bool keepSize = true;

        protected override void SetInitScale()
        {
            var config = GetScaleConfig();
            if (Map.currentMap != null && config != null)
            {
                var camera = Map.currentMap.camera;
                if (camera != null)
                {
                    if (mPrefabInitScale == 0)
                    {
                        mPrefabInitScale = gameObject.transform.localScale.x;
                        mPrefabInitScaleConst = mPrefabInitScale;
                    }

                    float cameraHeight = camera.transform.position.y;
                    cameraHeight = Mathf.Clamp(cameraHeight, config.minimumCameraHeight, config.maximumCameraHeight);
                    mLastCameraHeight = cameraHeight;
                    //D.Log(string.Format("Set Init Scale: {0}",cameraHeight));
                    SetScaleAtHeight(cameraHeight);
                }
            }
        }

        protected override void SetScaleAtHeight(float cameraHeight)
        {
            var camera = Map.currentMap.camera;
            var config = GetScaleConfig();
			if (config == null)
			{
				return;
			}

            cameraHeight = Mathf.Clamp(cameraHeight, config.minimumCameraHeight, config.maximumCameraHeight);

            float oldWidth = Utils.CalculateViewportWidth(config.cameraFovWhenScaleIsOne, Map.currentMap);
            float fov = Map.currentMap.GetCameraFOVAtHeight(cameraHeight);
            float newWidth = Utils.CalculateViewportWidth(fov, Map.currentMap);
            float ratio = newWidth / oldWidth;

            float scaleFactor = mPrefabInitScale / config.cameraHeightWhenScaleIsBaseScale;
            float t = (cameraHeight - config.minimumCameraHeight) / (config.maximumCameraHeight - config.minimumCameraHeight);
            t = Mathf.Clamp(t, 0, 1.0f);

            float deltaScale = 1.0f;
            if (mUseDeltaScale)
            {
                deltaScale = 1 + maxDeltaScale * t;
            }

            if (keepSize)
            {
                transform.localScale = Vector3.one * scaleFactor * cameraHeight * ratio * deltaScale;
            }
            else
            {
                t = Utils.EaseInQuad(0, 1, t);
                float scaleE = Mathf.Lerp(minExtraScale, maxExtraScale, t);
                transform.localScale = Vector3.one * scaleFactor * cameraHeight * deltaScale * scaleE;
            }
        }

        protected override KeepScaleConfig GetScaleConfig()
        {
            return MapCameraMgr.buildingScaleConfig;
        }

        protected virtual float GetGridSize() { return 1.0f; }
    }
}
