﻿using UnityEngine;
using System.Collections.Generic;

namespace TFW.Map
{
    public partial class SplitFogLayerView : MapLayerView
    {
        public bool OnSelectionChange(int x, int y, int width, int height)
        {
            if (mSelectionGameObject != null)
            {
                mSelectionGameObject.SetActive(true);
                return UpdateSelection(x, y, width, height);
            }
            return false;
        }

        void InitSelectionData(string fogSelectionMaterialPath)
        {
            if (!string.IsNullOrEmpty(fogSelectionMaterialPath))
            {
                mSelectionGameObject = new GameObject();
                var renderer = mSelectionGameObject.AddComponent<MeshRenderer>();

                var fogLayerData = layerData as SplitFogLayerData;
                var mtl = MapModuleResourceMgr.LoadMaterial(fogSelectionMaterialPath);
                Debug.Assert(mtl != null, "fog selection material not found!");
                var tilePrefab = MapModuleResourceMgr.LoadPrefab(fogLayerData.tilePrefabPaths[1]);
                var sharedMaterials = tilePrefab.GetComponent<MeshRenderer>().sharedMaterials;
                int idx = 0;
                if (sharedMaterials.Length > 1)
                {
                    idx = 1;
                }
                if (sharedMaterials[idx] != null)
                {
                    var texture = sharedMaterials[idx].GetTexture(fogLayerData.fogMaskTexPropertyName);
                    mtl.SetTexture(fogLayerData.fogMaskTexPropertyName, texture);
                }
                renderer.sharedMaterial = mtl;
                var filter = mSelectionGameObject.AddComponent<MeshFilter>();
                mSelectionMesh = new Mesh();
                mSelectionMesh.MarkDynamic();
                filter.sharedMesh = mSelectionMesh;
            }
        }

        void DestroySelectionData()
        {
            Utils.DestroyObject(mSelectionGameObject);
            mSelectionGameObject = null;

            Utils.DestroyObject(mSelectionMesh);
            mSelectionMesh = null;
        }

        enum TileBorderDirection
        {
            kLeftBottomCorner,
            kLeftTopCorner,
            kRightBottomCorner,
            kRightTopCorner,
            kLeftEdge,
            kRightEdge,
            kBottomEdge,
            kTopEdge,
            kInner,
        }

        TileBorderDirection GetTileBorderDirection(int x, int y, int minX, int minY, int maxX, int maxY)
        {
            if (x == minX && y == minY)
            {
                return TileBorderDirection.kLeftBottomCorner;
            }
            else if (x == minX && y == maxY)
            {
                return TileBorderDirection.kLeftTopCorner;
            }
            else if (x == maxX && y == maxY)
            {
                return TileBorderDirection.kRightTopCorner;
            }
            else if (x == maxX && y == minY)
            {
                return TileBorderDirection.kRightBottomCorner;
            }
            else if (x == minX)
            {
                return TileBorderDirection.kLeftEdge;
            }
            else if (x == maxX)
            {
                return TileBorderDirection.kRightEdge;
            }
            else if (y == minY)
            {
                return TileBorderDirection.kBottomEdge;
            }
            else if (y == maxY)
            {
                return TileBorderDirection.kTopEdge;
            }
            return TileBorderDirection.kInner;
        }

        bool IfTileCanBeOpened(TileBorderDirection dir, int tileType)
        {
            if (tileType >= 15)
            {
                return true;
            }

            switch (dir)
            {
                case TileBorderDirection.kLeftBottomCorner:
                    return (tileType & 1) != 0;
                case TileBorderDirection.kLeftTopCorner:
                    return (tileType & 4) != 0;
                case TileBorderDirection.kRightBottomCorner:
                    return (tileType & 2) != 0;
                case TileBorderDirection.kRightTopCorner:
                    return (tileType & 8) != 0;
                case TileBorderDirection.kLeftEdge:
                    return (tileType & 1) != 0 || (tileType & 4) != 0;
                case TileBorderDirection.kRightEdge:
                    return (tileType & 2) != 0 || (tileType & 8) != 0;
                case TileBorderDirection.kBottomEdge:
                    return (tileType & 1) != 0 || (tileType & 2) != 0;
                case TileBorderDirection.kTopEdge:
                    return (tileType & 4) != 0 || (tileType & 8) != 0;
                case TileBorderDirection.kInner:
                    return true;
            }
            return false;
        }

        bool UpdateSelection(int x, int y, int width, int height)
        {
            mSelectionMeshVertices.Clear();
            mSelectionMeshIndices.Clear();
            mSelectionMeshUVs.Clear();
            Debug.Assert(width > 0 && height > 0);
            var layerData = mLayerData as SplitFogLayerData;
            int maxY = y + height - 1;
            int maxX = x + width - 1;
            int n = 0;
            for (int i = y; i <= maxY; ++i)
            {
                for (int j = x; j <= maxX; ++j)
                {
                    int tileType = layerData.GetTileType(j, i);
                    if (tileType >= 0)
                    {
                        var tileDir = GetTileBorderDirection(j, i, x, y, maxX, maxY);
                        bool isValidTile = IfTileCanBeOpened(tileDir, tileType);
                        if (isValidTile)
                        {
                            Vector3 v0 = layerData.FromCoordinateToWorldPosition(j, i);
                            Vector3 v1 = layerData.FromCoordinateToWorldPosition(j, i + 1);
                            Vector3 v2 = layerData.FromCoordinateToWorldPosition(j + 1, i + 1);
                            Vector3 v3 = layerData.FromCoordinateToWorldPosition(j + 1, i);
                            v0.y = layerData.fogHeight + 0.1f;
                            v1.y = layerData.fogHeight + 0.1f;
                            v2.y = layerData.fogHeight + 0.1f;
                            v3.y = layerData.fogHeight + 0.1f;

                            mSelectionMeshVertices.Add(v0);
                            mSelectionMeshVertices.Add(v1);
                            mSelectionMeshVertices.Add(v2);
                            mSelectionMeshVertices.Add(v3);

                            var uvs = GetTileAtlasUV(tileType);
                            mSelectionMeshUVs.AddRange(uvs);

                            mSelectionMeshIndices.Add(n);
                            mSelectionMeshIndices.Add(n + 1);
                            mSelectionMeshIndices.Add(n + 2);
                            mSelectionMeshIndices.Add(n);
                            mSelectionMeshIndices.Add(n + 2);
                            mSelectionMeshIndices.Add(n + 3);

                            n += 4;
                        }
                    }
                }
            }

            if (mSelectionMeshVertices.Count > 0)
            {
                mSelectionMesh.Clear();
                mSelectionMesh.SetVertices(mSelectionMeshVertices);
                mSelectionMesh.SetUVs(0, mSelectionMeshUVs);
                mSelectionMesh.SetIndices(mSelectionMeshIndices.ToArray(), MeshTopology.Triangles, 0);
                mSelectionMesh.UploadMeshData(false);
                mSelectionMesh.RecalculateBounds();

                return false;
            }
            return true;
        }

        public void OnHideSelection()
        {
            if (mSelectionGameObject != null)
            {
                mSelectionGameObject.SetActive(false);
            }
        }

        GameObject mSelectionGameObject;
        Mesh mSelectionMesh;
        List<Vector3> mSelectionMeshVertices = new List<Vector3>();
        List<Vector2> mSelectionMeshUVs = new List<Vector2>();
        List<int> mSelectionMeshIndices = new List<int>();
    }
}