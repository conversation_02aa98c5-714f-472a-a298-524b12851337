﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using System.Collections.Generic;
using UnityEditor;

namespace TFW.Map
{
    public class ActionRemovePolygonRiverLayer : EditorAction
    {
        public ActionRemovePolygonRiverLayer(int layerID)
        {
            var layer = Map.currentMap.GetMapLayerByID(layerID) as PolygonRiverLayer;
            mDisplayVertexRadius = layer.displayVertexRadius;
            mLayerID = layerID;
            mLayerWidth = layer.GetTotalWidth();
            mLayerHeight = layer.GetTotalHeight();

            List<IMapObjectData> collisions = new List<IMapObjectData>();
            layer.GetAllObjects(collisions);
            for (int i = 0; i < collisions.Count; ++i)
            {
                var action = new ActionRemovePolygonRiver(layerID, collisions[i].GetEntityID());
                mRemoveActions.Add(action);
            }
        }

        public override bool Do()
        {
            bool suc = true;
            for (int i = mRemoveActions.Count - 1; i >= 0; --i)
            {
                suc &= mRemoveActions[i].Do();
            }

            var layer = Map.currentMap.GetMapLayerByID(mLayerID) as PolygonRiverLayer;
            int index = Map.currentMap.GetMapLayerIndex(layer);
            Map.currentMap.RemoveMapLayerByIndex(index);
            return suc;
        }

        public override bool Undo()
        {
            var map = Map.currentMap as EditorMap;
            var layer = map.CreatePolygonRiverLayer(MapCoreDef.MAP_LAYER_NODE_POLYGON_RIVER, mLayerWidth, mLayerHeight, mLayerID, mDisplayVertexRadius);
            Map.currentMap.AddMapLayer(layer);

            bool suc = true;
            for (int i = 0; i < mRemoveActions.Count; ++i)
            {
                suc &= mRemoveActions[i].Undo();
            }

            Selection.activeObject = layer.layerView.root;
            return suc;
        }

        List<ActionRemovePolygonRiver> mRemoveActions = new List<ActionRemovePolygonRiver>();
        int mLayerID;
        float mLayerWidth;
        float mLayerHeight;
        float mDisplayVertexRadius;

    }
}

#endif