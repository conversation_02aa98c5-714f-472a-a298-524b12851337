﻿ 



 
 

using UnityEngine;

namespace TFW.Map
{
    public abstract class BakedAnimationDataBase : ScriptableObject
    {
        public string prefabName;
        //使用的animation controller guid
        public string animationControllerAssetGuid;
        //烘培前的prefab guid
        public string originalPrefabGuid;

        public AnimationInfo[] animationInfo;
        public AnimationStateInfo[] animationStateInfo;
        public AnimationParameterInfo[] parameterInfo;
        public bool useRGBA32Texture = true;
        public bool useAnimationBlending = false;
        public bool useSRPBatcher = false;
        public AnimationBlendType blendType = AnimationBlendType.Fast;

        public int GetAnimationIndex(string stateName)
        {
            for (int i = 0; i < animationStateInfo.Length; ++i)
            {
                if (animationStateInfo[i].stateName == stateName)
                {
                    return i;
                }
            }
            Debug.Assert(false);
            return -1;
        }

        //used by joint animation
        public int GetAnimationOffset(int animType, RendererShaderType type)
        {
            Debug.Assert(animationInfo[animType].bakedVertexAnimationStartOffsetsForEachMesh.Length == 0, "can't used in vertex animation!");
            if (animType >= 0 && animType < animationInfo.Length)
            {
                return type == RendererShaderType.RigidShader ? animationInfo[animType].rigidAnimStartOffset : animationInfo[animType].skinAnimStartOffset;
            }
            Debug.Assert(false, "invalid animation!");
            return -1;
        }

        //used by vertex animation
        public int GetVertexAnimationOffsetForRenderer(int meshIndex, int animType)
        {
            if (animType >= 0 && animType < animationInfo.Length)
            {
                return animationInfo[animType].bakedVertexAnimationStartOffsetsForEachMesh[meshIndex];
            }
            Debug.Assert(false, "invalid animation!");
            return -1;
        }

        public float GetVertexAnimationOffsetRatioForRenderer(int meshIndex, int animType)
        {
            if (animType >= 0 && animType < animationInfo.Length)
            {
                return animationInfo[animType].bakedVertexAnimationStartOffsetRatioForEachMesh[meshIndex];
            }
            Debug.Assert(false, "invalid animation!");
            return -1;
        }

        public int GetCPUDrivenAnimationOffset(int animType)
        {
            if (animType >= 0 && animType < animationInfo.Length)
            {
                return animationInfo[animType].cpuDrivenAnimStartOffset;
            }
            Debug.Assert(false, "invalid animation!");
            return -1;
        }

        public abstract string[] GetCPUDrivenCustomBoneNames();
    }
}
