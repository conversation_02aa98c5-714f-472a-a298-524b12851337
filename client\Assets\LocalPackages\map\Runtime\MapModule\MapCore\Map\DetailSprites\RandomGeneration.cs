﻿ 



 
 

﻿using UnityEngine;

namespace TFW.Map
{
    public partial class DetailSprites
    {
        int[] masks = new int[]{
  1664787, 1607698, 2121976, 1707312, 2041697, 926241, 3085074, 687036,
  855912, 850892, 3601333, 3109118, 3637298, 3164302, 3280601, 3640752,
  2171401, 3573463, 1600172, 4076805, 4084738, 703953, 1274324, 1976395,
  2377148, 4187023, 1935682, 778127, 363649, 2905628, 3756587, 3962212,
  1460539, 91956, 830794, 2072797, 1720535, 1366567, 2203137, 3682659,
  1779979, 2920409, 2024513, 1036197, 1480592, 1201671, 1396972, 2630170,
  4142822, 2519041, 1086265, 2960766, 2806346, 417550, 1647044, 2902257,
  3929526, 1216105, 2829042, 3101401, 3086769, 322784, 724504, 2542054,
  3506231, 672736, 1458161, 47343, 2914967, 2023907, 2250604, 3878150,
  2291567, 857025, 2780995, 3427945, 3675529, 2578244, 2234804, 3524712,
  2507826, 1768810, 2545751, 1430017, 2483203, 172555, 3154831, 2298710,
  4185933, 2369133, 2431352, 3147587, 649345, 3722374, 4131188, 833204,
  64697, 1911499, 4178953, 2262041, 557887, 1661759, 949481, 2588629,
  3275198, 4000494, 3163343, 2217632, 2459789, 2023448, 2243766, 3753165,
  4008809, 1341503, 84725, 4068024, 639767, 1392489, 3504453, 3766976,
  634152, 3016169, 3180193, 198861, 3763624, 2132653, 3613040, 1306062,
  27380, 1524823, 1419907, 721752, 31268, 1945770, 2506863, 2844133,
  172489, 2752191, 1939977, 3682777, 731949, 1683719, 888604, 493529,
  3336365, 1552050, 3511613, 2388954, 979971, 2046362, 157, 935112,
  463607, 1812092, 3611367, 2118955, 1098722, 1936527, 2513179, 2166136,
  3459405, 1799661, 1734673, 3850472, 2700553, 2651157, 2420668, 3625321,
  1304993, 1438196, 1676719, 78518, 411781, 2734522, 3093001, 3936137,
  1041255, 4012636, 949976, 3006910, 855415, 2660151, 266859, 2902154,
  248460, 2294468, 3730610, 2548006, 1323958, 2896957, 1925660, 1740432,
  2540403, 2608020, 632898, 414949, 1714234, 1914713, 3540514, 3311884,
  2524121, 3821071, 2168146, 781900, 2271412, 2642395, 1932655, 1090853,
  2428864, 1013728, 522707, 1529295, 3507979, 3426516, 1652147, 2876283,
  1902064, 3549049, 1735667, 3212039, 1639553, 2427274, 2714458, 1590919,
  3626767, 3782414, 1478100, 2937669, 3875173, 2075964, 4156778, 2733968,
  1595794, 4068143, 2792015, 3448604, 1648742, 2108201, 68573, 717181,
  2725255, 538243, 4125458, 1288325, 558123, 664701, 3550721, 1555679,
  2698070, 3552002, 3296538, 3616484, 2053485, 2752111, 3731529, 3183865};

        int SEED = 3752256;

        int[] blocksi = new int[] { 6, 12, 0, 11, 5, 15, 8, 1, 9, 17, 10, 3, 2, 16, 4, 14, 7, 13 };
        int[] blocksj = new int[] { 1, 8, 9, 14, 11, 5, 4, 15, 3, 10, 12, 17, 7, 16, 0, 2, 13, 6 };

        int[] blocksk = new int[]{ 26, 11, 6, 24, 32, 35, 19, 25, 13, 28, 4, 33, 9,
  31, 37, 22, 0, 2, 39, 5, 10, 17, 15, 21, 14, 7,
  29, 34, 36, 8, 12, 16, 38, 23, 30, 3, 27, 1, 20, 18};

        int[] blocksl = new int[]{ 13, 8, 14, 25, 1, 7, 30, 0, 31, 23,
  36, 29, 4, 17, 35, 33, 21, 39, 15, 6,
  3, 19, 26, 11, 12, 37, 2, 5, 34, 24,
  32, 9, 22, 38, 18, 10, 28, 27, 16, 20};

        float GetNum(int curNum, int k, int l, int i, int j)
        {
            int ii = blocksi[i];
            int jj = blocksj[j];
            int ll = blocksl[l];
            int kk = blocksk[k];

            int a = ((((ll << 5) + ii)) << 11) + ((kk << 5) + jj);
            float v = ((float)((a ^ SEED) ^ masks[curNum]) / (float)4194303.0); //(to get a 0 to 1 value)
            return v;
        }

        void Step(ref int currNum)
        {
            currNum++;
            if (currNum > 255)
            {
                currNum = 0;
            }
        }
    }
}
