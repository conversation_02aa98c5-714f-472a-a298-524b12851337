using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;

namespace CISystem
{
    public static class DingDingNotifier
    {
        static string DingTalkWebhook => CISystemProjectSettings.instance.dingTalkWebhook;

        static void StartBackgroundTask(IEnumerator update, Action end = null)
        {
            EditorApplication.CallbackFunction closureCallback = null;

            closureCallback = () =>
            {
                try
                {
                    if (update.MoveNext() == false)
                    {
                        if (end != null)
                            end();
                        EditorApplication.update -= closureCallback;
                    }
                }
                catch (Exception ex)
                {
                    if (end != null)
                        end();
                    Debug.LogException(ex);
                    EditorApplication.update -= closureCallback;
                }
            };

            EditorApplication.update += closureCallback;
        }

        static void PostNotify(string jsonData)
        {
            if (!string.IsNullOrEmpty(DingTalkWebhook))
            {
                var bytes = Encoding.UTF8.GetBytes(jsonData);

                UnityWebRequest www = new UnityWebRequest(DingTalkWebhook, "POST");
                www.uploadHandler = new UploadHandlerRaw(bytes);

                www.downloadHandler = new DownloadHandlerBuffer();
                www.SetRequestHeader("Content-Type", "application/json");
                www.SendWebRequest();
            }
            else
            {
                UnityEngine.Debug.Log("no dingtalk url");
            }
        }

        public static void Notify(string title, string msg, string url)
        {
            var builder = new StringBuilder();
            builder.Append("{");
            builder.Append("\"msgtype\":\"markdown\"");
            builder.Append(",");
            builder.Append("\"markdown\":{");
            builder.Append($"\"title\":\"{title}\"");
            builder.Append(",");
            if (string.IsNullOrEmpty(url))
            {
                builder.Append($"\"text\":\"**{title}**\n\n {msg} \n \"");
            }
            else
            {
                builder.Append($"\"text\":\"**{title}**\n\n {msg} \n [点此下载工程]({url}) \"");
            }

            builder.Append("},");
            builder.Append("\"at\":{\"isAtAll\":true");
            builder.Append("}}");
            PostNotify(builder.ToString());
        }


        public static void Notify(string msg, string url)
        {
            Notify("打包", msg, url);
        }

        public static void Notify(string msg)
        {
            var jsonData = "{\"msgtype\": \"text\", \"text\": {\"content\": \"" + msg + "\"}}";
            PostNotify(jsonData);
        }

        public static void NotifyMarkDown(string title, string markdown)
        {
            var jsonBuilder = new StringBuilder();
            jsonBuilder.AppendLine("{");
            jsonBuilder.AppendLine("\"msgtype\":\"markdown\",");
            jsonBuilder.AppendLine("\"markdown\":{");
            jsonBuilder.AppendLine($"\"title\":\"{title}\",");
            jsonBuilder.AppendLine($"\"text\":\"{markdown}\"");
            jsonBuilder.AppendLine("},");
            jsonBuilder.AppendLine("}");
            PostNotify(jsonBuilder.ToString());
        }

#if DEBUG_TEST
        [MenuItem("钉钉通知测试/通知")]
        public static void NotifyTest()
        {
            Notify("Gold World", "打包完成", "https://google.com");
        }
#endif
    }
}
