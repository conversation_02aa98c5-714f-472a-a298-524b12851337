﻿








using System.Collections.Generic;
using Common;
using cspb;
using Cysharp.Threading.Tasks;
using DeepUI;
using Game.Data;
using GameState;
using THelper;
using UI;

namespace Logic
{
    /// <summary>
    /// 联盟申请记录 Logic
    /// </summary>
    public class LAllianceApplyRecord : Ins<LAllianceApplyRecord>,IBeforeLoginCallback,ILogoutCallback
    {
        #region Property
        #endregion

        #region Interface

        
        /// <summary>
        /// 获取联盟申请记录列表
        /// </summary>
        /// <returns></returns>
        public List<UnionInfo> GetUnionApplyRecordList()
        {
            return m_UnionApplyRecordList;
        }

        /// <summary>
        /// 更新联盟申请记录列表
        /// </summary>
        /// <param name="unionId">联盟ID</param>
        /// <param name="isAdd">是否是添加</param>
        /// <param name="unionInfo">联盟信息</param>
        public void UpdateUnionApplyList(long unionId, bool isAdd, UnionInfo unionInfo)
        {
            if (isAdd)
            {
            }
            else
            {
                var list = m_UnionApplyRecordList;
                for (int i = 0; i < list.Count; i++)
                    if (list[i].ID == unionId)
                    {
                        list.RemoveAt(i);
                        break;
                    }
            }
        }

        /// <summary>
        /// 联盟申请记录请求
        /// </summary>
        public void UnionApplyRecordReq()
        {
            MessageMgr.Send(new UnionApplyRecordReq());
        }
        #endregion

        #region Method
        /// <summary>
        /// 联盟申请记录 ack
        /// </summary>
        /// <param name="args"></param>
        void OnUnionApplyRecordAck(UnionApplyRecordAck args)
        {
            Debug("Send2GS OnUnionApplyRecordAck args = {0}", args);

            var list = args.result;

            list.Sort((a, b) => { return a.TS > b.TS ? 1 : -1; });


            m_UnionApplyRecordList = list;
            EventMgr.FireEvent(TEventType.AllianceApplyRecordList, list);
        }

        /// <summary>
        /// 联盟申请记录変更 ntf
        /// </summary>
        /// <param name="args"></param>
        void OnUnionApplyResponseNtf(UnionApplyResponseNtf args)
        {
            Debug("OnUnionApplyResponseNtf args = {0}", args);

            if (!args.isAgree)
            {
                // 对方不同意删除申请记录
                // 这个地方更新下 已申请的 联盟id
                LAllianceMgr.I.UpdateRecommendUnionListApplied(args.ID, false);
                LAllianceMgr.I.UpdateUnionApplyList(args.ID, false);

                EventMgr.FireEvent(TEventType.AllianceApplyRecordChange,
                    args.ID, args.isAgree, args.name);
                EventMgr.FireEvent(TEventType.UnionApplyFaild);
            }
            else
            {
                var uiAlliance =PopupManager.I.FindPopup<UI.Alliance.UIAllianceMain>();
                if (uiAlliance == null)
                {
                    PopupManager.I.ShowLayer<UI.Alliance.UIAllianceMain>(new UI.Alliance.UIAllianceMainData
                    {
                        CurrentTabKey = GameData.I.MainData.CurrMenuType,
                    });
                }

                EventMgr.FireEvent(TEventType.UnionApplySuccess);
            }
        }

        public UniTask OnBeforeLogin()
        {
            m_UnionApplyRecordList = new List<UnionInfo>();

            // 联盟申请记录 ack
            MessageMgr.RegisterMsg<UnionApplyRecordAck>(this, OnUnionApplyRecordAck);
            // 联盟申请处理通知 ntf
            MessageMgr.RegisterMsg<UnionApplyResponseNtf>(this, OnUnionApplyResponseNtf);
            return UniTask.CompletedTask;
        }

        public UniTask OnLogout()
        {
            MessageMgr.UnregisterMsg(this);
            m_UnionApplyRecordList = null;

            return UniTask.CompletedTask;
        }
        #endregion

        #region Field

        /// <summary>
        /// 联盟申请记录列表
        /// </summary>
        private List<UnionInfo> m_UnionApplyRecordList;

        #endregion
    }
}