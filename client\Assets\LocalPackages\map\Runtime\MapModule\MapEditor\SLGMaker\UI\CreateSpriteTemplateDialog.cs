﻿ 



 
 


#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map {
    public class CreateSpriteTemplateDialog : EditorWindow {
        public void Show(System.Action onCreateSpriteTemplate) {
            mOnCreateSpriteTemplate = onCreateSpriteTemplate;
        }

        void OnGUI() {
            mName = EditorGUILayout.TextField("Name", mName);
            mColor = EditorGUILayout.ColorField("Color", mColor);

            DrawPropertySetting();

            if (GUILayout.Button("Create")) {
                bool valid = CheckValidation();

                if (valid) {
                    var temp = new SpriteTemplate(Map.currentMap.nextCustomObjectID, Map.currentMap, mName, mSelectedPropertySetID, 1, 1, mColor);
                    var editorMapData = Map.currentMap.data as EditorMapData;
                    editorMapData.AddSpriteTemplate(temp);

                    if (mOnCreateSpriteTemplate != null) {
                        mOnCreateSpriteTemplate();
                    }

                    Close();
                }
                else {
                    EditorUtility.DisplayDialog("Error", "Invalid Name", "OK");
                }
            }
        }
        

        bool CheckValidation() {
            if (mName.Length == 0) {
                return false;
            }

            var editorMapData = Map.currentMap.data as EditorMapData;
            var spriteTemplate = editorMapData.FindSpriteTemplate(mName);
            if (spriteTemplate != null) {
                return false;
            }

            return true;
        }

        void DrawPropertySetting() {
            EditorGUILayout.BeginHorizontal();

            if (mSelectedPropertySetID != 0) {
                var ps = Map.currentMap.FindObject(mSelectedPropertySetID) as PropertySet;
                mPropertyName = ps.name;
            }

            EditorGUILayout.TextField("Property Set", mPropertyName);

            if (GUILayout.Button("Edit Property")) {
                var propertyEditor = EditorWindow.GetWindow<PropertyEditor>("Property Editor");
                EditorUtils.CenterWindow(propertyEditor, 800, 800, 1000, 1000);
                propertyEditor.Show(0, mSelectedPropertySetID, (int propertySetID) => { mSelectedPropertySetID = propertySetID; });
            }

            if (GUILayout.Button("Reset")) {
                mSelectedPropertySetID = 0;
                mPropertyName = "";
            }

            EditorGUILayout.EndHorizontal();
        }

        string mName = "Color Sprite";
        Color32 mColor = new Color32(255, 255, 255, 255);
        int mSelectedPropertySetID = 0;
        string mPropertyName = "";
        System.Action mOnCreateSpriteTemplate;
    }
}

#endif