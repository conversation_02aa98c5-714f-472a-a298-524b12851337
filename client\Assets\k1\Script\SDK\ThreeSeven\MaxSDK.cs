
using UnityEngine;


/**
* MaxSDK，统一接口单例类
*/
namespace maxsdk
{

    public sealed class MaxSDK
    {

        private static readonly MaxSDK INSTANCE = new MaxSDK();
        private MaxSDKUnitySupportBase supportBase;

        public static MaxSDK GetInstance()
        {
            return INSTANCE;
        }

        //构造函数
        private MaxSDK()
        {
            Debug.Log("开始设置Unity-平台桥接，当前平台：" + Application.platform);

#if UNITY_ANDROID && !UNITY_EDITOR
			supportBase = new MaxSDKUnitySupportAndroid();
#elif UNITY_IOS && !UNITY_EDITOR
			supportBase = new MaxSDKUnitySupportIOS();
#elif UNITY_STANDALONE_WIN && !UNITY_EDITOR
			supportBase = new MaxSDKUnitySupportWin();
#endif
        }


        /**
		 * 获取当前 SDK 的类型
		 */
        public int GetSDKType()
        {

            if (supportBase == null)
            {
                return MaxSDKType.MAX_SDK_UNKNOWN;
            }

            return supportBase.GetSDKType();
        }

        public string GetAppConfig()
        {

            if (supportBase == null)
            {
                return "";
            }

            return supportBase.GetAppConfig();
        }

        /**
		 * 设置监听器
		 */
        public void SetListener(MaxSDKListener listener)
        {
            supportBase?.SetListener(listener);
        }

        /**
		 * 初始化
		 */
        public void Init(MaxSDKInitInfo initInfo)
        {
            supportBase?.Init(initInfo);
        }

        /**
		 * 登录
		 */
        public void Login(MaxSDKLoginInfo loginInfo)
        {

            supportBase?.Login(loginInfo);

        }

        /**
		* 数据上报接口
		*/
        public void ReportRoleInfo(MaxSDKRoleInfo roleInfo)
        {
            supportBase?.ReportRoleInfo(roleInfo);
        }


        /**
		 * 支付
		 */
        public void Pay(MaxSDKPayInfo orderInfo)
        {

            supportBase?.Pay(orderInfo);
        }


        /**
		 * 切换账号
		 */
        public void SwitchAccount(MaxSDKSwitchAccountInfo switchAccountInfo)
        {
            supportBase?.SwitchAccount(switchAccountInfo);
        }


        /**
        * 登出
         */
        public void Logout(MaxSDKLogoutInfo logoutInfo)
        {
            supportBase?.Logout(logoutInfo);
        }



        /**
		 * 退出游戏
		 */
        public void ExitGame(MaxSDKExitInfo exitInfo)
        {
            supportBase?.ExitGame(exitInfo);
        }

        /**
	     * 游戏埋点上报接口
	     */
        public void ReportEvent(MaxSDKReportEventInfo eventInfo)
        {
            supportBase?.ReportEvent(eventInfo);
        }


        /**
	    * 用户行为扩展接口
	    */
        public void OpenActionExt(MaxSDKActionInfo actionInfo)
        {
            supportBase?.OpenActionExt(actionInfo);
        }

        /**
		 *	是否支持某个行为扩展 
		 */
        public bool IsActionSupported(int type)
        {
            return supportBase != null && supportBase.IsActionSupported(type);
        }


        /**
		 * 分享
		 */
        public void Share(MaxSDKShareInfo shareInfo)
        {
            supportBase?.Share(shareInfo);
        }



        /**
         * 扩展接口（同步），支持返回值
         */
        public string DispatchSync(MaxSDKDispatchInfo dispatchInfo)
        {
            return supportBase?.DispatchSync(dispatchInfo);
        }


        /**
         * 扩展接口（异步），带统一回调方法  OnDispatchResult(MaxSDKDispatchBean bean)
         */
        public void DispatchASync(MaxSDKDispatchInfo dispatchInfo)
        {
            supportBase?.DispatchASync(dispatchInfo);
        }

    }
}

