﻿ 



 
 

#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace TFW.Map
{
    public partial class RegionLayerUI : UnityEditor.Editor
    {
        //合并距离相近的顶点
        void CombineVertex()
        {
            var selections = mSelectionManager.selections;
            if (selections.Count > 1)
            {
                Vector3 pos = Vector3.zero;
                for (int i = 0; i < selections.Count; ++i)
                {
                    var region = mLogic.layer.GetRegion(selections[i].regionID);
                    if (i == 0)
                    {
                        pos = region.GetVertexPos(PrefabOutlineType.NavMeshObstacle, selections[i].vertexIndex);
                    }
                    else
                    {
                        mLogic.layer.SetVertexPosition(PrefabOutlineType.NavMeshObstacle, selections[i].regionID, selections[i].vertexIndex, pos);
                    }
                }
            }
            else
            {
                EditorUtility.DisplayDialog("", "Please select at lease 2 vertices first!", "OK");
            }

            SceneView.RepaintAll();
        }
    }
}

#endif