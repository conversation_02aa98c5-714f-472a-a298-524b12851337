﻿ 



 
 



/*
 * created by wzw at 2019/8/15
 */

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;
using System.IO;
using UnityEditor;

namespace TFW.Map
{
    public class MapCollisionLayerEventHandlers
    {
        public System.Action<int> onDeleteCollision;
        public System.Action<int> onAddCollision;
        public System.Action<int> onMoveCollision;
        public System.Action<int, int> onMoveVertex;
        public System.Action<int, int> onInsertVertex;
        public System.Action<int, int> onRemoveVertex;
        public System.Action<int, PrefabOutlineType> onOutlineChanged;
    }

    //管理地图的特殊障碍区域
    public partial class MapCollisionLayer : MapLayerBase
    {
        public MapCollisionLayer(Map map) :base(map) { }

        public override void OnDestroy()
        {
            if (mLayerView != null)
            {
                mLayerView.OnDestroy();
                mLayerView = null;
            }
            if (mLayerData != null)
            {
                Map.currentMap.DestroyObject(mLayerData);
                mLayerData = null;
            }

            mGateRegionDetector.OnDestroy();

            SetDirty();
        }

        public override void Unload()
        {
            Map.currentMap.RemoveMapLayerByID(id);
        }

        public override void Load(config.MapLayerData layerData, MapManager.MapSetting setting, bool asyncLoading)
        {
            var map = Map.currentMap;
            var sourceLayer = layerData as config.MapCollisionLayerData;

            var header = new MapLayerDataHeader(sourceLayer.id, sourceLayer.name, 1, 1, sourceLayer.width, sourceLayer.height, GridType.Rectangle, sourceLayer.origin + (setting == null ? Vector3.zero : setting.origin));

            mLayerData = new MapCollisionLayerData(header, map);
            mLayerView = new MapCollisionLayerView(mLayerData, true);
            mLayerView.active = layerData.active;
            mLayerData.SetObjectActiveStateChangeCallback(mLayerView.OnObjectActiveStateChange);

            mLayerData.isLoading = true;
            if (sourceLayer.collisions != null)
            {
                int n = sourceLayer.collisions.Length;
                for (int i = 0; i < n; ++i)
                {
                    var outlineDatas = new OutlineData[2];
                    var model = sourceLayer.collisions[i] as config.MapCollisionData;
                    var vertices = Utils.ConvertToVector3List(model.navMeshObstacleOutlines);
                    outlineDatas[0] = new OutlineData(vertices);
                    var placementOutlineVertices = Utils.ConvertToVector3List(model.objectPlacementOutlines);
                    if (placementOutlineVertices.Count == 0)
                    {
                        outlineDatas[1] = new OutlineData(vertices);
                    }
                    else
                    {
                        outlineDatas[1] = new OutlineData(placementOutlineVertices);
                    }

                    var collisionData = new MapCollisionData(model.id, Map.currentMap, outlineDatas, model.radius, model.isExtandable, model.attribute, model.type, false);
                    mLayerData.AddObjectData(collisionData);
                }
            }
            mLayerData.isLoading = false;

            this.displayType = sourceLayer.displayType;
            this.displayVertexRadius = sourceLayer.displayVertexRadius;
            mMaxConnectionDistance = sourceLayer.maxConnectionDistance;

            Map.currentMap.AddMapLayer(this);

            SetDirty();

            //load detectors
            regionDetector.Load(sourceLayer.detectors);
        }

        public override bool Resize(float newWidth, float newHeight, bool useLayerOffset)
        {
            var removedObjectIDs = mLayerData.Resize(newWidth, newHeight);
            if (removedObjectIDs != null && removedObjectIDs.Count > 0)
            {
                for (int i = 0; i < removedObjectIDs.Count; ++i)
                {
                    mLayerView.RemoveObjectView(removedObjectIDs[i]);
                }
                SetDirty();
                return true;
            }
            return false;
        }

        public void SetEventHandlers(MapCollisionLayerEventHandlers eventHandlers)
        {
            mEventHandlers = eventHandlers;
        }

        public void InsertCollisionVertex(PrefabOutlineType type, int dataID, int index, Vector3 vertex)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.InsertVertex(type, index, vertex);
                mLayerView.InsertVertex(type, objectData, index, vertex);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onInsertVertex != null)
                    {
                        mEventHandlers.onInsertVertex(dataID, index);
                    }
                }
            }

            SetDirty();
        }

        public void RemoveCollsionVertex(PrefabOutlineType type, int dataID, int index)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.RemoveVertex(type, index);
                mLayerView.RemoveVertex(type, objectData, index);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onRemoveVertex != null)
                    {
                        mEventHandlers.onRemoveVertex(dataID, index);
                    }
                }
            }

            SetDirty();
        }

        public void SetVertexPosition(PrefabOutlineType type, int dataID, int vertexIndex, Vector3 pos)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.SetVertexPosition(type, vertexIndex, pos);
                mLayerView.UpdateVertex(type, dataID, vertexIndex, pos);
            }

            if (mEventHandlers != null)
            {
                if (mEventHandlers.onMoveVertex != null)
                {
                    mEventHandlers.onMoveVertex(dataID, vertexIndex);
                }
            }

            SetDirty();
        }

        public void MoveObject(PrefabOutlineType type, int dataID, Vector3 offset)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.Move(type, offset);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onMoveCollision != null)
                    {
                        mEventHandlers.onMoveCollision(dataID);
                    }
                }

                SetDirty();
            }
        }

        public void ClearOutline(PrefabOutlineType type, int dataID)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.ClearOutline(type);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void SetOutline(PrefabOutlineType type, int dataID, List<Vector3> vertices)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.SetOutline(type, vertices);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void ClearAllOutlines(PrefabOutlineType type)
        {
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var collision = allObjects[i] as MapCollisionData;
                collision.ClearOutline(type);
                mLayerView.UpdateView(collision.id);
            }

            SetDirty();
        }

        public void ExpandOutline(PrefabOutlineType type, int dataID, float radius)
        {
            var objectData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (objectData != null)
            {
                objectData.ExpandOutline(type, radius);
                mLayerView.UpdateView(dataID);

                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onOutlineChanged != null)
                    {
                        mEventHandlers.onOutlineChanged(dataID, type);
                    }
                }
            }

            SetDirty();
        }

        public void SetVertexDisplayRadius(float radius)
        {
            displayVertexRadius = radius;
            List<IMapObjectData> allObjects = new List<IMapObjectData>();
            mLayerData.GetAllObjects(allObjects);
            for (int i = 0; i < allObjects.Count; ++i)
            {
                var obj = allObjects[i] as MapCollisionData;
                obj.displayRadius = radius;
            }

            var allViews = mLayerView.allViews;
            foreach (var p in allViews)
            {
                var view = (p.Value as MapCollisionView);
                view.SetVertexDisplayRadius(radius);
            }
        }

        public void SetSelected(int dataID, bool selected)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (collisionData != null)
            {
                collisionData.isSelected = selected;
                collisionData.useOverridenColor = selected;
                collisionData.overridenColor = Color.green;
            }
        }

        public void SetIntersectedWithObstacles(int dataID, bool hitObstacle)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            if (collisionData != null)
            {
                collisionData.hitObstacles = hitObstacle;
            }
        }

        public void UpdateColor(PrefabOutlineType type)
        {
            if (!mColorDirty)
            {
                return;
            }
            mColorDirty = false;

            var allObjects = mLayerData.objects;
            foreach (var obj in allObjects)
            {
                var collisionData = obj.Value as MapCollisionData;
                if (collisionData.HasAttribute(CollisionAttribute.SpecialRegion))
                {
                    SetDisplayColor(type, collisionData.id, Color.magenta);
                }
                else if (!collisionData.IsSimplePolygon(type))
                {
                    SetDisplayColor(type, collisionData.id, Color.red);
                }
                else if (collisionData.HasAttribute(CollisionAttribute.CanPlaceDecorationObject))
                {
                    SetDisplayColor(type, collisionData.id, Color.yellow);
                }
                else if (collisionData.HasAttribute(CollisionAttribute.TestCollisionWhenPlaceDecorationObject))
                {
                    SetDisplayColor(type, collisionData.id, new Color32(255, 128, 192, 255));
                }
                else if (collisionData.HasAttribute(CollisionAttribute.CollisionCheck))
                {
                    SetDisplayColor(type, collisionData.id, new Color(1, 0.5f, 192 / 255.0f, 1.0f));
                }
                else if (collisionData.HasAttribute(CollisionAttribute.RiverClipper))
                {
                    SetDisplayColor(type, collisionData.id, new Color32(0, 162, 232, 255));
                }
                else
                {
                    SetDisplayColor(type, collisionData.id, Color.white);
                }
            }
        }

        void SetDisplayColor(PrefabOutlineType type, int dataID, Color color)
        {
            var collisionData = mLayerData.GetObjectData(dataID) as MapCollisionData;
            collisionData.color = color;

            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                var collisionView = (view as MapCollisionView);
                collisionView.SetColor(type, collisionData.activeColor);
            }
        }

        public bool AddObject(IMapObjectData objectData)
        {
            if (objectData == null)
            {
                return false;
            }

            int objectID = objectData.GetEntityID();
            if (mLayerData.GetObjectData(objectID) == null)
            {
                bool success = mLayerData.AddObjectData(objectData);
                if (success)
                {
                    mLayerView.AddObjectView(objectData);

                    SetDirty();

                    if (mEventHandlers != null)
                    {
                        if (mEventHandlers.onAddCollision != null)
                        {
                            mEventHandlers.onAddCollision(objectID);
                        }
                    }

                    return true;
                }
            }
            return false;
        }

        public int GetObjectIDByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    return p.Value.objectDataID;
                }
            }
            return 0;
        }

        public void RemoveObjectByGameObjectID(int gameObjectID)
        {
            var views = mLayerView.allViews;
            foreach (var p in views)
            {
                if (p.Value.model.gameObject.GetInstanceID() == gameObjectID)
                {
                    RemoveObject(p.Value.objectDataID);
                    break;
                }
            }
        }

        public bool RemoveObject(int objectDataID)
        {
            bool success = mLayerData.RemoveObjectData(objectDataID);
            if (success)
            {
                if (mEventHandlers != null)
                {
                    if (mEventHandlers.onDeleteCollision != null)
                    {
                        mEventHandlers.onDeleteCollision(objectDataID);
                    }
                }

                mLayerView.RemoveObjectView(objectDataID);

                SetDirty();
            }
            return success;
        }

        public void RemoveAllObjects()
        {
            List<IMapObjectData> objects = new List<IMapObjectData>();
            GetAllObjects(objects);
            for (int i = 0; i < objects.Count; ++i)
            {
                RemoveObject(objects[i].GetEntityID());
            }

            SetDirty();
        }

        public void ShowObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, true, 0);
            }
        }

        public void HideObject(int objectDataID)
        {
            var objData = mLayerData.GetObjectData(objectDataID);
            if (objData != null)
            {
                mLayerData.SetObjectActive(objData, false, 0);
            }
        }

        public GameObject GetObjectGameObject(int dataID)
        {
            var view = mLayerView.GetObjectView(dataID);
            if (view != null)
            {
                return view.model.gameObject;
            }
            return null;
        }

        public override bool UpdateViewport(Rect newViewport, float newCameraZoom)
        {
            bool lodChanged = mLayerData.UpdateViewport(newViewport, newCameraZoom);
            if (lodChanged)
            {
                mLayerView.SetZoom(newCameraZoom, lodChanged);
            }
            return lodChanged;
        }

        public override void RefreshObjectsInViewport()
        {
            mLayerData.RefreshObjectsInViewport();
        }

        public override float GetTotalWidth()
        {
            return mLayerData.GetLayerWidthInMeter(0);
        }
        public override float GetTotalHeight()
        {
            return mLayerData.GetLayerHeightInMeter(0);
        }

        public override bool Contains(int objectID)
        {
            return mLayerData.Contains(objectID);
        }

        public override int GetCurrentLOD()
        {
            return mLayerData.currentLOD;
        }

        public override MapLayerData GetLayerData()
        {
            return mLayerData;
        }

        public override MapLayerView GetLayerView()
        {
            return mLayerView;
        }

        public void GetAllObjects(List<IMapObjectData> objects)
        {
            mLayerData.GetAllObjects(objects);
        }

        public void Traverse(System.Func<IMapObjectData, bool> visitFunc)
        {
            var objects = mLayerData.objects;
            foreach (var p in objects)
            {
                if (visitFunc(p.Value))
                {
                    break;
                }
            }
        }

        public bool IntersectWithPolygon(PrefabOutlineType type, List<Vector3> polygon)
        {
            return mLayerData.IntersectWithPolygon(type, polygon);
        }

        //for debug
        public void CheckCollision(PrefabOutlineType type, System.Action<PolygonObjectData, PolygonObjectData> onCollide)
        {
            mLayerData.CheckCollision(type, onCollide);
        }

        public void ShowOutline(PrefabOutlineType type)
        {
            mLayerView.ShowOutline(type);
        }

        public bool SetCollisionAreaType(int dataID, int type)
        {
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            if (data != null)
            {
                data.type = type;

                if (mEventHandlers != null && mEventHandlers.onOutlineChanged != null)
                {
                    mEventHandlers.onOutlineChanged(dataID, PrefabOutlineType.NavMeshObstacle);
                }
                return true;
            }
            return false;
        }

        public bool AddCollisionAttribute(int dataID, CollisionAttribute attribute)
        {
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            if (data != null)
            {
                data.AddAttribute(attribute);

                if (mEventHandlers != null && mEventHandlers.onOutlineChanged != null)
                {
                    mEventHandlers.onOutlineChanged(dataID, PrefabOutlineType.NavMeshObstacle);
                }

                SetColorDirty();

                return true;
            }
            return false;
        }

        public bool RemoveCollisionAttribute(int dataID, CollisionAttribute attribute)
        {
            var data = Map.currentMap.FindObject(dataID) as MapCollisionData;
            if (data != null)
            {
                data.RemoveAttribute(attribute);

                if (mEventHandlers != null && mEventHandlers.onOutlineChanged != null)
                {
                    mEventHandlers.onOutlineChanged(dataID, PrefabOutlineType.NavMeshObstacle);
                }

                SetColorDirty();

                return true;
            }
            return false;
        }

        //获取所有可放置装饰物的障碍物边框
        public void GetPolygonsOfCanNotPlaceDecorationObjects(List<ObstacleObject> polygons)
        {
            mLayerData.GetPolygonsOfCanNotPlaceDecorationObjects(polygons);
        }

        public void GetCollisionsOfType(List<MapCollisionData> collisions, CollisionAttribute attribute)
        {
            mLayerData.GetCollisionsOfType(collisions, attribute);
        }

        public void GetCollisionOutlinesOfType(List<List<Vector3>> collisionOutlines, CollisionAttribute attribute, PrefabOutlineType type)
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            mLayerData.GetCollisionsOfType(collisions, attribute);
            for (int i = 0; i < collisions.Count; ++i)
            {
                collisionOutlines.Add(collisions[i].GetOutlineVertices(type));
            }
        }

        public void GetCollisionsOfType(PrefabOutlineType type, List<ObstacleObject> collisions, CollisionAttribute attribute)
        {
            mLayerData.GetCollisionsOfType(type, collisions, attribute);
        }

        public void GetAllCollisions(List<MapCollisionData> collisions)
        {
            mLayerData.GetAllCollisions(collisions);
        }

        public void ExportGateNavMesh(string filePath, bool checkError)
        {
#if UNITY_EDITOR
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetCollisionsOfType(collisions, CollisionAttribute.IsGateArea);
            if (collisions.Count == 0 && checkError)
            {
                EditorUtility.DisplayDialog("Error", "No gate found!", "OK");
                return;
            }

#if false
            //get navmesh vertices
            var navmeshLayer = Map.currentMap.GetMapLayer(MapCoreDef.MAP_LAYER_NODE_NAVMESH) as NavMeshLayer;
            if (navmeshLayer == null)
            {
                EditorUtility.DisplayDialog("Error", "Create NavMesh Layer First!", "OK");
                return;
            }
            List<int> serverSideNavMeshVertices = navmeshLayer.GetServerSideNavMeshVertices();
#endif

            List<object> tileMeshList = new List<object>();
            List<IObstacle> obstacles = new List<IObstacle>();
            for (int i = 0; i < collisions.Count; ++i)
            {
                obstacles.Clear();
                List<MapCollisionData> allOtherCollisions = new List<MapCollisionData>();
                mLayerData.GetAllCollisions(allOtherCollisions);
                allOtherCollisions.Remove(collisions[i]);

                List<List<Vector3>> allOtherCollisionPolygons = new List<List<Vector3>>();
                for (int k = 0; k < allOtherCollisions.Count; ++k)
                {
                    allOtherCollisionPolygons.Add(allOtherCollisions[k].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle));
                }

                List<List<Vector3>> holes;
                List<List<Vector3>> noneHoles;
                PolygonAlgorithm.GetDifferencePolygons(collisions[i].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle), allOtherCollisionPolygons, out noneHoles, out holes);

                Vector3[] meshVertices;
                int[] meshIndices;
                Triangulator.TriangulatePolygons(noneHoles, holes, false, 100, 180000, null, out meshVertices, out meshIndices);
                int areaID = collisions[i].type;

                //meshVertices = UseNavMeshVertices(meshVertices, serverSideNavMeshVertices);

                //export json
                Dictionary<string, object> rootObject = new Dictionary<string, object>();
                rootObject["id"] = areaID;
                rootObject["mapwidth"] = Map.currentMap.mapWidth;
                rootObject["mapheight"] = Map.currentMap.mapHeight;
                var trianglesObject = JSONExporter.ExportTriangles(meshIndices);
                var verticesObject = JSONExporter.ExportVertices(meshVertices);
                rootObject["triangles"] = trianglesObject;
                rootObject["vertices"] = verticesObject;

                tileMeshList.Add(rootObject);
            }

            var data = JSONParser.Serialize(tileMeshList);
            File.WriteAllText(filePath, data);
#endif
        }

        public void RemoveEmptyCollisions()
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetAllCollisions(collisions);
            for (int i = 0; i < collisions.Count; ++i)
            {
                if (collisions[i].GetOutlineVertices(PrefabOutlineType.NavMeshObstacle).Count == 0 &&
                    collisions[i].GetOutlineVertices(PrefabOutlineType.ObjectPlacementObstacle).Count == 0)
                {
                    RemoveObject(collisions[i].GetEntityID());
                }
            }
        }

        public void RevertCollision(int collisionID, PrefabOutlineType outlineType)
        {
            var collisionData = layerData.GetObjectData(collisionID) as MapCollisionData;
            collisionData.Revert(outlineType);
            var collisionView = layerView.GetObjectView(collisionID) as MapCollisionView;
            collisionView.Revert(outlineType);
        }

        void SetDirty()
        {
#if UNITY_EDITOR
            EditorConfig.dirtyFlag |= DirtyMask.CollisionLayer;
            EditorConfig.dirtyFlag |= DirtyMask.NavMesh;
            EditorConfig.dirtyFlag |= DirtyMask.NPCRegionConfig;
            EditorConfig.dirtyFlag |= DirtyMask.NPCSpawnPoints;
#endif
        }

        public void SetColorDirty()
        {
            mColorDirty = true;
        }

        public bool FindSnapPosition(PrefabOutlineType type, Vector3 pos, float radius, int exceptionCollisionID, out Vector3 snapPos)
        {
            return mLayerData.FindSnapPosition(type, pos, radius, exceptionCollisionID, out snapPos);
        }

        public void ExportCameraLookAtArea()
        {
            List<MapCollisionData> collisions = new List<MapCollisionData>();
            GetCollisionsOfType(collisions, CollisionAttribute.CameraLookAtArea);

            if (collisions.Count == 0)
            {
                return;
            }
            else if (collisions.Count > 1)
            {
                EditorUtility.DisplayDialog("Error", "Only support one camera look at area!", "OK");
                return;
            }

            string dataPath = MapCoreDef.GetCameraLookAtAreaDataPath(SLGMakerEditor.instance.exportFolder);

            using MemoryStream stream = new MemoryStream();
            using BinaryWriter writer = new BinaryWriter(stream);

            writer.Write(VersionSetting.CameraLookAtAreaFileVersion);

            var vertices = collisions[0].GetOutlineVerticesCopy(PrefabOutlineType.NavMeshObstacle);
            if (!Utils.IsPolygonCW(vertices)) {
                Utils.ReverseList(vertices);
            }
            int n = vertices.Count;
            writer.Write(n);
            for (int i = 0; i < n; ++i)
            {
                Utils.WriteVector3(writer, vertices[i]);
            }

            var data = stream.ToArray();
            File.WriteAllBytes(dataPath, data);
            writer.Close();
        }

        public override string name { get { return mLayerData?.name; } set { mLayerData.name = value; } }
        public override int id { get { return mLayerData.id; } }
        public override Vector3 layerOffset { get { return mLayerData.layerOffset; } }
        public override GameObject gameObject
        {
            get
            {
                return mLayerView.root;
            }
        }
        public override int horizontalTileCount => 1;
        public override int verticalTileCount => 1;
        public override float tileHeight => GetTotalWidth();
        public override float tileWidth => GetTotalHeight();
        public override GridType gridType => GridType.Rectangle;
        public override bool active { get { return mLayerView.active; } set { mLayerView.active = value; } }
        public bool asyncLoading { set { mLayerView.asyncLoading = value; } get { return mLayerView.asyncLoading; } }
        public int objectCount { get { return mLayerData.objectCount; } }
        public override int lodCount => mLayerData.lodCount;
        public MapCollisionLayerData layerData { get { return mLayerData; } }
        public MapCollisionLayerView layerView { get { return mLayerView; } }
        public NavMeshRegionDetectorEditor regionDetector { get { return mGateRegionDetector; } }

        protected MapCollisionLayerData mLayerData;
        protected MapCollisionLayerView mLayerView;

        public PrefabOutlineType displayType { set; get; }
        public float displayVertexRadius { get; internal set; }
        public float maxCollectionDistance { set { mMaxConnectionDistance = value; } get { return mMaxConnectionDistance; } }

        MapCollisionLayerEventHandlers mEventHandlers;
        bool mColorDirty = false;
        NavMeshRegionDetectorEditor mGateRegionDetector = new NavMeshRegionDetectorEditor();
        float mMaxConnectionDistance = 10;
    }
}

#endif