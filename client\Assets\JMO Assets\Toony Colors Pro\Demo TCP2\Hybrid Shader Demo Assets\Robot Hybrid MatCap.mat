%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Robot Hybrid MatCap
  m_Shader: {fileID: -6465566751694194690, guid: df5bb027d94a6c44bb32b3c31ec1303f,
    type: 3}
  m_ShaderKeywords: TCP2_MATCAP TCP2_MATCAP_MASK TCP2_OUTLINE_CONST_SIZE TCP2_RIM_LIGHTING
    TCP2_RIM_LIGHTING_LIGHTMASK TCP2_SHADOW_LIGHT_COLOR TCP2_SPECULAR TCP2_TANGENT_AS_NORMALS
    TCP2_UV_NORMALS_FULL
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 90f5c2a59e018304bb00979205857f25, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 5, y: 5}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: e6e80abe66eff324c99232e67941f2e8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapMask:
        m_Texture: {fileID: 2800000, guid: 37b8c632e5bb8424dbd260bd0b2aa4fb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 2800000, guid: 05cb8fab23c8ef9429ebaf3150311a31, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowBaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 0.15
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DirectIntensityOutline: 1
    - _DstBlend: 0
    - _EmissionChannel: 4
    - _FresnelMax: 1.5
    - _FresnelMin: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _IndirectIntensity: 1
    - _IndirectIntensityOutline: 0
    - _MatCapMaskChannel: 0
    - _MatCapType: 1
    - _Metallic: 0
    - _Mode: 0
    - _NormalsSource: 2
    - _NormalsUVType: 0
    - _OcclusionChannel: 0
    - _OcclusionStrength: 1
    - _OutlineLightingType: 0
    - _OutlineLightingTypeURP: 0
    - _OutlineMaxWidth: 1
    - _OutlineMinWidth: 1
    - _OutlinePixelSizeType: 1
    - _OutlineTextureLOD: 5
    - _OutlineTextureType: 0
    - _OutlineWidth: 2
    - _Parallax: 0.02
    - _RampBands: 4
    - _RampBandsSmoothing: 0.1
    - _RampOffset: 0
    - _RampScale: 1
    - _RampSmoothing: 0.05
    - _RampThreshold: 0.75
    - _RampType: 0
    - _ReceiveShadowsOff: 1
    - _ReflectionMapType: 2
    - _ReflectionSmoothness: 1
    - _RenderingMode: 0
    - _RimMax: 1
    - _RimMin: 0.4
    - _ShadowColorLightAtten: 1
    - _SingleIndirectColor: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularMapType: 0
    - _SpecularRoughness: 0.352
    - _SpecularToonSize: 0.005
    - _SpecularToonSmoothness: 0.024
    - _SpecularType: 0
    - _SrcBlend: 1
    - _UVSec: 0
    - _UseAlphaTest: 0
    - _UseEmission: 0
    - _UseFresnelReflections: 0
    - _UseMatCap: 1
    - _UseMatCapMask: 1
    - _UseMobileMode: 0
    - _UseNormalMap: 0
    - _UseOcclusion: 0
    - _UseOutline: 1
    - _UseReflections: 0
    - _UseRim: 1
    - _UseRimLightMask: 1
    - _UseShadowTexture: 0
    - _UseSpecular: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.8, g: 0.8, b: 0.8, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectionColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _RimColor: {r: 1.6, g: 1.6, b: 1.6, a: 0.5}
    - _SColor: {r: 0.004524946, g: 0, b: 0.5, a: 1}
    - _SpecularColor: {r: 2.1185474, g: 1.6237458, b: 1.2711284, a: 1}
