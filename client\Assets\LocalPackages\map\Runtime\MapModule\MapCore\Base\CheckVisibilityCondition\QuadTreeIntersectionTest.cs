﻿ 



 
 



/*
 * created by wzw at 2019.3.5
 */
using UnityEngine;

namespace TFW.Map {
    public static class QuadTreeIntersectionTest {
        //左移视野时Bounding box的相交性判断
        public static bool LeftCondition(Rect modelWorldBounds, Rect bounds) {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;

            var max = bounds.max;
            var min = bounds.min;
            if (objMax.x >= min.x && objMax.x <= max.x &&
                !(objMin.y > max.y || min.y > objMax.y)) {
                return true;
            }
            return false;
        }

        //右移视野时Bounding box的相交性判断
        public static bool RightCondition(Rect modelWorldBounds, Rect bounds) {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;

            var max = bounds.max;
            var min = bounds.min;
            if (objMin.x >= min.x && objMin.x <= max.x &&
                !(objMin.y > max.y || min.y > objMax.y)) {
                return true;
            }
            return false;
        }

        //上移视野时Bounding box的相交性判断
        public static bool TopCondition(Rect modelWorldBounds, Rect bounds) {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            var max = bounds.max;
            var min = bounds.min;
            if (objMax.y >= min.y && objMax.y <= max.y &&
                !(objMin.x > max.x || min.x > objMax.x)) {
                return true;
            }
            return false;
        }

        //下移视野时Bounding box的相交性判断
        public static bool BottomCondition(Rect modelWorldBounds, Rect bounds) {
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            var max = bounds.max;
            var min = bounds.min;
            if (objMin.y >= min.y && objMin.y <= max.y &&
                !(objMin.x > max.x || min.x > objMax.x)) {
                return true;
            }
            return false;
        }

        public static bool IntersectCondition(Rect modelWorldBounds, Rect bounds) {
            var min = bounds.min;
            var max = bounds.max;
            var objMax = modelWorldBounds.max;
            var objMin = modelWorldBounds.min;
            if (objMin.x > max.x || min.x > objMax.x ||
                min.y > objMax.y || objMin.y > max.y) {
                return false;
            }
            return true;
        }
    }
}
