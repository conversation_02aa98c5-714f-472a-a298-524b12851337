﻿ 



 
 


using System.Text;
using UnityEngine;

namespace TFW.Map
{
    //拖动相机时绕view center在水平方向旋转一定角度
    public class CameraDragRotate : CameraAction
    {
        public enum CameraState
        {
            Invalid,
            Dragging,
        }

        public CameraDragRotate(CameraActionType type, float maxAngle) :
            base(type)
        {
            moveAxis = CameraMoveAxis.Horizontal;
            ownAxis = CameraMoveAxis.Horizontal;
            on = true;
            mMaxAngle = maxAngle;
        }

        public void TurnOn(bool on)
        {
            this.on = on;
            if (!on)
            {
                OnFinish();
            }
        }

        protected override void UpdateImpl(Vector3 currentCameraPos)
        {
            mTargetPosition = currentCameraPos;
            mDragMoveOffset = Vector3.zero;
            int touchCount = MapTouchManager.touchCount;
            if (on && touchCount == 1)
            {
                var touch = MapTouchManager.GetTouch(0);

#if UNITY_EDITOR || UNITY_STANDALONE
                //unity remote
                if (touch.fingerId != 0)
                {
                    return;
                }
#endif
                var camera = Map.currentMap.camera;
                mLastTouchWorldPosition = Utils.FromScreenToWorldPosition(touch.lastPosition, camera.firstCamera);
                mTouchWorldPosition = Utils.FromScreenToWorldPosition(touch.position, camera.firstCamera);

                mDragDir = Mathf.Sign(touch.lastPosition.x - touch.position.x);
                mDragMoveOffset = mTouchWorldPosition - mLastTouchWorldPosition;

                if (touch.state == MapTouchState.Touch)
                {
                    mCameraState = CameraState.Dragging;

                    mCanDrag = !touch.blockUIDrag;
                }
                else if (touch.state == MapTouchState.Touching)
                {
                    mCameraState = CameraState.Dragging;
                    
                    if (mCanDrag)
                    {
                        enabled = true;
                    }
                    else
                    {
                        OnFinish();
                    }
                }
                else if (touch.state == MapTouchState.Release)
                {
                    OnFinish();
                }
            }
            else if (touchCount == 2)
            {
                OnFinish();
            }
        }

        public override Vector3 GetTargetPosition()
        {
            Vector3 cameraTargetPos = mTargetPosition;
            if (on)
            {
                if (mTargetPosition.y < MapCameraMgr.cameraSetting.maxHorizontalRotationHeight)
                {
                    if (mCameraState == CameraState.Dragging)
                    {
                        //拖动相机中
                        var viewCenter = Map.currentMap.viewCenter;
                        var cameraToViewCenter = cameraTargetPos - viewCenter;
                        float viewDistance = cameraToViewCenter.magnitude;
                        var camera = MapCameraMgr.MapCamera.firstCamera;
                        var cameraRoot = MapCameraMgr.MapCameraRoot.transform;
                        //根据拖动距离计算应该旋转多少度
                        float rotationX = cameraRoot.rotation.eulerAngles.x;
                        float rotationRadius = viewDistance * Mathf.Cos(rotationX * Mathf.Deg2Rad);
                        float arcLength = mDragMoveOffset.magnitude;

                        float speed = 1.0f;
                        if (mRotateSpeedModifierCallback != null)
                        {
                            speed = mRotateSpeedModifierCallback(cameraTargetPos.y);
                        }
                        float angle = arcLength / rotationRadius * Mathf.Rad2Deg * -mDragDir * speed;

                        //clamp angles
                        float currentAngle = MapCameraMgr.updatedCameraYRotation;
                        float newAngle = currentAngle + angle;
                        float deltaAngle = Mathf.Abs(angle);
                        float maxAngle = mMaxAngle;
                        if (maxAngle == 0)
                        {
                            maxAngle = MapCameraMgr.cameraSetting.horizontalRotationRange;
                        }
                        if (newAngle < -maxAngle)
                        {
                            deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle + maxAngle);
                        }
                        else if (newAngle > maxAngle)
                        {
                            deltaAngle = Mathf.Abs(angle) - Mathf.Abs(newAngle - maxAngle);
                        }
                        deltaAngle *= Mathf.Sign(angle);
                        currentAngle += deltaAngle;

                        //Debug.LogError($"delta angle: {deltaAngle}, current angle: {mCurrentAngle}");

                        //旋转相机位置
                        Quaternion rotation = Quaternion.Euler(0, deltaAngle, 0);
                        cameraTargetPos = viewCenter + rotation * cameraToViewCenter.normalized * viewDistance;
                        //设置相机旋转
                        MapCameraMgr.updatedCameraYRotation = currentAngle;
                    }
                }
            }
            return cameraTargetPos;
        }

        public override void OnFinishImpl()
        {
            enabled = false;
            isFinished = true;
            mDragMoveOffset = Vector3.zero;
            mCameraState = CameraState.Invalid;
        }

        public void ResetCurrentAngle()
        {
        }

        public override string GetDebugMessage()
        {
            StringBuilder builder = new StringBuilder();
            builder.AppendLine($"enabled: {enabled}");
            builder.AppendLine($"on: {on}");
            builder.AppendLine($"state: {mCameraState}");
            builder.AppendLine($"blocked by UI: {!mCanDrag}");
            return builder.ToString();
        }

        public bool on
        {
            set
            {
                mOn = value;
                if (!mOn)
                {
                    isFinished = true;
                }
            }
            get
            {
                return mOn;
            }
        }

        public CameraState state { get { return mCameraState; } }
        public float maxAngle { get { return mMaxAngle; } set { mMaxAngle = value; } }
        public System.Func<float, float> rotateSpeedModifierCallback { set { mRotateSpeedModifierCallback = value; } get { return mRotateSpeedModifierCallback; } }

        CameraState mCameraState = CameraState.Invalid;
        float mMaxAngle;
        
        Vector3 mTargetPosition;
        Vector3 mDragMoveOffset;
        Vector3 mTouchWorldPosition;
        Vector3 mLastTouchWorldPosition;
        float mDragDir;
        bool mCanDrag = false;

        bool mOn = true;

        System.Func<float, float> mRotateSpeedModifierCallback;
    }
}
